(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[4391],{5671:(n,e,t)=>{"use strict";t.d(e,{x:()=>o});var i=t(37876),r=t(14232);let l=n=>{let{value:e,onChange:t,placeholder:l="Write something...",height:o=300,disabled:d=!1}=n,s=(0,r.useRef)(null),[c,p]=(0,r.useState)(!1);(0,r.useEffect)(()=>{s.current&&1&&!c&&s.current.innerHTML!==e&&(s.current.innerHTML=e||"")},[e,c]);let u=()=>{s.current&&t&&t(s.current.innerHTML)},a=(n,e)=>{if("undefined"!=typeof document){var t;document.execCommand(n,!1,e||""),u(),null==(t=s.current)||t.focus()}};return(0,i.jsx)("div",{className:"simple-rich-text-editor",style:{border:"1px solid #ccc"},children:(0,i.jsxs)(i.Fragment,{children:[(0,i.jsxs)("div",{className:"toolbar",style:{padding:"8px",borderBottom:"1px solid #ccc",background:"#f5f5f5"},children:[(0,i.jsx)("button",{type:"button",onClick:()=>a("bold"),style:{margin:"0 5px",padding:"3px 8px"},children:(0,i.jsx)("strong",{children:"B"})}),(0,i.jsx)("button",{type:"button",onClick:()=>a("italic"),style:{margin:"0 5px",padding:"3px 8px"},children:(0,i.jsx)("em",{children:"I"})}),(0,i.jsx)("button",{type:"button",onClick:()=>a("underline"),style:{margin:"0 5px",padding:"3px 8px"},children:(0,i.jsx)("u",{children:"U"})}),(0,i.jsx)("button",{type:"button",onClick:()=>a("insertOrderedList"),style:{margin:"0 5px",padding:"3px 8px"},children:"OL"}),(0,i.jsx)("button",{type:"button",onClick:()=>a("insertUnorderedList"),style:{margin:"0 5px",padding:"3px 8px"},children:"UL"}),(0,i.jsx)("button",{type:"button",onClick:()=>{let n=prompt("Enter the link URL");n&&a("createLink",n)},style:{margin:"0 5px",padding:"3px 8px"},children:"Link"})]}),(0,i.jsx)("div",{ref:s,contentEditable:!d,onInput:u,onFocus:()=>p(!0),onBlur:()=>p(!1),style:{padding:"15px",minHeight:o,maxHeight:2*o,overflow:"auto",outline:"none"},"data-placeholder":e?"":l,suppressContentEditableWarning:!0})]})})},o=n=>{let{initContent:e,onChange:t}=n;return(0,i.jsx)(l,{value:e||"",onChange:n=>t(n)})}},48314:(n,e,t)=>{(window.__NEXT_P=window.__NEXT_P||[]).push(["/institution/Form",function(){return t(48012)}])}},n=>{var e=e=>n(n.s=e);n.O(0,[7725,1772,7336,5939,5266,8012,636,6593,8792],()=>e(48314)),_N_E=n.O()}]);
//# sourceMappingURL=Form-41463c76746bd85a.js.map