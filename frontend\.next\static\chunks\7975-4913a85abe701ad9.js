"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[7975],{14145:(e,t,s)=>{s.r(t),s.d(t,{default:()=>f});var r=s(37876);s(14232);var l=s(60282),n=s(56970),a=s(48230),i=s.n(a),c=s(21772),o=s(11041),d=s(10841),u=s.n(d),j=s(31753),h=s(46864),x=s(31647);let f=e=>{let{t}=(0,j.Bd)("common"),s=()=>(0,r.jsx)(r.Fragment,{children:e.editAccess?(0,r.jsx)(i(),{href:"/project/[...routes]",as:"/project/edit/".concat(e.routeData.routes[1]),children:(0,r.jsxs)(l.A,{variant:"secondary",size:"sm",children:[(0,r.jsx)(c.g,{icon:o.hpd}),"\xa0",t("Edit")]})}):""}),a=(0,h.canEditProject)(()=>(0,r.jsx)(s,{}));return(0,r.jsx)(r.Fragment,{children:(0,r.jsx)(n.A,{className:"projectRow",children:(0,r.jsxs)("div",{className:"projectBanner",children:[(0,r.jsx)("div",{className:"projectImg",children:(0,r.jsx)("img",{src:"/images/project-banner.jpg",alt:"Project Detail"})}),function(e,t,s,l,n){return(0,r.jsxs)("div",{className:"projectTitleBlock",children:[(0,r.jsxs)("h4",{className:"projectTitle",children:[e.title,"\xa0\xa0",t.routes&&t.routes[1]?(0,r.jsx)(s,{project:e}):null]}),(0,r.jsxs)("div",{className:"projectDate",children:[(0,r.jsxs)("div",{className:"projectStart",children:[(0,r.jsx)("i",{className:"fas fa-calendar-alt"}),(0,r.jsxs)("div",{children:[(0,r.jsxs)("h6",{style:{color:"white"},children:[l("StartDate"),":"]}),(0,r.jsx)("h5",{children:e.start_date?u()(e.start_date).format(n):null})]})]}),(0,r.jsxs)("div",{className:"projectStatus me-2",children:[(0,r.jsx)("i",{className:"fas fa-hourglass-half"}),(0,r.jsxs)("div",{children:[(0,r.jsxs)("h6",{style:{color:"white"},children:[l("Status"),":"]}),(0,r.jsx)("h5",{children:e.status&&e.status.title})]})]}),(0,r.jsx)(x.A,{entityId:t.routes[1],entityType:"project"})]})]})}(e.projectData,e.routeData,a,t,"DD-MM-YYYY")]})})})}},18576:(e,t,s)=>{s.r(t),s.d(t,{default:()=>d});var r=s(37876);s(14232);var l=s(56970),n=s(37784),a=s(31753),i=s(98661),c=s(72800);let o=e=>{let t=[];return null==e||e.forEach(e=>{var s;null==(s=e.partner_institution)||s.forEach(e=>{t.push({_id:e._id,title:e.title})})}),t=t.filter((e,t,s)=>s.findIndex(t=>t._id===e._id)===t)},d=e=>{let{t}=(0,a.Bd)("common"),{description:s,partner_institutions:d}=e.project,u=o(d);return(0,r.jsx)(r.Fragment,{children:(0,r.jsxs)(l.A,{className:"projectInfoBlock",children:[(0,r.jsx)(n.A,{className:"projectDescBlock",md:8,children:(0,r.jsx)(c.A,{description:s})}),(0,r.jsxs)(n.A,{md:4,className:"projectInfo",children:[(0,r.jsx)(i.A,{header:t("ProjectInformation"),body:function(e,t){let{area_of_work:s,funded_by:l}=t;return(0,r.jsxs)("div",{children:[(0,r.jsxs)("div",{className:"projetInfoItems",children:[(0,r.jsxs)("h6",{children:[e("AreaofWork"),": "]}),(0,r.jsx)("p",{children:null==s?void 0:s.map(e=>e.title).join(", ")})]}),(0,r.jsxs)("div",{className:"projetInfoItems",children:[(0,r.jsxs)("h6",{children:[e("FundedBy"),": "]}),(0,r.jsx)("p",{children:l})]})]})}(t,e.project)}),(0,r.jsx)(i.A,{header:t("PartnerOrganisation"),body:function(e){return(0,r.jsx)("ul",{className:"projectPartner",children:null==e?void 0:e.map((e,t)=>(0,r.jsx)("li",{children:(null==e?void 0:e.title)||""},t))})}(u)}),(0,r.jsx)(i.A,{header:t("CountriesCoveredbyProject"),body:function(e){return(0,r.jsx)("ul",{className:"projectPartner",children:null==e?void 0:e.map((e,t)=>{var s;return(0,r.jsx)("li",{children:(null==e||null==(s=e.partner_country)?void 0:s.title)||""},t)})})}(d)})]})]})})}},31647:(e,t,s)=>{s.d(t,{A:()=>d});var r=s(37876),l=s(31777),n=s(11041),a=s(14232),i=s(21772),c=s(53718);let o={operation:"Operation",institution:"Institution",event:"Event",project:"Project",vspace:"Vspace"},d=(0,l.Ng)(e=>e)(e=>{let{user:t,entityId:s,entityType:l}=e,[d,u]=(0,a.useState)(!1),[j,h]=(0,a.useState)(""),x=async()=>{if(!(null==t?void 0:t._id))return;let e=await c.A.get("/flag",{query:{entity_id:s,user:t._id,onModel:o[l]}});e&&e.data&&e.data.length>0&&(h(e.data[0]),u(!0))},f=async e=>{if(e.preventDefault(),!(null==t?void 0:t._id))return;let r=!d,n={entity_type:l,entity_id:s,user:t._id,onModel:o[l]};if(r){let e=await c.A.post("/flag",n);e&&e._id&&(h(e),u(r))}else{let e=await c.A.remove("/flag/".concat(j._id));e&&e.n&&u(r)}};return(0,a.useEffect)(()=>{x()},[]),(0,r.jsx)("div",{className:"subscribe-flag",children:(0,r.jsxs)("a",{href:"",onClick:f,children:[(0,r.jsx)("span",{className:"check",children:d?(0,r.jsx)(i.g,{className:"clickable checkIcon",icon:n.SGM,color:"#00CC00"}):(0,r.jsx)(i.g,{className:"clickable minusIcon",icon:n.OQW,color:"#fff"})}),(0,r.jsx)(i.g,{className:"bookmark",icon:n.G06,color:"#d4d4d4"})]})})})},87975:(e,t,s)=>{s.r(t),s.d(t,{default:()=>u});var r=s(37876),l=s(14232),n=s(49589),a=s(53718),i=s(37308),c=s(14145),o=s(18576),d=s(18690);let u=e=>{let[t,s]=(0,l.useState)({title:"",website:"",area_of_work:[],status:{},funded_by:"",country:{},description:"",end_date:"",start_date:"",partner_institutions:[],partner_institution:{},created_at:"",updated_at:""}),[,u]=(0,l.useState)(""),[,j]=(0,l.useState)(""),[h,x]=(0,l.useState)(!1);(0,l.useEffect)(()=>{(null==e?void 0:e.routes[1])&&f()},[]);let f=async()=>{let t=await a.A.post("/users/getLoggedUser",{});t&&t.roles&&t.roles.length&&e.routes&&e.routes[1]&&(async r=>{let l=await a.A.get("/project/".concat(e.routes[1]),r);(function(e,t,s,r){var l,n,a;e&&t(e),(null==e||null==(l=e.user)?void 0:l.firstname)&&(null==e||null==(n=e.user)?void 0:n.lastname)&&s("".concat(e.user.firstname," ").concat(e.user.lastname)),(null==e||null==(a=e.user)?void 0:a.position)&&r(e.user.position)})(l,s,u,j),function(e,t){x(!1),t&&t.roles&&(t.roles.includes("SUPER_ADMIN")||t.roles.filter(e=>"EMT_NATIONAL_FOCALPOINT"==e).length>0&&e.user._id==t._id||t.roles.filter(e=>"NGOS"==e).length>0&&e.user._id==t._id||t.roles.filter(e=>"GENERAL_USER"==e).length>0&&e.user._id==t._id||t.roles.filter(e=>"PLATFORM_ADMIN"==e).length>0&&e.user._id==t._id||t.roles.filter(e=>"INIG_STAKEHOLDER"==e).length>0&&e.user._id==t._id?x(!0):t.roles.filter(e=>"EMT"==e).length>0&&e.user._id==t._id&&x(!0))}(l,t)})({})},p={projectData:t,routeData:e,editAccess:h};return(0,r.jsxs)(n.A,{className:"projectDetail",fluid:!0,children:[(0,r.jsx)(i.A,{routes:e.routes}),(0,r.jsx)(c.default,{...p}),(0,r.jsx)(o.default,{project:t}),(0,r.jsx)(d.default,{...p})]})}},98661:(e,t,s)=>{s.d(t,{A:()=>o});var r=s(37876),l=s(14232),n=s(29335),a=s(31195);function i(e){let{list:t,dialogClassName:s}=e;return(0,r.jsxs)(a.A,{...e,dialogClassName:s,"aria-labelledby":"contained-modal-title-vcenter",centered:!0,children:[(0,r.jsx)(a.A.Header,{closeButton:!0,children:(0,r.jsx)(a.A.Title,{id:"contained-modal-title-vcenter",children:t.heading})}),(0,r.jsx)(a.A.Body,{children:t.body})]})}function c(e){let{list:t}=e,[s,a]=l.useState(!1);return t&&t.body?(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)("button",{type:"button",onClick:()=>a(!0),style:{border:"none",background:"none",padding:0},children:(0,r.jsx)(n.A.Footer,{children:(0,r.jsx)("i",{className:"fas fa-chevron-down"})})}),e.list&&(0,r.jsx)(i,{list:e.list,show:s,onHide:()=>a(!1),dialogClassName:e.dialogClassName})]}):null}let o=function(e){let{header:t,body:s}=e;return(0,r.jsxs)(n.A,{className:"text-center infoCard",children:[(0,r.jsx)(n.A.Header,{children:t}),(0,r.jsx)(n.A.Body,{children:(0,r.jsx)(n.A.Text,{children:s})}),(0,r.jsx)(c,{...e})]})}}}]);
//# sourceMappingURL=7975-4913a85abe701ad9.js.map