"use strict";(()=>{var e={};e.id=4354,e.ids=[636,3220,4354],e.modules={123:e=>{e.exports=require("dom-helpers/removeEventListener")},1332:e=>{e.exports=require("react-custom-scrollbars-2")},1428:e=>{e.exports=import("axios")},1680:e=>{e.exports=require("@restart/ui/utils")},1919:e=>{e.exports=require("react-transition-group/Transition")},3892:e=>{e.exports=require("classnames")},4048:e=>{e.exports=require("react-truncate")},6009:e=>{e.exports=require("dom-helpers/ownerDocument")},6952:e=>{e.exports=require("@restart/ui/Modal")},7374:e=>{e.exports=require("@restart/ui/Dropdown")},8732:e=>{e.exports=require("react/jsx-runtime")},9653:e=>{e.exports=require("@restart/ui/Overlay")},11242:e=>{e.exports=require("redux-persist/integration/react")},11688:e=>{e.exports=require("@restart/hooks/useWillUnmount")},12388:e=>{e.exports=require("dom-helpers/addEventListener")},13364:e=>{e.exports=require("redux-saga/effects")},14062:e=>{e.exports=import("react-redux")},14078:e=>{e.exports=import("swr")},14332:e=>{e.exports=require("uncontrollable")},16116:e=>{e.exports=require("invariant")},18622:e=>{e.exports=require("yup")},19121:e=>{e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},22326:e=>{e.exports=require("react-dom")},22541:e=>{e.exports=require("redux-persist/lib/storage")},25303:e=>{e.exports=require("@restart/ui/NavItem")},26324:e=>{e.exports=require("warning")},27825:e=>{e.exports=require("lodash")},27910:e=>{e.exports=require("stream")},28217:e=>{e.exports=require("@restart/ui/DropdownItem")},29021:e=>{e.exports=require("fs")},29294:e=>{e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},29628:e=>{e.exports=require("dom-helpers/addClass")},29825:e=>{e.exports=require("prop-types")},29841:e=>{e.exports=require("dom-helpers/hasClass")},30362:e=>{e.exports=require("@restart/ui/DropdownToggle")},33873:e=>{e.exports=require("path")},36653:e=>{e.exports=require("nprogress")},36955:e=>{e.exports=require("@restart/hooks/useBreakpoint")},37766:e=>{e.exports=require("@restart/hooks/useMergedRefs")},38609:e=>{e.exports=require("react-data-table-component")},39756:e=>{e.exports=import("redux")},40051:e=>{e.exports=require("dom-helpers/removeClass")},40361:e=>{e.exports=require("next/dist/compiled/next-server/pages.runtime.prod.js")},42893:e=>{e.exports=import("react-hot-toast")},43294:e=>{e.exports=require("formik")},50009:e=>{e.exports=require("@restart/ui/DropdownMenu")},56084:(e,r,t)=>{t.d(r,{A:()=>u});var s=t(8732);t(82015);var a=t(38609),o=t.n(a),i=t(88751),n=t(30370);function p(e){let{t:r}=(0,i.useTranslation)("common"),t={rowsPerPageText:r("Rowsperpage")},{columns:a,data:p,totalRows:u,resetPaginationToggle:l,subheader:d,subHeaderComponent:c,handlePerRowsChange:x,handlePageChange:g,rowsPerPage:m,defaultRowsPerPage:q,selectableRows:h,loading:P,pagServer:y,onSelectedRowsChange:f,clearSelectedRows:S,sortServer:v,onSort:A,persistTableHead:b,sortFunction:w,...j}=e,T={paginationComponentOptions:t,noDataComponent:r("NoData"),noHeader:!0,columns:a,data:p||[],dense:!0,paginationResetDefaultPage:l,subHeader:d,progressPending:P,subHeaderComponent:c,pagination:!0,paginationServer:y,paginationPerPage:q||10,paginationRowsPerPageOptions:m||[10,15,20,25,30],paginationTotalRows:u,onChangeRowsPerPage:x,onChangePage:g,selectableRows:h,onSelectedRowsChange:f,clearSelectedRows:S,progressComponent:(0,s.jsx)(n.A,{}),sortIcon:(0,s.jsx)("i",{className:"sort-icon fas fa-exchange-alt"}),sortServer:v,onSort:A,sortFunction:w,persistTableHead:b,className:"rki-table"};return(0,s.jsx)(o(),{...T})}p.defaultProps={subHeader:!1,pagination:!0,totalRows:null,pagServer:!0,onSelectedRowsChange:null,clearSelectedRows:!1,sortServer:!1,persistTableHead:!1};let u=p},57664:e=>{e.exports=require("dom-helpers/scrollbarSize")},58928:e=>{e.exports=require("dom-helpers/canUseDOM")},59717:e=>{e.exports=require("@restart/ui/DropdownContext")},60560:e=>{e.exports=require("dom-helpers/contains")},63033:e=>{e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},64343:(e,r,t)=>{t.a(e,async(e,s)=>{try{t.r(r),t.d(r,{config:()=>q,default:()=>c,getServerSideProps:()=>m,getStaticPaths:()=>g,getStaticProps:()=>x,reportWebVitals:()=>h,routeModule:()=>A,unstable_getServerProps:()=>S,unstable_getServerSideProps:()=>v,unstable_getStaticParams:()=>f,unstable_getStaticPaths:()=>y,unstable_getStaticProps:()=>P});var a=t(63885),o=t(80237),i=t(81413),n=t(9616),p=t.n(n),u=t(72386),l=t(95864),d=e([u,l]);[u,l]=d.then?(await d)():d;let c=(0,i.M)(l,"default"),x=(0,i.M)(l,"getStaticProps"),g=(0,i.M)(l,"getStaticPaths"),m=(0,i.M)(l,"getServerSideProps"),q=(0,i.M)(l,"config"),h=(0,i.M)(l,"reportWebVitals"),P=(0,i.M)(l,"unstable_getStaticProps"),y=(0,i.M)(l,"unstable_getStaticPaths"),f=(0,i.M)(l,"unstable_getStaticParams"),S=(0,i.M)(l,"unstable_getServerProps"),v=(0,i.M)(l,"unstable_getServerSideProps"),A=new a.PagesRouteModule({definition:{kind:o.A.PAGES,page:"/adminsettings/updateType/updateTypeTable",pathname:"/adminsettings/updateType/updateTypeTable",bundlePath:"",filename:""},components:{App:u.default,Document:p()},userland:l});s()}catch(e){s(e)}})},65447:e=>{e.exports=require("@restart/ui/Button")},67364:e=>{e.exports=require("@restart/hooks/useCallbackRef")},68455:e=>{e.exports=import("redux-saga")},69722:e=>{e.exports=require("es6-promise")},74075:e=>{e.exports=require("zlib")},74716:e=>{e.exports=require("moment")},74987:e=>{e.exports=require("@restart/ui/ModalManager")},78097:e=>{e.exports=require("next-redux-saga")},78634:e=>{e.exports=require("@restart/ui/Anchor")},80237:(e,r)=>{Object.defineProperty(r,"A",{enumerable:!0,get:function(){return t}});var t=function(e){return e.PAGES="PAGES",e.PAGES_API="PAGES_API",e.APP_PAGE="APP_PAGE",e.APP_ROUTE="APP_ROUTE",e.IMAGE="IMAGE",e}({})},81366:e=>{e.exports=require("redux-auth-wrapper/connectedAuthWrapper")},81413:(e,r)=>{Object.defineProperty(r,"M",{enumerable:!0,get:function(){return function e(r,t){return t in r?r[t]:"then"in r&&"function"==typeof r.then?r.then(r=>e(r,t)):"function"==typeof r&&"default"===t?r:void 0}}})},81521:e=>{e.exports=require("dom-helpers/querySelectorAll")},81895:e=>{e.exports=require("@restart/hooks/useEventCallback")},82015:e=>{e.exports=require("react")},86842:e=>{e.exports=require("@restart/ui/SelectableContext")},87571:e=>{e.exports=require("dom-helpers/transitionEnd")},88751:e=>{e.exports=require("next-i18next")},93787:e=>{e.exports=require("redux-persist")},94947:e=>{e.exports=require("@restart/hooks/useTimeout")},95864:(e,r,t)=>{t.a(e,async(e,s)=>{try{t.r(r),t.d(r,{default:()=>m});var a=t(8732),o=t(19918),i=t.n(o),n=t(82015),p=t(12403),u=t(91353),l=t(42893),d=t(56084),c=t(88751),x=t(63487),g=e([l,x]);[l,x]=g.then?(await g)():g;let m=e=>{let{t:r}=(0,c.useTranslation)("common"),[t,s]=(0,n.useState)([]),[,o]=(0,n.useState)(!1),[g,m]=(0,n.useState)(0),[q,h]=(0,n.useState)(10),[P,y]=(0,n.useState)(!1),[f,S]=(0,n.useState)({}),v=()=>y(!1),A=[{name:r("adminsetting.updatestype.Title"),selector:"title"},{name:r("adminsetting.updatestype.Icon"),selector:"icon",cell:e=>(0,a.jsx)("i",{className:`fas ${e.icon}`})},{name:r("adminsetting.updatestype.Action"),selector:"",cell:e=>(0,a.jsxs)("div",{children:[(0,a.jsx)(i(),{href:"/adminsettings/[...routes]",as:`/adminsettings/edit_update_type/${e._id}`,children:(0,a.jsx)("i",{className:"icon fas fa-edit"})}),"\xa0",(0,a.jsx)("a",{onClick:()=>M(e),children:(0,a.jsx)("i",{className:"icon fas fa-trash-alt"})})," "]})}],b={sort:{title:"asc"},limit:q,page:1,query:{}};(0,n.useEffect)(()=>{w(b)},[]);let w=async e=>{o(!0);let r=await x.A.get("/updatetype",e);r&&r.data&&r.data.length>0&&(s(r.data),m(r.totalCount),o(!1))},j=async(e,r)=>{b.limit=e,b.page=r,o(!0);let t=await x.A.get("/updatetype",b);t&&t.data&&t.data.length>0&&(s(t.data),h(e),o(!1))},T=async()=>{try{await x.A.remove(`/updatetype/${f}`),w(b),y(!1),l.default.success(r("adminsetting.updatestype.Table.updateTypeDeletedSuccessfully"))}catch(e){l.default.error(r("adminsetting.updatestype.Table.errorDeletingUpdateType"))}},M=async e=>{S(e._id),y(!0)};return(0,a.jsxs)("div",{children:[(0,a.jsxs)(p.A,{show:P,onHide:v,children:[(0,a.jsx)(p.A.Header,{closeButton:!0,children:(0,a.jsx)(p.A.Title,{children:r("adminsetting.updatestype.DeleteUpdateType")})}),(0,a.jsx)(p.A.Body,{children:r("adminsetting.updatestype.Areyousurewanttodeletethisupdatetype?")}),(0,a.jsxs)(p.A.Footer,{children:[(0,a.jsx)(u.A,{variant:"secondary",onClick:v,children:r("adminsetting.updatestype.Cancel")}),(0,a.jsx)(u.A,{variant:"primary",onClick:T,children:r("adminsetting.updatestype.Yes")})]})]}),(0,a.jsx)(d.A,{columns:A,data:t,totalRows:g,pagServer:!0,handlePerRowsChange:j,handlePageChange:e=>{b.limit=q,b.page=e,w(b)}})]})};s()}catch(e){s(e)}})},96196:e=>{e.exports=require("next-redux-wrapper")},98320:e=>{e.exports=require("@restart/hooks/useIsomorphicEffect")},99460:e=>{e.exports=require("dom-helpers/css")}};var r=require("../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[6089,9216,9616,2386],()=>t(64343));module.exports=s})();