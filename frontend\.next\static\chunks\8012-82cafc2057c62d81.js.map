{"version": 3, "file": "static/chunks/8012-82cafc2057c62d81.js", "mappings": "4HAGA,OAAMA,EACFC,gBAAgBC,CAAa,CAAEC,CAAa,CAAE,CAG1C,IAAMC,EAAoB,EAAE,CAM5B,OALAF,EAAOG,OAAO,CAAC,IAC6D,GAAG,GAAhEC,MAAM,CAAC,GAAgBC,EAAMC,GAAG,GAAKC,EAAMC,KAAK,EAAEC,MAAM,EAC/DP,EAAWQ,IAAI,CAACH,EAAMC,KAAK,CAEnC,GACON,CACX,CAEA,MAAMS,kBAAkBC,CAAc,CAAEC,CAAgB,CAAE,CACtDD,EAAQT,OAAO,CAAC,MAAOW,IACnB,IAAIC,EAAW,MAAMC,EAAAA,CAAUA,CAACC,GAAG,CAAC,UAAiB,OAAPH,GAC1CC,GAASG,kBAAkB,CAACT,MAAM,EAAE,CACpCM,EAASG,kBAAkB,CAAGH,EAASG,kBAAkB,CAACd,MAAM,CAC5D,GAAiBe,EAAOC,aAAa,GAAKP,GAE9C,MAAMG,EAAAA,CAAUA,CAACK,KAAK,CAAC,UAAiB,OAAPP,GAAUC,GAEnD,EACJ,CAEQO,kBAAkBC,CAAkB,CAAEH,CAAkB,CAAE,CAI9D,QAAOI,EAHgBpB,MAAM,CAAC,GACnBqB,EAAGL,aAAa,GAAKA,GAEpBX,MAAM,CAGdiB,EAHiB,OAAO,QAGPN,CAAkB,CAAEO,CAAqB,CAAE,CAChE,MAAO,CACHC,gBAAiBD,EACjBP,cAAeA,EACfS,OAAQ,iBACZ,CACJ,CAEA,MAAcC,YAAYhB,CAAW,CAAEiB,CAAe,CAAEC,CAAc,CAAE,CACpE,IAAIjB,EAAgB,CAAC,EAWrB,OAAOA,GAVUiB,EACF,MAAMhB,EAAAA,CAAUA,CAACC,GAAG,CAAE,SAAS,CACtCgB,MAAO,CACHC,MAAOH,EACPI,SAAUH,CACd,CACJ,GAEW,MAAMhB,EAAAA,CAAUA,CAACC,GAAG,CAAC,UAAiB,OAAPH,GAGlD,CAEA,MAAMsB,gBAAgBC,CAAY,CAAEjB,CAAkB,CAAEO,CAAqB,CAAE,CAC3EU,EAAMlC,OAAO,CAAC,MAAOmC,IACjB,IAAMxB,EAASwB,EAAKhC,GAAG,CACvB,GAAIQ,EAAQ,CACR,IAAIC,EAAW,MAAM,IAAI,CAACe,WAAW,CAAChB,GAClCC,IACI,EAAUG,IADJ,cACsB,EAAEH,GAASG,kBAAkB,CAAG,IAChEH,EAASG,kBAAkB,CAAGH,EAASG,kBAAkB,CAACqB,GAAG,CAAC,IACtDpB,EAAOC,aAAa,GAAKA,GAAiBD,YAA8B,GAAvBU,MAAM,GACvDV,EAAOU,MAAM,CAAG,mBAEbV,IAEP,IAAK,CAACG,iBAAiB,CAACP,EAASG,kBAAkB,EAAI,EAAE,CAAEE,IAC3DL,EAASG,UADkE,QAChD,CAACR,IAAI,CAAC,IAAI,CAACgB,gBAAgB,CAACN,EAAeO,IAE1E,MAAMX,EAAAA,CAAUA,CAACK,KAAK,CAAC,UAAiB,OAAPP,GAAUC,GAEnD,MAEI,CAFG,GAEC,CAACyB,sBAAsB,CAACF,EAAKJ,KAAK,CAAEI,EAAKH,QAAQ,CAAEf,EAAeO,EAE9E,EACJ,CAEA,MAAaa,uBAAuBN,CAAU,CAAEC,CAAa,CAAEf,CAAkB,CAAEO,CAAqB,CAAE,CAEtG,IAAIZ,EAAW,MAAMC,EAAAA,CAAUA,CAACC,GAAG,CAAE,SAAS,CAAEgB,MADlC,OAAEC,WAAOC,CAAS,CACsB,GAClDpB,GAAYA,EAAS0B,IAAI,CAAC,EAAE,EAAE,CAE9B1B,CADAA,EAAWA,EAAS0B,IAAI,CAAC,IAChBvB,kBAAkB,CAACR,IAAI,CAAC,IAAI,CAACgB,gBAAgB,CAACN,EAAeO,IAC1D,MAAMX,EAAAA,CAAUA,CAACK,KAAK,CAAC,UAAuB,OAAbN,EAAST,GAAG,EAAIS,GAErE,CACJ,CAEA,MAAe,IAAIjB,mBAAmBA,EAAC,0KCqCvC,MAvH+B,OAAC,QAC9B4C,CAAM,YAsHOC,EArHbC,CAAY,OACZC,CAAK,OACLC,CAAK,GAmH8BH,EAAC,KAlHpCI,CAAQ,SACRC,CAAO,CACH,GACE,CAACC,EAAOC,EAAS,CAAGC,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAS,GACrC,CAACC,EAAMC,EAAQ,CAAGF,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAS,IACnC,CAACG,EAAKC,EAAO,CAAGJ,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAM,MAC9BK,EAAYC,CAAAA,EAAAA,EAAAA,MAAAA,CAAMA,CAAM,MACtB,GAAEC,CAAC,CAAE,CAAGC,CAAAA,EAAAA,EAAAA,EAAAA,CAAcA,CAAC,UAE/BC,CAAAA,EAAAA,EAAAA,SAAAA,CAASA,CAAC,KACRP,EAAQN,EACV,EAAG,CAACA,EAAS,EAEb,IAAMc,EAAc,UAelB,IAAMC,EAAOC,CAbS,QAENC,EADd,GAYyBC,CAZnBD,EAAME,EAAQC,KAAK,CAAC,KACnBC,EAAAA,OAAOJ,EAAAA,CAAG,CAAC,EAAE,CAACK,KAAK,CAAC,YAAbL,KAAAA,EAAAA,CAAyB,CAAC,EAAE,CACnCM,CADON,CACAO,KAAKP,CAAG,CAAC,EAAE,EACrBQ,EAAIF,EAAK7D,MAAM,CACZgE,EAAQ,IAAIC,WAAWF,GAC9B,KAAOA,IAAK,CACVC,CAAK,CAACD,EAAE,CAAGF,EAAKK,UAAU,CAACH,GAE7B,OAAO,IAAII,KAAK,CAACH,EAAM,CAAE,CAAEI,KAAMT,CAAK,GACxC,EAEeZ,EAAUsB,OAAO,CAACC,QAAQ,GAAGC,SAAS,CAAC,aAAc,KAIpEhC,EADgBiC,MACRC,EAFkBC,GAAG,EAAIC,OAAOC,SAAAA,EAAW,eACT,CAACvB,IAG3C,IAAMwB,EAAK,IAAIC,IAJsE,KAKrFD,EAAGE,MAAM,CAAC,OAAQ1B,EAAMV,GAExB,GAAI,CACF,IAAMqC,EAAM,MAAMzE,EAAAA,CAAUA,CAAC0E,IAAI,CAAC,SAAUJ,EAAI,CAC9C,eAAgB,qBAClB,GAEIG,GAAOA,EAAInF,GAAG,EAAE,EACZmF,EAAInF,GAAG,CAEjB,CAAE,QAAM,CACN,KAjCa,CAiCPqF,wCACR,CACAC,EAAAA,EAAKA,CAACC,OAAO,CAACnC,EAAE,sCAChBd,GAAa,GACbW,EAAO,MACPF,EAAQ,QACRH,EAAS,EACX,EAEA,MACE,+BACE,UAAC4C,MAAAA,UACC,WAACC,EAAAA,CAAKA,CAAAA,CACJC,KAAMtD,EACNuD,KAAK,KACLC,kBAAgB,cAChBC,OAAQ,IAAMvD,GAAa,GAC3BwD,QAAQ,cAER,WAACL,EAAAA,CAAKA,CAACM,IAAI,YACT,WAACP,MAAAA,CAAIQ,UAAU,mFACb,UAACC,IAAYA,CACXC,IAAKhD,EACLiD,MAAO,IACPC,OAAQ,IACRC,aAAc,EACd1D,MAAOA,EACP2D,IANWL,EAMJ,CAAC,EAAG,EAAG,EAAG,GAAI,CACrB1D,MAAOS,GAAYT,EACnBgE,CADavD,KACN,CAACmD,MAAO,OAAOC,OAAQ,MAAM,IAEtC,UAACZ,MAAAA,CAAIQ,UAAU,2BACb,UAACQ,OAAAA,UAAMpD,EAAE,qEAIb,UAACoC,MAAAA,CAAIQ,UAAU,qBACb,WAACS,EAAAA,CAAGA,CAAAA,WACF,UAACC,EAAAA,CAAGA,CAAAA,CAACC,GAAI,EAAGC,GAAI,EAAGC,GAAI,EAAGb,UAAU,gBAClC,UAACc,IAAAA,UAAG1D,EAAE,YAER,UAACsD,EAAAA,CAAGA,CAAAA,CAACC,GAAI,GAAIC,GAAI,GAAIC,GAAI,YACvB,UAACE,IAAWA,CACV7G,MAAOyC,EACPqE,QAAQ,OACRC,IAAK,EACLC,IAAK,GACLC,KAAM,IACNC,QAAQ,UANEL,SAOA,GACRnE,EAASyE,OAAOC,EAAYC,MAAM,CAACrH,KAAK,eAOpD,WAACuF,EAAAA,CAAKA,CAAC+B,MAAM,YACX,UAACC,EAAAA,CAAMA,CAAAA,CAACC,QAASnE,WAAcH,EAAE,UACjC,UAACqE,EAAAA,CAAMA,CAAAA,CAACL,QAAQ,SAASM,QAAS,IAAMpF,GAAa,YAClDc,EAAE,qBAOjB,qVC+iCA,MAvpCwB,OAAC,QAAEuE,CAAM,YAupClBC,CAvpCoBC,CAAW,CAAwB,GAC5DC,EAAY3E,CAAAA,EAAAA,EAAAA,EAspCQyE,EAAC,EAtpCTzE,CAAMA,CAAC,MACnB4E,EAAU5E,CAAAA,EAAAA,EAAAA,MAAAA,CAAMA,CAAC,MACjB,GAAEC,CAAC,MAAE4E,CAAI,CAAE,CAAG3E,CAAAA,EAAAA,EAAAA,EAAAA,CAAcA,CAAC,UAC7B4E,EAAqB,CACvBC,iBAAkB,GAClBC,MAAO,GACPC,QAAS,GACTC,aAAc,GACdC,QAAS,GACTC,iBAAkB,KAClBC,UAAW,GACXC,UAAWrF,EAAE,YACbsF,aAAc,GACdC,QAAS,GACTC,UAAW,GACXC,UAAW,GACXC,KAAM,GACNC,OAAQ,EAAE,CACVhH,MAAO,EAAE,CACT8F,YAAa,GACbmB,UAAW,EAAE,CACbC,QAAS,EAAE,CACXC,QAAS,EAAE,CACXC,YAAa,GACbC,OAAQ,EAAE,CACVC,OAAQ,KACRC,WAAY,GACZC,KAAM,GACNC,oBAAoB,EACpBC,WAAY,EAChB,EACM,OAAE9H,CAAK,CAAE,CAAG+H,CAAAA,EAAAA,EAAAA,SAAAA,CAASA,GACrBC,EAAc3B,EAAK4B,QAAQ,CAC3BC,EAAgC,OAAlB7B,EAAK4B,QAAQ,CAAY,CAAEE,SAAU,KAAM,EAAI,CAAE3B,MAAO,KAAM,EAE5E,CAAC4B,EAAaC,EAAe,CAAQnH,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAC,EAAE,EAChD,CAACoH,EAAoBC,EAAsB,CAAGrH,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAQ,EAAE,EAChE,CAACsH,EAAeC,EAAiB,CAAGvH,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAQ,EAAE,EACtD,CAACwH,EAAsBC,EAAwB,CAAGzH,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAM,MAChE,CAACqG,EAASqB,EAAW,CAAG1H,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAQ,EAAE,EAC1C,CAACmG,EAAWwB,EAAa,CAAG3H,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAQ,EAAE,EAC9C,CAACoG,EAASwB,EAAW,CAAG5H,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAQ,EAAE,EAC1C,CAAC6H,EAASC,EAAU,CAAG9H,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAQ,EAAE,EACzC,CAAC+H,EAAWC,EAAa,CAAGhI,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAQ,EAAE,EAC9C,CAACiI,GAAkBC,GAAoB,CAAGlI,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAS,GAC3D,EAAGmI,GAAsB,CAAGnI,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAQ,EAAE,EAC9C,CAACoI,GAAKC,GAAO,CAAGrI,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAQ,CAClC,CACIhB,SAAU,GACVD,MAAO,GACPiG,YAAa,GACbsD,gBAAiB/H,EAAE,YACnBgI,cAAe,GACfpL,IAAK,IACT,EACH,EACK,CAACqL,GAAkBC,GAAoB,CAAGzI,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAQ,EAAE,EAC5D,CAAC0I,GAAeC,GAAiB,CAAG3I,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAQ,EAAE,EACtD,CAAC4I,GAAaC,GAAe,CAAG7I,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAQ,EAAE,EAClD,CAAC8I,GAAYC,GAAc,CAAG/I,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAQ,EAAE,EAChDgJ,GAAoBlE,GAAuB,QAAbA,CAAM,CAAC,EAAE,EAAc,CAAC,CAACA,CAAM,CAAC,EAAE,CAChE,CAACmE,GAAYC,GAAc,CAAGlJ,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAMoF,GAC5C,CAAC+D,GAAiBC,GAAmB,CAAGpJ,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAQ,EAAE,EAC1D,CAACqJ,GAAUC,GAAY,CAAGtJ,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAQ,EAAE,EAE5CuJ,GAAoB,CACtBzK,MAAO,CAAC,EACR0K,KAAM,CAAElE,MAAO,KAAM,EACrBmE,MAAO,GACX,EAEMC,GAAiB,CACnB5K,MAAO,CAAC,EACR0K,KAAM,CAAElE,MAAO,KAAM,EACrBqE,OAAQ,uLACRF,MAAO,IACPG,KAAM,EACV,EAYAnJ,CAAAA,EAAAA,EAAAA,SAAAA,CAASA,CAAC,KAEN,GAAiB,QAAbqE,CAAM,CAAC,EAAE,EAAcE,KAET,EACV6E,MAHgC,GAqmCvCA,CAAuC,EAC5CC,EAASpE,YAnmC6BoE,IAmmCb,CAAGA,GAAYA,EAASpI,IAAI,CAAGoI,EAASpI,IAAI,CAACvE,GAAG,CAAG,GAC5E2M,EAAS/D,SAAS,CAAG+D,EAASC,OAAO,CAACC,MAAM,CAC5CF,EAAS9D,SAAS,CAAG8D,EAASC,OAAO,CAACE,MAAM,CAC5CH,EAAS7D,IAAI,CAAG6D,EAASC,OAAO,CAAC9D,IAAI,CACrC6D,EAASvE,OAAO,CAAGuE,EAASC,OAAO,CAACxE,OAAO,EAAIuE,EAASC,OAAO,CAACxE,OAAO,CAACpI,GAAG,CAAG2M,EAASC,OAAO,CAACxE,OAAO,CAACpI,GAAG,CAAG,IAC7G2M,CADkH,CACzGtE,YAAY,CAAGsE,CADyG,CAChGC,OAAO,EAAID,EAASC,OAAO,CAACvE,YAAY,CAAGsE,EAASC,OAAO,CAACvE,YAAY,CAAG,GAC5GsE,EAAS5D,MAAM,CAAG4D,EAASC,OAAO,CAAC7D,MAAM,CACnC4D,EAASC,OAAO,CAAC7D,MAAM,CAAC9G,GAAG,CAAC,CAAC8K,EAAWC,KAC/B,CAAEC,MAAOF,EAAK5E,KAAK,CAAEjI,MAAO6M,EAAK/M,GAAG,CAAC,GAE9C,EACV,KA5mCgB,GAAM,YAAEkN,CAAU,UAAEC,CAAQ,CAAEC,WAAS,UAAEC,CAAQ,CAAE,CAAGC,SAkkC7DA,CAAmC,CAAE3D,CAAmB,EAC7D,IAAMuD,EAAaP,EAAS3D,SAAS,CAnkC6DW,EAokCnFX,SAAS,CAAC/G,GAAG,CAAC,CAAC8K,EAAWC,IAC1B,EAAEC,MAAOF,EAAK5E,KAAK,CAAEjI,MAAO6M,EAAK/M,GAAG,CAAC,GAE9C,EAAE,CACFmN,EAAWR,EAASY,QAAQ,CAC5BZ,EAASY,QAAQ,CAACtL,GAAG,CAAC,CAAC8K,EAAWC,KACzB,CAAEC,MAAOF,EAAK5E,KAAK,CAAEjI,MAAO6M,EAAK/M,GAAG,CAAC,GAE9C,EAAE,CACFoN,EAAYT,EAAST,QAAQ,CAC7BS,EAAST,QAAQ,CAACjK,GAAG,CAAC,CAAC8K,EAAWC,KACzB,CAAEC,MAAOF,EAAK5E,KAAK,CAAEjI,MAAO6M,EAAK/M,GAAG,CAAC,GAE9C,EAAE,CAEFqN,EAAWV,EAASzD,OAAO,CAC3ByD,EAASzD,OAAO,CAACjH,GAAG,CAAC,IACZ,CAAEgL,MAAOF,EAAK5E,KAAK,CAACwB,EAAY,CAAEzJ,MAAO6M,EAAK/M,GAAG,CAAC,GAE3D,EAAE,CAMR,OALA2M,EAAS,KAAQ,CAAT,EAAqBa,YAAY,CACnCb,EAASa,YAAY,CAACvL,GAAG,CAAC,CAAC8K,EAAWC,KAC7B,CAAEC,MAAOF,EAAKlL,QAAQ,CAAE3B,MAAO6M,EAAK/M,GAAG,CAAC,GAEjD,EAAE,CACD,CAAEkN,sBAAYC,EAAUC,qBAAWC,CAAS,CACvD,IA/lCsD,OAAlBrF,EAAK4B,QAAQ,CAAY,KAAO5B,EAAK4B,QAAQ,EAEjE6D,GALa5F,EAKMO,OAAO,CAAEgE,IAC5B5B,EAAa0C,GAEbzC,EAAW0C,GACXO,GAAoBN,GAJ4B,EAKrCC,GACXrC,GAAsBqC,GAEtBnD,EAAsByC,EAASvD,MAAM,CAAGuD,EAASvD,MAAM,CAAG,EAAE,EAC5DgB,EATqF,EAS3DX,UAAU,CAAGkD,EAASlD,UAAU,CAAG,EAAE,EAC/Da,EAAwBqC,EAAStD,MAAM,EAAIsD,EAAStD,MAAM,CAACrJ,GAAG,CAAG2M,EAAStD,MAAM,CAACrJ,GAAG,CAAG,MACvF+L,GAAc,GAAqB,EAAE,GAAG4B,CAAS,CAhBpC9F,EAgBsB,EAAgB,CAAY,EAAT8E,CAI9DiB,KACAC,EALsE,GAMtEC,KACAC,GAAgB3B,IAChB4B,GAAa5B,IACb6B,GAAW7B,IACX8B,GAAY3B,GAChB,EAAG,EAAE,EAEL,IAAMuB,GAAe,UAQjB,IAAMnB,EAAW,MAAMjM,EAAAA,CAAUA,CAACC,GAAG,CAAC,WAPR,CAOoBwN,KANxCtE,EACNyC,MAAO,IACPE,OAAQ,mGACRC,MAAM,EACN2B,aAAczE,CAClB,GAEIgD,GAAY0B,MAAMC,OAAO,CAAC3B,EAASxK,IAAI,GAAG,EAC3BwK,EAASxK,IAAI,CAEpC,EAEMoM,GAAmB,IACrB,GAAIxM,GAASA,EAAMI,IAAI,EAAIJ,EAAMI,IAAI,CAAChC,MAAM,CAAG,EAAG,CAC9C,IAAMqO,EAAmB,EAAE,CAC3BzM,EAAMI,IAAI,CAACtC,OAAO,CAAC,IAIX4O,EAH6B7N,kBAAkB,CAACd,MAAM,CACtD,GAAiBe,EAAOC,aAAa,GAAK6G,CAAM,CAAC,EAAE,EAAsB,aAAlB9G,EAAOU,MAAM,EAE9CpB,MAAM,EAAE,EAClBC,IAAI,CAAC,CACb6M,MAAOjL,EAAKH,QAAQ,CACpB3B,MAAO8B,EAAKhC,GAAG,CACf4B,MAAOI,EAAKJ,KAAK,EAG7B,GACAqK,GAAmBuC,GACnBzC,GAAe4B,GAAoB,EAC/B,GAAGA,CAAS,CACZ5L,EAF+B,IAExByM,EACX,EACJ,CACJ,EAEME,GAA+B,CAACC,EAAeC,KACjD,IAAIC,GAAS,EAab,OAZAF,EAAO9O,OAAO,CAAC,IACXmC,EAAKpB,kBAAkB,CAACf,OAAO,CAAC,IAC5B,GACIgB,EAAOC,aAAa,GAAK6G,CAAM,CAAC,EAAE,EAClC9G,sBAAOU,MAAM,EACbqN,EAAY5O,GAAG,GAAKgC,EAAKhC,GAAG,CAC9B,CACE6O,GAAS,EACT,MACJ,CACJ,EACJ,GACOA,CACX,EAEMC,GAAc,IAChB,IAAIC,EAAuB,EAAE,CAO7B,MAHAJ,CAHAA,EAASA,EAAO7O,MAAM,CAAC,IACZ4O,GAA6BC,EAAQ3M,GAChD,EACOnC,GAFiD,IAE1C,CAAC,GAFiD,CAG5DkP,EAAc3O,IAAI,CAAC,CAAE6M,MAAOjL,EAAKH,QAAQ,CAAE3B,MAAO8B,EAAKhC,GAAG,EAC9D,GACO+O,CACX,EAEMlB,GAAW,UAOb,IAAM9L,EAAQ,MAAMrB,EAAAA,CAAUA,CAACC,GAAG,CAAC,SANL,CAMewN,MALlC,CAAC,EACR9B,KAAM,CAAElE,MAAO,KAAM,EACrBmE,MAAO,IACPE,OAAQ,iTACZ,GAKA,GAHIX,IACA0C,GAAiBxM,GADP,GAGDsM,MAAMC,OAAO,CAACvM,EAAMI,IAAI,EAAG,CACpC,IAAIwM,EAAgB,EAAE,CACtB,GAAI9C,GAAU,CACV,IAAImD,EAAiBF,GAAY/M,EAAMI,IAAI,EAC3CJ,EAAMI,IAAI,CAACtC,OAAO,CAAC,IACf8O,EAAOvO,IAAI,CAAC,CAAE6M,MAAOjL,EAAKH,QAAQ,CAAE3B,MAAO8B,EAAKhC,GAAG,EACvD,GACA6K,EAAamE,EACjB,MACIjN,CADG,CACGI,IAAI,CAACtC,OAAO,CAAEmC,IAChB2M,EAAOvO,IAAI,CAAC,CAAE6M,MAAOjL,EAAKH,QAAQ,CAAE3B,MAAO8B,EAAKhC,GAAG,EACvD,GACA6K,EAAa8D,EAErB,CACJ,EAEMZ,GAAkB,MAAOkB,IAC3B,IAAMtC,EAAW,MAAMjM,EAAAA,CAAUA,CAACC,GAAG,CAAC,mBAAoBsO,GACtDtC,GAAY0B,MAAMC,OAAO,CAAC3B,EAASxK,IAAI,GAAG,GACtBwK,EAASxK,IAAI,CAEzC,EAGM+L,GAAc,MAAOgB,IACvB,IAAMvC,EAAW,MAAMjM,EAAAA,CAAUA,CAACC,GAAG,CAAC,eAAgBuO,GAClDvC,GAAY0B,MAAMC,OAAO,CAAC3B,EAASxK,IAAI,GAAG,GAC1BwK,EAASxK,IAAI,CACxBrC,EAEOqP,IAFD,CAAC,GAAepC,EAAK/M,GAAG,EAAI2B,EAAMgG,MAAM,CAAE,EAAE,EAClD1F,GAAG,CAAC,CAAC8K,EAAWC,IAAa,EAAEC,MAAOF,EAAK5E,KAAK,CAAEjI,MAAO6M,EAAK/M,GAAG,CAAC,GAG/E,EAGMgO,GAAe,MAAOiB,IACxB,IAAMtC,EAAW,MAAMjM,EAAAA,CAAUA,CAACC,GAAG,CAAC,aAAcsO,GAChDtC,GAAY0B,MAAMC,OAAO,CAAC3B,EAASxK,IAAI,GAAG,GACvBwK,EAASxK,IAAI,CAACF,GAAG,CAAC,CAAC8K,EAAWC,KACtC,CAAEC,MAAOF,EAAK5E,KAAK,CAAEjI,MAAO6M,EAAK/M,GAAG,CAAC,GAIxD,EAEMiO,GAAa,MAAOgB,IACtB,IAAMtC,EAAW,MAAMjM,EAAAA,CAAUA,CAACC,GAAG,CAAC,sBAAuBsO,GACzDtC,GAAY0B,MAAMC,OAAO,CAAC3B,EAASxK,IAAI,GAIvCuJ,GAHiBiB,EAASxK,IAAI,CAACF,GAAG,CAAC,CAGpBkL,EAHgCH,KACpC,CAAEC,MAAOF,EAAK5E,KAAK,CAAEjI,MAAO6M,EAAK/M,GAAG,CAAC,GAIxD,EAEM4N,GAAY,UAOd,IAAMwB,EAAuB,EAAE,CACzBC,EAAe,MAAM3O,EAAAA,CAAUA,CAACC,GAAG,CAAC,cAPrB,CAOoC2O,MAN9C,CAAC,EACRhD,MAAO,IACPD,KAAM,CAAElE,MAAO,KAAM,CACzB,GAIIkH,GAAgBhB,MAAMC,OAAO,CAACe,EAAalN,IAAI,GAE/CoN,IAAAA,IAAM,CAACF,EAAalN,IAAI,CAAE,CAAC4K,EAAWC,KAClCoC,EAAchP,IAAI,CAAC2M,EAAK/M,GAAG,CAC/B,GAQJ,IAAM2M,EAAW,MAAMjM,EAAAA,CAAUA,CAACC,GAAG,CAAC,UALjB,CACjBgB,MAAO,CAAE6N,YAAaJ,CAAc,EACpC9C,MAAO,IACPD,KAAM,CAAElE,MAAO,KAAM,CACzB,GAEIwE,GAAY0B,MAAMC,OAAO,CAAC3B,EAASxK,IAAI,GAIvCyJ,GAHgBe,EAASxK,IAAI,CAACF,GAAG,CAGnBwN,CAHqB1C,EAAWC,IACnC,EAAEC,MAAOF,EAAK5E,KAAK,CAACwB,EAAY,CAAEzJ,MAAO6M,EAAK/M,GAAG,CAAC,GAIrE,EAEMyN,GAAY,MAAOiC,EAAST,KAC9B,IAAIU,EAAkB,EAAE,CACxB,GAAID,EAAI,CACJ,IAAM/C,EAAW,MAAMjM,EAAAA,CAAUA,CAACC,GAAG,CAAC,mBAAsB,OAAH+O,GAAMT,GAC3DtC,GAAYA,EAASxK,IAAI,EAAE,CAC3BwN,EAAWhD,EAASxK,IAAI,CAACF,GAAG,CAAC,CAAC8K,EAAWC,KAC9B,CAAEC,MAAOF,EAAK5E,KAAK,CAAEjI,MAAO6M,EAAK/M,GAAG,GAC/C,EACSqM,IAAI,CAAC,CAACuD,EAAQ9I,IAAW8I,EAAE3C,KAAK,CAAC4C,aAAa,CAAC/I,EAAEmG,KAAK,EAEvE,CACAtC,EAAUgF,EACd,EAEMG,GAAa,IACf/D,GAAc,GAAqB,EAC/B,GAAG4B,CAAS,CACZ,EAF+B,CAE5BoC,CAAG,GAEd,EAEMC,GAAe,IACjB,GAAM,CAAElN,MAAI,OAAE5C,CAAK,CAAE,CAAG+P,EAAE1I,MAAM,CAMhC,GALAwE,GAAc,GAAqB,EAC/B,GAAG4B,CAAS,CACZ,CAAC7K,CAF8B,CAEzB,CAAE5C,EACZ,GAEY,WAAR4C,EAAmB,CACnB,IAAMoN,EAAgBD,EAAE1I,MAAM,CAAC2I,aAAa,CAC5C,GAAID,EAAE1I,MAAM,EAAI2I,GAAkC,MAAjBA,EAAuB,CACpD,IAAMC,EAAcF,EAAE1I,MAAM,CAAC2I,EAAc,CAACE,YAAY,CAAC,oBACzDrE,GAAc,GAAqB,EAC/B,GAAG4B,CAAS,CACZtF,EAF+B,WAEjB8H,EAClB,EACJ,CACJ,CAEY,WAARrN,IACA2K,GAAUvN,EAAOkM,IACjB0D,GAAW,CAAE/G,OAAQ,EAAE,GAE/B,EAEMsH,GAAqB,CAACJ,EAAQnN,KAChCiJ,GAAc,GAAqB,EAC/B,GAAG4B,CAAS,CACZ,CAAC7K,CAF8B,CAEzB,CAAEmN,EACZ,EACJ,EAEMK,GAAoB,IACtBvE,GAAc,GAAqB,EAAE,GAAG4B,CAAS,CAAExE,EAAhB,UAA6BjJ,EAAM,EAC1E,EAMM,CAACqQ,GAAmB,CAAG1N,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAS,IAKxC,CAAC2N,GAAW,CAAG3N,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAS,IAChC,CAAC4N,GAAiB/C,GAAoB,CAAG7K,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAQ,EAAE,EAS3D6N,GAAS,KACX,IAAMC,EAAQ,IAAI1F,GAAI,CACtB0F,EAAMvQ,IAAI,CAAC,CACPyB,SAAU,GACVD,MAAO,GACPuJ,gBAAiB/H,EAAE,YACnBgI,cAAe,GACfvD,YAAa,GACb7H,IAAK,IACT,GACAkL,GAAOyF,GACP5F,GAAoB4F,EAAMxQ,MAAM,CACpC,EACMyQ,GAAY,CAACC,EAASC,KACxB7F,GAAI8F,MAAM,CAACD,EAAG,GACd,IAAMH,EAAQ,IAAI1F,GAAI,CACtBC,GAAOyF,GACP5F,GAAoB4F,EAAMxQ,MAAM,EACb,GAAG,CAAlB8K,GAAI9K,MAAM,EACVuQ,IAER,EACMM,GAAY,CAACf,EAAQa,KACvB,IAAMG,EAAa,IAAIhG,GAAI,CAC3BgG,CAAU,CAACH,EAAE,CAACb,EAAE1I,MAAM,CAACzE,IAAI,CAAC,CAAGmN,EAAE1I,MAAM,CAACrH,KAAK,CAC7CgL,GAAO+F,EACX,EAwBMC,GAAgB,CAACnP,EAAYoP,KAC/B,GAAItF,GAAU,CAEV,IAAMuF,EAAeC,EAAAA,CAAiBA,CAAC5R,eAAe,CAACuM,GAAiBjK,GACxEsP,EAAAA,CAAiBA,CAAChR,iBAAiB,CAAC+Q,EAAczJ,CAAM,CAAC,EAAE,EAE3D0J,EAAAA,CAAiBA,CAACvP,eAAe,CAACC,EAAO4F,CAAM,CAAC,EAAE,CAAEwJ,EAAgBhJ,KAAK,CAC7E,MACIpG,CADG,CACGlC,OAAO,CAAC,MAAOmC,QAYFvB,EAKPA,EAhBR,IACIA,EADAD,EAASwB,EAAKhC,GAAG,CAcrB,GAZIQ,EACAC,EAAW,IADH,EACSC,EAAAA,CAAUA,CAACC,GAAG,CAAC,UAAiB,OAAPH,IACnCwB,EAAKJ,KAAK,EAAII,EAAKH,QAAQ,EAAE,CAQpCrB,EAASC,CADTA,EAAWA,CAAAA,OANXA,EAAW,MAAMC,EAAAA,CAAUA,CAACC,EAMjBF,CANoB,CAAE,SAAS,CACtCkB,MAAO,CACHC,MAAOI,EAAKJ,KAAK,CACjBC,SAAUG,EAAKH,QAAQ,CAE/B,KACWpB,OAAAA,EAAAA,EAAU0B,IAAAA,EAAV1B,KAAAA,EAAAA,EAAgBN,GAAhBM,GAAsB,EAAGA,EAAS0B,IAAI,CAAC,EAAE,CAAG,EAAC,EACtCnC,GAAG,EAErBS,GAAYA,EAAST,GAAG,CAAE,CAC1B,IAAIsR,EAAsB,EAAE,QACxB7Q,GAAAA,OAAAA,EAAAA,EAAUG,OAAVH,WAAUG,EAAVH,KAAAA,EAAAA,EAA8BN,GAA9BM,GAAoC,EAGhC8Q,CAFJD,EAAe,IAAI7Q,EAASG,kBAAkB,CAAC,EAClBd,MAAM,CAAC,GAAiBe,EAAOC,aAAa,GAAKqQ,EAAgBnR,GAAG,EACnFG,MAAM,EAAI,EACpBmR,CADuB,CACVlR,IAAI,CAAC,CACdkB,gBAAiB6P,EAAgBhJ,KAAK,CACtCrH,cAAeqQ,EAAgBnR,GAAG,CAClCuB,OAAQ,iBACZ,GAEA+P,EAAeA,EAAarP,GAAG,CAAC,IACxBpB,EAAOC,aAAa,GAAKqQ,EAAgBnR,GAAG,EAAsB,YAAY,CAA9Ba,EAAOU,MAAM,GAC7DV,EAAOU,MAAM,CAAG,mBAEbV,IAIfyQ,EAAe,CACX,CACIhQ,gBAAiB6P,EAAgBhJ,KAAK,CACtCrH,cAAeqQ,EAAgBnR,GAAG,CAClCuB,OAAQ,iBACZ,EACH,CAELd,EAASG,kBAAkB,CAAG0Q,EAClB,MAAM5Q,EAAAA,CAAUA,CAACK,KAAK,CAAC,UAAiB,OAAPP,GAAUC,EAC3D,CACJ,EAER,EAEM+Q,GAAe,MAAOvB,QAmEhBF,EAYJA,MAfApD,EACA8E,EAhEJxB,EAAEyB,cAAc,GAChB,IAAMC,EAAU7F,GAAW/J,KAAK,CAACE,GAAG,CAAC,CAAC8K,EAAWC,KACtC,CAAEhN,IAAK+M,EAAK7M,KAAK,CAAC,GAE7B,IAAK,IAAI4Q,EAAI,EAAGA,EAAI7F,GAAI9K,MAAM,CAAE2Q,KAosBxC,SAASc,CAAsB,CAAEd,CAAS,EAElC7F,CAAG,CAAC6F,EAAE,CAACjP,QAAQ,CAAGoJ,CAAG,CAAC6F,EAAE,CAACjP,QAAQ,CAACgQ,WAAW,GAC7C5G,CAAG,CAAC6F,EAAE,CAAClP,KAAK,CAAGqJ,CAAG,CAAC6F,EAAE,CAAClP,KAAK,CAACiQ,WAAW,EAE/C,EAzsB2D5G,GAAK6F,GACxD,IAAMgB,EAAmBvC,IAAAA,MAAQ,CAACtE,GAAK,YACjC8G,EAAgBxC,IAAAA,MAAQ,CAACtE,GAAK,SACpC,GAAI6G,EAAiB3R,MAAM,EAAI8K,GAAI9K,MAAM,EAAI4R,EAAc5R,MAAM,EAAI8K,GAAI9K,MAAM,CAAE,YAC7EmF,EAAAA,EAAKA,CAAC0M,KAAK,CAAC5O,EAAE,gCAIlB,IAAM2M,EAAM,CACR5H,MAAO2D,GAAW3D,KAAK,CAAC8J,IAAI,GAC5BvJ,aAAcoD,GAAWpD,YAAY,CAErC6E,SAAUtE,EACJA,EAAQhH,GAAG,CAAC,CAAC8K,EAAWC,IACfD,EAAK7M,KAAK,EAEnB,EAAE,CACRgM,SAAUuE,GAAkBA,GAAgBxO,GAAG,CAAC,CAAC8K,EAAWC,IAAYD,EAAK7M,KAAK,EAAI,EAAE,CACxF8I,UAAWA,EACLA,EAAU/G,GAAG,CAAC,CAAC8K,EAAWC,IACjBD,EAAK7M,KAAK,EAEnB,EAAE,CAERgJ,QAAS4C,GAAW5C,OAAO,CAE3B0D,QAAS,CACLxE,QAAS0D,GAAW1D,OAAO,CAC3BC,aAAcyD,GAAWzD,YAAY,CACrCU,OAAQ+C,GAAW/C,MAAM,CACnB+C,GAAW/C,MAAM,CAAC9G,GAAG,CAAC,CAAC8K,EAAWC,IACzBD,EAAK7M,KAAK,EAEnB,EAAE,CACR2M,OAAQf,GAAWlD,SAAS,CAC5BkE,OAAQhB,GAAWjD,SAAS,CAC5BC,KAAMgD,GAAWhD,IAAI,EAGzB0E,aAAc,IAAIvC,MAAQ0G,EAAQ,CAClCrJ,QAASwD,GAAWxD,OAAO,CAC3BE,UAAWsD,GAAWtD,SAAS,CAC/BC,UAAWqD,GAAWrD,SAAS,CAC/BE,QAASmD,GAAWnD,OAAO,CAC3BpE,KAAMuH,GAAWvD,gBAAgB,CACjCY,YAAa2C,GAAW3C,WAAW,CACnCC,OAAQ0C,GAAW1C,MAAM,CACzBC,OAAQyC,GAAWzC,MAAM,CACzBC,WAAYwC,GAAWxC,UAAU,CACjCC,KAAMuC,GAAWvC,IAAI,CACrBC,mBAAyC,MAArBsC,CAA4B,EAAjBzC,KAAwB,CAAlB,CACrCI,WAAYqC,GAAWrC,UAAU,CACjCyI,oBAAqBpG,GAAWoG,mBAAmB,EAGnDhJ,GAAWA,EAAQ/I,MAAM,EAAE,GACvB+I,OAAO,CAAGA,EAAQjH,GAAG,CAAC,GAAkBwN,EAAQvP,MAAK,EAKzD2L,IACIkE,MADM,CACNA,GAAAA,OAAAA,EAAAA,EAAKvC,EAALuC,UAAKvC,EAALuC,KAAAA,EAAAA,EAAmB5P,GAAnB4P,GAAyB,EAAG,GAAG,CAC1BA,EAAIvC,YAAY,CAAC2E,IAAI,CAAC,GAAcC,EAAIpS,GAAG,GAAK8L,GAAWoG,mBAAmB,GAAG,CAClFnC,EAAImC,mBAAmB,CAAG,IAGlCT,EAAW,wCACX9E,EAAW,MAAMjM,EAAAA,CAAUA,CAACK,KAAK,CAAC,gBAA0B,OAAV4G,CAAM,CAAC,EAAE,EAAIoI,KAE/D0B,EAAW,sCACX9E,EAAW,MAAMjM,EAAAA,CAAUA,CAAC0E,IAAI,CAAC,eAAgB2K,IAEjDpD,GAAYA,EAAS3M,GAAG,EAAE,CAC1B+P,OAAAA,EAAAA,EAAIvC,YAAAA,EAAJuC,KAAAA,EAAAA,EAAkB5P,GAAlB4P,GAAkB5P,GAAS+Q,GAAcnB,EAAIvC,YAAY,CAAEb,GAEvDvJ,EAAE,OAFiE,YAIvEkC,EAAAA,EAAKA,CAACC,OAAO,CAACnC,EAAEqO,IAChBY,IAAAA,IAAW,CAAC,2BAA4B,qBAAkC,OAAb1F,EAAS3M,GAAG,KAEzDyR,EAAZ5F,GAAuB,OAAb,oCACI,yCAEF,oCAAZc,IACA8E,EAAW,sCAEfnM,EAAAA,EAAKA,CAAC0M,KAAK,CAAC5O,EAAEqO,IAEtB,EAIMa,GAAQ,CAAC5C,EAAW6C,KACtB,IAAIC,EAAiB,CAAC,EAChBC,EAAe/C,EAAG5P,MAAM,CAAC,GAAeiN,EAAKwF,KAAK,EAAIA,GAAOtQ,GAAG,CAAE8K,GAAcA,EAAK2F,QAAQ,EACnG,OAAQH,GACJ,KAAK,EACDC,EAAY,CAAEnJ,OAAQsJ,OAAOF,EAAc,EAC3C,KACJ,MAAK,EAED,KACJ,MAAK,EACDD,EAAY,CAAEpJ,OAAQqJ,CAAa,CAE3C,CACA1G,GAAc,GAAqB,EAAE,GAAG4B,CAAS,CAAE,EAAhB,CAAmB6E,CAAS,CAAC,EACpE,EAEMI,GAAY,IACd7G,GAAc,GAAqB,EAAE,GAAG4B,CAAS,CAAElE,EAAhB,SAA4BoJ,EAAU,EAC7E,EAGMC,GAAe,IACjB/G,GAAc,GAAqB,EAC/B,GAAG4B,CAAS,CACZtE,EAF+B,KAEvBqG,EACRlG,mBAA0B,MAANkG,CAAa,CACrC,EACJ,EACA,EAHgD,IAI5C,UAACqD,EAAAA,CAAqBA,CAAAA,CAACC,SAAUxB,GAActL,IAAK6B,EAASkL,cAAenH,GAAYoH,oBAAoB,WACxG,UAACC,EAAAA,CAASA,CAAAA,CAACnN,UAAU,WAAWoN,KAAK,aACjC,UAACC,EAAAA,CAAIA,CAAAA,UACD,WAACA,EAAAA,CAAIA,CAACtN,IAAI,YACN,UAACU,EAAAA,CAAGA,CAAAA,UACA,UAACC,EAAAA,CAAGA,CAAAA,UACA,UAAC2M,EAAAA,CAAIA,CAACC,KAAK,WACQ,SAAd3L,CAAM,CAAC,EAAE,CAAcvE,EAAE,oBAAsBA,EAAE,yBAK9D,UAACqD,EAAAA,CAAGA,CAAAA,CAACT,UAAU,gBACX,UAACU,EAAAA,CAAGA,CAAAA,UACA,WAAC6M,EAAAA,CAAIA,CAACC,KAAK,YACP,UAACD,EAAAA,CAAIA,CAACE,KAAK,EAACzN,UAAU,0BAAkB5C,EAAE,sBAC1C,UAACsQ,EAAAA,EAASA,CAAAA,CACN5Q,KAAK,QACL4M,GAAG,QACHiE,UAAU,EACVzT,MAAO4L,GAAW3D,KAAK,CACvByL,SAAU5D,GACV6D,UAAW,GAAgC,IAAhB3T,EAAM+R,IAAI,GACrC6B,aAAc,CACVD,UAAWzQ,EAAE,+BACjB,EACA2Q,GAAG,QACHC,WAAW,EACXC,KAAM,WAKtB,UAACxN,EAAAA,CAAGA,CAAAA,CAACT,UAAU,gBACX,UAACU,EAAAA,CAAGA,CAAAA,UACA,WAAC6M,EAAAA,CAAIA,CAACC,KAAK,YACP,WAACD,EAAAA,CAAIA,CAACE,KAAK,YAAC,IAAErQ,EAAE,kBAChB,UAAC8Q,EAAAA,CAAeA,CAAAA,CAACC,YAAarI,GAAW3C,WAAW,CAAEyK,SAAU,GAActD,GAAkB8D,YAI5G,UAAC3N,EAAAA,CAAGA,CAAAA,CAACT,UAAU,gBACX,UAACU,EAAAA,CAAGA,CAAAA,UACA,WAAC6M,EAAAA,CAAIA,CAACC,KAAK,YACP,WAACD,EAAAA,CAAIA,CAACE,KAAK,EAACzN,UAAU,kCACjB5C,EAAE,eACH,UAACmQ,EAAAA,CAAIA,CAACc,KAAK,EACPrO,UAAU,mCACVzB,KAAK,SACL+P,QAASxI,GAAWtC,kBAAkB,CACtCkG,GAAG,gBACHzC,MAAO7J,EAAE,yBAGjB,UAACmR,EAAAA,OAAuBA,CAAAA,CACpB/R,MAAO,GAAasQ,GAAapD,GACjCrG,OAAQgB,EACR9F,KAAM,iBAKtB,WAACkC,EAAAA,CAAGA,CAAAA,CAACT,UAAU,iBACX,UAACU,EAAAA,CAAGA,CAAAA,CAACG,GAAI,EAAGF,GAAI,WACZ,WAAC4M,EAAAA,CAAIA,CAACC,KAAK,YACP,UAACD,EAAAA,CAAIA,CAACE,KAAK,WAAErQ,EAAE,aACf,UAACsQ,EAAAA,EAASA,CAAAA,CACN5Q,KAAK,UACL4M,GAAG,UACHxP,MAAO4L,GAAWxD,OAAO,CACzBkM,QAAQ,gGACRV,aAAc,CACVU,QAASpR,EAAE,0BACf,EACAwQ,SAAU5D,GACV2D,UAAU,EACVI,GAAG,QACHC,WAAW,EACXC,KAAM,SAKlB,UAACvN,EAAAA,CAAGA,CAAAA,CAACG,GAAI,EAAGF,GAAI,WACZ,WAAC8N,EAAAA,CAAUA,CAAAA,CAACzO,UAAU,0BAClB,WAACuN,EAAAA,CAAIA,CAACC,KAAK,EAACxN,UAAU,yDAClB,UAACuN,EAAAA,CAAIA,CAACE,KAAK,WAAErQ,EAAE,eACf,WAACsR,EAAAA,EAAWA,CAAAA,CACRnQ,KAAK,UACLzB,KAAK,YACL4M,GAAG,WACHxP,MAAO4L,GAAWrD,SAAS,CAC3BmL,SAAU5D,GACV2D,UAAU,EACVG,aAAa,aAEb,UAACa,SAAAA,CAAOzU,MAAO4L,GAAWrD,SAAS,UAAGqD,GAAWrD,SAAS,GACzDsB,EAAY9H,GAAG,CAAC,CAAC2S,EAAY5H,IAEtB,UAAC2H,SAAAA,CAEGzU,MAAO0U,EAAMnM,SAAS,UACxB,IAAwBmM,MAAAA,CAApBA,EAAMnM,SAAS,CAAC,MAAgB,OAAZmM,EAAMzM,KAAK,GAF5B6E,UAQzB,WAACuG,EAAAA,CAAIA,CAACC,KAAK,EAACxN,UAAU,qDAClB,UAACuN,EAAAA,CAAIA,CAACE,KAAK,WAAC,SACZ,UAACC,EAAAA,EAASA,CAAAA,CACN5Q,KAAK,YACL4M,GAAG,YACHxP,MAAO4L,GAAWtD,SAAS,CAC3BoL,SAAU5D,GACV2D,UAAU,EACVI,GAAG,QACHC,WAAW,EACXC,KAAM,EACNH,aAAc,CAAC,YAM/B,UAACpN,EAAAA,CAAGA,CAAAA,CAACG,GAAI,EAAGF,GAAI,WACZ,WAAC4M,EAAAA,CAAIA,CAACC,KAAK,YACP,UAACD,EAAAA,CAAIA,CAACE,KAAK,WAAErQ,EAAE,aACf,UAACsQ,EAAAA,EAASA,CAAAA,CACN5Q,KAAK,UACL4M,GAAG,UACHxP,MAAO4L,GAAWnD,OAAO,CACzBiL,SAAU5D,GACV2D,UAAU,EACVI,GAAG,QACHC,WAAW,EACXC,KAAM,EACNH,aAAc,CAAC,EACfD,UAAW,KAAM,EACjBW,QAAQ,aAKxB,UAACnB,EAAAA,CAAIA,CAACwB,IAAI,WACN,UAAC/N,IAAAA,UAAG1D,EAAE,eAGV,WAACqD,EAAAA,CAAGA,CAAAA,CAACT,UAAU,iBACX,UAACU,EAAAA,CAAGA,CAAAA,CAACG,EAAE,IAACD,GAAI,WACR,WAAC2M,EAAAA,CAAIA,CAACC,KAAK,YACP,UAACD,EAAAA,CAAIA,CAACE,KAAK,WAAErQ,EAAE,cACf,UAACsQ,EAAAA,EAASA,CAAAA,CACN5Q,KAAK,YACL4M,GAAG,YACHxP,MAAO4L,GAAWlD,SAAS,CAC3BgL,SAAU5D,GACV2D,UAAU,EACVI,GAAG,QACHC,WAAW,EACXC,KAAM,EACNH,aAAc,CAAC,EACfD,UAAW,KAAM,EACjBW,QAAQ,UAIpB,UAAC9N,EAAAA,CAAGA,CAAAA,CAACG,EAAE,IAACD,GAAI,WACR,WAAC2M,EAAAA,CAAIA,CAACC,KAAK,YACP,UAACD,EAAAA,CAAIA,CAACE,KAAK,WAAErQ,EAAE,cACf,UAACsQ,EAAAA,EAASA,CAAAA,CACN5Q,KAAK,YACL4M,GAAG,YACHxP,MAAO4L,GAAWjD,SAAS,CAC3B+K,SAAU5D,GACV2D,UAAU,EACVI,GAAG,QACHC,WAAW,EACXC,KAAM,EACNH,aAAc,CAAC,EACfD,UAAW,KAAM,EACjBW,QAAQ,UAIpB,UAAC9N,EAAAA,CAAGA,CAAAA,CAACG,EAAE,IAACD,GAAI,WACR,WAAC2M,EAAAA,CAAIA,CAACC,KAAK,YACP,UAACD,EAAAA,CAAIA,CAACE,KAAK,WAAErQ,EAAE,UACf,UAACsQ,EAAAA,EAASA,CAAAA,CACN5Q,KAAK,OACL4M,GAAG,OACHxP,MAAO4L,GAAWhD,IAAI,CACtB8K,SAAU5D,GACV2D,UAAU,EACVI,GAAG,QACHC,WAAW,EACXC,KAAM,EACNH,aAAc,CAAC,EACfD,UAAW,KAAM,EACjBW,QAAQ,UAIpB,UAAC9N,EAAAA,CAAGA,CAAAA,CAACG,EAAE,IAACD,GAAI,WACR,WAAC2M,EAAAA,CAAIA,CAACC,KAAK,YACP,UAACD,EAAAA,CAAIA,CAACE,KAAK,EAACzN,UAAU,0BAAkB5C,EAAE,aAC1C,WAACsR,EAAAA,EAAWA,CAAAA,CACR5R,KAAK,UACL4M,GAAG,UACHxP,MAAO4L,GAAW1D,OAAO,CACzBwL,SAAU5D,GACV8D,aAAc1Q,EAAE,uBAChBuQ,QAAQ,cAER,UAACgB,SAAAA,CAAOzU,MAAM,YAAIkD,EAAE,mBACnB2G,EAAY9H,GAAG,CAAC,CAAC8K,EAAW+D,IAErB,UAAC6D,SAAAA,CAAOG,mBAAkB/H,EAAK1E,YAAY,CAAUnI,MAAO6M,EAAK/M,GAAG,UAC/D+M,EAAK5E,KAAK,EADmC2I,YAQtE,UAACpK,EAAAA,CAAGA,CAAAA,CAACG,EAAE,IAACD,GAAI,WACR,WAAC2M,EAAAA,CAAIA,CAACC,KAAK,EAACjN,MAAO,CAAEwO,SAAU,OAAQ,YACnC,UAACxB,EAAAA,CAAIA,CAACE,KAAK,WAAErQ,EAAE,oBACf,UAAC4R,EAAAA,EAAWA,CAAAA,CACRC,gBAAiB,CACbC,gBAAiB9R,EAAE,iBACnB+R,oBAAqB,0BACzB,EACAC,QAAS1K,EACTxK,MAAO4L,GAAW/C,MAAM,CACxB/C,UAAW,SACX4N,SAAU,GAAYvD,GAAmBJ,EAAG,UAC5CoF,WAAYjS,EAAE,2BAK9B,UAACiQ,EAAAA,CAAIA,CAACwB,IAAI,WACN,UAAC/N,IAAAA,UAAG1D,EAAE,mBAEV,UAACkS,KAAAA,CAAAA,GACD,UAAC7O,EAAAA,CAAGA,CAAAA,CAACT,UAAU,gBACX,UAACU,EAAAA,CAAGA,CAAAA,UACA,UAAC6M,EAAAA,CAAIA,CAACC,KAAK,WACP,WAAC+B,EAAAA,CAAIA,CAAAA,CACDC,UAAW1K,GACX2K,SAAWC,GAAW3K,GAAoB2K,GAC1ChG,GAAG,qCAEH,UAACiG,EAAAA,CAAGA,CAAAA,CACAC,SAAU,GACVzN,MACI,UAAC3C,MAAAA,UACG,UAACqQ,IAAAA,UAAGzS,EAAE,6BAId,UAACqD,EAAAA,CAAGA,CAAAA,UACA,UAACC,EAAAA,CAAGA,CAAAA,CAACE,EAAE,IAACC,GAAI,WACR,WAAC0M,EAAAA,CAAIA,CAACC,KAAK,EAACjN,MAAO,CAAEuP,WAAY,MAAO,YACpC,UAACvC,EAAAA,CAAIA,CAACE,KAAK,WAAErQ,EAAE,kBACf,UAAC4R,EAAAA,EAAWA,CAAAA,CACRC,gBAAiB,CACbC,gBAAiB,eACjBC,oBAAqB,wBACzB,EACAC,QAASxK,EACTgJ,SAAU,GAAYvD,GAAmBJ,EAAG,SAC5CjK,UAAW,OACX9F,MAAO4L,GAAW/J,KAAK,CACvBsT,WAAYjS,EAAE,0BAMjC6H,GAAIhJ,GAAG,CAAC,CAAC8K,EAAW+D,IAEb,WAAC6E,EAAAA,CAAGA,CAAAA,CACAC,SAAU,GAAS,OAAN9E,EAAI,GACjB3I,MAAO,GAAuB2I,MAAAA,CAApB1N,EAAE,eAAe,KAAS,OAAN0N,EAAI,aAElC,WAACrK,EAAAA,CAAGA,CAAAA,CAACT,UAAU,iBACX,UAACU,EAAAA,CAAGA,CAAAA,CAACG,GAAI,EAAGF,GAAI,WACZ,WAAC4M,EAAAA,CAAIA,CAACC,KAAK,EAACjN,MAAO,CAAEuP,WAAY,MAAO,YACpC,UAACpP,EAAAA,CAAGA,CAAAA,CAACV,UAAU,eACX,UAACuN,EAAAA,CAAIA,CAACE,KAAK,WAAErQ,EAAE,YAEnB,UAAC6J,QAAAA,CAAMjH,UAAU,iBACb,UAAC0N,EAAAA,EAASA,CAAAA,CACN5Q,KAAK,WACL4M,GAAG,WACHxP,MAAO6M,EAAKlL,QAAQ,CACpB+R,SAAU,GAAY5C,GAAUf,EAAGa,YAMnD,UAACpK,EAAAA,CAAGA,CAAAA,CAACG,GAAI,EAAGF,GAAI,WACZ,WAAC4M,EAAAA,CAAIA,CAACC,KAAK,EAACjN,MAAO,CAAEuP,WAAY,MAAO,YACpC,UAACpP,EAAAA,CAAGA,CAAAA,CAACV,UAAU,eACX,UAACuN,EAAAA,CAAIA,CAACE,KAAK,WAAErQ,EAAE,aAEnB,UAAC6J,QAAAA,CAAMjH,UAAU,iBACb,UAAC0N,EAAAA,EAASA,CAAAA,CACN5Q,KAAK,QACL4M,GAAG,QACHxP,MAAO6M,EAAKnL,KAAK,CACjBgS,SAAU,GAAY5C,GAAUf,EAAGa,GACnC0D,QAAQ,wBACRV,aAAc,CACVU,QAAS,4BACb,WAMhB,UAAC9N,EAAAA,CAAGA,CAAAA,CAACG,GAAI,EAAGF,GAAI,WACZ,WAAC8N,EAAAA,CAAUA,CAAAA,CAACzO,UAAU,0BAClB,WAACuN,EAAAA,CAAIA,CAACC,KAAK,EACPjN,MAAO,CAAEuP,WAAY,MAAO,EAC5B9P,UAAU,yDAEV,UAACuN,EAAAA,CAAIA,CAACE,KAAK,WAAErQ,EAAE,eACf,WAACsR,EAAAA,EAAWA,CAAAA,CACRnQ,KAAK,UACLzB,KAAK,kBACL4M,GAAG,kBACHxP,MAAO6M,EAAK5B,eAAe,CAC3ByI,SAAU,GAAY5C,GAAUf,EAAGa,aAEnC,UAAC6D,SAAAA,CAAOzU,MAAO6M,EAAK5B,eAAe,UAC9B4B,EAAK5B,eAAe,GAExBpB,EAAY9H,GAAG,CAAC,CAAC8K,EAAW+D,IAErB,UAAC6D,SAAAA,CAEGzU,MAAO6M,EAAKtE,SAAS,UACvB,IAAuBsE,MAAAA,CAAnBA,EAAKtE,SAAS,CAAC,MAAe,OAAXsE,EAAK5E,KAAK,GAF1B2I,UAQzB,WAACyC,EAAAA,CAAIA,CAACC,KAAK,EACPjN,MAAO,CAAEuP,WAAY,MAAO,EAC5B9P,UAAU,qDAEV,UAACuN,EAAAA,CAAIA,CAACE,KAAK,WAAC,SACZ,UAACC,EAAAA,EAASA,CAAAA,CACN5Q,KAAK,gBACL4M,GAAG,gBACHxP,MAAO6M,EAAK3B,aAAa,CACzBwI,SAAU,GAAY5C,GAAUf,EAAGa,aAMnD,UAACpK,EAAAA,CAAGA,CAAAA,UACA,UAAC6M,EAAAA,CAAIA,CAACC,KAAK,EAACxN,UAAU,eAClB,UAAC+P,QAAAA,CACGjT,KAAK,cACL4M,GAAG,cACHxP,MAAO4L,GAAW3D,KAAK,CACvB5D,KAAK,kBAKrB,UAACkC,EAAAA,CAAGA,CAAAA,UACO,MACH,UAACD,OAAAA,CAAAA,GAED,UAACE,EAAAA,CAAGA,CAAAA,CAACsP,EAAE,IAACnP,GAAG,aACP,UAACY,EAAAA,CAAMA,CAAAA,CACHgO,SAAU,GAAY1K,GAAoB2K,GAC1CtO,QAAQ,YACRM,QAAS,GAAOkJ,GAAUX,EAAGa,YAE5B1N,EAAE,oBAQ/B,UAACuS,EAAAA,CAAGA,CAAAA,CACAxN,MACI,UAAC3C,MAAAA,UACG,WAACgB,OAAAA,CAAKkB,QAASgJ,aACV,IACD,UAACmF,IAAAA,UAAGzS,EAAE,sCAUtC,UAACiQ,EAAAA,CAAIA,CAACwB,IAAI,WACN,UAAC/N,IAAAA,UAAG1D,EAAE,gBAEV,WAACqD,EAAAA,CAAGA,CAAAA,CAACT,UAAU,iBACX,UAACU,EAAAA,CAAGA,CAAAA,CAACG,EAAE,IAACD,GAAI,WACR,WAAC2M,EAAAA,CAAIA,CAACC,KAAK,YACP,UAACD,EAAAA,CAAIA,CAACE,KAAK,WAAErQ,EAAE,sBAEf,WAACsR,EAAAA,EAAWA,CAAAA,CACR5R,KAAK,mBACL4M,GAAG,mBACHxP,MAAuC,OAAhC4L,GAAWvD,gBAAgB,CAAY,GAAKuD,GAAWvD,gBAAgB,CAC9EqL,SAAU5D,GACV2D,UAAU,EACVG,aAAa,aAEb,UAACa,SAAAA,CAAOzU,MAAM,YAAIkD,EAAE,4BACnBiI,GAAiBpJ,GAAG,CAAC,CAAC8K,EAAM+D,IAErB,UAAC6D,SAAAA,CAAezU,MAAO6M,EAAK/M,GAAG,UAC1B+M,EAAK5E,KAAK,EADF2I,YAQjC,UAACpK,EAAAA,CAAGA,CAAAA,CAACG,EAAE,IAACD,GAAI,WACR,WAAC2M,EAAAA,CAAIA,CAACC,KAAK,YACP,UAACD,EAAAA,CAAIA,CAACE,KAAK,WAAErQ,EAAE,aACf,UAAC4R,EAAAA,EAAWA,CAAAA,CACRC,gBAAiB,CACbC,gBAAiB9R,EAAE,iBACnB+R,oBAAqB,2BACzB,EACAC,QAAS3J,GACTvL,MAAO+I,EACP2K,SAprBb3D,CAorBuBgG,GAnrB1CxL,EAAWwF,EACf,EAmrBoCjK,UAAW,WACXqP,WAAYjS,EAAE,mBAElB,UAACoD,OAAAA,UAAMgK,UAGf,UAAC9J,EAAAA,CAAGA,CAAAA,CAACG,EAAE,IAACD,GAAI,WACR,WAAC2M,EAAAA,CAAIA,CAACC,KAAK,YACP,UAACD,EAAAA,CAAIA,CAACE,KAAK,WAAErQ,EAAE,eACf,UAAC4R,EAAAA,EAAWA,CAAAA,CACRC,gBAAiB,CACbC,gBAAiB9R,EAAE,mBACnB+R,oBAAqB,4BACzB,EACAC,QAAS7J,GACTrL,MAAO8I,EACP4K,SA3sBX3D,CA2sBqBiG,GA1sB1C1L,EAAayF,EACjB,EA0sBoCjK,UAAW,YACXqP,WAAYjS,EAAE,qBAEjBmN,WAIb,WAAC9J,EAAAA,CAAGA,CAAAA,CAACT,UAAU,iBACX,UAACU,EAAAA,CAAGA,CAAAA,CAACE,EAAE,IAACC,GAAI,WACR,WAAC0M,EAAAA,CAAIA,CAACC,KAAK,EAACjN,MAAO,CAAEwO,SAAU,OAAQ,YACnC,UAACxB,EAAAA,CAAIA,CAACE,KAAK,WAAErQ,EAAE,0BACf,UAAC4R,EAAAA,EAAWA,CAAAA,CACRC,gBAAiB,CACbC,gBAAiB9R,EAAE,wBACnB+R,oBAAqB,0BACzB,EACAC,QAASzJ,GACTwK,cAn+Bd,CAACf,EAAgBtV,KACnC,GAAI,CAACA,EACD,MADS,CACFsV,EAEX,IAAMgB,EAAQ,OAAW,IAAW,OAAPtW,GAAU,MACvC,OAAOsV,EAAQtV,MAAM,CAAC,OAAC,OAAEmN,CAAK,CAAqB,UAAKA,GAASA,EAAMlJ,KAAK,CAACqS,IACjF,EA89BoCxC,SAnuBf,CAmuByByC,GAluB1C9L,EAAW0F,EACf,EAkuBoC/P,MAAOgJ,EACPlD,UAAW,WACXqP,WAAYjS,EAAE,yBAI1B,UAACsD,EAAAA,CAAGA,CAAAA,CAACE,EAAE,IAACC,GAAI,WACR,WAAC0M,EAAAA,CAAIA,CAACC,KAAK,EAACjN,MAAO,CAAEwO,SAAU,OAAQ,YACnC,UAACxB,EAAAA,CAAIA,CAACE,KAAK,WAAErQ,EAAE,cACf,UAAC4R,EAAAA,EAAWA,CAAAA,CACRC,gBAAiB,CACbC,gBAAiB9R,EAAE,kBACnB+R,oBAAqB,2BACzB,EACAC,QAASlJ,GACT0H,SApuBb,CAouBuB0C,GAnuB1C5I,GAAoBuC,EACxB,EAmuBoC/P,MAAOuQ,GACPzK,UAAW,WACXqP,WAAYjS,EAAE,4BAK9B,WAACqD,EAAAA,CAAGA,CAAAA,CAACT,UAAU,iBACX,UAACU,EAAAA,CAAGA,CAAAA,CAACC,GAAI,GAAIC,GAAI,EAAGC,GAAI,WACpB,WAAC0M,EAAAA,CAAIA,CAACC,KAAK,YACP,UAACD,EAAAA,CAAIA,CAACE,KAAK,WAAErQ,EAAE,gBACf,UAACsQ,EAAAA,EAASA,CAAAA,CACN5Q,KAAK,aACL4M,GAAG,aACHxP,MAAO4L,GAAWxC,UAAU,CAC5BsK,SAAU5D,GACV2D,UAAU,EACVI,GAAG,QACHC,WAAW,EACXC,KAAM,EACNH,aAAc,CAAC,EACfD,UAAW,KAAM,EACjBW,QAAQ,UAIpB,UAAC9N,EAAAA,CAAGA,CAAAA,CAACC,GAAI,GAAIC,GAAI,EAAGC,GAAI,WACpB,WAAC0M,EAAAA,CAAIA,CAACC,KAAK,YACP,UAACD,EAAAA,CAAIA,CAACE,KAAK,WAAErQ,EAAE,UACf,UAACsQ,EAAAA,EAASA,CAAAA,CACN5Q,KAAK,OACL4M,GAAG,OACHxP,MAAO4L,GAAWvC,IAAI,CACtBqK,SAAU5D,GACV2D,UAAU,EACVI,GAAG,QACHC,WAAW,EACXC,KAAM,EACNH,aAAc,CAAC,EACfD,UAAW,IAAM,GACjBW,QAAQ,aAKxB,UAACnB,EAAAA,CAAIA,CAACwB,IAAI,WACN,UAAC/N,IAAAA,UAAG1D,EAAE,oBAEV,UAACqD,EAAAA,CAAGA,CAAAA,UACA,UAACC,EAAAA,CAAGA,CAAAA,UACA,UAAC6P,EAAAA,CAAaA,CAAAA,CACVhE,MAAO,EACPiE,MAAOvM,EACPwM,QAAStM,EACTuM,SAAU,CAAChH,EAAS6C,IAAeD,GAAM5C,EAAI6C,GAC7CoE,eAAgB,GAAsB/D,GAAUC,SAI5D,UAACpM,EAAAA,CAAGA,CAAAA,CAACT,UAAU,gBACX,WAACU,EAAAA,CAAGA,CAAAA,WACA,UAACe,EAAAA,CAAMA,CAAAA,CAACzB,UAAU,OAAOzB,KAAK,SAAS6C,QAAQ,UAAUlB,IAAK4B,WACzD1E,EAAE,YAEP,UAACqE,EAAAA,CAAMA,CAAAA,CAACzB,UAAU,OAAO0B,QArwBhC,CAqwByCkP,IApwB1D7K,GAAc9D,GACdiC,EAAsB,EAAE,EACxBE,EAAiB,EAAE,EACnBE,EAAwB,MACxBY,GAAO,EAAE,EAETV,EAAa,EAAE,EACfC,EAAW,EAAE,EACba,GAAoB,EAAE,EACtBM,GAAc,EAAE,EAChBZ,GAAsB,EAAE,EAExBT,EAAW,EAAE,EAEbmD,GAAoB,EAAE,EAEtB5I,OAAO+R,QAAQ,CAAC,EAAG,EACvB,EAmvB4EzP,QAAQ,gBACnDhE,EAAE,WAEP,UAAC0T,IAAIA,CAACC,KAAK,eAAehD,GAAG,qBAAxB+C,GACD,UAACrP,EAAAA,CAAMA,CAAAA,CAACL,QAAQ,qBAAahE,EAAE,2BASnE,mHCxqCA,IAAM4T,EAA8BC,EAAAA,UAAgB,CAAC,EAA9B,CAKpB/Q,QALmD,EAApB,SAChCF,CAAS,UACTkR,CAAQ,CACRnD,GAAIoD,EAAY,MAAM,CACtB,GAAGC,EACJ,GAEC,OADAF,EAAWG,CAAAA,EAAAA,EAAAA,EAAAA,CAAkBA,CAACH,EAAU,oBACpBI,CAAAA,EAAAA,EAAAA,GAAAA,CAAIA,CAACH,EAAW,CAClCjR,IAAKA,EACLF,UAAWuR,IAAWvR,EAAWkR,GACjC,GAAGE,CAAK,EAEZ,GACAJ,EAJyBO,WAIC,CAAG,iBCG7B,IAAM9C,EAA0BwC,EAAAA,SAAb,CAA6B,CAAC,GAQ9C/Q,MAR2B,EAAoB,UAChDgR,CAAQ,MACRvR,CAAI,eACJ6R,CAAa,WACbxR,CAAS,CAET+N,CADA,EACIoD,EAAY,KAAK,CACrB,GAAGC,EACJ,GACCF,EAAWG,CAAAA,EAAAA,EAAAA,EAAAA,CAAkBA,CAACH,EAAU,eAIxC,IAAMO,EAAeC,CAAAA,EAAAA,EAAAA,OAAAA,CAAOA,CAAC,IAAO,GAAC,EAAI,EAAE,EAC3C,MAAoBJ,CAAb,EAAaA,EAAAA,GAAAA,CAAIA,CAACK,EAAP,CAAwBA,CAACC,QAAQ,CAAE,CACnD1X,MAAOuX,EACPI,SAAuBP,CAAb,EAAaA,EAAAA,GAAAA,CAAIA,CAACH,EAAP,CACnBjR,IAAKA,EACL,GAAGkR,CAAK,CACRpR,KAduJ,KAc5IuR,IAAWvR,EAAWkR,EAAUvR,GAAQ,GAAeA,MAA7C4R,CAAiCL,EAAS,KAAQ,OAALvR,GAAQ6R,GAAiB,iBAC7F,EACF,EACF,GACA/C,EAAWqD,WAAW,CAAG,aACzB,MAAeC,OAAOC,MAAM,CAACvD,EAAY,CACvCI,MAAMmC,CACNiB,MAhCsBb,CAgCfc,EAhCqCZ,CAAAA,EAAAA,EAAb,GAAaA,CAAIA,CAACN,EAAgB,CACjEa,GAD0C,MACnBP,CAAb,CDGiBN,CCHJM,CDGK,CCHLA,CADwCN,EACxCM,CAAIA,CAACa,EAAAA,CAAcA,CAAE,CAC1C5T,KAAM,QACN,GAAG6S,CACL,EACF,GA4BEgB,SAvCyBhB,CAuCfiB,EAvCqCf,CAAAA,EAAAA,EAAb,GAAaA,CAAIA,CAACN,EAAgB,CACpEa,GAD6C,MACtBP,CAAb,EAAaA,EAAAA,CAD2CN,EAC3CM,CAAIA,CAACa,EAAP,CAAqBA,CAAE,CAC1C5T,KAAM,WACN,GAAG6S,CACL,EACF,EAmCA,EAAE,EAAC,iFChBH,IAAMrE,EAAwBuF,CAAAA,EAAAA,EAAAA,UAAAA,CAAUA,CAA8C,CAAClB,EAAOlR,KAC5F,GAAM,UAAE2R,CAAQ,UAAE7E,CAAQ,cAAEuF,CAAY,CAAEvS,WAAS,CAAEwS,YAAU,eAAEvF,CAAa,CAAE,GAAGwF,EAAM,CAAGrB,EAGtFsB,EAAmBC,EAAAA,EAAU,GAAGC,KAAK,CAAC,CAAC,GAE7C,MACE,UAACC,EAAAA,EAAMA,CAAAA,CACL5F,cAAeA,GAAiB,CAAC,EACjCyF,iBAAkBA,EAClB1F,SAAU,CAAC8F,EAA6BC,KAEtC,IAAMC,EAAuB,CAC3BtH,eAAgB,KAAO,EACvBuH,gBAAiB,KAAO,EACxBC,cAAe,KACf3R,OAAQ,KACR4R,YAAa,IAAIC,MAAM,UACvBC,SAAS,EACTC,YAAY,EACZC,kBAAkB,EAClBC,WAAY,EACZC,UAAW,GACXC,UAAWC,KAAKC,GAAG,GACnBrV,KAAM,SACNsV,mBAAoB,KAAM,EAC1BC,qBAAsB,KAAM,EAC5BC,QAAS,KAAO,CAClB,EAEI/G,GAEFA,EAASgG,EAAWF,EAAQC,CAFhB,CAIhB,EACC,GAAGN,CAAI,UAEP,GACC,UAAClF,EAAAA,EAAIA,CAAAA,CACHrN,IAAKA,EACL8M,SAAUgH,EAAYxI,YAAY,CAClC+G,aAAcA,EACdvS,UAAWA,EACXwS,WAAYA,WAES,YAApB,OAAOX,EAA0BA,EAASmC,GAAenC,KAKpE,GAEA9E,EAAsB+E,WAAW,CAAG,wBAEpC,MAAe/E,qBAAqBA,EAAC,yEClF9B,IAAMW,EAAY,OAAC,MACxB5Q,CAAI,IACJ4M,CAAE,CACFiE,UAAQ,WACRE,CAAS,cACTC,CAAY,UACZF,CAAQ,OACR1T,CAAK,IACL6T,CAAE,WACFC,CAAS,CACTC,MAAI,CACJO,SAAO,CACP,GAAG4C,EACC,GAuBJ,MACE,UAAC6C,EAAAA,EAAKA,CAAAA,CAACnX,KAAMA,EAAMoX,SAtBJ,CAsBcA,GApB7B,IAAMC,EAA2B,UAAf,OAAOC,EAAmBA,EAAMzH,OAAOyH,GAAO,WAChE,GAAiB,EAACA,GAAOD,IAAR,GAAkBlI,IAAI,EAAO,CAAC,CACtC6B,EAD0C,KAC1CA,EAAAA,KAAAA,EAAAA,EAAcD,SAAAA,GAAa,EAA3BC,uBAGLD,GAAa,CAACA,EAAUuG,GACnBtG,GADyB,IACzBA,EAAAA,KAAAA,EAAAA,EAAcD,SAAAA,GAAa,EAA3BC,cAGLU,GAAW4F,GAET,CADU,CADI,GACAC,OAAO7F,GACd8F,IAAI,CAACF,GACPtG,GADa,IACbA,EAAAA,KAAAA,EAAAA,EAAcU,OAAAA,GAAW,IAAzBV,mBAKb,WAIK,OAAC,OAAEyG,CAAK,MAAEC,CAAI,CAAO,SACpB,iCACE,UAACjH,EAAAA,CAAIA,CAACkH,OAAO,EACV,GAAGF,CAAK,CACR,GAAGnD,CAAK,CACT1H,GAAIA,EACJqE,GAAIA,GAAM,QACVE,KAAMA,EACNyG,UAAWF,EAAKG,OAAO,EAAI,CAAC,CAACH,EAAKxI,KAAK,CACvC4B,SAAU,IACR2G,EAAM3G,QAAQ,CAAC3D,GACX2D,GAAUA,EAAS3D,EACzB,EACA/P,MAAOA,KAAU0a,MAAY1a,EAAQqa,EAAMra,KAAK,GAEjDsa,EAAKG,OAAO,EAAIH,EAAKxI,KAAK,CACzB,UAACuB,EAAAA,CAAIA,CAACkH,OAAO,CAACI,QAAQ,EAACtW,KAAK,mBACzBiW,EAAKxI,KAAK,GAEX,UAKd,EAAE,EAIyB,OAAC,MAC1BlP,CAAI,IACJ4M,CAAE,CACFiE,UAAQ,cACRG,CAAY,UACZF,CAAQ,OACR1T,CAAK,UACL2X,CAAQ,CACR,GAAGT,EACC,GAUJ,MACE,UAAC6C,EAAAA,EAAKA,CAAAA,CAACnX,KAAMA,EAAMoX,SATJ,CAScA,GAR7B,GAAIvG,GAAa,EAACyG,GAAe,IAAhB,CAAQA,CAAQ,CAAC,CAChC,EADoC,IAC7BtG,OAAAA,EAAAA,KAAAA,EAAAA,EAAcD,SAAAA,GAAa,EAA3BC,sBAIX,WAIK,OAAC,OAAEyG,CAAK,MAAEC,CAAI,CAAO,SACpB,iCACE,UAACjH,EAAAA,CAAIA,CAACkH,OAAO,EACX1G,GAAG,SACF,GAAGwG,CAAK,CACR,GAAGnD,CAAK,CACT1H,GAAIA,EACJgL,UAAWF,EAAKG,OAAO,EAAI,CAAC,CAACH,EAAKxI,KAAK,CACvC4B,SAAU,IACR2G,EAAM3G,QAAQ,CAAC3D,GACX2D,GAAUA,EAAS3D,EACzB,EACA/P,WAAiB0a,IAAV1a,EAAsBA,EAAQqa,EAAMra,KAAK,UAE/C2X,IAEF2C,EAAKG,OAAO,EAAIH,EAAKxI,KAAK,CACzB,UAACuB,EAAAA,CAAIA,CAACkH,OAAO,CAACI,QAAQ,EAACtW,KAAK,mBACzBiW,EAAKxI,KAAK,GAEX,UAKd,EAAE,sJC4HF,MApOgC,OAAC,OAAExP,CAAK,QAAE6G,CAAM,IAoOjCyR,EApOmCvW,CAAI,CAA2C,GACvF,GAAEnB,CAAC,CAAE,CAAGC,CAAAA,EAAAA,EAAAA,EAAAA,CAAcA,CAAC,CAmOKyX,EAAC,OAlO7B,CAACC,EAAOC,EAAS,CAAGnY,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,EAAU,GACtC,CAACoY,EAAOC,EAAS,CAAQrY,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAC,EAAE,EACpC,CAACsY,EAASC,EAAW,CAAGvY,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAC,IACjC,CAACwY,EAAUC,EAAY,CAAGzY,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAgB,IAC9C0Y,EAAoB,gBAAThX,EAAyB,SAAW,SAGzDjB,CAAAA,EAAAA,EAAAA,SAAAA,CAASA,CAAC,KACN+F,EAASiS,EAAY,GAAwCjS,MAAAA,CAArCmS,8BAAsB,CAAC,gBAAqB,OAAPnS,IAAYiS,EAAY,KACzF,EAAG,CAACjS,EAAO,EAKX,IAAMoS,EAAiB,CACnBC,KAAM,EACNC,QAAS,OACTC,cAAe,SACfC,WAAY,SACZC,eAAgB,SAChB3V,MAAO,OACPC,OAAQ,OACR2V,YAAa,GACbC,YAAa,UACbC,gBAAiB,UACjB3V,MAAO,QACP4V,WAAY,0BAChB,EA6CMC,EAAmB,CACrBH,YAAa,SACjB,EAIMI,EAAa,IACfpB,EAASZ,EACb,EAkBM,cAAEiC,CAAY,eAAEC,CAAa,cAAEC,CAAY,cAAEC,CAAY,cAAEC,CAAY,gBAAEC,CAAc,CAAE,CAAGC,CAAAA,EAAAA,EAAAA,EAAAA,CAAWA,CAAC,CAC1GC,QAAS,GACTC,OAAQ,UACRC,UAAU,EACVC,QAAS,EACTC,QAAS,IACTC,OApBYC,IAMZhC,EALiBgC,EAAKjb,GAAG,CAAC,CAACib,EAAWpM,IAClCiH,OAAOC,MAAM,CAACkF,EAAM,CAChBC,QAAStY,IAAIuY,eAAe,CAACF,EACjC,KAGAA,EAAK/c,MAAM,CAAG,GACd6a,GAAS,EAEjB,EAWInH,UAiDJ,CAjDewJ,QAiDcH,CAAS,EAelC,MAdiB,UAAU,CAAvB3B,EACkC,SAAS,CAAvC2B,EAAK3Y,IAAI,CAAC+Y,SAAS,CAAC,EAAG,IAGvBhY,EAAAA,EAAKA,CAAC0M,KAAK,CAAC5O,EAAE,6BAEE,UAAU,CAAvBmY,GAC6B,OAAM,GAAI,EAAnChX,IAAI,CAAC+Y,SAAS,CAAC,EAAG,IACzBhY,EAAAA,EAAKA,CAAC0M,KAAK,CAAC5O,EAAE,6BAMf,IACX,CAhEA,GAIMmD,EAAQmR,CAAAA,EAAAA,EAAAA,OAAAA,CAAOA,CACjB,IAAO,EACH,GAAG+D,CAAS,CACZ,GAAIc,EAAeJ,EAAc,CAAEoB,QAAS,iBAAkB,CAAC,CAC/D,GAAIf,EAAe,CAAEe,QAAS,oBAAqB,EAAI,CAAEA,QAAS,iBAAkB,CAAC,CACrF,GAAId,EAAe,CAAEc,QAAS,gBAAiB,EAAI,aAAEpB,CAAY,CAAC,CACtE,EACA,CAACI,EAAcE,EAAa,EAK1Be,EAAiBd,EAAevc,MAAM,CAAG,GAAKuc,CAAc,CAAC,EAAE,CAACQ,IAAI,CAACvX,IAAI,CAAG,IAI5EmN,EAAe,IACjBsI,EAAW1L,GACXlN,EAAMkN,EACV,EAIM+N,EAAa,UACf,IAAItY,GAEAA,EADAkE,EACM,MADE,EACI3I,CAAUA,CAACgd,MAAM,CAAC,SAAgB,OAAPrU,IAEjC,MAAM3I,EAAAA,CAAUA,CAACgd,MAAM,CAAC,SAAiB,OAARvC,MAGhChW,EAAInF,GAAG,EAAE,CAChBsb,EAAY,MACZxI,EAAa,MAErB,EAIM6K,EAAeC,IACjBtC,EAAYsC,EAChB,EAqBA,MACI,iCACI,UAACpY,MAAAA,CACGQ,UAAU,yDACVO,MAAO,CAAEJ,MAAO,OAAQC,OAAQ,OAAQ,WAExC,WAACZ,MAAAA,CAAK,GAAG6W,EAAa,OAAE9V,CAAM,EAAE,WAC5B,UAACwP,QAAAA,CAAO,GAAGuG,GAAe,GAC1B,UAACuB,EAAAA,CAAeA,CAAAA,CAACC,KAAMC,EAAAA,GAAgBA,CAAEpY,KAAK,KAAKW,MAAM,SACzD,UAACuP,IAAAA,CAAEtP,MAAO,CAAED,MAAO,UAAW0X,aAAc,KAAM,WAC7C5a,EAAE,mDAEP,WAAC6a,QAAAA,CAAM1X,MAAO,CAAED,MAAO,SAAU,YAC7B,UAACQ,IAAAA,UAAG1D,EAAE,WAAa,IAAEA,EAAE,mCAE1Boa,GACG,WAACS,QAAAA,CAAMjY,UAAU,6BACb,UAAC6X,EAAAA,CAAeA,CAAAA,CAACC,KAAMI,EAAAA,GAAmBA,CAAEvY,KAAK,KAAKW,MAAM,QAAQ,OAC7DlD,EAAE,0CAGhBqZ,GACG,WAACwB,QAAAA,CAAMjY,UAAU,cAAcO,MAAO,CAAED,MAAO,KAAM,YACjD,UAACuX,EAAAA,CAAeA,CAAAA,CAACC,KAAMI,EAAAA,GAAmBA,CAAEvY,KAAK,KAAKW,MAAM,QAC3DlD,EAAE,mCAKlBiY,GACG,+BACI,UAAC7V,MAAAA,CAAIe,MAhKQ,CACzBoV,QAAS,OACTC,cAAe,MACfE,eAAgB,aAChBqC,SAAU,OACVC,UAAW,EACf,WA2JoB,WAAC5Y,MAAAA,CAAIe,MA/KN,CACfoV,QAAS,cACTtV,aAAc,EACdgY,OAAQ,iBACRL,aAAc,EACdM,YAAa,GACbnY,MAAO,IACPC,OAAQ,IACRmY,QAAS,EACTC,SAAU,WACVC,UAAW,mCACXC,UAAW,YACf,YAoKwB,UAAClZ,MAAAA,CAAIe,MA1JL,CA0JYoY,QAzJvB,MACb,WAyJ4B,UAAC3b,MAAAA,CAAI4b,IAAKvD,EAAU9U,MA3IpC,CACRoV,QAAS,QACTvV,OAAQ,MACZ,MA0IwB,UAACyX,EAAAA,CAAeA,CAAAA,CAACC,KAAMe,EAAAA,GAAaA,CAAEtY,MAzJ5C,CACdiY,SAAU,WACVM,SAAU,OACVC,IAAK,QACLC,MAAO,QACPC,OAAQ,IACRC,OAAQ,UACRjD,gBAAiB,OACjB3V,MAAO,OACPD,aAAc,KAClB,EA+I2EC,MAAM,QAAQoB,QAAS+V,WAM1F,UAACpb,EAAAA,OAAsBA,CAAAA,CACnBD,OAAQ2Y,EACRvY,MAAO,GAAasQ,EAAapD,GACjCnN,MAAO0Y,GAASA,CAAK,CAAC,EAAE,CAAGA,CAAK,CAAC,EAAE,CAACkC,OAAO,CAAG,GAC9C7a,aAAc,GAAc8Z,EAAWhC,GACvC3X,SAAUwY,GAASA,CAAK,CAAC,EAAE,CAAGA,CAAK,CAAC,EAAE,CAACnY,IAAI,CAAG,GAC9CJ,QAAS,GAAcib,EAAYC,OAInD,gMCvNA,IAAIuB,EAAmB,EAAE,CAEnB1D,EAAiB,CACrBC,KAAM,EACNC,QAAS,OACTC,cAAe,SACfC,WAAY,SACZC,eAAgB,SAChB3V,MAAO,OACPC,OAAQ,OACR2V,YAAa,GACbC,YAAa,UACbC,gBAAiB,UACjB3V,MAAO,QACP4V,WAAY,2BACZqC,QAAS,MACX,EAYMa,EAAuB,CAC3BzD,QAAS,OACT4C,QAAS,OACTpY,MAAO,OACPkY,OAAQ,iBACRzC,cAAe,SACfE,eAAgB,aAChBqC,SAAU,OACVC,UAAW,EACb,EAcMpb,EAAM,CACVmD,MAAO,OACT,EAEMgW,EAAmB,CACvBH,YAAa,SACf,EA4WA,EA1WsB,IACpB,IAmSIqD,EAnSE,GAAEjc,CAAC,CAAE,CAAGC,CAAAA,EAAAA,EAAAA,EAAAA,CAAcA,CAAC,GAyWhBkT,OAxWP,CAAC+I,EAAWC,EAAa,CAAG1c,CAAAA,CAwWP,CAxWOA,EAAAA,QAAAA,CAAQA,EAAC,GACrC,CAAC2c,EAAYC,EAAc,CAAG5c,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,GACtCyJ,EACJ8K,iBAAM7S,IAAI,CAAoB,UAAWiX,UAAwB,CAC7D,CAACP,EAAOC,EAAS,CAAGrY,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAQ,EAAE,EACtC,CAAC6c,EAAOC,EAAS,CAAG9c,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,EAAC,GAC7B,CAAC+c,EAAaC,EAAe,CAAGhd,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAW,EAAE,EAErD0Y,EAAWnE,GAAwB,gBAAfA,EAAM7S,IAAI,CAAqB,SAAW,SAC9Dub,EAAc,MAAOpQ,IACZ,MAAMhP,EAAAA,CAAUA,CAACgd,MAAM,CAAC,GAAehO,MAAAA,CAAZ6L,EAAS,KAAM,OAAH7L,GACtD,EAEM+N,EAAa,IACjBgC,EAAcvC,GACdqC,GAAa,EACf,EAEMQ,EAAe,CAAC9P,EAA8DsC,KAClF,IAAMyN,EAAQ,IAAIJ,EAAY,CAC9BI,CAAK,CAACzN,EAAM,CAAGtC,EAAE1I,MAAM,CAACrH,KAAK,CAC7B2f,EAAeG,EACjB,EAEMC,EAAe,IAEnB,OAAQC,GADiBhD,EAAKpa,IAAI,CAACe,KAAK,CAAC,KAAKsc,GAAG,IAE/C,IAAK,MACL,IAAK,MACL,IAAK,OACL,IAAK,MACL,IAAK,MACH,MAAO,UAACnd,MAAAA,CAAI4b,IAAK1B,EAAKC,OAAO,CAAE5W,MAAOvD,GACxC,KAAK,MACH,MACE,UAACA,MAAAA,CACC4b,IAAI,gCACJ5Y,UACiB,gBAAfoR,EAAM7S,IAAI,CAAqB,aAAe,cAItD,KAAK,OAmBL,QAlBE,MACE,UAACvB,MAAAA,CACC4b,IAAI,iCACJ5Y,UACiB,gBAAfoR,EAAM7S,IAAI,CAAqB,aAAe,cAItD,KAAK,MACL,IAAK,OACH,MACE,UAACvB,MAAAA,CACC4b,IAAI,gCACJ5Y,UACiB,gBAAfoR,EAAM7S,IAAI,CAAqB,aAAe,cAaxD,CACF,EAEM6b,EAAY,IAAMb,GAAa,GAE/Bc,EAAgB,KACpBd,GAAa,EACf,EAEMe,EAAgB,IAEpB,IAAMvQ,EADNwQ,GAAef,CAAAA,GAEGe,EAAavgB,GAAG,CAC5B,CAAE0S,SAAU6N,EAAavgB,GAAI,EAC7B,CAAEkd,KAAMqD,CAAa,EACrBC,EAASjR,IAAAA,SAAW,CAAC4P,EAAMpP,GAE3B0Q,EAAY,IAAIb,EAAY,CAClCa,EAAU1P,MAAM,CAACyP,EAAQ,GACzBX,EAAeY,GAEfX,EAAYX,CAAI,CAACqB,EAAO,CAAC9N,QAAQ,EACjCyM,EAAKpO,MAAM,CAACyP,EAAQ,GACpBpJ,EAAMV,QAAQ,CAACyI,EAAM/H,EAAM7E,KAAK,CAAG6E,EAAM7E,KAAK,CAAG,GACjD,IAAMmO,EAAW,IAAIzF,EAAM,CAC3ByF,EAAS3P,MAAM,CAAC2P,EAASC,OAAO,CAACJ,GAAe,GAChDrF,EAASwF,GACTnB,GAAa,EACf,EAEMqB,EAAc3F,EAAMhZ,GAAG,CAAC,CAACib,EAAWpM,IAEtC,WAACtL,MAAAA,WACC,UAACkB,EAAAA,CAAGA,CAAAA,CAACsP,GAAI,YACP,WAACxQ,MAAAA,CAAIQ,UAAU,gBACb,UAACU,EAAAA,CAAGA,CAAAA,CACFE,GAAI,EACJC,GAAI,EACJb,UACiB,8CAAfoR,EAAM7S,IAAI,CACN,gDACA,oDAGL0b,EAAa/C,KAEhB,UAACxW,EAAAA,CAAGA,CAAAA,CAACE,GAAI,EAAGC,GAAI,EAAGb,UAAU,6BAC3B,WAACuN,EAAAA,CAAIA,CAAAA,WACH,WAACA,EAAAA,CAAIA,CAACC,KAAK,EAACqN,UAAU,qBACpB,UAACtN,EAAAA,CAAIA,CAACE,KAAK,EAACzN,UAAU,gBAAQ5C,EAAE,cAChC,UAACmQ,EAAAA,CAAIA,CAACkH,OAAO,EACX9U,KAAK,KACLpB,KAAK,OACLuc,QAAQ,IACR5gB,MAAOgd,EAAK6D,aAAa,CAAG7D,EAAK6D,aAAa,CAAG7D,EAAKpa,IAAI,MAG9D,WAACyQ,EAAAA,CAAIA,CAACC,KAAK,EAACqN,UAAU,wBACpB,UAACtN,EAAAA,CAAIA,CAACE,KAAK,WACR2D,kBAAM7S,IAAI,CACPnB,EAAE,uCACFA,EAAE,wBAER,UAACmQ,EAAAA,CAAIA,CAACkH,OAAO,EACXuG,UAA0B,gBAAf5J,EAAM7S,IAAI,CAAqB,SAAMqW,EAChDjV,KAAK,KACLpB,KAAK,OACL0c,YACiB,gBAAf7J,EAAM7S,IAAI,CACNnB,EAAE,kCACFA,EAAE,sCAERlD,MAAO0f,CAAW,CAAC9O,EAAE,CACrB8C,SAAU,GAAOmM,EAAa9P,EAAGa,aAKzC,UAACpK,EAAAA,CAAGA,CAAAA,CACFE,GAAI,EACJC,GAAI,EACJb,UAAU,gCACV0B,QAAS,IAAM+V,EAAWP,YAE1B,UAACzV,EAAAA,CAAMA,CAAAA,CAACL,QAAQ,gBAAQhE,EAAE,mBAIhC,WAACqC,EAAAA,CAAKA,CAAAA,CAACC,KAAM4Z,EAAWzZ,OAAQua,YAC9B,UAAC3a,EAAAA,CAAKA,CAACyb,MAAM,EAACC,WAAW,aACvB,UAAC1b,EAAAA,CAAKA,CAAC6N,KAAK,WAAElQ,EAAE,kBAElB,UAACqC,EAAAA,CAAKA,CAACM,IAAI,WAAE3C,EAAE,qCACf,WAACqC,EAAAA,CAAKA,CAAC+B,MAAM,YACX,UAACC,EAAAA,CAAMA,CAAAA,CAACL,QAAQ,YAAYM,QAAS2Y,WAClCjd,EAAE,YAEL,UAACqE,EAAAA,CAAMA,CAAAA,CAACL,QAAQ,UAAUM,QAAS,IAAM4Y,EAAcpD,YACpD9Z,EAAE,iBAlED0N,IA0EdxN,CAAAA,EAAAA,EAAAA,SAAAA,CAASA,CAAC,KACR2X,EAAMpb,OAAO,CAAC,GAAUgF,IAAIuc,eAAe,CAAClE,EAAKC,OAAO,GACxDgC,EAAO,EAAE,EACR,EAAE,EAEL7b,CAAAA,EAAAA,EAAAA,SAAAA,CAASA,CAAC,KACR8T,EAAMT,cAAc,CAACiJ,EACvB,EAAG,CAACA,EAAY,EAEhBtc,CAAAA,EAAAA,EAAAA,SAAAA,CAASA,CAAC,KACRuc,EAAezI,EAAMX,OAAO,CAC9B,EAAG,CAACW,EAAMX,OAAO,CAAC,EAElBnT,CAAAA,EAAAA,EAAAA,SAAAA,CAASA,CAAC,KACR8T,GAAgC,SAAvBA,EAAMiK,YAAY,EAAe1B,GAAS,GAC/CvI,GAASA,EAAMZ,KAAK,EAAE,EAaf,IAZMY,EAAMZ,KAAK,CAACvU,GAAG,CAAC,CAAC8K,EAAWC,KACzCmS,EAAK/e,IAAI,CAAC,CACRsS,SAAU3F,EAAK/M,GAAG,CAClBuS,MAAO6E,EAAM7E,KAAK,CAAG6E,EAAM7E,KAAK,CAAG,EACnChO,KAAMwI,EAAKjK,IAAI,CAACe,KAAK,CAAC,IAAI,CAAC,EAAE,GAEV,CACnB,GAAGkJ,CAAI,CACPoQ,QAAS,GAAwCpQ,MAAAA,CAArCyO,8BAAsB,CAAC,gBAAuB,OAATzO,EAAK/M,GAAG,CAC3D,IAGkB,CAExB,EAAG,CAACoX,EAAMZ,KAAK,CAAC,EAEhB,IAAM8K,EAAc,MAAOC,EAAqBhP,KAC9C,GAAIgP,EAAaphB,MAAM,CAAGoS,EACxB,GAAI,CACF,CAF6B,GAEvBiP,EAAY,IAAIvc,SACtBuc,EAAKtc,MAAM,CAAC,OAAQqc,CAAY,CAAChP,EAAM,EACvC,IAAMpN,EAAM,MAAMzE,EAAAA,CAAUA,CAAC0E,IAAI,CAACmW,EAAUiG,EAAM,CAChD,eAAgB,qBAClB,GACArC,EAAK/e,IAAI,CAAC,CACRsS,SAAUvN,EAAInF,GAAG,CACjBkd,KAAMqE,CAAY,CAAChP,EAAM,CACzBA,MAAO6E,EAAM7E,KAAK,CAAG6E,EAAM7E,KAAK,CAAG,EACnChO,KAAMgd,CAAY,CAAChP,EAAM,CAACzP,IAAI,CAACe,KAAK,CAAC,IAAI,CAAC,EAAE,GAE9Cyd,EAAYC,EAAchP,EAAQ,EACpC,CAAE,MAAOP,EAAO,CACdsP,EAAYC,EAAchP,EAAQ,EACpC,MAEA6E,EAAMV,QAAQ,CAACyI,EAAM/H,EAAM7E,KAAK,CAAG6E,EAAM7E,KAAK,CAAG,EAErD,EAEM0K,EAASwE,CAAAA,EAAAA,EAAAA,WAAAA,CAAWA,CAAC,MAAOC,IAChC,MAAMJ,EAAYI,EAAc,GAChC,IAAMC,EAAWD,EAAazf,GAAG,CAAC,GAChC8V,OAAOC,MAAM,CAACkF,EAAM,CAClBC,QAAStY,IAAIuY,eAAe,CAACF,EAC/B,IAEFwC,EACIxE,EAAS,GAAe,IAAIvN,KAAcgU,EAAS,EACnDzG,EAAS,IAAIyG,EAAS,CAC5B,EAAG,EAAE,EAkBC,cACJtF,CAAY,eACZC,CAAa,CACbC,cAAY,cACZC,CAAY,cACZC,CAAY,gBACZC,CAAc,CACf,CAAGC,CAAAA,EAAAA,EAAAA,EAAAA,CAAWA,CAAC,CACdE,OACEzF,GAASA,EAAM7S,IAAI,CACf,+MACA,UACNuY,SAAU4C,EACV3C,QAAS,EACTC,QAAS1Q,SACT2Q,EACApJ,UAhCF,CAgCawJ,QAhCJA,CAA8B,EACrC,GAAiB,UAAU,CAAvB9B,GACF,GAAkC,SAAS,CAAvC2B,EAAK3Y,IAAI,CAAC+Y,SAAS,CAAC,EAAG,GAIzB,OADAhY,EAAAA,EAAKA,CAAC0M,KAAK,CAAC5O,EAAE,6BACP,CAAEwe,KAAM,oBAAqBC,QAAS,yBAA0B,CACzE,MACK,GAAiB,UAAU,CAAvBtG,GAC2B,OAAM,GAAI,EAAnChX,IAAI,CAAC+Y,SAAS,CAAC,EAAG,GAE3B,OADAhY,EAAAA,EAAKA,CAAC0M,KAAK,CAAC5O,EAAE,6BACP,CAAEwe,KAAM,oBAAqBC,QAAS,yBAA0B,EAG3E,OAAO,IACT,CAkBA,GAEMtb,EAAQmR,CAAAA,EAAAA,EAAAA,OAAAA,CAAOA,CACnB,IAAO,EACL,GAAG+D,CAAS,CACZ,GAAIc,EAAeJ,EAAc,CAAEoB,QAAS,iBAAkB,CAAC,CAC/D,GAAIf,EACA,CAAEe,QAAS,oBAAqB,EAChC,CAAEA,QAAS,iBAAkB,CAAC,CAClC,GAAId,EAAe,CAAEc,QAAS,gBAAiB,EAAI,CAAEpB,aAAY,CAAC,CACpE,EACA,CAACI,EAAcE,EAAa,EAK5B4C,EADEjI,GAAwB,eAAe,CAA9BA,EAAM7S,IAAI,CAEnB,UAAC0Z,QAAAA,CAAM1X,MAAO,CAAED,MAAO,SAAU,WAAIlD,EAAE,uBAIvC,UAAC6a,QAAAA,CAAM1X,MAAO,CAAED,MAAO,SAAU,WAAIlD,EAAE,oBAI3C,IAAMoa,EACJd,EAAevc,MAAM,CAAG,GAAKuc,CAAc,CAAC,EAAE,CAACQ,IAAI,CAACvX,IAAI,CAAG2G,EAC7D,MACE,iCACE,UAAC9G,MAAAA,CACCQ,UAAU,yDACVO,MAAO,CAAEJ,MAAO,OAAQC,OAAQ,OAAQ,WAExC,WAACZ,MAAAA,CAAK,GAAG6W,EAAa,OAAE9V,CAAM,EAAE,WAC9B,UAACwP,QAAAA,CAAO,GAAGuG,GAAe,GAC1B,UAACuB,EAAAA,CAAeA,CAAAA,CAACC,KAAMC,EAAAA,GAAgBA,CAAEpY,KAAK,KAAKW,MAAM,SACzD,UAACuP,IAAAA,CAAEtP,MAAO,CAAED,MAAO,UAAW0X,aAAc,KAAM,WAC/C5a,EAAE,mDAGJ,CAACsc,GACA,WAACzB,QAAAA,CAAM1X,MAAO,CAAED,MAAO,SAAU,YAC/B,UAACQ,IAAAA,UAAE,UAAS,wCAGfuY,GACAjI,EAAM7S,IAAI,CACPiZ,GACE,CAFU,EAEV,QAACS,QAAAA,CAAMjY,UAAU,6BACf,UAAC6X,EAAAA,CAAeA,CAAAA,CACdC,KAAMI,EAAAA,GAAmBA,CACzBvY,KAAK,KACLW,MAAM,QACL,IACFlD,EAAE,4CAaVqZ,CAVGe,EAWF,WAACS,QAAAA,CAAMjY,UAAU,cAAcO,MAAO,CAAED,MAAO,SAAU,YACvD,UAACuX,EAAAA,CAAeA,CAAAA,CACdC,KAAMI,EAAAA,GAAmBA,CACzBvY,KAAK,KACLW,MAAM,QACL,IACFlD,EAAE,mCAKV6X,EAAM9a,MAAM,CAAG,GAAK,UAACqF,MAAAA,CAAIe,MAAO6Y,WAAkBwB,MAGzD", "sources": ["webpack://_N_E/./services/invitation.service.ts", "webpack://_N_E/./pages/institution/InstitutionImageEditor.tsx", "webpack://_N_E/./pages/institution/Form.tsx", "webpack://_N_E/./node_modules/react-bootstrap/esm/InputGroupText.js", "webpack://_N_E/./node_modules/react-bootstrap/esm/InputGroup.js", "webpack://_N_E/./components/common/ValidationFormWrapper.tsx", "webpack://_N_E/./components/common/FormikTextInput.tsx", "webpack://_N_E/./pages/institution/InstitutionImageHandler.tsx", "webpack://_N_E/./components/common/ReactDropZone.tsx"], "sourcesContent": ["//Import services/components\r\nimport apiService from \"./apiService\";\r\n\r\nclass InvitationService {\r\n    arrayDifference(array1: any[], array2: any[]) {\r\n        // returns array\r\n        // element present in array one but not in array 2\r\n        const difference: any[] = [];\r\n        array1.forEach((ar1El: any) => {\r\n            if (array2.filter((ar2El: any) => ar2El._id === ar1El.value).length === 0) {\r\n                difference.push(ar1El.value);\r\n            }\r\n        });\r\n        return difference;\r\n    }\r\n\r\n    async deleteInvitations(userIds: any[], instituteId: any) {\r\n        userIds.forEach(async (userId: any) => {\r\n            let userData = await apiService.get(`/users/${userId}`);\r\n            if (userData.institutionInvites.length) {\r\n                userData.institutionInvites = userData.institutionInvites.filter(\r\n                    (invite: any) => invite.institutionId !== instituteId\r\n                );\r\n                await apiService.patch(`/users/${userId}`, userData);\r\n            }\r\n        });\r\n    }\r\n\r\n    private isDuplicateInvite(userInvites: any[], institutionId: any) {\r\n        let dups = userInvites.filter((ui: any) => {\r\n            return ui.institutionId === institutionId;\r\n        });\r\n        return dups.length ? true : false;\r\n    }\r\n\r\n    private getNewInviteMeta(institutionId: any, institutionTitle: any) {\r\n        return {\r\n            institutionName: institutionTitle,\r\n            institutionId: institutionId,\r\n            status: \"Request Pending\",\r\n        };\r\n    }\r\n\r\n    private async getUserData(userId: any, userEmail?: any, userName?: any) {\r\n        let userData: any = {};\r\n        if (userEmail && userName) {\r\n            userData = await apiService.get(`/users`, {\r\n                query: {\r\n                    email: userEmail,\r\n                    username: userName,\r\n                },\r\n            });\r\n        } else {\r\n            userData = await apiService.get(`/users/${userId}`);\r\n        }\r\n        return userData;\r\n    }\r\n\r\n    async sendInvitations(users: any[], institutionId: any, institutionTitle: any) {\r\n        users.forEach(async (user: any) => {\r\n            const userId = user._id;\r\n            if (userId) {\r\n                let userData = await this.getUserData(userId);\r\n                if (userData) {\r\n                    if (!userData.institutionInvites) userData.institutionInvites = [];\r\n                    userData.institutionInvites = userData.institutionInvites.map((invite: any) => {\r\n                        if (invite.institutionId === institutionId && invite.status === \"Rejected\") {\r\n                            invite.status = \"Request Pending\";\r\n                        }\r\n                        return invite;\r\n                    });\r\n                    if (!this.isDuplicateInvite(userData.institutionInvites || [], institutionId)) {\r\n                        userData.institutionInvites.push(this.getNewInviteMeta(institutionId, institutionTitle));\r\n                    }\r\n                    await apiService.patch(`/users/${userId}`, userData);\r\n                }\r\n            } else {\r\n                //means new user\r\n                this.inviteNewUserWithEmail(user.email, user.username, institutionId, institutionTitle);\r\n            }\r\n        });\r\n    }\r\n\r\n    public async inviteNewUserWithEmail(email: any, username: any, institutionId: any, institutionTitle: any) {\r\n        const query = { email, username };\r\n        let userData = await apiService.get(`/users`, { query });\r\n        if (userData && userData.data[0]) {\r\n            userData = userData.data[0];\r\n            userData.institutionInvites.push(this.getNewInviteMeta(institutionId, institutionTitle));\r\n            const res = await apiService.patch(`/users/${userData._id}`, userData);\r\n        }\r\n    }\r\n}\r\n\r\nexport default new InvitationService();\r\n", "//Import Library\r\nimport React, { useState, useEffect, useRef } from \"react\";\r\nimport AvatarEditor from \"react-avatar-editor\";\r\nimport RangeSlider from \"react-bootstrap-range-slider\";\r\nimport { Modal, Button, Row, Col } from \"react-bootstrap\";\r\n\r\nimport toast from 'react-hot-toast';\r\n\r\n//Import services/components\r\nimport apiService from \"../../services/apiService\";\r\nimport { useTranslation } from 'next-i18next';\r\n\r\nconst InstitutionImageEditor = ({\r\n  isOpen,\r\n  onModalClose,\r\n  image,\r\n  getId,\r\n  fileName,\r\n  getBlob,\r\n}: any) => {\r\n  const [scale, setScale] = useState<number>(1);\r\n  const [name, setName] = useState<string>(\"\");\r\n  const [img, setImg] = useState<any>(null);\r\n  const editorRef = useRef<any>(null);\r\n    const { t } = useTranslation('common');\r\n\r\n  useEffect(() => {\r\n    setName(fileName);\r\n  }, [fileName]);\r\n  const newLocal = \"Something wrong in server || your data!\";\r\n  const cropHandler = async () => {\r\n    /*****Helper Function to convert to blob******/\r\n    const dataURLtoBlob = (dataurl: string) => {\r\n      const arr = dataurl.split(\",\");\r\n      const  mime = arr[0].match(/:(.*?);/)?.[1];\r\n      const  bstr = atob(arr[1]);\r\n      let n = bstr.length;\r\n      const  u8arr = new Uint8Array(n);\r\n      while (n--) {\r\n        u8arr[n] = bstr.charCodeAt(n);\r\n      }\r\n      return new Blob([u8arr], { type: mime });\r\n    };\r\n    /*****End ********/\r\n    const canvas = editorRef.current.getImage().toDataURL(\"image/jpeg\", 0.6);\r\n    const blob = dataURLtoBlob(canvas);\r\n    const urlCreator = window.URL || window.webkitURL; //For Creating the url for preview\r\n    const blobUrl = urlCreator.createObjectURL(blob);\r\n    getBlob(blobUrl);\r\n\r\n    const fd = new FormData();\r\n    fd.append(\"file\", blob, name);\r\n\r\n    try {\r\n      const res = await apiService.post(\"/image\", fd, {\r\n        \"Content-Type\": \"multipart/form-data\",\r\n      });\r\n\r\n      if (res && res._id) {\r\n        getId(res._id);\r\n      }\r\n    } catch {\r\n      throw newLocal;\r\n    }\r\n    toast.success(t(\"toast.CroppedtheimageSuccessfully\"));\r\n    onModalClose(false);\r\n    setImg(null);\r\n    setName(\"none\");\r\n    setScale(1);\r\n  };\r\n\r\n  return (\r\n    <>\r\n      <div>\r\n        <Modal\r\n          show={isOpen}\r\n          size=\"lg\"\r\n          aria-labelledby=\"ProfileEdit\"\r\n          onHide={() => onModalClose(false)}\r\n          centered\r\n        >\r\n          <Modal.Body>\r\n            <div className=\"d-flex flex-column justify-content-center align-items-center imgRotate\">\r\n              <AvatarEditor\r\n                ref={editorRef}\r\n                width={700}\r\n                height={400}\r\n                borderRadius={2}\r\n                scale={scale}\r\n                color={[0, 0, 0, 0.6]}\r\n                image={img ? img : image}\r\n                style={{width: \"100%\",height: \"auto\"}}\r\n              />\r\n              <div className=\"info-identifier\">\r\n                <span>{t(\"ThisareawillcontainyourInstitutionandfocalpointinformation\")}</span>\r\n              </div>\r\n            </div>\r\n\r\n            <div className=\"mx-2 my-3\">\r\n              <Row>\r\n                <Col sm={1} md={1} lg={1} className=\"pe-0\">\r\n                  <b>{t(\"Zoom\")}</b>\r\n                </Col>\r\n                <Col sm={11} md={11} lg={11}>\r\n                  <RangeSlider\r\n                    value={scale}\r\n                    tooltip=\"auto\"\r\n                    min={1}\r\n                    max={10}\r\n                    step={0.01}\r\n                    variant=\"primary\"\r\n                    onChange={(changeEvent) =>\r\n                      setScale(Number(changeEvent.target.value))\r\n                    }\r\n                  />\r\n                </Col>\r\n              </Row>\r\n            </div>\r\n          </Modal.Body>\r\n          <Modal.Footer>\r\n            <Button onClick={cropHandler}>{t(\"Crop\")}</Button>\r\n            <Button variant=\"danger\" onClick={() => onModalClose(false)}>\r\n              {t(\"Cancel\")}\r\n            </Button>\r\n          </Modal.Footer>\r\n        </Modal>\r\n      </div>\r\n    </>\r\n  );\r\n};\r\n\r\nexport default InstitutionImageEditor;\r\n", "//Import Library\r\nimport React, { useState, useRef, useEffect } from \"react\";\r\nimport Router, { useRouter } from \"next/router\";\r\nimport _ from \"lodash\";\r\nimport Link from \"next/link\";\r\nimport { MultiSelect } from \"react-multi-select-component\";\r\nimport { <PERSON><PERSON>, Card, Form, Container, Row, Col, Tabs, Tab, InputGroup } from \"react-bootstrap\";\r\nimport { TextInput, SelectGroup } from \"../../components/common/FormikTextInput\";\r\nimport ValidationFormWrapper from \"../../components/common/ValidationFormWrapper\";\r\n\r\nimport toast from 'react-hot-toast';\r\n\r\n//Import services/components\r\nimport ReactDropZone from \"../../components/common/ReactDropZone\";\r\nimport apiService from \"../../services/apiService\";\r\nimport invitationService from \"../../services/invitation.service\";\r\nimport { useTranslation } from 'next-i18next';\r\nimport InstitutionImageHandler from \"./InstitutionImageHandler\";\r\nimport { EditorComponent } from \"../../shared/quill-editor/quill-editor.component\"\r\n\r\ninterface InstitutionFormProps {\r\n  routes: string[];\r\n  institution: any;\r\n}\r\n\r\nconst InstitutionForm = ({ routes, institution }: InstitutionFormProps) => {\r\n    const buttonRef = useRef(null);\r\n    const formRef = useRef(null);\r\n    const { t, i18n } = useTranslation(\"common\");\r\n    const _initialState: any = {\r\n        organisationName: \"\",\r\n        title: \"\",\r\n        country: \"\",\r\n        world_region: \"\",\r\n        website: \"\",\r\n        organisationType: null,\r\n        telephone: \"\",\r\n        dial_code: t(\"DialCode\"),\r\n        contact_name: \"\",\r\n        twitter: \"\",\r\n        addressL1: \"\",\r\n        addressL2: \"\",\r\n        city: \"\",\r\n        region: [],\r\n        users: [],\r\n        institution: \"\",\r\n        expertise: [],\r\n        network: [],\r\n        hazards: [],\r\n        description: \"\",\r\n        images: [],\r\n        header: null,\r\n        department: \"\",\r\n        unit: \"\",\r\n        use_default_header: true,\r\n        images_src: [],\r\n    };\r\n    const { query } = useRouter();\r\n    const currentLang = i18n.language;\r\n    const titleSearch = i18n.language === \"de\" ? { title_de: \"asc\" } : { title: \"asc\" };\r\n\r\n    const [countryList, setcountryList]: any = useState([]);\r\n    const [dropZoneCollection, setDropZoneCollection] = useState<any[]>([]);\r\n    const [srcCollection, setSrcCollection] = useState<any[]>([]);\r\n    const [headerDropCollection, setHeaderDropCollection] = useState<any>(null);\r\n    const [hazards, setHazards] = useState<any[]>([]);\r\n    const [expertise, setExpertise] = useState<any[]>([]);\r\n    const [network, setNetwork] = useState<any[]>([]);\r\n    const [regions, setRegion] = useState<any[]>([]);\r\n    const [usersList, setUsersList] = useState<any[]>([]);\r\n    const [defaultActiveKey, setdefaultActiveKey] = useState<number>(1);\r\n    const [, setHazardInfiniteList] = useState<any[]>([]);\r\n    const [tab, setTab] = useState<any[]>([\r\n        {\r\n            username: \"\",\r\n            email: \"\",\r\n            institution: \"\",\r\n            focal_dial_code: t(\"DialCode\"),\r\n            mobile_number: \"\",\r\n            _id: null,\r\n        },\r\n    ]);\r\n    const [organizationList, setOrganizationList] = useState<any[]>([]);\r\n    const [expertiseList, setexpertiseList] = useState<any[]>([]);\r\n    const [networkList, setnetworkList] = useState<any[]>([]);\r\n    const [hazardList, sethazardList] = useState<any[]>([]);\r\n    const editform: boolean = routes && routes[0] == \"edit\" && !!routes[1];\r\n    const [initialVal, setinitialVal] = useState<any>(_initialState);\r\n    const [existingFpUsers, setExistingFpUsers] = useState<any[]>([]);\r\n    const [partners, setPartners] = useState<any[]>([]);\r\n\r\n    const institutionParams = {\r\n        query: {},\r\n        sort: { title: \"asc\" },\r\n        limit: \"~\",\r\n    };\r\n\r\n    const partnersParams = {\r\n        query: {},\r\n        sort: { title: \"asc\" },\r\n        select: \"-networks -expertise -focal_points -hazard_types -description -twitter -telephone -images -header -partners -type -user -website -hazards -contact_name -updated_at -created_at -__v\",\r\n        limit: \"~\",\r\n        lean: true,\r\n    };\r\n\r\n    /***For modal Show & Hide */\r\n    /***End */\r\n\r\n    const filterOptions = (options: any[], filter: string) => {\r\n        if (!filter) {\r\n            return options;\r\n        }\r\n        const regex = new RegExp(`^${filter}`, \"gi\");\r\n        return options.filter(({ label }: { label: string }) => label && label.match(regex));\r\n    };\r\n    useEffect(() => {\r\n        /* Start Update data for form */\r\n        if (routes[0] == \"edit\" && institution) {\r\n            const response = institution;\r\n            if (response) {\r\n                response_institution_Func(response);\r\n                const currentLang = i18n.language === \"fr\" ? \"en\" : i18n.language;\r\n                const { _expertise, _network, _partners, _hazards } = response_methods_func(response, currentLang);\r\n                getRegion(response.country, institutionParams); // update region based on the country\r\n                setExpertise(_expertise);\r\n\r\n                setNetwork(_network);\r\n                setSelectedPartners(_partners);\r\n                setHazards(_hazards);\r\n                setHazardInfiniteList(_hazards);\r\n\r\n                setDropZoneCollection(response.images ? response.images : []);\r\n                setSrcCollection(response.images_src ? response.images_src : []);\r\n                setHeaderDropCollection(response.header && response.header._id ? response.header._id : null);\r\n                setinitialVal((prevState: any) => ({ ...prevState, ...response }));\r\n            }\r\n        }\r\n\r\n        getHazard();\r\n        getUsers();\r\n        getCountries();\r\n        getOrganization(institutionParams);\r\n        getExpertise(institutionParams);\r\n        getNetwork(institutionParams);\r\n        getPartners(partnersParams);\r\n    }, []);\r\n\r\n    const getCountries = async () => {\r\n        const institutionParamslist = {\r\n            sort: titleSearch,\r\n            limit: \"~\",\r\n            select: \"-code -code3 -coordinates -created_at -first_letter -health_profile -security_advice -updated_at\",\r\n            lean: true,\r\n            languageCode: currentLang,\r\n        };\r\n        const response = await apiService.get(\"/country\", institutionParamslist);\r\n        if (response && Array.isArray(response.data)) {\r\n            setcountryList(response.data);\r\n        }\r\n    };\r\n\r\n    const setApprovedUsers = (users: any) => {\r\n        if (users && users.data && users.data.length > 0) {\r\n            const tempStorage: any = [];\r\n            users.data.forEach((user: any) => {\r\n                let foundInstituteInvites = user.institutionInvites.filter(\r\n                    (invite: any) => invite.institutionId === routes[1] && invite.status === \"Approved\"\r\n                );\r\n                if (foundInstituteInvites.length) {\r\n                    tempStorage.push({\r\n                        label: user.username,\r\n                        value: user._id,\r\n                        email: user.email,\r\n                    });\r\n                }\r\n            });\r\n            setExistingFpUsers(tempStorage);\r\n            setinitialVal((prevState: any) => ({\r\n                ...prevState,\r\n                users: tempStorage,\r\n            }));\r\n        }\r\n    };\r\n\r\n    const isUserReqPendingForInstitute = (_users: any[], currentUser: any) => {\r\n        let result = false;\r\n        _users.forEach((user: any) => {\r\n            user.institutionInvites.forEach((invite: any) => {\r\n                if (\r\n                    invite.institutionId === routes[1] &&\r\n                    invite.status === \"Request Pending\" &&\r\n                    currentUser._id === user._id\r\n                ) {\r\n                    result = true;\r\n                    return;\r\n                }\r\n            });\r\n        });\r\n        return result;\r\n    };\r\n\r\n    const filterUsers = (_users: any[]) => {\r\n        let filteredUsers: any[] = [];\r\n        _users = _users.filter((user: any) => {\r\n            return isUserReqPendingForInstitute(_users, user) ? false : true;\r\n        });\r\n        _users.forEach((user: any) => {\r\n            filteredUsers.push({ label: user.username, value: user._id });\r\n        });\r\n        return filteredUsers;\r\n    };\r\n\r\n    const getUsers = async () => {\r\n        const institutionParamslist = {\r\n            query: {},\r\n            sort: { title: \"asc\" },\r\n            limit: \"~\",\r\n            select: \"-firstname -lastname -email -password -role -country -region -institution -is_focal_point -image -mobile_number -enabled -created_at -updated_at -dataConsentPolicy -restrictedUsePolicy -acceptCookiesPolicy -withdrawConsentPolicy -medicalConsentPolicy -fullDataProtectionConsentPolicy -emailActivateToken\",\r\n        };\r\n        const users = await apiService.get(\"/users\", institutionParamslist);\r\n        if (editform) {\r\n            setApprovedUsers(users);\r\n        }\r\n        if (users && Array.isArray(users.data)) {\r\n            let _users: any[] = [];\r\n            if (editform) {\r\n                let _filteredUsers = filterUsers(users.data);\r\n                users.data.forEach((user: any) => {\r\n                    _users.push({ label: user.username, value: user._id });\r\n                });\r\n                setUsersList(_filteredUsers);\r\n            } else {\r\n                users.data.forEach((user: any) => {\r\n                    _users.push({ label: user.username, value: user._id });\r\n                });\r\n                setUsersList(_users);\r\n            }\r\n        }\r\n    };\r\n\r\n    const getOrganization = async (institutionParamsinit: any) => {\r\n        const response = await apiService.get(\"/InstitutionType\", institutionParamsinit);\r\n        if (response && Array.isArray(response.data)) {\r\n            setOrganizationList(response.data);\r\n        }\r\n    };\r\n\r\n    /***Get partner Organisation***/\r\n    const getPartners = async (partnersParamsinit: any) => {\r\n        const response = await apiService.get(\"/institution\", partnersParamsinit);\r\n        if (response && Array.isArray(response.data)) {\r\n            const partner = response.data\r\n                .filter((item: any) => item._id != query.routes![1])\r\n                .map((item: any, _i: any) => ({ label: item.title, value: item._id }));\r\n            setPartners(partner);\r\n        }\r\n    };\r\n    /***End***/\r\n\r\n    const getExpertise = async (institutionParamsinit: any) => {\r\n        const response = await apiService.get(\"/Expertise\", institutionParamsinit);\r\n        if (response && Array.isArray(response.data)) {\r\n            const _expertise = response.data.map((item: any, _i: any) => {\r\n                return { label: item.title, value: item._id };\r\n            });\r\n            setexpertiseList(_expertise);\r\n        }\r\n    };\r\n\r\n    const getNetwork = async (institutionParamsinit: any) => {\r\n        const response = await apiService.get(\"/InstitutionNetwork\", institutionParamsinit);\r\n        if (response && Array.isArray(response.data)) {\r\n            const _network = response.data.map((item: any, _i: any) => {\r\n                return { label: item.title, value: item._id };\r\n            });\r\n            setnetworkList(_network);\r\n        }\r\n    };\r\n\r\n    const getHazard = async () => {\r\n        const RegionParams = {\r\n            query: {},\r\n            limit: \"~\",\r\n            sort: { title: \"asc\" },\r\n        };\r\n\r\n        const allHazardType: any[] = [];\r\n        const responsetype = await apiService.get(\"/hazardtype\", RegionParams);\r\n        if (responsetype && Array.isArray(responsetype.data)) {\r\n            const finalHazard: any[] = [];\r\n            _.each(responsetype.data, (item: any, _i: any) => {\r\n                allHazardType.push(item._id);\r\n            });\r\n        }\r\n\r\n        const searchParams = {\r\n            query: { hazard_type: allHazardType },\r\n            limit: \"~\",\r\n            sort: { title: \"asc\" },\r\n        };\r\n        const response = await apiService.get(\"/hazard\", searchParams);\r\n        if (response && Array.isArray(response.data)) {\r\n            const _hazard = response.data.map((item: any, _i: any) => {\r\n                return { label: item.title[currentLang], value: item._id };\r\n            });\r\n            sethazardList(_hazard);\r\n        }\r\n    };\r\n\r\n    const getRegion = async (id: any, institutionParamsinit: any) => {\r\n        let _regions: any[] = [];\r\n        if (id) {\r\n            const response = await apiService.get(`/country_region/${id}`, institutionParamsinit);\r\n            if (response && response.data) {\r\n                _regions = response.data.map((item: any, _i: any) => {\r\n                    return { label: item.title, value: item._id };\r\n                });\r\n                _regions.sort((a: any, b: any) => a.label.localeCompare(b.label));\r\n            }\r\n        }\r\n        setRegion(_regions);\r\n    };\r\n\r\n    const clearValue = (obj: any) => {\r\n        setinitialVal((prevState: any) => ({\r\n            ...prevState,\r\n            ...obj,\r\n        }));\r\n    };\r\n\r\n    const handleChange = (e: any) => {\r\n        const { name, value } = e.target;\r\n        setinitialVal((prevState: any) => ({\r\n            ...prevState,\r\n            [name]: value,\r\n        }));\r\n\r\n        if (name == \"country\") {\r\n            const selectedIndex = e.target.selectedIndex;\r\n            if (e.target && selectedIndex && selectedIndex != null) {\r\n                const worldRegion = e.target[selectedIndex].getAttribute(\"data-worldregion\");\r\n                setinitialVal((prevState: any) => ({\r\n                    ...prevState,\r\n                    world_region: worldRegion,\r\n                }));\r\n            }\r\n        }\r\n\r\n        if (name == \"country\") {\r\n            getRegion(value, institutionParams);\r\n            clearValue({ region: [] });\r\n        }\r\n    };\r\n\r\n    const bindCountryRegions = (e: any, name: any) => {\r\n        setinitialVal((prevState: any) => ({\r\n            ...prevState,\r\n            [name]: e,\r\n        }));\r\n    };\r\n\r\n    const handleDescription = (value: any) => {\r\n        setinitialVal((prevState: any) => ({ ...prevState, description: value }));\r\n    };\r\n\r\n    const handleHazard = (e: any) => {\r\n        setHazards(e);\r\n    };\r\n\r\n    const [hazardExpertiseVal] = useState<string>(\"\");\r\n    const handleExpertise = (e: any) => {\r\n        setExpertise(e);\r\n    };\r\n\r\n    const [networkVal] = useState<string>(\"\");\r\n    const [selctedPartners, setSelectedPartners] = useState<any[]>([]);\r\n    const handleNetwork = (e: any) => {\r\n        setNetwork(e);\r\n    };\r\n\r\n    const handlePartners = (e: any) => {\r\n        setSelectedPartners(e);\r\n    };\r\n\r\n    const tabAdd = () => {\r\n        const _temp = [...tab];\r\n        _temp.push({\r\n            username: \"\",\r\n            email: \"\",\r\n            focal_dial_code: t(\"DialCode\"),\r\n            mobile_number: \"\",\r\n            institution: \"\",\r\n            _id: null,\r\n        });\r\n        setTab(_temp);\r\n        setdefaultActiveKey(_temp.length);\r\n    };\r\n    const removeTab = (_e: any, i: any) => {\r\n        tab.splice(i, 1);\r\n        const _temp = [...tab];\r\n        setTab(_temp);\r\n        setdefaultActiveKey(_temp.length);\r\n        if (tab.length === 0) {\r\n            tabAdd();\r\n        }\r\n    };\r\n    const handleTab = (e: any, i: any) => {\r\n        const _tempCosts = [...tab];\r\n        _tempCosts[i][e.target.name] = e.target.value;\r\n        setTab(_tempCosts);\r\n    };\r\n\r\n    const resetHandler = () => {\r\n        setinitialVal(_initialState);\r\n        setDropZoneCollection([]);\r\n        setSrcCollection([]);\r\n        setHeaderDropCollection(null);\r\n        setTab([]);\r\n\r\n        setExpertise([]);\r\n        setNetwork([]);\r\n        setOrganizationList([]);\r\n        sethazardList([]);\r\n        setHazardInfiniteList([]);\r\n        // Reset Expertise with Hazards field\r\n        setHazards([])\r\n        // Reset Partners field\r\n        setSelectedPartners([])\r\n        // Reset validation state (Formik handles this automatically)\r\n        window.scrollTo(0, 0);\r\n    };\r\n\r\n\r\n\r\n    const inviteFpUsers = (users: any, institutionData: any) => {\r\n        if (editform) {\r\n            //delete invitations which are removed from dropdown\r\n            const removedUsers = invitationService.arrayDifference(existingFpUsers, users);\r\n            invitationService.deleteInvitations(removedUsers, routes[1]);\r\n            // send invitations from dropdowns\r\n            invitationService.sendInvitations(users, routes[1], institutionData.title);\r\n        } else {\r\n            users.forEach(async (user: any) => {\r\n                let userId = user._id;\r\n                let userData: any;\r\n                if (userId) {\r\n                    userData = await apiService.get(`/users/${userId}`);\r\n                } else if (user.email && user.username) {\r\n                    userData = await apiService.get(`/users`, {\r\n                        query: {\r\n                            email: user.email,\r\n                            username: user.username,\r\n                        },\r\n                    });\r\n                    userData = userData?.data?.length ? userData.data[0] : {};\r\n                    userId = userData._id;\r\n                }\r\n                if (userData && userData._id) {\r\n                    let newInInvites: any[] = [];\r\n                    if (userData?.institutionInvites?.length) {\r\n                        newInInvites = [...userData.institutionInvites];\r\n                        let dupInvite = newInInvites.filter((invite: any) => invite.institutionId === institutionData._id);\r\n                        if (dupInvite.length <= 0) {\r\n                            newInInvites.push({\r\n                                institutionName: institutionData.title,\r\n                                institutionId: institutionData._id,\r\n                                status: \"Request Pending\",\r\n                            });\r\n                        } else {\r\n                            newInInvites = newInInvites.map((invite: any) => {\r\n                                if (invite.institutionId === institutionData._id && invite.status === \"Rejected\") {\r\n                                    invite.status = \"Request Pending\";\r\n                                }\r\n                                return invite;\r\n                            });\r\n                        }\r\n                    } else {\r\n                        newInInvites = [\r\n                            {\r\n                                institutionName: institutionData.title,\r\n                                institutionId: institutionData._id,\r\n                                status: \"Request Pending\",\r\n                            },\r\n                        ];\r\n                    }\r\n                    userData.institutionInvites = newInInvites;\r\n                    const res = await apiService.patch(`/users/${userId}`, userData);\r\n                }\r\n            });\r\n        }\r\n    };\r\n\r\n    const handleSubmit = async (e: any) => {\r\n        e.preventDefault();\r\n        const extuser = initialVal.users.map((item: any, _i: any) => {\r\n            return { _id: item.value };\r\n        });\r\n        for (let i = 0; i < tab.length; i++) itertion_func(tab, i);\r\n        const isuniqueUsername = _.uniqBy(tab, \"username\");\r\n        const isuniqueEmail = _.uniqBy(tab, \"email\");\r\n        if (isuniqueUsername.length != tab.length || isuniqueEmail.length != tab.length) {\r\n            toast.error(t(\"toast.UniqueFocalpointIssue\"));\r\n            return;\r\n        }\r\n\r\n        const obj = {\r\n            title: initialVal.title.trim(),\r\n            contact_name: initialVal.contact_name,\r\n\r\n            networks: network\r\n                ? network.map((item: any, _i: any) => {\r\n                    return item.value;\r\n                })\r\n                : [],\r\n            partners: selctedPartners ? selctedPartners.map((item: any, _i: any) => item.value) : [],\r\n            expertise: expertise\r\n                ? expertise.map((item: any, _i: any) => {\r\n                    return item.value;\r\n                })\r\n                : [],\r\n\r\n            hazards: initialVal.hazards,\r\n\r\n            address: {\r\n                country: initialVal.country,\r\n                world_region: initialVal.world_region,\r\n                region: initialVal.region\r\n                    ? initialVal.region.map((item: any, _i: any) => {\r\n                        return item.value;\r\n                    })\r\n                    : [],\r\n                line_1: initialVal.addressL1,\r\n                line_2: initialVal.addressL2,\r\n                city: initialVal.city,\r\n            },\r\n\r\n            focal_points: [...tab, ...extuser],\r\n            website: initialVal.website,\r\n            telephone: initialVal.telephone,\r\n            dial_code: initialVal.dial_code,\r\n            twitter: initialVal.twitter,\r\n            type: initialVal.organisationType,\r\n            description: initialVal.description,\r\n            images: initialVal.images,\r\n            header: initialVal.header,\r\n            department: initialVal.department,\r\n            unit: initialVal.unit,\r\n            use_default_header: initialVal.header == null ? true : false,\r\n            images_src: initialVal.images_src,\r\n            primary_focal_point: initialVal.primary_focal_point,\r\n        };\r\n\r\n        if (hazards && hazards.length) {\r\n            obj.hazards = hazards.map((_hazard: any) => _hazard.value);\r\n        }\r\n\r\n        let response: any;\r\n        let toastMsg: string;\r\n        if (editform) {\r\n            if (obj?.focal_points?.length > 0) {\r\n                if (!obj.focal_points.find((_fp: any) => _fp._id === initialVal.primary_focal_point)) {\r\n                    obj.primary_focal_point = \"\";\r\n                }\r\n            }\r\n            toastMsg = \"toast.Organisationupdatedsuccessfully\";\r\n            response = await apiService.patch(`/institution/${routes[1]}`, obj);\r\n        } else {\r\n            toastMsg = \"toast.Organisationaddedsuccessfully\";\r\n            response = await apiService.post(\"/institution\", obj);\r\n        }\r\n        if (response && response._id) {\r\n            obj.focal_points?.length ? inviteFpUsers(obj.focal_points, response) : \"\";\r\n            {\r\n                t(\"addOrganisation\");\r\n            }\r\n            toast.success(t(toastMsg))\r\n            Router.push(\"/institution/[...routes]\", `/institution/show/${response._id}`);\r\n        } else {\r\n            if (editform) { toastMsg = \"toast.OrganisationNotupdatedsuccessfully\";}\r\n            else { toastMsg = \"toast.OrganisationNotaddedsuccessfully\"; }\r\n\r\n            if (response == 'toast.usernameoremailusedalready') {\r\n                toastMsg = \"toast.OrganisationNameShouldUnique\";\r\n            }\r\n            toast.error(t(toastMsg));\r\n        }\r\n    };\r\n\r\n\r\n\r\n    const getID = (id: any[], index: any) => {\r\n        let imageData: any = {};\r\n        const filterIndex0 = id.filter((item: any) => item.index == index).map((item: any) => item.serverID);\r\n        switch (index) {\r\n            case 0:\r\n                imageData = { header: String(filterIndex0) };\r\n                break;\r\n            case 1:\r\n                /*****It's for Focal Point ******/\r\n                break;\r\n            case 2:\r\n                imageData = { images: filterIndex0 };\r\n                break;\r\n        }\r\n        setinitialVal((prevState: any) => ({ ...prevState, ...imageData }));\r\n    };\r\n\r\n    const getSource = (imgSrcArr: any[]) => {\r\n        setinitialVal((prevState: any) => ({ ...prevState, images_src: imgSrcArr }));\r\n    };\r\n\r\n    //Get Id, for sending the server\r\n    const getIdHandler = (id: any) => {\r\n        setinitialVal((prevState: any) => ({\r\n            ...prevState,\r\n            header: id,\r\n            use_default_header: id == null ? true : false,\r\n        }));\r\n    };\r\n    return (\r\n        <ValidationFormWrapper onSubmit={handleSubmit} ref={formRef} initialValues={initialVal} enableReinitialize={true}>\r\n            <Container className=\"formCard\" fluid>\r\n                <Card>\r\n                    <Card.Body>\r\n                        <Row>\r\n                            <Col>\r\n                                <Card.Title>\r\n                                    {routes[0] === \"edit\" ? t(\"editOrganisation\") : t(\"addOrganisation\")}\r\n                                </Card.Title>\r\n                            </Col>\r\n                        </Row>\r\n                        {/* <hr /> */}\r\n                        <Row className=\"mb-3\">\r\n                            <Col>\r\n                                <Form.Group>\r\n                                    <Form.Label className=\"required-field\">{t(\"OrganisationName\")}</Form.Label>\r\n                                    <TextInput\r\n                                        name=\"title\"\r\n                                        id=\"title\"\r\n                                        required={true}\r\n                                        value={initialVal.title}\r\n                                        onChange={handleChange}\r\n                                        validator={(value: any) => value.trim() != \"\"}\r\n                                        errorMessage={{\r\n                                            validator: t(\"PleaseAddtheOrganisationName\"),\r\n                                        }}\r\n                                        as=\"input\"\r\n                                        multiline={false}\r\n                                        rows={1}\r\n                                    />\r\n                                </Form.Group>\r\n                            </Col>\r\n                        </Row>\r\n                        <Row className=\"mb-3\">\r\n                            <Col>\r\n                                <Form.Group>\r\n                                    <Form.Label> {t(\"Description\")}</Form.Label>\r\n                                    <EditorComponent initContent={initialVal.description} onChange={(evt: any) => handleDescription(evt)} />\r\n                                </Form.Group>\r\n                            </Col>\r\n                        </Row>\r\n                        <Row className=\"mb-3\">\r\n                            <Col>\r\n                                <Form.Group>\r\n                                    <Form.Label className=\"d-inline switch-right\">\r\n                                        {t(\"HeaderImage\")}\r\n                                        <Form.Check\r\n                                            className=\"ms-2 d-inline institution_switch\"\r\n                                            type=\"switch\"\r\n                                            checked={initialVal.use_default_header}\r\n                                            id=\"custom-switch\"\r\n                                            label={t(\"UseDefaultHeader\")}\r\n                                        />\r\n                                    </Form.Label>\r\n                                    <InstitutionImageHandler\r\n                                        getId={(id: any) => getIdHandler(id)}\r\n                                        header={headerDropCollection}\r\n                                        type={\"image\"}\r\n                                    />\r\n                                </Form.Group>\r\n                            </Col>\r\n                        </Row>\r\n                        <Row className=\"mb-3\">\r\n                            <Col lg={4} sm={6}>\r\n                                <Form.Group>\r\n                                    <Form.Label>{t(\"Website\")}</Form.Label>\r\n                                    <TextInput\r\n                                        name=\"website\"\r\n                                        id=\"website\"\r\n                                        value={initialVal.website}\r\n                                        pattern=\"^(?:http(s)?:\\/\\/)?[\\w.-]+(?:\\.[\\w\\.-]+)+[\\w\\-\\._~:/?#[\\]@!\\$&'\\(\\)\\*\\+,;=.]+$\"\r\n                                        errorMessage={{\r\n                                            pattern: t(\"Pleaseentervalidwebsite\"),\r\n                                        }}\r\n                                        onChange={handleChange}\r\n                                        required={false}\r\n                                        as=\"input\"\r\n                                        multiline={false}\r\n                                        rows={1}\r\n                                    />\r\n                                </Form.Group>\r\n                            </Col>\r\n\r\n                            <Col lg={4} sm={6}>\r\n                                <InputGroup className=\"row ms-0 me-0\">\r\n                                    <Form.Group className=\"institutionDialCode col-5 col-lg-4 ps-0 pe-0\">\r\n                                        <Form.Label>{t(\"Telephone\")}</Form.Label>\r\n                                        <SelectGroup\r\n                                            type=\"numbers\"\r\n                                            name=\"dial_code\"\r\n                                            id=\"dialCode\"\r\n                                            value={initialVal.dial_code}\r\n                                            onChange={handleChange}\r\n                                            required={false}\r\n                                            errorMessage=\"\"\r\n                                        >\r\n                                            <option value={initialVal.dial_code}>{initialVal.dial_code}</option>\r\n                                            {countryList.map((citem: any, _i: any) => {\r\n                                                return (\r\n                                                    <option\r\n                                                        key={_i}\r\n                                                        value={citem.dial_code}\r\n                                                    >{`(${citem.dial_code}) ${citem.title}`}</option>\r\n                                                );\r\n                                            })}\r\n                                        </SelectGroup>\r\n                                    </Form.Group>\r\n\r\n                                    <Form.Group className=\"institutionTelephone col-7 col-lg-8 pe-0\">\r\n                                        <Form.Label>&nbsp;</Form.Label>\r\n                                        <TextInput\r\n                                            name=\"telephone\"\r\n                                            id=\"telephone\"\r\n                                            value={initialVal.telephone}\r\n                                            onChange={handleChange}\r\n                                            required={false}\r\n                                            as=\"input\"\r\n                                            multiline={false}\r\n                                            rows={1}\r\n                                            errorMessage={{}}\r\n                                        />\r\n                                    </Form.Group>\r\n                                </InputGroup>\r\n                            </Col>\r\n\r\n                            <Col lg={4} sm={6}>\r\n                                <Form.Group>\r\n                                    <Form.Label>{t(\"Twitter\")}</Form.Label>\r\n                                    <TextInput\r\n                                        name=\"twitter\"\r\n                                        id=\"twitter\"\r\n                                        value={initialVal.twitter}\r\n                                        onChange={handleChange}\r\n                                        required={false}\r\n                                        as=\"input\"\r\n                                        multiline={false}\r\n                                        rows={1}\r\n                                        errorMessage={{}}\r\n                                        validator={() => true}\r\n                                        pattern=\"\"\r\n                                    />\r\n                                </Form.Group>\r\n                            </Col>\r\n                        </Row>\r\n                        <Card.Text>\r\n                            <b>{t(\"Address\")}</b>\r\n                            {/* <hr /> */}\r\n                        </Card.Text>\r\n                        <Row className=\"mb-3\">\r\n                            <Col lg md={2}>\r\n                                <Form.Group>\r\n                                    <Form.Label>{t(\"Address1\")}</Form.Label>\r\n                                    <TextInput\r\n                                        name=\"addressL1\"\r\n                                        id=\"addressL1\"\r\n                                        value={initialVal.addressL1}\r\n                                        onChange={handleChange}\r\n                                        required={false}\r\n                                        as=\"input\"\r\n                                        multiline={false}\r\n                                        rows={1}\r\n                                        errorMessage={{}}\r\n                                        validator={() => true}\r\n                                        pattern=\"\"\r\n                                    />\r\n                                </Form.Group>\r\n                            </Col>\r\n                            <Col lg md={2}>\r\n                                <Form.Group>\r\n                                    <Form.Label>{t(\"Address2\")}</Form.Label>\r\n                                    <TextInput\r\n                                        name=\"addressL2\"\r\n                                        id=\"addressL2\"\r\n                                        value={initialVal.addressL2}\r\n                                        onChange={handleChange}\r\n                                        required={false}\r\n                                        as=\"input\"\r\n                                        multiline={false}\r\n                                        rows={1}\r\n                                        errorMessage={{}}\r\n                                        validator={() => true}\r\n                                        pattern=\"\"\r\n                                    />\r\n                                </Form.Group>\r\n                            </Col>\r\n                            <Col lg md={2}>\r\n                                <Form.Group>\r\n                                    <Form.Label>{t(\"City\")}</Form.Label>\r\n                                    <TextInput\r\n                                        name=\"city\"\r\n                                        id=\"city\"\r\n                                        value={initialVal.city}\r\n                                        onChange={handleChange}\r\n                                        required={false}\r\n                                        as=\"input\"\r\n                                        multiline={false}\r\n                                        rows={1}\r\n                                        errorMessage={{}}\r\n                                        validator={() => true}\r\n                                        pattern=\"\"\r\n                                    />\r\n                                </Form.Group>\r\n                            </Col>\r\n                            <Col lg md={3}>\r\n                                <Form.Group>\r\n                                    <Form.Label className=\"required-field\">{t(\"Country\")}</Form.Label>\r\n                                    <SelectGroup\r\n                                        name=\"country\"\r\n                                        id=\"country\"\r\n                                        value={initialVal.country}\r\n                                        onChange={handleChange}\r\n                                        errorMessage={t(\"thisfieldisrequired\")}\r\n                                        required\r\n                                    >\r\n                                        <option value=\"\">{t(\"SelectCountry\")}</option>\r\n                                        {countryList.map((item: any, i: any) => {\r\n                                            return (\r\n                                                <option data-worldregion={item.world_region} key={i} value={item._id}>\r\n                                                    {item.title}\r\n                                                </option>\r\n                                            );\r\n                                        })}\r\n                                    </SelectGroup>\r\n                                </Form.Group>\r\n                            </Col>\r\n                            <Col lg md={3}>\r\n                                <Form.Group style={{ maxWidth: \"250px\" }}>\r\n                                    <Form.Label>{t(\"CountryRegions\")}</Form.Label>\r\n                                    <MultiSelect\r\n                                        overrideStrings={{\r\n                                            selectSomeItems: t(\"SelectRegions\"),\r\n                                            allItemsAreSelected: \"All Regions are Selected\",\r\n                                        }}\r\n                                        options={regions}\r\n                                        value={initialVal.region}\r\n                                        className={\"region\"}\r\n                                        onChange={(e: any) => bindCountryRegions(e, \"region\")}\r\n                                        labelledBy={t(\"SelectRegions\")}\r\n                                    />\r\n                                </Form.Group>\r\n                            </Col>\r\n                        </Row>\r\n                        <Card.Text>\r\n                            <b>{t(\"FocalPoints\")}</b>\r\n                        </Card.Text>\r\n                        <hr />\r\n                        <Row className=\"mb-3\">\r\n                            <Col>\r\n                                <Form.Group>\r\n                                    <Tabs\r\n                                        activeKey={defaultActiveKey}\r\n                                        onSelect={(k: any) => setdefaultActiveKey(k)}\r\n                                        id=\"uncontrolled-tab-example\"\r\n                                    >\r\n                                        <Tab\r\n                                            eventKey={\"\"}\r\n                                            title={\r\n                                                <div>\r\n                                                    <p>{t(\"ExistingUser\")}</p>\r\n                                                </div>\r\n                                            }\r\n                                        >\r\n                                            <Row >\r\n                                                <Col md lg={4}>\r\n                                                    <Form.Group style={{ paddingTop: \"20px\" }}>\r\n                                                        <Form.Label>{t(\"ExistingUser\")}</Form.Label>\r\n                                                        <MultiSelect\r\n                                                            overrideStrings={{\r\n                                                                selectSomeItems: \"Select Users\",\r\n                                                                allItemsAreSelected: \"All Users are Selected\",\r\n                                                            }}\r\n                                                            options={usersList}\r\n                                                            onChange={(e: any) => bindCountryRegions(e, \"users\")}\r\n                                                            className={\"user\"}\r\n                                                            value={initialVal.users}\r\n                                                            labelledBy={t(\"SelectUsers\")}\r\n                                                        />\r\n                                                    </Form.Group>\r\n                                                </Col>\r\n                                            </Row>\r\n                                        </Tab>\r\n                                        {tab.map((item: any, i: number) => {\r\n                                            return (\r\n                                                <Tab\r\n                                                    eventKey={`${i + 1}`}\r\n                                                    title={`${t(\"FocalPoints\")} ${i + 1}`}\r\n                                                >\r\n                                                    <Row className=\"mb-3\">\r\n                                                        <Col lg={4} sm={6}>\r\n                                                            <Form.Group style={{ paddingTop: \"20px\" }}>\r\n                                                                <Col className=\"p-0\">\r\n                                                                    <Form.Label>{t(\"Name\")}</Form.Label>\r\n                                                                </Col>\r\n                                                                <label className=\"w-100\">\r\n                                                                    <TextInput\r\n                                                                        name=\"username\"\r\n                                                                        id=\"username\"\r\n                                                                        value={item.username}\r\n                                                                        onChange={(e: any) => handleTab(e, i)}\r\n                                                                    ></TextInput>\r\n                                                                </label>\r\n                                                            </Form.Group>\r\n                                                        </Col>\r\n\r\n                                                        <Col lg={4} sm={6}>\r\n                                                            <Form.Group style={{ paddingTop: \"20px\" }}>\r\n                                                                <Col className=\"p-0\">\r\n                                                                    <Form.Label>{t(\"Email\")}</Form.Label>\r\n                                                                </Col>\r\n                                                                <label className=\"w-100\">\r\n                                                                    <TextInput\r\n                                                                        name=\"email\"\r\n                                                                        id=\"email\"\r\n                                                                        value={item.email}\r\n                                                                        onChange={(e: any) => handleTab(e, i)}\r\n                                                                        pattern=\"^[^@]+@[^@]+\\.[^@]+$\"\r\n                                                                        errorMessage={{\r\n                                                                            pattern: \"Please enter a valid email\",\r\n                                                                        }}\r\n                                                                    ></TextInput>\r\n                                                                </label>\r\n                                                            </Form.Group>\r\n                                                        </Col>\r\n\r\n                                                        <Col lg={4} sm={6}>\r\n                                                            <InputGroup className=\"row ms-0 me-0\">\r\n                                                                <Form.Group\r\n                                                                    style={{ paddingTop: \"20px\" }}\r\n                                                                    className=\"institutionDialCode col-5 col-lg-4 ps-0 pe-0\"\r\n                                                                >\r\n                                                                    <Form.Label>{t(\"Telephone\")}</Form.Label>\r\n                                                                    <SelectGroup\r\n                                                                        type=\"numbers\"\r\n                                                                        name=\"focal_dial_code\"\r\n                                                                        id=\"focal_dial_Code\"\r\n                                                                        value={item.focal_dial_code}\r\n                                                                        onChange={(e: any) => handleTab(e, i)}\r\n                                                                    >\r\n                                                                        <option value={item.focal_dial_code}>\r\n                                                                            {item.focal_dial_code}\r\n                                                                        </option>\r\n                                                                        {countryList.map((item: any, i: any) => {\r\n                                                                            return (\r\n                                                                                <option\r\n                                                                                    key={i}\r\n                                                                                    value={item.dial_code}\r\n                                                                                >{`(${item.dial_code}) ${item.title}`}</option>\r\n                                                                            );\r\n                                                                        })}\r\n                                                                    </SelectGroup>\r\n                                                                </Form.Group>\r\n\r\n                                                                <Form.Group\r\n                                                                    style={{ paddingTop: \"20px\" }}\r\n                                                                    className=\"institutionTelephone col-7 col-lg-8 pe-0\"\r\n                                                                >\r\n                                                                    <Form.Label>&nbsp;</Form.Label>\r\n                                                                    <TextInput\r\n                                                                        name=\"mobile_number\"\r\n                                                                        id=\"mobile_number\"\r\n                                                                        value={item.mobile_number}\r\n                                                                        onChange={(e: any) => handleTab(e, i)}\r\n                                                                    ></TextInput>\r\n                                                                </Form.Group>\r\n                                                            </InputGroup>\r\n                                                        </Col>\r\n\r\n                                                        <Col>\r\n                                                            <Form.Group className=\"m-0\">\r\n                                                                <input\r\n                                                                    name=\"institution\"\r\n                                                                    id=\"institution\"\r\n                                                                    value={initialVal.title}\r\n                                                                    type=\"hidden\"\r\n                                                                ></input>\r\n                                                            </Form.Group>\r\n                                                        </Col>\r\n                                                    </Row>\r\n                                                    <Row>\r\n                                                        {i === 0 ? (\r\n                                                            <span></span>\r\n                                                        ) : (\r\n                                                            <Col xs lg=\"4\">\r\n                                                                <Button\r\n                                                                    onSelect={(k: any) => setdefaultActiveKey(k)}\r\n                                                                    variant=\"secondary\"\r\n                                                                    onClick={(e) => removeTab(e, i)}\r\n                                                                >\r\n                                                                    {t(\"Remove\")}\r\n                                                                </Button>\r\n                                                            </Col>\r\n                                                        )}\r\n                                                    </Row>\r\n                                                </Tab>\r\n                                            );\r\n                                        })}\r\n                                        <Tab\r\n                                            title={\r\n                                                <div>\r\n                                                    <span onClick={tabAdd}>\r\n                                                        {\" \"}\r\n                                                        <p>{t(\"AddnewFocalPoint\")}</p>\r\n                                                    </span>\r\n                                                </div>\r\n                                            }\r\n                                        ></Tab>\r\n                                    </Tabs>\r\n                                </Form.Group>\r\n                            </Col>\r\n                        </Row>\r\n\r\n                        <Card.Text>\r\n                            <b>{t(\"MoreInfo\")}</b>\r\n                        </Card.Text>\r\n                        <Row className=\"mb-3\">\r\n                            <Col lg md={4}>\r\n                                <Form.Group>\r\n                                    <Form.Label>{t(\"OrganisationType\")}</Form.Label>\r\n\r\n                                    <SelectGroup\r\n                                        name=\"organisationType\"\r\n                                        id=\"organisationType\"\r\n                                        value={initialVal.organisationType === null ? \"\" : initialVal.organisationType}\r\n                                        onChange={handleChange}\r\n                                        required={false}\r\n                                        errorMessage=\"\"\r\n                                    >\r\n                                        <option value=\"\">{t(\"SelectOrganisationType\")}</option>\r\n                                        {organizationList.map((item, i) => {\r\n                                            return (\r\n                                                <option key={i} value={item._id}>\r\n                                                    {item.title}\r\n                                                </option>\r\n                                            );\r\n                                        })}\r\n                                    </SelectGroup>\r\n                                </Form.Group>\r\n                            </Col>\r\n                            <Col lg md={4}>\r\n                                <Form.Group>\r\n                                    <Form.Label>{t(\"Network\")}</Form.Label>\r\n                                    <MultiSelect\r\n                                        overrideStrings={{\r\n                                            selectSomeItems: t(\"SelectNetwork\"),\r\n                                            allItemsAreSelected: \"All Networks are Selected\",\r\n                                        }}\r\n                                        options={networkList}\r\n                                        value={network}\r\n                                        onChange={handleNetwork}\r\n                                        className={\"net-work\"}\r\n                                        labelledBy={t(\"SelectNetwork\")}\r\n                                    />\r\n                                    <span>{networkVal}</span>\r\n                                </Form.Group>\r\n                            </Col>\r\n                            <Col lg md={4}>\r\n                                <Form.Group>\r\n                                    <Form.Label>{t(\"Expertise\")}</Form.Label>\r\n                                    <MultiSelect\r\n                                        overrideStrings={{\r\n                                            selectSomeItems: t(\"SelectExpertise\"),\r\n                                            allItemsAreSelected: \"All Expertise are Selected\",\r\n                                        }}\r\n                                        options={expertiseList}\r\n                                        value={expertise}\r\n                                        onChange={handleExpertise}\r\n                                        className={\"expertise\"}\r\n                                        labelledBy={t(\"SelectExpertise\")}\r\n                                    />\r\n                                    {hazardExpertiseVal}\r\n                                </Form.Group>\r\n                            </Col>\r\n                        </Row>\r\n                        <Row className=\"mb-3\">\r\n                            <Col md lg={6}>\r\n                                <Form.Group style={{ maxWidth: \"600px\" }}>\r\n                                    <Form.Label>{t(\"ExpertisewithHazards\")}</Form.Label>\r\n                                    <MultiSelect\r\n                                        overrideStrings={{\r\n                                            selectSomeItems: t(\"ExpertisewithHazards\"),\r\n                                            allItemsAreSelected: \"All Hazards are Selected\",\r\n                                        }}\r\n                                        options={hazardList}\r\n                                        filterOptions={filterOptions}\r\n                                        onChange={handleHazard}\r\n                                        value={hazards}\r\n                                        className={\"partners\"}\r\n                                        labelledBy={t(\"SelectPartners\")}\r\n                                    />\r\n                                </Form.Group>\r\n                            </Col>\r\n                            <Col md lg={6}>\r\n                                <Form.Group style={{ maxWidth: \"600px\" }}>\r\n                                    <Form.Label>{t(\"Partners\")}</Form.Label>\r\n                                    <MultiSelect\r\n                                        overrideStrings={{\r\n                                            selectSomeItems: t(\"SelectPartners\"),\r\n                                            allItemsAreSelected: \"All Partners are Selected\",\r\n                                        }}\r\n                                        options={partners}\r\n                                        onChange={handlePartners}\r\n                                        value={selctedPartners}\r\n                                        className={\"partners\"}\r\n                                        labelledBy={t(\"SelectPartners\")}\r\n                                    />\r\n                                </Form.Group>\r\n                            </Col>\r\n                        </Row>\r\n                        <Row className=\"mb-3\">\r\n                            <Col sm={12} md={6} lg={6}>\r\n                                <Form.Group>\r\n                                    <Form.Label>{t(\"Department\")}</Form.Label>\r\n                                    <TextInput\r\n                                        name=\"department\"\r\n                                        id=\"department\"\r\n                                        value={initialVal.department}\r\n                                        onChange={handleChange}\r\n                                        required={false}\r\n                                        as=\"input\"\r\n                                        multiline={false}\r\n                                        rows={1}\r\n                                        errorMessage={{}}\r\n                                        validator={() => true}\r\n                                        pattern=\"\"\r\n                                    ></TextInput>\r\n                                </Form.Group>\r\n                            </Col>\r\n                            <Col sm={12} md={6} lg={6}>\r\n                                <Form.Group>\r\n                                    <Form.Label>{t(\"Unit\")}</Form.Label>\r\n                                    <TextInput\r\n                                        name=\"unit\"\r\n                                        id=\"unit\"\r\n                                        value={initialVal.unit}\r\n                                        onChange={handleChange}\r\n                                        required={false}\r\n                                        as=\"input\"\r\n                                        multiline={false}\r\n                                        rows={1}\r\n                                        errorMessage={{}}\r\n                                        validator={() => true}\r\n                                        pattern=\"\"\r\n                                    ></TextInput>\r\n                                </Form.Group>\r\n                            </Col>\r\n                        </Row>\r\n                        <Card.Text>\r\n                            <b>{t(\"MediaGallery\")}</b>\r\n                        </Card.Text>\r\n                        <Row>\r\n                            <Col>\r\n                                <ReactDropZone\r\n                                    index={2}\r\n                                    datas={dropZoneCollection}\r\n                                    srcText={srcCollection}\r\n                                    getImgID={(id: any, index: any) => getID(id, index)}\r\n                                    getImageSource={(imgSrcArr: any[]) => getSource(imgSrcArr)}\r\n                                />\r\n                            </Col>\r\n                        </Row>\r\n                        <Row className=\"my-4\">\r\n                            <Col>\r\n                                <Button className=\"me-2\" type=\"submit\" variant=\"primary\" ref={buttonRef}>\r\n                                    {t(\"submit\")}\r\n                                </Button>\r\n                                <Button className=\"me-2\" onClick={resetHandler} variant=\"info\">\r\n                                    {t(\"reset\")}\r\n                                </Button>\r\n                                <Link href=\"/institution\" as=\"/institution\" >\r\n                                    <Button variant=\"secondary\">{t(\"Cancel\")}</Button>\r\n                                </Link>\r\n                            </Col>\r\n                        </Row>\r\n                    </Card.Body>\r\n                </Card>\r\n            </Container>\r\n        </ValidationFormWrapper>\r\n    );\r\n};\r\n\r\nexport default InstitutionForm;\r\nfunction initialValues_func(setinitialVal: React.Dispatch<any>, initialVal: any) {\r\n    setinitialVal(initialVal);\r\n}\r\n\r\nfunction itertion_func(tab: any, i: number) {\r\n    {\r\n        tab[i].username = tab[i].username.toLowerCase();\r\n        tab[i].email = tab[i].email.toLowerCase();\r\n    }\r\n}\r\n\r\nfunction response_methods_func(response: any, currentLang: string) {\r\n    const _expertise = response.expertise\r\n        ? response.expertise.map((item: any, _i: any) => {\r\n            return { label: item.title, value: item._id };\r\n        })\r\n        : [];\r\n    const _network = response.networks\r\n        ? response.networks.map((item: any, _i: any) => {\r\n            return { label: item.title, value: item._id };\r\n        })\r\n        : [];\r\n    const _partners = response.partners\r\n        ? response.partners.map((item: any, _i: any) => {\r\n            return { label: item.title, value: item._id };\r\n        })\r\n        : [];\r\n    //** Only get the index ****/\r\n    const _hazards = response.hazards\r\n        ? response.hazards.map((item: any) => {\r\n            return { label: item.title[currentLang], value: item._id };\r\n        })\r\n        : [];\r\n    response[\"users\"] = response.focal_points\r\n        ? response.focal_points.map((item: any, _i: any) => {\r\n            return { label: item.username, value: item._id };\r\n        })\r\n        : [];\r\n    return { _expertise, _network, _partners, _hazards };\r\n}\r\n\r\nfunction response_institution_Func(response: any) {\r\n    response.organisationType = response && response.type ? response.type._id : \"\";\r\n    response.addressL1 = response.address.line_1;\r\n    response.addressL2 = response.address.line_2;\r\n    response.city = response.address.city;\r\n    response.country = response.address.country && response.address.country._id ? response.address.country._id : \" \"; // status value\r\n    response.world_region = response.address && response.address.world_region ? response.address.world_region : \"\";\r\n    response.region = response.address.region\r\n        ? response.address.region.map((item: any, _i: any) => {\r\n            return { label: item.title, value: item._id };\r\n        })\r\n        : [];\r\n}\r\n", "\"use client\";\n\nimport * as React from 'react';\nimport classNames from 'classnames';\nimport { useBootstrapPrefix } from './ThemeProvider';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst InputGroupText = /*#__PURE__*/React.forwardRef(({\n  className,\n  bsPrefix,\n  as: Component = 'span',\n  ...props\n}, ref) => {\n  bsPrefix = useBootstrapPrefix(bsPrefix, 'input-group-text');\n  return /*#__PURE__*/_jsx(Component, {\n    ref: ref,\n    className: classNames(className, bsPrefix),\n    ...props\n  });\n});\nInputGroupText.displayName = 'InputGroupText';\nexport default InputGroupText;", "\"use client\";\n\nimport classNames from 'classnames';\nimport * as React from 'react';\nimport { useMemo } from 'react';\nimport { useBootstrapPrefix } from './ThemeProvider';\nimport FormCheckInput from './FormCheckInput';\nimport InputGroupContext from './InputGroupContext';\nimport InputGroupText from './InputGroupText';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst InputGroupCheckbox = props => /*#__PURE__*/_jsx(InputGroupText, {\n  children: /*#__PURE__*/_jsx(FormCheckInput, {\n    type: \"checkbox\",\n    ...props\n  })\n});\nconst InputGroupRadio = props => /*#__PURE__*/_jsx(InputGroupText, {\n  children: /*#__PURE__*/_jsx(FormCheckInput, {\n    type: \"radio\",\n    ...props\n  })\n});\nconst InputGroup = /*#__PURE__*/React.forwardRef(({\n  bsPrefix,\n  size,\n  hasValidation,\n  className,\n  // Need to define the default \"as\" during prop destructuring to be compatible with styled-components github.com/react-bootstrap/react-bootstrap/issues/3595\n  as: Component = 'div',\n  ...props\n}, ref) => {\n  bsPrefix = useBootstrapPrefix(bsPrefix, 'input-group');\n\n  // Intentionally an empty object. Used in detecting if a dropdown\n  // exists under an input group.\n  const contextValue = useMemo(() => ({}), []);\n  return /*#__PURE__*/_jsx(InputGroupContext.Provider, {\n    value: contextValue,\n    children: /*#__PURE__*/_jsx(Component, {\n      ref: ref,\n      ...props,\n      className: classNames(className, bsPrefix, size && `${bsPrefix}-${size}`, hasValidation && 'has-validation')\n    })\n  });\n});\nInputGroup.displayName = 'InputGroup';\nexport default Object.assign(InputGroup, {\n  Text: InputGroupText,\n  Radio: InputGroupRadio,\n  Checkbox: InputGroupCheckbox\n});", "import React, { forwardRef } from 'react';\r\nimport { Formik, Form, FormikProps, FormikHelpers } from 'formik';\r\nimport * as Yup from 'yup';\r\n\r\n// This is a wrapper component that replaces react-bootstrap4-form-validation with Formik\r\ninterface MockEvent {\r\n  preventDefault: () => void;\r\n  stopPropagation: () => void;\r\n  currentTarget: (EventTarget & Element) | null;\r\n  target: EventTarget | null;\r\n  nativeEvent: Event;\r\n  bubbles: boolean;\r\n  cancelable: boolean;\r\n  defaultPrevented: boolean;\r\n  eventPhase: number;\r\n  isTrusted: boolean;\r\n  timeStamp: number;\r\n  type: string;\r\n  isDefaultPrevented: () => boolean;\r\n  isPropagationStopped: () => boolean;\r\n  persist: () => void;\r\n}\r\n\r\ninterface ValidationFormWrapperProps {\r\n  children: React.ReactNode | ((formikProps: FormikProps<any>) => React.ReactNode);\r\n  onSubmit: (event: React.FormEvent | MockEvent, values?: Record<string, any>, actions?: FormikHelpers<Record<string, any>>) => void;\r\n  onErrorSubmit?: (errors: any) => void;\r\n  initialValues?: Record<string, any>;\r\n  enableReinitialize?: boolean;\r\n  autoComplete?: string;\r\n  className?: string;\r\n  onKeyPress?: (e: any) => void;\r\n}\r\n\r\nconst ValidationFormWrapper = forwardRef<HTMLFormElement, ValidationFormWrapperProps>((props, ref) => {\r\n  const { children, onSubmit, autoComplete, className, onKeyPress, initialValues, ...rest } = props;\r\n\r\n  // Create an empty validation schema by default\r\n  const validationSchema = Yup.object().shape({});\r\n\r\n  return (\r\n    <Formik\r\n      initialValues={initialValues || {}}\r\n      validationSchema={validationSchema}\r\n      onSubmit={(values: Record<string, any>, actions: FormikHelpers<Record<string, any>>) => {\r\n        // Create a mock event object with preventDefault method and currentTarget\r\n        const mockEvent: MockEvent = {\r\n          preventDefault: () => {},\r\n          stopPropagation: () => {},\r\n          currentTarget: null, // Set to null to avoid checkValidity errors\r\n          target: null,\r\n          nativeEvent: new Event('submit'),\r\n          bubbles: false,\r\n          cancelable: true,\r\n          defaultPrevented: false,\r\n          eventPhase: 0,\r\n          isTrusted: false,\r\n          timeStamp: Date.now(),\r\n          type: 'submit',\r\n          isDefaultPrevented: () => false,\r\n          isPropagationStopped: () => false,\r\n          persist: () => {}\r\n        };\r\n\r\n        if (onSubmit) {\r\n          // Pass the mock event object to maintain compatibility with the original code\r\n          onSubmit(mockEvent, values, actions);\r\n        }\r\n      }}\r\n      {...rest}\r\n    >\r\n      {(formikProps: FormikProps<any>) => (\r\n        <Form\r\n          ref={ref}\r\n          onSubmit={formikProps.handleSubmit}\r\n          autoComplete={autoComplete}\r\n          className={className}\r\n          onKeyPress={onKeyPress}\r\n        >\r\n          {typeof children === 'function' ? children(formikProps) : children}\r\n        </Form>\r\n      )}\r\n    </Formik>\r\n  );\r\n});\r\n\r\nValidationFormWrapper.displayName = 'ValidationFormWrapper';\r\n\r\nexport default ValidationFormWrapper;\r\n", "import React from 'react';\r\nimport { Form } from 'react-bootstrap';\r\nimport { Field, ErrorMessage, useField } from 'formik';\r\n\r\n// This component mimics the TextInput component from react-bootstrap4-form-validation\r\n// but uses Formik under the hood\r\nexport const TextInput = ({\r\n  name,\r\n  id,\r\n  required,\r\n  validator,\r\n  errorMessage,\r\n  onChange,\r\n  value,\r\n  as,\r\n  multiline,\r\n  rows,\r\n  pattern,\r\n  ...props\r\n}: any) => {\r\n  // Create a custom validation function that mimics the original validator\r\n  const validate = (val: any) => {\r\n    // Safely convert value to string and trim\r\n    const stringVal = typeof val === 'string' ? val : String(val || '');\r\n    if (required && (!val || stringVal.trim() === '')) {\r\n      return errorMessage?.validator || 'This field is required';\r\n    }\r\n\r\n    if (validator && !validator(val)) {\r\n      return errorMessage?.validator || 'Invalid value';\r\n    }\r\n\r\n    if (pattern && val) {\r\n      const regex = new RegExp(pattern);\r\n      if (!regex.test(val)) {\r\n        return errorMessage?.pattern || 'Invalid format';\r\n      }\r\n    }\r\n\r\n    return undefined;\r\n  };\r\n\r\n  return (\r\n    <Field name={name} validate={validate}>\r\n      {({ field, meta }: any) => (\r\n        <>\r\n          <Form.Control\r\n            {...field}\r\n            {...props}\r\n            id={id}\r\n            as={as || 'input'}\r\n            rows={rows}\r\n            isInvalid={meta.touched && !!meta.error}\r\n            onChange={(e: React.ChangeEvent<HTMLInputElement>) => {\r\n              field.onChange(e);\r\n              if (onChange) onChange(e);\r\n            }}\r\n            value={value !== undefined ? value : field.value}\r\n          />\r\n          {meta.touched && meta.error ? (\r\n            <Form.Control.Feedback type=\"invalid\">\r\n              {meta.error}\r\n            </Form.Control.Feedback>\r\n          ) : null}\r\n        </>\r\n      )}\r\n    </Field>\r\n  );\r\n};\r\n\r\n// This component mimics the SelectGroup component from react-bootstrap4-form-validation\r\n// but uses Formik under the hood\r\nexport const SelectGroup = ({\r\n  name,\r\n  id,\r\n  required,\r\n  errorMessage,\r\n  onChange,\r\n  value,\r\n  children,\r\n  ...props\r\n}: any) => {\r\n  // Create a custom validation function that mimics the original validator\r\n  const validate = (val: any) => {\r\n    if (required && (!val || val === '')) {\r\n      return errorMessage?.validator || 'This field is required';\r\n    }\r\n\r\n    return undefined;\r\n  };\r\n\r\n  return (\r\n    <Field name={name} validate={validate}>\r\n      {({ field, meta }: any) => (\r\n        <>\r\n          <Form.Control\r\n            as=\"select\"\r\n            {...field}\r\n            {...props}\r\n            id={id}\r\n            isInvalid={meta.touched && !!meta.error}\r\n            onChange={(e: React.ChangeEvent<HTMLSelectElement>) => {\r\n              field.onChange(e);\r\n              if (onChange) onChange(e);\r\n            }}\r\n            value={value !== undefined ? value : field.value}\r\n          >\r\n            {children}\r\n          </Form.Control>\r\n          {meta.touched && meta.error ? (\r\n            <Form.Control.Feedback type=\"invalid\">\r\n              {meta.error}\r\n            </Form.Control.Feedback>\r\n          ) : null}\r\n        </>\r\n      )}\r\n    </Field>\r\n  );\r\n};\r\n\r\n// Export both components as named exports and as a default object\r\nexport default {\r\n  TextInput,\r\n  SelectGroup\r\n};\r\n", "//Import Library\r\nimport React, { useState, useMemo, useEffect } from \"react\";\r\nimport { FileError, useDropzone } from \"react-dropzone\";\r\nimport { FontAwesomeIcon } from \"@fortawesome/react-fontawesome\";\r\nimport { faTimesCircle, faExclamationCircle, faCloudUploadAlt } from \"@fortawesome/free-solid-svg-icons\";\r\n\r\nimport toast from 'react-hot-toast';\r\n\r\n//Import services/components\r\nimport InstitutionImageEditor from \"./InstitutionImageEditor\";\r\nimport apiService from \"../../services/apiService\";\r\nimport { useTranslation } from 'next-i18next';\r\n\r\n\r\nconst InstitutionImageCropper = ({ getId, header, type } : { getId: any, header: any, type: any }) => {\r\n    const { t } = useTranslation('common');\r\n    const [modal, setModal] = useState<boolean>(false);\r\n    const [files, setFiles]: any = useState([]);\r\n    const [imageId, setImageId] = useState(\"\");\r\n    const [thumbUrl, setThumbUrl] = useState<string | null>(\"\");\r\n        const endpoint = type === \"application\" ? \"/files\" : \"/image\";\r\n\r\n    /*Display the cropped image in edit */\r\n    useEffect(() => {\r\n        header ? setThumbUrl(`${process.env.API_SERVER}/image/show/${header}`) : setThumbUrl(null);\r\n    }, [header]);\r\n\r\n    /*End*/\r\n\r\n    /*Styles For the container*/\r\n    const baseStyle: any = {\r\n        flex: 1,\r\n        display: \"flex\",\r\n        flexDirection: \"column\",\r\n        alignItems: \"center\",\r\n        justifyContent: \"center\",\r\n        width: \"100%\",\r\n        height: \"100%\",\r\n        borderWidth: 0.1,\r\n        borderColor: \"#fafafa\",\r\n        backgroundColor: \"#fafafa\",\r\n        color: \"black\",\r\n        transition: \"border  .24s ease-in-out\",\r\n    };\r\n\r\n    const thumb: any = {\r\n        display: \"inline-flex\",\r\n        borderRadius: 2,\r\n        border: \"1px solid #ddd\",\r\n        marginBottom: 8,\r\n        marginRight: 20,\r\n        width: 170,\r\n        height: 100,\r\n        padding: 2,\r\n        position: \"relative\",\r\n        boxShadow: \"0 0 15px 0.25px rgba(0,0,0,0.25)\",\r\n        boxSizing: \"border-box\",\r\n    };\r\n\r\n    const thumbsContainer: any = {\r\n        display: \"flex\",\r\n        flexDirection: \"row\",\r\n        justifyContent: \"flex-start\",\r\n        flexWrap: \"wrap\",\r\n        marginTop: 20,\r\n    };\r\n\r\n    const thumbInner: any = {\r\n        display: \"flex\",\r\n    };\r\n\r\n    const icon: any = {\r\n        position: \"absolute\",\r\n        fontSize: \"22px\",\r\n        top: \"-10px\",\r\n        right: \"-10px\",\r\n        zIndex: 1000,\r\n        cursor: \"pointer\",\r\n        backgroundColor: \"#fff\",\r\n        color: \"#000\",\r\n        borderRadius: \"50%\",\r\n    };\r\n\r\n    const img = {\r\n        display: \"block\",\r\n        height: \"100%\",\r\n    };\r\n\r\n    const activeStyle: any = {\r\n        borderColor: \"#2196f3\",\r\n    };\r\n    /*End of Styles*/\r\n\r\n    /**Handle Modal Close**/\r\n    const modalClose = (val: boolean) => {\r\n        setModal(val);\r\n    };\r\n    /*End*/\r\n\r\n    /*For Handle the dropFiles*/\r\n    const onDrop = (file: any) => {\r\n        const accFiles = file.map((file: any, i: any) =>\r\n            Object.assign(file, {\r\n                preview: URL.createObjectURL(file),\r\n            })\r\n        );\r\n        setFiles(accFiles);\r\n        if (file.length > 0) {\r\n            setModal(true);\r\n        }\r\n    };\r\n    /*End*/\r\n\r\n    /*Setting the intial accept type & size e.t.c */\r\n    const { getRootProps, getInputProps, isDragActive, isDragAccept, isDragReject, fileRejections } = useDropzone({\r\n        noClick: false,\r\n        accept: \"image/*\",\r\n        multiple: false,\r\n        minSize: 0,\r\n        maxSize: 2000000,\r\n        onDrop,\r\n        validator: nameLengthValidator,\r\n    });\r\n    /*End*/\r\n\r\n    /*Styles for drag & drop Container*/\r\n    const style = useMemo(\r\n        () => ({\r\n            ...baseStyle,\r\n            ...(isDragActive ? activeStyle : { outline: \"2px dashed #bbb\" }),\r\n            ...(isDragAccept ? { outline: \"2px dashed #595959\" } : { outline: \"2px dashed #bbb\" }),\r\n            ...(isDragReject ? { outline: \"2px dashed red\" } : { activeStyle }),\r\n        }),\r\n        [isDragActive, isDragReject]\r\n    );\r\n    /*End*/\r\n\r\n    /*Reject the file If length is greater than 2mb*/\r\n    const isFileTooLarge = fileRejections.length > 0 && fileRejections[0].file.size > 2000000;\r\n    /*End*/\r\n\r\n    /*Get Id crop using callBack Function*/\r\n    const getIdHandler = (id: any) => {\r\n        setImageId(id);\r\n        getId(id);\r\n    };\r\n    /*End*/\r\n\r\n    /*Remove File Handler*/\r\n    const removeFile = async () => {\r\n        let res;\r\n        if (header) {\r\n            res = await apiService.remove(`image/${header}`);\r\n        } else {\r\n            res = await apiService.remove(`image/${imageId}`);\r\n        }\r\n\r\n        if (res && res._id) {\r\n            setThumbUrl(null);\r\n            getIdHandler(null);\r\n        }\r\n    };\r\n    /*End*/\r\n\r\n    //***Get Blob from the react avaatr editor for preview*/\r\n    const blobHandler = (url: any) => {\r\n        setThumbUrl(url);\r\n    };\r\n    /*End*/\r\n\r\n    function nameLengthValidator(file: any) {\r\n        if (endpoint === \"/image\") {\r\n            if (file.type.substring(0, 5) === \"image\") {\r\n                return null;\r\n            } else {\r\n                toast.error(t(\"toast.filetypenotsupport\"));\r\n            }\r\n        } else if (endpoint === \"/files\") {\r\n            if (!(file.type.substring(0, 5) !== \"image\")) {\r\n                toast.error(t(\"toast.filetypenotsupport\"));\r\n            }\r\n        } else {\r\n            return null;\r\n        }\r\n        \r\n        return null;\r\n    }\r\n\r\n    return (\r\n        <>\r\n            <div\r\n                className=\" d-flex justify-content-center align-items-center mt-3\"\r\n                style={{ width: \"100%\", height: \"180px\" }}\r\n            >\r\n                <div {...getRootProps({ style })}>\r\n                    <input {...getInputProps()} />\r\n                    <FontAwesomeIcon icon={faCloudUploadAlt} size=\"4x\" color=\"#999\" />\r\n                    <p style={{ color: \"#595959\", marginBottom: \"0px\" }}>\r\n                        {t(\"Drag'n'dropsomefileshere,orclicktoselectfiles\")}\r\n                    </p>\r\n                    <small style={{ color: \"#595959\" }}>\r\n                        <b>{t(\"Note:\")}</b> {t(\"Onesingleimagewillbeaccepted\")}\r\n                    </small>\r\n                    {isFileTooLarge && (\r\n                        <small className=\"text-danger mt-2\">\r\n                            <FontAwesomeIcon icon={faExclamationCircle} size=\"1x\" color=\"red\" />\r\n                            &nbsp;{t(\"FileistoolargeItshouldbelessthan2MB\")}\r\n                        </small>\r\n                    )}\r\n                    {isDragReject && (\r\n                        <small className=\"text-danger\" style={{ color: \"red\" }}>\r\n                            <FontAwesomeIcon icon={faExclamationCircle} size=\"1x\" color=\"red\" />\r\n                            {t(\"Filetypenotacceptedsorr\")}\r\n                        </small>\r\n                    )}\r\n                </div>\r\n            </div>\r\n            {thumbUrl && (\r\n                <>\r\n                    <div style={thumbsContainer}>\r\n                        <div style={thumb}>\r\n                            <div style={thumbInner}>\r\n                                <img src={thumbUrl} style={img} />\r\n                            </div>\r\n                            <FontAwesomeIcon icon={faTimesCircle} style={icon} color=\"black\" onClick={removeFile} />\r\n                        </div>\r\n                    </div>\r\n                </>\r\n            )}\r\n\r\n            <InstitutionImageEditor\r\n                isOpen={modal}\r\n                getId={(id: any) => getIdHandler(id)}\r\n                image={files && files[0] ? files[0].preview : \"\"}\r\n                onModalClose={(val: any) => modalClose(val)}\r\n                fileName={files && files[0] ? files[0].name : \"\"}\r\n                getBlob={(url: any) => blobHandler(url)}\r\n            />\r\n        </>\r\n    );\r\n};\r\n\r\nexport default InstitutionImageCropper;\r\n", "//Import Library\r\nimport React, { useMemo, useEffect, useState, useCallback } from \"react\";\r\nimport { useDropzone } from \"react-dropzone\";\r\nimport { FontAwesomeIcon } from \"@fortawesome/react-fontawesome\";\r\nimport {\r\n  faExclamationCircle,\r\n  faCloudUploadAlt,\r\n} from \"@fortawesome/free-solid-svg-icons\";\r\nimport { Form, Button, Modal, Col } from \"react-bootstrap\";\r\nimport _ from \"lodash\";\r\n\r\nimport toast from 'react-hot-toast';\r\n\r\n//Import services/components\r\nimport apiService from \"../../services/apiService\";\r\nimport { useTranslation } from 'next-i18next';\r\n\r\n// Define type for temp array items\r\ninterface TempItem {\r\n  serverID: string;\r\n  file?: any;\r\n  index: number;\r\n  type: string;\r\n}\r\n\r\nlet temp: TempItem[] = [];\r\n\r\nconst baseStyle: any = {\r\n  flex: 1,\r\n  display: \"flex\",\r\n  flexDirection: \"column\",\r\n  alignItems: \"center\",\r\n  justifyContent: \"center\",\r\n  width: \"100%\",\r\n  height: \"100%\",\r\n  borderWidth: 0.1,\r\n  borderColor: \"#fafafa\",\r\n  backgroundColor: \"#fafafa\",\r\n  color: \"black\",\r\n  transition: \"border  .24s ease-in-out\",\r\n  padding: \"15px\",\r\n};\r\n\r\nconst thumb: any = {\r\n  display: \"flex\",\r\n  borderRadius: 2,\r\n  border: \"1px solid #ddd\",\r\n  margin: 8,\r\n  height: 175,\r\n  boxShadow: \"0 0 15px 0.25px rgba(0,0,0,0.15)\",\r\n  boxSizing: \"border-box\",\r\n};\r\n\r\nconst thumbsContainer: any = {\r\n  display: \"flex\",\r\n  padding: \"10px\",\r\n  width: \"100%\",\r\n  border: \"2px solid gray\",\r\n  flexDirection: \"column\",\r\n  justifyContent: \"flex-start\",\r\n  flexWrap: \"wrap\",\r\n  marginTop: 20,\r\n};\r\n\r\nconst thumbInner: any = {\r\n  display: \"flex\",\r\n  minWidth: 0,\r\n};\r\n\r\nconst deleteIcon: any = {\r\n  display: \"flex\",\r\n  justifyContent: \"center\",\r\n  alignItems: \"center\",\r\n  marginLeft: 30,\r\n};\r\n\r\nconst img = {\r\n  width: \"150px\",\r\n};\r\n\r\nconst activeStyle: any = {\r\n  borderColor: \"#2196f3\",\r\n};\r\n\r\nconst ReactDropZone = (props: any) => {\r\n  const { t } = useTranslation('common');\r\n  const [modalShow, setModalShow] = useState(false);\r\n  const [deleteFile, setDeleteFile] = useState();\r\n  const limit: any =\r\n    props.type == \"application\" ? 20971520 : process.env.UPLOAD_LIMIT;\r\n  const [files, setFiles] = useState<any[]>([]);\r\n  const [multi, setMulti] = useState(true);\r\n  const [imageSource, setImageSource] = useState<string[]>([]);\r\n\r\n  const endpoint = props && props.type === \"application\" ? \"/files\" : \"/image\";\r\n  const imageDelete = async (id: string) => {\r\n    const _res = await apiService.remove(`${endpoint}/${id}`);\r\n  };\r\n\r\n  const removeFile = (file: any) => {\r\n    setDeleteFile(file);\r\n    setModalShow(true);\r\n  };\r\n\r\n  const handleSource = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>, index: number) => {\r\n    const items = [...imageSource];\r\n    items[index] = e.target.value;\r\n    setImageSource(items);\r\n  };\r\n\r\n  const getComponent = (file: any) => {\r\n    const fileType = file && file.name.split(\".\").pop();\r\n    switch (fileType) {\r\n      case \"JPG\":\r\n      case \"jpg\":\r\n      case \"jpeg\":\r\n      case \"jpg\":\r\n      case \"png\":\r\n        return <img src={file.preview} style={img} />;\r\n      case \"pdf\":\r\n        return (\r\n          <img\r\n            src=\"/images/fileIcons/pdfFile.png\"\r\n            className={\r\n              props.type === \"application\" ? \"docPreview\" : \"imgPreview\"\r\n            }\r\n          />\r\n        );\r\n      case \"docx\":\r\n        return (\r\n          <img\r\n            src=\"/images/fileIcons/wordFile.png\"\r\n            className={\r\n              props.type === \"application\" ? \"docPreview\" : \"imgPreview\"\r\n            }\r\n          />\r\n        );\r\n      case \"xls\":\r\n      case \"xlsx\":\r\n        return (\r\n          <img\r\n            src=\"/images/fileIcons/xlsFile.png\"\r\n            className={\r\n              props.type === \"application\" ? \"docPreview\" : \"imgPreview\"\r\n            }\r\n          />\r\n        );\r\n      default:\r\n        return (\r\n          <img\r\n            src=\"/images/fileIcons/wordFile.png\"\r\n            className={\r\n              props.type === \"application\" ? \"docPreview\" : \"imgPreview\"\r\n            }\r\n          />\r\n        );\r\n    }\r\n  };\r\n\r\n  const modalHide = () => setModalShow(false);\r\n\r\n  const cancelHandler = () => {\r\n    setModalShow(false);\r\n  };\r\n\r\n  const submitHandler = (fileselector: any) => {\r\n    fileselector = deleteFile;\r\n    const obj =\r\n      fileselector && fileselector._id\r\n        ? { serverID: fileselector._id }\r\n        : { file: fileselector };\r\n    const _index = _.findIndex(temp, obj);\r\n    //**Delete the source Field**//\r\n    const removeSrc = [...imageSource];\r\n    removeSrc.splice(_index, 1);\r\n    setImageSource(removeSrc);\r\n    //**End**/\r\n    imageDelete(temp[_index].serverID);\r\n    temp.splice(_index, 1);\r\n    props.getImgID(temp, props.index ? props.index : 0);\r\n    const newFiles = [...files];\r\n    newFiles.splice(newFiles.indexOf(fileselector), 1);\r\n    setFiles(newFiles);\r\n    setModalShow(false);\r\n  };\r\n\r\n  const thumbs: any = files.map((file: any, i) => {\r\n    return (\r\n      <div key={i}>\r\n        <Col xs={12}>\r\n          <div className=\"row\">\r\n            <Col\r\n              md={4}\r\n              lg={3}\r\n              className={\r\n                props.type === \"application text-center align-self-center\"\r\n                  ? \"docImagePreview text-center align-self-center\"\r\n                  : \"imgPreview text-center align-self-center\"\r\n              }\r\n            >\r\n              {getComponent(file)}\r\n            </Col>\r\n            <Col md={5} lg={7} className=\"align-self-center\">\r\n              <Form>\r\n                <Form.Group controlId=\"filename\">\r\n                  <Form.Label className=\"mt-2\">{t(\"FileName\")}</Form.Label>\r\n                  <Form.Control\r\n                    size=\"sm\"\r\n                    type=\"text\"\r\n                    disabled\r\n                    value={file.original_name ? file.original_name : file.name}\r\n                  />\r\n                </Form.Group>\r\n                <Form.Group controlId=\"description\">\r\n                  <Form.Label>\r\n                    {props.type === \"application\"\r\n                      ? t(\"ShortDescription/(Max255Characters)\")\r\n                      : t(\"Source/Description\")}\r\n                  </Form.Label>\r\n                  <Form.Control\r\n                    maxLength={props.type === \"application\" ? 255 : undefined}\r\n                    size=\"sm\"\r\n                    type=\"text\"\r\n                    placeholder={\r\n                      props.type === \"application\"\r\n                        ? t(\"`Enteryourdocumentdescription`\")\r\n                        : t(\"`Enteryourimagesource/description`\")\r\n                    }\r\n                    value={imageSource[i]}\r\n                    onChange={(e) => handleSource(e, i)}\r\n                  />\r\n                </Form.Group>\r\n              </Form>\r\n            </Col>\r\n            <Col\r\n              md={3}\r\n              lg={2}\r\n              className=\"align-self-center text-center\"\r\n              onClick={() => removeFile(file)}\r\n            >\r\n              <Button variant=\"dark\">{t(\"Remove\")}</Button>\r\n            </Col>\r\n          </div>\r\n        </Col>\r\n        <Modal show={modalShow} onHide={modalHide}>\r\n          <Modal.Header closeButton>\r\n            <Modal.Title>{t(\"DeleteFile\")}</Modal.Title>\r\n          </Modal.Header>\r\n          <Modal.Body>{t(\"Areyousurewanttodeletethisfile?\")}</Modal.Body>\r\n          <Modal.Footer>\r\n            <Button variant=\"secondary\" onClick={cancelHandler}>\r\n              {t(\"Cancel\")}\r\n            </Button>\r\n            <Button variant=\"primary\" onClick={() => submitHandler(file)}>\r\n              {t(\"yes\")}\r\n            </Button>\r\n          </Modal.Footer>\r\n        </Modal>\r\n      </div>\r\n    );\r\n  });\r\n\r\n  useEffect(() => {\r\n    files.forEach((file) => URL.revokeObjectURL(file.preview));\r\n    temp = [];\r\n  }, []);\r\n\r\n  useEffect(() => {\r\n    props.getImageSource(imageSource);\r\n  }, [imageSource]);\r\n\r\n  useEffect(() => {\r\n    setImageSource(props.srcText);\r\n  }, [props.srcText]);\r\n\r\n  useEffect(() => {\r\n    props && props.singleUpload === \"true\" && setMulti(false);\r\n    if (props && props.datas) {\r\n      const newObj = props.datas.map((item: any, _i: number) => {\r\n        temp.push({\r\n          serverID: item._id,\r\n          index: props.index ? props.index : 0,\r\n          type: item.name.split(\".\")[1],\r\n        });\r\n        const previewState = {\r\n          ...item,\r\n          preview: `${process.env.API_SERVER}/image/show/${item._id}`,\r\n        };\r\n        return previewState;\r\n      });\r\n      setFiles([...newObj]);\r\n    }\r\n  }, [props.datas]);\r\n\r\n  const filesUpload = async (filesinitial: any[], index: number) => {\r\n    if (filesinitial.length > index) {\r\n      try {\r\n        const form: any = new FormData();\r\n        form.append(\"file\", filesinitial[index]);\r\n        const res = await apiService.post(endpoint, form, {\r\n          \"Content-Type\": \"multipart/form-data\",\r\n        });\r\n        temp.push({\r\n          serverID: res._id,\r\n          file: filesinitial[index],\r\n          index: props.index ? props.index : 0,\r\n          type: filesinitial[index].name.split(\".\")[1],\r\n        });\r\n        filesUpload(filesinitial, index + 1);\r\n      } catch (error) {\r\n        filesUpload(filesinitial, index + 1);\r\n      }\r\n    } else {\r\n      props.getImgID(temp, props.index ? props.index : 0);\r\n    }\r\n  };\r\n\r\n  const onDrop = useCallback(async (ondrop_files: any[]) => {\r\n    await filesUpload(ondrop_files, 0);\r\n    const accFiles = ondrop_files.map((file: any) =>\r\n      Object.assign(file, {\r\n        preview: URL.createObjectURL(file),\r\n      })\r\n    );\r\n    multi\r\n      ? setFiles((prevState) => [...prevState, ...accFiles])\r\n      : setFiles([...accFiles]);\r\n  }, []);\r\n\r\n  function nameLengthValidator(file: File) {\r\n    if (endpoint === \"/image\") {\r\n      if (file.type.substring(0, 5) === \"image\") {\r\n        return null;\r\n      } else {\r\n        toast.error(t(\"toast.filetypenotsupport\"));\r\n        return { code: \"file-invalid-type\", message: \"File type not supported\" };\r\n      }\r\n    } else if (endpoint === \"/files\") {\r\n      if (!(file.type.substring(0, 5) !== \"image\")) {\r\n        toast.error(t(\"toast.filetypenotsupport\"));\r\n        return { code: \"file-invalid-type\", message: \"File type not supported\" };\r\n      }\r\n    }\r\n    return null;\r\n  }\r\n  const {\r\n    getRootProps,\r\n    getInputProps,\r\n    isDragActive,\r\n    isDragAccept,\r\n    isDragReject,\r\n    fileRejections,\r\n  } = useDropzone({\r\n    accept:\r\n      props && props.type\r\n        ? \"application/pdf, application/vnd.openxmlformats-officedocument.wordprocessingml.document, application/vnd.oasis.opendocument.text,application/vnd.openxmlformats-officedocument.spreadsheetml.sheet,text/csv\"\r\n        : \"image/*\",\r\n    multiple: multi,\r\n    minSize: 0,\r\n    maxSize: limit,\r\n    onDrop,\r\n    validator: nameLengthValidator,\r\n  });\r\n\r\n  const style = useMemo(\r\n    () => ({\r\n      ...baseStyle,\r\n      ...(isDragActive ? activeStyle : { outline: \"2px dashed #bbb\" }),\r\n      ...(isDragAccept\r\n        ? { outline: \"2px dashed #595959\" }\r\n        : { outline: \"2px dashed #bbb\" }),\r\n      ...(isDragReject ? { outline: \"2px dashed red\" } : { activeStyle }),\r\n    }),\r\n    [isDragActive, isDragReject]\r\n  );\r\n\r\n  let dropZoneMsg;\r\n  if (props && props.type === \"application\") {\r\n    dropZoneMsg = (\r\n      <small style={{ color: \"#595959\" }}>{t(\"DocumentWeSupport\")}</small>\r\n    );\r\n  } else {\r\n    dropZoneMsg = (\r\n      <small style={{ color: \"#595959\" }}>{t(\"ImageWeSupport\")}</small>\r\n    );\r\n  }\r\n\r\n  const isFileTooLarge =\r\n    fileRejections.length > 0 && fileRejections[0].file.size > limit;\r\n  return (\r\n    <>\r\n      <div\r\n        className=\" d-flex justify-content-center align-items-center mt-3\"\r\n        style={{ width: \"100%\", height: \"180px\" }}\r\n      >\r\n        <div {...getRootProps({ style })}>\r\n          <input {...getInputProps()} />\r\n          <FontAwesomeIcon icon={faCloudUploadAlt} size=\"4x\" color=\"#999\" />\r\n          <p style={{ color: \"#595959\", marginBottom: \"0px\" }}>\r\n            {t(\"Drag'n'dropsomefileshere,orclicktoselectfiles\")}\r\n          </p>\r\n\r\n          {!multi && (\r\n            <small style={{ color: \"#595959\" }}>\r\n              <b>Note:</b> One single image will be accepted\r\n            </small>\r\n          )}\r\n          {dropZoneMsg}\r\n          {props.type === \"application\"\r\n            ? isFileTooLarge && (\r\n                <small className=\"text-danger mt-2\">\r\n                  <FontAwesomeIcon\r\n                    icon={faExclamationCircle}\r\n                    size=\"1x\"\r\n                    color=\"red\"\r\n                  />{\" \"}\r\n                  {t(\"FileistoolargeItshouldbelessthan20MB\")}\r\n                </small>\r\n              )\r\n            : isFileTooLarge && (\r\n                <small className=\"text-danger mt-2\">\r\n                  <FontAwesomeIcon\r\n                    icon={faExclamationCircle}\r\n                    size=\"1x\"\r\n                    color=\"red\"\r\n                  />{\" \"}\r\n                  {t(\"FileistoolargeItshouldbelessthan20MB\")}\r\n                </small>\r\n              )}\r\n          {isDragReject && (\r\n            <small className=\"text-danger\" style={{ color: \"#595959\" }}>\r\n              <FontAwesomeIcon\r\n                icon={faExclamationCircle}\r\n                size=\"1x\"\r\n                color=\"red\"\r\n              />{\" \"}\r\n              {t(\"Filetypenotacceptedsorr\")}\r\n            </small>\r\n          )}\r\n        </div>\r\n      </div>\r\n      {files.length > 0 && <div style={thumbsContainer}>{thumbs}</div>}\r\n    </>\r\n  );\r\n};\r\n\r\nexport default ReactDropZone;\r\n"], "names": ["InvitationService", "arrayDifference", "array1", "array2", "difference", "for<PERSON>ach", "filter", "ar2El", "_id", "ar1El", "value", "length", "push", "deleteInvitations", "userIds", "instituteId", "userId", "userData", "apiService", "get", "institutionInvites", "invite", "institutionId", "patch", "isDuplicateInvite", "userInvites", "dups", "ui", "getNewInviteMeta", "institutionTitle", "institutionName", "status", "getUserData", "userEmail", "userName", "query", "email", "username", "sendInvitations", "users", "user", "map", "inviteNewUserWithEmail", "data", "isOpen", "InstitutionImageEditor", "onModalClose", "image", "getId", "fileName", "getBlob", "scale", "setScale", "useState", "name", "setName", "img", "setImg", "editor<PERSON><PERSON>", "useRef", "t", "useTranslation", "useEffect", "<PERSON><PERSON><PERSON><PERSON>", "blob", "dataURLtoBlob", "arr", "canvas", "dataurl", "split", "mime", "match", "bstr", "atob", "n", "u8arr", "Uint8Array", "charCodeAt", "Blob", "type", "current", "getImage", "toDataURL", "urlCreator", "blobUrl", "URL", "window", "webkitURL", "fd", "FormData", "append", "res", "post", "newLocal", "toast", "success", "div", "Modal", "show", "size", "aria-<PERSON>by", "onHide", "centered", "Body", "className", "AvatarEditor", "ref", "width", "height", "borderRadius", "color", "style", "span", "Row", "Col", "sm", "md", "lg", "b", "RangeSlider", "tooltip", "min", "max", "step", "variant", "Number", "changeEvent", "target", "Footer", "<PERSON><PERSON>", "onClick", "routes", "InstitutionForm", "institution", "buttonRef", "formRef", "i18n", "_initialState", "organisationName", "title", "country", "world_region", "website", "organisationType", "telephone", "dial_code", "contact_name", "twitter", "addressL1", "addressL2", "city", "region", "expertise", "network", "hazards", "description", "images", "header", "department", "unit", "use_default_header", "images_src", "useRouter", "currentLang", "language", "titleSearch", "title_de", "countryList", "setcountryList", "dropZoneCollection", "setDropZoneCollection", "srcCollection", "setSrcCollection", "headerDropCollection", "setHeaderDropCollection", "<PERSON><PERSON><PERSON><PERSON>", "setExpertise", "setNetwork", "regions", "setRegion", "usersList", "setUsersList", "defaultActiveKey", "setdefaultActiveKey", "setHazardInfiniteList", "tab", "setTab", "focal_dial_code", "mobile_number", "organizationList", "setOrganizationList", "expertiseList", "setexpertiseList", "networkList", "setnetworkList", "hazardList", "sethazardList", "editform", "initialVal", "setinitialVal", "existingFpUsers", "setExistingFpUsers", "partners", "setPartners", "institutionParams", "sort", "limit", "partnersParams", "select", "lean", "response_institution_Func", "response", "address", "line_1", "line_2", "item", "_i", "label", "_expertise", "_network", "_partners", "_hazards", "response_methods_func", "networks", "focal_points", "getRegion", "setSelectedPartners", "prevState", "<PERSON><PERSON><PERSON><PERSON>", "getUsers", "getCountries", "getOrganization", "getExpertise", "getNetwork", "getPartners", "institutionParamslist", "languageCode", "Array", "isArray", "setApprovedUsers", "tempStorage", "foundInstituteInvites", "isUserReqPendingForInstitute", "_users", "currentUser", "result", "filterUsers", "filteredUsers", "_filteredUsers", "institutionParamsinit", "partnersParamsinit", "partner", "allHazardType", "responsetype", "RegionParams", "_", "hazard_type", "_hazard", "id", "_regions", "a", "localeCompare", "clearValue", "obj", "handleChange", "e", "selectedIndex", "worldRegion", "getAttribute", "bindCountryRegions", "handleDescription", "hazardExpertiseVal", "networkVal", "selctedPartners", "tabAdd", "_temp", "removeTab", "_e", "i", "splice", "handleTab", "_tempCosts", "inviteFpUsers", "institutionData", "removedUsers", "invitationService", "newInInvites", "dupInvite", "handleSubmit", "toastMsg", "preventDefault", "extuser", "itertion_func", "toLowerCase", "isuniqueUsername", "isuniqueEmail", "error", "trim", "primary_focal_point", "find", "_fp", "Router", "getID", "index", "imageData", "filterIndex0", "serverID", "String", "getSource", "imgSrcArr", "getIdHandler", "ValidationFormWrapper", "onSubmit", "initialValues", "enableReinitialize", "Container", "fluid", "Card", "Title", "Form", "Group", "Label", "TextInput", "required", "onChange", "validator", "errorMessage", "as", "multiline", "rows", "EditorComponent", "initContent", "evt", "Check", "checked", "InstitutionImageHandler", "pattern", "InputGroup", "SelectGroup", "option", "citem", "Text", "data-worldregion", "max<PERSON><PERSON><PERSON>", "MultiSelect", "overrideStrings", "selectSomeItems", "allItemsAreSelected", "options", "labelledBy", "hr", "Tabs", "active<PERSON><PERSON>", "onSelect", "k", "Tab", "eventKey", "p", "paddingTop", "input", "xs", "handleNetwork", "handleExpertise", "filterOptions", "regex", "handleHazard", "handlePartners", "ReactDropZone", "datas", "srcText", "getImgID", "getImageSource", "re<PERSON><PERSON><PERSON><PERSON>", "scrollTo", "Link", "href", "InputGroupText", "React", "bsPrefix", "Component", "props", "useBootstrapPrefix", "_jsx", "classNames", "hasValidation", "contextValue", "useMemo", "InputGroupContext", "Provider", "children", "displayName", "Object", "assign", "Radio", "InputGroupRadio", "FormCheckInput", "Checkbox", "InputGroupCheckbox", "forwardRef", "autoComplete", "onKeyPress", "rest", "validationSchema", "<PERSON><PERSON>", "shape", "<PERSON><PERSON>", "values", "actions", "mockEvent", "stopPropagation", "currentTarget", "nativeEvent", "Event", "bubbles", "cancelable", "defaultPrevented", "eventPhase", "isTrusted", "timeStamp", "Date", "now", "isDefaultPrevented", "isPropagationStopped", "persist", "formikProps", "Field", "validate", "stringVal", "val", "RegExp", "test", "field", "meta", "Control", "isInvalid", "touched", "undefined", "<PERSON><PERSON><PERSON>", "InstitutionImageCropper", "modal", "setModal", "files", "setFiles", "imageId", "setImageId", "thumbUrl", "setThumbUrl", "endpoint", "process", "baseStyle", "flex", "display", "flexDirection", "alignItems", "justifyContent", "borderWidth", "borderColor", "backgroundColor", "transition", "activeStyle", "modalClose", "getRootProps", "getInputProps", "isDragActive", "isDragAccept", "isDragReject", "fileRejections", "useDropzone", "noClick", "accept", "multiple", "minSize", "maxSize", "onDrop", "file", "preview", "createObjectURL", "nameLengthValidator", "substring", "outline", "isFileTooLarge", "removeFile", "remove", "b<PERSON><PERSON><PERSON><PERSON><PERSON>", "url", "FontAwesomeIcon", "icon", "faCloudUploadAlt", "marginBottom", "small", "faExclamationCircle", "flexWrap", "marginTop", "border", "marginRight", "padding", "position", "boxShadow", "boxSizing", "thumbInner", "src", "faTimesCircle", "fontSize", "top", "right", "zIndex", "cursor", "temp", "thumbsContainer", "dropZoneMsg", "modalShow", "setModalShow", "deleteFile", "setDeleteFile", "multi", "set<PERSON><PERSON><PERSON>", "imageSource", "setImageSource", "imageDelete", "handleSource", "items", "getComponent", "fileType", "pop", "modalHide", "cancelHandler", "<PERSON><PERSON><PERSON><PERSON>", "fileselector", "_index", "removeSrc", "newFiles", "indexOf", "thumbs", "controlId", "disabled", "original_name", "max<PERSON><PERSON><PERSON>", "placeholder", "Header", "closeButton", "revokeObjectURL", "singleUpload", "filesUpload", "filesinitial", "form", "useCallback", "ondrop_files", "accFiles", "code", "message"], "sourceRoot": "", "ignoreList": [3, 4]}