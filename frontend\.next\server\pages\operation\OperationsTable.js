"use strict";(()=>{var e={};e.id=6256,e.ids=[636,3220,6256],e.modules={123:e=>{e.exports=require("dom-helpers/removeEventListener")},1332:e=>{e.exports=require("react-custom-scrollbars-2")},1428:e=>{e.exports=import("axios")},1680:e=>{e.exports=require("@restart/ui/utils")},1919:e=>{e.exports=require("react-transition-group/Transition")},3892:e=>{e.exports=require("classnames")},4048:e=>{e.exports=require("react-truncate")},6009:e=>{e.exports=require("dom-helpers/ownerDocument")},6952:e=>{e.exports=require("@restart/ui/Modal")},7374:e=>{e.exports=require("@restart/ui/Dropdown")},8732:e=>{e.exports=require("react/jsx-runtime")},9653:e=>{e.exports=require("@restart/ui/Overlay")},11242:e=>{e.exports=require("redux-persist/integration/react")},11688:e=>{e.exports=require("@restart/hooks/useWillUnmount")},12388:e=>{e.exports=require("dom-helpers/addEventListener")},13364:e=>{e.exports=require("redux-saga/effects")},14062:e=>{e.exports=import("react-redux")},14078:e=>{e.exports=import("swr")},14332:e=>{e.exports=require("uncontrollable")},16116:e=>{e.exports=require("invariant")},18622:e=>{e.exports=require("yup")},19121:e=>{e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},22326:e=>{e.exports=require("react-dom")},22541:e=>{e.exports=require("redux-persist/lib/storage")},25303:e=>{e.exports=require("@restart/ui/NavItem")},26324:e=>{e.exports=require("warning")},27825:e=>{e.exports=require("lodash")},27910:e=>{e.exports=require("stream")},28217:e=>{e.exports=require("@restart/ui/DropdownItem")},29021:e=>{e.exports=require("fs")},29294:e=>{e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},29628:e=>{e.exports=require("dom-helpers/addClass")},29825:e=>{e.exports=require("prop-types")},29841:e=>{e.exports=require("dom-helpers/hasClass")},30362:e=>{e.exports=require("@restart/ui/DropdownToggle")},33873:e=>{e.exports=require("path")},36653:e=>{e.exports=require("nprogress")},36955:e=>{e.exports=require("@restart/hooks/useBreakpoint")},37766:e=>{e.exports=require("@restart/hooks/useMergedRefs")},38609:e=>{e.exports=require("react-data-table-component")},39756:e=>{e.exports=import("redux")},39982:(e,r,t)=>{t.a(e,async(e,s)=>{try{t.r(r),t.d(r,{default:()=>S});var a=t(8732),o=t(82015),i=t(19918),n=t.n(i),u=t(27825),l=t.n(u),p=t(44233),d=t(74716),c=t.n(d),x=t(56084),q=t(63487),g=t(44164),h=t(88751),m=e([q,g]);[q,g]=m.then?(await m)():m;let y=({partners:e})=>e&&e.length>0?(0,a.jsx)("ul",{children:e.map((e,r)=>{if(e.institution)return(0,a.jsx)("li",{children:(0,a.jsx)(n(),{href:"/institution/[...routes]",as:`/institution/show/${e.institution._id}`,children:e.institution.title})},r)})}):null,S=function(e){let{t:r}=(0,h.useTranslation)("common"),t=(0,p.useRouter)(),{setOperations:s,selectedRegions:i}=e,[u,d]=(0,o.useState)(""),[m,S]=(0,o.useState)(""),[P,A]=(0,o.useState)(!1),[f,b]=(0,o.useState)([]),[_,v]=(0,o.useState)(!1),[w,j]=(0,o.useState)(0),[M,C]=(0,o.useState)(10),[T,E]=(0,o.useState)(1),[R,k]=(0,o.useState)(null),D={sort:{created_at:"desc"},lean:!0,populate:[{path:"partners.status",select:"title"},{path:"partners.institution",select:"title"},{path:"status",select:"title"},{path:"country",select:"coordinates"}],limit:M,page:1,query:{},select:"-timeline -region -hazard -description -end_date -syndrome -hazard_type -created_at -updated_at"},[O,N]=(0,o.useState)(D),G=[{name:r("Operations"),selector:"title",sortable:!0,cell:e=>(0,a.jsx)(n(),{href:"/operation/[...routes]",as:`/operation/show/${e._id}`,children:e.title})},{name:r("Status"),selector:"status",sortable:!0,cell:e=>e.status&&e.status.title?e.status.title:""},{name:r("StartDate"),selector:"start_date",sortable:!0,cell:e=>e&&e.start_date?c()(e.start_date).format("M/D/Y"):""},{name:r("Partners"),selector:"partners",cell:e=>(0,a.jsx)(y,{partners:e.partners})}],I=async e=>{v(!0),t.query&&t.query.country&&(e.query.country=t.query.country),null===i?delete e.query.world_region:0===i.length?e.query.world_region=["__NO_MATCH__"]:e.query.world_region=i;let r=await q.A.get("/operation",e);r&&Array.isArray(r.data)&&(b(r.data),s(r.data),j(r.totalCount),v(!1))},H=async(e,r)=>{D.limit=e,D.page=r,v(!0),t.query&&t.query.country&&(D.query.country=t.query.country),null===i?delete D.query.world_region:0===i.length?D.query.world_region=["__NO_MATCH__"]:D.query.world_region=i,m&&(D.query={...D.query,status:m}),R&&(D.sort=R.sort);let a=await q.A.get("/operation",D);a&&Array.isArray(a.data)&&(b(a.data),s(a.data),C(e),v(!1)),E(r)},z=async(e,r)=>{v(!0),D.sort={[e.selector]:r},m&&(D.query={...D.query,status:m}),""!==u&&(D.query={...D.query,title:u}),await I(D),k(D),v(!1)},U=(e,r)=>{e?(O.query.title=e,O.page=r):delete O.query.title,N({...O})},W=(0,o.useRef)(l().debounce((e,r)=>U(e,r),Number("500")||300)).current,F=(0,o.useMemo)(()=>{let e=e=>{S(e),e?(O.query.status=e,O.page=T):delete O.query.status,N({...O})};return(0,a.jsx)(g.default,{onFilter:e=>{d(e.target.value),W(e.target.value,T)},onFilterStatusChange:r=>e(r.target.value),onClear:()=>{u&&(A(!P),d(""))},filterText:u,filterStatus:m})},[u,m,P,i]);return(0,a.jsx)(x.A,{columns:G,data:f,totalRows:w,loading:_,subheader:!0,persistTableHead:!0,onSort:z,sortServer:!0,pagServer:!0,resetPaginationToggle:P,subHeaderComponent:F,handlePerRowsChange:H,handlePageChange:e=>{D.limit=M,D.page=e,""!==u&&(D.query={title:u}),m&&(D.query={...D.query,status:m}),R&&(D.sort=R.sort),I(D),E(e)}})};s()}catch(e){s(e)}})},40051:e=>{e.exports=require("dom-helpers/removeClass")},40361:e=>{e.exports=require("next/dist/compiled/next-server/pages.runtime.prod.js")},42893:e=>{e.exports=import("react-hot-toast")},43294:e=>{e.exports=require("formik")},44164:(e,r,t)=>{t.a(e,async(e,s)=>{try{t.r(r),t.d(r,{default:()=>q});var a=t(8732),o=t(82015),i=t(7082),n=t(83551),u=t(49481),l=t(84517),p=t(59549),d=t(63487),c=t(88751),x=e([d]);d=(x.then?(await x)():x)[0];let q=({filterText:e,onFilter:r,onFilterStatusChange:t,onClear:s,filterStatus:x})=>{let[q,g]=(0,o.useState)([]),{t:h}=(0,c.useTranslation)("common"),m=async e=>{let r=await d.A.get("/operation_status",e);r&&Array.isArray(r.data)&&g(r.data)};return(0,o.useEffect)(()=>{m({query:{},sort:{title:"asc"}})},[]),(0,a.jsx)(i.A,{fluid:!0,className:"p-0",children:(0,a.jsxs)(n.A,{children:[(0,a.jsx)(u.A,{xs:6,className:"ps-0 align-self-end mb-3",children:(0,a.jsx)(l.A,{type:"text",className:"searchInput",placeholder:h("search"),"aria-label":"Search",value:e,onChange:r})}),(0,a.jsx)(u.A,{xs:6,children:(0,a.jsx)(p.A,{children:(0,a.jsxs)(p.A.Group,{as:n.A,controlId:"statusFilter",children:[(0,a.jsx)(p.A.Label,{column:!0,sm:"3",lg:"2",children:h("Status")}),(0,a.jsx)(u.A,{className:"ps-0 pe-1",children:(0,a.jsxs)(l.A,{as:"select","aria-label":"Status",onChange:t,value:x,children:[(0,a.jsx)("option",{value:"",children:"All"}),q.map((e,r)=>(0,a.jsx)("option",{value:e._id,children:e.title},r))]})})]})})})]})})};s()}catch(e){s(e)}})},48893:(e,r,t)=>{t.a(e,async(e,s)=>{try{t.r(r),t.d(r,{config:()=>h,default:()=>c,getServerSideProps:()=>g,getStaticPaths:()=>q,getStaticProps:()=>x,reportWebVitals:()=>m,routeModule:()=>b,unstable_getServerProps:()=>A,unstable_getServerSideProps:()=>f,unstable_getStaticParams:()=>P,unstable_getStaticPaths:()=>S,unstable_getStaticProps:()=>y});var a=t(63885),o=t(80237),i=t(81413),n=t(9616),u=t.n(n),l=t(72386),p=t(39982),d=e([l,p]);[l,p]=d.then?(await d)():d;let c=(0,i.M)(p,"default"),x=(0,i.M)(p,"getStaticProps"),q=(0,i.M)(p,"getStaticPaths"),g=(0,i.M)(p,"getServerSideProps"),h=(0,i.M)(p,"config"),m=(0,i.M)(p,"reportWebVitals"),y=(0,i.M)(p,"unstable_getStaticProps"),S=(0,i.M)(p,"unstable_getStaticPaths"),P=(0,i.M)(p,"unstable_getStaticParams"),A=(0,i.M)(p,"unstable_getServerProps"),f=(0,i.M)(p,"unstable_getServerSideProps"),b=new a.PagesRouteModule({definition:{kind:o.A.PAGES,page:"/operation/OperationsTable",pathname:"/operation/OperationsTable",bundlePath:"",filename:""},components:{App:l.default,Document:u()},userland:p});s()}catch(e){s(e)}})},50009:e=>{e.exports=require("@restart/ui/DropdownMenu")},56084:(e,r,t)=>{t.d(r,{A:()=>l});var s=t(8732);t(82015);var a=t(38609),o=t.n(a),i=t(88751),n=t(30370);function u(e){let{t:r}=(0,i.useTranslation)("common"),t={rowsPerPageText:r("Rowsperpage")},{columns:a,data:u,totalRows:l,resetPaginationToggle:p,subheader:d,subHeaderComponent:c,handlePerRowsChange:x,handlePageChange:q,rowsPerPage:g,defaultRowsPerPage:h,selectableRows:m,loading:y,pagServer:S,onSelectedRowsChange:P,clearSelectedRows:A,sortServer:f,onSort:b,persistTableHead:_,sortFunction:v,...w}=e,j={paginationComponentOptions:t,noDataComponent:r("NoData"),noHeader:!0,columns:a,data:u||[],dense:!0,paginationResetDefaultPage:p,subHeader:d,progressPending:y,subHeaderComponent:c,pagination:!0,paginationServer:S,paginationPerPage:h||10,paginationRowsPerPageOptions:g||[10,15,20,25,30],paginationTotalRows:l,onChangeRowsPerPage:x,onChangePage:q,selectableRows:m,onSelectedRowsChange:P,clearSelectedRows:A,progressComponent:(0,s.jsx)(n.A,{}),sortIcon:(0,s.jsx)("i",{className:"sort-icon fas fa-exchange-alt"}),sortServer:f,onSort:b,sortFunction:v,persistTableHead:_,className:"rki-table"};return(0,s.jsx)(o(),{...j})}u.defaultProps={subHeader:!1,pagination:!0,totalRows:null,pagServer:!0,onSelectedRowsChange:null,clearSelectedRows:!1,sortServer:!1,persistTableHead:!1};let l=u},57664:e=>{e.exports=require("dom-helpers/scrollbarSize")},58928:e=>{e.exports=require("dom-helpers/canUseDOM")},59717:e=>{e.exports=require("@restart/ui/DropdownContext")},60560:e=>{e.exports=require("dom-helpers/contains")},63033:e=>{e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},65447:e=>{e.exports=require("@restart/ui/Button")},67364:e=>{e.exports=require("@restart/hooks/useCallbackRef")},68455:e=>{e.exports=import("redux-saga")},69722:e=>{e.exports=require("es6-promise")},74075:e=>{e.exports=require("zlib")},74716:e=>{e.exports=require("moment")},74987:e=>{e.exports=require("@restart/ui/ModalManager")},78097:e=>{e.exports=require("next-redux-saga")},78634:e=>{e.exports=require("@restart/ui/Anchor")},80237:(e,r)=>{Object.defineProperty(r,"A",{enumerable:!0,get:function(){return t}});var t=function(e){return e.PAGES="PAGES",e.PAGES_API="PAGES_API",e.APP_PAGE="APP_PAGE",e.APP_ROUTE="APP_ROUTE",e.IMAGE="IMAGE",e}({})},81366:e=>{e.exports=require("redux-auth-wrapper/connectedAuthWrapper")},81413:(e,r)=>{Object.defineProperty(r,"M",{enumerable:!0,get:function(){return function e(r,t){return t in r?r[t]:"then"in r&&"function"==typeof r.then?r.then(r=>e(r,t)):"function"==typeof r&&"default"===t?r:void 0}}})},81521:e=>{e.exports=require("dom-helpers/querySelectorAll")},81895:e=>{e.exports=require("@restart/hooks/useEventCallback")},82015:e=>{e.exports=require("react")},86842:e=>{e.exports=require("@restart/ui/SelectableContext")},87571:e=>{e.exports=require("dom-helpers/transitionEnd")},88751:e=>{e.exports=require("next-i18next")},93787:e=>{e.exports=require("redux-persist")},94947:e=>{e.exports=require("@restart/hooks/useTimeout")},96196:e=>{e.exports=require("next-redux-wrapper")},98320:e=>{e.exports=require("@restart/hooks/useIsomorphicEffect")},99460:e=>{e.exports=require("dom-helpers/css")}};var r=require("../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[6089,9216,9616,2386],()=>t(48893));module.exports=s})();