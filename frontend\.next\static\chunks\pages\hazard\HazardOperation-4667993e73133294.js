(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[2058],{29335:(e,a,r)=>{"use strict";r.d(a,{A:()=>C});var s=r(15039),t=r.n(s),d=r(14232),l=r(77346),c=r(37876);let o=d.forwardRef((e,a)=>{let{className:r,bsPrefix:s,as:d="div",...o}=e;return s=(0,l.oU)(s,"card-body"),(0,c.jsx)(d,{ref:a,className:t()(r,s),...o})});o.displayName="CardBody";let n=d.forwardRef((e,a)=>{let{className:r,bsPrefix:s,as:d="div",...o}=e;return s=(0,l.oU)(s,"card-footer"),(0,c.jsx)(d,{ref:a,className:t()(r,s),...o})});n.displayName="CardFooter";var i=r(81764);let m=d.forwardRef((e,a)=>{let{bsPrefix:r,className:s,as:o="div",...n}=e,m=(0,l.oU)(r,"card-header"),f=(0,d.useMemo)(()=>({cardHeaderBsPrefix:m}),[m]);return(0,c.jsx)(i.A.Provider,{value:f,children:(0,c.jsx)(o,{ref:a,...n,className:t()(s,m)})})});m.displayName="CardHeader";let f=d.forwardRef((e,a)=>{let{bsPrefix:r,className:s,variant:d,as:o="img",...n}=e,i=(0,l.oU)(r,"card-img");return(0,c.jsx)(o,{ref:a,className:t()(d?"".concat(i,"-").concat(d):i,s),...n})});f.displayName="CardImg";let u=d.forwardRef((e,a)=>{let{className:r,bsPrefix:s,as:d="div",...o}=e;return s=(0,l.oU)(s,"card-img-overlay"),(0,c.jsx)(d,{ref:a,className:t()(r,s),...o})});u.displayName="CardImgOverlay";let N=d.forwardRef((e,a)=>{let{className:r,bsPrefix:s,as:d="a",...o}=e;return s=(0,l.oU)(s,"card-link"),(0,c.jsx)(d,{ref:a,className:t()(r,s),...o})});N.displayName="CardLink";var x=r(46052);let h=(0,x.A)("h6"),p=d.forwardRef((e,a)=>{let{className:r,bsPrefix:s,as:d=h,...o}=e;return s=(0,l.oU)(s,"card-subtitle"),(0,c.jsx)(d,{ref:a,className:t()(r,s),...o})});p.displayName="CardSubtitle";let j=d.forwardRef((e,a)=>{let{className:r,bsPrefix:s,as:d="p",...o}=e;return s=(0,l.oU)(s,"card-text"),(0,c.jsx)(d,{ref:a,className:t()(r,s),...o})});j.displayName="CardText";let y=(0,x.A)("h5"),w=d.forwardRef((e,a)=>{let{className:r,bsPrefix:s,as:d=y,...o}=e;return s=(0,l.oU)(s,"card-title"),(0,c.jsx)(d,{ref:a,className:t()(r,s),...o})});w.displayName="CardTitle";let v=d.forwardRef((e,a)=>{let{bsPrefix:r,className:s,bg:d,text:n,border:i,body:m=!1,children:f,as:u="div",...N}=e,x=(0,l.oU)(r,"card");return(0,c.jsx)(u,{ref:a,...N,className:t()(s,x,d&&"bg-".concat(d),n&&"text-".concat(n),i&&"border-".concat(i)),children:m?(0,c.jsx)(o,{children:f}):f})});v.displayName="Card";let C=Object.assign(v,{Img:f,Title:w,Subtitle:p,Body:o,Link:N,Text:j,Header:m,Footer:n,ImgOverlay:u})},50457:(e,a,r)=>{"use strict";r.r(a),r.d(a,{default:()=>o});var s=r(37876),t=r(48230),d=r.n(t),l=r(29335),c=r(31753);let o=e=>{let a=e.hazardOperationData,{t:r}=(0,c.Bd)("common");return(0,s.jsx)(s.Fragment,{children:(0,s.jsx)("div",{className:"rki-carousel-card",children:(0,s.jsxs)(l.A,{className:"infoCard",children:[(0,s.jsx)(l.A.Header,{className:"text-center",children:r("hazardshow.currentoperations")}),(0,s.jsx)(l.A.Body,{className:"hazardBody",children:a&&a.length>0?a.map((e,a)=>(0,s.jsx)("ul",{className:"ulItems",children:(0,s.jsxs)("li",{className:"liItems",children:[(0,s.jsx)(d(),{href:"/operation/[...routes]",as:"/operation/show/".concat(e._id),children:e&&e.title?"".concat(e.title):""},e._id),(0,s.jsxs)("span",{children:[" ","(",e&&e.country?"".concat(e.country.title):"",")"]})]},a)})):(0,s.jsx)("span",{className:"text-center",children:r("noSourceFound")})})]})})})}},81764:(e,a,r)=>{"use strict";r.d(a,{A:()=>t});let s=r(14232).createContext(null);s.displayName="CardHeaderContext";let t=s},83844:(e,a,r)=>{(window.__NEXT_P=window.__NEXT_P||[]).push(["/hazard/HazardOperation",function(){return r(50457)}])}},e=>{var a=a=>e(e.s=a);e.O(0,[636,6593,8792],()=>a(83844)),_N_E=e.O()}]);
//# sourceMappingURL=HazardOperation-4667993e73133294.js.map