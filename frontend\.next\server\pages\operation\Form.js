"use strict";(()=>{var e={};e.id=6364,e.ids=[636,3220,6364],e.modules={123:e=>{e.exports=require("dom-helpers/removeEventListener")},1332:e=>{e.exports=require("react-custom-scrollbars-2")},1428:e=>{e.exports=import("axios")},1680:e=>{e.exports=require("@restart/ui/utils")},1919:e=>{e.exports=require("react-transition-group/Transition")},3892:e=>{e.exports=require("classnames")},4048:e=>{e.exports=require("react-truncate")},6009:e=>{e.exports=require("dom-helpers/ownerDocument")},6952:e=>{e.exports=require("@restart/ui/Modal")},7374:e=>{e.exports=require("@restart/ui/Dropdown")},8732:e=>{e.exports=require("react/jsx-runtime")},9532:e=>{e.exports=require("@restart/ui/Nav")},9653:e=>{e.exports=require("@restart/ui/Overlay")},11e3:e=>{e.exports=require("react-multi-select-component")},11242:e=>{e.exports=require("redux-persist/integration/react")},11688:e=>{e.exports=require("@restart/hooks/useWillUnmount")},12388:e=>{e.exports=require("dom-helpers/addEventListener")},13364:e=>{e.exports=require("redux-saga/effects")},14062:e=>{e.exports=import("react-redux")},14078:e=>{e.exports=import("swr")},14332:e=>{e.exports=require("uncontrollable")},16029:e=>{e.exports=require("react-dropzone")},16116:e=>{e.exports=require("invariant")},18622:e=>{e.exports=require("yup")},19121:e=>{e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},20603:(e,r,t)=>{t.a(e,async(e,s)=>{try{t.r(r),t.d(r,{config:()=>g,default:()=>q,getServerSideProps:()=>m,getStaticPaths:()=>c,getStaticProps:()=>d,reportWebVitals:()=>h,routeModule:()=>M,unstable_getServerProps:()=>f,unstable_getServerSideProps:()=>P,unstable_getStaticParams:()=>S,unstable_getStaticPaths:()=>b,unstable_getStaticProps:()=>v});var o=t(63885),i=t(80237),a=t(81413),p=t(9616),u=t.n(p),x=t(72386),n=t(41522),l=e([x,n]);[x,n]=l.then?(await l)():l;let q=(0,a.M)(n,"default"),d=(0,a.M)(n,"getStaticProps"),c=(0,a.M)(n,"getStaticPaths"),m=(0,a.M)(n,"getServerSideProps"),g=(0,a.M)(n,"config"),h=(0,a.M)(n,"reportWebVitals"),v=(0,a.M)(n,"unstable_getStaticProps"),b=(0,a.M)(n,"unstable_getStaticPaths"),S=(0,a.M)(n,"unstable_getStaticParams"),f=(0,a.M)(n,"unstable_getServerProps"),P=(0,a.M)(n,"unstable_getServerSideProps"),M=new o.PagesRouteModule({definition:{kind:i.A.PAGES,page:"/operation/Form",pathname:"/operation/Form",bundlePath:"",filename:""},components:{App:x.default,Document:u()},userland:n});s()}catch(e){s(e)}})},21964:e=>{e.exports=require("@restart/ui/TabPanel")},22326:e=>{e.exports=require("react-dom")},22541:e=>{e.exports=require("redux-persist/lib/storage")},24047:(e,r,t)=>{t.d(r,{x:()=>a});var s=t(8732),o=t(82015);let i=({value:e,onChange:r,placeholder:t="Write something...",height:i=300,disabled:a=!1})=>{let p=(0,o.useRef)(null),[u,x]=(0,o.useState)(!1);return(0,o.useEffect)(()=>{p.current},[e,u]),(0,s.jsx)("div",{className:"simple-rich-text-editor",style:{border:"1px solid #ccc"},children:!1})},a=e=>{let{initContent:r,onChange:t}=e;return(0,s.jsx)(i,{value:r||"",onChange:e=>t(e)})}},25303:e=>{e.exports=require("@restart/ui/NavItem")},26324:e=>{e.exports=require("warning")},27825:e=>{e.exports=require("lodash")},27910:e=>{e.exports=require("stream")},28217:e=>{e.exports=require("@restart/ui/DropdownItem")},29021:e=>{e.exports=require("fs")},29294:e=>{e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},29628:e=>{e.exports=require("dom-helpers/addClass")},29780:e=>{e.exports=require("react-datepicker")},29825:e=>{e.exports=require("prop-types")},29841:e=>{e.exports=require("dom-helpers/hasClass")},30362:e=>{e.exports=require("@restart/ui/DropdownToggle")},33873:e=>{e.exports=require("path")},36653:e=>{e.exports=require("nprogress")},36955:e=>{e.exports=require("@restart/hooks/useBreakpoint")},37766:e=>{e.exports=require("@restart/hooks/useMergedRefs")},39756:e=>{e.exports=import("redux")},40051:e=>{e.exports=require("dom-helpers/removeClass")},40361:e=>{e.exports=require("next/dist/compiled/next-server/pages.runtime.prod.js")},42893:e=>{e.exports=import("react-hot-toast")},43294:e=>{e.exports=require("formik")},50009:e=>{e.exports=require("@restart/ui/DropdownMenu")},54131:e=>{e.exports=import("@fortawesome/free-solid-svg-icons")},57664:e=>{e.exports=require("dom-helpers/scrollbarSize")},58928:e=>{e.exports=require("dom-helpers/canUseDOM")},59717:e=>{e.exports=require("@restart/ui/DropdownContext")},60560:e=>{e.exports=require("dom-helpers/contains")},63033:e=>{e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},65209:e=>{e.exports=require("@restart/ui/TabContext")},65447:e=>{e.exports=require("@restart/ui/Button")},67364:e=>{e.exports=require("@restart/hooks/useCallbackRef")},68455:e=>{e.exports=import("redux-saga")},69722:e=>{e.exports=require("es6-promise")},70947:e=>{e.exports=require("@restart/ui/Tabs")},74075:e=>{e.exports=require("zlib")},74716:e=>{e.exports=require("moment")},74987:e=>{e.exports=require("@restart/ui/ModalManager")},78097:e=>{e.exports=require("next-redux-saga")},78634:e=>{e.exports=require("@restart/ui/Anchor")},80860:e=>{e.exports=require("@restart/ui/NoopTransition")},81366:e=>{e.exports=require("redux-auth-wrapper/connectedAuthWrapper")},81521:e=>{e.exports=require("dom-helpers/querySelectorAll")},81895:e=>{e.exports=require("@restart/hooks/useEventCallback")},82015:e=>{e.exports=require("react")},82053:e=>{e.exports=require("@fortawesome/react-fontawesome")},86842:e=>{e.exports=require("@restart/ui/SelectableContext")},87571:e=>{e.exports=require("dom-helpers/transitionEnd")},88751:e=>{e.exports=require("next-i18next")},93787:e=>{e.exports=require("redux-persist")},94947:e=>{e.exports=require("@restart/hooks/useTimeout")},96196:e=>{e.exports=require("next-redux-wrapper")},98320:e=>{e.exports=require("@restart/hooks/useIsomorphicEffect")},99460:e=>{e.exports=require("dom-helpers/css")}};var r=require("../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[6089,9216,9616,2386,5387,1522],()=>t(20603));module.exports=s})();