(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[1154],{28500:(e,a,r)=>{(window.__NEXT_P=window.__NEXT_P||[]).push(["/institution/InstitutionShow",function(){return r(90627)}])},29335:(e,a,r)=>{"use strict";r.d(a,{A:()=>_});var t=r(15039),s=r.n(t),d=r(14232),l=r(77346),o=r(37876);let i=d.forwardRef((e,a)=>{let{className:r,bsPrefix:t,as:d="div",...i}=e;return t=(0,l.oU)(t,"card-body"),(0,o.jsx)(d,{ref:a,className:s()(r,t),...i})});i.displayName="CardBody";let c=d.forwardRef((e,a)=>{let{className:r,bsPrefix:t,as:d="div",...i}=e;return t=(0,l.oU)(t,"card-footer"),(0,o.jsx)(d,{ref:a,className:s()(r,t),...i})});c.displayName="CardFooter";var n=r(81764);let f=d.forwardRef((e,a)=>{let{bsPrefix:r,className:t,as:i="div",...c}=e,f=(0,l.oU)(r,"card-header"),m=(0,d.useMemo)(()=>({cardHeaderBsPrefix:f}),[f]);return(0,o.jsx)(n.A.Provider,{value:m,children:(0,o.jsx)(i,{ref:a,...c,className:s()(t,f)})})});f.displayName="CardHeader";let m=d.forwardRef((e,a)=>{let{bsPrefix:r,className:t,variant:d,as:i="img",...c}=e,n=(0,l.oU)(r,"card-img");return(0,o.jsx)(i,{ref:a,className:s()(d?"".concat(n,"-").concat(d):n,t),...c})});m.displayName="CardImg";let u=d.forwardRef((e,a)=>{let{className:r,bsPrefix:t,as:d="div",...i}=e;return t=(0,l.oU)(t,"card-img-overlay"),(0,o.jsx)(d,{ref:a,className:s()(r,t),...i})});u.displayName="CardImgOverlay";let N=d.forwardRef((e,a)=>{let{className:r,bsPrefix:t,as:d="a",...i}=e;return t=(0,l.oU)(t,"card-link"),(0,o.jsx)(d,{ref:a,className:s()(r,t),...i})});N.displayName="CardLink";var x=r(46052);let w=(0,x.A)("h6"),y=d.forwardRef((e,a)=>{let{className:r,bsPrefix:t,as:d=w,...i}=e;return t=(0,l.oU)(t,"card-subtitle"),(0,o.jsx)(d,{ref:a,className:s()(r,t),...i})});y.displayName="CardSubtitle";let p=d.forwardRef((e,a)=>{let{className:r,bsPrefix:t,as:d="p",...i}=e;return t=(0,l.oU)(t,"card-text"),(0,o.jsx)(d,{ref:a,className:s()(r,t),...i})});p.displayName="CardText";let C=(0,x.A)("h5"),v=d.forwardRef((e,a)=>{let{className:r,bsPrefix:t,as:d=C,...i}=e;return t=(0,l.oU)(t,"card-title"),(0,o.jsx)(d,{ref:a,className:s()(r,t),...i})});v.displayName="CardTitle";let j=d.forwardRef((e,a)=>{let{bsPrefix:r,className:t,bg:d,text:c,border:n,body:f=!1,children:m,as:u="div",...N}=e,x=(0,l.oU)(r,"card");return(0,o.jsx)(u,{ref:a,...N,className:s()(t,x,d&&"bg-".concat(d),c&&"text-".concat(c),n&&"border-".concat(n)),children:f?(0,o.jsx)(i,{children:m}):m})});j.displayName="Card";let _=Object.assign(j,{Img:m,Title:v,Subtitle:y,Body:i,Link:N,Text:p,Header:f,Footer:c,ImgOverlay:u})},81764:(e,a,r)=>{"use strict";r.d(a,{A:()=>s});let t=r(14232).createContext(null);t.displayName="CardHeaderContext";let s=t}},e=>{var a=a=>e(e.s=a);e.O(0,[7725,1772,7126,8477,7308,9810,5293,636,6593,8792],()=>a(28500)),_N_E=e.O()}]);
//# sourceMappingURL=InstitutionShow-5b67f4486f193a0a.js.map