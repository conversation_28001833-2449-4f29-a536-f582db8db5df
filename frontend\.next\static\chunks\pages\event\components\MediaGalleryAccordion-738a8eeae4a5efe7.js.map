{"version": 3, "file": "static/chunks/pages/event/components/MediaGalleryAccordion-738a8eeae4a5efe7.js", "mappings": "4MAmCA,MAzB8B,IAC1B,GAAM,GAAEA,CAAC,CAAE,CAAGC,CAAAA,EAAAA,EAAAA,EAAAA,CAAcA,CAAC,MAwBlBC,IAvBL,CAACC,EAASC,EAAW,CAAGC,CAAAA,EAAAA,EAAAA,MAuBEH,EAvBFG,CAAQA,EAAC,GAEvC,MACI,+BACI,WAACC,EAAAA,CAASA,CAACC,IAAI,EAACC,SAAS,cACrB,WAACF,EAAAA,CAASA,CAACG,MAAM,EAACC,QAAS,IAAMN,EAAW,CAACD,aACzC,UAACQ,MAAAA,CAAIC,UAAU,qBAAaZ,EAAE,8BAC9B,UAACW,MAAAA,CAAIC,UAAU,qBACVT,EACG,UAACU,EAAAA,CAAeA,CAAAA,CAACC,KAAMC,EAAAA,GAAOA,CAAEC,MAAM,SAEtC,UAACH,EAAAA,CAAeA,CAAAA,CAACC,KAAMG,EAAAA,GAAMA,CAAED,MAAM,cAIjD,UAACV,EAAAA,CAASA,CAACY,IAAI,WACX,UAACC,EAAAA,CAAWA,CAAAA,CAACC,QAASC,EAAMC,MAAM,CAAEC,YAAaF,EAAMG,UAAU,SAKrF,2ICsIA,MAnIoB,IAClB,GAAM,GAAExB,CAAC,CAAE,CAAGC,CAAAA,EAAAA,EAAAA,EAAAA,CAAcA,CAAC,MAkIhBkB,IAjIP,CAACG,EAAQG,EAAU,CAAGpB,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAc,EAAE,EAG9CqB,EAAoB,IACxB,IAAMC,EAAc,8EAA8EC,IAAI,CAACC,EAAKC,WAAW,EAEvH,MACE,WAACnB,MAAAA,CAAIC,UAAU,4BACb,WAACmB,IAAAA,CAAEnB,UAAU,iBACX,UAACoB,IAAAA,UAAGhC,EAAE,cAAgB,IAAE6B,EAAKI,YAAY,EAAI,mBAE9CJ,EAAKC,WAAW,EACf,WAACnB,MAAAA,CAAIC,UAAU,wBACb,UAACmB,IAAAA,UAAE,UAACC,IAAAA,UAAGhC,EAAE,cACR2B,EACC,WAAChB,MAAAA,WACC,UAACE,EAAAA,CAAeA,CAAAA,CAACC,KAAMoB,EAAAA,GAAMA,CAAEC,KAAK,KAAKnB,MAAM,OAAOJ,UAAU,SAChE,UAACwB,IAAAA,CAAExB,UAAU,cAAcyB,KAAMR,EAAKC,WAAW,CAAEQ,OAAO,SAASC,IAAI,+BACpEV,EAAKC,WAAW,MAIrB,UAACnB,MAAAA,UACC,UAACoB,IAAAA,CAAEnB,UAAU,YAAY4B,MAAO,CAAEC,UAAW,WAAY,WACtDZ,EAAKC,WAAW,QAM1BD,EAAKa,YAAY,EAChB,WAACC,EAAAA,CAAMA,CAAAA,CAAC/B,UAAU,qCAAqCyB,KAAMR,EAAKa,YAAY,WAC3E1C,EAAE,YACH,UAACa,EAAAA,CAAeA,CAAAA,CAACC,KAAM8B,EAAAA,GAAUA,CAAET,KAAK,KAAKvB,UAAU,cAKjE,EA6CA,MA3CAiC,CAAAA,EAAAA,EAAAA,SAAAA,CAASA,CAAC,KACR,IAAMC,EAA8B,EAAE,CACtCzB,GAASA,EAAMD,OAAO,EAAI2B,MAAMC,OAAO,CAAC3B,EAAMD,OAAO,GAAKC,EAAMD,OAAO,CAAC6B,GAAG,CAAC,CAACpB,EAAMqB,KACjF,IACIC,EADEC,EAAWvB,GAAQA,EAAKwB,IAAI,CAACC,KAAK,CAAC,KAAKC,GAAG,GAGjD,OAAQH,GACN,IAAK,MACL,IAAK,MACL,IAAK,OACL,IAAK,MACHD,EAAS,GAAwCtB,MAAAA,CAArC2B,8BAAsB,CAAC,gBAAuB,OAAT3B,EAAK4B,GAAG,EACzD,KACF,KAAK,MACHN,EAAS,gCACT,KACF,KAAK,OACHA,EAAS,iCACT,KACF,KAAK,MACL,IAAK,OACHA,EAAS,gCACT,KACF,SACEA,EAAS,iCACb,CAEA,IAAMO,EAAQ,CAAc,SAAbN,GAAoC,QAAbA,GAAmC,QAAbA,GAAmC,SAAbA,CAAa,CAAK,EAC/F,GAA4CvB,MAAAA,CAAzC2B,8BAAsB,CAAC,oBAA2B,OAAT3B,EAAK4B,GAAG,EACnDE,EAAQ,GAAqE,OAAlE9B,GAAQA,EAAK+B,aAAa,CAAG/B,EAAK+B,aAAa,CAAG,iBAC7DC,EAAexC,EAAME,WAAW,EAAIwB,MAAMC,OAAO,CAAC3B,EAAME,WAAW,GACpEF,EAAME,WAAW,CAACuC,MAAM,CAAG,EAAIzC,EAAME,WAAW,CAAC2B,EAAE,CAAG,GAE3DJ,EAAeiB,IAAI,CAAC,CAClBC,IAAKb,EACLrB,YAAa+B,EACb5B,aAAc0B,EACdjB,aAAcgB,CAChB,EACF,GACAjC,EAAUqB,EACZ,EAAG,CAACzB,EAAM,EAGR,UAACV,MAAAA,UACEW,GAA4B,IAAlBA,EAAOwC,MAAM,CACtB,UAACnD,MAAAA,CAAIC,UAAU,wCACb,UAACmB,IAAAA,CAAEnB,UAAU,wDAAgDZ,EAAE,qBAGjE,UAACiE,EAAAA,EAAQA,CAAAA,CACPC,YAAY,EACZC,YAAY,EACZC,gBAAgB,EAChBC,cAAc,EACdC,mBAAmB,EACnBC,UAAU,EACVC,YAAa,GACbC,WAAW,EACXC,eAAe,EACfC,cAAc,EACdC,aAAc,IACZtD,EAAO2B,GAAG,CAAC,CAACpB,EAAMgD,IAChB,UAACC,MAAAA,CAECd,IAAKnC,EAAKmC,GAAG,CACbe,IAAK,aAAuB,OAAVF,EAAQ,GAC1BrC,MAAO,CAAEwC,MAAO,OAAQC,OAAQ,OAAQC,UAAW,OAAQ,GAHtDL,aAQVvD,EAAO2B,GAAG,CAAC,CAACpB,EAAMgD,IACjB,WAAClE,MAAAA,WACC,UAACmE,MAAAA,CACCd,IAAKnC,EAAKmC,GAAG,CACbe,IAAKlD,EAAKI,YAAY,EAAI,gBAC1BO,MAAO,CAAE2C,UAAW,QAASD,UAAW,SAAU,IAEnDxD,EAAkBG,KANXgD,OActB,mBCpKA,4CACA,0CACA,WACA,OAAe,EAAQ,KAA+D,CACtF,EACA,SAFsB", "sources": ["webpack://_N_E/./pages/event/components/MediaGalleryAccordion.tsx", "webpack://_N_E/./components/common/ReactImages.tsx", "webpack://_N_E/?47f6"], "sourcesContent": ["//Import Library\r\nimport React, { useEffect, useState } from \"react\";\r\nimport { faMinus, faPlus } from \"@fortawesome/free-solid-svg-icons\";\r\nimport { FontAwesomeIcon } from \"@fortawesome/react-fontawesome\";\r\nimport { Accordion, Card } from \"react-bootstrap\";\r\n\r\n//Import services/components\r\nimport ReactImages from \"../../../components/common/ReactImages\";\r\nimport { useTranslation } from 'next-i18next';\r\n\r\nconst MediaGalleryAccordion = (props: any) => {\r\n    const { t } = useTranslation('common');\r\n    const [section, setSection] = useState(false);\r\n\r\n    return (\r\n        <>\r\n            <Accordion.Item eventKey=\"0\">\r\n                <Accordion.Header onClick={() => setSection(!section)}>\r\n                    <div className=\"cardTitle\">{t(\"Events.show.MediaGallery\")}</div>\r\n                    <div className=\"cardArrow\">\r\n                        {section ? (\r\n                            <FontAwesomeIcon icon={faMinus} color=\"#fff\" />\r\n                        ) : (\r\n                            <FontAwesomeIcon icon={faPlus} color=\"#fff\" />\r\n                        )}\r\n                    </div>\r\n                </Accordion.Header>\r\n                <Accordion.Body>\r\n                    <ReactImages gallery={props.images} imageSource={props.images_src} />\r\n                </Accordion.Body>\r\n            </Accordion.Item>\r\n        </>\r\n    )\r\n};\r\n\r\nexport default MediaGalleryAccordion;", "\r\n//Import Library\r\nimport React, { useState, useEffect } from 'react';\r\nimport { Carousel } from 'react-responsive-carousel';\r\nimport { FontAwesomeIcon } from \"@fortawesome/react-fontawesome\";\r\nimport {\r\n  faLink, faDownload\r\n} from \"@fortawesome/free-solid-svg-icons\";\r\nimport { Button } from \"react-bootstrap\";\r\n\r\n//Import services/components\r\nimport { useTranslation } from 'next-i18next';\r\n\r\n// Import CSS for react-responsive-carousel\r\nimport \"react-responsive-carousel/lib/styles/carousel.min.css\";\r\n\r\n// Define types for image items\r\ninterface ImageItem {\r\n  src: string;\r\n  description: string;\r\n  originalName: string;\r\n  downloadLink: string | false;\r\n}\r\n\r\ninterface ReactImagesProps {\r\n  gallery?: Array<{\r\n    _id: string;\r\n    name: string;\r\n    original_name?: string;\r\n    src?: string;\r\n    caption?: string;\r\n    alt?: string;\r\n  }> | boolean;\r\n  imageSource?: string[] | boolean;\r\n}\r\n\r\nconst ReactImages = (props: ReactImagesProps) => {\r\n  const { t } = useTranslation('common');\r\n  const [images, setImages] = useState<ImageItem[]>([]);\r\n\r\n  // Render image description and metadata\r\n  const renderImageLegend = (item: ImageItem) => {\r\n    const isValidLink = /(http|https):\\/\\/(\\w+:{0,1}\\w*)?(\\S+)(:[0-9]+)?(\\/|\\/([\\w#!:.?+=&%!\\-\\/]))?/.test(item.description);\r\n\r\n    return (\r\n      <div className=\"carousel-legend\">\r\n        <p className=\"lead\">\r\n          <b>{t(\"Filename\")}</b> {item.originalName || \"No Name found\"}\r\n        </p>\r\n        {item.description && (\r\n          <div className=\"source_link\">\r\n            <p><b>{t(\"Source\")}</b></p>\r\n            {isValidLink ? (\r\n              <div>\r\n                <FontAwesomeIcon icon={faLink} size=\"1x\" color=\"#999\" className=\"me-1\" />\r\n                <a className=\"source_link\" href={item.description} target=\"_blank\" rel=\"noopener noreferrer\">\r\n                  {item.description}\r\n                </a>\r\n              </div>\r\n            ) : (\r\n              <div>\r\n                <p className=\"ps-0 py-0\" style={{ wordBreak: \"break-all\" }}>\r\n                  {item.description}\r\n                </p>\r\n              </div>\r\n            )}\r\n          </div>\r\n        )}\r\n        {item.downloadLink && (\r\n          <Button className=\"btn btn-success mt-2 btn--download\" href={item.downloadLink}>\r\n            {t(\"Download\")}\r\n            <FontAwesomeIcon icon={faDownload} size=\"1x\" className=\"ms-1\" />\r\n          </Button>\r\n        )}\r\n      </div>\r\n    );\r\n  };\r\n\r\n  useEffect(() => {\r\n    const carouselImages: ImageItem[] = [];\r\n    props && props.gallery && Array.isArray(props.gallery) && props.gallery.map((item, i) => {\r\n      const fileType = item && item.name.split('.').pop();\r\n      let imgSrc;\r\n\r\n      switch (fileType) {\r\n        case \"JPG\":\r\n        case \"jpg\":\r\n        case \"jpeg\":\r\n        case \"png\":\r\n          imgSrc = `${process.env.API_SERVER}/image/show/${item._id}`;\r\n          break;\r\n        case \"pdf\":\r\n          imgSrc = \"/images/fileIcons/pdfFile.png\";\r\n          break;\r\n        case \"docx\":\r\n          imgSrc = \"/images/fileIcons/wordFile.png\";\r\n          break;\r\n        case \"xls\":\r\n        case 'xlsx':\r\n          imgSrc = \"/images/fileIcons/xlsFile.png\";\r\n          break;\r\n        default:\r\n          imgSrc = \"/images/fileIcons/otherFile.png\";\r\n      }\r\n\r\n      const _link = (fileType === \"docx\" || fileType === \"pdf\" || fileType === \"xls\" || fileType === \"xlsx\")\r\n        && `${process.env.API_SERVER}/files/download/${item._id}`;\r\n      const _name = `${item && item.original_name ? item.original_name : \"No Name found\"}`;\r\n      const _description = props.imageSource && Array.isArray(props.imageSource)\r\n        && props.imageSource.length > 0 ? props.imageSource[i] : \"\";\r\n\r\n      carouselImages.push({\r\n        src: imgSrc,\r\n        description: _description,\r\n        originalName: _name,\r\n        downloadLink: _link\r\n      });\r\n    });\r\n    setImages(carouselImages);\r\n  }, [props]);\r\n\r\n  return (\r\n    <div>\r\n      {images && images.length === 0 ? (\r\n        <div className=\"border border-info my-3 mx-0\">\r\n          <p className=\"d-flex d-flex justify-content-center p-2 m-0\">{t(\"NoFilesFound!\")}</p>\r\n        </div>\r\n      ) : (\r\n        <Carousel\r\n          showThumbs={true}\r\n          showStatus={true}\r\n          showIndicators={true}\r\n          infiniteLoop={true}\r\n          useKeyboardArrows={true}\r\n          autoPlay={false}\r\n          stopOnHover={true}\r\n          swipeable={true}\r\n          dynamicHeight={false}\r\n          emulateTouch={true}\r\n          renderThumbs={() =>\r\n            images.map((item, index) => (\r\n              <img\r\n                key={index}\r\n                src={item.src}\r\n                alt={`Thumbnail ${index + 1}`}\r\n                style={{ width: '60px', height: '60px', objectFit: 'cover' }}\r\n              />\r\n            ))\r\n          }\r\n        >\r\n          {images.map((item, index) => (\r\n            <div key={index}>\r\n              <img\r\n                src={item.src}\r\n                alt={item.originalName || \"Gallery image\"}\r\n                style={{ maxHeight: '500px', objectFit: 'contain' }}\r\n              />\r\n              {renderImageLegend(item)}\r\n            </div>\r\n          ))}\r\n        </Carousel>\r\n      )}\r\n    </div>\r\n  );\r\n\r\n}\r\n\r\nexport default ReactImages;\r\n", "\n    (window.__NEXT_P = window.__NEXT_P || []).push([\n      \"/event/components/MediaGalleryAccordion\",\n      function () {\n        return require(\"private-next-pages/event/components/MediaGalleryAccordion.tsx\");\n      }\n    ]);\n    if(module.hot) {\n      module.hot.dispose(function () {\n        window.__NEXT_P.push([\"/event/components/MediaGalleryAccordion\"])\n      });\n    }\n  "], "names": ["t", "useTranslation", "MediaGalleryAccordion", "section", "setSection", "useState", "Accordion", "<PERSON><PERSON>", "eventKey", "Header", "onClick", "div", "className", "FontAwesomeIcon", "icon", "faMinus", "color", "faPlus", "Body", "ReactImages", "gallery", "props", "images", "imageSource", "images_src", "setImages", "renderImageLegend", "isValidLink", "test", "item", "description", "p", "b", "originalName", "faLink", "size", "a", "href", "target", "rel", "style", "wordBreak", "downloadLink", "<PERSON><PERSON>", "faDownload", "useEffect", "carouselImages", "Array", "isArray", "map", "i", "imgSrc", "fileType", "name", "split", "pop", "process", "_id", "_link", "_name", "original_name", "_description", "length", "push", "src", "Carousel", "showThumbs", "showStatus", "showIndicators", "infiniteLoop", "useKeyboardArrows", "autoPlay", "stopOnHover", "swipeable", "dynamicHeight", "emulate<PERSON><PERSON><PERSON>", "renderThumbs", "index", "img", "alt", "width", "height", "objectFit", "maxHeight"], "sourceRoot": "", "ignoreList": []}