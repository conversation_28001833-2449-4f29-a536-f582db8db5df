{"version": 3, "file": "static/chunks/9810-4375e8f45567821a.js", "mappings": "wRA8JA,MAtHwCA,IACpC,GAAM,GAAEC,CAAC,CAAE,CAAGC,CAAAA,EAAAA,EAAAA,EAAAA,CAAcA,CAAC,MAqHlBC,IAnHLC,EAAsB,IAEpB,UAACC,IAAIA,CACDC,KAgH6BH,EAAC,yBA/G9BI,GAAI,SAFHF,YAE6C,OAArBL,EAAMQ,IAAI,CAACC,MAAM,CAAC,EAAE,WAE7C,WAACC,EAAAA,CAAMA,CAAAA,CAACC,QAAQ,YAAYC,KAAK,eAC7B,UAACC,EAAAA,CAAeA,CAAAA,CAACC,KAAMC,EAAAA,GAAKA,GAAI,OACzBd,EAAE,aAMnBe,EAAoB,SAMyBhB,EAL/C,MACI,UAACiB,KAAAA,UACG,UAACC,OAAAA,CAAKC,UAAU,2BACZ,UAACd,IAAIA,CACDC,KAAK,2BACLC,GAAI,SAFHF,kBAEqD,aAAvBL,GAAAA,OAAAA,EAAAA,EAAOQ,IAAI,EAAXR,KAAAA,EAAAA,EAAaS,GAAbT,GAAmB,CAAC,EAAE,WAErD,UAACoB,IAAAA,CAAED,UAAU,qBAKjC,EAEME,EAAqBC,CAAAA,EAAAA,EAAAA,kBAAAA,CAAkBA,CAAC,IAAM,UAAClB,EAAAA,CAAAA,IAC/CmB,EAAuBC,CAAAA,EAAAA,EAAAA,oBAAAA,CAAoBA,CAAC,IAAM,UAACR,EAAAA,CAAAA,IAEzD,MACI,+BACI,UAACS,EAAAA,CAASA,CAAAA,CAACC,KAAK,aACZ,WAACC,EAAAA,CAAGA,CAAAA,WAkFpB,SAASC,CACe,CACpB5B,CAAU,CACVqB,CAAuB,CACvBQ,CAAmB,EAEnB,MACI,UAACC,EAAAA,CAAGA,CAAAA,CAACC,GAAI,YACL,WAACC,MAAAA,CAAIb,UAAU,2CACX,WAACc,KAAAA,CAAGd,UAAU,6BACTe,EAAgBC,KAAK,CAAC,YACtBN,GAAc7B,EAAMS,MAAM,EAAIT,EAAMS,MAAM,CAAC,EAAE,CAC1C,UAACY,EAAAA,CAAmBe,YAAaF,IACjC,QAER,UAACG,EAAAA,CAAQA,CAAAA,CAACC,SAAUtC,EAAMS,MAAM,CAAC,EAAE,CAAE8B,WAAW,oBAIhE,EAnGwBvC,EAAMkC,eAAe,CACrBlC,EAAMQ,IAAI,CACVa,EACArB,EAAM6B,UAAU,EAGpB,WAACC,EAAAA,CAAGA,CAAAA,CAACC,GAAI,YACL,UAACC,MAAAA,UACG,UAACQ,EAAAA,OAAaA,CAAAA,CACVC,YAAazC,EAAMkC,eAAe,CAACO,WAAW,KAyI9E,SAASC,CAA+C,CAAER,CAAoB,EAC1E,MACI,UAACF,MAAAA,CAAIb,UAAU,2BACX,WAACwB,KAAAA,WACG,WAAC1B,KAAAA,WACG,WAAC2B,QAAAA,WAAO3C,EAAE,aAAa,OACvB,UAACiB,OAAAA,CAAKC,UAAU,uBAAe,GAEvBe,MAAAA,CAFwD,cAA9BA,EAAgBW,SAAS,EACvDX,EAAgBW,SAAS,CACxB,KAA6B,OAA1BX,EAAgBY,SAAS,OAErC,WAAC7B,KAAAA,WACG,WAAC2B,QAAAA,WAAO3C,EAAE,WAAW,OACrB,WAACiB,OAAAA,CAAKC,UAAU,wBACXe,GAAmBA,EAAgBa,OAAO,CACrCb,EAAgBa,OAAO,CAACC,MAAM,CAC9B,KAAM,IACXd,GAAmBA,EAAgBa,OAAO,CACrCb,EAAgBa,OAAO,CAACE,MAAM,CAC9B,KAAM,IACXf,GAAmBA,EAAgBa,OAAO,CACrCb,EAAgBa,OAAO,CAACG,IAAI,CAC5B,WAGd,WAACjC,KAAAA,WACG,WAAC2B,QAAAA,WAAO3C,EAAE,WAAW,OACrB,UAACiB,OAAAA,CAAKC,UAAU,uBAAee,EAAgBiB,OAAO,UAK1E,EArK+ClD,EAAGD,EAAMkC,eAAe,KAEnD,UAACJ,EAAAA,CAAGA,CAAAA,CAACC,GAAI,WACL,WAACJ,EAAAA,CAAGA,CAAAA,WACA,WAACG,EAAAA,CAAGA,CAAAA,CAACsB,GAAI,EAAGjC,UAAU,gBACjBnB,EAAMqD,WAAW,CAACC,MAAM,EAAI,EACvBtD,EAAMqD,WAAW,CAACE,GAAG,CAAC,CAACC,EAAMC,KAC3B,GAAID,EAAKE,SAAS,CACd,CADgB,KAEZ,UAACC,KAAAA,CAAGxC,UAAU,qCAA6BlB,EAAE,sBAGzD,GACE,GACN,UAAC0C,KAAAA,CAAGxB,UAAU,+BACTnB,EAAMqD,WAAW,CAACC,MAAM,EAAI,EACvBtD,EAAMqD,WAAW,CAACE,GAAG,CAAC,CAACC,EAAMC,KAC3B,GAAID,EAAKE,SAAS,CACd,CADgB,KAEZ,WAACzC,KAAAA,CAEGE,UACIqC,EAAKE,SAAS,CAAG,YAAc,aAGnC,UAACxC,OAAAA,CAAKC,UAAU,2BACXqC,EAAKI,KAAK,EAAIJ,EAAKI,KAAK,CAACC,GAAG,CACzB,UAACC,MAAAA,CACGC,IAAK,GAAwCP,MAAAA,CAArCQ,8BAAsB,CAAC,gBAA6B,OAAfR,EAAKI,KAAK,CAACC,GAAG,IAG/D,UAACC,MAAAA,CAAIC,IAAI,6BAGjB,UAAC/B,MAAAA,CAAIb,UAAU,6BACX,WAAC8C,IAAAA,CAAE9C,UAAU,2BACT,UAAC+C,IAAAA,UAAGV,EAAKW,QAAQ,GACjB,UAACjD,OAAAA,UAAMsC,EAAKY,KAAK,GACjB,UAAClD,OAAAA,UACIsC,EAAKa,wBAAwB,GAElC,UAACnD,OAAAA,UAAMsC,EAAKc,aAAa,UArB5Bb,EA2BrB,GACE,QAyC1C,SAASc,CACoB,CACzBlB,CAAkB,CAClBrD,CAAU,CACVuB,CAAyB,CACzBW,CAAoB,MAgCPlC,EAAuBA,EA9BpC,MACI,WAAC8B,EAAAA,CAAGA,CAAAA,CAACsB,GAAI,YACL,UAACO,KAAAA,CAAGxC,UAAU,qCAA6BlB,EAAE,sBAC7C,WAAC0C,KAAAA,CAAGxB,UAAU,wBACTkC,EAAYC,MAAM,EAAI,EACjBD,EAAYE,GAAG,CAAC,CAACC,EAAMC,IAEjB,WAACxC,KAAAA,CAAeE,UAAWqC,EAAKE,SAAS,CAAG,YAAc,aACtD,UAACxC,OAAAA,CAAKC,UAAU,2BACXqC,EAAKI,KAAK,EAAIJ,EAAKI,KAAK,CAACC,GAAG,CACzB,UAACC,MAAAA,CACGC,IAAK,GAAwCP,MAAAA,CAArCQ,8BAAsB,CAAC,gBAA6B,OAAfR,EAAKI,KAAK,CAACC,GAAG,IAG/D,UAACC,MAAAA,CAAIC,IAAI,6BAGjB,UAAC/B,MAAAA,CAAIb,UAAU,6BACX,WAAC8C,IAAAA,CAAE9C,UAAU,2BACT,UAAC+C,IAAAA,UAAGV,EAAKW,QAAQ,GACjB,UAACjD,OAAAA,UAAMsC,EAAKY,KAAK,GACjB,UAAClD,OAAAA,UAAMsC,EAAKa,wBAAwB,GACpC,UAACnD,OAAAA,UAAMsC,EAAKc,aAAa,UAf5Bb,IAsBf,GACLzD,OAAAA,GAAAA,OAAAA,EAAAA,EAAOQ,IAAPR,EAAAA,KAAAA,EAAAA,EAAaS,GAAbT,GAAmB,UAAIA,GAAAA,OAAAA,EAAAA,EAAOQ,IAAAA,EAAPR,KAAAA,EAAAA,EAAaS,GAAbT,GAAmB,CAAC,EAAE,EAC1C,UAACuB,EAAAA,CAAqBa,YAAaF,IACnC,UAIpB,EAhFgCjC,EACAD,EAAMqD,WAAW,CACjBrD,EACAuB,EACAvB,EAAMkC,eAAe,aAQrD,0GCnJA,IAAMsC,EAAiB,CACrBC,UAAW,YACXrC,YAAa,cACbsC,MAAO,QACPC,QAAS,UACTC,OAAQ,QACV,EAoEA,EAAeC,CAAAA,EAAAA,EAAAA,EAAAA,CAAOA,CAAC,GAAgBC,GA1DG,IA0DIC,GAzDtC,CAAEC,KAyD6CD,CAzDzC,CAyD0C,SAzDxCzC,CAAQ,YAAEC,CAAU,CAAE,CAAGvC,EACjC,CAACiF,EAAUC,EAAY,CAAGC,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,EAAU,GAC5C,CAACC,EAAWC,EAAa,CAAGF,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAM,IAG1CG,EAA2B,UAC/B,GAAI,QAACN,EAAAA,KAAAA,EAAAA,EAAMnB,GAAAA,EAAK,CAAXmB,MACL,IAAMO,EAAY,MAAMC,EAAAA,CAAUA,CAACC,GAAG,CAAC,QAAS,CAACC,MAAO,CAACC,UAAWrD,EAAU0C,KAAMA,EAAKnB,GAAG,CAAE+B,QAASpB,CAAc,CAACjC,EAAW,CAAC,GAC9HgD,GAAaA,EAAUM,IAAI,EAAIN,EAAUM,IAAI,CAACvC,MAAM,CAAG,GAAG,CAC5D+B,EAAaE,EAAUM,IAAI,CAAC,EAAE,EAC9BX,GAAY,GAEhB,EAEMY,EAAkB,MAAOC,IAE7B,GADAA,EAAEC,cAAc,GACZ,CAAChB,SAAAA,KAAAA,EAAAA,CAAAA,CAAMnB,GAAAA,EAAK,OAChB,IAAMoC,EAAQ,CAAChB,EACTiB,EAAc,CAClBC,YAAa5D,EACboD,UAAWrD,EACX0C,KAAMA,EAAKnB,GAAG,CACd+B,QAASpB,CAAc,CAACjC,EAAW,EAErC,GAAI0D,EAAM,CACR,IAAMG,EAAc,MAAMZ,EAAAA,CAAUA,CAACa,IAAI,CAAC,QAASH,GAC/CE,GAAUA,EAAOvC,GAAG,EAAE,CACxBwB,EAAae,GACblB,EAAYe,GAEhB,KAAO,CACL,IAAMK,EAAW,MAAMd,EAAAA,CAAUA,CAACe,MAAM,CAAC,SAAuB,OAAdnB,EAAUvB,GAAG,GAC3DyC,GAAYA,EAASE,CAAC,EAAE,EACdP,EAEhB,CACF,EAKA,MAHAQ,CAAAA,EAAAA,EAAAA,SAAAA,CAASA,CAAC,KACRnB,GACF,EAAE,EAAE,EAEF,UAACtD,MAAAA,CAAIb,UAAU,0BACb,WAACuF,IAAAA,CAAEpG,KAAK,GAAGqG,QAASb,YAClB,UAAC5E,OAAAA,CAAKC,UAAU,iBACb8D,EACC,UAACpE,EAAAA,CAAeA,CAAAA,CAACM,UAAU,sBAAsBL,KAAM8F,EAAAA,GAAaA,CAAEC,MAAM,YAE5E,UAAChG,EAAAA,CAAeA,CAAAA,CAACM,UAAU,sBAAsBL,KAAMgG,EAAAA,GAAYA,CAAED,MAAM,WAG/E,UAAChG,EAAAA,CAAeA,CAAAA,CAACM,UAAU,WAAWL,KAAMiG,EAAAA,GAAUA,CAAEF,MAAM,gBAItE,sQC1EO,IAAMG,EAAoBC,CAAAA,EAAAA,EAAAA,CAAAA,CAAoBA,CAAC,CACpDC,sBAAuB,KACjBpC,EAAMqC,WAAW,IAAIrC,EAAMqC,WAAW,CAAC/E,WAAW,IAAI0C,EAAMqC,WAAW,CAAC/E,WAAW,CAAC,aAAa,CAKvGgF,CALyG,kBAKrF,mBACtB,GAAG,EAEkCH,CAAAA,EAAAA,EAAAA,CAAAA,CAAoBA,CAAC,CACxDC,sBAAuB,KACjBpC,EAAMqC,WAAW,IAAIrC,EAAMqC,WAAW,CAAC/E,WAAW,IAAI0C,EAAMqC,WAAW,CAAC/E,WAAW,CAAC,aAAa,CAKvGgF,CALyG,kBAKrF,wBACpBC,iBAAkB,IAAM,UAACC,EAAAA,OAAIA,CAAAA,CAAAA,EAC/B,GAAG,EAE+BL,CAAAA,EAAAA,EAAAA,CAAAA,CAAoBA,CAAC,CACrDC,sBAAuB,CAACpC,EAAO9E,KAC7B,GAAI8E,EAAMqC,WAAW,EAAIrC,EAAMqC,WAAW,CAAC/E,WAAW,EAAE,GAClD0C,EAAMqC,WAAW,CAAC/E,WAAW,CAAC,aAAa,CAC7C,CAD+C,MACxC,OAEP,GAAI0C,EAAMqC,WAAW,CAAC/E,WAAW,CAAC,aAAa,EAAE,EACrCA,WAAW,EAAIpC,EAAMoC,WAAW,CAAC4C,IAAI,EAAIhF,EAAMoC,WAAW,CAAC4C,IAAI,GAAKF,EAAME,IAAI,CAACnB,GAAG,CAC1F,CAD4F,MACrF,CAGb,CAEF,OAAO,CACT,EACAuD,mBAAoB,oBACtB,GAAG,EAEmCH,CAAAA,EAAAA,EAAAA,CAAAA,CAAoBA,CAAC,CACzDC,sBAAuB,CAACpC,EAAO9E,KAC7B,GAAI8E,EAAMqC,WAAW,EAAIrC,EAAMqC,WAAW,CAAC/E,WAAW,EAAE,GAClD0C,EAAMqC,WAAW,CAAC/E,WAAW,CAAC,aAAa,CAC7C,CAD+C,MACxC,OAEP,GAAI0C,EAAMqC,WAAW,CAAC/E,WAAW,CAAC,aAAa,EAAE,EACrCA,WAAW,EAAIpC,EAAMoC,WAAW,CAAC4C,IAAI,EAAIhF,EAAMoC,WAAW,CAAC4C,IAAI,GAAKF,EAAME,IAAI,CAACnB,GAAG,CAC1F,CAD4F,KACrF,EAGb,CAEF,OAAO,CACT,EACAuD,mBAAoB,yBACpBC,iBAAkB,IAAM,UAACC,EAAAA,OAAIA,CAAAA,CAAAA,EAC/B,GAAG,EAEoCL,CAAAA,EAAAA,EAAAA,CAAAA,CAAoBA,CAAC,CAC1DC,sBAAuB,KACjBpC,EAAMqC,WAAW,IAAIrC,EAAMqC,WAAW,CAACI,MAAM,IAAIzC,EAAMqC,WAAW,CAACI,MAAM,CAAC,WAAW,CAK3FH,CAL6F,kBAKzE,yBACtB,GAAG,EAEiCH,CAAAA,EAAAA,EAAAA,CAAAA,CAAoBA,CAAC,CACvDC,sBAAuB,CAACpC,EAAO9E,KAC7B,GAAI8E,EAAMqC,WAAW,EAAIrC,EAAMqC,WAAW,CAACK,uBAAuB,EAAE,GAC9D1C,EAAMqC,WAAW,CAACK,uBAAuB,CAAC,aAAa,CACzD,CAD2D,MACpD,OAEP,GAAI1C,EAAMqC,WAAW,CAACK,uBAAuB,CAAC,aAAa,EAAE,EAEnDpF,WAAW,EACjBpC,EAAMoC,WAAW,CAAC4C,IAAI,EACtBhF,EAAMoC,WAAW,CAAC4C,IAAI,GAAKF,EAAME,IAAI,CAACnB,GAAG,CAEzC,CADA,MACO,CAGb,CAEF,OAAO,CACT,EACAuD,mBAAoB,sBACtB,GAAG,EAEYJ,iBAAiBA,EAAC,yFCjDjC,MAxCsB,OAAC,aAAEvE,CAAW,CAA4B,GACxD,CAACgF,EAAOC,EAAU,CAAGvC,CAAAA,EAAAA,EAAAA,KAuCD3C,EAAC,CAvCA2C,CAAQA,EAAC,GAC9B,GAAElF,CAAC,CAAE,CAAGC,CAAAA,EAAAA,EAAAA,EAAAA,CAAcA,CAAC,UAE7B,MACE,iCACE,WAACyH,EAAAA,CAAKA,CAAAA,CACJC,KAAMH,EACNI,OAAQ,IAAMH,EAAU,CAACD,GACzBK,SAAS,SACTC,UAAU,YAEV,UAACJ,EAAAA,CAAKA,CAACK,MAAM,EAACC,WAAW,aACvB,UAACN,EAAAA,CAAKA,CAACO,KAAK,WAAEjI,EAAE,mBAElB,UAAC0H,EAAAA,CAAKA,CAACQ,IAAI,WACT,UAACnG,MAAAA,CAAIb,UAAU,cAAciH,wBAAyB,CAAEC,YAAuBC,GAAf7F,EAA2B,GAAKA,CAAY,SAI/GA,GAAeA,EAAYa,MAAM,CAAG,IACnC,UAACtB,MAAAA,CAAIoG,wBAAyB,CAAEC,YAAuBC,GAAf7F,EAA2B,GAAKA,CAAY,IACnE,IAAfA,EACF,WAACT,MAAAA,WACC,UAACA,MAAAA,CAAIb,UAAU,UAAWiH,wBAAyB,CAAEC,YAAuBC,GAAf7F,EAA2B,GAAKA,EAAY8F,SAAS,CAAC,EAAG,KAAO,GAA6CC,MAAAA,CAA1C/F,EAAYgG,QAAQ,CAAC,MAAQ,MAAQ,IAAa,OArB1K,GAqB4K,IACpL,UAACzG,MAAAA,CAAIb,UAAU,gBACb,UAACT,EAAAA,CAAMA,CAAAA,CACLiG,QAAS,IAAMe,EAAU,CAACD,GAC1BtG,UAAU,mBACVR,QAAQ,yBAERV,EAAE,mBAIN,KAGV", "sources": ["webpack://_N_E/./pages/institution/components/InstitutionCoverSectionContent.tsx", "webpack://_N_E/./components/common/Bookmark.tsx", "webpack://_N_E/./pages/institution/permission.tsx", "webpack://_N_E/./pages/institution/ReadMoreModal.tsx"], "sourcesContent": ["//Import Library\r\nimport React from \"react\";\r\nimport { <PERSON><PERSON>, <PERSON>, Container, <PERSON> } from \"react-bootstrap\";\r\nimport Link from \"next/link\";\r\nimport { faPen } from \"@fortawesome/free-solid-svg-icons\";\r\nimport { FontAwesomeIcon } from \"@fortawesome/react-fontawesome\";\r\n\r\n//Import services/components\r\nimport ReadMoreModal from \"../ReadMoreModal\";\r\nimport Bookmark from \"../../../components/common/Bookmark\";\r\nimport { canEditInstitution, canManageFocalPoints } from \"../permission\";\r\nimport { useTranslation } from 'next-i18next';\r\n\r\ninterface InstitutionCoverSectionContentProps {\r\n  institutionData: {\r\n    title: string;\r\n    description: string;\r\n    website?: string;\r\n    telephone?: string;\r\n    dial_code?: string;\r\n    address?: {\r\n      line_1?: string;\r\n      line_2?: string;\r\n      city?: string;\r\n      postal_code?: string;\r\n      country?: {\r\n        title: string;\r\n      };\r\n    };\r\n  };\r\n  routeData: {\r\n    routes: string[];\r\n  };\r\n  prop: {\r\n    routes: string[];\r\n  };\r\n  editAccess: boolean;\r\n  focalPoints: any[];\r\n}\r\n\r\nconst InstitutionCoverSectionContent = (props: InstitutionCoverSectionContentProps) => {\r\n    const { t } = useTranslation('common');\r\n\r\n    const EditInstitutionLink = () => {\r\n        return (\r\n            <Link\r\n                href=\"/institution/[...routes]\"\r\n                as={`/institution/edit/${props.prop.routes[1]}`}\r\n                >\r\n                <Button variant=\"secondary\" size=\"sm\">\r\n                    <FontAwesomeIcon icon={faPen} />\r\n                    &nbsp;{t(\"Edit\")}\r\n                </Button>\r\n            </Link>\r\n        );\r\n    };\r\n\r\n    const ManageFocalPoints = () => {\r\n        return (\r\n            <li>\r\n                <span className=\"image-container\">\r\n                    <Link\r\n                        href=\"/institution/[...routes]\"\r\n                        as={`/institution/focalpoint/${props?.prop?.routes[1]}`}\r\n                        >\r\n                        <i className=\"fas fa-plus\" />\r\n                    </Link>\r\n                </span>\r\n            </li>\r\n        );\r\n    };\r\n\r\n    const CanEditInstitution = canEditInstitution(() => <EditInstitutionLink />);\r\n    const CanManageFocalPoints = canManageFocalPoints(() => <ManageFocalPoints />);\r\n\r\n    return (\r\n        <>\r\n            <Container fluid>\r\n                <Row>\r\n                    {institution_request_func(\r\n                        props.institutionData,\r\n                        props.prop,\r\n                        CanEditInstitution,\r\n                        props.editAccess\r\n                    )}\r\n\r\n                    <Col xs={6}>\r\n                        <div>\r\n                            <ReadMoreModal\r\n                                description={props.institutionData.description}\r\n                            />\r\n                        </div>\r\n                        {institution_info_func(t, props.institutionData)}\r\n                    </Col>\r\n                    <Col xs={6}>\r\n                        <Row>\r\n                            <Col md={4} className=\"p-0\">\r\n                                {props.focalPoints.length >= 1\r\n                                    ? props.focalPoints.map((item, index) => {\r\n                                        if (item.isPrimary) {\r\n                                            return (\r\n                                                <h6 className=\"other-focal-points-header\">{t(\"PrimaryFocalPoint\")}</h6>\r\n                                            );\r\n                                        }\r\n                                    })\r\n                                    : \"\"}\r\n                                <ul className=\"focalPoints primary\">\r\n                                    {props.focalPoints.length >= 1\r\n                                        ? props.focalPoints.map((item, index) => {\r\n                                            if (item.isPrimary) {\r\n                                                return (\r\n                                                    <li\r\n                                                        key={index}\r\n                                                        className={\r\n                                                            item.isPrimary ? \"isPrimary\" : \"\"\r\n                                                        }\r\n                                                    >\r\n                                                        <span className=\"image-container\">\r\n                                                            {item.image && item.image._id ? (\r\n                                                                <img\r\n                                                                    src={`${process.env.API_SERVER}/image/show/${item.image._id}`}\r\n                                                                />\r\n                                                            ) : (\r\n                                                                <img src=\"/images/rkiProfile.jpg\" />\r\n                                                            )}\r\n                                                        </span>\r\n                                                        <div className=\"focalpointDetails\">\r\n                                                            <p className=\"fpDetailsFixed\">\r\n                                                                <b>{item.username}</b>\r\n                                                                <span>{item.email}</span>\r\n                                                                <span>\r\n                                                                    {item.focal_points_institution}\r\n                                                                </span>\r\n                                                                <span>{item.mobile_number}</span>\r\n                                                            </p>\r\n                                                        </div>\r\n                                                    </li>\r\n                                                );\r\n                                            }\r\n                                        })\r\n                                        : \"\"}\r\n                                </ul>\r\n                            </Col>\r\n                            {otherFocalpoint_request_func(\r\n                                t,\r\n                                props.focalPoints,\r\n                                props,\r\n                                CanManageFocalPoints,\r\n                                props.institutionData\r\n                            )}\r\n                        </Row>\r\n                    </Col>\r\n                </Row>\r\n            </Container>\r\n        </>\r\n    )\r\n}\r\n\r\nexport default InstitutionCoverSectionContent;\r\n\r\nfunction institution_request_func(\r\n    institutionData: any,\r\n    props: any,\r\n    CanEditInstitution: any,\r\n    editAccess: boolean\r\n) {\r\n    return (\r\n        <Col xs={12}>\r\n            <div className=\"d-flex justify-content-between\">\r\n                <h4 className=\"institutionTitle\">\r\n                    {institutionData.title} &nbsp;&nbsp;\r\n                    {editAccess && props.routes && props.routes[1] ? (\r\n                        <CanEditInstitution institution={institutionData} />\r\n                    ) : null}\r\n                </h4>\r\n                <Bookmark entityId={props.routes[1]} entityType=\"institution\" />\r\n            </div>\r\n        </Col>\r\n    );\r\n}\r\n\r\nfunction otherFocalpoint_request_func(\r\n    t: (p: string ) => string,\r\n    focalPoints: any[],\r\n    props: any,\r\n    CanManageFocalPoints: any,\r\n    institutionData: any\r\n) {\r\n    return (\r\n        <Col md={8}>\r\n            <h6 className=\"other-focal-points-header\">{t(\"OtherFocalPoints\")}</h6>\r\n            <ul className=\"focalPoints\">\r\n                {focalPoints.length >= 1\r\n                    ? focalPoints.map((item, index) => {\r\n                        return (\r\n                            <li key={index} className={item.isPrimary ? \"isPrimary\" : \"\"}>\r\n                                <span className=\"image-container\">\r\n                                    {item.image && item.image._id ? (\r\n                                        <img\r\n                                            src={`${process.env.API_SERVER}/image/show/${item.image._id}`}\r\n                                        />\r\n                                    ) : (\r\n                                        <img src=\"/images/rkiProfile.jpg\" />\r\n                                    )}\r\n                                </span>\r\n                                <div className=\"focalpointDetails\">\r\n                                    <p className=\"fpDetailsFixed\">\r\n                                        <b>{item.username}</b>\r\n                                        <span>{item.email}</span>\r\n                                        <span>{item.focal_points_institution}</span>\r\n                                        <span>{item.mobile_number}</span>\r\n                                    </p>\r\n                                </div>\r\n                            </li>\r\n                        );\r\n                        // }\r\n                    })\r\n                    : \"\"}\r\n                {props?.prop?.routes && props?.prop?.routes[1] ? (\r\n                    <CanManageFocalPoints institution={institutionData} />\r\n                ) : null}\r\n            </ul>\r\n        </Col>\r\n    );\r\n}\r\n\r\nfunction institution_info_func(t: (p: string ) => string, institutionData: any) {\r\n    return (\r\n        <div className=\"institutionInfo\">\r\n            <ul>\r\n                <li>\r\n                    <label>{t(\"Telephone\")}:</label>\r\n                    <span className=\"field-value\">{`${institutionData.dial_code !== \"undefined\" &&\r\n                        institutionData.dial_code\r\n                        } ${institutionData.telephone}`}</span>\r\n                </li>\r\n                <li>\r\n                    <label>{t(\"Address\")}:</label>\r\n                    <span className=\"field-value\">\r\n                        {institutionData && institutionData.address\r\n                            ? institutionData.address.line_1\r\n                            : null}{\" \"}\r\n                        {institutionData && institutionData.address\r\n                            ? institutionData.address.line_2\r\n                            : null}{\" \"}\r\n                        {institutionData && institutionData.address\r\n                            ? institutionData.address.city\r\n                            : null}\r\n                    </span>\r\n                </li>\r\n                <li>\r\n                    <label>{t(\"Website\")}:</label>\r\n                    <span className=\"field-value\">{institutionData.website}</span>\r\n                </li>\r\n            </ul>\r\n        </div>\r\n    );\r\n}\r\n", "//Import Library\r\nimport { connect } from \"react-redux\";\r\nimport {faBookmark, faCheckCircle, faPlusCircle} from \"@fortawesome/free-solid-svg-icons\";\r\nimport {useEffect, useState} from \"react\";\r\nimport {FontAwesomeIcon} from \"@fortawesome/react-fontawesome\";\r\n\r\n//Import services/components\r\nimport apiService from \"../../services/apiService\";\r\n\r\nconst onModelOptions = {\r\n  operation: \"Operation\",\r\n  institution: \"Institution\",\r\n  event: \"Event\",\r\n  project: \"Project\",\r\n  vspace: \"Vspace\"\r\n} as const;\r\n\r\ninterface BookMarkProps {\r\n  user?: {\r\n    _id: string;\r\n  };\r\n  entityId: string;\r\n  entityType: keyof typeof onModelOptions;\r\n}\r\n\r\nconst BookMark: React.FC<BookMarkProps> = (props) => {\r\n  const { user, entityId, entityType } = props;\r\n  const [bookmark, setBookmark] = useState<boolean>(false);\r\n  const [subscribe, setSubscribe] = useState<any>(\"\");\r\n\r\n  //Handle bookmark feature\r\n  const fetchIfContentSubscribed = async () => {\r\n    if (!user?._id) return;\r\n    const checkFlag = await apiService.get('/flag', {query: {entity_id: entityId, user: user._id, onModel: onModelOptions[entityType]}});\r\n    if (checkFlag && checkFlag.data && checkFlag.data.length > 0) {\r\n      setSubscribe(checkFlag.data[0]);\r\n      setBookmark(true);\r\n    }\r\n  };\r\n\r\n  const bookmarkHandler = async (e: React.MouseEvent<HTMLAnchorElement>) => {\r\n    e.preventDefault();\r\n    if (!user?._id) return;\r\n    const flag  = !bookmark;\r\n    const flagPayload = {\r\n      entity_type: entityType,\r\n      entity_id: entityId,\r\n      user: user._id,\r\n      onModel: onModelOptions[entityType]\r\n    }\r\n    if (flag) {\r\n      const flagIt: any = await apiService.post('/flag', flagPayload);\r\n      if (flagIt && flagIt._id) {\r\n        setSubscribe(flagIt);\r\n        setBookmark(flag);\r\n      }\r\n    } else {\r\n      const unFlagIt = await apiService.remove(`/flag/${subscribe._id}`);\r\n      if (unFlagIt && unFlagIt.n) {\r\n        setBookmark(flag);\r\n      }\r\n    }\r\n  }\r\n  //END Bookmark handler\r\n  useEffect(() => {\r\n    fetchIfContentSubscribed();\r\n  },[]);\r\n  return (\r\n    <div className=\"subscribe-flag\">\r\n      <a href=\"\" onClick={bookmarkHandler}>\r\n        <span className=\"check\">\r\n          {bookmark ?\r\n            <FontAwesomeIcon className=\"clickable checkIcon\" icon={faCheckCircle} color=\"#00CC00\" />\r\n            :\r\n            <FontAwesomeIcon className=\"clickable minusIcon\" icon={faPlusCircle} color=\"#fff\"/>\r\n          }\r\n        </span>\r\n        <FontAwesomeIcon className=\"bookmark\" icon={faBookmark} color=\"#d4d4d4\"/>\r\n      </a>\r\n    </div>\r\n  )\r\n}\r\n\r\nexport default connect((state: any) => state)(BookMark);\r\n\r\n", "//Import Library\r\nimport React from 'react';\r\nimport connectedAuthWrapper from 'redux-auth-wrapper/connectedAuthWrapper';\r\n\r\n//Import services/components\r\nimport R403 from \"../r403\";\r\n\r\nexport const canAddInstitution = connectedAuthWrapper({\r\n  authenticatedSelector: (state) => {\r\n    if (state.permissions && state.permissions.institution && state.permissions.institution['create:any']) {\r\n      return true;\r\n    }\r\n    return false;\r\n  },\r\n  wrapperDisplayName: 'CanAddInstitution',\r\n});\r\n\r\nexport const canAddInstitutionForm = connectedAuthWrapper({\r\n  authenticatedSelector: (state) => {\r\n    if (state.permissions && state.permissions.institution && state.permissions.institution['create:any']) {\r\n      return true;\r\n    }\r\n    return false;\r\n  },\r\n  wrapperDisplayName: 'CanAddInstitutionForm',\r\n  FailureComponent: () => <R403/>\r\n});\r\n\r\nexport const canEditInstitution = connectedAuthWrapper({\r\n  authenticatedSelector: (state, props) => {\r\n    if (state.permissions && state.permissions.institution) {\r\n      if (state.permissions.institution['update:any']) {\r\n        return true;\r\n      } else { //if update:own\r\n        if (state.permissions.institution['update:own']) {\r\n          if (props.institution && props.institution.user && props.institution.user === state.user._id) {\r\n            return true;\r\n          }\r\n        }\r\n      }\r\n    }\r\n    return false;\r\n  },\r\n  wrapperDisplayName: 'CanEditInstitution',\r\n});\r\n\r\nexport const canEditInstitutionForm = connectedAuthWrapper({\r\n  authenticatedSelector: (state, props) => {\r\n    if (state.permissions && state.permissions.institution) {\r\n      if (state.permissions.institution['update:any']) {\r\n        return true;\r\n      } else { //if update:own\r\n        if (state.permissions.institution['update:own']) {\r\n          if (props.institution && props.institution.user && props.institution.user === state.user._id) {\r\n            return true;\r\n          }\r\n        }\r\n      }\r\n    }\r\n    return false;\r\n  },\r\n  wrapperDisplayName: 'CanEditInstitutionForm',\r\n  FailureComponent: () => <R403/>\r\n});\r\n\r\nexport const canViewDiscussionUpdate = connectedAuthWrapper({\r\n  authenticatedSelector: (state) => {\r\n    if (state.permissions && state.permissions.update && state.permissions.update['read:any']) {\r\n      return true;\r\n    }\r\n    return false;\r\n  },\r\n  wrapperDisplayName: 'CanViewDiscussionUpdate',\r\n});\r\n\r\nexport const canManageFocalPoints = connectedAuthWrapper({\r\n  authenticatedSelector: (state, props) => {\r\n    if (state.permissions && state.permissions.institution_focal_point) {\r\n      if (state.permissions.institution_focal_point[\"update:any\"]) {\r\n        return true;\r\n      } else {\r\n        if (state.permissions.institution_focal_point[\"update:own\"]) {\r\n          if (\r\n            props.institution &&\r\n            props.institution.user &&\r\n            props.institution.user === state.user._id\r\n          ) {\r\n            return true;\r\n          }\r\n        }\r\n      }\r\n    }\r\n    return false;\r\n  },\r\n  wrapperDisplayName: \"canManageFocalPoints\",\r\n});\r\n\r\nexport default canAddInstitution;", "//Import Library\r\nimport { useState } from \"react\";\r\nimport { Modal, Button } from \"react-bootstrap\";\r\n\r\n//Import services/components\r\nimport { useTranslation } from 'next-i18next';\r\n\r\n\r\nconst ReadMoreModal = ({ description } : { description: string }) => {\r\n  const [modal, showModal] = useState(false);\r\n  const { t } = useTranslation('common');\r\n  const _string = \"\"\r\n  return (\r\n    <>\r\n      <Modal\r\n        show={modal}\r\n        onHide={() => showModal(!modal)}\r\n        backdrop=\"static\"\r\n        keyboard={false}\r\n      >\r\n        <Modal.Header closeButton>\r\n          <Modal.Title>{t(\"Description\")}</Modal.Title>\r\n        </Modal.Header>\r\n        <Modal.Body>\r\n          <div className=\"_readmore_d\" dangerouslySetInnerHTML={{ __html: description == undefined ? \"\" : description }}></div>\r\n        </Modal.Body>\r\n      </Modal>\r\n\r\n      {description && description.length < 130 ? (\r\n        <div dangerouslySetInnerHTML={{ __html: description == undefined ? \"\" : description }}></div>\r\n      ) : description != '' ? (\r\n        <div>\r\n          <div className=\"_tabelw\"  dangerouslySetInnerHTML={{ __html: description == undefined ? \"\" : description.substring(0, 130) + `${description.includes(\"<p\") ? '...' : ''}${_string}` }}></div>\r\n          <div className=\"pt-3\">\r\n            <Button\r\n              onClick={() => showModal(!modal)}\r\n              className=\"readMoreBtn mb-3\"\r\n              variant=\"outline-light\"\r\n            >\r\n             {t(\"ReadMore\")} \r\n            </Button>\r\n          </div>\r\n        </div>\r\n      ) : \"\"}\r\n    </>\r\n  );\r\n};\r\n\r\nexport default ReadMoreModal;\r\n"], "names": ["props", "t", "useTranslation", "InstitutionCoverSectionContent", "EditInstitutionLink", "Link", "href", "as", "prop", "routes", "<PERSON><PERSON>", "variant", "size", "FontAwesomeIcon", "icon", "faPen", "ManageFocalPoints", "li", "span", "className", "i", "CanEditInstitution", "canEditInstitution", "CanManageFocalPoints", "canManageFocalPoints", "Container", "fluid", "Row", "institution_request_func", "editAccess", "Col", "xs", "div", "h4", "institutionData", "title", "institution", "Bookmark", "entityId", "entityType", "ReadMoreModal", "description", "institution_info_func", "ul", "label", "dial_code", "telephone", "address", "line_1", "line_2", "city", "website", "md", "focalPoints", "length", "map", "item", "index", "isPrimary", "h6", "image", "_id", "img", "src", "process", "p", "b", "username", "email", "focal_points_institution", "mobile_number", "otherFocalpoint_request_func", "onModelOptions", "operation", "event", "project", "vspace", "connect", "state", "BookMark", "user", "bookmark", "setBookmark", "useState", "subscribe", "setSubscribe", "fetchIfContentSubscribed", "checkFlag", "apiService", "get", "query", "entity_id", "onModel", "data", "bookmarkHandler", "e", "preventDefault", "flag", "flagPayload", "entity_type", "flagIt", "post", "unFlagIt", "remove", "n", "useEffect", "a", "onClick", "faCheckCircle", "color", "faPlusCircle", "faBookmark", "canAddInstitution", "connectedAuthWrapper", "authenticatedSelector", "permissions", "wrapperDisplayName", "FailureComponent", "R403", "update", "institution_focal_point", "modal", "showModal", "Modal", "show", "onHide", "backdrop", "keyboard", "Header", "closeButton", "Title", "Body", "dangerouslySetInnerHTML", "__html", "undefined", "substring", "_string", "includes"], "sourceRoot": "", "ignoreList": []}