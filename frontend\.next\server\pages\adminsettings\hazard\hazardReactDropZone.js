"use strict";(()=>{var e={};e.id=3792,e.ids=[636,3220,3792],e.modules={123:e=>{e.exports=require("dom-helpers/removeEventListener")},1332:e=>{e.exports=require("react-custom-scrollbars-2")},1428:e=>{e.exports=import("axios")},1680:e=>{e.exports=require("@restart/ui/utils")},1919:e=>{e.exports=require("react-transition-group/Transition")},3892:e=>{e.exports=require("classnames")},4048:e=>{e.exports=require("react-truncate")},6009:e=>{e.exports=require("dom-helpers/ownerDocument")},6952:e=>{e.exports=require("@restart/ui/Modal")},7374:e=>{e.exports=require("@restart/ui/Dropdown")},8732:e=>{e.exports=require("react/jsx-runtime")},9653:e=>{e.exports=require("@restart/ui/Overlay")},11242:e=>{e.exports=require("redux-persist/integration/react")},11563:(e,r,t)=>{t.a(e,async(e,s)=>{try{t.r(r),t.d(r,{config:()=>g,default:()=>x,getServerSideProps:()=>h,getStaticPaths:()=>m,getStaticProps:()=>c,reportWebVitals:()=>f,routeModule:()=>P,unstable_getServerProps:()=>w,unstable_getServerSideProps:()=>y,unstable_getStaticParams:()=>v,unstable_getStaticPaths:()=>b,unstable_getStaticProps:()=>q});var o=t(63885),i=t(80237),a=t(81413),n=t(9616),p=t.n(n),l=t(72386),u=t(13822),d=e([l,u]);[l,u]=d.then?(await d)():d;let x=(0,a.M)(u,"default"),c=(0,a.M)(u,"getStaticProps"),m=(0,a.M)(u,"getStaticPaths"),h=(0,a.M)(u,"getServerSideProps"),g=(0,a.M)(u,"config"),f=(0,a.M)(u,"reportWebVitals"),q=(0,a.M)(u,"unstable_getStaticProps"),b=(0,a.M)(u,"unstable_getStaticPaths"),v=(0,a.M)(u,"unstable_getStaticParams"),w=(0,a.M)(u,"unstable_getServerProps"),y=(0,a.M)(u,"unstable_getServerSideProps"),P=new o.PagesRouteModule({definition:{kind:i.A.PAGES,page:"/adminsettings/hazard/hazardReactDropZone",pathname:"/adminsettings/hazard/hazardReactDropZone",bundlePath:"",filename:""},components:{App:l.default,Document:p()},userland:u});s()}catch(e){s(e)}})},11688:e=>{e.exports=require("@restart/hooks/useWillUnmount")},12388:e=>{e.exports=require("dom-helpers/addEventListener")},13364:e=>{e.exports=require("redux-saga/effects")},13822:(e,r,t)=>{t.a(e,async(e,s)=>{try{t.r(r),t.d(r,{default:()=>P});var o=t(8732),i=t(82015),a=t(16029),n=t(82053),p=t(54131),l=t(27825),u=t.n(l),d=t(63487),x=t(88751),c=e([p,d]);[p,d]=c.then?(await c)():c;let m=[],h="20971520",g={flex:1,display:"flex",flexDirection:"column",alignItems:"center",justifyContent:"center",width:"100%",height:"100%",borderWidth:.1,borderColor:"#fafafa",backgroundColor:"#fafafa",color:"black",transition:"border  .24s ease-in-out"},f={display:"inline-flex",borderRadius:2,border:"1px solid #ddd",marginBottom:8,marginRight:20,width:100,height:100,padding:2,position:"relative",boxShadow:"0 0 15px 0.25px rgba(0,0,0,0.25)",boxSizing:"border-box"},q={display:"flex",flexDirection:"row",justifyContent:"flex-start",flexWrap:"wrap",marginTop:20},b={display:"flex",minWidth:0,overflow:"hidden"},v={position:"absolute",fontSize:"22px",top:"-10px",right:"-10px",zIndex:1e3,cursor:"pointer",backgroundColor:"#fff",color:"#000",borderRadius:"50%"},w={display:"block",width:"auto",height:"100%"},y={borderColor:"#2196f3"},P=e=>{let{t:r}=(0,x.useTranslation)("common"),[t,s]=(0,i.useState)([]),l=async e=>{await d.A.remove(`/image/${e}`)},c=r=>{let o=r&&r._id?{serverID:r._id}:{file:r},i=u().findIndex(m,o);l(m[i].serverID),m.splice(i,1),e.getImgID(m,e.index?e.index:0);let a=[...t];a.splice(a.indexOf(r),1),s(a)},P=e=>(0,o.jsx)("img",{src:e.preview,style:w}),S=t.map((e,r)=>(0,o.jsx)("div",{children:(0,o.jsxs)("div",{style:f,children:[(0,o.jsx)("div",{style:b,children:P(e)}),(0,o.jsx)(n.FontAwesomeIcon,{icon:p.faTimesCircle,style:v,color:"black",onClick:()=>c(e)})]})},r));(0,i.useEffect)(()=>{t.forEach(e=>URL.revokeObjectURL(e.preview)),m=[]},[]),(0,i.useEffect)(()=>{if(e&&e.datas){let r=e.datas.map((r,t)=>(m.push({serverID:r._id,index:e.index?e.index:0}),{...r,preview:`http://localhost:3001/api/v1/image/show/${r._id}`}));s([...r])}},[e.datas]);let j=async(r,t)=>{if(r.length>t)try{let s=new FormData;s.append("file",r[t]);let o=await d.A.post("/image",s,{"Content-Type":"multipart/form-data"});m.push({serverID:o._id,file:r[t],index:e.index?e.index:0}),j(r,t+1)}catch(e){j(r,t+1)}else e.getImgID(m,e.index?e.index:0)},A=(0,i.useCallback)(e=>{j(e,0);let r=e.map((e,r)=>Object.assign(e,{preview:URL.createObjectURL(e)}));s([...r])},[]),{getRootProps:k,getInputProps:I,isDragActive:M,isDragAccept:C,isDragReject:D,fileRejections:E}=(0,a.useDropzone)({accept:"image/*",multiple:!1,minSize:0,maxSize:h,onDrop:A}),_=(0,i.useMemo)(()=>({...g,...M?y:{outline:"2px dashed #bbb"},...C?{outline:"2px dashed #595959"}:{outline:"2px dashed #bbb"},...D?{outline:"2px dashed red"}:{activeStyle:y}}),[M,D]),z=E.length>0&&E[0].file.size>h;return(0,o.jsxs)(o.Fragment,{children:[(0,o.jsx)("div",{className:" d-flex justify-content-center align-items-center mt-3",style:{width:"100%",height:"180px"},children:(0,o.jsxs)("div",{...k({style:_}),children:[(0,o.jsx)("input",{...I()}),(0,o.jsx)(n.FontAwesomeIcon,{icon:p.faCloudUploadAlt,size:"4x",color:"#999"}),(0,o.jsx)("p",{style:{color:"#595959",marginBottom:"0px"},children:r("Drag'n'dropsomefileshere,orclicktoselectfiles")}),(0,o.jsx)("small",{style:{color:"#595959"},children:r("ImageWeSupport")}),(0,o.jsxs)("small",{style:{color:"#595959"},children:[(0,o.jsx)("b",{children:r("Note:")})," ",r("Onesingleimagewillbeaccepted")]}),z&&(0,o.jsxs)("small",{className:"text-danger mt-2",children:[" ",(0,o.jsx)(n.FontAwesomeIcon,{icon:p.faExclamationCircle,size:"1x",color:"red"})," ",r("FileistoolargeItshouldbelessthan20MB")]}),D&&(0,o.jsxs)("small",{className:"text-danger",style:{color:"#595959"},children:[(0,o.jsx)(n.FontAwesomeIcon,{icon:p.faExclamationCircle,size:"1x",color:"red"})," ",r("Filetypenotacceptedsorr")]})]})}),(0,o.jsx)("div",{style:q,children:S})]})};s()}catch(e){s(e)}})},14062:e=>{e.exports=import("react-redux")},14078:e=>{e.exports=import("swr")},14332:e=>{e.exports=require("uncontrollable")},16029:e=>{e.exports=require("react-dropzone")},16116:e=>{e.exports=require("invariant")},18622:e=>{e.exports=require("yup")},19121:e=>{e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},22326:e=>{e.exports=require("react-dom")},22541:e=>{e.exports=require("redux-persist/lib/storage")},25303:e=>{e.exports=require("@restart/ui/NavItem")},26324:e=>{e.exports=require("warning")},27825:e=>{e.exports=require("lodash")},27910:e=>{e.exports=require("stream")},28217:e=>{e.exports=require("@restart/ui/DropdownItem")},29021:e=>{e.exports=require("fs")},29294:e=>{e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},29628:e=>{e.exports=require("dom-helpers/addClass")},29825:e=>{e.exports=require("prop-types")},29841:e=>{e.exports=require("dom-helpers/hasClass")},30362:e=>{e.exports=require("@restart/ui/DropdownToggle")},33873:e=>{e.exports=require("path")},36653:e=>{e.exports=require("nprogress")},36955:e=>{e.exports=require("@restart/hooks/useBreakpoint")},37766:e=>{e.exports=require("@restart/hooks/useMergedRefs")},39756:e=>{e.exports=import("redux")},40051:e=>{e.exports=require("dom-helpers/removeClass")},40361:e=>{e.exports=require("next/dist/compiled/next-server/pages.runtime.prod.js")},42893:e=>{e.exports=import("react-hot-toast")},43294:e=>{e.exports=require("formik")},50009:e=>{e.exports=require("@restart/ui/DropdownMenu")},54131:e=>{e.exports=import("@fortawesome/free-solid-svg-icons")},57664:e=>{e.exports=require("dom-helpers/scrollbarSize")},58928:e=>{e.exports=require("dom-helpers/canUseDOM")},59717:e=>{e.exports=require("@restart/ui/DropdownContext")},60560:e=>{e.exports=require("dom-helpers/contains")},63033:e=>{e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},65447:e=>{e.exports=require("@restart/ui/Button")},67364:e=>{e.exports=require("@restart/hooks/useCallbackRef")},68455:e=>{e.exports=import("redux-saga")},69722:e=>{e.exports=require("es6-promise")},74075:e=>{e.exports=require("zlib")},74716:e=>{e.exports=require("moment")},74987:e=>{e.exports=require("@restart/ui/ModalManager")},78097:e=>{e.exports=require("next-redux-saga")},78634:e=>{e.exports=require("@restart/ui/Anchor")},80237:(e,r)=>{Object.defineProperty(r,"A",{enumerable:!0,get:function(){return t}});var t=function(e){return e.PAGES="PAGES",e.PAGES_API="PAGES_API",e.APP_PAGE="APP_PAGE",e.APP_ROUTE="APP_ROUTE",e.IMAGE="IMAGE",e}({})},81366:e=>{e.exports=require("redux-auth-wrapper/connectedAuthWrapper")},81413:(e,r)=>{Object.defineProperty(r,"M",{enumerable:!0,get:function(){return function e(r,t){return t in r?r[t]:"then"in r&&"function"==typeof r.then?r.then(r=>e(r,t)):"function"==typeof r&&"default"===t?r:void 0}}})},81521:e=>{e.exports=require("dom-helpers/querySelectorAll")},81895:e=>{e.exports=require("@restart/hooks/useEventCallback")},82015:e=>{e.exports=require("react")},82053:e=>{e.exports=require("@fortawesome/react-fontawesome")},86842:e=>{e.exports=require("@restart/ui/SelectableContext")},87571:e=>{e.exports=require("dom-helpers/transitionEnd")},88751:e=>{e.exports=require("next-i18next")},93787:e=>{e.exports=require("redux-persist")},94947:e=>{e.exports=require("@restart/hooks/useTimeout")},96196:e=>{e.exports=require("next-redux-wrapper")},98320:e=>{e.exports=require("@restart/hooks/useIsomorphicEffect")},99460:e=>{e.exports=require("dom-helpers/css")}};var r=require("../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[6089,9216,9616,2386],()=>t(11563));module.exports=s})();