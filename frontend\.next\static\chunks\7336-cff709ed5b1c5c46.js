"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[7336],{17336:(e,t,n)=>{n.d(t,{VB:()=>Y});var r=n(14232),o=n(95062),i=n.n(o);function a(e,t,n,r){return new(n||(n=Promise))(function(o,i){function a(e){try{u(r.next(e))}catch(e){i(e)}}function c(e){try{u(r.throw(e))}catch(e){i(e)}}function u(e){var t;e.done?o(e.value):((t=e.value)instanceof n?t:new n(function(e){e(t)})).then(a,c)}u((r=r.apply(e,t||[])).next())})}function c(e,t){var n,r,o,i={label:0,sent:function(){if(1&o[0])throw o[1];return o[1]},trys:[],ops:[]},a=Object.create(("function"==typeof Iterator?Iterator:Object).prototype);return a.next=c(0),a.throw=c(1),a.return=c(2),"function"==typeof Symbol&&(a[Symbol.iterator]=function(){return this}),a;function c(c){return function(u){var l=[c,u];if(n)throw TypeError("Generator is already executing.");for(;a&&(a=0,l[0]&&(i=0)),i;)try{if(n=1,r&&(o=2&l[0]?r.return:l[0]?r.throw||((o=r.return)&&o.call(r),0):r.next)&&!(o=o.call(r,l[1])).done)return o;switch(r=0,o&&(l=[2&l[0],o.value]),l[0]){case 0:case 1:o=l;break;case 4:return i.label++,{value:l[1],done:!1};case 5:i.label++,r=l[1],l=[0];continue;case 7:l=i.ops.pop(),i.trys.pop();continue;default:if(!(o=(o=i.trys).length>0&&o[o.length-1])&&(6===l[0]||2===l[0])){i=0;continue}if(3===l[0]&&(!o||l[1]>o[0]&&l[1]<o[3])){i.label=l[1];break}if(6===l[0]&&i.label<o[1]){i.label=o[1],o=l;break}if(o&&i.label<o[2]){i.label=o[2],i.ops.push(l);break}o[2]&&i.ops.pop(),i.trys.pop();continue}l=t.call(e,i)}catch(e){l=[6,e],r=0}finally{n=o=0}if(5&l[0])throw l[1];return{value:l[0]?l[1]:void 0,done:!0}}}}Object.create;Object.create;var u=("function"==typeof SuppressedError&&SuppressedError,new Map([["aac","audio/aac"],["abw","application/x-abiword"],["arc","application/x-freearc"],["avif","image/avif"],["avi","video/x-msvideo"],["azw","application/vnd.amazon.ebook"],["bin","application/octet-stream"],["bmp","image/bmp"],["bz","application/x-bzip"],["bz2","application/x-bzip2"],["cda","application/x-cdf"],["csh","application/x-csh"],["css","text/css"],["csv","text/csv"],["doc","application/msword"],["docx","application/vnd.openxmlformats-officedocument.wordprocessingml.document"],["eot","application/vnd.ms-fontobject"],["epub","application/epub+zip"],["gz","application/gzip"],["gif","image/gif"],["heic","image/heic"],["heif","image/heif"],["htm","text/html"],["html","text/html"],["ico","image/vnd.microsoft.icon"],["ics","text/calendar"],["jar","application/java-archive"],["jpeg","image/jpeg"],["jpg","image/jpeg"],["js","text/javascript"],["json","application/json"],["jsonld","application/ld+json"],["mid","audio/midi"],["midi","audio/midi"],["mjs","text/javascript"],["mp3","audio/mpeg"],["mp4","video/mp4"],["mpeg","video/mpeg"],["mpkg","application/vnd.apple.installer+xml"],["odp","application/vnd.oasis.opendocument.presentation"],["ods","application/vnd.oasis.opendocument.spreadsheet"],["odt","application/vnd.oasis.opendocument.text"],["oga","audio/ogg"],["ogv","video/ogg"],["ogx","application/ogg"],["opus","audio/opus"],["otf","font/otf"],["png","image/png"],["pdf","application/pdf"],["php","application/x-httpd-php"],["ppt","application/vnd.ms-powerpoint"],["pptx","application/vnd.openxmlformats-officedocument.presentationml.presentation"],["rar","application/vnd.rar"],["rtf","application/rtf"],["sh","application/x-sh"],["svg","image/svg+xml"],["swf","application/x-shockwave-flash"],["tar","application/x-tar"],["tif","image/tiff"],["tiff","image/tiff"],["ts","video/mp2t"],["ttf","font/ttf"],["txt","text/plain"],["vsd","application/vnd.visio"],["wav","audio/wav"],["weba","audio/webm"],["webm","video/webm"],["webp","image/webp"],["woff","font/woff"],["woff2","font/woff2"],["xhtml","application/xhtml+xml"],["xls","application/vnd.ms-excel"],["xlsx","application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"],["xml","application/xml"],["xul","application/vnd.mozilla.xul+xml"],["zip","application/zip"],["7z","application/x-7z-compressed"],["mkv","video/x-matroska"],["mov","video/quicktime"],["msg","application/vnd.ms-outlook"]]));function l(e,t){var n=function(e){var t=e.name;if(t&&-1!==t.lastIndexOf(".")&&!e.type){var n=t.split(".").pop().toLowerCase(),r=u.get(n);r&&Object.defineProperty(e,"type",{value:r,writable:!1,configurable:!1,enumerable:!0})}return e}(e);if("string"!=typeof n.path){var r=e.webkitRelativePath;Object.defineProperty(n,"path",{value:"string"==typeof t?t:"string"==typeof r&&r.length>0?r:e.name,writable:!1,configurable:!1,enumerable:!0})}return n}var s=[".DS_Store","Thumbs.db"];function f(e){return"object"==typeof e&&null!==e}function p(e){return e.filter(function(e){return -1===s.indexOf(e.name)})}function d(e){if(null===e)return[];for(var t=[],n=0;n<e.length;n++){var r=e[n];t.push(r)}return t}function v(e){if("function"!=typeof e.webkitGetAsEntry)return g(e);var t=e.webkitGetAsEntry();return t&&t.isDirectory?y(t):g(e)}function g(e){var t=e.getAsFile();return t?Promise.resolve(l(t)):Promise.reject(e+" is not a File")}function m(e){return a(this,void 0,void 0,function(){return c(this,function(t){return[2,e.isDirectory?y(e):function(e){return a(this,void 0,void 0,function(){return c(this,function(t){return[2,new Promise(function(t,n){e.file(function(n){t(l(n,e.fullPath))},function(e){n(e)})})]})})}(e)]})})}function y(e){var t=e.createReader();return new Promise(function(e,n){var r=[];!function o(){var i=this;t.readEntries(function(t){return a(i,void 0,void 0,function(){var i;return c(this,function(a){switch(a.label){case 0:if(t.length)return[3,5];a.label=1;case 1:return a.trys.push([1,3,,4]),[4,Promise.all(r)];case 2:return e(a.sent()),[3,4];case 3:return n(a.sent()),[3,4];case 4:return[3,6];case 5:i=Promise.all(t.map(m)),r.push(i),o(),a.label=6;case 6:return[2]}})})},function(e){n(e)})}()})}var b=n(34343);function h(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),n.push.apply(n,r)}return n}function w(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?h(Object(n),!0).forEach(function(t){D(e,t,n[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):h(Object(n)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))})}return e}function D(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function x(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){var n,r,o=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=o){var i=[],a=!0,c=!1;try{for(o=o.call(e);!(a=(n=o.next()).done)&&(i.push(n.value),!t||i.length!==t);a=!0);}catch(e){c=!0,r=e}finally{try{a||null==o.return||o.return()}finally{if(c)throw r}}return i}}(e,t)||function(e,t){if(e){if("string"==typeof e)return O(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);if("Object"===n&&e.constructor&&(n=e.constructor.name),"Map"===n||"Set"===n)return Array.from(e);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return O(e,t)}}(e,t)||function(){throw TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function O(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=Array(t);n<t;n++)r[n]=e[n];return r}var j=function(e){var t=Array.isArray(e=Array.isArray(e)&&1===e.length?e[0]:e)?"one of ".concat(e.join(", ")):e;return{code:"file-invalid-type",message:"File type must be ".concat(t)}},A=function(e){return{code:"file-too-large",message:"File is larger than ".concat(e," ").concat(1===e?"byte":"bytes")}},F=function(e){return{code:"file-too-small",message:"File is smaller than ".concat(e," ").concat(1===e?"byte":"bytes")}},k={code:"too-many-files",message:"Too many files"};function E(e,t){var n="application/x-moz-file"===e.type||(0,b.A)(e,t);return[n,n?null:j(t)]}function P(e,t,n){if(S(e.size)){if(S(t)&&S(n)){if(e.size>n)return[!1,A(n)];if(e.size<t)return[!1,F(t)]}else if(S(t)&&e.size<t)return[!1,F(t)];else if(S(n)&&e.size>n)return[!1,A(n)]}return[!0,null]}function S(e){return null!=e}function C(e){return"function"==typeof e.isPropagationStopped?e.isPropagationStopped():void 0!==e.cancelBubble&&e.cancelBubble}function z(e){return e.dataTransfer?Array.prototype.some.call(e.dataTransfer.types,function(e){return"Files"===e||"application/x-moz-file"===e}):!!e.target&&!!e.target.files}function R(e){e.preventDefault()}function T(){for(var e=arguments.length,t=Array(e),n=0;n<e;n++)t[n]=arguments[n];return function(e){for(var n=arguments.length,r=Array(n>1?n-1:0),o=1;o<n;o++)r[o-1]=arguments[o];return t.some(function(t){return!C(e)&&t&&t.apply(void 0,[e].concat(r)),C(e)})}}var I=["children"],L=["open"],B=["refKey","role","onKeyDown","onFocus","onBlur","onClick","onDragEnter","onDragOver","onDragLeave","onDrop"],K=["refKey","onChange","onClick"];function M(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){var n,r,o=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=o){var i=[],a=!0,c=!1;try{for(o=o.call(e);!(a=(n=o.next()).done)&&(i.push(n.value),!t||i.length!==t);a=!0);}catch(e){c=!0,r=e}finally{try{a||null==o.return||o.return()}finally{if(c)throw r}}return i}}(e,t)||_(e,t)||function(){throw TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function _(e,t){if(e){if("string"==typeof e)return N(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);if("Object"===n&&e.constructor&&(n=e.constructor.name),"Map"===n||"Set"===n)return Array.from(e);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return N(e,t)}}function N(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=Array(t);n<t;n++)r[n]=e[n];return r}function $(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),n.push.apply(n,r)}return n}function G(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?$(Object(n),!0).forEach(function(t){U(e,t,n[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):$(Object(n)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))})}return e}function U(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function q(e,t){if(null==e)return{};var n,r,o=function(e,t){if(null==e)return{};var n,r,o={},i=Object.keys(e);for(r=0;r<i.length;r++)n=i[r],t.indexOf(n)>=0||(o[n]=e[n]);return o}(e,t);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);for(r=0;r<i.length;r++)n=i[r],!(t.indexOf(n)>=0)&&Object.prototype.propertyIsEnumerable.call(e,n)&&(o[n]=e[n])}return o}var W=(0,r.forwardRef)(function(e,t){var n=e.children,o=Y(q(e,I)),i=o.open,a=q(o,L);return(0,r.useImperativeHandle)(t,function(){return{open:i}},[i]),r.createElement(r.Fragment,null,n(G(G({},a),{},{open:i})))});W.displayName="Dropzone";var H={disabled:!1,getFilesFromEvent:function(e){return a(this,void 0,void 0,function(){return c(this,function(t){var n;if(f(e)&&f(e.dataTransfer))return[2,function(e,t){return a(this,void 0,void 0,function(){var n;return c(this,function(r){switch(r.label){case 0:if(null===e)return[2,[]];if(!e.items)return[3,2];if(n=d(e.items).filter(function(e){return"file"===e.kind}),"drop"!==t)return[2,n];return[4,Promise.all(n.map(v))];case 1:return[2,p(function e(t){return t.reduce(function(t,n){return function(){for(var e=[],t=0;t<arguments.length;t++)e=e.concat(function(e,t){var n="function"==typeof Symbol&&e[Symbol.iterator];if(!n)return e;var r,o,i=n.call(e),a=[];try{for(;(void 0===t||t-- >0)&&!(r=i.next()).done;)a.push(r.value)}catch(e){o={error:e}}finally{try{r&&!r.done&&(n=i.return)&&n.call(i)}finally{if(o)throw o.error}}return a}(arguments[t]));return e}(t,Array.isArray(n)?e(n):[n])},[])}(r.sent()))];case 2:return[2,p(d(e.files).map(function(e){return l(e)}))]}})})}(e.dataTransfer,e.type)];if(f(n=e)&&f(n.target))return[2,d(e.target.files).map(function(e){return l(e)})];return Array.isArray(e)&&e.every(function(e){return"getFile"in e&&"function"==typeof e.getFile})?[2,function(e){return a(this,void 0,void 0,function(){return c(this,function(t){switch(t.label){case 0:return[4,Promise.all(e.map(function(e){return e.getFile()}))];case 1:return[2,t.sent().map(function(e){return l(e)})]}})})}(e)]:[2,[]]})})},maxSize:1/0,minSize:0,multiple:!0,maxFiles:0,preventDropOnDocument:!0,noClick:!1,noKeyboard:!1,noDrag:!1,noDragEventsBubbling:!1,validator:null,useFsAccessApi:!0};W.defaultProps=H,W.propTypes={children:i().func,accept:i().oneOfType([i().string,i().arrayOf(i().string)]),multiple:i().bool,preventDropOnDocument:i().bool,noClick:i().bool,noKeyboard:i().bool,noDrag:i().bool,noDragEventsBubbling:i().bool,minSize:i().number,maxSize:i().number,maxFiles:i().number,disabled:i().bool,getFilesFromEvent:i().func,onFileDialogCancel:i().func,onFileDialogOpen:i().func,useFsAccessApi:i().bool,onDragEnter:i().func,onDragLeave:i().func,onDragOver:i().func,onDrop:i().func,onDropAccepted:i().func,onDropRejected:i().func,validator:i().func};var V={isFocused:!1,isFileDialogActive:!1,isDragActive:!1,isDragAccept:!1,isDragReject:!1,draggedFiles:[],acceptedFiles:[],fileRejections:[]};function Y(){var e,t,n,o,i,a,c,u=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},l=G(G({},H),u),s=l.accept,f=l.disabled,p=l.getFilesFromEvent,d=l.maxSize,v=l.minSize,g=l.multiple,m=l.maxFiles,y=l.onDragEnter,b=l.onDragLeave,h=l.onDragOver,O=l.onDrop,j=l.onDropAccepted,A=l.onDropRejected,F=l.onFileDialogCancel,S=l.onFileDialogOpen,I=l.useFsAccessApi,L=l.preventDropOnDocument,$=l.noClick,W=l.noKeyboard,Y=l.noDrag,X=l.noDragEventsBubbling,Z=l.validator,ee=(0,r.useMemo)(function(){return"function"==typeof S?S:Q},[S]),et=(0,r.useMemo)(function(){return"function"==typeof F?F:Q},[F]),en=(0,r.useRef)(null),er=(0,r.useRef)(null),eo=M((0,r.useReducer)(J,V),2),ei=eo[0],ea=eo[1],ec=ei.isFocused,eu=ei.isFileDialogActive,el=ei.draggedFiles,es=(0,r.useRef)("undefined"!=typeof window&&window.isSecureContext&&I&&"showOpenFilePicker"in window),ef=function(){!es.current&&eu&&setTimeout(function(){er.current&&(er.current.files.length||(ea({type:"closeDialog"}),et()))},300)};(0,r.useEffect)(function(){return window.addEventListener("focus",ef,!1),function(){window.removeEventListener("focus",ef,!1)}},[er,eu,et,es]);var ep=(0,r.useRef)([]),ed=function(e){en.current&&en.current.contains(e.target)||(e.preventDefault(),ep.current=[])};(0,r.useEffect)(function(){return L&&(document.addEventListener("dragover",R,!1),document.addEventListener("drop",ed,!1)),function(){L&&(document.removeEventListener("dragover",R),document.removeEventListener("drop",ed))}},[en,L]);var ev=(0,r.useCallback)(function(e){var t;e.preventDefault(),e.persist(),ek(e),ep.current=[].concat(function(e){if(Array.isArray(e))return N(e)}(t=ep.current)||function(e){if("undefined"!=typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)}(t)||_(t)||function(){throw TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}(),[e.target]),z(e)&&Promise.resolve(p(e)).then(function(t){(!C(e)||X)&&(ea({draggedFiles:t,isDragActive:!0,type:"setDraggedFiles"}),y&&y(e))})},[p,y,X]),eg=(0,r.useCallback)(function(e){e.preventDefault(),e.persist(),ek(e);var t=z(e);if(t&&e.dataTransfer)try{e.dataTransfer.dropEffect="copy"}catch(e){}return t&&h&&h(e),!1},[h,X]),em=(0,r.useCallback)(function(e){e.preventDefault(),e.persist(),ek(e);var t=ep.current.filter(function(e){return en.current&&en.current.contains(e)}),n=t.indexOf(e.target);-1!==n&&t.splice(n,1),ep.current=t,!(t.length>0)&&(ea({isDragActive:!1,type:"setDraggedFiles",draggedFiles:[]}),z(e)&&b&&b(e))},[en,b,X]),ey=(0,r.useCallback)(function(e,t){var n=[],r=[];e.forEach(function(e){var t=M(E(e,s),2),o=t[0],i=t[1],a=M(P(e,v,d),2),c=a[0],u=a[1],l=Z?Z(e):null;if(o&&c&&!l)n.push(e);else{var f=[i,u];l&&(f=f.concat(l)),r.push({file:e,errors:f.filter(function(e){return e})})}}),(!g&&n.length>1||g&&m>=1&&n.length>m)&&(n.forEach(function(e){r.push({file:e,errors:[k]})}),n.splice(0)),ea({acceptedFiles:n,fileRejections:r,type:"setFiles"}),O&&O(n,r,t),r.length>0&&A&&A(r,t),n.length>0&&j&&j(n,t)},[ea,g,s,v,d,m,O,j,A,Z]),eb=(0,r.useCallback)(function(e){e.preventDefault(),e.persist(),ek(e),ep.current=[],z(e)&&Promise.resolve(p(e)).then(function(t){(!C(e)||X)&&ey(t,e)}),ea({type:"reset"})},[p,ey,X]),eh=(0,r.useCallback)(function(){if(es.current){ea({type:"openDialog"}),ee();var e,t={multiple:g,types:[{description:"everything",accept:Array.isArray(e="string"==typeof(e=s)?e.split(","):e)?e.filter(function(e){return"audio/*"===e||"video/*"===e||"image/*"===e||"text/*"===e||/\w+\/[-+.\w]+/g.test(e)}).reduce(function(e,t){return w(w({},e),{},D({},t,[]))},{}):{}}]};window.showOpenFilePicker(t).then(function(e){return p(e)}).then(function(e){ey(e,null),ea({type:"closeDialog"})}).catch(function(e){e instanceof DOMException&&("AbortError"===e.name||e.code===e.ABORT_ERR)?(et(e),ea({type:"closeDialog"})):e instanceof DOMException&&("SecurityError"===e.name||e.code===e.SECURITY_ERR)&&(es.current=!1,er.current&&(er.current.value=null,er.current.click()))});return}er.current&&(ea({type:"openDialog"}),ee(),er.current.value=null,er.current.click())},[ea,ee,et,I,ey,s,g]),ew=(0,r.useCallback)(function(e){en.current&&en.current.isEqualNode(e.target)&&(" "===e.key||"Enter"===e.key||32===e.keyCode||13===e.keyCode)&&(e.preventDefault(),eh())},[en,eh]),eD=(0,r.useCallback)(function(){ea({type:"focus"})},[]),ex=(0,r.useCallback)(function(){ea({type:"blur"})},[]),eO=(0,r.useCallback)(function(){$||(function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:window.navigator.userAgent;return -1!==e.indexOf("MSIE")||-1!==e.indexOf("Trident/")||-1!==e.indexOf("Edge/")}()?setTimeout(eh,0):eh())},[$,eh]),ej=function(e){return f?null:e},eA=function(e){return W?null:ej(e)},eF=function(e){return Y?null:ej(e)},ek=function(e){X&&e.stopPropagation()},eE=(0,r.useMemo)(function(){return function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=e.refKey,n=e.role,r=e.onKeyDown,o=e.onFocus,i=e.onBlur,a=e.onClick,c=e.onDragEnter,u=e.onDragOver,l=e.onDragLeave,s=e.onDrop,p=q(e,B);return G(G(U({onKeyDown:eA(T(r,ew)),onFocus:eA(T(o,eD)),onBlur:eA(T(i,ex)),onClick:ej(T(a,eO)),onDragEnter:eF(T(c,ev)),onDragOver:eF(T(u,eg)),onDragLeave:eF(T(l,em)),onDrop:eF(T(s,eb)),role:"string"==typeof n&&""!==n?n:"button"},void 0===t?"ref":t,en),f||W?{}:{tabIndex:0}),p)}},[en,ew,eD,ex,eO,ev,eg,em,eb,W,Y,f]),eP=(0,r.useCallback)(function(e){e.stopPropagation()},[]),eS=(0,r.useMemo)(function(){return function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=e.refKey,n=e.onChange,r=e.onClick,o=q(e,K);return G(G({},U({accept:s,multiple:g,type:"file",style:{display:"none"},onChange:ej(T(n,eb)),onClick:ej(T(r,eP)),tabIndex:-1},void 0===t?"ref":t,er)),o)}},[er,s,g,eb,f]),eC=el.length,ez=eC>0&&(t=(e={files:el,accept:s,minSize:v,maxSize:d,multiple:g,maxFiles:m}).files,n=e.accept,o=e.minSize,i=e.maxSize,a=e.multiple,c=e.maxFiles,(!!a||!(t.length>1))&&(!a||!(c>=1)||!(t.length>c))&&t.every(function(e){var t=x(E(e,n),1)[0],r=x(P(e,o,i),1)[0];return t&&r}));return G(G({},ei),{},{isDragAccept:ez,isDragReject:eC>0&&!ez,isFocused:ec&&!f,getRootProps:eE,getInputProps:eS,rootRef:en,inputRef:er,open:ej(eh)})}function J(e,t){switch(t.type){case"focus":return G(G({},e),{},{isFocused:!0});case"blur":return G(G({},e),{},{isFocused:!1});case"openDialog":return G(G({},V),{},{isFileDialogActive:!0});case"closeDialog":return G(G({},e),{},{isFileDialogActive:!1});case"setDraggedFiles":var n=t.isDragActive,r=t.draggedFiles;return G(G({},e),{},{draggedFiles:r,isDragActive:n});case"setFiles":return G(G({},e),{},{acceptedFiles:t.acceptedFiles,fileRejections:t.fileRejections});case"reset":return G({},V);default:return e}}function Q(){}},34343:(e,t)=>{t.A=function(e,t){if(e&&t){var n=Array.isArray(t)?t:t.split(",");if(0===n.length)return!0;var r=e.name||"",o=(e.type||"").toLowerCase(),i=o.replace(/\/.*$/,"");return n.some(function(e){var t=e.trim().toLowerCase();return"."===t.charAt(0)?r.toLowerCase().endsWith(t):t.endsWith("/*")?i===t.replace(/\/.*$/,""):o===t})}return!0}}}]);
//# sourceMappingURL=7336-cff709ed5b1c5c46.js.map