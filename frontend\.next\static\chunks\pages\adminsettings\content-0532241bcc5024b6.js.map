{"version": 3, "file": "static/chunks/pages/adminsettings/content-0532241bcc5024b6.js", "mappings": "qNAcA,IAAMA,EAAQ,CACZ,CACEC,IAAK,YACLC,MAAO,YACT,EACA,CACED,IAAK,cACLC,MAAO,eACT,EACA,CACED,IAAK,QACLC,MAAO,QACT,EACA,CACED,IAAK,UACLC,MAAO,UACT,EACA,CACED,IAAK,UACLC,MAAO,SACT,EACA,CACED,IAAK,SACLC,MAAO,gBACT,EACD,CA2CD,EAxC2B,OAAC,YAACC,CAAU,QAwCxBC,EAxC0BC,CAAQ,eAwChBD,EAAC,GAxCiBE,CAAkB,SAAEC,CAAO,YAAEC,CAAU,CAA2B,GAC7G,CAAEC,GAAC,CAAE,CAAGC,CAAAA,EAAAA,EAAAA,EAAAA,CAAcA,CAAC,UAC7B,MACE,UAACC,EAAAA,CAASA,CAAAA,CAACC,KAAK,IAACC,UAAU,eACzB,WAACC,EAAAA,CAAGA,CAAAA,WACF,UAACC,EAAAA,CAAGA,CAAAA,CAACC,GAAI,EAAGC,GAAI,EAAGJ,UAAU,+BAC3B,UAACK,EAAAA,CAASA,CAAAA,UACV,UAACC,EAAAA,CAAWA,CAAAA,CACVC,KAAK,OACLP,UAAU,cACVQ,YAAaZ,EAAE,qCACfa,aAAW,SACXC,MAAOpB,EACPqB,SAAUnB,QAId,UAACU,EAAAA,CAAGA,CAAAA,CAACC,GAAI,EAAGC,GAAI,WACd,WAACC,EAAAA,CAASA,CAAAA,CAACO,GAAIX,EAAAA,CAAGA,WAChB,UAACY,EAAAA,CAASA,CAAAA,CAACC,MAAM,IAACC,GAAI,EAAGC,GAAI,EAAGhB,UAAU,gBAAQJ,EAAE,qCACpD,UAACM,EAAAA,CAAGA,CAAAA,CAACF,UAAU,mBACb,UAACM,EAAAA,CAAWA,CAAAA,CACVM,GAAG,SACHH,aAAW,OACXE,SAAWM,GAAMxB,EAAmBwB,GACpCP,MAAOf,WACNR,EAAM+B,GAAG,CAAC,CAACC,EAAMC,IAEd,UAACC,SAAAA,CAAmBX,MAAOS,EAAK/B,GAAG,UAAG+B,EAAK9B,KAAK,EAAnC+B,iBAUjC,mBC/EA,4CACA,yBACA,WACA,OAAe,EAAQ,KAAoD,CAC3E,EACA,SAFsB,oGCiCtB,SAASE,EAASC,CAAoB,EACpC,GAAM,CAAE3B,CAAC,CAAE,CAAGC,CAAAA,EAAAA,EAAAA,EAAAA,CAAcA,CAAC,UACvB2B,EAA6B,CACjCC,gBAAiB7B,EAAE,cACnB,EACI,SACJ8B,CAAO,CACPC,MAAI,WACJC,CAAS,uBACTC,CAAqB,WACrBC,CAAS,oBACTC,CAAkB,qBAClBC,CAAmB,kBACnBC,CAAgB,aAChBC,CAAW,oBACXC,CAAkB,gBAClBC,CAAc,SACdC,CAAO,WACPC,CAAS,sBACTC,CAAoB,CACpBC,mBAAiB,CACjBC,YAAU,QACVC,CAAM,kBACNC,CAAgB,cAChBC,CAAY,CAEZ,CADA,EACGC,EACJ,CAAGtB,EAGEuB,EAAiB,4BACrBtB,EACAuB,gBAAiBnD,EAAE,IAP0C,MAQ7DoD,UAAU,UACVtB,EACAC,KAAMA,GAAQ,EAAE,CAChBsB,OAAO,EACPC,2BAA4BrB,EAC5BsB,UAAWrB,EACXsB,gBAAiBf,qBACjBN,EACAsB,YAAY,EACZC,iBAAkBhB,EAClBiB,kBAAmBpB,GAA0C,GAC7DqB,eADwCrB,cACVD,GAA4B,CAAC,GAAI,GAAI,GAAI,CAA3BA,EAA+B,GAAG,CAC9EuB,oBAAqB7B,EACrB8B,oBAAqB1B,EACrB2B,aAAc1B,iBACdG,EACAG,uBACAC,oBACAoB,kBAAmB,UAACC,EAAAA,CAAYA,CAAAA,CAAAA,GAChCC,SAAU,UAACC,IAAAA,CAAE/D,UAAU,6CACvByC,SACAC,eACAE,mBACAD,EACA3C,UAAW,WACb,EACA,MACE,UAACgE,EAAAA,EAASA,CAAAA,CAAE,GAAGlB,CAAc,EAEjC,CAEAxB,EAAS2C,YAAY,CAAG,CACtBd,WAAW,EACXE,YAAY,EACZzB,UAAW,KACXU,WAAW,EACXC,qBAAsB,KACtBC,mBAAmB,EACnBC,YAAY,EACZE,kBAAkB,CACpB,EAEA,MAAerB,QAAQA,EAAC,+QC/FxB,IAAM4C,EAAwB,CAAC3D,EAAc4D,KACzC,OAAQ5D,GACJ,IAAK,YACD4D,EAAOC,MAAM,CACT,8KACJ,KACJ,KAAK,cACDD,EAAOC,MAAM,CACT,mRACJ,KACJ,KAAK,QACDD,EAAOC,MAAM,CACT,6OACJ,KACJ,KAAK,UACDD,EAAOC,MAAM,CACT,sKACJ,KACJ,KAAK,UACDD,EAAOC,MAAM,CACT,+OACJ,KACJ,KAAK,SACDD,EAAOC,MAAM,CACT,kMACJ,KACJ,SACID,EAAOC,MAAM,CACT,sIAEZ,CACA,OAAOD,CACX,WA4QA,MAtQgB,IACZ,GAAM,CAACE,EAAWC,EAAa,CAAGC,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAgB,EAAE,EACtD,GAAE3E,CAAC,CAAE,CAAGC,CAAAA,EAAAA,EAAAA,EAAAA,CAAcA,CAAC,UACvB,CAACU,EAAMiE,EAAQ,CAAGD,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAS,aACnC,CAAC1C,EAAuB4C,EAAyB,CAAGC,EAAAA,QAAc,EAAU,GAC5E,CAACpF,EAAYqF,EAAc,CAAGD,EAAAA,QAAc,CAAS,IACrD,CAAC9C,EAAWgD,EAAa,CAAGL,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAS,GAC7C,CAACM,EAASC,EAAW,CAAGP,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAS,IACzC,CAAClC,EAAS0C,EAAW,CAAGR,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,EAAU,GAC1C,CAACS,EAAaC,EAAS,CAAGV,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,EAAU,GAC5C,CAACW,EAAaC,EAAe,CAAGZ,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAM,MAC9C,CAACa,EAAaC,EAAe,CAAGd,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAS,GAEjD,CAACe,EAAaC,EAAe,CAAGhB,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAc,MAExDJ,EAAc,CACdqB,MAAOX,EACPY,KAAM,CAAEC,WAAY,MAAO,CAC/B,EAEMhE,EAAU,CACZ,CACIiE,KAAM/F,EAAE,oCACRgG,SAAU,GAAcC,EAAIxG,KAAK,CACjCyG,KAAM,iBACFC,EAAExF,IAAI,CACF,UAACyF,IAAIA,CAACC,KAAM,IAAW,OAAPF,EAAExF,IAAI,CAAC,gBAAeK,GAAI,EAArCoF,EAAwDD,MAAAA,CAAfA,EAAExF,IAAI,CAAC,UAA8BA,MAAAA,CAAtBwF,CAAC,CAACG,CA8O/C,CA9O4DH,EA+OrF,UAAiB,OAAPA,EAAExF,IAAI,EA/OwE,CAAC,KAAWwF,MAAAA,CAARxF,EAAK,KAAS,OAANwF,EAAE3G,GAAG,WAC3F2G,EAAE1G,KAAK,GAGZ,UAAC2G,IAAIA,CAACC,KAAM,IAAS,OAAL1F,EAAK,gBAAeK,GAAI,IAAiBmF,GAApDC,GAAoDD,CAAbxF,EAAK,UAAc,OAANwF,EAAE3G,GAAG,WACzD2G,EAAE1G,KAAK,IAGpB8G,UAAU,CACd,EACA,CACIR,KAAM/F,EAAE,qCACRgG,SAAU,QAAsBC,QAAAA,CAAAA,MAAAA,GAAAA,EAAIO,IAAAA,EAAJP,KAAAA,EAAAA,EAAUQ,GAAVR,KAAUQ,GAAY,IACtDP,KAAM,GAAqBC,EAAEK,IAAI,CAAGL,EAAEK,IAAI,CAACC,QAAQ,CAAG,GACtDF,SAAU,EACd,EACA,CACIR,KAAM/F,EAAE,sCACRgG,SAAU,GAAsBC,EAAIH,UAAU,CAC9CI,KAAM,GAAoBQ,IAAOP,EAAEL,UAAU,EAAEa,MAAM,CAAC,SACtDJ,UAAU,CACd,EACA,CACIR,KAAM/F,EAAE,sCACRgG,SAAU,GAAsBC,EAAIW,UAAU,CAC9CV,KAAM,GAAoBQ,IAAOP,EAAES,UAAU,EAAED,MAAM,CAAC,SACtDJ,UAAU,CADsBG,EAGpC,CACIX,KAAM/F,EAAE,qCACRgG,SAAU,GAAsBC,EAAIzG,GAAG,CACvC+G,UAAU,EACVL,KAAM,QAEAR,EAAgDA,EAAuES,EAclHT,EAAgDA,QAfvD,+BACC,CAACA,OAAAA,GAAAA,OAAAA,EAAAA,EAAamB,KAAAA,EAAbnB,GAAAA,EAAAA,EAAAA,EAAoBoB,GAApBpB,KAA4B,CAAC,yBAAmBA,GAAAA,OAAAA,EAAAA,EAAamB,KAAAA,EAAbnB,GAAAA,EAAAA,EAAAA,EAAoBoB,GAApBpB,KAA4B,CAAC,mBAAgB,EAAMA,OAAAA,EAAAA,KAAAA,EAAAA,EAAalG,GAAAA,UAAbkG,GAAoBS,MAAAA,GAAAA,EAAAA,IAAGK,EAAHL,KAAAA,EAAAA,EAAS3G,GAAT2G,EACpH,WAACY,MAAAA,WACE,UAACX,IAAIA,CAACC,KAAM,IAAS,OAAL1F,EAAK,gBAAeK,GAAI,IAAiBmF,GAApDC,GAAoDD,CAAbxF,EAAK,UAAc,OAANwF,EAAE3G,GAAG,WAE1D,UAAC2E,IAAAA,CAAE/D,UAAU,uBAEV,OAEP,UAACgG,IAAIA,CAACC,KAAK,IAAIW,QAAU3F,GAAM4F,EAAWd,EAAG9E,YAEzC,UAAC8C,IAAAA,CAAE/D,UAAU,+BAIpB,CAAEsF,MAAAA,GAAAA,OAAAA,EAAAA,EAAamB,KAAAA,EAAbnB,GAAAA,EAAAA,EAAAA,EAAoBoB,GAApBpB,KAA4B,CAAC,yBAAmBA,GAAAA,OAAAA,EAAAA,EAAamB,KAAAA,EAAbnB,GAAAA,EAAAA,EAAAA,EAAoBoB,GAApBpB,KAA4B,CAAC,kBAAgB,CAavF,GAZR,WAACqB,MAAAA,WACE,UAACX,IAAIA,CAACC,KAAM,IAAS,OAAL1F,EAAK,gBAAeK,GAAI,IAAiBmF,GAApDC,GAAoDD,CAAbxF,EAAK,UAAc,OAANwF,EAAE3G,GAAG,WAE1D,UAAC2E,IAAAA,CAAE/D,UAAU,uBAEV,OAEP,UAACgG,IAAIA,CAACC,KAAK,IAAIW,QAAS,GAAOC,EAAWd,EAAG9E,YAEzC,UAAC8C,IAAAA,CAAE/D,UAAU,iCAQjC,EACH,CAEK8G,EAAY,MAAO3C,IACrBY,GAAW,GACXZ,EAASD,EAAsB3D,EAAM4D,GACrC,IAAM4C,EAAsB,MAAMC,EAAAA,CAAUA,CAACC,IAAI,CAAC,uBAAwB,CAAC,GACvEF,GAAuBA,EAAoBV,QAAQ,EAAE,EACtCU,GAEnB,IAAMG,EAAuC,MAAMF,EAAAA,CAAUA,CAACG,GAAG,CAAC,IAAS,OAAL5G,GAAQ4D,GAC1E+C,GAAYA,EAASvF,IAAI,EAAIyF,MAAMC,OAAO,CAACH,EAASvF,IAAI,GAAKuF,EAASvF,IAAI,CAAC2F,MAAM,CAAG,GAAG,EAC1EJ,EAASvF,IAAI,EAC1BiD,EAAasC,EAASK,UAAU,EAAI,IAEpCjD,EAAa,EAAE,EAEnBS,GAAW,EACf,EAEM8B,EAAa,MAAOhB,EAAkB5E,KACxCA,EAAEuG,cAAc,GAChBrC,EAAe,CAAEsC,GAAI5B,EAAIzG,GAAG,CAAEmB,KAAMA,CAAK,GACzC0E,GAAS,EACb,EAWMjD,EAAsB,MAAO0F,EAAoBC,KACnD5C,GAAW,GACXZ,EAAOqB,KAAK,CAAGkC,EACfvD,EAAOwD,IAAI,CAAGA,EACdtC,EAAesC,GACfxD,EAASD,EAAsB3D,EAAM4D,GACrC,IAAM+C,EAAuC,MAAMF,EAAAA,CAAUA,CAACG,GAAG,CAAC,IAAS,OAAL5G,GAAQ4D,GAC1E+C,GAAYE,MAAMC,OAAO,CAACH,EAASvF,IAAI,GAAG,CAC1C2C,EAAa4C,EAASvF,IAAI,EAC1BmD,EAAW4C,GACX3C,GAAW,GAEnB,EAEM6C,EAAY,IAAM3C,GAAS,GAE3B4C,EAAe,UACjB,GAAI,CACA,MAAMb,EAAAA,CAAUA,CAACc,MAAM,CAAC,IAAwB5C,MAAAA,CAApBA,EAAY3E,IAAI,CAAC,KAAkB,OAAf2E,EAAYuC,EAAE,GAC9DtD,EAAOwD,IAAI,CAAGvC,EACd0B,EAAU3C,GACVc,EAAS,IACT8C,EAAAA,EAAKA,CAACC,OAAO,CAACpI,EAAE,yDACpB,CAAE,MAAOqI,EAAO,CACZF,EAAAA,EAAKA,CAACE,KAAK,CAACrI,EAAE,mDAClB,CACJ,EAEAsI,CAAAA,EAAAA,EAAAA,SAAAA,CAASA,CAAC,KACNpB,EAAU3C,EACd,EAAG,CAAC5D,EAAK,EAET,IAAM4H,EAAa,MAAOrH,EAAasH,KACnCrD,GAAW,GACXZ,EAAOsB,IAAI,CAAG,CACV,CAAC3E,EAAO8E,QAAQ,CAAC,CAAEwC,CACvB,EACA,MAAMtB,EAAU3C,GAChBY,GAAW,EACf,EAEMsD,EAAyB3D,EAAAA,OAAa,CAAC,KAQzC,IAAM4D,EAAyB,IAC3B9D,EAAQ+D,EACZ,EAEMC,EAAY,IACVC,GAAG,GACIC,KAAK,CAAG,CAAErJ,MAAOoJ,EAAE,EAE9B3B,EAAU3C,EACd,EAEMwE,EAAoBC,IAAAA,QAAU,CAAC,GAAeJ,EAAUC,GAAII,OAAOC,KAAgC,GAAK,KAO9G,MACI,UAACvJ,EAAAA,OAAkBA,CAAAA,CACfC,SAPa,CAOHuJ,GANdpE,EAAc1D,EAAE+H,MAAM,CAACtI,KAAK,EAC5BiI,EAAkB1H,EAAE+H,MAAM,CAACtI,KAAK,CACpC,EAKQhB,QA5BY,CA4BHuJ,IA3BT3J,IACAmF,EAAyB,CAAC5C,GAC1B8C,EAFY,IAIpB,EAwBQrF,WAAYA,EACZG,mBAAoB,GAA6C6I,EAAuBrH,EAAE+H,MAAM,CAACtI,KAAK,EACtGf,WAAYY,GAGxB,EAAG,CAACjB,EAAYiB,EAAMsB,EAAsB,EAE5C,MACI,WAAC/B,EAAAA,CAASA,CAAAA,CAACoJ,MAAO,CAAEC,UAAW,QAAS,EAAGpJ,KAAK,IAACC,UAAU,gBACvD,UAACC,EAAAA,CAAGA,CAAAA,UACA,UAACC,EAAAA,CAAGA,CAAAA,CAACC,GAAI,YACL,UAACiJ,EAAAA,CAAWA,CAAAA,CAAC/J,MAAOO,EAAE,4CAG9B,UAACK,EAAAA,CAAGA,CAAAA,CAACD,UAAU,gBACX,WAACE,EAAAA,CAAGA,CAAAA,CAACC,GAAI,aACL,WAACkJ,EAAAA,CAAKA,CAAAA,CAACC,KAAMtE,EAAauE,OAAQ3B,YAC9B,UAACyB,EAAAA,CAAKA,CAACG,MAAM,EAACC,WAAW,aACrB,UAACJ,EAAAA,CAAKA,CAACK,KAAK,WAAE9J,EAAE,gDAEpB,UAACyJ,EAAAA,CAAKA,CAACM,IAAI,WAAE/J,EAAE,mEACf,WAACyJ,EAAAA,CAAKA,CAACO,MAAM,YACT,UAACC,EAAAA,CAAMA,CAAAA,CAACC,QAAQ,YAAYlD,QAASgB,WAChChI,EAAE,uCAEP,UAACiK,EAAAA,CAAMA,CAAAA,CAACC,QAAQ,UAAUlD,QAASiB,WAC9BjI,EAAE,0CAIf,UAAC0B,EAAAA,CAAQA,CAAAA,CACLI,QAASA,EACTW,QAASA,EACTV,KAAM0C,EACNzC,UAAWA,EACXO,mBAAoB0C,EACpB/C,SAAS,IACTY,OAAQyF,EACR1F,UAAU,IACVH,UAAW,GACXP,mBAAoBsG,EACpB1F,gBAAgB,IAChBd,sBAAuBA,EACvBG,oBAAqBA,EACrBC,iBA5HM0F,CA4HY1F,GA3HlCkC,EAAOwD,IAAI,CAAGA,EACK,IAAI,CAAnBrI,IACA6E,EAAOuE,KAAK,CAAG,CAAErJ,MAAOC,EAAW,EAEvC+F,EAAesC,GACfb,EAAU3C,EACd,WA2HJ,gEC3Se,SAASiF,EAAY7H,CAAuB,EACzD,MACE,UAACwI,KAAAA,CAAG/J,UAAU,wBAAgBuB,EAAMlC,KAAK,EAE7C", "sources": ["webpack://_N_E/./pages/adminsettings/content/ContentTableFilter.tsx", "webpack://_N_E/?4df5", "webpack://_N_E/./components/common/RKITable.tsx", "webpack://_N_E/./pages/adminsettings/content/index.tsx", "webpack://_N_E/./components/common/PageHeading.tsx"], "sourcesContent": ["//Import Library\r\nimport {useEffect, useState} from \"react\";\r\nimport {Col, Container, FormControl, FormGroup, FormLabel, Row} from \"react-bootstrap\";\r\n\r\n//Import services/components\r\nimport { useTranslation } from 'next-i18next';\r\n\r\ninterface ContentTableFilterProps {\r\n  filterText: string;\r\n  onFilter: (e: React.ChangeEvent<HTMLInputElement>) => void;\r\n  onFilterTypeChange: (e: React.ChangeEvent<HTMLSelectElement>) => void;\r\n  onClear: () => void;\r\n  filterType: string;\r\n}\r\nconst types = [\r\n  {\r\n    _id: \"operation\",\r\n    title: \"Operations\"\r\n  },\r\n  {\r\n    _id: \"institution\",\r\n    title: \"Organisations\"\r\n  },\r\n  {\r\n    _id: \"event\",\r\n    title: \"Events\"\r\n  },\r\n  {\r\n    _id: \"project\",\r\n    title: \"Projects\"\r\n  },\r\n  {\r\n    _id: \"updates\",\r\n    title: \"Updates\"\r\n  },\r\n  {\r\n    _id: \"vspace\",\r\n    title: \"Virtual Spaces\"\r\n  }\r\n];\r\n\r\n\r\nconst ContentTableFilter = ({filterText, onFilter, onFilterTypeChange, onClear, filterType }: ContentTableFilterProps) => {\r\n  const { t } = useTranslation('common');\r\n  return (\r\n    <Container fluid className=\"p-0\">\r\n      <Row>\r\n        <Col xs={6} md={4} className=\"ps-0 align-self-end\" >\r\n          <FormGroup>\r\n          <FormControl\r\n            type=\"text\"\r\n            className=\"searchInput\"\r\n            placeholder={t(\"adminsetting.content.table.Search\")}\r\n            aria-label=\"Search\"\r\n            value={filterText}\r\n            onChange={onFilter}\r\n          />\r\n          </FormGroup>\r\n        </Col>\r\n        <Col xs={6} md={4}>\r\n          <FormGroup as={Row}>\r\n            <FormLabel column sm={3} lg={2} className=\"me-2\">{t('adminsetting.content.table.Type')}</FormLabel>\r\n            <Col className=\"ps-md-0\">\r\n              <FormControl\r\n                as=\"select\"\r\n                aria-label=\"Type\"\r\n                onChange={(e) => onFilterTypeChange(e as unknown as React.ChangeEvent<HTMLSelectElement>)}\r\n                value={filterType}>\r\n                {types.map((item, index) => {\r\n                  return (\r\n                    <option key={index} value={item._id}>{item.title}</option>\r\n                  )\r\n                })}\r\n              </FormControl>\r\n            </Col>\r\n          </FormGroup>\r\n        </Col>\r\n      </Row>\r\n    </Container>\r\n  )\r\n};\r\n\r\nexport default ContentTableFilter;\r\n", "\n    (window.__NEXT_P = window.__NEXT_P || []).push([\n      \"/adminsettings/content\",\n      function () {\n        return require(\"private-next-pages/adminsettings/content/index.tsx\");\n      }\n    ]);\n    if(module.hot) {\n      module.hot.dispose(function () {\n        window.__NEXT_P.push([\"/adminsettings/content\"])\n      });\n    }\n  ", "//Import Library\r\nimport React from 'react'\r\nimport DataTable  from 'react-data-table-component';\r\n\r\n//Import services/components\r\nimport { useTranslation } from 'next-i18next';\r\nimport CustomLoader from './CustomLoader';\r\n\r\ninterface RKITableProps {\r\n  columns: any[];\r\n  data: any[];\r\n  totalRows?: number;\r\n  resetPaginationToggle?: boolean;\r\n  subheader?: boolean;\r\n  subHeaderComponent?: React.ReactNode;\r\n  handlePerRowsChange?: (newPerPage: number, page: number) => void;\r\n  handlePageChange?: (page: number) => void;\r\n  rowsPerPage?: number[];\r\n  defaultRowsPerPage?: number;\r\n  selectableRows?: boolean;\r\n  loading?: boolean;\r\n  pagServer?: boolean;\r\n  onSelectedRowsChange?: (selectedRows: any) => void;\r\n  clearSelectedRows?: boolean;\r\n  sortServer?: boolean;\r\n  onSort?: (column: any, sortDirection: string) => void;\r\n  persistTableHead?: boolean;\r\n  sortFunction?: (rows: any[], field: any, direction: 'asc' | 'desc') => any[];\r\n  noHeader?: boolean;\r\n  dense?: boolean;\r\n  pagination?: boolean;\r\n  paginationServer?: boolean;\r\n  paginationTotalRows?: number;\r\n  subHeaderAlign?: string;\r\n  [key: string]: any;\r\n}\r\n\r\nfunction RKITable(props: RKITableProps) {\r\n  const { t } = useTranslation('common');\r\n  const paginationComponentOptions = {\r\n    rowsPerPageText: t('Rowsperpage'),\r\n    };\r\n  const {\r\n    columns,\r\n    data,\r\n    totalRows,\r\n    resetPaginationToggle,\r\n    subheader,\r\n    subHeaderComponent,\r\n    handlePerRowsChange,\r\n    handlePageChange,\r\n    rowsPerPage,\r\n    defaultRowsPerPage,\r\n    selectableRows,\r\n    loading,\r\n    pagServer,\r\n    onSelectedRowsChange,\r\n    clearSelectedRows,\r\n    sortServer,\r\n    onSort,\r\n    persistTableHead,\r\n    sortFunction,\r\n    // Filter out any unknown props that might cause DOM warnings\r\n    ...otherProps\r\n  } = props;\r\n\r\n  // Only pass known DataTable props to avoid DOM warnings\r\n  const dataTableProps = {\r\n    paginationComponentOptions,\r\n    noDataComponent: t(\"NoData\"),\r\n    noHeader: true,\r\n    columns,\r\n    data: data || [],\r\n    dense: true,\r\n    paginationResetDefaultPage: resetPaginationToggle,\r\n    subHeader: subheader,\r\n    progressPending: loading,\r\n    subHeaderComponent,\r\n    pagination: true,\r\n    paginationServer: pagServer,\r\n    paginationPerPage: defaultRowsPerPage ? defaultRowsPerPage : 10,\r\n    paginationRowsPerPageOptions: rowsPerPage ? rowsPerPage : [10, 15, 20, 25, 30],\r\n    paginationTotalRows: totalRows,\r\n    onChangeRowsPerPage: handlePerRowsChange,\r\n    onChangePage: handlePageChange,\r\n    selectableRows,\r\n    onSelectedRowsChange,\r\n    clearSelectedRows,\r\n    progressComponent: <CustomLoader/>,\r\n    sortIcon: <i className=\"sort-icon fas fa-exchange-alt\" />,\r\n    sortServer,\r\n    onSort,\r\n    sortFunction,\r\n    persistTableHead,\r\n    className: \"rki-table\"\r\n  };\r\n  return (\r\n    <DataTable {...dataTableProps} />\r\n  )\r\n};\r\n\r\nRKITable.defaultProps = {\r\n  subHeader: false,\r\n  pagination: true,\r\n  totalRows: null,\r\n  pagServer: true,\r\n  onSelectedRowsChange: null,\r\n  clearSelectedRows: false,\r\n  sortServer: false,\r\n  persistTableHead: false\r\n}\r\n\r\nexport default RKITable;\r\n\r\n", "//Import Library\r\nimport Link from \"next/link\";\r\nimport _ from \"lodash\";\r\nimport React, { useEffect, useState } from \"react\";\r\nimport moment from \"moment\";\r\nimport { <PERSON><PERSON>, But<PERSON>, Container, Row, Col } from \"react-bootstrap\";\r\nimport toast from 'react-hot-toast';\r\n\r\n//Import services/components\r\nimport RKITable from \"../../../components/common/RKITable\";\r\nimport apiService from \"../../../services/apiService\";\r\nimport ContentTableFilter from \"./ContentTableFilter\";\r\nimport PageHeading from \"../../../components/common/PageHeading\";\r\nimport { useTranslation } from 'next-i18next';\r\nimport { serverSideTranslations } from 'next-i18next/serverSideTranslations';\r\nimport { ContentItem, User, ApiResponse, PaginationParams } from \"../../../types\";\r\n\r\nconst getSelectFieldOptions = (type: string, params: PaginationParams) => {\r\n    switch (type) {\r\n        case \"operation\":\r\n            params.select =\r\n                \"-status -start_date -images -timeline -country -world_region -region -hazard -description -end_date -syndrome -hazard_type -partners -images -document -doc_src -images_src\";\r\n            break;\r\n        case \"institution\":\r\n            params.select =\r\n                \"-partners -primary_focal_point -email -status -images -use_default_header -header -twitter -dial_code -telephone -department -unit -contact_name -description -type -networks -expertise -hazard_types -hazards -address -focal_points -website  -document -doc_src -images_src \";\r\n            break;\r\n        case \"event\":\r\n            params.select =\r\n                \"-images -more_info -date -risk_assessment -rki_monitored -officially_validated -laboratory_confirmed -status -syndrome -hazard -hazard_type -country_regions -world_region -country -operation -description -document -doc_src -images_src\";\r\n            break;\r\n        case \"project\":\r\n            params.select =\r\n                \"-vspace_visibility -vspace -institution_invites -partner_institutions -area_of_work -region -country -end_date -start_date -status -funded_by -description -website\";\r\n            break;\r\n        case \"updates\":\r\n            params.select =\r\n                \" -region -country -end_date -start_date -status -category -contact_details -reply -show_as_announcement -technical_guidance -use_in_media_gallery -field_report -media -images -document -doc_src -images_src -location -link -description  \";\r\n            break;\r\n        case \"vspace\":\r\n            params.select =\r\n                \"-vspace_email_invite -file_category -subscribers -images -members -visibility -topic -end_date -start_date -description -images -document -doc_src -images_src -nonMembers -private_user_invite\";\r\n            break;\r\n        default:\r\n            params.select =\r\n                \"-status -start_date -images -timeline -country -world_region -region -hazard -description -end_date -syndrome -hazard_type -partners\";\r\n            break;\r\n    }\r\n    return params;\r\n};\r\n\r\ninterface ContentProps {\r\n    [key: string]: any;\r\n}\r\n\r\nconst Content = (_props: ContentProps) => {\r\n    const [tableData, setTableData] = useState<ContentItem[]>([]);\r\n    const { t } = useTranslation('common');\r\n    const [type, setType] = useState<string>(\"operation\");\r\n    const [resetPaginationToggle, setResetPaginationToggle] = React.useState<boolean>(false);\r\n    const [filterText, setFilterText] = React.useState<string>(\"\");\r\n    const [totalRows, setTotalRows] = useState<number>(0);\r\n    const [perPage, setPerPage] = useState<number>(25);\r\n    const [loading, setLoading] = useState<boolean>(false);\r\n    const [isModalShow, setModal] = useState<boolean>(false);\r\n    const [contentItem, setContentItem] = useState<any>(null);\r\n    const [currentPage, setCurrentPage] = useState<number>(1);\r\n\r\n    const [currentUser, setcurrentUser] = useState<User | null>(null);\r\n\r\n    let params: any = {\r\n        limit: perPage,\r\n        sort: { created_at: \"desc\" },\r\n    };\r\n\r\n    const columns = [\r\n        {\r\n            name: t(\"adminsetting.content.table.Title\"),\r\n            selector: (row: any) => row.title,\r\n            cell: (d: any) =>\r\n                d.type ? (\r\n                    <Link href={`/${d.type}/[...routes]`} as={`/${d.type}/show/${d[modules_func(d)]}/${type}/${d._id}`}>\r\n                        {d.title}\r\n                    </Link>\r\n                ) : (\r\n                    <Link href={`/${type}/[...routes]`} as={`/${type}/show/${d._id}`}>\r\n                        {d.title}\r\n                    </Link>\r\n                ),\r\n            sortable: true,\r\n        },\r\n        {\r\n            name: t(\"adminsetting.content.table.Author\"),\r\n            selector: (row: ContentItem) => row.user?.username || '',\r\n            cell: (d: ContentItem) => (d.user ? d.user.username : \"\"),\r\n            sortable: true,\r\n        },\r\n        {\r\n            name: t(\"adminsetting.content.table.Created\"),\r\n            selector: (row: ContentItem) => row.created_at,\r\n            cell: (d: ContentItem) => moment(d.created_at).format(\"M/D/Y\"),\r\n            sortable: true,\r\n        },\r\n        {\r\n            name: t(\"adminsetting.content.table.Updated\"),\r\n            selector: (row: ContentItem) => row.updated_at,\r\n            cell: (d: ContentItem) => moment(d.updated_at).format(\"M/D/Y\"),\r\n            sortable: true,\r\n        },\r\n        {\r\n            name: t(\"adminsetting.content.table.Action\"),\r\n            selector: (row: ContentItem) => row._id,\r\n            sortable: false,\r\n            cell: (d: ContentItem) => (\r\n                <>\r\n                {(currentUser?.roles?.includes('GENERAL_USER') || currentUser?.roles?.includes('PLATFORM_ADMIN')) && currentUser?._id == d?.user?._id ?\r\n                    (<div>\r\n                        <Link href={`/${type}/[...routes]`} as={`/${type}/edit/${d._id}`}>\r\n\r\n                            <i className=\"icon fas fa-edit\" />\r\n\r\n                        </Link>\r\n                        &nbsp;\r\n                        <Link href=\"#\" onClick={(e) => userAction(d, e)}>\r\n\r\n                            <i className=\"icon fas fa-trash-alt\" />\r\n\r\n                        </Link>\r\n                    </div>) :\r\n                    (!(currentUser?.roles?.includes('GENERAL_USER') || currentUser?.roles?.includes('PLATFORM_ADMIN')) ?\r\n                    (<div>\r\n                        <Link href={`/${type}/[...routes]`} as={`/${type}/edit/${d._id}`}>\r\n\r\n                            <i className=\"icon fas fa-edit\" />\r\n\r\n                        </Link>\r\n                        &nbsp;\r\n                        <Link href=\"#\" onClick={(e) => userAction(d, e)}>\r\n\r\n                            <i className=\"icon fas fa-trash-alt\" />\r\n\r\n                        </Link>\r\n                    </div>): \"\")\r\n                }\r\n                </>\r\n\r\n            ),\r\n        },\r\n    ];\r\n\r\n    const fetchData = async (params: PaginationParams) => {\r\n        setLoading(true);\r\n        params = getSelectFieldOptions(type, params);\r\n        const currentUserResponse = await apiService.post(\"/users/getLoggedUser\", {});\r\n        if (currentUserResponse && currentUserResponse.username) {\r\n            setcurrentUser(currentUserResponse);\r\n        }\r\n        const response: ApiResponse<ContentItem[]> = await apiService.get(`/${type}`, params);\r\n        if (response && response.data && Array.isArray(response.data) && response.data.length > 0) {\r\n            setTableData(response.data);\r\n            setTotalRows(response.totalCount || 0);\r\n        } else {\r\n            setTableData([]);\r\n        }\r\n        setLoading(false);\r\n    };\r\n\r\n    const userAction = async (row: ContentItem, e: React.MouseEvent) => {\r\n        e.preventDefault();\r\n        setContentItem({ id: row._id, type: type });\r\n        setModal(true);\r\n    };\r\n\r\n    const handlePageChange = (page: number) => {\r\n        params.page = page;\r\n        if (filterText !== \"\") {\r\n            params.query = { title: filterText };\r\n        }\r\n        setCurrentPage(page);\r\n        fetchData(params);\r\n    };\r\n\r\n    const handlePerRowsChange = async (newPerPage: number, page: number) => {\r\n        setLoading(true);\r\n        params.limit = newPerPage;\r\n        params.page = page;\r\n        setCurrentPage(page);\r\n        params = getSelectFieldOptions(type, params);\r\n        const response: ApiResponse<ContentItem[]> = await apiService.get(`/${type}`, params);\r\n        if (response && Array.isArray(response.data)) {\r\n            setTableData(response.data);\r\n            setPerPage(newPerPage);\r\n            setLoading(false);\r\n        }\r\n    };\r\n\r\n    const modalHide = () => setModal(false);\r\n\r\n    const modalConfirm = async () => {\r\n        try {\r\n            await apiService.remove(`/${contentItem.type}/${contentItem.id}`);\r\n            params.page = currentPage;\r\n            fetchData(params);\r\n            setModal(false);\r\n            toast.success(t(\"adminsetting.content.table.contentDeletedSuccessfully\"));\r\n        } catch (error) {\r\n            toast.error(t(\"adminsetting.content.table.errorDeletingContent\"));\r\n        }\r\n    };\r\n\r\n    useEffect(() => {\r\n        fetchData(params);\r\n    }, [type]);\r\n\r\n    const handleSort = async (column: any, sortDirection: string) => {\r\n        setLoading(true);\r\n        params.sort = {\r\n            [column.selector]: sortDirection,\r\n        };\r\n        await fetchData(params);\r\n        setLoading(false);\r\n    };\r\n\r\n    const subHeaderComponentMemo = React.useMemo(() => {\r\n        const handleClear = () => {\r\n            if (filterText) {\r\n                setResetPaginationToggle(!resetPaginationToggle);\r\n                setFilterText(\"\");\r\n            }\r\n        };\r\n\r\n        const handleFilterTypeChange = (type_initial: string) => {\r\n            setType(type_initial);\r\n        };\r\n\r\n        const sendQuery = (q: string) => {\r\n            if (q) {\r\n                params.query = { title: q };\r\n            }\r\n            fetchData(params);\r\n        };\r\n\r\n        const handleSearchTitle = _.debounce((q: string) => sendQuery(q), Number(process.env.SEARCH_DEBOUNCE_TIME) || 300);\r\n\r\n        const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {\r\n            setFilterText(e.target.value);\r\n            handleSearchTitle(e.target.value);\r\n        };\r\n\r\n        return (\r\n            <ContentTableFilter\r\n                onFilter={handleChange}\r\n                onClear={handleClear}\r\n                filterText={filterText}\r\n                onFilterTypeChange={(e: React.ChangeEvent<HTMLSelectElement>) => handleFilterTypeChange(e.target.value)}\r\n                filterType={type}\r\n            />\r\n        );\r\n    }, [filterText, type, resetPaginationToggle]);\r\n\r\n    return (\r\n        <Container style={{ overflowX: \"hidden\" }} fluid className=\"p-0\">\r\n            <Row>\r\n                <Col xs={12}>\r\n                    <PageHeading title={t(\"adminsetting.content.table.content\")} />\r\n                </Col>\r\n            </Row>\r\n            <Row className=\"mt-3\">\r\n                <Col xs={12}>\r\n                    <Modal show={isModalShow} onHide={modalHide}>\r\n                        <Modal.Header closeButton>\r\n                            <Modal.Title>{t(\"adminsetting.content.table.DeleteContent\")}</Modal.Title>\r\n                        </Modal.Header>\r\n                        <Modal.Body>{t(\"adminsetting.content.table.Areyousurewanttodeletethiscontent?\")}</Modal.Body>\r\n                        <Modal.Footer>\r\n                            <Button variant=\"secondary\" onClick={modalHide}>\r\n                                {t(\"adminsetting.content.table.Cancel\")}\r\n                            </Button>\r\n                            <Button variant=\"primary\" onClick={modalConfirm}>\r\n                                {t(\"adminsetting.content.table.Yes\")}\r\n                            </Button>\r\n                        </Modal.Footer>\r\n                    </Modal>\r\n                    <RKITable\r\n                        columns={columns}\r\n                        loading={loading}\r\n                        data={tableData}\r\n                        totalRows={totalRows}\r\n                        defaultRowsPerPage={perPage}\r\n                        subheader\r\n                        onSort={handleSort}\r\n                        sortServer\r\n                        pagServer={true}\r\n                        subHeaderComponent={subHeaderComponentMemo}\r\n                        persistTableHead\r\n                        resetPaginationToggle={resetPaginationToggle}\r\n                        handlePerRowsChange={handlePerRowsChange}\r\n                        handlePageChange={handlePageChange}\r\n                    />\r\n                </Col>\r\n            </Row>\r\n        </Container>\r\n    );\r\n};\r\n\r\ninterface ServerSidePropsContext {\r\n  locale: string;\r\n}\r\n\r\nexport async function getServerSideProps({ locale }: ServerSidePropsContext) {\r\n  return {\r\n    props: {\r\n      ...(await serverSideTranslations(locale, ['common'])),\r\n    },\r\n  }\r\n}\r\n\r\nexport default Content;\r\n\r\nfunction modules_func(d: ContentItem) {\r\n    return `parent_${d.type}`;\r\n}\r\n", "interface PageHeadingProps {\r\n  title: string; // Required based on actual usage\r\n}\r\n\r\nexport default function PageHeading(props: PageHeadingProps) {\r\n  return (\r\n    <h2 className=\"page-heading\">{props.title}</h2>\r\n  )\r\n}\r\n"], "names": ["types", "_id", "title", "filterText", "ContentTableFilter", "onFilter", "onFilterTypeChange", "onClear", "filterType", "t", "useTranslation", "Container", "fluid", "className", "Row", "Col", "xs", "md", "FormGroup", "FormControl", "type", "placeholder", "aria-label", "value", "onChange", "as", "FormLabel", "column", "sm", "lg", "e", "map", "item", "index", "option", "RKITable", "props", "paginationComponentOptions", "rowsPerPageText", "columns", "data", "totalRows", "resetPaginationToggle", "subheader", "subHeaderComponent", "handlePerRowsChange", "handlePageChange", "rowsPerPage", "defaultRowsPerPage", "selectableRows", "loading", "pagServer", "onSelectedRowsChange", "clearSelectedRows", "sortServer", "onSort", "persistTableHead", "sortFunction", "otherProps", "dataTableProps", "noDataComponent", "<PERSON><PERSON><PERSON><PERSON>", "dense", "paginationResetDefaultPage", "subHeader", "progressPending", "pagination", "paginationServer", "paginationPerPage", "paginationRowsPerPageOptions", "paginationTotalRows", "onChangeRowsPerPage", "onChangePage", "progressComponent", "CustomLoader", "sortIcon", "i", "DataTable", "defaultProps", "getSelectFieldOptions", "params", "select", "tableData", "setTableData", "useState", "setType", "setResetPaginationToggle", "React", "setFilterText", "setTotalRows", "perPage", "setPerPage", "setLoading", "isModalShow", "setModal", "contentItem", "setContentItem", "currentPage", "setCurrentPage", "currentUser", "setcurrentUser", "limit", "sort", "created_at", "name", "selector", "row", "cell", "d", "Link", "href", "modules_func", "sortable", "user", "username", "moment", "format", "updated_at", "roles", "includes", "div", "onClick", "userAction", "fetchData", "currentUserResponse", "apiService", "post", "response", "get", "Array", "isArray", "length", "totalCount", "preventDefault", "id", "newPerPage", "page", "modalHide", "modalConfirm", "remove", "toast", "success", "error", "useEffect", "handleSort", "sortDirection", "subHeaderComponentMemo", "handleFilterTypeChange", "type_initial", "<PERSON><PERSON><PERSON><PERSON>", "q", "query", "handleSearchTitle", "_", "Number", "process", "handleChange", "target", "handleClear", "style", "overflowX", "PageHeading", "Modal", "show", "onHide", "Header", "closeButton", "Title", "Body", "Footer", "<PERSON><PERSON>", "variant", "h2"], "sourceRoot": "", "ignoreList": []}