"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[8357],{5303:(e,t,n)=>{var i=(Object.defineProperty(t,"__esModule",{value:!0}),t.PrevNextButton=void 0,function(e){return e&&e.__esModule?e:{default:e}}(n(14232))),o=n(36930),a=n(79910);t.PrevNextButton=function(e){var t,n=e.name,r=e.isDisabled,s=e.onClick,u=e.renderPrevButton,e=e.renderNextButton;return"function"==typeof u?i.default.createElement("div",{className:o.Classnames.BUTTON_PREV,onClick:s},u({isDisabled:r})):"function"==typeof e?i.default.createElement("div",{className:o.Classnames.BUTTON_NEXT,onClick:s},e({isDisabled:r})):(e=(u="prev"===n)?"<":">",n=u?o.Classnames.BUTTON_PREV:o.Classnames.BUTTON_NEXT,t=u?o.Classnames.BUTTON_PREV_WRAPPER:o.Classnames.BUTTON_NEXT_WRAPPER,u=u?o.Classnames.BUTTON_PREV_ITEM:o.Classnames.BUTTON_NEXT_ITEM,r=r?o.Modifiers.INACTIVE:"",u=(0,a.concatClassnames)(u,r),i.default.createElement("div",{className:n},i.default.createElement("div",{className:t},i.default.createElement("p",{className:u,onClick:function(e){return s(e)}},i.default.createElement("span",{"data-area":e})))))}},6305:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.debug=void 0,t.debug=function(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t]}},7485:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.PrevNextButton=t.PlayPauseButton=t.DotsNavigation=t.StageItem=t.SlideInfo=void 0;var i=n(67296),o=(Object.defineProperty(t,"SlideInfo",{enumerable:!0,get:function(){return i.SlideInfo}}),n(67272)),a=(Object.defineProperty(t,"StageItem",{enumerable:!0,get:function(){return o.StageItem}}),n(28641)),r=(Object.defineProperty(t,"DotsNavigation",{enumerable:!0,get:function(){return a.DotsNavigation}}),n(56413)),s=(Object.defineProperty(t,"PlayPauseButton",{enumerable:!0,get:function(){return r.PlayPauseButton}}),n(5303));Object.defineProperty(t,"PrevNextButton",{enumerable:!0,get:function(){return s.PrevNextButton}})},8636:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.rotateByAngle=function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0;if(0===t)return e;var n=e.x,i=e.y,o=Math.PI/180*t;return{x:n*Math.cos(o)+i*Math.sin(o),y:i*Math.cos(o)-n*Math.sin(o)}}},21330:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.calculateDirection=function(e){var t,n=i.TraceDirectionKey.NEGATIVE,o=i.TraceDirectionKey.POSITIVE,a=e[e.length-1],r=e[e.length-2]||0;return e.every(function(e){return 0===e})?i.TraceDirectionKey.NONE:(t=a>r?o:n,0===a&&(t=r<0?o:n),t)};var i=n(91305)},22173:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.defaultProps=void 0;var i=n(36930);t.defaultProps={activeIndex:0,animationDuration:400,animationEasingFunction:"ease",animationType:i.AnimationType.SLIDE,autoHeight:!1,autoWidth:!1,autoPlay:!1,autoPlayControls:!1,autoPlayDirection:i.AutoplayDirection.LTR,autoPlayInterval:400,autoPlayStrategy:i.AutoPlayStrategy.DEFAULT,children:void 0,controlsStrategy:i.ControlsStrategy.DEFAULT,disableButtonsControls:!1,disableDotsControls:!1,disableSlideInfo:!0,infinite:!1,innerWidth:void 0,items:void 0,keyboardNavigation:!1,mouseTracking:!1,syncStateOnPropsUpdate:!0,name:"",paddingLeft:0,paddingRight:0,responsive:void 0,swipeDelta:20,swipeExtraPadding:200,ssrSilentMode:!0,touchTracking:!0,touchMoveDefaultEvents:!0,onInitialized:function(){},onResized:function(){},onUpdated:function(){},onResizeEvent:void 0,onSlideChange:function(){},onSlideChanged:function(){}}},25936:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.isVerticalTouchmoveDetected=t.getFadeoutAnimationPosition=t.getFadeoutAnimationIndex=t.getSwipeTouchendIndex=t.getSwipeTouchendPosition=t.getSwipeTransformationCursor=t.getTransformationItemIndex=t.getSwipeShiftValue=t.getItemCoords=t.getIsLeftDirection=t.shouldRecalculateSwipePosition=t.getSwipeLimitMax=t.getSwipeLimitMin=t.shouldCancelSlideAnimation=t.shouldRecalculateSlideIndex=t.getUpdateSlidePositionIndex=t.getActiveIndex=t.getStartIndex=t.getShiftIndex=void 0;var n=(t.getShiftIndex=function(e,t){return(e=void 0===e?0:e)+(t=void 0===t?0:t)},function(e,t){if(void 0===e&&(e=0),t=void 0===t?0:t){if(t<=e)return t-1;if(0<e)return e}return 0}),i=(t.getStartIndex=n,function(e){var n=e.startIndex,n=void 0===n?0:n,i=e.itemsCount,e=e.infinite;return void 0!==e&&e?n:(0,t.getStartIndex)(n,void 0===i?0:i)}),o=(t.getActiveIndex=i,function(e,t){return e<0?t-1:t<=e?0:e}),a=(t.getUpdateSlidePositionIndex=o,function(e,t){return e<0||t<=e}),r=(t.shouldRecalculateSlideIndex=a,function(e,t){return e<0||t<=e}),s=(t.shouldCancelSlideAnimation=r,function(e,t){var n=e.itemsOffset,e=e.transformationSet,e=void 0===e?[]:e,i=t.infinite,t=t.swipeExtraPadding;return i?(e[void 0===n?0:n]||{}).position:Math.min(void 0===t?0:t,void 0===(i=(e[0]||{}).width)?0:i)}),u=(t.getSwipeLimitMin=s,function(e,n){var i=n.infinite,n=n.swipeExtraPadding,n=void 0===n?0:n,o=e.itemsCount,a=e.itemsOffset,r=e.itemsInSlide,r=void 0===r?1:r,e=e.transformationSet,e=void 0===e?[]:e;return i?(e[(void 0===o?1:o)+(0,t.getShiftIndex)(r,void 0===a?0:a)]||{}).position||0:(0,t.getItemCoords)(-r,e).position+n}),l=(t.getSwipeLimitMax=u,function(e,t,n){return-t<=e||Math.abs(e)>=n}),d=(t.shouldRecalculateSwipePosition=l,function(e){return(e=void 0===e?0:e)<0}),c=(t.getIsLeftDirection=d,function(e,t){return(t=void 0===t?[]:t).slice(e=void 0===e?0:e)[0]||{position:0,width:0}}),f=(t.getItemCoords=c,function(e,n){return void 0===e&&(e=0),void 0===n&&(n=[]),(0,t.getItemCoords)(e,n).position}),h=(t.getSwipeShiftValue=f,function(e,t){return void 0===t&&(t=0),(e=void 0===e?[]:e).findIndex(function(e){return e.position>=Math.abs(t)})}),p=(t.getTransformationItemIndex=h,function(e,n,i){return void 0===e&&(e=[]),void 0===n&&(n=0),void 0===i&&(i=0),e=(0,t.getTransformationItemIndex)(e,n),(0,t.getIsLeftDirection)(i)?e:e-1}),v=(t.getSwipeTransformationCursor=p,function(e,n,i){void 0===i&&(i=0);var o=e.infinite,a=e.autoWidth,r=e.isStageContentPartial,s=e.swipeAllowedPositionMax,e=e.transformationSet,i=(0,t.getSwipeTransformationCursor)(e,i,n),n=(0,t.getItemCoords)(i,e).position;if(!o){if(a&&r)return 0;if(s<n)return-s}return-n}),m=(t.getSwipeTouchendPosition=v,function(e,n){var i=n.transformationSet,o=n.itemsInSlide,a=n.itemsOffset,r=n.itemsCount,s=n.infinite,u=n.isStageContentPartial,l=n.activeIndex,n=n.translate3d;return s||!u&&n!==Math.abs(e)?(u=(0,t.getTransformationItemIndex)(i,e),s?u<(n=(0,t.getShiftIndex)(o,a))?r-o-a+u:n+r<=u?u-(n+r):u-n:u):l}),g=(t.getSwipeTouchendIndex=m,function(e){var t=e.infinite,n=e.activeIndex,e=e.itemsInSlide;return t?n+e:n}),y=(t.getFadeoutAnimationIndex=g,function(e,t){var n=t.activeIndex,t=t.stageWidth;return e<n?-((n-e)*t)||0:(e-n)*t||0}),P=(t.getFadeoutAnimationPosition=y,function(e,t,n){return e<(n=void 0===n?0:n)||e<.1*t});t.isVerticalTouchmoveDetected=P},27693:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0});var i=n(21330);Object.keys(i).forEach(function(e){"default"!==e&&"__esModule"!==e&&(e in t&&t[e]===i[e]||Object.defineProperty(t,e,{enumerable:!0,get:function(){return i[e]}}))});var o=n(47464);Object.keys(o).forEach(function(e){"default"!==e&&"__esModule"!==e&&(e in t&&t[e]===o[e]||Object.defineProperty(t,e,{enumerable:!0,get:function(){return o[e]}}))});var a=n(83301);Object.keys(a).forEach(function(e){"default"!==e&&"__esModule"!==e&&(e in t&&t[e]===a[e]||Object.defineProperty(t,e,{enumerable:!0,get:function(){return a[e]}}))});var r=n(87776);Object.keys(r).forEach(function(e){"default"!==e&&"__esModule"!==e&&(e in t&&t[e]===r[e]||Object.defineProperty(t,e,{enumerable:!0,get:function(){return r[e]}}))});var s=n(90950);Object.keys(s).forEach(function(e){"default"!==e&&"__esModule"!==e&&(e in t&&t[e]===s[e]||Object.defineProperty(t,e,{enumerable:!0,get:function(){return s[e]}}))});var u=n(58748);Object.keys(u).forEach(function(e){"default"!==e&&"__esModule"!==e&&(e in t&&t[e]===u[e]||Object.defineProperty(t,e,{enumerable:!0,get:function(){return u[e]}}))});var l=n(51546);Object.keys(l).forEach(function(e){"default"!==e&&"__esModule"!==e&&(e in t&&t[e]===l[e]||Object.defineProperty(t,e,{enumerable:!0,get:function(){return l[e]}}))});var d=n(39538);Object.keys(d).forEach(function(e){"default"!==e&&"__esModule"!==e&&(e in t&&t[e]===d[e]||Object.defineProperty(t,e,{enumerable:!0,get:function(){return d[e]}}))});var c=n(63172);Object.keys(c).forEach(function(e){"default"!==e&&"__esModule"!==e&&(e in t&&t[e]===c[e]||Object.defineProperty(t,e,{enumerable:!0,get:function(){return c[e]}}))});var f=n(30301);Object.keys(f).forEach(function(e){"default"!==e&&"__esModule"!==e&&(e in t&&t[e]===f[e]||Object.defineProperty(t,e,{enumerable:!0,get:function(){return f[e]}}))});var h=n(80998);Object.keys(h).forEach(function(e){"default"!==e&&"__esModule"!==e&&(e in t&&t[e]===h[e]||Object.defineProperty(t,e,{enumerable:!0,get:function(){return h[e]}}))});var p=n(89743);Object.keys(p).forEach(function(e){"default"!==e&&"__esModule"!==e&&(e in t&&t[e]===p[e]||Object.defineProperty(t,e,{enumerable:!0,get:function(){return p[e]}}))});var v=n(38296);Object.keys(v).forEach(function(e){"default"!==e&&"__esModule"!==e&&(e in t&&t[e]===v[e]||Object.defineProperty(t,e,{enumerable:!0,get:function(){return v[e]}}))});var m=n(69939);Object.keys(m).forEach(function(e){"default"!==e&&"__esModule"!==e&&(e in t&&t[e]===m[e]||Object.defineProperty(t,e,{enumerable:!0,get:function(){return m[e]}}))});var g=n(88253);Object.keys(g).forEach(function(e){"default"!==e&&"__esModule"!==e&&(e in t&&t[e]===g[e]||Object.defineProperty(t,e,{enumerable:!0,get:function(){return g[e]}}))});var y=n(73910);Object.keys(y).forEach(function(e){"default"!==e&&"__esModule"!==e&&(e in t&&t[e]===y[e]||Object.defineProperty(t,e,{enumerable:!0,get:function(){return y[e]}}))});var P=n(8636);Object.keys(P).forEach(function(e){"default"!==e&&"__esModule"!==e&&(e in t&&t[e]===P[e]||Object.defineProperty(t,e,{enumerable:!0,get:function(){return P[e]}}))});var S=n(42767);Object.keys(S).forEach(function(e){"default"!==e&&"__esModule"!==e&&(e in t&&t[e]===S[e]||Object.defineProperty(t,e,{enumerable:!0,get:function(){return S[e]}}))})},28641:(e,t,n)=>{var i=(Object.defineProperty(t,"__esModule",{value:!0}),t.DotsNavigation=void 0,function(e){return e&&e.__esModule?e:{default:e}}(n(14232))),o=n(36930),a=n(79910);t.DotsNavigation=function(e){var t=e.state,n=e.onClick,r=e.onMouseEnter,s=e.onMouseLeave,u=e.controlsStrategy,l=e.renderDotsItem,d=t.itemsCount,c=t.itemsInSlide,f=t.infinite,e=t.autoWidth,h=t.activeIndex,p=(0,a.getSlideItemInfo)(t).isNextSlideDisabled,v=(0,a.hasDotForEachSlide)(e,u),m=(0,a.getDotsNavigationLength)(d,c,v);return i.default.createElement("ul",{className:o.Classnames.DOTS},Array.from({length:d}).map(function(e,u){var g,y,P;if(u<m)return y=(0,a.checkIsTheLastDotIndex)(u,!!f,m),g=(0,a.getItemIndexForDotNavigation)(u,y,d,c),y=(0,a.getActiveSlideIndex)(p,t),v&&((y=h)<0?y=d-1:d<=h&&(y=0),g=u),y=y===u?o.Modifiers.ACTIVE:"",P=l?o.Modifiers.CUSTOM:"",P=(0,a.concatClassnames)(o.Classnames.DOTS_ITEM,y,P),i.default.createElement("li",{key:"dot-item-".concat(u),onMouseEnter:r,onMouseLeave:s,onClick:function(){return n(g)},className:P},l&&l({isActive:!!y,activeIndex:u}))}))}},29335:(e,t,n)=>{n.d(t,{A:()=>T});var i=n(15039),o=n.n(i),a=n(14232),r=n(77346),s=n(37876);let u=a.forwardRef((e,t)=>{let{className:n,bsPrefix:i,as:a="div",...u}=e;return i=(0,r.oU)(i,"card-body"),(0,s.jsx)(a,{ref:t,className:o()(n,i),...u})});u.displayName="CardBody";let l=a.forwardRef((e,t)=>{let{className:n,bsPrefix:i,as:a="div",...u}=e;return i=(0,r.oU)(i,"card-footer"),(0,s.jsx)(a,{ref:t,className:o()(n,i),...u})});l.displayName="CardFooter";var d=n(81764);let c=a.forwardRef((e,t)=>{let{bsPrefix:n,className:i,as:u="div",...l}=e,c=(0,r.oU)(n,"card-header"),f=(0,a.useMemo)(()=>({cardHeaderBsPrefix:c}),[c]);return(0,s.jsx)(d.A.Provider,{value:f,children:(0,s.jsx)(u,{ref:t,...l,className:o()(i,c)})})});c.displayName="CardHeader";let f=a.forwardRef((e,t)=>{let{bsPrefix:n,className:i,variant:a,as:u="img",...l}=e,d=(0,r.oU)(n,"card-img");return(0,s.jsx)(u,{ref:t,className:o()(a?"".concat(d,"-").concat(a):d,i),...l})});f.displayName="CardImg";let h=a.forwardRef((e,t)=>{let{className:n,bsPrefix:i,as:a="div",...u}=e;return i=(0,r.oU)(i,"card-img-overlay"),(0,s.jsx)(a,{ref:t,className:o()(n,i),...u})});h.displayName="CardImgOverlay";let p=a.forwardRef((e,t)=>{let{className:n,bsPrefix:i,as:a="a",...u}=e;return i=(0,r.oU)(i,"card-link"),(0,s.jsx)(a,{ref:t,className:o()(n,i),...u})});p.displayName="CardLink";var v=n(46052);let m=(0,v.A)("h6"),g=a.forwardRef((e,t)=>{let{className:n,bsPrefix:i,as:a=m,...u}=e;return i=(0,r.oU)(i,"card-subtitle"),(0,s.jsx)(a,{ref:t,className:o()(n,i),...u})});g.displayName="CardSubtitle";let y=a.forwardRef((e,t)=>{let{className:n,bsPrefix:i,as:a="p",...u}=e;return i=(0,r.oU)(i,"card-text"),(0,s.jsx)(a,{ref:t,className:o()(n,i),...u})});y.displayName="CardText";let P=(0,v.A)("h5"),S=a.forwardRef((e,t)=>{let{className:n,bsPrefix:i,as:a=P,...u}=e;return i=(0,r.oU)(i,"card-title"),(0,s.jsx)(a,{ref:t,className:o()(n,i),...u})});S.displayName="CardTitle";let b=a.forwardRef((e,t)=>{let{bsPrefix:n,className:i,bg:a,text:l,border:d,body:c=!1,children:f,as:h="div",...p}=e,v=(0,r.oU)(n,"card");return(0,s.jsx)(h,{ref:t,...p,className:o()(i,v,a&&"bg-".concat(a),l&&"text-".concat(l),d&&"border-".concat(d)),children:c?(0,s.jsx)(u,{children:f}):f})});b.displayName="Card";let T=Object.assign(b,{Img:f,Title:S,Subtitle:g,Body:u,Link:p,Text:y,Header:c,Footer:l,ImgOverlay:h})},30301:(e,t)=>{function n(e){return(n="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}Object.defineProperty(t,"__esModule",{value:!0}),t.checkIsTouchEventsSupported=void 0,t.checkIsTouchEventsSupported=function(){return("undefined"==typeof window?"undefined":n(window))==="object"&&("ontouchstart"in window||!!window.navigator.maxTouchPoints)}},36930:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.Modifiers=t.Classnames=t.AutoplayDirection=t.ControlsStrategy=t.AutoPlayStrategy=t.AnimationType=t.EventType=void 0,function(e){e.ACTION="action",e.INIT="init",e.RESIZE="resize",e.UPDATE="update"}(t.EventType||(t.EventType={})),function(e){e.FADEOUT="fadeout",e.SLIDE="slide"}(t.AnimationType||(t.AnimationType={})),function(e){e.DEFAULT="default",e.ALL="all",e.ACTION="action",e.NONE="none"}(t.AutoPlayStrategy||(t.AutoPlayStrategy={})),function(e){e.DEFAULT="default",e.ALTERNATE="alternate",e.RESPONSIVE="responsive"}(t.ControlsStrategy||(t.ControlsStrategy={})),function(e){e.RTL="rtl",e.LTR="ltr"}(t.AutoplayDirection||(t.AutoplayDirection={})),function(e){e.ANIMATED="animated animated-out fadeOut",e.ROOT="alice-carousel",e.WRAPPER="alice-carousel__wrapper",e.STAGE="alice-carousel__stage",e.STAGE_ITEM="alice-carousel__stage-item",e.DOTS="alice-carousel__dots",e.DOTS_ITEM="alice-carousel__dots-item",e.PLAY_BTN="alice-carousel__play-btn",e.PLAY_BTN_ITEM="alice-carousel__play-btn-item",e.PLAY_BTN_WRAPPER="alice-carousel__play-btn-wrapper",e.SLIDE_INFO="alice-carousel__slide-info",e.SLIDE_INFO_ITEM="alice-carousel__slide-info-item",e.BUTTON_PREV="alice-carousel__prev-btn",e.BUTTON_PREV_WRAPPER="alice-carousel__prev-btn-wrapper",e.BUTTON_PREV_ITEM="alice-carousel__prev-btn-item",e.BUTTON_NEXT="alice-carousel__next-btn",e.BUTTON_NEXT_WRAPPER="alice-carousel__next-btn-wrapper",e.BUTTON_NEXT_ITEM="alice-carousel__next-btn-item"}(t.Classnames||(t.Classnames={})),function(e){e.ACTIVE="__active",e.INACTIVE="__inactive",e.CLONED="__cloned",e.CUSTOM="__custom",e.PAUSE="__pause",e.SEPARATOR="__separator",e.SSR="__ssr",e.TARGET="__target"}(t.Modifiers||(t.Modifiers={}))},37399:(e,t,n)=>{var i=function(){var e=function(t,n){return(e=Object.setPrototypeOf||(({__proto__:[]})instanceof Array?function(e,t){e.__proto__=t}:function(e,t){for(var n in t)Object.prototype.hasOwnProperty.call(t,n)&&(e[n]=t[n])}))(t,n)};return function(t,n){if("function"!=typeof n&&null!==n)throw TypeError("Class extends value "+String(n)+" is not a constructor or null");function i(){this.constructor=t}e(t,n),t.prototype=null===n?Object.create(n):(i.prototype=n.prototype,new i)}}(),o=function(){return(o=Object.assign||function(e){for(var t,n=1,i=arguments.length;n<i;n++)for(var o in t=arguments[n])Object.prototype.hasOwnProperty.call(t,o)&&(e[o]=t[o]);return e}).apply(this,arguments)},a=Object.create?function(e,t,n,i){void 0===i&&(i=n);var o=Object.getOwnPropertyDescriptor(t,n);o&&("get"in o?t.__esModule:!o.writable&&!o.configurable)||(o={enumerable:!0,get:function(){return t[n]}}),Object.defineProperty(e,i,o)}:function(e,t,n,i){e[i=void 0===i?n:i]=t[n]},r=Object.create?function(e,t){Object.defineProperty(e,"default",{enumerable:!0,value:t})}:function(e,t){e.default=t},s=function(e){if(e&&e.__esModule)return e;var t={};if(null!=e)for(var n in e)"default"!==n&&Object.prototype.hasOwnProperty.call(e,n)&&a(t,e,n);return r(t,e),t},u=function(e,t,n,i){return new(n=n||Promise)(function(o,a){function r(e){try{u(i.next(e))}catch(e){a(e)}}function s(e){try{u(i.throw(e))}catch(e){a(e)}}function u(e){var t;e.done?o(e.value):((t=e.value)instanceof n?t:new n(function(e){e(t)})).then(r,s)}u((i=i.apply(e,t||[])).next())})},l=function(e,t){var n,i,o,a={label:0,sent:function(){if(1&o[0])throw o[1];return o[1]},trys:[],ops:[]},r={next:s(0),throw:s(1),return:s(2)};return"function"==typeof Symbol&&(r[Symbol.iterator]=function(){return this}),r;function s(r){return function(s){var u=[r,s];if(n)throw TypeError("Generator is already executing.");for(;a;)try{if(n=1,i&&(o=2&u[0]?i.return:u[0]?i.throw||((o=i.return)&&o.call(i),0):i.next)&&!(o=o.call(i,u[1])).done)return o;switch(i=0,(u=o?[2&u[0],o.value]:u)[0]){case 0:case 1:o=u;break;case 4:return a.label++,{value:u[1],done:!1};case 5:a.label++,i=u[1],u=[0];continue;case 7:u=a.ops.pop(),a.trys.pop();continue;default:if(!(o=0<(o=a.trys).length&&o[o.length-1])&&(6===u[0]||2===u[0])){a=0;continue}if(3===u[0]&&(!o||u[1]>o[0]&&u[1]<o[3]))a.label=u[1];else if(6===u[0]&&a.label<o[1])a.label=o[1],o=u;else{if(!(o&&a.label<o[2])){o[2]&&a.ops.pop(),a.trys.pop();continue}a.label=o[2],a.ops.push(u)}}u=t.call(e,a)}catch(e){u=[6,e],i=0}finally{n=o=0}if(5&u[0])throw u[1];return{value:u[0]?u[1]:void 0,done:!0}}}},d=function(e){return e&&e.__esModule?e:{default:e}},c=(Object.defineProperty(t,"__esModule",{value:!0}),t.Link=void 0,d(n(14232))),f=d(n(79319)),h=n(22173),p=(t.Link=d(n(49675)).default,s(n(7485))),v=s(n(79910)),m=n(36930),g=(function(e,t){for(var n in e)"default"===n||Object.prototype.hasOwnProperty.call(t,n)||a(t,e,n)}(n(36930),t),function(e){function t(t){var n=e.call(this,t)||this;return n.swipeListener=null,n._handleKeyboardEvents=function(e){switch(e.code){case"Space":return n.props.autoPlay&&n._handlePlayPauseToggle();case"ArrowLeft":return n.slidePrev(e);case"ArrowRight":return n.slideNext(e)}},n._handleBeforeSlideEnd=function(e){return u(n,void 0,void 0,function(){var t,n,i;return l(this,function(o){switch(o.label){case 0:return(i=(n=this.state).activeIndex,t=n.itemsCount,n=n.fadeoutAnimationProcessing,v.shouldRecalculateSlideIndex(i,t))?(i=v.getUpdateSlidePositionIndex(i,t),[4,this._handleUpdateSlidePosition(i)]):[3,2];case 1:return o.sent(),[3,4];case 2:return n?[4,this.setState({fadeoutAnimationIndex:null,fadeoutAnimationPosition:null,fadeoutAnimationProcessing:!1})]:[3,4];case 3:o.sent(),o.label=4;case 4:return this._handleSlideChanged(e),[2]}})})},n._handleMouseEnter=function(){var e=n.props.autoPlayStrategy;v.shouldCancelAutoPlayOnHover(e)&&n.state.isAutoPlaying&&(n.isHovered=!0,n._handlePause())},n._handleMouseLeave=function(){n.state.isAutoPlaying&&(n.isHovered=!1,n._handlePlay())},n._handlePause=function(){n._clearAutoPlayTimeout()},n._handlePlayPauseToggle=function(){return u(n,void 0,void 0,function(){var e;return l(this,function(t){switch(t.label){case 0:return e=this.state.isAutoPlaying,this.hasUserAction=!0,[4,this.setState({isAutoPlaying:!e,isAutoPlayCanceledOnAction:!0})];case 1:return t.sent(),e?this._handlePause():this._handlePlay(),[2]}})})},n._setRootComponentRef=function(e){return n.rootElement=e},n._setStageComponentRef=function(e){return n.stageComponent=e},n._renderStageItem=function(e,t){var i=v.getRenderStageItemStyles(t,n.state),o=v.getRenderStageItemClasses(t,n.state);return c.default.createElement(p.StageItem,{styles:i,className:o,key:"stage-item-".concat(t),item:e})},n._renderSlideInfo=function(){var e=n.props.renderSlideInfo,t=n.state,i=t.activeIndex,t=t.itemsCount;return c.default.createElement(p.SlideInfo,{itemsCount:t,activeIndex:i,renderSlideInfo:e})},n.state=v.calculateInitialState(t,null),n.isHovered=!1,n.isAnimationDisabled=!1,n.isTouchMoveProcessStarted=!1,n.cancelTouchAnimations=!1,n.hasUserAction=!1,n.rootElement=null,n.rootComponentDimensions={},n.stageComponent=null,n.startTouchmovePosition=void 0,n.slideTo=n.slideTo.bind(n),n.slidePrev=n.slidePrev.bind(n),n.slideNext=n.slideNext.bind(n),n._handleTouchmove=n._handleTouchmove.bind(n),n._handleTouchend=n._handleTouchend.bind(n),n._handleDotClick=n._handleDotClick.bind(n),n._handleResize=n._handleResize.bind(n),t=v.debounce(n._handleResize,100),n._handleResizeDebounced=t[0],n._cancelResizeDebounced=t[1],n}return i(t,e),t.prototype.componentDidMount=function(){return u(this,void 0,void 0,function(){return l(this,function(e){switch(e.label){case 0:return[4,this._setInitialState()];case 1:return e.sent(),this._addEventListeners(),this._setupSwipeHandlers(),this.props.autoPlay&&this._handlePlay(),[2]}})})},t.prototype.componentDidUpdate=function(e){var t=this.props,n=t.activeIndex,i=t.animationDuration,o=t.autoWidth,a=t.children,r=t.infinite,s=t.items,u=t.paddingLeft,l=t.paddingRight,d=t.responsive,c=t.swipeExtraPadding,f=t.mouseTracking,h=t.swipeDelta,p=t.touchTracking,t=t.touchMoveDefaultEvents;a&&e.children!==a||e.autoWidth!==o||e.infinite!==r||e.items!==s||e.paddingLeft!==u||e.paddingRight!==l||e.responsive!==d||e.swipeExtraPadding!==c?this._updateComponent():(e.animationDuration!==i&&this.setState({animationDuration:i}),e.activeIndex!==n&&this.slideTo(n,m.EventType.UPDATE)),e.swipeDelta===h&&e.mouseTracking===f&&e.touchTracking===p&&e.touchMoveDefaultEvents===t||this._updateSwipeProps(),this.props.keyboardNavigation!==e.keyboardNavigation&&this._updateEventListeners()},t.prototype.componentWillUnmount=function(){this._cancelResizeDebounced(),this._cancelTimeoutAnimations(),this._removeEventListeners()},Object.defineProperty(t.prototype,"eventObject",{get:function(){var e=this.state,t=e.itemsInSlide,e=e.activeIndex,n=v.getSlideItemInfo(this.state),i=n.isNextSlideDisabled,n=n.isPrevSlideDisabled;return{item:e,slide:v.getActiveSlideIndex(i,this.state),itemsInSlide:t,isNextSlideDisabled:i,isPrevSlideDisabled:n,type:m.EventType.ACTION}},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"isFadeoutAnimationAllowed",{get:function(){var e=this.state.itemsInSlide,t=this.props,n=t.animationType,i=t.paddingLeft,o=t.paddingRight,t=t.autoWidth;return 1===e&&n===m.AnimationType.FADEOUT&&!(i||o||t)},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"touchmovePosition",{get:function(){return void 0!==this.startTouchmovePosition?this.startTouchmovePosition:this.state.translate3d},enumerable:!1,configurable:!0}),t.prototype.slideTo=function(e,t){var n,i,o;void 0===e&&(e=0),this._handlePause(),this.isFadeoutAnimationAllowed?(n=v.getUpdateSlidePositionIndex(e,this.state.itemsCount),i=v.getFadeoutAnimationPosition(n,this.state),o=v.getFadeoutAnimationIndex(this.state),this._handleSlideTo({activeIndex:n,fadeoutAnimationIndex:o,fadeoutAnimationPosition:i,eventType:t})):this._handleSlideTo({activeIndex:e,eventType:t})},t.prototype.slidePrev=function(e){this._handlePause(),e&&e.isTrusted&&(this.hasUserAction=!0);var t,n,e=this.state.activeIndex-1;this.isFadeoutAnimationAllowed?(t=-this.state.stageWidth,n=v.getFadeoutAnimationIndex(this.state),this._handleSlideTo({activeIndex:e,fadeoutAnimationIndex:n,fadeoutAnimationPosition:t})):this._handleSlideTo({activeIndex:e})},t.prototype.slideNext=function(e){this._handlePause(),e&&e.isTrusted&&(this.hasUserAction=!0);var t,n,e=this.state.activeIndex+1;this.isFadeoutAnimationAllowed?(t=this.state.stageWidth,n=v.getFadeoutAnimationIndex(this.state),this._handleSlideTo({activeIndex:e,fadeoutAnimationIndex:n,fadeoutAnimationPosition:t})):this._handleSlideTo({activeIndex:e})},t.prototype._addEventListeners=function(){window.addEventListener("resize",this._handleResizeDebounced),this.props.keyboardNavigation&&window.addEventListener("keyup",this._handleKeyboardEvents)},t.prototype._removeEventListeners=function(){this.swipeListener&&this.swipeListener.destroy(),window.removeEventListener("resize",this._handleResizeDebounced),window.removeEventListener("keyup",this._handleKeyboardEvents)},t.prototype._updateEventListeners=function(){this.props.keyboardNavigation?window.addEventListener("keyup",this._handleKeyboardEvents):window.removeEventListener("keyup",this._handleKeyboardEvents)},t.prototype._handleResize=function(e){return u(this,void 0,void 0,function(){var t,n,i,a;return l(this,function(r){switch(r.label){case 0:return(i=this.props.onResizeEvent,n=v.getElementDimensions(this.rootElement),(i||v.shouldHandleResizeEvent)(e,this.rootComponentDimensions,n))?(this._cancelTimeoutAnimations(),this.rootComponentDimensions=n,n=(i=this.state).itemsCount,t=i.isAutoPlaying,i=v.getUpdateSlidePositionIndex(this.state.activeIndex,n),n=v.calculateInitialState(o(o({},this.props),{activeIndex:i}),this.stageComponent),i=v.getTranslate3dProperty(n.activeIndex,n),a=o(o({},n),{translate3d:i,isAutoPlaying:t}),v.animate(this.stageComponent,{position:-i}),[4,this.setState(a)]):[3,2];case 1:r.sent(),this._handleResized({itemsInSlide:a.itemsInSlide}),this.isAnimationDisabled=!1,t&&this._handlePlay(),r.label=2;case 2:return[2]}})})},t.prototype._handleTouchmove=function(e,t){var n=t.absY,i=t.absX,o=t.deltaX,t=this.props.swipeDelta,a=this.state,r=a.swipeShiftValue,s=a.swipeLimitMin,u=a.swipeLimitMax,l=a.infinite,a=a.fadeoutAnimationProcessing;if(this.hasUserAction=!0,!(a||!this.isTouchMoveProcessStarted&&v.isVerticalTouchmoveDetected(i,n,t))){this.isTouchMoveProcessStarted||(this._cancelTimeoutAnimations(),this._setTouchmovePosition(),this.isAnimationDisabled=!0,this.isTouchMoveProcessStarted=!0,this._handleSlideChange());var d=v.getTouchmoveTranslatePosition(o,this.touchmovePosition);if(!1===l)return s<d||d<-u?void 0:void v.animate(this.stageComponent,{position:d});if(v.shouldRecalculateSwipePosition(d,s,u))try{!function e(){v.getIsLeftDirection(o)?d+=r:d+=-r,v.shouldRecalculateSwipePosition(d,s,u)&&e()}()}catch(e){v.debug(e)}v.animate(this.stageComponent,{position:d})}},t.prototype._handleTouchend=function(e,t){var n,i,o,t=t.deltaX;this._clearTouchmovePosition(),this.isTouchMoveProcessStarted&&(this.isTouchMoveProcessStarted=!1,n=this.state.animationDuration,i=this.props.animationEasingFunction,o=v.getTranslateXProperty(this.stageComponent),t=v.getSwipeTouchendPosition(this.state,t,o),v.animate(this.stageComponent,{position:t,animationDuration:n,animationEasingFunction:i}),this._handleBeforeTouchEnd(t))},t.prototype._handleBeforeTouchEnd=function(e){var t=this,n=this.state.animationDuration;this.touchEndTimeoutId=window.setTimeout(function(){return u(t,void 0,void 0,function(){var t,n,i,o=this;return l(this,function(a){switch(a.label){case 0:return t=v.getSwipeTouchendIndex(e,this.state),n=v.getTranslate3dProperty(t,this.state),v.animate(this.stageComponent,{position:-n}),i=v.getTransitionProperty(),[4,this.setState({activeIndex:t,translate3d:n,transition:i})];case 1:return a.sent(),requestAnimationFrame(function(){return o._handleSlideChanged()}),[2]}})})},n)},t.prototype._handleSlideTo=function(e){var t=e.activeIndex,n=void 0===t?0:t,t=e.fadeoutAnimationIndex,i=void 0===t?null:t,t=e.fadeoutAnimationPosition,o=void 0===t?null:t,a=e.eventType;return u(this,void 0,void 0,function(){var e,t,r,s,u=this;return l(this,function(l){switch(l.label){case 0:return(r=(t=this.props).infinite,t=t.animationEasingFunction,s=(e=this.state).itemsCount,e=e.animationDuration,this.isAnimationDisabled||this.state.activeIndex===n||!r&&v.shouldCancelSlideAnimation(n,s))?[2]:(this.isAnimationDisabled=!0,this._cancelTimeoutAnimations(),this._handleSlideChange(a),r=!1,s=v.getTranslate3dProperty(n,this.state),t=null!==i&&null!==o?(r=!0,v.getTransitionProperty()):v.getTransitionProperty({animationDuration:e,animationEasingFunction:t}),[4,this.setState({activeIndex:n,transition:t,translate3d:s,animationDuration:e,fadeoutAnimationIndex:i,fadeoutAnimationPosition:o,fadeoutAnimationProcessing:r})]);case 1:return l.sent(),this.slideEndTimeoutId=window.setTimeout(function(){return u._handleBeforeSlideEnd(a)},e),[2]}})})},t.prototype._handleUpdateSlidePosition=function(e){return u(this,void 0,void 0,function(){var t,n,i;return l(this,function(o){switch(o.label){case 0:return t=this.state.animationDuration,n=v.getTranslate3dProperty(e,this.state),i=v.getTransitionProperty({animationDuration:0}),[4,this.setState({activeIndex:e,translate3d:n,transition:i,animationDuration:t,fadeoutAnimationIndex:null,fadeoutAnimationPosition:null,fadeoutAnimationProcessing:!1})];case 1:return o.sent(),[2]}})})},t.prototype._handleUpdated=function(){this.props.onUpdated&&this.props.onUpdated(o(o({},this.eventObject),{type:m.EventType.UPDATE}))},t.prototype._handleResized=function(e){void 0===e&&(e={}),this.props.onResized&&this.props.onResized(o(o(o({},this.eventObject),e),{type:m.EventType.RESIZE}))},t.prototype._handleSlideChange=function(e){this.props.onSlideChange&&(e=e?o(o({},this.eventObject),{type:e}):this.eventObject,this.props.onSlideChange(e))},t.prototype._handleSlideChanged=function(e){return u(this,void 0,void 0,function(){var t,n,i,a;return l(this,function(r){switch(r.label){case 0:return(t=(n=this.state).isAutoPlaying,n=n.isAutoPlayCanceledOnAction,a=(i=this.props).autoPlayStrategy,i=i.onSlideChanged,v.shouldCancelAutoPlayOnAction(a)&&this.hasUserAction&&!n)?[4,this.setState({isAutoPlayCanceledOnAction:!0,isAutoPlaying:!1})]:[3,2];case 1:return r.sent(),[3,3];case 2:t&&this._handlePlay(),r.label=3;case 3:return this.isAnimationDisabled=!1,i&&(a=e?o(o({},this.eventObject),{type:e}):this.eventObject,i(a)),e===m.EventType.UPDATE&&this._handleUpdated(),[2]}})})},t.prototype._handleDotClick=function(e){this.hasUserAction=!0,this.slideTo(e)},t.prototype._handlePlay=function(){this._setAutoPlayInterval()},t.prototype._cancelTimeoutAnimations=function(){this._clearAutoPlayTimeout(),this._clearSlideEndTimeout(),this.clearTouchendTimeout()},t.prototype._clearAutoPlayTimeout=function(){window.clearTimeout(this.autoPlayTimeoutId),this.autoPlayTimeoutId=void 0},t.prototype._clearSlideEndTimeout=function(){clearTimeout(this.slideEndTimeoutId),this.slideEndTimeoutId=void 0},t.prototype.clearTouchendTimeout=function(){clearTimeout(this.touchEndTimeoutId),this.touchEndTimeoutId=void 0},t.prototype._clearTouchmovePosition=function(){this.startTouchmovePosition=void 0},t.prototype._setTouchmovePosition=function(){var e=v.getTranslateXProperty(this.stageComponent);this.startTouchmovePosition=-e},t.prototype._setInitialState=function(){return u(this,void 0,void 0,function(){var e;return l(this,function(t){switch(t.label){case 0:return e=v.calculateInitialState(this.props,this.stageComponent),this.rootComponentDimensions=v.getElementDimensions(this.rootElement),[4,this.setState(e)];case 1:return t.sent(),this.props.onInitialized&&this.props.onInitialized(o(o({},this.eventObject),{type:m.EventType.INIT})),[2]}})})},t.prototype._setAutoPlayInterval=function(){var e=this,t=this.props,n=t.autoPlayDirection,t=t.autoPlayInterval;this.autoPlayTimeoutId=window.setTimeout(function(){e.isHovered||(n===m.AutoplayDirection.RTL?e.slidePrev():e.slideNext())},t)},t.prototype._setupSwipeHandlers=function(){this.swipeListener=new f.default({element:this.rootElement,delta:this.props.swipeDelta,onSwiping:this._handleTouchmove,onSwiped:this._handleTouchend,rotationAngle:5,mouseTrackingEnabled:this.props.mouseTracking,touchTrackingEnabled:this.props.touchTracking,preventDefaultTouchmoveEvent:!this.props.touchMoveDefaultEvents,preventTrackingOnMouseleave:!0}),this.swipeListener.init()},t.prototype._updateComponent=function(){var e=this,t=(this.props.syncStateOnPropsUpdate?this.state:this.props).activeIndex,n=o(o({},this.props),{activeIndex:t});this._cancelTimeoutAnimations(),this.isAnimationDisabled=!1,this.state.isAutoPlaying&&this._handlePlay(),this.setState({clones:v.createClones(n)}),requestAnimationFrame(function(){e.setState(v.calculateInitialState(n,e.stageComponent),function(){return e._handleUpdated()})})},t.prototype._updateSwipeProps=function(){this.swipeListener&&this.swipeListener.update({delta:this.props.swipeDelta,mouseTrackingEnabled:this.props.mouseTracking,touchTrackingEnabled:this.props.touchTracking,preventDefaultTouchmoveEvent:!this.props.touchMoveDefaultEvents})},t.prototype._renderDotsNavigation=function(){var e=this.props,t=e.renderDotsItem,e=e.controlsStrategy;return c.default.createElement(p.DotsNavigation,{state:this.state,onClick:this._handleDotClick,renderDotsItem:t,controlsStrategy:e})},t.prototype._renderPrevButton=function(){var e=this.props.renderPrevButton,t=v.getSlideItemInfo(this.state).isPrevSlideDisabled;return c.default.createElement(p.PrevNextButton,{name:"prev",onClick:this.slidePrev,isDisabled:t,renderPrevButton:e})},t.prototype._renderNextButton=function(){var e=this.props.renderNextButton,t=v.getSlideItemInfo(this.state).isNextSlideDisabled;return c.default.createElement(p.PrevNextButton,{name:"next",onClick:this.slideNext,isDisabled:t,renderNextButton:e})},t.prototype._renderPlayPauseButton=function(){var e=this.props.renderPlayPauseButton,t=this.state.isAutoPlaying;return c.default.createElement(p.PlayPauseButton,{isPlaying:t,onClick:this._handlePlayPauseToggle,renderPlayPauseButton:e})},t.prototype.render=function(){var e=this.state,t=e.translate3d,n=e.clones,i=e.transition,e=e.canUseDom,o=v.shouldDisableDots(this.props,this.state),a=v.shouldDisableButtons(this.props,this.state),r=v.getRenderWrapperStyles(this.props,this.state,this.stageComponent),t=v.getRenderStageStyles({translate3d:t},{transition:i}),i=this.props.ssrSilentMode||e?"":m.Modifiers.SSR,e=v.concatClassnames(m.Classnames.ROOT,i);return c.default.createElement("div",{className:e},c.default.createElement("div",{ref:this._setRootComponentRef},c.default.createElement("div",{style:r,className:m.Classnames.WRAPPER,onMouseEnter:this._handleMouseEnter,onMouseLeave:this._handleMouseLeave},c.default.createElement("ul",{style:t,className:m.Classnames.STAGE,ref:this._setStageComponentRef},n.map(this._renderStageItem)))),o?null:this._renderDotsNavigation(),a?null:this._renderPrevButton(),a?null:this._renderNextButton(),this.props.disableSlideInfo?null:this._renderSlideInfo(),this.props.autoPlayControls?this._renderPlayPauseButton():null)},t.defaultProps=h.defaultProps,t}(c.default.PureComponent));t.default=g},38296:(e,t)=>{function n(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);t&&(i=i.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),n.push.apply(n,i)}return n}Object.defineProperty(t,"__esModule",{value:!0}),t.getInitialState=void 0,t.getInitialState=function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};return function(e){for(var t=1;t<arguments.length;t++){var i=null!=arguments[t]?arguments[t]:{};t%2?n(Object(i),!0).forEach(function(t){var n,o,a;n=e,o=t,a=i[t],o in n?Object.defineProperty(n,o,{value:a,enumerable:!0,configurable:!0,writable:!0}):n[o]=a}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(i)):n(Object(i)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(i,t))})}return e}({x:0,y:0,start:0,isSwiping:!1,traceX:[],traceY:[]},e)}},39538:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.checkIsMoreThanSingleTouches=void 0,t.checkIsMoreThanSingleTouches=function(e){return!!(e.touches&&e.touches.length>1)}},42767:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.updateTrace=function(e,t){return e[e.length-1]!==t&&e.push(t),e}},47464:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.calculateDirectionDelta=function(e){for(var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0,n=e.length-1,a=i.TraceDirectionKey.NONE;n>=0;n--){var r=e[n],s=(0,o.getDirectionKey)(r),u=(0,o.getDirectionValue)(r[s]),l=e[n-1]||{},d=(0,o.getDirectionKey)(l),c=(0,o.getDirectionValue)(l[d]);if((0,o.getDifference)(u,c)>=t){a=s;break}a=d}return a};var i=n(91305),o=n(80998)},49675:(e,t,n)=>{var i=function(){return(i=Object.assign||function(e){for(var t,n=1,i=arguments.length;n<i;n++)for(var o in t=arguments[n])Object.prototype.hasOwnProperty.call(t,o)&&(e[o]=t[o]);return e}).apply(this,arguments)},o=(Object.defineProperty(t,"__esModule",{value:!0}),function(e){return e&&e.__esModule?e:{default:e}}(n(14232)));t.default=function(e){var t={xDown:null,xUp:null};return o.default.createElement("a",i({onClick:function(e){t.xDown!==t.xUp&&e.preventDefault()},onMouseDown:function(e){e.preventDefault(),t.xUp=null,t.xDown=e.clientX},onMouseUp:function(e){e.preventDefault(),t.xUp=e.clientX}},e),e.children)}},51546:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.calculateVelocity=function(e,t,n){return Math.sqrt(e*e+t*t)/(n||1)}},56413:(e,t,n)=>{var i=(Object.defineProperty(t,"__esModule",{value:!0}),t.PlayPauseButton=void 0,function(e){return e&&e.__esModule?e:{default:e}}(n(14232))),o=n(36930),a=n(79910);t.PlayPauseButton=function(e){var t=e.isPlaying,n=e.onClick,e=e.renderPlayPauseButton;return"function"==typeof e?i.default.createElement("div",{className:o.Classnames.PLAY_BTN,onClick:n},e({isPlaying:t})):(e=t?o.Modifiers.PAUSE:"",t=(0,a.concatClassnames)(o.Classnames.PLAY_BTN_ITEM,e),i.default.createElement("div",{className:o.Classnames.PLAY_BTN},i.default.createElement("div",{className:o.Classnames.PLAY_BTN_WRAPPER},i.default.createElement("div",{onClick:n,className:t}))))}},58748:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.calculateTraceDirections=function(){for(var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[],t=[],n=i.TraceDirectionKey.POSITIVE,a=i.TraceDirectionKey.NEGATIVE,r=0,s=[],u=i.TraceDirectionKey.NONE;r<e.length;r++){var l=e[r],d=e[r-1];if(s.length){var c=l>d?n:a;u===i.TraceDirectionKey.NONE&&(u=c),c===u?s.push(l):(t.push(o({},u,s.slice())),(s=[]).push(l),u=c)}else 0!==l&&(u=l>0?n:a),s.push(l)}return s.length&&t.push(o({},u,s)),t};var i=n(91305);function o(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}},62707:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.calculateInitialState=t.getIsStageContentPartial=t.concatClassnames=void 0;var i=n(93019),o=n(25936),a=(t.concatClassnames=function(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];return e.filter(Boolean).join(" ")},function(e,t,n){return void 0===t&&(t=0),void 0===n&&(n=0),!(e=void 0!==e&&e)&&n<=t}),r=(t.getIsStageContentPartial=a,function(e,t,n){void 0===n&&(n=(0,i.canUseDOM)());var a,r,s=e.animationDuration,s=void 0===s?0:s,u=e.infinite,u=void 0!==u&&u,l=e.autoPlay,l=void 0!==l&&l,d=e.autoWidth,d=void 0!==d&&d,c=(0,i.createClones)(e),f=(0,i.getTransitionProperty)(),h=(0,i.getItemsCount)(e),p=(0,i.getItemsOffset)(e),v=(0,i.getItemsInSlide)(h,e),m=(0,o.getStartIndex)(e.activeIndex,h),m=(0,o.getActiveIndex)({startIndex:m,itemsCount:h,infinite:u}),g=(0,i.getElementDimensions)(t).width,y=(t=(a=d?(t=(0,i.createAutowidthTransformationSet)(t,g,u)).coords:(t=(0,i.createDefaultTransformationSet)(c,g,v,u)).coords,r=t.content,t).partial,(0,o.getItemCoords)(-v,a).position),P=(0,o.getSwipeLimitMin)({itemsOffset:p,transformationSet:a},e),e=(0,o.getSwipeLimitMax)({itemsCount:h,itemsOffset:p,itemsInSlide:v,transformationSet:a},e),S=(0,o.getSwipeShiftValue)(h,a);return{activeIndex:m,autoWidth:d,animationDuration:s,clones:c,infinite:u,itemsCount:h,itemsInSlide:v,itemsOffset:p,translate3d:(0,i.getTranslate3dProperty)(m,{itemsInSlide:v,itemsOffset:p,transformationSet:a,autoWidth:d,infinite:u}),stageWidth:g,stageContentWidth:r,initialStageHeight:0,isStageContentPartial:t,isAutoPlaying:l,isAutoPlayCanceledOnAction:!1,transformationSet:a,transition:f,fadeoutAnimationIndex:null,fadeoutAnimationPosition:null,fadeoutAnimationProcessing:!1,swipeLimitMin:P,swipeLimitMax:e,swipeAllowedPositionMax:y,swipeShiftValue:S,canUseDom:n}});t.calculateInitialState=r},63172:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.checkIsPassiveSupported=function(e){if("boolean"==typeof e)return e;var t={isPassiveSupported:e};try{var n=(0,i.createOptions)(t);window.addEventListener("checkIsPassiveSupported",o,n),window.removeEventListener("checkIsPassiveSupported",o,n)}catch(e){}return t.isPassiveSupported},t.noop=void 0;var i=n(89743),o=function(){};t.noop=o},66074:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.getSlideItemInfo=t.getSlideInfo=t.getSlideIndexForMultipleItems=t.getSlideIndexForNonMultipleItems=t.getActiveSlideDotsLength=t.getActiveSlideIndex=void 0;var n=(t.getActiveSlideIndex=function(e,n){var n=n||{},i=n.activeIndex,o=n.itemsInSlide,n=n.itemsCount,i=i+o;return 1===o?(0,t.getSlideIndexForNonMultipleItems)(i,o,n):(0,t.getSlideIndexForMultipleItems)(i,o,n,e)},function(e,t){var n;return void 0===t&&(t=1),(e=void 0===e?0:e)&&t?(n=Math.floor(e/t),e%t==0?n-1:n):0}),i=(t.getActiveSlideDotsLength=n,function(e,t,n){return e<t?n-t:n<e?0:e-1}),o=(t.getSlideIndexForNonMultipleItems=i,function(e,n,i,o){var a=(0,t.getActiveSlideDotsLength)(i,n);return e===i+n?0:o||e<n&&0!==e?a:0===e?i%n==0?a:a-1:0<n?Math.floor(e/n)-1:0}),a=(t.getSlideIndexForMultipleItems=o,function(e,t){return void 0===t&&(t=0),(e=(e=void 0===e?0:e)+1)<1?e=t:t<e&&(e=1),{item:e,itemsCount:t}}),r=(t.getSlideInfo=a,function(e){var e=e||{},t=e.itemsInSlide,n=e.activeIndex,i=e.infinite,o=e.itemsCount;return e.isStageContentPartial?{isPrevSlideDisabled:!0,isNextSlideDisabled:!0}:{isPrevSlideDisabled:!1===i&&0===n,isNextSlideDisabled:!1===i&&o-t<=n}});t.getSlideItemInfo=r},67272:(e,t,n)=>{var i=(Object.defineProperty(t,"__esModule",{value:!0}),t.StageItem=void 0,function(e){return e&&e.__esModule?e:{default:e}}(n(14232)));t.StageItem=function(e){var t=e.item,n=e.className,e=e.styles;return i.default.createElement("li",{style:e,className:n},t)}},67296:(e,t,n)=>{var i=(Object.defineProperty(t,"__esModule",{value:!0}),t.SlideInfo=void 0,function(e){return e&&e.__esModule?e:{default:e}}(n(14232))),o=n(36930),a=n(79910);t.SlideInfo=function(e){var t=e.activeIndex,n=e.itemsCount,e=e.renderSlideInfo,t=(0,a.getSlideInfo)(t,n).item;return"function"==typeof e?i.default.createElement("div",{className:o.Classnames.SLIDE_INFO},e({item:t,itemsCount:n})):(e=(0,a.concatClassnames)(o.Classnames.SLIDE_INFO_ITEM,o.Modifiers.SEPARATOR),i.default.createElement("div",{className:o.Classnames.SLIDE_INFO},i.default.createElement("span",{className:o.Classnames.SLIDE_INFO_ITEM},t),i.default.createElement("span",{className:e},"/"),i.default.createElement("span",{className:o.Classnames.SLIDE_INFO_ITEM},n)))}},69939:(e,t)=>{function n(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);t&&(i=i.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),n.push.apply(n,i)}return n}Object.defineProperty(t,"__esModule",{value:!0}),t.getInitialProps=void 0,t.getInitialProps=function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};return function(e){for(var t=1;t<arguments.length;t++){var i=null!=arguments[t]?arguments[t]:{};t%2?n(Object(i),!0).forEach(function(t){var n,o,a;n=e,o=t,a=i[t],o in n?Object.defineProperty(n,o,{value:a,enumerable:!0,configurable:!0,writable:!0}):n[o]=a}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(i)):n(Object(i)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(i,t))})}return e}({element:null,target:null,delta:10,directionDelta:0,rotationAngle:0,mouseTrackingEnabled:!1,touchTrackingEnabled:!0,preventDefaultTouchmoveEvent:!1,preventTrackingOnMouseleave:!1},e)}},71682:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.isClonedItem=t.isTargetItem=t.isActiveItem=t.getRenderStageItemClasses=void 0;var i=n(36930),o=n(62707),a=n(25936),r=(t.getRenderStageItemClasses=function(e,n){void 0===e&&(e=0);var a=n.fadeoutAnimationIndex,r=(0,t.isActiveItem)(e,n)?i.Modifiers.ACTIVE:"",s=(0,t.isClonedItem)(e,n)?i.Modifiers.CLONED:"",n=(0,t.isTargetItem)(e,n)?i.Modifiers.TARGET:"",e=e===a?i.Classnames.ANIMATED:"";return(0,o.concatClassnames)(i.Classnames.STAGE_ITEM,r,s,n,e)},function(e,t){void 0===e&&(e=0);var n=t.activeIndex,i=t.itemsInSlide,o=t.itemsOffset,r=t.infinite,t=t.autoWidth,s=(0,a.getShiftIndex)(i,o);return t&&r?e-s===n+o:(t=n+s,r?t<=e&&e<t+i:n<=e&&e<t)}),s=(t.isActiveItem=r,function(e,t){void 0===e&&(e=0);var n=t.activeIndex,i=t.itemsInSlide,o=t.itemsOffset,r=t.infinite,t=t.autoWidth,i=(0,a.getShiftIndex)(i,o);return r?t&&r?e-i===n+o:e===n+i:e===n}),u=(t.isTargetItem=s,function(e,t){void 0===e&&(e=0);var n=t.itemsInSlide,i=t.itemsOffset,o=t.itemsCount,r=t.infinite,t=t.autoWidth;return!!r&&(t&&r?e<n||o-1+n<e:e<(t=(0,a.getShiftIndex)(n,i))||o-1+t<e)});t.isClonedItem=u},73910:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.resolveDirection=function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:s.Axis.X,n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:0;if(n){var u=(0,o.calculateTraceDirections)(e),l=(0,a.calculateDirectionDelta)(u,n);return(0,r.resolveAxisDirection)(t,l)}var d=(0,i.calculateDirection)(e);return(0,r.resolveAxisDirection)(t,d)};var i=n(21330),o=n(58748),a=n(47464),r=n(80998),s=n(91305)},79319:(e,t,n)=>{function i(e){return(i="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}Object.defineProperty(t,"__esModule",{value:!0});var o={};t.default=void 0;var a=function(e,t){if(e&&e.__esModule)return e;if(null===e||"object"!==i(e)&&"function"!=typeof e)return{default:e};var n=s(t);if(n&&n.has(e))return n.get(e);var o={},a=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var r in e)if("default"!==r&&Object.prototype.hasOwnProperty.call(e,r)){var u=a?Object.getOwnPropertyDescriptor(e,r):null;u&&(u.get||u.set)?Object.defineProperty(o,r,u):o[r]=e[r]}return o.default=e,n&&n.set(e,o),o}(n(27693)),r=n(91305);function s(e){if("function"!=typeof WeakMap)return null;var t=new WeakMap,n=new WeakMap;return(s=function(e){return e?n:t})(e)}function u(e,t){for(var n=0;n<t.length;n++){var i=t[n];i.enumerable=i.enumerable||!1,i.configurable=!0,"value"in i&&(i.writable=!0),Object.defineProperty(e,i.key,i)}}function l(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}Object.keys(r).forEach(function(e){!("default"===e||"__esModule"===e||Object.prototype.hasOwnProperty.call(o,e))&&(e in t&&t[e]===r[e]||Object.defineProperty(t,e,{enumerable:!0,get:function(){return r[e]}}))}),t.default=function(){var e,t;function n(e){if(!(this instanceof n))throw TypeError("Cannot call a class as a function");l(this,"state",void 0),l(this,"props",void 0),this.state=a.getInitialState(),this.props=a.getInitialProps(e),this.handleSwipeStart=this.handleSwipeStart.bind(this),this.handleSwipeMove=this.handleSwipeMove.bind(this),this.handleSwipeEnd=this.handleSwipeEnd.bind(this),this.handleMouseDown=this.handleMouseDown.bind(this),this.handleMouseMove=this.handleMouseMove.bind(this),this.handleMouseUp=this.handleMouseUp.bind(this),this.handleMouseLeave=this.handleMouseLeave.bind(this)}return e=[{key:"init",value:function(){this.setupTouchListeners(),this.setupMouseListeners()}},{key:"update",value:function(e){var t=this.props,n=Object.assign({},t,e);if(t.element!==n.element||t.target!==n.target){this.destroy(),this.props=n,this.init();return}this.props=n,(t.mouseTrackingEnabled!==n.mouseTrackingEnabled||t.preventTrackingOnMouseleave!==n.preventTrackingOnMouseleave)&&(this.cleanupMouseListeners(),n.mouseTrackingEnabled?this.setupMouseListeners():this.cleanupMouseListeners()),t.touchTrackingEnabled!==n.touchTrackingEnabled&&(this.cleanupTouchListeners(),n.touchTrackingEnabled?this.setupTouchListeners():this.cleanupTouchListeners())}},{key:"destroy",value:function(){this.cleanupMouseListeners(),this.cleanupTouchListeners(),this.state=a.getInitialState(),this.props=a.getInitialProps()}},{key:"setupTouchListeners",value:function(){var e=this.props,t=e.element,n=e.target,i=e.touchTrackingEnabled;if(t&&i){var o=n||t,r=a.checkIsPassiveSupported(),s=a.getOptions(r);o.addEventListener("touchstart",this.handleSwipeStart,s),o.addEventListener("touchmove",this.handleSwipeMove,s),o.addEventListener("touchend",this.handleSwipeEnd,s)}}},{key:"cleanupTouchListeners",value:function(){var e=this.props,t=e.element,n=e.target||t;n&&(n.removeEventListener("touchstart",this.handleSwipeStart),n.removeEventListener("touchmove",this.handleSwipeMove),n.removeEventListener("touchend",this.handleSwipeEnd))}},{key:"setupMouseListeners",value:function(){var e=this.props,t=e.element,n=e.mouseTrackingEnabled,i=e.preventTrackingOnMouseleave;n&&t&&(t.addEventListener("mousedown",this.handleMouseDown),t.addEventListener("mousemove",this.handleMouseMove),t.addEventListener("mouseup",this.handleMouseUp),i&&t.addEventListener("mouseleave",this.handleMouseLeave))}},{key:"cleanupMouseListeners",value:function(){var e=this.props.element;e&&(e.removeEventListener("mousedown",this.handleMouseDown),e.removeEventListener("mousemove",this.handleMouseMove),e.removeEventListener("mouseup",this.handleMouseUp),e.removeEventListener("mouseleave",this.handleMouseLeave))}},{key:"getEventData",value:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{directionDelta:0},n=this.props.rotationAngle,i=t.directionDelta,o=a.calculateMovingPosition(e),r=a.rotateByAngle(o,n);return a.calculatePosition(this.state,{rotatePosition:r,directionDelta:i})}},{key:"handleSwipeStart",value:function(e){if(!a.checkIsMoreThanSingleTouches(e)){var t=this.props.rotationAngle,n=a.calculateMovingPosition(e),i=a.rotateByAngle(n,t),o=i.x,r=i.y;this.state=a.getInitialState({isSwiping:!1,start:Date.now(),x:o,y:r})}}},{key:"handleSwipeMove",value:function(e){var t=this.state,n=t.x,i=t.y,o=t.isSwiping;if(!(!n||!i||a.checkIsMoreThanSingleTouches(e))){var r=this.props.directionDelta||0,s=this.getEventData(e,{directionDelta:r}),u=s.absX,l=s.absY,d=s.deltaX,c=s.deltaY,f=s.directionX,h=s.directionY,p=s.duration,v=s.velocity,m=this.props,g=m.delta,y=m.preventDefaultTouchmoveEvent,P=m.onSwipeStart,S=m.onSwiping;e.cancelable&&y&&e.preventDefault(),(!(u<Number(g)&&l<Number(g))||o)&&(P&&!o&&P(e,{deltaX:d,deltaY:c,absX:u,absY:l,directionX:f,directionY:h,duration:p,velocity:v}),this.state.isSwiping=!0,S&&S(e,{deltaX:d,deltaY:c,absX:u,absY:l,directionX:f,directionY:h,duration:p,velocity:v}))}}},{key:"handleSwipeEnd",value:function(e){var t=this.props,n=t.onSwiped,i=t.onTap;if(this.state.isSwiping){var o=this.props.directionDelta||0,r=this.getEventData(e,{directionDelta:o});n&&n(e,r)}else{var s=this.getEventData(e);i&&i(e,s)}this.state=a.getInitialState()}},{key:"handleMouseDown",value:function(e){var t=this.props.target;t?t===e.target&&this.handleSwipeStart(e):this.handleSwipeStart(e)}},{key:"handleMouseMove",value:function(e){this.handleSwipeMove(e)}},{key:"handleMouseUp",value:function(e){var t=this.state.isSwiping,n=this.props.target;n?(n===e.target||t)&&this.handleSwipeEnd(e):this.handleSwipeEnd(e)}},{key:"handleMouseLeave",value:function(e){this.state.isSwiping&&this.handleSwipeEnd(e)}}],t=[{key:"isTouchEventsSupported",value:function(){return a.checkIsTouchEventsSupported()}}],e&&u(n.prototype,e),t&&u(n,t),Object.defineProperty(n,"prototype",{writable:!1}),n}()},79910:(e,t,n)=>{var i=Object.create?function(e,t,n,i){void 0===i&&(i=n);var o=Object.getOwnPropertyDescriptor(t,n);o&&("get"in o?t.__esModule:!o.writable&&!o.configurable)||(o={enumerable:!0,get:function(){return t[n]}}),Object.defineProperty(e,i,o)}:function(e,t,n,i){e[i=void 0===i?n:i]=t[n]},o=function(e,t){for(var n in e)"default"===n||Object.prototype.hasOwnProperty.call(t,n)||i(t,e,n)};Object.defineProperty(t,"__esModule",{value:!0}),o(n(62707),t),o(n(93019),t),o(n(71682),t),o(n(87424),t),o(n(25936),t),o(n(6305),t),o(n(66074),t),o(n(87212),t),o(n(92716),t)},80998:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.resolveAxisDirection=t.getDirectionValue=t.getDirectionKey=t.getDifference=void 0;var i=n(91305);t.getDirectionKey=function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};switch(Object.keys(e).toString()){case i.TraceDirectionKey.POSITIVE:return i.TraceDirectionKey.POSITIVE;case i.TraceDirectionKey.NEGATIVE:return i.TraceDirectionKey.NEGATIVE;default:return i.TraceDirectionKey.NONE}},t.getDirectionValue=function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[];return e[e.length-1]||0},t.getDifference=function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:0,t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0;return Math.abs(e-t)},t.resolveAxisDirection=function(e,t){var n=i.Direction.LEFT,o=i.Direction.RIGHT,a=i.Direction.NONE;return e===i.Axis.Y&&(n=i.Direction.BOTTOM,o=i.Direction.TOP),t===i.TraceDirectionKey.NEGATIVE&&(a=n),t===i.TraceDirectionKey.POSITIVE&&(a=o),a}},81764:(e,t,n)=>{n.d(t,{A:()=>o});let i=n(14232).createContext(null);i.displayName="CardHeaderContext";let o=i},83301:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.calculateDuration=function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:0,t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0;return e?t-e:0}},87212:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.shouldCancelAutoPlayOnHover=t.shouldCancelAutoPlayOnAction=t.getItemIndexForDotNavigation=t.checkIsTheLastDotIndex=t.getDotsNavigationLength=t.hasDotForEachSlide=t.isStrategy=t.shouldDisableButtons=t.shouldDisableDots=t.shouldDisableControls=void 0;var i=n(36930);function o(e,n){var e=(e||{}).controlsStrategy,n=n||{},o=n.itemsInSlide,a=n.itemsCount,n=n.autoWidth;if((0,t.isStrategy)(e,i.ControlsStrategy.RESPONSIVE))return!n&&o===a}t.shouldDisableControls=o,t.shouldDisableDots=function(e,t){return e.disableDotsControls||o(e,t)},t.shouldDisableButtons=function(e,t){return e.disableButtonsControls||!e.infinite&&o(e,t)};var a=(t.isStrategy=function(e,t){return void 0===e&&(e=""),void 0===t&&(t=""),!!(e&&e.includes(t))},function(e,n){return e||(0,t.isStrategy)(n,i.ControlsStrategy.ALTERNATE)}),r=(t.hasDotForEachSlide=a,function(e,t,n){return void 0===e&&(e=0),void 0===t&&(t=1),(n=void 0!==n&&n)?e:0!==Number(t)&&Math.ceil(e/t)||0}),s=(t.getDotsNavigationLength=r,function(e,t,n){return!t&&e===n-1}),u=(t.checkIsTheLastDotIndex=s,function(e,t,n,i){return(t?n-i:e*i)||0}),l=(t.getItemIndexForDotNavigation=u,function(e){return(e=void 0===e?"":e)===i.AutoPlayStrategy.ACTION||e===i.AutoPlayStrategy.ALL}),d=(t.shouldCancelAutoPlayOnAction=l,function(e){return(e=void 0===e?"":e)===i.AutoPlayStrategy.DEFAULT||e===i.AutoPlayStrategy.ALL});t.shouldCancelAutoPlayOnHover=d},87424:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.debounce=void 0,t.debounce=function(e,t){function n(){i&&(clearTimeout(i),i=void 0)}void 0===t&&(t=0);var i=void 0;return[function(){for(var o=this,a=[],r=0;r<arguments.length;r++)a[r]=arguments[r];n(),i=window.setTimeout(function(){e.apply(o,a),i=void 0},t)},n]}},87776:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.calculateMovingPosition=function(e){if("changedTouches"in e){var t=e.changedTouches&&e.changedTouches[0];return{x:t&&t.clientX,y:t&&t.clientY}}return{x:e.clientX,y:e.clientY}}},88253:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.getOptions=function(){var e=arguments.length>0&&void 0!==arguments[0]&&arguments[0];return e?{passive:!1}:{}}},89743:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.createOptions=function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};return Object.defineProperty(e,"passive",{get:function(){return this.isPassiveSupported=!0,!0},enumerable:!0}),e}},90950:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.calculatePosition=function(e,t){var n=e.start,u=e.x,l=e.y,d=e.traceX,c=e.traceY,f=t.rotatePosition,h=t.directionDelta,p=f.x-u,v=l-f.y,m=Math.abs(p),g=Math.abs(v);(0,i.updateTrace)(d,p),(0,i.updateTrace)(c,v);var y=(0,o.resolveDirection)(d,s.Axis.X,h),P=(0,o.resolveDirection)(c,s.Axis.Y,h),S=(0,a.calculateDuration)(n,Date.now()),b=(0,r.calculateVelocity)(m,g,S);return{absX:m,absY:g,deltaX:p,deltaY:v,directionX:y,directionY:P,duration:S,positionX:f.x,positionY:f.y,velocity:b}};var i=n(42767),o=n(73910),a=n(83301),r=n(51546),s=n(91305)},91305:(e,t)=>{var n,i,o;Object.defineProperty(t,"__esModule",{value:!0}),t.TraceDirectionKey=t.Direction=t.Axis=void 0,t.TraceDirectionKey=n,function(e){e.NEGATIVE="NEGATIVE",e.POSITIVE="POSITIVE",e.NONE="NONE"}(n||(t.TraceDirectionKey=n={})),t.Direction=i,function(e){e.TOP="TOP",e.LEFT="LEFT",e.RIGHT="RIGHT",e.BOTTOM="BOTTOM",e.NONE="NONE"}(i||(t.Direction=i={})),t.Axis=o,function(e){e.X="x",e.Y="y"}(o||(t.Axis=o={}))},92716:(e,t)=>{var n=function(){return(n=Object.assign||function(e){for(var t,n=1,i=arguments.length;n<i;n++)for(var o in t=arguments[n])Object.prototype.hasOwnProperty.call(t,o)&&(e[o]=t[o]);return e}).apply(this,arguments)},i=(Object.defineProperty(t,"__esModule",{value:!0}),t.mapPositionCoords=t.mapPartialCoords=void 0,function(e){return e.map(function(e){return{width:e.width,position:0}})}),o=(t.mapPartialCoords=i,function(e,t){return void 0===t&&(t=0),e.map(function(e){return e.position>t?n(n({},e),{position:t}):e})});t.mapPositionCoords=o},93019:(e,t,n)=>{var i=function(){return(i=Object.assign||function(e){for(var t,n=1,i=arguments.length;n<i;n++)for(var o in t=arguments[n])Object.prototype.hasOwnProperty.call(t,o)&&(e[o]=t[o]);return e}).apply(this,arguments)},o=(Object.defineProperty(t,"__esModule",{value:!0}),t.getItemsInSlide=t.canUseDOM=t.getTransformMatrix=t.getTranslateXProperty=t.getTouchmoveTranslatePosition=t.getTranslate3dProperty=t.getRenderStageItemStyles=t.getRenderStageStyles=t.getTransitionProperty=t.getRenderWrapperStyles=t.animate=t.shouldHandleResizeEvent=t.getElementFirstChild=t.getElementCursor=t.getAutoheightProperty=t.getElementDimensions=t.getItemWidth=t.createDefaultTransformationSet=t.createAutowidthTransformationSet=t.isElement=t.createClones=t.getItemsOffset=t.getItemsCount=t.getSlides=void 0,n(92716)),a=n(25936),r=(t.getSlides=function(e){var t=e.children,e=e.items;return t?t.length?t:[t]:void 0===e?[]:e},function(e){return(0,t.getSlides)(e).length}),s=(t.getItemsCount=r,function(e){var t=e.infinite,n=e.paddingRight,e=e.paddingLeft;return t&&(e||n)?1:0}),u=(t.getItemsOffset=s,function(e){var n,i,o,a,r=(0,t.getSlides)(e);return e.infinite?(n=(0,t.getItemsCount)(e),a=(0,t.getItemsOffset)(e),o=Math.min(e=(0,t.getItemsInSlide)(n,e),n)+a,i=r.slice(0,o),o=r.slice(-o),a&&e===n&&(a=r[0],e=r.slice(-1)[0],o.unshift(e),i.push(a)),o.concat(r,i)):r}),l=(t.createClones=u,function(e){try{return e instanceof Element||e instanceof HTMLDocument}catch(e){return!1}}),d=(t.isElement=l,function(e,n,i){void 0===n&&(n=0),void 0===i&&(i=!1);var a=0,r=!0,s=[];return(0,t.isElement)(e)&&(s=Array.from((null==e?void 0:e.children)||[]).reduce(function(e,t,i){var o=0,i=i-1,s=e[i],t=h(null==t?void 0:t.firstChild).width,t=void 0===t?0:t;return r=(a+=t)<=n,s&&(o=0==i?s.width:s.width+s.position),e.push({position:o,width:t}),e},[]),i||(s=r?(0,o.mapPartialCoords)(s):(e=a-n,(0,o.mapPositionCoords)(s,e)))),{coords:s,content:a,partial:r}}),c=(t.createAutowidthTransformationSet=d,function(e,n,i,a){void 0===a&&(a=!1);var r=0,s=!0,u=[],l=(0,t.getItemWidth)(n,i);return u=e.reduce(function(e,t,i){var o=0,i=e[i-1];return s=(r+=l)<=n,i&&(o=l+i.position||0),e.push({width:l,position:o}),e},[]),{coords:u=a?u:s?(0,o.mapPartialCoords)(u):(i=r-n,(0,o.mapPositionCoords)(u,i)),content:r,partial:s}}),f=(t.createDefaultTransformationSet=c,function(e,t){return 0<t?e/t:e});function h(e){return e&&e.getBoundingClientRect?{width:(e=e.getBoundingClientRect()).width,height:e.height}:{width:0,height:0}}t.getItemWidth=f,t.getElementDimensions=h;var p=(t.getAutoheightProperty=function(e,n,i){var n=(0,t.getElementCursor)(n,i),i=(0,t.getElementFirstChild)(e,n);if((0,t.isElement)(i))return n=parseFloat((e=window.getComputedStyle(i)).marginTop),e=parseFloat(e.marginBottom),Math.ceil(i.offsetHeight+n+e)},function(e,n){var i=n.activeIndex,n=n.itemsInSlide;return e.infinite?i+n+(0,t.getItemsOffset)(e):i}),v=(t.getElementCursor=p,function(e,t){return(e=e&&e.children||[])[t]&&e[t].firstChild||null});t.getElementFirstChild=v,t.shouldHandleResizeEvent=function(e,t,n){return(t=void 0===t?{}:t).width!==(n=void 0===n?{}:n).width},t.animate=function(e,n){var n=n||{},i=n.position,i=void 0===i?0:i,o=n.animationDuration,o=void 0===o?0:o,n=n.animationEasingFunction,n=void 0===n?"ease":n;return e&&(0,t.isElement)(e)&&(e.style.transition="transform ".concat(o,"ms ").concat(n," 0ms"),e.style.transform="translate3d(".concat(i,"px, 0, 0)")),e};var m=(t.getRenderWrapperStyles=function(e,n,i){var o=e||{},a=o.paddingLeft,r=o.paddingRight,s=o.autoHeight,o=o.animationDuration,s=s?(0,t.getAutoheightProperty)(i,e,n):void 0;return{height:s,transition:s?"height ".concat(o,"ms"):void 0,paddingLeft:"".concat(a,"px"),paddingRight:"".concat(r,"px")}},function(e){var e=e||{},t=e.animationDuration,e=e.animationEasingFunction,e=void 0===e?"ease":e;return"transform ".concat(void 0===t?0:t,"ms ").concat(e," 0ms")}),g=(t.getTransitionProperty=m,function(e,t){return e=(e||{}).translate3d,e="translate3d(".concat(-(void 0===e?0:e),"px, 0, 0)"),i(i({},t),{transform:e})}),y=(t.getRenderStageStyles=g,function(e,t){var n=t.transformationSet,i=t.fadeoutAnimationIndex,o=t.fadeoutAnimationPosition,a=t.fadeoutAnimationProcessing,t=t.animationDuration,n=(n[e]||{}).width;return a&&i===e?{transform:"translateX(".concat(o,"px)"),animationDuration:"".concat(t,"ms"),width:"".concat(n,"px")}:{width:n}}),P=(t.getRenderStageItemStyles=y,function(e,t){var n=e,i=t.infinite,o=t.itemsOffset,r=t.itemsInSlide,t=t.transformationSet;return((void 0===t?[]:t)[n=i?e+(0,a.getShiftIndex)(void 0===r?0:r,void 0===o?0:o):n]||{}).position||0}),S=(t.getTranslate3dProperty=P,function(e,t){return-(t-Math.floor(e))});function b(e){return e&&(0,t.isElement)(e)&&window.getComputedStyle(e).transform.match(/(-?[0-9.]+)/g)||[]}t.getTouchmoveTranslatePosition=S,t.getTranslateXProperty=function(e){return Number(e=(e=b(e))&&e[4]||"")},t.getTransformMatrix=b;var T=(t.canUseDOM=function(){var e;try{return!!(null==(e=null==window?void 0:window.document)?void 0:e.createElement)}catch(e){return!1}},function(e,n){var i,o=1,a=n.responsive,r=n.autoWidth,s=n.infinite,n=n.innerWidth;return void 0!==r&&r?void 0!==s&&s?e:o:(a&&(r=Object.keys(a)).length&&(n||(0,t.canUseDOM)())&&(i=void 0===n?window.innerWidth:n,r.forEach(function(t){var n;Number(t)<=i&&(n=(t=a[t]).items,o="contain"===(void 0===(t=t.itemsFit)?"fill":t)?n:Math.min(n,e))})),o||1)});t.getItemsInSlide=T}}]);
//# sourceMappingURL=8357-c8345767d7f41c7e.js.map