"use strict";(()=>{var e={};e.id=5500,e.ids=[636,3220,5500],e.modules={123:e=>{e.exports=require("dom-helpers/removeEventListener")},1332:e=>{e.exports=require("react-custom-scrollbars-2")},1428:e=>{e.exports=import("axios")},1680:e=>{e.exports=require("@restart/ui/utils")},1919:e=>{e.exports=require("react-transition-group/Transition")},3892:e=>{e.exports=require("classnames")},4048:e=>{e.exports=require("react-truncate")},6009:e=>{e.exports=require("dom-helpers/ownerDocument")},6952:e=>{e.exports=require("@restart/ui/Modal")},7374:e=>{e.exports=require("@restart/ui/Dropdown")},8732:e=>{e.exports=require("react/jsx-runtime")},9653:e=>{e.exports=require("@restart/ui/Overlay")},11242:e=>{e.exports=require("redux-persist/integration/react")},11688:e=>{e.exports=require("@restart/hooks/useWillUnmount")},12388:e=>{e.exports=require("dom-helpers/addEventListener")},13364:e=>{e.exports=require("redux-saga/effects")},14062:e=>{e.exports=import("react-redux")},14078:e=>{e.exports=import("swr")},14332:e=>{e.exports=require("uncontrollable")},16116:e=>{e.exports=require("invariant")},18622:e=>{e.exports=require("yup")},19121:e=>{e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},22326:e=>{e.exports=require("react-dom")},22541:e=>{e.exports=require("redux-persist/lib/storage")},25303:e=>{e.exports=require("@restart/ui/NavItem")},26324:e=>{e.exports=require("warning")},27825:e=>{e.exports=require("lodash")},27910:e=>{e.exports=require("stream")},28217:e=>{e.exports=require("@restart/ui/DropdownItem")},29021:e=>{e.exports=require("fs")},29294:e=>{e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},29628:e=>{e.exports=require("dom-helpers/addClass")},29825:e=>{e.exports=require("prop-types")},29841:e=>{e.exports=require("dom-helpers/hasClass")},30362:e=>{e.exports=require("@restart/ui/DropdownToggle")},33873:e=>{e.exports=require("path")},36653:e=>{e.exports=require("nprogress")},36955:e=>{e.exports=require("@restart/hooks/useBreakpoint")},37766:e=>{e.exports=require("@restart/hooks/useMergedRefs")},39756:e=>{e.exports=import("redux")},40051:e=>{e.exports=require("dom-helpers/removeClass")},40361:e=>{e.exports=require("next/dist/compiled/next-server/pages.runtime.prod.js")},42893:e=>{e.exports=import("react-hot-toast")},43294:e=>{e.exports=require("formik")},50009:e=>{e.exports=require("@restart/ui/DropdownMenu")},57664:e=>{e.exports=require("dom-helpers/scrollbarSize")},58928:e=>{e.exports=require("dom-helpers/canUseDOM")},59717:e=>{e.exports=require("@restart/ui/DropdownContext")},60560:e=>{e.exports=require("dom-helpers/contains")},63033:e=>{e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},65447:e=>{e.exports=require("@restart/ui/Button")},67364:e=>{e.exports=require("@restart/hooks/useCallbackRef")},68455:e=>{e.exports=import("redux-saga")},68975:(e,r,t)=>{t.a(e,async(e,s)=>{try{t.r(r),t.d(r,{config:()=>f,default:()=>d,getServerSideProps:()=>m,getStaticPaths:()=>x,getStaticProps:()=>y,reportWebVitals:()=>h,routeModule:()=>j,unstable_getServerProps:()=>v,unstable_getServerSideProps:()=>b,unstable_getStaticParams:()=>T,unstable_getStaticPaths:()=>q,unstable_getStaticProps:()=>g});var o=t(63885),i=t(80237),a=t(81413),n=t(9616),l=t.n(n),p=t(72386),u=t(99506),c=e([p]);p=(c.then?(await c)():c)[0];let d=(0,a.M)(u,"default"),y=(0,a.M)(u,"getStaticProps"),x=(0,a.M)(u,"getStaticPaths"),m=(0,a.M)(u,"getServerSideProps"),f=(0,a.M)(u,"config"),h=(0,a.M)(u,"reportWebVitals"),g=(0,a.M)(u,"unstable_getStaticProps"),q=(0,a.M)(u,"unstable_getStaticPaths"),T=(0,a.M)(u,"unstable_getStaticParams"),v=(0,a.M)(u,"unstable_getServerProps"),b=(0,a.M)(u,"unstable_getServerSideProps"),j=new o.PagesRouteModule({definition:{kind:i.A.PAGES,page:"/country/components/CountryCoverSection",pathname:"/country/components/CountryCoverSection",bundlePath:"",filename:""},components:{App:p.default,Document:l()},userland:u});s()}catch(e){s(e)}})},69722:e=>{e.exports=require("es6-promise")},72953:(e,r,t)=>{t.d(r,{A:()=>y});var s=t(8732);t(82015);var o=t(94696);let i=({position:e,onCloseClick:r,children:t})=>(0,s.jsx)(o.InfoWindow,{position:e,onCloseClick:r,children:(0,s.jsx)("div",{children:t})}),a="labels.text.fill",n="labels.text.stroke",l="road.highway",p="geometry.stroke",u=[{elementType:"geometry",stylers:[{color:"#1d2c4d"}]},{elementType:a,stylers:[{color:"#8ec3b9"}]},{elementType:n,stylers:[{color:"#1a3646"}]},{featureType:"administrative",elementType:"geometry",stylers:[{visibility:"off"}]},{featureType:"administrative.country",elementType:p,stylers:[{color:"#4b6878"}]},{featureType:"administrative.land_parcel",elementType:"labels",stylers:[{visibility:"off"}]},{featureType:"administrative.land_parcel",elementType:a,stylers:[{color:"#64779e"}]},{featureType:"administrative.province",elementType:p,stylers:[{color:"#4b6878"}]},{featureType:"landscape.man_made",elementType:p,stylers:[{color:"#334e87"}]},{featureType:"landscape.natural",elementType:"geometry",stylers:[{color:"#023e58"}]},{featureType:"poi",stylers:[{visibility:"off"}]},{featureType:"poi",elementType:"geometry",stylers:[{color:"#283d6a"}]},{featureType:"poi",elementType:"labels.text",stylers:[{visibility:"off"}]},{featureType:"poi",elementType:a,stylers:[{color:"#6f9ba5"}]},{featureType:"poi",elementType:n,stylers:[{color:"#1d2c4d"}]},{featureType:"poi.park",elementType:"geometry.fill",stylers:[{color:"#023e58"}]},{featureType:"poi.park",elementType:a,stylers:[{color:"#3C7680"}]},{featureType:"road",stylers:[{visibility:"off"}]},{featureType:"road",elementType:"geometry",stylers:[{color:"#304a7d"}]},{featureType:"road",elementType:"labels.icon",stylers:[{visibility:"off"}]},{featureType:"road",elementType:a,stylers:[{color:"#98a5be"}]},{featureType:"road",elementType:n,stylers:[{color:"#1d2c4d"}]},{featureType:l,elementType:"geometry",stylers:[{color:"#2c6675"}]},{featureType:l,elementType:p,stylers:[{color:"#255763"}]},{featureType:l,elementType:a,stylers:[{color:"#b0d5ce"}]},{featureType:l,elementType:n,stylers:[{color:"#023e58"}]},{featureType:"road.local",elementType:"labels",stylers:[{visibility:"off"}]},{featureType:"transit",stylers:[{visibility:"off"}]},{featureType:"transit",elementType:a,stylers:[{color:"#98a5be"}]},{featureType:"transit",elementType:n,stylers:[{color:"#1d2c4d"}]},{featureType:"transit.line",elementType:"geometry.fill",stylers:[{color:"#283d6a"}]},{featureType:"transit.station",elementType:"geometry",stylers:[{color:"#3a4762"}]},{featureType:"water",elementType:"geometry",stylers:[{color:"#0e1626"}]},{featureType:"water",elementType:a,stylers:[{color:"#4e6d70"}]}];var c=t(44233),d=t(40691);let y=({markerInfo:e,activeMarker:r,initialCenter:t,children:a,height:n=300,width:l="114%",language:p,zoom:y=1,minZoom:x=1,onClose:m})=>{let{locale:f}=(0,c.useRouter)(),{isLoaded:h,loadError:g}=(0,d._)(),q={width:l,height:"number"==typeof n?`${n}px`:n};return g?(0,s.jsx)("div",{children:"Error loading maps"}):h?(0,s.jsx)("div",{className:"map-container",children:(0,s.jsx)("div",{className:"mapprint",style:{width:l,height:n,position:"relative"},children:(0,s.jsxs)(o.GoogleMap,{mapContainerStyle:q,center:t||{lat:52.520017,lng:13.404195},zoom:y,onLoad:e=>{e.setOptions({styles:u})},options:{minZoom:x,draggable:!0,keyboardShortcuts:!1,streetViewControl:!1,panControl:!1,clickableIcons:!1,mapTypeControl:!1,fullscreenControl:!0},children:[a,e&&r&&r.getPosition&&(0,s.jsx)(i,{position:r.getPosition(),onCloseClick:()=>{console.log("close click"),m?.()},children:e})]})})}):(0,s.jsx)("div",{children:"Loading Maps..."})}},74075:e=>{e.exports=require("zlib")},74716:e=>{e.exports=require("moment")},74987:e=>{e.exports=require("@restart/ui/ModalManager")},78097:e=>{e.exports=require("next-redux-saga")},78634:e=>{e.exports=require("@restart/ui/Anchor")},80237:(e,r)=>{Object.defineProperty(r,"A",{enumerable:!0,get:function(){return t}});var t=function(e){return e.PAGES="PAGES",e.PAGES_API="PAGES_API",e.APP_PAGE="APP_PAGE",e.APP_ROUTE="APP_ROUTE",e.IMAGE="IMAGE",e}({})},81366:e=>{e.exports=require("redux-auth-wrapper/connectedAuthWrapper")},81413:(e,r)=>{Object.defineProperty(r,"M",{enumerable:!0,get:function(){return function e(r,t){return t in r?r[t]:"then"in r&&"function"==typeof r.then?r.then(r=>e(r,t)):"function"==typeof r&&"default"===t?r:void 0}}})},81521:e=>{e.exports=require("dom-helpers/querySelectorAll")},81895:e=>{e.exports=require("@restart/hooks/useEventCallback")},82015:e=>{e.exports=require("react")},86842:e=>{e.exports=require("@restart/ui/SelectableContext")},87571:e=>{e.exports=require("dom-helpers/transitionEnd")},88751:e=>{e.exports=require("next-i18next")},89364:(e,r,t)=>{t.d(r,{A:()=>i});var s=t(8732);t(82015);var o=t(94696);let i=({name:e="Marker",id:r="",countryId:t="",type:i,icon:a,position:n,onClick:l,title:p,draggable:u=!1})=>n&&"number"==typeof n.lat&&"number"==typeof n.lng?(0,s.jsx)(o.Marker,{position:n,icon:a,title:p||e,draggable:u,onClick:s=>{l&&l({name:e,id:r,countryId:t,type:i,position:n},{position:n,getPosition:()=>n},s)}}):null},93787:e=>{e.exports=require("redux-persist")},94696:e=>{e.exports=require("@react-google-maps/api")},94947:e=>{e.exports=require("@restart/hooks/useTimeout")},96196:e=>{e.exports=require("next-redux-wrapper")},98320:e=>{e.exports=require("@restart/hooks/useIsomorphicEffect")},99460:e=>{e.exports=require("dom-helpers/css")},99506:(e,r,t)=>{t.r(r),t.d(r,{default:()=>c});var s=t(8732);t(82015);var o=t(83551),i=t(49481),a=t(19918),n=t.n(a),l=t(72953),p=t(89364),u=t(88751);let c=e=>{let{t:r}=(0,u.useTranslation)("common");return(0,s.jsx)(s.Fragment,{children:(0,s.jsx)(o.A,{children:(0,s.jsxs)(i.A,{xs:12,style:{display:"flex"},children:[(0,s.jsx)("div",{className:"countryMap",children:e?.latlng?.lat?(0,s.jsx)(l.A,{initialCenter:e.latlng,children:(0,s.jsx)(p.A,{icon:{url:"/images/map-marker-blue.svg"},position:e.latlng})}):null}),(0,s.jsxs)("div",{className:"countryInfo",children:[(0,s.jsxs)("h4",{children:[" ",e?.countryData?.title," "]}),function(e,r,t,o,i,a){return(0,s.jsxs)("div",{className:"countryInfoDetails",children:[(0,s.jsx)(n(),{href:{pathname:"/operation",query:{country:e?._id,status:r}},children:(0,s.jsxs)("div",{className:"countryInfo-Item",children:[(0,s.jsx)("div",{className:"countryInfo-img",children:(0,s.jsx)("img",{src:"/images/countryinfo1.png",width:"25",height:"25",alt:"Organization Quick Info"})}),(0,s.jsxs)("span",{children:[t("CurrentOperation"),(0,s.jsx)("br",{}),(0,s.jsx)("b",{children:o?.operation_count?o.operation_count:0})]})]})}),(0,s.jsx)(n(),{href:{pathname:"/event",query:{country:e?._id,status:i}},children:(0,s.jsxs)("div",{className:"countryInfo-Item",children:[(0,s.jsx)("div",{className:"countryInfo-img",children:(0,s.jsx)("img",{src:"/images/countryinfo2.png",width:"30",height:"22",alt:"Organization Quick Info"})}),(0,s.jsxs)("span",{children:[t("CurrentEvent"),(0,s.jsx)("br",{}),(0,s.jsx)("b",{children:o?.event_count?o.event_count:0})]})]})}),(0,s.jsx)(n(),{href:{pathname:"/project",query:{country:e._id,status:a}},children:(0,s.jsxs)("div",{className:"countryInfo-Item",children:[(0,s.jsx)("div",{className:"countryInfo-img",children:(0,s.jsx)("img",{src:"/images/quickinfo3.png",width:"24",height:"21",alt:"Organization Quick Info"})}),(0,s.jsxs)("span",{children:[t("CurrentProject"),(0,s.jsx)("br",{}),(0,s.jsx)("b",{children:o?.project_count?o.project_count:0})]})]})})]})}(e.countryData,e.operationStatusId,r,e.countryStatsData,e.eventStatusId,e.projectStatusId)]})]})})})}}};var r=require("../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[6089,9216,9616,2386],()=>t(68975));module.exports=s})();