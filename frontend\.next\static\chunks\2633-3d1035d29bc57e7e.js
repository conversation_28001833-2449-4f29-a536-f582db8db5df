"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[2633],{67814:(e,t,r)=>{r.d(t,{KF:()=>w});var n=r(14232),a=r(37876);!function(e,{insertAt:t}={}){if(!e||typeof document>"u")return;let r=document.head||document.getElementsByTagName("head")[0],n=document.createElement("style");n.type="text/css","top"===t&&r.firstChild?r.insertBefore(n,r.firstChild):r.appendChild(n),n.styleSheet?n.styleSheet.cssText=e:n.appendChild(document.createTextNode(e))}(`.rmsc{--rmsc-main: #4285f4;--rmsc-hover: #f1f3f5;--rmsc-selected: #e2e6ea;--rmsc-border: #ccc;--rmsc-gray: #aaa;--rmsc-bg: #fff;--rmsc-p: 10px;--rmsc-radius: 4px;--rmsc-h: 38px}.rmsc *{box-sizing:border-box;transition:all .2s ease}.rmsc .gray{color:var(--rmsc-gray)}.rmsc .dropdown-content{position:absolute;z-index:1;top:100%;width:100%;padding-top:8px}.rmsc .dropdown-content .panel-content{overflow:hidden;border-radius:var(--rmsc-radius);background:var(--rmsc-bg);box-shadow:0 0 0 1px #0000001a,0 4px 11px #0000001a}.rmsc .dropdown-container{position:relative;outline:0;background-color:var(--rmsc-bg);border:1px solid var(--rmsc-border);border-radius:var(--rmsc-radius)}.rmsc .dropdown-container[aria-disabled=true]:focus-within{box-shadow:var(--rmsc-gray) 0 0 0 1px;border-color:var(--rmsc-gray)}.rmsc .dropdown-container:focus-within{box-shadow:var(--rmsc-main) 0 0 0 1px;border-color:var(--rmsc-main)}.rmsc .dropdown-heading{position:relative;padding:0 var(--rmsc-p);display:flex;align-items:center;width:100%;height:var(--rmsc-h);cursor:default;outline:0}.rmsc .dropdown-heading .dropdown-heading-value{overflow:hidden;text-overflow:ellipsis;white-space:nowrap;flex:1}.rmsc .clear-selected-button{cursor:pointer;background:none;border:0;padding:0;display:flex}.rmsc .options{max-height:260px;overflow-y:auto;margin:0;padding-left:0}.rmsc .options li{list-style:none;margin:0}.rmsc .select-item{box-sizing:border-box;cursor:pointer;display:block;padding:var(--rmsc-p);outline-offset:-1px;outline-color:var(--rmsc-primary)}.rmsc .select-item:hover{background:var(--rmsc-hover)}.rmsc .select-item.selected{background:var(--rmsc-selected)}.rmsc .no-options{padding:var(--rmsc-p);text-align:center;color:var(--rmsc-gray)}.rmsc .search{width:100%;position:relative;border-bottom:1px solid var(--rmsc-border)}.rmsc .search input{background:none;height:var(--rmsc-h);padding:0 var(--rmsc-p);width:100%;outline:0;border:0;font-size:1em}.rmsc .search input:focus{background:var(--rmsc-hover)}.rmsc .search-clear-button{cursor:pointer;position:absolute;top:0;right:0;bottom:0;background:none;border:0;padding:0 calc(var(--rmsc-p) / 2)}.rmsc .search-clear-button [hidden]{display:none}.rmsc .item-renderer{display:flex;align-items:baseline}.rmsc .item-renderer input{margin:0 5px 0 0}.rmsc .item-renderer.disabled{opacity:.5}.rmsc .spinner{animation:rotate 2s linear infinite}.rmsc .spinner .path{stroke:var(--rmsc-border);stroke-width:4px;stroke-linecap:round;animation:dash 1.5s ease-in-out infinite}@keyframes rotate{to{transform:rotate(360deg)}}@keyframes dash{0%{stroke-dasharray:1,150;stroke-dashoffset:0}50%{stroke-dasharray:90,150;stroke-dashoffset:-35}to{stroke-dasharray:90,150;stroke-dashoffset:-124}}
`);var l={allItemsAreSelected:"All items are selected.",clearSearch:"Clear Search",clearSelected:"Clear Selected",noOptions:"No options",search:"Search",selectAll:"Select All",selectAllFiltered:"Select All (Filtered)",selectSomeItems:"Select...",create:"Create"},o={value:[],hasSelectAll:!0,className:"multi-select",debounceDuration:200,options:[]},s=n.createContext({}),c=({props:e,children:t})=>{let[r,c]=(0,n.useState)(e.options);return(0,n.useEffect)(()=>{c(e.options)},[e.options]),(0,a.jsx)(s.Provider,{value:{t:t=>{var r;return(null==(r=e.overrideStrings)?void 0:r[t])||l[t]},...o,...e,options:r,setOptions:c},children:t})},i=()=>n.useContext(s),d={when:!0,eventTypes:["keydown"]};function u(e,t,r){let a=(0,n.useMemo)(()=>Array.isArray(e)?e:[e],[e]),l=Object.assign({},d,r),{when:o,eventTypes:s}=l,c=(0,n.useRef)(t),{target:i}=l;(0,n.useEffect)(()=>{c.current=t});let u=(0,n.useCallback)(e=>{a.some(t=>e.key===t||e.code===t)&&c.current(e)},[a]);(0,n.useEffect)(()=>{if(o&&"u">typeof window){let e=i?i.current:window;return s.forEach(t=>{e&&e.addEventListener(t,u)}),()=>{s.forEach(t=>{e&&e.removeEventListener(t,u)})}}},[o,s,a,i,t])}var m={ARROW_DOWN:"ArrowDown",ARROW_UP:"ArrowUp",ENTER:"Enter",ESCAPE:"Escape",SPACE:"Space"},p=(e,t)=>{let r;return function(...n){clearTimeout(r),r=setTimeout(()=>{e.apply(null,n)},t)}},f=()=>(0,a.jsxs)("svg",{width:"24",height:"24",fill:"none",stroke:"currentColor",strokeWidth:"2",className:"dropdown-search-clear-icon gray",children:[(0,a.jsx)("line",{x1:"18",y1:"6",x2:"6",y2:"18"}),(0,a.jsx)("line",{x1:"6",y1:"6",x2:"18",y2:"18"})]}),h=({checked:e,option:t,onClick:r,disabled:n})=>(0,a.jsxs)("div",{className:`item-renderer ${n?"disabled":""}`,children:[(0,a.jsx)("input",{type:"checkbox",onChange:r,checked:e,tabIndex:-1,disabled:n}),(0,a.jsx)("span",{children:t.label})]}),b=({itemRenderer:e=h,option:t,checked:r,tabIndex:l,disabled:o,onSelectionChanged:s,onClick:c})=>{let i=(0,n.useRef)(),d=()=>{o||s(!r)};return u([m.ENTER,m.SPACE],e=>{d(),e.preventDefault()},{target:i}),(0,a.jsx)("label",{className:`select-item ${r?"selected":""}`,role:"option","aria-selected":r,tabIndex:l,ref:i,children:(0,a.jsx)(e,{option:t,checked:r,onClick:e=>{d(),c(e)},disabled:o})})},v=({options:e,onClick:t,skipIndex:r})=>{let{disabled:n,value:l,onChange:o,ItemRenderer:s}=i(),c=(e,t)=>{n||o(t?[...l,e]:l.filter(t=>t.value!==e.value))};return(0,a.jsx)(a.Fragment,{children:e.map((e,o)=>{let i=o+r;return(0,a.jsx)("li",{children:(0,a.jsx)(b,{tabIndex:i,option:e,onSelectionChanged:t=>c(e,t),checked:!!l.find(t=>t.value===e.value),onClick:e=>t(e,i),itemRenderer:s,disabled:e.disabled||n})},(null==e?void 0:e.key)||o)})})},g=()=>{let{t:e,onChange:t,options:r,setOptions:l,value:o,filterOptions:s,ItemRenderer:c,disabled:d,disableSearch:h,hasSelectAll:g,ClearIcon:y,debounceDuration:x,isCreatable:C,onCreateOption:k}=i(),w=(0,n.useRef)(),E=(0,n.useRef)(),[N,j]=(0,n.useState)(""),[O,S]=(0,n.useState)(r),[A,R]=(0,n.useState)(""),[I,_]=(0,n.useState)(0),T=(0,n.useCallback)(p(e=>R(e),x),[]),P=(0,n.useMemo)(()=>{let e=0;return h||(e+=1),g&&(e+=1),e},[h,g]),W={label:e(N?"selectAllFiltered":"selectAll"),value:""},L=e=>{let t=O.filter(e=>!e.disabled).map(e=>e.value);if(e){let e=[...o.map(e=>e.value),...t];return(s?O:r).filter(t=>e.includes(t.value))}return o.filter(e=>!t.includes(e.value))},M=()=>{var e;R(""),j(""),null==(e=null==E?void 0:E.current)||e.focus()},U=e=>_(e);u([m.ARROW_DOWN,m.ARROW_UP],e=>{switch(e.code){case m.ARROW_UP:F(-1);break;case m.ARROW_DOWN:F(1);break;default:return}e.stopPropagation(),e.preventDefault()},{target:w});let B=async()=>{let e={label:N,value:N,__isNew__:!0};k&&(e=await k(N)),l([e,...r]),M(),t([...o,e])},D=async()=>s?await s(r,A):function(e,t){return t?e.filter(({label:e,value:r})=>null!=e&&null!=r&&e.toLowerCase().includes(t.toLowerCase())):e}(r,A),F=e=>{let t=I+e;_(t=Math.min(t=Math.max(0,t),r.length+Math.max(P-1,0)))};(0,n.useEffect)(()=>{var e,t;null==(t=null==(e=null==w?void 0:w.current)?void 0:e.querySelector(`[tabIndex='${I}']`))||t.focus()},[I]);let[$,z]=(0,n.useMemo)(()=>{let e=O.filter(e=>!e.disabled);return[e.every(e=>-1!==o.findIndex(t=>t.value===e.value)),0!==e.length]},[O,o]);(0,n.useEffect)(()=>{D().then(S)},[A,r]);let K=(0,n.useRef)();u([m.ENTER],B,{target:K});let q=C&&N&&!O.some(e=>(null==e?void 0:e.value)===N);return(0,a.jsxs)("div",{className:"select-panel",role:"listbox",ref:w,children:[!h&&(0,a.jsxs)("div",{className:"search",children:[(0,a.jsx)("input",{placeholder:e("search"),type:"text","aria-describedby":e("search"),onChange:e=>{T(e.target.value),j(e.target.value),_(0)},onFocus:()=>{_(0)},value:N,ref:E,tabIndex:0}),(0,a.jsx)("button",{type:"button",className:"search-clear-button",hidden:!N,onClick:M,"aria-label":e("clearSearch"),children:y||(0,a.jsx)(f,{})})]}),(0,a.jsxs)("ul",{className:"options",children:[g&&z&&(0,a.jsx)(b,{tabIndex:+(1!==P),checked:$,option:W,onSelectionChanged:e=>{t(L(e))},onClick:()=>U(1),itemRenderer:c,disabled:d}),O.length?(0,a.jsx)(v,{skipIndex:P,options:O,onClick:(e,t)=>U(t)}):q?(0,a.jsx)("li",{onClick:B,className:"select-item creatable",tabIndex:1,ref:K,children:`${e("create")} "${N}"`}):(0,a.jsx)("li",{className:"no-options",children:e("noOptions")})]})]})},y=({expanded:e})=>(0,a.jsx)("svg",{width:"24",height:"24",fill:"none",stroke:"currentColor",strokeWidth:"2",className:"dropdown-heading-dropdown-arrow gray",children:(0,a.jsx)("path",{d:e?"M18 15 12 9 6 15":"M6 9L12 15 18 9"})}),x=()=>{let{t:e,value:t,options:r,valueRenderer:n}=i(),l=0===t.length,o=t.length===r.length,s=n&&n(t,r);return l?(0,a.jsx)("span",{className:"gray",children:s||e("selectSomeItems")}):(0,a.jsx)("span",{children:s||(o?e("allItemsAreSelected"):t.map(e=>e.label).join(", "))})},C=({size:e=24})=>(0,a.jsx)("span",{style:{width:e,marginRight:"0.2rem"},children:(0,a.jsx)("svg",{width:e,height:e,className:"spinner",viewBox:"0 0 50 50",style:{display:"inline",verticalAlign:"middle"},children:(0,a.jsx)("circle",{cx:"25",cy:"25",r:"20",fill:"none",className:"path"})})}),k=()=>{let{t:e,onMenuToggle:t,ArrowRenderer:r,shouldToggleOnHover:l,isLoading:o,disabled:s,onChange:c,labelledBy:d,value:p,isOpen:h,defaultIsOpen:b,ClearSelectedIcon:v,closeOnChangedValue:k}=i();(0,n.useEffect)(()=>{k&&j(!1)},[p]);let[w,E]=(0,n.useState)(!0),[N,j]=(0,n.useState)(b),[O,S]=(0,n.useState)(!1),A=(0,n.useRef)();(function(e,t){let r=(0,n.useRef)(!1);(0,n.useEffect)(()=>{r.current?e():r.current=!0},t)})(()=>{t&&t(N)},[N]),(0,n.useEffect)(()=>{void 0===b&&"boolean"==typeof h&&(E(!1),j(h))},[h]),u([m.ENTER,m.ARROW_DOWN,m.SPACE,m.ESCAPE],e=>{var t;["text","button"].includes(e.target.type)&&[m.SPACE,m.ENTER].includes(e.code)||(w&&(e.code===m.ESCAPE?(j(!1),null==(t=null==A?void 0:A.current)||t.focus()):j(!0)),e.preventDefault())},{target:A});let R=e=>{w&&l&&j(e)};return(0,a.jsxs)("div",{tabIndex:0,className:"dropdown-container","aria-labelledby":d,"aria-expanded":N,"aria-readonly":!0,"aria-disabled":s,ref:A,onFocus:()=>!O&&S(!0),onBlur:e=>{!e.currentTarget.contains(e.relatedTarget)&&w&&(S(!1),j(!1))},onMouseEnter:()=>R(!0),onMouseLeave:()=>R(!1),children:[(0,a.jsxs)("div",{className:"dropdown-heading",onClick:()=>{w&&j(!o&&!s&&!N)},children:[(0,a.jsx)("div",{className:"dropdown-heading-value",children:(0,a.jsx)(x,{})}),o&&(0,a.jsx)(C,{}),p.length>0&&null!==v&&(0,a.jsx)("button",{type:"button",className:"clear-selected-button",onClick:e=>{e.stopPropagation(),c([]),w&&j(!1)},disabled:s,"aria-label":e("clearSelected"),children:v||(0,a.jsx)(f,{})}),(0,a.jsx)(r||y,{expanded:N})]}),N&&(0,a.jsx)("div",{className:"dropdown-content",children:(0,a.jsx)("div",{className:"panel-content",children:(0,a.jsx)(g,{})})})]})},w=e=>(0,a.jsx)(c,{props:e,children:(0,a.jsx)("div",{className:`rmsc ${e.className||"multi-select"}`,children:(0,a.jsx)(k,{})})})},87902:(e,t,r)=>{var n,a,l=function(){function e(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,n.key,n)}}return function(t,r,n){return r&&e(t.prototype,r),n&&e(t,n),t}}();t.ZX=function(e){if(document.body.classList.add("react-confirm-alert-body-element"),!document.getElementById("react-confirm-alert-firm-svg")){var t,r="http://www.w3.org/2000/svg",n=document.createElementNS(r,"feGaussianBlur");n.setAttribute("stdDeviation","0.3");var a=document.createElementNS(r,"filter");a.setAttribute("id","gaussian-blur"),a.appendChild(n);var l=document.createElementNS(r,"svg");l.setAttribute("id","react-confirm-alert-firm-svg"),l.setAttribute("class","react-confirm-alert-svg"),l.appendChild(a),document.body.appendChild(l)}(t=document.getElementById("react-confirm-alert"))||(document.body.children[0].classList.add("react-confirm-alert-blur"),(t=document.createElement("div")).id="react-confirm-alert",document.body.appendChild(t)),(0,i.render)(s.default.createElement(m,e),t)};var o=r(14232),s=d(o),c=d(r(95062)),i=r(98477);function d(e){return e&&e.__esModule?e:{default:e}}function u(e,t){if(!e)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return t&&("object"==typeof t||"function"==typeof t)?t:e}var m=(a=n=function(e){if("function"!=typeof e&&null!==e)throw TypeError("Super expression must either be null or a function, not "+typeof e);function t(){if(!(this instanceof t))throw TypeError("Cannot call a class as a function");for(var e,r,n,a=arguments.length,l=Array(a),o=0;o<a;o++)l[o]=arguments[o];return r=n=u(this,(e=t.__proto__||Object.getPrototypeOf(t)).call.apply(e,[this].concat(l))),n.handleClickButton=function(e){e.onClick&&e.onClick(),n.close()},n.handleClickOverlay=function(e){var t=n.props,r=t.closeOnClickOutside,a=t.onClickOutside,l=e.target===n.overlay;r&&l&&(a(),n.close())},n.close=function(){var e,t,r,a=n.props.afterClose;document.body.classList.remove("react-confirm-alert-body-element"),(e=document.getElementById("react-confirm-alert"))&&((0,i.unmountComponentAtNode)(e),e.parentNode.removeChild(e)),t=a,(r=document.getElementById("react-confirm-alert-firm-svg"))&&r.parentNode.removeChild(r),document.body.children[0].classList.remove("react-confirm-alert-blur"),t()},n.keyboardClose=function(e){var t=n.props,r=t.closeOnEscape,a=t.onKeypressEscape,l=t.keyCodeForClose,o=e.keyCode,s=27===o;l.includes(o)&&n.close(),r&&s&&(a(e),n.close())},n.componentDidMount=function(){document.addEventListener("keydown",n.keyboardClose,!1)},n.componentWillUnmount=function(){document.removeEventListener("keydown",n.keyboardClose,!1),n.props.willUnmount()},n.renderCustomUI=function(){var e=n.props,t=e.title,r=e.message,a=e.buttons;return(0,e.customUI)({title:t,message:r,buttons:a,onClose:n.close})},u(n,r)}return t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,enumerable:!1,writable:!0,configurable:!0}}),e&&(Object.setPrototypeOf?Object.setPrototypeOf(t,e):t.__proto__=e),l(t,[{key:"render",value:function(){var e=this,t=this.props,r=t.title,n=t.message,a=t.buttons,l=t.childrenElement,o=t.customUI,c=t.overlayClassName;return s.default.createElement("div",{className:"react-confirm-alert-overlay "+c,ref:function(t){return e.overlay=t},onClick:this.handleClickOverlay},s.default.createElement("div",{className:"react-confirm-alert"},o?this.renderCustomUI():s.default.createElement("div",{className:"react-confirm-alert-body"},r&&s.default.createElement("h1",null,r),n,l(),s.default.createElement("div",{className:"react-confirm-alert-button-group"},a.map(function(t,r){return s.default.createElement("button",{key:r,onClick:function(){return e.handleClickButton(t)},className:t.className},t.label)})))))}}]),t}(o.Component),n.propTypes={title:c.default.string,message:c.default.string,buttons:c.default.array.isRequired,childrenElement:c.default.func,customUI:c.default.func,closeOnClickOutside:c.default.bool,closeOnEscape:c.default.bool,keyCodeForClose:c.default.arrayOf(c.default.number),willUnmount:c.default.func,afterClose:c.default.func,onClickOutside:c.default.func,onKeypressEscape:c.default.func,overlayClassName:c.default.string},n.defaultProps={buttons:[{label:"Cancel",onClick:function(){return null},className:null},{label:"Confirm",onClick:function(){return null},className:null}],childrenElement:function(){return null},closeOnClickOutside:!0,closeOnEscape:!0,keyCodeForClose:[],willUnmount:function(){return null},afterClose:function(){return null},onClickOutside:function(){return null},onKeypressEscape:function(){return null}},a)}}]);
//# sourceMappingURL=2633-3d1035d29bc57e7e.js.map