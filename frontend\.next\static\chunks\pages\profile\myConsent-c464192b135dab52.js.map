{"version": 3, "file": "static/chunks/pages/profile/myConsent-c464192b135dab52.js", "mappings": "wMAOA,IAAMA,EAAgBC,CAAAA,EAAAA,EAAAA,CAAAA,CAAgBA,CAAC,KACvCD,GAAcE,WAAW,CAAG,gBAC5B,IAAMC,EAA4BC,EAAAA,UAAgB,CAA7B,CAA8B,EAKhDC,QAL6B,WAC9BC,CAAS,UACTC,CAAQ,CACRC,GAAIC,EAAYT,CAAa,CAC7B,GAAGU,EACJ,GAEC,OADAH,EAAWI,CAAAA,EAAAA,EAAAA,EAAAA,CAAkBA,CAACJ,EAAU,iBACpBK,CAAAA,EAAAA,EAAAA,GAAAA,CAAIA,CAACH,EAAW,CAClCJ,IAAKA,EACLC,UAAWO,IAAWP,EAAWC,GACjC,GAAGG,CAAK,EAEZ,GACAP,EAJyBU,WAID,CAAG,8BCf3B,IAAMC,EAAyBV,EAAAA,QAAb,EAA6B,CAAC,GAK7CC,KAL0B,GAAoB,WAC/CC,CAAS,UACTC,CAAQ,CACRC,GAAIC,EAAYM,EAAAA,CAAM,CACtB,GAAGL,EACJ,GAEC,OADAH,EAAWI,CAAAA,EAAAA,EAAAA,EAAAA,CAAkBA,CAACJ,EAAU,cACpBK,CAAAA,EAAAA,EAAAA,GAAAA,CAAIA,CAACH,EAAW,CAClCJ,IAAKA,EACLC,UAAWO,IAAWP,EAAWC,GACjC,GAAGG,CAAK,EAEZ,GACAI,EAAUZ,WAAW,CAAG,sCCPxB,IAAMc,EAAqBZ,EAAAA,IAAb,MAA6B,CAAC,CAACa,EAAmBZ,CAAvC,IACvB,GAAM,CACJE,UAAQ,MACRW,GAAO,CAAI,YACXC,EAAa,aAAa,cAC1BC,CAAY,WACZd,CAAS,UACTe,CAAQ,SACRC,EAAU,SAAS,SACnBC,CAAO,CACPC,aAAW,YACXC,EAAaC,EAAAA,CAAI,CACjB,GAAGhB,EACJ,CAAGiB,CAAAA,EAAAA,EAAAA,EAAAA,CAAeA,CAACV,EAAmB,CACrCC,KAAM,SACR,GACMU,EAASjB,CAAAA,EAAAA,EAAAA,EAAAA,CAAkBA,CAACJ,EAAU,SACtCsB,EAAcC,CAAAA,EAAAA,EAAAA,CAAAA,CAAgBA,CAACC,IAC/BR,GACFA,EAAQ,GAAOQ,CADJ,CAGf,GACMC,EAAaP,CAAe,MAAOC,EAAAA,CAAIA,CAAGD,EAC1CQ,EAAqBC,CAAAA,EAAAA,EAAAA,CAAb,GAAaA,CAAKA,CAAC,MAAR,CACvBC,KAAM,QACN,GAAI,CAACH,EAAatB,OAAQ0B,CAAS,CACnC/B,IAAKA,EACLC,UAAWO,IAAWP,EAAWsB,EAAQN,GAAW,GAAaA,MAA5CT,CAAkCe,EAAO,KAAW,OAARN,GAAWE,GAAe,GAAU,OAAPI,EAAO,iBACrGP,SAAU,CAACG,GAA4BZ,CAAAA,EAAAA,EAAAA,GAAAA,CAAIA,CAACyB,EAAlB,CAA6BA,CAAE,CACvDC,QADmC,EAEnC,aAAcnB,EACdG,QAASF,CACX,GAAIC,EACN,UACA,EACoBT,CAAAA,CADhB,CACgBA,EAAAA,GAAAA,CAAIA,CAACoB,EAAY,CACnCO,eAAe,EACf,GAAG7B,CAAK,CACRL,IAAK+B,OACLI,GAAItB,EACJG,SAAUY,CACZ,GAPwBf,EAAOe,EAAQ,IAQzC,GACAjB,EAAMd,WAAW,CAAG,QACpB,MAAeuC,OAAOC,MAAM,CAAC1B,EAAO,CAClC2B,KDrCa7B,CCqCPA,CACN8B,ODtCsB9B,CDETX,CEoCJA,EACT,CAFeW,CAEd,OFrCwBX,EAAC,GEoCLA,gICqCvB,MAxFkB,OAAC,QAAE0C,CAAM,YAwFZC,CAxFcC,CAAW,IAAEC,CAAE,CAA+C,GACnF,GAAEC,CAAC,CAAE,CAAGC,CAAAA,EAAAA,EAAAA,EAAAA,CAAcA,CAAC,UACvBC,EAAqB,CACzBC,UAAU,EACVC,SAAU,GACVC,SAAU,GACVC,UAAU,CACZ,EACM,CAACC,EAAUC,EAAY,CAAGC,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAACP,GACnC,CAACQ,EAAeC,EAAiB,CAAGF,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,EAAC,GAE7CG,EAAiB,IACrB,GAAM,MAAEC,CAAI,SAAEC,CAAO,CAAE,CAAGhC,EAAEiC,MAAM,CAClCP,EAAaQ,GAAe,EAAE,GAAGA,CAAS,CAAE,CAACH,CAAjB,CAAsB,CAAEC,EAAQ,GAC5DH,EAAiB,CAACD,EACpB,EACMO,EAAe,IACnBT,EAAYN,GACZS,EAAiBO,EACnB,EAKA,MACE,+BACE,WAACC,EAAAA,CAAKA,CAAAA,CACJlD,KAAM2B,EACNwB,OAPoB,CAOZC,IANZvB,GAAY,EACd,EAMMwB,KAAK,KACLvB,GAAG,eACH1C,UAAU,kBAEV,UAAC8D,EAAAA,CAAKA,CAACI,MAAM,EAACC,WAAW,aACvB,UAACL,EAAAA,CAAKA,CAACM,KAAK,WAAEzB,EAAE,yBAElB,UAACmB,EAAAA,CAAKA,CAACO,IAAI,WACT,WAACC,MAAAA,CAAItE,UAAU,sBACb,UAACU,EAAAA,CAAKA,CAAAA,CAACM,QAAQ,kBAAU2B,EAAE,sBAC3B,UAAC4B,EAAAA,CAAIA,CAACC,KAAK,EACTxE,UAAU,OACVyE,KAAK,WACLjB,KAAK,WACLkB,SAAUnB,EACVE,QAASP,EAASJ,QAAQ,CAC1B6B,MAAM,WACNC,MAAOjC,EAAE,0BAEV,UAAC4B,EAAAA,CAAIA,CAACC,KAAK,EACVxE,UAAU,OACVwD,KAAK,WACLkB,SAAUnB,EACVkB,KAAK,WACLhB,QAASP,EAASH,QAAQ,CAC1B4B,MAAM,WACNC,MAAOjC,EAAE,0BAEX,UAAC4B,EAAAA,CAAIA,CAACC,KAAK,EACTxE,UAAU,OACVwD,KAAK,WACLkB,SAAUnB,EACVkB,KAAK,WACLhB,QAASP,EAASF,QAAQ,CAC1B2B,MAAM,WACNC,MAAOjC,EAAE,0BAEX,UAAC4B,EAAAA,CAAIA,CAACC,KAAK,EACTxE,UAAU,OACVyE,KAAK,WACLjB,KAAK,WACLmB,MAAM,WACNC,MAAOjC,EAAE,wBACTc,QAASP,EAASD,QAAQ,CAC1ByB,SAAUnB,SAIhB,UAACsB,EAAAA,OAAYA,CAAAA,CACXC,SAAS,SACTC,OAAQrC,GAAU,EAALA,CACbsC,OAAQ3B,EACR4B,aAAc,GAAcrB,EAAaC,SAKnD,gLC3BA,MArDqB,IACjB,GAAM,QAAEmB,CAAM,CAAEC,WAoDLJ,GApDiB,QAAEE,CAAM,EAoDZ,QApDcD,CAAQ,CAAE,CAAG1E,EAC7C,GAAEuC,CAAC,CAAE,CAAGC,CAAAA,EAAAA,EAAAA,EAAAA,CAAcA,CAAC,UAEvBrB,EAAc,KAChB0D,GAAa,EACjB,EAEMC,EAAwB,UAC1B,IAAIC,GACa,UAAU,CAAvBL,EACW,MAAMM,EAAAA,CAAUA,CAACC,MAAM,CAAC,GAAeN,MAAAA,CAAZD,EAAS,KAAU,OAAPC,IAEvC,MAAMK,EAAAA,CAAUA,CAACE,IAAI,CAAC,GAAY,OAATR,GAAY,CAAES,KAAMR,CAAO,MAI/DE,GAAa,GACbO,IAAAA,IAAW,CAAC,SAEpB,EAEA,MACI,WAAC1B,EAAAA,CAAKA,CAAAA,CAAClD,KAAMoE,EAAQjB,OAAQxC,YACzB,UAAC+C,MAAAA,CAAItE,UAAU,2BACX,UAACyF,EAAAA,CAAeA,CAAAA,CACZC,KAAMC,EAAAA,GAAqBA,CAC3B1B,KAAK,KACL2B,MAAM,YACNC,MAAO,CACHC,WAAY,UACZC,QAAS,OACTC,aAAc,MACdC,MAAO,QACPC,OAAQ,OACZ,MAGR,UAACpC,EAAAA,CAAKA,CAACO,IAAI,WAAC,UAAC8B,IAAAA,UAAGxD,EAAE,oDAClB,WAACmB,EAAAA,CAAKA,CAACsC,MAAM,YACT,UAACC,EAAAA,CAAMA,CAAAA,CAACrF,QAAQ,YAAYgB,QAAST,WACpCoB,EAAE,QAEH,UAAC0D,EAAAA,CAAMA,CAAAA,CAACrF,QAAQ,SAASgB,QAASkD,WACjCvC,EAAE,sBAKnB,mBC9DA,4CACA,qBACA,WACA,OAAe,EAAQ,KAA0C,CACjE,EACA,SAFsB", "sources": ["webpack://_N_E/./node_modules/react-bootstrap/esm/AlertHeading.js", "webpack://_N_E/./node_modules/react-bootstrap/esm/AlertLink.js", "webpack://_N_E/./node_modules/react-bootstrap/esm/Alert.js", "webpack://_N_E/./pages/profile/myConsent.tsx", "webpack://_N_E/./pages/profile/confirmation.tsx", "webpack://_N_E/?039b"], "sourcesContent": ["\"use client\";\n\nimport * as React from 'react';\nimport classNames from 'classnames';\nimport { useBootstrapPrefix } from './ThemeProvider';\nimport divWithClassName from './divWithClassName';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst DivStyledAsH4 = divWithClassName('h4');\nDivStyledAsH4.displayName = 'DivStyledAsH4';\nconst AlertHeading = /*#__PURE__*/React.forwardRef(({\n  className,\n  bsPrefix,\n  as: Component = DivStyledAsH4,\n  ...props\n}, ref) => {\n  bsPrefix = useBootstrapPrefix(bsPrefix, 'alert-heading');\n  return /*#__PURE__*/_jsx(Component, {\n    ref: ref,\n    className: classNames(className, bsPrefix),\n    ...props\n  });\n});\nAlertHeading.displayName = 'AlertHeading';\nexport default AlertHeading;", "\"use client\";\n\nimport * as React from 'react';\nimport classNames from 'classnames';\nimport Anchor from '@restart/ui/Anchor';\nimport { useBootstrapPrefix } from './ThemeProvider';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst AlertLink = /*#__PURE__*/React.forwardRef(({\n  className,\n  bsPrefix,\n  as: Component = Anchor,\n  ...props\n}, ref) => {\n  bsPrefix = useBootstrapPrefix(bsPrefix, 'alert-link');\n  return /*#__PURE__*/_jsx(Component, {\n    ref: ref,\n    className: classNames(className, bsPrefix),\n    ...props\n  });\n});\nAlertLink.displayName = 'AlertLink';\nexport default AlertLink;", "\"use client\";\n\nimport classNames from 'classnames';\nimport * as React from 'react';\nimport { useUncontrolled } from 'uncontrollable';\nimport useEventCallback from '@restart/hooks/useEventCallback';\nimport { useBootstrapPrefix } from './ThemeProvider';\nimport AlertHeading from './AlertHeading';\nimport AlertLink from './AlertLink';\nimport Fade from './Fade';\nimport CloseButton from './CloseButton';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nimport { jsxs as _jsxs } from \"react/jsx-runtime\";\nconst Alert = /*#__PURE__*/React.forwardRef((uncontrolledProps, ref) => {\n  const {\n    bsPrefix,\n    show = true,\n    closeLabel = 'Close alert',\n    closeVariant,\n    className,\n    children,\n    variant = 'primary',\n    onClose,\n    dismissible,\n    transition = Fade,\n    ...props\n  } = useUncontrolled(uncontrolledProps, {\n    show: 'onClose'\n  });\n  const prefix = useBootstrapPrefix(bsPrefix, 'alert');\n  const handleClose = useEventCallback(e => {\n    if (onClose) {\n      onClose(false, e);\n    }\n  });\n  const Transition = transition === true ? Fade : transition;\n  const alert = /*#__PURE__*/_jsxs(\"div\", {\n    role: \"alert\",\n    ...(!Transition ? props : undefined),\n    ref: ref,\n    className: classNames(className, prefix, variant && `${prefix}-${variant}`, dismissible && `${prefix}-dismissible`),\n    children: [dismissible && /*#__PURE__*/_jsx(CloseButton, {\n      onClick: handleClose,\n      \"aria-label\": closeLabel,\n      variant: closeVariant\n    }), children]\n  });\n  if (!Transition) return show ? alert : null;\n  return /*#__PURE__*/_jsx(Transition, {\n    unmountOnExit: true,\n    ...props,\n    ref: undefined,\n    in: show,\n    children: alert\n  });\n});\nAlert.displayName = 'Alert';\nexport default Object.assign(Alert, {\n  Link: AlertLink,\n  Heading: AlertHeading\n});", "//Import Library\r\nimport React, { useState } from \"react\";\r\nimport { Form, Modal, Alert } from \"react-bootstrap\";\r\n\r\n//Import services/components\r\nimport Confirmation from \"./confirmation\";\r\nimport { useTranslation } from 'next-i18next';\r\n\r\nconst MyConsent = ({ isOpen, manageClose, id } : { isOpen: any, manageClose: any, id: any }) => {\r\n  const { t } = useTranslation('common');\r\n  const consentIntialState = {\r\n    consent1: true,\r\n    consent2: true,\r\n    consent3: true,\r\n    consent4: true,\r\n  };\r\n  const [consents, setConsents] = useState(consentIntialState);\r\n  const [warningDialog, setWarningDialog] = useState(false);\r\n\r\n  const consentHandler = (e: React.ChangeEvent<HTMLInputElement>) => {\r\n    const { name, checked } = e.target;\r\n    setConsents((prevState) => ({ ...prevState, [name]: checked }));\r\n    setWarningDialog(!warningDialog);\r\n  };\r\n  const closeHandler = (val: any) => {\r\n    setConsents(consentIntialState);\r\n    setWarningDialog(val);\r\n  };\r\n\r\n  const modalCloseHandler = () => {\r\n    manageClose(false);\r\n  };\r\n  return (\r\n    <>\r\n      <Modal\r\n        show={isOpen}\r\n        onHide={modalCloseHandler}\r\n        size=\"xl\"\r\n        id=\"main-content\"\r\n        className=\"w-100\"\r\n      >\r\n        <Modal.Header closeButton>\r\n          <Modal.Title>{t(\"declaration.title\")}</Modal.Title>\r\n        </Modal.Header>\r\n        <Modal.Body>\r\n          <div className=\"p-3 w-100\">\r\n            <Alert variant=\"danger\">{t(\"declaration.info\")}</Alert>\r\n            <Form.Check\r\n              className=\"pb-4\"\r\n              type=\"checkbox\"\r\n              name=\"consent1\"\r\n              onChange={consentHandler}\r\n              checked={consents.consent1}\r\n              value=\"consent1\"\r\n              label={t(\"declaration.consent1\")}\r\n            />\r\n             <Form.Check\r\n              className=\"pb-4\"\r\n              name=\"consent2\"\r\n              onChange={consentHandler}\r\n              type=\"checkbox\"\r\n              checked={consents.consent2}\r\n              value=\"consent2\"\r\n              label={t(\"declaration.consent2\")}\r\n            />\r\n            <Form.Check\r\n              className=\"pb-4\"\r\n              name=\"consent3\"\r\n              onChange={consentHandler}\r\n              type=\"checkbox\"\r\n              checked={consents.consent3}\r\n              value=\"consent3\"\r\n              label={t(\"declaration.consent3\")}\r\n            />\r\n            <Form.Check\r\n              className=\"pb-4\"\r\n              type=\"checkbox\"\r\n              name=\"consent4\"\r\n              value=\"consent4\"\r\n              label={t(\"declaration.consent4\")}\r\n              checked={consents.consent4}\r\n              onChange={consentHandler}\r\n            />       \r\n          </div>\r\n        </Modal.Body>\r\n        <Confirmation\r\n          endpoint=\"/users\"\r\n          userId={id ? id : \"\"}\r\n          isopen={warningDialog}\r\n          manageDialog={(val: any) => closeHandler(val)}\r\n        />\r\n      </Modal>\r\n    </>\r\n  );\r\n};\r\n\r\nexport default MyConsent;\r\n", "//Import Library\r\nimport React from \"react\";\r\nimport { <PERSON><PERSON>, But<PERSON> } from \"react-bootstrap\";\r\nimport { FontAwesomeIcon } from \"@fortawesome/react-fontawesome\";\r\nimport {\r\n    faExclamationTriangle\r\n} from \"@fortawesome/free-solid-svg-icons\";\r\nimport Router from 'next/router';\r\n\r\n//Import services/components\r\nimport apiService from '../../services/apiService';\r\nimport { useTranslation } from 'next-i18next';\r\n\r\n\r\nconst Confirmation = (props: any) => {\r\n    const { isopen, manageDialog, userId, endpoint } = props;\r\n    const { t } = useTranslation('common');\r\n\r\n    const handleClose = () => {\r\n        manageDialog(false)\r\n    }\r\n\r\n    const accountDeleteHandlder = async () => {\r\n        let response;\r\n        if (endpoint === \"/users\") {\r\n            response = await apiService.remove(`${endpoint}/${userId}`);\r\n        } else {\r\n            response = await apiService.post(`${endpoint}`, { code: userId })\r\n        }\r\n\r\n        if (response) {\r\n            manageDialog(false);\r\n            Router.push('/home');\r\n        }\r\n    }\r\n\r\n    return (\r\n        <Modal show={isopen} onHide={handleClose}>\r\n            <div className=\"text-center p-2\">\r\n                <FontAwesomeIcon\r\n                    icon={faExclamationTriangle}\r\n                    size=\"5x\"\r\n                    color=\"indianRed\"\r\n                    style={{\r\n                        background: \"#d6deec\",\r\n                        padding: \"19px\",\r\n                        borderRadius: \"50%\",\r\n                        width: \"100px\",\r\n                        height: \"100px\"\r\n                    }}\r\n                />\r\n            </div>\r\n            <Modal.Body><b>{t(\"AreyousureyouwishtoleavetheKnowledgePlatform\")}</b></Modal.Body>\r\n            <Modal.Footer>\r\n                <Button variant=\"secondary\" onClick={handleClose}>\r\n                {t(\"No\")}\r\n          </Button>\r\n                <Button variant=\"danger\" onClick={accountDeleteHandlder}>\r\n                {t(\"YesDeleteMe\")}\r\n          </Button>\r\n            </Modal.Footer>\r\n        </Modal>\r\n    )\r\n}\r\n\r\n\r\n\r\nexport default Confirmation;", "\n    (window.__NEXT_P = window.__NEXT_P || []).push([\n      \"/profile/myConsent\",\n      function () {\n        return require(\"private-next-pages/profile/myConsent.tsx\");\n      }\n    ]);\n    if(module.hot) {\n      module.hot.dispose(function () {\n        window.__NEXT_P.push([\"/profile/myConsent\"])\n      });\n    }\n  "], "names": ["DivStyledAsH4", "divWithClassName", "displayName", "AlertHeading", "React", "ref", "className", "bsPrefix", "as", "Component", "props", "useBootstrapPrefix", "_jsx", "classNames", "AlertLink", "<PERSON><PERSON>", "<PERSON><PERSON>", "uncontrolledProps", "show", "<PERSON><PERSON><PERSON><PERSON>", "closeVariant", "children", "variant", "onClose", "dismissible", "transition", "Fade", "useUncontrolled", "prefix", "handleClose", "useEventCallback", "e", "Transition", "alert", "_jsxs", "role", "undefined", "CloseButton", "onClick", "unmountOnExit", "in", "Object", "assign", "Link", "Heading", "isOpen", "MyConsent", "manageClose", "id", "t", "useTranslation", "consentIntialState", "consent1", "consent2", "consent3", "consent4", "consents", "setConsents", "useState", "warningDialog", "setWarningDialog", "<PERSON><PERSON><PERSON><PERSON>", "name", "checked", "target", "prevState", "<PERSON><PERSON><PERSON><PERSON>", "val", "Modal", "onHide", "modalCloseHandler", "size", "Header", "closeButton", "Title", "Body", "div", "Form", "Check", "type", "onChange", "value", "label", "Confirmation", "endpoint", "userId", "isopen", "manageDialog", "accountDeleteHandlder", "response", "apiService", "remove", "post", "code", "Router", "FontAwesomeIcon", "icon", "faExclamationTriangle", "color", "style", "background", "padding", "borderRadius", "width", "height", "b", "Footer", "<PERSON><PERSON>"], "sourceRoot": "", "ignoreList": [0, 1, 2]}