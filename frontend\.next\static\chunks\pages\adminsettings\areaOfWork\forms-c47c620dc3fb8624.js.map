{"version": 3, "file": "static/chunks/pages/adminsettings/areaOfWork/forms-c47c620dc3fb8624.js", "mappings": "sTAuJA,MApIwBA,IACpB,GAAM,CAAEC,GAAC,CAAE,CAAGC,CAAAA,EAAAA,EAAAA,EAAAA,CAAcA,CAAC,MAmIlBC,IAjILC,EAAqB,CACvBC,IAAK,GACLC,EA+HsB,IA/Hf,EACX,EAEM,CAACC,EAAYC,EAAc,CAAGC,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAaL,GAEnDM,EAAoB,CAAC,CAAEV,CAAAA,EAAMW,MAAM,EAAwB,sBAApBX,EAAMW,MAAM,CAAC,EAAE,EAA4BX,EAAMW,MAAM,CAAC,IAE/FC,EAAUC,CAAAA,EAAAA,EAAAA,MAAAA,CAAMA,CAAM,MAkBtBC,EAAe,MAAOC,QAMpBC,EACAC,EANJF,EAAMG,cAAc,GACpB,IAAMC,EAAM,CACRb,MAAOC,EAAWD,KAAK,CAACc,IAAI,EAChC,EAIIV,GACAO,EAAW,KADD,6BAEVD,EAAW,MAAMK,EAAAA,CAAUA,CAACC,KAAK,CAAC,eAA+B,OAAhBtB,EAAMW,MAAM,CAAC,EAAE,EAAIQ,KAEpEF,EAAW,gCACXD,EAAW,MAAMK,EAAAA,CAAUA,CAACE,IAAI,CAAC,cAAeJ,IAEhDH,GAAYA,EAASX,GAAG,EAAE,EAC1BmB,EAAKA,CAACC,OAAO,CAACxB,EAAEgB,IAChBS,IAAAA,IAAW,CAAC,gCAERV,OAAAA,EAAAA,KAAAA,EAAAA,EAAUW,SAAAA,CAAVX,GAAwB,KACxBQ,EAAAA,EAAKA,CAACI,KAAK,CAAC3B,EAAE,yBAEduB,EAAAA,EAAKA,CAACI,KAAK,CAACZ,EAGxB,EAiBA,MAfAa,CAAAA,EAAAA,EAAAA,SAAAA,CAASA,CAAC,KACN,IAAMC,EAAmB,CACrBC,MAAO,CAAC,EACRC,KAAM,CAAE1B,MAAO,KAAM,EACrB2B,MAAO,GACX,EACIvB,GAKAwB,CAJ0B,MADhB,IAEN,IAAMlB,EAAuB,MAAMK,EAAAA,CAAUA,CAACc,GAAG,CAAC,eAA+B,OAAhBnC,EAAMW,MAAM,CAAC,EAAE,EAAImB,GACpFtB,EAAc,GAAgB,EAAE,GAAG4B,CAAS,CAAE,EAAhB,CAAmBpB,CAAQ,IAC7D,GAGR,EAAG,EAAE,EAGD,UAACqB,MAAAA,UACG,UAACC,EAAAA,CAASA,CAAAA,CAACC,UAAU,WAAWC,KAAK,aACjC,UAACC,EAAAA,CAAIA,CAAAA,CACDC,MAAO,CACHC,UAAW,MACXC,UAAW,kEACf,WAEA,UAACC,EAAAA,CAAqBA,CAAAA,CAACC,SAAUhC,EAAciC,IAAKnC,EAASoC,cAAezC,EAAY0C,oBAAoB,WACxG,WAACR,EAAAA,CAAIA,CAACS,IAAI,YACN,UAACC,EAAAA,CAAGA,CAAAA,UACA,UAACC,EAAAA,CAAGA,CAAAA,UACA,UAACX,EAAAA,CAAIA,CAACY,KAAK,WAAEpD,EAAE,kDAGvB,UAACqD,KAAAA,CAAAA,GACD,UAACH,EAAAA,CAAGA,CAAAA,UACA,UAACC,EAAAA,CAAGA,CAAAA,CAACG,EAAE,IAACC,GAAI,EAAGC,GAAI,YACf,WAACC,EAAAA,CAAIA,CAACC,KAAK,YACP,UAACD,EAAAA,CAAIA,CAACE,KAAK,EAACrB,UAAU,0BACjBtC,EAAE,8CAEP,UAAC4D,EAAAA,EAASA,CAAAA,CACNC,KAAK,QACLC,GAAG,QACHC,QAAQ,IACRC,MAAO1D,EAAWD,KAAK,CACvB4D,UAAYD,GAA8C,KAA/BE,OAAOF,GAAS,IAAI7C,IAAI,GACnDgD,aAAc,CACVF,UAAWjE,EAAE,uDACjB,EACAoE,SApFnB,CAoF6BC,GAnF9C,GAAIC,EAAEC,MAAM,CAAE,CACV,GAAM,MAAEV,CAAI,OAAEG,CAAK,CAAE,CAAGM,EAAEC,MAAM,CAChChE,EAAc,GAAgB,EAC1B,GAAG4B,CAAS,CACZ,CAAC0B,CAFyB,CAEpB,CAAEG,EACZ,EACJ,CACJ,WAiFwB,UAACd,EAAAA,CAAGA,CAAAA,CAACZ,UAAU,gBACX,WAACa,EAAAA,CAAGA,CAAAA,WACA,UAACqB,EAAAA,CAAMA,CAAAA,CAAClC,UAAU,OAAOmC,KAAK,SAASC,QAAQ,mBAC1C1E,EAAE,0CAEP,UAACwE,EAAAA,CAAMA,CAAAA,CAAClC,UAAU,OAAOqC,QApGpC,CAoG6CC,IAnG9DrE,EAAcJ,GAEd0E,OAAOC,QAAQ,CAAC,EAAG,EACvB,EAgGgFJ,QAAQ,gBACnD1E,EAAE,yCAEP,UAAC+E,IAAIA,CACDC,KAAK,6BACLC,GAAK,OAFJF,gCAID,UAACP,EAAAA,CAAMA,CAAAA,CAACE,QAAQ,qBAAa1E,EAAE,2DAUvE,0GChJA,IAAMkF,EAAwBC,EAAAA,OAAb,GAA6B,CAAC,GAK5CrC,IALyB,IAAoB,WAC9CR,CAAS,UACT8C,CAAQ,CACRH,GAAII,EAAY,KAAK,CACrB,GAAGtF,EACJ,GAEC,OADAqF,EAAWE,CAAAA,EAAAA,EAAAA,EAAAA,CAAkBA,CAACF,EAAU,aACpBG,CAAAA,EAAAA,EAAAA,GAAAA,CAAIA,CAACF,EAAW,CAClCvC,IAAKA,EACLR,UAAWkD,IAAWlD,EAAW8C,GACjC,GAAGrF,CAAK,EAEZ,GACAmF,EAASO,WAAW,CAAG,WCbvB,IAAMC,EAA0BP,EAAAA,SAAb,CAA6B,CAAC,GAK9CrC,MAL2B,EAAoB,WAChDR,CAAS,UACT8C,CAAQ,CACRH,GAAII,EAAY,KAAK,CACrB,GAAGtF,EACJ,GAEC,OADAqF,EAAWE,CAAAA,EAAAA,EAAAA,EAAAA,CAAkBA,CAACF,EAAU,eACpBG,CAAAA,EAAAA,EAAAA,GAAAA,CAAIA,CAACF,EAAW,CAClCvC,IAAKA,EACLR,UAAWkD,IAAWlD,EAAW8C,GACjC,GAAGrF,CAAK,EAEZ,GACA2F,EAAWD,WAAW,CAAG,4BCXzB,IAAME,EAA0BR,EAAAA,SAAb,CAA6B,CAAC,GAM9CrC,MAN2B,EAAoB,UAChDsC,CAAQ,WACR9C,CAAS,CAET2C,CADA,EACII,EAAY,KAAK,CACrB,GAAGtF,EACJ,GACO6F,EAASN,CAAAA,EAAAA,EAAAA,EAAAA,CAAkBA,CAACF,EAAU,eACtCS,EAAeC,CAAAA,EAAAA,EAAAA,OAAAA,CAAOA,CAAC,IAAO,EAClCC,mBAAoBH,EACtB,EAAI,CAACA,EAAO,EACZ,MAAoBL,CAAAA,EAAAA,EAAAA,GAAAA,CAAIA,CAACS,EAAAA,CAAiBA,CAACC,QAAQ,CAAE,CACnDjC,MAAO6B,EACPK,SAAuBX,CAAAA,EAAAA,EAAAA,GAAAA,CAAIA,CAACF,EAAW,CACrCvC,IAAKA,EACL,GAAG/C,CAAK,CACRuC,UAAWkD,IAAWlD,EAAWsD,EACnC,EACF,EACF,GACAD,EAAWF,GAJgBD,QAIL,CAAG,aCtBzB,IAAMW,EAAuBhB,EAAAA,MAAb,IAA6B,CAC7C,CACA,EAMGrC,GARwB,KAE1B,UACCsC,CAAQ,WACR9C,CAAS,SACToC,CAAO,CACPO,GAAII,EAAY,KAAK,CACrB,GAAGtF,EACJ,GACO6F,EAASN,CAAAA,EAAAA,EAAAA,EAAAA,CAAkBA,CAACF,EAAU,YAC5C,MAAoBG,CAAb,EAAaA,EAAAA,GAAAA,CAAIA,CAACF,EAAW,CAClCvC,IAAKA,EACLR,UAAWkD,IAAWd,EAAU,GAAaA,MAAAA,CAAVkB,EAAO,EAArBJ,GAAgC,OAARd,CAX0G,EAW9FkB,EAAQtD,GACjE,GAAGvC,CAAK,EAEZ,GACAoG,EAAQV,WAAW,CAAG,UChBtB,IAAMW,EAA8BjB,EAAAA,UAAgB,CAAC,EAA9B,CAKpBrC,QALmD,EAApB,SAChCR,CAAS,UACT8C,CAAQ,CACRH,GAAII,EAAY,KAAK,CACrB,GAAGtF,EACJ,GAEC,OADAqF,EAAWE,CAAAA,EAAAA,EAAAA,EAAAA,CAAkBA,CAACF,EAAU,oBACpBG,CAAAA,EAAAA,EAAAA,GAAAA,CAAIA,CAACF,EAAW,CAClCvC,IAAKA,EACLR,UAAWkD,IAAWlD,EAAW8C,GACjC,GAAGrF,CAAK,EAEZ,GACAqG,EAAeX,WAAW,CAAG,iBCb7B,IAAMY,EAAwBlB,EAAAA,OAAb,GAA6B,CAAC,GAK5CrC,IALyB,IAAoB,WAC9CR,CAAS,CACT8C,UAAQ,CACRH,GAAII,EAAY,GAAG,CACnB,GAAGtF,EACJ,GAEC,OADAqF,EAAWE,CAAAA,EAAAA,EAAAA,EAAAA,CAAkBA,CAACF,EAAU,aACpBG,CAAAA,EAAAA,EAAAA,GAAAA,CAAIA,CAACF,EAAW,CAClCvC,IAAKA,EACLR,UAAWkD,IAAWlD,EAAW8C,GACjC,GAAGrF,CAAK,EAEZ,GACAsG,EAJyBb,WAIL,CAAG,0BCZvB,IAAMc,EAAgBC,CAAAA,EAAAA,EAAAA,CAAAA,CAAgBA,CAAC,MACjCC,EAA4BrB,EAAAA,UAAgB,CAA7B,CAA8B,EAKhDrC,QAL6B,WAC9BR,CAAS,UACT8C,CAAQ,CACRH,GAAII,EAAYiB,CAAa,CAC7B,GAAGvG,EACJ,GAEC,OADAqF,EAAWE,CAAAA,EAAAA,EAAAA,EAAAA,CAAkBA,CAACF,EAAU,iBACpBG,CAAAA,EAAAA,EAAAA,GAAAA,CAAIA,CAACF,EAAW,CAClCvC,IAAKA,EACLR,UAAWkD,IAAWlD,EAAW8C,GACjC,GAAGrF,CAAK,EAEZ,GACAyG,EAJyBhB,WAID,CAAG,eCf3B,IAAMiB,EAAwBtB,EAAAA,OAAb,GAA6B,CAAC,GAK5CrC,IALyB,IAAoB,WAC9CR,CAAS,UACT8C,CAAQ,CACRH,GAAII,EAAY,GAAG,CACnB,GAAGtF,EACJ,GAEC,OADAqF,EAAWE,CAAAA,EAAAA,EAAAA,EAAAA,CAAkBA,CAACF,EAAU,aACpBG,CAAAA,EAAAA,EAAAA,GAAAA,CAAIA,CAACF,EAAW,CAClCvC,IAAKA,EACLR,UAAWkD,IAAWlD,EAAW8C,GACjC,GAAGrF,CAAK,EAEZ,GACA0G,EAJyBjB,WAIL,CAAG,WCZvB,IAAMkB,EAAgBH,CAAAA,EAAAA,EAAAA,CAAAA,CAAgBA,CAAC,MACjCI,EAAyBxB,EAAAA,QAAb,EAA6B,CAAC,GAK7CrC,KAL0B,GAAoB,WAC/CR,CAAS,UACT8C,CAAQ,CACRH,GAAII,EAAYqB,CAAa,CAC7B,GAAG3G,EACJ,GAEC,OADAqF,EAAWE,CAAAA,EAAAA,EAAAA,EAAAA,CAAkBA,CAACF,EAAU,cACpBG,CAAAA,EAAAA,EAAAA,GAAAA,CAAIA,CAACF,EAAW,CAClCvC,IAAKA,EACLR,UAAWkD,IAAWlD,EAAW8C,GACjC,GAAGrF,CAAK,EAEZ,EACA4G,GAJyBnB,WAIJ,CAAG,YCNxB,IAAMhD,EAAoB2C,EAAAA,GAAb,OAA6B,CAAC,GAWxCrC,QAXyC,UAC1CsC,CAAQ,WACR9C,CAAS,IACTsE,CAAE,MACFC,CAAI,QACJC,CAAM,MACNC,GAAO,CAAK,UACZb,CAAQ,CAERjB,CADA,EACII,EAAY,KAAK,CACrB,GAAGtF,EACJ,GACO6F,EAASN,CAAAA,EAAAA,EAAAA,EAAAA,CAAkBA,CAACF,EAAU,QAC5C,MAAoBG,CAAb,EAAaA,EAAAA,GAAAA,CAAIA,CAACF,EAAP,CAChBvC,IAAKA,EACL,GAAG/C,CAAK,CACRuC,UAAWkD,IAAWlD,EAAWsD,EAAQgB,GAAM,MAAS,GAAnCpB,GAAmC,CAAHoB,GAAMC,GAAQ,QAAa,OAALA,GAAQC,GAAU,UAAiB,OAAPA,IACvGZ,IATyJ,KAS/Ia,EAAoBxB,CAAAA,EAAAA,EAAb,GAAaA,CAAIA,CTZvBL,ESYkC,CAC3CgB,GAD0B,ETZThB,EAAC,ESYuBA,CAE3C,GAAKgB,CACP,EACF,GACA1D,EAAKiD,WAAW,CAAG,OACnB,MAAeuB,OAAOC,MAAM,CAACzE,EAAM,CACjC0E,INhBaf,CMgBRA,CACL/C,KNjBoB+C,CKDPQ,CLCQ,CMkBrBQ,EAFYhB,KDjBUQ,EAAC,CCmBbH,CACVvD,CAFgB0D,KAEVzB,CACNH,GHrByByB,EAAC,CGqBpBH,CACNe,CAHsBZ,GACRtB,CFtBDuB,CEwBPA,CACNY,CJzBsB,GIuBRhB,EFvBOI,CLSRd,CKTS,CE0BtB2B,EAFcb,KRxBDf,CCSUC,COkBvB4B,CPlBwB,GOgBN5B,IRzBKD,EAAC,CGAXU,CK2BDA,CADMV,CAElB,EAAC,SL5B0BU,EAAC,GK2BFA,0ICwCrB,IAAMoB,EAAQ,CACnBC,WA1C4C,OAAC,MAC7C5D,CAAI,eACJ6D,CAAa,CACbtD,UAAQ,cACRD,CAAY,UACZ+B,CAAQ,CACT,GACO,QAAEyB,CAAM,SAAEC,CAAO,CAAE,CAAGC,CAAAA,EAAAA,EAAAA,EAAAA,CAAgBA,GACtCC,EAAWF,CAAO,CAAC/D,EAAK,EAAI8D,CAAM,CAAC9D,EAAK,CAGzBsB,EAAAA,OAAa,CAAC,IAAO,OAAEtB,EAAK,EAAI,CAACA,EAAK,EAG3D,IAAMkE,EAAoB5C,EAAAA,QAAc,CAAC6C,GAAG,CAAC9B,EAAU,GACrD,EAAIf,cAAoB,CAAC8C,IAxC7B,IAwCqC,KAxC5BC,CAAmB,EAC1B,MAAwB,UAAjB,OAAOnI,GAAsBA,QACtC,EAwCmBkI,EAAMlI,KAAK,EACfoF,CADkB,CAClBA,YAAkB,CAAC8C,EAA6C,MACrEpE,EACA,GAAGoE,EAAMlI,KAAK,GAIbkI,GAGT,MACE,WAAC7F,MAAAA,WACC,UAACA,MAAAA,CAAIE,UAAU,uBACZyF,IAEFD,GACC,UAAC1F,MAAAA,CAAIE,UAAU,oCACZ6B,GAAiB,kBAAOwD,CAAM,CAAC9D,EAAK,CAAgB8D,CAAM,CAAC9D,EAAK,CAAGK,OAAOyD,CAAM,CAAC9D,GAAK,MAKjG,EAIEsE,UAhE0C,OAAC,IAAErE,CAAE,OAAEsE,CAAK,OAAEpE,CAAK,MAAEH,CAAI,CAAEwE,UAAQ,CAAE,GACzE,QAAEC,CAAM,eAAEC,CAAa,CAAE,CAAGV,CAAAA,EAAAA,EAAAA,EAAAA,CAAgBA,GAC5CW,EAAY3E,GAAQC,EAE1B,MACE,UAACL,EAAAA,CAAIA,CAACgF,KAAK,EACThE,KAAK,QACLX,GAAIA,EACJsE,MAAOA,EACPpE,MAAOA,EACPH,KAAM2E,EACNE,QAASJ,CAAM,CAACE,EAAU,GAAKxE,EAC/BI,SAAU,IACRmE,EAAcC,EAAWlE,EAAEC,MAAM,CAACP,KAAK,CACzC,EACAqE,SAAUA,EACVM,MAAM,KAGZ,CA8CA,CCzEgBC,CDyEd,ECzEcA,CAAAA,CACLhF,EAAAA,EAAAA,CACEiF,EAAAA,EAAAA,gGCeb,IAAMjG,EAAwBkG,CAAAA,EAAAA,EAAAA,UAAAA,CAAUA,CAA8C,CAAC/I,EAAO+C,KAC5F,GAAM,UAAEoD,CAAQ,UAAErD,CAAQ,cAAEkG,CAAY,CAAEzG,WAAS,YAAE0G,CAAU,eAAEjG,CAAa,CAAE,GAAGkG,EAAM,CAAGlJ,EAGtFmJ,EAAmBC,EAAAA,EAAU,GAAGC,KAAK,CAAC,CAAC,GAE7C,MACE,UAACC,EAAAA,EAAMA,CAAAA,CACLtG,cAAeA,GAAiB,CAAC,EACjCmG,iBAAkBA,EAClBrG,SAAU,CAACyF,EAA6BgB,KAEtC,IAAMC,EAAuB,CAC3BtI,eAAgB,KAAO,EACvBuI,gBAAiB,KAAO,EACxBC,cAAe,KACflF,OAAQ,KACRmF,YAAa,IAAIC,MAAM,UACvBC,SAAS,EACTC,YAAY,EACZC,iBAAkB,GAClBC,WAAY,EACZC,WAAW,EACXC,UAAWC,KAAKC,GAAG,GACnB1F,KAAM,SACN2F,mBAAoB,IAAM,GAC1BC,qBAAsB,KAAM,EAC5BC,QAAS,KAAO,CAClB,EAEIzH,GAEFA,EAAS0G,EAAWjB,EAAQgB,CAFhB,CAIhB,EACC,GAAGL,CAAI,UAEP,GACC,UAACxF,EAAAA,EAAIA,CAAAA,CACHX,IAAKA,EACLD,SAAU0H,EAAY1J,YAAY,CAClCkI,aAAcA,EACdzG,UAAWA,EACX0G,WAAYA,WAES,YAApB,OAAO9C,EAA0BA,EAASqE,GAAerE,KAKpE,GAEAtD,EAAsB6C,WAAW,CAAG,wBAEpC,MAAe7C,qBAAqBA,EAAC,sFClF9B,IAAMgB,EAAY,OAAC,MACxBC,CAAI,IACJC,CAAE,UACFC,CAAQ,WACRE,CAAS,cACTE,CAAY,UACZC,CAAQ,OACRJ,CAAK,IACLiB,CAAE,WACFuF,CAAS,MACTC,CAAI,SACJC,CAAO,CACP,GAAG3K,EACC,GAuBJ,MACE,UAAC4K,EAAAA,EAAKA,CAAAA,CAAC9G,KAAMA,EAAM+G,SAtBJ,CAsBcA,GApB7B,IAAMC,EAA2B,UAAf,OAAOC,EAAmBA,EAAM5G,OAAO4G,GAAO,WAChE,GAAiB,EAACA,GAA4B,IAA7B,CAAQD,EAAU1J,IAAI,EAAO,CAAC,CACtCgD,EAD0C,KAC1CA,EAAAA,KAAAA,EAAAA,EAAcF,SAAAA,GAAa,EAA3BE,uBAGLF,GAAa,CAACA,EAAU6G,GACnB3G,CAAAA,EADyB,MACzBA,KAAAA,EAAAA,EAAcF,QAAdE,CAAuB,GAAI,gBAGhCuG,GAAWI,GAET,CADU,CADI,GACAC,OAAOL,GACdM,IAAI,CAACF,GACP3G,CAAAA,EADa,MACbA,KAAAA,EAAAA,EAAcuG,OAAAA,CAAdvG,EAAyB,uBAKtC,WAIK,OAAC,OAAE8G,CAAK,MAAEC,CAAI,CAAO,SACpB,iCACE,UAACzH,EAAAA,CAAIA,CAAC0H,OAAO,EACV,GAAGF,CAAK,CACR,GAAGlL,CAAK,CACT+D,GAAIA,EACJmB,GAAIA,GAAM,QACVwF,KAAMA,EACNW,UAAWF,EAAKtD,OAAO,EAAI,CAAC,CAACsD,EAAKvJ,KAAK,CACvCyC,SAAU,IACR6G,EAAM7G,QAAQ,CAACE,GACXF,GAAUA,EAASE,EACzB,EACAN,WAAiBqH,IAAVrH,EAAsBA,EAAQiH,EAAMjH,KAAK,GAEjDkH,EAAKtD,OAAO,EAAIsD,EAAKvJ,KAAK,CACzB,UAAC8B,EAAAA,CAAIA,CAAC0H,OAAO,CAACG,QAAQ,EAAC7G,KAAK,mBACzByG,EAAKvJ,KAAK,GAEX,UAKd,EAAE,EAIyB,OAAC,MAC1BkC,CAAI,IACJC,CAAE,UACFC,CAAQ,cACRI,CAAY,UACZC,CAAQ,OACRJ,CAAK,UACLkC,CAAQ,CACR,GAAGnG,EACC,GAUJ,MACE,UAAC4K,EAAAA,EAAKA,CAAAA,CAAC9G,KAAMA,EAAM+G,SATJ,CAScA,GAR7B,GAAI7G,GAAa,EAAC+G,GAAe,IAAhB,CAAQA,CAAQ,CAAC,CAChC,EADoC,IAC7B3G,OAAAA,EAAAA,KAAAA,EAAAA,EAAcF,SAAAA,GAAa,EAA3BE,sBAIX,WAIK,OAAC,OAAE8G,CAAK,MAAEC,CAAI,CAAO,SACpB,iCACE,UAACzH,EAAAA,CAAIA,CAAC0H,OAAO,EACXlG,GAAG,SACF,GAAGgG,CAAK,CACR,GAAGlL,CAAK,CACT+D,GAAIA,EACJsH,UAAWF,EAAKtD,OAAO,EAAI,CAAC,CAACsD,EAAKvJ,KAAK,CACvCyC,SAAU,IACR6G,EAAM7G,QAAQ,CAACE,GACXF,GAAUA,EAASE,EACzB,EACAN,WAAiBqH,IAAVrH,EAAsBA,EAAQiH,EAAMjH,KAAK,UAE/CkC,IAEFgF,EAAKtD,OAAO,EAAIsD,EAAKvJ,KAAK,CACzB,UAAC8B,EAAAA,CAAIA,CAAC0H,OAAO,CAACG,QAAQ,EAAC7G,KAAK,mBACzByG,EAAKvJ,KAAK,GAEX,UAKd,EAAE,iBCrHF,4CACA,kCACA,WACA,OAAe,EAAQ,KAAuD,CAC9E,EACA,SAFsB,wCCDtB,IAAM4J,EAAuBpG,QAAb,CAAaA,UAAF,GAAqB,CAAC,MACjDoG,EAAQ9F,WAAW,CAAG,oBACtB,MAAe8F,OAAOA,EAAC", "sources": ["webpack://_N_E/./pages/adminsettings/areaOfWork/forms.tsx", "webpack://_N_E/./node_modules/react-bootstrap/esm/CardBody.js", "webpack://_N_E/./node_modules/react-bootstrap/esm/CardFooter.js", "webpack://_N_E/./node_modules/react-bootstrap/esm/CardHeader.js", "webpack://_N_E/./node_modules/react-bootstrap/esm/CardImg.js", "webpack://_N_E/./node_modules/react-bootstrap/esm/CardImgOverlay.js", "webpack://_N_E/./node_modules/react-bootstrap/esm/CardLink.js", "webpack://_N_E/./node_modules/react-bootstrap/esm/CardSubtitle.js", "webpack://_N_E/./node_modules/react-bootstrap/esm/CardText.js", "webpack://_N_E/./node_modules/react-bootstrap/esm/CardTitle.js", "webpack://_N_E/./node_modules/react-bootstrap/esm/Card.js", "webpack://_N_E/./components/common/FormikRadio.tsx", "webpack://_N_E/./components/common/FormValidation.tsx", "webpack://_N_E/./components/common/ValidationFormWrapper.tsx", "webpack://_N_E/./components/common/FormikTextInput.tsx", "webpack://_N_E/?3be2", "webpack://_N_E/./node_modules/react-bootstrap/esm/CardHeaderContext.js"], "sourcesContent": ["//Import Library\r\nimport { useState, useRef, useEffect } from \"react\";\r\nimport { Container, Card, Row, Col, Form, Button } from \"react-bootstrap\";\r\n// import { ValidationForm, TextInput } from \"../../../components/common/FormValidation\";\r\nimport ValidationFormWrapper from \"../../../components/common/ValidationFormWrapper\";\r\nimport { TextInput, SelectGroup } from \"../../../components/common/FormValidation\";\r\nimport Router from \"next/router\";\r\nimport toast from 'react-hot-toast';\r\nimport Link from \"next/link\";\r\n\r\n//Import services/components\r\nimport { AreaOfWork } from \"../../../types\";\r\nimport apiService from \"../../../services/apiService\";\r\nimport { useTranslation } from 'next-i18next';\r\n\r\ninterface AreaOfWorkFormProps {\r\n    routes: string[];\r\n}\r\n\r\nconst AreaOfWorkForm = (props: AreaOfWorkFormProps) => {\r\n    const { t } = useTranslation('common');\r\n\r\n    const _initialareaOfWork = {\r\n        _id: \"\",\r\n        title: \"\",\r\n    };\r\n\r\n    const [initialVal, setInitialVal] = useState<AreaOfWork>(_initialareaOfWork);\r\n\r\n    const editform: boolean = !!(props.routes && props.routes[0] === \"edit_area_of_work\" && props.routes[1]);\r\n\r\n    const formRef = useRef<any>(null);\r\n\r\n    const resetHandler = () => {\r\n        setInitialVal(_initialareaOfWork);\r\n        // Reset validation state (Formik handles this automatically)\r\n        window.scrollTo(0, 0);\r\n    };\r\n\r\n    const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {\r\n        if (e.target) {\r\n            const { name, value } = e.target;\r\n            setInitialVal((prevState) => ({\r\n                ...prevState,\r\n                [name]: value,\r\n            }));\r\n        }\r\n    };\r\n\r\n    const handleSubmit = async (event: any) => {\r\n        event.preventDefault();\r\n        const obj = {\r\n            title: initialVal.title.trim(),\r\n        };\r\n\r\n        let response;\r\n        let toastMsg;\r\n        if (editform) {\r\n            toastMsg = \"Areaofworkisupdatedsuccessfully\";\r\n            response = await apiService.patch(`/areaofwork/${props.routes[1]}`, obj);\r\n        } else {\r\n            toastMsg = \"Areaofworkisaddedsuccessfully\";\r\n            response = await apiService.post(\"/areaofwork\", obj);\r\n        }\r\n        if (response && response._id) {\r\n            toast.success(t(toastMsg));\r\n            Router.push(\"/adminsettings/area_of_work\");\r\n        } else {\r\n            if (response?.errorCode === 11000) {\r\n                toast.error(t(\"duplicatesNotAllowed\"));\r\n            } else {\r\n                toast.error(response);\r\n            }\r\n        }\r\n    };\r\n\r\n    useEffect(() => {\r\n        const areaOfWorkParams = {\r\n            query: {},\r\n            sort: { title: \"asc\" },\r\n            limit: \"~\",\r\n        };\r\n        if (editform) {\r\n            const getAreaOfWorkData = async () => {\r\n                const response: AreaOfWork = await apiService.get(`/areaofwork/${props.routes[1]}`, areaOfWorkParams);\r\n                setInitialVal((prevState) => ({ ...prevState, ...response }));\r\n            };\r\n            getAreaOfWorkData();\r\n        }\r\n    }, []);\r\n\r\n    return (\r\n        <div>\r\n            <Container className=\"formCard\" fluid>\r\n                <Card\r\n                    style={{\r\n                        marginTop: \"5px\",\r\n                        boxShadow: \"0 4px 8px 0 rgba(0, 0, 0, 0.2), 0 6px 20px 0 rgba(0, 0, 0, 0.19)\",\r\n                    }}\r\n                >\r\n                    <ValidationFormWrapper onSubmit={handleSubmit} ref={formRef} initialValues={initialVal} enableReinitialize={true}>\r\n                        <Card.Body>\r\n                            <Row>\r\n                                <Col>\r\n                                    <Card.Title>{t(\"adminsetting.areaofwork.Forms.AreaOfWork\")}</Card.Title>\r\n                                </Col>\r\n                            </Row>\r\n                            <hr />\r\n                            <Row>\r\n                                <Col md lg={6} sm={12}>\r\n                                    <Form.Group>\r\n                                        <Form.Label className=\"required-field\">\r\n                                            {t(\"adminsetting.areaofwork.Forms.AreaOfWork\")}\r\n                                        </Form.Label>\r\n                                        <TextInput\r\n                                            name=\"title\"\r\n                                            id=\"title\"\r\n                                            required\r\n                                            value={initialVal.title}\r\n                                            validator={(value: any) => String(value || '').trim() !== \"\"}\r\n                                            errorMessage={{\r\n                                                validator: t(\"adminsetting.areaofwork.Forms.PleaseAddtheAreaofWork\"),\r\n                                            }}\r\n                                            onChange={handleChange}\r\n                                        />\r\n                                    </Form.Group>\r\n                                </Col>\r\n                            </Row>\r\n                            <Row className=\"my-4\">\r\n                                <Col>\r\n                                    <Button className=\"me-2\" type=\"submit\" variant=\"primary\">\r\n                                        {t(\"adminsetting.areaofwork.Forms.Submit\")}\r\n                                    </Button>\r\n                                    <Button className=\"me-2\" onClick={resetHandler} variant=\"info\">\r\n                                        {t(\"adminsetting.areaofwork.Forms.Reset\")}\r\n                                    </Button>\r\n                                    <Link\r\n                                        href=\"/adminsettings/[...routes]\"\r\n                                        as={`/adminsettings/area_of_work`}\r\n                                        >\r\n                                        <Button variant=\"secondary\">{t(\"adminsetting.areaofwork.Forms.Cancel\")}</Button>\r\n                                    </Link>\r\n                                </Col>\r\n                            </Row>\r\n                        </Card.Body>\r\n                    </ValidationFormWrapper>\r\n                </Card>\r\n            </Container>\r\n        </div>\r\n    );\r\n};\r\nexport default AreaOfWorkForm;\r\n", "\"use client\";\n\nimport * as React from 'react';\nimport classNames from 'classnames';\nimport { useBootstrapPrefix } from './ThemeProvider';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst CardBody = /*#__PURE__*/React.forwardRef(({\n  className,\n  bsPrefix,\n  as: Component = 'div',\n  ...props\n}, ref) => {\n  bsPrefix = useBootstrapPrefix(bsPrefix, 'card-body');\n  return /*#__PURE__*/_jsx(Component, {\n    ref: ref,\n    className: classNames(className, bsPrefix),\n    ...props\n  });\n});\nCardBody.displayName = 'CardBody';\nexport default CardBody;", "\"use client\";\n\nimport * as React from 'react';\nimport classNames from 'classnames';\nimport { useBootstrapPrefix } from './ThemeProvider';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst CardFooter = /*#__PURE__*/React.forwardRef(({\n  className,\n  bsPrefix,\n  as: Component = 'div',\n  ...props\n}, ref) => {\n  bsPrefix = useBootstrapPrefix(bsPrefix, 'card-footer');\n  return /*#__PURE__*/_jsx(Component, {\n    ref: ref,\n    className: classNames(className, bsPrefix),\n    ...props\n  });\n});\nCardFooter.displayName = 'CardFooter';\nexport default CardFooter;", "\"use client\";\n\nimport classNames from 'classnames';\nimport * as React from 'react';\nimport { useMemo } from 'react';\nimport { useBootstrapPrefix } from './ThemeProvider';\nimport CardHeaderContext from './CardHeaderContext';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst CardHeader = /*#__PURE__*/React.forwardRef(({\n  bsPrefix,\n  className,\n  // Need to define the default \"as\" during prop destructuring to be compatible with styled-components github.com/react-bootstrap/react-bootstrap/issues/3595\n  as: Component = 'div',\n  ...props\n}, ref) => {\n  const prefix = useBootstrapPrefix(bsPrefix, 'card-header');\n  const contextValue = useMemo(() => ({\n    cardHeaderBsPrefix: prefix\n  }), [prefix]);\n  return /*#__PURE__*/_jsx(CardHeaderContext.Provider, {\n    value: contextValue,\n    children: /*#__PURE__*/_jsx(Component, {\n      ref: ref,\n      ...props,\n      className: classNames(className, prefix)\n    })\n  });\n});\nCardHeader.displayName = 'CardHeader';\nexport default CardHeader;", "\"use client\";\n\nimport classNames from 'classnames';\nimport * as React from 'react';\nimport { useBootstrapPrefix } from './ThemeProvider';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst CardImg = /*#__PURE__*/React.forwardRef(\n// Need to define the default \"as\" during prop destructuring to be compatible with styled-components github.com/react-bootstrap/react-bootstrap/issues/3595\n({\n  bsPrefix,\n  className,\n  variant,\n  as: Component = 'img',\n  ...props\n}, ref) => {\n  const prefix = useBootstrapPrefix(bsPrefix, 'card-img');\n  return /*#__PURE__*/_jsx(Component, {\n    ref: ref,\n    className: classNames(variant ? `${prefix}-${variant}` : prefix, className),\n    ...props\n  });\n});\nCardImg.displayName = 'CardImg';\nexport default CardImg;", "\"use client\";\n\nimport * as React from 'react';\nimport classNames from 'classnames';\nimport { useBootstrapPrefix } from './ThemeProvider';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst CardImgOverlay = /*#__PURE__*/React.forwardRef(({\n  className,\n  bsPrefix,\n  as: Component = 'div',\n  ...props\n}, ref) => {\n  bsPrefix = useBootstrapPrefix(bsPrefix, 'card-img-overlay');\n  return /*#__PURE__*/_jsx(Component, {\n    ref: ref,\n    className: classNames(className, bsPrefix),\n    ...props\n  });\n});\nCardImgOverlay.displayName = 'CardImgOverlay';\nexport default CardImgOverlay;", "\"use client\";\n\nimport * as React from 'react';\nimport classNames from 'classnames';\nimport { useBootstrapPrefix } from './ThemeProvider';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst CardLink = /*#__PURE__*/React.forwardRef(({\n  className,\n  bsPrefix,\n  as: Component = 'a',\n  ...props\n}, ref) => {\n  bsPrefix = useBootstrapPrefix(bsPrefix, 'card-link');\n  return /*#__PURE__*/_jsx(Component, {\n    ref: ref,\n    className: classNames(className, bsPrefix),\n    ...props\n  });\n});\nCardLink.displayName = 'CardLink';\nexport default CardLink;", "\"use client\";\n\nimport * as React from 'react';\nimport classNames from 'classnames';\nimport { useBootstrapPrefix } from './ThemeProvider';\nimport divWithClassName from './divWithClassName';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst DivStyledAsH6 = divWithClassName('h6');\nconst CardSubtitle = /*#__PURE__*/React.forwardRef(({\n  className,\n  bsPrefix,\n  as: Component = DivStyledAsH6,\n  ...props\n}, ref) => {\n  bsPrefix = useBootstrapPrefix(bsPrefix, 'card-subtitle');\n  return /*#__PURE__*/_jsx(Component, {\n    ref: ref,\n    className: classNames(className, bsPrefix),\n    ...props\n  });\n});\nCardSubtitle.displayName = 'CardSubtitle';\nexport default CardSubtitle;", "\"use client\";\n\nimport * as React from 'react';\nimport classNames from 'classnames';\nimport { useBootstrapPrefix } from './ThemeProvider';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst CardText = /*#__PURE__*/React.forwardRef(({\n  className,\n  bsPrefix,\n  as: Component = 'p',\n  ...props\n}, ref) => {\n  bsPrefix = useBootstrapPrefix(bsPrefix, 'card-text');\n  return /*#__PURE__*/_jsx(Component, {\n    ref: ref,\n    className: classNames(className, bsPrefix),\n    ...props\n  });\n});\nCardText.displayName = 'CardText';\nexport default CardText;", "\"use client\";\n\nimport * as React from 'react';\nimport classNames from 'classnames';\nimport { useBootstrapPrefix } from './ThemeProvider';\nimport divWithClassName from './divWithClassName';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst DivStyledAsH5 = divWithClassName('h5');\nconst CardTitle = /*#__PURE__*/React.forwardRef(({\n  className,\n  bsPrefix,\n  as: Component = DivStyledAsH5,\n  ...props\n}, ref) => {\n  bsPrefix = useBootstrapPrefix(bsPrefix, 'card-title');\n  return /*#__PURE__*/_jsx(Component, {\n    ref: ref,\n    className: classNames(className, bsPrefix),\n    ...props\n  });\n});\nCardTitle.displayName = 'CardTitle';\nexport default CardTitle;", "\"use client\";\n\nimport classNames from 'classnames';\nimport * as React from 'react';\nimport { useBootstrapPrefix } from './ThemeProvider';\nimport CardBody from './CardBody';\nimport CardFooter from './CardFooter';\nimport CardHeader from './CardHeader';\nimport CardImg from './CardImg';\nimport CardImgOverlay from './CardImgOverlay';\nimport CardLink from './CardLink';\nimport CardSubtitle from './CardSubtitle';\nimport CardText from './CardText';\nimport CardTitle from './CardTitle';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst Card = /*#__PURE__*/React.forwardRef(({\n  bsPrefix,\n  className,\n  bg,\n  text,\n  border,\n  body = false,\n  children,\n  // Need to define the default \"as\" during prop destructuring to be compatible with styled-components github.com/react-bootstrap/react-bootstrap/issues/3595\n  as: Component = 'div',\n  ...props\n}, ref) => {\n  const prefix = useBootstrapPrefix(bsPrefix, 'card');\n  return /*#__PURE__*/_jsx(Component, {\n    ref: ref,\n    ...props,\n    className: classNames(className, prefix, bg && `bg-${bg}`, text && `text-${text}`, border && `border-${border}`),\n    children: body ? /*#__PURE__*/_jsx(CardBody, {\n      children: children\n    }) : children\n  });\n});\nCard.displayName = 'Card';\nexport default Object.assign(Card, {\n  Img: CardImg,\n  Title: CardTitle,\n  Subtitle: CardSubtitle,\n  Body: CardBody,\n  Link: CardLink,\n  Text: CardText,\n  Header: CardHeader,\n  Footer: CardFooter,\n  ImgOverlay: CardImgOverlay\n});", "import React from 'react';\r\nimport { useFormikContext, Field } from 'formik';\r\nimport { Form } from 'react-bootstrap';\r\n\r\ninterface RadioItemProps {\r\n  id: string;\r\n  label: string;\r\n  value: string;\r\n  name?: string;\r\n  disabled?: boolean;\r\n}\r\n\r\ninterface RadioGroupProps {\r\n  name: string;\r\n  valueSelected: string;\r\n  onChange: (e: React.ChangeEvent<HTMLInputElement>) => void;\r\n  errorMessage?: string;\r\n  children: React.ReactNode;\r\n}\r\n\r\n// Type Guard to ensure child.props is an object\r\nfunction isObject(props: any): props is { [key: string]: any } {\r\n  return typeof props === 'object' && props !== null;\r\n}\r\n\r\nconst RadioItem: React.FC<RadioItemProps> = ({ id, label, value, name, disabled }) => {\r\n  const { values, setFieldValue } = useFormikContext<any>();\r\n  const fieldName = name || id;\r\n\r\n  return (\r\n    <Form.Check\r\n      type=\"radio\"\r\n      id={id}\r\n      label={label}\r\n      value={value}\r\n      name={fieldName}\r\n      checked={values[fieldName] === value}\r\n      onChange={(e) => {\r\n        setFieldValue(fieldName, e.target.value);\r\n      }}\r\n      disabled={disabled}\r\n      inline\r\n    />\r\n  );\r\n};\r\n\r\nconst RadioGroup: React.FC<RadioGroupProps> = ({\r\n  name,\r\n  valueSelected,\r\n  onChange,\r\n  errorMessage,\r\n  children,\r\n}) => {\r\n  const { errors, touched } = useFormikContext<any>();\r\n  const hasError = touched[name] && errors[name];\r\n\r\n  // Create a context to pass the name to RadioItems\r\n  const radioContext = React.useMemo(() => ({ name }), [name]);\r\n\r\n  // Clone children to pass the name, ensuring props are spread safely\r\n  const childrenWithProps = React.Children.map(children, (child) => {\r\n    if (React.isValidElement(child)) {\r\n      // Ensure child.props is an object before spreading\r\n      if (isObject(child.props)) {\r\n        return React.cloneElement(child as React.ReactElement<RadioItemProps>, {\r\n          name,\r\n          ...child.props, // Safely spread child.props\r\n        });\r\n      }\r\n    }\r\n    return child;\r\n  });\r\n\r\n  return (\r\n    <div>\r\n      <div className=\"radio-group\">\r\n        {childrenWithProps}\r\n      </div>\r\n      {hasError && (\r\n        <div className=\"invalid-feedback d-block\">\r\n          {errorMessage || (typeof errors[name] === 'string' ? errors[name] : String(errors[name]))}\r\n        </div>\r\n      )}\r\n    </div>\r\n  );\r\n};\r\n\r\nexport const Radio = {\r\n  RadioGroup,\r\n  RadioItem,\r\n};\r\n\r\nexport default Radio;\r\n", "// This file exports all the components needed to replace react-bootstrap4-form-validation\r\n// It serves as a drop-in replacement for the original library\r\n\r\nimport ValidationForm from './ValidationFormWrapper';\r\nimport { TextInput, SelectGroup } from './FormikTextInput';\r\nimport { Radio } from './FormikRadio';\r\n\r\n// Export all components\r\nexport {\r\n  ValidationForm,\r\n  TextInput,\r\n  SelectGroup,\r\n  Radio\r\n};\r\n\r\n// Also export as default for convenience\r\nexport default {\r\n  ValidationForm,\r\n  TextInput,\r\n  SelectGroup,\r\n  Radio\r\n};\r\n", "import React, { forwardRef } from 'react';\r\nimport { Formik, Form, FormikProps, FormikHelpers } from 'formik';\r\nimport * as Yup from 'yup';\r\n\r\n// This is a wrapper component that replaces react-bootstrap4-form-validation with Formik\r\ninterface MockEvent {\r\n  preventDefault: () => void;\r\n  stopPropagation: () => void;\r\n  currentTarget: (EventTarget & Element) | null;\r\n  target: EventTarget | null;\r\n  nativeEvent: Event;\r\n  bubbles: boolean;\r\n  cancelable: boolean;\r\n  defaultPrevented: boolean;\r\n  eventPhase: number;\r\n  isTrusted: boolean;\r\n  timeStamp: number;\r\n  type: string;\r\n  isDefaultPrevented: () => boolean;\r\n  isPropagationStopped: () => boolean;\r\n  persist: () => void;\r\n}\r\n\r\ninterface ValidationFormWrapperProps {\r\n  children: React.ReactNode | ((formikProps: FormikProps<any>) => React.ReactNode);\r\n  onSubmit: (event: React.FormEvent | MockEvent, values?: Record<string, any>, actions?: FormikHelpers<Record<string, any>>) => void;\r\n  onErrorSubmit?: (errors: any) => void;\r\n  initialValues?: Record<string, any>;\r\n  enableReinitialize?: boolean;\r\n  autoComplete?: string;\r\n  className?: string;\r\n  onKeyPress?: (e: any) => void;\r\n}\r\n\r\nconst ValidationFormWrapper = forwardRef<HTMLFormElement, ValidationFormWrapperProps>((props, ref) => {\r\n  const { children, onSubmit, autoComplete, className, onKeyPress, initialValues, ...rest } = props;\r\n\r\n  // Create an empty validation schema by default\r\n  const validationSchema = Yup.object().shape({});\r\n\r\n  return (\r\n    <Formik\r\n      initialValues={initialValues || {}}\r\n      validationSchema={validationSchema}\r\n      onSubmit={(values: Record<string, any>, actions: FormikHelpers<Record<string, any>>) => {\r\n        // Create a mock event object with preventDefault method and currentTarget\r\n        const mockEvent: MockEvent = {\r\n          preventDefault: () => {},\r\n          stopPropagation: () => {},\r\n          currentTarget: null, // Set to null to avoid checkValidity errors\r\n          target: null,\r\n          nativeEvent: new Event('submit'),\r\n          bubbles: false,\r\n          cancelable: true,\r\n          defaultPrevented: false,\r\n          eventPhase: 0,\r\n          isTrusted: false,\r\n          timeStamp: Date.now(),\r\n          type: 'submit',\r\n          isDefaultPrevented: () => false,\r\n          isPropagationStopped: () => false,\r\n          persist: () => {}\r\n        };\r\n\r\n        if (onSubmit) {\r\n          // Pass the mock event object to maintain compatibility with the original code\r\n          onSubmit(mockEvent, values, actions);\r\n        }\r\n      }}\r\n      {...rest}\r\n    >\r\n      {(formikProps: FormikProps<any>) => (\r\n        <Form\r\n          ref={ref}\r\n          onSubmit={formikProps.handleSubmit}\r\n          autoComplete={autoComplete}\r\n          className={className}\r\n          onKeyPress={onKeyPress}\r\n        >\r\n          {typeof children === 'function' ? children(formikProps) : children}\r\n        </Form>\r\n      )}\r\n    </Formik>\r\n  );\r\n});\r\n\r\nValidationFormWrapper.displayName = 'ValidationFormWrapper';\r\n\r\nexport default ValidationFormWrapper;\r\n", "import React from 'react';\r\nimport { Form } from 'react-bootstrap';\r\nimport { Field, ErrorMessage, useField } from 'formik';\r\n\r\n// This component mimics the TextInput component from react-bootstrap4-form-validation\r\n// but uses Formik under the hood\r\nexport const TextInput = ({\r\n  name,\r\n  id,\r\n  required,\r\n  validator,\r\n  errorMessage,\r\n  onChange,\r\n  value,\r\n  as,\r\n  multiline,\r\n  rows,\r\n  pattern,\r\n  ...props\r\n}: any) => {\r\n  // Create a custom validation function that mimics the original validator\r\n  const validate = (val: any) => {\r\n    // Safely convert value to string and trim\r\n    const stringVal = typeof val === 'string' ? val : String(val || '');\r\n    if (required && (!val || stringVal.trim() === '')) {\r\n      return errorMessage?.validator || 'This field is required';\r\n    }\r\n\r\n    if (validator && !validator(val)) {\r\n      return errorMessage?.validator || 'Invalid value';\r\n    }\r\n\r\n    if (pattern && val) {\r\n      const regex = new RegExp(pattern);\r\n      if (!regex.test(val)) {\r\n        return errorMessage?.pattern || 'Invalid format';\r\n      }\r\n    }\r\n\r\n    return undefined;\r\n  };\r\n\r\n  return (\r\n    <Field name={name} validate={validate}>\r\n      {({ field, meta }: any) => (\r\n        <>\r\n          <Form.Control\r\n            {...field}\r\n            {...props}\r\n            id={id}\r\n            as={as || 'input'}\r\n            rows={rows}\r\n            isInvalid={meta.touched && !!meta.error}\r\n            onChange={(e: React.ChangeEvent<HTMLInputElement>) => {\r\n              field.onChange(e);\r\n              if (onChange) onChange(e);\r\n            }}\r\n            value={value !== undefined ? value : field.value}\r\n          />\r\n          {meta.touched && meta.error ? (\r\n            <Form.Control.Feedback type=\"invalid\">\r\n              {meta.error}\r\n            </Form.Control.Feedback>\r\n          ) : null}\r\n        </>\r\n      )}\r\n    </Field>\r\n  );\r\n};\r\n\r\n// This component mimics the SelectGroup component from react-bootstrap4-form-validation\r\n// but uses Formik under the hood\r\nexport const SelectGroup = ({\r\n  name,\r\n  id,\r\n  required,\r\n  errorMessage,\r\n  onChange,\r\n  value,\r\n  children,\r\n  ...props\r\n}: any) => {\r\n  // Create a custom validation function that mimics the original validator\r\n  const validate = (val: any) => {\r\n    if (required && (!val || val === '')) {\r\n      return errorMessage?.validator || 'This field is required';\r\n    }\r\n\r\n    return undefined;\r\n  };\r\n\r\n  return (\r\n    <Field name={name} validate={validate}>\r\n      {({ field, meta }: any) => (\r\n        <>\r\n          <Form.Control\r\n            as=\"select\"\r\n            {...field}\r\n            {...props}\r\n            id={id}\r\n            isInvalid={meta.touched && !!meta.error}\r\n            onChange={(e: React.ChangeEvent<HTMLSelectElement>) => {\r\n              field.onChange(e);\r\n              if (onChange) onChange(e);\r\n            }}\r\n            value={value !== undefined ? value : field.value}\r\n          >\r\n            {children}\r\n          </Form.Control>\r\n          {meta.touched && meta.error ? (\r\n            <Form.Control.Feedback type=\"invalid\">\r\n              {meta.error}\r\n            </Form.Control.Feedback>\r\n          ) : null}\r\n        </>\r\n      )}\r\n    </Field>\r\n  );\r\n};\r\n\r\n// Export both components as named exports and as a default object\r\nexport default {\r\n  TextInput,\r\n  SelectGroup\r\n};\r\n", "\n    (window.__NEXT_P = window.__NEXT_P || []).push([\n      \"/adminsettings/areaOfWork/forms\",\n      function () {\n        return require(\"private-next-pages/adminsettings/areaOfWork/forms.tsx\");\n      }\n    ]);\n    if(module.hot) {\n      module.hot.dispose(function () {\n        window.__NEXT_P.push([\"/adminsettings/areaOfWork/forms\"])\n      });\n    }\n  ", "\"use client\";\n\nimport * as React from 'react';\nconst context = /*#__PURE__*/React.createContext(null);\ncontext.displayName = 'CardHeaderContext';\nexport default context;"], "names": ["props", "t", "useTranslation", "AreaOfWorkForm", "_initialareaOfWork", "_id", "title", "initialVal", "setInitialVal", "useState", "editform", "routes", "formRef", "useRef", "handleSubmit", "event", "response", "toastMsg", "preventDefault", "obj", "trim", "apiService", "patch", "post", "toast", "success", "Router", "errorCode", "error", "useEffect", "areaOfWorkParams", "query", "sort", "limit", "getAreaOfWorkData", "get", "prevState", "div", "Container", "className", "fluid", "Card", "style", "marginTop", "boxShadow", "ValidationFormWrapper", "onSubmit", "ref", "initialValues", "enableReinitialize", "Body", "Row", "Col", "Title", "hr", "md", "lg", "sm", "Form", "Group", "Label", "TextInput", "name", "id", "required", "value", "validator", "String", "errorMessage", "onChange", "handleChange", "e", "target", "<PERSON><PERSON>", "type", "variant", "onClick", "re<PERSON><PERSON><PERSON><PERSON>", "window", "scrollTo", "Link", "href", "as", "CardBody", "React", "bsPrefix", "Component", "useBootstrapPrefix", "_jsx", "classNames", "displayName", "<PERSON><PERSON><PERSON>er", "<PERSON><PERSON><PERSON><PERSON>", "prefix", "contextValue", "useMemo", "cardHeaderBsPrefix", "CardHeaderContext", "Provider", "children", "CardImg", "CardImgOverlay", "CardLink", "DivStyledAsH6", "divWithClassName", "CardSubtitle", "CardText", "DivStyledAsH5", "CardTitle", "bg", "text", "border", "body", "Object", "assign", "Img", "Subtitle", "Text", "Header", "Footer", "ImgOverlay", "Radio", "RadioGroup", "valueSelected", "errors", "touched", "useFormikContext", "<PERSON><PERSON><PERSON><PERSON>", "childrenWithProps", "map", "child", "isObject", "RadioItem", "label", "disabled", "values", "setFieldValue", "fieldName", "Check", "checked", "inline", "ValidationForm", "SelectGroup", "forwardRef", "autoComplete", "onKeyPress", "rest", "validationSchema", "<PERSON><PERSON>", "shape", "<PERSON><PERSON>", "actions", "mockEvent", "stopPropagation", "currentTarget", "nativeEvent", "Event", "bubbles", "cancelable", "defaultPrevented", "eventPhase", "isTrusted", "timeStamp", "Date", "now", "isDefaultPrevented", "isPropagationStopped", "persist", "formikProps", "multiline", "rows", "pattern", "Field", "validate", "stringVal", "val", "RegExp", "test", "field", "meta", "Control", "isInvalid", "undefined", "<PERSON><PERSON><PERSON>", "context"], "sourceRoot": "", "ignoreList": [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 16]}