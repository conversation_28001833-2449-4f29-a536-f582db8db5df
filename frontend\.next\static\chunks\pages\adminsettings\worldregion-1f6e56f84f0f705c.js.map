{"version": 3, "file": "static/chunks/pages/adminsettings/worldregion-1f6e56f84f0f705c.js", "mappings": "0OAyIA,MA9H0BA,IACtB,GAAM,GAAEC,CAAC,CAAE,CAAGC,CAAAA,EAAAA,EAAAA,EAAAA,CAAcA,CAAC,MA6HlBC,IA5HL,CAACC,EAAWC,EAAe,CAAGC,CAAAA,EAAAA,EAAAA,CA4HTH,EAAC,KA5HQG,CAAQA,CAAC,EAAE,EACzC,EAAGC,EAAW,CAAGD,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,EAAC,GAC1B,CAACE,EAAWC,EAAa,CAAGH,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAC,GACrC,CAACI,EAASC,EAAW,CAAGL,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAC,IACjC,CAACM,EAAaC,EAAS,CAAGP,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,EAAC,GACnC,CAACQ,EAAmBC,EAAqB,CAAGT,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAC,CAAC,GAEtDU,EAAoB,CACtBC,KAAM,CAAEC,MAAO,KAAM,EACrBC,MAAOT,EACPU,KAAM,EACNC,MAAO,CAAC,CACZ,EAEMC,EAAU,CACZ,CACIC,KAAMtB,EAAE,wCACRuB,SAAU,GAAcC,EAAIP,KAAK,CACjCQ,SAAU,EACd,EACA,CACIH,KAAMtB,EAAE,uCACRuB,SAAU,GAAcC,EAAIE,IAAI,CAChCD,UAAU,EACVE,KAAM,GAAYC,EAAEF,IAAI,EAE5B,CACIJ,KAAMtB,EAAE,yCACRuB,SAAU,GAAcC,EAAIK,GAAG,CAC/BJ,UAAU,EACVE,KAAM,GACF,WAACG,MAAAA,WACG,UAACC,IAAIA,CAACC,KAAK,6BAA6BC,GAAI,OAAvCF,4BAAgF,OAANH,EAAEC,GAAG,WAEhF,UAACK,IAAAA,CAAEC,UAAU,uBAEV,OAEP,UAACC,IAAAA,CAAEC,QAAS,IAAMC,EAAWV,YACzB,UAACM,IAAAA,CAAEC,UAAU,4BACZ,MAGjB,EACH,CAEKI,EAAqB,UACvBjC,GAAW,GACX,IAAMkC,EAAW,MAAMC,EAAAA,CAAUA,CAACC,GAAG,CAAC,eAAgB3B,GAClDyB,GAAYA,EAASG,IAAI,EAAIH,EAASG,IAAI,CAACC,MAAM,CAAG,GAAG,CACvDxC,EAAeoC,EAASG,IAAI,EAC5BnC,EAAagC,EAASK,UAAU,EAChCvC,GAAW,GAEnB,EAOMwC,EAAsB,MAAOC,EAAiB5B,KAChDJ,EAAkBG,KAAK,CAAG6B,EAC1BhC,EAAkBI,IAAI,CAAGA,EACzBb,GAAW,GACX,IAAMkC,EAAW,MAAMC,EAAAA,CAAUA,CAACC,GAAG,CAAC,eAAgB3B,GAClDyB,GAAYA,EAASG,IAAI,EAAIH,EAASG,IAAI,CAACC,MAAM,CAAG,GAAG,CACvDxC,EAAeoC,EAASG,IAAI,EAC5BjC,EAAWqC,GACXzC,GAAW,GAEnB,EAEMgC,EAAa,MAAOd,IACtBV,EAAqBU,EAAIK,GAAG,EAC5BjB,GAAS,EACb,EAEMoC,EAAe,UACjB,GAAI,CACA,MAAMP,EAAAA,CAAUA,CAACQ,MAAM,CAAC,gBAAkC,OAAlBpC,IACxC0B,IACA3B,EAAS,IACTsC,EAAAA,EAAKA,CAACC,OAAO,CAACnD,EAAE,iEACpB,CAAE,MAAOoD,EAAO,CACZF,EAAAA,EAAKA,CAACE,KAAK,CAACpD,EAAE,2DAClB,CACJ,EAEMqD,EAAY,IAAMzC,EAAS,IAMjC,MAJA0C,CAAAA,EAAAA,EAAAA,SAAAA,CAASA,CAAC,KACNf,GACJ,EAAG,EAAE,EAGD,WAACT,MAAAA,WACG,WAACyB,EAAAA,CAAKA,CAAAA,CAACC,KAAM7C,EAAa8C,OAAQJ,YAC9B,UAACE,EAAAA,CAAKA,CAACG,MAAM,EAACC,WAAW,aACrB,UAACJ,EAAAA,CAAKA,CAACK,KAAK,WAAE5D,EAAE,wDAEpB,UAACuD,EAAAA,CAAKA,CAACM,IAAI,WAAE7D,EAAE,2EACf,WAACuD,EAAAA,CAAKA,CAACO,MAAM,YACT,UAACC,EAAAA,CAAMA,CAAAA,CAACC,QAAQ,YAAY3B,QAASgB,WAChCrD,EAAE,2CAEP,UAAC+D,EAAAA,CAAMA,CAAAA,CAACC,QAAQ,UAAU3B,QAASW,WAC9BhD,EAAE,8CAKf,UAACiE,EAAAA,CAAQA,CAAAA,CACL5C,QAASA,EACTsB,KAAMxC,EACNI,UAAWA,EACX2D,UAAW,GACXpB,oBAAqBA,EACrBqB,iBA/DchD,CA+DIgD,GA9D1BpD,EAAkBG,KAAK,CAAGT,EAC1BM,EAAkBI,IAAI,CAAGA,EACzBoB,GACJ,MA+DJ,iOCzEA,MAlDyB,QAkCjB6B,EAAAA,EAjCN,GAAM,GAAEpE,CAAC,CAAE,CAAGC,CAAAA,EAAAA,EAAAA,EAAAA,CAAcA,CAAC,UACvBoE,EAAuB,EAgDAC,EA9CzB,UAACxC,MAAAA,UACC,WAACyC,EAAAA,CAASA,CAAAA,CAACC,MAAO,CAAEC,UAAW,QAAS,EAAGC,KAAK,IAACvC,UAAU,gBACzD,UAACwC,EAAAA,CAAGA,CAAAA,UACF,UAACC,EAAAA,CAAGA,CAAAA,CAACC,GAAI,YACP,UAACC,EAAAA,CAAWA,CAAAA,CAAC7D,MAAOjB,EAAE,mDAG1B,UAAC2E,EAAAA,CAAGA,CAAAA,UACF,UAACC,EAAAA,CAAGA,CAAAA,CAACC,GAAI,YACP,UAAC9C,IAAIA,CACHC,KAAK,6BACLC,GAAG,OAFAF,sCAIH,UAACgC,EAAAA,CAAMA,CAAAA,CAACC,QAAQ,YAAYe,KAAK,cAChC/E,EAAE,wDAKT,UAAC2E,EAAAA,CAAGA,CAAAA,CAACxC,UAAU,gBACb,UAACyC,EAAAA,CAAGA,CAAAA,CAACC,GAAI,YACP,UAAE3E,EAAAA,OAAgBA,CAAAA,CAAAA,YAOxB8E,EAAqBC,CAAAA,EAAAA,EAAAA,iBAAAA,CAAiBA,CAAC,IAAM,UAACZ,EAAAA,CAAAA,IAC9CD,EAAYc,CAAAA,EAAAA,EAAAA,EAAAA,CAAWA,CAAC,GAAWd,SACzC,IAAI,GAAEA,GAAAA,OAAAA,EAAAA,EAAOe,IAAPf,OAAkB,GAAlBA,OAAAA,EAAAA,EAAoBgB,WAAAA,EAApBhB,KAAAA,EAAAA,CAAiC,CAAC,GAAlCA,UAA+C,EAInD,UAACY,EAAAA,CAAAA,GAHM,UAACK,EAAAA,OAAeA,CAAAA,CAAAA,EAK3B,6mBCjDA,IAAMC,EAAS,aACFC,EAAmBC,CAAAA,EAAAA,EAAAA,CAAAA,CAAoBA,CAAC,CACnDC,sBAAuB,KACjBrB,EAAMe,WAAW,IAAIf,EAAMe,WAAW,CAACO,YAAY,IAAItB,EAAMe,WAAW,CAACO,YAAY,CAACJ,EAAO,CAKnGK,CALqG,kBAKjF,kBACtB,GAAG,EAE0BH,CAAAA,EAAAA,EAAAA,CAAAA,CAAoBA,CAAC,CAChDC,sBAAuB,KACjBrB,EAAMe,WAAW,IAAIf,EAAMe,WAAW,CAACS,OAAO,IAAIxB,EAAMe,WAAW,CAACS,OAAO,CAACN,EAAO,CAKzFK,CAL2F,kBAKvE,eACtB,GAAG,EAEmCH,CAAAA,EAAAA,EAAAA,CAAAA,CAAoBA,CAAC,CACzDC,sBAAuB,KACjBrB,EAAMe,WAAW,IAAIf,EAAMe,WAAW,CAACU,iBAAiB,IAAIzB,EAAMe,WAAW,CAACU,iBAAiB,CAACP,EAAO,CAK7GK,CAL+G,kBAK3F,wBACtB,GAAG,EAE8BH,CAAAA,EAAAA,EAAAA,CAAAA,CAAoBA,CAAC,CACpDC,sBAAuB,KACjBrB,EAAMe,WAAW,IAAIf,EAAMe,WAAW,CAACW,YAAY,IAAI1B,EAAMe,WAAW,CAACW,YAAY,CAACR,EAAO,CAKnGK,CALqG,kBAKjF,mBACtB,GAEaI,EAAkBP,CAAAA,EAAAA,EAAAA,CAAAA,CAAoBA,CAAC,CAClDC,sBAAuB,KACjBrB,EAAMe,WAAW,IAAIf,EAAMe,WAAW,CAACa,SAAS,IAAI5B,EAAMe,WAAW,CAACa,SAAS,CAACV,EAAO,CAK7FK,CAL+F,kBAK3E,iBACtB,GAAG,EAEqCH,CAAAA,EAAAA,EAAAA,CAAAA,CAAoBA,CAAC,CAC3DC,sBAAuB,KACjBrB,EAAMe,WAAW,IAAIf,EAAMe,WAAW,CAACc,uBAAuB,IAAI7B,EAAMe,WAAW,CAACc,uBAAuB,CAACX,EAAO,CAKzHK,CAL2H,kBAKvG,0BACtB,GAAG,EAEiCH,CAAAA,EAAAA,EAAAA,CAAAA,CAAoBA,CAAC,CACvDC,sBAAwBrB,GAClBA,IAAMe,WAAW,IAAIf,EAAMe,WAAW,CAACc,uBAAuB,IAAI7B,EAAMe,WAAW,CAACc,uBAAuB,CAACX,EAAO,CAKzHK,CAL2H,kBAKvG,sBACtB,GAAG,EAC0BH,CAAAA,EAAAA,EAAAA,CAAAA,CAAoBA,CAAC,CAChDC,sBAAuB,KACjBrB,EAAMe,WAAW,IAAIf,EAAMe,WAAW,CAACe,MAAM,IAAI9B,EAAMe,WAAW,CAACe,MAAM,CAACZ,EAAO,CAKvFK,CALyF,kBAKrE,eACtB,GAAG,EAE8BH,CAAAA,EAAAA,EAAAA,CAAAA,CAAoBA,CAAC,CACpDC,sBAAuB,KACjBrB,EAAMe,WAAW,IAAIf,EAAMe,WAAW,CAACgB,WAAW,IAAI/B,EAAMe,WAAW,CAACgB,WAAW,CAACb,EAAO,CAKjGK,CALmG,kBAK/E,mBACtB,GAAG,EAEuCH,CAAAA,EAAAA,EAAAA,CAAAA,CAAoBA,CAAC,CAC7DC,sBAAuB,GACjBrB,IAAMe,WAAW,IAAIf,EAAMe,WAAW,CAACiB,WAAW,IAAIhC,EAAMe,WAAW,CAACiB,WAAW,CAACd,EAAO,CAKjGK,CALmG,kBAK/E,4BACtB,GAAG,EAEuCH,CAAAA,EAAAA,EAAAA,CAAAA,CAAoBA,CAAC,CAC7DC,sBAAuB,KACjBrB,EAAMe,WAAW,IAAIf,EAAMe,WAAW,CAACkB,mBAAmB,IAAIjC,EAAMe,WAAW,CAACkB,mBAAmB,CAACf,EAAO,CAKjHK,CALmH,kBAK/F,4BACtB,GAEaW,EAA0Bd,CAAAA,EAAAA,EAAAA,CAAAA,CAAoBA,CAAC,CAC1DC,sBAAuB,KACjBrB,EAAMe,WAAW,IAAIf,EAAMe,WAAW,CAACoB,gBAAgB,IAAInC,EAAMe,WAAW,CAACoB,gBAAgB,CAACjB,EAAO,CAK3GK,CAL6G,kBAKzF,yBACtB,GAAG,EAEkCH,CAAAA,EAAAA,EAAAA,CAAAA,CAAoBA,CAAC,CACxDC,sBAAuB,KACjBrB,EAAMe,WAAW,IAAIf,EAAMe,WAAW,CAACqB,gBAAgB,IAAIpC,EAAMe,WAAW,CAACqB,gBAAgB,CAAClB,EAAO,CAK3GK,CAL6G,kBAKzF,uBACtB,GAAG,EAEgCH,CAAAA,EAAAA,EAAAA,CAAAA,CAAoBA,CAAC,CACtDC,sBAAuB,GACjBrB,IAAMe,WAAW,IAAIf,EAAMe,WAAW,CAACsB,cAAc,IAAIrC,EAAMe,WAAW,CAACsB,cAAc,CAACnB,EAAO,CAKvGK,CALyG,kBAKrF,qBACtB,GAAG,EAE0BH,CAAAA,EAAAA,EAAAA,CAAAA,CAAoBA,CAAC,CAChDC,sBAAuB,KACjBrB,EAAMe,WAAW,IAAIf,EAAMe,WAAW,CAACuB,MAAM,IAAItC,EAAMe,WAAW,CAACuB,MAAM,CAACpB,EAAO,CAKvFK,CALyF,kBAKrE,eACtB,GAAG,EAE6BH,CAAAA,EAAAA,EAAAA,CAAAA,CAAoBA,CAAC,CACnDC,sBAAwBrB,KAClBA,EAAMe,WAAW,IAAIf,EAAMe,WAAW,CAACwB,UAAU,IAAIvC,EAAMe,WAAW,CAACwB,UAAU,CAACrB,EAAO,CAK/FK,CALiG,kBAK7E,kBACtB,GAAG,EAE4BH,CAAAA,EAAAA,EAAAA,CAAAA,CAAoBA,CAAC,CAClDC,sBAAuB,GACjBrB,IAAMe,WAAW,IAAIf,EAAMe,WAAW,CAACyB,QAAQ,IAAIxC,EAAMe,WAAW,CAACyB,QAAQ,CAACtB,EAAO,CAK3FK,CAL6F,kBAKzE,iBACtB,GAAG,EAE8BH,CAAAA,EAAAA,EAAAA,CAAAA,CAAoBA,CAAC,CACpDC,sBAAuB,KACjBrB,EAAMe,WAAW,IAAIf,EAAMe,WAAW,CAAC0B,WAAW,IAAIzC,EAAMe,WAAW,CAAC0B,WAAW,CAACvB,EAAO,CAKjGK,CALmG,kBAK/E,mBACtB,GAEamB,EAActB,CAAAA,EAAAA,EAAAA,CAAAA,CAAoBA,CAAC,CAC9CC,sBAAuB,KACjBrB,EAAMe,WAAW,IAAIf,EAAMe,WAAW,CAAC4B,KAAK,IAAI3C,EAAMe,WAAW,CAAC4B,KAAK,CAACzB,EAAO,CAKrFK,CALuF,kBAKnE,aACtB,GAAG,EAE8BH,CAAAA,EAAAA,EAAAA,CAAAA,CAAoBA,CAAC,CACpDC,sBAAuB,KACjBrB,EAAMe,WAAW,IAAIf,EAAMe,WAAW,CAACC,WAAW,IAAIhB,EAAMe,WAAW,CAACC,WAAW,CAACE,EAAO,CAKjGK,CALmG,kBAK/E,mBACtB,GAAG,EAE8BH,CAAAA,EAAAA,EAAAA,CAAAA,CAAoBA,CAAC,CACpDC,sBAAuB,KACjBrB,EAAMe,WAAW,IAAIf,EAAMe,WAAW,CAAC6B,YAAY,IAAI5C,EAAMe,WAAW,CAAC6B,YAAY,CAAC1B,EAAO,CAKnGK,CALqG,kBAKjF,mBACtB,GAAG,EAE0BH,CAAAA,EAAAA,EAAAA,CAAAA,CAAoBA,CAAC,CAChDC,sBAAuB,GACrB,EAAIrB,EAAMe,WAAW,IAAIf,EAAMe,WAAW,CAAC8B,SAAS,IAAI7C,EAAMe,WAAW,CAAC8B,SAAS,CAAC3B,EAAO,IAAIlB,EAAMe,WAAW,CAAC+B,OAAO,IAAI9C,EAAMe,WAAW,CAAC+B,OAAO,CAAC5B,EAAO,IAAGlB,EAAMe,WAAW,CAACgC,KAAK,IAAI/C,EAAMe,WAAW,CAACgC,KAAK,CAAC7B,EAAO,IAAGlB,EAAMe,WAAW,CAACiC,MAAM,IAAIhD,EAAMe,WAAW,CAACiC,MAAM,CAAC9B,EAAO,IAAGlB,EAAMe,WAAW,CAACiB,WAAW,IAAIhC,EAAMe,WAAW,CAACiB,WAAW,CAACd,EAAO,IAAGlB,EAAMe,WAAW,CAACkC,MAAM,IAAIjD,EAAMe,WAAW,CAACkC,MAAM,CAAC/B,EAAO,EAAE,CAG5Z,EAETK,mBAAoB,eACtB,GAAG,EAEYJ,gBAAgBA,EAAC,2FC1LhC,SAAStB,EAASqD,CAAoB,EACpC,GAAM,CAAEtH,GAAC,CAAE,CAAGC,CAAAA,EAAAA,EAAAA,EAAAA,CAAcA,CAAC,UACvBsH,EAA6B,CACjCC,gBAAiBxH,EAAE,cACnB,EACI,SACJqB,CAAO,MACPsB,CAAI,WACJpC,CAAS,uBACTkH,CAAqB,CACrBC,WAAS,CACTC,oBAAkB,qBAClB7E,CAAmB,kBACnBqB,CAAgB,aAChByD,CAAW,oBACXC,CAAkB,gBAClBC,CAAc,SACdC,CAAO,WACP7D,CAAS,sBACT8D,CAAoB,mBACpBC,CAAiB,YACjBC,CAAU,QACVC,CAAM,kBACNC,CAAgB,cAChBC,CAAY,CAEZ,CADA,EACGC,EACJ,CAAGhB,EAGEiB,EAAiB,4BACrBhB,EACAiB,gBAAiBxI,EAAE,IAP0C,MAQ7DyI,SAAU,GACVpH,UACAsB,KAAMA,GAAQ,EAAE,CAChB+F,OAAO,EACPC,2BAA4BlB,EAC5BmB,UAAWlB,EACXmB,gBAAiBd,qBACjBJ,EACAmB,YAAY,EACZC,iBAAkB7E,EAClB8E,kBAAmBnB,GAA0C,GAC7DoB,eADwCpB,cACVD,GAA4B,CAAC,GAAI,GAAI,GAAI,CAA3BA,EAA+B,GAAG,CAC9EsB,oBAAqB3I,EACrB4I,oBAAqBrG,EACrBsG,aAAcjF,iBACd2D,uBACAE,oBACAC,EACAoB,kBAAmB,UAACC,EAAAA,CAAYA,CAAAA,CAAAA,GAChCC,SAAU,UAACrH,IAAAA,CAAEC,UAAU,6CACvB+F,SACAC,EACAE,gCACAD,EACAjG,UAAW,WACb,EACA,MACE,UAACqH,EAAAA,EAASA,CAAAA,CAAE,GAAGjB,CAAc,EAEjC,CAEAtE,EAASwF,YAAY,CAAG,CACtBb,WAAW,EACXE,YAAY,EACZvI,UAAW,KACX2D,WAAW,EACX8D,qBAAsB,KACtBC,mBAAmB,EACnBC,YAAY,EACZE,kBAAkB,CACpB,EAEA,MAAenE,QAAQA,EAAC,mEChHT,SAASoB,EAAgBtF,CAAW,EAC/C,MACE,UAAC+B,MAAAA,CAAIK,UAAU,sDACb,UAACL,MAAAA,CAAIK,UAAU,mBAAU,yCAG/B,gECFa,SAAS2C,EAAYwC,CAAuB,EACzD,MACE,UAACoC,KAAAA,CAAGvH,UAAU,wBAAgBmF,EAAMrG,KAAK,EAE7C,mBCPA,4CACA,6BACA,WACA,OAAe,EAAQ,KAAwD,CAC/E,EACA,SAFsB", "sources": ["webpack://_N_E/./pages/adminsettings/worldregion/worldregionTable.tsx", "webpack://_N_E/./pages/adminsettings/worldregion/index.tsx", "webpack://_N_E/./pages/adminsettings/permissions.tsx", "webpack://_N_E/./components/common/RKITable.tsx", "webpack://_N_E/./pages/rNoAccess.tsx", "webpack://_N_E/./components/common/PageHeading.tsx", "webpack://_N_E/?d1a4"], "sourcesContent": ["//Import Library\r\nimport { useState, useEffect } from \"react\";\r\nimport Link from \"next/link\";\r\nimport { <PERSON><PERSON>, Button } from \"react-bootstrap\";\r\n\r\nimport toast from 'react-hot-toast';\r\n\r\n//Import services/components\r\nimport RKITable from \"../../../components/common/RKITable\";\r\nimport apiService from \"../../../services/apiService\";\r\nimport { useTranslation } from 'next-i18next';\r\nconst WorldregionTable = (_props: any) => {\r\n    const { t } = useTranslation('common');\r\n    const [tabledata, setDataToTable] = useState([]);\r\n    const [, setLoading] = useState(false);\r\n    const [totalRows, setTotalRows] = useState(0);\r\n    const [perPage, setPerPage] = useState(10);\r\n    const [isModalShow, setModal] = useState(false);\r\n    const [selectWorldregion, setSelectWorldregion] = useState({});\r\n\r\n    const worldregionParams = {\r\n        sort: { title: \"asc\" },\r\n        limit: perPage,\r\n        page: 1,\r\n        query: {},\r\n    };\r\n\r\n    const columns = [\r\n        {\r\n            name: t(\"adminsetting.worldregion.table.Title\"),\r\n            selector: (row: any) => row.title,\r\n            sortable: true,\r\n        },\r\n        {\r\n            name: t(\"adminsetting.worldregion.table.Code\"),\r\n            selector: (row: any) => row.code,\r\n            sortable: true,\r\n            cell: (d: any) => d.code,\r\n        },\r\n        {\r\n            name: t(\"adminsetting.worldregion.table.Action\"),\r\n            selector: (row: any) => row._id,\r\n            sortable: false,\r\n            cell: (d: any) => (\r\n                <div>\r\n                    <Link href=\"/adminsettings/[...routes]\" as={`/adminsettings/edit_worldregion/${d._id}`}>\r\n\r\n                        <i className=\"icon fas fa-edit\" />\r\n\r\n                    </Link>\r\n                    &nbsp;\r\n                    <a onClick={() => userAction(d)}>\r\n                        <i className=\"icon fas fa-trash-alt\" />\r\n                    </a>{\" \"}\r\n                </div>\r\n            ),\r\n        },\r\n    ];\r\n\r\n    const getWorldregionData = async () => {\r\n        setLoading(true);\r\n        const response = await apiService.get(\"/worldregion\", worldregionParams);\r\n        if (response && response.data && response.data.length > 0) {\r\n            setDataToTable(response.data);\r\n            setTotalRows(response.totalCount);\r\n            setLoading(false);\r\n        }\r\n    };\r\n    const handlePageChange = (page: any) => {\r\n        worldregionParams.limit = perPage;\r\n        worldregionParams.page = page;\r\n        getWorldregionData();\r\n    };\r\n\r\n    const handlePerRowsChange = async (newPerPage: any, page: any) => {\r\n        worldregionParams.limit = newPerPage;\r\n        worldregionParams.page = page;\r\n        setLoading(true);\r\n        const response = await apiService.get(\"/worldregion\", worldregionParams);\r\n        if (response && response.data && response.data.length > 0) {\r\n            setDataToTable(response.data);\r\n            setPerPage(newPerPage);\r\n            setLoading(false);\r\n        }\r\n    };\r\n\r\n    const userAction = async (row: any) => {\r\n        setSelectWorldregion(row._id);\r\n        setModal(true);\r\n    };\r\n\r\n    const modalConfirm = async () => {\r\n        try {\r\n            await apiService.remove(`/worldregion/${selectWorldregion}`);\r\n            getWorldregionData();\r\n            setModal(false);\r\n            toast.success(t(\"adminsetting.worldregion.table.worldRegionDeletedSuccessfully\"));\r\n        } catch (error) {\r\n            toast.error(t(\"adminsetting.worldregion.table.errorDeletingWorldRegion\"));\r\n        }\r\n    };\r\n\r\n    const modalHide = () => setModal(false);\r\n\r\n    useEffect(() => {\r\n        getWorldregionData();\r\n    }, []);\r\n\r\n    return (\r\n        <div>\r\n            <Modal show={isModalShow} onHide={modalHide}>\r\n                <Modal.Header closeButton>\r\n                    <Modal.Title>{t(\"adminsetting.worldregion.table.DeleteWorldregion\")}</Modal.Title>\r\n                </Modal.Header>\r\n                <Modal.Body>{t(\"adminsetting.worldregion.table.Areyousurewanttodeletethisworldregion?\")}</Modal.Body>\r\n                <Modal.Footer>\r\n                    <Button variant=\"secondary\" onClick={modalHide}>\r\n                        {t(\"adminsetting.worldregion.table.Cancel\")}\r\n                    </Button>\r\n                    <Button variant=\"primary\" onClick={modalConfirm}>\r\n                        {t(\"adminsetting.worldregion.table.Yes\")}\r\n                    </Button>\r\n                </Modal.Footer>\r\n            </Modal>\r\n\r\n            <RKITable\r\n                columns={columns}\r\n                data={tabledata}\r\n                totalRows={totalRows}\r\n                pagServer={true}\r\n                handlePerRowsChange={handlePerRowsChange}\r\n                handlePageChange={handlePageChange}\r\n            />\r\n        </div>\r\n    );\r\n};\r\n\r\nexport default WorldregionTable;\r\n", "//Import Library\r\nimport { Contain<PERSON>, <PERSON>, <PERSON>, Button } from \"react-bootstrap\";\r\nimport Link from \"next/link\";\r\n\r\n//Import services/components\r\nimport PageHeading from \"../../../components/common/PageHeading\";\r\nimport WorldregionTable from \"./worldregionTable\";\r\nimport { useTranslation } from 'next-i18next';\r\nimport { serverSideTranslations } from 'next-i18next/serverSideTranslations';\r\nimport { canAddWorldRegion } from \"../permissions\";\r\nimport { useSelector } from \"react-redux\";\r\nimport NoAccessMessage from \"../../rNoAccess\";\r\nconst WorldregionIndex = (_props: any) => {\r\n  const { t } = useTranslation('common');\r\n  const ShowWorldregionIndex = () => {\r\n    return (\r\n      <div>\r\n        <Container style={{ overflowX: \"hidden\" }} fluid className=\"p-0\">\r\n          <Row>\r\n            <Col xs={12}>\r\n              <PageHeading title={t(\"adminsetting.worldregion.form.WorldRegion\")}/>\r\n            </Col>\r\n          </Row>\r\n          <Row>\r\n            <Col xs={12}>\r\n              <Link\r\n                href=\"/adminsettings/[...routes]\"\r\n                as=\"/adminsettings/create_worldregion\"\r\n                >\r\n                <Button variant=\"secondary\" size=\"sm\">\r\n                {t(\"adminsetting.worldregion.form.Addworldregion\")}\r\n              </Button>\r\n              </Link>\r\n            </Col>\r\n          </Row>\r\n          <Row className=\"mt-3\">\r\n            <Col xs={12}>\r\n              < WorldregionTable />\r\n            </Col>\r\n          </Row>\r\n        </Container>\r\n      </div>\r\n    );\r\n  }\r\n  const ShowAddWorldRegion = canAddWorldRegion(() => <ShowWorldregionIndex />);\r\n  const state:any = useSelector((state) => state);\r\n  if (!(state?.permissions?.worl_region?.['create:any'])) {\r\n    return <NoAccessMessage />\r\n  }\r\n  return(\r\n    <ShowAddWorldRegion />\r\n  );\r\n}\r\n\r\nexport async function getServerSideProps({ locale } : { locale: string }) {\r\n  return {\r\n    props: {\r\n      ...(await serverSideTranslations(locale, ['common'])),\r\n    },\r\n  }\r\n}\r\n\r\nexport default WorldregionIndex;", "//Import services/components\r\nimport connectedAuthWrapper from 'redux-auth-wrapper/connectedAuthWrapper';\r\n\r\nconst create = \"create:any\";\r\nexport const canAddAreaOfWork = connectedAuthWrapper({\r\n  authenticatedSelector: (state) => {\r\n    if (state.permissions && state.permissions.area_of_work && state.permissions.area_of_work[create]) {\r\n      return true;\r\n    }\r\n    return false;\r\n  },\r\n  wrapperDisplayName: 'CanAddAreaOfWork',\r\n});\r\n\r\nexport const canAddCountry = connectedAuthWrapper({\r\n  authenticatedSelector: (state) => {\r\n    if (state.permissions && state.permissions.country && state.permissions.country[create]) {\r\n      return true;\r\n    }\r\n    return false;\r\n  },\r\n  wrapperDisplayName: 'CanAddCountry',\r\n});\r\n\r\nexport const canAddDeploymentStatus = connectedAuthWrapper({\r\n  authenticatedSelector: (state) => {\r\n    if (state.permissions && state.permissions.deployment_status && state.permissions.deployment_status[create]) {\r\n      return true;\r\n    }\r\n    return false;\r\n  },\r\n  wrapperDisplayName: 'CanAddDeploymentStatus',\r\n});\r\n\r\nexport const canAddEventStatus = connectedAuthWrapper({\r\n  authenticatedSelector: (state) => {\r\n    if (state.permissions && state.permissions.event_status && state.permissions.event_status[create]) {\r\n      return true;\r\n    }\r\n    return false;\r\n  },\r\n  wrapperDisplayName: 'CanAddEventStatus',\r\n});\r\n\r\nexport const canAddExpertise = connectedAuthWrapper({\r\n  authenticatedSelector: (state) => {\r\n    if (state.permissions && state.permissions.expertise && state.permissions.expertise[create]) {\r\n      return true;\r\n    }\r\n    return false;\r\n  },\r\n  wrapperDisplayName: 'CanAddExpertise',\r\n});\r\n\r\nexport const canAddFocalPointApproval = connectedAuthWrapper({\r\n  authenticatedSelector: (state) => {\r\n    if (state.permissions && state.permissions.institution_focal_point && state.permissions.institution_focal_point[create]) {\r\n      return true;\r\n    }\r\n    return false;\r\n  },\r\n  wrapperDisplayName: 'CanAddFocalPointApproval',\r\n});\r\n\r\nexport const canAddVspaceApproval = connectedAuthWrapper({\r\n  authenticatedSelector: (state) => {\r\n    if (state.permissions && state.permissions.institution_focal_point && state.permissions.institution_focal_point[create]) {\r\n      return true;\r\n    }\r\n    return false;\r\n  },\r\n  wrapperDisplayName: 'CanAddVspaceApproval',\r\n});\r\nexport const canAddHazards = connectedAuthWrapper({\r\n  authenticatedSelector: (state) => {\r\n    if (state.permissions && state.permissions.hazard && state.permissions.hazard[create]) {\r\n      return true;\r\n    }\r\n    return false;\r\n  },\r\n  wrapperDisplayName: 'CanAddHazards',\r\n});\r\n\r\nexport const canAddHazardTypes = connectedAuthWrapper({\r\n  authenticatedSelector: (state) => {\r\n    if (state.permissions && state.permissions.hazard_type && state.permissions.hazard_type[create]) {\r\n      return true;\r\n    }\r\n    return false;\r\n  },\r\n  wrapperDisplayName: 'CanAddHazardTypes',\r\n}); \r\n\r\nexport const canAddOrganisationApproval = connectedAuthWrapper({\r\n  authenticatedSelector: (state) => {\r\n    if (state.permissions && state.permissions.institution && state.permissions.institution[create]) {\r\n      return true;\r\n    }\r\n    return false;\r\n  },\r\n  wrapperDisplayName: 'CanAddOrganisationApproval',\r\n}); \r\n\r\nexport const canAddOrganisationNetworks = connectedAuthWrapper({\r\n  authenticatedSelector: (state) => {\r\n    if (state.permissions && state.permissions.institution_network && state.permissions.institution_network[create]) {\r\n      return true;\r\n    }\r\n    return false;\r\n  },\r\n  wrapperDisplayName: 'CanAddOrganisationNetworks',\r\n});\r\n\r\nexport const canAddOrganisationTypes = connectedAuthWrapper({\r\n  authenticatedSelector: (state) => {\r\n    if (state.permissions && state.permissions.institution_type && state.permissions.institution_type[create]) {\r\n      return true;\r\n    }\r\n    return false;\r\n  },\r\n  wrapperDisplayName: 'CanAddOrganisationTypes',\r\n});\r\n\r\nexport const canAddOperationStatus = connectedAuthWrapper({\r\n  authenticatedSelector: (state) => {\r\n    if (state.permissions && state.permissions.operation_status && state.permissions.operation_status[create]) {\r\n      return true;\r\n    }\r\n    return false;\r\n  },\r\n  wrapperDisplayName: 'CanAddOperationStatus',\r\n});\r\n\r\nexport const canAddProjectStatus = connectedAuthWrapper({\r\n  authenticatedSelector: (state) => {\r\n    if (state.permissions && state.permissions.project_status && state.permissions.project_status[create]) {\r\n      return true;\r\n    }\r\n    return false;\r\n  },\r\n  wrapperDisplayName: 'CanAddProjectStatus',\r\n});\r\n\r\nexport const canAddRegions = connectedAuthWrapper({\r\n  authenticatedSelector: (state) => {\r\n    if (state.permissions && state.permissions.region && state.permissions.region[create]) {\r\n      return true;\r\n    }\r\n    return false;\r\n  },\r\n  wrapperDisplayName: 'CanAddRegions',\r\n});\r\n\r\nexport const canAddRiskLevels = connectedAuthWrapper({\r\n  authenticatedSelector: (state) => {\r\n    if (state.permissions && state.permissions.risk_level && state.permissions.risk_level[create]) {\r\n      return true;\r\n    }\r\n    return false;\r\n  },\r\n  wrapperDisplayName: 'CanAddRiskLevels',\r\n});\r\n\r\nexport const canAddSyndromes = connectedAuthWrapper({\r\n  authenticatedSelector: (state) => {\r\n    if (state.permissions && state.permissions.syndrome && state.permissions.syndrome[create]) {\r\n      return true;\r\n    }\r\n    return false;\r\n  },\r\n  wrapperDisplayName: 'CanAddSyndromes',\r\n});\r\n\r\nexport const canAddUpdateTypes = connectedAuthWrapper({\r\n  authenticatedSelector: (state) => {\r\n    if (state.permissions && state.permissions.update_type && state.permissions.update_type[create]) {\r\n      return true;\r\n    }\r\n    return false;\r\n  },\r\n  wrapperDisplayName: 'CanAddUpdateTypes',\r\n});\r\n\r\nexport const canAddUsers = connectedAuthWrapper({\r\n  authenticatedSelector: (state) => {\r\n    if (state.permissions && state.permissions.users && state.permissions.users[create]) {\r\n      return true;\r\n    }\r\n    return false;\r\n  },\r\n  wrapperDisplayName: 'CanAddUsers',\r\n});\r\n\r\nexport const canAddWorldRegion = connectedAuthWrapper({\r\n  authenticatedSelector: (state) => {\r\n    if (state.permissions && state.permissions.worl_region && state.permissions.worl_region[create]) {\r\n      return true;\r\n    }\r\n    return false;\r\n  },\r\n  wrapperDisplayName: 'CanAddWorldRegion',\r\n});\r\n\r\nexport const canAddLandingPage = connectedAuthWrapper({\r\n  authenticatedSelector: (state) => {\r\n    if (state.permissions && state.permissions.landing_page && state.permissions.landing_page[create]) {\r\n      return true;\r\n    }\r\n    return false;\r\n  },\r\n  wrapperDisplayName: 'CanAddLandingPage',\r\n});\r\n\r\nexport const canAddContent = connectedAuthWrapper({\r\n  authenticatedSelector: (state) => {\r\n    if (state.permissions && state.permissions.operation && state.permissions.operation[create] && state.permissions.project && state.permissions.project[create]&& state.permissions.event && state.permissions.event[create]&& state.permissions.vspace && state.permissions.vspace[create]&& state.permissions.institution && state.permissions.institution[create]&& state.permissions.update && state.permissions.update[create]) {\r\n      return true;\r\n    }\r\n    return false;\r\n  },\r\n  wrapperDisplayName: 'CanAddContent',\r\n});\r\n\r\nexport default canAddAreaOfWork;", "//Import Library\r\nimport React from 'react'\r\nimport DataTable  from 'react-data-table-component';\r\n\r\n//Import services/components\r\nimport { useTranslation } from 'next-i18next';\r\nimport CustomLoader from './CustomLoader';\r\n\r\ninterface RKITableProps {\r\n  columns: any[];\r\n  data: any[];\r\n  totalRows?: number;\r\n  resetPaginationToggle?: boolean;\r\n  subheader?: boolean;\r\n  subHeaderComponent?: React.ReactNode;\r\n  handlePerRowsChange?: (newPerPage: number, page: number) => void;\r\n  handlePageChange?: (page: number) => void;\r\n  rowsPerPage?: number[];\r\n  defaultRowsPerPage?: number;\r\n  selectableRows?: boolean;\r\n  loading?: boolean;\r\n  pagServer?: boolean;\r\n  onSelectedRowsChange?: (selectedRows: any) => void;\r\n  clearSelectedRows?: boolean;\r\n  sortServer?: boolean;\r\n  onSort?: (column: any, sortDirection: string) => void;\r\n  persistTableHead?: boolean;\r\n  sortFunction?: (rows: any[], field: any, direction: 'asc' | 'desc') => any[];\r\n  noHeader?: boolean;\r\n  dense?: boolean;\r\n  pagination?: boolean;\r\n  paginationServer?: boolean;\r\n  paginationTotalRows?: number;\r\n  subHeaderAlign?: string;\r\n  [key: string]: any;\r\n}\r\n\r\nfunction RKITable(props: RKITableProps) {\r\n  const { t } = useTranslation('common');\r\n  const paginationComponentOptions = {\r\n    rowsPerPageText: t('Rowsperpage'),\r\n    };\r\n  const {\r\n    columns,\r\n    data,\r\n    totalRows,\r\n    resetPaginationToggle,\r\n    subheader,\r\n    subHeaderComponent,\r\n    handlePerRowsChange,\r\n    handlePageChange,\r\n    rowsPerPage,\r\n    defaultRowsPerPage,\r\n    selectableRows,\r\n    loading,\r\n    pagServer,\r\n    onSelectedRowsChange,\r\n    clearSelectedRows,\r\n    sortServer,\r\n    onSort,\r\n    persistTableHead,\r\n    sortFunction,\r\n    // Filter out any unknown props that might cause DOM warnings\r\n    ...otherProps\r\n  } = props;\r\n\r\n  // Only pass known DataTable props to avoid DOM warnings\r\n  const dataTableProps = {\r\n    paginationComponentOptions,\r\n    noDataComponent: t(\"NoData\"),\r\n    noHeader: true,\r\n    columns,\r\n    data: data || [],\r\n    dense: true,\r\n    paginationResetDefaultPage: resetPaginationToggle,\r\n    subHeader: subheader,\r\n    progressPending: loading,\r\n    subHeaderComponent,\r\n    pagination: true,\r\n    paginationServer: pagServer,\r\n    paginationPerPage: defaultRowsPerPage ? defaultRowsPerPage : 10,\r\n    paginationRowsPerPageOptions: rowsPerPage ? rowsPerPage : [10, 15, 20, 25, 30],\r\n    paginationTotalRows: totalRows,\r\n    onChangeRowsPerPage: handlePerRowsChange,\r\n    onChangePage: handlePageChange,\r\n    selectableRows,\r\n    onSelectedRowsChange,\r\n    clearSelectedRows,\r\n    progressComponent: <CustomLoader/>,\r\n    sortIcon: <i className=\"sort-icon fas fa-exchange-alt\" />,\r\n    sortServer,\r\n    onSort,\r\n    sortFunction,\r\n    persistTableHead,\r\n    className: \"rki-table\"\r\n  };\r\n  return (\r\n    <DataTable {...dataTableProps} />\r\n  )\r\n};\r\n\r\nRKITable.defaultProps = {\r\n  subHeader: false,\r\n  pagination: true,\r\n  totalRows: null,\r\n  pagServer: true,\r\n  onSelectedRowsChange: null,\r\n  clearSelectedRows: false,\r\n  sortServer: false,\r\n  persistTableHead: false\r\n}\r\n\r\nexport default RKITable;\r\n\r\n", "export default function NoAccessMessage(_props: any) {\r\n    return (\r\n      <div className=\"container-fluid p-0 response-message-block\">\r\n        <div className=\"message\">you don't have permission to access</div>\r\n      </div>\r\n    )\r\n  }", "interface PageHeadingProps {\r\n  title: string; // Required based on actual usage\r\n}\r\n\r\nexport default function PageHeading(props: PageHeadingProps) {\r\n  return (\r\n    <h2 className=\"page-heading\">{props.title}</h2>\r\n  )\r\n}\r\n", "\n    (window.__NEXT_P = window.__NEXT_P || []).push([\n      \"/adminsettings/worldregion\",\n      function () {\n        return require(\"private-next-pages/adminsettings/worldregion/index.tsx\");\n      }\n    ]);\n    if(module.hot) {\n      module.hot.dispose(function () {\n        window.__NEXT_P.push([\"/adminsettings/worldregion\"])\n      });\n    }\n  "], "names": ["_props", "t", "useTranslation", "WorldregionTable", "tabledata", "setDataToTable", "useState", "setLoading", "totalRows", "setTotalRows", "perPage", "setPerPage", "isModalShow", "setModal", "selectWorldregion", "setSelectWorldregion", "worldregionParams", "sort", "title", "limit", "page", "query", "columns", "name", "selector", "row", "sortable", "code", "cell", "d", "_id", "div", "Link", "href", "as", "i", "className", "a", "onClick", "userAction", "getWorldregionData", "response", "apiService", "get", "data", "length", "totalCount", "handlePerRowsChange", "newPerPage", "modalConfirm", "remove", "toast", "success", "error", "modalHide", "useEffect", "Modal", "show", "onHide", "Header", "closeButton", "Title", "Body", "Footer", "<PERSON><PERSON>", "variant", "RKITable", "pagServer", "handlePageChange", "state", "ShowWorldregionIndex", "WorldregionIndex", "Container", "style", "overflowX", "fluid", "Row", "Col", "xs", "PageHeading", "size", "ShowAddWorldRegion", "canAddWorldRegion", "useSelector", "permissions", "worl_region", "NoAccessMessage", "create", "canAddAreaOfWork", "connectedAuthWrapper", "authenticatedSelector", "area_of_work", "wrapperDisplayName", "country", "deployment_status", "event_status", "canAddExpertise", "expertise", "institution_focal_point", "hazard", "hazard_type", "institution", "institution_network", "canAddOrganisationTypes", "institution_type", "operation_status", "project_status", "region", "risk_level", "syndrome", "update_type", "canAddUsers", "users", "landing_page", "operation", "project", "event", "vspace", "update", "props", "paginationComponentOptions", "rowsPerPageText", "resetPaginationToggle", "subheader", "subHeaderComponent", "rowsPerPage", "defaultRowsPerPage", "selectableRows", "loading", "onSelectedRowsChange", "clearSelectedRows", "sortServer", "onSort", "persistTableHead", "sortFunction", "otherProps", "dataTableProps", "noDataComponent", "<PERSON><PERSON><PERSON><PERSON>", "dense", "paginationResetDefaultPage", "subHeader", "progressPending", "pagination", "paginationServer", "paginationPerPage", "paginationRowsPerPageOptions", "paginationTotalRows", "onChangeRowsPerPage", "onChangePage", "progressComponent", "CustomLoader", "sortIcon", "DataTable", "defaultProps", "h2"], "sourceRoot": "", "ignoreList": []}