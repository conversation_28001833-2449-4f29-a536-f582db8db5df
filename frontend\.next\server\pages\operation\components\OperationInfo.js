"use strict";(()=>{var e={};e.id=3756,e.ids=[636,3220,3756],e.modules={123:e=>{e.exports=require("dom-helpers/removeEventListener")},1332:e=>{e.exports=require("react-custom-scrollbars-2")},1428:e=>{e.exports=import("axios")},1680:e=>{e.exports=require("@restart/ui/utils")},1919:e=>{e.exports=require("react-transition-group/Transition")},3892:e=>{e.exports=require("classnames")},4048:e=>{e.exports=require("react-truncate")},6009:e=>{e.exports=require("dom-helpers/ownerDocument")},6952:e=>{e.exports=require("@restart/ui/Modal")},7374:e=>{e.exports=require("@restart/ui/Dropdown")},8732:e=>{e.exports=require("react/jsx-runtime")},9653:e=>{e.exports=require("@restart/ui/Overlay")},11242:e=>{e.exports=require("redux-persist/integration/react")},11688:e=>{e.exports=require("@restart/hooks/useWillUnmount")},12388:e=>{e.exports=require("dom-helpers/addEventListener")},13364:e=>{e.exports=require("redux-saga/effects")},14062:e=>{e.exports=import("react-redux")},14078:e=>{e.exports=import("swr")},14332:e=>{e.exports=require("uncontrollable")},16116:e=>{e.exports=require("invariant")},18622:e=>{e.exports=require("yup")},19121:e=>{e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},22326:e=>{e.exports=require("react-dom")},22541:e=>{e.exports=require("redux-persist/lib/storage")},25303:e=>{e.exports=require("@restart/ui/NavItem")},26324:e=>{e.exports=require("warning")},27825:e=>{e.exports=require("lodash")},27910:e=>{e.exports=require("stream")},28163:(e,r,t)=>{t.r(r),t.d(r,{canAddOperation:()=>n,canAddOperationForm:()=>p,canEditOperation:()=>u,canEditOperationForm:()=>c,canViewDiscussionUpdate:()=>l,default:()=>d});var s=t(8732);t(82015);var o=t(81366),i=t.n(o),a=t(61421);let n=i()({authenticatedSelector:e=>!!e.permissions&&!!e.permissions.operation&&!!e.permissions.operation["create:any"],wrapperDisplayName:"CanAddOperation"}),p=i()({authenticatedSelector:e=>!!e.permissions&&!!e.permissions.operation&&!!e.permissions.operation["create:any"],wrapperDisplayName:"CanAddOperationForm",FailureComponent:()=>(0,s.jsx)(a.default,{})}),u=i()({authenticatedSelector:(e,r)=>{if(e.permissions&&e.permissions.operation){if(e.permissions.operation["update:any"])return!0;else if(e.permissions.operation["update:own"]&&r.operation&&r.operation.user&&r.operation.user._id===e.user._id)return!0}return!1},wrapperDisplayName:"CanEditOperation"}),c=i()({authenticatedSelector:(e,r)=>{if(e.permissions&&e.permissions.operation){if(e.permissions.operation["update:any"])return!0;else if(e.permissions.operation["update:own"]&&r.operation&&r.operation.user&&r.operation.user._id===e.user._id)return!0}return!1},wrapperDisplayName:"CanEditOperationForm",FailureComponent:()=>(0,s.jsx)(a.default,{})}),l=i()({authenticatedSelector:e=>!!e.permissions&&!!e.permissions.update&&!!e.permissions.update["read:any"],wrapperDisplayName:"CanViewDiscussionUpdate"}),d=n},28217:e=>{e.exports=require("@restart/ui/DropdownItem")},29021:e=>{e.exports=require("fs")},29294:e=>{e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},29628:e=>{e.exports=require("dom-helpers/addClass")},29825:e=>{e.exports=require("prop-types")},29841:e=>{e.exports=require("dom-helpers/hasClass")},30362:e=>{e.exports=require("@restart/ui/DropdownToggle")},33873:e=>{e.exports=require("path")},36653:e=>{e.exports=require("nprogress")},36955:e=>{e.exports=require("@restart/hooks/useBreakpoint")},37766:e=>{e.exports=require("@restart/hooks/useMergedRefs")},39756:e=>{e.exports=import("redux")},40051:e=>{e.exports=require("dom-helpers/removeClass")},40361:e=>{e.exports=require("next/dist/compiled/next-server/pages.runtime.prod.js")},42893:e=>{e.exports=import("react-hot-toast")},43294:e=>{e.exports=require("formik")},50009:e=>{e.exports=require("@restart/ui/DropdownMenu")},54131:e=>{e.exports=import("@fortawesome/free-solid-svg-icons")},56187:(e,r,t)=>{t.a(e,async(e,s)=>{try{t.r(r),t.d(r,{config:()=>f,default:()=>d,getServerSideProps:()=>q,getStaticPaths:()=>m,getStaticProps:()=>x,reportWebVitals:()=>h,routeModule:()=>A,unstable_getServerProps:()=>v,unstable_getServerSideProps:()=>w,unstable_getStaticParams:()=>y,unstable_getStaticPaths:()=>P,unstable_getStaticProps:()=>g});var o=t(63885),i=t(80237),a=t(81413),n=t(9616),p=t.n(n),u=t(72386),c=t(95970),l=e([u,c]);[u,c]=l.then?(await l)():l;let d=(0,a.M)(c,"default"),x=(0,a.M)(c,"getStaticProps"),m=(0,a.M)(c,"getStaticPaths"),q=(0,a.M)(c,"getServerSideProps"),f=(0,a.M)(c,"config"),h=(0,a.M)(c,"reportWebVitals"),g=(0,a.M)(c,"unstable_getStaticProps"),P=(0,a.M)(c,"unstable_getStaticPaths"),y=(0,a.M)(c,"unstable_getStaticParams"),v=(0,a.M)(c,"unstable_getServerProps"),w=(0,a.M)(c,"unstable_getServerSideProps"),A=new o.PagesRouteModule({definition:{kind:i.A.PAGES,page:"/operation/components/OperationInfo",pathname:"/operation/components/OperationInfo",bundlePath:"",filename:""},components:{App:u.default,Document:p()},userland:c});s()}catch(e){s(e)}})},57664:e=>{e.exports=require("dom-helpers/scrollbarSize")},58928:e=>{e.exports=require("dom-helpers/canUseDOM")},59717:e=>{e.exports=require("@restart/ui/DropdownContext")},60560:e=>{e.exports=require("dom-helpers/contains")},63033:e=>{e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},65447:e=>{e.exports=require("@restart/ui/Button")},67364:e=>{e.exports=require("@restart/hooks/useCallbackRef")},68455:e=>{e.exports=import("redux-saga")},69722:e=>{e.exports=require("es6-promise")},74075:e=>{e.exports=require("zlib")},74716:e=>{e.exports=require("moment")},74987:e=>{e.exports=require("@restart/ui/ModalManager")},78097:e=>{e.exports=require("next-redux-saga")},78634:e=>{e.exports=require("@restart/ui/Anchor")},80237:(e,r)=>{Object.defineProperty(r,"A",{enumerable:!0,get:function(){return t}});var t=function(e){return e.PAGES="PAGES",e.PAGES_API="PAGES_API",e.APP_PAGE="APP_PAGE",e.APP_ROUTE="APP_ROUTE",e.IMAGE="IMAGE",e}({})},81366:e=>{e.exports=require("redux-auth-wrapper/connectedAuthWrapper")},81413:(e,r)=>{Object.defineProperty(r,"M",{enumerable:!0,get:function(){return function e(r,t){return t in r?r[t]:"then"in r&&"function"==typeof r.then?r.then(r=>e(r,t)):"function"==typeof r&&"default"===t?r:void 0}}})},81426:(e,r,t)=>{t.a(e,async(e,s)=>{try{t.d(r,{A:()=>d});var o=t(8732),i=t(14062),a=t(54131),n=t(82015),p=t(82053),u=t(63487),c=e([i,a,u]);[i,a,u]=c.then?(await c)():c;let l={operation:"Operation",institution:"Institution",event:"Event",project:"Project",vspace:"Vspace"},d=(0,i.connect)(e=>e)(e=>{let{user:r,entityId:t,entityType:s}=e,[i,c]=(0,n.useState)(!1),[d,x]=(0,n.useState)(""),m=async()=>{if(!r?._id)return;let e=await u.A.get("/flag",{query:{entity_id:t,user:r._id,onModel:l[s]}});e&&e.data&&e.data.length>0&&(x(e.data[0]),c(!0))},q=async e=>{if(e.preventDefault(),!r?._id)return;let o=!i,a={entity_type:s,entity_id:t,user:r._id,onModel:l[s]};if(o){let e=await u.A.post("/flag",a);e&&e._id&&(x(e),c(o))}else{let e=await u.A.remove(`/flag/${d._id}`);e&&e.n&&c(o)}};return(0,n.useEffect)(()=>{m()},[]),(0,o.jsx)("div",{className:"subscribe-flag",children:(0,o.jsxs)("a",{href:"",onClick:q,children:[(0,o.jsx)("span",{className:"check",children:i?(0,o.jsx)(p.FontAwesomeIcon,{className:"clickable checkIcon",icon:a.faCheckCircle,color:"#00CC00"}):(0,o.jsx)(p.FontAwesomeIcon,{className:"clickable minusIcon",icon:a.faPlusCircle,color:"#fff"})}),(0,o.jsx)(p.FontAwesomeIcon,{className:"bookmark",icon:a.faBookmark,color:"#d4d4d4"})]})})});s()}catch(e){s(e)}})},81521:e=>{e.exports=require("dom-helpers/querySelectorAll")},81895:e=>{e.exports=require("@restart/hooks/useEventCallback")},82015:e=>{e.exports=require("react")},82053:e=>{e.exports=require("@fortawesome/react-fontawesome")},86842:e=>{e.exports=require("@restart/ui/SelectableContext")},87571:e=>{e.exports=require("dom-helpers/transitionEnd")},88751:e=>{e.exports=require("next-i18next")},93787:e=>{e.exports=require("redux-persist")},94947:e=>{e.exports=require("@restart/hooks/useTimeout")},95970:(e,r,t)=>{t.a(e,async(e,s)=>{try{t.r(r),t.d(r,{default:()=>m});var o=t(8732);t(82015);var i=t(91353),a=t(82053),n=t(19918),p=t.n(n),u=t(54131),c=t(81426),l=t(28163),d=t(88751),x=e([u,c]);[u,c]=x.then?(await x)():x;let m=e=>{let{t:r}=(0,d.useTranslation)("common"),t=()=>(0,o.jsx)(o.Fragment,{children:e.editData?(0,o.jsx)(p(),{href:"/operation/[...routes]",as:`/operation/edit/${e.routeData.routes[1]}`,children:(0,o.jsxs)(i.A,{variant:"secondary",size:"sm",children:[(0,o.jsx)(a.FontAwesomeIcon,{icon:u.faPen}),"\xa0",r("Edit")]})}):""}),s=(0,l.canEditOperation)(()=>(0,o.jsx)(t,{}));return(0,o.jsx)(o.Fragment,{children:(0,o.jsxs)("section",{className:"d-flex justify-content-between",children:[(0,o.jsxs)("h4",{className:"operationTitle",children:[e.operation.title,"\xa0\xa0",e.routeData.routes&&e.routeData.routes[1]?(0,o.jsx)(s,{operation:e.operation}):null]}),(0,o.jsx)(c.A,{entityId:e.routeData.routes[1],entityType:"operation"})]})})};s()}catch(e){s(e)}})},96196:e=>{e.exports=require("next-redux-wrapper")},98320:e=>{e.exports=require("@restart/hooks/useIsomorphicEffect")},99460:e=>{e.exports=require("dom-helpers/css")}};var r=require("../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[6089,9216,9616,2386],()=>t(56187));module.exports=s})();