"use strict";(()=>{var e={};e.id=7600,e.ids=[636,3220,7600],e.modules={123:e=>{e.exports=require("dom-helpers/removeEventListener")},1332:e=>{e.exports=require("react-custom-scrollbars-2")},1428:e=>{e.exports=import("axios")},1680:e=>{e.exports=require("@restart/ui/utils")},1919:e=>{e.exports=require("react-transition-group/Transition")},3892:e=>{e.exports=require("classnames")},4048:e=>{e.exports=require("react-truncate")},6009:e=>{e.exports=require("dom-helpers/ownerDocument")},6952:e=>{e.exports=require("@restart/ui/Modal")},7374:e=>{e.exports=require("@restart/ui/Dropdown")},8732:e=>{e.exports=require("react/jsx-runtime")},9653:e=>{e.exports=require("@restart/ui/Overlay")},11242:e=>{e.exports=require("redux-persist/integration/react")},11688:e=>{e.exports=require("@restart/hooks/useWillUnmount")},12388:e=>{e.exports=require("dom-helpers/addEventListener")},13364:e=>{e.exports=require("redux-saga/effects")},14062:e=>{e.exports=import("react-redux")},14078:e=>{e.exports=import("swr")},14332:e=>{e.exports=require("uncontrollable")},16116:e=>{e.exports=require("invariant")},18622:e=>{e.exports=require("yup")},19121:e=>{e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},22326:e=>{e.exports=require("react-dom")},22541:e=>{e.exports=require("redux-persist/lib/storage")},25303:e=>{e.exports=require("@restart/ui/NavItem")},26324:e=>{e.exports=require("warning")},27825:e=>{e.exports=require("lodash")},27910:e=>{e.exports=require("stream")},28217:e=>{e.exports=require("@restart/ui/DropdownItem")},29021:e=>{e.exports=require("fs")},29294:e=>{e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},29628:e=>{e.exports=require("dom-helpers/addClass")},29825:e=>{e.exports=require("prop-types")},29841:e=>{e.exports=require("dom-helpers/hasClass")},30362:e=>{e.exports=require("@restart/ui/DropdownToggle")},33873:e=>{e.exports=require("path")},36653:e=>{e.exports=require("nprogress")},36955:e=>{e.exports=require("@restart/hooks/useBreakpoint")},37766:e=>{e.exports=require("@restart/hooks/useMergedRefs")},38609:e=>{e.exports=require("react-data-table-component")},39756:e=>{e.exports=import("redux")},40051:e=>{e.exports=require("dom-helpers/removeClass")},40361:e=>{e.exports=require("next/dist/compiled/next-server/pages.runtime.prod.js")},42404:(e,r,t)=>{t.a(e,async(e,s)=>{try{t.r(r),t.d(r,{default:()=>x});var a=t(8732),o=t(7082),i=t(83551),n=t(49481),u=t(99800),l=t(82015),p=t(63487),d=t(88751),c=e([u,p]);[u,p]=c.then?(await c)():c;let x=({countryHandler:e,value:r})=>{let{t,i18n:s}=(0,d.useTranslation)("common"),c="de"===s.language?{title_de:"asc"}:{title:"asc"},x=s.language,[g,m]=(0,l.useState)([]),h={sort:c,limit:"~",languageCode:x};return(0,l.useEffect)(()=>{(async()=>{let e=await p.A.get("/country",h);e&&e.data&&e.data.length>0&&m(e.data)})()},[]),(0,a.jsx)(o.A,{fluid:!0,children:(0,a.jsx)(i.A,{children:(0,a.jsx)(n.A,{md:4,className:"ps-1",children:(0,a.jsx)(u.default,{value:[r],placeholder:t("adminsetting.Regions.SelectCountry"),isClearable:!0,onChange:e,options:g.length>0?g.map((e,r)=>({value:e._id,label:e.title})):[]})})})})};s()}catch(e){s(e)}})},42893:e=>{e.exports=import("react-hot-toast")},43294:e=>{e.exports=require("formik")},50009:e=>{e.exports=require("@restart/ui/DropdownMenu")},51758:(e,r,t)=>{t.a(e,async(e,s)=>{try{t.r(r),t.d(r,{default:()=>h});var a=t(8732),o=t(82015),i=t(42893),n=t(19918),u=t.n(n),l=t(12403),p=t(91353),d=t(63487),c=t(56084),x=t(42404),g=t(88751),m=e([i,d,x]);[i,d,x]=m.then?(await m)():m;let h=e=>{let{t:r}=(0,g.useTranslation)("common"),[t,s]=(0,o.useState)([]),[,n]=(0,o.useState)(!1),[m,h]=(0,o.useState)(0),[q,P]=(0,o.useState)(10),[f,S]=(0,o.useState)(!1),[v,y]=(0,o.useState)({}),[b,A]=(0,o.useState)(""),w={sort:{title:"asc"},limit:q,page:1,query:{}},j=[{name:r("adminsetting.Regions.Region"),selector:e=>e.title,sortable:!0},{name:r("adminsetting.Regions.Country"),selector:e=>e.country?.title||"",sortable:!0,cell:e=>e.country&&e.country.title?e.country.title:""},{name:r("adminsetting.Regions.Action"),selector:e=>e._id,sortable:!1,cell:e=>(0,a.jsxs)("div",{children:[(0,a.jsx)(u(),{href:"/adminsettings/[...routes]",as:`/adminsettings/edit_region/${e._id}`,children:(0,a.jsx)("i",{className:"icon fas fa-edit"})}),"\xa0",(0,a.jsx)("a",{onClick:()=>_(e),children:(0,a.jsx)("i",{className:"icon fas fa-trash-alt"})})," "]})}],R=async()=>{n(!0);let e=await d.A.get("/region",w);e&&e.data&&(s(e.data),h(e.totalCount),n(!1))},C=async(e,r)=>{w.limit=e,w.page=r,b&&(w.query={country:b.value}),n(!0);let t=await d.A.get("/region",w);t&&t.data&&t.data.length>0&&(s(t.data),P(e),n(!1))},_=async e=>{y(e._id),S(!0)},M=async()=>{try{await d.A.remove(`/region/${v}`),R(),S(!1),i.default.success(r("adminsetting.Regions.Table.regionDeletedSuccessfully"))}catch(e){i.default.error(r("adminsetting.Regions.Table.errorDeletingRegion"))}},k=()=>S(!1);return(0,o.useEffect)(()=>{R()},[]),(0,o.useEffect)(()=>{b&&(w.query={country:b.value}),R()},[b]),(0,a.jsxs)("div",{className:"region__table",children:[(0,a.jsxs)(l.A,{show:f,onHide:k,children:[(0,a.jsx)(l.A.Header,{closeButton:!0,children:(0,a.jsx)(l.A.Title,{children:r("adminsetting.Regions.DeleteRegion")})}),(0,a.jsx)(l.A.Body,{children:r("adminsetting.Regions.Areyousurewanttodeletethisregion?")}),(0,a.jsxs)(l.A.Footer,{children:[(0,a.jsx)(p.A,{variant:"secondary",onClick:k,children:r("adminsetting.Regions.Cancel")}),(0,a.jsx)(p.A,{variant:"primary",onClick:M,children:r("adminsetting.Regions.Yes")})]})]}),(0,a.jsx)(x.default,{countryHandler:e=>{A(e)},value:b}),(0,a.jsx)(c.A,{columns:j,data:t,totalRows:m,pagServer:!0,handlePerRowsChange:C,handlePageChange:e=>{w.limit=q,w.page=e,b&&(w.query={country:b.value}),R()}})]})};s()}catch(e){s(e)}})},56084:(e,r,t)=>{t.d(r,{A:()=>l});var s=t(8732);t(82015);var a=t(38609),o=t.n(a),i=t(88751),n=t(30370);function u(e){let{t:r}=(0,i.useTranslation)("common"),t={rowsPerPageText:r("Rowsperpage")},{columns:a,data:u,totalRows:l,resetPaginationToggle:p,subheader:d,subHeaderComponent:c,handlePerRowsChange:x,handlePageChange:g,rowsPerPage:m,defaultRowsPerPage:h,selectableRows:q,loading:P,pagServer:f,onSelectedRowsChange:S,clearSelectedRows:v,sortServer:y,onSort:b,persistTableHead:A,sortFunction:w,...j}=e,R={paginationComponentOptions:t,noDataComponent:r("NoData"),noHeader:!0,columns:a,data:u||[],dense:!0,paginationResetDefaultPage:p,subHeader:d,progressPending:P,subHeaderComponent:c,pagination:!0,paginationServer:f,paginationPerPage:h||10,paginationRowsPerPageOptions:m||[10,15,20,25,30],paginationTotalRows:l,onChangeRowsPerPage:x,onChangePage:g,selectableRows:q,onSelectedRowsChange:S,clearSelectedRows:v,progressComponent:(0,s.jsx)(n.A,{}),sortIcon:(0,s.jsx)("i",{className:"sort-icon fas fa-exchange-alt"}),sortServer:y,onSort:b,sortFunction:w,persistTableHead:A,className:"rki-table"};return(0,s.jsx)(o(),{...R})}u.defaultProps={subHeader:!1,pagination:!0,totalRows:null,pagServer:!0,onSelectedRowsChange:null,clearSelectedRows:!1,sortServer:!1,persistTableHead:!1};let l=u},57664:e=>{e.exports=require("dom-helpers/scrollbarSize")},58928:e=>{e.exports=require("dom-helpers/canUseDOM")},59717:e=>{e.exports=require("@restart/ui/DropdownContext")},60560:e=>{e.exports=require("dom-helpers/contains")},63033:e=>{e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},65447:e=>{e.exports=require("@restart/ui/Button")},67364:e=>{e.exports=require("@restart/hooks/useCallbackRef")},68455:e=>{e.exports=import("redux-saga")},69722:e=>{e.exports=require("es6-promise")},74075:e=>{e.exports=require("zlib")},74716:e=>{e.exports=require("moment")},74987:e=>{e.exports=require("@restart/ui/ModalManager")},78097:e=>{e.exports=require("next-redux-saga")},78634:e=>{e.exports=require("@restart/ui/Anchor")},80237:(e,r)=>{Object.defineProperty(r,"A",{enumerable:!0,get:function(){return t}});var t=function(e){return e.PAGES="PAGES",e.PAGES_API="PAGES_API",e.APP_PAGE="APP_PAGE",e.APP_ROUTE="APP_ROUTE",e.IMAGE="IMAGE",e}({})},81366:e=>{e.exports=require("redux-auth-wrapper/connectedAuthWrapper")},81413:(e,r)=>{Object.defineProperty(r,"M",{enumerable:!0,get:function(){return function e(r,t){return t in r?r[t]:"then"in r&&"function"==typeof r.then?r.then(r=>e(r,t)):"function"==typeof r&&"default"===t?r:void 0}}})},81521:e=>{e.exports=require("dom-helpers/querySelectorAll")},81895:e=>{e.exports=require("@restart/hooks/useEventCallback")},82015:e=>{e.exports=require("react")},84211:(e,r,t)=>{t.a(e,async(e,s)=>{try{t.r(r),t.d(r,{config:()=>h,default:()=>c,getServerSideProps:()=>m,getStaticPaths:()=>g,getStaticProps:()=>x,reportWebVitals:()=>q,routeModule:()=>b,unstable_getServerProps:()=>v,unstable_getServerSideProps:()=>y,unstable_getStaticParams:()=>S,unstable_getStaticPaths:()=>f,unstable_getStaticProps:()=>P});var a=t(63885),o=t(80237),i=t(81413),n=t(9616),u=t.n(n),l=t(72386),p=t(51758),d=e([l,p]);[l,p]=d.then?(await d)():d;let c=(0,i.M)(p,"default"),x=(0,i.M)(p,"getStaticProps"),g=(0,i.M)(p,"getStaticPaths"),m=(0,i.M)(p,"getServerSideProps"),h=(0,i.M)(p,"config"),q=(0,i.M)(p,"reportWebVitals"),P=(0,i.M)(p,"unstable_getStaticProps"),f=(0,i.M)(p,"unstable_getStaticPaths"),S=(0,i.M)(p,"unstable_getStaticParams"),v=(0,i.M)(p,"unstable_getServerProps"),y=(0,i.M)(p,"unstable_getServerSideProps"),b=new a.PagesRouteModule({definition:{kind:o.A.PAGES,page:"/adminsettings/region/regionTable",pathname:"/adminsettings/region/regionTable",bundlePath:"",filename:""},components:{App:l.default,Document:u()},userland:p});s()}catch(e){s(e)}})},86842:e=>{e.exports=require("@restart/ui/SelectableContext")},87571:e=>{e.exports=require("dom-helpers/transitionEnd")},88751:e=>{e.exports=require("next-i18next")},93787:e=>{e.exports=require("redux-persist")},94947:e=>{e.exports=require("@restart/hooks/useTimeout")},96196:e=>{e.exports=require("next-redux-wrapper")},98320:e=>{e.exports=require("@restart/hooks/useIsomorphicEffect")},99460:e=>{e.exports=require("dom-helpers/css")},99800:e=>{e.exports=import("react-select")}};var r=require("../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[6089,9216,9616,2386],()=>t(84211));module.exports=s})();