{"version": 3, "file": "static/chunks/pages/operation/ListMapContainer-a7d11e3eba3c6d51.js", "mappings": "0MA4HA,MAnG0BA,IACxB,GAAM,CAAEC,MAAI,CAAE,CAAGC,CAAAA,EAAAA,EAAAA,EAAAA,CAAcA,CAAC,GAkGnBC,OAjGPC,EAAcH,EAAKI,KAiGIF,EAAC,CAjGG,CAC3B,YAAEG,CAAU,CAAE,CAAGN,EACjB,CAACO,EAAQC,EAAU,CAAGC,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAQ,EAAE,EACxC,CAACC,EAAcC,EAAgB,CAAQF,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAC,CAAC,GACjD,CAACG,EAAYC,EAAc,CAAQJ,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAC,CAAC,GAC7C,CAACK,EAAmBC,EAAqB,CAAQN,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAC,CAAC,GAoB3DO,EAAc,KAClBL,EAAgB,MAChBE,EAAc,KAChB,EAEMI,EAAgB,CAACC,EAA4DC,EAAaC,KAC9FJ,IACAL,EAAgBQ,GAChBN,EAAc,CACZQ,KAAMH,EAAUG,IAAI,CACpBC,GAAIJ,EAAUI,EAAE,CAChBC,UAAWL,EAAUK,SAAS,EAElC,EAEMC,EAA4B,KAChC,IAAMC,EAA+B,EAAE,CACvCC,IAAAA,OAAS,CAACpB,EAAY,IACpBmB,EAAsBE,IAAI,CAAC,CACzBC,MAAOC,EAAUD,KAAK,CACtBN,GAAIO,EAAUC,GAAG,CACjBC,IACEF,EAAUG,OAAO,EACjBH,EAAUG,OAAO,CAACC,WAAW,EAC7BC,WAAWL,EAAUG,OAAO,CAACC,WAAW,CAAC,EAAE,CAACE,QAAQ,EACtDC,IACEP,EAAUG,OAAO,EACjBH,EAAUG,OAAO,CAACC,WAAW,EAC7BC,WAAWL,EAAUG,OAAO,CAACC,WAAW,CAAC,EAAE,CAACI,SAAS,EACvDd,UAAWM,EAAUG,OAAO,EAAIH,EAAUG,OAAO,CAACF,GAAG,EAEzD,GACAtB,EAAU,IAAIiB,EAAsB,CACtC,EAUA,MARAa,CAAAA,EAAAA,EAAAA,SAAAA,CAASA,CAAC,KACRd,IACIlB,GAAcA,EAAWiC,MAAM,CAAG,GAAG,EACLb,IAAAA,OAAS,CAACpB,EAAY,KACnCkC,UAEzB,EAAG,CAAClC,EAAW,EAGb,UAACmC,EAAAA,CAAOA,CAAAA,CACNC,QAAS1B,EACTT,OAAQA,EACRF,SAAUD,EACVM,aAAcA,EACdE,WAAY,UAnEG,IACjB,GAAM,MAAE+B,CAAI,CAAE,CAAGC,SACjB,GAAYD,EAAKpB,SAAS,EAAIT,CAAiB,CAAC6B,EAAKpB,SAAS,CAAC,CAE3D,CAF6D,EAE7D,OAACsB,KAAAA,UACE/B,CAAiB,CAAC6B,EAAKpB,SAAS,CAAC,CAACuB,GAAG,CAAC,CAACC,EAAWC,IAE/C,UAACC,KAAAA,UACC,UAACC,IAAAA,CAAEC,KAAM,GAAiCJ,MAAAA,CAA9B3C,EAAY,oBAA2B,OAAT2C,EAAKjB,GAAG,WAAKiB,EAAKnB,KAAK,IAD1DoB,MAQZ,IACT,EAmDiBI,CAAWT,KAAM/B,aAE7BL,EAAOgC,MAAM,EAAI,EACdhC,EAAOuC,GAAG,CAAC,CAACC,EAAMC,IAEd,UAACK,EAAAA,CAAYA,CAAAA,CAEXhC,KAAM0B,EAAKnB,KAAK,CAChBN,GAAIyB,EAAKzB,EAAE,CACXC,UAAWwB,EAAKxB,SAAS,CACzB+B,KAAM,CACJC,IAAK,8BACP,EACAC,QAASvC,EACTwC,SAAUV,GARLC,IAYX,MAGV,wFCtDA,MA/CkD,OAAC,MACjD3B,EAAO,QAAQ,IACfC,CA6Ca+B,CA7CR,EAAE,CACP9B,QA4CyB8B,EAAC,EA5Cd,EAAE,MACdK,CAAI,MACJJ,CAAI,UACJG,CAAQ,SACRD,CAAO,OACP5B,CAAK,WACL+B,GAAY,CAAK,CAClB,UAsBC,GAAyC,UAAxB,OAAOF,EAAS1B,GAAG,EAAyC,UAAxB,OAAO0B,EAASrB,GAAG,CAKtE,UAACwB,EAAAA,EAAMA,CAAAA,CACLH,SAAUA,EACVH,KAAMA,EACN1B,MAAOA,GAASP,EAChBsC,UAAWA,EACXH,QA/BgB,CA+BPK,GA9BPL,GAeFA,EAdoB,IADT,EAETnC,EACAC,WAYmBH,IAXnBI,OACAmC,WACAD,CACF,EAGe,UACbA,EACAK,YAAa,IAAML,CACrB,EAE6BrC,EAEjC,IAIS,IAYX,mBCjEA,4CACA,8BACA,WACA,OAAe,EAAQ,KAAmD,CAC1E,EACA,SAFsB,+ECYtB,MARyB,OAAC,UAAEqC,CAAQ,OAQrBM,OARuBC,CAAY,QAQnBD,EARqBE,CAAQ,CAAS,GACnE,MACE,UAACC,EAAAA,EAAUA,CAAAA,CAACT,SAAUA,EAAUO,aAAcA,WAC5C,UAACG,MAAAA,UAAKF,KAGZ,ECdMG,EAAO,mBACPC,EAAQ,qBACRC,EAAO,eACPC,EAAW,kBAgTjB,EA/SkB,CAChB,CACE,YAAe,IA6SJC,OA5SX,EA4SoBA,EAAC,IA5SV,CACT,CACE,MAAS,SACX,EACD,EAEH,CACE,YAAeJ,EACf,QAAW,CACT,CACE,MAAS,SACX,EACD,EAEH,CACE,YAAeC,EACf,QAAW,CACT,CACE,MAAS,SACX,EACD,EAEH,CACE,YAAe,iBACf,YAAe,WACf,QAAW,CACT,CACE,WAAc,KAChB,EACD,EAEH,CACE,YAAe,yBACf,YAAeE,EACf,QAAW,CACT,CACE,MAAS,SACX,EACD,EAEH,CACE,YAAe,6BACf,YAAe,SACf,QAAW,CACT,CACE,WAAc,KAChB,EACD,EAEH,CACE,YAAe,6BACf,YAAeH,EACf,QAAW,CACT,CACE,MAAS,SACX,EACD,EAEH,CACE,YAAe,0BACf,YAAeG,EACf,QAAW,CACT,CACE,MAAS,SACX,EACD,EAEH,CACE,YAAe,qBACf,YAAeA,EACf,QAAW,CACT,CACE,MAAS,SACX,EACD,EAEH,CACE,YAAe,oBACf,YAAe,WACf,QAAW,CACT,CACE,MAAS,SACX,EACD,EAEH,CACE,YAAe,MACf,QAAW,CACT,CACE,WAAc,KAChB,EACD,EAEH,CACE,YAAe,MACf,YAAe,WACf,QAAW,CACT,CACE,MAAS,SACX,EACD,EAEH,CACE,YAAe,MACf,YAAe,cACf,QAAW,CACT,CACE,WAAc,KAChB,EACD,EAEH,CACE,YAAe,MACf,YAAeH,EACf,QAAW,CACT,CACE,MAAS,SACX,EACD,EAEH,CACE,YAAe,MACf,YAAeC,EACf,QAAW,CACT,CACE,MAAS,SACX,EACD,EAEH,CACE,YAAe,WACf,YAAe,gBACf,QAAW,CACT,CACE,MAAS,SACX,EACD,EAEH,CACE,YAAe,WACf,YAAeD,EACf,QAAW,CACT,CACE,MAAS,SACX,EACD,EAEH,CACE,YAAe,OACf,QAAW,CACT,CACE,WAAc,KAChB,EACD,EAEH,CACE,YAAe,OACf,YAAe,WACf,QAAW,CACT,CACE,MAAS,SACX,EAEJ,EACA,CACE,YAAe,OACf,YAAe,cACf,QAAW,CACT,CACE,WAAc,KAChB,EACD,EAEH,CACE,YAAe,OACf,YAAeA,EACf,QAAW,CACT,CACE,MAAS,SACX,EACD,EAEH,CACE,YAAe,OACf,YAAeC,EACf,QAAW,CACT,CACE,MAAS,SACX,EACD,EAEH,CACE,YAAeC,EACf,YAAe,WACf,QAAW,CACT,CACE,MAAS,SACX,EACD,EAEH,CACE,YAAeA,EACf,YAAeC,EACf,QAAW,CACT,CACE,MAAS,SACX,EACD,EAEH,CACE,YAAeD,EACf,YAAeF,EACf,QAAW,CACT,CACE,MAAS,SACX,EACD,EAEH,CACE,YAAeE,EACf,YAAeD,EACf,QAAW,CACT,CACE,MAAS,SACX,EACD,EAEH,CACE,YAAe,aACf,YAAe,SACf,QAAW,CACT,CACE,WAAc,KAChB,EACD,EAEH,CACE,YAAe,UACf,QAAW,CACT,CACE,WAAc,KAChB,EACD,EAEH,CACE,YAAe,UACf,YAAeD,EACf,QAAW,CACT,CACE,MAAS,SACX,EACD,EAEH,CACE,YAAe,UACf,YAAeC,EACf,QAAW,CACT,CACE,MAAS,SACX,EACD,EAEH,CACE,YAAe,eACf,YAAe,gBACf,QAAW,CACT,CACE,MAAS,SACX,EACD,EAEH,CACE,YAAe,kBACf,YAAe,WACf,QAAW,CACT,CACE,MAAS,SACX,EAEJ,EACA,CACE,YAAe,QACf,YAAe,WACf,QAAW,CACT,CACE,MAAS,SACX,EACD,EAEH,CACE,YAAe,QACf,YAAeD,EACf,QAAW,CACT,CACE,MAAS,SACX,EAEJ,EACD,2BCnND,MAzEwC,OAAC,SAyE1BK,GAxEb7D,CAAU,GAwEU6D,EAAC,SAvErB/D,CAAY,CACZgE,eAAa,UACbT,CAAQ,QACRU,EAAS,GAAG,OACZC,EAAQ,MAAM,UACdvE,CAAQ,MACRwE,EAAO,CAAC,SACRC,EAAU,CAAC,SACXpC,CAAO,CACR,GACO,QAAEqC,CAAM,CAAE,CAAGC,CAAAA,EAAAA,EAAAA,SAAAA,CAASA,GACtB,CAAEC,UAAQ,WAAEC,CAAS,CAAE,CAAIC,CAAAA,EAAAA,EAAAA,CAAAA,CAAaA,UAmB9C,EAAsB,SAAP,CAAQhB,MAAAA,UAAI,uBACtBc,EAGH,UAACd,MAAAA,CAAIiB,UAAU,yBACb,UAACjB,MAAAA,CAAIiB,UAAU,WAAWC,MAAO,CAAET,eAAOD,EAAQlB,SAAU,UAAW,WACrE,WAAC6B,EAAAA,EAASA,CAAAA,CACRC,kBAzBe,CACrBX,MAAOA,EACPD,OAA0B,UAAlB,OAAOA,EAAsB,GAAU,OAAPA,EAAO,MAAMA,CACvD,EAuBQa,OAhBOd,CAgBCc,EArBM,CACpBzD,IAAK,SAIyB0D,CAH9BrD,IAAK,SACP,EAmBQyC,KAAMA,EACNa,OAhBU,CAgBFC,GAfd7C,EAAI8C,UAAU,CAAC,CACbC,OAAQrB,CACV,EACF,EAaQsB,QAAS,CACPhB,EAhBWN,MAgBFM,EACTnB,WAAW,EACXoC,mBAAmB,EACnBC,mBAAmB,EACnBC,YAAY,EACZC,gBAAgB,EAChBC,gBAAgB,EAChBC,kBAAmB,EACrB,YAECnC,EACArD,GAAcF,GAAgBA,EAAaoD,WAAW,EACrD,UAACC,EAAgBA,CACfN,SAAU/C,EAAaoD,SADRC,EACmB,GAClCC,aAAc,KAEZqC,QAAQC,GAAG,CAAC,qBACZ5D,GAAAA,GACF,WAEC9B,GAHC8B,QA5BQ,UAACyB,MAAAA,UAAI,mBAsC7B", "sources": ["webpack://_N_E/./pages/operation/ListMapContainer.tsx", "webpack://_N_E/./components/common/RKIMapMarker.tsx", "webpack://_N_E/?92fa", "webpack://_N_E/./components/common/RKIMapInfowindow.tsx", "webpack://_N_E/./components/common/mapStyles.tsx", "webpack://_N_E/./components/common/RKIMap1.tsx"], "sourcesContent": ["//Import Library\r\nimport React, { useState, useEffect } from \"react\";\r\nimport _ from \"lodash\";\r\n\r\n//Import services/components\r\nimport RKIMAP1 from \"../../components/common/RKIMap1\";\r\nimport RKIMapMarker from \"../../components/common/RKIMapMarker\";\r\nimport { useTranslation } from 'next-i18next';\r\n\r\ninterface Operation {\r\n  _id: string;\r\n  title: string;\r\n  country?: {\r\n    _id: string;\r\n    coordinates?: Array<{\r\n      latitude: string;\r\n      longitude: string;\r\n    }>;\r\n  };\r\n}\r\n\r\ninterface ListMapContainerProps {\r\n  operations: Operation[];\r\n}\r\n\r\nconst ListMapContainer = (props: ListMapContainerProps) => {\r\n  const { i18n } = useTranslation('common');\r\n  const currentLang = i18n.language;\r\n  const { operations } = props;\r\n  const [points, setPoints] = useState<any[]>([]);\r\n  const [activeMarker, setactiveMarker]: any = useState({});\r\n  const [markerInfo, setMarkerInfo]: any = useState({});\r\n  const [groupedOperations, setGroupedOperations]: any = useState({});\r\n\r\n  const MarkerInfo = (Markerprops: { info: { countryId?: string } }) => {\r\n    const { info } = Markerprops;\r\n    if (info && info.countryId && groupedOperations[info.countryId]) {\r\n      return (\r\n        <ul>\r\n          {groupedOperations[info.countryId].map((item: any, index: any) => {\r\n            return (\r\n              <li key={index}>\r\n                <a href={`${currentLang}/operation/show/${item._id}`}>{item.title}</a>\r\n              </li>\r\n            );\r\n          })}\r\n        </ul>\r\n      );\r\n    }\r\n    return null;\r\n  };\r\n\r\n  const resetMarker = () => {\r\n    setactiveMarker(null);\r\n    setMarkerInfo(null);\r\n  };\r\n\r\n  const onMarkerClick = (propsinit: { name: string; id: string; countryId: string }, marker: any, e: any) => {\r\n    resetMarker();\r\n    setactiveMarker(marker);\r\n    setMarkerInfo({\r\n      name: propsinit.name,\r\n      id: propsinit.id,\r\n      countryId: propsinit.countryId,\r\n    });\r\n  };\r\n\r\n  const setPointersFromOperations = () => {\r\n    const filteroperationPoints: any[] = [];\r\n    _.forEach(operations, (operation) => {\r\n      filteroperationPoints.push({\r\n        title: operation.title,\r\n        id: operation._id,\r\n        lat:\r\n          operation.country &&\r\n          operation.country.coordinates &&\r\n          parseFloat(operation.country.coordinates[0].latitude),\r\n        lng:\r\n          operation.country &&\r\n          operation.country.coordinates &&\r\n          parseFloat(operation.country.coordinates[0].longitude),\r\n        countryId: operation.country && operation.country._id,\r\n      });\r\n    });\r\n    setPoints([...filteroperationPoints]);\r\n  };\r\n\r\n  useEffect(() => {\r\n    setPointersFromOperations();\r\n    if (operations && operations.length > 0) {\r\n      const groupByCountriesOperation = _.groupBy(operations, \"country._id\");\r\n      setGroupedOperations(groupByCountriesOperation);\r\n    }\r\n  }, [operations]);\r\n\r\n  return (\r\n    <RKIMAP1\r\n      onClose={resetMarker}\r\n      points={points}\r\n      language={currentLang}\r\n      activeMarker={activeMarker}\r\n      markerInfo={<MarkerInfo info={markerInfo} />}\r\n    >\r\n      {points.length >= 1\r\n        ? points.map((item, index) => {\r\n            return (\r\n              <RKIMapMarker\r\n                key={index}\r\n                name={item.title}\r\n                id={item.id}\r\n                countryId={item.countryId}\r\n                icon={{\r\n                  url: \"/images/map-marker-white.svg\",\r\n                }}\r\n                onClick={onMarkerClick}\r\n                position={item}\r\n              />\r\n            );\r\n          })\r\n        : null}\r\n    </RKIMAP1>\r\n  );\r\n};\r\n\r\nexport default ListMapContainer;\r\n", "import React from 'react';\r\nimport { Marker } from '@react-google-maps/api';\r\n\r\ninterface RKIMapMarkerProps {\r\n  name?: string;\r\n  id?: string;\r\n  countryId?: string;\r\n  type?: string;\r\n  icon?: {\r\n    url: string;\r\n    scaledSize?: google.maps.Size;\r\n  };\r\n  position: {\r\n    lat: number;\r\n    lng: number;\r\n  };\r\n  onClick?: (props: any, marker: any, e: any) => void;\r\n  title?: string;\r\n  draggable?: boolean;\r\n}\r\n\r\nconst RKIMapMarker: React.FC<RKIMapMarkerProps> = ({\r\n  name = 'Marker',\r\n  id = '',\r\n  countryId = '',\r\n  type,\r\n  icon,\r\n  position,\r\n  onClick,\r\n  title,\r\n  draggable = false,\r\n}) => {\r\n  const handleClick = (e: google.maps.MapMouseEvent) => {\r\n    if (onClick) {\r\n      const markerProps = {\r\n        name,\r\n        id,\r\n        countryId,\r\n        type,\r\n        position,\r\n      };\r\n\r\n      // Create a marker-like object for compatibility with old onClick signature\r\n      const marker = {\r\n        position,\r\n        getPosition: () => position,\r\n      };\r\n\r\n      onClick(markerProps, marker, e);\r\n    }\r\n  };\r\n\r\n  // Ensure position is valid\r\n  if (!position || typeof position.lat !== 'number' || typeof position.lng !== 'number') {\r\n    return null;\r\n  }\r\n\r\n  return (\r\n    <Marker\r\n      position={position}\r\n      icon={icon}\r\n      title={title || name}\r\n      draggable={draggable}\r\n      onClick={handleClick}\r\n    />\r\n  );\r\n};\r\n\r\nexport default RKIMapMarker;\r\n", "\n    (window.__NEXT_P = window.__NEXT_P || []).push([\n      \"/operation/ListMapContainer\",\n      function () {\n        return require(\"private-next-pages/operation/ListMapContainer.tsx\");\n      }\n    ]);\n    if(module.hot) {\n      module.hot.dispose(function () {\n        window.__NEXT_P.push([\"/operation/ListMapContainer\"])\n      });\n    }\n  ", "import { InfoWindow } from '@react-google-maps/api';\r\n\r\ninterface Props {\r\n  position: google.maps.LatLngLiteral;\r\n  onCloseClick?: () => void;\r\n  children?: React.ReactNode;\r\n}\r\n\r\nconst RKIMapInfowindow = ({ position, onCloseClick, children }: Props) => {\r\n  return (\r\n    <InfoWindow position={position} onCloseClick={onCloseClick}>\r\n      <div>{children}</div>\r\n    </InfoWindow>\r\n  );\r\n};\r\n\r\nexport default RKIMapInfowindow;\r\n", "const fill = \"labels.text.fill\"\r\nconst stoke = \"labels.text.stroke\"\r\nconst road = \"road.highway\"\r\nconst geometry = \"geometry.stroke\"\r\nconst mapStyles = [\r\n  {\r\n    \"elementType\": \"geometry\",\r\n    \"stylers\": [\r\n      {\r\n        \"color\": \"#1d2c4d\"\r\n      }\r\n    ]\r\n  },\r\n  {\r\n    \"elementType\": fill,\r\n    \"stylers\": [\r\n      {\r\n        \"color\": \"#8ec3b9\"\r\n      }\r\n    ]\r\n  },\r\n  {\r\n    \"elementType\": stoke,\r\n    \"stylers\": [\r\n      {\r\n        \"color\": \"#1a3646\"\r\n      }\r\n    ]\r\n  },\r\n  {\r\n    \"featureType\": \"administrative\",\r\n    \"elementType\": \"geometry\",\r\n    \"stylers\": [\r\n      {\r\n        \"visibility\": \"off\"\r\n      }\r\n    ]\r\n  },\r\n  {\r\n    \"featureType\": \"administrative.country\",\r\n    \"elementType\": geometry,\r\n    \"stylers\": [\r\n      {\r\n        \"color\": \"#4b6878\"\r\n      }\r\n    ]\r\n  },\r\n  {\r\n    \"featureType\": \"administrative.land_parcel\",\r\n    \"elementType\": \"labels\",\r\n    \"stylers\": [\r\n      {\r\n        \"visibility\": \"off\"\r\n      }\r\n    ]\r\n  },\r\n  {\r\n    \"featureType\": \"administrative.land_parcel\",\r\n    \"elementType\": fill,\r\n    \"stylers\": [\r\n      {\r\n        \"color\": \"#64779e\"\r\n      }\r\n    ]\r\n  },\r\n  {\r\n    \"featureType\": \"administrative.province\",\r\n    \"elementType\": geometry,\r\n    \"stylers\": [\r\n      {\r\n        \"color\": \"#4b6878\"\r\n      }\r\n    ]\r\n  },\r\n  {\r\n    \"featureType\": \"landscape.man_made\",\r\n    \"elementType\": geometry,\r\n    \"stylers\": [\r\n      {\r\n        \"color\": \"#334e87\"\r\n      }\r\n    ]\r\n  },\r\n  {\r\n    \"featureType\": \"landscape.natural\",\r\n    \"elementType\": \"geometry\",\r\n    \"stylers\": [\r\n      {\r\n        \"color\": \"#023e58\"\r\n      }\r\n    ]\r\n  },\r\n  {\r\n    \"featureType\": \"poi\",\r\n    \"stylers\": [\r\n      {\r\n        \"visibility\": \"off\"\r\n      }\r\n    ]\r\n  },\r\n  {\r\n    \"featureType\": \"poi\",\r\n    \"elementType\": \"geometry\",\r\n    \"stylers\": [\r\n      {\r\n        \"color\": \"#283d6a\"\r\n      }\r\n    ]\r\n  },\r\n  {\r\n    \"featureType\": \"poi\",\r\n    \"elementType\": \"labels.text\",\r\n    \"stylers\": [\r\n      {\r\n        \"visibility\": \"off\"\r\n      }\r\n    ]\r\n  },\r\n  {\r\n    \"featureType\": \"poi\",\r\n    \"elementType\": fill,\r\n    \"stylers\": [\r\n      {\r\n        \"color\": \"#6f9ba5\"\r\n      }\r\n    ]\r\n  },\r\n  {\r\n    \"featureType\": \"poi\",\r\n    \"elementType\": stoke,\r\n    \"stylers\": [\r\n      {\r\n        \"color\": \"#1d2c4d\"\r\n      }\r\n    ]\r\n  },\r\n  {\r\n    \"featureType\": \"poi.park\",\r\n    \"elementType\": \"geometry.fill\",\r\n    \"stylers\": [\r\n      {\r\n        \"color\": \"#023e58\"\r\n      }\r\n    ]\r\n  },\r\n  {\r\n    \"featureType\": \"poi.park\",\r\n    \"elementType\": fill,\r\n    \"stylers\": [\r\n      {\r\n        \"color\": \"#3C7680\"\r\n      }\r\n    ]\r\n  },\r\n  {\r\n    \"featureType\": \"road\",\r\n    \"stylers\": [\r\n      {\r\n        \"visibility\": \"off\"\r\n      }\r\n    ]\r\n  },\r\n  {\r\n    \"featureType\": \"road\",\r\n    \"elementType\": \"geometry\",\r\n    \"stylers\": [\r\n      {\r\n        \"color\": \"#304a7d\"\r\n      }\r\n    ]\r\n  },\r\n  {\r\n    \"featureType\": \"road\",\r\n    \"elementType\": \"labels.icon\",\r\n    \"stylers\": [\r\n      {\r\n        \"visibility\": \"off\"\r\n      }\r\n    ]\r\n  },\r\n  {\r\n    \"featureType\": \"road\",\r\n    \"elementType\": fill,\r\n    \"stylers\": [\r\n      {\r\n        \"color\": \"#98a5be\"\r\n      }\r\n    ]\r\n  },\r\n  {\r\n    \"featureType\": \"road\",\r\n    \"elementType\": stoke,\r\n    \"stylers\": [\r\n      {\r\n        \"color\": \"#1d2c4d\"\r\n      }\r\n    ]\r\n  },\r\n  {\r\n    \"featureType\": road,\r\n    \"elementType\": \"geometry\",\r\n    \"stylers\": [\r\n      {\r\n        \"color\": \"#2c6675\"\r\n      }\r\n    ]\r\n  },\r\n  {\r\n    \"featureType\": road,\r\n    \"elementType\": geometry,\r\n    \"stylers\": [\r\n      {\r\n        \"color\": \"#255763\"\r\n      }\r\n    ]\r\n  },\r\n  {\r\n    \"featureType\": road,\r\n    \"elementType\": fill,\r\n    \"stylers\": [\r\n      {\r\n        \"color\": \"#b0d5ce\"\r\n      }\r\n    ]\r\n  },\r\n  {\r\n    \"featureType\": road,\r\n    \"elementType\": stoke,\r\n    \"stylers\": [\r\n      {\r\n        \"color\": \"#023e58\"\r\n      }\r\n    ]\r\n  },\r\n  {\r\n    \"featureType\": \"road.local\",\r\n    \"elementType\": \"labels\",\r\n    \"stylers\": [\r\n      {\r\n        \"visibility\": \"off\"\r\n      }\r\n    ]\r\n  },\r\n  {\r\n    \"featureType\": \"transit\",\r\n    \"stylers\": [\r\n      {\r\n        \"visibility\": \"off\"\r\n      }\r\n    ]\r\n  },\r\n  {\r\n    \"featureType\": \"transit\",\r\n    \"elementType\": fill,\r\n    \"stylers\": [\r\n      {\r\n        \"color\": \"#98a5be\"\r\n      }\r\n    ]\r\n  },\r\n  {\r\n    \"featureType\": \"transit\",\r\n    \"elementType\": stoke,\r\n    \"stylers\": [\r\n      {\r\n        \"color\": \"#1d2c4d\"\r\n      }\r\n    ]\r\n  },\r\n  {\r\n    \"featureType\": \"transit.line\",\r\n    \"elementType\": \"geometry.fill\",\r\n    \"stylers\": [\r\n      {\r\n        \"color\": \"#283d6a\"\r\n      }\r\n    ]\r\n  },\r\n  {\r\n    \"featureType\": \"transit.station\",\r\n    \"elementType\": \"geometry\",\r\n    \"stylers\": [\r\n      {\r\n        \"color\": \"#3a4762\"\r\n      }\r\n    ]\r\n  },\r\n  {\r\n    \"featureType\": \"water\",\r\n    \"elementType\": \"geometry\",\r\n    \"stylers\": [\r\n      {\r\n        \"color\": \"#0e1626\"\r\n      }\r\n    ]\r\n  },\r\n  {\r\n    \"featureType\": \"water\",\r\n    \"elementType\": fill,\r\n    \"stylers\": [\r\n      {\r\n        \"color\": \"#4e6d70\"\r\n      }\r\n    ]\r\n  }\r\n];\r\n\r\nexport default mapStyles;", "import React from 'react';\r\nimport { GoogleMap, useJsApiLoader } from '@react-google-maps/api';\r\nimport RKIMapInfowindow from './RKIMapInfowindow';\r\nimport mapStyles from './mapStyles';\r\nimport { useRouter } from \"next/router\";\r\nimport { useGoogleMaps } from './GoogleMapsProvider';\r\n\r\ninterface RKIMap1Props {\r\n  markerInfo?: React.ReactNode;\r\n  activeMarker?: any;\r\n  initialCenter?: { lat: number; lng: number };\r\n  children?: React.ReactNode;\r\n  height?: number | string;\r\n  width?: string;\r\n  language?: string;\r\n  points?: any[];\r\n  zoom?: number;\r\n  minZoom?: number;\r\n  onClose?: () => void\r\n}\r\n\r\nconst RKIMap1: React.FC<RKIMap1Props> = ({\r\n  markerInfo,\r\n  activeMarker,\r\n  initialCenter,\r\n  children,\r\n  height = 300,\r\n  width = \"114%\",\r\n  language,\r\n  zoom = 1,\r\n  minZoom = 1,\r\n  onClose\r\n}) => {\r\n  const { locale } = useRouter();\r\n  const { isLoaded, loadError } =  useGoogleMaps();\r\n  const containerStyle = {\r\n    width: width,\r\n    height: typeof height === 'number' ? `${height}px` : height,\r\n  };\r\n\r\n  const defaultCenter = {\r\n    lat: 52.520017,\r\n    lng: 13.404195,\r\n  };\r\n\r\n  const center = initialCenter || defaultCenter;\r\n\r\n  const onMapLoad = (map: google.maps.Map) => {\r\n    map.setOptions({\r\n      styles: mapStyles,\r\n    });\r\n  };\r\n\r\n  if (loadError) return <div>Error loading maps</div>;\r\n  if (!isLoaded) return <div>Loading Maps...</div>;\r\n\r\n  return (\r\n    <div className=\"map-container\">\r\n      <div className=\"mapprint\" style={{ width, height, position: 'relative' }}>\r\n        <GoogleMap\r\n          mapContainerStyle={containerStyle}\r\n          center={center}\r\n          zoom={zoom}\r\n          onLoad={onMapLoad}\r\n          options={{\r\n            minZoom: minZoom,\r\n            draggable: true,\r\n            keyboardShortcuts: false,\r\n            streetViewControl: false,\r\n            panControl: false,\r\n            clickableIcons: false,\r\n            mapTypeControl: false,\r\n            fullscreenControl: true,\r\n          }}\r\n        >\r\n          {children}\r\n          {markerInfo && activeMarker && activeMarker.getPosition && (\r\n            <RKIMapInfowindow\r\n              position={activeMarker.getPosition()}\r\n              onCloseClick={() => {\r\n                // Handle close if needed\r\n                console.log('close click');\r\n                onClose?.()\r\n              }}\r\n            >\r\n              {markerInfo}\r\n            </RKIMapInfowindow>\r\n          )}\r\n        </GoogleMap>\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default RKIMap1;\r\n"], "names": ["props", "i18n", "useTranslation", "ListMapContainer", "currentLang", "language", "operations", "points", "setPoints", "useState", "activeMarker", "set<PERSON><PERSON><PERSON><PERSON>", "markerInfo", "setMarkerInfo", "groupedOperations", "setGroupedOperations", "reset<PERSON><PERSON><PERSON>", "onMarkerClick", "propsinit", "marker", "e", "name", "id", "countryId", "setPointersFromOperations", "filteroperationPoints", "_", "push", "title", "operation", "_id", "lat", "country", "coordinates", "parseFloat", "latitude", "lng", "longitude", "useEffect", "length", "groupByCountriesOperation", "RKIMAP1", "onClose", "info", "Markerprops", "ul", "map", "item", "index", "li", "a", "href", "MarkerInfo", "R<PERSON>IMapMarker", "icon", "url", "onClick", "position", "type", "draggable", "<PERSON><PERSON>", "handleClick", "getPosition", "RKIMapInfowindow", "onCloseClick", "children", "InfoWindow", "div", "fill", "stoke", "road", "geometry", "mapStyles", "RKIMap1", "initialCenter", "height", "width", "zoom", "minZoom", "locale", "useRouter", "isLoaded", "loadError", "useGoogleMaps", "className", "style", "GoogleMap", "mapContainerStyle", "center", "defaultCenter", "onLoad", "onMapLoad", "setOptions", "styles", "options", "keyboardShortcuts", "streetViewControl", "panControl", "clickableIcons", "mapTypeControl", "fullscreenControl", "console", "log"], "sourceRoot": "", "ignoreList": []}