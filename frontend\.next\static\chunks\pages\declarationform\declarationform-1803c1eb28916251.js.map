{"version": 3, "file": "static/chunks/pages/declarationform/declarationform-1803c1eb28916251.js", "mappings": "+EACA,4CACA,mCACA,WACA,OAAe,EAAQ,KAAwD,CAC/E,EACA,SAFsB", "sources": ["webpack://_N_E/?b25f"], "sourcesContent": ["\n    (window.__NEXT_P = window.__NEXT_P || []).push([\n      \"/declarationform/declarationform\",\n      function () {\n        return require(\"private-next-pages/declarationform/declarationform.tsx\");\n      }\n    ]);\n    if(module.hot) {\n      module.hot.dispose(function () {\n        window.__NEXT_P.push([\"/declarationform/declarationform\"])\n      });\n    }\n  "], "names": [], "sourceRoot": "", "ignoreList": []}