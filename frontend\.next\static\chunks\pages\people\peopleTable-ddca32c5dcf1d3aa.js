(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[3979],{39518:(e,t,i)=>{(window.__NEXT_P=window.__NEXT_P||[]).push(["/people/peopleTable",function(){return i(63128)}])},50749:(e,t,i)=>{"use strict";i.d(t,{A:()=>u});var n=i(37876);i(14232);var s=i(89773),a=i(31753),o=i(5507);function r(e){let{t}=(0,a.Bd)("common"),i={rowsPerPageText:t("Rowsperpage")},{columns:r,data:u,totalRows:l,resetPaginationToggle:d,subheader:c,subHeaderComponent:p,handlePerRowsChange:m,handlePageChange:g,rowsPerPage:_,defaultRowsPerPage:h,selectableRows:y,loading:P,pagServer:w,onSelectedRowsChange:f,clearSelectedRows:b,sortServer:S,onSort:A,persistTableHead:R,sortFunction:x,...v}=e,C={paginationComponentOptions:i,noDataComponent:t("NoData"),noHeader:!0,columns:r,data:u||[],dense:!0,paginationResetDefaultPage:d,subHeader:c,progressPending:P,subHeaderComponent:p,pagination:!0,paginationServer:w,paginationPerPage:h||10,paginationRowsPerPageOptions:_||[10,15,20,25,30],paginationTotalRows:l,onChangeRowsPerPage:m,onChangePage:g,selectableRows:y,onSelectedRowsChange:f,clearSelectedRows:b,progressComponent:(0,n.jsx)(o.A,{}),sortIcon:(0,n.jsx)("i",{className:"sort-icon fas fa-exchange-alt"}),sortServer:S,onSort:A,sortFunction:x,persistTableHead:R,className:"rki-table"};return(0,n.jsx)(s.Ay,{...C})}r.defaultProps={subHeader:!1,pagination:!0,totalRows:null,pagServer:!0,onSelectedRowsChange:null,clearSelectedRows:!1,sortServer:!1,persistTableHead:!1};let u=r},63128:(e,t,i)=>{"use strict";i.r(t),i.d(t,{default:()=>l});var n=i(37876),s=i(14232),a=i(72178),o=i(50749),r=i(53718),u=i(31753);let l=function(e){let[t,i]=(0,s.useState)([]),{t:l}=(0,u.Bd)("common"),[d,c]=(0,s.useState)(!1),[p,m]=(0,s.useState)(0),[g,_]=(0,s.useState)(10),[h]=(0,s.useState)([]),[y]=(0,s.useState)([]),[P,w]=(0,s.useState)(""),[f,b]=(0,s.useState)(null),[S,A]=(0,s.useState)(!1),R={sort:{created_at:"desc"},limit:g,page:1,query:{status:{$ne:"Request Pending"},vspace_status:{$ne:"Request Pending"}},select:"-country -dial_code -firstname -region -institution.address -institution.contact_name -institution.description -institution.dial_code -institution.document -institution.doc_src -institution.images -institution.images_src -institution.expertise -institution.hazards -institution.hazard_types -institution.header -institution.networks -institution.partners -institution.telephone -institution.twitter -institution.type -institution.unit -institution.user -mobile_number -position"},x=[{name:l("People.form.Username"),selector:"username",cell:e=>e.username,sortable:!0},{name:l("People.form.Email"),selector:"email",cell:e=>e.email,sortable:!0},{name:l("People.form.Role"),selector:"roles",cell:e=>e.roles?e.roles[0]:"",sortable:!0},{name:l("People.form.Organisation"),selector:"institution",cell:e=>e.institution&&e.institution.title?e.institution.title:"",sortable:!0}],v=async e=>{c(!0);let t=await r.A.get("/users",e);t&&Array.isArray(t.data)&&(i(t.data),m(t.totalCount),c(!1))},C=async(e,t)=>{c(!0),R.sort={[e.selector]:t},await v(R),b(R),c(!1)},q=async(e,t)=>{R.limit=e,R.page=t,c(!0);let n=await r.A.get("/users",R);n&&Array.isArray(n.data)&&(i(n.data),_(e),c(!1))};(0,s.useEffect)(()=>{v(R)},[]);let j=s.useMemo(()=>{let e=e=>{e&&(/^[^@]+@[^@]+\.[^@]+$/.test(e.toLowerCase())?R.query={...R.query,email:e}:R.query={...R.query,username:e}),v(R),R.query={status:{$ne:"Request Pending"},vspace_status:{$ne:"Request Pending"}}},t=()=>{i()},i=()=>{e(P)};return(0,n.jsx)(a.default,{onFilter:t=>{t&&t.label?(w(t.label),e(t.label)):(R.query={status:{$ne:"Request Pending"},vspace_status:{$ne:"Request Pending"}},w(""),v(R))},onClear:()=>{P&&(A(!S),w(""))},filterText:P,roles:h,onHandleSearch:i,institutions:y,onKeyPress:e=>{"Enter"===e.key&&t()}})},[P]);return(0,n.jsx)("div",{children:(0,n.jsx)(o.A,{columns:x,data:t,totalRows:p,subheader:!0,persistTableHead:!0,loading:d,onSort:C,sortServer:!0,pagServer:!0,resetPaginationToggle:S,subHeaderComponent:j,handlePerRowsChange:q,handlePageChange:e=>{R.limit=g,R.page=e,f&&(R.sort=f.sort),v(R)}})})}},72178:(e,t,i)=>{"use strict";i.r(t),i.d(t,{default:()=>c});var n=i(37876),s=i(49589),a=i(56970),o=i(37784),r=i(2827),u=i(14232),l=i(53718),d=i(31753);let c=e=>{let{filterText:t,onFilter:i,onClear:c,roles:p,onHandleSearch:m,institutions:g,onKeyPress:_}=e,{t:h}=(0,d.Bd)("common"),[y,P]=(0,u.useState)([]),[,w]=(0,u.useState)(!1),f={sort:{created_at:"desc"},limit:"~",page:1,query:{},select:"-country -dial_code -firstname -region -institution.address -institution.contact_name -institution.description -institution.dial_code -institution.document -institution.doc_src -institution.images -institution.images_src -institution.expertise -institution.hazards -institution.hazard_types -institution.header -institution.networks -institution.partners -institution.telephone -institution.twitter -institution.type -institution.unit -institution.user -mobile_number -position"},b=async()=>{w(!0);let e=await l.A.get("/users",f);e&&Array.isArray(e.data)&&(P(e.data.map((e,t)=>({label:e.username,value:e._id}))),w(!1))};return(0,u.useEffect)(()=>{b()},[]),(0,n.jsx)(s.A,{fluid:!0,className:"p-0",children:(0,n.jsx)(a.A,{children:(0,n.jsx)(o.A,{xs:12,md:6,lg:4,className:"p-0 me-3",children:(0,n.jsx)(r.Ay,{autoFocus:!0,isClearable:!0,isSearchable:!0,onKeyDown:_,onChange:i,placeholder:h("People.form.UsernameorEmail"),options:y})})})})}}},e=>{var t=t=>e(e.s=t);e.O(0,[9773,698,2827,636,6593,8792],()=>t(39518)),_N_E=e.O()}]);
//# sourceMappingURL=peopleTable-ddca32c5dcf1d3aa.js.map