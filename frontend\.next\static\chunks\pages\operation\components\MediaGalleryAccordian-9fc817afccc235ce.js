(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[8373],{33458:(e,s,a)=>{"use strict";a.d(s,{A:()=>d});var i=a(37876),r=a(14232),l=a(65418),c=a(21772),n=a(11041),o=a(60282),t=a(31753);a(57637);let d=e=>{let{t:s}=(0,t.Bd)("common"),[a,d]=(0,r.useState)([]),m=e=>{let a=/(http|https):\/\/(\w+:{0,1}\w*)?(\S+)(:[0-9]+)?(\/|\/([\w#!:.?+=&%!\-\/]))?/.test(e.description);return(0,i.jsxs)("div",{className:"carousel-legend",children:[(0,i.jsxs)("p",{className:"lead",children:[(0,i.jsx)("b",{children:s("Filename")})," ",e.originalName||"No Name found"]}),e.description&&(0,i.jsxs)("div",{className:"source_link",children:[(0,i.jsx)("p",{children:(0,i.jsx)("b",{children:s("Source")})}),a?(0,i.jsxs)("div",{children:[(0,i.jsx)(c.g,{icon:n.CQO,size:"1x",color:"#999",className:"me-1"}),(0,i.jsx)("a",{className:"source_link",href:e.description,target:"_blank",rel:"noopener noreferrer",children:e.description})]}):(0,i.jsx)("div",{children:(0,i.jsx)("p",{className:"ps-0 py-0",style:{wordBreak:"break-all"},children:e.description})})]}),e.downloadLink&&(0,i.jsxs)(o.A,{className:"btn btn-success mt-2 btn--download",href:e.downloadLink,children:[s("Download"),(0,i.jsx)(c.g,{icon:n.cbP,size:"1x",className:"ms-1"})]})]})};return(0,r.useEffect)(()=>{let s=[];e&&e.gallery&&Array.isArray(e.gallery)&&e.gallery.map((a,i)=>{let r,l=a&&a.name.split(".").pop();switch(l){case"JPG":case"jpg":case"jpeg":case"png":r="".concat("http://localhost:3001/api/v1","/image/show/").concat(a._id);break;case"pdf":r="/images/fileIcons/pdfFile.png";break;case"docx":r="/images/fileIcons/wordFile.png";break;case"xls":case"xlsx":r="/images/fileIcons/xlsFile.png";break;default:r="/images/fileIcons/otherFile.png"}let c=("docx"===l||"pdf"===l||"xls"===l||"xlsx"===l)&&"".concat("http://localhost:3001/api/v1","/files/download/").concat(a._id),n="".concat(a&&a.original_name?a.original_name:"No Name found"),o=e.imageSource&&Array.isArray(e.imageSource)&&e.imageSource.length>0?e.imageSource[i]:"";s.push({src:r,description:o,originalName:n,downloadLink:c})}),d(s)},[e]),(0,i.jsx)("div",{children:a&&0===a.length?(0,i.jsx)("div",{className:"border border-info my-3 mx-0",children:(0,i.jsx)("p",{className:"d-flex d-flex justify-content-center p-2 m-0",children:s("NoFilesFound!")})}):(0,i.jsx)(l.FN,{showThumbs:!0,showStatus:!0,showIndicators:!0,infiniteLoop:!0,useKeyboardArrows:!0,autoPlay:!1,stopOnHover:!0,swipeable:!0,dynamicHeight:!1,emulateTouch:!0,renderThumbs:()=>a.map((e,s)=>(0,i.jsx)("img",{src:e.src,alt:"Thumbnail ".concat(s+1),style:{width:"60px",height:"60px",objectFit:"cover"}},s)),children:a.map((e,s)=>(0,i.jsxs)("div",{children:[(0,i.jsx)("img",{src:e.src,alt:e.originalName||"Gallery image",style:{maxHeight:"500px",objectFit:"contain"}}),m(e)]},s))})})}},64610:(e,s,a)=>{(window.__NEXT_P=window.__NEXT_P||[]).push(["/operation/components/MediaGalleryAccordian",function(){return a(99238)}])},99238:(e,s,a)=>{"use strict";a.r(s),a.d(s,{default:()=>d});var i=a(37876),r=a(14232),l=a(32890),c=a(21772),n=a(11041),o=a(31753),t=a(33458);let d=e=>{let{t:s}=(0,o.Bd)("common"),[a,d]=(0,r.useState)(!1);return(0,i.jsx)(i.Fragment,{children:(0,i.jsxs)(l.A.Item,{eventKey:"0",children:[(0,i.jsxs)(l.A.Header,{onClick:()=>d(!a),children:[(0,i.jsx)("div",{className:"cardTitle",children:s("MediaGallery")}),(0,i.jsx)("div",{className:"cardArrow",children:a?(0,i.jsx)(c.g,{icon:n.EZy,color:"#fff"}):(0,i.jsx)(c.g,{icon:n.QLR,color:"#fff"})})]}),(0,i.jsx)(l.A.Body,{children:(0,i.jsx)(t.A,{gallery:e.operation.images,imageSource:e.operation.images_src})})]})})}}},e=>{var s=s=>e(e.s=s);e.O(0,[7725,1772,7126,636,6593,8792],()=>s(64610)),_N_E=e.O()}]);
//# sourceMappingURL=MediaGalleryAccordian-9fc817afccc235ce.js.map