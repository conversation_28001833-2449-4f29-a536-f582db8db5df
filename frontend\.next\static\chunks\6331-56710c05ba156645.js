"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[6331],{12613:(e,t,s)=>{s.d(t,{A:()=>r});var a=s(37876);s(14232);var i=s(56856);let r=e=>(0,a.jsx)(i.Ay,{...e})},16331:(e,t,s)=>{s.r(t),s.d(t,{default:()=>E});var a=s(37876),i=s(14232),r=s(49589),l=s(29335),n=s(56970),d=s(37784),o=s(29504),c=s(60282),u=s(888),p=s(54975),h=s(21772),x=s(11041),m=s(67814),j=s(82851),g=s.n(j),A=s(10841),y=s.n(A),v=s(48230),f=s.n(v),_=s(35611),b=s(54773),S=s(97685),w=s(89099),C=s.n(w),k=s(89673),N=s(53718),I=s(12613),D=s(43206),T=s(31753),z=s(5671);let E=e=>{let{t,i18n:s}=(0,T.Bd)("common"),j="fr"===s.language?"en":s.language,A="de"===s.language?{title_de:"asc"}:{title:"asc"},v=(0,i.useRef)(null),w=(0,i.useRef)(null),[E,O]=(0,i.useState)({invitesCountry:"",invitesRegion:[],invitesOrganisationType:"",invitesOrganisation:"",invitesExpertise:"",invitesNetWork:"",visibility:!0}),[F]=(0,i.useState)(j?"title.".concat(j):"title.en"),[L,M]=(0,i.useState)([]),[R,q]=(0,i.useState)([]),[G,P]=(0,i.useState)([]),[B,W]=(0,i.useState)([]),[H,V]=(0,i.useState)([]),[U,K]=(0,i.useState)([]),[,J]=(0,i.useState)([]),[Q,$]=(0,i.useState)(1),[X,Y]=(0,i.useState)([{institution:"",expertise:[],partner_expertise:[],organisation_status:"",work_description:"",status:""}]),[Z,ee]=(0,i.useState)([]),[et,es]=(0,i.useState)([]),[ea,ei]=(0,i.useState)([]),[er,el]=(0,i.useState)([]),[en,ed]=(0,i.useState)([]),[eo,ec]=(0,i.useState)([]),[eu,ep]=(0,i.useState)([]),[eh,ex]=(0,i.useState)(!1),[em]=(0,i.useState)(j),[ej,eg]=(0,i.useState)(!1),[eA,ey]=(0,i.useState)(null),[ev,ef]=(0,i.useState)([{timetitle:"",iconclass:"",date:null}]),[e_,eb]=(0,i.useState)(!0),eS=e.routes&&"edit"===e.routes[0]&&e.routes[1],ew={title:"",country:"",world_region:"",region:[],status:" ",hazard_type:"",hazard:[],syndrome:null,active_tab:1,start_date:null,TimeLineStartDate:new Date,end_date:null,description:"",images:[],document:[],checked:!1,images_src:[],doc_src:[]},eC={query:{},sort:A,limit:"~",languageCode:j},[ek,eN]=(0,i.useState)(ew),eI=()=>{let e=[...X];e.push({status:"",institution:"",expertise:[],organisation_status:"",work_description:"",partner_expertise:[]}),Y(e),$(e.length)},eD=(e,t)=>{X.splice(t,1);let s=[...X];Y(s),$(s.length),0===X.length&&eI()},eT=(e,t)=>{if(e.target){let{name:s,value:a}=e.target;X[t][s]=a,"organisation_status"===s&&(X[t].status=a)}else X[t].partner_expertise=e,X[t].expertise=e.map(e=>e.value);Y([...X])},ez=async e=>{let t=await N.A.get("/operation_status",e);t&&Array.isArray(t.data)&&V(t.data);let s=await N.A.get("/country",e);s&&Array.isArray(s.data)&&K(s.data);let a=await N.A.get("/hazardtype",e);a&&Array.isArray(a.data)&&ee(a.data);let i=await N.A.get("/syndrome",e);i&&Array.isArray(i.data)&&ed(i.data),e.select="-contact_name -description -type -networks -expertise -hazard_types -hazards -address -focal_points -website -telephone -twitter -header -use_default_header -images -status -email -user -created_at -updated_at -primary_focal_point",e.query.status={$ne:"Request Pending"};let r=await N.A.get("/institution",e);r&&Array.isArray(r.data)&&ep(r.data);let l=await N.A.get("/expertise",e);l&&Array.isArray(l.data)&&ec(l.data.map((e,t)=>({label:e.title,value:e._id})));let n=await N.A.get("/deploymentstatus",e);n&&Array.isArray(n.data)&&el(n.data)},eE=(e,t,s)=>(M(e.images?e.images:[]),q(e.images_src?e.images_src:[]),W(e.document?e.document:[]),P(e.doc_src?e.doc_src:[]),Y(t),eG(e.country),eP(e.hazard_type),eN(t=>({...t,...e})),s?eN(e=>({...e,checked:!0})):null),eO=(e,t,s,a)=>{a.push({institution:e.institution&&e.institution._id,partner_expertise:t,expertise:s,organisation_status:e&&e.status?e.status._id:"",status:e&&e.status?e.status._id:"",work_description:e.work_description})},eF=e=>{let{status:t,country:s,syndromes:a,region:i,hazard_type:r,hazard:l,timeline:n,start_date:d,end_date:o}=e;e.status=t&&t._id?t._id:" ",e.start_date=d?y()(d).toDate():null,e.end_date=o?y()(o).toDate():null,e.country=s&&s._id?s._id:" ",e.syndrome=a&&a._id?a._id:null,e.hazard_type=r&&r._id?r._id:"",e.hazard=l?l.map(e=>({label:e.title[em],value:e._id})):[],e.region=i?i.map(e=>({label:e.title,value:e._id})):[],n&&ef(n)},eL=async()=>{let t=await N.A.get("/operation/".concat(e.routes[1]),eC);if(t){let e=[],{end_date:s,partners:a}=t;eF(t),a&&a.forEach(t=>{let s=t.expertise&&t.expertise.map(e=>({label:e.title,value:e._id})),a=t.expertise&&t.expertise.map(e=>e._id);eO(t,s,a,e)}),eE(t,e,s)}},eM=async()=>{let e=await N.A.post("/users/getLoggedUser",{});if(e&&e.roles){var t;(null==(t=e.roles)?void 0:t.filter(e=>"EMT_NATIONAL_FOCALPOINT"==e)).length>0?eb(!1):eb(!0)}};(0,i.useEffect)(()=>{eS&&eL(),ez(eC),eM()},[]);let eR=e=>{eN(t=>({...t,...e})),O(t=>({...t,...e}))},eq=async e=>{let t=[];if(e){let s=await N.A.get("/country_region/".concat(e),eC);s&&s.data&&(t=s.data.map((e,t)=>({label:e.title,value:e._id}))).sort((e,t)=>e.label.localeCompare(t.label))}return t},eG=async e=>{es(await eq(e))},eP=async e=>{let t=[];if(e){let s=await N.A.get("/hazard_hazard_type/".concat(e),{query:{enabled:!0},sort:{[F]:"asc"},limit:"~",select:"-description -first_letter -hazard_type -picture -picture_source  -created_at -updated_at"});s&&s.data&&(t=s.data.map(e=>({label:e.title[em],value:e._id})))}ei(t)},eB=()=>{let e={timetitle:"",iconclass:"",date:null};ef(t=>[...t,e])},eW=(e,t)=>{ev.splice(t,1),ef([...ev]),0===ev.length&&eB()},eH=e=>{if(e.target){let{name:t,value:s}=e.target;if("country"===t){let t=e.target.selectedIndex;if(e.target&&t&&null!=t){let s=e.target[t].getAttribute("data-worldregion");eN(e=>({...e,world_region:s}))}}eN(e=>({...e,[t]:s})),"country"===t&&(eG(s),eR({region:[]})),"hazard_type"===t&&(eP(s),eR({hazard:[]}))}else eN(t=>({...t,region:e}))};i.useEffect(()=>{if(E){let e={};Object.keys(E).forEach((t,s)=>{let a=E[t].length>0&&E[t].map(e=>e.value);e[t]=a||[]}),eV(e)}},[E]);let eV=async e=>{let{invitesCountry:t,invitesOrganisation:s}=e,a=await N.A.post("/user-invite",{query:{country:t,institution:s}});a&&Array.isArray(a)&&J(a.map((e,t)=>({label:e.username,value:e._id})))},eU=(e,t)=>{let{name:s,value:a}=e.target,i=[...ev];i[t][s]=a,ef(i)},eK=(e,t)=>{"start_date"===t&&null==e||"start_date"===t?eN(t=>({...t,end_date:null,start_date:e})):eN(s=>({...s,[t]:e}))},eJ=(e,t)=>{ev[t].date=e,ef([...ev])},eQ=e=>{eN(t=>({...t,description:e}))},e$=async(s,a,i)=>{let r,l;s&&s.preventDefault&&s.preventDefault(),w.current&&w.current.setAttribute("disabled","disabled");let n=g().map(X,g().partialRight(g().pick,["institution","expertise","status","work_description"])),d={title:ek.title.trim(),description:ek.description,status:ek.status,start_date:ek.start_date,end_date:ek.end_date,country:ek.country,world_region:ek.world_region,syndrome:ek.syndrome,hazard_type:ek.hazard_type,hazard:ek.hazard?ek.hazard.map((e,t)=>e.value):[],region:ek.region?ek.region.map((e,t)=>e.value):[],timeline:ev.length>0&&""!==ev[0].timetitle?ev:[],partners:n,images:ek.images,images_src:ek.images_src,document:ek.document,doc_src:ek.doc_src};eS?(l="toast.Operationupdatedsuccessfully",r=await N.A.patch("/operation/".concat(e.routes[1]),d)):(l="toast.Operationaddedsuccessfully",r=await N.A.post("/operation",d)),r&&r._id?eh?(ey((null==r?void 0:r._id)&&r._id),eg(!0)):(S.Ay.success(t(l)),C().push("/operation/[...routes]","/operation/show/".concat(r._id))):(l=eS?"toast.OperationNotupdatedsuccessfully":"toast.OperationNotaddedsuccessfully",S.Ay.error(t(l))),w.current&&w.current.removeAttribute("disabled")},eX=e=>{let t=[],s=[];e.length>0&&e.map(e=>{e.type&&(e.type.includes("pdf")||e.type.includes("docx")||e.type.includes("xlsx")||e.type.includes("xls"))?s.push(e.serverID):t.push(e.serverID)}),eN(e=>({...e,images:t})),eN(e=>({...e,document:s}))},eY=e=>{eN(t=>({...t,images_src:e}))},eZ=e=>{eN(t=>({...t,doc_src:e}))};return(0,a.jsxs)(r.A,{className:"formCard",fluid:!0,children:[(0,a.jsx)(l.A,{children:(0,a.jsx)(b.A,{onSubmit:e$,ref:v,onErrorSubmit:e=>{let t=-1,s=g().map(X,g().partialRight(g().pick,["institution","expertise","status","work_description"])),a=0;for(let e in s){if(""===s[e].institution||""===s[e].status){t=a;break}a++}if(t>-1){$(t+1);let e=document.getElementById("btnAddForm");null==e||e.scrollIntoView()}},initialValues:ek,enableReinitialize:!0,children:(0,a.jsxs)(l.A.Body,{children:[(0,a.jsx)(n.A,{children:(0,a.jsx)(d.A,{children:(0,a.jsx)(l.A.Title,{children:eS?t("editOperation"):t("addOperation")})})}),(0,a.jsx)("hr",{}),(0,a.jsxs)(n.A,{className:"mb-3",children:[(0,a.jsx)(d.A,{md:!0,lg:6,sm:12,children:(0,a.jsxs)(o.A.Group,{children:[(0,a.jsx)(o.A.Label,{className:"required-field",children:t("CountryOrTerritory")}),(0,a.jsxs)(_.s3,{name:"country",id:"country",value:ek.country,onChange:eH,required:!0,errorMessage:t("thisfieldisrequired"),children:[(0,a.jsx)("option",{value:"",children:t("SelectCountry")}),U.map((e,t)=>(0,a.jsx)("option",{"data-worldregion":e.world_region._id,value:e._id,children:e.title},t))]})]})}),(0,a.jsx)(d.A,{md:!0,lg:6,sm:12,children:(0,a.jsxs)(o.A.Group,{style:{maxWidth:"600px"},children:[(0,a.jsx)(o.A.Label,{children:t("CountryRegions")}),(0,a.jsx)(m.KF,{overrideStrings:{selectSomeItems:t("SelectRegions"),allItemsAreSelected:"All Regions are Selected"},options:et,value:ek.region,onChange:eH,className:"region",labelledBy:"Select Country Regions"})]})})]}),(0,a.jsxs)(n.A,{className:"mb-3",children:[(0,a.jsx)(d.A,{md:!0,lg:4,sm:12,children:(0,a.jsxs)(o.A.Group,{children:[(0,a.jsx)(o.A.Label,{className:"required-field",children:t("HazardType")}),(0,a.jsxs)(_.s3,{name:"hazard_type",id:"hazard_type",value:ek.hazard_type,onChange:eH,required:!0,errorMessage:t("thisfieldisrequired"),children:[(0,a.jsx)("option",{value:"",children:t("SelectHazardType")}),Z.map((e,t)=>(0,a.jsx)("option",{value:e._id,children:e.title},t))]})]})}),(0,a.jsx)(d.A,{md:!0,lg:4,sm:12,children:(0,a.jsxs)(o.A.Group,{children:[(0,a.jsx)(o.A.Label,{children:t("Hazard")}),(0,a.jsx)(o.A.Group,{children:(0,a.jsx)(m.KF,{overrideStrings:{selectSomeItems:t("SelectHazard"),allItemsAreSelected:"All Hazards are Selected"},options:ea,value:ek.hazard,onChange:e=>{eN(t=>({...t,hazard:e}))},className:"hazard",labelledBy:"Select Hazards"})})]})}),(0,a.jsx)(d.A,{md:!0,lg:4,sm:12,children:(0,a.jsxs)(o.A.Group,{children:[(0,a.jsx)(o.A.Label,{children:t("Syndrome")}),(0,a.jsxs)(_.s3,{name:"syndrome",id:"syndrome",value:ek.syndrome,onChange:eH,children:[(0,a.jsx)("option",{value:"",children:t("SelectSyndrome")}),en.map((e,t)=>(0,a.jsx)("option",{value:e._id,children:e.title},t))]})]})})]}),(0,a.jsx)(n.A,{className:"mb-3",children:(0,a.jsx)(d.A,{children:(0,a.jsxs)(o.A.Group,{children:[(0,a.jsx)(o.A.Label,{className:"required-field",children:t("Title")}),(0,a.jsx)(_.ks,{name:"title",id:"title",required:!0,value:ek.title,validator:e=>""!==e.trim(),errorMessage:{validator:t("PleaseAddtheTitle")},onChange:eH})]})})}),(0,a.jsx)(n.A,{className:"mb-3",children:(0,a.jsx)(d.A,{children:(0,a.jsxs)(o.A.Group,{children:[(0,a.jsx)(o.A.Label,{children:t("Description")}),(0,a.jsx)(z.x,{initContent:ek.description,onChange:e=>eQ(e)})]})})}),(0,a.jsxs)(n.A,{className:"mb-3",children:[H.length?(0,a.jsx)(d.A,{md:!0,lg:3,sm:12,children:(0,a.jsxs)(o.A.Group,{children:[(0,a.jsx)(o.A.Label,{className:"required-field",children:t("OperationStatus")}),(0,a.jsxs)(_.s3,{name:"status",id:"status",value:ek.status,onChange:eH,required:!0,errorMessage:t("thisfieldisrequired"),children:[(0,a.jsx)("option",{value:"",children:t("SelectOperationStatus")}),H.map((e,t)=>(0,a.jsx)("option",{value:e._id,children:e.title},t))]})]})}):null,(0,a.jsx)(d.A,{md:!0,lg:3,sm:12,children:(0,a.jsxs)(o.A.Group,{children:[(0,a.jsx)(n.A,{children:(0,a.jsx)(d.A,{children:(0,a.jsx)(o.A.Label,{children:t("OperationStartDate")})})}),(0,a.jsx)(I.A,{selected:ek.start_date,onChange:e=>eK(e,"start_date"),dateFormat:"MMMM d, yyyy",placeholderText:t("SelectStartDate")})]})}),(0,a.jsx)(d.A,{md:!0,lg:2,sm:12,children:(0,a.jsx)(o.A.Check,{type:"checkbox",checked:ek.checked,onChange:()=>{eN(e=>({...e,checked:!e.checked,end_date:null}))},label:t("ShowEndDate")})}),ek.checked&&(0,a.jsx)(d.A,{md:!0,lg:3,sm:12,children:(0,a.jsxs)(o.A.Group,{children:[(0,a.jsx)(n.A,{children:(0,a.jsx)(d.A,{children:(0,a.jsx)(o.A.Label,{children:t("OperationEndDate")})})}),(0,a.jsx)(I.A,{selected:ek.end_date,disabled:!ek.start_date,onChange:e=>eK(e,"end_date"),dateFormat:"MMMM d, yyyy",minDate:ek.start_date,placeholderText:t("SelectEndDate")})]})})]}),(0,a.jsx)(l.A.Text,{children:(0,a.jsx)("b",{children:t("Timeline")})}),(0,a.jsx)("hr",{}),ev.map((e,s)=>(0,a.jsx)("div",{children:(0,a.jsxs)(n.A,{children:[(0,a.jsx)(d.A,{md:4,children:(0,a.jsxs)(o.A.Group,{children:[(0,a.jsx)(o.A.Label,{children:t("Title")}),(0,a.jsx)(_.ks,{name:"timetitle",id:"timetitle",value:e.timetitle,onChange:e=>eU(e,s)})]})}),(0,a.jsx)(d.A,{md:3,children:(0,a.jsxs)(o.A.Group,{children:[(0,a.jsx)(o.A.Label,{children:t("IconClass")}),(0,a.jsxs)(_.s3,{name:"iconclass",id:"iconclass",value:e.iconclass,onChange:e=>eU(e,s),children:[(0,a.jsx)("option",{value:"-1",children:"-Select-"}),(0,a.jsx)("option",{value:"1",children:"RFA"}),(0,a.jsx)("option",{value:"2",children:"Alert"}),(0,a.jsx)("option",{value:"3",children:"Mission to Country"}),(0,a.jsx)("option",{value:"4",children:"Calendar Event"}),(0,a.jsx)("option",{value:"5",children:"Documents"}),(0,a.jsx)("option",{value:"6",children:"Meeting"}),(0,a.jsx)("option",{value:"7",children:"Others"})]})]})}),(0,a.jsx)(d.A,{md:3,children:(0,a.jsxs)(o.A.Group,{children:[(0,a.jsx)(n.A,{children:(0,a.jsx)(d.A,{children:(0,a.jsx)(o.A.Label,{children:t("StartDate")})})}),(0,a.jsx)(I.A,{selected:e.date?y()(e.date).toDate():null,onChange:e=>eJ(e,s),dateFormat:"MMMM d, yyyy",placeholderText:t("SelectStartDate")})]})}),(0,a.jsx)(d.A,{md:2,className:"text-md-center",children:(0,a.jsx)(o.A.Group,{children:0===s?(0,a.jsx)("div",{}):(0,a.jsx)(c.A,{variant:"secondary",style:{marginTop:"30px"},onClick:e=>eW(e,s),children:t("Remove")})})})]})},s)),(0,a.jsx)(n.A,{children:(0,a.jsx)(d.A,{md:!0,lg:"4",children:(0,a.jsx)(c.A,{id:"btnAddForm",variant:"secondary",style:{marginTop:"27px",marginBottom:"20px"},onClick:eB,children:t("ADDANOTHERITEM")})})}),(0,a.jsx)(n.A,{className:"mt-1",children:(0,a.jsxs)(d.A,{children:[(0,a.jsx)(l.A.Text,{children:(0,a.jsx)("b",{children:t("AddOrganisation(s)")})}),(0,a.jsx)("hr",{})]})}),(0,a.jsx)(n.A,{className:"mb-3",children:(0,a.jsx)(d.A,{children:(0,a.jsx)(o.A.Group,{children:(0,a.jsxs)(u.A,{activeKey:Q,onSelect:e=>$(e),id:"uncontrolled-tab-example",children:[" ",X.map((e,s)=>(0,a.jsxs)(p.A,{eventKey:"".concat(s+1),title:"Organisation ".concat(s+1),children:[(0,a.jsxs)(n.A,{className:"mb-3",children:[(0,a.jsx)(d.A,{md:4,children:(0,a.jsxs)(o.A.Group,{style:{paddingTop:"20px"},children:[(0,a.jsx)(o.A.Label,{className:"required-field",children:t("NameofAssociatedOrganisation")}),(0,a.jsxs)(_.s3,{name:"institution",id:"institution",value:e.institution,onChange:e=>eT(e,s),required:!0,errorMessage:t("thisfieldisrequired"),children:[(0,a.jsx)("option",{value:"",children:t("SelectOrganisation")}),eu.map((e,t)=>(0,a.jsx)("option",{value:e._id,children:e.title},t))]})]})}),(0,a.jsx)(d.A,{md:4,children:(0,a.jsxs)(o.A.Group,{style:{paddingTop:"20px",maxWidth:"400px"},children:[(0,a.jsx)(o.A.Label,{children:t("WorkingExpertise")}),(0,a.jsx)(m.KF,{overrideStrings:{selectSomeItems:t("ChooseExpertise"),allItemsAreSelected:"All Expertise are Selected"},onChange:e=>eT(e,s),options:eo,value:e.partner_expertise,className:"work-expert",labelledBy:"Select Expertise"})]})}),(0,a.jsx)(d.A,{md:4,children:(0,a.jsxs)(o.A.Group,{style:{paddingTop:"20px"},children:[(0,a.jsx)(o.A.Label,{className:"required-field",children:t("Status")}),(0,a.jsxs)(_.s3,{name:"organisation_status",id:"organisation_status",value:e.status,onChange:e=>eT(e,s),required:!0,errorMessage:t("thisfieldisrequired"),children:[(0,a.jsx)("option",{value:"",children:t("SelectStatus")}),er.map((e,t)=>(0,a.jsx)("option",{value:e._id,children:e.title},t))]})]})})]}),(0,a.jsx)(n.A,{children:(0,a.jsx)(d.A,{children:(0,a.jsxs)(o.A.Group,{children:[(0,a.jsx)(o.A.Label,{children:t("Description")}),(0,a.jsx)(z.x,{initContent:e.work_description,onChange:e=>(function(e,t){let s=[...X];s[t].work_description=e,Y(s)})(e,s)})]})})}),(0,a.jsx)("div",{children:0===s?(0,a.jsx)("span",{}):(0,a.jsx)(d.A,{xs:!0,lg:"4",children:(0,a.jsx)(c.A,{onSelect:e=>$(e),variant:"secondary",onClick:e=>eD(e,s),children:t("Remove")})})})]},s)),(0,a.jsx)(p.A,{title:(0,a.jsx)("div",{children:(0,a.jsx)("span",{onClick:eI,children:(0,a.jsx)(h.g,{icon:x.QLR,color:"#808080"})})})})]})})})}),(0,a.jsxs)(d.A,{className:"px-0",children:[(0,a.jsx)(l.A.Text,{children:(0,a.jsx)("b",{children:t("MediaGallery")})}),(0,a.jsx)("hr",{})]}),(0,a.jsx)(n.A,{children:(0,a.jsx)(d.A,{children:(0,a.jsx)(k.A,{datas:L,srcText:R,getImgID:e=>eX(e),getImageSource:e=>eY(e)})})}),(0,a.jsxs)(d.A,{className:"px-0 mt-4",children:[(0,a.jsx)(l.A.Text,{children:(0,a.jsx)("b",{children:t("Documents")})}),(0,a.jsx)("hr",{})]}),(0,a.jsx)(n.A,{children:(0,a.jsx)(d.A,{children:(0,a.jsx)(k.A,{type:"application",datas:B,srcText:G,getImgID:e=>eX(e),getImageSource:e=>eZ(e)})})}),(0,a.jsx)(n.A,{className:"mt-4",children:(0,a.jsxs)(d.A,{children:[(0,a.jsx)(l.A.Text,{children:(0,a.jsx)("b",{children:t("VirtualSpace")})}),(0,a.jsx)("hr",{}),(0,a.jsx)(o.A.Check,{className:"p-0",type:"checkbox",disabled:!e_,onChange:()=>ex(!eh),name:"virtula",checked:eh,label:t("WouldliketocreateaVirtualSpace")})]})}),(0,a.jsx)(n.A,{className:"my-4",children:(0,a.jsxs)(d.A,{children:[(0,a.jsx)(c.A,{className:"me-2",type:"submit",variant:"primary",ref:w,onClick:e$,children:t("submit")}),(0,a.jsx)(c.A,{className:"me-2",onClick:()=>{eN(ew),M([]),q([]),W([]),P([]),ep([]),ef([]),Y([]),window.scrollTo(0,0)},variant:"info",children:t("reset")}),(0,a.jsx)(f(),{href:"/operation",as:"/operation",children:(0,a.jsx)(c.A,{variant:"secondary",children:t("Cancel")})})]})})]})})}),ej&&(0,a.jsx)(D.A,{type:"Operation",id:eA})]})}},35611:(e,t,s)=>{s.d(t,{sx:()=>o,s3:()=>i.s3,ks:()=>i.ks,yk:()=>a.A});var a=s(54773),i=s(59200),r=s(37876),l=s(14232),n=s(39593),d=s(29504);let o={RadioGroup:e=>{let{name:t,valueSelected:s,onChange:a,errorMessage:i,children:d}=e,{errors:o,touched:c}=(0,n.j7)(),u=c[t]&&o[t];l.useMemo(()=>({name:t}),[t]);let p=l.Children.map(d,e=>l.isValidElement(e)&&function(e){return"object"==typeof e&&null!==e}(e.props)?l.cloneElement(e,{name:t,...e.props}):e);return(0,r.jsxs)("div",{children:[(0,r.jsx)("div",{className:"radio-group",children:p}),u&&(0,r.jsx)("div",{className:"invalid-feedback d-block",children:i||("string"==typeof o[t]?o[t]:String(o[t]))})]})},RadioItem:e=>{let{id:t,label:s,value:a,name:i,disabled:l}=e,{values:o,setFieldValue:c}=(0,n.j7)(),u=i||t;return(0,r.jsx)(d.A.Check,{type:"radio",id:t,label:s,value:a,name:u,checked:o[u]===a,onChange:e=>{c(u,e.target.value)},disabled:l,inline:!0})}};a.A,i.ks,i.s3},43206:(e,t,s)=>{s.d(t,{A:()=>u});var a=s(37876),i=s(14232),r=s(31195),l=s(21772),n=s(11041),d=s(89099),o=s.n(d),c=s(31753);let u=e=>{let{type:t,id:s}=e,[d,u]=(0,i.useState)(5),{t:p}=(0,c.Bd)("common");return(0,i.useEffect)(()=>{d>0?setTimeout(()=>u(d-1),1e3):o().push({pathname:"/vspace/create",query:{id:s,source:t}})}),(0,a.jsxs)(r.A,{show:!0,children:[(0,a.jsx)("div",{className:"modal--align mt-2",children:(0,a.jsx)("div",{className:"modal--icon",children:(0,a.jsx)(l.g,{icon:n.e68,color:"#4dc724",size:"4x"})})}),(0,a.jsx)("div",{className:"text-center mt-4",children:(0,a.jsxs)("p",{className:"lead",children:[t," ",p("vspace.formSubmittedSuccessfully"),(0,a.jsx)("br",{}),(0,a.jsx)("small",{children:p("vspace.vspaceCreationRedirect")}),(0,a.jsx)("br",{}),(0,a.jsx)("small",{children:(0,a.jsxs)("b",{children:[" ",p("vspace.waitFor")," ",d," ",p("vspace.waitForSec")]})})]})})]})}},54773:(e,t,s)=>{s.d(t,{A:()=>d});var a=s(37876),i=s(14232),r=s(39593),l=s(91408);let n=(0,i.forwardRef)((e,t)=>{let{children:s,onSubmit:i,autoComplete:n,className:d,onKeyPress:o,initialValues:c,...u}=e,p=l.Ik().shape({});return(0,a.jsx)(r.l1,{initialValues:c||{},validationSchema:p,onSubmit:(e,t)=>{let s={preventDefault:()=>{},stopPropagation:()=>{},currentTarget:null,target:null,nativeEvent:new Event("submit"),bubbles:!1,cancelable:!0,defaultPrevented:!1,eventPhase:0,isTrusted:!1,timeStamp:Date.now(),type:"submit",isDefaultPrevented:()=>!1,isPropagationStopped:()=>!1,persist:()=>{}};i&&i(s,e,t)},...u,children:e=>(0,a.jsx)(r.lV,{ref:t,onSubmit:e.handleSubmit,autoComplete:n,className:d,onKeyPress:o,children:"function"==typeof s?s(e):s})})});n.displayName="ValidationFormWrapper";let d=n},59200:(e,t,s)=>{s.d(t,{ks:()=>l,s3:()=>n});var a=s(37876);s(14232);var i=s(29504),r=s(39593);let l=e=>{let{name:t,id:s,required:l,validator:n,errorMessage:d,onChange:o,value:c,as:u,multiline:p,rows:h,pattern:x,...m}=e;return(0,a.jsx)(r.D0,{name:t,validate:e=>{let t="string"==typeof e?e:String(e||"");return l&&(!e||""===t.trim())?(null==d?void 0:d.validator)||"This field is required":n&&!n(e)?(null==d?void 0:d.validator)||"Invalid value":x&&e&&!new RegExp(x).test(e)?(null==d?void 0:d.pattern)||"Invalid format":void 0},children:e=>{let{field:t,meta:r}=e;return(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(i.A.Control,{...t,...m,id:s,as:u||"input",rows:h,isInvalid:r.touched&&!!r.error,onChange:e=>{t.onChange(e),o&&o(e)},value:void 0!==c?c:t.value}),r.touched&&r.error?(0,a.jsx)(i.A.Control.Feedback,{type:"invalid",children:r.error}):null]})}})},n=e=>{let{name:t,id:s,required:l,errorMessage:n,onChange:d,value:o,children:c,...u}=e;return(0,a.jsx)(r.D0,{name:t,validate:e=>{if(l&&(!e||""===e))return(null==n?void 0:n.validator)||"This field is required"},children:e=>{let{field:t,meta:r}=e;return(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(i.A.Control,{as:"select",...t,...u,id:s,isInvalid:r.touched&&!!r.error,onChange:e=>{t.onChange(e),d&&d(e)},value:void 0!==o?o:t.value,children:c}),r.touched&&r.error?(0,a.jsx)(i.A.Control.Feedback,{type:"invalid",children:r.error}):null]})}})}},89673:(e,t,s)=>{s.d(t,{A:()=>_});var a=s(37876),i=s(14232),r=s(17336),l=s(21772),n=s(11041),d=s(37784),o=s(29504),c=s(60282),u=s(31195),p=s(82851),h=s.n(p),x=s(97685),m=s(53718),j=s(31753);let g=[],A={flex:1,display:"flex",flexDirection:"column",alignItems:"center",justifyContent:"center",width:"100%",height:"100%",borderWidth:.1,borderColor:"#fafafa",backgroundColor:"#fafafa",color:"black",transition:"border  .24s ease-in-out",padding:"15px"},y={display:"flex",padding:"10px",width:"100%",border:"2px solid gray",flexDirection:"column",justifyContent:"flex-start",flexWrap:"wrap",marginTop:20},v={width:"150px"},f={borderColor:"#2196f3"},_=e=>{let t,{t:s}=(0,j.Bd)("common"),[p,_]=(0,i.useState)(!1),[b,S]=(0,i.useState)(),w="application"==e.type?0x1400000:"20971520",[C,k]=(0,i.useState)([]),[N,I]=(0,i.useState)(!0),[D,T]=(0,i.useState)([]),z=e&&"application"===e.type?"/files":"/image",E=async e=>{await m.A.remove("".concat(z,"/").concat(e))},O=e=>{S(e),_(!0)},F=(e,t)=>{let s=[...D];s[t]=e.target.value,T(s)},L=t=>{switch(t&&t.name.split(".").pop()){case"JPG":case"jpg":case"jpeg":case"jpg":case"png":return(0,a.jsx)("img",{src:t.preview,style:v});case"pdf":return(0,a.jsx)("img",{src:"/images/fileIcons/pdfFile.png",className:"application"===e.type?"docPreview":"imgPreview"});case"docx":default:return(0,a.jsx)("img",{src:"/images/fileIcons/wordFile.png",className:"application"===e.type?"docPreview":"imgPreview"});case"xls":case"xlsx":return(0,a.jsx)("img",{src:"/images/fileIcons/xlsFile.png",className:"application"===e.type?"docPreview":"imgPreview"})}},M=()=>_(!1),R=()=>{_(!1)},q=t=>{let s=(t=b)&&t._id?{serverID:t._id}:{file:t},a=h().findIndex(g,s),i=[...D];i.splice(a,1),T(i),E(g[a].serverID),g.splice(a,1),e.getImgID(g,e.index?e.index:0);let r=[...C];r.splice(r.indexOf(t),1),k(r),_(!1)},G=C.map((t,i)=>(0,a.jsxs)("div",{children:[(0,a.jsx)(d.A,{xs:12,children:(0,a.jsxs)("div",{className:"row",children:[(0,a.jsx)(d.A,{md:4,lg:3,className:"application text-center align-self-center"===e.type?"docImagePreview text-center align-self-center":"imgPreview text-center align-self-center",children:L(t)}),(0,a.jsx)(d.A,{md:5,lg:7,className:"align-self-center",children:(0,a.jsxs)(o.A,{children:[(0,a.jsxs)(o.A.Group,{controlId:"filename",children:[(0,a.jsx)(o.A.Label,{className:"mt-2",children:s("FileName")}),(0,a.jsx)(o.A.Control,{size:"sm",type:"text",disabled:!0,value:t.original_name?t.original_name:t.name})]}),(0,a.jsxs)(o.A.Group,{controlId:"description",children:[(0,a.jsx)(o.A.Label,{children:"application"===e.type?s("ShortDescription/(Max255Characters)"):s("Source/Description")}),(0,a.jsx)(o.A.Control,{maxLength:"application"===e.type?255:void 0,size:"sm",type:"text",placeholder:"application"===e.type?s("`Enteryourdocumentdescription`"):s("`Enteryourimagesource/description`"),value:D[i],onChange:e=>F(e,i)})]})]})}),(0,a.jsx)(d.A,{md:3,lg:2,className:"align-self-center text-center",onClick:()=>O(t),children:(0,a.jsx)(c.A,{variant:"dark",children:s("Remove")})})]})}),(0,a.jsxs)(u.A,{show:p,onHide:M,children:[(0,a.jsx)(u.A.Header,{closeButton:!0,children:(0,a.jsx)(u.A.Title,{children:s("DeleteFile")})}),(0,a.jsx)(u.A.Body,{children:s("Areyousurewanttodeletethisfile?")}),(0,a.jsxs)(u.A.Footer,{children:[(0,a.jsx)(c.A,{variant:"secondary",onClick:R,children:s("Cancel")}),(0,a.jsx)(c.A,{variant:"primary",onClick:()=>q(t),children:s("yes")})]})]})]},i));(0,i.useEffect)(()=>{C.forEach(e=>URL.revokeObjectURL(e.preview)),g=[]},[]),(0,i.useEffect)(()=>{e.getImageSource(D)},[D]),(0,i.useEffect)(()=>{T(e.srcText)},[e.srcText]),(0,i.useEffect)(()=>{e&&"true"===e.singleUpload&&I(!1),e&&e.datas&&k([...e.datas.map((t,s)=>(g.push({serverID:t._id,index:e.index?e.index:0,type:t.name.split(".")[1]}),{...t,preview:"".concat("http://localhost:3001/api/v1","/image/show/").concat(t._id)}))])},[e.datas]);let P=async(t,s)=>{if(t.length>s)try{let a=new FormData;a.append("file",t[s]);let i=await m.A.post(z,a,{"Content-Type":"multipart/form-data"});g.push({serverID:i._id,file:t[s],index:e.index?e.index:0,type:t[s].name.split(".")[1]}),P(t,s+1)}catch(e){P(t,s+1)}else e.getImgID(g,e.index?e.index:0)},B=(0,i.useCallback)(async e=>{await P(e,0);let t=e.map(e=>Object.assign(e,{preview:URL.createObjectURL(e)}));N?k(e=>[...e,...t]):k([...t])},[]),{getRootProps:W,getInputProps:H,isDragActive:V,isDragAccept:U,isDragReject:K,fileRejections:J}=(0,r.VB)({accept:e&&e.type?"application/pdf, application/vnd.openxmlformats-officedocument.wordprocessingml.document, application/vnd.oasis.opendocument.text,application/vnd.openxmlformats-officedocument.spreadsheetml.sheet,text/csv":"image/*",multiple:N,minSize:0,maxSize:w,onDrop:B,validator:function(e){if("/image"===z){if("image"!==e.type.substring(0,5))return x.Ay.error(s("toast.filetypenotsupport")),{code:"file-invalid-type",message:"File type not supported"}}else if("/files"===z&&"image"===e.type.substring(0,5))return x.Ay.error(s("toast.filetypenotsupport")),{code:"file-invalid-type",message:"File type not supported"};return null}}),Q=(0,i.useMemo)(()=>({...A,...V?f:{outline:"2px dashed #bbb"},...U?{outline:"2px dashed #595959"}:{outline:"2px dashed #bbb"},...K?{outline:"2px dashed red"}:{activeStyle:f}}),[V,K]);t=e&&"application"===e.type?(0,a.jsx)("small",{style:{color:"#595959"},children:s("DocumentWeSupport")}):(0,a.jsx)("small",{style:{color:"#595959"},children:s("ImageWeSupport")});let $=J.length>0&&J[0].file.size>w;return(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)("div",{className:" d-flex justify-content-center align-items-center mt-3",style:{width:"100%",height:"180px"},children:(0,a.jsxs)("div",{...W({style:Q}),children:[(0,a.jsx)("input",{...H()}),(0,a.jsx)(l.g,{icon:n.rOd,size:"4x",color:"#999"}),(0,a.jsx)("p",{style:{color:"#595959",marginBottom:"0px"},children:s("Drag'n'dropsomefileshere,orclicktoselectfiles")}),!N&&(0,a.jsxs)("small",{style:{color:"#595959"},children:[(0,a.jsx)("b",{children:"Note:"})," One single image will be accepted"]}),t,(e.type,$&&(0,a.jsxs)("small",{className:"text-danger mt-2",children:[(0,a.jsx)(l.g,{icon:n.tUE,size:"1x",color:"red"})," ",s("FileistoolargeItshouldbelessthan20MB")]})),K&&(0,a.jsxs)("small",{className:"text-danger",style:{color:"#595959"},children:[(0,a.jsx)(l.g,{icon:n.tUE,size:"1x",color:"red"})," ",s("Filetypenotacceptedsorr")]})]})}),C.length>0&&(0,a.jsx)("div",{style:y,children:G})]})}}}]);
//# sourceMappingURL=6331-56710c05ba156645.js.map