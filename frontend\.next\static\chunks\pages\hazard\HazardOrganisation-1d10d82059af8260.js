(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[6330],{29335:(e,a,r)=>{"use strict";r.d(a,{A:()=>C});var s=r(15039),t=r.n(s),d=r(14232),l=r(77346),c=r(37876);let i=d.forwardRef((e,a)=>{let{className:r,bsPrefix:s,as:d="div",...i}=e;return s=(0,l.oU)(s,"card-body"),(0,c.jsx)(d,{ref:a,className:t()(r,s),...i})});i.displayName="CardBody";let n=d.forwardRef((e,a)=>{let{className:r,bsPrefix:s,as:d="div",...i}=e;return s=(0,l.oU)(s,"card-footer"),(0,c.jsx)(d,{ref:a,className:t()(r,s),...i})});n.displayName="CardFooter";var o=r(81764);let m=d.forwardRef((e,a)=>{let{bsPrefix:r,className:s,as:i="div",...n}=e,m=(0,l.oU)(r,"card-header"),f=(0,d.useMemo)(()=>({cardHeaderBsPrefix:m}),[m]);return(0,c.jsx)(o.A.Provider,{value:f,children:(0,c.jsx)(i,{ref:a,...n,className:t()(s,m)})})});m.displayName="CardHeader";let f=d.forwardRef((e,a)=>{let{bsPrefix:r,className:s,variant:d,as:i="img",...n}=e,o=(0,l.oU)(r,"card-img");return(0,c.jsx)(i,{ref:a,className:t()(d?"".concat(o,"-").concat(d):o,s),...n})});f.displayName="CardImg";let u=d.forwardRef((e,a)=>{let{className:r,bsPrefix:s,as:d="div",...i}=e;return s=(0,l.oU)(s,"card-img-overlay"),(0,c.jsx)(d,{ref:a,className:t()(r,s),...i})});u.displayName="CardImgOverlay";let N=d.forwardRef((e,a)=>{let{className:r,bsPrefix:s,as:d="a",...i}=e;return s=(0,l.oU)(s,"card-link"),(0,c.jsx)(d,{ref:a,className:t()(r,s),...i})});N.displayName="CardLink";var x=r(46052);let h=(0,x.A)("h6"),j=d.forwardRef((e,a)=>{let{className:r,bsPrefix:s,as:d=h,...i}=e;return s=(0,l.oU)(s,"card-subtitle"),(0,c.jsx)(d,{ref:a,className:t()(r,s),...i})});j.displayName="CardSubtitle";let y=d.forwardRef((e,a)=>{let{className:r,bsPrefix:s,as:d="p",...i}=e;return s=(0,l.oU)(s,"card-text"),(0,c.jsx)(d,{ref:a,className:t()(r,s),...i})});y.displayName="CardText";let p=(0,x.A)("h5"),w=d.forwardRef((e,a)=>{let{className:r,bsPrefix:s,as:d=p,...i}=e;return s=(0,l.oU)(s,"card-title"),(0,c.jsx)(d,{ref:a,className:t()(r,s),...i})});w.displayName="CardTitle";let v=d.forwardRef((e,a)=>{let{bsPrefix:r,className:s,bg:d,text:n,border:o,body:m=!1,children:f,as:u="div",...N}=e,x=(0,l.oU)(r,"card");return(0,c.jsx)(u,{ref:a,...N,className:t()(s,x,d&&"bg-".concat(d),n&&"text-".concat(n),o&&"border-".concat(o)),children:m?(0,c.jsx)(i,{children:f}):f})});v.displayName="Card";let C=Object.assign(v,{Img:f,Title:w,Subtitle:j,Body:i,Link:N,Text:y,Header:m,Footer:n,ImgOverlay:u})},44340:(e,a,r)=>{"use strict";r.r(a),r.d(a,{default:()=>i});var s=r(37876),t=r(29335),d=r(48230),l=r.n(d),c=r(31753);let i=e=>{let a=e.hazardInstitutionData,{t:r}=(0,c.Bd)("common");return(0,s.jsx)(s.Fragment,{children:(0,s.jsx)("div",{className:"rki-carousel-card",children:(0,s.jsxs)(t.A,{className:"infoCard",children:[(0,s.jsx)(t.A.Header,{className:"text-center",children:r("hazardshow.organisations")}),(0,s.jsx)(t.A.Body,{className:"hazardBody",children:a&&a.length>0?a.map((e,a)=>(0,s.jsx)("ul",{className:"ulItems",children:(0,s.jsxs)("li",{className:"liItems",children:[(0,s.jsx)(l(),{href:"/institution/[...routes]",as:"/institution/show/".concat(e._id),children:e&&e.title?"".concat(e.title):""}),(0,s.jsxs)("span",{children:[" ","(",e&&e.address&&e.address.country?"".concat(e.address.country.title):"",")"]})]},a)})):(0,s.jsx)("span",{className:"text-center",children:r("noRecordFound")})})]})})})}},81764:(e,a,r)=>{"use strict";r.d(a,{A:()=>t});let s=r(14232).createContext(null);s.displayName="CardHeaderContext";let t=s},87620:(e,a,r)=>{(window.__NEXT_P=window.__NEXT_P||[]).push(["/hazard/HazardOrganisation",function(){return r(44340)}])}},e=>{var a=a=>e(e.s=a);e.O(0,[636,6593,8792],()=>a(87620)),_N_E=e.O()}]);
//# sourceMappingURL=HazardOrganisation-1d10d82059af8260.js.map