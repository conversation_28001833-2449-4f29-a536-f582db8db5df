// Warning suppression script for development
// This script helps suppress console warnings during development

const originalConsoleWarn = console.warn;
const originalConsoleError = console.error;

// List of warnings to suppress
const suppressedWarnings = [
  'Deprecation The legacy JS API is deprecated',
  'Using / for division outside of calc() is deprecated',
  'redux-persist failed to create sync storage',
  'You are using legacy implementation',
  'Fast Refresh had to perform a full reload',
  'sass-loader',
  'css-loader',
  'postcss-loader',
  'resolve-url-loader'
];

// Function to check if a message should be suppressed
function shouldSuppress(message) {
  if (typeof message !== 'string') return false;
  
  return suppressedWarnings.some(warning => 
    message.includes(warning)
  );
}

// Override console.warn
console.warn = function(...args) {
  const message = args.join(' ');
  if (!shouldSuppress(message)) {
    originalConsoleWarn.apply(console, args);
  }
};

// Override console.error for specific warnings that come as errors
console.error = function(...args) {
  const message = args.join(' ');
  if (!shouldSuppress(message)) {
    originalConsoleError.apply(console, args);
  }
};

// Export for potential use in other files
if (typeof module !== 'undefined' && module.exports) {
  module.exports = {
    suppressedWarnings,
    shouldSuppress
  };
}
