"use strict";(()=>{var e={};e.id=3166,e.ids=[636,3166,3220],e.modules={123:e=>{e.exports=require("dom-helpers/removeEventListener")},1332:e=>{e.exports=require("react-custom-scrollbars-2")},1428:e=>{e.exports=import("axios")},1680:e=>{e.exports=require("@restart/ui/utils")},1919:e=>{e.exports=require("react-transition-group/Transition")},3892:e=>{e.exports=require("classnames")},4048:e=>{e.exports=require("react-truncate")},6009:e=>{e.exports=require("dom-helpers/ownerDocument")},6952:e=>{e.exports=require("@restart/ui/Modal")},7374:e=>{e.exports=require("@restart/ui/Dropdown")},8732:e=>{e.exports=require("react/jsx-runtime")},9653:e=>{e.exports=require("@restart/ui/Overlay")},11242:e=>{e.exports=require("redux-persist/integration/react")},11688:e=>{e.exports=require("@restart/hooks/useWillUnmount")},12388:e=>{e.exports=require("dom-helpers/addEventListener")},13364:e=>{e.exports=require("redux-saga/effects")},14062:e=>{e.exports=import("react-redux")},14078:e=>{e.exports=import("swr")},14332:e=>{e.exports=require("uncontrollable")},16116:e=>{e.exports=require("invariant")},18622:e=>{e.exports=require("yup")},19121:e=>{e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},22326:e=>{e.exports=require("react-dom")},22541:e=>{e.exports=require("redux-persist/lib/storage")},25303:e=>{e.exports=require("@restart/ui/NavItem")},26324:e=>{e.exports=require("warning")},27825:e=>{e.exports=require("lodash")},27910:e=>{e.exports=require("stream")},28217:e=>{e.exports=require("@restart/ui/DropdownItem")},29021:e=>{e.exports=require("fs")},29294:e=>{e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},29628:e=>{e.exports=require("dom-helpers/addClass")},29825:e=>{e.exports=require("prop-types")},29841:e=>{e.exports=require("dom-helpers/hasClass")},30362:e=>{e.exports=require("@restart/ui/DropdownToggle")},32412:(e,r,t)=>{t.d(r,{A:()=>i});var s=t(8732),o=t(52449),a=t.n(o);function i(){return(0,s.jsxs)(a(),{viewBox:"0 0 380 70",height:50,width:317,speed:2,title:"Loading",foregroundColor:"#f7f7f7",backgroundColor:"#ecebeb",uniqueKey:"operation",children:[(0,s.jsx)("rect",{x:"10",y:"0",rx:"4",ry:"4",width:"320",height:"25"}),(0,s.jsx)("rect",{x:"40",y:"40",rx:"3",ry:"3",width:"250",height:"20"})]})}},33873:e=>{e.exports=require("path")},36653:e=>{e.exports=require("nprogress")},36955:e=>{e.exports=require("@restart/hooks/useBreakpoint")},37766:e=>{e.exports=require("@restart/hooks/useMergedRefs")},39756:e=>{e.exports=import("redux")},40051:e=>{e.exports=require("dom-helpers/removeClass")},40361:e=>{e.exports=require("next/dist/compiled/next-server/pages.runtime.prod.js")},42893:e=>{e.exports=import("react-hot-toast")},43294:e=>{e.exports=require("formik")},50009:e=>{e.exports=require("@restart/ui/DropdownMenu")},51612:(e,r,t)=>{t.a(e,async(e,s)=>{try{t.r(r),t.d(r,{default:()=>l});var o=t(8732),a=t(82015),i=t(63487),n=t(32412),u=t(88751),p=e([i]);i=(p.then?(await p)():p)[0];let l=()=>{let{i18n:e}=(0,u.useTranslation)("common");e.language&&e.language;let[r,t]=(0,a.useState)({title:"",description:"",pageCategory:"",images:"",isEnabled:!0}),[s,p]=(0,a.useState)(!1),l={query:{pageCategory:""},sort:{title:"asc"},limit:"~"},d=async()=>{let e=await x();if(p(!0),e.length>0){l.query.pageCategory=e;let r=await i.A.get("/landingPage",l);if(Array.isArray(r.data)&&r.data.length>0){let e=r.data[r.data.length-1];e.images=r&&e.images.length>0&&!0===e.isEnabled?e.images.map((e,r)=>String(`http://localhost:3001/api/v1/image/show/${e._id}`)):"/images/logo.jpg",e.description=r&&e.description.length>0&&!0===e.isEnabled?e.description:"The Robert Koch Institut is taking over the coordination of the “WHO AMR Surveillance and Quality Assessment Collaborating Centres Network” this autumn 2019. The network supports the World Health Organization (WHO) to reduce drug-resistant infections globally. It focuses on further developing the global antimicrobial resistance (AMR) surveillance system (GLASS), and promoting exchange and peer support between countries.",t(e),x(),p(!1)}}},x=async()=>{let e=await i.A.get("/pagecategory",{query:{title:"AboutUs"}});return!!e&&!!e.data&&e.data.length>0&&e.data[0]._id};(0,a.useEffect)(()=>{d()},[]);let c=r.description.replace(/\&nbsp;/g," ");return(0,o.jsx)("div",{className:"aboutUs",children:!0===s?(0,o.jsx)(n.A,{}):(0,o.jsxs)("div",{children:[(0,o.jsx)("img",{className:"logoImg",src:r.images,alt:""}),(0,o.jsx)("div",{dangerouslySetInnerHTML:{__html:c}})," "]})})};s()}catch(e){s(e)}})},52449:e=>{e.exports=require("react-content-loader")},57664:e=>{e.exports=require("dom-helpers/scrollbarSize")},58928:e=>{e.exports=require("dom-helpers/canUseDOM")},59717:e=>{e.exports=require("@restart/ui/DropdownContext")},60560:e=>{e.exports=require("dom-helpers/contains")},63033:e=>{e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},65447:e=>{e.exports=require("@restart/ui/Button")},67364:e=>{e.exports=require("@restart/hooks/useCallbackRef")},68455:e=>{e.exports=import("redux-saga")},69722:e=>{e.exports=require("es6-promise")},74075:e=>{e.exports=require("zlib")},74716:e=>{e.exports=require("moment")},74987:e=>{e.exports=require("@restart/ui/ModalManager")},78097:e=>{e.exports=require("next-redux-saga")},78634:e=>{e.exports=require("@restart/ui/Anchor")},80237:(e,r)=>{Object.defineProperty(r,"A",{enumerable:!0,get:function(){return t}});var t=function(e){return e.PAGES="PAGES",e.PAGES_API="PAGES_API",e.APP_PAGE="APP_PAGE",e.APP_ROUTE="APP_ROUTE",e.IMAGE="IMAGE",e}({})},81366:e=>{e.exports=require("redux-auth-wrapper/connectedAuthWrapper")},81413:(e,r)=>{Object.defineProperty(r,"M",{enumerable:!0,get:function(){return function e(r,t){return t in r?r[t]:"then"in r&&"function"==typeof r.then?r.then(r=>e(r,t)):"function"==typeof r&&"default"===t?r:void 0}}})},81521:e=>{e.exports=require("dom-helpers/querySelectorAll")},81895:e=>{e.exports=require("@restart/hooks/useEventCallback")},82015:e=>{e.exports=require("react")},84933:(e,r,t)=>{t.a(e,async(e,s)=>{try{t.r(r),t.d(r,{config:()=>q,default:()=>x,getServerSideProps:()=>h,getStaticPaths:()=>g,getStaticProps:()=>c,reportWebVitals:()=>m,routeModule:()=>S,unstable_getServerProps:()=>v,unstable_getServerSideProps:()=>y,unstable_getStaticParams:()=>P,unstable_getStaticPaths:()=>f,unstable_getStaticProps:()=>b});var o=t(63885),a=t(80237),i=t(81413),n=t(9616),u=t.n(n),p=t(72386),l=t(51612),d=e([p,l]);[p,l]=d.then?(await d)():d;let x=(0,i.M)(l,"default"),c=(0,i.M)(l,"getStaticProps"),g=(0,i.M)(l,"getStaticPaths"),h=(0,i.M)(l,"getServerSideProps"),q=(0,i.M)(l,"config"),m=(0,i.M)(l,"reportWebVitals"),b=(0,i.M)(l,"unstable_getStaticProps"),f=(0,i.M)(l,"unstable_getStaticPaths"),P=(0,i.M)(l,"unstable_getStaticParams"),v=(0,i.M)(l,"unstable_getServerProps"),y=(0,i.M)(l,"unstable_getServerSideProps"),S=new o.PagesRouteModule({definition:{kind:a.A.PAGES,page:"/dashboard/AboutUs",pathname:"/dashboard/AboutUs",bundlePath:"",filename:""},components:{App:p.default,Document:u()},userland:l});s()}catch(e){s(e)}})},86842:e=>{e.exports=require("@restart/ui/SelectableContext")},87571:e=>{e.exports=require("dom-helpers/transitionEnd")},88751:e=>{e.exports=require("next-i18next")},93787:e=>{e.exports=require("redux-persist")},94947:e=>{e.exports=require("@restart/hooks/useTimeout")},96196:e=>{e.exports=require("next-redux-wrapper")},98320:e=>{e.exports=require("@restart/hooks/useIsomorphicEffect")},99460:e=>{e.exports=require("dom-helpers/css")}};var r=require("../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[6089,9216,9616,2386],()=>t(84933));module.exports=s})();