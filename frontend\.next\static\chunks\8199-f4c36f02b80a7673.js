"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[8199],{5377:(e,t,r)=>{r.d(t,{A:()=>i});var l=r(37876),n=r(14232),s=r(66619),a=r(15641),o=r(31753);let i=e=>{let{i18n:t}=(0,o.Bd)("common"),r=t.language,{mapdata:i}=e,[y,c]=(0,n.useState)({}),[d,p]=(0,n.useState)({}),[u,m]=(0,n.useState)({}),f=()=>{p(null),m(null)},h=()=>{c({title:i.title,id:i._id,lat:i.country&&i.country.coordinates?parseFloat(i.country.coordinates[0].latitude):null,lng:i.country&&i.country.coordinates?parseFloat(i.country.coordinates[0].longitude):null})};return(0,n.useEffect)(()=>{h()},[i]),(0,l.jsxs)(l.<PERSON>ag<PERSON>,{children:[" ",y&&y.id?(0,l.jsx)(s.A,{onClose:f,language:r,initialCenter:{lat:y.lat,lng:y.lng},activeMarker:d,markerInfo:(0,l.jsx)(e=>{let{info:t}=e;return(0,l.jsx)("a",{children:null==t?void 0:t.name})},{info:u}),children:(0,l.jsx)(a.A,{name:y.title,icon:{url:"/images/map-marker-white.svg"},onClick:(e,t,r)=>{f(),p(t),m({name:e.name})},position:y})}):null]})}},15641:(e,t,r)=>{r.d(t,{A:()=>s});var l=r(37876);r(14232);var n=r(62945);let s=e=>{let{name:t="Marker",id:r="",countryId:s="",type:a,icon:o,position:i,onClick:y,title:c,draggable:d=!1}=e;return i&&"number"==typeof i.lat&&"number"==typeof i.lng?(0,l.jsx)(n.pH,{position:i,icon:o,title:c||t,draggable:d,onClick:e=>{y&&y({name:t,id:r,countryId:s,type:a,position:i},{position:i,getPosition:()=>i},e)}}):null}},18199:(e,t,r)=>{r.r(t),r.d(t,{default:()=>i});var l=r(37876);r(14232);var n=r(56970),s=r(37784),a=r(5377),o=r(31753);let i=e=>{var t,r,i,y;let{t:c}=(0,o.Bd)("common");return(0,l.jsx)(l.Fragment,{children:(0,l.jsxs)(n.A,{children:[(0,l.jsx)(s.A,{md:6,children:(0,l.jsx)(a.A,{mapdata:e.eventData})}),(0,l.jsxs)(s.A,{className:"eventDetails ps-md-0",md:6,children:[(0,l.jsxs)("p",{children:[" ",(0,l.jsx)("b",{children:c("Events.show.EventId")}),": ",(0,l.jsx)("span",{children:e.eventData?e.eventData.title:""})," "]}),function(e,t){return(0,l.jsxs)("p",{children:[(0,l.jsx)("b",{children:e("Events.show.OperationName")}),":",(0,l.jsx)("span",{children:t&&t.operation?t.operation.title:""})]})}(c,e.eventData),(0,l.jsxs)("p",{children:[(0,l.jsx)("b",{children:c("Events.show.Country&Territory")}),":",(0,l.jsx)("span",{children:(null==(t=e.eventData)?void 0:t.country)?e.eventData.country.title:""})]}),function(e,t){return(0,l.jsxs)("p",{children:[(0,l.jsx)("b",{children:e("Events.show.MonitoredbyRKI")}),":",(0,l.jsx)("span",{children:t&&t.rki_monitored?!0===t.rki_monitored&&"Yes":"No"})]})}(c,e.eventData),function(e,t){return(0,l.jsxs)("p",{children:[(0,l.jsx)("b",{children:e("Events.show.CountryRegion")}),":",(0,l.jsx)("span",{children:t&&t.country_regions?t.country_regions.map(e=>e.title).join(", "):""})]})}(c,e.eventData),(0,l.jsxs)("p",{children:[(0,l.jsx)("b",{children:c("Events.show.Status")}),":",(0,l.jsx)("span",{children:(null==(r=e.eventData)?void 0:r.status)?e.eventData.status.title:""})]}),function(){var t;return(0,l.jsxs)("p",{children:[(0,l.jsx)("b",{children:c("Events.show.HazardType")}),":",(0,l.jsx)("span",{children:(null==(t=e.eventData)?void 0:t.hazard_type)?e.eventData.hazard_type.title:""})]})}(),function(e,t){return(0,l.jsxs)("p",{children:[(0,l.jsx)("b",{children:e("Events.show.Hazard")}),":",(0,l.jsx)("span",{children:t&&t.hazard?t.hazard.map(e=>e?e.title.en:"").join(", "):""})]})}(c,e.eventData),(0,l.jsxs)("p",{children:[(0,l.jsx)("b",{children:c("Events.show.Syndrome")}),":",(0,l.jsx)("span",{children:(null==(i=e.eventData)?void 0:i.syndrome)?e.eventData.syndrome.title:""})]}),function(e,t){return(0,l.jsxs)("p",{children:[(0,l.jsx)("b",{children:e("Events.show.LaboratoryConfirmed")}),":",(0,l.jsx)("span",{children:t&&t.laboratory_confirmed?!0===t.laboratory_confirmed&&"Yes":"No"})]})}(c,e.eventData),(0,l.jsxs)("p",{style:{margin:0},children:[(0,l.jsx)("b",{children:c("Events.show.Validatedbyofficial")}),":",(0,l.jsx)("span",{children:(null==(y=e.eventData)?void 0:y.officially_validated)?!0===e.eventData.officially_validated&&"Yes":"No"})]})]})]})})}},66619:(e,t,r)=>{r.d(t,{A:()=>u});var l=r(37876);r(14232);var n=r(62945);let s=e=>{let{position:t,onCloseClick:r,children:s}=e;return(0,l.jsx)(n.Fu,{position:t,onCloseClick:r,children:(0,l.jsx)("div",{children:s})})},a="labels.text.fill",o="labels.text.stroke",i="road.highway",y="geometry.stroke",c=[{elementType:"geometry",stylers:[{color:"#1d2c4d"}]},{elementType:a,stylers:[{color:"#8ec3b9"}]},{elementType:o,stylers:[{color:"#1a3646"}]},{featureType:"administrative",elementType:"geometry",stylers:[{visibility:"off"}]},{featureType:"administrative.country",elementType:y,stylers:[{color:"#4b6878"}]},{featureType:"administrative.land_parcel",elementType:"labels",stylers:[{visibility:"off"}]},{featureType:"administrative.land_parcel",elementType:a,stylers:[{color:"#64779e"}]},{featureType:"administrative.province",elementType:y,stylers:[{color:"#4b6878"}]},{featureType:"landscape.man_made",elementType:y,stylers:[{color:"#334e87"}]},{featureType:"landscape.natural",elementType:"geometry",stylers:[{color:"#023e58"}]},{featureType:"poi",stylers:[{visibility:"off"}]},{featureType:"poi",elementType:"geometry",stylers:[{color:"#283d6a"}]},{featureType:"poi",elementType:"labels.text",stylers:[{visibility:"off"}]},{featureType:"poi",elementType:a,stylers:[{color:"#6f9ba5"}]},{featureType:"poi",elementType:o,stylers:[{color:"#1d2c4d"}]},{featureType:"poi.park",elementType:"geometry.fill",stylers:[{color:"#023e58"}]},{featureType:"poi.park",elementType:a,stylers:[{color:"#3C7680"}]},{featureType:"road",stylers:[{visibility:"off"}]},{featureType:"road",elementType:"geometry",stylers:[{color:"#304a7d"}]},{featureType:"road",elementType:"labels.icon",stylers:[{visibility:"off"}]},{featureType:"road",elementType:a,stylers:[{color:"#98a5be"}]},{featureType:"road",elementType:o,stylers:[{color:"#1d2c4d"}]},{featureType:i,elementType:"geometry",stylers:[{color:"#2c6675"}]},{featureType:i,elementType:y,stylers:[{color:"#255763"}]},{featureType:i,elementType:a,stylers:[{color:"#b0d5ce"}]},{featureType:i,elementType:o,stylers:[{color:"#023e58"}]},{featureType:"road.local",elementType:"labels",stylers:[{visibility:"off"}]},{featureType:"transit",stylers:[{visibility:"off"}]},{featureType:"transit",elementType:a,stylers:[{color:"#98a5be"}]},{featureType:"transit",elementType:o,stylers:[{color:"#1d2c4d"}]},{featureType:"transit.line",elementType:"geometry.fill",stylers:[{color:"#283d6a"}]},{featureType:"transit.station",elementType:"geometry",stylers:[{color:"#3a4762"}]},{featureType:"water",elementType:"geometry",stylers:[{color:"#0e1626"}]},{featureType:"water",elementType:a,stylers:[{color:"#4e6d70"}]}];var d=r(89099),p=r(55316);let u=e=>{let{markerInfo:t,activeMarker:r,initialCenter:a,children:o,height:i=300,width:y="114%",language:u,zoom:m=1,minZoom:f=1,onClose:h}=e,{locale:v}=(0,d.useRouter)(),{isLoaded:T,loadError:x}=(0,p._)();return x?(0,l.jsx)("div",{children:"Error loading maps"}):T?(0,l.jsx)("div",{className:"map-container",children:(0,l.jsx)("div",{className:"mapprint",style:{width:y,height:i,position:"relative"},children:(0,l.jsxs)(n.u6,{mapContainerStyle:{width:y,height:"number"==typeof i?"".concat(i,"px"):i},center:a||{lat:52.520017,lng:13.404195},zoom:m,onLoad:e=>{e.setOptions({styles:c})},options:{minZoom:f,draggable:!0,keyboardShortcuts:!1,streetViewControl:!1,panControl:!1,clickableIcons:!1,mapTypeControl:!1,fullscreenControl:!0},children:[o,t&&r&&r.getPosition&&(0,l.jsx)(s,{position:r.getPosition(),onCloseClick:()=>{console.log("close click"),null==h||h()},children:t})]})})}):(0,l.jsx)("div",{children:"Loading Maps..."})}}}]);
//# sourceMappingURL=8199-f4c36f02b80a7673.js.map