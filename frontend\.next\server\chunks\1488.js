"use strict";exports.id=1488,exports.ids=[1488],exports.modules={11314:(e,t,r)=>{r.r(t),r.d(t,{default:()=>i});var n=r(8732);r(82015);var l=r(83551),s=r(49481),a=r(92730),o=r(88751);let i=e=>{let{t}=(0,o.useTranslation)("common");return(0,n.jsx)(n.Fragment,{children:(0,n.jsxs)(l.A,{children:[(0,n.jsx)(s.A,{md:6,children:(0,n.jsx)(a.A,{mapdata:e.eventData})}),(0,n.jsxs)(s.A,{className:"eventDetails ps-md-0",md:6,children:[(0,n.jsxs)("p",{children:[" ",(0,n.jsx)("b",{children:t("Events.show.EventId")}),": ",(0,n.jsx)("span",{children:e.eventData?e.eventData.title:""})," "]}),function(e,t){return(0,n.jsxs)("p",{children:[(0,n.jsx)("b",{children:e("Events.show.OperationName")}),":",(0,n.jsx)("span",{children:t&&t.operation?t.operation.title:""})]})}(t,e.eventData),(0,n.jsxs)("p",{children:[(0,n.jsx)("b",{children:t("Events.show.Country&Territory")}),":",(0,n.jsx)("span",{children:e.eventData?.country?e.eventData.country.title:""})]}),function(e,t){return(0,n.jsxs)("p",{children:[(0,n.jsx)("b",{children:e("Events.show.MonitoredbyRKI")}),":",(0,n.jsx)("span",{children:t&&t.rki_monitored?!0===t.rki_monitored&&"Yes":"No"})]})}(t,e.eventData),function(e,t){return(0,n.jsxs)("p",{children:[(0,n.jsx)("b",{children:e("Events.show.CountryRegion")}),":",(0,n.jsx)("span",{children:t&&t.country_regions?t.country_regions.map(e=>e.title).join(", "):""})]})}(t,e.eventData),(0,n.jsxs)("p",{children:[(0,n.jsx)("b",{children:t("Events.show.Status")}),":",(0,n.jsx)("span",{children:e.eventData?.status?e.eventData.status.title:""})]}),(0,n.jsxs)("p",{children:[(0,n.jsx)("b",{children:t("Events.show.HazardType")}),":",(0,n.jsx)("span",{children:e.eventData?.hazard_type?e.eventData.hazard_type.title:""})]}),function(e,t){return(0,n.jsxs)("p",{children:[(0,n.jsx)("b",{children:e("Events.show.Hazard")}),":",(0,n.jsx)("span",{children:t&&t.hazard?t.hazard.map(e=>e?e.title.en:"").join(", "):""})]})}(t,e.eventData),(0,n.jsxs)("p",{children:[(0,n.jsx)("b",{children:t("Events.show.Syndrome")}),":",(0,n.jsx)("span",{children:e.eventData?.syndrome?e.eventData.syndrome.title:""})]}),function(e,t){return(0,n.jsxs)("p",{children:[(0,n.jsx)("b",{children:e("Events.show.LaboratoryConfirmed")}),":",(0,n.jsx)("span",{children:t&&t.laboratory_confirmed?!0===t.laboratory_confirmed&&"Yes":"No"})]})}(t,e.eventData),(0,n.jsxs)("p",{style:{margin:0},children:[(0,n.jsx)("b",{children:t("Events.show.Validatedbyofficial")}),":",(0,n.jsx)("span",{children:e.eventData?.officially_validated?!0===e.eventData.officially_validated&&"Yes":"No"})]})]})]})})}},72953:(e,t,r)=>{r.d(t,{A:()=>u});var n=r(8732);r(82015);var l=r(94696);let s=({position:e,onCloseClick:t,children:r})=>(0,n.jsx)(l.InfoWindow,{position:e,onCloseClick:t,children:(0,n.jsx)("div",{children:r})}),a="labels.text.fill",o="labels.text.stroke",i="road.highway",y="geometry.stroke",c=[{elementType:"geometry",stylers:[{color:"#1d2c4d"}]},{elementType:a,stylers:[{color:"#8ec3b9"}]},{elementType:o,stylers:[{color:"#1a3646"}]},{featureType:"administrative",elementType:"geometry",stylers:[{visibility:"off"}]},{featureType:"administrative.country",elementType:y,stylers:[{color:"#4b6878"}]},{featureType:"administrative.land_parcel",elementType:"labels",stylers:[{visibility:"off"}]},{featureType:"administrative.land_parcel",elementType:a,stylers:[{color:"#64779e"}]},{featureType:"administrative.province",elementType:y,stylers:[{color:"#4b6878"}]},{featureType:"landscape.man_made",elementType:y,stylers:[{color:"#334e87"}]},{featureType:"landscape.natural",elementType:"geometry",stylers:[{color:"#023e58"}]},{featureType:"poi",stylers:[{visibility:"off"}]},{featureType:"poi",elementType:"geometry",stylers:[{color:"#283d6a"}]},{featureType:"poi",elementType:"labels.text",stylers:[{visibility:"off"}]},{featureType:"poi",elementType:a,stylers:[{color:"#6f9ba5"}]},{featureType:"poi",elementType:o,stylers:[{color:"#1d2c4d"}]},{featureType:"poi.park",elementType:"geometry.fill",stylers:[{color:"#023e58"}]},{featureType:"poi.park",elementType:a,stylers:[{color:"#3C7680"}]},{featureType:"road",stylers:[{visibility:"off"}]},{featureType:"road",elementType:"geometry",stylers:[{color:"#304a7d"}]},{featureType:"road",elementType:"labels.icon",stylers:[{visibility:"off"}]},{featureType:"road",elementType:a,stylers:[{color:"#98a5be"}]},{featureType:"road",elementType:o,stylers:[{color:"#1d2c4d"}]},{featureType:i,elementType:"geometry",stylers:[{color:"#2c6675"}]},{featureType:i,elementType:y,stylers:[{color:"#255763"}]},{featureType:i,elementType:a,stylers:[{color:"#b0d5ce"}]},{featureType:i,elementType:o,stylers:[{color:"#023e58"}]},{featureType:"road.local",elementType:"labels",stylers:[{visibility:"off"}]},{featureType:"transit",stylers:[{visibility:"off"}]},{featureType:"transit",elementType:a,stylers:[{color:"#98a5be"}]},{featureType:"transit",elementType:o,stylers:[{color:"#1d2c4d"}]},{featureType:"transit.line",elementType:"geometry.fill",stylers:[{color:"#283d6a"}]},{featureType:"transit.station",elementType:"geometry",stylers:[{color:"#3a4762"}]},{featureType:"water",elementType:"geometry",stylers:[{color:"#0e1626"}]},{featureType:"water",elementType:a,stylers:[{color:"#4e6d70"}]}];var p=r(44233),d=r(40691);let u=({markerInfo:e,activeMarker:t,initialCenter:r,children:a,height:o=300,width:i="114%",language:y,zoom:u=1,minZoom:m=1,onClose:f})=>{let{locale:h}=(0,p.useRouter)(),{isLoaded:T,loadError:v}=(0,d._)(),x={width:i,height:"number"==typeof o?`${o}px`:o};return v?(0,n.jsx)("div",{children:"Error loading maps"}):T?(0,n.jsx)("div",{className:"map-container",children:(0,n.jsx)("div",{className:"mapprint",style:{width:i,height:o,position:"relative"},children:(0,n.jsxs)(l.GoogleMap,{mapContainerStyle:x,center:r||{lat:52.520017,lng:13.404195},zoom:u,onLoad:e=>{e.setOptions({styles:c})},options:{minZoom:m,draggable:!0,keyboardShortcuts:!1,streetViewControl:!1,panControl:!1,clickableIcons:!1,mapTypeControl:!1,fullscreenControl:!0},children:[a,e&&t&&t.getPosition&&(0,n.jsx)(s,{position:t.getPosition(),onCloseClick:()=>{console.log("close click"),f?.()},children:e})]})})}):(0,n.jsx)("div",{children:"Loading Maps..."})}},80237:(e,t)=>{Object.defineProperty(t,"A",{enumerable:!0,get:function(){return r}});var r=function(e){return e.PAGES="PAGES",e.PAGES_API="PAGES_API",e.APP_PAGE="APP_PAGE",e.APP_ROUTE="APP_ROUTE",e.IMAGE="IMAGE",e}({})},81413:(e,t)=>{Object.defineProperty(t,"M",{enumerable:!0,get:function(){return function e(t,r){return r in t?t[r]:"then"in t&&"function"==typeof t.then?t.then(t=>e(t,r)):"function"==typeof t&&"default"===r?t:void 0}}})},89364:(e,t,r)=>{r.d(t,{A:()=>s});var n=r(8732);r(82015);var l=r(94696);let s=({name:e="Marker",id:t="",countryId:r="",type:s,icon:a,position:o,onClick:i,title:y,draggable:c=!1})=>o&&"number"==typeof o.lat&&"number"==typeof o.lng?(0,n.jsx)(l.Marker,{position:o,icon:a,title:y||e,draggable:c,onClick:n=>{i&&i({name:e,id:t,countryId:r,type:s,position:o},{position:o,getPosition:()=>o},n)}}):null},92730:(e,t,r)=>{r.d(t,{A:()=>i});var n=r(8732),l=r(82015),s=r(72953),a=r(89364),o=r(88751);let i=e=>{let{i18n:t}=(0,o.useTranslation)("common"),r=t.language,{mapdata:i}=e,[y,c]=(0,l.useState)({}),[p,d]=(0,l.useState)({}),[u,m]=(0,l.useState)({}),f=()=>{d(null),m(null)},h=()=>{c({title:i.title,id:i._id,lat:i.country&&i.country.coordinates?parseFloat(i.country.coordinates[0].latitude):null,lng:i.country&&i.country.coordinates?parseFloat(i.country.coordinates[0].longitude):null})};return(0,l.useEffect)(()=>{h()},[i]),(0,n.jsxs)(n.Fragment,{children:[" ",y&&y.id?(0,n.jsx)(s.A,{onClose:f,language:r,initialCenter:{lat:y.lat,lng:y.lng},activeMarker:p,markerInfo:(0,n.jsx)(e=>{let{info:t}=e;return(0,n.jsx)("a",{children:t?.name})},{info:u}),children:(0,n.jsx)(a.A,{name:y.title,icon:{url:"/images/map-marker-white.svg"},onClick:(e,t,r)=>{f(),d(t),m({name:e.name})},position:y})}):null]})}}};