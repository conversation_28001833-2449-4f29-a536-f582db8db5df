"use strict";(()=>{var e={};e.id=3790,e.ids=[636,3220,3790],e.modules={123:e=>{e.exports=require("dom-helpers/removeEventListener")},1332:e=>{e.exports=require("react-custom-scrollbars-2")},1428:e=>{e.exports=import("axios")},1680:e=>{e.exports=require("@restart/ui/utils")},1919:e=>{e.exports=require("react-transition-group/Transition")},3892:e=>{e.exports=require("classnames")},4048:e=>{e.exports=require("react-truncate")},6009:e=>{e.exports=require("dom-helpers/ownerDocument")},6952:e=>{e.exports=require("@restart/ui/Modal")},7374:e=>{e.exports=require("@restart/ui/Dropdown")},8732:e=>{e.exports=require("react/jsx-runtime")},9653:e=>{e.exports=require("@restart/ui/Overlay")},11e3:e=>{e.exports=require("react-multi-select-component")},11242:e=>{e.exports=require("redux-persist/integration/react")},11688:e=>{e.exports=require("@restart/hooks/useWillUnmount")},12388:e=>{e.exports=require("dom-helpers/addEventListener")},13364:e=>{e.exports=require("redux-saga/effects")},14062:e=>{e.exports=import("react-redux")},14078:e=>{e.exports=import("swr")},14332:e=>{e.exports=require("uncontrollable")},14749:(e,r,t)=>{t.a(e,async(e,s)=>{try{t.r(r),t.d(r,{config:()=>h,default:()=>c,getServerSideProps:()=>g,getStaticPaths:()=>m,getStaticProps:()=>x,reportWebVitals:()=>q,routeModule:()=>f,unstable_getServerProps:()=>b,unstable_getServerSideProps:()=>S,unstable_getStaticParams:()=>P,unstable_getStaticPaths:()=>v,unstable_getStaticProps:()=>y});var a=t(63885),o=t(80237),i=t(81413),n=t(9616),u=t.n(n),l=t(72386),p=t(52508),d=e([l,p]);[l,p]=d.then?(await d)():d;let c=(0,i.M)(p,"default"),x=(0,i.M)(p,"getStaticProps"),m=(0,i.M)(p,"getStaticPaths"),g=(0,i.M)(p,"getServerSideProps"),h=(0,i.M)(p,"config"),q=(0,i.M)(p,"reportWebVitals"),y=(0,i.M)(p,"unstable_getStaticProps"),v=(0,i.M)(p,"unstable_getStaticPaths"),P=(0,i.M)(p,"unstable_getStaticParams"),b=(0,i.M)(p,"unstable_getServerProps"),S=(0,i.M)(p,"unstable_getServerSideProps"),f=new a.PagesRouteModule({definition:{kind:o.A.PAGES,page:"/profile/bookmarkTable",pathname:"/profile/bookmarkTable",bundlePath:"",filename:""},components:{App:l.default,Document:u()},userland:p});s()}catch(e){s(e)}})},16116:e=>{e.exports=require("invariant")},18622:e=>{e.exports=require("yup")},19121:e=>{e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},22326:e=>{e.exports=require("react-dom")},22541:e=>{e.exports=require("redux-persist/lib/storage")},25303:e=>{e.exports=require("@restart/ui/NavItem")},26324:e=>{e.exports=require("warning")},27825:e=>{e.exports=require("lodash")},27910:e=>{e.exports=require("stream")},28217:e=>{e.exports=require("@restart/ui/DropdownItem")},29021:e=>{e.exports=require("fs")},29294:e=>{e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},29628:e=>{e.exports=require("dom-helpers/addClass")},29825:e=>{e.exports=require("prop-types")},29841:e=>{e.exports=require("dom-helpers/hasClass")},30362:e=>{e.exports=require("@restart/ui/DropdownToggle")},33873:e=>{e.exports=require("path")},36653:e=>{e.exports=require("nprogress")},36955:e=>{e.exports=require("@restart/hooks/useBreakpoint")},37766:e=>{e.exports=require("@restart/hooks/useMergedRefs")},38609:e=>{e.exports=require("react-data-table-component")},39756:e=>{e.exports=import("redux")},40051:e=>{e.exports=require("dom-helpers/removeClass")},40361:e=>{e.exports=require("next/dist/compiled/next-server/pages.runtime.prod.js")},42893:e=>{e.exports=import("react-hot-toast")},43294:e=>{e.exports=require("formik")},50009:e=>{e.exports=require("@restart/ui/DropdownMenu")},52508:(e,r,t)=>{t.a(e,async(e,s)=>{try{t.r(r),t.d(r,{default:()=>v});var a=t(8732),o=t(82015),i=t.n(o),n=t(14062),u=t(27825),l=t.n(u),p=t(19918),d=t.n(p),c=t(12403),x=t(91353),m=t(62706),g=t(56084),h=t(63487),q=t(88751),y=e([n,h]);[n,h]=y.then?(await y)():y;let v=(0,n.connect)(e=>e)(e=>{let r=[{value:"institution",label:"Organisations"},{value:"operation",label:"Operations"},{value:"project",label:"Projects"},{value:"event",label:"Events"},{value:"vspace",label:"Virtual Spaces"}],[t,s]=(0,o.useState)([]),[,n]=(0,o.useState)(!1),[u,p]=(0,o.useState)(!1),[y,v]=(0,o.useState)(0),[P,b]=(0,o.useState)(10),[S,f]=(0,o.useState)(""),[A,w]=(0,o.useState)(!1),[_,k]=(0,o.useState)(r),[j,M]=(0,o.useState)({}),{t:C}=(0,q.useTranslation)("common"),E=async e=>{e&&e._id&&M(e._id),p(!0)},T={sort:{created_at:"asc"},populate:{path:"entity_id",select:"title"},lean:!0,limit:P,page:1,query:{user:e.user&&e.user._id?e.user._id:""}},R=[{name:C("Title"),selector:"",cell:e=>e.entity_id&&e.entity_id.title?(0,a.jsx)(d(),{href:`/${e.entity_type}/[...routes]`,as:`/${e.entity_type}/show/${e.entity_id._id}`,children:e.entity_id.title}):""},{name:C("Group"),selector:"group",cell:e=>e.onModel&&"Institution"===e.onModel?"Organisation":e.onModel},{name:C("Remove"),selector:"",cell:e=>(0,a.jsx)("div",{onClick:()=>E(e),style:{cursor:"pointer"},children:(0,a.jsx)("i",{className:"icon fas fa-trash-alt"})})}],G=async()=>{n(!0);let e=await h.A.get("/flag",T);e&&e.data&&(s(e.data),v(e.totalCount),n(!1))},D=async(e,r)=>{T.limit=e,T.page=r,n(!0);let t=l().map(_,"value");t&&t.length>0&&(T.query={...T.query,entity_type:t});let a=await h.A.get("/flag",T);a&&a.data&&a.data.length>0&&(s(a.data),b(e),n(!1))};(0,o.useEffect)(()=>{T.page=1,G()},[]);let I=i().useMemo(()=>{let e=e=>{e&&(T.populate.match={title:{$regex:e}},G())},t=l().debounce(r=>e(r),Number("500")||300);return(0,a.jsx)(m.default,{onFilter:e=>{f(e.target.value),t(e.target.value)},onClear:()=>{S&&(w(!A),f(""))},filterText:S,handleGroupHandler:e=>{k(e);let r=l().map(e,"value");0===r.length?s([]):(T.query={...T.query,entity_type:r},G())},groupType:_,options:r})},[S,_,A]),N=()=>p(!1),O=async()=>{if(p(!1),await h.A.remove(`/flag/${j}`),_&&Array.isArray(_)){let e=l().map(_,"value");e&&e.length>0&&(T.query={...T.query,entity_type:e})}G()};return(0,a.jsxs)("div",{className:"my-bookmark-table",children:[(0,a.jsxs)(c.A,{show:u,onHide:N,children:[(0,a.jsx)(c.A.Header,{closeButton:!0,children:(0,a.jsx)(c.A.Title,{children:C("Removebookmark")})}),(0,a.jsx)(c.A.Body,{children:C("Areyousurewanttoremovefromyourbookmark")}),(0,a.jsxs)(c.A.Footer,{children:[(0,a.jsx)(x.A,{variant:"secondary",onClick:N,children:C("Cancel")}),(0,a.jsx)(x.A,{variant:"primary",onClick:O,children:C("bookmarkDeleteYes")})]})]}),(0,a.jsx)(g.A,{columns:R,data:t,totalRows:y,subheader:!0,pagServer:!0,resetPaginationToggle:A,subHeaderComponent:I,handlePerRowsChange:D,handlePageChange:e=>{T.limit=P,T.page=e,""!==S&&(T.query={title:S});let r=l().map(_,"value");r&&r.length>0&&(T.query={...T.query,entity_type:r}),G()}})]})});s()}catch(e){s(e)}})},56084:(e,r,t)=>{t.d(r,{A:()=>l});var s=t(8732);t(82015);var a=t(38609),o=t.n(a),i=t(88751),n=t(30370);function u(e){let{t:r}=(0,i.useTranslation)("common"),t={rowsPerPageText:r("Rowsperpage")},{columns:a,data:u,totalRows:l,resetPaginationToggle:p,subheader:d,subHeaderComponent:c,handlePerRowsChange:x,handlePageChange:m,rowsPerPage:g,defaultRowsPerPage:h,selectableRows:q,loading:y,pagServer:v,onSelectedRowsChange:P,clearSelectedRows:b,sortServer:S,onSort:f,persistTableHead:A,sortFunction:w,..._}=e,k={paginationComponentOptions:t,noDataComponent:r("NoData"),noHeader:!0,columns:a,data:u||[],dense:!0,paginationResetDefaultPage:p,subHeader:d,progressPending:y,subHeaderComponent:c,pagination:!0,paginationServer:v,paginationPerPage:h||10,paginationRowsPerPageOptions:g||[10,15,20,25,30],paginationTotalRows:l,onChangeRowsPerPage:x,onChangePage:m,selectableRows:q,onSelectedRowsChange:P,clearSelectedRows:b,progressComponent:(0,s.jsx)(n.A,{}),sortIcon:(0,s.jsx)("i",{className:"sort-icon fas fa-exchange-alt"}),sortServer:S,onSort:f,sortFunction:w,persistTableHead:A,className:"rki-table"};return(0,s.jsx)(o(),{...k})}u.defaultProps={subHeader:!1,pagination:!0,totalRows:null,pagServer:!0,onSelectedRowsChange:null,clearSelectedRows:!1,sortServer:!1,persistTableHead:!1};let l=u},57664:e=>{e.exports=require("dom-helpers/scrollbarSize")},58928:e=>{e.exports=require("dom-helpers/canUseDOM")},59717:e=>{e.exports=require("@restart/ui/DropdownContext")},60560:e=>{e.exports=require("dom-helpers/contains")},62706:(e,r,t)=>{t.r(r),t.d(r,{default:()=>p});var s=t(8732);t(82015);var a=t(7082),o=t(83551),i=t(49481),n=t(59549),u=t(11e3),l=t(88751);let p=({filterText:e,onFilter:r,onClear:t,handleGroupHandler:p,groupType:d,options:c})=>{let{t:x}=(0,l.useTranslation)("common");return(0,s.jsx)(a.A,{fluid:!0,className:"p-0",children:(0,s.jsx)(o.A,{children:(0,s.jsx)(i.A,{xs:4,md:4,lg:4,children:(0,s.jsx)(n.A.Group,{style:{maxWidth:"800px"},children:(0,s.jsx)(u.MultiSelect,{overrideStrings:{selectSomeItems:x("ChooseGroup"),allItemsAreSelected:"All Groups are Selected"},options:c,value:d,onChange:p,className:"choose-group",labelledBy:"Select Network"})})})})})}},63033:e=>{e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},65447:e=>{e.exports=require("@restart/ui/Button")},67364:e=>{e.exports=require("@restart/hooks/useCallbackRef")},68455:e=>{e.exports=import("redux-saga")},69722:e=>{e.exports=require("es6-promise")},74075:e=>{e.exports=require("zlib")},74716:e=>{e.exports=require("moment")},74987:e=>{e.exports=require("@restart/ui/ModalManager")},78097:e=>{e.exports=require("next-redux-saga")},78634:e=>{e.exports=require("@restart/ui/Anchor")},80237:(e,r)=>{Object.defineProperty(r,"A",{enumerable:!0,get:function(){return t}});var t=function(e){return e.PAGES="PAGES",e.PAGES_API="PAGES_API",e.APP_PAGE="APP_PAGE",e.APP_ROUTE="APP_ROUTE",e.IMAGE="IMAGE",e}({})},81366:e=>{e.exports=require("redux-auth-wrapper/connectedAuthWrapper")},81413:(e,r)=>{Object.defineProperty(r,"M",{enumerable:!0,get:function(){return function e(r,t){return t in r?r[t]:"then"in r&&"function"==typeof r.then?r.then(r=>e(r,t)):"function"==typeof r&&"default"===t?r:void 0}}})},81521:e=>{e.exports=require("dom-helpers/querySelectorAll")},81895:e=>{e.exports=require("@restart/hooks/useEventCallback")},82015:e=>{e.exports=require("react")},86842:e=>{e.exports=require("@restart/ui/SelectableContext")},87571:e=>{e.exports=require("dom-helpers/transitionEnd")},88751:e=>{e.exports=require("next-i18next")},93787:e=>{e.exports=require("redux-persist")},94947:e=>{e.exports=require("@restart/hooks/useTimeout")},96196:e=>{e.exports=require("next-redux-wrapper")},98320:e=>{e.exports=require("@restart/hooks/useIsomorphicEffect")},99460:e=>{e.exports=require("dom-helpers/css")}};var r=require("../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[6089,9216,9616,2386],()=>t(14749));module.exports=s})();