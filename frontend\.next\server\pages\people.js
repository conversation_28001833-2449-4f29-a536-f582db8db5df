"use strict";(()=>{var e={};e.id=4137,e.ids=[636,3220,4137],e.modules={123:e=>{e.exports=require("dom-helpers/removeEventListener")},1332:e=>{e.exports=require("react-custom-scrollbars-2")},1428:e=>{e.exports=import("axios")},1680:e=>{e.exports=require("@restart/ui/utils")},1919:e=>{e.exports=require("react-transition-group/Transition")},3892:e=>{e.exports=require("classnames")},4048:e=>{e.exports=require("react-truncate")},6009:e=>{e.exports=require("dom-helpers/ownerDocument")},6952:e=>{e.exports=require("@restart/ui/Modal")},7374:e=>{e.exports=require("@restart/ui/Dropdown")},8732:e=>{e.exports=require("react/jsx-runtime")},9653:e=>{e.exports=require("@restart/ui/Overlay")},11242:e=>{e.exports=require("redux-persist/integration/react")},11688:e=>{e.exports=require("@restart/hooks/useWillUnmount")},12388:e=>{e.exports=require("dom-helpers/addEventListener")},13364:e=>{e.exports=require("redux-saga/effects")},14062:e=>{e.exports=import("react-redux")},14078:e=>{e.exports=import("swr")},14332:e=>{e.exports=require("uncontrollable")},16116:e=>{e.exports=require("invariant")},18622:e=>{e.exports=require("yup")},19121:e=>{e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},21198:(e,t,r)=>{r.a(e,async(e,s)=>{try{r.r(t),r.d(t,{default:()=>m,getStaticProps:()=>x});var i=r(8732);r(82015);var a=r(7082),o=r(83551),n=r(49481),u=r(67943),l=r(27053),p=r(88751),d=r(35576),c=e([u]);async function x({locale:e}){return{props:{...await (0,d.serverSideTranslations)(e,["common"])}}}u=(c.then?(await c)():c)[0];let m=()=>{let{t:e}=(0,p.useTranslation)("common");return(0,i.jsxs)(a.A,{fluid:!0,className:"p-0",children:[(0,i.jsx)(o.A,{children:(0,i.jsx)(n.A,{xs:12,children:(0,i.jsx)(l.A,{title:e("menu.people")})})}),(0,i.jsx)(o.A,{className:"mt-3",children:(0,i.jsx)(n.A,{xs:12,children:(0,i.jsx)(u.default,{})})})]})};s()}catch(e){s(e)}})},22326:e=>{e.exports=require("react-dom")},22541:e=>{e.exports=require("redux-persist/lib/storage")},23118:(e,t,r)=>{r.a(e,async(e,s)=>{try{r.r(t),r.d(t,{config:()=>q,default:()=>c,getServerSideProps:()=>g,getStaticPaths:()=>m,getStaticProps:()=>x,reportWebVitals:()=>h,routeModule:()=>v,unstable_getServerProps:()=>b,unstable_getServerSideProps:()=>y,unstable_getStaticParams:()=>S,unstable_getStaticPaths:()=>f,unstable_getStaticProps:()=>P});var i=r(63885),a=r(80237),o=r(81413),n=r(9616),u=r.n(n),l=r(72386),p=r(21198),d=e([l,p]);[l,p]=d.then?(await d)():d;let c=(0,o.M)(p,"default"),x=(0,o.M)(p,"getStaticProps"),m=(0,o.M)(p,"getStaticPaths"),g=(0,o.M)(p,"getServerSideProps"),q=(0,o.M)(p,"config"),h=(0,o.M)(p,"reportWebVitals"),P=(0,o.M)(p,"unstable_getStaticProps"),f=(0,o.M)(p,"unstable_getStaticPaths"),S=(0,o.M)(p,"unstable_getStaticParams"),b=(0,o.M)(p,"unstable_getServerProps"),y=(0,o.M)(p,"unstable_getServerSideProps"),v=new i.PagesRouteModule({definition:{kind:a.A.PAGES,page:"/people",pathname:"/people",bundlePath:"",filename:""},components:{App:l.default,Document:u()},userland:p});s()}catch(e){s(e)}})},25303:e=>{e.exports=require("@restart/ui/NavItem")},26324:e=>{e.exports=require("warning")},27053:(e,t,r)=>{r.d(t,{A:()=>i});var s=r(8732);function i(e){return(0,s.jsx)("h2",{className:"page-heading",children:e.title})}},27825:e=>{e.exports=require("lodash")},27910:e=>{e.exports=require("stream")},28217:e=>{e.exports=require("@restart/ui/DropdownItem")},29021:e=>{e.exports=require("fs")},29294:e=>{e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},29628:e=>{e.exports=require("dom-helpers/addClass")},29825:e=>{e.exports=require("prop-types")},29841:e=>{e.exports=require("dom-helpers/hasClass")},30362:e=>{e.exports=require("@restart/ui/DropdownToggle")},33045:(e,t,r)=>{r.a(e,async(e,s)=>{try{r.r(t),r.d(t,{default:()=>x});var i=r(8732),a=r(7082),o=r(83551),n=r(49481),u=r(99800),l=r(82015),p=r(63487),d=r(88751),c=e([u,p]);[u,p]=c.then?(await c)():c;let x=({filterText:e,onFilter:t,onClear:r,roles:s,onHandleSearch:c,institutions:x,onKeyPress:m})=>{let{t:g}=(0,d.useTranslation)("common"),[q,h]=(0,l.useState)([]),[,P]=(0,l.useState)(!1),f={sort:{created_at:"desc"},limit:"~",page:1,query:{},select:"-country -dial_code -firstname -region -institution.address -institution.contact_name -institution.description -institution.dial_code -institution.document -institution.doc_src -institution.images -institution.images_src -institution.expertise -institution.hazards -institution.hazard_types -institution.header -institution.networks -institution.partners -institution.telephone -institution.twitter -institution.type -institution.unit -institution.user -mobile_number -position"},S=async()=>{P(!0);let e=await p.A.get("/users",f);if(e&&Array.isArray(e.data)){let t=e.data.map((e,t)=>({label:e.username,value:e._id}));h(t),P(!1)}};return(0,l.useEffect)(()=>{S()},[]),(0,i.jsx)(a.A,{fluid:!0,className:"p-0",children:(0,i.jsx)(o.A,{children:(0,i.jsx)(n.A,{xs:12,md:6,lg:4,className:"p-0 me-3",children:(0,i.jsx)(u.default,{autoFocus:!0,isClearable:!0,isSearchable:!0,onKeyDown:m,onChange:t,placeholder:g("People.form.UsernameorEmail"),options:q})})})})};s()}catch(e){s(e)}})},33873:e=>{e.exports=require("path")},35576:e=>{e.exports=require("next-i18next/serverSideTranslations")},36653:e=>{e.exports=require("nprogress")},36955:e=>{e.exports=require("@restart/hooks/useBreakpoint")},37766:e=>{e.exports=require("@restart/hooks/useMergedRefs")},38609:e=>{e.exports=require("react-data-table-component")},39756:e=>{e.exports=import("redux")},40051:e=>{e.exports=require("dom-helpers/removeClass")},40361:e=>{e.exports=require("next/dist/compiled/next-server/pages.runtime.prod.js")},42893:e=>{e.exports=import("react-hot-toast")},43294:e=>{e.exports=require("formik")},50009:e=>{e.exports=require("@restart/ui/DropdownMenu")},56084:(e,t,r)=>{r.d(t,{A:()=>l});var s=r(8732);r(82015);var i=r(38609),a=r.n(i),o=r(88751),n=r(30370);function u(e){let{t}=(0,o.useTranslation)("common"),r={rowsPerPageText:t("Rowsperpage")},{columns:i,data:u,totalRows:l,resetPaginationToggle:p,subheader:d,subHeaderComponent:c,handlePerRowsChange:x,handlePageChange:m,rowsPerPage:g,defaultRowsPerPage:q,selectableRows:h,loading:P,pagServer:f,onSelectedRowsChange:S,clearSelectedRows:b,sortServer:y,onSort:v,persistTableHead:A,sortFunction:w,..._}=e,j={paginationComponentOptions:r,noDataComponent:t("NoData"),noHeader:!0,columns:i,data:u||[],dense:!0,paginationResetDefaultPage:p,subHeader:d,progressPending:P,subHeaderComponent:c,pagination:!0,paginationServer:f,paginationPerPage:q||10,paginationRowsPerPageOptions:g||[10,15,20,25,30],paginationTotalRows:l,onChangeRowsPerPage:x,onChangePage:m,selectableRows:h,onSelectedRowsChange:S,clearSelectedRows:b,progressComponent:(0,s.jsx)(n.A,{}),sortIcon:(0,s.jsx)("i",{className:"sort-icon fas fa-exchange-alt"}),sortServer:y,onSort:v,sortFunction:w,persistTableHead:A,className:"rki-table"};return(0,s.jsx)(a(),{...j})}u.defaultProps={subHeader:!1,pagination:!0,totalRows:null,pagServer:!0,onSelectedRowsChange:null,clearSelectedRows:!1,sortServer:!1,persistTableHead:!1};let l=u},57664:e=>{e.exports=require("dom-helpers/scrollbarSize")},58928:e=>{e.exports=require("dom-helpers/canUseDOM")},59717:e=>{e.exports=require("@restart/ui/DropdownContext")},60560:e=>{e.exports=require("dom-helpers/contains")},63033:e=>{e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},65447:e=>{e.exports=require("@restart/ui/Button")},67364:e=>{e.exports=require("@restart/hooks/useCallbackRef")},67943:(e,t,r)=>{r.a(e,async(e,s)=>{try{r.r(t),r.d(t,{default:()=>c});var i=r(8732),a=r(82015),o=r.n(a),n=r(33045),u=r(56084),l=r(63487),p=r(88751),d=e([n,l]);[n,l]=d.then?(await d)():d;let c=function(e){let[t,r]=(0,a.useState)([]),{t:s}=(0,p.useTranslation)("common"),[d,c]=(0,a.useState)(!1),[x,m]=(0,a.useState)(0),[g,q]=(0,a.useState)(10),[h]=(0,a.useState)([]),[P]=(0,a.useState)([]),[f,S]=(0,a.useState)(""),[b,y]=(0,a.useState)(null),[v,A]=(0,a.useState)(!1),w={sort:{created_at:"desc"},limit:g,page:1,query:{status:{$ne:"Request Pending"},vspace_status:{$ne:"Request Pending"}},select:"-country -dial_code -firstname -region -institution.address -institution.contact_name -institution.description -institution.dial_code -institution.document -institution.doc_src -institution.images -institution.images_src -institution.expertise -institution.hazards -institution.hazard_types -institution.header -institution.networks -institution.partners -institution.telephone -institution.twitter -institution.type -institution.unit -institution.user -mobile_number -position"},_=[{name:s("People.form.Username"),selector:"username",cell:e=>e.username,sortable:!0},{name:s("People.form.Email"),selector:"email",cell:e=>e.email,sortable:!0},{name:s("People.form.Role"),selector:"roles",cell:e=>e.roles?e.roles[0]:"",sortable:!0},{name:s("People.form.Organisation"),selector:"institution",cell:e=>e.institution&&e.institution.title?e.institution.title:"",sortable:!0}],j=async e=>{c(!0);let t=await l.A.get("/users",e);t&&Array.isArray(t.data)&&(r(t.data),m(t.totalCount),c(!1))},M=async(e,t)=>{c(!0),w.sort={[e.selector]:t},await j(w),y(w),c(!1)},R=async(e,t)=>{w.limit=e,w.page=t,c(!0);let s=await l.A.get("/users",w);s&&Array.isArray(s.data)&&(r(s.data),q(e),c(!1))},C=o().useMemo(()=>{let e=e=>{e&&(/^[^@]+@[^@]+\.[^@]+$/.test(e.toLowerCase())?w.query={...w.query,email:e}:w.query={...w.query,username:e}),j(w),w.query={status:{$ne:"Request Pending"},vspace_status:{$ne:"Request Pending"}}},t=()=>{r()},r=()=>{e(f)};return(0,i.jsx)(n.default,{onFilter:t=>{t&&t.label?(S(t.label),e(t.label)):(w.query={status:{$ne:"Request Pending"},vspace_status:{$ne:"Request Pending"}},S(""),j(w))},onClear:()=>{f&&(A(!v),S(""))},filterText:f,roles:h,onHandleSearch:r,institutions:P,onKeyPress:e=>{"Enter"===e.key&&t()}})},[f]);return(0,i.jsx)("div",{children:(0,i.jsx)(u.A,{columns:_,data:t,totalRows:x,subheader:!0,persistTableHead:!0,loading:d,onSort:M,sortServer:!0,pagServer:!0,resetPaginationToggle:v,subHeaderComponent:C,handlePerRowsChange:R,handlePageChange:e=>{w.limit=g,w.page=e,b&&(w.sort=b.sort),j(w)}})})};s()}catch(e){s(e)}})},68455:e=>{e.exports=import("redux-saga")},69722:e=>{e.exports=require("es6-promise")},74075:e=>{e.exports=require("zlib")},74716:e=>{e.exports=require("moment")},74987:e=>{e.exports=require("@restart/ui/ModalManager")},78097:e=>{e.exports=require("next-redux-saga")},78634:e=>{e.exports=require("@restart/ui/Anchor")},80237:(e,t)=>{Object.defineProperty(t,"A",{enumerable:!0,get:function(){return r}});var r=function(e){return e.PAGES="PAGES",e.PAGES_API="PAGES_API",e.APP_PAGE="APP_PAGE",e.APP_ROUTE="APP_ROUTE",e.IMAGE="IMAGE",e}({})},81366:e=>{e.exports=require("redux-auth-wrapper/connectedAuthWrapper")},81413:(e,t)=>{Object.defineProperty(t,"M",{enumerable:!0,get:function(){return function e(t,r){return r in t?t[r]:"then"in t&&"function"==typeof t.then?t.then(t=>e(t,r)):"function"==typeof t&&"default"===r?t:void 0}}})},81521:e=>{e.exports=require("dom-helpers/querySelectorAll")},81895:e=>{e.exports=require("@restart/hooks/useEventCallback")},82015:e=>{e.exports=require("react")},86842:e=>{e.exports=require("@restart/ui/SelectableContext")},87571:e=>{e.exports=require("dom-helpers/transitionEnd")},88751:e=>{e.exports=require("next-i18next")},93787:e=>{e.exports=require("redux-persist")},94947:e=>{e.exports=require("@restart/hooks/useTimeout")},96196:e=>{e.exports=require("next-redux-wrapper")},98320:e=>{e.exports=require("@restart/hooks/useIsomorphicEffect")},99460:e=>{e.exports=require("dom-helpers/css")},99800:e=>{e.exports=import("react-select")}};var t=require("../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[6089,9216,9616,2386],()=>r(23118));module.exports=s})();