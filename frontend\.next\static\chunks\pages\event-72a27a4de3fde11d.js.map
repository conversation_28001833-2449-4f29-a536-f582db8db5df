{"version": 3, "file": "static/chunks/pages/event-72a27a4de3fde11d.js", "mappings": "qNAqFA,MAnEqB,OAAC,YACpBA,CAAU,QAkEGC,EAjEbC,CAAQ,SACRC,CAAO,CAgEmB,qBA/D1BC,CAAoB,cACpBC,CAAY,CAOb,GACO,CAACC,EAAYC,EAAc,CAAGC,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAC,EAAE,EACzC,GAAEC,CAAC,CAAE,CAAGC,CAAAA,EAAAA,EAAAA,EAAAA,CAAcA,CAAC,UAIvBC,EAAc,MAAOC,IACzB,IAAMC,EAAW,MAAMC,EAAAA,CAAUA,CAACC,GAAG,CAAC,cAAeH,GAEjDC,GAAYG,MAAMC,OAAO,CAACJ,EAASK,IAAI,GAAG,EAC9BL,EAASK,IAAI,CAE/B,EAMA,MAJAC,CAAAA,EAAAA,EAAAA,SAAAA,CAASA,CAAC,KACRR,EAAY,CAAES,MAAO,CAAC,EAAGC,KAAM,CAAEC,MAAO,KAAM,CAAE,EAClD,EAAG,EAAE,EAGH,UAACC,EAAAA,CAASA,CAAAA,CAACC,KAAK,IAACC,UAAU,eACzB,WAACC,EAAAA,CAAGA,CAAAA,WACF,UAACC,EAAAA,CAAGA,CAAAA,CAACC,GAAI,EAAGH,UAAU,eACpB,UAACI,EAAAA,CAAWA,CAAAA,CACVC,KAAK,OACLL,UAAU,cACVM,YAActB,EAAE,uBAChBuB,aAAW,SACXC,MAAOjC,EACPkC,SAAUhC,MAId,UAACyB,EAAAA,CAAGA,CAAAA,CAACC,GAAI,WACP,WAACC,EAAAA,CAAWA,CAAAA,CACVM,GAAG,SACHH,aAAW,aACXI,mBAAiB,cACjBF,SAAU9B,EACV6B,MAAO5B,YAEP,UAACgC,SAAAA,CAAOJ,MAAO,YAAKxB,EAAE,mCACrBH,EAAWgC,GAAG,CAAC,CAACC,EAAWC,IAExB,UAACH,SAAAA,CAAmBJ,MAAOM,EAAKE,GAAG,UAChCF,EAAKjB,KAAK,EADAkB,aAU7B,8NC5EO,IAAME,EAAcC,CAAAA,EAAAA,EAAAA,CAAAA,CAAoBA,CAAC,CAC9CC,sBAAuB,KACjBC,EAAMC,WAAW,IAAID,EAAMC,WAAW,CAACC,KAAK,IAAIF,EAAMC,WAAW,CAACC,KAAK,CAAC,aAAa,CAK3FC,CAL6F,kBAKzE,aACtB,GAAG,EAE4BL,CAAAA,EAAAA,EAAAA,CAAAA,CAAoBA,CAAC,CAClDC,sBAAuB,KACjBC,EAAMC,WAAW,IAAID,EAAMC,WAAW,CAACC,KAAK,IAAIF,EAAMC,WAAW,CAACC,KAAK,CAAC,aAAa,CAK3FC,CAL6F,kBAKzE,kBACpBC,iBAAkB,IAAM,UAACC,EAAAA,OAAIA,CAAAA,CAAAA,EAC/B,GAAG,EAEyBP,CAAAA,EAAAA,EAAAA,CAAAA,CAAoBA,CAAC,CAC/CC,sBAAuB,CAACC,EAAOM,KAC7B,GAAIN,EAAMC,WAAW,EAAID,EAAMC,WAAW,CAACC,KAAK,CAC9C,CADgD,GAC5CF,EAAMC,WAAW,CAACC,KAAK,CAAC,aAAa,CACvC,CADyC,KAClC,QAEP,GAAIF,EAAMC,WAAW,CAACC,KAAK,CAAC,aAAa,EAAE,EAC/BA,KAAK,EAAII,EAAMJ,KAAK,CAACK,IAAI,EAAID,EAAMJ,KAAK,CAACK,IAAI,CAACX,GAAG,GAAKI,EAAMO,IAAI,CAACX,GAAG,CAC5E,CAD8E,KACvE,EAGb,CAEF,OAAO,CACT,EACAO,mBAAoB,cACtB,GAAG,EAE6BL,CAAAA,EAAAA,EAAAA,CAAAA,CAAoBA,CAAC,CACnDC,sBAAuB,CAACC,EAAOM,KAC7B,GAAIN,EAAMC,WAAW,EAAID,EAAMC,WAAW,CAACC,KAAK,EAAE,GAC5CF,EAAMC,WAAW,CAACC,KAAK,CAAC,aAAa,CACvC,CADyC,MAClC,OAEP,GAAIF,EAAMC,WAAW,CAACC,KAAK,CAAC,aAAa,EAAE,EAC/BA,KAAK,EAAII,EAAMJ,KAAK,CAACK,IAAI,EAAID,EAAMJ,KAAK,CAACK,IAAI,CAACX,GAAG,GAAKI,EAAMO,IAAI,CAACX,GAAG,CAC5E,CAD8E,MACvE,CAGb,CAEF,OAAO,CACT,EACAO,mBAAoB,mBACpBC,iBAAkB,IAAM,UAACC,EAAAA,OAAIA,CAAAA,CAAAA,EAC/B,GAAG,EAEoCP,CAAAA,EAAAA,EAAAA,CAAAA,CAAoBA,CAAC,CAC1DC,sBAAuB,KACjBC,EAAMC,WAAW,IAAID,EAAMC,WAAW,CAACO,MAAM,IAAIR,EAAMC,WAAW,CAACO,MAAM,CAAC,WAAW,CAK3FL,CAL6F,kBAKzE,yBACtB,GAAG,EAEYN,WAAWA,EAAC,yOCgB3B,MA3EgB,KACd,GAAM,GAAEjC,CAAC,CAAE,CAAGC,CAAAA,EAAAA,EAAAA,EAAAA,CAAcA,CAAC,KA0EhB4C,KAzEP,CAACC,CAyEaD,CAzESE,CAyER,CAzEwB,CAAGhD,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,EAAC,GACnD,CAACiD,EAAiBC,EAAmB,CAAGlD,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAC,MACjD,CAACmD,EAAQC,EAAU,CAAGpD,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAC,EAAE,EACjCqD,EAAa,IAAML,EAAgB,CAACD,GAEpCO,EAAoB,IAEtB,UAACC,IAAIA,CAACC,KAAK,qBAAqB7B,GAAG,eAA9B4B,UAA+C,UAACE,EAAAA,CAAMA,CAAAA,CAACC,QAAQ,YAAYC,KAAK,cAChF1D,EAAE,gBAKL2D,EAAc1B,CAAAA,EAAAA,EAAAA,WAAAA,CAAWA,CAAC,IAAM,UAACoB,EAAAA,CAAAA,IAEjCO,EAAgB,IACpBX,EAAmBY,EACrB,EAEA,MACE,WAAC/C,EAAAA,CAASA,CAAAA,CAACC,KAAK,IAACC,UAAU,gBACzB,UAACC,EAAAA,CAAGA,CAAAA,UACF,UAACC,EAAAA,CAAGA,CAAAA,CAACC,GAAI,YACP,UAAC2C,EAAAA,CAAWA,CAAAA,CAACjD,MAAOb,EAAE,qBAG1B,UAACiB,EAAAA,CAAGA,CAAAA,UACF,UAACC,EAAAA,CAAGA,CAAAA,CAACC,GAAI,YACP,UAAC4C,EAAAA,OAAgBA,CAAAA,CAACb,OAAQA,QAG9B,UAACjC,EAAAA,CAAGA,CAAAA,UACF,UAACC,EAAAA,CAAGA,CAAAA,CAACC,GAAI,YACP,UAAC6C,EAAAA,CAAsBA,CAAAA,CACrBC,QAAS,GAASL,EAAcC,GAChCb,gBAAiB,EAAE,CACnBY,cAAeA,QAIrB,UAAC3C,EAAAA,CAAGA,CAAAA,UACF,UAACC,EAAAA,CAAGA,CAAAA,CAACC,GAAI,GAAIH,UAAU,gBACrB,WAACC,EAAAA,CAAGA,CAAAA,WACF,UAACC,EAAAA,CAAGA,CAAAA,UACF,UAACyC,EAAAA,CAAAA,KAEH,UAACzC,EAAAA,CAAGA,CAAAA,UACF,UAACgD,IAAAA,CAAElD,UAAU,eAAM,WAACmD,QAAAA,WAAOnE,EAAE,qDAAqD,IAAC,UAACwD,EAAAA,CAAMA,CAAAA,CAACC,QAAQ,OAAOC,KAAK,KAAK1C,UAAU,oBAAoBoD,QAAShB,WAAapD,EAAE,4BAAmC,UAACqE,OAAAA,UAAK,SAAcrE,EAAE,sEAK3O,UAACiB,EAAAA,CAAGA,CAAAA,CAACD,UAAU,gBACb,UAACE,EAAAA,CAAGA,CAAAA,CAACC,GAAI,YACP,UAACmD,EAAAA,OAAWA,CAAAA,CAACtB,gBAAiBA,EAAiBG,UAAWA,QAG9D,UAACoB,EAAAA,CAAcA,CAAAA,CACbC,KAAM1B,EACN2B,OAAQrB,MAIhB,wFCbA,MA/CkD,OAAC,MACjDsB,EAAO,QAAQ,IACfC,CA6CaC,CA7CR,EAAE,SA6CkBA,EAAC,EA5Cd,EAAE,MACdvD,CAAI,MACJwD,CAAI,UACJC,CAAQ,CACRV,SAAO,CACPvD,OAAK,WACLkE,GAAY,CAAK,CAClB,UAsBC,GAAyC,UAAxB,OAAOD,EAASE,GAAG,EAAyC,UAAxB,OAAOF,EAASG,GAAG,CAKtE,UAACC,EAAAA,EAAMA,CAAAA,CACLJ,SAAUA,EACVD,KAAMA,EACNhE,MAAOA,GAAS6D,EAChBK,UAAWA,EACXX,QA/BgB,CA+BPe,GA9BPf,GAeFA,EAdoB,IADT,EAETM,KACAC,QAYmBS,IAXnBC,OACAhE,WACAyD,CACF,EAGe,UACbA,EACAQ,YAAa,IAAMR,CACrB,EAE6BS,EAEjC,IAIS,IAYX,6GC7BA,SAASC,EAAS9C,CAAoB,EACpC,GAAM,CAAE1C,CAAC,CAAE,CAAGC,CAAAA,EAAAA,EAAAA,EAAAA,CAAcA,CAAC,UACvBwF,EAA6B,CACjCC,gBAAiB1F,EAAE,cACnB,EACI,SACJ2F,CAAO,MACPlF,CAAI,WACJmF,CAAS,uBACTC,CAAqB,WACrBC,CAAS,oBACTC,CAAkB,qBAClBC,CAAmB,kBACnBC,CAAgB,aAChBC,CAAW,oBACXC,CAAkB,gBAClBC,CAAc,SACdC,CAAO,WACPC,CAAS,sBACTC,CAAoB,CACpBC,mBAAiB,CACjBC,YAAU,CACVC,QAAM,kBACNC,CAAgB,cAChBC,CAAY,CAEZ,CADA,EACGC,EACJ,CAAGnE,EAGEoE,EAAiB,4BACrBrB,EACAsB,gBAAiB/G,EAAE,IAP0C,MAQ7DgH,UAAU,UACVrB,EACAlF,KAAMA,GAAQ,EAAE,CAChBwG,OAAO,EACPC,2BAA4BrB,EAC5BsB,UAAWrB,EACXsB,gBAAiBf,qBACjBN,EACAsB,YAAY,EACZC,iBAAkBhB,EAClBiB,kBAAmBpB,GAA0C,GAC7DqB,eADwCrB,cACVD,GAA4B,CAAC,GAAI,GAAI,GAAI,CAA3BA,EAA+B,GAAG,CAC9EuB,oBAAqB7B,EACrB8B,oBAAqB1B,EACrB2B,aAAc1B,iBACdG,uBACAG,oBACAC,EACAoB,kBAAmB,UAACC,EAAAA,CAAYA,CAAAA,CAAAA,GAChCC,SAAU,UAACC,IAAAA,CAAE/G,UAAU,6CACvByF,SACAC,eACAE,mBACAD,EACA3F,UAAW,WACb,EACA,MACE,UAACgH,EAAAA,EAASA,CAAAA,CAAE,GAAGlB,CAAc,EAEjC,CAEAtB,EAASyC,YAAY,CAAG,CACtBd,WAAW,EACXE,WAAY,GACZzB,UAAW,KACXU,WAAW,EACXC,qBAAsB,KACtBC,mBAAmB,EACnBC,YAAY,EACZE,kBAAkB,CACpB,EAEA,MAAenB,QAAQA,EAAC,uJCHxB,MAnGyB,IACvB,GAAM,MAAE0C,CAAI,CAAE,CAAGjI,CAAAA,EAAAA,EAAAA,EAAAA,CAAcA,CAAC,GAkGnB8D,OAjGPoE,EAAcD,EAAKE,KAiGIrE,EAAC,CAjGG,CAC3B,QAAEb,CAAM,CAAE,CAAGR,EACb,CAAC2F,EAAeC,EAAiB,CAAGvI,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAyB,CAAC,GACtE,CAACwI,EAAQC,EAAU,CAAGzI,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAQ,EAAE,EACxC,CAAC0I,EAAcC,EAAgB,CAAG3I,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAM,CAAC,GACjD,CAAC4I,EAAYC,EAAc,CAAG7I,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAM,CAAC,GAsB7C8I,EAAc,KAClBH,EAAgB,MAChBE,EAAc,KAChB,EAEME,EAAgB,CAACC,EAAgB3D,EAAa4D,KAClDH,IACAH,EAAgBtD,GAChBwD,EAAc,CACZlE,KAAMqE,EAAUrE,IAAI,CACpBC,GAAIoE,EAAUpE,EAAE,CAChBU,UAAW0D,EAAU1D,SAAS,EAElC,EAEM4D,EAAsB,KAC1B,IAAMC,EAA2B,EAAE,CACnCC,IAAAA,OAAS,CAACjG,EAAQ,IAChBgG,EAAkBE,IAAI,CAAC,CACrBvI,MAAOyB,EAAMzB,KAAK,CAClB8D,GAAIrC,EAAMN,GAAG,CACbgD,IACE1C,EAAM+G,OAAO,EACb/G,EAAM+G,OAAO,CAACC,WAAW,EACzBhH,EAAM+G,OAAO,CAACC,WAAW,CAAC,EAAE,CAACC,QAAQ,CACvCtE,IACE3C,EAAM+G,OAAO,EACb/G,EAAM+G,OAAO,CAACC,WAAW,EACzBhH,EAAM+G,OAAO,CAACC,WAAW,CAAC,EAAE,CAACE,SAAS,CACxCnE,UAAW/C,EAAM+G,OAAO,EAAI/G,EAAM+G,OAAO,CAACrH,GAAG,EAEjD,GACAwG,EAAU,IAAIU,EAAkB,CAClC,EAOA,MALAxI,CAAAA,EAAAA,EAAAA,SAAAA,CAASA,CAAC,KACRuI,IACAX,EAAiBa,IAAAA,OAAS,CAACjG,EAAQ,eACrC,EAAG,CAACA,EAAO,EAGT,UAACuG,EAAAA,CAAOA,CAAAA,CACNC,QAASb,EACTT,SAAUD,EACVM,aAAcA,EACdE,WAAY,UAACgB,IAhEf,GAAM,MAAEC,CAAI,CAAE,CAAGC,SACjB,GAAYD,EAAKvE,SAAS,EAAIgD,CAAa,CAACuB,EAAKvE,SAAS,CAAC,CAEvD,UAACyE,KAAAA,UACEzB,CAAa,CAACuB,EAAKvE,SAAS,CAAC,CAACxD,GAAG,CAAC,CAACC,EAAMC,IAEtC,UAACgI,KAAAA,UACC,UAACzG,IAAIA,CAACC,KAAK,qBAAqB7B,GAAI,IAA8BI,MAAAA,CAA1BqG,EAAY,EAA/C7E,cAAsE,OAATxB,EAAKE,GAAG,WACvEF,EAAKjB,KAAK,IAFNkB,MAUZ,IACT,EA+CiB4H,CAAWC,KAAMjB,aAE7BJ,EAAOyB,MAAM,EAAI,EACdzB,EAAO1G,GAAG,CAAC,CAACC,EAAMC,KAChB,GAAID,EAAKkD,GAAG,CACV,CADY,KAEV,UAACJ,EAAAA,CAAYA,CAAAA,CAEXF,KAAM5C,EAAKjB,KAAK,CAChB8D,GAAI7C,EAAK6C,EAAE,CACXU,UAAWvD,EAAKuD,SAAS,CACzBR,KAAM,CACJoF,IAAK,8BACP,EACA7F,QAAS0E,EACThE,SAAUhD,GARLC,EAYb,GACA,MAGV,wFC3FA,MARyB,OAAC,CAAE+C,UAAQ,OAQrBoF,OARuBC,CAAY,QAQnBD,EARqBE,CAAQ,CAAS,GACnE,MACE,UAACC,EAAAA,EAAUA,CAAAA,CAACvF,SAAUA,EAAUqF,aAAcA,WAC5C,UAACG,MAAAA,UAAKF,KAGZ,ECdMG,EAAO,mBACPC,EAAQ,qBACRC,EAAO,eACPC,EAAW,kBAgTjB,EA/SkB,CAChB,CACE,YAAe,IA6SJC,OA5SX,EA4SoBA,EAAC,IA5SV,CACT,CACE,MAAS,SACX,EACD,EAEH,CACE,YAAeJ,EACf,QAAW,CACT,CACE,MAAS,SACX,EACD,EAEH,CACE,YAAeC,EACf,QAAW,CACT,CACE,MAAS,SACX,EACD,EAEH,CACE,YAAe,iBACf,YAAe,WACf,QAAW,CACT,CACE,WAAc,KAChB,EACD,EAEH,CACE,YAAe,yBACf,YAAeE,EACf,QAAW,CACT,CACE,MAAS,SACX,EACD,EAEH,CACE,YAAe,6BACf,YAAe,SACf,QAAW,CACT,CACE,WAAc,KAChB,EAEJ,EACA,CACE,YAAe,6BACf,YAAeH,EACf,QAAW,CACT,CACE,MAAS,SACX,EACD,EAEH,CACE,YAAe,0BACf,YAAeG,EACf,QAAW,CACT,CACE,MAAS,SACX,EACD,EAEH,CACE,YAAe,qBACf,YAAeA,EACf,QAAW,CACT,CACE,MAAS,SACX,EAEJ,EACA,CACE,YAAe,oBACf,YAAe,WACf,QAAW,CACT,CACE,MAAS,SACX,EACD,EAEH,CACE,YAAe,MACf,QAAW,CACT,CACE,WAAc,KAChB,EACD,EAEH,CACE,YAAe,MACf,YAAe,WACf,QAAW,CACT,CACE,MAAS,SACX,EACD,EAEH,CACE,YAAe,MACf,YAAe,cACf,QAAW,CACT,CACE,WAAc,KAChB,EACD,EAEH,CACE,YAAe,MACf,YAAeH,EACf,QAAW,CACT,CACE,MAAS,SACX,EACD,EAEH,CACE,YAAe,MACf,YAAeC,EACf,QAAW,CACT,CACE,MAAS,SACX,EACD,EAEH,CACE,YAAe,WACf,YAAe,gBACf,QAAW,CACT,CACE,MAAS,SACX,EACD,EAEH,CACE,YAAe,WACf,YAAeD,EACf,QAAW,CACT,CACE,MAAS,SACX,EACD,EAEH,CACE,YAAe,OACf,QAAW,CACT,CACE,WAAc,KAChB,EACD,EAEH,CACE,YAAe,OACf,YAAe,WACf,QAAW,CACT,CACE,MAAS,SACX,EACD,EAEH,CACE,YAAe,OACf,YAAe,cACf,QAAW,CACT,CACE,WAAc,KAChB,EACD,EAEH,CACE,YAAe,OACf,YAAeA,EACf,QAAW,CACT,CACE,MAAS,SACX,EAEJ,EACA,CACE,YAAe,OACf,YAAeC,EACf,QAAW,CACT,CACE,MAAS,SACX,EACD,EAEH,CACE,YAAeC,EACf,YAAe,WACf,QAAW,CACT,CACE,MAAS,SACX,EACD,EAEH,CACE,YAAeA,EACf,YAAeC,EACf,QAAW,CACT,CACE,MAAS,SACX,EAEJ,EACA,CACE,YAAeD,EACf,YAAeF,EACf,QAAW,CACT,CACE,MAAS,SACX,EACD,EAEH,CACE,YAAeE,EACf,YAAeD,EACf,QAAW,CACT,CACE,MAAS,SACX,EACD,EAEH,CACE,YAAe,aACf,YAAe,SACf,QAAW,CACT,CACE,WAAc,KAChB,EACD,EAEH,CACE,YAAe,UACf,QAAW,CACT,CACE,WAAc,KAChB,EACD,EAEH,CACE,YAAe,UACf,YAAeD,EACf,QAAW,CACT,CACE,MAAS,SACX,EACD,EAEH,CACE,YAAe,UACf,YAAeC,EACf,QAAW,CACT,CACE,MAAS,SACX,EACD,EAEH,CACE,YAAe,eACf,YAAe,gBACf,QAAW,CACT,CACE,MAAS,SACX,EACD,EAEH,CACE,YAAe,kBACf,YAAe,WACf,QAAW,CACT,CACE,MAAS,SACX,EACD,EAEH,CACE,YAAe,QACf,YAAe,WACf,QAAW,CACT,CACE,MAAS,SACX,EACD,EAEH,CACE,YAAe,QACf,YAAeD,EACf,QAAW,CACT,CACE,MAAS,SACX,EACD,EAEJ,2BCnND,MAzEwC,OAAC,SAyE1BK,GAxEbjC,CAAU,GAwEUiC,EAAC,SAvErBnC,CAAY,eACZoC,CAAa,UACbT,CAAQ,QACRU,EAAS,GAAG,OACZC,EAAQ,MAAM,UACd3C,CAAQ,CACR4C,OAAO,CAAC,SACRC,EAAU,CAAC,SACXvB,CAAO,CACR,GACO,QAAEwB,CAAM,CAAE,CAAGC,CAAAA,EAAAA,EAAAA,SAAAA,CAASA,GACtB,UAAEC,CAAQ,CAAEC,WAAS,CAAE,CAAIC,CAAAA,EAAAA,EAAAA,CAAAA,CAAaA,UAmB9C,EAAsB,SAAP,CAAQhB,MAAAA,UAAI,uBACtBc,EAGH,UAACd,MAAAA,CAAItJ,UAAU,yBACb,UAACsJ,MAAAA,CAAItJ,UAAU,WAAWuK,MAAO,OAAER,SAAOD,EAAQhG,SAAU,UAAW,WACrE,WAAC0G,EAAAA,EAASA,CAAAA,CACRC,kBAzBe,CACrBV,MAAOA,EACPD,OAA0B,UAAlB,OAAOA,EAAsB,GAAU,OAAPA,EAAO,MAAMA,CACvD,EAuBQY,OAhBOb,CAgBCa,EArBM,CACpB1G,IAAK,SAIyB2G,CAH9B1G,IAAK,SACP,EAmBQ+F,KAAMA,EACNY,OAhBU,CAgBFC,GAfdhK,EAAIiK,UAAU,CAAC,CACbC,OAAQpB,CACV,EACF,EAaQqB,QAAS,CACPf,EAhBWN,MAgBFM,EACTlG,WAAW,EACXkH,mBAAmB,EACnBC,mBAAmB,EACnBC,YAAY,EACZC,gBAAgB,EAChBC,gBAAgB,EAChBC,kBAAmB,EACrB,YAEClC,EACAzB,GAAcF,GAAgBA,EAAanD,WAAW,EACrD,UAAC4E,EAAgBA,CACfpF,SAAU2D,EAAanD,SADR4E,EACmB,GAClCC,aAAc,KAEZoC,QAAQC,GAAG,CAAC,qBACZ9C,GAAAA,GACF,WAECf,GAHCe,QA5BQ,UAACY,MAAAA,UAAI,mBAsC7B,2ICrEA,SAAStG,EAAuBtB,CAAkC,EAChE,GAAM,SAACuB,CAAO,CAAC,CAAGvB,EACZ,CAAC+J,EAAYC,EAAc,CAAG3M,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,EAAC,GACvC,CAAC4M,EAAQC,EAAU,CAAG7M,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAe,EAAE,EAC/C,CAACiD,EAAiBC,EAAmB,CAAGlD,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAW,EAAE,EAC7D,CAAEC,GAAC,CAAE,CAAGC,CAAAA,EAAAA,EAAAA,EAAAA,CAAcA,CAAC,UACvB4M,EAAe,CACnB,MAAS,CAAC,EACV,MAAS,IACT,KAAQ,CAAE,MAAS,KAAM,CAC3B,EAEMC,EAAiB,MAAOC,IAC5B,IAAM3M,EAAW,MAAMC,EAAAA,CAAUA,CAACC,GAAG,CAAC,eAAgByM,GACtD,GAAI3M,GAAYG,MAAMC,OAAO,CAACJ,EAASK,IAAI,EAAG,CAC5C,IAAMuM,EAA6B,EAAE,CAC/BC,EAAwB,EAAE,CAEhC9D,IAAAA,IAAM,CAAC/I,EAASK,IAAI,CAAE,CAACqB,EAAMqH,KAC3B,IAAM+D,EAAyB,CAC7B,GAAGpL,CAAI,CACPqL,WAAW,CACb,EACAH,EAAa5D,IAAI,CAAC8D,GAClBD,EAAY7D,IAAI,CAACtH,EAAKE,GAAG,CAC3B,GAEAiC,EAAQgJ,GACRhK,EAAmBgK,GACnBL,EAAUI,EACZ,CACF,EAEAtM,CAAAA,EAAAA,EAAAA,SAAAA,CAASA,CAAC,KACRoM,EAAeD,EACjB,EAAG,EAAE,EAmBL,IAAMO,EAA+B,IACnC,IAAMC,EAAiB,IAAIV,EAAO,CAC9BW,EAAyB,IAAItK,EAAgB,CAEjDqK,EAAeE,OAAO,CAAC,CAACzL,EAAMC,KACxBD,EAAK0L,IAAI,GAAKjI,EAAEkI,MAAM,CAAC9I,EAAE,EAAE,CAC7B0I,CAAc,CAACtL,EAAM,CAACoL,SAAS,CAAG5H,EAAEkI,MAAM,CAACC,OAAO,CAC7CnI,EAAEkI,MAAM,CAACC,OAAO,CAGnBJ,CAHqB,CAGElE,IAAI,CAACtH,EAAKE,GAAG,EAFpCsL,EAAyBA,EAAuBK,MAAM,CAACC,GAAKA,IAAM9L,EAAKE,GAAG,EAKhF,GAEAiB,EAAmBqK,GACnBrJ,EAAQqJ,GACRZ,GAAc,GACdE,EAAUS,EACZ,EAcA,MACE,WAAC/C,MAAAA,CAAItJ,UAAU,qCACb,UAAC6M,EAAAA,CAAIA,CAACC,KAAK,EACTzM,KAAK,WACLsD,GAAK,MACLoJ,MAAO/N,EAAE,cACT0N,QAASjB,EACThL,SAzDmB,CAyDTuM,GAxDd,IAAMX,EAAiBV,EAAO9K,GAAG,CAACC,GAAS,EACzC,EADyC,CACtCA,CAAI,CACPqL,UAAW7K,EAAMmL,MAAM,CAACC,OAAO,CACjC,GAEIO,EAA6B,EAAE,CAC/B3L,EAAMmL,MAAM,CAACC,OAAO,EAAE,CACxBO,EAAmBZ,EAAexL,GAAG,CAACC,GAAQA,EAAKE,IAAG,EAGxDiC,EAAQgK,GACRhL,EAAmBgL,GACnBvB,EAAcpK,EAAMmL,MAAM,CAACC,OAAO,EAClCd,EAAUS,EACZ,IA4CKV,EAAO9K,GAAG,CAAC,CAACC,EAAMC,IAEf,UAAC8L,EAAAA,CAAIA,CAACC,KAAK,EAETzM,KAAK,WACLsD,GAAI7C,EAAK0L,IAAI,CACbO,MAAOjM,EAAKjB,KAAK,CACjBW,MAAOM,EAAK0L,IAAI,CAChB/L,SAAU2L,EACVM,QAASf,CAAM,CAAC5K,EAAM,CAACoL,SAAS,EAN3BpL,IAUX,UAACyB,EAAAA,CAAMA,CAAAA,CAACY,QAlCW,CAkCF8J,IAjCnB,IAAMb,EAAiBV,EAAO9K,GAAG,CAACC,GAAS,EACzC,EADyC,CACtCA,CAAI,CACPqL,WAAW,EACb,GAEAlK,EAAmB,EAAE,EACrByJ,GAAc,GACdE,EAAUS,GACVpJ,EAAQ,EAAE,CACZ,EAwBqCjD,UAAU,0BAAkBhB,EAAE,gBAGrE,CAEAgE,EAAuBiE,YAAY,CAAG,CACpChE,QAAS,KAAS,CACpB,EAEA,MAAeD,sBAAsBA,EAAC,wCCzIvB,SAASF,EAAYpB,CAAuB,EACzD,MACE,UAACyL,KAAAA,CAAGnN,UAAU,wBAAgB0B,EAAM7B,KAAK,EAE7C,yMCKA,IAAMuN,EAAU,IACd,GAAM,MAAElG,CAAI,CAAE,CAAGjI,CAAAA,EAAAA,EAAAA,EAAAA,CAAcA,CAAC,UAC1BkI,EAAgC,OAAlBD,EAAKE,QAAQ,CAAY,KAAOF,EAAKE,QAAQ,CAC3D,SAAEiG,CAAO,CAAE,CAAG3L,EACpB,MACE,UAACoH,KAAAA,UACEuE,EAAQxM,GAAG,CAAC,CAACC,EAAWC,IAEdD,GAAQA,EAAKE,GAAG,EAAIF,EAAKjB,KAAK,EAAIiB,EAAKjB,KAAK,CAACsH,EAAY,CAC9D,UAAC4B,KAAAA,UACC,UAACzG,IAAIA,CAACC,KAAK,sBAAsB7B,GAAI,cAAhC4B,EAAyD,OAATxB,EAAKE,GAAG,WAC1DF,EAAKjB,KAAK,CAACsH,EAAY,CAACmG,QAAQ,MAF5BvM,GAMT,KAMZ,EAgRA,EA9QA,SAAqBW,CAAU,EAC7B,IAAM6L,EAASpD,CAAAA,EAAAA,EAAAA,KA6QF7G,IA7QE6G,CAASA,GAClB,GAAEnL,CAAC,CAAE,CAAGC,CAAAA,EAAAA,EAAAA,EAAAA,CAAcA,CAAC,UACvB,WAAEkD,CAAS,iBAAEH,CAAe,CAAE,CAAGN,EACjC,CAACnD,EAAYiP,EAAc,CAAGC,EAAAA,QAAc,CAAC,IAC7C,CAAC7O,EAAc8O,EAAgB,CAAGD,EAAAA,QAAc,CAAC,IACjD,CAAC5I,EAAuB8I,EAAyB,CACrDF,EAAAA,QAAc,EAAC,GACX,CAACG,EAAWC,EAAe,CAAG9O,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAC,EAAE,EACzC,CAACsG,EAASyI,EAAW,CAAG/O,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAC,IACjC,CAAC6F,EAAWmJ,EAAa,CAAGhP,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAC,GACrC,CAACiP,EAASC,EAAW,CAAGlP,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAC,IACjC,CAACmP,EAASC,EAAW,CAAGpP,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAC,GACjC,CAACqP,EAAUC,EAAY,CAAGtP,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAM,MAExCuP,EAAmB,CACvB1O,KAAM,CAAE2O,WAAY,MAAO,EAC3BC,MAAM,EACNC,SAAU,CACR,CAAEC,KAAM,UAAWC,OAAQ,mBAAoB,EAC/C,CAAED,KAAM,cAAeC,OAAQ,OAAQ,EACvC,CAAED,KAAM,SAAUC,OAAQ,OAAQ,EACnC,CACDC,MAAOZ,EACPa,KAAM,EACNlP,MAAO,CAAC,EACRgP,OACE,6MACJ,EAEM,CAACG,EAAUC,EAAY,CAAGhQ,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAACuP,GAEnC3J,EAAU,CACd,CACEjB,KAAM1E,EAAE,wBACRgQ,SAAU,QACVC,SAAU,GACVlF,MAAO,MACPmF,KAAM,GACJ,UAAC5M,IAAIA,CAACC,KAAK,qBAAqB7B,GAAI,eAA/B4B,MAAoD,CAAN6M,EAAEnO,GAAG,WACrDmO,EAAEtP,KAAK,EAGd,EACA,CACE6D,KAAM1E,EAAE,wBACRgQ,SAAU,UACVC,UAAU,EACVC,KAAM,GACJC,EAAE9G,OAAO,EAAI8G,EAAE9G,OAAO,CAACxI,KAAK,CAC1B,UAACyC,IAAIA,CACHC,KAAK,uBACL7B,GAAI,aAFD4B,IAEgC,OAAd6M,EAAE9G,OAAO,CAACrH,GAAG,WAEjCmO,EAAE9G,OAAO,CAACxI,KAAK,GAGlB,EAEN,EACA,CACE6D,KAAM1E,EAAE,2BACRgQ,SAAU,cACVC,SAAU,GACVC,KAAM,GACJC,EAAEC,WAAW,EAAID,EAAEC,WAAW,CAACvP,KAAK,CAAGsP,EAAEC,WAAW,CAACvP,KAAK,CAAG,EACjE,EACA,CACE6D,KAAM1E,EAAE,uBACRgQ,SAAU,SACVE,KAAM,GAAY,UAAC9B,EAAAA,CAAQC,QAAS8B,EAAEE,MAAM,EAC9C,EACA,CACE3L,KAAM1E,EAAE,+BACRgQ,SAAU,aACVC,UAAU,EACVC,KAAM,GAAYI,IAAOH,EAAEI,UAAU,EAAEC,MAAM,CAAC,QAChD,EACA,CACE9L,KAAM1E,EAAE,EAHgBsQ,0BAIxBN,SAAU,aACVC,UAAU,EACVC,KAAOC,GAAWG,IAAOH,EAAEM,UAAU,EAAED,MAAM,CAAC,QAChD,EACD,CAEKE,EAAgB,MAAOC,CAJDL,GAK1BxB,GAAW,GAGPP,EAAO5N,KAAK,EAAI4N,EAAO5N,KAAK,CAAC0I,OAAO,EAAE,CACxCsH,EAAgBhQ,KAAK,CAAC,OAAU,CAAG4N,EAAO5N,KAAK,CAAC0I,OAAAA,EAI1B,MAAM,CAA1BrG,EAEF,OAAO2N,EAAgBhQ,KAAK,CAAC,YAAe,CACnCqC,GAA8B,GAAdgH,MAAM,CAE/B2G,EAAgBhQ,KAAK,CAAC,YAAe,CAAG,CAAC,eAAe,CAGxDgQ,EAAgBhQ,KAAK,CAAC,YAAe,CAAGqC,EAI1C,IAAM5C,EAAW,MAAMC,EAAAA,CAAUA,CAACC,GAAG,CAAC,SAAUqQ,GAC5CvQ,GAAYG,MAAMC,OAAO,CAACJ,EAASK,IAAI,GAAG,CAC5CoO,EAAezO,EAASK,IAAI,EAC5B0C,EAAU/C,EAASK,IAAI,EACvBsO,EAAa3O,EAASwQ,UAAU,GAGlC9B,GAAW,EACb,EAoBM9I,EAAsB,MAAO6K,EAAiBhB,KAClDP,EAAYM,KAAK,CAAGiB,EACpBvB,EAAYO,IAAI,CAAGA,EACnBf,GAAW,GAGPP,EAAO5N,KAAK,EAAI4N,EAAO5N,KAAK,CAAC0I,OAAO,EAAE,CACxCiG,EAAY3O,KAAK,CAAC,OAAU,CAAG4N,EAAO5N,KAAK,CAAC0I,OAAAA,EAItB,MAAM,CAA1BrG,EACF,OAAOsM,EAAY3O,KAAK,CAAC,YAAe,CACJ,GAAG,CAA9BqC,EAAgBgH,MAAM,CAC/BsF,EAAY3O,KAAK,CAAC,YAAe,CAAG,CAAC,eAAe,CAEpD2O,EAAY3O,KAAK,CAAC,YAAe,CAAGqC,EAItCpD,IACG0P,EAAY3O,KAAK,CAAG,CAAE,GAAtB2O,EAAqC3O,KAAK,CAAEyP,YAAaxQ,EAAa,EAGzEwP,IAAaE,EAAY1O,IAAI,CAAGwO,CAAnBE,CAA4B1O,IAAAA,EAGzC,IAAMR,EAAW,MAAMC,EAAAA,CAAUA,CAACC,GAAG,CAAC,SAAUgP,GAC5ClP,GAAYG,MAAMC,OAAO,CAACJ,EAASK,IAAI,GAAG,CAC5CoO,EAAezO,EAASK,IAAI,EAC5B0C,EAAU/C,EAASK,IAAI,EACvBwO,EAAW4B,GACX/B,GAAW,IAGbK,EAAWU,EACb,EAGAnP,CAAAA,EAAAA,EAAAA,SAAAA,CAASA,CAAC,KACRoP,EAASD,IAAI,CAAG,EAChBa,EAAcZ,EAChB,EAAG,CAAC9M,EAAiBuL,EAAO,EAE5B7N,CAAAA,EAAAA,EAAAA,SAAAA,CAASA,CAAC,KACRgQ,EAAcZ,EAChB,EAAG,CAACA,EAAS,EAGb,IAAMgB,EAAa,MAAOC,EAAaC,KACrClC,EAAW,IACXQ,EAAY1O,IAAI,CAAG,CACjB,CAACmQ,EAAOf,QAAQ,CAAC,CAAEgB,CACrB,EACApR,IACG0P,EAAY3O,KAAK,CAAG,CAAE,GAAtB2O,EAAqC3O,KAAK,CAAEyP,YAAaxQ,EAAa,EAC1D,KAAfL,CACG+P,GAAAA,EAAY3O,KAAK,CAAG,CAAE,GAAG2O,EAAY3O,KAAK,CAAEE,MAAOtB,EAAW,EAEjE,MAAMmR,EAAcpB,GACpBD,EAAYC,GACZR,EAAW,GACb,EAEMmC,EAAY,CAACC,EAAQrB,KACrBqB,GAAG,EACIvQ,KAAK,CAAC,KAAQ,CAAGuQ,EAC1BpB,EAASD,IAAI,CAAGA,GAGhB,OAAOC,EAASnP,KAAK,CAACE,KAAK,CAC3BkP,EAAY,CAAE,GAAGD,CAAQ,EAE7B,EAEMqB,EAAoBC,CAAAA,EAAAA,EAAAA,MAAAA,CAAMA,CAC9BjI,IAAAA,QAAU,CACR,CAAC+H,EAAGrB,IAASoB,EAAUC,EAAGrB,GAC1BwB,OAAOC,KAAgC,GAAK,MAE9CC,OAAO,CAEHC,EAAyB/C,EAAAA,OAAa,CAAC,KAQ3C,IAAMgD,EAAyB,IAC7B/C,EAAgB7O,GACZA,EACFiQ,EAASnP,KAAK,CAAC,EADD,SACe,CAAGd,EAGhC,OAAOiQ,EAASnP,KAAK,CAACyP,WAAW,CACjCL,EAAY,CAAE,GAAGD,CAAQ,EAE7B,EAOA,MACE,UAACtQ,EAAAA,OAAYA,CAAAA,CACXC,SAPiB,CAOPiS,GANZlD,EAAcjJ,EAAEkI,MAAM,CAACjM,KAAK,EAC5B2P,EAAkB5L,EAAEkI,MAAM,CAACjM,KAAK,CAAE0N,EACpC,EAKIvP,qBAAuB4F,GAAWkM,EAAuBlM,EAAEkI,MAAM,CAACjM,KAAK,EACvE9B,QA3BgB,CA2BPiS,IA1BPpS,IACFoP,EAAyB,CAAC9I,GAC1B2I,EAFc,IAIlB,EAuBIjP,WAAYA,EACZK,aAAcA,GAGpB,EAAG,CAACL,EAAYsG,EAAuBjG,EAAcoD,EAAgB,EAErE,MACE,UAACwC,EAAAA,CAAQA,CAAAA,CACPG,QAASA,EACTlF,KAAMmO,EACNhJ,UAAWA,EACXS,QAASA,EACTP,SAAS,IACTa,gBAAgB,IAChBD,OAAQoK,EACRrK,UAAU,IACVH,WAAW,EACXP,mBAAoByL,EACpBxL,oBAAqBA,EACrBC,iBAnJsB4J,CAmJJ5J,GAlJpBqJ,EAAYM,KAAK,CAAGZ,EACpBM,EAAYO,IAAI,CAAGA,EAGnBjQ,IACG0P,EAAY3O,KAAK,CAAG,CAAE,GAAtB2O,EAAqC3O,KAAK,CAAEyP,YAAaxQ,EAAa,EAGzEwP,IAAaE,EAAY1O,IAAI,CAAGwO,CAAnBE,CAA4B1O,IAAAA,EAGzC8P,EAAcpB,GACdH,EAAWU,EACb,GAwIF,mBC/SA,4CACA,SACA,WACA,OAAe,EAAQ,IAAoC,CAC3D,EACA,UAFsB", "sources": ["webpack://_N_E/./pages/event/EventsTableFilter.tsx", "webpack://_N_E/./pages/event/permission.tsx", "webpack://_N_E/./pages/event/index.tsx", "webpack://_N_E/./components/common/RKIMapMarker.tsx", "webpack://_N_E/./components/common/RKITable.tsx", "webpack://_N_E/./pages/event/ListMapcontainer.tsx", "webpack://_N_E/./components/common/RKIMapInfowindow.tsx", "webpack://_N_E/./components/common/mapStyles.tsx", "webpack://_N_E/./components/common/RKIMap1.tsx", "webpack://_N_E/./components/common/RegionsMultiCheckboxes.tsx", "webpack://_N_E/./components/common/PageHeading.tsx", "webpack://_N_E/./pages/event/EventsTable.tsx", "webpack://_N_E/?bbd8"], "sourcesContent": ["//Import Library\r\nimport { useEffect, useState } from \"react\";\r\nimport _ from \"lodash\";\r\nimport {\r\n  Col,\r\n  Container,\r\n  FormControl,\r\n  FormGroup,\r\n  FormLabel,\r\n  Row,\r\n} from \"react-bootstrap\";\r\n\r\n//Import services/components\r\nimport apiService from \"../../services/apiService\";\r\nimport React from \"react\";\r\nimport { useTranslation } from 'next-i18next';\r\n\r\n\r\nconst EventsFilter = ({\r\n  filterText,\r\n  onFilter,\r\n  onClear,\r\n  onFilterHazardChange,\r\n  filterHazard,\r\n}: {\r\n  filterText: any,\r\n  onFilter: any,\r\n  onClear: any,\r\n  onFilterHazardChange: any,\r\n  filterHazard: any,\r\n}) => {\r\n  const [hazardType, setHazardType] = useState([]);\r\n  const { t } = useTranslation('common');\r\n\r\n\r\n\r\n  const getNetworks = async (params: any) => {\r\n    const response = await apiService.get(\"/hazardtype\", params);\r\n\r\n    if (response && Array.isArray(response.data)) {\r\n      setHazardType(response.data);\r\n    }\r\n  };\r\n\r\n  useEffect(() => {\r\n    getNetworks({ query: {}, sort: { title: \"asc\" } });\r\n  }, []);\r\n\r\n  return (\r\n    <Container fluid className=\"p-0\">\r\n      <Row>\r\n        <Col xs={6} className=\"p-0\">\r\n          <FormControl\r\n            type=\"text\"\r\n            className=\"searchInput\"\r\n            placeholder= {t(\"Events.table.Search\")}\r\n            aria-label=\"Search\"\r\n            value={filterText}\r\n            onChange={onFilter}\r\n          />\r\n        </Col>\r\n\r\n        <Col xs={6}>\r\n          <FormControl\r\n            as=\"select\"\r\n            aria-label=\"HazardType\"\r\n            aria-placeholder=\"Hazard Type\"\r\n            onChange={onFilterHazardChange}\r\n            value={filterHazard}\r\n          >\r\n            <option value={\"\"}>{t(\"Events.forms.SelectHazardType\")}</option>\r\n            {hazardType.map((item: any, index) => {\r\n              return (\r\n                <option key={index} value={item._id}>\r\n                  {item.title}\r\n                </option>\r\n              );\r\n            })}\r\n          </FormControl>\r\n        </Col>\r\n      </Row>\r\n    </Container>\r\n  );\r\n};\r\n\r\nexport default EventsFilter;\r\n", "//Import Library\r\nimport React from 'react';\r\nimport connectedAuthWrapper from 'redux-auth-wrapper/connectedAuthWrapper';\r\n\r\n//Import services/components\r\nimport R403 from \"../r403\";\r\n\r\nexport const canAddEvent = connectedAuthWrapper({\r\n  authenticatedSelector: (state) => {\r\n    if (state.permissions && state.permissions.event && state.permissions.event['create:any']) {\r\n      return true;\r\n    }\r\n    return false;\r\n  },\r\n  wrapperDisplayName: 'CanAddEvent',\r\n});\r\n\r\nexport const canAddEventForm = connectedAuthWrapper({\r\n  authenticatedSelector: (state) => {\r\n    if (state.permissions && state.permissions.event && state.permissions.event['create:any']) {\r\n      return true;\r\n    }\r\n    return false;\r\n  },\r\n  wrapperDisplayName: 'CanAddEventForm',\r\n  FailureComponent: () => <R403/>\r\n});\r\n\r\nexport const canEditEvent = connectedAuthWrapper({\r\n  authenticatedSelector: (state, props) => {\r\n    if (state.permissions && state.permissions.event) {\r\n      if (state.permissions.event['update:any']) {\r\n        return true;\r\n      } else { //if update:own\r\n        if (state.permissions.event['update:own']) {\r\n          if (props.event && props.event.user && props.event.user._id === state.user._id) {\r\n            return true;\r\n          }  \r\n        }\r\n      }\r\n    }\r\n    return false;\r\n  },\r\n  wrapperDisplayName: 'CanEditEvent',\r\n});\r\n\r\nexport const canEditEventForm = connectedAuthWrapper({\r\n  authenticatedSelector: (state, props) => {\r\n    if (state.permissions && state.permissions.event) {\r\n      if (state.permissions.event['update:any']) {\r\n        return true;\r\n      } else { //if update:own\r\n        if (state.permissions.event['update:own']) {\r\n          if (props.event && props.event.user && props.event.user._id === state.user._id) {\r\n            return true;\r\n          }\r\n        }\r\n      }\r\n    }\r\n    return false;\r\n  },\r\n  wrapperDisplayName: 'CanEditEventForm',\r\n  FailureComponent: () => <R403/>\r\n});\r\n\r\nexport const canViewDiscussionUpdate = connectedAuthWrapper({\r\n  authenticatedSelector: (state) => {\r\n    if (state.permissions && state.permissions.update && state.permissions.update['read:any']) {\r\n      return true;\r\n    }\r\n    return false;\r\n  },\r\n  wrapperDisplayName: 'CanViewDiscussionUpdate',\r\n});\r\n\r\nexport default canAddEvent;", "//Import Library\r\nimport React, { useState } from \"react\";\r\nimport Link from 'next/link';\r\nimport Button from 'react-bootstrap/Button';\r\nimport { Container, Col, Row } from \"react-bootstrap\";\r\n\r\n//Import services/components\r\nimport PageHeading from \"../../components/common/PageHeading\";\r\nimport EventsTable from \"./EventsTable\";\r\nimport ListMapContainer from \"./ListMapcontainer\";\r\nimport { useTranslation } from 'next-i18next';\r\nimport { serverSideTranslations } from 'next-i18next/serverSideTranslations';\r\nimport ContactUsModal from \"../../components/layout/modals/contact-us\";\r\nimport { canAddEvent } from \"./permission\";\r\nimport RegionsMultiCheckboxes from \"../../components/common/RegionsMultiCheckboxes\";\r\n\r\nconst Project = () => {\r\n  const { t } = useTranslation('common');\r\n  const [isContactModalEnbled, setContactModal] = useState(false);\r\n  const [selectedRegions, setSelectedRegions] = useState(null);\r\n  const [events, setEvents] = useState([]);\r\n  const onSetModal = () => setContactModal(!isContactModalEnbled);\r\n\r\n  const AddEventComponent = () => {\r\n    return (\r\n      <Link href='/event/[...routes]' as='/event/create' ><Button variant=\"secondary\" size=\"sm\">\r\n          {t('addEvent')}\r\n        </Button></Link>\r\n    );\r\n  }\r\n\r\n  const CanAddEvent = canAddEvent(() => <AddEventComponent />);\r\n\r\n  const regionHandler = (val: any) => {\r\n    setSelectedRegions(val);\r\n  }\r\n\r\n  return (\r\n    <Container fluid className=\"p-0\">\r\n      <Row>\r\n        <Col xs={12}>\r\n          <PageHeading title={t('menu.events')} />\r\n        </Col>\r\n      </Row>\r\n      <Row>\r\n        <Col xs={12}>\r\n          <ListMapContainer events={events} />\r\n        </Col>\r\n      </Row>\r\n      <Row>\r\n        <Col xs={12}>\r\n          <RegionsMultiCheckboxes\r\n            filtreg={(val) => regionHandler(val)}\r\n            selectedRegions={[]}\r\n            regionHandler={regionHandler}\r\n          />\r\n        </Col>\r\n      </Row>\r\n      <Row>\r\n        <Col xs={12} className=\"ps-4\">\r\n          <Row>\r\n            <Col>\r\n              <CanAddEvent />\r\n            </Col>\r\n            <Col>\r\n              <p className=\"m-0\"><small>{t(\"Events.table.Doyouknowofaneventthatneedstobeadded\")} <Button variant=\"link\" size=\"sm\" className=\"p-0 outlineButton\" onClick={onSetModal}>{t(\"Events.table.Clickhere\")}</Button><span>&nbsp;</span>{t(\"Events.table.toinformthePublicHealthIntelligenceTeam\")}</small></p>\r\n            </Col>\r\n          </Row>\r\n        </Col>\r\n      </Row>\r\n      <Row className=\"mt-1\">\r\n        <Col xs={12}>\r\n          <EventsTable selectedRegions={selectedRegions} setEvents={setEvents} />\r\n        </Col>\r\n      </Row>\r\n      <ContactUsModal\r\n        show={isContactModalEnbled}\r\n        onHide={onSetModal}\r\n      />\r\n    </Container>\r\n  );\r\n}\r\n\r\nexport async function getStaticProps({ locale }: { locale: string }) {\r\n  return {\r\n    props: {\r\n      ...(await serverSideTranslations(locale, ['common'])),\r\n    },\r\n  }\r\n}\r\n\r\nexport default Project;\r\n\r\n\r\n\r\n\r\n\r\n\r\n", "import React from 'react';\r\nimport { Marker } from '@react-google-maps/api';\r\n\r\ninterface RKIMapMarkerProps {\r\n  name?: string;\r\n  id?: string;\r\n  countryId?: string;\r\n  type?: string;\r\n  icon?: {\r\n    url: string;\r\n    scaledSize?: google.maps.Size;\r\n  };\r\n  position: {\r\n    lat: number;\r\n    lng: number;\r\n  };\r\n  onClick?: (props: any, marker: any, e: any) => void;\r\n  title?: string;\r\n  draggable?: boolean;\r\n}\r\n\r\nconst RKIMapMarker: React.FC<RKIMapMarkerProps> = ({\r\n  name = 'Marker',\r\n  id = '',\r\n  countryId = '',\r\n  type,\r\n  icon,\r\n  position,\r\n  onClick,\r\n  title,\r\n  draggable = false,\r\n}) => {\r\n  const handleClick = (e: google.maps.MapMouseEvent) => {\r\n    if (onClick) {\r\n      const markerProps = {\r\n        name,\r\n        id,\r\n        countryId,\r\n        type,\r\n        position,\r\n      };\r\n\r\n      // Create a marker-like object for compatibility with old onClick signature\r\n      const marker = {\r\n        position,\r\n        getPosition: () => position,\r\n      };\r\n\r\n      onClick(markerProps, marker, e);\r\n    }\r\n  };\r\n\r\n  // Ensure position is valid\r\n  if (!position || typeof position.lat !== 'number' || typeof position.lng !== 'number') {\r\n    return null;\r\n  }\r\n\r\n  return (\r\n    <Marker\r\n      position={position}\r\n      icon={icon}\r\n      title={title || name}\r\n      draggable={draggable}\r\n      onClick={handleClick}\r\n    />\r\n  );\r\n};\r\n\r\nexport default RKIMapMarker;\r\n", "//Import Library\r\nimport React from 'react'\r\nimport DataTable  from 'react-data-table-component';\r\n\r\n//Import services/components\r\nimport { useTranslation } from 'next-i18next';\r\nimport CustomLoader from './CustomLoader';\r\n\r\ninterface RKITableProps {\r\n  columns: any[];\r\n  data: any[];\r\n  totalRows?: number;\r\n  resetPaginationToggle?: boolean;\r\n  subheader?: boolean;\r\n  subHeaderComponent?: React.ReactNode;\r\n  handlePerRowsChange?: (newPerPage: number, page: number) => void;\r\n  handlePageChange?: (page: number) => void;\r\n  rowsPerPage?: number[];\r\n  defaultRowsPerPage?: number;\r\n  selectableRows?: boolean;\r\n  loading?: boolean;\r\n  pagServer?: boolean;\r\n  onSelectedRowsChange?: (selectedRows: any) => void;\r\n  clearSelectedRows?: boolean;\r\n  sortServer?: boolean;\r\n  onSort?: (column: any, sortDirection: string) => void;\r\n  persistTableHead?: boolean;\r\n  sortFunction?: (rows: any[], field: any, direction: 'asc' | 'desc') => any[];\r\n  noHeader?: boolean;\r\n  dense?: boolean;\r\n  pagination?: boolean;\r\n  paginationServer?: boolean;\r\n  paginationTotalRows?: number;\r\n  subHeaderAlign?: string;\r\n  [key: string]: any;\r\n}\r\n\r\nfunction RKITable(props: RKITableProps) {\r\n  const { t } = useTranslation('common');\r\n  const paginationComponentOptions = {\r\n    rowsPerPageText: t('Rowsperpage'),\r\n    };\r\n  const {\r\n    columns,\r\n    data,\r\n    totalRows,\r\n    resetPaginationToggle,\r\n    subheader,\r\n    subHeaderComponent,\r\n    handlePerRowsChange,\r\n    handlePageChange,\r\n    rowsPerPage,\r\n    defaultRowsPerPage,\r\n    selectableRows,\r\n    loading,\r\n    pagServer,\r\n    onSelectedRowsChange,\r\n    clearSelectedRows,\r\n    sortServer,\r\n    onSort,\r\n    persistTableHead,\r\n    sortFunction,\r\n    // Filter out any unknown props that might cause DOM warnings\r\n    ...otherProps\r\n  } = props;\r\n\r\n  // Only pass known DataTable props to avoid DOM warnings\r\n  const dataTableProps = {\r\n    paginationComponentOptions,\r\n    noDataComponent: t(\"NoData\"),\r\n    noHeader: true,\r\n    columns,\r\n    data: data || [],\r\n    dense: true,\r\n    paginationResetDefaultPage: resetPaginationToggle,\r\n    subHeader: subheader,\r\n    progressPending: loading,\r\n    subHeaderComponent,\r\n    pagination: true,\r\n    paginationServer: pagServer,\r\n    paginationPerPage: defaultRowsPerPage ? defaultRowsPerPage : 10,\r\n    paginationRowsPerPageOptions: rowsPerPage ? rowsPerPage : [10, 15, 20, 25, 30],\r\n    paginationTotalRows: totalRows,\r\n    onChangeRowsPerPage: handlePerRowsChange,\r\n    onChangePage: handlePageChange,\r\n    selectableRows,\r\n    onSelectedRowsChange,\r\n    clearSelectedRows,\r\n    progressComponent: <CustomLoader/>,\r\n    sortIcon: <i className=\"sort-icon fas fa-exchange-alt\" />,\r\n    sortServer,\r\n    onSort,\r\n    sortFunction,\r\n    persistTableHead,\r\n    className: \"rki-table\"\r\n  };\r\n  return (\r\n    <DataTable {...dataTableProps} />\r\n  )\r\n};\r\n\r\nRKITable.defaultProps = {\r\n  subHeader: false,\r\n  pagination: true,\r\n  totalRows: null,\r\n  pagServer: true,\r\n  onSelectedRowsChange: null,\r\n  clearSelectedRows: false,\r\n  sortServer: false,\r\n  persistTableHead: false\r\n}\r\n\r\nexport default RKITable;\r\n\r\n", "//Import Library\r\nimport _ from \"lodash\";\r\nimport Link from \"next/link\";\r\nimport React, { useState, useEffect } from \"react\";\r\n\r\n//Import services/components\r\nimport { useTranslation } from 'next-i18next';\r\nimport RKIMAP1 from \"../../components/common/RKIMap1\";\r\nimport RKIMapMarker from \"../../components/common/RKIMapMarker\";\r\n\r\nconst ListMapContainer = (props: any) => {\r\n  const { i18n } = useTranslation('common');\r\n  const currentLang = i18n.language;\r\n  const { events } = props;\r\n  const [groupedEvents, setGroupedEvents] = useState<{[key: string]: any[]}>({});\r\n  const [points, setPoints] = useState<any[]>([]);\r\n  const [activeMarker, setactiveMarker] = useState<any>({});\r\n  const [markerInfo, setMarkerInfo] = useState<any>({});\r\n\r\n  const MarkerInfo = (Markerprops: any) => {\r\n    const { info } = Markerprops;\r\n    if (info && info.countryId && groupedEvents[info.countryId]) {\r\n      return (\r\n        <ul>\r\n          {groupedEvents[info.countryId].map((item, index) => {\r\n            return (\r\n              <li key={index}>\r\n                <Link href=\"/event/[...routes]\" as={`/${currentLang}/event/show/${item._id}`}>\r\n                  {item.title}\r\n                </Link>\r\n              </li>\r\n            );\r\n          })}\r\n        </ul>\r\n      );\r\n    }\r\n    return null;\r\n  };\r\n\r\n  const resetMarker = () => {\r\n    setactiveMarker(null);\r\n    setMarkerInfo(null);\r\n  };\r\n\r\n  const onMarkerClick = (propsinit: any, marker: any, _e: any) => {\r\n    resetMarker();\r\n    setactiveMarker(marker);\r\n    setMarkerInfo({\r\n      name: propsinit.name,\r\n      id: propsinit.id,\r\n      countryId: propsinit.countryId,\r\n    });\r\n  };\r\n\r\n  const setPointsFromEvents = () => {\r\n    const eventFilterpoints: any[] = [];\r\n    _.forEach(events, (event) => {\r\n      eventFilterpoints.push({\r\n        title: event.title,\r\n        id: event._id,\r\n        lat:\r\n          event.country &&\r\n          event.country.coordinates &&\r\n          event.country.coordinates[0].latitude,\r\n        lng:\r\n          event.country &&\r\n          event.country.coordinates &&\r\n          event.country.coordinates[0].longitude,\r\n        countryId: event.country && event.country._id,\r\n      });\r\n    });\r\n    setPoints([...eventFilterpoints]);\r\n  };\r\n\r\n  useEffect(() => {\r\n    setPointsFromEvents();\r\n    setGroupedEvents(_.groupBy(events, \"country._id\"));\r\n  }, [events]);\r\n\r\n  return (\r\n    <RKIMAP1\r\n      onClose={resetMarker}\r\n      language={currentLang}\r\n      activeMarker={activeMarker}\r\n      markerInfo={<MarkerInfo info={markerInfo} />}\r\n    >\r\n      {points.length >= 1\r\n        ? points.map((item, index) => {\r\n            if (item.lat) {\r\n              return (\r\n                <RKIMapMarker\r\n                  key={index}\r\n                  name={item.title}\r\n                  id={item.id}\r\n                  countryId={item.countryId}\r\n                  icon={{\r\n                    url: \"/images/map-marker-white.svg\",\r\n                  }}\r\n                  onClick={onMarkerClick}\r\n                  position={item}\r\n                />\r\n              );\r\n            }\r\n          })\r\n        : null}\r\n    </RKIMAP1>\r\n  );\r\n};\r\n\r\nexport default ListMapContainer;\r\n", "import { InfoWindow } from '@react-google-maps/api';\r\n\r\ninterface Props {\r\n  position: google.maps.LatLngLiteral;\r\n  onCloseClick?: () => void;\r\n  children?: React.ReactNode;\r\n}\r\n\r\nconst RKIMapInfowindow = ({ position, onCloseClick, children }: Props) => {\r\n  return (\r\n    <InfoWindow position={position} onCloseClick={onCloseClick}>\r\n      <div>{children}</div>\r\n    </InfoWindow>\r\n  );\r\n};\r\n\r\nexport default RKIMapInfowindow;\r\n", "const fill = \"labels.text.fill\"\r\nconst stoke = \"labels.text.stroke\"\r\nconst road = \"road.highway\"\r\nconst geometry = \"geometry.stroke\"\r\nconst mapStyles = [\r\n  {\r\n    \"elementType\": \"geometry\",\r\n    \"stylers\": [\r\n      {\r\n        \"color\": \"#1d2c4d\"\r\n      }\r\n    ]\r\n  },\r\n  {\r\n    \"elementType\": fill,\r\n    \"stylers\": [\r\n      {\r\n        \"color\": \"#8ec3b9\"\r\n      }\r\n    ]\r\n  },\r\n  {\r\n    \"elementType\": stoke,\r\n    \"stylers\": [\r\n      {\r\n        \"color\": \"#1a3646\"\r\n      }\r\n    ]\r\n  },\r\n  {\r\n    \"featureType\": \"administrative\",\r\n    \"elementType\": \"geometry\",\r\n    \"stylers\": [\r\n      {\r\n        \"visibility\": \"off\"\r\n      }\r\n    ]\r\n  },\r\n  {\r\n    \"featureType\": \"administrative.country\",\r\n    \"elementType\": geometry,\r\n    \"stylers\": [\r\n      {\r\n        \"color\": \"#4b6878\"\r\n      }\r\n    ]\r\n  },\r\n  {\r\n    \"featureType\": \"administrative.land_parcel\",\r\n    \"elementType\": \"labels\",\r\n    \"stylers\": [\r\n      {\r\n        \"visibility\": \"off\"\r\n      }\r\n    ]\r\n  },\r\n  {\r\n    \"featureType\": \"administrative.land_parcel\",\r\n    \"elementType\": fill,\r\n    \"stylers\": [\r\n      {\r\n        \"color\": \"#64779e\"\r\n      }\r\n    ]\r\n  },\r\n  {\r\n    \"featureType\": \"administrative.province\",\r\n    \"elementType\": geometry,\r\n    \"stylers\": [\r\n      {\r\n        \"color\": \"#4b6878\"\r\n      }\r\n    ]\r\n  },\r\n  {\r\n    \"featureType\": \"landscape.man_made\",\r\n    \"elementType\": geometry,\r\n    \"stylers\": [\r\n      {\r\n        \"color\": \"#334e87\"\r\n      }\r\n    ]\r\n  },\r\n  {\r\n    \"featureType\": \"landscape.natural\",\r\n    \"elementType\": \"geometry\",\r\n    \"stylers\": [\r\n      {\r\n        \"color\": \"#023e58\"\r\n      }\r\n    ]\r\n  },\r\n  {\r\n    \"featureType\": \"poi\",\r\n    \"stylers\": [\r\n      {\r\n        \"visibility\": \"off\"\r\n      }\r\n    ]\r\n  },\r\n  {\r\n    \"featureType\": \"poi\",\r\n    \"elementType\": \"geometry\",\r\n    \"stylers\": [\r\n      {\r\n        \"color\": \"#283d6a\"\r\n      }\r\n    ]\r\n  },\r\n  {\r\n    \"featureType\": \"poi\",\r\n    \"elementType\": \"labels.text\",\r\n    \"stylers\": [\r\n      {\r\n        \"visibility\": \"off\"\r\n      }\r\n    ]\r\n  },\r\n  {\r\n    \"featureType\": \"poi\",\r\n    \"elementType\": fill,\r\n    \"stylers\": [\r\n      {\r\n        \"color\": \"#6f9ba5\"\r\n      }\r\n    ]\r\n  },\r\n  {\r\n    \"featureType\": \"poi\",\r\n    \"elementType\": stoke,\r\n    \"stylers\": [\r\n      {\r\n        \"color\": \"#1d2c4d\"\r\n      }\r\n    ]\r\n  },\r\n  {\r\n    \"featureType\": \"poi.park\",\r\n    \"elementType\": \"geometry.fill\",\r\n    \"stylers\": [\r\n      {\r\n        \"color\": \"#023e58\"\r\n      }\r\n    ]\r\n  },\r\n  {\r\n    \"featureType\": \"poi.park\",\r\n    \"elementType\": fill,\r\n    \"stylers\": [\r\n      {\r\n        \"color\": \"#3C7680\"\r\n      }\r\n    ]\r\n  },\r\n  {\r\n    \"featureType\": \"road\",\r\n    \"stylers\": [\r\n      {\r\n        \"visibility\": \"off\"\r\n      }\r\n    ]\r\n  },\r\n  {\r\n    \"featureType\": \"road\",\r\n    \"elementType\": \"geometry\",\r\n    \"stylers\": [\r\n      {\r\n        \"color\": \"#304a7d\"\r\n      }\r\n    ]\r\n  },\r\n  {\r\n    \"featureType\": \"road\",\r\n    \"elementType\": \"labels.icon\",\r\n    \"stylers\": [\r\n      {\r\n        \"visibility\": \"off\"\r\n      }\r\n    ]\r\n  },\r\n  {\r\n    \"featureType\": \"road\",\r\n    \"elementType\": fill,\r\n    \"stylers\": [\r\n      {\r\n        \"color\": \"#98a5be\"\r\n      }\r\n    ]\r\n  },\r\n  {\r\n    \"featureType\": \"road\",\r\n    \"elementType\": stoke,\r\n    \"stylers\": [\r\n      {\r\n        \"color\": \"#1d2c4d\"\r\n      }\r\n    ]\r\n  },\r\n  {\r\n    \"featureType\": road,\r\n    \"elementType\": \"geometry\",\r\n    \"stylers\": [\r\n      {\r\n        \"color\": \"#2c6675\"\r\n      }\r\n    ]\r\n  },\r\n  {\r\n    \"featureType\": road,\r\n    \"elementType\": geometry,\r\n    \"stylers\": [\r\n      {\r\n        \"color\": \"#255763\"\r\n      }\r\n    ]\r\n  },\r\n  {\r\n    \"featureType\": road,\r\n    \"elementType\": fill,\r\n    \"stylers\": [\r\n      {\r\n        \"color\": \"#b0d5ce\"\r\n      }\r\n    ]\r\n  },\r\n  {\r\n    \"featureType\": road,\r\n    \"elementType\": stoke,\r\n    \"stylers\": [\r\n      {\r\n        \"color\": \"#023e58\"\r\n      }\r\n    ]\r\n  },\r\n  {\r\n    \"featureType\": \"road.local\",\r\n    \"elementType\": \"labels\",\r\n    \"stylers\": [\r\n      {\r\n        \"visibility\": \"off\"\r\n      }\r\n    ]\r\n  },\r\n  {\r\n    \"featureType\": \"transit\",\r\n    \"stylers\": [\r\n      {\r\n        \"visibility\": \"off\"\r\n      }\r\n    ]\r\n  },\r\n  {\r\n    \"featureType\": \"transit\",\r\n    \"elementType\": fill,\r\n    \"stylers\": [\r\n      {\r\n        \"color\": \"#98a5be\"\r\n      }\r\n    ]\r\n  },\r\n  {\r\n    \"featureType\": \"transit\",\r\n    \"elementType\": stoke,\r\n    \"stylers\": [\r\n      {\r\n        \"color\": \"#1d2c4d\"\r\n      }\r\n    ]\r\n  },\r\n  {\r\n    \"featureType\": \"transit.line\",\r\n    \"elementType\": \"geometry.fill\",\r\n    \"stylers\": [\r\n      {\r\n        \"color\": \"#283d6a\"\r\n      }\r\n    ]\r\n  },\r\n  {\r\n    \"featureType\": \"transit.station\",\r\n    \"elementType\": \"geometry\",\r\n    \"stylers\": [\r\n      {\r\n        \"color\": \"#3a4762\"\r\n      }\r\n    ]\r\n  },\r\n  {\r\n    \"featureType\": \"water\",\r\n    \"elementType\": \"geometry\",\r\n    \"stylers\": [\r\n      {\r\n        \"color\": \"#0e1626\"\r\n      }\r\n    ]\r\n  },\r\n  {\r\n    \"featureType\": \"water\",\r\n    \"elementType\": fill,\r\n    \"stylers\": [\r\n      {\r\n        \"color\": \"#4e6d70\"\r\n      }\r\n    ]\r\n  }\r\n];\r\n\r\nexport default mapStyles;", "import React from 'react';\r\nimport { GoogleMap, useJsApiLoader } from '@react-google-maps/api';\r\nimport RKIMapInfowindow from './RKIMapInfowindow';\r\nimport mapStyles from './mapStyles';\r\nimport { useRouter } from \"next/router\";\r\nimport { useGoogleMaps } from './GoogleMapsProvider';\r\n\r\ninterface RKIMap1Props {\r\n  markerInfo?: React.ReactNode;\r\n  activeMarker?: any;\r\n  initialCenter?: { lat: number; lng: number };\r\n  children?: React.ReactNode;\r\n  height?: number | string;\r\n  width?: string;\r\n  language?: string;\r\n  points?: any[];\r\n  zoom?: number;\r\n  minZoom?: number;\r\n  onClose?: () => void\r\n}\r\n\r\nconst RKIMap1: React.FC<RKIMap1Props> = ({\r\n  markerInfo,\r\n  activeMarker,\r\n  initialCenter,\r\n  children,\r\n  height = 300,\r\n  width = \"114%\",\r\n  language,\r\n  zoom = 1,\r\n  minZoom = 1,\r\n  onClose\r\n}) => {\r\n  const { locale } = useRouter();\r\n  const { isLoaded, loadError } =  useGoogleMaps();\r\n  const containerStyle = {\r\n    width: width,\r\n    height: typeof height === 'number' ? `${height}px` : height,\r\n  };\r\n\r\n  const defaultCenter = {\r\n    lat: 52.520017,\r\n    lng: 13.404195,\r\n  };\r\n\r\n  const center = initialCenter || defaultCenter;\r\n\r\n  const onMapLoad = (map: google.maps.Map) => {\r\n    map.setOptions({\r\n      styles: mapStyles,\r\n    });\r\n  };\r\n\r\n  if (loadError) return <div>Error loading maps</div>;\r\n  if (!isLoaded) return <div>Loading Maps...</div>;\r\n\r\n  return (\r\n    <div className=\"map-container\">\r\n      <div className=\"mapprint\" style={{ width, height, position: 'relative' }}>\r\n        <GoogleMap\r\n          mapContainerStyle={containerStyle}\r\n          center={center}\r\n          zoom={zoom}\r\n          onLoad={onMapLoad}\r\n          options={{\r\n            minZoom: minZoom,\r\n            draggable: true,\r\n            keyboardShortcuts: false,\r\n            streetViewControl: false,\r\n            panControl: false,\r\n            clickableIcons: false,\r\n            mapTypeControl: false,\r\n            fullscreenControl: true,\r\n          }}\r\n        >\r\n          {children}\r\n          {markerInfo && activeMarker && activeMarker.getPosition && (\r\n            <RKIMapInfowindow\r\n              position={activeMarker.getPosition()}\r\n              onCloseClick={() => {\r\n                // Handle close if needed\r\n                console.log('close click');\r\n                onClose?.()\r\n              }}\r\n            >\r\n              {markerInfo}\r\n            </RKIMapInfowindow>\r\n          )}\r\n        </GoogleMap>\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default RKIMap1;\r\n", "//Import Library\r\nimport React, { useState, useEffect } from 'react';\r\nimport _ from 'lodash';\r\nimport { Form, Button } from \"react-bootstrap\";\r\n\r\n//Import services/components\r\nimport apiService from '../../services/apiService';\r\nimport { useTranslation } from 'next-i18next';\r\n\r\n// Define types for region items\r\ninterface RegionItem {\r\n  _id: string;\r\n  code: string;\r\n  title: string;\r\n  isChecked: boolean;\r\n}\r\n\r\ninterface RegionsMultiCheckboxesProps {\r\n  regionHandler: (regions: string[]) => void;\r\n  selectedRegions: string[];\r\n  filtreg: (regions: string[]) => void;\r\n}\r\n\r\nfunction RegionsMultiCheckboxes(props: RegionsMultiCheckboxesProps) {\r\n  const {filtreg} = props;\r\n  const [allregions, setAllregions] = useState(true);\r\n  const [region, setRegion] = useState<RegionItem[]>([]);\r\n  const [selectedRegions, setSelectedRegions] = useState<string[]>([]);\r\n  const { t } = useTranslation('common');\r\n  const RegionParams = {\r\n    \"query\": {},\r\n    \"limit\": \"~\",\r\n    \"sort\": { \"title\": \"asc\" }\r\n  };\r\n\r\n  const getworldregion = async (RegionParams_initial: typeof RegionParams) => {\r\n    const response = await apiService.get('/worldregion', RegionParams_initial);\r\n    if (response && Array.isArray(response.data)) {\r\n      const finalRegions: RegionItem[] = [];\r\n      const selectedIds: string[] = [];\r\n\r\n      _.each(response.data, (item, _) => {\r\n        const regionItem: RegionItem = {\r\n          ...item,\r\n          isChecked: true\r\n        };\r\n        finalRegions.push(regionItem);\r\n        selectedIds.push(item._id);\r\n      });\r\n\r\n      filtreg(selectedIds);\r\n      setSelectedRegions(selectedIds);\r\n      setRegion(finalRegions);\r\n    }\r\n  };\r\n\r\n  useEffect(() => {\r\n    getworldregion(RegionParams);\r\n  }, [])\r\n\r\n  const handleAllChecked = (event: React.ChangeEvent<HTMLInputElement>) => {\r\n    const updatedRegions = region.map(item => ({\r\n      ...item,\r\n      isChecked: event.target.checked\r\n    }));\r\n\r\n    let selected_Regions: string[] = [];\r\n    if (event.target.checked) {\r\n      selected_Regions = updatedRegions.map(item => item._id);\r\n    }\r\n\r\n    filtreg(selected_Regions);\r\n    setSelectedRegions(selected_Regions);\r\n    setAllregions(event.target.checked);\r\n    setRegion(updatedRegions);\r\n  };\r\n\r\n  const handleIndividualRegionChange = (e: React.ChangeEvent<HTMLInputElement>) => {\r\n    const updatedRegions = [...region];\r\n    let updatedSelectedRegions = [...selectedRegions];\r\n\r\n    updatedRegions.forEach((item, index) => {\r\n      if (item.code === e.target.id) {\r\n        updatedRegions[index].isChecked = e.target.checked;\r\n        if (!e.target.checked) {\r\n          updatedSelectedRegions = updatedSelectedRegions.filter(n => n !== item._id);\r\n        } else {\r\n          updatedSelectedRegions.push(item._id);\r\n        }\r\n      }\r\n    });\r\n\r\n    setSelectedRegions(updatedSelectedRegions);\r\n    filtreg(updatedSelectedRegions);\r\n    setAllregions(false);\r\n    setRegion(updatedRegions);\r\n  };\r\n\r\n  const resetAllRegion = () => {\r\n    const updatedRegions = region.map(item => ({\r\n      ...item,\r\n      isChecked: false\r\n    }));\r\n\r\n    setSelectedRegions([]);\r\n    setAllregions(false);\r\n    setRegion(updatedRegions);\r\n    filtreg([]);\r\n  };\r\n\r\n  return (\r\n    <div className=\"regions-multi-checkboxes\">\r\n      <Form.Check\r\n        type=\"checkbox\"\r\n        id={`all`}\r\n        label={t(\"AllRegions\")}\r\n        checked={allregions}\r\n        onChange={handleAllChecked}\r\n      />\r\n      {region.map((item, index) => {\r\n        return (\r\n          <Form.Check\r\n            key={index}\r\n            type=\"checkbox\"\r\n            id={item.code}\r\n            label={item.title}\r\n            value={item.code}\r\n            onChange={handleIndividualRegionChange}\r\n            checked={region[index].isChecked}\r\n          />\r\n        )\r\n      })}\r\n      <Button onClick={resetAllRegion} className=\"btn-plain ps-2\">{t(\"ClearAll\")}</Button>\r\n    </div>\r\n  )\r\n}\r\n\r\nRegionsMultiCheckboxes.defaultProps = {\r\n  filtreg: () => {\"\"}\r\n}\r\n\r\nexport default RegionsMultiCheckboxes;\r\n", "interface PageHeadingProps {\r\n  title: string; // Required based on actual usage\r\n}\r\n\r\nexport default function PageHeading(props: PageHeadingProps) {\r\n  return (\r\n    <h2 className=\"page-heading\">{props.title}</h2>\r\n  )\r\n}\r\n", "//Import Library\r\nimport Link from \"next/link\";\r\nimport moment from \"moment\";\r\nimport _ from \"lodash\";\r\nimport React, { useEffect, useState, useRef } from \"react\";\r\nimport { useRouter } from \"next/router\";\r\n\r\n//Import services/components\r\nimport RKITable from \"../../components/common/RKITable\";\r\nimport apiService from \"../../services/apiService\";\r\nimport EventsFilter from \"./EventsTableFilter\";\r\nimport { useTranslation } from 'next-i18next';\r\n\r\nconst Hazards = (props: any) => {\r\n  const { i18n } = useTranslation('common');\r\n  const currentLang = i18n.language === \"fr\" ? \"en\" : i18n.language;\r\n  const { hazards } = props;\r\n  return (\r\n    <ul>\r\n      {hazards.map((item: any, index: any) => {\r\n        {\r\n          return item && item._id && item.title && item.title[currentLang] ? (\r\n            <li key={index}>\r\n              <Link href=\"/hazard/[...routes]\" as={`/hazard/show/${item._id}`} >\r\n                {item.title[currentLang].toString()}\r\n              </Link>\r\n            </li>\r\n          ) : (\r\n            \"\"\r\n          );\r\n        }\r\n      })}\r\n    </ul>\r\n  );\r\n};\r\n\r\nfunction EventsTable(props: any) {\r\n  const router = useRouter();\r\n  const { t } = useTranslation('common');\r\n  const { setEvents, selectedRegions } = props;\r\n  const [filterText, setFilterText] = React.useState(\"\");\r\n  const [filterHazard, setFilterHazard] = React.useState(\"\");\r\n  const [resetPaginationToggle, setResetPaginationToggle] =\r\n    React.useState(false);\r\n  const [tabledata, setDataToTable] = useState([]);\r\n  const [loading, setLoading] = useState(false);\r\n  const [totalRows, setTotalRows] = useState(0);\r\n  const [perPage, setPerPage] = useState(10);\r\n  const [pageNum, setPageNum] = useState(1);\r\n  const [pageSort, setPageSort] = useState<any>(null);\r\n\r\n  const eventParams: any = {\r\n    sort: { created_at: \"desc\" },\r\n    lean: true,\r\n    populate: [\r\n      { path: \"country\", select: \"coordinates title\" },\r\n      { path: \"hazard_type\", select: \"title\" },\r\n      { path: \"hazard\", select: \"title\" },\r\n    ],\r\n    limit: perPage,\r\n    page: 1,\r\n    query: {},\r\n    select:\r\n      \"-description -operation -world_region -country_regions -syndrome -status -laboratory_confirmed -officially_validated -rki_monitored -risk_assessment -date -more_info -images -user -created_at -updated_at\",\r\n  };\r\n\r\n  const [evParams, setEvParams] = useState(eventParams);\r\n\r\n  const columns = [\r\n    {\r\n      name: t(\"Events.table.EventId\"),\r\n      selector: \"title\",\r\n      sortable: true,\r\n      width: \"20%\",\r\n      cell: (d: any) => (\r\n        <Link href=\"/event/[...routes]\" as={`/event/show/${d._id}`}>\r\n          {d.title}\r\n        </Link>\r\n      ),\r\n    },\r\n    {\r\n      name: t(\"Events.table.Country\"),\r\n      selector: \"country\",\r\n      sortable: true,\r\n      cell: (d: any) =>\r\n        d.country && d.country.title ? (\r\n          <Link\r\n            href=\"/country/[...routes]\"\r\n            as={`/country/show/${d.country._id}`}\r\n          >\r\n            {d.country.title}\r\n          </Link>\r\n        ) : (\r\n          \"\"\r\n        ),\r\n    },\r\n    {\r\n      name: t(\"Events.table.HazardType\"),\r\n      selector: \"hazard_type\",\r\n      sortable: true,\r\n      cell: (d: any) =>\r\n        d.hazard_type && d.hazard_type.title ? d.hazard_type.title : \"\",\r\n    },\r\n    {\r\n      name: t(\"Events.table.Hazard\"),\r\n      selector: \"hazard\",\r\n      cell: (d: any) => <Hazards hazards={d.hazard} />,\r\n    },\r\n    {\r\n      name: t(\"Events.table.InfoReceivedon\"),\r\n      selector: \"created_at\",\r\n      sortable: true,\r\n      cell: (d: any) => moment(d.start_date).format(\"M/D/Y\"),\r\n    },\r\n    {\r\n      name: t(\"Events.table.Lastupdated\"),\r\n      selector: \"updated_at\",\r\n      sortable: true,\r\n      cell: (d: any) => moment(d.updated_at).format(\"M/D/Y\"),\r\n    },\r\n  ];\r\n\r\n  const getEventsData = async (eventParamsinit: any) => {\r\n    setLoading(true);\r\n\r\n    // Check if there's a country query\r\n    if (router.query && router.query.country) {\r\n      eventParamsinit.query[\"country\"] = router.query.country;\r\n    }\r\n\r\n    // Handle selectedRegions correctly\r\n    if (selectedRegions === null) {\r\n      // No regions selected: don't filter by region\r\n      delete eventParamsinit.query[\"world_region\"];\r\n    } else if (selectedRegions.length === 0) {\r\n      // Empty region selection: force zero results\r\n      eventParamsinit.query[\"world_region\"] = [\"__NO_MATCH__\"];\r\n    } else {\r\n      // Filter by selected regions\r\n      eventParamsinit.query[\"world_region\"] = selectedRegions;\r\n    }\r\n\r\n    // Fetch data from the API\r\n    const response = await apiService.get(\"/event\", eventParamsinit);\r\n    if (response && Array.isArray(response.data)) {\r\n      setDataToTable(response.data);\r\n      setEvents(response.data);\r\n      setTotalRows(response.totalCount);\r\n    }\r\n\r\n    setLoading(false);\r\n  };\r\n\r\n\r\n  const handlePageChange = (page: any) => {\r\n    eventParams.limit = perPage;\r\n    eventParams.page = page;\r\n\r\n    // Add hazard type filter\r\n    filterHazard &&\r\n      (eventParams.query = { ...eventParams.query, hazard_type: filterHazard });\r\n\r\n    // Add sorting\r\n    pageSort && (eventParams.sort = pageSort.sort);\r\n\r\n    // Get data with updated filters\r\n    getEventsData(eventParams);\r\n    setPageNum(page);\r\n  };\r\n\r\n\r\n  const handlePerRowsChange = async (newPerPage: any, page: any) => {\r\n    eventParams.limit = newPerPage;\r\n    eventParams.page = page;\r\n    setLoading(true);\r\n\r\n    // Handle selected region in pagination\r\n    if (router.query && router.query.country) {\r\n      eventParams.query[\"country\"] = router.query.country;\r\n    }\r\n\r\n    // Handle selected region filter\r\n    if (selectedRegions === null) {\r\n      delete eventParams.query[\"world_region\"];\r\n    } else if (selectedRegions.length === 0) {\r\n      eventParams.query[\"world_region\"] = [\"__NO_MATCH__\"];\r\n    } else {\r\n      eventParams.query[\"world_region\"] = selectedRegions;\r\n    }\r\n\r\n    // Apply hazard filter\r\n    filterHazard &&\r\n      (eventParams.query = { ...eventParams.query, hazard_type: filterHazard });\r\n\r\n    // Apply sorting\r\n    pageSort && (eventParams.sort = pageSort.sort);\r\n\r\n    // Fetch data from the API\r\n    const response = await apiService.get(\"/event\", eventParams);\r\n    if (response && Array.isArray(response.data)) {\r\n      setDataToTable(response.data);\r\n      setEvents(response.data);\r\n      setPerPage(newPerPage);\r\n      setLoading(false);\r\n    }\r\n\r\n    setPageNum(page);\r\n  };\r\n\r\n\r\n  useEffect(() => {\r\n    evParams.page = 1;\r\n    getEventsData(evParams);\r\n  }, [selectedRegions, router]);\r\n\r\n  useEffect(() => {\r\n    getEventsData(evParams);\r\n  }, [evParams]);\r\n\r\n\r\n  const handleSort = async (column: any, sortDirection: any) => {\r\n    setLoading(true);\r\n    eventParams.sort = {\r\n      [column.selector]: sortDirection,\r\n    };\r\n    filterHazard &&\r\n      (eventParams.query = { ...eventParams.query, hazard_type: filterHazard });\r\n    filterText !== \"\" &&\r\n      (eventParams.query = { ...eventParams.query, title: filterText });\r\n\r\n    await getEventsData(eventParams);\r\n    setPageSort(eventParams);\r\n    setLoading(false);\r\n  };\r\n\r\n  const sendQuery = (q: any, page: any) => {\r\n    if (q) {\r\n      evParams.query[\"title\"] = q;\r\n      evParams.page = page;\r\n      setEvParams({ ...evParams });\r\n    } else {\r\n      delete evParams.query.title;\r\n      setEvParams({ ...evParams });\r\n    }\r\n  };\r\n\r\n  const handleSearchTitle = useRef(\r\n    _.debounce(\r\n      (q, page) => sendQuery(q, page),\r\n      Number(process.env.SEARCH_DEBOUNCE_TIME) || 300\r\n    )\r\n  ).current;\r\n\r\n  const subHeaderComponentMemo = React.useMemo(() => {\r\n    const handleClear = () => {\r\n      if (filterText) {\r\n        setResetPaginationToggle(!resetPaginationToggle);\r\n        setFilterText(\"\");\r\n      }\r\n    };\r\n\r\n    const handleFilterHazardType = (hazardType: any) => {\r\n      setFilterHazard(hazardType);\r\n      if (hazardType) {\r\n        evParams.query[\"hazard_type\"] = hazardType;\r\n        setEvParams({ ...evParams });\r\n      } else {\r\n        delete evParams.query.hazard_type;\r\n        setEvParams({ ...evParams });\r\n      }\r\n    };\r\n\r\n    const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {\r\n      setFilterText(e.target.value);\r\n      handleSearchTitle(e.target.value, pageNum);\r\n    };\r\n\r\n    return (\r\n      <EventsFilter\r\n        onFilter={handleChange}\r\n        onFilterHazardChange={(e: any) => handleFilterHazardType(e.target.value)}\r\n        onClear={handleClear}\r\n        filterText={filterText}\r\n        filterHazard={filterHazard}\r\n      />\r\n    );\r\n  }, [filterText, resetPaginationToggle, filterHazard, selectedRegions]);\r\n\r\n  return (\r\n    <RKITable\r\n      columns={columns}\r\n      data={tabledata}\r\n      totalRows={totalRows}\r\n      loading={loading}\r\n      subheader\r\n      persistTableHead\r\n      onSort={handleSort}\r\n      sortServer\r\n      pagServer={true}\r\n      subHeaderComponent={subHeaderComponentMemo}\r\n      handlePerRowsChange={handlePerRowsChange}\r\n      handlePageChange={handlePageChange}\r\n    />\r\n  );\r\n}\r\n\r\nexport default EventsTable;\r\n", "\n    (window.__NEXT_P = window.__NEXT_P || []).push([\n      \"/event\",\n      function () {\n        return require(\"private-next-pages/event/index.tsx\");\n      }\n    ]);\n    if(module.hot) {\n      module.hot.dispose(function () {\n        window.__NEXT_P.push([\"/event\"])\n      });\n    }\n  "], "names": ["filterText", "<PERSON><PERSON><PERSON>er", "onFilter", "onClear", "onFilterHazardChange", "filterHazard", "hazardType", "setHazardType", "useState", "t", "useTranslation", "getNetworks", "params", "response", "apiService", "get", "Array", "isArray", "data", "useEffect", "query", "sort", "title", "Container", "fluid", "className", "Row", "Col", "xs", "FormControl", "type", "placeholder", "aria-label", "value", "onChange", "as", "aria-placeholder", "option", "map", "item", "index", "_id", "canAddEvent", "connectedAuthWrapper", "authenticatedSelector", "state", "permissions", "event", "wrapperDisplayName", "FailureComponent", "R403", "props", "user", "update", "Project", "isContactModalEnbled", "setContactModal", "selectedRegions", "setSelectedRegions", "events", "setEvents", "onSetModal", "AddEventComponent", "Link", "href", "<PERSON><PERSON>", "variant", "size", "CanAddEvent", "regionHandler", "val", "PageHeading", "ListMapContainer", "RegionsMultiCheckboxes", "filtreg", "p", "small", "onClick", "span", "EventsTable", "ContactUsModal", "show", "onHide", "name", "id", "R<PERSON>IMapMarker", "icon", "position", "draggable", "lat", "lng", "<PERSON><PERSON>", "handleClick", "marker", "countryId", "getPosition", "e", "RKITable", "paginationComponentOptions", "rowsPerPageText", "columns", "totalRows", "resetPaginationToggle", "subheader", "subHeaderComponent", "handlePerRowsChange", "handlePageChange", "rowsPerPage", "defaultRowsPerPage", "selectableRows", "loading", "pagServer", "onSelectedRowsChange", "clearSelectedRows", "sortServer", "onSort", "persistTableHead", "sortFunction", "otherProps", "dataTableProps", "noDataComponent", "<PERSON><PERSON><PERSON><PERSON>", "dense", "paginationResetDefaultPage", "subHeader", "progressPending", "pagination", "paginationServer", "paginationPerPage", "paginationRowsPerPageOptions", "paginationTotalRows", "onChangeRowsPerPage", "onChangePage", "progressComponent", "CustomLoader", "sortIcon", "i", "DataTable", "defaultProps", "i18n", "currentLang", "language", "groupedEvents", "setGroupedEvents", "points", "setPoints", "activeMarker", "set<PERSON><PERSON><PERSON><PERSON>", "markerInfo", "setMarkerInfo", "reset<PERSON><PERSON><PERSON>", "onMarkerClick", "propsinit", "_e", "setPointsFromEvents", "eventFilterpoints", "_", "push", "country", "coordinates", "latitude", "longitude", "RKIMAP1", "onClose", "MarkerInfo", "info", "Markerprops", "ul", "li", "length", "url", "RKIMapInfowindow", "onCloseClick", "children", "InfoWindow", "div", "fill", "stoke", "road", "geometry", "mapStyles", "RKIMap1", "initialCenter", "height", "width", "zoom", "minZoom", "locale", "useRouter", "isLoaded", "loadError", "useGoogleMaps", "style", "GoogleMap", "mapContainerStyle", "center", "defaultCenter", "onLoad", "onMapLoad", "setOptions", "styles", "options", "keyboardShortcuts", "streetViewControl", "panControl", "clickableIcons", "mapTypeControl", "fullscreenControl", "console", "log", "allregions", "setAllregions", "region", "setRegion", "RegionParams", "getworldregion", "RegionParams_initial", "finalRegions", "selectedIds", "regionItem", "isChecked", "handleIndividualRegionChange", "updatedRegions", "updatedSelectedRegions", "for<PERSON>ach", "code", "target", "checked", "filter", "n", "Form", "Check", "label", "handleAllChecked", "selected_Regions", "resetAllRegion", "h2", "Hazards", "hazards", "toString", "router", "setFilterText", "React", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "setResetPaginationToggle", "tabledata", "setDataToTable", "setLoading", "setTotalRows", "perPage", "setPerPage", "pageNum", "setPageNum", "pageSort", "setPageSort", "eventParams", "created_at", "lean", "populate", "path", "select", "limit", "page", "evParams", "setEvParams", "selector", "sortable", "cell", "d", "hazard_type", "hazard", "moment", "start_date", "format", "updated_at", "getEventsData", "eventParamsinit", "totalCount", "newPerPage", "handleSort", "column", "sortDirection", "<PERSON><PERSON><PERSON><PERSON>", "q", "handleSearchTitle", "useRef", "Number", "process", "current", "subHeaderComponentMemo", "handleFilterHazardType", "handleChange", "handleClear"], "sourceRoot": "", "ignoreList": []}