{"version": 3, "file": "static/chunks/pages/adminsettings/updateType-4452af6bf7a626ca.js", "mappings": "sPA6CA,MApCyBA,IACvB,GAAM,GAAEC,CAAC,CAAE,CAAGC,CAAAA,EAAAA,EAAAA,EAAAA,CAAcA,CAAC,MAmChBC,IAlCPC,EAAsB,IAExB,OAgCyB,EAhCzB,EAACC,EAAAA,CAASA,CAAAA,CAACC,MAAO,CAAEC,UAAW,QAAS,EAAGC,KAAK,IAACC,UAAU,gBACzD,UAACC,EAAAA,CAAGA,CAAAA,UACF,UAACC,EAAAA,CAAGA,CAAAA,CAACC,GAAI,YACP,UAACC,EAAAA,CAAWA,CAAAA,CAACC,MAAOb,EAAE,6CAG1B,UAACS,EAAAA,CAAGA,CAAAA,UACF,UAACC,EAAAA,CAAGA,CAAAA,CAACC,GAAI,YACP,UAACG,IAAIA,CACHC,KAAK,6BACLC,GAAG,OAFAF,sCAIH,UAACG,EAAAA,CAAMA,CAAAA,CAACC,QAAQ,YAAYC,KAAK,cAChCnB,EAAE,kDAKT,UAACS,EAAAA,CAAGA,CAAAA,CAACD,UAAU,gBACb,UAACE,EAAAA,CAAGA,CAAAA,CAACC,GAAI,YACP,UAACS,EAAAA,OAAeA,CAAAA,CAAAA,UAOpBC,EAAqBC,CAAAA,EAAAA,EAAAA,OAAAA,CAAgBA,CAAC,IAAM,UAACnB,EAAAA,CAAAA,IACnD,MACE,UAACkB,EAAAA,CAAAA,EAEL,6mBCzCA,IAAME,EAAS,aACFD,EAAmBE,CAAAA,EAAAA,EAAAA,CAAAA,CAAoBA,CAAC,CACnDC,sBAAuB,KACjBC,EAAMC,WAAW,IAAID,EAAMC,WAAW,CAACC,YAAY,IAAIF,EAAMC,WAAW,CAACC,YAAY,CAACL,EAAO,CAKnGM,CALqG,kBAKjF,kBACtB,GAAG,EAE0BL,CAAAA,EAAAA,EAAAA,CAAAA,CAAoBA,CAAC,CAChDC,sBAAwBC,KAClBA,EAAMC,WAAW,IAAID,EAAMC,WAAW,CAACG,OAAO,IAAIJ,EAAMC,WAAW,CAACG,OAAO,CAACP,EAAO,CAKzFM,CAL2F,kBAKvE,eACtB,GAAG,EAEmCL,CAAAA,EAAAA,EAAAA,CAAAA,CAAoBA,CAAC,CACzDC,sBAAuB,KACjBC,EAAMC,WAAW,IAAID,EAAMC,WAAW,CAACI,iBAAiB,IAAIL,EAAMC,WAAW,CAACI,iBAAiB,CAACR,EAAO,CAK7GM,CAL+G,kBAK3F,wBACtB,GAAG,EAE8BL,CAAAA,EAAAA,EAAAA,CAAAA,CAAoBA,CAAC,CACpDC,sBAAuB,KACjBC,EAAMC,WAAW,IAAID,EAAMC,WAAW,CAACK,YAAY,IAAIN,EAAMC,WAAW,CAACK,YAAY,CAACT,EAAO,CAKnGM,CALqG,kBAKjF,mBACtB,GAAG,EAE4BL,CAAAA,EAAAA,EAAAA,CAAAA,CAAoBA,CAAC,CAClDC,sBAAuB,KACjBC,EAAMC,WAAW,IAAID,EAAMC,WAAW,CAACM,SAAS,IAAIP,EAAMC,WAAW,CAACM,SAAS,CAACV,EAAO,CAK7FM,CAL+F,kBAK3E,iBACtB,GAAG,EAEqCL,CAAAA,EAAAA,EAAAA,CAAAA,CAAoBA,CAAC,CAC3DC,sBAAuB,KACjBC,EAAMC,WAAW,IAAID,EAAMC,WAAW,CAACO,uBAAuB,IAAIR,EAAMC,WAAW,CAACO,uBAAuB,CAACX,EAAO,CAKzHM,CAL2H,kBAKvG,0BACtB,GAAG,EAEiCL,CAAAA,EAAAA,EAAAA,CAAAA,CAAoBA,CAAC,CACvDC,sBAAuB,KACjBC,EAAMC,WAAW,IAAID,EAAMC,WAAW,CAACO,uBAAuB,IAAIR,EAAMC,WAAW,CAACO,uBAAuB,CAACX,EAAO,CAKzHM,CAL2H,kBAKvG,sBACtB,GAAG,EAC0BL,CAAAA,EAAAA,EAAAA,CAAAA,CAAoBA,CAAC,CAChDC,sBAAuB,KACjBC,EAAMC,WAAW,IAAID,EAAMC,WAAW,CAACQ,MAAM,IAAIT,EAAMC,WAAW,CAACQ,MAAM,CAACZ,EAAO,CAKvFM,CALyF,kBAKrE,eACtB,GAAG,EAE8BL,CAAAA,EAAAA,EAAAA,CAAAA,CAAoBA,CAAC,CACpDC,sBAAuB,KACjBC,EAAMC,WAAW,IAAID,EAAMC,WAAW,CAACS,WAAW,IAAIV,EAAMC,WAAW,CAACS,WAAW,CAACb,EAAO,CAKjGM,CALmG,kBAK/E,mBACtB,GAAG,EAEuCL,CAAAA,EAAAA,EAAAA,CAAAA,CAAoBA,CAAC,CAC7DC,sBAAuB,GACjBC,IAAMC,WAAW,IAAID,EAAMC,WAAW,CAACU,WAAW,IAAIX,EAAMC,WAAW,CAACU,WAAW,CAACd,EAAO,CAKjGM,CALmG,kBAK/E,4BACtB,GAEaS,EAA6Bd,CAAAA,EAAAA,EAAAA,CAAAA,CAAoBA,CAAC,CAC7DC,sBAAuB,KACjBC,EAAMC,WAAW,IAAID,EAAMC,WAAW,CAACY,mBAAmB,IAAIb,EAAMC,WAAW,CAACY,mBAAmB,CAAChB,EAAO,CAKjHM,CALmH,kBAK/F,4BACtB,GAAG,EAEoCL,CAAAA,EAAAA,EAAAA,CAAAA,CAAoBA,CAAC,CAC1DC,sBAAuB,KACjBC,EAAMC,WAAW,IAAID,EAAMC,WAAW,CAACa,gBAAgB,IAAId,EAAMC,WAAW,CAACa,gBAAgB,CAACjB,EAAO,CAK3GM,CAL6G,kBAKzF,yBACtB,GAAG,EAEkCL,CAAAA,EAAAA,EAAAA,CAAAA,CAAoBA,CAAC,CACxDC,sBAAuB,GACjBC,IAAMC,WAAW,IAAID,EAAMC,WAAW,CAACc,gBAAgB,IAAIf,EAAMC,WAAW,CAACc,gBAAgB,CAAClB,EAAO,CAK3GM,CAL6G,kBAKzF,uBACtB,GAAG,EAEgCL,CAAAA,EAAAA,EAAAA,CAAAA,CAAoBA,CAAC,CACtDC,sBAAuB,KACjBC,EAAMC,WAAW,IAAID,EAAMC,WAAW,CAACe,cAAc,IAAIhB,EAAMC,WAAW,CAACe,cAAc,CAACnB,EAAO,CAKvGM,CALyG,kBAKrF,qBACtB,GAAG,EAE0BL,CAAAA,EAAAA,EAAAA,CAAAA,CAAoBA,CAAC,CAChDC,sBAAuB,KACjBC,EAAMC,WAAW,IAAID,EAAMC,WAAW,CAACgB,MAAM,IAAIjB,EAAMC,WAAW,CAACgB,MAAM,CAACpB,EAAO,CAKvFM,CALyF,kBAKrE,eACtB,GAAG,EAE6BL,CAAAA,EAAAA,EAAAA,CAAAA,CAAoBA,CAAC,CACnDC,sBAAuB,KACjBC,EAAMC,WAAW,IAAID,EAAMC,WAAW,CAACiB,UAAU,IAAIlB,EAAMC,WAAW,CAACiB,UAAU,CAACrB,EAAO,CAK/FM,CALiG,kBAK7E,kBACtB,GAAG,EAE4BL,CAAAA,EAAAA,EAAAA,CAAAA,CAAoBA,CAAC,CAClDC,sBAAuB,KACjBC,EAAMC,WAAW,IAAID,EAAMC,WAAW,CAACkB,QAAQ,IAAInB,EAAMC,WAAW,CAACkB,QAAQ,CAACtB,EAAO,CAK3FM,CAL6F,kBAKzE,iBACtB,GAAG,EAE8BL,CAAAA,EAAAA,EAAAA,CAAAA,CAAoBA,CAAC,CACpDC,sBAAuB,KACjBC,EAAMC,WAAW,IAAID,EAAMC,WAAW,CAACmB,WAAW,IAAIpB,EAAMC,WAAW,CAACmB,WAAW,CAACvB,EAAO,CAKjGM,CALmG,kBAK/E,mBACtB,GAEakB,EAAcvB,CAAAA,EAAAA,EAAAA,CAAAA,CAAoBA,CAAC,CAC9CC,sBAAuB,KACjBC,EAAMC,WAAW,IAAID,EAAMC,WAAW,CAACqB,KAAK,IAAItB,EAAMC,WAAW,CAACqB,KAAK,CAACzB,EAAO,CAKrFM,CALuF,kBAKnE,aACtB,GAAG,EAE8BL,CAAAA,EAAAA,EAAAA,CAAAA,CAAoBA,CAAC,CACpDC,sBAAuB,KACjBC,EAAMC,WAAW,IAAID,EAAMC,WAAW,CAACsB,WAAW,IAAIvB,EAAMC,WAAW,CAACsB,WAAW,CAAC1B,EAAO,CAKjGM,CALmG,kBAK/E,mBACtB,GAAG,EAE8BL,CAAAA,EAAAA,EAAAA,CAAAA,CAAoBA,CAAC,CACpDC,sBAAuB,KACjBC,EAAMC,WAAW,IAAID,EAAMC,WAAW,CAACuB,YAAY,IAAIxB,EAAMC,WAAW,CAACuB,YAAY,CAAC3B,EAAO,CAKnGM,CALqG,kBAKjF,mBACtB,GAAG,EAE0BL,CAAAA,EAAAA,EAAAA,CAAAA,CAAoBA,CAAC,CAChDC,sBAAwBC,GACtB,EAAIA,EAAMC,WAAW,IAAID,EAAMC,WAAW,CAACwB,SAAS,IAAIzB,EAAMC,WAAW,CAACwB,SAAS,CAAC5B,EAAO,IAAIG,EAAMC,WAAW,CAACyB,OAAO,IAAI1B,EAAMC,WAAW,CAACyB,OAAO,CAAC7B,EAAO,IAAGG,EAAMC,WAAW,CAAC0B,KAAK,IAAI3B,EAAMC,WAAW,CAAC0B,KAAK,CAAC9B,EAAO,IAAGG,EAAMC,WAAW,CAAC2B,MAAM,IAAI5B,EAAMC,WAAW,CAAC2B,MAAM,CAAC/B,EAAO,IAAGG,EAAMC,WAAW,CAACU,WAAW,IAAIX,EAAMC,WAAW,CAACU,WAAW,CAACd,EAAO,IAAGG,EAAMC,WAAW,CAAC4B,MAAM,IAAI7B,EAAMC,WAAW,CAAC4B,MAAM,CAAChC,EAAO,EAAE,CAG5Z,EAETM,mBAAoB,eACtB,GAAG,EAEYP,gBAAgBA,EAAC,CC9NhC,4CACA,4BACA,WACA,OAAe,EAAQ,KAAuD,CAC9E,EACA,SAFsB,qKCiItB,MAzHwB,IACpB,GAAM,GAAEtB,CAAC,CAAE,CAAGC,CAAAA,EAAAA,EAAAA,EAAAA,CAAcA,CAAC,MAwHlBmB,IAvHL,CAACoC,EAAWC,EAAe,CAAGC,CAAAA,EAAAA,EAAAA,EAuHT,MAvHSA,CAAQA,CAAC,EAAE,EACzC,EAAGC,EAAW,CAAGD,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAC,IAC1B,CAACE,EAAWC,EAAa,CAAGH,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAC,GACrC,CAACI,EAASC,EAAW,CAAGL,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAC,IACjC,CAACM,EAAaC,EAAS,CAAGP,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,EAAC,GACnC,CAACQ,EAAkBC,EAAoB,CAAGT,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAC,CAAC,GACpDU,EAAY,IAAMH,GAAS,GAE3BI,EAAU,CACZ,CACIC,KAAMtE,EAAE,kCACRuE,SAAU,OACd,EACA,CACID,KAAMtE,EAAE,iCACRuE,SAAU,OACVC,KAAOC,GAAW,UAACC,IAAAA,CAAElE,UAAW,OAAc,OAAPiE,EAAEE,IAAI,GACjD,EACA,CACIL,KAAMtE,EAAE,mCACRuE,SAAU,GACVC,KAAM,GACF,WAACI,MAAAA,WACG,UAAC9D,IAAIA,CAACC,KAAK,6BAA6BC,GAAI,OAAvCF,4BAAgF,OAAN2D,EAAEI,GAAG,WAEhF,UAACH,IAAAA,CAAElE,UAAU,uBAEV,OAEP,UAACsE,IAAAA,CAAEC,QAAS,IAAMC,EAAWP,YACzB,UAACC,IAAAA,CAAElE,UAAU,4BACZ,MAGjB,EACH,CACKyE,EAAmB,CACrBC,KAAM,CAAErE,MAAO,KAAM,EACrBsE,MAAOrB,EACPsB,KAAM,EACNC,MAAO,CAAC,CACZ,EAEAC,CAAAA,EAAAA,EAAAA,SAAAA,CAASA,CAAC,KACNC,EAAkBN,EACtB,EAAG,EAAE,EAEL,IAAMM,EAAoB,MAAOC,IAC7B7B,EAAW,IACX,IAAM8B,EAAW,MAAMC,EAAAA,CAAUA,CAACC,GAAG,CAAC,cAAeH,GACjDC,GAAYA,EAASG,IAAI,EAAIH,EAASG,IAAI,CAACC,MAAM,CAAG,GAAG,CACvDpC,EAAegC,EAASG,IAAI,EAC5B/B,EAAa4B,EAASK,UAAU,EAChCnC,EAAW,IAEnB,EAQMoC,EAAsB,MAAOC,EAAiBZ,KAChDH,EAAiBE,KAAK,CAAGa,EACzBf,EAAiBG,IAAI,CAAGA,EACxBzB,GAAW,GACX,IAAM8B,EAAW,MAAMC,EAAAA,CAAUA,CAACC,GAAG,CAAC,cAAeV,GACjDQ,GAAYA,EAASG,IAAI,EAAIH,EAASG,IAAI,CAACC,MAAM,CAAG,GAAG,CACvDpC,EAAegC,EAASG,IAAI,EAC5B7B,EAAWiC,GACXrC,EAAW,IAEnB,EAEMsC,EAAe,UACjB,GAAI,CACA,MAAMP,EAAAA,CAAUA,CAACQ,MAAM,CAAC,eAAgC,OAAjBhC,IACvCqB,EAAkBN,GAClBhB,EAAS,IACTkC,EAAAA,EAAKA,CAACC,OAAO,CAACpG,EAAE,gEACpB,CAAE,MAAOqG,EAAO,CACZF,EAAAA,EAAKA,CAACE,KAAK,CAACrG,EAAE,0DAClB,CACJ,EAEMgF,EAAa,MAAOsB,IACtBnC,EAAoBmC,EAAIzB,GAAG,EAC3BZ,GAAS,EACb,EAEA,MACI,WAACW,MAAAA,WACG,WAAC2B,EAAAA,CAAKA,CAAAA,CAACC,KAAMxC,EAAayC,OAAQrC,YAC9B,UAACmC,EAAAA,CAAKA,CAACG,MAAM,EAACC,WAAW,aACrB,UAACJ,EAAAA,CAAKA,CAACK,KAAK,WAAE5G,EAAE,iDAEpB,UAACuG,EAAAA,CAAKA,CAACM,IAAI,WAAE7G,EAAE,oEACf,WAACuG,EAAAA,CAAKA,CAACO,MAAM,YACT,UAAC7F,EAAAA,CAAMA,CAAAA,CAACC,QAAQ,YAAY6D,QAASX,WAChCpE,EAAE,qCAEP,UAACiB,EAAAA,CAAMA,CAAAA,CAACC,QAAQ,UAAU6D,QAASkB,WAC9BjG,EAAE,wCAKf,UAAC+G,EAAAA,CAAQA,CAAAA,CACL1C,QAASA,EACTuB,KAAMpC,EACNI,UAAWA,EACXoD,UAAW,GACXjB,oBAAqBA,EACrBkB,iBAzDc7B,CAyDI6B,GAxD1BhC,EAAiBE,KAAK,CAAGrB,EACzBmB,EAAiBG,IAAI,CAAGA,EACxBG,EAAkBN,EACtB,MAyDJ,6GC/FA,SAAS8B,EAASG,CAAoB,EACpC,GAAM,GAAElH,CAAC,CAAE,CAAGC,CAAAA,EAAAA,EAAAA,EAAAA,CAAcA,CAAC,UACvBkH,EAA6B,CACjCC,gBAAiBpH,EAAE,cACnB,EACI,SACJqE,CAAO,MACPuB,CAAI,WACJhC,CAAS,CACTyD,uBAAqB,WACrBC,CAAS,oBACTC,CAAkB,qBAClBxB,CAAmB,kBACnBkB,CAAgB,aAChBO,CAAW,oBACXC,CAAkB,gBAClBC,CAAc,SACdC,CAAO,WACPX,CAAS,sBACTY,CAAoB,mBACpBC,CAAiB,YACjBC,CAAU,QACVC,CAAM,kBACNC,CAAgB,cAChBC,CAAY,CAEZ,CADA,EACGC,EACJ,CAAGhB,EAGEiB,EAAiB,4BACrBhB,EACAiB,gBAAiBpI,EAAE,IAP0C,MAQ7DqI,UAAU,UACVhE,EACAuB,KAAMA,GAAQ,EAAE,CAChB0C,OAAO,EACPC,2BAA4BlB,EAC5BmB,UAAWlB,EACXmB,gBAAiBd,qBACjBJ,EACAmB,YAAY,EACZC,iBAAkB3B,EAClB4B,kBAAmBnB,GAA0C,GAC7DoB,eADwCpB,cACVD,GAA4B,CAAC,GAAI,GAAI,GAAI,CAA3BA,EAA+B,GAAG,CAC9EsB,oBAAqBlF,EACrBmF,oBAAqBhD,EACrBiD,aAAc/B,iBACdS,uBACAE,oBACAC,EACAoB,kBAAmB,UAACC,EAAAA,CAAYA,CAAAA,CAAAA,GAChCC,SAAU,UAACzE,IAAAA,CAAElE,UAAU,6CACvBsH,SACAC,eACAE,mBACAD,EACAxH,UAAW,WACb,EACA,MACE,UAAC4I,EAAAA,EAASA,CAAAA,CAAE,GAAGjB,CAAc,EAEjC,CAEApB,EAASsC,YAAY,CAAG,CACtBb,WAAW,EACXE,YAAY,EACZ9E,UAAW,KACXoD,WAAW,EACXY,qBAAsB,KACtBC,mBAAmB,EACnBC,YAAY,EACZE,kBAAkB,CACpB,EAEA,MAAejB,QAAQA,EAAC,sDC5GT,SAASnG,EAAYsG,CAAuB,EACzD,MACE,UAACoC,KAAAA,CAAG9I,UAAU,wBAAgB0G,EAAMrG,KAAK,EAE7C", "sources": ["webpack://_N_E/./pages/adminsettings/updateType/index.tsx", "webpack://_N_E/./pages/adminsettings/permissions.tsx", "webpack://_N_E/?792c", "webpack://_N_E/./pages/adminsettings/updateType/updateTypeTable.tsx", "webpack://_N_E/./components/common/RKITable.tsx", "webpack://_N_E/./components/common/PageHeading.tsx"], "sourcesContent": ["//Import Library\r\nimport { Container, <PERSON>, <PERSON>, Button } from \"react-bootstrap\";\r\nimport Link from \"next/link\";\r\n\r\n//Import services/components\r\nimport PageHeading from \"../../../components/common/PageHeading\";\r\nimport UpdateTypeTable from \"./updateTypeTable\";\r\nimport { useTranslation } from 'next-i18next';\r\nimport canAddAreaOfWork from \"../permissions\";\r\nconst UpdateTypeIndex = (_props: any) => {\r\n  const { t } = useTranslation('common');\r\n  const ShowUpdateTypeIndex = () => {\r\n    return (\r\n      <Container style={{ overflowX: \"hidden\" }} fluid className=\"p-0\">\r\n        <Row>\r\n          <Col xs={12}>\r\n            <PageHeading title={t(\"adminsetting.updatestype.UpdateType\")} />\r\n          </Col>\r\n        </Row>\r\n        <Row>\r\n          <Col xs={12}>\r\n            <Link\r\n              href=\"/adminsettings/[...routes]\"\r\n              as=\"/adminsettings/create_update_type\"\r\n              >\r\n              <Button variant=\"secondary\" size=\"sm\">\r\n              {t(\"adminsetting.updatestype.AddUpdateType\")}\r\n              </Button>\r\n            </Link>\r\n          </Col>\r\n        </Row>\r\n        <Row className=\"mt-3\">\r\n          <Col xs={12}>\r\n            <UpdateTypeTable />\r\n          </Col>\r\n        </Row>\r\n      </Container>\r\n    );\r\n  }\r\n  \r\n  const ShowAddUpdateTypes = canAddAreaOfWork(() => <ShowUpdateTypeIndex />);\r\n  return(\r\n    <ShowAddUpdateTypes />\r\n  )\r\n}\r\nexport default UpdateTypeIndex;", "//Import services/components\r\nimport connectedAuthWrapper from 'redux-auth-wrapper/connectedAuthWrapper';\r\n\r\nconst create = \"create:any\";\r\nexport const canAddAreaOfWork = connectedAuthWrapper({\r\n  authenticatedSelector: (state) => {\r\n    if (state.permissions && state.permissions.area_of_work && state.permissions.area_of_work[create]) {\r\n      return true;\r\n    }\r\n    return false;\r\n  },\r\n  wrapperDisplayName: 'CanAddAreaOfWork',\r\n});\r\n\r\nexport const canAddCountry = connectedAuthWrapper({\r\n  authenticatedSelector: (state) => {\r\n    if (state.permissions && state.permissions.country && state.permissions.country[create]) {\r\n      return true;\r\n    }\r\n    return false;\r\n  },\r\n  wrapperDisplayName: 'CanAddCountry',\r\n});\r\n\r\nexport const canAddDeploymentStatus = connectedAuthWrapper({\r\n  authenticatedSelector: (state) => {\r\n    if (state.permissions && state.permissions.deployment_status && state.permissions.deployment_status[create]) {\r\n      return true;\r\n    }\r\n    return false;\r\n  },\r\n  wrapperDisplayName: 'CanAddDeploymentStatus',\r\n});\r\n\r\nexport const canAddEventStatus = connectedAuthWrapper({\r\n  authenticatedSelector: (state) => {\r\n    if (state.permissions && state.permissions.event_status && state.permissions.event_status[create]) {\r\n      return true;\r\n    }\r\n    return false;\r\n  },\r\n  wrapperDisplayName: 'CanAddEventStatus',\r\n});\r\n\r\nexport const canAddExpertise = connectedAuthWrapper({\r\n  authenticatedSelector: (state) => {\r\n    if (state.permissions && state.permissions.expertise && state.permissions.expertise[create]) {\r\n      return true;\r\n    }\r\n    return false;\r\n  },\r\n  wrapperDisplayName: 'CanAddExpertise',\r\n});\r\n\r\nexport const canAddFocalPointApproval = connectedAuthWrapper({\r\n  authenticatedSelector: (state) => {\r\n    if (state.permissions && state.permissions.institution_focal_point && state.permissions.institution_focal_point[create]) {\r\n      return true;\r\n    }\r\n    return false;\r\n  },\r\n  wrapperDisplayName: 'CanAddFocalPointApproval',\r\n});\r\n\r\nexport const canAddVspaceApproval = connectedAuthWrapper({\r\n  authenticatedSelector: (state) => {\r\n    if (state.permissions && state.permissions.institution_focal_point && state.permissions.institution_focal_point[create]) {\r\n      return true;\r\n    }\r\n    return false;\r\n  },\r\n  wrapperDisplayName: 'CanAddVspaceApproval',\r\n});\r\nexport const canAddHazards = connectedAuthWrapper({\r\n  authenticatedSelector: (state) => {\r\n    if (state.permissions && state.permissions.hazard && state.permissions.hazard[create]) {\r\n      return true;\r\n    }\r\n    return false;\r\n  },\r\n  wrapperDisplayName: 'CanAddHazards',\r\n});\r\n\r\nexport const canAddHazardTypes = connectedAuthWrapper({\r\n  authenticatedSelector: (state) => {\r\n    if (state.permissions && state.permissions.hazard_type && state.permissions.hazard_type[create]) {\r\n      return true;\r\n    }\r\n    return false;\r\n  },\r\n  wrapperDisplayName: 'CanAddHazardTypes',\r\n}); \r\n\r\nexport const canAddOrganisationApproval = connectedAuthWrapper({\r\n  authenticatedSelector: (state) => {\r\n    if (state.permissions && state.permissions.institution && state.permissions.institution[create]) {\r\n      return true;\r\n    }\r\n    return false;\r\n  },\r\n  wrapperDisplayName: 'CanAddOrganisationApproval',\r\n}); \r\n\r\nexport const canAddOrganisationNetworks = connectedAuthWrapper({\r\n  authenticatedSelector: (state) => {\r\n    if (state.permissions && state.permissions.institution_network && state.permissions.institution_network[create]) {\r\n      return true;\r\n    }\r\n    return false;\r\n  },\r\n  wrapperDisplayName: 'CanAddOrganisationNetworks',\r\n});\r\n\r\nexport const canAddOrganisationTypes = connectedAuthWrapper({\r\n  authenticatedSelector: (state) => {\r\n    if (state.permissions && state.permissions.institution_type && state.permissions.institution_type[create]) {\r\n      return true;\r\n    }\r\n    return false;\r\n  },\r\n  wrapperDisplayName: 'CanAddOrganisationTypes',\r\n});\r\n\r\nexport const canAddOperationStatus = connectedAuthWrapper({\r\n  authenticatedSelector: (state) => {\r\n    if (state.permissions && state.permissions.operation_status && state.permissions.operation_status[create]) {\r\n      return true;\r\n    }\r\n    return false;\r\n  },\r\n  wrapperDisplayName: 'CanAddOperationStatus',\r\n});\r\n\r\nexport const canAddProjectStatus = connectedAuthWrapper({\r\n  authenticatedSelector: (state) => {\r\n    if (state.permissions && state.permissions.project_status && state.permissions.project_status[create]) {\r\n      return true;\r\n    }\r\n    return false;\r\n  },\r\n  wrapperDisplayName: 'CanAddProjectStatus',\r\n});\r\n\r\nexport const canAddRegions = connectedAuthWrapper({\r\n  authenticatedSelector: (state) => {\r\n    if (state.permissions && state.permissions.region && state.permissions.region[create]) {\r\n      return true;\r\n    }\r\n    return false;\r\n  },\r\n  wrapperDisplayName: 'CanAddRegions',\r\n});\r\n\r\nexport const canAddRiskLevels = connectedAuthWrapper({\r\n  authenticatedSelector: (state) => {\r\n    if (state.permissions && state.permissions.risk_level && state.permissions.risk_level[create]) {\r\n      return true;\r\n    }\r\n    return false;\r\n  },\r\n  wrapperDisplayName: 'CanAddRiskLevels',\r\n});\r\n\r\nexport const canAddSyndromes = connectedAuthWrapper({\r\n  authenticatedSelector: (state) => {\r\n    if (state.permissions && state.permissions.syndrome && state.permissions.syndrome[create]) {\r\n      return true;\r\n    }\r\n    return false;\r\n  },\r\n  wrapperDisplayName: 'CanAddSyndromes',\r\n});\r\n\r\nexport const canAddUpdateTypes = connectedAuthWrapper({\r\n  authenticatedSelector: (state) => {\r\n    if (state.permissions && state.permissions.update_type && state.permissions.update_type[create]) {\r\n      return true;\r\n    }\r\n    return false;\r\n  },\r\n  wrapperDisplayName: 'CanAddUpdateTypes',\r\n});\r\n\r\nexport const canAddUsers = connectedAuthWrapper({\r\n  authenticatedSelector: (state) => {\r\n    if (state.permissions && state.permissions.users && state.permissions.users[create]) {\r\n      return true;\r\n    }\r\n    return false;\r\n  },\r\n  wrapperDisplayName: 'CanAddUsers',\r\n});\r\n\r\nexport const canAddWorldRegion = connectedAuthWrapper({\r\n  authenticatedSelector: (state) => {\r\n    if (state.permissions && state.permissions.worl_region && state.permissions.worl_region[create]) {\r\n      return true;\r\n    }\r\n    return false;\r\n  },\r\n  wrapperDisplayName: 'CanAddWorldRegion',\r\n});\r\n\r\nexport const canAddLandingPage = connectedAuthWrapper({\r\n  authenticatedSelector: (state) => {\r\n    if (state.permissions && state.permissions.landing_page && state.permissions.landing_page[create]) {\r\n      return true;\r\n    }\r\n    return false;\r\n  },\r\n  wrapperDisplayName: 'CanAddLandingPage',\r\n});\r\n\r\nexport const canAddContent = connectedAuthWrapper({\r\n  authenticatedSelector: (state) => {\r\n    if (state.permissions && state.permissions.operation && state.permissions.operation[create] && state.permissions.project && state.permissions.project[create]&& state.permissions.event && state.permissions.event[create]&& state.permissions.vspace && state.permissions.vspace[create]&& state.permissions.institution && state.permissions.institution[create]&& state.permissions.update && state.permissions.update[create]) {\r\n      return true;\r\n    }\r\n    return false;\r\n  },\r\n  wrapperDisplayName: 'CanAddContent',\r\n});\r\n\r\nexport default canAddAreaOfWork;", "\n    (window.__NEXT_P = window.__NEXT_P || []).push([\n      \"/adminsettings/updateType\",\n      function () {\n        return require(\"private-next-pages/adminsettings/updateType/index.tsx\");\n      }\n    ]);\n    if(module.hot) {\n      module.hot.dispose(function () {\n        window.__NEXT_P.push([\"/adminsettings/updateType\"])\n      });\n    }\n  ", "//Import Library\r\nimport Link from \"next/link\";\r\nimport { useState, useEffect } from \"react\";\r\nimport { <PERSON><PERSON>, But<PERSON> } from \"react-bootstrap\";\r\n\r\nimport toast from 'react-hot-toast';\r\n\r\n//Import services/components\r\nimport RKITable from \"../../../components/common/RKITable\";\r\nimport { useTranslation } from 'next-i18next';\r\nimport apiService from \"../../../services/apiService\";\r\n\r\nconst UpdateTypeTable = (_props: any) => {\r\n    const { t } = useTranslation('common');\r\n    const [tabledata, setDataToTable] = useState([]);\r\n    const [, setLoading] = useState(false);\r\n    const [totalRows, setTotalRows] = useState(0);\r\n    const [perPage, setPerPage] = useState(10);\r\n    const [isModalShow, setModal] = useState(false);\r\n    const [selectUpdateType, setSelectUpdateType] = useState({});\r\n    const modalHide = () => setModal(false);\r\n    \r\n    const columns = [\r\n        {\r\n            name: t(\"adminsetting.updatestype.Title\"),\r\n            selector: \"title\",\r\n        },\r\n        {\r\n            name: t(\"adminsetting.updatestype.Icon\"),\r\n            selector: \"icon\",\r\n            cell: (d: any) => <i className={`fas ${d.icon}`}></i>,\r\n        },\r\n        {\r\n            name: t(\"adminsetting.updatestype.Action\"),\r\n            selector: \"\",\r\n            cell: (d: any) => (\r\n                <div>\r\n                    <Link href=\"/adminsettings/[...routes]\" as={`/adminsettings/edit_update_type/${d._id}`}>\r\n\r\n                        <i className=\"icon fas fa-edit\" />\r\n\r\n                    </Link>\r\n                    &nbsp;\r\n                    <a onClick={() => userAction(d)}>\r\n                        <i className=\"icon fas fa-trash-alt\" />\r\n                    </a>{\" \"}\r\n                </div>\r\n            ),\r\n        },\r\n    ];\r\n    const updateTypeParams = {\r\n        sort: { title: \"asc\" },\r\n        limit: perPage,\r\n        page: 1,\r\n        query: {},\r\n    };\r\n\r\n    useEffect(() => {\r\n        getUpdateTypeData(updateTypeParams);\r\n    }, []);\r\n\r\n    const getUpdateTypeData = async (updateTypeParams_initials: any) => {\r\n        setLoading(true);\r\n        const response = await apiService.get(\"/updatetype\", updateTypeParams_initials);\r\n        if (response && response.data && response.data.length > 0) {\r\n            setDataToTable(response.data);\r\n            setTotalRows(response.totalCount);\r\n            setLoading(false);\r\n        }\r\n    };\r\n\r\n    const handlePageChange = (page: any) => {\r\n        updateTypeParams.limit = perPage;\r\n        updateTypeParams.page = page;\r\n        getUpdateTypeData(updateTypeParams);\r\n    };\r\n\r\n    const handlePerRowsChange = async (newPerPage: any, page: any) => {\r\n        updateTypeParams.limit = newPerPage;\r\n        updateTypeParams.page = page;\r\n        setLoading(true);\r\n        const response = await apiService.get(\"/updatetype\", updateTypeParams);\r\n        if (response && response.data && response.data.length > 0) {\r\n            setDataToTable(response.data);\r\n            setPerPage(newPerPage);\r\n            setLoading(false);\r\n        }\r\n    };\r\n\r\n    const modalConfirm = async () => {\r\n        try {\r\n            await apiService.remove(`/updatetype/${selectUpdateType}`);\r\n            getUpdateTypeData(updateTypeParams);\r\n            setModal(false);\r\n            toast.success(t(\"adminsetting.updatestype.Table.updateTypeDeletedSuccessfully\"));\r\n        } catch (error) {\r\n            toast.error(t(\"adminsetting.updatestype.Table.errorDeletingUpdateType\"));\r\n        }\r\n    };\r\n\r\n    const userAction = async (row: any) => {\r\n        setSelectUpdateType(row._id);\r\n        setModal(true);\r\n    };\r\n\r\n    return (\r\n        <div>\r\n            <Modal show={isModalShow} onHide={modalHide}>\r\n                <Modal.Header closeButton>\r\n                    <Modal.Title>{t(\"adminsetting.updatestype.DeleteUpdateType\")}</Modal.Title>\r\n                </Modal.Header>\r\n                <Modal.Body>{t(\"adminsetting.updatestype.Areyousurewanttodeletethisupdatetype?\")}</Modal.Body>\r\n                <Modal.Footer>\r\n                    <Button variant=\"secondary\" onClick={modalHide}>\r\n                        {t(\"adminsetting.updatestype.Cancel\")}\r\n                    </Button>\r\n                    <Button variant=\"primary\" onClick={modalConfirm}>\r\n                        {t(\"adminsetting.updatestype.Yes\")}\r\n                    </Button>\r\n                </Modal.Footer>\r\n            </Modal>\r\n\r\n            <RKITable\r\n                columns={columns}\r\n                data={tabledata}\r\n                totalRows={totalRows}\r\n                pagServer={true}\r\n                handlePerRowsChange={handlePerRowsChange}\r\n                handlePageChange={handlePageChange}\r\n            />\r\n        </div>\r\n    );\r\n};\r\nexport default UpdateTypeTable;\r\n", "//Import Library\r\nimport React from 'react'\r\nimport DataTable  from 'react-data-table-component';\r\n\r\n//Import services/components\r\nimport { useTranslation } from 'next-i18next';\r\nimport CustomLoader from './CustomLoader';\r\n\r\ninterface RKITableProps {\r\n  columns: any[];\r\n  data: any[];\r\n  totalRows?: number;\r\n  resetPaginationToggle?: boolean;\r\n  subheader?: boolean;\r\n  subHeaderComponent?: React.ReactNode;\r\n  handlePerRowsChange?: (newPerPage: number, page: number) => void;\r\n  handlePageChange?: (page: number) => void;\r\n  rowsPerPage?: number[];\r\n  defaultRowsPerPage?: number;\r\n  selectableRows?: boolean;\r\n  loading?: boolean;\r\n  pagServer?: boolean;\r\n  onSelectedRowsChange?: (selectedRows: any) => void;\r\n  clearSelectedRows?: boolean;\r\n  sortServer?: boolean;\r\n  onSort?: (column: any, sortDirection: string) => void;\r\n  persistTableHead?: boolean;\r\n  sortFunction?: (rows: any[], field: any, direction: 'asc' | 'desc') => any[];\r\n  noHeader?: boolean;\r\n  dense?: boolean;\r\n  pagination?: boolean;\r\n  paginationServer?: boolean;\r\n  paginationTotalRows?: number;\r\n  subHeaderAlign?: string;\r\n  [key: string]: any;\r\n}\r\n\r\nfunction RKITable(props: RKITableProps) {\r\n  const { t } = useTranslation('common');\r\n  const paginationComponentOptions = {\r\n    rowsPerPageText: t('Rowsperpage'),\r\n    };\r\n  const {\r\n    columns,\r\n    data,\r\n    totalRows,\r\n    resetPaginationToggle,\r\n    subheader,\r\n    subHeaderComponent,\r\n    handlePerRowsChange,\r\n    handlePageChange,\r\n    rowsPerPage,\r\n    defaultRowsPerPage,\r\n    selectableRows,\r\n    loading,\r\n    pagServer,\r\n    onSelectedRowsChange,\r\n    clearSelectedRows,\r\n    sortServer,\r\n    onSort,\r\n    persistTableHead,\r\n    sortFunction,\r\n    // Filter out any unknown props that might cause DOM warnings\r\n    ...otherProps\r\n  } = props;\r\n\r\n  // Only pass known DataTable props to avoid DOM warnings\r\n  const dataTableProps = {\r\n    paginationComponentOptions,\r\n    noDataComponent: t(\"NoData\"),\r\n    noHeader: true,\r\n    columns,\r\n    data: data || [],\r\n    dense: true,\r\n    paginationResetDefaultPage: resetPaginationToggle,\r\n    subHeader: subheader,\r\n    progressPending: loading,\r\n    subHeaderComponent,\r\n    pagination: true,\r\n    paginationServer: pagServer,\r\n    paginationPerPage: defaultRowsPerPage ? defaultRowsPerPage : 10,\r\n    paginationRowsPerPageOptions: rowsPerPage ? rowsPerPage : [10, 15, 20, 25, 30],\r\n    paginationTotalRows: totalRows,\r\n    onChangeRowsPerPage: handlePerRowsChange,\r\n    onChangePage: handlePageChange,\r\n    selectableRows,\r\n    onSelectedRowsChange,\r\n    clearSelectedRows,\r\n    progressComponent: <CustomLoader/>,\r\n    sortIcon: <i className=\"sort-icon fas fa-exchange-alt\" />,\r\n    sortServer,\r\n    onSort,\r\n    sortFunction,\r\n    persistTableHead,\r\n    className: \"rki-table\"\r\n  };\r\n  return (\r\n    <DataTable {...dataTableProps} />\r\n  )\r\n};\r\n\r\nRKITable.defaultProps = {\r\n  subHeader: false,\r\n  pagination: true,\r\n  totalRows: null,\r\n  pagServer: true,\r\n  onSelectedRowsChange: null,\r\n  clearSelectedRows: false,\r\n  sortServer: false,\r\n  persistTableHead: false\r\n}\r\n\r\nexport default RKITable;\r\n\r\n", "interface PageHeadingProps {\r\n  title: string; // Required based on actual usage\r\n}\r\n\r\nexport default function PageHeading(props: PageHeadingProps) {\r\n  return (\r\n    <h2 className=\"page-heading\">{props.title}</h2>\r\n  )\r\n}\r\n"], "names": ["_props", "t", "useTranslation", "UpdateTypeIndex", "ShowUpdateTypeIndex", "Container", "style", "overflowX", "fluid", "className", "Row", "Col", "xs", "PageHeading", "title", "Link", "href", "as", "<PERSON><PERSON>", "variant", "size", "UpdateTypeTable", "ShowAddUpdateTypes", "canAddAreaOfWork", "create", "connectedAuthWrapper", "authenticatedSelector", "state", "permissions", "area_of_work", "wrapperDisplayName", "country", "deployment_status", "event_status", "expertise", "institution_focal_point", "hazard", "hazard_type", "institution", "canAddOrganisationNetworks", "institution_network", "institution_type", "operation_status", "project_status", "region", "risk_level", "syndrome", "update_type", "canAddUsers", "users", "worl_region", "landing_page", "operation", "project", "event", "vspace", "update", "tabledata", "setDataToTable", "useState", "setLoading", "totalRows", "setTotalRows", "perPage", "setPerPage", "isModalShow", "setModal", "selectUpdateType", "setSelectUpdateType", "modalHide", "columns", "name", "selector", "cell", "d", "i", "icon", "div", "_id", "a", "onClick", "userAction", "updateTypeParams", "sort", "limit", "page", "query", "useEffect", "getUpdateTypeData", "updateTypeParams_initials", "response", "apiService", "get", "data", "length", "totalCount", "handlePerRowsChange", "newPerPage", "modalConfirm", "remove", "toast", "success", "error", "row", "Modal", "show", "onHide", "Header", "closeButton", "Title", "Body", "Footer", "RKITable", "pagServer", "handlePageChange", "props", "paginationComponentOptions", "rowsPerPageText", "resetPaginationToggle", "subheader", "subHeaderComponent", "rowsPerPage", "defaultRowsPerPage", "selectableRows", "loading", "onSelectedRowsChange", "clearSelectedRows", "sortServer", "onSort", "persistTableHead", "sortFunction", "otherProps", "dataTableProps", "noDataComponent", "<PERSON><PERSON><PERSON><PERSON>", "dense", "paginationResetDefaultPage", "subHeader", "progressPending", "pagination", "paginationServer", "paginationPerPage", "paginationRowsPerPageOptions", "paginationTotalRows", "onChangeRowsPerPage", "onChangePage", "progressComponent", "CustomLoader", "sortIcon", "DataTable", "defaultProps", "h2"], "sourceRoot": "", "ignoreList": []}