{"version": 3, "file": "static/chunks/pages/routePermissions-eea93bea24b9a179.js", "mappings": "gFACA,4CACA,oBACA,WACA,OAAe,EAAQ,KAAyC,CAChE,EACA,SAFsB", "sources": ["webpack://_N_E/?623c"], "sourcesContent": ["\n    (window.__NEXT_P = window.__NEXT_P || []).push([\n      \"/routePermissions\",\n      function () {\n        return require(\"private-next-pages/routePermissions.tsx\");\n      }\n    ]);\n    if(module.hot) {\n      module.hot.dispose(function () {\n        window.__NEXT_P.push([\"/routePermissions\"])\n      });\n    }\n  "], "names": [], "sourceRoot": "", "ignoreList": []}