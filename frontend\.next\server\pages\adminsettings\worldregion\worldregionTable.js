"use strict";(()=>{var e={};e.id=182,e.ids=[182,636,3220],e.modules={123:e=>{e.exports=require("dom-helpers/removeEventListener")},1332:e=>{e.exports=require("react-custom-scrollbars-2")},1428:e=>{e.exports=import("axios")},1680:e=>{e.exports=require("@restart/ui/utils")},1919:e=>{e.exports=require("react-transition-group/Transition")},3892:e=>{e.exports=require("classnames")},4048:e=>{e.exports=require("react-truncate")},6009:e=>{e.exports=require("dom-helpers/ownerDocument")},6952:e=>{e.exports=require("@restart/ui/Modal")},7374:e=>{e.exports=require("@restart/ui/Dropdown")},8732:e=>{e.exports=require("react/jsx-runtime")},9653:e=>{e.exports=require("@restart/ui/Overlay")},11242:e=>{e.exports=require("redux-persist/integration/react")},11688:e=>{e.exports=require("@restart/hooks/useWillUnmount")},12388:e=>{e.exports=require("dom-helpers/addEventListener")},13364:e=>{e.exports=require("redux-saga/effects")},14062:e=>{e.exports=import("react-redux")},14078:e=>{e.exports=import("swr")},14332:e=>{e.exports=require("uncontrollable")},16116:e=>{e.exports=require("invariant")},18622:e=>{e.exports=require("yup")},19121:e=>{e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},22326:e=>{e.exports=require("react-dom")},22541:e=>{e.exports=require("redux-persist/lib/storage")},25303:e=>{e.exports=require("@restart/ui/NavItem")},26324:e=>{e.exports=require("warning")},27825:e=>{e.exports=require("lodash")},27910:e=>{e.exports=require("stream")},28217:e=>{e.exports=require("@restart/ui/DropdownItem")},29021:e=>{e.exports=require("fs")},29294:e=>{e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},29628:e=>{e.exports=require("dom-helpers/addClass")},29825:e=>{e.exports=require("prop-types")},29841:e=>{e.exports=require("dom-helpers/hasClass")},30362:e=>{e.exports=require("@restart/ui/DropdownToggle")},33873:e=>{e.exports=require("path")},36653:e=>{e.exports=require("nprogress")},36955:e=>{e.exports=require("@restart/hooks/useBreakpoint")},37766:e=>{e.exports=require("@restart/hooks/useMergedRefs")},38609:e=>{e.exports=require("react-data-table-component")},39756:e=>{e.exports=import("redux")},40051:e=>{e.exports=require("dom-helpers/removeClass")},40361:e=>{e.exports=require("next/dist/compiled/next-server/pages.runtime.prod.js")},42893:e=>{e.exports=import("react-hot-toast")},43294:e=>{e.exports=require("formik")},50009:e=>{e.exports=require("@restart/ui/DropdownMenu")},55999:(e,r,t)=>{t.a(e,async(e,s)=>{try{t.r(r),t.d(r,{config:()=>q,default:()=>c,getServerSideProps:()=>m,getStaticPaths:()=>g,getStaticProps:()=>x,reportWebVitals:()=>h,routeModule:()=>v,unstable_getServerProps:()=>f,unstable_getServerSideProps:()=>S,unstable_getStaticParams:()=>b,unstable_getStaticPaths:()=>w,unstable_getStaticProps:()=>P});var o=t(63885),a=t(80237),i=t(81413),n=t(9616),u=t.n(n),l=t(72386),p=t(66116),d=e([l,p]);[l,p]=d.then?(await d)():d;let c=(0,i.M)(p,"default"),x=(0,i.M)(p,"getStaticProps"),g=(0,i.M)(p,"getStaticPaths"),m=(0,i.M)(p,"getServerSideProps"),q=(0,i.M)(p,"config"),h=(0,i.M)(p,"reportWebVitals"),P=(0,i.M)(p,"unstable_getStaticProps"),w=(0,i.M)(p,"unstable_getStaticPaths"),b=(0,i.M)(p,"unstable_getStaticParams"),f=(0,i.M)(p,"unstable_getServerProps"),S=(0,i.M)(p,"unstable_getServerSideProps"),v=new o.PagesRouteModule({definition:{kind:a.A.PAGES,page:"/adminsettings/worldregion/worldregionTable",pathname:"/adminsettings/worldregion/worldregionTable",bundlePath:"",filename:""},components:{App:l.default,Document:u()},userland:p});s()}catch(e){s(e)}})},56084:(e,r,t)=>{t.d(r,{A:()=>l});var s=t(8732);t(82015);var o=t(38609),a=t.n(o),i=t(88751),n=t(30370);function u(e){let{t:r}=(0,i.useTranslation)("common"),t={rowsPerPageText:r("Rowsperpage")},{columns:o,data:u,totalRows:l,resetPaginationToggle:p,subheader:d,subHeaderComponent:c,handlePerRowsChange:x,handlePageChange:g,rowsPerPage:m,defaultRowsPerPage:q,selectableRows:h,loading:P,pagServer:w,onSelectedRowsChange:b,clearSelectedRows:f,sortServer:S,onSort:v,persistTableHead:A,sortFunction:y,...j}=e,M={paginationComponentOptions:t,noDataComponent:r("NoData"),noHeader:!0,columns:o,data:u||[],dense:!0,paginationResetDefaultPage:p,subHeader:d,progressPending:P,subHeaderComponent:c,pagination:!0,paginationServer:w,paginationPerPage:q||10,paginationRowsPerPageOptions:m||[10,15,20,25,30],paginationTotalRows:l,onChangeRowsPerPage:x,onChangePage:g,selectableRows:h,onSelectedRowsChange:b,clearSelectedRows:f,progressComponent:(0,s.jsx)(n.A,{}),sortIcon:(0,s.jsx)("i",{className:"sort-icon fas fa-exchange-alt"}),sortServer:S,onSort:v,sortFunction:y,persistTableHead:A,className:"rki-table"};return(0,s.jsx)(a(),{...M})}u.defaultProps={subHeader:!1,pagination:!0,totalRows:null,pagServer:!0,onSelectedRowsChange:null,clearSelectedRows:!1,sortServer:!1,persistTableHead:!1};let l=u},57664:e=>{e.exports=require("dom-helpers/scrollbarSize")},58928:e=>{e.exports=require("dom-helpers/canUseDOM")},59717:e=>{e.exports=require("@restart/ui/DropdownContext")},60560:e=>{e.exports=require("dom-helpers/contains")},63033:e=>{e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},65447:e=>{e.exports=require("@restart/ui/Button")},66116:(e,r,t)=>{t.a(e,async(e,s)=>{try{t.r(r),t.d(r,{default:()=>m});var o=t(8732),a=t(82015),i=t(19918),n=t.n(i),u=t(12403),l=t(91353),p=t(42893),d=t(56084),c=t(63487),x=t(88751),g=e([p,c]);[p,c]=g.then?(await g)():g;let m=e=>{let{t:r}=(0,x.useTranslation)("common"),[t,s]=(0,a.useState)([]),[,i]=(0,a.useState)(!1),[g,m]=(0,a.useState)(0),[q,h]=(0,a.useState)(10),[P,w]=(0,a.useState)(!1),[b,f]=(0,a.useState)({}),S={sort:{title:"asc"},limit:q,page:1,query:{}},v=[{name:r("adminsetting.worldregion.table.Title"),selector:e=>e.title,sortable:!0},{name:r("adminsetting.worldregion.table.Code"),selector:e=>e.code,sortable:!0,cell:e=>e.code},{name:r("adminsetting.worldregion.table.Action"),selector:e=>e._id,sortable:!1,cell:e=>(0,o.jsxs)("div",{children:[(0,o.jsx)(n(),{href:"/adminsettings/[...routes]",as:`/adminsettings/edit_worldregion/${e._id}`,children:(0,o.jsx)("i",{className:"icon fas fa-edit"})}),"\xa0",(0,o.jsx)("a",{onClick:()=>j(e),children:(0,o.jsx)("i",{className:"icon fas fa-trash-alt"})})," "]})}],A=async()=>{i(!0);let e=await c.A.get("/worldregion",S);e&&e.data&&e.data.length>0&&(s(e.data),m(e.totalCount),i(!1))},y=async(e,r)=>{S.limit=e,S.page=r,i(!0);let t=await c.A.get("/worldregion",S);t&&t.data&&t.data.length>0&&(s(t.data),h(e),i(!1))},j=async e=>{f(e._id),w(!0)},M=async()=>{try{await c.A.remove(`/worldregion/${b}`),A(),w(!1),p.default.success(r("adminsetting.worldregion.table.worldRegionDeletedSuccessfully"))}catch(e){p.default.error(r("adminsetting.worldregion.table.errorDeletingWorldRegion"))}},C=()=>w(!1);return(0,a.useEffect)(()=>{A()},[]),(0,o.jsxs)("div",{children:[(0,o.jsxs)(u.A,{show:P,onHide:C,children:[(0,o.jsx)(u.A.Header,{closeButton:!0,children:(0,o.jsx)(u.A.Title,{children:r("adminsetting.worldregion.table.DeleteWorldregion")})}),(0,o.jsx)(u.A.Body,{children:r("adminsetting.worldregion.table.Areyousurewanttodeletethisworldregion?")}),(0,o.jsxs)(u.A.Footer,{children:[(0,o.jsx)(l.A,{variant:"secondary",onClick:C,children:r("adminsetting.worldregion.table.Cancel")}),(0,o.jsx)(l.A,{variant:"primary",onClick:M,children:r("adminsetting.worldregion.table.Yes")})]})]}),(0,o.jsx)(d.A,{columns:v,data:t,totalRows:g,pagServer:!0,handlePerRowsChange:y,handlePageChange:e=>{S.limit=q,S.page=e,A()}})]})};s()}catch(e){s(e)}})},67364:e=>{e.exports=require("@restart/hooks/useCallbackRef")},68455:e=>{e.exports=import("redux-saga")},69722:e=>{e.exports=require("es6-promise")},74075:e=>{e.exports=require("zlib")},74716:e=>{e.exports=require("moment")},74987:e=>{e.exports=require("@restart/ui/ModalManager")},78097:e=>{e.exports=require("next-redux-saga")},78634:e=>{e.exports=require("@restart/ui/Anchor")},80237:(e,r)=>{Object.defineProperty(r,"A",{enumerable:!0,get:function(){return t}});var t=function(e){return e.PAGES="PAGES",e.PAGES_API="PAGES_API",e.APP_PAGE="APP_PAGE",e.APP_ROUTE="APP_ROUTE",e.IMAGE="IMAGE",e}({})},81366:e=>{e.exports=require("redux-auth-wrapper/connectedAuthWrapper")},81413:(e,r)=>{Object.defineProperty(r,"M",{enumerable:!0,get:function(){return function e(r,t){return t in r?r[t]:"then"in r&&"function"==typeof r.then?r.then(r=>e(r,t)):"function"==typeof r&&"default"===t?r:void 0}}})},81521:e=>{e.exports=require("dom-helpers/querySelectorAll")},81895:e=>{e.exports=require("@restart/hooks/useEventCallback")},82015:e=>{e.exports=require("react")},86842:e=>{e.exports=require("@restart/ui/SelectableContext")},87571:e=>{e.exports=require("dom-helpers/transitionEnd")},88751:e=>{e.exports=require("next-i18next")},93787:e=>{e.exports=require("redux-persist")},94947:e=>{e.exports=require("@restart/hooks/useTimeout")},96196:e=>{e.exports=require("next-redux-wrapper")},98320:e=>{e.exports=require("@restart/hooks/useIsomorphicEffect")},99460:e=>{e.exports=require("dom-helpers/css")}};var r=require("../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[6089,9216,9616,2386],()=>t(55999));module.exports=s})();