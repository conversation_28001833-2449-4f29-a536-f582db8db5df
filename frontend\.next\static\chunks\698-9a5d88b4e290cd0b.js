"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[698],{50698:(t,e,n)=>{n.d(e,{UE:()=>U,ll:()=>M,rD:()=>$,UU:()=>B,cY:()=>N});let i=Math.min,o=Math.max,r=Math.round,l=Math.floor,c=t=>({x:t,y:t}),f={left:"right",right:"left",bottom:"top",top:"bottom"},u={start:"end",end:"start"};function s(t,e){return"function"==typeof t?t(e):t}function a(t){return t.split("-")[0]}function d(t){return t.split("-")[1]}function p(t){return"y"===t?"height":"width"}function h(t){return["top","bottom"].includes(a(t))?"y":"x"}function m(t){return"x"===h(t)?"y":"x"}function g(t){return t.replace(/start|end/g,t=>u[t])}function v(t){return t.replace(/left|right|bottom|top/g,t=>f[t])}function w(t){return"number"!=typeof t?{top:0,right:0,bottom:0,left:0,...t}:{top:t,right:t,bottom:t,left:t}}function y(t){let{x:e,y:n,width:i,height:o}=t;return{width:i,height:o,top:n,left:e,right:e+i,bottom:n+o,x:e,y:n}}function x(t,e,n){let i,{reference:o,floating:r}=t,l=h(e),c=m(e),f=p(c),u=a(e),s="y"===l,g=o.x+o.width/2-r.width/2,v=o.y+o.height/2-r.height/2,w=o[f]/2-r[f]/2;switch(u){case"top":i={x:g,y:o.y-r.height};break;case"bottom":i={x:g,y:o.y+o.height};break;case"right":i={x:o.x+o.width,y:v};break;case"left":i={x:o.x-r.width,y:v};break;default:i={x:o.x,y:o.y}}switch(d(e)){case"start":i[c]-=w*(n&&s?-1:1);break;case"end":i[c]+=w*(n&&s?-1:1)}return i}let b=async(t,e,n)=>{let{placement:i="bottom",strategy:o="absolute",middleware:r=[],platform:l}=n,c=r.filter(Boolean),f=await (null==l.isRTL?void 0:l.isRTL(e)),u=await l.getElementRects({reference:t,floating:e,strategy:o}),{x:s,y:a}=x(u,i,f),d=i,p={},h=0;for(let n=0;n<c.length;n++){let{name:r,fn:m}=c[n],{x:g,y:v,data:w,reset:y}=await m({x:s,y:a,initialPlacement:i,placement:d,strategy:o,middlewareData:p,rects:u,platform:l,elements:{reference:t,floating:e}});s=null!=g?g:s,a=null!=v?v:a,p={...p,[r]:{...p[r],...w}},y&&h<=50&&(h++,"object"==typeof y&&(y.placement&&(d=y.placement),y.rects&&(u=!0===y.rects?await l.getElementRects({reference:t,floating:e,strategy:o}):y.rects),{x:s,y:a}=x(u,d,f)),n=-1)}return{x:s,y:a,placement:d,strategy:o,middlewareData:p}};async function L(t,e){var n;void 0===e&&(e={});let{x:i,y:o,platform:r,rects:l,elements:c,strategy:f}=t,{boundary:u="clippingAncestors",rootBoundary:a="viewport",elementContext:d="floating",altBoundary:p=!1,padding:h=0}=s(e,t),m=w(h),g=c[p?"floating"===d?"reference":"floating":d],v=y(await r.getClippingRect({element:null==(n=await (null==r.isElement?void 0:r.isElement(g)))||n?g:g.contextElement||await (null==r.getDocumentElement?void 0:r.getDocumentElement(c.floating)),boundary:u,rootBoundary:a,strategy:f})),x="floating"===d?{x:i,y:o,width:l.floating.width,height:l.floating.height}:l.reference,b=await (null==r.getOffsetParent?void 0:r.getOffsetParent(c.floating)),L=await (null==r.isElement?void 0:r.isElement(b))&&await (null==r.getScale?void 0:r.getScale(b))||{x:1,y:1},T=y(r.convertOffsetParentRelativeRectToViewportRelativeRect?await r.convertOffsetParentRelativeRectToViewportRelativeRect({elements:c,rect:x,offsetParent:b,strategy:f}):x);return{top:(v.top-T.top+m.top)/L.y,bottom:(T.bottom-v.bottom+m.bottom)/L.y,left:(v.left-T.left+m.left)/L.x,right:(T.right-v.right+m.right)/L.x}}async function T(t,e){let{placement:n,platform:i,elements:o}=t,r=await (null==i.isRTL?void 0:i.isRTL(o.floating)),l=a(n),c=d(n),f="y"===h(n),u=["left","top"].includes(l)?-1:1,p=r&&f?-1:1,m=s(e,t),{mainAxis:g,crossAxis:v,alignmentAxis:w}="number"==typeof m?{mainAxis:m,crossAxis:0,alignmentAxis:null}:{mainAxis:m.mainAxis||0,crossAxis:m.crossAxis||0,alignmentAxis:m.alignmentAxis};return c&&"number"==typeof w&&(v="end"===c?-1*w:w),f?{x:v*p,y:g*u}:{x:g*u,y:v*p}}var R=n(84974);function E(t){let e=(0,R.L9)(t),n=parseFloat(e.width)||0,i=parseFloat(e.height)||0,o=(0,R.sb)(t),l=o?t.offsetWidth:n,c=o?t.offsetHeight:i,f=r(n)!==l||r(i)!==c;return f&&(n=l,i=c),{width:n,height:i,$:f}}function k(t){return(0,R.vq)(t)?t:t.contextElement}function C(t){let e=k(t);if(!(0,R.sb)(e))return c(1);let n=e.getBoundingClientRect(),{width:i,height:o,$:l}=E(e),f=(l?r(n.width):n.width)/i,u=(l?r(n.height):n.height)/o;return f&&Number.isFinite(f)||(f=1),u&&Number.isFinite(u)||(u=1),{x:f,y:u}}let A=c(0);function O(t){let e=(0,R.zk)(t);return(0,R.Tc)()&&e.visualViewport?{x:e.visualViewport.offsetLeft,y:e.visualViewport.offsetTop}:A}function P(t,e,n,i){var o;void 0===e&&(e=!1),void 0===n&&(n=!1);let r=t.getBoundingClientRect(),l=k(t),f=c(1);e&&(i?(0,R.vq)(i)&&(f=C(i)):f=C(t));let u=(void 0===(o=n)&&(o=!1),i&&(!o||i===(0,R.zk)(l))&&o)?O(l):c(0),s=(r.left+u.x)/f.x,a=(r.top+u.y)/f.y,d=r.width/f.x,p=r.height/f.y;if(l){let t=(0,R.zk)(l),e=i&&(0,R.vq)(i)?(0,R.zk)(i):i,n=t,o=(0,R._m)(n);for(;o&&i&&e!==n;){let t=C(o),e=o.getBoundingClientRect(),i=(0,R.L9)(o),r=e.left+(o.clientLeft+parseFloat(i.paddingLeft))*t.x,l=e.top+(o.clientTop+parseFloat(i.paddingTop))*t.y;s*=t.x,a*=t.y,d*=t.x,p*=t.y,s+=r,a+=l,n=(0,R.zk)(o),o=(0,R._m)(n)}}return y({width:d,height:p,x:s,y:a})}function q(t,e){let n=(0,R.CP)(t).scrollLeft;return e?e.left+n:P((0,R.ep)(t)).left+n}function D(t,e,n){void 0===n&&(n=!1);let i=t.getBoundingClientRect();return{x:i.left+e.scrollLeft-(n?0:q(t,i)),y:i.top+e.scrollTop}}function F(t,e,n){let i;if("viewport"===e)i=function(t,e){let n=(0,R.zk)(t),i=(0,R.ep)(t),o=n.visualViewport,r=i.clientWidth,l=i.clientHeight,c=0,f=0;if(o){r=o.width,l=o.height;let t=(0,R.Tc)();(!t||t&&"fixed"===e)&&(c=o.offsetLeft,f=o.offsetTop)}return{width:r,height:l,x:c,y:f}}(t,n);else if("document"===e)i=function(t){let e=(0,R.ep)(t),n=(0,R.CP)(t),i=t.ownerDocument.body,r=o(e.scrollWidth,e.clientWidth,i.scrollWidth,i.clientWidth),l=o(e.scrollHeight,e.clientHeight,i.scrollHeight,i.clientHeight),c=-n.scrollLeft+q(t),f=-n.scrollTop;return"rtl"===(0,R.L9)(i).direction&&(c+=o(e.clientWidth,i.clientWidth)-r),{width:r,height:l,x:c,y:f}}((0,R.ep)(t));else if((0,R.vq)(e))i=function(t,e){let n=P(t,!0,"fixed"===e),i=n.top+t.clientTop,o=n.left+t.clientLeft,r=(0,R.sb)(t)?C(t):c(1),l=t.clientWidth*r.x,f=t.clientHeight*r.y;return{width:l,height:f,x:o*r.x,y:i*r.y}}(e,n);else{let n=O(t);i={x:e.x-n.x,y:e.y-n.y,width:e.width,height:e.height}}return y(i)}function S(t){return"static"===(0,R.L9)(t).position}function z(t,e){if(!(0,R.sb)(t)||"fixed"===(0,R.L9)(t).position)return null;if(e)return e(t);let n=t.offsetParent;return(0,R.ep)(t)===n&&(n=n.ownerDocument.body),n}function H(t,e){let n=(0,R.zk)(t);if((0,R.Tf)(t))return n;if(!(0,R.sb)(t)){let e=(0,R.$4)(t);for(;e&&!(0,R.eu)(e);){if((0,R.vq)(e)&&!S(e))return e;e=(0,R.$4)(e)}return n}let i=z(t,e);for(;i&&(0,R.Lv)(i)&&S(i);)i=z(i,e);return i&&(0,R.eu)(i)&&S(i)&&!(0,R.sQ)(i)?n:i||(0,R.gJ)(t)||n}let W=async function(t){let e=this.getOffsetParent||H,n=this.getDimensions,i=await n(t.floating);return{reference:function(t,e,n){let i=(0,R.sb)(e),o=(0,R.ep)(e),r="fixed"===n,l=P(t,!0,r,e),f={scrollLeft:0,scrollTop:0},u=c(0);if(i||!i&&!r)if(("body"!==(0,R.mq)(e)||(0,R.ZU)(o))&&(f=(0,R.CP)(e)),i){let t=P(e,!0,r,e);u.x=t.x+e.clientLeft,u.y=t.y+e.clientTop}else o&&(u.x=q(o));r&&!i&&o&&(u.x=q(o));let s=!o||i||r?c(0):D(o,f);return{x:l.left+f.scrollLeft-u.x-s.x,y:l.top+f.scrollTop-u.y-s.y,width:l.width,height:l.height}}(t.reference,await e(t.floating),t.strategy),floating:{x:0,y:0,width:i.width,height:i.height}}},V={convertOffsetParentRelativeRectToViewportRelativeRect:function(t){let{elements:e,rect:n,offsetParent:i,strategy:o}=t,r="fixed"===o,l=(0,R.ep)(i),f=!!e&&(0,R.Tf)(e.floating);if(i===l||f&&r)return n;let u={scrollLeft:0,scrollTop:0},s=c(1),a=c(0),d=(0,R.sb)(i);if((d||!d&&!r)&&(("body"!==(0,R.mq)(i)||(0,R.ZU)(l))&&(u=(0,R.CP)(i)),(0,R.sb)(i))){let t=P(i);s=C(i),a.x=t.x+i.clientLeft,a.y=t.y+i.clientTop}let p=!l||d||r?c(0):D(l,u,!0);return{width:n.width*s.x,height:n.height*s.y,x:n.x*s.x-u.scrollLeft*s.x+a.x+p.x,y:n.y*s.y-u.scrollTop*s.y+a.y+p.y}},getDocumentElement:R.ep,getClippingRect:function(t){let{element:e,boundary:n,rootBoundary:r,strategy:l}=t,c=[..."clippingAncestors"===n?(0,R.Tf)(e)?[]:function(t,e){let n=e.get(t);if(n)return n;let i=(0,R.v9)(t,[],!1).filter(t=>(0,R.vq)(t)&&"body"!==(0,R.mq)(t)),o=null,r="fixed"===(0,R.L9)(t).position,l=r?(0,R.$4)(t):t;for(;(0,R.vq)(l)&&!(0,R.eu)(l);){let e=(0,R.L9)(l),n=(0,R.sQ)(l);n||"fixed"!==e.position||(o=null),(r?!n&&!o:!n&&"static"===e.position&&!!o&&["absolute","fixed"].includes(o.position)||(0,R.ZU)(l)&&!n&&function t(e,n){let i=(0,R.$4)(e);return!(i===n||!(0,R.vq)(i)||(0,R.eu)(i))&&("fixed"===(0,R.L9)(i).position||t(i,n))}(t,l))?i=i.filter(t=>t!==l):o=e,l=(0,R.$4)(l)}return e.set(t,i),i}(e,this._c):[].concat(n),r],f=c[0],u=c.reduce((t,n)=>{let r=F(e,n,l);return t.top=o(r.top,t.top),t.right=i(r.right,t.right),t.bottom=i(r.bottom,t.bottom),t.left=o(r.left,t.left),t},F(e,f,l));return{width:u.right-u.left,height:u.bottom-u.top,x:u.left,y:u.top}},getOffsetParent:H,getElementRects:W,getClientRects:function(t){return Array.from(t.getClientRects())},getDimensions:function(t){let{width:e,height:n}=E(t);return{width:e,height:n}},getScale:C,isElement:R.vq,isRTL:function(t){return"rtl"===(0,R.L9)(t).direction}};function _(t,e){return t.x===e.x&&t.y===e.y&&t.width===e.width&&t.height===e.height}function M(t,e,n,r){let c;void 0===r&&(r={});let{ancestorScroll:f=!0,ancestorResize:u=!0,elementResize:s="function"==typeof ResizeObserver,layoutShift:a="function"==typeof IntersectionObserver,animationFrame:d=!1}=r,p=k(t),h=f||u?[...p?(0,R.v9)(p):[],...(0,R.v9)(e)]:[];h.forEach(t=>{f&&t.addEventListener("scroll",n,{passive:!0}),u&&t.addEventListener("resize",n)});let m=p&&a?function(t,e){let n,r=null,c=(0,R.ep)(t);function f(){var t;clearTimeout(n),null==(t=r)||t.disconnect(),r=null}return!function u(s,a){void 0===s&&(s=!1),void 0===a&&(a=1),f();let d=t.getBoundingClientRect(),{left:p,top:h,width:m,height:g}=d;if(s||e(),!m||!g)return;let v=l(h),w=l(c.clientWidth-(p+m)),y={rootMargin:-v+"px "+-w+"px "+-l(c.clientHeight-(h+g))+"px "+-l(p)+"px",threshold:o(0,i(1,a))||1},x=!0;function b(e){let i=e[0].intersectionRatio;if(i!==a){if(!x)return u();i?u(!1,i):n=setTimeout(()=>{u(!1,1e-7)},1e3)}1!==i||_(d,t.getBoundingClientRect())||u(),x=!1}try{r=new IntersectionObserver(b,{...y,root:c.ownerDocument})}catch(t){r=new IntersectionObserver(b,y)}r.observe(t)}(!0),f}(p,n):null,g=-1,v=null;s&&(v=new ResizeObserver(t=>{let[i]=t;i&&i.target===p&&v&&(v.unobserve(e),cancelAnimationFrame(g),g=requestAnimationFrame(()=>{var t;null==(t=v)||t.observe(e)})),n()}),p&&!d&&v.observe(p),v.observe(e));let w=d?P(t):null;return d&&function e(){let i=P(t);w&&!_(w,i)&&n(),w=i,c=requestAnimationFrame(e)}(),n(),()=>{var t;h.forEach(t=>{f&&t.removeEventListener("scroll",n),u&&t.removeEventListener("resize",n)}),null==m||m(),null==(t=v)||t.disconnect(),v=null,d&&cancelAnimationFrame(c)}}let N=function(t){return void 0===t&&(t=0),{name:"offset",options:t,async fn(e){var n,i;let{x:o,y:r,placement:l,middlewareData:c}=e,f=await T(e,t);return l===(null==(n=c.offset)?void 0:n.placement)&&null!=(i=c.arrow)&&i.alignmentOffset?{}:{x:o+f.x,y:r+f.y,data:{...f,placement:l}}}}},B=function(t){return void 0===t&&(t={}),{name:"flip",options:t,async fn(e){var n,i,o,r,l,c;let{placement:f,middlewareData:u,rects:w,initialPlacement:y,platform:x,elements:b}=e,{mainAxis:T=!0,crossAxis:R=!0,fallbackPlacements:E,fallbackStrategy:k="bestFit",fallbackAxisSideDirection:C="none",flipAlignment:A=!0,...O}=s(t,e);if(null!=(n=u.arrow)&&n.alignmentOffset)return{};let P=a(f),q=h(y),D=a(y)===y,F=await (null==x.isRTL?void 0:x.isRTL(b.floating)),S=E||(D||!A?[v(y)]:function(t){let e=v(t);return[g(t),e,g(e)]}(y)),z="none"!==C;!E&&z&&S.push(...function(t,e,n,i){let o=d(t),r=function(t,e,n){let i=["left","right"],o=["right","left"];switch(t){case"top":case"bottom":if(n)return e?o:i;return e?i:o;case"left":case"right":return e?["top","bottom"]:["bottom","top"];default:return[]}}(a(t),"start"===n,i);return o&&(r=r.map(t=>t+"-"+o),e&&(r=r.concat(r.map(g)))),r}(y,A,C,F));let H=[y,...S],W=await L(e,O),V=[],_=(null==(i=u.flip)?void 0:i.overflows)||[];if(T&&V.push(W[P]),R){let t=function(t,e,n){void 0===n&&(n=!1);let i=d(t),o=m(t),r=p(o),l="x"===o?i===(n?"end":"start")?"right":"left":"start"===i?"bottom":"top";return e.reference[r]>e.floating[r]&&(l=v(l)),[l,v(l)]}(f,w,F);V.push(W[t[0]],W[t[1]])}if(_=[..._,{placement:f,overflows:V}],!V.every(t=>t<=0)){let t=((null==(o=u.flip)?void 0:o.index)||0)+1,e=H[t];if(e){let n="alignment"===R&&q!==h(e),i=(null==(l=_[0])?void 0:l.overflows[0])>0;if(!n||i)return{data:{index:t,overflows:_},reset:{placement:e}}}let n=null==(r=_.filter(t=>t.overflows[0]<=0).sort((t,e)=>t.overflows[1]-e.overflows[1])[0])?void 0:r.placement;if(!n)switch(k){case"bestFit":{let t=null==(c=_.filter(t=>{if(z){let e=h(t.placement);return e===q||"y"===e}return!0}).map(t=>[t.placement,t.overflows.filter(t=>t>0).reduce((t,e)=>t+e,0)]).sort((t,e)=>t[1]-e[1])[0])?void 0:c[0];t&&(n=t);break}case"initialPlacement":n=y}if(f!==n)return{reset:{placement:n}}}return{}}}},U=t=>({name:"arrow",options:t,async fn(e){let{x:n,y:r,placement:l,rects:c,platform:f,elements:u,middlewareData:a}=e,{element:h,padding:g=0}=s(t,e)||{};if(null==h)return{};let v=w(g),y={x:n,y:r},x=m(l),b=p(x),L=await f.getDimensions(h),T="y"===x,R=T?"clientHeight":"clientWidth",E=c.reference[b]+c.reference[x]-y[x]-c.floating[b],k=y[x]-c.reference[x],C=await (null==f.getOffsetParent?void 0:f.getOffsetParent(h)),A=C?C[R]:0;A&&await (null==f.isElement?void 0:f.isElement(C))||(A=u.floating[R]||c.floating[b]);let O=A/2-L[b]/2-1,P=i(v[T?"top":"left"],O),q=i(v[T?"bottom":"right"],O),D=A-L[b]-q,F=A/2-L[b]/2+(E/2-k/2),S=o(P,i(F,D)),z=!a.arrow&&null!=d(l)&&F!==S&&c.reference[b]/2-(F<P?P:q)-L[b]/2<0,H=z?F<P?F-P:F-D:0;return{[x]:y[x]+H,data:{[x]:S,centerOffset:F-S-H,...z&&{alignmentOffset:H}},reset:z}}}),$=(t,e,n)=>{let i=new Map,o={platform:V,...n},r={...o.platform,_c:i};return b(t,e,{...o,platform:r})}},84974:(t,e,n)=>{function i(){return"undefined"!=typeof window}function o(t){return c(t)?(t.nodeName||"").toLowerCase():"#document"}function r(t){var e;return(null==t||null==(e=t.ownerDocument)?void 0:e.defaultView)||window}function l(t){var e;return null==(e=(c(t)?t.ownerDocument:t.document)||window.document)?void 0:e.documentElement}function c(t){return!!i()&&(t instanceof Node||t instanceof r(t).Node)}function f(t){return!!i()&&(t instanceof Element||t instanceof r(t).Element)}function u(t){return!!i()&&(t instanceof HTMLElement||t instanceof r(t).HTMLElement)}function s(t){return!!i()&&"undefined"!=typeof ShadowRoot&&(t instanceof ShadowRoot||t instanceof r(t).ShadowRoot)}function a(t){let{overflow:e,overflowX:n,overflowY:i,display:o}=w(t);return/auto|scroll|overlay|hidden|clip/.test(e+i+n)&&!["inline","contents"].includes(o)}function d(t){return["table","td","th"].includes(o(t))}function p(t){return[":popover-open",":modal"].some(e=>{try{return t.matches(e)}catch(t){return!1}})}function h(t){let e=g(),n=f(t)?w(t):t;return["transform","translate","scale","rotate","perspective"].some(t=>!!n[t]&&"none"!==n[t])||!!n.containerType&&"normal"!==n.containerType||!e&&!!n.backdropFilter&&"none"!==n.backdropFilter||!e&&!!n.filter&&"none"!==n.filter||["transform","translate","scale","rotate","perspective","filter"].some(t=>(n.willChange||"").includes(t))||["paint","layout","strict","content"].some(t=>(n.contain||"").includes(t))}function m(t){let e=x(t);for(;u(e)&&!v(e);){if(h(e))return e;if(p(e))break;e=x(e)}return null}function g(){return"undefined"!=typeof CSS&&!!CSS.supports&&CSS.supports("-webkit-backdrop-filter","none")}function v(t){return["html","body","#document"].includes(o(t))}function w(t){return r(t).getComputedStyle(t)}function y(t){return f(t)?{scrollLeft:t.scrollLeft,scrollTop:t.scrollTop}:{scrollLeft:t.scrollX,scrollTop:t.scrollY}}function x(t){if("html"===o(t))return t;let e=t.assignedSlot||t.parentNode||s(t)&&t.host||l(t);return s(e)?e.host:e}function b(t){return t.parent&&Object.getPrototypeOf(t.parent)?t.frameElement:null}n.d(e,{$4:()=>x,CP:()=>y,L9:()=>w,Lv:()=>d,Tc:()=>g,Tf:()=>p,ZU:()=>a,_m:()=>b,ep:()=>l,eu:()=>v,gJ:()=>m,mq:()=>o,sQ:()=>h,sb:()=>u,v9:()=>function t(e,n,i){var o;void 0===n&&(n=[]),void 0===i&&(i=!0);let l=function t(e){let n=x(e);return v(n)?e.ownerDocument?e.ownerDocument.body:e.body:u(n)&&a(n)?n:t(n)}(e),c=l===(null==(o=e.ownerDocument)?void 0:o.body),f=r(l);if(c){let e=b(f);return n.concat(f,f.visualViewport||[],a(l)?l:[],e&&i?t(e):[])}return n.concat(l,t(l,[],i))},vq:()=>f,zk:()=>r})}}]);
//# sourceMappingURL=698-9a5d88b4e290cd0b.js.map