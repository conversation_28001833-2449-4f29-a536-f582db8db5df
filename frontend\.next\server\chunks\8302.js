"use strict";exports.id=8302,exports.ids=[8302],exports.modules={33912:(e,a,t)=>{t.a(e,async(e,o)=>{try{t.r(a),t.d(a,{default:()=>m});var n=t(8732),r=t(54131),s=t(82053),c=t(93024),i=t(38058),l=t(88751),d=t(82015),u=e([r,i]);[r,i]=u.then?(await u)():u;let m=e=>{let{t:a}=(0,l.useTranslation)("common"),[t,o]=(0,d.useState)(!1);return(0,n.jsx)(n.Fragment,{children:(0,n.jsxs)(c.A.Item,{eventKey:"0",children:[(0,n.jsxs)(c<PERSON><PERSON><PERSON>,{onClick:()=>o(!t),children:[(0,n.jsx)("div",{className:"cardTitle",children:a("LinkedVirtualSpace")}),(0,n.jsx)("div",{className:"cardArrow",children:t?(0,n.jsx)(s.FontAwesomeIcon,{icon:r.faMinus,color:"#fff"}):(0,n.jsx)(s.FontAwesomeIcon,{icon:r.faPlus,color:"#fff"})})]}),(0,n.jsx)(c.A.Body,{children:(0,n.jsx)(i.A,{id:e.routeData?.routes?.[1]||"",type:"Project",vspaceData:[],vspaceDataLoading:!1,vspaceDataTotalRows:0,vspaceDataPerPage:10,vspaceDataCurrentPage:1})})]})})};o()}catch(e){o(e)}})},38058:(e,a,t)=>{t.a(e,async(e,o)=>{try{t.d(a,{A:()=>m});var n=t(8732),r=t(82015),s=t(19918),c=t.n(s),i=t(56084),l=t(63487),d=t(88751),u=e([l]);l=(u.then?(await u)():u)[0];let m=e=>{let{t:a}=(0,d.useTranslation)("common"),{type:t,id:o}=e,[s,u]=(0,r.useState)([]),[m,p]=(0,r.useState)(!1),[A,h]=(0,r.useState)(0),[f,x]=(0,r.useState)(10),[g]=(0,r.useState)(!1),j={sort:{created_at:"asc"},limit:f,page:1,query:{}},P=[{name:a("Title"),selector:"title",cell:e=>e&&e.title&&e._id?(0,n.jsx)(c(),{href:"/vspace/[...routes]",as:`/vspace/show/${e._id}`,children:e.title}):""},{name:a("Owner"),selector:"users",cell:e=>e&&e.user&&e.user.firstname?`${e.user.firstname} ${e.user.lastname}`:""},{name:a("PublicPrivate"),selector:"visibility",cell:e=>e&&e.visibility?"Public":"Private"},{name:a("NumberofMembers"),selector:"members",cell:e=>e&&e.members?e.members.length:"-"}],w=async e=>{p(!0);let a=await l.A.get(`stats/get${t}WithVspace/${o}`,j);a&&("Operation"===t?u(a.operation):u(a.project),h(a.totalCount),p(!1))},v=async(e,a)=>{j.limit=e,j.page=a,p(!0);let n=await l.A.get(`stats/get${t}WithVspace/${o}`,j);n&&("Operation"===t?u(n.operation):u(n.project),x(e),p(!1))};return(0,r.useEffect)(()=>{w(j)},[]),(0,n.jsx)("div",{children:(0,n.jsx)(i.A,{columns:P,data:s,totalRows:A,loading:m,resetPaginationToggle:g,handlePerRowsChange:v,handlePageChange:e=>{j.limit=f,j.page=e,w(j)}})})};o()}catch(e){o(e)}})},43388:(e,a,t)=>{t.a(e,async(e,o)=>{try{t.r(a),t.d(a,{default:()=>f});var n=t(8732),r=t(93024),s=t(94846),c=t(54131),i=t(82053),l=t(82015),d=t(82491),u=t(88751),m=t(93268),p=t(33912),A=t(53873),h=e([s,c,d,p,A]);[s,c,d,p,A]=h.then?(await h)():h;let f=e=>{let{t:a}=(0,u.useTranslation)("common"),[t,o]=(0,l.useState)(!1),h=()=>(0,n.jsxs)(r.A.Item,{eventKey:"1",children:[(0,n.jsxs)(r.A.Header,{onClick:()=>o(!t),children:[(0,n.jsx)("div",{className:"cardTitle",children:a("discussions")}),(0,n.jsx)("div",{className:"cardArrow",children:t?(0,n.jsx)(i.FontAwesomeIcon,{icon:c.faMinus,color:"#fff"}):(0,n.jsx)(i.FontAwesomeIcon,{icon:c.faPlus,color:"#fff"})})]}),(0,n.jsx)(r.A.Body,{children:(0,n.jsx)(d.A,{type:"hazard",id:e?.routeData&&e?.routeData?.routes?e.routeData.routes[1]:null})})]}),f=(0,m.default)(()=>(0,n.jsx)(h,{}));return(0,n.jsxs)(n.Fragment,{children:[(0,n.jsx)(r.A,{className:"countryAccordionNew",children:(0,n.jsx)(A.default,{...e})}),(0,n.jsx)(r.A,{className:"countryAccordionNew",children:(0,n.jsx)(s.default,{...e})}),(0,n.jsx)(r.A,{className:"countryAccordionNew",children:(0,n.jsx)(f,{})}),(0,n.jsx)(r.A,{className:"countryAccordion",children:(0,n.jsx)(p.default,{loading:e.documentAccoirdianProps.loading,Document:e.documentAccoirdianProps.Document,updateDocument:e.documentAccoirdianProps.updateDocument,hazardDocSort:e.documentAccoirdianProps.hazardDocSort,hazardDocUpdateSort:e.documentAccoirdianProps.hazardDocUpdateSort,docSrc:e.documentAccoirdianProps.docSrc,routeData:e.routeData})})]})};o()}catch(e){o(e)}})},53873:(e,a,t)=>{t.a(e,async(e,o)=>{try{t.r(a),t.d(a,{default:()=>m});var n=t(8732),r=t(54131),s=t(82053),c=t(93024),i=t(82015),l=t(42447),d=t(88751),u=e([r,l]);[r,l]=u.then?(await u)():u;let m=e=>{let{t:a}=(0,d.useTranslation)("common"),[t,o]=(0,i.useState)(!1);return(0,n.jsx)(n.Fragment,{children:(0,n.jsxs)(c.A.Item,{eventKey:"0",children:[(0,n.jsxs)(c.A.Header,{onClick:()=>o(!t),children:[(0,n.jsx)("div",{className:"cardTitle",children:a("mediaGallery")}),(0,n.jsx)("div",{className:"cardArrow",children:t?(0,n.jsx)(s.FontAwesomeIcon,{icon:r.faMinus,color:"#fff"}):(0,n.jsx)(s.FontAwesomeIcon,{icon:r.faPlus,color:"#fff"})})]}),(0,n.jsx)(c.A.Body,{children:(0,n.jsx)(l.A,{gallery:e.images,imageSource:e.imgSrc})})]})})};o()}catch(e){o(e)}})},56084:(e,a,t)=>{t.d(a,{A:()=>l});var o=t(8732);t(82015);var n=t(38609),r=t.n(n),s=t(88751),c=t(30370);function i(e){let{t:a}=(0,s.useTranslation)("common"),t={rowsPerPageText:a("Rowsperpage")},{columns:n,data:i,totalRows:l,resetPaginationToggle:d,subheader:u,subHeaderComponent:m,handlePerRowsChange:p,handlePageChange:A,rowsPerPage:h,defaultRowsPerPage:f,selectableRows:x,loading:g,pagServer:j,onSelectedRowsChange:P,clearSelectedRows:w,sortServer:v,onSort:y,persistTableHead:D,sortFunction:S,...N}=e,b={paginationComponentOptions:t,noDataComponent:a("NoData"),noHeader:!0,columns:n,data:i||[],dense:!0,paginationResetDefaultPage:d,subHeader:u,progressPending:g,subHeaderComponent:m,pagination:!0,paginationServer:j,paginationPerPage:f||10,paginationRowsPerPageOptions:h||[10,15,20,25,30],paginationTotalRows:l,onChangeRowsPerPage:p,onChangePage:A,selectableRows:x,onSelectedRowsChange:P,clearSelectedRows:w,progressComponent:(0,o.jsx)(c.A,{}),sortIcon:(0,o.jsx)("i",{className:"sort-icon fas fa-exchange-alt"}),sortServer:v,onSort:y,sortFunction:S,persistTableHead:D,className:"rki-table"};return(0,o.jsx)(r(),{...b})}i.defaultProps={subHeader:!1,pagination:!0,totalRows:null,pagServer:!0,onSelectedRowsChange:null,clearSelectedRows:!1,sortServer:!1,persistTableHead:!1};let l=i},80237:(e,a)=>{Object.defineProperty(a,"A",{enumerable:!0,get:function(){return t}});var t=function(e){return e.PAGES="PAGES",e.PAGES_API="PAGES_API",e.APP_PAGE="APP_PAGE",e.APP_ROUTE="APP_ROUTE",e.IMAGE="IMAGE",e}({})},81413:(e,a)=>{Object.defineProperty(a,"M",{enumerable:!0,get:function(){return function e(a,t){return t in a?a[t]:"then"in a&&"function"==typeof a.then?a.then(a=>e(a,t)):"function"==typeof a&&"default"===t?a:void 0}}})},93268:(e,a,t)=>{t.r(a),t.d(a,{canViewDiscussionUpdate:()=>n,default:()=>r});var o=t(81366);let n=t.n(o)()({authenticatedSelector:e=>!!e.permissions&&!!e.permissions.update&&!!e.permissions.update["read:any"],wrapperDisplayName:"CanViewDiscussionUpdate"}),r=n},94846:(e,a,t)=>{t.a(e,async(e,o)=>{try{t.r(a),t.d(a,{default:()=>m});var n=t(8732),r=t(54131),s=t(82053),c=t(93024),i=t(82015),l=t(97377),d=t(88751),u=e([r]);r=(u.then?(await u)():u)[0];let m=e=>{let{t:a}=(0,d.useTranslation)("common"),[t,o]=(0,i.useState)(!1);return(0,n.jsx)(n.Fragment,{children:(0,n.jsxs)(c.A.Item,{eventKey:"0",children:[(0,n.jsxs)(c.A.Header,{onClick:()=>o(!t),children:[(0,n.jsx)("div",{className:"cardTitle",children:a("documents")}),(0,n.jsx)("div",{className:"cardArrow",children:t?(0,n.jsx)(s.FontAwesomeIcon,{icon:r.faMinus,color:"#fff"}):(0,n.jsx)(s.FontAwesomeIcon,{icon:r.faPlus,color:"#fff"})})]}),(0,n.jsxs)(c.A.Body,{children:[(0,n.jsx)(l.A,{loading:e.documentAccoirdianProps.loading,sortProps:e.documentAccoirdianProps.hazardDocSort,docs:e.documentAccoirdianProps.Document||[],docsDescription:e.documentAccoirdianProps.docSrc}),(0,n.jsx)("h6",{className:"mt-3",children:a("DocumentsfromUpdates")}),(0,n.jsx)(l.A,{loading:e.documentAccoirdianProps.loading,sortProps:e.documentAccoirdianProps.hazardDocUpdateSort,docs:e.documentAccoirdianProps.updateDocument||[],docsDescription:e.documentAccoirdianProps.docSrc})]})]})})};o()}catch(e){o(e)}})},97377:(e,a,t)=>{t.d(a,{A:()=>i});var o=t(8732);t(82015);var n=t(74716),r=t.n(n),s=t(56084),c=t(88751);let i=({docs:e,docsDescription:a,sortProps:t,loading:n})=>{let i=async(e,a)=>{t({columnSelector:e.selector,sortDirection:a})},{t:l}=(0,c.useTranslation)("common"),d=[{name:l("FileType"),width:"15%",selector:"extension",cell:e=>e&&e.extension&&e.extension},{name:l("FileName"),width:"25%",selector:"document_title",cell:e=>e&&e.original_name&&(0,o.jsx)("a",{href:`http://localhost:3001/api/v1/files/download/${e._id}`,target:"_blank",children:e.original_name.split(".").slice(0,-1).join(".")}),sortable:!0},{name:l("Description"),selector:"description",cell:e=>e&&e.description&&e.description},{name:l("UploadedDate"),width:"25%",selector:"doc_created_at",cell:e=>e&&e.updated_at&&r()(e.updated_at).format("MM/DD/YYYY"),sortable:!0}];return(0,o.jsx)(s.A,{columns:d,data:e,pagServer:!0,onSort:i,persistTableHead:!0,loading:n})}}};