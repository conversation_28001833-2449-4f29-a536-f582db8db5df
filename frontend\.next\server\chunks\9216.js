exports.id=9216,exports.ids=[9216],exports.modules={1311:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"getDomainLocale",{enumerable:!0,get:function(){return a}});let n=r(37779);function a(e,t,a,o){{let i=r(7711).normalizeLocalePath,l=r(13448).detectDomainLocale,s=t||i(e,a).detectedLocale,u=l(o,void 0,s);if(u){let t="http"+(u.http?"":"s")+"://",r=s===u.defaultLocale?"":"/"+s;return""+t+u.domain+(0,n.normalizePathTrailingSlash)(""+r+e)}return!1}}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},1523:(e,t,r)=>{"use strict";e.exports=r(63885).vendored.contexts.HeadManagerContext},2041:(e,t,r)=>{"use strict";r.d(t,{A:()=>o});var n=r(22326),a=r.n(n);function o(e){return e&&"setState"in e?a().findDOMNode(e):null!=e?e:null}},2118:(e,t,r)=>{"use strict";var n=r(92921);t.__esModule=!0,t.default=void 0;var a=function(e,t){if(e&&e.__esModule)return e;if(null===e||"object"!=typeof e&&"function"!=typeof e)return{default:e};var r=s(t);if(r&&r.has(e))return r.get(e);var n={__proto__:null},a=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var o in e)if("default"!==o&&Object.prototype.hasOwnProperty.call(e,o)){var i=a?Object.getOwnPropertyDescriptor(e,o):null;i&&(i.get||i.set)?Object.defineProperty(n,o,i):n[o]=e[o]}return n.default=e,r&&r.set(e,n),n}(r(82015)),o=n(r(3892)),i=r(11940),l=r(8732);function s(e){if("function"!=typeof WeakMap)return null;var t=new WeakMap,r=new WeakMap;return(s=function(e){return e?r:t})(e)}let u=a.forwardRef(({className:e,bsPrefix:t,as:r="div",...n},a)=>(t=(0,i.useBootstrapPrefix)(t,"modal-footer"),(0,l.jsx)(r,{ref:a,className:(0,o.default)(e,t),...n})));u.displayName="ModalFooter",t.default=u,e.exports=t.default},2827:(e,t,r)=>{"use strict";r.d(t,{A:()=>v,S:()=>y});var n=r(3892),a=r.n(n),o=r(82015),i=r(50009),l=r(98320),s=r.n(l),u=r(37766),c=r.n(u);r(26324);var d=r(72871),f=r(24765),p=r(44696),h=r(80739),m=r(28004),_=r(8732);function y(e,t,r){let n=e?r?"bottom-start":"bottom-end":r?"bottom-end":"bottom-start";return"up"===t?n=e?r?"top-start":"top-end":r?"top-end":"top-start":"end"===t?n=e?r?"left-end":"right-end":r?"left-start":"right-start":"start"===t?n=e?r?"right-end":"left-end":r?"right-start":"left-start":"down-centered"===t?n="bottom":"up-centered"===t&&(n="top"),n}let g=o.forwardRef(({bsPrefix:e,className:t,align:r,rootCloseEvent:n,flip:l=!0,show:u,renderOnMount:g,as:v="div",popperConfig:b,variant:x,...E},j)=>{let w=!1,R=(0,o.useContext)(p.A),P=(0,h.oU)(e,"dropdown-menu"),{align:O,drop:T,isRTL:N}=(0,o.useContext)(d.A);r=r||O;let A=(0,o.useContext)(f.A),S=[];if(r)if("object"==typeof r){let e=Object.keys(r);if(e.length){let t=e[0],n=r[t];w="start"===n,S.push(`${P}-${t}-${n}`)}}else"end"===r&&(w=!0);let C=y(w,T,N),[M,{hasShown:k,popper:I,show:D,toggle:L}]=(0,i.useDropdownMenu)({flip:l,rootCloseEvent:n,show:u,usePopper:!R&&0===S.length,offset:[0,2],popperConfig:b,placement:C});if(M.ref=c()((0,m.A)(j,"DropdownMenu"),M.ref),s()(()=>{D&&(null==I||I.update())},[D]),!k&&!g&&!A)return null;"string"!=typeof v&&(M.show=D,M.close=()=>null==L?void 0:L(!1),M.align=r);let U=E.style;return null!=I&&I.placement&&(U={...E.style,...M.style},E["x-placement"]=I.placement),(0,_.jsx)(v,{...E,...M,style:U,...(S.length||R)&&{"data-bs-popper":"static"},className:a()(t,P,D&&"show",w&&`${P}-end`,x&&`${P}-${x}`,...S)})});g.displayName="DropdownMenu";let v=g},3147:(e,t)=>{"use strict";function r(e){if("function"!=typeof WeakMap)return null;var t=new WeakMap,n=new WeakMap;return(r=function(e){return e?n:t})(e)}t._=function(e,t){if(!t&&e&&e.__esModule)return e;if(null===e||"object"!=typeof e&&"function"!=typeof e)return{default:e};var n=r(t);if(n&&n.has(e))return n.get(e);var a={__proto__:null},o=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var i in e)if("default"!==i&&Object.prototype.hasOwnProperty.call(e,i)){var l=o?Object.getOwnPropertyDescriptor(e,i):null;l&&(l.get||l.set)?Object.defineProperty(a,i,l):a[i]=e[i]}return a.default=e,n&&n.set(e,a),a}},3563:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"interpolateAs",{enumerable:!0,get:function(){return o}});let n=r(82417),a=r(3898);function o(e,t,r){let o="",i=(0,a.getRouteRegex)(e),l=i.groups,s=(t!==e?(0,n.getRouteMatcher)(i)(t):"")||r;o=e;let u=Object.keys(l);return u.every(e=>{let t=s[e]||"",{repeat:r,optional:n}=l[e],a="["+(r?"...":"")+e+"]";return n&&(a=(t?"":"/")+"["+a+"]"),r&&!Array.isArray(t)&&(t=[t]),(n||e in s)&&(o=o.replace(a,r?t.map(e=>encodeURIComponent(e)).join("/"):encodeURIComponent(t))||"/")})||(o=""),{params:u,result:o}}},3898:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{getNamedMiddlewareRegex:function(){return m},getNamedRouteRegex:function(){return h},getRouteRegex:function(){return d},parseParameter:function(){return s}});let n=r(32072),a=r(76580),o=r(58900),i=r(21730),l=/^([^[]*)\[((?:\[[^\]]*\])|[^\]]+)\](.*)$/;function s(e){let t=e.match(l);return t?u(t[2]):u(e)}function u(e){let t=e.startsWith("[")&&e.endsWith("]");t&&(e=e.slice(1,-1));let r=e.startsWith("...");return r&&(e=e.slice(3)),{key:e,repeat:r,optional:t}}function c(e,t,r){let n={},s=1,c=[];for(let d of(0,i.removeTrailingSlash)(e).slice(1).split("/")){let e=a.INTERCEPTION_ROUTE_MARKERS.find(e=>d.startsWith(e)),i=d.match(l);if(e&&i&&i[2]){let{key:t,optional:r,repeat:a}=u(i[2]);n[t]={pos:s++,repeat:a,optional:r},c.push("/"+(0,o.escapeStringRegexp)(e)+"([^/]+?)")}else if(i&&i[2]){let{key:e,repeat:t,optional:a}=u(i[2]);n[e]={pos:s++,repeat:t,optional:a},r&&i[1]&&c.push("/"+(0,o.escapeStringRegexp)(i[1]));let l=t?a?"(?:/(.+?))?":"/(.+?)":"/([^/]+?)";r&&i[1]&&(l=l.substring(1)),c.push(l)}else c.push("/"+(0,o.escapeStringRegexp)(d));t&&i&&i[3]&&c.push((0,o.escapeStringRegexp)(i[3]))}return{parameterizedRoute:c.join(""),groups:n}}function d(e,t){let{includeSuffix:r=!1,includePrefix:n=!1,excludeOptionalTrailingSlash:a=!1}=void 0===t?{}:t,{parameterizedRoute:o,groups:i}=c(e,r,n),l=o;return a||(l+="(?:/)?"),{re:RegExp("^"+l+"$"),groups:i}}function f(e){let t,{interceptionMarker:r,getSafeRouteKey:n,segment:a,routeKeys:i,keyPrefix:l,backreferenceDuplicateKeys:s}=e,{key:c,optional:d,repeat:f}=u(a),p=c.replace(/\W/g,"");l&&(p=""+l+p);let h=!1;(0===p.length||p.length>30)&&(h=!0),isNaN(parseInt(p.slice(0,1)))||(h=!0),h&&(p=n());let m=p in i;l?i[p]=""+l+c:i[p]=c;let _=r?(0,o.escapeStringRegexp)(r):"";return t=m&&s?"\\k<"+p+">":f?"(?<"+p+">.+?)":"(?<"+p+">[^/]+?)",d?"(?:/"+_+t+")?":"/"+_+t}function p(e,t,r,s,u){let c,d=(c=0,()=>{let e="",t=++c;for(;t>0;)e+=String.fromCharCode(97+(t-1)%26),t=Math.floor((t-1)/26);return e}),p={},h=[];for(let c of(0,i.removeTrailingSlash)(e).slice(1).split("/")){let e=a.INTERCEPTION_ROUTE_MARKERS.some(e=>c.startsWith(e)),i=c.match(l);if(e&&i&&i[2])h.push(f({getSafeRouteKey:d,interceptionMarker:i[1],segment:i[2],routeKeys:p,keyPrefix:t?n.NEXT_INTERCEPTION_MARKER_PREFIX:void 0,backreferenceDuplicateKeys:u}));else if(i&&i[2]){s&&i[1]&&h.push("/"+(0,o.escapeStringRegexp)(i[1]));let e=f({getSafeRouteKey:d,segment:i[2],routeKeys:p,keyPrefix:t?n.NEXT_QUERY_PARAM_PREFIX:void 0,backreferenceDuplicateKeys:u});s&&i[1]&&(e=e.substring(1)),h.push(e)}else h.push("/"+(0,o.escapeStringRegexp)(c));r&&i&&i[3]&&h.push((0,o.escapeStringRegexp)(i[3]))}return{namedParameterizedRoute:h.join(""),routeKeys:p}}function h(e,t){var r,n,a;let o=p(e,t.prefixRouteKeys,null!=(r=t.includeSuffix)&&r,null!=(n=t.includePrefix)&&n,null!=(a=t.backreferenceDuplicateKeys)&&a),i=o.namedParameterizedRoute;return t.excludeOptionalTrailingSlash||(i+="(?:/)?"),{...d(e,t),namedRegex:"^"+i+"$",routeKeys:o.routeKeys}}function m(e,t){let{parameterizedRoute:r}=c(e,!1,!1),{catchAll:n=!0}=t;if("/"===r)return{namedRegex:"^/"+(n?".*":"")+"$"};let{namedParameterizedRoute:a}=p(e,!1,!1,!1,!1);return{namedRegex:"^"+a+(n?"(?:(/.*)?)":"")+"$"}}},4828:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{createKey:function(){return X},default:function(){return z},matchesMiddleware:function(){return D}});let n=r(87020),a=r(3147),o=r(21730),i=r(99320),l=r(51484),s=a._(r(61644)),u=r(71749),c=r(92746),d=n._(r(41217)),f=r(54718),p=r(80229),h=r(65939);r(41027);let m=r(82417),_=r(3898),y=r(32848),g=r(13448),v=r(86290),b=r(91169),x=r(17980),E=r(20367),j=r(96092),w=r(20617),R=r(56231),P=r(41918),O=r(59705),T=r(92620),N=r(37145),A=r(60651);r(97825);let S=r(68410),C=r(3563),M=r(62140),k=r(32072);function I(){return Object.assign(Object.defineProperty(Error("Route Cancelled"),"__NEXT_ERROR_CODE",{value:"E315",enumerable:!1,configurable:!0}),{cancelled:!0})}async function D(e){let t=await Promise.resolve(e.router.pageLoader.getMiddleware());if(!t)return!1;let{pathname:r}=(0,v.parsePath)(e.asPath),n=(0,w.hasBasePath)(r)?(0,E.removeBasePath)(r):r,a=(0,j.addBasePath)((0,b.addLocale)(n,e.locale));return t.some(e=>new RegExp(e.regexp).test(a))}function L(e){let t=(0,f.getLocationOrigin)();return e.startsWith(t)?e.substring(t.length):e}function U(e,t,r){let[n,a]=(0,R.resolveHref)(e,t,!0),o=(0,f.getLocationOrigin)(),i=n.startsWith(o),l=a&&a.startsWith(o);n=L(n),a=a?L(a):a;let s=i?n:(0,j.addBasePath)(n),u=r?L((0,R.resolveHref)(e,r)):a||n;return{url:s,as:l?u:(0,j.addBasePath)(u)}}function B(e,t){let r=(0,o.removeTrailingSlash)((0,u.denormalizePagePath)(e));return"/404"===r||"/_error"===r?e:(t.includes(r)||t.some(t=>{if((0,p.isDynamicRoute)(t)&&(0,_.getRouteRegex)(t).re.test(r))return e=t,!0}),(0,o.removeTrailingSlash)(e))}async function F(e){if(!await D(e)||!e.fetchData)return null;let t=await e.fetchData(),r=await function(e,t,r){let n={basePath:r.router.basePath,i18n:{locales:r.router.locales},trailingSlash:!1},a=t.headers.get("x-nextjs-rewrite"),l=a||t.headers.get("x-nextjs-matched-path"),s=t.headers.get(k.MATCHED_PATH_HEADER);if(!s||l||s.includes("__next_data_catchall")||s.includes("/_error")||s.includes("/404")||(l=s),l){if(l.startsWith("/")){let t=(0,h.parseRelativeUrl)(l),s=(0,O.getNextPathnameInfo)(t.pathname,{nextConfig:n,parseData:!0}),u=(0,o.removeTrailingSlash)(s.pathname);return Promise.all([r.router.pageLoader.getPageList(),(0,i.getClientBuildManifest)()]).then(o=>{let[i,{__rewrites:l}]=o,d=(0,b.addLocale)(s.pathname,s.locale);if((0,p.isDynamicRoute)(d)||!a&&i.includes((0,c.normalizeLocalePath)((0,E.removeBasePath)(d),r.router.locales).pathname)){let r=(0,O.getNextPathnameInfo)((0,h.parseRelativeUrl)(e).pathname,{nextConfig:n,parseData:!0});t.pathname=d=(0,j.addBasePath)(r.pathname)}if(!i.includes(u)){let e=B(u,i);e!==u&&(u=e)}let f=i.includes(u)?u:B((0,c.normalizeLocalePath)((0,E.removeBasePath)(t.pathname),r.router.locales).pathname,i);if((0,p.isDynamicRoute)(f)){let e=(0,m.getRouteMatcher)((0,_.getRouteRegex)(f))(d);Object.assign(t.query,e||{})}return{type:"rewrite",parsedAs:t,resolvedHref:f}})}let t=(0,v.parsePath)(e);return Promise.resolve({type:"redirect-external",destination:""+(0,T.formatNextPathnameInfo)({...(0,O.getNextPathnameInfo)(t.pathname,{nextConfig:n,parseData:!0}),defaultLocale:r.router.defaultLocale,buildId:""})+t.query+t.hash})}let u=t.headers.get("x-nextjs-redirect");if(u){if(u.startsWith("/")){let e=(0,v.parsePath)(u),t=(0,T.formatNextPathnameInfo)({...(0,O.getNextPathnameInfo)(e.pathname,{nextConfig:n,parseData:!0}),defaultLocale:r.router.defaultLocale,buildId:""});return Promise.resolve({type:"redirect-internal",newAs:""+t+e.query+e.hash,newUrl:""+t+e.query+e.hash})}return Promise.resolve({type:"redirect-external",destination:u})}return Promise.resolve({type:"next"})}(t.dataHref,t.response,e);return{dataHref:t.dataHref,json:t.json,response:t.response,text:t.text,cacheKey:t.cacheKey,effect:r}}let $=Symbol("SSG_DATA_NOT_FOUND");function H(e){try{return JSON.parse(e)}catch(e){return null}}function W(e){let{dataHref:t,inflightCache:r,isPrefetch:n,hasMiddleware:a,isServerRender:o,parseJSON:l,persistCache:s,isBackground:u,unstable_skipClientCache:c}=e,{href:d}=new URL(t,window.location.href),f=e=>{var u;return(function e(t,r,n){return fetch(t,{credentials:"same-origin",method:n.method||"GET",headers:Object.assign({},n.headers,{"x-nextjs-data":"1"})}).then(a=>!a.ok&&r>1&&a.status>=500?e(t,r-1,n):a)})(t,o?3:1,{headers:Object.assign({},n?{purpose:"prefetch"}:{},n&&a?{"x-middleware-prefetch":"1"}:{},{}),method:null!=(u=null==e?void 0:e.method)?u:"GET"}).then(r=>r.ok&&(null==e?void 0:e.method)==="HEAD"?{dataHref:t,response:r,text:"",json:{},cacheKey:d}:r.text().then(e=>{if(!r.ok){if(a&&[301,302,307,308].includes(r.status))return{dataHref:t,response:r,text:e,json:{},cacheKey:d};if(404===r.status){var n;if(null==(n=H(e))?void 0:n.notFound)return{dataHref:t,json:{notFound:$},response:r,text:e,cacheKey:d}}let l=Object.defineProperty(Error("Failed to load static props"),"__NEXT_ERROR_CODE",{value:"E124",enumerable:!1,configurable:!0});throw o||(0,i.markAssetError)(l),l}return{dataHref:t,json:l?H(e):null,response:r,text:e,cacheKey:d}})).then(e=>(s&&"no-cache"!==e.response.headers.get("x-middleware-cache")||delete r[d],e)).catch(e=>{throw c||delete r[d],("Failed to fetch"===e.message||"NetworkError when attempting to fetch resource."===e.message||"Load failed"===e.message)&&(0,i.markAssetError)(e),e})};return c&&s?f({}).then(e=>("no-cache"!==e.response.headers.get("x-middleware-cache")&&(r[d]=Promise.resolve(e)),e)):void 0!==r[d]?r[d]:r[d]=f(u?{method:"HEAD"}:{})}function X(){return Math.random().toString(36).slice(2,10)}function G(e){let{url:t,router:r}=e;if(t===(0,j.addBasePath)((0,b.addLocale)(r.asPath,r.locale)))throw Object.defineProperty(Error("Invariant: attempted to hard navigate to the same URL "+t+" "+location.href),"__NEXT_ERROR_CODE",{value:"E282",enumerable:!1,configurable:!0});window.location.href=t}let q=e=>{let{route:t,router:r}=e,n=!1,a=r.clc=()=>{n=!0};return()=>{if(n){let e=Object.defineProperty(Error('Abort fetching component for route: "'+t+'"'),"__NEXT_ERROR_CODE",{value:"E483",enumerable:!1,configurable:!0});throw e.cancelled=!0,e}a===r.clc&&(r.clc=null)}};class z{reload(){window.location.reload()}back(){window.history.back()}forward(){window.history.forward()}push(e,t,r){return void 0===r&&(r={}),{url:e,as:t}=U(this,e,t),this.change("pushState",e,t,r)}replace(e,t,r){return void 0===r&&(r={}),{url:e,as:t}=U(this,e,t),this.change("replaceState",e,t,r)}async _bfl(e,t,n,a){{if(!this._bfl_s&&!this._bfl_d){let t,o,{BloomFilter:l}=r(61219);try{({__routerFilterStatic:t,__routerFilterDynamic:o}=await (0,i.getClientBuildManifest)())}catch(t){if(console.error(t),a)return!0;return G({url:(0,j.addBasePath)((0,b.addLocale)(e,n||this.locale,this.defaultLocale)),router:this}),new Promise(()=>{})}(null==t?void 0:t.numHashes)&&(this._bfl_s=new l(t.numItems,t.errorRate),this._bfl_s.import(t)),(null==o?void 0:o.numHashes)&&(this._bfl_d=new l(o.numItems,o.errorRate),this._bfl_d.import(o))}let c=!1,d=!1;for(let{as:r,allowMatchCurrent:i}of[{as:e},{as:t}])if(r){let t=(0,o.removeTrailingSlash)(new URL(r,"http://n").pathname),f=(0,j.addBasePath)((0,b.addLocale)(t,n||this.locale));if(i||t!==(0,o.removeTrailingSlash)(new URL(this.asPath,"http://n").pathname)){var l,s,u;for(let e of(c=c||!!(null==(l=this._bfl_s)?void 0:l.contains(t))||!!(null==(s=this._bfl_s)?void 0:s.contains(f)),[t,f])){let t=e.split("/");for(let e=0;!d&&e<t.length+1;e++){let r=t.slice(0,e).join("/");if(r&&(null==(u=this._bfl_d)?void 0:u.contains(r))){d=!0;break}}}if(c||d){if(a)return!0;return G({url:(0,j.addBasePath)((0,b.addLocale)(e,n||this.locale,this.defaultLocale)),router:this}),new Promise(()=>{})}}}}return!1}async change(e,t,r,n,a){var u,d,R,P,O,T,M,k,L,F;let H,W;if(!(0,A.isLocalURL)(t))return G({url:t,router:this}),!1;let X=1===n._h;X||n.shallow||await this._bfl(r,void 0,n.locale);let q=X||n._shouldResolveHref||(0,v.parsePath)(t).pathname===(0,v.parsePath)(r).pathname,V={...this.state},K=!0!==this.isReady;this.isReady=!0;let Y=this.isSsr;if(X||(this.isSsr=!1),X&&this.clc)return!1;let Q=V.locale;{V.locale=!1===n.locale?this.defaultLocale:n.locale||V.locale,void 0===n.locale&&(n.locale=V.locale);let e=(0,h.parseRelativeUrl)((0,w.hasBasePath)(r)?(0,E.removeBasePath)(r):r),a=(0,c.normalizeLocalePath)(e.pathname,this.locales);a.detectedLocale&&(V.locale=a.detectedLocale,e.pathname=(0,j.addBasePath)(e.pathname),r=(0,y.formatWithValidation)(e),t=(0,j.addBasePath)((0,c.normalizeLocalePath)((0,w.hasBasePath)(t)?(0,E.removeBasePath)(t):t,this.locales).pathname));let o=!1;(null==(d=this.locales)?void 0:d.includes(V.locale))||(e.pathname=(0,b.addLocale)(e.pathname,V.locale),G({url:(0,y.formatWithValidation)(e),router:this}),o=!0);let i=(0,g.detectDomainLocale)(this.domainLocales,void 0,V.locale);if(!o&&i&&this.isLocaleDomain&&self.location.hostname!==i.domain){let e=(0,E.removeBasePath)(r);G({url:"http"+(i.http?"":"s")+"://"+i.domain+(0,j.addBasePath)((V.locale===i.defaultLocale?"":"/"+V.locale)+("/"===e?"":e)||"/"),router:this}),o=!0}if(o)return new Promise(()=>{})}f.ST&&performance.mark("routeChange");let{shallow:J=!1,scroll:Z=!0}=n,ee={shallow:J};this._inFlightRoute&&this.clc&&(Y||z.events.emit("routeChangeError",I(),this._inFlightRoute,ee),this.clc(),this.clc=null),r=(0,j.addBasePath)((0,b.addLocale)((0,w.hasBasePath)(r)?(0,E.removeBasePath)(r):r,n.locale,this.defaultLocale));let et=(0,x.removeLocale)((0,w.hasBasePath)(r)?(0,E.removeBasePath)(r):r,V.locale);this._inFlightRoute=r;let er=Q!==V.locale;if(!X&&this.onlyAHashChange(et)&&!er){V.asPath=et,z.events.emit("hashChangeStart",r,ee),this.changeState(e,t,r,{...n,scroll:!1}),Z&&this.scrollToHash(et);try{await this.set(V,this.components[V.route],null)}catch(e){throw(0,s.default)(e)&&e.cancelled&&z.events.emit("routeChangeError",e,et,ee),e}return z.events.emit("hashChangeComplete",r,ee),!0}let en=(0,h.parseRelativeUrl)(t),{pathname:ea,query:eo}=en;try{[H,{__rewrites:W}]=await Promise.all([this.pageLoader.getPageList(),(0,i.getClientBuildManifest)(),this.pageLoader.getMiddleware()])}catch(e){return G({url:r,router:this}),!1}this.urlIsNew(et)||er||(e="replaceState");let ei=r;ea=ea?(0,o.removeTrailingSlash)((0,E.removeBasePath)(ea)):ea;let el=(0,o.removeTrailingSlash)(ea),es=r.startsWith("/")&&(0,h.parseRelativeUrl)(r).pathname;if(null==(u=this.components[ea])?void 0:u.__appRouter)return G({url:r,router:this}),new Promise(()=>{});let eu=!!(es&&el!==es&&(!(0,p.isDynamicRoute)(el)||!(0,m.getRouteMatcher)((0,_.getRouteRegex)(el))(es))),ec=!n.shallow&&await D({asPath:r,locale:V.locale,router:this});if(X&&ec&&(q=!1),q&&"/_error"!==ea&&(n._shouldResolveHref=!0,en.pathname=B(ea,H),en.pathname!==ea&&(ea=en.pathname,en.pathname=(0,j.addBasePath)(ea),ec||(t=(0,y.formatWithValidation)(en)))),!(0,A.isLocalURL)(r))return G({url:r,router:this}),!1;ei=(0,x.removeLocale)((0,E.removeBasePath)(ei),V.locale),el=(0,o.removeTrailingSlash)(ea);let ed=!1;if((0,p.isDynamicRoute)(el)){let e=(0,h.parseRelativeUrl)(ei),n=e.pathname,a=(0,_.getRouteRegex)(el);ed=(0,m.getRouteMatcher)(a)(n);let o=el===n,i=o?(0,C.interpolateAs)(el,n,eo):{};if(ed&&(!o||i.result))o?r=(0,y.formatWithValidation)(Object.assign({},e,{pathname:i.result,query:(0,S.omit)(eo,i.params)})):Object.assign(eo,ed);else{let e=Object.keys(a.groups).filter(e=>!eo[e]&&!a.groups[e].optional);if(e.length>0&&!ec)throw Object.defineProperty(Error((o?"The provided `href` ("+t+") value is missing query values ("+e.join(", ")+") to be interpolated properly. ":"The provided `as` value ("+n+") is incompatible with the `href` value ("+el+"). ")+"Read more: https://nextjs.org/docs/messages/"+(o?"href-interpolation-failed":"incompatible-href-as")),"__NEXT_ERROR_CODE",{value:"E344",enumerable:!1,configurable:!0})}}X||z.events.emit("routeChangeStart",r,ee);let ef="/404"===this.pathname||"/_error"===this.pathname;try{let o=await this.getRouteInfo({route:el,pathname:ea,query:eo,as:r,resolvedAs:ei,routeProps:ee,locale:V.locale,isPreview:V.isPreview,hasMiddleware:ec,unstable_skipClientCache:n.unstable_skipClientCache,isQueryUpdating:X&&!this.isFallback,isMiddlewareRewrite:eu});if(X||n.shallow||await this._bfl(r,"resolvedAs"in o?o.resolvedAs:void 0,V.locale),"route"in o&&ec){el=ea=o.route||el,ee.shallow||(eo=Object.assign({},o.query||{},eo));let e=(0,w.hasBasePath)(en.pathname)?(0,E.removeBasePath)(en.pathname):en.pathname;if(ed&&ea!==e&&Object.keys(ed).forEach(e=>{ed&&eo[e]===ed[e]&&delete eo[e]}),(0,p.isDynamicRoute)(ea)){let e=!ee.shallow&&o.resolvedAs?o.resolvedAs:(0,j.addBasePath)((0,b.addLocale)(new URL(r,location.href).pathname,V.locale),!0);(0,w.hasBasePath)(e)&&(e=(0,E.removeBasePath)(e));{let t=(0,c.normalizeLocalePath)(e,this.locales);V.locale=t.detectedLocale||V.locale,e=t.pathname}let t=(0,_.getRouteRegex)(ea),n=(0,m.getRouteMatcher)(t)(new URL(e,location.href).pathname);n&&Object.assign(eo,n)}}if("type"in o)if("redirect-internal"===o.type)return this.change(e,o.newUrl,o.newAs,n);else return G({url:o.destination,router:this}),new Promise(()=>{});let i=o.Component;if(i&&i.unstable_scriptLoader&&[].concat(i.unstable_scriptLoader()).forEach(e=>{(0,l.handleClientScriptLoad)(e.props)}),(o.__N_SSG||o.__N_SSP)&&o.props){if(o.props.pageProps&&o.props.pageProps.__N_REDIRECT){n.locale=!1;let t=o.props.pageProps.__N_REDIRECT;if(t.startsWith("/")&&!1!==o.props.pageProps.__N_REDIRECT_BASE_PATH){let r=(0,h.parseRelativeUrl)(t);r.pathname=B(r.pathname,H);let{url:a,as:o}=U(this,t,t);return this.change(e,a,o,n)}return G({url:t,router:this}),new Promise(()=>{})}if(V.isPreview=!!o.props.__N_PREVIEW,o.props.notFound===$){let e;try{await this.fetchComponent("/404"),e="/404"}catch(t){e="/_error"}if(o=await this.getRouteInfo({route:e,pathname:e,query:eo,as:r,resolvedAs:ei,routeProps:{shallow:!1},locale:V.locale,isPreview:V.isPreview,isNotFound:!0}),"type"in o)throw Object.defineProperty(Error("Unexpected middleware effect on /404"),"__NEXT_ERROR_CODE",{value:"E158",enumerable:!1,configurable:!0})}}X&&"/_error"===this.pathname&&(null==(P=self.__NEXT_DATA__.props)||null==(R=P.pageProps)?void 0:R.statusCode)===500&&(null==(O=o.props)?void 0:O.pageProps)&&(o.props.pageProps.statusCode=500);let u=n.shallow&&V.route===(null!=(T=o.route)?T:el),d=null!=(M=n.scroll)?M:!X&&!u,f=null!=a?a:d?{x:0,y:0}:null,y={...V,route:el,pathname:ea,query:eo,asPath:et,isFallback:!1};if(X&&ef){if(o=await this.getRouteInfo({route:this.pathname,pathname:this.pathname,query:eo,as:r,resolvedAs:ei,routeProps:{shallow:!1},locale:V.locale,isPreview:V.isPreview,isQueryUpdating:X&&!this.isFallback}),"type"in o)throw Object.defineProperty(Error("Unexpected middleware effect on "+this.pathname),"__NEXT_ERROR_CODE",{value:"E225",enumerable:!1,configurable:!0});"/_error"===this.pathname&&(null==(L=self.__NEXT_DATA__.props)||null==(k=L.pageProps)?void 0:k.statusCode)===500&&(null==(F=o.props)?void 0:F.pageProps)&&(o.props.pageProps.statusCode=500);try{await this.set(y,o,f)}catch(e){throw(0,s.default)(e)&&e.cancelled&&z.events.emit("routeChangeError",e,et,ee),e}return!0}if(z.events.emit("beforeHistoryChange",r,ee),this.changeState(e,t,r,n),!(X&&!f&&!K&&!er&&(0,N.compareRouterStates)(y,this.state))){try{await this.set(y,o,f)}catch(e){if(e.cancelled)o.error=o.error||e;else throw e}if(o.error)throw X||z.events.emit("routeChangeError",o.error,et,ee),o.error;V.locale&&(document.documentElement.lang=V.locale),X||z.events.emit("routeChangeComplete",r,ee),d&&/#.+$/.test(r)&&this.scrollToHash(r)}return!0}catch(e){if((0,s.default)(e)&&e.cancelled)return!1;throw e}}changeState(e,t,r,n){void 0===n&&(n={}),("pushState"!==e||(0,f.getURL)()!==r)&&(this._shallow=n.shallow,window.history[e]({url:t,as:r,options:n,__N:!0,key:this._key="pushState"!==e?this._key:X()},"",r))}async handleRouteInfoError(e,t,r,n,a,o){if(e.cancelled)throw e;if((0,i.isAssetError)(e)||o)throw z.events.emit("routeChangeError",e,n,a),G({url:n,router:this}),I();console.error(e);try{let n,{page:a,styleSheets:o}=await this.fetchComponent("/_error"),i={props:n,Component:a,styleSheets:o,err:e,error:e};if(!i.props)try{i.props=await this.getInitialProps(a,{err:e,pathname:t,query:r})}catch(e){console.error("Error in error page `getInitialProps`: ",e),i.props={}}return i}catch(e){return this.handleRouteInfoError((0,s.default)(e)?e:Object.defineProperty(Error(e+""),"__NEXT_ERROR_CODE",{value:"E394",enumerable:!1,configurable:!0}),t,r,n,a,!0)}}async getRouteInfo(e){let{route:t,pathname:r,query:n,as:a,resolvedAs:i,routeProps:l,locale:u,hasMiddleware:d,isPreview:f,unstable_skipClientCache:p,isQueryUpdating:h,isMiddlewareRewrite:m,isNotFound:_}=e,g=t;try{var v,b,x,j;let e=this.components[g];if(l.shallow&&e&&this.route===g)return e;let t=q({route:g,router:this});d&&(e=void 0);let s=!e||"initial"in e?void 0:e,w={dataHref:this.pageLoader.getDataHref({href:(0,y.formatWithValidation)({pathname:r,query:n}),skipInterpolation:!0,asPath:_?"/404":i,locale:u}),hasMiddleware:!0,isServerRender:this.isSsr,parseJSON:!0,inflightCache:h?this.sbc:this.sdc,persistCache:!f,isPrefetch:!1,unstable_skipClientCache:p,isBackground:h},R=h&&!m?null:await F({fetchData:()=>W(w),asPath:_?"/404":i,locale:u,router:this}).catch(e=>{if(h)return null;throw e});if(R&&("/_error"===r||"/404"===r)&&(R.effect=void 0),h&&(R?R.json=self.__NEXT_DATA__.props:R={json:self.__NEXT_DATA__.props}),t(),(null==R||null==(v=R.effect)?void 0:v.type)==="redirect-internal"||(null==R||null==(b=R.effect)?void 0:b.type)==="redirect-external")return R.effect;if((null==R||null==(x=R.effect)?void 0:x.type)==="rewrite"){let t=(0,o.removeTrailingSlash)(R.effect.resolvedHref),a=await this.pageLoader.getPageList();if((!h||a.includes(t))&&(g=t,r=R.effect.resolvedHref,n={...n,...R.effect.parsedAs.query},i=(0,E.removeBasePath)((0,c.normalizeLocalePath)(R.effect.parsedAs.pathname,this.locales).pathname),e=this.components[g],l.shallow&&e&&this.route===g&&!d))return{...e,route:g}}if((0,P.isAPIRoute)(g))return G({url:a,router:this}),new Promise(()=>{});let O=s||await this.fetchComponent(g).then(e=>({Component:e.page,styleSheets:e.styleSheets,__N_SSG:e.mod.__N_SSG,__N_SSP:e.mod.__N_SSP})),T=null==R||null==(j=R.response)?void 0:j.headers.get("x-middleware-skip"),N=O.__N_SSG||O.__N_SSP;T&&(null==R?void 0:R.dataHref)&&delete this.sdc[R.dataHref];let{props:A,cacheKey:S}=await this._getData(async()=>{if(N){if((null==R?void 0:R.json)&&!T)return{cacheKey:R.cacheKey,props:R.json};let e=(null==R?void 0:R.dataHref)?R.dataHref:this.pageLoader.getDataHref({href:(0,y.formatWithValidation)({pathname:r,query:n}),asPath:i,locale:u}),t=await W({dataHref:e,isServerRender:this.isSsr,parseJSON:!0,inflightCache:T?{}:this.sdc,persistCache:!f,isPrefetch:!1,unstable_skipClientCache:p});return{cacheKey:t.cacheKey,props:t.json||{}}}return{headers:{},props:await this.getInitialProps(O.Component,{pathname:r,query:n,asPath:a,locale:u,locales:this.locales,defaultLocale:this.defaultLocale})}});return O.__N_SSP&&w.dataHref&&S&&delete this.sdc[S],this.isPreview||!O.__N_SSG||h||W(Object.assign({},w,{isBackground:!0,persistCache:!1,inflightCache:this.sbc})).catch(()=>{}),A.pageProps=Object.assign({},A.pageProps),O.props=A,O.route=g,O.query=n,O.resolvedAs=i,this.components[g]=O,O}catch(e){return this.handleRouteInfoError((0,s.getProperError)(e),r,n,a,l)}}set(e,t,r){return this.state=e,this.sub(t,this.components["/_app"].Component,r)}beforePopState(e){this._bps=e}onlyAHashChange(e){if(!this.asPath)return!1;let[t,r]=this.asPath.split("#",2),[n,a]=e.split("#",2);return!!a&&t===n&&r===a||t===n&&r!==a}scrollToHash(e){let[,t=""]=e.split("#",2);(0,M.handleSmoothScroll)(()=>{if(""===t||"top"===t)return void window.scrollTo(0,0);let e=decodeURIComponent(t),r=document.getElementById(e);if(r)return void r.scrollIntoView();let n=document.getElementsByName(e)[0];n&&n.scrollIntoView()},{onlyHashChange:this.onlyAHashChange(e)})}urlIsNew(e){return this.asPath!==e}async prefetch(e,t,r){void 0===t&&(t=e),void 0===r&&(r={});let n=(0,h.parseRelativeUrl)(e),a=n.pathname,{pathname:i,query:l}=n,s=i;if(!1===r.locale){n.pathname=i=(0,c.normalizeLocalePath)(i,this.locales).pathname,e=(0,y.formatWithValidation)(n);let a=(0,h.parseRelativeUrl)(t),o=(0,c.normalizeLocalePath)(a.pathname,this.locales);a.pathname=o.pathname,r.locale=o.detectedLocale||this.defaultLocale,t=(0,y.formatWithValidation)(a)}let u=await this.pageLoader.getPageList(),d=t,f=void 0!==r.locale?r.locale||void 0:this.locale,g=await D({asPath:t,locale:f,router:this});n.pathname=B(n.pathname,u),(0,p.isDynamicRoute)(n.pathname)&&(i=n.pathname,n.pathname=i,Object.assign(l,(0,m.getRouteMatcher)((0,_.getRouteRegex)(n.pathname))((0,v.parsePath)(t).pathname)||{}),g||(e=(0,y.formatWithValidation)(n)));let b=await F({fetchData:()=>W({dataHref:this.pageLoader.getDataHref({href:(0,y.formatWithValidation)({pathname:s,query:l}),skipInterpolation:!0,asPath:d,locale:f}),hasMiddleware:!0,isServerRender:!1,parseJSON:!0,inflightCache:this.sdc,persistCache:!this.isPreview,isPrefetch:!0}),asPath:t,locale:f,router:this});if((null==b?void 0:b.effect.type)==="rewrite"&&(n.pathname=b.effect.resolvedHref,i=b.effect.resolvedHref,l={...l,...b.effect.parsedAs.query},d=b.effect.parsedAs.pathname,e=(0,y.formatWithValidation)(n)),(null==b?void 0:b.effect.type)==="redirect-external")return;let x=(0,o.removeTrailingSlash)(i);await this._bfl(t,d,r.locale,!0)&&(this.components[a]={__appRouter:!0}),await Promise.all([this.pageLoader._isSsg(x).then(t=>!!t&&W({dataHref:(null==b?void 0:b.json)?null==b?void 0:b.dataHref:this.pageLoader.getDataHref({href:e,asPath:d,locale:f}),isServerRender:!1,parseJSON:!0,inflightCache:this.sdc,persistCache:!this.isPreview,isPrefetch:!0,unstable_skipClientCache:r.unstable_skipClientCache||r.priority&&!0}).then(()=>!1).catch(()=>!1)),this.pageLoader[r.priority?"loadPage":"prefetch"](x)])}async fetchComponent(e){let t=q({route:e,router:this});try{let r=await this.pageLoader.loadPage(e);return t(),r}catch(e){throw t(),e}}_getData(e){let t=!1,r=()=>{t=!0};return this.clc=r,e().then(e=>{if(r===this.clc&&(this.clc=null),t){let e=Object.defineProperty(Error("Loading initial props cancelled"),"__NEXT_ERROR_CODE",{value:"E405",enumerable:!1,configurable:!0});throw e.cancelled=!0,e}return e})}getInitialProps(e,t){let{Component:r}=this.components["/_app"],n=this._wrapApp(r);return t.AppTree=n,(0,f.loadGetInitialProps)(r,{AppTree:n,Component:e,router:this,ctx:t})}get route(){return this.state.route}get pathname(){return this.state.pathname}get query(){return this.state.query}get asPath(){return this.state.asPath}get locale(){return this.state.locale}get isFallback(){return this.state.isFallback}get isPreview(){return this.state.isPreview}constructor(e,t,r,{initialProps:n,pageLoader:a,App:i,wrapApp:l,Component:s,err:u,subscription:c,isFallback:d,locale:m,locales:_,defaultLocale:v,domainLocales:b,isPreview:x}){this.sdc={},this.sbc={},this.isFirstPopStateEvent=!0,this._key=X(),this.onPopState=e=>{let t,{isFirstPopStateEvent:r}=this;this.isFirstPopStateEvent=!1;let n=e.state;if(!n){let{pathname:e,query:t}=this;this.changeState("replaceState",(0,y.formatWithValidation)({pathname:(0,j.addBasePath)(e),query:t}),(0,f.getURL)());return}if(n.__NA)return void window.location.reload();if(!n.__N||r&&this.locale===n.options.locale&&n.as===this.asPath)return;let{url:a,as:o,options:i,key:l}=n;this._key=l;let{pathname:s}=(0,h.parseRelativeUrl)(a);(!this.isSsr||o!==(0,j.addBasePath)(this.asPath)||s!==(0,j.addBasePath)(this.pathname))&&(!this._bps||this._bps(n))&&this.change("replaceState",a,o,Object.assign({},i,{shallow:i.shallow&&this._shallow,locale:i.locale||this.defaultLocale,_h:0}),t)};let E=(0,o.removeTrailingSlash)(e);this.components={},"/_error"!==e&&(this.components[E]={Component:s,initial:!0,props:n,err:u,__N_SSG:n&&n.__N_SSG,__N_SSP:n&&n.__N_SSP}),this.components["/_app"]={Component:i,styleSheets:[]},this.events=z.events,this.pageLoader=a;let w=(0,p.isDynamicRoute)(e)&&self.__NEXT_DATA__.autoExport;this.basePath="",this.sub=c,this.clc=null,this._wrapApp=l,this.isSsr=!0,this.isLocaleDomain=!1,this.isReady=!!(self.__NEXT_DATA__.gssp||self.__NEXT_DATA__.gip||self.__NEXT_DATA__.isExperimentalCompile||self.__NEXT_DATA__.appGip&&!self.__NEXT_DATA__.gsp||!w&&!self.location.search),this.locales=_,this.defaultLocale=v,this.domainLocales=b,this.isLocaleDomain=!!(0,g.detectDomainLocale)(b,self.location.hostname),this.state={route:E,pathname:e,query:t,asPath:w?e:r,isPreview:!!x,locale:m,isFallback:d},this._initialMatchesMiddlewarePromise=Promise.resolve(!1)}}z.events=(0,d.default)()},5939:(e,t,r)=>{e.exports=r(95504)},6217:(e,t,r)=>{"use strict";r.a(e,async(e,n)=>{try{r.d(t,{n:()=>u}),r(82015);var a=r(14078),o=r(12872),i=e([a]);let s=(a=(i.then?(await i)():i)[0]).default.default||a.default;async function l([e,t]){let r=await fetch(e,t),n=await r.json();if(r.ok)return n.data||null;throw new o.y({message:`Failed to fetch tweet at "${e}" with "${r.status}".`,data:n,status:r.status})}let u=(e,t,r)=>{let{isLoading:n,data:a,error:o}=s(()=>t||e?[t||e&&`https://react-tweet.vercel.app/api/tweet/${e}`,r]:null,l,{revalidateIfStale:!1,revalidateOnFocus:!1,shouldRetryOnError:!1});return{isLoading:!!(n||void 0===a&&!o),data:a,error:o}};n()}catch(e){n(e)}})},6756:(e,t,r)=>{"use strict";r.d(t,{A:()=>b});var n=r(3892),a=r.n(n),o=r(99460),i=r.n(o),l=r(82015),s=r.n(l),u=r(1919),c=r(1680),d=r(77291);let f=function(...e){return e.filter(e=>null!=e).reduce((e,t)=>{if("function"!=typeof t)throw Error("Invalid Argument Type, must only provide functions, undefined, or null.");return null===e?t:function(...r){e.apply(this,r),t.apply(this,r)}},null)};var p=r(49542),h=r(66933),m=r(8732);let _={height:["marginTop","marginBottom"],width:["marginLeft","marginRight"]};function y(e,t){let r=t[`offset${e[0].toUpperCase()}${e.slice(1)}`],n=_[e];return r+parseInt(i()(t,n[0]),10)+parseInt(i()(t,n[1]),10)}let g={[u.EXITED]:"collapse",[u.EXITING]:"collapsing",[u.ENTERING]:"collapsing",[u.ENTERED]:"collapse show"},v=s().forwardRef(({onEnter:e,onEntering:t,onEntered:r,onExit:n,onExiting:o,className:i,children:u,dimension:_="height",in:v=!1,timeout:b=300,mountOnEnter:x=!1,unmountOnExit:E=!1,appear:j=!1,getDimensionValue:w=y,...R},P)=>{let O="function"==typeof _?_():_,T=(0,l.useMemo)(()=>f(e=>{e.style[O]="0"},e),[O,e]),N=(0,l.useMemo)(()=>f(e=>{let t=`scroll${O[0].toUpperCase()}${O.slice(1)}`;e.style[O]=`${e[t]}px`},t),[O,t]),A=(0,l.useMemo)(()=>f(e=>{e.style[O]=null},r),[O,r]),S=(0,l.useMemo)(()=>f(e=>{e.style[O]=`${w(O,e)}px`,(0,p.A)(e)},n),[n,w,O]),C=(0,l.useMemo)(()=>f(e=>{e.style[O]=null},o),[O,o]);return(0,m.jsx)(h.A,{ref:P,addEndListener:d.A,...R,"aria-expanded":R.role?v:null,onEnter:T,onEntering:N,onEntered:A,onExit:S,onExiting:C,childRef:(0,c.getChildRef)(u),in:v,timeout:b,mountOnEnter:x,unmountOnExit:E,appear:j,children:(e,t)=>s().cloneElement(u,{...t,className:a()(i,u.props.className,g[e],"width"===O&&"collapse-horizontal")})})});v.displayName="Collapse";let b=v},6898:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"parseUrl",{enumerable:!0,get:function(){return o}});let n=r(87536),a=r(65939);function o(e){if(e.startsWith("/"))return(0,a.parseRelativeUrl)(e);let t=new URL(e);return{hash:t.hash,hostname:t.hostname,href:t.href,pathname:t.pathname,port:t.port,protocol:t.protocol,query:(0,n.searchParamsToUrlQuery)(t.searchParams),search:t.search}}},7082:(e,t,r)=>{"use strict";r.d(t,{A:()=>u});var n=r(3892),a=r.n(n),o=r(82015),i=r(80739),l=r(8732);let s=o.forwardRef(({bsPrefix:e,fluid:t=!1,as:r="div",className:n,...o},s)=>{let u=(0,i.oU)(e,"container"),c="string"==typeof t?`-${t}`:"-fluid";return(0,l.jsx)(r,{ref:s,...o,className:a()(n,t?`${u}${c}`:u)})});s.displayName="Container";let u=s},7408:(e,t,r)=>{"use strict";var n=r(92921);t.__esModule=!0,t.default=void 0;var a=n(r(29825)),o=function(e,t){if(e&&e.__esModule)return e;if(null===e||"object"!=typeof e&&"function"!=typeof e)return{default:e};var r=s(t);if(r&&r.has(e))return r.get(e);var n={__proto__:null},a=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var o in e)if("default"!==o&&Object.prototype.hasOwnProperty.call(e,o)){var i=a?Object.getOwnPropertyDescriptor(e,o):null;i&&(i.get||i.set)?Object.defineProperty(n,o,i):n[o]=e[o]}return n.default=e,r&&r.set(e,n),n}(r(82015)),i=n(r(3892)),l=r(8732);function s(e){if("function"!=typeof WeakMap)return null;var t=new WeakMap,r=new WeakMap;return(s=function(e){return e?r:t})(e)}let u={"aria-label":a.default.string,onClick:a.default.func,variant:a.default.oneOf(["white"])},c=o.forwardRef(({className:e,variant:t,"aria-label":r="Close",...n},a)=>(0,l.jsx)("button",{ref:a,type:"button",className:(0,i.default)("btn-close",t&&`btn-close-${t}`,e),"aria-label":r,...n}));c.displayName="CloseButton",c.propTypes=u,t.default=c,e.exports=t.default},7444:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"notFound",{enumerable:!0,get:function(){return a}});let n=""+r(82323).HTTP_ERROR_FALLBACK_ERROR_CODE+";404";function a(){let e=Object.defineProperty(Error(n),"__NEXT_ERROR_CODE",{value:"E394",enumerable:!1,configurable:!0});throw e.digest=n,e}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},7711:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"normalizeLocalePath",{enumerable:!0,get:function(){return n}});let n=(e,t)=>r(92746).normalizeLocalePath(e,t);("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},7783:(e,t,r)=>{"use strict";r.d(t,{A:()=>l});var n=r(82015),a=r(3892),o=r.n(a),i=r(8732);let l=e=>n.forwardRef((t,r)=>(0,i.jsx)("div",{...t,ref:r,className:o()(t.className,e)}))},7965:(e,t,r)=>{"use strict";r.d(t,{L:()=>i});var n=r(8732),a=r(97580),o=r(50262);let i=e=>(0,n.jsx)(a.X,{children:(0,n.jsxs)("div",{className:o.root,children:[(0,n.jsx)("h3",{children:"Tweet not found"}),(0,n.jsx)("p",{children:"The embedded tweet could not be found…"})]})})},11836:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{Postpone:function(){return w},abortAndThrowOnSynchronousRequestDataAccess:function(){return E},abortOnSynchronousPlatformIOAccess:function(){return b},accessedDynamicData:function(){return C},annotateDynamicAccess:function(){return U},consumeDynamicAccess:function(){return M},createDynamicTrackingState:function(){return f},createDynamicValidationState:function(){return p},createHangingInputAbortSignal:function(){return L},createPostponedAbortSignal:function(){return D},formatDynamicAPIAccesses:function(){return k},getFirstDynamicReason:function(){return h},isDynamicPostpone:function(){return O},isPrerenderInterruptedError:function(){return S},markCurrentScopeAsDynamic:function(){return m},postponeWithTracking:function(){return R},throwIfDisallowedDynamic:function(){return G},throwToInterruptStaticGeneration:function(){return y},trackAllowedDynamicAccess:function(){return X},trackDynamicDataInDynamicRender:function(){return g},trackFallbackParamAccessed:function(){return _},trackSynchronousPlatformIOAccessInDev:function(){return x},trackSynchronousRequestDataAccessInDev:function(){return j},useDynamicRouteParams:function(){return B}});let n=function(e){return e&&e.__esModule?e:{default:e}}(r(82015)),a=r(43210),o=r(75330),i=r(63033),l=r(29294),s=r(45061),u=r(90978),c=r(91504),d="function"==typeof n.default.unstable_postpone;function f(e){return{isDebugDynamicAccesses:e,dynamicAccesses:[],syncDynamicExpression:void 0,syncDynamicErrorWithStack:null}}function p(){return{hasSuspendedDynamic:!1,hasDynamicMetadata:!1,hasDynamicViewport:!1,hasSyncDynamicErrors:!1,dynamicErrors:[]}}function h(e){var t;return null==(t=e.dynamicAccesses[0])?void 0:t.expression}function m(e,t,r){if((!t||"cache"!==t.type&&"unstable-cache"!==t.type)&&!e.forceDynamic&&!e.forceStatic){if(e.dynamicShouldError)throw Object.defineProperty(new o.StaticGenBailoutError(`Route ${e.route} with \`dynamic = "error"\` couldn't be rendered statically because it used \`${r}\`. See more info here: https://nextjs.org/docs/app/building-your-application/rendering/static-and-dynamic#dynamic-rendering`),"__NEXT_ERROR_CODE",{value:"E553",enumerable:!1,configurable:!0});if(t){if("prerender-ppr"===t.type)R(e.route,r,t.dynamicTracking);else if("prerender-legacy"===t.type){t.revalidate=0;let n=Object.defineProperty(new a.DynamicServerError(`Route ${e.route} couldn't be rendered statically because it used ${r}. See more info here: https://nextjs.org/docs/messages/dynamic-server-error`),"__NEXT_ERROR_CODE",{value:"E550",enumerable:!1,configurable:!0});throw e.dynamicUsageDescription=r,e.dynamicUsageStack=n.stack,n}}}}function _(e,t){let r=i.workUnitAsyncStorage.getStore();r&&"prerender-ppr"===r.type&&R(e.route,t,r.dynamicTracking)}function y(e,t,r){let n=Object.defineProperty(new a.DynamicServerError(`Route ${t.route} couldn't be rendered statically because it used \`${e}\`. See more info here: https://nextjs.org/docs/messages/dynamic-server-error`),"__NEXT_ERROR_CODE",{value:"E558",enumerable:!1,configurable:!0});throw r.revalidate=0,t.dynamicUsageDescription=e,t.dynamicUsageStack=n.stack,n}function g(e,t){t&&"cache"!==t.type&&"unstable-cache"!==t.type&&("prerender"===t.type||"prerender-legacy"===t.type)&&(t.revalidate=0)}function v(e,t,r){let n=A(`Route ${e} needs to bail out of prerendering at this point because it used ${t}.`);r.controller.abort(n);let a=r.dynamicTracking;a&&a.dynamicAccesses.push({stack:a.isDebugDynamicAccesses?Error().stack:void 0,expression:t})}function b(e,t,r,n){let a=n.dynamicTracking;a&&null===a.syncDynamicErrorWithStack&&(a.syncDynamicExpression=t,a.syncDynamicErrorWithStack=r),v(e,t,n)}function x(e){e.prerenderPhase=!1}function E(e,t,r,n){if(!1===n.controller.signal.aborted){let a=n.dynamicTracking;a&&null===a.syncDynamicErrorWithStack&&(a.syncDynamicExpression=t,a.syncDynamicErrorWithStack=r,!0===n.validating&&(a.syncDynamicLogged=!0)),v(e,t,n)}throw A(`Route ${e} needs to bail out of prerendering at this point because it used ${t}.`)}let j=x;function w({reason:e,route:t}){let r=i.workUnitAsyncStorage.getStore();R(t,e,r&&"prerender-ppr"===r.type?r.dynamicTracking:null)}function R(e,t,r){I(),r&&r.dynamicAccesses.push({stack:r.isDebugDynamicAccesses?Error().stack:void 0,expression:t}),n.default.unstable_postpone(P(e,t))}function P(e,t){return`Route ${e} needs to bail out of prerendering at this point because it used ${t}. React throws this special object to indicate where. It should not be caught by your own try/catch. Learn more: https://nextjs.org/docs/messages/ppr-caught-error`}function O(e){return"object"==typeof e&&null!==e&&"string"==typeof e.message&&T(e.message)}function T(e){return e.includes("needs to bail out of prerendering at this point because it used")&&e.includes("Learn more: https://nextjs.org/docs/messages/ppr-caught-error")}if(!1===T(P("%%%","^^^")))throw Object.defineProperty(Error("Invariant: isDynamicPostpone misidentified a postpone reason. This is a bug in Next.js"),"__NEXT_ERROR_CODE",{value:"E296",enumerable:!1,configurable:!0});let N="NEXT_PRERENDER_INTERRUPTED";function A(e){let t=Object.defineProperty(Error(e),"__NEXT_ERROR_CODE",{value:"E394",enumerable:!1,configurable:!0});return t.digest=N,t}function S(e){return"object"==typeof e&&null!==e&&e.digest===N&&"name"in e&&"message"in e&&e instanceof Error}function C(e){return e.length>0}function M(e,t){return e.dynamicAccesses.push(...t.dynamicAccesses),e.dynamicAccesses}function k(e){return e.filter(e=>"string"==typeof e.stack&&e.stack.length>0).map(({expression:e,stack:t})=>(t=t.split("\n").slice(4).filter(e=>!(e.includes("node_modules/next/")||e.includes(" (<anonymous>)")||e.includes(" (node:"))).join("\n"),`Dynamic API Usage Debug - ${e}:
${t}`))}function I(){if(!d)throw Object.defineProperty(Error("Invariant: React.unstable_postpone is not defined. This suggests the wrong version of React was loaded. This is a bug in Next.js"),"__NEXT_ERROR_CODE",{value:"E224",enumerable:!1,configurable:!0})}function D(e){I();let t=new AbortController;try{n.default.unstable_postpone(e)}catch(e){t.abort(e)}return t.signal}function L(e){let t=new AbortController;return e.cacheSignal?e.cacheSignal.inputReady().then(()=>{t.abort()}):(0,c.scheduleOnNextTick)(()=>t.abort()),t.signal}function U(e,t){let r=t.dynamicTracking;r&&r.dynamicAccesses.push({stack:r.isDebugDynamicAccesses?Error().stack:void 0,expression:e})}function B(e){let t=l.workAsyncStorage.getStore();if(t&&t.isStaticGeneration&&t.fallbackRouteParams&&t.fallbackRouteParams.size>0){let r=i.workUnitAsyncStorage.getStore();r&&("prerender"===r.type?n.default.use((0,s.makeHangingPromise)(r.renderSignal,e)):"prerender-ppr"===r.type?R(t.route,e,r.dynamicTracking):"prerender-legacy"===r.type&&y(e,t,r))}}let F=/\n\s+at Suspense \(<anonymous>\)/,$=RegExp(`\\n\\s+at ${u.METADATA_BOUNDARY_NAME}[\\n\\s]`),H=RegExp(`\\n\\s+at ${u.VIEWPORT_BOUNDARY_NAME}[\\n\\s]`),W=RegExp(`\\n\\s+at ${u.OUTLET_BOUNDARY_NAME}[\\n\\s]`);function X(e,t,r,n,a){if(!W.test(t)){if($.test(t)){r.hasDynamicMetadata=!0;return}if(H.test(t)){r.hasDynamicViewport=!0;return}if(F.test(t)){r.hasSuspendedDynamic=!0;return}else if(n.syncDynamicErrorWithStack||a.syncDynamicErrorWithStack){r.hasSyncDynamicErrors=!0;return}else{let n=function(e,t){let r=Object.defineProperty(Error(e),"__NEXT_ERROR_CODE",{value:"E394",enumerable:!1,configurable:!0});return r.stack="Error: "+e+t,r}(`Route "${e}": A component accessed data, headers, params, searchParams, or a short-lived cache without a Suspense boundary nor a "use cache" above it. We don't have the exact line number added to error messages yet but you can see which component in the stack below. See more info: https://nextjs.org/docs/messages/next-prerender-missing-suspense`,t);r.dynamicErrors.push(n);return}}}function G(e,t,r,n){let a,i,l;if(r.syncDynamicErrorWithStack?(a=r.syncDynamicErrorWithStack,i=r.syncDynamicExpression,l=!0===r.syncDynamicLogged):n.syncDynamicErrorWithStack?(a=n.syncDynamicErrorWithStack,i=n.syncDynamicExpression,l=!0===n.syncDynamicLogged):(a=null,i=void 0,l=!1),t.hasSyncDynamicErrors&&a)throw l||console.error(a),new o.StaticGenBailoutError;let s=t.dynamicErrors;if(s.length){for(let e=0;e<s.length;e++)console.error(s[e]);throw new o.StaticGenBailoutError}if(!t.hasSuspendedDynamic){if(t.hasDynamicMetadata){if(a)throw console.error(a),Object.defineProperty(new o.StaticGenBailoutError(`Route "${e}" has a \`generateMetadata\` that could not finish rendering before ${i} was used. Follow the instructions in the error for this expression to resolve.`),"__NEXT_ERROR_CODE",{value:"E608",enumerable:!1,configurable:!0});throw Object.defineProperty(new o.StaticGenBailoutError(`Route "${e}" has a \`generateMetadata\` that depends on Request data (\`cookies()\`, etc...) or external data (\`fetch(...)\`, etc...) but the rest of the route was static or only used cached data (\`"use cache"\`). If you expected this route to be prerenderable update your \`generateMetadata\` to not use Request data and only use cached external data. Otherwise, add \`await connection()\` somewhere within this route to indicate explicitly it should not be prerendered.`),"__NEXT_ERROR_CODE",{value:"E534",enumerable:!1,configurable:!0})}else if(t.hasDynamicViewport){if(a)throw console.error(a),Object.defineProperty(new o.StaticGenBailoutError(`Route "${e}" has a \`generateViewport\` that could not finish rendering before ${i} was used. Follow the instructions in the error for this expression to resolve.`),"__NEXT_ERROR_CODE",{value:"E573",enumerable:!1,configurable:!0});throw Object.defineProperty(new o.StaticGenBailoutError(`Route "${e}" has a \`generateViewport\` that depends on Request data (\`cookies()\`, etc...) or external data (\`fetch(...)\`, etc...) but the rest of the route was static or only used cached data (\`"use cache"\`). If you expected this route to be prerenderable update your \`generateViewport\` to not use Request data and only use cached external data. Otherwise, add \`await connection()\` somewhere within this route to indicate explicitly it should not be prerendered.`),"__NEXT_ERROR_CODE",{value:"E590",enumerable:!1,configurable:!0})}}}},11940:(e,t,r)=>{"use strict";t.__esModule=!0,t.ThemeConsumer=t.DEFAULT_MIN_BREAKPOINT=t.DEFAULT_BREAKPOINTS=void 0,t.createBootstrapComponent=function(e,t){"string"==typeof t&&(t={prefix:t});let r=e.prototype&&e.prototype.isReactComponent,{prefix:o,forwardRefAs:i=r?"ref":"innerRef"}=t,l=n.forwardRef(({...t},r)=>{t[i]=r;let n=d(t.bsPrefix,o);return(0,a.jsx)(e,{...t,bsPrefix:n})});return l.displayName=`Bootstrap(${e.displayName||e.name})`,l},t.default=void 0,t.useBootstrapBreakpoints=function(){let{breakpoints:e}=(0,n.useContext)(s);return e},t.useBootstrapMinBreakpoint=function(){let{minBreakpoint:e}=(0,n.useContext)(s);return e},t.useBootstrapPrefix=d,t.useIsRTL=function(){let{dir:e}=(0,n.useContext)(s);return"rtl"===e};var n=function(e,t){if(e&&e.__esModule)return e;if(null===e||"object"!=typeof e&&"function"!=typeof e)return{default:e};var r=o(t);if(r&&r.has(e))return r.get(e);var n={__proto__:null},a=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var i in e)if("default"!==i&&Object.prototype.hasOwnProperty.call(e,i)){var l=a?Object.getOwnPropertyDescriptor(e,i):null;l&&(l.get||l.set)?Object.defineProperty(n,i,l):n[i]=e[i]}return n.default=e,r&&r.set(e,n),n}(r(82015)),a=r(8732);function o(e){if("function"!=typeof WeakMap)return null;var t=new WeakMap,r=new WeakMap;return(o=function(e){return e?r:t})(e)}let i=t.DEFAULT_BREAKPOINTS=["xxl","xl","lg","md","sm","xs"],l=t.DEFAULT_MIN_BREAKPOINT="xs",s=n.createContext({prefixes:{},breakpoints:i,minBreakpoint:l}),{Consumer:u,Provider:c}=s;function d(e,t){let{prefixes:r}=(0,n.useContext)(s);return e||r[t]||t}t.ThemeConsumer=u,t.default=function({prefixes:e={},breakpoints:t=i,minBreakpoint:r=l,dir:o,children:s}){let u=(0,n.useMemo)(()=>({prefixes:{...e},breakpoints:t,minBreakpoint:r,dir:o}),[e,t,r,o]);return(0,a.jsx)(c,{value:u,children:s})}},12403:(e,t,r)=>{"use strict";r.d(t,{A:()=>X});var n=r(3892),a=r.n(n),o=r(12388),i=r.n(o),l=r(58928),s=r.n(l),u=r(6009),c=r.n(u),d=r(123),f=r.n(d),p=r(57664),h=r.n(p),m=r(67364),_=r.n(m),y=r(81895),g=r.n(y),v=r(37766),b=r.n(v),x=r(11688),E=r.n(x),j=r(87571),w=r.n(j),R=r(82015),P=r(6952),O=r.n(P),T=r(72889),N=r(19799),A=r(80739),S=r(8732);let C=R.forwardRef(({className:e,bsPrefix:t,as:r="div",...n},o)=>(t=(0,A.oU)(t,"modal-body"),(0,S.jsx)(r,{ref:o,className:a()(e,t),...n})));C.displayName="ModalBody";var M=r(43601);let k=R.forwardRef(({bsPrefix:e,className:t,contentClassName:r,centered:n,size:o,fullscreen:i,children:l,scrollable:s,...u},c)=>{e=(0,A.oU)(e,"modal");let d=`${e}-dialog`,f="string"==typeof i?`${e}-fullscreen-${i}`:`${e}-fullscreen`;return(0,S.jsx)("div",{...u,ref:c,className:a()(d,t,o&&`${e}-${o}`,n&&`${d}-centered`,s&&`${d}-scrollable`,i&&f),children:(0,S.jsx)("div",{className:a()(`${e}-content`,r),children:l})})});k.displayName="ModalDialog";let I=k,D=R.forwardRef(({className:e,bsPrefix:t,as:r="div",...n},o)=>(t=(0,A.oU)(t,"modal-footer"),(0,S.jsx)(r,{ref:o,className:a()(e,t),...n})));D.displayName="ModalFooter";var L=r(46689);let U=R.forwardRef(({bsPrefix:e,className:t,closeLabel:r="Close",closeButton:n=!1,...o},i)=>(e=(0,A.oU)(e,"modal-header"),(0,S.jsx)(L.A,{ref:i,...o,className:a()(t,e),closeLabel:r,closeButton:n})));U.displayName="ModalHeader";let B=(0,r(7783).A)("h4"),F=R.forwardRef(({className:e,bsPrefix:t,as:r=B,...n},o)=>(t=(0,A.oU)(t,"modal-title"),(0,S.jsx)(r,{ref:o,className:a()(e,t),...n})));function $(e){return(0,S.jsx)(N.A,{...e,timeout:null})}function H(e){return(0,S.jsx)(N.A,{...e,timeout:null})}F.displayName="ModalTitle";let W=R.forwardRef(({bsPrefix:e,className:t,style:r,dialogClassName:n,contentClassName:o,children:l,dialogAs:u=I,"data-bs-theme":d,"aria-labelledby":p,"aria-describedby":m,"aria-label":y,show:v=!1,animation:x=!0,backdrop:j=!0,keyboard:P=!0,onEscapeKeyDown:N,onShow:C,onHide:k,container:D,autoFocus:L=!0,enforceFocus:U=!0,restoreFocus:B=!0,restoreFocusOptions:F,onEntered:W,onExit:X,onExiting:G,onEnter:q,onEntering:z,onExited:V,backdropClassName:K,manager:Y,...Q},J)=>{let[Z,ee]=(0,R.useState)({}),[et,er]=(0,R.useState)(!1),en=(0,R.useRef)(!1),ea=(0,R.useRef)(!1),eo=(0,R.useRef)(null),[ei,el]=_()(),es=b()(J,el),eu=g()(k),ec=(0,A.Wz)();e=(0,A.oU)(e,"modal");let ed=(0,R.useMemo)(()=>({onHide:eu}),[eu]);function ef(){return Y||(0,T.R)({isRTL:ec})}function ep(e){if(!s())return;let t=ef().getScrollbarWidth()>0,r=e.scrollHeight>c()(e).documentElement.clientHeight;ee({paddingRight:t&&!r?h()():void 0,paddingLeft:!t&&r?h()():void 0})}let eh=g()(()=>{ei&&ep(ei.dialog)});E()(()=>{f()(window,"resize",eh),null==eo.current||eo.current()});let em=()=>{en.current=!0},e_=e=>{en.current&&ei&&e.target===ei.dialog&&(ea.current=!0),en.current=!1},ey=()=>{er(!0),eo.current=w()(ei.dialog,()=>{er(!1)})},eg=e=>{e.target===e.currentTarget&&ey()},ev=e=>{if("static"===j)return void eg(e);if(ea.current||e.target!==e.currentTarget){ea.current=!1;return}null==k||k()},eb=(0,R.useCallback)(t=>(0,S.jsx)("div",{...t,className:a()(`${e}-backdrop`,K,!x&&"show")}),[x,K,e]),ex={...r,...Z};return ex.display="block",(0,S.jsx)(M.A.Provider,{value:ed,children:(0,S.jsx)(O(),{show:v,ref:es,backdrop:j,container:D,keyboard:!0,autoFocus:L,enforceFocus:U,restoreFocus:B,restoreFocusOptions:F,onEscapeKeyDown:e=>{P?null==N||N(e):(e.preventDefault(),"static"===j&&ey())},onShow:C,onHide:k,onEnter:(e,t)=>{e&&ep(e),null==q||q(e,t)},onEntering:(e,t)=>{null==z||z(e,t),i()(window,"resize",eh)},onEntered:W,onExit:e=>{null==eo.current||eo.current(),null==X||X(e)},onExiting:G,onExited:e=>{e&&(e.style.display=""),null==V||V(e),f()(window,"resize",eh)},manager:ef(),transition:x?$:void 0,backdropTransition:x?H:void 0,renderBackdrop:eb,renderDialog:r=>(0,S.jsx)("div",{role:"dialog",...r,style:ex,className:a()(t,e,et&&`${e}-static`,!x&&"show"),onClick:j?ev:void 0,onMouseUp:e_,"data-bs-theme":d,"aria-label":y,"aria-labelledby":p,"aria-describedby":m,children:(0,S.jsx)(u,{...Q,onMouseDown:em,className:n,contentClassName:o,children:l})})})})});W.displayName="Modal";let X=Object.assign(W,{Body:C,Header:U,Title:F,Footer:D,Dialog:I,TRANSITION_DURATION:300,BACKDROP_TRANSITION_DURATION:150})},12872:(e,t,r)=>{"use strict";r.d(t,{y:()=>n});class n extends Error{constructor({message:e,status:t,data:r}){super(e),this.name="TwitterApiError",this.status=t,this.data=r}}},13448:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"detectDomainLocale",{enumerable:!0,get:function(){return n}});let n=function(){for(var e=arguments.length,t=Array(e),n=0;n<e;n++)t[n]=arguments[n];return r(60031).C(...t)};("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},13524:(e,t,r)=>{"use strict";r.d(t,{A:()=>u});var n=r(3892),a=r.n(n),o=r(82015),i=r(80739),l=r(8732);let s=o.forwardRef(({bsPrefix:e,variant:t,animation:r="border",size:n,as:o="div",className:s,...u},c)=>{e=(0,i.oU)(e,"spinner");let d=`${e}-${r}`;return(0,l.jsx)(o,{ref:c,...u,className:a()(s,d,n&&`${d}-${n}`,t&&`text-${t}`)})});s.displayName="Spinner";let u=s},13690:(e,t,r)=>{"use strict";e.exports=r(63885).vendored.contexts.AppRouterContext},15027:(e,t,r)=>{"use strict";e.exports=r(63885).vendored.contexts.HooksClientContext},16220:(e,t,r)=>{e.exports=r(25150)},17980:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"removeLocale",{enumerable:!0,get:function(){return a}});let n=r(86290);function a(e,t){{let{pathname:r}=(0,n.parsePath)(e),a=r.toLowerCase(),o=null==t?void 0:t.toLowerCase();return t&&(a.startsWith("/"+o+"/")||a==="/"+o)?(r.length===t.length+1?"/":"")+e.slice(t.length+1):e}}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},18137:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{REDIRECT_ERROR_CODE:function(){return a},RedirectType:function(){return o},isRedirectError:function(){return i}});let n=r(25735),a="NEXT_REDIRECT";var o=function(e){return e.push="push",e.replace="replace",e}({});function i(e){if("object"!=typeof e||null===e||!("digest"in e)||"string"!=typeof e.digest)return!1;let t=e.digest.split(";"),[r,o]=t,i=t.slice(2,-2).join(";"),l=Number(t.at(-2));return r===a&&("replace"===o||"push"===o)&&"string"==typeof i&&!isNaN(l)&&l in n.RedirectStatusCode}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},19799:(e,t,r)=>{"use strict";r.d(t,{A:()=>h});var n=r(3892),a=r.n(n),o=r(82015),i=r(1919),l=r(1680),s=r(77291),u=r(49542),c=r(66933),d=r(8732);let f={[i.ENTERING]:"show",[i.ENTERED]:"show"},p=o.forwardRef(({className:e,children:t,transitionClasses:r={},onEnter:n,...i},p)=>{let h={in:!1,timeout:300,mountOnEnter:!1,unmountOnExit:!1,appear:!1,...i},m=(0,o.useCallback)((e,t)=>{(0,u.A)(e),null==n||n(e,t)},[n]);return(0,d.jsx)(c.A,{ref:p,addEndListener:s.A,...h,onEnter:m,childRef:(0,l.getChildRef)(t),children:(n,i)=>o.cloneElement(t,{...i,className:a()("fade",e,t.props.className,f[n],r[n])})})});p.displayName="Fade";let h=p},19811:e=>{e.exports={skeleton:"skeleton_skeleton__gUMqh",loading:"skeleton_loading__XZoZ6"}},19918:(e,t,r)=>{e.exports=r(44013)},20367:(e,t,r)=>{"use strict";function n(e){return e}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"removeBasePath",{enumerable:!0,get:function(){return n}}),r(20617),("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},20608:e=>{e.exports={anchor:"tweet-media-video_anchor__EMqq1",videoButton:"tweet-media-video_videoButton__P9iF2",videoButtonIcon:"tweet-media-video_videoButtonIcon__7gRo1",watchOnTwitter:"tweet-media-video_watchOnTwitter__2ucCU",viewReplies:"tweet-media-video_viewReplies__dp8G_"}},20617:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"hasBasePath",{enumerable:!0,get:function(){return a}});let n=r(69596);function a(e){return(0,n.pathHasPrefix)(e,"")}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},21275:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{ReadonlyURLSearchParams:function(){return c},RedirectType:function(){return a.RedirectType},forbidden:function(){return i.forbidden},notFound:function(){return o.notFound},permanentRedirect:function(){return n.permanentRedirect},redirect:function(){return n.redirect},unauthorized:function(){return l.unauthorized},unstable_rethrow:function(){return s.unstable_rethrow}});let n=r(26968),a=r(18137),o=r(7444),i=r(84619),l=r(76614),s=r(79198);class u extends Error{constructor(){super("Method unavailable on `ReadonlyURLSearchParams`. Read more: https://nextjs.org/docs/app/api-reference/functions/use-search-params#updating-searchparams")}}class c extends URLSearchParams{append(){throw new u}delete(){throw new u}set(){throw new u}sort(){throw new u}}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},21730:(e,t)=>{"use strict";function r(e){return e.replace(/\/$/,"")||"/"}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"removeTrailingSlash",{enumerable:!0,get:function(){return r}})},22199:(e,t)=>{"use strict";t.__esModule=!0,t.default=function(e){e.offsetHeight},e.exports=t.default},23778:(e,t,r)=>{"use strict";r.d(t,{A:()=>d});var n=r(3892),a=r.n(n),o=r(82015),i=r(80739),l=r(33496),s=r(30905),u=r(8732);let c=o.forwardRef(({bsPrefix:e,placement:t="right",className:r,style:n,children:o,arrowProps:c,hasDoneInitialMeasure:d,popper:f,show:p,...h},m)=>{e=(0,i.oU)(e,"tooltip");let _=(0,i.Wz)(),[y]=(null==t?void 0:t.split("-"))||[],g=(0,l.G)(y,_),v=n;return p&&!d&&(v={...n,...(0,s.A)(null==f?void 0:f.strategy)}),(0,u.jsxs)("div",{ref:m,style:v,role:"tooltip","x-placement":y,className:a()(r,e,`bs-tooltip-${g}`),...h,children:[(0,u.jsx)("div",{className:"tooltip-arrow",...c}),(0,u.jsx)("div",{className:`${e}-inner`,children:o})]})});c.displayName="Tooltip";let d=Object.assign(c,{TOOLTIP_OFFSET:[0,6]})},24132:(e,t,r)=>{"use strict";r.d(t,{A:()=>n});let n=r(82015).createContext({})},24562:(e,t,r)=>{"use strict";r.d(t,{A:()=>n});let n=function(){for(var e,t,r=0,n="",a=arguments.length;r<a;r++)(e=arguments[r])&&(t=function e(t){var r,n,a="";if("string"==typeof t||"number"==typeof t)a+=t;else if("object"==typeof t)if(Array.isArray(t)){var o=t.length;for(r=0;r<o;r++)t[r]&&(n=e(t[r]))&&(a&&(a+=" "),a+=n)}else for(n in t)t[n]&&(a&&(a+=" "),a+=n);return a}(e))&&(n&&(n+=" "),n+=t);return n}},24658:e=>{e.exports={root:"tweet-body_root__ChzUj"}},24765:(e,t,r)=>{"use strict";r.d(t,{A:()=>a});let n=r(82015).createContext(null);n.displayName="InputGroupContext";let a=n},25048:e=>{e.exports={verifiedOld:"verified-badge_verifiedOld__zcaba",verifiedBlue:"verified-badge_verifiedBlue__s3_Vu",verifiedGovernment:"verified-badge_verifiedGovernment__qRJxq"}},25150:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{ReadonlyURLSearchParams:function(){return s.ReadonlyURLSearchParams},RedirectType:function(){return s.RedirectType},ServerInsertedHTMLContext:function(){return u.ServerInsertedHTMLContext},forbidden:function(){return s.forbidden},notFound:function(){return s.notFound},permanentRedirect:function(){return s.permanentRedirect},redirect:function(){return s.redirect},unauthorized:function(){return s.unauthorized},unstable_rethrow:function(){return s.unstable_rethrow},useParams:function(){return h},usePathname:function(){return f},useRouter:function(){return p},useSearchParams:function(){return d},useSelectedLayoutSegment:function(){return _},useSelectedLayoutSegments:function(){return m},useServerInsertedHTML:function(){return u.useServerInsertedHTML}});let n=r(82015),a=r(13690),o=r(15027),i=r(33453),l=r(67286),s=r(21275),u=r(59281),c=r(11836).useDynamicRouteParams;function d(){let e=(0,n.useContext)(o.SearchParamsContext),t=(0,n.useMemo)(()=>e?new s.ReadonlyURLSearchParams(e):null,[e]);{let{bailoutToClientRendering:e}=r(74853);e("useSearchParams()")}return t}function f(){return null==c||c("usePathname()"),(0,n.useContext)(o.PathnameContext)}function p(){let e=(0,n.useContext)(a.AppRouterContext);if(null===e)throw Object.defineProperty(Error("invariant expected app router to be mounted"),"__NEXT_ERROR_CODE",{value:"E238",enumerable:!1,configurable:!0});return e}function h(){return null==c||c("useParams()"),(0,n.useContext)(o.PathParamsContext)}function m(e){void 0===e&&(e="children"),null==c||c("useSelectedLayoutSegments()");let t=(0,n.useContext)(a.LayoutRouterContext);return t?function e(t,r,n,a){let o;if(void 0===n&&(n=!0),void 0===a&&(a=[]),n)o=t[1][r];else{var s;let e=t[1];o=null!=(s=e.children)?s:Object.values(e)[0]}if(!o)return a;let u=o[0],c=(0,i.getSegmentValue)(u);return!c||c.startsWith(l.PAGE_SEGMENT_KEY)?a:(a.push(c),e(o,r,!1,a))}(t.parentTree,e):null}function _(e){void 0===e&&(e="children"),null==c||c("useSelectedLayoutSegment()");let t=m(e);if(!t||0===t.length)return null;let r="children"===e?t[0]:t[t.length-1];return r===l.DEFAULT_SEGMENT_KEY?null:r}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},25735:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"RedirectStatusCode",{enumerable:!0,get:function(){return r}});var r=function(e){return e[e.SeeOther=303]="SeeOther",e[e.TemporaryRedirect=307]="TemporaryRedirect",e[e.PermanentRedirect=308]="PermanentRedirect",e}({});("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},26968:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{getRedirectError:function(){return i},getRedirectStatusCodeFromError:function(){return d},getRedirectTypeFromError:function(){return c},getURLFromRedirectError:function(){return u},permanentRedirect:function(){return s},redirect:function(){return l}});let n=r(25735),a=r(18137),o=r(19121).actionAsyncStorage;function i(e,t,r){void 0===r&&(r=n.RedirectStatusCode.TemporaryRedirect);let o=Object.defineProperty(Error(a.REDIRECT_ERROR_CODE),"__NEXT_ERROR_CODE",{value:"E394",enumerable:!1,configurable:!0});return o.digest=a.REDIRECT_ERROR_CODE+";"+t+";"+e+";"+r+";",o}function l(e,t){var r;throw null!=t||(t=(null==o||null==(r=o.getStore())?void 0:r.isAction)?a.RedirectType.push:a.RedirectType.replace),i(e,t,n.RedirectStatusCode.TemporaryRedirect)}function s(e,t){throw void 0===t&&(t=a.RedirectType.replace),i(e,t,n.RedirectStatusCode.PermanentRedirect)}function u(e){return(0,a.isRedirectError)(e)?e.digest.split(";").slice(2,-2).join(";"):null}function c(e){if(!(0,a.isRedirectError)(e))throw Object.defineProperty(Error("Not a redirect error"),"__NEXT_ERROR_CODE",{value:"E260",enumerable:!1,configurable:!0});return e.digest.split(";",2)[1]}function d(e){if(!(0,a.isRedirectError)(e))throw Object.defineProperty(Error("Not a redirect error"),"__NEXT_ERROR_CODE",{value:"E260",enumerable:!1,configurable:!0});return Number(e.digest.split(";").at(-2))}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},28004:(e,t,r)=>{"use strict";function n(e,t){return e}r.d(t,{A:()=>n}),r(16116),r(82015),r(37766)},28333:(e,t)=>{"use strict";function r(e,t){return void 0===t&&(t=""),("/"===e?"/index":/^\/index(\/|$)/.test(e)?"/index"+e:e)+t}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"default",{enumerable:!0,get:function(){return r}})},28537:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"HTML_LIMITED_BOT_UA_RE",{enumerable:!0,get:function(){return r}});let r=/Mediapartners-Google|Slurp|DuckDuckBot|baiduspider|yandex|sogou|bitlybot|tumblr|vkShare|quora link preview|redditbot|ia_archiver|Bingbot|BingPreview|applebot|facebookexternalhit|facebookcatalog|Twitterbot|LinkedInBot|Slackbot|Discordbot|WhatsApp|SkypeUriPreview|Yeti/i},30905:(e,t,r)=>{"use strict";function n(e="absolute"){return{position:e,top:"0",left:"0",opacity:"0",pointerEvents:"none"}}r.d(t,{A:()=>n})},32006:e=>{e.exports={info:"tweet-info_info__ll_kH",infoLink:"tweet-info_infoLink__xdgYO",infoIcon:"tweet-info_infoIcon__S8lzA"}},32072:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{ACTION_SUFFIX:function(){return d},APP_DIR_ALIAS:function(){return S},CACHE_ONE_YEAR:function(){return j},DOT_NEXT_ALIAS:function(){return N},ESLINT_DEFAULT_DIRS:function(){return Y},GSP_NO_RETURNED_VALUE:function(){return X},GSSP_COMPONENT_MEMBER_ERROR:function(){return z},GSSP_NO_RETURNED_VALUE:function(){return G},INFINITE_CACHE:function(){return w},INSTRUMENTATION_HOOK_FILENAME:function(){return O},MATCHED_PATH_HEADER:function(){return a},MIDDLEWARE_FILENAME:function(){return R},MIDDLEWARE_LOCATION_REGEXP:function(){return P},NEXT_BODY_SUFFIX:function(){return h},NEXT_CACHE_IMPLICIT_TAG_ID:function(){return E},NEXT_CACHE_REVALIDATED_TAGS_HEADER:function(){return _},NEXT_CACHE_REVALIDATE_TAG_TOKEN_HEADER:function(){return y},NEXT_CACHE_SOFT_TAG_MAX_LENGTH:function(){return x},NEXT_CACHE_TAGS_HEADER:function(){return m},NEXT_CACHE_TAG_MAX_ITEMS:function(){return v},NEXT_CACHE_TAG_MAX_LENGTH:function(){return b},NEXT_DATA_SUFFIX:function(){return f},NEXT_INTERCEPTION_MARKER_PREFIX:function(){return n},NEXT_META_SUFFIX:function(){return p},NEXT_QUERY_PARAM_PREFIX:function(){return r},NEXT_RESUME_HEADER:function(){return g},NON_STANDARD_NODE_ENV:function(){return V},PAGES_DIR_ALIAS:function(){return T},PRERENDER_REVALIDATE_HEADER:function(){return o},PRERENDER_REVALIDATE_ONLY_GENERATED_HEADER:function(){return i},PUBLIC_DIR_MIDDLEWARE_CONFLICT:function(){return U},ROOT_DIR_ALIAS:function(){return A},RSC_ACTION_CLIENT_WRAPPER_ALIAS:function(){return L},RSC_ACTION_ENCRYPTION_ALIAS:function(){return D},RSC_ACTION_PROXY_ALIAS:function(){return k},RSC_ACTION_VALIDATE_ALIAS:function(){return M},RSC_CACHE_WRAPPER_ALIAS:function(){return I},RSC_MOD_REF_PROXY_ALIAS:function(){return C},RSC_PREFETCH_SUFFIX:function(){return l},RSC_SEGMENTS_DIR_SUFFIX:function(){return s},RSC_SEGMENT_SUFFIX:function(){return u},RSC_SUFFIX:function(){return c},SERVER_PROPS_EXPORT_ERROR:function(){return W},SERVER_PROPS_GET_INIT_PROPS_CONFLICT:function(){return F},SERVER_PROPS_SSG_CONFLICT:function(){return $},SERVER_RUNTIME:function(){return Q},SSG_FALLBACK_EXPORT_ERROR:function(){return K},SSG_GET_INITIAL_PROPS_CONFLICT:function(){return B},STATIC_STATUS_PAGE_GET_INITIAL_PROPS_ERROR:function(){return H},UNSTABLE_REVALIDATE_RENAME_ERROR:function(){return q},WEBPACK_LAYERS:function(){return Z},WEBPACK_RESOURCE_QUERIES:function(){return ee}});let r="nxtP",n="nxtI",a="x-matched-path",o="x-prerender-revalidate",i="x-prerender-revalidate-if-generated",l=".prefetch.rsc",s=".segments",u=".segment.rsc",c=".rsc",d=".action",f=".json",p=".meta",h=".body",m="x-next-cache-tags",_="x-next-revalidated-tags",y="x-next-revalidate-tag-token",g="next-resume",v=128,b=256,x=1024,E="_N_T_",j=31536e3,w=0xfffffffe,R="middleware",P=`(?:src/)?${R}`,O="instrumentation",T="private-next-pages",N="private-dot-next",A="private-next-root-dir",S="private-next-app-dir",C="private-next-rsc-mod-ref-proxy",M="private-next-rsc-action-validate",k="private-next-rsc-server-reference",I="private-next-rsc-cache-wrapper",D="private-next-rsc-action-encryption",L="private-next-rsc-action-client-wrapper",U="You can not have a '_next' folder inside of your public folder. This conflicts with the internal '/_next' route. https://nextjs.org/docs/messages/public-next-folder-conflict",B="You can not use getInitialProps with getStaticProps. To use SSG, please remove your getInitialProps",F="You can not use getInitialProps with getServerSideProps. Please remove getInitialProps.",$="You can not use getStaticProps or getStaticPaths with getServerSideProps. To use SSG, please remove getServerSideProps",H="can not have getInitialProps/getServerSideProps, https://nextjs.org/docs/messages/404-get-initial-props",W="pages with `getServerSideProps` can not be exported. See more info here: https://nextjs.org/docs/messages/gssp-export",X="Your `getStaticProps` function did not return an object. Did you forget to add a `return`?",G="Your `getServerSideProps` function did not return an object. Did you forget to add a `return`?",q="The `unstable_revalidate` property is available for general use.\nPlease use `revalidate` instead.",z="can not be attached to a page's component and must be exported from the page. See more info here: https://nextjs.org/docs/messages/gssp-component-member",V='You are using a non-standard "NODE_ENV" value in your environment. This creates inconsistencies in the project and is strongly advised against. Read more: https://nextjs.org/docs/messages/non-standard-node-env',K="Pages with `fallback` enabled in `getStaticPaths` can not be exported. See more info here: https://nextjs.org/docs/messages/ssg-fallback-true-export",Y=["app","pages","components","lib","src"],Q={edge:"edge",experimentalEdge:"experimental-edge",nodejs:"nodejs"},J={shared:"shared",reactServerComponents:"rsc",serverSideRendering:"ssr",actionBrowser:"action-browser",apiNode:"api-node",apiEdge:"api-edge",middleware:"middleware",instrument:"instrument",edgeAsset:"edge-asset",appPagesBrowser:"app-pages-browser",pagesDirBrowser:"pages-dir-browser",pagesDirEdge:"pages-dir-edge",pagesDirNode:"pages-dir-node"},Z={...J,GROUP:{builtinReact:[J.reactServerComponents,J.actionBrowser],serverOnly:[J.reactServerComponents,J.actionBrowser,J.instrument,J.middleware],neutralTarget:[J.apiNode,J.apiEdge],clientOnly:[J.serverSideRendering,J.appPagesBrowser],bundled:[J.reactServerComponents,J.actionBrowser,J.serverSideRendering,J.appPagesBrowser,J.shared,J.instrument,J.middleware],appPages:[J.reactServerComponents,J.serverSideRendering,J.appPagesBrowser,J.actionBrowser]}},ee={edgeSSREntry:"__next_edge_ssr_entry__",metadata:"__next_metadata__",metadataRoute:"__next_metadata_route__",metadataImageMeta:"__next_metadata_image_meta__"}},32848:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{formatUrl:function(){return o},formatWithValidation:function(){return l},urlObjectKeys:function(){return i}});let n=r(3147)._(r(87536)),a=/https?|ftp|gopher|file/;function o(e){let{auth:t,hostname:r}=e,o=e.protocol||"",i=e.pathname||"",l=e.hash||"",s=e.query||"",u=!1;t=t?encodeURIComponent(t).replace(/%3A/i,":")+"@":"",e.host?u=t+e.host:r&&(u=t+(~r.indexOf(":")?"["+r+"]":r),e.port&&(u+=":"+e.port)),s&&"object"==typeof s&&(s=String(n.urlQueryToSearchParams(s)));let c=e.search||s&&"?"+s||"";return o&&!o.endsWith(":")&&(o+=":"),e.slashes||(!o||a.test(o))&&!1!==u?(u="//"+(u||""),i&&"/"!==i[0]&&(i="/"+i)):u||(u=""),l&&"#"!==l[0]&&(l="#"+l),c&&"?"!==c[0]&&(c="?"+c),""+o+u+(i=i.replace(/[?#]/g,encodeURIComponent))+(c=c.replace("#","%23"))+l}let i=["auth","hash","host","hostname","href","path","pathname","port","protocol","query","search","slashes"];function l(e){return o(e)}},33057:e=>{e.exports={header:"tweet-header_header__CXzdi",avatar:"tweet-header_avatar__0Wi9G",avatarOverflow:"tweet-header_avatarOverflow__E2gxj",avatarSquare:"tweet-header_avatarSquare__uIUBO",avatarShadow:"tweet-header_avatarShadow__CB9Zo",author:"tweet-header_author___jWoR",authorLink:"tweet-header_authorLink__qj5Sm",authorVerified:"tweet-header_authorVerified__OFYo2",authorLinkText:"tweet-header_authorLinkText__y6HdU",authorMeta:"tweet-header_authorMeta__gIC3U",authorFollow:"tweet-header_authorFollow__w_j4h",username:"tweet-header_username__UebZb",follow:"tweet-header_follow__Fi7bf",separator:"tweet-header_separator__d4pqe",brand:"tweet-header_brand__0FLQl",twitterIcon:"tweet-header_twitterIcon__m0Rzu"}},33266:e=>{e.exports={replies:"tweet-replies_replies__PUxl8",link:"tweet-replies_link__roxYQ",text:"tweet-replies_text__o0Naf"}},33453:(e,t)=>{"use strict";function r(e){return Array.isArray(e)?e[1]:e}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"getSegmentValue",{enumerable:!0,get:function(){return r}}),("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},33496:(e,t,r)=>{"use strict";function n(e,t){let r=e;return"left"===e?r=t?"end":"start":"right"===e&&(r=t?"start":"end"),r}r.d(t,{G:()=>n}),r(82015).Component},33718:(e,t,r)=>{"use strict";r.d(t,{l:()=>s});var n=r(8732),a=r(97580),o=r(19811);let i=({style:e})=>(0,n.jsx)("span",{className:o.skeleton,style:e});var l=r(39503);let s=()=>(0,n.jsxs)(a.X,{className:l.root,children:[(0,n.jsx)(i,{style:{height:"3rem",marginBottom:"0.75rem"}}),(0,n.jsx)(i,{style:{height:"6rem",margin:"0.5rem 0"}}),(0,n.jsx)("div",{style:{borderTop:"var(--tweet-border)",margin:"0.5rem 0"}}),(0,n.jsx)(i,{style:{height:"2rem"}}),(0,n.jsx)(i,{style:{height:"2rem",borderRadius:"9999px",marginTop:"0.5rem"}})]})},34036:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"getPathMatch",{enumerable:!0,get:function(){return a}});let n=r(41791);function a(e,t){let r=[],a=(0,n.pathToRegexp)(e,r,{delimiter:"/",sensitive:"boolean"==typeof(null==t?void 0:t.sensitive)&&t.sensitive,strict:null==t?void 0:t.strict}),o=(0,n.regexpToFunction)((null==t?void 0:t.regexModifier)?new RegExp(t.regexModifier(a.source),a.flags):a,r);return(e,n)=>{if("string"!=typeof e)return!1;let a=o(e);if(!a)return!1;if(null==t?void 0:t.removeUnnamedParams)for(let e of r)"number"==typeof e.name&&delete a.params[e.name];return{...n,...a.params}}}},34445:(e,t,r)=>{"use strict";function n(e){return function(){let{cookie:t}=e;if(!t)return{};let{parse:n}=r(53484);return n(Array.isArray(t)?t.join("; "):t)}}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"getCookieParser",{enumerable:!0,get:function(){return n}})},35623:(e,t,r)=>{"use strict";r.d(t,{A:()=>d});var n=r(3892),a=r.n(n),o=r(82015),i=r(80739),l=r(63899),s=r(67776),u=r(8732);let c=o.forwardRef(({id:e,title:t,children:r,bsPrefix:n,className:o,rootCloseEvent:c,menuRole:d,disabled:f,active:p,renderMenuOnMount:h,menuVariant:m,..._},y)=>{let g=(0,i.oU)(void 0,"nav-item");return(0,u.jsxs)(l.A,{ref:y,..._,className:a()(o,g),children:[(0,u.jsx)(l.A.Toggle,{id:e,eventKey:null,active:p,disabled:f,childBsPrefix:n,as:s.A,children:t}),(0,u.jsx)(l.A.Menu,{role:d,renderOnMount:h,rootCloseEvent:c,variant:m,children:r})]})});c.displayName="NavDropdown";let d=Object.assign(c,{Item:l.A.Item,ItemText:l.A.ItemText,Divider:l.A.Divider,Header:l.A.Header})},36655:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"addPathPrefix",{enumerable:!0,get:function(){return a}});let n=r(86290);function a(e,t){if(!e.startsWith("/")||!t)return e;let{pathname:r,query:a,hash:o}=(0,n.parsePath)(e);return""+t+r+a+o}},37145:(e,t)=>{"use strict";function r(e,t){let r=Object.keys(e);if(r.length!==Object.keys(t).length)return!1;for(let n=r.length;n--;){let a=r[n];if("query"===a){let r=Object.keys(e.query);if(r.length!==Object.keys(t.query).length)return!1;for(let n=r.length;n--;){let a=r[n];if(!t.query.hasOwnProperty(a)||e.query[a]!==t.query[a])return!1}}else if(!t.hasOwnProperty(a)||e[a]!==t[a])return!1}return!0}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"compareRouterStates",{enumerable:!0,get:function(){return r}})},37779:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"normalizePathTrailingSlash",{enumerable:!0,get:function(){return o}});let n=r(21730),a=r(86290),o=e=>{if(!e.startsWith("/"))return e;let{pathname:t,query:r,hash:o}=(0,a.parsePath)(e);return""+(0,n.removeTrailingSlash)(t)+r+o};("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},39503:e=>{e.exports={root:"tweet-skeleton_root__1sn43"}},40602:e=>{e.exports={root:"quoted-tweet-body_root__szSfI"}},41027:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"default",{enumerable:!0,get:function(){return u}});let n=r(34036),a=r(93045),o=r(21730),i=r(92746),l=r(20367),s=r(65939);function u(e,t,r,u,c,d){let f,p=!1,h=!1,m=(0,s.parseRelativeUrl)(e),_=(0,o.removeTrailingSlash)((0,i.normalizeLocalePath)((0,l.removeBasePath)(m.pathname),d).pathname),y=r=>{let s=(0,n.getPathMatch)(r.source+"",{removeUnnamedParams:!0,strict:!0})(m.pathname);if((r.has||r.missing)&&s){let e=(0,a.matchHas)({headers:{host:document.location.hostname,"user-agent":navigator.userAgent},cookies:document.cookie.split("; ").reduce((e,t)=>{let[r,...n]=t.split("=");return e[r]=n.join("="),e},{})},m.query,r.has,r.missing);e?Object.assign(s,e):s=!1}if(s){if(!r.destination)return h=!0,!0;let n=(0,a.prepareDestination)({appendParamsToQuery:!0,destination:r.destination,params:s,query:u});if(m=n.parsedDestination,e=n.newUrl,Object.assign(u,n.parsedDestination.query),_=(0,o.removeTrailingSlash)((0,i.normalizeLocalePath)((0,l.removeBasePath)(e),d).pathname),t.includes(_))return p=!0,f=_,!0;if((f=c(_))!==e&&t.includes(f))return p=!0,!0}},g=!1;for(let e=0;e<r.beforeFiles.length;e++)y(r.beforeFiles[e]);if(!(p=t.includes(_))){if(!g){for(let e=0;e<r.afterFiles.length;e++)if(y(r.afterFiles[e])){g=!0;break}}if(g||(f=c(_),g=p=t.includes(f)),!g){for(let e=0;e<r.fallback.length;e++)if(y(r.fallback[e])){g=!0;break}}}return{asPath:e,parsedAs:m,matchedPage:p,resolvedHref:f,externalDest:h}}},41217:(e,t)=>{"use strict";function r(){let e=Object.create(null);return{on(t,r){(e[t]||(e[t]=[])).push(r)},off(t,r){e[t]&&e[t].splice(e[t].indexOf(r)>>>0,1)},emit(t){for(var r=arguments.length,n=Array(r>1?r-1:0),a=1;a<r;a++)n[a-1]=arguments[a];(e[t]||[]).slice().map(e=>{e(...n)})}}}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"default",{enumerable:!0,get:function(){return r}})},41791:e=>{(()=>{"use strict";"undefined"!=typeof __nccwpck_require__&&(__nccwpck_require__.ab=__dirname+"/");var t={};(()=>{function e(e,t){void 0===t&&(t={});for(var r=function(e){for(var t=[],r=0;r<e.length;){var n=e[r];if("*"===n||"+"===n||"?"===n){t.push({type:"MODIFIER",index:r,value:e[r++]});continue}if("\\"===n){t.push({type:"ESCAPED_CHAR",index:r++,value:e[r++]});continue}if("{"===n){t.push({type:"OPEN",index:r,value:e[r++]});continue}if("}"===n){t.push({type:"CLOSE",index:r,value:e[r++]});continue}if(":"===n){for(var a="",o=r+1;o<e.length;){var i=e.charCodeAt(o);if(i>=48&&i<=57||i>=65&&i<=90||i>=97&&i<=122||95===i){a+=e[o++];continue}break}if(!a)throw TypeError("Missing parameter name at "+r);t.push({type:"NAME",index:r,value:a}),r=o;continue}if("("===n){var l=1,s="",o=r+1;if("?"===e[o])throw TypeError('Pattern cannot start with "?" at '+o);for(;o<e.length;){if("\\"===e[o]){s+=e[o++]+e[o++];continue}if(")"===e[o]){if(0==--l){o++;break}}else if("("===e[o]&&(l++,"?"!==e[o+1]))throw TypeError("Capturing groups are not allowed at "+o);s+=e[o++]}if(l)throw TypeError("Unbalanced pattern at "+r);if(!s)throw TypeError("Missing pattern at "+r);t.push({type:"PATTERN",index:r,value:s}),r=o;continue}t.push({type:"CHAR",index:r,value:e[r++]})}return t.push({type:"END",index:r,value:""}),t}(e),n=t.prefixes,o=void 0===n?"./":n,i="[^"+a(t.delimiter||"/#?")+"]+?",l=[],s=0,u=0,c="",d=function(e){if(u<r.length&&r[u].type===e)return r[u++].value},f=function(e){var t=d(e);if(void 0!==t)return t;var n=r[u];throw TypeError("Unexpected "+n.type+" at "+n.index+", expected "+e)},p=function(){for(var e,t="";e=d("CHAR")||d("ESCAPED_CHAR");)t+=e;return t};u<r.length;){var h=d("CHAR"),m=d("NAME"),_=d("PATTERN");if(m||_){var y=h||"";-1===o.indexOf(y)&&(c+=y,y=""),c&&(l.push(c),c=""),l.push({name:m||s++,prefix:y,suffix:"",pattern:_||i,modifier:d("MODIFIER")||""});continue}var g=h||d("ESCAPED_CHAR");if(g){c+=g;continue}if(c&&(l.push(c),c=""),d("OPEN")){var y=p(),v=d("NAME")||"",b=d("PATTERN")||"",x=p();f("CLOSE"),l.push({name:v||(b?s++:""),pattern:v&&!b?i:b,prefix:y,suffix:x,modifier:d("MODIFIER")||""});continue}f("END")}return l}function r(e,t){void 0===t&&(t={});var r=o(t),n=t.encode,a=void 0===n?function(e){return e}:n,i=t.validate,l=void 0===i||i,s=e.map(function(e){if("object"==typeof e)return RegExp("^(?:"+e.pattern+")$",r)});return function(t){for(var r="",n=0;n<e.length;n++){var o=e[n];if("string"==typeof o){r+=o;continue}var i=t?t[o.name]:void 0,u="?"===o.modifier||"*"===o.modifier,c="*"===o.modifier||"+"===o.modifier;if(Array.isArray(i)){if(!c)throw TypeError('Expected "'+o.name+'" to not repeat, but got an array');if(0===i.length){if(u)continue;throw TypeError('Expected "'+o.name+'" to not be empty')}for(var d=0;d<i.length;d++){var f=a(i[d],o);if(l&&!s[n].test(f))throw TypeError('Expected all "'+o.name+'" to match "'+o.pattern+'", but got "'+f+'"');r+=o.prefix+f+o.suffix}continue}if("string"==typeof i||"number"==typeof i){var f=a(String(i),o);if(l&&!s[n].test(f))throw TypeError('Expected "'+o.name+'" to match "'+o.pattern+'", but got "'+f+'"');r+=o.prefix+f+o.suffix;continue}if(!u){var p=c?"an array":"a string";throw TypeError('Expected "'+o.name+'" to be '+p)}}return r}}function n(e,t,r){void 0===r&&(r={});var n=r.decode,a=void 0===n?function(e){return e}:n;return function(r){var n=e.exec(r);if(!n)return!1;for(var o=n[0],i=n.index,l=Object.create(null),s=1;s<n.length;s++)!function(e){if(void 0!==n[e]){var r=t[e-1];"*"===r.modifier||"+"===r.modifier?l[r.name]=n[e].split(r.prefix+r.suffix).map(function(e){return a(e,r)}):l[r.name]=a(n[e],r)}}(s);return{path:o,index:i,params:l}}}function a(e){return e.replace(/([.+*?=^!:${}()[\]|/\\])/g,"\\$1")}function o(e){return e&&e.sensitive?"":"i"}function i(e,t,r){void 0===r&&(r={});for(var n=r.strict,i=void 0!==n&&n,l=r.start,s=r.end,u=r.encode,c=void 0===u?function(e){return e}:u,d="["+a(r.endsWith||"")+"]|$",f="["+a(r.delimiter||"/#?")+"]",p=void 0===l||l?"^":"",h=0;h<e.length;h++){var m=e[h];if("string"==typeof m)p+=a(c(m));else{var _=a(c(m.prefix)),y=a(c(m.suffix));if(m.pattern)if(t&&t.push(m),_||y)if("+"===m.modifier||"*"===m.modifier){var g="*"===m.modifier?"?":"";p+="(?:"+_+"((?:"+m.pattern+")(?:"+y+_+"(?:"+m.pattern+"))*)"+y+")"+g}else p+="(?:"+_+"("+m.pattern+")"+y+")"+m.modifier;else p+="("+m.pattern+")"+m.modifier;else p+="(?:"+_+y+")"+m.modifier}}if(void 0===s||s)i||(p+=f+"?"),p+=r.endsWith?"(?="+d+")":"$";else{var v=e[e.length-1],b="string"==typeof v?f.indexOf(v[v.length-1])>-1:void 0===v;i||(p+="(?:"+f+"(?="+d+"))?"),b||(p+="(?="+f+"|"+d+")")}return new RegExp(p,o(r))}function l(t,r,n){if(t instanceof RegExp){if(!r)return t;var a=t.source.match(/\((?!\?)/g);if(a)for(var s=0;s<a.length;s++)r.push({name:s,prefix:"",suffix:"",modifier:"",pattern:""});return t}return Array.isArray(t)?RegExp("(?:"+t.map(function(e){return l(e,r,n).source}).join("|")+")",o(n)):i(e(t,n),r,n)}Object.defineProperty(t,"__esModule",{value:!0}),t.parse=e,t.compile=function(t,n){return r(e(t,n),n)},t.tokensToFunction=r,t.match=function(e,t){var r=[];return n(l(e,r,t),r,t)},t.regexpToFunction=n,t.tokensToRegexp=i,t.pathToRegexp=l})(),e.exports=t})()},41833:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"addLocale",{enumerable:!0,get:function(){return o}});let n=r(36655),a=r(69596);function o(e,t,r,o){if(!t||t===r)return e;let i=e.toLowerCase();return!o&&((0,a.pathHasPrefix)(i,"/api")||(0,a.pathHasPrefix)(i,"/"+t.toLowerCase()))?e:(0,n.addPathPrefix)(e,"/"+t)}},41918:(e,t)=>{"use strict";function r(e){return"/api"===e||!!(null==e?void 0:e.startsWith("/api/"))}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"isAPIRoute",{enumerable:!0,get:function(){return r}})},42535:e=>{e.exports={verified:"icons_verified__1eJnA"}},42998:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"unstable_rethrow",{enumerable:!0,get:function(){return function e(t){if((0,i.isNextRouterError)(t)||(0,o.isBailoutToCSRError)(t)||(0,s.isDynamicServerError)(t)||(0,l.isDynamicPostpone)(t)||(0,a.isPostpone)(t)||(0,n.isHangingPromiseRejectionError)(t))throw t;t instanceof Error&&"cause"in t&&e(t.cause)}}});let n=r(45061),a=r(66174),o=r(86261),i=r(60889),l=r(11836),s=r(43210);("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},43210:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{DynamicServerError:function(){return n},isDynamicServerError:function(){return a}});let r="DYNAMIC_SERVER_USAGE";class n extends Error{constructor(e){super("Dynamic server usage: "+e),this.description=e,this.digest=r}}function a(e){return"object"==typeof e&&null!==e&&"digest"in e&&"string"==typeof e.digest&&e.digest===r}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},43601:(e,t,r)=>{"use strict";r.d(t,{A:()=>n});let n=r(82015).createContext({onHide(){}})},44013:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{default:function(){return x},useLinkStatus:function(){return b}});let n=r(3147),a=r(8732),o=n._(r(82015)),i=r(56231),l=r(60651),s=r(32848),u=r(54718),c=r(91169),d=r(52088),f=r(56152),p=r(1311),h=r(96092),m=r(69871);function _(e,t,r,n){}function y(e){return"string"==typeof e?e:(0,s.formatUrl)(e)}r(89281);let g=o.default.forwardRef(function(e,t){let r,n,{href:s,as:_,children:g,prefetch:v=null,passHref:b,replace:x,shallow:E,scroll:j,locale:w,onClick:R,onNavigate:P,onMouseEnter:O,onTouchStart:T,legacyBehavior:N=!1,...A}=e;r=g,N&&("string"==typeof r||"number"==typeof r)&&(r=(0,a.jsx)("a",{children:r}));let S=o.default.useContext(d.RouterContext),C=!1!==v,{href:M,as:k}=o.default.useMemo(()=>{if(!S){let e=y(s);return{href:e,as:_?y(_):e}}let[e,t]=(0,i.resolveHref)(S,s,!0);return{href:e,as:_?(0,i.resolveHref)(S,_):t||e}},[S,s,_]),I=o.default.useRef(M),D=o.default.useRef(k);N&&(n=o.default.Children.only(r));let L=N?n&&"object"==typeof n&&n.ref:t,[U,B,F]=(0,f.useIntersection)({rootMargin:"200px"}),$=o.default.useCallback(e=>{(D.current!==k||I.current!==M)&&(F(),D.current=k,I.current=M),U(e)},[k,M,F,U]),H=(0,m.useMergedRef)($,L);o.default.useEffect(()=>{},[k,M,B,w,C,null==S?void 0:S.locale,S]);let W={ref:H,onClick(e){N||"function"!=typeof R||R(e),N&&n.props&&"function"==typeof n.props.onClick&&n.props.onClick(e),S&&(e.defaultPrevented||function(e,t,r,n,a,o,i,s,u){let{nodeName:c}=e.currentTarget;if(!("A"===c.toUpperCase()&&function(e){let t=e.currentTarget.getAttribute("target");return t&&"_self"!==t||e.metaKey||e.ctrlKey||e.shiftKey||e.altKey||e.nativeEvent&&2===e.nativeEvent.which}(e)||e.currentTarget.hasAttribute("download"))){if(!(0,l.isLocalURL)(r)){a&&(e.preventDefault(),location.replace(r));return}e.preventDefault(),(()=>{if(u){let e=!1;if(u({preventDefault:()=>{e=!0}}),e)return}let e=null==i||i;"beforePopState"in t?t[a?"replace":"push"](r,n,{shallow:o,locale:s,scroll:e}):t[a?"replace":"push"](n||r,{scroll:e})})()}}(e,S,M,k,x,E,j,w,P))},onMouseEnter(e){N||"function"!=typeof O||O(e),N&&n.props&&"function"==typeof n.props.onMouseEnter&&n.props.onMouseEnter(e)},onTouchStart:function(e){N||"function"!=typeof T||T(e),N&&n.props&&"function"==typeof n.props.onTouchStart&&n.props.onTouchStart(e)}};if((0,u.isAbsoluteUrl)(k))W.href=k;else if(!N||b||"a"===n.type&&!("href"in n.props)){let e=void 0!==w?w:null==S?void 0:S.locale;W.href=(null==S?void 0:S.isLocaleDomain)&&(0,p.getDomainLocale)(k,e,null==S?void 0:S.locales,null==S?void 0:S.domainLocales)||(0,h.addBasePath)((0,c.addLocale)(k,e,null==S?void 0:S.defaultLocale))}return N?o.default.cloneElement(n,W):(0,a.jsx)("a",{...A,...W,children:r})}),v=(0,o.createContext)({pending:!1}),b=()=>(0,o.useContext)(v),x=g;("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},44080:(e,t,r)=>{"use strict";r.d(t,{A:()=>d});var n=r(3892),a=r.n(n),o=r(82015),i=r(29825),l=r.n(i),s=r(8732);let u={type:l().string,tooltip:l().bool,as:l().elementType},c=o.forwardRef(({as:e="div",className:t,type:r="valid",tooltip:n=!1,...o},i)=>(0,s.jsx)(e,{...o,ref:i,className:a()(t,`${r}-${n?"tooltip":"feedback"}`)}));c.displayName="Feedback",c.propTypes=u;let d=c},44233:(e,t,r)=>{e.exports=r(85306)},44696:(e,t,r)=>{"use strict";r.d(t,{A:()=>a});let n=r(82015).createContext(null);n.displayName="NavbarContext";let a=n},45061:(e,t)=>{"use strict";function r(e){return"object"==typeof e&&null!==e&&"digest"in e&&e.digest===n}Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{isHangingPromiseRejectionError:function(){return r},makeHangingPromise:function(){return i}});let n="HANGING_PROMISE_REJECTION";class a extends Error{constructor(e){super(`During prerendering, ${e} rejects when the prerender is complete. Typically these errors are handled by React but if you move ${e} to a different context by using \`setTimeout\`, \`after\`, or similar functions you may observe this error and you should handle it in that context.`),this.expression=e,this.digest=n}}let o=new WeakMap;function i(e,t){if(e.aborted)return Promise.reject(new a(t));{let r=new Promise((r,n)=>{let i=n.bind(null,new a(t)),l=o.get(e);if(l)l.push(i);else{let t=[i];o.set(e,t),e.addEventListener("abort",()=>{for(let e=0;e<t.length;e++)t[e]()},{once:!0})}});return r.catch(l),r}}function l(){}},45975:(e,t,r)=>{"use strict";var n=r(92921);t.__esModule=!0,t.default=void 0;var a=n(r(3892)),o=function(e,t){if(e&&e.__esModule)return e;if(null===e||"object"!=typeof e&&"function"!=typeof e)return{default:e};var r=s(t);if(r&&r.has(e))return r.get(e);var n={__proto__:null},a=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var o in e)if("default"!==o&&Object.prototype.hasOwnProperty.call(e,o)){var i=a?Object.getOwnPropertyDescriptor(e,o):null;i&&(i.get||i.set)?Object.defineProperty(n,o,i):n[o]=e[o]}return n.default=e,r&&r.set(e,n),n}(r(82015)),i=r(11940),l=r(8732);function s(e){if("function"!=typeof WeakMap)return null;var t=new WeakMap,r=new WeakMap;return(s=function(e){return e?r:t})(e)}let u=o.forwardRef(({bsPrefix:e,className:t,contentClassName:r,centered:n,size:o,fullscreen:s,children:u,scrollable:c,...d},f)=>{e=(0,i.useBootstrapPrefix)(e,"modal");let p=`${e}-dialog`,h="string"==typeof s?`${e}-fullscreen-${s}`:`${e}-fullscreen`;return(0,l.jsx)("div",{...d,ref:f,className:(0,a.default)(p,t,o&&`${e}-${o}`,n&&`${p}-centered`,c&&`${p}-scrollable`,s&&h),children:(0,l.jsx)("div",{className:(0,a.default)(`${e}-content`,r),children:u})})});u.displayName="ModalDialog",t.default=u,e.exports=t.default},46689:(e,t,r)=>{"use strict";r.d(t,{A:()=>c});var n=r(82015),a=r(81895),o=r.n(a),i=r(73087),l=r(43601),s=r(8732);let u=n.forwardRef(({closeLabel:e="Close",closeVariant:t,closeButton:r=!1,onHide:a,children:u,...c},d)=>{let f=(0,n.useContext)(l.A),p=o()(()=>{null==f||f.onHide(),null==a||a()});return(0,s.jsxs)("div",{ref:d,...c,children:[u,r&&(0,s.jsx)(i.A,{"aria-label":e,variant:t,onClick:p})]})});u.displayName="AbstractModalHeader";let c=u},49182:e=>{e.exports={root:"tweet-link_root__4EzRS"}},49481:(e,t,r)=>{"use strict";r.d(t,{A:()=>u});var n=r(3892),a=r.n(n),o=r(82015),i=r(80739),l=r(8732);let s=o.forwardRef((e,t)=>{let[{className:r,...n},{as:o="div",bsPrefix:s,spans:u}]=function({as:e,bsPrefix:t,className:r,...n}){t=(0,i.oU)(t,"col");let o=(0,i.gy)(),l=(0,i.Jm)(),s=[],u=[];return o.forEach(e=>{let r,a,o,i=n[e];delete n[e],"object"==typeof i&&null!=i?{span:r,offset:a,order:o}=i:r=i;let c=e!==l?`-${e}`:"";r&&s.push(!0===r?`${t}${c}`:`${t}${c}-${r}`),null!=o&&u.push(`order${c}-${o}`),null!=a&&u.push(`offset${c}-${a}`)}),[{...n,className:a()(r,...s,...u)},{as:e,bsPrefix:t,spans:s}]}(e);return(0,l.jsx)(o,{...n,ref:t,className:a()(r,!u.length&&s)})});s.displayName="Col";let u=s},49542:(e,t,r)=>{"use strict";function n(e){e.offsetHeight}r.d(t,{A:()=>n})},50262:e=>{e.exports={root:"tweet-not-found_root__KQedq"}},51484:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{default:function(){return g},handleClientScriptLoad:function(){return m},initScriptLoader:function(){return _}});let n=r(87020),a=r(3147),o=r(8732),i=n._(r(22326)),l=a._(r(82015)),s=r(1523),u=r(98941),c=r(84841),d=new Map,f=new Set,p=e=>{if(i.default.preinit)return void e.forEach(e=>{i.default.preinit(e,{as:"style"})})},h=e=>{let{src:t,id:r,onLoad:n=()=>{},onReady:a=null,dangerouslySetInnerHTML:o,children:i="",strategy:l="afterInteractive",onError:s,stylesheets:c}=e,h=r||t;if(h&&f.has(h))return;if(d.has(t)){f.add(h),d.get(t).then(n,s);return}let m=()=>{a&&a(),f.add(h)},_=document.createElement("script"),y=new Promise((e,t)=>{_.addEventListener("load",function(t){e(),n&&n.call(this,t),m()}),_.addEventListener("error",function(e){t(e)})}).catch(function(e){s&&s(e)});o?(_.innerHTML=o.__html||"",m()):i?(_.textContent="string"==typeof i?i:Array.isArray(i)?i.join(""):"",m()):t&&(_.src=t,d.set(t,y)),(0,u.setAttributesFromProps)(_,e),"worker"===l&&_.setAttribute("type","text/partytown"),_.setAttribute("data-nscript",l),c&&p(c),document.body.appendChild(_)};function m(e){let{strategy:t="afterInteractive"}=e;"lazyOnload"===t?window.addEventListener("load",()=>{(0,c.requestIdleCallback)(()=>h(e))}):h(e)}function _(e){e.forEach(m),[...document.querySelectorAll('[data-nscript="beforeInteractive"]'),...document.querySelectorAll('[data-nscript="beforePageRender"]')].forEach(e=>{let t=e.id||e.getAttribute("src");f.add(t)})}function y(e){let{id:t,src:r="",onLoad:n=()=>{},onReady:a=null,strategy:u="afterInteractive",onError:d,stylesheets:p,...m}=e,{updateScripts:_,scripts:y,getIsSsr:g,appDir:v,nonce:b}=(0,l.useContext)(s.HeadManagerContext),x=(0,l.useRef)(!1);(0,l.useEffect)(()=>{let e=t||r;x.current||(a&&e&&f.has(e)&&a(),x.current=!0)},[a,t,r]);let E=(0,l.useRef)(!1);if((0,l.useEffect)(()=>{if(!E.current){if("afterInteractive"===u)h(e);else"lazyOnload"===u&&("complete"===document.readyState?(0,c.requestIdleCallback)(()=>h(e)):window.addEventListener("load",()=>{(0,c.requestIdleCallback)(()=>h(e))}));E.current=!0}},[e,u]),("beforeInteractive"===u||"worker"===u)&&(_?(y[u]=(y[u]||[]).concat([{id:t,src:r,onLoad:n,onReady:a,onError:d,...m}]),_(y)):g&&g()?f.add(t||r):g&&!g()&&h(e)),v){if(p&&p.forEach(e=>{i.default.preinit(e,{as:"style"})}),"beforeInteractive"===u)if(!r)return m.dangerouslySetInnerHTML&&(m.children=m.dangerouslySetInnerHTML.__html,delete m.dangerouslySetInnerHTML),(0,o.jsx)("script",{nonce:b,dangerouslySetInnerHTML:{__html:"(self.__next_s=self.__next_s||[]).push("+JSON.stringify([0,{...m,id:t}])+")"}});else return i.default.preload(r,m.integrity?{as:"script",integrity:m.integrity,nonce:b,crossOrigin:m.crossOrigin}:{as:"script",nonce:b,crossOrigin:m.crossOrigin}),(0,o.jsx)("script",{nonce:b,dangerouslySetInnerHTML:{__html:"(self.__next_s=self.__next_s||[]).push("+JSON.stringify([r,{...m,id:t}])+")"}});"afterInteractive"===u&&r&&i.default.preload(r,m.integrity?{as:"script",integrity:m.integrity,nonce:b,crossOrigin:m.crossOrigin}:{as:"script",nonce:b,crossOrigin:m.crossOrigin})}return null}Object.defineProperty(y,"__nextScript",{value:!0});let g=y;("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},52088:(e,t,r)=>{"use strict";e.exports=r(63885).vendored.contexts.RouterContext},52260:(e,t,r)=>{"use strict";function n(e){if("function"!=typeof WeakMap)return null;var t=new WeakMap,r=new WeakMap;return(n=function(e){return e?r:t})(e)}t.__esModule=!0,t.default=void 0,t.default=(function(e,t){if(e&&e.__esModule)return e;if(null===e||"object"!=typeof e&&"function"!=typeof e)return{default:e};var r=n(t);if(r&&r.has(e))return r.get(e);var a={__proto__:null},o=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var i in e)if("default"!==i&&Object.prototype.hasOwnProperty.call(e,i)){var l=o?Object.getOwnPropertyDescriptor(e,i):null;l&&(l.get||l.set)?Object.defineProperty(a,i,l):a[i]=e[i]}return a.default=e,r&&r.set(e,a),a})(r(82015)).createContext({onHide(){}}),e.exports=t.default},52755:(e,t,r)=>{"use strict";r.d(t,{A:()=>c});var n=r(3892),a=r.n(n),o=r(82015),i=r(24132),l=r(80739),s=r(8732);let u=o.forwardRef(({id:e,bsPrefix:t,className:r,type:n="checkbox",isValid:u=!1,isInvalid:c=!1,as:d="input",...f},p)=>{let{controlId:h}=(0,o.useContext)(i.A);return t=(0,l.oU)(t,"form-check-input"),(0,s.jsx)(d,{...f,ref:p,type:n,id:e||h,className:a()(r,t,u&&"is-valid",c&&"is-invalid")})});u.displayName="FormCheckInput";let c=u},53484:e=>{(()=>{"use strict";"undefined"!=typeof __nccwpck_require__&&(__nccwpck_require__.ab=__dirname+"/");var t={};(()=>{t.parse=function(t,r){if("string"!=typeof t)throw TypeError("argument str must be a string");for(var a={},o=t.split(n),i=(r||{}).decode||e,l=0;l<o.length;l++){var s=o[l],u=s.indexOf("=");if(!(u<0)){var c=s.substr(0,u).trim(),d=s.substr(++u,s.length).trim();'"'==d[0]&&(d=d.slice(1,-1)),void 0==a[c]&&(a[c]=function(e,t){try{return t(e)}catch(t){return e}}(d,i))}}return a},t.serialize=function(e,t,n){var o=n||{},i=o.encode||r;if("function"!=typeof i)throw TypeError("option encode is invalid");if(!a.test(e))throw TypeError("argument name is invalid");var l=i(t);if(l&&!a.test(l))throw TypeError("argument val is invalid");var s=e+"="+l;if(null!=o.maxAge){var u=o.maxAge-0;if(isNaN(u)||!isFinite(u))throw TypeError("option maxAge is invalid");s+="; Max-Age="+Math.floor(u)}if(o.domain){if(!a.test(o.domain))throw TypeError("option domain is invalid");s+="; Domain="+o.domain}if(o.path){if(!a.test(o.path))throw TypeError("option path is invalid");s+="; Path="+o.path}if(o.expires){if("function"!=typeof o.expires.toUTCString)throw TypeError("option expires is invalid");s+="; Expires="+o.expires.toUTCString()}if(o.httpOnly&&(s+="; HttpOnly"),o.secure&&(s+="; Secure"),o.sameSite)switch("string"==typeof o.sameSite?o.sameSite.toLowerCase():o.sameSite){case!0:case"strict":s+="; SameSite=Strict";break;case"lax":s+="; SameSite=Lax";break;case"none":s+="; SameSite=None";break;default:throw TypeError("option sameSite is invalid")}return s};var e=decodeURIComponent,r=encodeURIComponent,n=/; */,a=/^[\u0009\u0020-\u007e\u0080-\u00ff]+$/})(),e.exports=t})()},53786:(e,t,r)=>{"use strict";var n=r(92921);t.__esModule=!0,t.default=void 0;var a=n(r(3892)),o=function(e,t){if(e&&e.__esModule)return e;if(null===e||"object"!=typeof e&&"function"!=typeof e)return{default:e};var r=f(t);if(r&&r.has(e))return r.get(e);var n={__proto__:null},a=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var o in e)if("default"!==o&&Object.prototype.hasOwnProperty.call(e,o)){var i=a?Object.getOwnPropertyDescriptor(e,o):null;i&&(i.get||i.set)?Object.defineProperty(n,o,i):n[o]=e[o]}return n.default=e,r&&r.set(e,n),n}(r(82015)),i=r(1919),l=r(1680),s=n(r(80732)),u=n(r(22199)),c=n(r(93998)),d=r(8732);function f(e){if("function"!=typeof WeakMap)return null;var t=new WeakMap,r=new WeakMap;return(f=function(e){return e?r:t})(e)}let p={[i.ENTERING]:"show",[i.ENTERED]:"show"},h=o.forwardRef(({className:e,children:t,transitionClasses:r={},onEnter:n,...i},f)=>{let h={in:!1,timeout:300,mountOnEnter:!1,unmountOnExit:!1,appear:!1,...i},m=(0,o.useCallback)((e,t)=>{(0,u.default)(e),null==n||n(e,t)},[n]);return(0,d.jsx)(c.default,{ref:f,addEndListener:s.default,...h,onEnter:m,childRef:(0,l.getChildRef)(t),children:(n,i)=>o.cloneElement(t,{...i,className:(0,a.default)("fade",e,t.props.className,p[n],r[n])})})});h.displayName="Fade",t.default=h,e.exports=t.default},55786:()=>{},56152:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"useIntersection",{enumerable:!0,get:function(){return s}});let n=r(82015),a=r(84841),o="function"==typeof IntersectionObserver,i=new Map,l=[];function s(e){let{rootRef:t,rootMargin:r,disabled:s}=e,u=s||!o,[c,d]=(0,n.useState)(!1),f=(0,n.useRef)(null),p=(0,n.useCallback)(e=>{f.current=e},[]);return(0,n.useEffect)(()=>{if(o){if(u||c)return;let e=f.current;if(e&&e.tagName)return function(e,t,r){let{id:n,observer:a,elements:o}=function(e){let t,r={root:e.root||null,margin:e.rootMargin||""},n=l.find(e=>e.root===r.root&&e.margin===r.margin);if(n&&(t=i.get(n)))return t;let a=new Map;return t={id:r,observer:new IntersectionObserver(e=>{e.forEach(e=>{let t=a.get(e.target),r=e.isIntersecting||e.intersectionRatio>0;t&&r&&t(r)})},e),elements:a},l.push(r),i.set(r,t),t}(r);return o.set(e,t),a.observe(e),function(){if(o.delete(e),a.unobserve(e),0===o.size){a.disconnect(),i.delete(n);let e=l.findIndex(e=>e.root===n.root&&e.margin===n.margin);e>-1&&l.splice(e,1)}}}(e,e=>e&&d(e),{root:null==t?void 0:t.current,rootMargin:r})}else if(!c){let e=(0,a.requestIdleCallback)(()=>d(!0));return()=>(0,a.cancelIdleCallback)(e)}},[u,r,t,c,f.current]),[p,c,(0,n.useCallback)(()=>{d(!1)},[])]}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},56231:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"resolveHref",{enumerable:!0,get:function(){return d}});let n=r(87536),a=r(32848),o=r(68410),i=r(54718),l=r(37779),s=r(60651),u=r(66275),c=r(3563);function d(e,t,r){let d,f="string"==typeof t?t:(0,a.formatWithValidation)(t),p=f.match(/^[a-zA-Z]{1,}:\/\//),h=p?f.slice(p[0].length):f;if((h.split("?",1)[0]||"").match(/(\/\/|\\)/)){console.error("Invalid href '"+f+"' passed to next/router in page: '"+e.pathname+"'. Repeated forward-slashes (//) or backslashes \\ are not valid in the href.");let t=(0,i.normalizeRepeatedSlashes)(h);f=(p?p[0]:"")+t}if(!(0,s.isLocalURL)(f))return r?[f]:f;try{d=new URL(f.startsWith("#")?e.asPath:e.pathname,"http://n")}catch(e){d=new URL("/","http://n")}try{let e=new URL(f,d);e.pathname=(0,l.normalizePathTrailingSlash)(e.pathname);let t="";if((0,u.isDynamicRoute)(e.pathname)&&e.searchParams&&r){let r=(0,n.searchParamsToUrlQuery)(e.searchParams),{result:i,params:l}=(0,c.interpolateAs)(e.pathname,e.pathname,r);i&&(t=(0,a.formatWithValidation)({pathname:i,hash:e.hash,query:(0,o.omit)(r,l)}))}let i=e.origin===d.origin?e.href.slice(e.origin.length):e.href;return r?[i,t||i]:i}catch(e){return r?[f]:f}}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},58246:(e,t,r)=>{"use strict";let n;var a=r(92921);t.__esModule=!0,t.default=void 0,t.getSharedManager=function(e){return n||(n=new d(e)),n};var o=a(r(29628)),i=a(r(99460)),l=a(r(81521)),s=a(r(40051)),u=a(r(74987));let c={FIXED_CONTENT:".fixed-top, .fixed-bottom, .is-fixed, .sticky-top",STICKY_CONTENT:".sticky-top",NAVBAR_TOGGLER:".navbar-toggler"};class d extends u.default{adjustAndStore(e,t,r){let n=t.style[e];t.dataset[e]=n,(0,i.default)(t,{[e]:`${parseFloat((0,i.default)(t,e))+r}px`})}restore(e,t){let r=t.dataset[e];void 0!==r&&(delete t.dataset[e],(0,i.default)(t,{[e]:r}))}setContainerStyle(e){super.setContainerStyle(e);let t=this.getElement();if((0,o.default)(t,"modal-open"),!e.scrollBarWidth)return;let r=this.isRTL?"paddingLeft":"paddingRight",n=this.isRTL?"marginLeft":"marginRight";(0,l.default)(t,c.FIXED_CONTENT).forEach(t=>this.adjustAndStore(r,t,e.scrollBarWidth)),(0,l.default)(t,c.STICKY_CONTENT).forEach(t=>this.adjustAndStore(n,t,-e.scrollBarWidth)),(0,l.default)(t,c.NAVBAR_TOGGLER).forEach(t=>this.adjustAndStore(n,t,e.scrollBarWidth))}removeContainerStyle(e){super.removeContainerStyle(e);let t=this.getElement();(0,s.default)(t,"modal-open");let r=this.isRTL?"paddingLeft":"paddingRight",n=this.isRTL?"marginLeft":"marginRight";(0,l.default)(t,c.FIXED_CONTENT).forEach(e=>this.restore(r,e)),(0,l.default)(t,c.STICKY_CONTENT).forEach(e=>this.restore(n,e)),(0,l.default)(t,c.NAVBAR_TOGGLER).forEach(e=>this.restore(n,e))}}t.default=d},58562:(e,t,r)=>{"use strict";r.d(t,{Tj:()=>a,jJ:()=>o,mf:()=>i});var n=r(82015);function a(e,t){let r=0;return n.Children.map(e,e=>n.isValidElement(e)?t(e,r++):e)}function o(e,t){let r=0;n.Children.forEach(e,e=>{n.isValidElement(e)&&t(e,r++)})}function i(e,t){return n.Children.toArray(e).some(e=>n.isValidElement(e)&&e.type===t)}},58900:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"escapeStringRegexp",{enumerable:!0,get:function(){return a}});let r=/[|\\{}()[\]^$+*?.-]/,n=/[|\\{}()[\]^$+*?.-]/g;function a(e){return r.test(e)?e.replace(n,"\\$&"):e}},58992:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"addPathSuffix",{enumerable:!0,get:function(){return a}});let n=r(86290);function a(e,t){if(!e.startsWith("/")||!t)return e;let{pathname:r,query:a,hash:o}=(0,n.parsePath)(e);return""+r+t+a+o}},59039:e=>{e.exports={root:"tweet-container_root__0rJLq",article:"tweet-container_article__0ERPK"}},59281:(e,t,r)=>{"use strict";e.exports=r(63885).vendored.contexts.ServerInsertedHtml},59549:(e,t,r)=>{"use strict";r.d(t,{A:()=>N});var n=r(3892),a=r.n(n),o=r(29825),i=r.n(o),l=r(82015),s=r(44080),u=r(52755),c=r(24132),d=r(80739),f=r(8732);let p=l.forwardRef(({bsPrefix:e,className:t,htmlFor:r,...n},o)=>{let{controlId:i}=(0,l.useContext)(c.A);return e=(0,d.oU)(e,"form-check-label"),(0,f.jsx)("label",{...n,ref:o,htmlFor:r||i,className:a()(t,e)})});p.displayName="FormCheckLabel";var h=r(58562);let m=l.forwardRef(({id:e,bsPrefix:t,bsSwitchPrefix:r,inline:n=!1,reverse:o=!1,disabled:i=!1,isValid:m=!1,isInvalid:_=!1,feedbackTooltip:y=!1,feedback:g,feedbackType:v,className:b,style:x,title:E="",type:j="checkbox",label:w,children:R,as:P="input",...O},T)=>{t=(0,d.oU)(t,"form-check"),r=(0,d.oU)(r,"form-switch");let{controlId:N}=(0,l.useContext)(c.A),A=(0,l.useMemo)(()=>({controlId:e||N}),[N,e]),S=!R&&null!=w&&!1!==w||(0,h.mf)(R,p),C=(0,f.jsx)(u.A,{...O,type:"switch"===j?"checkbox":j,ref:T,isValid:m,isInvalid:_,disabled:i,as:P});return(0,f.jsx)(c.A.Provider,{value:A,children:(0,f.jsx)("div",{style:x,className:a()(b,S&&t,n&&`${t}-inline`,o&&`${t}-reverse`,"switch"===j&&r),children:R||(0,f.jsxs)(f.Fragment,{children:[C,S&&(0,f.jsx)(p,{title:E,children:w}),g&&(0,f.jsx)(s.A,{type:v,tooltip:y,children:g})]})})})});m.displayName="FormCheck";let _=Object.assign(m,{Input:u.A,Label:p});var y=r(84517);let g=l.forwardRef(({className:e,bsPrefix:t,as:r="div",...n},o)=>(t=(0,d.oU)(t,"form-floating"),(0,f.jsx)(r,{ref:o,className:a()(e,t),...n})));g.displayName="FormFloating";var v=r(98132),b=r(89555);let x=l.forwardRef(({bsPrefix:e,className:t,id:r,...n},o)=>{let{controlId:i}=(0,l.useContext)(c.A);return e=(0,d.oU)(e,"form-range"),(0,f.jsx)("input",{...n,type:"range",ref:o,className:a()(t,e),id:r||i})});x.displayName="FormRange";let E=l.forwardRef(({bsPrefix:e,size:t,htmlSize:r,className:n,isValid:o=!1,isInvalid:i=!1,id:s,...u},p)=>{let{controlId:h}=(0,l.useContext)(c.A);return e=(0,d.oU)(e,"form-select"),(0,f.jsx)("select",{...u,size:r,ref:p,className:a()(n,e,t&&`${e}-${t}`,o&&"is-valid",i&&"is-invalid"),id:s||h})});E.displayName="FormSelect";let j=l.forwardRef(({bsPrefix:e,className:t,as:r="small",muted:n,...o},i)=>(e=(0,d.oU)(e,"form-text"),(0,f.jsx)(r,{...o,ref:i,className:a()(t,e,n&&"text-muted")})));j.displayName="FormText";let w=l.forwardRef((e,t)=>(0,f.jsx)(_,{...e,ref:t,type:"switch"}));w.displayName="Switch";let R=Object.assign(w,{Input:_.Input,Label:_.Label}),P=l.forwardRef(({bsPrefix:e,className:t,children:r,controlId:n,label:o,...i},l)=>(e=(0,d.oU)(e,"form-floating"),(0,f.jsxs)(v.A,{ref:l,className:a()(t,e),controlId:n,...i,children:[r,(0,f.jsx)("label",{htmlFor:n,children:o})]})));P.displayName="FloatingLabel";let O={_ref:i().any,validated:i().bool,as:i().elementType},T=l.forwardRef(({className:e,validated:t,as:r="form",...n},o)=>(0,f.jsx)(r,{...n,ref:o,className:a()(e,t&&"was-validated")}));T.displayName="Form",T.propTypes=O;let N=Object.assign(T,{Group:v.A,Control:y.A,Floating:g,Check:_,Switch:R,Label:b.A,Text:j,Range:x,Select:E,FloatingLabel:P})},59690:(e,t,r)=>{"use strict";var n=r(92921);t.__esModule=!0,t.default=void 0;var a=function(e,t){if(e&&e.__esModule)return e;if(null===e||"object"!=typeof e&&"function"!=typeof e)return{default:e};var r=u(t);if(r&&r.has(e))return r.get(e);var n={__proto__:null},a=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var o in e)if("default"!==o&&Object.prototype.hasOwnProperty.call(e,o)){var i=a?Object.getOwnPropertyDescriptor(e,o):null;i&&(i.get||i.set)?Object.defineProperty(n,o,i):n[o]=e[o]}return n.default=e,r&&r.set(e,n),n}(r(82015)),o=n(r(81895)),i=n(r(7408)),l=n(r(52260)),s=r(8732);function u(e){if("function"!=typeof WeakMap)return null;var t=new WeakMap,r=new WeakMap;return(u=function(e){return e?r:t})(e)}let c=a.forwardRef(({closeLabel:e="Close",closeVariant:t,closeButton:r=!1,onHide:n,children:u,...c},d)=>{let f=(0,a.useContext)(l.default),p=(0,o.default)(()=>{null==f||f.onHide(),null==n||n()});return(0,s.jsxs)("div",{ref:d,...c,children:[u,r&&(0,s.jsx)(i.default,{"aria-label":e,variant:t,onClick:p})]})});c.displayName="AbstractModalHeader",t.default=c,e.exports=t.default},59705:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"getNextPathnameInfo",{enumerable:!0,get:function(){return i}});let n=r(92746),a=r(88156),o=r(69596);function i(e,t){var r,i;let{basePath:l,i18n:s,trailingSlash:u}=null!=(r=t.nextConfig)?r:{},c={pathname:e,trailingSlash:"/"!==e?e.endsWith("/"):u};l&&(0,o.pathHasPrefix)(c.pathname,l)&&(c.pathname=(0,a.removePathPrefix)(c.pathname,l),c.basePath=l);let d=c.pathname;if(c.pathname.startsWith("/_next/data/")&&c.pathname.endsWith(".json")){let e=c.pathname.replace(/^\/_next\/data\//,"").replace(/\.json$/,"").split("/");c.buildId=e[0],d="index"!==e[1]?"/"+e.slice(1).join("/"):"/",!0===t.parseData&&(c.pathname=d)}if(s){let e=t.i18nProvider?t.i18nProvider.analyze(c.pathname):(0,n.normalizeLocalePath)(c.pathname,s.locales);c.locale=e.detectedLocale,c.pathname=null!=(i=e.pathname)?i:c.pathname,!e.detectedLocale&&c.buildId&&(e=t.i18nProvider?t.i18nProvider.analyze(d):(0,n.normalizeLocalePath)(d,s.locales)).detectedLocale&&(c.locale=e.detectedLocale)}return c}},60031:(e,t)=>{"use strict";function r(e,t,r){if(e)for(let o of(r&&(r=r.toLowerCase()),e)){var n,a;if(t===(null==(n=o.domain)?void 0:n.split(":",1)[0].toLowerCase())||r===o.defaultLocale.toLowerCase()||(null==(a=o.locales)?void 0:a.some(e=>e.toLowerCase()===r)))return o}}Object.defineProperty(t,"C",{enumerable:!0,get:function(){return r}})},60651:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"isLocalURL",{enumerable:!0,get:function(){return o}});let n=r(54718),a=r(20617);function o(e){if(!(0,n.isAbsoluteUrl)(e))return!0;try{let t=(0,n.getLocationOrigin)(),r=new URL(e,t);return r.origin===t&&(0,a.hasBasePath)(r.pathname)}catch(e){return!1}}},60889:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"isNextRouterError",{enumerable:!0,get:function(){return o}});let n=r(82323),a=r(18137);function o(e){return(0,a.isRedirectError)(e)||(0,n.isHTTPAccessFallbackError)(e)}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},61219:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"BloomFilter",{enumerable:!0,get:function(){return n}});class n{static from(e,t){void 0===t&&(t=1e-4);let r=new n(e.length,t);for(let t of e)r.add(t);return r}export(){let e={numItems:this.numItems,errorRate:this.errorRate,numBits:this.numBits,numHashes:this.numHashes,bitArray:this.bitArray};if(this.errorRate<1e-4){let t=JSON.stringify(e),n=r(78550).sync(t);n>1024&&console.warn("Creating filter with error rate less than 0.1% (0.001) can increase the size dramatically proceed with caution. Received error rate "+this.errorRate+" resulted in size "+t.length+" bytes, "+n+" bytes (gzip)")}return e}import(e){this.numItems=e.numItems,this.errorRate=e.errorRate,this.numBits=e.numBits,this.numHashes=e.numHashes,this.bitArray=e.bitArray}add(e){this.getHashValues(e).forEach(e=>{this.bitArray[e]=1})}contains(e){return this.getHashValues(e).every(e=>this.bitArray[e])}getHashValues(e){let t=[];for(let r=1;r<=this.numHashes;r++){let n=function(e){let t=0;for(let r=0;r<e.length;r++)t=Math.imul(t^e.charCodeAt(r),0x5bd1e995),t^=t>>>13,t=Math.imul(t,0x5bd1e995);return t>>>0}(""+e+r)%this.numBits;t.push(n)}return t}constructor(e,t=1e-4){this.numItems=e,this.errorRate=t,this.numBits=Math.ceil(-(e*Math.log(t))/(Math.log(2)*Math.log(2))),this.numHashes=Math.ceil(this.numBits/e*Math.log(2)),this.bitArray=Array(this.numBits).fill(0)}}},62140:(e,t)=>{"use strict";function r(e,t){if(void 0===t&&(t={}),t.onlyHashChange)return void e();let r=document.documentElement,n=r.style.scrollBehavior;r.style.scrollBehavior="auto",t.dontForceLayout||r.getClientRects(),e(),r.style.scrollBehavior=n}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"handleSmoothScroll",{enumerable:!0,get:function(){return r}})},63241:(e,t,r)=>{"use strict";r.d(t,{A:()=>C});var n=r(60560),a=r.n(n),o=r(29825),i=r.n(o),l=r(82015),s=r(94947),u=r.n(s);r(26324);var c=r(14332),d=r(37766),f=r.n(d),p=r(1680),h=r(3892),m=r.n(h),_=r(9653),y=r.n(_),g=r(81895),v=r.n(g),b=r(98320),x=r.n(b),E=r(29841),j=r.n(E),w=r(80739),R=r(81181),P=r(23778),O=r(19799),T=r(2041),N=r(8732);let A=l.forwardRef(({children:e,transition:t=O.A,popperConfig:r={},rootClose:n=!1,placement:a="top",show:o=!1,...i},s)=>{let u=(0,l.useRef)({}),[c,d]=(0,l.useState)(null),[p,h]=function(e){let t=(0,l.useRef)(null),r=(0,w.oU)(void 0,"popover"),n=(0,w.oU)(void 0,"tooltip"),a=(0,l.useMemo)(()=>({name:"offset",options:{offset:()=>{if(e)return e;if(t.current){if(j()(t.current,r))return R.A.POPPER_OFFSET;if(j()(t.current,n))return P.A.TOOLTIP_OFFSET}return[0,0]}}}),[e,r,n]);return[t,[a]]}(i.offset),_=f()(s,p),g=!0===t?O.A:t||void 0,b=v()(e=>{d(e),null==r||null==r.onFirstUpdate||r.onFirstUpdate(e)});return x()(()=>{c&&i.target&&(null==u.current.scheduleUpdate||u.current.scheduleUpdate())},[c,i.target]),(0,l.useEffect)(()=>{o||d(null)},[o]),(0,N.jsx)(y(),{...i,ref:_,popperConfig:{...r,modifiers:h.concat(r.modifiers||[]),onFirstUpdate:b},transition:g,rootClose:n,placement:a,show:o,children:(n,{arrowProps:a,popper:o,show:i})=>{var s;!function(e,t){let{ref:r}=e,{ref:n}=t;e.ref=r.__wrapped||(r.__wrapped=e=>r((0,T.A)(e))),t.ref=n.__wrapped||(n.__wrapped=e=>n((0,T.A)(e)))}(n,a);let d=null==o?void 0:o.placement,f=Object.assign(u.current,{state:null==o?void 0:o.state,scheduleUpdate:null==o?void 0:o.update,placement:d,outOfBoundaries:(null==o||null==(s=o.state)||null==(s=s.modifiersData.hide)?void 0:s.isReferenceHidden)||!1,strategy:r.strategy}),p=!!c;return"function"==typeof e?e({...n,placement:d,show:i,...!t&&i&&{className:"show"},popper:f,arrowProps:a,hasDoneInitialMeasure:p}):l.cloneElement(e,{...n,placement:d,arrowProps:a,popper:f,hasDoneInitialMeasure:p,className:m()(e.props.className,!t&&i&&"show"),style:{...e.props.style,...n.style}})}})});function S(e,t,r){let[n]=t,o=n.currentTarget,i=n.relatedTarget||n.nativeEvent[r];i&&i===o||a()(o,i)||e(...t)}A.displayName="Overlay",i().oneOf(["click","hover","focus"]);let C=({trigger:e=["hover","focus"],overlay:t,children:r,popperConfig:n={},show:a,defaultShow:o=!1,onToggle:i,delay:s,placement:d,flip:h=d&&-1!==d.indexOf("auto"),...m})=>{let _=(0,l.useRef)(null),y=f()(_,(0,p.getChildRef)(r)),g=u()(),v=(0,l.useRef)(""),[b,x]=(0,c.useUncontrolledProp)(a,o,i),E=function(e){return e&&"object"==typeof e?e:{show:e,hide:e}}(s),{onFocus:j,onBlur:w,onClick:R}="function"!=typeof r?l.Children.only(r).props:{},P=(0,l.useCallback)(()=>{if(g.clear(),v.current="show",!E.show)return void x(!0);g.set(()=>{"show"===v.current&&x(!0)},E.show)},[E.show,x,g]),O=(0,l.useCallback)(()=>{if(g.clear(),v.current="hide",!E.hide)return void x(!1);g.set(()=>{"hide"===v.current&&x(!1)},E.hide)},[E.hide,x,g]),C=(0,l.useCallback)((...e)=>{P(),null==j||j(...e)},[P,j]),M=(0,l.useCallback)((...e)=>{O(),null==w||w(...e)},[O,w]),k=(0,l.useCallback)((...e)=>{x(!b),null==R||R(...e)},[R,x,b]),I=(0,l.useCallback)((...e)=>{S(P,e,"fromElement")},[P]),D=(0,l.useCallback)((...e)=>{S(O,e,"toElement")},[O]),L=null==e?[]:[].concat(e),U={ref:e=>{y((0,T.A)(e))}};return -1!==L.indexOf("click")&&(U.onClick=k),-1!==L.indexOf("focus")&&(U.onFocus=C,U.onBlur=M),-1!==L.indexOf("hover")&&(U.onMouseOver=I,U.onMouseOut=D),(0,N.jsxs)(N.Fragment,{children:["function"==typeof r?r(U):(0,l.cloneElement)(r,U),(0,N.jsx)(A,{...m,show:b,onHide:O,flip:h,placement:d,popperConfig:n,target:_.current,children:t})]})}},63899:(e,t,r)=>{"use strict";r.d(t,{A:()=>R});var n=r(3892),a=r.n(n),o=r(82015),i=r(7374),l=r.n(i),s=r(14332),u=r(81895),c=r.n(u),d=r(72871),f=r(80739),p=r(8732);let h=o.forwardRef(({className:e,bsPrefix:t,as:r="hr",role:n="separator",...o},i)=>(t=(0,f.oU)(t,"dropdown-divider"),(0,p.jsx)(r,{ref:i,className:a()(e,t),role:n,...o})));h.displayName="DropdownDivider";let m=o.forwardRef(({className:e,bsPrefix:t,as:r="div",role:n="heading",...o},i)=>(t=(0,f.oU)(t,"dropdown-header"),(0,p.jsx)(r,{ref:i,className:a()(e,t),role:n,...o})));m.displayName="DropdownHeader";var _=r(28217),y=r(78634),g=r.n(y);let v=o.forwardRef(({bsPrefix:e,className:t,eventKey:r,disabled:n=!1,onClick:o,active:i,as:l=g(),...s},u)=>{let c=(0,f.oU)(e,"dropdown-item"),[d,h]=(0,_.useDropdownItem)({key:r,href:s.href,disabled:n,onClick:o,active:i});return(0,p.jsx)(l,{...s,...d,ref:u,className:a()(t,c,h.isActive&&"active",n&&"disabled")})});v.displayName="DropdownItem";let b=o.forwardRef(({className:e,bsPrefix:t,as:r="span",...n},o)=>(t=(0,f.oU)(t,"dropdown-item-text"),(0,p.jsx)(r,{ref:o,className:a()(e,t),...n})));b.displayName="DropdownItemText";var x=r(2827),E=r(95046),j=r(24765);let w=o.forwardRef((e,t)=>{let{bsPrefix:r,drop:n="down",show:i,className:u,align:h="start",onSelect:m,onToggle:_,focusFirstItemOnShow:y,as:g="div",navbar:v,autoClose:b=!0,...E}=(0,s.useUncontrolled)(e,{show:"onToggle"}),w=(0,o.useContext)(j.A),R=(0,f.oU)(r,"dropdown"),P=(0,f.Wz)(),O=e=>!1===b?"click"===e:"inside"===b?"rootClose"!==e:"outside"!==b||"select"!==e,T=c()((e,t)=>{var r;(null==(r=t.originalEvent)||null==(r=r.target)?void 0:r.classList.contains("dropdown-toggle"))&&"mousedown"===t.source||(t.originalEvent.currentTarget===document&&("keydown"!==t.source||"Escape"===t.originalEvent.key)&&(t.source="rootClose"),O(t.source)&&(null==_||_(e,t)))}),N=(0,x.S)("end"===h,n,P),A=(0,o.useMemo)(()=>({align:h,drop:n,isRTL:P}),[h,n,P]),S={down:R,"down-centered":`${R}-center`,up:"dropup","up-centered":"dropup-center dropup",end:"dropend",start:"dropstart"};return(0,p.jsx)(d.A.Provider,{value:A,children:(0,p.jsx)(l(),{placement:N,show:i,onSelect:m,onToggle:T,focusFirstItemOnShow:y,itemSelector:`.${R}-item:not(.disabled):not(:disabled)`,children:w?E.children:(0,p.jsx)(g,{...E,ref:t,className:a()(u,i&&"show",S[n])})})})});w.displayName="Dropdown";let R=Object.assign(w,{Toggle:E.A,Menu:x.A,Item:v,ItemText:b,Divider:h,Header:m})},64066:e=>{e.exports={root:"tweet-in-reply-to_root__o784R"}},65939:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"parseRelativeUrl",{enumerable:!0,get:function(){return a}}),r(54718);let n=r(87536);function a(e,t,r){void 0===r&&(r=!0);let a=new URL("http://n"),o=t?new URL(t,a):e.startsWith(".")?new URL("http://n"):a,{pathname:i,searchParams:l,search:s,hash:u,href:c,origin:d}=new URL(e,o);if(d!==a.origin)throw Object.defineProperty(Error("invariant: invalid relative URL, router received "+e),"__NEXT_ERROR_CODE",{value:"E159",enumerable:!1,configurable:!0});return{pathname:i,query:r?(0,n.searchParamsToUrlQuery)(l):void 0,search:s,hash:u,href:c.slice(d.length)}}},66174:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"isPostpone",{enumerable:!0,get:function(){return n}});let r=Symbol.for("react.postpone");function n(e){return"object"==typeof e&&null!==e&&e.$$typeof===r}},66933:(e,t,r)=>{"use strict";r.d(t,{A:()=>f});var n=r(82015),a=r.n(n),o=r(1919),i=r.n(o),l=r(37766),s=r.n(l),u=r(2041),c=r(8732);let d=a().forwardRef(({onEnter:e,onEntering:t,onEntered:r,onExit:o,onExiting:l,onExited:d,addEndListener:f,children:p,childRef:h,...m},_)=>{let y=(0,n.useRef)(null),g=s()(y,h),v=e=>{g((0,u.A)(e))},b=e=>t=>{e&&y.current&&e(y.current,t)},x=(0,n.useCallback)(b(e),[e]),E=(0,n.useCallback)(b(t),[t]),j=(0,n.useCallback)(b(r),[r]),w=(0,n.useCallback)(b(o),[o]),R=(0,n.useCallback)(b(l),[l]),P=(0,n.useCallback)(b(d),[d]),O=(0,n.useCallback)(b(f),[f]);return(0,c.jsx)(i(),{ref:_,...m,onEnter:x,onEntered:j,onEntering:E,onExit:w,onExited:P,onExiting:R,addEndListener:O,nodeRef:y,children:"function"==typeof p?(e,t)=>p(e,{...t,ref:v}):a().cloneElement(p,{ref:v})})});d.displayName="TransitionWrapper";let f=d},67750:e=>{e.exports={root:"tweet-media_root__k6gQ2",rounded:"tweet-media_rounded__LgwFx",mediaWrapper:"tweet-media_mediaWrapper__6rfqr",grid2Columns:"tweet-media_grid2Columns__tO2Ea",grid3:"tweet-media_grid3__XbH4s",grid2x2:"tweet-media_grid2x2__Wiunq",mediaContainer:"tweet-media_mediaContainer__rjXGp",mediaLink:"tweet-media_mediaLink__vFkZL",skeleton:"tweet-media_skeleton__qZmSS",image:"tweet-media_image__yoPJg"}},67776:(e,t,r)=>{"use strict";r.d(t,{A:()=>p});var n=r(3892),a=r.n(n),o=r(82015),i=r(78634),l=r.n(i),s=r(25303),u=r(86842),c=r(80739),d=r(8732);let f=o.forwardRef(({bsPrefix:e,className:t,as:r=l(),active:n,eventKey:o,disabled:i=!1,...f},p)=>{e=(0,c.oU)(e,"nav-link");let[h,m]=(0,s.useNavItem)({key:(0,u.makeEventKey)(o,f.href),active:n,disabled:i,...f});return(0,d.jsx)(r,{...f,...h,ref:p,disabled:i,className:a()(t,e,i&&"disabled",m.isActive&&"active")})});f.displayName="NavLink";let p=f},68410:(e,t)=>{"use strict";function r(e,t){let r={};return Object.keys(e).forEach(n=>{t.includes(n)||(r[n]=e[n])}),r}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"omit",{enumerable:!0,get:function(){return r}})},69596:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"pathHasPrefix",{enumerable:!0,get:function(){return a}});let n=r(86290);function a(e,t){if("string"!=typeof e)return!1;let{pathname:r}=(0,n.parsePath)(e);return r===t||r.startsWith(t+"/")}},69871:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"useMergedRef",{enumerable:!0,get:function(){return a}});let n=r(82015);function a(e,t){let r=(0,n.useRef)(null),a=(0,n.useRef)(null);return(0,n.useCallback)(n=>{if(null===n){let e=r.current;e&&(r.current=null,e());let t=a.current;t&&(a.current=null,t())}else e&&(r.current=o(e,n)),t&&(a.current=o(t,n))},[e,t])}function o(e,t){if("function"!=typeof e)return e.current=t,()=>{e.current=null};{let r=e(t);return"function"==typeof r?r:()=>e(null)}}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},70219:(e,t,r)=>{"use strict";var n=r(92921);t.__esModule=!0,t.default=void 0;var a=function(e,t){if(e&&e.__esModule)return e;if(null===e||"object"!=typeof e&&"function"!=typeof e)return{default:e};var r=u(t);if(r&&r.has(e))return r.get(e);var n={__proto__:null},a=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var o in e)if("default"!==o&&Object.prototype.hasOwnProperty.call(e,o)){var i=a?Object.getOwnPropertyDescriptor(e,o):null;i&&(i.get||i.set)?Object.defineProperty(n,o,i):n[o]=e[o]}return n.default=e,r&&r.set(e,n),n}(r(82015)),o=n(r(3892)),i=n(r(73262)),l=r(11940),s=r(8732);function u(e){if("function"!=typeof WeakMap)return null;var t=new WeakMap,r=new WeakMap;return(u=function(e){return e?r:t})(e)}let c=(0,i.default)("h4"),d=a.forwardRef(({className:e,bsPrefix:t,as:r=c,...n},a)=>(t=(0,l.useBootstrapPrefix)(t,"modal-title"),(0,s.jsx)(r,{ref:a,className:(0,o.default)(e,t),...n})));d.displayName="ModalTitle",t.default=d,e.exports=t.default},72871:(e,t,r)=>{"use strict";r.d(t,{A:()=>a});let n=r(82015).createContext({});n.displayName="DropdownContext";let a=n},72889:(e,t,r)=>{"use strict";let n;r.d(t,{A:()=>y,R:()=>_});var a=r(29628),o=r.n(a),i=r(99460),l=r.n(i),s=r(81521),u=r.n(s),c=r(40051),d=r.n(c),f=r(74987),p=r.n(f);let h={FIXED_CONTENT:".fixed-top, .fixed-bottom, .is-fixed, .sticky-top",STICKY_CONTENT:".sticky-top",NAVBAR_TOGGLER:".navbar-toggler"};class m extends p(){adjustAndStore(e,t,r){let n=t.style[e];t.dataset[e]=n,l()(t,{[e]:`${parseFloat(l()(t,e))+r}px`})}restore(e,t){let r=t.dataset[e];void 0!==r&&(delete t.dataset[e],l()(t,{[e]:r}))}setContainerStyle(e){super.setContainerStyle(e);let t=this.getElement();if(o()(t,"modal-open"),!e.scrollBarWidth)return;let r=this.isRTL?"paddingLeft":"paddingRight",n=this.isRTL?"marginLeft":"marginRight";u()(t,h.FIXED_CONTENT).forEach(t=>this.adjustAndStore(r,t,e.scrollBarWidth)),u()(t,h.STICKY_CONTENT).forEach(t=>this.adjustAndStore(n,t,-e.scrollBarWidth)),u()(t,h.NAVBAR_TOGGLER).forEach(t=>this.adjustAndStore(n,t,e.scrollBarWidth))}removeContainerStyle(e){super.removeContainerStyle(e);let t=this.getElement();d()(t,"modal-open");let r=this.isRTL?"paddingLeft":"paddingRight",n=this.isRTL?"marginLeft":"marginRight";u()(t,h.FIXED_CONTENT).forEach(e=>this.restore(r,e)),u()(t,h.STICKY_CONTENT).forEach(e=>this.restore(n,e)),u()(t,h.NAVBAR_TOGGLER).forEach(e=>this.restore(n,e))}}function _(e){return n||(n=new m(e)),n}let y=m},73087:(e,t,r)=>{"use strict";r.d(t,{A:()=>d});var n=r(29825),a=r.n(n),o=r(82015),i=r(3892),l=r.n(i),s=r(8732);let u={"aria-label":a().string,onClick:a().func,variant:a().oneOf(["white"])},c=o.forwardRef(({className:e,variant:t,"aria-label":r="Close",...n},a)=>(0,s.jsx)("button",{ref:a,type:"button",className:l()("btn-close",t&&`btn-close-${t}`,e),"aria-label":r,...n}));c.displayName="CloseButton",c.propTypes=u;let d=c},73262:(e,t,r)=>{"use strict";var n=r(92921);t.__esModule=!0,t.default=void 0;var a=function(e,t){if(e&&e.__esModule)return e;if(null===e||"object"!=typeof e&&"function"!=typeof e)return{default:e};var r=l(t);if(r&&r.has(e))return r.get(e);var n={__proto__:null},a=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var o in e)if("default"!==o&&Object.prototype.hasOwnProperty.call(e,o)){var i=a?Object.getOwnPropertyDescriptor(e,o):null;i&&(i.get||i.set)?Object.defineProperty(n,o,i):n[o]=e[o]}return n.default=e,r&&r.set(e,n),n}(r(82015)),o=n(r(3892)),i=r(8732);function l(e){if("function"!=typeof WeakMap)return null;var t=new WeakMap,r=new WeakMap;return(l=function(e){return e?r:t})(e)}t.default=e=>a.forwardRef((t,r)=>(0,i.jsx)("div",{...t,ref:r,className:(0,o.default)(t.className,e)})),e.exports=t.default},73612:(e,t,r)=>{"use strict";var n=r(92921);t.__esModule=!0,t.default=void 0;var a=n(r(3892)),o=function(e,t){if(e&&e.__esModule)return e;if(null===e||"object"!=typeof e&&"function"!=typeof e)return{default:e};var r=u(t);if(r&&r.has(e))return r.get(e);var n={__proto__:null},a=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var o in e)if("default"!==o&&Object.prototype.hasOwnProperty.call(e,o)){var i=a?Object.getOwnPropertyDescriptor(e,o):null;i&&(i.get||i.set)?Object.defineProperty(n,o,i):n[o]=e[o]}return n.default=e,r&&r.set(e,n),n}(r(82015)),i=r(11940),l=n(r(59690)),s=r(8732);function u(e){if("function"!=typeof WeakMap)return null;var t=new WeakMap,r=new WeakMap;return(u=function(e){return e?r:t})(e)}let c=o.forwardRef(({bsPrefix:e,className:t,closeLabel:r="Close",closeButton:n=!1,...o},u)=>(e=(0,i.useBootstrapPrefix)(e,"modal-header"),(0,s.jsx)(l.default,{ref:u,...o,className:(0,a.default)(t,e),closeLabel:r,closeButton:n})));c.displayName="ModalHeader",t.default=c,e.exports=t.default},73949:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"default",{enumerable:!0,get:function(){return o}}),r(87020);let n=r(8732);r(82015);let a=r(85306);function o(e){function t(t){return(0,n.jsx)(e,{router:(0,a.useRouter)(),...t})}return t.getInitialProps=e.getInitialProps,t.origGetInitialProps=e.origGetInitialProps,t}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},74853:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"bailoutToClientRendering",{enumerable:!0,get:function(){return o}});let n=r(86261),a=r(29294);function o(e){let t=a.workAsyncStorage.getStore();if((null==t||!t.forceStatic)&&(null==t?void 0:t.isStaticGeneration))throw Object.defineProperty(new n.BailoutToCSRError(e),"__NEXT_ERROR_CODE",{value:"E394",enumerable:!1,configurable:!0})}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},75330:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{StaticGenBailoutError:function(){return n},isStaticGenBailoutError:function(){return a}});let r="NEXT_STATIC_GEN_BAILOUT";class n extends Error{constructor(...e){super(...e),this.code=r}}function a(e){return"object"==typeof e&&null!==e&&"code"in e&&e.code===r}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},76614:(e,t,r)=>{"use strict";function n(){throw Object.defineProperty(Error("`unauthorized()` is experimental and only allowed to be used when `experimental.authInterrupts` is enabled."),"__NEXT_ERROR_CODE",{value:"E411",enumerable:!1,configurable:!0})}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"unauthorized",{enumerable:!0,get:function(){return n}}),r(82323).HTTP_ERROR_FALLBACK_ERROR_CODE,("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},77291:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});var n=r(99460),a=r.n(n),o=r(87571),i=r.n(o);function l(e,t){let r=a()(e,t)||"",n=-1===r.indexOf("ms")?1e3:1;return parseFloat(r)*n}function s(e,t){let r=l(e,"transitionDuration"),n=l(e,"transitionDelay"),a=i()(e,r=>{r.target===e&&(a(),t(r))},r+n)}},78219:(e,t,r)=>{"use strict";var n=r(92921);t.__esModule=!0,t.default=void 0;var a=n(r(3892)),o=n(r(12388)),i=n(r(58928)),l=n(r(6009)),s=n(r(123)),u=n(r(57664)),c=n(r(67364)),d=n(r(81895)),f=n(r(37766)),p=n(r(11688)),h=n(r(87571)),m=function(e,t){if(e&&e.__esModule)return e;if(null===e||"object"!=typeof e&&"function"!=typeof e)return{default:e};var r=O(t);if(r&&r.has(e))return r.get(e);var n={__proto__:null},a=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var o in e)if("default"!==o&&Object.prototype.hasOwnProperty.call(e,o)){var i=a?Object.getOwnPropertyDescriptor(e,o):null;i&&(i.get||i.set)?Object.defineProperty(n,o,i):n[o]=e[o]}return n.default=e,r&&r.set(e,n),n}(r(82015)),_=n(r(6952)),y=r(58246),g=n(r(53786)),v=n(r(80643)),b=n(r(52260)),x=n(r(45975)),E=n(r(2118)),j=n(r(73612)),w=n(r(70219)),R=r(11940),P=r(8732);function O(e){if("function"!=typeof WeakMap)return null;var t=new WeakMap,r=new WeakMap;return(O=function(e){return e?r:t})(e)}function T(e){return(0,P.jsx)(g.default,{...e,timeout:null})}function N(e){return(0,P.jsx)(g.default,{...e,timeout:null})}let A=m.forwardRef(({bsPrefix:e,className:t,style:r,dialogClassName:n,contentClassName:g,children:v,dialogAs:E=x.default,"data-bs-theme":j,"aria-labelledby":w,"aria-describedby":O,"aria-label":A,show:S=!1,animation:C=!0,backdrop:M=!0,keyboard:k=!0,onEscapeKeyDown:I,onShow:D,onHide:L,container:U,autoFocus:B=!0,enforceFocus:F=!0,restoreFocus:$=!0,restoreFocusOptions:H,onEntered:W,onExit:X,onExiting:G,onEnter:q,onEntering:z,onExited:V,backdropClassName:K,manager:Y,...Q},J)=>{let[Z,ee]=(0,m.useState)({}),[et,er]=(0,m.useState)(!1),en=(0,m.useRef)(!1),ea=(0,m.useRef)(!1),eo=(0,m.useRef)(null),[ei,el]=(0,c.default)(),es=(0,f.default)(J,el),eu=(0,d.default)(L),ec=(0,R.useIsRTL)();e=(0,R.useBootstrapPrefix)(e,"modal");let ed=(0,m.useMemo)(()=>({onHide:eu}),[eu]);function ef(){return Y||(0,y.getSharedManager)({isRTL:ec})}function ep(e){if(!i.default)return;let t=ef().getScrollbarWidth()>0,r=e.scrollHeight>(0,l.default)(e).documentElement.clientHeight;ee({paddingRight:t&&!r?(0,u.default)():void 0,paddingLeft:!t&&r?(0,u.default)():void 0})}let eh=(0,d.default)(()=>{ei&&ep(ei.dialog)});(0,p.default)(()=>{(0,s.default)(window,"resize",eh),null==eo.current||eo.current()});let em=()=>{en.current=!0},e_=e=>{en.current&&ei&&e.target===ei.dialog&&(ea.current=!0),en.current=!1},ey=()=>{er(!0),eo.current=(0,h.default)(ei.dialog,()=>{er(!1)})},eg=e=>{e.target===e.currentTarget&&ey()},ev=e=>{if("static"===M)return void eg(e);if(ea.current||e.target!==e.currentTarget){ea.current=!1;return}null==L||L()},eb=(0,m.useCallback)(t=>(0,P.jsx)("div",{...t,className:(0,a.default)(`${e}-backdrop`,K,!C&&"show")}),[C,K,e]),ex={...r,...Z};return ex.display="block",(0,P.jsx)(b.default.Provider,{value:ed,children:(0,P.jsx)(_.default,{show:S,ref:es,backdrop:M,container:U,keyboard:!0,autoFocus:B,enforceFocus:F,restoreFocus:$,restoreFocusOptions:H,onEscapeKeyDown:e=>{k?null==I||I(e):(e.preventDefault(),"static"===M&&ey())},onShow:D,onHide:L,onEnter:(e,t)=>{e&&ep(e),null==q||q(e,t)},onEntering:(e,t)=>{null==z||z(e,t),(0,o.default)(window,"resize",eh)},onEntered:W,onExit:e=>{null==eo.current||eo.current(),null==X||X(e)},onExiting:G,onExited:e=>{e&&(e.style.display=""),null==V||V(e),(0,s.default)(window,"resize",eh)},manager:ef(),transition:C?T:void 0,backdropTransition:C?N:void 0,renderBackdrop:eb,renderDialog:r=>(0,P.jsx)("div",{role:"dialog",...r,style:ex,className:(0,a.default)(t,e,et&&`${e}-static`,!C&&"show"),onClick:M?ev:void 0,onMouseUp:e_,"data-bs-theme":j,"aria-label":A,"aria-labelledby":w,"aria-describedby":O,children:(0,P.jsx)(E,{...Q,onMouseDown:em,className:n,contentClassName:g,children:v})})})})});A.displayName="Modal",t.default=Object.assign(A,{Body:v.default,Header:j.default,Title:w.default,Footer:E.default,Dialog:x.default,TRANSITION_DURATION:300,BACKDROP_TRANSITION_DURATION:150}),e.exports=t.default},78550:(e,t,r)=>{(()=>{var t={154:(e,t,r)=>{var n=r(781),a=["write","end","destroy"],o=["resume","pause"],i=["data","close"],l=Array.prototype.slice;function s(e,t){if(e.forEach)return e.forEach(t);for(var r=0;r<e.length;r++)t(e[r],r)}e.exports=function(e,t){var r=new n,u=!1;return s(a,function(t){r[t]=function(){return e[t].apply(e,arguments)}}),s(o,function(e){r[e]=function(){r.emit(e);var n=t[e];if(n)return n.apply(t,arguments);t.emit(e)}}),s(i,function(e){t.on(e,function(){var t=l.call(arguments);t.unshift(e),r.emit.apply(r,t)})}),t.on("end",function(){if(!u){u=!0;var e=l.call(arguments);e.unshift("end"),r.emit.apply(r,e)}}),e.on("drain",function(){r.emit("drain")}),e.on("error",c),t.on("error",c),r.writable=e.writable,r.readable=t.readable,r;function c(e){r.emit("error",e)}}},349:(e,t,r)=>{"use strict";let n=r(147),a=r(781),o=r(796),i=r(154),l=r(530),s=e=>Object.assign({level:9},e);e.exports=(e,t)=>e?l(o.gzip)(e,s(t)).then(e=>e.length).catch(e=>0):Promise.resolve(0),e.exports.sync=(e,t)=>o.gzipSync(e,s(t)).length,e.exports.stream=e=>{let t=new a.PassThrough,r=new a.PassThrough,n=i(t,r),l=0,u=o.createGzip(s(e)).on("data",e=>{l+=e.length}).on("error",()=>{n.gzipSize=0}).on("end",()=>{n.gzipSize=l,n.emit("gzip-size",l),r.end()});return t.pipe(u),t.pipe(r,{end:!1}),n},e.exports.file=(t,r)=>new Promise((a,o)=>{let i=n.createReadStream(t);i.on("error",o);let l=i.pipe(e.exports.stream(r));l.on("error",o),l.on("gzip-size",a)}),e.exports.fileSync=(t,r)=>e.exports.sync(n.readFileSync(t),r)},530:e=>{"use strict";let t=(e,t)=>function(...r){return new t.promiseModule((n,a)=>{t.multiArgs?r.push((...e)=>{t.errorFirst?e[0]?a(e):(e.shift(),n(e)):n(e)}):t.errorFirst?r.push((e,t)=>{e?a(e):n(t)}):r.push(n),e.apply(this,r)})};e.exports=(e,r)=>{let n;r=Object.assign({exclude:[/.+(Sync|Stream)$/],errorFirst:!0,promiseModule:Promise},r);let a=typeof e;if(null===e||"object"!==a&&"function"!==a)throw TypeError(`Expected \`input\` to be a \`Function\` or \`Object\`, got \`${null===e?"null":a}\``);let o=e=>{let t=t=>"string"==typeof t?e===t:t.test(e);return r.include?r.include.some(t):!r.exclude.some(t)};for(let i in n="function"===a?function(...n){return r.excludeMain?e(...n):t(e,r).apply(this,n)}:Object.create(Object.getPrototypeOf(e)),e){let a=e[i];n[i]="function"==typeof a&&o(i)?t(a,r):a}return n}},147:e=>{"use strict";e.exports=r(29021)},781:e=>{"use strict";e.exports=r(27910)},796:e=>{"use strict";e.exports=r(74075)}},n={};function a(e){var r=n[e];if(void 0!==r)return r.exports;var o=n[e]={exports:{}},i=!0;try{t[e](o,o.exports,a),i=!1}finally{i&&delete n[e]}return o.exports}a.ab=__dirname+"/",e.exports=a(349)})()},79007:(e,t)=>{"use strict";function r(){return""}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"getDeploymentIdQueryOrEmptyString",{enumerable:!0,get:function(){return r}})},79193:e=>{e.exports={header:"quoted-tweet-header_header___qrcQ",avatar:"quoted-tweet-header_avatar__lGzrW",avatarSquare:"quoted-tweet-header_avatarSquare__l_eYT",author:"quoted-tweet-header_author__k48VI",authorText:"quoted-tweet-header_authorText__FULly",username:"quoted-tweet-header_username__YLPXR"}},79198:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"unstable_rethrow",{enumerable:!0,get:function(){return n}});let n=r(42998).unstable_rethrow;("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},80643:(e,t,r)=>{"use strict";var n=r(92921);t.__esModule=!0,t.default=void 0;var a=function(e,t){if(e&&e.__esModule)return e;if(null===e||"object"!=typeof e&&"function"!=typeof e)return{default:e};var r=s(t);if(r&&r.has(e))return r.get(e);var n={__proto__:null},a=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var o in e)if("default"!==o&&Object.prototype.hasOwnProperty.call(e,o)){var i=a?Object.getOwnPropertyDescriptor(e,o):null;i&&(i.get||i.set)?Object.defineProperty(n,o,i):n[o]=e[o]}return n.default=e,r&&r.set(e,n),n}(r(82015)),o=n(r(3892)),i=r(11940),l=r(8732);function s(e){if("function"!=typeof WeakMap)return null;var t=new WeakMap,r=new WeakMap;return(s=function(e){return e?r:t})(e)}let u=a.forwardRef(({className:e,bsPrefix:t,as:r="div",...n},a)=>(t=(0,i.useBootstrapPrefix)(t,"modal-body"),(0,l.jsx)(r,{ref:a,className:(0,o.default)(e,t),...n})));u.displayName="ModalBody",t.default=u,e.exports=t.default},80732:(e,t,r)=>{"use strict";var n=r(92921);t.__esModule=!0,t.default=function(e,t){let r=i(e,"transitionDuration"),n=i(e,"transitionDelay"),a=(0,o.default)(e,r=>{r.target===e&&(a(),t(r))},r+n)};var a=n(r(99460)),o=n(r(87571));function i(e,t){let r=(0,a.default)(e,t)||"",n=-1===r.indexOf("ms")?1e3:1;return parseFloat(r)*n}e.exports=t.default},80739:(e,t,r)=>{"use strict";r.d(t,{Jm:()=>u,Wz:()=>c,gy:()=>s,oU:()=>l});var n=r(82015);r(8732);let a=n.createContext({prefixes:{},breakpoints:["xxl","xl","lg","md","sm","xs"],minBreakpoint:"xs"}),{Consumer:o,Provider:i}=a;function l(e,t){let{prefixes:r}=(0,n.useContext)(a);return e||r[t]||t}function s(){let{breakpoints:e}=(0,n.useContext)(a);return e}function u(){let{minBreakpoint:e}=(0,n.useContext)(a);return e}function c(){let{dir:e}=(0,n.useContext)(a);return"rtl"===e}},81181:(e,t,r)=>{"use strict";r.d(t,{A:()=>p});var n=r(3892),a=r.n(n),o=r(82015),i=r(80739),l=r(8732);let s=o.forwardRef(({className:e,bsPrefix:t,as:r="div",...n},o)=>(t=(0,i.oU)(t,"popover-header"),(0,l.jsx)(r,{ref:o,className:a()(e,t),...n})));s.displayName="PopoverHeader";let u=o.forwardRef(({className:e,bsPrefix:t,as:r="div",...n},o)=>(t=(0,i.oU)(t,"popover-body"),(0,l.jsx)(r,{ref:o,className:a()(e,t),...n})));u.displayName="PopoverBody";var c=r(33496),d=r(30905);let f=o.forwardRef(({bsPrefix:e,placement:t="right",className:r,style:n,children:o,body:s,arrowProps:f,hasDoneInitialMeasure:p,popper:h,show:m,..._},y)=>{let g=(0,i.oU)(e,"popover"),v=(0,i.Wz)(),[b]=(null==t?void 0:t.split("-"))||[],x=(0,c.G)(b,v),E=n;return m&&!p&&(E={...n,...(0,d.A)(null==h?void 0:h.strategy)}),(0,l.jsxs)("div",{ref:y,role:"tooltip",style:E,"x-placement":b,className:a()(r,g,b&&`bs-popover-${x}`),..._,children:[(0,l.jsx)("div",{className:"popover-arrow",...f}),s?(0,l.jsx)(u,{children:o}):o]})});f.displayName="Popover";let p=Object.assign(f,{Header:s,Body:u,POPPER_OFFSET:[0,8]})},82323:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{HTTPAccessErrorStatus:function(){return r},HTTP_ERROR_FALLBACK_ERROR_CODE:function(){return a},getAccessFallbackErrorTypeByStatus:function(){return l},getAccessFallbackHTTPStatus:function(){return i},isHTTPAccessFallbackError:function(){return o}});let r={NOT_FOUND:404,FORBIDDEN:403,UNAUTHORIZED:401},n=new Set(Object.values(r)),a="NEXT_HTTP_ERROR_FALLBACK";function o(e){if("object"!=typeof e||null===e||!("digest"in e)||"string"!=typeof e.digest)return!1;let[t,r]=e.digest.split(";");return t===a&&n.has(Number(r))}function i(e){return Number(e.digest.split(";")[1])}function l(e){switch(e){case 401:return"unauthorized";case 403:return"forbidden";case 404:return"not-found";default:return}}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},82417:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"getRouteMatcher",{enumerable:!0,get:function(){return a}});let n=r(54718);function a(e){let{re:t,groups:r}=e;return e=>{let a=t.exec(e);if(!a)return!1;let o=e=>{try{return decodeURIComponent(e)}catch(e){throw Object.defineProperty(new n.DecodeError("failed to decode param"),"__NEXT_ERROR_CODE",{value:"E528",enumerable:!1,configurable:!0})}},i={};for(let[e,t]of Object.entries(r)){let r=a[t.pos];void 0!==r&&(t.repeat?i[e]=r.split("/").map(e=>o(e)):i[e]=o(r))}return i}}},83551:(e,t,r)=>{"use strict";r.d(t,{A:()=>u});var n=r(3892),a=r.n(n),o=r(82015),i=r(80739),l=r(8732);let s=o.forwardRef(({bsPrefix:e,className:t,as:r="div",...n},o)=>{let s=(0,i.oU)(e,"row"),u=(0,i.gy)(),c=(0,i.Jm)(),d=`${s}-cols`,f=[];return u.forEach(e=>{let t,r=n[e];delete n[e],null!=r&&"object"==typeof r?{cols:t}=r:t=r;let a=e!==c?`-${e}`:"";null!=t&&f.push(`${d}${a}-${t}`)}),(0,l.jsx)(r,{ref:o,...n,className:a()(t,s,...f)})});s.displayName="Row";let u=s},84517:(e,t,r)=>{"use strict";r.d(t,{A:()=>d});var n=r(3892),a=r.n(n),o=r(82015);r(26324);var i=r(44080),l=r(24132),s=r(80739),u=r(8732);let c=o.forwardRef(({bsPrefix:e,type:t,size:r,htmlSize:n,id:i,className:c,isValid:d=!1,isInvalid:f=!1,plaintext:p,readOnly:h,as:m="input",..._},y)=>{let{controlId:g}=(0,o.useContext)(l.A);return e=(0,s.oU)(e,"form-control"),(0,u.jsx)(m,{..._,type:t,size:n,ref:y,readOnly:h,id:i||g,className:a()(c,p?`${e}-plaintext`:e,r&&`${e}-${r}`,"color"===t&&`${e}-color`,d&&"is-valid",f&&"is-invalid")})});c.displayName="FormControl";let d=Object.assign(c,{Feedback:i.A})},84619:(e,t,r)=>{"use strict";function n(){throw Object.defineProperty(Error("`forbidden()` is experimental and only allowed to be enabled when `experimental.authInterrupts` is enabled."),"__NEXT_ERROR_CODE",{value:"E488",enumerable:!1,configurable:!0})}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"forbidden",{enumerable:!0,get:function(){return n}}),r(82323).HTTP_ERROR_FALLBACK_ERROR_CODE,("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},84706:(e,t,r)=>{"use strict";var n=r(92921);t.__esModule=!0,t.default=function(e){return e&&"setState"in e?a.default.findDOMNode(e):null!=e?e:null};var a=n(r(22326));e.exports=t.default},84837:e=>{e.exports={root:"tweet-info-created-at_root__KaxZi"}},84841:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{cancelIdleCallback:function(){return n},requestIdleCallback:function(){return r}});let r="undefined"!=typeof self&&self.requestIdleCallback&&self.requestIdleCallback.bind(window)||function(e){let t=Date.now();return self.setTimeout(function(){e({didTimeout:!1,timeRemaining:function(){return Math.max(0,50-(Date.now()-t))}})},1)},n="undefined"!=typeof self&&self.cancelIdleCallback&&self.cancelIdleCallback.bind(window)||function(e){return clearTimeout(e)};("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},85306:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{Router:function(){return o.default},createRouter:function(){return m},default:function(){return p},makePublicRouterInstance:function(){return _},useRouter:function(){return h},withRouter:function(){return s.default}});let n=r(87020),a=n._(r(82015)),o=n._(r(4828)),i=r(52088),l=n._(r(61644)),s=n._(r(73949)),u={router:null,readyCallbacks:[],ready(e){if(this.router)return e()}},c=["pathname","route","query","asPath","components","isFallback","basePath","locale","locales","defaultLocale","isReady","isPreview","isLocaleDomain","domainLocales"],d=["push","replace","reload","back","prefetch","beforePopState"];function f(){if(!u.router)throw Object.defineProperty(Error('No router instance found.\nYou should only use "next/router" on the client side of your app.\n'),"__NEXT_ERROR_CODE",{value:"E394",enumerable:!1,configurable:!0});return u.router}Object.defineProperty(u,"events",{get:()=>o.default.events}),c.forEach(e=>{Object.defineProperty(u,e,{get:()=>f()[e]})}),d.forEach(e=>{u[e]=function(){for(var t=arguments.length,r=Array(t),n=0;n<t;n++)r[n]=arguments[n];return f()[e](...r)}}),["routeChangeStart","beforeHistoryChange","routeChangeComplete","routeChangeError","hashChangeStart","hashChangeComplete"].forEach(e=>{u.ready(()=>{o.default.events.on(e,function(){for(var t=arguments.length,r=Array(t),n=0;n<t;n++)r[n]=arguments[n];let a="on"+e.charAt(0).toUpperCase()+e.substring(1);if(u[a])try{u[a](...r)}catch(e){console.error("Error when running the Router event: "+a),console.error((0,l.default)(e)?e.message+"\n"+e.stack:e+"")}})})});let p=u;function h(){let e=a.default.useContext(i.RouterContext);if(!e)throw Object.defineProperty(Error("NextRouter was not mounted. https://nextjs.org/docs/messages/next-router-not-mounted"),"__NEXT_ERROR_CODE",{value:"E509",enumerable:!1,configurable:!0});return e}function m(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];return u.router=new o.default(...t),u.readyCallbacks.forEach(e=>e()),u.readyCallbacks=[],u.router}function _(e){let t={};for(let r of c){if("object"==typeof e[r]){t[r]=Object.assign(Array.isArray(e[r])?[]:{},e[r]);continue}t[r]=e[r]}return t.events=o.default.events,d.forEach(r=>{t[r]=function(){for(var t=arguments.length,n=Array(t),a=0;a<t;a++)n[a]=arguments[a];return e[r](...n)}}),t}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},86261:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{BailoutToCSRError:function(){return n},isBailoutToCSRError:function(){return a}});let r="BAILOUT_TO_CLIENT_SIDE_RENDERING";class n extends Error{constructor(e){super("Bail out to client-side rendering: "+e),this.reason=e,this.digest=r}}function a(e){return"object"==typeof e&&null!==e&&"digest"in e&&e.digest===r}},86290:(e,t)=>{"use strict";function r(e){let t=e.indexOf("#"),r=e.indexOf("?"),n=r>-1&&(t<0||r<t);return n||t>-1?{pathname:e.substring(0,n?r:t),query:n?e.substring(r,t>-1?t:void 0):"",hash:t>-1?e.slice(t):""}:{pathname:e,query:"",hash:""}}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"parsePath",{enumerable:!0,get:function(){return r}})},87536:(e,t)=>{"use strict";function r(e){let t={};for(let[r,n]of e.entries()){let e=t[r];void 0===e?t[r]=n:Array.isArray(e)?e.push(n):t[r]=[e,n]}return t}function n(e){return"string"==typeof e?e:("number"!=typeof e||isNaN(e))&&"boolean"!=typeof e?"":String(e)}function a(e){let t=new URLSearchParams;for(let[r,a]of Object.entries(e))if(Array.isArray(a))for(let e of a)t.append(r,n(e));else t.set(r,n(a));return t}function o(e){for(var t=arguments.length,r=Array(t>1?t-1:0),n=1;n<t;n++)r[n-1]=arguments[n];for(let t of r){for(let r of t.keys())e.delete(r);for(let[r,n]of t.entries())e.append(r,n)}return e}Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{assign:function(){return o},searchParamsToUrlQuery:function(){return r},urlQueryToSearchParams:function(){return a}})},88156:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"removePathPrefix",{enumerable:!0,get:function(){return a}});let n=r(69596);function a(e,t){if(!(0,n.pathHasPrefix)(e,t))return e;let r=e.slice(t.length);return r.startsWith("/")?r:"/"+r}},88542:(e,t)=>{"use strict";let r;function n(e){return(null==r?void 0:r.createScriptURL(e))||e}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"__unsafeCreateTrustedScriptURL",{enumerable:!0,get:function(){return n}}),("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},89281:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"errorOnce",{enumerable:!0,get:function(){return r}});let r=e=>{}},89555:(e,t,r)=>{"use strict";r.d(t,{A:()=>d});var n=r(3892),a=r.n(n),o=r(82015);r(26324);var i=r(49481),l=r(24132),s=r(80739),u=r(8732);let c=o.forwardRef(({as:e="label",bsPrefix:t,column:r=!1,visuallyHidden:n=!1,className:c,htmlFor:d,...f},p)=>{let{controlId:h}=(0,o.useContext)(l.A);t=(0,s.oU)(t,"form-label");let m="col-form-label";"string"==typeof r&&(m=`${m} ${m}-${r}`);let _=a()(c,t,n&&"visually-hidden",r&&m);return(d=d||h,r)?(0,u.jsx)(i.A,{ref:p,as:"label",className:_,htmlFor:d,...f}):(0,u.jsx)(e,{ref:p,className:_,htmlFor:d,...f})});c.displayName="FormLabel";let d=c},90978:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{METADATA_BOUNDARY_NAME:function(){return r},OUTLET_BOUNDARY_NAME:function(){return a},VIEWPORT_BOUNDARY_NAME:function(){return n}});let r="__next_metadata_boundary__",n="__next_viewport_boundary__",a="__next_outlet_boundary__"},91169:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"addLocale",{enumerable:!0,get:function(){return a}});let n=r(37779),a=function(e){for(var t=arguments.length,a=Array(t>1?t-1:0),o=1;o<t;o++)a[o-1]=arguments[o];return(0,n.normalizePathTrailingSlash)(r(41833).addLocale(e,...a))};("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},91353:(e,t,r)=>{"use strict";r.d(t,{A:()=>c});var n=r(3892),a=r.n(n),o=r(82015),i=r(65447),l=r(80739),s=r(8732);let u=o.forwardRef(({as:e,bsPrefix:t,variant:r="primary",size:n,active:o=!1,disabled:u=!1,className:c,...d},f)=>{let p=(0,l.oU)(t,"btn"),[h,{tagName:m}]=(0,i.useButtonProps)({tagName:e,disabled:u,...d});return(0,s.jsx)(m,{...h,...d,ref:f,disabled:u,className:a()(c,p,o&&"active",r&&`${p}-${r}`,n&&`${p}-${n}`,d.href&&u&&"disabled")})});u.displayName="Button";let c=u},91504:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{atLeastOneTask:function(){return a},scheduleImmediate:function(){return n},scheduleOnNextTick:function(){return r},waitAtLeastOneReactRenderTask:function(){return o}});let r=e=>{Promise.resolve().then(()=>{process.nextTick(e)})},n=e=>{setImmediate(e)};function a(){return new Promise(e=>n(e))}function o(){return new Promise(e=>setImmediate(e))}},91923:(e,t,r)=>{"use strict";r.d(t,{A:()=>W});var n=r(3892),a=r.n(n),o=r(82015),i=r(86842),l=r.n(i),s=r(14332),u=r(80739),c=r(8732);let d=o.forwardRef(({bsPrefix:e,className:t,as:r,...n},o)=>{e=(0,u.oU)(e,"navbar-brand");let i=r||(n.href?"a":"span");return(0,c.jsx)(i,{...n,ref:o,className:a()(t,e)})});d.displayName="NavbarBrand";var f=r(6756),p=r(44696);let h=o.forwardRef(({children:e,bsPrefix:t,...r},n)=>{t=(0,u.oU)(t,"navbar-collapse");let a=(0,o.useContext)(p.A);return(0,c.jsx)(f.A,{in:!!(a&&a.expanded),...r,children:(0,c.jsx)("div",{ref:n,className:t,children:e})})});h.displayName="NavbarCollapse";var m=r(81895),_=r.n(m);let y=o.forwardRef(({bsPrefix:e,className:t,children:r,label:n="Toggle navigation",as:i="button",onClick:l,...s},d)=>{e=(0,u.oU)(e,"navbar-toggler");let{onToggle:f,expanded:h}=(0,o.useContext)(p.A)||{},m=_()(e=>{l&&l(e),f&&f()});return"button"===i&&(s.type="button"),(0,c.jsx)(i,{...s,ref:d,onClick:m,"aria-label":n,className:a()(t,e,!h&&"collapsed"),children:r||(0,c.jsx)("span",{className:`${e}-icon`})})});y.displayName="NavbarToggle";var g=r(36955),v=r.n(g),b=r(6952),x=r.n(b),E=r(19799);let j=o.forwardRef(({className:e,bsPrefix:t,as:r="div",...n},o)=>(t=(0,u.oU)(t,"offcanvas-body"),(0,c.jsx)(r,{ref:o,className:a()(e,t),...n})));j.displayName="OffcanvasBody";var w=r(1919),R=r(1680),P=r(77291),O=r(66933);let T={[w.ENTERING]:"show",[w.ENTERED]:"show"},N=o.forwardRef(({bsPrefix:e,className:t,children:r,in:n=!1,mountOnEnter:i=!1,unmountOnExit:l=!1,appear:s=!1,...d},f)=>(e=(0,u.oU)(e,"offcanvas"),(0,c.jsx)(O.A,{ref:f,addEndListener:P.A,in:n,mountOnEnter:i,unmountOnExit:l,appear:s,...d,childRef:(0,R.getChildRef)(r),children:(n,i)=>o.cloneElement(r,{...i,className:a()(t,r.props.className,(n===w.ENTERING||n===w.EXITING)&&`${e}-toggling`,T[n])})})));N.displayName="OffcanvasToggling";var A=r(43601),S=r(46689);let C=o.forwardRef(({bsPrefix:e,className:t,closeLabel:r="Close",closeButton:n=!1,...o},i)=>(e=(0,u.oU)(e,"offcanvas-header"),(0,c.jsx)(S.A,{ref:i,...o,className:a()(t,e),closeLabel:r,closeButton:n})));C.displayName="OffcanvasHeader";let M=(0,r(7783).A)("h5"),k=o.forwardRef(({className:e,bsPrefix:t,as:r=M,...n},o)=>(t=(0,u.oU)(t,"offcanvas-title"),(0,c.jsx)(r,{ref:o,className:a()(e,t),...n})));k.displayName="OffcanvasTitle";var I=r(72889);function D(e){return(0,c.jsx)(N,{...e})}function L(e){return(0,c.jsx)(E.A,{...e})}let U=o.forwardRef(({bsPrefix:e,className:t,children:r,"aria-labelledby":n,placement:i="start",responsive:l,show:s=!1,backdrop:d=!0,keyboard:f=!0,scroll:p=!1,onEscapeKeyDown:h,onShow:m,onHide:y,container:g,autoFocus:b=!0,enforceFocus:E=!0,restoreFocus:j=!0,restoreFocusOptions:w,onEntered:R,onExit:P,onExiting:O,onEnter:T,onEntering:N,onExited:S,backdropClassName:C,manager:M,renderStaticNode:k=!1,...U},B)=>{let F=(0,o.useRef)();e=(0,u.oU)(e,"offcanvas");let[$,H]=(0,o.useState)(!1),W=_()(y),X=v()(l||"xs","up");(0,o.useEffect)(()=>{H(l?s&&!X:s)},[s,l,X]);let G=(0,o.useMemo)(()=>({onHide:W}),[W]),q=(0,o.useCallback)(t=>(0,c.jsx)("div",{...t,className:a()(`${e}-backdrop`,C)}),[C,e]),z=o=>(0,c.jsx)("div",{...o,...U,className:a()(t,l?`${e}-${l}`:e,`${e}-${i}`),"aria-labelledby":n,children:r});return(0,c.jsxs)(c.Fragment,{children:[!$&&(l||k)&&z({}),(0,c.jsx)(A.A.Provider,{value:G,children:(0,c.jsx)(x(),{show:$,ref:B,backdrop:d,container:g,keyboard:f,autoFocus:b,enforceFocus:E&&!p,restoreFocus:j,restoreFocusOptions:w,onEscapeKeyDown:h,onShow:m,onHide:W,onEnter:(e,...t)=>{e&&(e.style.visibility="visible"),null==T||T(e,...t)},onEntering:N,onEntered:R,onExit:P,onExiting:O,onExited:(e,...t)=>{e&&(e.style.visibility=""),null==S||S(...t)},manager:M||(p?(F.current||(F.current=new I.A({handleContainerOverflow:!1})),F.current):(0,I.R)()),transition:D,backdropTransition:L,renderBackdrop:q,renderDialog:z})})]})});U.displayName="Offcanvas";let B=Object.assign(U,{Body:j,Header:C,Title:k}),F=o.forwardRef(({onHide:e,...t},r)=>{let n=(0,o.useContext)(p.A),a=_()(()=>{null==n||null==n.onToggle||n.onToggle(),null==e||e()});return(0,c.jsx)(B,{ref:r,show:!!(null!=n&&n.expanded),...t,renderStaticNode:!0,onHide:a})});F.displayName="NavbarOffcanvas";let $=o.forwardRef(({className:e,bsPrefix:t,as:r="span",...n},o)=>(t=(0,u.oU)(t,"navbar-text"),(0,c.jsx)(r,{ref:o,className:a()(e,t),...n})));$.displayName="NavbarText";let H=o.forwardRef((e,t)=>{let{bsPrefix:r,expand:n=!0,variant:i="light",bg:d,fixed:f,sticky:h,className:m,as:_="nav",expanded:y,onToggle:g,onSelect:v,collapseOnSelect:b=!1,...x}=(0,s.useUncontrolled)(e,{expanded:"onToggle"}),E=(0,u.oU)(r,"navbar"),j=(0,o.useCallback)((...e)=>{null==v||v(...e),b&&y&&(null==g||g(!1))},[v,b,y,g]);void 0===x.role&&"nav"!==_&&(x.role="navigation");let w=`${E}-expand`;"string"==typeof n&&(w=`${w}-${n}`);let R=(0,o.useMemo)(()=>({onToggle:()=>null==g?void 0:g(!y),bsPrefix:E,expanded:!!y,expand:n}),[E,y,n,g]);return(0,c.jsx)(p.A.Provider,{value:R,children:(0,c.jsx)(l().Provider,{value:j,children:(0,c.jsx)(_,{ref:t,...x,className:a()(m,E,n&&w,i&&`${E}-${i}`,d&&`bg-${d}`,h&&`sticky-${h}`,f&&`fixed-${f}`)})})})});H.displayName="Navbar";let W=Object.assign(H,{Brand:d,Collapse:h,Offcanvas:F,Text:$,Toggle:y})},92620:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"formatNextPathnameInfo",{enumerable:!0,get:function(){return l}});let n=r(21730),a=r(36655),o=r(58992),i=r(41833);function l(e){let t=(0,i.addLocale)(e.pathname,e.locale,e.buildId?void 0:e.defaultLocale,e.ignorePrefix);return(e.buildId||!e.trailingSlash)&&(t=(0,n.removeTrailingSlash)(t)),e.buildId&&(t=(0,o.addPathSuffix)((0,a.addPathPrefix)(t,"/_next/data/"+e.buildId),"/"===e.pathname?"index.json":".json")),t=(0,a.addPathPrefix)(t,e.basePath),!e.buildId&&e.trailingSlash?t.endsWith("/")?t:(0,o.addPathSuffix)(t,"/"):(0,n.removeTrailingSlash)(t)}},92746:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"normalizeLocalePath",{enumerable:!0,get:function(){return n}});let r=new WeakMap;function n(e,t){let n;if(!t)return{pathname:e};let a=r.get(t);a||(a=t.map(e=>e.toLowerCase()),r.set(t,a));let o=e.split("/",2);if(!o[1])return{pathname:e};let i=o[1].toLowerCase(),l=a.indexOf(i);return l<0?{pathname:e}:(n=t[l],{pathname:e=e.slice(n.length+1)||"/",detectedLocale:n})}},92921:e=>{e.exports=function(e){return e&&e.__esModule?e:{default:e}},e.exports.__esModule=!0,e.exports.default=e.exports},93024:(e,t,r)=>{"use strict";r.d(t,{A:()=>v});var n=r(3892),a=r.n(n),o=r(82015),i=r(14332),l=r(80739),s=r(6756);function u(e,t){return Array.isArray(e)?e.includes(t):e===t}let c=o.createContext({});c.displayName="AccordionContext";var d=r(8732);let f=o.forwardRef(({as:e="div",bsPrefix:t,className:r,children:n,eventKey:i,...f},p)=>{let{activeEventKey:h}=(0,o.useContext)(c);return t=(0,l.oU)(t,"accordion-collapse"),(0,d.jsx)(s.A,{ref:p,in:u(h,i),...f,className:a()(r,t),children:(0,d.jsx)(e,{children:o.Children.only(n)})})});f.displayName="AccordionCollapse";let p=o.createContext({eventKey:""});p.displayName="AccordionItemContext";let h=o.forwardRef(({as:e="div",bsPrefix:t,className:r,onEnter:n,onEntering:i,onEntered:s,onExit:u,onExiting:c,onExited:h,...m},_)=>{t=(0,l.oU)(t,"accordion-body");let{eventKey:y}=(0,o.useContext)(p);return(0,d.jsx)(f,{eventKey:y,onEnter:n,onEntering:i,onEntered:s,onExit:u,onExiting:c,onExited:h,children:(0,d.jsx)(e,{ref:_,...m,className:a()(r,t)})})});h.displayName="AccordionBody";let m=o.forwardRef(({as:e="button",bsPrefix:t,className:r,onClick:n,...i},s)=>{t=(0,l.oU)(t,"accordion-button");let{eventKey:f}=(0,o.useContext)(p),h=function(e,t){let{activeEventKey:r,onSelect:n,alwaysOpen:a}=(0,o.useContext)(c);return o=>{let i=e===r?null:e;a&&(i=Array.isArray(r)?r.includes(e)?r.filter(t=>t!==e):[...r,e]:[e]),null==n||n(i,o),null==t||t(o)}}(f,n),{activeEventKey:m}=(0,o.useContext)(c);return"button"===e&&(i.type="button"),(0,d.jsx)(e,{ref:s,onClick:h,...i,"aria-expanded":Array.isArray(m)?m.includes(f):f===m,className:a()(r,t,!u(m,f)&&"collapsed")})});m.displayName="AccordionButton";let _=o.forwardRef(({as:e="h2","aria-controls":t,bsPrefix:r,className:n,children:o,onClick:i,...s},u)=>(r=(0,l.oU)(r,"accordion-header"),(0,d.jsx)(e,{ref:u,...s,className:a()(n,r),children:(0,d.jsx)(m,{onClick:i,"aria-controls":t,children:o})})));_.displayName="AccordionHeader";let y=o.forwardRef(({as:e="div",bsPrefix:t,className:r,eventKey:n,...i},s)=>{t=(0,l.oU)(t,"accordion-item");let u=(0,o.useMemo)(()=>({eventKey:n}),[n]);return(0,d.jsx)(p.Provider,{value:u,children:(0,d.jsx)(e,{ref:s,...i,className:a()(r,t)})})});y.displayName="AccordionItem";let g=o.forwardRef((e,t)=>{let{as:r="div",activeKey:n,bsPrefix:s,className:u,onSelect:f,flush:p,alwaysOpen:h,...m}=(0,i.useUncontrolled)(e,{activeKey:"onSelect"}),_=(0,l.oU)(s,"accordion"),y=(0,o.useMemo)(()=>({activeEventKey:n,onSelect:f,alwaysOpen:h}),[n,f,h]);return(0,d.jsx)(c.Provider,{value:y,children:(0,d.jsx)(r,{ref:t,...m,className:a()(u,_,p&&`${_}-flush`)})})});g.displayName="Accordion";let v=Object.assign(g,{Button:m,Collapse:f,Item:y,Header:_,Body:h})},93045:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{compileNonPath:function(){return c},matchHas:function(){return u},parseDestination:function(){return d},prepareDestination:function(){return f}});let n=r(41791),a=r(58900),o=r(6898),i=r(76580),l=r(34445);function s(e){return e.replace(/__ESC_COLON_/gi,":")}function u(e,t,r,n){void 0===r&&(r=[]),void 0===n&&(n=[]);let a={},o=r=>{let n,o=r.key;switch(r.type){case"header":o=o.toLowerCase(),n=e.headers[o];break;case"cookie":n="cookies"in e?e.cookies[r.key]:(0,l.getCookieParser)(e.headers)()[r.key];break;case"query":n=t[o];break;case"host":{let{host:t}=(null==e?void 0:e.headers)||{};n=null==t?void 0:t.split(":",1)[0].toLowerCase()}}if(!r.value&&n)return a[function(e){let t="";for(let r=0;r<e.length;r++){let n=e.charCodeAt(r);(n>64&&n<91||n>96&&n<123)&&(t+=e[r])}return t}(o)]=n,!0;if(n){let e=RegExp("^"+r.value+"$"),t=Array.isArray(n)?n.slice(-1)[0].match(e):n.match(e);if(t)return Array.isArray(t)&&(t.groups?Object.keys(t.groups).forEach(e=>{a[e]=t.groups[e]}):"host"===r.type&&t[0]&&(a.host=t[0])),!0}return!1};return!(!r.every(e=>o(e))||n.some(e=>o(e)))&&a}function c(e,t){if(!e.includes(":"))return e;for(let r of Object.keys(t))e.includes(":"+r)&&(e=e.replace(RegExp(":"+r+"\\*","g"),":"+r+"--ESCAPED_PARAM_ASTERISKS").replace(RegExp(":"+r+"\\?","g"),":"+r+"--ESCAPED_PARAM_QUESTION").replace(RegExp(":"+r+"\\+","g"),":"+r+"--ESCAPED_PARAM_PLUS").replace(RegExp(":"+r+"(?!\\w)","g"),"--ESCAPED_PARAM_COLON"+r));return e=e.replace(/(:|\*|\?|\+|\(|\)|\{|\})/g,"\\$1").replace(/--ESCAPED_PARAM_PLUS/g,"+").replace(/--ESCAPED_PARAM_COLON/g,":").replace(/--ESCAPED_PARAM_QUESTION/g,"?").replace(/--ESCAPED_PARAM_ASTERISKS/g,"*"),(0,n.compile)("/"+e,{validate:!1})(t).slice(1)}function d(e){let t=e.destination;for(let r of Object.keys({...e.params,...e.query}))r&&(t=t.replace(RegExp(":"+(0,a.escapeStringRegexp)(r),"g"),"__ESC_COLON_"+r));let r=(0,o.parseUrl)(t),n=r.pathname;n&&(n=s(n));let i=r.href;i&&(i=s(i));let l=r.hostname;l&&(l=s(l));let u=r.hash;return u&&(u=s(u)),{...r,pathname:n,hostname:l,href:i,hash:u}}function f(e){let t,r,a=Object.assign({},e.query),o=d(e),{hostname:l,query:u}=o,f=o.pathname;o.hash&&(f=""+f+o.hash);let p=[],h=[];for(let e of((0,n.pathToRegexp)(f,h),h))p.push(e.name);if(l){let e=[];for(let t of((0,n.pathToRegexp)(l,e),e))p.push(t.name)}let m=(0,n.compile)(f,{validate:!1});for(let[r,a]of(l&&(t=(0,n.compile)(l,{validate:!1})),Object.entries(u)))Array.isArray(a)?u[r]=a.map(t=>c(s(t),e.params)):"string"==typeof a&&(u[r]=c(s(a),e.params));let _=Object.keys(e.params).filter(e=>"nextInternalLocale"!==e);if(e.appendParamsToQuery&&!_.some(e=>p.includes(e)))for(let t of _)t in u||(u[t]=e.params[t]);if((0,i.isInterceptionRouteAppPath)(f))for(let t of f.split("/")){let r=i.INTERCEPTION_ROUTE_MARKERS.find(e=>t.startsWith(e));if(r){"(..)(..)"===r?(e.params["0"]="(..)",e.params["1"]="(..)"):e.params["0"]=r;break}}try{let[n,a]=(r=m(e.params)).split("#",2);t&&(o.hostname=t(e.params)),o.pathname=n,o.hash=(a?"#":"")+(a||""),delete o.search}catch(e){if(e.message.match(/Expected .*? to not repeat, but got an array/))throw Object.defineProperty(Error("To use a multi-match in the destination you must add `*` at the end of the param name to signify it should repeat. https://nextjs.org/docs/messages/invalid-multi-match"),"__NEXT_ERROR_CODE",{value:"E329",enumerable:!1,configurable:!0});throw e}return o.query={...a,...o.query},{newUrl:r,destQuery:u,parsedDestination:o}}},93655:e=>{e.exports={root:"quoted-tweet-container_root__92393",article:"quoted-tweet-container_article__FoJQN"}},93847:e=>{e.exports={actions:"tweet-actions_actions__UDw7H",like:"tweet-actions_like__H1xYv",reply:"tweet-actions_reply__S4rFc",copy:"tweet-actions_copy__Tbdg_",likeIconWrapper:"tweet-actions_likeIconWrapper__JQkhp",likeCount:"tweet-actions_likeCount__MyxBd",replyIconWrapper:"tweet-actions_replyIconWrapper__NVdGa",copyIconWrapper:"tweet-actions_copyIconWrapper__toM2y",likeIcon:"tweet-actions_likeIcon__fhDng",replyIcon:"tweet-actions_replyIcon__MI2tG",copyIcon:"tweet-actions_copyIcon__SEaWw",replyText:"tweet-actions_replyText__doQct",copyText:"tweet-actions_copyText__fEqBx"}},93998:(e,t,r)=>{"use strict";var n=r(92921);t.__esModule=!0,t.default=void 0;var a=function(e,t){if(e&&e.__esModule)return e;if(null===e||"object"!=typeof e&&"function"!=typeof e)return{default:e};var r=u(t);if(r&&r.has(e))return r.get(e);var n={__proto__:null},a=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var o in e)if("default"!==o&&Object.prototype.hasOwnProperty.call(e,o)){var i=a?Object.getOwnPropertyDescriptor(e,o):null;i&&(i.get||i.set)?Object.defineProperty(n,o,i):n[o]=e[o]}return n.default=e,r&&r.set(e,n),n}(r(82015)),o=n(r(1919)),i=n(r(37766)),l=n(r(84706)),s=r(8732);function u(e){if("function"!=typeof WeakMap)return null;var t=new WeakMap,r=new WeakMap;return(u=function(e){return e?r:t})(e)}let c=a.default.forwardRef(({onEnter:e,onEntering:t,onEntered:r,onExit:n,onExiting:u,onExited:c,addEndListener:d,children:f,childRef:p,...h},m)=>{let _=(0,a.useRef)(null),y=(0,i.default)(_,p),g=e=>{y((0,l.default)(e))},v=e=>t=>{e&&_.current&&e(_.current,t)},b=(0,a.useCallback)(v(e),[e]),x=(0,a.useCallback)(v(t),[t]),E=(0,a.useCallback)(v(r),[r]),j=(0,a.useCallback)(v(n),[n]),w=(0,a.useCallback)(v(u),[u]),R=(0,a.useCallback)(v(c),[c]),P=(0,a.useCallback)(v(d),[d]);return(0,s.jsx)(o.default,{ref:m,...h,onEnter:b,onEntered:E,onEntering:x,onExit:j,onExited:R,onExiting:w,addEndListener:P,nodeRef:_,children:"function"==typeof f?(e,t)=>f(e,{...t,ref:g}):a.default.cloneElement(f,{ref:g})})});c.displayName="TransitionWrapper",t.default=c,e.exports=t.default},94145:(e,t,r)=>{"use strict";r.a(e,async(e,n)=>{try{r.d(t,{Y:()=>c});var a=r(8732),o=r(33718),i=r(7965),l=r(94908),s=r(6217),u=e([s]);s=(u.then?(await u)():u)[0];let c=({id:e,apiUrl:t,fallback:r=(0,a.jsx)(o.l,{}),components:n,fetchOptions:u,onError:c})=>{let{data:d,error:f,isLoading:p}=(0,s.n)(e,t,u);if(p)return r;if(f||!d){let e=(null==n?void 0:n.TweetNotFound)||i.L;return(0,a.jsx)(e,{error:c?c(f):f})}return(0,a.jsx)(l.c,{tweet:d,components:n})};n()}catch(e){n(e)}})},94908:(e,t,r)=>{"use strict";r.d(t,{c:()=>es});var n=r(8732),a=r(97580),o=r(24562);let i=e=>(0,n.jsx)("img",{...e});var l=r(33057),s=r(42535);let u=()=>(0,n.jsx)("svg",{viewBox:"0 0 24 24","aria-label":"Verified account",role:"img",className:s.verified,children:(0,n.jsx)("g",{children:(0,n.jsx)("path",{d:"M22.25 12c0-1.43-.88-2.67-2.19-3.34.46-1.39.2-2.9-.81-3.91s-2.52-1.27-3.91-.81c-.66-1.31-1.91-2.19-3.34-2.19s-2.67.88-3.33 2.19c-1.4-.46-2.91-.2-3.92.81s-1.26 2.52-.8 3.91c-1.31.67-2.2 1.91-2.2 3.34s.89 2.67 2.2 3.34c-.46 1.39-.21 2.9.8 3.91s2.52 1.26 3.91.81c.67 1.31 1.91 2.19 3.34 2.19s2.68-.88 3.34-2.19c1.39.45 2.9.2 3.91-.81s1.27-2.52.81-3.91c1.31-.67 2.19-1.91 2.19-3.34zm-11.71 4.2L6.8 12.46l1.41-1.42 2.26 2.26 4.8-5.23 1.47 1.36-6.2 6.77z"})})}),c=()=>(0,n.jsx)("svg",{viewBox:"0 0 22 22","aria-label":"Verified account",role:"img",className:s.verified,children:(0,n.jsx)("g",{children:(0,n.jsx)("path",{clipRule:"evenodd",d:"M12.05 2.056c-.568-.608-1.532-.608-2.1 0l-1.393 1.49c-.284.303-.685.47-1.1.455L5.42 3.932c-.832-.028-1.514.654-1.486 1.486l.069 2.039c.014.415-.152.816-.456 1.1l-1.49 1.392c-.608.568-.608 1.533 0 2.101l1.49 1.393c.304.284.47.684.456 1.1l-.07 2.038c-.027.832.655 1.514 1.487 1.486l2.038-.069c.415-.014.816.152 1.1.455l1.392 1.49c.569.609 1.533.609 2.102 0l1.393-1.49c.283-.303.684-.47 1.099-.455l2.038.069c.832.028 1.515-.654 1.486-1.486L18 14.542c-.015-.415.152-.815.455-1.099l1.49-1.393c.608-.568.608-1.533 0-2.101l-1.49-1.393c-.303-.283-.47-.684-.455-1.1l.068-2.038c.029-.832-.654-1.514-1.486-1.486l-2.038.07c-.415.013-.816-.153-1.1-.456zm-5.817 9.367l3.429 3.428 5.683-6.206-1.347-1.247-4.4 4.795-2.072-2.072z",fillRule:"evenodd"})})}),d=()=>(0,n.jsx)("svg",{viewBox:"0 0 22 22","aria-label":"Verified account",role:"img",className:s.verified,children:(0,n.jsxs)("g",{children:[(0,n.jsxs)("linearGradient",{gradientUnits:"userSpaceOnUse",id:"0-a",x1:"4.411",x2:"18.083",y1:"2.495",y2:"21.508",children:[(0,n.jsx)("stop",{offset:"0",stopColor:"#f4e72a"}),(0,n.jsx)("stop",{offset:".539",stopColor:"#cd8105"}),(0,n.jsx)("stop",{offset:".68",stopColor:"#cb7b00"}),(0,n.jsx)("stop",{offset:"1",stopColor:"#f4ec26"}),(0,n.jsx)("stop",{offset:"1",stopColor:"#f4e72a"})]}),(0,n.jsxs)("linearGradient",{gradientUnits:"userSpaceOnUse",id:"0-b",x1:"5.355",x2:"16.361",y1:"3.395",y2:"19.133",children:[(0,n.jsx)("stop",{offset:"0",stopColor:"#f9e87f"}),(0,n.jsx)("stop",{offset:".406",stopColor:"#e2b719"}),(0,n.jsx)("stop",{offset:".989",stopColor:"#e2b719"})]}),(0,n.jsxs)("g",{clipRule:"evenodd",fillRule:"evenodd",children:[(0,n.jsx)("path",{d:"M13.324 3.848L11 1.6 8.676 3.848l-3.201-.453-.559 3.184L2.06 8.095 3.48 11l-1.42 2.904 2.856 1.516.559 3.184 3.201-.452L11 20.4l2.324-2.248 3.201.452.559-3.184 2.856-1.516L18.52 11l1.42-2.905-2.856-1.516-.559-3.184zm-7.09 7.575l3.428 3.428 5.683-6.206-1.347-1.247-4.4 4.795-2.072-2.072z",fill:"url(#0-a)"}),(0,n.jsx)("path",{d:"M13.101 4.533L11 2.5 8.899 4.533l-2.895-.41-.505 2.88-2.583 1.37L4.2 11l-1.284 2.627 2.583 1.37.505 2.88 2.895-.41L11 19.5l2.101-2.033 2.895.41.505-2.88 2.583-1.37L17.8 11l1.284-2.627-2.583-1.37-.505-2.88zm-6.868 6.89l3.429 3.428 5.683-6.206-1.347-1.247-4.4 4.795-2.072-2.072z",fill:"url(#0-b)"}),(0,n.jsx)("path",{d:"M6.233 11.423l3.429 3.428 5.65-6.17.038-.033-.005 1.398-5.683 6.206-3.429-3.429-.003-1.405.005.003z",fill:"#d18800"})]})]})});var f=r(25048);let p=({user:e,className:t})=>{let r=e.verified||e.is_blue_verified||e.verified_type,a=(0,n.jsx)(u,{}),i=f.verifiedBlue;if(r)switch(!e.is_blue_verified&&(i=f.verifiedOld),e.verified_type){case"Government":a=(0,n.jsx)(c,{}),i=f.verifiedGovernment;break;case"Business":a=(0,n.jsx)(d,{}),i=null}return r?(0,n.jsx)("div",{className:(0,o.A)(t,i),children:a}):null},h=({tweet:e,components:t})=>{var r;let a=null!=(r=null==t?void 0:t.AvatarImg)?r:i,{user:s}=e;return(0,n.jsxs)("div",{className:l.header,children:[(0,n.jsxs)("a",{href:e.url,className:l.avatar,target:"_blank",rel:"noopener noreferrer",children:[(0,n.jsx)("div",{className:(0,o.A)(l.avatarOverflow,"Square"===s.profile_image_shape&&l.avatarSquare),children:(0,n.jsx)(a,{src:s.profile_image_url_https,alt:s.name,width:48,height:48})}),(0,n.jsx)("div",{className:l.avatarOverflow,children:(0,n.jsx)("div",{className:l.avatarShadow})})]}),(0,n.jsxs)("div",{className:l.author,children:[(0,n.jsxs)("a",{href:e.url,className:l.authorLink,target:"_blank",rel:"noopener noreferrer",children:[(0,n.jsx)("div",{className:l.authorLinkText,children:(0,n.jsx)("span",{title:s.name,children:s.name})}),(0,n.jsx)(p,{user:s,className:l.authorVerified})]}),(0,n.jsxs)("div",{className:l.authorMeta,children:[(0,n.jsx)("a",{href:e.url,className:l.username,target:"_blank",rel:"noopener noreferrer",children:(0,n.jsxs)("span",{title:`@${s.screen_name}`,children:["@",s.screen_name]})}),(0,n.jsxs)("div",{className:l.authorFollow,children:[(0,n.jsx)("span",{className:l.separator,children:"\xb7"}),(0,n.jsx)("a",{href:s.follow_url,className:l.follow,target:"_blank",rel:"noopener noreferrer",children:"Follow"})]})]})]}),(0,n.jsx)("a",{href:e.url,className:l.brand,target:"_blank",rel:"noopener noreferrer","aria-label":"View on Twitter",children:(0,n.jsx)("svg",{viewBox:"0 0 24 24","aria-hidden":"true",className:l.twitterIcon,children:(0,n.jsx)("g",{children:(0,n.jsx)("path",{d:"M18.244 2.25h3.308l-7.227 8.26 8.502 11.24H16.17l-5.214-6.817L4.99 21.75H1.68l7.73-8.835L1.254 2.25H8.08l4.713 6.231zm-1.161 17.52h1.833L7.084 4.126H5.117z"})})})})]})};var m=r(64066);let _=({tweet:e})=>(0,n.jsxs)("a",{href:e.in_reply_to_url,className:m.root,target:"_blank",rel:"noopener noreferrer",children:["Replying to @",e.in_reply_to_screen_name]});var y=r(49182);let g=({href:e,children:t})=>(0,n.jsx)("a",{href:e,className:y.root,target:"_blank",rel:"noopener noreferrer nofollow",children:t});var v=r(24658);let b=({tweet:e})=>(0,n.jsx)("p",{className:v.root,lang:e.lang,dir:"auto",children:e.entities.map((e,t)=>{switch(e.type){case"hashtag":case"mention":case"url":case"symbol":return(0,n.jsx)(g,{href:e.href,children:e.text},t);case"media":return;default:return(0,n.jsx)("span",{dangerouslySetInnerHTML:{__html:e.text}},t)}})});var x=r(82015);let E=e=>`https://x.com/${e.user.screen_name}/status/${e.id_str}`,j=e=>`https://x.com/${"string"==typeof e?e:e.user.screen_name}`,w=e=>`https://x.com/intent/like?tweet_id=${e.id_str}`,R=e=>`https://x.com/intent/tweet?in_reply_to=${e.id_str}`,P=e=>`https://x.com/intent/follow?screen_name=${e.user.screen_name}`,O=e=>`https://x.com/hashtag/${e.text}`,T=e=>`https://x.com/search?q=%24${e.text}`,N=e=>`https://x.com/${e.in_reply_to_screen_name}/status/${e.in_reply_to_status_id_str}`,A=(e,t)=>{let r=new URL(e.media_url_https),n=r.pathname.split(".").pop();return n?(r.pathname=r.pathname.replace(`.${n}`,""),r.searchParams.set("format",n),r.searchParams.set("name",t),r.toString()):e.media_url_https},S=e=>{let{variants:t}=e.video_info;return t.filter(e=>"video/mp4"===e.content_type).sort((e,t)=>{var r,n;return(null!=(r=t.bitrate)?r:0)-(null!=(n=e.bitrate)?n:0)})},C=e=>{let t=S(e);return t.length>1?t[1]:t[0]},M=e=>e>999999?`${(e/1e6).toFixed(1)}M`:e>999?`${(e/1e3).toFixed(1)}K`:e.toString();function k(e){let t=Array.from(e.text),r=[{indices:e.display_text_range,type:"text"}];return I(r,"hashtag",e.entities.hashtags),I(r,"mention",e.entities.user_mentions),I(r,"url",e.entities.urls),I(r,"symbol",e.entities.symbols),e.entities.media&&I(r,"media",e.entities.media),function(e,t){e.entities.media&&e.entities.media[0].indices[0]<e.display_text_range[1]&&(e.display_text_range[1]=e.entities.media[0].indices[0]);let r=t.at(-1);r&&r.indices[1]>e.display_text_range[1]&&(r.indices[1]=e.display_text_range[1])}(e,r),r.map(e=>{let r=t.slice(e.indices[0],e.indices[1]).join("");switch(e.type){case"hashtag":return Object.assign(e,{href:O(e),text:r});case"mention":return Object.assign(e,{href:j(e.screen_name),text:r});case"url":case"media":return Object.assign(e,{href:e.expanded_url,text:e.display_url});case"symbol":return Object.assign(e,{href:T(e),text:r});default:return Object.assign(e,{text:r})}})}function I(e,t,r){for(let n of r)for(let[r,a]of e.entries()){if(a.indices[0]>n.indices[0]||a.indices[1]<n.indices[1])continue;let o=[{...n,type:t}];a.indices[0]<n.indices[0]&&o.unshift({indices:[a.indices[0],n.indices[0]],type:"text"}),a.indices[1]>n.indices[1]&&o.push({indices:[n.indices[1],a.indices[1]],type:"text"}),e.splice(r,1,...o);break}}let D=e=>({...e,url:E(e),user:{...e.user,url:j(e),follow_url:P(e)},like_url:w(e),reply_url:R(e),in_reply_to_url:e.in_reply_to_screen_name?N(e):void 0,entities:k(e),quoted_tweet:e.quoted_tweet?{...e.quoted_tweet,url:E(e.quoted_tweet),entities:k(e.quoted_tweet)}:void 0});var L=r(67750),U=r(20608);let B=({tweet:e,media:t})=>{let[r,a]=(0,x.useState)(!0),[i,l]=(0,x.useState)(!1),[s,u]=(0,x.useState)(!1),c=C(t),d=0;return(0,n.jsxs)(n.Fragment,{children:[(0,n.jsx)("video",{className:L.image,poster:A(t,"small"),controls:!r,playsInline:!0,preload:"none",tabIndex:r?-1:0,onPlay:()=>{d&&window.clearTimeout(d),i||l(!0),s&&u(!1)},onPause:()=>{d&&window.clearTimeout(d),d=window.setTimeout(()=>{i&&l(!1),d=0},100)},onEnded:()=>{u(!0)},children:(0,n.jsx)("source",{src:c.url,type:c.content_type})}),r&&(0,n.jsx)("button",{type:"button",className:U.videoButton,"aria-label":"View video on X",onClick:e=>{let t=e.currentTarget.previousSibling;e.preventDefault(),a(!1),t.load(),t.play().then(()=>{l(!0),t.focus()}).catch(e=>{console.error("Error playing video:",e),a(!0),l(!1)})},children:(0,n.jsx)("svg",{viewBox:"0 0 24 24",className:U.videoButtonIcon,"aria-hidden":"true",children:(0,n.jsx)("g",{children:(0,n.jsx)("path",{d:"M21 12L4 2v20l17-10z"})})})}),!i&&!s&&(0,n.jsx)("div",{className:U.watchOnTwitter,children:(0,n.jsx)("a",{href:e.url,className:U.anchor,target:"_blank",rel:"noopener noreferrer",children:r?"Watch on X":"Continue watching on X"})}),s&&(0,n.jsx)("a",{href:e.url,className:(0,o.A)(U.anchor,U.viewReplies),target:"_blank",rel:"noopener noreferrer",children:"View replies"})]})},F=e=>(0,n.jsx)("img",{...e}),$=(e,t)=>{let r=56.25;return 1===t&&(r=100/e.original_info.width*e.original_info.height),2===t&&(r*=2),{width:"photo"===e.type?void 0:"unset",paddingBottom:`${r}%`}},H=({tweet:e,components:t,quoted:r})=>{var a,i,l,s;let u=null!=(l=null==(a=e.mediaDetails)?void 0:a.length)?l:0,c=null!=(s=null==t?void 0:t.MediaImg)?s:F;return(0,n.jsx)("div",{className:(0,o.A)(L.root,!r&&L.rounded),children:(0,n.jsx)("div",{className:(0,o.A)(L.mediaWrapper,u>1&&L.grid2Columns,3===u&&L.grid3,u>4&&L.grid2x2),children:null==(i=e.mediaDetails)?void 0:i.map(t=>(0,n.jsx)(x.Fragment,{children:"photo"===t.type?(0,n.jsxs)("a",{href:e.url,className:(0,o.A)(L.mediaContainer,L.mediaLink),target:"_blank",rel:"noopener noreferrer",children:[(0,n.jsx)("div",{className:L.skeleton,style:$(t,u)}),(0,n.jsx)(c,{src:A(t,"small"),alt:t.ext_alt_text||"Image",className:L.image,draggable:!0})]},t.media_url_https):(0,n.jsxs)("div",{className:L.mediaContainer,children:[(0,n.jsx)("div",{className:L.skeleton,style:$(t,u)}),(0,n.jsx)(B,{tweet:e,media:t})]},t.media_url_https)},t.media_url_https))})})},W=new Intl.DateTimeFormat("en-US",{hour:"numeric",minute:"2-digit",hour12:!0,weekday:"short",month:"short",day:"numeric",year:"numeric"}),X=e=>{let t={};for(let r of e)t[r.type]=r.value;return t},G=e=>{let t=X(W.formatToParts(e)),r=`${t.hour}:${t.minute} ${t.dayPeriod}`,n=`${t.month} ${t.day}, ${t.year}`;return`${r} \xb7 ${n}`};var q=r(84837);let z=({tweet:e})=>{let t=new Date(e.created_at),r=G(t);return(0,n.jsx)("a",{className:q.root,href:e.url,target:"_blank",rel:"noopener noreferrer","aria-label":r,children:(0,n.jsx)("time",{dateTime:t.toISOString(),children:r})})};var V=r(32006);let K=({tweet:e})=>(0,n.jsxs)("div",{className:V.info,children:[(0,n.jsx)(z,{tweet:e}),(0,n.jsx)("a",{className:V.infoLink,href:"https://help.x.com/en/x-for-websites-ads-info-and-privacy",target:"_blank",rel:"noopener noreferrer","aria-label":"Twitter for Websites, Ads Information and Privacy",children:(0,n.jsx)("svg",{viewBox:"0 0 24 24","aria-hidden":"true",className:V.infoIcon,children:(0,n.jsx)("g",{children:(0,n.jsx)("path",{d:"M13.5 8.5c0 .83-.67 1.5-1.5 1.5s-1.5-.67-1.5-1.5S11.17 7 12 7s1.5.67 1.5 1.5zM13 17v-5h-2v5h2zm-1 5.25c5.66 0 10.25-4.59 10.25-10.25S17.66 1.75 12 1.75 1.75 6.34 1.75 12 6.34 22.25 12 22.25zM20.25 12c0 4.56-3.69 8.25-8.25 8.25S3.75 16.56 3.75 12 7.44 3.75 12 3.75s8.25 3.69 8.25 8.25z"})})})})]});var Y=r(93847);let Q=({tweet:e})=>{let[t,r]=(0,x.useState)(!1);return(0,x.useEffect)(()=>{if(t){let e=setTimeout(()=>{r(!1)},6e3);return()=>clearTimeout(e)}},[t]),(0,n.jsxs)("button",{type:"button",className:Y.copy,"aria-label":"Copy link",onClick:()=>{navigator.clipboard.writeText(e.url),r(!0)},children:[(0,n.jsx)("div",{className:Y.copyIconWrapper,children:t?(0,n.jsx)("svg",{viewBox:"0 0 24 24",className:Y.copyIcon,"aria-hidden":"true",children:(0,n.jsx)("g",{children:(0,n.jsx)("path",{d:"M9.64 18.952l-5.55-4.861 1.317-1.504 3.951 3.459 8.459-10.948L19.4 6.32 9.64 18.952z"})})}):(0,n.jsx)("svg",{viewBox:"0 0 24 24",className:Y.copyIcon,"aria-hidden":"true",children:(0,n.jsx)("g",{children:(0,n.jsx)("path",{d:"M18.36 5.64c-1.95-1.96-5.11-1.96-7.07 0L9.88 7.05 8.46 5.64l1.42-1.42c2.73-2.73 7.16-2.73 9.9 0 2.73 2.74 2.73 7.17 0 9.9l-1.42 1.42-1.41-1.42 1.41-1.41c1.96-1.96 1.96-5.12 0-7.07zm-2.12 3.53l-7.07 7.07-1.41-1.41 7.07-7.07 1.41 1.41zm-12.02.71l1.42-1.42 1.41 1.42-1.41 1.41c-1.96 1.96-1.96 5.12 0 7.07 1.95 1.96 5.11 1.96 7.07 0l1.41-1.41 1.42 1.41-1.42 1.42c-2.73 2.73-7.16 2.73-9.9 0-2.73-2.74-2.73-7.17 0-9.9z"})})})}),(0,n.jsx)("span",{className:Y.copyText,children:t?"Copied!":"Copy link"})]})},J=({tweet:e})=>{let t=M(e.favorite_count);return(0,n.jsxs)("div",{className:Y.actions,children:[(0,n.jsxs)("a",{className:Y.like,href:e.like_url,target:"_blank",rel:"noopener noreferrer","aria-label":`Like. This Tweet has ${t} likes`,children:[(0,n.jsx)("div",{className:Y.likeIconWrapper,children:(0,n.jsx)("svg",{viewBox:"0 0 24 24",className:Y.likeIcon,"aria-hidden":"true",children:(0,n.jsx)("g",{children:(0,n.jsx)("path",{d:"M20.884 13.19c-1.351 2.48-4.001 5.12-8.379 7.67l-.503.3-.504-.3c-4.379-2.55-7.029-5.19-8.382-7.67-1.36-2.5-1.41-4.86-.514-6.67.887-1.79 2.647-2.91 4.601-3.01 1.651-.09 3.368.56 4.798 2.01 1.429-1.45 3.146-2.1 4.796-2.01 1.954.1 3.714 1.22 4.601 3.01.896 1.81.846 4.17-.514 6.67z"})})})}),(0,n.jsx)("span",{className:Y.likeCount,children:t})]}),(0,n.jsxs)("a",{className:Y.reply,href:e.reply_url,target:"_blank",rel:"noopener noreferrer","aria-label":"Reply to this Tweet on Twitter",children:[(0,n.jsx)("div",{className:Y.replyIconWrapper,children:(0,n.jsx)("svg",{viewBox:"0 0 24 24",className:Y.replyIcon,"aria-hidden":"true",children:(0,n.jsx)("g",{children:(0,n.jsx)("path",{d:"M1.751 10c0-4.42 3.584-8 8.005-8h4.366c4.49 0 8.129 3.64 8.129 8.13 0 2.96-1.607 5.68-4.196 7.11l-8.054 4.46v-3.69h-.067c-4.49.1-8.183-3.51-8.183-8.01z"})})})}),(0,n.jsx)("span",{className:Y.replyText,children:"Reply"})]}),(0,n.jsx)(Q,{tweet:e})]})};var Z=r(33266);let ee=({tweet:e})=>(0,n.jsx)("div",{className:Z.replies,children:(0,n.jsx)("a",{className:Z.link,href:e.url,target:"_blank",rel:"noopener noreferrer",children:(0,n.jsx)("span",{className:Z.text,children:0===e.conversation_count?"Read more on X":1===e.conversation_count?`Read ${M(e.conversation_count)} reply`:`Read ${M(e.conversation_count)} replies`})})});var et=r(93655);let er=({tweet:e,children:t})=>(0,n.jsx)("div",{className:et.root,onClick:t=>{t.preventDefault(),window.open(e.url,"_blank")},children:(0,n.jsx)("article",{className:et.article,children:t})});var en=r(79193);let ea=({tweet:e})=>{let{user:t}=e;return(0,n.jsxs)("div",{className:en.header,children:[(0,n.jsx)("a",{href:e.url,className:en.avatar,target:"_blank",rel:"noopener noreferrer",children:(0,n.jsx)("div",{className:(0,o.A)(en.avatarOverflow,"Square"===t.profile_image_shape&&en.avatarSquare),children:(0,n.jsx)(i,{src:t.profile_image_url_https,alt:t.name,width:20,height:20})})}),(0,n.jsxs)("div",{className:en.author,children:[(0,n.jsx)("div",{className:en.authorText,children:(0,n.jsx)("span",{title:t.name,children:t.name})}),(0,n.jsx)(p,{user:t}),(0,n.jsx)("div",{className:en.username,children:(0,n.jsxs)("span",{title:`@${t.screen_name}`,children:["@",t.screen_name]})})]})]})};var eo=r(40602);let ei=({tweet:e})=>(0,n.jsx)("p",{className:eo.root,lang:e.lang,dir:"auto",children:e.entities.map((e,t)=>(0,n.jsx)("span",{dangerouslySetInnerHTML:{__html:e.text}},t))}),el=({tweet:e})=>{var t;return(0,n.jsxs)(er,{tweet:e,children:[(0,n.jsx)(ea,{tweet:e}),(0,n.jsx)(ei,{tweet:e}),(null==(t=e.mediaDetails)?void 0:t.length)?(0,n.jsx)(H,{quoted:!0,tweet:e}):null]})},es=({tweet:e,components:t})=>{var r;let o=(0,x.useMemo)(()=>D(e),[e]);return(0,n.jsxs)(a.X,{children:[(0,n.jsx)(h,{tweet:o,components:t}),o.in_reply_to_status_id_str&&(0,n.jsx)(_,{tweet:o}),(0,n.jsx)(b,{tweet:o}),(null==(r=o.mediaDetails)?void 0:r.length)?(0,n.jsx)(H,{tweet:o,components:t}):null,o.quoted_tweet&&(0,n.jsx)(el,{tweet:o.quoted_tweet}),(0,n.jsx)(K,{tweet:o}),(0,n.jsx)(J,{tweet:o}),(0,n.jsx)(ee,{tweet:o})]})}},95046:(e,t,r)=>{"use strict";r.d(t,{A:()=>_});var n=r(37766),a=r.n(n),o=r(59717),i=r.n(o),l=r(30362),s=r(3892),u=r.n(s),c=r(82015),d=r(91353),f=r(80739),p=r(28004),h=r(8732);let m=c.forwardRef(({bsPrefix:e,split:t,className:r,childBsPrefix:n,as:o=d.A,...s},m)=>{let _=(0,f.oU)(e,"dropdown-toggle"),y=(0,c.useContext)(i());void 0!==n&&(s.bsPrefix=n);let[g]=(0,l.useDropdownToggle)();return g.ref=a()(g.ref,(0,p.A)(m,"DropdownToggle")),(0,h.jsx)(o,{className:u()(r,_,t&&`${_}-split`,(null==y?void 0:y.show)&&"show"),...g,...s})});m.displayName="DropdownToggle";let _=m},95504:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"default",{enumerable:!0,get:function(){return s}});let n=r(87020),a=r(8732),o=n._(r(82015)),i=r(54718);async function l(e){let{Component:t,ctx:r}=e;return{pageProps:await (0,i.loadGetInitialProps)(t,r)}}class s extends o.default.Component{render(){let{Component:e,pageProps:t}=this.props;return(0,a.jsx)(e,{...t})}}s.origGetInitialProps=l,s.getInitialProps=l,("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},96092:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"addBasePath",{enumerable:!0,get:function(){return o}});let n=r(36655),a=r(37779);function o(e,t){return(0,a.normalizePathTrailingSlash)((0,n.addPathPrefix)(e,""))}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},97580:(e,t,r)=>{"use strict";r.d(t,{X:()=>i});var n=r(8732),a=r(24562),o=r(59039);r(55786);let i=({className:e,children:t})=>(0,n.jsx)("div",{className:(0,a.A)("react-tweet-theme",o.root,e),children:(0,n.jsx)("article",{className:o.article,children:t})})},97825:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{HTML_LIMITED_BOT_UA_RE:function(){return n.HTML_LIMITED_BOT_UA_RE},HTML_LIMITED_BOT_UA_RE_STRING:function(){return o},getBotType:function(){return s},isBot:function(){return l}});let n=r(28537),a=/Googlebot|Google-PageRenderer|AdsBot-Google|googleweblight|Storebot-Google/i,o=n.HTML_LIMITED_BOT_UA_RE.source;function i(e){return n.HTML_LIMITED_BOT_UA_RE.test(e)}function l(e){return a.test(e)||i(e)}function s(e){return a.test(e)?"dom":i(e)?"html":void 0}},98132:(e,t,r)=>{"use strict";r.d(t,{A:()=>l});var n=r(82015),a=r(24132),o=r(8732);let i=n.forwardRef(({controlId:e,as:t="div",...r},i)=>{let l=(0,n.useMemo)(()=>({controlId:e}),[e]);return(0,o.jsx)(a.A.Provider,{value:l,children:(0,o.jsx)(t,{...r,ref:i})})});i.displayName="FormGroup";let l=i},98941:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"setAttributesFromProps",{enumerable:!0,get:function(){return o}});let r={acceptCharset:"accept-charset",className:"class",htmlFor:"for",httpEquiv:"http-equiv",noModule:"noModule"},n=["onLoad","onReady","dangerouslySetInnerHTML","children","onError","strategy","stylesheets"];function a(e){return["async","defer","noModule"].includes(e)}function o(e,t){for(let[o,i]of Object.entries(t)){if(!t.hasOwnProperty(o)||n.includes(o)||void 0===i)continue;let l=r[o]||o.toLowerCase();"SCRIPT"===e.tagName&&a(l)?e[l]=!!i:e.setAttribute(l,String(i)),(!1===i||"SCRIPT"===e.tagName&&a(l)&&(!i||"false"===i))&&(e.setAttribute(l,""),e.removeAttribute(l))}}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},99320:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{createRouteLoader:function(){return _},getClientBuildManifest:function(){return h},isAssetError:function(){return c},markAssetError:function(){return u}}),r(87020),r(28333);let n=r(88542),a=r(84841),o=r(79007),i=r(15296);function l(e,t,r){let n,a=t.get(e);if(a)return"future"in a?a.future:Promise.resolve(a);let o=new Promise(e=>{n=e});return t.set(e,{resolve:n,future:o}),r?r().then(e=>(n(e),e)).catch(r=>{throw t.delete(e),r}):o}let s=Symbol("ASSET_LOAD_ERROR");function u(e){return Object.defineProperty(e,s,{})}function c(e){return e&&s in e}let d=function(e){try{return e=document.createElement("link"),!!window.MSInputMethodContext&&!!document.documentMode||e.relList.supports("prefetch")}catch(e){return!1}}(),f=()=>(0,o.getDeploymentIdQueryOrEmptyString)();function p(e,t,r){return new Promise((n,o)=>{let i=!1;e.then(e=>{i=!0,n(e)}).catch(o),(0,a.requestIdleCallback)(()=>setTimeout(()=>{i||o(r)},t))})}function h(){return self.__BUILD_MANIFEST?Promise.resolve(self.__BUILD_MANIFEST):p(new Promise(e=>{let t=self.__BUILD_MANIFEST_CB;self.__BUILD_MANIFEST_CB=()=>{e(self.__BUILD_MANIFEST),t&&t()}}),3800,u(Object.defineProperty(Error("Failed to load client build manifest"),"__NEXT_ERROR_CODE",{value:"E273",enumerable:!1,configurable:!0})))}function m(e,t){return h().then(r=>{if(!(t in r))throw u(Object.defineProperty(Error("Failed to lookup route: "+t),"__NEXT_ERROR_CODE",{value:"E446",enumerable:!1,configurable:!0}));let a=r[t].map(t=>e+"/_next/"+(0,i.encodeURIPath)(t));return{scripts:a.filter(e=>e.endsWith(".js")).map(e=>(0,n.__unsafeCreateTrustedScriptURL)(e)+f()),css:a.filter(e=>e.endsWith(".css")).map(e=>e+f())}})}function _(e){let t=new Map,r=new Map,n=new Map,o=new Map;function i(e){{var t;let n=r.get(e.toString());return n?n:document.querySelector('script[src^="'+e+'"]')?Promise.resolve():(r.set(e.toString(),n=new Promise((r,n)=>{(t=document.createElement("script")).onload=r,t.onerror=()=>n(u(Object.defineProperty(Error("Failed to load script: "+e),"__NEXT_ERROR_CODE",{value:"E74",enumerable:!1,configurable:!0}))),t.crossOrigin=void 0,t.src=e,document.body.appendChild(t)})),n)}}function s(e){let t=n.get(e);return t||n.set(e,t=fetch(e,{credentials:"same-origin"}).then(t=>{if(!t.ok)throw Object.defineProperty(Error("Failed to load stylesheet: "+e),"__NEXT_ERROR_CODE",{value:"E189",enumerable:!1,configurable:!0});return t.text().then(t=>({href:e,content:t}))}).catch(e=>{throw u(e)})),t}return{whenEntrypoint:e=>l(e,t),onEntrypoint(e,r){(r?Promise.resolve().then(()=>r()).then(e=>({component:e&&e.default||e,exports:e}),e=>({error:e})):Promise.resolve(void 0)).then(r=>{let n=t.get(e);n&&"resolve"in n?r&&(t.set(e,r),n.resolve(r)):(r?t.set(e,r):t.delete(e),o.delete(e))})},loadRoute(r,n){return l(r,o,()=>{let a;return p(m(e,r).then(e=>{let{scripts:n,css:a}=e;return Promise.all([t.has(r)?[]:Promise.all(n.map(i)),Promise.all(a.map(s))])}).then(e=>this.whenEntrypoint(r).then(t=>({entrypoint:t,styles:e[1]}))),3800,u(Object.defineProperty(Error("Route did not complete loading: "+r),"__NEXT_ERROR_CODE",{value:"E12",enumerable:!1,configurable:!0}))).then(e=>{let{entrypoint:t,styles:r}=e,n=Object.assign({styles:r},t);return"error"in t?t:n}).catch(e=>{if(n)throw e;return{error:e}}).finally(()=>null==a?void 0:a())})},prefetch(t){let r;return(r=navigator.connection)&&(r.saveData||/2g/.test(r.effectiveType))?Promise.resolve():m(e,t).then(e=>Promise.all(d?e.scripts.map(e=>{var t,r,n;return t=e.toString(),r="script",new Promise((e,a)=>{let o='\n      link[rel="prefetch"][href^="'+t+'"],\n      link[rel="preload"][href^="'+t+'"],\n      script[src^="'+t+'"]';if(document.querySelector(o))return e();n=document.createElement("link"),r&&(n.as=r),n.rel="prefetch",n.crossOrigin=void 0,n.onload=e,n.onerror=()=>a(u(Object.defineProperty(Error("Failed to prefetch: "+t),"__NEXT_ERROR_CODE",{value:"E268",enumerable:!1,configurable:!0}))),n.href=t,document.head.appendChild(n)})}):[])).then(()=>{(0,a.requestIdleCallback)(()=>this.loadRoute(t,!0).catch(()=>{}))}).catch(()=>{})}}}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)}};