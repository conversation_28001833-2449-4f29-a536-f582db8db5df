(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[1081],{13214:(e,s,a)=>{(window.__NEXT_P=window.__NEXT_P||[]).push(["/operation/components/OperationAccordianSection",function(){return a(64990)}])},33458:(e,s,a)=>{"use strict";a.d(s,{A:()=>d});var i=a(37876),n=a(14232),c=a(65418),l=a(21772),o=a(11041),r=a(60282),t=a(31753);a(57637);let d=e=>{let{t:s}=(0,t.Bd)("common"),[a,d]=(0,n.useState)([]),p=e=>{let a=/(http|https):\/\/(\w+:{0,1}\w*)?(\S+)(:[0-9]+)?(\/|\/([\w#!:.?+=&%!\-\/]))?/.test(e.description);return(0,i.jsxs)("div",{className:"carousel-legend",children:[(0,i.jsxs)("p",{className:"lead",children:[(0,i.jsx)("b",{children:s("Filename")})," ",e.originalName||"No Name found"]}),e.description&&(0,i.jsxs)("div",{className:"source_link",children:[(0,i.jsx)("p",{children:(0,i.jsx)("b",{children:s("Source")})}),a?(0,i.jsxs)("div",{children:[(0,i.jsx)(l.g,{icon:o.CQO,size:"1x",color:"#999",className:"me-1"}),(0,i.jsx)("a",{className:"source_link",href:e.description,target:"_blank",rel:"noopener noreferrer",children:e.description})]}):(0,i.jsx)("div",{children:(0,i.jsx)("p",{className:"ps-0 py-0",style:{wordBreak:"break-all"},children:e.description})})]}),e.downloadLink&&(0,i.jsxs)(r.A,{className:"btn btn-success mt-2 btn--download",href:e.downloadLink,children:[s("Download"),(0,i.jsx)(l.g,{icon:o.cbP,size:"1x",className:"ms-1"})]})]})};return(0,n.useEffect)(()=>{let s=[];e&&e.gallery&&Array.isArray(e.gallery)&&e.gallery.map((a,i)=>{let n,c=a&&a.name.split(".").pop();switch(c){case"JPG":case"jpg":case"jpeg":case"png":n="".concat("http://localhost:3001/api/v1","/image/show/").concat(a._id);break;case"pdf":n="/images/fileIcons/pdfFile.png";break;case"docx":n="/images/fileIcons/wordFile.png";break;case"xls":case"xlsx":n="/images/fileIcons/xlsFile.png";break;default:n="/images/fileIcons/otherFile.png"}let l=("docx"===c||"pdf"===c||"xls"===c||"xlsx"===c)&&"".concat("http://localhost:3001/api/v1","/files/download/").concat(a._id),o="".concat(a&&a.original_name?a.original_name:"No Name found"),r=e.imageSource&&Array.isArray(e.imageSource)&&e.imageSource.length>0?e.imageSource[i]:"";s.push({src:n,description:r,originalName:o,downloadLink:l})}),d(s)},[e]),(0,i.jsx)("div",{children:a&&0===a.length?(0,i.jsx)("div",{className:"border border-info my-3 mx-0",children:(0,i.jsx)("p",{className:"d-flex d-flex justify-content-center p-2 m-0",children:s("NoFilesFound!")})}):(0,i.jsx)(c.FN,{showThumbs:!0,showStatus:!0,showIndicators:!0,infiniteLoop:!0,useKeyboardArrows:!0,autoPlay:!1,stopOnHover:!0,swipeable:!0,dynamicHeight:!1,emulateTouch:!0,renderThumbs:()=>a.map((e,s)=>(0,i.jsx)("img",{src:e.src,alt:"Thumbnail ".concat(s+1),style:{width:"60px",height:"60px",objectFit:"cover"}},s)),children:a.map((e,s)=>(0,i.jsxs)("div",{children:[(0,i.jsx)("img",{src:e.src,alt:e.originalName||"Gallery image",style:{maxHeight:"500px",objectFit:"contain"}}),p(e)]},s))})})}}},e=>{var s=s=>e(e.s=s);e.O(0,[7725,9773,1772,7126,8477,4672,636,6593,8792],()=>s(13214)),_N_E=e.O()}]);
//# sourceMappingURL=OperationAccordianSection-54378981ca3daf38.js.map