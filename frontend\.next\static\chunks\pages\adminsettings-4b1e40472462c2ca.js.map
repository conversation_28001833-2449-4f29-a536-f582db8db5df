{"version": 3, "file": "static/chunks/pages/adminsettings-4b1e40472462c2ca.js", "mappings": "+EACA,4CACA,iBACA,WACA,OAAe,EAAQ,KAA4C,CACnE,EACA,SAFsB,omBCDtB,IAAMA,EAAS,aACFC,EAAmBC,CAAAA,EAAAA,EAAAA,CAAAA,CAAoBA,CAAC,CACnDC,sBAAuB,GACjBC,IAAMC,WAAW,IAAID,EAAMC,WAAW,CAACC,YAAY,IAAIF,EAAMC,WAAW,CAACC,YAAY,CAACN,EAAO,CAKnGO,CALqG,kBAKjF,kBACtB,GAEaC,EAAgBN,CAAAA,EAAAA,EAAAA,CAAAA,CAAoBA,CAAC,CAChDC,sBAAuB,KACjBC,EAAMC,WAAW,IAAID,EAAMC,WAAW,CAACI,OAAO,IAAIL,EAAMC,WAAW,CAACI,OAAO,CAACT,EAAO,CAKzFO,CAL2F,kBAKvE,eACtB,GAAG,EAEmCL,CAAAA,EAAAA,EAAAA,CAAAA,CAAoBA,CAAC,CACzDC,sBAAwBC,KAClBA,EAAMC,WAAW,IAAID,EAAMC,WAAW,CAACK,iBAAiB,IAAIN,EAAMC,WAAW,CAACK,iBAAiB,CAACV,EAAO,CAK7GO,CAL+G,kBAK3F,wBACtB,GAAG,EAE8BL,CAAAA,EAAAA,EAAAA,CAAAA,CAAoBA,CAAC,CACpDC,sBAAuB,KACjBC,EAAMC,WAAW,IAAID,EAAMC,WAAW,CAACM,YAAY,IAAIP,EAAMC,WAAW,CAACM,YAAY,CAACX,EAAO,CAKnGO,CALqG,kBAKjF,mBACtB,GAAG,EAE4BL,CAAAA,EAAAA,EAAAA,CAAAA,CAAoBA,CAAC,CAClDC,sBAAuB,KACjBC,EAAMC,WAAW,IAAID,EAAMC,WAAW,CAACO,SAAS,IAAIR,EAAMC,WAAW,CAACO,SAAS,CAACZ,EAAO,CAK7FO,CAL+F,kBAK3E,iBACtB,GAAG,EAEqCL,CAAAA,EAAAA,EAAAA,CAAAA,CAAoBA,CAAC,CAC3DC,sBAAuB,KACjBC,EAAMC,WAAW,IAAID,EAAMC,WAAW,CAACQ,uBAAuB,IAAIT,EAAMC,WAAW,CAACQ,uBAAuB,CAACb,EAAO,CAKzHO,CAL2H,kBAKvG,0BACtB,GAAG,EAEiCL,CAAAA,EAAAA,EAAAA,CAAAA,CAAoBA,CAAC,CACvDC,sBAAuB,KACjBC,EAAMC,WAAW,IAAID,EAAMC,WAAW,CAACQ,uBAAuB,IAAIT,EAAMC,WAAW,CAACQ,uBAAuB,CAACb,EAAO,CAKzHO,CAL2H,kBAKvG,sBACtB,GAAG,EAC0BL,CAAAA,EAAAA,EAAAA,CAAAA,CAAoBA,CAAC,CAChDC,sBAAuB,KACjBC,EAAMC,WAAW,IAAID,EAAMC,WAAW,CAACS,MAAM,IAAIV,EAAMC,WAAW,CAACS,MAAM,CAACd,EAAO,CAKvFO,CALyF,kBAKrE,eACtB,GAAG,EAE8BL,CAAAA,EAAAA,EAAAA,CAAAA,CAAoBA,CAAC,CACpDC,sBAAwBC,KAClBA,EAAMC,WAAW,IAAID,EAAMC,WAAW,CAACU,WAAW,IAAIX,EAAMC,WAAW,CAACU,WAAW,CAACf,EAAO,CAKjGO,CALmG,kBAK/E,mBACtB,GAAG,EAEuCL,CAAAA,EAAAA,EAAAA,CAAAA,CAAoBA,CAAC,CAC7DC,sBAAuB,KACjBC,EAAMC,WAAW,IAAID,EAAMC,WAAW,CAACW,WAAW,IAAIZ,EAAMC,WAAW,CAACW,WAAW,CAAChB,EAAO,CAKjGO,CALmG,kBAK/E,4BACtB,GAAG,EAEuCL,CAAAA,EAAAA,EAAAA,CAAAA,CAAoBA,CAAC,CAC7DC,sBAAuB,KACjBC,EAAMC,WAAW,IAAID,EAAMC,WAAW,CAACY,mBAAmB,IAAIb,EAAMC,WAAW,CAACY,mBAAmB,CAACjB,EAAO,CAKjHO,CALmH,kBAK/F,4BACtB,GAAG,EAEoCL,CAAAA,EAAAA,EAAAA,CAAAA,CAAoBA,CAAC,CAC1DC,sBAAwBC,KAClBA,EAAMC,WAAW,IAAID,EAAMC,WAAW,CAACa,gBAAgB,IAAId,EAAMC,WAAW,CAACa,gBAAgB,CAAClB,EAAO,CAK3GO,CAL6G,kBAKzF,yBACtB,GAAG,EAEkCL,CAAAA,EAAAA,EAAAA,CAAAA,CAAoBA,CAAC,CACxDC,sBAAuB,KACjBC,EAAMC,WAAW,IAAID,EAAMC,WAAW,CAACc,gBAAgB,IAAIf,EAAMC,WAAW,CAACc,gBAAgB,CAACnB,EAAO,CAK3GO,CAL6G,kBAKzF,uBACtB,GAEaa,EAAsBlB,CAAAA,EAAAA,EAAAA,CAAAA,CAAoBA,CAAC,CACtDC,sBAAuB,KACjBC,EAAMC,WAAW,IAAID,EAAMC,WAAW,CAACgB,cAAc,IAAIjB,EAAMC,WAAW,CAACgB,cAAc,CAACrB,EAAO,CAKvGO,CALyG,kBAKrF,qBACtB,GAAG,EAE0BL,CAAAA,EAAAA,EAAAA,CAAAA,CAAoBA,CAAC,CAChDC,sBAAuB,GACjBC,IAAMC,WAAW,IAAID,EAAMC,WAAW,CAACiB,MAAM,IAAIlB,EAAMC,WAAW,CAACiB,MAAM,CAACtB,EAAO,CAKvFO,CALyF,kBAKrE,eACtB,GAAG,EAE6BL,CAAAA,EAAAA,EAAAA,CAAAA,CAAoBA,CAAC,CACnDC,sBAAuB,KACjBC,EAAMC,WAAW,IAAID,EAAMC,WAAW,CAACkB,UAAU,IAAInB,EAAMC,WAAW,CAACkB,UAAU,CAACvB,EAAO,CAK/FO,CALiG,kBAK7E,kBACtB,GAAG,EAE4BL,CAAAA,EAAAA,EAAAA,CAAAA,CAAoBA,CAAC,CAClDC,sBAAuB,KACjBC,EAAMC,WAAW,IAAID,EAAMC,WAAW,CAACmB,QAAQ,IAAIpB,EAAMC,WAAW,CAACmB,QAAQ,CAACxB,EAAO,CAK3FO,CAL6F,kBAKzE,iBACtB,GAAG,EAE8BL,CAAAA,EAAAA,EAAAA,CAAAA,CAAoBA,CAAC,CACpDC,sBAAuB,KACjBC,EAAMC,WAAW,IAAID,EAAMC,WAAW,CAACoB,WAAW,IAAIrB,EAAMC,WAAW,CAACoB,WAAW,CAACzB,EAAO,CAKjGO,CALmG,kBAK/E,mBACtB,GAAG,EAEwBL,CAAAA,EAAAA,EAAAA,CAAAA,CAAoBA,CAAC,CAC9CC,sBAAuB,KACjBC,EAAMC,WAAW,IAAID,EAAMC,WAAW,CAACqB,KAAK,IAAItB,EAAMC,WAAW,CAACqB,KAAK,CAAC1B,EAAO,CAKrFO,CALuF,kBAKnE,aACtB,GAAG,EAE8BL,CAAAA,EAAAA,EAAAA,CAAAA,CAAoBA,CAAC,CACpDC,sBAAuB,KACjBC,EAAMC,WAAW,IAAID,EAAMC,WAAW,CAACsB,WAAW,IAAIvB,EAAMC,WAAW,CAACsB,WAAW,CAAC3B,EAAO,CAKjGO,CALmG,kBAK/E,mBACtB,GAAG,EAE8BL,CAAAA,EAAAA,EAAAA,CAAAA,CAAoBA,CAAC,CACpDC,sBAAuB,KACjBC,EAAMC,WAAW,IAAID,EAAMC,WAAW,CAACuB,YAAY,IAAIxB,EAAMC,WAAW,CAACuB,YAAY,CAAC5B,EAAO,CAKnGO,CALqG,kBAKjF,mBACtB,GAAG,EAE0BL,CAAAA,EAAAA,EAAAA,CAAAA,CAAoBA,CAAC,CAChDC,sBAAuB,GACrB,EAAIC,EAAMC,WAAW,IAAID,EAAMC,WAAW,CAACwB,SAAS,IAAIzB,EAAMC,WAAW,CAACwB,SAAS,CAAC7B,EAAO,IAAII,EAAMC,WAAW,CAACyB,OAAO,IAAI1B,EAAMC,WAAW,CAACyB,OAAO,CAAC9B,EAAO,IAAGI,EAAMC,WAAW,CAAC0B,KAAK,IAAI3B,EAAMC,WAAW,CAAC0B,KAAK,CAAC/B,EAAO,IAAGI,EAAMC,WAAW,CAAC2B,MAAM,IAAI5B,EAAMC,WAAW,CAAC2B,MAAM,CAAChC,EAAO,IAAGI,EAAMC,WAAW,CAACW,WAAW,IAAIZ,EAAMC,WAAW,CAACW,WAAW,CAAChB,EAAO,IAAGI,EAAMC,WAAW,CAAC4B,MAAM,IAAI7B,EAAMC,WAAW,CAAC4B,MAAM,CAACjC,EAAO,EAAE,CAG5Z,EAETO,mBAAoB,eACtB,GAAG,EAEYN,gBAAgBA,EAAC,wFCzNhC,IAAMiC,EAAwBC,EAAAA,OAAb,GAA6B,CAAC,GAK5CC,IALyB,IAAoB,WAC9CC,CAAS,UACTC,CAAQ,CACRC,GAAIC,EAAY,KAAK,CACrB,GAAGC,EACJ,GAEC,OAAO,EADIC,CAAAA,EAAAA,EAAAA,EAAAA,CAAkBA,CACX,EADsB,aACpBC,CAAAA,EAAAA,EAAAA,GAAAA,CAAIA,CAACH,EAAW,CAClCJ,IAAKA,EACLC,UAAWO,IAAWP,EAAWC,GACjC,GAAGG,CAAK,EAEZ,GACAP,EAJyBU,WAIL,CAAG,WCbvB,IAAMC,EAA0BV,EAAAA,SAAb,CAA6B,CAAC,GAK9CC,MAL2B,EAAoB,CAChDC,WAAS,UACTC,CAAQ,CACRC,GAAIC,EAAY,KAAK,CACrB,GAAGC,EACJ,GAEC,OADAH,EAAWI,CAAAA,EAAAA,EAAAA,EAAAA,CAAkBA,CACX,EADsB,eACpBC,CAAAA,EAAAA,EAAAA,GAAAA,CAAIA,CAACH,EAAW,CAClCJ,IAAKA,EACLC,UAAWO,IAAWP,EAAWC,GACjC,GAAGG,CAAK,EAEZ,GACAI,EAAWC,WAAW,CAAG,4BCXzB,IAAMC,EAA0BZ,EAAAA,SAAb,CAA6B,CAAC,GAM9CC,MAN2B,EAAoB,UAChDE,CAAQ,WACRD,CAAS,CAETE,CADA,EACIC,EAAY,KAAK,CACrB,GAAGC,EACJ,GACOO,EAASN,CAAAA,EAAAA,EAAAA,EAAAA,CAAkBA,CAACJ,EAAU,eACtCW,EAAeC,CAAAA,EAAAA,EAAAA,OAAAA,CAAOA,CAAC,IAAO,EAClCC,mBAAoBH,EACtB,EAAI,CAACA,EAAO,EACZ,MAAoBL,CAAb,EAAaA,EAAAA,GAAAA,CAAIA,CAACS,EAAAA,CAAiBA,CAACC,QAAQ,CAAE,CACnDC,MAAOL,EACPM,SAAuBZ,CAAb,EAAaA,EAAAA,GAAAA,CAAIA,CAACH,EAAP,CACnBJ,IAAKA,EACL,GAAGK,CAAK,CACRJ,UAAWO,IAAWP,EAAWW,EACnC,EACF,EACF,GACAD,EAAWD,GAJgBF,QAIL,CAAG,aCtBzB,IAAMY,EAAuBrB,EAAAA,MAAb,IAA6B,CAC7C,CACA,EAMGC,GARwB,KAE1B,UACCE,CAAQ,CACRD,WAAS,CACToB,SAAO,CACPlB,GAAIC,EAAY,KAAK,CACrB,GAAGC,EACJ,GACOO,EAASN,CAAAA,EAAAA,EAAAA,EAAAA,CAAkBA,CAACJ,EAAU,YAC5C,MAAoBK,CAAAA,EAAAA,EAAAA,GAAAA,CAAIA,CAACH,EAAP,CAChBJ,IAAKA,EACLC,UAAWO,IAAWa,EAAU,GAAaA,MAAAA,CAAVT,EAAO,EAArBJ,GAAgC,OAARa,CAX0G,EAW9FT,EAAQX,GACjE,GAAGI,CAAK,EAEZ,GACAe,EAAQV,WAAW,CAAG,UChBtB,IAAMY,EAA8BvB,EAAAA,UAAgB,CAAC,EAA9B,CAKpBC,QALmD,EAApB,SAChCC,CAAS,UACTC,CAAQ,CACRC,GAAIC,EAAY,KAAK,CACrB,GAAGC,EACJ,GAEC,OAAO,EADIC,CAAAA,EAAAA,EAAAA,EAAAA,CAAkBA,CAACJ,EAAU,oBACpBK,CAAAA,EAAAA,EAAAA,GAAAA,CAAIA,CAACH,EAAW,CAClCJ,IAAKA,EACLC,UAAWO,IAAWP,EAAWC,GACjC,GAAGG,CAAK,EAEZ,GACAiB,EAJyBd,WAIC,CAAG,iBCb7B,IAAMe,EAAwBxB,EAAAA,OAAb,GAA6B,CAAC,GAK5CC,IALyB,IAAoB,WAC9CC,CAAS,UACTC,CAAQ,CACRC,GAAIC,EAAY,GAAG,CACnB,GAAGC,EACJ,GAEC,OADAH,EAAWI,CAAAA,EAAAA,EAAAA,EAAAA,CAAkBA,CACX,EADsB,aACpBC,CAAAA,EAAAA,EAAAA,GAAAA,CAAIA,CAACH,EAAW,CAClCJ,IAAKA,EACLC,UAAWO,IAAWP,EAAWC,GACjC,GAAGG,CACL,EACF,GACAkB,EAASb,WAAW,CAAG,0BCZvB,IAAMc,EAAgBC,CAAAA,EAAAA,EAAAA,CAAAA,CAAgBA,CAAC,MACjCC,EAA4B3B,EAAAA,UAAgB,CAAC,GAKhDC,QALiD,WAClDC,CAAS,CACTC,UAAQ,CACRC,GAAIC,EAAYoB,CAAa,CAC7B,GAAGnB,EACJ,GAEC,OADAH,EAAWI,CAAAA,EAAAA,EAAAA,EAAAA,CAAkBA,CACX,EADsB,iBACpBC,CAAAA,EAAAA,EAAAA,GAAAA,CAAIA,CAACH,EAAW,CAClCJ,IAAKA,EACLC,UAAWO,IAAWP,EAAWC,GACjC,GAAGG,CAAK,EAEZ,GACAqB,EAAahB,WAAW,CAAG,eCf3B,IAAMiB,EAAwB5B,EAAAA,OAAb,GAA6B,CAAC,GAK5CC,IALyB,IAAoB,WAC9CC,CAAS,CACTC,UAAQ,CACRC,GAAIC,EAAY,GAAG,CACnB,GAAGC,EACJ,GAEC,OADAH,EAAWI,CAAAA,EAAAA,EAAAA,EAAAA,CAAkBA,CACX,EADsB,aACpBC,CAAAA,EAAAA,EAAAA,GAAAA,CAAIA,CAACH,EAAW,CAClCJ,IAAKA,EACLC,UAAWO,IAAWP,EAAWC,GACjC,GAAGG,CAAK,EAEZ,GACAsB,EAASjB,WAAW,CAAG,WCZvB,IAAMkB,EAAgBH,CAAAA,EAAAA,EAAAA,CAAAA,CAAgBA,CAAC,MACjCI,EAAyB9B,EAAAA,QAAb,EAA6B,CAAC,GAK7CC,KAL0B,GAAoB,WAC/CC,CAAS,UACTC,CAAQ,CACRC,GAAIC,EAAYwB,CAAa,CAC7B,GAAGvB,EACJ,GAEC,OADAH,EAAWI,CAAAA,EAAAA,EAAAA,EAAAA,CAAkBA,CAACJ,EAAU,cACpBK,CAAAA,EAAAA,EAAAA,GAAAA,CAAIA,CAACH,EAAW,CAClCJ,IAAKA,EACLC,UAAWO,IAAWP,EAAWC,GACjC,GAAGG,CAAK,EAEZ,GACAwB,EAAUnB,WAAW,CAAG,YCNxB,IAAMoB,EAAoB/B,EAAAA,GAAb,OAA6B,CAAC,GAAnB,QAAoB,UAC1CG,CAAQ,WACRD,CAAS,CACT8B,IAAE,MACFC,CAAI,QACJC,CAAM,CACNC,QAAO,CAAK,CACZf,UAAQ,CAERhB,CADA,EACIC,EAAY,KAAK,CACrB,GAAGC,EACJ,GACOO,EAASN,CAAAA,EAAAA,EAAAA,EAAAA,CAAkBA,CAACJ,EAAU,QAC5C,MAAoBK,CAAb,EAAaA,EAAAA,GAAAA,CAAIA,CAACH,EAAP,CAChBJ,IAAKA,EACL,GAAGK,CAAK,CACRJ,UAAWO,IAAWP,EAAWW,EAAQmB,GAAM,MAAS,GAAnCvB,GAAmC,CAAHuB,GAAMC,GAAQ,QAAa,OAALA,GAAQC,GAAU,UAAiB,OAAPA,IACvGd,IATyJ,KAS/Ie,EAAoB3B,CAAAA,EAAAA,EAAb,GAAaA,CAAIA,CAACT,EAAU,CAC3CqB,GAD0B,MAAerB,CAE3C,GAAKqB,CACP,EACF,GACAW,EAAKpB,WAAW,CAAG,OACnB,MAAeyB,OAAOC,MAAM,CAACN,EAAM,CACjCO,INhBajB,CMgBRA,CACLkB,KNjBoBlB,CKDPS,CLCQ,CMkBrBU,EAFYnB,KDjBUS,EAAC,CCmBbH,CACVc,CAFgBX,ITpBH/B,CSsBPA,CACN2C,GHrByBf,EDFZH,CLAQzB,CSwBrB4C,CAHsBhB,GACR5B,CFtBD6B,CFAQJ,CIyBrBoB,CJzBsB,GIuBRpB,EFvBOI,CLSRhB,COgBLA,CACRiC,EAFcjB,KRxBDlB,CQ0BLA,CACRoC,CPlBwB,GOgBNlC,IRzBKF,EAAC,CGAXa,CK2BDA,CADMb,CAElB,EAAC,SL5B0Ba,EAAC,GK2BFA,+CC3Cb,SAASwB,EAAYzC,CAAuB,EACzD,MACE,UAAC0C,KAAAA,CAAG9C,UAAU,wBAAgBI,EAAM2C,KAAK,EAE7C,uNCkpBA,MAxnBc,KACZ,GAAM,GAAEC,CAAC,CAAE,CAAGC,CAAAA,EAAAA,EAAAA,EAAAA,CAAcA,CAAC,KAunBhBC,KArnBPC,EAAa,IAEf,UAACC,EAAAA,CAAGA,CAAAA,CAACC,GAAI,EAAGC,GAAI,GAAItD,UAAW,qBAC7B,UAACuD,MAAAA,CAAIvD,UAAU,0DACb,UAACwC,IAAIA,CACHgB,KAAK,6BACLtD,GAAG,OAFAsC,gCAIH,UAACX,EAAAA,CAAIA,CAAAA,CAAC7B,UAAU,qBACd,UAAC6B,EAAAA,CAAIA,CAACU,IAAI,WACR,WAACV,EAAAA,CAAIA,CAACY,IAAI,YACPO,EAAE,sCACH,UAACS,OAAAA,CAAKzD,UAAU,sBACZ,UAAC0D,EAAAA,CAAeA,CAAAA,CAACC,KAAMC,EAAAA,GAAYA,kBAWjDC,EAAY,IAEd,UAACT,EAAAA,CAAGA,CAAAA,CAACC,GAAI,EAAGC,GAAI,GAAItD,UAAW,qBAC7B,UAACuD,MAAAA,CAAIvD,UAAU,0DACb,UAACwC,IAAIA,CACHgB,KAAK,6BACLtD,GAAG,OAFAsC,2BAIH,UAACX,EAAAA,CAAIA,CAAAA,CAAC7B,UAAU,oBACd,UAAC6B,EAAAA,CAAIA,CAACU,IAAI,WACR,WAACV,EAAAA,CAAIA,CAACY,IAAI,YACPO,EAAE,qCACH,UAACS,OAAAA,CAAKzD,UAAU,sBACZ,UAAC0D,EAAAA,CAAeA,CAAAA,CAACC,KAAMC,EAAAA,GAAYA,kBAWjDE,EAAmB,IAErB,UAACV,EAAAA,CAAGA,CAAAA,CAACC,GAAI,EAAGC,GAAI,GAAItD,UAAW,qBAC7B,UAACuD,MAAAA,CAAIvD,UAAU,2DACb,UAACwC,IAAIA,CACHgB,KAAK,6BACLtD,GAAG,OAFAsC,oCAIH,UAACX,EAAAA,CAAIA,CAAAA,CAAC7B,UAAU,oBACd,UAAC6B,EAAAA,CAAIA,CAACU,IAAI,WACR,WAACV,EAAAA,CAAIA,CAACY,IAAI,YACPO,EAAE,4CACH,UAACS,OAAAA,CAAKzD,UAAU,sBACZ,UAAC0D,EAAAA,CAAeA,CAAAA,CAACC,KAAMC,EAAAA,GAAYA,kBAWjDG,EAAc,IAEhB,UAACX,EAAAA,CAAGA,CAAAA,CAACC,GAAI,EAAGC,GAAI,GAAItD,UAAW,qBAC7B,UAACuD,MAAAA,CAAIvD,UAAU,0DACb,UAACwC,IAAIA,CACHgB,KAAK,6BACLtD,GAAG,OAFAsC,+BAIH,UAACX,EAAAA,CAAIA,CAAAA,CAAC7B,UAAU,oBACd,UAAC6B,EAAAA,CAAIA,CAACU,IAAI,WACR,WAACV,EAAAA,CAAIA,CAACY,IAAI,YACPO,EAAE,uCACC,UAACS,OAAAA,CAAKzD,UAAU,sBAChB,UAAC0D,EAAAA,CAAeA,CAAAA,CAACC,KAAMC,EAAAA,GAAYA,kBAWjDI,EAAY,IAEd,UAACZ,EAAAA,CAAGA,CAAAA,CAACC,GAAI,EAAGC,GAAI,GAAItD,UAAW,qBAC7B,UAACuD,MAAAA,CAAIvD,UAAU,0DACb,UAACwC,IAAIA,CACHgB,KAAK,6BACLtD,GAAG,OAFAsC,6BAIH,UAACX,EAAAA,CAAIA,CAAAA,CAAC7B,UAAU,oBACd,UAAC6B,EAAAA,CAAIA,CAACU,IAAI,WACR,WAACV,EAAAA,CAAIA,CAACY,IAAI,YAEPO,EAAE,qCACC,UAACS,OAAAA,CAAKzD,UAAU,sBAChB,UAAC0D,EAAAA,CAAeA,CAAAA,CAACC,KAAMC,EAAAA,GAAYA,kBAYjDK,EAAqB,IAEvB,UAACb,EAAAA,CAAGA,CAAAA,CAACC,GAAI,EAAGC,GAAI,GAAItD,UAAW,qBAC7B,UAACuD,MAAAA,CAAIvD,UAAU,0DACb,UAACwC,IAAIA,CACHgB,KAAK,6BACLtD,GAAG,OAFAsC,+BAIH,UAACX,EAAAA,CAAIA,CAAAA,CAAC7B,UAAU,oBACd,UAAC6B,EAAAA,CAAIA,CAACU,IAAI,WACR,WAACV,EAAAA,CAAIA,CAACY,IAAI,YAEPO,EAAE,8CACH,UAACS,OAAAA,CAAKzD,UAAU,sBACZ,UAAC0D,EAAAA,CAAeA,CAAAA,CAACC,KAAMC,EAAAA,GAAYA,kBAYjDM,EAAiB,IAEnB,UAACd,EAAAA,CAAGA,CAAAA,CAACC,GAAI,EAAGC,GAAI,GAAItD,UAAW,qBAC7B,UAACuD,MAAAA,CAAIvD,UAAU,0DACb,UAACwC,IAAIA,CACHgB,KAAK,6BACLtD,GAAG,OAFAsC,gCAIH,UAACX,EAAAA,CAAIA,CAAAA,CAAC7B,UAAU,oBACd,UAAC6B,EAAAA,CAAIA,CAACU,IAAI,WACR,WAACV,EAAAA,CAAIA,CAACY,IAAI,YAEPO,EAAE,0CACH,UAACS,OAAAA,CAAKzD,UAAU,sBACZ,UAAC0D,EAAAA,CAAeA,CAAAA,CAACC,KAAMC,EAAAA,GAAYA,kBAYjDO,EAAU,IAEZ,UAACf,EAAAA,CAAGA,CAAAA,CAACC,GAAI,EAAGC,GAAI,GAAItD,UAAW,qBAC7B,UAACuD,MAAAA,CAAIvD,UAAU,0DACb,UAACwC,IAAIA,CACHgB,KAAK,6BACLtD,GAAG,OAFAsC,0BAIH,UAACX,EAAAA,CAAIA,CAAAA,CAAC7B,UAAU,oBACd,UAAC6B,EAAAA,CAAIA,CAACU,IAAI,WACR,WAACV,EAAAA,CAAIA,CAACY,IAAI,YAEPO,EAAE,mCACC,UAACS,OAAAA,CAAKzD,UAAU,sBAChB,UAAC0D,EAAAA,CAAeA,CAAAA,CAACC,KAAMC,EAAAA,GAAYA,kBAYjDQ,EAAc,IAEhB,UAAChB,EAAAA,CAAGA,CAAAA,CAACC,GAAI,EAAGC,GAAI,GAAItD,UAAW,qBAC7B,UAACuD,MAAAA,CAAIvD,UAAU,0DACb,UAACwC,IAAIA,CACHgB,KAAK,6BACLtD,GAAG,OAFAsC,+BAIH,UAACX,EAAAA,CAAIA,CAAAA,CAAC7B,UAAU,oBACd,UAAC6B,EAAAA,CAAIA,CAACU,IAAI,WACR,WAACV,EAAAA,CAAIA,CAACY,IAAI,YAEPO,EAAE,uCACC,UAACS,OAAAA,CAAKzD,UAAU,sBAChB,UAAC0D,EAAAA,CAAeA,CAAAA,CAACC,KAAMC,EAAAA,GAAYA,kBAYjDS,EAAuB,IAEzB,UAACjB,EAAAA,CAAGA,CAAAA,CAACC,GAAI,EAAGC,GAAI,GAAItD,UAAW,qBAC7B,UAACuD,MAAAA,CAAIvD,UAAU,0DACb,UAACwC,IAAIA,CACHgB,KAAK,6BACLtD,GAAG,OAFAsC,wCAIH,UAACX,EAAAA,CAAIA,CAAAA,CAAC7B,UAAU,oBACd,UAAC6B,EAAAA,CAAIA,CAACU,IAAI,WACR,WAACV,EAAAA,CAAIA,CAACY,IAAI,YAEPO,EAAE,gDACH,UAACS,OAAAA,CAAKzD,UAAU,sBACZ,UAAC0D,EAAAA,CAAeA,CAAAA,CAACC,KAAMC,EAAAA,GAAYA,kBAYjDU,EAAuB,IAEzB,UAAClB,EAAAA,CAAGA,CAAAA,CAACC,GAAI,EAAGC,GAAI,GAAItD,UAAW,qBAC7B,UAACuD,MAAAA,CAAIvD,UAAU,0DACb,UAACwC,IAAIA,CACHgB,KAAK,6BACLtD,GAAG,OAFAsC,uCAIH,UAACX,EAAAA,CAAIA,CAAAA,CAAC7B,UAAU,oBACd,UAAC6B,EAAAA,CAAIA,CAACU,IAAI,WACR,WAACV,EAAAA,CAAIA,CAACY,IAAI,YAEPO,EAAE,gDACH,UAACS,OAAAA,CAAKzD,UAAU,sBACZ,UAAC0D,EAAAA,CAAeA,CAAAA,CAACC,KAAMC,EAAAA,GAAYA,kBAYjDW,EAAoB,IAEtB,UAACnB,EAAAA,CAAGA,CAAAA,CAACC,GAAI,EAAGC,GAAI,GAAItD,UAAW,qBAC7B,UAACuD,MAAAA,CAAIvD,UAAU,0DACb,UAACwC,IAAIA,CACHgB,KAAK,6BACLtD,GAAG,OAFAsC,oCAIH,UAACX,EAAAA,CAAIA,CAAAA,CAAC7B,UAAU,oBACd,UAAC6B,EAAAA,CAAIA,CAACU,IAAI,WACR,WAACV,EAAAA,CAAIA,CAACY,IAAI,YAEPO,EAAE,6CACH,UAACS,OAAAA,CAAKzD,UAAU,sBACZ,UAAC0D,EAAAA,CAAeA,CAAAA,CAACC,KAAMC,EAAAA,GAAYA,kBAYjDY,EAAkB,IAEpB,UAACpB,EAAAA,CAAGA,CAAAA,CAACC,GAAI,EAAGC,GAAI,GAAItD,UAAW,qBAC7B,UAACuD,MAAAA,CAAIvD,UAAU,0DACb,UAACwC,IAAIA,CACHgB,KAAK,6BACLtD,GAAG,OAFAsC,mCAIH,UAACX,EAAAA,CAAIA,CAAAA,CAAC7B,UAAU,oBACd,UAAC6B,EAAAA,CAAIA,CAACU,IAAI,WACR,WAACV,EAAAA,CAAIA,CAACY,IAAI,YAEPO,EAAE,2CACL,UAACS,OAAAA,CAAKzD,UAAU,sBACV,UAAC0D,EAAAA,CAAeA,CAAAA,CAACC,KAAMC,EAAAA,GAAYA,kBAYjDa,EAAgB,IAElB,UAACrB,EAAAA,CAAGA,CAAAA,CAACC,GAAI,EAAGC,GAAI,GAAItD,UAAW,qBAC7B,UAACuD,MAAAA,CAAIvD,UAAU,0DACb,UAACwC,IAAIA,CACHgB,KAAK,6BACLtD,GAAG,OAFAsC,iCAIH,UAACX,EAAAA,CAAIA,CAAAA,CAAC7B,UAAU,oBACd,UAAC6B,EAAAA,CAAIA,CAACU,IAAI,WACR,WAACV,EAAAA,CAAIA,CAACY,IAAI,YAEPO,EAAE,yCACL,UAACS,OAAAA,CAAKzD,UAAU,sBACV,UAAC0D,EAAAA,CAAeA,CAAAA,CAACC,KAAMC,EAAAA,GAAYA,kBAYjDc,EAAU,IAEZ,UAACtB,EAAAA,CAAGA,CAAAA,CAACC,GAAI,EAAGC,GAAI,GAAItD,UAAW,qBAC7B,UAACuD,MAAAA,CAAIvD,UAAU,0DACb,UAACwC,IAAIA,CACHgB,KAAK,6BACLtD,GAAG,OAFAsC,0BAIH,UAACX,EAAAA,CAAIA,CAAAA,CAAC7B,UAAU,oBACd,UAAC6B,EAAAA,CAAIA,CAACU,IAAI,WACR,WAACV,EAAAA,CAAIA,CAACY,IAAI,YAEPO,EAAE,mCACL,UAACS,OAAAA,CAAKzD,UAAU,sBACV,UAAC0D,EAAAA,CAAeA,CAAAA,CAACC,KAAMC,EAAAA,GAAYA,kBAYjDe,EAAa,IAEf,UAACvB,EAAAA,CAAGA,CAAAA,CAACC,GAAI,EAAGC,GAAI,GAAItD,UAAW,qBAC7B,UAACuD,MAAAA,CAAIvD,UAAU,0DACb,UAACwC,IAAIA,CACHgB,KAAK,6BACLtD,GAAG,OAFAsC,6BAIH,UAACX,EAAAA,CAAIA,CAAAA,CAAC7B,UAAU,oBACd,UAAC6B,EAAAA,CAAIA,CAACU,IAAI,WACR,WAACV,EAAAA,CAAIA,CAACY,IAAI,YAEPO,EAAE,sCACL,UAACS,OAAAA,CAAKzD,UAAU,sBACV,UAAC0D,EAAAA,CAAeA,CAAAA,CAACC,KAAMC,EAAAA,GAAYA,kBAYjDgB,EAAY,IAEd,UAACxB,EAAAA,CAAGA,CAAAA,CAACC,GAAI,EAAGC,GAAI,GAAItD,UAAW,qBAC7B,UAACuD,MAAAA,CAAIvD,UAAU,0DACb,UAACwC,IAAIA,CACHgB,KAAK,6BACLtD,GAAG,OAFAsC,4BAIH,UAACX,EAAAA,CAAIA,CAAAA,CAAC7B,UAAU,oBACd,UAAC6B,EAAAA,CAAIA,CAACU,IAAI,WACR,WAACV,EAAAA,CAAIA,CAACY,IAAI,YAEPO,EAAE,qCACL,UAACS,OAAAA,CAAKzD,UAAU,sBACV,UAAC0D,EAAAA,CAAeA,CAAAA,CAACC,KAAMC,EAAAA,GAAYA,kBAYjDiB,EAAc,IAEhB,UAACzB,EAAAA,CAAGA,CAAAA,CAACC,GAAI,EAAGC,GAAI,GAAItD,UAAW,qBAC7B,UAACuD,MAAAA,CAAIvD,UAAU,0DACb,UAACwC,IAAIA,CACHgB,KAAK,6BACLtD,GAAG,OAFAsC,+BAIH,UAACX,EAAAA,CAAIA,CAAAA,CAAC7B,UAAU,oBACd,UAAC6B,EAAAA,CAAIA,CAACU,IAAI,WACR,WAACV,EAAAA,CAAIA,CAACY,IAAI,YAEPO,EAAE,wCACL,UAACS,OAAAA,CAAKzD,UAAU,sBACV,UAAC0D,EAAAA,CAAeA,CAAAA,CAACC,KAAMC,EAAAA,GAAYA,kBAYjDkB,EAAQ,IAEV,UAAC1B,EAAAA,CAAGA,CAAAA,CAAEC,GAAI,EAAGC,GAAI,GAAItD,UAAW,qBAC9B,UAACuD,MAAAA,CAAIvD,UAAU,0DACb,UAACwC,IAAIA,CACHgB,KAAK,6BACLtD,GAAG,OAFAsC,yBAIH,UAACX,EAAAA,CAAIA,CAAAA,CAAC7B,UAAU,oBACd,UAAC6B,EAAAA,CAAIA,CAACU,IAAI,WACR,WAACV,EAAAA,CAAIA,CAACY,IAAI,YAEPO,EAAE,iCACC,UAACS,OAAAA,CAAKzD,UAAU,sBAChB,UAAC0D,EAAAA,CAAeA,CAAAA,CAACC,KAAMC,EAAAA,GAAYA,kBAYjDmB,EAAe,IAEjB,UAAC3B,EAAAA,CAAGA,CAAAA,CAAEC,GAAI,EAAGC,GAAI,GAAItD,UAAW,qBAC9B,UAACuD,MAAAA,CAAIvD,UAAU,0DACb,UAACwC,IAAIA,CACHgB,KAAK,6BACLtD,GAAG,OAFAsC,+BAIH,UAACX,EAAAA,CAAIA,CAAAA,CAAC7B,UAAU,oBACd,UAAC6B,EAAAA,CAAIA,CAACU,IAAI,WACR,WAACV,EAAAA,CAAIA,CAACY,IAAI,YAEPO,EAAE,wCACC,UAACS,OAAAA,CAAKzD,UAAU,sBAChB,UAAC0D,EAAAA,CAAeA,CAAAA,CAACC,KAAMC,EAAAA,GAAYA,kBAYjDoB,EAAc,IAEhB,UAAC5B,EAAAA,CAAGA,CAAAA,CAAEC,GAAI,EAAGC,GAAI,GAAItD,UAAW,qBAC9B,UAACuD,MAAAA,CAAIvD,UAAU,0DACb,UAACwC,IAAIA,CACHgB,KAAK,6BACLtD,GAAG,OAFAsC,2BAIH,UAACX,EAAAA,CAAIA,CAAAA,CAAC7B,UAAU,oBACd,UAAC6B,EAAAA,CAAIA,CAACU,IAAI,WACR,WAACV,EAAAA,CAAIA,CAACY,IAAI,YAEPO,EAAE,2CACH,UAACS,OAAAA,CAAKzD,UAAU,sBACZ,UAAC0D,EAAAA,CAAeA,CAAAA,CAACC,KAAMC,EAAAA,GAAYA,kBAYjDqB,EAAU,IAEZ,UAAC7B,EAAAA,CAAGA,CAAAA,CAAEC,GAAI,EAAGC,GAAI,GAAItD,UAAW,qBAC9B,UAACuD,MAAAA,CAAIvD,UAAU,0DACb,UAACwC,IAAIA,CACHgB,KAAK,6BACLtD,GAAG,OAFAsC,2BAIH,UAACX,EAAAA,CAAIA,CAAAA,CAAC7B,UAAU,oBACd,UAAC6B,EAAAA,CAAIA,CAACU,IAAI,WACR,WAACV,EAAAA,CAAIA,CAACY,IAAI,YAELO,EAAE,mCACD,UAACS,OAAAA,CAAKzD,UAAU,sBAChB,UAAC0D,EAAAA,CAAeA,CAAAA,CAACC,KAAMC,EAAAA,GAAYA,kBAajDsB,EAAmBtH,CAAAA,EAAAA,EAAAA,gBAAAA,CAAgBA,CAAC,IAAM,UAACuF,EAAAA,CAAAA,IAC3CgC,EAAgBhH,CAAAA,EAAAA,EAAAA,aAAAA,CAAaA,CAAC,IAAM,UAAC0F,EAAAA,CAAAA,IACrCuB,EAAyBC,CAAAA,EAAAA,EAAAA,sBAAAA,CAAsBA,CAAC,IAAM,UAACvB,EAAAA,CAAAA,IACvDwB,EAAoBC,CAAAA,EAAAA,EAAAA,iBAAAA,CAAiBA,CAAC,IAAM,UAACxB,EAAAA,CAAAA,IAC7CyB,EAAkBC,CAAAA,EAAAA,EAAAA,eAAAA,CAAeA,CAAC,IAAM,UAACzB,EAAAA,CAAAA,IACzC0B,EAA2BC,CAAAA,EAAAA,EAAAA,wBAAAA,CAAwBA,CAAC,IAAM,UAAC1B,EAAAA,CAAAA,IAC3D2B,EAAuBC,CAAAA,EAAAA,EAAAA,oBAAAA,CAAoBA,CAAC,IAAM,UAAC3B,EAAAA,CAAAA,IAEnD4B,EAAgBC,CAAAA,EAAAA,EAAAA,aAAAA,CAAaA,CAAC,IAAM,UAAC5B,EAAAA,CAAAA,IACrC6B,EAAoBC,CAAAA,EAAAA,EAAAA,iBAAAA,CAAiBA,CAAC,IAAM,UAAC7B,EAAAA,CAAAA,IAC7C8B,EAA6BC,CAAAA,EAAAA,EAAAA,0BAAAA,CAA0BA,CAAC,IAAM,UAAC9B,EAAAA,CAAAA,IAC/D+B,EAA6BC,CAAAA,EAAAA,EAAAA,0BAAAA,CAA0BA,CAAC,IAAM,UAAC/B,EAAAA,CAAAA,IAC/DgC,EAA0BC,CAAAA,EAAAA,EAAAA,uBAAAA,CAAuBA,CAAC,IAAM,UAAChC,EAAAA,CAAAA,IACzDiC,EAAwBC,CAAAA,EAAAA,EAAAA,qBAAAA,CAAqBA,CAAC,IAAM,UAACjC,EAAAA,CAAAA,IACrDkC,EAAsB3H,CAAAA,EAAAA,EAAAA,mBAAAA,CAAmBA,CAAC,IAAM,UAAC0F,EAAAA,CAAAA,IACjDkC,EAAgBC,CAAAA,EAAAA,EAAAA,aAAAA,CAAaA,CAAC,IAAM,UAAClC,EAAAA,CAAAA,IACrCmC,EAAmBC,CAAAA,EAAAA,EAAAA,gBAAAA,CAAgBA,CAAC,IAAM,UAACnC,EAAAA,CAAAA,IAC3CoC,EAAkBC,CAAAA,EAAAA,EAAAA,eAAAA,CAAeA,CAAC,IAAM,UAACpC,EAAAA,CAAAA,IACzCqC,EAAoBC,CAAAA,EAAAA,EAAAA,iBAAAA,CAAiBA,CAAC,IAAM,UAACrC,EAAAA,CAAAA,IAC7CsC,EAAcC,CAAAA,EAAAA,EAAAA,WAAAA,CAAWA,CAAC,IAAM,UAACtC,EAAAA,CAAAA,IACjCuC,EAAoBC,CAAAA,EAAAA,EAAAA,iBAAAA,CAAiBA,CAAC,IAAM,UAACvC,EAAAA,CAAAA,IAC7CwC,GAAoBC,CAAAA,EAAAA,EAAAA,iBAAAA,CAAiBA,CAAC,IAAM,UAACxC,EAAAA,CAAAA,IAC7CyC,GAAgBC,CAAAA,EAAAA,EAAAA,aAAAA,CAAaA,CAAC,IAAM,UAACzC,EAAAA,CAAAA,IAG3C,MACE,WAAC0C,EAAAA,CAASA,CAAAA,CAACC,KAAK,IAACC,MAAO,CAAEC,UAAW,QAAS,EAAG9H,UAAU,gBACzD,UAAC6C,EAAAA,CAAWA,CAAAA,CAACE,MAAOC,EAAE,wBACtB,WAAC+E,EAAAA,CAAGA,CAAAA,WACF,UAAC7C,EAAAA,CAAAA,GACD,UAACC,EAAAA,CAAAA,GACD,UAACC,EAAAA,CAAAA,GACD,UAACE,EAAAA,CAAAA,GACD,UAACE,EAAAA,CAAAA,GACD,UAACE,EAAAA,CAAAA,GACD,UAACE,EAAAA,CAAAA,GACD,UAACE,EAAAA,CAAAA,GACD,UAACE,EAAAA,CAAAA,GACD,UAACE,EAAAA,CAAAA,GACD,UAACE,EAAAA,CAAAA,GACD,UAACE,EAAAA,CAAAA,GACD,UAACE,EAAAA,CAAAA,GACD,UAACE,EAAAA,CAAAA,GACD,UAACC,EAAAA,CAAAA,GACD,UAACE,EAAAA,CAAAA,GACD,UAACE,EAAAA,CAAAA,GACD,UAACE,EAAAA,CAAAA,GACD,UAACE,EAAAA,CAAAA,GACD,UAACE,EAAAA,CAAAA,GACD,UAACE,GAAAA,CAAAA,GACD,UAACE,GAAAA,CAAAA,QAIT,iDC7oBA,IAAMO,EAAuBlI,QAAb,CAAaA,UAAF,GAAqB,CAAC,MACjDkI,EAAQvH,WAAW,CAAG,oBACtB,MAAeuH,OAAOA,EAAC", "sources": ["webpack://_N_E/?a3d9", "webpack://_N_E/./pages/adminsettings/permissions.tsx", "webpack://_N_E/./node_modules/react-bootstrap/esm/CardBody.js", "webpack://_N_E/./node_modules/react-bootstrap/esm/CardFooter.js", "webpack://_N_E/./node_modules/react-bootstrap/esm/CardHeader.js", "webpack://_N_E/./node_modules/react-bootstrap/esm/CardImg.js", "webpack://_N_E/./node_modules/react-bootstrap/esm/CardImgOverlay.js", "webpack://_N_E/./node_modules/react-bootstrap/esm/CardLink.js", "webpack://_N_E/./node_modules/react-bootstrap/esm/CardSubtitle.js", "webpack://_N_E/./node_modules/react-bootstrap/esm/CardText.js", "webpack://_N_E/./node_modules/react-bootstrap/esm/CardTitle.js", "webpack://_N_E/./node_modules/react-bootstrap/esm/Card.js", "webpack://_N_E/./components/common/PageHeading.tsx", "webpack://_N_E/./pages/adminsettings/index.tsx", "webpack://_N_E/./node_modules/react-bootstrap/esm/CardHeaderContext.js"], "sourcesContent": ["\n    (window.__NEXT_P = window.__NEXT_P || []).push([\n      \"/adminsettings\",\n      function () {\n        return require(\"private-next-pages/adminsettings/index.tsx\");\n      }\n    ]);\n    if(module.hot) {\n      module.hot.dispose(function () {\n        window.__NEXT_P.push([\"/adminsettings\"])\n      });\n    }\n  ", "//Import services/components\r\nimport connectedAuthWrapper from 'redux-auth-wrapper/connectedAuthWrapper';\r\n\r\nconst create = \"create:any\";\r\nexport const canAddAreaOfWork = connectedAuthWrapper({\r\n  authenticatedSelector: (state) => {\r\n    if (state.permissions && state.permissions.area_of_work && state.permissions.area_of_work[create]) {\r\n      return true;\r\n    }\r\n    return false;\r\n  },\r\n  wrapperDisplayName: 'CanAddAreaOfWork',\r\n});\r\n\r\nexport const canAddCountry = connectedAuthWrapper({\r\n  authenticatedSelector: (state) => {\r\n    if (state.permissions && state.permissions.country && state.permissions.country[create]) {\r\n      return true;\r\n    }\r\n    return false;\r\n  },\r\n  wrapperDisplayName: 'CanAddCountry',\r\n});\r\n\r\nexport const canAddDeploymentStatus = connectedAuthWrapper({\r\n  authenticatedSelector: (state) => {\r\n    if (state.permissions && state.permissions.deployment_status && state.permissions.deployment_status[create]) {\r\n      return true;\r\n    }\r\n    return false;\r\n  },\r\n  wrapperDisplayName: 'CanAddDeploymentStatus',\r\n});\r\n\r\nexport const canAddEventStatus = connectedAuthWrapper({\r\n  authenticatedSelector: (state) => {\r\n    if (state.permissions && state.permissions.event_status && state.permissions.event_status[create]) {\r\n      return true;\r\n    }\r\n    return false;\r\n  },\r\n  wrapperDisplayName: 'CanAddEventStatus',\r\n});\r\n\r\nexport const canAddExpertise = connectedAuthWrapper({\r\n  authenticatedSelector: (state) => {\r\n    if (state.permissions && state.permissions.expertise && state.permissions.expertise[create]) {\r\n      return true;\r\n    }\r\n    return false;\r\n  },\r\n  wrapperDisplayName: 'CanAddExpertise',\r\n});\r\n\r\nexport const canAddFocalPointApproval = connectedAuthWrapper({\r\n  authenticatedSelector: (state) => {\r\n    if (state.permissions && state.permissions.institution_focal_point && state.permissions.institution_focal_point[create]) {\r\n      return true;\r\n    }\r\n    return false;\r\n  },\r\n  wrapperDisplayName: 'CanAddFocalPointApproval',\r\n});\r\n\r\nexport const canAddVspaceApproval = connectedAuthWrapper({\r\n  authenticatedSelector: (state) => {\r\n    if (state.permissions && state.permissions.institution_focal_point && state.permissions.institution_focal_point[create]) {\r\n      return true;\r\n    }\r\n    return false;\r\n  },\r\n  wrapperDisplayName: 'CanAddVspaceApproval',\r\n});\r\nexport const canAddHazards = connectedAuthWrapper({\r\n  authenticatedSelector: (state) => {\r\n    if (state.permissions && state.permissions.hazard && state.permissions.hazard[create]) {\r\n      return true;\r\n    }\r\n    return false;\r\n  },\r\n  wrapperDisplayName: 'CanAddHazards',\r\n});\r\n\r\nexport const canAddHazardTypes = connectedAuthWrapper({\r\n  authenticatedSelector: (state) => {\r\n    if (state.permissions && state.permissions.hazard_type && state.permissions.hazard_type[create]) {\r\n      return true;\r\n    }\r\n    return false;\r\n  },\r\n  wrapperDisplayName: 'CanAddHazardTypes',\r\n}); \r\n\r\nexport const canAddOrganisationApproval = connectedAuthWrapper({\r\n  authenticatedSelector: (state) => {\r\n    if (state.permissions && state.permissions.institution && state.permissions.institution[create]) {\r\n      return true;\r\n    }\r\n    return false;\r\n  },\r\n  wrapperDisplayName: 'CanAddOrganisationApproval',\r\n}); \r\n\r\nexport const canAddOrganisationNetworks = connectedAuthWrapper({\r\n  authenticatedSelector: (state) => {\r\n    if (state.permissions && state.permissions.institution_network && state.permissions.institution_network[create]) {\r\n      return true;\r\n    }\r\n    return false;\r\n  },\r\n  wrapperDisplayName: 'CanAddOrganisationNetworks',\r\n});\r\n\r\nexport const canAddOrganisationTypes = connectedAuthWrapper({\r\n  authenticatedSelector: (state) => {\r\n    if (state.permissions && state.permissions.institution_type && state.permissions.institution_type[create]) {\r\n      return true;\r\n    }\r\n    return false;\r\n  },\r\n  wrapperDisplayName: 'CanAddOrganisationTypes',\r\n});\r\n\r\nexport const canAddOperationStatus = connectedAuthWrapper({\r\n  authenticatedSelector: (state) => {\r\n    if (state.permissions && state.permissions.operation_status && state.permissions.operation_status[create]) {\r\n      return true;\r\n    }\r\n    return false;\r\n  },\r\n  wrapperDisplayName: 'CanAddOperationStatus',\r\n});\r\n\r\nexport const canAddProjectStatus = connectedAuthWrapper({\r\n  authenticatedSelector: (state) => {\r\n    if (state.permissions && state.permissions.project_status && state.permissions.project_status[create]) {\r\n      return true;\r\n    }\r\n    return false;\r\n  },\r\n  wrapperDisplayName: 'CanAddProjectStatus',\r\n});\r\n\r\nexport const canAddRegions = connectedAuthWrapper({\r\n  authenticatedSelector: (state) => {\r\n    if (state.permissions && state.permissions.region && state.permissions.region[create]) {\r\n      return true;\r\n    }\r\n    return false;\r\n  },\r\n  wrapperDisplayName: 'CanAddRegions',\r\n});\r\n\r\nexport const canAddRiskLevels = connectedAuthWrapper({\r\n  authenticatedSelector: (state) => {\r\n    if (state.permissions && state.permissions.risk_level && state.permissions.risk_level[create]) {\r\n      return true;\r\n    }\r\n    return false;\r\n  },\r\n  wrapperDisplayName: 'CanAddRiskLevels',\r\n});\r\n\r\nexport const canAddSyndromes = connectedAuthWrapper({\r\n  authenticatedSelector: (state) => {\r\n    if (state.permissions && state.permissions.syndrome && state.permissions.syndrome[create]) {\r\n      return true;\r\n    }\r\n    return false;\r\n  },\r\n  wrapperDisplayName: 'CanAddSyndromes',\r\n});\r\n\r\nexport const canAddUpdateTypes = connectedAuthWrapper({\r\n  authenticatedSelector: (state) => {\r\n    if (state.permissions && state.permissions.update_type && state.permissions.update_type[create]) {\r\n      return true;\r\n    }\r\n    return false;\r\n  },\r\n  wrapperDisplayName: 'CanAddUpdateTypes',\r\n});\r\n\r\nexport const canAddUsers = connectedAuthWrapper({\r\n  authenticatedSelector: (state) => {\r\n    if (state.permissions && state.permissions.users && state.permissions.users[create]) {\r\n      return true;\r\n    }\r\n    return false;\r\n  },\r\n  wrapperDisplayName: 'CanAddUsers',\r\n});\r\n\r\nexport const canAddWorldRegion = connectedAuthWrapper({\r\n  authenticatedSelector: (state) => {\r\n    if (state.permissions && state.permissions.worl_region && state.permissions.worl_region[create]) {\r\n      return true;\r\n    }\r\n    return false;\r\n  },\r\n  wrapperDisplayName: 'CanAddWorldRegion',\r\n});\r\n\r\nexport const canAddLandingPage = connectedAuthWrapper({\r\n  authenticatedSelector: (state) => {\r\n    if (state.permissions && state.permissions.landing_page && state.permissions.landing_page[create]) {\r\n      return true;\r\n    }\r\n    return false;\r\n  },\r\n  wrapperDisplayName: 'CanAddLandingPage',\r\n});\r\n\r\nexport const canAddContent = connectedAuthWrapper({\r\n  authenticatedSelector: (state) => {\r\n    if (state.permissions && state.permissions.operation && state.permissions.operation[create] && state.permissions.project && state.permissions.project[create]&& state.permissions.event && state.permissions.event[create]&& state.permissions.vspace && state.permissions.vspace[create]&& state.permissions.institution && state.permissions.institution[create]&& state.permissions.update && state.permissions.update[create]) {\r\n      return true;\r\n    }\r\n    return false;\r\n  },\r\n  wrapperDisplayName: 'CanAddContent',\r\n});\r\n\r\nexport default canAddAreaOfWork;", "\"use client\";\n\nimport * as React from 'react';\nimport classNames from 'classnames';\nimport { useBootstrapPrefix } from './ThemeProvider';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst CardBody = /*#__PURE__*/React.forwardRef(({\n  className,\n  bsPrefix,\n  as: Component = 'div',\n  ...props\n}, ref) => {\n  bsPrefix = useBootstrapPrefix(bsPrefix, 'card-body');\n  return /*#__PURE__*/_jsx(Component, {\n    ref: ref,\n    className: classNames(className, bsPrefix),\n    ...props\n  });\n});\nCardBody.displayName = 'CardBody';\nexport default CardBody;", "\"use client\";\n\nimport * as React from 'react';\nimport classNames from 'classnames';\nimport { useBootstrapPrefix } from './ThemeProvider';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst CardFooter = /*#__PURE__*/React.forwardRef(({\n  className,\n  bsPrefix,\n  as: Component = 'div',\n  ...props\n}, ref) => {\n  bsPrefix = useBootstrapPrefix(bsPrefix, 'card-footer');\n  return /*#__PURE__*/_jsx(Component, {\n    ref: ref,\n    className: classNames(className, bsPrefix),\n    ...props\n  });\n});\nCardFooter.displayName = 'CardFooter';\nexport default CardFooter;", "\"use client\";\n\nimport classNames from 'classnames';\nimport * as React from 'react';\nimport { useMemo } from 'react';\nimport { useBootstrapPrefix } from './ThemeProvider';\nimport CardHeaderContext from './CardHeaderContext';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst CardHeader = /*#__PURE__*/React.forwardRef(({\n  bsPrefix,\n  className,\n  // Need to define the default \"as\" during prop destructuring to be compatible with styled-components github.com/react-bootstrap/react-bootstrap/issues/3595\n  as: Component = 'div',\n  ...props\n}, ref) => {\n  const prefix = useBootstrapPrefix(bsPrefix, 'card-header');\n  const contextValue = useMemo(() => ({\n    cardHeaderBsPrefix: prefix\n  }), [prefix]);\n  return /*#__PURE__*/_jsx(CardHeaderContext.Provider, {\n    value: contextValue,\n    children: /*#__PURE__*/_jsx(Component, {\n      ref: ref,\n      ...props,\n      className: classNames(className, prefix)\n    })\n  });\n});\nCardHeader.displayName = 'CardHeader';\nexport default CardHeader;", "\"use client\";\n\nimport classNames from 'classnames';\nimport * as React from 'react';\nimport { useBootstrapPrefix } from './ThemeProvider';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst CardImg = /*#__PURE__*/React.forwardRef(\n// Need to define the default \"as\" during prop destructuring to be compatible with styled-components github.com/react-bootstrap/react-bootstrap/issues/3595\n({\n  bsPrefix,\n  className,\n  variant,\n  as: Component = 'img',\n  ...props\n}, ref) => {\n  const prefix = useBootstrapPrefix(bsPrefix, 'card-img');\n  return /*#__PURE__*/_jsx(Component, {\n    ref: ref,\n    className: classNames(variant ? `${prefix}-${variant}` : prefix, className),\n    ...props\n  });\n});\nCardImg.displayName = 'CardImg';\nexport default CardImg;", "\"use client\";\n\nimport * as React from 'react';\nimport classNames from 'classnames';\nimport { useBootstrapPrefix } from './ThemeProvider';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst CardImgOverlay = /*#__PURE__*/React.forwardRef(({\n  className,\n  bsPrefix,\n  as: Component = 'div',\n  ...props\n}, ref) => {\n  bsPrefix = useBootstrapPrefix(bsPrefix, 'card-img-overlay');\n  return /*#__PURE__*/_jsx(Component, {\n    ref: ref,\n    className: classNames(className, bsPrefix),\n    ...props\n  });\n});\nCardImgOverlay.displayName = 'CardImgOverlay';\nexport default CardImgOverlay;", "\"use client\";\n\nimport * as React from 'react';\nimport classNames from 'classnames';\nimport { useBootstrapPrefix } from './ThemeProvider';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst CardLink = /*#__PURE__*/React.forwardRef(({\n  className,\n  bsPrefix,\n  as: Component = 'a',\n  ...props\n}, ref) => {\n  bsPrefix = useBootstrapPrefix(bsPrefix, 'card-link');\n  return /*#__PURE__*/_jsx(Component, {\n    ref: ref,\n    className: classNames(className, bsPrefix),\n    ...props\n  });\n});\nCardLink.displayName = 'CardLink';\nexport default CardLink;", "\"use client\";\n\nimport * as React from 'react';\nimport classNames from 'classnames';\nimport { useBootstrapPrefix } from './ThemeProvider';\nimport divWithClassName from './divWithClassName';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst DivStyledAsH6 = divWithClassName('h6');\nconst CardSubtitle = /*#__PURE__*/React.forwardRef(({\n  className,\n  bsPrefix,\n  as: Component = DivStyledAsH6,\n  ...props\n}, ref) => {\n  bsPrefix = useBootstrapPrefix(bsPrefix, 'card-subtitle');\n  return /*#__PURE__*/_jsx(Component, {\n    ref: ref,\n    className: classNames(className, bsPrefix),\n    ...props\n  });\n});\nCardSubtitle.displayName = 'CardSubtitle';\nexport default CardSubtitle;", "\"use client\";\n\nimport * as React from 'react';\nimport classNames from 'classnames';\nimport { useBootstrapPrefix } from './ThemeProvider';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst CardText = /*#__PURE__*/React.forwardRef(({\n  className,\n  bsPrefix,\n  as: Component = 'p',\n  ...props\n}, ref) => {\n  bsPrefix = useBootstrapPrefix(bsPrefix, 'card-text');\n  return /*#__PURE__*/_jsx(Component, {\n    ref: ref,\n    className: classNames(className, bsPrefix),\n    ...props\n  });\n});\nCardText.displayName = 'CardText';\nexport default CardText;", "\"use client\";\n\nimport * as React from 'react';\nimport classNames from 'classnames';\nimport { useBootstrapPrefix } from './ThemeProvider';\nimport divWithClassName from './divWithClassName';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst DivStyledAsH5 = divWithClassName('h5');\nconst CardTitle = /*#__PURE__*/React.forwardRef(({\n  className,\n  bsPrefix,\n  as: Component = DivStyledAsH5,\n  ...props\n}, ref) => {\n  bsPrefix = useBootstrapPrefix(bsPrefix, 'card-title');\n  return /*#__PURE__*/_jsx(Component, {\n    ref: ref,\n    className: classNames(className, bsPrefix),\n    ...props\n  });\n});\nCardTitle.displayName = 'CardTitle';\nexport default CardTitle;", "\"use client\";\n\nimport classNames from 'classnames';\nimport * as React from 'react';\nimport { useBootstrapPrefix } from './ThemeProvider';\nimport CardBody from './CardBody';\nimport CardFooter from './CardFooter';\nimport CardHeader from './CardHeader';\nimport CardImg from './CardImg';\nimport CardImgOverlay from './CardImgOverlay';\nimport CardLink from './CardLink';\nimport CardSubtitle from './CardSubtitle';\nimport CardText from './CardText';\nimport CardTitle from './CardTitle';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst Card = /*#__PURE__*/React.forwardRef(({\n  bsPrefix,\n  className,\n  bg,\n  text,\n  border,\n  body = false,\n  children,\n  // Need to define the default \"as\" during prop destructuring to be compatible with styled-components github.com/react-bootstrap/react-bootstrap/issues/3595\n  as: Component = 'div',\n  ...props\n}, ref) => {\n  const prefix = useBootstrapPrefix(bsPrefix, 'card');\n  return /*#__PURE__*/_jsx(Component, {\n    ref: ref,\n    ...props,\n    className: classNames(className, prefix, bg && `bg-${bg}`, text && `text-${text}`, border && `border-${border}`),\n    children: body ? /*#__PURE__*/_jsx(CardBody, {\n      children: children\n    }) : children\n  });\n});\nCard.displayName = 'Card';\nexport default Object.assign(Card, {\n  Img: CardImg,\n  Title: CardTitle,\n  Subtitle: CardSubtitle,\n  Body: CardBody,\n  Link: CardLink,\n  Text: CardText,\n  Header: CardHeader,\n  Footer: CardFooter,\n  ImgOverlay: CardImgOverlay\n});", "interface PageHeadingProps {\r\n  title: string; // Required based on actual usage\r\n}\r\n\r\nexport default function PageHeading(props: PageHeadingProps) {\r\n  return (\r\n    <h2 className=\"page-heading\">{props.title}</h2>\r\n  )\r\n}\r\n", "//Import Library\r\nimport { <PERSON>, Row, Col, Container } from \"react-bootstrap\";\r\nimport Link from \"next/link\";\r\nimport { FontAwesomeIcon } from \"@fortawesome/react-fontawesome\";\r\nimport { faArrowRight } from \"@fortawesome/free-solid-svg-icons\";\r\n\r\n//Import services/components\r\nimport PageHeading from \"../../components/common/PageHeading\";\r\nimport { useTranslation } from 'next-i18next';\r\nimport { serverSideTranslations } from 'next-i18next/serverSideTranslations';\r\nimport { canAddAreaOfWork } from \"../adminsettings/permissions\";\r\nimport { canAddCountry } from \"../adminsettings/permissions\";\r\nimport { canAddDeploymentStatus } from \"../adminsettings/permissions\";\r\nimport { canAddEventStatus } from \"../adminsettings/permissions\";\r\nimport { canAddExpertise } from \"../adminsettings/permissions\";\r\nimport { canAddFocalPointApproval } from \"../adminsettings/permissions\";\r\nimport {canAddVspaceApproval} from \"../adminsettings/permissions\"\r\n\r\nimport { canAddHazards } from \"../adminsettings/permissions\";\r\nimport { canAddHazardTypes } from \"../adminsettings/permissions\";\r\nimport { canAddOrganisationApproval } from \"../adminsettings/permissions\";\r\nimport { canAddOrganisationNetworks } from \"../adminsettings/permissions\";\r\nimport { canAddOrganisationTypes } from \"../adminsettings/permissions\";\r\nimport { canAddOperationStatus } from \"../adminsettings/permissions\";\r\nimport { canAddProjectStatus } from \"../adminsettings/permissions\";\r\nimport { canAddRegions } from \"../adminsettings/permissions\";\r\nimport { canAddRiskLevels } from \"../adminsettings/permissions\";\r\nimport { canAddSyndromes } from \"../adminsettings/permissions\";\r\nimport { canAddUpdateTypes } from \"../adminsettings/permissions\";\r\nimport { canAddUsers } from \"../adminsettings/permissions\";\r\nimport { canAddWorldRegion } from \"../adminsettings/permissions\";\r\nimport { canAddLandingPage } from \"../adminsettings/permissions\";\r\nimport {canAddContent} from \"../adminsettings/permissions\";\r\n\r\nconst admin = () => {\r\n  const { t } = useTranslation('common');\r\n\r\n  const AreaOfWork = () => {\r\n    return (\r\n      <Col md={4} sm={12} className =\"mb-2 mt-2\">\r\n        <div className=\"infoCard_admin_card text-center cursor-pointer\">\r\n          <Link\r\n            href=\"/adminsettings/[...routes]\"\r\n            as=\"/adminsettings/area_of_work\"\r\n            >\r\n            <Card className=\"infoCard \">\r\n              <Card.Body>\r\n                <Card.Text>\r\n                  {t(\"adminsetting.adminindex.Areaofwork\")}\r\n                  <span className=\"arrowStyle\">\r\n                      <FontAwesomeIcon icon={faArrowRight} />\r\n                    </span>\r\n                </Card.Text>\r\n              </Card.Body>\r\n            </Card>\r\n          </Link>\r\n        </div>\r\n      </Col>\r\n    );\r\n  }\r\n\r\n  const Countries = () => {\r\n    return (\r\n      <Col md={4} sm={12} className =\"mb-2 mt-2\">\r\n        <div className=\"infoCard_admin_card text-center cursor-pointer\">\r\n          <Link\r\n            href=\"/adminsettings/[...routes]\"\r\n            as=\"/adminsettings/country\"\r\n            >\r\n            <Card className=\"infoCard\">\r\n              <Card.Body>\r\n                <Card.Text>\r\n                  {t(\"adminsetting.adminindex.Countries\")}\r\n                  <span className=\"arrowStyle\">\r\n                      <FontAwesomeIcon icon={faArrowRight} />\r\n                    </span>\r\n                </Card.Text>\r\n              </Card.Body>\r\n            </Card>\r\n          </Link>\r\n        </div>\r\n      </Col>\r\n    );\r\n  }\r\n\r\n  const DeploymentStatus = () => {\r\n    return (\r\n      <Col md={4} sm={12} className =\"mb-2 mt-2\">\r\n        <div className=\" infoCard_admin_card text-center cursor-pointer\">\r\n          <Link\r\n            href=\"/adminsettings/[...routes]\"\r\n            as=\"/adminsettings/deploymentstatus\"\r\n            >\r\n            <Card className=\"infoCard\">\r\n              <Card.Body>\r\n                <Card.Text>\r\n                  {t(\"adminsetting.adminindex.DeploymentStatus\")}\r\n                  <span className=\"arrowStyle\">\r\n                      <FontAwesomeIcon icon={faArrowRight} />\r\n                    </span>\r\n                </Card.Text>\r\n              </Card.Body>\r\n            </Card>\r\n          </Link>\r\n        </div>\r\n      </Col>\r\n    );\r\n  }\r\n\r\n  const EventStatus = () => {\r\n    return (\r\n      <Col md={4} sm={12} className =\"mb-2 mt-2\">\r\n        <div className=\"infoCard_admin_card text-center cursor-pointer\">\r\n          <Link\r\n            href=\"/adminsettings/[...routes]\"\r\n            as=\"/adminsettings/eventstatus\"\r\n            >\r\n            <Card className=\"infoCard\">\r\n              <Card.Body>\r\n                <Card.Text>\r\n                  {t(\"adminsetting.adminindex.EventStatus\")}\r\n                      <span className=\"arrowStyle\">\r\n                      <FontAwesomeIcon icon={faArrowRight} />\r\n                    </span>\r\n                </Card.Text>\r\n              </Card.Body>\r\n            </Card>\r\n          </Link>\r\n        </div>\r\n      </Col>\r\n    );\r\n  }\r\n\r\n  const Expertise = () => {\r\n    return (\r\n      <Col md={4} sm={12} className =\"mb-2 mt-2\">\r\n        <div className=\"infoCard_admin_card text-center cursor-pointer\">\r\n          <Link\r\n            href=\"/adminsettings/[...routes]\"\r\n            as=\"/adminsettings/expertise\"\r\n            >\r\n            <Card className=\"infoCard\">\r\n              <Card.Body>\r\n                <Card.Text>\r\n                  \r\n                  {t(\"adminsetting.adminindex.Expertise\")}\r\n                      <span className=\"arrowStyle\">\r\n                      <FontAwesomeIcon icon={faArrowRight} />\r\n                    </span>\r\n                  \r\n                </Card.Text>\r\n              </Card.Body>\r\n            </Card>\r\n          </Link>\r\n        </div>\r\n      </Col>\r\n    );\r\n  }\r\n\r\n  const FocalPointApproval = () => {\r\n    return (\r\n      <Col md={4} sm={12} className =\"mb-2 mt-2\">\r\n        <div className=\"infoCard_admin_card text-center cursor-pointer\">\r\n          <Link\r\n            href=\"/adminsettings/[...routes]\"\r\n            as=\"/adminsettings/focal_point\"\r\n            >\r\n            <Card className=\"infoCard\">\r\n              <Card.Body>\r\n                <Card.Text>\r\n                  \r\n                  {t(\"adminsetting.adminindex.FocalPointApproval\")}\r\n                  <span className=\"arrowStyle\">\r\n                      <FontAwesomeIcon icon={faArrowRight} />\r\n                    </span>\r\n                  \r\n                </Card.Text>\r\n              </Card.Body>\r\n            </Card>\r\n          </Link>\r\n        </div>\r\n      </Col>\r\n    );\r\n  }\r\n\r\n  const VspaceApproval = () => {\r\n    return (\r\n      <Col md={4} sm={12} className =\"mb-2 mt-2\">\r\n        <div className=\"infoCard_admin_card text-center cursor-pointer\">\r\n          <Link\r\n            href=\"/adminsettings/[...routes]\"\r\n            as=\"/adminsettings/Vspace_point\"\r\n            >\r\n            <Card className=\"infoCard\">\r\n              <Card.Body>\r\n                <Card.Text>\r\n                  \r\n                  {t(\"adminsetting.adminindex.VspaceApproval\")}\r\n                  <span className=\"arrowStyle\">\r\n                      <FontAwesomeIcon icon={faArrowRight} />\r\n                    </span>\r\n                  \r\n                </Card.Text>\r\n              </Card.Body>\r\n            </Card>\r\n          </Link>\r\n        </div>\r\n      </Col>\r\n    );\r\n  }\r\n\r\n  const Hazards = () => {\r\n    return (\r\n      <Col md={4} sm={12} className =\"mb-2 mt-2\">\r\n        <div className=\"infoCard_admin_card text-center cursor-pointer\">\r\n          <Link\r\n            href=\"/adminsettings/[...routes]\"\r\n            as=\"/adminsettings/hazard\"\r\n            >\r\n            <Card className=\"infoCard\">\r\n              <Card.Body>\r\n                <Card.Text>\r\n                  \r\n                  {t(\"adminsetting.adminindex.Hazards\")}\r\n                      <span className=\"arrowStyle\">\r\n                      <FontAwesomeIcon icon={faArrowRight} />\r\n                    </span>\r\n                  \r\n                </Card.Text>\r\n              </Card.Body>\r\n            </Card>\r\n          </Link>\r\n        </div>\r\n      </Col>\r\n    );\r\n  }\r\n\r\n  const HazardTypes = () => {\r\n    return (\r\n      <Col md={4} sm={12} className =\"mb-2 mt-2\">\r\n        <div className=\"infoCard_admin_card text-center cursor-pointer\">\r\n          <Link\r\n            href=\"/adminsettings/[...routes]\"\r\n            as=\"/adminsettings/hazardTypes\"\r\n            >\r\n            <Card className=\"infoCard\">\r\n              <Card.Body>\r\n                <Card.Text>\r\n                  \r\n                  {t(\"adminsetting.adminindex.HazardTypes\")}\r\n                      <span className=\"arrowStyle\">\r\n                      <FontAwesomeIcon icon={faArrowRight} />\r\n                    </span>\r\n                  \r\n                </Card.Text>\r\n              </Card.Body>\r\n            </Card>\r\n          </Link>\r\n        </div>\r\n      </Col>\r\n    );\r\n  }\r\n\r\n  const OrganisationApproval = () => {\r\n    return (\r\n      <Col md={4} sm={12} className =\"mb-2 mt-2\">\r\n        <div className=\"infoCard_admin_card text-center cursor-pointer\">\r\n          <Link\r\n            href=\"/adminsettings/[...routes]\"\r\n            as=\"/adminsettings/institution_approval\"\r\n            >\r\n            <Card className=\"infoCard\">\r\n              <Card.Body>\r\n                <Card.Text>\r\n                  \r\n                  {t(\"adminsetting.adminindex.OrganisationApproval\")}\r\n                  <span className=\"arrowStyle\">\r\n                      <FontAwesomeIcon icon={faArrowRight} />\r\n                    </span>\r\n                  \r\n                </Card.Text>\r\n              </Card.Body>\r\n            </Card>\r\n          </Link>\r\n        </div>\r\n      </Col>\r\n    );\r\n  }\r\n\r\n  const OrganisationNetworks = () => {\r\n    return (\r\n      <Col md={4} sm={12} className =\"mb-2 mt-2\">\r\n        <div className=\"infoCard_admin_card text-center cursor-pointer\">\r\n          <Link\r\n            href=\"/adminsettings/[...routes]\"\r\n            as=\"/adminsettings/institution_network\"\r\n            >\r\n            <Card className=\"infoCard\">\r\n              <Card.Body>\r\n                <Card.Text>\r\n                  \r\n                  {t(\"adminsetting.adminindex.OrganisationNetworks\")}\r\n                  <span className=\"arrowStyle\">\r\n                      <FontAwesomeIcon icon={faArrowRight} />\r\n                    </span>\r\n                  \r\n                </Card.Text>\r\n              </Card.Body>\r\n            </Card>\r\n          </Link>\r\n        </div>\r\n      </Col>\r\n    );\r\n  }\r\n\r\n  const OrganisationTypes = () => {\r\n    return (\r\n      <Col md={4} sm={12} className =\"mb-2 mt-2\">\r\n        <div className=\"infoCard_admin_card text-center cursor-pointer\">\r\n          <Link\r\n            href=\"/adminsettings/[...routes]\"\r\n            as=\"/adminsettings/institution_type\"\r\n            >\r\n            <Card className=\"infoCard\">\r\n              <Card.Body>\r\n                <Card.Text>\r\n                  \r\n                  {t(\"adminsetting.adminindex.OrganisationTypes\")}\r\n                  <span className=\"arrowStyle\">\r\n                      <FontAwesomeIcon icon={faArrowRight} />\r\n                    </span>\r\n                  \r\n                </Card.Text>\r\n              </Card.Body>\r\n            </Card>\r\n          </Link>\r\n        </div>\r\n      </Col>\r\n    );\r\n  }\r\n\r\n  const OperationStatus = () => {\r\n    return (\r\n      <Col md={4} sm={12} className =\"mb-2 mt-2\">\r\n        <div className=\"infoCard_admin_card text-center cursor-pointer\">\r\n          <Link\r\n            href=\"/adminsettings/[...routes]\"\r\n            as=\"/adminsettings/operationstatus\"\r\n            >\r\n            <Card className=\"infoCard\">\r\n              <Card.Body>\r\n                <Card.Text>\r\n                  \r\n                  {t(\"adminsetting.adminindex.OperationStatus\")}\r\n                <span className=\"arrowStyle\">\r\n                      <FontAwesomeIcon icon={faArrowRight} />\r\n                    </span>\r\n                  \r\n                </Card.Text>\r\n              </Card.Body>\r\n            </Card>\r\n          </Link>\r\n        </div>\r\n      </Col>\r\n    );\r\n  }\r\n\r\n  const ProjectStatus = () => {\r\n    return (\r\n      <Col md={4} sm={12} className =\"mb-2 mt-2\">\r\n        <div className=\"infoCard_admin_card text-center cursor-pointer\">\r\n          <Link\r\n            href=\"/adminsettings/[...routes]\"\r\n            as=\"/adminsettings/projectstatus\"\r\n            >\r\n            <Card className=\"infoCard\">\r\n              <Card.Body>\r\n                <Card.Text>\r\n                  \r\n                  {t(\"adminsetting.adminindex.ProjectStatus\")}\r\n                <span className=\"arrowStyle\">\r\n                      <FontAwesomeIcon icon={faArrowRight} />\r\n                    </span>\r\n                  \r\n                </Card.Text>\r\n              </Card.Body>\r\n            </Card>\r\n          </Link>\r\n        </div>\r\n      </Col>\r\n    );\r\n  }\r\n\r\n  const Regions = () => {\r\n    return (\r\n      <Col md={4} sm={12} className =\"mb-2 mt-2\">\r\n        <div className=\"infoCard_admin_card text-center cursor-pointer\">\r\n          <Link\r\n            href=\"/adminsettings/[...routes]\"\r\n            as=\"/adminsettings/region\"\r\n            >\r\n            <Card className=\"infoCard\">\r\n              <Card.Body>\r\n                <Card.Text>\r\n                  \r\n                  {t(\"adminsetting.adminindex.Regions\")}\r\n                <span className=\"arrowStyle\">\r\n                      <FontAwesomeIcon icon={faArrowRight} />\r\n                    </span>\r\n                  \r\n                </Card.Text>\r\n              </Card.Body>\r\n            </Card>\r\n          </Link>\r\n        </div>\r\n      </Col>\r\n    );\r\n  }\r\n\r\n  const RiskLevels = () => {\r\n    return (\r\n      <Col md={4} sm={12} className =\"mb-2 mt-2\">\r\n        <div className=\"infoCard_admin_card text-center cursor-pointer\">\r\n          <Link\r\n            href=\"/adminsettings/[...routes]\"\r\n            as=\"/adminsettings/risklevel\"\r\n            >\r\n            <Card className=\"infoCard\">\r\n              <Card.Body>\r\n                <Card.Text>\r\n                  \r\n                  {t(\"adminsetting.adminindex.RiskLevels\")}\r\n                <span className=\"arrowStyle\">\r\n                      <FontAwesomeIcon icon={faArrowRight} />\r\n                    </span>\r\n                  \r\n                </Card.Text>\r\n              </Card.Body>\r\n            </Card>\r\n          </Link>\r\n        </div>\r\n      </Col>\r\n    );\r\n  }\r\n\r\n  const Syndromes = () => {\r\n    return (\r\n      <Col md={4} sm={12} className =\"mb-2 mt-2\">\r\n        <div className=\"infoCard_admin_card text-center cursor-pointer\">\r\n          <Link\r\n            href=\"/adminsettings/[...routes]\"\r\n            as=\"/adminsettings/syndrome\"\r\n            >\r\n            <Card className=\"infoCard\">\r\n              <Card.Body>\r\n                <Card.Text>\r\n                  \r\n                  {t(\"adminsetting.adminindex.Syndromes\")}\r\n                <span className=\"arrowStyle\">\r\n                      <FontAwesomeIcon icon={faArrowRight} />\r\n                    </span>\r\n                  \r\n                </Card.Text>\r\n              </Card.Body>\r\n            </Card>\r\n          </Link>\r\n        </div>\r\n      </Col>\r\n    );\r\n  }\r\n\r\n  const UpdateTypes = () => {\r\n    return (\r\n      <Col md={4} sm={12} className =\"mb-2 mt-2\">\r\n        <div className=\"infoCard_admin_card text-center cursor-pointer\">\r\n          <Link\r\n            href=\"/adminsettings/[...routes]\"\r\n            as=\"/adminsettings/update_type\"\r\n            >\r\n            <Card className=\"infoCard\">\r\n              <Card.Body>\r\n                <Card.Text>\r\n                  \r\n                  {t(\"adminsetting.adminindex.UpdatesTypes\")}\r\n                <span className=\"arrowStyle\">\r\n                      <FontAwesomeIcon icon={faArrowRight} />\r\n                    </span>\r\n                  \r\n                </Card.Text>\r\n              </Card.Body>\r\n            </Card>\r\n          </Link>\r\n        </div>\r\n      </Col>\r\n    );\r\n  }\r\n\r\n  const Users = () => {\r\n    return (\r\n      <Col  md={4} sm={12} className =\"mb-2 mt-2\">\r\n        <div className=\"infoCard_admin_card text-center cursor-pointer\">\r\n          <Link\r\n            href=\"/adminsettings/[...routes]\"\r\n            as=\"/adminsettings/users\"\r\n            >\r\n            <Card className=\"infoCard\">\r\n              <Card.Body>\r\n                <Card.Text>\r\n                  \r\n                  {t(\"adminsetting.adminindex.Users\")}\r\n                      <span className=\"arrowStyle\">\r\n                      <FontAwesomeIcon icon={faArrowRight} />\r\n                    </span>\r\n                  \r\n                </Card.Text>\r\n              </Card.Body>\r\n            </Card>\r\n          </Link>\r\n        </div>\r\n      </Col>\r\n    );\r\n  }\r\n\r\n  const WorldRegions = () => {\r\n    return (\r\n      <Col  md={4} sm={12} className =\"mb-2 mt-2\">\r\n        <div className=\"infoCard_admin_card text-center cursor-pointer\">\r\n          <Link\r\n            href=\"/adminsettings/[...routes]\"\r\n            as=\"/adminsettings/worldregion\"\r\n            >\r\n            <Card className=\"infoCard\">\r\n              <Card.Body>\r\n                <Card.Text>\r\n                  \r\n                  {t(\"adminsetting.adminindex.WorldRegions\")}\r\n                      <span className=\"arrowStyle\">\r\n                      <FontAwesomeIcon icon={faArrowRight} />\r\n                    </span>\r\n                  \r\n                </Card.Text>\r\n              </Card.Body>\r\n            </Card>\r\n          </Link>\r\n        </div>\r\n      </Col>\r\n    );\r\n  }\r\n\r\n  const LandingPage = () => {\r\n    return (\r\n      <Col  md={4} sm={12} className =\"mb-2 mt-2\">\r\n        <div className=\"infoCard_admin_card text-center cursor-pointer\">\r\n          <Link\r\n            href=\"/adminsettings/[...routes]\"\r\n            as=\"/adminsettings/landing\"\r\n            >\r\n            <Card className=\"infoCard\">\r\n              <Card.Body>\r\n                <Card.Text>\r\n                  \r\n                  {t(\"adminsetting.adminindex.EditableContent\")}\r\n                  <span className=\"arrowStyle\">\r\n                      <FontAwesomeIcon icon={faArrowRight} />\r\n                    </span>\r\n                  \r\n                </Card.Text>\r\n              </Card.Body>\r\n            </Card>\r\n          </Link>\r\n        </div>\r\n      </Col>\r\n    );\r\n  }\r\n\r\n  const Content = () => {\r\n    return (\r\n      <Col  md={4} sm={12} className =\"mb-2 mt-2\">\r\n        <div className=\"infoCard_admin_card text-center cursor-pointer\">\r\n          <Link\r\n            href=\"/adminsettings/[...routes]\"\r\n            as=\"/adminsettings/content\"\r\n            >\r\n            <Card className=\"infoCard\">\r\n              <Card.Body>\r\n                <Card.Text>\r\n                  \r\n                    {t(\"adminsetting.adminindex.Content\")}\r\n                      <span className=\"arrowStyle\">\r\n                      <FontAwesomeIcon icon={faArrowRight} />\r\n                    </span>\r\n                  \r\n                </Card.Text>\r\n              </Card.Body>\r\n            </Card>\r\n          </Link>\r\n        </div>\r\n      </Col>\r\n    );\r\n  }\r\n\r\n\r\n  const CanAddAreaOfWork = canAddAreaOfWork(() => <AreaOfWork />);\r\n  const CanAddCountry = canAddCountry(() => <Countries />);\r\n  const CanAddDeploymentStatus = canAddDeploymentStatus(() => <DeploymentStatus />);\r\n  const CanAddEventStatus = canAddEventStatus(() => <EventStatus />);\r\n  const CanAddExpertise = canAddExpertise(() => <Expertise />);\r\n  const CanAddFocalPointApproval = canAddFocalPointApproval(() => <FocalPointApproval />);\r\n  const CanAddVspaceApproval = canAddVspaceApproval(() => <VspaceApproval />);\r\n\r\n  const CanAddHazards = canAddHazards(() => <Hazards />);\r\n  const CanAddHazardTypes = canAddHazardTypes(() => <HazardTypes />);\r\n  const CanAddOrganisationApproval = canAddOrganisationApproval(() => <OrganisationApproval />);\r\n  const CanAddOrganisationNetworks = canAddOrganisationNetworks(() => <OrganisationNetworks />);\r\n  const CanAddOrganisationTypes = canAddOrganisationTypes(() => <OrganisationTypes />);\r\n  const CanAddOperationStatus = canAddOperationStatus(() => <OperationStatus />);\r\n  const CanAddProjectStatus = canAddProjectStatus(() => <ProjectStatus />);\r\n  const CanAddRegions = canAddRegions(() => <Regions />);\r\n  const CanAddRiskLevels = canAddRiskLevels(() => <RiskLevels />);\r\n  const CanAddSyndromes = canAddSyndromes(() => <Syndromes />);\r\n  const CanAddUpdateTypes = canAddUpdateTypes(() => <UpdateTypes />);\r\n  const CanAddUsers = canAddUsers(() => <Users />);\r\n  const CanAddWorldRegion = canAddWorldRegion(() => <WorldRegions />);\r\n  const CanAddLandingPage = canAddLandingPage(() => <LandingPage />);\r\n  const CanAddContent = canAddContent(() => <Content/>)\r\n\r\n\r\n  return (\r\n    <Container fluid style={{ overflowX: \"hidden\" }} className=\"p-0\">\r\n      <PageHeading title={t(\"menu.adminSettings\")} />\r\n      <Row>\r\n        <CanAddAreaOfWork />\r\n        <CanAddCountry />\r\n        <CanAddDeploymentStatus />\r\n        <CanAddEventStatus />\r\n        <CanAddExpertise />\r\n        <CanAddFocalPointApproval />\r\n        <CanAddVspaceApproval />\r\n        <CanAddHazards />\r\n        <CanAddHazardTypes />\r\n        <CanAddOrganisationApproval />\r\n        <CanAddOrganisationNetworks />\r\n        <CanAddOrganisationTypes />\r\n        <CanAddOperationStatus />\r\n        <CanAddProjectStatus />\r\n        <CanAddRegions />\r\n        <CanAddRiskLevels />\r\n        <CanAddSyndromes />\r\n        <CanAddUpdateTypes />\r\n        <CanAddUsers />\r\n        <CanAddWorldRegion />\r\n        <CanAddLandingPage />\r\n        <CanAddContent/>\r\n      </Row>\r\n    </Container>\r\n  );\r\n};\r\n\r\nexport async function getStaticProps({ locale }: { locale: string}) {\r\n  return {\r\n    props: {\r\n      ...(await serverSideTranslations(locale, ['common'])),\r\n    },\r\n  }\r\n}\r\n\r\nexport default admin;\r\n", "\"use client\";\n\nimport * as React from 'react';\nconst context = /*#__PURE__*/React.createContext(null);\ncontext.displayName = 'CardHeaderContext';\nexport default context;"], "names": ["create", "canAddAreaOfWork", "connectedAuthWrapper", "authenticatedSelector", "state", "permissions", "area_of_work", "wrapperDisplayName", "canAddCountry", "country", "deployment_status", "event_status", "expertise", "institution_focal_point", "hazard", "hazard_type", "institution", "institution_network", "institution_type", "operation_status", "canAddProjectStatus", "project_status", "region", "risk_level", "syndrome", "update_type", "users", "worl_region", "landing_page", "operation", "project", "event", "vspace", "update", "CardBody", "React", "ref", "className", "bsPrefix", "as", "Component", "props", "useBootstrapPrefix", "_jsx", "classNames", "<PERSON><PERSON><PERSON>er", "displayName", "<PERSON><PERSON><PERSON><PERSON>", "prefix", "contextValue", "useMemo", "cardHeaderBsPrefix", "CardHeaderContext", "Provider", "value", "children", "CardImg", "variant", "CardImgOverlay", "CardLink", "DivStyledAsH6", "divWithClassName", "CardSubtitle", "CardText", "DivStyledAsH5", "CardTitle", "Card", "bg", "text", "border", "body", "Object", "assign", "Img", "Title", "Subtitle", "Body", "Link", "Text", "Header", "Footer", "ImgOverlay", "PageHeading", "h2", "title", "t", "useTranslation", "admin", "AreaOfWork", "Col", "md", "sm", "div", "href", "span", "FontAwesomeIcon", "icon", "faArrowRight", "Countries", "DeploymentStatus", "EventStatus", "Expertise", "FocalPointApproval", "VspaceApproval", "Hazards", "HazardTypes", "OrganisationApproval", "OrganisationNetworks", "OrganisationTypes", "OperationStatus", "ProjectStatus", "Regions", "RiskLevels", "Syndromes", "UpdateTypes", "Users", "WorldRegions", "LandingPage", "Content", "CanAddAreaOfWork", "CanAddCountry", "CanAddDeploymentStatus", "canAddDeploymentStatus", "CanAddEventStatus", "canAddEventStatus", "CanAddExpertise", "canAddExpertise", "CanAddFocalPointApproval", "canAddFocalPointApproval", "CanAddVspaceApproval", "canAddVspaceApproval", "CanAddHazards", "canAddHazards", "CanAddHazardTypes", "canAddHazardTypes", "CanAddOrganisationApproval", "canAddOrganisationApproval", "CanAddOrganisationNetworks", "canAddOrganisationNetworks", "CanAddOrganisationTypes", "canAddOrganisationTypes", "CanAddOperationStatus", "canAddOperationStatus", "CanAddProjectStatus", "CanAddRegions", "canAddRegions", "CanAddRiskLevels", "canAddRiskLevels", "CanAddSyndromes", "canAddSyndromes", "CanAddUpdateTypes", "canAddUpdateTypes", "CanAddUsers", "canAddUsers", "CanAddWorldRegion", "canAddWorldRegion", "CanAddLandingPage", "canAddLandingPage", "CanAddContent", "canAddContent", "Container", "fluid", "style", "overflowX", "Row", "context"], "sourceRoot": "", "ignoreList": [2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 14]}