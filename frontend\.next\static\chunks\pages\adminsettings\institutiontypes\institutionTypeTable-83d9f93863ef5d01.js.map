{"version": 3, "file": "static/chunks/pages/adminsettings/institutiontypes/institutionTypeTable-83d9f93863ef5d01.js", "mappings": "+EACA,4CACA,uDACA,WACA,OAAe,EAAQ,KAA4E,CACnG,EACA,SAFsB,oGCiCtB,SAASA,EAASC,CAAoB,EACpC,GAAM,CAAEC,CAAC,CAAE,CAAGC,CAAAA,EAAAA,EAAAA,EAAAA,CAAcA,CAAC,UACvBC,EAA6B,CACjCC,gBAAiBH,EAAE,cACnB,EACI,SACJI,CAAO,MACPC,CAAI,WACJC,CAAS,uBACTC,CAAqB,CACrBC,WAAS,oBACTC,CAAkB,qBAClBC,CAAmB,kBACnBC,CAAgB,aAChBC,CAAW,oBACXC,CAAkB,gBAClBC,CAAc,SACdC,CAAO,WACPC,CAAS,sBACTC,CAAoB,mBACpBC,CAAiB,YACjBC,CAAU,QACVC,CAAM,kBACNC,CAAgB,cAChBC,CAAY,CAEZ,CADA,EACGC,EACJ,CAAGxB,EAGEyB,EAAiB,4BACrBtB,EACAuB,gBAAiBzB,EAAE,IAP0C,MAQ7D0B,UAAU,UACVtB,EACAC,KAAMA,GAAQ,EAAE,CAChBsB,OAAO,EACPC,2BAA4BrB,EAC5BsB,UAAWrB,EACXsB,gBAAiBf,qBACjBN,EACAsB,YAAY,EACZC,iBAAkBhB,EAClBiB,kBAAmBpB,GAA0C,GAC7DqB,eADwCrB,cACVD,GAA4B,CAAC,GAAI,GAAI,GAAI,CAA3BA,EAA+B,GAAG,CAC9EuB,oBAAqB7B,EACrB8B,oBAAqB1B,EACrB2B,aAAc1B,iBACdG,uBACAG,oBACAC,EACAoB,kBAAmB,UAACC,EAAAA,CAAYA,CAAAA,CAAAA,GAChCC,SAAU,UAACC,IAAAA,CAAEC,UAAU,6CACvBvB,SACAC,eACAE,mBACAD,EACAqB,UAAW,WACb,EACA,MACE,UAACC,EAAAA,EAASA,CAAAA,CAAE,GAAGnB,CAAc,EAEjC,CAEA1B,EAAS8C,YAAY,CAAG,CACtBf,WAAW,EACXE,YAAY,EACZzB,UAAW,KACXU,WAAW,EACXC,qBAAsB,KACtBC,mBAAmB,EACnBC,YAAY,EACZE,iBAAkB,EACpB,EAEA,MAAevB,QAAQA,EAAC,oKCexB,MApH6B,IACzB,GAAM,CAAC+C,EAAWC,EAAe,CAAGC,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAmHjCC,EAnHoC,EACzC,EAAGC,EAAW,CAAGF,CAAAA,EAAAA,EAAAA,MAkHQC,EAlHRD,CAAQA,EAAC,GAC1B,CAACzC,EAAW4C,EAAa,CAAGH,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAC,GACrC,CAACI,EAASC,EAAW,CAAGL,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAC,IACjC,CAACM,EAAaC,EAAS,CAAGP,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAC,IACnC,CAACQ,EAAuBC,EAAyB,CAAGT,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAC,CAAC,GAC9DU,EAAY,IAAMH,GAAS,GAC3B,GAAEtD,CAAC,CAAE,CAAGC,CAAAA,EAAAA,EAAAA,EAAAA,CAAcA,CAAC,UAEvBG,EAAU,CACZ,CACIsD,KAAM1D,EAAE,SACR2D,SAAU,OACd,EACA,CACID,KAAM1D,EAAE,UACR2D,SAAU,GACVC,KAAM,GACF,WAACC,MAAAA,WACG,UAACC,IAAIA,CAACC,KAAK,6BAA6BC,GAAI,OAAvCF,iCAAqF,OAANG,EAAEC,GAAG,WAErF,UAACzB,IAAAA,CAAEC,UAAU,uBAEV,OAEP,UAACyB,IAAAA,CAAEC,QAAS,IAAMC,EAAWJ,YACzB,UAACxB,IAAAA,CAAEC,UAAU,4BACZ,MAGjB,EACH,CACK4B,EAAwB,CAC1BC,KAAM,CAAEC,MAAO,KAAM,EACrBC,MAAOtB,EACPuB,KAAM,EACNC,MAAO,CAAC,CACZ,EAEAC,CAAAA,EAAAA,EAAAA,SAAAA,CAASA,CAAC,KACNC,GACJ,EAAG,EAAE,EAEL,IAAMA,EAAyB,UAC3B5B,GAAW,GACX,IAAM6B,EAAW,MAAMC,EAAAA,CAAUA,CAACC,GAAG,CAAC,mBAAoBV,GACtDQ,GAAYA,EAASzE,IAAI,EAAIyE,EAASzE,IAAI,CAAC4E,MAAM,CAAG,GAAG,CACvDnC,EAAegC,EAASzE,IAAI,EAC5B6C,EAAa4B,EAASI,UAAU,EAChCjC,GAAW,GAEnB,EAQMvC,EAAsB,MAAOyE,EAAiBT,KAChDJ,EAAsBG,KAAK,CAAGU,EAC9Bb,EAAsBI,IAAI,CAAGA,EAC7BzB,GAAW,GACX,IAAM6B,EAAW,MAAMC,EAAAA,CAAUA,CAACC,GAAG,CAAC,mBAAoBV,GACtDQ,GAAYA,EAASzE,IAAI,EAAIyE,EAASzE,IAAI,CAAC4E,MAAM,CAAG,GAAG,CACvDnC,EAAegC,EAASzE,IAAI,EAC5B+C,EAAW+B,GACXlC,EAAW,IAEnB,EAEMmC,EAAe,UACjB,GAAI,CACA,MAAML,EAAAA,CAAUA,CAACM,MAAM,CAAC,oBAA0C,OAAtB9B,IAC5CsB,IACAvB,GAAS,GACTgC,EAAAA,EAAKA,CAACC,OAAO,CAACvF,EAAE,mEACpB,CAAE,MAAOwF,EAAO,CACZF,EAAAA,EAAKA,CAACE,KAAK,CAACxF,EAAE,6DAClB,CACJ,EAEMqE,EAAa,MAAOoB,IACtBjC,EAAyBiC,EAAIvB,GAAG,EAChCZ,GAAS,EACb,EAEA,MACI,WAACO,MAAAA,WACG,WAAC6B,EAAAA,CAAKA,CAAAA,CAACC,KAAMtC,EAAauC,OAAQnC,YAC9B,UAACiC,EAAAA,CAAKA,CAACG,MAAM,EAACC,WAAW,aACrB,UAACJ,EAAAA,CAAKA,CAACK,KAAK,WAAE/F,EAAE,6CAEpB,UAAC0F,EAAAA,CAAKA,CAACM,IAAI,WAAEhG,EAAE,yCACf,WAAC0F,EAAAA,CAAKA,CAACO,MAAM,YACT,UAACC,EAAAA,CAAMA,CAAAA,CAACC,QAAQ,YAAY/B,QAASX,WAChCzD,EAAE,YAEP,UAACkG,EAAAA,CAAMA,CAAAA,CAACC,QAAQ,UAAU/B,QAASgB,WAC9BpF,EAAE,eAKf,UAACF,EAAAA,CAAQA,CAAAA,CACLM,QAASA,EACTC,KAAMwC,EACNvC,UAAWA,EACXU,WAAW,EACXN,oBAAqBA,EACrBC,iBAzDa,CAyDKA,GAxD1B2D,EAAsBG,KAAK,CAAGtB,EAC9BmB,EAAsBI,IAAI,CAAGA,EAC7BG,GACJ,MAyDJ", "sources": ["webpack://_N_E/?4c08", "webpack://_N_E/./components/common/RKITable.tsx", "webpack://_N_E/./pages/adminsettings/institutiontypes/institutionTypeTable.tsx"], "sourcesContent": ["\n    (window.__NEXT_P = window.__NEXT_P || []).push([\n      \"/adminsettings/institutiontypes/institutionTypeTable\",\n      function () {\n        return require(\"private-next-pages/adminsettings/institutiontypes/institutionTypeTable.tsx\");\n      }\n    ]);\n    if(module.hot) {\n      module.hot.dispose(function () {\n        window.__NEXT_P.push([\"/adminsettings/institutiontypes/institutionTypeTable\"])\n      });\n    }\n  ", "//Import Library\r\nimport React from 'react'\r\nimport DataTable  from 'react-data-table-component';\r\n\r\n//Import services/components\r\nimport { useTranslation } from 'next-i18next';\r\nimport CustomLoader from './CustomLoader';\r\n\r\ninterface RKITableProps {\r\n  columns: any[];\r\n  data: any[];\r\n  totalRows?: number;\r\n  resetPaginationToggle?: boolean;\r\n  subheader?: boolean;\r\n  subHeaderComponent?: React.ReactNode;\r\n  handlePerRowsChange?: (newPerPage: number, page: number) => void;\r\n  handlePageChange?: (page: number) => void;\r\n  rowsPerPage?: number[];\r\n  defaultRowsPerPage?: number;\r\n  selectableRows?: boolean;\r\n  loading?: boolean;\r\n  pagServer?: boolean;\r\n  onSelectedRowsChange?: (selectedRows: any) => void;\r\n  clearSelectedRows?: boolean;\r\n  sortServer?: boolean;\r\n  onSort?: (column: any, sortDirection: string) => void;\r\n  persistTableHead?: boolean;\r\n  sortFunction?: (rows: any[], field: any, direction: 'asc' | 'desc') => any[];\r\n  noHeader?: boolean;\r\n  dense?: boolean;\r\n  pagination?: boolean;\r\n  paginationServer?: boolean;\r\n  paginationTotalRows?: number;\r\n  subHeaderAlign?: string;\r\n  [key: string]: any;\r\n}\r\n\r\nfunction RKITable(props: RKITableProps) {\r\n  const { t } = useTranslation('common');\r\n  const paginationComponentOptions = {\r\n    rowsPerPageText: t('Rowsperpage'),\r\n    };\r\n  const {\r\n    columns,\r\n    data,\r\n    totalRows,\r\n    resetPaginationToggle,\r\n    subheader,\r\n    subHeaderComponent,\r\n    handlePerRowsChange,\r\n    handlePageChange,\r\n    rowsPerPage,\r\n    defaultRowsPerPage,\r\n    selectableRows,\r\n    loading,\r\n    pagServer,\r\n    onSelectedRowsChange,\r\n    clearSelectedRows,\r\n    sortServer,\r\n    onSort,\r\n    persistTableHead,\r\n    sortFunction,\r\n    // Filter out any unknown props that might cause DOM warnings\r\n    ...otherProps\r\n  } = props;\r\n\r\n  // Only pass known DataTable props to avoid DOM warnings\r\n  const dataTableProps = {\r\n    paginationComponentOptions,\r\n    noDataComponent: t(\"NoData\"),\r\n    noHeader: true,\r\n    columns,\r\n    data: data || [],\r\n    dense: true,\r\n    paginationResetDefaultPage: resetPaginationToggle,\r\n    subHeader: subheader,\r\n    progressPending: loading,\r\n    subHeaderComponent,\r\n    pagination: true,\r\n    paginationServer: pagServer,\r\n    paginationPerPage: defaultRowsPerPage ? defaultRowsPerPage : 10,\r\n    paginationRowsPerPageOptions: rowsPerPage ? rowsPerPage : [10, 15, 20, 25, 30],\r\n    paginationTotalRows: totalRows,\r\n    onChangeRowsPerPage: handlePerRowsChange,\r\n    onChangePage: handlePageChange,\r\n    selectableRows,\r\n    onSelectedRowsChange,\r\n    clearSelectedRows,\r\n    progressComponent: <CustomLoader/>,\r\n    sortIcon: <i className=\"sort-icon fas fa-exchange-alt\" />,\r\n    sortServer,\r\n    onSort,\r\n    sortFunction,\r\n    persistTableHead,\r\n    className: \"rki-table\"\r\n  };\r\n  return (\r\n    <DataTable {...dataTableProps} />\r\n  )\r\n};\r\n\r\nRKITable.defaultProps = {\r\n  subHeader: false,\r\n  pagination: true,\r\n  totalRows: null,\r\n  pagServer: true,\r\n  onSelectedRowsChange: null,\r\n  clearSelectedRows: false,\r\n  sortServer: false,\r\n  persistTableHead: false\r\n}\r\n\r\nexport default RKITable;\r\n\r\n", "//Import Library\r\nimport Link from \"next/link\";\r\nimport { useState, useEffect } from \"react\";\r\nimport { <PERSON><PERSON>, But<PERSON> } from \"react-bootstrap\";\r\nimport toast from 'react-hot-toast';\r\n\r\n//Import services/components\r\nimport RKITable from \"../../../components/common/RKITable\";\r\nimport apiService from \"../../../services/apiService\";\r\nimport { useTranslation } from 'next-i18next';\r\n\r\nconst InstitutionTypeTable = (_props: any) => {\r\n    const [tabledata, setDataToTable] = useState([]);\r\n    const [, setLoading] = useState(false);\r\n    const [totalRows, setTotalRows] = useState(0);\r\n    const [perPage, setPerPage] = useState(10);\r\n    const [isModalShow, setModal] = useState(false);\r\n    const [selectInstitutionType, setSelectInstitutionType] = useState({});\r\n    const modalHide = () => setModal(false);\r\n    const { t } = useTranslation('common');\r\n\r\n    const columns = [\r\n        {\r\n            name: t(\"Title\"),\r\n            selector: \"title\",\r\n        },\r\n        {\r\n            name: t(\"action\"),\r\n            selector: \"\",\r\n            cell: (d: any) => (\r\n                <div>\r\n                    <Link href=\"/adminsettings/[...routes]\" as={`/adminsettings/edit_institution_type/${d._id}`}>\r\n\r\n                        <i className=\"icon fas fa-edit\" />\r\n\r\n                    </Link>\r\n                    &nbsp;\r\n                    <a onClick={() => userAction(d)}>\r\n                        <i className=\"icon fas fa-trash-alt\" />\r\n                    </a>{\" \"}\r\n                </div>\r\n            ),\r\n        },\r\n    ];\r\n    const institutionTypeParams = {\r\n        sort: { title: \"asc\" },\r\n        limit: perPage,\r\n        page: 1,\r\n        query: {},\r\n    };\r\n\r\n    useEffect(() => {\r\n        getInstitutionTypeData();\r\n    }, []);\r\n\r\n    const getInstitutionTypeData = async () => {\r\n        setLoading(true);\r\n        const response = await apiService.get(\"/institutiontype\", institutionTypeParams);\r\n        if (response && response.data && response.data.length > 0) {\r\n            setDataToTable(response.data);\r\n            setTotalRows(response.totalCount);\r\n            setLoading(false);\r\n        }\r\n    };\r\n\r\n    const handlePageChange = (page: any) => {\r\n        institutionTypeParams.limit = perPage;\r\n        institutionTypeParams.page = page;\r\n        getInstitutionTypeData();\r\n    };\r\n\r\n    const handlePerRowsChange = async (newPerPage: any, page: any) => {\r\n        institutionTypeParams.limit = newPerPage;\r\n        institutionTypeParams.page = page;\r\n        setLoading(true);\r\n        const response = await apiService.get(\"/institutiontype\", institutionTypeParams);\r\n        if (response && response.data && response.data.length > 0) {\r\n            setDataToTable(response.data);\r\n            setPerPage(newPerPage);\r\n            setLoading(false);\r\n        }\r\n    };\r\n\r\n    const modalConfirm = async () => {\r\n        try {\r\n            await apiService.remove(`/institutiontype/${selectInstitutionType}`);\r\n            getInstitutionTypeData();\r\n            setModal(false);\r\n            toast.success(t(\"adminsetting.Organisationtypes.Table.orgTypeDeletedSuccessfully\"));\r\n        } catch (error) {\r\n            toast.error(t(\"adminsetting.Organisationtypes.Table.errorDeletingOrgType\"));\r\n        }\r\n    };\r\n\r\n    const userAction = async (row: any) => {\r\n        setSelectInstitutionType(row._id);\r\n        setModal(true);\r\n    };\r\n\r\n    return (\r\n        <div>\r\n            <Modal show={isModalShow} onHide={modalHide}>\r\n                <Modal.Header closeButton>\r\n                    <Modal.Title>{t(\"adminsetting.Organisationtypes.Delete\")}</Modal.Title>\r\n                </Modal.Header>\r\n                <Modal.Body>{t(\"adminsetting.Organisationtypes.sure\")}</Modal.Body>\r\n                <Modal.Footer>\r\n                    <Button variant=\"secondary\" onClick={modalHide}>\r\n                        {t(\"Cancel\")}\r\n                    </Button>\r\n                    <Button variant=\"primary\" onClick={modalConfirm}>\r\n                        {t(\"yes\")}\r\n                    </Button>\r\n                </Modal.Footer>\r\n            </Modal>\r\n\r\n            <RKITable\r\n                columns={columns}\r\n                data={tabledata}\r\n                totalRows={totalRows}\r\n                pagServer={true}\r\n                handlePerRowsChange={handlePerRowsChange}\r\n                handlePageChange={handlePageChange}\r\n            />\r\n        </div>\r\n    );\r\n};\r\nexport default InstitutionTypeTable;\r\n"], "names": ["RKITable", "props", "t", "useTranslation", "paginationComponentOptions", "rowsPerPageText", "columns", "data", "totalRows", "resetPaginationToggle", "subheader", "subHeaderComponent", "handlePerRowsChange", "handlePageChange", "rowsPerPage", "defaultRowsPerPage", "selectableRows", "loading", "pagServer", "onSelectedRowsChange", "clearSelectedRows", "sortServer", "onSort", "persistTableHead", "sortFunction", "otherProps", "dataTableProps", "noDataComponent", "<PERSON><PERSON><PERSON><PERSON>", "dense", "paginationResetDefaultPage", "subHeader", "progressPending", "pagination", "paginationServer", "paginationPerPage", "paginationRowsPerPageOptions", "paginationTotalRows", "onChangeRowsPerPage", "onChangePage", "progressComponent", "CustomLoader", "sortIcon", "i", "className", "DataTable", "defaultProps", "tabledata", "setDataToTable", "useState", "InstitutionTypeTable", "setLoading", "setTotalRows", "perPage", "setPerPage", "isModalShow", "setModal", "selectInstitutionType", "setSelectInstitutionType", "modalHide", "name", "selector", "cell", "div", "Link", "href", "as", "d", "_id", "a", "onClick", "userAction", "institutionTypeParams", "sort", "title", "limit", "page", "query", "useEffect", "getInstitutionTypeData", "response", "apiService", "get", "length", "totalCount", "newPerPage", "modalConfirm", "remove", "toast", "success", "error", "row", "Modal", "show", "onHide", "Header", "closeButton", "Title", "Body", "Footer", "<PERSON><PERSON>", "variant"], "sourceRoot": "", "ignoreList": []}