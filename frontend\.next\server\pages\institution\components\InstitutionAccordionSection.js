(()=>{var e={};e.id=9361,e.ids=[636,3220,9361],e.modules={123:e=>{"use strict";e.exports=require("dom-helpers/removeEventListener")},1332:e=>{"use strict";e.exports=require("react-custom-scrollbars-2")},1428:e=>{"use strict";e.exports=import("axios")},1680:e=>{"use strict";e.exports=require("@restart/ui/utils")},1919:e=>{"use strict";e.exports=require("react-transition-group/Transition")},3892:e=>{"use strict";e.exports=require("classnames")},4048:e=>{"use strict";e.exports=require("react-truncate")},5850:(e,t,r)=>{"use strict";r.a(e,async(e,s)=>{try{r.r(t),r.d(t,{default:()=>c});var i=r(8732);r(82015);var n=r(93024),o=r(42447),a=r(88751),u=e([o]);o=(u.then?(await u)():u)[0];let c=e=>{let{t}=(0,a.useTranslation)("common");return(0,i.jsx)(n.A,{defaultActiveKey:"1",children:(0,i.jsxs)(n.A.Item,{eventKey:"1",children:[(0,i.jsx)(n.A.Header,{children:(0,i.jsx)("div",{className:"cardTitle",children:t("MediaGallery")})}),(0,i.jsx)(n.A.Body,{children:(0,i.jsx)(o.A,{gallery:e.images,imageSource:e.images_src})})]})})};s()}catch(e){s(e)}})},6009:e=>{"use strict";e.exports=require("dom-helpers/ownerDocument")},6952:e=>{"use strict";e.exports=require("@restart/ui/Modal")},7374:e=>{"use strict";e.exports=require("@restart/ui/Dropdown")},8732:e=>{"use strict";e.exports=require("react/jsx-runtime")},9653:e=>{"use strict";e.exports=require("@restart/ui/Overlay")},10735:(e,t,r)=>{"use strict";r.a(e,async(e,s)=>{try{r.r(t),r.d(t,{config:()=>j,default:()=>d,getServerSideProps:()=>h,getStaticPaths:()=>m,getStaticProps:()=>x,reportWebVitals:()=>f,routeModule:()=>v,unstable_getServerProps:()=>w,unstable_getServerSideProps:()=>A,unstable_getStaticParams:()=>y,unstable_getStaticPaths:()=>q,unstable_getStaticProps:()=>g});var i=r(63885),n=r(80237),o=r(81413),a=r(9616),u=r.n(a),c=r(72386),l=r(97125),p=e([c,l]);[c,l]=p.then?(await p)():p;let d=(0,o.M)(l,"default"),x=(0,o.M)(l,"getStaticProps"),m=(0,o.M)(l,"getStaticPaths"),h=(0,o.M)(l,"getServerSideProps"),j=(0,o.M)(l,"config"),f=(0,o.M)(l,"reportWebVitals"),g=(0,o.M)(l,"unstable_getStaticProps"),q=(0,o.M)(l,"unstable_getStaticPaths"),y=(0,o.M)(l,"unstable_getStaticParams"),w=(0,o.M)(l,"unstable_getServerProps"),A=(0,o.M)(l,"unstable_getServerSideProps"),v=new i.PagesRouteModule({definition:{kind:n.A.PAGES,page:"/institution/components/InstitutionAccordionSection",pathname:"/institution/components/InstitutionAccordionSection",bundlePath:"",filename:""},components:{App:c.default,Document:u()},userland:l});s()}catch(e){s(e)}})},11242:e=>{"use strict";e.exports=require("redux-persist/integration/react")},11688:e=>{"use strict";e.exports=require("@restart/hooks/useWillUnmount")},12388:e=>{"use strict";e.exports=require("dom-helpers/addEventListener")},13364:e=>{"use strict";e.exports=require("redux-saga/effects")},14062:e=>{"use strict";e.exports=import("react-redux")},14078:e=>{"use strict";e.exports=import("swr")},14332:e=>{"use strict";e.exports=require("uncontrollable")},16116:e=>{"use strict";e.exports=require("invariant")},18622:e=>{"use strict";e.exports=require("yup")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},22326:e=>{"use strict";e.exports=require("react-dom")},22541:e=>{"use strict";e.exports=require("redux-persist/lib/storage")},25303:e=>{"use strict";e.exports=require("@restart/ui/NavItem")},26324:e=>{"use strict";e.exports=require("warning")},27825:e=>{"use strict";e.exports=require("lodash")},27910:e=>{"use strict";e.exports=require("stream")},28217:e=>{"use strict";e.exports=require("@restart/ui/DropdownItem")},28966:(e,t,r)=>{"use strict";r.r(t),r.d(t,{canAddInstitution:()=>a,canAddInstitutionForm:()=>u,canEditInstitution:()=>c,canEditInstitutionForm:()=>l,canManageFocalPoints:()=>d,canViewDiscussionUpdate:()=>p,default:()=>x});var s=r(8732);r(82015);var i=r(81366),n=r.n(i),o=r(61421);let a=n()({authenticatedSelector:e=>!!e.permissions&&!!e.permissions.institution&&!!e.permissions.institution["create:any"],wrapperDisplayName:"CanAddInstitution"}),u=n()({authenticatedSelector:e=>!!e.permissions&&!!e.permissions.institution&&!!e.permissions.institution["create:any"],wrapperDisplayName:"CanAddInstitutionForm",FailureComponent:()=>(0,s.jsx)(o.default,{})}),c=n()({authenticatedSelector:(e,t)=>{if(e.permissions&&e.permissions.institution){if(e.permissions.institution["update:any"])return!0;else if(e.permissions.institution["update:own"]&&t.institution&&t.institution.user&&t.institution.user===e.user._id)return!0}return!1},wrapperDisplayName:"CanEditInstitution"}),l=n()({authenticatedSelector:(e,t)=>{if(e.permissions&&e.permissions.institution){if(e.permissions.institution["update:any"])return!0;else if(e.permissions.institution["update:own"]&&t.institution&&t.institution.user&&t.institution.user===e.user._id)return!0}return!1},wrapperDisplayName:"CanEditInstitutionForm",FailureComponent:()=>(0,s.jsx)(o.default,{})}),p=n()({authenticatedSelector:e=>!!e.permissions&&!!e.permissions.update&&!!e.permissions.update["read:any"],wrapperDisplayName:"CanViewDiscussionUpdate"}),d=n()({authenticatedSelector:(e,t)=>{if(e.permissions&&e.permissions.institution_focal_point){if(e.permissions.institution_focal_point["update:any"])return!0;else if(e.permissions.institution_focal_point["update:own"]&&t.institution&&t.institution.user&&t.institution.user===e.user._id)return!0}return!1},wrapperDisplayName:"canManageFocalPoints"}),x=a},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},29628:e=>{"use strict";e.exports=require("dom-helpers/addClass")},29825:e=>{"use strict";e.exports=require("prop-types")},29841:e=>{"use strict";e.exports=require("dom-helpers/hasClass")},30362:e=>{"use strict";e.exports=require("@restart/ui/DropdownToggle")},33873:e=>{"use strict";e.exports=require("path")},36653:e=>{"use strict";e.exports=require("nprogress")},36955:e=>{"use strict";e.exports=require("@restart/hooks/useBreakpoint")},37766:e=>{"use strict";e.exports=require("@restart/hooks/useMergedRefs")},39756:e=>{"use strict";e.exports=import("redux")},40051:e=>{"use strict";e.exports=require("dom-helpers/removeClass")},40361:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/pages.runtime.prod.js")},42447:(e,t,r)=>{"use strict";r.a(e,async(e,s)=>{try{r.d(t,{A:()=>d});var i=r(8732),n=r(82015),o=r(81149),a=r(82053),u=r(54131),c=r(91353),l=r(88751);r(72025);var p=e([u]);u=(p.then?(await p)():p)[0];let d=e=>{let{t}=(0,l.useTranslation)("common"),[r,s]=(0,n.useState)([]),p=e=>{let r=/(http|https):\/\/(\w+:{0,1}\w*)?(\S+)(:[0-9]+)?(\/|\/([\w#!:.?+=&%!\-\/]))?/.test(e.description);return(0,i.jsxs)("div",{className:"carousel-legend",children:[(0,i.jsxs)("p",{className:"lead",children:[(0,i.jsx)("b",{children:t("Filename")})," ",e.originalName||"No Name found"]}),e.description&&(0,i.jsxs)("div",{className:"source_link",children:[(0,i.jsx)("p",{children:(0,i.jsx)("b",{children:t("Source")})}),r?(0,i.jsxs)("div",{children:[(0,i.jsx)(a.FontAwesomeIcon,{icon:u.faLink,size:"1x",color:"#999",className:"me-1"}),(0,i.jsx)("a",{className:"source_link",href:e.description,target:"_blank",rel:"noopener noreferrer",children:e.description})]}):(0,i.jsx)("div",{children:(0,i.jsx)("p",{className:"ps-0 py-0",style:{wordBreak:"break-all"},children:e.description})})]}),e.downloadLink&&(0,i.jsxs)(c.A,{className:"btn btn-success mt-2 btn--download",href:e.downloadLink,children:[t("Download"),(0,i.jsx)(a.FontAwesomeIcon,{icon:u.faDownload,size:"1x",className:"ms-1"})]})]})};return(0,n.useEffect)(()=>{let t=[];e&&e.gallery&&Array.isArray(e.gallery)&&e.gallery.map((r,s)=>{let i,n=r&&r.name.split(".").pop();switch(n){case"JPG":case"jpg":case"jpeg":case"png":i=`http://localhost:3001/api/v1/image/show/${r._id}`;break;case"pdf":i="/images/fileIcons/pdfFile.png";break;case"docx":i="/images/fileIcons/wordFile.png";break;case"xls":case"xlsx":i="/images/fileIcons/xlsFile.png";break;default:i="/images/fileIcons/otherFile.png"}let o=("docx"===n||"pdf"===n||"xls"===n||"xlsx"===n)&&`http://localhost:3001/api/v1/files/download/${r._id}`,a=`${r&&r.original_name?r.original_name:"No Name found"}`,u=e.imageSource&&Array.isArray(e.imageSource)&&e.imageSource.length>0?e.imageSource[s]:"";t.push({src:i,description:u,originalName:a,downloadLink:o})}),s(t)},[e]),(0,i.jsx)("div",{children:r&&0===r.length?(0,i.jsx)("div",{className:"border border-info my-3 mx-0",children:(0,i.jsx)("p",{className:"d-flex d-flex justify-content-center p-2 m-0",children:t("NoFilesFound!")})}):(0,i.jsx)(o.Carousel,{showThumbs:!0,showStatus:!0,showIndicators:!0,infiniteLoop:!0,useKeyboardArrows:!0,autoPlay:!1,stopOnHover:!0,swipeable:!0,dynamicHeight:!1,emulateTouch:!0,renderThumbs:()=>r.map((e,t)=>(0,i.jsx)("img",{src:e.src,alt:`Thumbnail ${t+1}`,style:{width:"60px",height:"60px",objectFit:"cover"}},t)),children:r.map((e,t)=>(0,i.jsxs)("div",{children:[(0,i.jsx)("img",{src:e.src,alt:e.originalName||"Gallery image",style:{maxHeight:"500px",objectFit:"contain"}}),p(e)]},t))})})};s()}catch(e){s(e)}})},42893:e=>{"use strict";e.exports=import("react-hot-toast")},43294:e=>{"use strict";e.exports=require("formik")},50009:e=>{"use strict";e.exports=require("@restart/ui/DropdownMenu")},54131:e=>{"use strict";e.exports=import("@fortawesome/free-solid-svg-icons")},57664:e=>{"use strict";e.exports=require("dom-helpers/scrollbarSize")},58928:e=>{"use strict";e.exports=require("dom-helpers/canUseDOM")},59717:e=>{"use strict";e.exports=require("@restart/ui/DropdownContext")},60560:e=>{"use strict";e.exports=require("dom-helpers/contains")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},65447:e=>{"use strict";e.exports=require("@restart/ui/Button")},67364:e=>{"use strict";e.exports=require("@restart/hooks/useCallbackRef")},68455:e=>{"use strict";e.exports=import("redux-saga")},69722:e=>{"use strict";e.exports=require("es6-promise")},72025:()=>{},74075:e=>{"use strict";e.exports=require("zlib")},74716:e=>{"use strict";e.exports=require("moment")},74987:e=>{"use strict";e.exports=require("@restart/ui/ModalManager")},78097:e=>{"use strict";e.exports=require("next-redux-saga")},78634:e=>{"use strict";e.exports=require("@restart/ui/Anchor")},78959:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>u});var s=r(8732);r(82015);var i=r(93024),n=r(19918),o=r.n(n),a=r(88751);let u=e=>{let{t}=(0,a.useTranslation)("common"),{institutionData:r,activeOperations:n,activeProjects:u}=e;return(0,s.jsx)(i.A,{defaultActiveKey:"0",children:(0,s.jsxs)(i.A.Item,{eventKey:"0",children:[(0,s.jsx)(i.A.Header,{children:(0,s.jsx)("div",{className:"cardTitle",children:t("MoreInfo")})}),(0,s.jsxs)(i.A.Body,{className:"institutionDetails ps-4",children:[(0,s.jsxs)("p",{children:[(0,s.jsx)("b",{children:t("OrganisationType")}),":",(0,s.jsxs)("span",{children:[" ",r.type?r.type.title:""]})]}),(0,s.jsxs)("p",{children:[(0,s.jsx)("b",{children:t("Network")}),":",(0,s.jsx)("span",{children:r.networks?r.networks.map((e,t)=>(0,s.jsx)("span",{children:(0,s.jsx)("li",{children:e.title})},t)):""})]}),(0,s.jsxs)("p",{children:[(0,s.jsx)("b",{children:t("ActiveOperation")}),":",(0,s.jsx)("span",{children:n&&n.length>0?n.map((e,t)=>(0,s.jsx)("li",{children:(0,s.jsx)(o(),{href:"/operation/[...routes]",as:`/operation/show/${e._id}`,children:e.title})},t)):(0,s.jsx)("li",{children:t("NoActiveoperationsfound")})})]}),(0,s.jsxs)("p",{children:[(0,s.jsx)("b",{children:t("Expertise")}),":",(0,s.jsxs)("span",{children:[" ",r.expertise?r.expertise.map((e,t)=>(0,s.jsxs)("li",{children:[e.title," ",(0,s.jsx)("br",{})]},t)):""]})]}),(0,s.jsxs)("p",{children:[(0,s.jsx)("b",{children:t("ActiveProject")}),":",(0,s.jsx)("span",{children:u&&u.length>0?u.map((e,t)=>(0,s.jsx)("li",{children:(0,s.jsx)(o(),{href:"/project/[...routes]",as:`/project/show/${e._id}`,children:e.title})},t)):(0,s.jsx)("li",{children:t("NoActiveprojectsfound")})})]}),(0,s.jsxs)("p",{children:[(0,s.jsx)("b",{children:t("Department")}),":",(0,s.jsx)("span",{children:r&&r.department?r.department:t("Nodepartmentfound")})]}),(0,s.jsxs)("p",{children:[(0,s.jsx)("b",{children:t("Unit")}),":",(0,s.jsx)("span",{children:r&&r.unit?r.unit:t("Nounitfound")})]})]})]})})}},80237:(e,t)=>{"use strict";Object.defineProperty(t,"A",{enumerable:!0,get:function(){return r}});var r=function(e){return e.PAGES="PAGES",e.PAGES_API="PAGES_API",e.APP_PAGE="APP_PAGE",e.APP_ROUTE="APP_ROUTE",e.IMAGE="IMAGE",e}({})},81149:e=>{"use strict";e.exports=require("react-responsive-carousel")},81366:e=>{"use strict";e.exports=require("redux-auth-wrapper/connectedAuthWrapper")},81413:(e,t)=>{"use strict";Object.defineProperty(t,"M",{enumerable:!0,get:function(){return function e(t,r){return r in t?t[r]:"then"in t&&"function"==typeof t.then?t.then(t=>e(t,r)):"function"==typeof t&&"default"===r?t:void 0}}})},81521:e=>{"use strict";e.exports=require("dom-helpers/querySelectorAll")},81895:e=>{"use strict";e.exports=require("@restart/hooks/useEventCallback")},82015:e=>{"use strict";e.exports=require("react")},82053:e=>{"use strict";e.exports=require("@fortawesome/react-fontawesome")},86842:e=>{"use strict";e.exports=require("@restart/ui/SelectableContext")},86843:e=>{"use strict";e.exports=require("moment/locale/de")},87571:e=>{"use strict";e.exports=require("dom-helpers/transitionEnd")},88751:e=>{"use strict";e.exports=require("next-i18next")},93787:e=>{"use strict";e.exports=require("redux-persist")},94947:e=>{"use strict";e.exports=require("@restart/hooks/useTimeout")},96196:e=>{"use strict";e.exports=require("next-redux-wrapper")},97125:(e,t,r)=>{"use strict";r.a(e,async(e,s)=>{try{r.r(t),r.d(t,{default:()=>p});var i=r(8732);r(82015);var n=r(93024),o=r(99368),a=r(28966),u=r(78959),c=r(5850),l=e([o,c]);[o,c]=l.then?(await l)():l;let p=e=>{let t=(0,a.canViewDiscussionUpdate)(()=>(0,i.jsx)(o.default,{...e.prop}));return(0,i.jsxs)(i.Fragment,{children:[(0,i.jsx)(n.A,{className:"countryAccordionNew",children:(0,i.jsx)(u.default,{...e})}),(0,i.jsx)(n.A,{className:"countryAccordionNew",children:(0,i.jsx)(c.default,{...e.institutionData})}),(0,i.jsx)(n.A,{className:"countryAccordionNew",children:(0,i.jsx)(t,{})})]})};s()}catch(e){s(e)}})},98320:e=>{"use strict";e.exports=require("@restart/hooks/useIsomorphicEffect")},99368:(e,t,r)=>{"use strict";r.a(e,async(e,s)=>{try{r.r(t),r.d(t,{default:()=>c});var i=r(8732);r(82015);var n=r(93024),o=r(82491),a=r(88751),u=e([o]);o=(u.then?(await u)():u)[0];let c=e=>{let{t}=(0,a.useTranslation)("common");return(0,i.jsx)(n.A,{defaultActiveKey:"2",children:(0,i.jsxs)(n.A.Item,{eventKey:"2",children:[(0,i.jsx)(n.A.Header,{children:(0,i.jsx)("div",{className:"cardTitle",children:t("Discussions")})}),(0,i.jsx)(n.A.Body,{children:(0,i.jsx)(o.A,{type:"hazard",id:e&&e.routes?e.routes[1]:null})})]})})};s()}catch(e){s(e)}})},99460:e=>{"use strict";e.exports=require("dom-helpers/css")}};var t=require("../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[6089,9216,9616,2386,2491],()=>r(10735));module.exports=s})();