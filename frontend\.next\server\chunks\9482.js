"use strict";exports.id=9482,exports.ids=[9482],exports.modules={6417:(e,t,a)=>{a.d(t,{A:()=>s});let r=a(82015).createContext(null);r.displayName="CardHeaderContext";let s=r},15119:(e,t,a)=>{a.r(t),a.d(t,{default:()=>o});var r=a(8732);a(82015);var s=a(83551),n=a(49481),l=a(91353),i=a(88751);let o=e=>{let{t}=(0,i.useTranslation)("common");return(0,r.jsx)(r.Fragment,{children:(0,r.jsxs)(s.A,{children:[(0,r.jsx)(n.A,{children:e.health_profile?(0,r.jsx)("a",{href:e.health_profile,target:"_blank",children:(0,r.jsx)(l.A,{className:"countryBtn d-grid",variant:"primary",size:"lg",children:t("healthprofile")})}):""}),(0,r.jsx)(n.A,{children:e.security_advice?(0,r.jsx)("a",{href:e.security_advice,target:"_blank",children:(0,r.jsx)(l.A,{className:"countryBtn d-grid",variant:"primary",size:"lg",children:t("securityadvice")})}):""})]})})}},18597:(e,t,a)=>{a.d(t,{A:()=>T});var r=a(3892),s=a.n(r),n=a(82015),l=a(80739),i=a(8732);let o=n.forwardRef(({className:e,bsPrefix:t,as:a="div",...r},n)=>(t=(0,l.oU)(t,"card-body"),(0,i.jsx)(a,{ref:n,className:s()(e,t),...r})));o.displayName="CardBody";let c=n.forwardRef(({className:e,bsPrefix:t,as:a="div",...r},n)=>(t=(0,l.oU)(t,"card-footer"),(0,i.jsx)(a,{ref:n,className:s()(e,t),...r})));c.displayName="CardFooter";var d=a(6417);let u=n.forwardRef(({bsPrefix:e,className:t,as:a="div",...r},o)=>{let c=(0,l.oU)(e,"card-header"),u=(0,n.useMemo)(()=>({cardHeaderBsPrefix:c}),[c]);return(0,i.jsx)(d.A.Provider,{value:u,children:(0,i.jsx)(a,{ref:o,...r,className:s()(t,c)})})});u.displayName="CardHeader";let p=n.forwardRef(({bsPrefix:e,className:t,variant:a,as:r="img",...n},o)=>{let c=(0,l.oU)(e,"card-img");return(0,i.jsx)(r,{ref:o,className:s()(a?`${c}-${a}`:c,t),...n})});p.displayName="CardImg";let y=n.forwardRef(({className:e,bsPrefix:t,as:a="div",...r},n)=>(t=(0,l.oU)(t,"card-img-overlay"),(0,i.jsx)(a,{ref:n,className:s()(e,t),...r})));y.displayName="CardImgOverlay";let m=n.forwardRef(({className:e,bsPrefix:t,as:a="a",...r},n)=>(t=(0,l.oU)(t,"card-link"),(0,i.jsx)(a,{ref:n,className:s()(e,t),...r})));m.displayName="CardLink";var f=a(7783);let h=(0,f.A)("h6"),g=n.forwardRef(({className:e,bsPrefix:t,as:a=h,...r},n)=>(t=(0,l.oU)(t,"card-subtitle"),(0,i.jsx)(a,{ref:n,className:s()(e,t),...r})));g.displayName="CardSubtitle";let x=n.forwardRef(({className:e,bsPrefix:t,as:a="p",...r},n)=>(t=(0,l.oU)(t,"card-text"),(0,i.jsx)(a,{ref:n,className:s()(e,t),...r})));x.displayName="CardText";let j=(0,f.A)("h5"),v=n.forwardRef(({className:e,bsPrefix:t,as:a=j,...r},n)=>(t=(0,l.oU)(t,"card-title"),(0,i.jsx)(a,{ref:n,className:s()(e,t),...r})));v.displayName="CardTitle";let _=n.forwardRef(({bsPrefix:e,className:t,bg:a,text:r,border:n,body:c=!1,children:d,as:u="div",...p},y)=>{let m=(0,l.oU)(e,"card");return(0,i.jsx)(u,{ref:y,...p,className:s()(t,m,a&&`bg-${a}`,r&&`text-${r}`,n&&`border-${n}`),children:c?(0,i.jsx)(o,{children:d}):d})});_.displayName="Card";let T=Object.assign(_,{Img:p,Title:v,Subtitle:g,Body:o,Link:m,Text:x,Header:u,Footer:c,ImgOverlay:y})},43438:(e,t,a)=>{a.r(t),a.d(t,{canViewDiscussionUpdate:()=>s,default:()=>n}),a(82015);var r=a(81366);let s=a.n(r)()({authenticatedSelector:e=>!!e.permissions&&!!e.permissions.update&&!!e.permissions.update["read:any"],wrapperDisplayName:"CanViewDiscussionUpdate"}),n=s},46396:(e,t,a)=>{a.a(e,async(e,r)=>{try{a.r(t),a.d(t,{default:()=>c});var s=a(8732);a(82015);var n=a(93024),l=a(42447),i=a(88751),o=e([l]);l=(o.then?(await o)():o)[0];let c=e=>{let{t}=(0,i.useTranslation)("common");return(0,s.jsx)(n.A,{defaultActiveKey:"0",children:(0,s.jsxs)(n.A.Item,{eventKey:"0",children:[(0,s.jsx)(n.A.Header,{children:(0,s.jsx)("div",{className:"cardTitle",children:t("MediaGallery")})}),(0,s.jsx)(n.A.Body,{children:(0,s.jsx)(l.A,{gallery:e.images,imageSource:e.imgSrc})})]})})};r()}catch(e){r(e)}})},49908:(e,t,a)=>{a.a(e,async(e,r)=>{try{a.r(t),a.d(t,{default:()=>f});var s=a(8732),n=a(82015),l=a(27825),i=a.n(l),o=a(99506),c=a(15119),d=a(56077),u=a(77136),p=a(63487),y=a(88751),m=e([d,u,p]);[d,u,p]=m.then?(await m)():m;let f=e=>{let t,{t:a}=(0,y.useTranslation)("common"),[r,l]=(0,n.useState)({title:"",health_profile:"",security_advice:"",_id:""}),[m,f]=(0,n.useState)({operation_count:0,project_count:0,event_count:0}),[h,g]=(0,n.useState)({}),[x,j]=(0,n.useState)(),[v,_]=(0,n.useState)(),[T]=(0,n.useState)(!1),[A,b]=(0,n.useState)(),[w,S]=(0,n.useState)([]),[N,P]=(0,n.useState)([]),[C,I]=(0,n.useState)([]),[k]=(0,n.useState)([]),[D,R]=(0,n.useState)([]),U={sort:{created_at:"asc"},query:{country:e.routes[1]},limit:"~",select:"-contact_details -end_date -start_date -description -link -media -reply -parent_vspace -user -type -updated_at -update_type -created_at"},E={sort:{doc_created_at:"asc"},query:{},limit:"~",DocTable:!0,select:"-contact_details -end_date -images -start_date -description -images -link -media -reply -parent_vspace -user -type -updated_at -update_type -created_at"},O={sort:{doc_created_at:"asc"},query:{},limit:"~",DocUpdateTable:!0,select:"-contact_details -end_date -images -start_date -description -images -link -media -reply -parent_vspace -user -type -updated_at -update_type -created_at"},H=async t=>{let a=await p.A.get(`/country/${e.routes[1]}`,t),r=[];a?.data?.map(e=>{e?.document?.length>0&&e.document.map((t,a)=>{t.description=e.document[a].docsrc,r.push(t)})}),S(r.flat())},M=async()=>{let t=await p.A.get(`/country/${e.routes[1]}`,O),a=[];t?.data?.map(e=>{e?.document?.length>0&&e.document.map((t,r)=>{t.description=e.document[r].docsrc,a.push(t)})}),P(a.flat())},$=async()=>{let e=[],t=[],a=[],r=[],s=await p.A.get("/operation",U);s&&(s?.data.map((s,n)=>{s?.document?.length>0&&(a.push(s.document),t.push(s.doc_src)),s?.images?.length>0&&(r.push(s.images),e.push(s.images_src))}),I(r.flat(1/0)),R(e.flat(1/0)))},q=async e=>{let t=await p.A.get("/projectStatus");if(t?.data?.length>0){let e=[];i().forEach(t.data,function(t){("Ongoing"==t.title||"Planning"==t.title)&&e.push(t._id)}),_(e)}return!1},G=async t=>{let a=await p.A.get(`/stats/country/${e.routes[1]}`,t);f(a)},B=async e=>{let t=await p.A.get("/operation_status");if(t?.data?.length>0){let e=[];i().forEach(t.data,function(t){("Deployed"==t.title||"Mobilizing"==t.title||"Monitoring"==t.title)&&e.push(t._id)}),j(e)}return!1};(0,n.useEffect)(()=>{if(e?.routes[1]){let t=async t=>{let a=await p.A.get(`/country/${e.routes[1]}`,t);g({lat:parseFloat(a&&Array.isArray(a.coordinates)&&a.coordinates[0]&&a.coordinates[0].latitude?a.coordinates[0].latitude:20.593684),lng:parseFloat(a&&Array.isArray(a.coordinates)&&a.coordinates[0]&&a.coordinates[0].longitude?a.coordinates[0].longitude:78.96288)}),l(a)},a=async e=>{let t=await p.A.get("/eventStatus",{query:{title:"Current"}});return t?.data?.length>0&&b(t.data[0]._id),!1};t({}),G({}),B({}),q({}),a({}),H(E),M(),$()}},[]);let F=r?.health_profile?.replace("en",a("healthProfileLang"));switch(a("securityAdviceLang")){case"en":case"fr":t=r?.security_advice?.replace("/de/aussenpolitik/laender/","/en/aussenpolitik/laenderinformationen/");break;case"de":t=r?.security_advice?.replace("/de",`/${a("securityAdviceLang")}`)}let K={health_profile:F,security_advice:t};return(0,s.jsxs)("div",{children:[(0,s.jsx)(u.A,{routes:e.routes}),(0,s.jsx)(o.default,{latlng:h,countryData:r,operationStatusId:x,countryStatsData:m,eventStatusId:A,projectStatusId:v}),(0,s.jsx)("br",{}),(0,s.jsx)(c.default,{...K}),(0,s.jsx)(d.default,{prop:e,images:C,imgSrc:D,loading:T,updateSort:t=>{let a={sort:{},query:{parent_country:e.routes[1]},limit:"~",DocTable:!0,select:"-contact_details -end_date -images -start_date -description -images -link -media -reply -parent_vspace -user -type -updated_at -update_type -created_at"};a.sort={[t.columnSelector]:t.sortDirection},H(a)},document:w,docSrc:k,updateDocumentSort:t=>{({sort:{},query:{parent_country:e.routes[1]},limit:"~",DocUpdateTable:!0,select:"-contact_details -end_date -images -start_date -description -images -link -media -reply -parent_vspace -user -type -updated_at -update_type -created_at"}).sort={[t.columnSelector]:t.sortDirection},M()},updateDocument:N})]})};r()}catch(e){r(e)}})},56077:(e,t,a)=>{a.a(e,async(e,r)=>{try{a.r(t),a.d(t,{default:()=>p});var s=a(8732);a(82015);var n=a(93024),l=a(67640),i=a(46396),o=a(90717),c=a(73888),d=a(43438),u=e([l,i,c]);[l,i,c]=u.then?(await u)():u;let p=e=>{let t=(0,d.default)(()=>(0,s.jsx)(c.default,{...e.prop}));return(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)(n.A,{defaultActiveKey:"0",className:"countryAccordionNew",children:(0,s.jsx)(l.default,{...e})}),(0,s.jsx)(n.A,{className:"countryAccordionNew",children:(0,s.jsx)(i.default,{...e})}),(0,s.jsx)(n.A,{className:"countryAccordionNew",children:(0,s.jsx)(o.default,{...e})}),(0,s.jsx)(n.A,{className:"countryAccordionNew",children:(0,s.jsx)(t,{...e})})]})};r()}catch(e){r(e)}})},56084:(e,t,a)=>{a.d(t,{A:()=>c});var r=a(8732);a(82015);var s=a(38609),n=a.n(s),l=a(88751),i=a(30370);function o(e){let{t}=(0,l.useTranslation)("common"),a={rowsPerPageText:t("Rowsperpage")},{columns:s,data:o,totalRows:c,resetPaginationToggle:d,subheader:u,subHeaderComponent:p,handlePerRowsChange:y,handlePageChange:m,rowsPerPage:f,defaultRowsPerPage:h,selectableRows:g,loading:x,pagServer:j,onSelectedRowsChange:v,clearSelectedRows:_,sortServer:T,onSort:A,persistTableHead:b,sortFunction:w,...S}=e,N={paginationComponentOptions:a,noDataComponent:t("NoData"),noHeader:!0,columns:s,data:o||[],dense:!0,paginationResetDefaultPage:d,subHeader:u,progressPending:x,subHeaderComponent:p,pagination:!0,paginationServer:j,paginationPerPage:h||10,paginationRowsPerPageOptions:f||[10,15,20,25,30],paginationTotalRows:c,onChangeRowsPerPage:y,onChangePage:m,selectableRows:g,onSelectedRowsChange:v,clearSelectedRows:_,progressComponent:(0,r.jsx)(i.A,{}),sortIcon:(0,r.jsx)("i",{className:"sort-icon fas fa-exchange-alt"}),sortServer:T,onSort:A,sortFunction:w,persistTableHead:b,className:"rki-table"};return(0,r.jsx)(n(),{...N})}o.defaultProps={subHeader:!1,pagination:!0,totalRows:null,pagServer:!0,onSelectedRowsChange:null,clearSelectedRows:!1,sortServer:!1,persistTableHead:!1};let c=o},67640:(e,t,a)=>{a.a(e,async(e,r)=>{try{a.r(t),a.d(t,{default:()=>p});var s=a(8732);a(82015);var n=a(93024),l=a(7082),i=a(83551),o=a(49481),c=a(67666),d=a(88751),u=e([c]);c=(u.then?(await u)():u)[0];let p=e=>{let{t}=(0,d.useTranslation)("common");return(0,s.jsx)(n.A,{defaultActiveKey:"0",children:(0,s.jsxs)(n.A.Item,{eventKey:"0",children:[(0,s.jsx)(n.A.Header,{children:(0,s.jsx)("div",{className:"cardTitle",children:t("Organisation")})}),(0,s.jsx)(n.A.Body,{children:(0,s.jsx)(l.A,{fluid:!0,children:(0,s.jsx)(i.A,{children:(0,s.jsx)(o.A,{children:(0,s.jsx)(c.default,{...e.prop})})})})})]})})};r()}catch(e){r(e)}})},67666:(e,t,a)=>{a.a(e,async(e,r)=>{try{a.r(t),a.d(t,{default:()=>p});var s=a(8732),n=a(82015),l=a(19918),i=a.n(l),o=a(56084),c=a(63487),d=a(88751),u=e([c]);c=(u.then?(await u)():u)[0];let p=function(e){let[t,a]=(0,n.useState)([]),[,r]=(0,n.useState)(!1),[l,u]=(0,n.useState)(0),[p]=(0,n.useState)(10),y=e&&e.routes?e.routes[1]:null,[m,f]=(0,n.useState)(null),{t:h,i18n:g}=(0,d.useTranslation)("common"),x=g.language,j={sort:{},limit:p,page:1,instiTable:!0,query:{},languageCode:x},v=[{name:h("Organisation"),selector:"title",cell:e=>(0,s.jsx)(i(),{href:"/institution/[...routes]",as:`/institution/show/${e._id}`,children:e.title}),sortable:!0,maxWidth:"200px"},{name:h("ContactName"),selector:"contact_name",cell:e=>e.user?e.user.username:"",maxWidth:"200px"},{name:h("Expertise"),selector:"expertise",maxWidth:"200px"},{name:h("Region"),selector:"address.region",maxWidth:"200px"}],_=async(e,t)=>{r(!0),j.sort={[e.selector]:t};let a={sort:{[e.selector]:t},limit:p,page:1,instiTable:!0,query:{}};f(a),T(a)},T=async e=>{r(!0),0==Object.keys(e.sort).length&&(e.sort={created_at:"desc"});let t=await c.A.get(`/country/${y}/institution`,e);r(!0),t&&t.data&&t.data.length>0&&(t.data.forEach((e,a)=>{t.data[a].expertise=e.expertise.map(e=>e.title).join(", "),t.data[a].address.region=e.address.region.map(e=>e.title).join(", ")}),a(t.data),u(t.totalCount)),r(!1)},A=async(e,t)=>{j.limit=e,j.page=t,m&&(j.sort=m.sort),T(j)};return(0,s.jsx)(o.A,{columns:v,data:t,totalRows:l,handlePerRowsChange:A,handlePageChange:e=>{j.limit=p,j.page=e,m&&(j.sort=m.sort),T(j)},persistTableHead:!0,onSort:_})};r()}catch(e){r(e)}})},72953:(e,t,a)=>{a.d(t,{A:()=>y});var r=a(8732);a(82015);var s=a(94696);let n=({position:e,onCloseClick:t,children:a})=>(0,r.jsx)(s.InfoWindow,{position:e,onCloseClick:t,children:(0,r.jsx)("div",{children:a})}),l="labels.text.fill",i="labels.text.stroke",o="road.highway",c="geometry.stroke",d=[{elementType:"geometry",stylers:[{color:"#1d2c4d"}]},{elementType:l,stylers:[{color:"#8ec3b9"}]},{elementType:i,stylers:[{color:"#1a3646"}]},{featureType:"administrative",elementType:"geometry",stylers:[{visibility:"off"}]},{featureType:"administrative.country",elementType:c,stylers:[{color:"#4b6878"}]},{featureType:"administrative.land_parcel",elementType:"labels",stylers:[{visibility:"off"}]},{featureType:"administrative.land_parcel",elementType:l,stylers:[{color:"#64779e"}]},{featureType:"administrative.province",elementType:c,stylers:[{color:"#4b6878"}]},{featureType:"landscape.man_made",elementType:c,stylers:[{color:"#334e87"}]},{featureType:"landscape.natural",elementType:"geometry",stylers:[{color:"#023e58"}]},{featureType:"poi",stylers:[{visibility:"off"}]},{featureType:"poi",elementType:"geometry",stylers:[{color:"#283d6a"}]},{featureType:"poi",elementType:"labels.text",stylers:[{visibility:"off"}]},{featureType:"poi",elementType:l,stylers:[{color:"#6f9ba5"}]},{featureType:"poi",elementType:i,stylers:[{color:"#1d2c4d"}]},{featureType:"poi.park",elementType:"geometry.fill",stylers:[{color:"#023e58"}]},{featureType:"poi.park",elementType:l,stylers:[{color:"#3C7680"}]},{featureType:"road",stylers:[{visibility:"off"}]},{featureType:"road",elementType:"geometry",stylers:[{color:"#304a7d"}]},{featureType:"road",elementType:"labels.icon",stylers:[{visibility:"off"}]},{featureType:"road",elementType:l,stylers:[{color:"#98a5be"}]},{featureType:"road",elementType:i,stylers:[{color:"#1d2c4d"}]},{featureType:o,elementType:"geometry",stylers:[{color:"#2c6675"}]},{featureType:o,elementType:c,stylers:[{color:"#255763"}]},{featureType:o,elementType:l,stylers:[{color:"#b0d5ce"}]},{featureType:o,elementType:i,stylers:[{color:"#023e58"}]},{featureType:"road.local",elementType:"labels",stylers:[{visibility:"off"}]},{featureType:"transit",stylers:[{visibility:"off"}]},{featureType:"transit",elementType:l,stylers:[{color:"#98a5be"}]},{featureType:"transit",elementType:i,stylers:[{color:"#1d2c4d"}]},{featureType:"transit.line",elementType:"geometry.fill",stylers:[{color:"#283d6a"}]},{featureType:"transit.station",elementType:"geometry",stylers:[{color:"#3a4762"}]},{featureType:"water",elementType:"geometry",stylers:[{color:"#0e1626"}]},{featureType:"water",elementType:l,stylers:[{color:"#4e6d70"}]}];var u=a(44233),p=a(40691);let y=({markerInfo:e,activeMarker:t,initialCenter:a,children:l,height:i=300,width:o="114%",language:c,zoom:y=1,minZoom:m=1,onClose:f})=>{let{locale:h}=(0,u.useRouter)(),{isLoaded:g,loadError:x}=(0,p._)(),j={width:o,height:"number"==typeof i?`${i}px`:i};return x?(0,r.jsx)("div",{children:"Error loading maps"}):g?(0,r.jsx)("div",{className:"map-container",children:(0,r.jsx)("div",{className:"mapprint",style:{width:o,height:i,position:"relative"},children:(0,r.jsxs)(s.GoogleMap,{mapContainerStyle:j,center:a||{lat:52.520017,lng:13.404195},zoom:y,onLoad:e=>{e.setOptions({styles:d})},options:{minZoom:m,draggable:!0,keyboardShortcuts:!1,streetViewControl:!1,panControl:!1,clickableIcons:!1,mapTypeControl:!1,fullscreenControl:!0},children:[l,e&&t&&t.getPosition&&(0,r.jsx)(n,{position:t.getPosition(),onCloseClick:()=>{console.log("close click"),f?.()},children:e})]})})}):(0,r.jsx)("div",{children:"Loading Maps..."})}},73888:(e,t,a)=>{a.a(e,async(e,r)=>{try{a.r(t),a.d(t,{default:()=>c});var s=a(8732);a(82015);var n=a(93024),l=a(82491),i=a(88751),o=e([l]);l=(o.then?(await o)():o)[0];let c=e=>{let{t}=(0,i.useTranslation)("common");return(0,s.jsx)(n.A,{defaultActiveKey:"1",children:(0,s.jsxs)(n.A.Item,{eventKey:"1",children:[(0,s.jsx)(n.A.Header,{children:(0,s.jsx)("div",{className:"cardTitle",children:t("Discussion")})}),(0,s.jsx)(n.A.Body,{children:(0,s.jsx)(l.A,{type:"country",id:e?.routes?e.routes[1]:null})})]})})};r()}catch(e){r(e)}})},80237:(e,t)=>{Object.defineProperty(t,"A",{enumerable:!0,get:function(){return a}});var a=function(e){return e.PAGES="PAGES",e.PAGES_API="PAGES_API",e.APP_PAGE="APP_PAGE",e.APP_ROUTE="APP_ROUTE",e.IMAGE="IMAGE",e}({})},81413:(e,t)=>{Object.defineProperty(t,"M",{enumerable:!0,get:function(){return function e(t,a){return a in t?t[a]:"then"in t&&"function"==typeof t.then?t.then(t=>e(t,a)):"function"==typeof t&&"default"===a?t:void 0}}})},89364:(e,t,a)=>{a.d(t,{A:()=>n});var r=a(8732);a(82015);var s=a(94696);let n=({name:e="Marker",id:t="",countryId:a="",type:n,icon:l,position:i,onClick:o,title:c,draggable:d=!1})=>i&&"number"==typeof i.lat&&"number"==typeof i.lng?(0,r.jsx)(s.Marker,{position:i,icon:l,title:c||e,draggable:d,onClick:r=>{o&&o({name:e,id:t,countryId:a,type:n,position:i},{position:i,getPosition:()=>i},r)}}):null},90717:(e,t,a)=>{a.r(t),a.d(t,{default:()=>i});var r=a(8732);a(82015);var s=a(93024),n=a(88751),l=a(97377);let i=e=>{let{t}=(0,n.useTranslation)("common");return(0,r.jsx)(s.A,{defaultActiveKey:"0",children:(0,r.jsxs)(s.A.Item,{eventKey:"0",children:[(0,r.jsx)(s.A.Header,{children:(0,r.jsx)("div",{className:"cardTitle",children:t("Documents")})}),(0,r.jsxs)(s.A.Body,{children:[(0,r.jsx)(l.A,{loading:e.loading,sortProps:e.updateSort,docs:e.document,docsDescription:e.docSrc}),(0,r.jsx)("h6",{className:"mt-3",children:t("DocumentsfromUpdates")}),(0,r.jsx)(l.A,{loading:e.loading,sortProps:e.updateDocumentSort,docs:e.updateDocument,docsDescription:e.docSrc})]})]})})}},97377:(e,t,a)=>{a.d(t,{A:()=>o});var r=a(8732);a(82015);var s=a(74716),n=a.n(s),l=a(56084),i=a(88751);let o=({docs:e,docsDescription:t,sortProps:a,loading:s})=>{let o=async(e,t)=>{a({columnSelector:e.selector,sortDirection:t})},{t:c}=(0,i.useTranslation)("common"),d=[{name:c("FileType"),width:"15%",selector:"extension",cell:e=>e&&e.extension&&e.extension},{name:c("FileName"),width:"25%",selector:"document_title",cell:e=>e&&e.original_name&&(0,r.jsx)("a",{href:`http://localhost:3001/api/v1/files/download/${e._id}`,target:"_blank",children:e.original_name.split(".").slice(0,-1).join(".")}),sortable:!0},{name:c("Description"),selector:"description",cell:e=>e&&e.description&&e.description},{name:c("UploadedDate"),width:"25%",selector:"doc_created_at",cell:e=>e&&e.updated_at&&n()(e.updated_at).format("MM/DD/YYYY"),sortable:!0}];return(0,r.jsx)(l.A,{columns:d,data:e,pagServer:!0,onSort:o,persistTableHead:!0,loading:s})}},99506:(e,t,a)=>{a.r(t),a.d(t,{default:()=>u});var r=a(8732);a(82015);var s=a(83551),n=a(49481),l=a(19918),i=a.n(l),o=a(72953),c=a(89364),d=a(88751);let u=e=>{let{t}=(0,d.useTranslation)("common");return(0,r.jsx)(r.Fragment,{children:(0,r.jsx)(s.A,{children:(0,r.jsxs)(n.A,{xs:12,style:{display:"flex"},children:[(0,r.jsx)("div",{className:"countryMap",children:e?.latlng?.lat?(0,r.jsx)(o.A,{initialCenter:e.latlng,children:(0,r.jsx)(c.A,{icon:{url:"/images/map-marker-blue.svg"},position:e.latlng})}):null}),(0,r.jsxs)("div",{className:"countryInfo",children:[(0,r.jsxs)("h4",{children:[" ",e?.countryData?.title," "]}),function(e,t,a,s,n,l){return(0,r.jsxs)("div",{className:"countryInfoDetails",children:[(0,r.jsx)(i(),{href:{pathname:"/operation",query:{country:e?._id,status:t}},children:(0,r.jsxs)("div",{className:"countryInfo-Item",children:[(0,r.jsx)("div",{className:"countryInfo-img",children:(0,r.jsx)("img",{src:"/images/countryinfo1.png",width:"25",height:"25",alt:"Organization Quick Info"})}),(0,r.jsxs)("span",{children:[a("CurrentOperation"),(0,r.jsx)("br",{}),(0,r.jsx)("b",{children:s?.operation_count?s.operation_count:0})]})]})}),(0,r.jsx)(i(),{href:{pathname:"/event",query:{country:e?._id,status:n}},children:(0,r.jsxs)("div",{className:"countryInfo-Item",children:[(0,r.jsx)("div",{className:"countryInfo-img",children:(0,r.jsx)("img",{src:"/images/countryinfo2.png",width:"30",height:"22",alt:"Organization Quick Info"})}),(0,r.jsxs)("span",{children:[a("CurrentEvent"),(0,r.jsx)("br",{}),(0,r.jsx)("b",{children:s?.event_count?s.event_count:0})]})]})}),(0,r.jsx)(i(),{href:{pathname:"/project",query:{country:e._id,status:l}},children:(0,r.jsxs)("div",{className:"countryInfo-Item",children:[(0,r.jsx)("div",{className:"countryInfo-img",children:(0,r.jsx)("img",{src:"/images/quickinfo3.png",width:"24",height:"21",alt:"Organization Quick Info"})}),(0,r.jsxs)("span",{children:[a("CurrentProject"),(0,r.jsx)("br",{}),(0,r.jsx)("b",{children:s?.project_count?s.project_count:0})]})]})})]})}(e.countryData,e.operationStatusId,t,e.countryStatsData,e.eventStatusId,e.projectStatusId)]})]})})})}}};