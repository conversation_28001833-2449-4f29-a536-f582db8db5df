(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[6540],{40928:(e,t,i)=>{"use strict";i.r(t),i.d(t,{default:()=>g});var l=i(37876),s=i(14232),n=i(93131),r=i.n(n),a=i(7940),o=i.n(a),d=i(31195),c=i(56970),m=i(37784),u=i(60282),h=i(97685),x=i(53718),p=i(31753);let g=e=>{let{isOpen:t,onModalClose:i,image:n,getId:a,fileName:g,getBlob:f}=e,[j,b]=(0,s.useState)(1),[y,w]=(0,s.useState)(""),[v,C]=(0,s.useState)(null),A=(0,s.useRef)(null),{t:k}=(0,p.Bd)("common");(0,s.useEffect)(()=>{w(g)},[g]);let N=async()=>{let e=(e=>{var t;let i=e.split(","),l=null==(t=i[0].match(/:(.*?);/))?void 0:t[1],s=atob(i[1]),n=s.length,r=new Uint8Array(n);for(;n--;)r[n]=s.charCodeAt(n);return new Blob([r],{type:l})})(A.current.getImage().toDataURL("image/jpeg",.6));f((window.URL||window.webkitURL).createObjectURL(e));let t=new FormData;t.append("file",e,y);try{let e=await x.A.post("/image",t,{"Content-Type":"multipart/form-data"});e&&e._id&&a(e._id)}catch(e){throw"Something wrong in server || your data!"}h.Ay.success(k("toast.CroppedtheimageSuccessfully")),i(!1),C(null),w("none"),b(1)};return(0,l.jsx)(l.Fragment,{children:(0,l.jsx)("div",{children:(0,l.jsxs)(d.A,{show:t,size:"lg","aria-labelledby":"ProfileEdit",onHide:()=>i(!1),centered:!0,children:[(0,l.jsxs)(d.A.Body,{children:[(0,l.jsxs)("div",{className:"d-flex flex-column justify-content-center align-items-center imgRotate",children:[(0,l.jsx)(r(),{ref:A,width:700,height:400,borderRadius:2,scale:j,color:[0,0,0,.6],image:v||n,style:{width:"100%",height:"auto"}}),(0,l.jsx)("div",{className:"info-identifier",children:(0,l.jsx)("span",{children:k("ThisareawillcontainyourInstitutionandfocalpointinformation")})})]}),(0,l.jsx)("div",{className:"mx-2 my-3",children:(0,l.jsxs)(c.A,{children:[(0,l.jsx)(m.A,{sm:1,md:1,lg:1,className:"pe-0",children:(0,l.jsx)("b",{children:k("Zoom")})}),(0,l.jsx)(m.A,{sm:11,md:11,lg:11,children:(0,l.jsx)(o(),{value:j,tooltip:"auto",min:1,max:10,step:.01,variant:"primary",onChange:e=>b(Number(e.target.value))})})]})})]}),(0,l.jsxs)(d.A.Footer,{children:[(0,l.jsx)(u.A,{onClick:N,children:k("Crop")}),(0,l.jsx)(u.A,{variant:"danger",onClick:()=>i(!1),children:k("Cancel")})]})]})})})}},58060:(e,t,i)=>{(window.__NEXT_P=window.__NEXT_P||[]).push(["/institution/InstitutionImageHandler",function(){return i(62677)}])},62677:(e,t,i)=>{"use strict";i.r(t),i.d(t,{default:()=>u});var l=i(37876),s=i(14232),n=i(17336),r=i(21772),a=i(11041),o=i(97685),d=i(40928),c=i(53718),m=i(31753);let u=e=>{let{getId:t,header:i,type:u}=e,{t:h}=(0,m.Bd)("common"),[x,p]=(0,s.useState)(!1),[g,f]=(0,s.useState)([]),[j,b]=(0,s.useState)(""),[y,w]=(0,s.useState)(""),v="application"===u?"/files":"/image";(0,s.useEffect)(()=>{i?w("".concat("http://localhost:3001/api/v1","/image/show/").concat(i)):w(null)},[i]);let C={flex:1,display:"flex",flexDirection:"column",alignItems:"center",justifyContent:"center",width:"100%",height:"100%",borderWidth:.1,borderColor:"#fafafa",backgroundColor:"#fafafa",color:"black",transition:"border  .24s ease-in-out"},A={borderColor:"#2196f3"},k=e=>{p(e)},{getRootProps:N,getInputProps:_,isDragActive:S,isDragAccept:R,isDragReject:z,fileRejections:E}=(0,n.VB)({noClick:!1,accept:"image/*",multiple:!1,minSize:0,maxSize:2e6,onDrop:e=>{f(e.map((e,t)=>Object.assign(e,{preview:URL.createObjectURL(e)}))),e.length>0&&p(!0)},validator:function(e){return"/image"===v?"image"===e.type.substring(0,5)||o.Ay.error(h("toast.filetypenotsupport")):"/files"===v&&"image"===e.type.substring(0,5)&&o.Ay.error(h("toast.filetypenotsupport")),null}}),B=(0,s.useMemo)(()=>({...C,...S?A:{outline:"2px dashed #bbb"},...R?{outline:"2px dashed #595959"}:{outline:"2px dashed #bbb"},...z?{outline:"2px dashed red"}:{activeStyle:A}}),[S,z]),U=E.length>0&&E[0].file.size>2e6,I=e=>{b(e),t(e)},O=async()=>{let e;(e=i?await c.A.remove("image/".concat(i)):await c.A.remove("image/".concat(j)))&&e._id&&(w(null),I(null))},F=e=>{w(e)};return(0,l.jsxs)(l.Fragment,{children:[(0,l.jsx)("div",{className:" d-flex justify-content-center align-items-center mt-3",style:{width:"100%",height:"180px"},children:(0,l.jsxs)("div",{...N({style:B}),children:[(0,l.jsx)("input",{..._()}),(0,l.jsx)(r.g,{icon:a.rOd,size:"4x",color:"#999"}),(0,l.jsx)("p",{style:{color:"#595959",marginBottom:"0px"},children:h("Drag'n'dropsomefileshere,orclicktoselectfiles")}),(0,l.jsxs)("small",{style:{color:"#595959"},children:[(0,l.jsx)("b",{children:h("Note:")})," ",h("Onesingleimagewillbeaccepted")]}),U&&(0,l.jsxs)("small",{className:"text-danger mt-2",children:[(0,l.jsx)(r.g,{icon:a.tUE,size:"1x",color:"red"}),"\xa0",h("FileistoolargeItshouldbelessthan2MB")]}),z&&(0,l.jsxs)("small",{className:"text-danger",style:{color:"red"},children:[(0,l.jsx)(r.g,{icon:a.tUE,size:"1x",color:"red"}),h("Filetypenotacceptedsorr")]})]})}),y&&(0,l.jsx)(l.Fragment,{children:(0,l.jsx)("div",{style:{display:"flex",flexDirection:"row",justifyContent:"flex-start",flexWrap:"wrap",marginTop:20},children:(0,l.jsxs)("div",{style:{display:"inline-flex",borderRadius:2,border:"1px solid #ddd",marginBottom:8,marginRight:20,width:170,height:100,padding:2,position:"relative",boxShadow:"0 0 15px 0.25px rgba(0,0,0,0.25)",boxSizing:"border-box"},children:[(0,l.jsx)("div",{style:{display:"flex"},children:(0,l.jsx)("img",{src:y,style:{display:"block",height:"100%"}})}),(0,l.jsx)(r.g,{icon:a.s0Q,style:{position:"absolute",fontSize:"22px",top:"-10px",right:"-10px",zIndex:1e3,cursor:"pointer",backgroundColor:"#fff",color:"#000",borderRadius:"50%"},color:"black",onClick:O})]})})}),(0,l.jsx)(d.default,{isOpen:x,getId:e=>I(e),image:g&&g[0]?g[0].preview:"",onModalClose:e=>k(e),fileName:g&&g[0]?g[0].name:"",getBlob:e=>F(e)})]})}}},e=>{var t=t=>e(e.s=t);e.O(0,[7725,1772,7336,5266,636,6593,8792],()=>t(58060)),_N_E=e.O()}]);
//# sourceMappingURL=InstitutionImageHandler-462a81cdc3cd9add.js.map