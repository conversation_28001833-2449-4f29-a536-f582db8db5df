{"version": 3, "file": "static/chunks/pages/institution/components/InstitutionCoverSectionContent-bc73191b9b317560.js", "mappings": "gFACA,4CACA,yDACA,WACA,OAAe,EAAQ,IAA8E,CACrG,EACA,UAFsB", "sources": ["webpack://_N_E/?1d06"], "sourcesContent": ["\n    (window.__NEXT_P = window.__NEXT_P || []).push([\n      \"/institution/components/InstitutionCoverSectionContent\",\n      function () {\n        return require(\"private-next-pages/institution/components/InstitutionCoverSectionContent.tsx\");\n      }\n    ]);\n    if(module.hot) {\n      module.hot.dispose(function () {\n        window.__NEXT_P.push([\"/institution/components/InstitutionCoverSectionContent\"])\n      });\n    }\n  "], "names": [], "sourceRoot": "", "ignoreList": []}