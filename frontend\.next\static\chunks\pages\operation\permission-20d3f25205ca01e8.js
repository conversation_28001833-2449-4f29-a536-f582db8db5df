(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[7551],{22352:(e,i,r)=>{"use strict";r.r(i),r.d(i,{canAddOperation:()=>o,canAddOperationForm:()=>t,canEditOperation:()=>p,canEditOperationForm:()=>u,canViewDiscussionUpdate:()=>d,default:()=>m});var a=r(37876);r(14232);var n=r(8178),s=r(59626);let o=(0,n.A)({authenticatedSelector:e=>!!e.permissions&&!!e.permissions.operation&&!!e.permissions.operation["create:any"],wrapperDisplayName:"CanAddOperation"}),t=(0,n.A)({authenticatedSelector:e=>!!e.permissions&&!!e.permissions.operation&&!!e.permissions.operation["create:any"],wrapperDisplayName:"CanAddOperationForm",FailureComponent:()=>(0,a.jsx)(s.default,{})}),p=(0,n.A)({authenticatedSelector:(e,i)=>{if(e.permissions&&e.permissions.operation){if(e.permissions.operation["update:any"])return!0;else if(e.permissions.operation["update:own"]&&i.operation&&i.operation.user&&i.operation.user._id===e.user._id)return!0}return!1},wrapperDisplayName:"CanEditOperation"}),u=(0,n.A)({authenticatedSelector:(e,i)=>{if(e.permissions&&e.permissions.operation){if(e.permissions.operation["update:any"])return!0;else if(e.permissions.operation["update:own"]&&i.operation&&i.operation.user&&i.operation.user._id===e.user._id)return!0}return!1},wrapperDisplayName:"CanEditOperationForm",FailureComponent:()=>(0,a.jsx)(s.default,{})}),d=(0,n.A)({authenticatedSelector:e=>!!e.permissions&&!!e.permissions.update&&!!e.permissions.update["read:any"],wrapperDisplayName:"CanViewDiscussionUpdate"}),m=o},92110:(e,i,r)=>{(window.__NEXT_P=window.__NEXT_P||[]).push(["/operation/permission",function(){return r(22352)}])}},e=>{var i=i=>e(e.s=i);e.O(0,[636,6593,8792],()=>i(92110)),_N_E=e.O()}]);
//# sourceMappingURL=permission-20d3f25205ca01e8.js.map