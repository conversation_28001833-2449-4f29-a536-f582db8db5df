{"version": 3, "file": "static/chunks/4100.7ddfdbfe3dd233fc.js", "mappings": "0FAAO,SAASA,EAAY,gBAC1BC,GAAW,CAAK,CAChBC,SAAS,EAAK,UACdC,GAAW,CAAK,CACjB,CAJ2B,WAIxB,CAAC,EAJuB,EAK1B,OAAOF,GAAaC,GAAUC,CAChC,wFANgBH,qCAAAA,iHCEHI,qCAAAA,KAAN,IAAMA,EAAsCC,aAFjC,QAEiCA,OAAK,CAACC,aAAa,CAAC,CAAC,sGCS/DC,qCAAAA,KAXT,IAAIA,EAAW,IAAgB,sGCoB/B,qCAAwBC,aAnBuC,OAgBzDC,EAAkDC,EAAAA,eAAe,CACjEC,EAA4CC,EAAAA,IADhBC,KACyB,CAE5C,KAH8B,GACjBA,CAEJL,CAH4B,CAGjBM,CAAsB,EACvD,GAAM,EAH+B,KAAO,MAGpCC,CAAW,yBAAEC,CAAuB,CAAE,CAAGF,EAEjD,SAASG,IACP,GAAIF,GAAeA,EAAYG,gBAAgB,CAAE,CAC/C,IAAMC,EAAeC,EAAAA,QAAQ,CAACC,OAAO,CACnCC,MAAMC,IAAI,CAACR,EAAYG,gBAAgB,EAA0BM,MAAM,CACrEC,UAGJV,EAAYW,UAAU,CAACV,EAAwBG,EAAcL,GAC/D,CACF,CA2CA,OApCAL,EAA0B,SACxBM,EACA,OADAA,MAAAA,CAAAA,EAAAA,GAAAA,IAAAA,EAAAA,EAAaG,gBAAAA,GAAbH,EAA+BY,GAAG,CAACb,EAAMc,QAAQ,EAC1C,SACLb,CAAAA,OAAAA,GAA6B,EAA7BA,KAAAA,EAAAA,EAAaG,gBAAAA,GAAbH,EAA+Bc,MAAM,CAACf,EAAMc,QAAQ,CACtD,CACF,GAOAnB,EAA0B,KACpBM,IACFA,EAAYe,OADG,OACW,CAAGb,CAAAA,EAExB,KACDF,IACFA,EAAYe,OADG,OACW,CAAGb,CAAAA,CAEjC,IAGFN,EAAoB,KACdI,GAAeA,EAAYe,cAAc,EAAE,CAC7Cf,EAAYe,cAAc,GAC1Bf,EAAYe,cAAc,CAAG,MAExB,KACDf,GAAeA,EAAYe,cAAc,EAAE,CAC7Cf,EAAYe,cAAc,GAC1Bf,EAAYe,cAAc,CAAG,KAEjC,IAGK,IACT,2ICIqBC,uCAhFH,gBACD,QAGXC,EAA0C,CAC9C,IAAK,cACL,IAAK,+BACL,IAAK,qBACL,IAAK,uBACP,EASA,SAASC,EAAiB,CAIR,EAJQ,QACxBC,CAAG,KACHC,CAAG,KACHC,CAAG,CACa,CAJQ,EAuBxB,MAAO,CAAEC,WAjBPF,GAAOA,EAAIE,UAAU,CAAGF,EAAIE,UAAU,CAAGD,EAAMA,EAAIC,UAAU,CAAI,IAiB9CC,SAZRC,OAAOC,QAAQ,CAACF,QAAQ,CAavC,CAEA,IAAMG,EAA8C,CAClDC,MAAO,CAELC,WACE,8FACFC,OAAQ,QACRC,UAAW,SACXC,QAAS,OACTC,cAAe,SACfC,WAAY,SACZC,eAAgB,QAClB,EACAC,KAAM,CACJC,WAAY,MACd,EACAC,GAAI,CACFN,QAAS,eACTO,OAAQ,aACRC,aAAc,GACdC,SAAU,GACVC,WAAY,IACZC,cAAe,KACjB,EACAC,GAAI,CACFH,SAAU,GACVC,WAAY,IACZL,WAAY,MACd,EACAQ,KAAM,CACJb,QAAS,cACX,CACF,CAKe,OAAMf,UAAsB1B,EAAAA,OAAK,CAACuD,SAAS,CAMxDC,QAAS,CACP,GAAM,YAAExB,CAAU,cAAEyB,GAAe,CAAI,CAAE,CAAG,IAAI,CAAChD,KAAK,CAChDiD,EACJ,IAAI,CAACjD,KAAK,CAACiD,KAAK,EAChB/B,CAAW,CAACK,EAAW,EACvB,mCAEF,MACE,WAAC2B,MAAAA,CAAIC,MAAOxB,EAAOC,KAAK,WACtB,UAACwB,EAAAA,OAAI,WACH,UAACH,QAAAA,UACE1B,EACMA,EAAW,KAAI0B,EAClB,8DAGR,WAACC,MAAAA,CAAIC,MAAOxB,EAAOS,IAAI,WACrB,UAACe,QAAAA,CACCE,wBAAyB,CAkBvBC,OAAS,kGACPN,CAAAA,CACI,kIACA,GAER,CAFS,GAKVzB,EACC,UAACe,CADFf,IACEe,CAAGiB,MADLhC,IACe,gBAAgB4B,MAAOxB,EAAOW,EAAE,UAC3Cf,IAED,KACJ,UAAC2B,MAAAA,CAAIC,MAAOxB,EAAOkB,IAAI,UACrB,WAACD,KAAAA,CAAGO,MAAOxB,EAAOiB,EAAE,WACjB,IAAI,CAAC5C,KAAK,CAACiD,KAAK,EAAI1B,EACnB0B,EAEA,iBAFAA,IAEA,YAAE,0DACwD,KACvDtC,CAAQ,IAAI,CAACX,KAAK,CAACwB,QAAQ,EAC1B,cAD0B,OAC1B,YAAE,iBAAe,IAAI,CAACxB,KAAK,CAACwB,QAAQ,IACnC,IAAI,oDAGT,cAOd,CACF,CA3EqBP,EACZuC,WAAAA,CAAc,YADFvC,EAGZwC,eAAAA,CAAkBtC,EAHNF,EAIZyC,mBAAAA,CAAsBvC,gYCmH/B,OAAmB,mBAAnB,GA1LgBwC,WAAW,mBAAXA,kDAX4B,gBACzB,YACa,WACG,WACP,MAOrB,SAASA,EAAYC,CAAiB,EAAjBA,KAAAA,IAAAA,IAAAA,GAAY,GACtC,IAAMC,EAAO,CAAC,UAACC,OAAAA,CAAKC,QAAQ,SAAY,WAAa,CAMrD,OALI,GACFF,EAAKG,IAAI,CACP,CAFY,EAEZ,OAACF,OAAAA,CAAKG,KAAK,WAAWC,QAAQ,sBAAyB,aAGpDL,CACT,CAEA,SAASM,EACPC,CAAoC,CACpCC,CAA2C,QAG3C,UAAI,OAAOA,GAAuC,UAAjB,OAAOA,EAC/BD,EAGLC,EAAMC,IAAI,GAAK/E,EAAAA,OAAK,CAACgF,QAAQ,CACxBH,CAD0B,CACrBI,MAAM,CAChB,EACAjF,OAAK,CAACe,QAAQ,CAACC,OAAO,CAAC8D,EAAMrE,KAAK,CAACc,QAAQ,EAAE2D,MAAM,CACjD,CAEEC,EACAC,IAEA,UACE,OAAOA,GACkB,UAAzB,OAAOA,EAEAD,EAEFA,EAAaF,MAAM,CAACG,GAE7B,EAAE,GAIDP,EAAKI,MAAM,CAACH,EACrB,GA/CyB,OAiDzB,IAAMO,EAAY,CAAC,OAAQ,YAAa,UAAW,WAAW,CAsE9D,SAASC,EACPC,CAAoD,CACpD9E,CAAQ,EAER,GAAM,CAAE4D,WAAS,CAAE,CAAG5D,EACtB,OAAO8E,EACJL,MAAM,CAACN,EAAkB,EAAE,EAC3BY,OAAO,GACPP,MAAM,CAACb,EAAYC,GAAWmB,OAAO,IACrCrE,MAAM,CAACsE,SAxEHA,EACP,IAAMC,EAAO,IAAIC,IACXC,EAAO,IAAID,IACXE,EAAY,IAAIF,IAChBG,EAAsD,CAAC,EAE7D,OAAO,IACL,IAAIC,GAAW,EACXC,GAAS,EAEb,GAAIC,EAAEC,GAAG,EAAqB,UAAjB,OAAOD,EAAEC,GAAG,EAAiBD,EAAEC,GAAG,CAACC,OAAO,CAAC,KAAO,EAAG,CAChEH,GAAS,EACT,IAAME,EAAMD,EAAEC,GAAG,CAACE,KAAK,CAACH,EAAEC,GAAG,CAACC,OAAO,CAAC,KAAO,GACzCT,EAAKW,GAAG,CAACH,GACXH,GAAW,EAEXL,EAAKpE,GAAG,CAAC4E,EAEb,CAGA,OAAQD,EAAElB,IAAI,EACZ,IAAK,QACL,IAAK,OACCa,EAAKS,GAAG,CAACJ,EAAElB,IAAI,EACjBgB,CADoB,EACT,EAEXH,EAAKtE,GAAG,CAAC2E,EAAElB,IAAI,EAEjB,KACF,KAAK,OACH,IAAK,IAAIuB,EAAI,EAAGC,EAAMlB,EAAUmB,MAAM,CAAEF,EAAIC,EAAKD,IAAK,CACpD,IAAMG,EAAWpB,CAAS,CAACiB,EAAE,CAC7B,GAAKL,CAAD,CAAGxF,KAAK,CAACiG,cAAc,CAACD,GAE5B,GAAiB,KAFsB,MAEX,CAAxBA,EACEZ,EAAUQ,GAAG,CAACI,GAChBV,GAAW,EAEXF,EAAUvE,CAHiB,EAGd,CAACmF,OAEX,CACL,IAAME,EAAWV,EAAExF,KAAK,CAACgG,EAAS,CAC5BG,EAAad,CAAc,CAACW,EAAS,EAAI,IAAId,GAC9Cc,EAAa,SAAbA,GAAuB,CAACT,CAAAA,CAAAA,CAAK,CAAMY,EAAWP,GAAG,CAACM,GACrDZ,GAAW,GAEXa,EAHgE,GAGlD,CAACD,GACfb,CAAc,CAACW,EAAS,CAAGG,EAE/B,CACF,CAEJ,CAEA,OAAOb,CACT,CACF,KAgBKP,OAAO,GACPqB,GAAG,CAAC,CAACC,EAA4BR,KAChC,IAAMJ,EAAMY,EAAEZ,GAAG,EAAII,EACrB,GACES,CAAAA,CACQC,EADY,CACT,CAACC,CAALF,oBAA0B,EACjC,CAAC1C,GAGY,QAFb,CAEEyC,EAAE/B,IAAI,EACN+B,EAAErG,KAAK,CAAC,IAAO,EAEf,CAAC,CADD,kCACqC,2BAA2B,CAACyG,IAAI,CACnE,GAASJ,EAAErG,KAAK,CAAC,IAAO,CAAC0G,OAF+D,GAErD,CAACC,IAEtC,CACA,IAAMC,EAAW,CAAE,GAAIP,EAAErG,KAAK,EAAI,CAAC,CAAC,EAOpC,OANA4G,CAAQ,CAAC,YAAY,CAAGA,EAAS,IAAO,CACxCA,CADgC,CACvB,IAAO,EAAR,IAAWC,EAGnBD,CAAQ,CAAC,uBAAuB,EAAG,EAE5BrH,EAAAA,OAAK,CAACuH,YAAY,CAACT,EAAGO,EAC/B,CAiBF,OAAOrH,EAAAA,OAAK,CAACuH,YAAY,CAACT,EAAG,KAAEZ,CAAI,EACrC,EACJ,KAoBA,EAdA,SAASrC,CAAgD,EAA3C,IAAEtC,UAAQ,CAAiC,CAA3C,EACNiG,EAAWC,CAAAA,EAAAA,EAAAA,UAAAA,EAAW1H,EAAAA,eAAe,EACrCW,EAAc+G,GAAAA,EAAAA,UAAAA,EAAWC,EAAAA,kBAAkB,EACjD,MACE,UAACC,EAAAA,OAAM,EACLhH,wBAAyB2E,EACzB5E,YAAaA,EACb2D,UAAW1E,CAAAA,EAAAA,EAAAA,WAAAA,EAAY6H,YAEtBjG,GAGP", "sources": ["webpack://_N_E/../../src/shared/lib/amp-mode.ts", "webpack://_N_E/../../src/shared/lib/amp-context.shared-runtime.ts", "webpack://_N_E/../../src/shared/lib/utils/warn-once.ts", "webpack://_N_E/../../src/shared/lib/side-effect.tsx", "webpack://_N_E/../../src/pages/_error.tsx", "webpack://_N_E/../../src/shared/lib/head.tsx"], "sourcesContent": ["export function isInAmpMode({\n  ampFirst = false,\n  hybrid = false,\n  hasQuery = false,\n} = {}): boolean {\n  return ampFirst || (hybrid && hasQuery)\n}\n", "import React from 'react'\n\nexport const AmpStateContext: React.Context<any> = React.createContext({})\n\nif (process.env.NODE_ENV !== 'production') {\n  AmpStateContext.displayName = 'AmpStateContext'\n}\n", "let warnOnce = (_: string) => {}\nif (process.env.NODE_ENV !== 'production') {\n  const warnings = new Set<string>()\n  warnOnce = (msg: string) => {\n    if (!warnings.has(msg)) {\n      console.warn(msg)\n    }\n    warnings.add(msg)\n  }\n}\n\nexport { warnOnce }\n", "import type React from 'react'\nimport { Children, useEffect, useLayoutEffect, type JSX } from 'react'\n\ntype State = JSX.Element[] | undefined\n\nexport type SideEffectProps = {\n  reduceComponentsToState: <T extends {}>(\n    components: Array<React.ReactElement<any>>,\n    props: T\n  ) => State\n  handleStateChange?: (state: State) => void\n  headManager: any\n  inAmpMode?: boolean\n  children: React.ReactNode\n}\n\nconst isServer = typeof window === 'undefined'\nconst useClientOnlyLayoutEffect = isServer ? () => {} : useLayoutEffect\nconst useClientOnlyEffect = isServer ? () => {} : useEffect\n\nexport default function SideEffect(props: SideEffectProps) {\n  const { headManager, reduceComponentsToState } = props\n\n  function emitChange() {\n    if (headManager && headManager.mountedInstances) {\n      const headElements = Children.toArray(\n        Array.from(headManager.mountedInstances as Set<React.ReactNode>).filter(\n          Boolean\n        )\n      ) as React.ReactElement[]\n      headManager.updateHead(reduceComponentsToState(headElements, props))\n    }\n  }\n\n  if (isServer) {\n    headManager?.mountedInstances?.add(props.children)\n    emitChange()\n  }\n\n  useClientOnlyLayoutEffect(() => {\n    headManager?.mountedInstances?.add(props.children)\n    return () => {\n      headManager?.mountedInstances?.delete(props.children)\n    }\n  })\n\n  // We need to call `updateHead` method whenever the `SideEffect` is trigger in all\n  // life-cycles: mount, update, unmount. However, if there are multiple `SideEffect`s\n  // being rendered, we only trigger the method from the last one.\n  // This is ensured by keeping the last unflushed `updateHead` in the `_pendingUpdate`\n  // singleton in the layout effect pass, and actually trigger it in the effect pass.\n  useClientOnlyLayoutEffect(() => {\n    if (headManager) {\n      headManager._pendingUpdate = emitChange\n    }\n    return () => {\n      if (headManager) {\n        headManager._pendingUpdate = emitChange\n      }\n    }\n  })\n\n  useClientOnlyEffect(() => {\n    if (headManager && headManager._pendingUpdate) {\n      headManager._pendingUpdate()\n      headManager._pendingUpdate = null\n    }\n    return () => {\n      if (headManager && headManager._pendingUpdate) {\n        headManager._pendingUpdate()\n        headManager._pendingUpdate = null\n      }\n    }\n  })\n\n  return null\n}\n", "import React from 'react'\nimport Head from '../shared/lib/head'\nimport type { NextPageContext } from '../shared/lib/utils'\n\nconst statusCodes: { [code: number]: string } = {\n  400: 'Bad Request',\n  404: 'This page could not be found',\n  405: 'Method Not Allowed',\n  500: 'Internal Server Error',\n}\n\nexport type ErrorProps = {\n  statusCode: number\n  hostname?: string\n  title?: string\n  withDarkMode?: boolean\n}\n\nfunction _getInitialProps({\n  req,\n  res,\n  err,\n}: NextPageContext): Promise<ErrorProps> | ErrorProps {\n  const statusCode =\n    res && res.statusCode ? res.statusCode : err ? err.statusCode! : 404\n\n  let hostname\n\n  if (typeof window !== 'undefined') {\n    hostname = window.location.hostname\n  } else if (req) {\n    const { getRequestMeta } =\n      require('../server/request-meta') as typeof import('../server/request-meta')\n\n    const initUrl = getRequestMeta(req, 'initURL')\n    if (initUrl) {\n      const url = new URL(initUrl)\n      hostname = url.hostname\n    }\n  }\n\n  return { statusCode, hostname }\n}\n\nconst styles: Record<string, React.CSSProperties> = {\n  error: {\n    // https://github.com/sindresorhus/modern-normalize/blob/main/modern-normalize.css#L38-L52\n    fontFamily:\n      'system-ui,\"Segoe UI\",Roboto,Helvetica,Arial,sans-serif,\"Apple Color Emoji\",\"Segoe UI Emoji\"',\n    height: '100vh',\n    textAlign: 'center',\n    display: 'flex',\n    flexDirection: 'column',\n    alignItems: 'center',\n    justifyContent: 'center',\n  },\n  desc: {\n    lineHeight: '48px',\n  },\n  h1: {\n    display: 'inline-block',\n    margin: '0 20px 0 0',\n    paddingRight: 23,\n    fontSize: 24,\n    fontWeight: 500,\n    verticalAlign: 'top',\n  },\n  h2: {\n    fontSize: 14,\n    fontWeight: 400,\n    lineHeight: '28px',\n  },\n  wrap: {\n    display: 'inline-block',\n  },\n}\n\n/**\n * `Error` component used for handling errors.\n */\nexport default class Error<P = {}> extends React.Component<P & ErrorProps> {\n  static displayName = 'ErrorPage'\n\n  static getInitialProps = _getInitialProps\n  static origGetInitialProps = _getInitialProps\n\n  render() {\n    const { statusCode, withDarkMode = true } = this.props\n    const title =\n      this.props.title ||\n      statusCodes[statusCode] ||\n      'An unexpected error has occurred'\n\n    return (\n      <div style={styles.error}>\n        <Head>\n          <title>\n            {statusCode\n              ? `${statusCode}: ${title}`\n              : 'Application error: a client-side exception has occurred'}\n          </title>\n        </Head>\n        <div style={styles.desc}>\n          <style\n            dangerouslySetInnerHTML={{\n              /* CSS minified from\n                body { margin: 0; color: #000; background: #fff; }\n                .next-error-h1 {\n                  border-right: 1px solid rgba(0, 0, 0, .3);\n                }\n\n                ${\n                  withDarkMode\n                    ? `@media (prefers-color-scheme: dark) {\n                  body { color: #fff; background: #000; }\n                  .next-error-h1 {\n                    border-right: 1px solid rgba(255, 255, 255, .3);\n                  }\n                }`\n                    : ''\n                }\n               */\n              __html: `body{color:#000;background:#fff;margin:0}.next-error-h1{border-right:1px solid rgba(0,0,0,.3)}${\n                withDarkMode\n                  ? '@media (prefers-color-scheme:dark){body{color:#fff;background:#000}.next-error-h1{border-right:1px solid rgba(255,255,255,.3)}}'\n                  : ''\n              }`,\n            }}\n          />\n\n          {statusCode ? (\n            <h1 className=\"next-error-h1\" style={styles.h1}>\n              {statusCode}\n            </h1>\n          ) : null}\n          <div style={styles.wrap}>\n            <h2 style={styles.h2}>\n              {this.props.title || statusCode ? (\n                title\n              ) : (\n                <>\n                  Application error: a client-side exception has occurred{' '}\n                  {Boolean(this.props.hostname) && (\n                    <>while loading {this.props.hostname}</>\n                  )}{' '}\n                  (see the browser console for more information)\n                </>\n              )}\n              .\n            </h2>\n          </div>\n        </div>\n      </div>\n    )\n  }\n}\n", "'use client'\n\nimport React, { useContext, type JSX } from 'react'\nimport Effect from './side-effect'\nimport { AmpStateContext } from './amp-context.shared-runtime'\nimport { HeadManagerContext } from './head-manager-context.shared-runtime'\nimport { isInAmpMode } from './amp-mode'\nimport { warnOnce } from './utils/warn-once'\n\ntype WithInAmpMode = {\n  inAmpMode?: boolean\n}\n\nexport function defaultHead(inAmpMode = false): JSX.Element[] {\n  const head = [<meta charSet=\"utf-8\" key=\"charset\" />]\n  if (!inAmpMode) {\n    head.push(\n      <meta name=\"viewport\" content=\"width=device-width\" key=\"viewport\" />\n    )\n  }\n  return head\n}\n\nfunction onlyReactElement(\n  list: Array<React.ReactElement<any>>,\n  child: React.ReactElement | number | string\n): Array<React.ReactElement<any>> {\n  // React children can be \"string\" or \"number\" in this case we ignore them for backwards compat\n  if (typeof child === 'string' || typeof child === 'number') {\n    return list\n  }\n  // Adds support for React.Fragment\n  if (child.type === React.Fragment) {\n    return list.concat(\n      // @ts-expect-error @types/react does not remove fragments but this could also return ReactPortal[]\n      React.Children.toArray(child.props.children).reduce(\n        // @ts-expect-error @types/react does not remove fragments but this could also return ReactPortal[]\n        (\n          fragmentList: Array<React.ReactElement<any>>,\n          fragmentChild: React.ReactElement | number | string\n        ): Array<React.ReactElement<any>> => {\n          if (\n            typeof fragmentChild === 'string' ||\n            typeof fragmentChild === 'number'\n          ) {\n            return fragmentList\n          }\n          return fragmentList.concat(fragmentChild)\n        },\n        []\n      )\n    )\n  }\n  return list.concat(child)\n}\n\nconst METATYPES = ['name', 'httpEquiv', 'charSet', 'itemProp']\n\n/*\n returns a function for filtering head child elements\n which shouldn't be duplicated, like <title/>\n Also adds support for deduplicated `key` properties\n*/\nfunction unique() {\n  const keys = new Set()\n  const tags = new Set()\n  const metaTypes = new Set()\n  const metaCategories: { [metatype: string]: Set<string> } = {}\n\n  return (h: React.ReactElement<any>) => {\n    let isUnique = true\n    let hasKey = false\n\n    if (h.key && typeof h.key !== 'number' && h.key.indexOf('$') > 0) {\n      hasKey = true\n      const key = h.key.slice(h.key.indexOf('$') + 1)\n      if (keys.has(key)) {\n        isUnique = false\n      } else {\n        keys.add(key)\n      }\n    }\n\n    // eslint-disable-next-line default-case\n    switch (h.type) {\n      case 'title':\n      case 'base':\n        if (tags.has(h.type)) {\n          isUnique = false\n        } else {\n          tags.add(h.type)\n        }\n        break\n      case 'meta':\n        for (let i = 0, len = METATYPES.length; i < len; i++) {\n          const metatype = METATYPES[i]\n          if (!h.props.hasOwnProperty(metatype)) continue\n\n          if (metatype === 'charSet') {\n            if (metaTypes.has(metatype)) {\n              isUnique = false\n            } else {\n              metaTypes.add(metatype)\n            }\n          } else {\n            const category = h.props[metatype]\n            const categories = metaCategories[metatype] || new Set()\n            if ((metatype !== 'name' || !hasKey) && categories.has(category)) {\n              isUnique = false\n            } else {\n              categories.add(category)\n              metaCategories[metatype] = categories\n            }\n          }\n        }\n        break\n    }\n\n    return isUnique\n  }\n}\n\n/**\n *\n * @param headChildrenElements List of children of <Head>\n */\nfunction reduceComponents<T extends {} & WithInAmpMode>(\n  headChildrenElements: Array<React.ReactElement<any>>,\n  props: T\n) {\n  const { inAmpMode } = props\n  return headChildrenElements\n    .reduce(onlyReactElement, [])\n    .reverse()\n    .concat(defaultHead(inAmpMode).reverse())\n    .filter(unique())\n    .reverse()\n    .map((c: React.ReactElement<any>, i: number) => {\n      const key = c.key || i\n      if (\n        process.env.NODE_ENV !== 'development' &&\n        process.env.__NEXT_OPTIMIZE_FONTS &&\n        !inAmpMode\n      ) {\n        if (\n          c.type === 'link' &&\n          c.props['href'] &&\n          // TODO(prateekbh@): Replace this with const from `constants` when the tree shaking works.\n          ['https://fonts.googleapis.com/css', 'https://use.typekit.net/'].some(\n            (url) => c.props['href'].startsWith(url)\n          )\n        ) {\n          const newProps = { ...(c.props || {}) }\n          newProps['data-href'] = newProps['href']\n          newProps['href'] = undefined\n\n          // Add this attribute to make it easy to identify optimized tags\n          newProps['data-optimized-fonts'] = true\n\n          return React.cloneElement(c, newProps)\n        }\n      }\n      if (process.env.NODE_ENV === 'development') {\n        // omit JSON-LD structured data snippets from the warning\n        if (c.type === 'script' && c.props['type'] !== 'application/ld+json') {\n          const srcMessage = c.props['src']\n            ? `<script> tag with src=\"${c.props['src']}\"`\n            : `inline <script>`\n          warnOnce(\n            `Do not add <script> tags using next/head (see ${srcMessage}). Use next/script instead. \\nSee more info here: https://nextjs.org/docs/messages/no-script-tags-in-head-component`\n          )\n        } else if (c.type === 'link' && c.props['rel'] === 'stylesheet') {\n          warnOnce(\n            `Do not add stylesheets using next/head (see <link rel=\"stylesheet\"> tag with href=\"${c.props['href']}\"). Use Document instead. \\nSee more info here: https://nextjs.org/docs/messages/no-stylesheets-in-head-component`\n          )\n        }\n      }\n      return React.cloneElement(c, { key })\n    })\n}\n\n/**\n * This component injects elements to `<head>` of your page.\n * To avoid duplicated `tags` in `<head>` you can use the `key` property, which will make sure every tag is only rendered once.\n */\nfunction Head({ children }: { children: React.ReactNode }) {\n  const ampState = useContext(AmpStateContext)\n  const headManager = useContext(HeadManagerContext)\n  return (\n    <Effect\n      reduceComponentsToState={reduceComponents}\n      headManager={headManager}\n      inAmpMode={isInAmpMode(ampState)}\n    >\n      {children}\n    </Effect>\n  )\n}\n\nexport default Head\n"], "names": ["isInAmpMode", "ampFirs<PERSON>", "hybrid", "<PERSON><PERSON><PERSON><PERSON>", "AmpStateContext", "React", "createContext", "warnOnce", "SideEffect", "useClientOnlyLayoutEffect", "useLayoutEffect", "useClientOnlyEffect", "useEffect", "isServer", "props", "headManager", "reduceComponentsToState", "emitChange", "mountedInstances", "headElements", "Children", "toArray", "Array", "from", "filter", "Boolean", "updateHead", "add", "children", "delete", "_pendingUpdate", "Error", "statusCodes", "_getInitialProps", "req", "res", "err", "statusCode", "hostname", "window", "location", "styles", "error", "fontFamily", "height", "textAlign", "display", "flexDirection", "alignItems", "justifyContent", "desc", "lineHeight", "h1", "margin", "paddingRight", "fontSize", "fontWeight", "verticalAlign", "h2", "wrap", "Component", "render", "withDarkMode", "title", "div", "style", "Head", "dangerouslySetInnerHTML", "__html", "className", "displayName", "getInitialProps", "origGetInitialProps", "defaultHead", "inAmpMode", "head", "meta", "charSet", "push", "name", "content", "onlyReactElement", "list", "child", "type", "Fragment", "concat", "reduce", "fragmentList", "fragmentChild", "METATYPES", "reduceComponents", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "reverse", "unique", "keys", "Set", "tags", "metaTypes", "metaCategories", "isUnique", "<PERSON><PERSON><PERSON>", "h", "key", "indexOf", "slice", "has", "i", "len", "length", "metatype", "hasOwnProperty", "category", "categories", "map", "c", "process", "env", "__NEXT_OPTIMIZE_FONTS", "some", "startsWith", "url", "newProps", "undefined", "cloneElement", "ampState", "useContext", "HeadManagerContext", "Effect"], "sourceRoot": "", "ignoreList": []}