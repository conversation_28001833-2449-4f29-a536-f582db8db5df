{"version": 3, "file": "static/chunks/pages/adminsettings/hazard/hazardReactDropZone-34c848e5a0c49809.js", "mappings": "gFACA,4CACA,4CACA,WACA,OAAe,EAAQ,KAAiE,CACxF,EACA,SAFsB,0JCatB,IAAIA,EAAY,EAAE,CAEZC,EAAaC,UAAwB,CAErCC,EAAiB,CACrBC,KAAM,EACNC,QAAS,OACTC,cAAe,SACfC,WAAY,SACZC,eAAgB,SAChBC,MAAO,OACPC,OAAQ,OACRC,YAAa,GACbC,YAAa,UACbC,gBAAiB,UACjBC,MAAO,QACPC,WAAY,0BACd,EAEMC,EAAa,CACjBX,QAAS,cACTY,aAAc,EACdC,OAAQ,iBACRC,aAAc,EACdC,YAAa,GACbX,MAAO,IACPC,OAAQ,IACRW,QAAS,EACTC,SAAU,WACVC,UAAW,mCACXC,UAAW,YACb,EAEMC,EAAuB,CAC3BpB,QAAS,OACTC,cAAe,MACfE,eAAgB,aAChBkB,SAAU,OACVC,UAAW,EACb,EAEMC,EAAkB,CACtBvB,QAAS,OACTwB,SAAU,EACVC,SAAU,QACZ,EAEMC,EAAY,CAChBT,SAAU,WACVU,SAAU,OACVC,IAAK,QACLC,MAAO,QACPC,OAAQ,IACRC,OAAQ,UACRvB,gBAAiB,OACjBC,MAAO,OACPG,aAAc,KAChB,EAEMoB,EAAM,CACVhC,QAAS,QACTI,MAAO,OACPC,OAAQ,MACV,EAEM4B,EAAmB,CACvB1B,YAAa,SACf,EA0IA,EAxI4B,IAC1B,GAAM,GAAE2B,CAAC,CAAE,CAAGC,CAAAA,EAAAA,EAAAA,EAAAA,CAAcA,CAAC,MAuIhBC,IAtIP,CAACC,EAAOC,EAAS,CAAGC,CAAAA,EAAAA,EAAAA,IAsIMH,EAAC,EAtIPG,CAAQA,CAAQ,EAAE,EAGtCC,EAAc,MAAOC,IACZ,MAAMC,EAAAA,CAAUA,CAACC,MAAM,CAAC,UAAa,OAAHF,GACjD,EAEMG,EAAa,IACjB,IAAMC,EAAMC,GAAQA,EAAKC,GAAG,CAAG,CAAEC,SAAUF,EAAKC,GAAG,EAAK,CAAED,KAAMA,CAAK,EAC/DG,EAASC,IAAAA,SAAW,CAACvD,EAAMkD,GACjCL,EAAY7C,CAAI,CAACsD,EAAO,CAACD,QAAQ,EACjCrD,EAAKwD,MAAM,CAACF,EAAQ,GACpBG,EAAMC,QAAQ,CAAC1D,EAAMyD,EAAME,KAAK,CAAGF,EAAME,KAAK,CAAG,GACjD,IAAMC,EAAgB,IAAIlB,EAAM,CAChCkB,EAASJ,MAAM,CAACI,EAASC,OAAO,CAACV,GAAO,GACxCR,EAASiB,EAEX,EAEME,EAAe,GACX,UAACzB,MAAAA,CAAI0B,IAAKZ,EAAKa,OAAO,CAAEC,MAAO5B,IAGnC6B,EAAcxB,EAAMyB,GAAG,CAAC,CAAChB,EAAWiB,IAEtC,UAACC,MAAAA,UACC,WAACA,MAAAA,CAAIJ,MAAOjD,YACV,UAACqD,MAAAA,CAAIJ,MAAOrC,WACTkC,EAAaX,KAGhB,UAACmB,EAAAA,CAAeA,CAAAA,CACdvC,KAAMwC,EAAAA,GAAaA,CACnBN,MAAOlC,EACPjB,MAAM,QACN0D,QAAS,IAAMvB,EAAWE,SAVtBiB,IAiBdK,CAAAA,EAAAA,EAAAA,SAAAA,CAASA,CAAC,KACR/B,EAAMgC,OAAO,CAAEvB,GAAcwB,IAAIC,eAAe,CAACzB,EAAKa,OAAO,GAC7DhE,EAAO,EAAE,EACR,EAAE,EAELyE,CAAAA,EAAAA,EAAAA,SAAAA,CAASA,CAAC,KAEJhB,GAASA,EAAMoB,KAAK,EAAE,EAOf,IANMpB,EAAMoB,KAAK,CAACV,GAAG,CAAC,CAACW,EAAWC,KAEzC/E,EAAKgF,IAAI,CAAC,CAAE3B,SAAUyB,EAAK1B,GAAG,CAAEO,MAAOF,EAAME,KAAK,CAAGF,EAAME,KAAK,CAAG,CAAE,GAChD,CAAE,GAAGmB,CAAI,CAAEd,QAAS,GAAwCc,MAAAA,CAArC5E,8BAAsB,CAAC,gBAAuB,OAAT4E,EAAK1B,GAAG,CAAG,IAG1E,CAExB,EAAG,CAACK,EAAMoB,KAAK,CAAC,EAEhB,IAAMI,EAAc,MAAOC,EAAqBvB,KAE9C,GAAIuB,EAAeC,MAAM,CAAGxB,EAC1B,GAAI,CACF,CAF+B,GAEzByB,EAAY,IAAIC,SACtBD,EAAKE,MAAM,CAAC,OAAQJ,CAAc,CAACvB,EAAM,EACzC,IAAM4B,EAAM,MAAMxC,EAAAA,CAAUA,CAACyC,IAAI,CAAE,SAASJ,EAAM,CAAE,eAAgB,qBAAsB,GAC1FpF,EAAKgF,IAAI,CAAC,CAAE3B,SAAUkC,EAAInC,GAAG,CAAED,KAAM+B,CAAc,CAACvB,EAAM,CAAEA,MAAOF,EAAME,KAAK,CAAGF,EAAME,KAAK,CAAG,CAAE,GACjGsB,EAAYC,EAAgBvB,EAAQ,EACtC,CAAE,MAAO8B,EAAO,CACdR,EAAYC,EAAgBvB,EAAQ,EACtC,MAEAF,EAAMC,QAAQ,CAAC1D,EAAMyD,EAAME,KAAK,CAAGF,EAAME,KAAK,CAAG,EAErD,EAEM+B,EAASC,CAAAA,EAAAA,EAAAA,WAAAA,CAAWA,CAAC,IACzBV,EAAYW,EAAY,GAMxBjD,EAAS,IALQiD,EAAWzB,GAAG,CAAC,CAAChB,EAAWiB,IAC1CyB,OAAOC,MAAM,CAAC3C,EAAM,CAClBa,QAASW,IAAIoB,eAAe,CAAC5C,EAC/B,IAEoB,CACxB,EAAG,EAAE,EAEC,cACJ6C,CAAY,eACZC,CAAa,cACbC,CAAY,cACZC,CAAY,cACZC,CAAY,gBACZC,CAAc,CACf,CAAGC,CAAAA,EAAAA,EAAAA,EAAAA,CAAWA,CAAC,CACdC,OAAQ,UACRC,UAAU,EACVC,QAAS,EACTC,QAASzG,SACTyF,CACF,GAEMzB,EAAQ0C,CAAAA,EAAAA,EAAAA,OAAAA,CAAOA,CACnB,IAAO,EACL,GAAGxG,CAAS,CACZ,GAAI+F,EAAe5D,EAAc,CAAEsE,QAAS,iBAAkB,CAAC,CAC/D,GAAIT,EACA,CAAES,QAAS,oBAAqB,EAChC,CAAEA,QAAS,iBAAkB,CAAC,CAClC,GAAIR,EAAe,CAAEQ,QAAS,gBAAiB,EAAI,aAACtE,CAAW,CAAC,GAElE,CAAC4D,EAAcE,EAAa,EAGxBS,EAAiBR,EAAelB,MAAM,CAAG,GAAKkB,CAAc,CAAC,EAAE,CAAClD,IAAI,CAAC2D,IAAI,CAAG7G,EAClF,MACE,iCACE,UAACoE,MAAAA,CAAI0C,UAAU,yDAAyD9C,MAAO,CAAExD,MAAO,OAAQC,OAAQ,OAAQ,WAC9G,WAAC2D,MAAAA,CAAK,GAAG2B,EAAa,OAAE/B,CAAM,EAAE,WAC9B,UAAC+C,QAAAA,CAAO,GAAGf,GAAe,GAC1B,UAAC3B,EAAAA,CAAeA,CAAAA,CAACvC,KAAMkF,EAAAA,GAAgBA,CAAEH,KAAK,KAAKhG,MAAM,SACzD,UAACoG,IAAAA,CAAEjD,MAAO,CAAEnD,MAAO,UAAWK,aAAc,KAAM,WAAIoB,EAAE,mDACxD,UAAC4E,QAAAA,CAAMlD,MAAO,CAAEnD,MAAO,SAAU,WAAIyB,EAAE,oBACvC,WAAC4E,QAAAA,CAAMlD,MAAO,CAAEnD,MAAO,SAAU,YAAG,UAACsG,IAAAA,UAAG7E,EAAE,WAAa,IAAEA,EAAE,mCAE1DsE,GAAkB,WAACM,QAAAA,CAAMJ,UAAU,6BAAmB,IAAC,UAACzC,EAAAA,CAAeA,CAAAA,CAACvC,KAAMsF,EAAAA,GAAmBA,CAAEP,KAAK,KAAKhG,MAAM,QAAQ,IAAEyB,EAAE,2CAC/H6D,GAAgB,WAACe,QAAAA,CAAMJ,UAAU,cAAc9C,MAAO,CAAEnD,MAAO,SAAU,YAAG,UAACwD,EAAAA,CAAeA,CAAAA,CAACvC,KAAMsF,EAAAA,GAAmBA,CAAEP,KAAK,KAAKhG,MAAM,QAAQ,IAAEyB,EAAE,mCAGzJ,UAAC8B,MAAAA,CAAIJ,MAAOxC,WAAkByC,MAGpC", "sources": ["webpack://_N_E/?5686", "webpack://_N_E/./pages/adminsettings/hazard/hazardReactDropZone.tsx"], "sourcesContent": ["\n    (window.__NEXT_P = window.__NEXT_P || []).push([\n      \"/adminsettings/hazard/hazardReactDropZone\",\n      function () {\n        return require(\"private-next-pages/adminsettings/hazard/hazardReactDropZone.tsx\");\n      }\n    ]);\n    if(module.hot) {\n      module.hot.dispose(function () {\n        window.__NEXT_P.push([\"/adminsettings/hazard/hazardReactDropZone\"])\n      });\n    }\n  ", "//Import Library\r\nimport React, { useMemo, useEffect, useState, useCallback } from \"react\";\r\nimport { useDropzone } from \"react-dropzone\";\r\nimport { FontAwesomeIcon } from \"@fortawesome/react-fontawesome\";\r\nimport {\r\n  faTimesCircle,\r\n  faExclamationCircle,\r\n  faCloudUploadAlt,\r\n\r\n} from \"@fortawesome/free-solid-svg-icons\";\r\nimport _ from 'lodash';\r\n\r\n//Import services/components\r\nimport apiService from \"../../../services/apiService\";\r\nimport { useTranslation } from 'next-i18next';\r\n\r\n\r\nlet temp: any = [];\r\n\r\nconst limit: any = process.env.UPLOAD_LIMIT;\r\n\r\nconst baseStyle: any = {\r\n  flex: 1,\r\n  display: \"flex\",\r\n  flexDirection: \"column\",\r\n  alignItems: \"center\",\r\n  justifyContent: \"center\",\r\n  width: \"100%\",\r\n  height: \"100%\",\r\n  borderWidth: 0.1,\r\n  borderColor: \"#fafafa\",\r\n  backgroundColor: \"#fafafa\",\r\n  color: \"black\",\r\n  transition: \"border  .24s ease-in-out\",\r\n};\r\n\r\nconst thumb: any = {\r\n  display: \"inline-flex\",\r\n  borderRadius: 2,\r\n  border: \"1px solid #ddd\",\r\n  marginBottom: 8,\r\n  marginRight: 20,\r\n  width: 100,\r\n  height: 100,\r\n  padding: 2,\r\n  position: \"relative\",\r\n  boxShadow: \"0 0 15px 0.25px rgba(0,0,0,0.25)\",\r\n  boxSizing: \"border-box\"\r\n};\r\n\r\nconst thumbsContainer: any = {\r\n  display: \"flex\",\r\n  flexDirection: \"row\",\r\n  justifyContent: \"flex-start\",\r\n  flexWrap: \"wrap\",\r\n  marginTop: 20,\r\n};\r\n\r\nconst thumbInner: any = {\r\n  display: \"flex\",\r\n  minWidth: 0,\r\n  overflow: \"hidden\",\r\n};\r\n\r\nconst icon: any = {\r\n  position: \"absolute\",\r\n  fontSize: \"22px\",\r\n  top: \"-10px\",\r\n  right: \"-10px\",\r\n  zIndex: 1000,\r\n  cursor: \"pointer\",\r\n  backgroundColor: \"#fff\",\r\n  color: \"#000\",\r\n  borderRadius: \"50%\"\r\n};\r\n\r\nconst img = {\r\n  display: \"block\",\r\n  width: \"auto\",\r\n  height: \"100%\",\r\n};\r\n\r\nconst activeStyle: any = {\r\n  borderColor: \"#2196f3\",\r\n};\r\n\r\nconst HazardReactDropZone = (props: any) => {\r\n  const { t } = useTranslation('common');\r\n  const [files, setFiles] = useState<any[]>([]);\r\n\r\n\r\n  const imageDelete = async (id: any) => {\r\n    const _res = await apiService.remove(`/image/${id}`);\r\n  }\r\n\r\n  const removeFile = (file: any) => {\r\n    const obj = file && file._id ? { serverID: file._id } : { file: file }\r\n    const _index = _.findIndex(temp, obj);\r\n    imageDelete(temp[_index].serverID)\r\n    temp.splice(_index, 1);\r\n    props.getImgID(temp, props.index ? props.index : 0)\r\n    const newFiles: any = [...files];\r\n    newFiles.splice(newFiles.indexOf(file), 1);\r\n    setFiles(newFiles);\r\n\r\n  };\r\n\r\n  const getComponent = (file: any) => {\r\n    return (<img src={file.preview} style={img} />);\r\n  }\r\n\r\n  const thumbs: any = files.map((file: any, i) => {\r\n    return (\r\n      <div key={i}>\r\n        <div style={thumb}>\r\n          <div style={thumbInner}>\r\n            {getComponent(file)}\r\n          </div>\r\n\r\n          <FontAwesomeIcon\r\n            icon={faTimesCircle}\r\n            style={icon}\r\n            color=\"black\"\r\n            onClick={() => removeFile(file)}\r\n          />\r\n        </div>\r\n      </div>\r\n    );\r\n  });\r\n\r\n  useEffect(() => {\r\n    files.forEach((file: any) => URL.revokeObjectURL(file.preview));\r\n    temp = [];\r\n  }, []);\r\n\r\n  useEffect(() => {\r\n\r\n    if (props && props.datas) {\r\n      const newObj = props.datas.map((item: any, _i: any) => {\r\n\r\n        temp.push({ serverID: item._id, index: props.index ? props.index : 0 });\r\n        const previewState = { ...item, preview: `${process.env.API_SERVER}/image/show/${item._id}` }\r\n        return previewState;\r\n      });\r\n      setFiles([...newObj])\r\n    }\r\n  }, [props.datas]);\r\n\r\n  const filesUpload = async (files_initials: any, index: any) => {\r\n\r\n    if (files_initials.length > index) {\r\n      try {\r\n        const form: any = new FormData();\r\n        form.append(\"file\", files_initials[index]);\r\n        const res = await apiService.post(`/image`, form, { 'Content-Type': 'multipart/form-data' });\r\n        temp.push({ serverID: res._id, file: files_initials[index], index: props.index ? props.index : 0 });\r\n        filesUpload(files_initials, index + 1);\r\n      } catch (error) {\r\n        filesUpload(files_initials, index + 1);\r\n      }\r\n    } else {\r\n      props.getImgID(temp, props.index ? props.index : 0);\r\n    }\r\n  }\r\n\r\n  const onDrop = useCallback((drop_files: any) => {\r\n    filesUpload(drop_files, 0);\r\n    const accFiles = drop_files.map((file: any, i: any) =>\r\n      Object.assign(file, {\r\n        preview: URL.createObjectURL(file),\r\n      }),\r\n    );\r\n    setFiles([...accFiles])\r\n  }, []);\r\n\r\n  const {\r\n    getRootProps,\r\n    getInputProps,\r\n    isDragActive,\r\n    isDragAccept,\r\n    isDragReject,\r\n    fileRejections\r\n  } = useDropzone({\r\n    accept: \"image/*\",\r\n    multiple: false,\r\n    minSize: 0,\r\n    maxSize: limit,\r\n    onDrop\r\n  })\r\n\r\n  const style = useMemo(\r\n    () => ({\r\n      ...baseStyle,\r\n      ...(isDragActive ? activeStyle : { outline: \"2px dashed #bbb\" }),\r\n      ...(isDragAccept\r\n        ? { outline: \"2px dashed #595959\" }\r\n        : { outline: \"2px dashed #bbb\" }),\r\n      ...(isDragReject ? { outline: \"2px dashed red\" } : {activeStyle}),\r\n    }),\r\n    [isDragActive, isDragReject]\r\n  );\r\n\r\n  const isFileTooLarge = fileRejections.length > 0 && fileRejections[0].file.size > limit;\r\n  return (\r\n    <>\r\n      <div className=\" d-flex justify-content-center align-items-center mt-3\" style={{ width: \"100%\", height: \"180px\" }}>\r\n        <div {...getRootProps({ style })}>\r\n          <input {...getInputProps()} />\r\n          <FontAwesomeIcon icon={faCloudUploadAlt} size=\"4x\" color=\"#999\" />\r\n          <p style={{ color: '#595959', marginBottom: \"0px\" }}>{t(\"Drag'n'dropsomefileshere,orclicktoselectfiles\")}</p>\r\n          <small style={{ color: '#595959' }}>{t(\"ImageWeSupport\")}</small>\r\n          <small style={{ color: '#595959' }}><b>{t(\"Note:\")}</b> {t(\"Onesingleimagewillbeaccepted\")}</small>\r\n\r\n          {isFileTooLarge && <small className=\"text-danger mt-2\"> <FontAwesomeIcon icon={faExclamationCircle} size=\"1x\" color=\"red\" /> {t(\"FileistoolargeItshouldbelessthan20MB\")}</small>}\r\n          {isDragReject && <small className=\"text-danger\" style={{ color: '#595959' }}><FontAwesomeIcon icon={faExclamationCircle} size=\"1x\" color=\"red\" /> {t(\"Filetypenotacceptedsorr\")}</small>}\r\n        </div>\r\n      </div>\r\n      <div style={thumbsContainer}>{thumbs}</div>\r\n    </>\r\n  );\r\n};\r\n\r\nexport default HazardReactDropZone;\r\n"], "names": ["temp", "limit", "process", "baseStyle", "flex", "display", "flexDirection", "alignItems", "justifyContent", "width", "height", "borderWidth", "borderColor", "backgroundColor", "color", "transition", "thumb", "borderRadius", "border", "marginBottom", "marginRight", "padding", "position", "boxShadow", "boxSizing", "thumbsContainer", "flexWrap", "marginTop", "thumbInner", "min<PERSON><PERSON><PERSON>", "overflow", "icon", "fontSize", "top", "right", "zIndex", "cursor", "img", "activeStyle", "t", "useTranslation", "HazardReactDropZone", "files", "setFiles", "useState", "imageDelete", "id", "apiService", "remove", "removeFile", "obj", "file", "_id", "serverID", "_index", "_", "splice", "props", "getImgID", "index", "newFiles", "indexOf", "getComponent", "src", "preview", "style", "thumbs", "map", "i", "div", "FontAwesomeIcon", "faTimesCircle", "onClick", "useEffect", "for<PERSON>ach", "URL", "revokeObjectURL", "datas", "item", "_i", "push", "filesUpload", "files_initials", "length", "form", "FormData", "append", "res", "post", "error", "onDrop", "useCallback", "drop_files", "Object", "assign", "createObjectURL", "getRootProps", "getInputProps", "isDragActive", "isDragAccept", "isDragReject", "fileRejections", "useDropzone", "accept", "multiple", "minSize", "maxSize", "useMemo", "outline", "isFileTooLarge", "size", "className", "input", "faCloudUploadAlt", "p", "small", "b", "faExclamationCircle"], "sourceRoot": "", "ignoreList": []}