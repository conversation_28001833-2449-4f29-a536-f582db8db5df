(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[3126],{66780:(e,s,i)=>{(window.__NEXT_P=window.__NEXT_P||[]).push(["/hazard/permission",function(){return i(86207)}])},86207:(e,s,i)=>{"use strict";i.r(s),i.d(s,{canViewDiscussionUpdate:()=>a,default:()=>n});let a=(0,i(8178).A)({authenticatedSelector:e=>!!e.permissions&&!!e.permissions.update&&!!e.permissions.update["read:any"],wrapperDisplayName:"CanViewDiscussionUpdate"}),n=a}},e=>{var s=s=>e(e.s=s);e.O(0,[636,6593,8792],()=>s(66780)),_N_E=e.O()}]);
//# sourceMappingURL=permission-2b47a54650cdef85.js.map