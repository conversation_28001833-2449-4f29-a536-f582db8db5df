"use strict";(()=>{var e={};e.id=6699,e.ids=[636,3220,6699],e.modules={123:e=>{e.exports=require("dom-helpers/removeEventListener")},1332:e=>{e.exports=require("react-custom-scrollbars-2")},1428:e=>{e.exports=import("axios")},1680:e=>{e.exports=require("@restart/ui/utils")},1919:e=>{e.exports=require("react-transition-group/Transition")},3892:e=>{e.exports=require("classnames")},4048:e=>{e.exports=require("react-truncate")},6009:e=>{e.exports=require("dom-helpers/ownerDocument")},6952:e=>{e.exports=require("@restart/ui/Modal")},7374:e=>{e.exports=require("@restart/ui/Dropdown")},8732:e=>{e.exports=require("react/jsx-runtime")},9653:e=>{e.exports=require("@restart/ui/Overlay")},11242:e=>{e.exports=require("redux-persist/integration/react")},11688:e=>{e.exports=require("@restart/hooks/useWillUnmount")},12388:e=>{e.exports=require("dom-helpers/addEventListener")},13364:e=>{e.exports=require("redux-saga/effects")},14062:e=>{e.exports=import("react-redux")},14078:e=>{e.exports=import("swr")},14332:e=>{e.exports=require("uncontrollable")},16116:e=>{e.exports=require("invariant")},17615:(e,r,s)=>{s.a(e,async(e,t)=>{try{s.r(r),s.d(r,{config:()=>q,default:()=>c,getServerSideProps:()=>A,getStaticPaths:()=>x,getStaticProps:()=>l,reportWebVitals:()=>h,routeModule:()=>v,unstable_getServerProps:()=>_,unstable_getServerSideProps:()=>w,unstable_getStaticParams:()=>S,unstable_getStaticPaths:()=>y,unstable_getStaticProps:()=>g});var i=s(63885),a=s(80237),o=s(81413),n=s(9616),p=s.n(n),u=s(72386),d=s(45927),m=e([u]);u=(m.then?(await m)():m)[0];let c=(0,o.M)(d,"default"),l=(0,o.M)(d,"getStaticProps"),x=(0,o.M)(d,"getStaticPaths"),A=(0,o.M)(d,"getServerSideProps"),q=(0,o.M)(d,"config"),h=(0,o.M)(d,"reportWebVitals"),g=(0,o.M)(d,"unstable_getStaticProps"),y=(0,o.M)(d,"unstable_getStaticPaths"),S=(0,o.M)(d,"unstable_getStaticParams"),_=(0,o.M)(d,"unstable_getServerProps"),w=(0,o.M)(d,"unstable_getServerSideProps"),v=new i.PagesRouteModule({definition:{kind:a.A.PAGES,page:"/adminsettings/permissions",pathname:"/adminsettings/permissions",bundlePath:"",filename:""},components:{App:u.default,Document:p()},userland:d});t()}catch(e){t(e)}})},18622:e=>{e.exports=require("yup")},19121:e=>{e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},22326:e=>{e.exports=require("react-dom")},22541:e=>{e.exports=require("redux-persist/lib/storage")},25303:e=>{e.exports=require("@restart/ui/NavItem")},26324:e=>{e.exports=require("warning")},27825:e=>{e.exports=require("lodash")},27910:e=>{e.exports=require("stream")},28217:e=>{e.exports=require("@restart/ui/DropdownItem")},29021:e=>{e.exports=require("fs")},29294:e=>{e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},29628:e=>{e.exports=require("dom-helpers/addClass")},29825:e=>{e.exports=require("prop-types")},29841:e=>{e.exports=require("dom-helpers/hasClass")},30362:e=>{e.exports=require("@restart/ui/DropdownToggle")},33873:e=>{e.exports=require("path")},36653:e=>{e.exports=require("nprogress")},36955:e=>{e.exports=require("@restart/hooks/useBreakpoint")},37766:e=>{e.exports=require("@restart/hooks/useMergedRefs")},39756:e=>{e.exports=import("redux")},40051:e=>{e.exports=require("dom-helpers/removeClass")},40361:e=>{e.exports=require("next/dist/compiled/next-server/pages.runtime.prod.js")},42893:e=>{e.exports=import("react-hot-toast")},43294:e=>{e.exports=require("formik")},45927:(e,r,s)=>{s.r(r),s.d(r,{canAddAreaOfWork:()=>o,canAddContent:()=>D,canAddCountry:()=>n,canAddDeploymentStatus:()=>p,canAddEventStatus:()=>u,canAddExpertise:()=>d,canAddFocalPointApproval:()=>m,canAddHazardTypes:()=>x,canAddHazards:()=>l,canAddLandingPage:()=>C,canAddOperationStatus:()=>g,canAddOrganisationApproval:()=>A,canAddOrganisationNetworks:()=>q,canAddOrganisationTypes:()=>h,canAddProjectStatus:()=>y,canAddRegions:()=>S,canAddRiskLevels:()=>_,canAddSyndromes:()=>w,canAddUpdateTypes:()=>v,canAddUsers:()=>P,canAddVspaceApproval:()=>c,canAddWorldRegion:()=>f,default:()=>k});var t=s(81366),i=s.n(t);let a="create:any",o=i()({authenticatedSelector:e=>!!e.permissions&&!!e.permissions.area_of_work&&!!e.permissions.area_of_work[a],wrapperDisplayName:"CanAddAreaOfWork"}),n=i()({authenticatedSelector:e=>!!e.permissions&&!!e.permissions.country&&!!e.permissions.country[a],wrapperDisplayName:"CanAddCountry"}),p=i()({authenticatedSelector:e=>!!e.permissions&&!!e.permissions.deployment_status&&!!e.permissions.deployment_status[a],wrapperDisplayName:"CanAddDeploymentStatus"}),u=i()({authenticatedSelector:e=>!!e.permissions&&!!e.permissions.event_status&&!!e.permissions.event_status[a],wrapperDisplayName:"CanAddEventStatus"}),d=i()({authenticatedSelector:e=>!!e.permissions&&!!e.permissions.expertise&&!!e.permissions.expertise[a],wrapperDisplayName:"CanAddExpertise"}),m=i()({authenticatedSelector:e=>!!e.permissions&&!!e.permissions.institution_focal_point&&!!e.permissions.institution_focal_point[a],wrapperDisplayName:"CanAddFocalPointApproval"}),c=i()({authenticatedSelector:e=>!!e.permissions&&!!e.permissions.institution_focal_point&&!!e.permissions.institution_focal_point[a],wrapperDisplayName:"CanAddVspaceApproval"}),l=i()({authenticatedSelector:e=>!!e.permissions&&!!e.permissions.hazard&&!!e.permissions.hazard[a],wrapperDisplayName:"CanAddHazards"}),x=i()({authenticatedSelector:e=>!!e.permissions&&!!e.permissions.hazard_type&&!!e.permissions.hazard_type[a],wrapperDisplayName:"CanAddHazardTypes"}),A=i()({authenticatedSelector:e=>!!e.permissions&&!!e.permissions.institution&&!!e.permissions.institution[a],wrapperDisplayName:"CanAddOrganisationApproval"}),q=i()({authenticatedSelector:e=>!!e.permissions&&!!e.permissions.institution_network&&!!e.permissions.institution_network[a],wrapperDisplayName:"CanAddOrganisationNetworks"}),h=i()({authenticatedSelector:e=>!!e.permissions&&!!e.permissions.institution_type&&!!e.permissions.institution_type[a],wrapperDisplayName:"CanAddOrganisationTypes"}),g=i()({authenticatedSelector:e=>!!e.permissions&&!!e.permissions.operation_status&&!!e.permissions.operation_status[a],wrapperDisplayName:"CanAddOperationStatus"}),y=i()({authenticatedSelector:e=>!!e.permissions&&!!e.permissions.project_status&&!!e.permissions.project_status[a],wrapperDisplayName:"CanAddProjectStatus"}),S=i()({authenticatedSelector:e=>!!e.permissions&&!!e.permissions.region&&!!e.permissions.region[a],wrapperDisplayName:"CanAddRegions"}),_=i()({authenticatedSelector:e=>!!e.permissions&&!!e.permissions.risk_level&&!!e.permissions.risk_level[a],wrapperDisplayName:"CanAddRiskLevels"}),w=i()({authenticatedSelector:e=>!!e.permissions&&!!e.permissions.syndrome&&!!e.permissions.syndrome[a],wrapperDisplayName:"CanAddSyndromes"}),v=i()({authenticatedSelector:e=>!!e.permissions&&!!e.permissions.update_type&&!!e.permissions.update_type[a],wrapperDisplayName:"CanAddUpdateTypes"}),P=i()({authenticatedSelector:e=>!!e.permissions&&!!e.permissions.users&&!!e.permissions.users[a],wrapperDisplayName:"CanAddUsers"}),f=i()({authenticatedSelector:e=>!!e.permissions&&!!e.permissions.worl_region&&!!e.permissions.worl_region[a],wrapperDisplayName:"CanAddWorldRegion"}),C=i()({authenticatedSelector:e=>!!e.permissions&&!!e.permissions.landing_page&&!!e.permissions.landing_page[a],wrapperDisplayName:"CanAddLandingPage"}),D=i()({authenticatedSelector:e=>!!e.permissions&&!!e.permissions.operation&&!!e.permissions.operation[a]&&!!e.permissions.project&&!!e.permissions.project[a]&&!!e.permissions.event&&!!e.permissions.event[a]&&!!e.permissions.vspace&&!!e.permissions.vspace[a]&&!!e.permissions.institution&&!!e.permissions.institution[a]&&!!e.permissions.update&&!!e.permissions.update[a]||!1,wrapperDisplayName:"CanAddContent"}),k=o},50009:e=>{e.exports=require("@restart/ui/DropdownMenu")},57664:e=>{e.exports=require("dom-helpers/scrollbarSize")},58928:e=>{e.exports=require("dom-helpers/canUseDOM")},59717:e=>{e.exports=require("@restart/ui/DropdownContext")},60560:e=>{e.exports=require("dom-helpers/contains")},63033:e=>{e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},65447:e=>{e.exports=require("@restart/ui/Button")},67364:e=>{e.exports=require("@restart/hooks/useCallbackRef")},68455:e=>{e.exports=import("redux-saga")},69722:e=>{e.exports=require("es6-promise")},74075:e=>{e.exports=require("zlib")},74716:e=>{e.exports=require("moment")},74987:e=>{e.exports=require("@restart/ui/ModalManager")},78097:e=>{e.exports=require("next-redux-saga")},78634:e=>{e.exports=require("@restart/ui/Anchor")},80237:(e,r)=>{Object.defineProperty(r,"A",{enumerable:!0,get:function(){return s}});var s=function(e){return e.PAGES="PAGES",e.PAGES_API="PAGES_API",e.APP_PAGE="APP_PAGE",e.APP_ROUTE="APP_ROUTE",e.IMAGE="IMAGE",e}({})},81366:e=>{e.exports=require("redux-auth-wrapper/connectedAuthWrapper")},81413:(e,r)=>{Object.defineProperty(r,"M",{enumerable:!0,get:function(){return function e(r,s){return s in r?r[s]:"then"in r&&"function"==typeof r.then?r.then(r=>e(r,s)):"function"==typeof r&&"default"===s?r:void 0}}})},81521:e=>{e.exports=require("dom-helpers/querySelectorAll")},81895:e=>{e.exports=require("@restart/hooks/useEventCallback")},82015:e=>{e.exports=require("react")},86842:e=>{e.exports=require("@restart/ui/SelectableContext")},87571:e=>{e.exports=require("dom-helpers/transitionEnd")},88751:e=>{e.exports=require("next-i18next")},93787:e=>{e.exports=require("redux-persist")},94947:e=>{e.exports=require("@restart/hooks/useTimeout")},96196:e=>{e.exports=require("next-redux-wrapper")},98320:e=>{e.exports=require("@restart/hooks/useIsomorphicEffect")},99460:e=>{e.exports=require("dom-helpers/css")}};var r=require("../../webpack-runtime.js");r.C(e);var s=e=>r(r.s=e),t=r.X(0,[6089,9216,9616,2386],()=>s(17615));module.exports=t})();