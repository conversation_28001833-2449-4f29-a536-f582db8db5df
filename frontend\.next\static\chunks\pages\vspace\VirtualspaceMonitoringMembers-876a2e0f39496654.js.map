{"version": 3, "file": "static/chunks/pages/vspace/VirtualspaceMonitoringMembers-876a2e0f39496654.js", "mappings": "gFACA,4CACA,wCACA,WACA,OAAe,EAAQ,KAA6D,CACpF,EACA,SAFsB,oGCiCtB,SAASA,EAASC,CAAoB,EACpC,GAAM,CAAEC,GAAC,CAAE,CAAGC,CAAAA,EAAAA,EAAAA,EAAAA,CAAcA,CAAC,UACvBC,EAA6B,CACjCC,gBAAiBH,EAAE,cACnB,EACI,SACJI,CAAO,MACPC,CAAI,WACJC,CAAS,uBACTC,CAAqB,WACrBC,CAAS,oBACTC,CAAkB,qBAClBC,CAAmB,kBACnBC,CAAgB,aAChBC,CAAW,oBACXC,CAAkB,gBAClBC,CAAc,SACdC,CAAO,WACPC,CAAS,sBACTC,CAAoB,mBACpBC,CAAiB,YACjBC,CAAU,CACVC,QAAM,kBACNC,CAAgB,cAChBC,CAAY,CAEZ,CADA,EACGC,EACJ,CAAGxB,EAGEyB,EAAiB,4BACrBtB,EACAuB,gBAAiBzB,EAAE,IAP0C,MAQ7D0B,UAAU,UACVtB,EACAC,KAAMA,GAAQ,EAAE,CAChBsB,MAAO,GACPC,2BAA4BrB,EAC5BsB,UAAWrB,EACXsB,gBAAiBf,qBACjBN,EACAsB,YAAY,EACZC,iBAAkBhB,EAClBiB,kBAAmBpB,GAA0C,GAC7DqB,eADwCrB,cACVD,GAA4B,CAAC,GAAI,GAAI,GAAI,CAA3BA,EAA+B,GAAG,CAC9EuB,oBAAqB7B,EACrB8B,oBAAqB1B,EACrB2B,aAAc1B,iBACdG,uBACAG,oBACAC,EACAoB,kBAAmB,UAACC,EAAAA,CAAYA,CAAAA,CAAAA,GAChCC,SAAU,UAACC,IAAAA,CAAEC,UAAU,6CACvBvB,SACAC,eACAE,mBACAD,EACAqB,UAAW,WACb,EACA,MACE,UAACC,EAAAA,EAASA,CAAAA,CAAE,GAAGnB,CAAc,EAEjC,CAEA1B,EAAS8C,YAAY,CAAG,CACtBf,WAAW,EACXE,WAAY,GACZzB,UAAW,KACXU,WAAW,EACXC,qBAAsB,KACtBC,mBAAmB,EACnBC,YAAY,EACZE,kBAAkB,CACpB,EAEA,MAAevB,QAAQA,EAAC,kGC1ExB,MAhCgC,GAa5B,yBAmBW+C,MAlBT,UAAC/C,EAAAA,CAAQA,CAAAA,CACP4B,EAiB8BmB,EAAC,KAjBrB,GACVzC,QAAS0C,EAASC,iBAAiB,CAAC3C,OAAO,CAC3CC,KAAMyC,EAASC,iBAAiB,CAACC,WAAW,CAC5CrB,OAAO,EACPK,gBAAgB,IAChBhB,WAAW,EACXmB,oBAAqB,EACrBN,SAAS,IACToB,eAAe,OACflB,WAAY,GACZV,gBAAgB,IAChBC,aAxBqC,CAwBvB4B,EAxBmCC,EAAYC,IAO1DC,IAAAA,OAAS,CAACC,EANG,GAClB,CAKqBC,CALbJ,EAAM,CACLK,CAAG,CAACL,EAAM,CAACM,WAAW,GAExBD,CAAG,CAACL,EAAM,CAEiBC", "sources": ["webpack://_N_E/?fc79", "webpack://_N_E/./components/common/RKITable.tsx", "webpack://_N_E/./pages/vspace/VirtualspaceMonitoringMembers.tsx"], "sourcesContent": ["\n    (window.__NEXT_P = window.__NEXT_P || []).push([\n      \"/vspace/VirtualspaceMonitoringMembers\",\n      function () {\n        return require(\"private-next-pages/vspace/VirtualspaceMonitoringMembers.tsx\");\n      }\n    ]);\n    if(module.hot) {\n      module.hot.dispose(function () {\n        window.__NEXT_P.push([\"/vspace/VirtualspaceMonitoringMembers\"])\n      });\n    }\n  ", "//Import Library\r\nimport React from 'react'\r\nimport DataTable  from 'react-data-table-component';\r\n\r\n//Import services/components\r\nimport { useTranslation } from 'next-i18next';\r\nimport CustomLoader from './CustomLoader';\r\n\r\ninterface RKITableProps {\r\n  columns: any[];\r\n  data: any[];\r\n  totalRows?: number;\r\n  resetPaginationToggle?: boolean;\r\n  subheader?: boolean;\r\n  subHeaderComponent?: React.ReactNode;\r\n  handlePerRowsChange?: (newPerPage: number, page: number) => void;\r\n  handlePageChange?: (page: number) => void;\r\n  rowsPerPage?: number[];\r\n  defaultRowsPerPage?: number;\r\n  selectableRows?: boolean;\r\n  loading?: boolean;\r\n  pagServer?: boolean;\r\n  onSelectedRowsChange?: (selectedRows: any) => void;\r\n  clearSelectedRows?: boolean;\r\n  sortServer?: boolean;\r\n  onSort?: (column: any, sortDirection: string) => void;\r\n  persistTableHead?: boolean;\r\n  sortFunction?: (rows: any[], field: any, direction: 'asc' | 'desc') => any[];\r\n  noHeader?: boolean;\r\n  dense?: boolean;\r\n  pagination?: boolean;\r\n  paginationServer?: boolean;\r\n  paginationTotalRows?: number;\r\n  subHeaderAlign?: string;\r\n  [key: string]: any;\r\n}\r\n\r\nfunction RKITable(props: RKITableProps) {\r\n  const { t } = useTranslation('common');\r\n  const paginationComponentOptions = {\r\n    rowsPerPageText: t('Rowsperpage'),\r\n    };\r\n  const {\r\n    columns,\r\n    data,\r\n    totalRows,\r\n    resetPaginationToggle,\r\n    subheader,\r\n    subHeaderComponent,\r\n    handlePerRowsChange,\r\n    handlePageChange,\r\n    rowsPerPage,\r\n    defaultRowsPerPage,\r\n    selectableRows,\r\n    loading,\r\n    pagServer,\r\n    onSelectedRowsChange,\r\n    clearSelectedRows,\r\n    sortServer,\r\n    onSort,\r\n    persistTableHead,\r\n    sortFunction,\r\n    // Filter out any unknown props that might cause DOM warnings\r\n    ...otherProps\r\n  } = props;\r\n\r\n  // Only pass known DataTable props to avoid DOM warnings\r\n  const dataTableProps = {\r\n    paginationComponentOptions,\r\n    noDataComponent: t(\"NoData\"),\r\n    noHeader: true,\r\n    columns,\r\n    data: data || [],\r\n    dense: true,\r\n    paginationResetDefaultPage: resetPaginationToggle,\r\n    subHeader: subheader,\r\n    progressPending: loading,\r\n    subHeaderComponent,\r\n    pagination: true,\r\n    paginationServer: pagServer,\r\n    paginationPerPage: defaultRowsPerPage ? defaultRowsPerPage : 10,\r\n    paginationRowsPerPageOptions: rowsPerPage ? rowsPerPage : [10, 15, 20, 25, 30],\r\n    paginationTotalRows: totalRows,\r\n    onChangeRowsPerPage: handlePerRowsChange,\r\n    onChangePage: handlePageChange,\r\n    selectableRows,\r\n    onSelectedRowsChange,\r\n    clearSelectedRows,\r\n    progressComponent: <CustomLoader/>,\r\n    sortIcon: <i className=\"sort-icon fas fa-exchange-alt\" />,\r\n    sortServer,\r\n    onSort,\r\n    sortFunction,\r\n    persistTableHead,\r\n    className: \"rki-table\"\r\n  };\r\n  return (\r\n    <DataTable {...dataTableProps} />\r\n  )\r\n};\r\n\r\nRKITable.defaultProps = {\r\n  subHeader: false,\r\n  pagination: true,\r\n  totalRows: null,\r\n  pagServer: true,\r\n  onSelectedRowsChange: null,\r\n  clearSelectedRows: false,\r\n  sortServer: false,\r\n  persistTableHead: false\r\n}\r\n\r\nexport default RKITable;\r\n\r\n", "//Import Library\r\nimport _ from \"lodash\";\r\n\r\n//Import services/components\r\nimport RKITable from \"../../components/common/RKITable\";\r\n\r\nconst VspaceMonitoringMembers = (propData: any) => {\r\n \r\n  const sortMonitoringAndEvaluationMembers = (rows: any, field: any, direction: any) => {\r\n    const handleField = (row: any) => {\r\n      if (row[field]) {\r\n        return row[field].toLowerCase();\r\n      }\r\n      return row[field];\r\n    };\r\n    return _.orderBy(rows, handleField, direction);\r\n  };\r\n\r\n  return (\r\n    <>\r\n      <RKITable\r\n        noHeader={true}\r\n        columns={propData.Monitoringmembers.columns}\r\n        data={propData.Monitoringmembers.subscribers}\r\n        dense={true}\r\n        paginationServer\r\n        pagServer={true}\r\n        paginationTotalRows={0}\r\n        subHeader\r\n        subHeaderAlign=\"left\"\r\n        pagination={true}\r\n        persistTableHead\r\n        sortFunction={sortMonitoringAndEvaluationMembers}\r\n        />\r\n    </>\r\n  )\r\n}\r\n\r\nexport default VspaceMonitoringMembers;"], "names": ["RKITable", "props", "t", "useTranslation", "paginationComponentOptions", "rowsPerPageText", "columns", "data", "totalRows", "resetPaginationToggle", "subheader", "subHeaderComponent", "handlePerRowsChange", "handlePageChange", "rowsPerPage", "defaultRowsPerPage", "selectableRows", "loading", "pagServer", "onSelectedRowsChange", "clearSelectedRows", "sortServer", "onSort", "persistTableHead", "sortFunction", "otherProps", "dataTableProps", "noDataComponent", "<PERSON><PERSON><PERSON><PERSON>", "dense", "paginationResetDefaultPage", "subHeader", "progressPending", "pagination", "paginationServer", "paginationPerPage", "paginationRowsPerPageOptions", "paginationTotalRows", "onChangeRowsPerPage", "onChangePage", "progressComponent", "CustomLoader", "sortIcon", "i", "className", "DataTable", "defaultProps", "VspaceMonitoringMembers", "propData", "Monitoringmembers", "subscribers", "subHeaderAlign", "sortMonitoringAndEvaluationMembers", "field", "direction", "_", "rows", "handleField", "row", "toLowerCase"], "sourceRoot": "", "ignoreList": []}