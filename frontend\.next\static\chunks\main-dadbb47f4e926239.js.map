{"version": 3, "file": "static/chunks/main-dadbb47f4e926239.js", "mappings": "yFAIO,SAASA,EACdC,CAAc,CACdC,CAAqE,EAIrE,GAJAA,KAAAA,QAAAA,EAAmE,EAAC,EAIhEA,EAAQC,cAAc,CAAE,YAC1BF,IAGF,IAAMG,EAAcC,SAASC,eAAe,CACtCC,EAAWH,EAAYI,KAAK,CAACC,cAAc,GACrCD,KAAK,CAACC,cAAc,CAAG,OAC9BP,EAAQQ,eAAe,EAAE,EAIhBC,cAAc,GAE5BV,IACAG,EAAYI,KAAK,CAACC,cAAc,CAAGF,CACrC,+FArBgBP,qCAAAA,gCCMhB,cACA,0DACA,CAXA,qCAA6C,CAC7C,QACA,CAAC,EACD,qCAA6C,CAC7C,cACA,eACA,QACA,CACA,CAAC,EAAC,kHCNWY,qCAAAA,aAF8B,OAE9BA,EAAuB,SAACC,CAAAA,6BAASC,EAAAA,MAAAA,EAAAA,EAAAA,EAAAA,EAAAA,GAAAA,EAAAA,EAAAA,EAAAA,EAAAA,IAAAA,CAAAA,CAAAA,CAAAA,CAAAA,EAAAA,CAAAA,SAAAA,CAAAA,EAAAA,CAE1C,MAAOC,CAAAA,EAAAA,EAAAA,0BAAAA,EACLC,EAAAA,OAAAA,SAAAA,CAA2DH,KAASC,GAI1E,6XCsJaG,gBAAgB,mBAAhBA,GAUAC,yBAAyB,mBAAzBA,GAPAC,mBAAmB,mBAAnBA,GAsBAC,kBAAkB,mBAAlBA,GATAC,eAAe,mBAAfA,wBArKK,QAqJLJ,EAAmBK,EAAAA,OAAK,CAACC,aAAa,CACjD,MAEWJ,EAAsBG,EAAAA,OAAK,CAACC,aAAa,CAK5C,MAEGL,EAA4BI,EAAAA,OAAK,CAACC,aAAa,CAIzD,MAEUF,EAAkBC,EAAAA,OAAK,CAACC,aAAa,CAAkB,MASvDH,EAAqBE,EAAAA,OAAK,CAACC,aAAa,CAAc,IAAIC,oCCzLnEC,yIAEJ,OAEC,mBAFD,GAIgBC,SAAS,mBAATA,SAJhB,EAAe,IACND,EAGF,SAASC,EAAUC,CAAgB,EACxCF,EAAgBE,CAClB,oKCRaC,qBAAqB,mBAArBA,GAQAC,8BAA8B,mBAA9BA,GAuCGC,kCAAkC,mBAAlCA,GAPAC,2BAA2B,mBAA3BA,GAnBAC,yBAAyB,mBAAzBA,KArBT,IAAMJ,EAAwB,CACnCK,UAAW,IACXC,UAAW,IACXC,aAAc,GAChB,EAEMC,EAAgB,IAAIZ,IAAIa,OAAOC,MAAM,CAACV,IAE/BC,EAAiC,2BAavC,SAASG,EACdO,CAAc,EAEd,GACmB,UAAjB,OAAOA,GACPA,UACA,CAAE,YAAYA,CAAAA,CAAAA,CAAI,CACM,UACxB,OADOA,EAAMC,MAAM,CAEnB,OAAO,EAET,GAAM,CAACC,EAAQC,EAAW,CAAGH,EAAMC,MAAM,CAACG,KAAK,CAAC,KAEhD,OACEF,IAAWZ,GACXO,EAAcQ,GAAG,CAACC,OAAOH,GAE7B,CAEO,SAASX,EACdQ,CAA8B,EAG9B,OAAOM,OAAOH,EADWF,MAAM,CAACG,KAAK,CAAC,IAAI,CAAC,EAAE,CAE/C,CAEO,SAASb,EACdgB,CAAc,EAEd,OAAQA,GACN,KAAK,IACH,MAAO,cACT,MAAK,IACH,MAAO,WACT,MAAK,IACH,MAAO,WACT,SACE,MACJ,CACF,4XCtCgBC,gBAAgB,mBAAhBA,GAmCAC,eAAe,mBAAfA,aAzDmB,WACJ,OAqBxB,SAASD,EAAiBE,CAAa,EAC5C,MAAOC,CAAAA,EAAAA,EAAAA,kBAAAA,EACLD,EAAMN,KAAK,CAAC,KAAKQ,MAAM,CAAC,CAACC,EAAUC,EAASC,EAAOC,IAEjD,CAAKF,GAKDG,CAAAA,EAAAA,EAAAA,CALU,aAKVA,EAAeH,IAKfA,KAAoB,CALK,CAKlB,CAAC,EAAE,EAMXA,CAAY,SAAZA,GAAkC,UAAZA,CAAY,EAAM,CACzCC,IAAUC,EAASE,MAAM,CAAG,EAhBrBL,CAiBP,CAIQA,EAAS,IAAGC,EACrB,IAEP,CAMO,SAASL,EAAgBU,CAAW,EACzC,OAAOA,EAAIC,OAAO,CAChB,cACA,KAGJ,6HCvDaC,qCAAAA,wBALuB,WACF,WACI,eAClB,QAEPA,EAA6D,CACxErB,EACAsB,KAGA,IAAMC,EAAQC,CAAAA,EAAAA,EAAAA,OAAAA,EAAQxB,IAAU,UAAWA,EAAQA,EAAMuB,KAAK,CAAGvB,EAC3DyB,EAAgBC,CAAAA,EAAAA,EAAAA,qBAAAA,EAAsBH,GAMxCI,CAAAA,EAAAA,EAAAA,mBAAAA,EAAoBJ,IAExBK,CAAAA,EAAAA,CAFgC,CAEhCA,iBAAAA,EAAkBH,EACpB,6XChBaI,iBAAiB,mBAAjBA,GADAC,eAAe,mBAAfA,GADAC,mBAAmB,mBAAnBA,aAHiB,OAGjBA,EAAsB/C,CAAAA,EAAAA,EAAAA,aAAAA,EAAsC,MAC5D8C,EAAkB9C,CAAAA,EAAAA,EAAAA,aAAAA,EAA6B,MAC/C6C,EAAoB7C,CAAAA,EAAAA,EAAAA,aAAAA,EAA6B,sFCFZ,OA4BhDgD,KAAaC,wBAAwB,CAAG,IAExCC,EAAAA,CAAuBA,CAAG5D,CAC5B,gVClBgB6D,qCAAAA,aAfT,OAGDC,EAAa,gCAGbC,EAAoB,sBASnB,SAASF,EAAezB,CAAa,CAAE4B,CAAsB,QAKlE,CAL4CA,KAAAA,IAAAA,IAAAA,EAAkB,IAC1DC,CAAAA,EAAAA,EAAAA,0BAAAA,EAA2B7B,KAC7BA,EAAQ8B,CAD6B,EAC7BA,EAAAA,mCAAAA,EAAoC9B,GAAO+B,gBAAAA,EAGjDH,GACKD,EAAkBK,GADf,CACmB,CAAChC,GAGzB0B,EAAWM,IAAI,CAAChC,EACzB,oKCwYaiC,WAAW,mBAAXA,GAoBAC,uBAAuB,mBAAvBA,GAPAC,iBAAiB,mBAAjBA,GAZAC,cAAc,mBAAdA,GACAC,iBAAiB,mBAAjBA,GATAC,EAAE,mBAAFA,GACAC,EAAE,mBAAFA,GAlXAC,UAAU,mBAAVA,GAsQGC,QAAQ,mBAARA,GA+BAC,cAAc,mBAAdA,GAXAC,iBAAiB,mBAAjBA,GAKAC,MAAM,mBAANA,GAPHC,aAAa,mBAAbA,GAmBGC,SAAS,mBAATA,GAkBMC,mBAAmB,mBAAnBA,GAdNC,wBAAwB,mBAAxBA,GA+GAC,cAAc,mBAAdA,KA9ZT,IAAMT,EAAa,CAAC,MAAO,MAAO,MAAO,MAAO,MAAO,OAAO,CAsQ9D,SAASC,EACdzF,CAAK,EAEL,IACIkG,EADAC,GAAO,EAGX,OAAQ,sCAAItF,EAAAA,MAAAA,GAAAA,EAAAA,EAAAA,EAAAA,EAAAA,IAAAA,CAAAA,CAAAA,CAAAA,CAAAA,CAAAA,SAAAA,CAAAA,EAAAA,CAKV,OAJKsF,IACHA,EADS,CACF,EACPD,EAASlG,KAAMa,IAEVqF,CACT,CACF,CAIA,IAAME,EAAqB,6BACdP,EAAgB,GAAiBO,EAAmBpB,IAAI,CAACvB,GAE/D,SAASkC,IACd,GAAM,UAAEU,CAAQ,CAAEC,UAAQ,MAAEC,CAAI,CAAE,CAAGC,OAAOC,QAAQ,CACpD,OAAUJ,EAAS,KAAIC,GAAWC,EAAO,IAAMA,EAAbA,EAAoB,CACxD,CADyD,SAGzCX,IACd,GAAM,CAAEc,MAAI,CAAE,CAAGF,OAAOC,QAAQ,CAC1BE,EAAShB,IACf,OAAOe,EAAKE,SAAS,CAACD,EAAOnD,MAAM,CACrC,CAEO,SAASkC,EAAkBmB,CAA2B,EAC3D,MAA4B,UAArB,OAAOA,EACVA,EACAA,EAAUC,WAAW,EAAID,EAAUE,IAAI,EAAI,SACjD,CAEO,SAASjB,EAAUkB,CAAmB,EAC3C,OAAOA,EAAIC,QAAQ,EAAID,EAAIE,WAAW,CAGjC,SAASlB,EAAyBvC,CAAW,EAClD,IAAM0D,EAAW1D,EAAIf,KAAK,CAAC,KAG3B,OACE0E,CAHyB,CAAC,EAAE,CAMzB1D,MAFD,CAEQ,CAAC,MAAO,KACfA,OAAO,CAAC,SAAU,MACpByD,CAAAA,CAAS,EAAE,CAAI,IAAGA,EAASE,KAAK,CAAC,GAAGC,IAAI,CAAC,KAAS,GAEvD,CAFwD,eAIlCvB,EAIpBwB,CAAgC,CAAEC,CAAM,EAUxC,IAAMR,EAAMQ,EAAIR,GAAG,EAAKQ,EAAIA,GAAG,EAAIA,EAAIA,GAAG,CAACR,GAAG,CAE9C,GAAI,CAACO,EAAIE,eAAe,EAAE,MACxB,EAAQD,GAAG,EAAIA,EAAIX,SAAS,CAEnB,CAFqB,UAGf,MAAMd,EAAoByB,EAAIX,SAAS,CAAEW,EAAIA,GAAG,CAC7D,EAEK,CAAC,EAGV,IAAME,EAAQ,MAAMH,EAAIE,eAAe,CAACD,GAExC,GAAIR,GAAOlB,EAAUkB,GACnB,GADyB,IAClBU,EAGT,GAAI,CAACA,EAIH,KAJU,CAIJ,qBAAkB,CAAlB,MAHW,IAAGhC,EAClB6B,GACA,+DAA8DG,EAAM,cAChE,+DAAiB,GAazB,OAAOA,CACT,CAEO,IAAMpC,EAA4B,aAAvB,OAAOqC,YACZpC,EACXD,GACC,CAAC,OAAQ,UAAW,mBAAmB,CAAWsC,KAAK,CACtD,GAA2C,YAA/B,OAAOD,WAAW,CAACE,EAAO,CAGnC,OAAM5C,UAAoB6C,MAAO,CACjC,MAAM1C,UAAuB0C,MAAO,CACpC,MAAMzC,UAA0ByC,MAGrCC,YAAYC,CAAY,CAAE,CACxB,KAAK,GACL,IAAI,CAACC,IAAI,CAAG,SACZ,IAAI,CAAClB,IAAI,CAAG,oBACZ,IAAI,CAACmB,OAAO,CAAI,gCAA+BF,CACjD,CACF,CAEO,MAAM7C,UAA0B2C,MACrCC,YAAYC,CAAY,CAAEE,CAAe,CAAE,CACzC,KAAK,GACL,IAAI,CAACA,OAAO,CAAI,wCAAuCF,EAAK,IAAGE,CACjE,CACF,CAEO,MAAMhD,UAAgC4C,MAE3CC,aAAc,CACZ,KAAK,GACL,IAAI,CAACE,IAAI,CAAG,SACZ,IAAI,CAACC,OAAO,CAAI,mCAClB,CACF,CAWO,SAASjC,EAAe3D,CAAY,EACzC,OAAO6F,KAAKC,SAAS,CAAC,CAAEF,QAAS5F,EAAM4F,OAAO,CAAEG,MAAO/F,EAAM+F,KAAK,EACpE,8BC5cO,SAASC,EACdC,CAAS,CACTC,CAAS,EAET,IAAMC,EAAsC,CAAC,EAM7C,OALArG,OAAOoG,IAAI,CAACD,GAAQG,OAAO,CAAC,IACtB,EAAMC,QAAQ,CAACC,KACjBH,CAD4B,CACpBG,EAAI,CAAGL,CAAM,CAACK,EAAAA,CAE1B,GACOH,CACT,iFAXgBH,qCAAAA,wKCiOAO,qBAAqB,mBAArBA,GAtBAC,eAAe,mBAAfA,IA3MhB,OAAMC,EAOJC,OAAOC,CAAe,CAAQ,CAC5B,IAAI,CAACC,OAAO,CAACD,EAAQvG,KAAK,CAAC,KAAKyG,MAAM,CAACC,SAAU,EAAE,EAAE,EACvD,CAEAC,QAAmB,CACjB,OAAO,IAAI,CAACC,OAAO,EACrB,CAEQA,QAAQ9G,CAAoB,CAAY,CAAhCA,KAAAA,IAAAA,IAAAA,EAAiB,KAC/B,IAAM+G,EAAgB,IAAI,IAAI,CAACC,QAAQ,CAAChB,IAAI,GAAG,CAACiB,IAAI,EAC9B,MAAM,EAAxB,IAAI,CAACC,QAAQ,EACfH,EAAcI,MAAM,CAACJ,EAAcK,OAAO,CAAC,MAAO,GAE1B,MAAM,CAA5B,IAAI,CAACC,YAAY,EACnBN,EAAcI,MAAM,CAACJ,EAAcK,OAAO,CAAC,SAAU,GAErB,MAAM,CAApC,IAAI,CAACE,oBAAoB,EAC3BP,EAAcI,MAAM,CAACJ,EAAcK,OAAO,CAAC,WAAY,GAGzD,IAAMG,EAASR,EACZS,GAAG,CAAC,GAAO,IAAI,CAACR,QAAQ,CAACS,GAAG,CAACC,GAAIZ,OAAO,CAAE,GAAE9G,EAAS0H,EAAE,MACvDhH,MAAM,CAAC,CAACiH,EAAMC,IAAS,IAAID,KAASC,EAAK,CAAE,EAAE,EAQhD,GANsB,MAAM,CAAxB,IAAI,CAACV,QAAQ,EACfK,EAAOM,IAAI,IACN,IAAI,CAACb,QAAQ,CAACS,GAAG,CAAC,MAAOX,OAAO,CAAI9G,EAAO,IAAG,IAAI,CAACkH,QAAQ,CAAC,OAI/D,CAAC,IAAI,CAACY,WAAW,CAAE,CACrB,IAAMC,EAAe,MAAX/H,EAAiB,IAAMA,EAAO6E,KAAK,CAAC,EAAG,CAAC,GAClD,GAAiC,MAA7B,IAAI,CAACyC,oBAAoB,CAC3B,MAAM,qBAEL,CAFK,MACH,uFAAsFS,EAAE,UAASA,EAAE,QAAO,IAAI,CAACT,oBAAoB,CAAC,SADjI,+DAEN,GAGFC,EAAOS,OAAO,CAACD,EACjB,CAkBA,OAhB0B,MAAM,CAA5B,IAAI,CAACV,YAAY,EACnBE,EAAOM,IAAI,IACN,IAAI,CAACb,QAAQ,CACbS,GAAG,CAAC,SACJX,OAAO,CAAI9G,EAAO,OAAM,IAAI,CAACqH,YAAY,CAAC,OAIf,MAAM,CAApC,IAAI,CAACC,oBAAoB,EAC3BC,EAAOM,IAAI,IACN,IAAI,CAACb,QAAQ,CACbS,GAAG,CAAC,WACJX,OAAO,CAAI9G,EAAO,QAAO,IAAI,CAACsH,oBAAoB,CAAC,QAInDC,CACT,CAEQb,QACNuB,CAAkB,CAClBC,CAAmB,CACnBC,CAAmB,CACb,CACN,GAAwB,IAApBF,EAASjH,MAAM,CAAQ,CACzB,IAAI,CAAC8G,WAAW,EAAG,EACnB,MACF,CAEA,GAAIK,EACF,MAAM,IADQ,GACR,cAAwD,CAAxD,MAAW,+CAAX,+DAAuD,GAI/D,IAAIC,EAAcH,CAAQ,CAAC,EAAE,CAG7B,GAAIG,EAAYC,UAAU,CAAC,MAAQD,EAAYE,QAAQ,CAAC,KAAM,CAE5D,IAAIC,EAAcH,EAAYvD,KAAK,CAAC,EAAG,CAAC,GAEpC2D,GAAa,EAOjB,GANID,EAAYF,UAAU,CAAC,MAAQE,EAAYD,QAAQ,CAAC,MAAM,CAE5DC,EAAcA,EAAY1D,KAAK,CAAC,EAAG,CAAC,GACpC2D,GAAa,GAGXD,EAAYF,UAAU,CAAC,KACzB,CAD+B,KACzB,qBAEL,CAFK,MACH,6CAA4CE,EAAY,6BADrD,+DAEN,GASF,GANIA,EAAYF,UAAU,CAAC,QAAQ,CAEjCE,EAAcA,EAAYnE,SAAS,CAAC,GACpC+D,GAAa,GAGXI,EAAYF,UAAU,CAAC,MAAQE,EAAYD,QAAQ,CAAC,KACtD,CAD4D,KACtD,qBAEL,CAFK,MACH,4DAA2DC,EAAY,OADpE,+DAEN,GAGF,GAAIA,EAAYF,UAAU,CAAC,KACzB,CAD+B,KACzB,qBAEL,CAFK,MACH,wDAAuDE,EAAY,OADhE,+DAEN,GAGF,SAASE,EAAWC,CAA2B,CAAEC,CAAgB,EAC/D,GAAqB,MAAM,CAAvBD,GAMEA,IAAiBC,EAEnB,MAAM,EAFuB,KAEvB,cAEL,CAFK,MACH,mEAAkED,EAAa,UAASC,EAAS,OAD9F,+DAEN,GAIJT,EAAUhC,OAAO,CAAC,IAChB,GAAI0C,IAASD,EACX,MAAM,EADe,KACf,cAEL,CAFK,MACH,uCAAsCA,EAAS,yCAD5C,+DAEN,GAGF,GAAIC,EAAK1H,OAAO,CAAC,MAAO,MAAQkH,EAAYlH,OAAO,CAAC,MAAO,IACzD,CAD8D,KACxD,qBAEL,CAFSoE,MACP,mCAAkCsD,EAAK,UAASD,EAAS,kEADtD,+DAEN,EAEJ,GAEAT,EAAUL,IAAI,CAACc,EACjB,CAEA,GAAIR,EACF,GAAIK,EAAY,CACd,GAAyB,CAFb,KAER,IAAI,CAACnB,YAAY,CACnB,MAAM,qBAEL,CAFK,MACH,wFAAuF,IAAI,CAACA,YAAY,CAAC,WAAUY,CAAQ,CAAC,EAAE,CAAC,QAD5H,+DAEN,GAGFQ,EAAW,IAAI,CAACnB,oBAAoB,CAAEiB,GAEtC,IAAI,CAACjB,oBAAoB,CAAGiB,EAE5BH,EAAc,SAChB,KAAO,CACL,GAAiC,MAA7B,IAAI,CAACd,oBAAoB,CAC3B,MAAM,qBAEL,CAFK,MACH,yFAAwF,IAAI,CAACA,oBAAoB,CAAC,YAAWW,CAAQ,CAAC,EAAE,CAAC,OADtI,+DAEN,GAGFQ,EAAW,IAAI,CAACpB,YAAY,CAAEkB,GAE9B,IAAI,CAAClB,YAAY,CAAGkB,EAEpBH,EAAc,OAChB,KACK,CACL,GAAII,EACF,MAAM,IADQ,GACR,cAEL,CAFK,MACH,qDAAoDP,CAAQ,CAAC,EAAE,CAAC,OAD7D,+DAEN,GAEFQ,EAAW,IAAI,CAACvB,QAAQ,CAAEqB,GAE1B,IAAI,CAACrB,QAAQ,CAAGqB,EAEhBH,EAAc,IAChB,CACF,CAGI,IAAK,CAACpB,QAAQ,CAAC7G,GAAG,CAACiI,IACrB,IAAI,CAACpB,KAD8B,GACtB,CAAC6B,GAAG,CAACT,EAAa,IAAI7B,GAGrC,IAAI,CAACS,QAAQ,CACVS,GAAG,CAACW,GACJ1B,OAAO,CAACuB,EAASpD,KAAK,CAAC,GAAIqD,EAAWC,EAC3C,oBAvMAL,WAAAA,EAAuB,OACvBd,QAAAA,CAAiC,IAAI8B,SACrC5B,QAAAA,CAA0B,UAC1BG,YAAAA,CAA8B,UAC9BC,oBAAAA,CAAsC,KAoMxC,CAEO,SAAShB,EACdyC,CAAsC,EAatC,IAAMC,EAAO,IAAIzC,EAKjB,OAFAwC,EAAgB7C,OAAO,CAAC,GAAc8C,EAAKxC,MAAM,CAACyC,IAE3CD,EAAKnC,MAAM,EACpB,CAEO,SAASR,EACd6C,CAAY,CACZC,CAA0B,EAI1B,IAAMC,EAAkC,CAAC,EACnCC,EAAsB,EAAE,CAC9B,IAAK,IAAIC,EAAI,EAAGA,EAAIJ,EAAQlI,MAAM,CAAEsI,IAAK,CACvC,IAAM3I,EAAWwI,EAAOD,CAAO,CAACI,EAAE,CAClCF,EAAO,CAACzI,EAAS,CAAG2I,EACpBD,CAAS,CAACC,EAAE,CAAG3I,CACjB,CAOA,OAJe2F,EAAgB+C,GAIjB7B,GAAG,CAAC,GAAc0B,CAAO,CAACE,CAAO,CAACzI,EAAS,CAAC,CAC5D,uKCjLgB4I,8BAA8B,mBAA9BA,GAzDAC,yBAAyB,mBAAzBA,GA0CAC,kBAAkB,mBAAlBA,GAVAC,oBAAoB,mBAApBA,uCAvCuB,YACP,UACD,WACM,WACP,OAGvB,SAASF,EACdG,CAAuB,EAEvB,MAAO,CACLC,OACED,EAAYC,IAAI,EAClB,EACAC,UACEF,EAAYE,OAAO,EACrB,EACAC,UACEH,EAAYI,MAAM,EACpB,EACAC,aAAc,EACdnC,KAAK3D,CAAI,CAAE,cAAE+F,CAAM,CAAE,CAAV,WAAa,CAAC,EAAd,EACJN,EAAY9B,IAAI,CAAC3D,OAAMgG,EAAW,QAAED,CAAO,EAClD,EACA/I,QAAQgD,CAAI,CAAE,cAAE+F,CAAM,CAAE,CAAV,WAAa,CAAC,EAAd,EACPN,EAAYzI,OAAO,CAACgD,OAAMgG,EAAW,QAAED,CAAO,EACrD,EACAE,SAASjG,CAAI,EACNyF,EAAYQ,QAAQ,CAACjG,EAC5B,CACF,CACF,CAQO,SAASwF,EACdU,CAAwD,SAExD,EAAYC,OAAO,EAAKD,EAAD,KAAa,CAI7BE,CAJ+B,EAI/BA,EAAAA,oBAAAA,EAAqBF,EAAOG,MAAM,EAHhC,IAAIC,eAIf,CAEO,SAASf,EACdW,CAAqE,EAErE,GAAI,CAACA,EAAOC,OAAO,EAAI,CAACD,EAAOK,KAAK,CAClC,CADoC,MAC7B,KAET,IAAMC,EAAqB,CAAC,EAG5B,IAAK,IAAMtE,KADExG,EACKoG,KAAM,IADA,CADL2E,GAAAA,EAAAA,aAAAA,EAAcP,EAAOzJ,QAAQ,EACZiK,MAAM,EAExCF,CAAU,CAACtE,EAAI,CAAGgE,EAAOK,KAAK,CAACrE,EAAI,CAErC,OAAOsE,CACT,CAEO,SAASnB,EAA+B,CAO7C,EAP6C,aAC7CvC,CAAQ,QACRoD,CAAM,CACN,GAAGlF,EAIH,CAP6C,EAQvC2F,EAAMC,CAAAA,EAAAA,EAAAA,MAAAA,EAAO5F,EAAM6F,YAAY,EAC/BC,EAAQC,CAAAA,EAAAA,EAAAA,OAAAA,EAAQ,KAIpB,IA8BIhK,EA9BE8J,EAAeF,EAAIK,OAAO,CAOhC,GANIH,IACFF,EAAIK,OAAO,CAAG,IAKZjJ,CAAAA,EAAAA,EAAAA,cAAAA,EAAemI,EAAOzJ,QAAQ,GAAG,CAK/ByJ,EAAOe,UAAU,EAAE,GASH,CAACf,EAAOC,OAAO,EARjC,OAAO,KAkBX,GAAI,CACFpJ,EAAM,IAAImK,IAAIhB,EAAOG,MAAM,CAAE,WAC/B,CAAE,MAAOc,EAAG,CAEV,MAAO,GACT,CAEA,OAAOpK,EAAIN,QACb,EAAG,CAACyJ,EAAOG,MAAM,CAAEH,EAAOe,UAAU,CAAEf,EAAOC,OAAO,CAAED,EAAOzJ,QAAQ,CAAC,EAEtE,MACE,UAACiB,EAAAA,eAAe,CAAC0J,QAAQ,EAACN,MAAOA,WAC9BhE,GAGP,uHC3HgBuE,qCAAAA,aAJc,OAIvB,SAASA,EAAYnN,CAAY,EACtC,MAAOoN,CAAAA,EAAAA,EAAAA,aAAAA,EAAcpN,EAH4C,GAInE,CAD6BqN,+OCW7BC,EAAOC,OAAO,CARqB,CACjC,CAOeC,WANf,UACA,aACA,WACA,YACD,8HCsCeC,qCAAAA,aApDoB,WACH,WACH,OAkDvB,SAASA,EACdlL,CAAgB,CAChBlD,CAAgB,MAE0BA,EAyCxBiG,EAzClB,GAAM,UAAE+H,CAAQ,MAAEK,CAAI,eAAEC,CAAa,CAAE,CAAqB,OAAlBtO,EAAAA,EAAQuO,UAAAA,EAARvO,EAAsB,CAAC,EAC3DwO,EAAyB,UAC7BtL,EACAoL,cAA4B,MAAbpL,EAAmBA,EAAS2H,QAAQ,CAAC,KAAOyD,CAC7D,EAEIN,GAAYD,CAAAA,EAAAA,EAAAA,aAAAA,EAAcS,EAAKtL,QAAQ,CAAE8K,KAC3CQ,EAAKtL,IADiD,IACzC,CAAGuL,CAAAA,EAAAA,EAAAA,gBAAAA,EAAiBD,EAAKtL,QAAQ,CAAE8K,GAChDQ,EAAKR,QAAQ,CAAGA,GAElB,IAAIU,EAAuBF,EAAKtL,QAAQ,CAExC,GACEsL,EAAKtL,QAAQ,CAAC0H,UAAU,CAAC,iBACzB4D,EAAKtL,QAAQ,CAAC2H,QAAQ,CAAC,SACvB,CACA,IAAM8D,EAAQH,EAAKtL,QAAQ,CACxBO,OAAO,CAAC,mBAAoB,IAC5BA,OAAO,CAAC,UAAW,IACnBhB,KAAK,CAAC,KAGT+L,EAAKI,OAAO,CADID,CAAK,CACNC,EADS,CAExBF,EACEC,WAAK,CAAC,EAAE,CAAgB,IAAGA,EAAMvH,KAAK,CAAC,GAAGC,IAAI,CAAC,KAAS,KAIhC,IAAtBrH,EAAQ6O,SAAS,GACnBL,EAAKtL,QAAQ,CAAGwL,CAAAA,CAEpB,CAIA,GAAIL,EAAM,CACR,IAAIpI,EAASjG,EAAQ8O,YAAY,CAC7B9O,EAAQ8O,YAAY,CAACC,OAAO,CAACP,EAAKtL,QAAQ,EAC1C8L,CAAAA,EAAAA,EAAAA,mBAAAA,EAAoBR,EAAKtL,QAAQ,CAAEmL,EAAKY,OAAO,EAEnDT,EAAKU,MAAM,CAAGjJ,EAAOkJ,cAAc,CACnCX,EAAKtL,QAAQ,CAAkB,OAAf+C,EAAAA,EAAO/C,QAAAA,EAAP+C,EAAmBuI,EAAKtL,QAAQ,CAE5C,CAAC+C,EAAOkJ,cAAc,EAAIX,EAAKI,OAAO,EAAE,CAC1C3I,EAASjG,EAAQ8O,YAAY,CACzB9O,EAAQ8O,YAAY,CAACC,OAAO,CAACL,GAC7BM,GAAAA,EAAAA,mBAAAA,EAAoBN,EAAsBL,EAAKY,QAAO,EAE/CE,cAAc,EAAE,CACzBX,EAAKU,MAAM,CAAGjJ,EAAOkJ,cAAAA,CAG3B,CACA,OAAOX,CACT,8BC5GO,SAASY,EAAoBC,CAAkB,CAAEC,CAAkB,EACxE,IAAMC,EAAYpN,OAAOoG,IAAI,CAAC8G,GAC9B,GAAIE,EAAUhM,MAAM,GAAKpB,OAAOoG,IAAI,CAAC+G,GAAG/L,MAAM,CAAE,MAAO,GAEvD,IAAK,IAAIsI,EAAI0D,EAAUhM,MAAM,CAAEsI,KAAO,CACpC,IAAMlD,EAAM4G,CAAS,CAAC1D,EAAE,CACxB,GAAY,UAARlD,EAAiB,CACnB,IAAM6G,EAAYrN,OAAOoG,IAAI,CAAC8G,EAAErC,KAAK,EACrC,GAAIwC,EAAUjM,MAAM,GAAKpB,OAAOoG,IAAI,CAAC+G,EAAEtC,KAAK,EAAEzJ,MAAM,CAClD,CADoD,MAC7C,EAET,IAAK,IAAIkM,EAAID,EAAUjM,MAAM,CAAEkM,KAAO,CACpC,IAAMC,EAAWF,CAAS,CAACC,EAAE,CAC7B,GACE,CAACH,EAAEtC,KAAK,CAAC2C,cAAc,CAACD,IACxBL,EAAErC,KAAK,CAAC0C,EAAS,GAAKJ,EAAEtC,KAAK,CAAC0C,EAAS,CAEvC,CADA,KACO,EAEX,CACF,MAAO,GACL,CAACJ,EAAEK,cAAc,CAAChH,IAClB0G,CAAC,CAAC1G,EAA6B,GAAK2G,CAAC,CAAC3G,EAA6B,CAEnE,CADA,MACO,CAEX,CAEA,OAAO,CACT,gGA7BgByG,qCAAAA,iBCFhB,WAAY,OAAO,gBAAgB,IAAmB,EAAM,EAAu4B,EAAM,EAAt6B,aAAmB,SAAY,IAA4B,+CAAmD,aAA+B,iDAAqD,GAAY,CAAI,gCAAmC,WAAa,CAAK,CAAoB,GAApB,GAAoB,GAAS,IAAmB,IAAI,kCAAqC,aAAoB,EAAuB,GAAvB,GAAuB,GAAS,IAA2B,CAAJ,QAAI,KAAuB,kBAAmB,uBAAuB,2BAAwD,OAAb,aAAa,gBAAuB,IAAI,cAAc,SAAS,IAAI,wBAAwB,SAAS,0BAA4Q,SAAS,KAAkB,GAAN,EAAe,aAA2B,MAAW,CAAO,KAAQ,SAAa,cAAc,EAAK,GAAK,UAAa,KAAc,aAAsB,OAAM,IAAO,OAAkC,KAAsB,IAAf,eAAe,IAAkB,IAAT,IAAI,KAAK,OAAa,GAAM,WAAY,KAAK,WAAW,OAAO,KAA9iB,YAA4B,oBAAqB,uBAAuB,6BAA+D,OAAf,eAAe,gBAAuB,IAAI,KAAY,SAAS,IAAI,sBAAsB,SAAS,yBAA4V,IAAkO,gBAAmB,WAAW,aAA2J,cAAxY,uBAAuB,gCAAoC,sBAAuB,YAAY,mBAAmB,IAAK,oBAAqB,mBAAsB,iBAAqB,MAAoE,2BAA8B,iCAAiC,kBAAkB,aAAe,SAAS,UAAU,aAAa,cAA+B,OAAU,gBAAmB,SAAY,QAAW,mBAAsB,uBAA0B,SAAY,oBAAuB,wBAA2B,wBAAwB,UAAU,sBAAsB,iDAAqD,iBAAiB,WAAW,oBAAoB,+CAAmD,mBAAmB,YAAY,KAAS,cAAgC,WAAW,cAAkB,iBAAiB,YAAY,YAAY,KAAW,IAAI,oBAAsC,KAAQ,QAAQ,eAAiB,iBAAiB,KAAmE,KAA6C,IAApC,KAAK,OAA+B,CAAiB,qCC0E33EzC,EAKPiD,EAEA9C,EACA+C,EACAC,EACAC,EAMAC,EAEAC,EAGAC,EAAyBC,EACzBC,EAhBAC,gBAAoC5D,yHAL3B6D,OAAO,mBAAPA,GA+vBSC,OAAO,mBAAPA,IA7oBAC,UAAU,mBAAVA,GAnHX7D,MAAM,mBAANA,GADE8D,OAAO,mBAAPA,iCAvEN,mBAQyB,gBACX,YACc,eAClB,YAEa,WACK,SACJ,UAIxB,WACmB,UACsB,UAEzB,eACK,gBACL,YAEQ,WACwB,WACxB,WACI,WAEJ,WACH,WACK,UAM1B,WAIA,UAC4B,QAChB,SACe,OAwB3B,IAAMA,EAAUC,QAAQC,CAElBL,EAA+BM,CAAAA,EAAAA,EAAAA,OAAAA,IAEtCC,EAA8BC,GAAoB,EAAE,CAAC1J,KAAK,CAAC2J,IAAI,CAACD,GAYlEE,GAA2B,CAS/B,OAAMC,UAAkB7P,EAAAA,OAAK,CAACwF,SAAS,CAIrCsK,kBAAkBC,CAAmB,CAAE3C,CAAS,CAAE,CAChD,IAAI,CAAC/G,KAAK,CAAC1H,EAAE,CAACoR,EAAc3C,EAC9B,CAEA4C,mBAAoB,CAClB,IAAI,CAACC,YAAY,GASf1E,EAAO2E,KAAK,GACX1B,CAAAA,CAAYlC,UAAU,EACpBkC,EAAY2B,UAAU,GACpB/M,CAAAA,EAAAA,EAAAA,cAAc,EAACmI,EAAOzJ,QAAQ,GAC7BsD,SAASgL,MAAM,EAEfR,CAAAA,CADAN,CACuB,CAC1Bd,EAAYnI,CAFsB,IAEjB,EAChBmI,EAAYnI,KAAK,CAACgK,OAAO,EACxBjL,CAAAA,CAAAA,QAASgL,MAAM,EAEdR,CAAAA,CADAN,CACuB,CAAE,CAG/B/D,CAFA,CAFqC,OAK3B,CACNA,EAAOzJ,QAAQ,CACb,IACAwO,OACEC,CAAAA,EAAAA,EAAAA,MAAAA,EACEC,CAAAA,EAAAA,EAAAA,sBAAAA,EAAuBjF,EAAOK,KAAK,EACnC,IAAID,gBAAgBvG,SAASgL,MAAM,IAGzC1E,EACA,CAKE+E,GAAI,EAKJC,QAAS,CAAClC,EAAYlC,UAAU,EAAI,CAACsD,CACvC,GAEDe,KAAK,CAAC,IACL,GAAI,CAACC,EAAIC,SAAS,CAAE,MAAMD,CAC5B,EAEN,CAEAE,oBAAqB,CACnB,IAAI,CAACb,YAAY,EACnB,CAEAA,cAAe,CACb,GAAI,MAAEc,CAAI,CAAE,CAAG3L,SAEf,GAAI,CAAC2L,CADLA,EAAOA,GAAQA,EAAKxL,SAAS,CAAC,IACnB,OAEX,IAAMyL,EAAyBjS,SAASkS,cAAc,CAACF,GAClDC,GAILE,CAJS,UAIE,IAAMF,EAAGG,cAAc,GAAI,EACxC,CAEAC,QAAS,CAEL,OAAO,IAAI,CAAC/K,KAAK,CAAC8B,QAAQ,CAQhC,CAEO,eAAeiH,EAAWiC,CAA8B,EAA9BA,KAAAA,QAAAA,EAA4B,EAAC,EAa5D7C,EAAc1H,KAAKwK,KAAK,CACtBvS,SAASkS,cAAc,CAAC,iBAAkBM,WAAW,EAEvDpM,OAAOqM,aAAa,CAAGhD,EAEvBS,EAAgBT,EAAYS,aAAa,CACzC,IAAM9N,EAAiBqN,EAAYiD,WAAW,EAAI,GAGhDxO,KAAaC,wBAAwB,CAAE,GAAE/B,EAAO,WAGlDf,CAAAA,EAAAA,EAAAA,SAAAA,EAAU,CACRsR,IAJ+E,gBAI1D,CAAC,EACtBC,oBAAqBnD,EAAYrO,aAAa,EAAI,CAAC,CACrD,GAEAuL,EAASnH,CAAAA,EAAAA,EAAAA,MAAAA,IAGLmI,CAAAA,EAAAA,EAAAA,WAAW,EAAChB,KACdA,EAASkG,CAAAA,CADc,CACdA,EAAAA,cAAAA,EAAelG,EAAAA,CAGW,EACnC,GAAM,CAAEkC,qBAAmB,CAAE,CAC3BlO,EAAQ,KAA0C,EAE9C,UAFGA,UAEDmS,CAAkB,CAAE,CAC1BnS,EAAQ,KAAyC,EAE7C,UAFGA,QAEDoS,CAAgB,CAAE,CACxBpS,EAAQ,KAA+C,EAEnD,UAFGA,CAEDqS,CAAS,CAAE,CACjBrS,EAAQ,KAAuC,EAEjD,GAAI8O,EAAYX,KAFPnO,EAEc,CAAE,CACvB,IAAMsS,EAAWF,EAAiBpG,GAC5BuG,EAAmBrE,EACvBoE,EAASlQ,QAAQ,CACjB0M,EAAYX,OAAO,EAGjBoE,EAAiBlE,cAAc,EAAE,EAC1BjM,QAAQ,CAAGmQ,EAAiBnQ,QAAQ,CAC7C4J,EAASqG,EAAUC,IAKnB/C,EAAgBT,EAAYV,MAAM,CAIpC,IAAMoE,EAAiBL,GACrBvC,EACAnK,GAD+B,IACxBC,QAAQ,CAACH,QAAQ,CAKtBiN,KACFjD,EAAgBiD,EAAejD,OADb,MACaA,CAEnC,CACF,CAEA,GAAIT,EAAY2D,YAAY,CAAE,CAC5B,GAAM,kBAAEC,CAAgB,CAAE,CAAG1S,EAAQ,KAAU,EAC/C0S,EAAiB5D,EAAY2D,MADOzS,MACK,CAC3C,CAEA+O,EAAa,IAAI4D,EAAAA,OAAU,CAAC7D,EAAYhB,OAAO,CAAErM,GAEjD,IAAMmR,EAAuB,OAAC,CAACpJ,EAAGqJ,EAAE,UAClC9D,EAAW+D,WAAW,CAACC,YAAY,CAACvJ,EAAGqJ,IAezC,OAdIpN,OAAOuN,QAAQ,EAAE,OAGZA,QAAQ,CAAC/J,GAAG,CAAC,GAAOuI,WAAW,IAAMoB,EAASK,GAAI,IAE3DxN,OAAOuN,QAAQ,CAAG,EAAE,CAClBvN,OAAOuN,QAAQ,CAAS1J,IAAI,CAAGsJ,EAGjC3D,CADAA,EAAciE,CAAAA,EAAAA,EAAAA,OAAAA,GAAAA,EACFC,QAAQ,CAAG,IACdtH,EAAO2E,KAAK,CAGrBxB,EAAa3P,SAASkS,cAAc,CAAC,UAC9B,CAAEQ,YAAatQ,CAAO,CAC/B,CAEA,SAAS2R,EAAU5M,CAAiB,CAAE6M,CAAkB,EACtD,MAAO,UAAC7M,EAAAA,CAAK,GAAG6M,CAAQ,EAC1B,CAEA,SAASC,EAAa,CAEQ,MAmBJ/P,EArBJ,aACpBkF,CAAQ,CACoB,CAFR,EAId8K,EAAsBjT,EAAAA,OAAK,CAACoM,OAAO,CAAC,IACjCzB,CAAAA,EAAAA,EAAAA,yBAAAA,EAA0BY,GAChC,EAAE,EACL,MACE,UAACsE,EAAAA,CACClR,GAAI,GAGFuU,EAAY,CAAEhN,EAFd,EAEmB4I,EAAW8B,IAAK3P,CAAM,GAAG0P,KAAK,CAAC,GAChDwC,QAAQlS,IAHuB,CAGlB,CAAC,yBAA0B2P,OAFyB,MAMrE,UAACjR,EAAAA,gBAAgB,CAAC8M,QAAQ,EAACN,MAAO8G,WAChC,UAACjQ,EAAAA,mBAAmB,CAACyJ,QAAQ,EAACN,MAAOtB,CAAAA,EAAAA,EAAAA,oBAAoB,EAACU,YACxD,UAACb,EAAAA,8BAA8B,EAC7Ba,OAAQA,EACRW,aAAcjJ,OAAAA,EAAAA,KAAKuO,aAAa,CAAC4B,UAAAA,GAAnBnQ,WAEd,UAACH,EAAAA,UAF8C,OAE7B,CAAC2J,QAAQ,EAACN,MAAOvB,CAAAA,EAAAA,EAAAA,kBAAAA,EAAmBW,YACpD,UAAC8H,EAAAA,aAAa,CAAC5G,QAAQ,EAACN,MAAOmH,CAAAA,EAAAA,EAAAA,wBAAAA,EAAyB/H,YACtD,UAACgI,EAAAA,kBAAkB,CAAC9G,QAAQ,EAACN,MAAOwC,WAClC,UAAC6E,EAAAA,kBAAkB,CAAC/G,QAAQ,EAC1BN,MACEmD,CAAAA,YAAAA,CAAAA,IAAAA,IAAAA,IAAAA,KAAAA,KAAAA,KAAAA,KAAAA,KAAAA,CAAAA,WAAAA,CAAAA,GAAAA,GAAAA,GAAAA,GAAAA,GAAAA,IAAAA,IAAAA,IAAAA,CAAAA,KAAAA,eAAAA,OAAAA,UAAAA,oBAAAA,CAAAA,EAAAA,YAAAA,CAAAA,CAAAA,CACoB,UAGrBnH,iBAUrB,CAEA,IAAMsL,EACJ,GACA,IACE,IAAMV,EAAqB,CACzB,GAAGW,CAAe,CAClBlO,UAAWwJ,EACX4B,IAAKpC,EAAYoC,GAAG,QACpBrF,CACF,EACA,MAAO,UAACyH,EAAAA,UAAcF,EAAU5M,EAAK6M,IACvC,EAKF,SAASG,EAAYS,CAAkC,EACrD,GAAI,KAAEzN,CAAG,KAAE0K,CAAG,CAAE,CAAG+C,EA2BnB,OALAR,QAAQlS,KAAK,CAAC2P,GACduC,QAAQlS,KAAK,CACV,iIAGIwN,EACJmF,QAAQ,CAAC,WACTC,IAAI,CAAC,OAAC,CAAElN,KAAMmN,CAAc,aAAEC,CAAW,CAAE,GAC1C,MAAOnF,CAAAA,QAAAA,KAAAA,EAAAA,EAAcpJ,SAAAA,IAAcsO,EAC/B,oCAAyB,CACtBD,IAAI,CAAC,GACG,+BAAuB,CAACA,IAAI,CAAC,IAElCF,EAAiBzN,GAAG,CADpBA,EACuBA,EADP8N,OAAO,CAEhBC,KAGVJ,IAAI,CAAC,GAAQ,EACZC,eAAgBI,EAAEF,OAAO,CACzBD,YAAa,EAAE,EACjB,EACF,gBAAED,cAAgBC,CAAY,CACpC,GACCF,IAAI,CAAC,QAkBFF,KAlBG,gBAAEG,CAAc,aAAEC,CAAW,CAAE,GAI9BI,EAAUV,EAAQvN,GAClBkO,EAAS,CACb5O,UAAWsO,UACXK,SACA5I,EACApF,IAAK,KACHyK,EACA9O,SAAU0M,EAAY7H,IAAI,CAC1BiF,MAAO4C,EAAY5C,KAAK,QACxBF,UACAyI,CACF,CACF,EACA,OAAOE,QAAQC,OAAO,CACpBX,CAAsB,MAAtBA,CAAAA,EAAAA,EAAiBtN,KAAAA,EAAK,OAAtBsN,EAAwB/C,GAAAA,EACpB+C,EAAiBtN,KAAK,CACtB3B,CAAAA,EAAAA,EAAAA,mBAAAA,EAAoBwB,EAAKkO,IAC7BP,IAAI,CAAC,GAGLU,GAAS,CACP,GAAGZ,CAAgB,CAHrB,IAIE/C,EACApL,UAAWsO,EACXC,cACA1N,CAP+B,KAOxBmO,CACT,GAEJ,EACJ,CAIA,SAASC,EAAK,CAAsC,EAAtC,QAd6D,KAc3DC,CAAQ,CAA4B,CAAtC,EAIZ,OADA1U,EAAAA,OAAK,CAAC2U,eAAe,CAAC,IAAMD,IAAY,CAACA,EAAS,EAC3C,IACT,CAEA,IAAME,EAAmB,CACvBC,gBAAiB,kBACjBC,aAAc,eACdC,YAAa,cACbC,aAAc,eACdC,YAAa,aACf,EAEMC,EAAsB,CAC1BC,UAAW,oBACXC,gBAAiB,2BACjBC,oBAAqB,iCACrBjE,OAAQ,gBACV,EAEIkE,EAAiB,KAEjBC,IAAyB,EAE7B,SAASC,KACN,CACCZ,EAAiBE,YAAY,CAC7BF,EAAiBI,YAAY,CAC7BJ,EAAiBG,WAAW,CAC5BH,EAAiBK,WAAW,CAC7B,CAAC5N,OAAO,CAAC,GAAUf,YAAYkP,UAAU,CAACC,GAC7C,CAEA,SAASC,KACFxR,EAAAA,EAAE,EAAE,CAEToC,YAAYmP,IAAI,CAACb,EAAiBI,YAAY,EAElB1O,YAAYqP,YAFgC,IAEhB,CACtDf,EAAiBE,YAAY,CAC7B,QACA3S,MAAM,GAEyBmE,YAAYsP,OAAO,CAChDV,EAAoBE,eAAe,CACnCR,EAAiBC,eAAe,CAChCD,EAAiBE,YAAY,EAGNxO,YAAYsP,OAAO,CAC1CV,EAAoBC,SAAS,CAC7BP,EAAiBE,YAAY,CAC7BF,EAAiBI,YAAY,GAyB7BjG,GACFzI,UADe,EAEZqP,gBAAgB,CAACT,EAAoBC,SAAS,EAC9C9N,OAAO,CAAC0H,GAEbyG,KACF,CAEA,SAASK,KACP,GAAI,CAAC3R,EAAAA,EAAE,CAAE,OAEToC,YAAYmP,IAAI,CAACb,EAAiBG,WAAW,EAAE,IACzCe,EAAwCxP,YAAYqP,GADU,aACM,CACxEf,EAAiBK,WAAW,CAC5B,QAGGa,EAAgB3T,MAAM,EAAE,CAEDmE,YAAYqP,gBAAgB,CACtDf,EAAiBE,YAAY,CAC7B,QACA3S,MAAM,GAGNmE,YAAYsP,OAAO,CACjBV,EAAoBG,mBAAmB,CACvCS,CAAe,CAAC,EAAE,CAACpQ,IAAI,CACvBkP,EAAiBE,YAAY,EAE/BxO,YAAYsP,OAAO,CACjBV,EAAoB9D,MAAM,CAC1BwD,EAAiBE,YAAY,CAC7BF,EAAiBG,WAAW,EAE1BhG,IACFzI,SADe,GAEZqP,gBAAgB,CAACT,EAAoB9D,MAAM,EAC3C/J,OAAO,CAAC0H,GACXzI,YACGqP,gBAAgB,CAACT,EAAoBG,mBAAmB,EACxDhO,OAAO,CAAC0H,KAIfyG,KACC,CACCN,EAAoBG,mBAAmB,CACvCH,EAAoB9D,MAAM,CAC3B,CAAC/J,OAAO,CAAC,GAAaf,YAAYyP,aAAa,CAACH,IACnD,CA2BA,SAASI,GAAK,CAKZ,EALY,cACZC,CAAS,CACT9N,UAAQ,CAGR,CALY,SAQZnI,EAAAA,OAAK,CAAC2U,eAAe,CACnB,IAAMsB,EAAU5O,OAAO,CAAC,GAAcqN,KACtC,CAACuB,EAAU,EAeN9N,CACT,CAEA,SAASoM,GAAS7E,CAAsB,EACtC,IAwKiCgF,EAxJ7BwB,EAhBA,KAAEhQ,CAAG,CAwKwBwO,UAxKtBlP,CAwKsBkP,OAxKXrO,CAAK,KAAEuK,CAAG,CAAE,CAAoBlB,EAClDqE,EACF,YAAarE,OAAQrE,EAAYqE,EAAMqE,WAAW,CACpDvO,EAAYA,GAAaoJ,EAAapJ,SAAS,CAG/C,IAAMuN,EAAqB,IAF3B1M,EAAQA,GAASuI,EAAavI,KAAK,CAIjCb,EADGa,KAAK,SAERuK,SACArF,CACF,EAEAqD,EAAemE,EAEf,IAAIoD,GAAoB,EAElBC,EAAgB,IAAI/B,QAAc,CAACC,EAAS+B,KAC5CxH,GACFA,IAEFqH,EAAiB,KACfrH,EAAmB,EAJC,GAKpByF,GACF,EACAzF,EAAmB,KACjBsH,GAAW,EACXtH,EAAmB,KAEnB,IAAM5N,EAAa,qBAAmC,CAAnC,MAAU,0BAAV,+DAAkC,GACrDA,EAAM4P,SAAS,EAAG,EAClBwF,EAAOpV,EACT,CACF,GAoHA,SAASqV,IACPJ,GACF,EAEAK,SApHSA,EACP,GACE,CAACxC,EAKD,OAAO,IAMHyC,EAAmC,IAAItW,IAC3CuW,EAHA1X,SAAS2X,gBAAgB,CAAC,uBAGT/N,GAAG,CAAC,GAASgO,EAAIC,IAXsC,QAW1B,CAAC,iBAG3CC,EAA2B9X,SAAS+X,aAAa,CACrD,wBAEIC,EACJF,MAAAA,EAAAA,KAAAA,EAAAA,EAAUD,YAAY,CAAC,cAEzB7C,EAAY1M,OAAO,CAAC,OAAC,MAAEhC,CAAI,MAAE2R,CAAI,CAA+B,GAC9D,GAAI,CAACR,EAAalV,GAAG,CAAC+D,GAAO,CAC3B,IAAM4R,EAAWlY,SAASmY,aAAa,CAAC,SACxCD,EAASE,YAAY,CAAC,cAAe9R,GACrC4R,EAASE,YAAY,CAAC,QAAS,KAE3BJ,GACFE,EAASE,EADA,UACY,CAAC,QAASJ,GAGjChY,SAASqY,IAAI,CAACC,WAAW,CAACJ,GAC1BA,EAASI,WAAW,CAACtY,SAASuY,cAAc,CAACN,GAC/C,CACF,EAEF,IAgFA,IAAMO,EACJ,KADIA,IACJ,OADIA,KACJ,YACE,UAAC9C,EAAAA,CAAKC,SAhFV,CAgFoB8C,QAhFXA,EACP,GACE,GASA,CALqC,EAMrC,CACA,CANA,GAMMC,EAA4B,EAHlC,EAGsCvX,IAAI6T,EAAYpL,GAAG,CAAC,GAAO+O,EAAErS,IAAI,GACjEoR,EACJhH,EACE1Q,SAAS2X,CANyB,eAMT,CAAC,YAT2C,WAWnEF,EAAyBC,EAAiB9N,GAAG,CACjD,GAASgO,EAAIC,YAAY,CAAC,gBAI5B,IAAK,IAAIe,EAAM,EAAGA,EAAMnB,EAAarU,MAAM,CAAE,EAAEwV,EACzCF,EAD8C,GAC9B,CAACjB,CAAY,CAACmB,EAAI,EACpClB,CAAgB,CAACkB,EAAI,CAACC,eAAe,CAAC,SAEtCnB,CAAgB,CAACkB,EAAI,CAACR,YAAY,CAAC,QAAS,KAKhD,IAAIU,EAAgC9Y,SAAS+X,aAAa,CACxD,wBAGA,GAGA/C,EAAY1M,OAAO,CAAC,EADpB,KACqB,MAAEhC,CAAI,CAAoB,GACvCyS,EAA4B/Y,SAAS+X,aAAa,CACrD,sBAAqBzR,EAAK,MAG3B,IAGAwS,EAAeE,KADf,KACyB,CAAEC,YAAY,CACrCF,EACAD,EAAeI,WAAW,EAE5BJ,EAAgBC,EAEpB,GAIFrI,EACE1Q,SAAS2X,gBAAgB,CAAC,mBAC1BrP,OAAO,CAAC,IACR2J,EAAG+G,UAAU,CAAEG,WAAW,CAAClH,EAC7B,EACF,CAEA,GAAItB,EAAMtE,MAAM,CAAE,CAChB,GAAM,GAAE+M,CAAC,GAAEC,CAAC,CAAE,CAAG1I,EAAMtE,MAAM,CAC7B1M,CAAAA,EAAAA,EAAAA,kBAAAA,EAAmB,KACjByG,OAAOkT,QAAQ,CAACF,EAAGC,EACrB,EACF,CACF,IAWI,WAACpF,EAAAA,WACEF,EAAU5M,EAAK6M,GAChB,UAACuF,EAAAA,MAAM,EAACC,KAAK,gCACX,UAACC,EAAAA,cAAc,iBAtNvBC,EA6NmB/J,EAzNfxK,CAJc,CAIdA,CAHJvF,CAGM,CAH6B,CAG3B,IAyNwB,QAxNlB8W,IAAI,CAACb,EAAiBE,YAAY,EAGhD,IAAM4D,KAAanD,GAAgBG,EAAnB/W,CAAyCkX,GAsNvD,UAACG,GAAAA,CAAKC,UAAW,CAACvB,EAAU4B,EAAa,UAIrCiB,CAHDjI,IAQL,EARmC,KAtN9BgG,EASHqD,GADyB3Y,EAAAA,IARX,CAuNV,EA/M0B,CAAS2Y,eAAe,EACtC,KACdrD,EAAUlE,MAAM,CAACsH,EACnB,IATApD,EAAYsD,EAAAA,OAAQ,CAACC,WAAW,CAACJ,EAAOC,EAAS,CAC/CpW,mBAAAA,EAAAA,kBAAkB,GAGpBiT,IAAgB,GAwNXa,CACT,CAEA,eAAehF,GAAO0H,CAA+B,EAKnD,GACEA,EAAelI,GAAG,GAEjB,CADD,IACqC,IAA7BkI,EAAetT,SAAS,EAC9B,CAACsT,EAAeC,aAAAA,EAClB,YACA,MAAM7F,EAAY4F,GAIpB,GAAI,CACF,MAAMvE,GAASuE,EACjB,CAAE,KAVmF,CAU5ElI,EAAK,CACZ,IAAMoI,EAAYC,CAAAA,EAAAA,EAAAA,cAAAA,EAAerI,GAEjC,GAAKoI,EAA8CnI,SAAS,CAC1D,CAD4D,KACtDmI,CASR,OAAM9F,EAAY,CAAE,GAAG4F,CAAc,CAAElI,IAAKoI,CAAU,EACxD,CACF,CAEO,eAAe7J,GAAQkC,CAA6C,EACzE,IAAI6H,EAAa1K,EAAYoC,GAAG,CAEhC,GAAI,CACF,IAAMuI,EAAgB,MAAM1K,EAAW+D,WAAW,CAAC4G,cAAc,CAAC,SAClE,GAAI,UAAWD,EACb,MAAMA,EAAclY,KADQ,CAI9B,GAAM,CAAEoY,UAAWC,CAAG,CAAExM,QAASyM,CAAG,CAAE,CAAGJ,EACzCrK,EAAYwK,EACRC,GAAOA,EAAIC,eAAe,EAAE,GAChB,QAcRC,EAdS,IACbC,CAAE,MACFhU,CAAI,WACJiU,CAAS,OACTxN,CAAK,UACLyN,CAAQ,CACRC,WAAS,SACTC,CAAO,aACPC,CAAW,CACP,GAEEC,EAAsBC,KAAKC,GAAG,GAAG,KACrCC,CAAAA,IAAKC,KAAK,CAACD,KAAKE,MAAM,GAAM,QAAO,EAAM,KAIvCP,CAJ0C,GAI/BA,EAAQ3X,MAAM,EAAE,CAC7BsX,EAAiBK,CAAO,CAAC,EAAE,CAACH,SAAAA,EAG9B,IAAMW,EAAiC,CACrCZ,GAAIA,GAAMM,OACVtU,EACAiU,UAAWA,GAAaF,EACxBtN,MAAgB,MAATA,EAAgByN,EAAWzN,EAClCoO,MACgB,SAAdV,GAAsC,YAAdA,EACpB,SACA,WACR,EACIE,IACFO,EAAUP,OADK,IACM,CAAGA,CAAAA,EAE1BR,EAAIC,eAAe,CAACc,EACtB,GAGF,IAAME,EAKA,MAAM/L,EAAW+D,OAJrB,IAIgC,CAAC4G,cAAc,CAAC5K,EAAY7H,IAAI,EAClE,GAAI,UAAW6T,EACb,MAAMA,EAAevZ,KALmC,CAI3B,EAGbuZ,EAAenB,SAAS,CAU1C,MAAOpY,EAAO,CAEdiY,EAAaD,CAAAA,EAAAA,EAAAA,cAAAA,EAAehY,EAC9B,CA4CIkE,OAAOsV,mBAAmB,EAAE,MACxBtV,OAAOsV,mBAAmB,CAACjM,EAAYkM,UAAU,EAGzDnP,EAASoP,GAAAA,EAAAA,YAAAA,EAAanM,EAAY7H,IAAI,CAAE6H,EAAY5C,KAAK,CAAEF,EAAQ,CACjEkP,aAAcpM,EAAYnI,KAAK,YAC/BoI,EACAvI,IAAK4I,EACLtJ,UAAWwJ,UACXyE,EACA7C,IAAKsI,EACL5M,YAAYvE,CAAQyG,EAAYlC,UAAU,CAC1CuO,aAAc,CAACzN,EAAMlH,EAAKkF,IACxBgG,GACErQ,OAAOwP,MAAM,CAIX,CAAC,EAAGnD,EAAM,KACVlH,SACAkF,CACF,IAEJ0C,OAAQU,EAAYV,MAAM,CAC1BD,QAASW,EAAYX,OAAO,eAC5BoB,EACA6L,cAAetM,EAAYsM,aAAa,CACxCC,UAAWvM,EAAYuM,SAAS,GAGlCnL,EAA2B,MAAMrE,EAAOyP,gCAAgC,CAExE,IAAMC,EAA6B,CACjC/U,IAAK4I,EACLoM,SAAS,EACT1V,UAAWwJ,EACX3I,MAAOmI,EAAYnI,KAAK,CACxBuK,IAAKsI,EACLH,eAAe,CACjB,GAEI1H,MAAAA,EAAAA,KAAAA,EAAAA,EAAMyD,YAAAA,EAAc,CACtB,MAAMzD,EAAKyD,YAAY,GAGzB1D,GAAO6J,EACT,oPCz+BO,SAASxP,EAAqBC,CAAc,EACjD,OAAO,IAAIa,IAAIb,EAAQ,YAAYyP,YACrC,iGAFgB1P,qCAAAA,iCCAD,SAAS2P,EACtBzZ,CAAa,CACb0Z,CAAgB,EAQhB,OARAA,KAAAA,IAAAA,IAAAA,EAAc,IAQP9b,CALLoC,QACI,SACA,iBAAiBgC,IAAI,CAAChC,GACnB,SAAQA,EACTA,CAAAA,EACM0Z,CAChB,oFAXA,qCAAwBD,2IEFXvY,qCAAAA,KAAN,IAAMA,EACY,YAAvB,OAAOyY,YAEH,YAEA,IAEEC,WAAWpI,OAAO,CAAClS,KAAK,CAACA,EAC3B,qOCRN,mQAAmQ,+BAA+B,uCAAuC,sBAAsB,4DAA4D,6EAA6E,uCAAuC,6BAA4B,mEAAoE,8CAA8C,gCAAgC,6BAA6B,sCAAsC,SAAS,EAAE,aAAa,sCAAsC,QAAQ,EAAE,GAAE,qDAAsD,0CAA0C,sBAAsB,GAAG,GAAE,qDAAsD,uBAAuB,gEAA+D,6CAA8C,yEAA6E,0DAAyD,+CAAgD,IAAI,uBAAqB,SAAS,UAAU,uKCQx1Cua,sBAAsB,mBAAtBA,EAAAA,sBAAsB,EAFlBC,6BAA6B,mBAA7BA,GAgBGC,UAAU,mBAAVA,GAJAC,KAAK,mBAALA,aAlBuB,OAGjCC,EACJ,8EAEWH,EAAgCD,EAAAA,sBAAsB,CAACK,MAAM,CAQ1E,SAASC,EAAmBC,CAAiB,EAC3C,OAAOP,EAAAA,sBAAsB,CAAC7X,IAAI,CAACoY,EACrC,CAEO,SAASJ,EAAMI,CAAiB,EACrC,OAAOC,OAAWD,IAAcD,EAAmBC,EACrD,CAEO,SAASL,EAAWK,CAAiB,SAC1C,EAZkCpY,EAY9BqY,EAZkC,CAYvBD,GACN,MAELD,EAAmBC,CAHI,EAIlB,SAD0B,IAIrC,qKC9BaE,aAAa,mBAAbA,GAiIAC,kBAAkB,mBAAlBA,KAjIN,IAAMD,EAAgB,CAC3B,UACA,QACA,aACA,SACA,SACD,CA2HYC,EAA0C,CACrDC,YAAa,CAAC,IAAK,IAAK,IAAK,KAAM,KAAM,KAAM,KAAM,KAAK,CAC1DC,WAAY,CAAC,GAAI,GAAI,GAAI,GAAI,GAAI,IAAK,IAAK,IAAI,CAC/C7c,KAAM,eACN8c,OAAQ,UACRC,WAAY,GACZC,QAAS,EAAE,CACXC,qBAAqB,EACrBC,gBAAiB,GACjBC,QAAS,CAAC,aAAa,CACvBC,oBAAqB,GACrBC,sBAAwB,gDACxBC,uBAAwB,aACxBC,mBAAezR,EACf0R,eAAgB,EAAE,CAClBC,eAAW3R,EACX4R,aAAa,CACf,8BClJO,SAASC,EAAcC,CAAY,EACxC,OAAOA,EACJ9b,KAAK,CAAC,KACNsH,GAAG,CAAC,GAAOyU,mBAAmBzK,IAC9B1M,IAAI,CAAC,IACV,0FALgBiX,qCAAAA,0HCKAG,qCAAAA,aALc,WACa,OAIpC,SAASA,EAAY9d,CAAY,CAAE+d,CAAkB,EAC1D,MAAO7d,CAAAA,EAAAA,EAAAA,0BAAAA,EACL6P,CAEIiO,EAAAA,EAAAA,CAFmDD,GACnD/d,CAAIA,QACJge,EAAche,EAN6C,IAMvCqN,ySCTrB,QACA,eAEuD,OAQ9DzH,OAAOqY,IAAI,CAAG,CACZnO,QAAAA,EAAAA,OAAO,CAEP,IAAI9D,QAAS,CACX,OAAOA,EAAAA,MAAM,EAEf2D,QAAAA,EAAAA,OAAO,EAGTE,CAAAA,EAAAA,EAAAA,UAAAA,EAAW,CAAC,GACTyE,IAAI,CAAC,IAAM1E,CAAAA,EAAAA,EAAAA,OAAAA,KACXwB,KAAK,CAACwC,QAAQlS,KAAK,+UCpBNwc,qCAAAA,aAFU,OAEnB,SAASA,EAAale,CAAY,CAAEuO,CAAe,EACnB,CACnC,GAAM,UAAEhM,CAAQ,CAAE,CAAG4b,CAAAA,EAAAA,EAAAA,SAAAA,EAAUne,GACzBoe,EAAY7b,EAAS8b,WAAW,GAChCC,EAAc/P,MAAAA,EAAAA,KAAAA,EAAAA,EAAQ8P,WAAW,GAEvC,OAAO9P,IACJ6P,EAAUnU,IAAVmU,MAAoB,CAAE,IAAGE,EAAY,MACpCF,IAAe,IAAGE,CAAAA,CAAAA,CAAY,CAC3B/b,EAASK,EAATL,IAAe,GAAKgM,EAAO3L,MAAM,CAAG,EAAK,IAAM,IAAI5C,EAAKyG,KAAK,CAC9D8H,EAAO3L,MAAM,CAAG,GAElB5C,CACN,CAEF,oPChBA,qCAA6C,CAC7C,QACA,CAAC,EAAC,CAMF,cACA,0CACA,cACA,SACK,CACL,EACA,GACA,6BACA,QACA,CAAK,CACL,qCACA,QACA,CAAK,CACL,gCACA,QACA,CACA,CAAC,EAED,aACA,sDACA,GAAkB,KAAwC,UAAzB,EAAE,OAAuB,CAA8B,EAExF,EAEA,SAJwD,CAKxD,UACA,kCACA,6CACA,QACA,cACA,eACA,CAAS,CAET,EACA,KACA,4FACA,oBAEA,sEACA,eAtBA,KAuBA,8DChDA,qCAA6C,CAC7C,QACA,CAAC,EAAC,CAuDF,cACA,0CACA,cACA,SACK,CACL,EACA,GACA,yBACA,QACA,CAAK,CACL,yBACA,QACA,CAAK,CACL,0BACA,QACA,CAAK,CACL,0BACA,QACA,CAAK,CACL,+BACA,QACA,CAAK,CACL,iCACA,QACA,CAAK,CACL,uCACA,QACA,CAAK,CACL,kCACA,QACA,CAAK,CACL,0BACA,QACA,CAAK,CACL,yCACA,QACA,CAAK,CACL,+BACA,QACA,CAAK,CACL,+BACA,QACA,CAAK,CACL,sCACA,QACA,CAAK,CACL,4BACA,QACA,CAAK,CACL,sCACA,QACA,CAAK,CACL,8CACA,QACA,CAAK,CACL,kDACA,QACA,CAAK,CACL,0CACA,QACA,CAAK,CACL,kCACA,QACA,CAAK,CACL,oCACA,QACA,CAAK,CACL,qCACA,QACA,CAAK,CACL,4BACA,QACA,CAAK,CACL,2CACA,QACA,CAAK,CACL,4BACA,QACA,CAAK,CACL,mCACA,QACA,CAAK,CACL,8BACA,QACA,CAAK,CACL,iCACA,QACA,CAAK,CACL,2BACA,QACA,CAAK,CACL,uCACA,QACA,CAAK,CACL,sDACA,QACA,CAAK,CACL,0CACA,QACA,CAAK,CACL,0BACA,QACA,CAAK,CACL,2CACA,QACA,CAAK,CACL,uCACA,QACA,CAAK,CACL,kCACA,QACA,CAAK,CACL,qCACA,QACA,CAAK,CACL,mCACA,QACA,CAAK,CACL,mCACA,QACA,CAAK,CACL,+BACA,QACA,CAAK,CACL,mCACA,QACA,CAAK,CACL,8BACA,QACA,CAAK,CACL,sBACA,QACA,CAAK,CACL,qCACA,QACA,CAAK,CACL,gDACA,QACA,CAAK,CACL,qCACA,QACA,CAAK,CACL,0BACA,QACA,CAAK,CACL,qCACA,QACA,CAAK,CACL,0CACA,QACA,CAAK,CACL,sDACA,QACA,CAAK,CACL,4CACA,QACA,CAAK,CACL,0BACA,QACA,CAAK,CACL,oCACA,SACA,CACA,CAAC,EACD,aACA,SACA,mBACA,2BACA,wCACA,kBACA,cACA,iBACA,SACA,YACA,UACA,UACA,UACA,sBACA,4BACA,gCACA,gBACA,MACA,MACA,OACA,UACA,UACA,aACA,eACA,cAA+C,EAAoB,EACnE,oBACA,uBACA,qBACA,0BACA,yBACA,mCACA,qCACA,sCACA,mCACA,uCACA,2CACA,kLACA,wGACA,4FACA,2HACA,4GACA,0HACA,+FACA,mGACA,uGACA,6JACA,sNACA,yJACA,GACA,MACA,QACA,aACA,MACA,MACA,CACA,GACA,YACA,qCACA,eACA,EAIA,GAGA,gBAIA,4BAGA,0BAGA,+BAGA,mBAGA,mBAGA,wBAGA,wBAGA,uBAGA,oCAGA,oCAGA,8BAGA,6BACA,EACA,GACA,KACA,OACA,cACA,wBACA,gBACA,CACA,YACA,wBACA,gBACA,aACA,aACA,CACA,eAEA,UACA,UACA,CACA,YACA,sBACA,kBACA,CACA,SACA,wBACA,gBACA,sBACA,kBACA,SACA,aACA,aACA,CACA,UAEA,wBACA,sBACA,kBACA,gBACA,CAEA,EACA,IACA,uCACA,6BACA,wCACA,gDACA,8BC7WO,SAASue,EAAoBnc,CAAa,EAC/C,OAAOA,EAAMU,OAAO,CAAC,MAAO,KAAO,GACrC,gGAFgByb,qCAAAA,kICIAC,qCAAAA,aAXe,WACE,OAU1B,SAASA,EAAoBpX,CAAY,EAC9C,IAAIqX,EAAQC,GAAAA,EAAAA,gBAAAA,EAAiBtX,GAC7B,OAAOqX,EAAMxU,UAAU,CAAC,YAAc,CAACpG,CAAAA,EAAAA,EAAAA,cAAAA,EAAe4a,GAClDA,EAAMhY,KAAK,CAAC,GACF,WAAVgY,EACEA,EACA,GACR,8HChBanM,qCAAAA,KAAN,IAAMA,EAAgC,sCAAIrS,EAAAA,MAAAA,GAAAA,EAAAA,EAAAA,EAAAA,EAAAA,IAAAA,CAAAA,CAAAA,CAAAA,CAAAA,CAAAA,SAAAA,CAAAA,EAAAA,CAE7C,OAAOE,EAAAA,OAAAA,kBAAAA,CAAqE,GACvEF,EAGT,2YER0BgI,qBAAqB,mBAArBA,EAAAA,qBAAqB,EAAtCC,eAAe,mBAAfA,EAAAA,eAAe,EACfrE,cAAc,mBAAdA,EAAAA,cAAc,YADgC,WACxB,iICUf8a,qCAAAA,aART,UAC6C,OAO7C,SAASA,EACdjd,CAAc,EAEd,MAAOkd,CAAAA,EAAAA,EAAAA,eAAAA,EAAgBld,IAAUP,CAAAA,EAAAA,EAAAA,yBAAAA,EAA0BO,EAC7D,oPCbO,SAASiB,EAAeH,CAAe,EAE5C,MAAsB,MAAfA,CAAO,CAAC,EAAE,EAAYA,EAAQ0H,QAAQ,CAAC,IAChD,CAEO,SAAS2U,EAAuBrc,CAAe,EACpD,OAAOA,EAAQyH,UAAU,CAAC,MAAQzH,eACpC,CAEO,SAASsc,EACdtc,CAAgB,CAChBoZ,CAA2D,EAI3D,GAFsBpZ,CAElBuc,CAF0BhX,QAAQ,CAACiX,GAEpB,CACjB,IAAMC,EAAmB1X,KAAKC,SAAS,CAACoU,GACxC,MAAOqD,SACHD,EAAmB,IAAMC,EACzBD,CACN,CAEA,OAAOxc,CACT,wIAGa0c,mBAAmB,mBAAnBA,GADAF,gBAAgB,mBAAhBA,GAhBGF,4BAA4B,mBAA5BA,GATAnc,cAAc,mBAAdA,GAKAkc,sBAAsB,mBAAtBA,KAoBT,IAAMG,EAAmB,WACnBE,EAAsB,qICnBnB9R,qCAAAA,aATU,OASnB,SAASA,EAAcpN,CAAY,CAAE4B,CAAc,EACxD,GAAoB,UAAhB,OAAO5B,EACT,OAAO,EAGT,GAAM,UAAEuC,CAAQ,CAAE,CAAG4b,CAAAA,EAAAA,EAAAA,SAAAA,EAAUne,GAC/B,OAAOuC,IAAaX,GAAUW,EAAS0H,UAAU,CAACrI,EAAS,IAC7D,yHCXgBud,qCAAAA,aAHgB,WACF,OAEvB,SAASA,EACd/c,CAAa,CACbgd,CAAkB,CAClB/S,CAAqB,EAErB,IAAIgT,EAAoB,GAElBC,EAAe/S,CAAAA,EAAAA,EAAAA,aAAAA,EAAcnK,GAC7Bmd,EAAgBD,EAAa9S,MAAM,CACnCgT,EAEJ,CAACJ,IAAehd,EAAQqd,CAAAA,EAAAA,EAAAA,UAD4B,OACZH,GAAcF,GAAc,IAAC,CAGrE/S,EAEFgT,CAJE,CAIkBjd,EACpB,IAAMsd,EAASle,OAAOoG,IAAI,CAAC2X,GAyC3B,OAtCE,EAAQvY,KAAK,CAAC,IACZ,IAAI4F,CAT0C,CASlC4S,CAAc,CAACG,EAAM,EAAI,GAC/B,QAAEC,CAAM,UAAEC,CAAQ,CAAE,CAAGN,CAAa,CAACI,EAAM,CAI7CG,EAAY,IAAGF,EAAAA,CAAS,MAAQ,IAAC,EAAU,IAM/C,OALIC,IACFC,EAAc,GAAe,CADjB,EACW,GAAM,EAAC,IAAKA,EAAS,KAE1CF,GAAU,CAACG,MAAMC,OAAO,CAACpT,KAAQA,EAAQ,CAACA,EAAM,EAGjDiT,CAAAA,GAAYF,KAASH,CAAAA,CAAAA,CAAa,EAElCH,EADD,EAEqBvc,OAAO,CACxBgd,EACAF,EACKhT,EACExD,GAAG,CAKD5G,GAAYqb,mBAAmBrb,GAXC,CAalCkE,IAAI,CAAC,KACRmX,mBAP2D,KAQ5D,IAEX,CAFa,GAGb,CACAwB,EAAoB,IAAG,QAMvBK,EACApa,OAAQ+Z,CACV,CACF,gBAT8D,gHCxDjDpD,qCAAAA,KAAN,IAAMA,EACX,yYCkBc5N,qCAAAA,KAXhB,IAAM4R,EAAQ,IAAIC,QAWX,SAAS7R,EACd9L,CAAgB,CAChB+L,CAA2B,MAYvBE,EATJ,GAAI,CAACF,EAAS,MAAO,CAAE/L,UAAS,EAGhC,IAAI4d,EAAoBF,EAAM5W,GAAG,CAACiF,GAC7B6R,IACHA,EAAoB7R,EAAQlF,GAAG,CAAEmF,GAAWA,EAAO8P,EAD7B,SACwC,IAC9D4B,EAAMxV,GAAG,CAAC6D,EAAS6R,IAOrB,IAAMzd,EAAWH,EAAST,KAAK,CAAC,IAAK,GAIrC,GAAI,CAACY,CAAQ,CAAC,EAAE,CAAE,MAAO,UAAEH,CAAS,EAGpC,IAAMC,EAAUE,CAAQ,CAAC,EAAE,CAAC2b,WAAW,GAIjC5b,EAAQ0d,EAAkBnX,OAAO,CAACxG,UACxC,EAAY,EAAU,CAAP,UAAkB,GAGjCgM,EAAiBF,CAAO,CAAC7L,EAAM,CAKxB,CAAEF,SAFTA,EAAWA,EAASkE,KAAK,CAAC+H,EAAe5L,MAAM,CAAG,IAAM,mBAErC4L,CAAe,EACpC,uHCnCgB4R,qCAAAA,aAvBuB,WACF,WAChB,WACoB,UACE,WAChB,WACI,WACD,OAgBvB,SAASA,EACdpU,CAAkB,CAClBlG,CAAS,CACTua,CAAmB,EAInB,IADIC,EACAC,EAA8B,UAAhB,OAAOza,EAAoBA,EAAO0a,CAAAA,EAAAA,EAAAA,oBAAAA,EAAqB1a,GAInE2a,EAAgBF,EAAYG,KAAK,CAAC,sBAClCC,EAAqBF,EACvBF,EAAY9Z,KAAK,CAACga,CAAa,CAAC,EAAE,CAAC7d,MAAM,EACzC2d,EAIJ,GAAKha,CAAAA,EAF+BzE,KAAK,CAAC,IAAK,EAElC,CAAC,EAAE,EAAI,IAAC,KAAQ,CAAC,aAAc,CAC1C8R,QAAQlS,KAAK,CACV,iBAAgB6e,EAAY,qCAAoCvU,EAAOzJ,QAAQ,CAAC,iFAEnF,IAAMqe,EAAgBxb,CAAAA,EAAAA,EAAAA,wBAAAA,EAAyBub,GAC/CJ,EAAeE,CAAAA,EAAgBA,CAAa,CAAC,EAAE,CAAG,IAAC,CACrD,CAGA,GAAI,CAACI,CAAAA,EAAAA,EAAAA,UAAAA,EAAWN,GACd,OAAQF,EAAY,CAACE,CADO,CACK,CAAGA,EAGtC,GAAI,CACFD,EAAO,IAAItT,IACTuT,EAAYtW,UAAU,CAAC,KAAO+B,EAAOG,MAAM,CAAGH,EAAOzJ,QAAQ,CAC7D,WAEJ,CAAE,MAAO0K,EAAG,CAEVqT,EAAO,IAAItT,IAAI,IAAK,WACtB,CAEA,GAAI,CACF,IAAM8T,EAAW,IAAI9T,IAAIuT,EAAaD,GACtCQ,EAASve,QAAQ,CAAGrC,CAAAA,EAAAA,EAAAA,0BAAAA,EAA2B4gB,EAASve,QAAQ,EAChE,IAAIwe,EAAiB,GAErB,GACEld,CAAAA,EAAAA,EAAAA,cAAAA,EAAeid,EAASve,QAAQ,GAChCue,EAASlF,YAAY,EACrByE,EACA,CACA,IAAMhU,EAAQ2U,CAAAA,EAAAA,EAAAA,sBAAAA,EAAuBF,EAASlF,YAAY,EAEpD,QAAEtW,CAAM,QAAEoa,CAAM,CAAE,CAAGP,CAAAA,EAAAA,EAAAA,aAAAA,EACzB2B,EAASve,QAAQ,CACjBue,EAASve,QAAQ,CACjB8J,GAGE/G,IACFyb,EAAiBP,CAAAA,CADP,CACOA,EAAAA,oBAAAA,EAAqB,CACpCje,SAAU+C,EACVkM,KAAMsP,EAAStP,IAAI,CACnBnF,MAAO3E,CAAAA,EAAAA,EAAAA,IAAAA,EAAK2E,EAAOqT,EACrB,GAEJ,CAGA,IAAMuB,EACJH,EAAS/a,MAAM,GAAKua,EAAKva,MAAM,CAC3B+a,EAAShb,IAAI,CAACW,KAAK,CAACqa,EAAS/a,MAAM,CAACnD,MAAM,EAC1Cke,EAAShb,IAAI,CAEnB,OAAOua,EACH,CAACY,EAAcF,GAAkBE,EAAa,CAC9CA,CACN,CAAE,MAAOhU,EAAG,CACV,OAAOoT,EAAY,CAACE,EAAY,CAAGA,CACrC,CACF,6XC5FSW,MAAM,mBAANA,EAAAA,OAAM,EA6IC9F,YAAY,mBAAZA,GA5BhB,OAAiD,mBAAjD,GA0CgBrH,wBAAwB,mBAAxBA,GA/BAoN,SAAS,mBAATA,GARIC,UAAU,mBAAVA,EAAAA,OAAU,2BAhIZ,gBACC,YAEW,eACV,gBA4HkB,QA9GhCC,EAAuC,CAC3CrV,OAAQ,KACRsV,eAAgB,EAAE,CAClBC,MAAMpM,CAAoB,EACxB,GAAI,IAAI,CAACnJ,MAAM,CAAE,OAAOmJ,IAEtB,IAAI,CAACmM,cAAc,CAAC7X,IAAI,CAAC0L,EAE7B,CACF,EAGMqM,EAAoB,CACxB,WACA,QACA,QACA,SACA,aACA,aACA,WACA,SACA,UACA,gBACA,UACA,YACA,iBACA,gBACD,CAWKC,EAAmB,CACvB,OACA,UACA,SACA,OACA,WACA,iBACD,CASD,SAASC,IACP,GAAI,CAACL,EAAgBrV,MAAM,CAIzB,CAJ2B,KAIrB,qBAAkB,CAAlB,MAFJ,gCACA,kEACI,+DAAiB,GAEzB,OAAOqV,EAAgBrV,MAAM,CAb/BxK,OAAOmgB,cAAc,CAACN,EAAiB,SAAU,KAC/ChY,IACS6X,EAAAA,OAAM,CAACU,MAElB,GAYAJ,EAAkB1Z,OAAO,CAAE+Z,IAKzBrgB,OAAOmgB,cAAc,CAACN,EAAiBQ,EAAO,KAC5CxY,IACiBqY,GACF,CAACG,EAAM,EAG1B,GAEAJ,EAAiB3Z,OAAO,CAAC,IAErBuZ,CAAuB,CAACQ,EAAM,CAAG,sCAAI5hB,EAAAA,MAAAA,GAAAA,EAAAA,EAAAA,EAAAA,EAAAA,IAAAA,CAAAA,CAAAA,CAAAA,CAAAA,CAAAA,SAAAA,CAAAA,EAAAA,CAErC,OADeyhB,GACF,CAACG,EAAM,IAAI5hB,EAC1B,CACF,GAEA6hB,CAxDE,mBACA,sBACA,sBACA,mBACA,kBACA,qBACD,CAkDYha,OAAO,CAAC,IACnBuZ,EAAgBE,KAAK,CAAC,KACpBL,EAAAA,OAAM,CAACU,MAAM,CAACG,EAAE,CAACC,EAAO,sCAAI/hB,EAAAA,MAAAA,GAAAA,EAAAA,EAAAA,EAAAA,EAAAA,IAAAA,CAAAA,CAAAA,CAAAA,CAAAA,CAAAA,SAAAA,CAAAA,EAAAA,CAC1B,IAAMgiB,EAAc,KAAID,EAAME,MAAM,CAAC,GAAGC,WAAW,GAAKH,EAAMhc,SAAS,CACrE,GAGF,GAAIoc,CAAgB,CAACH,EAAW,CAC9B,CADgC,EAC5B,CACFG,CAAgB,CAACH,EAAW,IAAIhiB,EAClC,CAAE,MAAOoR,EAAK,CACZuC,QAAQlS,KAAK,CAAE,wCAAuCugB,GACtDrO,QAAQlS,KAAK,CACXwB,CAAAA,EAAAA,EAAAA,OAAAA,EAAQmO,GAAUA,EAAI/J,OAAO,CAAC,KAAI+J,EAAI5J,KAAK,CAAK4J,EAAM,GAE1D,CAEJ,EACF,EACF,OAGA,EAAegQ,EAWR,SAASF,IACd,IAAMnV,EAASvL,EAAAA,OAAK,CAAC4hB,UAAU,CAACvO,EAAAA,aAAa,EAC7C,GAAI,CAAC9H,EACH,MADW,OACL,cAEL,CAFS9E,MACR,wFADI,+DAEN,GAGF,OAAO8E,CACT,CAQO,SAASoP,IACd,2BAAGnb,EAAH,qBAAGA,CAAAA,CAAH,eAA6C,CAM7C,OAJAohB,EAAgBrV,MAAM,CAAG,IAAIkV,EAAAA,OAAM,IAAIjhB,GACvCohB,EAAgBC,cAAc,CAACxZ,OAAO,CAAC,GAAQwa,KAC/CjB,EAAgBC,cAAc,CAAG,EAAE,CAE5BD,EAAgBrV,MAAM,CAOxB,SAAS+H,EAAyB/H,CAAc,EAErD,IAAMuW,EAAW,CAAC,EAElB,IAAK,IAAMC,KAAYhB,EAAmB,CACxC,GAAsC,UAAlC,OAAOiB,CAAY,CAACD,EAAS,CAAe,CAC9CD,CAAQ,CAACC,EAAS,CAAGhhB,OAAOwP,MAAM,CAChC+O,MAAMC,OAAO,CAACyC,CAAY,CAACD,EAAS,EAAI,EAAE,CAAG,CAAC,EAC9CC,CAAY,CAACD,EAAS,EACtB,QAEJ,CAEAD,CAAQ,CAACC,EAAS,CAAGC,CAAY,CAACD,EAAS,CAY7C,OARAD,EAASX,MAAM,CAR0B,EAQvBV,OAAM,CAACU,MAAM,CAE/BH,EAAiB3Z,OAAO,CAAC,IACvBya,CAAQ,CAACV,EAAM,CAAG,sCAAI5hB,EAAAA,MAAAA,GAAAA,EAAAA,EAAAA,EAAAA,EAAAA,IAAAA,CAAAA,CAAAA,CAAAA,CAAAA,CAAAA,SAAAA,CAAAA,EAAAA,CACpB,OAAOwiB,CAAY,CAACZ,EAAM,IAAI5hB,EAChC,CACF,GAEOsiB,CACT,kVClMYG,qCAAAA,KAAL,IAAKA,EAAAA,SAAAA,CAAAA,SAAAA,WAAAA,gHAAAA,oXCiCS5P,wBA9BO,WACE,eACI,YACR,UACK,UACE,WACG,WACsB,SAKnD,MAkBQ,OAAMA,EA0BnB6P,aAAc,CAEV,MAAOC,CAAAA,EAAAA,EAAAA,sBAAAA,IAAyBtO,IAAI,CAAC,GAAcuO,EAASC,WAAW,CAwB3E,CAEAC,eAAgB,CAUZ,OAHAnd,OAAOod,qBAAqB,CADDjT,EAAsC,CAI1DnK,OAAOod,aAFTC,QAE8B,CAoDvCC,YArDQpX,CA0DP,CAAU,CACT,GAAM,QAAEK,CAAM,MAAErG,CAAI,QAAEyI,CAAM,CAAE,CAAGmR,EAC3B,CAAEnd,SAAU4gB,CAAY,OAAE9W,CAAK,QAAEwE,CAAM,CAAE,CAAG0B,CAAAA,EAAAA,EAAAA,gBAAAA,EAAiBzM,GAC7D,CAAEvD,SAAU6c,CAAU,CAAE,CAAG7M,GAAAA,EAAAA,gBAAAA,EAAiBpG,GAC5C/J,EAAQmc,CAAAA,EAAAA,EAAAA,mBAAAA,EAAoB4E,GAClC,GAAiB,KAAK,CAAlB/gB,CAAK,CAAC,EAAE,CACV,MAAM,qBAA+D,CAA/D,MAAW,4CAA2CA,EAAM,KAA5D,+DAA8D,OAG9CpC,EAYtB0f,EAAO0D,iBAAiB,CACpBhE,EACAvb,CAAAA,EAAAA,EAAAA,cAAAA,EAAezB,GACb+c,CAAAA,EAAAA,EAAAA,aAAAA,EAAcgE,EAAc/D,EAAY/S,GAAO/G,MAAM,CACrDlD,EAfN,IAAMihB,EAAYxH,CAAAA,EAAAA,EAAAA,OAAAA,EAChB0C,CAAAA,EAAAA,EAAAA,mBAAAA,EAAoBxe,CAAAA,EAAAA,EAAAA,SAAAA,EAAUC,EAAMuO,IACpC,SAEF,MAAOuP,CAAAA,EAAAA,EAAAA,WAAAA,EACJ,eAAc,IAAI,CAAC7P,OAAO,CAAGoV,EAAYxS,GAC1C,EAWN,CAEAyS,OACE,CACa,CACK,CAClB,OAAO,IAAI,CAACC,kBAHqB,CAGF,CAACjP,IAAI,CAAC,GAAcuO,EAAS9gB,GAAG,CAACK,GAClE,CAEAiS,SAASjS,CAAa,CAA0B,CAC9C,OAAO,IAAI,CAAC6Q,WAAW,CAACuQ,SAAS,CAACphB,GAAOkS,IAAI,CAAC,IAC5C,GAAI,cAAelO,EACjB,GADsB,GACf,CACLgB,KAAMhB,EAAI0T,SAAS,CACnBE,IAAK5T,EAAImH,OAAO,CAChBiH,YAAapO,EAAIqd,MAAM,CAACra,GAAG,CAAC,GAAQ,EAClCtD,KAAM4d,EAAE5d,IAAI,CACZ2R,KAAMiM,EAAEC,OAAO,EACjB,CACF,CAEF,OAAMvd,EAAI1E,KAAK,EAEnB,CAEAqK,SAAS3J,CAAa,CAAiB,CACrC,OAAO,IAAI,CAAC6Q,WAAW,CAAClH,QAAQ,CAAC3J,EACnC,CAtKA+E,YAAY8G,CAAe,CAAEiE,CAAmB,CAAE,CAChD,IAAI,CAACe,WAAW,CAAG2Q,CAAAA,EAAAA,EAAAA,iBAAAA,EAAkB1R,GAErC,IAAI,CAACjE,OAAO,CAAGA,EACf,IAAI,CAACiE,WAAW,CAAGA,EAEnB,IAAI,CAACqR,mBAAmB,CAAG,IAAIzO,QAAQ,IACjClP,OAAOie,cAAc,CACvB9O,CADyB,CACjBnP,OAAOie,cAAc,EAE7Bje,OAAOke,iBAAiB,CAAG,KACzB/O,EAAQnP,OAAOie,cAAc,CAC/B,CAEJ,EACF,CAwJF,yUCnIA,6DA9EiB,OAyBjB,OAAME,EAmBJC,IAAIC,CAAgB,CAAE,CACpB,GAAI,SAA8B,KAA1B,CAACC,KAAK,CAACA,KAAK,CAClB,MAAM,qBAAmC,CAAnC,MAAU,0BAAV,8DAAkC,GAG1C,IAAI,CAACA,KAAK,CAAG,CACXA,MAAO,QACPD,QAASA,MAAAA,EAAAA,EAAWvJ,KAAKC,GAAG,EAC9B,EAEA,IAAI,CAACwJ,SAAS,CAAC,IAAI,CACrB,CAvBAhd,YACEhB,CAAY,CACZ9G,CAAoB,CACpB8kB,CAA+B,CAC/B,KAEkB9kB,EACDA,EAFjB,IAAI,CAAC8G,IAAI,CAAGA,EACZ,IAAI,CAACie,UAAU,CAAG/kB,OAAAA,EAAAA,EAAQ+kB,UAAAA,EAAR/kB,EAAsB,CAAC,EACzC,IAAI,CAAC+a,SAAS,CAAG/a,OAAAA,EAAAA,EAAQ+a,SAAAA,EAAR/a,EAAqBqb,KAAKC,GAAG,GAC9C,IAAI,CAACwJ,SAAS,CAAGA,EACjB,IAAI,CAACD,KAAK,CAAG,CAAEA,MAAO,YAAa,CACrC,CAcF,CAEA,MAAMG,EAOJC,UAAUne,CAAY,CAAE9G,CAAoB,CAAE,CAC5C,OAAO,IAAI0kB,EAAK5d,EAAM9G,EAAS,IAAI,CAACklB,aAAa,CACnD,CAEAJ,UAAU7B,CAAyB,CAAc,CAE/C,OADA,IAAI,CAACkC,QAAQ,CAACzC,EAAE,CAAC,UAAWO,GACrB,KACL,IAAI,CAACkC,QAAQ,CAACC,GAAG,CAAC,UAAWnC,EAC/B,CACF,oBAfAkC,QAAAA,CAAgCvU,CAAAA,EAAAA,EAAAA,OAAAA,SAExBsU,aAAAA,CAAgB,IACtB,IAAI,CAACC,QAAQ,CAACE,IAAI,CAAC,UAAWC,EAChC,EAYF,KAGA,EAAe,IAAIN,4XC1ENO,iBAAiB,mBAAjBA,GASGvhB,mBAAmB,mBAAnBA,KAZhB,IAAMwhB,EAAiB,kCAGhB,OAAMD,UAA0B1d,MAGrCC,YAA4B2d,CAAc,CAAE,CAC1C,KAAK,CAAE,sCAAqCA,GAAAA,IAAAA,CADlBA,MAAAA,CAAAA,EAAAA,IAAAA,CAFZnjB,MAAAA,CAASkjB,CAIzB,CACF,CAGO,SAASxhB,EAAoBgO,CAAY,QAC9C,UAAI,OAAOA,GAA4B,OAARA,CAAgB,CAAE,YAAYA,GAAAA,CAAE,CAIpD1P,CAJwD,KAIlD,GAAKkjB,CACxB,8BCnBO,SAASE,EAAoBnY,CAAU,EAC5C,OAAOpL,OAAOwjB,SAAS,CAACC,QAAQ,CAAC7U,IAAI,CAACxD,EACxC,CAEO,SAASsY,EAActY,CAAU,EACtC,GAAImY,mBAAkD,GAA9BnY,GACtB,OAAO,EAGT,IAAMoY,EAAYxjB,OAAO2jB,cAAc,CAACvY,GAWxC,OAAqB,OAAdoY,GAAsBA,EAAUhW,cAAc,CAAC,gBACxD,wIArBgB+V,mBAAmB,mBAAnBA,GAIAG,aAAa,mBAAbA,wHCIAnlB,qCAAAA,aARc,WACA,OAOvB,SAASA,EACdC,CAAY,CACZuO,CAAuB,CACvBmB,CAAsB,CACtB0V,CAAsB,EAItB,GAAI,CAAC7W,GAAUA,IAAWmB,EAAe,OAAO1P,EAEhD,IAAMqlB,EAAQrlB,EAAKqe,WAAW,SAI9B,CAAK+G,IACChY,CAAAA,EAAAA,EAAAA,KADa,QACbA,EAAciY,EAAO,SACrBjY,CAAAA,EAAAA,EAAAA,aAAAA,EAAciY,EAAQ,IAAG9W,EAAO8P,WAAW,KAAO,EAIjDL,CAAAA,EAAAA,EAJwDhe,aAIxDge,EAAche,EAAO,IAAGuO,EACjC,mHCXA,qCAAwB6S,+BAlBQ,eAON,OAWX,SAASA,EAItBkE,CAA+C,EAE/C,SAASC,EAAkBze,CAAU,EACnC,MAAO,UAACwe,EAAAA,CAAkBtZ,OAAQmV,CAAAA,EAAAA,EAAAA,SAAAA,IAAc,GAAGra,CAAK,EAC1D,QAEAye,EAAkB1e,eAAe,CAAGye,EAAkBze,eAAe,CAEnE0e,EAA0BC,mBAAmB,CAC7CF,EACAE,mBAAmB,CAOdD,CACT,0PCYIE,yIAqGJ,OAgDC,mBAhDuBpS,GArHRqS,WAAW,mBAAXA,aApCuB,OAoChC,SAASA,EAAYC,CAAe,CAAEC,CAAe,EAC1D,GAAID,aAAkBE,aAAeD,aAAkBC,YAAa,CAClE,IAAMrO,EAAQoO,EAAOvO,YAAY,CAAC,SAGlC,GAAIG,GAAS,CAACmO,EAAOtO,YAAY,CAAC,SAAU,CAC1C,IAAMyO,EAAWF,EAAOG,SAAS,EAAC,GAGlC,OAFAD,EAASlO,YAAY,CAAC,QAAS,IAC/BkO,EAAStO,KAAK,CAAGA,EACVA,IAAUmO,EAAOnO,KAAK,EAAImO,EAAOD,WAAW,CAACI,EACtD,CACF,CAEA,OAAOH,EAAOD,WAAW,CAACE,EAC5B,CAuGe,SAASvS,IAItB,MAAO,CACL2S,iBAAkB,IAAIrlB,IACtBslB,WAAY,IACV,IAAMC,EAAsC,CAAC,EAE7CrO,EAAK/P,OAAO,CAAC,IACX,GAGEqe,CAFA,UAEEnN,IAAI,EACNmN,EAAErf,KAAK,CAAC,uBAAuB,CAE/B,CADA,EAEEtH,GANoD,MAM3C+X,QALyD,KAK5C,CAAE,oBAAmB4O,EAAErf,KAAK,CAAC,YAAY,CAAC,MAEhE,CADA,WAGAqf,EAAErf,KAAK,CAAChB,IAAI,CAAGqgB,EAAErf,KAAK,CAAC,YAAY,CACnCqf,EAAErf,KAAK,CAAC,YAAY,MAAGgF,EAI3B,IAAMsa,EAAaF,CAAI,CAACC,EAAEnN,IAAI,CAAC,EAAI,EAAE,CACrCoN,EAAW3c,IAAI,CAAC0c,GAChBD,CAAI,CAACC,EAAEnN,IAAI,CAAC,CAAGoN,CACjB,GAEA,IAAMC,EAAiBH,EAAKI,KAAK,CAAGJ,EAAKI,KAAK,CAAC,EAAE,CAAG,KAChDA,EAAQ,GACZ,GAAID,EAAgB,CAClB,GAAM,UAAEzd,CAAQ,CAAE,CAAGyd,EAAevf,KAAK,CACzCwf,EACsB,UAApB,OAAO1d,EACHA,EACAmX,MAAMC,OAAO,CAACpX,GACZA,EAASlC,IAAI,CAAC,IACd,EACV,CACI4f,IAAU9mB,SAAS8mB,KAAK,GAAE9mB,SAAS8mB,KAAK,CAAGA,CAAAA,EAC9C,CAAC,OAAQ,OAAQ,OAAQ,QAAS,SAAS,CAACxe,OAAO,CAAC,IACnD2d,EAAezM,EAAMkN,CAAI,CAAClN,EAAK,EAAI,EAAE,CACvC,EACF,CACF,CACF,CAlJEyM,EAAiB,CAACzM,EAAMoN,KACtB,IAAMG,EAAS/mB,SAAS+X,aAAa,CAAC,QACtC,GAAI,CAACgP,EAAQ,OAEb,IAAMC,EAAU,IAAI7lB,IAAI4lB,EAAOpP,gBAAgB,CAAE,GAAE6B,EAAK,qBAExD,GAAa,SAATA,EAAiB,CACnB,IAAMyN,EAAcF,EAAOhP,aAAa,CAAC,gBACrB,MAAM,EAAtBkP,GACFD,EAAQE,GAAG,CAACD,EAEhB,CAEA,IAAME,EAAqB,EAAE,CAC7B,IAAK,IAAIzb,EAAI,EAAGA,EAAIkb,EAAWxjB,MAAM,CAAEsI,IAAK,CAE1C,IAAM0a,EAnEZ,SAASgB,CAA8C,EAA5B,MAmEY9M,GAnEVd,CAAI,OAAElS,CAAK,CAAe,CAA5B,EACnB2K,EAAkBjS,SAASmY,aAAa,CAACqB,GAC/C6N,GAAAA,EAAAA,sBAAAA,EAAuBpV,EAAI3K,GAE3B,GAAM,UAAE8B,CAAQ,yBAAEke,CAAuB,CAAE,CAAGhgB,EAW9C,OAVIggB,EACFrV,EAAGsV,SAAS,CAAGD,EAAwBE,MAAM,EAAI,CADtB,EAElBpe,IACT6I,EAAGO,IADgB,OACL,CACQ,UAApB,OAAOpJ,EACHA,EACAmX,MAAMC,OAAO,CAACpX,GACZA,EAASlC,IAAI,CAAC,IACd,IAEH+K,CACT,EAkDwB2U,CAAU,CAAClb,EAAE,EAE/B0a,EAAOhO,YAAY,CAAC,iBAAkB,IAEtC,IAAIqP,GAAQ,EACZ,IAAK,IAAMtB,KAAUa,EACnB,GAAId,EAAYC,CADY,CACJC,GAAS,CAC/BY,EAAQU,MAAM,CAACvB,GACfsB,GAAQ,EACR,KACF,CAGEA,GACFN,EAAQld,EADC,EACG,CAACmc,EAEjB,CAEA,IAAK,IAAMD,KAAUa,EAAS,KAC5Bb,CAAiB,QAAjBA,EAAAA,EAAOnN,UAAAA,GAAPmN,EAAmBhN,WAAW,CAACgN,EACjC,CAEA,IAAK,IAAMC,KAAUe,EAGgB,MAHP,GAG1Bf,EAAOuB,OAAO,CAAC9I,WAAW,IACS,MACnC,CADAuH,EAAOvO,YAAY,CAAC,YAEpBkP,EAAOa,OAAO,CAACxB,GAEjBW,EAAOzO,WAAW,CAAC8N,EAEvB,8XC2LchC,iBAAiB,mBAAjBA,GA3DAhB,sBAAsB,mBAAtBA,GAnIAyE,YAAY,mBAAZA,GAJAC,cAAc,mBAAdA,gBA7FkB,eACa,WACX,WACc,WACpB,OAkD9B,SAASC,EACPvf,CAAW,CACXoB,CAA+B,CAC/Boe,CAA4B,EAE5B,IAOIC,EAPAC,EAAQte,EAAIC,GAAG,CAACrB,GACpB,GAAI0f,OAAO,CACT,WAAgBA,EACPA,EAAMC,GADQ,GACF,CAEd7S,QAAQC,OAAO,CAAC2S,GAGzB,IAAME,EAAmB,IAAI9S,QAAW,IACtC2S,EAAW1S,CACb,GAEA,OADA3L,EAAIqB,GAAG,CAACzC,EAAK,CAAE+M,QAAS0S,EAAWE,OAAQC,CAAK,GACzCJ,EACHA,IACGlT,IAAI,CAAC,IACJmT,EAAS7a,GACFA,IAERwE,KAAK,CAAC,IAEL,MADAhI,EAAI8d,MAAM,CAAClf,GACLqJ,CACR,GACFuW,CACN,CASA,IAAMC,EAAmBC,OAAO,oBAEzB,SAASR,EAAejW,CAAU,EACvC,OAAO7P,OAAOmgB,cAAc,CAACtQ,EAAKwW,EAAkB,CAAC,EACvD,CAEO,SAASR,EAAahW,CAAW,EACtC,OAAOA,GAAOwW,KAAoBxW,CACpC,CAgBA,IAAM0W,EAdN,SAASC,CAAkC,EACzC,GAAI,CAEF,OADAC,EAAOzoB,SAASmY,YAGS,CAHI,CAAC,QAI3B,CAAC,CAAC/R,OAAOsiB,oBAAoB,EAAI,CAAC,CAAE1oB,SAAiB2oB,YAAY,EAClEF,EAAKG,OAAO,CAACC,QAAQ,CAAC,WAE1B,CAAE,QAAM,CACN,OAAO,CACT,CACF,IAIMC,EAAsB,IACnBC,CAAAA,EAAAA,EAAAA,iCAAAA,IAgET,SAASC,EACPpV,CAAa,CACbqV,CAAU,CACVpX,CAAU,EAEV,OAAO,IAAIyD,QAAQ,CAACC,EAAS+B,KAC3B,IAAIxF,GAAY,EAEhB8B,EAAEkB,IAAI,CAAC,IAELhD,GAAY,EACZyD,EAAQpL,EACV,GAAGyH,KAAK,CAAC0F,GAiBP4R,CAAAA,EAAAA,EAAAA,mBAAAA,EAAoB,IAClB/W,WAAW,KACJL,GACHwF,EAAOzF,EAEX,EAAGoX,EAHe,CAMxB,EACF,CAQO,SAAS7F,WACd,KAAS+F,gBAAgB,CAChB7T,CADkB,OACVC,OAAO,CAACrR,KAAKilB,gBAAgB,EAYvCH,EATiB,IAAI1T,QAAmCC,IAE7D,IAAMuN,EAAK5e,EAQXklB,GARgBC,cAShBC,KATmC,MAC9BD,mBAAmB,CAAG,KACzB9T,EAAQrR,KAAKilB,gBAAgB,EAC7BrG,GAAMA,GACR,CACF,QAKEgF,EAAe,qBAAiD,CAAjD,MAAU,wCAAV,+DAAgD,IAEnE,CAMA,SAASyB,EACP7W,CAAmB,CACnB9P,CAAa,EAcb,OAAOwgB,IAAyBtO,IAAI,CAAEuO,IACpC,GAAI,CAAEzgB,CAAAA,KAASygB,CAAAA,CAAAA,CAAO,EAAI,IAClByE,EAAe,qBAA6C,CAA7C,MAAW,2BAA0BllB,GAArC,+DAA4C,IAEnE,IAAM4mB,EAAWnG,CAAQ,CAACzgB,EAAM,CAACgH,GAAG,CACjCse,GAAUxV,EAAc,UAAYyL,CAAAA,EAAAA,EAAAA,aAAAA,EAAc+J,IAErD,MAAO,CACLuB,QAASD,EACNzgB,MAAM,CAAC,GAAO2gB,EAAEhf,QAAQ,CAAC,QACzBd,GAAG,CAAC,GAAO+f,CAAAA,EAAAA,EAAAA,8BAAAA,EAA+BD,GAAKZ,KAClDc,IAAKJ,EACFzgB,MAAM,CAAC,GAAO2gB,EAAEhf,QAAQ,CAAC,SACzBd,GAAG,CAAC,GAAO8f,EAAIZ,IACpB,CACF,EACF,CAEO,SAAS1E,EAAkB1R,CAAmB,EACnD,IAAMmX,EACJ,IAAI3e,IACA4e,EAA+C,IAAI5e,IACnD8J,EAAqD,IAAI9J,IACzDvB,EACJ,IAAIuB,IAEN,SAAS6e,EACPC,CAA8B,EAKc,KAnJ9CC,EAoJI,IApJsB,EAoJmBH,EAAcjgB,GAAG,CAACmgB,EAAIvE,QAAQ,WACvE,EACS2C,EAILpoB,EALM,OAKG+X,aAAa,CAAE,gBAAeiS,EAAI,MACtC1U,CAD4C,OACpCC,OAAO,IAGxBuU,EAAc7e,GAAG,CAAC+e,EAAIvE,QAAQ,GAAK2C,EA5JhC,IAAI9S,CA4JmC4U,OA5J3B,CAAC3U,EAAS+B,KAM3B2S,CALAA,EAASjqB,SAASmY,aAAa,CAAC,WAKzBgS,MAAM,CAAG5U,EAChB0U,EAAOG,OAAO,CAAG,IACf9S,EAAOwQ,EAAe,qBAA0C,CAAtCpgB,MAAO,0BAoJsBsiB,EApJGA,CAApC,8DAAyC,KAIjEC,EAAOI,WAAW,MAAG9Z,EAIrB0Z,EAAOD,GAAG,EAJ0C,CAKpDhqB,SAASsqB,IAAI,CAAChS,WAAW,CAAC2R,EAC5B,IA2IW7B,EACT,CAGF,CAEA,KALS,EAEN,EAGMmC,EAAgBjkB,CAAY,EACnC,IAAI8hB,EAA6CpT,EAAYnL,GAAG,CAACvD,UAC7D8hB,GAIJpT,EAAY/J,CAJF,EAIK,CACb3E,EACC8hB,EAAOoC,MAAMlkB,EAAM,CAAEmkB,YAAa,aAAc,GAC9C3V,IAAI,CAAC,IACJ,GAAI,CAAClO,EAAI8jB,EAAE,CACT,CADW,KACL,qBAA+C,CAA/C,MAAW,8BAA6BpkB,GAAxC,+DAA8C,GAEtD,OAAOM,EAAIqR,IAAI,GAAGnD,IAAI,CAAC,GAAW,EAAExO,EAAF,GAAQA,EAAM6d,QAASlM,EAAK,EAChE,GACCrG,KAAK,CAAC,IACL,MAAMkW,EAAejW,EACvB,IAdKuW,CAiBX,CAEA,MAAO,gBACL/N,GACS0N,EAAWnlB,EAAOinB,GAE3BnW,aAAa9Q,CAAa,CAAE+nB,CAAoC,GAC5DA,EACErV,QAAQC,OAAO,GACZT,IAAI,CAAC,IAAM6V,KACX7V,IAAI,CACH,GAAmB,EACjBwF,MADiB,IACLvM,GAAWA,EAAQkH,OAAO,EAAKlH,EAC3CA,QAASA,EACX,EACA,GAAU,EAAE7L,CAAF,KAAS2P,GAAI,EAE3ByD,QAAQC,OAAO,MAACjJ,EAAAA,CAAAA,CAAS,IACvB,CAAC,IACL,IAAMse,EAAMf,EAAYhgB,GAAG,CAACjH,GACxBgoB,GAAO,YAAaA,EAClBja,GADuB,CAEzBkZ,EAAY5e,CADH,EACM,CAACrI,EAAO+N,GACvBia,EAAIrV,OAAO,CAAC5E,KAGVA,EACFkZ,EAAY5e,GADH,CACOrI,EAAO+N,GAEvBkZ,EAAYnC,MAAM,CAAC9kB,GAKrB+G,EAAO+d,MAAM,CAAC9kB,GAElB,EACF,EACAohB,UAAUphB,CAAa,CAAE2J,CAAkB,EACzC,OAAOwb,EAA6BnlB,EAAO+G,EAAQ,KACjD,IAAIkhB,EAQJ,OAAO7B,EACLO,EAAiB7W,EAAa9P,GAC3BkS,IAAI,CAAC,OAAC,SAAE2U,CAAO,CAAEG,KAAG,CAAE,GACrB,OAAOtU,QAAQwV,GAAG,CAAC,CACjBjB,EAAYtnB,GAAG,CAACK,GACZ,EAAE,CACF0S,QAAQwV,GAAG,CAACrB,EAAQ7f,GAAG,CAACmgB,IAC5BzU,QAAQwV,GAAG,CAAClB,EAAIhgB,GAAG,CAAC2gB,IACrB,CACH,GACCzV,IAAI,CAAC,GACG,IAAI,CAACuF,cAAc,CAACzX,GAAOkS,IAAI,CAAEiW,GAAgB,aACtDA,EACA9G,OAAQrd,CAAG,CAAC,EAAE,CAChB,IAvYY,KA0YhBkhB,EAAe,qBAAqD,CAArD,MAAW,mCAAkCllB,GAA7C,8DAAoD,KAElEkS,IAAI,CAAC,OAAC,YAAEiW,CAAU,QAAE9G,CAAM,CAAE,GACrBrd,EAAwB5E,OAAOwP,MAAM,CAGzC,CAAEyS,OAAQA,CAAQ,EAAG8G,GACvB,MAAO,UAAWA,EAAaA,EAAankB,CAC9C,GACCgL,KAAK,CAAEC,IACN,GAAItF,EAEF,MAAMsF,EAFM,MAIP,CAAE3P,MAAO2P,CAAI,CACtB,GACCmZ,OAAO,CAAC,IAAMH,MAAAA,EAAAA,KAAAA,EAAAA,IACnB,EACF,EACAte,SAAS3J,CAAa,EAGpB,IAAIqoB,QACJ,CAAKA,EAAMC,UAAkBC,UAAAA,EAAa,EAEpCF,EAAGG,QAAQ,EAAI,KAAKxmB,IAAI,CAACqmB,EAAGI,cAAa,EAAU/V,CAAP,OAAeC,OAAO,GAEjEgU,EAAiB7W,EAAa9P,GAClCkS,IAAI,CAAC,GACJQ,QAAQwV,GAAG,CACTvC,EACI+C,EAAO7B,OAAO,CAAC7f,GAAG,CAAC,SACjB2hB,GAxThB9C,IAAsB,KAFtBniB,EA0T+B2jB,EA1TnB,QA0TkC,GAzT9CuB,EAAU,SAGH,IAAIlW,QAAc,CAACC,EAAS+B,KACjC,IAAMmU,EAAY,uCACcnlB,EAAK,yCACNA,EAAK,2BACnBA,EAAK,KACtB,GAAItG,SAAS+X,aAAa,CAAC0T,GACzB,OAAOlW,CAD6B,GAItCkT,EAAOzoB,SAASmY,aAAa,CAAC,QAG1BqT,IAAI/C,EAAM+C,EAAE,CAAGA,CAAAA,EACnB/C,EAAMiD,GAAG,CAAI,WACbjD,EAAM4B,WAAW,MAAG9Z,EACpBkY,EAAM0B,KAD6C,CACvC,CAAG5U,EACfkT,EAAM2B,OAAO,CAAG,IACd9S,EAAOwQ,EAAe,qBAAwC,CAAxC,MAAW,uBAAsBxhB,GAAjC,+DAAuC,KAG/DmiB,EAAMniB,IAAI,CAAGA,EAEbtG,SAASqY,IAAI,CAACC,WAAW,CAACmQ,EAC5B,KAiSc,EAAE,GAGT3T,IAAI,CAAC,KACJoU,CAAAA,EAAAA,EAAAA,mBAAAA,EAAoB,IAAM,IAAI,CAAClF,SAAS,CAACphB,GAAO,GAAMgP,KAAK,CAAC,KAAO,GACrE,GACCA,KAAK,CACJ,KACO,EAEb,CACF,CACF,6XCjaa+Z,kBAAkB,mBAAlBA,GAiDAC,oBAAoB,mBAApBA,GApDAC,kBAAkB,mBAAlBA,GACAC,wBAAwB,mBAAxBA,GA8BAC,0BAA0B,mBAA1BA,GALAC,aAAa,mBAAbA,GADAC,aAAa,mBAAbA,GAvBAC,cAAc,mBAAdA,GAyBAC,wBAAwB,mBAAxBA,GAOAC,yBAAyB,mBAAzBA,GANAC,wBAAwB,mBAAxBA,GA4BAC,+BAA+B,mBAA/BA,GAPAC,gCAAgC,mBAAhCA,GACAC,oCAAoC,mBAApCA,GAUAC,qCAAqC,mBAArCA,IACAC,4CAA4C,mBAA5CA,IAPAC,yCAAyC,mBAAzCA,GAIAC,mCAAmC,mBAAnCA,IA5EAC,gBAAgB,mBAAhBA,GARAC,cAAc,mBAAdA,GA8CAC,YAAY,mBAAZA,GA4CAC,uBAAuB,mBAAvBA,IAUAC,uBAAuB,mBAAvBA,IANAC,kBAAkB,mBAAlBA,IAnDAC,8BAA8B,mBAA9BA,GAJAC,yBAAyB,mBAAzBA,GAiCAC,oBAAoB,mBAApBA,GAmBAC,oBAAoB,mBAApBA,IA6BAC,0BAA0B,mBAA1BA,IAtFAC,aAAa,mBAAbA,GADAC,aAAa,mBAAbA,GAHAC,yBAAyB,mBAAzBA,GAOAC,eAAe,mBAAfA,GAgCAC,mCAAmC,mBAAnCA,GALAC,yBAAyB,mBAAzBA,GAxBAC,mBAAmB,mBAAnBA,GA0BAC,kCAAkC,mBAAlCA,GAtEJ/f,0BAA0B,mBAA1BA,EAAAA,OAA0B,EA4DtBggB,qBAAqB,mBAArBA,GAxBAC,kBAAkB,mBAAlBA,GARAC,cAAc,mBAAdA,GAHAC,wBAAwB,mBAAxBA,GAHAC,YAAY,mBAAZA,GAKAC,UAAU,mBAAVA,GAJAC,sBAAsB,mBAAtBA,GACAC,uBAAuB,mBAAvBA,GAEAC,UAAU,mBAAVA,GAaAC,kBAAkB,mBAAlBA,GASAC,uBAAuB,mBAAvBA,GARAC,eAAe,mBAAfA,GA2EAC,gBAAgB,mBAAhBA,IAlEAC,gBAAgB,mBAAhBA,GAPAC,qBAAqB,mBAArBA,GAuDAC,eAAe,mBAAfA,IA/BAC,yBAAyB,mBAAzBA,GA8BAC,eAAe,mBAAfA,IAcAC,mBAAmB,mBAAnBA,IAnDAC,0BAA0B,mBAA1BA,GAxBAC,8BAA8B,mBAA9BA,GA4GAC,kBAAkB,mBAAlBA,IAhCAC,oBAAoB,mBAApBA,IAlEAC,oCAAoC,mBAApCA,GAoEAC,gCAAgC,mBAAhCA,IA7FAC,0BAA0B,mBAA1BA,GACAC,gCAAgC,mBAAhCA,GAQAC,aAAa,mBAAbA,wBA/B0B,QAM1B7C,EAAiB,CAC5B8C,OAAQ,SACRC,OAAQ,SACRC,WAAY,aACd,EAIajD,EAET,CACF,CAACC,EAAe8C,MAAM,CAAC,CAAE,EACzB,CAAC9C,EAAe+C,MAAM,CAAC,CAAE,EACzB,CAAC/C,EAAegD,UAAU,CAAC,CAAE,CAC/B,EAEaL,EAA6B,cAC7BC,EAAoC,GAAED,EAA2B,QACjErB,EAAe,eACfE,EAAyB,yBACzBC,EAA0B,0BAC1BJ,EAA2B,2BAC3BK,EAAa,aACbH,EAAa,aACbH,EAAiB,sBACjByB,EAAgB,qBAChB9D,EAAqB,0BACrBC,EAA2B,gCAC3BI,EAAiB,sBACjBP,EAAqB,0BACrB+B,EAA4B,iCAC5B0B,EAAiC,iCACjCnB,EAAqB,qBACrBR,EAAgB,qBAChBD,EAAgB,qBAChBiB,EAAqB,0BACrBE,EAAkB,uBAClBhB,EAAkB,uBAClBmB,EAAwB,6BACxB1B,EAA4B,yBAC5BU,EAAsB,2BACtByB,EACX,iCACWpC,EAAiC,8BACjCuB,EAA0B,+BAC1BG,EAAmB,SACnB9B,EAAe,CAC1B,iBACA,kBACA,iBACD,CACYd,EAAgB,WAChBD,EAAgB,CAAC,aAAc,QAAS,UAAU,CAClDG,EAA2B,SAC3BE,EAA2B,SAC3B8C,EAA6B,4BAC7BnB,EAAwB,4BACxBjC,EAA6B,sBAG7BK,EAA4B,4BAE5B4C,EAA4B,4BAE5BnB,EAA4B,4BAE5BE,EACX,qCAEWH,EACX,sCAEWP,EAAuB,uBAGvBd,EAAoC,OACpCC,EAAwC,GAAED,EAAiC,OAE3EX,EAAuB,sBAEvBe,EAA6C,gBAE7CL,EAAmC,MAEnCM,GAAuC,UAEvCH,GAAwC,YACxCC,GAA+CpE,OAC1DmE,IAEWO,GAA0B,kBAC1BM,GAAuB,uBACvB2B,GAAkB,UAClBF,GAAkB,UAClB7B,GAAqB,CAChCvmB,KAAM,kBACNopB,cAAe,IACfC,WAAY,kBACZC,WAAY,IACd,EACahD,GAA0B,CACrCtmB,KAAM,QACNopB,cAAe,IACfC,WAAY,kBACZC,WAAY,IACd,EACaf,GAAsB,CAAC,OAAO,CAC9BI,GAAuB,EAEvBE,GAAmC,IAEnCZ,GAAmB,CAC9BgB,OAAQ,SACRC,OAAQ,QACV,EAMatC,GAA6B,CACxC,iBACA,eACA,mBACA,4BACA,oBACA,uBACA,sBACA,eACA,iBACA,eACA,cACA,+BACA,4BACA,kCACA,mCACA,kCACD,CAEY8B,GAAqB,IAAIluB,IAAY,CAChDorB,EACAI,EACAL,EACAE,EACD,qPCjJM,SAAStN,EAAiB1e,CAAY,EAC3C,OAAOA,EAAK8C,OAAO,CAAC,MAAO,IAC7B,6FAFgB4b,qCAAAA,yHCCAmC,qCAAAA,aANiC,UACrB,OAKrB,SAASA,EAAWhe,CAAW,EAEpC,GAAI,CAACoC,CAAAA,EAAAA,EAAAA,aAAAA,EAAcpC,GAAM,OAAO,EAChC,GAAI,CAEF,IAAM6sB,EAAiB3qB,CAAAA,EAAAA,EAAAA,iBAAAA,IACjB4qB,EAAW,IAAI3iB,IAAInK,EAAK6sB,GAC9B,OAAOC,EAAS5pB,MAAM,GAAK2pB,GAAkBviB,GAAAA,EAAAA,WAAAA,EAAYwiB,EAASptB,QAAQ,CAC5E,CAAE,MAAO0K,EAAG,CACV,OAAO,CACT,CACF,gCCjBA,cACA,0BAA2C,UAC3C,8LCAa2iB,mBAAmB,mBAAnBA,GAEDC,YAAY,mBAAZA,GAgBIjR,eAAe,mBAAfA,aApBmB,OAEtBgR,EAAsB,gBAE5B,IAAKC,EAAAA,SAAAA,CAAAA,GAAAA,WAAAA,6BAAAA,OAgBL,SAASjR,EAAgBld,CAAc,EAC5C,GACmB,UAAjB,OAAOA,GACG,OAAVA,GACA,CAAE,YAAYA,CAAAA,CAAAA,CAAI,CACM,UAAxB,OAAOA,EAAMC,MAAM,CAEnB,MAAO,GAGT,IAAMA,EAASD,EAAMC,MAAM,CAACG,KAAK,CAAC,KAC5B,CAACguB,EAAW9W,EAAK,CAAGrX,EACpBouB,EAAcpuB,EAAO8E,KAAK,CAAC,EAAG,CAAC,GAAGC,IAAI,CAAC,KAGvCspB,EAAahuB,OAAOC,EAFJguB,EAAE,CAAC,CAAC,IAI1B,OACEH,IAAcF,IACJ,YAAT5W,GAAsBA,IAAtBA,MAA+B,EAAK,CACrC,iBAAO+W,GACP,CAACG,MAAMF,IACPA,KAActN,EAAAA,kBAAkB,sPCzCpC,QACA,WAA2C,MAA3C,GAAqC,GAAM,0CAAuF,MAAvF,GAAiF,GAAM,wBAAkE,GAAM,SAAW,EAAQ,KAA4B,YAA7B,oBCD5N,qCAA6C,CAC7C,QACA,CAAC,EAAC,CAKF,cACA,0CACA,cACA,SACK,CACL,EACA,GAIA,mBACA,QACA,CAAK,CACL,0BACA,QACA,CACA,CAAC,EACD,MAAuB,EAAQ,KAA+B,EAC9D,UAD8B,CAC9B,GACA,6DACA,CAcA,qBACA,KACA,EAoBA,4DAnCA,GACA,kBACA,gCAEA,iCACA,YACA,mBAEA,QACA,CACA,QACA,CAAK,CACL,EAuBA,8BACA,aACA,cACA,eACA,CAAK,CACL,uKClEayN,0BAA0B,mBAA1BA,GAkBGjsB,mCAAmC,mBAAnCA,GAXAD,0BAA0B,mBAA1BA,aAViB,MAGpBksB,EAA6B,CACxC,WACA,MACA,OACA,QACD,CAEM,SAASlsB,EAA2BjE,CAAY,EAErD,YAKU8L,IAJR9L,EACG8B,KAAK,CAAC,KACNsuB,IAAI,CAAC,GACJD,EAA2BC,IAAI,CAAC,GAAO5tB,EAAQyH,UAAU,CAAC0K,IAGlE,CAEO,SAASzQ,EAAoClE,CAAY,EAC9D,IAAIqwB,EACFC,EACAnsB,EAEF,IAAK,IAAM3B,KAAWxC,EAAK8B,KAAK,CAAC,KAAM,GACrCwuB,CACIA,CADKH,EAA2BC,IAAI,CAAC,GAAO5tB,EAAQyH,UAAU,CAAC0K,IACvD,CACT,CAAC0b,EAAmBlsB,EAAiB,CAAGnE,EAAK8B,KAAK,CAACwuB,EAAQ,GAC5D,KACF,CAGF,GAAI,CAACD,GAAqB,CAACC,GAAU,CAACnsB,EACpC,MAAM,UADgD,WAGrD,CAFK,MACH,+BAA8BnE,EAAK,qFADhC,+DAEN,GAKF,OAFAqwB,EAAoBnuB,CAAAA,EAAAA,EAAAA,gBAAAA,EAAiBmuB,GAE7BC,GACN,IAAK,MAGDnsB,EADwB,CAL0B,IAKrB,CAA3BksB,EACkB,IAAGlsB,EAEJksB,EAAoB,IAAMlsB,EAE/C,KACF,KAAK,OAEH,GAA0B,KAAK,CAA3BksB,EACF,MAAM,qBAEL,CAFK,MACH,+BAA8BrwB,EAAK,gEADhC,+DAEN,GAEFmE,EAAmBksB,EAChBvuB,KAAK,CAAC,KACN2E,KAAK,CAAC,EAAG,CAAC,GACV8pB,MAAM,CAACpsB,GACPuC,IAAI,CAAC,KACR,KACF,KAAK,QAEHvC,EAAmB,IAAMA,EACzB,KACF,KAAK,WAGH,IAAMqsB,EAAyBH,EAAkBvuB,KAAK,CAAC,KACvD,GAAI0uB,EAAuB5tB,MAAM,EAAI,EACnC,CADsC,KAChC,qBAEL,CAFK,MACH,+BAA8B5C,EAAK,mEADhC,+DAEN,GAGFmE,EAAmBqsB,EAChB/pB,KAAK,CAAC,EAAG,CAAC,GACV8pB,MAAM,CAACpsB,GACPuC,IAAI,CAAC,KACR,KACF,SACE,MAAM,qBAAyC,CAAzC,MAAU,gCAAV,+DAAwC,EAClD,CAEA,MAAO,mBAAE2pB,mBAAmBlsB,CAAiB,CAC/C,gIChEgB0iB,qCAAAA,KAxBhB,IAAM4J,EAA4C,CAChDC,cAAe,iBACfC,UAAW,QACXC,QAAS,MACTC,UAAW,aACXC,SAAU,UACZ,EAEMC,EAAc,CAClB,SACA,UACA,0BACA,WACA,UACA,WACA,cACD,CAED,SAASC,EACPC,CAAY,EAEZ,MAAO,CAAC,QAAS,QAAS,WAAW,CAAClpB,QAAQ,CAACkpB,EACjD,CAEO,SAASpK,EAAuBpV,CAAe,CAAE3K,CAAa,EACnE,IAAK,GAAM,CAACsM,EAAGxG,EAAM,GAAIpL,OAAO+Y,OAAO,CAACzT,GAAQ,CAC9C,GAAI,CAACA,EAAMkI,cAAc,CAACoE,IACtB2d,EAAYhpB,QAAQ,CAACqL,IAAI,WADC,SAQ9B,IAAM6d,EAAOR,CAAiB,CAACrd,EAAE,EAAIA,EAAEiL,WAAW,GAE/B,WAAf5M,EAAG0V,OAAO,EAAiB6J,EAAyBC,GAGpDxf,CAAwB,CAACwf,EAAK,CAAG,CAAC,CAACrkB,EAErC6E,EAAGmG,YAAY,CAACqZ,EAAMlgB,OAAOnE,MAMnB,IAAVA,GACgB,WAAf6E,EAAG0V,OAAO,EACT6J,EAAyBC,IACxB,EAACrkB,GAAmB,aAAM,EAC7B,CAGA6E,EAAGmG,YAAY,CAACqZ,EAAM,IACtBxf,EAAG4G,eAAe,CAAC4Y,GAEvB,CACF,qPCjCI,mBAA+D,sKC+XnDC,uBAAuB,mBAAvBA,GA5BAC,kBAAkB,mBAAlBA,GA7LA5kB,aAAa,mBAAbA,GAzFA6kB,cAAc,mBAAdA,aAnGT,WACoC,WACR,WACC,OAkF9BC,EAAoB,2CAcnB,SAASD,EAAezR,CAAa,EAC1C,IAAMe,EAAQf,EAAMe,KAAK,CAAC2Q,UAE1B,EAIOC,EAJH,CAI8B,CAAC,CAJvB,CAIyB,EAH5BA,EAAsB3R,EAIjC,CAaA,SAAS2R,EAAsB3R,CAAa,EAC1C,IAAME,EAAWF,EAAM1V,UAAU,CAAC,MAAQ0V,EAAMzV,QAAQ,CAAC,KACrD2V,IACFF,EAAQA,EAAMlZ,EADF,GACO,CAAC,EAAG,CAAC,IAE1B,IAAMmZ,EAASD,EAAM1V,UAAU,CAAC,OAIhC,OAHI2V,IACFD,EAAQA,EADE,KACS,CAAC,IAEf,CAAE3X,IAAK2X,SAAOC,WAAQC,CAAS,CACxC,CAEA,SAAS0R,EACPnvB,CAAa,CACbovB,CAAsB,CACtBC,CAAsB,EAEtB,IAAMjlB,EAAyC,CAAC,EAC5CklB,EAAa,EAEXhvB,EAAqB,EAAE,CAC7B,IAAK,IAAMF,IAAW+b,CAAAA,EAAAA,EAAAA,mBAAAA,EAAoBnc,GAAOqE,KAAK,CAAC,GAAG3E,KAAK,CAAC,KAAM,CACpE,IAAM6vB,EAAcxB,EAAAA,0BAA0B,CAACC,IAAI,CAAC,GAClD5tB,EAAQyH,UAAU,CAAC0K,IAEfid,EAAepvB,EAAQke,KAAK,CAAC2Q,GAEnC,GAAIM,GAAeC,GAAgBA,CAAY,CAAC,EAAE,CAAE,CAClD,CAHoD,EAG9C,KAAE5pB,CAAG,UAAE6X,CAAQ,IAHsD,IAGpDD,CAAM,CAAE,CAAG0R,EAAsBM,CAAY,CAAC,EAAE,EACvEplB,CAAM,CAACxE,EAAI,CAAG,CAAE6pB,IAAKH,WAAc9R,WAAQC,CAAS,EACpDnd,EAAS+G,IAAI,CAAE,IAAGqoB,CAAAA,EAAAA,EAAAA,kBAAAA,EAAmBH,GAAa,WACpD,MAAO,GAAIC,GAAgBA,CAAY,CAAC,EAAE,CAAE,CAC1C,GAAM,KAAE5pB,CAAG,QAAE4X,CAAM,UAAEC,CAAQ,CAAE,CAAGyR,EAAsBM,CAAY,CAAC,EAAE,EACvEplB,CAAM,CAACxE,EAAI,CAAG,CAAE6pB,IAAKH,WAAc9R,WAAQC,CAAS,EAEhD4R,GAAiBG,CAAY,CAAC,EAAE,EAAE,EAC3BnoB,IAAI,CAAE,IAAGqoB,GAAAA,EAAAA,kBAAAA,EAAmBF,CAAY,CAAC,EAAE,GAGtD,IAAIzZ,EAAIyH,EAAUC,EAAW,cAAgB,SAAY,YAGrD4R,GAAiBG,CAAY,CAAC,EAAE,EAAE,CACpCzZ,EAAIA,EAAEnS,SAAS,CAAC,IAGlBtD,EAAS+G,IAAI,CAAC0O,EAChB,MACEzV,CADK,CACI+G,IAAI,CAAE,IAAGqoB,CAAAA,EAAAA,EAAAA,kBAAAA,EAAmBtvB,IAInCgvB,GAAiBI,GAAgBA,CAAY,CAAC,EAAE,EAClDlvB,EAAS+G,IAAI,CAACqoB,CAAAA,EAAAA,EAAAA,kBAAAA,EAAmBF,CAAY,CAAC,EAAE,EAEpD,CAEA,MAAO,CACLG,mBAAoBrvB,EAASgE,IAAI,CAAC,IAClC8F,QACF,CACF,CAOO,SAASD,EACdylB,CAAuB,CACvB,qBACER,GAAgB,CAAK,eACrBC,GAAgB,CAAK,8BACrBQ,GAA+B,CAAK,CACf,CAJvB,WAI0B,CAAC,EAJ3B,EAMM,oBAAEF,CAAkB,QAAEvlB,CAAM,CAAE,CAAG+kB,EACrCS,EACAR,EACAC,GAGES,EAAKH,EAKT,OAJI,IACFG,GAAM,UAGD,CACLA,GAAI,OAAY,EALiB,EAKdA,EAAG,KACtB1lB,OAAQA,CACV,CACF,CAoBA,SAAS2lB,EAAsB,CAc9B,EAd8B,IAsDzBC,EAtDyB,oBAC7BC,CAAkB,iBAClBC,CAAe,SACf9vB,CAAO,CACP+vB,WAAS,WACTC,CAAS,CACTC,4BAA0B,CAQ3B,CAd8B,EAevB,KAAEzqB,CAAG,UAAE6X,CAAQ,QAAED,CAAM,CAAE,CAAG0R,EAAsB9uB,GAIpDkwB,EAAa1qB,EAAIlF,OAAO,CAAC,MAAO,IAEhC0vB,IACFE,EAAc,GAAEF,EADH,CACeE,EAE9B,IAAIC,GAAa,GAIS,IAAtBD,EAAW9vB,MAAM,EAAU8vB,EAAW9vB,MAAM,CAAG,KAAI,IACxC,GAEX,MAAOgwB,SAASF,EAAWjsB,KAAK,CAAC,EAAG,MAAM,CAC5CksB,GAAa,GAGXA,IACFD,EAAaJ,GAAAA,EAGf,CAJgB,GAIVO,EAAeH,KAAcH,EAE/BC,EACFD,CAAS,CAACG,EAAW,CAAI,GAAEF,CADd,CAC0BxqB,EAEvCuqB,CAAS,CAACG,EAAW,CAAG1qB,EAM1B,IAAM8qB,EAAqBT,EACvBP,CAAAA,EAAAA,EAAAA,kBAAAA,EAAmBO,GACnB,GAaJ,OAPED,EAHES,GAAgBJ,EAGP,OAAMC,EAAW,IACnB9S,EACE,MADM,EACU,GALmB,KAOnC,MAAK8S,EAAW,WAGtB7S,EACF,OAAMiT,EAAqBV,EAAQ,KACnC,IAAGU,EAAqBV,CAC/B,CAEA,SAASW,EACP3wB,CAAa,CACb4wB,CAAwB,CACxBxB,CAAsB,CACtBC,CAAsB,CACtBgB,CAAmC,EAEnC,MAAMH,GA1FFpnB,EAAI,EAED,KACL,IAAI+nB,EAAW,GACXnkB,EAAI,EAAE5D,EACV,KAAO4D,EAAI,EAAG,CACZmkB,GAAYliB,OAAOmiB,YAAY,CAAC,GAAOpkB,CAAAA,EAAAA,CAAAA,CAAI,CAAK,IAChDA,EAAI8L,KAAKC,KAAK,CAAE/L,GAAAA,CAAAA,CAAI,CAAK,IAE3B,OAAOmkB,CACT,GAiFMV,EAAyC,CAAC,EAE1C7vB,EAAqB,EAAE,CAC7B,IAAK,IAAMF,IAAW+b,GAAAA,EAAAA,mBAAAA,EAAoBnc,GAAOqE,KAAK,CAAC,GAAG3E,KAAK,CAAC,KAAM,CACpE,IAAMqxB,EAAwBhD,EAAAA,0BAA0B,CAACiD,IAAI,CAAC,GAC5D5wB,EAAQyH,UAAU,CAAC0K,IAGfid,EAAepvB,EAAQke,KAAK,CAAC2Q,GAEnC,GAAI8B,GAAyBvB,GAAgBA,CAAY,CAAC,EAAE,CAE1DlvB,CAF4D,CAFR,IAIvC,CACXyvB,EAAsB,CACpBG,eANuE,GAOvED,mBAAoBT,CAAY,CAAC,EAAE,CACnCpvB,QAASovB,CAAY,CAAC,EAAE,WACxBW,EACAC,UAAWQ,EACPK,EAAAA,+BAA+B,MAC/BvnB,6BACJ2mB,CACF,SAEG,GAAIb,GAAgBA,CAAY,CAAC,EAAE,CAAE,CAEtCH,GAAiBG,CAAY,CAAC,EAAE,EAAE,EAC3BnoB,IAAI,CAAE,IAAGqoB,CAAAA,EAAAA,EAAAA,kBAAAA,EAAmBF,CAAY,CAAC,EAAE,GAGtD,IAAIzZ,EAAIga,EAAsB,iBAC5BG,EACA9vB,QAASovB,CAAY,CAAC,EAAE,WACxBW,EACAC,UAAWQ,EAAkBM,EAAAA,uBAAuB,MAAGxnB,6BACvD2mB,CACF,GAGIhB,GAAiBG,CAAY,CAAC,EAAE,EAAE,CACpCzZ,EAAIA,EAAEnS,SAAS,CAAC,IAGlBtD,EAAS+G,IAAI,CAAC0O,EAChB,MACEzV,CADK,CACI+G,IAAI,CAAE,IAAGqoB,CAAAA,EAAAA,EAAAA,kBAAAA,EAAmBtvB,IAInCgvB,GAAiBI,GAAgBA,CAAY,CAAC,EAAE,EAAE,EAC3CnoB,IAAI,CAACqoB,CAAAA,EAAAA,EAAAA,kBAAAA,EAAmBF,CAAY,CAAC,EAAE,EAEpD,CAEA,MAAO,CACL2B,wBAAyB7wB,EAASgE,IAAI,CAAC,cACvC6rB,CACF,CACF,CAUO,SAASpB,EACda,CAAuB,CACvB3yB,CAAkC,MAKhCA,EACAA,EACAA,EALF,IAAMiG,EAASytB,EACbf,EACA3yB,EAAQ2zB,eAAe,CACvB3zB,OAAAA,EAAAA,EAAQmyB,aAAAA,GAARnyB,EACAA,MAAAA,CAAAA,EAAAA,EAAQoyB,YADiB,CACjBA,GAARpyB,EACAA,OAAAA,EAAAA,EAAQozB,YADiB,cACjBA,GAARpzB,GAGEm0B,EAAaluB,EAAOiuB,uBAAuB,CAK/C,OARwC,EAI3BtB,4BAA4B,EAAE,IAC3B,UAGT,CACL,GAAG1lB,EAAcylB,EAAiB3yB,EAAQ,CAC1Cm0B,WAAa,IAAGA,EAAW,IAC3BjB,UAAWjtB,EAAOitB,SAAS,CAE/B,CAMO,SAASrB,EACdc,CAAuB,CACvB3yB,CAEC,EAED,GAAM,oBAAE0yB,CAAkB,CAAE,CAAGR,EAC7BS,GACA,EACA,IAEI,UAAEyB,GAAW,CAAI,CAAE,CAAGp0B,EAC5B,GAA2B,KAAK,CAA5B0yB,EAEF,MAAO,CACLyB,WAAa,MAFKC,CAEDC,CAFY,KAAO,IAEL,GACjC,EAGF,GAAM,yBAAEH,CAAuB,CAAE,CAAGR,EAClCf,GACA,GACA,EACA,IACA,GAGF,MAAO,CACLwB,WAAa,IAAGD,GAFSE,EAAW,aAAe,IAEY,GACjE,CAD4CE,4HCxZ9BphB,qCAAAA,aA3BkB,UACK,OA0BhC,SAASA,EACd1P,CAAW,CACXyd,CAAa,CACbsT,CAAiB,EAAjBA,KAAAA,QAAAA,EAAa,IAEb,IAAMC,EAAa,IAAI7mB,IACrB,CAA6CjI,EAAAA,EAAAA,CAAhB,GAAG,CAAU,YAAGA,KAGzC+uB,EAAexT,EACjB,IAAItT,IAAIsT,EAAMuT,GACdhxB,EAAIoH,UAAU,CAAC,KACb,IAAI+C,IACF,MAA6B,CAAuBnH,EAApB,CAAU,KAAkB,CAACC,IAAI,EAEnE+tB,EAEA,UAAEtxB,CAAQ,cAAEqZ,CAAY,QAAE/K,CAAM,MAAEW,CAAI,MAAE1L,CAAI,QAAEC,CAAM,CAAE,CAAG,IAAIiH,IACjEnK,EACAixB,GAGF,GAAI/tB,IAAW8tB,EAAW9tB,MAAM,CAC9B,CADgC,KAC1B,qBAAoE,CAApE,MAAW,oDAAmDlD,GAA9D,+DAAmE,GAG3E,MAAO,UACLN,EACA8J,MAAOunB,EAAa5S,GAAAA,EAAAA,sBAAAA,EAAuBpF,QAAgB9P,SAC3D+E,OACAW,EACA1L,KAAMA,EAAKW,KAAK,CAACV,EAAOnD,MAAM,CAChC,CACF,kIClDgBmxB,qCAAAA,aAVoB,WACN,WACA,WACJ,OAOnB,SAASA,EAAuBlmB,CAAkB,EACvD,IAAItL,EAAWxC,CAAAA,EAAAA,EAAAA,SAAAA,EACb8N,EAAKtL,QAAQ,CACbsL,EAAKU,MAAM,CACXV,EAAKI,OAAO,MAAGnC,EAAY+B,EAAK6B,aAAa,CAC7C7B,EAAKuX,YAAY,EAenB,OAZIvX,EAAKI,OAAO,EAAI,CAACJ,EAAKF,aAAa,EAAE,EACvCpL,EAAWgc,GAAAA,EAAAA,mBAAAA,EAAoBhc,EAAAA,EAG7BsL,EAAKI,OAAO,EAAE,CAChB1L,EAAWyxB,CAAAA,EAAAA,EAAAA,aAAAA,EACThW,GAAAA,EAAAA,aAAAA,EAAczb,EAAW,eAAcsL,EAAKI,OAAO,EACjC,MAAlBJ,EAAKtL,QAAQ,CAAW,aAAe,UAI3CA,EAAWyb,CAAAA,EAAAA,EAAAA,aAAAA,EAAczb,EAAUsL,EAAKR,QAAQ,EACzC,CAACQ,EAAKI,OAAO,EAAIJ,EAAKF,aAAa,CACtC,EAAUzD,QAAQ,CAAC,KAEjB3H,EADAyxB,CAAAA,EAAAA,EAAAA,aAAAA,EAAczxB,EAAU,KAE1Bgc,CAAAA,EAAAA,EAAAA,mBAAAA,EAAoBhc,EAC1B,iIC3BgBa,qCAAAA,4BATE,gBACE,YACc,OAE5B6wB,EAAiC,2BACjCC,EAA2CC,OAC9C,OAAMF,EAA+B,OAAMA,EAA+B,QAGtE,SAAS7wB,EAAmCiO,CAAM,EACvD,IAAM+iB,EAAkBlxB,CAAAA,EAAAA,EAAAA,OAAAA,EAAQmO,GAC1BgjB,EAAcD,GAAkB/iB,EAAI5J,KAAK,EAAI,GAC7C6sB,EADkD,EAChBjjB,EAAI/J,OAAO,CAAG,GAChDitB,EAAaF,EAAYvyB,KAAK,CAAC,MAC/B0yB,EAAeD,EAAWE,SAAS,CAAC,GACxCP,EAAqC9vB,IAAI,CAACswB,IAGxCC,EADyBH,GAAgB,EAAE,EAEhC/tB,KAAK,CAAC,EAAG+tB,GAAc9tB,IAAI,CAAC,MACvC2tB,EAEEO,EAAW,OALiE,cAKzC,CAAxB,MAAUN,GAAV,+DAAuB,GASxC,OAPA9yB,OAAOwP,MAAM,CAAC4jB,EAAUvjB,GACxBwjB,CAAAA,EAAAA,EAAAA,iBAAAA,EAAkBxjB,EAAKujB,GACvBA,EAASntB,KAAK,CAAGktB,EAGjBG,SAKOA,CAA6B,EACpC,GAAI,CAACr0B,EAAAA,OAAK,CAACs0B,iBAAiB,CAC1B,CAD4B,MAG9B,IAAIttB,EAAQ/F,EAAM+F,KAAK,EAAI,GAErButB,EAAav0B,EAAAA,OAAK,CAACs0B,iBAAiB,GAEtCC,IAA6C,IAA/BvtB,EAAMyC,CAAgC,OAAxB,CAAC8qB,KAG/BtzB,EAAM+F,KAAK,CAFXA,EAEcA,CAFLutB,CAEKvtB,CAElB,EAlBmBmtB,GAEVA,CACT,kVC5BgB9C,qCAAAA,KAHhB,IAAMmD,EAAc,sBACdC,EAAkB,uBAEjB,SAASpD,EAAmBqD,CAAW,SAE5C,EAAgB/wB,IAAI,CAAC+wB,GACZA,EAAIryB,CADc,MACP,CAACoyB,EAAiB,QAE/BC,CACT,qKCMaC,kBAAkB,mBAAlBA,GAhBA1M,mBAAmB,mBAAnBA,KAAN,IAAMA,EACM,aAAhB,OAAOhlB,MACNA,KAAKglB,mBAAmB,EACxBhlB,KAAKglB,mBAAmB,CAAC2M,IAAI,CAACzvB,SAChC,SAAU0c,CAAuB,EAC/B,IAAIgT,EAAQ5a,KAAKC,GAAG,GACpB,OAAOjX,KAAKiO,UAAU,CAAC,WACrB2Q,EAAG,CACDiT,YAAY,EACZC,cAAe,WACb,OAAO5a,KAAK6a,GAAG,CAAC,EAAG,IAAM/a,CAAAA,IAAKC,GAAG,GAAK2a,CAAAA,CAAAA,CACxC,CACF,EACF,EAAG,EACL,EAEWF,EACM,oBAAT1xB,MACNA,KAAK0xB,kBAAkB,EACvB1xB,KAAK0xB,kBAAkB,CAACC,IAAI,CAACzvB,SAC/B,SAAUuU,CAAU,EAClB,OAAOub,aAAavb,EACtB,8XCklBcwb,SAAS,mBAATA,6BAiDKzU,GA9jBC0U,iBAAiB,mBAAjBA,mCAjFc,WAK7B,WACgC,eACC,YACJ,WACA,eACnB,YACkD,UACpC,UACE,SACL,eACI,WACF,WACO,WACF,WACT,WACA,UACG,WACE,WACH,WACA,WACA,WACD,UACS,WACG,WACH,WACT,WACL,WACD,WACS,WACK,SAEC,OAgCpC,SAASC,IACP,OAAOr0B,OAAOwP,MAAM,CAAC,qBAA4B,CAA5B,MAAU,mBAAV,+DAA2B,GAAG,CACjDM,WAAW,CACb,EACF,CASO,eAAeskB,EACpBv2B,CAAkC,EAElC,IAAMy2B,EAAW,MAAMhhB,QAAQC,OAAO,CACpC1V,EAAQ2M,MAAM,CAACkD,UAAU,CAAC6T,aAAa,IAEzC,GAAI,CAAC+S,EAAU,OAAO,EAEtB,GAAM,CAAEvzB,SAAU6c,CAAU,CAAE,CAAGjB,CAAAA,EAAAA,EAAAA,SAAAA,EAAU9e,EAAQ8M,MAAM,EAEnD4pB,EAAY5oB,CAAAA,EAAAA,EAAAA,WAAAA,EAAYiS,GAC1B/M,CAAAA,EAAAA,EAAAA,cAAAA,EAAe+M,GACfA,EACE4W,EAA0BlY,CAAAA,EAAAA,EAAAA,WAAAA,EAC9B/d,CAAAA,EAAAA,EAAAA,SAAAA,EAAUg2B,EAAW12B,EAAQkP,MAAM,GAKrC,OAAOunB,EAAS1C,IAAI,CAAC,GACnB,IAAIe,OAAOxf,EAAEshB,MAAM,EAAE7xB,IAAI,CAAC4xB,GAE9B,CAEA,SAASE,EAAYrzB,CAAW,EAC9B,IAAMkD,EAAShB,CAAAA,EAAAA,EAAAA,iBAAAA,IAEf,OAAOlC,EAAIoH,UAAU,CAAClE,GAAUlD,EAAImD,SAAS,CAACD,EAAOnD,MAAM,EAAIC,CACjE,CAEA,SAASszB,EAAanqB,CAAkB,CAAEnJ,CAAQ,CAAEmoB,CAAQ,EAG1D,GAAI,CAAC/J,EAAcmV,EAAW,CAAGhW,CAAAA,EAAAA,EAAAA,WAAAA,EAAYpU,EAAQnJ,GAAK,GACpDkD,EAAShB,CAAAA,EAAAA,EAAAA,iBAAAA,IACTsxB,EAAkBpV,EAAahX,UAAU,CAAClE,GAC1CuwB,EAAgBF,GAAcA,EAAWnsB,UAAU,CAAClE,GAE1Dkb,EAAeiV,EAAYjV,GAC3BmV,EAAaA,EAAaF,EAAYE,GAAcA,EAEpD,IAAMG,EAAcF,EAAkBpV,EAAenD,CAAAA,EAAAA,EAAAA,WAAAA,EAAYmD,GAC3DuV,EAAaxL,EACfkL,EAAY9V,CAAAA,EAAAA,EAAAA,WAAAA,EAAYpU,EAAQgf,IAChCoL,GAAcnV,EAElB,MAAO,CACLpe,IAAK0zB,EACLvL,GAAIsL,EAAgBE,EAAa1Y,GAAAA,EAAAA,WAAAA,EAAY0Y,EAC/C,CACF,CAEA,SAASC,EAAoBl0B,CAAgB,CAAEm0B,CAAe,EAC5D,IAAMC,EAAgBpY,CAAAA,EAAAA,EAAAA,mBAAAA,EAAoBC,CAAAA,EAAAA,EAAAA,mBAAAA,EAAoBjc,UACxC,SAAlBo0B,GAA8C,WAAW,CAA7BA,EACvBp0B,GAIL,EAAOwF,QAAQ,CAAC4uB,IAElBD,EAAMtD,IAAI,CAAC,IACT,CAHgC,EAG5BvvB,CAAAA,EAAAA,EAAAA,cAAAA,EAAeuD,IAASmF,CAAAA,EAAAA,EAAAA,aAAAA,EAAcnF,GAAM8qB,EAAE,CAAC9tB,IAAI,CAACuyB,GAEtD,OADAp0B,EAAW6E,GACJ,CAF+D,GAMrEmX,CAAAA,EAAAA,EAAAA,mBAAAA,EAAoBhc,GAC7B,CA+JA,eAAeq0B,EACbv3B,CAAkC,EAGlC,GAAI,CADY,MAAMu2B,EAAkBv2B,IACxB,CAACA,EAAQw3B,SAAS,CAChC,CADkC,MAC3B,KAGT,IAAMC,EAAO,MAAMz3B,EAAQw3B,SAAS,GAE9BE,EAAS,MAAMC,SAtKrB1a,CAAc,CACd2a,CAAkB,CAClB53B,CAAkC,EAElC,IAAMuO,EAAa,CACjBP,SAAUhO,EAAQ2M,MAAM,CAACqB,QAAQ,CACjCK,KAAM,CAAEY,QAASjP,EAAQ2M,MAAM,CAACsC,OAAO,EACvCX,eAAenF,CACjB,EACM0uB,EAFoD,EAE3BC,OAAO,CAAC9tB,GAAG,CAAC,oBAEvC+tB,EACFF,GAAiBD,EAASE,OAAO,CAAC9tB,GAAG,CAAC,yBAElCguB,EAAcJ,EAASE,OAAO,CAAC9tB,GAAG,CAACiuB,EAAAA,mBAAmB,EAa5D,IAVED,GACCD,GACAC,EAAYtvB,OADb,CACqB,CAAC,GAAtB,sBACCsvB,EAAD,QAAqB,CAAC,YACrBA,EAAD,QAAqB,CAAC,SACtB,CAEAD,EAAgBC,CAAAA,EAGdD,EAAe,CACjB,GACEA,EAAcntB,UAAU,CAAC,KAEzB,CACA,EAFA8F,EAEMwnB,EAAsBhlB,CAF0B,EAE1BA,EAAAA,gBAAAA,EAAiB6kB,GACvCI,EAAe/pB,CAAAA,EAAAA,EAAAA,mBAAAA,EAAoB8pB,EAAoBh1B,QAAQ,CAAE,YACrEqL,EACAM,WAAW,CACb,GAEIupB,EAAalZ,CAAAA,EAAAA,EAAAA,mBAAAA,EAAoBiZ,EAAaj1B,QAAQ,EAC1D,OAAOuS,QAAQwV,GAAG,CAAC,CACjBjrB,EAAQ2M,MAAM,CAACkD,UAAU,CAACyT,WAAW,GACrCC,CAAAA,EAAAA,EAAAA,sBAAAA,IACD,EAAEtO,IAAI,CAAC,OAAC,CAACoiB,EAAO,CAAEgB,WAAYC,CAAQ,CAAE,CAAM,GACzC3M,EAAKjrB,CAAAA,EAAAA,EAAAA,SAAAA,EAAUy3B,EAAaj1B,QAAQ,CAAEi1B,EAAajpB,MAAM,EAE7D,GACE1K,CAAAA,EAAAA,EAAAA,cAAAA,EAAemnB,IACd,CAACkM,GACAR,EAAM3uB,QAAQ,CACZsG,CAAAA,EAAAA,EAAAA,mBAAAA,EAAoBgE,CAAAA,EAAAA,EAAAA,cAAAA,EAAe2Y,GAAK3rB,EAAQ2M,MAAM,CAACsC,OAAO,EAC3D/L,QAAQ,EAEf,CACA,IAAMq1B,EAAenqB,CAAAA,EAAAA,EAAAA,mBAAAA,EACnB8E,CAAAA,EAAAA,EAAAA,gBAAAA,EAAiB+J,GAAQ/Z,QAAQ,CACjC,CACEqL,WAEIA,CAFQmC,CAGZ7B,KAH2C,GACvCpC,CAASA,EAEF,CACb,GAIFyrB,EAAoBh1B,QAAQ,CAD5ByoB,EAAKlN,CAAAA,EAAAA,EAAAA,WAAAA,EAAY8Z,EAAar1B,QAAQ,CAExC,CAiBO,GAAI,CAACm0B,EAAM3uB,QAAQ,CAAC0vB,GAAa,CACtC,IAAMI,EAAmBpB,EAAoBgB,EAAYf,GAErDmB,IAAqBJ,IACvBA,EAAaI,CAAAA,CAEjB,CAEA,GALuC,CAKjC5W,EAAgByV,EAAM3uB,QAAQ,CAAC0vB,GAQjCA,EAPAhB,EACEpoB,GAAAA,EAAAA,mBAAAA,EACEgE,CAAAA,EAAAA,EAAAA,cAAAA,EAAeklB,EAAoBh1B,QAAQ,EAC3ClD,EAAQ2M,MAAM,CAACsC,OAAO,EACtB/L,QAAQ,CACVm0B,GAIN,GAAI7yB,CAAAA,EAAAA,EAAAA,cAAAA,EAAeod,GAAe,CAChC,IAAM6W,EAAUrY,CAAAA,EAAAA,EAAAA,eAAAA,EAAgBlT,GAAAA,EAAAA,aAAAA,EAAc0U,IAAe+J,GAC7DxpB,OAAOwP,MAAM,CAACumB,EAAoBlrB,KAAK,CAAEyrB,GAAW,CAAC,EACvD,CAEA,MAAO,CACL9e,KAAM,UACNvG,SAAU8kB,eACVtW,CACF,CACF,EACF,CACA,IAAMuI,EAAMrL,CAAAA,EAAAA,EAAAA,SAAAA,EAAU7B,GAOtB,OAAOxH,QAAQC,OAAO,CAAC,CACrBiE,KAAM,oBACN+W,YAAc,GARCgE,CAAAA,CAQCxxB,CARDwxB,EAAAA,sBAAAA,EAAuB,CACtC,GAAGtmB,GAAAA,EAAAA,mBAAAA,EAAoB+b,EAAIjnB,QAAQ,CAAE,YAAEqL,EAAYM,WAAW,CAAK,EAAE,CACrEwB,cAAerQ,EAAQ2M,MAAM,CAAC0D,aAAa,CAC3CzB,QAAS,EACX,GAI6Bub,EAAInd,KAAK,CAAGmd,EAAIhY,IAC7C,EACF,CAEA,IAAMumB,EAAiBd,EAASE,OAAO,CAAC9tB,GAAG,CAAC,qBAE5C,GAAI0uB,EAAgB,CAClB,GAAIA,EAAe9tB,UAAU,CAAC,KAAM,CAClC,IAAMuf,EAAMrL,CAAAA,EAAAA,EAAAA,SAAAA,EAAU4Z,GAChBx1B,EAAWwxB,CAAAA,EAAAA,EAAAA,sBAAAA,EAAuB,CACtC,GAAGtmB,CAAAA,EAAAA,EAAAA,mBAAAA,EAAoB+b,EAAIjnB,QAAQ,CAAE,YAAEqL,EAAYM,WAAW,CAAK,EAAE,CACrEwB,cAAerQ,EAAQ2M,MAAM,CAAC0D,aAAa,CAC3CzB,QAAS,EACX,GAEA,OAAO6G,QAAQC,OAAO,CAAC,CACrBiE,KAAM,oBACNgf,MAAQ,GAAEz1B,EAAWinB,EAAInd,KAAK,CAAGmd,EAAIhY,IAAI,CACzCymB,OAAS,GAAE11B,EAAWinB,EAAInd,KAAK,CAAGmd,EAAIhY,IAAI,EAE9C,CAEA,OAAOsD,QAAQC,OAAO,CAAC,CACrBiE,KAAM,oBACN+W,YAAagI,CACf,EACF,CAEA,OAAOjjB,QAAQC,OAAO,CAAC,CAAEiE,KAAM,MAAgB,EACjD,EAgByC8d,EAAKoB,QAAQ,CAAEpB,EAAKG,QAAQ,CAAE53B,GAErE,MAAO,CACL64B,SAAUpB,EAAKoB,QAAQ,CACvBC,KAAMrB,EAAKqB,IAAI,CACflB,SAAUH,EAAKG,QAAQ,CACvBxf,KAAMqf,EAAKrf,IAAI,CACf2gB,SAAUtB,EAAKsB,QAAQ,CACvBrB,QACF,CACF,CAqFA,IAAMsB,EAAqBvQ,OAAO,sBAmDlC,SAASwQ,EAAiB7gB,CAAY,EACpC,GAAI,CACF,OAAOlQ,KAAKwK,KAAK,CAAC0F,EACpB,CAAE,MAAO/V,EAAO,CACd,OAAO,IACT,CACF,CAEA,SAAS62B,EAAc,CAUD,EAVC,aACrBL,CAAQ,eACRM,CAAa,YACbC,CAAU,CACVC,eAAa,gBACbC,CAAc,WACdC,CAAS,cACTC,CAAY,cACZC,CAAY,0BACZC,CAAwB,CACJ,CAVC,EAWf,CAAEjzB,KAAMsyB,CAAQ,CAAE,CAAG,IAAIprB,IAAIkrB,EAAUtyB,OAAOC,QAAQ,CAACC,IAAI,EAC3DkzB,EAAU,QAUJtZ,QATVuZ,CAtEJ,SAASA,EACPp2B,CAAW,CACXq2B,CAAgB,CAChB75B,CAAgD,EAEhD,OAAO2qB,MAAMnnB,EAAK,CAYhBonB,YAAa,cACbhjB,OAAQ5H,EAAQ4H,MAAM,EAAI,MAC1BkwB,QAAS31B,OAAOwP,MAAM,CAAC,CAAC,EAAG3R,EAAQ83B,OAAO,CAAE,CAC1C,gBAAiB,GACnB,EACF,GAAG7iB,IAAI,CAAC,GACC,CAAC2iB,EAAS/M,EAAE,EAAIgP,EAAW,GAAKjC,EAASh1B,MAAM,EAAI,IACtDg3B,EAAWp2B,EAAKq2B,EAAW,EAAG75B,GAC9B43B,GAER,EA2CeiB,EAAUS,EAAiB,EAAI,EAAG,CAC3CxB,QAAS31B,OAAOwP,MAAM,CACpB,CAAC,EACDynB,EAAa,CAAEU,QAAS,UAAW,EAAI,CAAC,EACxCV,GAAcC,EAAgB,CAAE,wBAAyB,GAAI,EAAI,CAAC,EAG9D,CAFJ3oB,GAIF9I,GAJgC,GAC1B,CAAqD,MAGnDyY,CAAAA,EAAAA,MAAAA,EAAAA,KAAAA,EAAAA,EAAQzY,MAAAA,EAARyY,EAAkB,KAC5B,GACGpL,IAAI,CAAC,GACJ,EAAa4V,EAAE,EAAIxK,CAAAA,MAAAA,EAAAA,KAAAA,EAAAA,EAAQzY,MAAAA,IAAW,OAC7B,CADqC,SACnCixB,WAAUjB,EAAUxf,KAAM,GAAI0gB,KAAM,CAAC,WAAGC,CAAS,EAGrDnB,EAASxf,IAAI,GAAGnD,IAAI,CAAC,IAC1B,GAAI,CAAC2iB,EAAS/M,EAAE,CAAE,CAOhB,GACEwO,GACA,CAAC,IAAK,IAAK,IAAK,IAAI,CAAC3wB,QAAQ,CAACkvB,EAASh1B,MAAM,EAE7C,CADA,KACO,UAAEi2B,WAAUjB,OAAUxf,EAAM0gB,KAAM,CAAC,WAAGC,CAAS,EAGxD,GAAwB,MAApBnB,EAASh1B,MAAM,CAAU,KACvBq2B,EAAJ,YAAIA,EAAiB7gB,EAAAA,CAAAA,CAAAA,KAAAA,EAAjB6gB,EAAwBc,QAAQ,CAClC,CADoC,KAC7B,UACLlB,EACAC,KAAM,CAAEiB,SAAUf,CAAmB,WACrCpB,OACAxf,WACA2gB,CACF,CAEJ,CAEA,IAAM12B,EAAQ,qBAAwC,CAAxC,MAAW,+BAAX,+DAAuC,EAWrD,OAJI,GACF4lB,CAAAA,EAAAA,EAAAA,QADmB,MACnBA,EAAe5lB,GAGXA,CACR,CAEA,MAAO,UACLw2B,EACAC,KAAMS,EAAYN,EAAiB7gB,GAAQ,cAC3Cwf,OACAxf,WACA2gB,CACF,CACF,IAED9jB,IAAI,CAAC,IAEF,GAEoD,YACpD,CADAwiB,EAAKG,MADGjnB,EACK,CAACmnB,CADFkC,KAAa,CACJ,CAAChwB,CADF,EACK,CAAC,uBAE1B,OAAOmvB,CAAa,CAACJ,EAAS,CAEzBtB,IAER1lB,KAAK,CAAC,IAcL,MAbI,GACF,OAAOonB,CAAa,CAACJ,EAAS,EAId,UALa,UAI7B,CAEA,CADI9wB,KADK,EACE,EAEK,oDAAhB+J,CACA,CADI/J,OAAO,CACF,CACT+J,kBAAI/J,OAAO,GACX,CACAggB,EAAAA,EAAAA,cAAAA,EAAejW,GAEXA,CACR,WAMJ,GAAgCwnB,EACvBG,EAAQ,CAAC,GAAG1kB,IAAI,CAAEwiB,CADmB,GAEc,YAAY,CAAhEA,EAAKG,QAAQ,CAACE,OAAO,CAAC9tB,GAAG,CAAC,wBAE5BmvB,CAAa,CAACJ,EAAS,CAAGtjB,QAAQC,OAAO,CAAC+hB,EAAAA,EAGrCA,SAIqBhrB,IAA5B0sB,CAAa,CAACJ,EAAS,CAClBI,CAAa,CAACJ,EAAS,CAExBI,CAAa,CAACJ,EAAS,CAAGY,EAChCF,EAAe,CAAE7xB,OAAQ,MAAO,EAAI,CAAC,EAEzC,CAMO,SAAS0uB,IACd,OAAO/a,KAAKE,MAAM,GAAGmK,QAAQ,CAAC,IAAIxe,KAAK,CAAC,EAAG,GAC7C,CAEA,SAAS6yB,EAAqB,CAM7B,EAN6B,QAC5Bz2B,CAAG,QACHmJ,CAAM,CAIP,CAN6B,EAS5B,GAAInJ,IAAQib,CAAAA,EAAAA,EAAAA,WAAAA,EAAY/d,CAAAA,EAAAA,EAAAA,SAAAA,EAAUiM,EAAOG,MAAM,CAAEH,EAAOuC,MAAM,GAC5D,CADgE,KAC1D,qBAEL,CAFK,MACH,yDAAwD1L,EAAI,IAAGgD,SAASC,IAAI,EADzE,+DAEN,EAEFF,QAAOC,QAAQ,CAACC,IAAI,CAAGjD,CACzB,CAEA,IAAM02B,EAAsB,OAAC,OAC3Bn3B,CAAK,QACL4J,CAAM,CAIP,GACKsF,GAAY,EACVkoB,EAAUxtB,EAAOytB,GAAG,CAAG,KAC3BnoB,GAAY,CACd,EAeA,MAbwB,CAajBooB,IAZL,GAAIpoB,EAAW,CACb,IAAM5P,EAAa,qBAElB,CAFkB,MAChB,wCAAuCU,EAAM,KAD7B,+DAEnB,EAEA,OADAV,EAAM4P,SAAS,EAAG,EACZ5P,CACR,CAEI83B,IAAWxtB,EAAOytB,GAAG,EAAE,CACzBztB,EAAOytB,GAAG,CAAG,KAEjB,CAEF,CAEe,OAAMvY,EA+SnBvV,QAAe,CACb/F,OAAOC,QAAQ,CAAC8F,MAAM,EACxB,CAKAH,MAAO,CACL5F,OAAO+zB,OAAO,CAACnuB,IAAI,EACrB,CAKAC,SAAU,CACR7F,OAAO+zB,OAAO,CAACluB,OAAO,EACxB,CAQAhC,KAAK5G,CAAQ,CAAEmoB,CAAQ,CAAE3rB,CAA+B,CAAE,QAAjCA,KAAAA,IAAAA,IAAAA,EAA6B,EAAC,EAcnD,KAAEwD,CAAG,IAAEmoB,CAAE,CAAE,CAAGmL,EAAa,IAAI,CAAEtzB,EAAKmoB,GAAAA,CAAE,GAC/B,CAAC4O,MAAM,CAAC,YAAa/2B,EAAKmoB,EAAI3rB,EAC3C,CAQAyD,QAAQD,CAAQ,CAAEmoB,CAAQ,CAAE3rB,CAA+B,CAAE,CAE3D,OAF0BA,KAAAA,IAAAA,IAAAA,EAA6B,EAAC,EACtD,CAAEwD,KAAG,IAAEmoB,CAAE,CAAE,CAAGmL,EAAa,IAAI,CAAEtzB,EAAKmoB,GAAAA,CAAE,GAC/B,CAAC4O,MAAM,CAAC,eAAgB/2B,EAAKmoB,EAAI3rB,EAC9C,CAEA,MAAMw6B,KACJ7O,CAAU,CACVoL,CAAmB,CACnB7nB,CAAuB,CACvBurB,CAAsB,CACtB,CACqD,CACnD,GAAI,CAAC,IAAI,CAACC,MAAM,EAAI,CAAC,IAAI,CAACC,MAAM,CAAE,CAChC,IAMIC,EACAC,EAPE,aAAEC,CAAW,CAAE,CACnBh6B,EAAQ,KAAwB,EAQlC,GAAI,EACA,CACAi6B,IAVKj6B,iBAUiB85B,CAAgB,CACtCI,sBAAuBH,CAAiB,CACzC,CAAI,MAAMtX,GAAAA,EAAAA,sBAAAA,GAAAA,CAAsB,CAIjC,MAAOvR,EAAK,CAIZ,GADAuC,QAAQlS,KAAK,CAAC2P,GACVyoB,EACF,OAAO,EAQT,GATkB,IAGlBR,EAAqB,CACnBz2B,IAAKib,CAAAA,EAAAA,EAAAA,WAAAA,EACH/d,CAAAA,EAAAA,EAAAA,SAAAA,EAAUirB,EAAIzc,GAAU,IAAI,CAACA,MAAM,CAAE,IAAI,CAACmB,aAAa,GAEzD1D,OAAQ,IAAI,GAEP,IAAI8I,QAAQ,KAAO,EAC5B,EAkBImlB,QAAAA,KAAAA,EAAAA,EAAkBK,SAAAA,EAAW,EAC/B,IAAI,CAACP,MAAM,CAAG,IAAII,EAChBF,EAAiBM,QAAQ,CACzBN,EAAiBO,SAAS,EAE5B,IAAI,CAACT,MAAM,CAACU,MAAM,CAACR,KAGjBC,MAAAA,EAAAA,KAAAA,EAAAA,EAAmBI,SAAAA,EAAW,EAChC,IAAI,CAACN,MAAM,CAAG,IAAIG,EAChBD,EAAkBK,QAAQ,CAC1BL,EAAkBM,SAAS,EAE7B,IAAI,CAACR,MAAM,CAACS,MAAM,CAACP,GAEvB,CAEA,IAAIQ,GAAmB,EACnBC,EAAoB,GAIxB,IAAK,GAAM,CAAE3P,GAAI4P,CAAK,mBAAEC,CAAiB,CAAE,EAFzC,CAAC,CAE4CC,GAF1C9P,CAAG,EAAG,CAAEA,GAAIoL,CAAW,EAAE,CAG5B,GAAIwE,EAAO,CACT,IAAMG,EAAYxc,GAAAA,EAAAA,mBAAAA,EAChB,IAAIvR,IAAI4tB,EAAO,YAAYr4B,QAAQ,EAE/By4B,EAAkBld,CAAAA,EAAAA,EAAAA,WAAAA,EACtB/d,CAAAA,EAAAA,EAAAA,SAAAA,EAAUg7B,EAAWxsB,GAAU,IAAI,CAACA,MAAM,GAG5C,GACEssB,GACAE,IACExc,GAAAA,EAAAA,mBAAAA,EAAoB,IAAIvR,IAAI,IAAI,CAACb,MAAM,CAAE,YAAY5J,QAAQ,EAC/D,KAGI,EACA,EAYmB,EAVvB,IAAK,IAAM04B,KALXP,EACEA,GACA,CAAC,EAAY,MAAX,OAAI,CAACX,MAAAA,EAAM,OAAX,EAAamB,QAAQ,CAACH,EAAAA,CAAAA,EACxB,CAAC,EAAY,MAAZ,GAAC,IAAI,CAAChB,MAAAA,EAAM,OAAX,EAAamB,QAAQ,CAACF,EAAAA,CAAAA,CAEC,CAACD,EAAWC,EAAgB,EAAE,CAGvD,IAAMG,EAAaF,EAAan5B,KAAK,CAAC,KACtC,IACE,IAAIoJ,EAAI,EACR,CAACyvB,GAAqBzvB,EAAIiwB,EAAWv4B,MAAM,CAAG,EAC9CsI,IACA,CACA,IAAMkwB,EAAcD,EAAW10B,KAAK,CAAC,EAAGyE,GAAGxE,IAAI,CAAC,KAChD,GAAI00B,IAAAA,OAAe,IAAfA,EAAmB,CAACpB,MAAAA,EAAM,OAAX,EAAakB,QAAQ,CAACE,EAAAA,CAAAA,CAAc,CACrDT,GAAoB,EACpB,KACF,CACF,CACF,CAIA,GAAID,GAAoBC,EAAmB,CACzC,GAAIb,EACF,OAAO,EAQT,GATkB,IAGlBR,EAAqB,CACnBz2B,IAAKib,CAAAA,EAAAA,EAAAA,WAAAA,EACH/d,CAAAA,EAAAA,EAAAA,SAAS,EAACirB,EAAIzc,GAAU,IAAI,CAACA,MAAM,CAAE,IAAI,CAACmB,aAAa,GAEzD1D,OAAQ,IAAI,GAEP,IAAI8I,QAAQ,KAAO,EAC5B,CACF,CACF,CAEJ,CACA,OAAO,CACT,CAEA,MAAc8kB,OACZ3yB,CAAqB,CACrBpE,CAAW,CACXmoB,CAAU,CACV3rB,CAA0B,CAC1Bg8B,CAAuC,CACrB,KA8Ob,EAjKI,EA4dL33B,EAAAA,EACA43B,EASwCA,EAGxCj8B,EAsCEqE,EAAAA,EACA43B,MAtZF5E,EAAiBiB,EAtMrB,GAAI,CAAC9W,CAAAA,EAAAA,EAAAA,UAAAA,EAAWhe,GAEd,GAFoB,IACpBy2B,EAAqB,KAAEz2B,EAAKmJ,OAAQ,IAAI,IACjC,EAKT,IAAMuvB,EAA0C,IAAvBl8B,EAAgB6R,EAAE,CAEtCqqB,GAAoBl8B,EAAQ8R,OAAO,EAAE,KAAlB,CAChB,IAAI,CAAC0oB,IAAI,CAAC7O,OAAIlf,EAAWzM,EAAQkP,MAAM,EAG/C,IAAIitB,EACFD,GACCl8B,EAAgBo8B,kBAAkB,EACnCtd,CAAAA,EAAAA,EAAAA,SAAAA,EAAUtb,GAAKN,QAAQ,GAAK4b,CAAAA,EAAAA,EAAAA,SAAAA,EAAU6M,GAAIzoB,QAAQ,CAE9Cm5B,EAAY,CAChB,GAAG,IAAI,CAACxX,KAAK,EAMTyX,GAAoC,IAAjB,IAAI,CAAC1vB,OAAO,CACrC,IAAI,CAACA,OAAO,EAAG,EACf,IAAM0E,EAAQ,IAAI,CAACA,KAAK,CAQxB,GANI,IACF,IAAI,CAACA,KAAK,EAAG,CADO,EAMlB4qB,GAAmB,IAAI,CAAC9B,GAAG,CAC7B,CAD+B,MACxB,EAGT,IAAMmC,EAAaF,EAAUntB,MAEQ,EACnCmtB,EAAUntB,MAAM,CACK,KAAnBlP,EAAQkP,MAAM,CACV,IAAI,CAACmB,aAAa,CAClBrQ,EAAQkP,MAAM,EAAImtB,EAAUntB,MAAM,CAEpC,KAA0B,IAAnBlP,EAAQkP,MAAM,CAAkB,EACzClP,EAAQkP,MAAM,CAAGmtB,EAAUntB,MAAAA,EAG7B,IAAMkE,EAAWF,GAAAA,EAAAA,gBAAAA,EACfpF,CAAAA,EAAAA,EAAAA,WAAAA,EAAY6d,GAAM3Y,CAAAA,EAAAA,EAAAA,cAAAA,EAAe2Y,GAAMA,GAEnCtY,EAAmBrE,CAAAA,EAAAA,EAAAA,mBAAAA,EACvBoE,EAASlQ,QAAQ,CACjB,IAAI,CAAC+L,OAAO,EAGVoE,EAAiBlE,cAAc,EAAE,CACnCktB,EAAUntB,MAAM,CAAGmE,EAAiBlE,cAAc,CAClDiE,EAASlQ,QAAQ,CAAGub,GAAAA,EAAAA,WAAAA,EAAYrL,EAASlQ,QAAQ,EACjDyoB,EAAKxK,CAAAA,EAAAA,EAAAA,oBAAAA,EAAqB/N,GAC1B5P,EAAMib,CAAAA,EAAAA,EAAAA,WAAAA,EACJzP,CAAAA,EAAAA,EAAAA,mBAAAA,EACElB,CAAAA,EAAAA,EAAAA,WAAAA,EAAYtK,GAAOwP,CAAAA,EAAAA,EAAAA,cAAc,EAACxP,GAAOA,EACzC,IAAI,CAACyL,OAAO,EACZ/L,QAAQ,GAGd,IAAIs5B,GAAc,EAMZ,CAAa,OAAZ,MAAI,CAACvtB,OAAAA,EAAO,OAAZ,EAAcvG,QAAQ,CAAC2zB,EAAUntB,MAAAA,CAAAA,CAAM,GAAI,EACrChM,QAAQ,CAAGxC,CAAAA,EAAAA,EAAAA,SAAAA,EAAU0S,EAASlQ,QAAQ,CAAEm5B,EAAUntB,MAAM,EACjE+qB,EAAqB,CACnBz2B,IAAK2d,CAAAA,EAAAA,EAAAA,oBAAAA,EAAqB/N,GAC1BzG,OAAQ,IAAI,GAId6vB,GAAc,GAIlB,IAAMlpB,EAAiBL,CAAAA,EAAAA,EAAAA,kBAAAA,EACrB,IAAI,CAACiJ,aAAa,MAClBzP,EACA4vB,EAAUntB,MAAM,EAKlB,GAAIwB,CAIC8rB,GACDlpB,EALiC,CAMjC,IAAI,CAACmpB,cAAc,EACnBp4B,KAAKmC,QAAQ,CAACH,QAAQ,GAAKiN,EAAeopB,MAAM,CAChD,CACA,IAAMC,EAAe3pB,CAAAA,EAAAA,EAAAA,cAAAA,EAAe2Y,GACpCsO,EAAqB,CACnBz2B,IAAM,QAAM8P,CAAAA,CAAespB,IAAI,CAAG,GAAK,KAAE,MACvCtpB,EAAeopB,MAAM,CACpBje,CAAAA,EAAAA,EAAAA,WAAAA,EACA,CACC4d,EAAUntB,EAAVmtB,IAAgB,GAAK/oB,EAAejD,aAAa,CAC7C,GACC,IAAGgsB,EAAUntB,MAAAA,GACA,CAAjBytB,KAAAA,EAAuB,GAAKA,CAAAA,CAAAA,CAAW,CAAO,KAEnDhwB,OAAQ,IAAI,GAId6vB,GAAc,CAChB,CAGF,GAAIA,EACF,OAAO,IADQ,QACI,KAAO,EAE9B,CAGIl3B,EAAAA,EAAE,EAAE,YACMuR,IAAI,CAAC,eAGnB,GAAM,SAAE/E,GAAU,CAAK,QAAEtF,EAAS,EAAI,CAAE,CAAGxM,EACrC68B,GAAa,SAAE/qB,CAAQ,EAEzB,IAAI,CAACgrB,cAAc,EAAI,IAAI,CAAC1C,GAAG,EAAE,CAC/B,GACFvY,EAAOU,EADG,IACG,CAAC8C,IAAI,CAChB,mBACAmR,IACA,IAAI,CAACsG,cAAc,CACnBD,IAGJ,IAAI,CAACzC,GAAG,GACR,IAAI,CAACA,GAAG,CAAG,MAGbzO,EAAKlN,CAAAA,EAAAA,EAAAA,WAAAA,EACH/d,CAAAA,EAAAA,EAAAA,SAAAA,EACEoN,CAAAA,EAAAA,EAAAA,WAAW,EAAC6d,GAAM3Y,CAAAA,EAAAA,EAAAA,cAAAA,EAAe2Y,GAAMA,EACvC3rB,EAAQkP,MAAM,CACd,IAAI,CAACmB,aAAa,GAGtB,IAAMqmB,GAAY7X,CAAAA,EAAAA,EAAAA,YAAAA,EAChB/Q,CAAAA,EAAAA,EAAAA,WAAAA,EAAY6d,GAAM3Y,CAAAA,EAAAA,EAAAA,cAAAA,EAAe2Y,GAAMA,EACvC0Q,EAAUntB,MAAM,EAElB,IAAI,CAAC4tB,cAAc,CAAGnR,EAEtB,IAAMoR,GAAeR,IAAeF,EAAUntB,MAAM,CAKpD,GAAI,CAACgtB,GAAmB,IAAI,CAACc,eAAe,CAACtG,KAAc,CAACqG,GAAc,CACxEV,EAAUvvB,MAAM,CAAG4pB,GACnB7U,EAAOU,MAAM,CAAC8C,IAAI,CAAC,kBAAmBsG,EAAIkR,IAE1C,IAAI,CAACI,WAAW,CAACr1B,EAAQpE,EAAKmoB,EAAI,CAChC,GAAG3rB,CAAO,CACVwM,QAAQ,CACV,GACIA,GACF,IAAI,CADM,YACO,CAACkqB,IAEpB,GAAI,CACF,MAAM,IAAI,CAACtrB,GAAG,CAACixB,EAAW,IAAI,CAACtV,UAAU,CAACsV,EAAUt5B,KAAK,CAAC,CAAE,KAC9D,CAAE,MAAOiP,EAAK,CAIZ,KAHInO,CAAAA,EAAAA,EAAAA,OAAAA,EAAQmO,IAAQA,EAAIC,SAAS,EAC/B4P,EAAOU,MAAM,CAAC8C,IAAI,CAAC,mBAAoBrT,EAAK0kB,GAAWmG,IAEnD7qB,CACR,CAGA,OADA6P,EAAOU,MAAM,CAAC8C,IAAI,CAAC,qBAAsBsG,EAAIkR,IACtC,EACT,CAEA,IAAIK,GAAShqB,CAAAA,EAAAA,EAAAA,gBAAAA,EAAiB1P,GAC1B,UAAEN,EAAQ,OAAE8J,EAAK,CAAE,CAAGkwB,GAM1B,GAAI,CACD,CAAC7F,EAAO,CAAEgB,WAAYC,CAAQ,CAAE,CAAC,CAAG,MAAM7iB,QAAQwV,GAAG,CAAC,CACrD,IAAI,CAACpb,UAAU,CAACyT,WAAW,GAC3BC,GAAAA,EAAAA,sBAAAA,IACA,IAAI,CAAC1T,UAAU,CAAC6T,aAAa,GAC9B,CACH,CAAE,MAAO1R,EAAK,CAIZ,OADAioB,EAAqB,CAAEz2B,IAAKmoB,EAAIhf,OAAQ,IAAI,IACrC,CACT,CAOI,IAAK,CAACwwB,QAAQ,CAACzG,KAAeqG,KAChCn1B,EAAS,EADsB,KAAe,OACrC,EAKX,IAAImvB,GAAapL,EAKjBzoB,GAAWA,GACPgc,GAAAA,EAAAA,mBAAAA,EAAoBlM,CAAAA,EAAAA,EAAAA,cAAAA,EAAe9P,KACnCA,GAEJ,IAAIH,GAAQmc,CAAAA,EAAAA,EAAAA,mBAAAA,EAAoBhc,IAC1Bk6B,GAAmBzR,EAAG/gB,UAAU,CAAC,MAAQsI,GAAAA,EAAAA,gBAAAA,EAAiByY,GAAIzoB,QAAQ,CAI5E,GAA8B,MAA9B,GAAK,IAAI,CAAC6jB,UAAU,CAAC7jB,GAAAA,EAAS,OAAzB,EAAmCm6B,WAAW,CAEjD,CAFmD,MACnDpD,EAAqB,CAAEz2B,IAAKmoB,EAAIhf,OAAQ,IAAI,GACrC,IAAI8I,QAAQ,KAAO,GAG5B,IAAM6nB,GAAsB,CAAC,CAC3BF,CAAAA,IACAr6B,KAAUq6B,IACT,EAAC54B,CAAAA,EAAAA,EAAAA,SAAD,KAACA,EAAezB,KACf,CAACqd,CAAAA,EAAAA,EAAAA,eAAAA,EAAgBlT,CAAAA,EAAAA,EAAAA,aAAAA,EAAcnK,KAAQq6B,GAAAA,CAAAA,CAAgB,CAAC,GAM1D,CAACp9B,EAAQ8R,OAAO,EACf,MAAMykB,EAAkB,CACvBzpB,OAAQ6e,EACRzc,OAAQmtB,EAAUntB,MAAM,CACxBvC,OAAQ,IAAI,GAoDhB,GAjDIuvB,GAAmBqB,KACrBpB,GAAoB,GAGlBA,GAAkC,KAJI,MAIO,CAAxBj5B,KACrBlD,EAAgBo8B,kBAAkB,EAAG,EA+BrCc,GAAOh6B,QAAQ,CAAGk0B,EAAoBl0B,GAAUm0B,GAE5C6F,GAAOh6B,QAAQ,GAAKA,KACtBA,GAAWg6B,EADqB,CACdh6B,QAAQ,CAC1Bg6B,GAAOh6B,QAAQ,CAAGub,GAAAA,EAAAA,WAAAA,EAAYvb,IAE1B,KACFM,EAAM2d,CAAAA,EAAAA,EAAAA,OADgB,aAChBA,EAAqB+b,GAAAA,IAM/B,CAAC1b,CAAAA,EAAAA,EAAAA,UAAAA,EAAWmK,GAQd,EARmB,KAOnBsO,EAAqB,CAAEz2B,IAAKmoB,EAAIhf,OAAQ,IAAI,IACrC,EAGToqB,GAAalY,CAAAA,EAAAA,EAAAA,YAAAA,EAAa7L,CAAAA,EAAAA,EAAAA,cAAAA,EAAe+jB,IAAasF,EAAUntB,MAAM,EAEtEnM,GAAQmc,CAAAA,EAAAA,EAAAA,mBAAAA,EAAoBhc,IAC5B,IAAIs6B,IAA6B,EAEjC,GAAIh5B,CAAAA,EAAAA,EAAAA,cAAAA,EAAezB,IAAQ,CACzB,IAAMqQ,EAAWF,CAAAA,EAAAA,EAAAA,gBAAAA,EAAiB6jB,IAC5BhX,EAAa3M,EAASlQ,QAAQ,CAE9Bu6B,EAAavwB,CAAAA,EAAAA,EAAAA,aAAAA,EAAcnK,IACjCy6B,GAAapd,CAAAA,EAAAA,EAAAA,eAAAA,EAAgBqd,GAAY1d,GACzC,IAAM2d,EAAoB36B,KAAUgd,EAC9B2B,EAAiBgc,EACnB5d,CAAAA,EAAAA,EAAAA,aAAAA,EAAc/c,GAAOgd,EAAY/S,IAChC,CAAC,EAEN,GAAI,MAAgB0wB,GAAsBhc,EAAezb,MAAAA,EAgC9Cy3B,EACT/R,EAAKxK,CAAAA,EAAAA,CAjCkC,CAiClCA,UADuB,UACvBA,EACHhf,OAAOwP,MAAM,CAAC,CAAC,EAAGyB,EAAU,CAC1BlQ,SAAUwe,EAAezb,MAAM,CAC/B+G,MAAO3E,CAAAA,EAAAA,EAAAA,IAAAA,EAAK2E,GAAO0U,EAAerB,MAAM,CAC1C,IAIFle,OAAOwP,MAAM,CAAC3E,GAAOwwB,QAzC2C,CAChE,IAAMG,EAAgBx7B,OAAOoG,IAAI,CAACk1B,EAAWtwB,MAAM,EAAEjE,MAAM,CACzD,GAAW,CAAC8D,EAAK,CAACsT,EAAM,EAAI,CAACmd,EAAWtwB,MAAM,CAACmT,EAAM,CAACE,QAAQ,EAGhE,GAAImd,EAAcp6B,MAAM,CAAG,GAAK,CAACg6B,GAc/B,MAAM,UAd4C,WAyBjD,CAXK,MACHG,CAAAA,EACI,wBAAyBl6B,EAAI,oCAAmCm6B,EAAct2B,IAAI,CACjF,MACA,kCACD,4BAA6B0Y,EAAW,4CAA6Chd,GAAM,OAAG,+CAE/F26B,EAAAA,CACI,4BACA,wBAAqB,CAC1B,oBAVC,2CAWN,EAEJ,CAWF,CAEI,GACF7b,EAdO,MAcM,CAACwD,IAAI,CAAC,mBAAoBsG,EAAIkR,IAG7C,IAAMe,GAAiC,SAAlB,IAAI,CAAC16B,QAAQ,EAAiC,YAAlB,IAAI,CAACA,QAAQ,CAE9D,GAAI,CACF,IAAI+4B,EAAY,MAAM,IAAI,CAAC4B,YAAY,CAAC,OACtC96B,YACAG,SACA8J,MACA2e,aACAoL,cACA8F,GACA3tB,OAAQmtB,EAAUntB,MAAM,CACxBiN,UAAWkgB,EAAUlgB,SAAS,CAC9Bkd,cAAekE,GACf7D,yBAA0B15B,EAAQ05B,wBAAwB,CAC1DwC,gBAAiBA,GAAmB,CAAC,IAAI,CAACxuB,UAAU,qBACpD4vB,EACF,GAUA,GARI,GAAqBt9B,EAAQ8R,OAAO,EAAE,KAAlB,CAChB,IAAI,CAAC0oB,IAAI,CACb7O,EACA,eAAgBsQ,EAAYA,EAAUlF,UAAU,MAAGtqB,EACnD4vB,EAAUntB,MAAM,EAIhB,UAAW+sB,GAAasB,GAAmB,CAE7Cx6B,GADAG,GAAW+4B,EACH/4B,KADkB,EAAIH,GAG1B,GAAY+O,OAAO,EACrB9E,IAAQ7K,OAAOwP,MAAM,CAAC,CAAC,EAAGsqB,EAAUjvB,KAAK,EAAI,CAAC,EAAGA,GAAAA,EAGnD,IAAM8wB,EAAwBhwB,CAAAA,EAAAA,EAAAA,WAAAA,EAAYovB,GAAOh6B,QAAQ,EACrD8P,CAAAA,EAAAA,EAAAA,cAAAA,EAAekqB,GAAOh6B,QAAQ,EAC9Bg6B,GAAOh6B,QAAQ,CAUnB,GARIs6B,IAAct6B,KAAa46B,GAC7B37B,OAAOoG,IAAI,CAACi1B,IAAY/0B,IAD4B,GACrB,CAAC,IAC1B+0B,IAAcxwB,EAAK,CAACrE,EAAI,GAAK60B,EAAU,CAAC70B,EAAI,EAAE,OACzCqE,EAAK,CAACrE,EAAI,GAKnBnE,CAAAA,EAAAA,EAAAA,cAAAA,EAAetB,IAAW,CAY5B,IAAI66B,EAVF,CAAClB,GAAW/qB,MAUEksB,CAVK,EAAI/B,EAAUlF,UAAU,CACvCkF,EAAUlF,UAAU,CACpBtY,CAAAA,EAAAA,EAAAA,WAAAA,EACE/d,CAAAA,EAAAA,EAAAA,SAAAA,EACE,IAAIiN,IAAIge,EAAInlB,SAASC,IAAI,EAAEvD,QAAQ,CACnCm5B,EAAUntB,MAAM,GAElB,GAKJpB,CAAAA,EAAAA,EAAAA,WAAAA,EAAYiwB,KACdA,EAAY/qB,CAAAA,EAAAA,EADc,cACdA,EAAe+qB,EAAAA,CAGQ,EACnC,IAAME,EAAejvB,CAAAA,EAAAA,EAAAA,mBAAAA,EAAoB+uB,EAAW,IAAI,CAAC9uB,OAAO,EAChEotB,EAAUntB,MAAM,CAAG+uB,EAAa9uB,cAAc,EAAIktB,EAAUntB,MAAM,CAClE6uB,EAAYE,EAAa/6B,QAAQ,CAEnC,IAAMu6B,EAAavwB,CAAAA,EAAAA,EAAAA,aAAAA,EAAchK,IAC3Bg7B,EAAgB9d,CAAAA,EAAAA,EAAAA,eAAAA,EAAgBqd,GACpC,IAAI9vB,IAAIowB,EAAWv3B,SAASC,IAAI,EAAEvD,QAAQ,EAGxCg7B,GACF/7B,OAAOwP,KADU,CACJ,CAAC3E,GAAOkxB,EAEzB,CACF,CAGA,GAAI,SAAUjC,EACZ,GAAuB,MADA,eACqB,CAAxCA,EAAUtiB,IAAI,CAChB,OAAO,IAAI,CAAC4gB,MAAM,CAAC3yB,EAAQq0B,EAAUrD,MAAM,CAAEqD,EAAUtD,KAAK,CAAE34B,QAG9D,OADAi6B,EAAqB,CAAEz2B,IAAKy4B,EAAUvL,WAAW,CAAE/jB,OAAQ,IAAI,GACxD,IAAI8I,QAAQ,KAAO,GAI9B,IAAMgF,EAAiBwhB,EAAUr1B,SAAS,CAU1C,GATI6T,GAAaA,EAAU0jB,qBAAqB,EAAE,EAC9B,CAACjN,MAAM,CAACzW,EAAU0jB,qBAAqB,IAEjD11B,OAAO,CAAC,IACd21B,CAAAA,EAAAA,EAAAA,sBAAAA,EAAuBhU,EAAO3iB,KAAK,CACrC,GAIGw0B,CAAAA,EAAUxqB,OAAO,EAAIwqB,EAAUoC,OAAAA,GAAYpC,EAAUx0B,KAAK,CAAE,CAC/D,GACEw0B,EAAUx0B,KAAK,CAAC62B,SAAS,EACzBrC,EAAUx0B,KAAK,CAAC62B,SAAS,CAACC,YAAY,CACtC,CAEAv+B,EAAQkP,MAAM,EAAG,EAEjB,IAAMwhB,EAAcuL,EAAUx0B,KAAK,CAAC62B,SAAS,CAACC,YAAY,CAK1D,GACE7N,EAAY9lB,UAAU,CAAC,OAC8B,IAArDqxB,EAAUx0B,KAAK,CAAC62B,SAAS,CAACE,sBAAsB,CAChD,CACA,IAAMC,EAAavrB,CAAAA,EAAAA,EAAAA,gBAAAA,EAAiBwd,GACpC+N,EAAWv7B,QAAQ,CAAGk0B,EACpBqH,EAAWv7B,QAAQ,CACnBm0B,GAGF,GAAM,CAAE7zB,IAAKo1B,CAAM,CAAEjN,GAAIgN,CAAK,CAAE,CAAG7B,EACjC,IAAI,CACJpG,EACAA,GAEF,OAAO,IAAI,CAAC6J,MAAM,CAAC3yB,EAAQgxB,EAAQD,EAAO34B,EAC5C,CAEA,OADAi6B,EAAqB,CAAEz2B,IAAKktB,EAAa/jB,OAAQ,IAAK,GAC/C,IAAI8I,QAAQ,KAAO,EAC5B,CAKA,GAHA4mB,EAAUlgB,SAAS,CAAG,CAAC,CAAC8f,EAAUx0B,KAAK,CAACi3B,WAAW,CAG/CzC,EAAUx0B,KAAK,CAACsyB,QAAQ,GAAKf,EAAoB,CACnD,IAAI2F,EAEJ,GAAI,CACF,MAAM,IAAI,CAACC,cAAc,CAAC,QAC1BD,EAAgB,MAClB,CAAE,MAAO/wB,EAAG,CACV+wB,EAAgB,SAClB,CAcA,GAZA1C,EAAY,MAAM,IAAI,CAAC4B,YAAY,CAAC,CAClC96B,MAAO47B,EACPz7B,SAAUy7B,QACV3xB,MACA2e,aACAoL,GACA8F,WAAY,CAAE/qB,SAAS,CAAM,EAC7B5C,OAAQmtB,EAAUntB,MAAM,CACxBiN,UAAWkgB,EAAUlgB,SAAS,CAC9B0iB,WAAY,EACd,GAEI,SAAU5C,EACZ,MAAM,GADiB,IACjB,cAAiD,CAAjD,MAAW,wCAAX,+DAAgD,EAE1D,CACF,CAGEC,GACkB,YAAlB,IAAI,CAACh5B,QAAQ,EACbmB,CAAAA,MAAAA,CAAAA,EAAAA,KAAKuO,aAAa,CAACnL,KAAAA,GAAgB,OAAnCpD,EAAAA,EAA0Bi6B,SAAAA,EAAS,OAAnCj6B,EAAqCssB,UAAU,IAAK,aACpDsL,EAAAA,EAAUx0B,KAAAA,EAAK,OAAfw0B,EAAiBqC,SAAAA,GACjB,CAGArC,EAAUx0B,KAAK,CAAC62B,SAAS,CAAC3N,UAAU,CAAG,KAIzC,IAAMmO,EACJ9+B,EAAQ8R,OAAO,EAAIuqB,EAAUt5B,KAAK,IAAqB,CAAfk5B,KAAAA,CAAAA,EAAAA,EAAUl5B,KAAAA,EAAVk5B,EAAmBl5B,EAAAA,CAAAA,CAAI,EAG/D/C,OAAAA,EAAAA,EAAQwM,MAAAA,EAARxM,EAAmB,CAACk8B,GAAmB,CAAC4C,EAEpCC,EAAsB/C,MAAAA,EAAAA,EADRgD,EAAe,CAAEzlB,EAAG,EAAGC,EAAG,CAAE,EAAI,CACRylB,IAGtCC,EAAsB,CAC1B,GAAG7C,CAAS,CACZt5B,SACAG,kBACA8J,GACAF,OAAQ4pB,GACRhpB,YAAY,CACd,EAOA,GAAIwuB,GAAmB0B,GAAc,CAanC,GAZA3B,EAAY,MAAM,IAAI,CAAC4B,YAAY,CAAC,CAClC96B,MAAO,IAAI,CAACG,QAAQ,CACpBA,SAAU,IAAI,CAACA,QAAQ,OACvB8J,MACA2e,aACAoL,GACA8F,WAAY,CAAE/qB,SAAS,CAAM,EAC7B5C,OAAQmtB,EAAUntB,MAAM,CACxBiN,UAAWkgB,EAAUlgB,SAAS,CAC9B+f,gBAAiBA,GAAmB,CAAC,IAAI,CAACxuB,UAAU,GAGlD,SAAUuuB,EACZ,MAAM,GADiB,IACjB,cAA6D,CAA7D,MAAW,mCAAkC,IAAI,CAAC/4B,QAAQ,EAA1D,+DAA4D,GAIhD,YAAlB,IAAI,CAACA,QAAQ,EACbmB,CAAAA,MAAAA,GAAAA,KAAKuO,aAAa,CAACnL,KAAAA,GAAgB,OAAnCpD,EAAAA,EAA0Bi6B,SAAAA,EAAS,OAAnCj6B,EAAqCssB,UAAAA,IAAe,aACpDsL,EAAAA,EAAUx0B,KAAAA,EAAK,OAAfw0B,EAAiBqC,SAAAA,GACjB,CAGArC,EAAUx0B,KAAK,CAAC62B,SAAS,CAAC3N,UAAU,CAAG,KAGzC,GAAI,CACF,MAAM,IAAI,CAACvlB,GAAG,CAAC8zB,EAAqBjD,EAAW8C,EACjD,CAAE,MAAO/sB,EAAK,CAIZ,KAHInO,GAAAA,EAAAA,OAAAA,EAAQmO,IAAQA,EAAIC,SAAS,EAAE,EAC1BsQ,MAAM,CAAC8C,IAAI,CAAC,mBAAoBrT,EAAK0kB,GAAWmG,IAEnD7qB,CACR,CAEA,OAAO,CACT,CAeA,GAbA6P,EAAOU,MAAM,CAAC8C,IAAI,CAAC,sBAAuBsG,EAAIkR,IAC9C,IAAI,CAACI,WAAW,CAACr1B,EAAQpE,EAAKmoB,EAAI3rB,GAY9B,CANFk8B,IACA,CAAC6C,GACD,CAACzC,GACD,CAACS,IACD3tB,CAAAA,EAAAA,EAAAA,mBAAAA,EAAoB8vB,EAAqB,IAAI,CAACra,MAAK,EAE/B,CACpB,GAAI,CACF,MAAM,IAAI,CAACzZ,GAAG,CAAC8zB,EAAqBjD,EAAW8C,EACjD,CAAE,MAAOI,EAAQ,CACf,GAAIA,EAAEltB,SAAS,CAAEgqB,EAAU55B,KAAK,CAAG45B,EAAU55B,KAAK,EAAI88B,OACjD,MAAMA,CACb,CAEA,GAAIlD,EAAU55B,KAAK,CAUjB,CAVmB,KACf,GACFwf,EAAOU,MAAM,CAAC8C,IAAI,CAChB,mBACA4W,EAAU55B,KAAK,CACfq0B,GACAmG,IAIEZ,EAAU55B,KAAK,CAIjBg6B,EAAUntB,MAAM,EAAE,CACpB/O,SAASC,eAAe,CAACg/B,IAAI,CAAG/C,EAAUntB,MAAAA,EAI1C,GACF2S,EAAOU,MAAM,CAAC8C,IAAI,CADE,sBACsBsG,EAAIkR,IAK5CmC,GAAgBK,OAAUt6B,IAAI,CAAC4mB,IACjC,CADsC,GAClC,CAACta,YAAY,CAACsa,EAEtB,CAEA,OAAO,CACT,CAAE,MAAO3Z,EAAK,CACZ,GAAInO,GAAAA,EAAAA,OAAAA,EAAQmO,IAAQA,EAAIC,SAAS,CAC/B,CADiC,KAC1B,EAET,OAAMD,CACR,CACF,CAEAirB,YACEr1B,CAAqB,CACrBpE,CAAW,CACXmoB,CAAU,CACV3rB,CAA+B,CACzB,CADNA,KAAAA,IAAAA,IAAAA,EAA6B,CAAC,IAcf,cAAX4H,GAA0BjC,CAAAA,EAAAA,EAAAA,MAAAA,MAAagmB,CAAAA,GAAI,CAC7C,IAAI,CAAC2T,QAAQ,CAAGt/B,EAAQ8R,OAAO,CAC/BvL,OAAO+zB,OAAO,CAAC1yB,EAAO,CACpB,KACEpE,EACAmoB,aACA3rB,EACAu/B,KAAK,EACL52B,IAAM,IAAI,CAAC62B,IAAI,CAAc,cAAX53B,EAAyB,IAAI,CAAC43B,IAAI,CAAGlJ,GACzD,EAIA,CAFA,EAGA3K,GAGN,CAEA,MAAM8T,qBACJztB,CAAgD,CAChD9O,CAAgB,CAChB8J,CAAqB,CACrB2e,CAAU,CACVkR,CAA2B,CAC3B6C,CAAuB,CACY,CACnC,GAAI1tB,EAAIC,SAAS,CAEf,CAFiB,KAEXD,EAGR,GAAIgW,CAAAA,EAAAA,EAAAA,QArBqF,IAqBrFA,EAAahW,IAAQ0tB,EAgBvB,MAfA7d,EAAOU,KAD+B,CACzB,CAAC8C,IAAI,CAAC,mBAAoBrT,EAAK2Z,EAAIkR,GAQhD5C,EAAqB,CACnBz2B,IAAKmoB,EACLhf,OAAQ,IAAI,GAKR6pB,IAGRjiB,QAAQlS,KAAK,CAAC2P,GAEd,GAAI,CAEF,IADIvK,EACE,CAAEM,KAAMnB,CAAS,aAAEuO,CAAW,CAAE,CACpC,MAAM,IAAI,CAACypB,cAAc,CAAC,WAEtB3C,EAAsC,CAC1Cx0B,kBACAb,cACAuO,MACAnD,EACA3P,MAAO2P,CACT,EAEA,GAAI,CAACiqB,EAAUx0B,KAAK,CAClB,CADoB,EAChB,CACFw0B,EAAUx0B,KAAK,CAAG,MAAM,IAAI,CAACD,eAAe,CAACZ,EAAW,KACtDoL,WACA9O,QACA8J,CACF,EACF,CAAE,MAAO2yB,EAAQ,CACfprB,QAAQlS,KAAK,CAAC,0CAA2Cs9B,GACzD1D,EAAUx0B,KAAK,CAAG,CAAC,CACrB,CAGF,OAAOw0B,CACT,CAAE,MAAO2D,EAAc,CACrB,OAAO,IAAI,CAACH,oBAAoB,CAC9B57B,CAAAA,EAAAA,EAAAA,OAAAA,EAAQ+7B,GAAgBA,EAAe,qBAA4B,CAAxB/3B,MAAM+3B,EAAe,IAAzB,+DAA2B,GAClE18B,EACA8J,EACA2e,EACAkR,GACA,EAEJ,CACF,CAEA,MAAMgB,aAAa,CA4BlB,CAAE,CA5BgB,IACjB96B,MAAO88B,CAAc,UACrB38B,CAAQ,OACR8J,CAAK,IACL2e,CAAE,YACFoL,CAAU,YACV8F,CAAU,CACV3tB,QAAM,eACNmqB,CAAa,WACbld,CAAS,0BACTud,CAAwB,iBACxBwC,CAAe,qBACfoB,CAAmB,YACnBuB,CAAU,CAeX,CA5BkB,EAmCb97B,EAAQ88B,EAEZ,GAAI,KA6EApI,EACAA,EAKEA,EAyDsBA,EA3I1B,IAAIqI,EAA6C,IAAI,CAAC/Y,UAAU,CAAChkB,EAAM,CACvE,GAAI85B,EAAW/qB,OAAO,EAAIguB,GAAgB,IAAI,CAAC/8B,KAAK,GAAKA,EACvD,KAD8D,EACvD+8B,EAGT,IAAMzF,EAAkBH,EAAoB,OAAEn3B,EAAO4J,OAAQ,IAAI,GAE7D0sB,IACFyG,OAAerzB,CAAAA,EAGjB,CAJmB,GAIfszB,GACFD,GACE,YAAaA,CAAf,CAAE,KAGErzB,EADAqzB,EAIAE,CANWF,CAAW,CAO1BjH,GANAnoB,MAMU,EANFC,EAMM,CANH,CAACqpB,KAAa,GAAL,CAMK,CAACnW,WAAW,CAAC,CACpCpd,KAAM0a,CAAAA,EAAAA,EAAAA,oBAAAA,EAAqB,UAAEje,QAAU8J,CAAM,GAC7C+W,mBAAmB,EACnBjX,OAAQ+xB,EAAa,OAAS9H,SAC9B7nB,CACF,GACAmqB,eAAe,EACfC,eAAgB,IAAI,CAAChoB,KAAK,CAC1BioB,WAAW,EACXJ,cAAeM,EAAe,IAAI,CAACwG,GAAG,CAAG,IAAI,CAACC,GAAG,CACjD1G,aAAc,CAACrd,EACfid,YAAY,2BACZM,EACAD,aAfmByC,CAgBrB,EAEIzE,EAKFyE,GAAmB,CAACoB,EAChB,KACA,MAAM/F,EAAsB,CAC1BC,UAAW,IAAM0B,EAAc8G,GAC/BlzB,OAAQ+xB,EAAa,OAAS9H,EAC9B7nB,OAAQA,EACRvC,OAAQ,IAAI,GACXoF,KAAK,CAAC,IAKP,GAAImqB,EACF,OAAO,IAET,IAHqB,GAGflqB,CACR,GAkBN,GAdIylB,IAAsB,IAAbv0B,QAAAA,GAAuC,SAAbA,CAAa,EAAK,EAAI,EACtDw0B,MAAM,MAAGjrB,CAAAA,EAGZyvB,IACGzE,EAGHA,EAAKqB,EAHI,EAGA,CAAGz0B,IAJK,CAIAuO,aAAa,CAACnL,KAAK,CAFpCgwB,EAAO,CAAEqB,KAAMz0B,KAAKuO,aAAa,CAACnL,KAAM,GAM5C4yB,IAGE5C,CAAAA,MAAAA,CAAAA,EAAAA,GAAAA,GAAAA,GAAAA,EAAMC,MAAAA,EAAM,OAAZD,EAAc9d,IAAI,IAAK,qBACvB8d,CAAAA,MAAAA,CAAAA,EAAY,GAAZA,IAAAA,EAAAA,EAAMC,MAAAA,EAAM,OAAZD,EAAc9d,IAAAA,IAAS,oBAEvB,CADA,MACO8d,EAAKC,MAAM,CAGpB,GAAID,CAAAA,MAAAA,CAAAA,EAAAA,GAAAA,GAAAA,GAAAA,EAAMC,MAAAA,EAAM,OAAZD,EAAc9d,IAAAA,IAAS,UAAW,CACpC,IAAMwmB,EAAgBjhB,CAAAA,EAAAA,EAAAA,mBAAAA,EAAoBuY,EAAKC,MAAM,CAAC9V,YAAY,EAC5DyV,EAAQ,MAAM,IAAI,CAACxnB,UAAU,CAACyT,WAAW,GAM/C,GAAI,EAAC4Y,GAAmB7E,EAAM3uB,QAAQ,CAACy3B,EAAAA,GAAgB,CACrDp9B,EAAQo9B,EACRj9B,EAAWu0B,EAAKC,MAAM,CAAC9V,YAAY,CACnC5U,EAAQ,CAAE,GAAGA,CAAK,CAAE,GAAGyqB,EAAKC,MAAM,CAACtkB,QAAQ,CAACpG,KAAK,EACjD+pB,EAAa/jB,CAAAA,EAAAA,EAAAA,cAAAA,EACXhE,CAAAA,EAAAA,EAAAA,mBAAAA,EAAoByoB,EAAKC,MAAM,CAACtkB,QAAQ,CAAClQ,QAAQ,CAAE,IAAI,CAAC+L,OAAO,EAC5D/L,QAAQ,EAIb48B,EAAe,IAAI,CAAC/Y,UAAU,CAAChkB,EAAM,CAEnC85B,EAAW/qB,OAAO,EAClBguB,GACA,IAAI,CAAC/8B,KAAK,GAAKA,GACf,CAACs2B,GAKD,MAAO,CAAE,GAAGyG,CAAY,CAJxB,MAI0B/8B,CAAM,CAGtC,CAEA,GAAIq9B,CAAAA,EAAAA,EAAAA,UAAAA,EAAWr9B,GAEb,KAFqB,EACrBk3B,EAAqB,CAAEz2B,IAAKmoB,EAAIhf,OAAQ,IAAI,GACrC,IAAI8I,QAAe,KAAO,GAGnC,IAAMwmB,EACJ8D,GACC,MAAM,IAAI,CAACnB,cAAc,CAAC77B,GAAOkS,IAAI,CACnClO,GAAS,EACRH,CADQ,SACGG,EAAIgB,IAAI,CACnBoN,YAAapO,EAAIoO,WAAW,CAC5B1D,QAAS1K,EAAI4T,GAAG,CAAClJ,OAAO,CACxB4sB,QAASt3B,EAAI4T,GAAG,CAAC0jB,OAAO,EAC1B,EAWEgC,EAAoB5I,MAAAA,CAAAA,EAAc,GAAdA,IAAAA,EAAAA,EAAMG,QAAAA,EAAQ,OAAdH,EAAgBK,OAAO,CAAC9tB,GAAG,CAAC,qBAEhDs2B,EAAkBrE,EAAUxqB,OAAO,EAAIwqB,EAAUoC,OAAO,KAIrC5G,MAAAA,EAAAA,KAAAA,EAAAA,EAAMoB,QAAAA,GAAU,OAChC,IAAI,CAACqH,GAAG,CAACzI,EAAKoB,QAAQ,CAAC,CAGhC,GAAM,CAAEpxB,OAAK,UAAEsxB,CAAQ,CAAE,CAAG,MAAM,IAAI,CAACwH,QAAQ,CAAC,UAC9C,GAAID,EAAiB,CACnB,GAAI7I,CAAAA,MAAAA,EAAAA,KAAAA,EAAAA,EAAMqB,IAAAA,GAAQ,CAACuH,EACjB,MAAO,CAAEtH,SAAUtB,CADiB,CACZsB,QAAQ,CAAEtxB,MAAOgwB,EAAKqB,IAAI,EAGpD,IAAMD,EAAWpB,CAAAA,MAAAA,EAAAA,KAAAA,EAAAA,EAAMoB,QAAAA,EACnBpB,EAAKoB,QAAQ,CACb,IAAI,CAAChpB,UAAU,CAACgU,WAAW,CAAC,CAC1Bpd,KAAM0a,CAAAA,EAAAA,EAAAA,oBAAAA,EAAqB,UAAEje,QAAU8J,CAAM,GAC7CF,OAAQiqB,SACR7nB,CACF,GAEEsxB,EAAU,MAAMtH,EAAc,UAClCL,EACAS,eAAgB,IAAI,CAAChoB,KAAK,CAC1BioB,WAAW,EACXJ,cAAekH,EAAoB,CAAC,EAAI,IAAI,CAACH,GAAG,CAChD1G,aAAc,CAACrd,EACfid,YAAY,2BACZM,CACF,GAEA,MAAO,CACLX,SAAUyH,EAAQzH,QAAQ,CAC1BtxB,MAAO+4B,EAAQ1H,IAAI,EAAI,CAAC,CAC1B,CACF,CAEA,MAAO,CACLhB,QAAS,CAAC,EACVrwB,MAAO,MAAM,IAAI,CAACD,eAAe,CAC/By0B,EAAUr1B,SAAS,CACnB,UAEE1D,QACA8J,EACAF,OAAQ6e,SACRzc,EACAD,QAAS,IAAI,CAACA,OAAO,CACrBoB,cAAe,IAAI,CAACA,aAAa,EAGvC,CACF,GAiCA,OA5BI4rB,EAAUoC,OAAO,EAAI2B,EAAoBnH,QAAQ,EAAIE,GACvD,OADiE,IACtD,CAACmH,GAAG,CAACnH,EAAS,CAMzB,IAAK,CAAC5c,SAAS,GACf8f,EAAUxqB,OAAO,EAEhByqB,EADDxrB,CAGAwoB,EACE/2B,KAJMwO,EAICgB,CAJE,CAACqoB,GAEZ,CAEe,CAAC,CAAC,EAAGgG,EAAqB,CACrCvG,WAJJ,GAIkB,EACdD,cAAc,EACdL,cAAe,IAAI,CAAC8G,GAAG,IAEzBluB,KAAK,CAAC,KAAO,GAGjBtK,EAAM62B,SAAS,CAAGn8B,OAAOwP,MAAM,CAAC,CAAC,EAAGlK,EAAM62B,SAAS,EACnDrC,EAAUx0B,KAAK,CAAGA,EAClBw0B,EAAUl5B,KAAK,CAAGA,EAClBk5B,EAAUjvB,KAAK,CAAGA,EAClBivB,EAAUlF,UAAU,CAAGA,EACvB,IAAI,CAAChQ,UAAU,CAAChkB,EAAM,CAAGk5B,EAElBA,CACT,CAAE,MAAOjqB,EAAK,CACZ,OAAO,IAAI,CAACytB,oBAAoB,CAC9BplB,GAAAA,EAAAA,cAAAA,EAAerI,GACf9O,EACA8J,EACA2e,EACAkR,EAEJ,CACF,CAEQzxB,IACNyZ,CAAwB,CACxB4S,CAAsB,CACtBwH,CAA4C,CAC7B,CAGf,OAFA,IAAI,CAACpa,KAAK,CAAGA,EAEN,IAAI,CAAC4b,GAAG,CACbhJ,EACA,IAAI,CAAC1Q,UAAU,CAAC,QAAQ,CAACngB,SAAS,CAClCq4B,EAEJ,CAMAyB,eAAezd,CAA0B,CAAE,CACzC,IAAI,CAAC0d,IAAI,CAAG1d,CACd,CAEA+Z,gBAAgBrR,CAAU,CAAW,CACnC,GAAI,CAAC,IAAI,CAAC7e,MAAM,CAAE,OAAO,EACzB,GAAM,CAAC8zB,EAAcC,EAAQ,CAAG,IAAI,CAAC/zB,MAAM,CAACrK,KAAK,CAAC,IAAK,GACjD,CAACq+B,EAAcC,EAAQ,CAAGpV,EAAGlpB,KAAK,CAAC,IAAK,SAG9C,EAAIs+B,GAAWH,IAAiBE,GAAgBD,IAAYE,GAKxDH,IAAiBE,EALgD,CAa9DD,IAAYE,CACrB,CAEA1vB,KAXqC,QAWxBsa,CAAU,CAAQ,CAC7B,GAAM,EAAGxZ,EAAO,EAAE,CAAC,CAAGwZ,EAAGlpB,KAAK,CAAC,IAAK,GAEpC3C,CAAAA,EAAAA,EAAAA,kBAAAA,EACE,KAGE,GAAa,KAATqS,GAAwB,QAATA,EAAgB,YACjC5L,OAAOkT,QAAQ,CAAC,EAAG,GAKrB,IAAMunB,EAAUC,mBAAmB9uB,GAE7B+uB,EAAO/gC,SAASkS,cAAc,CAAC2uB,GACrC,GAAIE,EAAM,YACRA,EAAK3uB,cAAc,GAKrB,IAAM4uB,EAAShhC,SAASihC,iBAAiB,CAACJ,EAAQ,CAAC,EAAE,IAEnDG,EAAO5uB,EADG,YACW,EAEzB,EACA,CACEtS,eAAgB,IAAI,CAAC+8B,eAAe,CAACrR,EACvC,EAEJ,CAEAwR,SAASrwB,CAAc,CAAW,CAChC,OAAO,IAAI,CAACA,MAAM,GAAKA,CACzB,CAQA,MAAMJ,SACJlJ,CAAW,CACXsJ,CAAoB,CACpB9M,CAA6B,CACd,CAMf,GARA8M,CAQI,IARJA,CAQiC,GARjCA,IAAAA,EAAiBtJ,CAAAA,EACjBxD,KAAAA,IAAAA,IAAAA,EAA2B,EAAC,EAOS+c,CAAAA,EAAAA,EAAAA,KAAAA,EAAMxW,OAAO8kB,SAAS,CAAClO,SAAS,EAInE,CAJsE,MAMxE,IAAI+f,EAAShqB,CAAAA,EAAAA,EAAAA,gBAAAA,EAAiB1P,GACxB69B,EAAcnE,EAAOh6B,QAAQ,CAE/B,CAAEA,UAAQ,CAAE8J,OAAK,CAAE,CAAGkwB,EACpBoE,EAAmBp+B,EAEzB,IAAIwN,IAA+B,EAAE,MACjB,CAAY,CAE5BwsB,EAAOh6B,QAAQ,CADfA,EAAW8L,CAAAA,EAAAA,EAAAA,mBAAAA,EAAqB9L,EAAU,IAAI,CAAC+L,OAAO,EAAE/L,QAAQ,CAEhEM,EAAM2d,CAAAA,EAAAA,EAAAA,oBAAAA,EAAqB+b,GAE3B,IAAI9pB,EAAWF,CAAAA,EAAAA,EAAAA,gBAAAA,EAAiBpG,GAC1BuG,EAAmBrE,CAAAA,EAAAA,EAAAA,mBAAAA,EACvBoE,EAASlQ,QAAQ,CACjB,IAAI,CAAC+L,OAAO,EAEdmE,EAASlQ,QAAQ,CAAGmQ,EAAiBnQ,QAAQ,CAC7ClD,EAAQkP,MAAM,CAAGmE,EAAiBlE,cAAc,EAAI,IAAI,CAACkB,aAAa,CACtEvD,EAASqU,CAAAA,EAAAA,EAAAA,oBAAAA,EAAqB/N,EAChC,CAGF,IAAMikB,EAAQ,MAAM,IAAI,CAACxnB,UAAU,CAACyT,WAAW,GAC3CyT,EAAajqB,EAEXoC,EACJ,KAA0B,IAAnBlP,EAAQkP,MAAM,CACjBlP,EAAQkP,MAAM,OAAIzC,EAClB,IAAI,CAACyC,MAAM,CAEXquB,EAAoB,MAAMhH,EAAkB,CAChDzpB,OAAQA,EACRoC,OAAQA,EACRvC,OAAQ,IAAI,GAsCduwB,EAAOh6B,QAAQ,CAAGk0B,EAAoB8F,EAAOh6B,QAAQ,CAAEm0B,GAEnD7yB,CAAAA,EAAAA,EAAAA,cAAAA,EAAe04B,EAAOh6B,QAAQ,GAAG,CACnCA,EAAWg6B,EAAOh6B,QAAQ,CAC1Bg6B,EAAOh6B,QAAQ,CAAGA,EAClBf,OAAOwP,MAAM,CACX3E,EACAoT,CAAAA,EAAAA,EAAAA,eAAAA,EAAgBlT,CAAAA,EAAAA,EAAAA,aAAAA,EAAcgwB,EAAOh6B,QAAQ,GAC3C4b,CAAAA,EAAAA,EAAAA,SAAAA,EAAUhS,GAAQ5J,QAAQ,GACvB,CAAC,GAGJ,IACFM,EAAM2d,CAAAA,EAAAA,EAAAA,QADgB,YAChBA,EAAqB+b,EAAAA,GAI/B,IAAMzF,EAGA,KAFJ/mB,CAEU6mB,EAAsB,CAC1BC,EAH6C,GAC/C,CAAI,IAES,IACT0B,EAAc,CACZL,SAAU,IAAI,CAAChpB,UAAU,CAACgU,WAAW,CAAC,CACpCpd,KAAM0a,CAAAA,EAAAA,EAAAA,oBAAAA,EAAqB,CACzBje,SAAUo+B,QACVt0B,CACF,GACA+W,mBAAmB,EACnBjX,OAAQiqB,SACR7nB,CACF,GACAmqB,cAAe,GACfC,gBAAgB,EAChBC,WAAW,EACXJ,cAAe,IAAI,CAAC+G,GAAG,CACvB1G,aAAc,CAAC,IAAI,CAACrd,SAAS,CAC7Bid,YAAY,CACd,GACFtsB,OAAQA,EACRoC,OAAQA,EACRvC,OAAQ,IAAI,GAmBpB,IAZI8qB,MAAAA,EAAAA,KAAAA,EAAAA,EAAMC,MAAM,CAAC/d,IAAAA,IAAS,WAAW,CACnCujB,EAAOh6B,QAAQ,CAAGu0B,EAAKC,MAAM,CAAC9V,YAAY,CAC1C1e,EAAWu0B,EAAKC,MAAM,CAAC9V,YAAY,CACnC5U,EAAQ,CAAE,GAAGA,CAAK,CAAE,GAAGyqB,EAAKC,MAAM,CAACtkB,QAAQ,CAACpG,KAAK,EACjD+pB,EAAaU,EAAKC,MAAM,CAACtkB,QAAQ,CAAClQ,QAAQ,CAC1CM,EAAM2d,CAAAA,EAAAA,EAAAA,oBAAAA,EAAqB+b,IAOzBzF,CAAAA,MAAAA,EAAAA,KAAAA,EAAAA,EAAMC,MAAM,CAAC/d,IAAAA,IAAS,oBACxB,CAD6C,MAI/C,IAAM5W,EAAQmc,CAAAA,EAAAA,EAAAA,mBAAAA,EAAoBhc,EAE9B,OAAM,IAAI,CAACs3B,IAAI,CAAC1tB,EAAQiqB,EAAY/2B,EAAQkP,MAAM,EAAE,IACtD,GAD6D,EACzD,CAAC6X,UAAU,CAACsa,EAAY,CAAG,CAAEhE,aAAa,EAAK,EAGrD,MAAM5nB,QAAQwV,GAAG,CAAC,CAChB,IAAI,CAACpb,UAAU,CAACoU,MAAM,CAAClhB,GAAOkS,IAAI,CAAC,GAC1BssB,KACHrI,EAAc,CACZL,SAAUpB,CAAAA,MAAAA,EAAAA,KAAAA,EAAAA,EAAMqB,IAAAA,EACZrB,MAAAA,EAAAA,KAAAA,EAAAA,EAAMoB,QAAQ,CACd,IAAI,CAAChpB,UAAU,CAACgU,WAAW,CAAC,CAC1Bpd,KAAMjD,EACNsJ,OAAQiqB,EACR7nB,OAAQA,CACV,GACJoqB,gBAAgB,EAChBC,WAAW,EACXJ,cAAe,IAAI,CAAC+G,GAAG,CACvB1G,aAAc,CAAC,IAAI,CAACrd,SAAS,CAC7Bid,WAAY,GACZM,yBACE15B,EAAQ05B,wBAAwB,EAC/B15B,EAAQwhC,QAAQ,GACf,CAAC,CAAC9wB,EAELuE,EAF+C,EAE3C,CAAC,KAAM,GACXlD,KAAK,CAAC,KAAM,IAGrB,IAAI,CAFE,UAES,CAAC/R,EAAQwhC,QAAQ,CAAG,WAAa,WAAW,CAACz+B,GAC7D,CACH,CAEA,MAAM67B,eAAe77B,CAAa,CAAE,CAClC,IAAMs3B,EAAkBH,EAAoB,OAAEn3B,EAAO4J,OAAQ,IAAI,GAEjE,GAAI,CACF,IAAM80B,EAAkB,MAAM,IAAI,CAAC5xB,UAAU,CAACmF,QAAQ,CAACjS,GAGvD,OAFAs3B,IAEOoH,CACT,CAAE,MAAOzvB,EAAK,CAEZ,MADAqoB,IACMroB,CACR,CACF,CAEAuuB,SAAYxgC,CAAoB,CAAc,CAC5C,IAAIkS,GAAY,EACVkoB,EAAS,KACbloB,GAAY,CACd,EAEA,OADA,IAAI,CAACmoB,GAAG,CAAGD,EACJp6B,IAAKkV,IAAI,CAAC,IAKf,GAJIklB,IAAW,IAAI,CAACC,GAAG,EAAE,CACvB,IAAI,CAACA,GAAG,CAAG,MAGTnoB,EAAW,CACb,IAAMD,EAAW,qBAA4C,CAA5C,MAAU,mCAAV,+DAA2C,EAE5D,OADAA,EAAIC,SAAS,EAAG,EACVD,CACR,CAEA,OAAOylB,CACT,EACF,CAEAjwB,gBACEZ,CAAwB,CACxBW,CAAoB,CACU,CAC9B,GAAM,CAAEX,UAAWU,CAAG,CAAE,CAAG,IAAI,CAACyf,UAAU,CAAC,QAAQ,CAC7CxR,EAAU,IAAI,CAACmsB,QAAQ,CAACp6B,GAE9B,OADAC,EAAIgO,OAAO,CAAGA,EACPzP,CAAAA,EAAAA,EAAAA,mBAAAA,EAA4CwB,EAAK,SACtDiO,YACA3O,EACA+F,OAAQ,IAAI,KACZpF,CACF,EACF,CAEA,IAAIxE,OAAgB,CAClB,OAAO,IAAI,CAAC8hB,KAAK,CAAC9hB,KAAK,CAGzB,IAAIG,UAAmB,CACrB,OAAO,IAAI,CAAC2hB,KAAK,CAAC3hB,QAAQ,CAG5B,IAAI8J,OAAwB,CAC1B,OAAO,IAAI,CAAC6X,KAAK,CAAC7X,KAAK,CAGzB,IAAIF,QAAiB,CACnB,OAAO,IAAI,CAAC+X,KAAK,CAAC/X,MAAM,CAG1B,IAAIoC,QAA6B,CAC/B,OAAO,IAAI,CAAC2V,KAAK,CAAC3V,MAAM,CAG1B,IAAIxB,YAAsB,CACxB,OAAO,IAAI,CAACmX,KAAK,CAACnX,UAAU,CAG9B,IAAIyO,WAAqB,CACvB,OAAO,IAAI,CAAC0I,KAAK,CAAC1I,SAAS,CA/0D7BrU,YACE5E,CAAgB,CAChB8J,CAAqB,CACrB2e,CAAU,CACV,cACE3P,CAAY,YACZnM,CAAU,KACVvI,CAAG,SACHuN,CAAO,WACPjO,CAAS,KACToL,CAAG,CACHiK,cAAY,YACZvO,CAAU,CACVwB,QAAM,SACND,CAAO,eACPoB,CAAa,eACb6L,CAAa,WACbC,CAAS,CAeV,CACD,MAxEF+jB,GAAAA,CAAqB,CAAC,OAEtBD,GAAAA,CAAqB,CAAC,OAgBtB0B,oBAAAA,CAAuB,QAiBfnC,IAAAA,CAAelJ,SA+JvBsL,UAAAA,CAAa,IACX,IA2CI5F,EA3CE,sBAAE2F,CAAoB,CAAE,CAAG,IAAI,CACrC,IAAI,CAACA,oBAAoB,EAAG,EAE5B,IAAM9c,EAAQsa,EAAEta,KAAK,CAErB,GAAI,CAACA,EAAO,CAUV,GAAM,UAAE3hB,CAAQ,CAAE8J,OAAK,CAAE,CAAG,IAAI,CAChC,IAAI,CAACiwB,WAAW,CACd,eACA9b,CAAAA,EAAAA,EAAAA,oBAAAA,EAAqB,CAAEje,SAAUub,GAAAA,EAAAA,WAAAA,EAAYvb,SAAW8J,CAAM,GAC9DrH,CAAAA,EAAAA,EAAAA,MAAAA,KAEF,MACF,CAGA,GAAIkf,EAAMgd,IAAI,CAAE,YACdt7B,OAAOC,QAAQ,CAAC8F,MAAM,GAIxB,GAAI,CAACuY,EAAM0a,GAAG,EAAE,GAOd,IAAI,CAACrwB,MAAM,GAAK2V,EAAM7kB,OAAO,CAACkP,MAAM,EACpC2V,EAAM8G,EAAE,GAAK,IAAI,CAAC7e,MAAM,CAPxB,CAQA,MAKF,GAAM,KAAEtJ,CAAG,IAAEmoB,CAAE,SAAE3rB,CAAO,CAAE2I,KAAG,CAAE,CAAGkc,EAsBlC,IAAI,CAAC2a,IAAI,CAAG72B,EAEZ,GAAM,CAAEzF,UAAQ,CAAE,CAAGgQ,GAAAA,EAAAA,gBAAAA,EAAiB1P,KAKpC,IAAI,CAAC8N,KAAK,EACVqa,IAAOlN,CAAAA,EAAAA,EAAAA,WAAAA,EAAY,IAAI,CAAC3R,MAAM,GAC9B5J,IAAaub,CAAAA,EAAAA,EAAAA,WAAAA,EAAY,IAAI,CAACvb,SAAQ,GACtC,EAME,IAAI,CAACy9B,IAAI,EAAK,EAAD,EAAK,CAACA,IAAI,CAAC9b,EAAAA,GAAQ,IAIhC,CAAC0V,MAAM,CACT,eACA/2B,EACAmoB,EACAxpB,OAAOwP,MAAM,CAA2C,CAAC,EAAG3R,EAAS,CACnE8R,QAAS9R,EAAQ8R,OAAO,EAAI,IAAI,CAACwtB,QAAQ,CACzCpwB,OAAQlP,EAAQkP,MAAM,EAAI,IAAI,CAACmB,aAAa,CAE5CwB,GAAI,CACN,GACAmqB,EAEJ,EA3NE,IAAMj5B,EAAQmc,CAAAA,EAAAA,EAAAA,mBAAAA,EAAoBhc,GAGlC,IAAI,CAAC6jB,UAAU,CAAG,CAAC,EAIF,WAAW,CAAxB7jB,IACF,IAAI,CAAC6jB,UAAU,CAAChkB,EAAM,CAAG,WACvB6D,EACA0V,SAAS,EACT7U,MAAOuU,MACPhK,EACAP,QAASuK,GAAgBA,EAAavK,OAAO,CAC7C4sB,QAASriB,GAAgBA,EAAaqiB,OAAO,CAC/C,EAGF,IAAI,CAACtX,UAAU,CAAC,QAAQ,CAAG,CACzBngB,UAAWU,EACX6N,YAAa,EAEZ,EAKH,IAAI,CAACoN,MAAM,CAAGV,EAAOU,MAAM,CAE3B,IAAI,CAAC1S,UAAU,CAAGA,EAGlB,IAAMiyB,EACJt9B,CAAAA,EAAAA,EAAAA,cAAAA,EAAetB,IAAamB,KAAKuO,aAAa,CAAC4B,UAAU,CA6CzD,GA3CF,IAAI,CAACxG,QAAQ,CAAyC,EAAtC0C,CAChB,IAAI,CAD8C,GAC1C,CAAGuL,EACX,IAAI,CAACme,GAAG,CAAG,KACX,IAAI,CAACsH,QAAQ,CAAG7sB,EAGhB,IAAI,CAACvD,KAAK,EAAG,EACb,IAAI,CAACmrB,cAAc,EAAG,EACtB,IAAI,CAAC7vB,OAAO,CAAG,CAAC,CACdvI,CAAAA,KAAKuO,aAAa,CAACmvB,IAAI,EACvB19B,KAAKuO,aAAa,CAACovB,GAAG,EACtB39B,KAAKuO,aAAa,CAACqvB,qBAAqB,EACvC59B,KAAKuO,aAAa,CAACsvB,MAAM,EAAI,CAAC79B,KAAKuO,aAAa,CAACuvB,GAAG,EACpD,CAACL,GACA,CAACz9B,KAAKmC,QAAQ,CAACgL,MAAM,CACR4wB,CAIf,EAJE,CAAC1xB,CAIC,CAACzB,OAAO,CAAGA,EACf,IAAI,CAACoB,aAAa,CAAGA,EACrB,IAAI,CAAC6L,aAAa,CAAGA,EACrB,IAAI,CAACugB,cAAc,CAAG,CAAC,CAACxpB,CAAAA,EAAAA,EAAAA,kBAAAA,EACtBiJ,EACA7X,KAAKmC,QAAQ,CAACH,QAAQ,EAI1B,IAAI,CAACwe,KAAK,CAAG,OACX9hB,WACAG,QACA8J,EACAF,OAAQg1B,EAAoB5+B,EAAWyoB,EACvCxP,UAAW,CAAC,CAACA,EACbjN,OAA0CA,CAAlCwB,KAA+B,GAAYjE,CAASA,GAC5DiB,CACF,EAEA,IAAI,CAAC0O,gCAAgC,CAAG3G,QAAQC,OAAO,EAAC,GAKlD,CAACiW,EAAG/gB,UAAU,CAAC,MAAO,CAGxB,IAAM5K,EAA6B,CAAEkP,QAAO,EACtCpC,EAASnH,CAAAA,EAAAA,EAAAA,MAAAA,IAEf,IAAI,CAACyW,gCAAgC,CAAGma,EAAkB,CACxD5pB,OAAQ,IAAI,CACZuC,gBACApC,CACF,GAAGmI,IAAI,CAAC,IAGJjV,EAAgBo8B,kBAAkB,CAAGzQ,IAAOzoB,EAE9C,IAAI,CAAC+5B,WAAW,CACd,eACAxE,EACI3rB,EACAqU,CAAAA,EAAAA,EAAAA,oBAAAA,EAAqB,CACnBje,SAAUub,CAAAA,EAAAA,EAAAA,WAAAA,EAAYvb,SACtB8J,CACF,GACJF,EACA9M,GAEKy4B,GAEX,CAEAlyB,OAAO87B,gBAAgB,CAAC,WAAY,IAAI,CAACT,UAAU,CAUvD,CAwrDF,CAh4DqB/f,EA6CZU,MAAAA,CAAmC3R,CAAAA,EAAAA,EAAAA,OAAAA,uHCvrB/BkqB,qCAAAA,IAAN,OAAMA,EAiBX,OAAOwH,KAAKC,CAAe,CAAEpH,CAA8B,CAAE,CAAhCA,KAAAA,IAAAA,IAAAA,EAnBJ,IAmBgBqH,EACvC,IADuCA,EACxB,IAAI1H,EAAYyH,EAAMh/B,MAAM,CAAE43B,GAE7C,IAAK,IAAMsH,KAAQF,EACjBr5B,EAAOme,EADiB,CACd,CAACob,GAEb,OAAOv5B,CACT,CAEAw5B,QAAS,CAwBP,MAvBa,CACXxH,SAAU,IAAI,CAACA,QAAQ,CACvBC,UAAW,IAAI,CAACA,SAAS,CACzBwH,QAAS,IAAI,CAACA,OAAO,CACrB1H,UAAW,IAAI,CAACA,SAAS,CACzB2H,SAAU,IAAI,CAACA,QACjB,CAkBF,CAEAxH,OAAO3D,CAAyC,CAAE,CAChD,IAAI,CAACyD,QAAQ,CAAGzD,EAAKyD,QAAQ,CAC7B,IAAI,CAACC,SAAS,CAAG1D,EAAK0D,SAAS,CAC/B,IAAI,CAACwH,OAAO,CAAGlL,EAAKkL,OAAO,CAC3B,IAAI,CAAC1H,SAAS,CAAGxD,EAAKwD,SAAS,CAC/B,IAAI,CAAC2H,QAAQ,CAAGnL,EAAKmL,QAAQ,CAG/Bvb,IAAIob,CAAY,CAAE,CACG,IAAI,CAACI,aAAa,CAACJ,GAC3Bh6B,OAAO,CAAE0J,IAClB,IAAI,CAACywB,QAAQ,CAACzwB,EAAK,CAAG,CACxB,EACF,CAEA0pB,SAAS4G,CAAY,CAAE,CAErB,OADmB,IAAI,CAACI,aAAa,CAACJ,GACpB96B,KAAK,CAAC,GAAU,IAAI,CAACi7B,QAAQ,CAACzwB,EAAK,CACvD,CAEA0wB,cAAcJ,CAAY,CAAE,CAC1B,IAAMK,EAAa,EAAE,CACrB,IAAK,IAAIj3B,EAAI,EAAGA,GAAK,IAAI,CAACovB,SAAS,CAAEpvB,IAAK,CACxC,IAAMsG,EA1FZ,SAAS4wB,CAAuB,EAC9B,IAAIjc,EAAI,EACR,IAAK,IAAIjb,EAAI,EAAGA,EAAIiqB,EAAIvyB,MAAM,CAAEsI,IAAK,EAE/B0P,KAAKynB,IAAI,CAAClc,EADJgP,EAAImN,UAAU,CAACp3B,GACJ,YACrBib,GAAKA,IAAM,GACXA,EAAIvL,KAAKynB,IAAI,CAAClc,EAAG,YAEnB,OAAOA,IAAM,CACf,EAiFgC,GAAE2b,EAAO52B,GAAO,IAAI,CAAC82B,OAAO,CACtDG,EAAW14B,IAAI,CAAC+H,EAClB,CACA,OAAO2wB,CACT,CAzEAh7B,YAAYozB,CAAgB,CAAEC,MAAsC,CAAE,CACpE,IAAI,CAACD,QAAQ,CAAGA,EAChB,IAAI,CAACC,SAAS,CAAGA,EACjB,IAAI,CAACwH,OAAO,CAAGpnB,KAAK2nB,IAAI,CACtB,CAAEhI,CAAAA,EAAW3f,KAAK4nB,GAAG,CAAChI,EAAAA,CAAAA,CAAS,CAAM5f,KAAK4nB,GAAG,CAAC,GAAK5nB,KAAK4nB,GAAG,CAAC,IAAC,CAE/D,IAAI,CAAClI,SAAS,CAAG1f,KAAK2nB,IAAI,CAAE,IAAI,CAACP,OAAO,CAAGzH,EAAY3f,KAAK4nB,GAAG,CAAC,IAChE,IAAI,CAACP,QAAQ,CAAG,MAAU,IAAI,CAACD,OAAO,EAAES,IAAI,CAAC,EAC/C,CAkEF,kHCxFa1pB,qCAAAA,aARuB,WACP,OAOhBA,EAAS,OAAC,UAAEnQ,CAAQ,MAAEoQ,CAAI,CAAe,GAC9C,CAAC0pB,EAAYC,EAAc,CAAGC,GAAAA,EAAAA,QAAAA,EAA6B,MAWjE,MATAC,CAAAA,EAAAA,EAAAA,SAAAA,EAAU,KACR,IAAMC,EAAUtjC,SAASmY,aAAa,CAACqB,GAGvC,OAFAxZ,SAASsqB,IAAI,CAAChS,WAAW,CAACgrB,GAC1BH,EAAcG,GACP,KACLtjC,SAASsqB,IAAI,CAACnR,WAAW,CAACmqB,EAC5B,CACF,EAAG,CAAC9pB,EAAK,EAEF0pB,EAAaK,CAAAA,EAAAA,EAAAA,MAAbL,MAAaK,EAAan6B,EAAU85B,CAApCA,EAAkD,IAC3D,qVCnBa1uB,qCAAAA,KAAN,IAAMA,EAURvT,aAZa,QAYbA,OAAK,CAACC,aAAa,CAAC,CAAC,+BCVnB,SAASsgB,EACdpF,CAA6B,EAE7B,IAAMvP,EAAwB,CAAC,EAC/B,IAAK,GAAM,CAACrE,EAAK4E,EAAM,GAAIgP,EAAarB,OAAO,GAAI,CACjD,IAAM7a,EAAW2M,CAAK,CAACrE,EAAI,MACH,IAAbtI,EACT2M,CAAK,CAACrE,EAAI,CAAG4E,EACJmT,MAAMC,OAAO,CAACtgB,GACvBA,EAAS+J,IAAI,CAACmD,CADoB,EAGlCP,CAAK,CAACrE,EAAI,CAAG,CAACtI,EAAUkN,EAAM,CAGlC,OAAOP,CACT,CAEA,SAAS22B,EAAuBrjB,CAAc,QAC5C,UAAI,OAAOA,EACFA,GAIN,YAA6B,KAAtBA,GAAuBuQ,MAAMvQ,EAAAA,GACpB,WAAjB,OAAOA,EAIA,GAFA5O,OAAO4O,EAIlB,CAEO,SAAS1O,EAAuB5E,CAAqB,EAC1D,IAAMuP,EAAe,IAAIxP,gBACzB,IAAK,GAAM,CAACpE,EAAK4E,EAAM,GAAIpL,OAAO+Y,OAAO,CAAClO,GACxC,GAAI0T,CAD4C,KACtCC,OAAO,CAACpT,GAChB,IAAK,CADmB,GACbk1B,KAAQl1B,EACjBgP,EAAaqnB,EADW,IACL,CAACj7B,EAAKg7B,EAAuBlB,SAGlDlmB,EAAanR,GAAG,CAACzC,EAAKg7B,EAAuBp2B,IAGjD,OAAOgP,CACT,CAEO,SAAS5K,EACdkyB,CAAuB,EACvB,2BAAGC,EAAH,6BAAGA,CAAAA,CAAH,iBAAsC,CAEtC,IAAK,IAAMvnB,KAAgBunB,EAAkB,CAC3C,IAAK,IAAMn7B,KAAO4T,EAAahU,IAAI,GAAI,EAC9Bsf,MAAM,CAAClf,GAGhB,IAAK,GAAM,CAACA,EAAK4E,EAAM,GAAIgP,EAAarB,OAAO,GAAI,EAC1C0oB,MAAM,CAACj7B,EAAK4E,EAEvB,CAEA,OAAOs2B,CACT,wIAfgBlyB,MAAM,mBAANA,GA9CAgQ,sBAAsB,mBAAtBA,GAgCA/P,sBAAsB,mBAAtBA,iCC7BT,SAASkN,EAAUne,CAAY,EACpC,IAAMojC,EAAYpjC,EAAKgJ,OAAO,CAAC,KACzBq6B,EAAarjC,EAAKgJ,OAAO,CAAC,KAC1Bs6B,EAAWD,EAAa,CAAC,GAAMD,EAAAA,CAAY,GAAKC,EAAaD,CAAAA,CAAAA,CAAQ,OAE3E,GAAgBA,EAAY,CAAC,EACpB,CADuB,SAElBpjC,EAAKgG,SAAS,CAAC,EAAGs9B,EAAWD,EAAaD,GACpD/2B,MAAOi3B,EACHtjC,EAAKgG,SAAS,CAACq9B,EAAYD,EAAY,CAAC,EAAIA,EAAYt3B,QACxD,GACJ0F,KAAM4xB,EAAY,CAAC,EAAIpjC,EAAKyG,KAAK,CAAC28B,GAAa,EACjD,EAGK,CAAE7gC,SAAUvC,EAAMqM,MAAO,GAAImF,KAAM,EAAG,CAC/C,sFAhBgB2M,qCAAAA,iCCmBD,SAASlO,IACtB,IAAMqa,EAAkC9oB,OAAO+hC,MAAM,CAAC,MAEtD,MAAO,CACLxhB,GAAG/I,CAAY,CAAEwqB,CAAgB,EAC7BlZ,CAAAA,CAAG,CAACtR,EAAK,GAAKsR,CAAG,CAACtR,EAAK,CAAG,IAAC,CAAC,IAAO,CAACwqB,EACxC,EAEA/e,IAAIzL,CAAY,CAAEwqB,CAAgB,EAC5BlZ,CAAG,CAACtR,EAAK,EAAE,CACV,CAACA,EAAK,CAACjQ,MAAM,CAACuhB,CAAG,CAACtR,EAAK,CAAChQ,OAAO,CAACw6B,KAAa,EAAG,EAEvD,EAEA9e,KAAK1L,CAAY,EAAE,2BAAGyqB,EAAH,6BAAGA,CAAAA,CAAH,iBAAc,EAE7BnZ,CAAG,CAACtR,EAAK,EAAI,IAAIvS,KAAK,GAAG2C,GAAG,CAAC,IAC7Bo6B,KAAWC,EACb,EACF,CACF,CACF,oFArBA,qCAAwBxzB,iCCdxB,aAIA,QACA,CAdA,qCAA6C,CAC7C,QACA,CAAC,EACD,4DAAoE,CACpE,cACA,eACA,QACA,CACA,CAAC,EAAC,uHCFc+jB,qCAAAA,aAPU,OAOnB,SAASA,EAAch0B,CAAY,CAAE0jC,CAAe,EACzD,GAAI,CAAC1jC,EAAKiK,UAAU,CAAC,MAAQ,CAACy5B,EAC5B,MADoC,CAC7B1jC,EAGT,GAAM,UAAEuC,CAAQ,CAAE8J,OAAK,MAAEmF,CAAI,CAAE,CAAG2M,CAAAA,EAAAA,EAAAA,SAAAA,EAAUne,GAC5C,MAAQ,GAAEuC,EAAWmhC,EAASr3B,EAAQmF,CACxC,4HCJgB1D,qCAAAA,aAVc,OAUvB,SAASA,EAAiB9N,CAAY,CAAE4B,CAAc,EAa3D,GAAI,CAACwL,CAAAA,EAAAA,EAAAA,aAAAA,EAAcpN,EAAM4B,GACvB,MADgC,CACzB5B,EAIT,IAAM2jC,EAAgB3jC,EAAKyG,KAAK,CAAC7E,EAAOgB,MAAM,SAG9C,EAAkBqH,UAAU,CAAC,KACpB05B,CAD0B,CAM3B,IAAGA,CACb,gCClCO,SAAStxB,EAAerS,CAAY,SAQPA,CAKpC,2FAbgBqS,qCAAAA,OAJY,mYCyX5B,OAAqB,mBAArB,GA7NgBorB,sBAAsB,mBAAtBA,GAgCA5qB,gBAAgB,mBAAhBA,kDA1LK,gBAC0C,YAE5B,WACI,WACH,OAE9B+wB,EAAc,IAAIl5B,IAClBm5B,EAAY,IAAIljC,IAiBhBmjC,EAAoB,IAOxB,GAAIzqB,EAAAA,OAAQ,CAAC0qB,OAAO,CAAE,YACpBC,EAAYl8B,OAAO,CAAC,IAClBuR,EAAAA,OAAQ,CAAC0qB,OAAO,CAACE,EAAY,CAAEjZ,GAAI,OAAQ,EAC7C,EASiC,EACjC,IAAInT,EAAOrY,SAASqY,IAAI,CACxBmsB,EAAYl8B,OAAO,CAAC,IAClB,IAAImgB,EAAOzoB,SAASmY,aAAa,CAAC,QAElCsQ,EAAKjP,IAAI,CAAG,WACZiP,EAAKiD,GAAG,CAAG,aACXjD,EAAKniB,IAAI,CAAGm+B,EAEZpsB,EAAKC,WAAW,CAACmQ,EACnB,EACF,CACF,EAEMic,EAAa,IACjB,GAAM,KACJ1a,CAAG,IACHrP,CAAE,QACFgqB,EAAS,KAAO,CAAC,SACjBC,EAAU,IAAI,yBACdtd,CAAuB,UACvBle,EAAW,EAAE,UACby7B,EAAW,kBAAkB,SAC7BC,CAAO,aACPN,CAAW,CACZ,CAAGl9B,EAEEsxB,EAAWje,GAAMqP,EAGvB,GAAI4O,GAAYyL,EAAU9hC,GAAG,CAACq2B,GAC5B,OAIF,CALyC,EAKrCwL,EAAY7hC,GAAG,CAACynB,GAAM,CACxBqa,EAAUnd,GAAG,CAAC0R,GAGdwL,EAAYv6B,GAAG,CAACmgB,GAAKlV,IAAI,CAAC6vB,EAAQG,GAClC,MACF,CAGA,IAAMC,EAAY,KAEZH,GACFA,IAGFP,EAJa,GAIA,CAACzL,EAChB,EAEM3mB,EAAKjS,SAASmY,aAAa,CAAC,UAE5B6sB,EAAc,IAAI1vB,QAAc,CAACC,EAAS+B,KAC9CrF,EAAGiwB,gBAAgB,CAAC,OAAQ,SAAUlD,CAAC,EACrCzpB,IACIovB,GACFA,EAAO/zB,GADG,CACC,CAAC,IAAI,CAAEouB,GAEpB+F,GACF,GACA9yB,EAAGiwB,gBAAgB,CAAC,QAAS,SAAUlD,CAAC,EACtC1nB,EAAO0nB,EACT,EACF,GAAGptB,KAAK,CAAC,SAAUotB,CAAC,EACd8F,GACFA,EAAQ9F,EAEZ,EAHe,CAKX1X,GAEFrV,EAAGsV,SAAS,CAAID,EAAwBE,MAAM,EAAe,GAE7Dud,KACS37B,GACT6I,EAAGO,KADgB,MACL,CACQ,UAApB,OAAOpJ,EACHA,EACAmX,MAAMC,OAAO,CAACpX,GACZA,EAASlC,IAAI,CAAC,IACd,GAER69B,KACS/a,IACT/X,CADc,CACX+X,GAAG,CAAGA,EAIToa,EAAYn5B,GAAG,CAAC+e,EAAKgb,IAGvB3d,CAAAA,EAAAA,EAAAA,sBAAAA,EAAuBpV,EAAI3K,GAEV,UAAU,CAAvBu9B,GACF5yB,EAAGmG,YAAY,CAAC,OAAQ,kBAG1BnG,EAAGmG,YAAY,CAAC,eAAgBysB,GAG5BL,GACFF,EAAkBE,GAGpBxkC,KAJiB,IAIRsqB,IAAI,CAAChS,WAAW,CAACrG,EAC5B,EAEO,SAASgsB,EAAuB32B,CAAkB,EACvD,GAAM,UAAEu9B,EAAW,kBAAkB,CAAE,CAAGv9B,EACzB,cAAc,CAA3Bu9B,EACFz+B,OAAO87B,gBAAgB,CAAC,OAAQ,KAC9BhZ,CAAAA,EAAAA,EAAAA,mBAAAA,EAAoB,IAAMwb,EAAWp9B,GACvC,GAEAo9B,EAAWp9B,EAEf,CAuBO,SAAS+L,EAAiB4xB,CAAgC,EAC/DA,EAAkB38B,OAAO,CAAC21B,GAXV,IACXj+B,SAAS2X,gBAAgB,CAAC,yCAC1B3X,SAAS2X,gBAAgB,CAAC,qCAC9B,CACOrP,OAAO,CAAE2hB,IACf,IAAM2O,EAAW3O,EAAOtP,EAAE,EAAIsP,EAAOpS,YAAY,CAAC,OAClDwsB,EAAUnd,GAAG,CAAC0R,EAChB,EAMF,CAOA,SAASsM,EAAO59B,CAAkB,EAChC,GAAM,IACJqT,CAAE,KACFqP,EAAM,EAAE,QACR2a,EAAS,KAAO,CAAC,SACjBC,EAAU,IAAI,UACdC,EAAW,kBAAkB,SAC7BC,CAAO,aACPN,CAAW,CACX,GAAGW,EACJ,CAAG79B,EAGE,eAAE89B,CAAa,CAAE3b,SAAO,UAAE3V,CAAQ,QAAEuxB,CAAM,OAAErtB,CAAK,CAAE,CACvD6K,CAAAA,EAAAA,EAAAA,UAAAA,EAAWrO,EAAAA,kBAAkB,EA4BzB8wB,EAAyBp4B,CAAAA,EAAAA,EAAAA,MAAAA,GAAO,GAEtCm2B,CAAAA,EAAAA,EAAAA,SAAAA,EAAU,KACR,IAAMzK,EAAWje,GAAMqP,EAClBsb,EAAuBh4B,OAAO,EAAE,CAE/Bs3B,GAAWhM,GAAYyL,EAAU9hC,GAAG,CAACq2B,IACvCgM,IAGFU,EAAuBh4B,CAJ6B,MAItB,EAAG,EAErC,EAAG,CAACs3B,EAASjqB,EAAIqP,EAAI,EAErB,IAAMub,EAA4Br4B,CAAAA,EAAAA,EAAAA,MAAAA,GAAO,GAoCzC,GAlCAm2B,CAAAA,EAAAA,EAAAA,SAAAA,EAAU,KACR,GAAI,CAACkC,EAA0Bj4B,OAAO,CAAE,CACtC,GAAiB,oBAAoB,CAAjCu3B,EACFH,EAAWp9B,OACW,cAAc,CAA3Bu9B,IA5Fa,YAAY,CAApC7kC,SAASwlC,UAAU,CACrBtc,CAAAA,EAAAA,EAAAA,mBAAAA,EAAoB,IAAMwb,MAE1Bt+B,KAFqCkB,EAE9B46B,gBAAgB,CAAC,OAAQ,KAC9BhZ,CAAAA,EAAAA,EAAAA,mBAAAA,EAAoB,IAAMwb,EAyFTp9B,GAxFnB,IA2FEi+B,EA5FqCj+B,OA4FJ,EAAG,CACtC,CACF,EAAG,CAACA,EAAOu9B,EAAS,GAEH,yBAAoC,WAAbA,CAAa,GAAU,CACzDO,GACF3b,CAAO,CAACob,EAAS,CAAIpb,CAAAA,CAAO,CAACob,EAAS,EADrB,EACyB,EAAI9T,MAAM,CAAC,CACnD,IACEpW,MACAqP,SACA2a,UACAC,UACAE,EACA,GAAGK,CAAS,EAEf,EACDC,EAAc3b,IACL3V,GAAYA,IAErBuwB,EAAUnd,GAAG,CAACvM,EAFmB,CAEbqP,GACXlW,GAAY,CAACA,KACtB4wB,EAAWp9B,IAKX+9B,CANkC,CAM1B,CAkBV,GARIb,GACFA,EAAYl8B,OAAO,CADJ,IAEbuR,EAAAA,OAAQ,CAAC0qB,OAAO,CAACkB,EAAU,CAAEja,GAAI,OAAQ,EAC3C,GAKe,qBAAqB,CAAlCqZ,EACF,GAAI,CAAC7a,EASH,GATQ,IAEJmb,EAAU7d,uBAAuB,EAAE,CAErC6d,EAAU/7B,QAAQ,CAAG+7B,EAAU7d,uBAAuB,CACnDE,MAAM,CACT,OAAO2d,EAAU7d,uBAAuB,EAIxC,UAAC2C,SAAAA,CACCjS,MAAOA,EACPsP,wBAAyB,CACvBE,OAAS,0CAAyCzf,KAAKC,SAAS,CAAC,CAC/D,EACA,CAAE,GAAGm9B,CAAS,IAAExqB,CAAG,EACpB,EAAE,GACL,SAgBJ,OAXAd,EAAAA,OAAQ,CAAC6rB,OAAO,CACd1b,EACAmb,EAAUQ,SAAS,CACf,CACEna,GAAI,SACJma,UAAWR,EAAUQ,SAAS,OAC9B3tB,EACAqS,YAAa8a,EAAU9a,WAAW,EAEpC,CAAEmB,GAAI,eAAUxT,EAAOqS,YAAa8a,EAAU9a,WAAW,GAG7D,UAACJ,SAAAA,CACCjS,MAAOA,EACPsP,wBAAyB,CACvBE,OAAS,0CAAyCzf,KAAKC,SAAS,CAAC,CAC/DgiB,EACA,CAAE,GAAGmb,CAAS,IAAExqB,CAAG,EACpB,EAAE,GACL,IAIgB,oBAAoB,CAAjCkqB,GACL7a,GAEFnQ,EAAAA,OAAQ,CAAC6rB,OAAO,CACd1b,EACAmb,EAAUQ,SAAS,CACf,CACEna,GAAI,SACJma,UAAWR,EAAUQ,SAAS,CAC9B3tB,QACAqS,YAAa8a,EAAU9a,WACzB,EACA,CAAEmB,GAAI,eAAUxT,EAAOqS,YAAa8a,EAAU9a,WAAW,EAIrE,CAEA,OAAO,IACT,CAEAroB,OAAOmgB,cAAc,CAAC+iB,EAAQ,eAAgB,CAAE93B,OAAO,CAAK,OAE5D,EAAe83B,qPCrXR,SAASriC,EAAmBrC,CAAY,EAC7C,OAAOA,EAAKiK,UAAU,CAAC,KAAOjK,EAAQ,IAAGA,CAC3C,+FAFgBqC,qCAAAA,yICGHnC,qCAAAA,aAPuB,WACV,OAMbA,EAA6B,IACxC,GAAI,CAACF,EAAKiK,UAAU,CAAC,KACnB,GAD2B8F,IACpB/P,EAGT,GAAM,EAJiE,QAI/DuC,CAAQ,OAAE8J,CAAK,MAAEmF,CAAI,CAAE,CAAG2M,GAAAA,EAAAA,SAAAA,EAAUne,GAW5C,MAAQ,GAAEue,CAAAA,EAAAA,EAAAA,mBAAAA,EAAoBhc,GAAY8J,EAAQmF,CACpD,uPCxBA,cACA,0CAEA,kBACA,cAEA,qBACA,YACA,EAAK,GACL,CACA,gBACA,gCACA,6DAAuF,WAEvF,WAEA,+BAEA,OAAmB,gBACnB,yDAEA,eACA,6DACA,iDACA,gDACA,UAQA,OAJA,YAEA,cAEA,CACA,qJC9BayC,qCAAAA,wBAJK,YAEiB,OAEtBA,EACXxT,EAAAA,OAAK,CAACC,aAAa,CAAsBic,EAAAA,kBAAkB,uKCchD1D,cAAc,mBAAdA,GA6Cb,OAA6B,mBAA7B,uCAhEkB,YACQ,OAEpBmsB,EAAkD,CACtDC,OAAQ,EACRC,KAAM,gBACNC,OAAQ,MACRC,OAAQ,OACRC,SAAU,SACVC,QAAS,EACTC,SAAU,WACVC,IAAK,EACLC,MAAO,MAGPC,WAAY,SACZC,SAAU,QACZ,EAEa9sB,EAAiB,KAC5B,GAAM,QAAE9M,CAAM,CAAE,CAAGgV,CAAAA,EAAAA,EAAAA,SAAAA,IACb,CAAC6kB,EAAmBC,EAAqB,CAAGxlC,EAAAA,OAAK,CAACmiC,QAAQ,CAAC,IAI3DsD,EAAuBzlC,EAAAA,OAAK,CAACiM,MAAM,CAACP,GA2B1C,OAnBA1L,EAAAA,OAAK,CAACoiC,CAmBN,QAnBe,CACb,KAEE,GAAIqD,EAAqBp5B,OAAO,GAAKX,EAGrC,GAFA+5B,EAAqBp5B,CADwB,MACjB,CAAGX,EAE3B3M,SAAS8mB,KAAK,CAChB2f,CADkB,CACGzmC,SAAS8mB,KAAK,MAC9B,KAEW6f,EADhB,IAAMA,EAAa3mC,SAAS+X,aAAa,CAAC,MAG1C0uB,EAAqBtiB,CAFgB,OAArBwiB,EAAAA,MAAAA,EAAAA,KAAAA,EAAAA,EAAYC,SAAAA,EAAZD,EAAyBA,MAAAA,EAAAA,KAAAA,EAAAA,EAAYn0B,WAAW,GAEhC7F,EAClC,CACF,EACA,CACCA,EAAO,EAIR,UAACiH,IAAAA,CACCizB,YAAU,YAAY,GACnB,2BACHC,KAAK,EAFsD,MAG3D3mC,MAAOylC,WAENY,GAGP,EAEA,EAAe/sB,gVC1DC+E,qCAAAA,aANU,OAMnB,SAASA,EAAche,CAAY,CAAE4B,CAAe,EACzD,GAAI,CAAC5B,EAAKiK,UAAU,CAAC,MAAQ,CAACrI,EAC5B,MADoC,CAC7B5B,EAGT,GAAM,UAAEuC,CAAQ,OAAE8J,CAAK,MAAEmF,CAAI,CAAE,CAAG2M,CAAAA,EAAAA,EAAAA,SAAAA,EAAUne,GAC5C,MAAQ,GAAE4B,EAASW,EAAW8J,EAAQmF,CACxC,kCCTI+0B,EA4BG,SAASpd,EACdtmB,CAAW,MAEJ2jC,EAAP,MAAOA,CAAAA,OAAAA,EAzBT,SAASA,EACP,GAAI,KAAkB,IAAXD,EAAyD,KAEhE3gC,EADF2gC,EACE3gC,CAAAA,MAFwCA,CAExCA,EAAAA,GAFmD,IAE5C6gC,YAAAA,EAAY,OAAnB7gC,EAAqB8gC,YAAY,CAAC,SAAU,CAC1CC,WAAax2B,GAAUA,EACvBy2B,aAAc,GAAWz2B,EACzB02B,gBAAiB,GAAW12B,CAC9B,KAAM,IACV,CAEA,OAAOo2B,CACT,GAcSC,CAAAA,CAAAA,KAAAA,EAAAA,EAAaK,eAAe,CAAChkC,EAAAA,CAAAA,EAAQA,CAC9C,2GAJgBsmB,qCAAAA,iYCJA3W,SAAS,mBAATA,GA6DAgO,oBAAoB,mBAApBA,GAfHsmB,aAAa,mBAAbA,wBAlDgB,QAEvBC,EAAmB,yBAElB,SAASv0B,EAAUw0B,CAAiB,EACzC,GAAI,MAAEC,CAAI,CAAEvhC,UAAQ,CAAE,CAAGshC,EACrBvhC,EAAWuhC,EAAOvhC,QAAQ,EAAI,GAC9BlD,EAAWykC,EAAOzkC,QAAQ,EAAI,GAC9BiP,EAAOw1B,EAAOx1B,IAAI,EAAI,GACtBnF,EAAQ26B,EAAO36B,KAAK,EAAI,GACxB66B,GAAuB,EAE3BD,EAAOA,EAAOppB,mBAAmBopB,GAAMnkC,OAAO,CAAC,OAAQ,KAAO,IAAM,GAEhEkkC,EAAOE,IAAI,CACbA,CADe,CACRD,EAAOD,EAAOE,IAAI,CAChBxhC,IACTwhC,EAAOD,EAAQ,EAACvhC,EAASsD,CAAV,MAAiB,CAAC,KAAQ,IAAGtD,EAAS,IAAKA,CAAAA,CAAAA,CAAO,EACtDC,IAAI,EAAE,CACfuhC,GAAQ,IAAMF,EAAOrhC,IAAAA,GAIrB0G,GAA0B,UAAjB,OAAOA,IAClBA,EAAQ0E,OAAOo2B,EAAYl2B,sBAAsB,CAAC5E,GAAAA,EAGpD,IAAIwE,EAASm2B,EAAOn2B,MAAM,EAAKxE,GAAU,IAAGA,GAAY,GAoBxD,OAlBI5G,GAAY,CAACA,EAASyE,QAAQ,CAAC,OAAMzE,GAAY,KAGnDuhC,EAAOI,OAAO,EACZ,EAAC3hC,GAAYshC,EAAiB3iC,IAAI,CAACqB,EAAAA,CAAAA,CAAQ,EAAe,IAATyhC,GACnD,EACO,MAAQA,CAAAA,EAAQ,IAAC,GACQ,MAAhB3kC,CAAQ,CAAC,EAAE,GAAUA,EAAW,IAAMA,CAAAA,GAC7C,IACT2kC,EADgB,EACT,EAGL11B,GAAoB,MAAZA,CAAI,CAAC,EAAE,GAAUA,EAAO,IAAMA,CAAAA,EACtCX,GAAUA,OAAM,CAAC,EAAE,GAAUA,EAAS,IAAMA,CAAAA,EAKxC,GAAEpL,EAAWyhC,GAHrB3kC,EAAWA,EAASO,OAAO,CAAC,GAGW+N,KAHFgN,mBAAAA,GACrChN,EAASA,EAAO/N,OAAO,CAAC,IAAK,QAEmB0O,CAClD,CAEO,IAAMs1B,EAAgB,CAC3B,OACA,OACA,OACA,WACA,OACA,OACA,WACA,OACA,WACA,QACA,SACA,UACD,CAEM,SAAStmB,EAAqB3d,CAAc,EAajD,OAAO2P,EAAU3P,EACnB,2HCxFgB4c,qCAAAA,aAdY,MAcrB,SAASA,EAAgB,CAGV,EAHU,OAC9ByS,CAAE,QACF1lB,CAAM,CACc,CAHU,EAI9B,OAAO,IACL,IAAMqwB,EAAa3K,EAAGmV,IAAI,CAAC9kC,GAC3B,GAAI,CAACs6B,EAAY,OAAO,EAExB,IAAMyK,EAAS,IACb,GAAI,CACF,OAAOhH,mBAAmB3gB,EAC5B,CAAE,QAAM,CACN,MAAM,qBAAyC,CAAzC,IAAItb,EAAAA,WAAW,CAAC,0BAAhB,+DAAwC,EAChD,CACF,EAEMqb,EAAiB,CAAC,EACxB,IAAK,GAAM,CAAC1X,EAAKu/B,EAAM,GAAI/lC,OAAO+Y,OAAO,CAAC/N,GAAS,CACjD,IAAMkU,EAAQmc,CAAU,CAAC0K,EAAM1V,GAAG,CAAC,MACrB/lB,IAAV4U,IACE6mB,EAAM3nB,CADa,KACP,CACdF,CAAM,CAAC1X,EAAI,CAAG0Y,EAAM5e,KAAK,CAAC,KAAKsH,GAAG,CAAC,GAAWk+B,EAAO5f,IAErDhI,CAAM,CAAC1X,EAAI,CAAGs/B,EAAO5mB,GAG3B,CAEA,OAAOhB,CACT,CACF,yHC1Ca5L,qCAAAA,KAAN,IAAMA,EAAgBrT,aAHX,QAGWA,OAAK,CAACC,aAAa,CAAoB,kCCD7D,SAAS4R,EACdk1B,CAAqC,CACrC9hC,CAAiB,CACjB8I,CAAuB,EAEvB,GAAKg5B,CAAD,CAMJ,IAAK,IAAM1F,GANO,EAEdtzB,IACFA,EAAiBA,EAAe6P,QADd,GACyB,IAG1BmpB,GAAa,KAEP1F,EAIrBA,EAHF,GACEp8B,KAFI+hC,OAAiB3F,CAER2F,CAFQ3F,EAAK/F,MAAAA,EAAM,OAAX+F,EAAahgC,KAAK,CAAC,IAAK,EAAE,CAAC,EAAE,CAACuc,WAAW,KAG9D7P,IAAmBszB,EAAKpyB,aAAa,CAAC2O,WAAW,YACjDyjB,EAAAA,EAAKxzB,OAAAA,EAAO,OAAZwzB,EAAc1O,IAAI,CAAC,GAAY7kB,EAAO8P,WAAW,KAAO7P,EAAAA,CAAAA,CAExD,EADA,KACOszB,CAEX,CACF,+FAtBgBxvB,qCAAAA", "sources": ["webpack://_N_E/../../src/shared/lib/router/utils/handle-smooth-scroll.ts", "webpack://_N_E/./node_modules/next/dist/lib/is-api-route.js", "webpack://_N_E/../../src/client/add-locale.ts", "webpack://_N_E/../../src/shared/lib/app-router-context.shared-runtime.ts", "webpack://_N_E/../../src/shared/lib/runtime-config.external.ts", "webpack://_N_E/../../src/client/components/http-access-fallback/http-access-fallback.ts", "webpack://_N_E/../../src/shared/lib/router/utils/app-paths.ts", "webpack://_N_E/../../src/client/react-client-callbacks/on-recoverable-error.ts", "webpack://_N_E/../../src/shared/lib/hooks-client-context.shared-runtime.ts", "webpack://_N_E/../../src/client/webpack.ts", "webpack://_N_E/../../src/shared/lib/router/utils/is-dynamic.ts", "webpack://_N_E/../../src/shared/lib/utils.ts", "webpack://_N_E/../../src/shared/lib/router/utils/omit.ts", "webpack://_N_E/../../src/shared/lib/router/utils/sorted-routes.ts", "webpack://_N_E/../../src/shared/lib/router/adapters.tsx", "webpack://_N_E/../../src/client/has-base-path.ts", "webpack://_N_E/../../src/shared/lib/modern-browserslist-target.js", "webpack://_N_E/../../src/shared/lib/router/utils/get-next-pathname-info.ts", "webpack://_N_E/../../src/shared/lib/router/utils/compare-states.ts", "webpack://_N_E/./node_modules/next/dist/compiled/process/browser.js", "webpack://_N_E/../../src/client/index.tsx", "webpack://_N_E/../../src/shared/lib/router/utils/as-path-to-search-params.ts", "webpack://_N_E/../../src/shared/lib/router/utils/get-asset-path-from-route.ts", "webpack://_N_E/ignored|D:\\rki\\frontend\\node_modules\\next\\dist\\lib|private-next-instrumentation-client", "webpack://_N_E/../../src/client/react-client-callbacks/report-global-error.ts", "webpack://_N_E/./node_modules/next/dist/build/polyfills/polyfill-module.js", "webpack://_N_E/../../src/shared/lib/router/utils/is-bot.ts", "webpack://_N_E/../../src/shared/lib/image-config.ts", "webpack://_N_E/../../src/shared/lib/encode-uri-path.ts", "webpack://_N_E/../../src/client/add-base-path.ts", "webpack://_N_E/../../src/client/next.ts", "webpack://_N_E/../../src/client/remove-locale.ts", "webpack://_N_E/./node_modules/next/dist/lib/error-telemetry-utils.js", "webpack://_N_E/./node_modules/next/dist/lib/constants.js", "webpack://_N_E/../../src/shared/lib/router/utils/remove-trailing-slash.ts", "webpack://_N_E/../../src/shared/lib/page-path/denormalize-page-path.ts", "webpack://_N_E/../../src/client/detect-domain-locale.ts", "webpack://_N_E/ignored|D:\\rki\\frontend\\node_modules\\next\\dist\\shared\\lib\\router|./utils/resolve-rewrites", "webpack://_N_E/../../src/shared/lib/router/utils/index.ts", "webpack://_N_E/../../src/client/components/is-next-router-error.ts", "webpack://_N_E/../../src/shared/lib/segment.ts", "webpack://_N_E/../../src/shared/lib/router/utils/path-has-prefix.ts", "webpack://_N_E/../../src/shared/lib/router/utils/interpolate-as.ts", "webpack://_N_E/../../src/shared/lib/router/utils/html-bots.ts", "webpack://_N_E/../../src/shared/lib/i18n/normalize-locale-path.ts", "webpack://_N_E/../../src/client/resolve-href.ts", "webpack://_N_E/../../src/client/router.ts", "webpack://_N_E/../../src/client/components/redirect-status-code.ts", "webpack://_N_E/../../src/client/page-loader.ts", "webpack://_N_E/../../src/client/tracing/tracer.ts", "webpack://_N_E/../../src/shared/lib/lazy-dynamic/bailout-to-csr.ts", "webpack://_N_E/../../src/shared/lib/is-plain-object.ts", "webpack://_N_E/../../src/shared/lib/router/utils/add-locale.ts", "webpack://_N_E/../../src/client/with-router.tsx", "webpack://_N_E/../../src/client/head-manager.ts", "webpack://_N_E/../../src/client/route-loader.ts", "webpack://_N_E/../../src/shared/lib/constants.ts", "webpack://_N_E/../../src/shared/lib/page-path/normalize-path-sep.ts", "webpack://_N_E/../../src/shared/lib/router/utils/is-local-url.ts", "webpack://_N_E/./node_modules/@swc/helpers/esm/_interop_require_default.js", "webpack://_N_E/../../src/client/components/redirect-error.ts", "webpack://_N_E/./node_modules/next/dist/build/polyfills/process.js", "webpack://_N_E/./node_modules/next/dist/lib/is-error.js", "webpack://_N_E/../../src/shared/lib/router/utils/interception-routes.ts", "webpack://_N_E/../../src/client/set-attributes-from-props.ts", "webpack://_N_E/./node_modules/next/dist/lib/require-instrumentation-client.js", "webpack://_N_E/../../src/shared/lib/router/utils/route-regex.ts", "webpack://_N_E/../../src/shared/lib/router/utils/parse-relative-url.ts", "webpack://_N_E/../../src/shared/lib/router/utils/format-next-pathname-info.ts", "webpack://_N_E/../../src/client/components/errors/stitched-error.ts", "webpack://_N_E/../../src/shared/lib/escape-regexp.ts", "webpack://_N_E/../../src/client/request-idle-callback.ts", "webpack://_N_E/../../src/shared/lib/router/router.ts", "webpack://_N_E/../../src/shared/lib/bloom-filter.ts", "webpack://_N_E/../../src/client/portal/index.tsx", "webpack://_N_E/../../src/shared/lib/head-manager-context.shared-runtime.ts", "webpack://_N_E/../../src/shared/lib/router/utils/querystring.ts", "webpack://_N_E/../../src/shared/lib/router/utils/parse-path.ts", "webpack://_N_E/../../src/shared/lib/mitt.ts", "webpack://_N_E/./node_modules/next/dist/build/deployment-id.js", "webpack://_N_E/../../src/shared/lib/router/utils/add-path-suffix.ts", "webpack://_N_E/../../src/shared/lib/router/utils/remove-path-prefix.ts", "webpack://_N_E/../../src/client/remove-base-path.ts", "webpack://_N_E/../../src/client/script.tsx", "webpack://_N_E/../../src/shared/lib/page-path/ensure-leading-slash.ts", "webpack://_N_E/../../src/client/normalize-trailing-slash.ts", "webpack://_N_E/./node_modules/@swc/helpers/esm/_interop_require_wildcard.js", "webpack://_N_E/../../src/shared/lib/image-config-context.shared-runtime.ts", "webpack://_N_E/../../src/client/route-announcer.tsx", "webpack://_N_E/../../src/shared/lib/router/utils/add-path-prefix.ts", "webpack://_N_E/../../src/client/trusted-types.ts", "webpack://_N_E/../../src/shared/lib/router/utils/format-url.ts", "webpack://_N_E/../../src/shared/lib/router/utils/route-matcher.ts", "webpack://_N_E/../../src/shared/lib/router-context.shared-runtime.ts", "webpack://_N_E/../../src/shared/lib/i18n/detect-domain-locale.ts"], "sourcesContent": ["/**\n * Run function with `scroll-behavior: auto` applied to `<html/>`.\n * This css change will be reverted after the function finishes.\n */\nexport function handleSmoothScroll(\n  fn: () => void,\n  options: { dontForceLayout?: boolean; onlyHashChange?: boolean } = {}\n) {\n  // if only the hash is changed, we don't need to disable smooth scrolling\n  // we only care to prevent smooth scrolling when navigating to a new page to avoid jarring UX\n  if (options.onlyHashChange) {\n    fn()\n    return\n  }\n  const htmlElement = document.documentElement\n  const existing = htmlElement.style.scrollBehavior\n  htmlElement.style.scrollBehavior = 'auto'\n  if (!options.dontForceLayout) {\n    // In Chrome-based browsers we need to force reflow before calling `scrollTo`.\n    // Otherwise it will not pickup the change in scrollBehavior\n    // More info here: https://github.com/vercel/next.js/issues/40719#issuecomment-1336248042\n    htmlElement.getClientRects()\n  }\n  fn()\n  htmlElement.style.scrollBehavior = existing\n}\n", "\"use strict\";\nObject.defineProperty(exports, \"__esModule\", {\n    value: true\n});\nObject.defineProperty(exports, \"isAPIRoute\", {\n    enumerable: true,\n    get: function() {\n        return isAPIRoute;\n    }\n});\nfunction isAPIRoute(value) {\n    return value === '/api' || Boolean(value == null ? void 0 : value.startsWith('/api/'));\n}\n\n//# sourceMappingURL=is-api-route.js.map", "import type { addLocale as Fn } from '../shared/lib/router/utils/add-locale'\nimport { normalizePathTrailingSlash } from './normalize-trailing-slash'\n\nexport const addLocale: typeof Fn = (path, ...args) => {\n  if (process.env.__NEXT_I18N_SUPPORT) {\n    return normalizePathTrailingSlash(\n      require('../shared/lib/router/utils/add-locale').addLocale(path, ...args)\n    )\n  }\n  return path\n}\n", "'use client'\n\nimport type { FetchServerResponseResult } from '../../client/components/router-reducer/fetch-server-response'\nimport type {\n  FocusAndScrollRef,\n  PrefetchKind,\n} from '../../client/components/router-reducer/router-reducer-types'\nimport type {\n  FlightRouterState,\n  FlightSegmentPath,\n} from '../../server/app-render/types'\nimport React from 'react'\n\nexport type ChildSegmentMap = Map<string, CacheNode>\n\n/**\n * Cache node used in app-router / layout-router.\n */\nexport type CacheNode = ReadyCacheNode | LazyCacheNode\n\nexport type LoadingModuleData =\n  | [React.JSX.Element, React.ReactNode, React.ReactNode]\n  | null\n\n/** viewport metadata node */\nexport type HeadData = React.ReactNode\n\nexport type LazyCacheNode = {\n  /**\n   * When rsc is null, this is a lazily-initialized cache node.\n   *\n   * If the app attempts to render it, it triggers a lazy data fetch,\n   * postpones the render, and schedules an update to a new tree.\n   *\n   * TODO: This mechanism should not be used when PPR is enabled, though it\n   * currently is in some cases until we've implemented partial\n   * segment fetching.\n   */\n  rsc: null\n\n  /**\n   * A prefetched version of the segment data. See explanation in corresponding\n   * field of ReadyCacheNode (below).\n   *\n   * Since LazyCacheNode mostly only exists in the non-PPR implementation, this\n   * will usually be null, but it could have been cloned from a previous\n   * CacheNode that was created by the PPR implementation. Eventually we want\n   * to migrate everything away from LazyCacheNode entirely.\n   */\n  prefetchRsc: React.ReactNode\n\n  /**\n   * A pending response for the lazy data fetch. If this is not present\n   * during render, it is lazily created.\n   */\n  lazyData: Promise<FetchServerResponseResult> | null\n\n  prefetchHead: HeadData | null\n\n  head: HeadData\n\n  loading: LoadingModuleData | Promise<LoadingModuleData>\n\n  /**\n   * Child parallel routes.\n   */\n  parallelRoutes: Map<string, ChildSegmentMap>\n\n  /**\n   * The timestamp of the navigation that last updated the CacheNode's data. If\n   * a CacheNode is reused from a previous navigation, this value is not\n   * updated. Used to track the staleness of the data.\n   */\n  navigatedAt: number\n}\n\nexport type ReadyCacheNode = {\n  /**\n   * When rsc is not null, it represents the RSC data for the\n   * corresponding segment.\n   *\n   * `null` is a valid React Node but because segment data is always a\n   * <LayoutRouter> component, we can use `null` to represent empty.\n   *\n   * TODO: For additional type safety, update this type to\n   * Exclude<React.ReactNode, null>. Need to update createEmptyCacheNode to\n   * accept rsc as an argument, or just inline the callers.\n   */\n  rsc: React.ReactNode\n\n  /**\n   * Represents a static version of the segment that can be shown immediately,\n   * and may or may not contain dynamic holes. It's prefetched before a\n   * navigation occurs.\n   *\n   * During rendering, we will choose whether to render `rsc` or `prefetchRsc`\n   * with `useDeferredValue`. As with the `rsc` field, a value of `null` means\n   * no value was provided. In this case, the LayoutRouter will go straight to\n   * rendering the `rsc` value; if that one is also missing, it will suspend and\n   * trigger a lazy fetch.\n   */\n  prefetchRsc: React.ReactNode\n\n  /**\n   * There should never be a lazy data request in this case.\n   */\n  lazyData: null\n  prefetchHead: HeadData | null\n\n  head: HeadData\n\n  loading: LoadingModuleData | Promise<LoadingModuleData>\n\n  parallelRoutes: Map<string, ChildSegmentMap>\n\n  navigatedAt: number\n}\n\nexport interface NavigateOptions {\n  scroll?: boolean\n}\n\nexport interface PrefetchOptions {\n  kind: PrefetchKind\n}\n\nexport interface AppRouterInstance {\n  /**\n   * Navigate to the previous history entry.\n   */\n  back(): void\n  /**\n   * Navigate to the next history entry.\n   */\n  forward(): void\n  /**\n   * Refresh the current page.\n   */\n  refresh(): void\n  /**\n   * Refresh the current page. Use in development only.\n   * @internal\n   */\n  hmrRefresh(): void\n  /**\n   * Navigate to the provided href.\n   * Pushes a new history entry.\n   */\n  push(href: string, options?: NavigateOptions): void\n  /**\n   * Navigate to the provided href.\n   * Replaces the current history entry.\n   */\n  replace(href: string, options?: NavigateOptions): void\n  /**\n   * Prefetch the provided href.\n   */\n  prefetch(href: string, options?: PrefetchOptions): void\n}\n\nexport const AppRouterContext = React.createContext<AppRouterInstance | null>(\n  null\n)\nexport const LayoutRouterContext = React.createContext<{\n  parentTree: FlightRouterState\n  parentCacheNode: CacheNode\n  parentSegmentPath: FlightSegmentPath | null\n  url: string\n} | null>(null)\n\nexport const GlobalLayoutRouterContext = React.createContext<{\n  tree: FlightRouterState\n  focusAndScrollRef: FocusAndScrollRef\n  nextUrl: string | null\n}>(null as any)\n\nexport const TemplateContext = React.createContext<React.ReactNode>(null as any)\n\nif (process.env.NODE_ENV !== 'production') {\n  AppRouterContext.displayName = 'AppRouterContext'\n  LayoutRouterContext.displayName = 'LayoutRouterContext'\n  GlobalLayoutRouterContext.displayName = 'GlobalLayoutRouterContext'\n  TemplateContext.displayName = 'TemplateContext'\n}\n\nexport const MissingSlotContext = React.createContext<Set<string>>(new Set())\n", "let runtimeConfig: any\n\nexport default () => {\n  return runtimeConfig\n}\n\nexport function setConfig(configValue: any): void {\n  runtimeConfig = configValue\n}\n", "export const HTTPAccessErrorStatus = {\n  NOT_FOUND: 404,\n  FORBIDDEN: 403,\n  UNAUTHORIZED: 401,\n}\n\nconst ALLOWED_CODES = new Set(Object.values(HTTPAccessErrorStatus))\n\nexport const HTTP_ERROR_FALLBACK_ERROR_CODE = 'NEXT_HTTP_ERROR_FALLBACK'\n\nexport type HTTPAccessFallbackError = Error & {\n  digest: `${typeof HTTP_ERROR_FALLBACK_ERROR_CODE};${string}`\n}\n\n/**\n * Checks an error to determine if it's an error generated by\n * the HTTP navigation APIs `notFound()`, `forbidden()` or `unauthorized()`.\n *\n * @param error the error that may reference a HTTP access error\n * @returns true if the error is a HTTP access error\n */\nexport function isHTTPAccessFallbackError(\n  error: unknown\n): error is HTTPAccessFallbackError {\n  if (\n    typeof error !== 'object' ||\n    error === null ||\n    !('digest' in error) ||\n    typeof error.digest !== 'string'\n  ) {\n    return false\n  }\n  const [prefix, httpStatus] = error.digest.split(';')\n\n  return (\n    prefix === HTTP_ERROR_FALLBACK_ERROR_CODE &&\n    ALLOWED_CODES.has(Number(httpStatus))\n  )\n}\n\nexport function getAccessFallbackHTTPStatus(\n  error: HTTPAccessFallbackError\n): number {\n  const httpStatus = error.digest.split(';')[1]\n  return Number(httpStatus)\n}\n\nexport function getAccessFallbackErrorTypeByStatus(\n  status: number\n): 'not-found' | 'forbidden' | 'unauthorized' | undefined {\n  switch (status) {\n    case 401:\n      return 'unauthorized'\n    case 403:\n      return 'forbidden'\n    case 404:\n      return 'not-found'\n    default:\n      return\n  }\n}\n", "import { ensureLeadingSlash } from '../../page-path/ensure-leading-slash'\nimport { isGroupSegment } from '../../segment'\n\n/**\n * Normalizes an app route so it represents the actual request path. Essentially\n * performing the following transformations:\n *\n * - `/(dashboard)/user/[id]/page` to `/user/[id]`\n * - `/(dashboard)/account/page` to `/account`\n * - `/user/[id]/page` to `/user/[id]`\n * - `/account/page` to `/account`\n * - `/page` to `/`\n * - `/(dashboard)/user/[id]/route` to `/user/[id]`\n * - `/(dashboard)/account/route` to `/account`\n * - `/user/[id]/route` to `/user/[id]`\n * - `/account/route` to `/account`\n * - `/route` to `/`\n * - `/` to `/`\n *\n * @param route the app route to normalize\n * @returns the normalized pathname\n */\nexport function normalizeAppPath(route: string) {\n  return ensureLeadingSlash(\n    route.split('/').reduce((pathname, segment, index, segments) => {\n      // Empty segments are ignored.\n      if (!segment) {\n        return pathname\n      }\n\n      // Groups are ignored.\n      if (isGroupSegment(segment)) {\n        return pathname\n      }\n\n      // Parallel segments are ignored.\n      if (segment[0] === '@') {\n        return pathname\n      }\n\n      // The last segment (if it's a leaf) should be ignored.\n      if (\n        (segment === 'page' || segment === 'route') &&\n        index === segments.length - 1\n      ) {\n        return pathname\n      }\n\n      return `${pathname}/${segment}`\n    }, '')\n  )\n}\n\n/**\n * Strips the `.rsc` extension if it's in the pathname.\n * Since this function is used on full urls it checks `?` for searchParams handling.\n */\nexport function normalizeRscURL(url: string) {\n  return url.replace(\n    /\\.rsc($|\\?)/,\n    // $1 ensures `?` is preserved\n    '$1'\n  )\n}\n", "// This module can be shared between both pages router and app router\n\nimport type { HydrationOptions } from 'react-dom/client'\nimport { isBailoutToCSRError } from '../../shared/lib/lazy-dynamic/bailout-to-csr'\nimport { reportGlobalError } from './report-global-error'\nimport { getReactStitchedError } from '../components/errors/stitched-error'\nimport isError from '../../lib/is-error'\n\nexport const onRecoverableError: HydrationOptions['onRecoverableError'] = (\n  error,\n  errorInfo\n) => {\n  // x-ref: https://github.com/facebook/react/pull/28736\n  const cause = isError(error) && 'cause' in error ? error.cause : error\n  const stitchedError = getReactStitchedError(cause)\n  // In development mode, pass along the component stack to the error\n  if (process.env.NODE_ENV === 'development' && errorInfo.componentStack) {\n    ;(stitchedError as any)._componentStack = errorInfo.componentStack\n  }\n  // Skip certain custom errors which are not expected to be reported on client\n  if (isBailoutToCSRError(cause)) return\n\n  reportGlobalError(stitchedError)\n}\n", "'use client'\n\nimport { createContext } from 'react'\nimport type { Params } from '../../server/request/params'\n\nexport const SearchParamsContext = createContext<URLSearchParams | null>(null)\nexport const PathnameContext = createContext<string | null>(null)\nexport const PathParamsContext = createContext<Params | null>(null)\n\nif (process.env.NODE_ENV !== 'production') {\n  SearchParamsContext.displayName = 'SearchParamsContext'\n  PathnameContext.displayName = 'PathnameContext'\n  PathParamsContext.displayName = 'PathParamsContext'\n}\n", "// eslint-disable-next-line @typescript-eslint/no-unused-vars\ndeclare const __webpack_require__: any\n// eslint-disable-next-line @typescript-eslint/no-unused-vars\ndeclare let __webpack_public_path__: string\n\nimport { getDeploymentIdQueryOrEmptyString } from '../build/deployment-id'\n\n// If we have a deployment ID, we need to append it to the webpack chunk names\n// I am keeping the process check explicit so this can be statically optimized\nif (process.env.NEXT_DEPLOYMENT_ID) {\n  const suffix = getDeploymentIdQueryOrEmptyString()\n  // eslint-disable-next-line no-undef\n  const getChunkScriptFilename = __webpack_require__.u\n  // eslint-disable-next-line no-undef\n  __webpack_require__.u = (...args: any[]) =>\n    // We enode the chunk filename because our static server matches against and encoded\n    // filename path.\n    getChunkScriptFilename(...args) + suffix\n\n  // eslint-disable-next-line no-undef\n  const getChunkCssFilename = __webpack_require__.k\n  // eslint-disable-next-line no-undef\n  __webpack_require__.k = (...args: any[]) =>\n    getChunkCssFilename(...args) + suffix\n\n  // eslint-disable-next-line no-undef\n  const getMiniCssFilename = __webpack_require__.miniCssF\n  // eslint-disable-next-line no-undef\n  __webpack_require__.miniCssF = (...args: any[]) =>\n    getMiniCssFilename(...args) + suffix\n}\n\n// Ignore the module ID transform in client.\n;(self as any).__next_set_public_path__ = (path: string) => {\n  // eslint-disable-next-line @typescript-eslint/no-unused-vars\n  __webpack_public_path__ = path\n}\n\nexport {}\n", "import {\n  extractInterceptionRouteInformation,\n  isInterceptionRouteAppPath,\n} from './interception-routes'\n\n// Identify /.*[param].*/ in route string\nconst TEST_ROUTE = /\\/[^/]*\\[[^/]+\\][^/]*(?=\\/|$)/\n\n// Identify /[param]/ in route string\nconst TEST_STRICT_ROUTE = /\\/\\[[^/]+\\](?=\\/|$)/\n\n/**\n * Check if a route is dynamic.\n *\n * @param route - The route to check.\n * @param strict - Whether to use strict mode which prohibits segments with prefixes/suffixes (default: true).\n * @returns Whether the route is dynamic.\n */\nexport function isDynamicRoute(route: string, strict: boolean = true): boolean {\n  if (isInterceptionRouteAppPath(route)) {\n    route = extractInterceptionRouteInformation(route).interceptedRoute\n  }\n\n  if (strict) {\n    return TEST_STRICT_ROUTE.test(route)\n  }\n\n  return TEST_ROUTE.test(route)\n}\n", "import type { HtmlProps } from './html-context.shared-runtime'\nimport type { ComponentType, JSX } from 'react'\nimport type { DomainLocale } from '../../server/config'\nimport type { Env } from '@next/env'\nimport type { IncomingMessage, ServerResponse } from 'http'\nimport type { NextRouter } from './router/router'\nimport type { ParsedUrlQuery } from 'querystring'\nimport type { PreviewData } from '../../types'\nimport type { COMPILER_NAMES } from './constants'\nimport type fs from 'fs'\n\nexport type NextComponentType<\n  Context extends BaseContext = NextPageContext,\n  InitialProps = {},\n  Props = {},\n> = ComponentType<Props> & {\n  /**\n   * Used for initial page load data population. Data returned from `getInitialProps` is serialized when server rendered.\n   * Make sure to return plain `Object` without using `Date`, `Map`, `Set`.\n   * @param context Context of `page`\n   */\n  getInitialProps?(context: Context): InitialProps | Promise<InitialProps>\n}\n\nexport type DocumentType = NextComponentType<\n  DocumentContext,\n  DocumentInitialProps,\n  DocumentProps\n>\n\nexport type AppType<P = {}> = NextComponentType<\n  AppContextType,\n  P,\n  AppPropsType<any, P>\n>\n\nexport type AppTreeType = ComponentType<\n  AppInitialProps & { [name: string]: any }\n>\n\n/**\n * Web vitals provided to _app.reportWebVitals by Core Web Vitals plugin developed by Google Chrome team.\n * https://nextjs.org/blog/next-9-4#integrated-web-vitals-reporting\n */\nexport const WEB_VITALS = ['CLS', 'FCP', 'FID', 'INP', 'LCP', 'TTFB'] as const\nexport type NextWebVitalsMetric = {\n  id: string\n  startTime: number\n  value: number\n  attribution?: { [key: string]: unknown }\n} & (\n  | {\n      label: 'web-vital'\n      name: (typeof WEB_VITALS)[number]\n    }\n  | {\n      label: 'custom'\n      name:\n        | 'Next.js-hydration'\n        | 'Next.js-route-change-to-render'\n        | 'Next.js-render'\n    }\n)\n\nexport type Enhancer<C> = (Component: C) => C\n\nexport type ComponentsEnhancer =\n  | {\n      enhanceApp?: Enhancer<AppType>\n      enhanceComponent?: Enhancer<NextComponentType>\n    }\n  | Enhancer<NextComponentType>\n\nexport type RenderPageResult = {\n  html: string\n  head?: Array<JSX.Element | null>\n}\n\nexport type RenderPage = (\n  options?: ComponentsEnhancer\n) => DocumentInitialProps | Promise<DocumentInitialProps>\n\nexport type BaseContext = {\n  res?: ServerResponse\n  [k: string]: any\n}\n\nexport type NEXT_DATA = {\n  props: Record<string, any>\n  page: string\n  query: ParsedUrlQuery\n  buildId: string\n  assetPrefix?: string\n  runtimeConfig?: { [key: string]: any }\n  nextExport?: boolean\n  autoExport?: boolean\n  isFallback?: boolean\n  isExperimentalCompile?: boolean\n  dynamicIds?: (string | number)[]\n  err?: Error & {\n    statusCode?: number\n    source?: typeof COMPILER_NAMES.server | typeof COMPILER_NAMES.edgeServer\n  }\n  gsp?: boolean\n  gssp?: boolean\n  customServer?: boolean\n  gip?: boolean\n  appGip?: boolean\n  locale?: string\n  locales?: readonly string[]\n  defaultLocale?: string\n  domainLocales?: readonly DomainLocale[]\n  scriptLoader?: any[]\n  isPreview?: boolean\n  notFoundSrcPage?: string\n}\n\n/**\n * `Next` context\n */\nexport interface NextPageContext {\n  /**\n   * Error object if encountered during rendering\n   */\n  err?: (Error & { statusCode?: number }) | null\n  /**\n   * `HTTP` request object.\n   */\n  req?: IncomingMessage\n  /**\n   * `HTTP` response object.\n   */\n  res?: ServerResponse\n  /**\n   * Path section of `URL`.\n   */\n  pathname: string\n  /**\n   * Query string section of `URL` parsed as an object.\n   */\n  query: ParsedUrlQuery\n  /**\n   * `String` of the actual path including query.\n   */\n  asPath?: string\n  /**\n   * The currently active locale\n   */\n  locale?: string\n  /**\n   * All configured locales\n   */\n  locales?: readonly string[]\n  /**\n   * The configured default locale\n   */\n  defaultLocale?: string\n  /**\n   * `Component` the tree of the App to use if needing to render separately\n   */\n  AppTree: AppTreeType\n}\n\nexport type AppContextType<Router extends NextRouter = NextRouter> = {\n  Component: NextComponentType<NextPageContext>\n  AppTree: AppTreeType\n  ctx: NextPageContext\n  router: Router\n}\n\nexport type AppInitialProps<PageProps = any> = {\n  pageProps: PageProps\n}\n\nexport type AppPropsType<\n  Router extends NextRouter = NextRouter,\n  PageProps = {},\n> = AppInitialProps<PageProps> & {\n  Component: NextComponentType<NextPageContext, any, any>\n  router: Router\n  __N_SSG?: boolean\n  __N_SSP?: boolean\n}\n\nexport type DocumentContext = NextPageContext & {\n  renderPage: RenderPage\n  defaultGetInitialProps(\n    ctx: DocumentContext,\n    options?: { nonce?: string }\n  ): Promise<DocumentInitialProps>\n}\n\nexport type DocumentInitialProps = RenderPageResult & {\n  styles?: React.ReactElement[] | Iterable<React.ReactNode> | JSX.Element\n}\n\nexport type DocumentProps = DocumentInitialProps & HtmlProps\n\n/**\n * Next `API` route request\n */\nexport interface NextApiRequest extends IncomingMessage {\n  /**\n   * Object of `query` values from url\n   */\n  query: Partial<{\n    [key: string]: string | string[]\n  }>\n  /**\n   * Object of `cookies` from header\n   */\n  cookies: Partial<{\n    [key: string]: string\n  }>\n\n  body: any\n\n  env: Env\n\n  draftMode?: boolean\n\n  preview?: boolean\n  /**\n   * Preview data set on the request, if any\n   * */\n  previewData?: PreviewData\n}\n\n/**\n * Send body of response\n */\ntype Send<T> = (body: T) => void\n\n/**\n * Next `API` route response\n */\nexport type NextApiResponse<Data = any> = ServerResponse & {\n  /**\n   * Send data `any` data in response\n   */\n  send: Send<Data>\n  /**\n   * Send data `json` data in response\n   */\n  json: Send<Data>\n  status: (statusCode: number) => NextApiResponse<Data>\n  redirect(url: string): NextApiResponse<Data>\n  redirect(status: number, url: string): NextApiResponse<Data>\n\n  /**\n   * Set draft mode\n   */\n  setDraftMode: (options: { enable: boolean }) => NextApiResponse<Data>\n\n  /**\n   * Set preview data for Next.js' prerender mode\n   */\n  setPreviewData: (\n    data: object | string,\n    options?: {\n      /**\n       * Specifies the number (in seconds) for the preview session to last for.\n       * The given number will be converted to an integer by rounding down.\n       * By default, no maximum age is set and the preview session finishes\n       * when the client shuts down (browser is closed).\n       */\n      maxAge?: number\n      /**\n       * Specifies the path for the preview session to work under. By default,\n       * the path is considered the \"default path\", i.e., any pages under \"/\".\n       */\n      path?: string\n    }\n  ) => NextApiResponse<Data>\n\n  /**\n   * Clear preview data for Next.js' prerender mode\n   */\n  clearPreviewData: (options?: { path?: string }) => NextApiResponse<Data>\n\n  /**\n   * Revalidate a specific page and regenerate it using On-Demand Incremental\n   * Static Regeneration.\n   * The path should be an actual path, not a rewritten path. E.g. for\n   * \"/blog/[slug]\" this should be \"/blog/post-1\".\n   * @link https://nextjs.org/docs/app/building-your-application/data-fetching/incremental-static-regeneration#on-demand-revalidation-with-revalidatepath\n   */\n  revalidate: (\n    urlPath: string,\n    opts?: {\n      unstable_onlyGenerated?: boolean\n    }\n  ) => Promise<void>\n}\n\n/**\n * Next `API` route handler\n */\nexport type NextApiHandler<T = any> = (\n  req: NextApiRequest,\n  res: NextApiResponse<T>\n) => unknown | Promise<unknown>\n\n/**\n * Utils\n */\nexport function execOnce<T extends (...args: any[]) => ReturnType<T>>(\n  fn: T\n): T {\n  let used = false\n  let result: ReturnType<T>\n\n  return ((...args: any[]) => {\n    if (!used) {\n      used = true\n      result = fn(...args)\n    }\n    return result\n  }) as T\n}\n\n// Scheme: https://tools.ietf.org/html/rfc3986#section-3.1\n// Absolute URL: https://tools.ietf.org/html/rfc3986#section-4.3\nconst ABSOLUTE_URL_REGEX = /^[a-zA-Z][a-zA-Z\\d+\\-.]*?:/\nexport const isAbsoluteUrl = (url: string) => ABSOLUTE_URL_REGEX.test(url)\n\nexport function getLocationOrigin() {\n  const { protocol, hostname, port } = window.location\n  return `${protocol}//${hostname}${port ? ':' + port : ''}`\n}\n\nexport function getURL() {\n  const { href } = window.location\n  const origin = getLocationOrigin()\n  return href.substring(origin.length)\n}\n\nexport function getDisplayName<P>(Component: ComponentType<P>) {\n  return typeof Component === 'string'\n    ? Component\n    : Component.displayName || Component.name || 'Unknown'\n}\n\nexport function isResSent(res: ServerResponse) {\n  return res.finished || res.headersSent\n}\n\nexport function normalizeRepeatedSlashes(url: string) {\n  const urlParts = url.split('?')\n  const urlNoQuery = urlParts[0]\n\n  return (\n    urlNoQuery\n      // first we replace any non-encoded backslashes with forward\n      // then normalize repeated forward slashes\n      .replace(/\\\\/g, '/')\n      .replace(/\\/\\/+/g, '/') +\n    (urlParts[1] ? `?${urlParts.slice(1).join('?')}` : '')\n  )\n}\n\nexport async function loadGetInitialProps<\n  C extends BaseContext,\n  IP = {},\n  P = {},\n>(App: NextComponentType<C, IP, P>, ctx: C): Promise<IP> {\n  if (process.env.NODE_ENV !== 'production') {\n    if (App.prototype?.getInitialProps) {\n      const message = `\"${getDisplayName(\n        App\n      )}.getInitialProps()\" is defined as an instance method - visit https://nextjs.org/docs/messages/get-initial-props-as-an-instance-method for more information.`\n      throw new Error(message)\n    }\n  }\n  // when called from _app `ctx` is nested in `ctx`\n  const res = ctx.res || (ctx.ctx && ctx.ctx.res)\n\n  if (!App.getInitialProps) {\n    if (ctx.ctx && ctx.Component) {\n      // @ts-ignore pageProps default\n      return {\n        pageProps: await loadGetInitialProps(ctx.Component, ctx.ctx),\n      }\n    }\n    return {} as IP\n  }\n\n  const props = await App.getInitialProps(ctx)\n\n  if (res && isResSent(res)) {\n    return props\n  }\n\n  if (!props) {\n    const message = `\"${getDisplayName(\n      App\n    )}.getInitialProps()\" should resolve to an object. But found \"${props}\" instead.`\n    throw new Error(message)\n  }\n\n  if (process.env.NODE_ENV !== 'production') {\n    if (Object.keys(props).length === 0 && !ctx.ctx) {\n      console.warn(\n        `${getDisplayName(\n          App\n        )} returned an empty object from \\`getInitialProps\\`. This de-optimizes and prevents automatic static optimization. https://nextjs.org/docs/messages/empty-object-getInitialProps`\n      )\n    }\n  }\n\n  return props\n}\n\nexport const SP = typeof performance !== 'undefined'\nexport const ST =\n  SP &&\n  (['mark', 'measure', 'getEntriesByName'] as const).every(\n    (method) => typeof performance[method] === 'function'\n  )\n\nexport class DecodeError extends Error {}\nexport class NormalizeError extends Error {}\nexport class PageNotFoundError extends Error {\n  code: string\n\n  constructor(page: string) {\n    super()\n    this.code = 'ENOENT'\n    this.name = 'PageNotFoundError'\n    this.message = `Cannot find module for page: ${page}`\n  }\n}\n\nexport class MissingStaticPage extends Error {\n  constructor(page: string, message: string) {\n    super()\n    this.message = `Failed to load static file for page: ${page} ${message}`\n  }\n}\n\nexport class MiddlewareNotFoundError extends Error {\n  code: string\n  constructor() {\n    super()\n    this.code = 'ENOENT'\n    this.message = `Cannot find the middleware module`\n  }\n}\n\nexport interface CacheFs {\n  existsSync: typeof fs.existsSync\n  readFile: typeof fs.promises.readFile\n  readFileSync: typeof fs.readFileSync\n  writeFile(f: string, d: any): Promise<void>\n  mkdir(dir: string): Promise<void | string>\n  stat(f: string): Promise<{ mtime: Date }>\n}\n\nexport function stringifyError(error: Error) {\n  return JSON.stringify({ message: error.message, stack: error.stack })\n}\n", "export function omit<T extends { [key: string]: unknown }, K extends keyof T>(\n  object: T,\n  keys: K[]\n): Omit<T, K> {\n  const omitted: { [key: string]: unknown } = {}\n  Object.keys(object).forEach((key) => {\n    if (!keys.includes(key as K)) {\n      omitted[key] = object[key]\n    }\n  })\n  return omitted as Omit<T, K>\n}\n", "class UrlNode {\n  placeholder: boolean = true\n  children: Map<string, UrlNode> = new Map()\n  slugName: string | null = null\n  restSlugName: string | null = null\n  optionalRestSlugName: string | null = null\n\n  insert(urlPath: string): void {\n    this._insert(urlPath.split('/').filter(Boolean), [], false)\n  }\n\n  smoosh(): string[] {\n    return this._smoosh()\n  }\n\n  private _smoosh(prefix: string = '/'): string[] {\n    const childrenPaths = [...this.children.keys()].sort()\n    if (this.slugName !== null) {\n      childrenPaths.splice(childrenPaths.indexOf('[]'), 1)\n    }\n    if (this.restSlugName !== null) {\n      childrenPaths.splice(childrenPaths.indexOf('[...]'), 1)\n    }\n    if (this.optionalRestSlugName !== null) {\n      childrenPaths.splice(childrenPaths.indexOf('[[...]]'), 1)\n    }\n\n    const routes = childrenPaths\n      .map((c) => this.children.get(c)!._smoosh(`${prefix}${c}/`))\n      .reduce((prev, curr) => [...prev, ...curr], [])\n\n    if (this.slugName !== null) {\n      routes.push(\n        ...this.children.get('[]')!._smoosh(`${prefix}[${this.slugName}]/`)\n      )\n    }\n\n    if (!this.placeholder) {\n      const r = prefix === '/' ? '/' : prefix.slice(0, -1)\n      if (this.optionalRestSlugName != null) {\n        throw new Error(\n          `You cannot define a route with the same specificity as a optional catch-all route (\"${r}\" and \"${r}[[...${this.optionalRestSlugName}]]\").`\n        )\n      }\n\n      routes.unshift(r)\n    }\n\n    if (this.restSlugName !== null) {\n      routes.push(\n        ...this.children\n          .get('[...]')!\n          ._smoosh(`${prefix}[...${this.restSlugName}]/`)\n      )\n    }\n\n    if (this.optionalRestSlugName !== null) {\n      routes.push(\n        ...this.children\n          .get('[[...]]')!\n          ._smoosh(`${prefix}[[...${this.optionalRestSlugName}]]/`)\n      )\n    }\n\n    return routes\n  }\n\n  private _insert(\n    urlPaths: string[],\n    slugNames: string[],\n    isCatchAll: boolean\n  ): void {\n    if (urlPaths.length === 0) {\n      this.placeholder = false\n      return\n    }\n\n    if (isCatchAll) {\n      throw new Error(`Catch-all must be the last part of the URL.`)\n    }\n\n    // The next segment in the urlPaths list\n    let nextSegment = urlPaths[0]\n\n    // Check if the segment matches `[something]`\n    if (nextSegment.startsWith('[') && nextSegment.endsWith(']')) {\n      // Strip `[` and `]`, leaving only `something`\n      let segmentName = nextSegment.slice(1, -1)\n\n      let isOptional = false\n      if (segmentName.startsWith('[') && segmentName.endsWith(']')) {\n        // Strip optional `[` and `]`, leaving only `something`\n        segmentName = segmentName.slice(1, -1)\n        isOptional = true\n      }\n\n      if (segmentName.startsWith('…')) {\n        throw new Error(\n          `Detected a three-dot character ('…') at ('${segmentName}'). Did you mean ('...')?`\n        )\n      }\n\n      if (segmentName.startsWith('...')) {\n        // Strip `...`, leaving only `something`\n        segmentName = segmentName.substring(3)\n        isCatchAll = true\n      }\n\n      if (segmentName.startsWith('[') || segmentName.endsWith(']')) {\n        throw new Error(\n          `Segment names may not start or end with extra brackets ('${segmentName}').`\n        )\n      }\n\n      if (segmentName.startsWith('.')) {\n        throw new Error(\n          `Segment names may not start with erroneous periods ('${segmentName}').`\n        )\n      }\n\n      function handleSlug(previousSlug: string | null, nextSlug: string) {\n        if (previousSlug !== null) {\n          // If the specific segment already has a slug but the slug is not `something`\n          // This prevents collisions like:\n          // pages/[post]/index.js\n          // pages/[id]/index.js\n          // Because currently multiple dynamic params on the same segment level are not supported\n          if (previousSlug !== nextSlug) {\n            // TODO: This error seems to be confusing for users, needs an error link, the description can be based on above comment.\n            throw new Error(\n              `You cannot use different slug names for the same dynamic path ('${previousSlug}' !== '${nextSlug}').`\n            )\n          }\n        }\n\n        slugNames.forEach((slug) => {\n          if (slug === nextSlug) {\n            throw new Error(\n              `You cannot have the same slug name \"${nextSlug}\" repeat within a single dynamic path`\n            )\n          }\n\n          if (slug.replace(/\\W/g, '') === nextSegment.replace(/\\W/g, '')) {\n            throw new Error(\n              `You cannot have the slug names \"${slug}\" and \"${nextSlug}\" differ only by non-word symbols within a single dynamic path`\n            )\n          }\n        })\n\n        slugNames.push(nextSlug)\n      }\n\n      if (isCatchAll) {\n        if (isOptional) {\n          if (this.restSlugName != null) {\n            throw new Error(\n              `You cannot use both an required and optional catch-all route at the same level (\"[...${this.restSlugName}]\" and \"${urlPaths[0]}\" ).`\n            )\n          }\n\n          handleSlug(this.optionalRestSlugName, segmentName)\n          // slugName is kept as it can only be one particular slugName\n          this.optionalRestSlugName = segmentName\n          // nextSegment is overwritten to [[...]] so that it can later be sorted specifically\n          nextSegment = '[[...]]'\n        } else {\n          if (this.optionalRestSlugName != null) {\n            throw new Error(\n              `You cannot use both an optional and required catch-all route at the same level (\"[[...${this.optionalRestSlugName}]]\" and \"${urlPaths[0]}\").`\n            )\n          }\n\n          handleSlug(this.restSlugName, segmentName)\n          // slugName is kept as it can only be one particular slugName\n          this.restSlugName = segmentName\n          // nextSegment is overwritten to [...] so that it can later be sorted specifically\n          nextSegment = '[...]'\n        }\n      } else {\n        if (isOptional) {\n          throw new Error(\n            `Optional route parameters are not yet supported (\"${urlPaths[0]}\").`\n          )\n        }\n        handleSlug(this.slugName, segmentName)\n        // slugName is kept as it can only be one particular slugName\n        this.slugName = segmentName\n        // nextSegment is overwritten to [] so that it can later be sorted specifically\n        nextSegment = '[]'\n      }\n    }\n\n    // If this UrlNode doesn't have the nextSegment yet we create a new child UrlNode\n    if (!this.children.has(nextSegment)) {\n      this.children.set(nextSegment, new UrlNode())\n    }\n\n    this.children\n      .get(nextSegment)!\n      ._insert(urlPaths.slice(1), slugNames, isCatchAll)\n  }\n}\n\nexport function getSortedRoutes(\n  normalizedPages: ReadonlyArray<string>\n): string[] {\n  // First the UrlNode is created, and every UrlNode can have only 1 dynamic segment\n  // Eg you can't have pages/[post]/abc.js and pages/[hello]/something-else.js\n  // Only 1 dynamic segment per nesting level\n\n  // So in the case that is test/integration/dynamic-routing it'll be this:\n  // pages/[post]/comments.js\n  // pages/blog/[post]/comment/[id].js\n  // Both are fine because `pages/[post]` and `pages/blog` are on the same level\n  // So in this case `UrlNode` created here has `this.slugName === 'post'`\n  // And since your PR passed through `slugName` as an array basically it'd including it in too many possibilities\n  // Instead what has to be passed through is the upwards path's dynamic names\n  const root = new UrlNode()\n\n  // Here the `root` gets injected multiple paths, and insert will break them up into sublevels\n  normalizedPages.forEach((pagePath) => root.insert(pagePath))\n  // Smoosh will then sort those sublevels up to the point where you get the correct route definition priority\n  return root.smoosh()\n}\n\nexport function getSortedRouteObjects<T>(\n  objects: T[],\n  getter: (obj: T) => string\n): T[] {\n  // We're assuming here that all the pathnames are unique, that way we can\n  // sort the list and use the index as the key.\n  const indexes: Record<string, number> = {}\n  const pathnames: string[] = []\n  for (let i = 0; i < objects.length; i++) {\n    const pathname = getter(objects[i])\n    indexes[pathname] = i\n    pathnames[i] = pathname\n  }\n\n  // Sort the pathnames.\n  const sorted = getSortedRoutes(pathnames)\n\n  // Map the sorted pathnames back to the original objects using the new sorted\n  // index.\n  return sorted.map((pathname) => objects[indexes[pathname]])\n}\n", "import type { AppRouterInstance } from '../app-router-context.shared-runtime'\nimport type { Params } from '../../../server/request/params'\nimport type { NextRouter } from './router'\n\nimport React, { useMemo, useRef } from 'react'\nimport { PathnameContext } from '../hooks-client-context.shared-runtime'\nimport { isDynamicRoute } from './utils'\nimport { asPathToSearchParams } from './utils/as-path-to-search-params'\nimport { getRouteRegex } from './utils/route-regex'\n\n/** It adapts a Pages Router (`NextRouter`) to the App Router Instance. */\nexport function adaptForAppRouterInstance(\n  pagesRouter: NextRouter\n): AppRouterInstance {\n  return {\n    back() {\n      pagesRouter.back()\n    },\n    forward() {\n      pagesRouter.forward()\n    },\n    refresh() {\n      pagesRouter.reload()\n    },\n    hmrRefresh() {},\n    push(href, { scroll } = {}) {\n      void pagesRouter.push(href, undefined, { scroll })\n    },\n    replace(href, { scroll } = {}) {\n      void pagesRouter.replace(href, undefined, { scroll })\n    },\n    prefetch(href) {\n      void pagesRouter.prefetch(href)\n    },\n  }\n}\n\n/**\n * adaptForSearchParams transforms the ParsedURLQuery into URLSearchParams.\n *\n * @param router the router that contains the query.\n * @returns the search params in the URLSearchParams format\n */\nexport function adaptForSearchParams(\n  router: Pick<NextRouter, 'isReady' | 'query' | 'asPath'>\n): URLSearchParams {\n  if (!router.isReady || !router.query) {\n    return new URLSearchParams()\n  }\n\n  return asPathToSearchParams(router.asPath)\n}\n\nexport function adaptForPathParams(\n  router: Pick<NextRouter, 'isReady' | 'pathname' | 'query' | 'asPath'>\n): Params | null {\n  if (!router.isReady || !router.query) {\n    return null\n  }\n  const pathParams: Params = {}\n  const routeRegex = getRouteRegex(router.pathname)\n  const keys = Object.keys(routeRegex.groups)\n  for (const key of keys) {\n    pathParams[key] = router.query[key]!\n  }\n  return pathParams\n}\n\nexport function PathnameContextProviderAdapter({\n  children,\n  router,\n  ...props\n}: React.PropsWithChildren<{\n  router: Pick<NextRouter, 'pathname' | 'asPath' | 'isReady' | 'isFallback'>\n  isAutoExport: boolean\n}>) {\n  const ref = useRef(props.isAutoExport)\n  const value = useMemo(() => {\n    // isAutoExport is only ever `true` on the first render from the server,\n    // so reset it to `false` after we read it for the first time as `true`. If\n    // we don't use the value, then we don't need it.\n    const isAutoExport = ref.current\n    if (isAutoExport) {\n      ref.current = false\n    }\n\n    // When the route is a dynamic route, we need to do more processing to\n    // determine if we need to stop showing the pathname.\n    if (isDynamicRoute(router.pathname)) {\n      // When the router is rendering the fallback page, it can't possibly know\n      // the path, so return `null` here. Read more about fallback pages over\n      // at:\n      // https://nextjs.org/docs/api-reference/data-fetching/get-static-paths#fallback-pages\n      if (router.isFallback) {\n        return null\n      }\n\n      // When `isAutoExport` is true, meaning this is a page page has been\n      // automatically statically optimized, and the router is not ready, then\n      // we can't know the pathname yet. Read more about automatic static\n      // optimization at:\n      // https://nextjs.org/docs/advanced-features/automatic-static-optimization\n      if (isAutoExport && !router.isReady) {\n        return null\n      }\n    }\n\n    // The `router.asPath` contains the pathname seen by the browser (including\n    // any query strings), so it should have that stripped. Read more about the\n    // `asPath` option over at:\n    // https://nextjs.org/docs/api-reference/next/router#router-object\n    let url: URL\n    try {\n      url = new URL(router.asPath, 'http://f')\n    } catch (_) {\n      // fallback to / for invalid asPath values e.g. //\n      return '/'\n    }\n\n    return url.pathname\n  }, [router.asPath, router.isFallback, router.isReady, router.pathname])\n\n  return (\n    <PathnameContext.Provider value={value}>\n      {children}\n    </PathnameContext.Provider>\n  )\n}\n", "import { pathHasPrefix } from '../shared/lib/router/utils/path-has-prefix'\n\nconst basePath = (process.env.__NEXT_ROUTER_BASEPATH as string) || ''\n\nexport function hasBasePath(path: string): boolean {\n  return pathHasPrefix(path, basePath)\n}\n", "// Note: This file is JS because it's used by the taskfile-swc.js file, which is JS.\n// Keep file changes in sync with the corresponding `.d.ts` files.\n/**\n * These are the browser versions that support all of the following:\n * static import: https://caniuse.com/es6-module\n * dynamic import: https://caniuse.com/es6-module-dynamic-import\n * import.meta: https://caniuse.com/mdn-javascript_operators_import_meta\n */\nconst MODERN_BROWSERSLIST_TARGET = [\n  'chrome 64',\n  'edge 79',\n  'firefox 67',\n  'opera 51',\n  'safari 12',\n]\n\nmodule.exports = MODERN_BROWSERSLIST_TARGET\n", "import { normalizeLocalePath } from '../../i18n/normalize-locale-path'\nimport { removePathPrefix } from './remove-path-prefix'\nimport { pathHasPrefix } from './path-has-prefix'\nimport type { I18NProvider } from '../../../../server/lib/i18n-provider'\n\nexport interface NextPathnameInfo {\n  /**\n   * The base path in case the pathname included it.\n   */\n  basePath?: string\n  /**\n   * The buildId for when the parsed URL is a data URL. Parsing it can be\n   * disabled with the `parseData` option.\n   */\n  buildId?: string\n  /**\n   * If there was a locale in the pathname, this will hold its value.\n   */\n  locale?: string\n  /**\n   * The processed pathname without a base path, locale, or data URL elements\n   * when parsing it is enabled.\n   */\n  pathname: string\n  /**\n   * A boolean telling if the pathname had a trailingSlash. This can be only\n   * true if trailingSlash is enabled.\n   */\n  trailingSlash?: boolean\n}\n\ninterface Options {\n  /**\n   * When passed to true, this function will also parse Nextjs data URLs.\n   */\n  parseData?: boolean\n  /**\n   * A partial of the Next.js configuration to parse the URL.\n   */\n  nextConfig?: {\n    basePath?: string\n    i18n?: { locales?: readonly string[] } | null\n    trailingSlash?: boolean\n  }\n\n  /**\n   * If provided, this normalizer will be used to detect the locale instead of\n   * the default locale detection.\n   */\n  i18nProvider?: I18NProvider\n}\n\nexport function getNextPathnameInfo(\n  pathname: string,\n  options: Options\n): NextPathnameInfo {\n  const { basePath, i18n, trailingSlash } = options.nextConfig ?? {}\n  const info: NextPathnameInfo = {\n    pathname,\n    trailingSlash: pathname !== '/' ? pathname.endsWith('/') : trailingSlash,\n  }\n\n  if (basePath && pathHasPrefix(info.pathname, basePath)) {\n    info.pathname = removePathPrefix(info.pathname, basePath)\n    info.basePath = basePath\n  }\n  let pathnameNoDataPrefix = info.pathname\n\n  if (\n    info.pathname.startsWith('/_next/data/') &&\n    info.pathname.endsWith('.json')\n  ) {\n    const paths = info.pathname\n      .replace(/^\\/_next\\/data\\//, '')\n      .replace(/\\.json$/, '')\n      .split('/')\n\n    const buildId = paths[0]\n    info.buildId = buildId\n    pathnameNoDataPrefix =\n      paths[1] !== 'index' ? `/${paths.slice(1).join('/')}` : '/'\n\n    // update pathname with normalized if enabled although\n    // we use normalized to populate locale info still\n    if (options.parseData === true) {\n      info.pathname = pathnameNoDataPrefix\n    }\n  }\n\n  // If provided, use the locale route normalizer to detect the locale instead\n  // of the function below.\n  if (i18n) {\n    let result = options.i18nProvider\n      ? options.i18nProvider.analyze(info.pathname)\n      : normalizeLocalePath(info.pathname, i18n.locales)\n\n    info.locale = result.detectedLocale\n    info.pathname = result.pathname ?? info.pathname\n\n    if (!result.detectedLocale && info.buildId) {\n      result = options.i18nProvider\n        ? options.i18nProvider.analyze(pathnameNoDataPrefix)\n        : normalizeLocalePath(pathnameNoDataPrefix, i18n.locales)\n\n      if (result.detectedLocale) {\n        info.locale = result.detectedLocale\n      }\n    }\n  }\n  return info\n}\n", "import type { default as Router } from '../router'\n\nexport function compareRouterStates(a: Router['state'], b: Router['state']) {\n  const stateKeys = Object.keys(a)\n  if (stateKeys.length !== Object.keys(b).length) return false\n\n  for (let i = stateKeys.length; i--; ) {\n    const key = stateKeys[i]\n    if (key === 'query') {\n      const queryKeys = Object.keys(a.query)\n      if (queryKeys.length !== Object.keys(b.query).length) {\n        return false\n      }\n      for (let j = queryKeys.length; j--; ) {\n        const queryKey = queryKeys[j]\n        if (\n          !b.query.hasOwnProperty(queryKey) ||\n          a.query[queryKey] !== b.query[queryKey]\n        ) {\n          return false\n        }\n      }\n    } else if (\n      !b.hasOwnProperty(key) ||\n      a[key as keyof Router['state']] !== b[key as keyof Router['state']]\n    ) {\n      return false\n    }\n  }\n\n  return true\n}\n", "(function(){var e={229:function(e){var t=e.exports={};var r;var n;function defaultSetTimout(){throw new Error(\"setTimeout has not been defined\")}function defaultClearTimeout(){throw new Error(\"clearTimeout has not been defined\")}(function(){try{if(typeof setTimeout===\"function\"){r=setTimeout}else{r=defaultSetTimout}}catch(e){r=defaultSetTimout}try{if(typeof clearTimeout===\"function\"){n=clearTimeout}else{n=defaultClearTimeout}}catch(e){n=defaultClearTimeout}})();function runTimeout(e){if(r===setTimeout){return setTimeout(e,0)}if((r===defaultSetTimout||!r)&&setTimeout){r=setTimeout;return setTimeout(e,0)}try{return r(e,0)}catch(t){try{return r.call(null,e,0)}catch(t){return r.call(this,e,0)}}}function runClearTimeout(e){if(n===clearTimeout){return clearTimeout(e)}if((n===defaultClearTimeout||!n)&&clearTimeout){n=clearTimeout;return clearTimeout(e)}try{return n(e)}catch(t){try{return n.call(null,e)}catch(t){return n.call(this,e)}}}var i=[];var o=false;var u;var a=-1;function cleanUpNextTick(){if(!o||!u){return}o=false;if(u.length){i=u.concat(i)}else{a=-1}if(i.length){drainQueue()}}function drainQueue(){if(o){return}var e=runTimeout(cleanUpNextTick);o=true;var t=i.length;while(t){u=i;i=[];while(++a<t){if(u){u[a].run()}}a=-1;t=i.length}u=null;o=false;runClearTimeout(e)}t.nextTick=function(e){var t=new Array(arguments.length-1);if(arguments.length>1){for(var r=1;r<arguments.length;r++){t[r-1]=arguments[r]}}i.push(new Item(e,t));if(i.length===1&&!o){runTimeout(drainQueue)}};function Item(e,t){this.fun=e;this.array=t}Item.prototype.run=function(){this.fun.apply(null,this.array)};t.title=\"browser\";t.browser=true;t.env={};t.argv=[];t.version=\"\";t.versions={};function noop(){}t.on=noop;t.addListener=noop;t.once=noop;t.off=noop;t.removeListener=noop;t.removeAllListeners=noop;t.emit=noop;t.prependListener=noop;t.prependOnceListener=noop;t.listeners=function(e){return[]};t.binding=function(e){throw new Error(\"process.binding is not supported\")};t.cwd=function(){return\"/\"};t.chdir=function(e){throw new Error(\"process.chdir is not supported\")};t.umask=function(){return 0}}};var t={};function __nccwpck_require__(r){var n=t[r];if(n!==undefined){return n.exports}var i=t[r]={exports:{}};var o=true;try{e[r](i,i.exports,__nccwpck_require__);o=false}finally{if(o)delete t[r]}return i.exports}if(typeof __nccwpck_require__!==\"undefined\")__nccwpck_require__.ab=__dirname+\"/\";var r=__nccwpck_require__(229);module.exports=r})();", "/* global location */\n// imports polyfill from `@next/polyfill-module` after build.\nimport '../build/polyfills/polyfill-module'\nimport type Router from '../shared/lib/router/router'\nimport type {\n  AppComponent,\n  AppProps,\n  PrivateRouteInfo,\n} from '../shared/lib/router/router'\n\nimport React, { type JSX } from 'react'\nimport ReactDOM from 'react-dom/client'\nimport { HeadManagerContext } from '../shared/lib/head-manager-context.shared-runtime'\nimport mitt from '../shared/lib/mitt'\nimport type { MittEmitter } from '../shared/lib/mitt'\nimport { RouterContext } from '../shared/lib/router-context.shared-runtime'\nimport { handleSmoothScroll } from '../shared/lib/router/utils/handle-smooth-scroll'\nimport { isDynamicRoute } from '../shared/lib/router/utils/is-dynamic'\nimport {\n  urlQueryToSearchParams,\n  assign,\n} from '../shared/lib/router/utils/querystring'\nimport { setConfig } from '../shared/lib/runtime-config.external'\nimport { getURL, loadGetInitialProps, ST } from '../shared/lib/utils'\nimport type { NextWebVitalsMetric, NEXT_DATA } from '../shared/lib/utils'\nimport { Portal } from './portal'\nimport initHeadManager from './head-manager'\nimport PageLoader from './page-loader'\nimport type { StyleSheetTuple } from './page-loader'\nimport { RouteAnnouncer } from './route-announcer'\nimport { createRouter, makePublicRouterInstance } from './router'\nimport { getProperError } from '../lib/is-error'\nimport { ImageConfigContext } from '../shared/lib/image-config-context.shared-runtime'\nimport type { ImageConfigComplete } from '../shared/lib/image-config'\nimport { removeBasePath } from './remove-base-path'\nimport { hasBasePath } from './has-base-path'\nimport { AppRouterContext } from '../shared/lib/app-router-context.shared-runtime'\nimport {\n  adaptForAppRouterInstance,\n  adaptForPathParams,\n  adaptForSearchParams,\n  PathnameContextProviderAdapter,\n} from '../shared/lib/router/adapters'\nimport {\n  SearchParamsContext,\n  PathParamsContext,\n} from '../shared/lib/hooks-client-context.shared-runtime'\nimport { onRecoverableError } from './react-client-callbacks/on-recoverable-error'\nimport tracer from './tracing/tracer'\nimport { isNextRouterError } from './components/is-next-router-error'\n\n/// <reference types=\"react-dom/experimental\" />\n\ndeclare global {\n  interface Window {\n    /* test fns */\n    __NEXT_HYDRATED?: boolean\n    __NEXT_HYDRATED_AT?: number\n    __NEXT_HYDRATED_CB?: () => void\n\n    /* prod */\n    __NEXT_DATA__: NEXT_DATA\n    __NEXT_P: any[]\n  }\n}\ntype RenderRouteInfo = PrivateRouteInfo & {\n  App: AppComponent\n  scroll?: { x: number; y: number } | null\n  isHydratePass?: boolean\n}\ntype RenderErrorProps = Omit<RenderRouteInfo, 'Component' | 'styleSheets'>\ntype RegisterFn = (input: [string, () => void]) => void\n\nexport const version = process.env.__NEXT_VERSION\nexport let router: Router\nexport const emitter: MittEmitter<string> = mitt()\n\nconst looseToArray = <T extends {}>(input: any): T[] => [].slice.call(input)\n\nlet initialData: NEXT_DATA\nlet defaultLocale: string | undefined = undefined\nlet asPath: string\nlet pageLoader: PageLoader\nlet appElement: HTMLElement | null\nlet headManager: {\n  mountedInstances: Set<unknown>\n  updateHead: (head: JSX.Element[]) => void\n  getIsSsr?: () => boolean\n}\nlet initialMatchesMiddleware = false\nlet lastAppProps: AppProps\n\nlet lastRenderReject: (() => void) | null\nlet devClient: any\n\nlet CachedApp: AppComponent, onPerfEntry: (metric: any) => void\nlet CachedComponent: React.ComponentType\n\nclass Container extends React.Component<{\n  children?: React.ReactNode\n  fn: (err: Error, info?: any) => void\n}> {\n  componentDidCatch(componentErr: Error, info: any) {\n    this.props.fn(componentErr, info)\n  }\n\n  componentDidMount() {\n    this.scrollToHash()\n\n    // We need to replace the router state if:\n    // - the page was (auto) exported and has a query string or search (hash)\n    // - it was auto exported and is a dynamic route (to provide params)\n    // - if it is a client-side skeleton (fallback render)\n    // - if middleware matches the current page (may have rewrite params)\n    // - if rewrites in next.config.js match (may have rewrite params)\n    if (\n      router.isSsr &&\n      (initialData.isFallback ||\n        (initialData.nextExport &&\n          (isDynamicRoute(router.pathname) ||\n            location.search ||\n            process.env.__NEXT_HAS_REWRITES ||\n            initialMatchesMiddleware)) ||\n        (initialData.props &&\n          initialData.props.__N_SSG &&\n          (location.search ||\n            process.env.__NEXT_HAS_REWRITES ||\n            initialMatchesMiddleware)))\n    ) {\n      // update query on mount for exported pages\n      router\n        .replace(\n          router.pathname +\n            '?' +\n            String(\n              assign(\n                urlQueryToSearchParams(router.query),\n                new URLSearchParams(location.search)\n              )\n            ),\n          asPath,\n          {\n            // @ts-ignore\n            // WARNING: `_h` is an internal option for handing Next.js\n            // client-side hydration. Your app should _never_ use this property.\n            // It may change at any time without notice.\n            _h: 1,\n            // Fallback pages must trigger the data fetch, so the transition is\n            // not shallow.\n            // Other pages (strictly updating query) happens shallowly, as data\n            // requirements would already be present.\n            shallow: !initialData.isFallback && !initialMatchesMiddleware,\n          }\n        )\n        .catch((err) => {\n          if (!err.cancelled) throw err\n        })\n    }\n  }\n\n  componentDidUpdate() {\n    this.scrollToHash()\n  }\n\n  scrollToHash() {\n    let { hash } = location\n    hash = hash && hash.substring(1)\n    if (!hash) return\n\n    const el: HTMLElement | null = document.getElementById(hash)\n    if (!el) return\n\n    // If we call scrollIntoView() in here without a setTimeout\n    // it won't scroll properly.\n    setTimeout(() => el.scrollIntoView(), 0)\n  }\n\n  render() {\n    if (process.env.NODE_ENV === 'production') {\n      return this.props.children\n    } else {\n      const {\n        PagesDevOverlay,\n      }: typeof import('./components/react-dev-overlay/pages/pages-dev-overlay') = require('./components/react-dev-overlay/pages/pages-dev-overlay')\n      return <PagesDevOverlay>{this.props.children}</PagesDevOverlay>\n    }\n  }\n}\n\nexport async function initialize(opts: { devClient?: any } = {}): Promise<{\n  assetPrefix: string\n}> {\n  // This makes sure this specific lines are removed in production\n  if (process.env.NODE_ENV === 'development') {\n    tracer.onSpanEnd(\n      (\n        require('./tracing/report-to-socket') as typeof import('./tracing/report-to-socket')\n      ).default\n    )\n    devClient = opts.devClient\n  }\n\n  initialData = JSON.parse(\n    document.getElementById('__NEXT_DATA__')!.textContent!\n  )\n  window.__NEXT_DATA__ = initialData\n\n  defaultLocale = initialData.defaultLocale\n  const prefix: string = initialData.assetPrefix || ''\n  // With dynamic assetPrefix it's no longer possible to set assetPrefix at the build time\n  // So, this is how we do it in the client side at runtime\n  ;(self as any).__next_set_public_path__(`${prefix}/_next/`) //eslint-disable-line\n\n  // Initialize next/config with the environment configuration\n  setConfig({\n    serverRuntimeConfig: {},\n    publicRuntimeConfig: initialData.runtimeConfig || {},\n  })\n\n  asPath = getURL()\n\n  // make sure not to attempt stripping basePath for 404s\n  if (hasBasePath(asPath)) {\n    asPath = removeBasePath(asPath)\n  }\n\n  if (process.env.__NEXT_I18N_SUPPORT) {\n    const { normalizeLocalePath } =\n      require('../shared/lib/i18n/normalize-locale-path') as typeof import('../shared/lib/i18n/normalize-locale-path')\n\n    const { detectDomainLocale } =\n      require('../shared/lib/i18n/detect-domain-locale') as typeof import('../shared/lib/i18n/detect-domain-locale')\n\n    const { parseRelativeUrl } =\n      require('../shared/lib/router/utils/parse-relative-url') as typeof import('../shared/lib/router/utils/parse-relative-url')\n\n    const { formatUrl } =\n      require('../shared/lib/router/utils/format-url') as typeof import('../shared/lib/router/utils/format-url')\n\n    if (initialData.locales) {\n      const parsedAs = parseRelativeUrl(asPath)\n      const localePathResult = normalizeLocalePath(\n        parsedAs.pathname,\n        initialData.locales\n      )\n\n      if (localePathResult.detectedLocale) {\n        parsedAs.pathname = localePathResult.pathname\n        asPath = formatUrl(parsedAs)\n      } else {\n        // derive the default locale if it wasn't detected in the asPath\n        // since we don't prerender static pages with all possible default\n        // locales\n        defaultLocale = initialData.locale\n      }\n\n      // attempt detecting default locale based on hostname\n      const detectedDomain = detectDomainLocale(\n        process.env.__NEXT_I18N_DOMAINS as any,\n        window.location.hostname\n      )\n\n      // TODO: investigate if defaultLocale needs to be populated after\n      // hydration to prevent mismatched renders\n      if (detectedDomain) {\n        defaultLocale = detectedDomain.defaultLocale\n      }\n    }\n  }\n\n  if (initialData.scriptLoader) {\n    const { initScriptLoader } = require('./script')\n    initScriptLoader(initialData.scriptLoader)\n  }\n\n  pageLoader = new PageLoader(initialData.buildId, prefix)\n\n  const register: RegisterFn = ([r, f]) =>\n    pageLoader.routeLoader.onEntrypoint(r, f)\n  if (window.__NEXT_P) {\n    // Defer page registration for another tick. This will increase the overall\n    // latency in hydrating the page, but reduce the total blocking time.\n    window.__NEXT_P.map((p) => setTimeout(() => register(p), 0))\n  }\n  window.__NEXT_P = []\n  ;(window.__NEXT_P as any).push = register\n\n  headManager = initHeadManager()\n  headManager.getIsSsr = () => {\n    return router.isSsr\n  }\n\n  appElement = document.getElementById('__next')\n  return { assetPrefix: prefix }\n}\n\nfunction renderApp(App: AppComponent, appProps: AppProps) {\n  return <App {...appProps} />\n}\n\nfunction AppContainer({\n  children,\n}: React.PropsWithChildren<{}>): React.ReactElement {\n  // Create a memoized value for next/navigation router context.\n  const adaptedForAppRouter = React.useMemo(() => {\n    return adaptForAppRouterInstance(router)\n  }, [])\n  return (\n    <Container\n      fn={(error) =>\n        // TODO: Fix disabled eslint rule\n        // eslint-disable-next-line @typescript-eslint/no-use-before-define\n        renderError({ App: CachedApp, err: error }).catch((err) =>\n          console.error('Error rendering page: ', err)\n        )\n      }\n    >\n      <AppRouterContext.Provider value={adaptedForAppRouter}>\n        <SearchParamsContext.Provider value={adaptForSearchParams(router)}>\n          <PathnameContextProviderAdapter\n            router={router}\n            isAutoExport={self.__NEXT_DATA__.autoExport ?? false}\n          >\n            <PathParamsContext.Provider value={adaptForPathParams(router)}>\n              <RouterContext.Provider value={makePublicRouterInstance(router)}>\n                <HeadManagerContext.Provider value={headManager}>\n                  <ImageConfigContext.Provider\n                    value={\n                      process.env\n                        .__NEXT_IMAGE_OPTS as any as ImageConfigComplete\n                    }\n                  >\n                    {children}\n                  </ImageConfigContext.Provider>\n                </HeadManagerContext.Provider>\n              </RouterContext.Provider>\n            </PathParamsContext.Provider>\n          </PathnameContextProviderAdapter>\n        </SearchParamsContext.Provider>\n      </AppRouterContext.Provider>\n    </Container>\n  )\n}\n\nconst wrapApp =\n  (App: AppComponent) =>\n  (wrappedAppProps: Record<string, any>): JSX.Element => {\n    const appProps: AppProps = {\n      ...wrappedAppProps,\n      Component: CachedComponent,\n      err: initialData.err,\n      router,\n    }\n    return <AppContainer>{renderApp(App, appProps)}</AppContainer>\n  }\n\n// This method handles all runtime and debug errors.\n// 404 and 500 errors are special kind of errors\n// and they are still handle via the main render method.\nfunction renderError(renderErrorProps: RenderErrorProps): Promise<any> {\n  let { App, err } = renderErrorProps\n\n  // In development runtime errors are caught by our overlay\n  // In production we catch runtime errors using componentDidCatch which will trigger renderError\n  if (process.env.NODE_ENV !== 'production') {\n    // A Next.js rendering runtime error is always unrecoverable\n    // FIXME: let's make this recoverable (error in GIP client-transition)\n    devClient.onUnrecoverableError()\n\n    // We need to render an empty <App> so that the `<ReactDevOverlay>` can\n    // render itself.\n    // TODO: Fix disabled eslint rule\n    // eslint-disable-next-line @typescript-eslint/no-use-before-define\n    return doRender({\n      App: () => null,\n      props: {},\n      Component: () => null,\n      styleSheets: [],\n    })\n  }\n\n  // Make sure we log the error to the console, otherwise users can't track down issues.\n  console.error(err)\n  console.error(\n    `A client-side exception has occurred, see here for more info: https://nextjs.org/docs/messages/client-side-exception-occurred`\n  )\n\n  return pageLoader\n    .loadPage('/_error')\n    .then(({ page: ErrorComponent, styleSheets }) => {\n      return lastAppProps?.Component === ErrorComponent\n        ? import('../pages/_error')\n            .then((errorModule) => {\n              return import('../pages/_app').then((appModule) => {\n                App = appModule.default as any as AppComponent\n                renderErrorProps.App = App\n                return errorModule\n              })\n            })\n            .then((m) => ({\n              ErrorComponent: m.default as React.ComponentType<{}>,\n              styleSheets: [],\n            }))\n        : { ErrorComponent, styleSheets }\n    })\n    .then(({ ErrorComponent, styleSheets }) => {\n      // In production we do a normal render with the `ErrorComponent` as component.\n      // If we've gotten here upon initial render, we can use the props from the server.\n      // Otherwise, we need to call `getInitialProps` on `App` before mounting.\n      const AppTree = wrapApp(App)\n      const appCtx = {\n        Component: ErrorComponent,\n        AppTree,\n        router,\n        ctx: {\n          err,\n          pathname: initialData.page,\n          query: initialData.query,\n          asPath,\n          AppTree,\n        },\n      }\n      return Promise.resolve(\n        renderErrorProps.props?.err\n          ? renderErrorProps.props\n          : loadGetInitialProps(App, appCtx)\n      ).then((initProps) =>\n        // TODO: Fix disabled eslint rule\n        // eslint-disable-next-line @typescript-eslint/no-use-before-define\n        doRender({\n          ...renderErrorProps,\n          err,\n          Component: ErrorComponent,\n          styleSheets,\n          props: initProps,\n        })\n      )\n    })\n}\n\n// Dummy component that we render as a child of Root so that we can\n// toggle the correct styles before the page is rendered.\nfunction Head({ callback }: { callback: () => void }): null {\n  // We use `useLayoutEffect` to guarantee the callback is executed\n  // as soon as React flushes the update.\n  React.useLayoutEffect(() => callback(), [callback])\n  return null\n}\n\nconst performanceMarks = {\n  navigationStart: 'navigationStart',\n  beforeRender: 'beforeRender',\n  afterRender: 'afterRender',\n  afterHydrate: 'afterHydrate',\n  routeChange: 'routeChange',\n} as const\n\nconst performanceMeasures = {\n  hydration: 'Next.js-hydration',\n  beforeHydration: 'Next.js-before-hydration',\n  routeChangeToRender: 'Next.js-route-change-to-render',\n  render: 'Next.js-render',\n} as const\n\nlet reactRoot: any = null\n// On initial render a hydrate should always happen\nlet shouldHydrate: boolean = true\n\nfunction clearMarks(): void {\n  ;[\n    performanceMarks.beforeRender,\n    performanceMarks.afterHydrate,\n    performanceMarks.afterRender,\n    performanceMarks.routeChange,\n  ].forEach((mark) => performance.clearMarks(mark))\n}\n\nfunction markHydrateComplete(): void {\n  if (!ST) return\n\n  performance.mark(performanceMarks.afterHydrate) // mark end of hydration\n\n  const hasBeforeRenderMark = performance.getEntriesByName(\n    performanceMarks.beforeRender,\n    'mark'\n  ).length\n  if (hasBeforeRenderMark) {\n    const beforeHydrationMeasure = performance.measure(\n      performanceMeasures.beforeHydration,\n      performanceMarks.navigationStart,\n      performanceMarks.beforeRender\n    )\n\n    const hydrationMeasure = performance.measure(\n      performanceMeasures.hydration,\n      performanceMarks.beforeRender,\n      performanceMarks.afterHydrate\n    )\n\n    if (\n      process.env.NODE_ENV === 'development' &&\n      // Old versions of Safari don't return `PerformanceMeasure`s from `performance.measure()`\n      beforeHydrationMeasure &&\n      hydrationMeasure\n    ) {\n      tracer\n        .startSpan('navigation-to-hydration', {\n          startTime: performance.timeOrigin + beforeHydrationMeasure.startTime,\n          attributes: {\n            pathname: location.pathname,\n            query: location.search,\n          },\n        })\n        .end(\n          performance.timeOrigin +\n            hydrationMeasure.startTime +\n            hydrationMeasure.duration\n        )\n    }\n  }\n\n  if (onPerfEntry) {\n    performance\n      .getEntriesByName(performanceMeasures.hydration)\n      .forEach(onPerfEntry)\n  }\n  clearMarks()\n}\n\nfunction markRenderComplete(): void {\n  if (!ST) return\n\n  performance.mark(performanceMarks.afterRender) // mark end of render\n  const navStartEntries: PerformanceEntryList = performance.getEntriesByName(\n    performanceMarks.routeChange,\n    'mark'\n  )\n\n  if (!navStartEntries.length) return\n\n  const hasBeforeRenderMark = performance.getEntriesByName(\n    performanceMarks.beforeRender,\n    'mark'\n  ).length\n\n  if (hasBeforeRenderMark) {\n    performance.measure(\n      performanceMeasures.routeChangeToRender,\n      navStartEntries[0].name,\n      performanceMarks.beforeRender\n    )\n    performance.measure(\n      performanceMeasures.render,\n      performanceMarks.beforeRender,\n      performanceMarks.afterRender\n    )\n    if (onPerfEntry) {\n      performance\n        .getEntriesByName(performanceMeasures.render)\n        .forEach(onPerfEntry)\n      performance\n        .getEntriesByName(performanceMeasures.routeChangeToRender)\n        .forEach(onPerfEntry)\n    }\n  }\n\n  clearMarks()\n  ;[\n    performanceMeasures.routeChangeToRender,\n    performanceMeasures.render,\n  ].forEach((measure) => performance.clearMeasures(measure))\n}\n\nfunction renderReactElement(\n  domEl: HTMLElement,\n  fn: (cb: () => void) => JSX.Element\n): void {\n  // mark start of hydrate/render\n  if (ST) {\n    performance.mark(performanceMarks.beforeRender)\n  }\n\n  const reactEl = fn(shouldHydrate ? markHydrateComplete : markRenderComplete)\n  if (!reactRoot) {\n    // Unlike with createRoot, you don't need a separate root.render() call here\n    reactRoot = ReactDOM.hydrateRoot(domEl, reactEl, {\n      onRecoverableError,\n    })\n    // TODO: Remove shouldHydrate variable when React 18 is stable as it can depend on `reactRoot` existing\n    shouldHydrate = false\n  } else {\n    const startTransition = (React as any).startTransition\n    startTransition(() => {\n      reactRoot.render(reactEl)\n    })\n  }\n}\n\nfunction Root({\n  callbacks,\n  children,\n}: React.PropsWithChildren<{\n  callbacks: Array<() => void>\n}>): React.ReactElement {\n  // We use `useLayoutEffect` to guarantee the callbacks are executed\n  // as soon as React flushes the update\n  React.useLayoutEffect(\n    () => callbacks.forEach((callback) => callback()),\n    [callbacks]\n  )\n\n  if (process.env.__NEXT_TEST_MODE) {\n    // eslint-disable-next-line react-hooks/rules-of-hooks\n    React.useEffect(() => {\n      window.__NEXT_HYDRATED = true\n      window.__NEXT_HYDRATED_AT = performance.now()\n\n      if (window.__NEXT_HYDRATED_CB) {\n        window.__NEXT_HYDRATED_CB()\n      }\n    }, [])\n  }\n\n  return children as React.ReactElement\n}\n\nfunction doRender(input: RenderRouteInfo): Promise<any> {\n  let { App, Component, props, err }: RenderRouteInfo = input\n  let styleSheets: StyleSheetTuple[] | undefined =\n    'initial' in input ? undefined : input.styleSheets\n  Component = Component || lastAppProps.Component\n  props = props || lastAppProps.props\n\n  const appProps: AppProps = {\n    ...props,\n    Component,\n    err,\n    router,\n  }\n  // lastAppProps has to be set before ReactDom.render to account for ReactDom throwing an error.\n  lastAppProps = appProps\n\n  let canceled: boolean = false\n  let resolvePromise: () => void\n  const renderPromise = new Promise<void>((resolve, reject) => {\n    if (lastRenderReject) {\n      lastRenderReject()\n    }\n    resolvePromise = () => {\n      lastRenderReject = null\n      resolve()\n    }\n    lastRenderReject = () => {\n      canceled = true\n      lastRenderReject = null\n\n      const error: any = new Error('Cancel rendering route')\n      error.cancelled = true\n      reject(error)\n    }\n  })\n\n  // This function has a return type to ensure it doesn't start returning a\n  // Promise. It should remain synchronous.\n  function onStart(): boolean {\n    if (\n      !styleSheets ||\n      // We use `style-loader` in development, so we don't need to do anything\n      // unless we're in production:\n      process.env.NODE_ENV !== 'production'\n    ) {\n      return false\n    }\n\n    const currentStyleTags: HTMLStyleElement[] = looseToArray<HTMLStyleElement>(\n      document.querySelectorAll('style[data-n-href]')\n    )\n    const currentHrefs: Set<string | null> = new Set(\n      currentStyleTags.map((tag) => tag.getAttribute('data-n-href'))\n    )\n\n    const noscript: Element | null = document.querySelector(\n      'noscript[data-n-css]'\n    )\n    const nonce: string | null | undefined =\n      noscript?.getAttribute('data-n-css')\n\n    styleSheets.forEach(({ href, text }: { href: string; text: any }) => {\n      if (!currentHrefs.has(href)) {\n        const styleTag = document.createElement('style')\n        styleTag.setAttribute('data-n-href', href)\n        styleTag.setAttribute('media', 'x')\n\n        if (nonce) {\n          styleTag.setAttribute('nonce', nonce)\n        }\n\n        document.head.appendChild(styleTag)\n        styleTag.appendChild(document.createTextNode(text))\n      }\n    })\n    return true\n  }\n\n  function onHeadCommit(): void {\n    if (\n      // Turbopack has it's own css injection handling, this code ends up removing the CSS.\n      !process.env.TURBOPACK &&\n      // We use `style-loader` in development, so we don't need to do anything\n      // unless we're in production:\n      process.env.NODE_ENV === 'production' &&\n      // We can skip this during hydration. Running it wont cause any harm, but\n      // we may as well save the CPU cycles:\n      styleSheets &&\n      // Ensure this render was not canceled\n      !canceled\n    ) {\n      const desiredHrefs: Set<string> = new Set(styleSheets.map((s) => s.href))\n      const currentStyleTags: HTMLStyleElement[] =\n        looseToArray<HTMLStyleElement>(\n          document.querySelectorAll('style[data-n-href]')\n        )\n      const currentHrefs: string[] = currentStyleTags.map(\n        (tag) => tag.getAttribute('data-n-href')!\n      )\n\n      // Toggle `<style>` tags on or off depending on if they're needed:\n      for (let idx = 0; idx < currentHrefs.length; ++idx) {\n        if (desiredHrefs.has(currentHrefs[idx])) {\n          currentStyleTags[idx].removeAttribute('media')\n        } else {\n          currentStyleTags[idx].setAttribute('media', 'x')\n        }\n      }\n\n      // Reorder styles into intended order:\n      let referenceNode: Element | null = document.querySelector(\n        'noscript[data-n-css]'\n      )\n      if (\n        // This should be an invariant:\n        referenceNode\n      ) {\n        styleSheets.forEach(({ href }: { href: string }) => {\n          const targetTag: Element | null = document.querySelector(\n            `style[data-n-href=\"${href}\"]`\n          )\n          if (\n            // This should be an invariant:\n            targetTag\n          ) {\n            referenceNode!.parentNode!.insertBefore(\n              targetTag,\n              referenceNode!.nextSibling\n            )\n            referenceNode = targetTag\n          }\n        })\n      }\n\n      // Finally, clean up server rendered stylesheets:\n      looseToArray<HTMLLinkElement>(\n        document.querySelectorAll('link[data-n-p]')\n      ).forEach((el) => {\n        el.parentNode!.removeChild(el)\n      })\n    }\n\n    if (input.scroll) {\n      const { x, y } = input.scroll\n      handleSmoothScroll(() => {\n        window.scrollTo(x, y)\n      })\n    }\n  }\n\n  function onRootCommit(): void {\n    resolvePromise()\n  }\n\n  onStart()\n\n  const elem: JSX.Element = (\n    <>\n      <Head callback={onHeadCommit} />\n      <AppContainer>\n        {renderApp(App, appProps)}\n        <Portal type=\"next-route-announcer\">\n          <RouteAnnouncer />\n        </Portal>\n      </AppContainer>\n    </>\n  )\n\n  // We catch runtime errors using componentDidCatch which will trigger renderError\n  renderReactElement(appElement!, (callback) => (\n    <Root callbacks={[callback, onRootCommit]}>\n      {process.env.__NEXT_STRICT_MODE ? (\n        <React.StrictMode>{elem}</React.StrictMode>\n      ) : (\n        elem\n      )}\n    </Root>\n  ))\n\n  return renderPromise\n}\n\nasync function render(renderingProps: RenderRouteInfo): Promise<void> {\n  // if an error occurs in a server-side page (e.g. in getInitialProps),\n  // skip re-rendering the error page client-side as data-fetching operations\n  // will already have been done on the server and NEXT_DATA contains the correct\n  // data for straight-forward hydration of the error page\n  if (\n    renderingProps.err &&\n    // renderingProps.Component might be undefined if there is a top/module-level error\n    (typeof renderingProps.Component === 'undefined' ||\n      !renderingProps.isHydratePass)\n  ) {\n    await renderError(renderingProps)\n    return\n  }\n\n  try {\n    await doRender(renderingProps)\n  } catch (err) {\n    const renderErr = getProperError(err)\n    // bubble up cancelation errors\n    if ((renderErr as Error & { cancelled?: boolean }).cancelled) {\n      throw renderErr\n    }\n\n    if (process.env.NODE_ENV === 'development') {\n      // Ensure this error is displayed in the overlay in development\n      setTimeout(() => {\n        throw renderErr\n      })\n    }\n    await renderError({ ...renderingProps, err: renderErr })\n  }\n}\n\nexport async function hydrate(opts?: { beforeRender?: () => Promise<void> }) {\n  let initialErr = initialData.err\n\n  try {\n    const appEntrypoint = await pageLoader.routeLoader.whenEntrypoint('/_app')\n    if ('error' in appEntrypoint) {\n      throw appEntrypoint.error\n    }\n\n    const { component: app, exports: mod } = appEntrypoint\n    CachedApp = app as AppComponent\n    if (mod && mod.reportWebVitals) {\n      onPerfEntry = ({\n        id,\n        name,\n        startTime,\n        value,\n        duration,\n        entryType,\n        entries,\n        attribution,\n      }: any): void => {\n        // Combines timestamp with random number for unique ID\n        const uniqueID: string = `${Date.now()}-${\n          Math.floor(Math.random() * (9e12 - 1)) + 1e12\n        }`\n        let perfStartEntry: string | undefined\n\n        if (entries && entries.length) {\n          perfStartEntry = entries[0].startTime\n        }\n\n        const webVitals: NextWebVitalsMetric = {\n          id: id || uniqueID,\n          name,\n          startTime: startTime || perfStartEntry,\n          value: value == null ? duration : value,\n          label:\n            entryType === 'mark' || entryType === 'measure'\n              ? 'custom'\n              : 'web-vital',\n        }\n        if (attribution) {\n          webVitals.attribution = attribution\n        }\n        mod.reportWebVitals(webVitals)\n      }\n    }\n\n    const pageEntrypoint =\n      // The dev server fails to serve script assets when there's a hydration\n      // error, so we need to skip waiting for the entrypoint.\n      process.env.NODE_ENV === 'development' && initialData.err\n        ? { error: initialData.err }\n        : await pageLoader.routeLoader.whenEntrypoint(initialData.page)\n    if ('error' in pageEntrypoint) {\n      throw pageEntrypoint.error\n    }\n    CachedComponent = pageEntrypoint.component\n\n    if (process.env.NODE_ENV !== 'production') {\n      const { isValidElementType } = require('next/dist/compiled/react-is')\n      if (!isValidElementType(CachedComponent)) {\n        throw new Error(\n          `The default export is not a React Component in page: \"${initialData.page}\"`\n        )\n      }\n    }\n  } catch (error) {\n    // This catches errors like throwing in the top level of a module\n    initialErr = getProperError(error)\n  }\n\n  if (process.env.NODE_ENV === 'development') {\n    const getServerError: typeof import('./components/react-dev-overlay/pages/client').getServerError =\n      require('./components/react-dev-overlay/pages/client').getServerError\n    // Server-side runtime errors need to be re-thrown on the client-side so\n    // that the overlay is rendered.\n    if (initialErr) {\n      if (initialErr === initialData.err) {\n        setTimeout(() => {\n          let error\n          try {\n            // Generate a new error object. We `throw` it because some browsers\n            // will set the `stack` when thrown, and we want to ensure ours is\n            // not overridden when we re-throw it below.\n            throw new Error(initialErr!.message)\n          } catch (e) {\n            error = e as Error\n          }\n\n          error.name = initialErr!.name\n          error.stack = initialErr!.stack\n          const errSource = initialErr.source!\n\n          // In development, error the navigation API usage in runtime,\n          // since it's not allowed to be used in pages router as it doesn't contain error boundary like app router.\n          if (isNextRouterError(initialErr)) {\n            error.message =\n              'Next.js navigation API is not allowed to be used in Pages Router.'\n          }\n\n          throw getServerError(error, errSource)\n        })\n      }\n      // We replaced the server-side error with a client-side error, and should\n      // no longer rewrite the stack trace to a Node error.\n      else {\n        setTimeout(() => {\n          throw initialErr\n        })\n      }\n    }\n  }\n\n  if (window.__NEXT_PRELOADREADY) {\n    await window.__NEXT_PRELOADREADY(initialData.dynamicIds)\n  }\n\n  router = createRouter(initialData.page, initialData.query, asPath, {\n    initialProps: initialData.props,\n    pageLoader,\n    App: CachedApp,\n    Component: CachedComponent,\n    wrapApp,\n    err: initialErr,\n    isFallback: Boolean(initialData.isFallback),\n    subscription: (info, App, scroll) =>\n      render(\n        Object.assign<\n          {},\n          Omit<RenderRouteInfo, 'App' | 'scroll'>,\n          Pick<RenderRouteInfo, 'App' | 'scroll'>\n        >({}, info, {\n          App,\n          scroll,\n        }) as RenderRouteInfo\n      ),\n    locale: initialData.locale,\n    locales: initialData.locales,\n    defaultLocale,\n    domainLocales: initialData.domainLocales,\n    isPreview: initialData.isPreview,\n  })\n\n  initialMatchesMiddleware = await router._initialMatchesMiddlewarePromise\n\n  const renderCtx: RenderRouteInfo = {\n    App: CachedApp,\n    initial: true,\n    Component: CachedComponent,\n    props: initialData.props,\n    err: initialErr,\n    isHydratePass: true,\n  }\n\n  if (opts?.beforeRender) {\n    await opts.beforeRender()\n  }\n\n  render(renderCtx)\n}\n", "// Convert router.asPath to a URLSearchParams object\n// example: /dynamic/[slug]?foo=bar -> { foo: 'bar' }\nexport function asPathToSearchParams(asPath: string): URLSearchParams {\n  return new URL(asPath, 'http://n').searchParams\n}\n", "// Translates a logical route into its pages asset path (relative from a common prefix)\n// \"asset path\" being its javascript file, data file, prerendered html,...\nexport default function getAssetPathFromRoute(\n  route: string,\n  ext: string = ''\n): string {\n  const path =\n    route === '/'\n      ? '/index'\n      : /^\\/index(\\/|$)/.test(route)\n        ? `/index${route}`\n        : route\n  return path + ext\n}\n", "/* (ignored) */", "export const reportGlobalError =\n  typeof reportError === 'function'\n    ? // In modern browsers, reportError will dispatch an error event,\n      // emulating an uncaught JavaScript error.\n      reportError\n    : (error: unknown) => {\n        // TODO: Dispatch error event\n        globalThis.console.error(error)\n      }\n", "\"trimStart\"in String.prototype||(String.prototype.trimStart=String.prototype.trimLeft),\"trimEnd\"in String.prototype||(String.prototype.trimEnd=String.prototype.trimRight),\"description\"in Symbol.prototype||Object.defineProperty(Symbol.prototype,\"description\",{configurable:!0,get:function(){var t=/\\((.*)\\)/.exec(this.toString());return t?t[1]:void 0}}),Array.prototype.flat||(Array.prototype.flat=function(t,r){return r=this.concat.apply([],this),t>1&&r.some(Array.isArray)?r.flat(t-1):r},Array.prototype.flatMap=function(t,r){return this.map(t,r).flat()}),Promise.prototype.finally||(Promise.prototype.finally=function(t){if(\"function\"!=typeof t)return this.then(t,t);var r=this.constructor||Promise;return this.then(function(n){return r.resolve(t()).then(function(){return n})},function(n){return r.resolve(t()).then(function(){throw n})})}),Object.fromEntries||(Object.fromEntries=function(t){return Array.from(t).reduce(function(t,r){return t[r[0]]=r[1],t},{})}),Array.prototype.at||(Array.prototype.at=function(t){var r=Math.trunc(t)||0;if(r<0&&(r+=this.length),!(r<0||r>=this.length))return this[r]}),Object.hasOwn||(Object.hasOwn=function(t,r){if(null==t)throw new TypeError(\"Cannot convert undefined or null to object\");return Object.prototype.hasOwnProperty.call(Object(t),r)}),\"canParse\"in URL||(URL.canParse=function(t,r){try{return!!new URL(t,r)}catch(t){return!1}});\n", "import { HTML_LIMITED_BOT_UA_RE } from './html-bots'\n\n// Bot crawler that will spin up a headless browser and execute JS\nconst HEADLESS_BROWSER_BOT_UA_RE =\n  /Googlebot|Google-PageRenderer|AdsBot-Google|googleweblight|Storebot-Google/i\n\nexport const HTML_LIMITED_BOT_UA_RE_STRING = HTML_LIMITED_BOT_UA_RE.source\n\nexport { HTML_LIMITED_BOT_UA_RE }\n\nfunction isDomBotUA(userAgent: string) {\n  return HEADLESS_BROWSER_BOT_UA_RE.test(userAgent)\n}\n\nfunction isHtmlLimitedBotUA(userAgent: string) {\n  return HTML_LIMITED_BOT_UA_RE.test(userAgent)\n}\n\nexport function isBot(userAgent: string): boolean {\n  return isDomBotUA(userAgent) || isHtmlLimitedBotUA(userAgent)\n}\n\nexport function getBotType(userAgent: string): 'dom' | 'html' | undefined {\n  if (isDomBotUA(userAgent)) {\n    return 'dom'\n  }\n  if (isHtmlLimitedBotUA(userAgent)) {\n    return 'html'\n  }\n  return undefined\n}\n", "export const VALID_LOADERS = [\n  'default',\n  'imgix',\n  'cloudinary',\n  'akamai',\n  'custom',\n] as const\n\nexport type LoaderValue = (typeof VALID_LOADERS)[number]\n\nexport type ImageLoaderProps = {\n  src: string\n  width: number\n  quality?: number\n}\n\nexport type ImageLoaderPropsWithConfig = ImageLoaderProps & {\n  config: Readonly<ImageConfig>\n}\n\nexport type LocalPattern = {\n  /**\n   * Can be literal or wildcard.\n   * Single `*` matches a single path segment.\n   * Double `**` matches any number of path segments.\n   */\n  pathname?: string\n\n  /**\n   * Can be literal query string such as `?v=1` or\n   * empty string meaning no query string.\n   */\n  search?: string\n}\n\nexport type RemotePattern = {\n  /**\n   * Must be `http` or `https`.\n   */\n  protocol?: 'http' | 'https'\n\n  /**\n   * Can be literal or wildcard.\n   * Single `*` matches a single subdomain.\n   * Double `**` matches any number of subdomains.\n   */\n  hostname: string\n\n  /**\n   * Can be literal port such as `8080` or empty string\n   * meaning no port.\n   */\n  port?: string\n\n  /**\n   * Can be literal or wildcard.\n   * Single `*` matches a single path segment.\n   * Double `**` matches any number of path segments.\n   */\n  pathname?: string\n\n  /**\n   * Can be literal query string such as `?v=1` or\n   * empty string meaning no query string.\n   */\n  search?: string\n}\n\ntype ImageFormat = 'image/avif' | 'image/webp'\n\n/**\n * Image configurations\n *\n * @see [Image configuration options](https://nextjs.org/docs/api-reference/next/image#configuration-options)\n */\nexport type ImageConfigComplete = {\n  /** @see [Device sizes documentation](https://nextjs.org/docs/api-reference/next/image#device-sizes) */\n  deviceSizes: number[]\n\n  /** @see [Image sizing documentation](https://nextjs.org/docs/app/building-your-application/optimizing/images#image-sizing) */\n  imageSizes: number[]\n\n  /** @see [Image loaders configuration](https://nextjs.org/docs/api-reference/next/legacy/image#loader) */\n  loader: LoaderValue\n\n  /** @see [Image loader configuration](https://nextjs.org/docs/api-reference/next/legacy/image#loader-configuration) */\n  path: string\n\n  /** @see [Image loader configuration](https://nextjs.org/docs/api-reference/next/image#loader-configuration) */\n  loaderFile: string\n\n  /**\n   * @deprecated Use `remotePatterns` instead.\n   */\n  domains: string[]\n\n  /** @see [Disable static image import configuration](https://nextjs.org/docs/api-reference/next/image#disable-static-imports) */\n  disableStaticImages: boolean\n\n  /** @see [Cache behavior](https://nextjs.org/docs/api-reference/next/image#caching-behavior) */\n  minimumCacheTTL: number\n\n  /** @see [Acceptable formats](https://nextjs.org/docs/api-reference/next/image#acceptable-formats) */\n  formats: ImageFormat[]\n\n  /** @see [Dangerously Allow SVG](https://nextjs.org/docs/api-reference/next/image#dangerously-allow-svg) */\n  dangerouslyAllowSVG: boolean\n\n  /** @see [Dangerously Allow SVG](https://nextjs.org/docs/api-reference/next/image#dangerously-allow-svg) */\n  contentSecurityPolicy: string\n\n  /** @see [Dangerously Allow SVG](https://nextjs.org/docs/api-reference/next/image#dangerously-allow-svg) */\n  contentDispositionType: 'inline' | 'attachment'\n\n  /** @see [Remote Patterns](https://nextjs.org/docs/api-reference/next/image#remotepatterns) */\n  remotePatterns: Array<URL | RemotePattern>\n\n  /** @see [Remote Patterns](https://nextjs.org/docs/api-reference/next/image#localPatterns) */\n  localPatterns: LocalPattern[] | undefined\n\n  /** @see [Qualities](https://nextjs.org/docs/api-reference/next/image#qualities) */\n  qualities: number[] | undefined\n\n  /** @see [Unoptimized](https://nextjs.org/docs/api-reference/next/image#unoptimized) */\n  unoptimized: boolean\n}\n\nexport type ImageConfig = Partial<ImageConfigComplete>\n\nexport const imageConfigDefault: ImageConfigComplete = {\n  deviceSizes: [640, 750, 828, 1080, 1200, 1920, 2048, 3840],\n  imageSizes: [16, 32, 48, 64, 96, 128, 256, 384],\n  path: '/_next/image',\n  loader: 'default',\n  loaderFile: '',\n  domains: [],\n  disableStaticImages: false,\n  minimumCacheTTL: 60,\n  formats: ['image/webp'],\n  dangerouslyAllowSVG: false,\n  contentSecurityPolicy: `script-src 'none'; frame-src 'none'; sandbox;`,\n  contentDispositionType: 'attachment',\n  localPatterns: undefined, // default: allow all local images\n  remotePatterns: [], // default: allow no remote images\n  qualities: undefined, // default: allow all qualities\n  unoptimized: false,\n}\n", "export function encodeURIPath(file: string) {\n  return file\n    .split('/')\n    .map((p) => encodeURIComponent(p))\n    .join('/')\n}\n", "import { addPathPrefix } from '../shared/lib/router/utils/add-path-prefix'\nimport { normalizePathTrailingSlash } from './normalize-trailing-slash'\n\nconst basePath = (process.env.__NEXT_ROUTER_BASEPATH as string) || ''\n\nexport function addBasePath(path: string, required?: boolean): string {\n  return normalizePathTrailingSlash(\n    process.env.__NEXT_MANUAL_CLIENT_BASE_PATH && !required\n      ? path\n      : addPathPrefix(path, basePath)\n  )\n}\n", "import './webpack'\nimport '../lib/require-instrumentation-client'\n\nimport { initialize, hydrate, version, router, emitter } from './'\n\ndeclare global {\n  interface Window {\n    next: any\n  }\n}\n\nwindow.next = {\n  version,\n  // router is initialized later so it has to be live-binded\n  get router() {\n    return router\n  },\n  emitter,\n}\n\ninitialize({})\n  .then(() => hydrate())\n  .catch(console.error)\n", "import { parsePath } from '../shared/lib/router/utils/parse-path'\n\nexport function removeLocale(path: string, locale?: string) {\n  if (process.env.__NEXT_I18N_SUPPORT) {\n    const { pathname } = parsePath(path)\n    const pathLower = pathname.toLowerCase()\n    const localeLower = locale?.toLowerCase()\n\n    return locale &&\n      (pathLower.startsWith(`/${localeLower}/`) ||\n        pathLower === `/${localeLower}`)\n      ? `${pathname.length === locale.length + 1 ? `/` : ``}${path.slice(\n          locale.length + 1\n        )}`\n      : path\n  }\n  return path\n}\n", "\"use strict\";\nObject.defineProperty(exports, \"__esModule\", {\n    value: true\n});\n0 && (module.exports = {\n    copyNextErrorCode: null,\n    createDigestWithErrorCode: null,\n    extractNextErrorCode: null\n});\nfunction _export(target, all) {\n    for(var name in all)Object.defineProperty(target, name, {\n        enumerable: true,\n        get: all[name]\n    });\n}\n_export(exports, {\n    copyNextErrorCode: function() {\n        return copyNextErrorCode;\n    },\n    createDigestWithErrorCode: function() {\n        return createDigestWithErrorCode;\n    },\n    extractNextErrorCode: function() {\n        return extractNextErrorCode;\n    }\n});\nconst ERROR_CODE_DELIMITER = '@';\nconst createDigestWithErrorCode = (thrownValue, originalDigest)=>{\n    if (typeof thrownValue === 'object' && thrownValue !== null && '__NEXT_ERROR_CODE' in thrownValue) {\n        return `${originalDigest}${ERROR_CODE_DELIMITER}${thrownValue.__NEXT_ERROR_CODE}`;\n    }\n    return originalDigest;\n};\nconst copyNextErrorCode = (source, target)=>{\n    const errorCode = extractNextErrorCode(source);\n    if (errorCode && typeof target === 'object' && target !== null) {\n        Object.defineProperty(target, '__NEXT_ERROR_CODE', {\n            value: errorCode,\n            enumerable: false,\n            configurable: true\n        });\n    }\n};\nconst extractNextErrorCode = (error)=>{\n    if (typeof error === 'object' && error !== null && '__NEXT_ERROR_CODE' in error && typeof error.__NEXT_ERROR_CODE === 'string') {\n        return error.__NEXT_ERROR_CODE;\n    }\n    if (typeof error === 'object' && error !== null && 'digest' in error && typeof error.digest === 'string') {\n        const segments = error.digest.split(ERROR_CODE_DELIMITER);\n        const errorCode = segments.find((segment)=>segment.startsWith('E'));\n        return errorCode;\n    }\n    return undefined;\n};\n\n//# sourceMappingURL=error-telemetry-utils.js.map", "\"use strict\";\nObject.defineProperty(exports, \"__esModule\", {\n    value: true\n});\n0 && (module.exports = {\n    ACTION_SUFFIX: null,\n    APP_DIR_ALIAS: null,\n    CACHE_ONE_YEAR: null,\n    DOT_NEXT_ALIAS: null,\n    ESLINT_DEFAULT_DIRS: null,\n    GSP_NO_RETURNED_VALUE: null,\n    GSSP_COMPONENT_MEMBER_ERROR: null,\n    GSSP_NO_RETURNED_VALUE: null,\n    INFINITE_CACHE: null,\n    INSTRUMENTATION_HOOK_FILENAME: null,\n    MATCHED_PATH_HEADER: null,\n    MIDDLEWARE_FILENAME: null,\n    MIDDLEWARE_LOCATION_REGEXP: null,\n    NEXT_BODY_SUFFIX: null,\n    NEXT_CACHE_IMPLICIT_TAG_ID: null,\n    NEXT_CACHE_REVALIDATED_TAGS_HEADER: null,\n    NEXT_CACHE_REVALIDATE_TAG_TOKEN_HEADER: null,\n    NEXT_CACHE_SOFT_TAG_MAX_LENGTH: null,\n    NEXT_CACHE_TAGS_HEADER: null,\n    NEXT_CACHE_TAG_MAX_ITEMS: null,\n    NEXT_CACHE_TAG_MAX_LENGTH: null,\n    NEXT_DATA_SUFFIX: null,\n    NEXT_INTERCEPTION_MARKER_PREFIX: null,\n    NEXT_META_SUFFIX: null,\n    NEXT_QUERY_PARAM_PREFIX: null,\n    NEXT_RESUME_HEADER: null,\n    NON_STANDARD_NODE_ENV: null,\n    PAGES_DIR_ALIAS: null,\n    PRERENDER_REVALIDATE_HEADER: null,\n    PRERENDER_REVALIDATE_ONLY_GENERATED_HEADER: null,\n    PUBLIC_DIR_MIDDLEWARE_CONFLICT: null,\n    ROOT_DIR_ALIAS: null,\n    RSC_ACTION_CLIENT_WRAPPER_ALIAS: null,\n    RSC_ACTION_ENCRYPTION_ALIAS: null,\n    RSC_ACTION_PROXY_ALIAS: null,\n    RSC_ACTION_VALIDATE_ALIAS: null,\n    RSC_CACHE_WRAPPER_ALIAS: null,\n    RSC_MOD_REF_PROXY_ALIAS: null,\n    RSC_PREFETCH_SUFFIX: null,\n    RSC_SEGMENTS_DIR_SUFFIX: null,\n    RSC_SEGMENT_SUFFIX: null,\n    RSC_SUFFIX: null,\n    SERVER_PROPS_EXPORT_ERROR: null,\n    SERVER_PROPS_GET_INIT_PROPS_CONFLICT: null,\n    SERVER_PROPS_SSG_CONFLICT: null,\n    SERVER_RUNTIME: null,\n    SSG_FALLBACK_EXPORT_ERROR: null,\n    SSG_GET_INITIAL_PROPS_CONFLICT: null,\n    STATIC_STATUS_PAGE_GET_INITIAL_PROPS_ERROR: null,\n    UNSTABLE_REVALIDATE_RENAME_ERROR: null,\n    WEBPACK_LAYERS: null,\n    WEBPACK_RESOURCE_QUERIES: null\n});\nfunction _export(target, all) {\n    for(var name in all)Object.defineProperty(target, name, {\n        enumerable: true,\n        get: all[name]\n    });\n}\n_export(exports, {\n    ACTION_SUFFIX: function() {\n        return ACTION_SUFFIX;\n    },\n    APP_DIR_ALIAS: function() {\n        return APP_DIR_ALIAS;\n    },\n    CACHE_ONE_YEAR: function() {\n        return CACHE_ONE_YEAR;\n    },\n    DOT_NEXT_ALIAS: function() {\n        return DOT_NEXT_ALIAS;\n    },\n    ESLINT_DEFAULT_DIRS: function() {\n        return ESLINT_DEFAULT_DIRS;\n    },\n    GSP_NO_RETURNED_VALUE: function() {\n        return GSP_NO_RETURNED_VALUE;\n    },\n    GSSP_COMPONENT_MEMBER_ERROR: function() {\n        return GSSP_COMPONENT_MEMBER_ERROR;\n    },\n    GSSP_NO_RETURNED_VALUE: function() {\n        return GSSP_NO_RETURNED_VALUE;\n    },\n    INFINITE_CACHE: function() {\n        return INFINITE_CACHE;\n    },\n    INSTRUMENTATION_HOOK_FILENAME: function() {\n        return INSTRUMENTATION_HOOK_FILENAME;\n    },\n    MATCHED_PATH_HEADER: function() {\n        return MATCHED_PATH_HEADER;\n    },\n    MIDDLEWARE_FILENAME: function() {\n        return MIDDLEWARE_FILENAME;\n    },\n    MIDDLEWARE_LOCATION_REGEXP: function() {\n        return MIDDLEWARE_LOCATION_REGEXP;\n    },\n    NEXT_BODY_SUFFIX: function() {\n        return NEXT_BODY_SUFFIX;\n    },\n    NEXT_CACHE_IMPLICIT_TAG_ID: function() {\n        return NEXT_CACHE_IMPLICIT_TAG_ID;\n    },\n    NEXT_CACHE_REVALIDATED_TAGS_HEADER: function() {\n        return NEXT_CACHE_REVALIDATED_TAGS_HEADER;\n    },\n    NEXT_CACHE_REVALIDATE_TAG_TOKEN_HEADER: function() {\n        return NEXT_CACHE_REVALIDATE_TAG_TOKEN_HEADER;\n    },\n    NEXT_CACHE_SOFT_TAG_MAX_LENGTH: function() {\n        return NEXT_CACHE_SOFT_TAG_MAX_LENGTH;\n    },\n    NEXT_CACHE_TAGS_HEADER: function() {\n        return NEXT_CACHE_TAGS_HEADER;\n    },\n    NEXT_CACHE_TAG_MAX_ITEMS: function() {\n        return NEXT_CACHE_TAG_MAX_ITEMS;\n    },\n    NEXT_CACHE_TAG_MAX_LENGTH: function() {\n        return NEXT_CACHE_TAG_MAX_LENGTH;\n    },\n    NEXT_DATA_SUFFIX: function() {\n        return NEXT_DATA_SUFFIX;\n    },\n    NEXT_INTERCEPTION_MARKER_PREFIX: function() {\n        return NEXT_INTERCEPTION_MARKER_PREFIX;\n    },\n    NEXT_META_SUFFIX: function() {\n        return NEXT_META_SUFFIX;\n    },\n    NEXT_QUERY_PARAM_PREFIX: function() {\n        return NEXT_QUERY_PARAM_PREFIX;\n    },\n    NEXT_RESUME_HEADER: function() {\n        return NEXT_RESUME_HEADER;\n    },\n    NON_STANDARD_NODE_ENV: function() {\n        return NON_STANDARD_NODE_ENV;\n    },\n    PAGES_DIR_ALIAS: function() {\n        return PAGES_DIR_ALIAS;\n    },\n    PRERENDER_REVALIDATE_HEADER: function() {\n        return PRERENDER_REVALIDATE_HEADER;\n    },\n    PRERENDER_REVALIDATE_ONLY_GENERATED_HEADER: function() {\n        return PRERENDER_REVALIDATE_ONLY_GENERATED_HEADER;\n    },\n    PUBLIC_DIR_MIDDLEWARE_CONFLICT: function() {\n        return PUBLIC_DIR_MIDDLEWARE_CONFLICT;\n    },\n    ROOT_DIR_ALIAS: function() {\n        return ROOT_DIR_ALIAS;\n    },\n    RSC_ACTION_CLIENT_WRAPPER_ALIAS: function() {\n        return RSC_ACTION_CLIENT_WRAPPER_ALIAS;\n    },\n    RSC_ACTION_ENCRYPTION_ALIAS: function() {\n        return RSC_ACTION_ENCRYPTION_ALIAS;\n    },\n    RSC_ACTION_PROXY_ALIAS: function() {\n        return RSC_ACTION_PROXY_ALIAS;\n    },\n    RSC_ACTION_VALIDATE_ALIAS: function() {\n        return RSC_ACTION_VALIDATE_ALIAS;\n    },\n    RSC_CACHE_WRAPPER_ALIAS: function() {\n        return RSC_CACHE_WRAPPER_ALIAS;\n    },\n    RSC_MOD_REF_PROXY_ALIAS: function() {\n        return RSC_MOD_REF_PROXY_ALIAS;\n    },\n    RSC_PREFETCH_SUFFIX: function() {\n        return RSC_PREFETCH_SUFFIX;\n    },\n    RSC_SEGMENTS_DIR_SUFFIX: function() {\n        return RSC_SEGMENTS_DIR_SUFFIX;\n    },\n    RSC_SEGMENT_SUFFIX: function() {\n        return RSC_SEGMENT_SUFFIX;\n    },\n    RSC_SUFFIX: function() {\n        return RSC_SUFFIX;\n    },\n    SERVER_PROPS_EXPORT_ERROR: function() {\n        return SERVER_PROPS_EXPORT_ERROR;\n    },\n    SERVER_PROPS_GET_INIT_PROPS_CONFLICT: function() {\n        return SERVER_PROPS_GET_INIT_PROPS_CONFLICT;\n    },\n    SERVER_PROPS_SSG_CONFLICT: function() {\n        return SERVER_PROPS_SSG_CONFLICT;\n    },\n    SERVER_RUNTIME: function() {\n        return SERVER_RUNTIME;\n    },\n    SSG_FALLBACK_EXPORT_ERROR: function() {\n        return SSG_FALLBACK_EXPORT_ERROR;\n    },\n    SSG_GET_INITIAL_PROPS_CONFLICT: function() {\n        return SSG_GET_INITIAL_PROPS_CONFLICT;\n    },\n    STATIC_STATUS_PAGE_GET_INITIAL_PROPS_ERROR: function() {\n        return STATIC_STATUS_PAGE_GET_INITIAL_PROPS_ERROR;\n    },\n    UNSTABLE_REVALIDATE_RENAME_ERROR: function() {\n        return UNSTABLE_REVALIDATE_RENAME_ERROR;\n    },\n    WEBPACK_LAYERS: function() {\n        return WEBPACK_LAYERS;\n    },\n    WEBPACK_RESOURCE_QUERIES: function() {\n        return WEBPACK_RESOURCE_QUERIES;\n    }\n});\nconst NEXT_QUERY_PARAM_PREFIX = 'nxtP';\nconst NEXT_INTERCEPTION_MARKER_PREFIX = 'nxtI';\nconst MATCHED_PATH_HEADER = 'x-matched-path';\nconst PRERENDER_REVALIDATE_HEADER = 'x-prerender-revalidate';\nconst PRERENDER_REVALIDATE_ONLY_GENERATED_HEADER = 'x-prerender-revalidate-if-generated';\nconst RSC_PREFETCH_SUFFIX = '.prefetch.rsc';\nconst RSC_SEGMENTS_DIR_SUFFIX = '.segments';\nconst RSC_SEGMENT_SUFFIX = '.segment.rsc';\nconst RSC_SUFFIX = '.rsc';\nconst ACTION_SUFFIX = '.action';\nconst NEXT_DATA_SUFFIX = '.json';\nconst NEXT_META_SUFFIX = '.meta';\nconst NEXT_BODY_SUFFIX = '.body';\nconst NEXT_CACHE_TAGS_HEADER = 'x-next-cache-tags';\nconst NEXT_CACHE_REVALIDATED_TAGS_HEADER = 'x-next-revalidated-tags';\nconst NEXT_CACHE_REVALIDATE_TAG_TOKEN_HEADER = 'x-next-revalidate-tag-token';\nconst NEXT_RESUME_HEADER = 'next-resume';\nconst NEXT_CACHE_TAG_MAX_ITEMS = 128;\nconst NEXT_CACHE_TAG_MAX_LENGTH = 256;\nconst NEXT_CACHE_SOFT_TAG_MAX_LENGTH = 1024;\nconst NEXT_CACHE_IMPLICIT_TAG_ID = '_N_T_';\nconst CACHE_ONE_YEAR = 31536000;\nconst INFINITE_CACHE = 0xfffffffe;\nconst MIDDLEWARE_FILENAME = 'middleware';\nconst MIDDLEWARE_LOCATION_REGEXP = `(?:src/)?${MIDDLEWARE_FILENAME}`;\nconst INSTRUMENTATION_HOOK_FILENAME = 'instrumentation';\nconst PAGES_DIR_ALIAS = 'private-next-pages';\nconst DOT_NEXT_ALIAS = 'private-dot-next';\nconst ROOT_DIR_ALIAS = 'private-next-root-dir';\nconst APP_DIR_ALIAS = 'private-next-app-dir';\nconst RSC_MOD_REF_PROXY_ALIAS = 'private-next-rsc-mod-ref-proxy';\nconst RSC_ACTION_VALIDATE_ALIAS = 'private-next-rsc-action-validate';\nconst RSC_ACTION_PROXY_ALIAS = 'private-next-rsc-server-reference';\nconst RSC_CACHE_WRAPPER_ALIAS = 'private-next-rsc-cache-wrapper';\nconst RSC_ACTION_ENCRYPTION_ALIAS = 'private-next-rsc-action-encryption';\nconst RSC_ACTION_CLIENT_WRAPPER_ALIAS = 'private-next-rsc-action-client-wrapper';\nconst PUBLIC_DIR_MIDDLEWARE_CONFLICT = `You can not have a '_next' folder inside of your public folder. This conflicts with the internal '/_next' route. https://nextjs.org/docs/messages/public-next-folder-conflict`;\nconst SSG_GET_INITIAL_PROPS_CONFLICT = `You can not use getInitialProps with getStaticProps. To use SSG, please remove your getInitialProps`;\nconst SERVER_PROPS_GET_INIT_PROPS_CONFLICT = `You can not use getInitialProps with getServerSideProps. Please remove getInitialProps.`;\nconst SERVER_PROPS_SSG_CONFLICT = `You can not use getStaticProps or getStaticPaths with getServerSideProps. To use SSG, please remove getServerSideProps`;\nconst STATIC_STATUS_PAGE_GET_INITIAL_PROPS_ERROR = `can not have getInitialProps/getServerSideProps, https://nextjs.org/docs/messages/404-get-initial-props`;\nconst SERVER_PROPS_EXPORT_ERROR = `pages with \\`getServerSideProps\\` can not be exported. See more info here: https://nextjs.org/docs/messages/gssp-export`;\nconst GSP_NO_RETURNED_VALUE = 'Your `getStaticProps` function did not return an object. Did you forget to add a `return`?';\nconst GSSP_NO_RETURNED_VALUE = 'Your `getServerSideProps` function did not return an object. Did you forget to add a `return`?';\nconst UNSTABLE_REVALIDATE_RENAME_ERROR = 'The `unstable_revalidate` property is available for general use.\\n' + 'Please use `revalidate` instead.';\nconst GSSP_COMPONENT_MEMBER_ERROR = `can not be attached to a page's component and must be exported from the page. See more info here: https://nextjs.org/docs/messages/gssp-component-member`;\nconst NON_STANDARD_NODE_ENV = `You are using a non-standard \"NODE_ENV\" value in your environment. This creates inconsistencies in the project and is strongly advised against. Read more: https://nextjs.org/docs/messages/non-standard-node-env`;\nconst SSG_FALLBACK_EXPORT_ERROR = `Pages with \\`fallback\\` enabled in \\`getStaticPaths\\` can not be exported. See more info here: https://nextjs.org/docs/messages/ssg-fallback-true-export`;\nconst ESLINT_DEFAULT_DIRS = [\n    'app',\n    'pages',\n    'components',\n    'lib',\n    'src'\n];\nconst SERVER_RUNTIME = {\n    edge: 'edge',\n    experimentalEdge: 'experimental-edge',\n    nodejs: 'nodejs'\n};\n/**\n * The names of the webpack layers. These layers are the primitives for the\n * webpack chunks.\n */ const WEBPACK_LAYERS_NAMES = {\n    /**\n   * The layer for the shared code between the client and server bundles.\n   */ shared: 'shared',\n    /**\n   * The layer for server-only runtime and picking up `react-server` export conditions.\n   * Including app router RSC pages and app router custom routes and metadata routes.\n   */ reactServerComponents: 'rsc',\n    /**\n   * Server Side Rendering layer for app (ssr).\n   */ serverSideRendering: 'ssr',\n    /**\n   * The browser client bundle layer for actions.\n   */ actionBrowser: 'action-browser',\n    /**\n   * The Node.js bundle layer for the API routes.\n   */ apiNode: 'api-node',\n    /**\n   * The Edge Lite bundle layer for the API routes.\n   */ apiEdge: 'api-edge',\n    /**\n   * The layer for the middleware code.\n   */ middleware: 'middleware',\n    /**\n   * The layer for the instrumentation hooks.\n   */ instrument: 'instrument',\n    /**\n   * The layer for assets on the edge.\n   */ edgeAsset: 'edge-asset',\n    /**\n   * The browser client bundle layer for App directory.\n   */ appPagesBrowser: 'app-pages-browser',\n    /**\n   * The browser client bundle layer for Pages directory.\n   */ pagesDirBrowser: 'pages-dir-browser',\n    /**\n   * The Edge Lite bundle layer for Pages directory.\n   */ pagesDirEdge: 'pages-dir-edge',\n    /**\n   * The Node.js bundle layer for Pages directory.\n   */ pagesDirNode: 'pages-dir-node'\n};\nconst WEBPACK_LAYERS = {\n    ...WEBPACK_LAYERS_NAMES,\n    GROUP: {\n        builtinReact: [\n            WEBPACK_LAYERS_NAMES.reactServerComponents,\n            WEBPACK_LAYERS_NAMES.actionBrowser\n        ],\n        serverOnly: [\n            WEBPACK_LAYERS_NAMES.reactServerComponents,\n            WEBPACK_LAYERS_NAMES.actionBrowser,\n            WEBPACK_LAYERS_NAMES.instrument,\n            WEBPACK_LAYERS_NAMES.middleware\n        ],\n        neutralTarget: [\n            // pages api\n            WEBPACK_LAYERS_NAMES.apiNode,\n            WEBPACK_LAYERS_NAMES.apiEdge\n        ],\n        clientOnly: [\n            WEBPACK_LAYERS_NAMES.serverSideRendering,\n            WEBPACK_LAYERS_NAMES.appPagesBrowser\n        ],\n        bundled: [\n            WEBPACK_LAYERS_NAMES.reactServerComponents,\n            WEBPACK_LAYERS_NAMES.actionBrowser,\n            WEBPACK_LAYERS_NAMES.serverSideRendering,\n            WEBPACK_LAYERS_NAMES.appPagesBrowser,\n            WEBPACK_LAYERS_NAMES.shared,\n            WEBPACK_LAYERS_NAMES.instrument,\n            WEBPACK_LAYERS_NAMES.middleware\n        ],\n        appPages: [\n            // app router pages and layouts\n            WEBPACK_LAYERS_NAMES.reactServerComponents,\n            WEBPACK_LAYERS_NAMES.serverSideRendering,\n            WEBPACK_LAYERS_NAMES.appPagesBrowser,\n            WEBPACK_LAYERS_NAMES.actionBrowser\n        ]\n    }\n};\nconst WEBPACK_RESOURCE_QUERIES = {\n    edgeSSREntry: '__next_edge_ssr_entry__',\n    metadata: '__next_metadata__',\n    metadataRoute: '__next_metadata_route__',\n    metadataImageMeta: '__next_metadata_image_meta__'\n};\n\n//# sourceMappingURL=constants.js.map", "/**\n * Removes the trailing slash for a given route or page path. Preserves the\n * root page. Examples:\n *   - `/foo/bar/` -> `/foo/bar`\n *   - `/foo/bar` -> `/foo/bar`\n *   - `/` -> `/`\n */\nexport function removeTrailingSlash(route: string) {\n  return route.replace(/\\/$/, '') || '/'\n}\n", "import { isDynamicRoute } from '../router/utils'\nimport { normalizePathSep } from './normalize-path-sep'\n\n/**\n * Performs the opposite transformation of `normalizePagePath`. Note that\n * this function is not idempotent either in cases where there are multiple\n * leading `/index` for the page. Examples:\n *  - `/index` -> `/`\n *  - `/index/foo` -> `/foo`\n *  - `/index/index` -> `/index`\n */\nexport function denormalizePagePath(page: string) {\n  let _page = normalizePathSep(page)\n  return _page.startsWith('/index/') && !isDynamicRoute(_page)\n    ? _page.slice(6)\n    : _page !== '/index'\n      ? _page\n      : '/'\n}\n", "import type { detectDomainLocale as Fn } from '../shared/lib/i18n/detect-domain-locale'\n\nexport const detectDomainLocale: typeof Fn = (...args) => {\n  if (process.env.__NEXT_I18N_SUPPORT) {\n    return require('../shared/lib/i18n/detect-domain-locale').detectDomainLocale(\n      ...args\n    )\n  }\n}\n", "/* (ignored) */", "export { getSortedRoutes, getSortedRouteObjects } from './sorted-routes'\nexport { isDynamicRoute } from './is-dynamic'\n", "import {\n  isHTTPAccessFallbackError,\n  type HTTPAccessFallbackError,\n} from './http-access-fallback/http-access-fallback'\nimport { isRedirectError, type RedirectError } from './redirect-error'\n\n/**\n * Returns true if the error is a navigation signal error. These errors are\n * thrown by user code to perform navigation operations and interrupt the React\n * render.\n */\nexport function isNextRouterError(\n  error: unknown\n): error is RedirectError | HTTPAccessFallbackError {\n  return isRedirectError(error) || isHTTPAccessFallbackError(error)\n}\n", "import type { Segment } from '../../server/app-render/types'\n\nexport function isGroupSegment(segment: string) {\n  // Use array[0] for performant purpose\n  return segment[0] === '(' && segment.endsWith(')')\n}\n\nexport function isParallelRouteSegment(segment: string) {\n  return segment.startsWith('@') && segment !== '@children'\n}\n\nexport function addSearchParamsIfPageSegment(\n  segment: Segment,\n  searchParams: Record<string, string | string[] | undefined>\n) {\n  const isPageSegment = segment.includes(PAGE_SEGMENT_KEY)\n\n  if (isPageSegment) {\n    const stringifiedQuery = JSON.stringify(searchParams)\n    return stringifiedQuery !== '{}'\n      ? PAGE_SEGMENT_KEY + '?' + stringifiedQuery\n      : PAGE_SEGMENT_KEY\n  }\n\n  return segment\n}\n\nexport const PAGE_SEGMENT_KEY = '__PAGE__'\nexport const DEFAULT_SEGMENT_KEY = '__DEFAULT__'\n", "import { parsePath } from './parse-path'\n\n/**\n * Checks if a given path starts with a given prefix. It ensures it matches\n * exactly without containing extra chars. e.g. prefix /docs should replace\n * for /docs, /docs/, /docs/a but not /docsss\n * @param path The path to check.\n * @param prefix The prefix to check against.\n */\nexport function pathHasPrefix(path: string, prefix: string) {\n  if (typeof path !== 'string') {\n    return false\n  }\n\n  const { pathname } = parsePath(path)\n  return pathname === prefix || pathname.startsWith(prefix + '/')\n}\n", "import type { ParsedUrlQuery } from 'querystring'\n\nimport { getRouteMatcher } from './route-matcher'\nimport { getRouteRegex } from './route-regex'\n\nexport function interpolateAs(\n  route: string,\n  asPathname: string,\n  query: ParsedUrlQuery\n) {\n  let interpolatedRoute = ''\n\n  const dynamicRegex = getRouteRegex(route)\n  const dynamicGroups = dynamicRegex.groups\n  const dynamicMatches =\n    // Try to match the dynamic route against the asPath\n    (asPathname !== route ? getRouteMatcher(dynamicRegex)(asPathname) : '') ||\n    // Fall back to reading the values from the href\n    // TODO: should this take priority; also need to change in the router.\n    query\n\n  interpolatedRoute = route\n  const params = Object.keys(dynamicGroups)\n\n  if (\n    !params.every((param) => {\n      let value = dynamicMatches[param] || ''\n      const { repeat, optional } = dynamicGroups[param]\n\n      // support single-level catch-all\n      // TODO: more robust handling for user-error (passing `/`)\n      let replaced = `[${repeat ? '...' : ''}${param}]`\n      if (optional) {\n        replaced = `${!value ? '/' : ''}[${replaced}]`\n      }\n      if (repeat && !Array.isArray(value)) value = [value]\n\n      return (\n        (optional || param in dynamicMatches) &&\n        // Interpolate group into data URL if present\n        (interpolatedRoute =\n          interpolatedRoute!.replace(\n            replaced,\n            repeat\n              ? (value as string[])\n                  .map(\n                    // these values should be fully encoded instead of just\n                    // path delimiter escaped since they are being inserted\n                    // into the URL and we expect URL encoded segments\n                    // when parsing dynamic route params\n                    (segment) => encodeURIComponent(segment)\n                  )\n                  .join('/')\n              : encodeURIComponent(value as string)\n          ) || '/')\n      )\n    })\n  ) {\n    interpolatedRoute = '' // did not satisfy all requirements\n\n    // n.b. We ignore this error because we handle warning for this case in\n    // development in the `<Link>` component directly.\n  }\n  return {\n    params,\n    result: interpolatedRoute,\n  }\n}\n", "// This regex contains the bots that we need to do a blocking render for and can't safely stream the response\n// due to how they parse the DOM. For example, they might explicitly check for metadata in the `head` tag, so we can't stream metadata tags after the `head` was sent.\nexport const HTML_LIMITED_BOT_UA_RE =\n  /Mediapartners-Google|Slurp|DuckDuckBot|baiduspider|yandex|sogou|bitlybot|tumblr|vkShare|quora link preview|redditbot|ia_archiver|Bingbot|BingPreview|applebot|facebookexternalhit|facebookcatalog|Twitterbot|LinkedInBot|Slackbot|Discordbot|WhatsApp|SkypeUriPreview|Yeti/i\n", "export interface PathLocale {\n  detectedLocale?: string\n  pathname: string\n}\n\n/**\n * A cache of lowercased locales for each list of locales. This is stored as a\n * WeakMap so if the locales are garbage collected, the cache entry will be\n * removed as well.\n */\nconst cache = new WeakMap<readonly string[], readonly string[]>()\n\n/**\n * For a pathname that may include a locale from a list of locales, it\n * removes the locale from the pathname returning it alongside with the\n * detected locale.\n *\n * @param pathname A pathname that may include a locale.\n * @param locales A list of locales.\n * @returns The detected locale and pathname without locale\n */\nexport function normalizeLocalePath(\n  pathname: string,\n  locales?: readonly string[]\n): PathLocale {\n  // If locales is undefined, return the pathname as is.\n  if (!locales) return { pathname }\n\n  // Get the cached lowercased locales or create a new cache entry.\n  let lowercasedLocales = cache.get(locales)\n  if (!lowercasedLocales) {\n    lowercasedLocales = locales.map((locale) => locale.toLowerCase())\n    cache.set(locales, lowercasedLocales)\n  }\n\n  let detectedLocale: string | undefined\n\n  // The first segment will be empty, because it has a leading `/`. If\n  // there is no further segment, there is no locale (or it's the default).\n  const segments = pathname.split('/', 2)\n\n  // If there's no second segment (ie, the pathname is just `/`), there's no\n  // locale.\n  if (!segments[1]) return { pathname }\n\n  // The second segment will contain the locale part if any.\n  const segment = segments[1].toLowerCase()\n\n  // See if the segment matches one of the locales. If it doesn't, there is\n  // no locale (or it's the default).\n  const index = lowercasedLocales.indexOf(segment)\n  if (index < 0) return { pathname }\n\n  // Return the case-sensitive locale.\n  detectedLocale = locales[index]\n\n  // Remove the `/${locale}` part of the pathname.\n  pathname = pathname.slice(detectedLocale.length + 1) || '/'\n\n  return { pathname, detectedLocale }\n}\n", "import type { NextRouter, Url } from '../shared/lib/router/router'\n\nimport { searchParamsToUrlQuery } from '../shared/lib/router/utils/querystring'\nimport { formatWithValidation } from '../shared/lib/router/utils/format-url'\nimport { omit } from '../shared/lib/router/utils/omit'\nimport { normalizeRepeatedSlashes } from '../shared/lib/utils'\nimport { normalizePathTrailingSlash } from './normalize-trailing-slash'\nimport { isLocalURL } from '../shared/lib/router/utils/is-local-url'\nimport { isDynamicRoute } from '../shared/lib/router/utils'\nimport { interpolateAs } from '../shared/lib/router/utils/interpolate-as'\n\n/**\n * Resolves a given hyperlink with a certain router state (basePath not included).\n * Preserves absolute urls.\n */\nexport function resolveHref(\n  router: NextRouter,\n  href: Url,\n  resolveAs: true\n): [string, string] | [string]\nexport function resolveHref(\n  router: NextRouter,\n  href: Url,\n  resolveAs?: false\n): string\nexport function resolveHref(\n  router: NextRouter,\n  href: Url,\n  resolveAs?: boolean\n): [string, string] | [string] | string {\n  // we use a dummy base url for relative urls\n  let base: URL\n  let urlAsString = typeof href === 'string' ? href : formatWithValidation(href)\n\n  // repeated slashes and backslashes in the URL are considered\n  // invalid and will never match a Next.js page/file\n  const urlProtoMatch = urlAsString.match(/^[a-zA-Z]{1,}:\\/\\//)\n  const urlAsStringNoProto = urlProtoMatch\n    ? urlAsString.slice(urlProtoMatch[0].length)\n    : urlAsString\n\n  const urlParts = urlAsStringNoProto.split('?', 1)\n\n  if ((urlParts[0] || '').match(/(\\/\\/|\\\\)/)) {\n    console.error(\n      `Invalid href '${urlAsString}' passed to next/router in page: '${router.pathname}'. Repeated forward-slashes (//) or backslashes \\\\ are not valid in the href.`\n    )\n    const normalizedUrl = normalizeRepeatedSlashes(urlAsStringNoProto)\n    urlAsString = (urlProtoMatch ? urlProtoMatch[0] : '') + normalizedUrl\n  }\n\n  // Return because it cannot be routed by the Next.js router\n  if (!isLocalURL(urlAsString)) {\n    return (resolveAs ? [urlAsString] : urlAsString) as string\n  }\n\n  try {\n    base = new URL(\n      urlAsString.startsWith('#') ? router.asPath : router.pathname,\n      'http://n'\n    )\n  } catch (_) {\n    // fallback to / for invalid asPath values e.g. //\n    base = new URL('/', 'http://n')\n  }\n\n  try {\n    const finalUrl = new URL(urlAsString, base)\n    finalUrl.pathname = normalizePathTrailingSlash(finalUrl.pathname)\n    let interpolatedAs = ''\n\n    if (\n      isDynamicRoute(finalUrl.pathname) &&\n      finalUrl.searchParams &&\n      resolveAs\n    ) {\n      const query = searchParamsToUrlQuery(finalUrl.searchParams)\n\n      const { result, params } = interpolateAs(\n        finalUrl.pathname,\n        finalUrl.pathname,\n        query\n      )\n\n      if (result) {\n        interpolatedAs = formatWithValidation({\n          pathname: result,\n          hash: finalUrl.hash,\n          query: omit(query, params),\n        })\n      }\n    }\n\n    // if the origin didn't change, it means we received a relative href\n    const resolvedHref =\n      finalUrl.origin === base.origin\n        ? finalUrl.href.slice(finalUrl.origin.length)\n        : finalUrl.href\n\n    return resolveAs\n      ? [resolvedHref, interpolatedAs || resolvedHref]\n      : resolvedHref\n  } catch (_) {\n    return resolveAs ? [urlAsString] : urlAsString\n  }\n}\n", "/* global window */\nimport React from 'react'\nimport Router from '../shared/lib/router/router'\nimport type { NextRouter } from '../shared/lib/router/router'\nimport { RouterContext } from '../shared/lib/router-context.shared-runtime'\nimport isError from '../lib/is-error'\n\ntype SingletonRouterBase = {\n  router: Router | null\n  readyCallbacks: Array<() => any>\n  ready(cb: () => any): void\n}\n\nexport { Router }\n\nexport type { NextRouter }\n\nexport type SingletonRouter = SingletonRouterBase & NextRouter\n\nconst singletonRouter: SingletonRouterBase = {\n  router: null, // holds the actual router instance\n  readyCallbacks: [],\n  ready(callback: () => void) {\n    if (this.router) return callback()\n    if (typeof window !== 'undefined') {\n      this.readyCallbacks.push(callback)\n    }\n  },\n}\n\n// Create public properties and methods of the router in the singletonRouter\nconst urlPropertyFields = [\n  'pathname',\n  'route',\n  'query',\n  'asPath',\n  'components',\n  'isFallback',\n  'basePath',\n  'locale',\n  'locales',\n  'defaultLocale',\n  'isReady',\n  'isPreview',\n  'isLocaleDomain',\n  'domainLocales',\n] as const\nconst routerEvents = [\n  'routeChangeStart',\n  'beforeHistoryChange',\n  'routeChangeComplete',\n  'routeChangeError',\n  'hashChangeStart',\n  'hashChangeComplete',\n] as const\nexport type RouterEvent = (typeof routerEvents)[number]\n\nconst coreMethodFields = [\n  'push',\n  'replace',\n  'reload',\n  'back',\n  'prefetch',\n  'beforePopState',\n] as const\n\n// Events is a static property on the router, the router doesn't have to be initialized to use it\nObject.defineProperty(singletonRouter, 'events', {\n  get() {\n    return Router.events\n  },\n})\n\nfunction getRouter(): Router {\n  if (!singletonRouter.router) {\n    const message =\n      'No router instance found.\\n' +\n      'You should only use \"next/router\" on the client side of your app.\\n'\n    throw new Error(message)\n  }\n  return singletonRouter.router\n}\n\nurlPropertyFields.forEach((field) => {\n  // Here we need to use Object.defineProperty because we need to return\n  // the property assigned to the actual router\n  // The value might get changed as we change routes and this is the\n  // proper way to access it\n  Object.defineProperty(singletonRouter, field, {\n    get() {\n      const router = getRouter()\n      return router[field] as string\n    },\n  })\n})\n\ncoreMethodFields.forEach((field) => {\n  // We don't really know the types here, so we add them later instead\n  ;(singletonRouter as any)[field] = (...args: any[]) => {\n    const router = getRouter() as any\n    return router[field](...args)\n  }\n})\n\nrouterEvents.forEach((event) => {\n  singletonRouter.ready(() => {\n    Router.events.on(event, (...args) => {\n      const eventField = `on${event.charAt(0).toUpperCase()}${event.substring(\n        1\n      )}`\n      const _singletonRouter = singletonRouter as any\n      if (_singletonRouter[eventField]) {\n        try {\n          _singletonRouter[eventField](...args)\n        } catch (err) {\n          console.error(`Error when running the Router event: ${eventField}`)\n          console.error(\n            isError(err) ? `${err.message}\\n${err.stack}` : err + ''\n          )\n        }\n      }\n    })\n  })\n})\n\n// Export the singletonRouter and this is the public API.\nexport default singletonRouter as SingletonRouter\n\n// Reexport the withRouter HOC\nexport { default as withRouter } from './with-router'\n\n/**\n * This hook gives access the [router object](https://nextjs.org/docs/pages/api-reference/functions/use-router#router-object)\n * inside the [Pages Router](https://nextjs.org/docs/pages/building-your-application).\n *\n * Read more: [Next.js Docs: `useRouter`](https://nextjs.org/docs/pages/api-reference/functions/use-router)\n */\nexport function useRouter(): NextRouter {\n  const router = React.useContext(RouterContext)\n  if (!router) {\n    throw new Error(\n      'NextRouter was not mounted. https://nextjs.org/docs/messages/next-router-not-mounted'\n    )\n  }\n\n  return router\n}\n\n/**\n * Create a router and assign it as the singleton instance.\n * This is used in client side when we are initializing the app.\n * This should **not** be used inside the server.\n * @internal\n */\nexport function createRouter(\n  ...args: ConstructorParameters<typeof Router>\n): Router {\n  singletonRouter.router = new Router(...args)\n  singletonRouter.readyCallbacks.forEach((cb) => cb())\n  singletonRouter.readyCallbacks = []\n\n  return singletonRouter.router\n}\n\n/**\n * This function is used to create the `withRouter` router instance\n * @internal\n */\nexport function makePublicRouterInstance(router: Router): NextRouter {\n  const scopedRouter = router as any\n  const instance = {} as any\n\n  for (const property of urlPropertyFields) {\n    if (typeof scopedRouter[property] === 'object') {\n      instance[property] = Object.assign(\n        Array.isArray(scopedRouter[property]) ? [] : {},\n        scopedRouter[property]\n      ) // makes sure query is not stateful\n      continue\n    }\n\n    instance[property] = scopedRouter[property]\n  }\n\n  // Events is a static property on the router, the router doesn't have to be initialized to use it\n  instance.events = Router.events\n\n  coreMethodFields.forEach((field) => {\n    instance[field] = (...args: any[]) => {\n      return scopedRouter[field](...args)\n    }\n  })\n\n  return instance\n}\n", "export enum RedirectStatusCode {\n  SeeOther = 303,\n  TemporaryRedirect = 307,\n  PermanentRedirect = 308,\n}\n", "import type { ComponentType } from 'react'\nimport type { RouteLoader } from './route-loader'\nimport type { MiddlewareMatcher } from '../build/analysis/get-page-static-info'\nimport { addBasePath } from './add-base-path'\nimport { interpolateAs } from '../shared/lib/router/utils/interpolate-as'\nimport getAssetPathFromRoute from '../shared/lib/router/utils/get-asset-path-from-route'\nimport { addLocale } from './add-locale'\nimport { isDynamicRoute } from '../shared/lib/router/utils/is-dynamic'\nimport { parseRelativeUrl } from '../shared/lib/router/utils/parse-relative-url'\nimport { removeTrailingSlash } from '../shared/lib/router/utils/remove-trailing-slash'\nimport { createRouteLoader, getClientBuildManifest } from './route-loader'\nimport {\n  DEV_CLIENT_PAGES_MANIFEST,\n  DEV_CLIENT_MIDDLEWARE_MANIFEST,\n  TURBOPACK_CLIENT_MIDDLEWARE_MANIFEST,\n} from '../shared/lib/constants'\n\ndeclare global {\n  interface Window {\n    __DEV_MIDDLEWARE_MATCHERS?: MiddlewareMatcher[]\n    __DEV_PAGES_MANIFEST?: { pages: string[] }\n    __SSG_MANIFEST_CB?: () => void\n    __SSG_MANIFEST?: Set<string>\n  }\n}\n\nexport type StyleSheetTuple = { href: string; text: string }\nexport type GoodPageCache = {\n  page: ComponentType\n  mod: any\n  styleSheets: StyleSheetTuple[]\n}\n\nexport default class PageLoader {\n  private buildId: string\n  private assetPrefix: string\n  private promisedSsgManifest: Promise<Set<string>>\n  private promisedDevPagesManifest?: Promise<string[]>\n  private promisedMiddlewareMatchers?: Promise<MiddlewareMatcher[]>\n\n  public routeLoader: RouteLoader\n\n  constructor(buildId: string, assetPrefix: string) {\n    this.routeLoader = createRouteLoader(assetPrefix)\n\n    this.buildId = buildId\n    this.assetPrefix = assetPrefix\n\n    this.promisedSsgManifest = new Promise((resolve) => {\n      if (window.__SSG_MANIFEST) {\n        resolve(window.__SSG_MANIFEST)\n      } else {\n        window.__SSG_MANIFEST_CB = () => {\n          resolve(window.__SSG_MANIFEST!)\n        }\n      }\n    })\n  }\n\n  getPageList() {\n    if (process.env.NODE_ENV === 'production') {\n      return getClientBuildManifest().then((manifest) => manifest.sortedPages)\n    } else {\n      if (window.__DEV_PAGES_MANIFEST) {\n        return window.__DEV_PAGES_MANIFEST.pages\n      } else {\n        this.promisedDevPagesManifest ||= fetch(\n          `${this.assetPrefix}/_next/static/development/${DEV_CLIENT_PAGES_MANIFEST}`,\n          { credentials: 'same-origin' }\n        )\n          .then((res) => res.json())\n          .then((manifest: { pages: string[] }) => {\n            window.__DEV_PAGES_MANIFEST = manifest\n            return manifest.pages\n          })\n          .catch((err) => {\n            console.log(`Failed to fetch devPagesManifest:`, err)\n            throw new Error(\n              `Failed to fetch _devPagesManifest.json. Is something blocking that network request?\\n` +\n                'Read more: https://nextjs.org/docs/messages/failed-to-fetch-devpagesmanifest'\n            )\n          })\n        return this.promisedDevPagesManifest\n      }\n    }\n  }\n\n  getMiddleware() {\n    // Webpack production\n    if (\n      process.env.NODE_ENV === 'production' &&\n      process.env.__NEXT_MIDDLEWARE_MATCHERS\n    ) {\n      const middlewareMatchers = process.env.__NEXT_MIDDLEWARE_MATCHERS\n      window.__MIDDLEWARE_MATCHERS = middlewareMatchers\n        ? (middlewareMatchers as any as MiddlewareMatcher[])\n        : undefined\n      return window.__MIDDLEWARE_MATCHERS\n      // Turbopack production\n    } else if (process.env.NODE_ENV === 'production') {\n      if (window.__MIDDLEWARE_MATCHERS) {\n        return window.__MIDDLEWARE_MATCHERS\n      } else {\n        if (!this.promisedMiddlewareMatchers) {\n          // TODO: Decide what should happen when fetching fails instead of asserting\n          // @ts-ignore\n          this.promisedMiddlewareMatchers = fetch(\n            `${this.assetPrefix}/_next/static/${this.buildId}/${TURBOPACK_CLIENT_MIDDLEWARE_MANIFEST}`,\n            { credentials: 'same-origin' }\n          )\n            .then((res) => res.json())\n            .then((matchers: MiddlewareMatcher[]) => {\n              window.__MIDDLEWARE_MATCHERS = matchers\n              return matchers\n            })\n            .catch((err) => {\n              console.log(`Failed to fetch _devMiddlewareManifest`, err)\n            })\n        }\n        // TODO Remove this assertion as this could be undefined\n        return this.promisedMiddlewareMatchers!\n      }\n      // Development both Turbopack and Webpack\n    } else {\n      if (window.__DEV_MIDDLEWARE_MATCHERS) {\n        return window.__DEV_MIDDLEWARE_MATCHERS\n      } else {\n        if (!this.promisedMiddlewareMatchers) {\n          // TODO: Decide what should happen when fetching fails instead of asserting\n          // @ts-ignore\n          this.promisedMiddlewareMatchers = fetch(\n            `${this.assetPrefix}/_next/static/${this.buildId}/${DEV_CLIENT_MIDDLEWARE_MANIFEST}`,\n            { credentials: 'same-origin' }\n          )\n            .then((res) => res.json())\n            .then((matchers: MiddlewareMatcher[]) => {\n              window.__DEV_MIDDLEWARE_MATCHERS = matchers\n              return matchers\n            })\n            .catch((err) => {\n              console.log(`Failed to fetch _devMiddlewareManifest`, err)\n            })\n        }\n        // TODO Remove this assertion as this could be undefined\n        return this.promisedMiddlewareMatchers!\n      }\n    }\n  }\n\n  getDataHref(params: {\n    asPath: string\n    href: string\n    locale?: string | false\n    skipInterpolation?: boolean\n  }): string {\n    const { asPath, href, locale } = params\n    const { pathname: hrefPathname, query, search } = parseRelativeUrl(href)\n    const { pathname: asPathname } = parseRelativeUrl(asPath)\n    const route = removeTrailingSlash(hrefPathname)\n    if (route[0] !== '/') {\n      throw new Error(`Route name should start with a \"/\", got \"${route}\"`)\n    }\n\n    const getHrefForSlug = (path: string) => {\n      const dataRoute = getAssetPathFromRoute(\n        removeTrailingSlash(addLocale(path, locale)),\n        '.json'\n      )\n      return addBasePath(\n        `/_next/data/${this.buildId}${dataRoute}${search}`,\n        true\n      )\n    }\n\n    return getHrefForSlug(\n      params.skipInterpolation\n        ? asPathname\n        : isDynamicRoute(route)\n          ? interpolateAs(hrefPathname, asPathname, query).result\n          : route\n    )\n  }\n\n  _isSsg(\n    /** the route (file-system path) */\n    route: string\n  ): Promise<boolean> {\n    return this.promisedSsgManifest.then((manifest) => manifest.has(route))\n  }\n\n  loadPage(route: string): Promise<GoodPageCache> {\n    return this.routeLoader.loadRoute(route).then((res) => {\n      if ('component' in res) {\n        return {\n          page: res.component,\n          mod: res.exports,\n          styleSheets: res.styles.map((o) => ({\n            href: o.href,\n            text: o.content,\n          })),\n        }\n      }\n      throw res.error\n    })\n  }\n\n  prefetch(route: string): Promise<void> {\n    return this.routeLoader.prefetch(route)\n  }\n}\n", "import mitt from '../../shared/lib/mitt'\nimport type { MittEmitter } from '../../shared/lib/mitt'\n\nexport type SpanOptions = {\n  startTime?: number\n  attributes?: Record<string, unknown>\n}\n\nexport type SpanState =\n  | {\n      state: 'inprogress'\n    }\n  | {\n      state: 'ended'\n      endTime: number\n    }\n\ninterface ISpan {\n  name: string\n  startTime: number\n  attributes: Record<string, unknown>\n  state: SpanState\n  end(endTime?: number): void\n}\n\nclass Span implements ISpan {\n  name: string\n  startTime: number\n  onSpanEnd: (span: Span) => void\n  state: SpanState\n  attributes: Record<string, unknown>\n\n  constructor(\n    name: string,\n    options: SpanOptions,\n    onSpanEnd: (span: Span) => void\n  ) {\n    this.name = name\n    this.attributes = options.attributes ?? {}\n    this.startTime = options.startTime ?? Date.now()\n    this.onSpanEnd = onSpanEnd\n    this.state = { state: 'inprogress' }\n  }\n\n  end(endTime?: number) {\n    if (this.state.state === 'ended') {\n      throw new Error('Span has already ended')\n    }\n\n    this.state = {\n      state: 'ended',\n      endTime: endTime ?? Date.now(),\n    }\n\n    this.onSpanEnd(this)\n  }\n}\n\nclass Tracer {\n  _emitter: MittEmitter<string> = mitt()\n\n  private handleSpanEnd = (span: Span) => {\n    this._emitter.emit('spanend', span)\n  }\n\n  startSpan(name: string, options: SpanOptions) {\n    return new Span(name, options, this.handleSpanEnd)\n  }\n\n  onSpanEnd(cb: (span: ISpan) => void): () => void {\n    this._emitter.on('spanend', cb)\n    return () => {\n      this._emitter.off('spanend', cb)\n    }\n  }\n}\n\nexport type { ISpan as Span }\nexport default new Tracer()\n", "// This has to be a shared module which is shared between client component error boundary and dynamic component\nconst BAILOUT_TO_CSR = 'BAILOUT_TO_CLIENT_SIDE_RENDERING'\n\n/** An error that should be thrown when we want to bail out to client-side rendering. */\nexport class BailoutToCSRError extends Error {\n  public readonly digest = BAILOUT_TO_CSR\n\n  constructor(public readonly reason: string) {\n    super(`Bail out to client-side rendering: ${reason}`)\n  }\n}\n\n/** Checks if a passed argument is an error that is thrown if we want to bail out to client-side rendering. */\nexport function isBailoutToCSRError(err: unknown): err is BailoutToCSRError {\n  if (typeof err !== 'object' || err === null || !('digest' in err)) {\n    return false\n  }\n\n  return err.digest === BAILOUT_TO_CSR\n}\n", "export function getObjectClassLabel(value: any): string {\n  return Object.prototype.toString.call(value)\n}\n\nexport function isPlainObject(value: any): boolean {\n  if (getObjectClassLabel(value) !== '[object Object]') {\n    return false\n  }\n\n  const prototype = Object.getPrototypeOf(value)\n\n  /**\n   * this used to be previously:\n   *\n   * `return prototype === null || prototype === Object.prototype`\n   *\n   * but Edge Runtime expose Object from vm, being that kind of type-checking wrongly fail.\n   *\n   * It was changed to the current implementation since it's resilient to serialization.\n   */\n  return prototype === null || prototype.hasOwnProperty('isPrototypeOf')\n}\n", "import { addPathPrefix } from './add-path-prefix'\nimport { pathHasPrefix } from './path-has-prefix'\n\n/**\n * For a given path and a locale, if the locale is given, it will prefix the\n * locale. The path shouldn't be an API path. If a default locale is given the\n * prefix will be omitted if the locale is already the default locale.\n */\nexport function addLocale(\n  path: string,\n  locale?: string | false,\n  defaultLocale?: string,\n  ignorePrefix?: boolean\n) {\n  // If no locale was given or the locale is the default locale, we don't need\n  // to prefix the path.\n  if (!locale || locale === defaultLocale) return path\n\n  const lower = path.toLowerCase()\n\n  // If the path is an API path or the path already has the locale prefix, we\n  // don't need to prefix the path.\n  if (!ignorePrefix) {\n    if (pathHasPrefix(lower, '/api')) return path\n    if (pathHasPrefix(lower, `/${locale.toLowerCase()}`)) return path\n  }\n\n  // Add the locale prefix to the path.\n  return addPathPrefix(path, `/${locale}`)\n}\n", "import React, { type JSX } from 'react'\nimport type {\n  BaseContext,\n  NextComponentType,\n  NextPageContext,\n} from '../shared/lib/utils'\nimport type { NextRouter } from './router'\nimport { useRouter } from './router'\n\nexport type WithRouterProps = {\n  router: NextRouter\n}\n\nexport type ExcludeRouterProps<P> = Pick<\n  P,\n  Exclude<keyof P, keyof WithRouterProps>\n>\n\nexport default function withRouter<\n  P extends WithRouterProps,\n  C extends BaseContext = NextPageContext,\n>(\n  ComposedComponent: NextComponentType<C, any, P>\n): React.ComponentType<ExcludeRouterProps<P>> {\n  function WithRouterWrapper(props: any): JSX.Element {\n    return <ComposedComponent router={useRouter()} {...props} />\n  }\n\n  WithRouterWrapper.getInitialProps = ComposedComponent.getInitialProps\n  // This is needed to allow checking for custom getInitialProps in _app\n  ;(WithRouterWrapper as any).origGetInitialProps = (\n    ComposedComponent as any\n  ).origGetInitialProps\n  if (process.env.NODE_ENV !== 'production') {\n    const name =\n      ComposedComponent.displayName || ComposedComponent.name || 'Unknown'\n    WithRouterWrapper.displayName = `withRouter(${name})`\n  }\n\n  return WithRouterWrapper\n}\n", "import { setAttributesFromProps } from './set-attributes-from-props'\n\nimport type { JSX } from 'react'\n\nfunction reactElementToDOM({ type, props }: JSX.Element): HTMLElement {\n  const el: HTMLElement = document.createElement(type)\n  setAttributesFromProps(el, props)\n\n  const { children, dangerouslySetInnerHTML } = props\n  if (dangerouslySetInnerHTML) {\n    el.innerHTML = dangerouslySetInnerHTML.__html || ''\n  } else if (children) {\n    el.textContent =\n      typeof children === 'string'\n        ? children\n        : Array.isArray(children)\n          ? children.join('')\n          : ''\n  }\n  return el\n}\n\n/**\n * When a `nonce` is present on an element, browsers such as Chrome and Firefox strip it out of the\n * actual HTML attributes for security reasons *when the element is added to the document*. Thus,\n * given two equivalent elements that have nonces, `Element,isEqualNode()` will return false if one\n * of those elements gets added to the document. Although the `element.nonce` property will be the\n * same for both elements, the one that was added to the document will return an empty string for\n * its nonce HTML attribute value.\n *\n * This custom `isEqualNode()` function therefore removes the nonce value from the `newTag` before\n * comparing it to `oldTag`, restoring it afterwards.\n *\n * For more information, see:\n * https://bugs.chromium.org/p/chromium/issues/detail?id=1211471#c12\n */\nexport function isEqualNode(oldTag: Element, newTag: Element) {\n  if (oldTag instanceof HTMLElement && newTag instanceof HTMLElement) {\n    const nonce = newTag.getAttribute('nonce')\n    // Only strip the nonce if `oldTag` has had it stripped. An element's nonce attribute will not\n    // be stripped if there is no content security policy response header that includes a nonce.\n    if (nonce && !oldTag.getAttribute('nonce')) {\n      const cloneTag = newTag.cloneNode(true) as typeof newTag\n      cloneTag.setAttribute('nonce', '')\n      cloneTag.nonce = nonce\n      return nonce === oldTag.nonce && oldTag.isEqualNode(cloneTag)\n    }\n  }\n\n  return oldTag.isEqualNode(newTag)\n}\n\nlet updateElements: (type: string, components: JSX.Element[]) => void\n\nif (process.env.__NEXT_STRICT_NEXT_HEAD) {\n  updateElements = (type, components) => {\n    const headEl = document.querySelector('head')\n    if (!headEl) return\n\n    const oldTags = new Set(headEl.querySelectorAll(`${type}[data-next-head]`))\n\n    if (type === 'meta') {\n      const metaCharset = headEl.querySelector('meta[charset]')\n      if (metaCharset !== null) {\n        oldTags.add(metaCharset)\n      }\n    }\n\n    const newTags: Element[] = []\n    for (let i = 0; i < components.length; i++) {\n      const component = components[i]\n      const newTag = reactElementToDOM(component)\n      newTag.setAttribute('data-next-head', '')\n\n      let isNew = true\n      for (const oldTag of oldTags) {\n        if (isEqualNode(oldTag, newTag)) {\n          oldTags.delete(oldTag)\n          isNew = false\n          break\n        }\n      }\n\n      if (isNew) {\n        newTags.push(newTag)\n      }\n    }\n\n    for (const oldTag of oldTags) {\n      oldTag.parentNode?.removeChild(oldTag)\n    }\n\n    for (const newTag of newTags) {\n      // meta[charset] must be first element so special case\n      if (\n        newTag.tagName.toLowerCase() === 'meta' &&\n        newTag.getAttribute('charset') !== null\n      ) {\n        headEl.prepend(newTag)\n      }\n      headEl.appendChild(newTag)\n    }\n  }\n} else {\n  updateElements = (type, components) => {\n    const headEl = document.getElementsByTagName('head')[0]\n    const headCountEl: HTMLMetaElement = headEl.querySelector(\n      'meta[name=next-head-count]'\n    ) as HTMLMetaElement\n    if (process.env.NODE_ENV !== 'production') {\n      if (!headCountEl) {\n        console.error(\n          'Warning: next-head-count is missing. https://nextjs.org/docs/messages/next-head-count-missing'\n        )\n        return\n      }\n    }\n\n    const headCount = Number(headCountEl.content)\n    const oldTags: Element[] = []\n\n    for (\n      let i = 0, j = headCountEl.previousElementSibling;\n      i < headCount;\n      i++, j = j?.previousElementSibling || null\n    ) {\n      if (j?.tagName?.toLowerCase() === type) {\n        oldTags.push(j)\n      }\n    }\n    const newTags = (components.map(reactElementToDOM) as HTMLElement[]).filter(\n      (newTag) => {\n        for (let k = 0, len = oldTags.length; k < len; k++) {\n          const oldTag = oldTags[k]\n          if (isEqualNode(oldTag, newTag)) {\n            oldTags.splice(k, 1)\n            return false\n          }\n        }\n        return true\n      }\n    )\n\n    oldTags.forEach((t) => t.parentNode?.removeChild(t))\n    newTags.forEach((t) => headEl.insertBefore(t, headCountEl))\n    headCountEl.content = (\n      headCount -\n      oldTags.length +\n      newTags.length\n    ).toString()\n  }\n}\n\nexport default function initHeadManager(): {\n  mountedInstances: Set<unknown>\n  updateHead: (head: JSX.Element[]) => void\n} {\n  return {\n    mountedInstances: new Set(),\n    updateHead: (head: JSX.Element[]) => {\n      const tags: Record<string, JSX.Element[]> = {}\n\n      head.forEach((h) => {\n        if (\n          // If the font tag is loaded only on client navigation\n          // it won't be inlined. In this case revert to the original behavior\n          h.type === 'link' &&\n          h.props['data-optimized-fonts']\n        ) {\n          if (\n            document.querySelector(`style[data-href=\"${h.props['data-href']}\"]`)\n          ) {\n            return\n          } else {\n            h.props.href = h.props['data-href']\n            h.props['data-href'] = undefined\n          }\n        }\n\n        const components = tags[h.type] || []\n        components.push(h)\n        tags[h.type] = components\n      })\n\n      const titleComponent = tags.title ? tags.title[0] : null\n      let title = ''\n      if (titleComponent) {\n        const { children } = titleComponent.props\n        title =\n          typeof children === 'string'\n            ? children\n            : Array.isArray(children)\n              ? children.join('')\n              : ''\n      }\n      if (title !== document.title) document.title = title\n      ;['meta', 'base', 'link', 'style', 'script'].forEach((type) => {\n        updateElements(type, tags[type] || [])\n      })\n    },\n  }\n}\n", "import type { ComponentType } from 'react'\nimport type { MiddlewareMatcher } from '../build/analysis/get-page-static-info'\nimport getAssetPathFromRoute from '../shared/lib/router/utils/get-asset-path-from-route'\nimport { __unsafeCreateTrustedScriptURL } from './trusted-types'\nimport { requestIdleCallback } from './request-idle-callback'\nimport { getDeploymentIdQueryOrEmptyString } from '../build/deployment-id'\nimport { encodeURIPath } from '../shared/lib/encode-uri-path'\n\n// 3.8s was arbitrarily chosen as it's what https://web.dev/interactive\n// considers as \"Good\" time-to-interactive. We must assume something went\n// wrong beyond this point, and then fall-back to a full page transition to\n// show the user something of value.\nconst MS_MAX_IDLE_DELAY = 3800\n\ndeclare global {\n  interface Window {\n    __BUILD_MANIFEST?: Record<string, string[]>\n    __BUILD_MANIFEST_CB?: Function\n    __MIDDLEWARE_MATCHERS?: MiddlewareMatcher[]\n    __MIDDLEWARE_MANIFEST_CB?: Function\n    __REACT_LOADABLE_MANIFEST?: any\n    __DYNAMIC_CSS_MANIFEST?: any\n    __RSC_MANIFEST?: any\n    __RSC_SERVER_MANIFEST?: any\n    __NEXT_FONT_MANIFEST?: any\n    __SUBRESOURCE_INTEGRITY_MANIFEST?: string\n    __INTERCEPTION_ROUTE_REWRITE_MANIFEST?: string\n  }\n}\n\ninterface LoadedEntrypointSuccess {\n  component: ComponentType\n  exports: any\n}\ninterface LoadedEntrypointFailure {\n  error: unknown\n}\ntype RouteEntrypoint = LoadedEntrypointSuccess | LoadedEntrypointFailure\n\ninterface RouteStyleSheet {\n  href: string\n  content: string\n}\n\ninterface LoadedRouteSuccess extends LoadedEntrypointSuccess {\n  styles: RouteStyleSheet[]\n}\ninterface LoadedRouteFailure {\n  error: unknown\n}\ntype RouteLoaderEntry = LoadedRouteSuccess | LoadedRouteFailure\n\ninterface Future<V> {\n  resolve: (entrypoint: V) => void\n  future: Promise<V>\n}\nfunction withFuture<T extends object>(\n  key: string,\n  map: Map<string, Future<T> | T>,\n  generator?: () => Promise<T>\n): Promise<T> {\n  let entry = map.get(key)\n  if (entry) {\n    if ('future' in entry) {\n      return entry.future\n    }\n    return Promise.resolve(entry)\n  }\n  let resolver: (entrypoint: T) => void\n  const prom: Promise<T> = new Promise<T>((resolve) => {\n    resolver = resolve\n  })\n  map.set(key, { resolve: resolver!, future: prom })\n  return generator\n    ? generator()\n        .then((value) => {\n          resolver(value)\n          return value\n        })\n        .catch((err) => {\n          map.delete(key)\n          throw err\n        })\n    : prom\n}\n\nexport interface RouteLoader {\n  whenEntrypoint(route: string): Promise<RouteEntrypoint>\n  onEntrypoint(route: string, execute: () => unknown): void\n  loadRoute(route: string, prefetch?: boolean): Promise<RouteLoaderEntry>\n  prefetch(route: string): Promise<void>\n}\n\nconst ASSET_LOAD_ERROR = Symbol('ASSET_LOAD_ERROR')\n// TODO: unexport\nexport function markAssetError(err: Error): Error {\n  return Object.defineProperty(err, ASSET_LOAD_ERROR, {})\n}\n\nexport function isAssetError(err?: Error): boolean | undefined {\n  return err && ASSET_LOAD_ERROR in err\n}\n\nfunction hasPrefetch(link?: HTMLLinkElement): boolean {\n  try {\n    link = document.createElement('link')\n    return (\n      // detect IE11 since it supports prefetch but isn't detected\n      // with relList.support\n      (!!window.MSInputMethodContext && !!(document as any).documentMode) ||\n      link.relList.supports('prefetch')\n    )\n  } catch {\n    return false\n  }\n}\n\nconst canPrefetch: boolean = hasPrefetch()\n\nconst getAssetQueryString = () => {\n  return getDeploymentIdQueryOrEmptyString()\n}\n\nfunction prefetchViaDom(\n  href: string,\n  as: string,\n  link?: HTMLLinkElement\n): Promise<any> {\n  return new Promise<void>((resolve, reject) => {\n    const selector = `\n      link[rel=\"prefetch\"][href^=\"${href}\"],\n      link[rel=\"preload\"][href^=\"${href}\"],\n      script[src^=\"${href}\"]`\n    if (document.querySelector(selector)) {\n      return resolve()\n    }\n\n    link = document.createElement('link')\n\n    // The order of property assignment here is intentional:\n    if (as) link!.as = as\n    link!.rel = `prefetch`\n    link!.crossOrigin = process.env.__NEXT_CROSS_ORIGIN!\n    link!.onload = resolve as any\n    link!.onerror = () =>\n      reject(markAssetError(new Error(`Failed to prefetch: ${href}`)))\n\n    // `href` should always be last:\n    link!.href = href\n\n    document.head.appendChild(link)\n  })\n}\n\nfunction appendScript(\n  src: TrustedScriptURL | string,\n  script?: HTMLScriptElement\n): Promise<unknown> {\n  return new Promise((resolve, reject) => {\n    script = document.createElement('script')\n\n    // The order of property assignment here is intentional.\n    // 1. Setup success/failure hooks in case the browser synchronously\n    //    executes when `src` is set.\n    script.onload = resolve\n    script.onerror = () =>\n      reject(markAssetError(new Error(`Failed to load script: ${src}`)))\n\n    // 2. Configure the cross-origin attribute before setting `src` in case the\n    //    browser begins to fetch.\n    script.crossOrigin = process.env.__NEXT_CROSS_ORIGIN!\n\n    // 3. Finally, set the source and inject into the DOM in case the child\n    //    must be appended for fetching to start.\n    script.src = src as string\n    document.body.appendChild(script)\n  })\n}\n\n// We wait for pages to be built in dev before we start the route transition\n// timeout to prevent an un-necessary hard navigation in development.\nlet devBuildPromise: Promise<void> | undefined\n\n// Resolve a promise that times out after given amount of milliseconds.\nfunction resolvePromiseWithTimeout<T>(\n  p: Promise<T>,\n  ms: number,\n  err: Error\n): Promise<T> {\n  return new Promise((resolve, reject) => {\n    let cancelled = false\n\n    p.then((r) => {\n      // Resolved, cancel the timeout\n      cancelled = true\n      resolve(r)\n    }).catch(reject)\n\n    // We wrap these checks separately for better dead-code elimination in\n    // production bundles.\n    if (process.env.NODE_ENV === 'development') {\n      ;(devBuildPromise || Promise.resolve()).then(() => {\n        requestIdleCallback(() =>\n          setTimeout(() => {\n            if (!cancelled) {\n              reject(err)\n            }\n          }, ms)\n        )\n      })\n    }\n\n    if (process.env.NODE_ENV !== 'development') {\n      requestIdleCallback(() =>\n        setTimeout(() => {\n          if (!cancelled) {\n            reject(err)\n          }\n        }, ms)\n      )\n    }\n  })\n}\n\n// TODO: stop exporting or cache the failure\n// It'd be best to stop exporting this. It's an implementation detail. We're\n// only exporting it for backwards compatibility with the `page-loader`.\n// Only cache this response as a last resort if we cannot eliminate all other\n// code branches that use the Build Manifest Callback and push them through\n// the Route Loader interface.\nexport function getClientBuildManifest() {\n  if (self.__BUILD_MANIFEST) {\n    return Promise.resolve(self.__BUILD_MANIFEST)\n  }\n\n  const onBuildManifest = new Promise<Record<string, string[]>>((resolve) => {\n    // Mandatory because this is not concurrent safe:\n    const cb = self.__BUILD_MANIFEST_CB\n    self.__BUILD_MANIFEST_CB = () => {\n      resolve(self.__BUILD_MANIFEST!)\n      cb && cb()\n    }\n  })\n\n  return resolvePromiseWithTimeout(\n    onBuildManifest,\n    MS_MAX_IDLE_DELAY,\n    markAssetError(new Error('Failed to load client build manifest'))\n  )\n}\n\ninterface RouteFiles {\n  scripts: (TrustedScriptURL | string)[]\n  css: string[]\n}\nfunction getFilesForRoute(\n  assetPrefix: string,\n  route: string\n): Promise<RouteFiles> {\n  if (process.env.NODE_ENV === 'development') {\n    const scriptUrl =\n      assetPrefix +\n      '/_next/static/chunks/pages' +\n      encodeURIPath(getAssetPathFromRoute(route, '.js')) +\n      getAssetQueryString()\n    return Promise.resolve({\n      scripts: [__unsafeCreateTrustedScriptURL(scriptUrl)],\n      // Styles are handled by `style-loader` in development:\n      css: [],\n    })\n  }\n  return getClientBuildManifest().then((manifest) => {\n    if (!(route in manifest)) {\n      throw markAssetError(new Error(`Failed to lookup route: ${route}`))\n    }\n    const allFiles = manifest[route].map(\n      (entry) => assetPrefix + '/_next/' + encodeURIPath(entry)\n    )\n    return {\n      scripts: allFiles\n        .filter((v) => v.endsWith('.js'))\n        .map((v) => __unsafeCreateTrustedScriptURL(v) + getAssetQueryString()),\n      css: allFiles\n        .filter((v) => v.endsWith('.css'))\n        .map((v) => v + getAssetQueryString()),\n    }\n  })\n}\n\nexport function createRouteLoader(assetPrefix: string): RouteLoader {\n  const entrypoints: Map<string, Future<RouteEntrypoint> | RouteEntrypoint> =\n    new Map()\n  const loadedScripts: Map<string, Promise<unknown>> = new Map()\n  const styleSheets: Map<string, Promise<RouteStyleSheet>> = new Map()\n  const routes: Map<string, Future<RouteLoaderEntry> | RouteLoaderEntry> =\n    new Map()\n\n  function maybeExecuteScript(\n    src: TrustedScriptURL | string\n  ): Promise<unknown> {\n    // With HMR we might need to \"reload\" scripts when they are\n    // disposed and readded. Executing scripts twice has no functional\n    // differences\n    if (process.env.NODE_ENV !== 'development') {\n      let prom: Promise<unknown> | undefined = loadedScripts.get(src.toString())\n      if (prom) {\n        return prom\n      }\n\n      // Skip executing script if it's already in the DOM:\n      if (document.querySelector(`script[src^=\"${src}\"]`)) {\n        return Promise.resolve()\n      }\n\n      loadedScripts.set(src.toString(), (prom = appendScript(src)))\n      return prom\n    } else {\n      return appendScript(src)\n    }\n  }\n\n  function fetchStyleSheet(href: string): Promise<RouteStyleSheet> {\n    let prom: Promise<RouteStyleSheet> | undefined = styleSheets.get(href)\n    if (prom) {\n      return prom\n    }\n\n    styleSheets.set(\n      href,\n      (prom = fetch(href, { credentials: 'same-origin' })\n        .then((res) => {\n          if (!res.ok) {\n            throw new Error(`Failed to load stylesheet: ${href}`)\n          }\n          return res.text().then((text) => ({ href: href, content: text }))\n        })\n        .catch((err) => {\n          throw markAssetError(err)\n        }))\n    )\n    return prom\n  }\n\n  return {\n    whenEntrypoint(route: string) {\n      return withFuture(route, entrypoints)\n    },\n    onEntrypoint(route: string, execute: undefined | (() => unknown)) {\n      ;(execute\n        ? Promise.resolve()\n            .then(() => execute())\n            .then(\n              (exports: any) => ({\n                component: (exports && exports.default) || exports,\n                exports: exports,\n              }),\n              (err) => ({ error: err })\n            )\n        : Promise.resolve(undefined)\n      ).then((input: RouteEntrypoint | undefined) => {\n        const old = entrypoints.get(route)\n        if (old && 'resolve' in old) {\n          if (input) {\n            entrypoints.set(route, input)\n            old.resolve(input)\n          }\n        } else {\n          if (input) {\n            entrypoints.set(route, input)\n          } else {\n            entrypoints.delete(route)\n          }\n          // when this entrypoint has been resolved before\n          // the route is outdated and we want to invalidate\n          // this cache entry\n          routes.delete(route)\n        }\n      })\n    },\n    loadRoute(route: string, prefetch?: boolean) {\n      return withFuture<RouteLoaderEntry>(route, routes, () => {\n        let devBuildPromiseResolve: () => void\n\n        if (process.env.NODE_ENV === 'development') {\n          devBuildPromise = new Promise<void>((resolve) => {\n            devBuildPromiseResolve = resolve\n          })\n        }\n\n        return resolvePromiseWithTimeout(\n          getFilesForRoute(assetPrefix, route)\n            .then(({ scripts, css }) => {\n              return Promise.all([\n                entrypoints.has(route)\n                  ? []\n                  : Promise.all(scripts.map(maybeExecuteScript)),\n                Promise.all(css.map(fetchStyleSheet)),\n              ] as const)\n            })\n            .then((res) => {\n              return this.whenEntrypoint(route).then((entrypoint) => ({\n                entrypoint,\n                styles: res[1],\n              }))\n            }),\n          MS_MAX_IDLE_DELAY,\n          markAssetError(new Error(`Route did not complete loading: ${route}`))\n        )\n          .then(({ entrypoint, styles }) => {\n            const res: RouteLoaderEntry = Object.assign<\n              { styles: RouteStyleSheet[] },\n              RouteEntrypoint\n            >({ styles: styles! }, entrypoint)\n            return 'error' in entrypoint ? entrypoint : res\n          })\n          .catch((err) => {\n            if (prefetch) {\n              // we don't want to cache errors during prefetch\n              throw err\n            }\n            return { error: err }\n          })\n          .finally(() => devBuildPromiseResolve?.())\n      })\n    },\n    prefetch(route: string): Promise<void> {\n      // https://github.com/GoogleChromeLabs/quicklink/blob/453a661fa1fa940e2d2e044452398e38c67a98fb/src/index.mjs#L115-L118\n      // License: Apache 2.0\n      let cn\n      if ((cn = (navigator as any).connection)) {\n        // Don't prefetch if using 2G or if Save-Data is enabled.\n        if (cn.saveData || /2g/.test(cn.effectiveType)) return Promise.resolve()\n      }\n      return getFilesForRoute(assetPrefix, route)\n        .then((output) =>\n          Promise.all(\n            canPrefetch\n              ? output.scripts.map((script) =>\n                  prefetchViaDom(script.toString(), 'script')\n                )\n              : []\n          )\n        )\n        .then(() => {\n          requestIdleCallback(() => this.loadRoute(route, true).catch(() => {}))\n        })\n        .catch(\n          // swallow prefetch errors\n          () => {}\n        )\n    },\n  }\n}\n", "import MODERN_BROWSERSLIST_TARGET from './modern-browserslist-target'\n\nexport { MODERN_BROWSERSLIST_TARGET }\n\nexport type ValueOf<T> = Required<T>[keyof T]\n\nexport const COMPILER_NAMES = {\n  client: 'client',\n  server: 'server',\n  edgeServer: 'edge-server',\n} as const\n\nexport type CompilerNameValues = ValueOf<typeof COMPILER_NAMES>\n\nexport const COMPILER_INDEXES: {\n  [compilerKey in CompilerNameValues]: number\n} = {\n  [COMPILER_NAMES.client]: 0,\n  [COMPILER_NAMES.server]: 1,\n  [COMPILER_NAMES.edgeServer]: 2,\n} as const\n\nexport const UNDERSCORE_NOT_FOUND_ROUTE = '/_not-found'\nexport const UNDERSCORE_NOT_FOUND_ROUTE_ENTRY = `${UNDERSCORE_NOT_FOUND_ROUTE}/page`\nexport const PHASE_EXPORT = 'phase-export'\nexport const PHASE_PRODUCTION_BUILD = 'phase-production-build'\nexport const PHASE_PRODUCTION_SERVER = 'phase-production-server'\nexport const PHASE_DEVELOPMENT_SERVER = 'phase-development-server'\nexport const PHASE_TEST = 'phase-test'\nexport const PHASE_INFO = 'phase-info'\nexport const PAGES_MANIFEST = 'pages-manifest.json'\nexport const WEBPACK_STATS = 'webpack-stats.json'\nexport const APP_PATHS_MANIFEST = 'app-paths-manifest.json'\nexport const APP_PATH_ROUTES_MANIFEST = 'app-path-routes-manifest.json'\nexport const BUILD_MANIFEST = 'build-manifest.json'\nexport const APP_BUILD_MANIFEST = 'app-build-manifest.json'\nexport const FUNCTIONS_CONFIG_MANIFEST = 'functions-config-manifest.json'\nexport const SUBRESOURCE_INTEGRITY_MANIFEST = 'subresource-integrity-manifest'\nexport const NEXT_FONT_MANIFEST = 'next-font-manifest'\nexport const EXPORT_MARKER = 'export-marker.json'\nexport const EXPORT_DETAIL = 'export-detail.json'\nexport const PRERENDER_MANIFEST = 'prerender-manifest.json'\nexport const ROUTES_MANIFEST = 'routes-manifest.json'\nexport const IMAGES_MANIFEST = 'images-manifest.json'\nexport const SERVER_FILES_MANIFEST = 'required-server-files.json'\nexport const DEV_CLIENT_PAGES_MANIFEST = '_devPagesManifest.json'\nexport const MIDDLEWARE_MANIFEST = 'middleware-manifest.json'\nexport const TURBOPACK_CLIENT_MIDDLEWARE_MANIFEST =\n  '_clientMiddlewareManifest.json'\nexport const DEV_CLIENT_MIDDLEWARE_MANIFEST = '_devMiddlewareManifest.json'\nexport const REACT_LOADABLE_MANIFEST = 'react-loadable-manifest.json'\nexport const SERVER_DIRECTORY = 'server'\nexport const CONFIG_FILES = [\n  'next.config.js',\n  'next.config.mjs',\n  'next.config.ts',\n]\nexport const BUILD_ID_FILE = 'BUILD_ID'\nexport const BLOCKED_PAGES = ['/_document', '/_app', '/_error']\nexport const CLIENT_PUBLIC_FILES_PATH = 'public'\nexport const CLIENT_STATIC_FILES_PATH = 'static'\nexport const STRING_LITERAL_DROP_BUNDLE = '__NEXT_DROP_CLIENT_FILE__'\nexport const NEXT_BUILTIN_DOCUMENT = '__NEXT_BUILTIN_DOCUMENT__'\nexport const BARREL_OPTIMIZATION_PREFIX = '__barrel_optimize__'\n\n// server/[entry]/page_client-reference-manifest.js\nexport const CLIENT_REFERENCE_MANIFEST = 'client-reference-manifest'\n// server/server-reference-manifest\nexport const SERVER_REFERENCE_MANIFEST = 'server-reference-manifest'\n// server/middleware-build-manifest.js\nexport const MIDDLEWARE_BUILD_MANIFEST = 'middleware-build-manifest'\n// server/middleware-react-loadable-manifest.js\nexport const MIDDLEWARE_REACT_LOADABLE_MANIFEST =\n  'middleware-react-loadable-manifest'\n// server/interception-route-rewrite-manifest.js\nexport const INTERCEPTION_ROUTE_REWRITE_MANIFEST =\n  'interception-route-rewrite-manifest'\n// server/dynamic-css-manifest.js\nexport const DYNAMIC_CSS_MANIFEST = 'dynamic-css-manifest'\n\n// static/runtime/main.js\nexport const CLIENT_STATIC_FILES_RUNTIME_MAIN = `main`\nexport const CLIENT_STATIC_FILES_RUNTIME_MAIN_APP = `${CLIENT_STATIC_FILES_RUNTIME_MAIN}-app`\n// next internal client components chunk for layouts\nexport const APP_CLIENT_INTERNALS = 'app-pages-internals'\n// static/runtime/react-refresh.js\nexport const CLIENT_STATIC_FILES_RUNTIME_REACT_REFRESH = `react-refresh`\n// static/runtime/amp.js\nexport const CLIENT_STATIC_FILES_RUNTIME_AMP = `amp`\n// static/runtime/webpack.js\nexport const CLIENT_STATIC_FILES_RUNTIME_WEBPACK = `webpack`\n// static/runtime/polyfills.js\nexport const CLIENT_STATIC_FILES_RUNTIME_POLYFILLS = 'polyfills'\nexport const CLIENT_STATIC_FILES_RUNTIME_POLYFILLS_SYMBOL = Symbol(\n  CLIENT_STATIC_FILES_RUNTIME_POLYFILLS\n)\nexport const DEFAULT_RUNTIME_WEBPACK = 'webpack-runtime'\nexport const EDGE_RUNTIME_WEBPACK = 'edge-runtime-webpack'\nexport const STATIC_PROPS_ID = '__N_SSG'\nexport const SERVER_PROPS_ID = '__N_SSP'\nexport const DEFAULT_SERIF_FONT = {\n  name: 'Times New Roman',\n  xAvgCharWidth: 821,\n  azAvgWidth: 854.3953488372093,\n  unitsPerEm: 2048,\n}\nexport const DEFAULT_SANS_SERIF_FONT = {\n  name: 'Arial',\n  xAvgCharWidth: 904,\n  azAvgWidth: 934.5116279069767,\n  unitsPerEm: 2048,\n}\nexport const STATIC_STATUS_PAGES = ['/500']\nexport const TRACE_OUTPUT_VERSION = 1\n// in `MB`\nexport const TURBO_TRACE_DEFAULT_MEMORY_LIMIT = 6000\n\nexport const RSC_MODULE_TYPES = {\n  client: 'client',\n  server: 'server',\n} as const\n\n// comparing\n// https://nextjs.org/docs/api-reference/edge-runtime\n// with\n// https://nodejs.org/docs/latest/api/globals.html\nexport const EDGE_UNSUPPORTED_NODE_APIS = [\n  'clearImmediate',\n  'setImmediate',\n  'BroadcastChannel',\n  'ByteLengthQueuingStrategy',\n  'CompressionStream',\n  'CountQueuingStrategy',\n  'DecompressionStream',\n  'DomException',\n  'MessageChannel',\n  'MessageEvent',\n  'MessagePort',\n  'ReadableByteStreamController',\n  'ReadableStreamBYOBRequest',\n  'ReadableStreamDefaultController',\n  'TransformStreamDefaultController',\n  'WritableStreamDefaultController',\n]\n\nexport const SYSTEM_ENTRYPOINTS = new Set<string>([\n  CLIENT_STATIC_FILES_RUNTIME_MAIN,\n  CLIENT_STATIC_FILES_RUNTIME_REACT_REFRESH,\n  CLIENT_STATIC_FILES_RUNTIME_AMP,\n  CLIENT_STATIC_FILES_RUNTIME_MAIN_APP,\n])\n", "/**\n * For a given page path, this function ensures that there is no backslash\n * escaping slashes in the path. Example:\n *  - `foo\\/bar\\/baz` -> `foo/bar/baz`\n */\nexport function normalizePathSep(path: string): string {\n  return path.replace(/\\\\/g, '/')\n}\n", "import { isAbsoluteUrl, getLocationOrigin } from '../../utils'\nimport { hasBasePath } from '../../../../client/has-base-path'\n\n/**\n * Detects whether a given url is routable by the Next.js router (browser only).\n */\nexport function isLocalURL(url: string): boolean {\n  // prevent a hydration mismatch on href for url with anchor refs\n  if (!isAbsoluteUrl(url)) return true\n  try {\n    // absolute urls can be local if they are on the same origin\n    const locationOrigin = getLocationOrigin()\n    const resolved = new URL(url, locationOrigin)\n    return resolved.origin === locationOrigin && hasBasePath(resolved.pathname)\n  } catch (_) {\n    return false\n  }\n}\n", "function _interop_require_default(obj) {\n    return obj && obj.__esModule ? obj : { default: obj };\n}\nexport { _interop_require_default as _ };\n", "import { RedirectStatusCode } from './redirect-status-code'\n\nexport const REDIRECT_ERROR_CODE = 'NEXT_REDIRECT'\n\nexport enum RedirectType {\n  push = 'push',\n  replace = 'replace',\n}\n\nexport type RedirectError = Error & {\n  digest: `${typeof REDIRECT_ERROR_CODE};${RedirectType};${string};${RedirectStatusCode};`\n}\n\n/**\n * Checks an error to determine if it's an error generated by the\n * `redirect(url)` helper.\n *\n * @param error the error that may reference a redirect error\n * @returns true if the error is a redirect error\n */\nexport function isRedirectError(error: unknown): error is RedirectError {\n  if (\n    typeof error !== 'object' ||\n    error === null ||\n    !('digest' in error) ||\n    typeof error.digest !== 'string'\n  ) {\n    return false\n  }\n\n  const digest = error.digest.split(';')\n  const [errorCode, type] = digest\n  const destination = digest.slice(2, -2).join(';')\n  const status = digest.at(-2)\n\n  const statusCode = Number(status)\n\n  return (\n    errorCode === REDIRECT_ERROR_CODE &&\n    (type === 'replace' || type === 'push') &&\n    typeof destination === 'string' &&\n    !isNaN(statusCode) &&\n    statusCode in RedirectStatusCode\n  )\n}\n", "\"use strict\";\nvar _global_process, _global_process1;\nmodule.exports = ((_global_process = global.process) == null ? void 0 : _global_process.env) && typeof ((_global_process1 = global.process) == null ? void 0 : _global_process1.env) === 'object' ? global.process : require('next/dist/compiled/process');\n\n//# sourceMappingURL=process.js.map", "\"use strict\";\nObject.defineProperty(exports, \"__esModule\", {\n    value: true\n});\n0 && (module.exports = {\n    default: null,\n    getProperError: null\n});\nfunction _export(target, all) {\n    for(var name in all)Object.defineProperty(target, name, {\n        enumerable: true,\n        get: all[name]\n    });\n}\n_export(exports, {\n    /**\n * Checks whether the given value is a NextError.\n * This can be used to print a more detailed error message with properties like `code` & `digest`.\n */ default: function() {\n        return isError;\n    },\n    getProperError: function() {\n        return getProperError;\n    }\n});\nconst _isplainobject = require(\"../shared/lib/is-plain-object\");\nfunction isError(err) {\n    return typeof err === 'object' && err !== null && 'name' in err && 'message' in err;\n}\nfunction safeStringify(obj) {\n    const seen = new WeakSet();\n    return JSON.stringify(obj, (_key, value)=>{\n        // If value is an object and already seen, replace with \"[Circular]\"\n        if (typeof value === 'object' && value !== null) {\n            if (seen.has(value)) {\n                return '[Circular]';\n            }\n            seen.add(value);\n        }\n        return value;\n    });\n}\nfunction getProperError(err) {\n    if (isError(err)) {\n        return err;\n    }\n    if (process.env.NODE_ENV === 'development') {\n        // provide better error for case where `throw undefined`\n        // is called in development\n        if (typeof err === 'undefined') {\n            return Object.defineProperty(new Error('An undefined error was thrown, ' + 'see here for more info: https://nextjs.org/docs/messages/threw-undefined'), \"__NEXT_ERROR_CODE\", {\n                value: \"E98\",\n                enumerable: false,\n                configurable: true\n            });\n        }\n        if (err === null) {\n            return Object.defineProperty(new Error('A null error was thrown, ' + 'see here for more info: https://nextjs.org/docs/messages/threw-undefined'), \"__NEXT_ERROR_CODE\", {\n                value: \"E336\",\n                enumerable: false,\n                configurable: true\n            });\n        }\n    }\n    return Object.defineProperty(new Error((0, _isplainobject.isPlainObject)(err) ? safeStringify(err) : err + ''), \"__NEXT_ERROR_CODE\", {\n        value: \"E394\",\n        enumerable: false,\n        configurable: true\n    });\n}\n\n//# sourceMappingURL=is-error.js.map", "import { normalizeAppPath } from './app-paths'\n\n// order matters here, the first match will be used\nexport const INTERCEPTION_ROUTE_MARKERS = [\n  '(..)(..)',\n  '(.)',\n  '(..)',\n  '(...)',\n] as const\n\nexport function isInterceptionRouteAppPath(path: string): boolean {\n  // TODO-APP: add more serious validation\n  return (\n    path\n      .split('/')\n      .find((segment) =>\n        INTERCEPTION_ROUTE_MARKERS.find((m) => segment.startsWith(m))\n      ) !== undefined\n  )\n}\n\nexport function extractInterceptionRouteInformation(path: string) {\n  let interceptingRoute: string | undefined,\n    marker: (typeof INTERCEPTION_ROUTE_MARKERS)[number] | undefined,\n    interceptedRoute: string | undefined\n\n  for (const segment of path.split('/')) {\n    marker = INTERCEPTION_ROUTE_MARKERS.find((m) => segment.startsWith(m))\n    if (marker) {\n      ;[interceptingRoute, interceptedRoute] = path.split(marker, 2)\n      break\n    }\n  }\n\n  if (!interceptingRoute || !marker || !interceptedRoute) {\n    throw new Error(\n      `Invalid interception route: ${path}. Must be in the format /<intercepting route>/(..|...|..)(..)/<intercepted route>`\n    )\n  }\n\n  interceptingRoute = normalizeAppPath(interceptingRoute) // normalize the path, e.g. /(blog)/feed -> /feed\n\n  switch (marker) {\n    case '(.)':\n      // (.) indicates that we should match with sibling routes, so we just need to append the intercepted route to the intercepting route\n      if (interceptingRoute === '/') {\n        interceptedRoute = `/${interceptedRoute}`\n      } else {\n        interceptedRoute = interceptingRoute + '/' + interceptedRoute\n      }\n      break\n    case '(..)':\n      // (..) indicates that we should match at one level up, so we need to remove the last segment of the intercepting route\n      if (interceptingRoute === '/') {\n        throw new Error(\n          `Invalid interception route: ${path}. Cannot use (..) marker at the root level, use (.) instead.`\n        )\n      }\n      interceptedRoute = interceptingRoute\n        .split('/')\n        .slice(0, -1)\n        .concat(interceptedRoute)\n        .join('/')\n      break\n    case '(...)':\n      // (...) will match the route segment in the root directory, so we need to use the root directory to prepend the intercepted route\n      interceptedRoute = '/' + interceptedRoute\n      break\n    case '(..)(..)':\n      // (..)(..) indicates that we should match at two levels up, so we need to remove the last two segments of the intercepting route\n\n      const splitInterceptingRoute = interceptingRoute.split('/')\n      if (splitInterceptingRoute.length <= 2) {\n        throw new Error(\n          `Invalid interception route: ${path}. Cannot use (..)(..) marker at the root level or one level up.`\n        )\n      }\n\n      interceptedRoute = splitInterceptingRoute\n        .slice(0, -2)\n        .concat(interceptedRoute)\n        .join('/')\n      break\n    default:\n      throw new Error('Invariant: unexpected marker')\n  }\n\n  return { interceptingRoute, interceptedRoute }\n}\n", "const DOMAttributeNames: Record<string, string> = {\n  acceptCharset: 'accept-charset',\n  className: 'class',\n  htmlFor: 'for',\n  httpEquiv: 'http-equiv',\n  noModule: 'noModule',\n}\n\nconst ignoreProps = [\n  'onLoad',\n  'onReady',\n  'dangerouslySetInnerHTML',\n  'children',\n  'onError',\n  'strategy',\n  'stylesheets',\n]\n\nfunction isBooleanScriptAttribute(\n  attr: string\n): attr is 'async' | 'defer' | 'noModule' {\n  return ['async', 'defer', 'noModule'].includes(attr)\n}\n\nexport function setAttributesFromProps(el: HTMLElement, props: object) {\n  for (const [p, value] of Object.entries(props)) {\n    if (!props.hasOwnProperty(p)) continue\n    if (ignoreProps.includes(p)) continue\n\n    // we don't render undefined props to the DOM\n    if (value === undefined) {\n      continue\n    }\n\n    const attr = DOMAttributeNames[p] || p.toLowerCase()\n\n    if (el.tagName === 'SCRIPT' && isBooleanScriptAttribute(attr)) {\n      // Correctly assign boolean script attributes\n      // https://github.com/vercel/next.js/pull/20748\n      ;(el as HTMLScriptElement)[attr] = !!value\n    } else {\n      el.setAttribute(attr, String(value))\n    }\n\n    // Remove falsy non-zero boolean attributes so they are correctly interpreted\n    // (e.g. if we set them to false, this coerces to the string \"false\", which the browser interprets as true)\n    if (\n      value === false ||\n      (el.tagName === 'SCRIPT' &&\n        isBooleanScriptAttribute(attr) &&\n        (!value || value === 'false'))\n    ) {\n      // Call setAttribute before, as we need to set and unset the attribute to override force async:\n      // https://html.spec.whatwg.org/multipage/scripting.html#script-force-async\n      el.setAttribute(attr, '')\n      el.removeAttribute(attr)\n    }\n  }\n}\n", "/**\n * This module imports the client instrumentation hook from the project root.\n *\n * The `private-next-instrumentation-client` module is automatically aliased to\n * the `instrumentation-client.ts` file in the project root by webpack or turbopack.\n */ \"use strict\";\nif (process.env.NODE_ENV === 'development') {\n    const measureName = 'Client Instrumentation Hook';\n    const startTime = performance.now();\n    module.exports = require('private-next-instrumentation-client');\n    const endTime = performance.now();\n    const duration = endTime - startTime;\n    performance.measure(measureName, {\n        start: startTime,\n        end: endTime,\n        detail: 'Client instrumentation initialization'\n    });\n    // Using 16ms threshold as it represents one frame (1000ms/60fps)\n    // This helps identify if the instrumentation hook initialization\n    // could potentially cause frame drops during development.\n    const THRESHOLD = 16;\n    if (duration > THRESHOLD) {\n        console.log(`[${measureName}] Slow execution detected: ${duration.toFixed(0)}ms (Note: Code download overhead is not included in this measurement)`);\n    }\n} else {\n    module.exports = require('private-next-instrumentation-client');\n}\n\n//# sourceMappingURL=require-instrumentation-client.js.map", "import {\n  NEXT_INTERCEPTION_MARKER_PREFIX,\n  NEXT_QUERY_PARAM_PREFIX,\n} from '../../../../lib/constants'\nimport { INTERCEPTION_ROUTE_MARKERS } from './interception-routes'\nimport { escapeStringRegexp } from '../../escape-regexp'\nimport { removeTrailingSlash } from './remove-trailing-slash'\n\nexport interface Group {\n  pos: number\n  repeat: boolean\n  optional: boolean\n}\n\nexport interface RouteRegex {\n  groups: { [groupName: string]: Group }\n  re: RegExp\n}\n\ntype GetNamedRouteRegexOptions = {\n  /**\n   * Whether to prefix the route keys with the NEXT_INTERCEPTION_MARKER_PREFIX\n   * or NEXT_QUERY_PARAM_PREFIX. This is only relevant when creating the\n   * routes-manifest during the build.\n   */\n  prefixRouteKeys: boolean\n\n  /**\n   * Whether to include the suffix in the route regex. This means that when you\n   * have something like `/[...slug].json` the `.json` part will be included\n   * in the regex, yielding `/(.*).json` as the regex.\n   */\n  includeSuffix?: boolean\n\n  /**\n   * Whether to include the prefix in the route regex. This means that when you\n   * have something like `/[...slug].json` the `/` part will be included\n   * in the regex, yielding `^/(.*).json$` as the regex.\n   *\n   * Note that interception markers will already be included without the need\n   */\n  includePrefix?: boolean\n\n  /**\n   * Whether to exclude the optional trailing slash from the route regex.\n   */\n  excludeOptionalTrailingSlash?: boolean\n\n  /**\n   * Whether to backtrack duplicate keys. This is only relevant when creating\n   * the routes-manifest during the build.\n   */\n  backreferenceDuplicateKeys?: boolean\n}\n\ntype GetRouteRegexOptions = {\n  /**\n   * Whether to include extra parts in the route regex. This means that when you\n   * have something like `/[...slug].json` the `.json` part will be included\n   * in the regex, yielding `/(.*).json` as the regex.\n   */\n  includeSuffix?: boolean\n\n  /**\n   * Whether to include the prefix in the route regex. This means that when you\n   * have something like `/[...slug].json` the `/` part will be included\n   * in the regex, yielding `^/(.*).json$` as the regex.\n   *\n   * Note that interception markers will already be included without the need\n   * of adding this option.\n   */\n  includePrefix?: boolean\n\n  /**\n   * Whether to exclude the optional trailing slash from the route regex.\n   */\n  excludeOptionalTrailingSlash?: boolean\n}\n\n/**\n * Regular expression pattern used to match route parameters.\n * Matches both single parameters and parameter groups.\n * Examples:\n *   - `[[...slug]]` matches parameter group with key 'slug', repeat: true, optional: true\n *   - `[...slug]` matches parameter group with key 'slug', repeat: true, optional: false\n *   - `[[foo]]` matches parameter with key 'foo', repeat: false, optional: true\n *   - `[bar]` matches parameter with key 'bar', repeat: false, optional: false\n */\nconst PARAMETER_PATTERN = /^([^[]*)\\[((?:\\[[^\\]]*\\])|[^\\]]+)\\](.*)$/\n\n/**\n * Parses a given parameter from a route to a data structure that can be used\n * to generate the parametrized route.\n * Examples:\n *   - `[[...slug]]` -> `{ key: 'slug', repeat: true, optional: true }`\n *   - `[...slug]` -> `{ key: 'slug', repeat: true, optional: false }`\n *   - `[[foo]]` -> `{ key: 'foo', repeat: false, optional: true }`\n *   - `[bar]` -> `{ key: 'bar', repeat: false, optional: false }`\n *   - `fizz` -> `{ key: 'fizz', repeat: false, optional: false }`\n * @param param - The parameter to parse.\n * @returns The parsed parameter as a data structure.\n */\nexport function parseParameter(param: string) {\n  const match = param.match(PARAMETER_PATTERN)\n\n  if (!match) {\n    return parseMatchedParameter(param)\n  }\n\n  return parseMatchedParameter(match[2])\n}\n\n/**\n * Parses a matched parameter from the PARAMETER_PATTERN regex to a data structure that can be used\n * to generate the parametrized route.\n * Examples:\n *   - `[...slug]` -> `{ key: 'slug', repeat: true, optional: true }`\n *   - `...slug` -> `{ key: 'slug', repeat: true, optional: false }`\n *   - `[foo]` -> `{ key: 'foo', repeat: false, optional: true }`\n *   - `bar` -> `{ key: 'bar', repeat: false, optional: false }`\n * @param param - The matched parameter to parse.\n * @returns The parsed parameter as a data structure.\n */\nfunction parseMatchedParameter(param: string) {\n  const optional = param.startsWith('[') && param.endsWith(']')\n  if (optional) {\n    param = param.slice(1, -1)\n  }\n  const repeat = param.startsWith('...')\n  if (repeat) {\n    param = param.slice(3)\n  }\n  return { key: param, repeat, optional }\n}\n\nfunction getParametrizedRoute(\n  route: string,\n  includeSuffix: boolean,\n  includePrefix: boolean\n) {\n  const groups: { [groupName: string]: Group } = {}\n  let groupIndex = 1\n\n  const segments: string[] = []\n  for (const segment of removeTrailingSlash(route).slice(1).split('/')) {\n    const markerMatch = INTERCEPTION_ROUTE_MARKERS.find((m) =>\n      segment.startsWith(m)\n    )\n    const paramMatches = segment.match(PARAMETER_PATTERN) // Check for parameters\n\n    if (markerMatch && paramMatches && paramMatches[2]) {\n      const { key, optional, repeat } = parseMatchedParameter(paramMatches[2])\n      groups[key] = { pos: groupIndex++, repeat, optional }\n      segments.push(`/${escapeStringRegexp(markerMatch)}([^/]+?)`)\n    } else if (paramMatches && paramMatches[2]) {\n      const { key, repeat, optional } = parseMatchedParameter(paramMatches[2])\n      groups[key] = { pos: groupIndex++, repeat, optional }\n\n      if (includePrefix && paramMatches[1]) {\n        segments.push(`/${escapeStringRegexp(paramMatches[1])}`)\n      }\n\n      let s = repeat ? (optional ? '(?:/(.+?))?' : '/(.+?)') : '/([^/]+?)'\n\n      // Remove the leading slash if includePrefix already added it.\n      if (includePrefix && paramMatches[1]) {\n        s = s.substring(1)\n      }\n\n      segments.push(s)\n    } else {\n      segments.push(`/${escapeStringRegexp(segment)}`)\n    }\n\n    // If there's a suffix, add it to the segments if it's enabled.\n    if (includeSuffix && paramMatches && paramMatches[3]) {\n      segments.push(escapeStringRegexp(paramMatches[3]))\n    }\n  }\n\n  return {\n    parameterizedRoute: segments.join(''),\n    groups,\n  }\n}\n\n/**\n * From a normalized route this function generates a regular expression and\n * a corresponding groups object intended to be used to store matching groups\n * from the regular expression.\n */\nexport function getRouteRegex(\n  normalizedRoute: string,\n  {\n    includeSuffix = false,\n    includePrefix = false,\n    excludeOptionalTrailingSlash = false,\n  }: GetRouteRegexOptions = {}\n): RouteRegex {\n  const { parameterizedRoute, groups } = getParametrizedRoute(\n    normalizedRoute,\n    includeSuffix,\n    includePrefix\n  )\n\n  let re = parameterizedRoute\n  if (!excludeOptionalTrailingSlash) {\n    re += '(?:/)?'\n  }\n\n  return {\n    re: new RegExp(`^${re}$`),\n    groups: groups,\n  }\n}\n\n/**\n * Builds a function to generate a minimal routeKey using only a-z and minimal\n * number of characters.\n */\nfunction buildGetSafeRouteKey() {\n  let i = 0\n\n  return () => {\n    let routeKey = ''\n    let j = ++i\n    while (j > 0) {\n      routeKey += String.fromCharCode(97 + ((j - 1) % 26))\n      j = Math.floor((j - 1) / 26)\n    }\n    return routeKey\n  }\n}\n\nfunction getSafeKeyFromSegment({\n  interceptionMarker,\n  getSafeRouteKey,\n  segment,\n  routeKeys,\n  keyPrefix,\n  backreferenceDuplicateKeys,\n}: {\n  interceptionMarker?: string\n  getSafeRouteKey: () => string\n  segment: string\n  routeKeys: Record<string, string>\n  keyPrefix?: string\n  backreferenceDuplicateKeys: boolean\n}) {\n  const { key, optional, repeat } = parseMatchedParameter(segment)\n\n  // replace any non-word characters since they can break\n  // the named regex\n  let cleanedKey = key.replace(/\\W/g, '')\n\n  if (keyPrefix) {\n    cleanedKey = `${keyPrefix}${cleanedKey}`\n  }\n  let invalidKey = false\n\n  // check if the key is still invalid and fallback to using a known\n  // safe key\n  if (cleanedKey.length === 0 || cleanedKey.length > 30) {\n    invalidKey = true\n  }\n  if (!isNaN(parseInt(cleanedKey.slice(0, 1)))) {\n    invalidKey = true\n  }\n\n  if (invalidKey) {\n    cleanedKey = getSafeRouteKey()\n  }\n\n  const duplicateKey = cleanedKey in routeKeys\n\n  if (keyPrefix) {\n    routeKeys[cleanedKey] = `${keyPrefix}${key}`\n  } else {\n    routeKeys[cleanedKey] = key\n  }\n\n  // if the segment has an interception marker, make sure that's part of the regex pattern\n  // this is to ensure that the route with the interception marker doesn't incorrectly match\n  // the non-intercepted route (ie /app/(.)[username] should not match /app/[username])\n  const interceptionPrefix = interceptionMarker\n    ? escapeStringRegexp(interceptionMarker)\n    : ''\n\n  let pattern: string\n  if (duplicateKey && backreferenceDuplicateKeys) {\n    // Use a backreference to the key to ensure that the key is the same value\n    // in each of the placeholders.\n    pattern = `\\\\k<${cleanedKey}>`\n  } else if (repeat) {\n    pattern = `(?<${cleanedKey}>.+?)`\n  } else {\n    pattern = `(?<${cleanedKey}>[^/]+?)`\n  }\n\n  return optional\n    ? `(?:/${interceptionPrefix}${pattern})?`\n    : `/${interceptionPrefix}${pattern}`\n}\n\nfunction getNamedParametrizedRoute(\n  route: string,\n  prefixRouteKeys: boolean,\n  includeSuffix: boolean,\n  includePrefix: boolean,\n  backreferenceDuplicateKeys: boolean\n) {\n  const getSafeRouteKey = buildGetSafeRouteKey()\n  const routeKeys: { [named: string]: string } = {}\n\n  const segments: string[] = []\n  for (const segment of removeTrailingSlash(route).slice(1).split('/')) {\n    const hasInterceptionMarker = INTERCEPTION_ROUTE_MARKERS.some((m) =>\n      segment.startsWith(m)\n    )\n\n    const paramMatches = segment.match(PARAMETER_PATTERN) // Check for parameters\n\n    if (hasInterceptionMarker && paramMatches && paramMatches[2]) {\n      // If there's an interception marker, add it to the segments.\n      segments.push(\n        getSafeKeyFromSegment({\n          getSafeRouteKey,\n          interceptionMarker: paramMatches[1],\n          segment: paramMatches[2],\n          routeKeys,\n          keyPrefix: prefixRouteKeys\n            ? NEXT_INTERCEPTION_MARKER_PREFIX\n            : undefined,\n          backreferenceDuplicateKeys,\n        })\n      )\n    } else if (paramMatches && paramMatches[2]) {\n      // If there's a prefix, add it to the segments if it's enabled.\n      if (includePrefix && paramMatches[1]) {\n        segments.push(`/${escapeStringRegexp(paramMatches[1])}`)\n      }\n\n      let s = getSafeKeyFromSegment({\n        getSafeRouteKey,\n        segment: paramMatches[2],\n        routeKeys,\n        keyPrefix: prefixRouteKeys ? NEXT_QUERY_PARAM_PREFIX : undefined,\n        backreferenceDuplicateKeys,\n      })\n\n      // Remove the leading slash if includePrefix already added it.\n      if (includePrefix && paramMatches[1]) {\n        s = s.substring(1)\n      }\n\n      segments.push(s)\n    } else {\n      segments.push(`/${escapeStringRegexp(segment)}`)\n    }\n\n    // If there's a suffix, add it to the segments if it's enabled.\n    if (includeSuffix && paramMatches && paramMatches[3]) {\n      segments.push(escapeStringRegexp(paramMatches[3]))\n    }\n  }\n\n  return {\n    namedParameterizedRoute: segments.join(''),\n    routeKeys,\n  }\n}\n\n/**\n * This function extends `getRouteRegex` generating also a named regexp where\n * each group is named along with a routeKeys object that indexes the assigned\n * named group with its corresponding key. When the routeKeys need to be\n * prefixed to uniquely identify internally the \"prefixRouteKey\" arg should\n * be \"true\" currently this is only the case when creating the routes-manifest\n * during the build\n */\nexport function getNamedRouteRegex(\n  normalizedRoute: string,\n  options: GetNamedRouteRegexOptions\n) {\n  const result = getNamedParametrizedRoute(\n    normalizedRoute,\n    options.prefixRouteKeys,\n    options.includeSuffix ?? false,\n    options.includePrefix ?? false,\n    options.backreferenceDuplicateKeys ?? false\n  )\n\n  let namedRegex = result.namedParameterizedRoute\n  if (!options.excludeOptionalTrailingSlash) {\n    namedRegex += '(?:/)?'\n  }\n\n  return {\n    ...getRouteRegex(normalizedRoute, options),\n    namedRegex: `^${namedRegex}$`,\n    routeKeys: result.routeKeys,\n  }\n}\n\n/**\n * Generates a named regexp.\n * This is intended to be using for build time only.\n */\nexport function getNamedMiddlewareRegex(\n  normalizedRoute: string,\n  options: {\n    catchAll?: boolean\n  }\n) {\n  const { parameterizedRoute } = getParametrizedRoute(\n    normalizedRoute,\n    false,\n    false\n  )\n  const { catchAll = true } = options\n  if (parameterizedRoute === '/') {\n    let catchAllRegex = catchAll ? '.*' : ''\n    return {\n      namedRegex: `^/${catchAllRegex}$`,\n    }\n  }\n\n  const { namedParameterizedRoute } = getNamedParametrizedRoute(\n    normalizedRoute,\n    false,\n    false,\n    false,\n    false\n  )\n  let catchAllGroupedRegex = catchAll ? '(?:(/.*)?)' : ''\n  return {\n    namedRegex: `^${namedParameterizedRoute}${catchAllGroupedRegex}$`,\n  }\n}\n", "import type { ParsedUrlQuery } from 'querystring'\nimport { getLocationOrigin } from '../../utils'\nimport { searchParamsToUrlQuery } from './querystring'\n\nexport interface ParsedRelativeUrl {\n  hash: string\n  href: string\n  pathname: string\n  query: ParsedUrlQuery\n  search: string\n}\n\n/**\n * Parses path-relative urls (e.g. `/hello/world?foo=bar`). If url isn't path-relative\n * (e.g. `./hello`) then at least base must be.\n * Absolute urls are rejected with one exception, in the browser, absolute urls that are on\n * the current origin will be parsed as relative\n */\nexport function parseRelativeUrl(\n  url: string,\n  base?: string,\n  parseQuery?: true\n): ParsedRelativeUrl\nexport function parseRelativeUrl(\n  url: string,\n  base: string | undefined,\n  parseQuery: false\n): Omit<ParsedRelativeUrl, 'query'>\nexport function parseRelativeUrl(\n  url: string,\n  base?: string,\n  parseQuery = true\n): ParsedRelativeUrl | Omit<ParsedRelativeUrl, 'query'> {\n  const globalBase = new URL(\n    typeof window === 'undefined' ? 'http://n' : getLocationOrigin()\n  )\n\n  const resolvedBase = base\n    ? new URL(base, globalBase)\n    : url.startsWith('.')\n      ? new URL(\n          typeof window === 'undefined' ? 'http://n' : window.location.href\n        )\n      : globalBase\n\n  const { pathname, searchParams, search, hash, href, origin } = new URL(\n    url,\n    resolvedBase\n  )\n\n  if (origin !== globalBase.origin) {\n    throw new Error(`invariant: invalid relative URL, router received ${url}`)\n  }\n\n  return {\n    pathname,\n    query: parseQuery ? searchParamsToUrlQuery(searchParams) : undefined,\n    search,\n    hash,\n    href: href.slice(origin.length),\n  }\n}\n", "import type { NextPathnameInfo } from './get-next-pathname-info'\nimport { removeTrailingSlash } from './remove-trailing-slash'\nimport { addPathPrefix } from './add-path-prefix'\nimport { addPathSuffix } from './add-path-suffix'\nimport { addLocale } from './add-locale'\n\ninterface ExtendedInfo extends NextPathnameInfo {\n  defaultLocale?: string\n  ignorePrefix?: boolean\n}\n\nexport function formatNextPathnameInfo(info: ExtendedInfo) {\n  let pathname = addLocale(\n    info.pathname,\n    info.locale,\n    info.buildId ? undefined : info.defaultLocale,\n    info.ignorePrefix\n  )\n\n  if (info.buildId || !info.trailingSlash) {\n    pathname = removeTrailingSlash(pathname)\n  }\n\n  if (info.buildId) {\n    pathname = addPathSuffix(\n      addPathPrefix(pathname, `/_next/data/${info.buildId}`),\n      info.pathname === '/' ? 'index.json' : '.json'\n    )\n  }\n\n  pathname = addPathPrefix(pathname, info.basePath)\n  return !info.buildId && info.trailingSlash\n    ? !pathname.endsWith('/')\n      ? addPathSuffix(pathname, '/')\n      : pathname\n    : removeTrailingSlash(pathname)\n}\n", "import React from 'react'\nimport isError from '../../../lib/is-error'\nimport { copyNextErrorCode } from '../../../lib/error-telemetry-utils'\n\nconst REACT_ERROR_STACK_BOTTOM_FRAME = 'react-stack-bottom-frame'\nconst REACT_ERROR_STACK_BOTTOM_FRAME_REGEX = new RegExp(\n  `(at ${REACT_ERROR_STACK_BOTTOM_FRAME} )|(${REACT_ERROR_STACK_BOTTOM_FRAME}\\\\@)`\n)\n\nexport function getReactStitchedError<T = unknown>(err: T): Error | T {\n  const isErrorInstance = isError(err)\n  const originStack = isErrorInstance ? err.stack || '' : ''\n  const originMessage = isErrorInstance ? err.message : ''\n  const stackLines = originStack.split('\\n')\n  const indexOfSplit = stackLines.findIndex((line) =>\n    REACT_ERROR_STACK_BOTTOM_FRAME_REGEX.test(line)\n  )\n  const isOriginalReactError = indexOfSplit >= 0 // has the react-stack-bottom-frame\n  let newStack = isOriginalReactError\n    ? stackLines.slice(0, indexOfSplit).join('\\n')\n    : originStack\n\n  const newError = new Error(originMessage)\n  // Copy all enumerable properties, e.g. digest\n  Object.assign(newError, err)\n  copyNextErrorCode(err, newError)\n  newError.stack = newStack\n\n  // Avoid duplicate overriding stack frames\n  appendOwnerStack(newError)\n\n  return newError\n}\n\nfunction appendOwnerStack(error: Error) {\n  if (!React.captureOwnerStack) {\n    return\n  }\n  let stack = error.stack || ''\n  // This module is only bundled in development mode so this is safe.\n  const ownerStack = React.captureOwnerStack()\n  // Avoid duplicate overriding stack frames\n  if (ownerStack && stack.endsWith(ownerStack) === false) {\n    stack += ownerStack\n    // Override stack\n    error.stack = stack\n  }\n}\n", "// regexp is based on https://github.com/sindresorhus/escape-string-regexp\nconst reHasRegExp = /[|\\\\{}()[\\]^$+*?.-]/\nconst reReplaceRegExp = /[|\\\\{}()[\\]^$+*?.-]/g\n\nexport function escapeStringRegexp(str: string) {\n  // see also: https://github.com/lodash/lodash/blob/2da024c3b4f9947a48517639de7560457cd4ec6c/escapeRegExp.js#L23\n  if (reHasRegExp.test(str)) {\n    return str.replace(reReplaceRegExp, '\\\\$&')\n  }\n  return str\n}\n", "export const requestIdleCallback =\n  (typeof self !== 'undefined' &&\n    self.requestIdleCallback &&\n    self.requestIdleCallback.bind(window)) ||\n  function (cb: IdleRequestCallback): number {\n    let start = Date.now()\n    return self.setTimeout(function () {\n      cb({\n        didTimeout: false,\n        timeRemaining: function () {\n          return Math.max(0, 50 - (Date.now() - start))\n        },\n      })\n    }, 1)\n  }\n\nexport const cancelIdleCallback =\n  (typeof self !== 'undefined' &&\n    self.cancelIdleCallback &&\n    self.cancelIdleCallback.bind(window)) ||\n  function (id: number) {\n    return clearTimeout(id)\n  }\n", "// tslint:disable:no-console\nimport type { ComponentType } from 'react'\nimport type { DomainLocale } from '../../../server/config'\nimport type { MittEmitter } from '../mitt'\nimport type { ParsedUrlQuery } from 'querystring'\nimport type { RouterEvent } from '../../../client/router'\nimport type { StyleSheetTuple } from '../../../client/page-loader'\nimport type { UrlObject } from 'url'\nimport type PageLoader from '../../../client/page-loader'\nimport type { AppContextType, NextPageContext, NEXT_DATA } from '../utils'\nimport { removeTrailingSlash } from './utils/remove-trailing-slash'\nimport {\n  getClientBuildManifest,\n  isAssetError,\n  markAssetError,\n} from '../../../client/route-loader'\nimport { handleClientScriptLoad } from '../../../client/script'\nimport isError, { getProperError } from '../../../lib/is-error'\nimport { denormalizePagePath } from '../page-path/denormalize-page-path'\nimport { normalizeLocalePath } from '../i18n/normalize-locale-path'\nimport mitt from '../mitt'\nimport { getLocationOrigin, getURL, loadGetInitialProps, ST } from '../utils'\nimport { isDynamicRoute } from './utils/is-dynamic'\nimport { parseRelativeUrl } from './utils/parse-relative-url'\nimport resolveRewrites from './utils/resolve-rewrites'\nimport { getRouteMatcher } from './utils/route-matcher'\nimport { getRouteRegex } from './utils/route-regex'\nimport { formatWithValidation } from './utils/format-url'\nimport { detectDomainLocale } from '../../../client/detect-domain-locale'\nimport { parsePath } from './utils/parse-path'\nimport { addLocale } from '../../../client/add-locale'\nimport { removeLocale } from '../../../client/remove-locale'\nimport { removeBasePath } from '../../../client/remove-base-path'\nimport { addBasePath } from '../../../client/add-base-path'\nimport { hasBasePath } from '../../../client/has-base-path'\nimport { resolveHref } from '../../../client/resolve-href'\nimport { isAPIRoute } from '../../../lib/is-api-route'\nimport { getNextPathnameInfo } from './utils/get-next-pathname-info'\nimport { formatNextPathnameInfo } from './utils/format-next-pathname-info'\nimport { compareRouterStates } from './utils/compare-states'\nimport { isLocalURL } from './utils/is-local-url'\nimport { isBot } from './utils/is-bot'\nimport { omit } from './utils/omit'\nimport { interpolateAs } from './utils/interpolate-as'\nimport { handleSmoothScroll } from './utils/handle-smooth-scroll'\nimport type { Params } from '../../../server/request/params'\nimport { MATCHED_PATH_HEADER } from '../../../lib/constants'\n\ndeclare global {\n  interface Window {\n    /* prod */\n    __NEXT_DATA__: NEXT_DATA\n  }\n}\n\ninterface RouteProperties {\n  shallow: boolean\n}\n\ninterface TransitionOptions {\n  shallow?: boolean\n  locale?: string | false\n  scroll?: boolean\n  unstable_skipClientCache?: boolean\n}\n\ninterface NextHistoryState {\n  url: string\n  as: string\n  options: TransitionOptions\n}\n\nexport type HistoryState =\n  | null\n  | { __NA: true; __N?: false }\n  | { __N: false; __NA?: false }\n  | ({ __NA?: false; __N: true; key: string } & NextHistoryState)\n\nfunction buildCancellationError() {\n  return Object.assign(new Error('Route Cancelled'), {\n    cancelled: true,\n  })\n}\n\ninterface MiddlewareEffectParams<T extends FetchDataOutput> {\n  fetchData?: () => Promise<T>\n  locale?: string\n  asPath: string\n  router: Router\n}\n\nexport async function matchesMiddleware<T extends FetchDataOutput>(\n  options: MiddlewareEffectParams<T>\n): Promise<boolean> {\n  const matchers = await Promise.resolve(\n    options.router.pageLoader.getMiddleware()\n  )\n  if (!matchers) return false\n\n  const { pathname: asPathname } = parsePath(options.asPath)\n  // remove basePath first since path prefix has to be in the order of `/${basePath}/${locale}`\n  const cleanedAs = hasBasePath(asPathname)\n    ? removeBasePath(asPathname)\n    : asPathname\n  const asWithBasePathAndLocale = addBasePath(\n    addLocale(cleanedAs, options.locale)\n  )\n\n  // Check only path match on client. Matching \"has\" should be done on server\n  // where we can access more info such as headers, HttpOnly cookie, etc.\n  return matchers.some((m) =>\n    new RegExp(m.regexp).test(asWithBasePathAndLocale)\n  )\n}\n\nfunction stripOrigin(url: string) {\n  const origin = getLocationOrigin()\n\n  return url.startsWith(origin) ? url.substring(origin.length) : url\n}\n\nfunction prepareUrlAs(router: NextRouter, url: Url, as?: Url) {\n  // If url and as provided as an object representation,\n  // we'll format them into the string version here.\n  let [resolvedHref, resolvedAs] = resolveHref(router, url, true)\n  const origin = getLocationOrigin()\n  const hrefWasAbsolute = resolvedHref.startsWith(origin)\n  const asWasAbsolute = resolvedAs && resolvedAs.startsWith(origin)\n\n  resolvedHref = stripOrigin(resolvedHref)\n  resolvedAs = resolvedAs ? stripOrigin(resolvedAs) : resolvedAs\n\n  const preparedUrl = hrefWasAbsolute ? resolvedHref : addBasePath(resolvedHref)\n  const preparedAs = as\n    ? stripOrigin(resolveHref(router, as))\n    : resolvedAs || resolvedHref\n\n  return {\n    url: preparedUrl,\n    as: asWasAbsolute ? preparedAs : addBasePath(preparedAs),\n  }\n}\n\nfunction resolveDynamicRoute(pathname: string, pages: string[]) {\n  const cleanPathname = removeTrailingSlash(denormalizePagePath(pathname))\n  if (cleanPathname === '/404' || cleanPathname === '/_error') {\n    return pathname\n  }\n\n  // handle resolving href for dynamic routes\n  if (!pages.includes(cleanPathname)) {\n    // eslint-disable-next-line array-callback-return\n    pages.some((page) => {\n      if (isDynamicRoute(page) && getRouteRegex(page).re.test(cleanPathname)) {\n        pathname = page\n        return true\n      }\n    })\n  }\n  return removeTrailingSlash(pathname)\n}\n\nfunction getMiddlewareData<T extends FetchDataOutput>(\n  source: string,\n  response: Response,\n  options: MiddlewareEffectParams<T>\n) {\n  const nextConfig = {\n    basePath: options.router.basePath,\n    i18n: { locales: options.router.locales },\n    trailingSlash: Boolean(process.env.__NEXT_TRAILING_SLASH),\n  }\n  const rewriteHeader = response.headers.get('x-nextjs-rewrite')\n\n  let rewriteTarget =\n    rewriteHeader || response.headers.get('x-nextjs-matched-path')\n\n  const matchedPath = response.headers.get(MATCHED_PATH_HEADER)\n\n  if (\n    matchedPath &&\n    !rewriteTarget &&\n    !matchedPath.includes('__next_data_catchall') &&\n    !matchedPath.includes('/_error') &&\n    !matchedPath.includes('/404')\n  ) {\n    // leverage x-matched-path to detect next.config.js rewrites\n    rewriteTarget = matchedPath\n  }\n\n  if (rewriteTarget) {\n    if (\n      rewriteTarget.startsWith('/') ||\n      process.env.__NEXT_EXTERNAL_MIDDLEWARE_REWRITE_RESOLVE\n    ) {\n      const parsedRewriteTarget = parseRelativeUrl(rewriteTarget)\n      const pathnameInfo = getNextPathnameInfo(parsedRewriteTarget.pathname, {\n        nextConfig,\n        parseData: true,\n      })\n\n      let fsPathname = removeTrailingSlash(pathnameInfo.pathname)\n      return Promise.all([\n        options.router.pageLoader.getPageList(),\n        getClientBuildManifest(),\n      ]).then(([pages, { __rewrites: rewrites }]: any) => {\n        let as = addLocale(pathnameInfo.pathname, pathnameInfo.locale)\n\n        if (\n          isDynamicRoute(as) ||\n          (!rewriteHeader &&\n            pages.includes(\n              normalizeLocalePath(removeBasePath(as), options.router.locales)\n                .pathname\n            ))\n        ) {\n          const parsedSource = getNextPathnameInfo(\n            parseRelativeUrl(source).pathname,\n            {\n              nextConfig: process.env.__NEXT_HAS_REWRITES\n                ? undefined\n                : nextConfig,\n              parseData: true,\n            }\n          )\n\n          as = addBasePath(parsedSource.pathname)\n          parsedRewriteTarget.pathname = as\n        }\n\n        if (process.env.__NEXT_HAS_REWRITES) {\n          const result = resolveRewrites(\n            as,\n            pages,\n            rewrites,\n            parsedRewriteTarget.query,\n            (path: string) => resolveDynamicRoute(path, pages),\n            options.router.locales\n          )\n\n          if (result.matchedPage) {\n            parsedRewriteTarget.pathname = result.parsedAs.pathname\n            as = parsedRewriteTarget.pathname\n            Object.assign(parsedRewriteTarget.query, result.parsedAs.query)\n          }\n        } else if (!pages.includes(fsPathname)) {\n          const resolvedPathname = resolveDynamicRoute(fsPathname, pages)\n\n          if (resolvedPathname !== fsPathname) {\n            fsPathname = resolvedPathname\n          }\n        }\n\n        const resolvedHref = !pages.includes(fsPathname)\n          ? resolveDynamicRoute(\n              normalizeLocalePath(\n                removeBasePath(parsedRewriteTarget.pathname),\n                options.router.locales\n              ).pathname,\n              pages\n            )\n          : fsPathname\n\n        if (isDynamicRoute(resolvedHref)) {\n          const matches = getRouteMatcher(getRouteRegex(resolvedHref))(as)\n          Object.assign(parsedRewriteTarget.query, matches || {})\n        }\n\n        return {\n          type: 'rewrite' as const,\n          parsedAs: parsedRewriteTarget,\n          resolvedHref,\n        }\n      })\n    }\n    const src = parsePath(source)\n    const pathname = formatNextPathnameInfo({\n      ...getNextPathnameInfo(src.pathname, { nextConfig, parseData: true }),\n      defaultLocale: options.router.defaultLocale,\n      buildId: '',\n    })\n\n    return Promise.resolve({\n      type: 'redirect-external' as const,\n      destination: `${pathname}${src.query}${src.hash}`,\n    })\n  }\n\n  const redirectTarget = response.headers.get('x-nextjs-redirect')\n\n  if (redirectTarget) {\n    if (redirectTarget.startsWith('/')) {\n      const src = parsePath(redirectTarget)\n      const pathname = formatNextPathnameInfo({\n        ...getNextPathnameInfo(src.pathname, { nextConfig, parseData: true }),\n        defaultLocale: options.router.defaultLocale,\n        buildId: '',\n      })\n\n      return Promise.resolve({\n        type: 'redirect-internal' as const,\n        newAs: `${pathname}${src.query}${src.hash}`,\n        newUrl: `${pathname}${src.query}${src.hash}`,\n      })\n    }\n\n    return Promise.resolve({\n      type: 'redirect-external' as const,\n      destination: redirectTarget,\n    })\n  }\n\n  return Promise.resolve({ type: 'next' as const })\n}\n\ninterface WithMiddlewareEffectsOutput extends FetchDataOutput {\n  effect: Awaited<ReturnType<typeof getMiddlewareData>>\n}\n\nasync function withMiddlewareEffects<T extends FetchDataOutput>(\n  options: MiddlewareEffectParams<T>\n): Promise<WithMiddlewareEffectsOutput | null> {\n  const matches = await matchesMiddleware(options)\n  if (!matches || !options.fetchData) {\n    return null\n  }\n\n  const data = await options.fetchData()\n\n  const effect = await getMiddlewareData(data.dataHref, data.response, options)\n\n  return {\n    dataHref: data.dataHref,\n    json: data.json,\n    response: data.response,\n    text: data.text,\n    cacheKey: data.cacheKey,\n    effect,\n  }\n}\n\nexport type Url = UrlObject | string\n\nexport type BaseRouter = {\n  route: string\n  pathname: string\n  query: ParsedUrlQuery\n  asPath: string\n  basePath: string\n  locale?: string | undefined\n  locales?: readonly string[] | undefined\n  defaultLocale?: string | undefined\n  domainLocales?: readonly DomainLocale[] | undefined\n  isLocaleDomain: boolean\n}\n\nexport type NextRouter = BaseRouter &\n  Pick<\n    Router,\n    | 'push'\n    | 'replace'\n    | 'reload'\n    | 'back'\n    | 'forward'\n    | 'prefetch'\n    | 'beforePopState'\n    | 'events'\n    | 'isFallback'\n    | 'isReady'\n    | 'isPreview'\n  >\n\nexport type PrefetchOptions = {\n  priority?: boolean\n  locale?: string | false\n  unstable_skipClientCache?: boolean\n}\n\nexport type PrivateRouteInfo =\n  | (Omit<CompletePrivateRouteInfo, 'styleSheets'> & { initial: true })\n  | CompletePrivateRouteInfo\n\nexport type CompletePrivateRouteInfo = {\n  Component: ComponentType\n  styleSheets: StyleSheetTuple[]\n  __N_SSG?: boolean\n  __N_SSP?: boolean\n  props?: Record<string, any>\n  err?: Error\n  error?: any\n  route?: string\n  resolvedAs?: string\n  query?: ParsedUrlQuery\n}\n\nexport type AppProps = Pick<CompletePrivateRouteInfo, 'Component' | 'err'> & {\n  router: Router\n} & Record<string, any>\nexport type AppComponent = ComponentType<AppProps>\n\ntype Subscription = (\n  data: PrivateRouteInfo,\n  App: AppComponent,\n  resetScroll: { x: number; y: number } | null\n) => Promise<void>\n\ntype BeforePopStateCallback = (state: NextHistoryState) => boolean\n\ntype ComponentLoadCancel = (() => void) | null\n\ntype HistoryMethod = 'replaceState' | 'pushState'\n\nconst manualScrollRestoration =\n  process.env.__NEXT_SCROLL_RESTORATION &&\n  typeof window !== 'undefined' &&\n  'scrollRestoration' in window.history &&\n  !!(function () {\n    try {\n      let v = '__next'\n      // eslint-disable-next-line no-sequences\n      return sessionStorage.setItem(v, v), sessionStorage.removeItem(v), true\n    } catch (n) {}\n  })()\n\nconst SSG_DATA_NOT_FOUND = Symbol('SSG_DATA_NOT_FOUND')\n\nfunction fetchRetry(\n  url: string,\n  attempts: number,\n  options: Pick<RequestInit, 'method' | 'headers'>\n): Promise<Response> {\n  return fetch(url, {\n    // Cookies are required to be present for Next.js' SSG \"Preview Mode\".\n    // Cookies may also be required for `getServerSideProps`.\n    //\n    // > `fetch` won’t send cookies, unless you set the credentials init\n    // > option.\n    // https://developer.mozilla.org/docs/Web/API/Fetch_API/Using_Fetch\n    //\n    // > For maximum browser compatibility when it comes to sending &\n    // > receiving cookies, always supply the `credentials: 'same-origin'`\n    // > option instead of relying on the default.\n    // https://github.com/github/fetch#caveats\n    credentials: 'same-origin',\n    method: options.method || 'GET',\n    headers: Object.assign({}, options.headers, {\n      'x-nextjs-data': '1',\n    }),\n  }).then((response) => {\n    return !response.ok && attempts > 1 && response.status >= 500\n      ? fetchRetry(url, attempts - 1, options)\n      : response\n  })\n}\n\ninterface FetchDataOutput {\n  dataHref: string\n  json: Record<string, any> | null\n  response: Response\n  text: string\n  cacheKey: string\n}\n\ninterface FetchNextDataParams {\n  dataHref: string\n  isServerRender: boolean\n  parseJSON: boolean | undefined\n  hasMiddleware?: boolean\n  inflightCache: NextDataCache\n  persistCache: boolean\n  isPrefetch: boolean\n  isBackground?: boolean\n  unstable_skipClientCache?: boolean\n}\n\nfunction tryToParseAsJSON(text: string) {\n  try {\n    return JSON.parse(text)\n  } catch (error) {\n    return null\n  }\n}\n\nfunction fetchNextData({\n  dataHref,\n  inflightCache,\n  isPrefetch,\n  hasMiddleware,\n  isServerRender,\n  parseJSON,\n  persistCache,\n  isBackground,\n  unstable_skipClientCache,\n}: FetchNextDataParams): Promise<FetchDataOutput> {\n  const { href: cacheKey } = new URL(dataHref, window.location.href)\n  const getData = (params?: { method?: 'HEAD' | 'GET' }) =>\n    fetchRetry(dataHref, isServerRender ? 3 : 1, {\n      headers: Object.assign(\n        {} as HeadersInit,\n        isPrefetch ? { purpose: 'prefetch' } : {},\n        isPrefetch && hasMiddleware ? { 'x-middleware-prefetch': '1' } : {},\n        process.env.NEXT_DEPLOYMENT_ID\n          ? { 'x-deployment-id': process.env.NEXT_DEPLOYMENT_ID }\n          : {}\n      ),\n      method: params?.method ?? 'GET',\n    })\n      .then((response) => {\n        if (response.ok && params?.method === 'HEAD') {\n          return { dataHref, response, text: '', json: {}, cacheKey }\n        }\n\n        return response.text().then((text) => {\n          if (!response.ok) {\n            /**\n             * When the data response is a redirect because of a middleware\n             * we do not consider it an error. The headers must bring the\n             * mapped location.\n             * TODO: Change the status code in the handler.\n             */\n            if (\n              hasMiddleware &&\n              [301, 302, 307, 308].includes(response.status)\n            ) {\n              return { dataHref, response, text, json: {}, cacheKey }\n            }\n\n            if (response.status === 404) {\n              if (tryToParseAsJSON(text)?.notFound) {\n                return {\n                  dataHref,\n                  json: { notFound: SSG_DATA_NOT_FOUND },\n                  response,\n                  text,\n                  cacheKey,\n                }\n              }\n            }\n\n            const error = new Error(`Failed to load static props`)\n\n            /**\n             * We should only trigger a server-side transition if this was\n             * caused on a client-side transition. Otherwise, we'd get into\n             * an infinite loop.\n             */\n            if (!isServerRender) {\n              markAssetError(error)\n            }\n\n            throw error\n          }\n\n          return {\n            dataHref,\n            json: parseJSON ? tryToParseAsJSON(text) : null,\n            response,\n            text,\n            cacheKey,\n          }\n        })\n      })\n      .then((data) => {\n        if (\n          !persistCache ||\n          process.env.NODE_ENV !== 'production' ||\n          data.response.headers.get('x-middleware-cache') === 'no-cache'\n        ) {\n          delete inflightCache[cacheKey]\n        }\n        return data\n      })\n      .catch((err) => {\n        if (!unstable_skipClientCache) {\n          delete inflightCache[cacheKey]\n        }\n        if (\n          // chrome\n          err.message === 'Failed to fetch' ||\n          // firefox\n          err.message === 'NetworkError when attempting to fetch resource.' ||\n          // safari\n          err.message === 'Load failed'\n        ) {\n          markAssetError(err)\n        }\n        throw err\n      })\n\n  // when skipping client cache we wait to update\n  // inflight cache until successful data response\n  // this allows racing click event with fetching newer data\n  // without blocking navigation when stale data is available\n  if (unstable_skipClientCache && persistCache) {\n    return getData({}).then((data) => {\n      if (data.response.headers.get('x-middleware-cache') !== 'no-cache') {\n        // only update cache if not marked as no-cache\n        inflightCache[cacheKey] = Promise.resolve(data)\n      }\n\n      return data\n    })\n  }\n\n  if (inflightCache[cacheKey] !== undefined) {\n    return inflightCache[cacheKey]\n  }\n  return (inflightCache[cacheKey] = getData(\n    isBackground ? { method: 'HEAD' } : {}\n  ))\n}\n\ninterface NextDataCache {\n  [asPath: string]: Promise<FetchDataOutput>\n}\n\nexport function createKey() {\n  return Math.random().toString(36).slice(2, 10)\n}\n\nfunction handleHardNavigation({\n  url,\n  router,\n}: {\n  url: string\n  router: Router\n}) {\n  // ensure we don't trigger a hard navigation to the same\n  // URL as this can end up with an infinite refresh\n  if (url === addBasePath(addLocale(router.asPath, router.locale))) {\n    throw new Error(\n      `Invariant: attempted to hard navigate to the same URL ${url} ${location.href}`\n    )\n  }\n  window.location.href = url\n}\n\nconst getCancelledHandler = ({\n  route,\n  router,\n}: {\n  route: string\n  router: Router\n}) => {\n  let cancelled = false\n  const cancel = (router.clc = () => {\n    cancelled = true\n  })\n\n  const handleCancelled = () => {\n    if (cancelled) {\n      const error: any = new Error(\n        `Abort fetching component for route: \"${route}\"`\n      )\n      error.cancelled = true\n      throw error\n    }\n\n    if (cancel === router.clc) {\n      router.clc = null\n    }\n  }\n  return handleCancelled\n}\n\nexport default class Router implements BaseRouter {\n  basePath: string\n\n  /**\n   * Map of all components loaded in `Router`\n   */\n  components: { [pathname: string]: PrivateRouteInfo }\n  // Server Data Cache (full data requests)\n  sdc: NextDataCache = {}\n  // Server Background Cache (HEAD requests)\n  sbc: NextDataCache = {}\n\n  sub: Subscription\n  clc: ComponentLoadCancel\n  pageLoader: PageLoader\n  _bps: BeforePopStateCallback | undefined\n  events: MittEmitter<RouterEvent>\n  _wrapApp: (App: AppComponent) => any\n  isSsr: boolean\n  _inFlightRoute?: string | undefined\n  _shallow?: boolean | undefined\n  locales?: readonly string[] | undefined\n  defaultLocale?: string | undefined\n  domainLocales?: readonly DomainLocale[] | undefined\n  isReady: boolean\n  isLocaleDomain: boolean\n  isFirstPopStateEvent = true\n  _initialMatchesMiddlewarePromise: Promise<boolean>\n  // static entries filter\n  _bfl_s?: import('../../lib/bloom-filter').BloomFilter\n  // dynamic entires filter\n  _bfl_d?: import('../../lib/bloom-filter').BloomFilter\n\n  private state: Readonly<{\n    route: string\n    pathname: string\n    query: ParsedUrlQuery\n    asPath: string\n    locale: string | undefined\n    isFallback: boolean\n    isPreview: boolean\n  }>\n\n  private _key: string = createKey()\n\n  static events: MittEmitter<RouterEvent> = mitt()\n\n  constructor(\n    pathname: string,\n    query: ParsedUrlQuery,\n    as: string,\n    {\n      initialProps,\n      pageLoader,\n      App,\n      wrapApp,\n      Component,\n      err,\n      subscription,\n      isFallback,\n      locale,\n      locales,\n      defaultLocale,\n      domainLocales,\n      isPreview,\n    }: {\n      subscription: Subscription\n      initialProps: any\n      pageLoader: any\n      Component: ComponentType\n      App: AppComponent\n      wrapApp: (WrapAppComponent: AppComponent) => any\n      err?: Error\n      isFallback: boolean\n      locale?: string\n      locales?: readonly string[]\n      defaultLocale?: string\n      domainLocales?: readonly DomainLocale[]\n      isPreview?: boolean\n    }\n  ) {\n    // represents the current component key\n    const route = removeTrailingSlash(pathname)\n\n    // set up the component cache (by route keys)\n    this.components = {}\n    // We should not keep the cache, if there's an error\n    // Otherwise, this cause issues when when going back and\n    // come again to the errored page.\n    if (pathname !== '/_error') {\n      this.components[route] = {\n        Component,\n        initial: true,\n        props: initialProps,\n        err,\n        __N_SSG: initialProps && initialProps.__N_SSG,\n        __N_SSP: initialProps && initialProps.__N_SSP,\n      }\n    }\n\n    this.components['/_app'] = {\n      Component: App as ComponentType,\n      styleSheets: [\n        /* /_app does not need its stylesheets managed */\n      ],\n    }\n\n    // Backwards compat for Router.router.events\n    // TODO: Should be remove the following major version as it was never documented\n    this.events = Router.events\n\n    this.pageLoader = pageLoader\n    // if auto prerendered and dynamic route wait to update asPath\n    // until after mount to prevent hydration mismatch\n    const autoExportDynamic =\n      isDynamicRoute(pathname) && self.__NEXT_DATA__.autoExport\n\n    this.basePath = process.env.__NEXT_ROUTER_BASEPATH || ''\n    this.sub = subscription\n    this.clc = null\n    this._wrapApp = wrapApp\n    // make sure to ignore extra popState in safari on navigating\n    // back from external site\n    this.isSsr = true\n    this.isLocaleDomain = false\n    this.isReady = !!(\n      self.__NEXT_DATA__.gssp ||\n      self.__NEXT_DATA__.gip ||\n      self.__NEXT_DATA__.isExperimentalCompile ||\n      (self.__NEXT_DATA__.appGip && !self.__NEXT_DATA__.gsp) ||\n      (!autoExportDynamic &&\n        !self.location.search &&\n        !process.env.__NEXT_HAS_REWRITES)\n    )\n\n    if (process.env.__NEXT_I18N_SUPPORT) {\n      this.locales = locales\n      this.defaultLocale = defaultLocale\n      this.domainLocales = domainLocales\n      this.isLocaleDomain = !!detectDomainLocale(\n        domainLocales,\n        self.location.hostname\n      )\n    }\n\n    this.state = {\n      route,\n      pathname,\n      query,\n      asPath: autoExportDynamic ? pathname : as,\n      isPreview: !!isPreview,\n      locale: process.env.__NEXT_I18N_SUPPORT ? locale : undefined,\n      isFallback,\n    }\n\n    this._initialMatchesMiddlewarePromise = Promise.resolve(false)\n\n    if (typeof window !== 'undefined') {\n      // make sure \"as\" doesn't start with double slashes or else it can\n      // throw an error as it's considered invalid\n      if (!as.startsWith('//')) {\n        // in order for `e.state` to work on the `onpopstate` event\n        // we have to register the initial route upon initialization\n        const options: TransitionOptions = { locale }\n        const asPath = getURL()\n\n        this._initialMatchesMiddlewarePromise = matchesMiddleware({\n          router: this,\n          locale,\n          asPath,\n        }).then((matches) => {\n          // if middleware matches we leave resolving to the change function\n          // as the server needs to resolve for correct priority\n          ;(options as any)._shouldResolveHref = as !== pathname\n\n          this.changeState(\n            'replaceState',\n            matches\n              ? asPath\n              : formatWithValidation({\n                  pathname: addBasePath(pathname),\n                  query,\n                }),\n            asPath,\n            options\n          )\n          return matches\n        })\n      }\n\n      window.addEventListener('popstate', this.onPopState)\n\n      // enable custom scroll restoration handling when available\n      // otherwise fallback to browser's default handling\n      if (process.env.__NEXT_SCROLL_RESTORATION) {\n        if (manualScrollRestoration) {\n          window.history.scrollRestoration = 'manual'\n        }\n      }\n    }\n  }\n\n  onPopState = (e: PopStateEvent): void => {\n    const { isFirstPopStateEvent } = this\n    this.isFirstPopStateEvent = false\n\n    const state = e.state as HistoryState\n\n    if (!state) {\n      // We get state as undefined for two reasons.\n      //  1. With older safari (< 8) and older chrome (< 34)\n      //  2. When the URL changed with #\n      //\n      // In the both cases, we don't need to proceed and change the route.\n      // (as it's already changed)\n      // But we can simply replace the state with the new changes.\n      // Actually, for (1) we don't need to nothing. But it's hard to detect that event.\n      // So, doing the following for (1) does no harm.\n      const { pathname, query } = this\n      this.changeState(\n        'replaceState',\n        formatWithValidation({ pathname: addBasePath(pathname), query }),\n        getURL()\n      )\n      return\n    }\n\n    // __NA is used to identify if the history entry can be handled by the app-router.\n    if (state.__NA) {\n      window.location.reload()\n      return\n    }\n\n    if (!state.__N) {\n      return\n    }\n\n    // Safari fires popstateevent when reopening the browser.\n    if (\n      isFirstPopStateEvent &&\n      this.locale === state.options.locale &&\n      state.as === this.asPath\n    ) {\n      return\n    }\n\n    let forcedScroll: { x: number; y: number } | undefined\n    const { url, as, options, key } = state\n    if (process.env.__NEXT_SCROLL_RESTORATION) {\n      if (manualScrollRestoration) {\n        if (this._key !== key) {\n          // Snapshot current scroll position:\n          try {\n            sessionStorage.setItem(\n              '__next_scroll_' + this._key,\n              JSON.stringify({ x: self.pageXOffset, y: self.pageYOffset })\n            )\n          } catch {}\n\n          // Restore old scroll position:\n          try {\n            const v = sessionStorage.getItem('__next_scroll_' + key)\n            forcedScroll = JSON.parse(v!)\n          } catch {\n            forcedScroll = { x: 0, y: 0 }\n          }\n        }\n      }\n    }\n    this._key = key\n\n    const { pathname } = parseRelativeUrl(url)\n\n    // Make sure we don't re-render on initial load,\n    // can be caused by navigating back from an external site\n    if (\n      this.isSsr &&\n      as === addBasePath(this.asPath) &&\n      pathname === addBasePath(this.pathname)\n    ) {\n      return\n    }\n\n    // If the downstream application returns falsy, return.\n    // They will then be responsible for handling the event.\n    if (this._bps && !this._bps(state)) {\n      return\n    }\n\n    this.change(\n      'replaceState',\n      url,\n      as,\n      Object.assign<{}, TransitionOptions, TransitionOptions>({}, options, {\n        shallow: options.shallow && this._shallow,\n        locale: options.locale || this.defaultLocale,\n        // @ts-ignore internal value not exposed on types\n        _h: 0,\n      }),\n      forcedScroll\n    )\n  }\n\n  reload(): void {\n    window.location.reload()\n  }\n\n  /**\n   * Go back in history\n   */\n  back() {\n    window.history.back()\n  }\n\n  /**\n   * Go forward in history\n   */\n  forward() {\n    window.history.forward()\n  }\n\n  /**\n   * Performs a `pushState` with arguments\n   * @param url of the route\n   * @param as masks `url` for the browser\n   * @param options object you can define `shallow` and other options\n   */\n  push(url: Url, as?: Url, options: TransitionOptions = {}) {\n    if (process.env.__NEXT_SCROLL_RESTORATION) {\n      // TODO: remove in the future when we update history before route change\n      // is complete, as the popstate event should handle this capture.\n      if (manualScrollRestoration) {\n        try {\n          // Snapshot scroll position right before navigating to a new page:\n          sessionStorage.setItem(\n            '__next_scroll_' + this._key,\n            JSON.stringify({ x: self.pageXOffset, y: self.pageYOffset })\n          )\n        } catch {}\n      }\n    }\n    ;({ url, as } = prepareUrlAs(this, url, as))\n    return this.change('pushState', url, as, options)\n  }\n\n  /**\n   * Performs a `replaceState` with arguments\n   * @param url of the route\n   * @param as masks `url` for the browser\n   * @param options object you can define `shallow` and other options\n   */\n  replace(url: Url, as?: Url, options: TransitionOptions = {}) {\n    ;({ url, as } = prepareUrlAs(this, url, as))\n    return this.change('replaceState', url, as, options)\n  }\n\n  async _bfl(\n    as: string,\n    resolvedAs?: string,\n    locale?: string | false,\n    skipNavigate?: boolean\n  ) {\n    if (process.env.__NEXT_CLIENT_ROUTER_FILTER_ENABLED) {\n      if (!this._bfl_s && !this._bfl_d) {\n        const { BloomFilter } =\n          require('../../lib/bloom-filter') as typeof import('../../lib/bloom-filter')\n\n        type Filter = ReturnType<\n          import('../../lib/bloom-filter').BloomFilter['export']\n        >\n        let staticFilterData: Filter | undefined\n        let dynamicFilterData: Filter | undefined\n\n        try {\n          ;({\n            __routerFilterStatic: staticFilterData,\n            __routerFilterDynamic: dynamicFilterData,\n          } = (await getClientBuildManifest()) as any as {\n            __routerFilterStatic?: Filter\n            __routerFilterDynamic?: Filter\n          })\n        } catch (err) {\n          // failed to load build manifest hard navigate\n          // to be safe\n          console.error(err)\n          if (skipNavigate) {\n            return true\n          }\n          handleHardNavigation({\n            url: addBasePath(\n              addLocale(as, locale || this.locale, this.defaultLocale)\n            ),\n            router: this,\n          })\n          return new Promise(() => {})\n        }\n\n        const routerFilterSValue: Filter | false = process.env\n          .__NEXT_CLIENT_ROUTER_S_FILTER as any\n\n        if (!staticFilterData && routerFilterSValue) {\n          staticFilterData = routerFilterSValue ? routerFilterSValue : undefined\n        }\n\n        const routerFilterDValue: Filter | false = process.env\n          .__NEXT_CLIENT_ROUTER_D_FILTER as any\n\n        if (!dynamicFilterData && routerFilterDValue) {\n          dynamicFilterData = routerFilterDValue\n            ? routerFilterDValue\n            : undefined\n        }\n\n        if (staticFilterData?.numHashes) {\n          this._bfl_s = new BloomFilter(\n            staticFilterData.numItems,\n            staticFilterData.errorRate\n          )\n          this._bfl_s.import(staticFilterData)\n        }\n\n        if (dynamicFilterData?.numHashes) {\n          this._bfl_d = new BloomFilter(\n            dynamicFilterData.numItems,\n            dynamicFilterData.errorRate\n          )\n          this._bfl_d.import(dynamicFilterData)\n        }\n      }\n\n      let matchesBflStatic = false\n      let matchesBflDynamic = false\n      const pathsToCheck: Array<{ as?: string; allowMatchCurrent?: boolean }> =\n        [{ as }, { as: resolvedAs }]\n\n      for (const { as: curAs, allowMatchCurrent } of pathsToCheck) {\n        if (curAs) {\n          const asNoSlash = removeTrailingSlash(\n            new URL(curAs, 'http://n').pathname\n          )\n          const asNoSlashLocale = addBasePath(\n            addLocale(asNoSlash, locale || this.locale)\n          )\n\n          if (\n            allowMatchCurrent ||\n            asNoSlash !==\n              removeTrailingSlash(new URL(this.asPath, 'http://n').pathname)\n          ) {\n            matchesBflStatic =\n              matchesBflStatic ||\n              !!this._bfl_s?.contains(asNoSlash) ||\n              !!this._bfl_s?.contains(asNoSlashLocale)\n\n            for (const normalizedAS of [asNoSlash, asNoSlashLocale]) {\n              // if any sub-path of as matches a dynamic filter path\n              // it should be hard navigated\n              const curAsParts = normalizedAS.split('/')\n              for (\n                let i = 0;\n                !matchesBflDynamic && i < curAsParts.length + 1;\n                i++\n              ) {\n                const currentPart = curAsParts.slice(0, i).join('/')\n                if (currentPart && this._bfl_d?.contains(currentPart)) {\n                  matchesBflDynamic = true\n                  break\n                }\n              }\n            }\n\n            // if the client router filter is matched then we trigger\n            // a hard navigation\n            if (matchesBflStatic || matchesBflDynamic) {\n              if (skipNavigate) {\n                return true\n              }\n              handleHardNavigation({\n                url: addBasePath(\n                  addLocale(as, locale || this.locale, this.defaultLocale)\n                ),\n                router: this,\n              })\n              return new Promise(() => {})\n            }\n          }\n        }\n      }\n    }\n    return false\n  }\n\n  private async change(\n    method: HistoryMethod,\n    url: string,\n    as: string,\n    options: TransitionOptions,\n    forcedScroll?: { x: number; y: number }\n  ): Promise<boolean> {\n    if (!isLocalURL(url)) {\n      handleHardNavigation({ url, router: this })\n      return false\n    }\n    // WARNING: `_h` is an internal option for handing Next.js client-side\n    // hydration. Your app should _never_ use this property. It may change at\n    // any time without notice.\n    const isQueryUpdating = (options as any)._h === 1\n\n    if (!isQueryUpdating && !options.shallow) {\n      await this._bfl(as, undefined, options.locale)\n    }\n\n    let shouldResolveHref =\n      isQueryUpdating ||\n      (options as any)._shouldResolveHref ||\n      parsePath(url).pathname === parsePath(as).pathname\n\n    const nextState = {\n      ...this.state,\n    }\n\n    // for static pages with query params in the URL we delay\n    // marking the router ready until after the query is updated\n    // or a navigation has occurred\n    const readyStateChange = this.isReady !== true\n    this.isReady = true\n    const isSsr = this.isSsr\n\n    if (!isQueryUpdating) {\n      this.isSsr = false\n    }\n\n    // if a route transition is already in progress before\n    // the query updating is triggered ignore query updating\n    if (isQueryUpdating && this.clc) {\n      return false\n    }\n\n    const prevLocale = nextState.locale\n\n    if (process.env.__NEXT_I18N_SUPPORT) {\n      nextState.locale =\n        options.locale === false\n          ? this.defaultLocale\n          : options.locale || nextState.locale\n\n      if (typeof options.locale === 'undefined') {\n        options.locale = nextState.locale\n      }\n\n      const parsedAs = parseRelativeUrl(\n        hasBasePath(as) ? removeBasePath(as) : as\n      )\n      const localePathResult = normalizeLocalePath(\n        parsedAs.pathname,\n        this.locales\n      )\n\n      if (localePathResult.detectedLocale) {\n        nextState.locale = localePathResult.detectedLocale\n        parsedAs.pathname = addBasePath(parsedAs.pathname)\n        as = formatWithValidation(parsedAs)\n        url = addBasePath(\n          normalizeLocalePath(\n            hasBasePath(url) ? removeBasePath(url) : url,\n            this.locales\n          ).pathname\n        )\n      }\n      let didNavigate = false\n\n      // we need to wrap this in the env check again since regenerator runtime\n      // moves this on its own due to the return\n      if (process.env.__NEXT_I18N_SUPPORT) {\n        // if the locale isn't configured hard navigate to show 404 page\n        if (!this.locales?.includes(nextState.locale!)) {\n          parsedAs.pathname = addLocale(parsedAs.pathname, nextState.locale)\n          handleHardNavigation({\n            url: formatWithValidation(parsedAs),\n            router: this,\n          })\n          // this was previously a return but was removed in favor\n          // of better dead code elimination with regenerator runtime\n          didNavigate = true\n        }\n      }\n\n      const detectedDomain = detectDomainLocale(\n        this.domainLocales,\n        undefined,\n        nextState.locale\n      )\n\n      // we need to wrap this in the env check again since regenerator runtime\n      // moves this on its own due to the return\n      if (process.env.__NEXT_I18N_SUPPORT) {\n        // if we are navigating to a domain locale ensure we redirect to the\n        // correct domain\n        if (\n          !didNavigate &&\n          detectedDomain &&\n          this.isLocaleDomain &&\n          self.location.hostname !== detectedDomain.domain\n        ) {\n          const asNoBasePath = removeBasePath(as)\n          handleHardNavigation({\n            url: `http${detectedDomain.http ? '' : 's'}://${\n              detectedDomain.domain\n            }${addBasePath(\n              `${\n                nextState.locale === detectedDomain.defaultLocale\n                  ? ''\n                  : `/${nextState.locale}`\n              }${asNoBasePath === '/' ? '' : asNoBasePath}` || '/'\n            )}`,\n            router: this,\n          })\n          // this was previously a return but was removed in favor\n          // of better dead code elimination with regenerator runtime\n          didNavigate = true\n        }\n      }\n\n      if (didNavigate) {\n        return new Promise(() => {})\n      }\n    }\n\n    // marking route changes as a navigation start entry\n    if (ST) {\n      performance.mark('routeChange')\n    }\n\n    const { shallow = false, scroll = true } = options\n    const routeProps = { shallow }\n\n    if (this._inFlightRoute && this.clc) {\n      if (!isSsr) {\n        Router.events.emit(\n          'routeChangeError',\n          buildCancellationError(),\n          this._inFlightRoute,\n          routeProps\n        )\n      }\n      this.clc()\n      this.clc = null\n    }\n\n    as = addBasePath(\n      addLocale(\n        hasBasePath(as) ? removeBasePath(as) : as,\n        options.locale,\n        this.defaultLocale\n      )\n    )\n    const cleanedAs = removeLocale(\n      hasBasePath(as) ? removeBasePath(as) : as,\n      nextState.locale\n    )\n    this._inFlightRoute = as\n\n    const localeChange = prevLocale !== nextState.locale\n\n    // If the url change is only related to a hash change\n    // We should not proceed. We should only change the state.\n\n    if (!isQueryUpdating && this.onlyAHashChange(cleanedAs) && !localeChange) {\n      nextState.asPath = cleanedAs\n      Router.events.emit('hashChangeStart', as, routeProps)\n      // TODO: do we need the resolved href when only a hash change?\n      this.changeState(method, url, as, {\n        ...options,\n        scroll: false,\n      })\n      if (scroll) {\n        this.scrollToHash(cleanedAs)\n      }\n      try {\n        await this.set(nextState, this.components[nextState.route], null)\n      } catch (err) {\n        if (isError(err) && err.cancelled) {\n          Router.events.emit('routeChangeError', err, cleanedAs, routeProps)\n        }\n        throw err\n      }\n\n      Router.events.emit('hashChangeComplete', as, routeProps)\n      return true\n    }\n\n    let parsed = parseRelativeUrl(url)\n    let { pathname, query } = parsed\n\n    // The build manifest needs to be loaded before auto-static dynamic pages\n    // get their query parameters to allow ensuring they can be parsed properly\n    // when rewritten to\n    let pages: string[], rewrites: any\n    try {\n      ;[pages, { __rewrites: rewrites }] = await Promise.all([\n        this.pageLoader.getPageList(),\n        getClientBuildManifest(),\n        this.pageLoader.getMiddleware(),\n      ])\n    } catch (err) {\n      // If we fail to resolve the page list or client-build manifest, we must\n      // do a server-side transition:\n      handleHardNavigation({ url: as, router: this })\n      return false\n    }\n\n    // If asked to change the current URL we should reload the current page\n    // (not location.reload() but reload getInitialProps and other Next.js stuffs)\n    // We also need to set the method = replaceState always\n    // as this should not go into the history (That's how browsers work)\n    // We should compare the new asPath to the current asPath, not the url\n    if (!this.urlIsNew(cleanedAs) && !localeChange) {\n      method = 'replaceState'\n    }\n\n    // we need to resolve the as value using rewrites for dynamic SSG\n    // pages to allow building the data URL correctly\n    let resolvedAs = as\n\n    // url and as should always be prefixed with basePath by this\n    // point by either next/link or router.push/replace so strip the\n    // basePath from the pathname to match the pages dir 1-to-1\n    pathname = pathname\n      ? removeTrailingSlash(removeBasePath(pathname))\n      : pathname\n\n    let route = removeTrailingSlash(pathname)\n    const parsedAsPathname = as.startsWith('/') && parseRelativeUrl(as).pathname\n\n    // if we detected the path as app route during prefetching\n    // trigger hard navigation\n    if ((this.components[pathname] as any)?.__appRouter) {\n      handleHardNavigation({ url: as, router: this })\n      return new Promise(() => {})\n    }\n\n    const isMiddlewareRewrite = !!(\n      parsedAsPathname &&\n      route !== parsedAsPathname &&\n      (!isDynamicRoute(route) ||\n        !getRouteMatcher(getRouteRegex(route))(parsedAsPathname))\n    )\n\n    // we don't attempt resolve asPath when we need to execute\n    // middleware as the resolving will occur server-side\n    const isMiddlewareMatch =\n      !options.shallow &&\n      (await matchesMiddleware({\n        asPath: as,\n        locale: nextState.locale,\n        router: this,\n      }))\n\n    if (isQueryUpdating && isMiddlewareMatch) {\n      shouldResolveHref = false\n    }\n\n    if (shouldResolveHref && pathname !== '/_error') {\n      ;(options as any)._shouldResolveHref = true\n\n      if (process.env.__NEXT_HAS_REWRITES && as.startsWith('/')) {\n        const rewritesResult = resolveRewrites(\n          addBasePath(addLocale(cleanedAs, nextState.locale), true),\n          pages,\n          rewrites,\n          query,\n          (p: string) => resolveDynamicRoute(p, pages),\n          this.locales\n        )\n\n        if (rewritesResult.externalDest) {\n          handleHardNavigation({ url: as, router: this })\n          return true\n        }\n        if (!isMiddlewareMatch) {\n          resolvedAs = rewritesResult.asPath\n        }\n\n        if (rewritesResult.matchedPage && rewritesResult.resolvedHref) {\n          // if this directly matches a page we need to update the href to\n          // allow the correct page chunk to be loaded\n          pathname = rewritesResult.resolvedHref\n          parsed.pathname = addBasePath(pathname)\n\n          if (!isMiddlewareMatch) {\n            url = formatWithValidation(parsed)\n          }\n        }\n      } else {\n        parsed.pathname = resolveDynamicRoute(pathname, pages)\n\n        if (parsed.pathname !== pathname) {\n          pathname = parsed.pathname\n          parsed.pathname = addBasePath(pathname)\n\n          if (!isMiddlewareMatch) {\n            url = formatWithValidation(parsed)\n          }\n        }\n      }\n    }\n\n    if (!isLocalURL(as)) {\n      if (process.env.NODE_ENV !== 'production') {\n        throw new Error(\n          `Invalid href: \"${url}\" and as: \"${as}\", received relative href and external as` +\n            `\\nSee more info: https://nextjs.org/docs/messages/invalid-relative-url-external-as`\n        )\n      }\n      handleHardNavigation({ url: as, router: this })\n      return false\n    }\n\n    resolvedAs = removeLocale(removeBasePath(resolvedAs), nextState.locale)\n\n    route = removeTrailingSlash(pathname)\n    let routeMatch: Params | false = false\n\n    if (isDynamicRoute(route)) {\n      const parsedAs = parseRelativeUrl(resolvedAs)\n      const asPathname = parsedAs.pathname\n\n      const routeRegex = getRouteRegex(route)\n      routeMatch = getRouteMatcher(routeRegex)(asPathname)\n      const shouldInterpolate = route === asPathname\n      const interpolatedAs = shouldInterpolate\n        ? interpolateAs(route, asPathname, query)\n        : ({} as { result: undefined; params: undefined })\n\n      if (!routeMatch || (shouldInterpolate && !interpolatedAs.result)) {\n        const missingParams = Object.keys(routeRegex.groups).filter(\n          (param) => !query[param] && !routeRegex.groups[param].optional\n        )\n\n        if (missingParams.length > 0 && !isMiddlewareMatch) {\n          if (process.env.NODE_ENV !== 'production') {\n            console.warn(\n              `${\n                shouldInterpolate\n                  ? `Interpolating href`\n                  : `Mismatching \\`as\\` and \\`href\\``\n              } failed to manually provide ` +\n                `the params: ${missingParams.join(\n                  ', '\n                )} in the \\`href\\`'s \\`query\\``\n            )\n          }\n\n          throw new Error(\n            (shouldInterpolate\n              ? `The provided \\`href\\` (${url}) value is missing query values (${missingParams.join(\n                  ', '\n                )}) to be interpolated properly. `\n              : `The provided \\`as\\` value (${asPathname}) is incompatible with the \\`href\\` value (${route}). `) +\n              `Read more: https://nextjs.org/docs/messages/${\n                shouldInterpolate\n                  ? 'href-interpolation-failed'\n                  : 'incompatible-href-as'\n              }`\n          )\n        }\n      } else if (shouldInterpolate) {\n        as = formatWithValidation(\n          Object.assign({}, parsedAs, {\n            pathname: interpolatedAs.result,\n            query: omit(query, interpolatedAs.params!),\n          })\n        )\n      } else {\n        // Merge params into `query`, overwriting any specified in search\n        Object.assign(query, routeMatch)\n      }\n    }\n\n    if (!isQueryUpdating) {\n      Router.events.emit('routeChangeStart', as, routeProps)\n    }\n\n    const isErrorRoute = this.pathname === '/404' || this.pathname === '/_error'\n\n    try {\n      let routeInfo = await this.getRouteInfo({\n        route,\n        pathname,\n        query,\n        as,\n        resolvedAs,\n        routeProps,\n        locale: nextState.locale,\n        isPreview: nextState.isPreview,\n        hasMiddleware: isMiddlewareMatch,\n        unstable_skipClientCache: options.unstable_skipClientCache,\n        isQueryUpdating: isQueryUpdating && !this.isFallback,\n        isMiddlewareRewrite,\n      })\n\n      if (!isQueryUpdating && !options.shallow) {\n        await this._bfl(\n          as,\n          'resolvedAs' in routeInfo ? routeInfo.resolvedAs : undefined,\n          nextState.locale\n        )\n      }\n\n      if ('route' in routeInfo && isMiddlewareMatch) {\n        pathname = routeInfo.route || route\n        route = pathname\n\n        if (!routeProps.shallow) {\n          query = Object.assign({}, routeInfo.query || {}, query)\n        }\n\n        const cleanedParsedPathname = hasBasePath(parsed.pathname)\n          ? removeBasePath(parsed.pathname)\n          : parsed.pathname\n\n        if (routeMatch && pathname !== cleanedParsedPathname) {\n          Object.keys(routeMatch).forEach((key) => {\n            if (routeMatch && query[key] === routeMatch[key]) {\n              delete query[key]\n            }\n          })\n        }\n\n        if (isDynamicRoute(pathname)) {\n          const prefixedAs =\n            !routeProps.shallow && routeInfo.resolvedAs\n              ? routeInfo.resolvedAs\n              : addBasePath(\n                  addLocale(\n                    new URL(as, location.href).pathname,\n                    nextState.locale\n                  ),\n                  true\n                )\n\n          let rewriteAs = prefixedAs\n\n          if (hasBasePath(rewriteAs)) {\n            rewriteAs = removeBasePath(rewriteAs)\n          }\n\n          if (process.env.__NEXT_I18N_SUPPORT) {\n            const localeResult = normalizeLocalePath(rewriteAs, this.locales)\n            nextState.locale = localeResult.detectedLocale || nextState.locale\n            rewriteAs = localeResult.pathname\n          }\n          const routeRegex = getRouteRegex(pathname)\n          const curRouteMatch = getRouteMatcher(routeRegex)(\n            new URL(rewriteAs, location.href).pathname\n          )\n\n          if (curRouteMatch) {\n            Object.assign(query, curRouteMatch)\n          }\n        }\n      }\n\n      // If the routeInfo brings a redirect we simply apply it.\n      if ('type' in routeInfo) {\n        if (routeInfo.type === 'redirect-internal') {\n          return this.change(method, routeInfo.newUrl, routeInfo.newAs, options)\n        } else {\n          handleHardNavigation({ url: routeInfo.destination, router: this })\n          return new Promise(() => {})\n        }\n      }\n\n      const component: any = routeInfo.Component\n      if (component && component.unstable_scriptLoader) {\n        const scripts = [].concat(component.unstable_scriptLoader())\n\n        scripts.forEach((script: any) => {\n          handleClientScriptLoad(script.props)\n        })\n      }\n\n      // handle redirect on client-transition\n      if ((routeInfo.__N_SSG || routeInfo.__N_SSP) && routeInfo.props) {\n        if (\n          routeInfo.props.pageProps &&\n          routeInfo.props.pageProps.__N_REDIRECT\n        ) {\n          // Use the destination from redirect without adding locale\n          options.locale = false\n\n          const destination = routeInfo.props.pageProps.__N_REDIRECT\n\n          // check if destination is internal (resolves to a page) and attempt\n          // client-navigation if it is falling back to hard navigation if\n          // it's not\n          if (\n            destination.startsWith('/') &&\n            routeInfo.props.pageProps.__N_REDIRECT_BASE_PATH !== false\n          ) {\n            const parsedHref = parseRelativeUrl(destination)\n            parsedHref.pathname = resolveDynamicRoute(\n              parsedHref.pathname,\n              pages\n            )\n\n            const { url: newUrl, as: newAs } = prepareUrlAs(\n              this,\n              destination,\n              destination\n            )\n            return this.change(method, newUrl, newAs, options)\n          }\n          handleHardNavigation({ url: destination, router: this })\n          return new Promise(() => {})\n        }\n\n        nextState.isPreview = !!routeInfo.props.__N_PREVIEW\n\n        // handle SSG data 404\n        if (routeInfo.props.notFound === SSG_DATA_NOT_FOUND) {\n          let notFoundRoute\n\n          try {\n            await this.fetchComponent('/404')\n            notFoundRoute = '/404'\n          } catch (_) {\n            notFoundRoute = '/_error'\n          }\n\n          routeInfo = await this.getRouteInfo({\n            route: notFoundRoute,\n            pathname: notFoundRoute,\n            query,\n            as,\n            resolvedAs,\n            routeProps: { shallow: false },\n            locale: nextState.locale,\n            isPreview: nextState.isPreview,\n            isNotFound: true,\n          })\n\n          if ('type' in routeInfo) {\n            throw new Error(`Unexpected middleware effect on /404`)\n          }\n        }\n      }\n\n      if (\n        isQueryUpdating &&\n        this.pathname === '/_error' &&\n        self.__NEXT_DATA__.props?.pageProps?.statusCode === 500 &&\n        routeInfo.props?.pageProps\n      ) {\n        // ensure statusCode is still correct for static 500 page\n        // when updating query information\n        routeInfo.props.pageProps.statusCode = 500\n      }\n\n      // shallow routing is only allowed for same page URL changes.\n      const isValidShallowRoute =\n        options.shallow && nextState.route === (routeInfo.route ?? route)\n\n      const shouldScroll =\n        options.scroll ?? (!isQueryUpdating && !isValidShallowRoute)\n      const resetScroll = shouldScroll ? { x: 0, y: 0 } : null\n      const upcomingScrollState = forcedScroll ?? resetScroll\n\n      // the new state that the router gonna set\n      const upcomingRouterState = {\n        ...nextState,\n        route,\n        pathname,\n        query,\n        asPath: cleanedAs,\n        isFallback: false,\n      }\n\n      // When the page being rendered is the 404 page, we should only update the\n      // query parameters. Route changes here might add the basePath when it\n      // wasn't originally present. This is also why this block is before the\n      // below `changeState` call which updates the browser's history (changing\n      // the URL).\n      if (isQueryUpdating && isErrorRoute) {\n        routeInfo = await this.getRouteInfo({\n          route: this.pathname,\n          pathname: this.pathname,\n          query,\n          as,\n          resolvedAs,\n          routeProps: { shallow: false },\n          locale: nextState.locale,\n          isPreview: nextState.isPreview,\n          isQueryUpdating: isQueryUpdating && !this.isFallback,\n        })\n\n        if ('type' in routeInfo) {\n          throw new Error(`Unexpected middleware effect on ${this.pathname}`)\n        }\n\n        if (\n          this.pathname === '/_error' &&\n          self.__NEXT_DATA__.props?.pageProps?.statusCode === 500 &&\n          routeInfo.props?.pageProps\n        ) {\n          // ensure statusCode is still correct for static 500 page\n          // when updating query information\n          routeInfo.props.pageProps.statusCode = 500\n        }\n\n        try {\n          await this.set(upcomingRouterState, routeInfo, upcomingScrollState)\n        } catch (err) {\n          if (isError(err) && err.cancelled) {\n            Router.events.emit('routeChangeError', err, cleanedAs, routeProps)\n          }\n          throw err\n        }\n\n        return true\n      }\n\n      Router.events.emit('beforeHistoryChange', as, routeProps)\n      this.changeState(method, url, as, options)\n\n      // for query updates we can skip it if the state is unchanged and we don't\n      // need to scroll\n      // https://github.com/vercel/next.js/issues/37139\n      const canSkipUpdating =\n        isQueryUpdating &&\n        !upcomingScrollState &&\n        !readyStateChange &&\n        !localeChange &&\n        compareRouterStates(upcomingRouterState, this.state)\n\n      if (!canSkipUpdating) {\n        try {\n          await this.set(upcomingRouterState, routeInfo, upcomingScrollState)\n        } catch (e: any) {\n          if (e.cancelled) routeInfo.error = routeInfo.error || e\n          else throw e\n        }\n\n        if (routeInfo.error) {\n          if (!isQueryUpdating) {\n            Router.events.emit(\n              'routeChangeError',\n              routeInfo.error,\n              cleanedAs,\n              routeProps\n            )\n          }\n\n          throw routeInfo.error\n        }\n\n        if (process.env.__NEXT_I18N_SUPPORT) {\n          if (nextState.locale) {\n            document.documentElement.lang = nextState.locale\n          }\n        }\n\n        if (!isQueryUpdating) {\n          Router.events.emit('routeChangeComplete', as, routeProps)\n        }\n\n        // A hash mark # is the optional last part of a URL\n        const hashRegex = /#.+$/\n        if (shouldScroll && hashRegex.test(as)) {\n          this.scrollToHash(as)\n        }\n      }\n\n      return true\n    } catch (err) {\n      if (isError(err) && err.cancelled) {\n        return false\n      }\n      throw err\n    }\n  }\n\n  changeState(\n    method: HistoryMethod,\n    url: string,\n    as: string,\n    options: TransitionOptions = {}\n  ): void {\n    if (process.env.NODE_ENV !== 'production') {\n      if (typeof window.history === 'undefined') {\n        console.error(`Warning: window.history is not available.`)\n        return\n      }\n\n      if (typeof window.history[method] === 'undefined') {\n        console.error(`Warning: window.history.${method} is not available`)\n        return\n      }\n    }\n\n    if (method !== 'pushState' || getURL() !== as) {\n      this._shallow = options.shallow\n      window.history[method](\n        {\n          url,\n          as,\n          options,\n          __N: true,\n          key: (this._key = method !== 'pushState' ? this._key : createKey()),\n        } as HistoryState,\n        // Most browsers currently ignores this parameter, although they may use it in the future.\n        // Passing the empty string here should be safe against future changes to the method.\n        // https://developer.mozilla.org/docs/Web/API/History/replaceState\n        '',\n        as\n      )\n    }\n  }\n\n  async handleRouteInfoError(\n    err: Error & { code?: any; cancelled?: boolean },\n    pathname: string,\n    query: ParsedUrlQuery,\n    as: string,\n    routeProps: RouteProperties,\n    loadErrorFail?: boolean\n  ): Promise<CompletePrivateRouteInfo> {\n    if (err.cancelled) {\n      // bubble up cancellation errors\n      throw err\n    }\n\n    if (isAssetError(err) || loadErrorFail) {\n      Router.events.emit('routeChangeError', err, as, routeProps)\n\n      // If we can't load the page it could be one of following reasons\n      //  1. Page doesn't exists\n      //  2. Page does exist in a different zone\n      //  3. Internal error while loading the page\n\n      // So, doing a hard reload is the proper way to deal with this.\n      handleHardNavigation({\n        url: as,\n        router: this,\n      })\n\n      // Changing the URL doesn't block executing the current code path.\n      // So let's throw a cancellation error stop the routing logic.\n      throw buildCancellationError()\n    }\n\n    console.error(err)\n\n    try {\n      let props: Record<string, any> | undefined\n      const { page: Component, styleSheets } =\n        await this.fetchComponent('/_error')\n\n      const routeInfo: CompletePrivateRouteInfo = {\n        props,\n        Component,\n        styleSheets,\n        err,\n        error: err,\n      }\n\n      if (!routeInfo.props) {\n        try {\n          routeInfo.props = await this.getInitialProps(Component, {\n            err,\n            pathname,\n            query,\n          } as any)\n        } catch (gipErr) {\n          console.error('Error in error page `getInitialProps`: ', gipErr)\n          routeInfo.props = {}\n        }\n      }\n\n      return routeInfo\n    } catch (routeInfoErr) {\n      return this.handleRouteInfoError(\n        isError(routeInfoErr) ? routeInfoErr : new Error(routeInfoErr + ''),\n        pathname,\n        query,\n        as,\n        routeProps,\n        true\n      )\n    }\n  }\n\n  async getRouteInfo({\n    route: requestedRoute,\n    pathname,\n    query,\n    as,\n    resolvedAs,\n    routeProps,\n    locale,\n    hasMiddleware,\n    isPreview,\n    unstable_skipClientCache,\n    isQueryUpdating,\n    isMiddlewareRewrite,\n    isNotFound,\n  }: {\n    route: string\n    pathname: string\n    query: ParsedUrlQuery\n    as: string\n    resolvedAs: string\n    hasMiddleware?: boolean\n    routeProps: RouteProperties\n    locale: string | undefined\n    isPreview: boolean\n    unstable_skipClientCache?: boolean\n    isQueryUpdating?: boolean\n    isMiddlewareRewrite?: boolean\n    isNotFound?: boolean\n  }) {\n    /**\n     * This `route` binding can change if there's a rewrite\n     * so we keep a reference to the original requested route\n     * so we can store the cache for it and avoid re-requesting every time\n     * for shallow routing purposes.\n     */\n    let route = requestedRoute\n\n    try {\n      let existingInfo: PrivateRouteInfo | undefined = this.components[route]\n      if (routeProps.shallow && existingInfo && this.route === route) {\n        return existingInfo\n      }\n\n      const handleCancelled = getCancelledHandler({ route, router: this })\n\n      if (hasMiddleware) {\n        existingInfo = undefined\n      }\n\n      let cachedRouteInfo =\n        existingInfo &&\n        !('initial' in existingInfo) &&\n        process.env.NODE_ENV !== 'development'\n          ? existingInfo\n          : undefined\n\n      const isBackground = isQueryUpdating\n      const fetchNextDataParams: FetchNextDataParams = {\n        dataHref: this.pageLoader.getDataHref({\n          href: formatWithValidation({ pathname, query }),\n          skipInterpolation: true,\n          asPath: isNotFound ? '/404' : resolvedAs,\n          locale,\n        }),\n        hasMiddleware: true,\n        isServerRender: this.isSsr,\n        parseJSON: true,\n        inflightCache: isBackground ? this.sbc : this.sdc,\n        persistCache: !isPreview,\n        isPrefetch: false,\n        unstable_skipClientCache,\n        isBackground,\n      }\n\n      let data:\n        | WithMiddlewareEffectsOutput\n        | (Pick<WithMiddlewareEffectsOutput, 'json'> &\n            Omit<Partial<WithMiddlewareEffectsOutput>, 'json'>)\n        | null =\n        isQueryUpdating && !isMiddlewareRewrite\n          ? null\n          : await withMiddlewareEffects({\n              fetchData: () => fetchNextData(fetchNextDataParams),\n              asPath: isNotFound ? '/404' : resolvedAs,\n              locale: locale,\n              router: this,\n            }).catch((err) => {\n              // we don't hard error during query updating\n              // as it's un-necessary and doesn't need to be fatal\n              // unless it is a fallback route and the props can't\n              // be loaded\n              if (isQueryUpdating) {\n                return null\n              }\n              throw err\n            })\n\n      // when rendering error routes we don't apply middleware\n      // effects\n      if (data && (pathname === '/_error' || pathname === '/404')) {\n        data.effect = undefined\n      }\n\n      if (isQueryUpdating) {\n        if (!data) {\n          data = { json: self.__NEXT_DATA__.props }\n        } else {\n          data.json = self.__NEXT_DATA__.props\n        }\n      }\n\n      handleCancelled()\n\n      if (\n        data?.effect?.type === 'redirect-internal' ||\n        data?.effect?.type === 'redirect-external'\n      ) {\n        return data.effect\n      }\n\n      if (data?.effect?.type === 'rewrite') {\n        const resolvedRoute = removeTrailingSlash(data.effect.resolvedHref)\n        const pages = await this.pageLoader.getPageList()\n\n        // during query updating the page must match although during\n        // client-transition a redirect that doesn't match a page\n        // can be returned and this should trigger a hard navigation\n        // which is valid for incremental migration\n        if (!isQueryUpdating || pages.includes(resolvedRoute)) {\n          route = resolvedRoute\n          pathname = data.effect.resolvedHref\n          query = { ...query, ...data.effect.parsedAs.query }\n          resolvedAs = removeBasePath(\n            normalizeLocalePath(data.effect.parsedAs.pathname, this.locales)\n              .pathname\n          )\n\n          // Check again the cache with the new destination.\n          existingInfo = this.components[route]\n          if (\n            routeProps.shallow &&\n            existingInfo &&\n            this.route === route &&\n            !hasMiddleware\n          ) {\n            // If we have a match with the current route due to rewrite,\n            // we can copy the existing information to the rewritten one.\n            // Then, we return the information along with the matched route.\n            return { ...existingInfo, route }\n          }\n        }\n      }\n\n      if (isAPIRoute(route)) {\n        handleHardNavigation({ url: as, router: this })\n        return new Promise<never>(() => {})\n      }\n\n      const routeInfo =\n        cachedRouteInfo ||\n        (await this.fetchComponent(route).then<CompletePrivateRouteInfo>(\n          (res) => ({\n            Component: res.page,\n            styleSheets: res.styleSheets,\n            __N_SSG: res.mod.__N_SSG,\n            __N_SSP: res.mod.__N_SSP,\n          })\n        ))\n\n      if (process.env.NODE_ENV !== 'production') {\n        const { isValidElementType } = require('next/dist/compiled/react-is')\n        if (!isValidElementType(routeInfo.Component)) {\n          throw new Error(\n            `The default export is not a React Component in page: \"${pathname}\"`\n          )\n        }\n      }\n      const wasBailedPrefetch = data?.response?.headers.get('x-middleware-skip')\n\n      const shouldFetchData = routeInfo.__N_SSG || routeInfo.__N_SSP\n\n      // For non-SSG prefetches that bailed before sending data\n      // we clear the cache to fetch full response\n      if (wasBailedPrefetch && data?.dataHref) {\n        delete this.sdc[data.dataHref]\n      }\n\n      const { props, cacheKey } = await this._getData(async () => {\n        if (shouldFetchData) {\n          if (data?.json && !wasBailedPrefetch) {\n            return { cacheKey: data.cacheKey, props: data.json }\n          }\n\n          const dataHref = data?.dataHref\n            ? data.dataHref\n            : this.pageLoader.getDataHref({\n                href: formatWithValidation({ pathname, query }),\n                asPath: resolvedAs,\n                locale,\n              })\n\n          const fetched = await fetchNextData({\n            dataHref,\n            isServerRender: this.isSsr,\n            parseJSON: true,\n            inflightCache: wasBailedPrefetch ? {} : this.sdc,\n            persistCache: !isPreview,\n            isPrefetch: false,\n            unstable_skipClientCache,\n          })\n\n          return {\n            cacheKey: fetched.cacheKey,\n            props: fetched.json || {},\n          }\n        }\n\n        return {\n          headers: {},\n          props: await this.getInitialProps(\n            routeInfo.Component,\n            // we provide AppTree later so this needs to be `any`\n            {\n              pathname,\n              query,\n              asPath: as,\n              locale,\n              locales: this.locales,\n              defaultLocale: this.defaultLocale,\n            } as any\n          ),\n        }\n      })\n\n      // Only bust the data cache for SSP routes although\n      // middleware can skip cache per request with\n      // x-middleware-cache: no-cache as well\n      if (routeInfo.__N_SSP && fetchNextDataParams.dataHref && cacheKey) {\n        delete this.sdc[cacheKey]\n      }\n\n      // we kick off a HEAD request in the background\n      // when a non-prefetch request is made to signal revalidation\n      if (\n        !this.isPreview &&\n        routeInfo.__N_SSG &&\n        process.env.NODE_ENV !== 'development' &&\n        !isQueryUpdating\n      ) {\n        fetchNextData(\n          Object.assign({}, fetchNextDataParams, {\n            isBackground: true,\n            persistCache: false,\n            inflightCache: this.sbc,\n          })\n        ).catch(() => {})\n      }\n\n      props.pageProps = Object.assign({}, props.pageProps)\n      routeInfo.props = props\n      routeInfo.route = route\n      routeInfo.query = query\n      routeInfo.resolvedAs = resolvedAs\n      this.components[route] = routeInfo\n\n      return routeInfo\n    } catch (err) {\n      return this.handleRouteInfoError(\n        getProperError(err),\n        pathname,\n        query,\n        as,\n        routeProps\n      )\n    }\n  }\n\n  private set(\n    state: typeof this.state,\n    data: PrivateRouteInfo,\n    resetScroll: { x: number; y: number } | null\n  ): Promise<void> {\n    this.state = state\n\n    return this.sub(\n      data,\n      this.components['/_app'].Component as AppComponent,\n      resetScroll\n    )\n  }\n\n  /**\n   * Callback to execute before replacing router state\n   * @param cb callback to be executed\n   */\n  beforePopState(cb: BeforePopStateCallback) {\n    this._bps = cb\n  }\n\n  onlyAHashChange(as: string): boolean {\n    if (!this.asPath) return false\n    const [oldUrlNoHash, oldHash] = this.asPath.split('#', 2)\n    const [newUrlNoHash, newHash] = as.split('#', 2)\n\n    // Makes sure we scroll to the provided hash if the url/hash are the same\n    if (newHash && oldUrlNoHash === newUrlNoHash && oldHash === newHash) {\n      return true\n    }\n\n    // If the urls are change, there's more than a hash change\n    if (oldUrlNoHash !== newUrlNoHash) {\n      return false\n    }\n\n    // If the hash has changed, then it's a hash only change.\n    // This check is necessary to handle both the enter and\n    // leave hash === '' cases. The identity case falls through\n    // and is treated as a next reload.\n    return oldHash !== newHash\n  }\n\n  scrollToHash(as: string): void {\n    const [, hash = ''] = as.split('#', 2)\n\n    handleSmoothScroll(\n      () => {\n        // Scroll to top if the hash is just `#` with no value or `#top`\n        // To mirror browsers\n        if (hash === '' || hash === 'top') {\n          window.scrollTo(0, 0)\n          return\n        }\n\n        // Decode hash to make non-latin anchor works.\n        const rawHash = decodeURIComponent(hash)\n        // First we check if the element by id is found\n        const idEl = document.getElementById(rawHash)\n        if (idEl) {\n          idEl.scrollIntoView()\n          return\n        }\n        // If there's no element with the id, we check the `name` property\n        // To mirror browsers\n        const nameEl = document.getElementsByName(rawHash)[0]\n        if (nameEl) {\n          nameEl.scrollIntoView()\n        }\n      },\n      {\n        onlyHashChange: this.onlyAHashChange(as),\n      }\n    )\n  }\n\n  urlIsNew(asPath: string): boolean {\n    return this.asPath !== asPath\n  }\n\n  /**\n   * Prefetch page code, you may wait for the data during page rendering.\n   * This feature only works in production!\n   * @param url the href of prefetched page\n   * @param asPath the as path of the prefetched page\n   */\n  async prefetch(\n    url: string,\n    asPath: string = url,\n    options: PrefetchOptions = {}\n  ): Promise<void> {\n    // Prefetch is not supported in development mode because it would trigger on-demand-entries\n    if (process.env.NODE_ENV !== 'production') {\n      return\n    }\n\n    if (typeof window !== 'undefined' && isBot(window.navigator.userAgent)) {\n      // No prefetches for bots that render the link since they are typically navigating\n      // links via the equivalent of a hard navigation and hence never utilize these\n      // prefetches.\n      return\n    }\n    let parsed = parseRelativeUrl(url)\n    const urlPathname = parsed.pathname\n\n    let { pathname, query } = parsed\n    const originalPathname = pathname\n\n    if (process.env.__NEXT_I18N_SUPPORT) {\n      if (options.locale === false) {\n        pathname = normalizeLocalePath!(pathname, this.locales).pathname\n        parsed.pathname = pathname\n        url = formatWithValidation(parsed)\n\n        let parsedAs = parseRelativeUrl(asPath)\n        const localePathResult = normalizeLocalePath!(\n          parsedAs.pathname,\n          this.locales\n        )\n        parsedAs.pathname = localePathResult.pathname\n        options.locale = localePathResult.detectedLocale || this.defaultLocale\n        asPath = formatWithValidation(parsedAs)\n      }\n    }\n\n    const pages = await this.pageLoader.getPageList()\n    let resolvedAs = asPath\n\n    const locale =\n      typeof options.locale !== 'undefined'\n        ? options.locale || undefined\n        : this.locale\n\n    const isMiddlewareMatch = await matchesMiddleware({\n      asPath: asPath,\n      locale: locale,\n      router: this,\n    })\n\n    if (process.env.__NEXT_HAS_REWRITES && asPath.startsWith('/')) {\n      let rewrites: any\n      ;({ __rewrites: rewrites } = await getClientBuildManifest())\n\n      const rewritesResult = resolveRewrites(\n        addBasePath(addLocale(asPath, this.locale), true),\n        pages,\n        rewrites,\n        parsed.query,\n        (p: string) => resolveDynamicRoute(p, pages),\n        this.locales\n      )\n\n      if (rewritesResult.externalDest) {\n        return\n      }\n\n      if (!isMiddlewareMatch) {\n        resolvedAs = removeLocale(\n          removeBasePath(rewritesResult.asPath),\n          this.locale\n        )\n      }\n\n      if (rewritesResult.matchedPage && rewritesResult.resolvedHref) {\n        // if this directly matches a page we need to update the href to\n        // allow the correct page chunk to be loaded\n        pathname = rewritesResult.resolvedHref\n        parsed.pathname = pathname\n\n        if (!isMiddlewareMatch) {\n          url = formatWithValidation(parsed)\n        }\n      }\n    }\n    parsed.pathname = resolveDynamicRoute(parsed.pathname, pages)\n\n    if (isDynamicRoute(parsed.pathname)) {\n      pathname = parsed.pathname\n      parsed.pathname = pathname\n      Object.assign(\n        query,\n        getRouteMatcher(getRouteRegex(parsed.pathname))(\n          parsePath(asPath).pathname\n        ) || {}\n      )\n\n      if (!isMiddlewareMatch) {\n        url = formatWithValidation(parsed)\n      }\n    }\n\n    const data =\n      process.env.__NEXT_MIDDLEWARE_PREFETCH === 'strict'\n        ? null\n        : await withMiddlewareEffects({\n            fetchData: () =>\n              fetchNextData({\n                dataHref: this.pageLoader.getDataHref({\n                  href: formatWithValidation({\n                    pathname: originalPathname,\n                    query,\n                  }),\n                  skipInterpolation: true,\n                  asPath: resolvedAs,\n                  locale,\n                }),\n                hasMiddleware: true,\n                isServerRender: false,\n                parseJSON: true,\n                inflightCache: this.sdc,\n                persistCache: !this.isPreview,\n                isPrefetch: true,\n              }),\n            asPath: asPath,\n            locale: locale,\n            router: this,\n          })\n\n    /**\n     * If there was a rewrite we apply the effects of the rewrite on the\n     * current parameters for the prefetch.\n     */\n    if (data?.effect.type === 'rewrite') {\n      parsed.pathname = data.effect.resolvedHref\n      pathname = data.effect.resolvedHref\n      query = { ...query, ...data.effect.parsedAs.query }\n      resolvedAs = data.effect.parsedAs.pathname\n      url = formatWithValidation(parsed)\n    }\n\n    /**\n     * If there is a redirect to an external destination then we don't have\n     * to prefetch content as it will be unused.\n     */\n    if (data?.effect.type === 'redirect-external') {\n      return\n    }\n\n    const route = removeTrailingSlash(pathname)\n\n    if (await this._bfl(asPath, resolvedAs, options.locale, true)) {\n      this.components[urlPathname] = { __appRouter: true } as any\n    }\n\n    await Promise.all([\n      this.pageLoader._isSsg(route).then((isSsg) => {\n        return isSsg\n          ? fetchNextData({\n              dataHref: data?.json\n                ? data?.dataHref\n                : this.pageLoader.getDataHref({\n                    href: url,\n                    asPath: resolvedAs,\n                    locale: locale,\n                  }),\n              isServerRender: false,\n              parseJSON: true,\n              inflightCache: this.sdc,\n              persistCache: !this.isPreview,\n              isPrefetch: true,\n              unstable_skipClientCache:\n                options.unstable_skipClientCache ||\n                (options.priority &&\n                  !!process.env.__NEXT_OPTIMISTIC_CLIENT_CACHE),\n            })\n              .then(() => false)\n              .catch(() => false)\n          : false\n      }),\n      this.pageLoader[options.priority ? 'loadPage' : 'prefetch'](route),\n    ])\n  }\n\n  async fetchComponent(route: string) {\n    const handleCancelled = getCancelledHandler({ route, router: this })\n\n    try {\n      const componentResult = await this.pageLoader.loadPage(route)\n      handleCancelled()\n\n      return componentResult\n    } catch (err) {\n      handleCancelled()\n      throw err\n    }\n  }\n\n  _getData<T>(fn: () => Promise<T>): Promise<T> {\n    let cancelled = false\n    const cancel = () => {\n      cancelled = true\n    }\n    this.clc = cancel\n    return fn().then((data) => {\n      if (cancel === this.clc) {\n        this.clc = null\n      }\n\n      if (cancelled) {\n        const err: any = new Error('Loading initial props cancelled')\n        err.cancelled = true\n        throw err\n      }\n\n      return data\n    })\n  }\n\n  getInitialProps(\n    Component: ComponentType,\n    ctx: NextPageContext\n  ): Promise<Record<string, any>> {\n    const { Component: App } = this.components['/_app']\n    const AppTree = this._wrapApp(App as AppComponent)\n    ctx.AppTree = AppTree\n    return loadGetInitialProps<AppContextType<Router>>(App, {\n      AppTree,\n      Component,\n      router: this,\n      ctx,\n    })\n  }\n\n  get route(): string {\n    return this.state.route\n  }\n\n  get pathname(): string {\n    return this.state.pathname\n  }\n\n  get query(): ParsedUrlQuery {\n    return this.state.query\n  }\n\n  get asPath(): string {\n    return this.state.asPath\n  }\n\n  get locale(): string | undefined {\n    return this.state.locale\n  }\n\n  get isFallback(): boolean {\n    return this.state.isFallback\n  }\n\n  get isPreview(): boolean {\n    return this.state.isPreview\n  }\n}\n", "// minimal implementation MurmurHash2 hash function\nfunction murmurhash2(str: string) {\n  let h = 0\n  for (let i = 0; i < str.length; i++) {\n    const c = str.charCodeAt(i)\n    h = Math.imul(h ^ c, 0x5bd1e995)\n    h ^= h >>> 13\n    h = Math.imul(h, 0x5bd1e995)\n  }\n  return h >>> 0\n}\n\n// default to 0.01% error rate as the filter compresses very well\nconst DEFAULT_ERROR_RATE = 0.0001\n\nexport class BloomFilter {\n  numItems: number\n  errorRate: number\n  numBits: number\n  numHashes: number\n  bitArray: number[]\n\n  constructor(numItems: number, errorRate: number = DEFAULT_ERROR_RATE) {\n    this.numItems = numItems\n    this.errorRate = errorRate\n    this.numBits = Math.ceil(\n      -(numItems * Math.log(errorRate)) / (Math.log(2) * Math.log(2))\n    )\n    this.numHashes = Math.ceil((this.numBits / numItems) * Math.log(2))\n    this.bitArray = new Array(this.numBits).fill(0)\n  }\n\n  static from(items: string[], errorRate = DEFAULT_ERROR_RATE) {\n    const filter = new BloomFilter(items.length, errorRate)\n\n    for (const item of items) {\n      filter.add(item)\n    }\n    return filter\n  }\n\n  export() {\n    const data = {\n      numItems: this.numItems,\n      errorRate: this.errorRate,\n      numBits: this.numBits,\n      numHashes: this.numHashes,\n      bitArray: this.bitArray,\n    }\n\n    if (process.env.NEXT_RUNTIME === 'nodejs') {\n      if (this.errorRate < DEFAULT_ERROR_RATE) {\n        const filterData = JSON.stringify(data)\n        const gzipSize = require('next/dist/compiled/gzip-size').sync(\n          filterData\n        )\n\n        if (gzipSize > 1024) {\n          console.warn(\n            `Creating filter with error rate less than 0.1% (0.001) can increase the size dramatically proceed with caution. Received error rate ${this.errorRate} resulted in size ${filterData.length} bytes, ${gzipSize} bytes (gzip)`\n          )\n        }\n      }\n    }\n\n    return data\n  }\n\n  import(data: ReturnType<(typeof this)['export']>) {\n    this.numItems = data.numItems\n    this.errorRate = data.errorRate\n    this.numBits = data.numBits\n    this.numHashes = data.numHashes\n    this.bitArray = data.bitArray\n  }\n\n  add(item: string) {\n    const hashValues = this.getHashValues(item)\n    hashValues.forEach((hash) => {\n      this.bitArray[hash] = 1\n    })\n  }\n\n  contains(item: string) {\n    const hashValues = this.getHashValues(item)\n    return hashValues.every((hash) => this.bitArray[hash])\n  }\n\n  getHashValues(item: string) {\n    const hashValues = []\n    for (let i = 1; i <= this.numHashes; i++) {\n      const hash = murmurhash2(`${item}${i}`) % this.numBits\n      hashValues.push(hash)\n    }\n    return hashValues\n  }\n}\n", "import { useEffect, useState } from 'react'\nimport { createPortal } from 'react-dom'\n\ntype PortalProps = {\n  children: React.ReactNode\n  type: string\n}\n\nexport const Portal = ({ children, type }: PortalProps) => {\n  const [portalNode, setPortalNode] = useState<HTMLElement | null>(null)\n\n  useEffect(() => {\n    const element = document.createElement(type)\n    document.body.appendChild(element)\n    setPortalNode(element)\n    return () => {\n      document.body.removeChild(element)\n    }\n  }, [type])\n\n  return portalNode ? createPortal(children, portalNode) : null\n}\n", "import React from 'react'\n\nexport const HeadManagerContext: React.Context<{\n  updateHead?: (state: any) => void\n  mountedInstances?: any\n  updateScripts?: (state: any) => void\n  scripts?: any\n  getIsSsr?: () => boolean\n\n  // Used in app directory, to render script tags as server components.\n  appDir?: boolean\n  nonce?: string\n}> = React.createContext({})\n\nif (process.env.NODE_ENV !== 'production') {\n  HeadManagerContext.displayName = 'HeadManagerContext'\n}\n", "import type { ParsedUrlQuery } from 'querystring'\n\nexport function searchParamsToUrlQuery(\n  searchParams: URLSearchParams\n): ParsedUrlQuery {\n  const query: ParsedUrlQuery = {}\n  for (const [key, value] of searchParams.entries()) {\n    const existing = query[key]\n    if (typeof existing === 'undefined') {\n      query[key] = value\n    } else if (Array.isArray(existing)) {\n      existing.push(value)\n    } else {\n      query[key] = [existing, value]\n    }\n  }\n  return query\n}\n\nfunction stringifyUrlQueryParam(param: unknown): string {\n  if (typeof param === 'string') {\n    return param\n  }\n\n  if (\n    (typeof param === 'number' && !isNaN(param)) ||\n    typeof param === 'boolean'\n  ) {\n    return String(param)\n  } else {\n    return ''\n  }\n}\n\nexport function urlQueryToSearchParams(query: ParsedUrlQuery): URLSearchParams {\n  const searchParams = new URLSearchParams()\n  for (const [key, value] of Object.entries(query)) {\n    if (Array.isArray(value)) {\n      for (const item of value) {\n        searchParams.append(key, stringifyUrlQueryParam(item))\n      }\n    } else {\n      searchParams.set(key, stringifyUrlQueryParam(value))\n    }\n  }\n  return searchParams\n}\n\nexport function assign(\n  target: URLSearchParams,\n  ...searchParamsList: URLSearchParams[]\n): URLSearchParams {\n  for (const searchParams of searchParamsList) {\n    for (const key of searchParams.keys()) {\n      target.delete(key)\n    }\n\n    for (const [key, value] of searchParams.entries()) {\n      target.append(key, value)\n    }\n  }\n\n  return target\n}\n", "/**\n * Given a path this function will find the pathname, query and hash and return\n * them. This is useful to parse full paths on the client side.\n * @param path A path to parse e.g. /foo/bar?id=1#hash\n */\nexport function parsePath(path: string) {\n  const hashIndex = path.indexOf('#')\n  const queryIndex = path.indexOf('?')\n  const hasQuery = queryIndex > -1 && (hashIndex < 0 || queryIndex < hashIndex)\n\n  if (hasQuery || hashIndex > -1) {\n    return {\n      pathname: path.substring(0, hasQuery ? queryIndex : hashIndex),\n      query: hasQuery\n        ? path.substring(queryIndex, hashIndex > -1 ? hashIndex : undefined)\n        : '',\n      hash: hashIndex > -1 ? path.slice(hashIndex) : '',\n    }\n  }\n\n  return { pathname: path, query: '', hash: '' }\n}\n", "/*\nMIT License\n\nCopyright (c) <PERSON> (https://jasonformat.com/)\n\nPermission is hereby granted, free of charge, to any person obtaining a copy of this software and associated documentation files (the \"Software\"), to deal in the Software without restriction, including without limitation the rights to use, copy, modify, merge, publish, distribute, sublicense, and/or sell copies of the Software, and to permit persons to whom the Software is furnished to do so, subject to the following conditions:\n\nThe above copyright notice and this permission notice shall be included in all copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.\n*/\n\n// This file is based on https://github.com/developit/mitt/blob/v1.1.3/src/index.js\n// It's been edited for the needs of this script\n// See the LICENSE at the top of the file\n\ntype Handler = (...evts: any[]) => void\n\nexport type MittEmitter<T> = {\n  on(type: T, handler: Handler): void\n  off(type: T, handler: Handler): void\n  emit(type: T, ...evts: any[]): void\n}\n\nexport default function mitt(): MittEmitter<string> {\n  const all: { [s: string]: Handler[] } = Object.create(null)\n\n  return {\n    on(type: string, handler: Handler) {\n      ;(all[type] || (all[type] = [])).push(handler)\n    },\n\n    off(type: string, handler: Handler) {\n      if (all[type]) {\n        all[type].splice(all[type].indexOf(handler) >>> 0, 1)\n      }\n    },\n\n    emit(type: string, ...evts: any[]) {\n      // eslint-disable-next-line array-callback-return\n      ;(all[type] || []).slice().map((handler: Handler) => {\n        handler(...evts)\n      })\n    },\n  }\n}\n", "\"use strict\";\nObject.defineProperty(exports, \"__esModule\", {\n    value: true\n});\nObject.defineProperty(exports, \"getDeploymentIdQueryOrEmptyString\", {\n    enumerable: true,\n    get: function() {\n        return getDeploymentIdQueryOrEmptyString;\n    }\n});\nfunction getDeploymentIdQueryOrEmptyString() {\n    if (process.env.NEXT_DEPLOYMENT_ID) {\n        return `?dpl=${process.env.NEXT_DEPLOYMENT_ID}`;\n    }\n    return '';\n}\n\n//# sourceMappingURL=deployment-id.js.map", "import { parsePath } from './parse-path'\n\n/**\n * Similarly to `addPathPrefix`, this function adds a suffix at the end on the\n * provided path. It also works only for paths ensuring the argument starts\n * with a slash.\n */\nexport function addPathSuffix(path: string, suffix?: string) {\n  if (!path.startsWith('/') || !suffix) {\n    return path\n  }\n\n  const { pathname, query, hash } = parsePath(path)\n  return `${pathname}${suffix}${query}${hash}`\n}\n", "import { pathHasPrefix } from './path-has-prefix'\n\n/**\n * Given a path and a prefix it will remove the prefix when it exists in the\n * given path. It ensures it matches exactly without containing extra chars\n * and if the prefix is not there it will be noop.\n *\n * @param path The path to remove the prefix from.\n * @param prefix The prefix to be removed.\n */\nexport function removePathPrefix(path: string, prefix: string): string {\n  // If the path doesn't start with the prefix we can return it as is. This\n  // protects us from situations where the prefix is a substring of the path\n  // prefix such as:\n  //\n  // For prefix: /blog\n  //\n  //   /blog -> true\n  //   /blog/ -> true\n  //   /blog/1 -> true\n  //   /blogging -> false\n  //   /blogging/ -> false\n  //   /blogging/1 -> false\n  if (!pathHasPrefix(path, prefix)) {\n    return path\n  }\n\n  // Remove the prefix from the path via slicing.\n  const withoutPrefix = path.slice(prefix.length)\n\n  // If the path without the prefix starts with a `/` we can return it as is.\n  if (withoutPrefix.startsWith('/')) {\n    return withoutPrefix\n  }\n\n  // If the path without the prefix doesn't start with a `/` we need to add it\n  // back to the path to make sure it's a valid path.\n  return `/${withoutPrefix}`\n}\n", "import { hasBasePath } from './has-base-path'\n\nconst basePath = (process.env.__NEXT_ROUTER_BASEPATH as string) || ''\n\nexport function removeBasePath(path: string): string {\n  if (process.env.__NEXT_MANUAL_CLIENT_BASE_PATH) {\n    if (!hasBasePath(path)) {\n      return path\n    }\n  }\n\n  // Can't trim the basePath if it has zero length!\n  if (basePath.length === 0) return path\n\n  path = path.slice(basePath.length)\n  if (!path.startsWith('/')) path = `/${path}`\n  return path\n}\n", "'use client'\n\nimport ReactDOM from 'react-dom'\nimport React, { useEffect, useContext, useRef, type JSX } from 'react'\nimport type { ScriptHTMLAttributes } from 'react'\nimport { HeadManagerContext } from '../shared/lib/head-manager-context.shared-runtime'\nimport { setAttributesFromProps } from './set-attributes-from-props'\nimport { requestIdleCallback } from './request-idle-callback'\n\nconst ScriptCache = new Map()\nconst LoadCache = new Set()\n\nexport interface ScriptProps extends ScriptHTMLAttributes<HTMLScriptElement> {\n  strategy?: 'afterInteractive' | 'lazyOnload' | 'beforeInteractive' | 'worker'\n  id?: string\n  onLoad?: (e: any) => void\n  onReady?: () => void | null\n  onError?: (e: any) => void\n  children?: React.ReactNode\n  stylesheets?: string[]\n}\n\n/**\n * @deprecated Use `ScriptProps` instead.\n */\nexport type Props = ScriptProps\n\nconst insertStylesheets = (stylesheets: string[]) => {\n  // Case 1: Styles for afterInteractive/lazyOnload with appDir injected via handleClientScriptLoad\n  //\n  // Using ReactDOM.preinit to feature detect appDir and inject styles\n  // Stylesheets might have already been loaded if initialized with Script component\n  // Re-inject styles here to handle scripts loaded via handleClientScriptLoad\n  // ReactDOM.preinit handles dedup and ensures the styles are loaded only once\n  if (ReactDOM.preinit) {\n    stylesheets.forEach((stylesheet: string) => {\n      ReactDOM.preinit(stylesheet, { as: 'style' })\n    })\n\n    return\n  }\n\n  // Case 2: Styles for afterInteractive/lazyOnload with pages injected via handleClientScriptLoad\n  //\n  // We use this function to load styles when appdir is not detected\n  // TODO: Use React float APIs to load styles once available for pages dir\n  if (typeof window !== 'undefined') {\n    let head = document.head\n    stylesheets.forEach((stylesheet: string) => {\n      let link = document.createElement('link')\n\n      link.type = 'text/css'\n      link.rel = 'stylesheet'\n      link.href = stylesheet\n\n      head.appendChild(link)\n    })\n  }\n}\n\nconst loadScript = (props: ScriptProps): void => {\n  const {\n    src,\n    id,\n    onLoad = () => {},\n    onReady = null,\n    dangerouslySetInnerHTML,\n    children = '',\n    strategy = 'afterInteractive',\n    onError,\n    stylesheets,\n  } = props\n\n  const cacheKey = id || src\n\n  // Script has already loaded\n  if (cacheKey && LoadCache.has(cacheKey)) {\n    return\n  }\n\n  // Contents of this script are already loading/loaded\n  if (ScriptCache.has(src)) {\n    LoadCache.add(cacheKey)\n    // It is possible that multiple `next/script` components all have same \"src\", but has different \"onLoad\"\n    // This is to make sure the same remote script will only load once, but \"onLoad\" are executed in order\n    ScriptCache.get(src).then(onLoad, onError)\n    return\n  }\n\n  /** Execute after the script first loaded */\n  const afterLoad = () => {\n    // Run onReady for the first time after load event\n    if (onReady) {\n      onReady()\n    }\n    // add cacheKey to LoadCache when load successfully\n    LoadCache.add(cacheKey)\n  }\n\n  const el = document.createElement('script')\n\n  const loadPromise = new Promise<void>((resolve, reject) => {\n    el.addEventListener('load', function (e) {\n      resolve()\n      if (onLoad) {\n        onLoad.call(this, e)\n      }\n      afterLoad()\n    })\n    el.addEventListener('error', function (e) {\n      reject(e)\n    })\n  }).catch(function (e) {\n    if (onError) {\n      onError(e)\n    }\n  })\n\n  if (dangerouslySetInnerHTML) {\n    // Casting since lib.dom.d.ts doesn't have TrustedHTML yet.\n    el.innerHTML = (dangerouslySetInnerHTML.__html as string) || ''\n\n    afterLoad()\n  } else if (children) {\n    el.textContent =\n      typeof children === 'string'\n        ? children\n        : Array.isArray(children)\n          ? children.join('')\n          : ''\n\n    afterLoad()\n  } else if (src) {\n    el.src = src\n    // do not add cacheKey into LoadCache for remote script here\n    // cacheKey will be added to LoadCache when it is actually loaded (see loadPromise above)\n\n    ScriptCache.set(src, loadPromise)\n  }\n\n  setAttributesFromProps(el, props)\n\n  if (strategy === 'worker') {\n    el.setAttribute('type', 'text/partytown')\n  }\n\n  el.setAttribute('data-nscript', strategy)\n\n  // Load styles associated with this script\n  if (stylesheets) {\n    insertStylesheets(stylesheets)\n  }\n\n  document.body.appendChild(el)\n}\n\nexport function handleClientScriptLoad(props: ScriptProps) {\n  const { strategy = 'afterInteractive' } = props\n  if (strategy === 'lazyOnload') {\n    window.addEventListener('load', () => {\n      requestIdleCallback(() => loadScript(props))\n    })\n  } else {\n    loadScript(props)\n  }\n}\n\nfunction loadLazyScript(props: ScriptProps) {\n  if (document.readyState === 'complete') {\n    requestIdleCallback(() => loadScript(props))\n  } else {\n    window.addEventListener('load', () => {\n      requestIdleCallback(() => loadScript(props))\n    })\n  }\n}\n\nfunction addBeforeInteractiveToCache() {\n  const scripts = [\n    ...document.querySelectorAll('[data-nscript=\"beforeInteractive\"]'),\n    ...document.querySelectorAll('[data-nscript=\"beforePageRender\"]'),\n  ]\n  scripts.forEach((script) => {\n    const cacheKey = script.id || script.getAttribute('src')\n    LoadCache.add(cacheKey)\n  })\n}\n\nexport function initScriptLoader(scriptLoaderItems: ScriptProps[]) {\n  scriptLoaderItems.forEach(handleClientScriptLoad)\n  addBeforeInteractiveToCache()\n}\n\n/**\n * Load a third-party scripts in an optimized way.\n *\n * Read more: [Next.js Docs: `next/script`](https://nextjs.org/docs/app/api-reference/components/script)\n */\nfunction Script(props: ScriptProps): JSX.Element | null {\n  const {\n    id,\n    src = '',\n    onLoad = () => {},\n    onReady = null,\n    strategy = 'afterInteractive',\n    onError,\n    stylesheets,\n    ...restProps\n  } = props\n\n  // Context is available only during SSR\n  const { updateScripts, scripts, getIsSsr, appDir, nonce } =\n    useContext(HeadManagerContext)\n\n  /**\n   * - First mount:\n   *   1. The useEffect for onReady executes\n   *   2. hasOnReadyEffectCalled.current is false, but the script hasn't loaded yet (not in LoadCache)\n   *      onReady is skipped, set hasOnReadyEffectCalled.current to true\n   *   3. The useEffect for loadScript executes\n   *   4. hasLoadScriptEffectCalled.current is false, loadScript executes\n   *      Once the script is loaded, the onLoad and onReady will be called by then\n   *   [If strict mode is enabled / is wrapped in <OffScreen /> component]\n   *   5. The useEffect for onReady executes again\n   *   6. hasOnReadyEffectCalled.current is true, so entire effect is skipped\n   *   7. The useEffect for loadScript executes again\n   *   8. hasLoadScriptEffectCalled.current is true, so entire effect is skipped\n   *\n   * - Second mount:\n   *   1. The useEffect for onReady executes\n   *   2. hasOnReadyEffectCalled.current is false, but the script has already loaded (found in LoadCache)\n   *      onReady is called, set hasOnReadyEffectCalled.current to true\n   *   3. The useEffect for loadScript executes\n   *   4. The script is already loaded, loadScript bails out\n   *   [If strict mode is enabled / is wrapped in <OffScreen /> component]\n   *   5. The useEffect for onReady executes again\n   *   6. hasOnReadyEffectCalled.current is true, so entire effect is skipped\n   *   7. The useEffect for loadScript executes again\n   *   8. hasLoadScriptEffectCalled.current is true, so entire effect is skipped\n   */\n  const hasOnReadyEffectCalled = useRef(false)\n\n  useEffect(() => {\n    const cacheKey = id || src\n    if (!hasOnReadyEffectCalled.current) {\n      // Run onReady if script has loaded before but component is re-mounted\n      if (onReady && cacheKey && LoadCache.has(cacheKey)) {\n        onReady()\n      }\n\n      hasOnReadyEffectCalled.current = true\n    }\n  }, [onReady, id, src])\n\n  const hasLoadScriptEffectCalled = useRef(false)\n\n  useEffect(() => {\n    if (!hasLoadScriptEffectCalled.current) {\n      if (strategy === 'afterInteractive') {\n        loadScript(props)\n      } else if (strategy === 'lazyOnload') {\n        loadLazyScript(props)\n      }\n\n      hasLoadScriptEffectCalled.current = true\n    }\n  }, [props, strategy])\n\n  if (strategy === 'beforeInteractive' || strategy === 'worker') {\n    if (updateScripts) {\n      scripts[strategy] = (scripts[strategy] || []).concat([\n        {\n          id,\n          src,\n          onLoad,\n          onReady,\n          onError,\n          ...restProps,\n        },\n      ])\n      updateScripts(scripts)\n    } else if (getIsSsr && getIsSsr()) {\n      // Script has already loaded during SSR\n      LoadCache.add(id || src)\n    } else if (getIsSsr && !getIsSsr()) {\n      loadScript(props)\n    }\n  }\n\n  // For the app directory, we need React Float to preload these scripts.\n  if (appDir) {\n    // Injecting stylesheets here handles beforeInteractive and worker scripts correctly\n    // For other strategies injecting here ensures correct stylesheet order\n    // ReactDOM.preinit handles loading the styles in the correct order,\n    // also ensures the stylesheet is loaded only once and in a consistent manner\n    //\n    // Case 1: Styles for beforeInteractive/worker with appDir - handled here\n    // Case 2: Styles for beforeInteractive/worker with pages dir - Not handled yet\n    // Case 3: Styles for afterInteractive/lazyOnload with appDir - handled here\n    // Case 4: Styles for afterInteractive/lazyOnload with pages dir - handled in insertStylesheets function\n    if (stylesheets) {\n      stylesheets.forEach((styleSrc) => {\n        ReactDOM.preinit(styleSrc, { as: 'style' })\n      })\n    }\n\n    // Before interactive scripts need to be loaded by Next.js' runtime instead\n    // of native <script> tags, because they no longer have `defer`.\n    if (strategy === 'beforeInteractive') {\n      if (!src) {\n        // For inlined scripts, we put the content in `children`.\n        if (restProps.dangerouslySetInnerHTML) {\n          // Casting since lib.dom.d.ts doesn't have TrustedHTML yet.\n          restProps.children = restProps.dangerouslySetInnerHTML\n            .__html as string\n          delete restProps.dangerouslySetInnerHTML\n        }\n\n        return (\n          <script\n            nonce={nonce}\n            dangerouslySetInnerHTML={{\n              __html: `(self.__next_s=self.__next_s||[]).push(${JSON.stringify([\n                0,\n                { ...restProps, id },\n              ])})`,\n            }}\n          />\n        )\n      } else {\n        // @ts-ignore\n        ReactDOM.preload(\n          src,\n          restProps.integrity\n            ? {\n                as: 'script',\n                integrity: restProps.integrity,\n                nonce,\n                crossOrigin: restProps.crossOrigin,\n              }\n            : { as: 'script', nonce, crossOrigin: restProps.crossOrigin }\n        )\n        return (\n          <script\n            nonce={nonce}\n            dangerouslySetInnerHTML={{\n              __html: `(self.__next_s=self.__next_s||[]).push(${JSON.stringify([\n                src,\n                { ...restProps, id },\n              ])})`,\n            }}\n          />\n        )\n      }\n    } else if (strategy === 'afterInteractive') {\n      if (src) {\n        // @ts-ignore\n        ReactDOM.preload(\n          src,\n          restProps.integrity\n            ? {\n                as: 'script',\n                integrity: restProps.integrity,\n                nonce,\n                crossOrigin: restProps.crossOrigin,\n              }\n            : { as: 'script', nonce, crossOrigin: restProps.crossOrigin }\n        )\n      }\n    }\n  }\n\n  return null\n}\n\nObject.defineProperty(Script, '__nextScript', { value: true })\n\nexport default Script\n", "/**\n * For a given page path, this function ensures that there is a leading slash.\n * If there is not a leading slash, one is added, otherwise it is noop.\n */\nexport function ensureLeadingSlash(path: string) {\n  return path.startsWith('/') ? path : `/${path}`\n}\n", "import { removeTrailingSlash } from '../shared/lib/router/utils/remove-trailing-slash'\nimport { parsePath } from '../shared/lib/router/utils/parse-path'\n\n/**\n * Normalizes the trailing slash of a path according to the `trailingSlash` option\n * in `next.config.js`.\n */\nexport const normalizePathTrailingSlash = (path: string) => {\n  if (!path.startsWith('/') || process.env.__NEXT_MANUAL_TRAILING_SLASH) {\n    return path\n  }\n\n  const { pathname, query, hash } = parsePath(path)\n  if (process.env.__NEXT_TRAILING_SLASH) {\n    if (/\\.[^/]+\\/?$/.test(pathname)) {\n      return `${removeTrailingSlash(pathname)}${query}${hash}`\n    } else if (pathname.endsWith('/')) {\n      return `${pathname}${query}${hash}`\n    } else {\n      return `${pathname}/${query}${hash}`\n    }\n  }\n\n  return `${removeTrailingSlash(pathname)}${query}${hash}`\n}\n", "function _getRequireWildcardCache(nodeInterop) {\n    if (typeof WeakMap !== \"function\") return null;\n\n    var cacheBabelInterop = new WeakMap();\n    var cacheNodeInterop = new WeakMap();\n\n    return (_getRequireWildcardCache = function(nodeInterop) {\n        return nodeInterop ? cacheNodeInterop : cacheBabelInterop;\n    })(nodeInterop);\n}\nfunction _interop_require_wildcard(obj, nodeInterop) {\n    if (!nodeInterop && obj && obj.__esModule) return obj;\n    if (obj === null || typeof obj !== \"object\" && typeof obj !== \"function\") return { default: obj };\n\n    var cache = _getRequireWildcardCache(nodeInterop);\n\n    if (cache && cache.has(obj)) return cache.get(obj);\n\n    var newObj = { __proto__: null };\n    var hasPropertyDescriptor = Object.defineProperty && Object.getOwnPropertyDescriptor;\n\n    for (var key in obj) {\n        if (key !== \"default\" && Object.prototype.hasOwnProperty.call(obj, key)) {\n            var desc = hasPropertyDescriptor ? Object.getOwnPropertyDescriptor(obj, key) : null;\n            if (desc && (desc.get || desc.set)) Object.defineProperty(newObj, key, desc);\n            else newObj[key] = obj[key];\n        }\n    }\n\n    newObj.default = obj;\n\n    if (cache) cache.set(obj, newObj);\n\n    return newObj;\n}\nexport { _interop_require_wildcard as _ };\n", "import React from 'react'\nimport type { ImageConfigComplete } from './image-config'\nimport { imageConfigDefault } from './image-config'\n\nexport const ImageConfigContext =\n  React.createContext<ImageConfigComplete>(imageConfigDefault)\n\nif (process.env.NODE_ENV !== 'production') {\n  ImageConfigContext.displayName = 'ImageConfigContext'\n}\n", "import React from 'react'\nimport { useRouter } from './router'\n\nconst nextjsRouteAnnouncerStyles: React.CSSProperties = {\n  border: 0,\n  clip: 'rect(0 0 0 0)',\n  height: '1px',\n  margin: '-1px',\n  overflow: 'hidden',\n  padding: 0,\n  position: 'absolute',\n  top: 0,\n  width: '1px',\n\n  // https://medium.com/@jessebeach/beware-smushed-off-screen-accessible-text-5952a4c2cbfe\n  whiteSpace: 'nowrap',\n  wordWrap: 'normal',\n}\n\nexport const RouteAnnouncer = () => {\n  const { asPath } = useRouter()\n  const [routeAnnouncement, setRouteAnnouncement] = React.useState('')\n\n  // Only announce the path change, but not for the first load because screen\n  // reader will do that automatically.\n  const previouslyLoadedPath = React.useRef(asPath)\n\n  // Every time the path changes, announce the new page’s title following this\n  // priority: first the document title (from head), otherwise the first h1, or\n  // if none of these exist, then the pathname from the URL. This methodology is\n  // inspired by <PERSON><PERSON>’s accessible client routing user testing. More\n  // information can be found here:\n  // https://www.gatsbyjs.com/blog/2019-07-11-user-testing-accessible-client-routing/\n  React.useEffect(\n    () => {\n      // If the path hasn't change, we do nothing.\n      if (previouslyLoadedPath.current === asPath) return\n      previouslyLoadedPath.current = asPath\n\n      if (document.title) {\n        setRouteAnnouncement(document.title)\n      } else {\n        const pageHeader = document.querySelector('h1')\n        const content = pageHeader?.innerText ?? pageHeader?.textContent\n\n        setRouteAnnouncement(content || asPath)\n      }\n    },\n    // TODO: switch to pathname + query object of dynamic route requirements\n    [asPath]\n  )\n\n  return (\n    <p\n      aria-live=\"assertive\" // Make the announcement immediately.\n      id=\"__next-route-announcer__\"\n      role=\"alert\"\n      style={nextjsRouteAnnouncerStyles}\n    >\n      {routeAnnouncement}\n    </p>\n  )\n}\n\nexport default RouteAnnouncer\n", "import { parsePath } from './parse-path'\n\n/**\n * Adds the provided prefix to the given path. It first ensures that the path\n * is indeed starting with a slash.\n */\nexport function addPathPrefix(path: string, prefix?: string) {\n  if (!path.startsWith('/') || !prefix) {\n    return path\n  }\n\n  const { pathname, query, hash } = parsePath(path)\n  return `${prefix}${pathname}${query}${hash}`\n}\n", "/**\n * Stores the Trusted Types Policy. Starts as undefined and can be set to null\n * if Trusted Types is not supported in the browser.\n */\nlet policy: TrustedTypePolicy | null | undefined\n\n/**\n * Getter for the Trusted Types Policy. If it is undefined, it is instantiated\n * here or set to null if Trusted Types is not supported in the browser.\n */\nfunction getPolicy() {\n  if (typeof policy === 'undefined' && typeof window !== 'undefined') {\n    policy =\n      window.trustedTypes?.createPolicy('nextjs', {\n        createHTML: (input) => input,\n        createScript: (input) => input,\n        createScriptURL: (input) => input,\n      }) || null\n  }\n\n  return policy\n}\n\n/**\n * Unsafely promote a string to a TrustedScriptURL, falling back to strings\n * when Trusted Types are not available.\n * This is a security-sensitive function; any use of this function\n * must go through security review. In particular, it must be assured that the\n * provided string will never cause an XSS vulnerability if used in a context\n * that will cause a browser to load and execute a resource, e.g. when\n * assigning to script.src.\n */\nexport function __unsafeCreateTrustedScriptURL(\n  url: string\n): TrustedScriptURL | string {\n  return getPolicy()?.createScriptURL(url) || url\n}\n", "// Format function modified from nodejs\n// Copyright Joyent, Inc. and other Node contributors.\n//\n// Permission is hereby granted, free of charge, to any person obtaining a\n// copy of this software and associated documentation files (the\n// \"Software\"), to deal in the Software without restriction, including\n// without limitation the rights to use, copy, modify, merge, publish,\n// distribute, sublicense, and/or sell copies of the Software, and to permit\n// persons to whom the Software is furnished to do so, subject to the\n// following conditions:\n//\n// The above copyright notice and this permission notice shall be included\n// in all copies or substantial portions of the Software.\n//\n// THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS\n// OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\n// MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN\n// NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM,\n// DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR\n// OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE\n// USE OR OTHER DEALINGS IN THE SOFTWARE.\n\nimport type { UrlObject } from 'url'\nimport type { ParsedUrlQuery } from 'querystring'\nimport * as querystring from './querystring'\n\nconst slashedProtocols = /https?|ftp|gopher|file/\n\nexport function formatUrl(urlObj: UrlObject) {\n  let { auth, hostname } = urlObj\n  let protocol = urlObj.protocol || ''\n  let pathname = urlObj.pathname || ''\n  let hash = urlObj.hash || ''\n  let query = urlObj.query || ''\n  let host: string | false = false\n\n  auth = auth ? encodeURIComponent(auth).replace(/%3A/i, ':') + '@' : ''\n\n  if (urlObj.host) {\n    host = auth + urlObj.host\n  } else if (hostname) {\n    host = auth + (~hostname.indexOf(':') ? `[${hostname}]` : hostname)\n    if (urlObj.port) {\n      host += ':' + urlObj.port\n    }\n  }\n\n  if (query && typeof query === 'object') {\n    query = String(querystring.urlQueryToSearchParams(query as ParsedUrlQuery))\n  }\n\n  let search = urlObj.search || (query && `?${query}`) || ''\n\n  if (protocol && !protocol.endsWith(':')) protocol += ':'\n\n  if (\n    urlObj.slashes ||\n    ((!protocol || slashedProtocols.test(protocol)) && host !== false)\n  ) {\n    host = '//' + (host || '')\n    if (pathname && pathname[0] !== '/') pathname = '/' + pathname\n  } else if (!host) {\n    host = ''\n  }\n\n  if (hash && hash[0] !== '#') hash = '#' + hash\n  if (search && search[0] !== '?') search = '?' + search\n\n  pathname = pathname.replace(/[?#]/g, encodeURIComponent)\n  search = search.replace('#', '%23')\n\n  return `${protocol}${host}${pathname}${search}${hash}`\n}\n\nexport const urlObjectKeys = [\n  'auth',\n  'hash',\n  'host',\n  'hostname',\n  'href',\n  'path',\n  'pathname',\n  'port',\n  'protocol',\n  'query',\n  'search',\n  'slashes',\n]\n\nexport function formatWithValidation(url: UrlObject): string {\n  if (process.env.NODE_ENV === 'development') {\n    if (url !== null && typeof url === 'object') {\n      Object.keys(url).forEach((key) => {\n        if (!urlObjectKeys.includes(key)) {\n          console.warn(\n            `Unknown key passed via urlObject into url.format: ${key}`\n          )\n        }\n      })\n    }\n  }\n\n  return formatUrl(url)\n}\n", "import type { Group } from './route-regex'\nimport { DecodeError } from '../../utils'\nimport type { Params } from '../../../../server/request/params'\n\nexport interface RouteMatchFn {\n  (pathname: string): false | Params\n}\n\ntype RouteMatcherOptions = {\n  // We only use the exec method of the RegExp object. This helps us avoid using\n  // type assertions that the passed in properties are of the correct type.\n  re: Pick<RegExp, 'exec'>\n  groups: Record<string, Group>\n}\n\nexport function getRouteMatcher({\n  re,\n  groups,\n}: RouteMatcherOptions): RouteMatchFn {\n  return (pathname: string) => {\n    const routeMatch = re.exec(pathname)\n    if (!routeMatch) return false\n\n    const decode = (param: string) => {\n      try {\n        return decodeURIComponent(param)\n      } catch {\n        throw new DecodeError('failed to decode param')\n      }\n    }\n\n    const params: Params = {}\n    for (const [key, group] of Object.entries(groups)) {\n      const match = routeMatch[group.pos]\n      if (match !== undefined) {\n        if (group.repeat) {\n          params[key] = match.split('/').map((entry) => decode(entry))\n        } else {\n          params[key] = decode(match)\n        }\n      }\n    }\n\n    return params\n  }\n}\n", "import React from 'react'\nimport type { NextRouter } from './router/router'\n\nexport const RouterContext = React.createContext<NextRouter | null>(null)\n\nif (process.env.NODE_ENV !== 'production') {\n  RouterContext.displayName = 'RouterContext'\n}\n", "import type { DomainLocale } from '../../../server/config-shared'\n\nexport function detectDomainLocale(\n  domainItems?: readonly DomainLocale[],\n  hostname?: string,\n  detectedLocale?: string\n) {\n  if (!domainItems) return\n\n  if (detectedLocale) {\n    detectedLocale = detectedLocale.toLowerCase()\n  }\n\n  for (const item of domainItems) {\n    // remove port if present\n    const domainHostname = item.domain?.split(':', 1)[0].toLowerCase()\n    if (\n      hostname === domainHostname ||\n      detectedLocale === item.defaultLocale.toLowerCase() ||\n      item.locales?.some((locale) => locale.toLowerCase() === detectedLocale)\n    ) {\n      return item\n    }\n  }\n}\n"], "names": ["handleSmoothScroll", "fn", "options", "onlyHashChange", "htmlElement", "document", "documentElement", "existing", "style", "scroll<PERSON>eh<PERSON>or", "dontForceLayout", "getClientRects", "addLocale", "path", "args", "normalizePathTrailingSlash", "require", "AppRouterContext", "GlobalLayoutRouterContext", "LayoutRouterContext", "MissingSlotContext", "TemplateContext", "React", "createContext", "Set", "runtimeConfig", "setConfig", "config<PERSON><PERSON><PERSON>", "HTTPAccessErrorStatus", "HTTP_ERROR_FALLBACK_ERROR_CODE", "getAccessFallbackErrorTypeByStatus", "getAccessFallbackHTTPStatus", "isHTTPAccessFallbackError", "NOT_FOUND", "FORBIDDEN", "UNAUTHORIZED", "ALLOWED_CODES", "Object", "values", "error", "digest", "prefix", "httpStatus", "split", "has", "Number", "status", "normalizeAppPath", "normalizeRscURL", "route", "ensureLeadingSlash", "reduce", "pathname", "segment", "index", "segments", "isGroupSegment", "length", "url", "replace", "onRecoverableError", "errorInfo", "cause", "isError", "stitchedError", "getReactStitchedError", "isBailoutToCSRError", "reportGlobalError", "PathParamsContext", "PathnameContext", "SearchParamsContext", "self", "__next_set_public_path__", "__webpack_public_path__", "isDynamicRoute", "TEST_ROUTE", "TEST_STRICT_ROUTE", "strict", "isInterceptionRouteAppPath", "extractInterceptionRouteInformation", "interceptedRoute", "test", "DecodeError", "MiddlewareNotFoundError", "MissingStaticPage", "NormalizeError", "PageNotFoundError", "SP", "ST", "WEB_VITALS", "execOnce", "getDisplayName", "getLocationOrigin", "getURL", "isAbsoluteUrl", "isResSent", "loadGetInitialProps", "normalizeRepeatedSlashes", "stringifyError", "result", "used", "ABSOLUTE_URL_REGEX", "protocol", "hostname", "port", "window", "location", "href", "origin", "substring", "Component", "displayName", "name", "res", "finished", "headersSent", "urlParts", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "slice", "join", "App", "ctx", "getInitialProps", "props", "performance", "every", "method", "Error", "constructor", "page", "code", "message", "JSON", "stringify", "stack", "omit", "object", "keys", "omitted", "for<PERSON>ach", "includes", "key", "getSortedRouteObjects", "getSortedRoutes", "UrlNode", "insert", "url<PERSON><PERSON>", "_insert", "filter", "Boolean", "smoosh", "_smoosh", "childrenPaths", "children", "sort", "slug<PERSON><PERSON>", "splice", "indexOf", "restSlugName", "optionalRestSlugName", "routes", "map", "get", "c", "prev", "curr", "push", "placeholder", "r", "unshift", "url<PERSON><PERSON>s", "slug<PERSON><PERSON><PERSON>", "isCatchAll", "nextSegment", "startsWith", "endsWith", "segmentName", "isOptional", "handleSlug", "previousSlug", "nextSlug", "slug", "set", "Map", "normalizedPages", "root", "pagePath", "objects", "getter", "indexes", "pathnames", "i", "PathnameContextProviderAdapter", "adaptForAppRouterInstance", "adaptForPathParams", "adaptForSearchParams", "pagesRouter", "back", "forward", "refresh", "reload", "hmrRefresh", "scroll", "undefined", "prefetch", "router", "isReady", "asPathToSearchParams", "<PERSON><PERSON><PERSON>", "URLSearchParams", "query", "pathParams", "getRouteRegex", "groups", "ref", "useRef", "isAutoExport", "value", "useMemo", "current", "<PERSON><PERSON><PERSON><PERSON>", "URL", "_", "Provider", "has<PERSON>ase<PERSON><PERSON>", "pathHasPrefix", "basePath", "module", "exports", "MODERN_BROWSERSLIST_TARGET", "getNextPathnameInfo", "i18n", "trailingSlash", "nextConfig", "info", "removePathPrefix", "pathnameNoDataPrefix", "paths", "buildId", "parseData", "i18nProvider", "analyze", "normalizeLocalePath", "locales", "locale", "detectedLocale", "compareRouterStates", "a", "b", "stateKeys", "query<PERSON>eys", "j", "query<PERSON><PERSON>", "hasOwnProperty", "initialData", "page<PERSON><PERSON>der", "appElement", "headManager", "lastAppProps", "lastRenderReject", "CachedApp", "onPerfEntry", "CachedComponent", "defaultLocale", "emitter", "hydrate", "initialize", "version", "process", "env", "mitt", "looseToArray", "input", "call", "initialMatchesMiddleware", "Container", "componentDidCatch", "componentErr", "componentDidMount", "scrollToHash", "isSsr", "nextExport", "search", "__N_SSG", "String", "assign", "urlQueryToSearchParams", "_h", "shallow", "catch", "err", "cancelled", "componentDidUpdate", "hash", "el", "getElementById", "setTimeout", "scrollIntoView", "render", "opts", "parse", "textContent", "__NEXT_DATA__", "assetPrefix", "serverRuntimeConfig", "publicRuntimeConfig", "removeBasePath", "detectDomainLocale", "parseRelativeUrl", "formatUrl", "parsedAs", "localePathResult", "detectedDomain", "<PERSON><PERSON><PERSON><PERSON>", "initScriptLoader", "<PERSON><PERSON><PERSON><PERSON>", "register", "f", "routeLoader", "onEntrypoint", "__NEXT_P", "p", "initHeadManager", "getIsSsr", "renderApp", "appProps", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "adaptedForAppRouter", "renderError", "console", "autoExport", "RouterContext", "makePublicRouterInstance", "HeadManagerContext", "ImageConfigContext", "wrapApp", "wrappedAppProps", "renderErrorProps", "loadPage", "then", "ErrorComponent", "styleSheets", "default", "errorModule", "m", "AppTree", "appCtx", "Promise", "resolve", "doR<PERSON>", "initProps", "Head", "callback", "useLayoutEffect", "performanceMarks", "navigationStart", "beforeRender", "afterRender", "afterHydrate", "routeChange", "performanceMeasures", "hydration", "beforeHydration", "routeChangeToRender", "reactRoot", "shouldHydrate", "clearMarks", "mark", "markHydrateComplete", "getEntriesByName", "measure", "markRenderComplete", "navStartEntries", "clearMeasures", "Root", "callbacks", "resolvePromise", "canceled", "renderPromise", "reject", "onRootCommit", "onStart", "currentHrefs", "currentStyleTags", "querySelectorAll", "tag", "getAttribute", "noscript", "querySelector", "nonce", "text", "styleTag", "createElement", "setAttribute", "head", "append<PERSON><PERSON><PERSON>", "createTextNode", "elem", "onHeadCommit", "desiredHrefs", "s", "idx", "removeAttribute", "referenceNode", "targetTag", "parentNode", "insertBefore", "nextS<PERSON>ling", "<PERSON><PERSON><PERSON><PERSON>", "x", "y", "scrollTo", "Portal", "type", "RouteAnnouncer", "domEl", "reactEl", "startTransition", "ReactDOM", "hydrateRoot", "renderingProps", "isHydratePass", "renderErr", "getProperError", "initialErr", "appEntrypoint", "whenEntrypoint", "component", "app", "mod", "reportWebVitals", "perfStartEntry", "id", "startTime", "duration", "entryType", "entries", "attribution", "uniqueID", "Date", "now", "Math", "floor", "random", "webVitals", "label", "pageEntrypoint", "__NEXT_PRELOADREADY", "dynamicIds", "createRouter", "initialProps", "subscription", "domainLocales", "isPreview", "_initialMatchesMiddlewarePromise", "renderCtx", "initial", "searchParams", "getAssetPathFromRoute", "ext", "reportError", "globalThis", "HTML_LIMITED_BOT_UA_RE", "HTML_LIMITED_BOT_UA_RE_STRING", "getBotType", "isBot", "HEADLESS_BROWSER_BOT_UA_RE", "source", "isHtmlLimitedBotUA", "userAgent", "isDomBotUA", "VALID_LOADERS", "imageConfigDefault", "deviceSizes", "imageSizes", "loader", "loaderFile", "domains", "disableStaticImages", "minimumCacheTTL", "formats", "dangerouslyAllowSVG", "contentSecurityPolicy", "contentDispositionType", "localPatterns", "remotePatterns", "qualities", "unoptimized", "encodeURIPath", "file", "encodeURIComponent", "addBasePath", "required", "addPathPrefix", "next", "removeLocale", "parsePath", "pathLower", "toLowerCase", "localeLower", "removeTrailingSlash", "denormalizePagePath", "_page", "normalizePathSep", "isNextRouterError", "isRedirectError", "isParallelRouteSegment", "addSearchParamsIfPageSegment", "isPageSegment", "PAGE_SEGMENT_KEY", "stringified<PERSON><PERSON>y", "DEFAULT_SEGMENT_KEY", "interpolateAs", "asPathname", "interpolatedRoute", "dynamicRegex", "dynamicGroups", "dynamicMatches", "getRouteMatcher", "params", "param", "repeat", "optional", "replaced", "Array", "isArray", "cache", "WeakMap", "lowercasedLocales", "resolveHref", "resolveAs", "base", "urlAsString", "formatWithValidation", "urlProtoMatch", "match", "urlAsStringNoProto", "normalizedUrl", "isLocalURL", "finalUrl", "interpolatedAs", "searchParamsToUrlQuery", "resolvedHref", "Router", "useRouter", "with<PERSON><PERSON><PERSON>", "singletonRouter", "readyCallbacks", "ready", "url<PERSON><PERSON><PERSON><PERSON>ields", "core<PERSON><PERSON><PERSON><PERSON><PERSON>s", "getRouter", "defineProperty", "events", "field", "routerEvents", "on", "event", "eventField", "char<PERSON>t", "toUpperCase", "_singletonRouter", "useContext", "cb", "instance", "property", "scopedRouter", "RedirectStatusCode", "getPageList", "getClientBuildManifest", "manifest", "sortedPages", "getMiddleware", "__MIDDLEWARE_MATCHERS", "middlewareMatchers", "getDataHref", "hrefPathname", "skipInterpolation", "dataRoute", "_isSsg", "promisedSsgManifest", "loadRoute", "styles", "o", "content", "createRouteLoader", "__SSG_MANIFEST", "__SSG_MANIFEST_CB", "Span", "end", "endTime", "state", "onSpanEnd", "attributes", "Tracer", "startSpan", "handleSpanEnd", "_emitter", "off", "emit", "span", "BailoutToCSRError", "BAILOUT_TO_CSR", "reason", "getObjectClassLabel", "prototype", "toString", "isPlainObject", "getPrototypeOf", "ignorePrefix", "lower", "ComposedComponent", "WithRouterWrapper", "origGetInitialProps", "updateElements", "isEqualNode", "oldTag", "newTag", "HTMLElement", "cloneTag", "cloneNode", "mountedInstances", "updateHead", "tags", "h", "components", "titleComponent", "title", "headEl", "oldTags", "metaCharset", "add", "newTags", "reactElementToDOM", "setAttributesFromProps", "dangerouslySetInnerHTML", "innerHTML", "__html", "isNew", "delete", "tagName", "prepend", "isAssetError", "<PERSON><PERSON><PERSON><PERSON>", "withFuture", "generator", "resolver", "entry", "future", "prom", "ASSET_LOAD_ERROR", "Symbol", "canPrefetch", "hasPrefetch", "link", "MSInputMethodContext", "documentMode", "relList", "supports", "getAssetQueryString", "getDeploymentIdQueryOrEmptyString", "resolvePromiseWithTimeout", "ms", "requestIdleCallback", "__BUILD_MANIFEST", "onBuildManifest", "__BUILD_MANIFEST_CB", "MS_MAX_IDLE_DELAY", "getFilesForRoute", "allFiles", "scripts", "v", "__unsafeCreateTrustedScriptURL", "css", "entrypoints", "loadedScripts", "maybeExecuteScript", "src", "script", "appendScript", "onload", "onerror", "crossOrigin", "body", "fetchStyleSheet", "fetch", "credentials", "ok", "execute", "old", "devBuildPromiseResolve", "all", "entrypoint", "finally", "cn", "navigator", "connection", "saveData", "effectiveType", "output", "prefetchViaDom", "as", "selector", "rel", "APP_BUILD_MANIFEST", "APP_CLIENT_INTERNALS", "APP_PATHS_MANIFEST", "APP_PATH_ROUTES_MANIFEST", "BARREL_OPTIMIZATION_PREFIX", "BLOCKED_PAGES", "BUILD_ID_FILE", "BUILD_MANIFEST", "CLIENT_PUBLIC_FILES_PATH", "CLIENT_REFERENCE_MANIFEST", "CLIENT_STATIC_FILES_PATH", "CLIENT_STATIC_FILES_RUNTIME_AMP", "CLIENT_STATIC_FILES_RUNTIME_MAIN", "CLIENT_STATIC_FILES_RUNTIME_MAIN_APP", "CLIENT_STATIC_FILES_RUNTIME_POLYFILLS", "CLIENT_STATIC_FILES_RUNTIME_POLYFILLS_SYMBOL", "CLIENT_STATIC_FILES_RUNTIME_REACT_REFRESH", "CLIENT_STATIC_FILES_RUNTIME_WEBPACK", "COMPILER_INDEXES", "COMPILER_NAMES", "CONFIG_FILES", "DEFAULT_RUNTIME_WEBPACK", "DEFAULT_SANS_SERIF_FONT", "DEFAULT_SERIF_FONT", "DEV_CLIENT_MIDDLEWARE_MANIFEST", "DEV_CLIENT_PAGES_MANIFEST", "DYNAMIC_CSS_MANIFEST", "EDGE_RUNTIME_WEBPACK", "EDGE_UNSUPPORTED_NODE_APIS", "EXPORT_DETAIL", "EXPORT_MARKER", "FUNCTIONS_CONFIG_MANIFEST", "IMAGES_MANIFEST", "INTERCEPTION_ROUTE_REWRITE_MANIFEST", "MIDDLEWARE_BUILD_MANIFEST", "MIDDLEWARE_MANIFEST", "MIDDLEWARE_REACT_LOADABLE_MANIFEST", "NEXT_BUILTIN_DOCUMENT", "NEXT_FONT_MANIFEST", "PAGES_MANIFEST", "PHASE_DEVELOPMENT_SERVER", "PHASE_EXPORT", "PHASE_INFO", "PHASE_PRODUCTION_BUILD", "PHASE_PRODUCTION_SERVER", "PHASE_TEST", "PRERENDER_MANIFEST", "REACT_LOADABLE_MANIFEST", "ROUTES_MANIFEST", "RSC_MODULE_TYPES", "SERVER_DIRECTORY", "SERVER_FILES_MANIFEST", "SERVER_PROPS_ID", "SERVER_REFERENCE_MANIFEST", "STATIC_PROPS_ID", "STATIC_STATUS_PAGES", "STRING_LITERAL_DROP_BUNDLE", "SUBRESOURCE_INTEGRITY_MANIFEST", "SYSTEM_ENTRYPOINTS", "TRACE_OUTPUT_VERSION", "TURBOPACK_CLIENT_MIDDLEWARE_MANIFEST", "TURBO_TRACE_DEFAULT_MEMORY_LIMIT", "UNDERSCORE_NOT_FOUND_ROUTE", "UNDERSCORE_NOT_FOUND_ROUTE_ENTRY", "WEBPACK_STATS", "client", "server", "edgeServer", "xAvgCharWidth", "azAvgWidth", "unitsPerEm", "locationOrigin", "resolved", "REDIRECT_ERROR_CODE", "RedirectType", "errorCode", "destination", "statusCode", "at", "isNaN", "INTERCEPTION_ROUTE_MARKERS", "find", "interceptingRoute", "marker", "concat", "splitInterceptingRoute", "DOMAttributeNames", "acceptCharset", "className", "htmlFor", "httpEquiv", "noModule", "ignoreProps", "isBooleanScriptAttribute", "attr", "getNamedMiddlewareRegex", "getNamedRouteRegex", "parseParameter", "PARAMETER_PATTERN", "parseMatchedParameter", "getParametrizedRoute", "includeSuffix", "includePrefix", "groupIndex", "markerMatch", "paramMatch<PERSON>", "pos", "escapeStringRegexp", "parameterizedRoute", "normalizedRoute", "excludeOptionalTrailingSlash", "re", "getSafeKeyFromSegment", "pattern", "<PERSON><PERSON><PERSON><PERSON>", "getSafeRouteKey", "routeKeys", "keyPrefix", "backreferenceDuplicateKeys", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "parseInt", "duplicate<PERSON>ey", "interceptionPrefix", "getNamedParametrizedRoute", "prefixRouteKeys", "routeKey", "fromCharCode", "hasInterceptionMarker", "some", "NEXT_INTERCEPTION_MARKER_PREFIX", "NEXT_QUERY_PARAM_PREFIX", "namedParameterizedRoute", "namedRegex", "catchAll", "catchAllRegex", "catchAllGroupedRegex", "parse<PERSON><PERSON>y", "globalBase", "resolvedBase", "formatNextPathnameInfo", "addPathSuffix", "REACT_ERROR_STACK_BOTTOM_FRAME", "REACT_ERROR_STACK_BOTTOM_FRAME_REGEX", "RegExp", "isErrorInstance", "originStack", "originMessage", "stackLines", "indexOfSplit", "findIndex", "line", "newStack", "newError", "copyNextErrorCode", "appendOwnerStack", "captureOwnerStack", "ownerStack", "reHasRegExp", "reReplaceRegExp", "str", "cancelIdleCallback", "bind", "start", "didTimeout", "timeRemaining", "max", "clearTimeout", "create<PERSON><PERSON>", "matchesMiddleware", "buildCancellationError", "matchers", "cleanedAs", "asWithBasePathAndLocale", "regexp", "strip<PERSON><PERSON>in", "prepareUrlAs", "resolvedAs", "hrefWasAbsolute", "asWasAbsolute", "preparedUrl", "preparedAs", "resolveDynamicRoute", "pages", "cleanPathname", "withMiddlewareEffects", "fetchData", "data", "effect", "getMiddlewareData", "response", "rewriteHeader", "headers", "rewriteTarget", "<PERSON><PERSON><PERSON>", "MATCHED_PATH_HEADER", "parsedRewriteTarget", "pathnameInfo", "fsPathname", "__rewrites", "rewrites", "parsedSource", "resolvedPathname", "matches", "redirectTarget", "newAs", "newUrl", "dataHref", "json", "cache<PERSON>ey", "SSG_DATA_NOT_FOUND", "tryToParseAsJSON", "fetchNextData", "inflightCache", "isPrefetch", "hasMiddleware", "isServerRender", "parseJSON", "persistCache", "isBackground", "unstable_skipClientCache", "getData", "fetchRetry", "attempts", "purpose", "notFound", "NODE_ENV", "handleHardNavigation", "getCancelledHandler", "cancel", "clc", "handleCancelled", "history", "change", "_bfl", "skipNavigate", "_bfl_s", "_bfl_d", "staticFilterData", "dynamicFilterData", "<PERSON><PERSON><PERSON><PERSON>", "__routerFilterStatic", "__routerFilterDynamic", "numHashes", "numItems", "errorRate", "import", "matchesBflStatic", "matchesBflDynamic", "curAs", "allowMatchCurrent", "pathsToCheck", "asNoSlash", "asNoSlashLocale", "normalizedAS", "contains", "curAs<PERSON><PERSON>s", "currentPart", "forcedScroll", "routeInfo", "isQueryUpdating", "shouldResolveHref", "_shouldResolveHref", "nextState", "readyStateChange", "prevLocale", "didNavigate", "isLocaleDomain", "domain", "asNoBasePath", "http", "routeProps", "_inFlightRoute", "localeChange", "onlyAHashChange", "changeState", "parsed", "urlIsNew", "parsedAsPathname", "__appRouter", "isMiddlewareRewrite", "isMiddlewareMatch", "routeMatch", "routeRegex", "shouldInterpolate", "missingParams", "isErrorRoute", "getRouteInfo", "cleanedParsedPathname", "rewriteAs", "prefixedAs", "localeResult", "cur<PERSON><PERSON>eMatch", "unstable_scriptLoader", "handleClientScriptLoad", "__N_SSP", "pageProps", "__N_REDIRECT", "__N_REDIRECT_BASE_PATH", "parsedHref", "__N_PREVIEW", "notFoundRoute", "fetchComponent", "isNotFound", "isValidShallowRoute", "upcomingScrollState", "shouldScroll", "resetScroll", "upcomingRouterState", "e", "lang", "hashRegex", "_shallow", "__N", "_key", "handleRouteInfoError", "loadErrorFail", "gipErr", "routeInfoErr", "requestedRoute", "existingInfo", "cachedRouteInfo", "fetchNextDataParams", "sbc", "sdc", "resolvedRoute", "isAPIRoute", "wasBailedPrefetch", "shouldFetchData", "_getData", "fetched", "sub", "beforePopState", "_bps", "oldUrlNoHash", "oldHash", "newUrlNoHash", "newHash", "rawHash", "decodeURIComponent", "idEl", "nameEl", "getElementsByName", "urlPathname", "originalPathname", "isSsg", "priority", "componentResult", "_wrapApp", "isFirstPopStateEvent", "onPopState", "__NA", "autoExportDynamic", "gssp", "gip", "isExperimentalCompile", "appGip", "gsp", "__NEXT_HAS_REWRITES", "addEventListener", "from", "items", "DEFAULT_ERROR_RATE", "item", "export", "numBits", "bitArray", "getHashValues", "hashValues", "murmurhash2", "imul", "charCodeAt", "ceil", "log", "fill", "portalNode", "setPortalNode", "useState", "useEffect", "element", "createPortal", "stringifyUrlQueryParam", "append", "target", "searchParamsList", "hashIndex", "queryIndex", "<PERSON><PERSON><PERSON><PERSON>", "create", "handler", "evts", "suffix", "withoutPrefix", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Load<PERSON>ache", "insertStylesheets", "preinit", "stylesheets", "stylesheet", "loadScript", "onLoad", "onReady", "strategy", "onError", "afterLoad", "loadPromise", "scriptLoaderItems", "<PERSON><PERSON><PERSON>", "restProps", "updateScripts", "appDir", "hasOnReadyEffectCalled", "hasLoadScriptEffectCalled", "readyState", "styleSrc", "preload", "integrity", "nextjsRouteAnnouncerStyles", "border", "clip", "height", "margin", "overflow", "padding", "position", "top", "width", "whiteSpace", "wordWrap", "routeAnnouncement", "setRouteAnnouncement", "previouslyLoaded<PERSON><PERSON>", "pageHeader", "innerText", "aria-live", "role", "policy", "getPolicy", "trustedTypes", "createPolicy", "createHTML", "createScript", "createScriptURL", "urlObjectKeys", "slashedProtocols", "url<PERSON>bj", "auth", "host", "querystring", "slashes", "exec", "decode", "group", "domainItems", "domainHostname"], "sourceRoot": "", "ignoreList": [1, 19, 23, 25, 32, 33, 37, 59, 61, 62, 65, 79, 86]}