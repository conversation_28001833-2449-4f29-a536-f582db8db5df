{"version": 3, "file": "static/chunks/pages/operation/[...routes]-7ce9e9fbadcf3ca6.js", "mappings": "+NAgHA,MArGkCA,IAC9B,GAAM,CAAEC,CAAC,CAAE,CAAGC,CAAAA,EAAAA,EAAAA,EAAAA,CAAcA,CAAC,QAoGlBC,EAlGL,CAACC,EAAUC,EAAY,CAAGC,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAC,CAkGNH,EAAC,CAhGpCI,CAAAA,EAAAA,EAAAA,SAAAA,CAASA,CAAC,SACNC,SAAAA,EAAAA,SAASC,cAAc,CAAC,wBAAxBD,EAA+CE,MAAM,CAACN,EAAU,CAAhEI,GACJ,EAAG,CAACJ,EAAS,EAWb,IAAMO,EAAoB,CACtB,EAAG,uBACH,EAAG,yBACH,EAAG,2BACH,EAAG,wBACH,EAAG,4BACH,EAAG,2BACH,EAAG,4BACP,EAEA,MACI,+BACI,UAACC,EAAAA,CAAGA,CAAAA,UACA,UAACC,EAAAA,CAAGA,CAAAA,CAACC,UAAU,mBAAmBC,GAAI,YAClC,WAACC,MAAAA,CAAIF,UAAU,oBAAoBG,MAAO,CAAEC,UAAW,MAAO,YACzDlB,EAAMmB,SAAS,EAAInB,EAAMmB,SAAS,CAACC,QAAQ,CAACC,MAAM,CAAG,GAClD,WAACL,MAAAA,WACG,UAACA,MAAAA,CACGF,UAAU,OACVQ,QA5BZ,CA4BqBC,IA3BrC,IAAMC,EAAOpB,EAAW,GACxBC,EAAYmB,EAAO,EAAI,EAAIA,EAC/B,EA0BgCP,MAAO,CAAEQ,OAAQ,SAAU,WAE3B,UAACC,OAAAA,UACG,UAACC,EAAAA,CAAeA,CAAAA,CAACC,KAAMC,EAAAA,GAAWA,OAG1C,UAACb,MAAAA,CACGF,UAAU,OACVQ,QAhCX,CAgCoBQ,IA/BrCzB,EAAYD,EAAW,GAC3B,EA+BgCa,MAAO,CAAEQ,OAAQ,SAAU,WAE3B,UAACC,OAAAA,UACG,UAACC,EAAAA,CAAeA,CAAAA,CAACC,KAAMG,EAAAA,GAAYA,UAMnD,UAACf,MAAAA,CAAIF,UAAU,wBAAwBkB,GAAG,8BACtC,UAACC,KAAAA,CAAGnB,UAAU,uBACTd,EAAMmB,SAAS,EACZnB,EAAMmB,SAAS,CAACC,QAAQ,EACxBpB,EAAMmB,SAAS,CAACC,QAAQ,CAACc,GAAG,CAAC,CAACC,EAAWC,IAEjC,WAACC,KAAAA,CACGpB,MAAO,CACHqB,OAAQtC,EAAMmB,SAAS,CAACC,QAAQ,CAACC,MAAM,CAAGe,CAC9C,YAGA,UAACpB,MAAAA,CAAIF,UAAU,wBACX,UAACyB,MAAAA,CACGC,IAAK7B,CAAY,CAACwB,EAAKM,SAAS,CAAC,CACjCC,MAAM,OACNC,OAAO,WAGdR,EAAKS,SAAS,CACX,UAACC,IAAAA,CAAE/B,UAAU,sBAAcqB,EAAKS,SAAS,GAEzC,UAACC,IAAAA,CAAE/B,UAAU,sBAAcb,EAAE,aAEhCkC,EAAKW,IAAI,CACN,UAACD,IAAAA,CAAE/B,UAAU,qBACRiC,IAAOZ,EAAKW,IAAI,EAAEE,MAAM,CAhF/C,eAqFkB,UAACH,CALUE,GAKVF,CAAE/B,UAAU,qBAAab,EAAE,cArB3BmC,gBAiCrD,mKCgGA,MA9LsB,IACpB,GAAM,CAAEnC,CAAC,CAAE,CAAGC,CAAAA,EAAAA,EAAAA,EAAAA,CAAcA,CAAC,QA6LhB+C,EA3LP,CAACC,EAAeC,EAAiB,CAAG7C,CAAAA,EAAAA,EAAAA,EA2Lf,MA3LeA,CAAQA,CAAM,CACtD8C,MAAO,GACPhC,SAAU,EAAE,CACZiC,YAAa,GACbC,YAAa,CAAEF,MAAO,EAAG,EACzBG,OAAQ,EAAE,CACVC,SAAU,CAAEJ,MAAO,EAAG,EACtBK,WAAY,GACZC,WAAY,GACZC,QAAS,CAAEP,MAAO,EAAG,EACrBQ,OAAQ,CAAER,MAAO,EAAG,EACpBS,WAAY,GACZC,SAAU,GACVC,SAAU,EAAE,CACZC,OAAQ,EAAE,CACVC,WAAY,EAAE,CACdzD,SAAU,EAAE,CACZ0D,QAAS,EAAE,GAGP,CAACC,EAAsBC,EAAwB,CAAG9D,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,EAAC,GAE3D,CAAC+D,EAASC,EAAW,CAAGhE,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,EAAC,GACjC,CAACiE,EAAUC,EAAY,CAAGlE,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAC,EAAE,EACrC,CAACmE,EAAgBC,EAAmB,CAAGpE,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAC,EAAE,EAClD,CAACqE,EAAYC,EAAc,CAAGtE,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,EAAC,GAEvCuE,EAA2B,CAC/BC,KAAM,CAAEC,eAAgB,KAAM,EAC9BC,UAAU,CACZ,EAEMC,EAA6B,CACjCH,KAAM,CAAEC,eAAgB,KAAM,EAC9BG,gBAAgB,CAClB,EAcMC,EAAsB,UAC1B,IAAMC,EAAkB,EAAE,CAC1Bd,GAAW,GACX,IAAMe,EAAiB,MAAMC,EAAAA,CAAUA,CAACC,GAAG,CACzC,cAA8B,OAAhBvF,EAAMwF,MAAM,CAAC,EAAE,EAC7BX,GAGAQ,GACAI,MAAMC,OAAO,CAACL,IACdA,EAAehE,MAAM,EAAI,GACzBgE,CAAc,CAAC,EAAE,CAAC7E,QAAQ,EAC1B6E,CAAc,CAAC,EAAE,CAAC7E,QAAQ,CAACa,MAAM,EAAI,GACrC,CACAgE,EAAeM,OAAO,CAAC,IACrBC,EAAQpF,QAAQ,EACdoF,EAAQpF,QAAQ,CAACa,MAAM,CAAG,GAC1BuE,EAAQpF,QAAQ,CAAC0B,GAAG,CAAC,CAAC2D,EAAUzD,KAE9ByD,EAAIxC,WAAW,CADKuC,EAAQpF,QAAQ,CAAC4B,EAAE,CAAC0D,MAAM,CAE9CV,EAAWW,IAAI,CAACF,EAClB,EACJ,GACArB,EAAYY,IAEdd,GAAW,EACb,EAEM0B,EAAmB,UACvB,IAAMZ,EAAkB,EAAE,CAC1Bd,GAAW,GACX,IAAMe,EAAiB,MAAMC,EAAAA,CAAUA,CAACC,GAAG,CACzC,cAA8B,OAAhBvF,EAAMwF,MAAM,CAAC,EAAE,EAC7BP,GAGAI,GACAI,MAAMC,OAAO,CAACL,IACdA,EAAehE,MAAM,EAAI,GACzBgE,CAAc,CAAC,EAAE,CAAC7E,QAAQ,EAC1B6E,CAAc,CAAC,EAAE,CAAC7E,QAAQ,CAACa,MAAM,EAAI,GACrC,CACAgE,EAAeM,OAAO,CAAC,IACrBC,EAAQpF,QAAQ,EACdoF,EAAQpF,QAAQ,CAACa,MAAM,CAAG,GAC1BuE,EAAQpF,QAAQ,CAAC0B,GAAG,CAAC,CAAC2D,EAAUzD,KAE9ByD,EAAIxC,WAAW,CADKuC,EACFvC,QADkB,CAACjB,EAAE,CAAC0D,MAAM,CAE9CV,EAAWW,IAAI,CAACF,EAClB,EACJ,GACAnB,EAAmBU,IAErBd,GAAW,EACb,EACA/D,CAAAA,EAAAA,EAAAA,SAAAA,CAASA,CAAC,KACR0F,GACF,EAAG,EAAE,EAEL,IAAMA,EAAkB,UACtB,IAAMC,EAAO,MAAMZ,EAAAA,CAAUA,CAACa,IAAI,CAAC,uBAAwB,CAAC,GACxDD,GAAQA,EAAKE,KAAK,EAAIF,EAAKE,KAAK,CAAC/E,MAAM,EAAE,EACjCmE,MAAM,EAAIxF,EAAMwF,MAAM,CAAC,EAAE,EAAE,CAenCa,CAdyB,MAAOC,IAC9BlC,GAAwB,GACxB,IAAMmC,EAAW,MAAMjB,EAAAA,CAAUA,CAACC,GAAG,CACnC,cAA8B,OAAhBvF,EAAMwF,MAAM,CAAC,EAAE,EAC7Bc,GAEEC,IACFpD,EAAiBoD,GAGjBC,CAJY,QAeUtD,CAAkB,CAAEuD,CAAc,EAChE7B,GAAc,GACV6B,GAAaA,EAAU,KAAQ,EAAT,CACpBA,EAAU,KAAQ,CAACC,CAAV,OAAkB,CAAC,gBAAgB,EAG3B,KAAQ,CAACC,CAAV,KAAgB,CAAC,GAAoB,2BAALC,GAAgCvF,MAAM,CAAG,GAAK6B,EAAc2D,IAAI,CAAC,GAAM,EAAIJ,EAAU,GAAM,EAAE,EAAT,KAE3G,CAACE,CAAV,KAAgB,CAAC,GAAoB,OAALC,GAAYvF,MAAM,CAAG,GAAK6B,EAAc2D,IAAI,CAAC,GAAM,EAAIJ,EAAU,GAAM,EAAE,EAExG,KAAQ,CAACE,CAAV,KAAgB,CAAC,GAAoB,oBAALC,GAAyBvF,MAAM,CAAG,GAAK6B,EAAc2D,IAAI,CAAC,GAAM,EAAIJ,EAAU,GAAM,EAAE,EAAT,KAEpG,CAACE,CAAV,KAAgB,CAAC,GAAeC,mBAAqBvF,MAAM,CAAG,GAAK6B,EAAc2D,IAAI,CAAC,GAAM,EAAIJ,EAAU,GAAM,CAClI7B,CADoI,EAAT,GAElH6B,EAAU,KAAQ,CAACE,CAAV,KAAgB,CAAEC,GAAmB,kBAALA,GAAuBvF,MAAM,CAAG,GAAK6B,EAAc2D,IAAI,CAAC,GAAM,EAAIJ,EAAU,GAAM,EAAE,EAAT,CAC/G,GAGpB,EA7BiCF,EAAUL,IAEnC9B,GAAwB,GAC1B,EACiB,CAAC,GAClBe,IACAa,IAGN,EA2BIc,EAAW,CACb5D,cAAgBA,EAChB6D,UAAY/G,EACZ2E,WAAYA,EACZqC,wBAA0B,CACxB3C,QAASA,EACT4C,UAhIc,CAgIHA,GA/HbpC,EAAoBC,IAAI,CAAG,CACzB,CAACoB,EAAKgB,cAAc,CAAC,CAAEhB,EAAKiB,aAAa,EAE3ChC,GACF,EA4HIZ,SAAUA,EACVE,eAAgBA,EAChB2C,gBA7HoB,CA6HFA,GA5HpBnC,EAAsBH,IAAI,CAAG,CAC3B,CAACoB,EAAKgB,cAAc,CAAC,CAAEhB,EAAKiB,aAAa,EAE3CnB,GACF,CAyHE,CACF,EAEA,MACE,+BACI9C,OAAAA,EAAAA,KAAAA,EAAAA,EAAeE,KAAAA,EACf,QADAF,CACA,EAACmE,EAAAA,CAASA,CAAAA,CAACvG,UAAU,kBAAkBwG,KAAK,cAC1C,UAACC,EAAAA,CAAWA,CAAAA,CAAC/B,OAAQxF,EAAMwF,MAAM,GACjC,UAACgC,EAAAA,OAAqBA,CAAAA,CAAG,GAAGV,CAAQ,GACpC,UAAC3G,EAAAA,OAAwBA,CAAAA,CAACgB,UAAa+B,IACvC,UAACuE,EAAAA,OAAyBA,CAAAA,CAAE,GAAGX,CAAQ,MAGzC,0BAIR,mBC3MA,4CACA,yBACA,WACA,OAAe,EAAQ,KAA8C,CACrE,EACA,SAFsB,kICkCtB,MA7Be,KAEb,IAAMtB,EADSkC,CAAAA,EAAAA,EAAAA,SAAAA,CAASA,EA4BXC,CA3BaC,KAAK,CAACpC,CA2Bb,KA3BmB,EAAI,EAAE,CAEtCqC,EAAsBC,CAAAA,EAAAA,EAAAA,mBAAAA,CAAmBA,CAAC,IAAM,UAACC,EAAAA,OAAaA,CAAAA,CAACvC,OAAQA,KAE7E,OAAQA,CAAM,CAAC,EAAE,EACf,IAAK,SACH,MAAO,UAACqC,EAAAA,CAAAA,EAEV,KAAK,OACH,MAAO,UAACE,EAAAA,OAAaA,CAAAA,CAACvC,OAAQA,GAEhC,KAAK,OACH,MAAO,UAACvC,EAAAA,OAAaA,CAAAA,CAACuC,OAAQA,GAEhC,SACE,OAAO,IACX,CACF", "sources": ["webpack://_N_E/./pages/operation/components/OperationTimelineSection.tsx", "webpack://_N_E/./pages/operation/OperationShow.tsx", "webpack://_N_E/?9507", "webpack://_N_E/./pages/operation/[...routes].tsx"], "sourcesContent": ["//Import Library\r\nimport { faAngleLeft, faAngleRight } from \"@fortawesome/free-solid-svg-icons\";\r\nimport { FontAwesomeIcon } from \"@fortawesome/react-fontawesome\";\r\nimport moment from \"moment\";\r\nimport React, { useState, useEffect } from \"react\";\r\nimport { Col, Row } from \"react-bootstrap\";\r\n\r\n//Import services/components\r\nimport { useTranslation } from 'next-i18next';\r\n\r\n\r\nconst OperationTimelineSection = (props: any) => {\r\n    const { t } = useTranslation('common');\r\n    const formatDateWithoutTime = \"MM-D-YYYY\";\r\n    const [scrolled, setScrolled] = useState(40);\r\n\r\n    useEffect(() => {\r\n        document.getElementById(\"timeline-container\")?.scroll(scrolled, 5000);\r\n    }, [scrolled]);\r\n\r\n    const onLeftArrow = () => {\r\n        const temp = scrolled - 50;\r\n        setScrolled(temp < 0 ? 0 : temp);\r\n    };\r\n\r\n    const onRigthArrow = () => {\r\n        setScrolled(scrolled + 50);\r\n    };\r\n\r\n    const timelineIcon: any = {\r\n        1: \"/images/home/<USER>\",\r\n        2: \"/images/home/<USER>\",\r\n        3: \"/images/home/<USER>\",\r\n        4: \"/images/home/<USER>\",\r\n        5: \"/images/home/<USER>\",\r\n        6: \"/images/home/<USER>\",\r\n        7: \"/images/home/<USER>\",\r\n    };\r\n\r\n    return (\r\n        <>\r\n            <Row>\r\n                <Col className=\"operatinTimeline\" xs={12}>\r\n                    <div className=\"progress_main_sec\" style={{ marginTop: \"90px\" }}>\r\n                        {props.operation && props.operation.timeline.length > 2 && (\r\n                            <div>\r\n                                <div\r\n                                    className=\"prev\"\r\n                                    onClick={onLeftArrow}\r\n                                    style={{ cursor: \"pointer\" }}\r\n                                >\r\n                                    <span>\r\n                                        <FontAwesomeIcon icon={faAngleLeft} />\r\n                                    </span>\r\n                                </div>\r\n                                <div\r\n                                    className=\"next\"\r\n                                    onClick={onRigthArrow}\r\n                                    style={{ cursor: \"pointer\" }}\r\n                                >\r\n                                    <span>\r\n                                        <FontAwesomeIcon icon={faAngleRight} />\r\n                                    </span>\r\n                                </div>\r\n                            </div>\r\n                        )}\r\n\r\n                        <div className=\"progressbar-container\" id=\"timeline-container\">\r\n                            <ul className=\"progressbar\">\r\n                                {props.operation &&\r\n                                    props.operation.timeline &&\r\n                                    props.operation.timeline.map((item: any, i: any) => {\r\n                                        return (\r\n                                            <li\r\n                                                style={{\r\n                                                    zIndex: props.operation.timeline.length - i,\r\n                                                }}\r\n                                                key={i}\r\n                                            >\r\n                                                <div className=\"timelineIcon\">\r\n                                                    <img\r\n                                                        src={timelineIcon[item.iconclass]}\r\n                                                        width=\"80px\"\r\n                                                        height=\"80px\"\r\n                                                    />\r\n                                                </div>\r\n                                                {item.timetitle ? (\r\n                                                    <p className=\"step-label\">{item.timetitle}</p>\r\n                                                ) : (\r\n                                                    <p className=\"step-label\">{t(\"NoTitle\")}</p>\r\n                                                )}\r\n                                                {item.date ? (\r\n                                                    <p className=\"step-text\">\r\n                                                        {moment(item.date).format(\r\n                                                            formatDateWithoutTime\r\n                                                        )}\r\n                                                    </p>\r\n                                                ) : (\r\n                                                    <p className=\"step-text\">{t(\"NoDate\")}</p>\r\n                                                )}\r\n                                            </li>\r\n                                        );\r\n                                    })}\r\n                            </ul>\r\n                        </div>\r\n                    </div>\r\n                </Col>\r\n            </Row>\r\n        </>\r\n    )\r\n}\r\n\r\nexport default OperationTimelineSection;", "//Import Library\r\nimport React, { useState, useEffect } from \"react\";\r\nimport { Container } from \"react-bootstrap\";\r\n\r\n//Import services/components\r\nimport apiService from \"../../services/apiService\";\r\nimport UpdatePopup from \"../../components/updates/UpdatePopup\";\r\nimport { useTranslation } from 'next-i18next';\r\nimport OperationCoverSection from \"./components/OperationCoverSection\";\r\nimport OperationAccordianSection from \"./components/OperationAccordianSection\";\r\nimport OperationTimelineSection from \"./components/OperationTimelineSection\";\r\n\r\ninterface OperationShowProps {\r\n  routes: string[];\r\n}\r\n\r\nconst OperationShow = (props: OperationShowProps) => {\r\n  const { t } = useTranslation('common');\r\n\r\n  const [operationData, setOperationData] = useState<any>({\r\n    title: \"\",\r\n    timeline: [],\r\n    description: \"\",\r\n    hazard_type: { title: \"\" },\r\n    hazard: [],\r\n    syndrome: { title: \"\" },\r\n    created_at: \"\",\r\n    updated_at: \"\",\r\n    country: { title: \"\" },\r\n    status: { title: \"\" },\r\n    start_date: \"\",\r\n    end_date: \"\",\r\n    partners: [],\r\n    images: [],\r\n    images_src: [],\r\n    document: [],\r\n    doc_src: [],\r\n  });\r\n\r\n  const [operationDataLoading, setOperationDataLoading] = useState(false);\r\n\r\n  const [loading, setLoading] = useState(false);\r\n  const [Document, setDocument] = useState([]);\r\n  const [updateDocument, setUpdateDocuments] = useState([]);\r\n  const [editAccess, setEditAccess] = useState(false);\r\n\r\n  const operationFileParams: any = {\r\n    sort: { doc_created_at: \"asc\" },\r\n    Doctable: true,\r\n  };\r\n\r\n  const operationUpdateParams: any = {\r\n    sort: { doc_created_at: \"asc\" },\r\n    DocUpdatetable: true,\r\n  };\r\n  const sortProps = (data: any) => {\r\n    operationFileParams.sort = {\r\n      [data.columnSelector]: data.sortDirection,\r\n    };\r\n    fetchOperationFiles();\r\n  };\r\n  const sortUpdateProps = (data: any) => {\r\n    operationUpdateParams.sort = {\r\n      [data.columnSelector]: data.sortDirection,\r\n    };\r\n    fetchUpdateFiles();\r\n  };\r\n\r\n  const fetchOperationFiles = async () => {\r\n    const _documents: any = [];\r\n    setLoading(true);\r\n    const operationFiles = await apiService.get(\r\n      `/operation/${props.routes[1]}`,\r\n      operationFileParams\r\n    );\r\n    if (\r\n      operationFiles &&\r\n      Array.isArray(operationFiles) &&\r\n      operationFiles.length >= 1 &&\r\n      operationFiles[0].document &&\r\n      operationFiles[0].document.length >= 1\r\n    ) {\r\n      operationFiles.forEach((element) => {\r\n        element.document &&\r\n          element.document.length > 0 &&\r\n          element.document.map((ele: any, i: number) => {\r\n            const description = element.document[i].docsrc;\r\n            ele.description = description;\r\n            _documents.push(ele);\r\n          });\r\n      });\r\n      setDocument(_documents);\r\n    }\r\n    setLoading(false);\r\n  };\r\n\r\n  const fetchUpdateFiles = async () => {\r\n    const _documents: any = [];\r\n    setLoading(true);\r\n    const operationFiles = await apiService.get(\r\n      `/operation/${props.routes[1]}`,\r\n      operationUpdateParams\r\n    );\r\n    if (\r\n      operationFiles &&\r\n      Array.isArray(operationFiles) &&\r\n      operationFiles.length >= 1 &&\r\n      operationFiles[0].document &&\r\n      operationFiles[0].document.length >= 1\r\n    ) {\r\n      operationFiles.forEach((element) => {\r\n        element.document &&\r\n          element.document.length > 0 &&\r\n          element.document.map((ele: any, i: number) => {\r\n            const description = element.document[i].docsrc;\r\n            ele.description = description;\r\n            _documents.push(ele);\r\n          });\r\n      });\r\n      setUpdateDocuments(_documents);\r\n    }\r\n    setLoading(false);\r\n  };\r\n  useEffect(() => {\r\n    getLoggedInUser();\r\n  }, []);\r\n\r\n  const getLoggedInUser = async () => {\r\n    const data = await apiService.post(\"/users/getLoggedUser\", {});\r\n    if (data && data.roles && data.roles.length) {\r\n      if (props.routes && props.routes[1]) {\r\n        const getOperationData = async (operationParams: any) => {\r\n          setOperationDataLoading(true);\r\n          const response = await apiService.get(\r\n            `/operation/${props.routes[1]}`,\r\n            operationParams\r\n          );\r\n          if (response) {\r\n            setOperationData(response);\r\n\r\n            //Checking for Edit access\r\n            getOperationEditAccess(response, data);\r\n          }\r\n          setOperationDataLoading(false);\r\n        };\r\n        getOperationData({});\r\n        fetchOperationFiles();\r\n        fetchUpdateFiles();\r\n      }\r\n    }\r\n  };\r\n\r\n  function getOperationEditAccess(operationData: any, loginUser: any) {\r\n    setEditAccess(false);\r\n    if (loginUser && loginUser['roles']) {\r\n      if (loginUser['roles'].includes(\"SUPER_ADMIN\")) {\r\n        //SUPER_ADMIN can Edit all organisations\r\n        setEditAccess(true);\r\n      } else if (loginUser['roles'].filter((x: string) => x == \"EMT_NATIONAL_FOCALPOINT\").length > 0 && operationData.user['_id'] == loginUser['_id']) {\r\n          setEditAccess(true);\r\n      } else if (loginUser['roles'].filter((x: string) => x == \"EMT\").length > 0 && operationData.user['_id'] == loginUser['_id']) {\r\n        setEditAccess(true);\r\n      } else if (loginUser['roles'].filter((x: string) => x == \"INIG_STAKEHOLDER\").length > 0 && operationData.user['_id'] == loginUser['_id']) {\r\n        setEditAccess(true);\r\n      } else if (loginUser['roles'].filter((x: string) => x == \"GENERAL_USER\").length > 0 && operationData.user['_id'] == loginUser['_id']) {\r\n        setEditAccess(true);\r\n      } else if (loginUser['roles'].filter((x: string) => x == \"PLATFORM_ADMIN\").length > 0 && operationData.user['_id'] == loginUser['_id']) {\r\n        setEditAccess(true);\r\n      }\r\n    }\r\n  }\r\n\r\n  const getNoDataDiv = () => {\r\n    if (operationData && !operationData.title)\r\n      return <div className=\"nodataFound\">{t(\"vspace.Nodataavailable\")}</div>;\r\n  };\r\n\r\n  let propData = {\r\n    operationData : operationData,\r\n    routeData : props,\r\n    editAccess: editAccess,\r\n    documentAccoirdianProps : {\r\n      loading: loading,\r\n      sortProps: sortProps,\r\n      Document: Document,\r\n      updateDocument: updateDocument,\r\n      sortUpdateProps : sortUpdateProps\r\n    }\r\n  }\r\n\r\n  return (\r\n    <>\r\n      { operationData?.title ? (\r\n        <Container className=\"operationDetail\" fluid>\r\n          <UpdatePopup routes={props.routes} />\r\n          <OperationCoverSection  {...propData} />\r\n          <OperationTimelineSection operation = {operationData} />\r\n          <OperationAccordianSection {...propData} />\r\n        </Container>\r\n      ) : (\r\n        <></>\r\n      )}\r\n    </>\r\n  );\r\n};\r\n\r\nexport default OperationShow;\r\n", "\n    (window.__NEXT_P = window.__NEXT_P || []).push([\n      \"/operation/[...routes]\",\n      function () {\n        return require(\"private-next-pages/operation/[...routes].tsx\");\n      }\n    ]);\n    if(module.hot) {\n      module.hot.dispose(function () {\n        window.__NEXT_P.push([\"/operation/[...routes]\"])\n      });\n    }\n  ", "//Import Library\r\nimport { useRouter } from 'next/router';\r\n\r\n//Import services/components\r\nimport OperationForm from './Form';\r\nimport OperationShow from './OperationShow';\r\nimport { canAddOperationForm } from \"./permission\";\r\nimport { serverSideTranslations } from 'next-i18next/serverSideTranslations';\r\n\r\nconst Router = () => {\r\n  const router = useRouter()\r\n  const routes:any = router.query.routes || []\r\n\r\n  const CanAccessCreateForm = canAddOperationForm(() => <OperationForm routes={routes} />);\r\n\r\n  switch (routes[0]) {\r\n    case 'create':\r\n      return <CanAccessCreateForm />\r\n\r\n    case 'edit':\r\n      return <OperationForm routes={routes} />\r\n\r\n    case 'show':\r\n      return <OperationShow routes={routes}  />\r\n\r\n    default:\r\n      return null;\r\n  }\r\n}\r\n\r\nexport async function getServerSideProps({ locale }: { locale: string }) {\r\n  return {\r\n    props: {\r\n      ...(await serverSideTranslations(locale, ['common'])),\r\n    },\r\n  }\r\n}\r\n\r\nexport default Router\r\n"], "names": ["props", "t", "useTranslation", "OperationTimelineSection", "scrolled", "setScrolled", "useState", "useEffect", "document", "getElementById", "scroll", "timelineIcon", "Row", "Col", "className", "xs", "div", "style", "marginTop", "operation", "timeline", "length", "onClick", "onLeftArrow", "temp", "cursor", "span", "FontAwesomeIcon", "icon", "faAngleLeft", "onRigthArrow", "faAngleRight", "id", "ul", "map", "item", "i", "li", "zIndex", "img", "src", "iconclass", "width", "height", "timetitle", "p", "date", "moment", "format", "OperationShow", "operationData", "setOperationData", "title", "description", "hazard_type", "hazard", "syndrome", "created_at", "updated_at", "country", "status", "start_date", "end_date", "partners", "images", "images_src", "doc_src", "operationDataLoading", "setOperationDataLoading", "loading", "setLoading", "Document", "setDocument", "updateDocument", "setUpdateDocuments", "editAccess", "setEditAccess", "operationFileParams", "sort", "doc_created_at", "Doctable", "operationUpdateParams", "DocUpdatetable", "fetchOperationFiles", "_documents", "operationFiles", "apiService", "get", "routes", "Array", "isArray", "for<PERSON>ach", "element", "ele", "docsrc", "push", "fetchUpdateFiles", "getLoggedInUser", "data", "post", "roles", "getOperationData", "operationParams", "response", "getOperationEditAccess", "loginUser", "includes", "filter", "x", "user", "propData", "routeData", "documentAccoirdianProps", "sortProps", "columnSelector", "sortDirection", "sortUpdateProps", "Container", "fluid", "UpdatePopup", "OperationCoverSection", "OperationAccordianSection", "useRouter", "Router", "query", "CanAccessCreateForm", "canAddOperationForm", "OperationForm"], "sourceRoot": "", "ignoreList": []}