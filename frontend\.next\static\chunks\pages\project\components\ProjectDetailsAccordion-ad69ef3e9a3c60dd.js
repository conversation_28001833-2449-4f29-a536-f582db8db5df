(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[2808],{29335:(e,r,a)=>{"use strict";a.d(r,{A:()=>b});var s=a(15039),t=a.n(s),d=a(14232),l=a(77346),c=a(37876);let i=d.forwardRef((e,r)=>{let{className:a,bsPrefix:s,as:d="div",...i}=e;return s=(0,l.oU)(s,"card-body"),(0,c.jsx)(d,{ref:r,className:t()(a,s),...i})});i.displayName="CardBody";let n=d.forwardRef((e,r)=>{let{className:a,bsPrefix:s,as:d="div",...i}=e;return s=(0,l.oU)(s,"card-footer"),(0,c.jsx)(d,{ref:r,className:t()(a,s),...i})});n.displayName="CardFooter";var o=a(81764);let j=d.forwardRef((e,r)=>{let{bsPrefix:a,className:s,as:i="div",...n}=e,j=(0,l.oU)(a,"card-header"),f=(0,d.useMemo)(()=>({cardHeaderBsPrefix:j}),[j]);return(0,c.jsx)(o.A.Provider,{value:f,children:(0,c.jsx)(i,{ref:r,...n,className:t()(s,j)})})});j.displayName="CardHeader";let f=d.forwardRef((e,r)=>{let{bsPrefix:a,className:s,variant:d,as:i="img",...n}=e,o=(0,l.oU)(a,"card-img");return(0,c.jsx)(i,{ref:r,className:t()(d?"".concat(o,"-").concat(d):o,s),...n})});f.displayName="CardImg";let x=d.forwardRef((e,r)=>{let{className:a,bsPrefix:s,as:d="div",...i}=e;return s=(0,l.oU)(s,"card-img-overlay"),(0,c.jsx)(d,{ref:r,className:t()(a,s),...i})});x.displayName="CardImgOverlay";let p=d.forwardRef((e,r)=>{let{className:a,bsPrefix:s,as:d="a",...i}=e;return s=(0,l.oU)(s,"card-link"),(0,c.jsx)(d,{ref:r,className:t()(a,s),...i})});p.displayName="CardLink";var m=a(46052);let u=(0,m.A)("h6"),h=d.forwardRef((e,r)=>{let{className:a,bsPrefix:s,as:d=u,...i}=e;return s=(0,l.oU)(s,"card-subtitle"),(0,c.jsx)(d,{ref:r,className:t()(a,s),...i})});h.displayName="CardSubtitle";let N=d.forwardRef((e,r)=>{let{className:a,bsPrefix:s,as:d="p",...i}=e;return s=(0,l.oU)(s,"card-text"),(0,c.jsx)(d,{ref:r,className:t()(a,s),...i})});N.displayName="CardText";let v=(0,m.A)("h5"),w=d.forwardRef((e,r)=>{let{className:a,bsPrefix:s,as:d=v,...i}=e;return s=(0,l.oU)(s,"card-title"),(0,c.jsx)(d,{ref:r,className:t()(a,s),...i})});w.displayName="CardTitle";let y=d.forwardRef((e,r)=>{let{bsPrefix:a,className:s,bg:d,text:n,border:o,body:j=!1,children:f,as:x="div",...p}=e,m=(0,l.oU)(a,"card");return(0,c.jsx)(x,{ref:r,...p,className:t()(s,m,d&&"bg-".concat(d),n&&"text-".concat(n),o&&"border-".concat(o)),children:j?(0,c.jsx)(i,{children:f}):f})});y.displayName="Card";let b=Object.assign(y,{Img:f,Title:w,Subtitle:h,Body:i,Link:p,Text:N,Header:j,Footer:n,ImgOverlay:x})},29719:(e,r,a)=>{"use strict";a.r(r),a.d(r,{default:()=>f});var s=a(37876),t=a(14232),d=a(11041),l=a(21772),c=a(10841),i=a.n(c),n=a(32890),o=a(29335),j=a(31753);let f=e=>{var r;let a="DD-MM-YYYY HH:mm:ss",{t:c}=(0,j.Bd)("common"),[f,x]=(0,t.useState)(!0);return(0,s.jsx)(s.Fragment,{children:(0,s.jsxs)(n.A.Item,{eventKey:"0",children:[(0,s.jsxs)(n.A.Header,{onClick:()=>x(!f),children:[(0,s.jsx)("div",{className:"cardTitle",children:c("ProjectDetails")}),(0,s.jsx)("div",{className:"cardArrow",children:f?(0,s.jsx)(l.g,{icon:d.QLR,color:"#fff"}):(0,s.jsx)(l.g,{icon:d.EZy,color:"#fff"})})]}),(0,s.jsx)(n.A.Body,{children:(0,s.jsxs)(o.A.Text,{className:"projectDetails ps-0",children:[(0,s.jsxs)("p",{children:[(0,s.jsx)("b",{children:c("Status")}),":",(0,s.jsxs)("span",{children:[" ",e.project.status?e.project.status.title:""]})]}),(0,s.jsxs)("p",{children:[(0,s.jsx)("b",{children:c("Created")}),":",(0,s.jsxs)("span",{children:[" ",e.project.created_at?i()(e.project.created_at).format(a):null," "]})]}),(0,s.jsxs)("p",{children:[(0,s.jsx)("b",{children:c("EndDate")}),":",(0,s.jsxs)("span",{children:[" ",e.project.end_date?i()(e.project.end_date).format("DD-MM-YYYY"):null]})]}),(0,s.jsxs)("p",{children:[(0,s.jsx)("b",{children:c("LastModified")}),":",(0,s.jsxs)("span",{children:[" ",e.project.updated_at?i()(e.project.updated_at).format(a):null," "]})]}),(0,s.jsxs)("p",{children:[(0,s.jsx)("b",{children:c("WeblinktoProject")}),":",(0,s.jsx)("span",{children:(null==(r=e.project)?void 0:r.website)&&(0,s.jsx)("a",{href:e.project.website,target:"_blank",children:e.project.website})})]})]})})]})})}},32212:(e,r,a)=>{(window.__NEXT_P=window.__NEXT_P||[]).push(["/project/components/ProjectDetailsAccordion",function(){return a(29719)}])},81764:(e,r,a)=>{"use strict";a.d(r,{A:()=>t});let s=a(14232).createContext(null);s.displayName="CardHeaderContext";let t=s}},e=>{var r=r=>e(e.s=r);e.O(0,[7725,1772,636,6593,8792],()=>r(32212)),_N_E=e.O()}]);
//# sourceMappingURL=ProjectDetailsAccordion-ad69ef3e9a3c60dd.js.map