{"version": 3, "file": "static/chunks/pages/institution/components/InstitutionAccordionSection-22d6a3b2cfa5703e.js", "mappings": "wLA0BA,MAlB6BA,IACzB,GAAM,GAAEC,CAAC,CAAE,CAAGC,CAAAA,EAAAA,EAAAA,EAAAA,CAAcA,CAAC,MAiBlBC,IAhBX,MACI,SAe0BA,CAfzBC,CAe0B,CAf1BA,CAASA,CAAAA,CAACC,iBAAiB,aACxB,WAACD,EAAAA,CAASA,CAACE,IAAI,EAACC,SAAS,cACrB,UAACH,EAAAA,CAASA,CAACI,MAAM,WACb,UAACC,MAAAA,CAAIC,UAAU,qBAAaT,EAAE,mBAElC,UAACG,EAAAA,CAASA,CAACO,IAAI,WACX,UAACC,EAAAA,CAAUA,CAAAA,CACPC,KAAK,SACLC,GAAId,GAASA,EAAMe,MAAM,CAAGf,EAAMe,MAAM,CAAC,EAAE,CAAG,aAMtE,2IC8IA,MAnIoB,IAClB,GAAM,GAAEd,CAAC,CAAE,CAAGC,CAAAA,EAAAA,EAAAA,EAAAA,CAAcA,CAAC,MAkIhBc,IAjIP,CAACC,EAAQC,EAAU,CAAGC,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAc,EAAE,EAG9CC,EAAoB,IACxB,IAAMC,EAAc,8EAA8EC,IAAI,CAACC,EAAKC,WAAW,EAEvH,MACE,WAACf,MAAAA,CAAIC,UAAU,4BACb,WAACe,IAAAA,CAAEf,UAAU,iBACX,UAACgB,IAAAA,UAAGzB,EAAE,cAAgB,IAAEsB,EAAKI,YAAY,EAAI,mBAE9CJ,EAAKC,WAAW,EACf,WAACf,MAAAA,CAAIC,UAAU,wBACb,UAACe,IAAAA,UAAE,UAACC,IAAAA,UAAGzB,EAAE,cACRoB,EACC,WAACZ,MAAAA,WACC,UAACmB,EAAAA,CAAeA,CAAAA,CAACC,KAAMC,EAAAA,GAAMA,CAAEC,KAAK,KAAKC,MAAM,OAAOtB,UAAU,SAChE,UAACuB,IAAAA,CAAEvB,UAAU,cAAcwB,KAAMX,EAAKC,WAAW,CAAEW,OAAO,SAASC,IAAI,+BACpEb,EAAKC,WAAW,MAIrB,UAACf,MAAAA,UACC,UAACgB,IAAAA,CAAEf,UAAU,YAAY2B,MAAO,CAAEC,UAAW,WAAY,WACtDf,EAAKC,WAAW,QAM1BD,EAAKgB,YAAY,EAChB,WAACC,EAAAA,CAAMA,CAAAA,CAAC9B,UAAU,qCAAqCwB,KAAMX,EAAKgB,YAAY,WAC3EtC,EAAE,YACH,UAAC2B,EAAAA,CAAeA,CAAAA,CAACC,KAAMY,EAAAA,GAAUA,CAAEV,KAAK,KAAKrB,UAAU,cAKjE,EA6CA,MA3CAgC,CAAAA,EAAAA,EAAAA,SAAAA,CAASA,CAAC,KACR,IAAMC,EAA8B,EAAE,CACtC3C,GAASA,EAAM4C,OAAO,EAAIC,MAAMC,OAAO,CAAC9C,EAAM4C,OAAO,GAAK5C,EAAM4C,OAAO,CAACG,GAAG,CAAC,CAACxB,EAAMyB,KACjF,IACIC,EADEC,EAAW3B,GAAQA,EAAK4B,IAAI,CAACC,KAAK,CAAC,KAAKC,GAAG,GAGjD,OAAQH,GACN,IAAK,MACL,IAAK,MACL,IAAK,OACL,IAAK,MACHD,EAAS,GAAwC1B,MAAAA,CAArC+B,8BAAsB,CAAC,gBAAuB,OAAT/B,EAAKgC,GAAG,EACzD,KACF,KAAK,MACHN,EAAS,gCACT,KACF,KAAK,OACHA,EAAS,iCACT,KACF,KAAK,MACL,IAAK,OACHA,EAAS,gCACT,KACF,SACEA,EAAS,iCACb,CAEA,IAAMO,EAAQ,CAAc,SAAbN,GAAoC,QAAbA,GAAmC,QAAbA,GAAsBA,UAAa,CAAK,EAC/F,GAA4C3B,MAAAA,CAAzC+B,8BAAsB,CAAC,oBAA2B,OAAT/B,EAAKgC,GAAG,EACnDE,EAAQ,GAAqE,OAAlElC,GAAQA,EAAKmC,aAAa,CAAGnC,EAAKmC,aAAa,CAAG,iBAC7DC,EAAe3D,EAAM4D,WAAW,EAAIf,MAAMC,OAAO,CAAC9C,EAAM4D,WAAW,GACpE5D,EAAM4D,WAAW,CAACC,MAAM,CAAG,EAAI7D,EAAM4D,WAAW,CAACZ,EAAE,CAAG,GAE3DL,EAAemB,IAAI,CAAC,CAClBC,IAAKd,EACLzB,YAAamC,EACbhC,aAAc8B,EACdlB,aAAciB,CAChB,EACF,GACAtC,EAAUyB,EACZ,EAAG,CAAC3C,EAAM,EAGR,UAACS,MAAAA,UACEQ,GAA4B,IAAlBA,EAAO4C,MAAM,CACtB,UAACpD,MAAAA,CAAIC,UAAU,wCACb,UAACe,IAAAA,CAAEf,UAAU,wDAAgDT,EAAE,qBAGjE,UAAC+D,EAAAA,EAAQA,CAAAA,CACPC,YAAY,EACZC,YAAY,EACZC,gBAAgB,EAChBC,cAAc,EACdC,mBAAmB,EACnBC,SAAU,GACVC,aAAa,EACbC,WAAW,EACXC,eAAe,EACfC,cAAc,EACdC,aAAc,IACZ1D,EAAO8B,GAAG,CAAC,CAACxB,EAAMqD,IAChB,UAACC,MAAAA,CAECd,IAAKxC,EAAKwC,GAAG,CACbe,IAAK,aAAuB,OAAVF,EAAQ,GAC1BvC,MAAO,CAAE0C,MAAO,OAAQC,OAAQ,OAAQC,UAAW,OAAQ,GAHtDL,aAQV3D,EAAO8B,GAAG,CAAC,CAACxB,EAAMqD,IACjB,WAACnE,MAAAA,WACC,UAACoE,MAAAA,CACCd,IAAKxC,EAAKwC,GAAG,CACbe,IAAKvD,EAAKI,YAAY,EAAI,gBAC1BU,MAAO,CAAE6C,UAAW,QAASD,UAAW,SAAU,IAEnD7D,EAAkBG,KANXqD,OActB,kRC9JO,IAAMO,EAAoBC,CAAAA,EAAAA,EAAAA,CAAAA,CAAoBA,CAAC,CACpDC,sBAAuB,KACjBC,EAAMC,WAAW,IAAID,EAAMC,WAAW,CAACC,WAAW,IAAIF,EAAMC,WAAW,CAACC,WAAW,CAAC,aAAa,CAKvGC,CALyG,kBAKrF,mBACtB,GAAG,EAEkCL,CAAAA,EAAAA,EAAAA,CAAAA,CAAoBA,CAAC,CACxDC,sBAAwBC,KAClBA,EAAMC,WAAW,IAAID,EAAMC,WAAW,CAACC,WAAW,IAAIF,EAAMC,WAAW,CAACC,WAAW,CAAC,aAAa,CAKvGC,CALyG,kBAKrF,wBACpBC,iBAAkB,IAAM,UAACC,EAAAA,OAAIA,CAAAA,CAAAA,EAC/B,GAAG,EAE+BP,CAAAA,EAAAA,EAAAA,CAAAA,CAAoBA,CAAC,CACrDC,sBAAuB,CAACC,EAAOtF,KAC7B,GAAIsF,EAAMC,WAAW,EAAID,EAAMC,WAAW,CAACC,WAAW,EAAE,GAClDF,EAAMC,WAAW,CAACC,WAAW,CAAC,aAAa,CAC7C,CAD+C,KACxC,QAEP,GAAIF,EAAMC,WAAW,CAACC,WAAW,CAAC,aAAa,EAAE,EACrCA,WAAW,EAAIxF,EAAMwF,WAAW,CAACI,IAAI,EAAI5F,EAAMwF,WAAW,CAACI,IAAI,GAAKN,EAAMM,IAAI,CAACrC,GAAG,CAC1F,CAD4F,MACrF,CAGb,CAEF,OAAO,CACT,EACAkC,mBAAoB,oBACtB,GAAG,EAEmCL,CAAAA,EAAAA,EAAAA,CAAAA,CAAoBA,CAAC,CACzDC,sBAAuB,CAACC,EAAOtF,KAC7B,GAAIsF,EAAMC,WAAW,EAAID,EAAMC,WAAW,CAACC,WAAW,EAAE,GAClDF,EAAMC,WAAW,CAACC,WAAW,CAAC,aAAa,CAC7C,CAD+C,MACxC,OAEP,GAAIF,EAAMC,WAAW,CAACC,WAAW,CAAC,aAAa,EACzCxF,EAAMwF,WAAW,EAAIxF,EAAMwF,WAAW,CAACI,IAAI,EAAI5F,EAAMwF,WAAW,CAACI,IAAI,GAAKN,EAAMM,IAAI,CAACrC,GAAG,CAC1F,CAD4F,MACrF,CAGb,CAEF,OAAO,CACT,EACAkC,mBAAoB,yBACpBC,iBAAkB,IAAM,UAACC,EAAAA,OAAIA,CAAAA,CAAAA,EAC/B,GAAG,EAEoCP,CAAAA,EAAAA,EAAAA,CAAAA,CAAoBA,CAAC,CAC1DC,sBAAwBC,KAClBA,EAAMC,WAAW,IAAID,EAAMC,WAAW,CAACM,MAAM,IAAIP,EAAMC,WAAW,CAACM,MAAM,CAAC,WAAW,CAK3FJ,CAL6F,kBAKzE,yBACtB,GAAG,EAEiCL,CAAAA,EAAAA,EAAAA,CAAAA,CAAoBA,CAAC,CACvDC,sBAAuB,CAACC,EAAOtF,KAC7B,GAAIsF,EAAMC,WAAW,EAAID,EAAMC,WAAW,CAACO,uBAAuB,EAAE,GAC9DR,EAAMC,WAAW,CAACO,uBAAuB,CAAC,aAAa,CACzD,CAD2D,MACpD,OAEP,GAAIR,EAAMC,WAAW,CAACO,uBAAuB,CAAC,aAAa,EAAE,EAEnDN,WAAW,EACjBxF,EAAMwF,WAAW,CAACI,IAAI,EACtB5F,EAAMwF,WAAW,CAACI,IAAI,GAAKN,EAAMM,IAAI,CAACrC,GAAG,CAEzC,CADA,MACO,CAGb,CAEF,OAAO,CACT,EACAkC,mBAAoB,sBACtB,GAAG,EAEYN,iBAAiBA,EAAC,wGCtEjC,MAnB8B,IAC1B,GAAM,GAAElF,CAAC,CAAE,CAAGC,CAAAA,EAAAA,EAAAA,EAAAA,CAAcA,CAAC,MAkBlB6F,IAjBX,MACI,UAAC3F,CAgB2B2F,CAhB3B3F,CAASA,CAAAA,CAACC,iBAAiB,aACxB,WAACD,EAAAA,CAASA,CAACE,IAAI,EAACC,SAAS,cACrB,UAACH,EAAAA,CAASA,CAACI,MAAM,WACb,UAACC,MAAAA,CAAIC,UAAU,qBAAaT,EAAE,oBAElC,UAACG,EAAAA,CAASA,CAACO,IAAI,WACX,UAACK,EAAAA,CAAWA,CAAAA,CACR4B,QAAS5C,EAAMiB,MAAM,CACrB2C,YAAa5D,EAAMgG,UAAU,SAMrD,oICsHA,MA1I0B,IACtB,GAAM,GAAE/F,CAAC,CAAE,CAAGC,CAAAA,EAAAA,EAAAA,EAAAA,CAAcA,CAAC,MAyIlB+F,IAxIL,aAwIsBA,EAAC,EAxIrBC,CAAe,kBAAEC,CAAgB,gBAAEC,CAAc,CAAE,CAAGpG,EAoH9D,MACI,UAACI,EAAAA,CAASA,CAAAA,CAACC,iBAAiB,aACxB,WAACD,EAAAA,CAASA,CAACE,IAAI,EAACC,SAAS,cACrB,UAACH,EAAAA,CAASA,CAACI,MAAM,WACb,UAACC,MAAAA,CAAIC,UAAU,qBAAaT,EAAE,gBAElC,WAACG,EAAAA,CAASA,CAACO,IAAI,EAACD,UAAU,oCAtHlC,WAACe,IAAAA,WACG,UAACC,IAAAA,UAAGzB,EAAE,sBAAwB,IAC9B,WAACoG,OAAAA,WACI,IACAH,EAAgBrF,IAAI,CAAGqF,EAAgBrF,IAAI,CAACyF,KAAK,CAAG,SAO7D,WAAC7E,IAAAA,WACG,UAACC,IAAAA,UAAGzB,EAAE,aAAe,IACrB,UAACoG,OAAAA,UACIH,EAAgBK,QAAQ,CACnBL,EAAgBK,QAAQ,CAACxD,GAAG,CAAC,CAACxB,EAAWqD,IACvC,UAACyB,OAAAA,UACG,UAACG,KAAAA,UAAIjF,EAAK+E,KAAK,IADR1B,IAIb,QAOd,WAACnD,IAAAA,WACG,UAACC,IAAAA,UAAGzB,EAAE,qBAAuB,IAC7B,UAACoG,OAAAA,UACIF,GAAoBA,EAAiBtC,MAAM,CAAG,EAC3CsC,EAAiBpD,GAAG,CAAC,CAACxB,EAAWyB,IAC7B,UAACwD,KAAAA,UACG,UAACC,IAAIA,CACDvE,KAAM,yBACNwE,GAAI,WAFHD,QAE+B,OAATlF,EAAKgC,GAAG,WAE9BhC,EAAK+E,KAAK,IALVtD,IAUb,UAACwD,KAAAA,UAAIvG,EAAE,kCAQnB,WAACwB,IAAAA,WACG,UAACC,IAAAA,UAAGzB,EAAE,eAAiB,IACvB,WAACoG,OAAAA,WACI,IACAH,EAAgBS,SAAS,CACpBT,EAAgBS,SAAS,CAAC5D,GAAG,CAAC,CAACxB,EAAWqD,IACxC,WAAC4B,KAAAA,WACIjF,EAAK+E,KAAK,CAAC,IAAC,UAACM,KAAAA,CAAAA,KADThC,IAIX,SAOd,WAACnD,IAAAA,WACG,UAACC,IAAAA,UAAGzB,EAAE,mBAAqB,IAC3B,UAACoG,OAAAA,UACID,GAAkBA,EAAevC,MAAM,CAAG,EACvCuC,EAAerD,GAAG,CAAC,CAACxB,EAAWyB,IAC3B,UAACwD,KAAAA,UACG,UAACC,IAAIA,CACDvE,KAAM,uBACNwE,GAAI,aAFHD,IAE6B,OAATlF,EAAKgC,GAAG,WAE5BhC,EAAK+E,KAAK,IALVtD,IAUb,UAACwD,KAAAA,UAAIvG,EAAE,gCAQnB,WAACwB,IAAAA,WACG,UAACC,IAAAA,UAAGzB,EAAE,gBAAkB,IACxB,UAACoG,OAAAA,UACIH,GAAmBA,EAAgBW,UAAU,CACxCX,EAAgBW,UAAU,CAC1B5G,EAAE,0BAOhB,WAACwB,IAAAA,WACG,UAACC,IAAAA,UAAGzB,EAAE,UAAY,IAClB,UAACoG,OAAAA,UACIH,GAAmBA,EAAgBY,IAAI,CAClCZ,EAAgBY,IAAI,CACpB7G,EAAE,2BAuBxB,iJCxGA,MApBoC,IAChC,IAAM8G,EAA0BC,CAAAA,EAAAA,EAAAA,aAmBrBC,UAnBqBD,CAAuBA,CAAC,IACpD,UAAC7G,CAkBiC8G,CAlBjC9G,CAkBkC,MAlBfA,CAAAA,CAAE,GAAGH,EAAMkH,IAAI,IAGvC,MACI,iCACI,UAAC9G,EAAAA,CAASA,CAAAA,CAACM,UAAU,+BACjB,UAACuF,EAAAA,OAAiBA,CAAAA,CAAE,GAAGjG,CAAK,KAEhC,UAACI,EAAAA,CAASA,CAAAA,CAACM,UAAU,+BAClB,UAACqF,EAAAA,OAAqBA,CAAAA,CAAE,GAAG/F,EAAMkG,eAAe,KAEnD,UAAC9F,EAAAA,CAASA,CAAAA,CAACM,UAAU,+BACjB,UAACqG,EAAAA,CAAAA,OAIjB,mBClCA,4CACA,sDACA,WACA,OAAe,EAAQ,KAA2E,CAClG,EACA,SAFsB,gBCIiB,CAGtC,YAA4B,aAI7B,oBACA,OACA,iCACA,iCACA,0BACA,0BACA,+BACA,8BACA,8BACA,4BACA,6BAEA,yBAGA,qBACA,qGACA,KAEA,YACA,2EACA,oBACA,SACA,oEACA,KAEA,uDACA,8CACA,sBACA,gBACA,WACA,eACA,eACA,kBACA,yBACA,+BACA,CAAS,CACT,UACA,8BACA,aACA,+BACA,8BACA,gCACA,uCACA,CAAS,CACT,cACA,eACA,cACA,sBACA,iBACA,IACA,gBACA,IACA,gBACA,IACA,KACA,IACA,eACA,IACA,KACA,IACA,IACA,CAAS,CACT,2BAAoC,IAAI,IACxC,cACA,MACA,MACA,KACA,CAAS,CACJ,EAIL,CAAC,CAhFiD,EAAQ,KAAW,YAAZ", "sources": ["webpack://_N_E/./pages/institution/components/DiscussionAccordion.tsx", "webpack://_N_E/./components/common/ReactImages.tsx", "webpack://_N_E/./pages/institution/permission.tsx", "webpack://_N_E/./pages/institution/components/MediaGalleryAccordion.tsx", "webpack://_N_E/./pages/institution/components/MoreInfoAccordion.tsx", "webpack://_N_E/./pages/institution/components/InstitutionAccordionSection.tsx", "webpack://_N_E/?e9c0", "webpack://_N_E/./node_modules/moment/locale/de.js"], "sourcesContent": ["//Import Library\r\nimport React from \"react\";\r\nimport { Accordion } from \"react-bootstrap\";\r\n\r\n//Import services/components\r\nimport Discussion from \"../../../components/common/disussion\";\r\nimport { useTranslation } from 'next-i18next';\r\n\r\nconst DiscussionAccordion = (props: any) => {\r\n    const { t } = useTranslation('common');\r\n    return (\r\n        <Accordion defaultActiveKey=\"2\">\r\n            <Accordion.Item eventKey=\"2\">\r\n                <Accordion.Header>\r\n                    <div className=\"cardTitle\">{t(\"Discussions\")}</div>\r\n                </Accordion.Header>\r\n                <Accordion.Body>\r\n                    <Discussion\r\n                        type=\"hazard\"\r\n                        id={props && props.routes ? props.routes[1] : null}\r\n                    />\r\n                </Accordion.Body>\r\n            </Accordion.Item>\r\n        </Accordion>\r\n    );\r\n};\r\nexport default DiscussionAccordion;", "\r\n//Import Library\r\nimport React, { useState, useEffect } from 'react';\r\nimport { Carousel } from 'react-responsive-carousel';\r\nimport { FontAwesomeIcon } from \"@fortawesome/react-fontawesome\";\r\nimport {\r\n  faLink, faDownload\r\n} from \"@fortawesome/free-solid-svg-icons\";\r\nimport { Button } from \"react-bootstrap\";\r\n\r\n//Import services/components\r\nimport { useTranslation } from 'next-i18next';\r\n\r\n// Import CSS for react-responsive-carousel\r\nimport \"react-responsive-carousel/lib/styles/carousel.min.css\";\r\n\r\n// Define types for image items\r\ninterface ImageItem {\r\n  src: string;\r\n  description: string;\r\n  originalName: string;\r\n  downloadLink: string | false;\r\n}\r\n\r\ninterface ReactImagesProps {\r\n  gallery?: Array<{\r\n    _id: string;\r\n    name: string;\r\n    original_name?: string;\r\n    src?: string;\r\n    caption?: string;\r\n    alt?: string;\r\n  }> | boolean;\r\n  imageSource?: string[] | boolean;\r\n}\r\n\r\nconst ReactImages = (props: ReactImagesProps) => {\r\n  const { t } = useTranslation('common');\r\n  const [images, setImages] = useState<ImageItem[]>([]);\r\n\r\n  // Render image description and metadata\r\n  const renderImageLegend = (item: ImageItem) => {\r\n    const isValidLink = /(http|https):\\/\\/(\\w+:{0,1}\\w*)?(\\S+)(:[0-9]+)?(\\/|\\/([\\w#!:.?+=&%!\\-\\/]))?/.test(item.description);\r\n\r\n    return (\r\n      <div className=\"carousel-legend\">\r\n        <p className=\"lead\">\r\n          <b>{t(\"Filename\")}</b> {item.originalName || \"No Name found\"}\r\n        </p>\r\n        {item.description && (\r\n          <div className=\"source_link\">\r\n            <p><b>{t(\"Source\")}</b></p>\r\n            {isValidLink ? (\r\n              <div>\r\n                <FontAwesomeIcon icon={faLink} size=\"1x\" color=\"#999\" className=\"me-1\" />\r\n                <a className=\"source_link\" href={item.description} target=\"_blank\" rel=\"noopener noreferrer\">\r\n                  {item.description}\r\n                </a>\r\n              </div>\r\n            ) : (\r\n              <div>\r\n                <p className=\"ps-0 py-0\" style={{ wordBreak: \"break-all\" }}>\r\n                  {item.description}\r\n                </p>\r\n              </div>\r\n            )}\r\n          </div>\r\n        )}\r\n        {item.downloadLink && (\r\n          <Button className=\"btn btn-success mt-2 btn--download\" href={item.downloadLink}>\r\n            {t(\"Download\")}\r\n            <FontAwesomeIcon icon={faDownload} size=\"1x\" className=\"ms-1\" />\r\n          </Button>\r\n        )}\r\n      </div>\r\n    );\r\n  };\r\n\r\n  useEffect(() => {\r\n    const carouselImages: ImageItem[] = [];\r\n    props && props.gallery && Array.isArray(props.gallery) && props.gallery.map((item, i) => {\r\n      const fileType = item && item.name.split('.').pop();\r\n      let imgSrc;\r\n\r\n      switch (fileType) {\r\n        case \"JPG\":\r\n        case \"jpg\":\r\n        case \"jpeg\":\r\n        case \"png\":\r\n          imgSrc = `${process.env.API_SERVER}/image/show/${item._id}`;\r\n          break;\r\n        case \"pdf\":\r\n          imgSrc = \"/images/fileIcons/pdfFile.png\";\r\n          break;\r\n        case \"docx\":\r\n          imgSrc = \"/images/fileIcons/wordFile.png\";\r\n          break;\r\n        case \"xls\":\r\n        case 'xlsx':\r\n          imgSrc = \"/images/fileIcons/xlsFile.png\";\r\n          break;\r\n        default:\r\n          imgSrc = \"/images/fileIcons/otherFile.png\";\r\n      }\r\n\r\n      const _link = (fileType === \"docx\" || fileType === \"pdf\" || fileType === \"xls\" || fileType === \"xlsx\")\r\n        && `${process.env.API_SERVER}/files/download/${item._id}`;\r\n      const _name = `${item && item.original_name ? item.original_name : \"No Name found\"}`;\r\n      const _description = props.imageSource && Array.isArray(props.imageSource)\r\n        && props.imageSource.length > 0 ? props.imageSource[i] : \"\";\r\n\r\n      carouselImages.push({\r\n        src: imgSrc,\r\n        description: _description,\r\n        originalName: _name,\r\n        downloadLink: _link\r\n      });\r\n    });\r\n    setImages(carouselImages);\r\n  }, [props]);\r\n\r\n  return (\r\n    <div>\r\n      {images && images.length === 0 ? (\r\n        <div className=\"border border-info my-3 mx-0\">\r\n          <p className=\"d-flex d-flex justify-content-center p-2 m-0\">{t(\"NoFilesFound!\")}</p>\r\n        </div>\r\n      ) : (\r\n        <Carousel\r\n          showThumbs={true}\r\n          showStatus={true}\r\n          showIndicators={true}\r\n          infiniteLoop={true}\r\n          useKeyboardArrows={true}\r\n          autoPlay={false}\r\n          stopOnHover={true}\r\n          swipeable={true}\r\n          dynamicHeight={false}\r\n          emulateTouch={true}\r\n          renderThumbs={() =>\r\n            images.map((item, index) => (\r\n              <img\r\n                key={index}\r\n                src={item.src}\r\n                alt={`Thumbnail ${index + 1}`}\r\n                style={{ width: '60px', height: '60px', objectFit: 'cover' }}\r\n              />\r\n            ))\r\n          }\r\n        >\r\n          {images.map((item, index) => (\r\n            <div key={index}>\r\n              <img\r\n                src={item.src}\r\n                alt={item.originalName || \"Gallery image\"}\r\n                style={{ maxHeight: '500px', objectFit: 'contain' }}\r\n              />\r\n              {renderImageLegend(item)}\r\n            </div>\r\n          ))}\r\n        </Carousel>\r\n      )}\r\n    </div>\r\n  );\r\n\r\n}\r\n\r\nexport default ReactImages;\r\n", "//Import Library\r\nimport React from 'react';\r\nimport connectedAuthWrapper from 'redux-auth-wrapper/connectedAuthWrapper';\r\n\r\n//Import services/components\r\nimport R403 from \"../r403\";\r\n\r\nexport const canAddInstitution = connectedAuthWrapper({\r\n  authenticatedSelector: (state) => {\r\n    if (state.permissions && state.permissions.institution && state.permissions.institution['create:any']) {\r\n      return true;\r\n    }\r\n    return false;\r\n  },\r\n  wrapperDisplayName: 'CanAddInstitution',\r\n});\r\n\r\nexport const canAddInstitutionForm = connectedAuthWrapper({\r\n  authenticatedSelector: (state) => {\r\n    if (state.permissions && state.permissions.institution && state.permissions.institution['create:any']) {\r\n      return true;\r\n    }\r\n    return false;\r\n  },\r\n  wrapperDisplayName: 'CanAddInstitutionForm',\r\n  FailureComponent: () => <R403/>\r\n});\r\n\r\nexport const canEditInstitution = connectedAuthWrapper({\r\n  authenticatedSelector: (state, props) => {\r\n    if (state.permissions && state.permissions.institution) {\r\n      if (state.permissions.institution['update:any']) {\r\n        return true;\r\n      } else { //if update:own\r\n        if (state.permissions.institution['update:own']) {\r\n          if (props.institution && props.institution.user && props.institution.user === state.user._id) {\r\n            return true;\r\n          }\r\n        }\r\n      }\r\n    }\r\n    return false;\r\n  },\r\n  wrapperDisplayName: 'CanEditInstitution',\r\n});\r\n\r\nexport const canEditInstitutionForm = connectedAuthWrapper({\r\n  authenticatedSelector: (state, props) => {\r\n    if (state.permissions && state.permissions.institution) {\r\n      if (state.permissions.institution['update:any']) {\r\n        return true;\r\n      } else { //if update:own\r\n        if (state.permissions.institution['update:own']) {\r\n          if (props.institution && props.institution.user && props.institution.user === state.user._id) {\r\n            return true;\r\n          }\r\n        }\r\n      }\r\n    }\r\n    return false;\r\n  },\r\n  wrapperDisplayName: 'CanEditInstitutionForm',\r\n  FailureComponent: () => <R403/>\r\n});\r\n\r\nexport const canViewDiscussionUpdate = connectedAuthWrapper({\r\n  authenticatedSelector: (state) => {\r\n    if (state.permissions && state.permissions.update && state.permissions.update['read:any']) {\r\n      return true;\r\n    }\r\n    return false;\r\n  },\r\n  wrapperDisplayName: 'CanViewDiscussionUpdate',\r\n});\r\n\r\nexport const canManageFocalPoints = connectedAuthWrapper({\r\n  authenticatedSelector: (state, props) => {\r\n    if (state.permissions && state.permissions.institution_focal_point) {\r\n      if (state.permissions.institution_focal_point[\"update:any\"]) {\r\n        return true;\r\n      } else {\r\n        if (state.permissions.institution_focal_point[\"update:own\"]) {\r\n          if (\r\n            props.institution &&\r\n            props.institution.user &&\r\n            props.institution.user === state.user._id\r\n          ) {\r\n            return true;\r\n          }\r\n        }\r\n      }\r\n    }\r\n    return false;\r\n  },\r\n  wrapperDisplayName: \"canManageFocalPoints\",\r\n});\r\n\r\nexport default canAddInstitution;", "//Import Library\r\nimport React from \"react\";\r\nimport { Accordion } from \"react-bootstrap\";\r\n\r\n//Import services/components\r\nimport ReactImages from \"../../../components/common/ReactImages\";\r\nimport { useTranslation } from 'next-i18next';\r\n\r\nconst MediaGalleryAccordion = (props: any) => {\r\n    const { t } = useTranslation('common');\r\n    return (\r\n        <Accordion defaultActiveKey=\"1\">\r\n            <Accordion.Item eventKey=\"1\">\r\n                <Accordion.Header>\r\n                    <div className=\"cardTitle\">{t(\"MediaGallery\")}</div>\r\n                </Accordion.Header>\r\n                <Accordion.Body>\r\n                    <ReactImages\r\n                        gallery={props.images}\r\n                        imageSource={props.images_src}\r\n                    />\r\n                </Accordion.Body>\r\n            </Accordion.Item>\r\n        </Accordion>\r\n    )\r\n}\r\n\r\nexport default MediaGalleryAccordion;", "import React from \"react\";\r\nimport { Accordion } from \"react-bootstrap\";\r\nimport Link from \"next/link\";\r\nimport { useTranslation } from 'next-i18next';\r\n\r\nconst MoreInfoAccordion = (props: any) => {\r\n    const { t } = useTranslation('common');\r\n    const { institutionData, activeOperations, activeProjects } = props;\r\n\r\n    // Render organization type\r\n    const renderOrganizationType = () => (\r\n        <p>\r\n            <b>{t(\"OrganisationType\")}</b>:\r\n            <span>\r\n                {\" \"}\r\n                {institutionData.type ? institutionData.type.title : \"\"}\r\n            </span>\r\n        </p>\r\n    );\r\n\r\n    // Render networks\r\n    const renderNetworks = () => (\r\n        <p>\r\n            <b>{t(\"Network\")}</b>:\r\n            <span>\r\n                {institutionData.networks\r\n                    ? institutionData.networks.map((item: any, index: any) => (\r\n                        <span key={index}>\r\n                            <li>{item.title}</li>\r\n                        </span>\r\n                    ))\r\n                    : \"\"}\r\n            </span>\r\n        </p>\r\n    );\r\n\r\n    // Render active operations\r\n    const renderActiveOperations = () => (\r\n        <p>\r\n            <b>{t(\"ActiveOperation\")}</b>:\r\n            <span>\r\n                {activeOperations && activeOperations.length > 0 ? (\r\n                    activeOperations.map((item: any, i: any) => (\r\n                        <li key={i}>\r\n                            <Link\r\n                                href={\"/operation/[...routes]\"}\r\n                                as={`/operation/show/${item._id}`}\r\n                            >\r\n                                {item.title}\r\n                            </Link>\r\n                        </li>\r\n                    ))\r\n                ) : (\r\n                    <li>{t(\"NoActiveoperationsfound\")}</li>\r\n                )}\r\n            </span>\r\n        </p>\r\n    );\r\n\r\n    // Render expertise\r\n    const renderExpertise = () => (\r\n        <p>\r\n            <b>{t(\"Expertise\")}</b>:\r\n            <span>\r\n                {\" \"}\r\n                {institutionData.expertise\r\n                    ? institutionData.expertise.map((item: any, index: any) => (\r\n                        <li key={index}>\r\n                            {item.title} <br />\r\n                        </li>\r\n                    ))\r\n                    : \"\"}\r\n            </span>\r\n        </p>\r\n    );\r\n\r\n    // Render active projects\r\n    const renderActiveProjects = () => (\r\n        <p>\r\n            <b>{t(\"ActiveProject\")}</b>:\r\n            <span>\r\n                {activeProjects && activeProjects.length > 0 ? (\r\n                    activeProjects.map((item: any, i: any) => (\r\n                        <li key={i}>\r\n                            <Link\r\n                                href={\"/project/[...routes]\"}\r\n                                as={`/project/show/${item._id}`}\r\n                            >\r\n                                {item.title}\r\n                            </Link>\r\n                        </li>\r\n                    ))\r\n                ) : (\r\n                    <li>{t(\"NoActiveprojectsfound\")}</li>\r\n                )}\r\n            </span>\r\n        </p>\r\n    );\r\n\r\n    // Render department\r\n    const renderDepartment = () => (\r\n        <p>\r\n            <b>{t(\"Department\")}</b>:\r\n            <span>\r\n                {institutionData && institutionData.department\r\n                    ? institutionData.department\r\n                    : t(\"Nodepartmentfound\")}\r\n            </span>\r\n        </p>\r\n    );\r\n\r\n    // Render unit\r\n    const renderUnit = () => (\r\n        <p>\r\n            <b>{t(\"Unit\")}</b>:\r\n            <span>\r\n                {institutionData && institutionData.unit\r\n                    ? institutionData.unit\r\n                    : t(\"Nounitfound\")}\r\n            </span>\r\n        </p>\r\n    );\r\n\r\n    return (\r\n        <Accordion defaultActiveKey=\"0\">\r\n            <Accordion.Item eventKey=\"0\">\r\n                <Accordion.Header>\r\n                    <div className=\"cardTitle\">{t(\"MoreInfo\")}</div>\r\n                </Accordion.Header>\r\n                <Accordion.Body className=\"institutionDetails ps-4\">\r\n                    {renderOrganizationType()}\r\n                    {renderNetworks()}\r\n                    {renderActiveOperations()}\r\n                    {renderExpertise()}\r\n                    {renderActiveProjects()}\r\n                    {renderDepartment()}\r\n                    {renderUnit()}\r\n                </Accordion.Body>\r\n            </Accordion.Item>\r\n        </Accordion>\r\n    );\r\n};\r\n\r\nexport default MoreInfoAccordion;\r\n", "//Import Library\r\nimport React from \"react\";\r\nimport { Accordion } from \"react-bootstrap\";\r\n\r\n//Import services/components\r\nimport DiscussionAccordion from \"./DiscussionAccordion\";\r\nimport { canViewDiscussionUpdate } from \"../permission\";\r\nimport MoreInfoAccordion from \"./MoreInfoAccordion\";\r\nimport MediaGalleryAccordion from \"./MediaGalleryAccordion\";\r\n\r\ninterface InstitutionAccordionSectionProps {\r\n  institutionData: any;\r\n  prop: any;\r\n  activeProjects: any[];\r\n  activeOperations: any[];\r\n}\r\n\r\nconst InstitutionAccordionSection = (props: InstitutionAccordionSectionProps) => {\r\n    const CanViewDiscussionUpdate = canViewDiscussionUpdate(() => (\r\n        <DiscussionAccordion {...props.prop} />\r\n      ));\r\n\r\n    return (\r\n        <>\r\n            <Accordion className=\"countryAccordionNew\">\r\n                <MoreInfoAccordion {...props} />\r\n            </Accordion>\r\n            <Accordion className=\"countryAccordionNew\">\r\n               <MediaGalleryAccordion {...props.institutionData} />\r\n            </Accordion>\r\n            <Accordion className=\"countryAccordionNew\">\r\n                <CanViewDiscussionUpdate />\r\n            </Accordion>\r\n        </>\r\n    )\r\n}\r\n\r\nexport default InstitutionAccordionSection;", "\n    (window.__NEXT_P = window.__NEXT_P || []).push([\n      \"/institution/components/InstitutionAccordionSection\",\n      function () {\n        return require(\"private-next-pages/institution/components/InstitutionAccordionSection.tsx\");\n      }\n    ]);\n    if(module.hot) {\n      module.hot.dispose(function () {\n        window.__NEXT_P.push([\"/institution/components/InstitutionAccordionSection\"])\n      });\n    }\n  ", "//! moment.js locale configuration\n//! locale : German [de]\n//! author : lluchs : https://github.com/lluchs\n//! author: <PERSON><PERSON><PERSON>: https://github.com/Oire\n//! author : <PERSON><PERSON><PERSON><PERSON> : https://github.com/mik01aj\n\n;(function (global, factory) {\n   typeof exports === 'object' && typeof module !== 'undefined'\n       && typeof require === 'function' ? factory(require('../moment')) :\n   typeof define === 'function' && define.amd ? define(['../moment'], factory) :\n   factory(global.moment)\n}(this, (function (moment) { 'use strict';\n\n    //! moment.js locale configuration\n\n    function processRelativeTime(number, withoutSuffix, key, isFuture) {\n        var format = {\n            m: ['eine Minute', 'einer Minute'],\n            h: ['eine Stunde', 'einer Stunde'],\n            d: ['ein Tag', 'einem Tag'],\n            dd: [number + ' Tage', number + ' Tagen'],\n            w: ['eine Woche', 'einer Woche'],\n            M: ['ein <PERSON><PERSON>', 'einem Monat'],\n            MM: [number + ' <PERSON><PERSON>', number + ' Monaten'],\n            y: ['ein Jahr', 'einem Jahr'],\n            yy: [number + ' Jahre', number + ' Jahren'],\n        };\n        return withoutSuffix ? format[key][0] : format[key][1];\n    }\n\n    var de = moment.defineLocale('de', {\n        months: 'Januar_Februar_März_April_Mai_Juni_Juli_August_September_Oktober_November_Dezember'.split(\n            '_'\n        ),\n        monthsShort:\n            'Jan._Feb._März_Apr._Mai_Juni_Juli_Aug._Sep._Okt._Nov._Dez.'.split('_'),\n        monthsParseExact: true,\n        weekdays:\n            'Sonntag_Montag_Dienstag_Mittwoch_Donnerstag_Freitag_Samstag'.split(\n                '_'\n            ),\n        weekdaysShort: 'So._Mo._Di._Mi._Do._Fr._Sa.'.split('_'),\n        weekdaysMin: 'So_Mo_Di_Mi_Do_Fr_Sa'.split('_'),\n        weekdaysParseExact: true,\n        longDateFormat: {\n            LT: 'HH:mm',\n            LTS: 'HH:mm:ss',\n            L: 'DD.MM.YYYY',\n            LL: 'D. MMMM YYYY',\n            LLL: 'D. MMMM YYYY HH:mm',\n            LLLL: 'dddd, D. MMMM YYYY HH:mm',\n        },\n        calendar: {\n            sameDay: '[heute um] LT [Uhr]',\n            sameElse: 'L',\n            nextDay: '[morgen um] LT [Uhr]',\n            nextWeek: 'dddd [um] LT [Uhr]',\n            lastDay: '[gestern um] LT [Uhr]',\n            lastWeek: '[letzten] dddd [um] LT [Uhr]',\n        },\n        relativeTime: {\n            future: 'in %s',\n            past: 'vor %s',\n            s: 'ein paar Sekunden',\n            ss: '%d Sekunden',\n            m: processRelativeTime,\n            mm: '%d Minuten',\n            h: processRelativeTime,\n            hh: '%d Stunden',\n            d: processRelativeTime,\n            dd: processRelativeTime,\n            w: processRelativeTime,\n            ww: '%d Wochen',\n            M: processRelativeTime,\n            MM: processRelativeTime,\n            y: processRelativeTime,\n            yy: processRelativeTime,\n        },\n        dayOfMonthOrdinalParse: /\\d{1,2}\\./,\n        ordinal: '%d.',\n        week: {\n            dow: 1, // Monday is the first day of the week.\n            doy: 4, // The week that contains Jan 4th is the first week of the year.\n        },\n    });\n\n    return de;\n\n})));\n"], "names": ["props", "t", "useTranslation", "DiscussionAccordion", "Accordion", "defaultActiveKey", "<PERSON><PERSON>", "eventKey", "Header", "div", "className", "Body", "Discussion", "type", "id", "routes", "ReactImages", "images", "setImages", "useState", "renderImageLegend", "isValidLink", "test", "item", "description", "p", "b", "originalName", "FontAwesomeIcon", "icon", "faLink", "size", "color", "a", "href", "target", "rel", "style", "wordBreak", "downloadLink", "<PERSON><PERSON>", "faDownload", "useEffect", "carouselImages", "gallery", "Array", "isArray", "map", "i", "imgSrc", "fileType", "name", "split", "pop", "process", "_id", "_link", "_name", "original_name", "_description", "imageSource", "length", "push", "src", "Carousel", "showThumbs", "showStatus", "showIndicators", "infiniteLoop", "useKeyboardArrows", "autoPlay", "stopOnHover", "swipeable", "dynamicHeight", "emulate<PERSON><PERSON><PERSON>", "renderThumbs", "index", "img", "alt", "width", "height", "objectFit", "maxHeight", "canAddInstitution", "connectedAuthWrapper", "authenticatedSelector", "state", "permissions", "institution", "wrapperDisplayName", "FailureComponent", "R403", "user", "update", "institution_focal_point", "MediaGalleryAccordion", "images_src", "MoreInfoAccordion", "institutionData", "activeOperations", "activeProjects", "span", "title", "networks", "li", "Link", "as", "expertise", "br", "department", "unit", "CanViewDiscussionUpdate", "canViewDiscussionUpdate", "InstitutionAccordionSection", "prop"], "sourceRoot": "", "ignoreList": [7]}