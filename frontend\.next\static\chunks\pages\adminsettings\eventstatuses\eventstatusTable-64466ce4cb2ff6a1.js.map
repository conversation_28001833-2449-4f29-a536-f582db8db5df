{"version": 3, "file": "static/chunks/pages/adminsettings/eventstatuses/eventstatusTable-64466ce4cb2ff6a1.js", "mappings": "gFACA,4CACA,gDACA,WACA,OAAe,EAAQ,KAAqE,CAC5F,EACA,SAFsB,qKC+HtB,MAxHyB,IACrB,GAAM,CAAEA,CAAC,CAAE,CAAGC,CAAAA,EAAAA,EAAAA,EAAAA,CAAcA,CAAC,QAuHlBC,EAtHL,CAACC,EAAWC,EAAe,CAAGC,CAAAA,EAAAA,EAAAA,GAsHTH,EAAC,GAtHQG,CAAQA,CAAC,EAAE,EACzC,EAAGC,EAAW,CAAGD,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAC,IAC1B,CAACE,EAAWC,EAAa,CAAGH,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAC,GACrC,CAACI,EAASC,EAAW,CAAGL,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAC,IACjC,CAACM,EAAaC,EAAS,CAAGP,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,EAAC,GACnC,CAACQ,EAAmBC,EAAqB,CAAGT,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAC,CAAC,GAGtDU,EAAoB,CACtBC,KAAM,CAAEC,MAAO,KAAM,EACrBC,MAAOT,EACPU,KAAM,EACNC,MAAO,CAAC,CACZ,EAEMC,EAAU,CACZ,CACIC,KAAMtB,EAAE,wCACRuB,SAAU,OACd,EACA,CACID,KAAMtB,EAAE,yCACRuB,SAAU,GACVC,KAAM,GACF,WAACC,MAAAA,WACG,UAACC,IAAIA,CAACC,KAAK,6BAA6BC,GAAI,OAAvCF,4BAAgF,OAANG,EAAEC,GAAG,WAEhF,UAACC,IAAAA,CAAEC,UAAU,uBAEV,OAEP,UAACC,IAAAA,CAAEC,QAAS,IAAMC,EAAWN,YACzB,UAACE,IAAAA,CAAEC,UAAU,8BAI7B,EACH,CAEKI,EAAqB,UACvB9B,GAAW,GACX,IAAM+B,EAAW,MAAMC,EAAAA,CAAUA,CAACC,GAAG,CAAC,eAAgBxB,GAClDsB,GAAYA,EAASG,IAAI,EAAIH,EAASG,IAAI,CAACC,MAAM,CAAG,GAAG,CACvDrC,EAAeiC,EAASG,IAAI,EAC5BhC,EAAa6B,EAASK,UAAU,EAChCpC,GAAW,GAEnB,EAQMqC,EAAsB,MAAOC,EAAiBzB,KAChDJ,EAAkBG,KAAK,CAAG0B,EAC1B7B,EAAkBI,IAAI,CAAGA,EACzBb,GAAW,GACX,IAAM+B,EAAW,MAAMC,EAAAA,CAAUA,CAACC,GAAG,CAAC,eAAgBxB,GAClDsB,GAAYA,EAASG,IAAI,EAAIH,EAASG,IAAI,CAACC,MAAM,CAAG,GAAG,CACvDrC,EAAeiC,EAASG,IAAI,EAC5B9B,EAAWkC,GACXtC,GAAW,GAEnB,EAEM6B,EAAa,MAAOU,IACtB/B,EAAqB+B,EAAIf,GAAG,EAC5BlB,GAAS,EACb,EAEMkC,EAAe,UACjB,GAAI,CACA,MAAMR,EAAAA,CAAUA,CAACS,MAAM,CAAC,gBAAkC,OAAlBlC,IACxCuB,IACAxB,GAAS,GACToC,EAAAA,EAAKA,CAACC,OAAO,CAACjD,EAAE,iEACpB,CAAE,MAAOkD,EAAO,CACZF,EAAAA,EAAKA,CAACE,KAAK,CAAClD,EAAE,2DAClB,CACJ,EAEMmD,EAAY,IAAMvC,GAAS,GAMjC,MAJAwC,CAAAA,EAAAA,EAAAA,SAAAA,CAASA,CAAC,KACNhB,GACJ,EAAG,EAAE,EAGD,WAACX,MAAAA,WACG,WAAC4B,EAAAA,CAAKA,CAAAA,CAACC,KAAM3C,EAAa4C,OAAQJ,YAC9B,UAACE,EAAAA,CAAKA,CAACG,MAAM,EAACC,WAAW,aACrB,UAACJ,EAAAA,CAAKA,CAACK,KAAK,WAAE1D,EAAE,wDAEpB,UAACqD,EAAAA,CAAKA,CAACM,IAAI,WAAE3D,EAAE,2EACf,WAACqD,EAAAA,CAAKA,CAACO,MAAM,YACT,UAACC,EAAAA,CAAMA,CAAAA,CAACC,QAAQ,YAAY5B,QAASiB,WAChCnD,EAAE,2CAEP,UAAC6D,EAAAA,CAAMA,CAAAA,CAACC,QAAQ,UAAU5B,QAASY,WAC9B9C,EAAE,8CAKf,UAAC+D,EAAAA,CAAQA,CAAAA,CACL1C,QAASA,EACTmB,KAAMrC,EACNI,UAAWA,EACXyD,WAAW,EACXrB,oBAAqBA,EACrBsB,iBA/Da,CA+DKA,GA9D1BlD,EAAkBG,KAAK,CAAGT,EAC1BM,EAAkBI,IAAI,CAAGA,EACzBiB,GACJ,MA+DJ,6GC5FA,SAAS2B,EAASG,CAAoB,EACpC,GAAM,CAAElE,CAAC,CAAE,CAAGC,CAAAA,EAAAA,EAAAA,EAAAA,CAAcA,CAAC,UACvBkE,EAA6B,CACjCC,gBAAiBpE,EAAE,cACnB,EACI,SACJqB,CAAO,MACPmB,CAAI,WACJjC,CAAS,uBACT8D,CAAqB,WACrBC,CAAS,oBACTC,CAAkB,CAClB5B,qBAAmB,kBACnBsB,CAAgB,aAChBO,CAAW,oBACXC,CAAkB,gBAClBC,CAAc,SACdC,CAAO,WACPX,CAAS,sBACTY,CAAoB,mBACpBC,CAAiB,YACjBC,CAAU,QACVC,CAAM,kBACNC,CAAgB,cAChBC,CAAY,CAEZ,CADA,EACGC,EACJ,CAAGhB,EAGEiB,EAAiB,CACrBhB,6BACAiB,gBAAiBpF,EAAE,IAP0C,MAQ7DqF,UAAU,UACVhE,EACAmB,KAAMA,GAAQ,EAAE,CAChB8C,OAAO,EACPC,2BAA4BlB,EAC5BmB,UAAWlB,EACXmB,gBAAiBd,qBACjBJ,EACAmB,YAAY,EACZC,iBAAkB3B,EAClB4B,kBAAmBnB,GAA0C,GAC7DoB,eADwCpB,cACVD,GAA4B,CAAC,GAAI,GAAI,GAAI,CAA3BA,EAA+B,GAAG,CAC9EsB,oBAAqBvF,EACrBwF,oBAAqBpD,EACrBqD,aAAc/B,EACdS,iBACAE,yCACAC,EACAoB,kBAAmB,UAACC,EAAAA,CAAYA,CAAAA,CAAAA,GAChCC,SAAU,UAACpE,IAAAA,CAAEC,UAAU,6CACvB8C,SACAC,eACAE,mBACAD,EACAhD,UAAW,WACb,EACA,MACE,UAACoE,EAAAA,EAASA,CAAAA,CAAE,GAAGjB,CAAc,EAEjC,CAEApB,EAASsC,YAAY,CAAG,CACtBb,WAAW,EACXE,YAAY,EACZnF,UAAW,KACXyD,WAAW,EACXY,qBAAsB,KACtBC,mBAAmB,EACnBC,YAAY,EACZE,iBAAkB,EACpB,EAEA,MAAejB,QAAQA,EAAC", "sources": ["webpack://_N_E/?7419", "webpack://_N_E/./pages/adminsettings/eventstatuses/eventstatusTable.tsx", "webpack://_N_E/./components/common/RKITable.tsx"], "sourcesContent": ["\n    (window.__NEXT_P = window.__NEXT_P || []).push([\n      \"/adminsettings/eventstatuses/eventstatusTable\",\n      function () {\n        return require(\"private-next-pages/adminsettings/eventstatuses/eventstatusTable.tsx\");\n      }\n    ]);\n    if(module.hot) {\n      module.hot.dispose(function () {\n        window.__NEXT_P.push([\"/adminsettings/eventstatuses/eventstatusTable\"])\n      });\n    }\n  ", "//Import Library\r\nimport { useState, useEffect } from \"react\";\r\nimport Link from \"next/link\";\r\nimport { <PERSON><PERSON>, Button } from \"react-bootstrap\";\r\nimport toast from 'react-hot-toast';\r\n\r\n//Import services/components\r\nimport { useTranslation } from 'next-i18next';\r\nimport RKITable from \"../../../components/common/RKITable\";\r\nimport apiService from \"../../../services/apiService\";\r\n\r\nconst EventstatusTable = (_props: any) => {\r\n    const { t } = useTranslation('common');\r\n    const [tabledata, setDataToTable] = useState([]);\r\n    const [, setLoading] = useState(false);\r\n    const [totalRows, setTotalRows] = useState(0);\r\n    const [perPage, setPerPage] = useState(10);\r\n    const [isModalShow, setModal] = useState(false);\r\n    const [selectEventstatus, setSelectEventstatus] = useState({});\r\n\r\n\r\n    const eventstatusParams = {\r\n        sort: { title: \"asc\" },\r\n        limit: perPage,\r\n        page: 1,\r\n        query: {},\r\n    };\r\n\r\n    const columns = [\r\n        {\r\n            name: t(\"adminsetting.EventStatus.Table.Title\"),\r\n            selector: \"title\",\r\n        },\r\n        {\r\n            name: t(\"adminsetting.EventStatus.Table.Action\"),\r\n            selector: \"\",\r\n            cell: (d: any) => (\r\n                <div>\r\n                    <Link href=\"/adminsettings/[...routes]\" as={`/adminsettings/edit_eventstatus/${d._id}`}>\r\n\r\n                        <i className=\"icon fas fa-edit\" />\r\n\r\n                    </Link>\r\n                    &nbsp;\r\n                    <a onClick={() => userAction(d)}>\r\n                        <i className=\"icon fas fa-trash-alt\" />\r\n                    </a>\r\n                </div>\r\n            ),\r\n        },\r\n    ];\r\n\r\n    const geteventstatusData = async () => {\r\n        setLoading(true);\r\n        const response = await apiService.get(\"/eventstatus\", eventstatusParams);\r\n        if (response && response.data && response.data.length > 0) {\r\n            setDataToTable(response.data);\r\n            setTotalRows(response.totalCount);\r\n            setLoading(false);\r\n        }\r\n    };\r\n\r\n    const handlePageChange = (page: any) => {\r\n        eventstatusParams.limit = perPage;\r\n        eventstatusParams.page = page;\r\n        geteventstatusData();\r\n    };\r\n\r\n    const handlePerRowsChange = async (newPerPage: any, page: any) => {\r\n        eventstatusParams.limit = newPerPage;\r\n        eventstatusParams.page = page;\r\n        setLoading(true);\r\n        const response = await apiService.get(\"/eventstatus\", eventstatusParams);\r\n        if (response && response.data && response.data.length > 0) {\r\n            setDataToTable(response.data);\r\n            setPerPage(newPerPage);\r\n            setLoading(false);\r\n        }\r\n    };\r\n\r\n    const userAction = async (row: any) => {\r\n        setSelectEventstatus(row._id);\r\n        setModal(true);\r\n    };\r\n\r\n    const modalConfirm = async () => {\r\n        try {\r\n            await apiService.remove(`/eventstatus/${selectEventstatus}`);\r\n            geteventstatusData();\r\n            setModal(false);\r\n            toast.success(t(\"adminsetting.EventStatus.Table.eventStatusDeletedSuccessfully\"));\r\n        } catch (error) {\r\n            toast.error(t(\"adminsetting.EventStatus.Table.errorDeletingEventStatus\"));\r\n        }\r\n    };\r\n\r\n    const modalHide = () => setModal(false);\r\n\r\n    useEffect(() => {\r\n        geteventstatusData();\r\n    }, []);\r\n\r\n    return (\r\n        <div>\r\n            <Modal show={isModalShow} onHide={modalHide}>\r\n                <Modal.Header closeButton>\r\n                    <Modal.Title>{t(\"adminsetting.EventStatus.Table.DeleteEventstatus\")}</Modal.Title>\r\n                </Modal.Header>\r\n                <Modal.Body>{t(\"adminsetting.EventStatus.Table.Areyousurewanttodeletethiseventstatus?\")}</Modal.Body>\r\n                <Modal.Footer>\r\n                    <Button variant=\"secondary\" onClick={modalHide}>\r\n                        {t(\"adminsetting.EventStatus.Table.Cancel\")}\r\n                    </Button>\r\n                    <Button variant=\"primary\" onClick={modalConfirm}>\r\n                        {t(\"adminsetting.EventStatus.Table.Yes\")}\r\n                    </Button>\r\n                </Modal.Footer>\r\n            </Modal>\r\n\r\n            <RKITable\r\n                columns={columns}\r\n                data={tabledata}\r\n                totalRows={totalRows}\r\n                pagServer={true}\r\n                handlePerRowsChange={handlePerRowsChange}\r\n                handlePageChange={handlePageChange}\r\n            />\r\n        </div>\r\n    );\r\n};\r\n\r\nexport default EventstatusTable;\r\n", "//Import Library\r\nimport React from 'react'\r\nimport DataTable  from 'react-data-table-component';\r\n\r\n//Import services/components\r\nimport { useTranslation } from 'next-i18next';\r\nimport CustomLoader from './CustomLoader';\r\n\r\ninterface RKITableProps {\r\n  columns: any[];\r\n  data: any[];\r\n  totalRows?: number;\r\n  resetPaginationToggle?: boolean;\r\n  subheader?: boolean;\r\n  subHeaderComponent?: React.ReactNode;\r\n  handlePerRowsChange?: (newPerPage: number, page: number) => void;\r\n  handlePageChange?: (page: number) => void;\r\n  rowsPerPage?: number[];\r\n  defaultRowsPerPage?: number;\r\n  selectableRows?: boolean;\r\n  loading?: boolean;\r\n  pagServer?: boolean;\r\n  onSelectedRowsChange?: (selectedRows: any) => void;\r\n  clearSelectedRows?: boolean;\r\n  sortServer?: boolean;\r\n  onSort?: (column: any, sortDirection: string) => void;\r\n  persistTableHead?: boolean;\r\n  sortFunction?: (rows: any[], field: any, direction: 'asc' | 'desc') => any[];\r\n  noHeader?: boolean;\r\n  dense?: boolean;\r\n  pagination?: boolean;\r\n  paginationServer?: boolean;\r\n  paginationTotalRows?: number;\r\n  subHeaderAlign?: string;\r\n  [key: string]: any;\r\n}\r\n\r\nfunction RKITable(props: RKITableProps) {\r\n  const { t } = useTranslation('common');\r\n  const paginationComponentOptions = {\r\n    rowsPerPageText: t('Rowsperpage'),\r\n    };\r\n  const {\r\n    columns,\r\n    data,\r\n    totalRows,\r\n    resetPaginationToggle,\r\n    subheader,\r\n    subHeaderComponent,\r\n    handlePerRowsChange,\r\n    handlePageChange,\r\n    rowsPerPage,\r\n    defaultRowsPerPage,\r\n    selectableRows,\r\n    loading,\r\n    pagServer,\r\n    onSelectedRowsChange,\r\n    clearSelectedRows,\r\n    sortServer,\r\n    onSort,\r\n    persistTableHead,\r\n    sortFunction,\r\n    // Filter out any unknown props that might cause DOM warnings\r\n    ...otherProps\r\n  } = props;\r\n\r\n  // Only pass known DataTable props to avoid DOM warnings\r\n  const dataTableProps = {\r\n    paginationComponentOptions,\r\n    noDataComponent: t(\"NoData\"),\r\n    noHeader: true,\r\n    columns,\r\n    data: data || [],\r\n    dense: true,\r\n    paginationResetDefaultPage: resetPaginationToggle,\r\n    subHeader: subheader,\r\n    progressPending: loading,\r\n    subHeaderComponent,\r\n    pagination: true,\r\n    paginationServer: pagServer,\r\n    paginationPerPage: defaultRowsPerPage ? defaultRowsPerPage : 10,\r\n    paginationRowsPerPageOptions: rowsPerPage ? rowsPerPage : [10, 15, 20, 25, 30],\r\n    paginationTotalRows: totalRows,\r\n    onChangeRowsPerPage: handlePerRowsChange,\r\n    onChangePage: handlePageChange,\r\n    selectableRows,\r\n    onSelectedRowsChange,\r\n    clearSelectedRows,\r\n    progressComponent: <CustomLoader/>,\r\n    sortIcon: <i className=\"sort-icon fas fa-exchange-alt\" />,\r\n    sortServer,\r\n    onSort,\r\n    sortFunction,\r\n    persistTableHead,\r\n    className: \"rki-table\"\r\n  };\r\n  return (\r\n    <DataTable {...dataTableProps} />\r\n  )\r\n};\r\n\r\nRKITable.defaultProps = {\r\n  subHeader: false,\r\n  pagination: true,\r\n  totalRows: null,\r\n  pagServer: true,\r\n  onSelectedRowsChange: null,\r\n  clearSelectedRows: false,\r\n  sortServer: false,\r\n  persistTableHead: false\r\n}\r\n\r\nexport default RKITable;\r\n\r\n"], "names": ["t", "useTranslation", "EventstatusTable", "tabledata", "setDataToTable", "useState", "setLoading", "totalRows", "setTotalRows", "perPage", "setPerPage", "isModalShow", "setModal", "selectEventstatus", "setSelectEventstatus", "eventstatusParams", "sort", "title", "limit", "page", "query", "columns", "name", "selector", "cell", "div", "Link", "href", "as", "d", "_id", "i", "className", "a", "onClick", "userAction", "geteventstatusData", "response", "apiService", "get", "data", "length", "totalCount", "handlePerRowsChange", "newPerPage", "row", "modalConfirm", "remove", "toast", "success", "error", "modalHide", "useEffect", "Modal", "show", "onHide", "Header", "closeButton", "Title", "Body", "Footer", "<PERSON><PERSON>", "variant", "RKITable", "pagServer", "handlePageChange", "props", "paginationComponentOptions", "rowsPerPageText", "resetPaginationToggle", "subheader", "subHeaderComponent", "rowsPerPage", "defaultRowsPerPage", "selectableRows", "loading", "onSelectedRowsChange", "clearSelectedRows", "sortServer", "onSort", "persistTableHead", "sortFunction", "otherProps", "dataTableProps", "noDataComponent", "<PERSON><PERSON><PERSON><PERSON>", "dense", "paginationResetDefaultPage", "subHeader", "progressPending", "pagination", "paginationServer", "paginationPerPage", "paginationRowsPerPageOptions", "paginationTotalRows", "onChangeRowsPerPage", "onChangePage", "progressComponent", "CustomLoader", "sortIcon", "DataTable", "defaultProps"], "sourceRoot": "", "ignoreList": []}