(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[2317],{9227:(e,a,t)=>{"use strict";t.r(a),t.d(a,{default:()=>h});var r=t(37876),o=t(32890),n=t(60347),s=t(11041),c=t(21772),i=t(14232),d=t(48477),l=t(31753),u=t(86207),m=t(75657),p=t(81434);let h=e=>{let{t:a}=(0,l.Bd)("common"),[t,h]=(0,i.useState)(!1),g=()=>{var n;return(0,r.jsxs)(o.A.Item,{eventKey:"1",children:[(0,r.jsxs)(o<PERSON><PERSON><PERSON>,{onClick:()=>h(!t),children:[(0,r.jsx)("div",{className:"cardTitle",children:a("discussions")}),(0,r.jsx)("div",{className:"cardArrow",children:t?(0,r.jsx)(c.g,{icon:s.EZy,color:"#fff"}):(0,r.jsx)(c.g,{icon:s.QLR,color:"#fff"})})]}),(0,r.jsx)(o.A.Body,{children:(0,r.jsx)(d.A,{type:"hazard",id:(null==e?void 0:e.routeData)&&(null==e||null==(n=e.routeData)?void 0:n.routes)?e.routeData.routes[1]:null})})]})},x=(0,u.default)(()=>(0,r.jsx)(g,{}));return(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(o.A,{className:"countryAccordionNew",children:(0,r.jsx)(p.default,{...e})}),(0,r.jsx)(o.A,{className:"countryAccordionNew",children:(0,r.jsx)(n.default,{...e})}),(0,r.jsx)(o.A,{className:"countryAccordionNew",children:(0,r.jsx)(x,{})}),(0,r.jsx)(o.A,{className:"countryAccordion",children:(0,r.jsx)(m.default,{loading:e.documentAccoirdianProps.loading,Document:e.documentAccoirdianProps.Document,updateDocument:e.documentAccoirdianProps.updateDocument,hazardDocSort:e.documentAccoirdianProps.hazardDocSort,hazardDocUpdateSort:e.documentAccoirdianProps.hazardDocUpdateSort,docSrc:e.documentAccoirdianProps.docSrc,routeData:e.routeData})})]})}},34381:(e,a,t)=>{"use strict";t.d(a,{A:()=>l});var r=t(37876),o=t(14232),n=t(48230),s=t.n(n),c=t(50749),i=t(53718),d=t(31753);let l=e=>{let{t:a}=(0,d.Bd)("common"),{type:t,id:n}=e,[l,u]=(0,o.useState)([]),[m,p]=(0,o.useState)(!1),[h,g]=(0,o.useState)(0),[x,_]=(0,o.useState)(10),[j]=(0,o.useState)(!1),A={sort:{created_at:"asc"},limit:x,page:1,query:{}},f=[{name:a("Title"),selector:"title",cell:e=>e&&e.title&&e._id?(0,r.jsx)(s(),{href:"/vspace/[...routes]",as:"/vspace/show/".concat(e._id),children:e.title}):""},{name:a("Owner"),selector:"users",cell:e=>e&&e.user&&e.user.firstname?"".concat(e.user.firstname," ").concat(e.user.lastname):""},{name:a("PublicPrivate"),selector:"visibility",cell:e=>e&&e.visibility?"Public":"Private"},{name:a("NumberofMembers"),selector:"members",cell:e=>e&&e.members?e.members.length:"-"}],D=async e=>{p(!0);let a=await i.A.get("stats/get".concat(t,"WithVspace/").concat(n),A);a&&("Operation"===t?u(a.operation):u(a.project),g(a.totalCount),p(!1))},v=async(e,a)=>{A.limit=e,A.page=a,p(!0);let r=await i.A.get("stats/get".concat(t,"WithVspace/").concat(n),A);r&&("Operation"===t?u(r.operation):u(r.project),_(e),p(!1))};return(0,o.useEffect)(()=>{D(A)},[]),(0,r.jsx)("div",{children:(0,r.jsx)(c.A,{columns:f,data:l,totalRows:h,loading:m,resetPaginationToggle:j,handlePerRowsChange:v,handlePageChange:e=>{A.limit=x,A.page=e,D(A)}})})}},50749:(e,a,t)=>{"use strict";t.d(a,{A:()=>i});var r=t(37876);t(14232);var o=t(89773),n=t(31753),s=t(5507);function c(e){let{t:a}=(0,n.Bd)("common"),t={rowsPerPageText:a("Rowsperpage")},{columns:c,data:i,totalRows:d,resetPaginationToggle:l,subheader:u,subHeaderComponent:m,handlePerRowsChange:p,handlePageChange:h,rowsPerPage:g,defaultRowsPerPage:x,selectableRows:_,loading:j,pagServer:A,onSelectedRowsChange:f,clearSelectedRows:D,sortServer:v,onSort:S,persistTableHead:y,sortFunction:w,...M}=e,P={paginationComponentOptions:t,noDataComponent:a("NoData"),noHeader:!0,columns:c,data:i||[],dense:!0,paginationResetDefaultPage:l,subHeader:u,progressPending:j,subHeaderComponent:m,pagination:!0,paginationServer:A,paginationPerPage:x||10,paginationRowsPerPageOptions:g||[10,15,20,25,30],paginationTotalRows:d,onChangeRowsPerPage:p,onChangePage:h,selectableRows:_,onSelectedRowsChange:f,clearSelectedRows:D,progressComponent:(0,r.jsx)(s.A,{}),sortIcon:(0,r.jsx)("i",{className:"sort-icon fas fa-exchange-alt"}),sortServer:v,onSort:S,sortFunction:w,persistTableHead:y,className:"rki-table"};return(0,r.jsx)(o.Ay,{...P})}c.defaultProps={subHeader:!1,pagination:!0,totalRows:null,pagServer:!0,onSelectedRowsChange:null,clearSelectedRows:!1,sortServer:!1,persistTableHead:!1};let i=c},60347:(e,a,t)=>{"use strict";t.r(a),t.d(a,{default:()=>l});var r=t(37876),o=t(11041),n=t(21772),s=t(32890),c=t(14232),i=t(66404),d=t(31753);let l=e=>{let{t:a}=(0,d.Bd)("common"),[t,l]=(0,c.useState)(!1);return(0,r.jsx)(r.Fragment,{children:(0,r.jsxs)(s.A.Item,{eventKey:"0",children:[(0,r.jsxs)(s.A.Header,{onClick:()=>l(!t),children:[(0,r.jsx)("div",{className:"cardTitle",children:a("documents")}),(0,r.jsx)("div",{className:"cardArrow",children:t?(0,r.jsx)(n.g,{icon:o.EZy,color:"#fff"}):(0,r.jsx)(n.g,{icon:o.QLR,color:"#fff"})})]}),(0,r.jsxs)(s.A.Body,{children:[(0,r.jsx)(i.A,{loading:e.documentAccoirdianProps.loading,sortProps:e.documentAccoirdianProps.hazardDocSort,docs:e.documentAccoirdianProps.Document||[],docsDescription:e.documentAccoirdianProps.docSrc}),(0,r.jsx)("h6",{className:"mt-3",children:a("DocumentsfromUpdates")}),(0,r.jsx)(i.A,{loading:e.documentAccoirdianProps.loading,sortProps:e.documentAccoirdianProps.hazardDocUpdateSort,docs:e.documentAccoirdianProps.updateDocument||[],docsDescription:e.documentAccoirdianProps.docSrc})]})]})})}},66404:(e,a,t)=>{"use strict";t.d(a,{A:()=>i});var r=t(37876);t(14232);var o=t(10841),n=t.n(o),s=t(50749),c=t(31753);let i=e=>{let{docs:a,docsDescription:t,sortProps:o,loading:i}=e,d=async(e,a)=>{o({columnSelector:e.selector,sortDirection:a})},{t:l}=(0,c.Bd)("common"),u=[{name:l("FileType"),width:"15%",selector:"extension",cell:e=>e&&e.extension&&e.extension},{name:l("FileName"),width:"25%",selector:"document_title",cell:e=>e&&e.original_name&&(0,r.jsx)("a",{href:"".concat("http://localhost:3001/api/v1","/files/download/").concat(e._id),target:"_blank",children:e.original_name.split(".").slice(0,-1).join(".")}),sortable:!0},{name:l("Description"),selector:"description",cell:e=>e&&e.description&&e.description},{name:l("UploadedDate"),width:"25%",selector:"doc_created_at",cell:e=>e&&e.updated_at&&n()(e.updated_at).format("MM/DD/YYYY"),sortable:!0}];return(0,r.jsx)(s.A,{columns:u,data:a,pagServer:!0,onSort:d,persistTableHead:!0,loading:i})}},75657:(e,a,t)=>{"use strict";t.r(a),t.d(a,{default:()=>l});var r=t(37876),o=t(11041),n=t(21772),s=t(32890),c=t(34381),i=t(31753),d=t(14232);let l=e=>{var a,t;let{t:l}=(0,i.Bd)("common"),[u,m]=(0,d.useState)(!1);return(0,r.jsx)(r.Fragment,{children:(0,r.jsxs)(s.A.Item,{eventKey:"0",children:[(0,r.jsxs)(s.A.Header,{onClick:()=>m(!u),children:[(0,r.jsx)("div",{className:"cardTitle",children:l("LinkedVirtualSpace")}),(0,r.jsx)("div",{className:"cardArrow",children:u?(0,r.jsx)(n.g,{icon:o.EZy,color:"#fff"}):(0,r.jsx)(n.g,{icon:o.QLR,color:"#fff"})})]}),(0,r.jsx)(s.A.Body,{children:(0,r.jsx)(c.A,{id:(null==(t=e.routeData)||null==(a=t.routes)?void 0:a[1])||"",type:"Project",vspaceData:[],vspaceDataLoading:!1,vspaceDataTotalRows:0,vspaceDataPerPage:10,vspaceDataCurrentPage:1})})]})})}},81434:(e,a,t)=>{"use strict";t.r(a),t.d(a,{default:()=>l});var r=t(37876),o=t(11041),n=t(21772),s=t(32890),c=t(14232),i=t(33458),d=t(31753);let l=e=>{let{t:a}=(0,d.Bd)("common"),[t,l]=(0,c.useState)(!1);return(0,r.jsx)(r.Fragment,{children:(0,r.jsxs)(s.A.Item,{eventKey:"0",children:[(0,r.jsxs)(s.A.Header,{onClick:()=>l(!t),children:[(0,r.jsx)("div",{className:"cardTitle",children:a("mediaGallery")}),(0,r.jsx)("div",{className:"cardArrow",children:t?(0,r.jsx)(n.g,{icon:o.EZy,color:"#fff"}):(0,r.jsx)(n.g,{icon:o.QLR,color:"#fff"})})]}),(0,r.jsx)(s.A.Body,{children:(0,r.jsx)(i.A,{gallery:e.images,imageSource:e.imgSrc})})]})})}},84135:function(e,a,t){(function(e){"use strict";function a(e,a,t,r){var o={m:["eine Minute","einer Minute"],h:["eine Stunde","einer Stunde"],d:["ein Tag","einem Tag"],dd:[e+" Tage",e+" Tagen"],w:["eine Woche","einer Woche"],M:["ein Monat","einem Monat"],MM:[e+" Monate",e+" Monaten"],y:["ein Jahr","einem Jahr"],yy:[e+" Jahre",e+" Jahren"]};return a?o[t][0]:o[t][1]}e.defineLocale("de",{months:"Januar_Februar_M\xe4rz_April_Mai_Juni_Juli_August_September_Oktober_November_Dezember".split("_"),monthsShort:"Jan._Feb._M\xe4rz_Apr._Mai_Juni_Juli_Aug._Sep._Okt._Nov._Dez.".split("_"),monthsParseExact:!0,weekdays:"Sonntag_Montag_Dienstag_Mittwoch_Donnerstag_Freitag_Samstag".split("_"),weekdaysShort:"So._Mo._Di._Mi._Do._Fr._Sa.".split("_"),weekdaysMin:"So_Mo_Di_Mi_Do_Fr_Sa".split("_"),weekdaysParseExact:!0,longDateFormat:{LT:"HH:mm",LTS:"HH:mm:ss",L:"DD.MM.YYYY",LL:"D. MMMM YYYY",LLL:"D. MMMM YYYY HH:mm",LLLL:"dddd, D. MMMM YYYY HH:mm"},calendar:{sameDay:"[heute um] LT [Uhr]",sameElse:"L",nextDay:"[morgen um] LT [Uhr]",nextWeek:"dddd [um] LT [Uhr]",lastDay:"[gestern um] LT [Uhr]",lastWeek:"[letzten] dddd [um] LT [Uhr]"},relativeTime:{future:"in %s",past:"vor %s",s:"ein paar Sekunden",ss:"%d Sekunden",m:a,mm:"%d Minuten",h:a,hh:"%d Stunden",d:a,dd:a,w:a,ww:"%d Wochen",M:a,MM:a,y:a,yy:a},dayOfMonthOrdinalParse:/\d{1,2}\./,ordinal:"%d.",week:{dow:1,doy:4}})})(t(10841))},86207:(e,a,t)=>{"use strict";t.r(a),t.d(a,{canViewDiscussionUpdate:()=>r,default:()=>o});let r=(0,t(8178).A)({authenticatedSelector:e=>!!e.permissions&&!!e.permissions.update&&!!e.permissions.update["read:any"],wrapperDisplayName:"CanViewDiscussionUpdate"}),o=r}}]);
//# sourceMappingURL=2317-a00563328d285365.js.map