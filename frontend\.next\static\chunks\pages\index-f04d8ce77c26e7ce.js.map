{"version": 3, "file": "static/chunks/pages/index-f04d8ce77c26e7ce.js", "mappings": "gFACA,4CACA,IACA,WACA,OAAe,EAAQ,KAA8B,CACrD,EACA,SAFsB,0HCsBtB,MAAeA,CAAAA,EAAAA,EAAAA,EAAAA,CAAeA,CAAC,UAAUC,GAXhC,UAACC,EAAAA,OAASA,CAAAA,CAACC,EAAGC,EAAMD,CAAC,CAAEE,KAAMD,EAAMC,IAAI,CAAG,GAAGD,CAAK", "sources": ["webpack://_N_E/", "webpack://_N_E/./pages/index.tsx"], "sourcesContent": ["\n    (window.__NEXT_P = window.__NEXT_P || []).push([\n      \"/\",\n      function () {\n        return require(\"private-next-pages/index.tsx\");\n      }\n    ]);\n    if(module.hot) {\n      module.hot.dispose(function () {\n        window.__NEXT_P.push([\"/\"])\n      });\n    }\n  ", "//Import Library\r\nimport React from 'react';\r\n\r\n//Import services/components\r\nimport Dashboard from './dashboard/Dashboard';\r\nimport { withTranslation } from 'next-i18next';\r\nimport { serverSideTranslations } from 'next-i18next/serverSideTranslations';\r\n\r\ninterface IndexProps {\r\n  // t?: (key: string) => string;\r\n  user?: any;\r\n  [key: string]: any;\r\n}\r\n\r\nconst Index = (props: IndexProps) => {\r\n  return <Dashboard t={props.t} user={props.user} {...props} />\r\n};\r\n\r\nexport async function getStaticProps({ locale }: { locale: string }) {\r\n  return {\r\n    props: {\r\n      ...(await serverSideTranslations(locale, ['common'])),\r\n    },\r\n  }\r\n}\r\n\r\nexport default withTranslation('common')(Index);\r\n"], "names": ["withTranslation", "Index", "Dashboard", "t", "props", "user"], "sourceRoot": "", "ignoreList": []}