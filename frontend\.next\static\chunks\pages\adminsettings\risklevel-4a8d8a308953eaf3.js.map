{"version": 3, "file": "static/chunks/pages/adminsettings/risklevel-4a8d8a308953eaf3.js", "mappings": "0qBAGA,IAAMA,EAAS,aACFC,EAAmBC,CAAAA,EAAAA,EAAAA,CAAAA,CAAoBA,CAAC,CACnDC,sBAAuB,KACjBC,EAAMC,WAAW,IAAID,EAAMC,WAAW,CAACC,YAAY,IAAIF,EAAMC,WAAW,CAACC,YAAY,CAACN,EAAO,CAKnGO,CALqG,kBAKjF,kBACtB,GAAG,EAE0BL,CAAAA,EAAAA,EAAAA,CAAAA,CAAoBA,CAAC,CAChDC,sBAAuB,KACjBC,EAAMC,WAAW,IAAID,EAAMC,WAAW,CAACG,OAAO,IAAIJ,EAAMC,WAAW,CAACG,OAAO,CAACR,EAAO,CAKzFO,CAL2F,kBAKvE,eACtB,GAAG,EAEmCL,CAAAA,EAAAA,EAAAA,CAAAA,CAAoBA,CAAC,CACzDC,sBAAuB,KACjBC,EAAMC,WAAW,IAAID,EAAMC,WAAW,CAACI,iBAAiB,IAAIL,EAAMC,WAAW,CAACI,iBAAiB,CAACT,EAAO,CAK7GO,CAL+G,kBAK3F,wBACtB,GAAG,EAE8BL,CAAAA,EAAAA,EAAAA,CAAAA,CAAoBA,CAAC,CACpDC,sBAAuB,GACjBC,IAAMC,WAAW,IAAID,EAAMC,WAAW,CAACK,YAAY,IAAIN,EAAMC,WAAW,CAACK,YAAY,CAACV,EAAO,CAKnGO,CALqG,kBAKjF,mBACtB,GAAG,EAE4BL,CAAAA,EAAAA,EAAAA,CAAAA,CAAoBA,CAAC,CAClDC,sBAAuB,KACjBC,EAAMC,WAAW,IAAID,EAAMC,WAAW,CAACM,SAAS,IAAIP,EAAMC,WAAW,CAACM,SAAS,CAACX,EAAO,CAK7FO,CAL+F,kBAK3E,iBACtB,GAAG,EAEqCL,CAAAA,EAAAA,EAAAA,CAAAA,CAAoBA,CAAC,CAC3DC,sBAAuB,KACjBC,EAAMC,WAAW,IAAID,EAAMC,WAAW,CAACO,uBAAuB,IAAIR,EAAMC,WAAW,CAACO,uBAAuB,CAACZ,EAAO,CAKzHO,CAL2H,kBAKvG,0BACtB,GAAG,EAEiCL,CAAAA,EAAAA,EAAAA,CAAAA,CAAoBA,CAAC,CACvDC,sBAAuB,KACjBC,EAAMC,WAAW,IAAID,EAAMC,WAAW,CAACO,uBAAuB,IAAIR,EAAMC,WAAW,CAACO,uBAAuB,CAACZ,EAAO,CAKzHO,CAL2H,kBAKvG,sBACtB,GAAG,EAC0BL,CAAAA,EAAAA,EAAAA,CAAAA,CAAoBA,CAAC,CAChDC,sBAAuB,KACjBC,EAAMC,WAAW,IAAID,EAAMC,WAAW,CAACQ,MAAM,IAAIT,EAAMC,WAAW,CAACQ,MAAM,CAACb,EAAO,CAKvFO,CALyF,kBAKrE,eACtB,GAAG,EAE8BL,CAAAA,EAAAA,EAAAA,CAAAA,CAAoBA,CAAC,CACpDC,sBAAwBC,KAClBA,EAAMC,WAAW,IAAID,EAAMC,WAAW,CAACS,WAAW,IAAIV,EAAMC,WAAW,CAACS,WAAW,CAACd,EAAO,CAKjGO,CALmG,kBAK/E,mBACtB,GAAG,EAEuCL,CAAAA,EAAAA,EAAAA,CAAAA,CAAoBA,CAAC,CAC7DC,sBAAuB,KACjBC,EAAMC,WAAW,IAAID,EAAMC,WAAW,CAACU,WAAW,IAAIX,EAAMC,WAAW,CAACU,WAAW,CAACf,EAAO,CAKjGO,CALmG,kBAK/E,4BACtB,GAAG,EAEuCL,CAAAA,EAAAA,EAAAA,CAAAA,CAAoBA,CAAC,CAC7DC,sBAAwBC,KAClBA,EAAMC,WAAW,IAAID,EAAMC,WAAW,CAACW,mBAAmB,IAAIZ,EAAMC,WAAW,CAACW,mBAAmB,CAAChB,EAAO,CAKjHO,CALmH,kBAK/F,4BACtB,GAAG,EAEoCL,CAAAA,EAAAA,EAAAA,CAAAA,CAAoBA,CAAC,CAC1DC,sBAAuB,KACjBC,EAAMC,WAAW,IAAID,EAAMC,WAAW,CAACY,gBAAgB,IAAIb,EAAMC,WAAW,CAACY,gBAAgB,CAACjB,EAAO,CAK3GO,CAL6G,kBAKzF,yBACtB,GAAG,EAEkCL,CAAAA,EAAAA,EAAAA,CAAAA,CAAoBA,CAAC,CACxDC,sBAAuB,KACjBC,EAAMC,WAAW,IAAID,EAAMC,WAAW,CAACa,gBAAgB,IAAId,EAAMC,WAAW,CAACa,gBAAgB,CAAClB,EAAO,CAK3GO,CAL6G,kBAKzF,uBACtB,GAAG,EAEgCL,CAAAA,EAAAA,EAAAA,CAAAA,CAAoBA,CAAC,CACtDC,sBAAuB,KACjBC,EAAMC,WAAW,IAAID,EAAMC,WAAW,CAACc,cAAc,IAAIf,EAAMC,WAAW,CAACc,cAAc,CAACnB,EAAO,CAKvGO,CALyG,kBAKrF,qBACtB,GAAG,EAE0BL,CAAAA,EAAAA,EAAAA,CAAAA,CAAoBA,CAAC,CAChDC,sBAAuB,KACjBC,EAAMC,WAAW,IAAID,EAAMC,WAAW,CAACe,MAAM,IAAIhB,EAAMC,WAAW,CAACe,MAAM,CAACpB,EAAO,CAKvFO,CALyF,kBAKrE,eACtB,GAAG,EAE6BL,CAAAA,EAAAA,EAAAA,CAAAA,CAAoBA,CAAC,CACnDC,sBAAuB,KACjBC,EAAMC,WAAW,IAAID,EAAMC,WAAW,CAACgB,UAAU,IAAIjB,EAAMC,WAAW,CAACgB,UAAU,CAACrB,EAAO,CAK/FO,CALiG,kBAK7E,kBACtB,GAAG,EAE4BL,CAAAA,EAAAA,EAAAA,CAAAA,CAAoBA,CAAC,CAClDC,sBAAuB,KACjBC,EAAMC,WAAW,IAAID,EAAMC,WAAW,CAACiB,QAAQ,IAAIlB,EAAMC,WAAW,CAACiB,QAAQ,CAACtB,EAAO,CAK3FO,CAL6F,kBAKzE,iBACtB,GAAG,EAE8BL,CAAAA,EAAAA,EAAAA,CAAAA,CAAoBA,CAAC,CACpDC,sBAAuB,KACjBC,EAAMC,WAAW,IAAID,EAAMC,WAAW,CAACkB,WAAW,IAAInB,EAAMC,WAAW,CAACkB,WAAW,CAACvB,EAAO,CAKjGO,CALmG,kBAK/E,mBACtB,GAEaiB,EAActB,CAAAA,EAAAA,EAAAA,CAAAA,CAAoBA,CAAC,CAC9CC,sBAAuB,KACjBC,EAAMC,WAAW,IAAID,EAAMC,WAAW,CAACoB,KAAK,IAAIrB,EAAMC,WAAW,CAACoB,KAAK,CAACzB,EAAO,CAKrFO,CALuF,kBAKnE,aACtB,GAAG,EAE8BL,CAAAA,EAAAA,EAAAA,CAAAA,CAAoBA,CAAC,CACpDC,sBAAuB,KACjBC,EAAMC,WAAW,IAAID,EAAMC,WAAW,CAACqB,WAAW,IAAItB,EAAMC,WAAW,CAACqB,WAAW,CAAC1B,EAAO,CAKjGO,CALmG,kBAK/E,mBACtB,GAAG,EAE8BL,CAAAA,EAAAA,EAAAA,CAAAA,CAAoBA,CAAC,CACpDC,sBAAuB,KACjBC,EAAMC,WAAW,IAAID,EAAMC,WAAW,CAACsB,YAAY,IAAIvB,EAAMC,WAAW,CAACsB,YAAY,CAAC3B,EAAO,CAKnGO,CALqG,kBAKjF,mBACtB,GAAG,EAE0BL,CAAAA,EAAAA,EAAAA,CAAAA,CAAoBA,CAAC,CAChDC,sBAAuB,GACrB,EAAIC,EAAMC,WAAW,IAAID,EAAMC,WAAW,CAACuB,SAAS,IAAIxB,EAAMC,WAAW,CAACuB,SAAS,CAAC5B,EAAO,IAAII,EAAMC,WAAW,CAACwB,OAAO,IAAIzB,EAAMC,WAAW,CAACwB,OAAO,CAAC7B,EAAO,IAAGI,EAAMC,WAAW,CAACyB,KAAK,IAAI1B,EAAMC,WAAW,CAACyB,KAAK,CAAC9B,EAAO,IAAGI,EAAMC,WAAW,CAAC0B,MAAM,IAAI3B,EAAMC,WAAW,CAAC0B,MAAM,CAAC/B,EAAO,IAAGI,EAAMC,WAAW,CAACU,WAAW,IAAIX,EAAMC,WAAW,CAACU,WAAW,CAACf,EAAO,IAAGI,EAAMC,WAAW,CAAC2B,MAAM,IAAI5B,EAAMC,WAAW,CAAC2B,MAAM,CAAChC,EAAO,EAAE,CAG5Z,EAETO,mBAAoB,eACtB,GAAG,EAEYN,gBAAgBA,EAAC,gNC9JhC,MAnDuB,QAmCfG,EAAAA,EAlCN,GAAM,GAAE6B,CAAC,CAAE,CAAGC,CAAAA,EAAAA,EAAAA,EAAAA,CAAcA,CAAC,UACvBC,EAAqB,EAiDC,EA/CxB,UAACC,MAAAA,UACC,WAACC,EAAAA,CAASA,CAAAA,CAACC,MAAO,CAAEC,UAAW,QAAS,EAAGC,KAAK,IAACC,UAAU,gBACzD,UAACC,EAAAA,CAAGA,CAAAA,UACF,UAACC,EAAAA,CAAGA,CAAAA,CAACC,GAAI,YACP,UAACC,EAAAA,CAAWA,CAAAA,CAACC,MAAOb,EAAE,0CAG1B,UAACS,EAAAA,CAAGA,CAAAA,UACF,UAACC,EAAAA,CAAGA,CAAAA,CAACC,GAAI,YACP,UAACG,IAAIA,CACHC,KAAK,6BACLC,GAAG,OAFAF,oCAIH,UAACG,EAAAA,CAAMA,CAAAA,CAACC,QAAQ,YAAYC,KAAK,cAChCnB,EAAE,+CAKT,UAACS,EAAAA,CAAGA,CAAAA,CAACD,UAAU,gBACb,UAACE,EAAAA,CAAGA,CAAAA,CAACC,GAAI,YACP,UAAES,EAAAA,OAAcA,CAAAA,CAAAA,YAQtBC,EAAoBC,CAAAA,EAAAA,EAAAA,gBAAAA,CAAgBA,CAAC,IAAM,UAACpB,EAAAA,CAAAA,IAC5C/B,EAAYoD,CAAAA,EAAAA,EAAAA,EAAAA,CAAWA,CAAEpD,GAAUA,SACzC,IAAI,GAAEA,GAAAA,OAAAA,EAAAA,EAAOC,IAAPD,OAAOC,GAAPD,OAAAA,EAAAA,EAAAA,UAAoBiB,EAApBjB,KAAAA,EAAAA,CAAgC,CAAC,GAAjCA,UAA8C,EAIlD,CAJqD,EAIrD,OAACkD,EAAAA,CAAAA,GAHM,UAACG,EAAAA,OAAeA,CAAAA,CAAAA,EAK3B,6GClBA,SAASC,EAASC,CAAoB,EACpC,GAAM,GAAE1B,CAAC,CAAE,CAAGC,CAAAA,EAAAA,EAAAA,EAAAA,CAAcA,CAAC,UACvB0B,EAA6B,CACjCC,gBAAiB5B,EAAE,cACnB,EACI,SACJ6B,CAAO,MACPC,CAAI,CACJC,WAAS,CACTC,uBAAqB,CACrBC,WAAS,oBACTC,CAAkB,qBAClBC,CAAmB,kBACnBC,CAAgB,aAChBC,CAAW,oBACXC,CAAkB,gBAClBC,CAAc,SACdC,CAAO,WACPC,CAAS,sBACTC,CAAoB,mBACpBC,CAAiB,YACjBC,CAAU,QACVC,CAAM,kBACNC,CAAgB,cAChBC,CAAY,CAEZ,CADA,EACGC,EACJ,CAAGtB,EAGEuB,EAAiB,CACrBtB,6BACAuB,gBAAiBlD,EAAE,IAP0C,MAQ7DmD,UAAU,UACVtB,EACAC,KAAMA,GAAQ,EAAE,CAChBsB,OAAO,EACPC,2BAA4BrB,EAC5BsB,UAAWrB,EACXsB,gBAAiBf,qBACjBN,EACAsB,YAAY,EACZC,iBAAkBhB,EAClBiB,kBAAmBpB,GAA0C,GAC7DqB,eADwCrB,cACVD,GAA4B,CAAC,GAAI,GAAI,GAAI,CAA3BA,EAA+B,GAAG,CAC9EuB,oBAAqB7B,EACrB8B,oBAAqB1B,EACrB2B,aAAc1B,iBACdG,uBACAG,oBACAC,EACAoB,kBAAmB,UAACC,EAAAA,CAAYA,CAAAA,CAAAA,GAChCC,SAAU,UAACC,IAAAA,CAAE1D,UAAU,6CACvBoC,EACAC,sBACAE,mBACAD,EACAtC,UAAW,WACb,EACA,MACE,UAAC2D,EAAAA,EAASA,CAAAA,CAAE,GAAGlB,CAAc,EAEjC,CAEAxB,EAAS2C,YAAY,CAAG,CACtBd,UAAW,GACXE,WAAY,GACZzB,UAAW,KACXU,WAAW,EACXC,qBAAsB,KACtBC,mBAAmB,EACnBC,YAAY,EACZE,kBAAkB,CACpB,EAEA,MAAerB,QAAQA,EAAC,mEChHT,SAASD,EAAgB6C,CAAW,EAC/C,MACE,UAAClE,MAAAA,CAAIK,UAAU,sDACb,UAACL,MAAAA,CAAIK,UAAU,mBAAU,yCAG/B,gECFa,SAASI,EAAYc,CAAuB,EACzD,MACE,UAAC4C,KAAAA,CAAG9D,UAAU,wBAAgBkB,EAAMb,KAAK,EAE7C,8KCkIA,MA9HuB,IACnB,GAAM,CAAC0D,EAAWC,EAAe,CAAGC,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CA6HjCrD,EA7HoC,EACzC,EAAGsD,EAAW,CAAGD,CAAAA,EAAAA,EAAAA,EA4HG,MA5HHA,CAAQA,EAAC,GAC1B,CAAC1C,EAAW4C,EAAa,CAAGF,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAC,GACrC,CAACG,EAASC,EAAW,CAAGJ,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAC,IACjC,CAACK,EAAaC,EAAS,CAAGN,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAC,IACnC,CAACO,EAAiBC,EAAmB,CAAGR,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAC,CAAC,GAClD,GAAEzE,CAAC,CAAE,CAAGC,CAAAA,EAAAA,EAAAA,EAAAA,CAAcA,CAAC,UAEvBiF,EAAkB,CACpBC,KAAM,CAAEtE,MAAO,KAAM,EACrBuE,MAAOR,EACPS,KAAM,EACNC,MAAO,CAAC,CACZ,EAEMzD,EAAU,CACZ,CACI0D,KAAMvF,EAAE,SACRwF,SAAWC,GAAaA,EAAI5E,KAAK,CACjC6E,UAAU,CACd,EACA,CACIH,KAAMvF,EAAE,gCACRwF,SAAU,GAAcC,EAAIE,KAAK,CACjCD,UAAU,EACVE,KAAM,GAAYC,EAAEF,KAAK,EAE7B,CACIJ,KAAMvF,EAAE,UACRwF,SAAU,GAAcC,EAAIK,GAAG,CAC/BJ,SAAU,GACVE,KAAM,GACF,WAACzF,MAAAA,WACG,UAACW,IAAIA,CAACC,KAAK,6BAA6BC,GAAI,OAAvCF,0BAA8E,OAAN+E,EAAEC,GAAG,WAE9E,UAAC5B,IAAAA,CAAE1D,UAAU,uBAEV,OAEP,UAACuF,IAAAA,CAAEC,QAAS,IAAMC,EAAWJ,YACzB,UAAC3B,IAAAA,CAAE1D,UAAU,4BACZ,MAGjB,EACH,CAEK0F,EAAmB,UACrBxB,GAAW,GACX,IAAMyB,EAAW,MAAMC,EAAAA,CAAUA,CAACC,GAAG,CAAC,aAAcnB,GAChDiB,GAAYA,EAASrE,IAAI,EAAIqE,EAASrE,IAAI,CAACwE,MAAM,CAAG,GAAG,CACvD9B,EAAe2B,EAASrE,IAAI,EAC5B6C,EAAawB,EAASI,UAAU,EAChC7B,GAAW,GAEnB,EAOMvC,EAAsB,MAAOqE,EAAiBnB,KAChDH,EAAgBE,KAAK,CAAGoB,EACxBtB,EAAgBG,IAAI,CAAGA,EACvBX,GAAW,GACX,IAAMyB,EAAW,MAAMC,EAAAA,CAAUA,CAACC,GAAG,CAAC,aAAcnB,GAChDiB,GAAYA,EAASrE,IAAI,EAAIqE,EAASrE,IAAI,CAACwE,MAAM,CAAG,GAAG,CACvD9B,EAAe2B,EAASrE,IAAI,EAC5B+C,EAAW2B,GACX9B,GAAW,GAEnB,EAEMuB,EAAa,MAAOR,IACtBR,EAAmBQ,EAAIK,GAAG,EAC1Bf,GAAS,EACb,EAEM0B,EAAe,UACjB,GAAI,CACA,MAAML,EAAAA,CAAUA,CAACM,MAAM,CAAC,cAA8B,OAAhB1B,IACtCkB,IACAnB,EAAS,IACT4B,EAAAA,EAAKA,CAACC,OAAO,CAAC5G,EAAE,6DACpB,CAAE,MAAO6G,EAAO,CACZF,EAAAA,EAAKA,CAACE,KAAK,CAAC7G,EAAE,uDAClB,CACJ,EAEM8G,EAAY,IAAM/B,GAAS,GAMjC,MAJAgC,CAAAA,EAAAA,EAAAA,SAAAA,CAASA,CAAC,KACNb,GACJ,EAAG,EAAE,EAGD,WAAC/F,MAAAA,WACG,WAAC6G,EAAAA,CAAKA,CAAAA,CAACC,KAAMnC,EAAaoC,OAAQJ,YAC9B,UAACE,EAAAA,CAAKA,CAACG,MAAM,EAACC,WAAW,aACrB,UAACJ,EAAAA,CAAKA,CAACK,KAAK,WAAErH,EAAE,8CAEpB,WAACgH,EAAAA,CAAKA,CAACM,IAAI,YAAEtH,EAAE,8DAA8D,OAC7E,WAACgH,EAAAA,CAAKA,CAACO,MAAM,YACT,UAACtG,EAAAA,CAAMA,CAAAA,CAACC,QAAQ,YAAY8E,QAASc,WAChC9G,EAAE,YAEP,UAACiB,EAAAA,CAAMA,CAAAA,CAACC,QAAQ,UAAU8E,QAASS,WAC9BzG,EAAE,eAKf,UAACyB,EAAAA,CAAQA,CAAAA,CACLI,QAASA,EACTC,KAAMyC,EACNxC,UAAWA,EACXU,UAAW,GACXN,oBAAqBA,EACrBC,iBA/Da,CA+DKA,GA9D1B8C,EAAgBE,KAAK,CAAGR,EACxBM,EAAgBG,IAAI,CAAGA,EACvBa,GACJ,MA+DJ,mBCvIA,4CACA,2BACA,WACA,OAAe,EAAQ,KAAsD,CAC7E,EACA,SAFsB", "sources": ["webpack://_N_E/./pages/adminsettings/permissions.tsx", "webpack://_N_E/./pages/adminsettings/risklevel/index.tsx", "webpack://_N_E/./components/common/RKITable.tsx", "webpack://_N_E/./pages/rNoAccess.tsx", "webpack://_N_E/./components/common/PageHeading.tsx", "webpack://_N_E/./pages/adminsettings/risklevel/risklevelTable.tsx", "webpack://_N_E/?8835"], "sourcesContent": ["//Import services/components\r\nimport connectedAuthWrapper from 'redux-auth-wrapper/connectedAuthWrapper';\r\n\r\nconst create = \"create:any\";\r\nexport const canAddAreaOfWork = connectedAuthWrapper({\r\n  authenticatedSelector: (state) => {\r\n    if (state.permissions && state.permissions.area_of_work && state.permissions.area_of_work[create]) {\r\n      return true;\r\n    }\r\n    return false;\r\n  },\r\n  wrapperDisplayName: 'CanAddAreaOfWork',\r\n});\r\n\r\nexport const canAddCountry = connectedAuthWrapper({\r\n  authenticatedSelector: (state) => {\r\n    if (state.permissions && state.permissions.country && state.permissions.country[create]) {\r\n      return true;\r\n    }\r\n    return false;\r\n  },\r\n  wrapperDisplayName: 'CanAddCountry',\r\n});\r\n\r\nexport const canAddDeploymentStatus = connectedAuthWrapper({\r\n  authenticatedSelector: (state) => {\r\n    if (state.permissions && state.permissions.deployment_status && state.permissions.deployment_status[create]) {\r\n      return true;\r\n    }\r\n    return false;\r\n  },\r\n  wrapperDisplayName: 'CanAddDeploymentStatus',\r\n});\r\n\r\nexport const canAddEventStatus = connectedAuthWrapper({\r\n  authenticatedSelector: (state) => {\r\n    if (state.permissions && state.permissions.event_status && state.permissions.event_status[create]) {\r\n      return true;\r\n    }\r\n    return false;\r\n  },\r\n  wrapperDisplayName: 'CanAddEventStatus',\r\n});\r\n\r\nexport const canAddExpertise = connectedAuthWrapper({\r\n  authenticatedSelector: (state) => {\r\n    if (state.permissions && state.permissions.expertise && state.permissions.expertise[create]) {\r\n      return true;\r\n    }\r\n    return false;\r\n  },\r\n  wrapperDisplayName: 'CanAddExpertise',\r\n});\r\n\r\nexport const canAddFocalPointApproval = connectedAuthWrapper({\r\n  authenticatedSelector: (state) => {\r\n    if (state.permissions && state.permissions.institution_focal_point && state.permissions.institution_focal_point[create]) {\r\n      return true;\r\n    }\r\n    return false;\r\n  },\r\n  wrapperDisplayName: 'CanAddFocalPointApproval',\r\n});\r\n\r\nexport const canAddVspaceApproval = connectedAuthWrapper({\r\n  authenticatedSelector: (state) => {\r\n    if (state.permissions && state.permissions.institution_focal_point && state.permissions.institution_focal_point[create]) {\r\n      return true;\r\n    }\r\n    return false;\r\n  },\r\n  wrapperDisplayName: 'CanAddVspaceApproval',\r\n});\r\nexport const canAddHazards = connectedAuthWrapper({\r\n  authenticatedSelector: (state) => {\r\n    if (state.permissions && state.permissions.hazard && state.permissions.hazard[create]) {\r\n      return true;\r\n    }\r\n    return false;\r\n  },\r\n  wrapperDisplayName: 'CanAddHazards',\r\n});\r\n\r\nexport const canAddHazardTypes = connectedAuthWrapper({\r\n  authenticatedSelector: (state) => {\r\n    if (state.permissions && state.permissions.hazard_type && state.permissions.hazard_type[create]) {\r\n      return true;\r\n    }\r\n    return false;\r\n  },\r\n  wrapperDisplayName: 'CanAddHazardTypes',\r\n}); \r\n\r\nexport const canAddOrganisationApproval = connectedAuthWrapper({\r\n  authenticatedSelector: (state) => {\r\n    if (state.permissions && state.permissions.institution && state.permissions.institution[create]) {\r\n      return true;\r\n    }\r\n    return false;\r\n  },\r\n  wrapperDisplayName: 'CanAddOrganisationApproval',\r\n}); \r\n\r\nexport const canAddOrganisationNetworks = connectedAuthWrapper({\r\n  authenticatedSelector: (state) => {\r\n    if (state.permissions && state.permissions.institution_network && state.permissions.institution_network[create]) {\r\n      return true;\r\n    }\r\n    return false;\r\n  },\r\n  wrapperDisplayName: 'CanAddOrganisationNetworks',\r\n});\r\n\r\nexport const canAddOrganisationTypes = connectedAuthWrapper({\r\n  authenticatedSelector: (state) => {\r\n    if (state.permissions && state.permissions.institution_type && state.permissions.institution_type[create]) {\r\n      return true;\r\n    }\r\n    return false;\r\n  },\r\n  wrapperDisplayName: 'CanAddOrganisationTypes',\r\n});\r\n\r\nexport const canAddOperationStatus = connectedAuthWrapper({\r\n  authenticatedSelector: (state) => {\r\n    if (state.permissions && state.permissions.operation_status && state.permissions.operation_status[create]) {\r\n      return true;\r\n    }\r\n    return false;\r\n  },\r\n  wrapperDisplayName: 'CanAddOperationStatus',\r\n});\r\n\r\nexport const canAddProjectStatus = connectedAuthWrapper({\r\n  authenticatedSelector: (state) => {\r\n    if (state.permissions && state.permissions.project_status && state.permissions.project_status[create]) {\r\n      return true;\r\n    }\r\n    return false;\r\n  },\r\n  wrapperDisplayName: 'CanAddProjectStatus',\r\n});\r\n\r\nexport const canAddRegions = connectedAuthWrapper({\r\n  authenticatedSelector: (state) => {\r\n    if (state.permissions && state.permissions.region && state.permissions.region[create]) {\r\n      return true;\r\n    }\r\n    return false;\r\n  },\r\n  wrapperDisplayName: 'CanAddRegions',\r\n});\r\n\r\nexport const canAddRiskLevels = connectedAuthWrapper({\r\n  authenticatedSelector: (state) => {\r\n    if (state.permissions && state.permissions.risk_level && state.permissions.risk_level[create]) {\r\n      return true;\r\n    }\r\n    return false;\r\n  },\r\n  wrapperDisplayName: 'CanAddRiskLevels',\r\n});\r\n\r\nexport const canAddSyndromes = connectedAuthWrapper({\r\n  authenticatedSelector: (state) => {\r\n    if (state.permissions && state.permissions.syndrome && state.permissions.syndrome[create]) {\r\n      return true;\r\n    }\r\n    return false;\r\n  },\r\n  wrapperDisplayName: 'CanAddSyndromes',\r\n});\r\n\r\nexport const canAddUpdateTypes = connectedAuthWrapper({\r\n  authenticatedSelector: (state) => {\r\n    if (state.permissions && state.permissions.update_type && state.permissions.update_type[create]) {\r\n      return true;\r\n    }\r\n    return false;\r\n  },\r\n  wrapperDisplayName: 'CanAddUpdateTypes',\r\n});\r\n\r\nexport const canAddUsers = connectedAuthWrapper({\r\n  authenticatedSelector: (state) => {\r\n    if (state.permissions && state.permissions.users && state.permissions.users[create]) {\r\n      return true;\r\n    }\r\n    return false;\r\n  },\r\n  wrapperDisplayName: 'CanAddUsers',\r\n});\r\n\r\nexport const canAddWorldRegion = connectedAuthWrapper({\r\n  authenticatedSelector: (state) => {\r\n    if (state.permissions && state.permissions.worl_region && state.permissions.worl_region[create]) {\r\n      return true;\r\n    }\r\n    return false;\r\n  },\r\n  wrapperDisplayName: 'CanAddWorldRegion',\r\n});\r\n\r\nexport const canAddLandingPage = connectedAuthWrapper({\r\n  authenticatedSelector: (state) => {\r\n    if (state.permissions && state.permissions.landing_page && state.permissions.landing_page[create]) {\r\n      return true;\r\n    }\r\n    return false;\r\n  },\r\n  wrapperDisplayName: 'CanAddLandingPage',\r\n});\r\n\r\nexport const canAddContent = connectedAuthWrapper({\r\n  authenticatedSelector: (state) => {\r\n    if (state.permissions && state.permissions.operation && state.permissions.operation[create] && state.permissions.project && state.permissions.project[create]&& state.permissions.event && state.permissions.event[create]&& state.permissions.vspace && state.permissions.vspace[create]&& state.permissions.institution && state.permissions.institution[create]&& state.permissions.update && state.permissions.update[create]) {\r\n      return true;\r\n    }\r\n    return false;\r\n  },\r\n  wrapperDisplayName: 'CanAddContent',\r\n});\r\n\r\nexport default canAddAreaOfWork;", "//Import Library\r\nimport { Contain<PERSON>, <PERSON>, <PERSON>, Button } from \"react-bootstrap\";\r\nimport Link from \"next/link\";\r\n\r\n//Import services/components\r\nimport RisklevelTable from \"./risklevelTable\";\r\nimport { useTranslation } from 'next-i18next';\r\nimport { serverSideTranslations } from 'next-i18next/serverSideTranslations';\r\nimport PageHeading from \"../../../components/common/PageHeading\";\r\nimport { canAddRiskLevels } from \"../permissions\";\r\nimport { useSelector } from \"react-redux\";\r\nimport NoAccessMessage from \"../../rNoAccess\";\r\n\r\n\r\nconst RisklevelIndex = (_props: any) => {\r\n  const { t } = useTranslation('common');\r\n  const ShowRisklevelIndex = () => {\r\n    return (\r\n      <div>\r\n        <Container style={{ overflowX: \"hidden\" }} fluid className=\"p-0\">\r\n          <Row>\r\n            <Col xs={12}>\r\n              <PageHeading title={t(\"adminsetting.RiskLevel.Risklevel\")} />\r\n            </Col>\r\n          </Row>\r\n          <Row>\r\n            <Col xs={12}>\r\n              <Link\r\n                href=\"/adminsettings/[...routes]\"\r\n                as=\"/adminsettings/create_risklevel\"\r\n                >\r\n                <Button variant=\"secondary\" size=\"sm\">\r\n                {t(\"adminsetting.RiskLevel.AddRisklevel\")}\r\n              </Button>\r\n              </Link>\r\n            </Col>\r\n          </Row>\r\n          <Row className=\"mt-3\">\r\n            <Col xs={12}>\r\n              < RisklevelTable />\r\n            </Col>\r\n          </Row>\r\n        </Container>\r\n      </div>\r\n    );\r\n  }\r\n\r\n  const ShowAddRiskLevels = canAddRiskLevels(() => <ShowRisklevelIndex />);\r\n  const state:any = useSelector((state) => state);\r\n  if (!(state?.permissions?.risk_level?.['create:any'])) {\r\n    return <NoAccessMessage />\r\n  }\r\n  return(\r\n    <ShowAddRiskLevels />\r\n  )\r\n}\r\n\r\nexport async function getServerSideProps({ locale } : { locale: string }) {\r\n  return {\r\n    props: {\r\n      ...(await serverSideTranslations(locale, ['common'])),\r\n    },\r\n  }\r\n}\r\n\r\nexport default RisklevelIndex;", "//Import Library\r\nimport React from 'react'\r\nimport DataTable  from 'react-data-table-component';\r\n\r\n//Import services/components\r\nimport { useTranslation } from 'next-i18next';\r\nimport CustomLoader from './CustomLoader';\r\n\r\ninterface RKITableProps {\r\n  columns: any[];\r\n  data: any[];\r\n  totalRows?: number;\r\n  resetPaginationToggle?: boolean;\r\n  subheader?: boolean;\r\n  subHeaderComponent?: React.ReactNode;\r\n  handlePerRowsChange?: (newPerPage: number, page: number) => void;\r\n  handlePageChange?: (page: number) => void;\r\n  rowsPerPage?: number[];\r\n  defaultRowsPerPage?: number;\r\n  selectableRows?: boolean;\r\n  loading?: boolean;\r\n  pagServer?: boolean;\r\n  onSelectedRowsChange?: (selectedRows: any) => void;\r\n  clearSelectedRows?: boolean;\r\n  sortServer?: boolean;\r\n  onSort?: (column: any, sortDirection: string) => void;\r\n  persistTableHead?: boolean;\r\n  sortFunction?: (rows: any[], field: any, direction: 'asc' | 'desc') => any[];\r\n  noHeader?: boolean;\r\n  dense?: boolean;\r\n  pagination?: boolean;\r\n  paginationServer?: boolean;\r\n  paginationTotalRows?: number;\r\n  subHeaderAlign?: string;\r\n  [key: string]: any;\r\n}\r\n\r\nfunction RKITable(props: RKITableProps) {\r\n  const { t } = useTranslation('common');\r\n  const paginationComponentOptions = {\r\n    rowsPerPageText: t('Rowsperpage'),\r\n    };\r\n  const {\r\n    columns,\r\n    data,\r\n    totalRows,\r\n    resetPaginationToggle,\r\n    subheader,\r\n    subHeaderComponent,\r\n    handlePerRowsChange,\r\n    handlePageChange,\r\n    rowsPerPage,\r\n    defaultRowsPerPage,\r\n    selectableRows,\r\n    loading,\r\n    pagServer,\r\n    onSelectedRowsChange,\r\n    clearSelectedRows,\r\n    sortServer,\r\n    onSort,\r\n    persistTableHead,\r\n    sortFunction,\r\n    // Filter out any unknown props that might cause DOM warnings\r\n    ...otherProps\r\n  } = props;\r\n\r\n  // Only pass known DataTable props to avoid DOM warnings\r\n  const dataTableProps = {\r\n    paginationComponentOptions,\r\n    noDataComponent: t(\"NoData\"),\r\n    noHeader: true,\r\n    columns,\r\n    data: data || [],\r\n    dense: true,\r\n    paginationResetDefaultPage: resetPaginationToggle,\r\n    subHeader: subheader,\r\n    progressPending: loading,\r\n    subHeaderComponent,\r\n    pagination: true,\r\n    paginationServer: pagServer,\r\n    paginationPerPage: defaultRowsPerPage ? defaultRowsPerPage : 10,\r\n    paginationRowsPerPageOptions: rowsPerPage ? rowsPerPage : [10, 15, 20, 25, 30],\r\n    paginationTotalRows: totalRows,\r\n    onChangeRowsPerPage: handlePerRowsChange,\r\n    onChangePage: handlePageChange,\r\n    selectableRows,\r\n    onSelectedRowsChange,\r\n    clearSelectedRows,\r\n    progressComponent: <CustomLoader/>,\r\n    sortIcon: <i className=\"sort-icon fas fa-exchange-alt\" />,\r\n    sortServer,\r\n    onSort,\r\n    sortFunction,\r\n    persistTableHead,\r\n    className: \"rki-table\"\r\n  };\r\n  return (\r\n    <DataTable {...dataTableProps} />\r\n  )\r\n};\r\n\r\nRKITable.defaultProps = {\r\n  subHeader: false,\r\n  pagination: true,\r\n  totalRows: null,\r\n  pagServer: true,\r\n  onSelectedRowsChange: null,\r\n  clearSelectedRows: false,\r\n  sortServer: false,\r\n  persistTableHead: false\r\n}\r\n\r\nexport default RKITable;\r\n\r\n", "export default function NoAccessMessage(_props: any) {\r\n    return (\r\n      <div className=\"container-fluid p-0 response-message-block\">\r\n        <div className=\"message\">you don't have permission to access</div>\r\n      </div>\r\n    )\r\n  }", "interface PageHeadingProps {\r\n  title: string; // Required based on actual usage\r\n}\r\n\r\nexport default function PageHeading(props: PageHeadingProps) {\r\n  return (\r\n    <h2 className=\"page-heading\">{props.title}</h2>\r\n  )\r\n}\r\n", "//Import Library\r\nimport { useState, useEffect } from \"react\";\r\nimport Link from \"next/link\";\r\nimport { <PERSON><PERSON>, Button } from \"react-bootstrap\";\r\n\r\nimport toast from 'react-hot-toast';\r\n\r\n//Import services/components\r\nimport RKITable from \"../../../components/common/RKITable\";\r\nimport apiService from \"../../../services/apiService\";\r\nimport { useTranslation } from 'next-i18next';\r\n\r\nconst RisklevelTable = (_props: any) => {\r\n    const [tabledata, setDataToTable] = useState([]);\r\n    const [, setLoading] = useState(false);\r\n    const [totalRows, setTotalRows] = useState(0);\r\n    const [perPage, setPerPage] = useState(10);\r\n    const [isModalShow, setModal] = useState(false);\r\n    const [selectRisklevel, setSelectRisklevel] = useState({});\r\n    const { t } = useTranslation('common');\r\n\r\n    const risklevelParams = {\r\n        sort: { title: \"asc\" },\r\n        limit: perPage,\r\n        page: 1,\r\n        query: {},\r\n    };\r\n\r\n    const columns = [\r\n        {\r\n            name: t(\"Title\"),\r\n            selector: (row: any) => row.title,\r\n            sortable: true,\r\n        },\r\n        {\r\n            name: t(\"adminsetting.RiskLevel.Level\"),\r\n            selector: (row: any) => row.level,\r\n            sortable: true,\r\n            cell: (d: any) => d.level,\r\n        },\r\n        {\r\n            name: t(\"action\"),\r\n            selector: (row: any) => row._id,\r\n            sortable: false,\r\n            cell: (d: any) => (\r\n                <div>\r\n                    <Link href=\"/adminsettings/[...routes]\" as={`/adminsettings/edit_risklevel/${d._id}`}>\r\n\r\n                        <i className=\"icon fas fa-edit\" />\r\n\r\n                    </Link>\r\n                    &nbsp;\r\n                    <a onClick={() => userAction(d)}>\r\n                        <i className=\"icon fas fa-trash-alt\" />\r\n                    </a>{\" \"}\r\n                </div>\r\n            ),\r\n        },\r\n    ];\r\n\r\n    const getRisklevelData = async () => {\r\n        setLoading(true);\r\n        const response = await apiService.get(\"/risklevel\", risklevelParams);\r\n        if (response && response.data && response.data.length > 0) {\r\n            setDataToTable(response.data);\r\n            setTotalRows(response.totalCount);\r\n            setLoading(false);\r\n        }\r\n    };\r\n    const handlePageChange = (page: any) => {\r\n        risklevelParams.limit = perPage;\r\n        risklevelParams.page = page;\r\n        getRisklevelData();\r\n    };\r\n\r\n    const handlePerRowsChange = async (newPerPage: any, page: any) => {\r\n        risklevelParams.limit = newPerPage;\r\n        risklevelParams.page = page;\r\n        setLoading(true);\r\n        const response = await apiService.get(\"/risklevel\", risklevelParams);\r\n        if (response && response.data && response.data.length > 0) {\r\n            setDataToTable(response.data);\r\n            setPerPage(newPerPage);\r\n            setLoading(false);\r\n        }\r\n    };\r\n\r\n    const userAction = async (row: any) => {\r\n        setSelectRisklevel(row._id);\r\n        setModal(true);\r\n    };\r\n\r\n    const modalConfirm = async () => {\r\n        try {\r\n            await apiService.remove(`/risklevel/${selectRisklevel}`);\r\n            getRisklevelData();\r\n            setModal(false);\r\n            toast.success(t(\"adminsetting.RiskLevel.Table.riskLevelDeletedSuccessfully\"));\r\n        } catch (error) {\r\n            toast.error(t(\"adminsetting.RiskLevel.Table.errorDeletingRiskLevel\"));\r\n        }\r\n    };\r\n\r\n    const modalHide = () => setModal(false);\r\n\r\n    useEffect(() => {\r\n        getRisklevelData();\r\n    }, []);\r\n\r\n    return (\r\n        <div>\r\n            <Modal show={isModalShow} onHide={modalHide}>\r\n                <Modal.Header closeButton>\r\n                    <Modal.Title>{t(\"adminsetting.RiskLevel.DeleteRisklevel\")}</Modal.Title>\r\n                </Modal.Header>\r\n                <Modal.Body>{t(\"adminsetting.RiskLevel.Areyousurewanttodeletethisrisklevel\")} </Modal.Body>\r\n                <Modal.Footer>\r\n                    <Button variant=\"secondary\" onClick={modalHide}>\r\n                        {t(\"Cancel\")}\r\n                    </Button>\r\n                    <Button variant=\"primary\" onClick={modalConfirm}>\r\n                        {t(\"yes\")}\r\n                    </Button>\r\n                </Modal.Footer>\r\n            </Modal>\r\n\r\n            <RKITable\r\n                columns={columns}\r\n                data={tabledata}\r\n                totalRows={totalRows}\r\n                pagServer={true}\r\n                handlePerRowsChange={handlePerRowsChange}\r\n                handlePageChange={handlePageChange}\r\n            />\r\n        </div>\r\n    );\r\n};\r\n\r\nexport default RisklevelTable;\r\n", "\n    (window.__NEXT_P = window.__NEXT_P || []).push([\n      \"/adminsettings/risklevel\",\n      function () {\n        return require(\"private-next-pages/adminsettings/risklevel/index.tsx\");\n      }\n    ]);\n    if(module.hot) {\n      module.hot.dispose(function () {\n        window.__NEXT_P.push([\"/adminsettings/risklevel\"])\n      });\n    }\n  "], "names": ["create", "canAddAreaOfWork", "connectedAuthWrapper", "authenticatedSelector", "state", "permissions", "area_of_work", "wrapperDisplayName", "country", "deployment_status", "event_status", "expertise", "institution_focal_point", "hazard", "hazard_type", "institution", "institution_network", "institution_type", "operation_status", "project_status", "region", "risk_level", "syndrome", "update_type", "canAddUsers", "users", "worl_region", "landing_page", "operation", "project", "event", "vspace", "update", "t", "useTranslation", "ShowRisklevelIndex", "div", "Container", "style", "overflowX", "fluid", "className", "Row", "Col", "xs", "PageHeading", "title", "Link", "href", "as", "<PERSON><PERSON>", "variant", "size", "RisklevelTable", "ShowAddRiskLevels", "canAddRiskLevels", "useSelector", "NoAccessMessage", "RKITable", "props", "paginationComponentOptions", "rowsPerPageText", "columns", "data", "totalRows", "resetPaginationToggle", "subheader", "subHeaderComponent", "handlePerRowsChange", "handlePageChange", "rowsPerPage", "defaultRowsPerPage", "selectableRows", "loading", "pagServer", "onSelectedRowsChange", "clearSelectedRows", "sortServer", "onSort", "persistTableHead", "sortFunction", "otherProps", "dataTableProps", "noDataComponent", "<PERSON><PERSON><PERSON><PERSON>", "dense", "paginationResetDefaultPage", "subHeader", "progressPending", "pagination", "paginationServer", "paginationPerPage", "paginationRowsPerPageOptions", "paginationTotalRows", "onChangeRowsPerPage", "onChangePage", "progressComponent", "CustomLoader", "sortIcon", "i", "DataTable", "defaultProps", "_props", "h2", "tabledata", "setDataToTable", "useState", "setLoading", "setTotalRows", "perPage", "setPerPage", "isModalShow", "setModal", "selectRisklevel", "setSelectRisklevel", "risklevelParams", "sort", "limit", "page", "query", "name", "selector", "row", "sortable", "level", "cell", "d", "_id", "a", "onClick", "userAction", "getRisklevelData", "response", "apiService", "get", "length", "totalCount", "newPerPage", "modalConfirm", "remove", "toast", "success", "error", "modalHide", "useEffect", "Modal", "show", "onHide", "Header", "closeButton", "Title", "Body", "Footer"], "sourceRoot": "", "ignoreList": []}