{"version": 3, "file": "static/chunks/8199-f4c36f02b80a7673.js", "mappings": "wKA4FA,MArE0BA,IACxB,GAAM,CAAEC,MAAI,CAAE,CAAGC,CAAAA,EAAAA,EAAAA,EAAAA,CAAcA,CAAC,GAoEnBC,OAnEPC,EAAcH,EAAKI,KAmEIF,EAAC,CAnEG,CAC3B,SAAEG,CAAO,CAAE,CAAGN,EACd,CAACO,EAAQC,EAAU,CAAQC,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAC,CAAC,GACrC,CAACC,EAAcC,EAAgB,CAAQF,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAC,CAAC,GACjD,CAACG,EAAYC,EAAc,CAAQJ,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAC,CAAC,GAO7CK,EAAc,KAClBH,EAAgB,MAChBE,EAAc,KAChB,EAUME,EAAuB,KAC3BP,EAAU,CACRQ,MAAOV,EAAQU,KAAK,CACpBC,GAAIX,EAAQY,GAAG,CACfC,IACEb,EAAQc,OAAO,EAAId,EAAQc,OAAO,CAACC,WAAW,CAC1CC,WAAWhB,EAAQc,OAAO,CAACC,WAAW,CAAC,EAAE,CAACE,QAAQ,EAClD,KACNC,IACElB,EAAQc,OAAO,EAAId,EAAQc,OAAO,CAACC,WAAW,CAC1CC,WAAWhB,EAAQc,OAAO,CAACC,WAAW,CAAC,EAAE,CAACI,SAAS,EACnD,IACR,EACF,EAKA,MAHAC,CAAAA,EAAAA,EAAAA,SAAAA,CAASA,CAAC,KACRX,GACF,EAAG,CAACT,EAAQ,EAEV,iCACG,IACAC,GAAUA,EAAOU,EAAE,CAClB,UAACU,EAAAA,CAAOA,CAAAA,CACNC,QAASd,EACTT,SAAUD,EACVyB,cAAe,CAAEV,IAAKZ,EAAOY,GAAG,CAAEK,IAAKjB,EAAOiB,GAAG,EACjDd,aAAcA,EACdE,WAAY,UA7CD,IACjB,GAAM,MAAEkB,CAAI,CAAE,CAAGC,EACjB,MAAO,UAACC,IAAAA,gBAAGF,EAAAA,KAAAA,EAAAA,EAAMG,IAAI,EAAVH,EA2CQI,CAAWJ,KAAMlB,aAE9B,UAACuB,EAAAA,CAAYA,CAAAA,CACXF,KAAM1B,EAAOS,KAAK,CAClBoB,KAAM,CACJC,IAAK,8BACP,EACAC,QA1CY,CA0CHC,EA1CqCC,EAAaC,KACnE3B,IACAH,EAAgB6B,GAChB3B,EAAc,CACZoB,KAAMS,EAAcT,IAAI,EAE5B,EAqCUU,SAAUpC,MAGZ,OAGV,2ECtBA,MA/CkD,OAAC,MACjD0B,EAAO,QAAQ,IACfhB,CA6CakB,CA7CR,EAAE,SA6CkBA,EAAC,EA5Cd,EAAE,MACdS,CAAI,MACJR,CAAI,UACJO,CAAQ,SACRL,CAAO,OACPtB,CAAK,CACL6B,aAAY,CAAK,CAClB,UAsBC,GAAyC,UAAxB,OAAOF,EAASxB,GAAG,EAAyC,UAAU,OAA3BwB,EAASnB,GAAG,CAKtE,UAACsB,EAAAA,EAAMA,CAAAA,CACLH,SAAUA,EACVP,KAAMA,EACNpB,MAAOA,GAASiB,EAChBY,UAAWA,EACXP,QA/BgB,CA+BPS,GA9BPT,GAeFA,EAdoB,IADT,EAeHU,KAZN/B,QAYmBuB,IAXnBS,OACAL,WACAD,CACF,EAGe,UACbA,EACAO,YAAa,IAAMP,CACrB,EAE6BF,EAEjC,IAIS,IAYX,wHCiCA,MAtD0B,QAgBKzC,EAMAA,EAMAA,EAMFA,EAjCzB,GAAM,GAAEmD,CAAC,CAAE,CAAGjD,CAAAA,EAAAA,EAAAA,EAAAA,CAAcA,CAAC,UAC7B,CAoD4BkD,EAAC,GAnDzB,+BACI,WAACC,EAAAA,CAAGA,CAAAA,WACA,UAACC,EAAAA,CAAGA,CAAAA,CAACC,GAAI,WACL,UAACpD,EAAAA,CAAgBA,CAAAA,CAACG,QAASN,EAAMwD,SAAS,KAE9C,WAACF,EAAAA,CAAGA,CAAAA,CAACG,UAAU,uBAAuBF,GAAI,YACtC,WAACG,IAAAA,WACI,IACD,UAACC,IAAAA,UAAGR,EAAE,yBAA2B,KAAE,UAACS,OAAAA,UAAM5D,EAAMwD,SAAS,CAAGxD,EAAMwD,SAAS,CAACxC,KAAK,CAAG,KAAW,OAElG6C,SA2CZA,CAAyC,CAAEL,CAAc,EAC9D,MACI,WAACE,IAAAA,WACG,UAACC,IAAAA,UAAGR,EAAE,+BAAiC,IACvC,UAACS,OAAAA,UAAMJ,GAAaA,EAAUM,SAAS,CAAGN,EAAUM,SAAS,CAAC9C,KAAK,CAAG,OAGlF,EAlDoCmC,EAAGnD,EAAMwD,SAAS,EAClC,WAACE,IAAAA,WACG,UAACC,IAAAA,UAAGR,EAAE,mCAAqC,IAC3C,UAACS,OAAAA,UAAM5D,CAAAA,OAAAA,EAAAA,EAAMwD,SAAAA,EAANxD,KAAAA,EAAAA,EAAiBoB,GAAjBpB,IAAiBoB,EAAUpB,EAAMwD,SAAS,CAACpC,OAAO,CAACJ,KAAK,CAAG,QAErE+C,SA+CZA,CAA+C,CAAEP,CAAc,EACpE,MACI,WAACE,IAAAA,WACG,UAACC,IAAAA,UAAGR,EAAE,gCAAkC,IACxC,UAACS,OAAAA,UAAMJ,GAAaA,EAAUQ,aAAa,EAA+B,IAA5BR,EAAUQ,aAAa,EAAa,MAAQ,SAGtG,EAtD0Cb,EAAGnD,EAAMwD,SAAS,EAwD5D,SAASS,CAA8C,CAAET,CAAc,EACnE,MACI,WAACE,IAAAA,WACG,UAACC,IAAAA,UAAGR,EAAE,+BAAiC,IACvC,UAACS,OAAAA,UACIJ,GAAaA,EAAUU,eAAe,CACjCV,EAAUU,eAAe,CAACC,GAAG,CAAC,GAAeC,EAAKpD,KAAK,EAAEqD,IAAI,CAAC,MAC9D,OAItB,EAlEyClB,EAAGnD,EAAMwD,SAAS,EACvC,WAACE,IAAAA,WACG,UAACC,IAAAA,UAAGR,EAAE,wBAA0B,IAChC,UAACS,OAAAA,UAAM5D,CAAAA,OAAAA,EAAAA,EAAMwD,SAAAA,EAANxD,KAAAA,EAAAA,EAAiBsE,GAAjBtE,GAAiBsE,EAAStE,EAAMwD,SAAS,CAACc,MAAM,CAACtD,KAAK,CAAG,QAsBpF,SAASuD,MAIUvE,EAHf,MACI,WAAC0D,IAAAA,WACG,UAACC,IAAAA,UAAGR,EAAE,4BAA8B,IACpC,UAACS,OAAAA,UAAM5D,CAAAA,OAAAA,EAAAA,EAAMwD,SAAAA,EAANxD,KAAAA,EAAAA,EAAiBwE,GAAjBxE,QAAiBwE,EAAcxE,EAAMwD,SAAS,CAACgB,WAAW,CAACxD,KAAK,CAAG,OAGtF,IA1BiByD,SA8DZA,CAA2C,CAAEjB,CAAc,EAChE,MACI,WAACE,IAAAA,WACG,UAACC,IAAAA,UAAGR,EAAE,wBAA0B,IAChC,UAACS,OAAAA,UACIJ,GAAaA,EAAUkB,MAAM,CACxBlB,EAAUkB,MAAM,CAACP,GAAG,CAAC,GAAgBC,EAAOA,EAAKpD,KAAK,CAAC2D,EAAE,CAAG,IAAKN,IAAI,CAAC,MACtE,OAItB,EAzEsClB,EAAGnD,EAAMwD,SAAS,EACpC,WAACE,IAAAA,WACG,UAACC,IAAAA,UAAGR,EAAE,0BAA4B,IAClC,UAACS,OAAAA,UAAM5D,CAAAA,OAAAA,EAAAA,EAAMwD,SAAS,EAAfxD,KAAAA,EAAAA,EAAiB4E,GAAjB5E,KAAiB4E,EAAW5E,EAAMwD,SAAS,CAACoB,QAAQ,CAAC5D,KAAK,CAAG,QAwE5F,SAAS6D,CAA0C,CAAErB,CAAc,EAC/D,MACI,WAACE,IAAAA,WACG,UAACC,IAAAA,UAAGR,EAAE,qCAAuC,IAC7C,UAACS,OAAAA,UACIJ,GAAaA,EAAUsB,oBAAoB,CAAsC,KAAnCtB,EAAUsB,oBAAoB,EAAa,MAAQ,SAIlH,EA/EqC3B,EAAGnD,EAAMwD,SAAS,EACnC,WAACE,IAAAA,CAAEqB,MAAO,CAAEC,OAAQ,CAAE,YAClB,UAACrB,IAAAA,UAAGR,EAAE,qCAAuC,IAC7C,UAACS,OAAAA,UACI5D,CAAAA,OAAAA,EAAAA,EAAMwD,SAAAA,EAANxD,KAAAA,EAAAA,EAAiBiF,GAAjBjF,iBAAiBiF,GAC6B,IAAzCjF,EAAMwD,SAAS,CAACyB,oBAAoB,EAAa,MACjD,iBAgBlC,2ECjFA,MARyB,OAAC,UAAEtC,CAAQ,OAQrBuC,OARuBC,CAAY,QAQnBD,EARqBE,CAAQ,CAAS,GACnE,MACE,UAACC,EAAAA,EAAUA,CAAAA,CAAC1C,SAAUA,EAAUwC,aAAcA,WAC5C,UAACG,MAAAA,UAAKF,KAGZ,ECdMG,EAAO,mBACPC,EAAQ,qBACRC,EAAO,eACPC,EAAW,kBAgTjB,EA/SkB,CAChB,CACE,YAAe,IA6SJC,OA5SX,EA4SoBA,EAAC,IA5SV,CACT,CACE,MAAS,SACX,EACD,EAEH,CACE,YAAeJ,EACf,QAAW,CACT,CACE,MAAS,SACX,EACD,EAEH,CACE,YAAeC,EACf,QAAW,CACT,CACE,MAAS,SACX,EACD,EAEH,CACE,YAAe,iBACf,YAAe,WACf,QAAW,CACT,CACE,WAAc,KAChB,EACD,EAEH,CACE,YAAe,yBACf,YAAeE,EACf,QAAW,CACT,CACE,MAAS,SACX,EACD,EAEH,CACE,YAAe,6BACf,YAAe,SACf,QAAW,CACT,CACE,WAAc,KAChB,EACD,EAEH,CACE,YAAe,6BACf,YAAeH,EACf,QAAW,CACT,CACE,MAAS,SACX,EACD,EAEH,CACE,YAAe,0BACf,YAAeG,EACf,QAAW,CACT,CACE,MAAS,SACX,EACD,EAEH,CACE,YAAe,qBACf,YAAeA,EACf,QAAW,CACT,CACE,MAAS,SACX,EACD,EAEH,CACE,YAAe,oBACf,YAAe,WACf,QAAW,CACT,CACE,MAAS,SACX,EAEJ,EACA,CACE,YAAe,MACf,QAAW,CACT,CACE,WAAc,KAChB,EACD,EAEH,CACE,YAAe,MACf,YAAe,WACf,QAAW,CACT,CACE,MAAS,SACX,EACD,EAEH,CACE,YAAe,MACf,YAAe,cACf,QAAW,CACT,CACE,WAAc,KAChB,EACD,EAEH,CACE,YAAe,MACf,YAAeH,EACf,QAAW,CACT,CACE,MAAS,SACX,EACD,EAEH,CACE,YAAe,MACf,YAAeC,EACf,QAAW,CACT,CACE,MAAS,SACX,EACD,EAEH,CACE,YAAe,WACf,YAAe,gBACf,QAAW,CACT,CACE,MAAS,SACX,EACD,EAEH,CACE,YAAe,WACf,YAAeD,EACf,QAAW,CACT,CACE,MAAS,SACX,EACD,EAEH,CACE,YAAe,OACf,QAAW,CACT,CACE,WAAc,KAChB,EACD,EAEH,CACE,YAAe,OACf,YAAe,WACf,QAAW,CACT,CACE,MAAS,SACX,EACD,EAEH,CACE,YAAe,OACf,YAAe,cACf,QAAW,CACT,CACE,WAAc,KAChB,EACD,EAEH,CACE,YAAe,OACf,YAAeA,EACf,QAAW,CACT,CACE,MAAS,SACX,EACD,EAEH,CACE,YAAe,OACf,YAAeC,EACf,QAAW,CACT,CACE,MAAS,SACX,EACD,EAEH,CACE,YAAeC,EACf,YAAe,WACf,QAAW,CACT,CACE,MAAS,SACX,EACD,EAEH,CACE,YAAeA,EACf,YAAeC,EACf,QAAW,CACT,CACE,MAAS,SACX,EACD,EAEH,CACE,YAAeD,EACf,YAAeF,EACf,QAAW,CACT,CACE,MAAS,SACX,EACD,EAEH,CACE,YAAeE,EACf,YAAeD,EACf,QAAW,CACT,CACE,MAAS,SACX,EACD,EAEH,CACE,YAAe,aACf,YAAe,SACf,QAAW,CACT,CACE,WAAc,KAChB,EACD,EAEH,CACE,YAAe,UACf,QAAW,CACT,CACE,WAAc,KAChB,EACD,EAEH,CACE,YAAe,UACf,YAAeD,EACf,QAAW,CACT,CACE,MAAS,SACX,EACD,EAEH,CACE,YAAe,UACf,YAAeC,EACf,QAAW,CACT,CACE,MAAS,SACX,EACD,EAEH,CACE,YAAe,eACf,YAAe,gBACf,QAAW,CACT,CACE,MAAS,SACX,EACD,EAEH,CACE,YAAe,kBACf,YAAe,WACf,QAAW,CACT,CACE,MAAS,SACX,EACD,EAEH,CACE,YAAe,QACf,YAAe,WACf,QAAW,CACT,CACE,MAAS,SACX,EACD,EAEH,CACE,YAAe,QACf,YAAeD,EACf,QAAW,CACT,CACE,MAAS,SACX,EACD,EAEJ,2BCnND,MAzEwC,OAAC,SAyE1B5D,GAxEbf,CAAU,GAwEUe,EAAC,SAvErBjB,CAAY,eACZmB,CAAa,UACbuD,CAAQ,QACRQ,EAAS,GAAG,OACZC,EAAQ,MAAM,UACdxF,CAAQ,CACRyF,OAAO,CAAC,SACRC,EAAU,CAAC,SACXnE,CAAO,CACR,GACO,QAAEoE,CAAM,CAAE,CAAGC,CAAAA,EAAAA,EAAAA,SAAAA,CAASA,GACtB,UAAEC,CAAQ,WAAEC,CAAS,CAAE,CAAIC,CAAAA,EAAAA,EAAAA,CAAAA,CAAaA,UAmB9C,EAAsB,SAAP,CAAQd,MAAAA,UAAI,uBACtBY,EAGH,UAACZ,MAAAA,CAAI7B,UAAU,yBACb,UAAC6B,MAAAA,CAAI7B,UAAU,WAAWsB,MAAO,CAAEc,QAAOD,SAAQjD,SAAU,UAAW,WACrE,WAAC0D,EAAAA,EAASA,CAAAA,CACRC,kBAzBe,CAyBIC,MAxBlBV,EACPD,OAA0B,UAAlB,OAAOA,EAAsB,GAAU,OAAPA,EAAO,MAAMA,CACvD,EAuBQY,OAhBO3E,CAgBC2E,EArBM,CACpBrF,IAAK,SAIyBsF,CAH9BjF,IAAK,SACP,EAmBQsE,KAAMA,EACNY,OAhBU,CAgBFC,GAfdxC,EAAIyC,UAAU,CAAC,CACbC,OAAQlB,CACV,EACF,EAaQmB,QAAS,CACPf,EAhBWJ,MAgBFI,EACTlD,WAAW,EACXkE,mBAAmB,EACnBC,mBAAmB,EACnBC,YAAY,EACZC,gBAAgB,EAChBC,gBAAgB,EAChBC,mBAAmB,CACrB,YAEChC,EACAxE,GAAcF,GAAgBA,EAAawC,WAAW,EACrD,UAACgC,EAAgBA,CACfvC,SAAUjC,EAAawC,SADRgC,EACmB,GAClCC,aAAc,KAEZkC,QAAQC,GAAG,CAAC,eACZ1F,SAAAA,GACF,QADEA,GAGDhB,WA/BS,UAAC0E,MAAAA,UAAI,mBAsC7B", "sources": ["webpack://_N_E/./components/common/maps/ShowMapContainer.tsx", "webpack://_N_E/./components/common/RKIMapMarker.tsx", "webpack://_N_E/./pages/event/components/EventCoverSection.tsx", "webpack://_N_E/./components/common/RKIMapInfowindow.tsx", "webpack://_N_E/./components/common/mapStyles.tsx", "webpack://_N_E/./components/common/RKIMap1.tsx"], "sourcesContent": ["//Import Library\r\nimport React, { useEffect, useState } from \"react\";\r\n\r\n//Import services/components\r\nimport RKIMap1 from \"../RKIMap1\";\r\nimport RKIMapMarker from \"../RKIMapMarker\";\r\nimport { useTranslation } from 'next-i18next';\r\n\r\ninterface MapData {\r\n  _id: string;\r\n  title: string;\r\n  country?: {\r\n    coordinates?: Array<{\r\n      latitude: string;\r\n      longitude: string;\r\n    }>;\r\n  };\r\n}\r\n\r\ninterface ShowMapContainerProps {\r\n  mapdata: MapData;\r\n}\r\n\r\nconst ShowMapContainer = (props: ShowMapContainerProps) => {\r\n  const { i18n } = useTranslation('common');\r\n  const currentLang = i18n.language;\r\n  const { mapdata } = props;\r\n  const [points, setPoints]: any = useState({});\r\n  const [activeMarker, setactiveMarker]: any = useState({});\r\n  const [markerInfo, setMarkerInfo]: any = useState({});\r\n\r\n  const MarkerInfo = (Markerprops: { info: { name?: string } }) => {\r\n    const { info } = Markerprops;\r\n    return <a>{info?.name}</a>;\r\n  };\r\n\r\n  const resetMarker = () => {\r\n    setactiveMarker(null);\r\n    setMarkerInfo(null);\r\n  };\r\n\r\n  const onMarkerClick = (onMarkerprops: { name: string }, marker: any, e: any) => {\r\n    resetMarker();\r\n    setactiveMarker(marker);\r\n    setMarkerInfo({\r\n      name: onMarkerprops.name,\r\n    });\r\n  };\r\n\r\n  const setPointsFromMapData = () => {\r\n    setPoints({\r\n      title: mapdata.title,\r\n      id: mapdata._id,\r\n      lat:\r\n        mapdata.country && mapdata.country.coordinates\r\n          ? parseFloat(mapdata.country.coordinates[0].latitude)\r\n          : null,\r\n      lng:\r\n        mapdata.country && mapdata.country.coordinates\r\n          ? parseFloat(mapdata.country.coordinates[0].longitude)\r\n          : null,\r\n    });\r\n  };\r\n\r\n  useEffect(() => {\r\n    setPointsFromMapData();\r\n  }, [mapdata]);\r\n  return (\r\n    <>\r\n      {\" \"}\r\n      {points && points.id ? (\r\n        <RKIMap1\r\n          onClose={resetMarker}\r\n          language={currentLang}\r\n          initialCenter={{ lat: points.lat, lng: points.lng }}\r\n          activeMarker={activeMarker}\r\n          markerInfo={<MarkerInfo info={markerInfo} />}\r\n        >\r\n          <RKIMapMarker\r\n            name={points.title}\r\n            icon={{\r\n              url: \"/images/map-marker-white.svg\",\r\n            }}\r\n            onClick={onMarkerClick}\r\n            position={points}\r\n          />\r\n        </RKIMap1>\r\n      ) : null}\r\n    </>\r\n  );\r\n};\r\n\r\nexport default ShowMapContainer;\r\n", "import React from 'react';\r\nimport { Marker } from '@react-google-maps/api';\r\n\r\ninterface RKIMapMarkerProps {\r\n  name?: string;\r\n  id?: string;\r\n  countryId?: string;\r\n  type?: string;\r\n  icon?: {\r\n    url: string;\r\n    scaledSize?: google.maps.Size;\r\n  };\r\n  position: {\r\n    lat: number;\r\n    lng: number;\r\n  };\r\n  onClick?: (props: any, marker: any, e: any) => void;\r\n  title?: string;\r\n  draggable?: boolean;\r\n}\r\n\r\nconst RKIMapMarker: React.FC<RKIMapMarkerProps> = ({\r\n  name = 'Marker',\r\n  id = '',\r\n  countryId = '',\r\n  type,\r\n  icon,\r\n  position,\r\n  onClick,\r\n  title,\r\n  draggable = false,\r\n}) => {\r\n  const handleClick = (e: google.maps.MapMouseEvent) => {\r\n    if (onClick) {\r\n      const markerProps = {\r\n        name,\r\n        id,\r\n        countryId,\r\n        type,\r\n        position,\r\n      };\r\n\r\n      // Create a marker-like object for compatibility with old onClick signature\r\n      const marker = {\r\n        position,\r\n        getPosition: () => position,\r\n      };\r\n\r\n      onClick(markerProps, marker, e);\r\n    }\r\n  };\r\n\r\n  // Ensure position is valid\r\n  if (!position || typeof position.lat !== 'number' || typeof position.lng !== 'number') {\r\n    return null;\r\n  }\r\n\r\n  return (\r\n    <Marker\r\n      position={position}\r\n      icon={icon}\r\n      title={title || name}\r\n      draggable={draggable}\r\n      onClick={handleClick}\r\n    />\r\n  );\r\n};\r\n\r\nexport default RKIMapMarker;\r\n", "//Import Library\r\nimport React from 'react';\r\nimport { Col, Row } from \"react-bootstrap\";\r\n\r\n//Import services/components\r\nimport ShowMapContainer from \"../../../components/common/maps/ShowMapContainer\";\r\nimport { useTranslation } from 'next-i18next';\r\n\r\ninterface EventCoverSectionProps {\r\n  eventData: {\r\n    _id: string;\r\n    title: string;\r\n    country?: {\r\n      title: string;\r\n      coordinates?: Array<{\r\n        latitude: string;\r\n        longitude: string;\r\n      }>;\r\n    };\r\n    operation?: {\r\n      title: string;\r\n    };\r\n    status?: {\r\n      title: string;\r\n    };\r\n    hazard_type?: {\r\n      title: string;\r\n    };\r\n    syndrome?: {\r\n      title: string;\r\n    };\r\n    officially_validated?: boolean;\r\n    rki_monitored?: boolean;\r\n    laboratory_confirmed?: boolean;\r\n    country_regions?: Array<{\r\n      title: string;\r\n    }>;\r\n    hazard?: Array<{\r\n      title: {\r\n        en: string;\r\n      };\r\n    }>;\r\n  };\r\n}\r\n\r\nconst EventCoverSection = (props: EventCoverSectionProps) => {\r\n    const { t } = useTranslation('common');\r\n    return (\r\n        <>\r\n            <Row>\r\n                <Col md={6}>\r\n                    <ShowMapContainer mapdata={props.eventData} />\r\n                </Col>\r\n                <Col className=\"eventDetails ps-md-0\" md={6}>\r\n                    <p>\r\n                        {\" \"}\r\n                        <b>{t(\"Events.show.EventId\")}</b>: <span>{props.eventData ? props.eventData.title : \"\"}</span>{\" \"}\r\n                    </p>\r\n                    {operation_func(t, props.eventData)}\r\n                    <p>\r\n                        <b>{t(\"Events.show.Country&Territory\")}</b>:\r\n                        <span>{props.eventData?.country ? props.eventData.country.title : \"\"}</span>\r\n                    </p>\r\n                    {event_Monitored_func(t, props.eventData)}\r\n                    {Event_countrty_func(t, props.eventData)}\r\n                    <p>\r\n                        <b>{t(\"Events.show.Status\")}</b>:\r\n                        <span>{props.eventData?.status ? props.eventData.status.title : \"\"}</span>\r\n                    </p>\r\n                    {hazard_func()}\r\n                    {hazard_show_func(t, props.eventData)}\r\n                    <p>\r\n                        <b>{t(\"Events.show.Syndrome\")}</b>:\r\n                        <span>{props.eventData?.syndrome ? props.eventData.syndrome.title : \"\"}</span>\r\n                    </p>\r\n                    {laboratory_func(t, props.eventData)}\r\n                    <p style={{ margin: 0 }}>\r\n                        <b>{t(\"Events.show.Validatedbyofficial\")}</b>:\r\n                        <span>\r\n                            {props.eventData?.officially_validated\r\n                                ? props.eventData.officially_validated === true && \"Yes\"\r\n                                : \"No\"}\r\n                        </span>\r\n                    </p>\r\n                </Col>\r\n            </Row>\r\n        </>\r\n    );\r\n\r\n    function hazard_func() {\r\n        return (\r\n            <p>\r\n                <b>{t(\"Events.show.HazardType\")}</b>:\r\n                <span>{props.eventData?.hazard_type ? props.eventData.hazard_type.title : \"\"}</span>\r\n            </p>\r\n        );\r\n    }\r\n}\r\n\r\nexport default EventCoverSection;\r\n\r\nfunction operation_func(t: (key: string) => string, eventData: any) {\r\n    return (\r\n        <p>\r\n            <b>{t(\"Events.show.OperationName\")}</b>:\r\n            <span>{eventData && eventData.operation ? eventData.operation.title : \"\"}</span>\r\n        </p>\r\n    );\r\n}\r\n\r\nfunction event_Monitored_func(t: (key: string) => string, eventData: any) {\r\n    return (\r\n        <p>\r\n            <b>{t(\"Events.show.MonitoredbyRKI\")}</b>:\r\n            <span>{eventData && eventData.rki_monitored ? eventData.rki_monitored === true && \"Yes\" : \"No\"}</span>\r\n        </p>\r\n    );\r\n}\r\n\r\nfunction Event_countrty_func(t: (key: string) => string, eventData: any) {\r\n    return (\r\n        <p>\r\n            <b>{t(\"Events.show.CountryRegion\")}</b>:\r\n            <span>\r\n                {eventData && eventData.country_regions\r\n                    ? eventData.country_regions.map((item: any) => item.title).join(\", \")\r\n                    : \"\"}\r\n            </span>\r\n        </p>\r\n    );\r\n}\r\n\r\nfunction hazard_show_func(t: (key: string) => string, eventData: any) {\r\n    return (\r\n        <p>\r\n            <b>{t(\"Events.show.Hazard\")}</b>:\r\n            <span>\r\n                {eventData && eventData.hazard\r\n                    ? eventData.hazard.map((item: any) => (item ? item.title.en : \"\")).join(\", \")\r\n                    : \"\"}\r\n            </span>\r\n        </p>\r\n    );\r\n}\r\n\r\nfunction laboratory_func(t: (key: string) => string, eventData: any) {\r\n    return (\r\n        <p>\r\n            <b>{t(\"Events.show.LaboratoryConfirmed\")}</b>:\r\n            <span>\r\n                {eventData && eventData.laboratory_confirmed ? eventData.laboratory_confirmed === true && \"Yes\" : \"No\"}\r\n            </span>\r\n        </p>\r\n    );\r\n}", "import { InfoWindow } from '@react-google-maps/api';\r\n\r\ninterface Props {\r\n  position: google.maps.LatLngLiteral;\r\n  onCloseClick?: () => void;\r\n  children?: React.ReactNode;\r\n}\r\n\r\nconst RKIMapInfowindow = ({ position, onCloseClick, children }: Props) => {\r\n  return (\r\n    <InfoWindow position={position} onCloseClick={onCloseClick}>\r\n      <div>{children}</div>\r\n    </InfoWindow>\r\n  );\r\n};\r\n\r\nexport default RKIMapInfowindow;\r\n", "const fill = \"labels.text.fill\"\r\nconst stoke = \"labels.text.stroke\"\r\nconst road = \"road.highway\"\r\nconst geometry = \"geometry.stroke\"\r\nconst mapStyles = [\r\n  {\r\n    \"elementType\": \"geometry\",\r\n    \"stylers\": [\r\n      {\r\n        \"color\": \"#1d2c4d\"\r\n      }\r\n    ]\r\n  },\r\n  {\r\n    \"elementType\": fill,\r\n    \"stylers\": [\r\n      {\r\n        \"color\": \"#8ec3b9\"\r\n      }\r\n    ]\r\n  },\r\n  {\r\n    \"elementType\": stoke,\r\n    \"stylers\": [\r\n      {\r\n        \"color\": \"#1a3646\"\r\n      }\r\n    ]\r\n  },\r\n  {\r\n    \"featureType\": \"administrative\",\r\n    \"elementType\": \"geometry\",\r\n    \"stylers\": [\r\n      {\r\n        \"visibility\": \"off\"\r\n      }\r\n    ]\r\n  },\r\n  {\r\n    \"featureType\": \"administrative.country\",\r\n    \"elementType\": geometry,\r\n    \"stylers\": [\r\n      {\r\n        \"color\": \"#4b6878\"\r\n      }\r\n    ]\r\n  },\r\n  {\r\n    \"featureType\": \"administrative.land_parcel\",\r\n    \"elementType\": \"labels\",\r\n    \"stylers\": [\r\n      {\r\n        \"visibility\": \"off\"\r\n      }\r\n    ]\r\n  },\r\n  {\r\n    \"featureType\": \"administrative.land_parcel\",\r\n    \"elementType\": fill,\r\n    \"stylers\": [\r\n      {\r\n        \"color\": \"#64779e\"\r\n      }\r\n    ]\r\n  },\r\n  {\r\n    \"featureType\": \"administrative.province\",\r\n    \"elementType\": geometry,\r\n    \"stylers\": [\r\n      {\r\n        \"color\": \"#4b6878\"\r\n      }\r\n    ]\r\n  },\r\n  {\r\n    \"featureType\": \"landscape.man_made\",\r\n    \"elementType\": geometry,\r\n    \"stylers\": [\r\n      {\r\n        \"color\": \"#334e87\"\r\n      }\r\n    ]\r\n  },\r\n  {\r\n    \"featureType\": \"landscape.natural\",\r\n    \"elementType\": \"geometry\",\r\n    \"stylers\": [\r\n      {\r\n        \"color\": \"#023e58\"\r\n      }\r\n    ]\r\n  },\r\n  {\r\n    \"featureType\": \"poi\",\r\n    \"stylers\": [\r\n      {\r\n        \"visibility\": \"off\"\r\n      }\r\n    ]\r\n  },\r\n  {\r\n    \"featureType\": \"poi\",\r\n    \"elementType\": \"geometry\",\r\n    \"stylers\": [\r\n      {\r\n        \"color\": \"#283d6a\"\r\n      }\r\n    ]\r\n  },\r\n  {\r\n    \"featureType\": \"poi\",\r\n    \"elementType\": \"labels.text\",\r\n    \"stylers\": [\r\n      {\r\n        \"visibility\": \"off\"\r\n      }\r\n    ]\r\n  },\r\n  {\r\n    \"featureType\": \"poi\",\r\n    \"elementType\": fill,\r\n    \"stylers\": [\r\n      {\r\n        \"color\": \"#6f9ba5\"\r\n      }\r\n    ]\r\n  },\r\n  {\r\n    \"featureType\": \"poi\",\r\n    \"elementType\": stoke,\r\n    \"stylers\": [\r\n      {\r\n        \"color\": \"#1d2c4d\"\r\n      }\r\n    ]\r\n  },\r\n  {\r\n    \"featureType\": \"poi.park\",\r\n    \"elementType\": \"geometry.fill\",\r\n    \"stylers\": [\r\n      {\r\n        \"color\": \"#023e58\"\r\n      }\r\n    ]\r\n  },\r\n  {\r\n    \"featureType\": \"poi.park\",\r\n    \"elementType\": fill,\r\n    \"stylers\": [\r\n      {\r\n        \"color\": \"#3C7680\"\r\n      }\r\n    ]\r\n  },\r\n  {\r\n    \"featureType\": \"road\",\r\n    \"stylers\": [\r\n      {\r\n        \"visibility\": \"off\"\r\n      }\r\n    ]\r\n  },\r\n  {\r\n    \"featureType\": \"road\",\r\n    \"elementType\": \"geometry\",\r\n    \"stylers\": [\r\n      {\r\n        \"color\": \"#304a7d\"\r\n      }\r\n    ]\r\n  },\r\n  {\r\n    \"featureType\": \"road\",\r\n    \"elementType\": \"labels.icon\",\r\n    \"stylers\": [\r\n      {\r\n        \"visibility\": \"off\"\r\n      }\r\n    ]\r\n  },\r\n  {\r\n    \"featureType\": \"road\",\r\n    \"elementType\": fill,\r\n    \"stylers\": [\r\n      {\r\n        \"color\": \"#98a5be\"\r\n      }\r\n    ]\r\n  },\r\n  {\r\n    \"featureType\": \"road\",\r\n    \"elementType\": stoke,\r\n    \"stylers\": [\r\n      {\r\n        \"color\": \"#1d2c4d\"\r\n      }\r\n    ]\r\n  },\r\n  {\r\n    \"featureType\": road,\r\n    \"elementType\": \"geometry\",\r\n    \"stylers\": [\r\n      {\r\n        \"color\": \"#2c6675\"\r\n      }\r\n    ]\r\n  },\r\n  {\r\n    \"featureType\": road,\r\n    \"elementType\": geometry,\r\n    \"stylers\": [\r\n      {\r\n        \"color\": \"#255763\"\r\n      }\r\n    ]\r\n  },\r\n  {\r\n    \"featureType\": road,\r\n    \"elementType\": fill,\r\n    \"stylers\": [\r\n      {\r\n        \"color\": \"#b0d5ce\"\r\n      }\r\n    ]\r\n  },\r\n  {\r\n    \"featureType\": road,\r\n    \"elementType\": stoke,\r\n    \"stylers\": [\r\n      {\r\n        \"color\": \"#023e58\"\r\n      }\r\n    ]\r\n  },\r\n  {\r\n    \"featureType\": \"road.local\",\r\n    \"elementType\": \"labels\",\r\n    \"stylers\": [\r\n      {\r\n        \"visibility\": \"off\"\r\n      }\r\n    ]\r\n  },\r\n  {\r\n    \"featureType\": \"transit\",\r\n    \"stylers\": [\r\n      {\r\n        \"visibility\": \"off\"\r\n      }\r\n    ]\r\n  },\r\n  {\r\n    \"featureType\": \"transit\",\r\n    \"elementType\": fill,\r\n    \"stylers\": [\r\n      {\r\n        \"color\": \"#98a5be\"\r\n      }\r\n    ]\r\n  },\r\n  {\r\n    \"featureType\": \"transit\",\r\n    \"elementType\": stoke,\r\n    \"stylers\": [\r\n      {\r\n        \"color\": \"#1d2c4d\"\r\n      }\r\n    ]\r\n  },\r\n  {\r\n    \"featureType\": \"transit.line\",\r\n    \"elementType\": \"geometry.fill\",\r\n    \"stylers\": [\r\n      {\r\n        \"color\": \"#283d6a\"\r\n      }\r\n    ]\r\n  },\r\n  {\r\n    \"featureType\": \"transit.station\",\r\n    \"elementType\": \"geometry\",\r\n    \"stylers\": [\r\n      {\r\n        \"color\": \"#3a4762\"\r\n      }\r\n    ]\r\n  },\r\n  {\r\n    \"featureType\": \"water\",\r\n    \"elementType\": \"geometry\",\r\n    \"stylers\": [\r\n      {\r\n        \"color\": \"#0e1626\"\r\n      }\r\n    ]\r\n  },\r\n  {\r\n    \"featureType\": \"water\",\r\n    \"elementType\": fill,\r\n    \"stylers\": [\r\n      {\r\n        \"color\": \"#4e6d70\"\r\n      }\r\n    ]\r\n  }\r\n];\r\n\r\nexport default mapStyles;", "import React from 'react';\r\nimport { GoogleMap, useJsApiLoader } from '@react-google-maps/api';\r\nimport RKIMapInfowindow from './RKIMapInfowindow';\r\nimport mapStyles from './mapStyles';\r\nimport { useRouter } from \"next/router\";\r\nimport { useGoogleMaps } from './GoogleMapsProvider';\r\n\r\ninterface RKIMap1Props {\r\n  markerInfo?: React.ReactNode;\r\n  activeMarker?: any;\r\n  initialCenter?: { lat: number; lng: number };\r\n  children?: React.ReactNode;\r\n  height?: number | string;\r\n  width?: string;\r\n  language?: string;\r\n  points?: any[];\r\n  zoom?: number;\r\n  minZoom?: number;\r\n  onClose?: () => void\r\n}\r\n\r\nconst RKIMap1: React.FC<RKIMap1Props> = ({\r\n  markerInfo,\r\n  activeMarker,\r\n  initialCenter,\r\n  children,\r\n  height = 300,\r\n  width = \"114%\",\r\n  language,\r\n  zoom = 1,\r\n  minZoom = 1,\r\n  onClose\r\n}) => {\r\n  const { locale } = useRouter();\r\n  const { isLoaded, loadError } =  useGoogleMaps();\r\n  const containerStyle = {\r\n    width: width,\r\n    height: typeof height === 'number' ? `${height}px` : height,\r\n  };\r\n\r\n  const defaultCenter = {\r\n    lat: 52.520017,\r\n    lng: 13.404195,\r\n  };\r\n\r\n  const center = initialCenter || defaultCenter;\r\n\r\n  const onMapLoad = (map: google.maps.Map) => {\r\n    map.setOptions({\r\n      styles: mapStyles,\r\n    });\r\n  };\r\n\r\n  if (loadError) return <div>Error loading maps</div>;\r\n  if (!isLoaded) return <div>Loading Maps...</div>;\r\n\r\n  return (\r\n    <div className=\"map-container\">\r\n      <div className=\"mapprint\" style={{ width, height, position: 'relative' }}>\r\n        <GoogleMap\r\n          mapContainerStyle={containerStyle}\r\n          center={center}\r\n          zoom={zoom}\r\n          onLoad={onMapLoad}\r\n          options={{\r\n            minZoom: minZoom,\r\n            draggable: true,\r\n            keyboardShortcuts: false,\r\n            streetViewControl: false,\r\n            panControl: false,\r\n            clickableIcons: false,\r\n            mapTypeControl: false,\r\n            fullscreenControl: true,\r\n          }}\r\n        >\r\n          {children}\r\n          {markerInfo && activeMarker && activeMarker.getPosition && (\r\n            <RKIMapInfowindow\r\n              position={activeMarker.getPosition()}\r\n              onCloseClick={() => {\r\n                // Handle close if needed\r\n                console.log('close click');\r\n                onClose?.()\r\n              }}\r\n            >\r\n              {markerInfo}\r\n            </RKIMapInfowindow>\r\n          )}\r\n        </GoogleMap>\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default RKIMap1;\r\n"], "names": ["props", "i18n", "useTranslation", "ShowMapContainer", "currentLang", "language", "mapdata", "points", "setPoints", "useState", "activeMarker", "set<PERSON><PERSON><PERSON><PERSON>", "markerInfo", "setMarkerInfo", "reset<PERSON><PERSON><PERSON>", "setPointsFromMapData", "title", "id", "_id", "lat", "country", "coordinates", "parseFloat", "latitude", "lng", "longitude", "useEffect", "RKIMap1", "onClose", "initialCenter", "info", "Markerprops", "a", "name", "MarkerInfo", "R<PERSON>IMapMarker", "icon", "url", "onClick", "onMarkerClick", "marker", "e", "onMarkerprops", "position", "type", "draggable", "<PERSON><PERSON>", "handleClick", "markerProps", "countryId", "getPosition", "t", "EventCoverSection", "Row", "Col", "md", "eventData", "className", "p", "b", "span", "operation_func", "operation", "event_Monitored_func", "rki_monitored", "Event_countrty_func", "country_regions", "map", "item", "join", "status", "hazard_func", "hazard_type", "hazard_show_func", "hazard", "en", "syndrome", "laboratory_func", "laboratory_confirmed", "style", "margin", "officially_validated", "RKIMapInfowindow", "onCloseClick", "children", "InfoWindow", "div", "fill", "stoke", "road", "geometry", "mapStyles", "height", "width", "zoom", "minZoom", "locale", "useRouter", "isLoaded", "loadError", "useGoogleMaps", "GoogleMap", "mapContainerStyle", "containerStyle", "center", "defaultCenter", "onLoad", "onMapLoad", "setOptions", "styles", "options", "keyboardShortcuts", "streetViewControl", "panControl", "clickableIcons", "mapTypeControl", "fullscreenControl", "console", "log"], "sourceRoot": "", "ignoreList": []}