"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/_app",{

/***/ "(pages-dir-browser)/./pages/_app.tsx":
/*!************************!*\
  !*** ./pages/_app.tsx ***!
  \************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(pages-dir-browser)/./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(pages-dir-browser)/./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react_redux__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! react-redux */ \"(pages-dir-browser)/./node_modules/react-redux/dist/react-redux.mjs\");\n/* harmony import */ var redux_persist_integration_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! redux-persist/integration/react */ \"(pages-dir-browser)/./node_modules/redux-persist/es/integration/react.js\");\n/* harmony import */ var redux_persist__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! redux-persist */ \"(pages-dir-browser)/./node_modules/redux-persist/es/index.js\");\n/* harmony import */ var react_hot_toast__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! react-hot-toast */ \"(pages-dir-browser)/./node_modules/react-hot-toast/dist/index.mjs\");\n/* harmony import */ var _barrel_optimize_names_Spinner_react_bootstrap__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=Spinner!=!react-bootstrap */ \"(pages-dir-browser)/__barrel_optimize__?names=Spinner!=!./node_modules/react-bootstrap/esm/index.js\");\n/* harmony import */ var next_app__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! next/app */ \"(pages-dir-browser)/./node_modules/next/app.js\");\n/* harmony import */ var next_app__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(next_app__WEBPACK_IMPORTED_MODULE_5__);\n/* harmony import */ var next_i18next__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! next-i18next */ \"(pages-dir-browser)/./node_modules/next-i18next/dist/esm/index.js\");\n/* harmony import */ var _components_layout_authenticated_Layout__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ../components/layout/authenticated/Layout */ \"(pages-dir-browser)/./components/layout/authenticated/Layout.tsx\");\n/* harmony import */ var _components_layout_authenticated_Footer__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ../components/layout/authenticated/Footer */ \"(pages-dir-browser)/./components/layout/authenticated/Footer.tsx\");\n/* harmony import */ var _styles_global_scss__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ../styles/global.scss */ \"(pages-dir-browser)/./styles/global.scss\");\n/* harmony import */ var _styles_global_scss__WEBPACK_IMPORTED_MODULE_9___default = /*#__PURE__*/__webpack_require__.n(_styles_global_scss__WEBPACK_IMPORTED_MODULE_9__);\n/* harmony import */ var _components_hoc_AuthSync__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ../components/hoc/AuthSync */ \"(pages-dir-browser)/./components/hoc/AuthSync.tsx\");\n/* harmony import */ var _store__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! ../store */ \"(pages-dir-browser)/./store.tsx\");\n/* harmony import */ var _routePermissions__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! ./routePermissions */ \"(pages-dir-browser)/./pages/routePermissions.tsx\");\n/* harmony import */ var _components_common_GoogleMapsProvider__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! ../components/common/GoogleMapsProvider */ \"(pages-dir-browser)/./components/common/GoogleMapsProvider.tsx\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! next/router */ \"(pages-dir-browser)/./node_modules/next/router.js\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_14___default = /*#__PURE__*/__webpack_require__.n(next_router__WEBPACK_IMPORTED_MODULE_14__);\n\nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n//Import services/components\n\n\n\n\n\n\n\n\n\nconst store = (0,_store__WEBPACK_IMPORTED_MODULE_11__[\"default\"])({});\nconst persistor = (0,redux_persist__WEBPACK_IMPORTED_MODULE_3__.persistStore)(store);\nif (true) {\n    module.hot.addStatusHandler((status)=>{\n        if ( true && status === 'ready') {\n            window['__webpack_reload_css__'] = true;\n        }\n    });\n}\nfunction MyApp(param) {\n    let { Component, pageProps, router, isPublicRoute, isLoading } = param;\n    _s();\n    const CanAccessRoutes = (0,_routePermissions__WEBPACK_IMPORTED_MODULE_12__.canAccessRoutes)(()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Component, {\n            ...pageProps,\n            router: router\n        }, void 0, false, {\n            fileName: \"D:\\\\rki\\\\frontend\\\\pages\\\\_app.tsx\",\n            lineNumber: 43,\n            columnNumber: 49\n        }, this));\n    const { locale } = (0,next_router__WEBPACK_IMPORTED_MODULE_14__.useRouter)();\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_common_GoogleMapsProvider__WEBPACK_IMPORTED_MODULE_13__.GoogleMapsProvider, {\n        language: locale || 'en',\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_redux__WEBPACK_IMPORTED_MODULE_15__.Provider, {\n            store: store,\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(redux_persist_integration_react__WEBPACK_IMPORTED_MODULE_2__.PersistGate, {\n                loading: null,\n                persistor: persistor,\n                children: isLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Spinner_react_bootstrap__WEBPACK_IMPORTED_MODULE_16__.Spinner, {\n                    animation: \"border\",\n                    variant: \"primary\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\rki\\\\frontend\\\\pages\\\\_app.tsx\",\n                    lineNumber: 51,\n                    columnNumber: 13\n                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                    children: [\n                        public_route_func(),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_hot_toast__WEBPACK_IMPORTED_MODULE_4__.Toaster, {\n                            position: \"top-right\",\n                            reverseOrder: false\n                        }, void 0, false, {\n                            fileName: \"D:\\\\rki\\\\frontend\\\\pages\\\\_app.tsx\",\n                            lineNumber: 55,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true)\n            }, void 0, false, {\n                fileName: \"D:\\\\rki\\\\frontend\\\\pages\\\\_app.tsx\",\n                lineNumber: 49,\n                columnNumber: 7\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\rki\\\\frontend\\\\pages\\\\_app.tsx\",\n            lineNumber: 48,\n            columnNumber: 5\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\rki\\\\frontend\\\\pages\\\\_app.tsx\",\n        lineNumber: 47,\n        columnNumber: 5\n    }, this);\n    function public_route_func() {\n        return isPublicRoute ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Component, {\n            ...pageProps,\n            router: router\n        }, void 0, false, {\n            fileName: \"D:\\\\rki\\\\frontend\\\\pages\\\\_app.tsx\",\n            lineNumber: 65,\n            columnNumber: 29\n        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_layout_authenticated_Layout__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n            router: router,\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(CanAccessRoutes, {\n                    router: router,\n                    pageProps: pageProps\n                }, void 0, false, {\n                    fileName: \"D:\\\\rki\\\\frontend\\\\pages\\\\_app.tsx\",\n                    lineNumber: 67,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_layout_authenticated_Footer__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {}, void 0, false, {\n                    fileName: \"D:\\\\rki\\\\frontend\\\\pages\\\\_app.tsx\",\n                    lineNumber: 68,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\rki\\\\frontend\\\\pages\\\\_app.tsx\",\n            lineNumber: 66,\n            columnNumber: 7\n        }, this);\n    }\n}\n_s(MyApp, \"8NqG7Oj4xICngQpoOytAK34hitI=\", false, function() {\n    return [\n        next_router__WEBPACK_IMPORTED_MODULE_14__.useRouter\n    ];\n});\n_c = MyApp;\nMyApp.getInitialProps = async (appContext)=>{\n    const appProps = await next_app__WEBPACK_IMPORTED_MODULE_5___default().getInitialProps(appContext);\n    return {\n        ...appProps\n    };\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (withRedux(_store__WEBPACK_IMPORTED_MODULE_11__[\"default\"])(withReduxSaga((0,next_i18next__WEBPACK_IMPORTED_MODULE_6__.appWithTranslation)((0,_components_hoc_AuthSync__WEBPACK_IMPORTED_MODULE_10__[\"default\"])(MyApp)))));\nvar _c;\n$RefreshReg$(_c, \"MyApp\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-browser)/./pages/_app.tsx\n"));

/***/ })

});