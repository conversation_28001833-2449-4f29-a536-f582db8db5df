"use strict";(()=>{var e={};e.id=3504,e.ids=[636,3220,3504],e.modules={123:e=>{e.exports=require("dom-helpers/removeEventListener")},1332:e=>{e.exports=require("react-custom-scrollbars-2")},1428:e=>{e.exports=import("axios")},1680:e=>{e.exports=require("@restart/ui/utils")},1919:e=>{e.exports=require("react-transition-group/Transition")},3892:e=>{e.exports=require("classnames")},4048:e=>{e.exports=require("react-truncate")},6009:e=>{e.exports=require("dom-helpers/ownerDocument")},6945:(e,r,s)=>{s.a(e,async(e,t)=>{try{s.r(r),s.d(r,{default:()=>A});var i=s(8732),a=s(19918),n=s.n(a),o=s(82015),p=s(81181),d=s(63241),l=s(12403),u=s(91353),c=s(42893),m=s(56084),x=s(63487),h=s(88751),g=e([c,x]);[c,x]=g.then?(await g)():g;let A=e=>{let[r,s]=(0,o.useState)([]),[,t]=(0,o.useState)(!1),[a,g]=(0,o.useState)(0),[A,y]=(0,o.useState)(10),[w,S]=(0,o.useState)(!1),[j,q]=(0,o.useState)({}),v=()=>S(!1),{t:f}=(0,h.useTranslation)("common"),P=(0,i.jsxs)(p.A,{id:"popover-basic",children:[(0,i.jsx)(p.A.Header,{as:"h3",className:"text-center",children:"NETWORKS"}),(0,i.jsx)(p.A.Body,{children:(0,i.jsxs)("div",{className:"m-2",children:[(0,i.jsxs)("p",{children:[(0,i.jsx)("b",{children:"EMLab"})," - European Mobile Lab"]}),(0,i.jsxs)("p",{children:[(0,i.jsx)("b",{children:"EMT"})," - Emergency Medical Teams"]}),(0,i.jsxs)("p",{children:[(0,i.jsx)("b",{children:"GHPP"})," - Global Health Protection Program"]}),(0,i.jsxs)("p",{children:[(0,i.jsx)("b",{children:"GOARN"})," - Global Outbreak Alert & Response Network"]}),(0,i.jsxs)("p",{children:[(0,i.jsx)("b",{children:"IANPHI"})," - International Association of National Public Health Institutes"]}),(0,i.jsxs)("p",{children:[(0,i.jsx)("b",{children:"STAKOB"})," - St\xe4ndiger Arbeitskreis der Kompetenz-und Behandlungszentren"]}),(0,i.jsxs)("p",{children:[(0,i.jsx)("b",{children:"WHOCC"}),"- World Health Organization Collaborating Centres"]})]})})]}),_=[{name:(0,i.jsx)(d.A,{trigger:"click",placement:"right",overlay:P,children:(0,i.jsxs)("span",{children:[f("Title"),"\xa0\xa0\xa0",(0,i.jsx)("i",{className:"fa fa-info-circle",style:{cursor:"pointer"},"aria-hidden":"true"})]})}),selector:"title"},{name:f("action"),selector:"",cell:e=>(0,i.jsxs)("div",{children:[(0,i.jsx)(n(),{href:"/adminsettings/[...routes]",as:`/adminsettings/edit_institution_network/${e._id}`,children:(0,i.jsx)("i",{className:"icon fas fa-edit"})}),"\xa0",(0,i.jsx)("a",{onClick:()=>D(e),children:(0,i.jsx)("i",{className:"icon fas fa-trash-alt"})})]})}],k={sort:{title:"asc"},limit:A,page:1,query:{}};(0,o.useEffect)(()=>{b()},[]);let b=async()=>{t(!0);let e=await x.A.get("/institutionnetwork",k);e&&e.data&&e.data.length>0&&(s(e.data),g(e.totalCount),t(!1))},N=async(e,r)=>{k.limit=e,k.page=r,t(!0);let i=await x.A.get("/institutionnetwork",k);i&&i.data&&i.data.length>0&&(s(i.data),y(e),t(!1))},C=async()=>{try{await x.A.remove(`/institutionnetwork/${j}`),b(),S(!1),c.default.success(f("adminsetting.Organisationnetworks.Table.orgNetworkDeletedSuccessfully"))}catch(e){c.default.error(f("adminsetting.Organisationnetworks.Table.errorDeletingOrgNetwork"))}},D=async e=>{q(e._id),S(!0)};return(0,i.jsxs)("div",{children:[(0,i.jsxs)(l.A,{show:w,onHide:v,children:[(0,i.jsx)(l.A.Header,{closeButton:!0,children:(0,i.jsx)(l.A.Title,{children:f("adminsetting.Organisationnetworks.Delete")})}),(0,i.jsx)(l.A.Body,{children:f("adminsetting.Organisationnetworks.sure")}),(0,i.jsxs)(l.A.Footer,{children:[(0,i.jsx)(u.A,{variant:"secondary",onClick:v,children:f("cancel")}),(0,i.jsx)(u.A,{variant:"primary",onClick:C,children:f("yes")})]})]}),(0,i.jsx)(m.A,{columns:_,data:r,totalRows:a,pagServer:!0,handlePerRowsChange:N,handlePageChange:e=>{k.limit=A,k.page=e,b()}})]})};t()}catch(e){t(e)}})},6952:e=>{e.exports=require("@restart/ui/Modal")},7374:e=>{e.exports=require("@restart/ui/Dropdown")},8732:e=>{e.exports=require("react/jsx-runtime")},9653:e=>{e.exports=require("@restart/ui/Overlay")},11242:e=>{e.exports=require("redux-persist/integration/react")},11688:e=>{e.exports=require("@restart/hooks/useWillUnmount")},12388:e=>{e.exports=require("dom-helpers/addEventListener")},13364:e=>{e.exports=require("redux-saga/effects")},14062:e=>{e.exports=import("react-redux")},14078:e=>{e.exports=import("swr")},14332:e=>{e.exports=require("uncontrollable")},16116:e=>{e.exports=require("invariant")},17384:(e,r,s)=>{s.a(e,async(e,t)=>{try{s.r(r),s.d(r,{config:()=>g,default:()=>c,getServerSideProps:()=>h,getStaticPaths:()=>x,getStaticProps:()=>m,reportWebVitals:()=>A,routeModule:()=>v,unstable_getServerProps:()=>j,unstable_getServerSideProps:()=>q,unstable_getStaticParams:()=>S,unstable_getStaticPaths:()=>w,unstable_getStaticProps:()=>y});var i=s(63885),a=s(80237),n=s(81413),o=s(9616),p=s.n(o),d=s(72386),l=s(29853),u=e([d,l]);[d,l]=u.then?(await u)():u;let c=(0,n.M)(l,"default"),m=(0,n.M)(l,"getStaticProps"),x=(0,n.M)(l,"getStaticPaths"),h=(0,n.M)(l,"getServerSideProps"),g=(0,n.M)(l,"config"),A=(0,n.M)(l,"reportWebVitals"),y=(0,n.M)(l,"unstable_getStaticProps"),w=(0,n.M)(l,"unstable_getStaticPaths"),S=(0,n.M)(l,"unstable_getStaticParams"),j=(0,n.M)(l,"unstable_getServerProps"),q=(0,n.M)(l,"unstable_getServerSideProps"),v=new i.PagesRouteModule({definition:{kind:a.A.PAGES,page:"/adminsettings/institutionNetworks",pathname:"/adminsettings/institutionNetworks",bundlePath:"",filename:""},components:{App:d.default,Document:p()},userland:l});t()}catch(e){t(e)}})},18622:e=>{e.exports=require("yup")},19121:e=>{e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},22326:e=>{e.exports=require("react-dom")},22541:e=>{e.exports=require("redux-persist/lib/storage")},25303:e=>{e.exports=require("@restart/ui/NavItem")},26324:e=>{e.exports=require("warning")},27053:(e,r,s)=>{s.d(r,{A:()=>i});var t=s(8732);function i(e){return(0,t.jsx)("h2",{className:"page-heading",children:e.title})}},27825:e=>{e.exports=require("lodash")},27910:e=>{e.exports=require("stream")},28217:e=>{e.exports=require("@restart/ui/DropdownItem")},29021:e=>{e.exports=require("fs")},29294:e=>{e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},29628:e=>{e.exports=require("dom-helpers/addClass")},29825:e=>{e.exports=require("prop-types")},29841:e=>{e.exports=require("dom-helpers/hasClass")},29853:(e,r,s)=>{s.a(e,async(e,t)=>{try{s.r(r),s.d(r,{default:()=>y});var i=s(8732),a=s(7082),n=s(83551),o=s(49481),p=s(91353),d=s(19918),l=s.n(d),u=s(27053),c=s(6945),m=s(88751),x=s(45927),h=s(14062),g=s(35557),A=e([c,h]);[c,h]=A.then?(await A)():A;let y=e=>{let{t:r}=(0,m.useTranslation)("common"),s=()=>(0,i.jsxs)(a.A,{style:{overflowX:"hidden"},fluid:!0,className:"p-0",children:[(0,i.jsx)(n.A,{children:(0,i.jsx)(o.A,{xs:12,children:(0,i.jsx)(u.A,{title:r("adminsetting.Organisationnetworks.OrganisationNetworks")})})}),(0,i.jsx)(n.A,{children:(0,i.jsx)(o.A,{xs:12,children:(0,i.jsx)(l(),{href:"/adminsettings/[...routes]",as:"/adminsettings/create_institution_network",children:(0,i.jsx)(p.A,{variant:"secondary",size:"sm",children:r("adminsetting.Organisationnetworks.AddOrganisationNetwork")})})})}),(0,i.jsx)(n.A,{className:"mt-3",children:(0,i.jsx)(o.A,{xs:12,children:(0,i.jsx)(c.default,{})})})]}),t=(0,x.canAddOrganisationNetworks)(()=>(0,i.jsx)(s,{})),d=(0,h.useSelector)(e=>e);return d?.permissions?.institution_network?.["create:any"]?(0,i.jsx)(t,{}):(0,i.jsx)(g.default,{})};t()}catch(e){t(e)}})},30362:e=>{e.exports=require("@restart/ui/DropdownToggle")},33873:e=>{e.exports=require("path")},35557:(e,r,s)=>{s.r(r),s.d(r,{default:()=>i});var t=s(8732);function i(e){return(0,t.jsx)("div",{className:"container-fluid p-0 response-message-block",children:(0,t.jsx)("div",{className:"message",children:"you don't have permission to access"})})}},36653:e=>{e.exports=require("nprogress")},36955:e=>{e.exports=require("@restart/hooks/useBreakpoint")},37766:e=>{e.exports=require("@restart/hooks/useMergedRefs")},38609:e=>{e.exports=require("react-data-table-component")},39756:e=>{e.exports=import("redux")},40051:e=>{e.exports=require("dom-helpers/removeClass")},40361:e=>{e.exports=require("next/dist/compiled/next-server/pages.runtime.prod.js")},42893:e=>{e.exports=import("react-hot-toast")},43294:e=>{e.exports=require("formik")},45927:(e,r,s)=>{s.r(r),s.d(r,{canAddAreaOfWork:()=>n,canAddContent:()=>k,canAddCountry:()=>o,canAddDeploymentStatus:()=>p,canAddEventStatus:()=>d,canAddExpertise:()=>l,canAddFocalPointApproval:()=>u,canAddHazardTypes:()=>x,canAddHazards:()=>m,canAddLandingPage:()=>_,canAddOperationStatus:()=>y,canAddOrganisationApproval:()=>h,canAddOrganisationNetworks:()=>g,canAddOrganisationTypes:()=>A,canAddProjectStatus:()=>w,canAddRegions:()=>S,canAddRiskLevels:()=>j,canAddSyndromes:()=>q,canAddUpdateTypes:()=>v,canAddUsers:()=>f,canAddVspaceApproval:()=>c,canAddWorldRegion:()=>P,default:()=>b});var t=s(81366),i=s.n(t);let a="create:any",n=i()({authenticatedSelector:e=>!!e.permissions&&!!e.permissions.area_of_work&&!!e.permissions.area_of_work[a],wrapperDisplayName:"CanAddAreaOfWork"}),o=i()({authenticatedSelector:e=>!!e.permissions&&!!e.permissions.country&&!!e.permissions.country[a],wrapperDisplayName:"CanAddCountry"}),p=i()({authenticatedSelector:e=>!!e.permissions&&!!e.permissions.deployment_status&&!!e.permissions.deployment_status[a],wrapperDisplayName:"CanAddDeploymentStatus"}),d=i()({authenticatedSelector:e=>!!e.permissions&&!!e.permissions.event_status&&!!e.permissions.event_status[a],wrapperDisplayName:"CanAddEventStatus"}),l=i()({authenticatedSelector:e=>!!e.permissions&&!!e.permissions.expertise&&!!e.permissions.expertise[a],wrapperDisplayName:"CanAddExpertise"}),u=i()({authenticatedSelector:e=>!!e.permissions&&!!e.permissions.institution_focal_point&&!!e.permissions.institution_focal_point[a],wrapperDisplayName:"CanAddFocalPointApproval"}),c=i()({authenticatedSelector:e=>!!e.permissions&&!!e.permissions.institution_focal_point&&!!e.permissions.institution_focal_point[a],wrapperDisplayName:"CanAddVspaceApproval"}),m=i()({authenticatedSelector:e=>!!e.permissions&&!!e.permissions.hazard&&!!e.permissions.hazard[a],wrapperDisplayName:"CanAddHazards"}),x=i()({authenticatedSelector:e=>!!e.permissions&&!!e.permissions.hazard_type&&!!e.permissions.hazard_type[a],wrapperDisplayName:"CanAddHazardTypes"}),h=i()({authenticatedSelector:e=>!!e.permissions&&!!e.permissions.institution&&!!e.permissions.institution[a],wrapperDisplayName:"CanAddOrganisationApproval"}),g=i()({authenticatedSelector:e=>!!e.permissions&&!!e.permissions.institution_network&&!!e.permissions.institution_network[a],wrapperDisplayName:"CanAddOrganisationNetworks"}),A=i()({authenticatedSelector:e=>!!e.permissions&&!!e.permissions.institution_type&&!!e.permissions.institution_type[a],wrapperDisplayName:"CanAddOrganisationTypes"}),y=i()({authenticatedSelector:e=>!!e.permissions&&!!e.permissions.operation_status&&!!e.permissions.operation_status[a],wrapperDisplayName:"CanAddOperationStatus"}),w=i()({authenticatedSelector:e=>!!e.permissions&&!!e.permissions.project_status&&!!e.permissions.project_status[a],wrapperDisplayName:"CanAddProjectStatus"}),S=i()({authenticatedSelector:e=>!!e.permissions&&!!e.permissions.region&&!!e.permissions.region[a],wrapperDisplayName:"CanAddRegions"}),j=i()({authenticatedSelector:e=>!!e.permissions&&!!e.permissions.risk_level&&!!e.permissions.risk_level[a],wrapperDisplayName:"CanAddRiskLevels"}),q=i()({authenticatedSelector:e=>!!e.permissions&&!!e.permissions.syndrome&&!!e.permissions.syndrome[a],wrapperDisplayName:"CanAddSyndromes"}),v=i()({authenticatedSelector:e=>!!e.permissions&&!!e.permissions.update_type&&!!e.permissions.update_type[a],wrapperDisplayName:"CanAddUpdateTypes"}),f=i()({authenticatedSelector:e=>!!e.permissions&&!!e.permissions.users&&!!e.permissions.users[a],wrapperDisplayName:"CanAddUsers"}),P=i()({authenticatedSelector:e=>!!e.permissions&&!!e.permissions.worl_region&&!!e.permissions.worl_region[a],wrapperDisplayName:"CanAddWorldRegion"}),_=i()({authenticatedSelector:e=>!!e.permissions&&!!e.permissions.landing_page&&!!e.permissions.landing_page[a],wrapperDisplayName:"CanAddLandingPage"}),k=i()({authenticatedSelector:e=>!!e.permissions&&!!e.permissions.operation&&!!e.permissions.operation[a]&&!!e.permissions.project&&!!e.permissions.project[a]&&!!e.permissions.event&&!!e.permissions.event[a]&&!!e.permissions.vspace&&!!e.permissions.vspace[a]&&!!e.permissions.institution&&!!e.permissions.institution[a]&&!!e.permissions.update&&!!e.permissions.update[a]||!1,wrapperDisplayName:"CanAddContent"}),b=n},50009:e=>{e.exports=require("@restart/ui/DropdownMenu")},56084:(e,r,s)=>{s.d(r,{A:()=>d});var t=s(8732);s(82015);var i=s(38609),a=s.n(i),n=s(88751),o=s(30370);function p(e){let{t:r}=(0,n.useTranslation)("common"),s={rowsPerPageText:r("Rowsperpage")},{columns:i,data:p,totalRows:d,resetPaginationToggle:l,subheader:u,subHeaderComponent:c,handlePerRowsChange:m,handlePageChange:x,rowsPerPage:h,defaultRowsPerPage:g,selectableRows:A,loading:y,pagServer:w,onSelectedRowsChange:S,clearSelectedRows:j,sortServer:q,onSort:v,persistTableHead:f,sortFunction:P,..._}=e,k={paginationComponentOptions:s,noDataComponent:r("NoData"),noHeader:!0,columns:i,data:p||[],dense:!0,paginationResetDefaultPage:l,subHeader:u,progressPending:y,subHeaderComponent:c,pagination:!0,paginationServer:w,paginationPerPage:g||10,paginationRowsPerPageOptions:h||[10,15,20,25,30],paginationTotalRows:d,onChangeRowsPerPage:m,onChangePage:x,selectableRows:A,onSelectedRowsChange:S,clearSelectedRows:j,progressComponent:(0,t.jsx)(o.A,{}),sortIcon:(0,t.jsx)("i",{className:"sort-icon fas fa-exchange-alt"}),sortServer:q,onSort:v,sortFunction:P,persistTableHead:f,className:"rki-table"};return(0,t.jsx)(a(),{...k})}p.defaultProps={subHeader:!1,pagination:!0,totalRows:null,pagServer:!0,onSelectedRowsChange:null,clearSelectedRows:!1,sortServer:!1,persistTableHead:!1};let d=p},57664:e=>{e.exports=require("dom-helpers/scrollbarSize")},58928:e=>{e.exports=require("dom-helpers/canUseDOM")},59717:e=>{e.exports=require("@restart/ui/DropdownContext")},60560:e=>{e.exports=require("dom-helpers/contains")},63033:e=>{e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},65447:e=>{e.exports=require("@restart/ui/Button")},67364:e=>{e.exports=require("@restart/hooks/useCallbackRef")},68455:e=>{e.exports=import("redux-saga")},69722:e=>{e.exports=require("es6-promise")},74075:e=>{e.exports=require("zlib")},74716:e=>{e.exports=require("moment")},74987:e=>{e.exports=require("@restart/ui/ModalManager")},78097:e=>{e.exports=require("next-redux-saga")},78634:e=>{e.exports=require("@restart/ui/Anchor")},80237:(e,r)=>{Object.defineProperty(r,"A",{enumerable:!0,get:function(){return s}});var s=function(e){return e.PAGES="PAGES",e.PAGES_API="PAGES_API",e.APP_PAGE="APP_PAGE",e.APP_ROUTE="APP_ROUTE",e.IMAGE="IMAGE",e}({})},81366:e=>{e.exports=require("redux-auth-wrapper/connectedAuthWrapper")},81413:(e,r)=>{Object.defineProperty(r,"M",{enumerable:!0,get:function(){return function e(r,s){return s in r?r[s]:"then"in r&&"function"==typeof r.then?r.then(r=>e(r,s)):"function"==typeof r&&"default"===s?r:void 0}}})},81521:e=>{e.exports=require("dom-helpers/querySelectorAll")},81895:e=>{e.exports=require("@restart/hooks/useEventCallback")},82015:e=>{e.exports=require("react")},86842:e=>{e.exports=require("@restart/ui/SelectableContext")},87571:e=>{e.exports=require("dom-helpers/transitionEnd")},88751:e=>{e.exports=require("next-i18next")},93787:e=>{e.exports=require("redux-persist")},94947:e=>{e.exports=require("@restart/hooks/useTimeout")},96196:e=>{e.exports=require("next-redux-wrapper")},98320:e=>{e.exports=require("@restart/hooks/useIsomorphicEffect")},99460:e=>{e.exports=require("dom-helpers/css")}};var r=require("../../webpack-runtime.js");r.C(e);var s=e=>r(r.s=e),t=r.X(0,[6089,9216,9616,2386],()=>s(17384));module.exports=t})();