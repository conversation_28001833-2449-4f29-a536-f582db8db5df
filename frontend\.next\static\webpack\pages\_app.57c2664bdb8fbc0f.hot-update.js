"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/_app",{

/***/ "(pages-dir-browser)/./store.tsx":
/*!*******************!*\
  !*** ./store.tsx ***!
  \*******************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var redux__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! redux */ \"(pages-dir-browser)/./node_modules/redux/dist/redux.mjs\");\n/* harmony import */ var redux_persist__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! redux-persist */ \"(pages-dir-browser)/./node_modules/redux-persist/es/index.js\");\n/* harmony import */ var redux_persist_lib_storage__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! redux-persist/lib/storage */ \"(pages-dir-browser)/./node_modules/redux-persist/lib/storage/index.js\");\n/* harmony import */ var redux_saga__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! redux-saga */ \"(pages-dir-browser)/./node_modules/redux-saga/dist/redux-saga-core-npm-proxy.esm.js\");\n/* harmony import */ var _stores_userReducer__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./stores/userReducer */ \"(pages-dir-browser)/./stores/userReducer.tsx\");\n/* harmony import */ var _stores_permissionsReducer__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./stores/permissionsReducer */ \"(pages-dir-browser)/./stores/permissionsReducer.tsx\");\n/* harmony import */ var _saga__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./saga */ \"(pages-dir-browser)/./saga.tsx\");\n//Import Library\n\n\n\n\n// Fix for redux-persist SSR warning\nconst createNoopStorage = ()=>{\n    return {\n        getItem (_key) {\n            return Promise.resolve(null);\n        },\n        setItem (_key, value) {\n            return Promise.resolve(value);\n        },\n        removeItem (_key) {\n            return Promise.resolve();\n        }\n    };\n};\nconst persistStorage =  true ? redux_persist_lib_storage__WEBPACK_IMPORTED_MODULE_1__[\"default\"] : 0;\n//Import services/components\n\n\n\nconst rootReducer = (0,redux__WEBPACK_IMPORTED_MODULE_6__.combineReducers)({\n    user: _stores_userReducer__WEBPACK_IMPORTED_MODULE_3__[\"default\"],\n    permissions: _stores_permissionsReducer__WEBPACK_IMPORTED_MODULE_4__[\"default\"]\n});\nconst persistConfig = {\n    key: 'root',\n    storage: persistStorage\n};\nconst persistedReducer = (0,redux_persist__WEBPACK_IMPORTED_MODULE_0__.persistReducer)(persistConfig, rootReducer);\nconst bindMiddleware = (middleware)=>{\n    if (true) {\n        const { composeWithDevTools } = __webpack_require__(/*! @redux-devtools/extension */ \"(pages-dir-browser)/./node_modules/@redux-devtools/extension/lib/esm/index.js\");\n        return composeWithDevTools((0,redux__WEBPACK_IMPORTED_MODULE_6__.applyMiddleware)(...middleware));\n    }\n    return (0,redux__WEBPACK_IMPORTED_MODULE_6__.applyMiddleware)(...middleware);\n};\nfunction configureStore(initialState) {\n    const sagaMiddleware = (0,redux_saga__WEBPACK_IMPORTED_MODULE_2__[\"default\"])();\n    const store = (0,redux__WEBPACK_IMPORTED_MODULE_6__.createStore)(persistedReducer, initialState, bindMiddleware([\n        sagaMiddleware\n    ]));\n    store.sagaTask = sagaMiddleware.run(_saga__WEBPACK_IMPORTED_MODULE_5__[\"default\"]);\n    return store;\n}\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (configureStore);\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-browser)/./store.tsx\n"));

/***/ })

});