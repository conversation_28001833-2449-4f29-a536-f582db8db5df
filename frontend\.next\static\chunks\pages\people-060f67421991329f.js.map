{"version": 3, "file": "static/chunks/pages/people-060f67421991329f.js", "mappings": "0KAqCA,SAASA,EAASC,CAAoB,EACpC,GAAM,CAAEC,CAAC,CAAE,CAAGC,CAAAA,EAAAA,EAAAA,EAAAA,CAAcA,CAAC,UACvBC,EAA6B,CACjCC,gBAAiBH,EAAE,cACnB,EACI,SACJI,CAAO,MACPC,CAAI,WACJC,CAAS,uBACTC,CAAqB,WACrBC,CAAS,oBACTC,CAAkB,qBAClBC,CAAmB,kBACnBC,CAAgB,aAChBC,CAAW,oBACXC,CAAkB,gBAClBC,CAAc,SACdC,CAAO,WACPC,CAAS,sBACTC,CAAoB,mBACpBC,CAAiB,YACjBC,CAAU,QACVC,CAAM,kBACNC,CAAgB,cAChBC,CAAY,CAEZ,CADA,EACGC,EACJ,CAAGxB,EAGEyB,EAAiB,4BACrBtB,EACAuB,gBAAiBzB,EAAE,IAP0C,MAQ7D0B,UAAU,UACVtB,EACAC,KAAMA,GAAQ,EAAE,CAChBsB,MAAO,GACPC,2BAA4BrB,EAC5BsB,UAAWrB,EACXsB,gBAAiBf,qBACjBN,EACAsB,YAAY,EACZC,iBAAkBhB,EAClBiB,kBAAmBpB,GAA0C,GAC7DqB,eADwCrB,cACVD,GAA4B,CAAC,GAAI,GAAI,GAAI,CAA3BA,EAA+B,GAAG,CAC9EuB,oBAAqB7B,EACrB8B,oBAAqB1B,EACrB2B,aAAc1B,iBACdG,uBACAG,oBACAC,EACAoB,kBAAmB,UAACC,EAAAA,CAAYA,CAAAA,CAAAA,GAChCC,SAAU,UAACC,IAAAA,CAAEC,UAAU,6CACvBvB,SACAC,eACAE,mBACAD,EACAqB,UAAW,WACb,EACA,MACE,UAACC,EAAAA,EAASA,CAAAA,CAAE,GAAGnB,CAAc,EAEjC,CAEA1B,EAAS8C,YAAY,CAAG,CACtBf,WAAW,EACXE,YAAY,EACZzB,UAAW,KACXU,WAAW,EACXC,qBAAsB,KACtBC,kBAAmB,GACnBC,YAAY,EACZE,kBAAkB,CACpB,EAEA,MAAevB,QAAQA,EAAC,SC/GxB,4CACA,UACA,WACA,OAAe,EAAQ,KAAqC,CAC5D,EACA,SAFsB,2HCkLtB,MA7KA,SAAS+C,CAAuB,EAC5B,GAAM,CAACC,EAAWC,EAAe,CAAGC,CAAAA,EAAAA,EAAAA,EA4KzBH,MA5KyBG,CAAQA,CAAQ,EAAE,CA4KhCH,CA3KhB,CA2KiB,EA3Kf7C,CAAC,CAAE,CAAGC,CAAAA,EAAAA,EAAAA,EAAAA,CAAcA,CAAC,UACvB,CAACc,EAASkC,EAAW,CAAGD,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,EAAU,GAC1C,CAAC1C,EAAW4C,EAAa,CAAGF,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAS,GAC7C,CAACG,EAASC,EAAW,CAAGJ,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAS,IACzC,CAACK,EAAK,CAAGL,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAQ,EAAE,EAC3B,CAACM,EAAY,CAAGN,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAQ,EAAE,EAClC,CAACO,EAAYC,EAAc,CAAGR,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAS,IAC/C,CAACS,EAAUC,EAAY,CAAGV,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAM,MACxC,CAACzC,EAAuBoD,EAAyB,CAAGX,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,EAAU,GAEtEY,EAAkB,CACpBC,KAAM,CAAEC,WAAY,MAAO,EAC3BC,MAAOZ,EACPa,KAAM,EACNC,MAAO,CAAEC,OAAQ,CAAEC,IAAK,iBAAkB,EAAGC,cAAe,CAAED,IAAK,iBAAkB,CAAE,EACvFE,OAAQ,+dACZ,EAEMjE,EAAU,CACZ,CACIkE,KAAMtE,EAAE,wBACRuE,SAAU,WACVC,KAAM,GAAYC,EAAEC,QAAQ,CAC5BC,UAAU,CACd,EACA,CACIL,KAAMtE,EAAE,qBACRuE,SAAU,QACVC,KAAM,GAAYC,EAAEG,KAAK,CACzBD,UAAU,CACd,EACA,CACIL,KAAMtE,EAAE,oBACRuE,SAAU,QACVC,KAAM,GAAaC,EAAEI,KAAK,CAAGJ,EAAEI,KAAK,CAAC,EAAE,CAAG,GAC1CF,UAAU,CACd,EACA,CACIL,KAAMtE,EAAE,4BACRuE,SAAU,cACVC,KAAM,GAAaC,EAAEnB,WAAW,EAAImB,EAAEnB,WAAW,CAACwB,KAAK,CAAGL,EAAEnB,WAAW,CAACwB,KAAK,CAAG,GAChFH,UAAU,CACd,EACH,CAEKI,EAAc,MAAOC,IACvB/B,EAAW,IACX,IAAMgC,EAAW,MAAMC,EAAAA,CAAUA,CAACC,GAAG,CAAC,SAAUH,GAC5CC,GAAYG,MAAMC,OAAO,CAACJ,EAAS5E,IAAI,GAAG,CAC1C0C,EAAekC,EAAS5E,IAAI,EAC5B6C,EAAa+B,EAASK,UAAU,EAChCrC,GAAW,GAEnB,EAEMsC,EAAa,MAAOC,EAAaC,KACnCxC,GAAW,GACXW,EAAWC,IAAI,CAAG,CAEd,CAAC2B,EAAOjB,QAAQ,CAAC,CAAEkB,CACvB,EACA,MAAMV,EAAYnB,GAClBF,EAAYE,GACZX,GAAW,EACf,EASMvC,EAAsB,MAAOgF,EAAiB1B,KAChDJ,EAAWG,KAAK,CAAG2B,EACnB9B,EAAWI,IAAI,CAAGA,EAClBf,GAAW,GACX,IAAMgC,EAAW,MAAMC,EAAAA,CAAUA,CAACC,GAAG,CAAC,SAAUvB,GAC5CqB,GAAYG,MAAMC,OAAO,CAACJ,EAAS5E,IAAI,GAAG,CAC1C0C,EAAekC,EAAS5E,IAAI,EAC5B+C,EAAWsC,GACXzC,EAAW,IAEnB,EAEA0C,CAAAA,EAAAA,EAAAA,SAAAA,CAASA,CAAC,KACNZ,EAAYnB,EAChB,EAAG,EAAE,EAEL,IAAMgC,EAAyBC,EAAAA,OAAa,CAAC,KAQzC,IAAMC,EAAoB,IAClB7B,IACmB,GADZ,oBAEQ8B,IAAI,CAAC9B,EAAM+B,WAAW,IACjCpC,CADsC,CAC3BK,KAAK,CAAG,CAAE,GAAGL,EAAWK,KAAK,CAAOW,MAAOX,CAAQ,EAE9DL,EAAWK,KAAK,CAAG,CAAE,GAAGL,EAAWK,KAAK,CAAOS,SAAUT,CAAQ,GAIzEc,EAAYnB,GACZA,EAAWK,KAAK,CAAG,CAAEC,OAAQ,CAAEC,IAAK,iBAAkB,EAAGC,cAAe,CAAED,IAAK,iBAAkB,CAAE,CACvG,EAaM8B,EAAiB,KAEnBC,GACJ,EAEMA,EAAW,KACbJ,EAAkBvC,EACtB,EAQA,MACI,UAAC4C,EAAAA,OAAiBA,CAAAA,CACdC,SA5Ba,CA4BHC,GA3BVC,GAAKA,EAAEC,KAAK,EAAE,EACAD,EAAEC,KAAK,EACrBT,EAAkBQ,EAAEC,KAAK,IAEzB3C,EAAWK,KAAK,CAAG,CAAEC,OAAQ,CAAEC,IAAK,iBAAkB,EAAGC,cAAe,CAAED,IAAK,iBAAkB,CAAE,EACnGX,EAAc,IACduB,EAAYnB,GAEpB,EAoBQ4C,QAlDY,CAkDHC,IAjDTlD,IACAI,EAAyB,CAACpD,GAC1BiD,EAAc,IAEtB,EA8CQD,WAAYA,EACZsB,MAAOxB,EACPqD,eAAgBR,EAChBS,aAAcrD,EACdsD,WAdkBC,CAcNC,GAbE,SAAS,CAAvBD,EAAME,GAAG,EACTd,GAER,GAaJ,EAAG,CAAC1C,EAAW,EAEf,MACI,UAACyD,MAAAA,UACG,UAAClH,EAAAA,CAAQA,CAAAA,CACLM,QAASA,EACTC,KAAMyC,EACNxC,UAAWA,EACXE,SAAS,IACTa,gBAAgB,IAChBN,QAASA,EACTK,OAAQmE,EACRpE,UAAU,IACVH,WAAW,EACXT,sBAAuBA,EACvBE,mBAAoBmF,EACpBlF,oBAAqBA,EACrBC,iBAnGa,CAmGKA,GAlG1BiD,EAAWG,KAAK,CAAGZ,EACnBS,EAAWI,IAAI,CAAGA,EAClBP,IAAaG,EAAWC,IAAI,CAAGJ,CAAlBG,CAA2BC,IAAAA,EACxCkB,EAAYnB,EAChB,KAkGJ,gEChLe,SAASqD,EAAYlH,CAAuB,EACzD,MACE,UAACmH,KAAAA,CAAGxE,UAAU,wBAAgB3C,EAAM+E,KAAK,EAE7C,yJCgDA,MA/C0B,OAAC,CAAEvB,YAAU,QA+CxB4C,EA/C0BC,CAAQ,SAAEI,CAAO,IA+C1BL,EAAC,CA/C2BtB,CAAK,gBAAE6B,CAAc,cAAEC,CAAY,YAAEC,CAAU,CAA0H,GAC3N,GAAE5G,CAAC,CAAE,CAAGC,CAAAA,EAAAA,EAAAA,EAAAA,CAAcA,CAAC,UACvB,CAACkH,EAAMC,EAAQ,CAAGpE,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAC,EAAE,EAC7B,EAAGC,EAAW,CAAGD,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,EAAC,GAE1BY,EAAa,CACfC,KAAM,CAAEC,WAAY,MAAO,EAC3BC,MAAO,IACPC,KAAM,EACNC,MAAO,CAAC,EACRI,OAAQ,+dACZ,EAEMU,EAAc,UAChB9B,GAAW,GACX,IAAMgC,EAAW,MAAMC,EAAAA,CAAUA,CAACC,GAAG,CAAC,SAAUvB,GAC5CqB,GAAYG,MAAMC,OAAO,CAACJ,EAAS5E,IAAI,GAAG,CAI1C+G,EAHenC,EAAS5E,IAAI,CAACgH,GAAG,CAAC,CAACC,EAAWC,KAClC,CAAEhB,MAAOe,EAAK5C,QAAQ,CAAE8C,MAAOF,EAAKG,GAAG,CAAC,IAGnDxE,GAAW,GAEnB,EAKA,MAHA0C,CAAAA,EAAAA,EAAAA,SAAAA,CAASA,CAAC,KACNZ,GACJ,EAAG,EAAE,EAED,UAAC2C,EAAAA,CAASA,CAAAA,CAACC,KAAK,IAACjF,UAAU,eACvB,UAACkF,EAAAA,CAAGA,CAAAA,UACA,UAACC,EAAAA,CAAGA,CAAAA,CAACC,GAAI,GAAIC,GAAI,EAAGC,GAAI,EAAGtF,UAAU,oBACjC,UAACuF,EAAAA,EAAMA,CAAAA,CACHC,UAAW,GACXC,aAAa,EACbC,cAAc,EACdC,UAAWzB,EACX0B,SAAUlC,EACVmC,YAAavI,EAAE,+BACfwI,QAASrB,SAMjC,+KClBA,MA1Be,KACb,GAAM,CAAEnH,GAAC,CAAE,CAAGC,CAAAA,EAAAA,EAAAA,EAAAA,CAAcA,CAAC,KAyBhBwI,KAxBb,CAwBmBA,EAAC,GAvBlB,WAACf,EAAAA,CAASA,CAAAA,CAACC,KAAK,IAACjF,UAAU,gBACzB,UAACkF,EAAAA,CAAGA,CAAAA,UACF,UAACC,EAAAA,CAAGA,CAAAA,CAACC,GAAI,YACP,UAACb,EAAAA,CAAWA,CAAAA,CAACnC,MAAO9E,EAAE,qBAG1B,UAAC4H,EAAAA,CAAGA,CAAAA,CAAClF,UAAU,gBACb,UAACmF,EAAAA,CAAGA,CAAAA,CAACC,GAAI,YACP,UAACjF,EAAAA,OAAWA,CAAAA,CAAAA,SAKtB", "sources": ["webpack://_N_E/./components/common/RKITable.tsx", "webpack://_N_E/?de35", "webpack://_N_E/./pages/people/peopleTable.tsx", "webpack://_N_E/./components/common/PageHeading.tsx", "webpack://_N_E/./pages/people/peopleTableFilter.tsx", "webpack://_N_E/./pages/people/index.tsx"], "sourcesContent": ["//Import Library\r\nimport React from 'react'\r\nimport DataTable  from 'react-data-table-component';\r\n\r\n//Import services/components\r\nimport { useTranslation } from 'next-i18next';\r\nimport CustomLoader from './CustomLoader';\r\n\r\ninterface RKITableProps {\r\n  columns: any[];\r\n  data: any[];\r\n  totalRows?: number;\r\n  resetPaginationToggle?: boolean;\r\n  subheader?: boolean;\r\n  subHeaderComponent?: React.ReactNode;\r\n  handlePerRowsChange?: (newPerPage: number, page: number) => void;\r\n  handlePageChange?: (page: number) => void;\r\n  rowsPerPage?: number[];\r\n  defaultRowsPerPage?: number;\r\n  selectableRows?: boolean;\r\n  loading?: boolean;\r\n  pagServer?: boolean;\r\n  onSelectedRowsChange?: (selectedRows: any) => void;\r\n  clearSelectedRows?: boolean;\r\n  sortServer?: boolean;\r\n  onSort?: (column: any, sortDirection: string) => void;\r\n  persistTableHead?: boolean;\r\n  sortFunction?: (rows: any[], field: any, direction: 'asc' | 'desc') => any[];\r\n  noHeader?: boolean;\r\n  dense?: boolean;\r\n  pagination?: boolean;\r\n  paginationServer?: boolean;\r\n  paginationTotalRows?: number;\r\n  subHeaderAlign?: string;\r\n  [key: string]: any;\r\n}\r\n\r\nfunction RKITable(props: RKITableProps) {\r\n  const { t } = useTranslation('common');\r\n  const paginationComponentOptions = {\r\n    rowsPerPageText: t('Rowsperpage'),\r\n    };\r\n  const {\r\n    columns,\r\n    data,\r\n    totalRows,\r\n    resetPaginationToggle,\r\n    subheader,\r\n    subHeaderComponent,\r\n    handlePerRowsChange,\r\n    handlePageChange,\r\n    rowsPerPage,\r\n    defaultRowsPerPage,\r\n    selectableRows,\r\n    loading,\r\n    pagServer,\r\n    onSelectedRowsChange,\r\n    clearSelectedRows,\r\n    sortServer,\r\n    onSort,\r\n    persistTableHead,\r\n    sortFunction,\r\n    // Filter out any unknown props that might cause DOM warnings\r\n    ...otherProps\r\n  } = props;\r\n\r\n  // Only pass known DataTable props to avoid DOM warnings\r\n  const dataTableProps = {\r\n    paginationComponentOptions,\r\n    noDataComponent: t(\"NoData\"),\r\n    noHeader: true,\r\n    columns,\r\n    data: data || [],\r\n    dense: true,\r\n    paginationResetDefaultPage: resetPaginationToggle,\r\n    subHeader: subheader,\r\n    progressPending: loading,\r\n    subHeaderComponent,\r\n    pagination: true,\r\n    paginationServer: pagServer,\r\n    paginationPerPage: defaultRowsPerPage ? defaultRowsPerPage : 10,\r\n    paginationRowsPerPageOptions: rowsPerPage ? rowsPerPage : [10, 15, 20, 25, 30],\r\n    paginationTotalRows: totalRows,\r\n    onChangeRowsPerPage: handlePerRowsChange,\r\n    onChangePage: handlePageChange,\r\n    selectableRows,\r\n    onSelectedRowsChange,\r\n    clearSelectedRows,\r\n    progressComponent: <CustomLoader/>,\r\n    sortIcon: <i className=\"sort-icon fas fa-exchange-alt\" />,\r\n    sortServer,\r\n    onSort,\r\n    sortFunction,\r\n    persistTableHead,\r\n    className: \"rki-table\"\r\n  };\r\n  return (\r\n    <DataTable {...dataTableProps} />\r\n  )\r\n};\r\n\r\nRKITable.defaultProps = {\r\n  subHeader: false,\r\n  pagination: true,\r\n  totalRows: null,\r\n  pagServer: true,\r\n  onSelectedRowsChange: null,\r\n  clearSelectedRows: false,\r\n  sortServer: false,\r\n  persistTableHead: false\r\n}\r\n\r\nexport default RKITable;\r\n\r\n", "\n    (window.__NEXT_P = window.__NEXT_P || []).push([\n      \"/people\",\n      function () {\n        return require(\"private-next-pages/people/index.tsx\");\n      }\n    ]);\n    if(module.hot) {\n      module.hot.dispose(function () {\n        window.__NEXT_P.push([\"/people\"])\n      });\n    }\n  ", "//Import Library\r\nimport React, { ChangeEvent, useEffect, useState } from \"react\";\r\n\r\n//Import services/components\r\nimport PeopleTableFilter from \"./peopleTableFilter\";\r\nimport RKITable from \"../../components/common/RKITable\";\r\nimport apiService from \"../../services/apiService\";\r\nimport { useTranslation } from 'next-i18next';\r\n\r\nfunction PeopleTable(_props: any) {\r\n    const [tabledata, setDataToTable] = useState<any[]>([]);\r\n    const { t } = useTranslation('common');\r\n    const [loading, setLoading] = useState<boolean>(false);\r\n    const [totalRows, setTotalRows] = useState<number>(0);\r\n    const [perPage, setPerPage] = useState<number>(10);\r\n    const [role] = useState<any[]>([]);\r\n    const [institution] = useState<any[]>([]);\r\n    const [filterText, setFilterText] = useState<string>(\"\");\r\n    const [pageSort, setPageSort] = useState<any>(null);\r\n    const [resetPaginationToggle, setResetPaginationToggle] = useState<boolean>(false);\r\n\r\n    const userParams: any = {\r\n        sort: { created_at: \"desc\" },\r\n        limit: perPage,\r\n        page: 1,\r\n        query: { status: { $ne: \"Request Pending\" }, vspace_status: { $ne: \"Request Pending\" } },\r\n        select: \"-country -dial_code -firstname -region -institution.address -institution.contact_name -institution.description -institution.dial_code -institution.document -institution.doc_src -institution.images -institution.images_src -institution.expertise -institution.hazards -institution.hazard_types -institution.header -institution.networks -institution.partners -institution.telephone -institution.twitter -institution.type -institution.unit -institution.user -mobile_number -position\",\r\n    };\r\n\r\n    const columns = [\r\n        {\r\n            name: t(\"People.form.Username\"),\r\n            selector: \"username\",\r\n            cell: (d: any) => d.username,\r\n            sortable: true,\r\n        },\r\n        {\r\n            name: t(\"People.form.Email\"),\r\n            selector: \"email\",\r\n            cell: (d: any) => d.email,\r\n            sortable: true,\r\n        },\r\n        {\r\n            name: t(\"People.form.Role\"),\r\n            selector: \"roles\",\r\n            cell: (d: any) => (d.roles ? d.roles[0] : \"\"),\r\n            sortable: true,\r\n        },\r\n        {\r\n            name: t(\"People.form.Organisation\"),\r\n            selector: \"institution\",\r\n            cell: (d: any) => (d.institution && d.institution.title ? d.institution.title : \"\"),\r\n            sortable: true,\r\n        },\r\n    ];\r\n\r\n    const getUserData = async (userParamsinit: any) => {\r\n        setLoading(true);\r\n        const response = await apiService.get(\"/users\", userParamsinit);\r\n        if (response && Array.isArray(response.data)) {\r\n            setDataToTable(response.data);\r\n            setTotalRows(response.totalCount);\r\n            setLoading(false);\r\n        }\r\n    };\r\n\r\n    const handleSort = async (column: any, sortDirection: any) => {\r\n        setLoading(true);\r\n        userParams.sort = {\r\n            // ...userParams.sort,\r\n            [column.selector]: sortDirection,\r\n        };\r\n        await getUserData(userParams);\r\n        setPageSort(userParams);\r\n        setLoading(false);\r\n    };\r\n\r\n    const handlePageChange = (page: any) => {\r\n        userParams.limit = perPage;\r\n        userParams.page = page;\r\n        pageSort && (userParams.sort = pageSort.sort);\r\n        getUserData(userParams);\r\n    };\r\n\r\n    const handlePerRowsChange = async (newPerPage: any, page: any) => {\r\n        userParams.limit = newPerPage;\r\n        userParams.page = page;\r\n        setLoading(true);\r\n        const response = await apiService.get(\"/users\", userParams);\r\n        if (response && Array.isArray(response.data)) {\r\n            setDataToTable(response.data);\r\n            setPerPage(newPerPage);\r\n            setLoading(false);\r\n        }\r\n    };\r\n\r\n    useEffect(() => {\r\n        getUserData(userParams);\r\n    }, []);\r\n\r\n    const subHeaderComponentMemo = React.useMemo(() => {\r\n        const handleClear = () => {\r\n            if (filterText) {\r\n                setResetPaginationToggle(!resetPaginationToggle);\r\n                setFilterText(\"\");\r\n            }\r\n        };\r\n\r\n        const handleSearchTitle = (query: string) => {\r\n            if (query) {\r\n                const emailRegex = /^[^@]+@[^@]+\\.[^@]+$/;\r\n                if (emailRegex.test(query.toLowerCase())) {\r\n                    userParams.query = { ...userParams.query, ...{ email: query } };\r\n                } else {\r\n                    userParams.query = { ...userParams.query, ...{ username: query } };\r\n                }\r\n            }\r\n\r\n            getUserData(userParams);\r\n            userParams.query = { status: { $ne: \"Request Pending\" }, vspace_status: { $ne: \"Request Pending\" } };\r\n        };\r\n\r\n        const handleChange = (e: any) => {\r\n            if (e && e.label) {\r\n                setFilterText(e.label);\r\n                handleSearchTitle(e.label);\r\n            } else {\r\n                userParams.query = { status: { $ne: \"Request Pending\" }, vspace_status: { $ne: \"Request Pending\" } };\r\n                setFilterText(\"\");\r\n                getUserData(userParams);\r\n            }\r\n        };\r\n\r\n        const handleKeypress = () => {\r\n            //it triggers by pressing the enter ke\r\n            onSearch();\r\n        };\r\n\r\n        const onSearch = () => {\r\n            handleSearchTitle(filterText);\r\n        };\r\n\r\n        const userdataKeypress = (event: any) => {\r\n            if (event.key === \"Enter\") {\r\n                handleKeypress();\r\n            }\r\n        };\r\n\r\n        return (\r\n            <PeopleTableFilter\r\n                onFilter={handleChange}\r\n                onClear={handleClear}\r\n                filterText={filterText}\r\n                roles={role}\r\n                onHandleSearch={onSearch}\r\n                institutions={institution}\r\n                onKeyPress={userdataKeypress}\r\n            />\r\n        );\r\n    }, [filterText]);\r\n\r\n    return (\r\n        <div>\r\n            <RKITable\r\n                columns={columns}\r\n                data={tabledata}\r\n                totalRows={totalRows}\r\n                subheader\r\n                persistTableHead\r\n                loading={loading}\r\n                onSort={handleSort}\r\n                sortServer\r\n                pagServer={true}\r\n                resetPaginationToggle={resetPaginationToggle}\r\n                subHeaderComponent={subHeaderComponentMemo}\r\n                handlePerRowsChange={handlePerRowsChange}\r\n                handlePageChange={handlePageChange}\r\n            />\r\n        </div>\r\n    );\r\n}\r\n\r\nexport default PeopleTable;\r\n", "interface PageHeadingProps {\r\n  title: string; // Required based on actual usage\r\n}\r\n\r\nexport default function PageHeading(props: PageHeadingProps) {\r\n  return (\r\n    <h2 className=\"page-heading\">{props.title}</h2>\r\n  )\r\n}\r\n", "//Import Library\r\nimport { Col, Container, Row } from \"react-bootstrap\";\r\nimport Select from \"react-select\";\r\nimport { useEffect, useState } from \"react\";\r\n\r\n//Import services/components\r\nimport apiService from \"../../services/apiService\";\r\nimport { useTranslation } from 'next-i18next';\r\n\r\nconst PeopleTableFilter = ({ filterText, onFilter, onClear, roles, onHandleSearch, institutions, onKeyPress } : { filterText: any, onFilter: any, onClear: any, roles: any, onHandleSearch: any, institutions: any, onKeyPress: any }) => {\r\n    const { t } = useTranslation('common');\r\n    const [user, setUser] = useState([]);\r\n    const [, setLoading] = useState(false);\r\n\r\n    const userParams = {\r\n        sort: { created_at: \"desc\" },\r\n        limit: \"~\",\r\n        page: 1,\r\n        query: {},\r\n        select: \"-country -dial_code -firstname -region -institution.address -institution.contact_name -institution.description -institution.dial_code -institution.document -institution.doc_src -institution.images -institution.images_src -institution.expertise -institution.hazards -institution.hazard_types -institution.header -institution.networks -institution.partners -institution.telephone -institution.twitter -institution.type -institution.unit -institution.user -mobile_number -position\",\r\n    };\r\n\r\n    const getUserData = async () => {\r\n        setLoading(true);\r\n        const response = await apiService.get(\"/users\", userParams);\r\n        if (response && Array.isArray(response.data)) {\r\n            const _users = response.data.map((item: any, _i: any) => {\r\n                return { label: item.username, value: item._id };\r\n            });\r\n            setUser(_users);\r\n            setLoading(false);\r\n        }\r\n    };\r\n\r\n    useEffect(() => {\r\n        getUserData();\r\n    }, []);\r\n    return (\r\n        <Container fluid className=\"p-0\">\r\n            <Row>\r\n                <Col xs={12} md={6} lg={4} className=\"p-0 me-3\">\r\n                    <Select\r\n                        autoFocus={true}\r\n                        isClearable={true}\r\n                        isSearchable={true}\r\n                        onKeyDown={onKeyPress}\r\n                        onChange={onFilter}\r\n                        placeholder={t(\"People.form.UsernameorEmail\")}\r\n                        options={user}\r\n                    />\r\n                </Col>\r\n            </Row>\r\n        </Container>\r\n    );\r\n};\r\n\r\nexport default PeopleTableFilter;\r\n", "//Import Library\r\nimport React from \"react\";\r\nimport { Container, Col, Row } from \"react-bootstrap\";\r\n\r\n//Import services/components\r\nimport PeopleTable from \"./peopleTable\";\r\nimport PageHeading from \"../../components/common/PageHeading\";\r\nimport { useTranslation } from 'next-i18next';\r\nimport { serverSideTranslations } from 'next-i18next/serverSideTranslations';\r\n\r\nconst People = () => {\r\n  const { t } = useTranslation('common');\r\n  return (\r\n    <Container fluid className=\"p-0\">\r\n      <Row>\r\n        <Col xs={12}>\r\n          <PageHeading title={t(\"menu.people\")} />\r\n        </Col>\r\n      </Row>\r\n      <Row className=\"mt-3\">\r\n        <Col xs={12}>\r\n          <PeopleTable />\r\n        </Col>\r\n      </Row>\r\n    </Container>\r\n  );\r\n};\r\n\r\nexport async function getStaticProps({ locale } : { locale: string }) {\r\n  return {\r\n    props: {\r\n      ...(await serverSideTranslations(locale, ['common'])),\r\n    },\r\n  }\r\n}\r\n\r\nexport default People;\r\n"], "names": ["RKITable", "props", "t", "useTranslation", "paginationComponentOptions", "rowsPerPageText", "columns", "data", "totalRows", "resetPaginationToggle", "subheader", "subHeaderComponent", "handlePerRowsChange", "handlePageChange", "rowsPerPage", "defaultRowsPerPage", "selectableRows", "loading", "pagServer", "onSelectedRowsChange", "clearSelectedRows", "sortServer", "onSort", "persistTableHead", "sortFunction", "otherProps", "dataTableProps", "noDataComponent", "<PERSON><PERSON><PERSON><PERSON>", "dense", "paginationResetDefaultPage", "subHeader", "progressPending", "pagination", "paginationServer", "paginationPerPage", "paginationRowsPerPageOptions", "paginationTotalRows", "onChangeRowsPerPage", "onChangePage", "progressComponent", "CustomLoader", "sortIcon", "i", "className", "DataTable", "defaultProps", "PeopleTable", "tabledata", "setDataToTable", "useState", "setLoading", "setTotalRows", "perPage", "setPerPage", "role", "institution", "filterText", "setFilterText", "pageSort", "setPageSort", "setResetPaginationToggle", "userParams", "sort", "created_at", "limit", "page", "query", "status", "$ne", "vspace_status", "select", "name", "selector", "cell", "d", "username", "sortable", "email", "roles", "title", "getUserData", "userParamsinit", "response", "apiService", "get", "Array", "isArray", "totalCount", "handleSort", "column", "sortDirection", "newPerPage", "useEffect", "subHeaderComponentMemo", "React", "handleSearchTitle", "test", "toLowerCase", "handleKeypress", "onSearch", "PeopleTableFilter", "onFilter", "handleChange", "e", "label", "onClear", "handleClear", "onHandleSearch", "institutions", "onKeyPress", "event", "userdataKeypress", "key", "div", "PageHeading", "h2", "user", "setUser", "map", "item", "_i", "value", "_id", "Container", "fluid", "Row", "Col", "xs", "md", "lg", "Select", "autoFocus", "isClearable", "isSearchable", "onKeyDown", "onChange", "placeholder", "options", "People"], "sourceRoot": "", "ignoreList": []}