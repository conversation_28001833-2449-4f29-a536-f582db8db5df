(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[3554],{34381:(e,a,t)=>{"use strict";t.d(a,{A:()=>d});var s=t(37876),n=t(14232),r=t(48230),o=t.n(r),i=t(50749),c=t(53718),l=t(31753);let d=e=>{let{t:a}=(0,l.Bd)("common"),{type:t,id:r}=e,[d,u]=(0,n.useState)([]),[p,g]=(0,n.useState)(!1),[m,h]=(0,n.useState)(0),[v,P]=(0,n.useState)(10),[w]=(0,n.useState)(!1),f={sort:{created_at:"asc"},limit:v,page:1,query:{}},j=[{name:a("Title"),selector:"title",cell:e=>e&&e.title&&e._id?(0,s.jsx)(o(),{href:"/vspace/[...routes]",as:"/vspace/show/".concat(e._id),children:e.title}):""},{name:a("Owner"),selector:"users",cell:e=>e&&e.user&&e.user.firstname?"".concat(e.user.firstname," ").concat(e.user.lastname):""},{name:a("PublicPrivate"),selector:"visibility",cell:e=>e&&e.visibility?"Public":"Private"},{name:a("NumberofMembers"),selector:"members",cell:e=>e&&e.members?e.members.length:"-"}],x=async e=>{g(!0);let a=await c.A.get("stats/get".concat(t,"WithVspace/").concat(r),f);a&&("Operation"===t?u(a.operation):u(a.project),h(a.totalCount),g(!1))},b=async(e,a)=>{f.limit=e,f.page=a,g(!0);let s=await c.A.get("stats/get".concat(t,"WithVspace/").concat(r),f);s&&("Operation"===t?u(s.operation):u(s.project),P(e),g(!1))};return(0,n.useEffect)(()=>{x(f)},[]),(0,s.jsx)("div",{children:(0,s.jsx)(i.A,{columns:j,data:d,totalRows:m,loading:p,resetPaginationToggle:w,handlePerRowsChange:b,handlePageChange:e=>{f.limit=v,f.page=e,x(f)}})})}},50749:(e,a,t)=>{"use strict";t.d(a,{A:()=>c});var s=t(37876);t(14232);var n=t(89773),r=t(31753),o=t(5507);function i(e){let{t:a}=(0,r.Bd)("common"),t={rowsPerPageText:a("Rowsperpage")},{columns:i,data:c,totalRows:l,resetPaginationToggle:d,subheader:u,subHeaderComponent:p,handlePerRowsChange:g,handlePageChange:m,rowsPerPage:h,defaultRowsPerPage:v,selectableRows:P,loading:w,pagServer:f,onSelectedRowsChange:j,clearSelectedRows:x,sortServer:b,onSort:_,persistTableHead:A,sortFunction:S,...C}=e,R={paginationComponentOptions:t,noDataComponent:a("NoData"),noHeader:!0,columns:i,data:c||[],dense:!0,paginationResetDefaultPage:d,subHeader:u,progressPending:w,subHeaderComponent:p,pagination:!0,paginationServer:f,paginationPerPage:v||10,paginationRowsPerPageOptions:h||[10,15,20,25,30],paginationTotalRows:l,onChangeRowsPerPage:g,onChangePage:m,selectableRows:P,onSelectedRowsChange:j,clearSelectedRows:x,progressComponent:(0,s.jsx)(o.A,{}),sortIcon:(0,s.jsx)("i",{className:"sort-icon fas fa-exchange-alt"}),sortServer:b,onSort:_,sortFunction:S,persistTableHead:A,className:"rki-table"};return(0,s.jsx)(n.Ay,{...R})}i.defaultProps={subHeader:!1,pagination:!0,totalRows:null,pagServer:!0,onSelectedRowsChange:null,clearSelectedRows:!1,sortServer:!1,persistTableHead:!1};let c=i},52156:(e,a,t)=>{(window.__NEXT_P=window.__NEXT_P||[]).push(["/project/components/VirtualSpaceAccordion",function(){return t(54001)}])},54001:(e,a,t)=>{"use strict";t.r(a),t.d(a,{default:()=>d});var s=t(37876),n=t(14232),r=t(11041),o=t(21772),i=t(32890),c=t(34381),l=t(31753);let d=e=>{var a,t;let{t:d}=(0,l.Bd)("common"),[u,p]=(0,n.useState)(!1);return(0,s.jsx)(s.Fragment,{children:(0,s.jsxs)(i.A.Item,{eventKey:"0",children:[(0,s.jsxs)(i.A.Header,{onClick:()=>p(!u),children:[(0,s.jsx)("div",{className:"cardTitle",children:d("LinkedVirtualSpace")}),(0,s.jsx)("div",{className:"cardArrow",children:u?(0,s.jsx)(o.g,{icon:r.EZy,color:"#fff"}):(0,s.jsx)(o.g,{icon:r.QLR,color:"#fff"})})]}),(0,s.jsx)(i.A.Body,{children:(0,s.jsx)(c.A,{id:(null==(t=e.routeData)||null==(a=t.routes)?void 0:a[1])||"",type:"Project",vspaceData:[],vspaceDataLoading:!1,vspaceDataTotalRows:0,vspaceDataPerPage:10,vspaceDataCurrentPage:1})})]})})}}},e=>{var a=a=>e(e.s=a);e.O(0,[7725,9773,1772,636,6593,8792],()=>a(52156)),_N_E=e.O()}]);
//# sourceMappingURL=VirtualSpaceAccordion-4817728ae077ca35.js.map