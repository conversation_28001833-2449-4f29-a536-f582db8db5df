"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[9379],{5671:(e,t,i)=>{i.d(t,{x:()=>l});var n=i(37876),a=i(14232);let s=e=>{let{value:t,onChange:i,placeholder:s="Write something...",height:l=300,disabled:r=!1}=e,d=(0,a.useRef)(null),[o,c]=(0,a.useState)(!1);(0,a.useEffect)(()=>{d.current&&1&&!o&&d.current.innerHTML!==t&&(d.current.innerHTML=t||"")},[t,o]);let p=()=>{d.current&&i&&i(d.current.innerHTML)},u=(e,t)=>{if("undefined"!=typeof document){var i;document.execCommand(e,!1,t||""),p(),null==(i=d.current)||i.focus()}};return(0,n.jsx)("div",{className:"simple-rich-text-editor",style:{border:"1px solid #ccc"},children:(0,n.jsxs)(n.Fragment,{children:[(0,n.jsxs)("div",{className:"toolbar",style:{padding:"8px",borderBottom:"1px solid #ccc",background:"#f5f5f5"},children:[(0,n.jsx)("button",{type:"button",onClick:()=>u("bold"),style:{margin:"0 5px",padding:"3px 8px"},children:(0,n.jsx)("strong",{children:"B"})}),(0,n.jsx)("button",{type:"button",onClick:()=>u("italic"),style:{margin:"0 5px",padding:"3px 8px"},children:(0,n.jsx)("em",{children:"I"})}),(0,n.jsx)("button",{type:"button",onClick:()=>u("underline"),style:{margin:"0 5px",padding:"3px 8px"},children:(0,n.jsx)("u",{children:"U"})}),(0,n.jsx)("button",{type:"button",onClick:()=>u("insertOrderedList"),style:{margin:"0 5px",padding:"3px 8px"},children:"OL"}),(0,n.jsx)("button",{type:"button",onClick:()=>u("insertUnorderedList"),style:{margin:"0 5px",padding:"3px 8px"},children:"UL"}),(0,n.jsx)("button",{type:"button",onClick:()=>{let e=prompt("Enter the link URL");e&&u("createLink",e)},style:{margin:"0 5px",padding:"3px 8px"},children:"Link"})]}),(0,n.jsx)("div",{ref:d,contentEditable:!r,onInput:p,onFocus:()=>c(!0),onBlur:()=>c(!1),style:{padding:"15px",minHeight:l,maxHeight:2*l,overflow:"auto",outline:"none"},"data-placeholder":t?"":s,suppressContentEditableWarning:!0})]})})},l=e=>{let{initContent:t,onChange:i}=e;return(0,n.jsx)(s,{value:t||"",onChange:e=>i(e)})}},35611:(e,t,i)=>{i.d(t,{sx:()=>o,s3:()=>a.s3,ks:()=>a.ks,yk:()=>n.A});var n=i(54773),a=i(59200),s=i(37876),l=i(14232),r=i(39593),d=i(29504);let o={RadioGroup:e=>{let{name:t,valueSelected:i,onChange:n,errorMessage:a,children:d}=e,{errors:o,touched:c}=(0,r.j7)(),p=c[t]&&o[t];l.useMemo(()=>({name:t}),[t]);let u=l.Children.map(d,e=>l.isValidElement(e)&&function(e){return"object"==typeof e&&null!==e}(e.props)?l.cloneElement(e,{name:t,...e.props}):e);return(0,s.jsxs)("div",{children:[(0,s.jsx)("div",{className:"radio-group",children:u}),p&&(0,s.jsx)("div",{className:"invalid-feedback d-block",children:a||("string"==typeof o[t]?o[t]:String(o[t]))})]})},RadioItem:e=>{let{id:t,label:i,value:n,name:a,disabled:l}=e,{values:o,setFieldValue:c}=(0,r.j7)(),p=a||t;return(0,s.jsx)(d.A.Check,{type:"radio",id:t,label:i,value:n,name:p,checked:o[p]===n,onChange:e=>{c(p,e.target.value)},disabled:l,inline:!0})}};n.A,a.ks,a.s3},54773:(e,t,i)=>{i.d(t,{A:()=>d});var n=i(37876),a=i(14232),s=i(39593),l=i(91408);let r=(0,a.forwardRef)((e,t)=>{let{children:i,onSubmit:a,autoComplete:r,className:d,onKeyPress:o,initialValues:c,...p}=e,u=l.Ik().shape({});return(0,n.jsx)(s.l1,{initialValues:c||{},validationSchema:u,onSubmit:(e,t)=>{let i={preventDefault:()=>{},stopPropagation:()=>{},currentTarget:null,target:null,nativeEvent:new Event("submit"),bubbles:!1,cancelable:!0,defaultPrevented:!1,eventPhase:0,isTrusted:!1,timeStamp:Date.now(),type:"submit",isDefaultPrevented:()=>!1,isPropagationStopped:()=>!1,persist:()=>{}};a&&a(i,e,t)},...p,children:e=>(0,n.jsx)(s.lV,{ref:t,onSubmit:e.handleSubmit,autoComplete:r,className:d,onKeyPress:o,children:"function"==typeof i?i(e):i})})});r.displayName="ValidationFormWrapper";let d=r},59200:(e,t,i)=>{i.d(t,{ks:()=>l,s3:()=>r});var n=i(37876);i(14232);var a=i(29504),s=i(39593);let l=e=>{let{name:t,id:i,required:l,validator:r,errorMessage:d,onChange:o,value:c,as:p,multiline:u,rows:m,pattern:g,...x}=e;return(0,n.jsx)(s.D0,{name:t,validate:e=>{let t="string"==typeof e?e:String(e||"");return l&&(!e||""===t.trim())?(null==d?void 0:d.validator)||"This field is required":r&&!r(e)?(null==d?void 0:d.validator)||"Invalid value":g&&e&&!new RegExp(g).test(e)?(null==d?void 0:d.pattern)||"Invalid format":void 0},children:e=>{let{field:t,meta:s}=e;return(0,n.jsxs)(n.Fragment,{children:[(0,n.jsx)(a.A.Control,{...t,...x,id:i,as:p||"input",rows:m,isInvalid:s.touched&&!!s.error,onChange:e=>{t.onChange(e),o&&o(e)},value:void 0!==c?c:t.value}),s.touched&&s.error?(0,n.jsx)(a.A.Control.Feedback,{type:"invalid",children:s.error}):null]})}})},r=e=>{let{name:t,id:i,required:l,errorMessage:r,onChange:d,value:o,children:c,...p}=e;return(0,n.jsx)(s.D0,{name:t,validate:e=>{if(l&&(!e||""===e))return(null==r?void 0:r.validator)||"This field is required"},children:e=>{let{field:t,meta:s}=e;return(0,n.jsxs)(n.Fragment,{children:[(0,n.jsx)(a.A.Control,{as:"select",...t,...p,id:i,isInvalid:s.touched&&!!s.error,onChange:e=>{t.onChange(e),d&&d(e)},value:void 0!==o?o:t.value,children:c}),s.touched&&s.error?(0,n.jsx)(a.A.Control.Feedback,{type:"invalid",children:s.error}):null]})}})}},89673:(e,t,i)=>{i.d(t,{A:()=>A});var n=i(37876),a=i(14232),s=i(17336),l=i(21772),r=i(11041),d=i(37784),o=i(29504),c=i(60282),p=i(31195),u=i(82851),m=i.n(u),g=i(97685),x=i(53718),h=i(31753);let f=[],j={flex:1,display:"flex",flexDirection:"column",alignItems:"center",justifyContent:"center",width:"100%",height:"100%",borderWidth:.1,borderColor:"#fafafa",backgroundColor:"#fafafa",color:"black",transition:"border  .24s ease-in-out",padding:"15px"},v={display:"flex",padding:"10px",width:"100%",border:"2px solid gray",flexDirection:"column",justifyContent:"flex-start",flexWrap:"wrap",marginTop:20},b={width:"150px"},y={borderColor:"#2196f3"},A=e=>{let t,{t:i}=(0,h.Bd)("common"),[u,A]=(0,a.useState)(!1),[C,w]=(0,a.useState)(),k="application"==e.type?0x1400000:"20971520",[E,I]=(0,a.useState)([]),[N,S]=(0,a.useState)(!0),[_,D]=(0,a.useState)([]),L=e&&"application"===e.type?"/files":"/image",P=async e=>{await x.A.remove("".concat(L,"/").concat(e))},F=e=>{w(e),A(!0)},T=(e,t)=>{let i=[..._];i[t]=e.target.value,D(i)},U=t=>{switch(t&&t.name.split(".").pop()){case"JPG":case"jpg":case"jpeg":case"jpg":case"png":return(0,n.jsx)("img",{src:t.preview,style:b});case"pdf":return(0,n.jsx)("img",{src:"/images/fileIcons/pdfFile.png",className:"application"===e.type?"docPreview":"imgPreview"});case"docx":default:return(0,n.jsx)("img",{src:"/images/fileIcons/wordFile.png",className:"application"===e.type?"docPreview":"imgPreview"});case"xls":case"xlsx":return(0,n.jsx)("img",{src:"/images/fileIcons/xlsFile.png",className:"application"===e.type?"docPreview":"imgPreview"})}},R=()=>A(!1),B=()=>{A(!1)},O=t=>{let i=(t=C)&&t._id?{serverID:t._id}:{file:t},n=m().findIndex(f,i),a=[..._];a.splice(n,1),D(a),P(f[n].serverID),f.splice(n,1),e.getImgID(f,e.index?e.index:0);let s=[...E];s.splice(s.indexOf(t),1),I(s),A(!1)},z=E.map((t,a)=>(0,n.jsxs)("div",{children:[(0,n.jsx)(d.A,{xs:12,children:(0,n.jsxs)("div",{className:"row",children:[(0,n.jsx)(d.A,{md:4,lg:3,className:"application text-center align-self-center"===e.type?"docImagePreview text-center align-self-center":"imgPreview text-center align-self-center",children:U(t)}),(0,n.jsx)(d.A,{md:5,lg:7,className:"align-self-center",children:(0,n.jsxs)(o.A,{children:[(0,n.jsxs)(o.A.Group,{controlId:"filename",children:[(0,n.jsx)(o.A.Label,{className:"mt-2",children:i("FileName")}),(0,n.jsx)(o.A.Control,{size:"sm",type:"text",disabled:!0,value:t.original_name?t.original_name:t.name})]}),(0,n.jsxs)(o.A.Group,{controlId:"description",children:[(0,n.jsx)(o.A.Label,{children:"application"===e.type?i("ShortDescription/(Max255Characters)"):i("Source/Description")}),(0,n.jsx)(o.A.Control,{maxLength:"application"===e.type?255:void 0,size:"sm",type:"text",placeholder:"application"===e.type?i("`Enteryourdocumentdescription`"):i("`Enteryourimagesource/description`"),value:_[a],onChange:e=>T(e,a)})]})]})}),(0,n.jsx)(d.A,{md:3,lg:2,className:"align-self-center text-center",onClick:()=>F(t),children:(0,n.jsx)(c.A,{variant:"dark",children:i("Remove")})})]})}),(0,n.jsxs)(p.A,{show:u,onHide:R,children:[(0,n.jsx)(p.A.Header,{closeButton:!0,children:(0,n.jsx)(p.A.Title,{children:i("DeleteFile")})}),(0,n.jsx)(p.A.Body,{children:i("Areyousurewanttodeletethisfile?")}),(0,n.jsxs)(p.A.Footer,{children:[(0,n.jsx)(c.A,{variant:"secondary",onClick:B,children:i("Cancel")}),(0,n.jsx)(c.A,{variant:"primary",onClick:()=>O(t),children:i("yes")})]})]})]},a));(0,a.useEffect)(()=>{E.forEach(e=>URL.revokeObjectURL(e.preview)),f=[]},[]),(0,a.useEffect)(()=>{e.getImageSource(_)},[_]),(0,a.useEffect)(()=>{D(e.srcText)},[e.srcText]),(0,a.useEffect)(()=>{e&&"true"===e.singleUpload&&S(!1),e&&e.datas&&I([...e.datas.map((t,i)=>(f.push({serverID:t._id,index:e.index?e.index:0,type:t.name.split(".")[1]}),{...t,preview:"".concat("http://localhost:3001/api/v1","/image/show/").concat(t._id)}))])},[e.datas]);let M=async(t,i)=>{if(t.length>i)try{let n=new FormData;n.append("file",t[i]);let a=await x.A.post(L,n,{"Content-Type":"multipart/form-data"});f.push({serverID:a._id,file:t[i],index:e.index?e.index:0,type:t[i].name.split(".")[1]}),M(t,i+1)}catch(e){M(t,i+1)}else e.getImgID(f,e.index?e.index:0)},H=(0,a.useCallback)(async e=>{await M(e,0);let t=e.map(e=>Object.assign(e,{preview:URL.createObjectURL(e)}));N?I(e=>[...e,...t]):I([...t])},[]),{getRootProps:G,getInputProps:V,isDragActive:W,isDragAccept:q,isDragReject:K,fileRejections:J}=(0,s.VB)({accept:e&&e.type?"application/pdf, application/vnd.openxmlformats-officedocument.wordprocessingml.document, application/vnd.oasis.opendocument.text,application/vnd.openxmlformats-officedocument.spreadsheetml.sheet,text/csv":"image/*",multiple:N,minSize:0,maxSize:k,onDrop:H,validator:function(e){if("/image"===L){if("image"!==e.type.substring(0,5))return g.Ay.error(i("toast.filetypenotsupport")),{code:"file-invalid-type",message:"File type not supported"}}else if("/files"===L&&"image"===e.type.substring(0,5))return g.Ay.error(i("toast.filetypenotsupport")),{code:"file-invalid-type",message:"File type not supported"};return null}}),Q=(0,a.useMemo)(()=>({...j,...W?y:{outline:"2px dashed #bbb"},...q?{outline:"2px dashed #595959"}:{outline:"2px dashed #bbb"},...K?{outline:"2px dashed red"}:{activeStyle:y}}),[W,K]);t=e&&"application"===e.type?(0,n.jsx)("small",{style:{color:"#595959"},children:i("DocumentWeSupport")}):(0,n.jsx)("small",{style:{color:"#595959"},children:i("ImageWeSupport")});let X=J.length>0&&J[0].file.size>k;return(0,n.jsxs)(n.Fragment,{children:[(0,n.jsx)("div",{className:" d-flex justify-content-center align-items-center mt-3",style:{width:"100%",height:"180px"},children:(0,n.jsxs)("div",{...G({style:Q}),children:[(0,n.jsx)("input",{...V()}),(0,n.jsx)(l.g,{icon:r.rOd,size:"4x",color:"#999"}),(0,n.jsx)("p",{style:{color:"#595959",marginBottom:"0px"},children:i("Drag'n'dropsomefileshere,orclicktoselectfiles")}),!N&&(0,n.jsxs)("small",{style:{color:"#595959"},children:[(0,n.jsx)("b",{children:"Note:"})," One single image will be accepted"]}),t,(e.type,X&&(0,n.jsxs)("small",{className:"text-danger mt-2",children:[(0,n.jsx)(l.g,{icon:r.tUE,size:"1x",color:"red"})," ",i("FileistoolargeItshouldbelessthan20MB")]})),K&&(0,n.jsxs)("small",{className:"text-danger",style:{color:"#595959"},children:[(0,n.jsx)(l.g,{icon:r.tUE,size:"1x",color:"red"})," ",i("Filetypenotacceptedsorr")]})]})}),E.length>0&&(0,n.jsx)("div",{style:v,children:z})]})}},97257:(e,t,i)=>{i.d(t,{A:()=>g});var n=i(14232),a=i(95062),s=i.n(a),l=i(34192),r=i(13611),d=i(63824);let o=s().oneOf(["start","end"]),c=s().oneOfType([o,s().shape({sm:o}),s().shape({md:o}),s().shape({lg:o}),s().shape({xl:o}),s().shape({xxl:o}),s().object]);var p=i(37876);let u={id:s().string,href:s().string,onClick:s().func,title:s().node.isRequired,disabled:s().bool,align:c,menuRole:s().string,renderMenuOnMount:s().bool,rootCloseEvent:s().string,menuVariant:s().oneOf(["dark"]),flip:s().bool,bsPrefix:s().string,variant:s().string,size:s().string},m=n.forwardRef((e,t)=>{let{title:i,children:n,bsPrefix:a,rootCloseEvent:s,variant:o,size:c,menuRole:u,renderMenuOnMount:m,disabled:g,href:x,id:h,menuVariant:f,flip:j,...v}=e;return(0,p.jsxs)(l.A,{ref:t,...v,children:[(0,p.jsx)(r.A,{id:h,href:x,size:c,variant:o,disabled:g,childBsPrefix:a,children:i}),(0,p.jsx)(d.A,{role:u,renderOnMount:m,rootCloseEvent:s,variant:f,flip:j,children:n})]})});m.displayName="DropdownButton",m.propTypes=u;let g=m},99379:(e,t,i)=>{i.r(t),i.d(t,{default:()=>w});var n=i(37876),a=i(14232),s=i(49589),l=i(29335),r=i(56970),d=i(37784),o=i(29504),c=i(97257),p=i(34192),u=i(60282),m=i(89099),g=i.n(m),x=i(35611),h=i(54773),f=i(97685),j=i(48230),v=i.n(j),b=i(53718),y=i(89673),A=i(31753),C=i(5671);let w=e=>{let{t,i18n:i}=(0,A.Bd)("common"),m=e.routes&&"edit_landing"===e.routes[0]&&e.routes[1],j={title:"",description:"",pageCategory:"",isEnabled:!0,images:[],images_src:[],language:i.language},[w,k]=(0,a.useState)(j),[E,I]=(0,a.useState)([]),[N,S]=(0,a.useState)([]),[_,D]=(0,a.useState)([]),[L,P]=(0,a.useState)([]),F=e=>{k(t=>({...t,language:e.abbr}))},T={query:{},sort:{title:"asc"},limit:"~",select:"-_id -created_at -updated_at"},U=async()=>{let e=await b.A.get("/language",T);e&&D(e.data)},R=e=>{k(t=>({...t,description:e}))},B=e=>{let t=e.map(e=>e.serverID);k(e=>({...e,images:t}))},O=e=>{k(t=>({...t,images_src:e}))},z=async(i,n)=>{let a,s;i.preventDefault();let l={title:w.title.trim(),description:w.description,isEnabled:w.isEnabled,images:w.images,images_src:w.images_src,language:w.language};m?(s="adminsetting.landing.form.EditableContentisupdatedsuccessfully",a=await b.A.patch("/landingPage/".concat(e.routes[1]),l)):(s="adminsetting.landing.form.EditableContentisaddedsuccessfully",a=await b.A.post("/landingPage",l)),a&&a._id?(f.Ay.success(t(s)),g().push("/adminsettings/landing")):f.Ay.error(a)},M=async()=>{let e=await b.A.get("/pagecategory");if(e&&Array.isArray(e.data)){let t=e.data.filter(e=>"AboutUs"===e.title);t.unshift({_id:"",title:"Select"}),P(t)}},H=async()=>{let t=await b.A.get("/landingPage/".concat(e.routes[1]));if(t){let{pageCategory:e}=t;t.pageCategory=e&&e._id?e._id:"",I(t.images?t.images:[]),S(t.images_src?t.images_src:[]),k(e=>({...e,...t}))}};(0,a.useEffect)(()=>{U(),m&&H(),M()},[]);let G=(0,a.useRef)(null);return(0,n.jsx)(s.A,{className:"formCard",fluid:!0,children:(0,n.jsx)(l.A,{children:(0,n.jsx)(h.A,{onSubmit:z,ref:G,initialValues:w,enableReinitialize:!0,children:(0,n.jsxs)(l.A.Body,{children:[(0,n.jsx)(r.A,{children:(0,n.jsx)(d.A,{children:(0,n.jsx)(l.A.Title,{children:t("adminsetting.landing.form.EditableContent")})})}),(0,n.jsx)("hr",{}),(0,n.jsx)(r.A,{className:"mb-3",children:(0,n.jsx)(d.A,{md:!0,lg:12,sm:12,children:(0,n.jsxs)(o.A.Group,{children:[(0,n.jsx)(o.A.Label,{children:t("adminsetting.landing.form.Title")}),(0,n.jsxs)(x.s3,{name:"title",id:"title",value:w.title,validator:e=>""!==e.trim(),required:!0,errorMessage:{validator:t("adminsetting.landing.form.PleaseAddtheTitle")},onChange:e=>{if(e.target){let{name:t,value:i}=e.target;k(e=>({...e,[t]:i}))}},children:[(0,n.jsx)("option",{value:"",children:"Select"},""),(0,n.jsx)("option",{value:"Header",children:"Header"},"Header"),(0,n.jsx)("option",{value:"About Us",children:"About Us"},"About Us")]})]})})}),(0,n.jsx)(r.A,{className:"mb-3",children:(0,n.jsx)(d.A,{md:5,children:(0,n.jsxs)(o.A.Group,{children:[(0,n.jsx)(o.A.Label,{className:"pe-3",children:t("adminsetting.landing.form.chooseLanguage")}),(0,n.jsx)(c.A,{title:w.language.toUpperCase(),variant:"outline-secondary",id:"basic-dropdown",className:"d-inline",children:_&&_.map((e,t)=>(0,n.jsx)("div",{children:(0,n.jsxs)(p.A.Item,{active:e.abbr===w.language,eventKey:e._id,onClick:()=>F(e),children:[e.abbr.toUpperCase(),"-",e.title.toUpperCase()]})},t))})]})})}),(0,n.jsx)(r.A,{className:"mb-3",children:(0,n.jsx)(d.A,{children:(0,n.jsxs)(o.A.Group,{children:[(0,n.jsx)(o.A.Label,{children:t("adminsetting.landing.form.Description")}),(0,n.jsx)(C.x,{initContent:w.description,onChange:e=>R(e)})]})})}),(0,n.jsx)(r.A,{className:"mb-3",children:(0,n.jsx)(d.A,{children:(0,n.jsxs)(o.A.Group,{children:[(0,n.jsx)(o.A.Label,{children:t("adminsetting.landing.form.Images")}),(0,n.jsx)(y.A,{datas:E,srcText:N,getImgID:e=>B(e),getImageSource:e=>O(e)})]})})}),(0,n.jsx)(r.A,{className:"mb-3",children:(0,n.jsx)(d.A,{children:(0,n.jsx)(o.A.Group,{children:(0,n.jsx)(o.A.Check,{checked:w.isEnabled,name:"isEnabled",onClick:()=>{k(e=>({...e,isEnabled:!e.isEnabled}))},label:t("adminsetting.landing.form.Published"),type:"checkbox"})})})}),(0,n.jsx)(r.A,{className:"my-4",children:(0,n.jsxs)(d.A,{children:[(0,n.jsx)(u.A,{className:"me-2",type:"submit",variant:"primary",children:t("adminsetting.landing.form.Submit")}),(0,n.jsx)(u.A,{className:"me-2",onClick:()=>{k(j),I([]),S([]),window.scrollTo(0,0)},variant:"info",children:t("adminsetting.landing.form.Reset")}),(0,n.jsx)(v(),{href:"/adminsettings/[...routes]",as:"/adminsettings/landing",children:(0,n.jsx)(u.A,{variant:"secondary",children:t("adminsetting.landing.form.Cancel")})})]})})]})})})})}}}]);
//# sourceMappingURL=9379-7663e352585abb7c.js.map