{"version": 3, "file": "static/chunks/pages/adminsettings/institutionNetworks-f843c5fba55f2899.js", "mappings": "gFACA,4CACA,qCACA,WACA,OAAe,EAAQ,KAAgE,CACvF,EACA,SAFsB,omBCDtB,IAAMA,EAAS,aACFC,EAAmBC,CAAAA,EAAAA,EAAAA,CAAAA,CAAoBA,CAAC,CACnDC,sBAAuB,KACjBC,EAAMC,WAAW,IAAID,EAAMC,WAAW,CAACC,YAAY,IAAIF,EAAMC,WAAW,CAACC,YAAY,CAACN,EAAO,CAKnGO,CALqG,kBAKjF,kBACtB,GAAG,EAE0BL,CAAAA,EAAAA,EAAAA,CAAAA,CAAoBA,CAAC,CAChDC,sBAAuB,KACjBC,EAAMC,WAAW,IAAID,EAAMC,WAAW,CAACG,OAAO,IAAIJ,EAAMC,WAAW,CAACG,OAAO,CAACR,EAAO,CAKzFO,CAL2F,kBAKvE,eACtB,GAAG,EAEmCL,CAAAA,EAAAA,EAAAA,CAAAA,CAAoBA,CAAC,CACzDC,sBAAwBC,KAClBA,EAAMC,WAAW,IAAID,EAAMC,WAAW,CAACI,iBAAiB,IAAIL,EAAMC,WAAW,CAACI,iBAAiB,CAACT,EAAO,CAK7GO,CAL+G,kBAK3F,wBACtB,GAAG,EAE8BL,CAAAA,EAAAA,EAAAA,CAAAA,CAAoBA,CAAC,CACpDC,sBAAuB,KACjBC,EAAMC,WAAW,IAAID,EAAMC,WAAW,CAACK,YAAY,IAAIN,EAAMC,WAAW,CAACK,YAAY,CAACV,EAAO,CAKnGO,CALqG,kBAKjF,mBACtB,GAAG,EAE4BL,CAAAA,EAAAA,EAAAA,CAAAA,CAAoBA,CAAC,CAClDC,sBAAuB,KACjBC,EAAMC,WAAW,IAAID,EAAMC,WAAW,CAACM,SAAS,IAAIP,EAAMC,WAAW,CAACM,SAAS,CAACX,EAAO,CAK7FO,CAL+F,kBAK3E,iBACtB,GAAG,EAEqCL,CAAAA,EAAAA,EAAAA,CAAAA,CAAoBA,CAAC,CAC3DC,sBAAuB,KACjBC,EAAMC,WAAW,IAAID,EAAMC,WAAW,CAACO,uBAAuB,IAAIR,EAAMC,WAAW,CAACO,uBAAuB,CAACZ,EAAO,CAKzHO,CAL2H,kBAKvG,0BACtB,GAAG,EAEiCL,CAAAA,EAAAA,EAAAA,CAAAA,CAAoBA,CAAC,CACvDC,sBAAuB,KACjBC,EAAMC,WAAW,IAAID,EAAMC,WAAW,CAACO,uBAAuB,IAAIR,EAAMC,WAAW,CAACO,uBAAuB,CAACZ,EAAO,CAKzHO,CAL2H,kBAKvG,sBACtB,GAAG,EAC0BL,CAAAA,EAAAA,EAAAA,CAAAA,CAAoBA,CAAC,CAChDC,sBAAwBC,KAClBA,EAAMC,WAAW,IAAID,EAAMC,WAAW,CAACQ,MAAM,IAAIT,EAAMC,WAAW,CAACQ,MAAM,CAACb,EAAO,CAKvFO,CALyF,kBAKrE,eACtB,GAAG,EAE8BL,CAAAA,EAAAA,EAAAA,CAAAA,CAAoBA,CAAC,CACpDC,sBAAuB,GACjBC,IAAMC,WAAW,IAAID,EAAMC,WAAW,CAACS,WAAW,IAAIV,EAAMC,WAAW,CAACS,WAAW,CAACd,EAAO,CAKjGO,CALmG,kBAK/E,mBACtB,GAAG,EAEuCL,CAAAA,EAAAA,EAAAA,CAAAA,CAAoBA,CAAC,CAC7DC,sBAAuB,KACjBC,EAAMC,WAAW,IAAID,EAAMC,WAAW,CAACU,WAAW,IAAIX,EAAMC,WAAW,CAACU,WAAW,CAACf,EAAO,CAKjGO,CALmG,kBAK/E,4BACtB,GAEaS,EAA6Bd,CAAAA,EAAAA,EAAAA,CAAAA,CAAoBA,CAAC,CAC7DC,sBAAuB,GACjBC,IAAMC,WAAW,IAAID,EAAMC,WAAW,CAACY,mBAAmB,IAAIb,EAAMC,WAAW,CAACY,mBAAmB,CAACjB,EAAO,CAKjHO,CALmH,kBAK/F,4BACtB,GAEaW,EAA0BhB,CAAAA,EAAAA,EAAAA,CAAAA,CAAoBA,CAAC,CAC1DC,sBAAuB,KACjBC,EAAMC,WAAW,IAAID,EAAMC,WAAW,CAACc,gBAAgB,IAAIf,EAAMC,WAAW,CAACc,gBAAgB,CAACnB,EAAO,CAK3GO,CAL6G,kBAKzF,yBACtB,GAAG,EAEkCL,CAAAA,EAAAA,EAAAA,CAAAA,CAAoBA,CAAC,CACxDC,sBAAuB,KACjBC,EAAMC,WAAW,IAAID,EAAMC,WAAW,CAACe,gBAAgB,IAAIhB,EAAMC,WAAW,CAACe,gBAAgB,CAACpB,EAAO,CAK3GO,CAL6G,kBAKzF,uBACtB,GAAG,EAEgCL,CAAAA,EAAAA,EAAAA,CAAAA,CAAoBA,CAAC,CACtDC,sBAAuB,KACjBC,EAAMC,WAAW,IAAID,EAAMC,WAAW,CAACgB,cAAc,IAAIjB,EAAMC,WAAW,CAACgB,cAAc,CAACrB,EAAO,CAKvGO,CALyG,kBAKrF,qBACtB,GAAG,EAE0BL,CAAAA,EAAAA,EAAAA,CAAAA,CAAoBA,CAAC,CAChDC,sBAAuB,KACjBC,EAAMC,WAAW,IAAID,EAAMC,WAAW,CAACiB,MAAM,IAAIlB,EAAMC,WAAW,CAACiB,MAAM,CAACtB,EAAO,CAKvFO,CALyF,kBAKrE,eACtB,GAAG,EAE6BL,CAAAA,EAAAA,EAAAA,CAAAA,CAAoBA,CAAC,CACnDC,sBAAuB,GACjBC,IAAMC,WAAW,IAAID,EAAMC,WAAW,CAACkB,UAAU,IAAInB,EAAMC,WAAW,CAACkB,UAAU,CAACvB,EAAO,CAK/FO,CALiG,kBAK7E,kBACtB,GAAG,EAE4BL,CAAAA,EAAAA,EAAAA,CAAAA,CAAoBA,CAAC,CAClDC,sBAAwBC,KAClBA,EAAMC,WAAW,IAAID,EAAMC,WAAW,CAACmB,QAAQ,IAAIpB,EAAMC,WAAW,CAACmB,QAAQ,CAACxB,EAAO,CAK3FO,CAL6F,kBAKzE,iBACtB,GAAG,EAE8BL,CAAAA,EAAAA,EAAAA,CAAAA,CAAoBA,CAAC,CACpDC,sBAAuB,KACjBC,EAAMC,WAAW,IAAID,EAAMC,WAAW,CAACoB,WAAW,IAAIrB,EAAMC,WAAW,CAACoB,WAAW,CAACzB,EAAO,CAKjGO,CALmG,kBAK/E,mBACtB,GAAG,EAEwBL,CAAAA,EAAAA,EAAAA,CAAAA,CAAoBA,CAAC,CAC9CC,sBAAuB,KACjBC,EAAMC,WAAW,IAAID,EAAMC,WAAW,CAACqB,KAAK,IAAItB,EAAMC,WAAW,CAACqB,KAAK,CAAC1B,EAAO,CAKrFO,CALuF,kBAKnE,aACtB,GAAG,EAE8BL,CAAAA,EAAAA,EAAAA,CAAAA,CAAoBA,CAAC,CACpDC,sBAAuB,KACjBC,EAAMC,WAAW,IAAID,EAAMC,WAAW,CAACsB,WAAW,IAAIvB,EAAMC,WAAW,CAACsB,WAAW,CAAC3B,EAAO,CAKjGO,CALmG,kBAK/E,mBACtB,GAAG,EAE8BL,CAAAA,EAAAA,EAAAA,CAAAA,CAAoBA,CAAC,CACpDC,sBAAuB,GACjBC,IAAMC,WAAW,IAAID,EAAMC,WAAW,CAACuB,YAAY,IAAIxB,EAAMC,WAAW,CAACuB,YAAY,CAAC5B,EAAO,CAKnGO,CALqG,kBAKjF,mBACtB,GAAG,EAE0BL,CAAAA,EAAAA,EAAAA,CAAAA,CAAoBA,CAAC,CAChDC,sBAAwBC,GACtB,EAAIA,EAAMC,WAAW,IAAID,EAAMC,WAAW,CAACwB,SAAS,IAAIzB,EAAMC,WAAW,CAACwB,SAAS,CAAC7B,EAAO,IAAII,EAAMC,WAAW,CAACyB,OAAO,IAAI1B,EAAMC,WAAW,CAACyB,OAAO,CAAC9B,EAAO,IAAGI,EAAMC,WAAW,CAAC0B,KAAK,IAAI3B,EAAMC,WAAW,CAAC0B,KAAK,CAAC/B,EAAO,IAAGI,EAAMC,WAAW,CAAC2B,MAAM,IAAI5B,EAAMC,WAAW,CAAC2B,MAAM,CAAChC,EAAO,IAAGI,EAAMC,WAAW,CAACU,WAAW,IAAIX,EAAMC,WAAW,CAACU,WAAW,CAACf,EAAO,IAAGI,EAAMC,WAAW,CAAC4B,MAAM,IAAI7B,EAAMC,WAAW,CAAC4B,MAAM,CAACjC,EAAO,EAAE,CAG5Z,EAETO,mBAAoB,eACtB,GAAG,EAEYN,gBAAgBA,EAAC,kLCpDhC,MAhKgC,IAC5B,GAAM,CAACiC,EAAWC,EAAe,CAAGC,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CA+JjCC,EA/JoC,EACzC,EAAGC,EAAW,CAAGF,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,EA8JI,GA7J7B,CAACG,EAAWC,EAAa,CAAGJ,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAC,GACrC,CAACK,EAASC,EAAW,CAAGN,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAC,IACjC,CAACO,EAAaC,EAAS,CAAGR,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,EAAC,GACnC,CAACS,EAA0BC,EAA4B,CAAGV,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAC,CAAC,GACpEW,EAAY,IAAMH,GAAS,GAC3B,CAAEI,GAAC,CAAE,CAAGC,CAAAA,EAAAA,EAAAA,EAAAA,CAAcA,CAAC,UAIvBC,EACF,WAACC,EAAAA,CAAOA,CAAAA,CAACC,GAAG,0BACR,UAACD,EAAAA,CAAOA,CAACE,MAAM,EAACC,GAAG,KAAKC,UAAU,uBAAc,aAGhD,UAACJ,EAAAA,CAAOA,CAACK,IAAI,WACT,WAACC,MAAAA,CAAIF,UAAU,gBACX,WAACG,IAAAA,WACG,UAACC,IAAAA,UAAE,UAAS,4BAEhB,WAACD,IAAAA,WACG,UAACC,IAAAA,UAAE,QAAO,gCAEd,WAACD,IAAAA,WACG,UAACC,IAAAA,UAAE,SAAQ,yCAEf,WAACD,IAAAA,WACG,UAACC,IAAAA,UAAE,UAAS,iDAEhB,WAACD,IAAAA,WACG,UAACC,IAAAA,UAAE,WAAU,uEAEjB,WAACD,IAAAA,WACG,UAACC,IAAAA,UAAE,WAAU,uEAEjB,WAACD,IAAAA,WACG,UAACC,IAAAA,UAAE,UAAS,+DAgB1BC,EAAU,CACZ,CACIC,KAXJ,UAACC,EAAAA,CAAcA,CAAAA,CAACC,QAAQ,QAAQC,UAAU,QAAQC,QAASf,WACvD,WAACgB,OAAAA,WACIlB,EAAE,SAAS,eACZ,UAACmB,IAAAA,CAAEZ,UAAU,oBAAoBa,MAAO,CAAEC,OAAQ,SAAU,EAAGC,cAAY,cAS/EC,SAAU,OACd,EACA,CACIV,KAAMb,EAAE,UACRuB,SAAU,GACVC,KAAM,GACF,WAACf,MAAAA,WACG,UAACgB,IAAIA,CAACC,KAAK,6BAA6BpB,GAAI,OAAvCmB,oCAAwF,OAANE,EAAEC,GAAG,WAExF,UAACT,IAAAA,CAAEZ,UAAU,uBAEV,OAEP,UAACsB,IAAAA,CAAEC,QAAS,IAAMC,EAAWJ,YACzB,UAACR,IAAAA,CAAEZ,UAAU,8BAI7B,EACH,CACKyB,EAA2B,CAC7BC,KAAM,CAAEC,MAAO,KAAM,EACrBC,MAAO1C,EACP2C,KAAM,EACNC,MAAO,CAAC,CACZ,EAEAC,CAAAA,EAAAA,EAAAA,SAAAA,CAASA,CAAC,KACNC,GACJ,EAAG,EAAE,EAEL,IAAMA,EAA4B,UAC9BjD,GAAW,GACX,IAAMkD,EAAW,MAAMC,EAAAA,CAAUA,CAACC,GAAG,CAAC,sBAAuBV,GACzDQ,GAAYA,EAASG,IAAI,EAAIH,EAASG,IAAI,CAACC,MAAM,CAAG,GAAG,CACvDzD,EAAeqD,EAASG,IAAI,EAC5BnD,EAAagD,EAASK,UAAU,EAChCvD,GAAW,GAEnB,EAQMwD,EAAsB,MAAOC,EAAiBX,KAChDJ,EAAyBG,KAAK,CAAGY,EACjCf,EAAyBI,IAAI,CAAGA,EAChC9C,GAAW,GACX,IAAMkD,EAAW,MAAMC,EAAAA,CAAUA,CAACC,GAAG,CAAC,sBAAuBV,GACzDQ,GAAYA,EAASG,IAAI,EAAIH,EAASG,IAAI,CAACC,MAAM,CAAG,GAAG,CACvDzD,EAAeqD,EAASG,IAAI,EAC5BjD,EAAWqD,GACXzD,GAAW,GAEnB,EAEM0D,EAAe,UACjB,GAAI,CACA,MAAMP,EAAAA,CAAUA,CAACQ,MAAM,CAAC,uBAAgD,OAAzBpD,IAC/C0C,IACA3C,EAAS,IACTsD,EAAAA,EAAKA,CAACC,OAAO,CAACnD,EAAE,yEACpB,CAAE,MAAOoD,EAAO,CACZF,EAAAA,EAAKA,CAACE,KAAK,CAACpD,EAAE,mEAClB,CACJ,EAEM+B,EAAa,MAAOsB,IACtBvD,EAA4BuD,EAAIzB,GAAG,EACnChC,GAAS,EACb,EAEA,MACI,WAACa,MAAAA,WACG,WAAC6C,EAAAA,CAAKA,CAAAA,CAACC,KAAM5D,EAAa6D,OAAQzD,YAC9B,UAACuD,EAAAA,CAAKA,CAACjD,MAAM,EAACoD,WAAW,aACrB,UAACH,EAAAA,CAAKA,CAACI,KAAK,WAAE1D,EAAE,gDAEpB,UAACsD,EAAAA,CAAKA,CAAC9C,IAAI,WAAER,EAAE,4CACf,WAACsD,EAAAA,CAAKA,CAACK,MAAM,YACT,UAACC,EAAAA,CAAMA,CAAAA,CAACC,QAAQ,YAAY/B,QAAS/B,WAChCC,EAAE,YAEP,UAAC4D,EAAAA,CAAMA,CAAAA,CAACC,QAAQ,UAAU/B,QAASkB,WAC9BhD,EAAE,eAKf,UAAC8D,EAAAA,CAAQA,CAAAA,CACLlD,QAASA,EACT+B,KAAMzD,EACNK,UAAWA,EACXwE,WAAW,EACXjB,oBAAqBA,EACrBkB,iBAzDc5B,CAyDI4B,GAxD1BhC,EAAyBG,KAAK,CAAG1C,EACjCuC,EAAyBI,IAAI,CAAGA,EAChCG,GACJ,MAyDJ,6GCrIA,SAASuB,EAASG,CAAoB,EACpC,GAAM,GAAEjE,CAAC,CAAE,CAAGC,CAAAA,EAAAA,EAAAA,EAAAA,CAAcA,CAAC,UACvBiE,EAA6B,CACjCC,gBAAiBnE,EAAE,cACnB,EACI,SACJY,CAAO,MACP+B,CAAI,WACJpD,CAAS,uBACT6E,CAAqB,WACrBC,CAAS,oBACTC,CAAkB,qBAClBxB,CAAmB,kBACnBkB,CAAgB,aAChBO,CAAW,oBACXC,CAAkB,gBAClBC,CAAc,SACdC,CAAO,WACPX,CAAS,sBACTY,CAAoB,mBACpBC,CAAiB,YACjBC,CAAU,QACVC,CAAM,kBACNC,CAAgB,cAChBC,CAAY,CAEZ,CADA,EACGC,EACJ,CAAGhB,EAGEiB,EAAiB,4BACrBhB,EACAiB,gBAAiBnF,EAAE,IAP0C,MAQ7DoF,SAAU,WACVxE,EACA+B,KAAMA,GAAQ,EAAE,CAChB0C,OAAO,EACPC,2BAA4BlB,EAC5BmB,UAAWlB,EACXmB,gBAAiBd,qBACjBJ,EACAmB,YAAY,EACZC,iBAAkB3B,EAClB4B,kBAAmBnB,GAA0C,GAC7DoB,eADwCpB,cACVD,GAA4B,CAAC,GAAI,GAAI,GAAI,CAA3BA,EAA+B,GAAG,CAC9EsB,oBAAqBtG,EACrBuG,oBAAqBhD,EACrBiD,aAAc/B,iBACdS,uBACAE,oBACAC,EACAoB,kBAAmB,UAACC,EAAAA,CAAYA,CAAAA,CAAAA,GAChCC,SAAU,UAAC/E,IAAAA,CAAEZ,UAAU,6CACvBsE,SACAC,eACAE,mBACAD,EACAxE,UAAW,WACb,EACA,MACE,UAAC4F,EAAAA,EAASA,CAAAA,CAAE,GAAGjB,CAAc,EAEjC,CAEApB,EAASsC,YAAY,CAAG,CACtBb,WAAW,EACXE,YAAY,EACZlG,UAAW,KACXwE,WAAW,EACXY,qBAAsB,KACtBC,mBAAmB,EACnBC,YAAY,EACZE,iBAAkB,EACpB,EAEA,MAAejB,QAAQA,EAAC,mEChHT,SAASuC,EAAgBC,CAAW,EAC/C,MACE,UAAC7F,MAAAA,CAAIF,UAAU,sDACb,UAACE,MAAAA,CAAIF,UAAU,mBAAU,yCAG/B,gECFa,SAASgG,EAAYtC,CAAuB,EACzD,MACE,UAACuC,KAAAA,CAAGjG,UAAU,wBAAgB0D,EAAM/B,KAAK,EAE7C,+MC8CA,MAxCiCoE,QAiCzBlJ,EAAAA,EAhCN,GAAM,GAAE4C,CAAC,CAAE,CAAGC,CAAAA,EAAAA,EAAAA,EAAAA,CAAcA,CAAC,UACvBwG,EAA8B,IAEhC,OAoCiC,EApCjC,EAACC,EAAAA,CAASA,CAAAA,CAACtF,MAAO,CAAEuF,UAAW,QAAS,EAAGC,KAAK,IAACrG,UAAU,gBACzD,UAACsG,EAAAA,CAAGA,CAAAA,UACF,UAACC,EAAAA,CAAGA,CAAAA,CAACC,GAAI,YACP,UAACR,EAAAA,CAAWA,CAAAA,CAACrE,MAAQlC,EAAE,gEAG3B,UAAC6G,EAAAA,CAAGA,CAAAA,UACF,UAACC,EAAAA,CAAGA,CAAAA,CAACC,GAAI,YACP,UAACtF,IAAIA,CACHC,KAAK,6BACLpB,GAAG,OAFAmB,8CAIH,UAACmC,EAAAA,CAAMA,CAAAA,CAACC,QAAQ,YAAYmD,KAAK,cAChChH,EAAE,oEAKT,UAAC6G,EAAAA,CAAGA,CAAAA,CAACtG,UAAU,gBACb,UAACuG,EAAAA,CAAGA,CAAAA,CAACC,GAAI,YACP,UAAC1H,EAAAA,OAAuBA,CAAAA,CAAAA,UAO5B4H,EAA8BjJ,CAAAA,EAAAA,EAAAA,0BAAAA,CAA0BA,CAAC,IAAM,UAACyI,EAAAA,CAAAA,IAChErJ,EAAY8J,CAAAA,EAAAA,EAAAA,EAAAA,CAAWA,CAAC,GAAW9J,SACzC,IAAI,GAAEA,GAAAA,OAAAA,EAAAA,EAAOC,IAAPD,OAAOC,GAAPD,OAAAA,EAAAA,EAAAA,mBAAoBa,EAApBb,KAAAA,EAAAA,CAAyC,CAAC,GAA1CA,UAAuD,EAI3D,UAAC6J,EAAAA,CAAAA,GAHM,UAACZ,EAAAA,OAAeA,CAAAA,CAAAA,EAK3B", "sources": ["webpack://_N_E/?a891", "webpack://_N_E/./pages/adminsettings/permissions.tsx", "webpack://_N_E/./pages/adminsettings/institutionNetworks/institutionNetworkTable.tsx", "webpack://_N_E/./components/common/RKITable.tsx", "webpack://_N_E/./pages/rNoAccess.tsx", "webpack://_N_E/./components/common/PageHeading.tsx", "webpack://_N_E/./pages/adminsettings/institutionNetworks/index.tsx"], "sourcesContent": ["\n    (window.__NEXT_P = window.__NEXT_P || []).push([\n      \"/adminsettings/institutionNetworks\",\n      function () {\n        return require(\"private-next-pages/adminsettings/institutionNetworks/index.tsx\");\n      }\n    ]);\n    if(module.hot) {\n      module.hot.dispose(function () {\n        window.__NEXT_P.push([\"/adminsettings/institutionNetworks\"])\n      });\n    }\n  ", "//Import services/components\r\nimport connectedAuthWrapper from 'redux-auth-wrapper/connectedAuthWrapper';\r\n\r\nconst create = \"create:any\";\r\nexport const canAddAreaOfWork = connectedAuthWrapper({\r\n  authenticatedSelector: (state) => {\r\n    if (state.permissions && state.permissions.area_of_work && state.permissions.area_of_work[create]) {\r\n      return true;\r\n    }\r\n    return false;\r\n  },\r\n  wrapperDisplayName: 'CanAddAreaOfWork',\r\n});\r\n\r\nexport const canAddCountry = connectedAuthWrapper({\r\n  authenticatedSelector: (state) => {\r\n    if (state.permissions && state.permissions.country && state.permissions.country[create]) {\r\n      return true;\r\n    }\r\n    return false;\r\n  },\r\n  wrapperDisplayName: 'CanAddCountry',\r\n});\r\n\r\nexport const canAddDeploymentStatus = connectedAuthWrapper({\r\n  authenticatedSelector: (state) => {\r\n    if (state.permissions && state.permissions.deployment_status && state.permissions.deployment_status[create]) {\r\n      return true;\r\n    }\r\n    return false;\r\n  },\r\n  wrapperDisplayName: 'CanAddDeploymentStatus',\r\n});\r\n\r\nexport const canAddEventStatus = connectedAuthWrapper({\r\n  authenticatedSelector: (state) => {\r\n    if (state.permissions && state.permissions.event_status && state.permissions.event_status[create]) {\r\n      return true;\r\n    }\r\n    return false;\r\n  },\r\n  wrapperDisplayName: 'CanAddEventStatus',\r\n});\r\n\r\nexport const canAddExpertise = connectedAuthWrapper({\r\n  authenticatedSelector: (state) => {\r\n    if (state.permissions && state.permissions.expertise && state.permissions.expertise[create]) {\r\n      return true;\r\n    }\r\n    return false;\r\n  },\r\n  wrapperDisplayName: 'CanAddExpertise',\r\n});\r\n\r\nexport const canAddFocalPointApproval = connectedAuthWrapper({\r\n  authenticatedSelector: (state) => {\r\n    if (state.permissions && state.permissions.institution_focal_point && state.permissions.institution_focal_point[create]) {\r\n      return true;\r\n    }\r\n    return false;\r\n  },\r\n  wrapperDisplayName: 'CanAddFocalPointApproval',\r\n});\r\n\r\nexport const canAddVspaceApproval = connectedAuthWrapper({\r\n  authenticatedSelector: (state) => {\r\n    if (state.permissions && state.permissions.institution_focal_point && state.permissions.institution_focal_point[create]) {\r\n      return true;\r\n    }\r\n    return false;\r\n  },\r\n  wrapperDisplayName: 'CanAddVspaceApproval',\r\n});\r\nexport const canAddHazards = connectedAuthWrapper({\r\n  authenticatedSelector: (state) => {\r\n    if (state.permissions && state.permissions.hazard && state.permissions.hazard[create]) {\r\n      return true;\r\n    }\r\n    return false;\r\n  },\r\n  wrapperDisplayName: 'CanAddHazards',\r\n});\r\n\r\nexport const canAddHazardTypes = connectedAuthWrapper({\r\n  authenticatedSelector: (state) => {\r\n    if (state.permissions && state.permissions.hazard_type && state.permissions.hazard_type[create]) {\r\n      return true;\r\n    }\r\n    return false;\r\n  },\r\n  wrapperDisplayName: 'CanAddHazardTypes',\r\n}); \r\n\r\nexport const canAddOrganisationApproval = connectedAuthWrapper({\r\n  authenticatedSelector: (state) => {\r\n    if (state.permissions && state.permissions.institution && state.permissions.institution[create]) {\r\n      return true;\r\n    }\r\n    return false;\r\n  },\r\n  wrapperDisplayName: 'CanAddOrganisationApproval',\r\n}); \r\n\r\nexport const canAddOrganisationNetworks = connectedAuthWrapper({\r\n  authenticatedSelector: (state) => {\r\n    if (state.permissions && state.permissions.institution_network && state.permissions.institution_network[create]) {\r\n      return true;\r\n    }\r\n    return false;\r\n  },\r\n  wrapperDisplayName: 'CanAddOrganisationNetworks',\r\n});\r\n\r\nexport const canAddOrganisationTypes = connectedAuthWrapper({\r\n  authenticatedSelector: (state) => {\r\n    if (state.permissions && state.permissions.institution_type && state.permissions.institution_type[create]) {\r\n      return true;\r\n    }\r\n    return false;\r\n  },\r\n  wrapperDisplayName: 'CanAddOrganisationTypes',\r\n});\r\n\r\nexport const canAddOperationStatus = connectedAuthWrapper({\r\n  authenticatedSelector: (state) => {\r\n    if (state.permissions && state.permissions.operation_status && state.permissions.operation_status[create]) {\r\n      return true;\r\n    }\r\n    return false;\r\n  },\r\n  wrapperDisplayName: 'CanAddOperationStatus',\r\n});\r\n\r\nexport const canAddProjectStatus = connectedAuthWrapper({\r\n  authenticatedSelector: (state) => {\r\n    if (state.permissions && state.permissions.project_status && state.permissions.project_status[create]) {\r\n      return true;\r\n    }\r\n    return false;\r\n  },\r\n  wrapperDisplayName: 'CanAddProjectStatus',\r\n});\r\n\r\nexport const canAddRegions = connectedAuthWrapper({\r\n  authenticatedSelector: (state) => {\r\n    if (state.permissions && state.permissions.region && state.permissions.region[create]) {\r\n      return true;\r\n    }\r\n    return false;\r\n  },\r\n  wrapperDisplayName: 'CanAddRegions',\r\n});\r\n\r\nexport const canAddRiskLevels = connectedAuthWrapper({\r\n  authenticatedSelector: (state) => {\r\n    if (state.permissions && state.permissions.risk_level && state.permissions.risk_level[create]) {\r\n      return true;\r\n    }\r\n    return false;\r\n  },\r\n  wrapperDisplayName: 'CanAddRiskLevels',\r\n});\r\n\r\nexport const canAddSyndromes = connectedAuthWrapper({\r\n  authenticatedSelector: (state) => {\r\n    if (state.permissions && state.permissions.syndrome && state.permissions.syndrome[create]) {\r\n      return true;\r\n    }\r\n    return false;\r\n  },\r\n  wrapperDisplayName: 'CanAddSyndromes',\r\n});\r\n\r\nexport const canAddUpdateTypes = connectedAuthWrapper({\r\n  authenticatedSelector: (state) => {\r\n    if (state.permissions && state.permissions.update_type && state.permissions.update_type[create]) {\r\n      return true;\r\n    }\r\n    return false;\r\n  },\r\n  wrapperDisplayName: 'CanAddUpdateTypes',\r\n});\r\n\r\nexport const canAddUsers = connectedAuthWrapper({\r\n  authenticatedSelector: (state) => {\r\n    if (state.permissions && state.permissions.users && state.permissions.users[create]) {\r\n      return true;\r\n    }\r\n    return false;\r\n  },\r\n  wrapperDisplayName: 'CanAddUsers',\r\n});\r\n\r\nexport const canAddWorldRegion = connectedAuthWrapper({\r\n  authenticatedSelector: (state) => {\r\n    if (state.permissions && state.permissions.worl_region && state.permissions.worl_region[create]) {\r\n      return true;\r\n    }\r\n    return false;\r\n  },\r\n  wrapperDisplayName: 'CanAddWorldRegion',\r\n});\r\n\r\nexport const canAddLandingPage = connectedAuthWrapper({\r\n  authenticatedSelector: (state) => {\r\n    if (state.permissions && state.permissions.landing_page && state.permissions.landing_page[create]) {\r\n      return true;\r\n    }\r\n    return false;\r\n  },\r\n  wrapperDisplayName: 'CanAddLandingPage',\r\n});\r\n\r\nexport const canAddContent = connectedAuthWrapper({\r\n  authenticatedSelector: (state) => {\r\n    if (state.permissions && state.permissions.operation && state.permissions.operation[create] && state.permissions.project && state.permissions.project[create]&& state.permissions.event && state.permissions.event[create]&& state.permissions.vspace && state.permissions.vspace[create]&& state.permissions.institution && state.permissions.institution[create]&& state.permissions.update && state.permissions.update[create]) {\r\n      return true;\r\n    }\r\n    return false;\r\n  },\r\n  wrapperDisplayName: 'CanAddContent',\r\n});\r\n\r\nexport default canAddAreaOfWork;", "//Import Library\r\nimport Link from \"next/link\";\r\nimport { useState, useEffect } from \"react\";\r\nimport { Modal, Button, Popover, OverlayTrigger } from \"react-bootstrap\";\r\nimport toast from 'react-hot-toast';\r\n\r\n//Import services/components\r\nimport RKITable from \"../../../components/common/RKITable\";\r\nimport apiService from \"../../../services/apiService\";\r\nimport { useTranslation } from 'next-i18next';\r\n\r\nconst InstitutionNetworkTable = (_props: any) => {\r\n    const [tabledata, setDataToTable] = useState([]);\r\n    const [, setLoading] = useState(false);\r\n    const [totalRows, setTotalRows] = useState(0);\r\n    const [perPage, setPerPage] = useState(10);\r\n    const [isModalShow, setModal] = useState(false);\r\n    const [selectInstitutionNetwork, setSelectInstitutionNetwork] = useState({});\r\n    const modalHide = () => setModal(false);\r\n    const { t } = useTranslation('common');\r\n\r\n\r\n    // For network popover\r\n    const Networkpopover = (\r\n        <Popover id=\"popover-basic\">\r\n            <Popover.Header as=\"h3\" className=\"text-center\">\r\n                NETWORKS\r\n            </Popover.Header>\r\n            <Popover.Body>\r\n                <div className=\"m-2\">\r\n                    <p>\r\n                        <b>EMLab</b> - European Mobile Lab\r\n                    </p>\r\n                    <p>\r\n                        <b>EMT</b> - Emergency Medical Teams\r\n                    </p>\r\n                    <p>\r\n                        <b>GHPP</b> - Global Health Protection Program\r\n                    </p>\r\n                    <p>\r\n                        <b>GOARN</b> - Global Outbreak Alert & Response Network\r\n                    </p>\r\n                    <p>\r\n                        <b>IANPHI</b> - International Association of National Public Health Institutes\r\n                    </p>\r\n                    <p>\r\n                        <b>STAKOB</b> - Ständiger Arbeitskreis der Kompetenz-und Behandlungszentren\r\n                    </p>\r\n                    <p>\r\n                        <b>WHOCC</b>- World Health Organization Collaborating Centres\r\n                    </p>\r\n                </div>\r\n            </Popover.Body>\r\n        </Popover>\r\n    );\r\n    const icons = (\r\n        <OverlayTrigger trigger=\"click\" placement=\"right\" overlay={Networkpopover}>\r\n            <span>\r\n                {t(\"Title\")}&nbsp;&nbsp;&nbsp;\r\n                <i className=\"fa fa-info-circle\" style={{ cursor: \"pointer\" }} aria-hidden=\"true\"></i>\r\n            </span>\r\n        </OverlayTrigger>\r\n    );\r\n    // End\r\n\r\n    const columns = [\r\n        {\r\n            name: icons,\r\n            selector: \"title\",\r\n        },\r\n        {\r\n            name: t(\"action\"),\r\n            selector: \"\",\r\n            cell: (d: any) => (\r\n                <div>\r\n                    <Link href=\"/adminsettings/[...routes]\" as={`/adminsettings/edit_institution_network/${d._id}`}>\r\n\r\n                        <i className=\"icon fas fa-edit\" />\r\n\r\n                    </Link>\r\n                    &nbsp;\r\n                    <a onClick={() => userAction(d)}>\r\n                        <i className=\"icon fas fa-trash-alt\" />\r\n                    </a>\r\n                </div>\r\n            ),\r\n        },\r\n    ];\r\n    const institutionNetworkParams = {\r\n        sort: { title: \"asc\" },\r\n        limit: perPage,\r\n        page: 1,\r\n        query: {},\r\n    };\r\n\r\n    useEffect(() => {\r\n        getInstitutionNetworkData();\r\n    }, []);\r\n\r\n    const getInstitutionNetworkData = async () => {\r\n        setLoading(true);\r\n        const response = await apiService.get(\"/institutionnetwork\", institutionNetworkParams);\r\n        if (response && response.data && response.data.length > 0) {\r\n            setDataToTable(response.data);\r\n            setTotalRows(response.totalCount);\r\n            setLoading(false);\r\n        }\r\n    };\r\n\r\n    const handlePageChange = (page: any) => {\r\n        institutionNetworkParams.limit = perPage;\r\n        institutionNetworkParams.page = page;\r\n        getInstitutionNetworkData();\r\n    };\r\n\r\n    const handlePerRowsChange = async (newPerPage: any, page: any) => {\r\n        institutionNetworkParams.limit = newPerPage;\r\n        institutionNetworkParams.page = page;\r\n        setLoading(true);\r\n        const response = await apiService.get(\"/institutionnetwork\", institutionNetworkParams);\r\n        if (response && response.data && response.data.length > 0) {\r\n            setDataToTable(response.data);\r\n            setPerPage(newPerPage);\r\n            setLoading(false);\r\n        }\r\n    };\r\n\r\n    const modalConfirm = async () => {\r\n        try {\r\n            await apiService.remove(`/institutionnetwork/${selectInstitutionNetwork}`);\r\n            getInstitutionNetworkData();\r\n            setModal(false);\r\n            toast.success(t(\"adminsetting.Organisationnetworks.Table.orgNetworkDeletedSuccessfully\"));\r\n        } catch (error) {\r\n            toast.error(t(\"adminsetting.Organisationnetworks.Table.errorDeletingOrgNetwork\"));\r\n        }\r\n    };\r\n\r\n    const userAction = async (row: any) => {\r\n        setSelectInstitutionNetwork(row._id);\r\n        setModal(true);\r\n    };\r\n\r\n    return (\r\n        <div>\r\n            <Modal show={isModalShow} onHide={modalHide}>\r\n                <Modal.Header closeButton>\r\n                    <Modal.Title>{t(\"adminsetting.Organisationnetworks.Delete\")}</Modal.Title>\r\n                </Modal.Header>\r\n                <Modal.Body>{t(\"adminsetting.Organisationnetworks.sure\")}</Modal.Body>\r\n                <Modal.Footer>\r\n                    <Button variant=\"secondary\" onClick={modalHide}>\r\n                        {t(\"cancel\")}\r\n                    </Button>\r\n                    <Button variant=\"primary\" onClick={modalConfirm}>\r\n                        {t(\"yes\")}\r\n                    </Button>\r\n                </Modal.Footer>\r\n            </Modal>\r\n\r\n            <RKITable\r\n                columns={columns}\r\n                data={tabledata}\r\n                totalRows={totalRows}\r\n                pagServer={true}\r\n                handlePerRowsChange={handlePerRowsChange}\r\n                handlePageChange={handlePageChange}\r\n            />\r\n        </div>\r\n    );\r\n};\r\nexport default InstitutionNetworkTable;\r\n", "//Import Library\r\nimport React from 'react'\r\nimport DataTable  from 'react-data-table-component';\r\n\r\n//Import services/components\r\nimport { useTranslation } from 'next-i18next';\r\nimport CustomLoader from './CustomLoader';\r\n\r\ninterface RKITableProps {\r\n  columns: any[];\r\n  data: any[];\r\n  totalRows?: number;\r\n  resetPaginationToggle?: boolean;\r\n  subheader?: boolean;\r\n  subHeaderComponent?: React.ReactNode;\r\n  handlePerRowsChange?: (newPerPage: number, page: number) => void;\r\n  handlePageChange?: (page: number) => void;\r\n  rowsPerPage?: number[];\r\n  defaultRowsPerPage?: number;\r\n  selectableRows?: boolean;\r\n  loading?: boolean;\r\n  pagServer?: boolean;\r\n  onSelectedRowsChange?: (selectedRows: any) => void;\r\n  clearSelectedRows?: boolean;\r\n  sortServer?: boolean;\r\n  onSort?: (column: any, sortDirection: string) => void;\r\n  persistTableHead?: boolean;\r\n  sortFunction?: (rows: any[], field: any, direction: 'asc' | 'desc') => any[];\r\n  noHeader?: boolean;\r\n  dense?: boolean;\r\n  pagination?: boolean;\r\n  paginationServer?: boolean;\r\n  paginationTotalRows?: number;\r\n  subHeaderAlign?: string;\r\n  [key: string]: any;\r\n}\r\n\r\nfunction RKITable(props: RKITableProps) {\r\n  const { t } = useTranslation('common');\r\n  const paginationComponentOptions = {\r\n    rowsPerPageText: t('Rowsperpage'),\r\n    };\r\n  const {\r\n    columns,\r\n    data,\r\n    totalRows,\r\n    resetPaginationToggle,\r\n    subheader,\r\n    subHeaderComponent,\r\n    handlePerRowsChange,\r\n    handlePageChange,\r\n    rowsPerPage,\r\n    defaultRowsPerPage,\r\n    selectableRows,\r\n    loading,\r\n    pagServer,\r\n    onSelectedRowsChange,\r\n    clearSelectedRows,\r\n    sortServer,\r\n    onSort,\r\n    persistTableHead,\r\n    sortFunction,\r\n    // Filter out any unknown props that might cause DOM warnings\r\n    ...otherProps\r\n  } = props;\r\n\r\n  // Only pass known DataTable props to avoid DOM warnings\r\n  const dataTableProps = {\r\n    paginationComponentOptions,\r\n    noDataComponent: t(\"NoData\"),\r\n    noHeader: true,\r\n    columns,\r\n    data: data || [],\r\n    dense: true,\r\n    paginationResetDefaultPage: resetPaginationToggle,\r\n    subHeader: subheader,\r\n    progressPending: loading,\r\n    subHeaderComponent,\r\n    pagination: true,\r\n    paginationServer: pagServer,\r\n    paginationPerPage: defaultRowsPerPage ? defaultRowsPerPage : 10,\r\n    paginationRowsPerPageOptions: rowsPerPage ? rowsPerPage : [10, 15, 20, 25, 30],\r\n    paginationTotalRows: totalRows,\r\n    onChangeRowsPerPage: handlePerRowsChange,\r\n    onChangePage: handlePageChange,\r\n    selectableRows,\r\n    onSelectedRowsChange,\r\n    clearSelectedRows,\r\n    progressComponent: <CustomLoader/>,\r\n    sortIcon: <i className=\"sort-icon fas fa-exchange-alt\" />,\r\n    sortServer,\r\n    onSort,\r\n    sortFunction,\r\n    persistTableHead,\r\n    className: \"rki-table\"\r\n  };\r\n  return (\r\n    <DataTable {...dataTableProps} />\r\n  )\r\n};\r\n\r\nRKITable.defaultProps = {\r\n  subHeader: false,\r\n  pagination: true,\r\n  totalRows: null,\r\n  pagServer: true,\r\n  onSelectedRowsChange: null,\r\n  clearSelectedRows: false,\r\n  sortServer: false,\r\n  persistTableHead: false\r\n}\r\n\r\nexport default RKITable;\r\n\r\n", "export default function NoAccessMessage(_props: any) {\r\n    return (\r\n      <div className=\"container-fluid p-0 response-message-block\">\r\n        <div className=\"message\">you don't have permission to access</div>\r\n      </div>\r\n    )\r\n  }", "interface PageHeadingProps {\r\n  title: string; // Required based on actual usage\r\n}\r\n\r\nexport default function PageHeading(props: PageHeadingProps) {\r\n  return (\r\n    <h2 className=\"page-heading\">{props.title}</h2>\r\n  )\r\n}\r\n", "//Import Library\r\nimport { Contain<PERSON>, <PERSON>, <PERSON>, Button } from \"react-bootstrap\";\r\nimport Link from \"next/link\";\r\n\r\n//Import services/components\r\nimport PageHeading from \"../../../components/common/PageHeading\";\r\nimport InstitutionNetworkTable from \"./institutionNetworkTable\";\r\nimport { useTranslation } from 'next-i18next';\r\nimport { canAddOrganisationNetworks } from \"../permissions\";\r\nimport { useSelector } from \"react-redux\";\r\nimport NoAccessMessage from \"../../rNoAccess\";\r\n\r\n\r\n\r\nconst InstitutionNetworkIndex = (_props: any) => {\r\n  const { t } = useTranslation('common');\r\n  const ShowInstitutionNetworkIndex = () => {\r\n    return (\r\n      <Container style={{ overflowX: \"hidden\" }} fluid className=\"p-0\">\r\n        <Row>\r\n          <Col xs={12}>\r\n            <PageHeading title= {t(\"adminsetting.Organisationnetworks.OrganisationNetworks\")} />\r\n          </Col>\r\n        </Row>\r\n        <Row>\r\n          <Col xs={12}>\r\n            <Link\r\n              href=\"/adminsettings/[...routes]\"\r\n              as=\"/adminsettings/create_institution_network\"\r\n              >\r\n              <Button variant=\"secondary\" size=\"sm\">\r\n              {t(\"adminsetting.Organisationnetworks.AddOrganisationNetwork\")}\r\n              </Button>\r\n            </Link>\r\n          </Col>\r\n        </Row>\r\n        <Row className=\"mt-3\">\r\n          <Col xs={12}>\r\n            <InstitutionNetworkTable />\r\n          </Col>\r\n        </Row>\r\n      </Container>\r\n    );\r\n  }\r\n  \r\n  const ShowAddOrganisationNetworks = canAddOrganisationNetworks(() => <ShowInstitutionNetworkIndex />);\r\n  const state:any = useSelector((state) => state);\r\n  if (!(state?.permissions?.institution_network?.['create:any'])) {\r\n    return <NoAccessMessage />\r\n  }\r\n  return(\r\n    <ShowAddOrganisationNetworks />\r\n  )\r\n}\r\nexport default InstitutionNetworkIndex;"], "names": ["create", "canAddAreaOfWork", "connectedAuthWrapper", "authenticatedSelector", "state", "permissions", "area_of_work", "wrapperDisplayName", "country", "deployment_status", "event_status", "expertise", "institution_focal_point", "hazard", "hazard_type", "institution", "canAddOrganisationNetworks", "institution_network", "canAddOrganisationTypes", "institution_type", "operation_status", "project_status", "region", "risk_level", "syndrome", "update_type", "users", "worl_region", "landing_page", "operation", "project", "event", "vspace", "update", "tabledata", "setDataToTable", "useState", "InstitutionNetworkTable", "setLoading", "totalRows", "setTotalRows", "perPage", "setPerPage", "isModalShow", "setModal", "selectInstitutionNetwork", "setSelectInstitutionNetwork", "modalHide", "t", "useTranslation", "Networkpopover", "Popover", "id", "Header", "as", "className", "Body", "div", "p", "b", "columns", "name", "OverlayTrigger", "trigger", "placement", "overlay", "span", "i", "style", "cursor", "aria-hidden", "selector", "cell", "Link", "href", "d", "_id", "a", "onClick", "userAction", "institutionNetworkParams", "sort", "title", "limit", "page", "query", "useEffect", "getInstitutionNetworkData", "response", "apiService", "get", "data", "length", "totalCount", "handlePerRowsChange", "newPerPage", "modalConfirm", "remove", "toast", "success", "error", "row", "Modal", "show", "onHide", "closeButton", "Title", "Footer", "<PERSON><PERSON>", "variant", "RKITable", "pagServer", "handlePageChange", "props", "paginationComponentOptions", "rowsPerPageText", "resetPaginationToggle", "subheader", "subHeaderComponent", "rowsPerPage", "defaultRowsPerPage", "selectableRows", "loading", "onSelectedRowsChange", "clearSelectedRows", "sortServer", "onSort", "persistTableHead", "sortFunction", "otherProps", "dataTableProps", "noDataComponent", "<PERSON><PERSON><PERSON><PERSON>", "dense", "paginationResetDefaultPage", "subHeader", "progressPending", "pagination", "paginationServer", "paginationPerPage", "paginationRowsPerPageOptions", "paginationTotalRows", "onChangeRowsPerPage", "onChangePage", "progressComponent", "CustomLoader", "sortIcon", "DataTable", "defaultProps", "NoAccessMessage", "_props", "PageHeading", "h2", "ShowInstitutionNetworkIndex", "Container", "overflowX", "fluid", "Row", "Col", "xs", "size", "ShowAddOrganisationNetworks", "useSelector"], "sourceRoot": "", "ignoreList": []}