{"version": 3, "file": "static/chunks/pages/event/[...routes]-f36a58327e169980.js", "mappings": "gFACA,4CACA,qBACA,WACA,OAAe,EAAQ,KAA0C,CACjE,EACA,SAFsB,kICiCtB,MA5Be,KAEb,IAAMA,EAAcC,CADLC,EAAAA,EAAAA,SAAAA,CAASA,EA2BXC,CA1BcC,KAAK,CAACJ,CA0Bb,KA1BmB,EAAI,EAAE,CACvCK,EAAuBC,CAAAA,EAAAA,EAAAA,eAAAA,CAAeA,CAAC,IAAM,UAACC,EAAAA,OAASA,CAAAA,CAACP,OAAQA,KAEtE,OAAQA,CAAM,CAAC,EAAE,EACf,IAAK,SACH,MAAO,UAACK,EAAAA,CAAAA,EAEV,KAAK,OACH,MAAO,UAACE,EAAAA,OAASA,CAAAA,CAACP,OAAQA,GAE5B,KAAK,OACH,MAAO,UAACQ,EAAAA,OAASA,CAAAA,CAACR,OAAQA,GAE5B,SACE,OAAO,IACX,CACF,uHClBA,IAAMS,EAAiB,CACrBC,UAAW,YACXC,YAAa,cACbC,MAAO,QACPC,QAAS,UACTC,OAAQ,QACV,EAoEA,EAAeC,CAAAA,EAAAA,EAAAA,EAAAA,CAAOA,CAAC,GAAgBC,GA1DG,IACxC,GAAM,CAAEC,KAyD6CC,CAzDzC,CAyD0C,SAzDxCC,CAAQ,YAAEC,CAAU,CAAE,CAAGC,EACjC,CAACC,EAAUC,EAAY,CAAGC,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAU,IAC5C,CAACC,EAAWC,EAAa,CAAGF,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAM,IAG1CG,EAA2B,UAC/B,GAAI,QAACV,EAAAA,KAAAA,EAAAA,EAAMW,GAAAA,EAAK,CAAXX,MACL,IAAMY,EAAY,MAAMC,EAAAA,CAAUA,CAACC,GAAG,CAAC,QAAS,CAAC3B,MAAO,CAAC4B,UAAWb,EAAUF,KAAMA,EAAKW,GAAG,CAAEK,QAASxB,CAAc,CAACW,EAAW,CAAC,GAC9HS,GAAaA,EAAUK,IAAI,EAAIL,EAAUK,IAAI,CAACC,MAAM,CAAG,GAAG,CAC5DT,EAAaG,EAAUK,IAAI,CAAC,EAAE,EAC9BX,GAAY,GAEhB,EAEMa,EAAkB,MAAOC,IAE7B,GADAA,EAAEC,cAAc,GACZ,QAACrB,EAAAA,KAAAA,EAAAA,EAAMW,GAAAA,EAAK,CAAXX,MACL,IAAMsB,EAAQ,CAACjB,EACTkB,EAAc,CAClBC,YAAarB,EACbY,UAAWb,EACXF,KAAMA,EAAKW,GAAG,CACdK,QAASxB,CAAc,CAACW,EAAW,EAErC,GAAImB,EAAM,CACR,IAAMG,EAAc,MAAMZ,EAAAA,CAAUA,CAACa,IAAI,CAAC,QAASH,GAC/CE,GAAUA,EAAOd,GAAG,EAAE,CACxBF,EAAagB,GACbnB,EAAYgB,GAEhB,KAAO,CACL,IAAMK,EAAW,MAAMd,EAAAA,CAAUA,CAACe,MAAM,CAAC,SAAuB,OAAdpB,EAAUG,GAAG,GAC3DgB,GAAYA,EAASE,CAAC,EAAE,EACdP,EAEhB,CACF,EAKA,MAHAQ,CAAAA,EAAAA,EAAAA,SAAAA,CAASA,CAAC,KACRpB,GACF,EAAE,EAAE,EAEF,UAACqB,MAAAA,CAAIC,UAAU,0BACb,WAACC,IAAAA,CAAEC,KAAK,GAAGC,QAAShB,YAClB,UAACiB,OAAAA,CAAKJ,UAAU,iBACb3B,EACC,UAACgC,EAAAA,CAAeA,CAAAA,CAACL,UAAU,sBAAsBM,KAAMC,EAAAA,GAAaA,CAAEC,MAAM,YAE5E,UAACH,EAAAA,CAAeA,CAAAA,CAACL,UAAU,sBAAsBM,KAAMG,EAAAA,GAAYA,CAAED,MAAM,WAG/E,UAACH,EAAAA,CAAeA,CAAAA,CAACL,UAAU,WAAWM,KAAMI,EAAAA,GAAUA,CAAEF,MAAM,gBAItE,iNCdA,MArD2B,IACvB,GAAM,CAAEG,CAAC,CAAE,CAAGC,CAAAA,EAAAA,EAAAA,EAAAA,CAAcA,CAAC,QAoDlBC,EAlDLC,EAAqB,IAEnB,UAACC,EAgDqB,EAhDjBA,CACDb,KAAK,qBACLc,GAAI,eAFHD,MAE4C,CAA1B3C,EAAM6C,SAAS,CAAClE,MAAM,CAAC,EAAE,WAE5C,WAACmE,EAAAA,CAAMA,CAAAA,CAACC,QAAQ,YAAYC,KAAK,eAC7B,UAACf,EAAAA,CAAeA,CAAAA,CAACC,KAAMe,EAAAA,GAAKA,GAAI,OACzBV,EAAE,yBAMnBW,EAAeC,CAAAA,EAAAA,EAAAA,YAAAA,CAAYA,CAAC,IAAM,UAACT,EAAAA,CAAAA,IAEzC,MACI,+BACI,WAACU,EAAAA,CAAGA,CAAAA,WACA,WAACC,EAAAA,CAAGA,CAAAA,CAACC,GAAI,aACJC,SAWRA,MAISvD,EAQqBA,EAXnC,MACI,UAAC2B,MAAAA,CAAIC,UAAU,0CACX,WAAC4B,KAAAA,WACKxD,OAAAA,GAAAA,OAAAA,EAAAA,EAAOyD,IAAPzD,KAAOyD,EAAPzD,KAAAA,EAAAA,EAAkB0D,GAAlB1D,IAAyB,EACrB,GAAsC2D,MAAAA,CAAnC3D,EAAMyD,SAAS,CAACC,OAAO,CAACE,KAAK,CAAC,OAI3B5D,MAAAA,CAJgC2D,OACpC3D,EAAMyD,SAAS,CAACI,MAAM,CACfC,GAAG,CAAC,GAAgBC,GAAQA,EAAKH,KAAK,EAAIG,EAAKH,KAAK,CAACI,EAAE,CAAGD,EAAKH,KAAK,CAACI,EAAE,CAAG,IAC1EC,IAAI,CAAC,OACZ,MAA0B,OAAtBjE,EAAMyD,SAAS,CAACG,KAAK,CAAC,KAC5B,GAAG,WAEP5D,SAAAA,KAAAA,EAAAA,EAAAA,UAAOkE,UAAclE,GAAAA,OAAAA,EAAAA,EAAO6C,IAAP7C,KAAO6C,EAAP7C,KAAAA,EAAAA,EAAkBrB,GAAlBqB,GAAwB,CAAC,EAAE,EAAG,UAACkD,EAAAA,CAAa3D,MAAOS,EAAMyD,SAAS,GAAO,SAIhH,IA1BgB,UAACU,KAAAA,CAAAA,GACD,UAACC,EAAAA,CAAiBA,CAAAA,CAACC,YAAarE,EAAMyD,SAAS,CAACY,WAAW,MAE/D,UAAChB,EAAAA,CAAGA,CAAAA,CAACC,GAAI,WACL,UAACgB,EAAAA,CAAQA,CAAAA,CAACxE,SAAUE,EAAM6C,SAAS,CAAClE,MAAM,CAAC,EAAE,CAAEoB,WAAW,gBAuB9E,yJC2BA,MA7EkB,IACd,GAAM,CAACmE,EAAYK,EAAc,CAAGpE,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CA4EjChB,CA5EkC,GACvC,CAACsE,EAAWe,EAAa,CAAGrE,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAM,CAC5CyD,MAAO,GACPa,qBAAsB,GACtBC,qBAAsB,GACtBC,OAAQ,CAAEf,MAAO,EAAG,EACpBgB,SAAU,CAAEhB,MAAO,EAAG,EACtBF,QAAS,CAAEE,MAAO,EAAG,EACrBiB,YAAa,CAAEjB,MAAO,EAAG,EACzBkB,cAAe,GACfC,gBAAiB,CACbrB,QAAS,CAAEE,MAAO,EAAG,EACrBoB,cAAe,CAAEpB,MAAO,EAAG,EAC3BqB,OAAQ,CAAErB,MAAO,EAAG,CACxB,EACAC,OAAQ,EAAE,CACVqB,gBAAiB,EAAE,CACnB7F,UAAW,CAAEuE,MAAO,EAAG,EACvBS,YAAa,GACbc,UAAW,GACXC,OAAQ,EAAE,CACVC,WAAY,EAAE,GAGZC,EAAe,MAAOC,EAAkBC,KAC1C,IAAMC,EAAW,MAAMhF,EAAAA,CAAUA,CAACC,GAAG,CAAC,UAA0B,OAAhBV,EAAMrB,MAAM,CAAC,EAAE,EAAI4G,GAC/DE,IACAjB,EAAaiB,GACbC,CAFU,QAgBTA,CAAiC,CAAEC,CAAc,EACtDpB,GAAc,GACVoB,GAAaA,EAAU,KAAQ,EAAT,CAClBA,EAAU,KAAQ,CAACC,CAAV,OAAkB,CAAC,gBAAgB,EAG3B,KAAQ,CAACA,CAAV,OAAkB,CAAC,iBAAmBnC,EAAU7D,IAAI,CAACW,GAAG,EAAIoF,EAAU,GAAM,CAE5FpB,CAF8F,CAEhF,CAFuE,GAIhFoB,EAAU,KAAQ,CAACC,CAAV,OAAkB,CAAC,mBAAqBnC,EAAU7D,IAAI,CAACW,GAAG,EAAIoF,EAAU,GAAM,EAAE,EAAT,CAEvE,GAG1B,EA7B2BF,EAAUD,GAErC,EACA9D,CAAAA,EAAAA,EAAAA,SAAAA,CAASA,CAAC,KACF1B,EAAMrB,MAAM,EAAIqB,EAAMrB,MAAM,CAAC,EAAE,EAAE,GAGzC,EAAG,EAAE,EAEL,IAAMkH,EAAkB,UAEpBP,EAAa,CAAC,EADD,CACIzE,KADEJ,EAAAA,CAAUA,CAACa,IAAI,CAAC,uBAAwB,CAAC,GAEhE,EAmBMwE,EAAY,CACdrC,UAAYA,EACZZ,UAAY7C,EACZkE,WAAYA,CAChB,EAEA,MACI,WAAC6B,EAAAA,CAASA,CAAAA,CAACnE,UAAU,cAAcoE,KAAK,cACpC,UAACC,EAAAA,CAAWA,CAAAA,CAACtH,OAAQqB,EAAMrB,MAAM,GACjC,UAAC8D,EAAAA,OAAkBA,CAAAA,CAAG,GAAGqD,CAAS,GAClC,UAACI,EAAAA,OAAiBA,CAAAA,CAACzC,UAAcA,IACjC,UAAC0C,EAAAA,OAAqBA,CAAAA,CAAG,GAAGL,CAAS,KAIjD", "sources": ["webpack://_N_E/?4d68", "webpack://_N_E/./pages/event/[...routes].tsx", "webpack://_N_E/./components/common/Bookmark.tsx", "webpack://_N_E/./pages/event/components/EventHeaderSection.tsx", "webpack://_N_E/./pages/event/EventShow.tsx"], "sourcesContent": ["\n    (window.__NEXT_P = window.__NEXT_P || []).push([\n      \"/event/[...routes]\",\n      function () {\n        return require(\"private-next-pages/event/[...routes].tsx\");\n      }\n    ]);\n    if(module.hot) {\n      module.hot.dispose(function () {\n        window.__NEXT_P.push([\"/event/[...routes]\"])\n      });\n    }\n  ", "//Import Library\r\nimport { useRouter } from \"next/router\";\r\n\r\n//Import services/components\r\nimport EventShow from \"./EventShow\";\r\nimport EventForm from \"./Form\";\r\nimport { canAddEventForm } from \"./permission\";\r\nimport { serverSideTranslations } from 'next-i18next/serverSideTranslations';\r\n\r\nconst Router = () => {\r\n  const router = useRouter();\r\n  const routes: any = router.query.routes || [];\r\n  const CanAccessCreateForm  = canAddEventForm(() => <EventForm routes={routes} />)\r\n\r\n  switch (routes[0]) {\r\n    case \"create\":\r\n      return <CanAccessCreateForm />\r\n\r\n    case \"edit\":\r\n      return <EventForm routes={routes} />;\r\n\r\n    case \"show\":\r\n      return <EventShow routes={routes} />;\r\n\r\n    default:\r\n      return null;\r\n  }\r\n};\r\n\r\nexport async function getServerSideProps({ locale }: { locale: string }) {\r\n  return {\r\n    props: {\r\n      ...(await serverSideTranslations(locale, ['common'])),\r\n    },\r\n  }\r\n}\r\n\r\nexport default Router;\r\n", "//Import Library\r\nimport { connect } from \"react-redux\";\r\nimport {faBookmark, faCheckCircle, faPlusCircle} from \"@fortawesome/free-solid-svg-icons\";\r\nimport {useEffect, useState} from \"react\";\r\nimport {FontAwesomeIcon} from \"@fortawesome/react-fontawesome\";\r\n\r\n//Import services/components\r\nimport apiService from \"../../services/apiService\";\r\n\r\nconst onModelOptions = {\r\n  operation: \"Operation\",\r\n  institution: \"Institution\",\r\n  event: \"Event\",\r\n  project: \"Project\",\r\n  vspace: \"Vspace\"\r\n} as const;\r\n\r\ninterface BookMarkProps {\r\n  user?: {\r\n    _id: string;\r\n  };\r\n  entityId: string;\r\n  entityType: keyof typeof onModelOptions;\r\n}\r\n\r\nconst BookMark: React.FC<BookMarkProps> = (props) => {\r\n  const { user, entityId, entityType } = props;\r\n  const [bookmark, setBookmark] = useState<boolean>(false);\r\n  const [subscribe, setSubscribe] = useState<any>(\"\");\r\n\r\n  //Handle bookmark feature\r\n  const fetchIfContentSubscribed = async () => {\r\n    if (!user?._id) return;\r\n    const checkFlag = await apiService.get('/flag', {query: {entity_id: entityId, user: user._id, onModel: onModelOptions[entityType]}});\r\n    if (checkFlag && checkFlag.data && checkFlag.data.length > 0) {\r\n      setSubscribe(checkFlag.data[0]);\r\n      setBookmark(true);\r\n    }\r\n  };\r\n\r\n  const bookmarkHandler = async (e: React.MouseEvent<HTMLAnchorElement>) => {\r\n    e.preventDefault();\r\n    if (!user?._id) return;\r\n    const flag  = !bookmark;\r\n    const flagPayload = {\r\n      entity_type: entityType,\r\n      entity_id: entityId,\r\n      user: user._id,\r\n      onModel: onModelOptions[entityType]\r\n    }\r\n    if (flag) {\r\n      const flagIt: any = await apiService.post('/flag', flagPayload);\r\n      if (flagIt && flagIt._id) {\r\n        setSubscribe(flagIt);\r\n        setBookmark(flag);\r\n      }\r\n    } else {\r\n      const unFlagIt = await apiService.remove(`/flag/${subscribe._id}`);\r\n      if (unFlagIt && unFlagIt.n) {\r\n        setBookmark(flag);\r\n      }\r\n    }\r\n  }\r\n  //END Bookmark handler\r\n  useEffect(() => {\r\n    fetchIfContentSubscribed();\r\n  },[]);\r\n  return (\r\n    <div className=\"subscribe-flag\">\r\n      <a href=\"\" onClick={bookmarkHandler}>\r\n        <span className=\"check\">\r\n          {bookmark ?\r\n            <FontAwesomeIcon className=\"clickable checkIcon\" icon={faCheckCircle} color=\"#00CC00\" />\r\n            :\r\n            <FontAwesomeIcon className=\"clickable minusIcon\" icon={faPlusCircle} color=\"#fff\"/>\r\n          }\r\n        </span>\r\n        <FontAwesomeIcon className=\"bookmark\" icon={faBookmark} color=\"#d4d4d4\"/>\r\n      </a>\r\n    </div>\r\n  )\r\n}\r\n\r\nexport default connect((state: any) => state)(BookMark);\r\n\r\n", "//Import Library\r\nimport React from 'react';\r\nimport { But<PERSON>, Col, Row } from \"react-bootstrap\";\r\nimport Link from \"next/link\";\r\nimport { FontAwesomeIcon } from \"@fortawesome/react-fontawesome\";\r\nimport { faPen } from \"@fortawesome/free-solid-svg-icons\";\r\n\r\n//Import services/components\r\nimport ReadMoreContainer from \"../../../components/common/readMore/readMore\";\r\nimport Bookmark from \"../../../components/common/Bookmark\";\r\nimport { useTranslation } from 'next-i18next';\r\nimport { canEditEvent } from \"../permission\";\r\n\r\n\r\nconst EventHeaderSection = (props: any) => {\r\n    const { t } = useTranslation('common');\r\n\r\n    const EditEventComponent = () => {\r\n        return (\r\n            <Link\r\n                href=\"/event/[...routes]\"\r\n                as={`/event/edit/${props.routeData.routes[1]}`}\r\n                >\r\n                <Button variant=\"secondary\" size=\"sm\">\r\n                    <FontAwesomeIcon icon={faPen} />\r\n                    &nbsp;{t(\"Events.show.Edit\")}\r\n                </Button>\r\n            </Link>\r\n        );\r\n    };\r\n\r\n    const CanEditEvent = canEditEvent(() => <EditEventComponent />);\r\n    \r\n    return (\r\n        <>\r\n            <Row>\r\n                <Col md={11}>\r\n                    {eventdata_func()}\r\n                    <hr />\r\n                    <ReadMoreContainer description={props.eventData.description} />\r\n                </Col>\r\n                <Col md={1}>\r\n                    <Bookmark entityId={props.routeData.routes[1]} entityType=\"event\" />\r\n                </Col>\r\n            </Row>\r\n        </>\r\n    );\r\n\r\n    function eventdata_func() {\r\n        return (\r\n            <div className=\"d-flex justify-content-between\">\r\n                <h4>\r\n                    { props?.eventData?.country\r\n                        ? `${props.eventData.country.title} | ${String(\r\n                            props.eventData.hazard\r\n                                  .map((item: any) => (item && item.title && item.title.en ? item.title.en : \"\"))\r\n                                  .join(\", \")\r\n                          )} (${props.eventData.title})`\r\n                        : \"\"}\r\n                    &nbsp;&nbsp;\r\n                    { props?.editAccess && props?.routeData?.routes[1] ? <CanEditEvent event={props.eventData} /> : null}\r\n                </h4>\r\n            </div>\r\n        );\r\n    }\r\n}\r\n\r\nexport default EventHeaderSection;", "//Import Library\r\nimport React, { useEffect, useState } from \"react\";\r\nimport { Container } from \"react-bootstrap\";\r\n\r\n//Import services/components\r\nimport UpdatePopup from \"../../components/updates/UpdatePopup\";\r\nimport apiService from \"../../services/apiService\";\r\nimport EventHeaderSection from \"./components/EventHeaderSection\";\r\nimport EventCoverSection from \"./components/EventCoverSection\";\r\nimport EventAccordionSection from \"./components/EventAccordionSection\";\r\n\r\ninterface EventShowProps {\r\n  routes: string[];\r\n}\r\n\r\nconst EventShow = (props: EventShowProps) => {\r\n    const [editAccess, setEditAccess] = useState(false);\r\n    const [eventData, setEventData] = useState<any>({\r\n        title: \"\",\r\n        laboratory_confirmed: \"\",\r\n        officially_validated: \"\",\r\n        status: { title: \"\" },\r\n        syndrome: { title: \"\" },\r\n        country: { title: \"\" },\r\n        hazard_type: { title: \"\" },\r\n        rki_monitored: \"\",\r\n        risk_assessment: {\r\n            country: { title: \"\" },\r\n            international: { title: \"\" },\r\n            region: { title: \"\" },\r\n        },\r\n        hazard: [],\r\n        country_regions: [],\r\n        operation: { title: \"\" },\r\n        description: \"\",\r\n        more_info: \"\",\r\n        images: [],\r\n        images_src: [],\r\n    });\r\n\r\n    const getEventData = async (eventParams: any, loginUserData: any) => {\r\n        const response = await apiService.get(`/event/${props.routes[1]}`, eventParams);\r\n        if (response) {\r\n            setEventData(response);\r\n            getEventEditAccess(response, loginUserData);\r\n        }\r\n    };\r\n    useEffect(() => {\r\n        if (props.routes && props.routes[1]) {\r\n            getLoggedInUser();\r\n        }\r\n    }, []);\r\n\r\n    const getLoggedInUser = async () => {\r\n        const data = await apiService.post(\"/users/getLoggedUser\", {});\r\n        getEventData({}, data);\r\n    };\r\n\r\n    function getEventEditAccess(eventData: any, loginUser: any) {\r\n        setEditAccess(false);\r\n        if (loginUser && loginUser['roles']) {\r\n            if (loginUser['roles'].includes(\"SUPER_ADMIN\")) {\r\n                //SUPER_ADMIN can Edit all organisations\r\n                setEditAccess(true);\r\n            } else if (loginUser['roles'].includes(\"GENERAL_USER\") && eventData.user._id == loginUser['_id']) {\r\n                //\"GENERAL_USER\" can Edit organisations which is added by them only\r\n                setEditAccess(true);\r\n            }\r\n            else if (loginUser['roles'].includes(\"PLATFORM_ADMIN\") && eventData.user._id == loginUser['_id']) {\r\n                //\"PLATFORM_ADMIN\" can Edit organisations which is added by them only\r\n                setEditAccess(true);\r\n            }\r\n        }\r\n    }\r\n\r\n    const propsData = {\r\n        eventData : eventData,\r\n        routeData : props,\r\n        editAccess: editAccess\r\n    }\r\n\r\n    return (\r\n        <Container className=\"eventDetail\" fluid>\r\n            <UpdatePopup routes={props.routes} />\r\n            <EventHeaderSection { ...propsData } />\r\n            <EventCoverSection eventData = { eventData } />\r\n            <EventAccordionSection { ...propsData } />\r\n        </Container>\r\n    );\r\n\r\n};\r\n\r\nexport default EventShow;\r\n"], "names": ["routes", "router", "useRouter", "Router", "query", "CanAccessCreateForm", "canAddEventForm", "EventForm", "EventShow", "onModelOptions", "operation", "institution", "event", "project", "vspace", "connect", "state", "user", "BookMark", "entityId", "entityType", "props", "bookmark", "setBookmark", "useState", "subscribe", "setSubscribe", "fetchIfContentSubscribed", "_id", "checkFlag", "apiService", "get", "entity_id", "onModel", "data", "length", "bookmarkHandler", "e", "preventDefault", "flag", "flagPayload", "entity_type", "flagIt", "post", "unFlagIt", "remove", "n", "useEffect", "div", "className", "a", "href", "onClick", "span", "FontAwesomeIcon", "icon", "faCheckCircle", "color", "faPlusCircle", "faBookmark", "t", "useTranslation", "EventHeaderSection", "EditEventComponent", "Link", "as", "routeData", "<PERSON><PERSON>", "variant", "size", "faPen", "CanEditEvent", "canEditEvent", "Row", "Col", "md", "eventdata_func", "h4", "eventData", "country", "String", "title", "hazard", "map", "item", "en", "join", "editAccess", "hr", "ReadMoreContainer", "description", "Bookmark", "setEditAccess", "setEventData", "laboratory_confirmed", "officially_validated", "status", "syndrome", "hazard_type", "rki_monitored", "risk_assessment", "international", "region", "country_regions", "more_info", "images", "images_src", "getEventData", "eventParams", "loginUserData", "response", "getEventEditAccess", "loginUser", "includes", "getLoggedInUser", "propsData", "Container", "fluid", "UpdatePopup", "EventCoverSection", "EventAccordionSection"], "sourceRoot": "", "ignoreList": []}