"use strict";(()=>{var e={};e.id=9702,e.ids=[636,3220,9702],e.modules={123:e=>{e.exports=require("dom-helpers/removeEventListener")},1332:e=>{e.exports=require("react-custom-scrollbars-2")},1428:e=>{e.exports=import("axios")},1680:e=>{e.exports=require("@restart/ui/utils")},1919:e=>{e.exports=require("react-transition-group/Transition")},3892:e=>{e.exports=require("classnames")},4048:e=>{e.exports=require("react-truncate")},6009:e=>{e.exports=require("dom-helpers/ownerDocument")},6952:e=>{e.exports=require("@restart/ui/Modal")},7374:e=>{e.exports=require("@restart/ui/Dropdown")},8732:e=>{e.exports=require("react/jsx-runtime")},9653:e=>{e.exports=require("@restart/ui/Overlay")},11242:e=>{e.exports=require("redux-persist/integration/react")},11688:e=>{e.exports=require("@restart/hooks/useWillUnmount")},12388:e=>{e.exports=require("dom-helpers/addEventListener")},13364:e=>{e.exports=require("redux-saga/effects")},14062:e=>{e.exports=import("react-redux")},14078:e=>{e.exports=import("swr")},14332:e=>{e.exports=require("uncontrollable")},15653:(e,r,t)=>{t.d(r,{ks:()=>a,s3:()=>n});var s=t(8732);t(82015);var o=t(59549),i=t(43294);let a=({name:e,id:r,required:t,validator:a,errorMessage:n,onChange:u,value:p,as:l,multiline:d,rows:x,pattern:c,...m})=>(0,s.jsx)(i.Field,{name:e,validate:e=>{let r="string"==typeof e?e:String(e||"");return t&&(!e||""===r.trim())?n?.validator||"This field is required":a&&!a(e)?n?.validator||"Invalid value":c&&e&&!new RegExp(c).test(e)?n?.pattern||"Invalid format":void 0},children:({field:e,meta:t})=>(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)(o.A.Control,{...e,...m,id:r,as:l||"input",rows:x,isInvalid:t.touched&&!!t.error,onChange:r=>{e.onChange(r),u&&u(r)},value:void 0!==p?p:e.value}),t.touched&&t.error?(0,s.jsx)(o.A.Control.Feedback,{type:"invalid",children:t.error}):null]})}),n=({name:e,id:r,required:t,errorMessage:a,onChange:n,value:u,children:p,...l})=>(0,s.jsx)(i.Field,{name:e,validate:e=>{if(t&&(!e||""===e))return a?.validator||"This field is required"},children:({field:e,meta:t})=>(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)(o.A.Control,{as:"select",...e,...l,id:r,isInvalid:t.touched&&!!t.error,onChange:r=>{e.onChange(r),n&&n(r)},value:void 0!==u?u:e.value,children:p}),t.touched&&t.error?(0,s.jsx)(o.A.Control.Feedback,{type:"invalid",children:t.error}):null]})})},16116:e=>{e.exports=require("invariant")},18622:e=>{e.exports=require("yup")},19121:e=>{e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},22326:e=>{e.exports=require("react-dom")},22541:e=>{e.exports=require("redux-persist/lib/storage")},23579:(e,r,t)=>{t.d(r,{sx:()=>l,s3:()=>o.s3,ks:()=>o.ks,yk:()=>s.A});var s=t(66994),o=t(15653),i=t(8732),a=t(82015),n=t.n(a),u=t(43294),p=t(59549);let l={RadioGroup:({name:e,valueSelected:r,onChange:t,errorMessage:s,children:o})=>{let{errors:a,touched:p}=(0,u.useFormikContext)(),l=p[e]&&a[e];n().useMemo(()=>({name:e}),[e]);let d=n().Children.map(o,r=>n().isValidElement(r)&&function(e){return"object"==typeof e&&null!==e}(r.props)?n().cloneElement(r,{name:e,...r.props}):r);return(0,i.jsxs)("div",{children:[(0,i.jsx)("div",{className:"radio-group",children:d}),l&&(0,i.jsx)("div",{className:"invalid-feedback d-block",children:s||("string"==typeof a[e]?a[e]:String(a[e]))})]})},RadioItem:({id:e,label:r,value:t,name:s,disabled:o})=>{let{values:a,setFieldValue:n}=(0,u.useFormikContext)(),l=s||e;return(0,i.jsx)(p.A.Check,{type:"radio",id:e,label:r,value:t,name:l,checked:a[l]===t,onChange:e=>{n(l,e.target.value)},disabled:o,inline:!0})}};s.A,o.ks,o.s3},25303:e=>{e.exports=require("@restart/ui/NavItem")},26324:e=>{e.exports=require("warning")},27825:e=>{e.exports=require("lodash")},27910:e=>{e.exports=require("stream")},28217:e=>{e.exports=require("@restart/ui/DropdownItem")},29021:e=>{e.exports=require("fs")},29294:e=>{e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},29628:e=>{e.exports=require("dom-helpers/addClass")},29825:e=>{e.exports=require("prop-types")},29841:e=>{e.exports=require("dom-helpers/hasClass")},30362:e=>{e.exports=require("@restart/ui/DropdownToggle")},33873:e=>{e.exports=require("path")},36653:e=>{e.exports=require("nprogress")},36955:e=>{e.exports=require("@restart/hooks/useBreakpoint")},37247:(e,r,t)=>{t.a(e,async(e,s)=>{try{t.r(r),t.d(r,{config:()=>q,default:()=>x,getServerSideProps:()=>h,getStaticPaths:()=>m,getStaticProps:()=>c,reportWebVitals:()=>v,routeModule:()=>A,unstable_getServerProps:()=>P,unstable_getServerSideProps:()=>S,unstable_getStaticParams:()=>b,unstable_getStaticPaths:()=>f,unstable_getStaticProps:()=>g});var o=t(63885),i=t(80237),a=t(81413),n=t(9616),u=t.n(n),p=t(72386),l=t(45268),d=e([p]);p=(d.then?(await d)():d)[0];let x=(0,a.M)(l,"default"),c=(0,a.M)(l,"getStaticProps"),m=(0,a.M)(l,"getStaticPaths"),h=(0,a.M)(l,"getServerSideProps"),q=(0,a.M)(l,"config"),v=(0,a.M)(l,"reportWebVitals"),g=(0,a.M)(l,"unstable_getStaticProps"),f=(0,a.M)(l,"unstable_getStaticPaths"),b=(0,a.M)(l,"unstable_getStaticParams"),P=(0,a.M)(l,"unstable_getServerProps"),S=(0,a.M)(l,"unstable_getServerSideProps"),A=new o.PagesRouteModule({definition:{kind:i.A.PAGES,page:"/updates/ConversationForm",pathname:"/updates/ConversationForm",bundlePath:"",filename:""},components:{App:p.default,Document:u()},userland:l});s()}catch(e){s(e)}})},37766:e=>{e.exports=require("@restart/hooks/useMergedRefs")},39756:e=>{e.exports=import("redux")},40051:e=>{e.exports=require("dom-helpers/removeClass")},40361:e=>{e.exports=require("next/dist/compiled/next-server/pages.runtime.prod.js")},42893:e=>{e.exports=import("react-hot-toast")},43294:e=>{e.exports=require("formik")},45268:(e,r,t)=>{t.r(r),t.d(r,{default:()=>l});var s=t(8732);t(82015);var o=t(7082),i=t(83551),a=t(49481),n=t(59549),u=t(23579),p=t(88751);let l=e=>{let{t:r}=(0,p.useTranslation)("common"),{title:t,onHandleChange:l}=e;return(0,s.jsx)(o.A,{className:"formCard",fluid:!0,children:(0,s.jsx)(i.A,{children:(0,s.jsx)(a.A,{children:(0,s.jsxs)(n.A.Group,{children:[(0,s.jsxs)(n.A.Label,{className:"required-field",children:[r("update.Conversation")," ",r("update.Title")]}),(0,s.jsx)(u.ks,{name:"title",id:"title",required:!0,value:t,onChange:l,validator:e=>""!=e.trim(),errorMessage:{validator:r("Pleaseprovideconversationatitle")}})]})})})})}},50009:e=>{e.exports=require("@restart/ui/DropdownMenu")},57664:e=>{e.exports=require("dom-helpers/scrollbarSize")},58928:e=>{e.exports=require("dom-helpers/canUseDOM")},59717:e=>{e.exports=require("@restart/ui/DropdownContext")},60560:e=>{e.exports=require("dom-helpers/contains")},63033:e=>{e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},65447:e=>{e.exports=require("@restart/ui/Button")},66994:(e,r,t)=>{t.d(r,{A:()=>u});var s=t(8732),o=t(82015),i=t(43294),a=t(18622);let n=(0,o.forwardRef)((e,r)=>{let{children:t,onSubmit:o,autoComplete:n,className:u,onKeyPress:p,initialValues:l,...d}=e,x=a.object().shape({});return(0,s.jsx)(i.Formik,{initialValues:l||{},validationSchema:x,onSubmit:(e,r)=>{let t={preventDefault:()=>{},stopPropagation:()=>{},currentTarget:null,target:null,nativeEvent:new Event("submit"),bubbles:!1,cancelable:!0,defaultPrevented:!1,eventPhase:0,isTrusted:!1,timeStamp:Date.now(),type:"submit",isDefaultPrevented:()=>!1,isPropagationStopped:()=>!1,persist:()=>{}};o&&o(t,e,r)},...d,children:e=>(0,s.jsx)(i.Form,{ref:r,onSubmit:e.handleSubmit,autoComplete:n,className:u,onKeyPress:p,children:"function"==typeof t?t(e):t})})});n.displayName="ValidationFormWrapper";let u=n},67364:e=>{e.exports=require("@restart/hooks/useCallbackRef")},68455:e=>{e.exports=import("redux-saga")},69722:e=>{e.exports=require("es6-promise")},74075:e=>{e.exports=require("zlib")},74716:e=>{e.exports=require("moment")},74987:e=>{e.exports=require("@restart/ui/ModalManager")},78097:e=>{e.exports=require("next-redux-saga")},78634:e=>{e.exports=require("@restart/ui/Anchor")},80237:(e,r)=>{Object.defineProperty(r,"A",{enumerable:!0,get:function(){return t}});var t=function(e){return e.PAGES="PAGES",e.PAGES_API="PAGES_API",e.APP_PAGE="APP_PAGE",e.APP_ROUTE="APP_ROUTE",e.IMAGE="IMAGE",e}({})},81366:e=>{e.exports=require("redux-auth-wrapper/connectedAuthWrapper")},81413:(e,r)=>{Object.defineProperty(r,"M",{enumerable:!0,get:function(){return function e(r,t){return t in r?r[t]:"then"in r&&"function"==typeof r.then?r.then(r=>e(r,t)):"function"==typeof r&&"default"===t?r:void 0}}})},81521:e=>{e.exports=require("dom-helpers/querySelectorAll")},81895:e=>{e.exports=require("@restart/hooks/useEventCallback")},82015:e=>{e.exports=require("react")},86842:e=>{e.exports=require("@restart/ui/SelectableContext")},87571:e=>{e.exports=require("dom-helpers/transitionEnd")},88751:e=>{e.exports=require("next-i18next")},93787:e=>{e.exports=require("redux-persist")},94947:e=>{e.exports=require("@restart/hooks/useTimeout")},96196:e=>{e.exports=require("next-redux-wrapper")},98320:e=>{e.exports=require("@restart/hooks/useIsomorphicEffect")},99460:e=>{e.exports=require("dom-helpers/css")}};var r=require("../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[6089,9216,9616,2386],()=>t(37247));module.exports=s})();