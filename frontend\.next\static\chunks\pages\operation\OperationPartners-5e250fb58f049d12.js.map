{"version": 3, "file": "static/chunks/pages/operation/OperationPartners-5e250fb58f049d12.js", "mappings": "0KAqCA,SAASA,EAASC,CAAoB,EACpC,GAAM,GAAEC,CAAC,CAAE,CAAGC,CAAAA,EAAAA,EAAAA,EAAAA,CAAcA,CAAC,UACvBC,EAA6B,CACjCC,gBAAiBH,EAAE,cACnB,EACI,SACJI,CAAO,MACPC,CAAI,WACJC,CAAS,uBACTC,CAAqB,WACrBC,CAAS,oBACTC,CAAkB,qBAClBC,CAAmB,kBACnBC,CAAgB,aAChBC,CAAW,oBACXC,CAAkB,gBAClBC,CAAc,SACdC,CAAO,WACPC,CAAS,sBACTC,CAAoB,CACpBC,mBAAiB,YACjBC,CAAU,QACVC,CAAM,kBACNC,CAAgB,cAChBC,CAAY,CAEZ,CADA,EACGC,EACJ,CAAGxB,EAGEyB,EAAiB,4BACrBtB,EACAuB,gBAAiBzB,EAAE,IAP0C,MAQ7D0B,UAAU,UACVtB,EACAC,KAAMA,GAAQ,EAAE,CAChBsB,OAAO,EACPC,2BAA4BrB,EAC5BsB,UAAWrB,EACXsB,gBAAiBf,qBACjBN,EACAsB,WAAY,GACZC,iBAAkBhB,EAClBiB,kBAAmBpB,GAA0C,GAC7DqB,eADwCrB,cACVD,GAA4B,CAAC,GAAI,GAAI,GAAI,CAA3BA,EAA+B,GAAG,CAC9EuB,oBAAqB7B,EACrB8B,oBAAqB1B,EACrB2B,aAAc1B,iBACdG,uBACAG,oBACAC,EACAoB,kBAAmB,UAACC,EAAAA,CAAYA,CAAAA,CAAAA,GAChCC,SAAU,UAACC,IAAAA,CAAEC,UAAU,6CACvBvB,SACAC,eACAE,EACAD,mBACAqB,UAAW,WACb,EACA,MACE,UAACC,EAAAA,EAASA,CAAAA,CAAE,GAAGnB,CAAc,EAEjC,CAEA1B,EAAS8C,YAAY,CAAG,CACtBf,UAAW,GACXE,YAAY,EACZzB,UAAW,KACXU,WAAW,EACXC,qBAAsB,KACtBC,mBAAmB,EACnBC,WAAY,GACZE,iBAAkB,EACpB,EAEA,MAAevB,QAAQA,EAAC,uJCvGxB,IAAM+C,EAAW,OAAC,UAAEC,CAAQ,CAAsB,UAChD,GAAgBA,EAASC,MAAM,CAAG,EAE9B,CAFiC,EAEjC,OAACC,KAAAA,UACEF,EAASG,GAAG,CAAC,CAACC,EAAMC,IACZ,UAACC,KAAAA,UAAgBF,EAAKG,KAAK,EAAlBF,MAKjB,IACT,EAGMG,EACJ,WAACC,EAAAA,CAAOA,CAAAA,CAACC,GAAG,0BACV,UAACD,EAAAA,CAAOA,CAACE,MAAM,EAACC,GAAG,KAAKhB,UAAU,uBAAc,aAGhD,UAACa,EAAAA,CAAOA,CAACI,IAAI,WACX,WAACC,MAAAA,CAAIlB,UAAU,gBACb,WAACmB,IAAAA,WACC,UAACC,IAAAA,UAAE,UAAS,4BAEd,WAACD,IAAAA,WACC,UAACC,IAAAA,UAAE,QAAO,gCAEZ,WAACD,IAAAA,WACC,UAACC,IAAAA,UAAE,SAAQ,yCAEb,WAACD,IAAAA,WACC,UAACC,IAAAA,UAAE,UAAS,iDAEd,WAACD,IAAAA,WACC,UAACC,IAAAA,UAAE,WAAU,uEAGf,WAACD,IAAAA,WACC,UAACC,IAAAA,UAAE,WAAU,uEAGf,WAACD,IAAAA,WACC,UAACC,IAAAA,UAAE,UAAS,+DAkItB,EA3HA,SAASC,CAA4B,EACnC,GAAM,GAAE/D,CAAC,CAAE,CAAGC,CAAAA,EAAAA,EAAAA,EAAAA,CAAcA,CAAC,UACvB,KAyHwB8D,EAAC,GAzHvBC,CAAQ,CAAE,CAAGjE,EACfK,EAAU,CACd,CACE6D,KAAMjE,EAAE,gBACRkE,SAAU,QACVC,KAAM,GACJC,GAAKA,EAAEC,WAAW,CAChB,UAACC,IAAIA,CACHC,KAAK,2BACLb,GAAI,SAFDY,YAEwC,OAAlBF,EAAEC,WAAW,CAACG,GAAG,WAEzCJ,EAAEC,WAAW,CAAChB,KAAK,GAGtB,GAEJoB,UAAU,CACZ,EACA,CACER,KAAMjE,EAAE,WACRkE,SAAU,UACVC,KAAM,GACJC,GACAA,EAAEC,WAAW,EACbD,EAAEC,WAAW,CAACK,OAAO,EACrBN,EAAEC,WAAW,CAACK,OAAO,CAACC,OAAO,CAC3B,UAACL,IAAIA,CACHC,KAAK,uBACLb,GAAI,aAFDY,IAEoD,OAAlCF,EAAEC,WAAW,CAACK,OAAO,CAACC,OAAO,CAACH,GAAG,WAErDJ,EAAEC,WAAW,CAACK,OAAO,CAACC,OAAO,CAACtB,KAAK,GAGtC,GAEJoB,UAAU,CACZ,EACA,CACER,KAAMjE,EAAE,QACRkE,SAAU,aACVC,KAAM,GACJC,EAAEC,WAAW,EAAID,EAAEC,WAAW,CAACO,IAAI,EAAIR,EAAEC,WAAW,CAACO,IAAI,CAACvB,KAAK,CAC3De,EAAEC,WAAW,CAACO,IAAI,CAACvB,KAAK,CACxB,GACNoB,UAAU,CACZ,EACA,CACER,KACE,UAACY,EAAAA,CAAcA,CAAAA,CACbC,QAAQ,QACRC,UAAU,QACVC,QAAS1B,WAET,WAAC2B,OAAAA,WACEjF,EAAE,WAAW,eACd,UAACyC,IAAAA,CACCC,UAAU,oBACVwC,MAAO,CAAEC,OAAQ,SAAU,EAC3BC,cAAY,cAKpBlB,SAAUlE,EAAE,YACZmE,KAAM,GACJC,EAAEC,WAAW,EACbD,EAAEC,WAAW,CAACvB,QAAQ,EACtBsB,EAAEC,WAAW,CAACvB,QAAQ,CAACC,MAAM,CAAG,EAC9B,UAACF,EAAAA,CAASC,SAAUsB,EAAEC,WAAW,CAACvB,QAAQ,GAE1C,EAEN,EACD,CAEKuC,EAAY,IAChB,GAAIC,EAAIjB,WAAW,CAACK,OAAO,EAAIY,EAAIjB,WAAW,CAACK,OAAO,CAACC,OAAO,EAAE,MAE5DW,EAAIjB,WAAW,CAACK,OAAO,CAACC,OAAO,EAC/BW,EAAIjB,WAAW,CAACK,OAAO,CAACC,OAAO,CAACtB,KAAK,CAE9BiC,CADP,CACWjB,WAAW,CAACK,OAAO,CAACC,OAAO,CAACtB,KAAK,CAACkC,WAAW,GAEnDD,EAAIjB,WAAW,CAACK,OAAO,CAACC,OAAO,CAACtB,KAE3C,EAEMmC,EAAiB,IACrB,GAAIF,EAAIjB,WAAW,CAACO,IAAI,EAAE,EAChBP,WAAW,CAACO,IAAI,EAAIU,EAAIjB,WAAW,CAACO,IAAI,CAACvB,KAAK,CACpD,CADsD,MAC/CiC,EAAIjB,WAAW,CAACO,IAAI,CAACvB,KAAK,CAACkC,WAAW,EAGnD,EAiBA,MACE,UAACzF,EAAAA,CAAQA,CAAAA,CACPM,QAASA,EACTC,KAAM2D,EACNhD,WAAW,EACXK,gBAAgB,IAChBC,aArBe,CAACmE,EAAWC,EAAeC,IAYrCC,IAAAA,OAAS,CAACH,EAXG,IAWGI,GAVP,WAAW,CAArBH,EACFL,EAAUC,QACL,GAAc,cAAc,CAAxBI,EACTF,EAAeF,QAEf,GAAIA,EAAIjB,WAAW,EAAIiB,EAAIjB,WAAW,CAACqB,EAAM,CAC3C,CAD6C,MACtCJ,EAAIjB,WAAW,CAACqB,EAAM,CAACH,WAAW,EAG/C,EACoCI,IAYxC,mBClLA,4CACA,+BACA,WACA,OAAe,EAAQ,KAAoD,CAC3E,EACA,SAFsB", "sources": ["webpack://_N_E/./components/common/RKITable.tsx", "webpack://_N_E/./pages/operation/OperationPartners.tsx", "webpack://_N_E/?9755"], "sourcesContent": ["//Import Library\r\nimport React from 'react'\r\nimport DataTable  from 'react-data-table-component';\r\n\r\n//Import services/components\r\nimport { useTranslation } from 'next-i18next';\r\nimport CustomLoader from './CustomLoader';\r\n\r\ninterface RKITableProps {\r\n  columns: any[];\r\n  data: any[];\r\n  totalRows?: number;\r\n  resetPaginationToggle?: boolean;\r\n  subheader?: boolean;\r\n  subHeaderComponent?: React.ReactNode;\r\n  handlePerRowsChange?: (newPerPage: number, page: number) => void;\r\n  handlePageChange?: (page: number) => void;\r\n  rowsPerPage?: number[];\r\n  defaultRowsPerPage?: number;\r\n  selectableRows?: boolean;\r\n  loading?: boolean;\r\n  pagServer?: boolean;\r\n  onSelectedRowsChange?: (selectedRows: any) => void;\r\n  clearSelectedRows?: boolean;\r\n  sortServer?: boolean;\r\n  onSort?: (column: any, sortDirection: string) => void;\r\n  persistTableHead?: boolean;\r\n  sortFunction?: (rows: any[], field: any, direction: 'asc' | 'desc') => any[];\r\n  noHeader?: boolean;\r\n  dense?: boolean;\r\n  pagination?: boolean;\r\n  paginationServer?: boolean;\r\n  paginationTotalRows?: number;\r\n  subHeaderAlign?: string;\r\n  [key: string]: any;\r\n}\r\n\r\nfunction RKITable(props: RKITableProps) {\r\n  const { t } = useTranslation('common');\r\n  const paginationComponentOptions = {\r\n    rowsPerPageText: t('Rowsperpage'),\r\n    };\r\n  const {\r\n    columns,\r\n    data,\r\n    totalRows,\r\n    resetPaginationToggle,\r\n    subheader,\r\n    subHeaderComponent,\r\n    handlePerRowsChange,\r\n    handlePageChange,\r\n    rowsPerPage,\r\n    defaultRowsPerPage,\r\n    selectableRows,\r\n    loading,\r\n    pagServer,\r\n    onSelectedRowsChange,\r\n    clearSelectedRows,\r\n    sortServer,\r\n    onSort,\r\n    persistTableHead,\r\n    sortFunction,\r\n    // Filter out any unknown props that might cause DOM warnings\r\n    ...otherProps\r\n  } = props;\r\n\r\n  // Only pass known DataTable props to avoid DOM warnings\r\n  const dataTableProps = {\r\n    paginationComponentOptions,\r\n    noDataComponent: t(\"NoData\"),\r\n    noHeader: true,\r\n    columns,\r\n    data: data || [],\r\n    dense: true,\r\n    paginationResetDefaultPage: resetPaginationToggle,\r\n    subHeader: subheader,\r\n    progressPending: loading,\r\n    subHeaderComponent,\r\n    pagination: true,\r\n    paginationServer: pagServer,\r\n    paginationPerPage: defaultRowsPerPage ? defaultRowsPerPage : 10,\r\n    paginationRowsPerPageOptions: rowsPerPage ? rowsPerPage : [10, 15, 20, 25, 30],\r\n    paginationTotalRows: totalRows,\r\n    onChangeRowsPerPage: handlePerRowsChange,\r\n    onChangePage: handlePageChange,\r\n    selectableRows,\r\n    onSelectedRowsChange,\r\n    clearSelectedRows,\r\n    progressComponent: <CustomLoader/>,\r\n    sortIcon: <i className=\"sort-icon fas fa-exchange-alt\" />,\r\n    sortServer,\r\n    onSort,\r\n    sortFunction,\r\n    persistTableHead,\r\n    className: \"rki-table\"\r\n  };\r\n  return (\r\n    <DataTable {...dataTableProps} />\r\n  )\r\n};\r\n\r\nRKITable.defaultProps = {\r\n  subHeader: false,\r\n  pagination: true,\r\n  totalRows: null,\r\n  pagServer: true,\r\n  onSelectedRowsChange: null,\r\n  clearSelectedRows: false,\r\n  sortServer: false,\r\n  persistTableHead: false\r\n}\r\n\r\nexport default RKITable;\r\n\r\n", "//Import Library\r\nimport Link from \"next/link\";\r\nimport _ from \"lodash\";\r\nimport { Popover, OverlayTrigger } from \"react-bootstrap\";\r\n\r\n//Import services/components\r\nimport RKITable from \"../../components/common/RKITable\";\r\nimport { useTranslation } from 'next-i18next';\r\n\r\nconst Networks = ({ networks }: { networks: any[]}) => {\r\n  if (networks && networks.length > 0) {\r\n    return (\r\n      <ul>\r\n        {networks.map((item, index) => {\r\n          return <li key={index}>{item.title}</li>;\r\n        })}\r\n      </ul>\r\n    );\r\n  }\r\n  return null;\r\n};\r\n\r\n// For network popover\r\nconst Networkpopover = (\r\n  <Popover id=\"popover-basic\">\r\n    <Popover.Header as=\"h3\" className=\"text-center\">\r\n      NETWORKS\r\n    </Popover.Header>\r\n    <Popover.Body>\r\n      <div className=\"m-2\">\r\n        <p>\r\n          <b>EMLab</b> - European Mobile Lab\r\n        </p>\r\n        <p>\r\n          <b>EMT</b> - Emergency Medical Teams\r\n        </p>\r\n        <p>\r\n          <b>GHPP</b> - Global Health Protection Program\r\n        </p>\r\n        <p>\r\n          <b>GOARN</b> - Global Outbreak Alert & Response Network\r\n        </p>\r\n        <p>\r\n          <b>IANPHI</b> - International Association of National Public Health\r\n          Institutes\r\n        </p>\r\n        <p>\r\n          <b>STAKOB</b> - Ständiger Arbeitskreis der Kompetenz-und\r\n          Behandlungszentren\r\n        </p>\r\n        <p>\r\n          <b>WHOCC</b>- World Health Organization Collaborating Centres\r\n        </p>\r\n      </div>\r\n    </Popover.Body>\r\n  </Popover>\r\n);\r\n\r\nfunction OperationPartners(props: any) {\r\n  const { t } = useTranslation('common');\r\n  const { partners } = props;\r\n  const columns = [\r\n    {\r\n      name: t(\"Organisation\"),\r\n      selector: \"title\",\r\n      cell: (d: any) =>\r\n        d && d.institution ? (\r\n          <Link\r\n            href=\"/institution/[...routes]\"\r\n            as={`/institution/show/${d.institution._id}`}\r\n          >\r\n            {d.institution.title}\r\n          </Link>\r\n        ) : (\r\n          \"\"\r\n        ),\r\n      sortable: true,\r\n    },\r\n    {\r\n      name: t(\"Country\"),\r\n      selector: \"country\",\r\n      cell: (d: any) =>\r\n        d &&\r\n        d.institution &&\r\n        d.institution.address &&\r\n        d.institution.address.country ? (\r\n          <Link\r\n            href=\"/country/[...routes]\"\r\n            as={`/country/show/${d.institution.address.country._id}`}\r\n          >\r\n            {d.institution.address.country.title}\r\n          </Link>\r\n        ) : (\r\n          \"\"\r\n        ),\r\n      sortable: true,\r\n    },\r\n    {\r\n      name: t(\"Type\"),\r\n      selector: \"type.title\",\r\n      cell: (d: any) =>\r\n        d.institution && d.institution.type && d.institution.type.title\r\n          ? d.institution.type.title\r\n          : \"\",\r\n      sortable: true,\r\n    },\r\n    {\r\n      name: (\r\n        <OverlayTrigger\r\n          trigger=\"click\"\r\n          placement=\"right\"\r\n          overlay={Networkpopover}\r\n        >\r\n          <span>\r\n            {t(\"Network\")}&nbsp;&nbsp;&nbsp;\r\n            <i\r\n              className=\"fa fa-info-circle\"\r\n              style={{ cursor: \"pointer\" }}\r\n              aria-hidden=\"true\"\r\n            ></i>\r\n          </span>\r\n        </OverlayTrigger>\r\n      ),\r\n      selector: t(\"Networks\"),\r\n      cell: (d: any) =>\r\n        d.institution &&\r\n        d.institution.networks &&\r\n        d.institution.networks.length > 0 ? (\r\n          <Networks networks={d.institution.networks} />\r\n        ) : (\r\n          \"\"\r\n        ),\r\n    },\r\n  ];\r\n\r\n  const get_field = (row: any) => {\r\n    if (row.institution.address && row.institution.address.country) {\r\n      if (\r\n        row.institution.address.country &&\r\n        row.institution.address.country.title\r\n      ) {\r\n        return row.institution.address.country.title.toLowerCase();\r\n      }\r\n      return row.institution.address.country.title;\r\n    }\r\n  };\r\n\r\n  const get_fieldtitle = (row: any) => {\r\n    if (row.institution.type) {\r\n      if (row.institution.type && row.institution.type.title) {\r\n        return row.institution.type.title.toLowerCase();\r\n      }\r\n    }\r\n  };\r\n\r\n  const customSort = (rows: any, field: string, direction: any) => {\r\n    const handleField = (row: any) => {\r\n      if (field === \"country\") {\r\n        get_field(row);\r\n      } else if (field === \"type.title\") {\r\n        get_fieldtitle(row);\r\n      } else {\r\n        if (row.institution && row.institution[field]) {\r\n          return row.institution[field].toLowerCase();\r\n        }\r\n      }\r\n    };\r\n    return _.orderBy(rows, handleField, direction);\r\n  };\r\n\r\n  return (\r\n    <RKITable\r\n      columns={columns}\r\n      data={partners}\r\n      pagServer={true}\r\n      persistTableHead\r\n      sortFunction={customSort}\r\n    />\r\n  );\r\n}\r\n\r\nexport default OperationPartners;\r\n", "\n    (window.__NEXT_P = window.__NEXT_P || []).push([\n      \"/operation/OperationPartners\",\n      function () {\n        return require(\"private-next-pages/operation/OperationPartners.tsx\");\n      }\n    ]);\n    if(module.hot) {\n      module.hot.dispose(function () {\n        window.__NEXT_P.push([\"/operation/OperationPartners\"])\n      });\n    }\n  "], "names": ["RKITable", "props", "t", "useTranslation", "paginationComponentOptions", "rowsPerPageText", "columns", "data", "totalRows", "resetPaginationToggle", "subheader", "subHeaderComponent", "handlePerRowsChange", "handlePageChange", "rowsPerPage", "defaultRowsPerPage", "selectableRows", "loading", "pagServer", "onSelectedRowsChange", "clearSelectedRows", "sortServer", "onSort", "persistTableHead", "sortFunction", "otherProps", "dataTableProps", "noDataComponent", "<PERSON><PERSON><PERSON><PERSON>", "dense", "paginationResetDefaultPage", "subHeader", "progressPending", "pagination", "paginationServer", "paginationPerPage", "paginationRowsPerPageOptions", "paginationTotalRows", "onChangeRowsPerPage", "onChangePage", "progressComponent", "CustomLoader", "sortIcon", "i", "className", "DataTable", "defaultProps", "Networks", "networks", "length", "ul", "map", "item", "index", "li", "title", "Networkpopover", "Popover", "id", "Header", "as", "Body", "div", "p", "b", "OperationPartners", "partners", "name", "selector", "cell", "d", "institution", "Link", "href", "_id", "sortable", "address", "country", "type", "OverlayTrigger", "trigger", "placement", "overlay", "span", "style", "cursor", "aria-hidden", "get_field", "row", "toLowerCase", "get_fieldtitle", "rows", "field", "direction", "_", "handleField"], "sourceRoot": "", "ignoreList": []}