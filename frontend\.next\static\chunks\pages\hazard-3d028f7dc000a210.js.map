{"version": 3, "file": "static/chunks/pages/hazard-3d028f7dc000a210.js", "mappings": "qHAAO,IAAMA,EAAwB,CACnC,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,OACD,CAAC,EAEmC,CACnC,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,MACD,CAAC,wJCnBF,MAxBqB,OAAC,CAAEC,YAAU,QAwBnBC,EAxBqBC,CAAQ,CAAqB,GACzD,GAAEC,CAAC,CAAE,CAAGC,CAAAA,EAAAA,EAAAA,EAAAA,CAAcA,CAAC,UAE7B,MACE,UAACC,EAAAA,CAASA,CAAAA,CAACC,KAAK,IAACC,UAAU,eACzB,UAACC,EAAAA,CAAGA,CAAAA,UACF,WAACC,EAAAA,CAAUA,CAAAA,WACT,UAACC,EAAAA,CAAIA,CAACC,OAAO,EACXJ,UAAU,UACVK,KAAK,OACLC,YAAcV,EAAE,iBAChBW,MAAOd,EACPe,SAAUb,IAEZ,UAACc,MAAAA,CAAIT,UAAU,uBACb,UAACU,EAAAA,CAAeA,CAAAA,CAACC,KAAMC,EAAAA,GAAQA,WAO3C,gIC/BA,IAAMC,EAA8BC,EAAAA,UAAgB,CAAC,EAA9B,CAKpBC,QALmD,EAApB,SAChCf,CAAS,UACTgB,CAAQ,CACRC,GAAIC,EAAY,MAAM,CACtB,GAAGC,EACJ,GAEC,OADAH,EAAWI,CAAAA,EAAAA,EAAAA,EAAAA,CAAkBA,CAACJ,EAAU,oBACpBK,CAAAA,EAAAA,EAAAA,GAAAA,CAAIA,CAACH,EAAW,CAClCH,IAAKA,EACLf,UAAWsB,IAAWtB,EAAWgB,GACjC,GAAGG,CAAK,EAEZ,GACAN,EAJyBS,WAIC,CAAG,iBCG7B,IAAMpB,EAA0BY,EAAAA,SAAb,CAA6B,CAAC,GAQ9CC,MAR2B,EAAoB,UAChDC,CAAQ,MACRO,CAAI,eACJC,CAAa,WACbxB,CAAS,CAETiB,CADA,EACIC,EAAY,KAAK,CACrB,GAAGC,EACJ,GACCH,EAAWI,CAAAA,EAAAA,EAAAA,EAAAA,CAAkBA,CAACJ,EAAU,eAIxC,IAAMS,EAAeC,CAAAA,EAAAA,EAAAA,OAAAA,CAAOA,CAAC,IAAO,GAAC,EAAI,EAAE,EAC3C,MAAoBL,CAAb,EAAaA,EAAAA,GAAAA,CAAIA,CAACM,EAAP,CAAwBA,CAACC,QAAQ,CAAE,CACnDrB,MAAOkB,EACPI,SAAuBR,CAAb,EAAaA,EAAAA,GAAAA,CAAIA,CAACH,EAAP,CACnBH,IAAKA,EACL,GAAGI,CAAK,CACRnB,KAduJ,KAc5IsB,IAAWtB,EAAWgB,EAAUO,GAAQ,GAAeA,MAA7CD,CAAiCN,EAAS,KAAQ,OAALO,GAAQC,GAAiB,iBAC7F,EACF,EACF,GACAtB,EAAW4B,WAAW,CAAG,aACzB,MAAeC,OAAOC,MAAM,CAAC9B,EAAY,CACvC+B,MAAMpB,CACNqB,MAhCsBf,CAgCfgB,EAhCqCd,CAAAA,EAAAA,EAAb,GAAaA,CAAIA,CAACR,EAAgB,CACjEgB,GAD0C,MACnBR,CAAb,EAAaA,EAAAA,CADwCR,EACxCQ,CAAIA,CAACe,EAAAA,CAAcA,CAAE,CAC1C/B,KAAM,QACN,GAAGc,CAAK,EAEZ,GA4BEkB,SAvCyBlB,CAuCfmB,EAvCqCjB,CAAAA,EAAAA,EAAb,GAAaA,CAAIA,CDUtCR,ECVuD,CACpEgB,GAD6C,MACtBR,CAAAA,CDSIR,CCTJQ,CDSK,CCTLA,CAD2CR,EAC3CQ,CAAIA,CAACe,EAAAA,CAAcA,CAAE,CAC1C/B,KAAM,WACN,GAAGc,CAAK,EAEZ,EAmCA,EAAE,EAAC,kQCjCH,SAASoB,EAAsBpB,CAAU,EACrC,GAAM,GAAEvB,CAAC,CAAE,CAAGC,CAAAA,EAAAA,EAAAA,EAAAA,CAAcA,CAAC,UACvB,SAAE2C,CAAO,CAAE,CAAGrB,EACd,CAACsB,EAAWC,EAAa,CAAGC,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,EAAC,GACrC,CAACC,EAAQC,EAAU,CAAGF,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAe,EAAE,EAC/C,CAACG,EAAgBC,EAAkB,CAAGJ,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAW,EAAE,EAC3DK,EAAe,CACjBC,MAAO,CAAC,EACRC,MAAO,IACPC,KAAM,CAAEC,MAAO,KAAM,CACzB,EAEMC,EAAY,MAAOC,IACrB,IAAMC,EAAW,MAAMC,EAAAA,CAAUA,CAACC,GAAG,CAAC,cAAeH,GACrD,GAAIC,GAAYG,MAAMC,OAAO,CAACJ,EAASK,IAAI,EAAG,CAC1C,IAAMC,EAA4B,EAAE,CAC9BC,EAAwB,EAAE,CAEhCC,IAAAA,IAAM,CAACR,EAASK,IAAI,CAAE,CAACI,EAAWC,KAC9B,IAAMC,EAAyB,CAC3B,GAAGF,CAAI,CACPG,WAAW,CACf,EACAN,EAAYO,IAAI,CAACF,GACjBJ,EAAYM,IAAI,CAACJ,EAAKK,GAAG,CAC7B,GAEA7B,EAAQsB,GACRf,EAAkBe,GAClBjB,EAAUgB,EACd,CACJ,EAEAS,CAAAA,EAAAA,EAAAA,SAAAA,CAASA,CAAC,KACNjB,EAAUL,EACd,EAAG,EAAE,EAmBL,IAAMuB,EAA+B,IACjC,IAAMC,EAAgB,IAAI5B,EAAO,CAC7B6B,EAAwB,IAAI3B,EAAe,CAE/C0B,EAAcE,OAAO,CAAC,CAACV,EAAMW,KACrBX,EAAKY,IAAI,GAAKC,EAAEC,MAAM,CAACC,EAAE,EAAE,CAC3BP,CAAa,CAACG,EAAM,CAACR,SAAS,CAAGU,EAAEC,MAAM,CAACE,OAAO,CAC5CH,EAAEC,MAAM,CAACE,OAAO,CAGjBP,CAHmB,CAGGL,IAAI,CAACJ,EAAKK,GAAG,EAFnCI,EAAwBA,EAAsBQ,MAAM,CAACC,GAAKA,IAAMlB,EAAKK,GAAG,EAKpF,GAEAtB,EAAkB0B,GAClBjC,EAAQiC,GACR/B,GAAa,GACbG,EAAU2B,EACd,EAcA,MACI,WAAC/D,MAAAA,CAAIT,UAAU,qCACX,UAACG,EAAAA,CAAIA,CAACgF,KAAK,EACP9E,KAAK,WACL0E,GAAK,MACLC,QAASvC,EACT2C,MAAOxF,EAAE,2BACTY,SAzDa,CAyDH6E,GAxDlB,IAAMb,EAAgB5B,EAAO0C,GAAG,CAAEtB,GAAsB,EACpD,EADoD,CACjDA,CAAI,CACPG,UAAWoB,EAAMT,MAAM,CAACE,OAAO,CACnC,GAEIQ,EAA4B,EAAE,CAC9BD,EAAMT,MAAM,CAACE,OAAO,EAAE,CACtBQ,EAAkBhB,EAAcc,GAAG,CAACtB,GAAQA,EAAKK,IAAG,EAGxD7B,EAAQgD,GACRzC,EAAkByC,GAClB9C,EAAa6C,EAAMT,MAAM,CAACE,OAAO,EACjCnC,EAAU2B,EACd,IA4CS5B,EAAO0C,GAAG,CAAC,CAACtB,EAAMW,IAEX,UAACxE,EAAAA,CAAIA,CAACgF,KAAK,EAEP9E,KAAK,WACL0E,GAAIf,EAAKY,IAAI,CACbQ,MAAOpB,EAAKZ,KAAK,CACjB7C,MAAOyD,EAAKY,IAAI,CAChBpE,SAAU+D,EACVS,QAASpC,CAAM,CAAC+B,EAAM,CAACR,SAAS,EAN3BQ,IAWjB,UAACc,EAAAA,CAAMA,CAAAA,CAACC,QAnCO,CAmCEC,IAlCrB,IAAMnB,EAAgB5B,EAAO0C,GAAG,CAAC,GAAuB,EACpD,EADoD,CACjDtB,CAAI,CACPG,WAAW,EACf,GAEApB,EAAkB,EAAE,EACpBL,GAAa,GACbG,EAAU2B,GACVhC,EAAQ,EAAE,CACd,EAyByCxC,UAAU,0BACtCJ,EAAE,gBAInB,CAEA2C,EAAsBqD,YAAY,CAAG,CACjCpD,QAAS,KAET,CACJ,gCC6JA,MAtRe,KACb,GAAM,CAAE5C,GAAC,EAqRUiG,EAAC,EArRTC,CAAI,CAAE,CAAGjG,CAAAA,EAAAA,EAAAA,EAAAA,CAAcA,CAAC,UAC7BkG,EAAgC,OAAlBD,EAAKE,QAAQ,CAAY,KAAOF,EAAKE,QAAQ,CAE3DC,EAA4B,OAAhBF,EAAuBG,EAAAA,CAAqBA,CAAG1G,EAAAA,CAAqBA,CAChF,CAAC2G,EAAW,CAAGxD,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAACsD,GACxB,CAACG,EAAYC,EAAc,CAAQ1D,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAC,EAAE,EAC9C,CAAC2D,EAAaC,EAAe,CAAQ5D,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAC,EAAE,EAChD,CAAC6D,EAAcC,EAAgB,CAAQ9D,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAC,EAAE,EAClD,CAAC+D,EAAYC,EAAc,CAAQhE,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAC,GAC5C,CAACiE,EAAaC,EAAe,CAAQlE,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAC,GAC9C,CAACmE,EAAc,CAAQnE,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAC,IAChC,CAACoE,EAAaC,EAAe,CAAQrE,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAC,OAC9C,CAACsE,EAAQC,EAAU,CAAQvE,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,EAAC,GACpC,CAACwE,EAASC,EAAW,CAAQzE,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAC,EAAE,EACxC,CAAClD,EAAY4H,EAAc,CAAG1E,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAC,IACvC,CAAC2E,EAAU,CAAG3E,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAC4E,EAbK,SAAqB,OAAZxB,GAAgB,YAcrD,CAACyB,EAAQC,EAAW,CAAG9E,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,EAAC,GAChC,CAAC+E,EAAgBC,EAAkB,CAAGhF,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAM,CAAC,GAIrDiF,EAAiB,MAAOC,IAC5B,MAAMX,GAAU,GAChB,IAAM3D,EAAW,MAAMC,EAAAA,CAAUA,CAACC,GAAG,CAAC,UAAWoE,GAC7CtE,GAAYA,EAASK,IAAI,EAAE,CAC7B,MAAMsD,GAAU,GAChB,MAAMX,EAAehD,EAASK,IAAI,EAClC,MAAM+C,EAAcpD,EAASuE,UAAU,EAE3C,EAEMC,EAAmB,MAAOC,EAASrD,KAEvC,GADA,MAAMqC,EAAeb,CAAS,CAACxB,EAAM,EACjCwB,CAAS,CAACxB,EAAM,GAAK/E,EAAE,QAAUuG,CAAS,CAACxB,EAAM,GAAK/E,EAAE,QAI1D,CAJmE,GAEnE6H,GAAW,GACX,MAAMhB,EAAgB,EAAE,EACpB/C,MAAMC,OAAO,CAACwD,IAA+B,IAAnBA,EAAQc,MAAM,CAAQ,CAQlD,IAAMC,EAAe,CACnB,MAAS,CAAC,EACV,MAASpB,EACT,KAAQF,EACR,KAAQ,CAAC,CAACU,EAAS,CAAG,KAAM,CAC9B,EAEAa,CAdmB,UACjBD,EAAajF,KAAK,CAAG,CAAE,YAAekE,CAAQ,EAC9Ce,EAAaE,IAAI,CAAC,EAClB,IAAM7E,EAAW,MAAMC,EAAAA,CAAUA,CAACC,GAAG,CAAC,UAAWyE,EACjD,OAAM7B,EAAc9C,EAASK,IAAI,EACjC,MAAM+C,EAAcpD,EAASuE,UAAU,EACzC,IASF,KAEG,CACH,IAAMO,EAA2B,EAAE,CACnChB,EAAc,IACd,IAAMiB,EAASnC,CAAS,CAACxB,EAAM,CAAC4D,WAAW,GAC3Cd,EAAW,IACXnB,EAAYhB,GAAG,CAAC,MAAOtB,EAAWC,KAChC,GAAID,EAAKZ,KAAK,CAAC2C,EAAY,CAACwC,WAAW,GAAGC,KAAK,CAAC,GAAG,CAAC,EAAE,GAAKF,GAAUtE,EAAKyE,OAAO,CAAE,CACjF,IAAMC,EAAI,CAAEtF,MAAOY,EAAKZ,KAAK,CAAEiB,IAAKL,EAAKK,GAAG,CAAEsE,YAAa3E,EAAK2E,WAAW,EAC3EN,EAAkBjE,IAAI,CAACsE,EACzB,CACF,GAEIL,EAAkBJ,MAAM,EAAI,GAAG,MAC3BxB,EAAgB4B,GACtB1B,EAAc0B,EAAkBJ,MAAM,IAEtC,MAAMxB,EAAgB,EAAE,EACxB,MAAMJ,EAAc,EAAE,EACtB,MAAMM,EAAc,EAAE,EACtB,MAAMgB,EAAkB,CAAC,GAE7B,CACF,EAEMiB,EAAW,MAAOC,IACtB,IAAMC,EAAaD,EAAaE,QAAQ,CAAG,EAI3C,CAJ8C,EAE9C,MAAMlC,EAAeiC,GAEjBpF,MAAMC,OAAO,CAACwD,IAA+B,IAAnBA,EAAQc,EAJ+C,IAIzC,CAAQ,CASlD,IAAMC,EAAe,CACnB,MAAS,CAAC,EACV,MAASpB,EACT,KAAQgC,EACR,KAAQ,CAAC,CAACxB,EAAS,CAAG,KAAO,CAC/B,EAEAa,CAfmB,UACjBD,EAAajF,KAAK,CAAG,CAAE,YAAekE,CAAQ,EAC9C,IAAM5D,EAAW,MAAMC,EAAAA,CAAUA,CAACC,GAAG,CAAC,UAAWyE,EACjD,OAAM7B,EAAc9C,EAASK,IAAI,EACjC,MAAM+C,EAAcpD,EAASuE,UAAU,EACvC,MAAMH,EAAkBpE,GAC1B,GAUF,KACK,CACH,IAAMyF,EAAc,UAElB3C,EAAc9C,CADG,MAAMC,EAAAA,CAAUA,CAACC,GAAG,CAAC,UAAWwF,EAAAA,EAC1BrF,IAAI,CAC7B,EAEMqF,EAAmB,CACvB,MAAS,CAAC,EACV,MAASnC,EACT,KAAQgC,EACR,KAAQ,CAAE,CAACxB,EAAS,CAAG,KAAM,CAC/B,CACA,OAAM0B,GACR,CACF,EAEME,EAAe,MAAOC,IAI1B,GAHA1B,EAAW,IAGP/D,MAAMC,OAAO,CAACwF,IAAmC,IAArBA,EAAUlB,MAAM,CAAQ,CACtD,MAAM5B,EAAc,EAAE,EACtB,MAAMM,EAAc,GACpB,MAAMK,EAAe,OACrB,MAAMW,EAAkB,CAAC,GACzB,MAAMpB,EAAe,EAAE,EACvBE,EAAgB,EAAE,EAClBW,EAAW,EAAE,EACb,MACF,CAOAQ,EALyB,CACvB,MAAS,CAAE,KAIEwB,OAJaD,CAAU,EACpC,MAAS,IACT,KAAQ,CAAE,CAAC7B,EAAS,CAAG,KAAM,CAC/B,GAGAF,EAAW+B,GACX,IAAIE,EAAkB,EAAE,CAClBnB,EAAe,CACnB,MAAS,CAAC,EACV,MAASpB,EACT,KAAQF,EACR,KAAQ,CAAE,CAACU,EAAS,CAAG,KAAM,CAC/B,EAcAgC,CAZmB,UACjBpB,EAAa/E,IAAI,CAAG,CAAE,CAACmE,EAAS,CAAG,KAAM,EACzCY,EAAajF,KAAK,CAAG,CAAE,YAAekG,CAAU,EAChDjB,EAAaE,IAAI,CAAG,EACpB,IAAM7E,EAAW,MAAMC,EAAAA,CAAUA,CAACC,GAAG,CAAC,UAAWyE,GACjDmB,EAAkB9F,EAASK,IAAI,CAC/B,MAAMyC,EAAcgD,GACpB,MAAM1C,EAAcpD,EAASuE,UAAU,EACvC,MAAMd,EAAe,OACrB,MAAMW,EAAkBpE,GACxBkD,EAAgB,EAAE,EACpB,GAGF,EAGM8C,EAAe,CACnB,MAAS,CAAC,EACV,KAAQ,CAAC,CAACjC,EAAS,CAAG,KAAM,CAC9B,EACMkC,EAAe,UAEnBnD,EAAc9C,CADG,MAAMC,EAAAA,CAAUA,CAACC,GAAG,CAAC,UAAW8F,EAAAA,EAC1B3F,IAAI,CAC7B,EAGM6F,EAAY,CAACC,EAAOC,KACxBD,EAAIH,EAAatG,KAAK,CAAG,CAAE,CAACqE,EAAS,CAAGoC,GAAKA,CAAC,CAAC,EAAE,CAACE,WAAW,GAAKF,EAAEG,KAAK,CAAC,GAAGtB,WAAW,EAAG,EAAIgB,EAAatG,KAAK,CAAG,CAAC,EACrG,WAAhB8D,CAA6BwC,GAAAA,EAAatG,KAAK,CAAG,CAAE,GAAGsG,EAAatG,KAAK,CAAE6G,YAAaH,EAAa,EACrGH,GACF,EAEMO,EAAoBC,CAAAA,EAAAA,EAAAA,MAAAA,CAAMA,CAC9BjG,IAAAA,QAAU,CAAC,CAAC2F,EAAEO,IAAgBR,EAAUC,EAAEO,GAAcC,OAAOC,KAAgC,GAAK,MACpGC,OAAO,CAOT,MACE,WAACtK,EAAAA,CAASA,CAAAA,CAACC,KAAK,IAACC,UAAU,gBACzB,UAACC,EAAAA,CAAGA,CAAAA,UACF,UAACoK,EAAAA,CAAGA,CAAAA,CAACC,GAAI,YACP,UAACC,EAAAA,CAAWA,CAAAA,CAACnH,MAAOxD,EAAE,sBAG1B,WAACa,MAAAA,CAAIT,UAAU,+BACb,UAACwK,MAAAA,CAAIxK,UAAU,qBAAqByK,IAAI,8BAA8BC,IAAI,SAC1E,UAACnI,EAAqBA,CAAEwD,YAAcA,EAAY4E,IAA5BpI,ED9EO,SC8EqCwE,EAAavE,QAAS0G,OAE1F,WAACzI,MAAAA,CAAIT,UAAU,0BACb,UAACS,MAAAA,CAAIT,UAAU,6BACZmG,EAAUb,GAAG,CAAC,CAACtB,EAAMW,IACpB,UAACiG,OAAAA,CAAiB5K,UAAW,iBAAsD,OAArC+G,IAAgB/C,EAAO,SAAW,IAAM0B,QAAS,GAAOqC,EAAiBlD,EAAGF,YAASX,GAAxHW,MAGd6C,GAAW,UAAC/G,MAAAA,UACX,UAACR,EAAAA,CAAGA,CAAAA,UACF,UAACoK,EAAAA,CAAGA,CAAAA,CAACrK,UAAU,qBACb,UAACN,EAAAA,OAAYA,CAAAA,CAACC,SAzBJkF,CAyBcgG,GAxBlCxD,EAAcxC,EAAEC,MAAM,CAACvE,KAAK,EAC5BwJ,EAAkBlF,EAAEC,MAAM,CAACvE,KAAK,CAAE4G,EACpC,EAsBkD1H,WAAYA,UAKxD,UAACgB,MAAAA,CAAIT,UAAU,yBAEViH,EAAS,UAAC6D,EAAAA,CAAOA,CAAAA,CAACC,UAAU,SAASC,QAAQ,YACrB,IAAxBxE,EAAayB,MAAM,CAASzB,EAAalB,GAAG,CAAC,CAACtB,EAAWW,IACvD,UAACsG,KAAAA,CAAejL,UAAU,mCACxB,UAACkL,IAAIA,CAACC,KAAK,KAAND,iBAA4BjK,GAAI,gBAAyB,OAAT+C,EAAKK,GAAG,WAC1DL,EAAKZ,KAAK,EAAIY,EAAKZ,KAAK,CAAC2C,EAAY,CAAG/B,EAAKZ,KAAK,CAAC2C,EAAY,CAAG,MAF9DpB,IAMT,EAAYsD,MAAM,EAAI,EAAK,UAACmD,KAAAA,UACzBhF,EAAWd,GAAG,CAAC,CAACtB,EAAWW,IAC1BX,EAAKyE,OAAO,CACV,UAACwC,KAAAA,CAAejL,UAAU,mCACxB,UAACkL,IAAIA,CAACC,KAAK,KAAND,iBAA4BjK,GAAI,gBAAyB,OAAT+C,EAAKK,GAAG,WAC1DL,EAAKZ,KAAK,EAAIY,EAAKZ,KAAK,CAAC2C,EAAY,CAAG/B,EAAKZ,KAAK,CAAC2C,EAAY,CAAG,MAF9DpB,GAID,QAEN,WAAClE,MAAAA,CAAIT,UAAU,0BAAgB,IAAEJ,EAAE,kBAAkB,SAIlE8G,EAAaI,GAAgB,CAACrH,EAC5B,UAACgB,MAAAA,CAAIT,UAAU,8BACb,UAACqL,IAAaA,CACZC,UAAWC,KAAKC,IAAI,CAAC9D,EAAeI,UAAU,CAAGJ,EAAexE,KAAK,EACrEuI,mBAAoB,EACpBC,qBAAsB,EACtBC,aAAc/C,EACdgD,UAAWlE,EAAeU,IAAI,CAAG,EACjCyD,mBAAmB,aACnBC,cAAc,YACdC,kBAAkB,YAClBC,kBAAkB,YAClBC,sBAAsB,YACtBC,cAAc,YACdC,kBAAkB,YAClBC,gBAAgB,SAChBC,kBAAkB,WAClBC,cAAc,IACdC,UAAU,QAGZ,UAOd,gECzRe,SAAShC,EAAYpJ,CAAuB,EACzD,MACE,UAACqL,KAAAA,CAAGxM,UAAU,wBAAgBmB,EAAMiC,KAAK,EAE7C,mBCPA,4CACA,UACA,WACA,OAAe,EAAQ,KAAqC,CAC5D,EACA,SAFsB,oBCJsO,IAA1K,EAAQ,KAAO,EAAjC,CAAoC,CAApC,CAA4L,MAA5L,CAAyB,CAAmK,KAAgB,OAAO,cAAc,aAAa,aAAa,cAAc,cAAc,2CAA2C,wBAAwB,UAAU,8LAAmM,uCAAsC,aAAa,SAAS,eAAe,OAAO,oNAAoN,wBAAwB,eAAe,mBAAmB,SAAS,aAAa,yDAAyD,QAAQ,aAAa,aAAa,MAAM,cAAc,WAAW,+BAA+B,YAAY,YAAY,qCAAqC,QAAQ,0CAA0C,cAAc,EAAE,IAAI,aAAa,+DAA+D,uBAAuB,EAAE,8DAA8D,4FAA4F,eAAe,wCAAwC,SAAS,GAAG,SAAS,YAAY,YAAa,eAAc,cAAc,EAAE,uCAAuC,aAAa,wDAAwD,YAAY,mBAAmB,KAAK,mBAAmB,sEAAsE,WAAS,sBAAyB,kBAAkB,mQAAmQ,4JAA4J,YAAY,0BAA0B,iHAAiH,eAAiZ,aAAa,wDAAwD,YAAY,mBAAmB,KAAK,mBAAmB,sEAAsE,UAAS,uBAA7kB,aAAa,2WAAylB,kBAAkB,sHAAmI,+BAA+B,UAAlK,UAAkK,CAAY,0BAA0B,mEAAmE,YAA4O,cAAc,gEAAgE,mBAAmB,cAAc,iFAAiF,gBAAgB,aAAa,sGAAoG,GAAM,aAAa,wDAAwD,YAAY,mBAAmB,KAAK,mBAAmB,sEAAsE,UAAS,uBAA+L,gBAAgB,0EAA0E,yBAAuB,KAAQ,SAAiL,KAAc,gGAAoG,SAAS,cAAc,wEAAwE,8CAA6C,IAAM,kBAAkB,yCAAyC,kDAAkD,WAA5kD,aAAa,yMAAyM,IAAi4C,cAAkB,GAAe,mBAAuQ,GAAvQ,OAAuQ,EAAvQ,qEAA4G,CAA2J,EAA3J,wCAA0C,aAAa,MAAoG,EAApG,6BAAqC,wBAA+D,EAA/D,aAAuC,YAAY,OAAY,SAAM,oBAAgC,oDAA4D,uBAA5D,SAA4D,GAAmC,kCAAqC,IAAI,+EAAgF,MAAO,SAAS,UAAU,cAAc,UAAhS,GAA30B,GAAwpC,EAAhC,EAAgC,CAA1B,OAA0B,sBAA1B,GAA6D,GAA7D,eAA6D,wBAA3rC,iBAA0tC,IAA1tC,mBAA0tC,CAA1tC,SAA0tC,EAAnqC,YAAmqC,EAAnqC,4EAA8F,SAAqkC,OAAiB,CAAE,cAAc,MAAQ,qBAAqB,0EAA8E,gEAAiE,uBAAuB,qCAAqC,cAAc,EAAE,sCAAwC,2CAA2C,uCAAuC,UAAU,EAAE,4CAA8C,yFAAyF,YAAY,EAAE,wBAAwB,wCAA0C,mCAAmC,WAAW,qBAAqB,wCAA0C,WAAW,0BAA0B,uCAAyC,+DAA+D,sDAAoH,mDAAqD,6CAA6C,MAAM,SAAS,yDAArO,cAAqO,OAArO,cAAqO,QAArO,cAAqO,SAArO,aAAqO,CAAyF,EAAE,iBAAiB,2BAA2B,kCAAkC,0CAA4C,uBAAuB,8DAA8D,WAAW,EAAE,oCAAsC,8FAA8F,WAAW,EAAE,0CAA4C,8FAA8F,WAAW,EAAE,yCAA2C,qFAAqF,0CAA0C,iCAAmC,iLAAiL,oBAAoB,IAAI,gCAAgC,KAAK,gBAAgB,uCAAuC,sBAAsB,2BAA2B,MAAM,QAAQ,IAAI,KAAK,UAAU,gBAAgB,iCAAiC,EAAE,sBAAsB,iCAAiC,EAAE,gDAAgD,iCAAiC,EAAE,8DAA8D,+BAA+B,oBAAllJ,EAAklJ,CAAuB,2JAA2J,UAAU,+BAA+B,GAAG,wBAAyB,QAAQ,gHAAgH,8CAA6C,mBAAoB,EAAG,SAAS,uVAAgZ,SAAhZ,qDAAgZ,CAAW,GAAG,WAAgB,yCAAyC,kHAA6G,oqBAAyqB,EAAE,2CAA2C,kUAAkU,+BAA8B,mOAAoO,EAAE,sCAAsC,8EAA8E,mBAAmB,EAAE,uCAAuC,wDAAwD,gBAAgB,EAAE,uCAAuC,mEAAmE,8DAA8D,EAAE,yCAAyC,8BAA8B,8DAA8D,yCAAyC,kFAAkF,EAAE,uCAAuC,qKAAqK,yBAA78Q,EAA68Q,CAA4B,6UAA6U,GAAG,EAAE,8BAA8B,uCAAuC,iEAAiE,ohBAA0jB,+BAA+B,2DAA2D,yBAAyB,YAAY,0BAA0B,+HAAntB,iBAAmtB,qBAAsJ,+FAA+F,YAAY,0BAA0B,2HAA9+B,iBAA8+B,qBAAkJ,mDAAmD,WAAl1R,KAAgB,YAAY,WAAW,KAAK,WAAW,gHAAg0J,EAA29H,mCAA39H,EAA29H,aAA0D,YAAY,IAAI,cAAc,iBAAiB,6VAA6V,uCAAuC,yuBAAyuB,sBAAsB,qUAAqU,gDAAgD,qFAAqF,SAAS,qGAAqG,EAAE,SAAU,MAAM", "sources": ["webpack://_N_E/./data/alphabet.tsx", "webpack://_N_E/./pages/hazard/HazardSearch.tsx", "webpack://_N_E/./node_modules/react-bootstrap/esm/InputGroupText.js", "webpack://_N_E/./node_modules/react-bootstrap/esm/InputGroup.js", "webpack://_N_E/./components/common/hazardMultiCheckboxes.tsx", "webpack://_N_E/./pages/hazard/index.tsx", "webpack://_N_E/./components/common/PageHeading.tsx", "webpack://_N_E/?8fc5", "webpack://_N_E/./node_modules/react-paginate/dist/react-paginate.js"], "sourcesContent": ["export const ALPHABETIC_FILTERS_DE = [\r\n  \"A\",\r\n  \"B\",\r\n  \"C\",\r\n  \"D\",\r\n  \"E\",\r\n  \"F\",\r\n  \"G\",\r\n  \"H\",\r\n  \"I\",\r\n  \"J\",\r\n  \"K\",\r\n  \"L\",\r\n  \"M\",\r\n  \"N\",\r\n  \"O\",\r\n  \"P\",\r\n  \"Q\",\r\n  \"R\",\r\n  \"S\",\r\n  \"T\",\r\n  \"U\",\r\n  \"V\",\r\n  \"W\",\r\n  \"X\",\r\n  \"Y\",\r\n  \"Z\",\r\n  \"Alle\"\r\n];\r\n\r\nexport const ALPHABETIC_FILTERS_EN = [\r\n  \"A\",\r\n  \"B\",\r\n  \"C\",\r\n  \"D\",\r\n  \"E\",\r\n  \"F\",\r\n  \"G\",\r\n  \"H\",\r\n  \"I\",\r\n  \"J\",\r\n  \"K\",\r\n  \"L\",\r\n  \"M\",\r\n  \"N\",\r\n  \"O\",\r\n  \"P\",\r\n  \"Q\",\r\n  \"R\",\r\n  \"S\",\r\n  \"T\",\r\n  \"U\",\r\n  \"V\",\r\n  \"W\",\r\n  \"X\",\r\n  \"Y\",\r\n  \"Z\",\r\n  \"All\"\r\n];\r\n\r\n", "//Import Library\r\nimport { Container, Row, InputGroup, Form } from \"react-bootstrap\";\r\nimport { FontAwesomeIcon } from \"@fortawesome/react-fontawesome\";\r\nimport {\r\n  faSearch\r\n} from \"@fortawesome/free-solid-svg-icons\";\r\n\r\n//Import services/components\r\nimport { useTranslation } from 'next-i18next';\r\n\r\ninterface HazardSearchProps {\r\n  filterText: string;\r\n  onFilter: (e: React.ChangeEvent<HTMLInputElement>) => void;\r\n}\r\n\r\nconst HazardSearch = ({ filterText, onFilter }: HazardSearchProps) => {\r\n  const { t } = useTranslation('common');\r\n\r\n  return (\r\n    <Container fluid className=\"p-0\">\r\n      <Row>\r\n        <InputGroup >\r\n          <Form.Control\r\n            className=\"rounded\"\r\n            type=\"text\"\r\n            placeholder= {t(\"SearchHazards\")}\r\n            value={filterText}\r\n            onChange={onFilter}\r\n          />\r\n          <div className=\"search-icon\">\r\n            <FontAwesomeIcon icon={faSearch} />\r\n          </div>\r\n        </InputGroup>\r\n\r\n      </Row>\r\n    </Container>\r\n  );\r\n};\r\n\r\nexport default HazardSearch;\r\n", "\"use client\";\n\nimport * as React from 'react';\nimport classNames from 'classnames';\nimport { useBootstrapPrefix } from './ThemeProvider';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst InputGroupText = /*#__PURE__*/React.forwardRef(({\n  className,\n  bsPrefix,\n  as: Component = 'span',\n  ...props\n}, ref) => {\n  bsPrefix = useBootstrapPrefix(bsPrefix, 'input-group-text');\n  return /*#__PURE__*/_jsx(Component, {\n    ref: ref,\n    className: classNames(className, bsPrefix),\n    ...props\n  });\n});\nInputGroupText.displayName = 'InputGroupText';\nexport default InputGroupText;", "\"use client\";\n\nimport classNames from 'classnames';\nimport * as React from 'react';\nimport { useMemo } from 'react';\nimport { useBootstrapPrefix } from './ThemeProvider';\nimport FormCheckInput from './FormCheckInput';\nimport InputGroupContext from './InputGroupContext';\nimport InputGroupText from './InputGroupText';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst InputGroupCheckbox = props => /*#__PURE__*/_jsx(InputGroupText, {\n  children: /*#__PURE__*/_jsx(FormCheckInput, {\n    type: \"checkbox\",\n    ...props\n  })\n});\nconst InputGroupRadio = props => /*#__PURE__*/_jsx(InputGroupText, {\n  children: /*#__PURE__*/_jsx(FormCheckInput, {\n    type: \"radio\",\n    ...props\n  })\n});\nconst InputGroup = /*#__PURE__*/React.forwardRef(({\n  bsPrefix,\n  size,\n  hasValidation,\n  className,\n  // Need to define the default \"as\" during prop destructuring to be compatible with styled-components github.com/react-bootstrap/react-bootstrap/issues/3595\n  as: Component = 'div',\n  ...props\n}, ref) => {\n  bsPrefix = useBootstrapPrefix(bsPrefix, 'input-group');\n\n  // Intentionally an empty object. Used in detecting if a dropdown\n  // exists under an input group.\n  const contextValue = useMemo(() => ({}), []);\n  return /*#__PURE__*/_jsx(InputGroupContext.Provider, {\n    value: contextValue,\n    children: /*#__PURE__*/_jsx(Component, {\n      ref: ref,\n      ...props,\n      className: classNames(className, bsPrefix, size && `${bsPrefix}-${size}`, hasValidation && 'has-validation')\n    })\n  });\n});\nInputGroup.displayName = 'InputGroup';\nexport default Object.assign(InputGroup, {\n  Text: InputGroupText,\n  Radio: InputGroupRadio,\n  Checkbox: InputGroupCheckbox\n});", "//Import Library\r\nimport React, { useState, useEffect } from \"react\";\r\nimport _ from \"lodash\";\r\nimport { Form, Button } from \"react-bootstrap\";\r\n\r\n//Import services/components\r\nimport apiService from \"../../services/apiService\";\r\nimport { useTranslation } from 'next-i18next';\r\n\r\n// Define types for hazard items\r\ninterface HazardItem {\r\n    _id: string;\r\n    code: string;\r\n    title: string;\r\n    isChecked: boolean;\r\n}\r\n\r\nfunction HazardMultiCheckboxes(props: any) {\r\n    const { t } = useTranslation('common');\r\n    const { filthaz } = props;\r\n    const [allhazard, setAllhazard] = useState(true);\r\n    const [hazard, setHazard] = useState<HazardItem[]>([]);\r\n    const [selectedHazard, setSelectedHazard] = useState<string[]>([]);\r\n    const RegionParams = {\r\n        query: {},\r\n        limit: \"~\",\r\n        sort: { title: \"asc\" },\r\n    };\r\n\r\n    const gethazard = async (RegionParams_initial: any) => {\r\n        const response = await apiService.get(\"/hazardtype\", RegionParams_initial);\r\n        if (response && Array.isArray(response.data)) {\r\n            const finalHazard: HazardItem[] = [];\r\n            const selectedIds: string[] = [];\r\n\r\n            _.each(response.data, (item: any, _i: number) => {\r\n                const hazardItem: HazardItem = {\r\n                    ...item,\r\n                    isChecked: true\r\n                };\r\n                finalHazard.push(hazardItem);\r\n                selectedIds.push(item._id);\r\n            });\r\n\r\n            filthaz(selectedIds);\r\n            setSelectedHazard(selectedIds);\r\n            setHazard(finalHazard);\r\n        }\r\n    };\r\n\r\n    useEffect(() => {\r\n        gethazard(RegionParams);\r\n    }, []);\r\n\r\n    const handleAllChecked = (event: React.ChangeEvent<HTMLInputElement>) => {\r\n        const updatedHazard = hazard.map((item: HazardItem) => ({\r\n            ...item,\r\n            isChecked: event.target.checked\r\n        }));\r\n\r\n        let selected_Hazard: string[] = [];\r\n        if (event.target.checked) {\r\n            selected_Hazard = updatedHazard.map(item => item._id);\r\n        }\r\n\r\n        filthaz(selected_Hazard);\r\n        setSelectedHazard(selected_Hazard);\r\n        setAllhazard(event.target.checked);\r\n        setHazard(updatedHazard);\r\n    };\r\n\r\n    const handleIndividualHazardChange = (e: React.ChangeEvent<HTMLInputElement>) => {\r\n        const updatedHazard = [...hazard];\r\n        let updatedSelectedHazard = [...selectedHazard];\r\n\r\n        updatedHazard.forEach((item, index) => {\r\n            if (item.code === e.target.id) {\r\n                updatedHazard[index].isChecked = e.target.checked;\r\n                if (!e.target.checked) {\r\n                    updatedSelectedHazard = updatedSelectedHazard.filter(n => n !== item._id);\r\n                } else {\r\n                    updatedSelectedHazard.push(item._id);\r\n                }\r\n            }\r\n        });\r\n\r\n        setSelectedHazard(updatedSelectedHazard);\r\n        filthaz(updatedSelectedHazard);\r\n        setAllhazard(false);\r\n        setHazard(updatedHazard);\r\n    };\r\n\r\n    const resetAllHazard = () => {\r\n        const updatedHazard = hazard.map((item: HazardItem) => ({\r\n            ...item,\r\n            isChecked: false\r\n        }));\r\n\r\n        setSelectedHazard([]);\r\n        setAllhazard(false);\r\n        setHazard(updatedHazard);\r\n        filthaz([]);\r\n    };\r\n\r\n    return (\r\n        <div className=\"hazards-multi-checkboxes\">\r\n            <Form.Check\r\n                type=\"checkbox\"\r\n                id={`all`}\r\n                checked={allhazard}\r\n                label={t(\"Events.forms.AllHazards\")}\r\n                onChange={handleAllChecked}\r\n            />\r\n            {hazard.map((item, index) => {\r\n                return (\r\n                    <Form.Check\r\n                        key={index}\r\n                        type=\"checkbox\"\r\n                        id={item.code}\r\n                        label={item.title}\r\n                        value={item.code}\r\n                        onChange={handleIndividualHazardChange}\r\n                        checked={hazard[index].isChecked}\r\n                    />\r\n                );\r\n            })}\r\n\r\n            <Button onClick={resetAllHazard} className=\"btn-plain ps-2\">\r\n                {t(\"ClearAll\")}\r\n            </Button>\r\n        </div>\r\n    );\r\n}\r\n\r\nHazardMultiCheckboxes.defaultProps = {\r\n    filthaz: () => {\r\n        (\"\");\r\n    },\r\n};\r\n\r\nexport default HazardMultiCheckboxes;\r\n\r\nfunction Hazard_setSelected(setSelectedHazard: React.Dispatch<React.SetStateAction<any[]>>, selectedHazard: any[]) {\r\n    setSelectedHazard(selectedHazard);\r\n}\r\n\r\nfunction Hazard_selected(setSelectedHazard: React.Dispatch<React.SetStateAction<any[]>>, selectedHazard: any[]) {\r\n    setSelectedHazard(selectedHazard);\r\n}\r\n", "//Import Library\r\nimport React, { useState, useRef } from \"react\";\r\nimport ReactPaginate from 'react-paginate';\r\nimport Link from 'next/link';\r\nimport Spinner from 'react-bootstrap/Spinner';\r\nimport { Col, Container, Row, } from \"react-bootstrap\";\r\nimport HazardSearch from \"./HazardSearch\";\r\nimport _ from \"lodash\";\r\n\r\n//Import services/components\r\nimport HazardMultiCheckboxes from \"../../components/common/hazardMultiCheckboxes\";\r\nimport apiService from '../../services/apiService';\r\nimport { ALPHABETIC_FILTERS_EN, ALPHABETIC_FILTERS_DE } from \"../../data/alphabet\";\r\nimport PageHeading from \"../../components/common/PageHeading\";\r\nimport { useTranslation } from 'next-i18next';\r\nimport { serverSideTranslations } from 'next-i18next/serverSideTranslations';\r\n\r\nconst Hazard = () => {\r\n  const { t, i18n } = useTranslation('common');\r\n  const currentLang = i18n.language === 'fr' ? 'en' : i18n.language;\r\n  const titleSearch = currentLang ? `title.${currentLang}` : \"title.en\";\r\n  const alphabets = currentLang === 'en' ? ALPHABETIC_FILTERS_EN : ALPHABETIC_FILTERS_DE\r\n  const [intialarr,] = useState(alphabets);\r\n  const [hazardList, sethazardList]: any = useState([]);\r\n  const [splitHazard, setsplitHazard]: any = useState([]);\r\n  const [FilterHazard, setFilterHazard]: any = useState([]);\r\n  const [totalcount, setTotalCount]: any = useState(0);\r\n  const [currentPage, setCurrentPage]: any = useState(1);\r\n  const [postsPerPage,]: any = useState(50);\r\n  const [activeClass, setActiveClass]: any = useState('All');\r\n  const [loader, setLoader]: any = useState(true);\r\n  const [hazPage, sethazPage]: any = useState([]);\r\n  const [filterText, setFilterText] = useState('');\r\n  const [currLang,] = useState(titleSearch);\r\n  const [search, showSearch] = useState(true);\r\n  const [hazardResponse, setHazardResponse] = useState<any>({});\r\n\r\n\r\n\r\n  const splitHazardies = async (hazardParams: any) => {\r\n    await setLoader(true);\r\n    const response = await apiService.get('/hazard', hazardParams);\r\n    if (response && response.data) {\r\n      await setLoader(false);\r\n      await setsplitHazard(response.data);\r\n      await setTotalCount(response.totalCount);\r\n    }\r\n  }\r\n\r\n  const alphabeticFilter = async (_e: any, index: number) => {\r\n    await setActiveClass(intialarr[index]);\r\n    if (intialarr[index] === t(\"All\") || intialarr[index] === t(\"Alle\")) {\r\n\r\n      showSearch(true);\r\n      await setFilterHazard([])\r\n      if (Array.isArray(hazPage) && hazPage.length !== 0) {\r\n        const filtRegion = async () => {\r\n          regionParams.query = { \"hazard_type\": hazPage };\r\n          regionParams.page=1;\r\n          const response = await apiService.get('/hazard', regionParams);\r\n          await sethazardList(response.data);\r\n          await setTotalCount(response.totalCount);\r\n        }\r\n        const regionParams = {\r\n          \"query\": {},\r\n          \"limit\": postsPerPage,\r\n          \"page\": currentPage,\r\n          \"sort\": {[currLang] : \"asc\" }\r\n        };\r\n\r\n        filtRegion();\r\n      }\r\n    }\r\n    else {\r\n      const FilteringByLetter: any[] = [];\r\n      setFilterText(\"\")\r\n      const letter = intialarr[index].toLowerCase();\r\n      showSearch(false);\r\n      splitHazard.map(async (item: any, _i: any) => {\r\n        if (item.title[currentLang].toLowerCase().split(\"\")[0] === letter && item.enabled) {\r\n          const z = { title: item.title, _id: item._id, coordinates: item.coordinates };\r\n          FilteringByLetter.push(z);\r\n        }\r\n      })\r\n\r\n      if (FilteringByLetter.length >= 1) {\r\n        await setFilterHazard(FilteringByLetter);\r\n        setTotalCount(FilteringByLetter.length)\r\n      } else {\r\n        await setFilterHazard([]);\r\n        await sethazardList([]);\r\n        await setTotalCount([]);\r\n        await setHazardResponse({});\r\n      }\r\n    }\r\n  }\r\n\r\n  const paginate = async (selectedItem: any) => {\r\n    const pageNumber = selectedItem.selected + 1; // react-paginate uses 0-based indexing\r\n\r\n    await setCurrentPage(pageNumber);\r\n\r\n    if (Array.isArray(hazPage) && hazPage.length !== 0) {\r\n      const filtRegion = async () => {\r\n        regionParams.query = { \"hazard_type\": hazPage }\r\n        const response = await apiService.get('/hazard', regionParams);\r\n        await sethazardList(response.data);\r\n        await setTotalCount(response.totalCount);\r\n        await setHazardResponse(response);\r\n      }\r\n\r\n      const regionParams = {\r\n        \"query\": {},\r\n        \"limit\": postsPerPage,\r\n        \"page\": pageNumber,\r\n        \"sort\": {[currLang] : \"asc\"  }\r\n      };\r\n\r\n      filtRegion();\r\n    }\r\n    else {\r\n      const nextHazards = async () => {\r\n        const response = await apiService.get('/hazard', operationParamss);\r\n        sethazardList(response.data);\r\n      }\r\n\r\n      const operationParamss = {\r\n        \"query\": {},\r\n        \"limit\": postsPerPage,\r\n        \"page\": pageNumber,\r\n        \"sort\": { [currLang] : \"asc\" }\r\n      };\r\n      await nextHazards();\r\n    }\r\n  };\r\n\r\n  const filterhazard = async (getHazard: any[]) => {\r\n    showSearch(true);\r\n\r\n    // If no hazards are selected (empty array), show no results\r\n    if (Array.isArray(getHazard) && getHazard.length === 0) {\r\n      await sethazardList([]);\r\n      await setTotalCount(0);\r\n      await setActiveClass(\"All\");\r\n      await setHazardResponse({});\r\n      await setsplitHazard([]);\r\n      setFilterHazard([]);\r\n      sethazPage([]);\r\n      return;\r\n    }\r\n\r\n    const operationParams1 = {\r\n      \"query\": { \"hazard_type\": getHazard },\r\n      \"limit\": \"~\",\r\n      \"sort\": { [currLang] : \"asc\" }\r\n    };\r\n    splitHazardies(operationParams1);\r\n\r\n    sethazPage(getHazard)\r\n    let filterReghazard = [];\r\n    const regionParams = {\r\n      \"query\": {},\r\n      \"limit\": postsPerPage,\r\n      \"page\": currentPage,\r\n      \"sort\": { [currLang] : \"asc\" }\r\n    };\r\n\r\n    const filtHazard = async () => {\r\n      regionParams.sort = { [currLang] : \"asc\" };\r\n      regionParams.query = { \"hazard_type\": getHazard };\r\n      regionParams.page = 1;\r\n      const response = await apiService.get('/hazard', regionParams);\r\n      filterReghazard = response.data;\r\n      await sethazardList(filterReghazard);\r\n      await setTotalCount(response.totalCount);\r\n      await setActiveClass(\"All\");\r\n      await setHazardResponse(response);\r\n      setFilterHazard([])\r\n    }\r\n    filtHazard();\r\n\r\n  }\r\n\r\n  //Search Hazard\r\n  const searchParams = {\r\n    \"query\": {},\r\n    \"sort\": {[currLang] : \"asc\" }\r\n  }\r\n  const searchHazard = async () => {\r\n    const response = await apiService.get('/hazard', searchParams);\r\n    sethazardList(response.data);\r\n  }\r\n\r\n\r\n  const sendQuery = (q: any,hazPagevalue: any) => {\r\n    q ? searchParams.query = { [currLang] : q && q[0].toUpperCase() + q.slice(1).toLowerCase() } : searchParams.query = {};\r\n    activeClass !== \"ACTIVE\" && (searchParams.query = { ...searchParams.query, hazard_type: hazPagevalue });\r\n    searchHazard();\r\n  }\r\n\r\n  const handleSearchTitle = useRef(\r\n    _.debounce((q,hazPageinit) => sendQuery(q,hazPageinit), Number(process.env.SEARCH_DEBOUNCE_TIME) || 300)\r\n  ).current;\r\n\r\n  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {\r\n    setFilterText(e.target.value);\r\n    handleSearchTitle(e.target.value, hazPage)\r\n  }\r\n  //end\r\n  return (\r\n    <Container fluid className=\"p-0\">\r\n      <Row>\r\n        <Col xs={12}>\r\n          <PageHeading title={t('menu.hazards')} />\r\n        </Col>\r\n      </Row>\r\n      <div className=\"hazard-image-block\">\r\n        <img className=\"hazard-image-cover\" src=\"/images/hazard.838eccb4.jpg\" alt=\"Logo\" />\r\n        <HazardMultiCheckboxes  currentLang ={currentLang}filterByletter={activeClass} filthaz={filterhazard} />\r\n      </div>\r\n      <div className=\"alphabetBlock\">\r\n        <div className=\"alphabetContainer\">\r\n          {intialarr.map((item, index) => (\r\n            <span key={index} className={`alphabetItems ${activeClass === item ? 'active' : ''}`} onClick={(e) => alphabeticFilter(e, index)}>{item}</span>\r\n          ))}\r\n        </div>\r\n        {search && (<div>\r\n          <Row>\r\n            <Col className=\"mt-3 mx-3\">\r\n              <HazardSearch onFilter={handleChange} filterText={filterText} />\r\n            </Col>\r\n          </Row>\r\n        </div>)\r\n        }\r\n        <div className=\"alphabetLists\">\r\n          {\r\n             loader ? <Spinner animation=\"border\" variant=\"primary\" /> :\r\n             FilterHazard.length !== 0 ? FilterHazard.map((item: any, index: any) => (\r\n               <li key={index} className=\"alphaListItems clearfix\">\r\n                 <Link href='/hazard/[...routes]' as={`/hazard/show/${item._id}`}>\r\n                   {item.title && item.title[currentLang] ? item.title[currentLang] : ''}\r\n                 </Link>\r\n               </li>\r\n             )) :\r\n               (hazardList.length >= 1) ? <ul>\r\n                 {hazardList.map((item: any, index: any) => (\r\n                   item.enabled ?\r\n                     <li key={index} className=\"alphaListItems clearfix\">\r\n                       <Link href='/hazard/[...routes]' as={`/hazard/show/${item._id}`}>\r\n                         {item.title && item.title[currentLang] ? item.title[currentLang] : ''}\r\n                       </Link>\r\n                     </li> : null\r\n                 ))}\r\n               </ul> : <div className=\"noresultFound\"> {t(\"Noresultsfound\")} </div>\r\n         }\r\n        </div>\r\n        {\r\n          totalcount > postsPerPage && !filterText ?\r\n            <div className=\"hazards-pagination\">\r\n              <ReactPaginate\r\n                pageCount={Math.ceil(hazardResponse.totalCount / hazardResponse.limit)}\r\n                pageRangeDisplayed={5}\r\n                marginPagesDisplayed={2}\r\n                onPageChange={paginate}\r\n                forcePage={hazardResponse.page - 1} // react-paginate uses 0-based indexing\r\n                containerClassName=\"pagination\"\r\n                pageClassName=\"page-item\"\r\n                pageLinkClassName=\"page-link\"\r\n                previousClassName=\"page-item\"\r\n                previousLinkClassName=\"page-link\"\r\n                nextClassName=\"page-item\"\r\n                nextLinkClassName=\"page-link\"\r\n                activeClassName=\"active\"\r\n                disabledClassName=\"disabled\"\r\n                previousLabel=\"‹\"\r\n                nextLabel=\"›\"\r\n              />\r\n            </div>\r\n            : null\r\n        }\r\n\r\n      </div>\r\n    </Container>\r\n  );\r\n\r\n}\r\n\r\nexport async function getStaticProps({ locale }: { locale: string }) {\r\n  return {\r\n    props: {\r\n      ...(await serverSideTranslations(locale, ['common'])),\r\n    },\r\n  }\r\n}\r\n\r\nexport default Hazard;", "interface PageHeadingProps {\r\n  title: string; // Required based on actual usage\r\n}\r\n\r\nexport default function PageHeading(props: PageHeadingProps) {\r\n  return (\r\n    <h2 className=\"page-heading\">{props.title}</h2>\r\n  )\r\n}\r\n", "\n    (window.__NEXT_P = window.__NEXT_P || []).push([\n      \"/hazard\",\n      function () {\n        return require(\"private-next-pages/hazard/index.tsx\");\n      }\n    ]);\n    if(module.hot) {\n      module.hot.dispose(function () {\n        window.__NEXT_P.push([\"/hazard\"])\n      });\n    }\n  ", "!function(e,a){\"object\"==typeof exports&&\"object\"==typeof module?module.exports=a(require(\"react\")):\"function\"==typeof define&&define.amd?define([\"react\"],a):\"object\"==typeof exports?exports.ReactPaginate=a(require(\"react\")):e.ReactPaginate=a(e.React)}(this,(e=>(()=>{var a={703:(e,a,t)=>{\"use strict\";var r=t(414);function n(){}function i(){}i.resetWarningCache=n,e.exports=function(){function e(e,a,t,n,i,s){if(s!==r){var o=new Error(\"Calling PropTypes validators directly is not supported by the `prop-types` package. Use PropTypes.checkPropTypes() to call them. Read more at http://fb.me/use-check-prop-types\");throw o.name=\"Invariant Violation\",o}}function a(){return e}e.isRequired=e;var t={array:e,bigint:e,bool:e,func:e,number:e,object:e,string:e,symbol:e,any:e,arrayOf:a,element:e,elementType:e,instanceOf:a,node:e,objectOf:a,oneOf:a,oneOfType:a,shape:a,exact:a,checkPropTypes:i,resetWarningCache:n};return t.PropTypes=t,t}},697:(e,a,t)=>{e.exports=t(703)()},414:e=>{\"use strict\";e.exports=\"SECRET_DO_NOT_PASS_THIS_OR_YOU_WILL_BE_FIRED\"},98:a=>{\"use strict\";a.exports=e}},t={};function r(e){var n=t[e];if(void 0!==n)return n.exports;var i=t[e]={exports:{}};return a[e](i,i.exports,r),i.exports}r.n=e=>{var a=e&&e.__esModule?()=>e.default:()=>e;return r.d(a,{a}),a},r.d=(e,a)=>{for(var t in a)r.o(a,t)&&!r.o(e,t)&&Object.defineProperty(e,t,{enumerable:!0,get:a[t]})},r.o=(e,a)=>Object.prototype.hasOwnProperty.call(e,a),r.r=e=>{\"undefined\"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:\"Module\"}),Object.defineProperty(e,\"__esModule\",{value:!0})};var n={};return(()=>{\"use strict\";r.r(n),r.d(n,{default:()=>k});var e=r(98),a=r.n(e),t=r(697),i=r.n(t);function s(){return s=Object.assign?Object.assign.bind():function(e){for(var a=1;a<arguments.length;a++){var t=arguments[a];for(var r in t)Object.prototype.hasOwnProperty.call(t,r)&&(e[r]=t[r])}return e},s.apply(this,arguments)}var o=function(e){var t=e.pageClassName,r=e.pageLinkClassName,n=e.page,i=e.selected,o=e.activeClassName,l=e.activeLinkClassName,c=e.getEventListener,p=e.pageSelectedHandler,u=e.href,g=e.extraAriaContext,d=e.pageLabelBuilder,f=e.rel,b=e.ariaLabel||\"Page \"+n+(g?\" \"+g:\"\"),v=null;return i&&(v=\"page\",b=e.ariaLabel||\"Page \"+n+\" is your current page\",t=void 0!==t?t+\" \"+o:o,void 0!==r?void 0!==l&&(r=r+\" \"+l):r=l),a().createElement(\"li\",{className:t},a().createElement(\"a\",s({rel:f,role:u?void 0:\"button\",className:r,href:u,tabIndex:i?\"-1\":\"0\",\"aria-label\":b,\"aria-current\":v,onKeyPress:p},c(p)),d(n)))};o.propTypes={pageSelectedHandler:i().func.isRequired,selected:i().bool.isRequired,pageClassName:i().string,pageLinkClassName:i().string,activeClassName:i().string,activeLinkClassName:i().string,extraAriaContext:i().string,href:i().string,ariaLabel:i().string,page:i().number.isRequired,getEventListener:i().func.isRequired,pageLabelBuilder:i().func.isRequired,rel:i().string};const l=o;function c(){return c=Object.assign?Object.assign.bind():function(e){for(var a=1;a<arguments.length;a++){var t=arguments[a];for(var r in t)Object.prototype.hasOwnProperty.call(t,r)&&(e[r]=t[r])}return e},c.apply(this,arguments)}var p=function(e){var t=e.breakLabel,r=e.breakAriaLabel,n=e.breakClassName,i=e.breakLinkClassName,s=e.breakHandler,o=e.getEventListener,l=n||\"break\";return a().createElement(\"li\",{className:l},a().createElement(\"a\",c({className:i,role:\"button\",tabIndex:\"0\",\"aria-label\":r,onKeyPress:s},o(s)),t))};p.propTypes={breakLabel:i().oneOfType([i().string,i().node]),breakAriaLabel:i().string,breakClassName:i().string,breakLinkClassName:i().string,breakHandler:i().func.isRequired,getEventListener:i().func.isRequired};const u=p;function g(e){var a=arguments.length>1&&void 0!==arguments[1]?arguments[1]:\"\";return null!=e?e:a}function d(e){return d=\"function\"==typeof Symbol&&\"symbol\"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&\"function\"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?\"symbol\":typeof e},d(e)}function f(){return f=Object.assign?Object.assign.bind():function(e){for(var a=1;a<arguments.length;a++){var t=arguments[a];for(var r in t)Object.prototype.hasOwnProperty.call(t,r)&&(e[r]=t[r])}return e},f.apply(this,arguments)}function b(e,a){for(var t=0;t<a.length;t++){var r=a[t];r.enumerable=r.enumerable||!1,r.configurable=!0,\"value\"in r&&(r.writable=!0),Object.defineProperty(e,r.key,r)}}function v(e,a){return v=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,a){return e.__proto__=a,e},v(e,a)}function h(e,a){if(a&&(\"object\"===d(a)||\"function\"==typeof a))return a;if(void 0!==a)throw new TypeError(\"Derived constructors may only return object or undefined\");return m(e)}function m(e){if(void 0===e)throw new ReferenceError(\"this hasn't been initialised - super() hasn't been called\");return e}function y(e){return y=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)},y(e)}function C(e,a,t){return a in e?Object.defineProperty(e,a,{value:t,enumerable:!0,configurable:!0,writable:!0}):e[a]=t,e}var P=function(e){!function(e,a){if(\"function\"!=typeof a&&null!==a)throw new TypeError(\"Super expression must either be null or a function\");e.prototype=Object.create(a&&a.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,\"prototype\",{writable:!1}),a&&v(e,a)}(o,e);var t,r,n,i,s=(n=o,i=function(){if(\"undefined\"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if(\"function\"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(e){return!1}}(),function(){var e,a=y(n);if(i){var t=y(this).constructor;e=Reflect.construct(a,arguments,t)}else e=a.apply(this,arguments);return h(this,e)});function o(e){var t,r;return function(e,a){if(!(e instanceof a))throw new TypeError(\"Cannot call a class as a function\")}(this,o),C(m(t=s.call(this,e)),\"handlePreviousPage\",(function(e){var a=t.state.selected;t.handleClick(e,null,a>0?a-1:void 0,{isPrevious:!0})})),C(m(t),\"handleNextPage\",(function(e){var a=t.state.selected,r=t.props.pageCount;t.handleClick(e,null,a<r-1?a+1:void 0,{isNext:!0})})),C(m(t),\"handlePageSelected\",(function(e,a){if(t.state.selected===e)return t.callActiveCallback(e),void t.handleClick(a,null,void 0,{isActive:!0});t.handleClick(a,null,e)})),C(m(t),\"handlePageChange\",(function(e){t.state.selected!==e&&(t.setState({selected:e}),t.callCallback(e))})),C(m(t),\"getEventListener\",(function(e){return C({},t.props.eventListener,e)})),C(m(t),\"handleClick\",(function(e,a,r){var n=arguments.length>3&&void 0!==arguments[3]?arguments[3]:{},i=n.isPrevious,s=void 0!==i&&i,o=n.isNext,l=void 0!==o&&o,c=n.isBreak,p=void 0!==c&&c,u=n.isActive,g=void 0!==u&&u;e.preventDefault?e.preventDefault():e.returnValue=!1;var d=t.state.selected,f=t.props.onClick,b=r;if(f){var v=f({index:a,selected:d,nextSelectedPage:r,event:e,isPrevious:s,isNext:l,isBreak:p,isActive:g});if(!1===v)return;Number.isInteger(v)&&(b=v)}void 0!==b&&t.handlePageChange(b)})),C(m(t),\"handleBreakClick\",(function(e,a){var r=t.state.selected;t.handleClick(a,e,r<e?t.getForwardJump():t.getBackwardJump(),{isBreak:!0})})),C(m(t),\"callCallback\",(function(e){void 0!==t.props.onPageChange&&\"function\"==typeof t.props.onPageChange&&t.props.onPageChange({selected:e})})),C(m(t),\"callActiveCallback\",(function(e){void 0!==t.props.onPageActive&&\"function\"==typeof t.props.onPageActive&&t.props.onPageActive({selected:e})})),C(m(t),\"getElementPageRel\",(function(e){var a=t.state.selected,r=t.props,n=r.nextPageRel,i=r.prevPageRel,s=r.selectedPageRel;return a-1===e?i:a===e?s:a+1===e?n:void 0})),C(m(t),\"pagination\",(function(){var e=[],r=t.props,n=r.pageRangeDisplayed,i=r.pageCount,s=r.marginPagesDisplayed,o=r.breakLabel,l=r.breakClassName,c=r.breakLinkClassName,p=r.breakAriaLabels,g=t.state.selected;if(i<=n)for(var d=0;d<i;d++)e.push(t.getPageElement(d));else{var f=n/2,b=n-f;g>i-n/2?f=n-(b=i-g):g<n/2&&(b=n-(f=g));var v,h,m=function(e){return t.getPageElement(e)},y=[];for(v=0;v<i;v++){var C=v+1;if(C<=s)y.push({type:\"page\",index:v,display:m(v)});else if(C>i-s)y.push({type:\"page\",index:v,display:m(v)});else if(v>=g-f&&v<=g+(0===g&&n>1?b-1:b))y.push({type:\"page\",index:v,display:m(v)});else if(o&&y.length>0&&y[y.length-1].display!==h&&(n>0||s>0)){var P=v<g?p.backward:p.forward;h=a().createElement(u,{key:v,breakAriaLabel:P,breakLabel:o,breakClassName:l,breakLinkClassName:c,breakHandler:t.handleBreakClick.bind(null,v),getEventListener:t.getEventListener}),y.push({type:\"break\",index:v,display:h})}}y.forEach((function(a,t){var r=a;\"break\"===a.type&&y[t-1]&&\"page\"===y[t-1].type&&y[t+1]&&\"page\"===y[t+1].type&&y[t+1].index-y[t-1].index<=2&&(r={type:\"page\",index:a.index,display:m(a.index)}),e.push(r.display)}))}return e})),void 0!==e.initialPage&&void 0!==e.forcePage&&console.warn(\"(react-paginate): Both initialPage (\".concat(e.initialPage,\") and forcePage (\").concat(e.forcePage,\") props are provided, which is discouraged.\")+\" Use exclusively forcePage prop for a controlled component.\\nSee https://reactjs.org/docs/forms.html#controlled-components\"),r=e.initialPage?e.initialPage:e.forcePage?e.forcePage:0,t.state={selected:r},t}return t=o,(r=[{key:\"componentDidMount\",value:function(){var e=this.props,a=e.initialPage,t=e.disableInitialCallback,r=e.extraAriaContext,n=e.pageCount,i=e.forcePage;void 0===a||t||this.callCallback(a),r&&console.warn(\"DEPRECATED (react-paginate): The extraAriaContext prop is deprecated. You should now use the ariaLabelBuilder instead.\"),Number.isInteger(n)||console.warn(\"(react-paginate): The pageCount prop value provided is not an integer (\".concat(n,\"). Did you forget a Math.ceil()?\")),void 0!==a&&a>n-1&&console.warn(\"(react-paginate): The initialPage prop provided is greater than the maximum page index from pageCount prop (\".concat(a,\" > \").concat(n-1,\").\")),void 0!==i&&i>n-1&&console.warn(\"(react-paginate): The forcePage prop provided is greater than the maximum page index from pageCount prop (\".concat(i,\" > \").concat(n-1,\").\"))}},{key:\"componentDidUpdate\",value:function(e){void 0!==this.props.forcePage&&this.props.forcePage!==e.forcePage&&(this.props.forcePage>this.props.pageCount-1&&console.warn(\"(react-paginate): The forcePage prop provided is greater than the maximum page index from pageCount prop (\".concat(this.props.forcePage,\" > \").concat(this.props.pageCount-1,\").\")),this.setState({selected:this.props.forcePage})),Number.isInteger(e.pageCount)&&!Number.isInteger(this.props.pageCount)&&console.warn(\"(react-paginate): The pageCount prop value provided is not an integer (\".concat(this.props.pageCount,\"). Did you forget a Math.ceil()?\"))}},{key:\"getForwardJump\",value:function(){var e=this.state.selected,a=this.props,t=a.pageCount,r=e+a.pageRangeDisplayed;return r>=t?t-1:r}},{key:\"getBackwardJump\",value:function(){var e=this.state.selected-this.props.pageRangeDisplayed;return e<0?0:e}},{key:\"getElementHref\",value:function(e){var a=this.props,t=a.hrefBuilder,r=a.pageCount,n=a.hrefAllControls;if(t)return n||e>=0&&e<r?t(e+1,r,this.state.selected):void 0}},{key:\"ariaLabelBuilder\",value:function(e){var a=e===this.state.selected;if(this.props.ariaLabelBuilder&&e>=0&&e<this.props.pageCount){var t=this.props.ariaLabelBuilder(e+1,a);return this.props.extraAriaContext&&!a&&(t=t+\" \"+this.props.extraAriaContext),t}}},{key:\"getPageElement\",value:function(e){var t=this.state.selected,r=this.props,n=r.pageClassName,i=r.pageLinkClassName,s=r.activeClassName,o=r.activeLinkClassName,c=r.extraAriaContext,p=r.pageLabelBuilder;return a().createElement(l,{key:e,pageSelectedHandler:this.handlePageSelected.bind(null,e),selected:t===e,rel:this.getElementPageRel(e),pageClassName:n,pageLinkClassName:i,activeClassName:s,activeLinkClassName:o,extraAriaContext:c,href:this.getElementHref(e),ariaLabel:this.ariaLabelBuilder(e),page:e+1,pageLabelBuilder:p,getEventListener:this.getEventListener})}},{key:\"render\",value:function(){var e=this.props.renderOnZeroPageCount;if(0===this.props.pageCount&&void 0!==e)return e?e(this.props):e;var t=this.props,r=t.disabledClassName,n=t.disabledLinkClassName,i=t.pageCount,s=t.className,o=t.containerClassName,l=t.previousLabel,c=t.previousClassName,p=t.previousLinkClassName,u=t.previousAriaLabel,d=t.prevRel,b=t.nextLabel,v=t.nextClassName,h=t.nextLinkClassName,m=t.nextAriaLabel,y=t.nextRel,C=this.state.selected,P=0===C,k=C===i-1,x=\"\".concat(g(c)).concat(P?\" \".concat(g(r)):\"\"),L=\"\".concat(g(v)).concat(k?\" \".concat(g(r)):\"\"),N=\"\".concat(g(p)).concat(P?\" \".concat(g(n)):\"\"),O=\"\".concat(g(h)).concat(k?\" \".concat(g(n)):\"\"),R=P?\"true\":\"false\",E=k?\"true\":\"false\";return a().createElement(\"ul\",{className:s||o,role:\"navigation\",\"aria-label\":\"Pagination\"},a().createElement(\"li\",{className:x},a().createElement(\"a\",f({className:N,href:this.getElementHref(C-1),tabIndex:P?\"-1\":\"0\",role:\"button\",onKeyPress:this.handlePreviousPage,\"aria-disabled\":R,\"aria-label\":u,rel:d},this.getEventListener(this.handlePreviousPage)),l)),this.pagination(),a().createElement(\"li\",{className:L},a().createElement(\"a\",f({className:O,href:this.getElementHref(C+1),tabIndex:k?\"-1\":\"0\",role:\"button\",onKeyPress:this.handleNextPage,\"aria-disabled\":E,\"aria-label\":m,rel:y},this.getEventListener(this.handleNextPage)),b)))}}])&&b(t.prototype,r),Object.defineProperty(t,\"prototype\",{writable:!1}),o}(e.Component);C(P,\"propTypes\",{pageCount:i().number.isRequired,pageRangeDisplayed:i().number,marginPagesDisplayed:i().number,previousLabel:i().node,previousAriaLabel:i().string,prevPageRel:i().string,prevRel:i().string,nextLabel:i().node,nextAriaLabel:i().string,nextPageRel:i().string,nextRel:i().string,breakLabel:i().oneOfType([i().string,i().node]),breakAriaLabels:i().shape({forward:i().string,backward:i().string}),hrefBuilder:i().func,hrefAllControls:i().bool,onPageChange:i().func,onPageActive:i().func,onClick:i().func,initialPage:i().number,forcePage:i().number,disableInitialCallback:i().bool,containerClassName:i().string,className:i().string,pageClassName:i().string,pageLinkClassName:i().string,pageLabelBuilder:i().func,activeClassName:i().string,activeLinkClassName:i().string,previousClassName:i().string,nextClassName:i().string,previousLinkClassName:i().string,nextLinkClassName:i().string,disabledClassName:i().string,disabledLinkClassName:i().string,breakClassName:i().string,breakLinkClassName:i().string,extraAriaContext:i().string,ariaLabelBuilder:i().func,eventListener:i().string,renderOnZeroPageCount:i().func,selectedPageRel:i().string}),C(P,\"defaultProps\",{pageRangeDisplayed:2,marginPagesDisplayed:3,activeClassName:\"selected\",previousLabel:\"Previous\",previousClassName:\"previous\",previousAriaLabel:\"Previous page\",prevPageRel:\"prev\",prevRel:\"prev\",nextLabel:\"Next\",nextClassName:\"next\",nextAriaLabel:\"Next page\",nextPageRel:\"next\",nextRel:\"next\",breakLabel:\"...\",breakAriaLabels:{forward:\"Jump forward\",backward:\"Jump backward\"},disabledClassName:\"disabled\",disableInitialCallback:!1,pageLabelBuilder:function(e){return e},eventListener:\"onClick\",renderOnZeroPageCount:void 0,selectedPageRel:\"canonical\",hrefAllControls:!1});const k=P})(),n})()));\n//# sourceMappingURL=react-paginate.js.map"], "names": ["ALPHABETIC_FILTERS_DE", "filterText", "HazardSearch", "onFilter", "t", "useTranslation", "Container", "fluid", "className", "Row", "InputGroup", "Form", "Control", "type", "placeholder", "value", "onChange", "div", "FontAwesomeIcon", "icon", "faSearch", "InputGroupText", "React", "ref", "bsPrefix", "as", "Component", "props", "useBootstrapPrefix", "_jsx", "classNames", "size", "hasValidation", "contextValue", "useMemo", "InputGroupContext", "Provider", "children", "displayName", "Object", "assign", "Text", "Radio", "InputGroupRadio", "FormCheckInput", "Checkbox", "InputGroupCheckbox", "HazardMultiCheckboxes", "filthaz", "allhazard", "setAllhazard", "useState", "hazard", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "setSelectedHazard", "RegionParams", "query", "limit", "sort", "title", "gethazard", "RegionParams_initial", "response", "apiService", "get", "Array", "isArray", "data", "final<PERSON><PERSON><PERSON>", "selectedIds", "_", "item", "_i", "hazardItem", "isChecked", "push", "_id", "useEffect", "handleIndividualHazardChange", "updated<PERSON><PERSON>d", "updatedSelectedHazard", "for<PERSON>ach", "index", "code", "e", "target", "id", "checked", "filter", "n", "Check", "label", "handleAllChecked", "map", "event", "selected_Hazard", "<PERSON><PERSON>", "onClick", "resetAll<PERSON><PERSON>d", "defaultProps", "Hazard", "i18n", "currentLang", "language", "alphabets", "ALPHABETIC_FILTERS_EN", "in<PERSON><PERSON><PERSON>", "hazardList", "sethazardList", "splitHazard", "setsplit<PERSON><PERSON>d", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "totalcount", "setTotalCount", "currentPage", "setCurrentPage", "postsPerPage", "activeClass", "setActiveClass", "loader", "<PERSON><PERSON><PERSON><PERSON>", "hazPage", "sethazPage", "setFilterText", "currLang", "titleSearch", "search", "showSearch", "hazardResponse", "setHazardResponse", "splitHazardies", "hazardParams", "totalCount", "alphabeticFilter", "_e", "length", "regionParams", "filtRegion", "page", "FilteringByLetter", "letter", "toLowerCase", "split", "enabled", "z", "coordinates", "paginate", "selectedItem", "pageNumber", "selected", "nextHazards", "operationParamss", "filterhazard", "<PERSON><PERSON><PERSON><PERSON>", "operationParams1", "filterReghazard", "filtHazard", "searchParams", "searchHazard", "<PERSON><PERSON><PERSON><PERSON>", "q", "hazPagevalue", "toUpperCase", "slice", "hazard_type", "handleSearchTitle", "useRef", "hazPageinit", "Number", "process", "current", "Col", "xs", "PageHeading", "img", "src", "alt", "filterByletter", "span", "handleChange", "Spinner", "animation", "variant", "li", "Link", "href", "ul", "ReactPaginate", "pageCount", "Math", "ceil", "pageRangeDisplayed", "marginPagesDisplayed", "onPageChange", "forcePage", "containerClassName", "pageClassName", "pageLinkClassName", "previousClassName", "previousLinkClassName", "nextClassName", "nextLinkClassName", "activeClassName", "disabledClassName", "previousLabel", "next<PERSON><PERSON><PERSON>", "h2"], "sourceRoot": "", "ignoreList": [2, 3, 8]}