{"version": 3, "file": "static/chunks/pages/vspace/AnnouncementsAccordian-982dc83a0fd77e01.js", "mappings": "4MAgCA,MArBgCA,IAC5B,GAAM,CAAEC,CAAC,CAAE,CAAGC,CAAAA,EAAAA,EAAAA,EAAAA,CAAcA,CAAC,QAoBlBC,EAnBL,CAACC,EAASC,EAAW,CAAGC,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,EAAC,GACvC,MACI,+BACI,WAACC,EAAAA,CAASA,CAACC,IAAI,EAACC,SAAS,cACvB,WAACF,EAAAA,CAASA,CAACG,MAAM,EAACC,QAAS,IAAMN,EAAW,CAACD,aAC3C,UAACQ,MAAAA,CAAIC,UAAU,qBAAaZ,EAAE,0BAC9B,UAACW,MAAAA,CAAIC,UAAU,qBACZT,EAAU,UAACU,EAAAA,CAAeA,CAAAA,CAACC,KAAMC,EAAAA,GAAOA,CAAEC,MAAM,SAC/C,UAACH,EAAAA,CAAeA,CAAAA,CAACC,KAAMG,EAAAA,GAAMA,CAAED,MAAM,cAG3C,UAACV,EAAAA,CAASA,CAACY,IAAI,WACb,UAACC,EAAAA,OAAYA,CAAAA,CAAAA,SAK7B,2ECEA,MAVA,cACA,MAAkB,CASH,EATG,SAAM,IASM,CAR5B,eAAS,MACX,cACA,aACA,MACA,CACA,UACA,CAAG,GACH,4FCzBA,IAAMC,EAA+BC,EAAAA,UAAgB,CAAC,GAA9B,QAA+B,GAApB,QACjCT,CAAS,CACTU,UAAQ,CACRC,GAAIC,EAAY,KAAK,CACrB,GAAGzB,EACJ,GAEC,OAAO,EADI0B,CAAAA,EAAAA,EAAAA,EAAAA,CAAkBA,CAACH,EAAU,oBACpBI,CAAAA,EAAAA,EAAAA,GAAAA,CAAIA,CAACF,EAAW,CAClCG,IAAKA,EACLf,UAAWgB,IAAWhB,EAAWU,GACjC,GAAGvB,CAAK,EAEZ,GACAqB,EAJyBQ,WAIE,CAAG,kBCb9B,IAAMC,EAA4BR,EAAAA,UAAgB,CAA7B,CAA8B,EAMhDM,QAN6B,CAE9BJ,CADA,EACIC,EAAY,KAAK,UACrBF,CAAQ,WACRV,CAAS,CACT,GAAGb,EACJ,GACO+B,EAAiBF,IAAWhB,EAAWa,CAAAA,EAAAA,EAAAA,EAAAA,CAAkBA,CAACH,EAAU,GAAzCM,eACjC,MAAoBF,CAAAA,EAAAA,EAAAA,GAAAA,CAAIA,CAACF,EAAW,CAClCG,IAAKA,EACL,GAAG5B,CAAK,CACRa,UAAWkB,CACb,EACF,GACAD,EAAaE,WAAW,CAAG,iBAbkI,8CCsB7J,IAAMC,EAGNX,EAAAA,OAFA,GAEgB,CAAC,GAGdM,IALQ,GACX,EA2EMM,EA1EY,oBAChBC,EAAqB,CAAC,CACtB,GAAGC,EACJ,GACO,CAEJZ,CADA,EACIC,EAAY,IAP0B,CAOrB,UACrBF,CAAQ,OACRc,GAAQ,CAAI,MACZC,GAAO,CAAK,UACZC,EAAW,EAAI,YACfC,EAAa,EAAI,iBACjBC,EAAkB,EAAE,aACpBC,CAAW,UACXC,CAAQ,SACRC,CAAO,CACPC,QAAM,UACNC,EAAW,GAAI,IAZ4I,MAa3JC,GAAW,CAAI,WACfC,CAAS,OACTC,EAAQ,OAAO,aACfC,CAAW,YACXC,CAAU,MACVC,GAAO,CAAI,OACXC,GAAQ,CAAI,cACZC,CAAY,aACZC,CAAW,YACXC,CAAU,UACVC,EAAwB9B,CAAAA,EAAAA,EAAAA,GAAAA,CAAb,CAAkB,OAAQ,CACnC,EADoB,YACL,OACfd,UAAW,4BACb,EAAE,WACF6C,EAAY,UAAU,UACtBC,EAAwBhC,CAAAA,EAAAA,EAAAA,GAAAA,CAAb,CAAkB,OAAQ,CACnC,EADoB,YACL,OACfd,UAAW,4BACb,EAAE,WACF+C,EAAY,MAAM,SAClBC,CAAO,WACPhD,CAAS,UACTiD,CAAQ,CACR,GAAG9D,EACJ,CAAG+D,CAAAA,EAAAA,EAAAA,EAAAA,CAAeA,CAAC,oBAClB5B,EACA,GAAGC,CAAiB,EACnB,CACDM,YAAa,UACf,GACMsB,EAAStC,CAAAA,EAAAA,EAAAA,EAAAA,CAAkBA,CAACH,EAAU,YACtC0C,EAAQC,CAAAA,EAAAA,EAAAA,EAAAA,CAAQA,GAChBC,EAAmBC,CAAAA,EAAAA,EAAAA,MAAAA,CAAMA,CAAC,MAC1B,CAACC,EAAWC,EAAa,CAAGhE,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAC,QACrC,CAACiE,GAAQC,GAAU,CAAGlE,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,EAAC,GAC/B,CAACmE,GAAWC,GAAa,CAAGpE,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,EAAC,GACrC,CAACqE,GAAqBC,GAAuB,CAAGtE,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAACoC,GAAe,GAC9EmC,CAAAA,EAAAA,EAAAA,SAAAA,CAASA,CAAC,KACHJ,IAAa/B,IAAgBiC,KAC5BR,EAAiBW,OAAO,CAC1BR,CAD4B,CACfH,EAAiBW,EAFqB,KAEd,EAErCR,EAAa,CAAC5B,IAAe,EAAKiC,GAAsB,OAAS,QAE/DtC,GACFqC,IADS,GAGXE,GAAuBlC,GAAe,GAE1C,EAAG,CAACA,EAAa+B,GAAWE,GAAqBtC,EAAM,EACvDwC,CAAAA,EAAAA,EAAAA,SAAAA,CAASA,CAAC,KACJV,EAAiBW,OAAO,EAAE,CAC5BX,EAAiBW,OAAO,CAAG,KAE/B,GACA,IAAIC,GAAc,EAKlBC,CAAAA,EAAAA,EAAAA,EAAAA,CAAOA,CAAClB,EAAU,CAACmB,EAAOC,KACxB,EAAEH,GACEG,IAAUxC,IACZR,EAAsB+C,EAAMjF,KADH,CACS8C,QAAAA,CAEtC,GACA,IAAMqC,GAAyBC,CAAAA,EAAAA,EAAAA,CAAAA,CAAeA,CAAClD,GACzCmD,GAAOC,CAAAA,EAAAA,EAAAA,WAAAA,CAAWA,CAACC,IACvB,GAAId,GACF,OAEF,CAHe,GAGXe,EAAkBb,GAAsB,EAC5C,GAAIa,EAAkB,EAAG,CACvB,GAAI,CAACpC,EACH,IADS,GAGXoC,EAAkBT,GAAc,CAClC,CACAZ,EAAiBW,OAAO,CAAG,OACf,MAAZnC,GAAoBA,EAAS6C,EAAiBD,EAChD,EAAG,CAACd,GAAWE,GAAqBhC,EAAUS,EAAM2B,GAAY,EAG1DU,GAAOC,CAAAA,EAAAA,EAAAA,CAAAA,CAAgBA,CAACH,IAC5B,GAAId,GACF,OAEF,CAHe,GAGXe,EAAkBb,GAAsB,EAC5C,GAAIa,GAAmBT,GAAa,CAClC,GAAI,CAAC3B,EACH,IADS,GAGXoC,EAAkB,CACpB,CACArB,EAAiBW,OAAO,CAAG,OACf,MAAZnC,GAAoBA,EAAS6C,EAAiBD,EAChD,GACMI,GAAavB,CAAAA,EAAAA,EAAAA,MAAAA,CAAMA,GACzBwB,CAAAA,EAAAA,EAAAA,mBAAAA,CAAmBA,CAAChE,EAAK,IAAO,EAC9BiE,QAASF,GAAWb,OAAO,MAC3BO,QACAI,GACF,GAGA,IAAMK,GAAkBJ,CAAAA,EAAAA,EAAAA,CAAAA,CAAgBA,CAAC,KACnC,CAACK,SAASC,MAAM,EAtIxB,SAAmBH,CAAO,EACxB,GAAI,CAACA,GAAW,CAACA,EAAQI,KAAK,EAAI,CAACJ,EAAQK,UAAU,EAAI,CAACL,EAAQK,UAAU,CAACD,KAAK,CAChF,CADkF,MAC3E,EAET,IAAME,EAAeC,iBAAiBP,GACtC,MAAgC,SAAzBM,EAAaE,OAAO,EAA2C,WAA5BF,EAAaG,UAAU,EAAkE,SAAjDF,iBAAiBP,EAAQK,UAAU,EAAEG,OAAO,EAiI1FV,GAAWb,OAAO,GAAG,CACjDb,EACFoB,KADS,KAMf,GACMkB,GAA+B,SAAdlC,EAAuB,QAAU,MACxDmC,EAAgB,KACVnE,IAIO,GAJA,GAIXO,EALa4D,CAKM5D,EAAQ+B,GAAqB4B,IACtC,MAAV1D,GAAkBA,EAAO8B,GAAqB4B,IAChD,EAAG,CAAC5B,GAAoB,EACxB,IAAM8B,GAAiB,GAAkBpC,MAAAA,CAAfL,EAAO,UAAkB,OAAVK,GACnCqC,GAAuB,GAAkBH,MAAAA,CAAfvC,EAAO,UAAuB,OAAfuC,IACzCI,GAAcrB,CAAAA,EAAAA,EAAAA,WAAAA,CAAWA,CAACsB,IAC9BC,CAAAA,EAAAA,EAAAA,CAAAA,CAAoBA,CAACD,GACV,MAAXhE,GAAmBA,EAAQ+B,GAAqB4B,GAClD,EAAG,CAAC3D,EAAS+B,GAAqB4B,GAAe,EAC3CO,GAAgBxB,CAAAA,EAAAA,EAAAA,WAAAA,CAAWA,CAAC,KAChCZ,IAAa,GACH,MAAV7B,GAAkBA,EAAO8B,GAAqB4B,GAChD,EAAG,CAAC1D,EAAQ8B,GAAqB4B,GAAe,EAC1CQ,GAAgBzB,CAAAA,EAAAA,EAAAA,WAAAA,CAAWA,CAACC,IAChC,GAAIxC,GAAY,CAAC,kBAAkBiE,IAAI,CAACzB,EAAM0B,MAAM,CAACC,OAAO,EAC1D,CAD6D,MACrD3B,EAAM4B,GAAG,EACf,IAAK,YACH5B,EAAM6B,cAAc,GAChBnD,EACFwB,GAAKF,EADI,CAGTF,GAAKE,GAEP,MACF,KAAK,aACHA,EAAM6B,cAAc,GAChBnD,EACFoB,GAAKE,EADI,CAGTE,GAAKF,GAEP,MAEJ,CAEW,MAAbvC,GAAqBA,EAAUuC,EACjC,EAAG,CAACxC,EAAUC,EAAWqC,GAAMI,GAAMxB,EAAM,EACrCoD,GAAkB/B,CAAAA,EAAAA,EAAAA,WAAAA,CAAWA,CAACC,IACpB,SAAS,CAAnBtC,GACFuB,IAAU,GAEG,MAAftB,GAAuBA,EAAYqC,EACrC,EAAG,CAACtC,EAAOC,EAAY,EACjBoE,GAAiBhC,CAAAA,EAAAA,EAAAA,WAAAA,CAAWA,CAACC,IACjCf,IAAU,GACVrB,SAAsBA,EAAWoC,EACnC,EAAG,CAACpC,EAAW,EACToE,GAAiBnD,CAAAA,EAAAA,EAAAA,MAAAA,CAAMA,CAAC,GACxBoD,GAAiBpD,CAAAA,EAAAA,EAAAA,MAAAA,CAAMA,CAAC,GACxBqD,GAAsBC,CAAAA,EAAAA,EAAAA,CAAAA,CAAUA,GAChCC,GAAmBrC,CAAAA,EAAAA,EAAAA,WAAAA,CAAWA,CAACC,IACnCgC,GAAezC,OAAO,CAAGS,EAAMqC,OAAO,CAAC,EAAE,CAACC,OAAO,CACjDL,GAAe1C,OAAO,CAAG,EACX,SAAS,CAAnB7B,GACFuB,IAAU,GAEI,MAAhBlB,GAAwBA,EAAaiC,EACvC,EAAG,CAACtC,EAAOK,EAAa,EAClBwE,GAAkBxC,CAAAA,EAAAA,EAAAA,WAAAA,CAAWA,CAACC,IAC9BA,EAAMqC,OAAO,EAAIrC,EAAMqC,OAAO,CAACG,MAAM,CAAG,EAC1CP,CAD6C,EAC9B1C,OAAO,CAAG,EAEzB0C,GAAe1C,OAAO,CAAGS,EAAMqC,OAAO,CAAC,EAAE,CAACC,OAAO,CAAGN,GAAezC,OAAO,CAE7D,MAAfvB,GAAuBA,EAAYgC,EACrC,EAAG,CAAChC,EAAY,EACVyE,GAAiB1C,CAAAA,EAAAA,EAAAA,WAAAA,CAAWA,CAACC,IACjC,GAAIlC,EAAO,CACT,IAAM4E,EAAcT,GAAe1C,OAAO,CACtCoD,KAAKC,GAAG,CAACF,GA1NK,KA2NZA,EAAc,EAChB5C,CADmB,EADK+C,GAIxB3C,GAAKF,GAGX,CACc,OARiC,EAQxB,CAAnBtC,GACFwE,GAAoBY,GAAG,CAAC,KACtB7D,IAAU,EACZ,EAAG1B,QAAYwF,GAEH,MAAd9E,GAAsBA,EAAW+B,EACnC,EAAG,CAAClC,EAAOJ,EAAOoC,GAAMI,GAAMgC,GAAqB3E,EAAUU,EAAW,EAClE+E,GAAyB,MAAZzF,GAAoB,CAACyB,IAAU,CAACE,GAC7C+D,GAAoBpE,CAAAA,EAAAA,EAAAA,MAAAA,CAAMA,GAChCS,CAAAA,EAAAA,EAAAA,SAAAA,CAASA,CAAC,KACR,IAAI4D,EAAMC,EACV,GAAI,CAACH,GACH,OAAOD,EADQ,EAGXK,EAAW1E,EAAQoB,GAAOI,GAEhC,OADA+C,GAAkB1D,OAAO,CAAG8D,OAAOC,WAAW,CAAC9C,SAAS+C,eAAe,CAAGhD,GAAkB6C,EAAU,OAACF,EAAO,OAACC,EAAwBvD,GAAuBL,OAAAA,EAAmB4D,EAAwB5F,CAAAA,CAAO,CAAa2F,OAAOH,GAC7N,KACDE,MAAoC,IAAlB1D,OAAO,EAC3BiE,cAAcP,GAAkB1D,OAAO,CAE3C,CACF,EAAG,CAACyD,GAAYlD,GAAMI,GAAMN,GAAwBrC,EAAUgD,GAAiB7B,EAAM,EACrF,IAAM+E,GAAoBC,CAAAA,EAAAA,EAAAA,OAAAA,CAAOA,CAAC,IAAMzG,GAAc0G,MAAMC,IAAI,CAAC,CAC/DpB,OAAQhD,EACV,EAAG,CAACqE,EAAGlE,IAAUK,IACH,MAAZ5C,GAAoBA,EAASuC,EAAOK,EACtC,GAAI,CAAC/C,EAAYuC,GAAapC,EAAS,EACvC,MAAoB0G,CAAb,EAAaA,EAAAA,IAAAA,CAAKA,CAAC5H,CAAR,CAAmB,CACnCG,IAAK+D,GACL,GAAG3F,CAAK,CACRgD,UAAW+D,GACX7D,YAAamE,GACblE,WAAYmE,GACZhE,aAAcqE,GACdpE,YAAauE,GACbtE,WAAYwE,GACZnH,UAAWgB,IAAWhB,EAAWmD,EAAQ3B,GAAS,QAASC,CAAtCT,EAA8C,GAAU,OAAPmC,EAAO,SAAQH,GAAW,GAAaA,MAAAA,CAAVG,EAAO,KAAW,OAARH,IAC7GC,SAAU,CAACtB,GAA2Bb,CAAAA,EAAAA,EAAAA,GAAAA,CAAIA,CAAC,CAAlB,KAAyB,CAChDd,KADkC,KACvB,GAAU,OAAPmD,EAAO,eACrBF,SAAUwF,CAAAA,EAAAA,EAAAA,EAAAA,CAAGA,CAACxF,EAAU,CAACsF,EAAGlE,IAAuBvD,CAAAA,EAAAA,CAAb,CAAaA,GAAAA,CAAIA,CAAC,KAAP,IAAiB,CAChE4H,KAAM,SACN,iBAAkB,GAElB,aAAiC,MAAnB9G,GAA2BA,EAAgBsF,MAAM,CAAGtF,CAAe,CAACyC,EAAM,CAAG,IAF9B,KAEiD,OAAVA,EAAQ,GAC5GrE,UAAWqE,IAAUP,GAAsB,cAAW2D,EACtD3H,QAASqI,GAAoBA,EAAiB,CAAC9D,EAAM,MAAGoD,EACxD,eAAgBpD,IAAUP,EAC5B,EAAGO,GACL,GAAiBvD,CAAAA,EAAAA,EAAAA,GAAAA,CAAIA,CAAC,MAAO,CAC3Bd,UAAW,GAAU,OAAPmD,EAAO,UACrBF,SAAUwF,CAAAA,EAAAA,EAAAA,EAAAA,CAAGA,CAACxF,EAAU,CAACmB,EAAOC,KAC9B,IAAMsE,EAAWtE,IAAUP,GAC3B,OAAOtC,EAAqBV,CAAAA,EAAAA,EAAAA,CAAb,EAAaA,CAAIA,CAAC8H,EAAAA,CAAiBA,CAAE,CAClDC,EADwB,CACpBF,EACJG,QAASH,EAAW7C,QAAc2B,EAClCsB,UAAWJ,EAAW1C,QAAgBwB,EACtCuB,eAAgBC,EAAAA,CAAqBA,CACrChG,SAAU,CAACiG,EAAQC,IAA4B1I,EAAAA,OAAb,KAA+B,CAAC2D,EAAO,CACvE,EAD2C,CACxC+E,CAAU,CACbnJ,UAAWgB,IAAWoD,EAAMjF,KAAK,CAACa,QAAbgB,CAAsB,CAAE2H,GAAYO,eAAwBtD,GAAgB,CAACsD,eAAmC,YAAXA,CAAW,CAAQ,EAAM,SAAU,CAAY,aAAXA,GAAoC,YAAXA,CAAW,CAAQ,EAAMrD,GAClN,EACF,GAAoBpF,EAAb,WAAW,CAAoB,CAAC2D,EAAO,CAC5CpE,UAAWgB,IAAWoD,EAAMjF,KAAK,CAACa,QAAbgB,CAAsB,CAAE2H,GAAY,SAC3D,EACF,EACF,GAAIjH,GAAyB8G,CAAAA,EAAAA,EAAAA,IAAb,CAAkBA,CAACY,EAAAA,OAAR,CAAiBA,CAAE,CAC5CnG,SAAU,CAAEV,CAAAA,OAAQV,CAAgB,GAAmB2G,CAAAA,EAAb,EAAaA,IAAAA,CAAKA,CAACa,EAAAA,CAAR,CAAgB,CACnErJ,UAAW,GAAU,OAAPmD,EAAO,iBACrBrD,QAAS0E,GACTvB,SAAU,CAACL,EAAUC,GAA0B/B,CAAAA,EAAAA,EAAAA,GAAAA,CAAIA,CAAjB,OAA0B,CAC1Dd,GAD2C,OAChC,kBACXiD,SAAUJ,CACZ,GAAG,GACAN,CAAAA,GAAQV,IAAgBqC,IAAc,GAAmBsE,CAAAA,EAAb,EAAaA,IAAAA,CAAKA,CAACa,EAAAA,CAAR,CAAgB,CAC1ErJ,UAAW,GAAU,OAAPmD,EAAO,iBACrBrD,QAAS8E,GACT3B,SAAU,CAACH,EAAUC,GAA0BjC,CAAAA,EAAAA,EAAAA,GAAAA,CAAIA,CAAjB,OAA0B,CAC1Dd,GAD2C,OAChC,kBACXiD,SAAUF,CACZ,GACF,GAAG,GACF,EAEP,GACA3B,EAASD,WAAW,CAAG,WACvB,MAAemI,OAAOC,MAAM,CAACnI,EAAU,CACrCoI,QFzTahJ,CEyTJA,CACTb,KDzTasB,CCyTPA,EACN,EAAC,GF3T2BT,EAAC,ECCJS,CCwTDT,CDxTE,GCyTRS,qLC3TpB,SAASwI,EAAuBtK,CAAkC,EAChE,GAAM,eAAEuK,CAAa,CAAE,CAAGvK,EAC1B,MACE,UAACY,MAAAA,UACE2J,EAAcjB,GAAG,CAAC,CAACkB,EAAMtF,IAEtB,UAACuF,EAAAA,CAAGA,CAAAA,CAAC5J,UAAU,4BACb,UAAC6J,EAAAA,OAAgBA,CAAAA,CAACF,KAAMA,KADatF,KAOjD,CAwHA,MAtHA,SAAS9D,EACP,GAAM,GAAEnB,CAAC,CAAE,CAAGC,CAAAA,EAAAA,EAAAA,EAAAA,CAAcA,CAAC,UAEvByK,CAmHmBvJ,CApHVwJ,CAAAA,EAAAA,EAAAA,SAAAA,CAASA,GACGC,KAAK,CAACF,MAAM,EAAI,EAAE,CACvC,CAACJ,EAAeO,EAAiB,CAAGxK,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAU,EAAE,EAExD,CAACyK,EAAQC,EAAU,CAAG1K,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAC,GAE/B,CAAC2K,EAAkB,CAAG3K,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAC,GAE/B4K,EAAiB,KACrBJ,EAAiB,EAAE,CACrB,EAEMK,EAAgB,CACpBN,MAAO,CAAEO,sBAAsB,EAAMC,cAAeV,CAAM,CAAC,EAAE,EAC7DW,KAAM,CAAEC,WAAY,MAAO,EAC3BC,MAAO,IACPC,OAAQ,2GACV,EAEMC,EAAqB,qBAAOC,EAAAA,UAAAA,MAAAA,CAAAA,GAAAA,KAAAA,IAAAA,SAAAA,CAAAA,EAAAA,CAAAA,SAAAA,CAAAA,EAAAA,CAASR,EACnCS,EAAW,MAAMC,EAAAA,CAAUA,CAACC,GAAG,CAAC,WAAYH,EAC9CC,IAAYA,EAASG,IAAI,EAAIH,EAASG,IAAI,CAAChE,MAAM,CAAG,EAEtD+C,CAFyD,CACvC1B,IAAAA,KAAO,CAACwC,EAASG,GAClBC,CADsB,CAAE,IAGzCd,GAEJ,EAEArG,CAAAA,EAAAA,EAAAA,SAAAA,CAASA,CAAC,KACR6G,GACF,EAAG,EAAE,EAEL,IAAMO,EAAiB,IACrB,IAAI/G,EAAQ6F,EACN,CAACmB,EAAKC,EAAI,CAAG,CAAC,EAAGlB,EAAoB,EAAE,CAE3B,QAAQ,CAAtB5G,EACFa,IAEqB,QAAQ,CAAtBb,GACPa,IAGEA,EAAQiH,IACVjH,CADe,CACP,GAGNA,EAAQgH,IACVhH,CADe,CACPiH,CAAAA,EAIN,EAAepE,MAAM,CAAG7C,GAAW,GAAG,CACxCA,EAAQqF,EAAcxC,MAAM,EAAG,EAG7B,EAAeA,MAAM,CAAG7C,GAAW,GAAG,CACxCA,GAAQ,EAEV8F,EAAU9F,EACZ,EAEA,MACE,UAACtE,MAAAA,CAAIC,UAAU,8BACZ0J,GAAiBA,EAAcxC,MAAM,CAAG,EACvC,iCACE,UAACqE,EAAAA,CAASA,CAAAA,CAACC,KAAK,aACd,WAAC5B,EAAAA,CAAGA,CAAAA,WACF,UAAC6B,EAAAA,CAAGA,CAAAA,CAACC,GAAI,GAAI1L,UAAU,QAEtB0J,GAAiBA,EAAcxC,MAAM,CAAG,EACvC,UAACuE,EAAAA,CAAGA,CAAAA,CAACC,GAAI,EAAG1L,UAAU,yCACpB,WAACD,MAAAA,CAAIC,UAAU,gCACb,UAAC2L,IAAAA,CAAE3L,UAAU,wBAAwBF,QAAS,IAAMsL,EAAe,iBACjE,UAACQ,IAAAA,CAAE5L,UAAU,yBAEf,UAAC2L,IAAAA,CAAE3L,UAAU,yBAAyBF,QAAS,IAAMsL,EAAe,iBAClE,UAACQ,IAAAA,CAAE5L,UAAU,+BAIjB,UAGR,UAACuL,EAAAA,CAASA,CAAAA,CAACC,KAAK,aACd,UAAC5B,EAAAA,CAAGA,CAAAA,UACF,UAAC6B,EAAAA,CAAGA,CAAAA,CAACC,GAAI,GAAI1L,UAAU,eACrB,UAACoB,EAAAA,CAAQA,CAAAA,CAACO,YAAY,EAAOD,UAAU,EAAOO,SAAU,KAAMJ,YAAaqI,WACxER,EAAcjB,GAAG,CAAC,CAACkB,EAAMtF,IAEtB,UAACjD,EAAAA,CAAQA,CAACzB,IAAI,WACZ,UAAC8J,EAAAA,CAAuBC,cAAeC,KADrBtF,eAWhC,UAACkH,EAAAA,CAASA,CAAAA,CAACC,OAAO,WAChB,UAAC5B,EAAAA,CAAGA,CAAAA,UACF,UAAC6B,EAAAA,CAAGA,CAAAA,CAACC,GAAI,GAAI1L,UAAU,eACrB,UAACD,MAAAA,CAAIC,UAAU,kCACf,UAAC6L,IAAAA,CAAE7L,UAAU,wDAAgDZ,EAAE,mCAQ/E,mBCrJA,4CACA,iCACA,WACA,OAAe,EAAQ,KAAsD,CAC7E,EACA,SAFsB,mGCGP,SAASyK,EAAiB1K,CAAU,UAEjD,GAAM,MAAEwK,CAAI,CAAE,CAAGxK,EASjB,MACE,WAACsM,EAAAA,CAAGA,CAAAA,CAACzL,UAAU,MAAM0L,GAAI,aACvB,UAACI,IAAIA,CAACC,KAAM,IAAc,OAAVpC,EAAKjB,IAAI,CAAC,gBAAe/H,GAAI,EAAxCmL,EAA8DnC,MAAAA,CAAlBA,EAAKjB,IAAI,CAAC,UAA0CiB,MAAAA,CAAlCA,CAAI,CAACqC,EAAYrC,EAwB5D,UACD,OAAVA,EAAKjB,IAAI,EAzBoE,CAAC,YAAmB,OAATiB,EAAKsC,GAAG,WAE1G,EAAMC,MAAM,EAAIvC,EAAKuC,MAAM,CAAC,EAAE,CAC7B,UAACC,MAAAA,CAAIC,IAAK,GAAwCzC,MAAAA,CAArC0C,8BAAsB,CAAC,gBAAiC,OAAnB1C,EAAKuC,MAAM,CAAC,EAAE,CAACD,GAAG,EAAIK,IAAI,eAC1EtM,UAAU,gBACV,UAAC4L,IAAAA,CAAE5L,UAAU,iCAGnB,WAACD,MAAAA,CAAIC,UAAU,yBACb,UAAC8L,IAAIA,CAACC,KAAM,IAAc,OAAVpC,EAAKjB,IAAI,CAAC,gBAAe/H,GAAI,EAAxCmL,EAA8DnC,MAAAA,CAAlBA,EAAKjB,IAAI,CAAC,UAA0CiB,MAAAA,CAAlCA,CAAI,CAW1DA,EAXuEA,EAW9D,UACD,OAAVA,EAAKjB,IAAI,EAZsE,CAAC,YAAmB,OAATiB,EAAKsC,GAAG,WAC1GtC,GAAQA,EAAK4C,KAAK,CAAG5C,EAAK4C,KAAK,CAAG,KAErC,UAACV,IAAAA,UACElC,GAAQA,EAAK6C,WAAW,CAAGC,CAtBX,IACvB,IAAM1M,EAAMmF,SAASwH,aAAa,CAAC,OACnC3M,EAAI4M,SAAS,CAAGC,EAChB,IAAMC,EAAS9M,EAAI+M,WAAW,EAAI/M,EAAIgN,SAAS,EAAI,GACnD,OAAQF,EAAO3F,MAAM,CAXF,EAWK8F,EAAiB,GAA2C,OAAxCH,EAAOI,SAAS,CAAC,EAAGD,KAAoB,OAAOH,CAC7F,GAiBqDlD,CAlB8B,CAkBzB6C,WAAW,EAAI,YAK3E", "sources": ["webpack://_N_E/./pages/vspace/AnnouncementsAccordian.tsx", "webpack://_N_E/./node_modules/@restart/hooks/esm/useUpdateEffect.js", "webpack://_N_E/./node_modules/react-bootstrap/esm/CarouselCaption.js", "webpack://_N_E/./node_modules/react-bootstrap/esm/CarouselItem.js", "webpack://_N_E/./node_modules/react-bootstrap/esm/Carousel.js", "webpack://_N_E/./pages/vspace/vspace_announcement/Announcement.tsx", "webpack://_N_E/?3feb", "webpack://_N_E/./pages/vspace/vspace_announcement/AnnouncementItem.tsx"], "sourcesContent": ["//Import Library\r\nimport { faPlus, faMinus } from \"@fortawesome/free-solid-svg-icons\";\r\nimport { FontAwesomeIcon } from \"@fortawesome/react-fontawesome\";\r\nimport { Card, Accordion } from \"react-bootstrap\";\r\nimport { useState } from \"react\";\r\n\r\n//Import services/components\r\nimport Announcement from \"./vspace_announcement/Announcement\";\r\nimport { useTranslation } from 'next-i18next';\r\n\r\n\r\nconst AnnouncementsAccordian = (props: any) => {\r\n    const { t } = useTranslation('common');\r\n    const [section, setSection] = useState(false);\r\n    return(\r\n        <>\r\n            <Accordion.Item eventKey=\"1\">\r\n              <Accordion.Header onClick={() => setSection(!section)}>\r\n                <div className=\"cardTitle\">{t(\"vspace.Announcements\")}</div>\r\n                <div className=\"cardArrow\">\r\n                  {section ? <FontAwesomeIcon icon={faMinus} color=\"#fff\" /> :\r\n                    <FontAwesomeIcon icon={faPlus} color=\"#fff\" />}\r\n                </div>\r\n              </Accordion.Header>\r\n              <Accordion.Body>\r\n                <Announcement />\r\n              </Accordion.Body>\r\n            </Accordion.Item>\r\n        </>\r\n    )\r\n};\r\n\r\nexport default AnnouncementsAccordian;", "import { useEffect, useRef } from 'react';\n\n/**\n * Runs an effect only when the dependencies have changed, skipping the\n * initial \"on mount\" run. Caution, if the dependency list never changes,\n * the effect is **never run**\n *\n * ```ts\n *  const ref = useRef<HTMLInput>(null);\n *\n *  // focuses an element only if the focus changes, and not on mount\n *  useUpdateEffect(() => {\n *    const element = ref.current?.children[focusedIdx] as HTMLElement\n *\n *    element?.focus()\n *\n *  }, [focusedIndex])\n * ```\n * @param effect An effect to run on mount\n *\n * @category effects\n */\nfunction useUpdateEffect(fn, deps) {\n  const isFirst = useRef(true);\n  useEffect(() => {\n    if (isFirst.current) {\n      isFirst.current = false;\n      return;\n    }\n    return fn();\n  }, deps);\n}\nexport default useUpdateEffect;", "\"use client\";\n\nimport * as React from 'react';\nimport classNames from 'classnames';\nimport { useBootstrapPrefix } from './ThemeProvider';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst CarouselCaption = /*#__PURE__*/React.forwardRef(({\n  className,\n  bsPrefix,\n  as: Component = 'div',\n  ...props\n}, ref) => {\n  bsPrefix = useBootstrapPrefix(bsPrefix, 'carousel-caption');\n  return /*#__PURE__*/_jsx(Component, {\n    ref: ref,\n    className: classNames(className, bsPrefix),\n    ...props\n  });\n});\nCarouselCaption.displayName = 'CarouselCaption';\nexport default CarouselCaption;", "\"use client\";\n\nimport classNames from 'classnames';\nimport * as React from 'react';\nimport { useBootstrapPrefix } from './ThemeProvider';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst CarouselItem = /*#__PURE__*/React.forwardRef(({\n  // Need to define the default \"as\" during prop destructuring to be compatible with styled-components github.com/react-bootstrap/react-bootstrap/issues/3595\n  as: Component = 'div',\n  bsPrefix,\n  className,\n  ...props\n}, ref) => {\n  const finalClassName = classNames(className, useBootstrapPrefix(bsPrefix, 'carousel-item'));\n  return /*#__PURE__*/_jsx(Component, {\n    ref: ref,\n    ...props,\n    className: finalClassName\n  });\n});\nCarouselItem.displayName = 'CarouselItem';\nexport default CarouselItem;", "\"use client\";\n\nimport useEventCallback from '@restart/hooks/useEventCallback';\nimport useUpdateEffect from '@restart/hooks/useUpdateEffect';\nimport useCommittedRef from '@restart/hooks/useCommittedRef';\nimport useTimeout from '@restart/hooks/useTimeout';\nimport Anchor from '@restart/ui/Anchor';\nimport classNames from 'classnames';\nimport * as React from 'react';\nimport { useCallback, useEffect, useImperativeHandle, useMemo, useRef, useState } from 'react';\nimport { useUncontrolled } from 'uncontrollable';\nimport CarouselCaption from './CarouselCaption';\nimport CarouselItem from './CarouselItem';\nimport { map, forEach } from './ElementChildren';\nimport { useBootstrapPrefix, useIsRTL } from './ThemeProvider';\nimport transitionEndListener from './transitionEndListener';\nimport triggerBrowserReflow from './triggerBrowserReflow';\nimport TransitionWrapper from './TransitionWrapper';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nimport { jsxs as _jsxs } from \"react/jsx-runtime\";\nimport { Fragment as _Fragment } from \"react/jsx-runtime\";\nconst SWIPE_THRESHOLD = 40;\nfunction isVisible(element) {\n  if (!element || !element.style || !element.parentNode || !element.parentNode.style) {\n    return false;\n  }\n  const elementStyle = getComputedStyle(element);\n  return elementStyle.display !== 'none' && elementStyle.visibility !== 'hidden' && getComputedStyle(element.parentNode).display !== 'none';\n}\nconst Carousel =\n/*#__PURE__*/\n// eslint-disable-next-line react/display-name\nReact.forwardRef(({\n  defaultActiveIndex = 0,\n  ...uncontrolledProps\n}, ref) => {\n  const {\n    // Need to define the default \"as\" during prop destructuring to be compatible with styled-components github.com/react-bootstrap/react-bootstrap/issues/3595\n    as: Component = 'div',\n    bsPrefix,\n    slide = true,\n    fade = false,\n    controls = true,\n    indicators = true,\n    indicatorLabels = [],\n    activeIndex,\n    onSelect,\n    onSlide,\n    onSlid,\n    interval = 5000,\n    keyboard = true,\n    onKeyDown,\n    pause = 'hover',\n    onMouseOver,\n    onMouseOut,\n    wrap = true,\n    touch = true,\n    onTouchStart,\n    onTouchMove,\n    onTouchEnd,\n    prevIcon = /*#__PURE__*/_jsx(\"span\", {\n      \"aria-hidden\": \"true\",\n      className: \"carousel-control-prev-icon\"\n    }),\n    prevLabel = 'Previous',\n    nextIcon = /*#__PURE__*/_jsx(\"span\", {\n      \"aria-hidden\": \"true\",\n      className: \"carousel-control-next-icon\"\n    }),\n    nextLabel = 'Next',\n    variant,\n    className,\n    children,\n    ...props\n  } = useUncontrolled({\n    defaultActiveIndex,\n    ...uncontrolledProps\n  }, {\n    activeIndex: 'onSelect'\n  });\n  const prefix = useBootstrapPrefix(bsPrefix, 'carousel');\n  const isRTL = useIsRTL();\n  const nextDirectionRef = useRef(null);\n  const [direction, setDirection] = useState('next');\n  const [paused, setPaused] = useState(false);\n  const [isSliding, setIsSliding] = useState(false);\n  const [renderedActiveIndex, setRenderedActiveIndex] = useState(activeIndex || 0);\n  useEffect(() => {\n    if (!isSliding && activeIndex !== renderedActiveIndex) {\n      if (nextDirectionRef.current) {\n        setDirection(nextDirectionRef.current);\n      } else {\n        setDirection((activeIndex || 0) > renderedActiveIndex ? 'next' : 'prev');\n      }\n      if (slide) {\n        setIsSliding(true);\n      }\n      setRenderedActiveIndex(activeIndex || 0);\n    }\n  }, [activeIndex, isSliding, renderedActiveIndex, slide]);\n  useEffect(() => {\n    if (nextDirectionRef.current) {\n      nextDirectionRef.current = null;\n    }\n  });\n  let numChildren = 0;\n  let activeChildInterval;\n\n  // Iterate to grab all of the children's interval values\n  // (and count them, too)\n  forEach(children, (child, index) => {\n    ++numChildren;\n    if (index === activeIndex) {\n      activeChildInterval = child.props.interval;\n    }\n  });\n  const activeChildIntervalRef = useCommittedRef(activeChildInterval);\n  const prev = useCallback(event => {\n    if (isSliding) {\n      return;\n    }\n    let nextActiveIndex = renderedActiveIndex - 1;\n    if (nextActiveIndex < 0) {\n      if (!wrap) {\n        return;\n      }\n      nextActiveIndex = numChildren - 1;\n    }\n    nextDirectionRef.current = 'prev';\n    onSelect == null || onSelect(nextActiveIndex, event);\n  }, [isSliding, renderedActiveIndex, onSelect, wrap, numChildren]);\n\n  // This is used in the setInterval, so it should not invalidate.\n  const next = useEventCallback(event => {\n    if (isSliding) {\n      return;\n    }\n    let nextActiveIndex = renderedActiveIndex + 1;\n    if (nextActiveIndex >= numChildren) {\n      if (!wrap) {\n        return;\n      }\n      nextActiveIndex = 0;\n    }\n    nextDirectionRef.current = 'next';\n    onSelect == null || onSelect(nextActiveIndex, event);\n  });\n  const elementRef = useRef();\n  useImperativeHandle(ref, () => ({\n    element: elementRef.current,\n    prev,\n    next\n  }));\n\n  // This is used in the setInterval, so it should not invalidate.\n  const nextWhenVisible = useEventCallback(() => {\n    if (!document.hidden && isVisible(elementRef.current)) {\n      if (isRTL) {\n        prev();\n      } else {\n        next();\n      }\n    }\n  });\n  const slideDirection = direction === 'next' ? 'start' : 'end';\n  useUpdateEffect(() => {\n    if (slide) {\n      // These callbacks will be handled by the <Transition> callbacks.\n      return;\n    }\n    onSlide == null || onSlide(renderedActiveIndex, slideDirection);\n    onSlid == null || onSlid(renderedActiveIndex, slideDirection);\n  }, [renderedActiveIndex]);\n  const orderClassName = `${prefix}-item-${direction}`;\n  const directionalClassName = `${prefix}-item-${slideDirection}`;\n  const handleEnter = useCallback(node => {\n    triggerBrowserReflow(node);\n    onSlide == null || onSlide(renderedActiveIndex, slideDirection);\n  }, [onSlide, renderedActiveIndex, slideDirection]);\n  const handleEntered = useCallback(() => {\n    setIsSliding(false);\n    onSlid == null || onSlid(renderedActiveIndex, slideDirection);\n  }, [onSlid, renderedActiveIndex, slideDirection]);\n  const handleKeyDown = useCallback(event => {\n    if (keyboard && !/input|textarea/i.test(event.target.tagName)) {\n      switch (event.key) {\n        case 'ArrowLeft':\n          event.preventDefault();\n          if (isRTL) {\n            next(event);\n          } else {\n            prev(event);\n          }\n          return;\n        case 'ArrowRight':\n          event.preventDefault();\n          if (isRTL) {\n            prev(event);\n          } else {\n            next(event);\n          }\n          return;\n        default:\n      }\n    }\n    onKeyDown == null || onKeyDown(event);\n  }, [keyboard, onKeyDown, prev, next, isRTL]);\n  const handleMouseOver = useCallback(event => {\n    if (pause === 'hover') {\n      setPaused(true);\n    }\n    onMouseOver == null || onMouseOver(event);\n  }, [pause, onMouseOver]);\n  const handleMouseOut = useCallback(event => {\n    setPaused(false);\n    onMouseOut == null || onMouseOut(event);\n  }, [onMouseOut]);\n  const touchStartXRef = useRef(0);\n  const touchDeltaXRef = useRef(0);\n  const touchUnpauseTimeout = useTimeout();\n  const handleTouchStart = useCallback(event => {\n    touchStartXRef.current = event.touches[0].clientX;\n    touchDeltaXRef.current = 0;\n    if (pause === 'hover') {\n      setPaused(true);\n    }\n    onTouchStart == null || onTouchStart(event);\n  }, [pause, onTouchStart]);\n  const handleTouchMove = useCallback(event => {\n    if (event.touches && event.touches.length > 1) {\n      touchDeltaXRef.current = 0;\n    } else {\n      touchDeltaXRef.current = event.touches[0].clientX - touchStartXRef.current;\n    }\n    onTouchMove == null || onTouchMove(event);\n  }, [onTouchMove]);\n  const handleTouchEnd = useCallback(event => {\n    if (touch) {\n      const touchDeltaX = touchDeltaXRef.current;\n      if (Math.abs(touchDeltaX) > SWIPE_THRESHOLD) {\n        if (touchDeltaX > 0) {\n          prev(event);\n        } else {\n          next(event);\n        }\n      }\n    }\n    if (pause === 'hover') {\n      touchUnpauseTimeout.set(() => {\n        setPaused(false);\n      }, interval || undefined);\n    }\n    onTouchEnd == null || onTouchEnd(event);\n  }, [touch, pause, prev, next, touchUnpauseTimeout, interval, onTouchEnd]);\n  const shouldPlay = interval != null && !paused && !isSliding;\n  const intervalHandleRef = useRef();\n  useEffect(() => {\n    var _ref, _activeChildIntervalR;\n    if (!shouldPlay) {\n      return undefined;\n    }\n    const nextFunc = isRTL ? prev : next;\n    intervalHandleRef.current = window.setInterval(document.visibilityState ? nextWhenVisible : nextFunc, (_ref = (_activeChildIntervalR = activeChildIntervalRef.current) != null ? _activeChildIntervalR : interval) != null ? _ref : undefined);\n    return () => {\n      if (intervalHandleRef.current !== null) {\n        clearInterval(intervalHandleRef.current);\n      }\n    };\n  }, [shouldPlay, prev, next, activeChildIntervalRef, interval, nextWhenVisible, isRTL]);\n  const indicatorOnClicks = useMemo(() => indicators && Array.from({\n    length: numChildren\n  }, (_, index) => event => {\n    onSelect == null || onSelect(index, event);\n  }), [indicators, numChildren, onSelect]);\n  return /*#__PURE__*/_jsxs(Component, {\n    ref: elementRef,\n    ...props,\n    onKeyDown: handleKeyDown,\n    onMouseOver: handleMouseOver,\n    onMouseOut: handleMouseOut,\n    onTouchStart: handleTouchStart,\n    onTouchMove: handleTouchMove,\n    onTouchEnd: handleTouchEnd,\n    className: classNames(className, prefix, slide && 'slide', fade && `${prefix}-fade`, variant && `${prefix}-${variant}`),\n    children: [indicators && /*#__PURE__*/_jsx(\"div\", {\n      className: `${prefix}-indicators`,\n      children: map(children, (_, index) => /*#__PURE__*/_jsx(\"button\", {\n        type: \"button\",\n        \"data-bs-target\": \"\" // Bootstrap requires this in their css.\n        ,\n        \"aria-label\": indicatorLabels != null && indicatorLabels.length ? indicatorLabels[index] : `Slide ${index + 1}`,\n        className: index === renderedActiveIndex ? 'active' : undefined,\n        onClick: indicatorOnClicks ? indicatorOnClicks[index] : undefined,\n        \"aria-current\": index === renderedActiveIndex\n      }, index))\n    }), /*#__PURE__*/_jsx(\"div\", {\n      className: `${prefix}-inner`,\n      children: map(children, (child, index) => {\n        const isActive = index === renderedActiveIndex;\n        return slide ? /*#__PURE__*/_jsx(TransitionWrapper, {\n          in: isActive,\n          onEnter: isActive ? handleEnter : undefined,\n          onEntered: isActive ? handleEntered : undefined,\n          addEndListener: transitionEndListener,\n          children: (status, innerProps) => /*#__PURE__*/React.cloneElement(child, {\n            ...innerProps,\n            className: classNames(child.props.className, isActive && status !== 'entered' && orderClassName, (status === 'entered' || status === 'exiting') && 'active', (status === 'entering' || status === 'exiting') && directionalClassName)\n          })\n        }) : ( /*#__PURE__*/React.cloneElement(child, {\n          className: classNames(child.props.className, isActive && 'active')\n        }));\n      })\n    }), controls && /*#__PURE__*/_jsxs(_Fragment, {\n      children: [(wrap || activeIndex !== 0) && /*#__PURE__*/_jsxs(Anchor, {\n        className: `${prefix}-control-prev`,\n        onClick: prev,\n        children: [prevIcon, prevLabel && /*#__PURE__*/_jsx(\"span\", {\n          className: \"visually-hidden\",\n          children: prevLabel\n        })]\n      }), (wrap || activeIndex !== numChildren - 1) && /*#__PURE__*/_jsxs(Anchor, {\n        className: `${prefix}-control-next`,\n        onClick: next,\n        children: [nextIcon, nextLabel && /*#__PURE__*/_jsx(\"span\", {\n          className: \"visually-hidden\",\n          children: nextLabel\n        })]\n      })]\n    })]\n  });\n});\nCarousel.displayName = 'Carousel';\nexport default Object.assign(Carousel, {\n  Caption: CarouselCaption,\n  Item: CarouselItem\n});", "//Import Library\r\nimport React, { useEffect, useState } from 'react';\r\nimport { Container, Row } from \"react-bootstrap\";\r\nimport Col from \"react-bootstrap/Col\";\r\nimport { useRouter } from \"next/router\";\r\n\r\n//Import services/components\r\nimport _ from 'lodash';\r\nimport AnnouncementItem from \"./AnnouncementItem\";\r\nimport Carousel from 'react-bootstrap/Carousel'\r\nimport apiService from \"../../../services/apiService\";\r\nimport { useTranslation } from 'next-i18next';\r\n\r\n\r\n//TODO: Need to use RKISingleItemCarousel component to reuse our exisiting component\r\ninterface ListOfAnnouncementItemProps {\r\n  announcements: any[][];\r\n}\r\n\r\nfunction ListOfAnnouncementItem(props: ListOfAnnouncementItemProps) {\r\n  const { announcements } = props;\r\n  return (\r\n    <div>\r\n      {announcements.map((item, index) => {\r\n        return (\r\n          <Row className=\"announcementItem\" key={index}>\r\n            <AnnouncementItem item={item} />\r\n          </Row>\r\n        )\r\n      })}\r\n    </div>\r\n  )\r\n}\r\n\r\nfunction Announcement() {\r\n  const { t } = useTranslation('common');\r\n  const router = useRouter();\r\n  const routes: any = router.query.routes || [];\r\n  const [announcements, setAnnouncements] = useState<any[][]>([]);\r\n\r\n  const [cindex, setCindex] = useState(0);\r\n\r\n  const [carouselItemCount] = useState(3);\r\n\r\n  const setEmptyNotice = () => {\r\n    setAnnouncements([]);\r\n  };\r\n\r\n  const updatesParams = {\r\n    query: { show_as_announcement: true, parent_vspace: routes[1] },\r\n    sort: { created_at: \"desc\" },\r\n    limit: \"~\",\r\n    select: \"-created_at -update_type -contact_details -document -end_date -link -media -reply -start_date -updated_at\"\r\n  };\r\n\r\n  const fetchAnnouncements = async (params = updatesParams) => {\r\n    const response = await apiService.get('/updates', params);\r\n    if (response && response.data && response.data.length > 0) {\r\n      const partition = _.chunk(response.data, 3);\r\n      setAnnouncements(partition)\r\n    } else {\r\n      setEmptyNotice()\r\n    }\r\n  };\r\n\r\n  useEffect(() => {\r\n    fetchAnnouncements();\r\n  }, [])\r\n\r\n  const toggleCarousel = (direction: 'next' | 'prev') => {\r\n    let index = cindex;\r\n    const [min, max] = [0, carouselItemCount - 1];\r\n\r\n    if (direction === 'next') {\r\n      index++\r\n    }\r\n    else if (direction === 'prev') {\r\n      index--\r\n    }\r\n\r\n    if (index > max) {\r\n      index = 0\r\n    }\r\n\r\n    if (index < min) {\r\n      index = max\r\n    }\r\n\r\n\r\n    if ((announcements.length - index) === 1) {\r\n      index = announcements.length - 1;\r\n    }\r\n\r\n    if ((announcements.length - index) === 0) {\r\n      index = 1;\r\n    }\r\n    setCindex(index);\r\n  };\r\n\r\n  return (\r\n    <div className=\"announcements mt-0\">\r\n      {announcements && announcements.length > 0 ? (\r\n        <>\r\n          <Container fluid>\r\n            <Row>\r\n              <Col xs={10} className=\"p-0\">\r\n              </Col>\r\n              {announcements && announcements.length > 1 ?\r\n                <Col xs={2} className=\"text-end carousel-control p-0\">\r\n                  <div className=\"carousel-navigation\">\r\n                    <a className=\"left carousel-control\" onClick={() => toggleCarousel('prev')}>\r\n                      <i className=\"fa fa-chevron-left\" />\r\n                    </a>\r\n                    <a className=\"right carousel-control\" onClick={() => toggleCarousel('next')}>\r\n                      <i className=\"fa fa-chevron-right\" />\r\n                    </a>\r\n                  </div>\r\n                </Col>\r\n                : null}\r\n            </Row>\r\n          </Container>\r\n          <Container fluid>\r\n            <Row>\r\n              <Col xs={12} className=\"p-0\">\r\n                <Carousel indicators={false} controls={false} interval={null} activeIndex={cindex}>\r\n                  {announcements.map((item, index) => {\r\n                    return (\r\n                      <Carousel.Item key={index}>\r\n                        <ListOfAnnouncementItem announcements={item} />\r\n                      </Carousel.Item>\r\n                    )\r\n                  })}\r\n                </Carousel>\r\n              </Col>\r\n            </Row>\r\n          </Container>\r\n        </>\r\n      ) : (\r\n          <Container fluid={true}>\r\n            <Row>\r\n              <Col xs={12} className=\"p-0\">\r\n                <div className=\"border border-info m-3\">\r\n                <p className=\"d-flex d-flex justify-content-center p-2 m-0\">{t(\"NoAnnouncementFound!\")}</p>\r\n                </div>\r\n              </Col>\r\n            </Row>\r\n          </Container>\r\n        )}\r\n    </div>\r\n  )\r\n}\r\n\r\nexport default Announcement;", "\n    (window.__NEXT_P = window.__NEXT_P || []).push([\n      \"/vspace/AnnouncementsAccordian\",\n      function () {\n        return require(\"private-next-pages/vspace/AnnouncementsAccordian.tsx\");\n      }\n    ]);\n    if(module.hot) {\n      module.hot.dispose(function () {\n        window.__NEXT_P.push([\"/vspace/AnnouncementsAccordian\"])\n      });\n    }\n  ", "//Import Library\r\nimport { Col } from \"react-bootstrap\";\r\nimport <PERSON> from \"next/link\";\r\n\r\nconst truncateLength = 260;\r\n\r\n//TODO: Remove the maths random number for image after updates completed with image upload\r\nexport default function AnnouncementItem(props: any) {\r\n\r\n  const { item } = props;\r\n\r\n  const getTrimmedString = (html: any) => {\r\n    const div = document.createElement(\"div\");\r\n    div.innerHTML = html;\r\n    const string = div.textContent || div.innerText || \"\";\r\n    return (string.length > truncateLength ? `${string.substring(0, truncateLength - 3)}...` : string);\r\n  }\r\n\r\n  return (\r\n    <Col className=\"p-0\" xs={12}>\r\n      <Link href={`/${item.type}/[...routes]`} as={`/${item.type}/show/${item[Parent_func(item)]}/update/${item._id}`}>\r\n\r\n        {(item.images && item.images[0]) ?\r\n          <img src={`${process.env.API_SERVER}/image/show/${item.images[0]._id}`} alt=\"announcement\"\r\n            className=\"announceImg\" />\r\n          : <i className=\"fa fa-bullhorn announceImg\" />}\r\n\r\n      </Link>\r\n      <div className=\"announceDesc\">\r\n        <Link href={`/${item.type}/[...routes]`} as={`/${item.type}/show/${item[newFunction(item)]}/update/${item._id}`}>\r\n          {item && item.title ? item.title : ''}\r\n        </Link>\r\n        <p>\r\n          {item && item.description ? getTrimmedString(item.description) : null}\r\n        </p>\r\n      </div>\r\n    </Col>\r\n  );\r\n}\r\n\r\nfunction newFunction(item: any) {\r\n  return `parent_${item.type}`;\r\n}\r\n\r\nfunction Parent_func(item: any) {\r\n  return `parent_${item.type}`;\r\n}\r\n"], "names": ["props", "t", "useTranslation", "Announcements<PERSON><PERSON><PERSON>an", "section", "setSection", "useState", "Accordion", "<PERSON><PERSON>", "eventKey", "Header", "onClick", "div", "className", "FontAwesomeIcon", "icon", "faMinus", "color", "faPlus", "Body", "Announcement", "CarouselCaption", "React", "bsPrefix", "as", "Component", "useBootstrapPrefix", "_jsx", "ref", "classNames", "CarouselItem", "finalClassName", "displayName", "Carousel", "activeChildInterval", "defaultActiveIndex", "uncontrolledProps", "slide", "fade", "controls", "indicators", "indicatorLabels", "activeIndex", "onSelect", "onSlide", "onSlid", "interval", "keyboard", "onKeyDown", "pause", "onMouseOver", "onMouseOut", "wrap", "touch", "onTouchStart", "onTouchMove", "onTouchEnd", "prevIcon", "prevLabel", "nextIcon", "next<PERSON><PERSON><PERSON>", "variant", "children", "useUncontrolled", "prefix", "isRTL", "useIsRTL", "nextDirectionRef", "useRef", "direction", "setDirection", "paused", "setPaused", "isSliding", "setIsSliding", "renderedActiveIndex", "setRenderedActiveIndex", "useEffect", "current", "numC<PERSON><PERSON>n", "for<PERSON>ach", "child", "index", "activeChildIntervalRef", "useCommittedRef", "prev", "useCallback", "event", "nextActiveIndex", "next", "useEventCallback", "elementRef", "useImperativeHandle", "element", "nextWhenVisible", "document", "hidden", "style", "parentNode", "elementStyle", "getComputedStyle", "display", "visibility", "slideDirection", "useUpdateEffect", "orderClassName", "directionalClassName", "handleEnter", "node", "triggerBrowserReflow", "handleEntered", "handleKeyDown", "test", "target", "tagName", "key", "preventDefault", "handleMouseOver", "handleMouseOut", "touchStartXRef", "touchDeltaXRef", "touchUnpauseTimeout", "useTimeout", "handleTouchStart", "touches", "clientX", "handleTouchMove", "length", "handleTouchEnd", "touchDeltaX", "Math", "abs", "SWIPE_THRESHOLD", "set", "undefined", "shouldPlay", "intervalHandleRef", "_ref", "_activeChildIntervalR", "nextFunc", "window", "setInterval", "visibilityState", "clearInterval", "indicatorOnClicks", "useMemo", "Array", "from", "_", "_jsxs", "map", "type", "isActive", "TransitionWrapper", "in", "onEnter", "onEntered", "addEndListener", "transitionEndListener", "status", "innerProps", "_Fragment", "<PERSON><PERSON>", "Object", "assign", "Caption", "ListOfAnnouncementItem", "announcements", "item", "Row", "AnnouncementItem", "routes", "useRouter", "query", "setAnnouncements", "cindex", "setCindex", "carouselItemCount", "setEmptyNotice", "updatesParams", "show_as_announcement", "parent_vspace", "sort", "created_at", "limit", "select", "fetchAnnouncements", "params", "response", "apiService", "get", "data", "partition", "toggleCarousel", "min", "max", "Container", "fluid", "Col", "xs", "a", "i", "p", "Link", "href", "Parent_func", "_id", "images", "img", "src", "process", "alt", "title", "description", "getTrimmedString", "createElement", "innerHTML", "html", "string", "textContent", "innerText", "truncate<PERSON><PERSON>th", "substring"], "sourceRoot": "", "ignoreList": [1, 2, 3, 4]}