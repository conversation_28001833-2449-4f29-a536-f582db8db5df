{"version": 3, "file": "static/chunks/pages/adminsettings/approval/focal_point_appoval-2ba225a42f370216.js", "mappings": "0qBAGA,IAAMA,EAAS,aACFC,EAAmBC,CAAAA,EAAAA,EAAAA,CAAAA,CAAoBA,CAAC,CACnDC,sBAAuB,KACjBC,EAAMC,WAAW,IAAID,EAAMC,WAAW,CAACC,YAAY,IAAIF,EAAMC,WAAW,CAACC,YAAY,CAACN,EAAO,CAKnGO,CALqG,kBAKjF,kBACtB,GAAG,EAE0BL,CAAAA,EAAAA,EAAAA,CAAAA,CAAoBA,CAAC,CAChDC,sBAAuB,KACjBC,EAAMC,WAAW,IAAID,EAAMC,WAAW,CAACG,OAAO,IAAIJ,EAAMC,WAAW,CAACG,OAAO,CAACR,EAAO,CAKzFO,CAL2F,kBAKvE,eACtB,GAAG,EAEmCL,CAAAA,EAAAA,EAAAA,CAAAA,CAAoBA,CAAC,CACzDC,sBAAuB,KACjBC,EAAMC,WAAW,IAAID,EAAMC,WAAW,CAACI,iBAAiB,IAAIL,EAAMC,WAAW,CAACI,iBAAiB,CAACT,EAAO,CAK7GO,CAL+G,kBAK3F,wBACtB,GAAG,EAE8BL,CAAAA,EAAAA,EAAAA,CAAAA,CAAoBA,CAAC,CACpDC,sBAAuB,KACjBC,EAAMC,WAAW,IAAID,EAAMC,WAAW,CAACK,YAAY,IAAIN,EAAMC,WAAW,CAACK,YAAY,CAACV,EAAO,CAKnGO,CALqG,kBAKjF,mBACtB,GAAG,EAE4BL,CAAAA,EAAAA,EAAAA,CAAAA,CAAoBA,CAAC,CAClDC,sBAAuB,GACjBC,IAAMC,WAAW,IAAID,EAAMC,WAAW,CAACM,SAAS,IAAIP,EAAMC,WAAW,CAACM,SAAS,CAACX,EAAO,CAK7FO,CAL+F,kBAK3E,iBACtB,GAAG,EAEqCL,CAAAA,EAAAA,EAAAA,CAAAA,CAAoBA,CAAC,CAC3DC,sBAAuB,KACjBC,EAAMC,WAAW,IAAID,EAAMC,WAAW,CAACO,uBAAuB,IAAIR,EAAMC,WAAW,CAACO,uBAAuB,CAACZ,EAAO,CAKzHO,CAL2H,kBAKvG,0BACtB,GAAG,EAEiCL,CAAAA,EAAAA,EAAAA,CAAAA,CAAoBA,CAAC,CACvDC,sBAAwBC,KAClBA,EAAMC,WAAW,IAAID,EAAMC,WAAW,CAACO,uBAAuB,IAAIR,EAAMC,WAAW,CAACO,uBAAuB,CAACZ,EAAO,CAKzHO,CAL2H,kBAKvG,sBACtB,GAAG,EAC0BL,CAAAA,EAAAA,EAAAA,CAAAA,CAAoBA,CAAC,CAChDC,sBAAuB,KACjBC,EAAMC,WAAW,IAAID,EAAMC,WAAW,CAACQ,MAAM,IAAIT,EAAMC,WAAW,CAACQ,MAAM,CAACb,EAAO,CAKvFO,CALyF,kBAKrE,eACtB,GAAG,EAE8BL,CAAAA,EAAAA,EAAAA,CAAAA,CAAoBA,CAAC,CACpDC,sBAAuB,GACjBC,IAAMC,WAAW,IAAID,EAAMC,WAAW,CAACS,WAAW,IAAIV,EAAMC,WAAW,CAACS,WAAW,CAACd,EAAO,CAKjGO,CALmG,kBAK/E,mBACtB,GAAG,EAEuCL,CAAAA,EAAAA,EAAAA,CAAAA,CAAoBA,CAAC,CAC7DC,sBAAuB,KACjBC,EAAMC,WAAW,IAAID,EAAMC,WAAW,CAACU,WAAW,IAAIX,EAAMC,WAAW,CAACU,WAAW,CAACf,EAAO,CAKjGO,CALmG,kBAK/E,4BACtB,GAAG,EAEuCL,CAAAA,EAAAA,EAAAA,CAAAA,CAAoBA,CAAC,CAC7DC,sBAAuB,KACjBC,EAAMC,WAAW,IAAID,EAAMC,WAAW,CAACW,mBAAmB,IAAIZ,EAAMC,WAAW,CAACW,mBAAmB,CAAChB,EAAO,CAKjHO,CALmH,kBAK/F,4BACtB,GAAG,EAEoCL,CAAAA,EAAAA,EAAAA,CAAAA,CAAoBA,CAAC,CAC1DC,sBAAuB,KACjBC,EAAMC,WAAW,IAAID,EAAMC,WAAW,CAACY,gBAAgB,IAAIb,EAAMC,WAAW,CAACY,gBAAgB,CAACjB,EAAO,CAK3GO,CAL6G,kBAKzF,yBACtB,GAAG,EAEkCL,CAAAA,EAAAA,EAAAA,CAAAA,CAAoBA,CAAC,CACxDC,sBAAuB,KACjBC,EAAMC,WAAW,IAAID,EAAMC,WAAW,CAACa,gBAAgB,IAAId,EAAMC,WAAW,CAACa,gBAAgB,CAAClB,EAAO,CAK3GO,CAL6G,kBAKzF,uBACtB,GAAG,EAEgCL,CAAAA,EAAAA,EAAAA,CAAAA,CAAoBA,CAAC,CACtDC,sBAAuB,KACjBC,EAAMC,WAAW,IAAID,EAAMC,WAAW,CAACc,cAAc,IAAIf,EAAMC,WAAW,CAACc,cAAc,CAACnB,EAAO,CAKvGO,CALyG,kBAKrF,qBACtB,GAAG,EAE0BL,CAAAA,EAAAA,EAAAA,CAAAA,CAAoBA,CAAC,CAChDC,sBAAuB,KACjBC,EAAMC,WAAW,IAAID,EAAMC,WAAW,CAACe,MAAM,IAAIhB,EAAMC,WAAW,CAACe,MAAM,CAACpB,EAAO,CAKvFO,CALyF,kBAKrE,eACtB,GAEac,EAAmBnB,CAAAA,EAAAA,EAAAA,CAAAA,CAAoBA,CAAC,CACnDC,sBAAuB,KACjBC,EAAMC,WAAW,IAAID,EAAMC,WAAW,CAACiB,UAAU,IAAIlB,EAAMC,WAAW,CAACiB,UAAU,CAACtB,EAAO,CAK/FO,CALiG,kBAK7E,kBACtB,GAEagB,EAAkBrB,CAAAA,EAAAA,EAAAA,CAAAA,CAAoBA,CAAC,CAClDC,sBAAuB,GACjBC,IAAMC,WAAW,IAAID,EAAMC,WAAW,CAACmB,QAAQ,IAAIpB,EAAMC,WAAW,CAACmB,QAAQ,CAACxB,EAAO,CAK3FO,CAL6F,kBAKzE,iBACtB,GAAG,EAE8BL,CAAAA,EAAAA,EAAAA,CAAAA,CAAoBA,CAAC,CACpDC,sBAAuB,KACjBC,EAAMC,WAAW,IAAID,EAAMC,WAAW,CAACoB,WAAW,IAAIrB,EAAMC,WAAW,CAACoB,WAAW,CAACzB,EAAO,CAKjGO,CALmG,kBAK/E,mBACtB,GAEamB,EAAcxB,CAAAA,EAAAA,EAAAA,CAAAA,CAAoBA,CAAC,CAC9CC,sBAAuB,GACjBC,IAAMC,WAAW,IAAID,EAAMC,WAAW,CAACsB,KAAK,IAAIvB,EAAMC,WAAW,CAACsB,KAAK,CAAC3B,EAAO,CAKrFO,CALuF,kBAKnE,aACtB,GAAG,EAE8BL,CAAAA,EAAAA,EAAAA,CAAAA,CAAoBA,CAAC,CACpDC,sBAAuB,KACjBC,EAAMC,WAAW,IAAID,EAAMC,WAAW,CAACuB,WAAW,IAAIxB,EAAMC,WAAW,CAACuB,WAAW,CAAC5B,EAAO,CAKjGO,CALmG,kBAK/E,mBACtB,GAAG,EAE8BL,CAAAA,EAAAA,EAAAA,CAAAA,CAAoBA,CAAC,CACpDC,sBAAuB,KACjBC,EAAMC,WAAW,IAAID,EAAMC,WAAW,CAACwB,YAAY,IAAIzB,EAAMC,WAAW,CAACwB,YAAY,CAAC7B,EAAO,CAKnGO,CALqG,kBAKjF,mBACtB,GAAG,EAE0BL,CAAAA,EAAAA,EAAAA,CAAAA,CAAoBA,CAAC,CAChDC,sBAAuB,GACrB,EAAIC,EAAMC,WAAW,IAAID,EAAMC,WAAW,CAACyB,SAAS,IAAI1B,EAAMC,WAAW,CAACyB,SAAS,CAAC9B,EAAO,IAAII,EAAMC,WAAW,CAAC0B,OAAO,IAAI3B,EAAMC,WAAW,CAAC0B,OAAO,CAAC/B,EAAO,IAAGI,EAAMC,WAAW,CAAC2B,KAAK,IAAI5B,EAAMC,WAAW,CAAC2B,KAAK,CAAChC,EAAO,IAAGI,EAAMC,WAAW,CAAC4B,MAAM,IAAI7B,EAAMC,WAAW,CAAC4B,MAAM,CAACjC,EAAO,IAAGI,EAAMC,WAAW,CAACU,WAAW,IAAIX,EAAMC,WAAW,CAACU,WAAW,CAACf,EAAO,IAAGI,EAAMC,WAAW,CAAC6B,MAAM,IAAI9B,EAAMC,WAAW,CAAC6B,MAAM,CAAClC,EAAO,EAAE,CAG5Z,EAETO,mBAAoB,eACtB,GAAG,EAEYN,gBAAgBA,EAAC,2JCtLhC,MA7BuB,QAafG,EAAAA,EAZN,GAAM,GAAE+B,CAAC,CAAE,CAAGC,CAAAA,EAAAA,EAAAA,EAAAA,CAAcA,CAAC,UACvBC,EA2BqBC,EAAC,EAzBxB,WAACC,EAAAA,CAASA,CAAAA,CAACC,KAAK,IAACC,UAAU,gBACzB,UAACC,EAAAA,CAAWA,CAAAA,CAACC,MAAOR,EAAE,sCACtB,UAACS,EAAAA,OAAUA,CAAAA,CAAAA,MAKXC,EAA4BC,CAAAA,EAAAA,EAAAA,wBAAAA,CAAwBA,CAAC,IAAM,UAACT,EAAAA,CAAAA,IAC5DjC,EAAY2C,CAAAA,EAAAA,EAAAA,EAAAA,CAAWA,CAAC,GAAgB3C,SAC9C,IAAI,GAAEA,GAAAA,OAAAA,EAAAA,EAAOC,IAAPD,OAAkB,GAAlBA,OAAAA,EAAAA,EAAAA,uBAA2C,EAA3CA,KAAAA,EAAAA,CAA6C,CAAC,GAA9CA,UAA2D,EAI/D,CAJkE,EAIlE,OAACyC,EAAAA,CAAAA,GAHM,UAACG,EAAAA,OAAeA,CAAAA,CAAAA,EAK3B,6GCMA,SAASC,EAASC,CAAoB,EACpC,GAAM,CAAEf,GAAC,CAAE,CAAGC,CAAAA,EAAAA,EAAAA,EAAAA,CAAcA,CAAC,UACvBe,EAA6B,CACjCC,gBAAiBjB,EAAE,cACnB,EACI,SACJkB,CAAO,CACPC,MAAI,WACJC,CAAS,uBACTC,CAAqB,WACrBC,CAAS,oBACTC,CAAkB,qBAClBC,CAAmB,kBACnBC,CAAgB,aAChBC,CAAW,CACXC,oBAAkB,gBAClBC,CAAc,SACdC,CAAO,WACPC,CAAS,sBACTC,CAAoB,CACpBC,mBAAiB,CACjBC,YAAU,QACVC,CAAM,kBACNC,CAAgB,cAChBC,CAAY,CAEZ,CADA,EACGC,EACJ,CAAGtB,EAGEuB,EAAiB,4BACrBtB,EACAuB,gBAAiBvC,EAAE,IAP0C,MAQ7DwC,UAAU,UACVtB,EACAC,KAAMA,GAAQ,EAAE,CAChBsB,OAAO,EACPC,2BAA4BrB,EAC5BsB,UAAWrB,EACXsB,gBAAiBf,qBACjBN,EACAsB,YAAY,EACZC,iBAAkBhB,EAClBiB,kBAAmBpB,GAA0C,GAC7DqB,eADwCrB,cACVD,GAA4B,CAAC,GAAI,GAAI,GAAI,CAA3BA,EAA+B,GAAG,CAC9EuB,oBAAqB7B,EACrB8B,oBAAqB1B,EACrB2B,aAAc1B,iBACdG,uBACAG,oBACAC,EACAoB,kBAAmB,UAACC,EAAAA,CAAYA,CAAAA,CAAAA,GAChCC,SAAU,UAACC,IAAAA,CAAEjD,UAAU,6CACvB2B,SACAC,EACAE,eACAD,mBACA7B,UAAW,WACb,EACA,MACE,UAACkD,EAAAA,EAASA,CAAAA,CAAE,GAAGlB,CAAc,EAEjC,CAEAxB,EAAS2C,YAAY,CAAG,CACtBd,WAAW,EACXE,YAAY,EACZzB,UAAW,KACXU,WAAW,EACXC,qBAAsB,KACtBC,mBAAmB,EACnBC,YAAY,EACZE,kBAAkB,CACpB,EAEA,MAAerB,QAAQA,EAAC,mEChHT,SAASD,EAAgB6C,CAAW,EAC/C,MACE,UAACC,MAAAA,CAAIrD,UAAU,sDACb,UAACqD,MAAAA,CAAIrD,UAAU,mBAAU,yCAG/B,mBCLF,4CACA,8CACA,WACA,OAAe,EAAQ,KAAmE,CAC1F,EACA,SAFsB,uDCAP,SAASC,EAAYQ,CAAuB,EACzD,MACE,UAAC6C,KAAAA,CAAGtD,UAAU,wBAAgBS,EAAMP,KAAK,EAE7C,0JC4NA,MA9MA,SAAoBkD,CAAW,EAC3B,GAAM,GAAE1D,CAAC,CAAE,CAAGC,CAAAA,EAAAA,EAAAA,EAAAA,CAAcA,CAAC,QA6MRQ,EA5Mf,CAACoD,EAAWC,EAAe,CAAGC,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAQ,EAAE,EAChD,EAAGC,EAAW,CAAGD,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,EAAU,GACnC,CAAC3C,EAAW6C,EAAa,CAAGF,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAS,GAC7C,CAACG,EAASC,EAAW,CAAGJ,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAS,IACzC,CAACK,EAAaC,EAAS,CAAGN,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,EAAU,GAC5C,CAACO,EAAWC,EAAa,CAAGR,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAS,IAC7C,CAACS,EAAmBC,EAAqB,CAAGV,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAM,CAAC,GAG3DW,EAAc,CAChBC,KAAM,CAAEC,WAAY,MAAO,EAC3BC,MAAO,IACPC,KAAM,EACNC,MAAO,CAAE,4BAA6B,iBAAkB,CAC5D,EAEM7D,EAAU,CACZ,CACI8D,KAAMhF,EAAE,kDACRiF,SAAU,WACVC,KAAM,GAAYC,EAAEC,QAAQ,EAEhC,CACIJ,KAAMhF,EAAE,oBACRiF,SAAU,kBACVC,KAAM,GAAYC,EAAEE,eACxB,EACA,CACIL,KAAMhF,EAAE,+CACRiF,SAAU,QACVC,KAAM,GAAYC,EAAEG,KACxB,EACA,CACIN,KAAMhF,EAAE,gDACRiF,SAAU,GACVC,KAAM,GACF,WAACvB,MAAAA,WACIwB,eAAEI,iBAAiB,EAA2C,oBAAxBJ,EAAEI,iBAAiB,CACtD,iCACI,UAACC,EAAAA,CAAMA,CAAAA,CAACC,QAAQ,UAAUC,KAAK,KAAKC,QAAS,IAAMC,EAAWT,EAAG,oBAC5DnF,EAAE,oBACE,UAIb,yBAEqB,aAAxBmF,EAAEI,iBAAiB,EAA2C,oBAAxBJ,EAAEI,iBAAiB,CACtD,UAACC,EAAAA,CAAMA,CAAAA,CAACC,QAAQ,YAAYC,KAAK,KAAKC,QAAS,IAAMC,EAAWT,EAAG,mBAC9DnF,EAAE,kBAGP,2BAIhB,EACH,CAEK6F,EAAe,IACjB,IAAIC,EAAmB,EAAE,CAezB,OAdA3E,EAAK4E,OAAO,CAAC,UACTC,GAAAA,EAAMC,YAAND,MAAwB,CAACD,OAAO,CAAC,IACzBG,GAAqBA,mBAAgD,GAA9BC,MAAM,EAC7CL,EAAUM,IAAI,CAAC,CACX,GAAGJ,CAAI,CACP,GAAG,CACCK,cAAeH,EAAkBG,aAAa,CAC9ChB,gBAAiBa,EAAkBb,eAAe,CAClDE,kBAAmBW,EAAkBC,MAAM,CAC9C,EAGb,EACJ,GACOL,CACX,EAEMQ,EAAe,UACjBtC,GAAW,GACX,IAAMuC,EAAW,MAAMC,EAAAA,CAAUA,CAACC,GAAG,CAAC,SAAU/B,GAChD,GAAI6B,GAAYA,EAASpF,IAAI,CAAE,CAC3B,IAAI2E,EAAYD,EAAaU,EAASpF,IAAI,EAC1C2C,EAAe4C,EAAcZ,EAAW5B,EAAS,IACjDD,EAAa6B,EAAUa,MAAM,EAC7B3C,GAAW,EACf,CACJ,EACMvC,EAAmB,MAAOqD,IAC5Bd,GAAW,GACX,IAAMuC,EAAW,MAAMC,EAAAA,CAAUA,CAACC,GAAG,CAAC,SAAU/B,GAC5C6B,GAAYA,EAASpF,IAAI,EAAIoF,EAASpF,IAAI,CAACwF,MAAM,CAAG,GAAG,CAEvD7C,EAAe4C,EADCb,EAAaU,EAASpF,IAAI,EACF+C,EAAX4B,IAC7B9B,GAAW,GAEnB,EAEMxC,EAAsB,MAAOoF,EAAoB9B,KACnDd,GAAW,GACX,IAAMuC,EAAW,MAAMC,EAAAA,CAAUA,CAACC,GAAG,CAAC,SAAU/B,GAC5C6B,GAAYA,EAASpF,IAAI,EAAIoF,EAASpF,IAAI,CAACwF,MAAM,CAAG,GAAG,CAEvD7C,EAAe4C,EADCb,EAAaU,EAASpF,IAAI,EACFyF,EAAXd,IAC7B3B,EAAWyC,GACX5C,GAAW,GAEnB,EAEM0C,EAAgB,CAACG,EAAcC,EAAmBC,IAC7CF,EAAMG,KAAK,CAAC,CAACD,GAAc,EAAKD,EAAWC,EAAcD,GAGpEG,CAAAA,EAAAA,EAAAA,SAAAA,CAASA,CAAC,KACNX,GACJ,EAAG,EAAE,EAEL,IAAMV,EAAa,MAAOT,EAAQgB,KAC9Be,QAAQC,GAAG,CAAChC,EAAGgB,GACf9B,GAAS,GACTE,EAAa4B,GACThB,GAAKA,EAAEiC,GAAG,EAEV3C,EAAqB,CAAE,GAAGU,CAAC,CAAEgB,OADA,CACQkB,WADnBlB,EAAuB,WAAa,UACP,EAEvD,EAEMmB,EAAe,cAebC,EAMJ,GApBA/C,EAAkB,cAAiB,CAAlB,CAAqB,EACtCA,EAAkB,MAAS,CAAG,QAAb,GAEbA,GACAA,EAAkB,eAAD,GAAsB,EACvCA,EAAkB,eAAD,GAAsB,CAACmC,MAAM,EAChD,EACoB,eAAD,GAAsB,CAACa,GAAG,CAAC,IACpCC,EAAOpB,aAAa,GAAK7B,EAAkB,aAAgB,EAAE,GACtD2B,MAAM,CAAiB,YAAd7B,EAA0B,WAAa,YAEpDmD,IAIE,WAAW,CAAzBnD,EACC,MAAMkC,EAAAA,CAAUA,CAACkB,MAAM,CAAC,UAAmC,OAAzBlD,EAAkB,GAAM,GAE1D+C,EAAc,MAAMf,CAF+B,CAE/BA,CAAUA,CAACmB,KAAK,CAAC,UAAmC,OAAzBnD,EAAkB,GAAM,EAAIA,GAE3E+C,GAAsC,IAF0B,EAEjDA,EAAYpB,MAAM,CAAU,YAC3CyB,EAAAA,EAAKA,CAACC,KAAK,CACPN,EAAYhB,QAAQ,EAAIgB,EAAYhB,QAAQ,CAACuB,OAAO,CAC9CP,EAAYhB,QAAQ,CAACuB,OAAO,CAC5B9H,EAAE,8DAIZsG,IACkB,WAAW,CAAzBhC,EACAsD,EAAAA,EAAKA,CAACG,OAAO,CAAC/H,EAAE,oDAEhB4H,EAAAA,EAAKA,CAACC,KAAK,CAAC7H,EAAE,mDAElByE,EAAqB,CAAC,GACtBJ,GAAS,EAEjB,EAEM2D,EAAY,IAAM3D,GAAS,GAEjC,MACI,WAACV,MAAAA,WACG,WAACsE,EAAAA,CAAKA,CAAAA,CAACC,KAAM9D,EAAa+D,OAAQH,YAC9B,UAACC,EAAAA,CAAKA,CAACG,MAAM,EAACC,WAAW,aACrB,WAACJ,EAAAA,CAAKA,CAACK,KAAK,YACPhE,EAAUiE,MAAM,CAAC,GAAGC,WAAW,GAAKlE,EAAU0C,KAAK,CAAC,GAAI,IACxDhH,EAAE,mDAGX,WAACiI,EAAAA,CAAKA,CAACQ,IAAI,YACNzI,EAAE,0DAA0D,IAAEsE,EAAW,IACzEtE,EAAE,sDAEP,WAACiI,EAAAA,CAAKA,CAACS,MAAM,YACT,UAAClD,EAAAA,CAAMA,CAAAA,CAACC,QAAQ,YAAYE,QAASqC,WAChChI,EAAE,kDAEP,UAACwF,EAAAA,CAAMA,CAAAA,CAACC,QAAQ,UAAUE,QAAS2B,WAC9BtH,EAAE,qDAKf,UAACc,EAAAA,CAAQA,CAAAA,CACLI,QAASA,EACTC,KAAM0C,EACNzC,UAAWA,EACXU,WAAW,EACXN,oBAAqBA,EACrBC,iBAAkBA,MAIlC", "sources": ["webpack://_N_E/./pages/adminsettings/permissions.tsx", "webpack://_N_E/./pages/adminsettings/approval/focal_point_appoval.tsx", "webpack://_N_E/./components/common/RKITable.tsx", "webpack://_N_E/./pages/rNoAccess.tsx", "webpack://_N_E/?7483", "webpack://_N_E/./components/common/PageHeading.tsx", "webpack://_N_E/./pages/adminsettings/approval/AdminTable.tsx"], "sourcesContent": ["//Import services/components\r\nimport connectedAuthWrapper from 'redux-auth-wrapper/connectedAuthWrapper';\r\n\r\nconst create = \"create:any\";\r\nexport const canAddAreaOfWork = connectedAuthWrapper({\r\n  authenticatedSelector: (state) => {\r\n    if (state.permissions && state.permissions.area_of_work && state.permissions.area_of_work[create]) {\r\n      return true;\r\n    }\r\n    return false;\r\n  },\r\n  wrapperDisplayName: 'CanAddAreaOfWork',\r\n});\r\n\r\nexport const canAddCountry = connectedAuthWrapper({\r\n  authenticatedSelector: (state) => {\r\n    if (state.permissions && state.permissions.country && state.permissions.country[create]) {\r\n      return true;\r\n    }\r\n    return false;\r\n  },\r\n  wrapperDisplayName: 'CanAddCountry',\r\n});\r\n\r\nexport const canAddDeploymentStatus = connectedAuthWrapper({\r\n  authenticatedSelector: (state) => {\r\n    if (state.permissions && state.permissions.deployment_status && state.permissions.deployment_status[create]) {\r\n      return true;\r\n    }\r\n    return false;\r\n  },\r\n  wrapperDisplayName: 'CanAddDeploymentStatus',\r\n});\r\n\r\nexport const canAddEventStatus = connectedAuthWrapper({\r\n  authenticatedSelector: (state) => {\r\n    if (state.permissions && state.permissions.event_status && state.permissions.event_status[create]) {\r\n      return true;\r\n    }\r\n    return false;\r\n  },\r\n  wrapperDisplayName: 'CanAddEventStatus',\r\n});\r\n\r\nexport const canAddExpertise = connectedAuthWrapper({\r\n  authenticatedSelector: (state) => {\r\n    if (state.permissions && state.permissions.expertise && state.permissions.expertise[create]) {\r\n      return true;\r\n    }\r\n    return false;\r\n  },\r\n  wrapperDisplayName: 'CanAddExpertise',\r\n});\r\n\r\nexport const canAddFocalPointApproval = connectedAuthWrapper({\r\n  authenticatedSelector: (state) => {\r\n    if (state.permissions && state.permissions.institution_focal_point && state.permissions.institution_focal_point[create]) {\r\n      return true;\r\n    }\r\n    return false;\r\n  },\r\n  wrapperDisplayName: 'CanAddFocalPointApproval',\r\n});\r\n\r\nexport const canAddVspaceApproval = connectedAuthWrapper({\r\n  authenticatedSelector: (state) => {\r\n    if (state.permissions && state.permissions.institution_focal_point && state.permissions.institution_focal_point[create]) {\r\n      return true;\r\n    }\r\n    return false;\r\n  },\r\n  wrapperDisplayName: 'CanAddVspaceApproval',\r\n});\r\nexport const canAddHazards = connectedAuthWrapper({\r\n  authenticatedSelector: (state) => {\r\n    if (state.permissions && state.permissions.hazard && state.permissions.hazard[create]) {\r\n      return true;\r\n    }\r\n    return false;\r\n  },\r\n  wrapperDisplayName: 'CanAddHazards',\r\n});\r\n\r\nexport const canAddHazardTypes = connectedAuthWrapper({\r\n  authenticatedSelector: (state) => {\r\n    if (state.permissions && state.permissions.hazard_type && state.permissions.hazard_type[create]) {\r\n      return true;\r\n    }\r\n    return false;\r\n  },\r\n  wrapperDisplayName: 'CanAddHazardTypes',\r\n}); \r\n\r\nexport const canAddOrganisationApproval = connectedAuthWrapper({\r\n  authenticatedSelector: (state) => {\r\n    if (state.permissions && state.permissions.institution && state.permissions.institution[create]) {\r\n      return true;\r\n    }\r\n    return false;\r\n  },\r\n  wrapperDisplayName: 'CanAddOrganisationApproval',\r\n}); \r\n\r\nexport const canAddOrganisationNetworks = connectedAuthWrapper({\r\n  authenticatedSelector: (state) => {\r\n    if (state.permissions && state.permissions.institution_network && state.permissions.institution_network[create]) {\r\n      return true;\r\n    }\r\n    return false;\r\n  },\r\n  wrapperDisplayName: 'CanAddOrganisationNetworks',\r\n});\r\n\r\nexport const canAddOrganisationTypes = connectedAuthWrapper({\r\n  authenticatedSelector: (state) => {\r\n    if (state.permissions && state.permissions.institution_type && state.permissions.institution_type[create]) {\r\n      return true;\r\n    }\r\n    return false;\r\n  },\r\n  wrapperDisplayName: 'CanAddOrganisationTypes',\r\n});\r\n\r\nexport const canAddOperationStatus = connectedAuthWrapper({\r\n  authenticatedSelector: (state) => {\r\n    if (state.permissions && state.permissions.operation_status && state.permissions.operation_status[create]) {\r\n      return true;\r\n    }\r\n    return false;\r\n  },\r\n  wrapperDisplayName: 'CanAddOperationStatus',\r\n});\r\n\r\nexport const canAddProjectStatus = connectedAuthWrapper({\r\n  authenticatedSelector: (state) => {\r\n    if (state.permissions && state.permissions.project_status && state.permissions.project_status[create]) {\r\n      return true;\r\n    }\r\n    return false;\r\n  },\r\n  wrapperDisplayName: 'CanAddProjectStatus',\r\n});\r\n\r\nexport const canAddRegions = connectedAuthWrapper({\r\n  authenticatedSelector: (state) => {\r\n    if (state.permissions && state.permissions.region && state.permissions.region[create]) {\r\n      return true;\r\n    }\r\n    return false;\r\n  },\r\n  wrapperDisplayName: 'CanAddRegions',\r\n});\r\n\r\nexport const canAddRiskLevels = connectedAuthWrapper({\r\n  authenticatedSelector: (state) => {\r\n    if (state.permissions && state.permissions.risk_level && state.permissions.risk_level[create]) {\r\n      return true;\r\n    }\r\n    return false;\r\n  },\r\n  wrapperDisplayName: 'CanAddRiskLevels',\r\n});\r\n\r\nexport const canAddSyndromes = connectedAuthWrapper({\r\n  authenticatedSelector: (state) => {\r\n    if (state.permissions && state.permissions.syndrome && state.permissions.syndrome[create]) {\r\n      return true;\r\n    }\r\n    return false;\r\n  },\r\n  wrapperDisplayName: 'CanAddSyndromes',\r\n});\r\n\r\nexport const canAddUpdateTypes = connectedAuthWrapper({\r\n  authenticatedSelector: (state) => {\r\n    if (state.permissions && state.permissions.update_type && state.permissions.update_type[create]) {\r\n      return true;\r\n    }\r\n    return false;\r\n  },\r\n  wrapperDisplayName: 'CanAddUpdateTypes',\r\n});\r\n\r\nexport const canAddUsers = connectedAuthWrapper({\r\n  authenticatedSelector: (state) => {\r\n    if (state.permissions && state.permissions.users && state.permissions.users[create]) {\r\n      return true;\r\n    }\r\n    return false;\r\n  },\r\n  wrapperDisplayName: 'CanAddUsers',\r\n});\r\n\r\nexport const canAddWorldRegion = connectedAuthWrapper({\r\n  authenticatedSelector: (state) => {\r\n    if (state.permissions && state.permissions.worl_region && state.permissions.worl_region[create]) {\r\n      return true;\r\n    }\r\n    return false;\r\n  },\r\n  wrapperDisplayName: 'CanAddWorldRegion',\r\n});\r\n\r\nexport const canAddLandingPage = connectedAuthWrapper({\r\n  authenticatedSelector: (state) => {\r\n    if (state.permissions && state.permissions.landing_page && state.permissions.landing_page[create]) {\r\n      return true;\r\n    }\r\n    return false;\r\n  },\r\n  wrapperDisplayName: 'CanAddLandingPage',\r\n});\r\n\r\nexport const canAddContent = connectedAuthWrapper({\r\n  authenticatedSelector: (state) => {\r\n    if (state.permissions && state.permissions.operation && state.permissions.operation[create] && state.permissions.project && state.permissions.project[create]&& state.permissions.event && state.permissions.event[create]&& state.permissions.vspace && state.permissions.vspace[create]&& state.permissions.institution && state.permissions.institution[create]&& state.permissions.update && state.permissions.update[create]) {\r\n      return true;\r\n    }\r\n    return false;\r\n  },\r\n  wrapperDisplayName: 'CanAddContent',\r\n});\r\n\r\nexport default canAddAreaOfWork;", "//Import Library\r\nimport { Container } from \"react-bootstrap\";\r\n\r\n//Import services/components\r\nimport AdminTable from \"./AdminTable\";\r\nimport PageHeading from \"../../../components/common/PageHeading\";\r\nimport { useTranslation } from 'next-i18next';\r\nimport { serverSideTranslations } from 'next-i18next/serverSideTranslations';\r\nimport { canAddFocalPointApproval } from \"../permissions\";\r\nimport { useSelector } from \"react-redux\";\r\nimport NoAccessMessage from \"../../rNoAccess\";\r\n\r\nconst FocalPointShow = (_props: any) => {\r\n  const { t } = useTranslation('common');\r\n  const ShowFocalPoint = () => {\r\n    return (\r\n      <Container fluid className=\"p-0\">\r\n        <PageHeading title={t(\"adminsetting.FocalPointsApproval\")} />\r\n        <AdminTable />\r\n      </Container>\r\n    )\r\n  };\r\n\r\n  const ShowAddFocalPointApproval = canAddFocalPointApproval(() => <ShowFocalPoint />);\r\n  const state:any = useSelector((state: any) => state);\r\n  if (!(state?.permissions?.institution_focal_point?.['create:any'])) {\r\n    return <NoAccessMessage />\r\n  }\r\n  return(\r\n    <ShowAddFocalPointApproval />\r\n  );\r\n}\r\n\r\nexport async function getServerSideProps({ locale }: { locale: string }) {\r\n  return {\r\n    props: {\r\n      ...(await serverSideTranslations(locale, ['common'])),\r\n    },\r\n  }\r\n}\r\n\r\nexport default FocalPointShow;\r\n", "//Import Library\r\nimport React from 'react'\r\nimport DataTable  from 'react-data-table-component';\r\n\r\n//Import services/components\r\nimport { useTranslation } from 'next-i18next';\r\nimport CustomLoader from './CustomLoader';\r\n\r\ninterface RKITableProps {\r\n  columns: any[];\r\n  data: any[];\r\n  totalRows?: number;\r\n  resetPaginationToggle?: boolean;\r\n  subheader?: boolean;\r\n  subHeaderComponent?: React.ReactNode;\r\n  handlePerRowsChange?: (newPerPage: number, page: number) => void;\r\n  handlePageChange?: (page: number) => void;\r\n  rowsPerPage?: number[];\r\n  defaultRowsPerPage?: number;\r\n  selectableRows?: boolean;\r\n  loading?: boolean;\r\n  pagServer?: boolean;\r\n  onSelectedRowsChange?: (selectedRows: any) => void;\r\n  clearSelectedRows?: boolean;\r\n  sortServer?: boolean;\r\n  onSort?: (column: any, sortDirection: string) => void;\r\n  persistTableHead?: boolean;\r\n  sortFunction?: (rows: any[], field: any, direction: 'asc' | 'desc') => any[];\r\n  noHeader?: boolean;\r\n  dense?: boolean;\r\n  pagination?: boolean;\r\n  paginationServer?: boolean;\r\n  paginationTotalRows?: number;\r\n  subHeaderAlign?: string;\r\n  [key: string]: any;\r\n}\r\n\r\nfunction RKITable(props: RKITableProps) {\r\n  const { t } = useTranslation('common');\r\n  const paginationComponentOptions = {\r\n    rowsPerPageText: t('Rowsperpage'),\r\n    };\r\n  const {\r\n    columns,\r\n    data,\r\n    totalRows,\r\n    resetPaginationToggle,\r\n    subheader,\r\n    subHeaderComponent,\r\n    handlePerRowsChange,\r\n    handlePageChange,\r\n    rowsPerPage,\r\n    defaultRowsPerPage,\r\n    selectableRows,\r\n    loading,\r\n    pagServer,\r\n    onSelectedRowsChange,\r\n    clearSelectedRows,\r\n    sortServer,\r\n    onSort,\r\n    persistTableHead,\r\n    sortFunction,\r\n    // Filter out any unknown props that might cause DOM warnings\r\n    ...otherProps\r\n  } = props;\r\n\r\n  // Only pass known DataTable props to avoid DOM warnings\r\n  const dataTableProps = {\r\n    paginationComponentOptions,\r\n    noDataComponent: t(\"NoData\"),\r\n    noHeader: true,\r\n    columns,\r\n    data: data || [],\r\n    dense: true,\r\n    paginationResetDefaultPage: resetPaginationToggle,\r\n    subHeader: subheader,\r\n    progressPending: loading,\r\n    subHeaderComponent,\r\n    pagination: true,\r\n    paginationServer: pagServer,\r\n    paginationPerPage: defaultRowsPerPage ? defaultRowsPerPage : 10,\r\n    paginationRowsPerPageOptions: rowsPerPage ? rowsPerPage : [10, 15, 20, 25, 30],\r\n    paginationTotalRows: totalRows,\r\n    onChangeRowsPerPage: handlePerRowsChange,\r\n    onChangePage: handlePageChange,\r\n    selectableRows,\r\n    onSelectedRowsChange,\r\n    clearSelectedRows,\r\n    progressComponent: <CustomLoader/>,\r\n    sortIcon: <i className=\"sort-icon fas fa-exchange-alt\" />,\r\n    sortServer,\r\n    onSort,\r\n    sortFunction,\r\n    persistTableHead,\r\n    className: \"rki-table\"\r\n  };\r\n  return (\r\n    <DataTable {...dataTableProps} />\r\n  )\r\n};\r\n\r\nRKITable.defaultProps = {\r\n  subHeader: false,\r\n  pagination: true,\r\n  totalRows: null,\r\n  pagServer: true,\r\n  onSelectedRowsChange: null,\r\n  clearSelectedRows: false,\r\n  sortServer: false,\r\n  persistTableHead: false\r\n}\r\n\r\nexport default RKITable;\r\n\r\n", "export default function NoAccessMessage(_props: any) {\r\n    return (\r\n      <div className=\"container-fluid p-0 response-message-block\">\r\n        <div className=\"message\">you don't have permission to access</div>\r\n      </div>\r\n    )\r\n  }", "\n    (window.__NEXT_P = window.__NEXT_P || []).push([\n      \"/adminsettings/approval/focal_point_appoval\",\n      function () {\n        return require(\"private-next-pages/adminsettings/approval/focal_point_appoval.tsx\");\n      }\n    ]);\n    if(module.hot) {\n      module.hot.dispose(function () {\n        window.__NEXT_P.push([\"/adminsettings/approval/focal_point_appoval\"])\n      });\n    }\n  ", "interface PageHeadingProps {\r\n  title: string; // Required based on actual usage\r\n}\r\n\r\nexport default function PageHeading(props: PageHeadingProps) {\r\n  return (\r\n    <h2 className=\"page-heading\">{props.title}</h2>\r\n  )\r\n}\r\n", "//Import Library\r\nimport React, { useEffect, useState } from \"react\";\r\nimport { <PERSON><PERSON>, <PERSON><PERSON> } from \"react-bootstrap\";\r\nimport toast from 'react-hot-toast';\r\n\r\n//Import services/components\r\nimport RKITable from \"../../../components/common/RKITable\";\r\nimport apiService from \"../../../services/apiService\";\r\nimport { useTranslation } from 'next-i18next';\r\n\r\ninterface UserData {\r\n    _id: string;\r\n    username: string;\r\n    email: string;\r\n    institutionInvites: Array<{\r\n        institutionId: string;\r\n        institutionName: string;\r\n        status: string;\r\n    }>;\r\n    [key: string]: any;\r\n}\r\n\r\nfunction AdminTable(_props: any) {\r\n    const { t } = useTranslation('common');\r\n    const [tabledata, setDataToTable] = useState<any[]>([]);\r\n    const [, setLoading] = useState<boolean>(false);\r\n    const [totalRows, setTotalRows] = useState<number>(0);\r\n    const [perPage, setPerPage] = useState<number>(10);\r\n    const [isModalShow, setModal] = useState<boolean>(false);\r\n    const [newStatus, setNewStatus] = useState<string>(\"\");\r\n    const [selectUserDetails, setSelectUserDetails] = useState<any>({});\r\n\r\n\r\n    const usersParams = {\r\n        sort: { created_at: \"desc\" },\r\n        limit: \"~\",\r\n        page: 1,\r\n        query: { \"institutionInvites.status\": \"Request Pending\" },\r\n    };\r\n\r\n    const columns = [\r\n        {\r\n            name: t(\"adminsetting.FocalPointsApprovalTable.Username\"),\r\n            selector: \"username\",\r\n            cell: (d: any) => d.username,\r\n        },\r\n        {\r\n            name: t(\"OrganisationName\"),\r\n            selector: \"institutionName\",\r\n            cell: (d: any) => d.institutionName,\r\n        },\r\n        {\r\n            name: t(\"adminsetting.FocalPointsApprovalTable.Email\"),\r\n            selector: \"email\",\r\n            cell: (d: any) => d.email,\r\n        },\r\n        {\r\n            name: t(\"adminsetting.FocalPointsApprovalTable.Action\"),\r\n            selector: \"\",\r\n            cell: (d: any) => (\r\n                <div>\r\n                    {d.institutionStatus === \"Rejected\" || d.institutionStatus === \"Request Pending\" ? (\r\n                        <>\r\n                            <Button variant=\"primary\" size=\"sm\" onClick={() => userAction(d, \"approve\")}>\r\n                                {t(\"instu.Approves\")}\r\n                            </Button>\r\n                            &nbsp;\r\n                        </>\r\n                    ) : (\r\n                        <></>\r\n                    )}\r\n                    {d.institutionStatus === \"Approved\" || d.institutionStatus === \"Request Pending\" ? (\r\n                        <Button variant=\"secondary\" size=\"sm\" onClick={() => userAction(d, \"reject\")}>\r\n                            {t(\"instu.Reject\")}\r\n                        </Button>\r\n                    ) : (\r\n                        <></>\r\n                    )}\r\n                </div>\r\n            ),\r\n        },\r\n    ];\r\n\r\n    const getTableData = (data: UserData[]) => {\r\n        let tableData: any[] = [];\r\n        data.forEach((user) => {\r\n            user?.institutionInvites.forEach((institutionInvite) => {\r\n                if (institutionInvite && institutionInvite.status === \"Request Pending\") {\r\n                    tableData.push({\r\n                        ...user,\r\n                        ...{\r\n                            institutionId: institutionInvite.institutionId,\r\n                            institutionName: institutionInvite.institutionName,\r\n                            institutionStatus: institutionInvite.status,\r\n                        },\r\n                    });\r\n                }\r\n            });\r\n        });\r\n        return tableData;\r\n    };\r\n\r\n    const getUsersData = async () => {\r\n        setLoading(true);\r\n        const response = await apiService.get(\"/users\", usersParams);\r\n        if (response && response.data) {\r\n            let tableData = getTableData(response.data);\r\n            setDataToTable(localPaginate(tableData, perPage, 1));\r\n            setTotalRows(tableData.length);\r\n            setLoading(false);\r\n        }\r\n    };\r\n    const handlePageChange = async (page: number) => {\r\n        setLoading(true);\r\n        const response = await apiService.get(\"/users\", usersParams);\r\n        if (response && response.data && response.data.length > 0) {\r\n            let tableData = getTableData(response.data);\r\n            setDataToTable(localPaginate(tableData, perPage, page));\r\n            setLoading(false);\r\n        }\r\n    };\r\n\r\n    const handlePerRowsChange = async (newPerPage: number, page: number) => {\r\n        setLoading(true);\r\n        const response = await apiService.get(\"/users\", usersParams);\r\n        if (response && response.data && response.data.length > 0) {\r\n            let tableData = getTableData(response.data);\r\n            setDataToTable(localPaginate(tableData, newPerPage, page));\r\n            setPerPage(newPerPage);\r\n            setLoading(false);\r\n        }\r\n    };\r\n\r\n    const localPaginate = (array: any[], page_size: number, page_number: number) => {\r\n        return array.slice((page_number - 1) * page_size, page_number * page_size);\r\n    };\r\n\r\n    useEffect(() => {\r\n        getUsersData();\r\n    }, []);\r\n\r\n    const userAction = async (d: any, status: string) => {\r\n        console.log(d, status);\r\n        setModal(true);\r\n        setNewStatus(status);\r\n        if (d && d._id) {\r\n            const setStatus = status === \"approve\" ? \"Approved\" : \"Rejected\";\r\n            setSelectUserDetails({ ...d, status: setStatus });\r\n        }\r\n    };\r\n\r\n    const modalConfirm = async () => {\r\n        selectUserDetails[\"is_focal_point\"] = true;\r\n        selectUserDetails[\"status\"] = \"Approved\";\r\n        if (\r\n            selectUserDetails &&\r\n            selectUserDetails[\"institutionInvites\"] &&\r\n            selectUserDetails[\"institutionInvites\"].length\r\n        ) {\r\n            selectUserDetails[\"institutionInvites\"].map((invite: any) => {\r\n                if (invite.institutionId === selectUserDetails[\"institutionId\"]) {\r\n                    invite.status = newStatus === \"approve\" ? \"Approved\" : \"Rejected\";\r\n                }\r\n                return invite;\r\n            });\r\n        }\r\n        let updatedData;\r\n        if(newStatus !== \"approve\") {\r\n            await apiService.remove(`/users/${selectUserDetails[\"_id\"]}`);\r\n        } else {\r\n            updatedData = await apiService.patch(`/users/${selectUserDetails[\"_id\"]}`, selectUserDetails);\r\n        }\r\n        if (updatedData && updatedData.status === 403) {\r\n            toast.error(\r\n                updatedData.response && updatedData.response.message\r\n                    ? updatedData.response.message\r\n                    : t(\"adminsetting.FocalPointsApprovalTable.Somethingwentswrong\")\r\n            );\r\n            return;\r\n        } else {\r\n            getUsersData();\r\n            if (newStatus === \"approve\") {\r\n                toast.success(t(\"adminsetting.FocalPointsApprovalTable.Approvemm\"));\r\n            } else {\r\n                toast.error(t(\"adminsetting.FocalPointsApprovalTable.Rejected\"));\r\n            }\r\n            setSelectUserDetails({});\r\n            setModal(false);\r\n        }\r\n    };\r\n\r\n    const modalHide = () => setModal(false);\r\n\r\n    return (\r\n        <div>\r\n            <Modal show={isModalShow} onHide={modalHide}>\r\n                <Modal.Header closeButton>\r\n                    <Modal.Title>\r\n                        {newStatus.charAt(0).toUpperCase() + newStatus.slice(1)}{\" \"}\r\n                        {t(\"adminsetting.FocalPointsApprovalTable.User\")}\r\n                    </Modal.Title>\r\n                </Modal.Header>\r\n                <Modal.Body>\r\n                    {t(\"adminsetting.FocalPointsApprovalTable.Areyousurewantto\")} {newStatus}{\" \"}\r\n                    {t(\"adminsetting.FocalPointsApprovalTable.thisuser?\")}\r\n                </Modal.Body>\r\n                <Modal.Footer>\r\n                    <Button variant=\"secondary\" onClick={modalHide}>\r\n                        {t(\"adminsetting.FocalPointsApprovalTable.Cancel\")}\r\n                    </Button>\r\n                    <Button variant=\"primary\" onClick={modalConfirm}>\r\n                        {t(\"adminsetting.FocalPointsApprovalTable.Yes\")}\r\n                    </Button>\r\n                </Modal.Footer>\r\n            </Modal>\r\n\r\n            <RKITable\r\n                columns={columns}\r\n                data={tabledata}\r\n                totalRows={totalRows}\r\n                pagServer={true}\r\n                handlePerRowsChange={handlePerRowsChange}\r\n                handlePageChange={handlePageChange}\r\n            />\r\n        </div>\r\n    );\r\n}\r\n\r\nexport default AdminTable;\r\n"], "names": ["create", "canAddAreaOfWork", "connectedAuthWrapper", "authenticatedSelector", "state", "permissions", "area_of_work", "wrapperDisplayName", "country", "deployment_status", "event_status", "expertise", "institution_focal_point", "hazard", "hazard_type", "institution", "institution_network", "institution_type", "operation_status", "project_status", "region", "canAddRiskLevels", "risk_level", "canAddSyndromes", "syndrome", "update_type", "canAddUsers", "users", "worl_region", "landing_page", "operation", "project", "event", "vspace", "update", "t", "useTranslation", "ShowFocalPoint", "FocalPointShow", "Container", "fluid", "className", "PageHeading", "title", "AdminTable", "ShowAddFocalPointApproval", "canAddFocalPointApproval", "useSelector", "NoAccessMessage", "RKITable", "props", "paginationComponentOptions", "rowsPerPageText", "columns", "data", "totalRows", "resetPaginationToggle", "subheader", "subHeaderComponent", "handlePerRowsChange", "handlePageChange", "rowsPerPage", "defaultRowsPerPage", "selectableRows", "loading", "pagServer", "onSelectedRowsChange", "clearSelectedRows", "sortServer", "onSort", "persistTableHead", "sortFunction", "otherProps", "dataTableProps", "noDataComponent", "<PERSON><PERSON><PERSON><PERSON>", "dense", "paginationResetDefaultPage", "subHeader", "progressPending", "pagination", "paginationServer", "paginationPerPage", "paginationRowsPerPageOptions", "paginationTotalRows", "onChangeRowsPerPage", "onChangePage", "progressComponent", "CustomLoader", "sortIcon", "i", "DataTable", "defaultProps", "_props", "div", "h2", "tabledata", "setDataToTable", "useState", "setLoading", "setTotalRows", "perPage", "setPerPage", "isModalShow", "setModal", "newStatus", "setNewStatus", "selectUserDetails", "setSelectUserDetails", "usersParams", "sort", "created_at", "limit", "page", "query", "name", "selector", "cell", "d", "username", "institutionName", "email", "institutionStatus", "<PERSON><PERSON>", "variant", "size", "onClick", "userAction", "getTableData", "tableData", "for<PERSON>ach", "user", "institutionInvites", "institutionInvite", "status", "push", "institutionId", "getUsersData", "response", "apiService", "get", "localPaginate", "length", "newPerPage", "array", "page_size", "page_number", "slice", "useEffect", "console", "log", "_id", "setStatus", "modalConfirm", "updatedData", "map", "invite", "remove", "patch", "toast", "error", "message", "success", "modalHide", "Modal", "show", "onHide", "Header", "closeButton", "Title", "char<PERSON>t", "toUpperCase", "Body", "Footer"], "sourceRoot": "", "ignoreList": []}