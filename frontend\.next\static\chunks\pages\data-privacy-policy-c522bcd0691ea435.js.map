{"version": 3, "file": "static/chunks/pages/data-privacy-policy-c522bcd0691ea435.js", "mappings": "uKAMA,IAAMA,EAAwBC,EAAAA,OAAb,GAA6B,CAAC,GAK5CC,IALyB,IAAoB,WAC9CC,CAAS,CACTC,UAAQ,CACRC,GAAIC,EAAY,KAAK,CACrB,GAAGC,EACJ,GAEC,OADAH,EAAWI,CAAAA,EAAAA,EAAAA,EAAAA,CAAkBA,CAACJ,EAAU,aACpBK,CAAAA,EAAAA,EAAAA,GAAAA,CAAIA,CAACH,EAAW,CAClCJ,IAAKA,EACLC,UAAWO,IAAWP,EAAWC,GACjC,GAAGG,CAAK,EAEZ,GACAP,EAASW,WAAW,CAAG,WCbvB,IAAMC,EAA0BX,EAAAA,SAAb,CAA6B,CAAC,GAK9CC,MAL2B,EAAoB,WAChDC,CAAS,UACTC,CAAQ,CACRC,GAAIC,EAAY,KAAK,CACrB,GAAGC,EACJ,GAEC,OAAO,EADIC,CAAAA,EAAAA,EAAAA,EAAAA,CAAkBA,CAACJ,EAAU,eACpBK,CAAAA,EAAAA,EAAAA,GAAAA,CAAIA,CAACH,EAAW,CAClCJ,IAAKA,EACLC,UAAWO,IAAWP,EAAWC,GACjC,GAAGG,CAAK,EAEZ,GACAK,EAAWD,WAAW,CAAG,4BCXzB,IAAME,EAA0BZ,EAAAA,SAAb,CAA6B,CAAC,GAM9CC,MAN2B,EAAoB,UAChDE,CAAQ,WACRD,CAAS,CAETE,CADA,EACIC,EAAY,KAAK,CACrB,GAAGC,EACJ,GACOO,EAASN,CAAAA,EAAAA,EAAAA,EAAAA,CAAkBA,CAACJ,EAAU,eACtCW,EAAeC,CAAAA,EAAAA,EAAAA,OAAAA,CAAOA,CAAC,IAAO,EAClCC,mBAAoBH,EACtB,EAAI,CAACA,EAAO,EACZ,MAAoBL,CAAAA,EAAAA,EAAAA,GAAAA,CAAIA,CAACS,EAAP,CAAwBA,CAACC,QAAQ,CAAE,CACnDC,MAAOL,EACPM,SAAuBZ,CAAb,EAAaA,EAAAA,GAAAA,CAAIA,CAACH,EAAW,CACrCJ,IAAKA,EACL,GAAGK,CAAK,CACRJ,UAAWO,IAAWP,EAAWW,EACnC,EACF,EACF,GACAD,EAAWF,GAJgBD,QAIL,CAAG,aCtBzB,IAAMY,EAAuBrB,EAAAA,MAAb,IAA6B,CAC7C,CACA,EAMGC,GARwB,KAE1B,UACCE,CAAQ,WACRD,CAAS,SACToB,CAAO,CACPlB,GAAIC,EAAY,KAAK,CACrB,GAAGC,EACJ,GACOO,EAASN,CAAAA,EAAAA,EAAAA,EAAAA,CAAkBA,CAACJ,EAAU,YAC5C,MAAoBK,CAAb,EAAaA,EAAAA,GAAAA,CAAIA,CAACH,EAAP,CAChBJ,IAAKA,EACLC,UAAWO,IAAWa,EAAU,GAAaA,MAAAA,CAAVT,EAAO,EAArBJ,GAAgC,OAARa,CAX0G,EAW9FT,EAAQX,GACjE,GAAGI,CAAK,EAEZ,GACAe,EAAQX,WAAW,CAAG,UChBtB,IAAMa,EAA8BvB,EAAAA,UAAgB,CAAC,EAA9B,CAKpBC,QALmD,EAApB,SAChCC,CAAS,UACTC,CAAQ,CACRC,GAAIC,EAAY,KAAK,CACrB,GAAGC,EACJ,GAEC,OAAO,EADIC,CAAAA,EAAAA,EAAAA,EAAAA,CAAkBA,CAACJ,EAAU,oBACpBK,CAAAA,EAAAA,EAAAA,GAAAA,CAAIA,CAACH,EAAW,CAClCJ,IAAKA,EACLC,UAAWO,IAAWP,EAAWC,GACjC,GAAGG,CAAK,EAEZ,EACAiB,GAJyBd,WAIC,CAAG,iBCb7B,IAAMe,EAAwBxB,EAAAA,OAAb,GAA6B,CAAC,GAK5CC,IALyB,IAAoB,WAC9CC,CAAS,UACTC,CAAQ,CACRC,GAAIC,EAAY,GAAG,CACnB,GAAGC,EACJ,GAEC,OADAH,EAAWI,CAAAA,EAAAA,EAAAA,EAAAA,CAAkBA,CAACJ,EAAU,aACpBK,CAAAA,EAAAA,EAAAA,GAAAA,CAAIA,CAACH,EAAW,CAClCJ,IAAKA,EACLC,UAAWO,IAAWP,EAAWC,GACjC,GAAGG,CAAK,EAEZ,GACAkB,EAASd,WAAW,CAAG,0BCZvB,IAAMe,EAAgBC,CAAAA,EAAAA,EAAAA,CAAAA,CAAgBA,CAAC,MACjCC,EAA4B3B,EAAAA,UAAgB,CAAC,GAKhDC,QALiD,WAClDC,CAAS,UACTC,CAAQ,CACRC,GAAIC,EAAYoB,CAAa,CAC7B,GAAGnB,EACJ,GAEC,OAAO,EADIC,CAAAA,EAAAA,EAAAA,EAAAA,CAAkBA,CAACJ,EAAU,iBACpBK,CAAAA,EAAAA,EAAAA,GAAAA,CAAIA,CAACH,EAAW,CAClCJ,IAAKA,EACLC,UAAWO,IAAWP,EAAWC,GACjC,GAAGG,CAAK,EAEZ,GACAqB,EAJyBlB,WAID,CAAG,eCf3B,IAAMmB,EAAwB5B,EAAAA,OAAb,GAA6B,CAAC,GAK5CC,IALyB,IAAoB,WAC9CC,CAAS,UACTC,CAAQ,CACRC,GAAIC,EAAY,GAAG,CACnB,GAAGC,EACJ,GAEC,OADAH,EAAWI,CAAAA,EAAAA,EAAAA,EAAAA,CAAkBA,CAACJ,EAAU,aACpBK,CAAAA,EAAAA,EAAAA,GAAAA,CAAIA,CAACH,EAAW,CAClCJ,IAAKA,EACLC,UAAWO,IAAWP,EAAWC,GACjC,GAAGG,CAAK,EAEZ,GACAsB,EAASlB,WAAW,CAAG,WCZvB,IAAMmB,EAAgBH,CAAAA,EAAAA,EAAAA,CAAAA,CAAgBA,CAAC,MACjCI,EAAyB9B,EAAAA,QAAb,EAA6B,CAAC,GAK7CC,KAL0B,GAAoB,WAC/CC,CAAS,UACTC,CAAQ,CACRC,GAAIC,EAAYwB,CAAa,CAC7B,GAAGvB,EACJ,GAEC,OADAH,EAAWI,CAAAA,EAAAA,EAAAA,EAAAA,CAAkBA,CAACJ,EAAU,cACpBK,CAAAA,EAAAA,EAAAA,GAAAA,CAAIA,CAACH,EAAW,CAClCJ,IAAKA,EACLC,UAAWO,IAAWP,EAAWC,GACjC,GAAGG,CAAK,EAEZ,EACAwB,GAJyBrB,WAIJ,CAAG,YCNxB,IAAMsB,EAAoB/B,EAAAA,GAAb,OAA6B,CAAC,GAAnB,QAAoB,CAC1CG,UAAQ,WACRD,CAAS,IACT8B,CAAE,MACFC,CAAI,CACJC,QAAM,MACNC,GAAO,CAAK,UACZf,CAAQ,CAERhB,CADA,EACIC,EAAY,KAAK,CACrB,GAAGC,EACJ,GACOO,EAASN,CAAAA,EAAAA,EAAAA,EAAAA,CAAkBA,CAACJ,EAAU,QAC5C,MAAoBK,CAAb,EAAaA,EAAAA,GAAAA,CAAIA,CAACH,EAAP,CAChBJ,IAAKA,EACL,GAAGK,CAAK,CACRJ,UAAWO,IAAWP,EAAWW,EAAQmB,GAAM,MAAS,GAAnCvB,GAAmC,CAAHuB,GAAMC,GAAQ,QAAa,OAALA,GAAQC,GAAU,UAAiB,OAAPA,IACvGd,IATyJ,KAS/Ie,EAAoB3B,CAAAA,EAAAA,EAAb,GAAaA,CAAIA,CAACT,EAAU,CAC3CqB,GAD0B,MAAerB,CAE3C,GAAKqB,CACP,EACF,GACAW,EAAKrB,WAAW,CAAG,OACnB,MAAe0B,OAAOC,MAAM,CAACN,EAAM,CACjCO,INhBajB,CMgBRA,CACLkB,KNjBoBlB,CKDPS,CLCQ,CMkBrBU,EAFYnB,KDjBUS,EFATH,CGmBHA,CACVc,CAFgBX,ITpBH/B,CSsBPA,CACN2C,GHrByBf,EDFZH,CLAQzB,CSwBrB4C,CAHsBhB,GACR5B,CFtBD6B,CFAQJ,CIyBrBoB,CJzBsB,GIuBRpB,EFvBOI,CLSRhB,CKTS,CE0BtBiC,EAFcjB,KRxBDjB,CCSUC,COkBvBkC,CPlBwB,GOgBNlC,IRzBKD,EAAC,CGAXY,CK2BDA,CADMZ,CAElB,EAAC,SL5B0BY,EAAC,GK2BFA,qGC+D5B,MA/F0B,KACxB,GAAM,GAAEwB,CAAC,CAAE,CAAGC,CAAAA,EAAAA,EAAAA,EAAAA,CAAcA,CAAC,KA8FhBC,KA7Fb,MACE,MA4F4BA,EA5F5B,EAAClB,EAAAA,CAAIA,CAAAA,CAAC7B,UAAU,uBACd,WAAC6B,EAAAA,CAAIA,CAACU,IAAI,YACR,UAACS,KAAAA,UAAIH,EAAE,2BACP,UAACI,IAAAA,UAAGJ,EAAE,sBACN,WAACI,IAAAA,WACC,WAACC,SAAAA,WAAO,IAAEL,EAAE,uBACZ,UAACM,KAAAA,CAAAA,GACAN,EAAE,oBACH,UAACM,KAAAA,CAAAA,GACAN,EAAE,oBACH,UAACM,KAAAA,CAAAA,GACAN,EAAE,uBAEL,UAACI,IAAAA,UAAGJ,EAAE,sBACN,WAACI,IAAAA,WACC,UAACC,SAAAA,UAAQL,EAAE,sBACX,UAACM,KAAAA,CAAAA,GACAN,EAAE,oBACH,UAACM,KAAAA,CAAAA,GACAN,EAAE,oBACH,UAACM,KAAAA,CAAAA,GACAN,EAAE,qBACH,UAACM,KAAAA,CAAAA,GACAN,EAAE,qBACH,UAACM,KAAAA,CAAAA,GACAN,EAAE,wBAEL,WAACI,IAAAA,WACEJ,EAAE,qBACH,UAACM,KAAAA,CAAAA,GACAN,EAAE,qBACH,UAACM,KAAAA,CAAAA,GACAN,EAAE,qBACH,UAACM,KAAAA,CAAAA,GACAN,EAAE,qBAAsB,IACzB,UAACO,IAAAA,CAAEC,KAAMC,iCAAqB,UAAGA,iCAAqB,MAExD,WAACL,IAAAA,WACC,UAACC,SAAAA,UAAQL,EAAE,uBACX,UAACM,KAAAA,CAAAA,GACAN,EAAE,wBAEL,WAACI,IAAAA,WACEJ,EAAE,qBACH,UAACM,KAAAA,CAAAA,GACAN,EAAE,wBAEL,WAACI,IAAAA,WACC,WAACC,SAAAA,WAAO,IAAEL,EAAE,wBACZ,UAACM,KAAAA,CAAAA,GACAN,EAAE,wBAEL,WAACI,IAAAA,WACC,UAACC,SAAAA,UAAQL,EAAE,uBACX,UAACM,KAAAA,CAAAA,GACAN,EAAE,qBACH,UAACM,KAAAA,CAAAA,GACD,UAACC,IAAAA,CAAEG,OAAO,SAASF,KAAMC,uBAA4B,UAClDA,uBAA4B,MAGjC,UAACE,KAAAA,UAAIX,EAAE,iCACP,UAACI,IAAAA,UACC,UAACC,SAAAA,UAAQL,EAAE,kCAEb,UAACI,IAAAA,UAAGJ,EAAE,gCACN,UAACI,IAAAA,UACC,WAACC,SAAAA,WAAO,IAAEL,EAAE,mCAEd,UAACI,IAAAA,UAAGJ,EAAE,gCACN,UAACI,IAAAA,UAAGJ,EAAE,gCACN,WAACY,KAAAA,WACC,UAACC,KAAAA,UAAIb,EAAE,gCACP,UAACa,KAAAA,UAAIb,EAAE,gCACP,UAACa,KAAAA,UAAIb,EAAE,gCACP,UAACa,KAAAA,UAAIb,EAAE,gCACP,UAACa,KAAAA,UAAIb,EAAE,iCACP,UAACa,KAAAA,UAAIb,EAAE,oCAET,UAACI,IAAAA,UAAGJ,EAAE,iCACN,UAACI,IAAAA,UAAGJ,EAAE,iCACN,WAACI,IAAAA,WACEJ,EAAE,+BAAgC,IACnC,UAACO,IAAAA,CAAEG,OAAO,SAASF,KAAMR,EAAE,yCACxBA,EAAE,yCAMf,iDCzGA,IAAMc,EAAuB7D,QAAb,CAAaA,UAAF,GAAqB,CAAC,MACjD6D,EAAQnD,WAAW,CAAG,oBACtB,MAAemD,OAAOA,EAAC,UCJvB,4CACA,uBACA,WACA,OAAe,EAAQ,KAA4C,CACnE,EACA,SAFsB", "sources": ["webpack://_N_E/./node_modules/react-bootstrap/esm/CardBody.js", "webpack://_N_E/./node_modules/react-bootstrap/esm/CardFooter.js", "webpack://_N_E/./node_modules/react-bootstrap/esm/CardHeader.js", "webpack://_N_E/./node_modules/react-bootstrap/esm/CardImg.js", "webpack://_N_E/./node_modules/react-bootstrap/esm/CardImgOverlay.js", "webpack://_N_E/./node_modules/react-bootstrap/esm/CardLink.js", "webpack://_N_E/./node_modules/react-bootstrap/esm/CardSubtitle.js", "webpack://_N_E/./node_modules/react-bootstrap/esm/CardText.js", "webpack://_N_E/./node_modules/react-bootstrap/esm/CardTitle.js", "webpack://_N_E/./node_modules/react-bootstrap/esm/Card.js", "webpack://_N_E/./pages/data-privacy-policy.tsx", "webpack://_N_E/./node_modules/react-bootstrap/esm/CardHeaderContext.js", "webpack://_N_E/?0429"], "sourcesContent": ["\"use client\";\n\nimport * as React from 'react';\nimport classNames from 'classnames';\nimport { useBootstrapPrefix } from './ThemeProvider';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst CardBody = /*#__PURE__*/React.forwardRef(({\n  className,\n  bsPrefix,\n  as: Component = 'div',\n  ...props\n}, ref) => {\n  bsPrefix = useBootstrapPrefix(bsPrefix, 'card-body');\n  return /*#__PURE__*/_jsx(Component, {\n    ref: ref,\n    className: classNames(className, bsPrefix),\n    ...props\n  });\n});\nCardBody.displayName = 'CardBody';\nexport default CardBody;", "\"use client\";\n\nimport * as React from 'react';\nimport classNames from 'classnames';\nimport { useBootstrapPrefix } from './ThemeProvider';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst CardFooter = /*#__PURE__*/React.forwardRef(({\n  className,\n  bsPrefix,\n  as: Component = 'div',\n  ...props\n}, ref) => {\n  bsPrefix = useBootstrapPrefix(bsPrefix, 'card-footer');\n  return /*#__PURE__*/_jsx(Component, {\n    ref: ref,\n    className: classNames(className, bsPrefix),\n    ...props\n  });\n});\nCardFooter.displayName = 'CardFooter';\nexport default CardFooter;", "\"use client\";\n\nimport classNames from 'classnames';\nimport * as React from 'react';\nimport { useMemo } from 'react';\nimport { useBootstrapPrefix } from './ThemeProvider';\nimport CardHeaderContext from './CardHeaderContext';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst CardHeader = /*#__PURE__*/React.forwardRef(({\n  bsPrefix,\n  className,\n  // Need to define the default \"as\" during prop destructuring to be compatible with styled-components github.com/react-bootstrap/react-bootstrap/issues/3595\n  as: Component = 'div',\n  ...props\n}, ref) => {\n  const prefix = useBootstrapPrefix(bsPrefix, 'card-header');\n  const contextValue = useMemo(() => ({\n    cardHeaderBsPrefix: prefix\n  }), [prefix]);\n  return /*#__PURE__*/_jsx(CardHeaderContext.Provider, {\n    value: contextValue,\n    children: /*#__PURE__*/_jsx(Component, {\n      ref: ref,\n      ...props,\n      className: classNames(className, prefix)\n    })\n  });\n});\nCardHeader.displayName = 'CardHeader';\nexport default CardHeader;", "\"use client\";\n\nimport classNames from 'classnames';\nimport * as React from 'react';\nimport { useBootstrapPrefix } from './ThemeProvider';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst CardImg = /*#__PURE__*/React.forwardRef(\n// Need to define the default \"as\" during prop destructuring to be compatible with styled-components github.com/react-bootstrap/react-bootstrap/issues/3595\n({\n  bsPrefix,\n  className,\n  variant,\n  as: Component = 'img',\n  ...props\n}, ref) => {\n  const prefix = useBootstrapPrefix(bsPrefix, 'card-img');\n  return /*#__PURE__*/_jsx(Component, {\n    ref: ref,\n    className: classNames(variant ? `${prefix}-${variant}` : prefix, className),\n    ...props\n  });\n});\nCardImg.displayName = 'CardImg';\nexport default CardImg;", "\"use client\";\n\nimport * as React from 'react';\nimport classNames from 'classnames';\nimport { useBootstrapPrefix } from './ThemeProvider';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst CardImgOverlay = /*#__PURE__*/React.forwardRef(({\n  className,\n  bsPrefix,\n  as: Component = 'div',\n  ...props\n}, ref) => {\n  bsPrefix = useBootstrapPrefix(bsPrefix, 'card-img-overlay');\n  return /*#__PURE__*/_jsx(Component, {\n    ref: ref,\n    className: classNames(className, bsPrefix),\n    ...props\n  });\n});\nCardImgOverlay.displayName = 'CardImgOverlay';\nexport default CardImgOverlay;", "\"use client\";\n\nimport * as React from 'react';\nimport classNames from 'classnames';\nimport { useBootstrapPrefix } from './ThemeProvider';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst CardLink = /*#__PURE__*/React.forwardRef(({\n  className,\n  bsPrefix,\n  as: Component = 'a',\n  ...props\n}, ref) => {\n  bsPrefix = useBootstrapPrefix(bsPrefix, 'card-link');\n  return /*#__PURE__*/_jsx(Component, {\n    ref: ref,\n    className: classNames(className, bsPrefix),\n    ...props\n  });\n});\nCardLink.displayName = 'CardLink';\nexport default CardLink;", "\"use client\";\n\nimport * as React from 'react';\nimport classNames from 'classnames';\nimport { useBootstrapPrefix } from './ThemeProvider';\nimport divWithClassName from './divWithClassName';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst DivStyledAsH6 = divWithClassName('h6');\nconst CardSubtitle = /*#__PURE__*/React.forwardRef(({\n  className,\n  bsPrefix,\n  as: Component = DivStyledAsH6,\n  ...props\n}, ref) => {\n  bsPrefix = useBootstrapPrefix(bsPrefix, 'card-subtitle');\n  return /*#__PURE__*/_jsx(Component, {\n    ref: ref,\n    className: classNames(className, bsPrefix),\n    ...props\n  });\n});\nCardSubtitle.displayName = 'CardSubtitle';\nexport default CardSubtitle;", "\"use client\";\n\nimport * as React from 'react';\nimport classNames from 'classnames';\nimport { useBootstrapPrefix } from './ThemeProvider';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst CardText = /*#__PURE__*/React.forwardRef(({\n  className,\n  bsPrefix,\n  as: Component = 'p',\n  ...props\n}, ref) => {\n  bsPrefix = useBootstrapPrefix(bsPrefix, 'card-text');\n  return /*#__PURE__*/_jsx(Component, {\n    ref: ref,\n    className: classNames(className, bsPrefix),\n    ...props\n  });\n});\nCardText.displayName = 'CardText';\nexport default CardText;", "\"use client\";\n\nimport * as React from 'react';\nimport classNames from 'classnames';\nimport { useBootstrapPrefix } from './ThemeProvider';\nimport divWithClassName from './divWithClassName';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst DivStyledAsH5 = divWithClassName('h5');\nconst CardTitle = /*#__PURE__*/React.forwardRef(({\n  className,\n  bsPrefix,\n  as: Component = DivStyledAsH5,\n  ...props\n}, ref) => {\n  bsPrefix = useBootstrapPrefix(bsPrefix, 'card-title');\n  return /*#__PURE__*/_jsx(Component, {\n    ref: ref,\n    className: classNames(className, bsPrefix),\n    ...props\n  });\n});\nCardTitle.displayName = 'CardTitle';\nexport default CardTitle;", "\"use client\";\n\nimport classNames from 'classnames';\nimport * as React from 'react';\nimport { useBootstrapPrefix } from './ThemeProvider';\nimport CardBody from './CardBody';\nimport CardFooter from './CardFooter';\nimport CardHeader from './CardHeader';\nimport CardImg from './CardImg';\nimport CardImgOverlay from './CardImgOverlay';\nimport CardLink from './CardLink';\nimport CardSubtitle from './CardSubtitle';\nimport CardText from './CardText';\nimport CardTitle from './CardTitle';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst Card = /*#__PURE__*/React.forwardRef(({\n  bsPrefix,\n  className,\n  bg,\n  text,\n  border,\n  body = false,\n  children,\n  // Need to define the default \"as\" during prop destructuring to be compatible with styled-components github.com/react-bootstrap/react-bootstrap/issues/3595\n  as: Component = 'div',\n  ...props\n}, ref) => {\n  const prefix = useBootstrapPrefix(bsPrefix, 'card');\n  return /*#__PURE__*/_jsx(Component, {\n    ref: ref,\n    ...props,\n    className: classNames(className, prefix, bg && `bg-${bg}`, text && `text-${text}`, border && `border-${border}`),\n    children: body ? /*#__PURE__*/_jsx(CardBody, {\n      children: children\n    }) : children\n  });\n});\nCard.displayName = 'Card';\nexport default Object.assign(Card, {\n  Img: CardImg,\n  Title: CardTitle,\n  Subtitle: CardSubtitle,\n  Body: CardBody,\n  Link: CardLink,\n  Text: CardText,\n  Header: CardHeader,\n  Footer: CardFooter,\n  ImgOverlay: CardImgOverlay\n});", "//Import Library\r\nimport { Card } from \"react-bootstrap\";\r\n\r\n//Import services/components\r\nimport { useTranslation } from 'next-i18next';\r\nimport { serverSideTranslations } from 'next-i18next/serverSideTranslations';\r\n\r\nexport async function getServerSideProps({ locale } : { locale: string }) {\r\n  return {\r\n    props: {\r\n      ...(await serverSideTranslations(locale, ['common'])),\r\n    },\r\n  }\r\n}\r\n\r\nconst DataPrivacyPolicy = () => {\r\n  const { t } = useTranslation('common');\r\n  return (\r\n    <Card className=\"privacyCard\">\r\n      <Card.Body>\r\n        <h2>{t(\"dataPolicy.mainheader\")}</h2>\r\n        <p>{t(\"dataPolicy.info1\")}</p>\r\n        <p>\r\n          <strong> {t(\"dataPolicy.info2\")}</strong>\r\n          <br />\r\n          {t(\"dataPolicy.info3\")}\r\n          <br />\r\n          {t(\"dataPolicy.info4\")}\r\n          <br />\r\n          {t(\"dataPolicy.info5\")}\r\n        </p>\r\n        <p>{t(\"dataPolicy.info6\")}</p>\r\n        <p>\r\n          <strong>{t(\"dataPolicy.info7\")}</strong>\r\n          <br />\r\n          {t(\"dataPolicy.info8\")}\r\n          <br />\r\n          {t(\"dataPolicy.info9\")}\r\n          <br />\r\n          {t(\"dataPolicy.info10\")}\r\n          <br />\r\n          {t(\"dataPolicy.info11\")}\r\n          <br />\r\n          {t(\"dataPolicy.info12\")}\r\n        </p>\r\n        <p>\r\n          {t(\"dataPolicy.info13\")}\r\n          <br />\r\n          {t(\"dataPolicy.info14\")}\r\n          <br />\r\n          {t(\"dataPolicy.info15\")}\r\n          <br />\r\n          {t(\"dataPolicy.info16\")}{\" \"}\r\n          <a href={process.env.HOME_PAGE}>{process.env.HOME_PAGE}</a>\r\n        </p>\r\n        <p>\r\n          <strong>{t(\"dataPolicy.info17\")}</strong>\r\n          <br />\r\n          {t(\"dataPolicy.info18\")}\r\n        </p>\r\n        <p>\r\n          {t(\"dataPolicy.info19\")}\r\n          <br />\r\n          {t(\"dataPolicy.info20\")}\r\n        </p>\r\n        <p>\r\n          <strong> {t(\"dataPolicy.info21\")}</strong>\r\n          <br />\r\n          {t(\"dataPolicy.info22\")}\r\n        </p>\r\n        <p>\r\n          <strong>{t(\"dataPolicy.info23\")}</strong>\r\n          <br />\r\n          {t(\"dataPolicy.info24\")}\r\n          <br />\r\n          <a target=\"_blank\" href={process.env.ADAPPT_HOME_PAGE}>\r\n            {process.env.ADAPPT_HOME_PAGE}\r\n          </a>\r\n        </p>\r\n        <h3>{t(\"dataPolicy.subheader.header\")}</h3>\r\n        <p>\r\n          <strong>{t(\"dataPolicy.subheader.text1\")}</strong>\r\n        </p>\r\n        <p>{t(\"dataPolicy.subheader.text2\")}</p>\r\n        <p>\r\n          <strong> {t(\"dataPolicy.subheader.text3\")}</strong>\r\n        </p>\r\n        <p>{t(\"dataPolicy.subheader.text4\")}</p>\r\n        <p>{t(\"dataPolicy.subheader.text5\")}</p>\r\n        <ul>\r\n          <li>{t(\"dataPolicy.subheader.text6\")}</li>\r\n          <li>{t(\"dataPolicy.subheader.text7\")}</li>\r\n          <li>{t(\"dataPolicy.subheader.text8\")}</li>\r\n          <li>{t(\"dataPolicy.subheader.text9\")}</li>\r\n          <li>{t(\"dataPolicy.subheader.text10\")}</li>\r\n          <li>{t(\"dataPolicy.subheader.text11\")}</li>\r\n        </ul>\r\n        <p>{t(\"dataPolicy.subheader.text12\")}</p>\r\n        <p>{t(\"dataPolicy.subheader.text13\")}</p>\r\n        <p>\r\n          {t(\"dataPolicy.subheader.text14\")}{\" \"}\r\n          <a target=\"_blank\" href={t(\"dataPolicy.subheader.text14a\")}>\r\n            {t(\"dataPolicy.subheader.text14b\")}\r\n          </a>\r\n        </p>\r\n      </Card.Body>\r\n    </Card>\r\n  );\r\n};\r\n\r\nexport default DataPrivacyPolicy;\r\n", "\"use client\";\n\nimport * as React from 'react';\nconst context = /*#__PURE__*/React.createContext(null);\ncontext.displayName = 'CardHeaderContext';\nexport default context;", "\n    (window.__NEXT_P = window.__NEXT_P || []).push([\n      \"/data-privacy-policy\",\n      function () {\n        return require(\"private-next-pages/data-privacy-policy.tsx\");\n      }\n    ]);\n    if(module.hot) {\n      module.hot.dispose(function () {\n        window.__NEXT_P.push([\"/data-privacy-policy\"])\n      });\n    }\n  "], "names": ["CardBody", "React", "ref", "className", "bsPrefix", "as", "Component", "props", "useBootstrapPrefix", "_jsx", "classNames", "displayName", "<PERSON><PERSON><PERSON>er", "<PERSON><PERSON><PERSON><PERSON>", "prefix", "contextValue", "useMemo", "cardHeaderBsPrefix", "CardHeaderContext", "Provider", "value", "children", "CardImg", "variant", "CardImgOverlay", "CardLink", "DivStyledAsH6", "divWithClassName", "CardSubtitle", "CardText", "DivStyledAsH5", "CardTitle", "Card", "bg", "text", "border", "body", "Object", "assign", "Img", "Title", "Subtitle", "Body", "Link", "Text", "Header", "Footer", "ImgOverlay", "t", "useTranslation", "DataPrivacyPolicy", "h2", "p", "strong", "br", "a", "href", "process", "target", "h3", "ul", "li", "context"], "sourceRoot": "", "ignoreList": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 11]}