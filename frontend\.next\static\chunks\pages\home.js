/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["pages/home"],{

/***/ "(pages-dir-browser)/./components/layout/landing/AboutUs.tsx":
/*!***********************************************!*\
  !*** ./components/layout/landing/AboutUs.tsx ***!
  \***********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(pages-dir-browser)/./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(pages-dir-browser)/./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_Col_Row_react_bootstrap__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=Col,Row!=!react-bootstrap */ \"(pages-dir-browser)/__barrel_optimize__?names=Col,Row!=!./node_modules/react-bootstrap/esm/index.js\");\n//Import Library\n\n\n\nconst AboutUs = (param)=>{\n    let { innerRef } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"about-us\",\n        ref: innerRef,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"aboutus-wrap\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Col_Row_react_bootstrap__WEBPACK_IMPORTED_MODULE_2__.Row, {\n                xs: 12,\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Col_Row_react_bootstrap__WEBPACK_IMPORTED_MODULE_2__.Col, {\n                        sm: 6,\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                            src: \"/images/home/<USER>",\n                            alt: \"About\",\n                            width: \"100%\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\rki\\\\frontend\\\\components\\\\layout\\\\landing\\\\AboutUs.tsx\",\n                            lineNumber: 10,\n                            columnNumber: 9\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\rki\\\\frontend\\\\components\\\\layout\\\\landing\\\\AboutUs.tsx\",\n                        lineNumber: 9,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Col_Row_react_bootstrap__WEBPACK_IMPORTED_MODULE_2__.Col, {\n                        sm: 6,\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"content\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"title\",\n                                    children: \"About us\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\rki\\\\frontend\\\\components\\\\layout\\\\landing\\\\AboutUs.tsx\",\n                                    lineNumber: 14,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"desc\",\n                                    children: \"The German Ministry of Health has commissioned the Robert Koch Institut (RKI) to develop this platform. Under the coordination of the Centre for International Health Protection, based at RKI in Berlin, we want to support our partners in exchanging information in order to better coordinate the activities of all of them and thus strengthen the response capacity of all. \"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\rki\\\\frontend\\\\components\\\\layout\\\\landing\\\\AboutUs.tsx\",\n                                    lineNumber: 15,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\rki\\\\frontend\\\\components\\\\layout\\\\landing\\\\AboutUs.tsx\",\n                            lineNumber: 13,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\rki\\\\frontend\\\\components\\\\layout\\\\landing\\\\AboutUs.tsx\",\n                        lineNumber: 12,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\rki\\\\frontend\\\\components\\\\layout\\\\landing\\\\AboutUs.tsx\",\n                lineNumber: 8,\n                columnNumber: 7\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"D:\\\\rki\\\\frontend\\\\components\\\\layout\\\\landing\\\\AboutUs.tsx\",\n            lineNumber: 7,\n            columnNumber: 5\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"D:\\\\rki\\\\frontend\\\\components\\\\layout\\\\landing\\\\AboutUs.tsx\",\n        lineNumber: 6,\n        columnNumber: 3\n    }, undefined);\n};\n_c = AboutUs;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (AboutUs);\nvar _c;\n$RefreshReg$(_c, \"AboutUs\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-browser)/./components/layout/landing/AboutUs.tsx\n"));

/***/ }),

/***/ "(pages-dir-browser)/./components/layout/landing/Footer.tsx":
/*!**********************************************!*\
  !*** ./components/layout/landing/Footer.tsx ***!
  \**********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(pages-dir-browser)/./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(pages-dir-browser)/./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_Col_Row_react_bootstrap__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=Col,Row!=!react-bootstrap */ \"(pages-dir-browser)/__barrel_optimize__?names=Col,Row!=!./node_modules/react-bootstrap/esm/index.js\");\n//Import Library\n\n\n\nconst Footer = (param)=>{\n    let { innerRef } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"landing-footer\",\n        ref: innerRef,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"footer-block\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Col_Row_react_bootstrap__WEBPACK_IMPORTED_MODULE_2__.Col, {\n                xs: 12,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Col_Row_react_bootstrap__WEBPACK_IMPORTED_MODULE_2__.Row, {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Col_Row_react_bootstrap__WEBPACK_IMPORTED_MODULE_2__.Col, {\n                            sm: 3,\n                            style: {\n                                marginTop: \"10px\"\n                            },\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                src: \"/images/home/<USER>",\n                                className: \"img-fluid\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\rki\\\\frontend\\\\components\\\\layout\\\\landing\\\\Footer.tsx\",\n                                lineNumber: 18,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\rki\\\\frontend\\\\components\\\\layout\\\\landing\\\\Footer.tsx\",\n                            lineNumber: 12,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Col_Row_react_bootstrap__WEBPACK_IMPORTED_MODULE_2__.Col, {\n                            sm: 3,\n                            style: {\n                                marginTop: \"10px\"\n                            },\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"footerLeft\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    style: {\n                                        lineHeight: \"14px\"\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            children: \"The Robert Koch Institut is a Federal Institute within the portfolio of the Federal Ministry of Health\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\rki\\\\frontend\\\\components\\\\layout\\\\landing\\\\Footer.tsx\",\n                                            lineNumber: 28,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"br\", {}, void 0, false, {\n                                                    fileName: \"D:\\\\rki\\\\frontend\\\\components\\\\layout\\\\landing\\\\Footer.tsx\",\n                                                    lineNumber: 33,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    style: {\n                                                        fontFamily: \"verdana\"\n                                                    },\n                                                    children: \"\\xa9\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\rki\\\\frontend\\\\components\\\\layout\\\\landing\\\\Footer.tsx\",\n                                                    lineNumber: 34,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                \" \",\n                                                new Date().getFullYear(),\n                                                \" Robert Koch Institute\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\rki\\\\frontend\\\\components\\\\layout\\\\landing\\\\Footer.tsx\",\n                                            lineNumber: 32,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            children: \"All rights reserved unless explicitly granted.\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\rki\\\\frontend\\\\components\\\\layout\\\\landing\\\\Footer.tsx\",\n                                            lineNumber: 37,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\rki\\\\frontend\\\\components\\\\layout\\\\landing\\\\Footer.tsx\",\n                                    lineNumber: 27,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\rki\\\\frontend\\\\components\\\\layout\\\\landing\\\\Footer.tsx\",\n                                lineNumber: 26,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\rki\\\\frontend\\\\components\\\\layout\\\\landing\\\\Footer.tsx\",\n                            lineNumber: 20,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Col_Row_react_bootstrap__WEBPACK_IMPORTED_MODULE_2__.Col, {\n                            sm: 3,\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"contactInfo\",\n                                style: {\n                                    marginTop: \"3px\"\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        style: {\n                                            fontSize: \"16px\"\n                                        },\n                                        children: \"Robert Koch Institut\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\rki\\\\frontend\\\\components\\\\layout\\\\landing\\\\Footer.tsx\",\n                                        lineNumber: 43,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        children: [\n                                            \"Federal Information Centre for International\",\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"br\", {}, void 0, false, {\n                                                fileName: \"D:\\\\rki\\\\frontend\\\\components\\\\layout\\\\landing\\\\Footer.tsx\",\n                                                lineNumber: 46,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            \"Health Protection (ZIG1)\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\rki\\\\frontend\\\\components\\\\layout\\\\landing\\\\Footer.tsx\",\n                                        lineNumber: 44,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        children: \"Nordufer 20\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\rki\\\\frontend\\\\components\\\\layout\\\\landing\\\\Footer.tsx\",\n                                        lineNumber: 49,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        children: \"13353 Berlin \"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\rki\\\\frontend\\\\components\\\\layout\\\\landing\\\\Footer.tsx\",\n                                        lineNumber: 50,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        children: \"Germany\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\rki\\\\frontend\\\\components\\\\layout\\\\landing\\\\Footer.tsx\",\n                                        lineNumber: 51,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"br\", {}, void 0, false, {\n                                        fileName: \"D:\\\\rki\\\\frontend\\\\components\\\\layout\\\\landing\\\\Footer.tsx\",\n                                        lineNumber: 52,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"br\", {}, void 0, false, {\n                                        fileName: \"D:\\\\rki\\\\frontend\\\\components\\\\layout\\\\landing\\\\Footer.tsx\",\n                                        lineNumber: 53,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\rki\\\\frontend\\\\components\\\\layout\\\\landing\\\\Footer.tsx\",\n                                lineNumber: 42,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\rki\\\\frontend\\\\components\\\\layout\\\\landing\\\\Footer.tsx\",\n                            lineNumber: 41,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Col_Row_react_bootstrap__WEBPACK_IMPORTED_MODULE_2__.Col, {\n                            sm: 3,\n                            style: {\n                                marginTop: \"10px\"\n                            },\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"contactNum\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        children: \"Tel: +49 (0) 3018 7540\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\rki\\\\frontend\\\\components\\\\layout\\\\landing\\\\Footer.tsx\",\n                                        lineNumber: 63,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        children: \"Email: <EMAIL>\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\rki\\\\frontend\\\\components\\\\layout\\\\landing\\\\Footer.tsx\",\n                                        lineNumber: 64,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\rki\\\\frontend\\\\components\\\\layout\\\\landing\\\\Footer.tsx\",\n                                lineNumber: 62,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\rki\\\\frontend\\\\components\\\\layout\\\\landing\\\\Footer.tsx\",\n                            lineNumber: 56,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\rki\\\\frontend\\\\components\\\\layout\\\\landing\\\\Footer.tsx\",\n                    lineNumber: 11,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"D:\\\\rki\\\\frontend\\\\components\\\\layout\\\\landing\\\\Footer.tsx\",\n                lineNumber: 10,\n                columnNumber: 7\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"D:\\\\rki\\\\frontend\\\\components\\\\layout\\\\landing\\\\Footer.tsx\",\n            lineNumber: 9,\n            columnNumber: 5\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"D:\\\\rki\\\\frontend\\\\components\\\\layout\\\\landing\\\\Footer.tsx\",\n        lineNumber: 8,\n        columnNumber: 3\n    }, undefined);\n};\n_c = Footer;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (Footer);\nvar _c;\n$RefreshReg$(_c, \"Footer\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-browser)/./components/layout/landing/Footer.tsx\n"));

/***/ }),

/***/ "(pages-dir-browser)/./components/layout/landing/Header.tsx":
/*!**********************************************!*\
  !*** ./components/layout/landing/Header.tsx ***!
  \**********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(pages-dir-browser)/./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(pages-dir-browser)/./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_Col_Row_react_bootstrap__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Col,Row!=!react-bootstrap */ \"(pages-dir-browser)/__barrel_optimize__?names=Col,Row!=!./node_modules/react-bootstrap/esm/index.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(pages-dir-browser)/./node_modules/next/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _modals_layout_help__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./../modals/layout-help */ \"(pages-dir-browser)/./components/layout/modals/layout-help.tsx\");\n//Import Library\n\n\n\n\n//Import services/components\n\nconst Header = (param)=>{\n    let { onScroll } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"header-block\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"header-container\",\n            style: {},\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Col_Row_react_bootstrap__WEBPACK_IMPORTED_MODULE_4__.Row, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Col_Row_react_bootstrap__WEBPACK_IMPORTED_MODULE_4__.Col, {\n                        sm: 3,\n                        className: \"logo-image\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                            src: \"/images/home/<USER>",\n                            height: \"60px\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\rki\\\\frontend\\\\components\\\\layout\\\\landing\\\\Header.tsx\",\n                            lineNumber: 18,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\rki\\\\frontend\\\\components\\\\layout\\\\landing\\\\Header.tsx\",\n                        lineNumber: 17,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Col_Row_react_bootstrap__WEBPACK_IMPORTED_MODULE_4__.Col, {\n                        sm: 9,\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                            className: \"quick-menu\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                        onClick: ()=>onScroll(0),\n                                        children: \"Home\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\rki\\\\frontend\\\\components\\\\layout\\\\landing\\\\Header.tsx\",\n                                        lineNumber: 23,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\rki\\\\frontend\\\\components\\\\layout\\\\landing\\\\Header.tsx\",\n                                    lineNumber: 22,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                        onClick: ()=>onScroll(1),\n                                        children: \"Our network\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\rki\\\\frontend\\\\components\\\\layout\\\\landing\\\\Header.tsx\",\n                                        lineNumber: 26,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\rki\\\\frontend\\\\components\\\\layout\\\\landing\\\\Header.tsx\",\n                                    lineNumber: 25,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                        onClick: ()=>onScroll(2),\n                                        children: \"About us\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\rki\\\\frontend\\\\components\\\\layout\\\\landing\\\\Header.tsx\",\n                                        lineNumber: 29,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\rki\\\\frontend\\\\components\\\\layout\\\\landing\\\\Header.tsx\",\n                                    lineNumber: 28,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                        onClick: ()=>onScroll(3),\n                                        children: \"Contact us\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\rki\\\\frontend\\\\components\\\\layout\\\\landing\\\\Header.tsx\",\n                                        lineNumber: 32,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\rki\\\\frontend\\\\components\\\\layout\\\\landing\\\\Header.tsx\",\n                                    lineNumber: 31,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_modals_layout_help__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                        show: false,\n                                        onHide: ()=>{}\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\rki\\\\frontend\\\\components\\\\layout\\\\landing\\\\Header.tsx\",\n                                        lineNumber: 35,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\rki\\\\frontend\\\\components\\\\layout\\\\landing\\\\Header.tsx\",\n                                    lineNumber: 34,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                    className: \"login\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                        href: \"/login\",\n                                        children: \"Login\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\rki\\\\frontend\\\\components\\\\layout\\\\landing\\\\Header.tsx\",\n                                        lineNumber: 38,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\rki\\\\frontend\\\\components\\\\layout\\\\landing\\\\Header.tsx\",\n                                    lineNumber: 37,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\rki\\\\frontend\\\\components\\\\layout\\\\landing\\\\Header.tsx\",\n                            lineNumber: 21,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\rki\\\\frontend\\\\components\\\\layout\\\\landing\\\\Header.tsx\",\n                        lineNumber: 20,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\rki\\\\frontend\\\\components\\\\layout\\\\landing\\\\Header.tsx\",\n                lineNumber: 16,\n                columnNumber: 7\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"D:\\\\rki\\\\frontend\\\\components\\\\layout\\\\landing\\\\Header.tsx\",\n            lineNumber: 15,\n            columnNumber: 5\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"D:\\\\rki\\\\frontend\\\\components\\\\layout\\\\landing\\\\Header.tsx\",\n        lineNumber: 14,\n        columnNumber: 3\n    }, undefined);\n};\n_c = Header;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (Header);\nvar _c;\n$RefreshReg$(_c, \"Header\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-browser)/./components/layout/landing/Header.tsx\n"));

/***/ }),

/***/ "(pages-dir-browser)/./components/layout/landing/Networks.tsx":
/*!************************************************!*\
  !*** ./components/layout/landing/Networks.tsx ***!
  \************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(pages-dir-browser)/./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(pages-dir-browser)/./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_Col_react_bootstrap__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Col!=!react-bootstrap */ \"(pages-dir-browser)/__barrel_optimize__?names=Col!=!./node_modules/react-bootstrap/esm/index.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(pages-dir-browser)/./node_modules/next/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _data_landing__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../../../data/landing */ \"(pages-dir-browser)/./data/landing.ts\");\n//Import Library\n\n\n\n\n//Import services/components\n\nconst Networks = ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Col_react_bootstrap__WEBPACK_IMPORTED_MODULE_4__.Col, {\n        sm: 8,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"block-title network\",\n                children: \"Our Networks\"\n            }, void 0, false, {\n                fileName: \"D:\\\\rki\\\\frontend\\\\components\\\\layout\\\\landing\\\\Networks.tsx\",\n                lineNumber: 11,\n                columnNumber: 5\n            }, undefined),\n            _data_landing__WEBPACK_IMPORTED_MODULE_3__.networkData.map((item, idx)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"network-news\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                            src: _data_landing__WEBPACK_IMPORTED_MODULE_3__.networkImages[idx]\n                        }, void 0, false, {\n                            fileName: \"D:\\\\rki\\\\frontend\\\\components\\\\layout\\\\landing\\\\Networks.tsx\",\n                            lineNumber: 14,\n                            columnNumber: 9\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"newsContent\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"newsTitle\",\n                                    children: item.title\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\rki\\\\frontend\\\\components\\\\layout\\\\landing\\\\Networks.tsx\",\n                                    lineNumber: 16,\n                                    columnNumber: 11\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"newsDesc\",\n                                    children: [\n                                        item.content,\n                                        item.href ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                            href: item.href,\n                                            target: \"_blank\",\n                                            children: \"Find more information...\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\rki\\\\frontend\\\\components\\\\layout\\\\landing\\\\Networks.tsx\",\n                                            lineNumber: 18,\n                                            columnNumber: 24\n                                        }, undefined) : null\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\rki\\\\frontend\\\\components\\\\layout\\\\landing\\\\Networks.tsx\",\n                                    lineNumber: 17,\n                                    columnNumber: 11\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\rki\\\\frontend\\\\components\\\\layout\\\\landing\\\\Networks.tsx\",\n                            lineNumber: 15,\n                            columnNumber: 9\n                        }, undefined)\n                    ]\n                }, idx, true, {\n                    fileName: \"D:\\\\rki\\\\frontend\\\\components\\\\layout\\\\landing\\\\Networks.tsx\",\n                    lineNumber: 13,\n                    columnNumber: 7\n                }, undefined))\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\rki\\\\frontend\\\\components\\\\layout\\\\landing\\\\Networks.tsx\",\n        lineNumber: 10,\n        columnNumber: 3\n    }, undefined);\n_c = Networks;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (Networks);\nvar _c;\n$RefreshReg$(_c, \"Networks\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-browser)/./components/layout/landing/Networks.tsx\n"));

/***/ }),

/***/ "(pages-dir-browser)/./components/layout/landing/NewsFeed.tsx":
/*!************************************************!*\
  !*** ./components/layout/landing/NewsFeed.tsx ***!
  \************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(pages-dir-browser)/./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(pages-dir-browser)/./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_Col_react_bootstrap__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=Col!=!react-bootstrap */ \"(pages-dir-browser)/__barrel_optimize__?names=Col!=!./node_modules/react-bootstrap/esm/index.js\");\n/* harmony import */ var _data_landing__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../../data/landing */ \"(pages-dir-browser)/./data/landing.ts\");\n//Import Library\n\n\n\n//Import services/components\n\nconst NewsFeed = ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Col_react_bootstrap__WEBPACK_IMPORTED_MODULE_3__.Col, {\n        sm: 4,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"block-title news-feeds\",\n                children: \"News Feeds\"\n            }, void 0, false, {\n                fileName: \"D:\\\\rki\\\\frontend\\\\components\\\\layout\\\\landing\\\\NewsFeed.tsx\",\n                lineNumber: 10,\n                columnNumber: 5\n            }, undefined),\n            _data_landing__WEBPACK_IMPORTED_MODULE_2__.newsData.map((item, idx)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"feed-news\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                            src: _data_landing__WEBPACK_IMPORTED_MODULE_2__.newsImages[idx]\n                        }, void 0, false, {\n                            fileName: \"D:\\\\rki\\\\frontend\\\\components\\\\layout\\\\landing\\\\NewsFeed.tsx\",\n                            lineNumber: 13,\n                            columnNumber: 9\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"newsContent\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"newsTitle\",\n                                    children: [\n                                        item.title,\n                                        \" \",\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: item.tag\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\rki\\\\frontend\\\\components\\\\layout\\\\landing\\\\NewsFeed.tsx\",\n                                            lineNumber: 15,\n                                            columnNumber: 51\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\rki\\\\frontend\\\\components\\\\layout\\\\landing\\\\NewsFeed.tsx\",\n                                    lineNumber: 15,\n                                    columnNumber: 11\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"newsDesc\",\n                                    children: item.content\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\rki\\\\frontend\\\\components\\\\layout\\\\landing\\\\NewsFeed.tsx\",\n                                    lineNumber: 16,\n                                    columnNumber: 11\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\rki\\\\frontend\\\\components\\\\layout\\\\landing\\\\NewsFeed.tsx\",\n                            lineNumber: 14,\n                            columnNumber: 9\n                        }, undefined)\n                    ]\n                }, idx, true, {\n                    fileName: \"D:\\\\rki\\\\frontend\\\\components\\\\layout\\\\landing\\\\NewsFeed.tsx\",\n                    lineNumber: 12,\n                    columnNumber: 7\n                }, undefined))\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\rki\\\\frontend\\\\components\\\\layout\\\\landing\\\\NewsFeed.tsx\",\n        lineNumber: 9,\n        columnNumber: 3\n    }, undefined);\n_c = NewsFeed;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (NewsFeed);\nvar _c;\n$RefreshReg$(_c, \"NewsFeed\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-browser)/./components/layout/landing/NewsFeed.tsx\n"));

/***/ }),

/***/ "(pages-dir-browser)/./components/layout/landing/Slider.tsx":
/*!**********************************************!*\
  !*** ./components/layout/landing/Slider.tsx ***!
  \**********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(pages-dir-browser)/./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(pages-dir-browser)/./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_Col_Row_react_bootstrap__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Col,Row!=!react-bootstrap */ \"(pages-dir-browser)/__barrel_optimize__?names=Col,Row!=!./node_modules/react-bootstrap/esm/index.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(pages-dir-browser)/./node_modules/next/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _data_landing__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../../../data/landing */ \"(pages-dir-browser)/./data/landing.ts\");\n//Import Library\n\n\n\n\n//Import services/components\n\nconst Slider = ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"slider\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Col_Row_react_bootstrap__WEBPACK_IMPORTED_MODULE_4__.Row, {\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Col_Row_react_bootstrap__WEBPACK_IMPORTED_MODULE_4__.Col, {\n                xs: 12,\n                children: _data_landing__WEBPACK_IMPORTED_MODULE_3__.sliderData.map((item, idx)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"myslider\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                src: \"images/home/<USER>",\n                                width: \"100%\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\rki\\\\frontend\\\\components\\\\layout\\\\landing\\\\Slider.tsx\",\n                                lineNumber: 15,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"sliderContent\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"sliderTitle\",\n                                        children: item.title\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\rki\\\\frontend\\\\components\\\\layout\\\\landing\\\\Slider.tsx\",\n                                        lineNumber: 17,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"sliderDesc\",\n                                        children: [\n                                            item.content,\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                href: item.href,\n                                                target: \"_blank\",\n                                                children: \"More Information\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\rki\\\\frontend\\\\components\\\\layout\\\\landing\\\\Slider.tsx\",\n                                                lineNumber: 19,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\rki\\\\frontend\\\\components\\\\layout\\\\landing\\\\Slider.tsx\",\n                                        lineNumber: 18,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\rki\\\\frontend\\\\components\\\\layout\\\\landing\\\\Slider.tsx\",\n                                lineNumber: 16,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, idx, true, {\n                        fileName: \"D:\\\\rki\\\\frontend\\\\components\\\\layout\\\\landing\\\\Slider.tsx\",\n                        lineNumber: 14,\n                        columnNumber: 11\n                    }, undefined))\n            }, void 0, false, {\n                fileName: \"D:\\\\rki\\\\frontend\\\\components\\\\layout\\\\landing\\\\Slider.tsx\",\n                lineNumber: 12,\n                columnNumber: 7\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"D:\\\\rki\\\\frontend\\\\components\\\\layout\\\\landing\\\\Slider.tsx\",\n            lineNumber: 11,\n            columnNumber: 5\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"D:\\\\rki\\\\frontend\\\\components\\\\layout\\\\landing\\\\Slider.tsx\",\n        lineNumber: 10,\n        columnNumber: 3\n    }, undefined);\n_c = Slider;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (Slider);\nvar _c;\n$RefreshReg$(_c, \"Slider\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-browser)/./components/layout/landing/Slider.tsx\n"));

/***/ }),

/***/ "(pages-dir-browser)/./components/layout/landing/Welcome.tsx":
/*!***********************************************!*\
  !*** ./components/layout/landing/Welcome.tsx ***!
  \***********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(pages-dir-browser)/./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(pages-dir-browser)/./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n//Import Library\n\n\nconst Welcome = ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"welcome\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"about-section\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"title\",\n                    children: \"Welcome to the new Knowledge Platform for international health protection\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\rki\\\\frontend\\\\components\\\\layout\\\\landing\\\\Welcome.tsx\",\n                    lineNumber: 7,\n                    columnNumber: 7\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    children: \"The purpose of the new Knowledge Platform is to enable German Institutions and partners, who work in the field of international health protection, to share information in order to strengthen our all’s capacity to respond and engage all partners effectively within this network. Non-commercial entities and institutions with an ability to support outbreak control and public health emergencies are welcome to the network. The site disseminates information concerning events, projects, operations, partner institutions and activities of the network. In addition, the platform also creates a forum for partners to collaborate through virtual spaces, including spaces created for specific purpose such as WHO Collaboration Centres or Emergency Medical Teams. The development of the platform was financed by the German Ministry of Health.\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\rki\\\\frontend\\\\components\\\\layout\\\\landing\\\\Welcome.tsx\",\n                    lineNumber: 8,\n                    columnNumber: 7\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\rki\\\\frontend\\\\components\\\\layout\\\\landing\\\\Welcome.tsx\",\n            lineNumber: 6,\n            columnNumber: 5\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"D:\\\\rki\\\\frontend\\\\components\\\\layout\\\\landing\\\\Welcome.tsx\",\n        lineNumber: 5,\n        columnNumber: 3\n    }, undefined);\n_c = Welcome;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (Welcome);\nvar _c;\n$RefreshReg$(_c, \"Welcome\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-browser)/./components/layout/landing/Welcome.tsx\n"));

/***/ }),

/***/ "(pages-dir-browser)/./components/layout/landing/index.tsx":
/*!*********************************************!*\
  !*** ./components/layout/landing/index.tsx ***!
  \*********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(pages-dir-browser)/./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(pages-dir-browser)/./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_Row_react_bootstrap__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Row!=!react-bootstrap */ \"(pages-dir-browser)/__barrel_optimize__?names=Row!=!./node_modules/react-bootstrap/esm/index.js\");\n/* harmony import */ var _Header__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./Header */ \"(pages-dir-browser)/./components/layout/landing/Header.tsx\");\n/* harmony import */ var _Slider__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./Slider */ \"(pages-dir-browser)/./components/layout/landing/Slider.tsx\");\n/* harmony import */ var _Welcome__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./Welcome */ \"(pages-dir-browser)/./components/layout/landing/Welcome.tsx\");\n/* harmony import */ var _Networks__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./Networks */ \"(pages-dir-browser)/./components/layout/landing/Networks.tsx\");\n/* harmony import */ var _NewsFeed__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./NewsFeed */ \"(pages-dir-browser)/./components/layout/landing/NewsFeed.tsx\");\n/* harmony import */ var _AboutUs__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./AboutUs */ \"(pages-dir-browser)/./components/layout/landing/AboutUs.tsx\");\n/* harmony import */ var _Footer__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./Footer */ \"(pages-dir-browser)/./components/layout/landing/Footer.tsx\");\n//Import Library\n\nvar _s = $RefreshSig$();\n\n\n//Import services/components\n\n\n\n\n\n\n\nconst Layout = ()=>{\n    _s();\n    const homeRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const networkRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const aboutUsRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const footerRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    /**\r\n   * Scrolls to the respective ref element\r\n   * @param index: number\r\n   */ const onScrollToElement = (index)=>{\n        const refs = [\n            homeRef,\n            networkRef,\n            aboutUsRef,\n            footerRef\n        ];\n        if (refs[index] && refs[index].current) {\n            const headerHeight = 100;\n            const topOfElement = window.pageYOffset + refs[index].current.getBoundingClientRect().top - headerHeight;\n            window.scrollTo({\n                behavior: \"smooth\",\n                top: topOfElement\n            });\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"landing\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Header__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                onScroll: onScrollToElement\n            }, void 0, false, {\n                fileName: \"D:\\\\rki\\\\frontend\\\\components\\\\layout\\\\landing\\\\index.tsx\",\n                lineNumber: 41,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"horizontal-line\"\n            }, void 0, false, {\n                fileName: \"D:\\\\rki\\\\frontend\\\\components\\\\layout\\\\landing\\\\index.tsx\",\n                lineNumber: 42,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"home\",\n                ref: homeRef,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Slider__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {}, void 0, false, {\n                    fileName: \"D:\\\\rki\\\\frontend\\\\components\\\\layout\\\\landing\\\\index.tsx\",\n                    lineNumber: 44,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"D:\\\\rki\\\\frontend\\\\components\\\\layout\\\\landing\\\\index.tsx\",\n                lineNumber: 43,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Welcome__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {}, void 0, false, {\n                fileName: \"D:\\\\rki\\\\frontend\\\\components\\\\layout\\\\landing\\\\index.tsx\",\n                lineNumber: 46,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                ref: networkRef,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Row_react_bootstrap__WEBPACK_IMPORTED_MODULE_9__.Row, {\n                    className: \"feeds-section\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Networks__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {}, void 0, false, {\n                            fileName: \"D:\\\\rki\\\\frontend\\\\components\\\\layout\\\\landing\\\\index.tsx\",\n                            lineNumber: 49,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_NewsFeed__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {}, void 0, false, {\n                            fileName: \"D:\\\\rki\\\\frontend\\\\components\\\\layout\\\\landing\\\\index.tsx\",\n                            lineNumber: 50,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\rki\\\\frontend\\\\components\\\\layout\\\\landing\\\\index.tsx\",\n                    lineNumber: 48,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"D:\\\\rki\\\\frontend\\\\components\\\\layout\\\\landing\\\\index.tsx\",\n                lineNumber: 47,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_AboutUs__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                innerRef: aboutUsRef\n            }, void 0, false, {\n                fileName: \"D:\\\\rki\\\\frontend\\\\components\\\\layout\\\\landing\\\\index.tsx\",\n                lineNumber: 53,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Footer__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                innerRef: footerRef\n            }, void 0, false, {\n                fileName: \"D:\\\\rki\\\\frontend\\\\components\\\\layout\\\\landing\\\\index.tsx\",\n                lineNumber: 54,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\rki\\\\frontend\\\\components\\\\layout\\\\landing\\\\index.tsx\",\n        lineNumber: 40,\n        columnNumber: 5\n    }, undefined);\n};\n_s(Layout, \"9nQF96/CROdIV9gsS/Jai3sULcw=\");\n_c = Layout;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (Layout);\nvar _c;\n$RefreshReg$(_c, \"Layout\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-browser)/./components/layout/landing/index.tsx\n"));

/***/ }),

/***/ "(pages-dir-browser)/./components/layout/modals/layout-help.tsx":
/*!**************************************************!*\
  !*** ./components/layout/modals/layout-help.tsx ***!
  \**************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ LayoutHelpModal)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(pages-dir-browser)/./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _barrel_optimize_names_Accordion_Modal_OverlayTrigger_Tooltip_react_bootstrap__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Accordion,Modal,OverlayTrigger,Tooltip!=!react-bootstrap */ \"(pages-dir-browser)/__barrel_optimize__?names=Accordion,Modal,OverlayTrigger,Tooltip!=!./node_modules/react-bootstrap/esm/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(pages-dir-browser)/./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _services_apiService__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../../services/apiService */ \"(pages-dir-browser)/./services/apiService.tsx\");\n/* harmony import */ var next_i18next__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next-i18next */ \"(pages-dir-browser)/./node_modules/next-i18next/dist/esm/index.js\");\n//Import Library\n\nvar _s = $RefreshSig$();\n\n\n//Import services/components\n\n\nfunction LayoutHelpModal(props) {\n    _s();\n    const _initialVal = {\n        title: '',\n        description: '',\n        pageCategory: '',\n        isEnabled: true\n    };\n    const [showHelpModal, setHelpModal] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const handleClose = ()=>setHelpModal(false);\n    const [, setHelp] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(_initialVal);\n    const createMarkup = (htmlContent)=>{\n        return {\n            __html: htmlContent\n        };\n    };\n    const { t } = (0,next_i18next__WEBPACK_IMPORTED_MODULE_3__.useTranslation)('common');\n    const fetchModalData = async ()=>{\n        const helpParams = {\n            query: {\n                pageCategory: ''\n            },\n            sort: {\n                title: \"asc\"\n            },\n            limit: \"~\"\n        };\n        const pageCategoryId = await fetchPageCategory();\n        if (pageCategoryId.length > 0) {\n            helpParams.query.pageCategory = pageCategoryId;\n            const response = await _services_apiService__WEBPACK_IMPORTED_MODULE_2__[\"default\"].get('/landingPage', helpParams);\n            if (Array.isArray(response.data) && response.data.length > 0) {\n                response.data[0].title = response && response.data[0].title.length > 0 && response.data[0].isEnabled === true ? response.data[0].title : defaultTitle;\n                response.data[0].description = response && response.data[0].description.length > 0 && response.data[0].isEnabled === true ? response.data[0].description : defaultDesc;\n                setHelp(response.data[0]);\n                fetchPageCategory();\n            }\n        }\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"LayoutHelpModal.useEffect\": ()=>{\n            fetchModalData();\n        }\n    }[\"LayoutHelpModal.useEffect\"], []);\n    const fetchPageCategory = async ()=>{\n        const response = await _services_apiService__WEBPACK_IMPORTED_MODULE_2__[\"default\"].get('/pagecategory', {\n            query: {\n                title: \"Help\"\n            }\n        });\n        if (response && response.data && response.data.length > 0) {\n            const pageCategoryId = response.data[0]._id;\n            return pageCategoryId;\n        }\n        return false;\n    };\n    const defaultDesc = \"The RKI Platform is accessible by invitation only to cooperation partner organisations in the field of Health Protection and their staff, through their organisations focal points. If your organisation is already part of the platform and you would like to join please check with your lead focal point as they will be able to add you. <br /><br />  If your organisation is not registered, but you would like to add your organization to our platform, you can email the team at  <EMAIL>.<br/><br/>  We ask that all users of the platform allow their name, title and email to be shared to facilitate communications across the network. We have data protection rules in place to protect against the misuse of data. For further information please click <a href='https://www.rki.de/DE/Service/Datenschutz/datenschutzerklaerung_node.html' target='_blank'>here</a><br /><br />Thank you for your interest.<br/>Kind regards,\";\n    const defaultTitle = \"How do I access the RKI Platform?\";\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                onClick: ()=>setHelpModal(true),\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_Modal_OverlayTrigger_Tooltip_react_bootstrap__WEBPACK_IMPORTED_MODULE_4__.OverlayTrigger, {\n                    placement: \"bottom\",\n                    delay: {\n                        show: 250,\n                        hide: 400\n                    },\n                    overlay: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_Modal_OverlayTrigger_Tooltip_react_bootstrap__WEBPACK_IMPORTED_MODULE_4__.Tooltip, {\n                        id: \"print-tooltip\",\n                        children: t(\"Help\")\n                    }, void 0, false, {\n                        fileName: \"D:\\\\rki\\\\frontend\\\\components\\\\layout\\\\modals\\\\layout-help.tsx\",\n                        lineNumber: 80,\n                        columnNumber: 20\n                    }, void 0),\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                        className: \"fas fa-question-circle\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\rki\\\\frontend\\\\components\\\\layout\\\\modals\\\\layout-help.tsx\",\n                        lineNumber: 81,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\rki\\\\frontend\\\\components\\\\layout\\\\modals\\\\layout-help.tsx\",\n                    lineNumber: 77,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\rki\\\\frontend\\\\components\\\\layout\\\\modals\\\\layout-help.tsx\",\n                lineNumber: 76,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_Modal_OverlayTrigger_Tooltip_react_bootstrap__WEBPACK_IMPORTED_MODULE_4__.Modal, {\n                size: \"lg\",\n                show: showHelpModal,\n                onHide: handleClose,\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_Modal_OverlayTrigger_Tooltip_react_bootstrap__WEBPACK_IMPORTED_MODULE_4__.Modal.Header, {\n                        closeButton: true,\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_Modal_OverlayTrigger_Tooltip_react_bootstrap__WEBPACK_IMPORTED_MODULE_4__.Modal.Title, {\n                            id: \"help-modal\",\n                            children: t(\"Help\")\n                        }, void 0, false, {\n                            fileName: \"D:\\\\rki\\\\frontend\\\\components\\\\layout\\\\modals\\\\layout-help.tsx\",\n                            lineNumber: 84,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\rki\\\\frontend\\\\components\\\\layout\\\\modals\\\\layout-help.tsx\",\n                        lineNumber: 83,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_Modal_OverlayTrigger_Tooltip_react_bootstrap__WEBPACK_IMPORTED_MODULE_4__.Modal.Body, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_Modal_OverlayTrigger_Tooltip_react_bootstrap__WEBPACK_IMPORTED_MODULE_4__.Accordion, {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_Modal_OverlayTrigger_Tooltip_react_bootstrap__WEBPACK_IMPORTED_MODULE_4__.Accordion.Item, {\n                                    eventKey: \"0\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            className: \"help-content-li-title\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_Modal_OverlayTrigger_Tooltip_react_bootstrap__WEBPACK_IMPORTED_MODULE_4__.Accordion.Header, {\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    dangerouslySetInnerHTML: createMarkup(defaultTitle)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\rki\\\\frontend\\\\components\\\\layout\\\\modals\\\\layout-help.tsx\",\n                                                    lineNumber: 94,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\rki\\\\frontend\\\\components\\\\layout\\\\modals\\\\layout-help.tsx\",\n                                                lineNumber: 93,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\rki\\\\frontend\\\\components\\\\layout\\\\modals\\\\layout-help.tsx\",\n                                            lineNumber: 92,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_Modal_OverlayTrigger_Tooltip_react_bootstrap__WEBPACK_IMPORTED_MODULE_4__.Accordion.Body, {\n                                            className: \"help-content-li-content\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                dangerouslySetInnerHTML: createMarkup(defaultDesc)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\rki\\\\frontend\\\\components\\\\layout\\\\modals\\\\layout-help.tsx\",\n                                                lineNumber: 100,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\rki\\\\frontend\\\\components\\\\layout\\\\modals\\\\layout-help.tsx\",\n                                            lineNumber: 99,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\rki\\\\frontend\\\\components\\\\layout\\\\modals\\\\layout-help.tsx\",\n                                    lineNumber: 91,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\rki\\\\frontend\\\\components\\\\layout\\\\modals\\\\layout-help.tsx\",\n                                lineNumber: 90,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\rki\\\\frontend\\\\components\\\\layout\\\\modals\\\\layout-help.tsx\",\n                            lineNumber: 89,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\rki\\\\frontend\\\\components\\\\layout\\\\modals\\\\layout-help.tsx\",\n                        lineNumber: 88,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\rki\\\\frontend\\\\components\\\\layout\\\\modals\\\\layout-help.tsx\",\n                lineNumber: 82,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\rki\\\\frontend\\\\components\\\\layout\\\\modals\\\\layout-help.tsx\",\n        lineNumber: 75,\n        columnNumber: 5\n    }, this);\n}\n_s(LayoutHelpModal, \"8jJzeQruIpeorz9OCAiTht6ZZvg=\", false, function() {\n    return [\n        next_i18next__WEBPACK_IMPORTED_MODULE_3__.useTranslation\n    ];\n});\n_c = LayoutHelpModal;\nvar _c;\n$RefreshReg$(_c, \"LayoutHelpModal\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************\n//# sourceURL=webpack-internal:///(pages-dir-browser)/./components/layout/modals/layout-help.tsx\n"));

/***/ }),

/***/ "(pages-dir-browser)/./data/landing.ts":
/*!*************************!*\
  !*** ./data/landing.ts ***!
  \*************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   networkData: () => (/* binding */ networkData),\n/* harmony export */   networkImages: () => (/* binding */ networkImages),\n/* harmony export */   newsData: () => (/* binding */ newsData),\n/* harmony export */   newsImages: () => (/* binding */ newsImages),\n/* harmony export */   sliderData: () => (/* binding */ sliderData)\n/* harmony export */ });\nconst networkImages = {\n    0: \"images/home/<USER>",\n    1: \"images/home/<USER>",\n    2: \"images/home/<USER>",\n    3: \"images/home/<USER>",\n    4: \"images/home/<USER>",\n    5: \"images/home/<USER>",\n    6: \"images/home/<USER>",\n    7: \"images/home/<USER>",\n    8: \"images/home/<USER>"\n};\nconst newsImages = {\n    0: \"images/home/<USER>",\n    1: \"images/home/<USER>",\n    2: \"images/home/<USER>",\n    3: \"images/home/<USER>",\n    4: \"images/home/<USER>"\n};\nconst sliderData = [\n    {\n        title: \"SARS-CoV-2 in Germany\",\n        content: \"After a temporary stabilisation of case numbers at a higher level in late August and early September 2020, a steep increase in case numbers ensued in October in all federal states. Due to measures implemented at the beginning of November the rise in cases could be stopped, albeit no considerable reduction in case numbers ensued. Since 04/12/2020 case numbers have been sharply increasing again... \",\n        href: \"https://www.rki.de/EN/Content/infections/epidemiology/outbreaks/COVID-19/Situationsberichte_Tab.html;jsessionid=0D6329A535C4B9D303C461108C27A545.internet051\",\n        image: 'images/home/<USER>'\n    }\n];\nconst networkData = [\n    {\n        title: \"The Robert Koch Institut as international hub for health protection networks\",\n        content: \"When there are health emergencies across the world, such as disease outbreaks, the Robert Koch Institut’s expertise is in ever greater demand. RKI staff are involved in various international research projects and programmes. Thus, the RKI helps to tackle urgent public health problems and improve people’s health worldwide. \",\n        href: \"https://www.rki.de/EN/Content/Institute/International/international_activities_node.html\"\n    },\n    {\n        title: \"About the Global Health Protection Programme\",\n        content: \"As part of its international commitments, Germany is providing  increasing support to partner countries for the management of outbreaks and the development of reliable heafthcare systems. To this end, the German Federal Ministry of Health has launched a Global Health​ Protection Programme to improve and promote health at global scale. \",\n        href: \"https://ghpp.de/en/\"\n    },\n    {\n        title: \"Global Health Policy\",\n        content: \"Global health issues are closely related to several other policy areas such as development, security, trade and travel, the economy, human rights, nutrition, agriculture, research, employment, education, migration, the environment and climate protection, as well as humanitarian aid. \",\n        href: \"https://www.bundesgesundheitsministerium.de/en/international-co-operation.html\"\n    },\n    {\n        title: \"German Biosecurity Programme\",\n        content: \"The German Biosecurity, founded by the Federal Foreign Office, is intended to help partner countries tackle biosecurity threats, such as the intentional misuse of biological pathogens and toxins or outbreaks of highly pathogenic diseases and pandemics. The aim is to strengthen the health services of our partner countries in Africa, Central Asia and Eastern Europe, thus enhancing their national security. \",\n        href: \"https://www.auswaertiges-amt.de/en/aussenpolitik/themen/abruestung/uebersicht-bcwaffen-node/-/239362\"\n    },\n    {\n        title: \"Emergency Mobile Laboratory\",\n        content: \"The European Mobile Laboratory (EMLab) Consortium deploy state of the art boxed field laboratories as well as trained scientists and technicians to epidemics and outbreaks of infectious diseases caused by pathogens up to risk group 4 to perform diagnostics and supporting clinical laboratory analysis on site. \",\n        href: \"https://www.emlab.eu/\"\n    },\n    {\n        title: \"Emergency Medical Teams\",\n        content: \"The purpose of the Emergency Medical Teams initiative is to improve the timeliness and quality of health services provided by national and international EMTs and enhance the capacity of national health systems in leading the activation and coordination of this response in the immediate aftermath of a disaster, outbreak and/or other emergency. \",\n        href: \"https://extranet.who.int/emt/\"\n    },\n    {\n        title: \"Over 800 institutions in over 80 countries supporting WHO programmes\",\n        content: \"WHO collaborating centres are institutions such as research institutes, parts of universities or academies, which are designated by the Director-General to carry out activities in support of the Organization's programmes. Currently there are 24 from over 800 WHO collaborating centres in Germany. \",\n        href: \"https://www.who.int/about/who-we-are/structure/collaborating-centres\"\n    },\n    {\n        title: \"Global Outbreak Alert and Response Network (GOARN) \",\n        content: \"GOARN is a collaboration of existing institutions and networks, constantly alert and ready to respond. The network pools human and technical resources for rapid identification, confirmation and response to outbreaks of international importance. \",\n        href: \"https://extranet.who.int/goarn\"\n    },\n    {\n        title: \"International Association of National Public Health Institutes\",\n        content: \"The International Association of National Public Health Institutes is a member organization of government agencies working to improve national disease prevention and response. IANPHI is made up of 100+ members, located in approximately 90 countries. \"\n    }\n];\nconst newsData = [\n    {\n        title: \"PEI_Germany\",\n        tag: \"@PEI_Germany\",\n        content: \"Exten­sively drug-resis­tant Kleb­siella pneu­moniae out­break, north-eastern Ger­many, 2019 (Euro­surveillance, 12.12.2019)\"\n    },\n    {\n        title: \"BMG_Bund\",\n        tag: \"@BMG_Bund\",\n        content: \"Angesichts der Entwicklung in Italien rechnet Bundesgesundheitsminister Jens Spahn damit, dass sich das Coronavirus auch in Deutschland\"\n    },\n    {\n        title: \"Bfarm_de\",\n        tag: \"@bfarm_de\",\n        content: \"Was genau macht eigentlich das BfArM? Knapp vier Minuten Film geben Einblicke in unsere Aufgaben, unseren Arbeitsalltag und unsere Motivation.\"\n    },\n    {\n        title: \"FZBorstel\",\n        tag: \"@FZBorstel\",\n        content: \"The Research Center Borstel, Germany is hosting the next #lipidomics forum! Please check it out, share, spread the word, retweet, register!\"\n    },\n    {\n        title: \"Loeffler_News\",\n        tag: \"@Loeffler_News\",\n        content: \"With the current cases in Poland, #AfricanSwineFever made a westerly leap of about 250 km and has now moved about 80 km from the German border.\"\n    }\n];\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-browser)/./data/landing.ts\n"));

/***/ }),

/***/ "(pages-dir-browser)/./node_modules/next/dist/build/webpack/loaders/next-client-pages-loader.js?absolutePagePath=D%3A%5Crki%5Cfrontend%5Cpages%5Chome%5Cindex.tsx&page=%2Fhome!":
/*!******************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-client-pages-loader.js?absolutePagePath=D%3A%5Crki%5Cfrontend%5Cpages%5Chome%5Cindex.tsx&page=%2Fhome! ***!
  \******************************************************************************************************************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval(__webpack_require__.ts("\n    (window.__NEXT_P = window.__NEXT_P || []).push([\n      \"/home\",\n      function () {\n        return __webpack_require__(/*! ./pages/home/<USER>/ \"(pages-dir-browser)/./pages/home/<USER>");\n      }\n    ]);\n    if(true) {\n      module.hot.dispose(function () {\n        window.__NEXT_P.push([\"/home\"])\n      });\n    }\n  //# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHBhZ2VzLWRpci1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvYnVpbGQvd2VicGFjay9sb2FkZXJzL25leHQtY2xpZW50LXBhZ2VzLWxvYWRlci5qcz9hYnNvbHV0ZVBhZ2VQYXRoPUQlM0ElNUNya2klNUNmcm9udGVuZCU1Q3BhZ2VzJTVDaG9tZSU1Q2luZGV4LnRzeCZwYWdlPSUyRmhvbWUhIiwibWFwcGluZ3MiOiI7QUFDQTtBQUNBO0FBQ0E7QUFDQSxlQUFlLG1CQUFPLENBQUMsMEVBQXdCO0FBQy9DO0FBQ0E7QUFDQSxPQUFPLElBQVU7QUFDakIsTUFBTSxVQUFVO0FBQ2hCO0FBQ0EsT0FBTztBQUNQO0FBQ0EiLCJzb3VyY2VzIjpbIiJdLCJzb3VyY2VzQ29udGVudCI6WyJcbiAgICAod2luZG93Ll9fTkVYVF9QID0gd2luZG93Ll9fTkVYVF9QIHx8IFtdKS5wdXNoKFtcbiAgICAgIFwiL2hvbWVcIixcbiAgICAgIGZ1bmN0aW9uICgpIHtcbiAgICAgICAgcmV0dXJuIHJlcXVpcmUoXCIuL3BhZ2VzL2hvbWUvaW5kZXgudHN4XCIpO1xuICAgICAgfVxuICAgIF0pO1xuICAgIGlmKG1vZHVsZS5ob3QpIHtcbiAgICAgIG1vZHVsZS5ob3QuZGlzcG9zZShmdW5jdGlvbiAoKSB7XG4gICAgICAgIHdpbmRvdy5fX05FWFRfUC5wdXNoKFtcIi9ob21lXCJdKVxuICAgICAgfSk7XG4gICAgfVxuICAiXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(pages-dir-browser)/./node_modules/next/dist/build/webpack/loaders/next-client-pages-loader.js?absolutePagePath=D%3A%5Crki%5Cfrontend%5Cpages%5Chome%5Cindex.tsx&page=%2Fhome!\n"));

/***/ }),

/***/ "(pages-dir-browser)/./pages/home/<USER>":
/*!******************************!*\
  !*** ./pages/home/<USER>
  \******************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   __N_SSG: () => (/* binding */ __N_SSG),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(pages-dir-browser)/./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(pages-dir-browser)/./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_layout_landing__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../components/layout/landing */ \"(pages-dir-browser)/./components/layout/landing/index.tsx\");\n//Import Library\n\n\n//Import services/components\n\nconst Landings = ()=>{\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_layout_landing__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {}, void 0, false, {\n        fileName: \"D:\\\\rki\\\\frontend\\\\pages\\\\home\\\\index.tsx\",\n        lineNumber: 10,\n        columnNumber: 10\n    }, undefined);\n};\n_c = Landings;\nvar __N_SSG = true;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (Landings);\nvar _c;\n$RefreshReg$(_c, \"Landings\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHBhZ2VzLWRpci1icm93c2VyKS8uL3BhZ2VzL2hvbWUvaW5kZXgudHN4IiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7QUFBQSxnQkFBZ0I7O0FBQ1U7QUFFMUIsNEJBQTRCO0FBQzBCO0FBSXRELE1BQU1FLFdBQVc7SUFDZixxQkFBTyw4REFBQ0Qsa0VBQU9BOzs7OztBQUNqQjtLQUZNQzs7QUFZTixpRUFBZUEsUUFBUUEsRUFBQyIsInNvdXJjZXMiOlsiRDpcXHJraVxcZnJvbnRlbmRcXHBhZ2VzXFxob21lXFxpbmRleC50c3giXSwic291cmNlc0NvbnRlbnQiOlsiLy9JbXBvcnQgTGlicmFyeVxyXG5pbXBvcnQgUmVhY3QgZnJvbSBcInJlYWN0XCI7XHJcblxyXG4vL0ltcG9ydCBzZXJ2aWNlcy9jb21wb25lbnRzXHJcbmltcG9ydCBMYW5kaW5nIGZyb20gXCIuLi8uLi9jb21wb25lbnRzL2xheW91dC9sYW5kaW5nXCI7XHJcbmltcG9ydCB7IHNlcnZlclNpZGVUcmFuc2xhdGlvbnMgfSBmcm9tICduZXh0LWkxOG5leHQvc2VydmVyU2lkZVRyYW5zbGF0aW9ucyc7XHJcbmltcG9ydCB7IEdldFN0YXRpY1Byb3BzQ29udGV4dCwgR2V0U3RhdGljUHJvcHMgfSBmcm9tICduZXh0J1xyXG5cclxuY29uc3QgTGFuZGluZ3MgPSAoKSA9PiB7XHJcbiAgcmV0dXJuIDxMYW5kaW5nIC8+O1xyXG59O1xyXG5leHBvcnQgY29uc3QgZ2V0U3RhdGljUHJvcHM6IEdldFN0YXRpY1Byb3BzID0gYXN5bmMgKGNvbnRleHQ6IEdldFN0YXRpY1Byb3BzQ29udGV4dCkgPT4ge1xyXG4gIGNvbnN0IGxvY2FsZSA9IGNvbnRleHQubG9jYWxlIHx8ICdlbidcclxuXHJcbiAgcmV0dXJuIHtcclxuICAgIHByb3BzOiB7XHJcbiAgICAgIC4uLihhd2FpdCBzZXJ2ZXJTaWRlVHJhbnNsYXRpb25zKGxvY2FsZSwgWydjb21tb24nXSkpLFxyXG4gICAgfSxcclxuICB9XHJcbn1cclxuZXhwb3J0IGRlZmF1bHQgTGFuZGluZ3M7XHJcbiJdLCJuYW1lcyI6WyJSZWFjdCIsIkxhbmRpbmciLCJMYW5kaW5ncyJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(pages-dir-browser)/./pages/home/<USER>"));

/***/ }),

/***/ "(pages-dir-browser)/__barrel_optimize__?names=Accordion,Modal,OverlayTrigger,Tooltip!=!./node_modules/react-bootstrap/esm/index.js":
/*!**********************************************************************************************************************!*\
  !*** __barrel_optimize__?names=Accordion,Modal,OverlayTrigger,Tooltip!=!./node_modules/react-bootstrap/esm/index.js ***!
  \**********************************************************************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Accordion: () => (/* reexport safe */ _Accordion__WEBPACK_IMPORTED_MODULE_0__[\"default\"]),\n/* harmony export */   Modal: () => (/* reexport safe */ _Modal__WEBPACK_IMPORTED_MODULE_1__[\"default\"]),\n/* harmony export */   OverlayTrigger: () => (/* reexport safe */ _OverlayTrigger__WEBPACK_IMPORTED_MODULE_2__[\"default\"]),\n/* harmony export */   Tooltip: () => (/* reexport safe */ _Tooltip__WEBPACK_IMPORTED_MODULE_3__[\"default\"])\n/* harmony export */ });\n/* harmony import */ var _Accordion__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./Accordion */ \"(pages-dir-browser)/./node_modules/react-bootstrap/esm/Accordion.js\");\n/* harmony import */ var _Modal__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./Modal */ \"(pages-dir-browser)/./node_modules/react-bootstrap/esm/Modal.js\");\n/* harmony import */ var _OverlayTrigger__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./OverlayTrigger */ \"(pages-dir-browser)/./node_modules/react-bootstrap/esm/OverlayTrigger.js\");\n/* harmony import */ var _Tooltip__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./Tooltip */ \"(pages-dir-browser)/./node_modules/react-bootstrap/esm/Tooltip.js\");\n\n\n\n\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHBhZ2VzLWRpci1icm93c2VyKS9fX2JhcnJlbF9vcHRpbWl6ZV9fP25hbWVzPUFjY29yZGlvbixNb2RhbCxPdmVybGF5VHJpZ2dlcixUb29sdGlwIT0hLi9ub2RlX21vZHVsZXMvcmVhY3QtYm9vdHN0cmFwL2VzbS9pbmRleC5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7OztBQUNrRDtBQUNSO0FBQ2tCO0FBQ2QiLCJzb3VyY2VzIjpbIkQ6XFxya2lcXGZyb250ZW5kXFxub2RlX21vZHVsZXNcXHJlYWN0LWJvb3RzdHJhcFxcZXNtXFxpbmRleC5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJcbmV4cG9ydCB7IGRlZmF1bHQgYXMgQWNjb3JkaW9uIH0gZnJvbSBcIi4vQWNjb3JkaW9uXCJcbmV4cG9ydCB7IGRlZmF1bHQgYXMgTW9kYWwgfSBmcm9tIFwiLi9Nb2RhbFwiXG5leHBvcnQgeyBkZWZhdWx0IGFzIE92ZXJsYXlUcmlnZ2VyIH0gZnJvbSBcIi4vT3ZlcmxheVRyaWdnZXJcIlxuZXhwb3J0IHsgZGVmYXVsdCBhcyBUb29sdGlwIH0gZnJvbSBcIi4vVG9vbHRpcFwiIl0sIm5hbWVzIjpbImRlZmF1bHQiLCJBY2NvcmRpb24iLCJNb2RhbCIsIk92ZXJsYXlUcmlnZ2VyIiwiVG9vbHRpcCJdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(pages-dir-browser)/__barrel_optimize__?names=Accordion,Modal,OverlayTrigger,Tooltip!=!./node_modules/react-bootstrap/esm/index.js\n"));

/***/ }),

/***/ "(pages-dir-browser)/__barrel_optimize__?names=Col!=!./node_modules/react-bootstrap/esm/index.js":
/*!***********************************************************************************!*\
  !*** __barrel_optimize__?names=Col!=!./node_modules/react-bootstrap/esm/index.js ***!
  \***********************************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Col: () => (/* reexport safe */ _Col__WEBPACK_IMPORTED_MODULE_0__[\"default\"])\n/* harmony export */ });\n/* harmony import */ var _Col__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./Col */ \"(pages-dir-browser)/./node_modules/react-bootstrap/esm/Col.js\");\n\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHBhZ2VzLWRpci1icm93c2VyKS9fX2JhcnJlbF9vcHRpbWl6ZV9fP25hbWVzPUNvbCE9IS4vbm9kZV9tb2R1bGVzL3JlYWN0LWJvb3RzdHJhcC9lc20vaW5kZXguanMiLCJtYXBwaW5ncyI6Ijs7Ozs7QUFDc0MiLCJzb3VyY2VzIjpbIkQ6XFxya2lcXGZyb250ZW5kXFxub2RlX21vZHVsZXNcXHJlYWN0LWJvb3RzdHJhcFxcZXNtXFxpbmRleC5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJcbmV4cG9ydCB7IGRlZmF1bHQgYXMgQ29sIH0gZnJvbSBcIi4vQ29sXCIiXSwibmFtZXMiOlsiZGVmYXVsdCIsIkNvbCJdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(pages-dir-browser)/__barrel_optimize__?names=Col!=!./node_modules/react-bootstrap/esm/index.js\n"));

/***/ }),

/***/ "(pages-dir-browser)/__barrel_optimize__?names=Col,Row!=!./node_modules/react-bootstrap/esm/index.js":
/*!***************************************************************************************!*\
  !*** __barrel_optimize__?names=Col,Row!=!./node_modules/react-bootstrap/esm/index.js ***!
  \***************************************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Col: () => (/* reexport safe */ _Col__WEBPACK_IMPORTED_MODULE_0__[\"default\"]),\n/* harmony export */   Row: () => (/* reexport safe */ _Row__WEBPACK_IMPORTED_MODULE_1__[\"default\"])\n/* harmony export */ });\n/* harmony import */ var _Col__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./Col */ \"(pages-dir-browser)/./node_modules/react-bootstrap/esm/Col.js\");\n/* harmony import */ var _Row__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./Row */ \"(pages-dir-browser)/./node_modules/react-bootstrap/esm/Row.js\");\n\n\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHBhZ2VzLWRpci1icm93c2VyKS9fX2JhcnJlbF9vcHRpbWl6ZV9fP25hbWVzPUNvbCxSb3chPSEuL25vZGVfbW9kdWxlcy9yZWFjdC1ib290c3RyYXAvZXNtL2luZGV4LmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7QUFDc0M7QUFDQSIsInNvdXJjZXMiOlsiRDpcXHJraVxcZnJvbnRlbmRcXG5vZGVfbW9kdWxlc1xccmVhY3QtYm9vdHN0cmFwXFxlc21cXGluZGV4LmpzIl0sInNvdXJjZXNDb250ZW50IjpbIlxuZXhwb3J0IHsgZGVmYXVsdCBhcyBDb2wgfSBmcm9tIFwiLi9Db2xcIlxuZXhwb3J0IHsgZGVmYXVsdCBhcyBSb3cgfSBmcm9tIFwiLi9Sb3dcIiJdLCJuYW1lcyI6WyJkZWZhdWx0IiwiQ29sIiwiUm93Il0sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(pages-dir-browser)/__barrel_optimize__?names=Col,Row!=!./node_modules/react-bootstrap/esm/index.js\n"));

/***/ }),

/***/ "(pages-dir-browser)/__barrel_optimize__?names=Row!=!./node_modules/react-bootstrap/esm/index.js":
/*!***********************************************************************************!*\
  !*** __barrel_optimize__?names=Row!=!./node_modules/react-bootstrap/esm/index.js ***!
  \***********************************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Row: () => (/* reexport safe */ _Row__WEBPACK_IMPORTED_MODULE_0__[\"default\"])\n/* harmony export */ });\n/* harmony import */ var _Row__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./Row */ \"(pages-dir-browser)/./node_modules/react-bootstrap/esm/Row.js\");\n\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHBhZ2VzLWRpci1icm93c2VyKS9fX2JhcnJlbF9vcHRpbWl6ZV9fP25hbWVzPVJvdyE9IS4vbm9kZV9tb2R1bGVzL3JlYWN0LWJvb3RzdHJhcC9lc20vaW5kZXguanMiLCJtYXBwaW5ncyI6Ijs7Ozs7QUFDc0MiLCJzb3VyY2VzIjpbIkQ6XFxya2lcXGZyb250ZW5kXFxub2RlX21vZHVsZXNcXHJlYWN0LWJvb3RzdHJhcFxcZXNtXFxpbmRleC5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJcbmV4cG9ydCB7IGRlZmF1bHQgYXMgUm93IH0gZnJvbSBcIi4vUm93XCIiXSwibmFtZXMiOlsiZGVmYXVsdCIsIlJvdyJdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(pages-dir-browser)/__barrel_optimize__?names=Row!=!./node_modules/react-bootstrap/esm/index.js\n"));

/***/ })

},
/******/ __webpack_require__ => { // webpackRuntimeModules
/******/ var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
/******/ __webpack_require__.O(0, ["pages/_app","main"], () => (__webpack_exec__("(pages-dir-browser)/./node_modules/next/dist/build/webpack/loaders/next-client-pages-loader.js?absolutePagePath=D%3A%5Crki%5Cfrontend%5Cpages%5Chome%5Cindex.tsx&page=%2Fhome!")));
/******/ var __webpack_exports__ = __webpack_require__.O();
/******/ _N_E = __webpack_exports__;
/******/ }
]);