{"version": 3, "file": "static/chunks/pages/adminsettings/operationstatuses/operationstatusTable-6f3e5549eca92565.js", "mappings": "+EACA,4CACA,wDACA,WACA,OAAe,EAAQ,KAA6E,CACpG,EACA,SAFsB,oGCiCtB,SAASA,EAASC,CAAoB,EACpC,GAAM,GAAEC,CAAC,CAAE,CAAGC,CAAAA,EAAAA,EAAAA,EAAAA,CAAcA,CAAC,UACvBC,EAA6B,CACjCC,gBAAiBH,EAAE,cACnB,EACI,SACJI,CAAO,MACPC,CAAI,WACJC,CAAS,uBACTC,CAAqB,WACrBC,CAAS,CACTC,oBAAkB,qBAClBC,CAAmB,kBACnBC,CAAgB,aAChBC,CAAW,oBACXC,CAAkB,gBAClBC,CAAc,CACdC,SAAO,WACPC,CAAS,sBACTC,CAAoB,mBACpBC,CAAiB,YACjBC,CAAU,QACVC,CAAM,kBACNC,CAAgB,cAChBC,CAAY,CAEZ,CADA,EACGC,EACJ,CAAGxB,EAGEyB,EAAiB,4BACrBtB,EACAuB,gBAAiBzB,EAAE,IAP0C,MAQ7D0B,UAAU,UACVtB,EACAC,KAAMA,GAAQ,EAAE,CAChBsB,OAAO,EACPC,2BAA4BrB,EAC5BsB,UAAWrB,EACXsB,gBAAiBf,EACjBN,qBACAsB,YAAY,EACZC,iBAAkBhB,EAClBiB,kBAAmBpB,GAA0C,GAC7DqB,eADwCrB,cACVD,GAA4B,CAAC,GAAI,GAAI,GAAI,CAA3BA,EAA+B,GAAG,CAC9EuB,oBAAqB7B,EACrB8B,oBAAqB1B,EACrB2B,aAAc1B,iBACdG,uBACAG,oBACAC,EACAoB,kBAAmB,UAACC,EAAAA,CAAYA,CAAAA,CAAAA,GAChCC,SAAU,UAACC,IAAAA,CAAEC,UAAU,6CACvBvB,SACAC,eACAE,mBACAD,EACAqB,UAAW,WACb,EACA,MACE,UAACC,EAAAA,EAASA,CAAAA,CAAE,GAAGnB,CAAc,EAEjC,CAEA1B,EAAS8C,YAAY,CAAG,CACtBf,WAAW,EACXE,YAAY,EACZzB,UAAW,KACXU,WAAW,EACXC,qBAAsB,KACtBC,mBAAmB,EACnBC,WAAY,GACZE,iBAAkB,EACpB,EAEA,MAAevB,QAAQA,EAAC,oKCmBxB,MAvH6B,IACzB,GAAM,CAAC+C,EAAWC,EAAe,CAAGC,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAsHjCC,EAtHoC,EACzC,EAAGC,EAAW,CAAGF,CAAAA,EAAAA,EAAAA,MAqHQC,EArHRD,CAAQA,EAAC,GAC1B,CAACzC,EAAW4C,EAAa,CAAGH,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAC,GACrC,CAACI,EAASC,EAAW,CAAGL,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAC,IACjC,CAACM,EAAaC,EAAS,CAAGP,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAC,IACnC,CAACQ,EAAuBC,EAAyB,CAAGT,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAC,CAAC,GAC9D,GAAE/C,CAAC,CAAE,CAAGC,CAAAA,EAAAA,EAAAA,EAAAA,CAAcA,CAAC,UAEvBwD,EAAwB,CAC1BC,KAAM,CAAEC,MAAO,KAAM,EACrBC,MAAOT,EACPU,KAAM,EACNC,MAAO,CAAC,CACZ,EAEM1D,EAAU,CACZ,CACI2D,KAAM/D,EAAE,SACRgE,SAAU,OACd,EACA,CACID,KAAM/D,EAAE,UACRgE,SAAU,GACVC,KAAM,GACF,WAACC,MAAAA,WACG,UAACC,IAAIA,CAACC,KAAK,6BAA6BC,GAAI,OAAvCF,gCAAoF,OAANG,EAAEC,GAAG,WAEpF,UAAC9B,IAAAA,CAAEC,UAAU,uBAEV,OAEP,UAAC8B,IAAAA,CAAEC,QAAS,IAAMC,EAAWJ,YACzB,UAAC7B,IAAAA,CAAEC,UAAU,4BACZ,MAGjB,EACH,CAEKiC,EAAyB,UAC3B1B,GAAW,GACX,IAAM2B,EAAW,MAAMC,EAAAA,CAAUA,CAACC,GAAG,CAAC,oBAAqBrB,GACvDmB,GAAYA,EAASvE,IAAI,EAAIuE,EAASvE,IAAI,CAAC0E,MAAM,CAAG,GAAG,CACvDjC,EAAe8B,EAASvE,IAAI,EAC5B6C,EAAa0B,EAASI,UAAU,EAChC/B,GAAW,GAEnB,EAQMvC,EAAsB,MAAOuE,EAAiBpB,KAChDJ,EAAsBG,KAAK,CAAGqB,EAC9BxB,EAAsBI,IAAI,CAAGA,EAC7BZ,GAAW,GACX,IAAM2B,EAAW,MAAMC,EAAAA,CAAUA,CAACC,GAAG,CAAC,oBAAqBrB,GACvDmB,GAAYA,EAASvE,IAAI,EAAIuE,EAASvE,IAAI,CAAC0E,MAAM,CAAG,GAAG,CACvDjC,EAAe8B,EAASvE,IAAI,EAC5B+C,EAAW6B,GACXhC,GAAW,GAEnB,EAEMyB,EAAa,MAAOQ,IACtB1B,EAAyB0B,EAAIX,GAAG,EAChCjB,GAAS,EACb,EAEM6B,EAAe,UACjB,GAAI,CACA,MAAMN,EAAAA,CAAUA,CAACO,MAAM,CAAC,qBAA2C,OAAtB7B,IAC7CoB,IACArB,GAAS,GACT+B,EAAAA,EAAKA,CAACC,OAAO,CAACtF,EAAE,kEACpB,CAAE,MAAOuF,EAAO,CACZF,EAAAA,EAAKA,CAACE,KAAK,CAACvF,EAAE,4DAClB,CACJ,EAEMwF,EAAY,IAAMlC,GAAS,GAMjC,MAJAmC,CAAAA,EAAAA,EAAAA,SAAAA,CAASA,CAAC,KACNd,GACJ,EAAG,EAAE,EAGD,WAACT,MAAAA,WACG,WAACwB,EAAAA,CAAKA,CAAAA,CAACC,KAAMtC,EAAauC,OAAQJ,YAC9B,UAACE,EAAAA,CAAKA,CAACG,MAAM,EAACC,WAAW,aACrB,UAACJ,EAAAA,CAAKA,CAACK,KAAK,WAAE/F,EAAE,2CAEpB,UAAC0F,EAAAA,CAAKA,CAACM,IAAI,WAAEhG,EAAE,uCACf,WAAC0F,EAAAA,CAAKA,CAACO,MAAM,YACT,UAACC,EAAAA,CAAMA,CAAAA,CAACC,QAAQ,YAAY1B,QAASe,WAChCxF,EAAE,YAEP,UAACkG,EAAAA,CAAMA,CAAAA,CAACC,QAAQ,UAAU1B,QAASU,WAC9BnF,EAAE,eAKf,UAACF,EAAAA,CAAQA,CAAAA,CACLM,QAASA,EACTC,KAAMwC,EACNvC,UAAWA,EACXU,WAAW,EACXN,oBAAqBA,EACrBC,iBA/DckD,CA+DIlD,GA9D1B8C,EAAsBG,KAAK,CAAGT,EAC9BM,EAAsBI,IAAI,CAAGA,EAC7Bc,GACJ,MA+DJ", "sources": ["webpack://_N_E/?ade1", "webpack://_N_E/./components/common/RKITable.tsx", "webpack://_N_E/./pages/adminsettings/operationstatuses/operationstatusTable.tsx"], "sourcesContent": ["\n    (window.__NEXT_P = window.__NEXT_P || []).push([\n      \"/adminsettings/operationstatuses/operationstatusTable\",\n      function () {\n        return require(\"private-next-pages/adminsettings/operationstatuses/operationstatusTable.tsx\");\n      }\n    ]);\n    if(module.hot) {\n      module.hot.dispose(function () {\n        window.__NEXT_P.push([\"/adminsettings/operationstatuses/operationstatusTable\"])\n      });\n    }\n  ", "//Import Library\r\nimport React from 'react'\r\nimport DataTable  from 'react-data-table-component';\r\n\r\n//Import services/components\r\nimport { useTranslation } from 'next-i18next';\r\nimport CustomLoader from './CustomLoader';\r\n\r\ninterface RKITableProps {\r\n  columns: any[];\r\n  data: any[];\r\n  totalRows?: number;\r\n  resetPaginationToggle?: boolean;\r\n  subheader?: boolean;\r\n  subHeaderComponent?: React.ReactNode;\r\n  handlePerRowsChange?: (newPerPage: number, page: number) => void;\r\n  handlePageChange?: (page: number) => void;\r\n  rowsPerPage?: number[];\r\n  defaultRowsPerPage?: number;\r\n  selectableRows?: boolean;\r\n  loading?: boolean;\r\n  pagServer?: boolean;\r\n  onSelectedRowsChange?: (selectedRows: any) => void;\r\n  clearSelectedRows?: boolean;\r\n  sortServer?: boolean;\r\n  onSort?: (column: any, sortDirection: string) => void;\r\n  persistTableHead?: boolean;\r\n  sortFunction?: (rows: any[], field: any, direction: 'asc' | 'desc') => any[];\r\n  noHeader?: boolean;\r\n  dense?: boolean;\r\n  pagination?: boolean;\r\n  paginationServer?: boolean;\r\n  paginationTotalRows?: number;\r\n  subHeaderAlign?: string;\r\n  [key: string]: any;\r\n}\r\n\r\nfunction RKITable(props: RKITableProps) {\r\n  const { t } = useTranslation('common');\r\n  const paginationComponentOptions = {\r\n    rowsPerPageText: t('Rowsperpage'),\r\n    };\r\n  const {\r\n    columns,\r\n    data,\r\n    totalRows,\r\n    resetPaginationToggle,\r\n    subheader,\r\n    subHeaderComponent,\r\n    handlePerRowsChange,\r\n    handlePageChange,\r\n    rowsPerPage,\r\n    defaultRowsPerPage,\r\n    selectableRows,\r\n    loading,\r\n    pagServer,\r\n    onSelectedRowsChange,\r\n    clearSelectedRows,\r\n    sortServer,\r\n    onSort,\r\n    persistTableHead,\r\n    sortFunction,\r\n    // Filter out any unknown props that might cause DOM warnings\r\n    ...otherProps\r\n  } = props;\r\n\r\n  // Only pass known DataTable props to avoid DOM warnings\r\n  const dataTableProps = {\r\n    paginationComponentOptions,\r\n    noDataComponent: t(\"NoData\"),\r\n    noHeader: true,\r\n    columns,\r\n    data: data || [],\r\n    dense: true,\r\n    paginationResetDefaultPage: resetPaginationToggle,\r\n    subHeader: subheader,\r\n    progressPending: loading,\r\n    subHeaderComponent,\r\n    pagination: true,\r\n    paginationServer: pagServer,\r\n    paginationPerPage: defaultRowsPerPage ? defaultRowsPerPage : 10,\r\n    paginationRowsPerPageOptions: rowsPerPage ? rowsPerPage : [10, 15, 20, 25, 30],\r\n    paginationTotalRows: totalRows,\r\n    onChangeRowsPerPage: handlePerRowsChange,\r\n    onChangePage: handlePageChange,\r\n    selectableRows,\r\n    onSelectedRowsChange,\r\n    clearSelectedRows,\r\n    progressComponent: <CustomLoader/>,\r\n    sortIcon: <i className=\"sort-icon fas fa-exchange-alt\" />,\r\n    sortServer,\r\n    onSort,\r\n    sortFunction,\r\n    persistTableHead,\r\n    className: \"rki-table\"\r\n  };\r\n  return (\r\n    <DataTable {...dataTableProps} />\r\n  )\r\n};\r\n\r\nRKITable.defaultProps = {\r\n  subHeader: false,\r\n  pagination: true,\r\n  totalRows: null,\r\n  pagServer: true,\r\n  onSelectedRowsChange: null,\r\n  clearSelectedRows: false,\r\n  sortServer: false,\r\n  persistTableHead: false\r\n}\r\n\r\nexport default RKITable;\r\n\r\n", "//Import Library\r\nimport { useState, useEffect } from \"react\";\r\nimport Link from \"next/link\";\r\nimport { <PERSON><PERSON>, Button } from \"react-bootstrap\";\r\n\r\nimport toast from 'react-hot-toast';\r\n\r\n//Import services/components\r\nimport RKITable from \"../../../components/common/RKITable\";\r\nimport apiService from \"../../../services/apiService\";\r\nimport { useTranslation } from 'next-i18next';\r\n\r\nconst OperationstatusTable = (_props: any) => {\r\n    const [tabledata, setDataToTable] = useState([]);\r\n    const [, setLoading] = useState(false);\r\n    const [totalRows, setTotalRows] = useState(0);\r\n    const [perPage, setPerPage] = useState(10);\r\n    const [isModalShow, setModal] = useState(false);\r\n    const [selectOperationstatus, setSelectOperationstatus] = useState({});\r\n    const { t } = useTranslation('common');\r\n    \r\n    const operationstatusParams = {\r\n        sort: { title: \"asc\" },\r\n        limit: perPage,\r\n        page: 1,\r\n        query: {},\r\n    };\r\n\r\n    const columns = [\r\n        {\r\n            name: t(\"Title\"),\r\n            selector: \"title\",\r\n        },\r\n        {\r\n            name: t(\"action\"),\r\n            selector: \"\",\r\n            cell: (d: any) => (\r\n                <div>\r\n                    <Link href=\"/adminsettings/[...routes]\" as={`/adminsettings/edit_operationstatus/${d._id}`}>\r\n\r\n                        <i className=\"icon fas fa-edit\" />\r\n\r\n                    </Link>\r\n                    &nbsp;\r\n                    <a onClick={() => userAction(d)}>\r\n                        <i className=\"icon fas fa-trash-alt\" />\r\n                    </a>{\" \"}\r\n                </div>\r\n            ),\r\n        },\r\n    ];\r\n\r\n    const getoperationstatusData = async () => {\r\n        setLoading(true);\r\n        const response = await apiService.get(\"/operation_status\", operationstatusParams);\r\n        if (response && response.data && response.data.length > 0) {\r\n            setDataToTable(response.data);\r\n            setTotalRows(response.totalCount);\r\n            setLoading(false);\r\n        }\r\n    };\r\n\r\n    const handlePageChange = (page: any) => {\r\n        operationstatusParams.limit = perPage;\r\n        operationstatusParams.page = page;\r\n        getoperationstatusData();\r\n    };\r\n\r\n    const handlePerRowsChange = async (newPerPage: any, page: any) => {\r\n        operationstatusParams.limit = newPerPage;\r\n        operationstatusParams.page = page;\r\n        setLoading(true);\r\n        const response = await apiService.get(\"/operation_status\", operationstatusParams);\r\n        if (response && response.data && response.data.length > 0) {\r\n            setDataToTable(response.data);\r\n            setPerPage(newPerPage);\r\n            setLoading(false);\r\n        }\r\n    };\r\n\r\n    const userAction = async (row: any) => {\r\n        setSelectOperationstatus(row._id);\r\n        setModal(true);\r\n    };\r\n\r\n    const modalConfirm = async () => {\r\n        try {\r\n            await apiService.remove(`/operation_status/${selectOperationstatus}`);\r\n            getoperationstatusData();\r\n            setModal(false);\r\n            toast.success(t(\"adminsetting.OperationStatus.Table.opStatusDeletedSuccessfully\"));\r\n        } catch (error) {\r\n            toast.error(t(\"adminsetting.OperationStatus.Table.errorDeletingOpStatus\"));\r\n        }\r\n    };\r\n\r\n    const modalHide = () => setModal(false);\r\n\r\n    useEffect(() => {\r\n        getoperationstatusData();\r\n    }, []);\r\n\r\n    return (\r\n        <div>\r\n            <Modal show={isModalShow} onHide={modalHide}>\r\n                <Modal.Header closeButton>\r\n                    <Modal.Title>{t(\"adminsetting.OperationStatus.Delete\")}</Modal.Title>\r\n                </Modal.Header>\r\n                <Modal.Body>{t(\"adminsetting.OperationStatus.sure\")}</Modal.Body>\r\n                <Modal.Footer>\r\n                    <Button variant=\"secondary\" onClick={modalHide}>\r\n                        {t(\"Cancel\")}\r\n                    </Button>\r\n                    <Button variant=\"primary\" onClick={modalConfirm}>\r\n                        {t(\"yes\")}\r\n                    </Button>\r\n                </Modal.Footer>\r\n            </Modal>\r\n\r\n            <RKITable\r\n                columns={columns}\r\n                data={tabledata}\r\n                totalRows={totalRows}\r\n                pagServer={true}\r\n                handlePerRowsChange={handlePerRowsChange}\r\n                handlePageChange={handlePageChange}\r\n            />\r\n        </div>\r\n    );\r\n};\r\n\r\nexport default OperationstatusTable;\r\n"], "names": ["RKITable", "props", "t", "useTranslation", "paginationComponentOptions", "rowsPerPageText", "columns", "data", "totalRows", "resetPaginationToggle", "subheader", "subHeaderComponent", "handlePerRowsChange", "handlePageChange", "rowsPerPage", "defaultRowsPerPage", "selectableRows", "loading", "pagServer", "onSelectedRowsChange", "clearSelectedRows", "sortServer", "onSort", "persistTableHead", "sortFunction", "otherProps", "dataTableProps", "noDataComponent", "<PERSON><PERSON><PERSON><PERSON>", "dense", "paginationResetDefaultPage", "subHeader", "progressPending", "pagination", "paginationServer", "paginationPerPage", "paginationRowsPerPageOptions", "paginationTotalRows", "onChangeRowsPerPage", "onChangePage", "progressComponent", "CustomLoader", "sortIcon", "i", "className", "DataTable", "defaultProps", "tabledata", "setDataToTable", "useState", "OperationstatusTable", "setLoading", "setTotalRows", "perPage", "setPerPage", "isModalShow", "setModal", "selectOperationstatus", "setSelectOperationstatus", "operationstatusParams", "sort", "title", "limit", "page", "query", "name", "selector", "cell", "div", "Link", "href", "as", "d", "_id", "a", "onClick", "userAction", "getoperationstatusData", "response", "apiService", "get", "length", "totalCount", "newPerPage", "row", "modalConfirm", "remove", "toast", "success", "error", "modalHide", "useEffect", "Modal", "show", "onHide", "Header", "closeButton", "Title", "Body", "Footer", "<PERSON><PERSON>", "variant"], "sourceRoot": "", "ignoreList": []}