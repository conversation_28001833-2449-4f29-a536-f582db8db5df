"use strict";(()=>{var e={};e.id=5620,e.ids=[636,3220,5620],e.modules={123:e=>{e.exports=require("dom-helpers/removeEventListener")},1332:e=>{e.exports=require("react-custom-scrollbars-2")},1428:e=>{e.exports=import("axios")},1680:e=>{e.exports=require("@restart/ui/utils")},1919:e=>{e.exports=require("react-transition-group/Transition")},3892:e=>{e.exports=require("classnames")},4048:e=>{e.exports=require("react-truncate")},6009:e=>{e.exports=require("dom-helpers/ownerDocument")},6417:(e,r,s)=>{s.d(r,{A:()=>a});let t=s(82015).createContext(null);t.displayName="CardHeaderContext";let a=t},6858:(e,r,s)=>{s.d(r,{A:()=>p});var t=s(8732),a=s(82015),i=s.n(a),n=s(18597),o=s(78219),l=s.n(o);function c(e){let{list:r,dialogClassName:s}=e;return(0,t.jsxs)(l(),{...e,dialogClassName:s,"aria-labelledby":"contained-modal-title-vcenter",centered:!0,children:[(0,t.jsx)(l().Header,{closeButton:!0,children:(0,t.jsx)(l().Title,{id:"contained-modal-title-vcenter",children:r.heading})}),(0,t.jsx)(l().Body,{children:r.body})]})}function d(e){let{list:r}=e,[s,a]=i().useState(!1);return r&&r.body?(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)("button",{type:"button",onClick:()=>a(!0),style:{border:"none",background:"none",padding:0},children:(0,t.jsx)(n.A.Footer,{children:(0,t.jsx)("i",{className:"fas fa-chevron-down"})})}),e.list&&(0,t.jsx)(c,{list:e.list,show:s,onHide:()=>a(!1),dialogClassName:e.dialogClassName})]}):null}let p=function(e){let{header:r,body:s}=e;return(0,t.jsxs)(n.A,{className:"text-center infoCard",children:[(0,t.jsx)(n.A.Header,{children:r}),(0,t.jsx)(n.A.Body,{children:(0,t.jsx)(n.A.Text,{children:s})}),(0,t.jsx)(d,{...e})]})}},6952:e=>{e.exports=require("@restart/ui/Modal")},7374:e=>{e.exports=require("@restart/ui/Dropdown")},8732:e=>{e.exports=require("react/jsx-runtime")},9653:e=>{e.exports=require("@restart/ui/Overlay")},11242:e=>{e.exports=require("redux-persist/integration/react")},11688:e=>{e.exports=require("@restart/hooks/useWillUnmount")},12388:e=>{e.exports=require("dom-helpers/addEventListener")},13364:e=>{e.exports=require("redux-saga/effects")},14062:e=>{e.exports=import("react-redux")},14078:e=>{e.exports=import("swr")},14332:e=>{e.exports=require("uncontrollable")},16116:e=>{e.exports=require("invariant")},18597:(e,r,s)=>{s.d(r,{A:()=>A});var t=s(3892),a=s.n(t),i=s(82015),n=s(80739),o=s(8732);let l=i.forwardRef(({className:e,bsPrefix:r,as:s="div",...t},i)=>(r=(0,n.oU)(r,"card-body"),(0,o.jsx)(s,{ref:i,className:a()(e,r),...t})));l.displayName="CardBody";let c=i.forwardRef(({className:e,bsPrefix:r,as:s="div",...t},i)=>(r=(0,n.oU)(r,"card-footer"),(0,o.jsx)(s,{ref:i,className:a()(e,r),...t})));c.displayName="CardFooter";var d=s(6417);let p=i.forwardRef(({bsPrefix:e,className:r,as:s="div",...t},l)=>{let c=(0,n.oU)(e,"card-header"),p=(0,i.useMemo)(()=>({cardHeaderBsPrefix:c}),[c]);return(0,o.jsx)(d.A.Provider,{value:p,children:(0,o.jsx)(s,{ref:l,...t,className:a()(r,c)})})});p.displayName="CardHeader";let u=i.forwardRef(({bsPrefix:e,className:r,variant:s,as:t="img",...i},l)=>{let c=(0,n.oU)(e,"card-img");return(0,o.jsx)(t,{ref:l,className:a()(s?`${c}-${s}`:c,r),...i})});u.displayName="CardImg";let x=i.forwardRef(({className:e,bsPrefix:r,as:s="div",...t},i)=>(r=(0,n.oU)(r,"card-img-overlay"),(0,o.jsx)(s,{ref:i,className:a()(e,r),...t})));x.displayName="CardImgOverlay";let m=i.forwardRef(({className:e,bsPrefix:r,as:s="a",...t},i)=>(r=(0,n.oU)(r,"card-link"),(0,o.jsx)(s,{ref:i,className:a()(e,r),...t})));m.displayName="CardLink";var h=s(7783);let v=(0,h.A)("h6"),f=i.forwardRef(({className:e,bsPrefix:r,as:s=v,...t},i)=>(r=(0,n.oU)(r,"card-subtitle"),(0,o.jsx)(s,{ref:i,className:a()(e,r),...t})));f.displayName="CardSubtitle";let g=i.forwardRef(({className:e,bsPrefix:r,as:s="p",...t},i)=>(r=(0,n.oU)(r,"card-text"),(0,o.jsx)(s,{ref:i,className:a()(e,r),...t})));g.displayName="CardText";let b=(0,h.A)("h5"),j=i.forwardRef(({className:e,bsPrefix:r,as:s=b,...t},i)=>(r=(0,n.oU)(r,"card-title"),(0,o.jsx)(s,{ref:i,className:a()(e,r),...t})));j.displayName="CardTitle";let q=i.forwardRef(({bsPrefix:e,className:r,bg:s,text:t,border:i,body:c=!1,children:d,as:p="div",...u},x)=>{let m=(0,n.oU)(e,"card");return(0,o.jsx)(p,{ref:x,...u,className:a()(r,m,s&&`bg-${s}`,t&&`text-${t}`,i&&`border-${i}`),children:c?(0,o.jsx)(l,{children:d}):d})});q.displayName="Card";let A=Object.assign(q,{Img:u,Title:j,Subtitle:f,Body:l,Link:m,Text:g,Header:p,Footer:c,ImgOverlay:x})},18622:e=>{e.exports=require("yup")},19121:e=>{e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},22326:e=>{e.exports=require("react-dom")},22541:e=>{e.exports=require("redux-persist/lib/storage")},25303:e=>{e.exports=require("@restart/ui/NavItem")},26324:e=>{e.exports=require("warning")},27053:(e,r,s)=>{s.d(r,{A:()=>a});var t=s(8732);function a(e){return(0,t.jsx)("h2",{className:"page-heading",children:e.title})}},27282:(e,r,s)=>{s.a(e,async(e,t)=>{try{s.r(r),s.d(r,{config:()=>v,default:()=>u,getServerSideProps:()=>h,getStaticPaths:()=>m,getStaticProps:()=>x,reportWebVitals:()=>f,routeModule:()=>y,unstable_getServerProps:()=>q,unstable_getServerSideProps:()=>A,unstable_getStaticParams:()=>j,unstable_getStaticPaths:()=>b,unstable_getStaticProps:()=>g});var a=s(63885),i=s(80237),n=s(81413),o=s(9616),l=s.n(o),c=s(72386),d=s(33737),p=e([c,d]);[c,d]=p.then?(await p)():p;let u=(0,n.M)(d,"default"),x=(0,n.M)(d,"getStaticProps"),m=(0,n.M)(d,"getStaticPaths"),h=(0,n.M)(d,"getServerSideProps"),v=(0,n.M)(d,"config"),f=(0,n.M)(d,"reportWebVitals"),g=(0,n.M)(d,"unstable_getStaticProps"),b=(0,n.M)(d,"unstable_getStaticPaths"),j=(0,n.M)(d,"unstable_getStaticParams"),q=(0,n.M)(d,"unstable_getServerProps"),A=(0,n.M)(d,"unstable_getServerSideProps"),y=new a.PagesRouteModule({definition:{kind:i.A.PAGES,page:"/vspace",pathname:"/vspace",bundlePath:"",filename:""},components:{App:c.default,Document:l()},userland:d});t()}catch(e){t(e)}})},27825:e=>{e.exports=require("lodash")},27910:e=>{e.exports=require("stream")},28217:e=>{e.exports=require("@restart/ui/DropdownItem")},29021:e=>{e.exports=require("fs")},29294:e=>{e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},29628:e=>{e.exports=require("dom-helpers/addClass")},29825:e=>{e.exports=require("prop-types")},29841:e=>{e.exports=require("dom-helpers/hasClass")},30362:e=>{e.exports=require("@restart/ui/DropdownToggle")},31478:(e,r,s)=>{s.d(r,{A:()=>u});var t=s(8732),a=s(82015),i=s(49481),n=s(83551);let o=require("react-alice-carousel");var l=s.n(o),c=s(88751);let d={576:{items:1},1024:{items:4}},p={paddingLeft:0,paddingRight:0},u=({items:e,renderItems:r,...s})=>{let o=(0,a.useRef)(null),[u,x]=(0,a.useState)(0),[m,h]=(0,a.useState)([]),{t:v}=(0,c.useTranslation)("common"),f=e=>{let{item:r}=e;x(r)};return(0,a.useEffect)(()=>{h(e.map((e,s)=>r(e,s)))},[e]),(0,t.jsxs)("div",{style:{position:"relative"},children:[(0,t.jsx)(i.A,{lg:12,style:{paddingLeft:"13px"},children:(0,t.jsx)(l(),{items:m,activeIndex:u,responsive:d,infinite:!0,disableButtonsControls:!0,paddingLeft:p.paddingLeft,paddingRight:p.paddingRight,disableDotsControls:!0,onInitialized:f,onSlideChanged:f,onResized:f,ref:o})}),e&&e.length>0?(0,t.jsxs)(a.Fragment,{children:[(0,t.jsx)("div",{className:"alice-carousel__prev-btn-item",onClick:()=>{o.current&&o.current.slidePrev()},"aria-label":"Previous Slide",children:(0,t.jsx)("i",{className:"fa fa-chevron-circle-left"})}),(0,t.jsx)("div",{className:"alice-carousel__next-btn-item",onClick:()=>{o.current&&o.current.slideNext()},"aria-label":"Next Slide",children:(0,t.jsx)("i",{className:"fa fa-chevron-circle-right"})})]}):(0,t.jsx)(n.A,{children:(0,t.jsx)(i.A,{xs:12,className:"p-0",children:(0,t.jsx)("div",{className:"text-center",children:v("Content")})})})]})}},33737:(e,r,s)=>{s.a(e,async(e,t)=>{try{s.r(r),s.d(r,{default:()=>S,getStaticProps:()=>y});var a=s(8732),i=s(82015),n=s(19918),o=s.n(n),l=s(27825),c=s.n(l),d=s(91353),p=s(7082),u=s(83551),x=s(49481),m=s(42893),h=s(31478),v=s(63487),f=s(6858),g=s(88751),b=s(27053),j=s(45400),q=s(35576),A=e([m,v]);async function y({locale:e}){return{props:{...await (0,q.serverSideTranslations)(e,["common"])}}}[m,v]=A.then?(await A)():A;let S=()=>{let{t:e}=(0,g.useTranslation)("common"),[r,s]=(0,i.useState)([]),[t,n]=(0,i.useState)(""),[l,q]=(0,i.useState)([]),[A,y]=(0,i.useState)([]),[S,_]=(0,i.useState)([]),w={sort:{created_at:"desc"},lean:!0,query:{},populate:[{path:"topic",select:"title"},{path:"subscribers",select:"_id"}],select:"-start_date -end_date -members -images -user -created_at -updated_at -file_category -vspace_email_invite"},C=e=>{e&&(w.query={title:e}),k()},P=c().debounce(e=>C(e),Number("500")||300),k=async()=>{let e=await v.A.post("/users/getLoggedUser",{});if(e&&e._id)try{let{data:r}=await v.A.get("/vspace",w);await N(r,e,s,q,_,y)}catch(e){s([])}};(0,i.useEffect)(()=>{k()},[]);let M=e=>{let r=document.createElement("div");r.innerHTML=e;let s=r.textContent||r.innerText||"";return s.length>180?`${s.substring(0,177)}...`:s},R=async r=>{if(r&&r._id){await v.A.post("/vspace/updateSubscriber",{isSubscribe:!0,_id:r._id});let t=l.filter(e=>e._id!==r._id);q(t),s(e=>[r,...e]),m.default.success(e("vspace.subscribedForToast"))}},E=async r=>{r&&r._id&&await v.A.post("/vspace/subscribeRequest",{_id:r._id})&&(_(e=>[...e,r._id]),y(e=>[...e]),m.default.success(e("vspace.vspacehasrequested")))},T=()=>(0,a.jsx)(o(),{href:"/vspace/[...routes]",as:"/vspace/create",children:(0,a.jsx)(d.A,{children:e("createvirtualSpaces")})}),U=(0,j.canAddVspace)(()=>(0,a.jsx)(T,{}));return(0,a.jsxs)(p.A,{className:"vspace",children:[(0,a.jsx)(u.A,{className:"page-header",children:(0,a.jsx)(x.A,{lg:12,children:(0,a.jsx)(b.A,{title:e("vspace.virtualSpaces")})})}),(0,a.jsxs)(u.A,{children:[(0,a.jsx)(x.A,{className:"ps-2",md:6,children:(0,a.jsxs)("div",{className:"searchbar",children:[(0,a.jsx)("input",{className:"searchInput form-control",onChange:e=>{n(e.target.value),P(e.target.value)},value:t,type:"text",name:"",placeholder:e("vspace.Search")}),(0,a.jsx)("a",{href:"#",className:"search_icon",children:(0,a.jsx)("i",{className:"fas fa-search"})})]})}),(0,a.jsx)(x.A,{md:3}),(0,a.jsx)(x.A,{md:3,className:"createVSpace",children:(0,a.jsx)(U,{})})]}),(0,a.jsxs)(u.A,{className:"subscriptionBlock",children:[(0,a.jsx)(x.A,{className:"header-block",lg:12,children:(0,a.jsx)("h6",{children:(0,a.jsx)("span",{children:e("vspace.subscribedVspace")})})}),(0,a.jsx)(x.A,{lg:12,children:(0,a.jsx)(h.A,{selector:"title",items:r,renderItems:(e,r)=>{let s=M(e.description);return(0,a.jsx)(o(),{href:"vspace/[...routes]",as:`/vspace/show/${e._id}`,children:(0,a.jsx)("div",{className:"rki-carousel-card",children:(0,a.jsx)(f.A,{header:e.title,body:s})})},e._id)}})})]}),(0,a.jsxs)(u.A,{className:"subscriptionBlock publicVSpace popButton",children:[(0,a.jsx)(x.A,{className:"header-block",lg:12,children:(0,a.jsx)("h6",{children:(0,a.jsx)("span",{children:e("vspace.publicVspaceUnSubscribed")})})}),(0,a.jsx)(x.A,{lg:12,children:(0,a.jsx)(h.A,{items:l,renderItems:(r,s)=>{let t=M(r.description);return(0,a.jsxs)("div",{className:"rki-carousel-card",children:[(0,a.jsx)(f.A,{header:r.title,body:t}),(0,a.jsx)("div",{className:"hover-btn text-center",onClick:()=>R(r),children:e("vspace.Subscribe")})]},r._id)}})})]}),(0,a.jsxs)(u.A,{className:"subscriptionBlock popButton",children:[(0,a.jsx)(x.A,{className:"header-block",lg:12,children:(0,a.jsx)("h6",{children:(0,a.jsx)("span",{children:e("vspace.restrictedVSpaces")})})}),(0,a.jsx)(x.A,{lg:12,children:(0,a.jsx)(h.A,{items:A,renderItems:(r,s)=>{let t=M(r.description);return(0,a.jsxs)("div",{className:"rki-carousel-card",children:[(0,a.jsx)(f.A,{header:r.title,body:t}),-1===S.indexOf(r._id)?(0,a.jsx)("div",{className:"hover-btn text-center",onClick:()=>E(r),children:e("vspace.Request")}):(0,a.jsx)("div",{className:"hover-btn text-center",children:e("vspace.Requested")})]},r._id)}})})]})]})};async function N(e,r,s,t,a,i){if(Array.isArray(e)&&e.length>0){let n=c().filter(e,e=>{let s=!1;return Array.isArray(e.subscribers)&&e.subscribers.length>0&&(s=c().filter(e.subscribers,e=>e._id===r._id).length>0),s}),o=n.map(e=>e._id),l=c().filter(e,e=>!0===e.visibility&&-1===o.indexOf(e._id)),d=c().filter(e,e=>!1===e.visibility&&-1===o.indexOf(e._id));s(n),t(l);let p=await v.A.get("/vspace-request-subscribers/getRequestedByMe",{query:{requested_by:r._id}});if(p&&p.data){let e=p.data.map(e=>e.vspace._id);a(e)}i(d)}}t()}catch(e){t(e)}})},33873:e=>{e.exports=require("path")},35576:e=>{e.exports=require("next-i18next/serverSideTranslations")},36653:e=>{e.exports=require("nprogress")},36955:e=>{e.exports=require("@restart/hooks/useBreakpoint")},37766:e=>{e.exports=require("@restart/hooks/useMergedRefs")},39756:e=>{e.exports=import("redux")},40051:e=>{e.exports=require("dom-helpers/removeClass")},40361:e=>{e.exports=require("next/dist/compiled/next-server/pages.runtime.prod.js")},42893:e=>{e.exports=import("react-hot-toast")},43294:e=>{e.exports=require("formik")},45400:(e,r,s)=>{s.r(r),s.d(r,{canAddVspace:()=>o,canAddVspaceForm:()=>l,canEditVspace:()=>c,canEditVspaceForm:()=>d,canViewDiscussionUpdate:()=>p,default:()=>u});var t=s(8732);s(82015);var a=s(81366),i=s.n(a),n=s(61421);let o=i()({authenticatedSelector:e=>!!e.permissions&&!!e.permissions.vspace&&!!e.permissions.vspace["create:any"],wrapperDisplayName:"CanAddVspace"}),l=i()({authenticatedSelector:e=>!!e.permissions&&!!e.permissions.vspace&&!!e.permissions.vspace["create:any"],wrapperDisplayName:"CanAddVspaceForm",FailureComponent:()=>(0,t.jsx)(n.default,{})}),c=i()({authenticatedSelector:(e,r)=>{if(e.permissions&&e.permissions.vspace){if(e.permissions.vspace["update:any"])return!0;else if(e.permissions.vspace["update:own"]&&r.vspace&&r.vspace.user&&r.vspace.user._id===e.user._id)return!0}return!1},wrapperDisplayName:"CanEditVspace"}),d=i()({authenticatedSelector:(e,r)=>{if(e.permissions&&e.permissions.vspace){if(e.permissions.vspace["update:any"])return!0;else if(e.permissions.vspace["update:own"]&&r.vspace&&r.vspace.user&&r.vspace.user._id===e.user._id)return!0}return!1},wrapperDisplayName:"CanEditVspaceForm",FailureComponent:()=>(0,t.jsx)(n.default,{})}),p=i()({authenticatedSelector:e=>!!e.permissions&&!!e.permissions.update&&!!e.permissions.update["read:any"],wrapperDisplayName:"CanViewDiscussionUpdate"}),u=o},50009:e=>{e.exports=require("@restart/ui/DropdownMenu")},57664:e=>{e.exports=require("dom-helpers/scrollbarSize")},58928:e=>{e.exports=require("dom-helpers/canUseDOM")},59717:e=>{e.exports=require("@restart/ui/DropdownContext")},60560:e=>{e.exports=require("dom-helpers/contains")},63033:e=>{e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},65447:e=>{e.exports=require("@restart/ui/Button")},67364:e=>{e.exports=require("@restart/hooks/useCallbackRef")},68455:e=>{e.exports=import("redux-saga")},69722:e=>{e.exports=require("es6-promise")},74075:e=>{e.exports=require("zlib")},74716:e=>{e.exports=require("moment")},74987:e=>{e.exports=require("@restart/ui/ModalManager")},78097:e=>{e.exports=require("next-redux-saga")},78634:e=>{e.exports=require("@restart/ui/Anchor")},80237:(e,r)=>{Object.defineProperty(r,"A",{enumerable:!0,get:function(){return s}});var s=function(e){return e.PAGES="PAGES",e.PAGES_API="PAGES_API",e.APP_PAGE="APP_PAGE",e.APP_ROUTE="APP_ROUTE",e.IMAGE="IMAGE",e}({})},81366:e=>{e.exports=require("redux-auth-wrapper/connectedAuthWrapper")},81413:(e,r)=>{Object.defineProperty(r,"M",{enumerable:!0,get:function(){return function e(r,s){return s in r?r[s]:"then"in r&&"function"==typeof r.then?r.then(r=>e(r,s)):"function"==typeof r&&"default"===s?r:void 0}}})},81521:e=>{e.exports=require("dom-helpers/querySelectorAll")},81895:e=>{e.exports=require("@restart/hooks/useEventCallback")},82015:e=>{e.exports=require("react")},86842:e=>{e.exports=require("@restart/ui/SelectableContext")},87571:e=>{e.exports=require("dom-helpers/transitionEnd")},88751:e=>{e.exports=require("next-i18next")},93787:e=>{e.exports=require("redux-persist")},94947:e=>{e.exports=require("@restart/hooks/useTimeout")},96196:e=>{e.exports=require("next-redux-wrapper")},98320:e=>{e.exports=require("@restart/hooks/useIsomorphicEffect")},99460:e=>{e.exports=require("dom-helpers/css")}};var r=require("../webpack-runtime.js");r.C(e);var s=e=>r(r.s=e),t=r.X(0,[6089,9216,9616,2386],()=>s(27282));module.exports=t})();