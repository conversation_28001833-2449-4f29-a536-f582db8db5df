{"version": 3, "file": "static/chunks/pages/hazard/HazardShow-3ffc3a7d78b5e0f8.js", "mappings": "gFACA,4CACA,qBACA,WACA,OAAe,EAAQ,IAA0C,CACjE,EACA,UAFsB", "sources": ["webpack://_N_E/?1ce1"], "sourcesContent": ["\n    (window.__NEXT_P = window.__NEXT_P || []).push([\n      \"/hazard/HazardShow\",\n      function () {\n        return require(\"private-next-pages/hazard/HazardShow.tsx\");\n      }\n    ]);\n    if(module.hot) {\n      module.hot.dispose(function () {\n        window.__NEXT_P.push([\"/hazard/HazardShow\"])\n      });\n    }\n  "], "names": [], "sourceRoot": "", "ignoreList": []}