{"version": 3, "file": "static/chunks/pages/adminsettings/projectstatuses-72338e5ea1cc1dc8.js", "mappings": "0OAmIA,MAvH4BA,IACxB,GAAM,GAAEC,CAAC,CAAE,CAAGC,CAAAA,EAAAA,EAAAA,EAAAA,CAAcA,CAAC,MAsHlBC,IArHL,CAACC,EAAWC,EAAe,CAAGC,CAAAA,EAAAA,EAAAA,GAqHPH,EAAC,GArHMG,CAAQA,CAAC,EAAE,EACzC,EAAGC,EAAW,CAAGD,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,EAAC,GAC1B,CAACE,EAAWC,EAAa,CAAGH,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAC,GACrC,CAACI,EAASC,EAAW,CAAGL,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAC,IACjC,CAACM,EAAaC,EAAS,CAAGP,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,EAAC,GACnC,CAACQ,EAAqBC,EAAuB,CAAGT,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAC,CAAC,GAE1DU,EAAsB,CACxBC,KAAM,CAAEC,MAAO,KAAM,EACrBC,MAAOT,EACPU,KAAM,EACNC,MAAO,CAAC,CACZ,EAEMC,EAAU,CACZ,CACIC,KAAMtB,EAAE,oCACRuB,SAAU,OACd,EACA,CACID,KAAMtB,EAAE,qCACRuB,SAAU,GACVC,KAAM,GACF,WAACC,MAAAA,WACG,UAACC,IAAIA,CAACC,KAAK,6BAA6BC,GAAI,OAAvCF,8BAAkF,OAANG,EAAEC,GAAG,WAElF,UAACC,IAAAA,CAAEC,UAAU,uBAEV,OAEP,UAACC,IAAAA,CAAEC,QAAS,IAAMC,EAAWN,YACzB,UAACE,IAAAA,CAAEC,UAAU,4BACZ,MAGjB,EACH,CAEKI,EAAuB,UACzB9B,GAAW,GACX,IAAM+B,EAAW,MAAMC,EAAAA,CAAUA,CAACC,GAAG,CAAC,iBAAkBxB,GACpDsB,GAAYA,EAASG,IAAI,EAAIH,EAASG,IAAI,CAACC,MAAM,CAAG,GAAG,CACvDrC,EAAeiC,EAASG,IAAI,EAC5BhC,EAAa6B,EAASK,UAAU,EAChCpC,GAAW,GAEnB,EAQMqC,EAAsB,MAAOC,EAAiBzB,KAChDJ,EAAoBG,KAAK,CAAG0B,EAC5B7B,EAAoBI,IAAI,CAAGA,EAC3Bb,GAAW,GACX,IAAM+B,EAAW,MAAMC,EAAAA,CAAUA,CAACC,GAAG,CAAC,iBAAkBxB,GACpDsB,GAAYA,EAASG,IAAI,EAAIH,EAASG,IAAI,CAACC,MAAM,CAAG,GAAG,CACvDrC,EAAeiC,EAASG,IAAI,EAC5B9B,EAAWkC,GACXtC,GAAW,GAEnB,EAEM6B,EAAa,MAAOU,IACtB/B,EAAuB+B,EAAIf,GAAG,EAC9BlB,GAAS,EACb,EAEMkC,EAAe,UACjB,GAAI,CACA,MAAMR,EAAAA,CAAUA,CAACS,MAAM,CAAC,kBAAsC,OAApBlC,IAC1CuB,IACAxB,GAAS,GACToC,EAAAA,EAAKA,CAACC,OAAO,CAACjD,EAAE,qEACpB,CAAE,MAAOkD,EAAO,CACZF,EAAAA,EAAKA,CAACE,KAAK,CAAClD,EAAE,+DAClB,CACJ,EAEMmD,EAAY,IAAMvC,GAAS,GAMjC,MAJAwC,CAAAA,EAAAA,EAAAA,SAAAA,CAASA,CAAC,KACNhB,GACJ,EAAG,EAAE,EAGD,WAACX,MAAAA,WACG,WAAC4B,EAAAA,CAAKA,CAAAA,CAACC,KAAM3C,EAAa4C,OAAQJ,YAC9B,UAACE,EAAAA,CAAKA,CAACG,MAAM,EAACC,WAAW,aACrB,UAACJ,EAAAA,CAAKA,CAACK,KAAK,WAAE1D,EAAE,sDAEpB,UAACqD,EAAAA,CAAKA,CAACM,IAAI,WAAE3D,EAAE,wEACf,WAACqD,EAAAA,CAAKA,CAACO,MAAM,YACT,UAACC,EAAAA,CAAMA,CAAAA,CAACC,QAAQ,YAAY5B,QAASiB,WAChCnD,EAAE,uCAEP,UAAC6D,EAAAA,CAAMA,CAAAA,CAACC,QAAQ,UAAU5B,QAASY,WAC9B9C,EAAE,0CAKf,UAAC+D,EAAAA,CAAQA,CAAAA,CACL1C,QAASA,EACTmB,KAAMrC,EACNI,UAAWA,EACXyD,UAAW,GACXrB,oBAAqBA,EACrBsB,iBA/Da,CA+DKA,GA9D1BlD,EAAoBG,KAAK,CAAGT,EAC5BM,EAAoBI,IAAI,CAAGA,EAC3BiB,GACJ,MA+DJ,6mBC9HA,IAAM8B,EAAS,aACFC,EAAmBC,CAAAA,EAAAA,EAAAA,CAAAA,CAAoBA,CAAC,CACnDC,sBAAuB,KACjBC,EAAMC,WAAW,IAAID,EAAMC,WAAW,CAACC,YAAY,IAAIF,EAAMC,WAAW,CAACC,YAAY,CAACN,EAAO,CAKnGO,CALqG,kBAKjF,kBACtB,GAAG,EAE0BL,CAAAA,EAAAA,EAAAA,CAAAA,CAAoBA,CAAC,CAChDC,sBAAuB,KACjBC,EAAMC,WAAW,IAAID,EAAMC,WAAW,CAACG,OAAO,IAAIJ,EAAMC,WAAW,CAACG,OAAO,CAACR,EAAO,CAKzFO,CAL2F,kBAKvE,eACtB,GAAG,EAEmCL,CAAAA,EAAAA,EAAAA,CAAAA,CAAoBA,CAAC,CACzDC,sBAAuB,KACjBC,EAAMC,WAAW,IAAID,EAAMC,WAAW,CAACI,iBAAiB,IAAIL,EAAMC,WAAW,CAACI,iBAAiB,CAACT,EAAO,CAK7GO,CAL+G,kBAK3F,wBACtB,GAAG,EAE8BL,CAAAA,EAAAA,EAAAA,CAAAA,CAAoBA,CAAC,CACpDC,sBAAuB,KACjBC,EAAMC,WAAW,IAAID,EAAMC,WAAW,CAACK,YAAY,IAAIN,EAAMC,WAAW,CAACK,YAAY,CAACV,EAAO,CAKnGO,CALqG,kBAKjF,mBACtB,GAEaI,EAAkBT,CAAAA,EAAAA,EAAAA,CAAAA,CAAoBA,CAAC,CAClDC,sBAAuB,KACjBC,EAAMC,WAAW,IAAID,EAAMC,WAAW,CAACO,SAAS,IAAIR,EAAMC,WAAW,CAACO,SAAS,CAACZ,EAAO,CAK7FO,CAL+F,kBAK3E,iBACtB,GAAG,EAEqCL,CAAAA,EAAAA,EAAAA,CAAAA,CAAoBA,CAAC,CAC3DC,sBAAuB,KACjBC,EAAMC,WAAW,IAAID,EAAMC,WAAW,CAACQ,uBAAuB,IAAIT,EAAMC,WAAW,CAACQ,uBAAuB,CAACb,EAAO,CAKzHO,CAL2H,kBAKvG,0BACtB,GAAG,EAEiCL,CAAAA,EAAAA,EAAAA,CAAAA,CAAoBA,CAAC,CACvDC,sBAAuB,KACjBC,EAAMC,WAAW,IAAID,EAAMC,WAAW,CAACQ,uBAAuB,IAAIT,EAAMC,WAAW,CAACQ,uBAAuB,CAACb,EAAO,CAKzHO,CAL2H,kBAKvG,sBACtB,GAAG,EAC0BL,CAAAA,EAAAA,EAAAA,CAAAA,CAAoBA,CAAC,CAChDC,sBAAuB,KACjBC,EAAMC,WAAW,IAAID,EAAMC,WAAW,CAACS,MAAM,IAAIV,EAAMC,WAAW,CAACS,MAAM,CAACd,EAAO,CAKvFO,CALyF,kBAKrE,eACtB,GAAG,EAE8BL,CAAAA,EAAAA,EAAAA,CAAAA,CAAoBA,CAAC,CACpDC,sBAAuB,KACjBC,EAAMC,WAAW,IAAID,EAAMC,WAAW,CAACU,WAAW,IAAIX,EAAMC,WAAW,CAACU,WAAW,CAACf,EAAO,CAKjGO,CALmG,kBAK/E,mBACtB,GAAG,EAEuCL,CAAAA,EAAAA,EAAAA,CAAAA,CAAoBA,CAAC,CAC7DC,sBAAuB,KACjBC,EAAMC,WAAW,IAAID,EAAMC,WAAW,CAACW,WAAW,IAAIZ,EAAMC,WAAW,CAACW,WAAW,CAAChB,EAAO,CAKjGO,CALmG,kBAK/E,4BACtB,GAAG,EAEuCL,CAAAA,EAAAA,EAAAA,CAAAA,CAAoBA,CAAC,CAC7DC,sBAAuB,KACjBC,EAAMC,WAAW,IAAID,EAAMC,WAAW,CAACY,mBAAmB,IAAIb,EAAMC,WAAW,CAACY,mBAAmB,CAACjB,EAAO,CAKjHO,CALmH,kBAK/F,4BACtB,GAAG,EAEoCL,CAAAA,EAAAA,EAAAA,CAAAA,CAAoBA,CAAC,CAC1DC,sBAAuB,KACjBC,EAAMC,WAAW,IAAID,EAAMC,WAAW,CAACa,gBAAgB,IAAId,EAAMC,WAAW,CAACa,gBAAgB,CAAClB,EAAO,CAK3GO,CAL6G,kBAKzF,yBACtB,GAEaY,EAAwBjB,CAAAA,EAAAA,EAAAA,CAAAA,CAAoBA,CAAC,CACxDC,sBAAuB,KACjBC,EAAMC,WAAW,IAAID,EAAMC,WAAW,CAACe,gBAAgB,IAAIhB,EAAMC,WAAW,CAACe,gBAAgB,CAACpB,EAAO,CAK3GO,CAL6G,kBAKzF,uBACtB,GAAG,EAEgCL,CAAAA,EAAAA,EAAAA,CAAAA,CAAoBA,CAAC,CACtDC,sBAAuB,KACjBC,EAAMC,WAAW,IAAID,EAAMC,WAAW,CAACgB,cAAc,IAAIjB,EAAMC,WAAW,CAACgB,cAAc,CAACrB,EAAO,CAKvGO,CALyG,kBAKrF,qBACtB,GAAG,EAE0BL,CAAAA,EAAAA,EAAAA,CAAAA,CAAoBA,CAAC,CAChDC,sBAAuB,KACjBC,EAAMC,WAAW,IAAID,EAAMC,WAAW,CAACiB,MAAM,IAAIlB,EAAMC,WAAW,CAACiB,MAAM,CAACtB,EAAO,CAKvFO,CALyF,kBAKrE,eACtB,GAEagB,EAAmBrB,CAAAA,EAAAA,EAAAA,CAAAA,CAAoBA,CAAC,CACnDC,sBAAuB,KACjBC,EAAMC,WAAW,IAAID,EAAMC,WAAW,CAACmB,UAAU,IAAIpB,EAAMC,WAAW,CAACmB,UAAU,CAACxB,EAAO,CAK/FO,CALiG,kBAK7E,kBACtB,GAAG,EAE4BL,CAAAA,EAAAA,EAAAA,CAAAA,CAAoBA,CAAC,CAClDC,sBAAwBC,KAClBA,EAAMC,WAAW,IAAID,EAAMC,WAAW,CAACoB,QAAQ,IAAIrB,EAAMC,WAAW,CAACoB,QAAQ,CAACzB,EAAO,CAK3FO,CAL6F,kBAKzE,iBACtB,GAAG,EAE8BL,CAAAA,EAAAA,EAAAA,CAAAA,CAAoBA,CAAC,CACpDC,sBAAuB,KACjBC,EAAMC,WAAW,IAAID,EAAMC,WAAW,CAACqB,WAAW,IAAItB,EAAMC,WAAW,CAACqB,WAAW,CAAC1B,EAAO,CAKjGO,CALmG,kBAK/E,mBACtB,GAAG,EAEwBL,CAAAA,EAAAA,EAAAA,CAAAA,CAAoBA,CAAC,CAC9CC,sBAAuB,KACjBC,EAAMC,WAAW,IAAID,EAAMC,WAAW,CAACsB,KAAK,IAAIvB,EAAMC,WAAW,CAACsB,KAAK,CAAC3B,EAAO,CAKrFO,CALuF,kBAKnE,aACtB,GAAG,EAE8BL,CAAAA,EAAAA,EAAAA,CAAAA,CAAoBA,CAAC,CACpDC,sBAAuB,KACjBC,EAAMC,WAAW,IAAID,EAAMC,WAAW,CAACuB,WAAW,IAAIxB,EAAMC,WAAW,CAACuB,WAAW,CAAC5B,EAAO,CAKjGO,CALmG,kBAK/E,mBACtB,GAAG,EAE8BL,CAAAA,EAAAA,EAAAA,CAAAA,CAAoBA,CAAC,CACpDC,sBAAuB,GACjBC,IAAMC,WAAW,IAAID,EAAMC,WAAW,CAACwB,YAAY,IAAIzB,EAAMC,WAAW,CAACwB,YAAY,CAAC7B,EAAO,CAKnGO,CALqG,kBAKjF,mBACtB,GAAG,EAE0BL,CAAAA,EAAAA,EAAAA,CAAAA,CAAoBA,CAAC,CAChDC,sBAAwBC,GACtB,EAAIA,EAAMC,WAAW,IAAID,EAAMC,WAAW,CAACyB,SAAS,IAAI1B,EAAMC,WAAW,CAACyB,SAAS,CAAC9B,EAAO,IAAII,EAAMC,WAAW,CAAC0B,OAAO,IAAI3B,EAAMC,WAAW,CAAC0B,OAAO,CAAC/B,EAAO,IAAGI,EAAMC,WAAW,CAAC2B,KAAK,IAAI5B,EAAMC,WAAW,CAAC2B,KAAK,CAAChC,EAAO,IAAGI,EAAMC,WAAW,CAAC4B,MAAM,IAAI7B,EAAMC,WAAW,CAAC4B,MAAM,CAACjC,EAAO,IAAGI,EAAMC,WAAW,CAACW,WAAW,IAAIZ,EAAMC,WAAW,CAACW,WAAW,CAAChB,EAAO,IAAGI,EAAMC,WAAW,CAAC6B,MAAM,IAAI9B,EAAMC,WAAW,CAAC6B,MAAM,CAAClC,EAAO,EAAE,CAG5Z,EAETO,mBAAoB,eACtB,GAEA,EAAeN,gBAAgBA,EAAC,2FC1LhC,SAASJ,EAASsC,CAAoB,EACpC,GAAM,GAAErG,CAAC,CAAE,CAAGC,CAAAA,EAAAA,EAAAA,EAAAA,CAAcA,CAAC,UACvBqG,EAA6B,CACjCC,gBAAiBvG,EAAE,cACnB,EACI,CACJqB,SAAO,MACPmB,CAAI,WACJjC,CAAS,uBACTiG,CAAqB,WACrBC,CAAS,oBACTC,CAAkB,qBAClB/D,CAAmB,kBACnBsB,CAAgB,aAChB0C,CAAW,oBACXC,CAAkB,gBAClBC,CAAc,SACdC,CAAO,WACP9C,CAAS,sBACT+C,CAAoB,mBACpBC,CAAiB,YACjBC,CAAU,QACVC,CAAM,kBACNC,CAAgB,cAChBC,CAAY,CAEZ,CADA,EACGC,EACJ,CAAGhB,EAGEiB,EAAiB,CACrBhB,6BACAiB,gBAAiBvH,EAAE,IAP0C,MAQ7DwH,UAAU,UACVnG,EACAmB,KAAMA,GAAQ,EAAE,CAChBiF,MAAO,GACPC,2BAA4BlB,EAC5BmB,UAAWlB,EACXmB,gBAAiBd,qBACjBJ,EACAmB,YAAY,EACZC,iBAAkB9D,EAClB+D,kBAAmBnB,GAA0C,GAC7DoB,eADwCpB,cACVD,GAA4B,CAAC,GAAI,GAAI,GAAI,CAA3BA,EAA+B,GAAG,CAC9EsB,oBAAqB1H,EACrB2H,oBAAqBvF,EACrBwF,aAAclE,iBACd4C,uBACAE,oBACAC,EACAoB,kBAAmB,UAACC,EAAAA,CAAYA,CAAAA,CAAAA,GAChCC,SAAU,UAACvG,IAAAA,CAAEC,UAAU,6CACvBiF,SACAC,eACAE,mBACAD,EACAnF,UAAW,WACb,EACA,MACE,UAACuG,EAAAA,EAASA,CAAAA,CAAE,GAAGjB,CAAc,EAEjC,CAEAvD,EAASyE,YAAY,CAAG,CACtBb,WAAW,EACXE,YAAY,EACZtH,UAAW,KACXyD,WAAW,EACX+C,qBAAsB,KACtBC,mBAAmB,EACnBC,YAAY,EACZE,kBAAkB,CACpB,EAEA,MAAepD,QAAQA,EAAC,mEChHT,SAAS0E,EAAgB1I,CAAW,EAC/C,MACE,UAAC0B,MAAAA,CAAIO,UAAU,sDACb,UAACP,MAAAA,CAAIO,UAAU,mBAAU,yCAG/B,mBCLF,4CACA,iCACA,WACA,OAAe,EAAQ,KAA4D,CACnF,EACA,SAFsB,qMCkDtB,MA1C2B,QAmCnBsC,EAAAA,EAlCN,GAAM,GAAEtE,CAAC,CAAE,CAAGC,CAAAA,EAAAA,EAAAA,EAAAA,CAAcA,CAAC,UACvByI,EAAyB,IAE3B,EAsC4B,CAtC5B,OAACjH,MAAAA,UACC,WAACkH,EAAAA,CAASA,CAAAA,CAACC,MAAO,CAAEC,UAAW,QAAS,EAAGC,KAAK,IAAC9G,UAAU,gBACzD,UAAC+G,EAAAA,CAAGA,CAAAA,UACF,UAACC,EAAAA,CAAGA,CAAAA,CAACC,GAAI,YACP,UAACC,EAAAA,CAAWA,CAAAA,CAACjI,MAAOjB,EAAE,kDAG1B,UAAC+I,EAAAA,CAAGA,CAAAA,UACF,UAACC,EAAAA,CAAGA,CAAAA,CAACC,GAAI,YACP,UAACvH,IAAIA,CACHC,KAAK,6BACLC,GAAG,OAFAF,wCAIH,UAACmC,EAAAA,CAAMA,CAAAA,CAACC,QAAQ,YAAYqF,KAAK,cAC9BnJ,EAAE,uDAKX,UAAC+I,EAAAA,CAAGA,CAAAA,CAAC/G,UAAU,gBACb,UAACgH,EAAAA,CAAGA,CAAAA,CAACC,GAAI,YACP,UAAC/I,EAAAA,OAAkBA,CAAAA,CAAAA,YAQzBkJ,EAAuBC,CAAAA,EAAAA,EAAAA,mBAAAA,CAAmBA,CAAC,IAAM,UAACX,EAAAA,CAAAA,IAClDpE,EAAYgF,CAAAA,EAAAA,EAAAA,EAAAA,CAAWA,CAAC,GAAWhF,SACzC,IAAI,GAAEA,GAAAA,OAAAA,EAAAA,EAAOC,IAAPD,OAAkB,GAAlBA,OAAAA,EAAAA,EAAAA,cAAoBiB,EAApBjB,KAAAA,EAAAA,CAAoC,CAAC,GAArCA,UAAkD,EAItD,CAJyD,EAIzD,OAAC8E,EAAAA,CAAAA,GAHM,UAACX,EAAAA,OAAeA,CAAAA,CAAAA,EAK3B,gECjDe,SAASS,EAAY7C,CAAuB,EACzD,MACE,UAACkD,KAAAA,CAAGvH,UAAU,wBAAgBqE,EAAMpF,KAAK,EAE7C", "sources": ["webpack://_N_E/./pages/adminsettings/projectstatuses/projectstatusTable.tsx", "webpack://_N_E/./pages/adminsettings/permissions.tsx", "webpack://_N_E/./components/common/RKITable.tsx", "webpack://_N_E/./pages/rNoAccess.tsx", "webpack://_N_E/?b64e", "webpack://_N_E/./pages/adminsettings/projectstatuses/index.tsx", "webpack://_N_E/./components/common/PageHeading.tsx"], "sourcesContent": ["//Import Library\r\nimport { useState, useEffect } from \"react\";\r\nimport Link from \"next/link\";\r\nimport { <PERSON><PERSON>, But<PERSON> } from \"react-bootstrap\";\r\n\r\nimport toast from 'react-hot-toast';\r\n\r\n//Import services/components\r\nimport RKITable from \"../../../components/common/RKITable\";\r\nimport apiService from \"../../../services/apiService\";\r\nimport { useTranslation } from 'next-i18next';\r\n\r\nconst ProjectstatusTable = (_props: any) => {\r\n    const { t } = useTranslation('common');\r\n    const [tabledata, setDataToTable] = useState([]);\r\n    const [, setLoading] = useState(false);\r\n    const [totalRows, setTotalRows] = useState(0);\r\n    const [perPage, setPerPage] = useState(10);\r\n    const [isModalShow, setModal] = useState(false);\r\n    const [selectProjectstatus, setSelectProjectstatus] = useState({});\r\n    \r\n    const projectstatusParams = {\r\n        sort: { title: \"asc\" },\r\n        limit: perPage,\r\n        page: 1,\r\n        query: {},\r\n    };\r\n\r\n    const columns = [\r\n        {\r\n            name: t(\"adminsetting.ProjectStatus.Title\"),\r\n            selector: \"title\",\r\n        },\r\n        {\r\n            name: t(\"adminsetting.ProjectStatus.Action\"),\r\n            selector: \"\",\r\n            cell: (d: any) => (\r\n                <div>\r\n                    <Link href=\"/adminsettings/[...routes]\" as={`/adminsettings/edit_projectstatus/${d._id}`}>\r\n\r\n                        <i className=\"icon fas fa-edit\" />\r\n\r\n                    </Link>\r\n                    &nbsp;\r\n                    <a onClick={() => userAction(d)}>\r\n                        <i className=\"icon fas fa-trash-alt\" />\r\n                    </a>{\" \"}\r\n                </div>\r\n            ),\r\n        },\r\n    ];\r\n\r\n    const getProjectstatusData = async () => {\r\n        setLoading(true);\r\n        const response = await apiService.get(\"/projectstatus\", projectstatusParams);\r\n        if (response && response.data && response.data.length > 0) {\r\n            setDataToTable(response.data);\r\n            setTotalRows(response.totalCount);\r\n            setLoading(false);\r\n        }\r\n    };\r\n\r\n    const handlePageChange = (page: any) => {\r\n        projectstatusParams.limit = perPage;\r\n        projectstatusParams.page = page;\r\n        getProjectstatusData();\r\n    };\r\n\r\n    const handlePerRowsChange = async (newPerPage: any, page: any) => {\r\n        projectstatusParams.limit = newPerPage;\r\n        projectstatusParams.page = page;\r\n        setLoading(true);\r\n        const response = await apiService.get(\"/projectstatus\", projectstatusParams);\r\n        if (response && response.data && response.data.length > 0) {\r\n            setDataToTable(response.data);\r\n            setPerPage(newPerPage);\r\n            setLoading(false);\r\n        }\r\n    };\r\n\r\n    const userAction = async (row: any) => {\r\n        setSelectProjectstatus(row._id);\r\n        setModal(true);\r\n    };\r\n\r\n    const modalConfirm = async () => {\r\n        try {\r\n            await apiService.remove(`/projectstatus/${selectProjectstatus}`);\r\n            getProjectstatusData();\r\n            setModal(false);\r\n            toast.success(t(\"adminsetting.ProjectStatus.Table.projectStatusDeletedSuccessfully\"));\r\n        } catch (error) {\r\n            toast.error(t(\"adminsetting.ProjectStatus.Table.errorDeletingProjectStatus\"));\r\n        }\r\n    };\r\n\r\n    const modalHide = () => setModal(false);\r\n\r\n    useEffect(() => {\r\n        getProjectstatusData();\r\n    }, []);\r\n\r\n    return (\r\n        <div>\r\n            <Modal show={isModalShow} onHide={modalHide}>\r\n                <Modal.Header closeButton>\r\n                    <Modal.Title>{t(\"adminsetting.ProjectStatus.DeleteProjectstatus\")}</Modal.Title>\r\n                </Modal.Header>\r\n                <Modal.Body>{t(\"adminsetting.ProjectStatus.Areyousurewanttodeletethisprojectstatus\")}</Modal.Body>\r\n                <Modal.Footer>\r\n                    <Button variant=\"secondary\" onClick={modalHide}>\r\n                        {t(\"adminsetting.ProjectStatus.Cancel\")}\r\n                    </Button>\r\n                    <Button variant=\"primary\" onClick={modalConfirm}>\r\n                        {t(\"adminsetting.ProjectStatus.Yes\")}\r\n                    </Button>\r\n                </Modal.Footer>\r\n            </Modal>\r\n\r\n            <RKITable\r\n                columns={columns}\r\n                data={tabledata}\r\n                totalRows={totalRows}\r\n                pagServer={true}\r\n                handlePerRowsChange={handlePerRowsChange}\r\n                handlePageChange={handlePageChange}\r\n            />\r\n        </div>\r\n    );\r\n};\r\n\r\nexport default ProjectstatusTable;\r\n", "//Import services/components\r\nimport connectedAuthWrapper from 'redux-auth-wrapper/connectedAuthWrapper';\r\n\r\nconst create = \"create:any\";\r\nexport const canAddAreaOfWork = connectedAuthWrapper({\r\n  authenticatedSelector: (state) => {\r\n    if (state.permissions && state.permissions.area_of_work && state.permissions.area_of_work[create]) {\r\n      return true;\r\n    }\r\n    return false;\r\n  },\r\n  wrapperDisplayName: 'CanAddAreaOfWork',\r\n});\r\n\r\nexport const canAddCountry = connectedAuthWrapper({\r\n  authenticatedSelector: (state) => {\r\n    if (state.permissions && state.permissions.country && state.permissions.country[create]) {\r\n      return true;\r\n    }\r\n    return false;\r\n  },\r\n  wrapperDisplayName: 'CanAddCountry',\r\n});\r\n\r\nexport const canAddDeploymentStatus = connectedAuthWrapper({\r\n  authenticatedSelector: (state) => {\r\n    if (state.permissions && state.permissions.deployment_status && state.permissions.deployment_status[create]) {\r\n      return true;\r\n    }\r\n    return false;\r\n  },\r\n  wrapperDisplayName: 'CanAddDeploymentStatus',\r\n});\r\n\r\nexport const canAddEventStatus = connectedAuthWrapper({\r\n  authenticatedSelector: (state) => {\r\n    if (state.permissions && state.permissions.event_status && state.permissions.event_status[create]) {\r\n      return true;\r\n    }\r\n    return false;\r\n  },\r\n  wrapperDisplayName: 'CanAddEventStatus',\r\n});\r\n\r\nexport const canAddExpertise = connectedAuthWrapper({\r\n  authenticatedSelector: (state) => {\r\n    if (state.permissions && state.permissions.expertise && state.permissions.expertise[create]) {\r\n      return true;\r\n    }\r\n    return false;\r\n  },\r\n  wrapperDisplayName: 'CanAddExpertise',\r\n});\r\n\r\nexport const canAddFocalPointApproval = connectedAuthWrapper({\r\n  authenticatedSelector: (state) => {\r\n    if (state.permissions && state.permissions.institution_focal_point && state.permissions.institution_focal_point[create]) {\r\n      return true;\r\n    }\r\n    return false;\r\n  },\r\n  wrapperDisplayName: 'CanAddFocalPointApproval',\r\n});\r\n\r\nexport const canAddVspaceApproval = connectedAuthWrapper({\r\n  authenticatedSelector: (state) => {\r\n    if (state.permissions && state.permissions.institution_focal_point && state.permissions.institution_focal_point[create]) {\r\n      return true;\r\n    }\r\n    return false;\r\n  },\r\n  wrapperDisplayName: 'CanAddVspaceApproval',\r\n});\r\nexport const canAddHazards = connectedAuthWrapper({\r\n  authenticatedSelector: (state) => {\r\n    if (state.permissions && state.permissions.hazard && state.permissions.hazard[create]) {\r\n      return true;\r\n    }\r\n    return false;\r\n  },\r\n  wrapperDisplayName: 'CanAddHazards',\r\n});\r\n\r\nexport const canAddHazardTypes = connectedAuthWrapper({\r\n  authenticatedSelector: (state) => {\r\n    if (state.permissions && state.permissions.hazard_type && state.permissions.hazard_type[create]) {\r\n      return true;\r\n    }\r\n    return false;\r\n  },\r\n  wrapperDisplayName: 'CanAddHazardTypes',\r\n}); \r\n\r\nexport const canAddOrganisationApproval = connectedAuthWrapper({\r\n  authenticatedSelector: (state) => {\r\n    if (state.permissions && state.permissions.institution && state.permissions.institution[create]) {\r\n      return true;\r\n    }\r\n    return false;\r\n  },\r\n  wrapperDisplayName: 'CanAddOrganisationApproval',\r\n}); \r\n\r\nexport const canAddOrganisationNetworks = connectedAuthWrapper({\r\n  authenticatedSelector: (state) => {\r\n    if (state.permissions && state.permissions.institution_network && state.permissions.institution_network[create]) {\r\n      return true;\r\n    }\r\n    return false;\r\n  },\r\n  wrapperDisplayName: 'CanAddOrganisationNetworks',\r\n});\r\n\r\nexport const canAddOrganisationTypes = connectedAuthWrapper({\r\n  authenticatedSelector: (state) => {\r\n    if (state.permissions && state.permissions.institution_type && state.permissions.institution_type[create]) {\r\n      return true;\r\n    }\r\n    return false;\r\n  },\r\n  wrapperDisplayName: 'CanAddOrganisationTypes',\r\n});\r\n\r\nexport const canAddOperationStatus = connectedAuthWrapper({\r\n  authenticatedSelector: (state) => {\r\n    if (state.permissions && state.permissions.operation_status && state.permissions.operation_status[create]) {\r\n      return true;\r\n    }\r\n    return false;\r\n  },\r\n  wrapperDisplayName: 'CanAddOperationStatus',\r\n});\r\n\r\nexport const canAddProjectStatus = connectedAuthWrapper({\r\n  authenticatedSelector: (state) => {\r\n    if (state.permissions && state.permissions.project_status && state.permissions.project_status[create]) {\r\n      return true;\r\n    }\r\n    return false;\r\n  },\r\n  wrapperDisplayName: 'CanAddProjectStatus',\r\n});\r\n\r\nexport const canAddRegions = connectedAuthWrapper({\r\n  authenticatedSelector: (state) => {\r\n    if (state.permissions && state.permissions.region && state.permissions.region[create]) {\r\n      return true;\r\n    }\r\n    return false;\r\n  },\r\n  wrapperDisplayName: 'CanAddRegions',\r\n});\r\n\r\nexport const canAddRiskLevels = connectedAuthWrapper({\r\n  authenticatedSelector: (state) => {\r\n    if (state.permissions && state.permissions.risk_level && state.permissions.risk_level[create]) {\r\n      return true;\r\n    }\r\n    return false;\r\n  },\r\n  wrapperDisplayName: 'CanAddRiskLevels',\r\n});\r\n\r\nexport const canAddSyndromes = connectedAuthWrapper({\r\n  authenticatedSelector: (state) => {\r\n    if (state.permissions && state.permissions.syndrome && state.permissions.syndrome[create]) {\r\n      return true;\r\n    }\r\n    return false;\r\n  },\r\n  wrapperDisplayName: 'CanAddSyndromes',\r\n});\r\n\r\nexport const canAddUpdateTypes = connectedAuthWrapper({\r\n  authenticatedSelector: (state) => {\r\n    if (state.permissions && state.permissions.update_type && state.permissions.update_type[create]) {\r\n      return true;\r\n    }\r\n    return false;\r\n  },\r\n  wrapperDisplayName: 'CanAddUpdateTypes',\r\n});\r\n\r\nexport const canAddUsers = connectedAuthWrapper({\r\n  authenticatedSelector: (state) => {\r\n    if (state.permissions && state.permissions.users && state.permissions.users[create]) {\r\n      return true;\r\n    }\r\n    return false;\r\n  },\r\n  wrapperDisplayName: 'CanAddUsers',\r\n});\r\n\r\nexport const canAddWorldRegion = connectedAuthWrapper({\r\n  authenticatedSelector: (state) => {\r\n    if (state.permissions && state.permissions.worl_region && state.permissions.worl_region[create]) {\r\n      return true;\r\n    }\r\n    return false;\r\n  },\r\n  wrapperDisplayName: 'CanAddWorldRegion',\r\n});\r\n\r\nexport const canAddLandingPage = connectedAuthWrapper({\r\n  authenticatedSelector: (state) => {\r\n    if (state.permissions && state.permissions.landing_page && state.permissions.landing_page[create]) {\r\n      return true;\r\n    }\r\n    return false;\r\n  },\r\n  wrapperDisplayName: 'CanAddLandingPage',\r\n});\r\n\r\nexport const canAddContent = connectedAuthWrapper({\r\n  authenticatedSelector: (state) => {\r\n    if (state.permissions && state.permissions.operation && state.permissions.operation[create] && state.permissions.project && state.permissions.project[create]&& state.permissions.event && state.permissions.event[create]&& state.permissions.vspace && state.permissions.vspace[create]&& state.permissions.institution && state.permissions.institution[create]&& state.permissions.update && state.permissions.update[create]) {\r\n      return true;\r\n    }\r\n    return false;\r\n  },\r\n  wrapperDisplayName: 'CanAddContent',\r\n});\r\n\r\nexport default canAddAreaOfWork;", "//Import Library\r\nimport React from 'react'\r\nimport DataTable  from 'react-data-table-component';\r\n\r\n//Import services/components\r\nimport { useTranslation } from 'next-i18next';\r\nimport CustomLoader from './CustomLoader';\r\n\r\ninterface RKITableProps {\r\n  columns: any[];\r\n  data: any[];\r\n  totalRows?: number;\r\n  resetPaginationToggle?: boolean;\r\n  subheader?: boolean;\r\n  subHeaderComponent?: React.ReactNode;\r\n  handlePerRowsChange?: (newPerPage: number, page: number) => void;\r\n  handlePageChange?: (page: number) => void;\r\n  rowsPerPage?: number[];\r\n  defaultRowsPerPage?: number;\r\n  selectableRows?: boolean;\r\n  loading?: boolean;\r\n  pagServer?: boolean;\r\n  onSelectedRowsChange?: (selectedRows: any) => void;\r\n  clearSelectedRows?: boolean;\r\n  sortServer?: boolean;\r\n  onSort?: (column: any, sortDirection: string) => void;\r\n  persistTableHead?: boolean;\r\n  sortFunction?: (rows: any[], field: any, direction: 'asc' | 'desc') => any[];\r\n  noHeader?: boolean;\r\n  dense?: boolean;\r\n  pagination?: boolean;\r\n  paginationServer?: boolean;\r\n  paginationTotalRows?: number;\r\n  subHeaderAlign?: string;\r\n  [key: string]: any;\r\n}\r\n\r\nfunction RKITable(props: RKITableProps) {\r\n  const { t } = useTranslation('common');\r\n  const paginationComponentOptions = {\r\n    rowsPerPageText: t('Rowsperpage'),\r\n    };\r\n  const {\r\n    columns,\r\n    data,\r\n    totalRows,\r\n    resetPaginationToggle,\r\n    subheader,\r\n    subHeaderComponent,\r\n    handlePerRowsChange,\r\n    handlePageChange,\r\n    rowsPerPage,\r\n    defaultRowsPerPage,\r\n    selectableRows,\r\n    loading,\r\n    pagServer,\r\n    onSelectedRowsChange,\r\n    clearSelectedRows,\r\n    sortServer,\r\n    onSort,\r\n    persistTableHead,\r\n    sortFunction,\r\n    // Filter out any unknown props that might cause DOM warnings\r\n    ...otherProps\r\n  } = props;\r\n\r\n  // Only pass known DataTable props to avoid DOM warnings\r\n  const dataTableProps = {\r\n    paginationComponentOptions,\r\n    noDataComponent: t(\"NoData\"),\r\n    noHeader: true,\r\n    columns,\r\n    data: data || [],\r\n    dense: true,\r\n    paginationResetDefaultPage: resetPaginationToggle,\r\n    subHeader: subheader,\r\n    progressPending: loading,\r\n    subHeaderComponent,\r\n    pagination: true,\r\n    paginationServer: pagServer,\r\n    paginationPerPage: defaultRowsPerPage ? defaultRowsPerPage : 10,\r\n    paginationRowsPerPageOptions: rowsPerPage ? rowsPerPage : [10, 15, 20, 25, 30],\r\n    paginationTotalRows: totalRows,\r\n    onChangeRowsPerPage: handlePerRowsChange,\r\n    onChangePage: handlePageChange,\r\n    selectableRows,\r\n    onSelectedRowsChange,\r\n    clearSelectedRows,\r\n    progressComponent: <CustomLoader/>,\r\n    sortIcon: <i className=\"sort-icon fas fa-exchange-alt\" />,\r\n    sortServer,\r\n    onSort,\r\n    sortFunction,\r\n    persistTableHead,\r\n    className: \"rki-table\"\r\n  };\r\n  return (\r\n    <DataTable {...dataTableProps} />\r\n  )\r\n};\r\n\r\nRKITable.defaultProps = {\r\n  subHeader: false,\r\n  pagination: true,\r\n  totalRows: null,\r\n  pagServer: true,\r\n  onSelectedRowsChange: null,\r\n  clearSelectedRows: false,\r\n  sortServer: false,\r\n  persistTableHead: false\r\n}\r\n\r\nexport default RKITable;\r\n\r\n", "export default function NoAccessMessage(_props: any) {\r\n    return (\r\n      <div className=\"container-fluid p-0 response-message-block\">\r\n        <div className=\"message\">you don't have permission to access</div>\r\n      </div>\r\n    )\r\n  }", "\n    (window.__NEXT_P = window.__NEXT_P || []).push([\n      \"/adminsettings/projectstatuses\",\n      function () {\n        return require(\"private-next-pages/adminsettings/projectstatuses/index.tsx\");\n      }\n    ]);\n    if(module.hot) {\n      module.hot.dispose(function () {\n        window.__NEXT_P.push([\"/adminsettings/projectstatuses\"])\n      });\n    }\n  ", "//Import Library\r\nimport { Container, <PERSON>, <PERSON>, Button } from \"react-bootstrap\";\r\nimport Link from \"next/link\";\r\n\r\n//Import services/components\r\nimport PageHeading from \"../../../components/common/PageHeading\";\r\nimport ProjectstatusTable from \"./projectstatusTable\";\r\nimport { useTranslation } from 'next-i18next';\r\nimport { canAddProjectStatus } from \"../permissions\";\r\nimport { useSelector } from \"react-redux\";\r\nimport NoAccessMessage from \"../../rNoAccess\";\r\n\r\nconst ProjectstatusIndex = (_props: any) => {\r\n  const { t } = useTranslation('common');\r\n  const ShowProjectstatusIndex = () => {\r\n    return (\r\n      <div>\r\n        <Container style={{ overflowX: \"hidden\" }} fluid className=\"p-0\">\r\n          <Row>\r\n            <Col xs={12}>\r\n              <PageHeading title={t(\"adminsetting.ProjectStatus.ProjectStatus\")} />\r\n            </Col>\r\n          </Row>\r\n          <Row>\r\n            <Col xs={12}>\r\n              <Link\r\n                href=\"/adminsettings/[...routes]\"\r\n                as=\"/adminsettings/create_projectstatus\"\r\n                >\r\n                <Button variant=\"secondary\" size=\"sm\">\r\n                  {t(\"adminsetting.ProjectStatus.AddProjectStatus\")}\r\n              </Button>\r\n              </Link>\r\n            </Col>\r\n          </Row>\r\n          <Row className=\"mt-3\">\r\n            <Col xs={12}>\r\n              <ProjectstatusTable />\r\n            </Col>\r\n          </Row>\r\n        </Container>\r\n      </div>\r\n    );\r\n  }\r\n\r\n  const ShowAddProjectStatus = canAddProjectStatus(() => <ShowProjectstatusIndex />);\r\n  const state:any = useSelector((state) => state);\r\n  if (!(state?.permissions?.project_status?.['create:any'])) {\r\n    return <NoAccessMessage />\r\n  }\r\n  return(\r\n    <ShowAddProjectStatus />\r\n  )  \r\n}\r\nexport default ProjectstatusIndex;", "interface PageHeadingProps {\r\n  title: string; // Required based on actual usage\r\n}\r\n\r\nexport default function PageHeading(props: PageHeadingProps) {\r\n  return (\r\n    <h2 className=\"page-heading\">{props.title}</h2>\r\n  )\r\n}\r\n"], "names": ["_props", "t", "useTranslation", "ProjectstatusTable", "tabledata", "setDataToTable", "useState", "setLoading", "totalRows", "setTotalRows", "perPage", "setPerPage", "isModalShow", "setModal", "selectProjectstatus", "setSelectProjectstatus", "projectstatusParams", "sort", "title", "limit", "page", "query", "columns", "name", "selector", "cell", "div", "Link", "href", "as", "d", "_id", "i", "className", "a", "onClick", "userAction", "getProjectstatusData", "response", "apiService", "get", "data", "length", "totalCount", "handlePerRowsChange", "newPerPage", "row", "modalConfirm", "remove", "toast", "success", "error", "modalHide", "useEffect", "Modal", "show", "onHide", "Header", "closeButton", "Title", "Body", "Footer", "<PERSON><PERSON>", "variant", "RKITable", "pagServer", "handlePageChange", "create", "canAddAreaOfWork", "connectedAuthWrapper", "authenticatedSelector", "state", "permissions", "area_of_work", "wrapperDisplayName", "country", "deployment_status", "event_status", "canAddExpertise", "expertise", "institution_focal_point", "hazard", "hazard_type", "institution", "institution_network", "institution_type", "canAddOperationStatus", "operation_status", "project_status", "region", "canAddRiskLevels", "risk_level", "syndrome", "update_type", "users", "worl_region", "landing_page", "operation", "project", "event", "vspace", "update", "props", "paginationComponentOptions", "rowsPerPageText", "resetPaginationToggle", "subheader", "subHeaderComponent", "rowsPerPage", "defaultRowsPerPage", "selectableRows", "loading", "onSelectedRowsChange", "clearSelectedRows", "sortServer", "onSort", "persistTableHead", "sortFunction", "otherProps", "dataTableProps", "noDataComponent", "<PERSON><PERSON><PERSON><PERSON>", "dense", "paginationResetDefaultPage", "subHeader", "progressPending", "pagination", "paginationServer", "paginationPerPage", "paginationRowsPerPageOptions", "paginationTotalRows", "onChangeRowsPerPage", "onChangePage", "progressComponent", "CustomLoader", "sortIcon", "DataTable", "defaultProps", "NoAccessMessage", "ShowProjectstatusIndex", "Container", "style", "overflowX", "fluid", "Row", "Col", "xs", "PageHeading", "size", "ShowAddProjectStatus", "canAddProjectStatus", "useSelector", "h2"], "sourceRoot": "", "ignoreList": []}