"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[9875],{26188:(e,r,a)=>{a.d(r,{A:()=>b});var t=a(15039),s=a.n(t),l=a(14232),n=a(22631),o=a(76959),i=a(77346),d=a(46052),c=a(37876);let u=(0,d.A)("h4");u.displayName="DivStyledAsH4";let m=l.forwardRef((e,r)=>{let{className:a,bsPrefix:t,as:l=u,...n}=e;return t=(0,i.oU)(t,"alert-heading"),(0,c.jsx)(l,{ref:r,className:s()(a,t),...n})});m.displayName="AlertHeading";var p=a(10401);let h=l.forwardRef((e,r)=>{let{className:a,bsPrefix:t,as:l=p.A,...n}=e;return t=(0,i.oU)(t,"alert-link"),(0,c.jsx)(l,{ref:r,className:s()(a,t),...n})});h.displayName="AlertLink";var f=a(72788),x=a(12054);let v=l.forwardRef((e,r)=>{let{bsPrefix:a,show:t=!0,closeLabel:l="Close alert",closeVariant:d,className:u,children:m,variant:p="primary",onClose:h,dismissible:v,transition:b=f.A,...g}=(0,n.Zw)(e,{show:"onClose"}),y=(0,i.oU)(a,"alert"),w=(0,o.A)(e=>{h&&h(!1,e)}),N=!0===b?f.A:b,j=(0,c.jsxs)("div",{role:"alert",...!N?g:void 0,ref:r,className:s()(u,y,p&&"".concat(y,"-").concat(p),v&&"".concat(y,"-dismissible")),children:[v&&(0,c.jsx)(x.A,{onClick:w,"aria-label":l,variant:d}),m]});return N?(0,c.jsx)(N,{unmountOnExit:!0,...g,ref:void 0,in:t,children:j}):t?j:null});v.displayName="Alert";let b=Object.assign(v,{Link:h,Heading:m})},29335:(e,r,a)=>{a.d(r,{A:()=>N});var t=a(15039),s=a.n(t),l=a(14232),n=a(77346),o=a(37876);let i=l.forwardRef((e,r)=>{let{className:a,bsPrefix:t,as:l="div",...i}=e;return t=(0,n.oU)(t,"card-body"),(0,o.jsx)(l,{ref:r,className:s()(a,t),...i})});i.displayName="CardBody";let d=l.forwardRef((e,r)=>{let{className:a,bsPrefix:t,as:l="div",...i}=e;return t=(0,n.oU)(t,"card-footer"),(0,o.jsx)(l,{ref:r,className:s()(a,t),...i})});d.displayName="CardFooter";var c=a(81764);let u=l.forwardRef((e,r)=>{let{bsPrefix:a,className:t,as:i="div",...d}=e,u=(0,n.oU)(a,"card-header"),m=(0,l.useMemo)(()=>({cardHeaderBsPrefix:u}),[u]);return(0,o.jsx)(c.A.Provider,{value:m,children:(0,o.jsx)(i,{ref:r,...d,className:s()(t,u)})})});u.displayName="CardHeader";let m=l.forwardRef((e,r)=>{let{bsPrefix:a,className:t,variant:l,as:i="img",...d}=e,c=(0,n.oU)(a,"card-img");return(0,o.jsx)(i,{ref:r,className:s()(l?"".concat(c,"-").concat(l):c,t),...d})});m.displayName="CardImg";let p=l.forwardRef((e,r)=>{let{className:a,bsPrefix:t,as:l="div",...i}=e;return t=(0,n.oU)(t,"card-img-overlay"),(0,o.jsx)(l,{ref:r,className:s()(a,t),...i})});p.displayName="CardImgOverlay";let h=l.forwardRef((e,r)=>{let{className:a,bsPrefix:t,as:l="a",...i}=e;return t=(0,n.oU)(t,"card-link"),(0,o.jsx)(l,{ref:r,className:s()(a,t),...i})});h.displayName="CardLink";var f=a(46052);let x=(0,f.A)("h6"),v=l.forwardRef((e,r)=>{let{className:a,bsPrefix:t,as:l=x,...i}=e;return t=(0,n.oU)(t,"card-subtitle"),(0,o.jsx)(l,{ref:r,className:s()(a,t),...i})});v.displayName="CardSubtitle";let b=l.forwardRef((e,r)=>{let{className:a,bsPrefix:t,as:l="p",...i}=e;return t=(0,n.oU)(t,"card-text"),(0,o.jsx)(l,{ref:r,className:s()(a,t),...i})});b.displayName="CardText";let g=(0,f.A)("h5"),y=l.forwardRef((e,r)=>{let{className:a,bsPrefix:t,as:l=g,...i}=e;return t=(0,n.oU)(t,"card-title"),(0,o.jsx)(l,{ref:r,className:s()(a,t),...i})});y.displayName="CardTitle";let w=l.forwardRef((e,r)=>{let{bsPrefix:a,className:t,bg:l,text:d,border:c,body:u=!1,children:m,as:p="div",...h}=e,f=(0,n.oU)(a,"card");return(0,o.jsx)(p,{ref:r,...h,className:s()(t,f,l&&"bg-".concat(l),d&&"text-".concat(d),c&&"border-".concat(c)),children:u?(0,o.jsx)(i,{children:m}):m})});w.displayName="Card";let N=Object.assign(w,{Img:m,Title:y,Subtitle:v,Body:i,Link:h,Text:b,Header:u,Footer:d,ImgOverlay:p})},67814:(e,r,a)=>{a.d(r,{KF:()=>j});var t=a(14232),s=a(37876);!function(e,{insertAt:r}={}){if(!e||typeof document>"u")return;let a=document.head||document.getElementsByTagName("head")[0],t=document.createElement("style");t.type="text/css","top"===r&&a.firstChild?a.insertBefore(t,a.firstChild):a.appendChild(t),t.styleSheet?t.styleSheet.cssText=e:t.appendChild(document.createTextNode(e))}(`.rmsc{--rmsc-main: #4285f4;--rmsc-hover: #f1f3f5;--rmsc-selected: #e2e6ea;--rmsc-border: #ccc;--rmsc-gray: #aaa;--rmsc-bg: #fff;--rmsc-p: 10px;--rmsc-radius: 4px;--rmsc-h: 38px}.rmsc *{box-sizing:border-box;transition:all .2s ease}.rmsc .gray{color:var(--rmsc-gray)}.rmsc .dropdown-content{position:absolute;z-index:1;top:100%;width:100%;padding-top:8px}.rmsc .dropdown-content .panel-content{overflow:hidden;border-radius:var(--rmsc-radius);background:var(--rmsc-bg);box-shadow:0 0 0 1px #0000001a,0 4px 11px #0000001a}.rmsc .dropdown-container{position:relative;outline:0;background-color:var(--rmsc-bg);border:1px solid var(--rmsc-border);border-radius:var(--rmsc-radius)}.rmsc .dropdown-container[aria-disabled=true]:focus-within{box-shadow:var(--rmsc-gray) 0 0 0 1px;border-color:var(--rmsc-gray)}.rmsc .dropdown-container:focus-within{box-shadow:var(--rmsc-main) 0 0 0 1px;border-color:var(--rmsc-main)}.rmsc .dropdown-heading{position:relative;padding:0 var(--rmsc-p);display:flex;align-items:center;width:100%;height:var(--rmsc-h);cursor:default;outline:0}.rmsc .dropdown-heading .dropdown-heading-value{overflow:hidden;text-overflow:ellipsis;white-space:nowrap;flex:1}.rmsc .clear-selected-button{cursor:pointer;background:none;border:0;padding:0;display:flex}.rmsc .options{max-height:260px;overflow-y:auto;margin:0;padding-left:0}.rmsc .options li{list-style:none;margin:0}.rmsc .select-item{box-sizing:border-box;cursor:pointer;display:block;padding:var(--rmsc-p);outline-offset:-1px;outline-color:var(--rmsc-primary)}.rmsc .select-item:hover{background:var(--rmsc-hover)}.rmsc .select-item.selected{background:var(--rmsc-selected)}.rmsc .no-options{padding:var(--rmsc-p);text-align:center;color:var(--rmsc-gray)}.rmsc .search{width:100%;position:relative;border-bottom:1px solid var(--rmsc-border)}.rmsc .search input{background:none;height:var(--rmsc-h);padding:0 var(--rmsc-p);width:100%;outline:0;border:0;font-size:1em}.rmsc .search input:focus{background:var(--rmsc-hover)}.rmsc .search-clear-button{cursor:pointer;position:absolute;top:0;right:0;bottom:0;background:none;border:0;padding:0 calc(var(--rmsc-p) / 2)}.rmsc .search-clear-button [hidden]{display:none}.rmsc .item-renderer{display:flex;align-items:baseline}.rmsc .item-renderer input{margin:0 5px 0 0}.rmsc .item-renderer.disabled{opacity:.5}.rmsc .spinner{animation:rotate 2s linear infinite}.rmsc .spinner .path{stroke:var(--rmsc-border);stroke-width:4px;stroke-linecap:round;animation:dash 1.5s ease-in-out infinite}@keyframes rotate{to{transform:rotate(360deg)}}@keyframes dash{0%{stroke-dasharray:1,150;stroke-dashoffset:0}50%{stroke-dasharray:90,150;stroke-dashoffset:-35}to{stroke-dasharray:90,150;stroke-dashoffset:-124}}
`);var l={allItemsAreSelected:"All items are selected.",clearSearch:"Clear Search",clearSelected:"Clear Selected",noOptions:"No options",search:"Search",selectAll:"Select All",selectAllFiltered:"Select All (Filtered)",selectSomeItems:"Select...",create:"Create"},n={value:[],hasSelectAll:!0,className:"multi-select",debounceDuration:200,options:[]},o=t.createContext({}),i=({props:e,children:r})=>{let[a,i]=(0,t.useState)(e.options);return(0,t.useEffect)(()=>{i(e.options)},[e.options]),(0,s.jsx)(o.Provider,{value:{t:r=>{var a;return(null==(a=e.overrideStrings)?void 0:a[r])||l[r]},...n,...e,options:a,setOptions:i},children:r})},d=()=>t.useContext(o),c={when:!0,eventTypes:["keydown"]};function u(e,r,a){let s=(0,t.useMemo)(()=>Array.isArray(e)?e:[e],[e]),l=Object.assign({},c,a),{when:n,eventTypes:o}=l,i=(0,t.useRef)(r),{target:d}=l;(0,t.useEffect)(()=>{i.current=r});let u=(0,t.useCallback)(e=>{s.some(r=>e.key===r||e.code===r)&&i.current(e)},[s]);(0,t.useEffect)(()=>{if(n&&"u">typeof window){let e=d?d.current:window;return o.forEach(r=>{e&&e.addEventListener(r,u)}),()=>{o.forEach(r=>{e&&e.removeEventListener(r,u)})}}},[n,o,s,d,r])}var m={ARROW_DOWN:"ArrowDown",ARROW_UP:"ArrowUp",ENTER:"Enter",ESCAPE:"Escape",SPACE:"Space"},p=(e,r)=>{let a;return function(...t){clearTimeout(a),a=setTimeout(()=>{e.apply(null,t)},r)}},h=()=>(0,s.jsxs)("svg",{width:"24",height:"24",fill:"none",stroke:"currentColor",strokeWidth:"2",className:"dropdown-search-clear-icon gray",children:[(0,s.jsx)("line",{x1:"18",y1:"6",x2:"6",y2:"18"}),(0,s.jsx)("line",{x1:"6",y1:"6",x2:"18",y2:"18"})]}),f=({checked:e,option:r,onClick:a,disabled:t})=>(0,s.jsxs)("div",{className:`item-renderer ${t?"disabled":""}`,children:[(0,s.jsx)("input",{type:"checkbox",onChange:a,checked:e,tabIndex:-1,disabled:t}),(0,s.jsx)("span",{children:r.label})]}),x=({itemRenderer:e=f,option:r,checked:a,tabIndex:l,disabled:n,onSelectionChanged:o,onClick:i})=>{let d=(0,t.useRef)(),c=()=>{n||o(!a)};return u([m.ENTER,m.SPACE],e=>{c(),e.preventDefault()},{target:d}),(0,s.jsx)("label",{className:`select-item ${a?"selected":""}`,role:"option","aria-selected":a,tabIndex:l,ref:d,children:(0,s.jsx)(e,{option:r,checked:a,onClick:e=>{c(),i(e)},disabled:n})})},v=({options:e,onClick:r,skipIndex:a})=>{let{disabled:t,value:l,onChange:n,ItemRenderer:o}=d(),i=(e,r)=>{t||n(r?[...l,e]:l.filter(r=>r.value!==e.value))};return(0,s.jsx)(s.Fragment,{children:e.map((e,n)=>{let d=n+a;return(0,s.jsx)("li",{children:(0,s.jsx)(x,{tabIndex:d,option:e,onSelectionChanged:r=>i(e,r),checked:!!l.find(r=>r.value===e.value),onClick:e=>r(e,d),itemRenderer:o,disabled:e.disabled||t})},(null==e?void 0:e.key)||n)})})},b=()=>{let{t:e,onChange:r,options:a,setOptions:l,value:n,filterOptions:o,ItemRenderer:i,disabled:c,disableSearch:f,hasSelectAll:b,ClearIcon:g,debounceDuration:y,isCreatable:w,onCreateOption:N}=d(),j=(0,t.useRef)(),C=(0,t.useRef)(),[k,A]=(0,t.useState)(""),[R,S]=(0,t.useState)(a),[E,O]=(0,t.useState)(""),[I,T]=(0,t.useState)(0),U=(0,t.useCallback)(p(e=>O(e),y),[]),P=(0,t.useMemo)(()=>{let e=0;return f||(e+=1),b&&(e+=1),e},[f,b]),_={label:e(k?"selectAllFiltered":"selectAll"),value:""},W=e=>{let r=R.filter(e=>!e.disabled).map(e=>e.value);if(e){let e=[...n.map(e=>e.value),...r];return(o?R:a).filter(r=>e.includes(r.value))}return n.filter(e=>!r.includes(e.value))},M=()=>{var e;O(""),A(""),null==(e=null==C?void 0:C.current)||e.focus()},D=e=>T(e);u([m.ARROW_DOWN,m.ARROW_UP],e=>{switch(e.code){case m.ARROW_UP:B(-1);break;case m.ARROW_DOWN:B(1);break;default:return}e.stopPropagation(),e.preventDefault()},{target:j});let L=async()=>{let e={label:k,value:k,__isNew__:!0};N&&(e=await N(k)),l([e,...a]),M(),r([...n,e])},F=async()=>o?await o(a,E):function(e,r){return r?e.filter(({label:e,value:a})=>null!=e&&null!=a&&e.toLowerCase().includes(r.toLowerCase())):e}(a,E),B=e=>{let r=I+e;T(r=Math.min(r=Math.max(0,r),a.length+Math.max(P-1,0)))};(0,t.useEffect)(()=>{var e,r;null==(r=null==(e=null==j?void 0:j.current)?void 0:e.querySelector(`[tabIndex='${I}']`))||r.focus()},[I]);let[H,$]=(0,t.useMemo)(()=>{let e=R.filter(e=>!e.disabled);return[e.every(e=>-1!==n.findIndex(r=>r.value===e.value)),0!==e.length]},[R,n]);(0,t.useEffect)(()=>{F().then(S)},[E,a]);let z=(0,t.useRef)();u([m.ENTER],L,{target:z});let q=w&&k&&!R.some(e=>(null==e?void 0:e.value)===k);return(0,s.jsxs)("div",{className:"select-panel",role:"listbox",ref:j,children:[!f&&(0,s.jsxs)("div",{className:"search",children:[(0,s.jsx)("input",{placeholder:e("search"),type:"text","aria-describedby":e("search"),onChange:e=>{U(e.target.value),A(e.target.value),T(0)},onFocus:()=>{T(0)},value:k,ref:C,tabIndex:0}),(0,s.jsx)("button",{type:"button",className:"search-clear-button",hidden:!k,onClick:M,"aria-label":e("clearSearch"),children:g||(0,s.jsx)(h,{})})]}),(0,s.jsxs)("ul",{className:"options",children:[b&&$&&(0,s.jsx)(x,{tabIndex:+(1!==P),checked:H,option:_,onSelectionChanged:e=>{r(W(e))},onClick:()=>D(1),itemRenderer:i,disabled:c}),R.length?(0,s.jsx)(v,{skipIndex:P,options:R,onClick:(e,r)=>D(r)}):q?(0,s.jsx)("li",{onClick:L,className:"select-item creatable",tabIndex:1,ref:z,children:`${e("create")} "${k}"`}):(0,s.jsx)("li",{className:"no-options",children:e("noOptions")})]})]})},g=({expanded:e})=>(0,s.jsx)("svg",{width:"24",height:"24",fill:"none",stroke:"currentColor",strokeWidth:"2",className:"dropdown-heading-dropdown-arrow gray",children:(0,s.jsx)("path",{d:e?"M18 15 12 9 6 15":"M6 9L12 15 18 9"})}),y=()=>{let{t:e,value:r,options:a,valueRenderer:t}=d(),l=0===r.length,n=r.length===a.length,o=t&&t(r,a);return l?(0,s.jsx)("span",{className:"gray",children:o||e("selectSomeItems")}):(0,s.jsx)("span",{children:o||(n?e("allItemsAreSelected"):r.map(e=>e.label).join(", "))})},w=({size:e=24})=>(0,s.jsx)("span",{style:{width:e,marginRight:"0.2rem"},children:(0,s.jsx)("svg",{width:e,height:e,className:"spinner",viewBox:"0 0 50 50",style:{display:"inline",verticalAlign:"middle"},children:(0,s.jsx)("circle",{cx:"25",cy:"25",r:"20",fill:"none",className:"path"})})}),N=()=>{let{t:e,onMenuToggle:r,ArrowRenderer:a,shouldToggleOnHover:l,isLoading:n,disabled:o,onChange:i,labelledBy:c,value:p,isOpen:f,defaultIsOpen:x,ClearSelectedIcon:v,closeOnChangedValue:N}=d();(0,t.useEffect)(()=>{N&&A(!1)},[p]);let[j,C]=(0,t.useState)(!0),[k,A]=(0,t.useState)(x),[R,S]=(0,t.useState)(!1),E=(0,t.useRef)();(function(e,r){let a=(0,t.useRef)(!1);(0,t.useEffect)(()=>{a.current?e():a.current=!0},r)})(()=>{r&&r(k)},[k]),(0,t.useEffect)(()=>{void 0===x&&"boolean"==typeof f&&(C(!1),A(f))},[f]),u([m.ENTER,m.ARROW_DOWN,m.SPACE,m.ESCAPE],e=>{var r;["text","button"].includes(e.target.type)&&[m.SPACE,m.ENTER].includes(e.code)||(j&&(e.code===m.ESCAPE?(A(!1),null==(r=null==E?void 0:E.current)||r.focus()):A(!0)),e.preventDefault())},{target:E});let O=e=>{j&&l&&A(e)};return(0,s.jsxs)("div",{tabIndex:0,className:"dropdown-container","aria-labelledby":c,"aria-expanded":k,"aria-readonly":!0,"aria-disabled":o,ref:E,onFocus:()=>!R&&S(!0),onBlur:e=>{!e.currentTarget.contains(e.relatedTarget)&&j&&(S(!1),A(!1))},onMouseEnter:()=>O(!0),onMouseLeave:()=>O(!1),children:[(0,s.jsxs)("div",{className:"dropdown-heading",onClick:()=>{j&&A(!n&&!o&&!k)},children:[(0,s.jsx)("div",{className:"dropdown-heading-value",children:(0,s.jsx)(y,{})}),n&&(0,s.jsx)(w,{}),p.length>0&&null!==v&&(0,s.jsx)("button",{type:"button",className:"clear-selected-button",onClick:e=>{e.stopPropagation(),i([]),j&&A(!1)},disabled:o,"aria-label":e("clearSelected"),children:v||(0,s.jsx)(h,{})}),(0,s.jsx)(a||g,{expanded:k})]}),k&&(0,s.jsx)("div",{className:"dropdown-content",children:(0,s.jsx)("div",{className:"panel-content",children:(0,s.jsx)(b,{})})})]})},j=e=>(0,s.jsx)(i,{props:e,children:(0,s.jsx)("div",{className:`rmsc ${e.className||"multi-select"}`,children:(0,s.jsx)(N,{})})})},81764:(e,r,a)=>{a.d(r,{A:()=>s});let t=a(14232).createContext(null);t.displayName="CardHeaderContext";let s=t}}]);
//# sourceMappingURL=9875-74bd9a086f77f518.js.map