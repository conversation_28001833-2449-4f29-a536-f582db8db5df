{"version": 3, "file": "static/chunks/8076-b98d05f535a49bc7.js", "mappings": "wRA8PA,MAAeA,CAAAA,EAAAA,EAAAA,EAAAA,CAAOA,GAhPDC,IACnB,GAAM,GAAEC,CAAC,CAAE,CAAGC,CAAAA,EAAAA,EAAAA,EAAAA,CAAcA,CAAC,UACrB,QAAEC,CAAM,aAAEC,CAAW,MAAEC,CAAI,CAAE,CAAGL,EAElCM,EAAc,KAClBF,GAAY,EACd,EAqBM,CAACG,EAASC,EAAW,CAAGC,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAMC,CAlB1CC,IAAK,GACLC,SAAU,GACVC,UAAW,GACXC,SAAU,GACVC,SAAU,GACVC,YAAa,GACbC,KAAM,GACNC,MAAO,KACPC,MAAO,GACPC,SAAU,GACVC,kBAAmB,GACnBC,oBAAqB,GACrBC,oBAAqB,GACrBC,sBAAuB,GACvBC,qBAAsB,GACtBC,gCAAiC,EACnC,GAGM,CAACC,EAAcC,EAAgB,CAAGnB,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAQ,EAAE,EAE1DoB,CAAAA,EAAAA,EAAAA,SAAAA,CAASA,CAAC,KAaRC,CAZsB,UACpB,IAAMC,EACJ1B,GAAQA,EAAKW,WAAW,EAAIX,EAAKW,WAAW,CAACL,GAAG,CAC5CN,EAAKW,WAAW,CAACL,GAAG,CACpB,GACNH,EAAW,GAAqB,EAC9B,GAAGwB,CAAS,CACZ,EAF8B,CAE3B3B,CAAI,CACPW,YAAae,EACbb,MAAO,KACT,GACF,GAEF,EAAG,CAACb,EAAK,EAETwB,CAAAA,EAAAA,EAAAA,SAAAA,CAASA,CAAC,KACR,IAAMI,EAAqB,CACzBC,MAAO,CAAC,EACRC,KAAM,CAAEC,MAAO,KAAM,EACrBC,MAAO,IACPC,OACE,wOACJ,EAUAC,CAT0B,UACxB,IAAMC,EAAgB,MAAMC,EAAAA,CAAUA,CAACC,GAAG,CACxC,eACAT,EAEEO,IAAiBG,MAAMC,OAAO,CAACJ,EAAcnC,IAAI,GACnDuB,EAAgBY,EAAcnC,IAAI,EAEtC,GAEF,EAAG,EAAE,EAEL,IAAMwC,EAAkBC,IACtB,GAAIA,EAAEC,MAAM,CAAE,CACZ,GAAM,MAAEC,CAAI,OAAEC,CAAK,CAAE,CAAGH,EAAEC,MAAM,CAChCvC,EAAW,GAAqB,EAC9B,GAAGwB,CAAS,CACZ,CAACgB,CAF6B,CAExB,CAAEC,EACV,EACF,CACF,EAEMC,EAAgB,MAAOJ,IAC3BA,EAAEK,cAAc,GACC,MAAMV,EAAAA,CAAUA,CAACW,IAAI,CAAC,uBAAwB7C,KAE7DP,EAAMqD,QAAQ,CAACC,CAAAA,EAAAA,EAAAA,EAAAA,CAAoBA,IACnCC,EAAAA,EAAKA,CAACC,OAAO,CAACvD,EAAE,qCAChBG,GAAY,GAEhB,EAOA,MACE,UAACqD,MAAAA,UACC,UAACC,EAAAA,CAAKA,CAAAA,CACJC,KAAMxD,EACNyD,OAAQtD,EACRuD,KAAK,KACLC,UAAU,QACVC,QAAQ,aAER,WAACC,EAAAA,CAAqBA,CAAAA,CAACF,UAAU,MAAMG,SAAUf,EAAegB,cAAe3D,EAAS4D,oBAAoB,YAC1G,UAACT,EAAAA,CAAKA,CAACU,MAAM,EAACC,WAAW,aACvB,UAACX,EAAAA,CAAKA,CAACY,KAAK,WAAErE,EAAE,2BAElB,WAACyD,EAAAA,CAAKA,CAACa,IAAI,YACT,UAACC,KAAAA,UAAIvE,EAAE,2BACP,WAACwD,MAAAA,CAAIK,UAAU,sBACb,WAACW,EAAAA,CAAIA,CAACC,KAAK,EAACC,GAAIC,EAAAA,CAAGA,CAAEC,UAAU,WAAWf,UAAU,iBAClD,UAACW,EAAAA,CAAIA,CAACK,KAAK,EAACC,MAAM,IAACC,GAAG,IAAIC,GAAG,IAAIC,GAAG,IAAIpB,UAAU,0BAC/C7D,EAAE,sBAEL,UAACkF,EAAAA,CAAGA,CAAAA,CAACH,GAAG,IAAIC,GAAG,IAAIC,GAAG,cACpB,UAACE,EAAAA,EAASA,CAAAA,CACRpC,KAAK,WACLqC,QAAQ,IACRC,KAAK,OACLrC,MAAO1C,GAAWA,EAAQK,QAAQ,CAClC2E,YAAatF,EAAE,yBACfuF,SAAU3C,SAIhB,WAAC4B,EAAAA,CAAIA,CAACC,KAAK,EAACC,GAAIC,EAAAA,CAAGA,CAAEC,UAAU,WAAWf,UAAU,iBAClD,UAACW,EAAAA,CAAIA,CAACK,KAAK,EAACC,MAAM,IAACC,GAAG,IAAIC,GAAG,IAAIC,GAAG,IAAIpB,UAAU,0BAC/C7D,EAAE,kBAEL,UAACkF,EAAAA,CAAGA,CAAAA,CAACH,GAAG,IAAIC,GAAG,IAAIC,GAAG,cACpB,WAACN,EAAAA,CAAGA,CAAAA,WACF,UAACO,EAAAA,CAAGA,CAAAA,CAACF,GAAG,KAAKQ,GAAG,aACd,UAACL,EAAAA,EAASA,CAAAA,CACRpC,KAAK,YACLqC,QAAQ,IACRC,KAAK,OACLrC,MAAO1C,GAAWA,EAAQM,SAAS,CACnC0E,YAAatF,EAAE,8BACfyF,aAAczF,EAAE,oCAChBuF,SAAU3C,MAGd,UAACsC,EAAAA,CAAGA,CAAAA,CAACF,GAAG,KAAKQ,GAAG,IAAI3B,UAAU,wBAC5B,UAACsB,EAAAA,EAASA,CAAAA,CACRpC,KAAK,WACLsC,KAAK,OACLrC,MAAO1C,GAAWA,EAAQO,QAAQ,CAClCyE,YAAatF,EAAE,6BACfuF,SAAU3C,cAMpB,WAAC4B,EAAAA,CAAIA,CAACC,KAAK,EAACC,GAAIC,EAAAA,CAAGA,CAAEC,UAAU,WAAWf,UAAU,iBAClD,UAACW,EAAAA,CAAIA,CAACK,KAAK,EAACC,MAAM,IAACC,GAAG,IAAIC,GAAG,IAAIC,GAAG,aACjCjF,EAAE,sBAEL,UAACkF,EAAAA,CAAGA,CAAAA,CAACH,GAAG,IAAIC,GAAG,IAAIC,GAAG,cACpB,UAACE,EAAAA,EAASA,CAAAA,CACRpC,KAAK,WACLsC,KAAK,OACLrC,MAAO1C,GAAWA,EAAQQ,QAAQ,CAClCwE,YAAatF,EAAE,6BACfuF,SAAU3C,SAKhB,WAAC4B,EAAAA,CAAIA,CAACC,KAAK,EAACC,GAAIC,EAAAA,CAAGA,CAAEC,UAAU,cAAcf,UAAU,iBACrD,UAACW,EAAAA,CAAIA,CAACK,KAAK,EAACC,MAAM,IAACC,GAAG,IAAIC,GAAG,IAAIC,GAAG,aACjCjF,EAAE,0BAEL,UAACkF,EAAAA,CAAGA,CAAAA,CAACH,GAAG,IAAIC,GAAG,IAAIC,GAAG,cACpB,WAACS,EAAAA,EAAWA,CAAAA,CACV3C,KAAK,sBACLjB,GAAG,sBACHkB,MAAO1C,EAAQS,WAAW,CAC1BwE,SAzFU,CAyFAI,GAxF1B,GAAM,OAAE3C,CAAK,CAAE,CAAGH,EAAEC,MAAM,CAC1BvC,EAAW,GAAqB,EAAE,GAAGwB,CAAS,CAAEhB,EAAhB,UAA6BiC,EAAM,EACrE,EAuFkB4C,MAAO,CACLC,gBAAiB,UACjBC,aAAc,MACdC,MAAO,SACT,YAEA,UAACC,SAAAA,CAAOhD,MAAM,YAAIhD,EAAE,gCACnB0B,EAAauE,GAAG,CAAC,CAACC,EAAMC,IAChB,UAACH,SAAAA,CAAOhD,MAAOkD,EAAKxF,GAAG,UAAGwF,EAAK/D,KAAK,YAKnD,WAACqC,EAAAA,CAAIA,CAACC,KAAK,EAACC,GAAIC,EAAAA,CAAGA,CAAEC,UAAU,QAAQf,UAAU,iBAC/C,UAACW,EAAAA,CAAIA,CAACK,KAAK,EAACC,MAAM,IAACC,GAAG,IAAIC,GAAG,IAAIC,GAAG,IAAIpB,UAAU,0BAC/C7D,EAAE,mBAEL,UAACkF,EAAAA,CAAGA,CAAAA,CAACH,GAAG,IAAIC,GAAG,IAAIC,GAAG,cACpB,UAACE,EAAAA,EAASA,CAAAA,CACRC,QAAQ,IACRrC,KAAK,QACLsC,KAAK,OACLrC,MAAO1C,GAAWA,EAAQY,KAAK,CAC/BoE,YAAatF,EAAE,0BACfuF,SAAU3C,SAKhB,WAAC4B,EAAAA,CAAIA,CAACC,KAAK,EAACC,GAAIC,EAAAA,CAAGA,CAAEC,UAAU,WAAWf,UAAU,iBAClD,UAACW,EAAAA,CAAIA,CAACK,KAAK,EAACC,MAAM,IAACC,GAAG,IAAIC,GAAG,IAAIC,GAAG,aACjCjF,EAAE,sBAEL,UAACkF,EAAAA,CAAGA,CAAAA,CAACH,GAAG,IAAIC,GAAG,IAAIC,GAAG,cACpB,UAACE,EAAAA,EAASA,CAAAA,CACRpC,KAAK,WACLsC,KAAK,WACLrC,MAAO1C,EAAQa,QAAQ,CACvBmE,YAAatF,EAAE,gCACfuF,SAAU3C,EACVwD,QAAQ,iEACRX,aAAc,CACZW,QACEpG,EAAE,gDACN,eAMV,WAACyD,EAAAA,CAAKA,CAAC4C,MAAM,EAACxC,UAAU,iBACxB,UAACyC,EAAAA,CAAMA,CAAAA,CAACC,QAAQ,UAAUlB,KAAK,kBAC1BrF,EAAE,yBAEL,UAACsG,EAAAA,CAAMA,CAAAA,CAACC,QAAQ,SAAUC,QAASnG,WAChCL,EAAE,6BAOjB,+ICrKO,IAAMyG,EAAQ,CACnBC,WA1C4C,OAAC,MAC7C3D,CAAI,CACJ4D,eAAa,CACbpB,UAAQ,cACRE,CAAY,UACZmB,CAAQ,CACT,GACO,QAAEC,CAAM,SAAEC,CAAO,CAAE,CAAGC,CAAAA,EAAAA,EAAAA,EAAAA,CAAgBA,GACtCC,EAAWF,CAAO,CAAC/D,EAAK,EAAI8D,CAAM,CAAC9D,EAAK,CAGzBkE,EAAAA,OAAa,CAAC,IAAO,OAAElE,EAAK,EAAI,CAACA,EAAK,EAG3D,IAAMmE,EAAoBD,EAAAA,QAAc,CAAChB,GAAG,CAACW,EAAU,GACrD,EAAIK,cAAoB,CAACE,IAEnBC,IAF2B,KAxCnBrH,CAAU,EAC1B,MAAO,iBAAOA,GAAgC,OAAVA,CACtC,EAwCmBoH,EAAMpH,KAAK,EACfkH,CADkB,CAClBA,YAAkB,CAACE,EAA6C,MACrEpE,EACA,GAAGoE,EAAMpH,KAAK,GAIboH,GAGT,MACE,WAAC3D,MAAAA,WACC,UAACA,MAAAA,CAAIK,UAAU,uBACZqD,IAEFF,GACC,UAACxD,MAAAA,CAAIK,UAAU,oCACZ4B,GAAiB,kBAAOoB,CAAM,CAAC9D,EAAK,CAAgB8D,CAAM,CAAC9D,EAAK,CAAGsE,OAAOR,CAAM,CAAC9D,GAAK,MAKjG,EAIEuE,UAhE0C,OAAC,IAAExF,CAAE,OAAEyF,CAAK,OAAEvE,CAAK,MAAED,CAAI,CAAEyE,UAAQ,CAAE,GACzE,QAAEC,CAAM,eAAEC,CAAa,CAAE,CAAGX,CAAAA,EAAAA,EAAAA,EAAAA,CAAgBA,GAC5CY,EAAY5E,GAAQjB,EAE1B,MACE,UAAC0C,EAAAA,CAAIA,CAACoD,KAAK,EACTvC,KAAK,QACLvD,GAAIA,EACJyF,MAAOA,EACPvE,MAAOA,EACPD,KAAM4E,EACNE,QAASJ,CAAM,CAACE,EAAU,GAAK3E,EAC/BuC,SAAU,IACRmC,EAAcC,EAAW9E,EAAEC,MAAM,CAACE,KAAK,CACzC,EACAwE,SAAUA,EACVM,MAAM,KAGZ,CA8CA,EAAE,ECzEcC,CAAAA,CACL5C,EAAAA,EAAAA,CACEO,EAAAA,EAAAA,mFCeb,IAAM3B,EAAwBiE,CAAAA,EAAAA,EAAAA,UAAAA,CAAUA,CAA8C,CAACjI,EAAOkI,KAC5F,GAAM,UAAErB,CAAQ,UAAE5C,CAAQ,cAAEkE,CAAY,CAAErE,WAAS,CAAEsE,YAAU,eAAElE,CAAa,CAAE,GAAGmE,EAAM,CAAGrI,EAGtFsI,EAAmBC,EAAAA,EAAU,GAAGC,KAAK,CAAC,CAAC,GAE7C,MACE,UAACC,EAAAA,EAAMA,CAAAA,CACLvE,cAAeA,GAAiB,CAAC,EACjCoE,iBAAkBA,EAClBrE,SAAU,CAACyD,EAA6BgB,KAEtC,IAAMC,EAAuB,CAC3BxF,eAAgB,KAAO,EACvByF,gBAAiB,KAAO,EACxBC,cAAe,KACf9F,OAAQ,KACR+F,YAAa,IAAIC,MAAM,UACvBC,SAAS,EACTC,YAAY,EACZC,kBAAkB,EAClBC,WAAY,EACZC,WAAW,EACXC,UAAWC,KAAKC,GAAG,GACnBjE,KAAM,SACNkE,mBAAoB,KAAM,EAC1BC,qBAAsB,KAAM,EAC5BC,QAAS,KAAO,CAClB,EAEIzF,GAEFA,EAAS0E,EAAWjB,EAAQgB,CAFhB,CAIhB,EACC,GAAGL,CAAI,UAEP,GACC,UAAC5D,EAAAA,EAAIA,CAAAA,CACHyD,IAAKA,EACLjE,SAAU0F,EAAYC,YAAY,CAClCzB,aAAcA,EACdrE,UAAWA,EACXsE,WAAYA,WAES,YAApB,OAAOvB,EAA0BA,EAAS8C,GAAe9C,KAKpE,GAEA7C,EAAsB6F,WAAW,CAAG,wBAEpC,MAAe7F,qBAAqBA,EAAC,yEClF9B,IAAMoB,EAAY,OAAC,MACxBpC,CAAI,IACJjB,CAAE,UACFsD,CAAQ,WACRyE,CAAS,cACTpE,CAAY,UACZF,CAAQ,OACRvC,CAAK,IACL0B,CAAE,CACFoF,WAAS,CACTC,MAAI,SACJ3D,CAAO,CACP,GAAGrG,EACC,GAuBJ,MACE,UAACiK,EAAAA,EAAKA,CAAAA,CAACjH,KAAMA,EAAMkH,SAtBJ,CAsBcA,GApB7B,IAAMC,EAA2B,UAAf,OAAOC,EAAmBA,EAAM9C,OAAO8C,GAAO,WAChE,GAAiB,EAACA,GAA4B,IAA7B,GAAkBC,IAAI,EAAO,CAAC,CACtC3E,EAD0C,KAC1CA,EAAAA,KAAAA,EAAAA,EAAcoE,SAAAA,GAAa,EAA3BpE,uBAGLoE,GAAa,CAACA,EAAUM,GACnB1E,GADyB,IACzBA,EAAAA,KAAAA,EAAAA,EAAcoE,SAAAA,GAAa,EAA3BpE,cAGLW,GAAW+D,GAET,CADU,CADI,GACAE,OAAOjE,GACdkE,IAAI,CAACH,GACP1E,GADa,IACbA,EAAAA,KAAAA,EAAAA,EAAcW,OAAAA,GAAW,IAAzBX,mBAKb,WAIK,OAAC,CAAE8E,OAAK,MAAEC,CAAI,CAAO,SACpB,iCACE,UAAChG,EAAAA,CAAIA,CAACiG,OAAO,EACV,GAAGF,CAAK,CACR,GAAGxK,CAAK,CACT+B,GAAIA,EACJ4C,GAAIA,GAAM,QACVqF,KAAMA,EACNW,UAAWF,EAAK1D,OAAO,EAAI,CAAC,CAAC0D,EAAKG,KAAK,CACvCpF,SAAU,IACRgF,EAAMhF,QAAQ,CAAC1C,GACX0C,GAAUA,EAAS1C,EACzB,EACAG,WAAiB4H,IAAV5H,EAAsBA,EAAQuH,EAAMvH,KAAK,GAEjDwH,EAAK1D,OAAO,EAAI0D,EAAKG,KAAK,CACzB,UAACnG,EAAAA,CAAIA,CAACiG,OAAO,CAACI,QAAQ,EAACxF,KAAK,mBACzBmF,EAAKG,KAAK,GAEX,UAKd,EAAE,EAIyB,OAAC,MAC1B5H,CAAI,IACJjB,CAAE,UACFsD,CAAQ,cACRK,CAAY,UACZF,CAAQ,OACRvC,CAAK,UACL4D,CAAQ,CACR,GAAG7G,EACC,GAUJ,MACE,UAACiK,EAAAA,EAAKA,CAAAA,CAACjH,KAAMA,EAAMkH,SATJ,CAScA,GAR7B,GAAI7E,GAAa,EAAC+E,GAAe,IAAhB,CAAQA,CAAQ,CAAC,CAChC,EADoC,IAC7B1E,OAAAA,EAAAA,KAAAA,EAAAA,EAAcoE,SAAAA,GAAa,EAA3BpE,sBAIX,WAIK,OAAC,CAAE8E,OAAK,MAAEC,CAAI,CAAO,SACpB,iCACE,UAAChG,EAAAA,CAAIA,CAACiG,OAAO,EACX/F,GAAG,SACF,GAAG6F,CAAK,CACR,GAAGxK,CAAK,CACT+B,GAAIA,EACJ4I,UAAWF,EAAK1D,OAAO,EAAI,CAAC,CAAC0D,EAAKG,KAAK,CACvCpF,SAAU,IACRgF,EAAMhF,QAAQ,CAAC1C,GACX0C,GAAUA,EAAS1C,EACzB,EACAG,WAAiB4H,IAAV5H,EAAsBA,EAAQuH,EAAMvH,KAAK,UAE/C4D,IAEF4D,EAAK1D,OAAO,EAAI0D,EAAKG,KAAK,CACzB,UAACnG,EAAAA,CAAIA,CAACiG,OAAO,CAACI,QAAQ,EAACxF,KAAK,mBACzBmF,EAAKG,KAAK,GAEX,UAKd,EAAE", "sources": ["webpack://_N_E/./pages/profile/profileEdit.tsx", "webpack://_N_E/./components/common/FormikRadio.tsx", "webpack://_N_E/./components/common/FormValidation.tsx", "webpack://_N_E/./components/common/ValidationFormWrapper.tsx", "webpack://_N_E/./components/common/FormikTextInput.tsx"], "sourcesContent": ["//Import Library\r\nimport React, { useState, useEffect } from \"react\";\r\nimport { connect } from \"react-redux\";\r\nimport { Form, Modal, Row, Col, Button } from \"react-bootstrap\";\r\n\r\nimport toast from 'react-hot-toast';\r\nimport { TextInput, SelectGroup } from \"../../components/common/FormValidation\";\r\nimport ValidationFormWrapper from \"../../components/common/ValidationFormWrapper\";\r\n\r\n//Import services/components\r\nimport { loadLoggedinUserData } from \"../../stores/userActions\";\r\nimport apiService from \"../../services/apiService\";\r\nimport { useTranslation } from 'next-i18next';\r\n\r\nconst ProfileEdit = (props: any) => {\r\n  const { t } = useTranslation('common');\r\n    const { isOpen, manageClose, data } = props;\r\n\r\n  const handleClose = () => {\r\n    manageClose(false);\r\n  };\r\n\r\n  const intialState = {\r\n    _id: \"\",\r\n    username: \"\",\r\n    firstname: \"\",\r\n    lastname: \"\",\r\n    position: \"\",\r\n    institution: \"\",\r\n    role: \"\",\r\n    image: null,\r\n    email: \"\",\r\n    password: \"\",\r\n    dataConsentPolicy: \"\",\r\n    restrictedUsePolicy: \"\",\r\n    acceptCookiesPolicy: \"\",\r\n    withdrawConsentPolicy: \"\",\r\n    medicalConsentPolicy: \"\",\r\n    fullDataProtectionConsentPolicy: \"\"\r\n  };\r\n\r\n  const [profile, setProfile] = useState<any>(intialState);\r\n  const [organisation, setOrganisation] = useState<any[]>([]);\r\n\r\n  useEffect(() => {\r\n    const updateProfile = async () => {\r\n      const id =\r\n        data && data.institution && data.institution._id\r\n          ? data.institution._id\r\n          : \"\";\r\n      setProfile((prevState: any) => ({\r\n        ...prevState,\r\n        ...data,\r\n        institution: id,\r\n        image: null,\r\n      }));\r\n    };\r\n    updateProfile();\r\n  }, [data]);\r\n\r\n  useEffect(() => {\r\n    const organisationParams = {\r\n      query: {},\r\n      sort: { title: \"asc\" },\r\n      limit: \"~\",\r\n      select:\r\n        \"-contact_name -description -type -networks -expertise -hazard_types -hazards -address -focal_points -website -telephone -twitter -header -use_default_header -images -status -email -user -created_at -updated_at -primary_focal_point\",\r\n    };\r\n    const fetchOrganisation = async () => {\r\n      const organisations = await apiService.get(\r\n        \"/institution\",\r\n        organisationParams\r\n      );\r\n      if (organisations && Array.isArray(organisations.data)){\r\n        setOrganisation(organisations.data);\r\n      }\r\n    };\r\n    fetchOrganisation();\r\n  }, []);\r\n\r\n  const profileHandler = (e: React.ChangeEvent<HTMLInputElement>) => {\r\n    if (e.target) {\r\n      const { name, value } = e.target;\r\n      setProfile((prevState: any) => ({\r\n        ...prevState,\r\n        [name]: value,\r\n      }));\r\n    }\r\n  };\r\n\r\n  const submitHandler = async (e: any) => {\r\n    e.preventDefault();\r\n    const response = await apiService.post(\"/users/updateProfile\", profile);\r\n    if (response) {\r\n      props.dispatch(loadLoggedinUserData());\r\n      toast.success(t(\"toast.ProfileUpdatedSuccessfully\"));\r\n      manageClose(false);\r\n    }\r\n  };\r\n\r\n  const organisationHandler = (e: any) => {\r\n    const { value } = e.target;\r\n    setProfile((prevState: any) => ({ ...prevState, institution: value }));\r\n  };\r\n\r\n  return (\r\n    <div>\r\n      <Modal\r\n        show={isOpen}\r\n        onHide={handleClose}\r\n        size=\"xl\"\r\n        className=\"w-100\"\r\n        centered\r\n      >\r\n        <ValidationFormWrapper className=\"m-4\" onSubmit={submitHandler} initialValues={profile} enableReinitialize={true}>\r\n          <Modal.Header closeButton>\r\n            <Modal.Title>{t(\"setInfo.editprofile\")}</Modal.Title>\r\n          </Modal.Header>\r\n          <Modal.Body>\r\n            <h5>{t(\"setInfo.MyInformation\")}</h5>\r\n            <div className=\"p-2 w-100\">\r\n              <Form.Group as={Row} controlId=\"username\" className=\"mb-3\">\r\n                <Form.Label column md=\"4\" xs=\"5\" lg=\"2\" className=\"required-field\">\r\n                  {t(\"setInfo.username\")}\r\n                </Form.Label>\r\n                <Col md=\"8\" xs=\"7\" lg=\"10\">\r\n                  <TextInput\r\n                    name=\"username\"\r\n                    required\r\n                    type=\"text\"\r\n                    value={profile && profile.username}\r\n                    placeholder={t(\"setInfo.EnterYourName\")}\r\n                    onChange={profileHandler}\r\n                  />\r\n                </Col>\r\n              </Form.Group>\r\n              <Form.Group as={Row} controlId=\"username\" className=\"mb-3\">\r\n                <Form.Label column md=\"4\" xs=\"5\" lg=\"2\" className=\"required-field\">\r\n                  {t(\"setInfo.name\")}\r\n                </Form.Label>\r\n                <Col md=\"8\" xs=\"7\" lg=\"10\">\r\n                  <Row>\r\n                    <Col xs=\"12\" sm=\"6\">\r\n                      <TextInput\r\n                        name=\"firstname\"\r\n                        required\r\n                        type=\"text\"\r\n                        value={profile && profile.firstname}\r\n                        placeholder={t(\"setInfo.Enteryourfirstname\")}\r\n                        errorMessage={t(\"setInfo.Pleaseenteryourfirstname\")}\r\n                        onChange={profileHandler}\r\n                      />\r\n                    </Col>\r\n                    <Col xs=\"12\" sm=\"6\" className=\"pt-2 pt-sm-0\">\r\n                      <TextInput\r\n                        name=\"lastname\"\r\n                        type=\"text\"\r\n                        value={profile && profile.lastname}\r\n                        placeholder={t(\"setInfo.EnterYourlastname\")}\r\n                        onChange={profileHandler}\r\n                      />\r\n                    </Col>\r\n                  </Row>\r\n                </Col>\r\n              </Form.Group>\r\n              <Form.Group as={Row} controlId=\"position\" className=\"mb-3\">\r\n                <Form.Label column md=\"4\" xs=\"5\" lg=\"2\" >\r\n                  {t(\"setInfo.position\")}\r\n                </Form.Label>\r\n                <Col md=\"8\" xs=\"7\" lg=\"10\">\r\n                  <TextInput\r\n                    name=\"position\"\r\n                    type=\"text\"\r\n                    value={profile && profile.position}\r\n                    placeholder={t(\"setInfo.EnterYourposition\")}\r\n                    onChange={profileHandler}\r\n                  />\r\n                </Col>\r\n              </Form.Group>\r\n\r\n              <Form.Group as={Row} controlId=\"institution\" className=\"mb-3\">\r\n                <Form.Label column md=\"4\" xs=\"5\" lg=\"2\">\r\n                  {t(\"setInfo.organisation\")}\r\n                </Form.Label>\r\n                <Col md=\"8\" xs=\"7\" lg=\"10\">\r\n                  <SelectGroup\r\n                    name=\"partner_institution\"\r\n                    id=\"partner_institution\"\r\n                    value={profile.institution}\r\n                    onChange={organisationHandler}\r\n                    style={{\r\n                      backgroundColor: \"inherit\",\r\n                      borderRadius: \"5px\",\r\n                      color: \"#495057\",\r\n                    }}\r\n                  >\r\n                    <option value=\"\">{t(\"setInfo.SelectOrganisation\")}</option>\r\n                    {organisation.map((item, _i) => {\r\n                      return <option value={item._id}>{item.title}</option>;\r\n                    })}\r\n                  </SelectGroup>\r\n                </Col>\r\n              </Form.Group>\r\n              <Form.Group as={Row} controlId=\"Email\" className=\"mb-3\">\r\n                <Form.Label column md=\"4\" xs=\"5\" lg=\"2\" className=\"required-field\">\r\n                  {t(\"setInfo.email\")}\r\n                </Form.Label>\r\n                <Col md=\"8\" xs=\"7\" lg=\"10\">\r\n                  <TextInput\r\n                    required\r\n                    name=\"email\"\r\n                    type=\"text\"\r\n                    value={profile && profile.email}\r\n                    placeholder={t(\"setInfo.EnterYourEmail\")}\r\n                    onChange={profileHandler}\r\n                  />\r\n                </Col>\r\n              </Form.Group>\r\n\r\n              <Form.Group as={Row} controlId=\"password\" className=\"mb-3\">\r\n                <Form.Label column md=\"4\" xs=\"5\" lg=\"2\">\r\n                  {t(\"setInfo.password\")}\r\n                </Form.Label>\r\n                <Col md=\"8\" xs=\"7\" lg=\"10\">\r\n                  <TextInput\r\n                    name=\"password\"\r\n                    type=\"password\"\r\n                    value={profile.password}\r\n                    placeholder={t(\"setInfo.EnterYourNewPassword\")}\r\n                    onChange={profileHandler}\r\n                    pattern=\"^(?=.*?[A-Z])(?=.*?[a-z])(?=.*?[0-9])(?=.*?[#?!@$%^&*-]).{8,}$\"\r\n                    errorMessage={{\r\n                      pattern:\r\n                        t(\"setInfo.Passwordshouldcontainatleastcharacter\"),\r\n                    }}\r\n                  />\r\n                </Col>\r\n              </Form.Group>\r\n            </div>\r\n          </Modal.Body>\r\n          <Modal.Footer className=\"pb-0\">\r\n          <Button variant=\"primary\" type=\"submit\">\r\n              {t(\"setInfo.savechanges\")}\r\n            </Button>\r\n            <Button variant=\"danger\"  onClick={handleClose}>\r\n              {t(\"setInfo.Cancel\")}\r\n            </Button>\r\n          </Modal.Footer>\r\n        </ValidationFormWrapper>\r\n      </Modal>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default connect()(ProfileEdit);\r\n", "import React from 'react';\r\nimport { useFormikContext, Field } from 'formik';\r\nimport { Form } from 'react-bootstrap';\r\n\r\ninterface RadioItemProps {\r\n  id: string;\r\n  label: string;\r\n  value: string;\r\n  name?: string;\r\n  disabled?: boolean;\r\n}\r\n\r\ninterface RadioGroupProps {\r\n  name: string;\r\n  valueSelected: string;\r\n  onChange: (e: React.ChangeEvent<HTMLInputElement>) => void;\r\n  errorMessage?: string;\r\n  children: React.ReactNode;\r\n}\r\n\r\n// Type Guard to ensure child.props is an object\r\nfunction isObject(props: any): props is { [key: string]: any } {\r\n  return typeof props === 'object' && props !== null;\r\n}\r\n\r\nconst RadioItem: React.FC<RadioItemProps> = ({ id, label, value, name, disabled }) => {\r\n  const { values, setFieldValue } = useFormikContext<any>();\r\n  const fieldName = name || id;\r\n\r\n  return (\r\n    <Form.Check\r\n      type=\"radio\"\r\n      id={id}\r\n      label={label}\r\n      value={value}\r\n      name={fieldName}\r\n      checked={values[fieldName] === value}\r\n      onChange={(e) => {\r\n        setFieldValue(fieldName, e.target.value);\r\n      }}\r\n      disabled={disabled}\r\n      inline\r\n    />\r\n  );\r\n};\r\n\r\nconst RadioGroup: React.FC<RadioGroupProps> = ({\r\n  name,\r\n  valueSelected,\r\n  onChange,\r\n  errorMessage,\r\n  children,\r\n}) => {\r\n  const { errors, touched } = useFormikContext<any>();\r\n  const hasError = touched[name] && errors[name];\r\n\r\n  // Create a context to pass the name to RadioItems\r\n  const radioContext = React.useMemo(() => ({ name }), [name]);\r\n\r\n  // Clone children to pass the name, ensuring props are spread safely\r\n  const childrenWithProps = React.Children.map(children, (child) => {\r\n    if (React.isValidElement(child)) {\r\n      // Ensure child.props is an object before spreading\r\n      if (isObject(child.props)) {\r\n        return React.cloneElement(child as React.ReactElement<RadioItemProps>, {\r\n          name,\r\n          ...child.props, // Safely spread child.props\r\n        });\r\n      }\r\n    }\r\n    return child;\r\n  });\r\n\r\n  return (\r\n    <div>\r\n      <div className=\"radio-group\">\r\n        {childrenWithProps}\r\n      </div>\r\n      {hasError && (\r\n        <div className=\"invalid-feedback d-block\">\r\n          {errorMessage || (typeof errors[name] === 'string' ? errors[name] : String(errors[name]))}\r\n        </div>\r\n      )}\r\n    </div>\r\n  );\r\n};\r\n\r\nexport const Radio = {\r\n  RadioGroup,\r\n  RadioItem,\r\n};\r\n\r\nexport default Radio;\r\n", "// This file exports all the components needed to replace react-bootstrap4-form-validation\r\n// It serves as a drop-in replacement for the original library\r\n\r\nimport ValidationForm from './ValidationFormWrapper';\r\nimport { TextInput, SelectGroup } from './FormikTextInput';\r\nimport { Radio } from './FormikRadio';\r\n\r\n// Export all components\r\nexport {\r\n  ValidationForm,\r\n  TextInput,\r\n  SelectGroup,\r\n  Radio\r\n};\r\n\r\n// Also export as default for convenience\r\nexport default {\r\n  ValidationForm,\r\n  TextInput,\r\n  SelectGroup,\r\n  Radio\r\n};\r\n", "import React, { forwardRef } from 'react';\r\nimport { Formik, Form, FormikProps, FormikHelpers } from 'formik';\r\nimport * as Yup from 'yup';\r\n\r\n// This is a wrapper component that replaces react-bootstrap4-form-validation with Formik\r\ninterface MockEvent {\r\n  preventDefault: () => void;\r\n  stopPropagation: () => void;\r\n  currentTarget: (EventTarget & Element) | null;\r\n  target: EventTarget | null;\r\n  nativeEvent: Event;\r\n  bubbles: boolean;\r\n  cancelable: boolean;\r\n  defaultPrevented: boolean;\r\n  eventPhase: number;\r\n  isTrusted: boolean;\r\n  timeStamp: number;\r\n  type: string;\r\n  isDefaultPrevented: () => boolean;\r\n  isPropagationStopped: () => boolean;\r\n  persist: () => void;\r\n}\r\n\r\ninterface ValidationFormWrapperProps {\r\n  children: React.ReactNode | ((formikProps: FormikProps<any>) => React.ReactNode);\r\n  onSubmit: (event: React.FormEvent | MockEvent, values?: Record<string, any>, actions?: FormikHelpers<Record<string, any>>) => void;\r\n  onErrorSubmit?: (errors: any) => void;\r\n  initialValues?: Record<string, any>;\r\n  enableReinitialize?: boolean;\r\n  autoComplete?: string;\r\n  className?: string;\r\n  onKeyPress?: (e: any) => void;\r\n}\r\n\r\nconst ValidationFormWrapper = forwardRef<HTMLFormElement, ValidationFormWrapperProps>((props, ref) => {\r\n  const { children, onSubmit, autoComplete, className, onKeyPress, initialValues, ...rest } = props;\r\n\r\n  // Create an empty validation schema by default\r\n  const validationSchema = Yup.object().shape({});\r\n\r\n  return (\r\n    <Formik\r\n      initialValues={initialValues || {}}\r\n      validationSchema={validationSchema}\r\n      onSubmit={(values: Record<string, any>, actions: FormikHelpers<Record<string, any>>) => {\r\n        // Create a mock event object with preventDefault method and currentTarget\r\n        const mockEvent: MockEvent = {\r\n          preventDefault: () => {},\r\n          stopPropagation: () => {},\r\n          currentTarget: null, // Set to null to avoid checkValidity errors\r\n          target: null,\r\n          nativeEvent: new Event('submit'),\r\n          bubbles: false,\r\n          cancelable: true,\r\n          defaultPrevented: false,\r\n          eventPhase: 0,\r\n          isTrusted: false,\r\n          timeStamp: Date.now(),\r\n          type: 'submit',\r\n          isDefaultPrevented: () => false,\r\n          isPropagationStopped: () => false,\r\n          persist: () => {}\r\n        };\r\n\r\n        if (onSubmit) {\r\n          // Pass the mock event object to maintain compatibility with the original code\r\n          onSubmit(mockEvent, values, actions);\r\n        }\r\n      }}\r\n      {...rest}\r\n    >\r\n      {(formikProps: FormikProps<any>) => (\r\n        <Form\r\n          ref={ref}\r\n          onSubmit={formikProps.handleSubmit}\r\n          autoComplete={autoComplete}\r\n          className={className}\r\n          onKeyPress={onKeyPress}\r\n        >\r\n          {typeof children === 'function' ? children(formikProps) : children}\r\n        </Form>\r\n      )}\r\n    </Formik>\r\n  );\r\n});\r\n\r\nValidationFormWrapper.displayName = 'ValidationFormWrapper';\r\n\r\nexport default ValidationFormWrapper;\r\n", "import React from 'react';\r\nimport { Form } from 'react-bootstrap';\r\nimport { Field, ErrorMessage, useField } from 'formik';\r\n\r\n// This component mimics the TextInput component from react-bootstrap4-form-validation\r\n// but uses Formik under the hood\r\nexport const TextInput = ({\r\n  name,\r\n  id,\r\n  required,\r\n  validator,\r\n  errorMessage,\r\n  onChange,\r\n  value,\r\n  as,\r\n  multiline,\r\n  rows,\r\n  pattern,\r\n  ...props\r\n}: any) => {\r\n  // Create a custom validation function that mimics the original validator\r\n  const validate = (val: any) => {\r\n    // Safely convert value to string and trim\r\n    const stringVal = typeof val === 'string' ? val : String(val || '');\r\n    if (required && (!val || stringVal.trim() === '')) {\r\n      return errorMessage?.validator || 'This field is required';\r\n    }\r\n\r\n    if (validator && !validator(val)) {\r\n      return errorMessage?.validator || 'Invalid value';\r\n    }\r\n\r\n    if (pattern && val) {\r\n      const regex = new RegExp(pattern);\r\n      if (!regex.test(val)) {\r\n        return errorMessage?.pattern || 'Invalid format';\r\n      }\r\n    }\r\n\r\n    return undefined;\r\n  };\r\n\r\n  return (\r\n    <Field name={name} validate={validate}>\r\n      {({ field, meta }: any) => (\r\n        <>\r\n          <Form.Control\r\n            {...field}\r\n            {...props}\r\n            id={id}\r\n            as={as || 'input'}\r\n            rows={rows}\r\n            isInvalid={meta.touched && !!meta.error}\r\n            onChange={(e: React.ChangeEvent<HTMLInputElement>) => {\r\n              field.onChange(e);\r\n              if (onChange) onChange(e);\r\n            }}\r\n            value={value !== undefined ? value : field.value}\r\n          />\r\n          {meta.touched && meta.error ? (\r\n            <Form.Control.Feedback type=\"invalid\">\r\n              {meta.error}\r\n            </Form.Control.Feedback>\r\n          ) : null}\r\n        </>\r\n      )}\r\n    </Field>\r\n  );\r\n};\r\n\r\n// This component mimics the SelectGroup component from react-bootstrap4-form-validation\r\n// but uses Formik under the hood\r\nexport const SelectGroup = ({\r\n  name,\r\n  id,\r\n  required,\r\n  errorMessage,\r\n  onChange,\r\n  value,\r\n  children,\r\n  ...props\r\n}: any) => {\r\n  // Create a custom validation function that mimics the original validator\r\n  const validate = (val: any) => {\r\n    if (required && (!val || val === '')) {\r\n      return errorMessage?.validator || 'This field is required';\r\n    }\r\n\r\n    return undefined;\r\n  };\r\n\r\n  return (\r\n    <Field name={name} validate={validate}>\r\n      {({ field, meta }: any) => (\r\n        <>\r\n          <Form.Control\r\n            as=\"select\"\r\n            {...field}\r\n            {...props}\r\n            id={id}\r\n            isInvalid={meta.touched && !!meta.error}\r\n            onChange={(e: React.ChangeEvent<HTMLSelectElement>) => {\r\n              field.onChange(e);\r\n              if (onChange) onChange(e);\r\n            }}\r\n            value={value !== undefined ? value : field.value}\r\n          >\r\n            {children}\r\n          </Form.Control>\r\n          {meta.touched && meta.error ? (\r\n            <Form.Control.Feedback type=\"invalid\">\r\n              {meta.error}\r\n            </Form.Control.Feedback>\r\n          ) : null}\r\n        </>\r\n      )}\r\n    </Field>\r\n  );\r\n};\r\n\r\n// Export both components as named exports and as a default object\r\nexport default {\r\n  TextInput,\r\n  SelectGroup\r\n};\r\n"], "names": ["connect", "props", "t", "useTranslation", "isOpen", "manageClose", "data", "handleClose", "profile", "setProfile", "useState", "intialState", "_id", "username", "firstname", "lastname", "position", "institution", "role", "image", "email", "password", "dataConsentPolicy", "restrictedUsePolicy", "acceptCookiesPolicy", "withdrawConsentPolicy", "medicalConsentPolicy", "fullDataProtectionConsentPolicy", "organisation", "setOrganisation", "useEffect", "updateProfile", "id", "prevState", "organisationParams", "query", "sort", "title", "limit", "select", "fetchOrganisation", "organisations", "apiService", "get", "Array", "isArray", "<PERSON><PERSON><PERSON><PERSON>", "e", "target", "name", "value", "<PERSON><PERSON><PERSON><PERSON>", "preventDefault", "post", "dispatch", "loadLoggedinUserData", "toast", "success", "div", "Modal", "show", "onHide", "size", "className", "centered", "ValidationFormWrapper", "onSubmit", "initialValues", "enableReinitialize", "Header", "closeButton", "Title", "Body", "h5", "Form", "Group", "as", "Row", "controlId", "Label", "column", "md", "xs", "lg", "Col", "TextInput", "required", "type", "placeholder", "onChange", "sm", "errorMessage", "SelectGroup", "organisationHandler", "style", "backgroundColor", "borderRadius", "color", "option", "map", "item", "_i", "pattern", "Footer", "<PERSON><PERSON>", "variant", "onClick", "Radio", "RadioGroup", "valueSelected", "children", "errors", "touched", "useFormikContext", "<PERSON><PERSON><PERSON><PERSON>", "React", "childrenWithProps", "child", "isObject", "String", "RadioItem", "label", "disabled", "values", "setFieldValue", "fieldName", "Check", "checked", "inline", "ValidationForm", "forwardRef", "ref", "autoComplete", "onKeyPress", "rest", "validationSchema", "<PERSON><PERSON>", "shape", "<PERSON><PERSON>", "actions", "mockEvent", "stopPropagation", "currentTarget", "nativeEvent", "Event", "bubbles", "cancelable", "defaultPrevented", "eventPhase", "isTrusted", "timeStamp", "Date", "now", "isDefaultPrevented", "isPropagationStopped", "persist", "formikProps", "handleSubmit", "displayName", "validator", "multiline", "rows", "Field", "validate", "stringVal", "val", "trim", "RegExp", "test", "field", "meta", "Control", "isInvalid", "error", "undefined", "<PERSON><PERSON><PERSON>"], "sourceRoot": "", "ignoreList": []}