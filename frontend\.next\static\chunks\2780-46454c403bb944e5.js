"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[2780],{12780:(e,t,i)=>{i.r(t),i.d(t,{default:()=>w});var a=i(37876),s=i(14232),n=i(60282),l=i(49589),o=i(56970),r=i(37784),c=i(29504),d=i(82851),u=i.n(d),m=i(87902),p=i(67814),h=i(97685),_=i(48230),f=i.n(_),y=i(53718);class g{constructor(){this.getApiData=async function(e,t){let i=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};try{let a=await y.A.get(e,i);return t?a.data:a}catch(e){return e.response?e.response:{}}}}}let A=new g;var x=i(50749),j=i(31753),v=i(33657),b=i(33859);let w=e=>{let{institution:t,routes:i}=e,[d,_]=(0,s.useState)([]),[g,w]=(0,s.useState)([]),[P,C]=(0,s.useState)([]),[S,I]=(0,s.useState)(!1),[N,E]=(0,s.useState)(t),[R,k]=(0,s.useState)([]),[F,D]=(0,s.useState)([]),[T,q]=(0,s.useState)(!1),[O,B]=(0,s.useState)([]),{t:U}=(0,j.Bd)("common"),[H,z]=(0,s.useState)({username:"",email:"",_id:null}),G={query:{},sort:{title:"asc"},limit:"~",select:"-firstname -lastname -password -role -country -region -status -is_focal_point -image   -enabled -created_at -updated_at -dataConsentPolicy -restrictedUsePolicy -acceptCookiesPolicy -withdrawConsentPolicy -medicalConsentPolicy -fullDataProtectionConsentPolicy -emailActivateToken"},L={query:{},sort:{username:"asc"},limit:"~",select:"-firstname -lastname -password -role -country -region  -is_focal_point -image  -institution -enabled -created_at -updated_at -dataConsentPolicy -restrictedUsePolicy -acceptCookiesPolicy -withdrawConsentPolicy -medicalConsentPolicy -fullDataProtectionConsentPolicy -emailActivateToken"};(0,s.useEffect)(()=>{K(L)},[]);let Y=e=>e=(e=e.data?[...e.data]:[]).filter(e=>{if(0===e.institutionInvites.filter(e=>e.institutionId===i[1]&&"Rejected"!==e.status).length)return e}),K=async e=>{let t=await y.A.get("/users",e);t.data=Y(t),t&&Array.isArray(t.data)&&k(t.data.map((e,t)=>({label:e.username,value:e._id,email:e.email})));let i=await y.A.get("/users",e);i&&Array.isArray(i.data)&&B(i.data.map((e,t)=>({label:e.username,value:e._id,email:e.email})))};(0,s.useEffect)(()=>{N.focal_points&&N.focal_points.length>0&&(W(u().map(N.focal_points,"_id")),K(L)),N.primary_focal_point&&M()},[N]);let W=async e=>{q(!0);let t=await y.A.get("/users",{...G,query:{_id:e}});if(t.data&&t.data.length>0){let e=[];null==t||t.data.forEach(t=>{null==t||t.institutionInvites.forEach(a=>{a&&a.institutionId===i[1]&&"Approved"===a.status&&e.push({...t,...{institutionId:a.institutionId,institutionName:a.institutionName,institutionStatus:a.status}})})}),_(e),q(!1)}},X=[{name:U("Name"),selector:"focal_points_name",cell:e=>(0,a.jsx)("div",{children:e.username})},{name:U("Email"),selector:"focal_points_email",cell:e=>(0,a.jsx)("div",{children:e.email})},{name:U("Organisation"),selector:"focal_points_institution",cell:e=>(0,a.jsx)("div",{children:e.institution&&e.institution.title})},{name:U("Telephone"),selector:"mobile_number",cell:e=>(0,a.jsx)("div",{children:"".concat(e.dial_code?e.dial_code:""," ").concat(e.mobile_number?e.mobile_number:"")})}],Z=[{name:U("Name"),selector:"username",cell:e=>(0,a.jsx)("div",{children:e.username}),sortable:!0},{name:U("Email"),selector:"email",cell:e=>(0,a.jsx)("div",{children:e.email}),sortable:!0},{name:U("Organisation"),selector:"focal_points_institution",cell:e=>(0,a.jsx)("div",{children:e.institutionName}),sortable:!0},{name:U("Telephone"),selector:"mobile_number",cell:e=>(0,a.jsx)("div",{children:"".concat(e.dial_code?e.dial_code:""," ").concat(e.mobile_number?e.mobile_number:"")})}],J=(0,s.useCallback)(e=>{w(e.selectedRows),I(!1)},[]),M=async()=>{q(!0);let e=N.primary_focal_point,t=await y.A.get("/users",{...G,query:{_id:e}});t.data&&t.data.length>0&&(C(t.data),q(!1))},Q=async()=>{if(g.length<=0||g.length>1)h.Ay.error(U("toast.YoucannotaddmultipleFocalPointasPrimary"));else{let e=g[0]._id,t=await y.A.patch("/institution/".concat(i[1]),{primary_focal_point:e,title:N.title});if(t&&t._id){let e=u().filter(d,["_id",t.primary_focal_point]);E({...N,...{primary_focal_point:t.primary_focal_point}}),C(e),h.Ay.success(U("toast.Primaryfocalpointschangedsuccessfully")),I(!0)}}},V=async()=>{g.length>0?(0,m.ZX)({title:U("toast.deleteFocalPtFromOrg"),message:U("toast.deleteFocalPtFromOrgConfirm"),buttons:[{label:U("yes"),onClick:()=>ee(g)},{label:U("No"),onClick:()=>!1}]}):h.Ay.error(U("toast.Pleaseselectanyfocalpointstodelete"))},$=async e=>{if((null==e?void 0:e.length)&&e.find(e=>e._id===N.primary_focal_point)){let e=await y.A.patch("/institution/".concat(i[1]),{primary_focal_point:"",title:N.title});e&&e._id&&(C([]),I(!0))}},ee=async e=>{$(e);let t=[],a=u().map(e,"_id");if(0>u().indexOf(a,N.primary_focal_point)){let a=u().difference(d,e);t=a,a=u().map(a,e=>({_id:e._id}));let s={title:N.title,focal_points:e};e.forEach(async e=>{e.institutionInvites=e.institutionInvites.filter(e=>e.institutionId!=i[1]),await y.A.patch("/users/".concat(e._id),e)});let n=await y.A.patch("/institution/".concat(i[1],"/updateFocalPoints"),s);n&&n._id&&(h.Ay.success(U("toast.Focalpointsremovedfromorganisationsuccessfuly")),_(t),I(!0),K(L))}else h.Ay.error(U("toast.Youcannotdeleteaprimaryfocalpoint"))},et=async()=>{if(""==H.email)h.Ay.error(U("Emailidisempty"));else{let e=H.email.match(/^([^@]*)@/)[1];if(void 0!==O.find(e=>e.email==H.email))return void h.Ay.error(U("Givenexistinguserinnewuserfield"));Object.assign(H,{username:e}),[].push(H);let t=d.map((e,t)=>({_id:e._id})),a={title:N.title,focal_points:[H,...t]},s=await y.A.patch("/institution/".concat(i[1]),a);if(s&&s._id){h.Ay.success(U("toast.Invitationsentsuccessfully"));let e=await A.getApiData("/institution/".concat(i[1]),!1,{});e&&e._id&&E(e),v.A.inviteNewUserWithEmail(H.email,H.username,s._id,s.title),K(L)}else h.Ay.error(s);z({email:""})}},ei=async()=>{let e=F.map((e,t)=>({_id:e.value}));if(0===e.length)h.Ay.error(U("ExistingUsersisempty"));else{let t={title:N.title,focal_points:[...d,...e]},a=await y.A.patch("/institution/".concat(i[1]),t);if(a&&a._id){F.forEach(async e=>{var t;let i=e.value,s=await y.A.get("/users/".concat(i)),n=[];(null==s||null==(t=s.institutionInvites)?void 0:t.length)?(n=[...s.institutionInvites]).filter(e=>e.institutionId===a._id).length<=0?n.push({institutionName:a.title,institutionId:a._id,status:"Request Pending"}):n=n.map(e=>(e.institutionId===a._id&&"Rejected"===e.status&&(e.status="Request Pending"),e)):n=[{institutionName:a.title,institutionId:a._id,status:"Request Pending"}],s.institutionInvites=n,await y.A.patch("/users/".concat(i),s),K(L)}),h.Ay.success(U("toast.Invitationsentsuccessfully"));let e=await A.getApiData("/institution/".concat(i[1]),!1,{});e&&e._id&&E(e)}D([])}},ea=()=>(0,a.jsx)(a.Fragment,{children:N&&N._id?(0,a.jsx)(f(),{href:"/institution/[...routes]",as:"/institution/edit/".concat(i[1]),children:(0,a.jsxs)(n.A,{variant:"secondary",size:"sm",children:[(0,a.jsx)("i",{className:"fas fa-pen"}),"\xa0",U("Edit")]})}):null}),es=(0,b.canEditInstitution)(()=>(0,a.jsx)(ea,{}));return(0,a.jsxs)(l.A,{children:[(0,a.jsx)(o.A,{className:"mb-4",children:(0,a.jsx)(r.A,{children:(0,a.jsxs)("h4",{className:"institutionTitle",children:[(0,a.jsx)(f(),{href:"/institution/[...routes]",as:"/institution/show/".concat(i[1]),children:N.title}),(0,a.jsx)(es,{})]})})}),(0,a.jsx)(o.A,{children:(0,a.jsx)(r.A,{className:"medium",children:(0,a.jsx)("b",{children:U("PrimaryFocalPoint")})})}),(0,a.jsx)(o.A,{className:"primary-focal-point-table mb-3",children:(0,a.jsx)(x.A,{columns:X,data:P,loading:T,subheader:!1,pagination:!1,pagServer:!0,clearSelectedRows:S})}),(0,a.jsx)(o.A,{children:(0,a.jsx)(r.A,{className:"medium",children:(0,a.jsx)("b",{children:U("FocalPoints")})})}),(0,a.jsx)(o.A,{className:"mb-3",children:(0,a.jsx)(x.A,{columns:Z,data:d,loading:T,pagServer:!0,selectableRows:!0,subheader:!1,onSelectedRowsChange:J,clearSelectedRows:S,persistTableHead:!0,sortFunction:(e,t,i)=>u().orderBy(e,e=>e[t]?e[t].toLowerCase():e[t],i)})}),(0,a.jsx)(o.A,{children:(0,a.jsxs)(r.A,{children:[(0,a.jsx)(n.A,{className:"me-1",onClick:Q,variant:"primary",size:"sm",children:U("Promote")}),(0,a.jsx)(n.A,{onClick:V,variant:"primary",size:"sm",children:U("DeleteFocal")})]})}),(0,a.jsx)("br",{}),(0,a.jsxs)(o.A,{children:[(0,a.jsxs)(r.A,{md:6,children:[(0,a.jsxs)(c.A.Group,{controlId:"formBasicEmail",children:[(0,a.jsx)(c.A.Label,{children:U("external")}),(0,a.jsx)(c.A.Control,{name:"email",type:"email",placeholder:U("Enteremail"),value:H.email,onChange:e=>{if(e.target){let{name:t,value:i}=e.target;z(e=>({...e,[t]:i}))}}})]}),(0,a.jsx)(n.A,{onClick:et,variant:"primary",type:"submit",children:U("InviteFocalPoint")})]}),(0,a.jsxs)(r.A,{md:6,children:[(0,a.jsxs)(c.A.Group,{controlId:"formBasicEmail",children:[(0,a.jsx)(c.A.Label,{children:U("existing")}),(0,a.jsx)(p.KF,{overrideStrings:{selectSomeItems:U("SelectUsers")},options:R,value:F,onChange:e=>{D(e)},className:"focal-users",labelledBy:"Select Users"})]}),(0,a.jsx)(n.A,{onClick:ei,variant:"primary",type:"submit",children:U("Adduser")})]})]})]})}},50749:(e,t,i)=>{i.d(t,{A:()=>r});var a=i(37876);i(14232);var s=i(89773),n=i(31753),l=i(5507);function o(e){let{t}=(0,n.Bd)("common"),i={rowsPerPageText:t("Rowsperpage")},{columns:o,data:r,totalRows:c,resetPaginationToggle:d,subheader:u,subHeaderComponent:m,handlePerRowsChange:p,handlePageChange:h,rowsPerPage:_,defaultRowsPerPage:f,selectableRows:y,loading:g,pagServer:A,onSelectedRowsChange:x,clearSelectedRows:j,sortServer:v,onSort:b,persistTableHead:w,sortFunction:P,...C}=e,S={paginationComponentOptions:i,noDataComponent:t("NoData"),noHeader:!0,columns:o,data:r||[],dense:!0,paginationResetDefaultPage:d,subHeader:u,progressPending:g,subHeaderComponent:m,pagination:!0,paginationServer:A,paginationPerPage:f||10,paginationRowsPerPageOptions:_||[10,15,20,25,30],paginationTotalRows:c,onChangeRowsPerPage:p,onChangePage:h,selectableRows:y,onSelectedRowsChange:x,clearSelectedRows:j,progressComponent:(0,a.jsx)(l.A,{}),sortIcon:(0,a.jsx)("i",{className:"sort-icon fas fa-exchange-alt"}),sortServer:v,onSort:b,sortFunction:P,persistTableHead:w,className:"rki-table"};return(0,a.jsx)(s.Ay,{...S})}o.defaultProps={subHeader:!1,pagination:!0,totalRows:null,pagServer:!0,onSelectedRowsChange:null,clearSelectedRows:!1,sortServer:!1,persistTableHead:!1};let r=o}}]);
//# sourceMappingURL=2780-46454c403bb944e5.js.map