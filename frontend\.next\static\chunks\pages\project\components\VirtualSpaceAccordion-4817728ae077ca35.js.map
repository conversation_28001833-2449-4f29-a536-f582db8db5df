{"version": 3, "file": "static/chunks/pages/project/components/VirtualSpaceAccordion-4817728ae077ca35.js", "mappings": "6LA4GA,MAzFoB,IAClB,GAAM,GAAEA,CAAC,CAAE,CAAGC,CAAAA,EAAAA,EAAAA,EAAAA,CAAcA,CAAC,MAwFhBC,IAvFP,MAAEC,CAAI,EAuFa,EAvFXC,CAAE,CAAE,CAAGC,EACf,CAACC,EAAWC,EAAe,CAAGC,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAC,EAAE,EACzC,CAACC,EAASC,EAAW,CAAGF,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,EAAC,GACjC,CAACG,EAAWC,EAAa,CAAGJ,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAC,GACrC,CAACK,EAASC,EAAW,CAAGN,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAC,IACjC,CAACO,EAAsB,CAAGP,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,EAAC,GAEnCQ,EAAe,CACnBC,KAAM,CAAEC,WAAY,KAAM,EAC1BC,MAAON,EACPO,KAAM,EACNC,MAAO,CAAC,CACV,EAEMC,EAAU,CACd,CACEC,KAAMvB,EAAE,SACRwB,SAAU,QACVC,KAAM,GAAYC,GAAKA,EAAEC,KAAK,EAAID,EAAEE,GAAG,CAAG,UAACC,IAAIA,CAACC,KAAK,sBAAsBC,GAAI,cAAhCF,EAAsD,OAANH,EAAEE,GAAG,WAAMF,EAAEC,KAAK,GAAW,EAC9H,EACA,CACEJ,KAAMvB,EAAE,SACRwB,SAAU,QACVC,KAAM,GAAYC,GAAKA,EAAEM,IAAI,EAAIN,EAAEM,IAAI,CAACC,SAAS,CAAG,GAAuBP,MAAAA,CAApBA,EAAEM,IAAI,CAACC,SAAS,CAAC,KAAmB,OAAhBP,EAAEM,IAAI,CAACE,QAAQ,EAAK,EACjG,EACA,CACEX,KAAMvB,EAAE,iBACRwB,SAAU,aACVC,KAAM,GAAYC,GAAKA,EAAES,UAAU,CAAG,SAAW,SAEnD,EACA,CACEZ,KAAMvB,EAAE,mBACRwB,SAAU,UACVC,KAAM,GAAYC,GAAKA,EAAEU,OAAO,CAAGV,EAAEU,OAAO,CAACC,MAAM,CAAG,GACxD,EACD,CAEKC,EAAkB,MAAOC,IAC7B7B,GAAW,GACX,IAAM8B,EAAW,MAAMC,EAAAA,CAAUA,CAACC,GAAG,CAAC,YAA8BtC,MAAAA,CAAlBD,EAAK,eAAgB,OAAHC,GAAMY,GACtEwB,IACFrC,MADY,UACWI,EAAeiC,EAASG,SAAS,EAAIpC,EAAeiC,EAASI,OAAO,EAC3FhC,EAAa4B,EAASK,UAAU,EAChCnC,GAAW,GAEf,EAQMoC,EAAsB,MAAOC,EAAoB3B,KACrDJ,EAAaG,KAAK,CAAG4B,EACrB/B,EAAaI,IAAI,CAAGA,EACpBV,GAAW,GACX,IAAM8B,EAAW,MAAMC,EAAAA,CAAUA,CAACC,GAAG,CAAC,YAA8BtC,MAAAA,CAAlBD,EAAK,eAAgB,OAAHC,GAAMY,GACtEwB,IACO,MADG,QACZrC,EAAuBI,EAAeiC,EAASG,SAAS,EAAIpC,EAAeiC,EAASI,OAAO,EAC3F9B,EAAWiC,GACXrC,GAAW,GAEf,EAQA,MANAsC,CAAAA,EAAAA,EAAAA,SAAAA,CAASA,CAAC,KACRV,EAAgBtB,EAClB,EAAG,EAAE,EAKH,UAACiC,MAAAA,UACC,UAACC,EAAAA,CAAQA,CAAAA,CACP5B,QAASA,EACT6B,KAAM7C,EACNK,UAAWA,EACXF,QAASA,EACTM,sBAAuBA,EACvB+B,oBAAqBA,EACrBM,iBAjCmB,CAiCDA,GAhCtBpC,EAAaG,KAAK,CAAGN,EACrBG,EAAaI,IAAI,CAAGA,EACpBkB,EAAgBtB,EAClB,KAiCF,6GCrEA,SAASkC,EAAS7C,CAAoB,EACpC,GAAM,GAAEL,CAAC,CAAE,CAAGC,CAAAA,EAAAA,EAAAA,EAAAA,CAAcA,CAAC,UACvBoD,EAA6B,CACjCC,gBAAiBtD,EAAE,cACnB,EACI,SACJsB,CAAO,MACP6B,CAAI,WACJxC,CAAS,uBACTI,CAAqB,WACrBwC,CAAS,oBACTC,CAAkB,qBAClBV,CAAmB,kBACnBM,CAAgB,aAChBK,CAAW,oBACXC,CAAkB,gBAClBC,CAAc,SACdlD,CAAO,WACPmD,CAAS,sBACTC,CAAoB,mBACpBC,CAAiB,YACjBC,CAAU,QACVC,CAAM,CACNC,kBAAgB,cAChBC,CAAY,CAEZ,CADA,EACGC,EACJ,CAAG9D,EAGE+D,EAAiB,4BACrBf,EACAgB,gBAAiBrE,EAAE,IAP0C,MAQ7DsE,UAAU,UACVhD,EACA6B,KAAMA,GAAQ,EAAE,CAChBoB,OAAO,EACPC,2BAA4BzD,EAC5B0D,UAAWlB,EACXmB,gBAAiBjE,qBACjB+C,EACAmB,YAAY,EACZC,iBAAkBhB,EAClBiB,kBAAmBnB,GAA0C,GAC7DoB,eADwCpB,cACVD,GAA4B,CAAC,GAAI,GAAI,GAAI,CAA3BA,EAA+B,GAAG,CAC9EsB,oBAAqBpE,EACrBqE,oBAAqBlC,EACrBmC,aAAc7B,iBACdO,uBACAE,oBACAC,EACAoB,kBAAmB,UAACC,EAAAA,CAAYA,CAAAA,CAAAA,GAChCC,SAAU,UAACC,IAAAA,CAAEC,UAAU,6CACvBvB,SACAC,eACAE,mBACAD,EACAqB,UAAW,WACb,EACA,MACE,UAACC,EAAAA,EAASA,CAAAA,CAAE,GAAGnB,CAAc,EAEjC,CAEAlB,EAASsC,YAAY,CAAG,CACtBf,WAAW,EACXE,YAAY,EACZhE,UAAW,KACXiD,WAAW,EACXC,qBAAsB,KACtBC,mBAAmB,EACnBC,YAAY,EACZE,kBAAkB,CACpB,EAEA,MAAef,QAAQA,EAAC,SC/GxB,4CACA,4CACA,WACA,OAAe,EAAQ,KAAiE,CACxF,EACA,SAFsB,sIC4CtB,MA/B8B,QAkBJ7C,EAAAA,EAjBtB,GAAM,GAAEL,CAAC,CAAE,CAAGC,CAAAA,EAAAA,EAAAA,EAAAA,CAAcA,CAAC,UACvB,CAACwF,EAASC,EAAW,CAAGlF,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,EAAC,GACvC,MACI,+BACI,WAACmF,EAAAA,CAASA,CAACC,IAAI,EAACC,SAAS,cACrB,WAACF,EAAAA,CAASA,CAACG,MAAM,EAACC,QAAS,IAAML,EAAW,CAACD,aACzC,UAACxC,MAAAA,CAAIqC,UAAU,qBAAatF,EAAE,wBAC9B,UAACiD,MAAAA,CAAIqC,UAAU,qBACVG,EACG,UAACO,EAAAA,CAAeA,CAAAA,CAACC,KAAMC,EAAAA,GAAOA,CAAEC,MAAM,SAEtC,UAACH,EAAAA,CAAeA,CAAAA,CAACC,KAAMG,EAAAA,GAAMA,CAAED,MAAM,cAIjD,UAACR,EAAAA,CAASA,CAACU,IAAI,WACX,UAACnG,EAAAA,CAAWA,CAAAA,CACVE,GAAIC,QAAAA,EAAAA,EAAMiG,SAAAA,GAANjG,OAAAA,EAAAA,EAAAA,MAAiBkG,EAAjBlG,KAAAA,EAAAA,CAAyB,CAAC,EAAE,CAA5BA,EAAgC,GACpCF,KAAK,UACLqG,WAAY,EAAE,CACdC,mBAAmB,EACnBC,oBAAqB,EACrBC,kBAAmB,GACnBC,sBAAuB,UAM7C", "sources": ["webpack://_N_E/./components/common/VspaceTable.tsx", "webpack://_N_E/./components/common/RKITable.tsx", "webpack://_N_E/?c006", "webpack://_N_E/./pages/project/components/VirtualSpaceAccordion.tsx"], "sourcesContent": ["//Import Library\r\nimport { useEffect, useState } from \"react\";\r\nimport Link from 'next/link';\r\n\r\n//Import services/components\r\nimport RKITable from \"../../components/common/RKITable\";\r\nimport apiService from \"../../services/apiService\";\r\nimport { useTranslation } from 'next-i18next';\r\n\r\ninterface VspaceTableProps {\r\n  vspaceData: any;\r\n  vspaceDataLoading: boolean;\r\n  vspaceDataTotalRows: number;\r\n  vspaceDataPerPage: number;\r\n  vspaceDataCurrentPage: number;\r\n  type: string;\r\n  id: string;\r\n}\r\n\r\nconst VspaceTable = (props: VspaceTableProps) => {\r\n  const { t } = useTranslation('common');\r\n  const { type, id } = props;\r\n  const [tabledata, setDataToTable] = useState([]);\r\n  const [loading, setLoading] = useState(false);\r\n  const [totalRows, setTotalRows] = useState(0);\r\n  const [perPage, setPerPage] = useState(10);\r\n  const [resetPaginationToggle] = useState(false);\r\n\r\n  const vSpaceParams = {\r\n    sort: { created_at: \"asc\" },\r\n    limit: perPage,\r\n    page: 1,\r\n    query: {},\r\n  };\r\n\r\n  const columns = [\r\n    {\r\n      name: t(\"Title\"),\r\n      selector: \"title\",\r\n      cell: (d: any) => d && d.title && d._id ? <Link href=\"/vspace/[...routes]\" as={`/vspace/show/${d._id}`} >{d.title}</Link> : \"\",\r\n    },\r\n    {\r\n      name: t(\"Owner\"),\r\n      selector: \"users\",\r\n      cell: (d: any) => d && d.user && d.user.firstname ? `${d.user.firstname} ${d.user.lastname}` : \"\"\r\n    },\r\n    {\r\n      name: t(\"PublicPrivate\"),\r\n      selector: \"visibility\",\r\n      cell: (d: any) => d && d.visibility ? \"Public\" : \"Private\",\r\n\r\n    },\r\n    {\r\n      name: t(\"NumberofMembers\"),\r\n      selector: \"members\",\r\n      cell: (d: any) => d && d.members ? d.members.length : \"-\",\r\n    }\r\n  ];\r\n\r\n  const getLinkedVspace = async (vSpaceParams1: any) => {\r\n    setLoading(true);\r\n    const response = await apiService.get(`stats/get${type}WithVspace/${id}`, vSpaceParams);\r\n    if (response) {\r\n      type === \"Operation\" ? setDataToTable(response.operation) : setDataToTable(response.project);\r\n      setTotalRows(response.totalCount);\r\n      setLoading(false);\r\n    }\r\n  };\r\n\r\n  const handlePageChange = (page: number) => {\r\n    vSpaceParams.limit = perPage;\r\n    vSpaceParams.page = page;\r\n    getLinkedVspace(vSpaceParams);\r\n  };\r\n\r\n  const handlePerRowsChange = async (newPerPage: number, page: number) => {\r\n    vSpaceParams.limit = newPerPage;\r\n    vSpaceParams.page = page;\r\n    setLoading(true);\r\n    const response = await apiService.get(`stats/get${type}WithVspace/${id}`, vSpaceParams);\r\n    if (response) {\r\n      type === \"Operation\" ? setDataToTable(response.operation) : setDataToTable(response.project);\r\n      setPerPage(newPerPage);\r\n      setLoading(false);\r\n    }\r\n  };\r\n\r\n  useEffect(() => {\r\n    getLinkedVspace(vSpaceParams);\r\n  }, []);\r\n\r\n\r\n\r\n  return (\r\n    <div>\r\n      <RKITable\r\n        columns={columns}\r\n        data={tabledata}\r\n        totalRows={totalRows}\r\n        loading={loading}\r\n        resetPaginationToggle={resetPaginationToggle}\r\n        handlePerRowsChange={handlePerRowsChange}\r\n        handlePageChange={handlePageChange}\r\n      />\r\n    </div>\r\n  );\r\n}\r\n\r\nexport default VspaceTable;\r\n", "//Import Library\r\nimport React from 'react'\r\nimport DataTable  from 'react-data-table-component';\r\n\r\n//Import services/components\r\nimport { useTranslation } from 'next-i18next';\r\nimport CustomLoader from './CustomLoader';\r\n\r\ninterface RKITableProps {\r\n  columns: any[];\r\n  data: any[];\r\n  totalRows?: number;\r\n  resetPaginationToggle?: boolean;\r\n  subheader?: boolean;\r\n  subHeaderComponent?: React.ReactNode;\r\n  handlePerRowsChange?: (newPerPage: number, page: number) => void;\r\n  handlePageChange?: (page: number) => void;\r\n  rowsPerPage?: number[];\r\n  defaultRowsPerPage?: number;\r\n  selectableRows?: boolean;\r\n  loading?: boolean;\r\n  pagServer?: boolean;\r\n  onSelectedRowsChange?: (selectedRows: any) => void;\r\n  clearSelectedRows?: boolean;\r\n  sortServer?: boolean;\r\n  onSort?: (column: any, sortDirection: string) => void;\r\n  persistTableHead?: boolean;\r\n  sortFunction?: (rows: any[], field: any, direction: 'asc' | 'desc') => any[];\r\n  noHeader?: boolean;\r\n  dense?: boolean;\r\n  pagination?: boolean;\r\n  paginationServer?: boolean;\r\n  paginationTotalRows?: number;\r\n  subHeaderAlign?: string;\r\n  [key: string]: any;\r\n}\r\n\r\nfunction RKITable(props: RKITableProps) {\r\n  const { t } = useTranslation('common');\r\n  const paginationComponentOptions = {\r\n    rowsPerPageText: t('Rowsperpage'),\r\n    };\r\n  const {\r\n    columns,\r\n    data,\r\n    totalRows,\r\n    resetPaginationToggle,\r\n    subheader,\r\n    subHeaderComponent,\r\n    handlePerRowsChange,\r\n    handlePageChange,\r\n    rowsPerPage,\r\n    defaultRowsPerPage,\r\n    selectableRows,\r\n    loading,\r\n    pagServer,\r\n    onSelectedRowsChange,\r\n    clearSelectedRows,\r\n    sortServer,\r\n    onSort,\r\n    persistTableHead,\r\n    sortFunction,\r\n    // Filter out any unknown props that might cause DOM warnings\r\n    ...otherProps\r\n  } = props;\r\n\r\n  // Only pass known DataTable props to avoid DOM warnings\r\n  const dataTableProps = {\r\n    paginationComponentOptions,\r\n    noDataComponent: t(\"NoData\"),\r\n    noHeader: true,\r\n    columns,\r\n    data: data || [],\r\n    dense: true,\r\n    paginationResetDefaultPage: resetPaginationToggle,\r\n    subHeader: subheader,\r\n    progressPending: loading,\r\n    subHeaderComponent,\r\n    pagination: true,\r\n    paginationServer: pagServer,\r\n    paginationPerPage: defaultRowsPerPage ? defaultRowsPerPage : 10,\r\n    paginationRowsPerPageOptions: rowsPerPage ? rowsPerPage : [10, 15, 20, 25, 30],\r\n    paginationTotalRows: totalRows,\r\n    onChangeRowsPerPage: handlePerRowsChange,\r\n    onChangePage: handlePageChange,\r\n    selectableRows,\r\n    onSelectedRowsChange,\r\n    clearSelectedRows,\r\n    progressComponent: <CustomLoader/>,\r\n    sortIcon: <i className=\"sort-icon fas fa-exchange-alt\" />,\r\n    sortServer,\r\n    onSort,\r\n    sortFunction,\r\n    persistTableHead,\r\n    className: \"rki-table\"\r\n  };\r\n  return (\r\n    <DataTable {...dataTableProps} />\r\n  )\r\n};\r\n\r\nRKITable.defaultProps = {\r\n  subHeader: false,\r\n  pagination: true,\r\n  totalRows: null,\r\n  pagServer: true,\r\n  onSelectedRowsChange: null,\r\n  clearSelectedRows: false,\r\n  sortServer: false,\r\n  persistTableHead: false\r\n}\r\n\r\nexport default RKITable;\r\n\r\n", "\n    (window.__NEXT_P = window.__NEXT_P || []).push([\n      \"/project/components/VirtualSpaceAccordion\",\n      function () {\n        return require(\"private-next-pages/project/components/VirtualSpaceAccordion.tsx\");\n      }\n    ]);\n    if(module.hot) {\n      module.hot.dispose(function () {\n        window.__NEXT_P.push([\"/project/components/VirtualSpaceAccordion\"])\n      });\n    }\n  ", "//Import Library\r\nimport React, { useState } from \"react\";\r\nimport { faMinus, faPlus } from \"@fortawesome/free-solid-svg-icons\";\r\nimport { FontAwesomeIcon } from \"@fortawesome/react-fontawesome\";\r\nimport { Accordion, Card } from \"react-bootstrap\";\r\n\r\n//Import services/components\r\nimport VspaceTable from \"../../../components/common/VspaceTable\";\r\nimport { useTranslation } from 'next-i18next';\r\n\r\n\r\ninterface VirtualSpaceAccordionProps {\r\n  routeData: {\r\n    routes: string[];\r\n  };\r\n}\r\n\r\nconst VirtualSpaceAccordion = (props: VirtualSpaceAccordionProps) => {\r\n    const { t } = useTranslation('common');\r\n    const [section, setSection] = useState(false);\r\n    return (\r\n        <>\r\n            <Accordion.Item eventKey=\"0\">\r\n                <Accordion.Header onClick={() => setSection(!section)}>\r\n                    <div className=\"cardTitle\">{t(\"LinkedVirtualSpace\")}</div>\r\n                    <div className=\"cardArrow\">\r\n                        {section ? (\r\n                            <FontAwesomeIcon icon={faMinus} color=\"#fff\" />\r\n                        ) : (\r\n                            <FontAwesomeIcon icon={faPlus} color=\"#fff\" />\r\n                        )}\r\n                    </div>\r\n                </Accordion.Header>\r\n                <Accordion.Body>\r\n                    <VspaceTable\r\n                      id={props.routeData?.routes?.[1] || ''}\r\n                      type=\"Project\"\r\n                      vspaceData={[]}\r\n                      vspaceDataLoading={false}\r\n                      vspaceDataTotalRows={0}\r\n                      vspaceDataPerPage={10}\r\n                      vspaceDataCurrentPage={1}\r\n                    />\r\n                </Accordion.Body>\r\n            </Accordion.Item>\r\n        </>\r\n    )\r\n}\r\nexport default VirtualSpaceAccordion;"], "names": ["t", "useTranslation", "VspaceTable", "type", "id", "props", "tabledata", "setDataToTable", "useState", "loading", "setLoading", "totalRows", "setTotalRows", "perPage", "setPerPage", "resetPaginationToggle", "vSpaceParams", "sort", "created_at", "limit", "page", "query", "columns", "name", "selector", "cell", "d", "title", "_id", "Link", "href", "as", "user", "firstname", "lastname", "visibility", "members", "length", "getLinkedVspace", "vSpaceParams1", "response", "apiService", "get", "operation", "project", "totalCount", "handlePerRowsChange", "newPerPage", "useEffect", "div", "RKITable", "data", "handlePageChange", "paginationComponentOptions", "rowsPerPageText", "subheader", "subHeaderComponent", "rowsPerPage", "defaultRowsPerPage", "selectableRows", "pagServer", "onSelectedRowsChange", "clearSelectedRows", "sortServer", "onSort", "persistTableHead", "sortFunction", "otherProps", "dataTableProps", "noDataComponent", "<PERSON><PERSON><PERSON><PERSON>", "dense", "paginationResetDefaultPage", "subHeader", "progressPending", "pagination", "paginationServer", "paginationPerPage", "paginationRowsPerPageOptions", "paginationTotalRows", "onChangeRowsPerPage", "onChangePage", "progressComponent", "CustomLoader", "sortIcon", "i", "className", "DataTable", "defaultProps", "section", "setSection", "Accordion", "<PERSON><PERSON>", "eventKey", "Header", "onClick", "FontAwesomeIcon", "icon", "faMinus", "color", "faPlus", "Body", "routeData", "routes", "vspaceData", "vspaceDataLoading", "vspaceDataTotalRows", "vspaceDataPerPage", "vspaceDataCurrentPage"], "sourceRoot": "", "ignoreList": []}