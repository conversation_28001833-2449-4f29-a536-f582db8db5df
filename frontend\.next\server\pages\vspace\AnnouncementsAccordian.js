"use strict";(()=>{var e={};e.id=8707,e.ids=[636,3220,8707],e.modules={123:e=>{e.exports=require("dom-helpers/removeEventListener")},1332:e=>{e.exports=require("react-custom-scrollbars-2")},1428:e=>{e.exports=import("axios")},1680:e=>{e.exports=require("@restart/ui/utils")},1919:e=>{e.exports=require("react-transition-group/Transition")},3892:e=>{e.exports=require("classnames")},4048:e=>{e.exports=require("react-truncate")},6009:e=>{e.exports=require("dom-helpers/ownerDocument")},6952:e=>{e.exports=require("@restart/ui/Modal")},7374:e=>{e.exports=require("@restart/ui/Dropdown")},8732:e=>{e.exports=require("react/jsx-runtime")},9653:e=>{e.exports=require("@restart/ui/Overlay")},9855:(e,r,t)=>{t.a(e,async(e,s)=>{try{t.r(r),t.d(r,{config:()=>q,default:()=>x,getServerSideProps:()=>h,getStaticPaths:()=>m,getStaticProps:()=>d,reportWebVitals:()=>g,routeModule:()=>y,unstable_getServerProps:()=>A,unstable_getServerSideProps:()=>S,unstable_getStaticParams:()=>v,unstable_getStaticPaths:()=>j,unstable_getStaticProps:()=>f});var o=t(63885),a=t(80237),i=t(81413),n=t(9616),u=t.n(n),l=t(72386),p=t(59183),c=e([l,p]);[l,p]=c.then?(await c)():c;let x=(0,i.M)(p,"default"),d=(0,i.M)(p,"getStaticProps"),m=(0,i.M)(p,"getStaticPaths"),h=(0,i.M)(p,"getServerSideProps"),q=(0,i.M)(p,"config"),g=(0,i.M)(p,"reportWebVitals"),f=(0,i.M)(p,"unstable_getStaticProps"),j=(0,i.M)(p,"unstable_getStaticPaths"),v=(0,i.M)(p,"unstable_getStaticParams"),A=(0,i.M)(p,"unstable_getServerProps"),S=(0,i.M)(p,"unstable_getServerSideProps"),y=new o.PagesRouteModule({definition:{kind:a.A.PAGES,page:"/vspace/AnnouncementsAccordian",pathname:"/vspace/AnnouncementsAccordian",bundlePath:"",filename:""},components:{App:l.default,Document:u()},userland:p});s()}catch(e){s(e)}})},11242:e=>{e.exports=require("redux-persist/integration/react")},11688:e=>{e.exports=require("@restart/hooks/useWillUnmount")},12388:e=>{e.exports=require("dom-helpers/addEventListener")},13364:e=>{e.exports=require("redux-saga/effects")},13408:e=>{e.exports=require("@restart/hooks/useUpdateEffect")},14062:e=>{e.exports=import("react-redux")},14078:e=>{e.exports=import("swr")},14332:e=>{e.exports=require("uncontrollable")},16116:e=>{e.exports=require("invariant")},18622:e=>{e.exports=require("yup")},19121:e=>{e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},22326:e=>{e.exports=require("react-dom")},22541:e=>{e.exports=require("redux-persist/lib/storage")},25303:e=>{e.exports=require("@restart/ui/NavItem")},26324:e=>{e.exports=require("warning")},27825:e=>{e.exports=require("lodash")},27910:e=>{e.exports=require("stream")},28217:e=>{e.exports=require("@restart/ui/DropdownItem")},29021:e=>{e.exports=require("fs")},29294:e=>{e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},29628:e=>{e.exports=require("dom-helpers/addClass")},29825:e=>{e.exports=require("prop-types")},29841:e=>{e.exports=require("dom-helpers/hasClass")},30362:e=>{e.exports=require("@restart/ui/DropdownToggle")},33873:e=>{e.exports=require("path")},36653:e=>{e.exports=require("nprogress")},36955:e=>{e.exports=require("@restart/hooks/useBreakpoint")},37766:e=>{e.exports=require("@restart/hooks/useMergedRefs")},39756:e=>{e.exports=import("redux")},40051:e=>{e.exports=require("dom-helpers/removeClass")},40361:e=>{e.exports=require("next/dist/compiled/next-server/pages.runtime.prod.js")},42051:e=>{e.exports=require("@restart/hooks/useCommittedRef")},42893:e=>{e.exports=import("react-hot-toast")},43294:e=>{e.exports=require("formik")},46416:(e,r,t)=>{t.r(r),t.d(r,{default:()=>n});var s=t(8732),o=t(49481),a=t(19918),i=t.n(a);function n(e){var r,t;let{item:a}=e;return(0,s.jsxs)(o.A,{className:"p-0",xs:12,children:[(0,s.jsx)(i(),{href:`/${a.type}/[...routes]`,as:`/${a.type}/show/${a[r=a,`parent_${r.type}`]}/update/${a._id}`,children:a.images&&a.images[0]?(0,s.jsx)("img",{src:`http://localhost:3001/api/v1/image/show/${a.images[0]._id}`,alt:"announcement",className:"announceImg"}):(0,s.jsx)("i",{className:"fa fa-bullhorn announceImg"})}),(0,s.jsxs)("div",{className:"announceDesc",children:[(0,s.jsx)(i(),{href:`/${a.type}/[...routes]`,as:`/${a.type}/show/${a[t=a,`parent_${t.type}`]}/update/${a._id}`,children:a&&a.title?a.title:""}),(0,s.jsx)("p",{children:a&&a.description?(e=>{let r=document.createElement("div");r.innerHTML=e;let t=r.textContent||r.innerText||"";return t.length>260?`${t.substring(0,257)}...`:t})(a.description):null})]})]})}},50009:e=>{e.exports=require("@restart/ui/DropdownMenu")},54131:e=>{e.exports=import("@fortawesome/free-solid-svg-icons")},57664:e=>{e.exports=require("dom-helpers/scrollbarSize")},58928:e=>{e.exports=require("dom-helpers/canUseDOM")},59183:(e,r,t)=>{t.a(e,async(e,s)=>{try{t.r(r),t.d(r,{default:()=>x});var o=t(8732),a=t(54131),i=t(82053),n=t(93024),u=t(82015),l=t(75403),p=t(88751),c=e([a,l]);[a,l]=c.then?(await c)():c;let x=e=>{let{t:r}=(0,p.useTranslation)("common"),[t,s]=(0,u.useState)(!1);return(0,o.jsx)(o.Fragment,{children:(0,o.jsxs)(n.A.Item,{eventKey:"1",children:[(0,o.jsxs)(n.A.Header,{onClick:()=>s(!t),children:[(0,o.jsx)("div",{className:"cardTitle",children:r("vspace.Announcements")}),(0,o.jsx)("div",{className:"cardArrow",children:t?(0,o.jsx)(i.FontAwesomeIcon,{icon:a.faMinus,color:"#fff"}):(0,o.jsx)(i.FontAwesomeIcon,{icon:a.faPlus,color:"#fff"})})]}),(0,o.jsx)(n.A.Body,{children:(0,o.jsx)(l.default,{})})]})})};s()}catch(e){s(e)}})},59717:e=>{e.exports=require("@restart/ui/DropdownContext")},60560:e=>{e.exports=require("dom-helpers/contains")},63033:e=>{e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},65447:e=>{e.exports=require("@restart/ui/Button")},67364:e=>{e.exports=require("@restart/hooks/useCallbackRef")},68455:e=>{e.exports=import("redux-saga")},69722:e=>{e.exports=require("es6-promise")},74075:e=>{e.exports=require("zlib")},74716:e=>{e.exports=require("moment")},74987:e=>{e.exports=require("@restart/ui/ModalManager")},75403:(e,r,t)=>{t.a(e,async(e,s)=>{try{t.r(r),t.d(r,{default:()=>g});var o=t(8732),a=t(82015),i=t(83551),n=t(7082),u=t(20174),l=t(44233);t(27825);var p=t(46416),c=t(13866),x=t.n(c),d=t(63487),m=t(88751),h=e([d]);function q(e){let{announcements:r}=e;return(0,o.jsx)("div",{children:r.map((e,r)=>(0,o.jsx)(i.A,{className:"announcementItem",children:(0,o.jsx)(p.default,{item:e})},r))})}d=(h.then?(await h)():h)[0];let g=function(){let{t:e}=(0,m.useTranslation)("common"),r=(0,l.useRouter)().query.routes||[],[t,s]=(0,a.useState)([]),[p,c]=(0,a.useState)(0),[d]=(0,a.useState)(3);r[1];let h=e=>{let r=p,[s,o]=[0,d-1];"next"===e?r++:"prev"===e&&r--,r>o&&(r=0),r<s&&(r=o),t.length-r==1&&(r=t.length-1),t.length-r==0&&(r=1),c(r)};return(0,o.jsx)("div",{className:"announcements mt-0",children:t&&t.length>0?(0,o.jsxs)(o.Fragment,{children:[(0,o.jsx)(n.A,{fluid:!0,children:(0,o.jsxs)(i.A,{children:[(0,o.jsx)(u.Ay,{xs:10,className:"p-0"}),t&&t.length>1?(0,o.jsx)(u.Ay,{xs:2,className:"text-end carousel-control p-0",children:(0,o.jsxs)("div",{className:"carousel-navigation",children:[(0,o.jsx)("a",{className:"left carousel-control",onClick:()=>h("prev"),children:(0,o.jsx)("i",{className:"fa fa-chevron-left"})}),(0,o.jsx)("a",{className:"right carousel-control",onClick:()=>h("next"),children:(0,o.jsx)("i",{className:"fa fa-chevron-right"})})]})}):null]})}),(0,o.jsx)(n.A,{fluid:!0,children:(0,o.jsx)(i.A,{children:(0,o.jsx)(u.Ay,{xs:12,className:"p-0",children:(0,o.jsx)(x(),{indicators:!1,controls:!1,interval:null,activeIndex:p,children:t.map((e,r)=>(0,o.jsx)(x().Item,{children:(0,o.jsx)(q,{announcements:e})},r))})})})})]}):(0,o.jsx)(n.A,{fluid:!0,children:(0,o.jsx)(i.A,{children:(0,o.jsx)(u.Ay,{xs:12,className:"p-0",children:(0,o.jsx)("div",{className:"border border-info m-3",children:(0,o.jsx)("p",{className:"d-flex d-flex justify-content-center p-2 m-0",children:e("NoAnnouncementFound!")})})})})})})};s()}catch(e){s(e)}})},78097:e=>{e.exports=require("next-redux-saga")},78634:e=>{e.exports=require("@restart/ui/Anchor")},81366:e=>{e.exports=require("redux-auth-wrapper/connectedAuthWrapper")},81521:e=>{e.exports=require("dom-helpers/querySelectorAll")},81895:e=>{e.exports=require("@restart/hooks/useEventCallback")},82015:e=>{e.exports=require("react")},82053:e=>{e.exports=require("@fortawesome/react-fontawesome")},86842:e=>{e.exports=require("@restart/ui/SelectableContext")},87571:e=>{e.exports=require("dom-helpers/transitionEnd")},88751:e=>{e.exports=require("next-i18next")},93787:e=>{e.exports=require("redux-persist")},94947:e=>{e.exports=require("@restart/hooks/useTimeout")},96196:e=>{e.exports=require("next-redux-wrapper")},98320:e=>{e.exports=require("@restart/hooks/useIsomorphicEffect")},99460:e=>{e.exports=require("dom-helpers/css")}};var r=require("../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[6089,9216,9616,4033,2386],()=>t(9855));module.exports=s})();