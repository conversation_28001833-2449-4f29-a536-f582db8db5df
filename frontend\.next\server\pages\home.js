"use strict";(()=>{var e={};e.id=9845,e.ids=[636,3220,9845],e.modules={123:e=>{e.exports=require("dom-helpers/removeEventListener")},1332:e=>{e.exports=require("react-custom-scrollbars-2")},1428:e=>{e.exports=import("axios")},1680:e=>{e.exports=require("@restart/ui/utils")},1919:e=>{e.exports=require("react-transition-group/Transition")},3892:e=>{e.exports=require("classnames")},4048:e=>{e.exports=require("react-truncate")},6009:e=>{e.exports=require("dom-helpers/ownerDocument")},6952:e=>{e.exports=require("@restart/ui/Modal")},7374:e=>{e.exports=require("@restart/ui/Dropdown")},8732:e=>{e.exports=require("react/jsx-runtime")},9653:e=>{e.exports=require("@restart/ui/Overlay")},10192:(e,t,r)=>{r.d(t,{A:()=>a});var s=r(8732);r(82015);var i=r(83551),n=r(49481);let a=({innerRef:e})=>(0,s.jsx)("div",{className:"about-us",ref:e,children:(0,s.jsx)("div",{className:"aboutus-wrap",children:(0,s.jsxs)(i.A,{xs:12,children:[(0,s.jsx)(n.A,{sm:6,children:(0,s.jsx)("img",{src:"/images/home/<USER>",alt:"About",width:"100%"})}),(0,s.jsx)(n.A,{sm:6,children:(0,s.jsxs)("div",{className:"content",children:[(0,s.jsx)("div",{className:"title",children:"About us"}),(0,s.jsx)("div",{className:"desc",children:"The German Ministry of Health has commissioned the Robert Koch Institut (RKI) to develop this platform. Under the coordination of the Centre for International Health Protection, based at RKI in Berlin, we want to support our partners in exchanging information in order to better coordinate the activities of all of them and thus strengthen the response capacity of all. "})]})})]})})})},10462:(e,t,r)=>{r.a(e,async(e,s)=>{try{r.r(t),r.d(t,{config:()=>g,default:()=>u,getServerSideProps:()=>x,getStaticPaths:()=>m,getStaticProps:()=>p,reportWebVitals:()=>f,routeModule:()=>A,unstable_getServerProps:()=>w,unstable_getServerSideProps:()=>y,unstable_getStaticParams:()=>v,unstable_getStaticPaths:()=>j,unstable_getStaticProps:()=>b});var i=r(63885),n=r(80237),a=r(81413),o=r(9616),l=r.n(o),c=r(72386),d=r(83810),h=e([c,d]);[c,d]=h.then?(await h)():h;let u=(0,a.M)(d,"default"),p=(0,a.M)(d,"getStaticProps"),m=(0,a.M)(d,"getStaticPaths"),x=(0,a.M)(d,"getServerSideProps"),g=(0,a.M)(d,"config"),f=(0,a.M)(d,"reportWebVitals"),b=(0,a.M)(d,"unstable_getStaticProps"),j=(0,a.M)(d,"unstable_getStaticPaths"),v=(0,a.M)(d,"unstable_getStaticParams"),w=(0,a.M)(d,"unstable_getServerProps"),y=(0,a.M)(d,"unstable_getServerSideProps"),A=new i.PagesRouteModule({definition:{kind:n.A.PAGES,page:"/home",pathname:"/home",bundlePath:"",filename:""},components:{App:c.default,Document:l()},userland:d});s()}catch(e){s(e)}})},11242:e=>{e.exports=require("redux-persist/integration/react")},11688:e=>{e.exports=require("@restart/hooks/useWillUnmount")},12388:e=>{e.exports=require("dom-helpers/addEventListener")},13364:e=>{e.exports=require("redux-saga/effects")},14062:e=>{e.exports=import("react-redux")},14078:e=>{e.exports=import("swr")},14332:e=>{e.exports=require("uncontrollable")},16116:e=>{e.exports=require("invariant")},18622:e=>{e.exports=require("yup")},19121:e=>{e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},22326:e=>{e.exports=require("react-dom")},22541:e=>{e.exports=require("redux-persist/lib/storage")},23878:(e,t,r)=>{r.a(e,async(e,s)=>{try{r.d(t,{A:()=>h});var i=r(8732);r(82015);var n=r(83551),a=r(49481),o=r(19918),l=r.n(o),c=r(61972),d=e([c]);c=(d.then?(await d)():d)[0];let h=({onScroll:e})=>(0,i.jsx)("div",{className:"header-block",children:(0,i.jsx)("div",{className:"header-container",style:{},children:(0,i.jsxs)(n.A,{children:[(0,i.jsx)(a.A,{sm:3,className:"logo-image",children:(0,i.jsx)("img",{src:"/images/home/<USER>",height:"60px"})}),(0,i.jsx)(a.A,{sm:9,children:(0,i.jsxs)("ul",{className:"quick-menu",children:[(0,i.jsx)("li",{children:(0,i.jsx)("a",{onClick:()=>e(0),children:"Home"})}),(0,i.jsx)("li",{children:(0,i.jsx)("a",{onClick:()=>e(1),children:"Our network"})}),(0,i.jsx)("li",{children:(0,i.jsx)("a",{onClick:()=>e(2),children:"About us"})}),(0,i.jsx)("li",{children:(0,i.jsx)("a",{onClick:()=>e(3),children:"Contact us"})}),(0,i.jsx)("li",{children:(0,i.jsx)(c.A,{show:!1,onHide:()=>{}})}),(0,i.jsx)("li",{className:"login",children:(0,i.jsx)(l(),{href:"/login",children:"Login"})})]})})]})})});s()}catch(e){s(e)}})},25303:e=>{e.exports=require("@restart/ui/NavItem")},26324:e=>{e.exports=require("warning")},27825:e=>{e.exports=require("lodash")},27910:e=>{e.exports=require("stream")},28217:e=>{e.exports=require("@restart/ui/DropdownItem")},29021:e=>{e.exports=require("fs")},29294:e=>{e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},29628:e=>{e.exports=require("dom-helpers/addClass")},29825:e=>{e.exports=require("prop-types")},29841:e=>{e.exports=require("dom-helpers/hasClass")},30362:e=>{e.exports=require("@restart/ui/DropdownToggle")},33873:e=>{e.exports=require("path")},35576:e=>{e.exports=require("next-i18next/serverSideTranslations")},36653:e=>{e.exports=require("nprogress")},36955:e=>{e.exports=require("@restart/hooks/useBreakpoint")},37766:e=>{e.exports=require("@restart/hooks/useMergedRefs")},39212:(e,t,r)=>{r.d(t,{A:()=>l});var s=r(8732);r(82015);var i=r(49481),n=r(19918),a=r.n(n),o=r(87503);let l=()=>(0,s.jsxs)(i.A,{sm:8,children:[(0,s.jsx)("div",{className:"block-title network",children:"Our Networks"}),o.dI.map((e,t)=>(0,s.jsxs)("div",{className:"network-news",children:[(0,s.jsx)("img",{src:o.vY[t]}),(0,s.jsxs)("div",{className:"newsContent",children:[(0,s.jsx)("div",{className:"newsTitle",children:e.title}),(0,s.jsxs)("div",{className:"newsDesc",children:[e.content,e.href?(0,s.jsx)(a(),{href:e.href,target:"_blank",children:"Find more information..."}):null]})]})]},t))]})},39756:e=>{e.exports=import("redux")},40051:e=>{e.exports=require("dom-helpers/removeClass")},40361:e=>{e.exports=require("next/dist/compiled/next-server/pages.runtime.prod.js")},41500:(e,t,r)=>{r.d(t,{A:()=>a});var s=r(8732);r(82015);var i=r(49481),n=r(83551);let a=({innerRef:e})=>(0,s.jsx)("div",{className:"landing-footer",ref:e,children:(0,s.jsx)("div",{className:"footer-block",children:(0,s.jsx)(i.A,{xs:12,children:(0,s.jsxs)(n.A,{children:[(0,s.jsx)(i.A,{sm:3,style:{marginTop:"10px"},children:(0,s.jsx)("img",{src:"/images/home/<USER>",className:"img-fluid"})}),(0,s.jsx)(i.A,{sm:3,style:{marginTop:"10px"},children:(0,s.jsx)("div",{className:"footerLeft",children:(0,s.jsxs)("div",{style:{lineHeight:"14px"},children:[(0,s.jsx)("p",{children:"The Robert Koch Institut is a Federal Institute within the portfolio of the Federal Ministry of Health"}),(0,s.jsxs)("p",{children:[(0,s.jsx)("br",{}),(0,s.jsx)("span",{style:{fontFamily:"verdana"},children:"\xa9"})," ",new Date().getFullYear()," Robert Koch Institute"]}),(0,s.jsx)("p",{children:"All rights reserved unless explicitly granted."})]})})}),(0,s.jsx)(i.A,{sm:3,children:(0,s.jsxs)("div",{className:"contactInfo",style:{marginTop:"3px"},children:[(0,s.jsx)("p",{style:{fontSize:"16px"},children:"Robert Koch Institut"}),(0,s.jsxs)("p",{children:["Federal Information Centre for International",(0,s.jsx)("br",{}),"Health Protection (ZIG1)"]}),(0,s.jsx)("p",{children:"Nordufer 20"}),(0,s.jsx)("p",{children:"13353 Berlin "}),(0,s.jsx)("p",{children:"Germany"}),(0,s.jsx)("br",{}),(0,s.jsx)("br",{})]})}),(0,s.jsx)(i.A,{sm:3,style:{marginTop:"10px"},children:(0,s.jsxs)("div",{className:"contactNum",children:[(0,s.jsx)("p",{children:"Tel: +49 (0) 3018 7540"}),(0,s.jsx)("p",{children:"Email: <EMAIL>"})]})})]})})})})},42893:e=>{e.exports=import("react-hot-toast")},43294:e=>{e.exports=require("formik")},50009:e=>{e.exports=require("@restart/ui/DropdownMenu")},55949:(e,t,r)=>{r.a(e,async(e,s)=>{try{r.d(t,{A:()=>x});var i=r(8732),n=r(82015),a=r(83551),o=r(23878),l=r(85890),c=r(80213),d=r(39212),h=r(97544),u=r(10192),p=r(41500),m=e([o]);o=(m.then?(await m)():m)[0];let x=()=>{let e=(0,n.useRef)(null),t=(0,n.useRef)(null),r=(0,n.useRef)(null),s=(0,n.useRef)(null);return(0,i.jsxs)("div",{className:"landing",children:[(0,i.jsx)(o.A,{onScroll:i=>{let n=[e,t,r,s];if(n[i]&&n[i].current){let e=window.pageYOffset+n[i].current.getBoundingClientRect().top-100;window.scrollTo({behavior:"smooth",top:e})}}}),(0,i.jsx)("div",{className:"horizontal-line"}),(0,i.jsx)("div",{className:"home",ref:e,children:(0,i.jsx)(l.A,{})}),(0,i.jsx)(c.A,{}),(0,i.jsx)("div",{ref:t,children:(0,i.jsxs)(a.A,{className:"feeds-section",children:[(0,i.jsx)(d.A,{}),(0,i.jsx)(h.A,{})]})}),(0,i.jsx)(u.A,{innerRef:r}),(0,i.jsx)(p.A,{innerRef:s})]})};s()}catch(e){s(e)}})},57664:e=>{e.exports=require("dom-helpers/scrollbarSize")},58928:e=>{e.exports=require("dom-helpers/canUseDOM")},59717:e=>{e.exports=require("@restart/ui/DropdownContext")},60560:e=>{e.exports=require("dom-helpers/contains")},61972:(e,t,r)=>{r.a(e,async(e,s)=>{try{r.d(t,{A:()=>p});var i=r(8732),n=r(63241),a=r(23778),o=r(12403),l=r(93024),c=r(82015),d=r(63487),h=r(88751),u=e([d]);function p(e){let[t,r]=(0,c.useState)(!1),[,s]=(0,c.useState)({title:"",description:"",pageCategory:"",isEnabled:!0}),d=e=>({__html:e}),{t:u}=(0,h.useTranslation)("common");return(0,i.jsxs)("div",{children:[(0,i.jsx)("a",{onClick:()=>r(!0),children:(0,i.jsx)(n.A,{placement:"bottom",delay:{show:250,hide:400},overlay:(0,i.jsx)(a.A,{id:"print-tooltip",children:u("Help")}),children:(0,i.jsx)("i",{className:"fas fa-question-circle"})})}),(0,i.jsxs)(o.A,{size:"lg",show:t,onHide:()=>r(!1),children:[(0,i.jsx)(o.A.Header,{closeButton:!0,children:(0,i.jsx)(o.A.Title,{id:"help-modal",children:u("Help")})}),(0,i.jsx)(o.A.Body,{children:(0,i.jsx)("ul",{children:(0,i.jsx)(l.A,{children:(0,i.jsxs)(l.A.Item,{eventKey:"0",children:[(0,i.jsx)("li",{className:"help-content-li-title",children:(0,i.jsx)(l.A.Header,{children:(0,i.jsx)("div",{dangerouslySetInnerHTML:d("How do I access the RKI Platform?")})})}),(0,i.jsx)(l.A.Body,{className:"help-content-li-content",children:(0,i.jsx)("div",{dangerouslySetInnerHTML:d("The RKI Platform is accessible by invitation only to cooperation partner organisations in the field of Health Protection and their staff, through their organisations focal points. If your organisation is already part of the platform and you would like to join please check with your lead focal point as they will be able to add you. <br /><br />  If your organisation is not registered, but you would like to add your organization to our platform, you can email the team at  <EMAIL>.<br/><br/>  We ask that all users of the platform allow their name, title and email to be shared to facilitate communications across the network. We have data protection rules in place to protect against the misuse of data. For further information please click <a href='https://www.rki.de/DE/Service/Datenschutz/datenschutzerklaerung_node.html' target='_blank'>here</a><br /><br />Thank you for your interest.<br/>Kind regards,")})})]})})})})]})]})}d=(u.then?(await u)():u)[0],s()}catch(e){s(e)}})},63033:e=>{e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},65447:e=>{e.exports=require("@restart/ui/Button")},67364:e=>{e.exports=require("@restart/hooks/useCallbackRef")},68455:e=>{e.exports=import("redux-saga")},69722:e=>{e.exports=require("es6-promise")},74075:e=>{e.exports=require("zlib")},74716:e=>{e.exports=require("moment")},74987:e=>{e.exports=require("@restart/ui/ModalManager")},78097:e=>{e.exports=require("next-redux-saga")},78634:e=>{e.exports=require("@restart/ui/Anchor")},80213:(e,t,r)=>{r.d(t,{A:()=>i});var s=r(8732);r(82015);let i=()=>(0,s.jsx)("div",{className:"welcome",children:(0,s.jsxs)("div",{className:"about-section",children:[(0,s.jsx)("div",{className:"title",children:"Welcome to the new Knowledge Platform for international health protection"}),(0,s.jsx)("p",{children:"The purpose of the new Knowledge Platform is to enable German Institutions and partners, who work in the field of international health protection, to share information in order to strengthen our all’s capacity to respond and engage all partners effectively within this network. Non-commercial entities and institutions with an ability to support outbreak control and public health emergencies are welcome to the network. The site disseminates information concerning events, projects, operations, partner institutions and activities of the network. In addition, the platform also creates a forum for partners to collaborate through virtual spaces, including spaces created for specific purpose such as WHO Collaboration Centres or Emergency Medical Teams. The development of the platform was financed by the German Ministry of Health."})]})})},80237:(e,t)=>{Object.defineProperty(t,"A",{enumerable:!0,get:function(){return r}});var r=function(e){return e.PAGES="PAGES",e.PAGES_API="PAGES_API",e.APP_PAGE="APP_PAGE",e.APP_ROUTE="APP_ROUTE",e.IMAGE="IMAGE",e}({})},81366:e=>{e.exports=require("redux-auth-wrapper/connectedAuthWrapper")},81413:(e,t)=>{Object.defineProperty(t,"M",{enumerable:!0,get:function(){return function e(t,r){return r in t?t[r]:"then"in t&&"function"==typeof t.then?t.then(t=>e(t,r)):"function"==typeof t&&"default"===r?t:void 0}}})},81521:e=>{e.exports=require("dom-helpers/querySelectorAll")},81895:e=>{e.exports=require("@restart/hooks/useEventCallback")},82015:e=>{e.exports=require("react")},83810:(e,t,r)=>{r.a(e,async(e,s)=>{try{r.r(t),r.d(t,{default:()=>c,getStaticProps:()=>l});var i=r(8732);r(82015);var n=r(55949),a=r(35576),o=e([n]);n=(o.then?(await o)():o)[0];let l=async e=>{let t=e.locale||"en";return{props:{...await (0,a.serverSideTranslations)(t,["common"])}}},c=()=>(0,i.jsx)(n.A,{});s()}catch(e){s(e)}})},85890:(e,t,r)=>{r.d(t,{A:()=>c});var s=r(8732);r(82015);var i=r(83551),n=r(49481),a=r(19918),o=r.n(a),l=r(87503);let c=()=>(0,s.jsx)("div",{className:"slider",children:(0,s.jsx)(i.A,{children:(0,s.jsx)(n.A,{xs:12,children:l.SO.map((e,t)=>(0,s.jsxs)("div",{className:"myslider",children:[(0,s.jsx)("img",{src:"images/home/<USER>",width:"100%"}),(0,s.jsxs)("div",{className:"sliderContent",children:[(0,s.jsx)("div",{className:"sliderTitle",children:e.title}),(0,s.jsxs)("div",{className:"sliderDesc",children:[e.content,(0,s.jsx)(o(),{href:e.href,target:"_blank",children:"More Information"})]})]})]},t))})})})},86842:e=>{e.exports=require("@restart/ui/SelectableContext")},87503:(e,t,r)=>{r.d(t,{Md:()=>i,SO:()=>n,dI:()=>a,vY:()=>s,y$:()=>o});let s={0:"images/home/<USER>",1:"images/home/<USER>",2:"images/home/<USER>",3:"images/home/<USER>",4:"images/home/<USER>",5:"images/home/<USER>",6:"images/home/<USER>",7:"images/home/<USER>",8:"images/home/<USER>"},i={0:"images/home/<USER>",1:"images/home/<USER>",2:"images/home/<USER>",3:"images/home/<USER>",4:"images/home/<USER>"},n=[{title:"SARS-CoV-2 in Germany",content:"After a temporary stabilisation of case numbers at a higher level in late August and early September 2020, a steep increase in case numbers ensued in October in all federal states. Due to measures implemented at the beginning of November the rise in cases could be stopped, albeit no considerable reduction in case numbers ensued. Since 04/12/2020 case numbers have been sharply increasing again... ",href:"https://www.rki.de/EN/Content/infections/epidemiology/outbreaks/COVID-19/Situationsberichte_Tab.html;jsessionid=0D6329A535C4B9D303C461108C27A545.internet051",image:"images/home/<USER>"}],a=[{title:"The Robert Koch Institut as international hub for health protection networks",content:"When there are health emergencies across the world, such as disease outbreaks, the Robert Koch Institut’s expertise is in ever greater demand. RKI staff are involved in various international research projects and programmes. Thus, the RKI helps to tackle urgent public health problems and improve people’s health worldwide. ",href:"https://www.rki.de/EN/Content/Institute/International/international_activities_node.html"},{title:"About the Global Health Protection Programme",content:"As part of its international commitments, Germany is providing  increasing support to partner countries for the management of outbreaks and the development of reliable heafthcare systems. To this end, the German Federal Ministry of Health has launched a Global Health​ Protection Programme to improve and promote health at global scale. ",href:"https://ghpp.de/en/"},{title:"Global Health Policy",content:"Global health issues are closely related to several other policy areas such as development, security, trade and travel, the economy, human rights, nutrition, agriculture, research, employment, education, migration, the environment and climate protection, as well as humanitarian aid. ",href:"https://www.bundesgesundheitsministerium.de/en/international-co-operation.html"},{title:"German Biosecurity Programme",content:"The German Biosecurity, founded by the Federal Foreign Office, is intended to help partner countries tackle biosecurity threats, such as the intentional misuse of biological pathogens and toxins or outbreaks of highly pathogenic diseases and pandemics. The aim is to strengthen the health services of our partner countries in Africa, Central Asia and Eastern Europe, thus enhancing their national security. ",href:"https://www.auswaertiges-amt.de/en/aussenpolitik/themen/abruestung/uebersicht-bcwaffen-node/-/239362"},{title:"Emergency Mobile Laboratory",content:"The European Mobile Laboratory (EMLab) Consortium deploy state of the art boxed field laboratories as well as trained scientists and technicians to epidemics and outbreaks of infectious diseases caused by pathogens up to risk group 4 to perform diagnostics and supporting clinical laboratory analysis on site. ",href:"https://www.emlab.eu/"},{title:"Emergency Medical Teams",content:"The purpose of the Emergency Medical Teams initiative is to improve the timeliness and quality of health services provided by national and international EMTs and enhance the capacity of national health systems in leading the activation and coordination of this response in the immediate aftermath of a disaster, outbreak and/or other emergency. ",href:"https://extranet.who.int/emt/"},{title:"Over 800 institutions in over 80 countries supporting WHO programmes",content:"WHO collaborating centres are institutions such as research institutes, parts of universities or academies, which are designated by the Director-General to carry out activities in support of the Organization's programmes. Currently there are 24 from over 800 WHO collaborating centres in Germany. ",href:"https://www.who.int/about/who-we-are/structure/collaborating-centres"},{title:"Global Outbreak Alert and Response Network (GOARN) ",content:"GOARN is a collaboration of existing institutions and networks, constantly alert and ready to respond. The network pools human and technical resources for rapid identification, confirmation and response to outbreaks of international importance. ",href:"https://extranet.who.int/goarn"},{title:"International Association of National Public Health Institutes",content:"The International Association of National Public Health Institutes is a member organization of government agencies working to improve national disease prevention and response. IANPHI is made up of 100+ members, located in approximately 90 countries. "}],o=[{title:"PEI_Germany",tag:"@PEI_Germany",content:"Exten\xadsively drug-resis\xadtant Kleb\xadsiella pneu\xadmoniae out\xadbreak, north-eastern Ger\xadmany, 2019 (Euro\xadsurveillance, 12.12.2019)"},{title:"BMG_Bund",tag:"@BMG_Bund",content:"Angesichts der Entwicklung in Italien rechnet Bundesgesundheitsminister Jens Spahn damit, dass sich das Coronavirus auch in Deutschland"},{title:"Bfarm_de",tag:"@bfarm_de",content:"Was genau macht eigentlich das BfArM? Knapp vier Minuten Film geben Einblicke in unsere Aufgaben, unseren Arbeitsalltag und unsere Motivation."},{title:"FZBorstel",tag:"@FZBorstel",content:"The Research Center Borstel, Germany is hosting the next #lipidomics forum! Please check it out, share, spread the word, retweet, register!"},{title:"Loeffler_News",tag:"@Loeffler_News",content:"With the current cases in Poland, #AfricanSwineFever made a westerly leap of about 250 km and has now moved about 80 km from the German border."}]},87571:e=>{e.exports=require("dom-helpers/transitionEnd")},88751:e=>{e.exports=require("next-i18next")},93787:e=>{e.exports=require("redux-persist")},94947:e=>{e.exports=require("@restart/hooks/useTimeout")},96196:e=>{e.exports=require("next-redux-wrapper")},97544:(e,t,r)=>{r.d(t,{A:()=>a});var s=r(8732);r(82015);var i=r(49481),n=r(87503);let a=()=>(0,s.jsxs)(i.A,{sm:4,children:[(0,s.jsx)("div",{className:"block-title news-feeds",children:"News Feeds"}),n.y$.map((e,t)=>(0,s.jsxs)("div",{className:"feed-news",children:[(0,s.jsx)("img",{src:n.Md[t]}),(0,s.jsxs)("div",{className:"newsContent",children:[(0,s.jsxs)("div",{className:"newsTitle",children:[e.title," ",(0,s.jsx)("span",{children:e.tag})]}),(0,s.jsx)("div",{className:"newsDesc",children:e.content})]})]},t))]})},98320:e=>{e.exports=require("@restart/hooks/useIsomorphicEffect")},99460:e=>{e.exports=require("dom-helpers/css")}};var t=require("../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[6089,9216,9616,2386],()=>r(10462));module.exports=s})();