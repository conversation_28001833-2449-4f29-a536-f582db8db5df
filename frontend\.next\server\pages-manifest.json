{"/_app": "pages/_app.js", "/_error": "pages/_error.js", "/admin/login": "pages/admin/login.js", "/adminsettings/approval/AdminTable": "pages/adminsettings/approval/AdminTable.js", "/adminsettings/approval/InstitutionTable": "pages/adminsettings/approval/InstitutionTable.js", "/adminsettings/approval/VspaceAdmin": "pages/adminsettings/approval/VspaceAdmin.js", "/adminsettings/approval/vspace_appoval": "pages/adminsettings/approval/vspace_appoval.js", "/adminsettings/areaOfWork/areaOfWorkTable": "pages/adminsettings/areaOfWork/areaOfWorkTable.js", "/adminsettings/areaOfWork/forms": "pages/adminsettings/areaOfWork/forms.js", "/adminsettings/areaOfWork": "pages/adminsettings/areaOfWork.js", "/adminsettings/categories/categoryTable": "pages/adminsettings/categories/categoryTable.js", "/adminsettings/categories/form": "pages/adminsettings/categories/form.js", "/adminsettings/categories": "pages/adminsettings/categories.js", "/adminsettings/content/ContentTableFilter": "pages/adminsettings/content/ContentTableFilter.js", "/adminsettings/country/countryTable": "pages/adminsettings/country/countryTable.js", "/adminsettings/country/countryTableFilter": "pages/adminsettings/country/countryTableFilter.js", "/adminsettings/country/form": "pages/adminsettings/country/form.js", "/adminsettings/deploymentstatus/deploymentstatusTable": "pages/adminsettings/deploymentstatus/deploymentstatusTable.js", "/adminsettings/eventstatuses/eventstatusTable": "pages/adminsettings/eventstatuses/eventstatusTable.js", "/adminsettings/eventstatuses/form": "pages/adminsettings/eventstatuses/form.js", "/adminsettings/deploymentstatus/form": "pages/adminsettings/deploymentstatus/form.js", "/adminsettings/expertise/expertiseTable": "pages/adminsettings/expertise/expertiseTable.js", "/adminsettings/eventstatuses": "pages/adminsettings/eventstatuses.js", "/adminsettings/expertise/form": "pages/adminsettings/expertise/form.js", "/adminsettings/hazard/forms": "pages/adminsettings/hazard/forms.js", "/adminsettings/hazard/hazardReactDropZone": "pages/adminsettings/hazard/hazardReactDropZone.js", "/adminsettings/hazard/hazardTable": "pages/adminsettings/hazard/hazardTable.js", "/adminsettings/hazard/hazardTableFilter": "pages/adminsettings/hazard/hazardTableFilter.js", "/adminsettings/hazardtypes/forms": "pages/adminsettings/hazardtypes/forms.js", "/adminsettings/hazardtypes/hazardTypeTable": "pages/adminsettings/hazardtypes/hazardTypeTable.js", "/adminsettings/hazardtypes": "pages/adminsettings/hazardtypes.js", "/adminsettings/institutionNetworks/form": "pages/adminsettings/institutionNetworks/form.js", "/adminsettings/institutionNetworks": "pages/adminsettings/institutionNetworks.js", "/adminsettings/institutionNetworks/institutionNetworkTable": "pages/adminsettings/institutionNetworks/institutionNetworkTable.js", "/adminsettings/institutiontypes/form": "pages/adminsettings/institutiontypes/form.js", "/adminsettings/institutiontypes": "pages/adminsettings/institutiontypes.js", "/adminsettings/institutiontypes/institutionTypeTable": "pages/adminsettings/institutiontypes/institutionTypeTable.js", "/adminsettings/landingPage": "pages/adminsettings/landingPage.js", "/adminsettings/landingPage/form": "pages/adminsettings/landingPage/form.js", "/adminsettings/landingPage/landingPageTable": "pages/adminsettings/landingPage/landingPageTable.js", "/adminsettings/mailsettings/form": "pages/adminsettings/mailsettings/form.js", "/adminsettings/mailsettings": "pages/adminsettings/mailsettings.js", "/adminsettings/operationstatuses/form": "pages/adminsettings/operationstatuses/form.js", "/adminsettings/operationstatuses/operationstatusTable": "pages/adminsettings/operationstatuses/operationstatusTable.js", "/adminsettings/operationstatuses": "pages/adminsettings/operationstatuses.js", "/adminsettings/projectstatuses/form": "pages/adminsettings/projectstatuses/form.js", "/adminsettings/projectstatuses/projectstatusTable": "pages/adminsettings/projectstatuses/projectstatusTable.js", "/adminsettings/region/form": "pages/adminsettings/region/form.js", "/adminsettings/region/regionTableFilter": "pages/adminsettings/region/regionTableFilter.js", "/adminsettings/risklevel/form": "pages/adminsettings/risklevel/form.js", "/adminsettings/region/regionTable": "pages/adminsettings/region/regionTable.js", "/adminsettings/risklevel/risklevelTable": "pages/adminsettings/risklevel/risklevelTable.js", "/adminsettings/projectstatuses": "pages/adminsettings/projectstatuses.js", "/adminsettings/roles/form": "pages/adminsettings/roles/form.js", "/adminsettings/roles/roleTable": "pages/adminsettings/roles/roleTable.js", "/adminsettings/roles": "pages/adminsettings/roles.js", "/adminsettings/syndrome/form": "pages/adminsettings/syndrome/form.js", "/adminsettings/syndrome/syndromeTable": "pages/adminsettings/syndrome/syndromeTable.js", "/adminsettings/updateType/forms": "pages/adminsettings/updateType/forms.js", "/adminsettings/updateType": "pages/adminsettings/updateType.js", "/adminsettings/updateType/updateTypeTable": "pages/adminsettings/updateType/updateTypeTable.js", "/adminsettings/user/forms": "pages/adminsettings/user/forms.js", "/adminsettings/user": "pages/adminsettings/user.js", "/adminsettings/user/userTable": "pages/adminsettings/user/userTable.js", "/adminsettings/user/userTableFilter": "pages/adminsettings/user/userTableFilter.js", "/adminsettings/worldregion/form": "pages/adminsettings/worldregion/form.js", "/adminsettings/worldregion/worldregionTable": "pages/adminsettings/worldregion/worldregionTable.js", "/country/CountriesMap": "pages/country/CountriesMap.js", "/country/CountriesGlossary": "pages/country/CountriesGlossary.js", "/country/CountriesNamesListing": "pages/country/CountriesNamesListing.js", "/country/OrganizationTable": "pages/country/OrganizationTable.js", "/country/CountryShow": "pages/country/CountryShow.js", "/country/components/CountryAccordionSection": "pages/country/components/CountryAccordionSection.js", "/country/components/CountryButtonSection": "pages/country/components/CountryButtonSection.js", "/country/components/CountryCoverSection": "pages/country/components/CountryCoverSection.js", "/country/components/CountryDocumentAccordion": "pages/country/components/CountryDocumentAccordion.js", "/country/components/CountryMediaGalleryAccordion": "pages/country/components/CountryMediaGalleryAccordion.js", "/country/components/CountryOrganisationAccordion": "pages/country/components/CountryOrganisationAccordion.js", "/country/components/DiscussionAccordion": "pages/country/components/DiscussionAccordion.js", "/dashboard/AboutUs": "pages/dashboard/AboutUs.js", "/country/pagination": "pages/country/pagination.js", "/dashboard/ActiveProjectOperations": "pages/dashboard/ActiveProjectOperations.js", "/dashboard/Announcement": "pages/dashboard/Announcement.js", "/dashboard/AnnouncementItem": "pages/dashboard/AnnouncementItem.js", "/dashboard/CalendarEvents": "pages/dashboard/CalendarEvents.js", "/dashboard/ListContainer": "pages/dashboard/ListContainer.js", "/dashboard/OngoingOperations": "pages/dashboard/OngoingOperations.js", "/dashboard/OngoingProjects": "pages/dashboard/OngoingProjects.js", "/declarationform/declarationform": "pages/declarationform/declarationform.js", "/event/EventShow": "pages/event/EventShow.js", "/declarationform/invalidLink": "pages/declarationform/invalidLink.js", "/event/EventsTable": "pages/event/EventsTable.js", "/event/EventsTableFilter": "pages/event/EventsTableFilter.js", "/event/Form": "pages/event/Form.js", "/event/components/DiscussionAccordion": "pages/event/components/DiscussionAccordion.js", "/event/ListMapcontainer": "pages/event/ListMapcontainer.js", "/event/components/EventAccordionSection": "pages/event/components/EventAccordionSection.js", "/event/components/EventCoverSection": "pages/event/components/EventCoverSection.js", "/event/components/EventHeaderSection": "pages/event/components/EventHeaderSection.js", "/event/components/MediaGalleryAccordion": "pages/event/components/MediaGalleryAccordion.js", "/event/components/MoreInformationAccordion": "pages/event/components/MoreInformationAccordion.js", "/event/components/RiskAssessmentAccordion": "pages/event/components/RiskAssessmentAccordion.js", "/hazard/DocumentAccordian": "pages/hazard/DocumentAccordian.js", "/forgot-password": "pages/forgot-password.js", "/hazard/HazardAccordianSection": "pages/hazard/HazardAccordianSection.js", "/hazard/HazardOperation": "pages/hazard/HazardOperation.js", "/hazard/HazardCoverSection": "pages/hazard/HazardCoverSection.js", "/hazard/HazardOrganisation": "pages/hazard/HazardOrganisation.js", "/hazard/HazardSearch": "pages/hazard/HazardSearch.js", "/hazard/HazardCurrentEvent": "pages/hazard/HazardCurrentEvent.js", "/hazard/HazardShow": "pages/hazard/HazardShow.js", "/hazard/VirtualSpaceAccordian": "pages/hazard/VirtualSpaceAccordian.js", "/hazard/HazardPastEvent": "pages/hazard/HazardPastEvent.js", "/hazard/MediaGalleryAccordion": "pages/hazard/MediaGalleryAccordion.js", "/hazard/pagination": "pages/hazard/pagination.js", "/institution/InfoPopup": "pages/institution/InfoPopup.js", "/institution/InstitutionImageEditor": "pages/institution/InstitutionImageEditor.js", "/institution/Form": "pages/institution/Form.js", "/institution/InstitutionImageHandler": "pages/institution/InstitutionImageHandler.js", "/institution/InstitutionFocalPoint": "pages/institution/InstitutionFocalPoint.js", "/institution/InstitutionShow": "pages/institution/InstitutionShow.js", "/institution/InstitutionMapQuickInfo": "pages/institution/InstitutionMapQuickInfo.js", "/institution/InstitutionsTable": "pages/institution/InstitutionsTable.js", "/institution/ListMapContainer": "pages/institution/ListMapContainer.js", "/institution/ReadMoreModal": "pages/institution/ReadMoreModal.js", "/institution/InstitutionsFilter": "pages/institution/InstitutionsFilter.js", "/institution/components/DiscussionAccordion": "pages/institution/components/DiscussionAccordion.js", "/institution/components/InstitutionAccordionSection": "pages/institution/components/InstitutionAccordionSection.js", "/institution/components/InstitutionCoverSection": "pages/institution/components/InstitutionCoverSection.js", "/institution/components/InstitutionInfoSection": "pages/institution/components/InstitutionInfoSection.js", "/institution/components/InstitutionCoverSectionContent": "pages/institution/components/InstitutionCoverSectionContent.js", "/institution/components/MoreInfoAccordion": "pages/institution/components/MoreInfoAccordion.js", "/institution/components/MediaGalleryAccordion": "pages/institution/components/MediaGalleryAccordion.js", "/operation/Form": "pages/operation/Form.js", "/operation/OperationPartners": "pages/operation/OperationPartners.js", "/operation/OperationShow": "pages/operation/OperationShow.js", "/operation/OperationsTableFilter": "pages/operation/OperationsTableFilter.js", "/operation/OperationsTable": "pages/operation/OperationsTable.js", "/operation/ListMapContainer": "pages/operation/ListMapContainer.js", "/operation/components/DocumentsAccordian": "pages/operation/components/DocumentsAccordian.js", "/operation/components/OperationAccordianSection": "pages/operation/components/OperationAccordianSection.js", "/operation/components/MediaGalleryAccordian": "pages/operation/components/MediaGalleryAccordian.js", "/operation/components/OperationCoverSection": "pages/operation/components/OperationCoverSection.js", "/operation/components/OperationDetailsAccordian": "pages/operation/components/OperationDetailsAccordian.js", "/operation/components/OperationInfo": "pages/operation/components/OperationInfo.js", "/operation/components/OperationStats": "pages/operation/components/OperationStats.js", "/operation/components/OperationTimelineSection": "pages/operation/components/OperationTimelineSection.js", "/operation/components/PartnersAccordian": "pages/operation/components/PartnersAccordian.js", "/operation/components/VirtualSpaceAccordian": "pages/operation/components/VirtualSpaceAccordian.js", "/people/peopleTable": "pages/people/peopleTable.js", "/people/peopleTableFilter": "pages/people/peopleTableFilter.js", "/profile/bookmarkTable": "pages/profile/bookmarkTable.js", "/profile/confirmation": "pages/profile/confirmation.js", "/profile/bookmarkTableFilter": "pages/profile/bookmarkTableFilter.js", "/project/ListMapContainer": "pages/project/ListMapContainer.js", "/project/ProjectsTableFilter": "pages/project/ProjectsTableFilter.js", "/profile/profileEdit": "pages/profile/profileEdit.js", "/profile/myConsent": "pages/profile/myConsent.js", "/project/ProjectShow": "pages/project/ProjectShow.js", "/project/components/ProjectAccordianSection": "pages/project/components/ProjectAccordianSection.js", "/project/ProjectsTable": "pages/project/ProjectsTable.js", "/project/components/DiscussionAccordion": "pages/project/components/DiscussionAccordion.js", "/project/Form": "pages/project/Form.js", "/project/components/ProjectCoverSection": "pages/project/components/ProjectCoverSection.js", "/project/components/ProjectDetailsAccordion": "pages/project/components/ProjectDetailsAccordion.js", "/project/components/ProjectInfoSection": "pages/project/components/ProjectInfoSection.js", "/project/components/VirtualSpaceAccordion": "pages/project/components/VirtualSpaceAccordion.js", "/r403": "pages/r403.js", "/rNoAccess": "pages/rNoAccess.js", "/search": "pages/search.js", "/reset-password/[passwordToken]": "pages/reset-password/[passwordToken].js", "/updates/CalendarEventForm": "pages/updates/CalendarEventForm.js", "/updates/ContactForm": "pages/updates/ContactForm.js", "/updates/DocumentForm": "pages/updates/DocumentForm.js", "/updates/ConversationForm": "pages/updates/ConversationForm.js", "/updates/LinkForm": "pages/updates/LinkForm.js", "/updates/ImageForm": "pages/updates/ImageForm.js", "/users/UsersTable": "pages/users/UsersTable.js", "/users/View": "pages/users/View.js", "/vspace/AcceptRequestVspace": "pages/vspace/AcceptRequestVspace.js", "/users/Form": "pages/users/Form.js", "/vspace/MediaGalleryAccordian": "pages/vspace/MediaGalleryAccordian.js", "/vspace/DocumentAccordian": "pages/vspace/DocumentAccordian.js", "/vspace/AnnouncementsAccordian": "pages/vspace/AnnouncementsAccordian.js", "/vspace/Form": "pages/vspace/Form.js", "/vspace/View": "pages/vspace/View.js", "/vspace/VirtualspaceCalendarEvents": "pages/vspace/VirtualspaceCalendarEvents.js", "/vspace/ManageMembers": "pages/vspace/ManageMembers.js", "/vspace/VirtualspaceMonitoringMembers": "pages/vspace/VirtualspaceMonitoringMembers.js", "/vspace/VirtualSpaceAccordionSection": "pages/vspace/VirtualSpaceAccordionSection.js", "/vspace/VirtualspaceSubscribeRequestUsers": "pages/vspace/VirtualspaceSubscribeRequestUsers.js", "/vspace/vspace_announcement/Announcement": "pages/vspace/vspace_announcement/Announcement.js", "/vspace/vspace_announcement/AnnouncementItem": "pages/vspace/vspace_announcement/AnnouncementItem.js", "/adminsettings/approval/focal_point_appoval": "pages/adminsettings/approval/focal_point_appoval.js", "/adminsettings/approval/institution_approval": "pages/adminsettings/approval/institution_approval.js", "/adminsettings/[...routes]": "pages/adminsettings/[...routes].js", "/adminsettings/country": "pages/adminsettings/country.js", "/adminsettings/deploymentstatus": "pages/adminsettings/deploymentstatus.js", "/adminsettings/expertise": "pages/adminsettings/expertise.js", "/adminsettings/content": "pages/adminsettings/content.js", "/adminsettings/hazard": "pages/adminsettings/hazard.js", "/_document": "pages/_document.js", "/adminsettings/region": "pages/adminsettings/region.js", "/adminsettings/permissions": "pages/adminsettings/permissions.js", "/adminsettings/risklevel": "pages/adminsettings/risklevel.js", "/adminsettings": "pages/adminsettings.js", "/adminsettings/syndrome": "pages/adminsettings/syndrome.js", "/adminsettings/worldregion": "pages/adminsettings/worldregion.js", "/country/[...routes]": "pages/country/[...routes].js", "/country/permission": "pages/country/permission.js", "/country": "pages/country.js", "/dashboard/Dashboard": "pages/dashboard/Dashboard.js", "/data-privacy-policy": "pages/data-privacy-policy.js", "/declarationform/[...routes]": "pages/declarationform/[...routes].js", "/event/[...routes]": "pages/event/[...routes].js", "/events-calendar/[...routes]": "pages/events-calendar/[...routes].js", "/event/permission": "pages/event/permission.js", "/event": "pages/event.js", "/events-calendar": "pages/events-calendar.js", "/hazard/[...routes]": "pages/hazard/[...routes].js", "/hazard/permission": "pages/hazard/permission.js", "/": "pages/index.js", "/home": "pages/home.js", "/institution/[...routes]": "pages/institution/[...routes].js", "/hazard": "pages/hazard.js", "/institution": "pages/institution.js", "/institution/permission": "pages/institution/permission.js", "/operation/[...routes]": "pages/operation/[...routes].js", "/login": "pages/login.js", "/operation/permission": "pages/operation/permission.js", "/people": "pages/people.js", "/operation": "pages/operation.js", "/project/[...routes]": "pages/project/[...routes].js", "/project": "pages/project.js", "/project/permission": "pages/project/permission.js", "/profile": "pages/profile.js", "/routePermissions": "pages/routePermissions.js", "/updates/[...routes]": "pages/updates/[...routes].js", "/users/[...routes]": "pages/users/[...routes].js", "/users": "pages/users.js", "/vspace/[...routes]": "pages/vspace/[...routes].js", "/vspace/permission": "pages/vspace/permission.js", "/vspace": "pages/vspace.js", "/updates": "pages/updates.js"}