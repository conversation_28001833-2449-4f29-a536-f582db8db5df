{"version": 3, "file": "static/chunks/pages/institution/components/InstitutionInfoSection-12c4abadaa62ec51.js", "mappings": "gFACA,4CACA,iDACA,WACA,OAAe,EAAQ,KAAsE,CAC7F,EACA,SAFsB,iJCyGtB,MArF+B,IAC3B,GAAM,CAAEA,CAAC,CAAE,CAAGC,CAAAA,EAAAA,EAAAA,EAAAA,CAAcA,CAAC,QAoFlBC,EAjFL,CAACC,EAAOC,EAAS,CAAGC,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,EAAU,GACtC,CAACC,EAAWC,EAAa,CAAGF,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAQ,EAAE,EAC9C,CAACG,EAAWC,EAAa,CAAGJ,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAS,IAG7CK,EAAqB,IAGvB,OAFAN,EAAS,CAACD,GACVM,EAAaT,EAAEW,IACPA,GACJ,IAAK,WAKDJ,EAHAK,EAAMC,SAGOC,MAHQ,EAAIF,EAAMC,eAAe,CAACC,QAAQ,CAC7CF,EAAMC,eAAe,CAACC,QAAQ,CAC9B,EAAE,EAEZ,KAEJ,KAAK,aAKDP,EAHIK,EAAMG,SAGGC,QAHc,EAAIJ,EAAMG,iBAAiB,CAACC,aAAa,CAC1DJ,EAAMG,iBAAiB,CAACC,aAAa,CACrC,EAAE,EAEZ,KAEJ,KAAK,WAKDT,EAHAK,EAAMG,SAGOE,QAHU,EAAIL,EAAMG,iBAAiB,CAACE,WAAW,CACpDL,EAAMG,iBAAiB,CAACE,WAAW,CACnC,EAAE,CAGpB,CACJ,EACMC,EAAgBC,IAClBf,EAASe,EACb,EAGA,MACI,+BACI,WAACC,MAAAA,CAAIC,UAAU,0CACX,WAACC,EAAAA,CAAGA,CAAAA,WACCC,SAwCZA,CACkC,CACvCvB,CAAwB,CACxBe,CAMC,EAED,MACI,UAACS,EAAAA,CAAGA,CAAAA,UACA,WAACJ,MAAAA,CACGC,UAAU,mCACVI,QAAS,IAAMf,EAAmB,sBAElC,UAACU,MAAAA,CAAIC,UAAU,yBACX,UAACK,EAAAA,CAAeA,CAAAA,CAACC,KAAMC,EAAAA,GAAOA,CAAEC,MAAM,OAAOC,KAAK,SAEtD,WAACV,MAAAA,CAAIC,UAAU,0BACX,UAACU,KAAAA,UAAI/B,EAAE,cACP,UAACgC,KAAAA,UACIjB,GAAqBA,EAAkBD,QAAQ,CAC1CC,EAAkBD,QAAQ,CAC1B,WAM9B,EAvEkCJ,EAAoBV,EAAGY,EAAMG,iBAAiB,EAC5D,UAACS,EAAAA,CAAGA,CAAAA,UACA,WAACJ,MAAAA,CACGC,UAAU,mCACVI,QAAS,IAAMf,EAAmB,wBAElC,UAACU,MAAAA,CAAIC,UAAU,yBACX,UAACK,EAAAA,CAAeA,CAAAA,CACZC,KAAMM,EAAAA,GAAYA,CAClBJ,MAAM,OACNC,KAAK,SAGb,WAACV,MAAAA,CAAIC,UAAU,0BACX,UAACU,KAAAA,UAAI/B,EAAE,gBACP,UAACgC,KAAAA,UACIpB,EAAMG,iBAAiB,EAAIH,EAAMG,iBAAiB,CAACmB,UAAU,CACxDtB,EAAMG,iBAAiB,CAACmB,UAAU,CAClC,YAKtB,UAACV,EAAAA,CAAGA,CAAAA,UACCW,SAiDhBA,CACkC,CACvCnC,CAAwB,CACxBe,CAMC,EAED,MACI,WAACK,MAAAA,CACGC,UAAU,mCACVI,QAAS,IAAMf,EAAmB,sBAElC,UAACU,MAAAA,CAAIC,UAAU,yBACV,UAACK,EAAAA,CAAeA,CAAAA,CAACC,KAAMS,EAAAA,GAAYA,CAAEP,MAAM,OAAOC,KAAK,SAE5D,WAACV,MAAAA,CAAIC,UAAU,0BACX,UAACU,KAAAA,UAAI/B,EAAE,cACP,UAACgC,KAAAA,UACIjB,GAAqBA,EAAkBsB,QAAQ,CAC1CtB,EAAkBsB,QAAQ,CAC1B,SAK1B,EA9EsC3B,EAAoBV,EAAGY,EAAMG,iBAAiB,OAGpE,UAACuB,EAAAA,OAASA,CAAAA,CACNC,OAAQpC,EACRqC,QAAS,GAAkBtB,EAAaC,GACxCsB,KAAMnC,EACNK,KAAMH,QAK1B,oICzDA,MA1CkB,IAChB,GAAM,CAAER,CAAC,CAAE,CAAGC,CAAAA,EAAAA,EAAAA,EAAAA,CAAcA,CAAC,QAyChBqC,EAxCP,OAwCgBA,CAxCdC,CAAM,SAAEC,CAAO,MAAEC,CAAI,MAAE9B,CAAI,CAAE,CAAGC,EAClC8B,EAAiB,aAAT/B,EAAsB,UAmClB,EAnC8BgC,WAmCvChC,EAAsB,cAAgB,YAlCzCiC,EACJH,EAAKI,MAAM,CAAG,EACZJ,EAAKK,GAAG,CAAC,CAACC,EAAWC,IACnB,WAACC,OAAAA,WACC,UAACC,IAAIA,CAACC,KAAM,IAAkBJ,MAAAA,CAAdL,EAAM,UAAiB,OAATK,EAAKK,GAAG,IAAjCF,OACFH,EAAKM,KAAK,GAEb,UAACC,KAAAA,CAAAA,KAJQN,IAQb,WAACO,IAAAA,WACEvD,EAAE,MAAM,IAAEW,EAAK,IAAEX,EAAE,SAAS,OAInC,MACE,WAACwD,EAAAA,CAAKA,CAAAA,CACJC,QAAQ,IACR3B,KAAK,KACL4B,KAAMnB,EACNoB,OAAQ,IAAMnB,EAAQ,CAACD,GACvBqB,kBAAgB,wBAEhB,UAACJ,EAAAA,CAAKA,CAACK,MAAM,EAACC,WAAW,aACvB,UAACN,EAAAA,CAAKA,CAACO,KAAK,WAAEpD,MAEhB,UAAC6C,EAAAA,CAAKA,CAACQ,IAAI,WACT,UAAC5C,MAAAA,UAAKwB,QAQd", "sources": ["webpack://_N_E/?0909", "webpack://_N_E/./pages/institution/components/InstitutionInfoSection.tsx", "webpack://_N_E/./pages/institution/InfoPopup.tsx"], "sourcesContent": ["\n    (window.__NEXT_P = window.__NEXT_P || []).push([\n      \"/institution/components/InstitutionInfoSection\",\n      function () {\n        return require(\"private-next-pages/institution/components/InstitutionInfoSection.tsx\");\n      }\n    ]);\n    if(module.hot) {\n      module.hot.dispose(function () {\n        window.__NEXT_P.push([\"/institution/components/InstitutionInfoSection\"])\n      });\n    }\n  ", "//Import Library\r\nimport React, { useState } from \"react\";\r\nimport { faFolder<PERSON><PERSON>, faLayerGroup, faUsers } from \"@fortawesome/free-solid-svg-icons\";\r\nimport { FontAwesomeIcon } from \"@fortawesome/react-fontawesome\";\r\nimport { Col, Row } from \"react-bootstrap\";\r\n\r\n//Import services/components\r\nimport { useTranslation } from 'next-i18next';\r\nimport InfoPopup from \"../InfoPopup\";\r\n\r\ninterface InstitutionInfoSectionProps {\r\n  institutionData: {\r\n    description: string;\r\n    partners?: any[];\r\n  };\r\n  institutionStatus: {\r\n    partners: number;\r\n    operations: number;\r\n    projects: number;\r\n    operationData: any[];\r\n    projectData: any[];\r\n  };\r\n}\r\n\r\nconst InstitutionInfoSection = (props: InstitutionInfoSectionProps) => {\r\n    const { t } = useTranslation('common');\r\n\r\n    /***For Qucik Info Modal***/\r\n    const [popup, setPopup] = useState<boolean>(false);\r\n    const [quickInfo, setQuickInfo] = useState<any[]>([]);\r\n    const [fieldName, setFieldName] = useState<string>(\"\");\r\n\r\n    /***Handle Partner List***/\r\n    const partnerListHandler = (name: string) => {\r\n        setPopup(!popup);\r\n        setFieldName(t(name));\r\n        switch (name) {\r\n            case \"Partners\":\r\n                const partners =\r\n                props.institutionData && props.institutionData.partners\r\n                        ? props.institutionData.partners\r\n                        : [];\r\n                setQuickInfo(partners);\r\n                break;\r\n\r\n            case \"Operations\":\r\n                const operationData =\r\n                    props.institutionStatus && props.institutionStatus.operationData\r\n                        ? props.institutionStatus.operationData\r\n                        : [];\r\n                setQuickInfo(operationData);\r\n                break;\r\n\r\n            case \"Projects\":\r\n                const projectData =\r\n                props.institutionStatus && props.institutionStatus.projectData\r\n                        ? props.institutionStatus.projectData\r\n                        : [];\r\n                setQuickInfo(projectData);\r\n                break;\r\n        }\r\n    };\r\n    const closeHandler = (val: boolean) => {\r\n        setPopup(val);\r\n    };\r\n\r\n\r\n    return (\r\n        <>\r\n            <div className=\"institution-infographic-block\">\r\n                <Row>\r\n                    {partner_func(partnerListHandler, t, props.institutionStatus)}\r\n                    <Col>\r\n                        <div\r\n                            className=\"list-group-item d-flex clickable\"\r\n                            onClick={() => partnerListHandler(\"Operations\")}\r\n                        >\r\n                            <div className=\"quickinfo-img\">\r\n                                <FontAwesomeIcon\r\n                                    icon={faLayerGroup}\r\n                                    color=\"#fff\"\r\n                                    size=\"2x\"\r\n                                />\r\n                            </div>\r\n                            <div className=\"quickinfoDesc\">\r\n                                <h5>{t(\"Operations\")}</h5>\r\n                                <h4>\r\n                                    {props.institutionStatus && props.institutionStatus.operations\r\n                                        ? props.institutionStatus.operations\r\n                                        : 0}\r\n                                </h4>\r\n                            </div>\r\n                        </div>\r\n                    </Col>\r\n                    <Col>\r\n                        {project_func(partnerListHandler, t, props.institutionStatus)}\r\n                    </Col>\r\n                </Row>\r\n                <InfoPopup\r\n                    isShow={popup}\r\n                    isClose={(val: boolean) => closeHandler(val)}\r\n                    data={quickInfo}\r\n                    name={fieldName}\r\n                />\r\n            </div>\r\n        </>\r\n    )\r\n}\r\n\r\nexport default InstitutionInfoSection;\r\n\r\nfunction partner_func(\r\n    partnerListHandler: (name: any) => void,\r\n    t: (p: string) => string ,\r\n    institutionStatus: {\r\n        partners: number;\r\n        operations: number;\r\n        projects: number;\r\n        operationData: any[];\r\n        projectData: any[];\r\n    }\r\n) {\r\n    return (\r\n        <Col>\r\n            <div\r\n                className=\"list-group-item d-flex clickable\"\r\n                onClick={() => partnerListHandler(\"Partners\")}\r\n            >\r\n                <div className=\"quickinfo-img\">\r\n                    <FontAwesomeIcon icon={faUsers} color=\"#fff\" size=\"2x\" />\r\n                </div>\r\n                <div className=\"quickinfoDesc\">\r\n                    <h5>{t(\"Partners\")}</h5>\r\n                    <h4>\r\n                        {institutionStatus && institutionStatus.partners\r\n                            ? institutionStatus.partners\r\n                            : 0}\r\n                    </h4>\r\n                </div>\r\n            </div>\r\n        </Col>\r\n    );\r\n}\r\n\r\nfunction project_func(\r\n    partnerListHandler: (name: any) => void,\r\n    t: (p: string) => string,\r\n    institutionStatus: {\r\n        partners: number;\r\n        operations: number;\r\n        projects: number;\r\n        operationData: any[];\r\n        projectData: any[];\r\n    }\r\n) {\r\n    return (\r\n        <div\r\n            className=\"list-group-item d-flex clickable\"\r\n            onClick={() => partnerListHandler(\"Projects\")}\r\n        >\r\n            <div className=\"quickinfo-img\">\r\n                {<FontAwesomeIcon icon={faFolderOpen} color=\"#fff\" size=\"2x\" />}\r\n            </div>\r\n            <div className=\"quickinfoDesc\">\r\n                <h5>{t(\"Projects\")}</h5>\r\n                <h4>\r\n                    {institutionStatus && institutionStatus.projects\r\n                        ? institutionStatus.projects\r\n                        : 0}\r\n                </h4>\r\n            </div>\r\n        </div>\r\n    );\r\n}\r\n\r\n", "//Import Library\r\nimport React from \"react\";\r\nimport { Modal } from \"react-bootstrap\";\r\nimport Link from \"next/link\";\r\n\r\n//Import services/components\r\nimport { useTranslation } from 'next-i18next';\r\n\r\nconst InfoPopup = (props: any) => {\r\n  const { t } = useTranslation('common');\r\n  const { isShow, isClose, data, name } = props;\r\n  const route = name === \"Projects\" ? \"project\" : arrowfun();\r\n  const titles =\r\n    data.length > 0 ? (\r\n      data.map((item: any, i: any) => (\r\n        <span key={i}>\r\n          <Link href={`/${route}/show/${item._id}`}>\r\n            {item.title}\r\n          </Link>\r\n          <hr />\r\n        </span>\r\n      ))\r\n    ) : (\r\n      <p>\r\n        {t(\"No\")} {name} {t(\"Found\")}.\r\n      </p>\r\n    );\r\n\r\n  return (\r\n    <Modal\r\n      centered\r\n      size=\"sm\"\r\n      show={isShow}\r\n      onHide={() => isClose(!isShow)}\r\n      aria-labelledby=\"modal_popup\"\r\n    >\r\n      <Modal.Header closeButton>\r\n        <Modal.Title>{name}</Modal.Title>\r\n      </Modal.Header>\r\n      <Modal.Body>\r\n        <div>{titles}</div>\r\n      </Modal.Body>\r\n    </Modal>\r\n  );\r\n\r\n  function arrowfun() {\r\n    return name === \"Partners\" ? \"institution\" : \"operation\";\r\n  }\r\n};\r\n\r\nexport default InfoPopup;\r\n"], "names": ["t", "useTranslation", "InstitutionInfoSection", "popup", "setPopup", "useState", "quickInfo", "setQuickInfo", "fieldName", "setFieldName", "partner<PERSON><PERSON><PERSON><PERSON><PERSON>", "name", "props", "institutionData", "partners", "institutionStatus", "operationData", "projectData", "<PERSON><PERSON><PERSON><PERSON>", "val", "div", "className", "Row", "partner_func", "Col", "onClick", "FontAwesomeIcon", "icon", "faUsers", "color", "size", "h5", "h4", "faLayerGroup", "operations", "project_func", "faFolderOpen", "projects", "InfoPopup", "isShow", "isClose", "data", "route", "arrowfun", "titles", "length", "map", "item", "i", "span", "Link", "href", "_id", "title", "hr", "p", "Modal", "centered", "show", "onHide", "aria-<PERSON>by", "Header", "closeButton", "Title", "Body"], "sourceRoot": "", "ignoreList": []}