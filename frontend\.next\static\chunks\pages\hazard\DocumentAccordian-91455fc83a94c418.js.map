{"version": 3, "file": "static/chunks/pages/hazard/DocumentAccordian-91455fc83a94c418.js", "mappings": "gFACA,4CACA,4BACA,WACA,OAAe,EAAQ,KAAiD,CACxE,EACA,SAFsB,oGCiCtB,SAASA,EAASC,CAAoB,EACpC,GAAM,GAAEC,CAAC,CAAE,CAAGC,CAAAA,EAAAA,EAAAA,EAAAA,CAAcA,CAAC,UACvBC,EAA6B,CACjCC,gBAAiBH,EAAE,cACnB,EACI,SACJI,CAAO,CACPC,MAAI,WACJC,CAAS,uBACTC,CAAqB,WACrBC,CAAS,oBACTC,CAAkB,qBAClBC,CAAmB,kBACnBC,CAAgB,aAChBC,CAAW,oBACXC,CAAkB,gBAClBC,CAAc,SACdC,CAAO,WACPC,CAAS,sBACTC,CAAoB,mBACpBC,CAAiB,YACjBC,CAAU,QACVC,CAAM,kBACNC,CAAgB,CAChBC,cAAY,CAEZ,CADA,EACGC,EACJ,CAAGxB,EAGEyB,EAAiB,4BACrBtB,EACAuB,gBAAiBzB,EAAE,IAP0C,MAQ7D0B,UAAU,UACVtB,EACAC,KAAMA,GAAQ,EAAE,CAChBsB,OAAO,EACPC,2BAA4BrB,EAC5BsB,UAAWrB,EACXsB,gBAAiBf,qBACjBN,EACAsB,YAAY,EACZC,iBAAkBhB,EAClBiB,kBAAmBpB,GAA0C,GAC7DqB,eADwCrB,cACVD,GAA4B,CAAC,GAAI,GAAI,GAAI,CAA3BA,EAA+B,GAAG,CAC9EuB,oBAAqB7B,EACrB8B,oBAAqB1B,EACrB2B,aAAc1B,iBACdG,uBACAG,EACAC,oBACAoB,kBAAmB,UAACC,EAAAA,CAAYA,CAAAA,CAAAA,GAChCC,SAAU,UAACC,IAAAA,CAAEC,UAAU,6CACvBvB,SACAC,eACAE,mBACAD,EACAqB,UAAW,WACb,EACA,MACE,UAACC,EAAAA,EAASA,CAAAA,CAAE,GAAGnB,CAAc,EAEjC,CAEA1B,EAAS8C,YAAY,CAAG,CACtBf,WAAW,EACXE,YAAY,EACZzB,UAAW,KACXU,WAAW,EACXC,qBAAsB,KACtBC,kBAAmB,GACnBC,YAAY,EACZE,kBAAkB,CACpB,EAEA,MAAevB,QAAQA,EAAC,qIClExB,MApC0B,IACtB,GAAM,GAAEE,CAAC,CAAE,CAAGC,CAAAA,EAAAA,EAAAA,EAAAA,CAAcA,CAAC,MAmClB4C,IAlCL,CAACC,EAASC,EAAW,CAAGC,CAAAA,EAAAA,EAAAA,EAkCFH,EAAC,IAlCCG,CAAQA,EAAC,GACvC,MACI,+BACI,WAACC,EAAAA,CAASA,CAACC,IAAI,EAACC,SAAS,cACvB,WAACF,EAAAA,CAASA,CAACG,MAAM,EAACC,QAAS,IAAMN,EAAW,CAACD,aAC3C,UAACQ,MAAAA,CAAIZ,UAAU,qBAAa1C,EAAE,eAC9B,UAACsD,MAAAA,CAAIZ,UAAU,qBACZI,EACC,UAACS,EAAAA,CAAeA,CAAAA,CAACC,KAAMC,EAAAA,GAAOA,CAAEC,MAAM,SAEtC,UAACH,EAAAA,CAAeA,CAAAA,CAACC,KAAMG,EAAAA,GAAMA,CAAED,MAAM,cAI3C,WAACT,EAAAA,CAASA,CAACW,IAAI,YACb,UAACC,EAAAA,CAAaA,CAAAA,CACZ9C,QAAShB,EAAM+D,uBAAuB,CAAC/C,OAAO,CAC9CgD,UAAWhE,EAAM+D,uBAAuB,CAACE,aAAa,CACtDC,KAAMlE,EAAM+D,uBAAuB,CAACI,QAAQ,EAAI,EAAE,CAClDC,gBAAiBpE,EAAM+D,uBAAuB,CAACM,MAAM,GAEvD,UAACC,KAAAA,CAAG3B,UAAU,gBAAQ1C,EAAE,0BACxB,UAAC6D,EAAAA,CAAaA,CAAAA,CACZ9C,QAAShB,EAAM+D,uBAAuB,CAAC/C,OAAO,CAC9CgD,UAAWhE,EAAM+D,uBAAuB,CAACQ,mBAAmB,CAC5DL,KAAMlE,EAAM+D,uBAAuB,CAACS,cAAc,EAAI,EAAE,CACxDJ,gBAAiBpE,EAAM+D,uBAAuB,CAACM,MAAM,UAMvE,uHCyBA,MAtDoD,OAAC,MAAEH,CAAI,CAAEE,aAsD9CN,IAtD6D,SAsDhDA,EAAC,CAtD0D,SAAE9C,CAAO,CAAE,GAE1FyD,EAAa,MAAOC,EAAaC,KAKrCX,EAJiB,CACfY,OAGQC,QAHQH,EAAOI,QAAQ,CAC/BH,cAAeA,CACjB,EAEF,EAEM,GAAE1E,CAAC,CAAE,CAAGC,CAAAA,EAAAA,EAAAA,EAAAA,CAAcA,CAAC,UAGvBG,EAAU,CACd,CACE0E,KAAM9E,EAAE,YACR+E,MAAO,MACPF,SAAU,YACVG,KAAM,GAAYC,GAAKA,EAAEC,SAAS,EAAID,EAAEC,SAAS,EAEnD,CACEJ,KAAM9E,EAAE,YACR+E,MAAO,MACPF,SAAU,iBACVG,KAAM,GAAYC,GAAKA,EAAEE,aAAa,EAAI,UAACC,IAAAA,CAAEC,KAAM,GAA4CJ,MAAAA,CAAzCK,8BAAsB,CAAC,oBAAwB,OAANL,EAAEM,GAAG,EAAIC,OAAO,kBAAUP,EAAEE,aAAa,CAACM,KAAK,CAAC,KAAKC,KAAK,CAAC,EAAG,CAAC,GAAGC,IAAI,CAAC,OACtKC,UAAU,CACZ,EACA,CACEd,KAAM9E,EAAE,eACR6E,SAAU,cACVG,KAAM,GAAYC,GAAKA,EAAEY,WAAW,EAAIZ,EAAEY,WAAW,EAEvD,CACEf,KAAM9E,EAAE,gBACR+E,MAAO,MACPF,SAAU,iBACVG,KAAM,GAAYC,GAAKA,EAAEa,UAAU,EAAIC,IAAOd,EAAEa,UAAU,EAAEE,MAAM,CAAC,cACnEJ,MAD6CG,IACnC,CACZ,EACD,CAED,MACE,UAACjG,EAAAA,CAAQA,CAAAA,CACPM,QAASA,EACTC,KAAM4D,EACNjD,WAAW,EACXI,OAAQoD,EACRnD,gBAAgB,IAChBN,QAASA,GAIf", "sources": ["webpack://_N_E/?4195", "webpack://_N_E/./components/common/RKITable.tsx", "webpack://_N_E/./pages/hazard/DocumentAccordian.tsx", "webpack://_N_E/./components/common/DocumentTable.tsx"], "sourcesContent": ["\n    (window.__NEXT_P = window.__NEXT_P || []).push([\n      \"/hazard/DocumentAccordian\",\n      function () {\n        return require(\"private-next-pages/hazard/DocumentAccordian.tsx\");\n      }\n    ]);\n    if(module.hot) {\n      module.hot.dispose(function () {\n        window.__NEXT_P.push([\"/hazard/DocumentAccordian\"])\n      });\n    }\n  ", "//Import Library\r\nimport React from 'react'\r\nimport DataTable  from 'react-data-table-component';\r\n\r\n//Import services/components\r\nimport { useTranslation } from 'next-i18next';\r\nimport CustomLoader from './CustomLoader';\r\n\r\ninterface RKITableProps {\r\n  columns: any[];\r\n  data: any[];\r\n  totalRows?: number;\r\n  resetPaginationToggle?: boolean;\r\n  subheader?: boolean;\r\n  subHeaderComponent?: React.ReactNode;\r\n  handlePerRowsChange?: (newPerPage: number, page: number) => void;\r\n  handlePageChange?: (page: number) => void;\r\n  rowsPerPage?: number[];\r\n  defaultRowsPerPage?: number;\r\n  selectableRows?: boolean;\r\n  loading?: boolean;\r\n  pagServer?: boolean;\r\n  onSelectedRowsChange?: (selectedRows: any) => void;\r\n  clearSelectedRows?: boolean;\r\n  sortServer?: boolean;\r\n  onSort?: (column: any, sortDirection: string) => void;\r\n  persistTableHead?: boolean;\r\n  sortFunction?: (rows: any[], field: any, direction: 'asc' | 'desc') => any[];\r\n  noHeader?: boolean;\r\n  dense?: boolean;\r\n  pagination?: boolean;\r\n  paginationServer?: boolean;\r\n  paginationTotalRows?: number;\r\n  subHeaderAlign?: string;\r\n  [key: string]: any;\r\n}\r\n\r\nfunction RKITable(props: RKITableProps) {\r\n  const { t } = useTranslation('common');\r\n  const paginationComponentOptions = {\r\n    rowsPerPageText: t('Rowsperpage'),\r\n    };\r\n  const {\r\n    columns,\r\n    data,\r\n    totalRows,\r\n    resetPaginationToggle,\r\n    subheader,\r\n    subHeaderComponent,\r\n    handlePerRowsChange,\r\n    handlePageChange,\r\n    rowsPerPage,\r\n    defaultRowsPerPage,\r\n    selectableRows,\r\n    loading,\r\n    pagServer,\r\n    onSelectedRowsChange,\r\n    clearSelectedRows,\r\n    sortServer,\r\n    onSort,\r\n    persistTableHead,\r\n    sortFunction,\r\n    // Filter out any unknown props that might cause DOM warnings\r\n    ...otherProps\r\n  } = props;\r\n\r\n  // Only pass known DataTable props to avoid DOM warnings\r\n  const dataTableProps = {\r\n    paginationComponentOptions,\r\n    noDataComponent: t(\"NoData\"),\r\n    noHeader: true,\r\n    columns,\r\n    data: data || [],\r\n    dense: true,\r\n    paginationResetDefaultPage: resetPaginationToggle,\r\n    subHeader: subheader,\r\n    progressPending: loading,\r\n    subHeaderComponent,\r\n    pagination: true,\r\n    paginationServer: pagServer,\r\n    paginationPerPage: defaultRowsPerPage ? defaultRowsPerPage : 10,\r\n    paginationRowsPerPageOptions: rowsPerPage ? rowsPerPage : [10, 15, 20, 25, 30],\r\n    paginationTotalRows: totalRows,\r\n    onChangeRowsPerPage: handlePerRowsChange,\r\n    onChangePage: handlePageChange,\r\n    selectableRows,\r\n    onSelectedRowsChange,\r\n    clearSelectedRows,\r\n    progressComponent: <CustomLoader/>,\r\n    sortIcon: <i className=\"sort-icon fas fa-exchange-alt\" />,\r\n    sortServer,\r\n    onSort,\r\n    sortFunction,\r\n    persistTableHead,\r\n    className: \"rki-table\"\r\n  };\r\n  return (\r\n    <DataTable {...dataTableProps} />\r\n  )\r\n};\r\n\r\nRKITable.defaultProps = {\r\n  subHeader: false,\r\n  pagination: true,\r\n  totalRows: null,\r\n  pagServer: true,\r\n  onSelectedRowsChange: null,\r\n  clearSelectedRows: false,\r\n  sortServer: false,\r\n  persistTableHead: false\r\n}\r\n\r\nexport default RKITable;\r\n\r\n", "//Import Library\r\nimport { faMinus, faPlus } from \"@fortawesome/free-solid-svg-icons\";\r\nimport { FontAwesomeIcon } from \"@fortawesome/react-fontawesome\";\r\nimport { Card, Accordion } from \"react-bootstrap\";\r\nimport { useState } from \"react\";\r\n\r\n//Import services/components\r\nimport DocumentTable from \"../../components/common/DocumentTable\";\r\nimport { useTranslation } from 'next-i18next';\r\n\r\nconst DocumentAccordian = (props: any) => {\r\n    const { t } = useTranslation('common');\r\n    const [section, setSection] = useState(false);\r\n    return (\r\n        <>\r\n            <Accordion.Item eventKey=\"0\">\r\n              <Accordion.Header onClick={() => setSection(!section)}>\r\n                <div className=\"cardTitle\">{t(\"documents\")}</div>\r\n                <div className=\"cardArrow\">\r\n                  {section ? (\r\n                    <FontAwesomeIcon icon={faMinus} color=\"#fff\" />\r\n                  ) : (\r\n                    <FontAwesomeIcon icon={faPlus} color=\"#fff\" />\r\n                  )}\r\n                </div>\r\n              </Accordion.Header>\r\n              <Accordion.Body>\r\n                <DocumentTable\r\n                  loading={props.documentAccoirdianProps.loading}\r\n                  sortProps={props.documentAccoirdianProps.hazardDocSort}\r\n                  docs={props.documentAccoirdianProps.Document || []}\r\n                  docsDescription={props.documentAccoirdianProps.docSrc}\r\n                />\r\n                <h6 className=\"mt-3\">{t(\"DocumentsfromUpdates\")}</h6>\r\n                <DocumentTable\r\n                  loading={props.documentAccoirdianProps.loading}\r\n                  sortProps={props.documentAccoirdianProps.hazardDocUpdateSort}\r\n                  docs={props.documentAccoirdianProps.updateDocument || []}\r\n                  docsDescription={props.documentAccoirdianProps.docSrc}\r\n                />\r\n              </Accordion.Body>\r\n            </Accordion.Item>\r\n        </>\r\n    )\r\n};\r\n\r\nexport default DocumentAccordian;", "//Import Library\r\nimport React from \"react\";\r\nimport moment from \"moment\";\r\n\r\n//Import services/components\r\nimport RKITable from '../../components/common/RKITable';\r\nimport { useTranslation } from 'next-i18next';\r\n\r\ninterface DocumentTableProps {\r\n  docs: any[];\r\n  docsDescription: string;\r\n  sortProps: (sortObj: { columnSelector: string; sortDirection: string }) => void;\r\n  loading: boolean;\r\n}\r\n\r\nconst DocumentTable: React.FC<DocumentTableProps> = ({ docs, docsDescription, sortProps, loading }) => {\r\n\r\n  const handleSort = async (column: any, sortDirection: string) => {\r\n    const objSlect = {\r\n      columnSelector: column.selector,\r\n      sortDirection: sortDirection\r\n    }\r\n    sortProps(objSlect);\r\n  };\r\n\r\n  const { t } = useTranslation('common');\r\n\r\n\r\n  const columns = [\r\n    {\r\n      name: t(\"FileType\"),\r\n      width: \"15%\",\r\n      selector: 'extension',\r\n      cell: (d: any) => d && d.extension && d.extension,\r\n    },\r\n    {\r\n      name: t(\"FileName\"),\r\n      width: \"25%\",\r\n      selector: \"document_title\",\r\n      cell: (d: any) => d && d.original_name && <a href={`${process.env.API_SERVER}/files/download/${d._id}`} target=\"_blank\">{d.original_name.split('.').slice(0, -1).join('.')}</a>,\r\n      sortable: true\r\n    },\r\n    {\r\n      name: t(\"Description\"),\r\n      selector: 'description',\r\n      cell: (d: any) => d && d.description && d.description,\r\n    },\r\n    {\r\n      name: t(\"UploadedDate\"),\r\n      width: \"25%\",\r\n      selector: 'doc_created_at',\r\n      cell: (d: any) => d && d.updated_at && moment(d.updated_at).format('MM/DD/YYYY'),\r\n      sortable: true\r\n    }\r\n  ];\r\n\r\n  return (\r\n    <RKITable\r\n      columns={columns}\r\n      data={docs}\r\n      pagServer={true}\r\n      onSort={handleSort}\r\n      persistTableHead\r\n      loading={loading}\r\n    />\r\n\r\n  )\r\n}\r\n\r\nexport default DocumentTable;\r\n"], "names": ["RKITable", "props", "t", "useTranslation", "paginationComponentOptions", "rowsPerPageText", "columns", "data", "totalRows", "resetPaginationToggle", "subheader", "subHeaderComponent", "handlePerRowsChange", "handlePageChange", "rowsPerPage", "defaultRowsPerPage", "selectableRows", "loading", "pagServer", "onSelectedRowsChange", "clearSelectedRows", "sortServer", "onSort", "persistTableHead", "sortFunction", "otherProps", "dataTableProps", "noDataComponent", "<PERSON><PERSON><PERSON><PERSON>", "dense", "paginationResetDefaultPage", "subHeader", "progressPending", "pagination", "paginationServer", "paginationPerPage", "paginationRowsPerPageOptions", "paginationTotalRows", "onChangeRowsPerPage", "onChangePage", "progressComponent", "CustomLoader", "sortIcon", "i", "className", "DataTable", "defaultProps", "DocumentAccordian", "section", "setSection", "useState", "Accordion", "<PERSON><PERSON>", "eventKey", "Header", "onClick", "div", "FontAwesomeIcon", "icon", "faMinus", "color", "faPlus", "Body", "DocumentTable", "documentAccoirdianProps", "sortProps", "hazardDocSort", "docs", "Document", "docsDescription", "docSrc", "h6", "hazardDocUpdateSort", "updateDocument", "handleSort", "column", "sortDirection", "columnSelector", "objSlect", "selector", "name", "width", "cell", "d", "extension", "original_name", "a", "href", "process", "_id", "target", "split", "slice", "join", "sortable", "description", "updated_at", "moment", "format"], "sourceRoot": "", "ignoreList": []}