"use strict";exports.id=745,exports.ids=[745],exports.modules={6417:(e,r,a)=>{a.d(r,{A:()=>t});let s=a(82015).createContext(null);s.displayName="CardHeaderContext";let t=s},15653:(e,r,a)=>{a.d(r,{ks:()=>l,s3:()=>n});var s=a(8732);a(82015);var t=a(59549),i=a(43294);let l=({name:e,id:r,required:a,validator:l,errorMessage:n,onChange:d,value:o,as:c,multiline:u,rows:m,pattern:h,...p})=>(0,s.jsx)(i.Field,{name:e,validate:e=>{let r="string"==typeof e?e:String(e||"");return a&&(!e||""===r.trim())?n?.validator||"This field is required":l&&!l(e)?n?.validator||"Invalid value":h&&e&&!new RegExp(h).test(e)?n?.pattern||"Invalid format":void 0},children:({field:e,meta:a})=>(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)(t.A.Control,{...e,...p,id:r,as:c||"input",rows:m,isInvalid:a.touched&&!!a.error,onChange:r=>{e.onChange(r),d&&d(r)},value:void 0!==o?o:e.value}),a.touched&&a.error?(0,s.jsx)(t.A.Control.Feedback,{type:"invalid",children:a.error}):null]})}),n=({name:e,id:r,required:a,errorMessage:l,onChange:n,value:d,children:o,...c})=>(0,s.jsx)(i.Field,{name:e,validate:e=>{if(a&&(!e||""===e))return l?.validator||"This field is required"},children:({field:e,meta:a})=>(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)(t.A.Control,{as:"select",...e,...c,id:r,isInvalid:a.touched&&!!a.error,onChange:r=>{e.onChange(r),n&&n(r)},value:void 0!==d?d:e.value,children:o}),a.touched&&a.error?(0,s.jsx)(t.A.Control.Feedback,{type:"invalid",children:a.error}):null]})})},18597:(e,r,a)=>{a.d(r,{A:()=>w});var s=a(3892),t=a.n(s),i=a(82015),l=a(80739),n=a(8732);let d=i.forwardRef(({className:e,bsPrefix:r,as:a="div",...s},i)=>(r=(0,l.oU)(r,"card-body"),(0,n.jsx)(a,{ref:i,className:t()(e,r),...s})));d.displayName="CardBody";let o=i.forwardRef(({className:e,bsPrefix:r,as:a="div",...s},i)=>(r=(0,l.oU)(r,"card-footer"),(0,n.jsx)(a,{ref:i,className:t()(e,r),...s})));o.displayName="CardFooter";var c=a(6417);let u=i.forwardRef(({bsPrefix:e,className:r,as:a="div",...s},d)=>{let o=(0,l.oU)(e,"card-header"),u=(0,i.useMemo)(()=>({cardHeaderBsPrefix:o}),[o]);return(0,n.jsx)(c.A.Provider,{value:u,children:(0,n.jsx)(a,{ref:d,...s,className:t()(r,o)})})});u.displayName="CardHeader";let m=i.forwardRef(({bsPrefix:e,className:r,variant:a,as:s="img",...i},d)=>{let o=(0,l.oU)(e,"card-img");return(0,n.jsx)(s,{ref:d,className:t()(a?`${o}-${a}`:o,r),...i})});m.displayName="CardImg";let h=i.forwardRef(({className:e,bsPrefix:r,as:a="div",...s},i)=>(r=(0,l.oU)(r,"card-img-overlay"),(0,n.jsx)(a,{ref:i,className:t()(e,r),...s})));h.displayName="CardImgOverlay";let p=i.forwardRef(({className:e,bsPrefix:r,as:a="a",...s},i)=>(r=(0,l.oU)(r,"card-link"),(0,n.jsx)(a,{ref:i,className:t()(e,r),...s})));p.displayName="CardLink";var x=a(7783);let j=(0,x.A)("h6"),f=i.forwardRef(({className:e,bsPrefix:r,as:a=j,...s},i)=>(r=(0,l.oU)(r,"card-subtitle"),(0,n.jsx)(a,{ref:i,className:t()(e,r),...s})));f.displayName="CardSubtitle";let v=i.forwardRef(({className:e,bsPrefix:r,as:a="p",...s},i)=>(r=(0,l.oU)(r,"card-text"),(0,n.jsx)(a,{ref:i,className:t()(e,r),...s})));v.displayName="CardText";let A=(0,x.A)("h5"),g=i.forwardRef(({className:e,bsPrefix:r,as:a=A,...s},i)=>(r=(0,l.oU)(r,"card-title"),(0,n.jsx)(a,{ref:i,className:t()(e,r),...s})));g.displayName="CardTitle";let y=i.forwardRef(({bsPrefix:e,className:r,bg:a,text:s,border:i,body:o=!1,children:c,as:u="div",...m},h)=>{let p=(0,l.oU)(e,"card");return(0,n.jsx)(u,{ref:h,...m,className:t()(r,p,a&&`bg-${a}`,s&&`text-${s}`,i&&`border-${i}`),children:o?(0,n.jsx)(d,{children:c}):c})});y.displayName="Card";let w=Object.assign(y,{Img:m,Title:g,Subtitle:f,Body:d,Link:p,Text:v,Header:u,Footer:o,ImgOverlay:h})},23579:(e,r,a)=>{a.d(r,{sx:()=>c,s3:()=>t.s3,ks:()=>t.ks,yk:()=>s.A});var s=a(66994),t=a(15653),i=a(8732),l=a(82015),n=a.n(l),d=a(43294),o=a(59549);let c={RadioGroup:({name:e,valueSelected:r,onChange:a,errorMessage:s,children:t})=>{let{errors:l,touched:o}=(0,d.useFormikContext)(),c=o[e]&&l[e];n().useMemo(()=>({name:e}),[e]);let u=n().Children.map(t,r=>n().isValidElement(r)&&function(e){return"object"==typeof e&&null!==e}(r.props)?n().cloneElement(r,{name:e,...r.props}):r);return(0,i.jsxs)("div",{children:[(0,i.jsx)("div",{className:"radio-group",children:u}),c&&(0,i.jsx)("div",{className:"invalid-feedback d-block",children:s||("string"==typeof l[e]?l[e]:String(l[e]))})]})},RadioItem:({id:e,label:r,value:a,name:s,disabled:t})=>{let{values:l,setFieldValue:n}=(0,d.useFormikContext)(),c=s||e;return(0,i.jsx)(o.A.Check,{type:"radio",id:e,label:r,value:a,name:c,checked:l[c]===a,onChange:e=>{n(c,e.target.value)},disabled:t,inline:!0})}};s.A,t.ks,t.s3},66994:(e,r,a)=>{a.d(r,{A:()=>d});var s=a(8732),t=a(82015),i=a(43294),l=a(18622);let n=(0,t.forwardRef)((e,r)=>{let{children:a,onSubmit:t,autoComplete:n,className:d,onKeyPress:o,initialValues:c,...u}=e,m=l.object().shape({});return(0,s.jsx)(i.Formik,{initialValues:c||{},validationSchema:m,onSubmit:(e,r)=>{let a={preventDefault:()=>{},stopPropagation:()=>{},currentTarget:null,target:null,nativeEvent:new Event("submit"),bubbles:!1,cancelable:!0,defaultPrevented:!1,eventPhase:0,isTrusted:!1,timeStamp:Date.now(),type:"submit",isDefaultPrevented:()=>!1,isPropagationStopped:()=>!1,persist:()=>{}};t&&t(a,e,r)},...u,children:e=>(0,s.jsx)(i.Form,{ref:r,onSubmit:e.handleSubmit,autoComplete:n,className:d,onKeyPress:o,children:"function"==typeof a?a(e):a})})});n.displayName="ValidationFormWrapper";let d=n},80237:(e,r)=>{Object.defineProperty(r,"A",{enumerable:!0,get:function(){return a}});var a=function(e){return e.PAGES="PAGES",e.PAGES_API="PAGES_API",e.APP_PAGE="APP_PAGE",e.APP_ROUTE="APP_ROUTE",e.IMAGE="IMAGE",e}({})},81413:(e,r)=>{Object.defineProperty(r,"M",{enumerable:!0,get:function(){return function e(r,a){return a in r?r[a]:"then"in r&&"function"==typeof r.then?r.then(r=>e(r,a)):"function"==typeof r&&"default"===a?r:void 0}}})},97465:(e,r,a)=>{a.a(e,async(e,s)=>{try{a.r(r),a.d(r,{default:()=>w});var t=a(8732),i=a(82015),l=a(7082),n=a(18597),d=a(83551),o=a(49481),c=a(59549),u=a(91353),m=a(23579),h=a(44233),p=a.n(h),x=a(17906),j=a.n(x),f=a(11e3),v=a(63487),A=e([v]);v=(A.then?(await A)():A)[0];let g={username:"",email:"",role:"",password:"",confirm_password:"",institution:"",region:[],country:"",enabled:""},y={query:{},sort:{title:"asc"},limit:"~"},w=e=>{let[r,a]=(0,i.useState)(g),[s,x]=(0,i.useState)([]),[A,w]=(0,i.useState)([]),[b,C]=(0,i.useState)([]),[P,N]=(0,i.useState)([]),[S,_]=(0,i.useState)({}),R=(0,h.useRouter)().query.routes||[],k=async()=>{let e=await v.A.get("roles");e&&e.data&&e.data.length>0&&x(e.data)},I=e=>{_(e),a(r=>({...r,username:e.username,email:e.email,role:e.role?e.role._id:"",institution:e.institution?e.institution._id:"",region:e.region?e.region._id.map((e,r)=>({label:e.title,value:e._id})):[],country:e.country?e.country._id:"",enabled:e.enabled?"true":"false"}))},E=async()=>{if(R[1]){let e=await v.A.get(`users/${R[1]}`);e&&e._id&&I(e),F(e.country)}},q=async()=>{let e=await v.A.get("institution");e&&e.data&&e.data.length>0&&w(e.data)},G=async()=>{let e=await v.A.get("/country",y);e&&e.data&&e.data.length>0&&N(e.data)};(0,i.useEffect)(()=>{k(),E(),q(),G()},[]);let U=(0,i.useRef)(null),M=e=>{a(r=>({...r,...e}))},F=async e=>{let r=[];if(e){let a=await v.A.get(`/country_region/${e}`,{});a&&a.data&&(r=a.data.map((e,r)=>({label:e.title,value:e._id}))).sort((e,r)=>e.label.localeCompare(r.label))}C(r)},T=e=>{let{name:r,value:s}=e.currentTarget;a(e=>({...e,[r]:s})),"country"===r&&(F(s),M({region:[]}))},L=async e=>{e.preventDefault();let a={username:r.username,email:r.email,role:r.role,institution:r.institution?r.institution:null,region:r.region?r.region.map((e,r)=>e.value):[],country:r.country?r.country:null,enabled:"true"===r.enabled};S&&S._id?(""!==r.password&&(a.password=r.password),await v.A.patch(`/users/${S._id}`,a)):(a.password=r.password,await v.A.post("/users",a)),p().push("/users")},$=e=>e===r.password;return(0,t.jsx)(l.A,{className:"formCard",fluid:!0,children:(0,t.jsx)(n.A,{children:(0,t.jsx)(m.yk,{onSubmit:L,ref:U,children:(0,t.jsxs)(n.A.Body,{children:[(0,t.jsx)(d.A,{children:(0,t.jsx)(o.A,{children:(0,t.jsx)(n.A.Title,{children:S&&S._id?"Edit User":"Create User"})})}),(0,t.jsx)("hr",{}),(0,t.jsx)(d.A,{children:(0,t.jsx)(o.A,{children:(0,t.jsxs)(c.A.Group,{children:[(0,t.jsx)(c.A.Label,{className:"required-field",children:"Username"}),(0,t.jsx)(m.ks,{name:"username",id:"username",required:!0,minLength:"3",value:r.username,errorMessage:"You don't have a Username?",onChange:T})]})})}),(0,t.jsx)(d.A,{children:(0,t.jsx)(o.A,{children:(0,t.jsxs)(c.A.Group,{children:[(0,t.jsx)(c.A.Label,{className:"required-field",children:"Email"}),(0,t.jsx)(m.ks,{name:"email",id:"email",type:"email",validator:j().isEmail,required:!0,errorMessage:{validator:"Please enter a valid email"},value:r.email,onChange:T})]})})}),(0,t.jsx)(d.A,{children:(0,t.jsx)(o.A,{children:(0,t.jsxs)(c.A.Group,{children:[(0,t.jsx)(c.A.Label,{children:"Role"}),(0,t.jsxs)(m.s3,{name:"role",value:r.role,errorMessage:"Please select a role",onChange:T,children:[(0,t.jsx)("option",{value:"",children:"Select Role"}),s.map((e,r)=>(0,t.jsx)("option",{value:e._id,children:e.title},r))]})]})})}),(0,t.jsx)(d.A,{children:(0,t.jsx)(o.A,{children:(0,t.jsxs)(c.A.Group,{children:[(0,t.jsx)(c.A.Label,{children:"Institution"}),(0,t.jsxs)(m.s3,{name:"institution",value:r.institution,errorMessage:"Please select a Institution.",onChange:T,children:[(0,t.jsx)("option",{value:"",children:"Select Institution"}),A.map((e,r)=>(0,t.jsx)("option",{value:e._id,children:e.title},r))]})]})})}),(0,t.jsx)(d.A,{children:(0,t.jsx)(o.A,{children:(0,t.jsxs)(c.A.Group,{children:[(0,t.jsx)(c.A.Label,{children:"Country"}),(0,t.jsxs)(m.s3,{name:"country",id:"country",value:r.country,onChange:T,children:[(0,t.jsx)("option",{value:"",children:"Select Country"}),P.map((e,r)=>(0,t.jsx)("option",{value:e._id,children:e.title},e._id))]})]})})}),(0,t.jsx)(d.A,{children:(0,t.jsx)(o.A,{children:(0,t.jsxs)(c.A.Group,{children:[(0,t.jsx)(c.A.Label,{children:"Region"}),(0,t.jsx)(f.MultiSelect,{overrideStrings:{selectSomeItems:"Select Country Regions",allItemsAreSelected:"All Regions are Selected"},options:b,value:r.region,onChange:e=>{a(r=>({...r,region:e}))},className:"region",labelledBy:"Select Country Regions"})]})})}),(0,t.jsx)(d.A,{children:(0,t.jsx)(o.A,{children:(0,t.jsxs)(c.A.Group,{children:[(0,t.jsx)(c.A.Label,{className:"required-field",children:"Password"}),S&&S._id?(0,t.jsx)(m.ks,{name:"password",id:"password",type:"password",pattern:"(?=.*[A-Z]).{8,}",errorMessage:{pattern:"Password should be at least 8 characters and contains at least one upper case letter"},value:r.password,onChange:T}):(0,t.jsx)(m.ks,{name:"password",id:"password",type:"password",required:!0,pattern:"(?=.*[A-Z]).{8,}",errorMessage:{required:"Password is required",pattern:"Password should be at least 8 characters and contains at least one upper case letter"},value:r.password,onChange:T})]})})}),(0,t.jsx)(d.A,{children:(0,t.jsx)(o.A,{children:(0,t.jsxs)(c.A.Group,{children:[(0,t.jsx)(c.A.Label,{className:"required-field",children:"Confirm Password"}),S&&S._id?(0,t.jsx)(m.ks,{name:"confirm_password",id:"confirm_password",type:"password",validator:$,errorMessage:{required:"Confirm password is required",validator:"Password does not match"},value:r.confirm_password,onChange:T}):(0,t.jsx)(m.ks,{name:"confirm_password",id:"confirm_password",type:"password",required:!0,validator:$,errorMessage:{required:"Confirm password is required",validator:"Password does not match"},value:r.confirm_password,onChange:T})]})})}),(0,t.jsx)(d.A,{children:(0,t.jsx)(o.A,{children:(0,t.jsxs)(c.A.Group,{children:[(0,t.jsx)("label",{children:"Enabled"}),(0,t.jsxs)(m.sx.RadioGroup,{name:"enabled",errorMessage:"It is required",valueSelected:r.enabled,onChange:T,children:[(0,t.jsx)(m.sx.RadioItem,{id:"yes",label:"Yes",value:"true"}),(0,t.jsx)(m.sx.RadioItem,{id:"no",label:"No",value:"false"})]})]})})}),(0,t.jsxs)(d.A,{className:"my-4",children:[(0,t.jsx)(o.A,{xs:!0,lg:"2",children:(0,t.jsx)(u.A,{type:"submit",variant:"primary",children:"Submit"})}),(0,t.jsx)(o.A,{children:(0,t.jsx)(u.A,{onClick:()=>{a(g),window.scrollTo(0,0)},variant:"info",children:"Reset"})})]})]})})})})};s()}catch(e){s(e)}})}};