"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[2697],{8758:(e,s,a)=>{a.r(s),a.d(s,{default:()=>d});var t=a(37876),c=a(14232),n=a(82851),r=a.n(n),l=a(51337),i=a(53718);let d=function(e){let[s,a]=(0,c.useState)([]),n=async()=>{let e=await i.A.get("/updateType",{query:{title:"Calendar Event"}});e&&e.data&&d(e.data[0]._id)},d=async s=>{let t=await i.A.get("/updates",{query:{type:"vspace",update_type:s},sort:{created_at:"desc"},limit:20});if(t&&t.data&&e.id){let s=r().filter(t.data,{parent_vspace:e.id});r().forEach(s,function(e,a){s[a].allDay=!1}),a(s)}};return(0,c.useEffect)(()=>{e.id&&n()},[e.id]),(0,t.jsx)(l.A,{eventsList:s,minicalendar:!0,showEventCounts:!0})}},31647:(e,s,a)=>{a.d(s,{A:()=>o});var t=a(37876),c=a(31777),n=a(11041),r=a(14232),l=a(21772),i=a(53718);let d={operation:"Operation",institution:"Institution",event:"Event",project:"Project",vspace:"Vspace"},o=(0,c.Ng)(e=>e)(e=>{let{user:s,entityId:a,entityType:c}=e,[o,u]=(0,r.useState)(!1),[m,p]=(0,r.useState)(""),v=async()=>{if(!(null==s?void 0:s._id))return;let e=await i.A.get("/flag",{query:{entity_id:a,user:s._id,onModel:d[c]}});e&&e.data&&e.data.length>0&&(p(e.data[0]),u(!0))},h=async e=>{if(e.preventDefault(),!(null==s?void 0:s._id))return;let t=!o,n={entity_type:c,entity_id:a,user:s._id,onModel:d[c]};if(t){let e=await i.A.post("/flag",n);e&&e._id&&(p(e),u(t))}else{let e=await i.A.remove("/flag/".concat(m._id));e&&e.n&&u(t)}};return(0,r.useEffect)(()=>{v()},[]),(0,t.jsx)("div",{className:"subscribe-flag",children:(0,t.jsxs)("a",{href:"",onClick:h,children:[(0,t.jsx)("span",{className:"check",children:o?(0,t.jsx)(l.g,{className:"clickable checkIcon",icon:n.SGM,color:"#00CC00"}):(0,t.jsx)(l.g,{className:"clickable minusIcon",icon:n.OQW,color:"#fff"})}),(0,t.jsx)(l.g,{className:"bookmark",icon:n.G06,color:"#d4d4d4"})]})})})},32697:(e,s,a)=>{a.r(s),a.d(s,{default:()=>C});var t=a(37876),c=a(14232),n=a(60282),r=a(32890),l=a(49589),i=a(56970),d=a(37784),o=a(89099),u=a(10841),m=a.n(u);a(84135);var p=a(21772),v=a(11041),h=a(48230),x=a.n(h),j=a(53718),f=a(48477),g=a(72800),b=a(31753),y=a(37308),A=a(8758),_=a(31647),N=a(67663),S=a(56766),w=a(55176),E=a(20276);let M={true:"Public - Accessible to all site users",false:"Private - Accessible only to group members"},k=[{name:"User Name",selector:"username",sortable:!0},{name:"Email",selector:"email",sortable:!0}],C=e=>{let{t:s,i18n:a}=(0,b.Bd)("common"),[u,h]=(0,c.useState)({description:"",owner:""}),C="fr"===a.language?"en":a.language,[q,D]=(0,c.useState)([]),[R,T]=(0,c.useState)([]),[B,H]=(0,c.useState)([]),[L]=(0,c.useState)([]),[Y]=(0,c.useState)(C),[U,V]=(0,c.useState)([]),[F,I]=(0,c.useState)([]),[P,z]=(0,c.useState)(!1),[O,G]=(0,c.useState)(!1),W=(0,o.useRouter)().query.routes||[],Q={sort:{doc_created_at:"dsc"},Doctable:!0},K={sort:{doc_created_at:"dsc"},collation:"en",updateDoctable:!0},X=async()=>{let e=[];z(!0);let s=await j.A.get("/vspace/".concat(W[1]),K);s&&s.data&&Array.isArray(s.data)&&s.data.length>=1&&(s.data.forEach((s,a)=>{s.document&&s.document.length>0&&s.document.map((a,t)=>{a.description=s.document[t].docsrc,e.push(a)})}),H(e)),z(!1)},Z=async()=>{let e=[];G(!0);let s=await j.A.get("/vspace/".concat(W[1]),Q);s&&s.data&&Array.isArray(s.data)&&s.data.length>=1&&(s.data.forEach((s,a)=>{s.document&&s.document.length>0&&s.document.map((a,t)=>{a.description=s.document[t].docsrc,e.push(a)})}),I(e)),G(!1)},J=async()=>{try{let e=await j.A.get("/vspace/".concat(W[1])),s=await j.A.get("/vspace-request-subscribers/getRequestedToMe",{query:{vspace:W[1]},select:"-vspace -requested_to -created_at -updated_at"});if(s&&s.data&&s.data.length>0){let e=s.data.map(e=>(e.requested_by._id=e._id,e.requested_by));D(e)}let a=null==e?void 0:e.nonMembers.filter(s=>!(null==e?void 0:e.members.map(e=>e.email).includes(s)));if(a.length>0){let s=await j.A.get("/users",{query:{vspace_status:"Approved",is_vspace:!0},sort:{username:"asc"},limit:"~"}),t=a.map(e=>s.data.filter(s=>s.email==e).length>0?s.data.filter(s=>s.email==e)[0]._id:"");if((t=t.filter(e=>""!=e)).length>0){let a=t,c=e&&e.members.map(e=>e._id);await j.A.patch("/vspace/".concat(e._id),{...e,members:[...c,...a],nonMembers:""===e.nonMembers[0]?"":e.nonMembers});let n=null==a?void 0:a.map(e=>(null==s?void 0:s.data.filter(s=>s._id==e).length)>0?s.data.filter(s=>s._id==e)[0]:[]);n=n.filter(e=>""!=e),e.members=e.members.concat(n)}}T(e.members),h(e),V([{title:"VSpace",start:e.start_date,end:e.end_date,allDay:!0,images:e.images,images_src:e.images_src,document:e.document,doc_src:e.doc_src}])}catch(e){console.log(e)}};(0,c.useEffect)(()=>{J(),X(),Z()},[]);let $=[{name:s("vspace.UserName"),selector:"username",sortable:!0},{name:s("vspace.Email"),selector:"email",sortable:!0}],[ee,es]=(0,c.useState)(!0),[ea,et]=(0,c.useState)(!0),[ec,en]=(0,c.useState)(!0),[er,el]=(0,c.useState)(!0),ei=()=>(0,t.jsx)(x(),{href:"/vspace/[...routes]",as:"/vspace/edit/".concat(u._id),children:(0,t.jsxs)(n.A,{variant:"secondary",size:"sm",children:[(0,t.jsx)(p.g,{icon:v.hpd}),"\xa0",s("vspace.Edit")]})}),ed=()=>(0,t.jsx)(x(),{href:{pathname:"/vspace/[...routes]",query:{id:e.routes[1]}},as:"/vspace/manage?id=".concat(e.routes[1]),children:(0,t.jsx)("span",{children:(0,t.jsx)(p.g,{icon:v.McB,size:"2x",color:"#232c3d",className:"clickable"})})}),eo=()=>(0,t.jsxs)(r.A.Item,{eventKey:"3",children:[(0,t.jsxs)(r.A.Header,{onClick:()=>el(!er),children:[(0,t.jsx)("div",{className:"cardTitle",children:s("vspace.Discussions")}),(0,t.jsx)("div",{className:"cardArrow",children:er?(0,t.jsx)(p.g,{icon:v.QLR,color:"#fff"}):(0,t.jsx)(p.g,{icon:v.EZy,color:"#fff"})})]}),(0,t.jsx)(r.A.Body,{children:(0,t.jsx)(f.A,{type:"vspace",id:e&&e.routes?e.routes[1]:null})})]});(0,N.canViewDiscussionUpdate)(()=>(0,t.jsx)(eo,{}));let eu=(0,N.canEditVspace)(()=>(0,t.jsx)(ei,{})),em=(0,N.canEditVspace)(()=>(0,t.jsx)(ed,{})),ep={Monitoringmembers:{columns:k,subscribers:R},SubscribeRequestUsers:{requestedColumns:$,vspaceRequest:q},vSpaceLoading:O,vSpaceSort:e=>{Q.sort={[e.columnSelector]:e.sortDirection},Z()},documentAccoirdianProps:{vSpaceDocs:F,updateLoading:P,updateSort:e=>{K.sort={[e.columnSelector]:e.sortDirection},X()},docSrc:L},calenderEvents:U,document:B};return(0,t.jsxs)(l.A,{fluid:!0,className:"vspaceDetails",children:[(0,t.jsx)(y.A,{routes:e.routes}),(0,t.jsxs)(i.A,{style:{marginTop:"10px",marginBottom:"25px"},children:[(0,t.jsx)(d.A,{className:"ps-md-1 pe-md-1",md:8,children:function(e,s,a,c,n){return(0,t.jsxs)("div",{className:"vspaceCard",children:[(0,t.jsxs)("div",{className:"vspaceTitle",children:[(0,t.jsxs)("h4",{children:[e.title,"\xa0\xa0",s.routes&&s.routes[1]?(0,t.jsx)(a,{vspace:e}):null]}),(0,t.jsx)("section",{className:"d-flex justify-content-between",children:(0,t.jsx)(_.A,{entityId:s.routes[1],entityType:"vspace"})})]}),(0,t.jsx)("div",{className:"vspaceDesc",children:(0,t.jsx)(g.A,{description:e.description})}),(0,t.jsxs)("div",{className:"vspaceInfo",children:[(0,t.jsxs)("p",{children:[(0,t.jsx)("b",{children:c("vspace.Ownedby")}),": ",e.user&&e.user.username?"".concat(e.user.username):""," "]}),(0,t.jsxs)("p",{children:[(0,t.jsx)("b",{children:c("vspace.Groupvisibility")}),": ",M[e.visibility]]}),(0,t.jsxs)("p",{children:[(0,t.jsx)("b",{children:c("vspace.Created")}),": ",m()(e.created_at).locale(n).format("ddd, D MMMM YYYY")]}),(0,t.jsxs)("p",{children:[(0,t.jsx)("b",{children:c("vspace.LastModified")}),": ",m()(e.updated_at).locale(n).format("ddd, D MMMM YYYY")]})]})]})}(u,e,eu,s,Y)}),(0,t.jsx)(d.A,{className:"pe-md-1",md:4,children:(0,t.jsxs)("div",{className:"vspaceCard vspaceCalendar",children:[(0,t.jsx)("div",{className:"vspaceTitle",children:(0,t.jsx)("h4",{children:s("calendar")})}),(0,t.jsx)(A.default,{type:"vspace",id:e&&e.routes?e.routes[1]:null})]})})]}),(0,t.jsx)(i.A,{children:(0,t.jsxs)(d.A,{className:"ps-1 pe-1",children:[(0,t.jsxs)("div",{className:"d-flex justify-content-between",children:[(0,t.jsx)("h4",{children:s("vspace.Monitoringandevaluationmembers")}),e.routes&&e.routes[1]?(0,t.jsx)(em,{vspace:u}):null]}),(0,t.jsx)(S.default,{...ep})]})}),(0,t.jsx)(i.A,{children:(0,t.jsxs)(d.A,{className:"ps-1 pe-1",children:[(0,t.jsx)("h4",{children:s("vspace.SubscribeRequestUsers")}),q&&q.length>0?(0,t.jsx)(w.default,{...ep}):(0,t.jsx)("div",{className:"nodataFound",children:s("vspace.Nodataavailable")})]})}),(0,t.jsx)(i.A,{children:(0,t.jsx)(d.A,{className:"vspaceAccordion ps-1 pe-1",xs:12,children:(0,t.jsx)(E.default,{...ep})})})]})}},51337:(e,s,a)=>{a.d(s,{A:()=>_});var t=a(37876),c=a(30523),n=a(10841),r=a.n(n);a(90932);var l=a(89099),i=a.n(l),d=a(49589),o=a(56970),u=a(37784),m=a(21772),p=a(11041),v=a(82851),h=a.n(v),x=a(31753);let j=(0,c.ye)(r());function f(e){let{t:s,i18n:a}=(0,x.Bd)("common"),n=a.language,{eventsList:r,style:l,minicalendar:d,views:o}=e,u={};return e.showEventCounts&&(u={eventWrapper:A,month:{dateHeader:e=>(0,t.jsx)(y,{eventsList:r,...e})}}),d&&(u.toolbar=g),(0,t.jsx)(c.Vv,{culture:n,localizer:j,events:r,views:o,startAccessor:"start_date",endAccessor:"end_date",style:l,components:u,messages:{today:s("today"),previous:s("back"),next:s("Next"),month:s("Month"),week:s("Week"),day:s("Day")},onSelectEvent:e=>{let s=Object.keys(e).filter(e=>e.includes("parent")).toString(),a=s.split("_")[1];i().push("/".concat(a,"/show/").concat(e[s],"/update/").concat(e._id))}})}function g(e){return(0,t.jsx)(d.A,{className:"mb-1",children:(0,t.jsxs)(o.A,{children:[(0,t.jsx)(u.A,{className:"p-0",md:1,children:(0,t.jsx)("i",{style:{cursor:"pointer"},onClick:()=>e.onNavigate("PREV"),className:"fas fa-chevron-left"})}),(0,t.jsx)(u.A,{className:"text-center",md:10,children:(0,t.jsx)("span",{className:"rbc-toolbar-label",children:e.label})}),(0,t.jsx)(u.A,{className:"p-0 text-end",md:1,children:(0,t.jsx)("i",{style:{cursor:"pointer"},onClick:()=>e.onNavigate("NEXT"),className:"fas fa-chevron-right"})})]})})}f.defaultProps={minicalendar:!1,views:["month"]};let b=(e,s)=>{let a=0;return h().forEach(e,e=>{let t=r()(e.start_date).set({hour:0,minute:0,second:0,millisecond:0}),c=r()(e.end_date).set({hour:0,minute:0,second:0,millisecond:0});r()(s).isBetween(t,c,null,"[]")&&(a+=1)}),a},y=e=>{let{date:s,label:a,eventsList:c}=e,n=b(c,s),l=r()(s).isBefore(new Date,"day");return(0,t.jsxs)("div",{className:"rbc-date-cell",onClick:()=>i().push("/events-calendar"),children:[(0,t.jsx)("a",{href:"#",children:a}),n>0&&(0,t.jsxs)("span",{className:"d-flex justify-content-start align-items-center fa-stack",children:[(0,t.jsx)(m.g,{icon:p.yy,color:l?"grey":"#04A6FB",size:"lg"}),(0,t.jsx)("span",{className:"eventCount",children:n})]})]})},A=e=>(0,t.jsx)("div",{onSelect:e.onSelect}),_=f},55176:(e,s,a)=>{a.r(s),a.d(s,{default:()=>l});var t=a(37876),c=a(50749),n=a(82851),r=a.n(n);let l=e=>(0,t.jsx)(t.Fragment,{children:(0,t.jsx)(c.A,{noHeader:!0,columns:e.SubscribeRequestUsers.requestedColumns,data:e.SubscribeRequestUsers.vspaceRequest,dense:!0,paginationServer:!0,pagServer:!0,paginationTotalRows:0,subHeader:!0,subHeaderAlign:"left",pagination:!0,persistTableHead:!0,sortFunction:(e,s,a)=>r().orderBy(e,e=>e[s]?e[s].toLowerCase():e[s],a)})})},56766:(e,s,a)=>{a.r(s),a.d(s,{default:()=>l});var t=a(37876),c=a(82851),n=a.n(c),r=a(50749);let l=e=>(0,t.jsx)(t.Fragment,{children:(0,t.jsx)(r.A,{noHeader:!0,columns:e.Monitoringmembers.columns,data:e.Monitoringmembers.subscribers,dense:!0,paginationServer:!0,pagServer:!0,paginationTotalRows:0,subHeader:!0,subHeaderAlign:"left",pagination:!0,persistTableHead:!0,sortFunction:(e,s,a)=>n().orderBy(e,e=>e[s]?e[s].toLowerCase():e[s],a)})})}}]);
//# sourceMappingURL=2697-aaaced30b342592a.js.map