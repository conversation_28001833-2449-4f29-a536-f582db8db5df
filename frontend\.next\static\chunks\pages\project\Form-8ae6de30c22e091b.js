(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[3304],{5671:(e,n,t)=>{"use strict";t.d(n,{x:()=>o});var r=t(37876),i=t(14232);let l=e=>{let{value:n,onChange:t,placeholder:l="Write something...",height:o=300,disabled:d=!1}=e,s=(0,i.useRef)(null),[c,p]=(0,i.useState)(!1);(0,i.useEffect)(()=>{s.current&&1&&!c&&s.current.innerHTML!==n&&(s.current.innerHTML=n||"")},[n,c]);let u=()=>{s.current&&t&&t(s.current.innerHTML)},a=(e,n)=>{if("undefined"!=typeof document){var t;document.execCommand(e,!1,n||""),u(),null==(t=s.current)||t.focus()}};return(0,r.jsx)("div",{className:"simple-rich-text-editor",style:{border:"1px solid #ccc"},children:(0,r.jsxs)(r.Fragment,{children:[(0,r.jsxs)("div",{className:"toolbar",style:{padding:"8px",borderBottom:"1px solid #ccc",background:"#f5f5f5"},children:[(0,r.jsx)("button",{type:"button",onClick:()=>a("bold"),style:{margin:"0 5px",padding:"3px 8px"},children:(0,r.jsx)("strong",{children:"B"})}),(0,r.jsx)("button",{type:"button",onClick:()=>a("italic"),style:{margin:"0 5px",padding:"3px 8px"},children:(0,r.jsx)("em",{children:"I"})}),(0,r.jsx)("button",{type:"button",onClick:()=>a("underline"),style:{margin:"0 5px",padding:"3px 8px"},children:(0,r.jsx)("u",{children:"U"})}),(0,r.jsx)("button",{type:"button",onClick:()=>a("insertOrderedList"),style:{margin:"0 5px",padding:"3px 8px"},children:"OL"}),(0,r.jsx)("button",{type:"button",onClick:()=>a("insertUnorderedList"),style:{margin:"0 5px",padding:"3px 8px"},children:"UL"}),(0,r.jsx)("button",{type:"button",onClick:()=>{let e=prompt("Enter the link URL");e&&a("createLink",e)},style:{margin:"0 5px",padding:"3px 8px"},children:"Link"})]}),(0,r.jsx)("div",{ref:s,contentEditable:!d,onInput:u,onFocus:()=>p(!0),onBlur:()=>p(!1),style:{padding:"15px",minHeight:o,maxHeight:2*o,overflow:"auto",outline:"none"},"data-placeholder":n?"":l,suppressContentEditableWarning:!0})]})})},o=e=>{let{initContent:n,onChange:t}=e;return(0,r.jsx)(l,{value:n||"",onChange:e=>t(e)})}},47376:(e,n,t)=>{(window.__NEXT_P=window.__NEXT_P||[]).push(["/project/Form",function(){return t(67051)}])}},e=>{var n=n=>e(e.s=n);e.O(0,[7725,1121,6701,1772,698,8220,5939,7051,636,6593,8792],()=>n(47376)),_N_E=e.O()}]);
//# sourceMappingURL=Form-8ae6de30c22e091b.js.map