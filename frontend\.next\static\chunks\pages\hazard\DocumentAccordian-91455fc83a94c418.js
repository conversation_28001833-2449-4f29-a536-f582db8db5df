(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[5024],{45612:(e,o,a)=>{(window.__NEXT_P=window.__NEXT_P||[]).push(["/hazard/DocumentAccordian",function(){return a(60347)}])},50749:(e,o,a)=>{"use strict";a.d(o,{A:()=>i});var n=a(37876);a(14232);var t=a(89773),r=a(31753),s=a(5507);function c(e){let{t:o}=(0,r.Bd)("common"),a={rowsPerPageText:o("Rowsperpage")},{columns:c,data:i,totalRows:d,resetPaginationToggle:l,subheader:p,subHeaderComponent:m,handlePerRowsChange:u,handlePageChange:g,rowsPerPage:h,defaultRowsPerPage:P,selectableRows:x,loading:_,pagServer:A,onSelectedRowsChange:f,clearSelectedRows:w,sortServer:j,onSort:D,persistTableHead:v,sortFunction:S,...N}=e,b={paginationComponentOptions:a,noDataComponent:o("NoData"),noHeader:!0,columns:c,data:i||[],dense:!0,paginationResetDefaultPage:l,subHeader:p,progressPending:_,subHeaderComponent:m,pagination:!0,paginationServer:A,paginationPerPage:P||10,paginationRowsPerPageOptions:h||[10,15,20,25,30],paginationTotalRows:d,onChangeRowsPerPage:u,onChangePage:g,selectableRows:x,onSelectedRowsChange:f,clearSelectedRows:w,progressComponent:(0,n.jsx)(s.A,{}),sortIcon:(0,n.jsx)("i",{className:"sort-icon fas fa-exchange-alt"}),sortServer:j,onSort:D,sortFunction:S,persistTableHead:v,className:"rki-table"};return(0,n.jsx)(t.Ay,{...b})}c.defaultProps={subHeader:!1,pagination:!0,totalRows:null,pagServer:!0,onSelectedRowsChange:null,clearSelectedRows:!1,sortServer:!1,persistTableHead:!1};let i=c},60347:(e,o,a)=>{"use strict";a.r(o),a.d(o,{default:()=>l});var n=a(37876),t=a(11041),r=a(21772),s=a(32890),c=a(14232),i=a(66404),d=a(31753);let l=e=>{let{t:o}=(0,d.Bd)("common"),[a,l]=(0,c.useState)(!1);return(0,n.jsx)(n.Fragment,{children:(0,n.jsxs)(s.A.Item,{eventKey:"0",children:[(0,n.jsxs)(s.A.Header,{onClick:()=>l(!a),children:[(0,n.jsx)("div",{className:"cardTitle",children:o("documents")}),(0,n.jsx)("div",{className:"cardArrow",children:a?(0,n.jsx)(r.g,{icon:t.EZy,color:"#fff"}):(0,n.jsx)(r.g,{icon:t.QLR,color:"#fff"})})]}),(0,n.jsxs)(s.A.Body,{children:[(0,n.jsx)(i.A,{loading:e.documentAccoirdianProps.loading,sortProps:e.documentAccoirdianProps.hazardDocSort,docs:e.documentAccoirdianProps.Document||[],docsDescription:e.documentAccoirdianProps.docSrc}),(0,n.jsx)("h6",{className:"mt-3",children:o("DocumentsfromUpdates")}),(0,n.jsx)(i.A,{loading:e.documentAccoirdianProps.loading,sortProps:e.documentAccoirdianProps.hazardDocUpdateSort,docs:e.documentAccoirdianProps.updateDocument||[],docsDescription:e.documentAccoirdianProps.docSrc})]})]})})}},66404:(e,o,a)=>{"use strict";a.d(o,{A:()=>i});var n=a(37876);a(14232);var t=a(10841),r=a.n(t),s=a(50749),c=a(31753);let i=e=>{let{docs:o,docsDescription:a,sortProps:t,loading:i}=e,d=async(e,o)=>{t({columnSelector:e.selector,sortDirection:o})},{t:l}=(0,c.Bd)("common"),p=[{name:l("FileType"),width:"15%",selector:"extension",cell:e=>e&&e.extension&&e.extension},{name:l("FileName"),width:"25%",selector:"document_title",cell:e=>e&&e.original_name&&(0,n.jsx)("a",{href:"".concat("http://localhost:3001/api/v1","/files/download/").concat(e._id),target:"_blank",children:e.original_name.split(".").slice(0,-1).join(".")}),sortable:!0},{name:l("Description"),selector:"description",cell:e=>e&&e.description&&e.description},{name:l("UploadedDate"),width:"25%",selector:"doc_created_at",cell:e=>e&&e.updated_at&&r()(e.updated_at).format("MM/DD/YYYY"),sortable:!0}];return(0,n.jsx)(s.A,{columns:p,data:o,pagServer:!0,onSort:d,persistTableHead:!0,loading:i})}}},e=>{var o=o=>e(e.s=o);e.O(0,[7725,9773,1772,636,6593,8792],()=>o(45612)),_N_E=e.O()}]);
//# sourceMappingURL=DocumentAccordian-91455fc83a94c418.js.map