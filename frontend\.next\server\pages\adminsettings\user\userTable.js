"use strict";(()=>{var e={};e.id=9422,e.ids=[636,3220,9422],e.modules={123:e=>{e.exports=require("dom-helpers/removeEventListener")},1332:e=>{e.exports=require("react-custom-scrollbars-2")},1428:e=>{e.exports=import("axios")},1680:e=>{e.exports=require("@restart/ui/utils")},1919:e=>{e.exports=require("react-transition-group/Transition")},3892:e=>{e.exports=require("classnames")},4048:e=>{e.exports=require("react-truncate")},6009:e=>{e.exports=require("dom-helpers/ownerDocument")},6952:e=>{e.exports=require("@restart/ui/Modal")},7374:e=>{e.exports=require("@restart/ui/Dropdown")},8732:e=>{e.exports=require("react/jsx-runtime")},9653:e=>{e.exports=require("@restart/ui/Overlay")},11242:e=>{e.exports=require("redux-persist/integration/react")},11688:e=>{e.exports=require("@restart/hooks/useWillUnmount")},12388:e=>{e.exports=require("dom-helpers/addEventListener")},13364:e=>{e.exports=require("redux-saga/effects")},14062:e=>{e.exports=import("react-redux")},14078:e=>{e.exports=import("swr")},14332:e=>{e.exports=require("uncontrollable")},16116:e=>{e.exports=require("invariant")},18622:e=>{e.exports=require("yup")},19121:e=>{e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},22326:e=>{e.exports=require("react-dom")},22541:e=>{e.exports=require("redux-persist/lib/storage")},25303:e=>{e.exports=require("@restart/ui/NavItem")},26324:e=>{e.exports=require("warning")},27825:e=>{e.exports=require("lodash")},27910:e=>{e.exports=require("stream")},28217:e=>{e.exports=require("@restart/ui/DropdownItem")},29021:e=>{e.exports=require("fs")},29294:e=>{e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},29628:e=>{e.exports=require("dom-helpers/addClass")},29825:e=>{e.exports=require("prop-types")},29841:e=>{e.exports=require("dom-helpers/hasClass")},30362:e=>{e.exports=require("@restart/ui/DropdownToggle")},33873:e=>{e.exports=require("path")},36653:e=>{e.exports=require("nprogress")},36955:e=>{e.exports=require("@restart/hooks/useBreakpoint")},37766:e=>{e.exports=require("@restart/hooks/useMergedRefs")},38609:e=>{e.exports=require("react-data-table-component")},39756:e=>{e.exports=import("redux")},40051:e=>{e.exports=require("dom-helpers/removeClass")},40361:e=>{e.exports=require("next/dist/compiled/next-server/pages.runtime.prod.js")},42893:e=>{e.exports=import("react-hot-toast")},43294:e=>{e.exports=require("formik")},49399:(e,t,r)=>{r.a(e,async(e,s)=>{try{r.r(t),r.d(t,{config:()=>h,default:()=>c,getServerSideProps:()=>g,getStaticPaths:()=>m,getStaticProps:()=>x,reportWebVitals:()=>q,routeModule:()=>y,unstable_getServerProps:()=>A,unstable_getServerSideProps:()=>b,unstable_getStaticParams:()=>f,unstable_getStaticPaths:()=>S,unstable_getStaticProps:()=>P});var a=r(63885),i=r(80237),o=r(81413),n=r(9616),u=r.n(n),l=r(72386),p=r(74748),d=e([l,p]);[l,p]=d.then?(await d)():d;let c=(0,o.M)(p,"default"),x=(0,o.M)(p,"getStaticProps"),m=(0,o.M)(p,"getStaticPaths"),g=(0,o.M)(p,"getServerSideProps"),h=(0,o.M)(p,"config"),q=(0,o.M)(p,"reportWebVitals"),P=(0,o.M)(p,"unstable_getStaticProps"),S=(0,o.M)(p,"unstable_getStaticPaths"),f=(0,o.M)(p,"unstable_getStaticParams"),A=(0,o.M)(p,"unstable_getServerProps"),b=(0,o.M)(p,"unstable_getServerSideProps"),y=new a.PagesRouteModule({definition:{kind:i.A.PAGES,page:"/adminsettings/user/userTable",pathname:"/adminsettings/user/userTable",bundlePath:"",filename:""},components:{App:l.default,Document:u()},userland:p});s()}catch(e){s(e)}})},50009:e=>{e.exports=require("@restart/ui/DropdownMenu")},51538:(e,t,r)=>{r.a(e,async(e,s)=>{try{r.r(t),r.d(t,{default:()=>x});var a=r(8732),i=r(7082),o=r(83551),n=r(49481),u=r(99800),l=r(82015),p=r(88751),d=r(63487),c=e([u,d]);[u,d]=c.then?(await c)():c;let x=({filterText:e,onFilter:t,onHandleSearch:r,onClear:s,onKeyPress:c})=>{let{t:x}=(0,p.useTranslation)("common"),[m,g]=(0,l.useState)([]),[,h]=(0,l.useState)(!1),q={sort:{created_at:"desc"},limit:"~",page:1,query:{},select:"-country -dial_code -firstname -region -institution.address -institution.contact_name -institution.description -institution.dial_code -institution.document -institution.doc_src -institution.images -institution.images_src -institution.expertise -institution.hazards -institution.hazard_types -institution.header -institution.networks -institution.partners -institution.telephone -institution.twitter -institution.type -institution.unit -institution.user -mobile_number -position"},P=async()=>{h(!0);let e=await d.A.get("/users",q);if(e&&Array.isArray(e.data)){let t=e.data.map((e,t)=>({label:e.username,value:e._id}));g(t),h(!1)}};return(0,l.useEffect)(()=>{P()},[]),(0,a.jsx)(i.A,{fluid:!0,className:"p-0",children:(0,a.jsx)(o.A,{children:(0,a.jsx)(n.A,{xs:6,md:4,className:"p-0",children:(0,a.jsx)(u.default,{autoFocus:!0,isClearable:!0,isSearchable:!0,onKeyDown:c,onChange:t,placeholder:x("adminsetting.user.table.Usernameoremail"),options:m})})})})};s()}catch(e){s(e)}})},56084:(e,t,r)=>{r.d(t,{A:()=>l});var s=r(8732);r(82015);var a=r(38609),i=r.n(a),o=r(88751),n=r(30370);function u(e){let{t}=(0,o.useTranslation)("common"),r={rowsPerPageText:t("Rowsperpage")},{columns:a,data:u,totalRows:l,resetPaginationToggle:p,subheader:d,subHeaderComponent:c,handlePerRowsChange:x,handlePageChange:m,rowsPerPage:g,defaultRowsPerPage:h,selectableRows:q,loading:P,pagServer:S,onSelectedRowsChange:f,clearSelectedRows:A,sortServer:b,onSort:y,persistTableHead:E,sortFunction:v,...w}=e,_={paginationComponentOptions:r,noDataComponent:t("NoData"),noHeader:!0,columns:a,data:u||[],dense:!0,paginationResetDefaultPage:p,subHeader:d,progressPending:P,subHeaderComponent:c,pagination:!0,paginationServer:S,paginationPerPage:h||10,paginationRowsPerPageOptions:g||[10,15,20,25,30],paginationTotalRows:l,onChangeRowsPerPage:x,onChangePage:m,selectableRows:q,onSelectedRowsChange:f,clearSelectedRows:A,progressComponent:(0,s.jsx)(n.A,{}),sortIcon:(0,s.jsx)("i",{className:"sort-icon fas fa-exchange-alt"}),sortServer:b,onSort:y,sortFunction:v,persistTableHead:E,className:"rki-table"};return(0,s.jsx)(i(),{..._})}u.defaultProps={subHeader:!1,pagination:!0,totalRows:null,pagServer:!0,onSelectedRowsChange:null,clearSelectedRows:!1,sortServer:!1,persistTableHead:!1};let l=u},57664:e=>{e.exports=require("dom-helpers/scrollbarSize")},58928:e=>{e.exports=require("dom-helpers/canUseDOM")},59717:e=>{e.exports=require("@restart/ui/DropdownContext")},60560:e=>{e.exports=require("dom-helpers/contains")},63033:e=>{e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},65447:e=>{e.exports=require("@restart/ui/Button")},67364:e=>{e.exports=require("@restart/hooks/useCallbackRef")},68455:e=>{e.exports=import("redux-saga")},69722:e=>{e.exports=require("es6-promise")},74075:e=>{e.exports=require("zlib")},74716:e=>{e.exports=require("moment")},74748:(e,t,r)=>{r.a(e,async(e,s)=>{try{r.r(t),r.d(t,{default:()=>q});var a=r(8732),i=r(19918),o=r.n(i),n=r(82015),u=r.n(n),l=r(12403),p=r(91353),d=r(42893),c=r(56084),x=r(63487),m=r(51538),g=r(88751),h=e([d,x,m]);function q(e){let{t}=(0,g.useTranslation)("common"),[r,s]=(0,n.useState)([]),[i,h]=(0,n.useState)(!1),[q,P]=(0,n.useState)(0),[S,f]=(0,n.useState)(10),[A,b]=(0,n.useState)(10),[y,E]=(0,n.useState)(!1),[v,w]=(0,n.useState)({}),[_,M]=u().useState(""),[j,R]=u().useState(!1),[C,D]=(0,n.useState)({}),k={sort:{created_at:"desc"},limit:A,page:1,query:{}},U=[{name:t("adminsetting.user.table.Username"),selector:"username",cell:e=>e.username},{name:t("adminsetting.user.table.Email"),selector:"email",cell:e=>e.email},{name:t("adminsetting.user.table.Role"),selector:"role",cell:e=>e.roles?e.roles:""},{name:t("adminsetting.user.table.Organisation"),selector:"institution",cell:e=>e.institution?e.institution.title:""},{name:t("adminsetting.user.table.Action"),selector:"",cell:e=>(0,a.jsx)(a.Fragment,{children:e.isEdit?(0,a.jsxs)("div",{children:[(0,a.jsx)(o(),{href:"/adminsettings/[...routes]",as:`/adminsettings/edit_user/${e._id}`,children:(0,a.jsx)("i",{className:"icon fas fa-edit"})}),"\xa0",(0,a.jsx)("a",{onClick:()=>G(e),children:(0,a.jsx)("i",{className:"icon fas fa-trash-alt"})})]}):""})}],I=async e=>{h(!0);let t=await x.A.get("/users",e);t&&Array.isArray(t.data)&&(C.roles?.includes("SUPER_ADMIN")?t.data.map(e=>e.isEdit=!0):(t.data.filter(e=>e.roles.includes("SUPER_ADMIN")).map(e=>e.isEdit=!1),t.data.filter(e=>!e.roles.includes("SUPER_ADMIN")).map(e=>e.isEdit=!0)),s(t.data),P(t.totalCount),h(!1),T(t.data))},N=async(e,t)=>{k.limit=e,k.page=t,h(!0);let r=await x.A.get("/users",k);r&&Array.isArray(r.data)&&(C.roles?.includes("SUPER_ADMIN")?r.data.map(e=>e.isEdit=!0):(r.data.filter(e=>e.roles.includes("SUPER_ADMIN")).map(e=>e.isEdit=!1),r.data.filter(e=>!e.roles.includes("SUPER_ADMIN")).map(e=>e.isEdit=!0)),s(r.data),b(e),h(!1))},T=async e=>{let t=await x.A.post("/users/getLoggedUser",{});t&&t.username&&(D(t),console.log(e),t.roles.includes("SUPER_ADMIN")?e.map(e=>e.isEdit=!0):(e.filter(e=>e.roles.includes("SUPER_ADMIN")).map(e=>e.isEdit=!1),e.filter(e=>!e.roles.includes("SUPER_ADMIN")).map(e=>e.isEdit=!0)),s(e),f(e))},G=async e=>{w(e._id),E(!0)},H=async()=>{try{await x.A.remove(`/users/${v}`),I(k),E(!1),d.default.success(t("adminsetting.user.table.userDeletedSuccessfully"))}catch(e){d.default.error(t("adminsetting.user.table.errorDeletingUser"))}},O=()=>E(!1),z=u().useMemo(()=>{let e=e=>{e&&(RegExp("^[^@]+@[^@]+\\.[^@]+$").test(e.toLowerCase())?k.query={email:e}:k.query={username:e}),I(k),k.query={}},t=()=>{e(_)},r=()=>{t()};return(0,a.jsx)(m.default,{onFilter:t=>{t&&t.label?(M(t.label),e(t.label)):(k.query={},M(""),I(k))},onClear:()=>{_&&(R(!j),M(""))},filterText:_,onHandleSearch:t,onKeyPress:e=>{"Enter"===e.key&&r()}})},[_]);return(0,a.jsxs)("div",{children:[(0,a.jsxs)(l.A,{show:y,onHide:O,children:[(0,a.jsx)(l.A.Header,{closeButton:!0,children:(0,a.jsx)(l.A.Title,{children:t("adminsetting.user.table.DeleteUser")})}),(0,a.jsx)(l.A.Body,{children:t("adminsetting.user.table.Areyousurewanttodeletethisuser?")}),(0,a.jsxs)(l.A.Footer,{children:[(0,a.jsx)(p.A,{variant:"secondary",onClick:O,children:t("adminsetting.user.table.Cancel")}),(0,a.jsx)(p.A,{variant:"primary",onClick:H,children:t("adminsetting.user.table.Yes")})]})]}),(0,a.jsx)(c.A,{columns:e.trim&&"actions"===e.trim?U.slice(0,-1):U,data:r,totalRows:q,loading:i,subheader:!0,pagServer:!0,resetPaginationToggle:j,subHeaderComponent:z,handlePerRowsChange:N,handlePageChange:e=>{k.limit=A,k.page=e,I(k)}})]})}[d,x,m]=h.then?(await h)():h,s()}catch(e){s(e)}})},74987:e=>{e.exports=require("@restart/ui/ModalManager")},78097:e=>{e.exports=require("next-redux-saga")},78634:e=>{e.exports=require("@restart/ui/Anchor")},80237:(e,t)=>{Object.defineProperty(t,"A",{enumerable:!0,get:function(){return r}});var r=function(e){return e.PAGES="PAGES",e.PAGES_API="PAGES_API",e.APP_PAGE="APP_PAGE",e.APP_ROUTE="APP_ROUTE",e.IMAGE="IMAGE",e}({})},81366:e=>{e.exports=require("redux-auth-wrapper/connectedAuthWrapper")},81413:(e,t)=>{Object.defineProperty(t,"M",{enumerable:!0,get:function(){return function e(t,r){return r in t?t[r]:"then"in t&&"function"==typeof t.then?t.then(t=>e(t,r)):"function"==typeof t&&"default"===r?t:void 0}}})},81521:e=>{e.exports=require("dom-helpers/querySelectorAll")},81895:e=>{e.exports=require("@restart/hooks/useEventCallback")},82015:e=>{e.exports=require("react")},86842:e=>{e.exports=require("@restart/ui/SelectableContext")},87571:e=>{e.exports=require("dom-helpers/transitionEnd")},88751:e=>{e.exports=require("next-i18next")},93787:e=>{e.exports=require("redux-persist")},94947:e=>{e.exports=require("@restart/hooks/useTimeout")},96196:e=>{e.exports=require("next-redux-wrapper")},98320:e=>{e.exports=require("@restart/hooks/useIsomorphicEffect")},99460:e=>{e.exports=require("dom-helpers/css")},99800:e=>{e.exports=import("react-select")}};var t=require("../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[6089,9216,9616,2386],()=>r(49399));module.exports=s})();