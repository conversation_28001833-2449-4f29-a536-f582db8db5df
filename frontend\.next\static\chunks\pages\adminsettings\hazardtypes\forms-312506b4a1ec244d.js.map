{"version": 3, "file": "static/chunks/pages/adminsettings/hazardtypes/forms-312506b4a1ec244d.js", "mappings": "sIAwHA,MA9GkE,OAAC,OACjEA,CAAK,UACLC,CAAQ,GA4GKC,UA3GbC,EAAc,QA2GmBD,EAAC,UA3GA,QAClCE,EAAS,GAAG,UACZC,GAAW,CAAK,CACjB,GACOC,EAAYC,CAAAA,EAAAA,EAAAA,MAAAA,CAAMA,CAAiB,MACnC,CAACC,EAAWC,EAAa,CAAGC,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,EAAC,GAG3CC,CAAAA,EAAAA,EAAAA,SAAAA,CAASA,CAAC,KACJL,EAAUM,OAAO,EAAI,GAEnB,CAACJ,GAAaF,EAAUM,IAFa,GAEN,CAACC,SAFkB,GAEJb,IAChDM,EAAUM,CAD6C,MACtC,CAACC,SAAS,CAAGb,GAAS,GAG7C,EAAG,CAACA,EAAOQ,EAAU,EAGrB,IAAMM,EAAc,KACdR,EAAUM,OAAO,EAAIX,GACvBA,EAASK,EAAUM,GADc,IACP,CAACC,SAAS,CAExC,EAGME,EAAc,CAACC,EAAiBhB,KACpC,GAAwB,aAApB,OAAOiB,SAA0B,KAGnCX,EAFAW,SAASF,WAAW,CAACC,GAAS,EAAOhB,GAAS,IAC9Cc,WACAR,EAAAA,EAAUM,OAAAA,GAAVN,EAAmBY,KAAK,EAC1B,CACF,CAFIZ,CAIJ,MACE,UAACa,MAAAA,CAAIC,UAAU,0BAA0BC,MAAO,CAAEC,OAAQ,gBAAiB,WAEzE,MAD8B,GAC9B,wBACE,WAACH,MAAAA,CAAIC,UAAU,UAAUC,MAAO,CAAEE,QAAS,MAAOC,aAAc,iBAAkBC,WAAY,SAAU,YACpG,UAACC,SAAAA,CACCC,KAAK,SACLC,QAAS,IAAMb,EAAY,QAC3BM,MAAO,CAAEQ,OAAQ,QAASN,QAAS,SAAU,WAE7C,UAACO,SAAAA,UAAO,QAEV,UAACJ,SAAAA,CACCC,KAAK,SACLC,QAAS,IAAMb,EAAY,UAC3BM,MAAO,CAAEQ,OAAQ,QAASN,QAAS,SAAU,WAE7C,UAACQ,KAAAA,UAAG,QAEN,UAACL,SAAAA,CACCC,KAAK,SACLC,QAAS,IAAMb,EAAY,aAC3BM,MAAO,CAAEQ,OAAQ,QAASN,QAAS,SAAU,WAE7C,UAACS,IAAAA,UAAE,QAEL,UAACN,SAAAA,CACCC,KAAK,SACLC,QAAS,IAAMb,EAAY,qBAC3BM,MAAO,CAAEQ,OAAQ,QAASN,QAAS,SAAU,WAC9C,OAGD,UAACG,SAAAA,CACCC,KAAK,SACLC,QAAS,IAAMb,EAAY,uBAC3BM,MAAO,CAAEQ,OAAQ,QAASN,QAAS,SAAU,WAC9C,OAGD,UAACG,SAAAA,CACCC,KAAK,SACLC,QAAS,KACP,IAAMK,EAAMC,OAAO,sBACfD,GAAKlB,EAAY,aAAckB,EACrC,EACAZ,MAAO,CAAEQ,OAAQ,QAASN,QAAS,SAAU,WAC9C,YAIH,UAACJ,MAAAA,CACCgB,IAAK7B,EACL8B,gBAAiB,CAAC/B,EAClBgC,QAASvB,EACTwB,QAAS,IAAM7B,GAAa,GAC5B8B,OAAQ,IAAM9B,GAAa,GAC3BY,MAAO,CACLE,QAAS,OACTiB,UAAWpC,EACXqC,UAAoB,EAATrC,EACXsC,SAAU,OACVC,QAAS,MACX,EACAC,mBAAkB,EAAuB,GAAdzC,EAC3B0C,gCAAgC,QAO5C,EC7GaC,EAAmD,IAC9D,GAAM,aAAEC,CAAW,UAAE9C,CAAQ,CAAE,CAAG+C,EAElC,MACE,UAAC9C,EAAoBA,CACnBF,MAAO+C,GAAe,GACtB9C,SAAU,GAFSC,EAEa+C,IAGtC,EAAE,iQCsKF,MArKwBD,IACpB,IAAME,EAAqB,CACvBC,MAAO,GACPC,KAAM,GACNC,YAAa,EACjB,EAgK0B,CA9JnBC,EAAYC,EAAc,CAAG7C,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAawC,GAGnDM,EAAWR,EAAMS,MAAM,EAAIT,wBAAMS,MAAM,CAAC,EAAE,EAA4BT,EAAMS,MAAM,CAAC,EAAE,CACrF,GAAEC,CAAC,CAAE,CAAGC,CAAAA,EAAAA,EAAAA,EAAAA,CAAcA,CAAC,UAEvBC,EAAe,MAAOC,EAAYC,SAGzBR,EACDA,MAINS,EACAC,EARJH,EAAMI,cAAc,GACpB,IAAMC,EAAM,CACRf,KAAK,QAAEG,EAAAA,EAAWH,KAAAA,EAAXG,KAAAA,EAAAA,EAAkBa,GAAlBb,CAAsB,GAC7BF,IAAI,QAAEE,EAAAA,EAAWF,IAAAA,EAAXE,KAAAA,EAAAA,EAAiBa,GAAjBb,CAAqB,GAC3BD,YAAaC,EAAWD,WAC5B,EAIIG,GACAQ,EAAW,KADD,oCAEVD,EAAW,MAAMK,EAAAA,CAAUA,CAACC,KAAK,CAAC,eAA+B,OAAhBrB,EAAMS,MAAM,CAAC,EAAE,EAAIS,KAEpEF,EAAW,mCACXD,EAAW,MAAMK,EAAAA,CAAUA,CAACE,IAAI,CAAC,cAAeJ,IAEhDH,GAAYA,EAASQ,GAAG,EAAE,EAC1BC,EAAKA,CAACC,OAAO,CAACf,EAAEM,IAChBU,IAAAA,IAAW,CAAC,+BAERX,OAAAA,EAAAA,KAAAA,EAAAA,EAAUY,SAAAA,CAAVZ,GAAwB,KACxBS,EAD+B,EAC1BA,CAACI,KAAK,CAAClB,EAAE,yBAEdc,EAAAA,EAAKA,CAACI,KAAK,CAACb,EAGxB,EAOMc,EAAe,IACjB,GAAIC,EAAEC,MAAM,CAAE,CACV,GAAM,MAAEC,CAAI,OAAEhF,CAAK,CAAE,CAAG8E,EAAEC,MAAM,CAChCxB,EAAc,GAAgB,EAC1B,GAAG0B,CAAS,CACZ,CAACD,CAFyB,CAEpB,CAAEhF,CACZ,GACJ,CACJ,EAEMkF,EAAoB,IACtB3B,EAAc,GAAgB,EAC1B,GAAG0B,CAAS,CACZ5B,EAF0B,UAEbrD,EACjB,EACJ,EAEAW,CAAAA,EAAAA,EAAAA,SAAAA,CAASA,CAAC,KACN,IAAMwE,EAAmB,CACrBC,MAAO,CAAC,EACRC,KAAM,CAAElC,MAAO,KAAM,EACrBmC,MAAO,GACX,EAEI9B,GAKA+B,OALU,IAEN,IAAMxB,EAAuB,MAAMK,EAAAA,CAAUA,CAACoB,GAAG,CAAC,eAA+B,OAAhBxC,EAAMS,MAAM,CAAC,EAAE,EAAI0B,GACpF5B,EAAc,GAAgB,EAAE,GAAG0B,CAAS,CAAE,EAAhB,CAAmBlB,CAAQ,CAAC,EAC9D,IAGR,EAAG,EAAE,EAEL,IAAM0B,EAAUlF,CAAAA,EAAAA,EAAAA,MAAAA,CAAMA,CAAC,MAEvB,MACI,UAACmF,EAAAA,CAASA,CAAAA,CAACtE,UAAU,WAAWuE,KAAK,aACjC,UAACC,EAAAA,CAAIA,CAAAA,CACDvE,MAAO,CACHwE,UAAW,MACXC,UAAW,kEACf,WAEA,UAACC,EAAAA,CAAqBA,CAAAA,CAACC,SAAUpC,EAAczB,IAAKsD,EAASQ,cAAe3C,EAAY4C,oBAAoB,WACxG,WAACN,EAAAA,CAAIA,CAACO,IAAI,YACN,UAACC,EAAAA,CAAGA,CAAAA,UACA,UAACC,EAAAA,CAAGA,CAAAA,UACA,UAACT,EAAAA,CAAIA,CAACU,KAAK,WAAE9C,EAAWE,EAAE,2CAA6CA,EAAE,gDAGjF,UAAC6C,KAAAA,CAAAA,GACD,WAACH,EAAAA,CAAGA,CAAAA,CAAChF,UAAU,iBACX,UAACiF,EAAAA,CAAGA,CAAAA,CAACG,EAAE,IAACC,GAAI,EAAGC,GAAI,YACf,WAACC,EAAAA,CAAIA,CAACC,KAAK,YACP,UAACD,EAAAA,CAAIA,CAACE,KAAK,EAACzF,UAAU,0BACjBsC,EAAE,6CAEP,UAACoD,EAAAA,EAASA,CAAAA,CACN9B,KAAK,QACL+B,GAAG,QACHC,QAAQ,IACRhH,MAAOsD,EAAWH,KAAK,CACvB8D,aAAc,CACVC,UAAWxD,EAAE,+BAA+B,EAChDzD,SAAU4E,SAItB,UAACwB,EAAAA,CAAGA,CAAAA,CAACG,EAAE,IAACC,GAAI,EAAGC,GAAI,YACf,WAACC,EAAAA,CAAIA,CAACC,KAAK,YACP,UAACD,EAAAA,CAAIA,CAACE,KAAK,EAACzF,UAAU,0BACjBsC,EAAE,mCAEP,UAACoD,EAAAA,EAASA,CAAAA,CACN9B,KAAK,OACL+B,GAAG,OACHC,QAAQ,IACRhH,MAAOsD,EAAWF,IAAI,CACtB8D,UAAW,GAAkD,KAA/BC,OAAOnH,GAAS,IAAImE,IAAI,GACtD8C,aAAc,CACVC,UAAWxD,EAAE,kCACjB,EACAzD,SAAU4E,YAK1B,UAACuB,EAAAA,CAAGA,CAAAA,UACA,UAACC,EAAAA,CAAGA,CAAAA,UACA,WAACM,EAAAA,CAAIA,CAACC,KAAK,YACP,UAACD,EAAAA,CAAIA,CAACE,KAAK,WAAEnD,EAAE,iBACf,UAACZ,EAAAA,CAAeA,CAAAA,CAACC,YAAaO,EAAWD,WAAW,CAAEpD,SAAU,GAAciF,EAAkBkC,YAI5G,UAAChB,EAAAA,CAAGA,CAAAA,CAAChF,UAAU,gBACX,WAACiF,EAAAA,CAAGA,CAAAA,WACA,UAACgB,EAAAA,CAAMA,CAAAA,CAACjG,UAAU,OAAOO,KAAK,SAAS2F,QAAQ,mBAC1C5D,EAAE,YAEP,UAAC2D,EAAAA,CAAMA,CAAAA,CAACjG,UAAU,OAAOQ,QAzGhC,CAyGyC2F,IAxG1DhE,EAAcL,GAEdsE,OAAOC,QAAQ,CAAC,EAAG,EACvB,EAqG4EH,QAAQ,gBACnD5D,EAAE,WAEP,UAACgE,IAAIA,CACDC,KAAK,6BACLC,GAAK,OAFJF,+BAID,UAACL,EAAAA,CAAMA,CAAAA,CAACC,QAAQ,qBAAa5D,EAAE,2BASnE,0GChLA,IAAMmE,EAAwBC,EAAAA,OAAb,GAA6B,CAAC,GAK5C3F,IALyB,IAAoB,WAC9Cf,CAAS,UACT2G,CAAQ,CACRH,GAAII,EAAY,KAAK,CACrB,GAAGhF,EACJ,GAEC,OADA+E,EAAWE,CAAAA,EAAAA,EAAAA,EAAAA,CAAkBA,CAACF,EAAU,aACpBG,CAAAA,EAAAA,EAAAA,GAAAA,CAAIA,CAACF,EAAW,CAClC7F,IAAKA,EACLf,UAAW+G,IAAW/G,EAAW2G,GACjC,GAAG/E,CACL,EACF,GACA6E,EAASO,WAAW,CAAG,WCbvB,IAAMC,EAA0BP,EAAAA,SAAb,CAA6B,CAAC,GAK9C3F,MAL2B,EAAoB,WAChDf,CAAS,CACT2G,UAAQ,CACRH,GAAII,EAAY,KAAK,CACrB,GAAGhF,EACJ,GAEC,OADA+E,EAAWE,CAAAA,EAAAA,EAAAA,EAAAA,CAAkBA,CACX,EADsB,eACpBC,CAAAA,EAAAA,EAAAA,GAAAA,CAAIA,CAACF,EAAW,CAClC7F,IAAKA,EACLf,UAAW+G,IAAW/G,EAAW2G,GACjC,GAAG/E,CAAK,EAEZ,GACAqF,EAJyBF,WAIH,CAAG,4BCXzB,IAAMG,EAA0BR,EAAAA,SAAb,CAA6B,CAAC,GAM9C3F,MAN2B,EAAoB,CAChD4F,UAAQ,WACR3G,CAAS,CAETwG,CADA,EACII,EAAY,KAAK,CACrB,GAAGhF,EACJ,GACOuF,EAASN,CAAAA,EAAAA,EAAAA,EAAAA,CAAkBA,CAACF,EAAU,eACtCS,EAAeC,CAAAA,EAAAA,EAAAA,OAAAA,CAAOA,CAAC,IAAO,EAClCC,mBAAoBH,EACtB,EAAI,CAACA,EAAO,EACZ,MAAoBL,CAAAA,EAAAA,EAAAA,GAAAA,CAAIA,CAACS,EAAAA,CAAiBA,CAACC,QAAQ,CAAE,CACnD5I,MAAOwI,EACPK,SAAuBX,CAAAA,EAAAA,EAAAA,GAAAA,CAAIA,CAACF,EAAW,CACrC7F,IAAKA,EACL,GAAGa,CAAK,CACR5B,UAAW+G,IAAW/G,EAAWmH,EACnC,EACF,EACF,GACAD,EAAWF,GAJgBD,QAIL,CAAG,aCtBzB,IAAMW,EAAuBhB,EAAAA,MAAb,IAA6B,CAC7C,CACA,EAMG3F,GARwB,KAE1B,UACC4F,CAAQ,CACR3G,WAAS,SACTkG,CAAO,CACPM,GAAII,EAAY,KAAK,CACrB,GAAGhF,EACJ,GACOuF,EAASN,CAAAA,EAAAA,EAAAA,EAAAA,CAAkBA,CAACF,EAAU,YAC5C,MAAoBG,CAAb,EAAaA,EAAAA,GAAAA,CAAIA,CAACF,EAAP,CAChB7F,IAAKA,EACLf,UAAW+G,IAAWb,EAAU,GAAaA,MAAAA,CAAViB,EAAO,EAArBJ,GAAgC,OAARb,CAX0G,EAW9FiB,EAAQnH,GACjE,GAAG4B,CACL,EACF,GACA8F,EAAQV,WAAW,CAAG,UChBtB,IAAMW,EAA8BjB,EAAAA,UAAgB,CAAC,EAA9B,CAKpB3F,QALmD,EAApB,SAChCf,CAAS,UACT2G,CAAQ,CACRH,GAAII,EAAY,KAAK,CACrB,GAAGhF,EACJ,GAEC,OAAO,EADIiF,CAAAA,EAAAA,EAAAA,EAAAA,CAAkBA,CAACF,EAAU,oBACpBG,CAAAA,EAAAA,EAAAA,GAAAA,CAAIA,CAACF,EAAW,CAClC7F,IAAKA,EACLf,UAAW+G,IAAW/G,EAAW2G,GACjC,GAAG/E,CAAK,EAEZ,GACA+F,EAJyBZ,WAIC,CAAG,iBCb7B,IAAMa,EAAwBlB,EAAAA,OAAb,GAA6B,CAAC,GAK5C3F,IALyB,IAAoB,WAC9Cf,CAAS,UACT2G,CAAQ,CACRH,GAAII,EAAY,GAAG,CACnB,GAAGhF,EACJ,GAEC,OADA+E,EAAWE,CAAAA,EAAAA,EAAAA,EAAAA,CAAkBA,CAACF,EAAU,aACpBG,CAAAA,EAAAA,EAAAA,GAAAA,CAAIA,CAACF,EAAW,CAClC7F,IAAKA,EACLf,UAAW+G,IAAW/G,EAAW2G,GACjC,GAAG/E,CAAK,EAEZ,GACAgG,EAASZ,WAAW,CAAG,0BCZvB,IAAMa,EAAgBC,CAAAA,EAAAA,EAAAA,CAAAA,CAAgBA,CAAC,MACjCC,EAA4BrB,EAAAA,UAAgB,CAA7B,CAA8B,EAKhD3F,QAL6B,WAC9Bf,CAAS,UACT2G,CAAQ,CACRH,GAAII,EAAYiB,CAAa,CAC7B,GAAGjG,EACJ,GAEC,OADA+E,EAAWE,CAAAA,EAAAA,EAAAA,EAAAA,CAAkBA,CAACF,EAAU,iBACpBG,CAAAA,EAAAA,EAAAA,GAAAA,CAAIA,CAACF,EAAW,CAClC7F,IAAKA,EACLf,UAAW+G,IAAW/G,EAAW2G,GACjC,GAAG/E,CAAK,EAEZ,GACAmG,EAJyBhB,WAID,CAAG,eCf3B,IAAMiB,EAAwBtB,EAAAA,OAAb,GAA6B,CAAC,GAK5C3F,IALyB,IAAoB,WAC9Cf,CAAS,UACT2G,CAAQ,CACRH,GAAII,EAAY,GAAG,CACnB,GAAGhF,EACJ,GAEC,OADA+E,EAAWE,CAAAA,EAAAA,EAAAA,EAAAA,CAAkBA,CAACF,EAAU,aACpBG,CAAAA,EAAAA,EAAAA,GAAAA,CAAIA,CAACF,EAAW,CAClC7F,IAAKA,EACLf,UAAW+G,IAAW/G,EAAW2G,GACjC,GAAG/E,CAAK,EAEZ,GACAoG,EAJyBjB,WAIL,CAAG,WCZvB,IAAMkB,EAAgBH,CAAAA,EAAAA,EAAAA,CAAAA,CAAgBA,CAAC,MACjCI,EAAyBxB,EAAAA,QAAb,EAA6B,CAAC,GAK7C3F,KAL0B,GAAoB,CAC/Cf,WAAS,UACT2G,CAAQ,CACRH,GAAII,EAAYqB,CAAa,CAC7B,GAAGrG,EACJ,GAEC,OADA+E,EAAWE,CAAAA,EAAAA,EAAAA,EAAAA,CAAkBA,CAACF,EAAU,cACpBG,CAAAA,EAAAA,EAAAA,GAAAA,CAAIA,CAACF,EAAW,CAClC7F,IAAKA,EACLf,UAAW+G,IAAW/G,EAAW2G,GACjC,GAAG/E,CAAK,EAEZ,GACAsG,EAJyBnB,WAIJ,CAAG,YCNxB,IAAMvC,EAAoBkC,EAAAA,GAAb,OAA6B,CAAC,GAAnB,QAAoB,CAC1CC,UAAQ,WACR3G,CAAS,IACTmI,CAAE,MACFC,CAAI,QACJlI,CAAM,MACNmI,GAAO,CAAK,UACZZ,CAAQ,CAERjB,CADA,EACII,EAAY,KAAK,CACrB,GAAGhF,EACJ,GACOuF,EAASN,CAAAA,EAAAA,EAAAA,EAAAA,CAAkBA,CAACF,EAAU,QAC5C,MAAoBG,CAAb,EAAaA,EAAAA,GAAAA,CAAIA,CAACF,EAAP,CAChB7F,IAAKA,EACL,GAAGa,CAAK,CACR5B,UAAW+G,IAAW/G,EAAWmH,EAAQgB,GAAM,MAAS,GAAnCpB,GAAmC,CAAHoB,GAAMC,GAAQ,QAAa,OAALA,GAAQlI,GAAU,UAAiB,OAAPA,IACvGuH,IATyJ,KAS/IY,EAAoBvB,CAAAA,EAAAA,EAAb,GAAaA,CAAIA,CAACL,EAAU,CAC3CgB,GAD0B,MAAehB,CAE3C,GAAKgB,CACP,EACF,GACAjD,EAAKwC,WAAW,CAAG,OACnB,MAAesB,OAAOC,MAAM,CAAC/D,EAAM,CACjCgE,INhBad,CMgBRA,CACLxC,KNjBoBwC,CKDPQ,CCkBNA,CACPO,EAFYf,KDjBUQ,EAAC,CCmBbH,CACVhD,CAFgBmD,ITpBHzB,CSsBPA,CACNH,GHrByByB,EAAC,CNFLtB,CSwBrBiC,CTxBsB,GSsBRjC,CFtBDuB,CFAQJ,CIyBrBe,CJzBsB,GIuBRf,EFvBOI,CLSRd,CKTS,CE0BtB0B,EAFcZ,KRxBDf,CCSUC,COkBvB2B,CPlBwB,GOgBN3B,IRzBKD,EAAC,CGAXU,CK2BDA,CADMV,CAElB,EAAC,SL5B0BU,EAAC,GK2BFA,0ICwCrB,IAAMmB,EAAQ,CACnBC,WA1C4C,OAAC,MAC7CnF,CAAI,eACJoF,CAAa,UACbnK,CAAQ,cACRgH,CAAY,CACZ4B,UAAQ,CACT,GACO,CAAEwB,QAAM,SAAEC,CAAO,CAAE,CAAGC,CAAAA,EAAAA,EAAAA,EAAAA,CAAgBA,GACtCC,EAAWF,CAAO,CAACtF,EAAK,EAAIqF,CAAM,CAACrF,EAAK,CAGzB8C,EAAAA,OAAa,CAAC,IAAO,EAAE9C,MAAK,GAAI,CAACA,EAAK,EAG3D,IAAMyF,EAAoB3C,EAAAA,QAAc,CAAC4C,GAAG,CAAC7B,EAAU,GACrD,EAAIf,cAAoB,CAAC6C,IAxC7B,IAwCqC,KAxC5BC,CAAmB,EAC1B,MAAwB,UAAjB,OAAO5H,GAAgC,OAAVA,CACtC,EAwCmB2H,EAAM3H,KAAK,EACf8E,CADkB,CAClBA,YAAkB,CAAC6C,EAA6C,MACrE3F,EACA,GAAG2F,EAAM3H,KACX,GAGG2H,GAGT,MACE,WAACxJ,MAAAA,WACC,UAACA,MAAAA,CAAIC,UAAU,uBACZqJ,IAEFD,GACC,UAACrJ,MAAAA,CAAIC,UAAU,oCACZ6F,GAAiB,kBAAOoD,CAAM,CAACrF,EAAK,CAAgBqF,CAAM,CAACrF,EAAK,CAAGmC,OAAOkD,CAAM,CAACrF,GAAK,MAKjG,EAIE6F,UAhE0C,OAAC,CAAE9D,IAAE,CAAE+D,OAAK,OAAE9K,CAAK,MAAEgF,CAAI,UAAE3E,CAAQ,CAAE,GACzE,QAAEyD,CAAM,eAAEiH,CAAa,CAAE,CAAGR,CAAAA,EAAAA,EAAAA,EAAAA,CAAgBA,GAC5CS,EAAYhG,GAAQ+B,EAE1B,MACE,UAACJ,EAAAA,CAAIA,CAACsE,KAAK,EACTtJ,KAAK,QACLoF,GAAIA,EACJ+D,MAAOA,EACP9K,MAAOA,EACPgF,KAAMgG,EACNE,QAASpH,CAAM,CAACkH,EAAU,GAAKhL,EAC/BC,SAAU,IACR8K,EAAcC,EAAWlG,EAAEC,MAAM,CAAC/E,KAAK,CACzC,EACAK,SAAUA,EACV8K,MAAM,KAGZ,CA8CA,EAAE,ECzEcC,CAAAA,CACLtE,EAAAA,EAAAA,CACEuE,EAAAA,EAAAA,gGCeb,IAAMtF,EAAwBuF,CAAAA,EAAAA,EAAAA,UAAAA,CAAUA,CAA8C,CAACtI,EAAOb,KAC5F,GAAM,UAAE0G,CAAQ,UAAE7C,CAAQ,cAAEuF,CAAY,WAAEnK,CAAS,YAAEoK,CAAU,eAAEvF,CAAa,CAAE,GAAGwF,EAAM,CAAGzI,EAGtF0I,EAAmBC,EAAAA,EAAU,GAAGC,KAAK,CAAC,CAAC,GAE7C,MACE,UAACC,EAAAA,EAAMA,CAAAA,CACL5F,cAAeA,GAAiB,CAAC,EACjCyF,iBAAkBA,EAClB1F,SAAU,CAAClC,EAA6BgI,KAEtC,IAAMC,EAAuB,CAC3B9H,eAAgB,KAAO,EACvB+H,gBAAiB,KAAO,EACxBC,cAAe,KACflH,OAAQ,KACRmH,YAAa,IAAIC,MAAM,UACvBC,SAAS,EACTC,YAAY,EACZC,iBAAkB,GAClBC,WAAY,EACZC,WAAW,EACXC,UAAWC,KAAKC,GAAG,GACnBhL,KAAM,SACNiL,mBAAoB,KAAM,EAC1BC,qBAAsB,KAAM,EAC5BC,QAAS,KAAO,CAClB,EAEI9G,GAEFA,EAAS+F,EAAWjI,EAAQgI,CAFhB,CAIhB,EACC,GAAGL,CAAI,UAEP,GACC,UAAC9E,EAAAA,EAAIA,CAAAA,CACHxE,IAAKA,EACL6D,SAAU+G,EAAYnJ,YAAY,CAClC2H,aAAcA,EACdnK,UAAWA,EACXoK,WAAYA,WAES,YAApB,OAAO3C,EAA0BA,EAASkE,GAAelE,KAKpE,GAEA9C,EAAsBqC,WAAW,CAAG,wBAEpC,MAAerC,qBAAqBA,EAAC,sFClF9B,IAAMe,EAAY,OAAC,MACxB9B,CAAI,IACJ+B,CAAE,CACFC,UAAQ,WACRE,CAAS,cACTD,CAAY,UACZhH,CAAQ,OACRD,CAAK,IACL4H,CAAE,WACFoF,CAAS,MACTC,CAAI,SACJC,CAAO,CACP,GAAGlK,EACC,GAuBJ,MACE,UAACmK,EAAAA,EAAKA,CAAAA,CAACnI,KAAMA,EAAMoI,SAtBJ,CAsBcA,GApB7B,IAAMC,EAA2B,UAAf,OAAOC,EAAmBA,EAAMnG,OAAOmG,GAAO,WAChE,GAAiB,EAACA,GAA4B,IAA7B,CAAQD,EAAUlJ,IAAI,EAAO,CAAC,CACtC8C,EAD0C,KAC1CA,EAAAA,KAAAA,EAAAA,EAAcC,SAAAA,GAAa,EAA3BD,uBAGLC,GAAa,CAACA,EAAUoG,GACnBrG,GADyB,IACzBA,EAAAA,KAAAA,EAAAA,EAAcC,SAAAA,GAAa,EAA3BD,cAGLiG,GAAWI,GAET,CADU,CADI,GACAC,OAAOL,GACdM,IAAI,CAACF,GACPrG,GADa,IACbA,EAAAA,KAAAA,EAAAA,EAAciG,OAAAA,GAAW,IAAzBjG,mBAKb,WAIK,OAAC,OAAEwG,CAAK,MAAEC,CAAI,CAAO,SACpB,iCACE,UAAC/G,EAAAA,CAAIA,CAACgH,OAAO,EACV,GAAGF,CAAK,CACR,GAAGzK,CAAK,CACT+D,GAAIA,EACJa,GAAIA,GAAM,QACVqF,KAAMA,EACNW,UAAWF,EAAKpD,OAAO,EAAI,CAAC,CAACoD,EAAK9I,KAAK,CACvC3E,SAAU,IACRwN,EAAMxN,QAAQ,CAAC6E,GACX7E,GAAUA,EAAS6E,EACzB,EACA9E,MAAOA,KAAU6N,MAAY7N,EAAQyN,EAAMzN,KAAK,GAEjD0N,EAAKpD,OAAO,EAAIoD,EAAK9I,KAAK,CACzB,UAAC+B,EAAAA,CAAIA,CAACgH,OAAO,CAACG,QAAQ,EAACnM,KAAK,mBACzB+L,EAAK9I,KAAK,GAEX,UAKd,EAAE,EAIyB,OAAC,MAC1BI,CAAI,IACJ+B,CAAE,CACFC,UAAQ,cACRC,CAAY,UACZhH,CAAQ,OACRD,CAAK,UACL6I,CAAQ,CACR,GAAG7F,EACC,GAUJ,MACE,UAACmK,EAAAA,EAAKA,CAAAA,CAACnI,KAAMA,EAAMoI,SATJ,CAScA,GAR7B,GAAIpG,GAAa,EAACsG,GAAe,IAAhB,CAAQA,CAAQ,CAAC,CAChC,EADoC,IAC7BrG,OAAAA,EAAAA,KAAAA,EAAAA,EAAcC,SAAAA,GAAa,EAA3BD,sBAIX,WAIK,OAAC,OAAEwG,CAAK,MAAEC,CAAI,CAAO,SACpB,iCACE,UAAC/G,EAAAA,CAAIA,CAACgH,OAAO,EACX/F,GAAG,SACF,GAAG6F,CAAK,CACR,GAAGzK,CAAK,CACT+D,GAAIA,EACJ6G,UAAWF,EAAKpD,OAAO,EAAI,CAAC,CAACoD,EAAK9I,KAAK,CACvC3E,SAAU,IACRwN,EAAMxN,QAAQ,CAAC6E,GACX7E,GAAUA,EAAS6E,EACzB,EACA9E,WAAiB6N,IAAV7N,EAAsBA,EAAQyN,EAAMzN,KAAK,UAE/C6I,IAEF6E,EAAKpD,OAAO,EAAIoD,EAAK9I,KAAK,CACzB,UAAC+B,EAAAA,CAAIA,CAACgH,OAAO,CAACG,QAAQ,EAACnM,KAAK,mBACzB+L,EAAK9I,KAAK,GAEX,UAKd,EAAE,+CCnHF,IAAMmJ,EAAuBjG,QAAb,CAAaA,UAAF,GAAqB,CAAC,MACjDiG,EAAQ3F,WAAW,CAAG,oBACtB,MAAe2F,OAAOA,EAAC,UCJvB,4CACA,mCACA,WACA,OAAe,EAAQ,KAAwD,CAC/E,EACA,SAFsB", "sources": ["webpack://_N_E/./components/common/SimpleRichTextEditor.tsx", "webpack://_N_E/./shared/quill-editor/quill-editor.component.tsx", "webpack://_N_E/./pages/adminsettings/hazardtypes/forms.tsx", "webpack://_N_E/./node_modules/react-bootstrap/esm/CardBody.js", "webpack://_N_E/./node_modules/react-bootstrap/esm/CardFooter.js", "webpack://_N_E/./node_modules/react-bootstrap/esm/CardHeader.js", "webpack://_N_E/./node_modules/react-bootstrap/esm/CardImg.js", "webpack://_N_E/./node_modules/react-bootstrap/esm/CardImgOverlay.js", "webpack://_N_E/./node_modules/react-bootstrap/esm/CardLink.js", "webpack://_N_E/./node_modules/react-bootstrap/esm/CardSubtitle.js", "webpack://_N_E/./node_modules/react-bootstrap/esm/CardText.js", "webpack://_N_E/./node_modules/react-bootstrap/esm/CardTitle.js", "webpack://_N_E/./node_modules/react-bootstrap/esm/Card.js", "webpack://_N_E/./components/common/FormikRadio.tsx", "webpack://_N_E/./components/common/FormValidation.tsx", "webpack://_N_E/./components/common/ValidationFormWrapper.tsx", "webpack://_N_E/./components/common/FormikTextInput.tsx", "webpack://_N_E/./node_modules/react-bootstrap/esm/CardHeaderContext.js", "webpack://_N_E/?a004"], "sourcesContent": ["import React, { useRef, useEffect, useState } from 'react';\r\n\r\ninterface SimpleRichTextEditorProps {\r\n  value: string;\r\n  onChange: (content: string) => void;\r\n  placeholder?: string;\r\n  height?: number;\r\n  disabled?: boolean;\r\n}\r\n\r\nconst SimpleRichTextEditor: React.FC<SimpleRichTextEditorProps> = ({\r\n  value,\r\n  onChange,\r\n  placeholder = 'Write something...',\r\n  height = 300,\r\n  disabled = false,\r\n}) => {\r\n  const editorRef = useRef<HTMLDivElement>(null);\r\n  const [isFocused, setIsFocused] = useState(false);\r\n\r\n  // Initialize editor with HTML content\r\n  useEffect(() => {\r\n    if (editorRef.current && typeof window !== 'undefined') {\r\n      // Only update if the editor doesn't have focus to prevent cursor jumping\r\n      if (!isFocused && editorRef.current.innerHTML !== value) {\r\n        editorRef.current.innerHTML = value || '';\r\n      }\r\n    }\r\n  }, [value, isFocused]);\r\n\r\n  // Handle content changes\r\n  const handleInput = () => {\r\n    if (editorRef.current && onChange) {\r\n      onChange(editorRef.current.innerHTML);\r\n    }\r\n  };\r\n\r\n  // Simple toolbar buttons\r\n  const execCommand = (command: string, value?: string) => {\r\n    if (typeof document !== 'undefined') {\r\n      document.execCommand(command, false, value || '');\r\n      handleInput();\r\n      editorRef.current?.focus();\r\n    }\r\n  };\r\n\r\n  return (\r\n    <div className=\"simple-rich-text-editor\" style={{ border: '1px solid #ccc' }}>\r\n      {typeof window !== 'undefined' && (\r\n      <>\r\n        <div className=\"toolbar\" style={{ padding: '8px', borderBottom: '1px solid #ccc', background: '#f5f5f5' }}>\r\n            <button\r\n              type=\"button\"\r\n              onClick={() => execCommand('bold')}\r\n              style={{ margin: '0 5px', padding: '3px 8px' }}\r\n            >\r\n              <strong>B</strong>\r\n            </button>\r\n            <button\r\n              type=\"button\"\r\n              onClick={() => execCommand('italic')}\r\n              style={{ margin: '0 5px', padding: '3px 8px' }}\r\n            >\r\n              <em>I</em>\r\n            </button>\r\n            <button\r\n              type=\"button\"\r\n              onClick={() => execCommand('underline')}\r\n              style={{ margin: '0 5px', padding: '3px 8px' }}\r\n            >\r\n              <u>U</u>\r\n            </button>\r\n            <button\r\n              type=\"button\"\r\n              onClick={() => execCommand('insertOrderedList')}\r\n              style={{ margin: '0 5px', padding: '3px 8px' }}\r\n            >\r\n              OL\r\n            </button>\r\n            <button\r\n              type=\"button\"\r\n              onClick={() => execCommand('insertUnorderedList')}\r\n              style={{ margin: '0 5px', padding: '3px 8px' }}\r\n            >\r\n              UL\r\n            </button>\r\n            <button\r\n              type=\"button\"\r\n              onClick={() => {\r\n                const url = prompt('Enter the link URL');\r\n                if (url) execCommand('createLink', url);\r\n              }}\r\n              style={{ margin: '0 5px', padding: '3px 8px' }}\r\n            >\r\n              Link\r\n            </button>\r\n          </div>\r\n          <div\r\n            ref={editorRef}\r\n            contentEditable={!disabled}\r\n            onInput={handleInput}\r\n            onFocus={() => setIsFocused(true)}\r\n            onBlur={() => setIsFocused(false)}\r\n            style={{\r\n              padding: '15px',\r\n              minHeight: height,\r\n              maxHeight: height * 2,\r\n              overflow: 'auto',\r\n              outline: 'none',\r\n            }}\r\n            data-placeholder={!value ? placeholder : ''}\r\n            suppressContentEditableWarning={true}\r\n          >\r\n          </div>\r\n      </>\r\n      )}\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default SimpleRichTextEditor;\r\n", "// React Imports\r\nimport React from \"react\";\r\nimport SimpleRichTextEditor from \"../../components/common/SimpleRichTextEditor\";\r\n\r\ninterface IEditorComponentProps {\r\n  initContent: string | undefined;\r\n  onChange: Function;\r\n}\r\n\r\nexport const EditorComponent: React.FC<IEditorComponentProps> = (props) => {\r\n  const { initContent, onChange } = props;\r\n\r\n  return (\r\n    <SimpleRichTextEditor\r\n      value={initContent || \"\"}\r\n      onChange={(content) => onChange(content)}\r\n    />\r\n  );\r\n};\r\n", "//Import Library\r\nimport { Con<PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, Button } from \"react-bootstrap\";\r\nimport { TextInput } from \"../../../components/common/FormValidation\";\r\nimport ValidationFormWrapper from \"../../../components/common/ValidationFormWrapper\";\r\nimport { useRef, useState, useEffect } from \"react\";\r\nimport toast from 'react-hot-toast';\r\nimport Router from \"next/router\";\r\nimport Link from \"next/link\";\r\n\r\n//Import services/components\r\nimport apiService from \"../../../services/apiService\";\r\nimport { HazardType } from \"../../../types\";\r\nimport { useTranslation } from 'next-i18next';\r\nimport { EditorComponent } from \"../../../shared/quill-editor/quill-editor.component\";\r\n\r\ninterface HazardTypeFormProps {\r\n    [key: string]: any;\r\n}\r\n\r\nconst HazardTypeForm = (props: HazardTypeFormProps) => {\r\n    const _initialHazardType = {\r\n        title: \"\",\r\n        code: \"\",\r\n        description: \"\",\r\n    };\r\n\r\n    const [initialVal, setInitialVal] = useState<HazardType>(_initialHazardType);\r\n\r\n\r\n    const editform = props.routes && props.routes[0] === \"edit_hazard_types\" && props.routes[1];\r\n    const { t } = useTranslation('common');\r\n\r\n    const handleSubmit = async (event: any, values?: any) => {\r\n        event.preventDefault();\r\n        const obj = {\r\n            title: initialVal.title?.trim(),\r\n            code: initialVal.code?.trim(),\r\n            description: initialVal.description,\r\n        };\r\n\r\n        let response;\r\n        let toastMsg;\r\n        if (editform) {\r\n            toastMsg = \"adminsetting.hazardtypes.updatesuccess\";\r\n            response = await apiService.patch(`/hazardtype/${props.routes[1]}`, obj);\r\n        } else {\r\n            toastMsg = \"adminsetting.hazardtypes.success\";\r\n            response = await apiService.post(\"/hazardtype\", obj);\r\n        }\r\n        if (response && response._id) {\r\n            toast.success(t(toastMsg));\r\n            Router.push(\"/adminsettings/hazardTypes\");\r\n        } else {\r\n            if (response?.errorCode === 11000) {\r\n                toast.error(t(\"duplicatesNotAllowed\"));\r\n            } else {\r\n                toast.error(response);\r\n            }\r\n        }\r\n    };\r\n\r\n    const resetHandler = () => {\r\n        setInitialVal(_initialHazardType);\r\n        // Reset validation state (Formik handles this automatically)\r\n        window.scrollTo(0, 0);\r\n    };\r\n    const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement>) => {\r\n        if (e.target) {\r\n            const { name, value } = e.target;\r\n            setInitialVal((prevState) => ({\r\n                ...prevState,\r\n                [name]: value,\r\n            }));\r\n        }\r\n    };\r\n\r\n    const handleDescription = (value: string) => {\r\n        setInitialVal((prevState) => ({\r\n            ...prevState,\r\n            description: value,\r\n        }));\r\n    };\r\n\r\n    useEffect(() => {\r\n        const hazardTypeParams = {\r\n            query: {},\r\n            sort: { title: \"asc\" },\r\n            limit: \"~\",\r\n        };\r\n\r\n        if (editform) {\r\n            const getHazardTypeData = async () => {\r\n                const response: HazardType = await apiService.get(`/hazardtype/${props.routes[1]}`, hazardTypeParams);\r\n                setInitialVal((prevState) => ({ ...prevState, ...response }));\r\n            };\r\n            getHazardTypeData();\r\n        }\r\n    }, []);\r\n\r\n    const formRef = useRef(null);\r\n\r\n    return (\r\n        <Container className=\"formCard\" fluid>\r\n            <Card\r\n                style={{\r\n                    marginTop: \"5px\",\r\n                    boxShadow: \"0 4px 8px 0 rgba(0, 0, 0, 0.2), 0 6px 20px 0 rgba(0, 0, 0, 0.19)\",\r\n                }}\r\n            >\r\n                <ValidationFormWrapper onSubmit={handleSubmit} ref={formRef} initialValues={initialVal} enableReinitialize={true}>\r\n                    <Card.Body>\r\n                        <Row>\r\n                            <Col>\r\n                                <Card.Title>{editform ? t(\"adminsetting.hazardtypes.EditHazardType\") : t(\"adminsetting.hazardtypes.AddHazardType\")}</Card.Title>\r\n                            </Col>\r\n                        </Row>\r\n                        <hr />\r\n                        <Row className=\"mb-3\">\r\n                            <Col md lg={6} sm={12}>\r\n                                <Form.Group>\r\n                                    <Form.Label className=\"required-field\">\r\n                                        {t(\"adminsetting.hazardtypes.HazardTypeName\")}\r\n                                    </Form.Label>\r\n                                    <TextInput\r\n                                        name=\"title\"\r\n                                        id=\"title\"\r\n                                        required\r\n                                        value={initialVal.title}\r\n                                        errorMessage={{\r\n                                            validator: t(\"adminsetting.hazardtypes.Add\")}}\r\n                                        onChange={handleChange}\r\n                                    />\r\n                                </Form.Group>\r\n                            </Col>\r\n                            <Col md lg={6} sm={12}>\r\n                                <Form.Group>\r\n                                    <Form.Label className=\"required-field\">\r\n                                        {t(\"adminsetting.hazardtypes.Code\")}\r\n                                    </Form.Label>\r\n                                    <TextInput\r\n                                        name=\"code\"\r\n                                        id=\"code\"\r\n                                        required\r\n                                        value={initialVal.code}\r\n                                        validator={(value: string) => String(value || '').trim() !== \"\"}\r\n                                        errorMessage={{\r\n                                            validator: t(\"adminsetting.hazardtypes.Please\"),\r\n                                        }}\r\n                                        onChange={handleChange}\r\n                                    />\r\n                                </Form.Group>\r\n                            </Col>\r\n                        </Row>\r\n                        <Row>\r\n                            <Col>\r\n                                <Form.Group>\r\n                                    <Form.Label>{t(\"Description\")}</Form.Label>\r\n                                    <EditorComponent initContent={initialVal.description} onChange={(evt: any) => handleDescription(evt)} />\r\n                                </Form.Group>\r\n                            </Col>\r\n                        </Row>\r\n                        <Row className=\"my-4\">\r\n                            <Col>\r\n                                <Button className=\"me-2\" type=\"submit\" variant=\"primary\">\r\n                                    {t(\"submit\")}\r\n                                </Button>\r\n                                <Button className=\"me-2\" onClick={resetHandler} variant=\"info\">\r\n                                    {t(\"reset\")}\r\n                                </Button>\r\n                                <Link\r\n                                    href=\"/adminsettings/[...routes]\"\r\n                                    as={`/adminsettings/hazardTypes`}\r\n                                    >\r\n                                    <Button variant=\"secondary\">{t(\"Cancel\")}</Button>\r\n                                </Link>\r\n                            </Col>\r\n                        </Row>\r\n                    </Card.Body>\r\n                </ValidationFormWrapper>\r\n            </Card>\r\n        </Container>\r\n    );\r\n};\r\n\r\nexport default HazardTypeForm;\r\n", "\"use client\";\n\nimport * as React from 'react';\nimport classNames from 'classnames';\nimport { useBootstrapPrefix } from './ThemeProvider';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst CardBody = /*#__PURE__*/React.forwardRef(({\n  className,\n  bsPrefix,\n  as: Component = 'div',\n  ...props\n}, ref) => {\n  bsPrefix = useBootstrapPrefix(bsPrefix, 'card-body');\n  return /*#__PURE__*/_jsx(Component, {\n    ref: ref,\n    className: classNames(className, bsPrefix),\n    ...props\n  });\n});\nCardBody.displayName = 'CardBody';\nexport default CardBody;", "\"use client\";\n\nimport * as React from 'react';\nimport classNames from 'classnames';\nimport { useBootstrapPrefix } from './ThemeProvider';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst CardFooter = /*#__PURE__*/React.forwardRef(({\n  className,\n  bsPrefix,\n  as: Component = 'div',\n  ...props\n}, ref) => {\n  bsPrefix = useBootstrapPrefix(bsPrefix, 'card-footer');\n  return /*#__PURE__*/_jsx(Component, {\n    ref: ref,\n    className: classNames(className, bsPrefix),\n    ...props\n  });\n});\nCardFooter.displayName = 'CardFooter';\nexport default CardFooter;", "\"use client\";\n\nimport classNames from 'classnames';\nimport * as React from 'react';\nimport { useMemo } from 'react';\nimport { useBootstrapPrefix } from './ThemeProvider';\nimport CardHeaderContext from './CardHeaderContext';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst CardHeader = /*#__PURE__*/React.forwardRef(({\n  bsPrefix,\n  className,\n  // Need to define the default \"as\" during prop destructuring to be compatible with styled-components github.com/react-bootstrap/react-bootstrap/issues/3595\n  as: Component = 'div',\n  ...props\n}, ref) => {\n  const prefix = useBootstrapPrefix(bsPrefix, 'card-header');\n  const contextValue = useMemo(() => ({\n    cardHeaderBsPrefix: prefix\n  }), [prefix]);\n  return /*#__PURE__*/_jsx(CardHeaderContext.Provider, {\n    value: contextValue,\n    children: /*#__PURE__*/_jsx(Component, {\n      ref: ref,\n      ...props,\n      className: classNames(className, prefix)\n    })\n  });\n});\nCardHeader.displayName = 'CardHeader';\nexport default CardHeader;", "\"use client\";\n\nimport classNames from 'classnames';\nimport * as React from 'react';\nimport { useBootstrapPrefix } from './ThemeProvider';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst CardImg = /*#__PURE__*/React.forwardRef(\n// Need to define the default \"as\" during prop destructuring to be compatible with styled-components github.com/react-bootstrap/react-bootstrap/issues/3595\n({\n  bsPrefix,\n  className,\n  variant,\n  as: Component = 'img',\n  ...props\n}, ref) => {\n  const prefix = useBootstrapPrefix(bsPrefix, 'card-img');\n  return /*#__PURE__*/_jsx(Component, {\n    ref: ref,\n    className: classNames(variant ? `${prefix}-${variant}` : prefix, className),\n    ...props\n  });\n});\nCardImg.displayName = 'CardImg';\nexport default CardImg;", "\"use client\";\n\nimport * as React from 'react';\nimport classNames from 'classnames';\nimport { useBootstrapPrefix } from './ThemeProvider';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst CardImgOverlay = /*#__PURE__*/React.forwardRef(({\n  className,\n  bsPrefix,\n  as: Component = 'div',\n  ...props\n}, ref) => {\n  bsPrefix = useBootstrapPrefix(bsPrefix, 'card-img-overlay');\n  return /*#__PURE__*/_jsx(Component, {\n    ref: ref,\n    className: classNames(className, bsPrefix),\n    ...props\n  });\n});\nCardImgOverlay.displayName = 'CardImgOverlay';\nexport default CardImgOverlay;", "\"use client\";\n\nimport * as React from 'react';\nimport classNames from 'classnames';\nimport { useBootstrapPrefix } from './ThemeProvider';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst CardLink = /*#__PURE__*/React.forwardRef(({\n  className,\n  bsPrefix,\n  as: Component = 'a',\n  ...props\n}, ref) => {\n  bsPrefix = useBootstrapPrefix(bsPrefix, 'card-link');\n  return /*#__PURE__*/_jsx(Component, {\n    ref: ref,\n    className: classNames(className, bsPrefix),\n    ...props\n  });\n});\nCardLink.displayName = 'CardLink';\nexport default CardLink;", "\"use client\";\n\nimport * as React from 'react';\nimport classNames from 'classnames';\nimport { useBootstrapPrefix } from './ThemeProvider';\nimport divWithClassName from './divWithClassName';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst DivStyledAsH6 = divWithClassName('h6');\nconst CardSubtitle = /*#__PURE__*/React.forwardRef(({\n  className,\n  bsPrefix,\n  as: Component = DivStyledAsH6,\n  ...props\n}, ref) => {\n  bsPrefix = useBootstrapPrefix(bsPrefix, 'card-subtitle');\n  return /*#__PURE__*/_jsx(Component, {\n    ref: ref,\n    className: classNames(className, bsPrefix),\n    ...props\n  });\n});\nCardSubtitle.displayName = 'CardSubtitle';\nexport default CardSubtitle;", "\"use client\";\n\nimport * as React from 'react';\nimport classNames from 'classnames';\nimport { useBootstrapPrefix } from './ThemeProvider';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst CardText = /*#__PURE__*/React.forwardRef(({\n  className,\n  bsPrefix,\n  as: Component = 'p',\n  ...props\n}, ref) => {\n  bsPrefix = useBootstrapPrefix(bsPrefix, 'card-text');\n  return /*#__PURE__*/_jsx(Component, {\n    ref: ref,\n    className: classNames(className, bsPrefix),\n    ...props\n  });\n});\nCardText.displayName = 'CardText';\nexport default CardText;", "\"use client\";\n\nimport * as React from 'react';\nimport classNames from 'classnames';\nimport { useBootstrapPrefix } from './ThemeProvider';\nimport divWithClassName from './divWithClassName';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst DivStyledAsH5 = divWithClassName('h5');\nconst CardTitle = /*#__PURE__*/React.forwardRef(({\n  className,\n  bsPrefix,\n  as: Component = DivStyledAsH5,\n  ...props\n}, ref) => {\n  bsPrefix = useBootstrapPrefix(bsPrefix, 'card-title');\n  return /*#__PURE__*/_jsx(Component, {\n    ref: ref,\n    className: classNames(className, bsPrefix),\n    ...props\n  });\n});\nCardTitle.displayName = 'CardTitle';\nexport default CardTitle;", "\"use client\";\n\nimport classNames from 'classnames';\nimport * as React from 'react';\nimport { useBootstrapPrefix } from './ThemeProvider';\nimport CardBody from './CardBody';\nimport CardFooter from './CardFooter';\nimport CardHeader from './CardHeader';\nimport CardImg from './CardImg';\nimport CardImgOverlay from './CardImgOverlay';\nimport CardLink from './CardLink';\nimport CardSubtitle from './CardSubtitle';\nimport CardText from './CardText';\nimport CardTitle from './CardTitle';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst Card = /*#__PURE__*/React.forwardRef(({\n  bsPrefix,\n  className,\n  bg,\n  text,\n  border,\n  body = false,\n  children,\n  // Need to define the default \"as\" during prop destructuring to be compatible with styled-components github.com/react-bootstrap/react-bootstrap/issues/3595\n  as: Component = 'div',\n  ...props\n}, ref) => {\n  const prefix = useBootstrapPrefix(bsPrefix, 'card');\n  return /*#__PURE__*/_jsx(Component, {\n    ref: ref,\n    ...props,\n    className: classNames(className, prefix, bg && `bg-${bg}`, text && `text-${text}`, border && `border-${border}`),\n    children: body ? /*#__PURE__*/_jsx(CardBody, {\n      children: children\n    }) : children\n  });\n});\nCard.displayName = 'Card';\nexport default Object.assign(Card, {\n  Img: CardImg,\n  Title: CardTitle,\n  Subtitle: CardSubtitle,\n  Body: CardBody,\n  Link: CardLink,\n  Text: CardText,\n  Header: CardHeader,\n  Footer: CardFooter,\n  ImgOverlay: CardImgOverlay\n});", "import React from 'react';\r\nimport { useFormikContext, Field } from 'formik';\r\nimport { Form } from 'react-bootstrap';\r\n\r\ninterface RadioItemProps {\r\n  id: string;\r\n  label: string;\r\n  value: string;\r\n  name?: string;\r\n  disabled?: boolean;\r\n}\r\n\r\ninterface RadioGroupProps {\r\n  name: string;\r\n  valueSelected: string;\r\n  onChange: (e: React.ChangeEvent<HTMLInputElement>) => void;\r\n  errorMessage?: string;\r\n  children: React.ReactNode;\r\n}\r\n\r\n// Type Guard to ensure child.props is an object\r\nfunction isObject(props: any): props is { [key: string]: any } {\r\n  return typeof props === 'object' && props !== null;\r\n}\r\n\r\nconst RadioItem: React.FC<RadioItemProps> = ({ id, label, value, name, disabled }) => {\r\n  const { values, setFieldValue } = useFormikContext<any>();\r\n  const fieldName = name || id;\r\n\r\n  return (\r\n    <Form.Check\r\n      type=\"radio\"\r\n      id={id}\r\n      label={label}\r\n      value={value}\r\n      name={fieldName}\r\n      checked={values[fieldName] === value}\r\n      onChange={(e) => {\r\n        setFieldValue(fieldName, e.target.value);\r\n      }}\r\n      disabled={disabled}\r\n      inline\r\n    />\r\n  );\r\n};\r\n\r\nconst RadioGroup: React.FC<RadioGroupProps> = ({\r\n  name,\r\n  valueSelected,\r\n  onChange,\r\n  errorMessage,\r\n  children,\r\n}) => {\r\n  const { errors, touched } = useFormikContext<any>();\r\n  const hasError = touched[name] && errors[name];\r\n\r\n  // Create a context to pass the name to RadioItems\r\n  const radioContext = React.useMemo(() => ({ name }), [name]);\r\n\r\n  // Clone children to pass the name, ensuring props are spread safely\r\n  const childrenWithProps = React.Children.map(children, (child) => {\r\n    if (React.isValidElement(child)) {\r\n      // Ensure child.props is an object before spreading\r\n      if (isObject(child.props)) {\r\n        return React.cloneElement(child as React.ReactElement<RadioItemProps>, {\r\n          name,\r\n          ...child.props, // Safely spread child.props\r\n        });\r\n      }\r\n    }\r\n    return child;\r\n  });\r\n\r\n  return (\r\n    <div>\r\n      <div className=\"radio-group\">\r\n        {childrenWithProps}\r\n      </div>\r\n      {hasError && (\r\n        <div className=\"invalid-feedback d-block\">\r\n          {errorMessage || (typeof errors[name] === 'string' ? errors[name] : String(errors[name]))}\r\n        </div>\r\n      )}\r\n    </div>\r\n  );\r\n};\r\n\r\nexport const Radio = {\r\n  RadioGroup,\r\n  RadioItem,\r\n};\r\n\r\nexport default Radio;\r\n", "// This file exports all the components needed to replace react-bootstrap4-form-validation\r\n// It serves as a drop-in replacement for the original library\r\n\r\nimport ValidationForm from './ValidationFormWrapper';\r\nimport { TextInput, SelectGroup } from './FormikTextInput';\r\nimport { Radio } from './FormikRadio';\r\n\r\n// Export all components\r\nexport {\r\n  ValidationForm,\r\n  TextInput,\r\n  SelectGroup,\r\n  Radio\r\n};\r\n\r\n// Also export as default for convenience\r\nexport default {\r\n  ValidationForm,\r\n  TextInput,\r\n  SelectGroup,\r\n  Radio\r\n};\r\n", "import React, { forwardRef } from 'react';\r\nimport { Formik, Form, FormikProps, FormikHelpers } from 'formik';\r\nimport * as Yup from 'yup';\r\n\r\n// This is a wrapper component that replaces react-bootstrap4-form-validation with Formik\r\ninterface MockEvent {\r\n  preventDefault: () => void;\r\n  stopPropagation: () => void;\r\n  currentTarget: (EventTarget & Element) | null;\r\n  target: EventTarget | null;\r\n  nativeEvent: Event;\r\n  bubbles: boolean;\r\n  cancelable: boolean;\r\n  defaultPrevented: boolean;\r\n  eventPhase: number;\r\n  isTrusted: boolean;\r\n  timeStamp: number;\r\n  type: string;\r\n  isDefaultPrevented: () => boolean;\r\n  isPropagationStopped: () => boolean;\r\n  persist: () => void;\r\n}\r\n\r\ninterface ValidationFormWrapperProps {\r\n  children: React.ReactNode | ((formikProps: FormikProps<any>) => React.ReactNode);\r\n  onSubmit: (event: React.FormEvent | MockEvent, values?: Record<string, any>, actions?: FormikHelpers<Record<string, any>>) => void;\r\n  onErrorSubmit?: (errors: any) => void;\r\n  initialValues?: Record<string, any>;\r\n  enableReinitialize?: boolean;\r\n  autoComplete?: string;\r\n  className?: string;\r\n  onKeyPress?: (e: any) => void;\r\n}\r\n\r\nconst ValidationFormWrapper = forwardRef<HTMLFormElement, ValidationFormWrapperProps>((props, ref) => {\r\n  const { children, onSubmit, autoComplete, className, onKeyPress, initialValues, ...rest } = props;\r\n\r\n  // Create an empty validation schema by default\r\n  const validationSchema = Yup.object().shape({});\r\n\r\n  return (\r\n    <Formik\r\n      initialValues={initialValues || {}}\r\n      validationSchema={validationSchema}\r\n      onSubmit={(values: Record<string, any>, actions: FormikHelpers<Record<string, any>>) => {\r\n        // Create a mock event object with preventDefault method and currentTarget\r\n        const mockEvent: MockEvent = {\r\n          preventDefault: () => {},\r\n          stopPropagation: () => {},\r\n          currentTarget: null, // Set to null to avoid checkValidity errors\r\n          target: null,\r\n          nativeEvent: new Event('submit'),\r\n          bubbles: false,\r\n          cancelable: true,\r\n          defaultPrevented: false,\r\n          eventPhase: 0,\r\n          isTrusted: false,\r\n          timeStamp: Date.now(),\r\n          type: 'submit',\r\n          isDefaultPrevented: () => false,\r\n          isPropagationStopped: () => false,\r\n          persist: () => {}\r\n        };\r\n\r\n        if (onSubmit) {\r\n          // Pass the mock event object to maintain compatibility with the original code\r\n          onSubmit(mockEvent, values, actions);\r\n        }\r\n      }}\r\n      {...rest}\r\n    >\r\n      {(formikProps: FormikProps<any>) => (\r\n        <Form\r\n          ref={ref}\r\n          onSubmit={formikProps.handleSubmit}\r\n          autoComplete={autoComplete}\r\n          className={className}\r\n          onKeyPress={onKeyPress}\r\n        >\r\n          {typeof children === 'function' ? children(formikProps) : children}\r\n        </Form>\r\n      )}\r\n    </Formik>\r\n  );\r\n});\r\n\r\nValidationFormWrapper.displayName = 'ValidationFormWrapper';\r\n\r\nexport default ValidationFormWrapper;\r\n", "import React from 'react';\r\nimport { Form } from 'react-bootstrap';\r\nimport { Field, ErrorMessage, useField } from 'formik';\r\n\r\n// This component mimics the TextInput component from react-bootstrap4-form-validation\r\n// but uses Formik under the hood\r\nexport const TextInput = ({\r\n  name,\r\n  id,\r\n  required,\r\n  validator,\r\n  errorMessage,\r\n  onChange,\r\n  value,\r\n  as,\r\n  multiline,\r\n  rows,\r\n  pattern,\r\n  ...props\r\n}: any) => {\r\n  // Create a custom validation function that mimics the original validator\r\n  const validate = (val: any) => {\r\n    // Safely convert value to string and trim\r\n    const stringVal = typeof val === 'string' ? val : String(val || '');\r\n    if (required && (!val || stringVal.trim() === '')) {\r\n      return errorMessage?.validator || 'This field is required';\r\n    }\r\n\r\n    if (validator && !validator(val)) {\r\n      return errorMessage?.validator || 'Invalid value';\r\n    }\r\n\r\n    if (pattern && val) {\r\n      const regex = new RegExp(pattern);\r\n      if (!regex.test(val)) {\r\n        return errorMessage?.pattern || 'Invalid format';\r\n      }\r\n    }\r\n\r\n    return undefined;\r\n  };\r\n\r\n  return (\r\n    <Field name={name} validate={validate}>\r\n      {({ field, meta }: any) => (\r\n        <>\r\n          <Form.Control\r\n            {...field}\r\n            {...props}\r\n            id={id}\r\n            as={as || 'input'}\r\n            rows={rows}\r\n            isInvalid={meta.touched && !!meta.error}\r\n            onChange={(e: React.ChangeEvent<HTMLInputElement>) => {\r\n              field.onChange(e);\r\n              if (onChange) onChange(e);\r\n            }}\r\n            value={value !== undefined ? value : field.value}\r\n          />\r\n          {meta.touched && meta.error ? (\r\n            <Form.Control.Feedback type=\"invalid\">\r\n              {meta.error}\r\n            </Form.Control.Feedback>\r\n          ) : null}\r\n        </>\r\n      )}\r\n    </Field>\r\n  );\r\n};\r\n\r\n// This component mimics the SelectGroup component from react-bootstrap4-form-validation\r\n// but uses Formik under the hood\r\nexport const SelectGroup = ({\r\n  name,\r\n  id,\r\n  required,\r\n  errorMessage,\r\n  onChange,\r\n  value,\r\n  children,\r\n  ...props\r\n}: any) => {\r\n  // Create a custom validation function that mimics the original validator\r\n  const validate = (val: any) => {\r\n    if (required && (!val || val === '')) {\r\n      return errorMessage?.validator || 'This field is required';\r\n    }\r\n\r\n    return undefined;\r\n  };\r\n\r\n  return (\r\n    <Field name={name} validate={validate}>\r\n      {({ field, meta }: any) => (\r\n        <>\r\n          <Form.Control\r\n            as=\"select\"\r\n            {...field}\r\n            {...props}\r\n            id={id}\r\n            isInvalid={meta.touched && !!meta.error}\r\n            onChange={(e: React.ChangeEvent<HTMLSelectElement>) => {\r\n              field.onChange(e);\r\n              if (onChange) onChange(e);\r\n            }}\r\n            value={value !== undefined ? value : field.value}\r\n          >\r\n            {children}\r\n          </Form.Control>\r\n          {meta.touched && meta.error ? (\r\n            <Form.Control.Feedback type=\"invalid\">\r\n              {meta.error}\r\n            </Form.Control.Feedback>\r\n          ) : null}\r\n        </>\r\n      )}\r\n    </Field>\r\n  );\r\n};\r\n\r\n// Export both components as named exports and as a default object\r\nexport default {\r\n  TextInput,\r\n  SelectGroup\r\n};\r\n", "\"use client\";\n\nimport * as React from 'react';\nconst context = /*#__PURE__*/React.createContext(null);\ncontext.displayName = 'CardHeaderContext';\nexport default context;", "\n    (window.__NEXT_P = window.__NEXT_P || []).push([\n      \"/adminsettings/hazardtypes/forms\",\n      function () {\n        return require(\"private-next-pages/adminsettings/hazardtypes/forms.tsx\");\n      }\n    ]);\n    if(module.hot) {\n      module.hot.dispose(function () {\n        window.__NEXT_P.push([\"/adminsettings/hazardtypes/forms\"])\n      });\n    }\n  "], "names": ["value", "onChange", "SimpleRichTextEditor", "placeholder", "height", "disabled", "editor<PERSON><PERSON>", "useRef", "isFocused", "setIsFocused", "useState", "useEffect", "current", "innerHTML", "handleInput", "execCommand", "command", "document", "focus", "div", "className", "style", "border", "padding", "borderBottom", "background", "button", "type", "onClick", "margin", "strong", "em", "u", "url", "prompt", "ref", "contentEditable", "onInput", "onFocus", "onBlur", "minHeight", "maxHeight", "overflow", "outline", "data-placeholder", "suppressContentEditableWarning", "EditorComponent", "initContent", "props", "content", "_initialHazardType", "title", "code", "description", "initialVal", "setInitialVal", "editform", "routes", "t", "useTranslation", "handleSubmit", "event", "values", "response", "toastMsg", "preventDefault", "obj", "trim", "apiService", "patch", "post", "_id", "toast", "success", "Router", "errorCode", "error", "handleChange", "e", "target", "name", "prevState", "handleDescription", "hazardTypeParams", "query", "sort", "limit", "getHazardTypeData", "get", "formRef", "Container", "fluid", "Card", "marginTop", "boxShadow", "ValidationFormWrapper", "onSubmit", "initialValues", "enableReinitialize", "Body", "Row", "Col", "Title", "hr", "md", "lg", "sm", "Form", "Group", "Label", "TextInput", "id", "required", "errorMessage", "validator", "String", "evt", "<PERSON><PERSON>", "variant", "re<PERSON><PERSON><PERSON><PERSON>", "window", "scrollTo", "Link", "href", "as", "CardBody", "React", "bsPrefix", "Component", "useBootstrapPrefix", "_jsx", "classNames", "displayName", "<PERSON><PERSON><PERSON>er", "<PERSON><PERSON><PERSON><PERSON>", "prefix", "contextValue", "useMemo", "cardHeaderBsPrefix", "CardHeaderContext", "Provider", "children", "CardImg", "CardImgOverlay", "CardLink", "DivStyledAsH6", "divWithClassName", "CardSubtitle", "CardText", "DivStyledAsH5", "CardTitle", "bg", "text", "body", "Object", "assign", "Img", "Subtitle", "Text", "Header", "Footer", "ImgOverlay", "Radio", "RadioGroup", "valueSelected", "errors", "touched", "useFormikContext", "<PERSON><PERSON><PERSON><PERSON>", "childrenWithProps", "map", "child", "isObject", "RadioItem", "label", "setFieldValue", "fieldName", "Check", "checked", "inline", "ValidationForm", "SelectGroup", "forwardRef", "autoComplete", "onKeyPress", "rest", "validationSchema", "<PERSON><PERSON>", "shape", "<PERSON><PERSON>", "actions", "mockEvent", "stopPropagation", "currentTarget", "nativeEvent", "Event", "bubbles", "cancelable", "defaultPrevented", "eventPhase", "isTrusted", "timeStamp", "Date", "now", "isDefaultPrevented", "isPropagationStopped", "persist", "formikProps", "multiline", "rows", "pattern", "Field", "validate", "stringVal", "val", "RegExp", "test", "field", "meta", "Control", "isInvalid", "undefined", "<PERSON><PERSON><PERSON>", "context"], "sourceRoot": "", "ignoreList": [3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 17]}