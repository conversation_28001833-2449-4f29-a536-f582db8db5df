{"version": 3, "file": "static/chunks/pages/country/components/CountryCoverSection-cc5dfdbaec0522a4.js", "mappings": "qJAoEA,MA/CkD,OAAC,MACjDA,EAAO,QAAQ,IACfC,CA6CaC,CA7CR,EAAE,SA6CkBA,EA5CzBC,EAAY,EAAE,MACdC,CAAI,MACJC,CAAI,CACJC,UAAQ,SACRC,CAAO,CACPC,OAAK,WACLC,GAAY,CAAK,CAClB,UAsBK,GAAqC,UAAxB,OAAOH,EAASI,GAAG,EAAyC,UAAxB,OAAOJ,EAASK,GAAG,CAKtE,UAACC,EAAAA,EAAMA,CAAAA,CACLN,SAAUA,EACVD,KAAMA,EACNG,MAAOA,GAASR,EAChBS,UAAWA,EACXF,QA/BgB,CA+BPM,GA9BPN,GAeFA,EAdoB,IADT,EAeHO,KAZNb,QAYmBc,IAXnBZ,OACAC,WACAE,CACF,EAGe,UACbA,EACAU,YAAa,IAAMV,CACrB,EAE6BW,EAEjC,IAIS,IAYX,wFClDA,MARyB,OAAC,UAAEX,CAAQ,OAQrBY,OARuBC,CAAY,QAQnBD,EARqBE,CAAQ,CAAS,GACnE,MACE,UAACC,EAAAA,EAAUA,CAAAA,CAACf,SAAUA,EAAUa,aAAcA,WAC5C,UAACG,MAAAA,UAAKF,KAGZ,ECdMG,EAAO,mBACPC,EAAQ,qBACRC,EAAO,eACPC,EAAW,kBAgTjB,EA/SkB,CAChB,CACE,YAAe,IA6SJC,OA5SX,EA4SoBA,EAAC,IA5SV,CACT,CACE,MAAS,SACX,EACD,EAEH,CACE,YAAeJ,EACf,QAAW,CACT,CACE,MAAS,SACX,EACD,EAEH,CACE,YAAeC,EACf,QAAW,CACT,CACE,MAAS,SACX,EACD,EAEH,CACE,YAAe,iBACf,YAAe,WACf,QAAW,CACT,CACE,WAAc,KAChB,EAEJ,EACA,CACE,YAAe,yBACf,YAAeE,EACf,QAAW,CACT,CACE,MAAS,SACX,EACD,EAEH,CACE,YAAe,6BACf,YAAe,SACf,QAAW,CACT,CACE,WAAc,KAChB,EAEJ,EACA,CACE,YAAe,6BACf,YAAeH,EACf,QAAW,CACT,CACE,MAAS,SACX,EACD,EAEH,CACE,YAAe,0BACf,YAAeG,EACf,QAAW,CACT,CACE,MAAS,SACX,EACD,EAEH,CACE,YAAe,qBACf,YAAeA,EACf,QAAW,CACT,CACE,MAAS,SACX,EACD,EAEH,CACE,YAAe,oBACf,YAAe,WACf,QAAW,CACT,CACE,MAAS,SACX,EACD,EAEH,CACE,YAAe,MACf,QAAW,CACT,CACE,WAAc,KAChB,EACD,EAEH,CACE,YAAe,MACf,YAAe,WACf,QAAW,CACT,CACE,MAAS,SACX,EACD,EAEH,CACE,YAAe,MACf,YAAe,cACf,QAAW,CACT,CACE,WAAc,KAChB,EACD,EAEH,CACE,YAAe,MACf,YAAeH,EACf,QAAW,CACT,CACE,MAAS,SACX,EAEJ,EACA,CACE,YAAe,MACf,YAAeC,EACf,QAAW,CACT,CACE,MAAS,SACX,EACD,EAEH,CACE,YAAe,WACf,YAAe,gBACf,QAAW,CACT,CACE,MAAS,SACX,EACD,EAEH,CACE,YAAe,WACf,YAAeD,EACf,QAAW,CACT,CACE,MAAS,SACX,EACD,EAEH,CACE,YAAe,OACf,QAAW,CACT,CACE,WAAc,KAChB,EAEJ,EACA,CACE,YAAe,OACf,YAAe,WACf,QAAW,CACT,CACE,MAAS,SACX,EACD,EAEH,CACE,YAAe,OACf,YAAe,cACf,QAAW,CACT,CACE,WAAc,KAChB,EACD,EAEH,CACE,YAAe,OACf,YAAeA,EACf,QAAW,CACT,CACE,MAAS,SACX,EACD,EAEH,CACE,YAAe,OACf,YAAeC,EACf,QAAW,CACT,CACE,MAAS,SACX,EACD,EAEH,CACE,YAAeC,EACf,YAAe,WACf,QAAW,CACT,CACE,MAAS,SACX,EACD,EAEH,CACE,YAAeA,EACf,YAAeC,EACf,QAAW,CACT,CACE,MAAS,SACX,EACD,EAEH,CACE,YAAeD,EACf,YAAeF,EACf,QAAW,CACT,CACE,MAAS,SACX,EACD,EAEH,CACE,YAAeE,EACf,YAAeD,EACf,QAAW,CACT,CACE,MAAS,SACX,EACD,EAEH,CACE,YAAe,aACf,YAAe,SACf,QAAW,CACT,CACE,WAAc,KAChB,EACD,EAEH,CACE,YAAe,UACf,QAAW,CACT,CACE,WAAc,KAChB,EACD,EAEH,CACE,YAAe,UACf,YAAeD,EACf,QAAW,CACT,CACE,MAAS,SACX,EACD,EAEH,CACE,YAAe,UACf,YAAeC,EACf,QAAW,CACT,CACE,MAAS,SACX,EACD,EAEH,CACE,YAAe,eACf,YAAe,gBACf,QAAW,CACT,CACE,MAAS,SACX,EACD,EAEH,CACE,YAAe,kBACf,YAAe,WACf,QAAW,CACT,CACE,MAAS,SACX,EAEJ,EACA,CACE,YAAe,QACf,YAAe,WACf,QAAW,CACT,CACE,MAAS,SACX,EACD,EAEH,CACE,YAAe,QACf,YAAeD,EACf,QAAW,CACT,CACE,MAAS,SACX,EACD,EAEJ,2BCnND,MAzEwC,OAAC,SAyE1BK,GAxEbC,CAAU,GAwEUD,EAAC,SAvErBE,CAAY,eACZC,CAAa,UACbX,CAAQ,QACRY,EAAS,GAAG,OACZC,EAAQ,MAAM,UACdC,CAAQ,MACRC,EAAO,CAAC,SACRC,EAAU,CAAC,SACXC,CAAO,CACR,GACO,QAAEC,CAAM,CAAE,CAAGC,CAAAA,EAAAA,EAAAA,SAAAA,CAASA,GACtB,UAAEC,CAAQ,CAAEC,WAAS,CAAE,CAAIC,CAAAA,EAAAA,EAAAA,CAAAA,CAAaA,UAmB9C,EAAsB,SAAP,CAAQpB,MAAAA,UAAI,uBACtBkB,EAGH,QAHa,EAGZlB,MAAAA,CAAIqB,UAAU,yBACb,UAACrB,MAAAA,CAAIqB,UAAU,WAAWC,MAAO,OAAEX,SAAOD,EAAQ1B,SAAU,UAAW,WACrE,WAACuC,EAAAA,EAASA,CAAAA,CACRC,kBAzBe,CAyBIC,MAxBlBd,EACPD,OAA0B,UAAlB,OAAOA,EAAsB,GAAU,OAAPA,EAAO,MAAMA,CACvD,EAuBQgB,OAhBOjB,CAgBCiB,EArBM,CACpBtC,IAAK,SAIyBuC,CAH9BtC,IAAK,SACP,EAmBQwB,KAAMA,EACNe,OAhBU,CAgBFC,GAfdC,EAAIC,UAAU,CAAC,CACbC,OAAQ3B,CACV,EACF,EAaQ4B,QAAS,CACPnB,EAhBWT,MAgBFS,EACT3B,UAAW,GACX+C,mBAAmB,EACnBC,mBAAmB,EACnBC,YAAY,EACZC,gBAAgB,EAChBC,gBAAgB,EAChBC,mBAAmB,CACrB,YAECzC,EACAS,GAAcC,GAAgBA,EAAad,WAAW,EACrD,UAACE,EAAgBA,CACfZ,SAAUwB,EAAad,SADRE,EACmB,GAClCC,aAAc,KAEZ2C,QAAQC,GAAG,CAAC,qBACZ1B,GAAAA,GACF,WAECR,GAHCQ,QA5BQ,UAACf,MAAAA,UAAI,mBAsC7B,qKCzBA,MAnC4B,QAOH0C,EAYKA,EAlB1B,GAAM,GAAEC,CAAC,CAAE,CAAGC,CAAAA,EAAAA,EAAAA,EAAAA,CAAcA,CAAC,UAC7B,MACI,+BACI,UAACC,EAAAA,CAAGA,CAAAA,UACA,WAACC,EAAAA,CAAGA,CAAAA,CAACC,GAAI,GAAIzB,MAAO,CAAE0B,QAAS,MAAO,YAClC,UAAChD,MAAAA,CAAIqB,UAAU,sBACVqB,OAAAA,GAAAA,OAAAA,EAAAA,EAAOO,IAAPP,EAAOO,EAAPP,KAAAA,EAAAA,EAAetD,GAAfsD,EACG,UAACpC,EAAAA,CAAOA,CAAAA,CAACG,cAAeiC,EAAMO,MAAM,UAChC,UAACrE,EAAAA,CAAYA,CAAAA,CACTG,KAAM,CACFmE,IAAK,6BACT,EACAlE,SAAU0D,EAAMO,MAAM,KAG9B,OAER,WAACjD,MAAAA,CAAIqB,UAAU,wBACX,WAAC8B,KAAAA,WAAG,UAAET,GAAAA,MAAAA,GAAAA,EAAOU,IAAPV,OAAOU,EAAPV,KAAAA,EAAAA,EAAoBxD,GAApBwD,EAAyB,CAAC,OAkBxD,SACIU,CAKC,CACDC,CAAsB,CACtBV,CAA0B,CAC1BW,CAIC,CACDC,CAAkB,CAClBC,CAAoB,EAEpB,MACE,WAACxD,MAAAA,CAAIqB,UAAU,+BACb,UAACoC,IAAIA,CACHC,KAAM,CACJC,SAAU,aACVC,MAAO,CACLC,OAAO,EAJRJ,KAIUL,EAAAA,KAAAA,EAAAA,EAAaU,GAAG,CACzBC,OAAQV,CACV,CAFWD,WAMb,WAACpD,MAAAA,CAAIqB,UAAU,6BACb,UAACrB,MAAAA,CAAIqB,UAAU,2BACb,UAAC2C,MAAAA,CACCC,IAAI,2BACJtD,MAAM,KACND,OAAO,KACPwD,IAAI,8BAGR,WAACC,OAAAA,WACExB,EAAE,oBACH,UAACyB,KAAAA,CAAAA,GACD,UAACC,IAAAA,UACEf,SAAAA,KAAAA,EAAAA,EAAkBgB,WAAlBhB,IAAiC,EAC9BA,EAAiBgB,eAAe,CAChC,YAMZ,UAACb,IAAIA,CACHC,KAAM,CACJC,SAAU,SACVC,MAAO,CACLC,OAAO,MAJRJ,CAIUL,EAAAA,KAAAA,EAAAA,EAAaU,GAAG,CACzBC,OAAQR,CACV,CAFWH,WAMb,WAACpD,MAAAA,CAAIqB,UAAU,6BACb,UAACrB,MAAAA,CAAIqB,UAAU,2BACb,UAAC2C,MAAAA,CACCC,IAAI,2BACJtD,MAAM,KACND,OAAO,KACPwD,IAAI,8BAGR,WAACC,OAAAA,WACExB,EAAE,gBACH,UAACyB,KAAAA,CAAAA,GACD,UAACC,IAAAA,UACEf,OAAAA,EAAAA,KAAAA,EAAAA,EAAkBiB,WAAAA,EACfjB,EAAiBiB,GADpBjB,QAC+B,CAC5B,YAMZ,UAACG,IAAIA,CACHC,KAAM,CACJC,SAAU,WACVC,MAAO,CAAEC,QAAST,EAAYU,CAH7BL,EAGgC,CAAEM,OAAQP,CAAgB,CAC7D,WAGA,WAACxD,MAAAA,CAAIqB,UAAU,6BACb,UAACrB,MAAAA,CAAIqB,UAAU,2BACb,UAAC2C,MAAAA,CACCC,IAAI,yBACJtD,MAAM,KACND,OAAO,KACPwD,IAAI,8BAGR,WAACC,OAAAA,WACExB,EAAE,kBACH,UAACyB,KAAAA,CAAAA,GACD,UAACC,IAAAA,UACEf,SAAAA,KAAAA,EAAAA,EAAkBkB,WAAlBlB,EAAkBkB,EACflB,EAAiBkB,aAAa,CAC9B,cAQlB,EA/H0B9B,EAAMU,WAAW,CACjBV,EAAMW,iBAAiB,CACvBV,EACAD,EAAMY,gBAAgB,CACtBZ,EAAMa,aAAa,CACnBb,EAAMc,eAAe,WAOjD,mBChEA,4CACA,0CACA,WACA,OAAe,EAAQ,KAA+D,CACtF,EACA,SAFsB", "sources": ["webpack://_N_E/./components/common/RKIMapMarker.tsx", "webpack://_N_E/./components/common/RKIMapInfowindow.tsx", "webpack://_N_E/./components/common/mapStyles.tsx", "webpack://_N_E/./components/common/RKIMap1.tsx", "webpack://_N_E/./pages/country/components/CountryCoverSection.tsx", "webpack://_N_E/?3eb9"], "sourcesContent": ["import React from 'react';\r\nimport { Marker } from '@react-google-maps/api';\r\n\r\ninterface RKIMapMarkerProps {\r\n  name?: string;\r\n  id?: string;\r\n  countryId?: string;\r\n  type?: string;\r\n  icon?: {\r\n    url: string;\r\n    scaledSize?: google.maps.Size;\r\n  };\r\n  position: {\r\n    lat: number;\r\n    lng: number;\r\n  };\r\n  onClick?: (props: any, marker: any, e: any) => void;\r\n  title?: string;\r\n  draggable?: boolean;\r\n}\r\n\r\nconst RKIMapMarker: React.FC<RKIMapMarkerProps> = ({\r\n  name = 'Marker',\r\n  id = '',\r\n  countryId = '',\r\n  type,\r\n  icon,\r\n  position,\r\n  onClick,\r\n  title,\r\n  draggable = false,\r\n}) => {\r\n  const handleClick = (e: google.maps.MapMouseEvent) => {\r\n    if (onClick) {\r\n      const markerProps = {\r\n        name,\r\n        id,\r\n        countryId,\r\n        type,\r\n        position,\r\n      };\r\n\r\n      // Create a marker-like object for compatibility with old onClick signature\r\n      const marker = {\r\n        position,\r\n        getPosition: () => position,\r\n      };\r\n\r\n      onClick(markerProps, marker, e);\r\n    }\r\n  };\r\n\r\n  // Ensure position is valid\r\n  if (!position || typeof position.lat !== 'number' || typeof position.lng !== 'number') {\r\n    return null;\r\n  }\r\n\r\n  return (\r\n    <Marker\r\n      position={position}\r\n      icon={icon}\r\n      title={title || name}\r\n      draggable={draggable}\r\n      onClick={handleClick}\r\n    />\r\n  );\r\n};\r\n\r\nexport default RKIMapMarker;\r\n", "import { InfoWindow } from '@react-google-maps/api';\r\n\r\ninterface Props {\r\n  position: google.maps.LatLngLiteral;\r\n  onCloseClick?: () => void;\r\n  children?: React.ReactNode;\r\n}\r\n\r\nconst RKIMapInfowindow = ({ position, onCloseClick, children }: Props) => {\r\n  return (\r\n    <InfoWindow position={position} onCloseClick={onCloseClick}>\r\n      <div>{children}</div>\r\n    </InfoWindow>\r\n  );\r\n};\r\n\r\nexport default RKIMapInfowindow;\r\n", "const fill = \"labels.text.fill\"\r\nconst stoke = \"labels.text.stroke\"\r\nconst road = \"road.highway\"\r\nconst geometry = \"geometry.stroke\"\r\nconst mapStyles = [\r\n  {\r\n    \"elementType\": \"geometry\",\r\n    \"stylers\": [\r\n      {\r\n        \"color\": \"#1d2c4d\"\r\n      }\r\n    ]\r\n  },\r\n  {\r\n    \"elementType\": fill,\r\n    \"stylers\": [\r\n      {\r\n        \"color\": \"#8ec3b9\"\r\n      }\r\n    ]\r\n  },\r\n  {\r\n    \"elementType\": stoke,\r\n    \"stylers\": [\r\n      {\r\n        \"color\": \"#1a3646\"\r\n      }\r\n    ]\r\n  },\r\n  {\r\n    \"featureType\": \"administrative\",\r\n    \"elementType\": \"geometry\",\r\n    \"stylers\": [\r\n      {\r\n        \"visibility\": \"off\"\r\n      }\r\n    ]\r\n  },\r\n  {\r\n    \"featureType\": \"administrative.country\",\r\n    \"elementType\": geometry,\r\n    \"stylers\": [\r\n      {\r\n        \"color\": \"#4b6878\"\r\n      }\r\n    ]\r\n  },\r\n  {\r\n    \"featureType\": \"administrative.land_parcel\",\r\n    \"elementType\": \"labels\",\r\n    \"stylers\": [\r\n      {\r\n        \"visibility\": \"off\"\r\n      }\r\n    ]\r\n  },\r\n  {\r\n    \"featureType\": \"administrative.land_parcel\",\r\n    \"elementType\": fill,\r\n    \"stylers\": [\r\n      {\r\n        \"color\": \"#64779e\"\r\n      }\r\n    ]\r\n  },\r\n  {\r\n    \"featureType\": \"administrative.province\",\r\n    \"elementType\": geometry,\r\n    \"stylers\": [\r\n      {\r\n        \"color\": \"#4b6878\"\r\n      }\r\n    ]\r\n  },\r\n  {\r\n    \"featureType\": \"landscape.man_made\",\r\n    \"elementType\": geometry,\r\n    \"stylers\": [\r\n      {\r\n        \"color\": \"#334e87\"\r\n      }\r\n    ]\r\n  },\r\n  {\r\n    \"featureType\": \"landscape.natural\",\r\n    \"elementType\": \"geometry\",\r\n    \"stylers\": [\r\n      {\r\n        \"color\": \"#023e58\"\r\n      }\r\n    ]\r\n  },\r\n  {\r\n    \"featureType\": \"poi\",\r\n    \"stylers\": [\r\n      {\r\n        \"visibility\": \"off\"\r\n      }\r\n    ]\r\n  },\r\n  {\r\n    \"featureType\": \"poi\",\r\n    \"elementType\": \"geometry\",\r\n    \"stylers\": [\r\n      {\r\n        \"color\": \"#283d6a\"\r\n      }\r\n    ]\r\n  },\r\n  {\r\n    \"featureType\": \"poi\",\r\n    \"elementType\": \"labels.text\",\r\n    \"stylers\": [\r\n      {\r\n        \"visibility\": \"off\"\r\n      }\r\n    ]\r\n  },\r\n  {\r\n    \"featureType\": \"poi\",\r\n    \"elementType\": fill,\r\n    \"stylers\": [\r\n      {\r\n        \"color\": \"#6f9ba5\"\r\n      }\r\n    ]\r\n  },\r\n  {\r\n    \"featureType\": \"poi\",\r\n    \"elementType\": stoke,\r\n    \"stylers\": [\r\n      {\r\n        \"color\": \"#1d2c4d\"\r\n      }\r\n    ]\r\n  },\r\n  {\r\n    \"featureType\": \"poi.park\",\r\n    \"elementType\": \"geometry.fill\",\r\n    \"stylers\": [\r\n      {\r\n        \"color\": \"#023e58\"\r\n      }\r\n    ]\r\n  },\r\n  {\r\n    \"featureType\": \"poi.park\",\r\n    \"elementType\": fill,\r\n    \"stylers\": [\r\n      {\r\n        \"color\": \"#3C7680\"\r\n      }\r\n    ]\r\n  },\r\n  {\r\n    \"featureType\": \"road\",\r\n    \"stylers\": [\r\n      {\r\n        \"visibility\": \"off\"\r\n      }\r\n    ]\r\n  },\r\n  {\r\n    \"featureType\": \"road\",\r\n    \"elementType\": \"geometry\",\r\n    \"stylers\": [\r\n      {\r\n        \"color\": \"#304a7d\"\r\n      }\r\n    ]\r\n  },\r\n  {\r\n    \"featureType\": \"road\",\r\n    \"elementType\": \"labels.icon\",\r\n    \"stylers\": [\r\n      {\r\n        \"visibility\": \"off\"\r\n      }\r\n    ]\r\n  },\r\n  {\r\n    \"featureType\": \"road\",\r\n    \"elementType\": fill,\r\n    \"stylers\": [\r\n      {\r\n        \"color\": \"#98a5be\"\r\n      }\r\n    ]\r\n  },\r\n  {\r\n    \"featureType\": \"road\",\r\n    \"elementType\": stoke,\r\n    \"stylers\": [\r\n      {\r\n        \"color\": \"#1d2c4d\"\r\n      }\r\n    ]\r\n  },\r\n  {\r\n    \"featureType\": road,\r\n    \"elementType\": \"geometry\",\r\n    \"stylers\": [\r\n      {\r\n        \"color\": \"#2c6675\"\r\n      }\r\n    ]\r\n  },\r\n  {\r\n    \"featureType\": road,\r\n    \"elementType\": geometry,\r\n    \"stylers\": [\r\n      {\r\n        \"color\": \"#255763\"\r\n      }\r\n    ]\r\n  },\r\n  {\r\n    \"featureType\": road,\r\n    \"elementType\": fill,\r\n    \"stylers\": [\r\n      {\r\n        \"color\": \"#b0d5ce\"\r\n      }\r\n    ]\r\n  },\r\n  {\r\n    \"featureType\": road,\r\n    \"elementType\": stoke,\r\n    \"stylers\": [\r\n      {\r\n        \"color\": \"#023e58\"\r\n      }\r\n    ]\r\n  },\r\n  {\r\n    \"featureType\": \"road.local\",\r\n    \"elementType\": \"labels\",\r\n    \"stylers\": [\r\n      {\r\n        \"visibility\": \"off\"\r\n      }\r\n    ]\r\n  },\r\n  {\r\n    \"featureType\": \"transit\",\r\n    \"stylers\": [\r\n      {\r\n        \"visibility\": \"off\"\r\n      }\r\n    ]\r\n  },\r\n  {\r\n    \"featureType\": \"transit\",\r\n    \"elementType\": fill,\r\n    \"stylers\": [\r\n      {\r\n        \"color\": \"#98a5be\"\r\n      }\r\n    ]\r\n  },\r\n  {\r\n    \"featureType\": \"transit\",\r\n    \"elementType\": stoke,\r\n    \"stylers\": [\r\n      {\r\n        \"color\": \"#1d2c4d\"\r\n      }\r\n    ]\r\n  },\r\n  {\r\n    \"featureType\": \"transit.line\",\r\n    \"elementType\": \"geometry.fill\",\r\n    \"stylers\": [\r\n      {\r\n        \"color\": \"#283d6a\"\r\n      }\r\n    ]\r\n  },\r\n  {\r\n    \"featureType\": \"transit.station\",\r\n    \"elementType\": \"geometry\",\r\n    \"stylers\": [\r\n      {\r\n        \"color\": \"#3a4762\"\r\n      }\r\n    ]\r\n  },\r\n  {\r\n    \"featureType\": \"water\",\r\n    \"elementType\": \"geometry\",\r\n    \"stylers\": [\r\n      {\r\n        \"color\": \"#0e1626\"\r\n      }\r\n    ]\r\n  },\r\n  {\r\n    \"featureType\": \"water\",\r\n    \"elementType\": fill,\r\n    \"stylers\": [\r\n      {\r\n        \"color\": \"#4e6d70\"\r\n      }\r\n    ]\r\n  }\r\n];\r\n\r\nexport default mapStyles;", "import React from 'react';\r\nimport { GoogleMap, useJsApiLoader } from '@react-google-maps/api';\r\nimport RKIMapInfowindow from './RKIMapInfowindow';\r\nimport mapStyles from './mapStyles';\r\nimport { useRouter } from \"next/router\";\r\nimport { useGoogleMaps } from './GoogleMapsProvider';\r\n\r\ninterface RKIMap1Props {\r\n  markerInfo?: React.ReactNode;\r\n  activeMarker?: any;\r\n  initialCenter?: { lat: number; lng: number };\r\n  children?: React.ReactNode;\r\n  height?: number | string;\r\n  width?: string;\r\n  language?: string;\r\n  points?: any[];\r\n  zoom?: number;\r\n  minZoom?: number;\r\n  onClose?: () => void\r\n}\r\n\r\nconst RKIMap1: React.FC<RKIMap1Props> = ({\r\n  markerInfo,\r\n  activeMarker,\r\n  initialCenter,\r\n  children,\r\n  height = 300,\r\n  width = \"114%\",\r\n  language,\r\n  zoom = 1,\r\n  minZoom = 1,\r\n  onClose\r\n}) => {\r\n  const { locale } = useRouter();\r\n  const { isLoaded, loadError } =  useGoogleMaps();\r\n  const containerStyle = {\r\n    width: width,\r\n    height: typeof height === 'number' ? `${height}px` : height,\r\n  };\r\n\r\n  const defaultCenter = {\r\n    lat: 52.520017,\r\n    lng: 13.404195,\r\n  };\r\n\r\n  const center = initialCenter || defaultCenter;\r\n\r\n  const onMapLoad = (map: google.maps.Map) => {\r\n    map.setOptions({\r\n      styles: mapStyles,\r\n    });\r\n  };\r\n\r\n  if (loadError) return <div>Error loading maps</div>;\r\n  if (!isLoaded) return <div>Loading Maps...</div>;\r\n\r\n  return (\r\n    <div className=\"map-container\">\r\n      <div className=\"mapprint\" style={{ width, height, position: 'relative' }}>\r\n        <GoogleMap\r\n          mapContainerStyle={containerStyle}\r\n          center={center}\r\n          zoom={zoom}\r\n          onLoad={onMapLoad}\r\n          options={{\r\n            minZoom: minZoom,\r\n            draggable: true,\r\n            keyboardShortcuts: false,\r\n            streetViewControl: false,\r\n            panControl: false,\r\n            clickableIcons: false,\r\n            mapTypeControl: false,\r\n            fullscreenControl: true,\r\n          }}\r\n        >\r\n          {children}\r\n          {markerInfo && activeMarker && activeMarker.getPosition && (\r\n            <RKIMapInfowindow\r\n              position={activeMarker.getPosition()}\r\n              onCloseClick={() => {\r\n                // Handle close if needed\r\n                console.log('close click');\r\n                onClose?.()\r\n              }}\r\n            >\r\n              {markerInfo}\r\n            </RKIMapInfowindow>\r\n          )}\r\n        </GoogleMap>\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default RKIMap1;\r\n", "//Import Library\r\nimport React from \"react\";\r\nimport { Col, Row } from \"react-bootstrap\";\r\nimport Link from \"next/link\";\r\n\r\n//Import services/components\r\nimport RKIMap1 from \"../../../components/common/RKIMap1\";\r\nimport RKIMapMarker from \"../../../components/common/RKIMapMarker\";\r\nimport { useTranslation } from 'next-i18next';\r\n\r\n\r\ninterface CountryCoverSectionProps {\r\n  latlng: {\r\n    lat: number;\r\n    lng: number;\r\n  };\r\n  countryData: {\r\n    title: string;\r\n    health_profile: string;\r\n    security_advice: string;\r\n    _id: string;\r\n  };\r\n  operationStatusId: any;\r\n  countryStatsData: {\r\n    operation_count: number;\r\n    project_count: number;\r\n    event_count: number;\r\n  };\r\n  eventStatusId: any;\r\n  projectStatusId: any;\r\n}\r\n\r\nconst CountryCoverSection = (props: CountryCoverSectionProps) => {\r\n    const { t } = useTranslation('common');\r\n    return (\r\n        <>\r\n            <Row>\r\n                <Col xs={12} style={{ display: \"flex\" }}>\r\n                    <div className=\"countryMap\">\r\n                        {props?.latlng?.lat ? (\r\n                            <RKIMap1 initialCenter={props.latlng}>\r\n                                <RKIMapMarker\r\n                                    icon={{\r\n                                        url: \"/images/map-marker-blue.svg\",\r\n                                    }}\r\n                                    position={props.latlng}\r\n                                />\r\n                            </RKIMap1>\r\n                        ) : null}\r\n                    </div>\r\n                    <div className=\"countryInfo\">\r\n                        <h4> {props?.countryData?.title} </h4>\r\n                        {Country_func(\r\n                            props.countryData,\r\n                            props.operationStatusId,\r\n                            t,\r\n                            props.countryStatsData,\r\n                            props.eventStatusId,\r\n                            props.projectStatusId\r\n                        )}\r\n                    </div>\r\n                </Col>\r\n            </Row>\r\n        </>\r\n    )\r\n}\r\n\r\nexport default CountryCoverSection;\r\n\r\nfunction Country_func(\r\n    countryData: {\r\n      title: string;\r\n      health_profile: string;\r\n      security_advice: string;\r\n      _id: string;\r\n    },\r\n    operationStatusId: any,\r\n    t: (key: string) => string,\r\n    countryStatsData: {\r\n      operation_count: number;\r\n      project_count: number;\r\n      event_count: number;\r\n    },\r\n    eventStatusId: any,\r\n    projectStatusId: any\r\n  ) {\r\n    return (\r\n      <div className=\"countryInfoDetails\">\r\n        <Link\r\n          href={{\r\n            pathname: \"/operation\",\r\n            query: {\r\n              country: countryData?._id,\r\n              status: operationStatusId,\r\n            },\r\n          }}\r\n        >\r\n\r\n          <div className=\"countryInfo-Item\">\r\n            <div className=\"countryInfo-img\">\r\n              <img\r\n                src=\"/images/countryinfo1.png\"\r\n                width=\"25\"\r\n                height=\"25\"\r\n                alt=\"Organization Quick Info\"\r\n              />\r\n            </div>\r\n            <span>\r\n              {t(\"CurrentOperation\")}\r\n              <br />\r\n              <b>\r\n                {countryStatsData?.operation_count\r\n                  ? countryStatsData.operation_count\r\n                  : 0}\r\n              </b>\r\n            </span>\r\n          </div>\r\n\r\n        </Link>\r\n        <Link\r\n          href={{\r\n            pathname: \"/event\",\r\n            query: {\r\n              country: countryData?._id,\r\n              status: eventStatusId,\r\n            },\r\n          }}\r\n        >\r\n\r\n          <div className=\"countryInfo-Item\">\r\n            <div className=\"countryInfo-img\">\r\n              <img\r\n                src=\"/images/countryinfo2.png\"\r\n                width=\"30\"\r\n                height=\"22\"\r\n                alt=\"Organization Quick Info\"\r\n              />\r\n            </div>\r\n            <span>\r\n              {t(\"CurrentEvent\")}\r\n              <br />\r\n              <b>\r\n                {countryStatsData?.event_count\r\n                  ? countryStatsData.event_count\r\n                  : 0}\r\n              </b>\r\n            </span>\r\n          </div>\r\n\r\n        </Link>\r\n        <Link\r\n          href={{\r\n            pathname: \"/project\",\r\n            query: { country: countryData._id, status: projectStatusId },\r\n          }}\r\n        >\r\n\r\n          <div className=\"countryInfo-Item\">\r\n            <div className=\"countryInfo-img\">\r\n              <img\r\n                src=\"/images/quickinfo3.png\"\r\n                width=\"24\"\r\n                height=\"21\"\r\n                alt=\"Organization Quick Info\"\r\n              />\r\n            </div>\r\n            <span>\r\n              {t(\"CurrentProject\")}\r\n              <br />\r\n              <b>\r\n                {countryStatsData?.project_count\r\n                  ? countryStatsData.project_count\r\n                  : 0}\r\n              </b>\r\n            </span>\r\n          </div>\r\n\r\n        </Link>\r\n      </div>\r\n    );\r\n  }", "\n    (window.__NEXT_P = window.__NEXT_P || []).push([\n      \"/country/components/CountryCoverSection\",\n      function () {\n        return require(\"private-next-pages/country/components/CountryCoverSection.tsx\");\n      }\n    ]);\n    if(module.hot) {\n      module.hot.dispose(function () {\n        window.__NEXT_P.push([\"/country/components/CountryCoverSection\"])\n      });\n    }\n  "], "names": ["name", "id", "R<PERSON>IMapMarker", "countryId", "type", "icon", "position", "onClick", "title", "draggable", "lat", "lng", "<PERSON><PERSON>", "handleClick", "markerProps", "marker", "getPosition", "e", "RKIMapInfowindow", "onCloseClick", "children", "InfoWindow", "div", "fill", "stoke", "road", "geometry", "mapStyles", "RKIMap1", "markerInfo", "activeMarker", "initialCenter", "height", "width", "language", "zoom", "minZoom", "onClose", "locale", "useRouter", "isLoaded", "loadError", "useGoogleMaps", "className", "style", "GoogleMap", "mapContainerStyle", "containerStyle", "center", "defaultCenter", "onLoad", "onMapLoad", "map", "setOptions", "styles", "options", "keyboardShortcuts", "streetViewControl", "panControl", "clickableIcons", "mapTypeControl", "fullscreenControl", "console", "log", "props", "t", "useTranslation", "Row", "Col", "xs", "display", "latlng", "url", "h4", "countryData", "operationStatusId", "countryStatsData", "eventStatusId", "projectStatusId", "Link", "href", "pathname", "query", "country", "_id", "status", "img", "src", "alt", "span", "br", "b", "operation_count", "event_count", "project_count"], "sourceRoot": "", "ignoreList": []}