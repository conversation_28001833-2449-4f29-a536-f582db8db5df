"use strict";(()=>{var e={};e.id=6171,e.ids=[636,3220,6171],e.modules={123:e=>{e.exports=require("dom-helpers/removeEventListener")},1332:e=>{e.exports=require("react-custom-scrollbars-2")},1428:e=>{e.exports=import("axios")},1680:e=>{e.exports=require("@restart/ui/utils")},1919:e=>{e.exports=require("react-transition-group/Transition")},3892:e=>{e.exports=require("classnames")},4048:e=>{e.exports=require("react-truncate")},6009:e=>{e.exports=require("dom-helpers/ownerDocument")},6952:e=>{e.exports=require("@restart/ui/Modal")},7374:e=>{e.exports=require("@restart/ui/Dropdown")},8732:e=>{e.exports=require("react/jsx-runtime")},9653:e=>{e.exports=require("@restart/ui/Overlay")},11242:e=>{e.exports=require("redux-persist/integration/react")},11688:e=>{e.exports=require("@restart/hooks/useWillUnmount")},12388:e=>{e.exports=require("dom-helpers/addEventListener")},13364:e=>{e.exports=require("redux-saga/effects")},14062:e=>{e.exports=import("react-redux")},14078:e=>{e.exports=import("swr")},14332:e=>{e.exports=require("uncontrollable")},16116:e=>{e.exports=require("invariant")},16567:(e,r,t)=>{t.a(e,async(e,s)=>{try{t.r(r),t.d(r,{default:()=>d});var i=t(8732),o=t(82015),n=t(54131),a=t(82053),u=t(83551),p=t(49481),c=t(88751),l=t(76357),x=e([n]);n=(x.then?(await x)():x)[0];let d=e=>{let{t:r}=(0,c.useTranslation)("common"),[t,s]=(0,o.useState)(!1),[x,d]=(0,o.useState)([]),[h,m]=(0,o.useState)(""),q=i=>{switch(s(!t),m(r(i)),i){case"Partners":let o=e.institutionData&&e.institutionData.partners?e.institutionData.partners:[];d(o);break;case"Operations":let n=e.institutionStatus&&e.institutionStatus.operationData?e.institutionStatus.operationData:[];d(n);break;case"Projects":let a=e.institutionStatus&&e.institutionStatus.projectData?e.institutionStatus.projectData:[];d(a)}},f=e=>{s(e)};return(0,i.jsx)(i.Fragment,{children:(0,i.jsxs)("div",{className:"institution-infographic-block",children:[(0,i.jsxs)(u.A,{children:[function(e,r,t){return(0,i.jsx)(p.A,{children:(0,i.jsxs)("div",{className:"list-group-item d-flex clickable",onClick:()=>e("Partners"),children:[(0,i.jsx)("div",{className:"quickinfo-img",children:(0,i.jsx)(a.FontAwesomeIcon,{icon:n.faUsers,color:"#fff",size:"2x"})}),(0,i.jsxs)("div",{className:"quickinfoDesc",children:[(0,i.jsx)("h5",{children:r("Partners")}),(0,i.jsx)("h4",{children:t&&t.partners?t.partners:0})]})]})})}(q,r,e.institutionStatus),(0,i.jsx)(p.A,{children:(0,i.jsxs)("div",{className:"list-group-item d-flex clickable",onClick:()=>q("Operations"),children:[(0,i.jsx)("div",{className:"quickinfo-img",children:(0,i.jsx)(a.FontAwesomeIcon,{icon:n.faLayerGroup,color:"#fff",size:"2x"})}),(0,i.jsxs)("div",{className:"quickinfoDesc",children:[(0,i.jsx)("h5",{children:r("Operations")}),(0,i.jsx)("h4",{children:e.institutionStatus&&e.institutionStatus.operations?e.institutionStatus.operations:0})]})]})}),(0,i.jsx)(p.A,{children:function(e,r,t){return(0,i.jsxs)("div",{className:"list-group-item d-flex clickable",onClick:()=>e("Projects"),children:[(0,i.jsx)("div",{className:"quickinfo-img",children:(0,i.jsx)(a.FontAwesomeIcon,{icon:n.faFolderOpen,color:"#fff",size:"2x"})}),(0,i.jsxs)("div",{className:"quickinfoDesc",children:[(0,i.jsx)("h5",{children:r("Projects")}),(0,i.jsx)("h4",{children:t&&t.projects?t.projects:0})]})]})}(q,r,e.institutionStatus)})]}),(0,i.jsx)(l.default,{isShow:t,isClose:e=>f(e),data:x,name:h})]})})};s()}catch(e){s(e)}})},18622:e=>{e.exports=require("yup")},19121:e=>{e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},22326:e=>{e.exports=require("react-dom")},22541:e=>{e.exports=require("redux-persist/lib/storage")},25303:e=>{e.exports=require("@restart/ui/NavItem")},26324:e=>{e.exports=require("warning")},27825:e=>{e.exports=require("lodash")},27910:e=>{e.exports=require("stream")},28217:e=>{e.exports=require("@restart/ui/DropdownItem")},29021:e=>{e.exports=require("fs")},29294:e=>{e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},29628:e=>{e.exports=require("dom-helpers/addClass")},29825:e=>{e.exports=require("prop-types")},29841:e=>{e.exports=require("dom-helpers/hasClass")},30362:e=>{e.exports=require("@restart/ui/DropdownToggle")},33873:e=>{e.exports=require("path")},36653:e=>{e.exports=require("nprogress")},36955:e=>{e.exports=require("@restart/hooks/useBreakpoint")},37766:e=>{e.exports=require("@restart/hooks/useMergedRefs")},39756:e=>{e.exports=import("redux")},40051:e=>{e.exports=require("dom-helpers/removeClass")},40361:e=>{e.exports=require("next/dist/compiled/next-server/pages.runtime.prod.js")},42893:e=>{e.exports=import("react-hot-toast")},43294:e=>{e.exports=require("formik")},50009:e=>{e.exports=require("@restart/ui/DropdownMenu")},54131:e=>{e.exports=import("@fortawesome/free-solid-svg-icons")},57664:e=>{e.exports=require("dom-helpers/scrollbarSize")},58928:e=>{e.exports=require("dom-helpers/canUseDOM")},59717:e=>{e.exports=require("@restart/ui/DropdownContext")},60560:e=>{e.exports=require("dom-helpers/contains")},63033:e=>{e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},65447:e=>{e.exports=require("@restart/ui/Button")},67364:e=>{e.exports=require("@restart/hooks/useCallbackRef")},68455:e=>{e.exports=import("redux-saga")},69722:e=>{e.exports=require("es6-promise")},74075:e=>{e.exports=require("zlib")},74716:e=>{e.exports=require("moment")},74987:e=>{e.exports=require("@restart/ui/ModalManager")},76357:(e,r,t)=>{t.r(r),t.d(r,{default:()=>u});var s=t(8732);t(82015);var i=t(12403),o=t(19918),n=t.n(o),a=t(88751);let u=e=>{let{t:r}=(0,a.useTranslation)("common"),{isShow:t,isClose:o,data:u,name:p}=e,c="Projects"===p?"project":"Partners"===p?"institution":"operation",l=u.length>0?u.map((e,r)=>(0,s.jsxs)("span",{children:[(0,s.jsx)(n(),{href:`/${c}/show/${e._id}`,children:e.title}),(0,s.jsx)("hr",{})]},r)):(0,s.jsxs)("p",{children:[r("No")," ",p," ",r("Found"),"."]});return(0,s.jsxs)(i.A,{centered:!0,size:"sm",show:t,onHide:()=>o(!t),"aria-labelledby":"modal_popup",children:[(0,s.jsx)(i.A.Header,{closeButton:!0,children:(0,s.jsx)(i.A.Title,{children:p})}),(0,s.jsx)(i.A.Body,{children:(0,s.jsx)("div",{children:l})})]})}},78097:e=>{e.exports=require("next-redux-saga")},78634:e=>{e.exports=require("@restart/ui/Anchor")},80237:(e,r)=>{Object.defineProperty(r,"A",{enumerable:!0,get:function(){return t}});var t=function(e){return e.PAGES="PAGES",e.PAGES_API="PAGES_API",e.APP_PAGE="APP_PAGE",e.APP_ROUTE="APP_ROUTE",e.IMAGE="IMAGE",e}({})},81366:e=>{e.exports=require("redux-auth-wrapper/connectedAuthWrapper")},81413:(e,r)=>{Object.defineProperty(r,"M",{enumerable:!0,get:function(){return function e(r,t){return t in r?r[t]:"then"in r&&"function"==typeof r.then?r.then(r=>e(r,t)):"function"==typeof r&&"default"===t?r:void 0}}})},81521:e=>{e.exports=require("dom-helpers/querySelectorAll")},81895:e=>{e.exports=require("@restart/hooks/useEventCallback")},82015:e=>{e.exports=require("react")},82053:e=>{e.exports=require("@fortawesome/react-fontawesome")},86842:e=>{e.exports=require("@restart/ui/SelectableContext")},87571:e=>{e.exports=require("dom-helpers/transitionEnd")},88751:e=>{e.exports=require("next-i18next")},92239:(e,r,t)=>{t.a(e,async(e,s)=>{try{t.r(r),t.d(r,{config:()=>q,default:()=>x,getServerSideProps:()=>m,getStaticPaths:()=>h,getStaticProps:()=>d,reportWebVitals:()=>f,routeModule:()=>b,unstable_getServerProps:()=>S,unstable_getServerSideProps:()=>v,unstable_getStaticParams:()=>P,unstable_getStaticPaths:()=>g,unstable_getStaticProps:()=>j});var i=t(63885),o=t(80237),n=t(81413),a=t(9616),u=t.n(a),p=t(72386),c=t(16567),l=e([p,c]);[p,c]=l.then?(await l)():l;let x=(0,n.M)(c,"default"),d=(0,n.M)(c,"getStaticProps"),h=(0,n.M)(c,"getStaticPaths"),m=(0,n.M)(c,"getServerSideProps"),q=(0,n.M)(c,"config"),f=(0,n.M)(c,"reportWebVitals"),j=(0,n.M)(c,"unstable_getStaticProps"),g=(0,n.M)(c,"unstable_getStaticPaths"),P=(0,n.M)(c,"unstable_getStaticParams"),S=(0,n.M)(c,"unstable_getServerProps"),v=(0,n.M)(c,"unstable_getServerSideProps"),b=new i.PagesRouteModule({definition:{kind:o.A.PAGES,page:"/institution/components/InstitutionInfoSection",pathname:"/institution/components/InstitutionInfoSection",bundlePath:"",filename:""},components:{App:p.default,Document:u()},userland:c});s()}catch(e){s(e)}})},93787:e=>{e.exports=require("redux-persist")},94947:e=>{e.exports=require("@restart/hooks/useTimeout")},96196:e=>{e.exports=require("next-redux-wrapper")},98320:e=>{e.exports=require("@restart/hooks/useIsomorphicEffect")},99460:e=>{e.exports=require("dom-helpers/css")}};var r=require("../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[6089,9216,9616,2386],()=>t(92239));module.exports=s})();