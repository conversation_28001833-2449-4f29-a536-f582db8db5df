{"version": 3, "file": "static/chunks/pages/dashboard/ActiveProjectOperations-9713a89f72944185.js", "mappings": "gFACA,4CACA,qCACA,WACA,OAAe,EAAQ,IAA0D,CACjF,EACA,UAFsB", "sources": ["webpack://_N_E/?b7d4"], "sourcesContent": ["\n    (window.__NEXT_P = window.__NEXT_P || []).push([\n      \"/dashboard/ActiveProjectOperations\",\n      function () {\n        return require(\"private-next-pages/dashboard/ActiveProjectOperations.tsx\");\n      }\n    ]);\n    if(module.hot) {\n      module.hot.dispose(function () {\n        window.__NEXT_P.push([\"/dashboard/ActiveProjectOperations\"])\n      });\n    }\n  "], "names": [], "sourceRoot": "", "ignoreList": []}