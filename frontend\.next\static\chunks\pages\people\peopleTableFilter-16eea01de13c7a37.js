(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[705],{65362:(t,i,e)=>{(window.__NEXT_P=window.__NEXT_P||[]).push(["/people/peopleTableFilter",function(){return e(72178)}])},72178:(t,i,e)=>{"use strict";e.r(i),e.d(i,{default:()=>d});var n=e(37876),s=e(49589),a=e(56970),o=e(37784),r=e(2827),u=e(14232),l=e(53718),c=e(31753);let d=t=>{let{filterText:i,onFilter:e,onClear:d,roles:p,onHandleSearch:_,institutions:m,onKeyPress:h}=t,{t:w}=(0,c.Bd)("common"),[f,y]=(0,u.useState)([]),[,b]=(0,u.useState)(!1),g={sort:{created_at:"desc"},limit:"~",page:1,query:{},select:"-country -dial_code -firstname -region -institution.address -institution.contact_name -institution.description -institution.dial_code -institution.document -institution.doc_src -institution.images -institution.images_src -institution.expertise -institution.hazards -institution.hazard_types -institution.header -institution.networks -institution.partners -institution.telephone -institution.twitter -institution.type -institution.unit -institution.user -mobile_number -position"},A=async()=>{b(!0);let t=await l.A.get("/users",g);t&&Array.isArray(t.data)&&(y(t.data.map((t,i)=>({label:t.username,value:t._id}))),b(!1))};return(0,u.useEffect)(()=>{A()},[]),(0,n.jsx)(s.A,{fluid:!0,className:"p-0",children:(0,n.jsx)(a.A,{children:(0,n.jsx)(o.A,{xs:12,md:6,lg:4,className:"p-0 me-3",children:(0,n.jsx)(r.Ay,{autoFocus:!0,isClearable:!0,isSearchable:!0,onKeyDown:h,onChange:e,placeholder:w("People.form.UsernameorEmail"),options:f})})})})}}},t=>{var i=i=>t(t.s=i);t.O(0,[698,2827,636,6593,8792],()=>i(65362)),_N_E=t.O()}]);
//# sourceMappingURL=peopleTableFilter-16eea01de13c7a37.js.map