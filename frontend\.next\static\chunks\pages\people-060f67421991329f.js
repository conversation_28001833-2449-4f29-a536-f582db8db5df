(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[4137],{50749:(e,t,i)=>{"use strict";i.d(t,{A:()=>u});var s=i(37876);i(14232);var n=i(89773),a=i(31753),o=i(5507);function r(e){let{t}=(0,a.Bd)("common"),i={rowsPerPageText:t("Rowsperpage")},{columns:r,data:u,totalRows:l,resetPaginationToggle:d,subheader:c,subHeaderComponent:m,handlePerRowsChange:p,handlePageChange:g,rowsPerPage:h,defaultRowsPerPage:_,selectableRows:f,loading:y,pagServer:P,onSelectedRowsChange:w,clearSelectedRows:x,sortServer:A,onSort:b,persistTableHead:S,sortFunction:j,...v}=e,R={paginationComponentOptions:i,noDataComponent:t("NoData"),noHeader:!0,columns:r,data:u||[],dense:!0,paginationResetDefaultPage:d,subHeader:c,progressPending:y,subHeaderComponent:m,pagination:!0,paginationServer:P,paginationPerPage:_||10,paginationRowsPerPageOptions:h||[10,15,20,25,30],paginationTotalRows:l,onChangeRowsPerPage:p,onChangePage:g,selectableRows:f,onSelectedRowsChange:w,clearSelectedRows:x,progressComponent:(0,s.jsx)(o.A,{}),sortIcon:(0,s.jsx)("i",{className:"sort-icon fas fa-exchange-alt"}),sortServer:A,onSort:b,sortFunction:j,persistTableHead:S,className:"rki-table"};return(0,s.jsx)(n.Ay,{...R})}r.defaultProps={subHeader:!1,pagination:!0,totalRows:null,pagServer:!0,onSelectedRowsChange:null,clearSelectedRows:!1,sortServer:!1,persistTableHead:!1};let u=r},51009:(e,t,i)=>{(window.__NEXT_P=window.__NEXT_P||[]).push(["/people",function(){return i(85975)}])},63128:(e,t,i)=>{"use strict";i.r(t),i.d(t,{default:()=>l});var s=i(37876),n=i(14232),a=i(72178),o=i(50749),r=i(53718),u=i(31753);let l=function(e){let[t,i]=(0,n.useState)([]),{t:l}=(0,u.Bd)("common"),[d,c]=(0,n.useState)(!1),[m,p]=(0,n.useState)(0),[g,h]=(0,n.useState)(10),[_]=(0,n.useState)([]),[f]=(0,n.useState)([]),[y,P]=(0,n.useState)(""),[w,x]=(0,n.useState)(null),[A,b]=(0,n.useState)(!1),S={sort:{created_at:"desc"},limit:g,page:1,query:{status:{$ne:"Request Pending"},vspace_status:{$ne:"Request Pending"}},select:"-country -dial_code -firstname -region -institution.address -institution.contact_name -institution.description -institution.dial_code -institution.document -institution.doc_src -institution.images -institution.images_src -institution.expertise -institution.hazards -institution.hazard_types -institution.header -institution.networks -institution.partners -institution.telephone -institution.twitter -institution.type -institution.unit -institution.user -mobile_number -position"},j=[{name:l("People.form.Username"),selector:"username",cell:e=>e.username,sortable:!0},{name:l("People.form.Email"),selector:"email",cell:e=>e.email,sortable:!0},{name:l("People.form.Role"),selector:"roles",cell:e=>e.roles?e.roles[0]:"",sortable:!0},{name:l("People.form.Organisation"),selector:"institution",cell:e=>e.institution&&e.institution.title?e.institution.title:"",sortable:!0}],v=async e=>{c(!0);let t=await r.A.get("/users",e);t&&Array.isArray(t.data)&&(i(t.data),p(t.totalCount),c(!1))},R=async(e,t)=>{c(!0),S.sort={[e.selector]:t},await v(S),x(S),c(!1)},C=async(e,t)=>{S.limit=e,S.page=t,c(!0);let s=await r.A.get("/users",S);s&&Array.isArray(s.data)&&(i(s.data),h(e),c(!1))};(0,n.useEffect)(()=>{v(S)},[]);let q=n.useMemo(()=>{let e=e=>{e&&(/^[^@]+@[^@]+\.[^@]+$/.test(e.toLowerCase())?S.query={...S.query,email:e}:S.query={...S.query,username:e}),v(S),S.query={status:{$ne:"Request Pending"},vspace_status:{$ne:"Request Pending"}}},t=()=>{i()},i=()=>{e(y)};return(0,s.jsx)(a.default,{onFilter:t=>{t&&t.label?(P(t.label),e(t.label)):(S.query={status:{$ne:"Request Pending"},vspace_status:{$ne:"Request Pending"}},P(""),v(S))},onClear:()=>{y&&(b(!A),P(""))},filterText:y,roles:_,onHandleSearch:i,institutions:f,onKeyPress:e=>{"Enter"===e.key&&t()}})},[y]);return(0,s.jsx)("div",{children:(0,s.jsx)(o.A,{columns:j,data:t,totalRows:m,subheader:!0,persistTableHead:!0,loading:d,onSort:R,sortServer:!0,pagServer:!0,resetPaginationToggle:A,subHeaderComponent:q,handlePerRowsChange:C,handlePageChange:e=>{S.limit=g,S.page=e,w&&(S.sort=w.sort),v(S)}})})}},69600:(e,t,i)=>{"use strict";i.d(t,{A:()=>n});var s=i(37876);function n(e){return(0,s.jsx)("h2",{className:"page-heading",children:e.title})}},72178:(e,t,i)=>{"use strict";i.r(t),i.d(t,{default:()=>c});var s=i(37876),n=i(49589),a=i(56970),o=i(37784),r=i(2827),u=i(14232),l=i(53718),d=i(31753);let c=e=>{let{filterText:t,onFilter:i,onClear:c,roles:m,onHandleSearch:p,institutions:g,onKeyPress:h}=e,{t:_}=(0,d.Bd)("common"),[f,y]=(0,u.useState)([]),[,P]=(0,u.useState)(!1),w={sort:{created_at:"desc"},limit:"~",page:1,query:{},select:"-country -dial_code -firstname -region -institution.address -institution.contact_name -institution.description -institution.dial_code -institution.document -institution.doc_src -institution.images -institution.images_src -institution.expertise -institution.hazards -institution.hazard_types -institution.header -institution.networks -institution.partners -institution.telephone -institution.twitter -institution.type -institution.unit -institution.user -mobile_number -position"},x=async()=>{P(!0);let e=await l.A.get("/users",w);e&&Array.isArray(e.data)&&(y(e.data.map((e,t)=>({label:e.username,value:e._id}))),P(!1))};return(0,u.useEffect)(()=>{x()},[]),(0,s.jsx)(n.A,{fluid:!0,className:"p-0",children:(0,s.jsx)(a.A,{children:(0,s.jsx)(o.A,{xs:12,md:6,lg:4,className:"p-0 me-3",children:(0,s.jsx)(r.Ay,{autoFocus:!0,isClearable:!0,isSearchable:!0,onKeyDown:h,onChange:i,placeholder:_("People.form.UsernameorEmail"),options:f})})})})}},85975:(e,t,i)=>{"use strict";i.r(t),i.d(t,{__N_SSG:()=>d,default:()=>c});var s=i(37876);i(14232);var n=i(49589),a=i(56970),o=i(37784),r=i(63128),u=i(69600),l=i(31753),d=!0;let c=()=>{let{t:e}=(0,l.Bd)("common");return(0,s.jsxs)(n.A,{fluid:!0,className:"p-0",children:[(0,s.jsx)(a.A,{children:(0,s.jsx)(o.A,{xs:12,children:(0,s.jsx)(u.A,{title:e("menu.people")})})}),(0,s.jsx)(a.A,{className:"mt-3",children:(0,s.jsx)(o.A,{xs:12,children:(0,s.jsx)(r.default,{})})})]})}}},e=>{var t=t=>e(e.s=t);e.O(0,[9773,698,2827,636,6593,8792],()=>t(51009)),_N_E=e.O()}]);
//# sourceMappingURL=people-060f67421991329f.js.map