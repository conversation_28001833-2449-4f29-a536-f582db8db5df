(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[9219],{25150:(e,o,a)=>{"use strict";a.r(o),a.d(o,{default:()=>l});var n=a(37876),t=a(14232),c=a(32890),r=a(21772),s=a(11041),i=a(66404),d=a(31753);let l=e=>{let{t:o}=(0,d.Bd)("common"),[a,l]=(0,t.useState)(!1);return(0,n.jsx)(n.Fragment,{children:(0,n.jsxs)(c.A.Item,{eventKey:"0",children:[(0,n.jsxs)(c.<PERSON><PERSON>,{onClick:()=>l(!a),children:[(0,n.jsx)("div",{className:"cardTitle",children:o("Documents")}),(0,n.jsx)("div",{className:"cardArrow",children:a?(0,n.jsx)(r.g,{icon:s.EZy,color:"#fff"}):(0,n.jsx)(r.g,{icon:s.QLR,color:"#fff"})})]}),(0,n.jsxs)(c.A.Body,{children:[(0,n.jsx)(i.A,{loading:e.documentAccoirdianData.documentAccoirdianProps.loading,sortProps:e.documentAccoirdianData.documentAccoirdianProps.sortProps,docs:e.documentAccoirdianData.documentAccoirdianProps.Document||[],docsDescription:e.documentAccoirdianData.operationData.doc_src}),(0,n.jsx)("h6",{className:"mt-3",children:o("DocumentsfromUpdates")}),(0,n.jsx)(i.A,{loading:e.documentAccoirdianData.documentAccoirdianProps.loading,sortProps:e.documentAccoirdianData.documentAccoirdianProps.sortUpdateProps,docs:e.documentAccoirdianData.documentAccoirdianProps.updateDocument||[],docsDescription:e.documentAccoirdianData.operationData.doc_src})]})]})})}},26176:(e,o,a)=>{(window.__NEXT_P=window.__NEXT_P||[]).push(["/operation/components/DocumentsAccordian",function(){return a(25150)}])},50749:(e,o,a)=>{"use strict";a.d(o,{A:()=>i});var n=a(37876);a(14232);var t=a(89773),c=a(31753),r=a(5507);function s(e){let{t:o}=(0,c.Bd)("common"),a={rowsPerPageText:o("Rowsperpage")},{columns:s,data:i,totalRows:d,resetPaginationToggle:l,subheader:p,subHeaderComponent:m,handlePerRowsChange:u,handlePageChange:g,rowsPerPage:A,defaultRowsPerPage:D,selectableRows:P,loading:h,pagServer:_,onSelectedRowsChange:x,clearSelectedRows:f,sortServer:w,onSort:j,persistTableHead:v,sortFunction:N,...b}=e,R={paginationComponentOptions:a,noDataComponent:o("NoData"),noHeader:!0,columns:s,data:i||[],dense:!0,paginationResetDefaultPage:l,subHeader:p,progressPending:h,subHeaderComponent:m,pagination:!0,paginationServer:_,paginationPerPage:D||10,paginationRowsPerPageOptions:A||[10,15,20,25,30],paginationTotalRows:d,onChangeRowsPerPage:u,onChangePage:g,selectableRows:P,onSelectedRowsChange:x,clearSelectedRows:f,progressComponent:(0,n.jsx)(r.A,{}),sortIcon:(0,n.jsx)("i",{className:"sort-icon fas fa-exchange-alt"}),sortServer:w,onSort:j,sortFunction:N,persistTableHead:v,className:"rki-table"};return(0,n.jsx)(t.Ay,{...R})}s.defaultProps={subHeader:!1,pagination:!0,totalRows:null,pagServer:!0,onSelectedRowsChange:null,clearSelectedRows:!1,sortServer:!1,persistTableHead:!1};let i=s},66404:(e,o,a)=>{"use strict";a.d(o,{A:()=>i});var n=a(37876);a(14232);var t=a(10841),c=a.n(t),r=a(50749),s=a(31753);let i=e=>{let{docs:o,docsDescription:a,sortProps:t,loading:i}=e,d=async(e,o)=>{t({columnSelector:e.selector,sortDirection:o})},{t:l}=(0,s.Bd)("common"),p=[{name:l("FileType"),width:"15%",selector:"extension",cell:e=>e&&e.extension&&e.extension},{name:l("FileName"),width:"25%",selector:"document_title",cell:e=>e&&e.original_name&&(0,n.jsx)("a",{href:"".concat("http://localhost:3001/api/v1","/files/download/").concat(e._id),target:"_blank",children:e.original_name.split(".").slice(0,-1).join(".")}),sortable:!0},{name:l("Description"),selector:"description",cell:e=>e&&e.description&&e.description},{name:l("UploadedDate"),width:"25%",selector:"doc_created_at",cell:e=>e&&e.updated_at&&c()(e.updated_at).format("MM/DD/YYYY"),sortable:!0}];return(0,n.jsx)(r.A,{columns:p,data:o,pagServer:!0,onSort:d,persistTableHead:!0,loading:i})}}},e=>{var o=o=>e(e.s=o);e.O(0,[7725,9773,1772,636,6593,8792],()=>o(26176)),_N_E=e.O()}]);
//# sourceMappingURL=DocumentsAccordian-7aaa20ffeaff69fd.js.map