"use strict";(()=>{var e={};e.id=8522,e.ids=[636,3220,8522],e.modules={123:e=>{e.exports=require("dom-helpers/removeEventListener")},1332:e=>{e.exports=require("react-custom-scrollbars-2")},1428:e=>{e.exports=import("axios")},1680:e=>{e.exports=require("@restart/ui/utils")},1919:e=>{e.exports=require("react-transition-group/Transition")},3892:e=>{e.exports=require("classnames")},4048:e=>{e.exports=require("react-truncate")},6009:e=>{e.exports=require("dom-helpers/ownerDocument")},6952:e=>{e.exports=require("@restart/ui/Modal")},7374:e=>{e.exports=require("@restart/ui/Dropdown")},8732:e=>{e.exports=require("react/jsx-runtime")},9653:e=>{e.exports=require("@restart/ui/Overlay")},11242:e=>{e.exports=require("redux-persist/integration/react")},11688:e=>{e.exports=require("@restart/hooks/useWillUnmount")},12388:e=>{e.exports=require("dom-helpers/addEventListener")},13364:e=>{e.exports=require("redux-saga/effects")},14062:e=>{e.exports=import("react-redux")},14078:e=>{e.exports=import("swr")},14332:e=>{e.exports=require("uncontrollable")},16116:e=>{e.exports=require("invariant")},18622:e=>{e.exports=require("yup")},19121:e=>{e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},22326:e=>{e.exports=require("react-dom")},22541:e=>{e.exports=require("redux-persist/lib/storage")},25303:e=>{e.exports=require("@restart/ui/NavItem")},26324:e=>{e.exports=require("warning")},27053:(e,t,r)=>{r.d(t,{A:()=>a});var s=r(8732);function a(e){return(0,s.jsx)("h2",{className:"page-heading",children:e.title})}},27825:e=>{e.exports=require("lodash")},27910:e=>{e.exports=require("stream")},28217:e=>{e.exports=require("@restart/ui/DropdownItem")},29021:e=>{e.exports=require("fs")},29294:e=>{e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},29628:e=>{e.exports=require("dom-helpers/addClass")},29825:e=>{e.exports=require("prop-types")},29841:e=>{e.exports=require("dom-helpers/hasClass")},30362:e=>{e.exports=require("@restart/ui/DropdownToggle")},33873:e=>{e.exports=require("path")},35557:(e,t,r)=>{r.r(t),r.d(t,{default:()=>a});var s=r(8732);function a(e){return(0,s.jsx)("div",{className:"container-fluid p-0 response-message-block",children:(0,s.jsx)("div",{className:"message",children:"you don't have permission to access"})})}},36653:e=>{e.exports=require("nprogress")},36955:e=>{e.exports=require("@restart/hooks/useBreakpoint")},37766:e=>{e.exports=require("@restart/hooks/useMergedRefs")},38609:e=>{e.exports=require("react-data-table-component")},39756:e=>{e.exports=import("redux")},40051:e=>{e.exports=require("dom-helpers/removeClass")},40361:e=>{e.exports=require("next/dist/compiled/next-server/pages.runtime.prod.js")},42893:e=>{e.exports=import("react-hot-toast")},43294:e=>{e.exports=require("formik")},45080:(e,t,r)=>{r.a(e,async(e,s)=>{try{r.r(t),r.d(t,{default:()=>g});var a=r(8732),i=r(82015),n=r(19918),o=r.n(n),p=r(12403),d=r(91353),u=r(42893),c=r(56084),l=r(63487),m=r(88751),x=e([u,l]);[u,l]=x.then?(await x)():x;let g=e=>{let{t}=(0,m.useTranslation)("common"),[r,s]=(0,i.useState)([]),[,n]=(0,i.useState)(!1),[x,g]=(0,i.useState)(0),[h,A]=(0,i.useState)(10),[S,y]=(0,i.useState)(!1),[j,P]=(0,i.useState)({}),q={sort:{title:"asc"},limit:h,page:1,query:{}},v=[{name:t("adminsetting.ProjectStatus.Title"),selector:"title"},{name:t("adminsetting.ProjectStatus.Action"),selector:"",cell:e=>(0,a.jsxs)("div",{children:[(0,a.jsx)(o(),{href:"/adminsettings/[...routes]",as:`/adminsettings/edit_projectstatus/${e._id}`,children:(0,a.jsx)("i",{className:"icon fas fa-edit"})}),"\xa0",(0,a.jsx)("a",{onClick:()=>_(e),children:(0,a.jsx)("i",{className:"icon fas fa-trash-alt"})})," "]})}],w=async()=>{n(!0);let e=await l.A.get("/projectstatus",q);e&&e.data&&e.data.length>0&&(s(e.data),g(e.totalCount),n(!1))},f=async(e,t)=>{q.limit=e,q.page=t,n(!0);let r=await l.A.get("/projectstatus",q);r&&r.data&&r.data.length>0&&(s(r.data),A(e),n(!1))},_=async e=>{P(e._id),y(!0)},C=async()=>{try{await l.A.remove(`/projectstatus/${j}`),w(),y(!1),u.default.success(t("adminsetting.ProjectStatus.Table.projectStatusDeletedSuccessfully"))}catch(e){u.default.error(t("adminsetting.ProjectStatus.Table.errorDeletingProjectStatus"))}},D=()=>y(!1);return(0,i.useEffect)(()=>{w()},[]),(0,a.jsxs)("div",{children:[(0,a.jsxs)(p.A,{show:S,onHide:D,children:[(0,a.jsx)(p.A.Header,{closeButton:!0,children:(0,a.jsx)(p.A.Title,{children:t("adminsetting.ProjectStatus.DeleteProjectstatus")})}),(0,a.jsx)(p.A.Body,{children:t("adminsetting.ProjectStatus.Areyousurewanttodeletethisprojectstatus")}),(0,a.jsxs)(p.A.Footer,{children:[(0,a.jsx)(d.A,{variant:"secondary",onClick:D,children:t("adminsetting.ProjectStatus.Cancel")}),(0,a.jsx)(d.A,{variant:"primary",onClick:C,children:t("adminsetting.ProjectStatus.Yes")})]})]}),(0,a.jsx)(c.A,{columns:v,data:r,totalRows:x,pagServer:!0,handlePerRowsChange:f,handlePageChange:e=>{q.limit=h,q.page=e,w()}})]})};s()}catch(e){s(e)}})},45927:(e,t,r)=>{r.r(t),r.d(t,{canAddAreaOfWork:()=>n,canAddContent:()=>C,canAddCountry:()=>o,canAddDeploymentStatus:()=>p,canAddEventStatus:()=>d,canAddExpertise:()=>u,canAddFocalPointApproval:()=>c,canAddHazardTypes:()=>x,canAddHazards:()=>m,canAddLandingPage:()=>_,canAddOperationStatus:()=>S,canAddOrganisationApproval:()=>g,canAddOrganisationNetworks:()=>h,canAddOrganisationTypes:()=>A,canAddProjectStatus:()=>y,canAddRegions:()=>j,canAddRiskLevels:()=>P,canAddSyndromes:()=>q,canAddUpdateTypes:()=>v,canAddUsers:()=>w,canAddVspaceApproval:()=>l,canAddWorldRegion:()=>f,default:()=>D});var s=r(81366),a=r.n(s);let i="create:any",n=a()({authenticatedSelector:e=>!!e.permissions&&!!e.permissions.area_of_work&&!!e.permissions.area_of_work[i],wrapperDisplayName:"CanAddAreaOfWork"}),o=a()({authenticatedSelector:e=>!!e.permissions&&!!e.permissions.country&&!!e.permissions.country[i],wrapperDisplayName:"CanAddCountry"}),p=a()({authenticatedSelector:e=>!!e.permissions&&!!e.permissions.deployment_status&&!!e.permissions.deployment_status[i],wrapperDisplayName:"CanAddDeploymentStatus"}),d=a()({authenticatedSelector:e=>!!e.permissions&&!!e.permissions.event_status&&!!e.permissions.event_status[i],wrapperDisplayName:"CanAddEventStatus"}),u=a()({authenticatedSelector:e=>!!e.permissions&&!!e.permissions.expertise&&!!e.permissions.expertise[i],wrapperDisplayName:"CanAddExpertise"}),c=a()({authenticatedSelector:e=>!!e.permissions&&!!e.permissions.institution_focal_point&&!!e.permissions.institution_focal_point[i],wrapperDisplayName:"CanAddFocalPointApproval"}),l=a()({authenticatedSelector:e=>!!e.permissions&&!!e.permissions.institution_focal_point&&!!e.permissions.institution_focal_point[i],wrapperDisplayName:"CanAddVspaceApproval"}),m=a()({authenticatedSelector:e=>!!e.permissions&&!!e.permissions.hazard&&!!e.permissions.hazard[i],wrapperDisplayName:"CanAddHazards"}),x=a()({authenticatedSelector:e=>!!e.permissions&&!!e.permissions.hazard_type&&!!e.permissions.hazard_type[i],wrapperDisplayName:"CanAddHazardTypes"}),g=a()({authenticatedSelector:e=>!!e.permissions&&!!e.permissions.institution&&!!e.permissions.institution[i],wrapperDisplayName:"CanAddOrganisationApproval"}),h=a()({authenticatedSelector:e=>!!e.permissions&&!!e.permissions.institution_network&&!!e.permissions.institution_network[i],wrapperDisplayName:"CanAddOrganisationNetworks"}),A=a()({authenticatedSelector:e=>!!e.permissions&&!!e.permissions.institution_type&&!!e.permissions.institution_type[i],wrapperDisplayName:"CanAddOrganisationTypes"}),S=a()({authenticatedSelector:e=>!!e.permissions&&!!e.permissions.operation_status&&!!e.permissions.operation_status[i],wrapperDisplayName:"CanAddOperationStatus"}),y=a()({authenticatedSelector:e=>!!e.permissions&&!!e.permissions.project_status&&!!e.permissions.project_status[i],wrapperDisplayName:"CanAddProjectStatus"}),j=a()({authenticatedSelector:e=>!!e.permissions&&!!e.permissions.region&&!!e.permissions.region[i],wrapperDisplayName:"CanAddRegions"}),P=a()({authenticatedSelector:e=>!!e.permissions&&!!e.permissions.risk_level&&!!e.permissions.risk_level[i],wrapperDisplayName:"CanAddRiskLevels"}),q=a()({authenticatedSelector:e=>!!e.permissions&&!!e.permissions.syndrome&&!!e.permissions.syndrome[i],wrapperDisplayName:"CanAddSyndromes"}),v=a()({authenticatedSelector:e=>!!e.permissions&&!!e.permissions.update_type&&!!e.permissions.update_type[i],wrapperDisplayName:"CanAddUpdateTypes"}),w=a()({authenticatedSelector:e=>!!e.permissions&&!!e.permissions.users&&!!e.permissions.users[i],wrapperDisplayName:"CanAddUsers"}),f=a()({authenticatedSelector:e=>!!e.permissions&&!!e.permissions.worl_region&&!!e.permissions.worl_region[i],wrapperDisplayName:"CanAddWorldRegion"}),_=a()({authenticatedSelector:e=>!!e.permissions&&!!e.permissions.landing_page&&!!e.permissions.landing_page[i],wrapperDisplayName:"CanAddLandingPage"}),C=a()({authenticatedSelector:e=>!!e.permissions&&!!e.permissions.operation&&!!e.permissions.operation[i]&&!!e.permissions.project&&!!e.permissions.project[i]&&!!e.permissions.event&&!!e.permissions.event[i]&&!!e.permissions.vspace&&!!e.permissions.vspace[i]&&!!e.permissions.institution&&!!e.permissions.institution[i]&&!!e.permissions.update&&!!e.permissions.update[i]||!1,wrapperDisplayName:"CanAddContent"}),D=n},50009:e=>{e.exports=require("@restart/ui/DropdownMenu")},51559:(e,t,r)=>{r.a(e,async(e,s)=>{try{r.r(t),r.d(t,{default:()=>S});var a=r(8732),i=r(7082),n=r(83551),o=r(49481),p=r(91353),d=r(19918),u=r.n(d),c=r(27053),l=r(45080),m=r(88751),x=r(45927),g=r(14062),h=r(35557),A=e([l,g]);[l,g]=A.then?(await A)():A;let S=e=>{let{t}=(0,m.useTranslation)("common"),r=()=>(0,a.jsx)("div",{children:(0,a.jsxs)(i.A,{style:{overflowX:"hidden"},fluid:!0,className:"p-0",children:[(0,a.jsx)(n.A,{children:(0,a.jsx)(o.A,{xs:12,children:(0,a.jsx)(c.A,{title:t("adminsetting.ProjectStatus.ProjectStatus")})})}),(0,a.jsx)(n.A,{children:(0,a.jsx)(o.A,{xs:12,children:(0,a.jsx)(u(),{href:"/adminsettings/[...routes]",as:"/adminsettings/create_projectstatus",children:(0,a.jsx)(p.A,{variant:"secondary",size:"sm",children:t("adminsetting.ProjectStatus.AddProjectStatus")})})})}),(0,a.jsx)(n.A,{className:"mt-3",children:(0,a.jsx)(o.A,{xs:12,children:(0,a.jsx)(l.default,{})})})]})}),s=(0,x.canAddProjectStatus)(()=>(0,a.jsx)(r,{})),d=(0,g.useSelector)(e=>e);return d?.permissions?.project_status?.["create:any"]?(0,a.jsx)(s,{}):(0,a.jsx)(h.default,{})};s()}catch(e){s(e)}})},56084:(e,t,r)=>{r.d(t,{A:()=>d});var s=r(8732);r(82015);var a=r(38609),i=r.n(a),n=r(88751),o=r(30370);function p(e){let{t}=(0,n.useTranslation)("common"),r={rowsPerPageText:t("Rowsperpage")},{columns:a,data:p,totalRows:d,resetPaginationToggle:u,subheader:c,subHeaderComponent:l,handlePerRowsChange:m,handlePageChange:x,rowsPerPage:g,defaultRowsPerPage:h,selectableRows:A,loading:S,pagServer:y,onSelectedRowsChange:j,clearSelectedRows:P,sortServer:q,onSort:v,persistTableHead:w,sortFunction:f,..._}=e,C={paginationComponentOptions:r,noDataComponent:t("NoData"),noHeader:!0,columns:a,data:p||[],dense:!0,paginationResetDefaultPage:u,subHeader:c,progressPending:S,subHeaderComponent:l,pagination:!0,paginationServer:y,paginationPerPage:h||10,paginationRowsPerPageOptions:g||[10,15,20,25,30],paginationTotalRows:d,onChangeRowsPerPage:m,onChangePage:x,selectableRows:A,onSelectedRowsChange:j,clearSelectedRows:P,progressComponent:(0,s.jsx)(o.A,{}),sortIcon:(0,s.jsx)("i",{className:"sort-icon fas fa-exchange-alt"}),sortServer:q,onSort:v,sortFunction:f,persistTableHead:w,className:"rki-table"};return(0,s.jsx)(i(),{...C})}p.defaultProps={subHeader:!1,pagination:!0,totalRows:null,pagServer:!0,onSelectedRowsChange:null,clearSelectedRows:!1,sortServer:!1,persistTableHead:!1};let d=p},57664:e=>{e.exports=require("dom-helpers/scrollbarSize")},58928:e=>{e.exports=require("dom-helpers/canUseDOM")},59717:e=>{e.exports=require("@restart/ui/DropdownContext")},60560:e=>{e.exports=require("dom-helpers/contains")},63033:e=>{e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},65447:e=>{e.exports=require("@restart/ui/Button")},67364:e=>{e.exports=require("@restart/hooks/useCallbackRef")},68455:e=>{e.exports=import("redux-saga")},69722:e=>{e.exports=require("es6-promise")},74075:e=>{e.exports=require("zlib")},74716:e=>{e.exports=require("moment")},74987:e=>{e.exports=require("@restart/ui/ModalManager")},78097:e=>{e.exports=require("next-redux-saga")},78634:e=>{e.exports=require("@restart/ui/Anchor")},80237:(e,t)=>{Object.defineProperty(t,"A",{enumerable:!0,get:function(){return r}});var r=function(e){return e.PAGES="PAGES",e.PAGES_API="PAGES_API",e.APP_PAGE="APP_PAGE",e.APP_ROUTE="APP_ROUTE",e.IMAGE="IMAGE",e}({})},80960:(e,t,r)=>{r.a(e,async(e,s)=>{try{r.r(t),r.d(t,{config:()=>h,default:()=>l,getServerSideProps:()=>g,getStaticPaths:()=>x,getStaticProps:()=>m,reportWebVitals:()=>A,routeModule:()=>v,unstable_getServerProps:()=>P,unstable_getServerSideProps:()=>q,unstable_getStaticParams:()=>j,unstable_getStaticPaths:()=>y,unstable_getStaticProps:()=>S});var a=r(63885),i=r(80237),n=r(81413),o=r(9616),p=r.n(o),d=r(72386),u=r(51559),c=e([d,u]);[d,u]=c.then?(await c)():c;let l=(0,n.M)(u,"default"),m=(0,n.M)(u,"getStaticProps"),x=(0,n.M)(u,"getStaticPaths"),g=(0,n.M)(u,"getServerSideProps"),h=(0,n.M)(u,"config"),A=(0,n.M)(u,"reportWebVitals"),S=(0,n.M)(u,"unstable_getStaticProps"),y=(0,n.M)(u,"unstable_getStaticPaths"),j=(0,n.M)(u,"unstable_getStaticParams"),P=(0,n.M)(u,"unstable_getServerProps"),q=(0,n.M)(u,"unstable_getServerSideProps"),v=new a.PagesRouteModule({definition:{kind:i.A.PAGES,page:"/adminsettings/projectstatuses",pathname:"/adminsettings/projectstatuses",bundlePath:"",filename:""},components:{App:d.default,Document:p()},userland:u});s()}catch(e){s(e)}})},81366:e=>{e.exports=require("redux-auth-wrapper/connectedAuthWrapper")},81413:(e,t)=>{Object.defineProperty(t,"M",{enumerable:!0,get:function(){return function e(t,r){return r in t?t[r]:"then"in t&&"function"==typeof t.then?t.then(t=>e(t,r)):"function"==typeof t&&"default"===r?t:void 0}}})},81521:e=>{e.exports=require("dom-helpers/querySelectorAll")},81895:e=>{e.exports=require("@restart/hooks/useEventCallback")},82015:e=>{e.exports=require("react")},86842:e=>{e.exports=require("@restart/ui/SelectableContext")},87571:e=>{e.exports=require("dom-helpers/transitionEnd")},88751:e=>{e.exports=require("next-i18next")},93787:e=>{e.exports=require("redux-persist")},94947:e=>{e.exports=require("@restart/hooks/useTimeout")},96196:e=>{e.exports=require("next-redux-wrapper")},98320:e=>{e.exports=require("@restart/hooks/useIsomorphicEffect")},99460:e=>{e.exports=require("dom-helpers/css")}};var t=require("../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[6089,9216,9616,2386],()=>r(80960));module.exports=s})();