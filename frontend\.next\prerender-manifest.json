{"version": 4, "routes": {"/en/adminsettings/hazard": {"initialRevalidateSeconds": false, "srcRoute": null, "dataRoute": "/_next/data/hyw66_qcuUfJw5YvJjK5v/adminsettings/hazard.json", "allowHeader": ["host", "x-matched-path", "x-prerender-revalidate", "x-prerender-revalidate-if-generated", "x-next-revalidated-tags", "x-next-revalidate-tag-token"]}, "/de/adminsettings/hazard": {"initialRevalidateSeconds": false, "srcRoute": null, "dataRoute": "/_next/data/hyw66_qcuUfJw5YvJjK5v/adminsettings/hazard.json", "allowHeader": ["host", "x-matched-path", "x-prerender-revalidate", "x-prerender-revalidate-if-generated", "x-next-revalidated-tags", "x-next-revalidate-tag-token"]}, "/en/adminsettings": {"initialRevalidateSeconds": false, "srcRoute": null, "dataRoute": "/_next/data/hyw66_qcuUfJw5YvJjK5v/adminsettings.json", "allowHeader": ["host", "x-matched-path", "x-prerender-revalidate", "x-prerender-revalidate-if-generated", "x-next-revalidated-tags", "x-next-revalidate-tag-token"]}, "/de/adminsettings": {"initialRevalidateSeconds": false, "srcRoute": null, "dataRoute": "/_next/data/hyw66_qcuUfJw5YvJjK5v/adminsettings.json", "allowHeader": ["host", "x-matched-path", "x-prerender-revalidate", "x-prerender-revalidate-if-generated", "x-next-revalidated-tags", "x-next-revalidate-tag-token"]}, "/en/country": {"initialRevalidateSeconds": false, "srcRoute": null, "dataRoute": "/_next/data/hyw66_qcuUfJw5YvJjK5v/country.json", "allowHeader": ["host", "x-matched-path", "x-prerender-revalidate", "x-prerender-revalidate-if-generated", "x-next-revalidated-tags", "x-next-revalidate-tag-token"]}, "/de/country": {"initialRevalidateSeconds": false, "srcRoute": null, "dataRoute": "/_next/data/hyw66_qcuUfJw5YvJjK5v/country.json", "allowHeader": ["host", "x-matched-path", "x-prerender-revalidate", "x-prerender-revalidate-if-generated", "x-next-revalidated-tags", "x-next-revalidate-tag-token"]}, "/en/event": {"initialRevalidateSeconds": false, "srcRoute": null, "dataRoute": "/_next/data/hyw66_qcuUfJw5YvJjK5v/event.json", "allowHeader": ["host", "x-matched-path", "x-prerender-revalidate", "x-prerender-revalidate-if-generated", "x-next-revalidated-tags", "x-next-revalidate-tag-token"]}, "/de/event": {"initialRevalidateSeconds": false, "srcRoute": null, "dataRoute": "/_next/data/hyw66_qcuUfJw5YvJjK5v/event.json", "allowHeader": ["host", "x-matched-path", "x-prerender-revalidate", "x-prerender-revalidate-if-generated", "x-next-revalidated-tags", "x-next-revalidate-tag-token"]}, "/en/home": {"initialRevalidateSeconds": false, "srcRoute": null, "dataRoute": "/_next/data/hyw66_qcuUfJw5YvJjK5v/home.json", "allowHeader": ["host", "x-matched-path", "x-prerender-revalidate", "x-prerender-revalidate-if-generated", "x-next-revalidated-tags", "x-next-revalidate-tag-token"]}, "/de/home": {"initialRevalidateSeconds": false, "srcRoute": null, "dataRoute": "/_next/data/hyw66_qcuUfJw5YvJjK5v/home.json", "allowHeader": ["host", "x-matched-path", "x-prerender-revalidate", "x-prerender-revalidate-if-generated", "x-next-revalidated-tags", "x-next-revalidate-tag-token"]}, "/en/hazard": {"initialRevalidateSeconds": false, "srcRoute": null, "dataRoute": "/_next/data/hyw66_qcuUfJw5YvJjK5v/hazard.json", "allowHeader": ["host", "x-matched-path", "x-prerender-revalidate", "x-prerender-revalidate-if-generated", "x-next-revalidated-tags", "x-next-revalidate-tag-token"]}, "/de/hazard": {"initialRevalidateSeconds": false, "srcRoute": null, "dataRoute": "/_next/data/hyw66_qcuUfJw5YvJjK5v/hazard.json", "allowHeader": ["host", "x-matched-path", "x-prerender-revalidate", "x-prerender-revalidate-if-generated", "x-next-revalidated-tags", "x-next-revalidate-tag-token"]}, "/en/institution": {"initialRevalidateSeconds": false, "srcRoute": null, "dataRoute": "/_next/data/hyw66_qcuUfJw5YvJjK5v/institution.json", "allowHeader": ["host", "x-matched-path", "x-prerender-revalidate", "x-prerender-revalidate-if-generated", "x-next-revalidated-tags", "x-next-revalidate-tag-token"]}, "/de/institution": {"initialRevalidateSeconds": false, "srcRoute": null, "dataRoute": "/_next/data/hyw66_qcuUfJw5YvJjK5v/institution.json", "allowHeader": ["host", "x-matched-path", "x-prerender-revalidate", "x-prerender-revalidate-if-generated", "x-next-revalidated-tags", "x-next-revalidate-tag-token"]}, "/en/login": {"initialRevalidateSeconds": false, "srcRoute": null, "dataRoute": "/_next/data/hyw66_qcuUfJw5YvJjK5v/login.json", "allowHeader": ["host", "x-matched-path", "x-prerender-revalidate", "x-prerender-revalidate-if-generated", "x-next-revalidated-tags", "x-next-revalidate-tag-token"]}, "/de/login": {"initialRevalidateSeconds": false, "srcRoute": null, "dataRoute": "/_next/data/hyw66_qcuUfJw5YvJjK5v/login.json", "allowHeader": ["host", "x-matched-path", "x-prerender-revalidate", "x-prerender-revalidate-if-generated", "x-next-revalidated-tags", "x-next-revalidate-tag-token"]}, "/en/operation": {"initialRevalidateSeconds": false, "srcRoute": null, "dataRoute": "/_next/data/hyw66_qcuUfJw5YvJjK5v/operation.json", "allowHeader": ["host", "x-matched-path", "x-prerender-revalidate", "x-prerender-revalidate-if-generated", "x-next-revalidated-tags", "x-next-revalidate-tag-token"]}, "/de/operation": {"initialRevalidateSeconds": false, "srcRoute": null, "dataRoute": "/_next/data/hyw66_qcuUfJw5YvJjK5v/operation.json", "allowHeader": ["host", "x-matched-path", "x-prerender-revalidate", "x-prerender-revalidate-if-generated", "x-next-revalidated-tags", "x-next-revalidate-tag-token"]}, "/en/profile": {"initialRevalidateSeconds": false, "srcRoute": null, "dataRoute": "/_next/data/hyw66_qcuUfJw5YvJjK5v/profile.json", "allowHeader": ["host", "x-matched-path", "x-prerender-revalidate", "x-prerender-revalidate-if-generated", "x-next-revalidated-tags", "x-next-revalidate-tag-token"]}, "/de/profile": {"initialRevalidateSeconds": false, "srcRoute": null, "dataRoute": "/_next/data/hyw66_qcuUfJw5YvJjK5v/profile.json", "allowHeader": ["host", "x-matched-path", "x-prerender-revalidate", "x-prerender-revalidate-if-generated", "x-next-revalidated-tags", "x-next-revalidate-tag-token"]}, "/en/project": {"initialRevalidateSeconds": false, "srcRoute": null, "dataRoute": "/_next/data/hyw66_qcuUfJw5YvJjK5v/project.json", "allowHeader": ["host", "x-matched-path", "x-prerender-revalidate", "x-prerender-revalidate-if-generated", "x-next-revalidated-tags", "x-next-revalidate-tag-token"]}, "/de/project": {"initialRevalidateSeconds": false, "srcRoute": null, "dataRoute": "/_next/data/hyw66_qcuUfJw5YvJjK5v/project.json", "allowHeader": ["host", "x-matched-path", "x-prerender-revalidate", "x-prerender-revalidate-if-generated", "x-next-revalidated-tags", "x-next-revalidate-tag-token"]}, "/en/people": {"initialRevalidateSeconds": false, "srcRoute": null, "dataRoute": "/_next/data/hyw66_qcuUfJw5YvJjK5v/people.json", "allowHeader": ["host", "x-matched-path", "x-prerender-revalidate", "x-prerender-revalidate-if-generated", "x-next-revalidated-tags", "x-next-revalidate-tag-token"]}, "/de/people": {"initialRevalidateSeconds": false, "srcRoute": null, "dataRoute": "/_next/data/hyw66_qcuUfJw5YvJjK5v/people.json", "allowHeader": ["host", "x-matched-path", "x-prerender-revalidate", "x-prerender-revalidate-if-generated", "x-next-revalidated-tags", "x-next-revalidate-tag-token"]}, "/en/users": {"initialRevalidateSeconds": false, "srcRoute": null, "dataRoute": "/_next/data/hyw66_qcuUfJw5YvJjK5v/users.json", "allowHeader": ["host", "x-matched-path", "x-prerender-revalidate", "x-prerender-revalidate-if-generated", "x-next-revalidated-tags", "x-next-revalidate-tag-token"]}, "/de/users": {"initialRevalidateSeconds": false, "srcRoute": null, "dataRoute": "/_next/data/hyw66_qcuUfJw5YvJjK5v/users.json", "allowHeader": ["host", "x-matched-path", "x-prerender-revalidate", "x-prerender-revalidate-if-generated", "x-next-revalidated-tags", "x-next-revalidate-tag-token"]}, "/en/dashboard/Dashboard": {"initialRevalidateSeconds": false, "srcRoute": null, "dataRoute": "/_next/data/hyw66_qcuUfJw5YvJjK5v/dashboard/Dashboard.json", "allowHeader": ["host", "x-matched-path", "x-prerender-revalidate", "x-prerender-revalidate-if-generated", "x-next-revalidated-tags", "x-next-revalidate-tag-token"]}, "/de/dashboard/Dashboard": {"initialRevalidateSeconds": false, "srcRoute": null, "dataRoute": "/_next/data/hyw66_qcuUfJw5YvJjK5v/dashboard/Dashboard.json", "allowHeader": ["host", "x-matched-path", "x-prerender-revalidate", "x-prerender-revalidate-if-generated", "x-next-revalidated-tags", "x-next-revalidate-tag-token"]}, "/en/events-calendar": {"initialRevalidateSeconds": false, "srcRoute": null, "dataRoute": "/_next/data/hyw66_qcuUfJw5YvJjK5v/events-calendar.json", "allowHeader": ["host", "x-matched-path", "x-prerender-revalidate", "x-prerender-revalidate-if-generated", "x-next-revalidated-tags", "x-next-revalidate-tag-token"]}, "/de/events-calendar": {"initialRevalidateSeconds": false, "srcRoute": null, "dataRoute": "/_next/data/hyw66_qcuUfJw5YvJjK5v/events-calendar.json", "allowHeader": ["host", "x-matched-path", "x-prerender-revalidate", "x-prerender-revalidate-if-generated", "x-next-revalidated-tags", "x-next-revalidate-tag-token"]}, "/en": {"initialRevalidateSeconds": false, "srcRoute": null, "dataRoute": "/_next/data/hyw66_qcuUfJw5YvJjK5v/index.json", "allowHeader": ["host", "x-matched-path", "x-prerender-revalidate", "x-prerender-revalidate-if-generated", "x-next-revalidated-tags", "x-next-revalidate-tag-token"]}, "/de": {"initialRevalidateSeconds": false, "srcRoute": null, "dataRoute": "/_next/data/hyw66_qcuUfJw5YvJjK5v/index.json", "allowHeader": ["host", "x-matched-path", "x-prerender-revalidate", "x-prerender-revalidate-if-generated", "x-next-revalidated-tags", "x-next-revalidate-tag-token"]}, "/en/vspace": {"initialRevalidateSeconds": false, "srcRoute": null, "dataRoute": "/_next/data/hyw66_qcuUfJw5YvJjK5v/vspace.json", "allowHeader": ["host", "x-matched-path", "x-prerender-revalidate", "x-prerender-revalidate-if-generated", "x-next-revalidated-tags", "x-next-revalidate-tag-token"]}, "/de/vspace": {"initialRevalidateSeconds": false, "srcRoute": null, "dataRoute": "/_next/data/hyw66_qcuUfJw5YvJjK5v/vspace.json", "allowHeader": ["host", "x-matched-path", "x-prerender-revalidate", "x-prerender-revalidate-if-generated", "x-next-revalidated-tags", "x-next-revalidate-tag-token"]}, "/en/updates": {"initialRevalidateSeconds": false, "srcRoute": null, "dataRoute": "/_next/data/hyw66_qcuUfJw5YvJjK5v/updates.json", "allowHeader": ["host", "x-matched-path", "x-prerender-revalidate", "x-prerender-revalidate-if-generated", "x-next-revalidated-tags", "x-next-revalidate-tag-token"]}, "/de/updates": {"initialRevalidateSeconds": false, "srcRoute": null, "dataRoute": "/_next/data/hyw66_qcuUfJw5YvJjK5v/updates.json", "allowHeader": ["host", "x-matched-path", "x-prerender-revalidate", "x-prerender-revalidate-if-generated", "x-next-revalidated-tags", "x-next-revalidate-tag-token"]}}, "dynamicRoutes": {}, "notFoundRoutes": [], "preview": {"previewModeId": "8961936209cdd6cfe75ab196b8d92894", "previewModeSigningKey": "07a4bfcf7e2c18a9d5710b01c4ce7bee39f1648c95abf2be5252ac3c9f1e242b", "previewModeEncryptionKey": "ca0401eb53ec59d478cd2f9d74d546471d50f83ebba10174699b66e1f63e0960"}}