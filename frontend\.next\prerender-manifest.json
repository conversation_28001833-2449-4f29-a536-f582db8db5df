{"version": 4, "routes": {"/en/adminsettings/hazard": {"initialRevalidateSeconds": false, "srcRoute": null, "dataRoute": "/_next/data/yN9sb2e82_hGmHqnhKEMB/adminsettings/hazard.json", "allowHeader": ["host", "x-matched-path", "x-prerender-revalidate", "x-prerender-revalidate-if-generated", "x-next-revalidated-tags", "x-next-revalidate-tag-token"]}, "/de/adminsettings/hazard": {"initialRevalidateSeconds": false, "srcRoute": null, "dataRoute": "/_next/data/yN9sb2e82_hGmHqnhKEMB/adminsettings/hazard.json", "allowHeader": ["host", "x-matched-path", "x-prerender-revalidate", "x-prerender-revalidate-if-generated", "x-next-revalidated-tags", "x-next-revalidate-tag-token"]}, "/en/adminsettings": {"initialRevalidateSeconds": false, "srcRoute": null, "dataRoute": "/_next/data/yN9sb2e82_hGmHqnhKEMB/adminsettings.json", "allowHeader": ["host", "x-matched-path", "x-prerender-revalidate", "x-prerender-revalidate-if-generated", "x-next-revalidated-tags", "x-next-revalidate-tag-token"]}, "/de/adminsettings": {"initialRevalidateSeconds": false, "srcRoute": null, "dataRoute": "/_next/data/yN9sb2e82_hGmHqnhKEMB/adminsettings.json", "allowHeader": ["host", "x-matched-path", "x-prerender-revalidate", "x-prerender-revalidate-if-generated", "x-next-revalidated-tags", "x-next-revalidate-tag-token"]}, "/en/country": {"initialRevalidateSeconds": false, "srcRoute": null, "dataRoute": "/_next/data/yN9sb2e82_hGmHqnhKEMB/country.json", "allowHeader": ["host", "x-matched-path", "x-prerender-revalidate", "x-prerender-revalidate-if-generated", "x-next-revalidated-tags", "x-next-revalidate-tag-token"]}, "/de/country": {"initialRevalidateSeconds": false, "srcRoute": null, "dataRoute": "/_next/data/yN9sb2e82_hGmHqnhKEMB/country.json", "allowHeader": ["host", "x-matched-path", "x-prerender-revalidate", "x-prerender-revalidate-if-generated", "x-next-revalidated-tags", "x-next-revalidate-tag-token"]}, "/en/event": {"initialRevalidateSeconds": false, "srcRoute": null, "dataRoute": "/_next/data/yN9sb2e82_hGmHqnhKEMB/event.json", "allowHeader": ["host", "x-matched-path", "x-prerender-revalidate", "x-prerender-revalidate-if-generated", "x-next-revalidated-tags", "x-next-revalidate-tag-token"]}, "/de/event": {"initialRevalidateSeconds": false, "srcRoute": null, "dataRoute": "/_next/data/yN9sb2e82_hGmHqnhKEMB/event.json", "allowHeader": ["host", "x-matched-path", "x-prerender-revalidate", "x-prerender-revalidate-if-generated", "x-next-revalidated-tags", "x-next-revalidate-tag-token"]}, "/en/hazard": {"initialRevalidateSeconds": false, "srcRoute": null, "dataRoute": "/_next/data/yN9sb2e82_hGmHqnhKEMB/hazard.json", "allowHeader": ["host", "x-matched-path", "x-prerender-revalidate", "x-prerender-revalidate-if-generated", "x-next-revalidated-tags", "x-next-revalidate-tag-token"]}, "/de/hazard": {"initialRevalidateSeconds": false, "srcRoute": null, "dataRoute": "/_next/data/yN9sb2e82_hGmHqnhKEMB/hazard.json", "allowHeader": ["host", "x-matched-path", "x-prerender-revalidate", "x-prerender-revalidate-if-generated", "x-next-revalidated-tags", "x-next-revalidate-tag-token"]}, "/en/home": {"initialRevalidateSeconds": false, "srcRoute": null, "dataRoute": "/_next/data/yN9sb2e82_hGmHqnhKEMB/home.json", "allowHeader": ["host", "x-matched-path", "x-prerender-revalidate", "x-prerender-revalidate-if-generated", "x-next-revalidated-tags", "x-next-revalidate-tag-token"]}, "/de/home": {"initialRevalidateSeconds": false, "srcRoute": null, "dataRoute": "/_next/data/yN9sb2e82_hGmHqnhKEMB/home.json", "allowHeader": ["host", "x-matched-path", "x-prerender-revalidate", "x-prerender-revalidate-if-generated", "x-next-revalidated-tags", "x-next-revalidate-tag-token"]}, "/en/login": {"initialRevalidateSeconds": false, "srcRoute": null, "dataRoute": "/_next/data/yN9sb2e82_hGmHqnhKEMB/login.json", "allowHeader": ["host", "x-matched-path", "x-prerender-revalidate", "x-prerender-revalidate-if-generated", "x-next-revalidated-tags", "x-next-revalidate-tag-token"]}, "/de/login": {"initialRevalidateSeconds": false, "srcRoute": null, "dataRoute": "/_next/data/yN9sb2e82_hGmHqnhKEMB/login.json", "allowHeader": ["host", "x-matched-path", "x-prerender-revalidate", "x-prerender-revalidate-if-generated", "x-next-revalidated-tags", "x-next-revalidate-tag-token"]}, "/en/operation": {"initialRevalidateSeconds": false, "srcRoute": null, "dataRoute": "/_next/data/yN9sb2e82_hGmHqnhKEMB/operation.json", "allowHeader": ["host", "x-matched-path", "x-prerender-revalidate", "x-prerender-revalidate-if-generated", "x-next-revalidated-tags", "x-next-revalidate-tag-token"]}, "/de/operation": {"initialRevalidateSeconds": false, "srcRoute": null, "dataRoute": "/_next/data/yN9sb2e82_hGmHqnhKEMB/operation.json", "allowHeader": ["host", "x-matched-path", "x-prerender-revalidate", "x-prerender-revalidate-if-generated", "x-next-revalidated-tags", "x-next-revalidate-tag-token"]}, "/en/institution": {"initialRevalidateSeconds": false, "srcRoute": null, "dataRoute": "/_next/data/yN9sb2e82_hGmHqnhKEMB/institution.json", "allowHeader": ["host", "x-matched-path", "x-prerender-revalidate", "x-prerender-revalidate-if-generated", "x-next-revalidated-tags", "x-next-revalidate-tag-token"]}, "/de/institution": {"initialRevalidateSeconds": false, "srcRoute": null, "dataRoute": "/_next/data/yN9sb2e82_hGmHqnhKEMB/institution.json", "allowHeader": ["host", "x-matched-path", "x-prerender-revalidate", "x-prerender-revalidate-if-generated", "x-next-revalidated-tags", "x-next-revalidate-tag-token"]}, "/en/profile": {"initialRevalidateSeconds": false, "srcRoute": null, "dataRoute": "/_next/data/yN9sb2e82_hGmHqnhKEMB/profile.json", "allowHeader": ["host", "x-matched-path", "x-prerender-revalidate", "x-prerender-revalidate-if-generated", "x-next-revalidated-tags", "x-next-revalidate-tag-token"]}, "/de/profile": {"initialRevalidateSeconds": false, "srcRoute": null, "dataRoute": "/_next/data/yN9sb2e82_hGmHqnhKEMB/profile.json", "allowHeader": ["host", "x-matched-path", "x-prerender-revalidate", "x-prerender-revalidate-if-generated", "x-next-revalidated-tags", "x-next-revalidate-tag-token"]}, "/en/people": {"initialRevalidateSeconds": false, "srcRoute": null, "dataRoute": "/_next/data/yN9sb2e82_hGmHqnhKEMB/people.json", "allowHeader": ["host", "x-matched-path", "x-prerender-revalidate", "x-prerender-revalidate-if-generated", "x-next-revalidated-tags", "x-next-revalidate-tag-token"]}, "/de/people": {"initialRevalidateSeconds": false, "srcRoute": null, "dataRoute": "/_next/data/yN9sb2e82_hGmHqnhKEMB/people.json", "allowHeader": ["host", "x-matched-path", "x-prerender-revalidate", "x-prerender-revalidate-if-generated", "x-next-revalidated-tags", "x-next-revalidate-tag-token"]}, "/en/project": {"initialRevalidateSeconds": false, "srcRoute": null, "dataRoute": "/_next/data/yN9sb2e82_hGmHqnhKEMB/project.json", "allowHeader": ["host", "x-matched-path", "x-prerender-revalidate", "x-prerender-revalidate-if-generated", "x-next-revalidated-tags", "x-next-revalidate-tag-token"]}, "/de/project": {"initialRevalidateSeconds": false, "srcRoute": null, "dataRoute": "/_next/data/yN9sb2e82_hGmHqnhKEMB/project.json", "allowHeader": ["host", "x-matched-path", "x-prerender-revalidate", "x-prerender-revalidate-if-generated", "x-next-revalidated-tags", "x-next-revalidate-tag-token"]}, "/en/events-calendar": {"initialRevalidateSeconds": false, "srcRoute": null, "dataRoute": "/_next/data/yN9sb2e82_hGmHqnhKEMB/events-calendar.json", "allowHeader": ["host", "x-matched-path", "x-prerender-revalidate", "x-prerender-revalidate-if-generated", "x-next-revalidated-tags", "x-next-revalidate-tag-token"]}, "/de/events-calendar": {"initialRevalidateSeconds": false, "srcRoute": null, "dataRoute": "/_next/data/yN9sb2e82_hGmHqnhKEMB/events-calendar.json", "allowHeader": ["host", "x-matched-path", "x-prerender-revalidate", "x-prerender-revalidate-if-generated", "x-next-revalidated-tags", "x-next-revalidate-tag-token"]}, "/en/users": {"initialRevalidateSeconds": false, "srcRoute": null, "dataRoute": "/_next/data/yN9sb2e82_hGmHqnhKEMB/users.json", "allowHeader": ["host", "x-matched-path", "x-prerender-revalidate", "x-prerender-revalidate-if-generated", "x-next-revalidated-tags", "x-next-revalidate-tag-token"]}, "/de/users": {"initialRevalidateSeconds": false, "srcRoute": null, "dataRoute": "/_next/data/yN9sb2e82_hGmHqnhKEMB/users.json", "allowHeader": ["host", "x-matched-path", "x-prerender-revalidate", "x-prerender-revalidate-if-generated", "x-next-revalidated-tags", "x-next-revalidate-tag-token"]}, "/en/dashboard/Dashboard": {"initialRevalidateSeconds": false, "srcRoute": null, "dataRoute": "/_next/data/yN9sb2e82_hGmHqnhKEMB/dashboard/Dashboard.json", "allowHeader": ["host", "x-matched-path", "x-prerender-revalidate", "x-prerender-revalidate-if-generated", "x-next-revalidated-tags", "x-next-revalidate-tag-token"]}, "/de/dashboard/Dashboard": {"initialRevalidateSeconds": false, "srcRoute": null, "dataRoute": "/_next/data/yN9sb2e82_hGmHqnhKEMB/dashboard/Dashboard.json", "allowHeader": ["host", "x-matched-path", "x-prerender-revalidate", "x-prerender-revalidate-if-generated", "x-next-revalidated-tags", "x-next-revalidate-tag-token"]}, "/en": {"initialRevalidateSeconds": false, "srcRoute": null, "dataRoute": "/_next/data/yN9sb2e82_hGmHqnhKEMB/index.json", "allowHeader": ["host", "x-matched-path", "x-prerender-revalidate", "x-prerender-revalidate-if-generated", "x-next-revalidated-tags", "x-next-revalidate-tag-token"]}, "/de": {"initialRevalidateSeconds": false, "srcRoute": null, "dataRoute": "/_next/data/yN9sb2e82_hGmHqnhKEMB/index.json", "allowHeader": ["host", "x-matched-path", "x-prerender-revalidate", "x-prerender-revalidate-if-generated", "x-next-revalidated-tags", "x-next-revalidate-tag-token"]}, "/en/vspace": {"initialRevalidateSeconds": false, "srcRoute": null, "dataRoute": "/_next/data/yN9sb2e82_hGmHqnhKEMB/vspace.json", "allowHeader": ["host", "x-matched-path", "x-prerender-revalidate", "x-prerender-revalidate-if-generated", "x-next-revalidated-tags", "x-next-revalidate-tag-token"]}, "/de/vspace": {"initialRevalidateSeconds": false, "srcRoute": null, "dataRoute": "/_next/data/yN9sb2e82_hGmHqnhKEMB/vspace.json", "allowHeader": ["host", "x-matched-path", "x-prerender-revalidate", "x-prerender-revalidate-if-generated", "x-next-revalidated-tags", "x-next-revalidate-tag-token"]}, "/en/updates": {"initialRevalidateSeconds": false, "srcRoute": null, "dataRoute": "/_next/data/yN9sb2e82_hGmHqnhKEMB/updates.json", "allowHeader": ["host", "x-matched-path", "x-prerender-revalidate", "x-prerender-revalidate-if-generated", "x-next-revalidated-tags", "x-next-revalidate-tag-token"]}, "/de/updates": {"initialRevalidateSeconds": false, "srcRoute": null, "dataRoute": "/_next/data/yN9sb2e82_hGmHqnhKEMB/updates.json", "allowHeader": ["host", "x-matched-path", "x-prerender-revalidate", "x-prerender-revalidate-if-generated", "x-next-revalidated-tags", "x-next-revalidate-tag-token"]}}, "dynamicRoutes": {}, "notFoundRoutes": [], "preview": {"previewModeId": "8c44fe8fcb5e1f5c7bc3fc81d9081e5b", "previewModeSigningKey": "169dac4e81cd4358602695f2973e2347aa62113ae7b28bb605edb777e5ffe77b", "previewModeEncryptionKey": "7ab6f8b972812c5e532a7106bc564e5b08ff0daaafd1c31fb3a8deee93ccc859"}}