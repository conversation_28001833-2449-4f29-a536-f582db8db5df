{"version": 3, "file": "static/chunks/318-0585f5c762262bbc.js", "mappings": "oJAcA,MANsB,GAElB,UAACA,EAAAA,EAAUA,CAAAA,CAAE,GAAGC,CAAK,KAIVC,aAAaA,EAAC,mSCF7B,IAAMC,EAAkB,IACtB,IAAMC,EAAqBC,CAAAA,EAAAA,EAAAA,EAAAA,CAAYA,GACjC,CAACC,EAAaC,EAAe,CAAGC,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAC,EAAE,EAC3C,CAACC,EAAYC,EAAc,CAAGF,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAC,IACvC,CAACG,EAAOC,EAAS,CAAGJ,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAM,EAAE,EACpC,CAACK,EAAsBC,EAAwB,CAAGN,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAC,EAAE,EAC7D,CAACO,EAAYC,EAAc,CAAGR,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,EAAC,GACvC,CAACS,EAAeC,EAAiB,CAAGV,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,EAAC,GAC7C,CAACW,EAAgBC,EAAkB,CAAGZ,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAC,IAC/C,GAAEa,CAAC,MAACC,CAAI,CAAE,CAAGC,CAAAA,EAAAA,EAAAA,EAAAA,CAAcA,CAAC,UAC5BC,EAAgC,OAAlBF,EAAKG,QAAQ,CAAW,CAACC,SAAU,KAAK,EAAI,CAACC,MAAO,KAAK,EACvEC,EAAcN,EAAKG,QAAQ,CAEjCI,CAAAA,EAAAA,EAAAA,SAAAA,CAASA,CAAC,KA6BRC,CA3B8B,MAAOC,IACnC,IAAMC,EAAU,KA0BIC,CA1BEC,EAAAA,CAAUA,CAACC,GAAG,CAAC,WAAYJ,GAC7CC,GAAWI,MAAMC,OAAO,CAACL,EAAQM,IAAI,GAIvC/B,EAHiByB,EAAQM,IAAI,CAACC,GAAG,CAAC,CAACC,CAGpBC,CAH+BC,KACrC,CAAEC,MAAOH,EAAKb,KAAK,CAAEhB,MAAO6B,EAAKI,GAAG,CAAC,IAIhD,IAAMC,EAAkB,MAAMX,EAAAA,CAAUA,CAACC,GAAG,CAC1C,mBACAJ,GAEEc,GAAmBT,MAAMC,OAAO,CAACQ,EAAgBP,IAAI,GAIvDxB,EAHyB+B,EAAgBP,IAAI,CAACC,GAAG,CAAC,CAACC,EAAWE,KACrD,CAAEC,EAEaG,IAFNN,EAAKb,KAAK,CAAEhB,MAAO6B,EAAKI,GAAG,KAIjD,EAGsB,CACpBG,MAAO,CAAC,EACRC,KAAMxB,EACNyB,MAAO,IACPC,aAAatB,CACf,EAEF,EAAG,EAAE,EAILC,CAAAA,EAAAA,EAAAA,SAAAA,CAASA,CAAC,KACRlB,GAASV,EAAMkD,SAAS,EAAIlD,EAAMkD,SAAS,CAACxC,EAC9C,EAAG,CAACA,EAAM,EAuBV,IAAMyC,EAAiB,MAAOC,IACvBA,IAGM,GAHC,YAILC,IAAI,CAACD,IAIY,IAAI,CAAvBE,CAFW,MAAMrB,EAAAA,CAAUA,CAACsB,IAAI,CAAC,0BADvB,CACkDlB,MADzCe,CAAK,EACoCf,EAErDmB,OAAO,CACf7C,EAAS,IAAKD,GAAS,EAAE,CAAG,CAAEgC,MAAOU,EAAO1C,MAAO0C,CAAM,EAAE,GAG3DjC,EAAkBiC,GAClBnC,GAAiB,GACjBwC,WAAW,KACTxC,GAAiB,GACjBE,EAAkB,GACpB,EAAG,OACHV,EAAc,MAGhBM,GAAc,GACdN,EAAc,IACdgD,WAAW,KACT1C,GAAc,EAChB,EAAG,OAEP,EAEM2C,EAAgB,MAAOC,IAC3B,GAAKnD,CAAD,CAGJ,OAAQmD,EAAMC,CAHE,EAGC,EACf,IAAK,QACL,IAAK,MACH,MAAMT,EAAe3C,GACrBmD,EAAME,cAAc,EAExB,CACF,EAEMC,EAAa,UACbtD,GAAcA,EAAWuD,IAAI,IAAI,MAC7BZ,EAAe3C,EAAWuD,IAAI,GAExC,EAEM,gBACJC,CAAc,eACdC,CAAa,yBACbC,CAAuB,qBACvBC,CAAmB,kBACnBC,CAAgB,gBAChBC,CAAc,YACdC,CAAU,UACVC,CAAQ,CAAG,CAAGvE,EACVwE,EAAkB,mBACxB,MACE,WAACC,MAAAA,WACC,UAACC,EAAAA,CAAGA,CAAAA,CAACC,UAAU,eAAeC,GAAI,YAChC,UAACC,KAAAA,UACC,UAACC,OAAAA,UAAM1D,EAAE,sBAGb,WAAC2D,EAAAA,CAAGA,CAAAA,WACF,UAACL,EAAAA,CAAGA,CAAAA,UACF,WAACM,EAAAA,CAAIA,CAACC,KAAK,YACT,UAACD,EAAAA,CAAIA,CAACE,KAAK,WAAE9D,EAAE,4BACf,UAAC4D,EAAAA,CAAIA,CAACG,KAAK,EACXR,UAAU,QACRS,QAASd,EACTe,KAAK,aACLC,QAAStF,EAAMuF,gBAAgB,CAC/BC,KAAK,QACL9C,MAAQtB,EAAE,wBAIhB,UAACsD,EAAAA,CAAGA,CAAAA,CAACe,MAAO,CAAEC,UAAW,QAAS,WAChC,UAACV,EAAAA,CAAIA,CAACG,KAAK,EACTK,KAAK,QACLJ,QAAS,CAACd,EACVe,KAAK,aACL3E,MAAM,MACN4E,QAAStF,EAAMuF,gBAAgB,CAC/B7C,MAAOtB,EAAE,yBAId,CAACkD,GACA,WAACG,MAAAA,WACC,UAACC,EAAAA,CAAGA,CAAAA,CAACC,UAAU,eAAeC,GAAI,YAChC,UAACC,KAAAA,UACC,UAACC,OAAAA,UAAM1D,EAAE,wBAGb,WAAC2D,EAAAA,CAAGA,CAAAA,WACF,UAACL,EAAAA,CAAGA,CAAAA,CAACiB,GAAI,EAAGC,GAAI,EAAGhB,GAAI,WACrB,WAACI,EAAAA,CAAIA,CAACC,KAAK,YACT,UAACD,EAAAA,CAAIA,CAACE,KAAK,WAAE9D,EAAE,wBACf,UAACyE,EAAAA,EAAWA,CAAAA,CACVC,gBAAiB,CACfC,gBAAiB3E,EAAE,gBACrB,EACA4E,QAAS3F,GAAe,EAAE,CAC1B4F,SAAU,GAAYjG,EAAMiG,QAAQ,CAACC,EAAG,kBACxCxF,MAAOsD,EACPW,UAAWH,EACX2B,WAAY/E,EAAE,wBAIpB,UAACsD,EAAAA,CAAGA,CAAAA,CAACiB,GAAI,EAAGC,GAAI,EAAGhB,GAAI,WACrB,WAACI,EAAAA,CAAIA,CAACC,KAAK,YACT,UAACD,EAAAA,CAAIA,CAACE,KAAK,WAAE9D,EAAE,oBACf,UAACyE,EAAAA,EAAWA,CAAAA,CACVC,gBAAiB,CACfC,gBAAiB3E,EAAE,gBACrB,EACA4E,QAAShG,EAAMoG,kBAAkB,EAAI,EAAE,CACvC1F,MAAOuD,EACPgC,SAAU,GAAYjG,EAAMiG,QAAQ,CAACC,EAAG,iBACxCvB,UAAWH,EACX2B,WAAY/E,EAAE,wBAIpB,UAACsD,EAAAA,CAAGA,CAAAA,CAACiB,GAAI,EAAGC,GAAI,EAAGhB,GAAI,WACrB,WAACI,EAAAA,CAAIA,CAACC,KAAK,YACT,UAACD,EAAAA,CAAIA,CAACE,KAAK,WAAE9D,EAAE,sBACf,UAACyE,EAAAA,EAAWA,CAAAA,CACVC,gBAAiB,CACfC,gBAAiB3E,EAAE,yBACrB,EACA4E,QAASpF,GAAwB,EAAE,CACnCqF,SAAWC,GAAWlG,EAAMiG,QAAQ,CAACC,EAAG,2BACxCxF,MAAOwD,EACPS,UAAWH,EACX2B,WAAY/E,EAAE,2CAKtB,WAAC2D,EAAAA,CAAGA,CAAAA,WACF,UAACL,EAAAA,CAAGA,CAAAA,CAACiB,GAAI,EAAGC,GAAI,EAAGhB,GAAI,WACrB,WAACI,EAAAA,CAAIA,CAACC,KAAK,YACT,UAACD,EAAAA,CAAIA,CAACE,KAAK,WAAE9D,EAAE,kBACf,UAACyE,EAAAA,EAAWA,CAAAA,CACVC,gBAAiB,CACfC,gBAAiB3E,EAAE,qBACrB,EACA4E,QAAShG,EAAMqG,wBAAwB,EAAI,EAAE,CAC7CJ,SAAU,GAAYjG,EAAMiG,QAAQ,CAACC,EAAG,uBACxCxF,MAAOyD,EACPQ,UAAWH,EACX2B,WAAY/E,EAAE,6BAKpB,UAACsD,EAAAA,CAAGA,CAAAA,CAACiB,GAAI,EAAGC,GAAI,EAAGhB,GAAI,WACrB,WAACI,EAAAA,CAAIA,CAACC,KAAK,EAACQ,MAAO,CAAEa,SAAU,OAAQ,YACrC,UAACtB,EAAAA,CAAIA,CAACE,KAAK,WAAE9D,EAAE,eACf,UAACyE,EAAAA,EAAWA,CAAAA,CACVC,gBAAiB,CACfC,gBAAiB3E,EAAE,kBACrB,EACA4E,QAAShG,EAAMuG,mBAAmB,EAAI,EAAE,CACxCN,SAAU,GAAYjG,EAAMiG,QAAQ,CAACC,EAAG,oBACxCxF,MAAO0D,EACPO,UAAW,mBACXwB,WAAY,kCAIlB,UAACzB,EAAAA,CAAGA,CAAAA,CAACiB,GAAI,EAAGC,GAAI,EAAGhB,GAAI,WACrB,WAACI,EAAAA,CAAIA,CAACC,KAAK,YACT,UAACD,EAAAA,CAAIA,CAACE,KAAK,WAAE9D,EAAE,aACf,UAACyE,EAAAA,EAAWA,CAAAA,CACVC,gBAAiB,CACfC,gBAAiB3E,EAAE,gBACrB,EACA4E,QAAShG,EAAMwG,mBAAmB,EAAI,EAAE,CACxCP,SAAU,GAAYjG,EAAMiG,QAAQ,CAACC,EAAG,kBACxCxF,MAAO2D,EACPM,UAAW,mBACXwB,WAAY,qCAKpB,UAACzB,EAAAA,CAAGA,CAAAA,CAACC,UAAU,eAAeC,GAAI,YAChC,UAACC,KAAAA,UACC,UAACC,OAAAA,UAAM1D,EAAE,sCAGb,UAAC2D,EAAAA,CAAGA,CAAAA,UACF,UAACL,EAAAA,CAAGA,CAAAA,CAACiB,GAAI,GAAIf,GAAI,GAAIgB,GAAI,YACvB,UAACa,EAAAA,EAAMA,CAAAA,CACLC,mBAAmB,EACnBC,WAAYxG,EACZyG,OAAO,IACPlG,MAAO6D,GAAY,EAAE,CACrBsC,YAAczF,EAAE,eAChB6E,SAlOOa,CAkOGC,OAjOC/G,EACgB8G,EADvC,IAAME,EAAiBhH,CAAAA,OAAAA,EAAAA,EAAMiH,SAAAA,EAANjH,KAAAA,EAAAA,EAAiBU,GAAjBV,EAAiBU,GAAS,WACjD,GAAgBoG,EAASI,MAAM,CAAG,GAAKJ,CAAAA,OAAAA,EAAAA,CAAQ,CAACA,EAASI,MAAM,CAAG,IAA3BJ,KAAAA,EAAAA,EAA+BpG,GAA/BoG,EAA+BpG,IAAUsG,EACvEhH,EAAMiG,QAAQ,CACnBjG,EAAMmH,CAFsF,eAEtE,EAAI,EAAE,CAC5B,YAGKnH,EAAMiG,QAAQ,CAACa,GAAY,EAAE,CAAE,WAE1C,EAyNcd,QAAS,CACPhG,EAAMiH,SAAS,EAAI,CAAEvE,MAAO,YAAahC,MAAO,GAAI,KAChDV,EAAMmH,gBAAgB,EAAI,EAAE,CACjC,OAiBP,UAACzC,EAAAA,CAAGA,CAAAA,CAACC,UAAU,eAAeC,GAAI,YAChC,UAACC,KAAAA,CAAGF,UAAU,gBACZ,UAACG,OAAAA,UAAM1D,EAAE,4BAGb,UAAC2D,EAAAA,CAAGA,CAAAA,UACF,WAACL,EAAAA,CAAGA,CAAAA,CAACiB,GAAI,GAAIf,GAAI,GAAIgB,GAAI,aACvB,UAACwB,QAAAA,UAAOhG,EAAE,qBACV,UAACiG,EAAAA,CAAeA,CAAAA,CACdV,WAAYxG,EACZK,WAAYA,GAAc,GAC1B8G,WAAW,IACXV,OAAO,IACPW,YAAY,EACZtB,SAxPQuB,CAwPEC,EAtPpB9G,EAAS6G,GAAWrF,MAAMC,OAAO,CAACoF,GAAWA,EAAU,EAAE,EAuP/CE,cAnPY,CAmPGC,EAnPqBlH,EAAcmH,GAAiB,IAoPnEC,UAAWnE,EACXoE,OAAQhE,EACR+C,YAAczF,EAAE,oBAChBV,MAAOA,GAAS,EAAE,GAEnBI,GAAc,UAACsG,QAAAA,CAAMzC,UAAU,uBAAevD,EAAE,6BAChDJ,GAAiB,WAACoG,QAAAA,CAAMzC,UAAU,wBAAc,IAAEzD,EAAe,KAAGE,EAAE,+BAQrF,EAEAlB,EAAgB6H,YAAY,CAAG,CAC7Bd,UAAW,CACTvE,MAAO,YACPhC,MAAO,GACT,CACF,gBC3UA,IAAMsH,EAAoB,CACtBtG,MAAO,GACPuG,YAAa,GACbC,UAAW,KACXC,QAAS,KACTC,WAAY,GACZ9D,YAAY,EACZ+D,OAAQ,EAAE,CACVjD,SAAS,EACTkD,cAAe,GACfC,WAAY,EAAE,CACdC,WAAY,EAAE,CACdC,QAAS,EAAE,CACXC,QAAS,EAAE,CACXC,SAAU,EAAE,EA6hBhB,EA1hBmB,IACf,IAAMC,EAAYC,CAAAA,EAAAA,EAAAA,MAAAA,CAAMA,CAAM,EAyhBR,IAvhBhBC,EAASC,CAAAA,EAAAA,EAAAA,SAAAA,CAASA,GAClB,GAAE3H,CAAC,CAAE,CAAGE,CAAAA,EAAAA,EAAAA,EAAAA,CAAcA,CAAC,UACvB,CAAC0H,EAAoBC,EAAsB,CAAG1I,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAQ,EAAE,EAChE,CAAC2I,EAAeC,EAAiB,CAAG5I,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAQ,EAAE,EACtD,CAAC6I,EAAkBC,EAAoB,CAAG9I,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAQ,EAAE,EAC5D,CAAC+I,EAAeC,EAAiB,CAAGhJ,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAQ,EAAE,EACtD,CAACiJ,EAAWC,EAAa,CAAGlJ,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAMyH,GAC1C,CAAC0B,EAAWC,EAAa,CAAGpJ,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAQ,EAAE,EAC9C,CAACqJ,EAAaC,EAAe,CAAGtJ,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAQ,EAAE,EAClD,CAACuJ,EAAmBC,EAAgB,CAAGxJ,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAQ,EAAE,EACzD,CAACyJ,EAAcC,EAAe,CAAG1J,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAQ,EAAE,EACnD,CAAC2J,EAAeC,EAAiB,CAAG5J,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAQ,EAAE,EACtD,EAAG6J,EAAa,CAAG7J,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,EAAU,GACrC,CAAC8J,EAAQC,EAAU,CAAG/J,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAM,MACpC,CAACgK,EAAaC,EAAe,CAAGjK,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAM,MAC9C,CAACkK,EAAiBC,EAAmB,CAAGnK,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAM,CACxDyD,eAAgB,EAAE,CAClBC,cAAe,EAAE,CACjBC,wBAAyB,EAAE,CAC3BC,oBAAqB,EAAE,CACvBC,iBAAkB,EAAE,CACpBC,eAAgB,EAAE,CAClBE,SAAU,EAAE,GAGVoG,EAAU9B,CAAAA,EAAAA,EAAAA,MAAAA,CAAMA,CAAM,MAEtB+B,EAAoB,IACtBnB,EAAa,GAAqB,EAC9B,GAAGoB,CAAS,CACZ5C,EAF8B,UAEjBvH,EACjB,EACJ,EAEMoK,GAAe,CAACC,EAAWnH,KAC7B6F,EAAcoB,GAAoB,EAC9B,GAAGA,CAAS,CACZ,CAACjH,CAF6B,CAEzB,CAAEmH,EACX,EACJ,EA6BMC,GAAe,MAAOrH,QAiBpBsH,EACAC,EAVJ,GAPItC,EAAUuC,OAAO,EAAE,EACTA,OAAO,CAACC,YAAY,CAAC,WAAY,YAG/CzH,EAAME,cAAc,GAGhB,CAAC2F,EAAU9H,KAAK,EAAI8H,EAAU9H,KAAK,CAACwF,MAAM,CAAG,EAAG,CAChDmE,EAAAA,EAAKA,CAACC,KAAK,CAAClK,EAAE,qBACVwH,EAAUuC,OAAO,EAAE,EACTA,OAAO,CAACI,eAAe,CAAC,YAEtC,MACJ,CAEAnB,GAAa,GAIb,IAAM9G,EAASmH,EAAgBlG,QAAQ,CAAC2C,MAAM,CAAG,EAAIuD,EAAgBlG,QAAQ,CAACjC,GAAG,CAAEC,GAAcA,EAAK7B,KAAK,EAAI,EAAE,CAC3G2B,EAAY,CACdX,MAAO8H,EAAU9H,KAAK,CACtBuG,YAAauB,EAAUvB,WAAW,CAClCuD,WAAYhC,EAAUtB,SAAS,CAC/BuD,SAAUjC,EAAUrB,OAAO,CAC3B7D,YAAqC,IAAzBkF,EAAUlF,UAAU,CAChC+D,OAAQmB,EAAUnB,MAAM,CACxBG,WAAYgB,EAAUhB,UAAU,CAChCC,QAASnF,EACTiF,WACIiB,EAAUjB,UAAU,EAAIiB,EAAUjB,UAAU,CAACrB,MAAM,CAAG,EAChDsC,EAAUjB,UAAU,CAACjG,GAAG,CAAC,GAAeC,EAAK7B,KAAK,EAClD,GACViI,SAAUa,EAAUb,QAAQ,CAC5BD,QAASc,EAAUd,OAAO,EAGV,aAAa,CAA7B6B,EACAlI,EAAK,EAAD,OAAa,CAAGgI,EAEpBhI,EAAK,EAAD,KAAW,CAAGgI,EAGtB,GAAI,CACIrK,EAAM0L,MAAM,EAAwB,SAApB1L,EAAM0L,MAAM,CAAC,EAAE,EAAe1L,EAAM0L,MAAM,CAAC,EAAE,EAAE,EACpD,yCACXT,EAAW,MAAMhJ,EAAAA,CAAUA,CAAC0J,KAAK,CAAC,WAA2B,OAAhB3L,EAAM0L,MAAM,CAAC,EAAE,EAAIrJ,KAEhE6I,EAAW,uCACXD,EAAW,MAAMhJ,EAAAA,CAAUA,CAACsB,IAAI,CAAC,UAAWlB,IAE5C4I,GAAYA,EAAStI,GAAG,EAAE,EAC1B0I,EAAKA,CAACO,OAAO,CAACxK,EAAE8J,IAChBW,IAAAA,IAAW,CAAC,sBAAuB,gBAA6B,OAAbZ,EAAStI,GAAG,KAE/D0I,EAAAA,EAAKA,CAACC,KAAK,CAAClK,EAAE,qDACVwH,EAAUuC,OAAO,EAAE,EACTA,OAAO,CAACI,eAAe,CAAC,YAG9C,CAAE,MAAOD,EAAY,CACjBQ,QAAQR,KAAK,CAAC,8BAA+BA,GAC7CD,EAAAA,EAAKA,CAACC,KAAK,CAAClK,EAAE,qDACVwH,EAAUuC,OAAO,EAAE,EACTA,OAAO,CAACI,eAAe,CAAC,WAE1C,CACJ,EAEMQ,GAAc,IAChBC,GAAYA,EAAS3D,MAAM,CAAGY,EAAsB+C,EAAS3D,MAAM,EAAIY,EAAsB,EAAE,EAC/F+C,GAAYA,EAASxD,UAAU,CAAGW,EAAiB6C,EAASxD,UAAU,EAAIW,EAAiB,EAAE,EAC7F6C,GAAYA,EAASrD,QAAQ,CAAGY,EAAiByC,EAASrD,QAAQ,EAAIY,EAAiB,EAAE,EACzFyC,GAAYA,EAAStD,OAAO,CAAGW,EAAoB2C,EAAStD,OAAO,EAAIW,EAAoB,EAAE,CACjG,EACM4C,GAAgB,UAClB,IAAMC,EAAU,MAAMjK,EAAAA,CAAUA,CAACsB,IAAI,CAAC,uBAAwB,CAAC,GACzDyI,EAAW,MAAM/J,EAAAA,CAAUA,CAACC,GAAG,CAAC,WAA2B,OAAhBlC,EAAM0L,MAAM,CAAC,EAAE,GAC1DrJ,EAAY,CACdX,MAAOsK,EAAStK,KAAK,CACrBuG,YAAa+D,EAAS/D,WAAW,CACjCC,UAAW8D,EAASR,UAAU,CAAGW,IAAOH,EAASR,UAAVW,EAAsBC,MAAM,GAAK,KACxEjE,QAAS6D,EAASP,QAAQ,CAAGU,IAAOH,EAASP,QAAQ,EAAlBU,MAA0B,GAAK,KAClE7D,cAAe0D,EAAS1D,aAAa,CAAG0D,EAAS1D,aAAa,CAAG,KACjEhE,UAAAA,GAAY0H,EAAS1H,UAAU,CAC/B+H,EADkC,GAC5BL,EAASK,EAD0B,EACtB,CAAGL,EAASK,IAAI,CAAC1J,GAAG,CAAG,GAC1C0F,OAAQ2D,EAAS3D,MAAM,CACvBG,WAAYwD,EAASxD,UAAU,CAC/BD,WAC+B,KAA3ByD,EAASzD,UAAU,CAAC,EAAE,CAAUyD,EAASzD,UAAU,CAACjG,GAAG,CAAC,GAAgB,EAAEI,EAAF,IAASH,EAAM7B,MAAO6B,EAAK,GAAM,EAAE,EAEnH,GAAIyJ,EAASvD,OAAO,CAACvB,MAAM,CAAG,EAAG,CAC7B,IAAIoF,EAAuB,EAAE,CAC7BN,EAASvD,OAAO,CAAC8D,OAAO,CAAC,IACrBD,EAAcE,IAAI,CAAC,CAAE9J,MAAO+J,EAAOC,QAAQ,CAAEhM,MAAO+L,EAAO9J,GAAG,EAClE,GACA+H,EAAmB,CAAE,GAAGD,CAAe,CAAOlG,SAAU+H,CAAgB,EAC5E,CAOA,OANAP,GAAYC,GAERA,EAASK,IAAI,EAAIL,EAASK,IAAI,CAAC1J,GAAG,GAAKuJ,EAAQ,GAAM,EACrDL,IAAAA,IAAW,CAAC,WAEhBpC,EAAapH,GACN2J,EAASP,QAAQ,CAAGhC,EAAa,GAAqB,EAAE,GAAGoB,CAAS,CAAEzF,EAAhB,OAAyB,EAAK,GAAM,IACrG,EAEAxD,CAAAA,EAAAA,EAAAA,SAAAA,CAASA,CAAC,KACF5B,EAAM0L,MAAM,EAAwB,SAApB1L,EAAM0L,MAAM,CAAC,EAAE,EAAe1L,EAAM0L,MAAM,CAAC,EAAE,EAAE,KAInEpB,EAAUxB,GAAUA,EAAOhG,KAAK,EAAIgG,EAAOhG,KAAK,CAAC6J,EAAE,CAAG7D,EAAOhG,KAAK,CAAC6J,EAAE,CAAG,MACxEnC,EAAe1B,GAAUA,EAAOhG,KAAK,EAAIgG,EAAOhG,KAAK,CAAC8J,MAAM,CAAG9D,EAAOhG,KAAK,CAAC8J,MAAM,CAAG,KACzF,EAAG,EAAE,EAGLhL,CAAAA,EAAAA,EAAAA,SAAAA,CAASA,CAAC,KAEN,IAAMiL,EAAa,CACf/J,MAAO,CAAC,EACRC,KAAM,CAAE2J,SAAU,KAAM,EACxB1J,MAAO,IACP8J,OAAQ,wUACZ,EAkBAC,CAhBqB,cAEbxI,EADJ,IAAMA,EAAW,MAAMtC,EAAAA,CAAUA,CAACC,GAAG,CAAE,SAAS2K,UAC5CtI,GAAAA,OAAAA,EAAAA,EAAUlC,IAAAA,EAAVkC,CAAAA,IAAAA,EAAAA,EAAgB2C,GAAhB3C,GAAsB,IACtBA,EAASlC,IAAI,CAAGkC,EAASlC,IAAI,CAAC2K,MAAM,CAAC,GACL,oBAA5BC,EAAUC,aAAa,EAA0BD,oBAC3C,EADqDE,MAAM,CAE3D,EAEV5I,GAIAoF,EAHepF,EAASlC,GADd,CACkB,CAACC,GAAG,CAAC,CAACC,EAAWE,KAClC,CAAEC,MAAOH,EAAKmK,QAAQ,CAAEhM,MAAO6B,EAAKI,GAAG,CAAC,IAI3D,GAGJ,EAAG,CAAC8H,EAAgB,EAUpB2C,EAAAA,SAAe,CAAC,KACZ,GAAI3C,EAAiB,CACjB,IAAM4C,EAAsB,CAAC,EAC7BC,OAAOC,IAAI,CAAC9C,GAAiB8B,OAAO,CAAC,CAAChK,EAAWE,KAC7C,IAAM+K,EACF,CAAwB,CAACjL,EAAK,CAAC2E,MAAM,CAAG,GACxC,CAAwB,CAAC3E,EAAK,CAACD,GAAG,CAAC,GACxBmL,EAAE/M,KAAK,EAEtB2M,CAAc,CAAC9K,EAAK,CAAGiL,GAAgB,EAAE,GAAVA,GAE1BH,EACb,MACIvB,CADG,OACK4B,GAAG,CAAC,wBAEpB,EAAG,CAACjD,EAAgB,EAEpB,IAAMkD,GAAiB,IACfC,GAAezL,MAAMC,OAAO,CAACwL,EAAYvL,IAAI,GAAG,EAC3BuL,EAAYvL,IAAI,CAACC,GAAG,CAAC,GAG1BuL,CAFL,CAAEnL,MAAOH,EAAKb,KAAK,CAAEhB,MAAO6B,EAAKI,GAAG,CAAC,GAIxD,EAEMmL,GAAe,IACbC,GAAa5L,MAAMC,OAAO,CAAC2L,EAAU1L,IAAI,GAAG,EACzB0L,EAAU1L,IAAI,CAACC,GAAG,CAAC,CAACC,EAAWE,CAGjCuL,GAFN,EAAEtL,MAAOH,EAAKb,KAAK,CAAEhB,MAAO6B,EAAKI,GAAG,CAAC,GAIxD,EAEMsL,GAAwB,IACtBC,GAAsB/L,MAAMC,OAAO,CAAC8L,EAAmB7L,IAAI,GAI3D4H,EAH4BiE,EAAmB7L,IAAI,CAACC,GAAG,CAAC,CAACC,CAG1C4L,CAHqD1L,KACzD,CAAEC,MAAOH,EAAKb,KAAK,CAAEhB,MAAO6B,EAAKI,GAAG,CAAC,GAIxD,EACMyL,GAAW,MAAOf,IACpB,GAAM,gBACFrJ,CAAc,eACdC,CAAa,yBACbC,CAAuB,qBACvBC,CAAmB,kBACnBC,CAAgB,gBAChBC,CAAc,CACjB,CAAGgJ,EAYAgB,EAAkB,EAAE,CACpBC,EAAuB,EAAE,CACzBC,EAAkB,EAAE,CACpBC,EAAkB,EAAE,CAClBC,EAAc,MAAMxM,EAAAA,CAAUA,CAACsB,IAAI,CAAC,oBAftB,CAe2CmL,MAdpD,CACH3M,QAASiC,EACT2K,eAAgB1K,EAChB2K,iBAAkB1K,EAClB0J,YAAazJ,EACb0K,WAAYzK,EACZ0K,SAAUzK,EACVmB,KAAM,QACV,CACJ,GAMA,GAAIiJ,GAAetM,MAAMC,OAAO,CAACqM,GAAc,CAO3C,GANIA,CAAW,CAAC,EAAE,CAACM,OAAO,EAAIN,CAAW,CAAC,EAAE,CAACM,OAAO,CAAC7H,MAAM,CAAG,GAAG,EAClDuH,CAAW,CAAC,EAAE,CAACM,OAAO,CAGlBV,GAHsB,CAAC,CAAC9L,EAAWE,KACvC,CAAEC,MAAOH,EAAKb,KAAK,CAAEhB,MAAO6B,EAAKI,GAAG,CAAC,IAIhD8L,CAAW,CAAC,EAAE,CAACO,YAAY,EAAIP,CAAW,CAAC,EAAE,CAACO,YAAY,CAAC9H,MAAM,CAAG,EAUpE6C,CAVuE,CACvD0E,CAAW,CAAC,EAAE,CAACO,SASfV,GAT2B,CAAChM,GAAG,CAAC,CAAC2M,EAAUC,KACvDV,EAAWS,EAAIH,QAAQ,CAACxM,GAAG,CAAC,IACjB,CAAEI,MAAOH,EAAKb,KAAK,CAAEhB,MAAO6B,EAAKI,GAAG,CAAC,GAEhD4L,EAAWU,EAAIlB,SAAS,CAACzL,GAAG,CAAC,IAClB,CAAEI,MAAOH,EAAKb,KAAK,CAAEhB,MAAO6B,EAAKI,GAAG,CAAC,GAEzC,CAAED,MAAOuM,EAAIvN,KAAK,CAAEhB,MAAOuO,EAAItM,GAAI,KAG9CsH,EAAeuE,GACfrE,EAAiBoE,QACd,GAA2C,IAAvCE,CAAW,CAAC,EAAE,CAACO,YAAY,CAAC9H,MAAM,CAAQ,CACjD,IAAMiI,EAAoB,CACtBrM,MAAO,CAAC,EACRC,KAAM,CAAErB,MAAO,KAAM,EACrBsB,MAAO,GACX,EAEA2K,GADoB,MAAM1L,EAAAA,CAAUA,CAACC,EACtB0L,CADyB,CAAC,eAAgBuB,IAIzDrB,GADkB,MAAM7L,EAAAA,CAAUA,CAACC,GAAG,CAAC,aAAciN,IAIrDlB,GAD2B,MAAMhM,EAAAA,CAAUA,CAACC,GAAG,CAAC,KAC1BgM,iBADiDiB,GAE3E,CACIV,CAAW,CAAC,EAAE,CAAC/E,SAAS,EAAI+E,CAAW,CAAC,EAAE,CAAC/E,SAAS,CAACxC,MAAM,CAAG,GAAG,EAClDuH,CAAW,CAAC,EAAE,CAAC/E,MAGjBpG,GAH0B,CAAChB,GAAG,CAAC,CAACC,EAAWE,KAC7C,CAAEC,MAAOH,EAAKmK,QAAQ,CAAEhM,MAAO6B,EAAKI,GAAG,IAI1D,CACJ,EAiBMyM,GAAQ,IACV,IAAMC,EAAkB,EAAE,CACpBC,EAAgB,EAAE,CACpB3C,EAAGzF,MAAM,CAAG,GAAG,EACZ5E,GAAG,CAAC,IAECC,EAAKiD,IAAI,GACRjD,CAAAA,CAAKiD,IAAI,CAAC+J,QAAQ,CAAC,QAChBhN,EAAKiD,IAAI,CAAC+J,QAAQ,CAAC,SACnBhN,EAAKiD,IAAI,CAAC+J,QAAQ,CAAC,SACnBhN,EAAKiD,IAAI,CAAC+J,QAAQ,CAAC,OAAK,CAE5BD,EAAO9C,IAAI,CAACjK,EAAKiN,QAAQ,EAEzBH,EAAS7C,IAAI,CAACjK,EAAKiN,QAAQ,CAEnC,GAEJ/F,EAAa,GAAqB,EAAE,GAAGoB,CAAS,CAAExC,EAAhB,KAAwBgH,EAAS,GACnE5F,EAAa,GAAqB,EAAE,GAAGoB,CAAS,CAAElC,EAAhB,OAA0B2G,EAAO,EACvE,EAEMG,GAAY,IACdhG,EAAcoB,GAAoB,EAAE,GAAGA,CAAS,CAAErC,EAAhB,SAA4BkH,EAAU,EAC5E,EAEMC,GAAe,IACjBlG,EAAa,GAAqB,EAAE,GAAGoB,CAAS,CAAEnC,EAAhB,MAAyBkH,EAAU,EACzE,EAEMC,GAAiB,IACnB,GAAM,MAAExK,CAAI,CAAE3E,OAAK,CAAE,CAAGwF,EAAE4J,MAAM,CAChCrG,EAAcoB,GAAoB,EAC9B,GAAGA,CAAS,CACZ,CAACxF,CAF6B,CAExB,CAAE3E,EACZ,EACJ,EAEA,MACI,UAACqP,EAAAA,CAASA,CAAAA,CAACpL,UAAU,WAAWqL,KAAK,aACjC,UAACC,EAAAA,CAAIA,CAAAA,UACD,UAACC,EAAAA,EAAcA,CAAAA,CACXC,SAAUnF,GACVoF,IAAKzF,EACL0F,WAAanK,IACC,UAAVA,EAAEtC,GAAG,EAAgBsC,EAAErC,cAAc,EACzC,WAEA,WAACoM,EAAAA,CAAIA,CAACK,IAAI,YACN,UAACvL,EAAAA,CAAGA,CAAAA,UACA,UAACL,EAAAA,CAAGA,CAAAA,UACA,UAACuL,EAAAA,CAAIA,CAACM,KAAK,WACc,SAApBvQ,EAAM0L,MAAM,CAAC,EAAE,CACVtK,EAAE,2BACFA,EAAE,gCAIpB,UAACoP,KAAAA,CAAAA,GAED,UAACzL,EAAAA,CAAGA,CAAAA,CAACJ,UAAU,gBACX,UAACD,EAAAA,CAAGA,CAAAA,UACA,WAACM,EAAAA,CAAIA,CAACC,KAAK,YACP,UAACD,EAAAA,CAAIA,CAACE,KAAK,EAACP,UAAU,0BAAkBvD,EAAE,kBAC1C,UAAC4D,EAAAA,CAAIA,CAACyL,OAAO,EACTC,UAAW,EACXC,QAAQ,IACRnL,KAAK,OACLH,KAAK,QACL3E,MAAO8I,EAAU9H,KAAK,CACtBuE,SAAU4J,KAEd,UAAC7K,EAAAA,CAAIA,CAACyL,OAAO,CAACG,QAAQ,EAACpL,KAAK,mBACI,IAA3BgE,EAAU9H,KAAK,CAACwF,MAAM,CACjB9F,EAAE,uBACFA,EAAE,6BAMxB,UAAC2D,EAAAA,CAAGA,CAAAA,CAACJ,UAAU,gBACX,UAACD,EAAAA,CAAGA,CAAAA,UACA,WAACM,EAAAA,CAAIA,CAACC,KAAK,YACP,UAACD,EAAAA,CAAIA,CAACE,KAAK,WAAE9D,EAAE,iBACf,UAACyP,EAAAA,CAAeA,CAAAA,CAACC,YAAatH,EAAUvB,WAAW,CAAEhC,SAAU,GAAc2E,EAAkBmG,YAK3G,UAAChM,EAAAA,CAAGA,CAAAA,CAACJ,UAAU,gBACX,UAACD,EAAAA,CAAGA,CAAAA,CAACE,GAAI,YACL,WAACI,EAAAA,CAAIA,CAACC,KAAK,YACP,UAACD,EAAAA,CAAIA,CAACE,KAAK,WAAE9D,EAAE,kBACf,UAAC4P,EAAAA,CAAaA,CAAAA,CACVC,MAAOjI,EACPkI,QAAShI,EACTiI,SAAU,GAAa/B,GAAMzC,GAC7ByE,eAAgB,GAAoB3B,GAAUC,YAK9D,UAAC3K,EAAAA,CAAGA,CAAAA,CAACJ,UAAU,gBACX,UAACD,EAAAA,CAAGA,CAAAA,CAACE,GAAI,YACL,WAACI,EAAAA,CAAIA,CAACC,KAAK,YACP,UAACD,EAAAA,CAAIA,CAACE,KAAK,WAAE9D,EAAE,sBACf,UAAC4P,EAAAA,CAAaA,CAAAA,CACVxL,KAAK,cACLyL,MAAO3H,EACP4H,QAAS9H,EACT+H,SAAU,GAAa/B,GAAMzC,GAC7ByE,eAAgB,GAAoBzB,GAAaC,YAKjE,WAAC7K,EAAAA,CAAGA,CAAAA,CAACJ,UAAU,iBACX,UAACD,EAAAA,CAAGA,CAAAA,CAACiB,EAAE,IAACf,GAAI,EAAGgB,GAAI,YACf,WAACZ,EAAAA,CAAIA,CAACC,KAAK,YACP,UAACD,EAAAA,CAAIA,CAACE,KAAK,EAACP,UAAU,mBAAWvD,EAAE,sBACnC,UAACnB,EAAAA,CAAaA,CAAAA,CACV6G,SAAU0C,EAAUtB,SAAS,CAC7BjC,SAAU,GAAe6E,GAAaC,EAAM,aAC5CsG,WAAW,eACXC,gBAAiBlQ,EAAE,6BAI/B,UAACsD,EAAAA,CAAGA,CAAAA,CAACiB,EAAE,IAACf,GAAI,EAAGgB,GAAI,GAAIjB,UAAU,kBAC7B,UAACK,EAAAA,CAAIA,CAACG,KAAK,EACPK,KAAK,WACLJ,QAASoE,EAAUpE,OAAO,CAC1Ba,SAzbF,CAybYsL,IAxbtC9H,EAAa,GAAqB,EAC9B,GAAGoB,CAAS,CACZzF,EAF8B,MAErB,CAACyF,EAAUzF,OAAO,CAC/B,EACJ,EAqbgC1C,MAAOtB,EAAE,0BAGhBoI,EAAUpE,OAAO,EACd,UAACV,EAAAA,CAAGA,CAAAA,CAACiB,EAAE,IAACf,GAAI,EAAGgB,GAAI,YACf,WAACZ,EAAAA,CAAIA,CAACC,KAAK,YACP,UAACD,EAAAA,CAAIA,CAACE,KAAK,EAACP,UAAU,mBAAWvD,EAAE,oBACnC,UAACnB,EAAAA,CAAaA,CAAAA,CACV6G,SAAU0C,EAAUrB,OAAO,CAC3BqJ,QAAShI,EAAUtB,SAAS,CAC5BjC,SAAU,GAAe6E,GAAaC,EAAM,WAC5CsG,WAAW,eACXC,gBAAiBlQ,EAAE,gCAMvC,UAAClB,EAAeA,CACX,GAAGuK,CAAe,CAClB,GAAGjB,CAAS,CACbvC,ED/LE/G,EAAC,KC4LSA,CAGD,CAAEwC,MAAO,YAAahC,MAAO,GAAI,EAC5CyG,iBAAkBuC,EAClBtD,mBAAoBwD,EACpBvD,yBAA0ByD,EAC1BvD,oBAAqB2D,EACrB1D,oBAAqBwD,EACrB/D,SA/KG,CA+KOwL,EA/KEpM,KAChCqF,EAAmB,GAAqB,EACpC,GAAGG,CAAS,CACZ,CAACxF,CAFmC,CAE9B,CAAO,MAALa,EAAY,EAAE,CAAGA,EAC7B,EACJ,EA2KwBX,iBAzKH,CAyKqBmM,IAxKtCjI,EAAa,GAAqB,EAC9B,GAAGoB,CAAS,CACZvG,EAF8B,SAElB,CAACuG,EAAUvG,UAAU,CACrC,EACJ,EAqKwBuL,eAAgBA,GAChB3M,UAxSC,CAwSUyO,GAvS/BlI,EAAa,GAAqB,EAAE,GAAGoB,CAAS,CAAEtC,EAAhB,SAA4BnF,EAAM,EACxE,IAwSoB,UAAC2B,EAAAA,CAAGA,CAAAA,CAACJ,UAAU,gBACX,WAACD,EAAAA,CAAGA,CAAAA,WACA,UAACkN,EAAAA,CAAMA,CAAAA,CAACjN,UAAU,OAAOa,KAAK,SAASqM,QAAQ,UAAUzB,IAAKxH,WACzDxH,EAAE,YAEP,UAACwQ,EAAAA,CAAMA,CAAAA,CAACjN,UAAU,OAAOW,QAxdhC,CAwdyCwM,IAvd1DrI,EAAazB,GACbiB,EAAsB,EAAE,EACxBE,EAAiB,EAAE,EACnBI,EAAiB,EAAE,EACnBF,EAAoB,EAAE,EACtBqB,EAAmB,CACf1G,eAAgB,EAAE,CAClBC,cAAe,EAAE,CACjBC,wBAAyB,EAAE,CAC3BC,oBAAqB,EAAE,CACvBC,iBAAkB,EAAE,CACpBC,eAAgB,EAAE,CAClBE,SAAU,EACd,GAEA6F,GAAa,GACb2H,OAAOC,QAAQ,CAAC,EAAG,EACvB,EAsc4EH,QAAQ,gBACnDzQ,EAAE,WAEP,UAAC6Q,IAAIA,CAACC,KAAK,KAAND,KAAgBE,GAAG,mBACpB,UAACP,EAAAA,CAAMA,CAAAA,CAACC,QAAQ,qBAAazQ,EAAE,2BASnE,8ICneO,IAAMgR,EAAQ,CACnBC,WA1C4C,OAAC,MAC7ChN,CAAI,eACJiN,CAAa,UACbrM,CAAQ,cACRsM,CAAY,UACZC,CAAQ,CACT,GACO,QAAEC,CAAM,SAAEC,CAAO,CAAE,CAAGC,CAAAA,EAAAA,EAAAA,EAAAA,CAAgBA,GACtCC,EAAWF,CAAO,CAACrN,EAAK,EAAIoN,CAAM,CAACpN,EAAK,CAGzB+H,EAAAA,OAAa,CAAC,IAAO,OAAE/H,EAAK,EAAI,CAACA,EAAK,EAG3D,IAAMwN,EAAoBzF,EAAAA,QAAc,CAAC9K,GAAG,CAACkQ,EAAU,GACrD,EAAIpF,cAAoB,CAAC0F,IAEnBC,IAF2B,KAxC5BA,CAAmB,EAC1B,MAAwB,UAAjB,OAAO/S,GAAgC,OAAVA,CACtC,EAwCmB8S,EAAM9S,KAAK,EACfoN,CADkB,CAClBA,YAAkB,CAAC0F,EAA6C,MACrEzN,EACA,GAAGyN,EAAM9S,KAAK,GAIb8S,GAGT,MACE,WAACrO,MAAAA,WACC,UAACA,MAAAA,CAAIE,UAAU,uBACZkO,IAEFD,GACC,UAACnO,MAAAA,CAAIE,UAAU,oCACZ4N,GAAiB,kBAAOE,CAAM,CAACpN,EAAK,CAAgBoN,CAAM,CAACpN,EAAK,CAAG2N,OAAOP,CAAM,CAACpN,GAAK,MAKjG,EAIE4N,UAhE0C,OAAC,IAAEtG,CAAE,OAAEjK,CAAK,CAAEhC,OAAK,MAAE2E,CAAI,UAAE6N,CAAQ,CAAE,GACzE,QAAEC,CAAM,eAAEC,CAAa,CAAE,CAAGT,CAAAA,EAAAA,EAAAA,EAAAA,CAAgBA,GAC5CU,EAAYhO,GAAQsH,EAE1B,MACE,UAAC3H,EAAAA,CAAIA,CAACG,KAAK,EACTK,KAAK,QACLmH,GAAIA,EACJjK,MAAOA,EACPhC,MAAOA,EACP2E,KAAMgO,EACNjO,QAAS+N,CAAM,CAACE,EAAU,GAAK3S,EAC/BuF,SAAWC,IACTkN,EAAcC,EAAWnN,EAAE4J,MAAM,CAACpP,KAAK,CACzC,EACAwS,SAAUA,EACVI,MAAM,KAGZ,CA8CA,EAAE,ECzEcpD,CAAAA,CACLqD,EAAAA,EAAAA,CACEC,EAAAA,EAAAA,mFCeb,IAAMC,EAAwBC,CAAAA,EAAAA,EAAAA,UAAAA,CAAUA,CAA8C,CAAC1T,EAAOoQ,KAC5F,GAAM,UAAEoC,CAAQ,UAAErC,CAAQ,cAAEwD,CAAY,WAAEhP,CAAS,CAAE0L,YAAU,eAAEuD,CAAa,CAAE,GAAGC,EAAM,CAAG7T,EAGtF8T,EAAmBC,EAAAA,EAAU,GAAGC,KAAK,CAAC,CAAC,GAE7C,MACE,UAACC,EAAAA,EAAMA,CAAAA,CACLL,cAAeA,GAAiB,CAAC,EACjCE,iBAAkBA,EAClB3D,SAAU,CAACgD,EAA6Be,KAEtC,IAAMC,EAAuB,CAC3BtQ,eAAgB,KAAO,EACvBuQ,gBAAiB,KAAO,EACxBC,cAAe,KACfvE,OAAQ,KACRwE,YAAa,IAAIC,MAAM,UACvBC,SAAS,EACTC,YAAY,EACZC,kBAAkB,EAClBC,WAAY,EACZC,WAAW,EACXC,UAAWC,KAAKC,GAAG,GACnBvP,KAAM,SACNwP,mBAAoB,KAAM,EAC1BC,qBAAsB,KAAM,EAC5BC,QAAS,KAAO,CAClB,EAEI/E,GAEFA,EAASgE,EAAWhB,EAAQe,CAFhB,CAIhB,EACC,GAAGL,CAAI,UAEP,GACC,UAAC7O,EAAAA,EAAIA,CAAAA,CACHoL,IAAKA,EACLD,SAAUgF,EAAYC,YAAY,CAClCzB,aAAcA,EACdhP,UAAWA,EACX0L,WAAYA,WAES,YAApB,OAAOmC,EAA0BA,EAAS2C,GAAe3C,KAKpE,GAEAiB,EAAsB4B,WAAW,CAAG,wBAEpC,MAAe5B,qBAAqBA,EAAC,yEClF9B,IAAMF,EAAY,OAAC,MACxBlO,CAAI,IACJsH,CAAE,CACFgE,UAAQ,WACR2E,CAAS,cACT/C,CAAY,UACZtM,CAAQ,OACRvF,CAAK,IACLyR,CAAE,WACFoD,CAAS,MACTC,CAAI,CACJC,SAAO,CACP,GAAGzV,EACC,GAuBJ,MACE,UAAC0V,EAAAA,EAAKA,CAAAA,CAACrQ,KAAMA,EAAMsQ,SAtBHC,CAsBaD,GApB7B,IAAME,EAA2B,UAAf,OAAOD,EAAmBA,EAAM5C,OAAO4C,GAAO,WAChE,GAAiB,EAACA,GAA4B,IAA7B,CAAQC,EAAU9R,IAAI,EAAO,CAAC,CACtCwO,EAD0C,KAC1CA,EAAAA,KAAAA,EAAAA,EAAc+C,SAAAA,GAAa,EAA3B/C,uBAGL+C,GAAa,CAACA,EAAUM,GACnBrD,GADyB,MACzBA,KAAAA,EAAAA,EAAc+C,OAAd/C,EAAuB,GAAI,gBAGhCkD,GAAWG,GAET,CAACE,CAFa,GACAC,OAAON,GACdpS,IAAI,CAACuS,GACPrD,GADa,IACbA,EAAAA,KAAAA,EAAAA,EAAckD,OAAO,GAAI,IAAzBlD,mBAKb,WAIK,OAAC,OAAEyD,CAAK,CAAEC,MAAI,CAAO,SACpB,iCACE,UAACjR,EAAAA,CAAIA,CAACyL,OAAO,EACV,GAAGuF,CAAK,CACR,GAAGhW,CAAK,CACT2M,GAAIA,EACJwF,GAAIA,GAAM,QACVqD,KAAMA,EACNU,UAAWD,EAAKvD,OAAO,EAAI,CAAC,CAACuD,EAAK3K,KAAK,CACvCrF,SAAWC,IACT8P,EAAM/P,QAAQ,CAACC,GACXD,GAAUA,EAASC,EACzB,EACAxF,WAAiByV,IAAVzV,EAAsBA,EAAQsV,EAAMtV,KAAK,GAEjDuV,EAAKvD,OAAO,EAAIuD,EAAK3K,KAAK,CACzB,UAACtG,EAAAA,CAAIA,CAACyL,OAAO,CAACG,QAAQ,EAACpL,KAAK,mBACzByQ,EAAK3K,KAAK,GAEX,UAKd,EAAE,EAIyB,OAAC,MAC1BjG,CAAI,IACJsH,CAAE,UACFgE,CAAQ,cACR4B,CAAY,UACZtM,CAAQ,CACRvF,OAAK,CACL8R,UAAQ,CACR,GAAGxS,EACC,GAUJ,MACE,UAAC0V,EAAAA,EAAKA,CAAAA,CAACrQ,KAAMA,EAAMsQ,SATJ,CAScA,GAR7B,GAAIhF,GAAa,EAACiF,GAAe,IAAhB,CAAQA,CAAQ,CAAC,CAChC,EADoC,IAC7BrD,OAAAA,EAAAA,KAAAA,EAAAA,EAAc+C,SAAS,GAAI,EAA3B/C,sBAIX,WAIK,OAAC,OAAEyD,CAAK,CAAEC,MAAI,CAAO,SACpB,iCACE,UAACjR,EAAAA,CAAIA,CAACyL,OAAO,EACX0B,GAAG,SACF,GAAG6D,CAAK,CACR,GAAGhW,CAAK,CACT2M,GAAIA,EACJuJ,UAAWD,EAAKvD,OAAO,EAAI,CAAC,CAACuD,EAAK3K,KAAK,CACvCrF,SAAU,IACR+P,EAAM/P,QAAQ,CAACC,GACXD,GAAUA,EAASC,EACzB,EACAxF,WAAiByV,IAAVzV,EAAsBA,EAAQsV,EAAMtV,KAAK,UAE/C8R,IAEFyD,EAAKvD,OAAO,EAAIuD,EAAK3K,KAAK,CACzB,UAACtG,EAAAA,CAAIA,CAACyL,OAAO,CAACG,QAAQ,EAACpL,KAAK,mBACzByQ,EAAK3K,KAAK,GAEX,UAKd,EAAE,8DCtHF,YAAc,WAAW,GAAG,EAAE,kCAAkC,+FAAgG,0KAAwK,OAAS,qBAAqB,sBAAsB,yBAAyB,oBAAoB,kBAAkB,gBAAgB,eAAe,mBAAmB,eAAe,QAAQ,sBAAsB,wBAAwB,YAAY,uBAAuB,wBAAwB,kBAAkB,UAAU,SAAS,WAAW,gBAAgB,uCAAuC,gBAAgB,iCAAiC,0BAA0B,oDAAoD,0BAA0B,kBAAkB,UAAU,gCAAgC,oCAAoC,iCAAiC,2DAA2D,sCAAsC,8BAA8B,uCAAuC,sCAAsC,8BAA8B,wBAAwB,kBAAkB,wBAAwB,aAAa,mBAAmB,WAAW,qBAAqB,eAAe,UAAU,gDAAgD,gBAAgB,uBAAuB,mBAAmB,OAAO,6BAA6B,eAAe,gBAAgB,SAAS,UAAU,aAAa,eAAe,iBAAiB,gBAAgB,SAAS,eAAe,kBAAkB,gBAAgB,SAAS,mBAAmB,sBAAsB,eAAe,cAAc,sBAAsB,oBAAoB,kCAAkC,yBAAyB,6BAA6B,4BAA4B,gCAAgC,kBAAkB,sBAAsB,kBAAkB,uBAAuB,cAAc,WAAW,kBAAkB,2CAA2C,oBAAoB,gBAAgB,qBAAqB,wBAAwB,WAAW,UAAU,SAAS,cAAc,0BAA0B,6BAA6B,2BAA2B,eAAe,kBAAkB,MAAM,QAAQ,SAAS,gBAAgB,SAAS,kCAAkC,oCAAoC,aAAa,qBAAqB,aAAa,qBAAqB,2BAA2B,iBAAiB,8BAA8B,WAAW,eAAe,oCAAoC,qBAAqB,0BAA0B,iBAAiB,qBAAqB,yCAAyC,kBAAkB,GAAG,0BAA0B,gBAAgB,GAAG,uBAAuB,oBAAoB,IAAI,wBAAwB,sBAAsB,GAAG,wBAAwB;AACx8F,GAAkG,OAAQ,4PAA4P,IAAK,kFAAkF,GAAI,eAAgB,GAAG,MAAO,mBAAmB,IAAI,SAAS,cAAE,YAAgF,MAAO,eAAE,MAAM,aAAa,cAAc,SAAE,aAAc,OAAO,EAAjJ,IAAkB,MAAM,uDAAyH,iCAAsC,YAAY,EAAE,OAAO,YAAa,IAAwQ,GAAQ,gCAAgC,kBAAkB,MAAM,aAAE,mDAAmD,OAAQ,oBAAoB,KAAK,YAAE,KAAK,SAAS,GAAG,eAAE,MAAM,YAAY,EAAE,MAAM,iBAAE,KAAK,+CAA+C,MAAM,eAAE,MAAM,yBAAyB,yBAAyB,qBAAqB,2BAA2B,OAAO,cAAc,8BAA8B,IAAI,cAAc,OAAO,uFAAoL,UAAe,MAAM,sBAAsB,kCAAkC,gBAAgB,MAAiL,MAAU,UAAE,QAAQ,+HAA+H,SAAE,SAAS,8BAA8B,EAAE,SAAE,SAAS,8BAA8B,GAAG,EAAsF,IAAS,EAAsQ,MAAtQ,gCAAwC,GAAG,UAAE,QAAQ,2BAA2B,gBAAgB,YAAY,SAAE,UAAU,4DAA4D,EAAE,SAAE,SAAS,iBAAiB,GAAG,EAA8a,EAA7X,EAAS,CAAib,YAAjb,4EAA0F,IAAI,MAAM,YAAE,GAAgC,OAAQ,SAAS,CAAiB,2BAAlE,IAAS,uBAAuB,CAAkC,CAA8B,SAAS,EAAE,SAAE,UAAU,yBAAyB,gBAAgB,4DAA4D,SAAE,IAAI,2BAAjL,IAAO,UAA0K,WAAwC,EAAE,EAAE,CAAwd,EAA3Z,EAAS,EAA2c,MAA3c,wBAAgC,IAAI,IAAI,6CAA6C,eAAe,iDAAiD,MAAO,SAAC,CAAC,UAAE,EAAE,uBAAuB,UAAU,MAAO,SAAC,OAAO,SAAS,SAAC,IAAI,gJAAgJ,EAAE,4BAA4B,EAAE,EAAE,CAAoxE,EAA3tE,GAA0wE,EAA9vE,IAAI,qLAAqL,OAAO,YAAC,KAAK,YAAC,SAAS,cAAC,WAAW,cAAC,UAAU,cAAC,WAAW,cAAC,MAAM,iBAAE,oBAAqB,aAAE,MAAM,QAAQ,6BAA6B,WAAW,oDAAoD,OAAO,+CAA+C,MAAM,kCAAmC,6CAA8C,yCAAyC,CAAuE,OAAQ,MAAM,0DAA0D,WAAmJ,4BAAnJ,IAAkB,eAAe,sBAAuB,KAAM,wBAAwB,KAAM,gBAAe,wCAAwC,CAAgC,SAAS,EAAE,IAAiB,YAAa,OAAO,8BAA8B,8CAA8C,2BAAroF,cAAiB,oBAAoB,gBAAgB,kEAAsH,CAA09E,WAA2C,UAAU,yDAA6D,eAAE,MAAM,OAAQ,kFAA2E,EAAE,iBAAuB,MAAM,SAAW,aAAE,MAAM,+BAA+B,wEAAwE,QAAQ,eAAE,MAAM,YAAa,QAAQ,MAAO,YAAC,GAAG,eAAe,SAAU,EAAE,qDAAsD,MAAO,UAAC,QAAQ,4DAA4D,UAAC,QAAQ,6BAA6B,SAAC,UAAU,4EAA3gC,IAAO,yCAAyC,CAA29B,QAArtB,KAAY,KAAK,CAAosB,yBAAkH,EAAE,SAAC,WAAW,4GAA4G,SAAC,KAAK,EAAE,GAAG,EAAE,UAAC,OAAO,oCAAqC,SAAC,IAAI,wDAA70C,IAAO,OAAW,CAAK,CAAszC,2CAAuG,WAAW,SAAC,IAAK,0CAA0C,IAAK,SAAC,OAAO,yEAA0E,aAAa,GAAG,EAAE,GAAG,EAAE,SAAC,OAAO,+CAA+C,GAAG,GAAG,EAAE,CAA+C,IAAS,WAAW,GAAG,SAAE,QAAQ,mIAAmI,SAAE,SAAS,yCAAyC,EAAE,EAA2C,OAAY,IAAI,sCAAsC,sDAAsD,SAAS,SAAE,SAAS,kDAAkD,EAAE,SAAE,SAAS,sEAA8E,GAA2C,IAAS,UAAU,GAAG,SAAC,SAAS,OAAO,6BAA6B,UAAU,SAAC,QAAQ,gEAAgE,wCAAwC,UAAU,SAAC,WAAW,oDAAoD,EAAE,EAAE,EAAqD,KAAs8C,EAA17C,IAAI,mLAAmL,KAAK,eAAE,MAAM,SAAS,MAAM,SAAS,cAAC,WAAW,cAAC,UAAU,cAAC,OAAe,YAAE,GAAG,CAAx4L,cAAiB,MAAM,YAAE,KAAK,eAAE,MAAM,2BAA2B,MAAu0L,KAAQ,QAAQ,MAAM,eAAE,MAAM,8CAA8C,MAA8M,0CAAxM,IAAU,MAAM,wLAAwL,CAA6C,SAAS,EAAE,UAAU,WAAW,CAAgL,MAAO,UAAE,QAAQ,mIAAjM,cAAiM,OAAjM,IAAuB,6DAA6D,CAA6G,aAA7G,UAA6G,aAA7G,UAA6G,UAAsL,UAAE,QAAQ,qCAA7S,KAAgC,iBAAiB,CAA4P,UAAiD,SAAC,QAAQ,4CAA4C,SAAC,KAAM,EAAE,KAAK,SAAC,KAAM,wBAAwB,SAAC,WAAW,wDAA3Z,IAAO,oCAAoZ,uDAAiH,SAAC,KAAK,EAAE,EAAE,SAAC,CAAvhC,KAAuhC,CAAI,WAAW,GAAG,KAAK,SAAC,QAAQ,sCAAsC,SAAC,QAAQ,mCAAmC,SAAC,KAAK,EAAE,EAAE,GAAG,EAAE,CAA2J,EAA7G,GAAU,SAAC,IAAK,iBAAiB,SAAC,QAAQ,kBAAkB,4BAA4B,WAAW,SAAC,KAAK,EAAE,EAAE,gMCwBh/P,IAAI8K,EAAmB,EAAE,CAEnBC,EAAiB,CACrBC,KAAM,EACNC,QAAS,OACTC,cAAe,SACfC,WAAY,SACZC,eAAgB,SAChBC,MAAO,OACPC,OAAQ,OACRC,YAAa,GACbC,YAAa,UACbC,gBAAiB,UACjBC,MAAO,QACPC,WAAY,2BACZC,QAAS,MACX,EAYMC,EAAuB,CAC3BZ,QAAS,OACTW,QAAS,OACTP,MAAO,OACPS,OAAQ,iBACRZ,cAAe,SACfE,eAAgB,aAChBW,SAAU,OACV3R,UAAW,EACb,EAcM4R,EAAM,CACVX,MAAO,OACT,EAEMY,EAAmB,CACvBT,YAAa,SACf,EA4WA,EA1WsB,IACpB,IAmSIU,EAnSE,GAAEpW,CAAC,CAAE,CAAGE,CAAAA,EAAAA,EAAAA,EAAAA,CAAcA,CAAC,GAyWhB0P,OAxWP,CAACyG,EAAWC,EAAa,CAAGnX,CAAAA,CAwWP,CAxWOA,EAAAA,QAAAA,CAAQA,EAAC,GACrC,CAACoX,EAAYC,EAAc,CAAGrX,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,GACtCyC,EACU,eAAdhD,EAAMwF,IAAI,CAAoB,UAAWqS,UAAwB,CAC7D,CAACC,EAAOC,EAAS,CAAGxX,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAQ,EAAE,EACtC,CAACyX,EAAOC,EAAS,CAAG1X,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,EAAC,GAC7B,CAAC2X,EAAaC,EAAe,CAAG5X,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAW,EAAE,EAErD6X,EAAWpY,GAAwB,gBAAfA,EAAMwF,IAAI,CAAqB,SAAW,SAC9D6S,EAAc,MAAO1L,IACZ,MAAM1K,EAAAA,CAAUA,CAACqW,MAAM,CAAC,GAAe3L,MAAAA,CAAZyL,EAAS,KAAM,OAAHzL,GACtD,EAEM4L,EAAcC,IAClBZ,EAAcY,GACdd,EAAa,GACf,EAEMe,EAAe,CAACvS,EAA8DwS,KAClF,IAAMC,EAAQ,IAAIT,EAAY,CAC9BS,CAAK,CAACD,EAAM,CAAGxS,EAAE4J,MAAM,CAACpP,KAAK,CAC7ByX,EAAeQ,EACjB,EAEMC,EAAe,IAEnB,OAAQC,GADiBL,EAAKnT,IAAI,CAACyT,KAAK,CAAC,KAAKC,GAAG,IAE/C,IAAK,MACL,IAAK,MACL,IAAK,OACL,IAAK,MACL,IAAK,MACH,MAAO,UAACzB,MAAAA,CAAI0B,IAAKR,EAAKS,OAAO,CAAExT,MAAO6R,GACxC,KAAK,MACH,MACE,UAACA,MAAAA,CACC0B,IAAI,gCACJrU,UACE3E,kBAAMwF,IAAI,CAAqB,aAAe,cAItD,KAAK,OAmBL,QAlBE,MACE,UAAC8R,MAAAA,CACC0B,IAAI,iCACJrU,UACiB,gBAAf3E,EAAMwF,IAAI,CAAqB,aAAe,cAItD,KAAK,MACL,IAAK,OACH,MACE,UAAC8R,MAAAA,CACC0B,IAAI,gCACJrU,UACiB,gBAAf3E,EAAMwF,IAAI,CAAqB,aAAe,cAaxD,CACF,EAEM0T,EAAY,IAAMxB,GAAa,GAE/ByB,EAAgB,KACpBzB,GAAa,EACf,EAEM0B,EAAgB,IAEpB,IAAMC,EACJC,CAFFA,EAAe3B,CAAAA,GAEG2B,EAAa3W,GAAG,CAC5B,CAAE6M,SAAU8J,EAAa3W,GAAG,EAC5B,CAAE6V,KAAMc,CAAa,EACrBC,EAASC,IAAAA,SAAW,CAACpD,EAAMiD,GAE3BI,EAAY,IAAIvB,EAAY,CAClCuB,EAAUC,MAAM,CAACH,EAAQ,GACzBpB,EAAesB,GAEfpB,EAAYjC,CAAI,CAACmD,EAAO,CAAC/J,QAAQ,EACjC4G,EAAKsD,MAAM,CAACH,EAAQ,GACpBvZ,EAAMmR,QAAQ,CAACiF,EAAMpW,EAAM0Y,KAAK,CAAG1Y,EAAM0Y,KAAK,CAAG,GACjD,IAAMiB,EAAW,IAAI7B,EAAM,CAC3B6B,EAASD,MAAM,CAACC,EAASC,OAAO,CAACN,GAAe,GAChDvB,EAAS4B,GACTjC,GAAa,EACf,EAEMmC,EAAc/B,EAAMxV,GAAG,CAAC,CAACkW,EAAWtJ,IAEtC,WAACzK,MAAAA,WACC,UAACC,EAAAA,CAAGA,CAAAA,CAACoV,GAAI,YACP,WAACrV,MAAAA,CAAIE,UAAU,gBACb,UAACD,EAAAA,CAAGA,CAAAA,CACFiB,GAAI,EACJf,GAAI,EACJD,UACE3E,gDAAMwF,IAAI,CACN,gDACA,oDAGLoT,EAAaJ,KAEhB,UAAC9T,EAAAA,CAAGA,CAAAA,CAACiB,GAAI,EAAGf,GAAI,EAAGD,UAAU,6BAC3B,WAACK,EAAAA,CAAIA,CAAAA,WACH,WAACA,EAAAA,CAAIA,CAACC,KAAK,EAAC8U,UAAU,qBACpB,UAAC/U,EAAAA,CAAIA,CAACE,KAAK,EAACP,UAAU,gBAAQvD,EAAE,cAChC,UAAC4D,EAAAA,CAAIA,CAACyL,OAAO,EACXuJ,KAAK,KACLxU,KAAK,OACL0N,QAAQ,IACRxS,MAAO8X,EAAKyB,aAAa,CAAGzB,EAAKyB,aAAa,CAAGzB,EAAKnT,IAAI,MAG9D,WAACL,EAAAA,CAAIA,CAACC,KAAK,EAAC8U,UAAU,wBACpB,UAAC/U,EAAAA,CAAIA,CAACE,KAAK,WACO,gBAAflF,EAAMwF,IAAI,CACPpE,EAAE,uCACFA,EAAE,wBAER,UAAC4D,EAAAA,CAAIA,CAACyL,OAAO,EACXyJ,UAA0B,kBAAT1U,IAAI,CAAqB,SAAM2Q,EAChD6D,KAAK,KACLxU,KAAK,OACLqB,YACiB,gBAAf7G,EAAMwF,IAAI,CACNpE,EAAE,kCACFA,EAAE,sCAERV,MAAOwX,CAAW,CAAChJ,EAAE,CACrBjJ,SAAU,GAAOwS,EAAavS,EAAGgJ,aAKzC,UAACxK,EAAAA,CAAGA,CAAAA,CACFiB,GAAI,EACJf,GAAI,EACJD,UAAU,gCACVW,QAAS,IAAMiT,EAAWC,YAE1B,UAAC5G,EAAAA,CAAMA,CAAAA,CAACC,QAAQ,gBAAQzQ,EAAE,mBAIhC,WAAC+Y,EAAAA,CAAKA,CAAAA,CAACC,KAAM3C,EAAW4C,OAAQnB,YAC9B,UAACiB,EAAAA,CAAKA,CAACG,MAAM,EAACC,WAAW,aACvB,UAACJ,EAAAA,CAAKA,CAAC5J,KAAK,WAAEnP,EAAE,kBAElB,UAAC+Y,EAAAA,CAAKA,CAAC7J,IAAI,WAAElP,EAAE,qCACf,WAAC+Y,EAAAA,CAAKA,CAACK,MAAM,YACX,UAAC5I,EAAAA,CAAMA,CAAAA,CAACC,QAAQ,YAAYvM,QAAS6T,WAClC/X,EAAE,YAEL,UAACwQ,EAAAA,CAAMA,CAAAA,CAACC,QAAQ,UAAUvM,QAAS,IAAM8T,EAAcZ,YACpDpX,EAAE,iBAlED8N,IA0EdtN,CAAAA,EAAAA,EAAAA,SAAAA,CAASA,CAAC,KACRkW,EAAMvL,OAAO,CAAC,GAAUkO,IAAIC,eAAe,CAAClC,EAAKS,OAAO,GACxD7C,EAAO,EAAE,EACR,EAAE,EAELxU,CAAAA,EAAAA,EAAAA,SAAAA,CAASA,CAAC,KACR5B,EAAMoR,cAAc,CAAC8G,EACvB,EAAG,CAACA,EAAY,EAEhBtW,CAAAA,EAAAA,EAAAA,SAAAA,CAASA,CAAC,KACRuW,EAAenY,EAAMkR,OAAO,CAC9B,EAAG,CAAClR,EAAMkR,OAAO,CAAC,EAElBtP,CAAAA,EAAAA,EAAAA,SAAAA,CAASA,CAAC,KACR5B,GAASA,WAAM2a,YAAY,EAAe1C,GAAS,GAC/CjY,GAASA,EAAMiR,KAAK,EAatB8G,EAAS,IAZM/X,EAAMiR,KAAK,CAAC3O,GAAG,CAAC,CAACC,EAAWE,KACzC2T,EAAK5J,IAAI,CAAC,CACRgD,SAAUjN,EAAKI,GAAG,CAClB+V,MAAO1Y,EAAM0Y,KAAK,CAAG1Y,EAAM0Y,KAAK,CAAG,EACnClT,KAAMjD,EAAK8C,IAAI,CAACyT,KAAK,CAAC,IAAI,CAAC,EAAE,GAEV,CACnB,GAAGvW,CAAI,CACP0W,QAAS,GAAwC1W,MAAAA,CAArCsV,8BAAsB,CAAC,gBAAuB,OAATtV,EAAKI,GAAG,CAC3D,IAGkB,CAExB,EAAG,CAAC3C,EAAMiR,KAAK,CAAC,EAEhB,IAAM2J,EAAc,MAAOC,EAAqBnC,KAC9C,GAAImC,EAAa3T,MAAM,CAAGwR,EACxB,GAAI,CACF,CAF6B,GAEvBoC,EAAY,IAAIC,SACtBD,EAAKE,MAAM,CAAC,OAAQH,CAAY,CAACnC,EAAM,EACvC,IAAMuC,EAAM,MAAMhZ,EAAAA,CAAUA,CAACsB,IAAI,CAAC6U,EAAU0C,EAAM,CAChD,eAAgB,qBAClB,GACA1E,EAAK5J,IAAI,CAAC,CACRgD,SAAUyL,EAAItY,GAAG,CACjB6V,KAAMqC,CAAY,CAACnC,EAAM,CACzBA,MAAO1Y,EAAM0Y,KAAK,CAAG1Y,EAAM0Y,KAAK,CAAG,EACnClT,KAAMqV,CAAY,CAACnC,EAAM,CAACrT,IAAI,CAACyT,KAAK,CAAC,IAAI,CAAC,EAC5C,GACA8B,EAAYC,EAAcnC,EAAQ,EACpC,CAAE,MAAOpN,EAAO,CACdsP,EAAYC,EAAcnC,EAAQ,EACpC,MAEA1Y,EAAMmR,QAAQ,CAACiF,EAAMpW,EAAM0Y,KAAK,CAAG1Y,EAAM0Y,KAAK,CAAG,EAErD,EAEMwC,EAASC,CAAAA,EAAAA,EAAAA,WAAAA,CAAWA,CAAC,MAAOC,IAChC,MAAMR,EAAYQ,EAAc,GAChC,IAAMC,EAAWD,EAAa9Y,GAAG,CAAEkW,GACjClL,OAAOgO,MAAM,CAAC9C,EAAM,CAClBS,QAASwB,IAAIc,eAAe,CAAC/C,EAC/B,IAEFR,EACID,EAAS,GAAe,IAAIlN,KAAcwQ,EAAS,EACnDtD,EAAS,IAAIsD,EAAS,CAC5B,EAAG,EAAE,EAkBC,cACJG,CAAY,eACZC,CAAa,cACbC,CAAY,CACZC,cAAY,CACZC,cAAY,CACZC,gBAAc,CACf,CAAGC,CAAAA,EAAAA,EAAAA,EAAAA,CAAWA,CAAC,CACdC,OACE/b,GAASA,EAAMwF,IAAI,CACf,+MACA,UACNwW,SAAUhE,EACViE,QAAS,EACTC,QAASlZ,SACTkY,EACA5F,UAhCF,CAgCa6G,QAhCgB3D,CAAU,EACrC,GAAiB,UAAU,CAAvBJ,GACF,GAAkC,SAAS,CAAvCI,EAAKhT,IAAI,CAAC4W,SAAS,CAAC,EAAG,GAIzB,OADA/Q,EAAAA,EAAKA,CAACC,KAAK,CAAClK,EAAE,6BACP,CAAEib,KAAM,oBAAqB7Y,QAAS,yBAA0B,CACzE,MACK,GAAiB,UAAU,CAAvB4U,GAC2B,OAAM,GAApCI,EAAKhT,IAAI,CAAC4W,SAAS,CAAC,EAAG,GAE3B,OADA/Q,EAAAA,EAAKA,CAACC,KAAK,CAAClK,EAAE,6BACP,CAAEib,KAAM,oBAAqB7Y,QAAS,yBAA0B,EAG3E,OAAO,IACT,CAkBA,GAEMiC,EAAQ6W,CAAAA,EAAAA,EAAAA,OAAAA,CAAOA,CACnB,IAAO,EACL,GAAGjG,CAAS,CACZ,GAAIqF,EAAenE,EAAc,CAAEgF,QAAS,iBAAkB,CAAC,CAC/D,GAAIZ,EACA,CAAEY,QAAS,oBAAqB,EAChC,CAAEA,QAAS,iBAAkB,CAAC,CAClC,GAAIX,EAAe,CAAEW,QAAS,gBAAiB,EAAI,aAAEhF,CAAY,CAAC,CACpE,EACA,CAACmE,EAAcE,EAAa,EAK5BpE,EADExX,GAAwB,eAAe,CAA9BA,EAAMwF,IAAI,CAEnB,UAAC4B,QAAAA,CAAM3B,MAAO,CAAEuR,MAAO,SAAU,WAAI5V,EAAE,uBAIvC,UAACgG,QAAAA,CAAM3B,MAAO,CAAEuR,MAAO,SAAU,WAAI5V,EAAE,oBAI3C,IAAMob,EACJX,EAAe3U,MAAM,CAAG,GAAK2U,CAAc,CAAC,EAAE,CAACrD,IAAI,CAACwB,IAAI,CAAGhX,EAC7D,MACE,iCACE,UAACyB,MAAAA,CACCE,UAAU,yDACVc,MAAO,CAAEkR,MAAO,OAAQC,OAAQ,OAAQ,WAExC,WAACnS,MAAAA,CAAK,GAAG+W,EAAa,OAAE/V,CAAM,EAAE,WAC9B,UAACgX,QAAAA,CAAO,GAAGhB,GAAe,GAC1B,UAACiB,EAAAA,CAAeA,CAAAA,CAACC,KAAMC,EAAAA,GAAgBA,CAAE5C,KAAK,KAAKhD,MAAM,SACzD,UAAC6F,IAAAA,CAAEpX,MAAO,CAAEuR,MAAO,UAAW8F,aAAc,KAAM,WAC/C1b,EAAE,mDAGJ,CAAC4W,GACA,WAAC5Q,QAAAA,CAAM3B,MAAO,CAAEuR,MAAO,SAAU,YAC/B,UAAC+F,IAAAA,UAAE,UAAS,wCAGfvF,GACAxX,EAAMwF,IAAI,CACPgX,GACE,CAFU,EAEV,QAACpV,QAAAA,CAAMzC,UAAU,6BACf,UAAC+X,EAAAA,CAAeA,CAAAA,CACdC,KAAMK,EAAAA,GAAmBA,CACzBhD,KAAK,KACLhD,MAAM,QACL,IACF5V,EAAE,4CAaVwa,CAVGY,EAWF,WAACpV,QAAAA,CAAMzC,UAAU,cAAcc,MAAO,CAAEuR,MAAO,SAAU,YACvD,UAAC0F,EAAAA,CAAeA,CAAAA,CACdC,KAAMK,EAAAA,GAAmBA,CACzBhD,KAAK,KACLhD,MAAM,QACL,IACF5V,EAAE,mCAKV0W,EAAM5Q,MAAM,CAAG,GAAK,UAACzC,MAAAA,CAAIgB,MAAO0R,WAAkB0C,MAGzD", "sources": ["webpack://_N_E/./components/common/RKIDatePicker.tsx", "webpack://_N_E/./components/common/GroupVisibility.tsx", "webpack://_N_E/./pages/vspace/Form.tsx", "webpack://_N_E/./components/common/FormikRadio.tsx", "webpack://_N_E/./components/common/FormValidation.tsx", "webpack://_N_E/./components/common/ValidationFormWrapper.tsx", "webpack://_N_E/./components/common/FormikTextInput.tsx", "webpack://_N_E/./node_modules/react-multi-select-component/dist/esm/index.js", "webpack://_N_E/./components/common/ReactDropZone.tsx"], "sourcesContent": ["//Import Library\r\nimport React from 'react'\r\nimport DatePicker from \"react-datepicker\";\r\n\r\ninterface RKIDatePickerProps {\r\n  [key: string]: any;\r\n}\r\n\r\nconst RKIDatePicker = (props: RKIDatePickerProps) => {\r\n  return (\r\n    <DatePicker {...props}  />\r\n  )\r\n};\r\n\r\nexport default RKIDatePicker;\r\n", "//Import Library\r\nimport React, { useState, useEffect } from \"react\";\r\nimport { Form, Row, Col } from \"react-bootstrap\";\r\nimport {MultiSelect} from \"react-multi-select-component\";\r\nimport Select from \"react-select\";\r\nimport makeAnimated from \"react-select/animated\";\r\nimport CreatableSelect from 'react-select/creatable';\r\n\r\n//Import services/components\r\nimport apiService from \"../../services/apiService\";\r\nimport { useTranslation } from 'next-i18next';\r\n\r\nconst GroupVisibility = (props: any) => {\r\n  const animatedComponents = makeAnimated();\r\n  const [countryList, setcountryList] = useState([]);\r\n  const [inputValue, setInputValue] = useState(\"\");\r\n  const [value, setValue] = useState<any>([]);\r\n  const [organisationTypeList, setOrganisationTypeList] = useState([]);\r\n  const [validEmail, setValidEmail] = useState(false);\r\n  const [alreadyMember, setAlreadyMember] = useState(false);\r\n  const [lastAddedEmail, setLastAddedEmail] = useState(\"\");\r\n  const { t,i18n } = useTranslation('common');\r\n  const titleSearch = i18n.language === 'de'? {title_de: \"asc\"} : {title: \"asc\"};\r\n  const currentLang = i18n.language;\r\n\r\n  useEffect(() => {\r\n    // Create an scoped async function in the hook\r\n    const getProjectInitialData = async (projectParams_value: any) => {\r\n      const country = await apiService.get(\"/country\", projectParams_value);\r\n      if (country && Array.isArray(country.data)) {\r\n        const _country = country.data.map((item: any, _i: number) => {\r\n          return { label: item.title, value: item._id };\r\n        });\r\n        setcountryList(_country);\r\n      }\r\n      const institutionType = await apiService.get(\r\n        \"/institutiontype\",\r\n        projectParams_value\r\n      );\r\n      if (institutionType && Array.isArray(institutionType.data)) {\r\n        const _institutionType = institutionType.data.map((item: any, _i: number) => {\r\n          return { label: item.title, value: item._id };\r\n        });\r\n        setOrganisationTypeList(_institutionType);\r\n      }\r\n    };\r\n\r\n    // Get the data from project API\r\n    const projectParams = {\r\n      query: {},\r\n      sort: titleSearch,\r\n      limit: \"~\",\r\n      languageCode:currentLang\r\n    };\r\n    getProjectInitialData(projectParams);\r\n  }, []);\r\n\r\n\r\n  //Send email to the form\r\n  useEffect(() => {\r\n    value && props.nonMember && props.nonMember(value);\r\n  }, [value])\r\n\r\n  /***user Select handler ***/\r\n  const userHandler = (selected: any) => {\r\n    const allOptionValue = props.allOption?.value || \"*\";\r\n    if (selected && selected.length > 0 && selected[selected.length - 1]?.value === allOptionValue) {\r\n      return props.onChange(\r\n        props.multiUserOptions || [],\r\n        \"userList\"\r\n      );\r\n    } else {\r\n      return props.onChange(selected || [], \"userList\");\r\n    }\r\n  }\r\n\r\n  //Hanlde user Invite Functionality\r\n  const handleChange = (visible: any) => {\r\n    return (\r\n      setValue(visible && Array.isArray(visible) ? visible : [])\r\n    )\r\n  }\r\n\r\n  const handleInputChange = (VisibleHandle: any) => setInputValue(VisibleHandle || \"\");\r\n  const addEmailToList = async (email: string) => {\r\n    if (!email) return;\r\n\r\n    //Validate it is email or not\r\n    const re = /\\S+@\\S+\\.\\S+/;\r\n    if (re.test(email)) {\r\n      const data = {email : email}\r\n      const _users= await apiService.post(\"/vspace/filterNonmember\", data);\r\n\r\n      if(_users.message === '') {\r\n        setValue([...(value || []), { label: email, value: email }]);\r\n        setInputValue('');\r\n      } else {\r\n        setLastAddedEmail(email);\r\n        setAlreadyMember(true);\r\n        setTimeout(() => {\r\n          setAlreadyMember(false);\r\n          setLastAddedEmail(\"\");\r\n        }, 1200);\r\n        setInputValue('');\r\n      }\r\n    } else {\r\n      setValidEmail(true);\r\n      setInputValue(\"\");\r\n      setTimeout(() => {\r\n        setValidEmail(false);\r\n      }, 1200);\r\n    }\r\n  };\r\n\r\n  const handleKeyDown = async (event: React.KeyboardEvent) => {\r\n    if (!inputValue){\r\n      return;\r\n    }\r\n    switch (event.key) {\r\n      case 'Enter':\r\n      case 'Tab':\r\n        await addEmailToList(inputValue);\r\n        event.preventDefault();\r\n        break;\r\n    }\r\n  };\r\n\r\n  const handleBlur = async () => {\r\n    if (inputValue && inputValue.trim()) {\r\n      await addEmailToList(inputValue.trim());\r\n    }\r\n  };\r\n\r\n  const {\r\n    invitesCountry,\r\n    invitesRegion,\r\n    invitesOrganisationType,\r\n    invitesOrganisation,\r\n    invitesExpertise,\r\n    invitesNetWork,\r\n    visibility,\r\n    userList  } = props;\r\n  const VisibilityClass = \"visibility-space\";\r\n  return (\r\n    <div>\r\n      <Col className=\"header-block\" lg={12}>\r\n        <h6>\r\n          <span>{t(\"vspace.Admin\")}</span>\r\n        </h6>\r\n      </Col>\r\n      <Row>\r\n        <Col>\r\n          <Form.Group>\r\n            <Form.Label>{t(\"vspace.GroupVisibility\")}</Form.Label>\r\n            <Form.Check\r\n            className=\"check\"\r\n              checked={visibility}\r\n              name=\"visibility\"\r\n              onClick={props.handleVisibility}\r\n              type=\"radio\"\r\n              label= {t(\"vspace.Public\")}\r\n            />\r\n          </Form.Group>\r\n        </Col>\r\n        <Col style={{ marginTop: \"1.6rem\" }}>\r\n          <Form.Check\r\n            type=\"radio\"\r\n            checked={!visibility}\r\n            name=\"visibility\"\r\n            value=\"off\"\r\n            onClick={props.handleVisibility}\r\n            label={t(\"vspace.Private\")}\r\n          />\r\n        </Col>\r\n      </Row>\r\n      {!visibility && (\r\n        <div>\r\n          <Col className=\"header-block\" lg={12}>\r\n            <h6>\r\n              <span>{t(\"vspace.Invites\")}</span>\r\n            </h6>\r\n          </Col>\r\n          <Row>\r\n            <Col md={4} sm={4} lg={4}>\r\n              <Form.Group>\r\n                <Form.Label>{t(\"CountryOrTerritory\")}</Form.Label>\r\n                <MultiSelect\r\n                  overrideStrings={{\r\n                    selectSomeItems: t(\"SelectCountry\"),\r\n                  }}\r\n                  options={countryList || []}\r\n                  onChange={(e: any) => props.onChange(e, \"invitesCountry\")}\r\n                  value={invitesCountry}\r\n                  className={VisibilityClass}\r\n                  labelledBy={t(\"SelectCountry\")}\r\n                />\r\n              </Form.Group>\r\n            </Col>\r\n            <Col md={4} sm={4} lg={4}>\r\n              <Form.Group>\r\n                <Form.Label>{t(\"CountryRegions\")}</Form.Label>\r\n                <MultiSelect\r\n                  overrideStrings={{\r\n                    selectSomeItems: t(\"SelectRegions\"),\r\n                  }}\r\n                  options={props.multiRegionOptions || []}\r\n                  value={invitesRegion}\r\n                  onChange={(e: any) => props.onChange(e, \"invitesRegion\")}\r\n                  className={VisibilityClass}\r\n                  labelledBy={t(\"SelectRegions\")}\r\n                />\r\n              </Form.Group>\r\n            </Col>\r\n            <Col md={4} sm={4} lg={4}>\r\n              <Form.Group>\r\n                <Form.Label>{t(\"OrganisationType\")}</Form.Label>\r\n                <MultiSelect\r\n                  overrideStrings={{\r\n                    selectSomeItems: t(\"SelectOrganisationType\"),\r\n                  }}\r\n                  options={organisationTypeList || []}\r\n                  onChange={(e: any) => props.onChange(e, \"invitesOrganisationType\")}\r\n                  value={invitesOrganisationType}\r\n                  className={VisibilityClass}\r\n                  labelledBy={t(\"vspace.SelectOrganisationType\")}\r\n                />\r\n              </Form.Group>\r\n            </Col>\r\n          </Row>\r\n          <Row>\r\n            <Col md={4} sm={4} lg={4}>\r\n              <Form.Group>\r\n                <Form.Label>{t(\"Organisation\")}</Form.Label>\r\n                <MultiSelect\r\n                  overrideStrings={{\r\n                    selectSomeItems: t(\"SelectOrganisation\"),\r\n                  }}\r\n                  options={props.multiOrganisationOptions || []}\r\n                  onChange={(e: any) => props.onChange(e, \"invitesOrganisation\")}\r\n                  value={invitesOrganisation}\r\n                  className={VisibilityClass}\r\n                  labelledBy={t(\"SelectOrganisation\")}\r\n                />\r\n              </Form.Group>\r\n            </Col>\r\n\r\n            <Col md={4} sm={4} lg={4}>\r\n              <Form.Group style={{ maxWidth: \"450px\" }}>\r\n                <Form.Label>{t(\"Expertise\")}</Form.Label>\r\n                <MultiSelect\r\n                  overrideStrings={{\r\n                    selectSomeItems: t(\"SelectExpertise\"),\r\n                  }}\r\n                  options={props.multiExpertsOptions || []}\r\n                  onChange={(e: any) => props.onChange(e, \"invitesExpertise\")}\r\n                  value={invitesExpertise}\r\n                  className={\"visibility-space\"}\r\n                  labelledBy={\"Select Organisation Type\"}\r\n                />\r\n              </Form.Group>\r\n            </Col>\r\n            <Col md={4} sm={4} lg={4}>\r\n              <Form.Group>\r\n                <Form.Label>{t(\"Network\")}</Form.Label>\r\n                <MultiSelect\r\n                  overrideStrings={{\r\n                    selectSomeItems: t(\"SelectNetwork\"),\r\n                  }}\r\n                  options={props.multiNetworkOptions || []}\r\n                  onChange={(e: any) => props.onChange(e, \"invitesNetWork\")}\r\n                  value={invitesNetWork}\r\n                  className={\"visibility-space\"}\r\n                  labelledBy={\"Select Organisation Type\"}\r\n                />\r\n              </Form.Group>\r\n            </Col>\r\n          </Row>\r\n          <Col className=\"header-block\" lg={12}>\r\n            <h6>\r\n              <span>{t(\"vspace.PlatformMemberInvites\")}</span>\r\n            </h6>\r\n          </Col>\r\n          <Row>\r\n            <Col md={12} lg={12} sm={12}>\r\n              <Select\r\n                closeMenuOnSelect={false}\r\n                components={animatedComponents}\r\n                isMulti\r\n                value={userList || []}\r\n                placeholder= {t(\"SelectUsers\")}\r\n                onChange={userHandler}\r\n                options={[\r\n                  props.allOption || { label: \"All users\", value: \"*\" },\r\n                  ...(props.multiUserOptions || [])\r\n                ]}\r\n              />\r\n              {/*\r\n              /***** In future We add async Paginate\r\n              <AsyncPaginate\r\n                  value={userList}\r\n                  placeholder=\"Select User\"\r\n                  loadOptions={props.loadusers}\r\n                  onChange={userHandler}\r\n                  isMulti\r\n                  additional={{\r\n                    page: 1,\r\n                  }}\r\n                />\r\n                */}\r\n            </Col>\r\n          </Row>\r\n          <Col className=\"header-block\" lg={12}>\r\n            <h6 className=\"mb-1\">\r\n              <span>{t(\"vspace.NonPlatform\")}</span>\r\n            </h6>\r\n          </Col>\r\n          <Row>\r\n            <Col md={12} lg={12} sm={12}>\r\n              <small>{t(\"vspace.PressTab\")}</small>\r\n              <CreatableSelect\r\n                components={animatedComponents}\r\n                inputValue={inputValue || \"\"}\r\n                isClearable\r\n                isMulti\r\n                menuIsOpen={false}\r\n                onChange={handleChange}\r\n                onInputChange={handleInputChange}\r\n                onKeyDown={handleKeyDown}\r\n                onBlur={handleBlur}\r\n                placeholder= {t(\"vspace.Typeemail\")}\r\n                value={value || []}\r\n              />\r\n              {validEmail && <small className=\"text-danger\">{t('PleaseenterValidEmailid')}</small>}\r\n              {alreadyMember && <small className=\"text-danger\"> {lastAddedEmail}  {t('isalreadyexist')}</small>}\r\n            </Col>\r\n          </Row>\r\n        </div>\r\n      )\r\n      }\r\n    </div >\r\n  );\r\n};\r\n\r\nGroupVisibility.defaultProps = {\r\n  allOption: {\r\n    label: \"All users\",\r\n    value: \"*\",\r\n  },\r\n};\r\n\r\nexport default GroupVisibility;\r\n", "//Import Library\r\nimport React, { useState, useRef, useEffect } from \"react\";\r\nimport { <PERSON><PERSON>, Card, Form, Container, Row, Col } from \"react-bootstrap\";\r\nimport Router, { useRouter } from \"next/router\";\r\nimport Link from \"next/link\";\r\nimport moment from \"moment\";\r\nimport toast from 'react-hot-toast';\r\nimport { ValidationForm } from \"../../components/common/FormValidation\";\r\n\r\n//Import services/components\r\nimport apiService from \"../../services/apiService\";\r\nimport RKIDatePicker from \"../../components/common/RKIDatePicker\";\r\nimport { useTranslation } from 'next-i18next';\r\nimport ReactDropZone from \"../../components/common/ReactDropZone\";\r\nimport GroupVisibility from \"../../components/common/GroupVisibility\";\r\nimport { EditorComponent } from \"../../shared/quill-editor/quill-editor.component\";\r\n\r\nconst initialState: any = {\r\n    title: \"\",\r\n    description: \"\",\r\n    startDate: null,\r\n    endDate: null,\r\n    searchData: \"\",\r\n    visibility: true,\r\n    images: [],\r\n    checked: false,\r\n    file_category: \"\",\r\n    nonMembers: [],\r\n    images_src: [],\r\n    members: [],\r\n    doc_src: [],\r\n    document: [],\r\n};\r\n\r\nconst VSpaceForm = (props: any) => {\r\n    const buttonRef = useRef<any>(null);\r\n\r\n    const router = useRouter();\r\n    const { t } = useTranslation('common');\r\n    const [dropZoneCollection, setDropZoneCollection] = useState<any[]>([]);\r\n    const [srcCollection, setSrcCollection] = useState<any[]>([]);\r\n    const [docSrcCollection, setDocSrcCollection] = useState<any[]>([]);\r\n    const [docCollection, setDocCollection] = useState<any[]>([]);\r\n    const [formState, setFormState] = useState<any>(initialState);\r\n    const [usersList, setUsersList] = useState<any[]>([]);\r\n    const [groupRegion, setGroupRegion] = useState<any[]>([]);\r\n    const [groupOrganisation, setOrganisation] = useState<any[]>([]);\r\n    const [groupNetwork, setNetworkList] = useState<any[]>([]);\r\n    const [expertiseList, setExpertiseList] = useState<any[]>([]);\r\n    const [, setValidated] = useState<boolean>(false);\r\n    const [vSpace, setVspace] = useState<any>(null);\r\n    const [routeSource, setRouteSource] = useState<any>(null);\r\n    const [groupVisibility, setGroupVisibility] = useState<any>({\r\n        invitesCountry: [],\r\n        invitesRegion: [],\r\n        invitesOrganisationType: [],\r\n        invitesOrganisation: [],\r\n        invitesExpertise: [],\r\n        invitesNetWork: [],\r\n        userList: [],\r\n    });\r\n\r\n    const formRef = useRef<any>(null);\r\n\r\n    const handleDescription = (value: any) => {\r\n        setFormState((prevState: any) => ({\r\n            ...prevState,\r\n            description: value,\r\n        }));\r\n    };\r\n\r\n    const onChangeDate = (date: any, key: any) => {\r\n        setFormState((prevState: any) => ({\r\n            ...prevState,\r\n            [key]: date,\r\n        }));\r\n    };\r\n\r\n    const handleEndDateCheckBox = () => {\r\n        setFormState((prevState: any) => ({\r\n            ...prevState,\r\n            checked: !prevState.checked,\r\n        }));\r\n    };\r\n\r\n    const resetHandler = () => {\r\n        setFormState(initialState);\r\n        setDropZoneCollection([]);\r\n        setSrcCollection([]);\r\n        setDocCollection([]);\r\n        setDocSrcCollection([]);\r\n        setGroupVisibility({\r\n            invitesCountry: [],\r\n            invitesRegion: [],\r\n            invitesOrganisationType: [],\r\n            invitesOrganisation: [],\r\n            invitesExpertise: [],\r\n            invitesNetWork: [],\r\n            userList: [],\r\n        });\r\n        // Reset validation state (Formik handles this automatically)\r\n        setValidated(false);\r\n        window.scrollTo(0, 0);\r\n    };\r\n\r\n    const onSubmitForm = async (event: any) => {\r\n        if (buttonRef.current) {\r\n            buttonRef.current.setAttribute(\"disabled\", \"disabled\");\r\n        }\r\n\r\n        event.preventDefault();\r\n\r\n        // Basic validation for required fields\r\n        if (!formState.title || formState.title.length < 5) {\r\n            toast.error(t(\"minimum5CharsReq\"));\r\n            if (buttonRef.current) {\r\n                buttonRef.current.removeAttribute(\"disabled\");\r\n            }\r\n            return;\r\n        }\r\n\r\n        setValidated(true);\r\n        let response;\r\n        let toastMsg;\r\n        //send user id to members array\r\n        const _users = groupVisibility.userList.length > 0 ? groupVisibility.userList.map((item: any) => item.value) : [];\r\n        const data: any = {\r\n            title: formState.title,\r\n            description: formState.description,\r\n            start_date: formState.startDate,\r\n            end_date: formState.endDate,\r\n            visibility: formState.visibility === true,\r\n            images: formState.images,\r\n            images_src: formState.images_src,\r\n            members: _users,\r\n            nonMembers:\r\n                formState.nonMembers && formState.nonMembers.length > 0\r\n                    ? formState.nonMembers.map((item: any) => item.value)\r\n                    : \"\",\r\n            document: formState.document,\r\n            doc_src: formState.doc_src,\r\n        };\r\n\r\n        if (routeSource === \"Operation\") {\r\n            data[\"operation\"] = vSpace;\r\n        } else {\r\n            data[\"project\"] = vSpace;\r\n        }\r\n\r\n        try {\r\n            if (props.routes && props.routes[0] === \"edit\" && props.routes[1]) {\r\n                toastMsg = \"vspace.virtualspaceupdatedsuccessfully\";\r\n                response = await apiService.patch(`/vspace/${props.routes[1]}`, data);\r\n            } else {\r\n                toastMsg = \"vspace.virtualspaceaddedsuccessfully\";\r\n                response = await apiService.post(\"/vspace\", data);\r\n            }\r\n            if (response && response._id) {\r\n                toast.success(t(toastMsg));\r\n                Router.push(\"/vspace/[...routes]\", `/vspace/show/${response._id}`);\r\n            } else {\r\n                toast.error(t(\"An error occurred while saving the virtual space\"));\r\n                if (buttonRef.current) {\r\n                    buttonRef.current.removeAttribute(\"disabled\");\r\n                }\r\n            }\r\n        } catch (error: any) {\r\n            console.error(\"Error saving virtual space:\", error);\r\n            toast.error(t(\"An error occurred while saving the virtual space\"));\r\n            if (buttonRef.current) {\r\n                buttonRef.current.removeAttribute(\"disabled\");\r\n            }\r\n        }\r\n    };\r\n\r\n    const getrespdata = (respData: any) => {\r\n        respData && respData.images ? setDropZoneCollection(respData.images) : setDropZoneCollection([]);\r\n        respData && respData.images_src ? setSrcCollection(respData.images_src) : setSrcCollection([]);\r\n        respData && respData.document ? setDocCollection(respData.document) : setDocCollection([]);\r\n        respData && respData.doc_src ? setDocSrcCollection(respData.doc_src) : setDocSrcCollection([]);\r\n    };\r\n    const fetchFormData = async () => {\r\n        const logUser = await apiService.post(\"/users/getLoggedUser\", {});\r\n        const respData = await apiService.get(`/vspace/${props.routes[1]}`);\r\n        const data: any = {\r\n            title: respData.title,\r\n            description: respData.description,\r\n            startDate: respData.start_date ? moment(respData.start_date).toDate() : null,\r\n            endDate: respData.end_date ? moment(respData.end_date).toDate() : null,\r\n            file_category: respData.file_category ? respData.file_category : null,\r\n            visibility: respData.visibility ? true : false,\r\n            user: respData.user ? respData.user._id : \"\",\r\n            images: respData.images,\r\n            images_src: respData.images_src,\r\n            nonMembers:\r\n                respData.nonMembers[0] !== \"\" ? respData.nonMembers.map((item: any) => ({ label: item, value: item })) : [],\r\n        };\r\n        if (respData.members.length > 0) {\r\n            let selectedUsers: any[] = [];\r\n            respData.members.forEach((member: any) => {\r\n                selectedUsers.push({ label: member.username, value: member._id });\r\n            });\r\n            setGroupVisibility({ ...groupVisibility, ...{ userList: selectedUsers } });\r\n        }\r\n        getrespdata(respData);\r\n\r\n        if (respData.user && respData.user._id !== logUser[\"_id\"]) {\r\n            Router.push(\"/vspace\");\r\n        }\r\n        setFormState(data);\r\n        return respData.end_date ? setFormState((prevState: any) => ({ ...prevState, checked: true })) : null;\r\n    };\r\n\r\n    useEffect(() => {\r\n        if (props.routes && props.routes[0] === \"edit\" && props.routes[1]) {\r\n            fetchFormData();\r\n        }\r\n\r\n        setVspace(router && router.query && router.query.id ? router.query.id : null);\r\n        setRouteSource(router && router.query && router.query.source ? router.query.source : null);\r\n    }, []);\r\n\r\n    /**Fetching All Users**/\r\n    useEffect(() => {\r\n        //Parms\r\n        const userParams = {\r\n            query: {},\r\n            sort: { username: \"asc\" },\r\n            limit: \"~\",\r\n            select: \"-acceptCookiesPolicy -country -created_at -dataConsentPolicy -dial_code -enabled -firstname -image -institution -is_focal_point -password -position -region -restrictedUsePolicy -withdrawConsentPolicy -medicalConsentPolicy -fullDataProtectionConsentPolicy -email -roles -updated_at -emailActivateToken -lastname -mobile_number \",\r\n        };\r\n\r\n        const fetchAllUser = async () => {\r\n            const userList = await apiService.get(`/users`, userParams);\r\n            if (userList?.data?.length)\r\n                userList.data = userList.data.filter((_userList: any) =>\r\n                    _userList.vspace_status === \"Request Pending\" || _userList.status === \"Request Pending\"\r\n                        ? false\r\n                        : true\r\n                );\r\n            if (userList) {\r\n                const _users = userList.data.map((item: any, _i: any) => {\r\n                    return { label: item.username, value: item._id };\r\n                });\r\n                setUsersList(_users);\r\n            }\r\n        };\r\n\r\n        fetchAllUser();\r\n    }, [groupVisibility]);\r\n\r\n    /**End**/\r\n\r\n    /**Set Non Member to state***/\r\n    const nonMemberHandler = (email: any) => {\r\n        setFormState((prevState: any) => ({ ...prevState, nonMembers: email }));\r\n    };\r\n    /****End****/\r\n\r\n    React.useEffect(() => {\r\n        if (groupVisibility) {\r\n            const normalizeGroup: any = {};\r\n            Object.keys(groupVisibility).forEach((item: any, _i: any) => {\r\n                const _data: any[] =\r\n                    (groupVisibility as any)[item].length > 0 &&\r\n                    (groupVisibility as any)[item].map((d: any) => {\r\n                        return d.value;\r\n                    });\r\n                normalizeGroup[item] = _data ? _data : [];\r\n            });\r\n            getUsers(normalizeGroup);\r\n        } else {\r\n            console.log(\"No threshold reached.\");\r\n        }\r\n    }, [groupVisibility]);\r\n\r\n    const getinstitution = (institution: any) => {\r\n        if (institution && Array.isArray(institution.data)) {\r\n            const _institution = institution.data.map((item: any) => {\r\n                return { label: item.title, value: item._id };\r\n            });\r\n            setOrganisation(_institution);\r\n        }\r\n    };\r\n\r\n    const getexpertise = (expertise: any) => {\r\n        if (expertise && Array.isArray(expertise.data)) {\r\n            const _expertise = expertise.data.map((item: any, _i: any) => {\r\n                return { label: item.title, value: item._id };\r\n            });\r\n            setExpertiseList(_expertise);\r\n        }\r\n    };\r\n\r\n    const getinstitutionNetwork = (institutionNetwork: any) => {\r\n        if (institutionNetwork && Array.isArray(institutionNetwork.data)) {\r\n            const _institutionNetwork = institutionNetwork.data.map((item: any, _i: any) => {\r\n                return { label: item.title, value: item._id };\r\n            });\r\n            setNetworkList(_institutionNetwork);\r\n        }\r\n    };\r\n    const getUsers = async (normalizeGroup: any) => {\r\n        const {\r\n            invitesCountry,\r\n            invitesRegion,\r\n            invitesOrganisationType,\r\n            invitesOrganisation,\r\n            invitesExpertise,\r\n            invitesNetWork,\r\n        } = normalizeGroup;\r\n        const groupParams = {\r\n            query: {\r\n                country: invitesCountry,\r\n                country_region: invitesRegion,\r\n                institution_type: invitesOrganisationType,\r\n                institution: invitesOrganisation,\r\n                expertises: invitesExpertise,\r\n                networks: invitesNetWork,\r\n                type: \"public\",\r\n            },\r\n        };\r\n        let _regions: any[] = [];\r\n        let _organisation: any[] = [];\r\n        let _experts: any[] = [];\r\n        let _network: any[] = [];\r\n        const userInvites = await apiService.post(\"vspace/filterUser\", groupParams);\r\n        if (userInvites && Array.isArray(userInvites)) {\r\n            if (userInvites[0].regions && userInvites[0].regions.length > 0) {\r\n                _regions = userInvites[0].regions.map((item: any, _i: any) => {\r\n                    return { label: item.title, value: item._id };\r\n                });\r\n                setGroupRegion(_regions);\r\n            }\r\n            if (userInvites[1].organisation && userInvites[1].organisation.length > 0) {\r\n                _organisation = userInvites[1].organisation.map((org: any, i: any) => {\r\n                    _network = org.networks.map((item: any) => {\r\n                        return { label: item.title, value: item._id };\r\n                    });\r\n                    _experts = org.expertise.map((item: any) => {\r\n                        return { label: item.title, value: item._id };\r\n                    });\r\n                    return { label: org.title, value: org._id };\r\n                });\r\n                setOrganisation(_organisation);\r\n                setNetworkList(_network);\r\n                setExpertiseList(_experts);\r\n            } else if (userInvites[1].organisation.length === 0) {\r\n                const institutionParams = {\r\n                    query: {},\r\n                    sort: { title: \"asc\" },\r\n                    limit: \"~\",\r\n                };\r\n                const institution = await apiService.get(\"/institution\", institutionParams);\r\n                getinstitution(institution);\r\n\r\n                const expertise = await apiService.get(\"/expertise\", institutionParams);\r\n                getexpertise(expertise);\r\n\r\n                const institutionNetwork = await apiService.get(\"/institutionnetwork\", institutionParams);\r\n                getinstitutionNetwork(institutionNetwork);\r\n            }\r\n            if (userInvites[2].usersList && userInvites[2].usersList.length > 0) {\r\n                const _users = userInvites[2].usersList.map((item: any, _i: any) => {\r\n                    return { label: item.username, value: item._id };\r\n                });\r\n                setUsersList(_users);\r\n            }\r\n        }\r\n    };\r\n\r\n    //******To Handle Group Visibility******//\r\n    const handleInviteChange = (e: any, name: any) => {\r\n        setGroupVisibility((prevState: any) => ({\r\n            ...prevState,\r\n            [name]: e == null ? [] : e,\r\n        }));\r\n    };\r\n\r\n    const radiohandler = () => {\r\n        setFormState((prevState: any) => ({\r\n            ...prevState,\r\n            visibility: !prevState.visibility,\r\n        }));\r\n    };\r\n\r\n    const getID = (id: any) => {\r\n        const imageIds: any[] = [];\r\n        const docIds: any[] = [];\r\n        if (id.length > 0) {\r\n            id.map((item: any) => {\r\n                if (\r\n                    item.type &&\r\n                    (item.type.includes(\"pdf\") ||\r\n                        item.type.includes(\"docx\") ||\r\n                        item.type.includes(\"xlsx\") ||\r\n                        item.type.includes(\"xls\"))\r\n                ) {\r\n                    docIds.push(item.serverID);\r\n                } else {\r\n                    imageIds.push(item.serverID);\r\n                }\r\n            });\r\n        }\r\n        setFormState((prevState: any) => ({ ...prevState, images: imageIds }));\r\n        setFormState((prevState: any) => ({ ...prevState, document: docIds }));\r\n    };\r\n\r\n    const getSource = (imgSrcArr: any) => {\r\n        setFormState((prevState: any) => ({ ...prevState, images_src: imgSrcArr }));\r\n    };\r\n\r\n    const getDocSource = (docSrcArr: any) => {\r\n        setFormState((prevState: any) => ({ ...prevState, doc_src: docSrcArr }));\r\n    };\r\n\r\n    const onHandleChange = (e: any) => {\r\n        const { name, value } = e.target;\r\n        setFormState((prevState: any) => ({\r\n            ...prevState,\r\n            [name]: value,\r\n        }));\r\n    };\r\n\r\n    return (\r\n        <Container className=\"formCard\" fluid>\r\n            <Card>\r\n                <ValidationForm\r\n                    onSubmit={onSubmitForm}\r\n                    ref={formRef}\r\n                    onKeyPress={(e) => {\r\n                        e.key === \"Enter\" && e.preventDefault();\r\n                    }}\r\n                >\r\n                    <Card.Body>\r\n                        <Row>\r\n                            <Col>\r\n                                <Card.Title>\r\n                                    {props.routes[0] === \"edit\"\r\n                                        ? t(\"vspace.editVirtualSpace\")\r\n                                        : t(\"vspace.addVirtualSpace\")}\r\n                                </Card.Title>\r\n                            </Col>\r\n                        </Row>\r\n                        <hr />\r\n\r\n                        <Row className=\"mb-3\">\r\n                            <Col>\r\n                                <Form.Group>\r\n                                    <Form.Label className=\"required-field\">{t(\"vspace.title\")}</Form.Label>\r\n                                    <Form.Control\r\n                                        minLength={5}\r\n                                        required\r\n                                        type=\"text\"\r\n                                        name=\"title\"\r\n                                        value={formState.title}\r\n                                        onChange={onHandleChange}\r\n                                    />\r\n                                    <Form.Control.Feedback type=\"invalid\">\r\n                                        {formState.title.length === 0\r\n                                            ? t(\"Pleaseprovideatitle\")\r\n                                            : t(\"minimum5CharsReq\")}\r\n                                    </Form.Control.Feedback>\r\n                                </Form.Group>\r\n                            </Col>\r\n                        </Row>\r\n\r\n                        <Row className=\"mb-3\">\r\n                            <Col>\r\n                                <Form.Group>\r\n                                    <Form.Label>{t(\"vspace.Body\")}</Form.Label>\r\n                                    <EditorComponent initContent={formState.description} onChange={(evt: any) => handleDescription(evt)} />\r\n                                </Form.Group>\r\n                            </Col>\r\n                        </Row>\r\n\r\n                        <Row className=\"mb-3\">\r\n                            <Col lg={12}>\r\n                                <Form.Group>\r\n                                    <Form.Label>{t(\"vspace.Image\")}</Form.Label>\r\n                                    <ReactDropZone\r\n                                        datas={dropZoneCollection}\r\n                                        srcText={srcCollection}\r\n                                        getImgID={(id: any) => getID(id)}\r\n                                        getImageSource={(imgSrcArr: any) => getSource(imgSrcArr)}\r\n                                    />\r\n                                </Form.Group>\r\n                            </Col>\r\n                        </Row>\r\n                        <Row className=\"mb-3\">\r\n                            <Col lg={12}>\r\n                                <Form.Group>\r\n                                    <Form.Label>{t(\"vspace.Documents\")}</Form.Label>\r\n                                    <ReactDropZone\r\n                                        type=\"application\"\r\n                                        datas={docCollection}\r\n                                        srcText={docSrcCollection}\r\n                                        getImgID={(id: any) => getID(id)}\r\n                                        getImageSource={(docSrcArr: any) => getDocSource(docSrcArr)}\r\n                                    />\r\n                                </Form.Group>\r\n                            </Col>\r\n                        </Row>\r\n                        <Row className=\"mb-3\">\r\n                            <Col md lg={3} sm={12}>\r\n                                <Form.Group>\r\n                                    <Form.Label className=\"d-block\">{t(\"vspace.StartDate\")}</Form.Label>\r\n                                    <RKIDatePicker\r\n                                        selected={formState.startDate}\r\n                                        onChange={(date: any) => onChangeDate(date, \"startDate\")}\r\n                                        dateFormat=\"MMMM d, yyyy\"\r\n                                        placeholderText={t(\"vspace.Selectadate\")}\r\n                                    />\r\n                                </Form.Group>\r\n                            </Col>\r\n                            <Col md lg={2} sm={12} className=\"col-md\">\r\n                                <Form.Check\r\n                                    type=\"checkbox\"\r\n                                    checked={formState.checked}\r\n                                    onChange={handleEndDateCheckBox}\r\n                                    label={t(\"vspace.ShowEndDate\")}\r\n                                />\r\n                            </Col>\r\n                            {formState.checked && (\r\n                                <Col md lg={3} sm={12}>\r\n                                    <Form.Group>\r\n                                        <Form.Label className=\"d-block\">{t(\"vspace.EndDate\")}</Form.Label>\r\n                                        <RKIDatePicker\r\n                                            selected={formState.endDate}\r\n                                            minDate={formState.startDate}\r\n                                            onChange={(date: any) => onChangeDate(date, \"endDate\")}\r\n                                            dateFormat=\"MMMM d, yyyy\"\r\n                                            placeholderText={t(\"vspace.Selectadate\")}\r\n                                        />\r\n                                    </Form.Group>\r\n                                </Col>\r\n                            )}\r\n                        </Row>\r\n                        <GroupVisibility\r\n                            {...groupVisibility}\r\n                            {...formState}\r\n                            allOption={{ label: \"All users\", value: \"*\" }}\r\n                            multiUserOptions={usersList}\r\n                            multiRegionOptions={groupRegion}\r\n                            multiOrganisationOptions={groupOrganisation}\r\n                            multiExpertsOptions={expertiseList}\r\n                            multiNetworkOptions={groupNetwork}\r\n                            onChange={handleInviteChange}\r\n                            handleVisibility={radiohandler}\r\n                            onHandleChange={onHandleChange}\r\n                            nonMember={nonMemberHandler}\r\n                        />\r\n                        <Row className=\"my-4\">\r\n                            <Col>\r\n                                <Button className=\"me-2\" type=\"submit\" variant=\"primary\" ref={buttonRef}>\r\n                                    {t(\"submit\")}\r\n                                </Button>\r\n                                <Button className=\"me-2\" onClick={resetHandler} variant=\"info\">\r\n                                    {t(\"reset\")}\r\n                                </Button>\r\n                                <Link href=\"/vspace\" as=\"/vspace\" >\r\n                                    <Button variant=\"secondary\">{t(\"Cancel\")}</Button>\r\n                                </Link>\r\n                            </Col>\r\n                        </Row>\r\n                    </Card.Body>\r\n                </ValidationForm>\r\n            </Card>\r\n        </Container>\r\n    );\r\n};\r\n\r\nexport default VSpaceForm;\r\n", "import React from 'react';\r\nimport { useFormikContext, Field } from 'formik';\r\nimport { Form } from 'react-bootstrap';\r\n\r\ninterface RadioItemProps {\r\n  id: string;\r\n  label: string;\r\n  value: string;\r\n  name?: string;\r\n  disabled?: boolean;\r\n}\r\n\r\ninterface RadioGroupProps {\r\n  name: string;\r\n  valueSelected: string;\r\n  onChange: (e: React.ChangeEvent<HTMLInputElement>) => void;\r\n  errorMessage?: string;\r\n  children: React.ReactNode;\r\n}\r\n\r\n// Type Guard to ensure child.props is an object\r\nfunction isObject(props: any): props is { [key: string]: any } {\r\n  return typeof props === 'object' && props !== null;\r\n}\r\n\r\nconst RadioItem: React.FC<RadioItemProps> = ({ id, label, value, name, disabled }) => {\r\n  const { values, setFieldValue } = useFormikContext<any>();\r\n  const fieldName = name || id;\r\n\r\n  return (\r\n    <Form.Check\r\n      type=\"radio\"\r\n      id={id}\r\n      label={label}\r\n      value={value}\r\n      name={fieldName}\r\n      checked={values[fieldName] === value}\r\n      onChange={(e) => {\r\n        setFieldValue(fieldName, e.target.value);\r\n      }}\r\n      disabled={disabled}\r\n      inline\r\n    />\r\n  );\r\n};\r\n\r\nconst RadioGroup: React.FC<RadioGroupProps> = ({\r\n  name,\r\n  valueSelected,\r\n  onChange,\r\n  errorMessage,\r\n  children,\r\n}) => {\r\n  const { errors, touched } = useFormikContext<any>();\r\n  const hasError = touched[name] && errors[name];\r\n\r\n  // Create a context to pass the name to RadioItems\r\n  const radioContext = React.useMemo(() => ({ name }), [name]);\r\n\r\n  // Clone children to pass the name, ensuring props are spread safely\r\n  const childrenWithProps = React.Children.map(children, (child) => {\r\n    if (React.isValidElement(child)) {\r\n      // Ensure child.props is an object before spreading\r\n      if (isObject(child.props)) {\r\n        return React.cloneElement(child as React.ReactElement<RadioItemProps>, {\r\n          name,\r\n          ...child.props, // Safely spread child.props\r\n        });\r\n      }\r\n    }\r\n    return child;\r\n  });\r\n\r\n  return (\r\n    <div>\r\n      <div className=\"radio-group\">\r\n        {childrenWithProps}\r\n      </div>\r\n      {hasError && (\r\n        <div className=\"invalid-feedback d-block\">\r\n          {errorMessage || (typeof errors[name] === 'string' ? errors[name] : String(errors[name]))}\r\n        </div>\r\n      )}\r\n    </div>\r\n  );\r\n};\r\n\r\nexport const Radio = {\r\n  RadioGroup,\r\n  RadioItem,\r\n};\r\n\r\nexport default Radio;\r\n", "// This file exports all the components needed to replace react-bootstrap4-form-validation\r\n// It serves as a drop-in replacement for the original library\r\n\r\nimport ValidationForm from './ValidationFormWrapper';\r\nimport { TextInput, SelectGroup } from './FormikTextInput';\r\nimport { Radio } from './FormikRadio';\r\n\r\n// Export all components\r\nexport {\r\n  ValidationForm,\r\n  TextInput,\r\n  SelectGroup,\r\n  Radio\r\n};\r\n\r\n// Also export as default for convenience\r\nexport default {\r\n  ValidationForm,\r\n  TextInput,\r\n  SelectGroup,\r\n  Radio\r\n};\r\n", "import React, { forwardRef } from 'react';\r\nimport { Formik, Form, FormikProps, FormikHelpers } from 'formik';\r\nimport * as Yup from 'yup';\r\n\r\n// This is a wrapper component that replaces react-bootstrap4-form-validation with Formik\r\ninterface MockEvent {\r\n  preventDefault: () => void;\r\n  stopPropagation: () => void;\r\n  currentTarget: (EventTarget & Element) | null;\r\n  target: EventTarget | null;\r\n  nativeEvent: Event;\r\n  bubbles: boolean;\r\n  cancelable: boolean;\r\n  defaultPrevented: boolean;\r\n  eventPhase: number;\r\n  isTrusted: boolean;\r\n  timeStamp: number;\r\n  type: string;\r\n  isDefaultPrevented: () => boolean;\r\n  isPropagationStopped: () => boolean;\r\n  persist: () => void;\r\n}\r\n\r\ninterface ValidationFormWrapperProps {\r\n  children: React.ReactNode | ((formikProps: FormikProps<any>) => React.ReactNode);\r\n  onSubmit: (event: React.FormEvent | MockEvent, values?: Record<string, any>, actions?: FormikHelpers<Record<string, any>>) => void;\r\n  onErrorSubmit?: (errors: any) => void;\r\n  initialValues?: Record<string, any>;\r\n  enableReinitialize?: boolean;\r\n  autoComplete?: string;\r\n  className?: string;\r\n  onKeyPress?: (e: any) => void;\r\n}\r\n\r\nconst ValidationFormWrapper = forwardRef<HTMLFormElement, ValidationFormWrapperProps>((props, ref) => {\r\n  const { children, onSubmit, autoComplete, className, onKeyPress, initialValues, ...rest } = props;\r\n\r\n  // Create an empty validation schema by default\r\n  const validationSchema = Yup.object().shape({});\r\n\r\n  return (\r\n    <Formik\r\n      initialValues={initialValues || {}}\r\n      validationSchema={validationSchema}\r\n      onSubmit={(values: Record<string, any>, actions: FormikHelpers<Record<string, any>>) => {\r\n        // Create a mock event object with preventDefault method and currentTarget\r\n        const mockEvent: MockEvent = {\r\n          preventDefault: () => {},\r\n          stopPropagation: () => {},\r\n          currentTarget: null, // Set to null to avoid checkValidity errors\r\n          target: null,\r\n          nativeEvent: new Event('submit'),\r\n          bubbles: false,\r\n          cancelable: true,\r\n          defaultPrevented: false,\r\n          eventPhase: 0,\r\n          isTrusted: false,\r\n          timeStamp: Date.now(),\r\n          type: 'submit',\r\n          isDefaultPrevented: () => false,\r\n          isPropagationStopped: () => false,\r\n          persist: () => {}\r\n        };\r\n\r\n        if (onSubmit) {\r\n          // Pass the mock event object to maintain compatibility with the original code\r\n          onSubmit(mockEvent, values, actions);\r\n        }\r\n      }}\r\n      {...rest}\r\n    >\r\n      {(formikProps: FormikProps<any>) => (\r\n        <Form\r\n          ref={ref}\r\n          onSubmit={formikProps.handleSubmit}\r\n          autoComplete={autoComplete}\r\n          className={className}\r\n          onKeyPress={onKeyPress}\r\n        >\r\n          {typeof children === 'function' ? children(formikProps) : children}\r\n        </Form>\r\n      )}\r\n    </Formik>\r\n  );\r\n});\r\n\r\nValidationFormWrapper.displayName = 'ValidationFormWrapper';\r\n\r\nexport default ValidationFormWrapper;\r\n", "import React from 'react';\r\nimport { Form } from 'react-bootstrap';\r\nimport { Field, ErrorMessage, useField } from 'formik';\r\n\r\n// This component mimics the TextInput component from react-bootstrap4-form-validation\r\n// but uses Formik under the hood\r\nexport const TextInput = ({\r\n  name,\r\n  id,\r\n  required,\r\n  validator,\r\n  errorMessage,\r\n  onChange,\r\n  value,\r\n  as,\r\n  multiline,\r\n  rows,\r\n  pattern,\r\n  ...props\r\n}: any) => {\r\n  // Create a custom validation function that mimics the original validator\r\n  const validate = (val: any) => {\r\n    // Safely convert value to string and trim\r\n    const stringVal = typeof val === 'string' ? val : String(val || '');\r\n    if (required && (!val || stringVal.trim() === '')) {\r\n      return errorMessage?.validator || 'This field is required';\r\n    }\r\n\r\n    if (validator && !validator(val)) {\r\n      return errorMessage?.validator || 'Invalid value';\r\n    }\r\n\r\n    if (pattern && val) {\r\n      const regex = new RegExp(pattern);\r\n      if (!regex.test(val)) {\r\n        return errorMessage?.pattern || 'Invalid format';\r\n      }\r\n    }\r\n\r\n    return undefined;\r\n  };\r\n\r\n  return (\r\n    <Field name={name} validate={validate}>\r\n      {({ field, meta }: any) => (\r\n        <>\r\n          <Form.Control\r\n            {...field}\r\n            {...props}\r\n            id={id}\r\n            as={as || 'input'}\r\n            rows={rows}\r\n            isInvalid={meta.touched && !!meta.error}\r\n            onChange={(e: React.ChangeEvent<HTMLInputElement>) => {\r\n              field.onChange(e);\r\n              if (onChange) onChange(e);\r\n            }}\r\n            value={value !== undefined ? value : field.value}\r\n          />\r\n          {meta.touched && meta.error ? (\r\n            <Form.Control.Feedback type=\"invalid\">\r\n              {meta.error}\r\n            </Form.Control.Feedback>\r\n          ) : null}\r\n        </>\r\n      )}\r\n    </Field>\r\n  );\r\n};\r\n\r\n// This component mimics the SelectGroup component from react-bootstrap4-form-validation\r\n// but uses Formik under the hood\r\nexport const SelectGroup = ({\r\n  name,\r\n  id,\r\n  required,\r\n  errorMessage,\r\n  onChange,\r\n  value,\r\n  children,\r\n  ...props\r\n}: any) => {\r\n  // Create a custom validation function that mimics the original validator\r\n  const validate = (val: any) => {\r\n    if (required && (!val || val === '')) {\r\n      return errorMessage?.validator || 'This field is required';\r\n    }\r\n\r\n    return undefined;\r\n  };\r\n\r\n  return (\r\n    <Field name={name} validate={validate}>\r\n      {({ field, meta }: any) => (\r\n        <>\r\n          <Form.Control\r\n            as=\"select\"\r\n            {...field}\r\n            {...props}\r\n            id={id}\r\n            isInvalid={meta.touched && !!meta.error}\r\n            onChange={(e: React.ChangeEvent<HTMLSelectElement>) => {\r\n              field.onChange(e);\r\n              if (onChange) onChange(e);\r\n            }}\r\n            value={value !== undefined ? value : field.value}\r\n          >\r\n            {children}\r\n          </Form.Control>\r\n          {meta.touched && meta.error ? (\r\n            <Form.Control.Feedback type=\"invalid\">\r\n              {meta.error}\r\n            </Form.Control.Feedback>\r\n          ) : null}\r\n        </>\r\n      )}\r\n    </Field>\r\n  );\r\n};\r\n\r\n// Export both components as named exports and as a default object\r\nexport default {\r\n  TextInput,\r\n  SelectGroup\r\n};\r\n", "function V(e,{insertAt:n}={}){if(!e||typeof document>\"u\")return;let t=document.head||document.getElementsByTagName(\"head\")[0],r=document.createElement(\"style\");r.type=\"text/css\",n===\"top\"&&t.firstChild?t.insertBefore(r,t.firstChild):t.appendChild(r),r.styleSheet?r.styleSheet.cssText=e:r.appendChild(document.createTextNode(e))}V(`.rmsc{--rmsc-main: #4285f4;--rmsc-hover: #f1f3f5;--rmsc-selected: #e2e6ea;--rmsc-border: #ccc;--rmsc-gray: #aaa;--rmsc-bg: #fff;--rmsc-p: 10px;--rmsc-radius: 4px;--rmsc-h: 38px}.rmsc *{box-sizing:border-box;transition:all .2s ease}.rmsc .gray{color:var(--rmsc-gray)}.rmsc .dropdown-content{position:absolute;z-index:1;top:100%;width:100%;padding-top:8px}.rmsc .dropdown-content .panel-content{overflow:hidden;border-radius:var(--rmsc-radius);background:var(--rmsc-bg);box-shadow:0 0 0 1px #0000001a,0 4px 11px #0000001a}.rmsc .dropdown-container{position:relative;outline:0;background-color:var(--rmsc-bg);border:1px solid var(--rmsc-border);border-radius:var(--rmsc-radius)}.rmsc .dropdown-container[aria-disabled=true]:focus-within{box-shadow:var(--rmsc-gray) 0 0 0 1px;border-color:var(--rmsc-gray)}.rmsc .dropdown-container:focus-within{box-shadow:var(--rmsc-main) 0 0 0 1px;border-color:var(--rmsc-main)}.rmsc .dropdown-heading{position:relative;padding:0 var(--rmsc-p);display:flex;align-items:center;width:100%;height:var(--rmsc-h);cursor:default;outline:0}.rmsc .dropdown-heading .dropdown-heading-value{overflow:hidden;text-overflow:ellipsis;white-space:nowrap;flex:1}.rmsc .clear-selected-button{cursor:pointer;background:none;border:0;padding:0;display:flex}.rmsc .options{max-height:260px;overflow-y:auto;margin:0;padding-left:0}.rmsc .options li{list-style:none;margin:0}.rmsc .select-item{box-sizing:border-box;cursor:pointer;display:block;padding:var(--rmsc-p);outline-offset:-1px;outline-color:var(--rmsc-primary)}.rmsc .select-item:hover{background:var(--rmsc-hover)}.rmsc .select-item.selected{background:var(--rmsc-selected)}.rmsc .no-options{padding:var(--rmsc-p);text-align:center;color:var(--rmsc-gray)}.rmsc .search{width:100%;position:relative;border-bottom:1px solid var(--rmsc-border)}.rmsc .search input{background:none;height:var(--rmsc-h);padding:0 var(--rmsc-p);width:100%;outline:0;border:0;font-size:1em}.rmsc .search input:focus{background:var(--rmsc-hover)}.rmsc .search-clear-button{cursor:pointer;position:absolute;top:0;right:0;bottom:0;background:none;border:0;padding:0 calc(var(--rmsc-p) / 2)}.rmsc .search-clear-button [hidden]{display:none}.rmsc .item-renderer{display:flex;align-items:baseline}.rmsc .item-renderer input{margin:0 5px 0 0}.rmsc .item-renderer.disabled{opacity:.5}.rmsc .spinner{animation:rotate 2s linear infinite}.rmsc .spinner .path{stroke:var(--rmsc-border);stroke-width:4px;stroke-linecap:round;animation:dash 1.5s ease-in-out infinite}@keyframes rotate{to{transform:rotate(360deg)}}@keyframes dash{0%{stroke-dasharray:1,150;stroke-dashoffset:0}50%{stroke-dasharray:90,150;stroke-dashoffset:-35}to{stroke-dasharray:90,150;stroke-dashoffset:-124}}\n`);import oe,{useEffect as Pe,useState as Ne}from\"react\";import{jsx as Te}from\"react/jsx-runtime\";var Me={allItemsAreSelected:\"All items are selected.\",clearSearch:\"Clear Search\",clearSelected:\"Clear Selected\",noOptions:\"No options\",search:\"Search\",selectAll:\"Select All\",selectAllFiltered:\"Select All (Filtered)\",selectSomeItems:\"Select...\",create:\"Create\"},De={value:[],hasSelectAll:!0,className:\"multi-select\",debounceDuration:200,options:[]},re=oe.createContext({}),ne=({props:e,children:n})=>{let[t,r]=Ne(e.options),a=c=>{var u;return((u=e.overrideStrings)==null?void 0:u[c])||Me[c]};return Pe(()=>{r(e.options)},[e.options]),Te(re.Provider,{value:{t:a,...De,...e,options:t,setOptions:r},children:n})},w=()=>oe.useContext(re);import{useEffect as ye,useRef as Qe,useState as J}from\"react\";import{useEffect as Fe,useRef as Le}from\"react\";function se(e,n){let t=Le(!1);Fe(()=>{t.current?e():t.current=!0},n)}import{useCallback as Ke,useEffect as ae,useMemo as We,useRef as _e}from\"react\";var He={when:!0,eventTypes:[\"keydown\"]};function R(e,n,t){let r=We(()=>Array.isArray(e)?e:[e],[e]),a=Object.assign({},He,t),{when:c,eventTypes:u}=a,b=_e(n),{target:s}=a;ae(()=>{b.current=n});let p=Ke(i=>{r.some(l=>i.key===l||i.code===l)&&b.current(i)},[r]);ae(()=>{if(c&&typeof window<\"u\"){let i=s?s.current:window;return u.forEach(l=>{i&&i.addEventListener(l,p)}),()=>{u.forEach(l=>{i&&i.removeEventListener(l,p)})}}},[c,u,r,s,n])}var f={ARROW_DOWN:\"ArrowDown\",ARROW_UP:\"ArrowUp\",ENTER:\"Enter\",ESCAPE:\"Escape\",SPACE:\"Space\"};import{useCallback as Ge,useEffect as fe,useMemo as he,useRef as Y,useState as F}from\"react\";var le=(e,n)=>{let t;return function(...r){clearTimeout(t),t=setTimeout(()=>{e.apply(null,r)},n)}};function ie(e,n){return n?e.filter(({label:t,value:r})=>t!=null&&r!=null&&t.toLowerCase().includes(n.toLowerCase())):e}import{jsx as ce,jsxs as Be}from\"react/jsx-runtime\";var T=()=>Be(\"svg\",{width:\"24\",height:\"24\",fill:\"none\",stroke:\"currentColor\",strokeWidth:\"2\",className:\"dropdown-search-clear-icon gray\",children:[ce(\"line\",{x1:\"18\",y1:\"6\",x2:\"6\",y2:\"18\"}),ce(\"line\",{x1:\"6\",y1:\"6\",x2:\"18\",y2:\"18\"})]});import{useRef as $e}from\"react\";import{jsx as de,jsxs as Ve}from\"react/jsx-runtime\";var Ue=({checked:e,option:n,onClick:t,disabled:r})=>Ve(\"div\",{className:`item-renderer ${r?\"disabled\":\"\"}`,children:[de(\"input\",{type:\"checkbox\",onChange:t,checked:e,tabIndex:-1,disabled:r}),de(\"span\",{children:n.label})]}),pe=Ue;import{jsx as me}from\"react/jsx-runtime\";var Ye=({itemRenderer:e=pe,option:n,checked:t,tabIndex:r,disabled:a,onSelectionChanged:c,onClick:u})=>{let b=$e(),s=l=>{p(),l.preventDefault()},p=()=>{a||c(!t)},i=l=>{p(),u(l)};return R([f.ENTER,f.SPACE],s,{target:b}),me(\"label\",{className:`select-item ${t?\"selected\":\"\"}`,role:\"option\",\"aria-selected\":t,tabIndex:r,ref:b,children:me(e,{option:n,checked:t,onClick:i,disabled:a})})},N=Ye;import{Fragment as qe,jsx as $}from\"react/jsx-runtime\";var ze=({options:e,onClick:n,skipIndex:t})=>{let{disabled:r,value:a,onChange:c,ItemRenderer:u}=w(),b=(s,p)=>{r||c(p?[...a,s]:a.filter(i=>i.value!==s.value))};return $(qe,{children:e.map((s,p)=>{let i=p+t;return $(\"li\",{children:$(N,{tabIndex:i,option:s,onSelectionChanged:l=>b(s,l),checked:!!a.find(l=>l.value===s.value),onClick:l=>n(l,i),itemRenderer:u,disabled:s.disabled||r})},(s==null?void 0:s.key)||p)})})},ue=ze;import{jsx as k,jsxs as z}from\"react/jsx-runtime\";var Je=()=>{let{t:e,onChange:n,options:t,setOptions:r,value:a,filterOptions:c,ItemRenderer:u,disabled:b,disableSearch:s,hasSelectAll:p,ClearIcon:i,debounceDuration:l,isCreatable:L,onCreateOption:y}=w(),O=Y(),g=Y(),[m,M]=F(\"\"),[v,K]=F(t),[x,D]=F(\"\"),[E,I]=F(0),W=Ge(le(o=>D(o),l),[]),A=he(()=>{let o=0;return s||(o+=1),p&&(o+=1),o},[s,p]),_={label:e(m?\"selectAllFiltered\":\"selectAll\"),value:\"\"},H=o=>{let d=v.filter(C=>!C.disabled).map(C=>C.value);if(o){let Ae=[...a.map(U=>U.value),...d];return(c?v:t).filter(U=>Ae.includes(U.value))}return a.filter(C=>!d.includes(C.value))},B=o=>{let d=H(o);n(d)},h=o=>{W(o.target.value),M(o.target.value),I(0)},P=()=>{var o;D(\"\"),M(\"\"),(o=g==null?void 0:g.current)==null||o.focus()},Z=o=>I(o),we=o=>{switch(o.code){case f.ARROW_UP:ee(-1);break;case f.ARROW_DOWN:ee(1);break;default:return}o.stopPropagation(),o.preventDefault()};R([f.ARROW_DOWN,f.ARROW_UP],we,{target:O});let Oe=()=>{I(0)},j=async()=>{let o={label:m,value:m,__isNew__:!0};y&&(o=await y(m)),r([o,...t]),P(),n([...a,o])},Re=async()=>c?await c(t,x):ie(t,x),ee=o=>{let d=E+o;d=Math.max(0,d),d=Math.min(d,t.length+Math.max(A-1,0)),I(d)};fe(()=>{var o,d;(d=(o=O==null?void 0:O.current)==null?void 0:o.querySelector(`[tabIndex='${E}']`))==null||d.focus()},[E]);let[ke,Ee]=he(()=>{let o=v.filter(d=>!d.disabled);return[o.every(d=>a.findIndex(C=>C.value===d.value)!==-1),o.length!==0]},[v,a]);fe(()=>{Re().then(K)},[x,t]);let te=Y();R([f.ENTER],j,{target:te});let Ie=L&&m&&!v.some(o=>(o==null?void 0:o.value)===m);return z(\"div\",{className:\"select-panel\",role:\"listbox\",ref:O,children:[!s&&z(\"div\",{className:\"search\",children:[k(\"input\",{placeholder:e(\"search\"),type:\"text\",\"aria-describedby\":e(\"search\"),onChange:h,onFocus:Oe,value:m,ref:g,tabIndex:0}),k(\"button\",{type:\"button\",className:\"search-clear-button\",hidden:!m,onClick:P,\"aria-label\":e(\"clearSearch\"),children:i||k(T,{})})]}),z(\"ul\",{className:\"options\",children:[p&&Ee&&k(N,{tabIndex:A===1?0:1,checked:ke,option:_,onSelectionChanged:B,onClick:()=>Z(1),itemRenderer:u,disabled:b}),v.length?k(ue,{skipIndex:A,options:v,onClick:(o,d)=>Z(d)}):Ie?k(\"li\",{onClick:j,className:\"select-item creatable\",tabIndex:1,ref:te,children:`${e(\"create\")} \"${m}\"`}):k(\"li\",{className:\"no-options\",children:e(\"noOptions\")})]})]})},q=Je;import{jsx as be}from\"react/jsx-runtime\";var ge=({expanded:e})=>be(\"svg\",{width:\"24\",height:\"24\",fill:\"none\",stroke:\"currentColor\",strokeWidth:\"2\",className:\"dropdown-heading-dropdown-arrow gray\",children:be(\"path\",{d:e?\"M18 15 12 9 6 15\":\"M6 9L12 15 18 9\"})});import{jsx as ve}from\"react/jsx-runtime\";var xe=()=>{let{t:e,value:n,options:t,valueRenderer:r}=w(),a=n.length===0,c=n.length===t.length,u=r&&r(n,t);return a?ve(\"span\",{className:\"gray\",children:u||e(\"selectSomeItems\")}):ve(\"span\",{children:u||(c?e(\"allItemsAreSelected\"):(()=>n.map(s=>s.label).join(\", \"))())})};import{jsx as G}from\"react/jsx-runtime\";var Se=({size:e=24})=>G(\"span\",{style:{width:e,marginRight:\"0.2rem\"},children:G(\"svg\",{width:e,height:e,className:\"spinner\",viewBox:\"0 0 50 50\",style:{display:\"inline\",verticalAlign:\"middle\"},children:G(\"circle\",{cx:\"25\",cy:\"25\",r:\"20\",fill:\"none\",className:\"path\"})})});import{jsx as S,jsxs as Ce}from\"react/jsx-runtime\";var Xe=()=>{let{t:e,onMenuToggle:n,ArrowRenderer:t,shouldToggleOnHover:r,isLoading:a,disabled:c,onChange:u,labelledBy:b,value:s,isOpen:p,defaultIsOpen:i,ClearSelectedIcon:l,closeOnChangedValue:L}=w();ye(()=>{L&&m(!1)},[s]);let[y,O]=J(!0),[g,m]=J(i),[M,v]=J(!1),K=t||ge,x=Qe();se(()=>{n&&n(g)},[g]),ye(()=>{i===void 0&&typeof p==\"boolean\"&&(O(!1),m(p))},[p]);let D=h=>{var P;[\"text\",\"button\"].includes(h.target.type)&&[f.SPACE,f.ENTER].includes(h.code)||(y&&(h.code===f.ESCAPE?(m(!1),(P=x==null?void 0:x.current)==null||P.focus()):m(!0)),h.preventDefault())};R([f.ENTER,f.ARROW_DOWN,f.SPACE,f.ESCAPE],D,{target:x});let E=h=>{y&&r&&m(h)},I=()=>!M&&v(!0),W=h=>{!h.currentTarget.contains(h.relatedTarget)&&y&&(v(!1),m(!1))},A=()=>E(!0),_=()=>E(!1),H=()=>{y&&m(a||c?!1:!g)},B=h=>{h.stopPropagation(),u([]),y&&m(!1)};return Ce(\"div\",{tabIndex:0,className:\"dropdown-container\",\"aria-labelledby\":b,\"aria-expanded\":g,\"aria-readonly\":!0,\"aria-disabled\":c,ref:x,onFocus:I,onBlur:W,onMouseEnter:A,onMouseLeave:_,children:[Ce(\"div\",{className:\"dropdown-heading\",onClick:H,children:[S(\"div\",{className:\"dropdown-heading-value\",children:S(xe,{})}),a&&S(Se,{}),s.length>0&&l!==null&&S(\"button\",{type:\"button\",className:\"clear-selected-button\",onClick:B,disabled:c,\"aria-label\":e(\"clearSelected\"),children:l||S(T,{})}),S(K,{expanded:g})]}),g&&S(\"div\",{className:\"dropdown-content\",children:S(\"div\",{className:\"panel-content\",children:S(q,{})})})]})},Q=Xe;import{jsx as X}from\"react/jsx-runtime\";var Ze=e=>X(ne,{props:e,children:X(\"div\",{className:`rmsc ${e.className||\"multi-select\"}`,children:X(Q,{})})}),je=Ze;export{Q as Dropdown,je as MultiSelect,N as SelectItem,q as SelectPanel};\n", "//Import Library\r\nimport React, { useMemo, useEffect, useState, useCallback } from \"react\";\r\nimport { useDropzone } from \"react-dropzone\";\r\nimport { FontAwesomeIcon } from \"@fortawesome/react-fontawesome\";\r\nimport {\r\n  faExclamationCircle,\r\n  faCloudUploadAlt,\r\n} from \"@fortawesome/free-solid-svg-icons\";\r\nimport { Form, Button, Modal, Col } from \"react-bootstrap\";\r\nimport _ from \"lodash\";\r\n\r\nimport toast from 'react-hot-toast';\r\n\r\n//Import services/components\r\nimport apiService from \"../../services/apiService\";\r\nimport { useTranslation } from 'next-i18next';\r\n\r\n// Define type for temp array items\r\ninterface TempItem {\r\n  serverID: string;\r\n  file?: any;\r\n  index: number;\r\n  type: string;\r\n}\r\n\r\nlet temp: TempItem[] = [];\r\n\r\nconst baseStyle: any = {\r\n  flex: 1,\r\n  display: \"flex\",\r\n  flexDirection: \"column\",\r\n  alignItems: \"center\",\r\n  justifyContent: \"center\",\r\n  width: \"100%\",\r\n  height: \"100%\",\r\n  borderWidth: 0.1,\r\n  borderColor: \"#fafafa\",\r\n  backgroundColor: \"#fafafa\",\r\n  color: \"black\",\r\n  transition: \"border  .24s ease-in-out\",\r\n  padding: \"15px\",\r\n};\r\n\r\nconst thumb: any = {\r\n  display: \"flex\",\r\n  borderRadius: 2,\r\n  border: \"1px solid #ddd\",\r\n  margin: 8,\r\n  height: 175,\r\n  boxShadow: \"0 0 15px 0.25px rgba(0,0,0,0.15)\",\r\n  boxSizing: \"border-box\",\r\n};\r\n\r\nconst thumbsContainer: any = {\r\n  display: \"flex\",\r\n  padding: \"10px\",\r\n  width: \"100%\",\r\n  border: \"2px solid gray\",\r\n  flexDirection: \"column\",\r\n  justifyContent: \"flex-start\",\r\n  flexWrap: \"wrap\",\r\n  marginTop: 20,\r\n};\r\n\r\nconst thumbInner: any = {\r\n  display: \"flex\",\r\n  minWidth: 0,\r\n};\r\n\r\nconst deleteIcon: any = {\r\n  display: \"flex\",\r\n  justifyContent: \"center\",\r\n  alignItems: \"center\",\r\n  marginLeft: 30,\r\n};\r\n\r\nconst img = {\r\n  width: \"150px\",\r\n};\r\n\r\nconst activeStyle: any = {\r\n  borderColor: \"#2196f3\",\r\n};\r\n\r\nconst ReactDropZone = (props: any) => {\r\n  const { t } = useTranslation('common');\r\n  const [modalShow, setModalShow] = useState(false);\r\n  const [deleteFile, setDeleteFile] = useState();\r\n  const limit: any =\r\n    props.type == \"application\" ? 20971520 : process.env.UPLOAD_LIMIT;\r\n  const [files, setFiles] = useState<any[]>([]);\r\n  const [multi, setMulti] = useState(true);\r\n  const [imageSource, setImageSource] = useState<string[]>([]);\r\n\r\n  const endpoint = props && props.type === \"application\" ? \"/files\" : \"/image\";\r\n  const imageDelete = async (id: string) => {\r\n    const _res = await apiService.remove(`${endpoint}/${id}`);\r\n  };\r\n\r\n  const removeFile = (file: any) => {\r\n    setDeleteFile(file);\r\n    setModalShow(true);\r\n  };\r\n\r\n  const handleSource = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>, index: number) => {\r\n    const items = [...imageSource];\r\n    items[index] = e.target.value;\r\n    setImageSource(items);\r\n  };\r\n\r\n  const getComponent = (file: any) => {\r\n    const fileType = file && file.name.split(\".\").pop();\r\n    switch (fileType) {\r\n      case \"JPG\":\r\n      case \"jpg\":\r\n      case \"jpeg\":\r\n      case \"jpg\":\r\n      case \"png\":\r\n        return <img src={file.preview} style={img} />;\r\n      case \"pdf\":\r\n        return (\r\n          <img\r\n            src=\"/images/fileIcons/pdfFile.png\"\r\n            className={\r\n              props.type === \"application\" ? \"docPreview\" : \"imgPreview\"\r\n            }\r\n          />\r\n        );\r\n      case \"docx\":\r\n        return (\r\n          <img\r\n            src=\"/images/fileIcons/wordFile.png\"\r\n            className={\r\n              props.type === \"application\" ? \"docPreview\" : \"imgPreview\"\r\n            }\r\n          />\r\n        );\r\n      case \"xls\":\r\n      case \"xlsx\":\r\n        return (\r\n          <img\r\n            src=\"/images/fileIcons/xlsFile.png\"\r\n            className={\r\n              props.type === \"application\" ? \"docPreview\" : \"imgPreview\"\r\n            }\r\n          />\r\n        );\r\n      default:\r\n        return (\r\n          <img\r\n            src=\"/images/fileIcons/wordFile.png\"\r\n            className={\r\n              props.type === \"application\" ? \"docPreview\" : \"imgPreview\"\r\n            }\r\n          />\r\n        );\r\n    }\r\n  };\r\n\r\n  const modalHide = () => setModalShow(false);\r\n\r\n  const cancelHandler = () => {\r\n    setModalShow(false);\r\n  };\r\n\r\n  const submitHandler = (fileselector: any) => {\r\n    fileselector = deleteFile;\r\n    const obj =\r\n      fileselector && fileselector._id\r\n        ? { serverID: fileselector._id }\r\n        : { file: fileselector };\r\n    const _index = _.findIndex(temp, obj);\r\n    //**Delete the source Field**//\r\n    const removeSrc = [...imageSource];\r\n    removeSrc.splice(_index, 1);\r\n    setImageSource(removeSrc);\r\n    //**End**/\r\n    imageDelete(temp[_index].serverID);\r\n    temp.splice(_index, 1);\r\n    props.getImgID(temp, props.index ? props.index : 0);\r\n    const newFiles = [...files];\r\n    newFiles.splice(newFiles.indexOf(fileselector), 1);\r\n    setFiles(newFiles);\r\n    setModalShow(false);\r\n  };\r\n\r\n  const thumbs: any = files.map((file: any, i) => {\r\n    return (\r\n      <div key={i}>\r\n        <Col xs={12}>\r\n          <div className=\"row\">\r\n            <Col\r\n              md={4}\r\n              lg={3}\r\n              className={\r\n                props.type === \"application text-center align-self-center\"\r\n                  ? \"docImagePreview text-center align-self-center\"\r\n                  : \"imgPreview text-center align-self-center\"\r\n              }\r\n            >\r\n              {getComponent(file)}\r\n            </Col>\r\n            <Col md={5} lg={7} className=\"align-self-center\">\r\n              <Form>\r\n                <Form.Group controlId=\"filename\">\r\n                  <Form.Label className=\"mt-2\">{t(\"FileName\")}</Form.Label>\r\n                  <Form.Control\r\n                    size=\"sm\"\r\n                    type=\"text\"\r\n                    disabled\r\n                    value={file.original_name ? file.original_name : file.name}\r\n                  />\r\n                </Form.Group>\r\n                <Form.Group controlId=\"description\">\r\n                  <Form.Label>\r\n                    {props.type === \"application\"\r\n                      ? t(\"ShortDescription/(Max255Characters)\")\r\n                      : t(\"Source/Description\")}\r\n                  </Form.Label>\r\n                  <Form.Control\r\n                    maxLength={props.type === \"application\" ? 255 : undefined}\r\n                    size=\"sm\"\r\n                    type=\"text\"\r\n                    placeholder={\r\n                      props.type === \"application\"\r\n                        ? t(\"`Enteryourdocumentdescription`\")\r\n                        : t(\"`Enteryourimagesource/description`\")\r\n                    }\r\n                    value={imageSource[i]}\r\n                    onChange={(e) => handleSource(e, i)}\r\n                  />\r\n                </Form.Group>\r\n              </Form>\r\n            </Col>\r\n            <Col\r\n              md={3}\r\n              lg={2}\r\n              className=\"align-self-center text-center\"\r\n              onClick={() => removeFile(file)}\r\n            >\r\n              <Button variant=\"dark\">{t(\"Remove\")}</Button>\r\n            </Col>\r\n          </div>\r\n        </Col>\r\n        <Modal show={modalShow} onHide={modalHide}>\r\n          <Modal.Header closeButton>\r\n            <Modal.Title>{t(\"DeleteFile\")}</Modal.Title>\r\n          </Modal.Header>\r\n          <Modal.Body>{t(\"Areyousurewanttodeletethisfile?\")}</Modal.Body>\r\n          <Modal.Footer>\r\n            <Button variant=\"secondary\" onClick={cancelHandler}>\r\n              {t(\"Cancel\")}\r\n            </Button>\r\n            <Button variant=\"primary\" onClick={() => submitHandler(file)}>\r\n              {t(\"yes\")}\r\n            </Button>\r\n          </Modal.Footer>\r\n        </Modal>\r\n      </div>\r\n    );\r\n  });\r\n\r\n  useEffect(() => {\r\n    files.forEach((file) => URL.revokeObjectURL(file.preview));\r\n    temp = [];\r\n  }, []);\r\n\r\n  useEffect(() => {\r\n    props.getImageSource(imageSource);\r\n  }, [imageSource]);\r\n\r\n  useEffect(() => {\r\n    setImageSource(props.srcText);\r\n  }, [props.srcText]);\r\n\r\n  useEffect(() => {\r\n    props && props.singleUpload === \"true\" && setMulti(false);\r\n    if (props && props.datas) {\r\n      const newObj = props.datas.map((item: any, _i: number) => {\r\n        temp.push({\r\n          serverID: item._id,\r\n          index: props.index ? props.index : 0,\r\n          type: item.name.split(\".\")[1],\r\n        });\r\n        const previewState = {\r\n          ...item,\r\n          preview: `${process.env.API_SERVER}/image/show/${item._id}`,\r\n        };\r\n        return previewState;\r\n      });\r\n      setFiles([...newObj]);\r\n    }\r\n  }, [props.datas]);\r\n\r\n  const filesUpload = async (filesinitial: any[], index: number) => {\r\n    if (filesinitial.length > index) {\r\n      try {\r\n        const form: any = new FormData();\r\n        form.append(\"file\", filesinitial[index]);\r\n        const res = await apiService.post(endpoint, form, {\r\n          \"Content-Type\": \"multipart/form-data\",\r\n        });\r\n        temp.push({\r\n          serverID: res._id,\r\n          file: filesinitial[index],\r\n          index: props.index ? props.index : 0,\r\n          type: filesinitial[index].name.split(\".\")[1],\r\n        });\r\n        filesUpload(filesinitial, index + 1);\r\n      } catch (error) {\r\n        filesUpload(filesinitial, index + 1);\r\n      }\r\n    } else {\r\n      props.getImgID(temp, props.index ? props.index : 0);\r\n    }\r\n  };\r\n\r\n  const onDrop = useCallback(async (ondrop_files: any[]) => {\r\n    await filesUpload(ondrop_files, 0);\r\n    const accFiles = ondrop_files.map((file: any) =>\r\n      Object.assign(file, {\r\n        preview: URL.createObjectURL(file),\r\n      })\r\n    );\r\n    multi\r\n      ? setFiles((prevState) => [...prevState, ...accFiles])\r\n      : setFiles([...accFiles]);\r\n  }, []);\r\n\r\n  function nameLengthValidator(file: File) {\r\n    if (endpoint === \"/image\") {\r\n      if (file.type.substring(0, 5) === \"image\") {\r\n        return null;\r\n      } else {\r\n        toast.error(t(\"toast.filetypenotsupport\"));\r\n        return { code: \"file-invalid-type\", message: \"File type not supported\" };\r\n      }\r\n    } else if (endpoint === \"/files\") {\r\n      if (!(file.type.substring(0, 5) !== \"image\")) {\r\n        toast.error(t(\"toast.filetypenotsupport\"));\r\n        return { code: \"file-invalid-type\", message: \"File type not supported\" };\r\n      }\r\n    }\r\n    return null;\r\n  }\r\n  const {\r\n    getRootProps,\r\n    getInputProps,\r\n    isDragActive,\r\n    isDragAccept,\r\n    isDragReject,\r\n    fileRejections,\r\n  } = useDropzone({\r\n    accept:\r\n      props && props.type\r\n        ? \"application/pdf, application/vnd.openxmlformats-officedocument.wordprocessingml.document, application/vnd.oasis.opendocument.text,application/vnd.openxmlformats-officedocument.spreadsheetml.sheet,text/csv\"\r\n        : \"image/*\",\r\n    multiple: multi,\r\n    minSize: 0,\r\n    maxSize: limit,\r\n    onDrop,\r\n    validator: nameLengthValidator,\r\n  });\r\n\r\n  const style = useMemo(\r\n    () => ({\r\n      ...baseStyle,\r\n      ...(isDragActive ? activeStyle : { outline: \"2px dashed #bbb\" }),\r\n      ...(isDragAccept\r\n        ? { outline: \"2px dashed #595959\" }\r\n        : { outline: \"2px dashed #bbb\" }),\r\n      ...(isDragReject ? { outline: \"2px dashed red\" } : { activeStyle }),\r\n    }),\r\n    [isDragActive, isDragReject]\r\n  );\r\n\r\n  let dropZoneMsg;\r\n  if (props && props.type === \"application\") {\r\n    dropZoneMsg = (\r\n      <small style={{ color: \"#595959\" }}>{t(\"DocumentWeSupport\")}</small>\r\n    );\r\n  } else {\r\n    dropZoneMsg = (\r\n      <small style={{ color: \"#595959\" }}>{t(\"ImageWeSupport\")}</small>\r\n    );\r\n  }\r\n\r\n  const isFileTooLarge =\r\n    fileRejections.length > 0 && fileRejections[0].file.size > limit;\r\n  return (\r\n    <>\r\n      <div\r\n        className=\" d-flex justify-content-center align-items-center mt-3\"\r\n        style={{ width: \"100%\", height: \"180px\" }}\r\n      >\r\n        <div {...getRootProps({ style })}>\r\n          <input {...getInputProps()} />\r\n          <FontAwesomeIcon icon={faCloudUploadAlt} size=\"4x\" color=\"#999\" />\r\n          <p style={{ color: \"#595959\", marginBottom: \"0px\" }}>\r\n            {t(\"Drag'n'dropsomefileshere,orclicktoselectfiles\")}\r\n          </p>\r\n\r\n          {!multi && (\r\n            <small style={{ color: \"#595959\" }}>\r\n              <b>Note:</b> One single image will be accepted\r\n            </small>\r\n          )}\r\n          {dropZoneMsg}\r\n          {props.type === \"application\"\r\n            ? isFileTooLarge && (\r\n                <small className=\"text-danger mt-2\">\r\n                  <FontAwesomeIcon\r\n                    icon={faExclamationCircle}\r\n                    size=\"1x\"\r\n                    color=\"red\"\r\n                  />{\" \"}\r\n                  {t(\"FileistoolargeItshouldbelessthan20MB\")}\r\n                </small>\r\n              )\r\n            : isFileTooLarge && (\r\n                <small className=\"text-danger mt-2\">\r\n                  <FontAwesomeIcon\r\n                    icon={faExclamationCircle}\r\n                    size=\"1x\"\r\n                    color=\"red\"\r\n                  />{\" \"}\r\n                  {t(\"FileistoolargeItshouldbelessthan20MB\")}\r\n                </small>\r\n              )}\r\n          {isDragReject && (\r\n            <small className=\"text-danger\" style={{ color: \"#595959\" }}>\r\n              <FontAwesomeIcon\r\n                icon={faExclamationCircle}\r\n                size=\"1x\"\r\n                color=\"red\"\r\n              />{\" \"}\r\n              {t(\"Filetypenotacceptedsorr\")}\r\n            </small>\r\n          )}\r\n        </div>\r\n      </div>\r\n      {files.length > 0 && <div style={thumbsContainer}>{thumbs}</div>}\r\n    </>\r\n  );\r\n};\r\n\r\nexport default ReactDropZone;\r\n"], "names": ["DatePicker", "props", "RKIDatePicker", "GroupVisibility", "animatedComponents", "makeAnimated", "countryList", "setcountryList", "useState", "inputValue", "setInputValue", "value", "setValue", "organisationTypeList", "setOrganisationTypeList", "validEmail", "setValidEmail", "alreadyMember", "setAlreadyMember", "lastAddedEmail", "setLastAddedEmail", "t", "i18n", "useTranslation", "titleSearch", "language", "title_de", "title", "currentLang", "useEffect", "getProjectInitialData", "projectParams_value", "country", "projectParams", "apiService", "get", "Array", "isArray", "data", "map", "item", "_country", "_i", "label", "_id", "institutionType", "_institutionType", "query", "sort", "limit", "languageCode", "nonMember", "addEmailToList", "email", "test", "_users", "post", "message", "setTimeout", "handleKeyDown", "event", "key", "preventDefault", "handleBlur", "trim", "invitesCountry", "invitesRegion", "invitesOrganisationType", "invitesOrganisation", "invitesExpertise", "invitesNetWork", "visibility", "userList", "VisibilityClass", "div", "Col", "className", "lg", "h6", "span", "Row", "Form", "Group", "Label", "Check", "checked", "name", "onClick", "handleVisibility", "type", "style", "marginTop", "md", "sm", "MultiSelect", "overrideStrings", "selectSomeItems", "options", "onChange", "e", "labelledBy", "multiRegionOptions", "multiOrganisationOptions", "max<PERSON><PERSON><PERSON>", "multiExpertsOptions", "multiNetworkOptions", "Select", "closeMenuOnSelect", "components", "is<PERSON><PERSON><PERSON>", "placeholder", "selected", "userHandler", "allOptionValue", "allOption", "length", "multiUserOptions", "small", "CreatableSelect", "isClearable", "menuIsOpen", "visible", "handleChange", "onInputChange", "handleInputChange", "VisibleHandle", "onKeyDown", "onBlur", "defaultProps", "initialState", "description", "startDate", "endDate", "searchData", "images", "file_category", "nonMembers", "images_src", "members", "doc_src", "document", "buttonRef", "useRef", "router", "useRouter", "dropZoneCollection", "setDropZoneCollection", "srcCollection", "setSrcCollection", "docSrcCollection", "setDocSrcCollection", "docCollection", "setDocCollection", "formState", "setFormState", "usersList", "setUsersList", "groupRegion", "setGroupRegion", "groupOrganisation", "setOrganisation", "groupNetwork", "setNetworkList", "expertiseList", "setExpertiseList", "setValidated", "vSpace", "setVspace", "routeSource", "setRouteSource", "groupVisibility", "setGroupVisibility", "formRef", "handleDescription", "prevState", "onChangeDate", "date", "onSubmitForm", "response", "toastMsg", "current", "setAttribute", "toast", "error", "removeAttribute", "start_date", "end_date", "routes", "patch", "success", "Router", "console", "getrespdata", "respData", "fetchFormData", "logUser", "moment", "toDate", "user", "selectedUsers", "for<PERSON>ach", "push", "member", "username", "id", "source", "userParams", "select", "fetchAllUser", "filter", "_userList", "vspace_status", "status", "React", "normalizeGroup", "Object", "keys", "_data", "d", "log", "getinstitution", "institution", "_institution", "getexpertise", "expertise", "_expertise", "getinstitutionNetwork", "institutionNetwork", "_institutionNetwork", "getUsers", "_regions", "_organisation", "_experts", "_network", "userInvites", "groupParams", "country_region", "institution_type", "expertises", "networks", "regions", "organisation", "org", "i", "institutionParams", "getID", "imageIds", "docIds", "includes", "serverID", "getSource", "imgSrcArr", "getDocSource", "docSrcArr", "onHandleChange", "target", "Container", "fluid", "Card", "ValidationForm", "onSubmit", "ref", "onKeyPress", "Body", "Title", "hr", "Control", "<PERSON><PERSON><PERSON><PERSON>", "required", "<PERSON><PERSON><PERSON>", "EditorComponent", "initContent", "evt", "ReactDropZone", "datas", "srcText", "getImgID", "getImageSource", "dateFormat", "placeholderText", "handleEndDateCheckBox", "minDate", "handleInviteChange", "radiohandler", "nonMemberHandler", "<PERSON><PERSON>", "variant", "re<PERSON><PERSON><PERSON><PERSON>", "window", "scrollTo", "Link", "href", "as", "Radio", "RadioGroup", "valueSelected", "errorMessage", "children", "errors", "touched", "useFormikContext", "<PERSON><PERSON><PERSON><PERSON>", "childrenWithProps", "child", "isObject", "String", "RadioItem", "disabled", "values", "setFieldValue", "fieldName", "inline", "TextInput", "SelectGroup", "ValidationFormWrapper", "forwardRef", "autoComplete", "initialValues", "rest", "validationSchema", "<PERSON><PERSON>", "shape", "<PERSON><PERSON>", "actions", "mockEvent", "stopPropagation", "currentTarget", "nativeEvent", "Event", "bubbles", "cancelable", "defaultPrevented", "eventPhase", "isTrusted", "timeStamp", "Date", "now", "isDefaultPrevented", "isPropagationStopped", "persist", "formikProps", "handleSubmit", "displayName", "validator", "multiline", "rows", "pattern", "Field", "validate", "val", "stringVal", "regex", "RegExp", "field", "meta", "isInvalid", "undefined", "temp", "baseStyle", "flex", "display", "flexDirection", "alignItems", "justifyContent", "width", "height", "borderWidth", "borderColor", "backgroundColor", "color", "transition", "padding", "thumbsContainer", "border", "flexWrap", "img", "activeStyle", "dropZoneMsg", "modalShow", "setModalShow", "deleteFile", "setDeleteFile", "process", "files", "setFiles", "multi", "set<PERSON><PERSON><PERSON>", "imageSource", "setImageSource", "endpoint", "imageDelete", "remove", "removeFile", "file", "handleSource", "index", "items", "getComponent", "fileType", "split", "pop", "src", "preview", "modalHide", "cancelHandler", "<PERSON><PERSON><PERSON><PERSON>", "obj", "fileselector", "_index", "_", "removeSrc", "splice", "newFiles", "indexOf", "thumbs", "xs", "controlId", "size", "original_name", "max<PERSON><PERSON><PERSON>", "Modal", "show", "onHide", "Header", "closeButton", "Footer", "URL", "revokeObjectURL", "singleUpload", "filesUpload", "filesinitial", "form", "FormData", "append", "res", "onDrop", "useCallback", "ondrop_files", "accFiles", "assign", "createObjectURL", "getRootProps", "getInputProps", "isDragActive", "isDragAccept", "isDragReject", "fileRejections", "useDropzone", "accept", "multiple", "minSize", "maxSize", "nameLengthValidator", "substring", "code", "useMemo", "outline", "isFileTooLarge", "input", "FontAwesomeIcon", "icon", "faCloudUploadAlt", "p", "marginBottom", "b", "faExclamationCircle"], "sourceRoot": "", "ignoreList": [7]}