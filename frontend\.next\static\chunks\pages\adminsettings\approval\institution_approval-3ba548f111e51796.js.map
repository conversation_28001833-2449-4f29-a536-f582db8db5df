{"version": 3, "file": "static/chunks/pages/adminsettings/approval/institution_approval-3ba548f111e51796.js", "mappings": "gFACA,4CACA,+CACA,WACA,OAAe,EAAQ,KAAoE,CAC3F,EACA,SAFsB,omBCDtB,IAAMA,EAAS,aACFC,EAAmBC,CAAAA,EAAAA,EAAAA,CAAAA,CAAoBA,CAAC,CACnDC,sBAAuB,KACjBC,EAAMC,WAAW,IAAID,EAAMC,WAAW,CAACC,YAAY,IAAIF,EAAMC,WAAW,CAACC,YAAY,CAACN,EAAO,CAKnGO,CALqG,kBAKjF,kBACtB,GAAG,EAE0BL,CAAAA,EAAAA,EAAAA,CAAAA,CAAoBA,CAAC,CAChDC,sBAAuB,KACjBC,EAAMC,WAAW,IAAID,EAAMC,WAAW,CAACG,OAAO,IAAIJ,EAAMC,WAAW,CAACG,OAAO,CAACR,EAAO,CAKzFO,CAL2F,kBAKvE,eACtB,GAAG,EAEmCL,CAAAA,EAAAA,EAAAA,CAAAA,CAAoBA,CAAC,CACzDC,sBAAwBC,GAClBA,IAAMC,WAAW,IAAID,EAAMC,WAAW,CAACI,iBAAiB,IAAIL,EAAMC,WAAW,CAACI,iBAAiB,CAACT,EAAO,CAK7GO,CAL+G,kBAK3F,wBACtB,GAAG,EAE8BL,CAAAA,EAAAA,EAAAA,CAAAA,CAAoBA,CAAC,CACpDC,sBAAuB,KACjBC,EAAMC,WAAW,IAAID,EAAMC,WAAW,CAACK,YAAY,IAAIN,EAAMC,WAAW,CAACK,YAAY,CAACV,EAAO,CAKnGO,CALqG,kBAKjF,mBACtB,GAEaI,EAAkBT,CAAAA,EAAAA,EAAAA,CAAAA,CAAoBA,CAAC,CAClDC,sBAAuB,KACjBC,EAAMC,WAAW,IAAID,EAAMC,WAAW,CAACO,SAAS,IAAIR,EAAMC,WAAW,CAACO,SAAS,CAACZ,EAAO,CAK7FO,CAL+F,kBAK3E,iBACtB,GAEaM,EAA2BX,CAAAA,EAAAA,EAAAA,CAAAA,CAAoBA,CAAC,CAC3DC,sBAAuB,KACjBC,EAAMC,WAAW,IAAID,EAAMC,WAAW,CAACS,uBAAuB,IAAIV,EAAMC,WAAW,CAACS,uBAAuB,CAACd,EAAO,CAKzHO,CAL2H,kBAKvG,0BACtB,GAAG,EAEiCL,CAAAA,EAAAA,EAAAA,CAAAA,CAAoBA,CAAC,CACvDC,sBAAuB,KACjBC,EAAMC,WAAW,IAAID,EAAMC,WAAW,CAACS,uBAAuB,IAAIV,EAAMC,WAAW,CAACS,uBAAuB,CAACd,EAAO,CAKzHO,CAL2H,kBAKvG,sBACtB,GAAG,EAC0BL,CAAAA,EAAAA,EAAAA,CAAAA,CAAoBA,CAAC,CAChDC,sBAAuB,KACjBC,EAAMC,WAAW,IAAID,EAAMC,WAAW,CAACU,MAAM,IAAIX,EAAMC,WAAW,CAACU,MAAM,CAACf,EAAO,CAKvFO,CALyF,kBAKrE,eACtB,GAAG,EAE8BL,CAAAA,EAAAA,EAAAA,CAAAA,CAAoBA,CAAC,CACpDC,sBAAwBC,GAClBA,IAAMC,WAAW,IAAID,EAAMC,WAAW,CAACW,WAAW,IAAIZ,EAAMC,WAAW,CAACW,WAAW,CAAChB,EAAO,CAKjGO,CALmG,kBAK/E,mBACtB,GAAG,EAEuCL,CAAAA,EAAAA,EAAAA,CAAAA,CAAoBA,CAAC,CAC7DC,sBAAuB,KACjBC,EAAMC,WAAW,IAAID,EAAMC,WAAW,CAACY,WAAW,IAAIb,EAAMC,WAAW,CAACY,WAAW,CAACjB,EAAO,CAKjGO,CALmG,kBAK/E,4BACtB,GAAG,EAEuCL,CAAAA,EAAAA,EAAAA,CAAAA,CAAoBA,CAAC,CAC7DC,sBAAuB,KACjBC,EAAMC,WAAW,IAAID,EAAMC,WAAW,CAACa,mBAAmB,IAAId,EAAMC,WAAW,CAACa,mBAAmB,CAAClB,EAAO,CAKjHO,CALmH,kBAK/F,4BACtB,GAAG,EAEoCL,CAAAA,EAAAA,EAAAA,CAAAA,CAAoBA,CAAC,CAC1DC,sBAAuB,GACjBC,IAAMC,WAAW,IAAID,EAAMC,WAAW,CAACc,gBAAgB,IAAIf,EAAMC,WAAW,CAACc,gBAAgB,CAACnB,EAAO,CAK3GO,CAL6G,kBAKzF,yBACtB,GAAG,EAEkCL,CAAAA,EAAAA,EAAAA,CAAAA,CAAoBA,CAAC,CACxDC,sBAAuB,KACjBC,EAAMC,WAAW,IAAID,EAAMC,WAAW,CAACe,gBAAgB,IAAIhB,EAAMC,WAAW,CAACe,gBAAgB,CAACpB,EAAO,CAK3GO,CAL6G,kBAKzF,uBACtB,GAAG,EAEgCL,CAAAA,EAAAA,EAAAA,CAAAA,CAAoBA,CAAC,CACtDC,sBAAuB,KACjBC,EAAMC,WAAW,IAAID,EAAMC,WAAW,CAACgB,cAAc,IAAIjB,EAAMC,WAAW,CAACgB,cAAc,CAACrB,EAAO,CAKvGO,CALyG,kBAKrF,qBACtB,GAAG,EAE0BL,CAAAA,EAAAA,EAAAA,CAAAA,CAAoBA,CAAC,CAChDC,sBAAuB,KACjBC,EAAMC,WAAW,IAAID,EAAMC,WAAW,CAACiB,MAAM,IAAIlB,EAAMC,WAAW,CAACiB,MAAM,CAACtB,EAAO,CAKvFO,CALyF,kBAKrE,eACtB,GAAG,EAE6BL,CAAAA,EAAAA,EAAAA,CAAAA,CAAoBA,CAAC,CACnDC,sBAAuB,KACjBC,EAAMC,WAAW,IAAID,EAAMC,WAAW,CAACkB,UAAU,IAAInB,EAAMC,WAAW,CAACkB,UAAU,CAACvB,EAAO,CAK/FO,CALiG,kBAK7E,kBACtB,GAAG,EAE4BL,CAAAA,EAAAA,EAAAA,CAAAA,CAAoBA,CAAC,CAClDC,sBAAwBC,KAClBA,EAAMC,WAAW,IAAID,EAAMC,WAAW,CAACmB,QAAQ,IAAIpB,EAAMC,WAAW,CAACmB,QAAQ,CAACxB,EAAO,CAK3FO,CAL6F,kBAKzE,iBACtB,GAAG,EAE8BL,CAAAA,EAAAA,EAAAA,CAAAA,CAAoBA,CAAC,CACpDC,sBAAuB,KACjBC,EAAMC,WAAW,IAAID,EAAMC,WAAW,CAACoB,WAAW,IAAIrB,EAAMC,WAAW,CAACoB,WAAW,CAACzB,EAAO,CAKjGO,CALmG,kBAK/E,mBACtB,GAAG,EAEwBL,CAAAA,EAAAA,EAAAA,CAAAA,CAAoBA,CAAC,CAC9CC,sBAAuB,KACjBC,EAAMC,WAAW,IAAID,EAAMC,WAAW,CAACqB,KAAK,IAAItB,EAAMC,WAAW,CAACqB,KAAK,CAAC1B,EAAO,CAKrFO,CALuF,kBAKnE,aACtB,GAAG,EAE8BL,CAAAA,EAAAA,EAAAA,CAAAA,CAAoBA,CAAC,CACpDC,sBAAuB,KACjBC,EAAMC,WAAW,IAAID,EAAMC,WAAW,CAACsB,WAAW,IAAIvB,EAAMC,WAAW,CAACsB,WAAW,CAAC3B,EAAO,CAKjGO,CALmG,kBAK/E,mBACtB,GAAG,EAE8BL,CAAAA,EAAAA,EAAAA,CAAAA,CAAoBA,CAAC,CACpDC,sBAAuB,KACjBC,EAAMC,WAAW,IAAID,EAAMC,WAAW,CAACuB,YAAY,IAAIxB,EAAMC,WAAW,CAACuB,YAAY,CAAC5B,EAAO,CAKnGO,CALqG,kBAKjF,mBACtB,GAAG,EAE0BL,CAAAA,EAAAA,EAAAA,CAAAA,CAAoBA,CAAC,CAChDC,sBAAuB,GACrB,EAAIC,EAAMC,WAAW,IAAID,EAAMC,WAAW,CAACwB,SAAS,IAAIzB,EAAMC,WAAW,CAACwB,SAAS,CAAC7B,EAAO,IAAII,EAAMC,WAAW,CAACyB,OAAO,IAAI1B,EAAMC,WAAW,CAACyB,OAAO,CAAC9B,EAAO,IAAGI,EAAMC,WAAW,CAAC0B,KAAK,IAAI3B,EAAMC,WAAW,CAAC0B,KAAK,CAAC/B,EAAO,IAAGI,EAAMC,WAAW,CAAC2B,MAAM,IAAI5B,EAAMC,WAAW,CAAC2B,MAAM,CAAChC,EAAO,IAAGI,EAAMC,WAAW,CAACY,WAAW,IAAIb,EAAMC,WAAW,CAACY,WAAW,CAACjB,EAAO,IAAGI,EAAMC,WAAW,CAAC4B,MAAM,IAAI7B,EAAMC,WAAW,CAAC4B,MAAM,CAACjC,EAAO,EAAE,CAG5Z,EAETO,mBAAoB,eACtB,GAAG,EAEYN,gBAAgBA,EAAC,2JCrLhC,MA7B4B,QAapBG,EAAAA,EAZN,GAAM,CAAE8B,GAAC,CAAE,CAAGC,CAAAA,EAAAA,EAAAA,EAAAA,CAAcA,CAAC,UACvBC,EAA0B,IAE5B,WAACC,EAAAA,CAASA,CAAAA,CAACC,KAAK,IAACC,UAAU,gBACzB,UAACC,EAAAA,CAAWA,CAAAA,CAACC,MAAOP,EAAE,gDACtB,UAACQ,EAAAA,OAAgBA,CAAAA,CAAAA,MAKjBC,EAA8BC,CAAAA,EAAAA,EAAAA,0BAAAA,CAA0BA,CAAC,IAAM,UAACR,EAAAA,CAAAA,IAChEhC,EAAYyC,CAAAA,EAAAA,EAAAA,EAAAA,CAAWA,CAAC,GAAgBzC,SAC9C,IAAI,GAAEA,GAAAA,OAAAA,EAAAA,EAAOC,IAAPD,OAAOC,GAAPD,OAAAA,EAAAA,EAAAA,WAAoBa,EAApBb,KAAAA,EAAAA,CAAiC,CAAC,GAAlCA,UAA+C,EAInD,UAACuC,EAAAA,CAAAA,GAHM,UAACG,EAAAA,OAAeA,CAAAA,CAAAA,EAK3B,6GCKA,SAASC,EAASC,CAAoB,EACpC,GAAM,GAAEd,CAAC,CAAE,CAAGC,CAAAA,EAAAA,EAAAA,EAAAA,CAAcA,CAAC,UACvBc,EAA6B,CACjCC,gBAAiBhB,EAAE,cACnB,EACI,SACJiB,CAAO,MACPC,CAAI,WACJC,CAAS,uBACTC,CAAqB,WACrBC,CAAS,oBACTC,CAAkB,CAClBC,qBAAmB,kBACnBC,CAAgB,aAChBC,CAAW,oBACXC,CAAkB,gBAClBC,CAAc,SACdC,CAAO,WACPC,CAAS,sBACTC,CAAoB,mBACpBC,CAAiB,CACjBC,YAAU,QACVC,CAAM,kBACNC,CAAgB,cAChBC,CAAY,CAEZ,CADA,EACGC,EACJ,CAAGtB,EAGEuB,EAAiB,4BACrBtB,EACAuB,gBAAiBtC,EAAE,IAP0C,MAQ7DuC,UAAU,UACVtB,EACAC,KAAMA,GAAQ,EAAE,CAChBsB,OAAO,EACPC,2BAA4BrB,EAC5BsB,UAAWrB,EACXsB,gBAAiBf,qBACjBN,EACAsB,YAAY,EACZC,iBAAkBhB,EAClBiB,kBAAmBpB,GAA0C,GAC7DqB,eADwCrB,cACVD,GAA4B,CAAC,GAAI,GAAI,GAAI,CAA3BA,EAA+B,GAAG,CAC9EuB,oBAAqB7B,EACrB8B,oBAAqB1B,EACrB2B,aAAc1B,iBACdG,uBACAG,oBACAC,EACAoB,kBAAmB,UAACC,EAAAA,CAAYA,CAAAA,CAAAA,GAChCC,SAAU,UAACC,IAAAA,CAAEjD,UAAU,6CACvB2B,EACAC,sBACAE,EACAD,mBACA7B,UAAW,WACb,EACA,MACE,UAACkD,EAAAA,EAASA,CAAAA,CAAE,GAAGlB,CAAc,EAEjC,CAEAxB,EAAS2C,YAAY,CAAG,CACtBd,WAAW,EACXE,YAAY,EACZzB,UAAW,KACXU,WAAW,EACXC,qBAAsB,KACtBC,mBAAmB,EACnBC,WAAY,GACZE,kBAAkB,CACpB,EAEA,MAAerB,QAAQA,EAAC,mEChHT,SAASD,EAAgB6C,CAAW,EAC/C,MACE,UAACC,MAAAA,CAAIrD,UAAU,sDACb,UAACqD,MAAAA,CAAIrD,UAAU,mBAAU,yCAG/B,0JC6KF,MAxKA,SAASG,CAA4B,EACnC,GAAM,CAACmD,EAAWC,EAAe,CAAGC,CAAAA,EAAAA,EAAAA,EAuKvBrD,MAvKuBqD,CAAQA,CAAQ,EAAE,EAChD,EAAGC,EAAW,CAAGD,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,EAAU,GACnC,CAAC1C,EAAW4C,EAAa,CAAGF,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAS,GAC7C,CAACG,EAASC,EAAW,CAAGJ,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAS,IACzC,CAACK,EAAaC,EAAS,CAAGN,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,EAAU,GAC5C,CAACO,EAAWC,EAAa,CAAGR,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAS,IAC7C,CAACS,EAAmBC,EAAqB,CAAGV,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAM,CAAC,GAC3D,GAAE7D,CAAC,CAAE,CAAGC,CAAAA,EAAAA,EAAAA,EAAAA,CAAcA,CAAC,UAIvBuE,EAAa,CACjBC,KAAM,CAAEC,WAAY,MAAO,EAC3BC,MAAOX,EACPY,KAAM,EACNC,MAAO,CAAEC,OAAQ,iBAAkB,CACrC,EAEM7D,EAAU,CACd,CACE8D,KAAM/E,EAAE,SACRgF,SAAU,QACVC,KAAM,GAAYC,EAAE3E,KAAK,EAE3B,CACEwE,KAAM/E,EAAE,SACRgF,SAAU,QACVC,KAAM,GAAYC,EAAEC,KAAK,EAE3B,CACEJ,KAAO,cACPC,SAAU,eACVC,KAAM,GAAYC,EAAEE,YAAY,EAElC,CACEL,KAAM/E,EAAE,UACRgF,SAAU,GACVC,KAAM,GACJ,WAACvB,MAAAA,WACC,UAAC2B,EAAAA,CAAMA,CAAAA,CACLC,QAAQ,UACRC,KAAK,KACLC,QAAS,IAAMC,EAAWP,EAAG,oBAE5BlF,EAAE,oBACI,OAET,UAACqF,EAAAA,CAAMA,CAAAA,CACLC,QAAQ,YACRC,KAAK,KACLC,QAAS,IAAMC,EAAWP,EAAG,mBAE5BlF,EAAE,oBAIX,EACD,CAEK0F,EAAc,UAClB5B,GAAW,GACX,IAAM6B,EAAW,MAAMC,EAAAA,CAAUA,CAACC,GAAG,CAAC,eAAgBrB,GAClDmB,GAAYA,EAASzE,IAAI,EAAE,CAC7B0C,EAAe+B,EAASzE,IAAI,EAC5B6C,EAAa4B,EAASG,UAAU,EAChChC,GAAW,GAEf,EAOMvC,EAAsB,MAAOwE,EAAoBnB,KACrDJ,EAAWG,KAAK,CAAGoB,EACnBvB,EAAWI,IAAI,CAAGA,EAClBd,GAAW,GACX,IAAM6B,EAAW,MAAMC,EAAAA,CAAUA,CAACC,GAAG,CAAC,eAAgBrB,GAClDmB,GAAYA,EAASzE,IAAI,EAAIyE,EAASzE,IAAI,CAAC8E,MAAM,CAAG,GAAG,CACzDpC,EAAe+B,EAASzE,IAAI,EAC5B+C,EAAW8B,GACXjC,GAAW,GAEf,EAEAmC,CAAAA,EAAAA,EAAAA,SAAAA,CAASA,CAAC,KACRP,GACF,EAAG,EAAE,EAEL,IAAMD,EAAa,MAAOP,EAAQJ,KAChCX,EAAS,IACTE,EAAaS,GACTI,GAAKA,EAAEgB,GAAG,EAAE,EAEO,CAAE,GAAGhB,CAAC,CAAEJ,OADA,CACQqB,WADnBrB,EAAuB,WAAa,UACP,EAEnD,EAEMsB,EAAe,UAEnB,GAAoC,YAAW,CAA3C9B,EAAkB,MAAS,CAC7B,MAAMsB,EADa,CACHA,CAACS,MAAM,CAAC,gBAAyC,OAAzB/B,EAAkB,GAAM,GAChEoB,IACAY,EAAAA,EAAKA,CAFoD,KAE9C,CAACtG,EAAE,mBACduE,EAAqB,CAAC,GACtBJ,GAAS,OAEL,CACJ,IAAMoC,EAAc,MAAMX,EAAAA,CAAUA,CAACY,KAAK,CACxC,gBAAyC,OAAzBlC,EAAkB,GAAM,EACxCA,GAEF,GAAIiC,GAAsC,CAHP,KAGhBA,EAAYzB,MAAM,CAAU,YAC7CwB,EAAAA,EAAKA,CAACG,KAAK,CACTF,EAAYZ,QAAQ,EAAIY,EAAYZ,QAAQ,CAACe,OAAO,CAChDH,EAAYZ,QAAQ,CAACe,OAAO,CAC5B1G,EAAE,8DAIR0F,IACAY,EAAAA,EAAKA,CAACK,OAAO,CAAC3G,EAAE,kBAChBuE,EAAqB,CAAC,GACtBJ,EAAS,GAEb,CACAuB,IACAnB,EAAqB,CAAC,GACtBJ,GAAS,EACX,EAEMyC,EAAY,IAAMzC,GAAS,GAEjC,MACE,WAACT,MAAAA,WACC,WAACmD,EAAAA,CAAKA,CAAAA,CAACC,KAAM5C,EAAa6C,OAAQH,YAChC,UAACC,EAAAA,CAAKA,CAACG,MAAM,EAACC,WAAW,aACvB,WAACJ,EAAAA,CAAKA,CAACK,KAAK,YACT9C,EAAU+C,MAAM,CAAC,GAAGC,WAAW,GAAKhD,EAAUiD,KAAK,CAAC,GAAG,IAAErH,EAAE,qBAGhE,WAAC6G,EAAAA,CAAKA,CAACS,IAAI,YACVtH,EAAE,0DAA0D,KAAGoE,EAAU,IAAEpE,EAAE,wBAE9E,WAAC6G,EAAAA,CAAKA,CAACU,MAAM,YACX,UAAClC,EAAAA,CAAMA,CAAAA,CAACC,QAAQ,YAAYE,QAASoB,WACpC5G,EAAE,YAEH,UAACqF,EAAAA,CAAMA,CAAAA,CAACC,QAAQ,UAAUE,QAASY,WAClCpG,EAAE,eAKP,UAACa,EAAAA,CAAQA,CAAAA,CACPI,QAASA,EACTC,KAAMyC,EACNxC,UAAWA,EACXU,WAAW,EACXN,oBAAqBA,EACrBC,iBA7FmB,CA6FDA,GA5FtBgD,EAAWG,KAAK,CAAGX,EACnBQ,EAAWI,IAAI,CAAGA,EAClBc,GACF,MA6FF,gEC7Ke,SAASpF,EAAYQ,CAAuB,EACzD,MACE,UAAC0G,KAAAA,CAAGnH,UAAU,wBAAgBS,EAAMP,KAAK,EAE7C", "sources": ["webpack://_N_E/?8fb7", "webpack://_N_E/./pages/adminsettings/permissions.tsx", "webpack://_N_E/./pages/adminsettings/approval/institution_approval.tsx", "webpack://_N_E/./components/common/RKITable.tsx", "webpack://_N_E/./pages/rNoAccess.tsx", "webpack://_N_E/./pages/adminsettings/approval/InstitutionTable.tsx", "webpack://_N_E/./components/common/PageHeading.tsx"], "sourcesContent": ["\n    (window.__NEXT_P = window.__NEXT_P || []).push([\n      \"/adminsettings/approval/institution_approval\",\n      function () {\n        return require(\"private-next-pages/adminsettings/approval/institution_approval.tsx\");\n      }\n    ]);\n    if(module.hot) {\n      module.hot.dispose(function () {\n        window.__NEXT_P.push([\"/adminsettings/approval/institution_approval\"])\n      });\n    }\n  ", "//Import services/components\r\nimport connectedAuthWrapper from 'redux-auth-wrapper/connectedAuthWrapper';\r\n\r\nconst create = \"create:any\";\r\nexport const canAddAreaOfWork = connectedAuthWrapper({\r\n  authenticatedSelector: (state) => {\r\n    if (state.permissions && state.permissions.area_of_work && state.permissions.area_of_work[create]) {\r\n      return true;\r\n    }\r\n    return false;\r\n  },\r\n  wrapperDisplayName: 'CanAddAreaOfWork',\r\n});\r\n\r\nexport const canAddCountry = connectedAuthWrapper({\r\n  authenticatedSelector: (state) => {\r\n    if (state.permissions && state.permissions.country && state.permissions.country[create]) {\r\n      return true;\r\n    }\r\n    return false;\r\n  },\r\n  wrapperDisplayName: 'CanAddCountry',\r\n});\r\n\r\nexport const canAddDeploymentStatus = connectedAuthWrapper({\r\n  authenticatedSelector: (state) => {\r\n    if (state.permissions && state.permissions.deployment_status && state.permissions.deployment_status[create]) {\r\n      return true;\r\n    }\r\n    return false;\r\n  },\r\n  wrapperDisplayName: 'CanAddDeploymentStatus',\r\n});\r\n\r\nexport const canAddEventStatus = connectedAuthWrapper({\r\n  authenticatedSelector: (state) => {\r\n    if (state.permissions && state.permissions.event_status && state.permissions.event_status[create]) {\r\n      return true;\r\n    }\r\n    return false;\r\n  },\r\n  wrapperDisplayName: 'CanAddEventStatus',\r\n});\r\n\r\nexport const canAddExpertise = connectedAuthWrapper({\r\n  authenticatedSelector: (state) => {\r\n    if (state.permissions && state.permissions.expertise && state.permissions.expertise[create]) {\r\n      return true;\r\n    }\r\n    return false;\r\n  },\r\n  wrapperDisplayName: 'CanAddExpertise',\r\n});\r\n\r\nexport const canAddFocalPointApproval = connectedAuthWrapper({\r\n  authenticatedSelector: (state) => {\r\n    if (state.permissions && state.permissions.institution_focal_point && state.permissions.institution_focal_point[create]) {\r\n      return true;\r\n    }\r\n    return false;\r\n  },\r\n  wrapperDisplayName: 'CanAddFocalPointApproval',\r\n});\r\n\r\nexport const canAddVspaceApproval = connectedAuthWrapper({\r\n  authenticatedSelector: (state) => {\r\n    if (state.permissions && state.permissions.institution_focal_point && state.permissions.institution_focal_point[create]) {\r\n      return true;\r\n    }\r\n    return false;\r\n  },\r\n  wrapperDisplayName: 'CanAddVspaceApproval',\r\n});\r\nexport const canAddHazards = connectedAuthWrapper({\r\n  authenticatedSelector: (state) => {\r\n    if (state.permissions && state.permissions.hazard && state.permissions.hazard[create]) {\r\n      return true;\r\n    }\r\n    return false;\r\n  },\r\n  wrapperDisplayName: 'CanAddHazards',\r\n});\r\n\r\nexport const canAddHazardTypes = connectedAuthWrapper({\r\n  authenticatedSelector: (state) => {\r\n    if (state.permissions && state.permissions.hazard_type && state.permissions.hazard_type[create]) {\r\n      return true;\r\n    }\r\n    return false;\r\n  },\r\n  wrapperDisplayName: 'CanAddHazardTypes',\r\n}); \r\n\r\nexport const canAddOrganisationApproval = connectedAuthWrapper({\r\n  authenticatedSelector: (state) => {\r\n    if (state.permissions && state.permissions.institution && state.permissions.institution[create]) {\r\n      return true;\r\n    }\r\n    return false;\r\n  },\r\n  wrapperDisplayName: 'CanAddOrganisationApproval',\r\n}); \r\n\r\nexport const canAddOrganisationNetworks = connectedAuthWrapper({\r\n  authenticatedSelector: (state) => {\r\n    if (state.permissions && state.permissions.institution_network && state.permissions.institution_network[create]) {\r\n      return true;\r\n    }\r\n    return false;\r\n  },\r\n  wrapperDisplayName: 'CanAddOrganisationNetworks',\r\n});\r\n\r\nexport const canAddOrganisationTypes = connectedAuthWrapper({\r\n  authenticatedSelector: (state) => {\r\n    if (state.permissions && state.permissions.institution_type && state.permissions.institution_type[create]) {\r\n      return true;\r\n    }\r\n    return false;\r\n  },\r\n  wrapperDisplayName: 'CanAddOrganisationTypes',\r\n});\r\n\r\nexport const canAddOperationStatus = connectedAuthWrapper({\r\n  authenticatedSelector: (state) => {\r\n    if (state.permissions && state.permissions.operation_status && state.permissions.operation_status[create]) {\r\n      return true;\r\n    }\r\n    return false;\r\n  },\r\n  wrapperDisplayName: 'CanAddOperationStatus',\r\n});\r\n\r\nexport const canAddProjectStatus = connectedAuthWrapper({\r\n  authenticatedSelector: (state) => {\r\n    if (state.permissions && state.permissions.project_status && state.permissions.project_status[create]) {\r\n      return true;\r\n    }\r\n    return false;\r\n  },\r\n  wrapperDisplayName: 'CanAddProjectStatus',\r\n});\r\n\r\nexport const canAddRegions = connectedAuthWrapper({\r\n  authenticatedSelector: (state) => {\r\n    if (state.permissions && state.permissions.region && state.permissions.region[create]) {\r\n      return true;\r\n    }\r\n    return false;\r\n  },\r\n  wrapperDisplayName: 'CanAddRegions',\r\n});\r\n\r\nexport const canAddRiskLevels = connectedAuthWrapper({\r\n  authenticatedSelector: (state) => {\r\n    if (state.permissions && state.permissions.risk_level && state.permissions.risk_level[create]) {\r\n      return true;\r\n    }\r\n    return false;\r\n  },\r\n  wrapperDisplayName: 'CanAddRiskLevels',\r\n});\r\n\r\nexport const canAddSyndromes = connectedAuthWrapper({\r\n  authenticatedSelector: (state) => {\r\n    if (state.permissions && state.permissions.syndrome && state.permissions.syndrome[create]) {\r\n      return true;\r\n    }\r\n    return false;\r\n  },\r\n  wrapperDisplayName: 'CanAddSyndromes',\r\n});\r\n\r\nexport const canAddUpdateTypes = connectedAuthWrapper({\r\n  authenticatedSelector: (state) => {\r\n    if (state.permissions && state.permissions.update_type && state.permissions.update_type[create]) {\r\n      return true;\r\n    }\r\n    return false;\r\n  },\r\n  wrapperDisplayName: 'CanAddUpdateTypes',\r\n});\r\n\r\nexport const canAddUsers = connectedAuthWrapper({\r\n  authenticatedSelector: (state) => {\r\n    if (state.permissions && state.permissions.users && state.permissions.users[create]) {\r\n      return true;\r\n    }\r\n    return false;\r\n  },\r\n  wrapperDisplayName: 'CanAddUsers',\r\n});\r\n\r\nexport const canAddWorldRegion = connectedAuthWrapper({\r\n  authenticatedSelector: (state) => {\r\n    if (state.permissions && state.permissions.worl_region && state.permissions.worl_region[create]) {\r\n      return true;\r\n    }\r\n    return false;\r\n  },\r\n  wrapperDisplayName: 'CanAddWorldRegion',\r\n});\r\n\r\nexport const canAddLandingPage = connectedAuthWrapper({\r\n  authenticatedSelector: (state) => {\r\n    if (state.permissions && state.permissions.landing_page && state.permissions.landing_page[create]) {\r\n      return true;\r\n    }\r\n    return false;\r\n  },\r\n  wrapperDisplayName: 'CanAddLandingPage',\r\n});\r\n\r\nexport const canAddContent = connectedAuthWrapper({\r\n  authenticatedSelector: (state) => {\r\n    if (state.permissions && state.permissions.operation && state.permissions.operation[create] && state.permissions.project && state.permissions.project[create]&& state.permissions.event && state.permissions.event[create]&& state.permissions.vspace && state.permissions.vspace[create]&& state.permissions.institution && state.permissions.institution[create]&& state.permissions.update && state.permissions.update[create]) {\r\n      return true;\r\n    }\r\n    return false;\r\n  },\r\n  wrapperDisplayName: 'CanAddContent',\r\n});\r\n\r\nexport default canAddAreaOfWork;", "//Import Library\r\nimport { Container } from \"react-bootstrap\";\r\n\r\n//Import services/components\r\nimport InstitutionTable from \"./InstitutionTable\";\r\nimport PageHeading from \"../../../components/common/PageHeading\";\r\nimport { useTranslation } from 'next-i18next';\r\nimport { serverSideTranslations } from 'next-i18next/serverSideTranslations';\r\nimport { canAddOrganisationApproval } from \"../permissions\";\r\nimport { useSelector } from \"react-redux\";\r\nimport NoAccessMessage from \"../../rNoAccess\";\r\n\r\n\r\nconst InstitutionApproval = (_props: any) => {\r\n  const { t } = useTranslation('common');\r\n  const ShowInstitutionApproval = () => {\r\n    return (\r\n      <Container fluid className=\"p-0\">\r\n        <PageHeading title={t(\"adminsetting.approval.OrganisationApproval\")} />\r\n        <InstitutionTable />\r\n      </Container>\r\n    )\r\n  };\r\n\r\n  const ShowAddOrganisationApproval = canAddOrganisationApproval(() => <ShowInstitutionApproval />);\r\n  const state:any = useSelector((state: any) => state);\r\n  if (!(state?.permissions?.institution?.['create:any'])) {\r\n    return <NoAccessMessage />\r\n  }\r\n  return(\r\n    <ShowAddOrganisationApproval />\r\n  )\r\n}\r\n\r\nexport async function getServerSideProps({ locale }: { locale: string }) {\r\n  return {\r\n    props: {\r\n      ...(await serverSideTranslations(locale, ['common'])),\r\n    },\r\n  }\r\n}\r\n\r\nexport default InstitutionApproval;\r\n", "//Import Library\r\nimport React from 'react'\r\nimport DataTable  from 'react-data-table-component';\r\n\r\n//Import services/components\r\nimport { useTranslation } from 'next-i18next';\r\nimport CustomLoader from './CustomLoader';\r\n\r\ninterface RKITableProps {\r\n  columns: any[];\r\n  data: any[];\r\n  totalRows?: number;\r\n  resetPaginationToggle?: boolean;\r\n  subheader?: boolean;\r\n  subHeaderComponent?: React.ReactNode;\r\n  handlePerRowsChange?: (newPerPage: number, page: number) => void;\r\n  handlePageChange?: (page: number) => void;\r\n  rowsPerPage?: number[];\r\n  defaultRowsPerPage?: number;\r\n  selectableRows?: boolean;\r\n  loading?: boolean;\r\n  pagServer?: boolean;\r\n  onSelectedRowsChange?: (selectedRows: any) => void;\r\n  clearSelectedRows?: boolean;\r\n  sortServer?: boolean;\r\n  onSort?: (column: any, sortDirection: string) => void;\r\n  persistTableHead?: boolean;\r\n  sortFunction?: (rows: any[], field: any, direction: 'asc' | 'desc') => any[];\r\n  noHeader?: boolean;\r\n  dense?: boolean;\r\n  pagination?: boolean;\r\n  paginationServer?: boolean;\r\n  paginationTotalRows?: number;\r\n  subHeaderAlign?: string;\r\n  [key: string]: any;\r\n}\r\n\r\nfunction RKITable(props: RKITableProps) {\r\n  const { t } = useTranslation('common');\r\n  const paginationComponentOptions = {\r\n    rowsPerPageText: t('Rowsperpage'),\r\n    };\r\n  const {\r\n    columns,\r\n    data,\r\n    totalRows,\r\n    resetPaginationToggle,\r\n    subheader,\r\n    subHeaderComponent,\r\n    handlePerRowsChange,\r\n    handlePageChange,\r\n    rowsPerPage,\r\n    defaultRowsPerPage,\r\n    selectableRows,\r\n    loading,\r\n    pagServer,\r\n    onSelectedRowsChange,\r\n    clearSelectedRows,\r\n    sortServer,\r\n    onSort,\r\n    persistTableHead,\r\n    sortFunction,\r\n    // Filter out any unknown props that might cause DOM warnings\r\n    ...otherProps\r\n  } = props;\r\n\r\n  // Only pass known DataTable props to avoid DOM warnings\r\n  const dataTableProps = {\r\n    paginationComponentOptions,\r\n    noDataComponent: t(\"NoData\"),\r\n    noHeader: true,\r\n    columns,\r\n    data: data || [],\r\n    dense: true,\r\n    paginationResetDefaultPage: resetPaginationToggle,\r\n    subHeader: subheader,\r\n    progressPending: loading,\r\n    subHeaderComponent,\r\n    pagination: true,\r\n    paginationServer: pagServer,\r\n    paginationPerPage: defaultRowsPerPage ? defaultRowsPerPage : 10,\r\n    paginationRowsPerPageOptions: rowsPerPage ? rowsPerPage : [10, 15, 20, 25, 30],\r\n    paginationTotalRows: totalRows,\r\n    onChangeRowsPerPage: handlePerRowsChange,\r\n    onChangePage: handlePageChange,\r\n    selectableRows,\r\n    onSelectedRowsChange,\r\n    clearSelectedRows,\r\n    progressComponent: <CustomLoader/>,\r\n    sortIcon: <i className=\"sort-icon fas fa-exchange-alt\" />,\r\n    sortServer,\r\n    onSort,\r\n    sortFunction,\r\n    persistTableHead,\r\n    className: \"rki-table\"\r\n  };\r\n  return (\r\n    <DataTable {...dataTableProps} />\r\n  )\r\n};\r\n\r\nRKITable.defaultProps = {\r\n  subHeader: false,\r\n  pagination: true,\r\n  totalRows: null,\r\n  pagServer: true,\r\n  onSelectedRowsChange: null,\r\n  clearSelectedRows: false,\r\n  sortServer: false,\r\n  persistTableHead: false\r\n}\r\n\r\nexport default RKITable;\r\n\r\n", "export default function NoAccessMessage(_props: any) {\r\n    return (\r\n      <div className=\"container-fluid p-0 response-message-block\">\r\n        <div className=\"message\">you don't have permission to access</div>\r\n      </div>\r\n    )\r\n  }", "//Import Library\r\nimport React, { useEffect, useState } from \"react\";\r\nimport { <PERSON><PERSON>, <PERSON><PERSON> } from \"react-bootstrap\";\r\nimport toast from 'react-hot-toast';\r\n\r\n//Import services/components\r\nimport apiService from \"../../../services/apiService\";\r\nimport { useTranslation } from 'next-i18next';\r\nimport RKITable from \"../../../components/common/RKITable\";\r\n\r\n\r\nfunction InstitutionTable(_props: any) {\r\n  const [tabledata, setDataToTable] = useState<any[]>([]);\r\n  const [, setLoading] = useState<boolean>(false);\r\n  const [totalRows, setTotalRows] = useState<number>(0);\r\n  const [perPage, setPerPage] = useState<number>(10);\r\n  const [isModalShow, setModal] = useState<boolean>(false);\r\n  const [newStatus, setNewStatus] = useState<string>(\"\");\r\n  const [selectUserDetails, setSelectUserDetails] = useState<any>({});\r\n  const { t } = useTranslation('common');\r\n\r\n\r\n\r\n  const instParams = {\r\n    sort: { created_at: \"desc\" },\r\n    limit: perPage,\r\n    page: 1,\r\n    query: { status: \"Request Pending\" },\r\n  };\r\n\r\n  const columns = [\r\n    {\r\n      name: t(\"Title\"),\r\n      selector: \"title\",\r\n      cell: (d: any) => d.title,\r\n    },\r\n    {\r\n      name: t(\"Email\"),\r\n      selector: \"email\",\r\n      cell: (d: any) => d.email,\r\n    },\r\n    {\r\n      name: (\"ContactName\"),\r\n      selector: \"contact_name\",\r\n      cell: (d: any) => d.contact_name,\r\n    },\r\n    {\r\n      name: t(\"Action\"),\r\n      selector: \"\",\r\n      cell: (d: any) => (\r\n        <div>\r\n          <Button\r\n            variant=\"primary\"\r\n            size=\"sm\"\r\n            onClick={() => instAction(d, \"approve\")}\r\n          >\r\n            {t(\"instu.Approves\")}\r\n          </Button>\r\n          &nbsp;\r\n          <Button\r\n            variant=\"secondary\"\r\n            size=\"sm\"\r\n            onClick={() => instAction(d, \"reject\")}\r\n          >\r\n            {t(\"instu.Reject\")}\r\n          </Button>\r\n        </div>\r\n      ),\r\n    },\r\n  ];\r\n\r\n  const getInstData = async () => {\r\n    setLoading(true);\r\n    const response = await apiService.get(\"/institution\", instParams);\r\n    if (response && response.data) {\r\n      setDataToTable(response.data);\r\n      setTotalRows(response.totalCount);\r\n      setLoading(false);\r\n    }\r\n  };\r\n  const handlePageChange = (page: number) => {\r\n    instParams.limit = perPage;\r\n    instParams.page = page;\r\n    getInstData();\r\n  };\r\n\r\n  const handlePerRowsChange = async (newPerPage: number, page: number) => {\r\n    instParams.limit = newPerPage;\r\n    instParams.page = page;\r\n    setLoading(true);\r\n    const response = await apiService.get(\"/institution\", instParams);\r\n    if (response && response.data && response.data.length > 0) {\r\n      setDataToTable(response.data);\r\n      setPerPage(newPerPage);\r\n      setLoading(false);\r\n    }\r\n  };\r\n\r\n  useEffect(() => {\r\n    getInstData();\r\n  }, []);\r\n\r\n  const instAction = async (d: any, status: string) => {\r\n    setModal(true);\r\n    setNewStatus(status);\r\n    if (d && d._id) {\r\n      const setStatus = status === \"approve\" ? \"Approved\" : \"Rejected\";\r\n      setSelectUserDetails({ ...d, status: setStatus });\r\n    }\r\n  };\r\n\r\n  const modalConfirm = async () => {\r\n\r\n    if( selectUserDetails['status'] === \"Rejected\"){\r\n      await apiService.remove(`/institution/${selectUserDetails[\"_id\"]}`);\r\n      getInstData();\r\n      toast.error(t(\"instu.Rejected\"));\r\n      setSelectUserDetails({});\r\n      setModal(false);\r\n\r\n    }else {\r\n      const updatedData = await apiService.patch(\r\n        `/institution/${selectUserDetails[\"_id\"]}`,\r\n        selectUserDetails\r\n      );\r\n      if (updatedData && updatedData.status === 403) {\r\n        toast.error(\r\n          updatedData.response && updatedData.response.message\r\n            ? updatedData.response.message\r\n            : t(\"adminsetting.FocalPointsApprovalTable.Somethingwentswrong\")\r\n        );\r\n        return;\r\n      } else {\r\n        getInstData();\r\n        toast.success(t(\"instu.Approve\"));\r\n        setSelectUserDetails({});\r\n        setModal(false);\r\n      }\r\n    }\r\n    getInstData();\r\n    setSelectUserDetails({});\r\n    setModal(false);\r\n  };\r\n\r\n  const modalHide = () => setModal(false);\r\n\r\n  return (\r\n    <div>\r\n      <Modal show={isModalShow} onHide={modalHide}>\r\n        <Modal.Header closeButton>\r\n          <Modal.Title>\r\n            {newStatus.charAt(0).toUpperCase() + newStatus.slice(1)} {t(\"Organisation\")}\r\n          </Modal.Title>\r\n        </Modal.Header>\r\n        <Modal.Body>\r\n        {t(\"adminsetting.FocalPointsApprovalTable.Areyousurewantto\")}  {newStatus} {t(\"thisOrganisation?\")}\r\n        </Modal.Body>\r\n        <Modal.Footer>\r\n          <Button variant=\"secondary\" onClick={modalHide}>\r\n          {t(\"cancel\")}\r\n          </Button>\r\n          <Button variant=\"primary\" onClick={modalConfirm}>\r\n          {t(\"yes\")}\r\n          </Button>\r\n        </Modal.Footer>\r\n      </Modal>\r\n\r\n      <RKITable\r\n        columns={columns}\r\n        data={tabledata}\r\n        totalRows={totalRows}\r\n        pagServer={true}\r\n        handlePerRowsChange={handlePerRowsChange}\r\n        handlePageChange={handlePageChange}\r\n      />\r\n    </div>\r\n  );\r\n}\r\n\r\nexport default InstitutionTable;\r\n", "interface PageHeadingProps {\r\n  title: string; // Required based on actual usage\r\n}\r\n\r\nexport default function PageHeading(props: PageHeadingProps) {\r\n  return (\r\n    <h2 className=\"page-heading\">{props.title}</h2>\r\n  )\r\n}\r\n"], "names": ["create", "canAddAreaOfWork", "connectedAuthWrapper", "authenticatedSelector", "state", "permissions", "area_of_work", "wrapperDisplayName", "country", "deployment_status", "event_status", "canAddExpertise", "expertise", "canAddFocalPointApproval", "institution_focal_point", "hazard", "hazard_type", "institution", "institution_network", "institution_type", "operation_status", "project_status", "region", "risk_level", "syndrome", "update_type", "users", "worl_region", "landing_page", "operation", "project", "event", "vspace", "update", "t", "useTranslation", "ShowInstitutionApproval", "Container", "fluid", "className", "PageHeading", "title", "InstitutionTable", "ShowAddOrganisationApproval", "canAddOrganisationApproval", "useSelector", "NoAccessMessage", "RKITable", "props", "paginationComponentOptions", "rowsPerPageText", "columns", "data", "totalRows", "resetPaginationToggle", "subheader", "subHeaderComponent", "handlePerRowsChange", "handlePageChange", "rowsPerPage", "defaultRowsPerPage", "selectableRows", "loading", "pagServer", "onSelectedRowsChange", "clearSelectedRows", "sortServer", "onSort", "persistTableHead", "sortFunction", "otherProps", "dataTableProps", "noDataComponent", "<PERSON><PERSON><PERSON><PERSON>", "dense", "paginationResetDefaultPage", "subHeader", "progressPending", "pagination", "paginationServer", "paginationPerPage", "paginationRowsPerPageOptions", "paginationTotalRows", "onChangeRowsPerPage", "onChangePage", "progressComponent", "CustomLoader", "sortIcon", "i", "DataTable", "defaultProps", "_props", "div", "tabledata", "setDataToTable", "useState", "setLoading", "setTotalRows", "perPage", "setPerPage", "isModalShow", "setModal", "newStatus", "setNewStatus", "selectUserDetails", "setSelectUserDetails", "instParams", "sort", "created_at", "limit", "page", "query", "status", "name", "selector", "cell", "d", "email", "contact_name", "<PERSON><PERSON>", "variant", "size", "onClick", "instAction", "getInstData", "response", "apiService", "get", "totalCount", "newPerPage", "length", "useEffect", "_id", "setStatus", "modalConfirm", "remove", "toast", "updatedData", "patch", "error", "message", "success", "modalHide", "Modal", "show", "onHide", "Header", "closeButton", "Title", "char<PERSON>t", "toUpperCase", "slice", "Body", "Footer", "h2"], "sourceRoot": "", "ignoreList": []}