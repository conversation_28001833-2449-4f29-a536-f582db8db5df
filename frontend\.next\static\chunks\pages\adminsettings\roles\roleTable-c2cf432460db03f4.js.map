{"version": 3, "file": "static/chunks/pages/adminsettings/roles/roleTable-c2cf432460db03f4.js", "mappings": "yKAqCA,SAASA,EAASC,CAAoB,EACpC,GAAM,GAAEC,CAAC,CAAE,CAAGC,CAAAA,EAAAA,EAAAA,EAAAA,CAAcA,CAAC,UACvBC,EAA6B,CACjCC,gBAAiBH,EAAE,cACnB,EACI,CACJI,SAAO,MACPC,CAAI,WACJC,CAAS,uBACTC,CAAqB,WACrBC,CAAS,oBACTC,CAAkB,CAClBC,qBAAmB,kBACnBC,CAAgB,aAChBC,CAAW,oBACXC,CAAkB,gBAClBC,CAAc,SACdC,CAAO,WACPC,CAAS,CACTC,sBAAoB,mBACpBC,CAAiB,YACjBC,CAAU,QACVC,CAAM,kBACNC,CAAgB,cAChBC,CAAY,CAEZ,CADA,EACGC,EACJ,CAAGxB,EAGEyB,EAAiB,4BACrBtB,EACAuB,gBAAiBzB,EAAE,IAP0C,MAQ7D0B,UAAU,UACVtB,EACAC,KAAMA,GAAQ,EAAE,CAChBsB,OAAO,EACPC,2BAA4BrB,EAC5BsB,UAAWrB,EACXsB,gBAAiBf,EACjBN,qBACAsB,YAAY,EACZC,iBAAkBhB,EAClBiB,kBAAmBpB,GAA0C,GAC7DqB,eADwCrB,cACVD,GAA4B,CAAC,GAAI,GAAI,GAAI,CAA3BA,EAA+B,GAAG,CAC9EuB,oBAAqB7B,EACrB8B,oBAAqB1B,EACrB2B,aAAc1B,iBACdG,uBACAG,oBACAC,EACAoB,kBAAmB,UAACC,EAAAA,CAAYA,CAAAA,CAAAA,GAChCC,SAAU,UAACC,IAAAA,CAAEC,UAAU,6CACvBvB,SACAC,EACAE,eACAD,mBACAqB,UAAW,WACb,EACA,MACE,UAACC,EAAAA,EAASA,CAAAA,CAAE,GAAGnB,CAAc,EAEjC,CAEA1B,EAAS8C,YAAY,CAAG,CACtBf,WAAW,EACXE,YAAY,EACZzB,UAAW,KACXU,WAAW,EACXC,qBAAsB,KACtBC,mBAAmB,EACnBC,YAAY,EACZE,kBAAkB,CACpB,EAEA,MAAevB,QAAQA,EAAC,SC/GxB,4CACA,iCACA,WACA,OAAe,EAAQ,KAAsD,CAC7E,EACA,SAFsB,0JC8GtB,MAvGkB,IAChB,GAAM,CAAC+C,EAAWC,EAAe,CAAGC,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAC,EAAE,EACzC,EAAGC,EAAW,CAAGD,CAAAA,CAqGA,CArGAA,EAAAA,QAAAA,CAAQA,EAAC,GAC1B,CAACzC,EAAW2C,EAAa,CAAGF,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAC,GACrC,CAACG,EAASC,EAAW,CAAGJ,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAC,IACjC,CAACK,EAAaC,EAAS,CAAGN,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,EAAC,GACnC,CAACO,EAAYC,EAAc,CAAGR,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAC,CAAC,GACxC,GAAE/C,CAAC,CAAE,CAAGC,CAAAA,EAAAA,EAAAA,EAAAA,CAAcA,CAAC,UAGvBuD,EAAa,CACjB,KAAQ,CAAE,MAAS,KAAM,EACzB,MAASN,EACT,KAAQ,EACR,MAAS,CAAC,CACZ,EAEM9C,EAAU,CACd,CACEqD,KAAM,QACNC,SAAU,OACZ,EACA,CACED,KAAM,SACNC,SAAU,GACVC,KAAM,GAAY,WAACC,MAAAA,WAAI,UAACC,IAAIA,CAACC,KAAK,6BAA6BC,GAAI,OAAvCF,qBAAyE,OAANG,EAAEC,GAAG,WAAK,UAACxB,IAAAA,CAAEC,UAAU,uBAA4B,OAAM,UAACwB,IAAAA,CAAEC,QAAS,IAAMC,EAAWJ,YAAI,UAACvB,IAAAA,CAAEC,UAAU,4BAA8B,MACtO,EACD,CAEK2B,EAAc,UAClBrB,GAAW,GACX,IAAMsB,EAAW,MAAMC,EAAAA,CAAUA,CAACC,GAAG,CAAC,SAAUhB,GAC5Cc,GAAYA,EAASjE,IAAI,EAAIiE,EAASjE,IAAI,CAACoE,MAAM,CAAG,GAAG,CACzD3B,EAAewB,EAASjE,IAAI,EAC5B4C,EAAaqB,EAASI,UAAU,EAChC1B,GAAW,GAEf,EAQMtC,EAAsB,MAAOiE,EAAiBC,KAClDpB,EAAWqB,KAAK,CAAGF,EACnBnB,EAAWoB,IAAI,CAAGA,EAClB5B,EAAW,IACX,IAAMsB,EAAW,MAAMC,EAAAA,CAAUA,CAACC,GAAG,CAAC,SAAUhB,GAC5Cc,GAAYA,EAASjE,IAAI,EAAIiE,EAASjE,IAAI,CAACoE,MAAM,CAAG,GAAG,CACzD3B,EAAewB,EAASjE,IAAI,EAC5B8C,EAAWwB,GACX3B,GAAW,GAEf,EAEMoB,EAAa,MAAOU,IACxBvB,EAAcuB,EAAIb,GAAG,EACrBZ,GAAS,EACX,EAEM0B,EAAe,UACnB,MAAMR,EAAAA,CAAUA,CAACS,MAAM,CAAC,UAAqB,OAAX1B,IAClCe,IACAhB,GAAS,EACX,EAEM4B,EAAY,IAAM5B,GAAS,GAMjC,MAJA6B,CAAAA,EAAAA,EAAAA,SAAAA,CAASA,CAAC,KACRb,GACF,EAAG,EAAE,EAGH,WAACT,MAAAA,WACC,WAACuB,EAAAA,CAAKA,CAAAA,CAACC,KAAMhC,EAAaiC,OAAQJ,YAChC,UAACE,EAAAA,CAAKA,CAACG,MAAM,EAACC,WAAW,aACvB,UAACJ,EAAAA,CAAKA,CAACK,KAAK,WAAExF,EAAE,kBAElB,WAACmF,EAAAA,CAAKA,CAACM,IAAI,YAAEzF,EAAE,kCAAkC,OACjD,WAACmF,EAAAA,CAAKA,CAACO,MAAM,YACX,UAACC,EAAAA,CAAMA,CAAAA,CAACC,QAAQ,YAAYzB,QAASc,WACpCjF,EAAE,YAEH,UAAC2F,EAAAA,CAAMA,CAAAA,CAACC,QAAQ,UAAUzB,QAASY,WAClC/E,EAAE,eAKP,UAACF,EAAAA,CAAQA,CAAAA,CACPM,QAASA,EACTC,KAAMwC,EACNvC,UAAWA,EACXU,WAAW,EACXN,oBAAqBA,EACrBC,iBA1DmB,CA0DDA,GAzDtB6C,EAAWqB,KAAK,CAAG3B,EACnBM,EAAWoB,IAAI,CAAGA,EAClBP,GACF,MA0DF", "sources": ["webpack://_N_E/./components/common/RKITable.tsx", "webpack://_N_E/?4c62", "webpack://_N_E/./pages/adminsettings/roles/roleTable.tsx"], "sourcesContent": ["//Import Library\r\nimport React from 'react'\r\nimport DataTable  from 'react-data-table-component';\r\n\r\n//Import services/components\r\nimport { useTranslation } from 'next-i18next';\r\nimport CustomLoader from './CustomLoader';\r\n\r\ninterface RKITableProps {\r\n  columns: any[];\r\n  data: any[];\r\n  totalRows?: number;\r\n  resetPaginationToggle?: boolean;\r\n  subheader?: boolean;\r\n  subHeaderComponent?: React.ReactNode;\r\n  handlePerRowsChange?: (newPerPage: number, page: number) => void;\r\n  handlePageChange?: (page: number) => void;\r\n  rowsPerPage?: number[];\r\n  defaultRowsPerPage?: number;\r\n  selectableRows?: boolean;\r\n  loading?: boolean;\r\n  pagServer?: boolean;\r\n  onSelectedRowsChange?: (selectedRows: any) => void;\r\n  clearSelectedRows?: boolean;\r\n  sortServer?: boolean;\r\n  onSort?: (column: any, sortDirection: string) => void;\r\n  persistTableHead?: boolean;\r\n  sortFunction?: (rows: any[], field: any, direction: 'asc' | 'desc') => any[];\r\n  noHeader?: boolean;\r\n  dense?: boolean;\r\n  pagination?: boolean;\r\n  paginationServer?: boolean;\r\n  paginationTotalRows?: number;\r\n  subHeaderAlign?: string;\r\n  [key: string]: any;\r\n}\r\n\r\nfunction RKITable(props: RKITableProps) {\r\n  const { t } = useTranslation('common');\r\n  const paginationComponentOptions = {\r\n    rowsPerPageText: t('Rowsperpage'),\r\n    };\r\n  const {\r\n    columns,\r\n    data,\r\n    totalRows,\r\n    resetPaginationToggle,\r\n    subheader,\r\n    subHeaderComponent,\r\n    handlePerRowsChange,\r\n    handlePageChange,\r\n    rowsPerPage,\r\n    defaultRowsPerPage,\r\n    selectableRows,\r\n    loading,\r\n    pagServer,\r\n    onSelectedRowsChange,\r\n    clearSelectedRows,\r\n    sortServer,\r\n    onSort,\r\n    persistTableHead,\r\n    sortFunction,\r\n    // Filter out any unknown props that might cause DOM warnings\r\n    ...otherProps\r\n  } = props;\r\n\r\n  // Only pass known DataTable props to avoid DOM warnings\r\n  const dataTableProps = {\r\n    paginationComponentOptions,\r\n    noDataComponent: t(\"NoData\"),\r\n    noHeader: true,\r\n    columns,\r\n    data: data || [],\r\n    dense: true,\r\n    paginationResetDefaultPage: resetPaginationToggle,\r\n    subHeader: subheader,\r\n    progressPending: loading,\r\n    subHeaderComponent,\r\n    pagination: true,\r\n    paginationServer: pagServer,\r\n    paginationPerPage: defaultRowsPerPage ? defaultRowsPerPage : 10,\r\n    paginationRowsPerPageOptions: rowsPerPage ? rowsPerPage : [10, 15, 20, 25, 30],\r\n    paginationTotalRows: totalRows,\r\n    onChangeRowsPerPage: handlePerRowsChange,\r\n    onChangePage: handlePageChange,\r\n    selectableRows,\r\n    onSelectedRowsChange,\r\n    clearSelectedRows,\r\n    progressComponent: <CustomLoader/>,\r\n    sortIcon: <i className=\"sort-icon fas fa-exchange-alt\" />,\r\n    sortServer,\r\n    onSort,\r\n    sortFunction,\r\n    persistTableHead,\r\n    className: \"rki-table\"\r\n  };\r\n  return (\r\n    <DataTable {...dataTableProps} />\r\n  )\r\n};\r\n\r\nRKITable.defaultProps = {\r\n  subHeader: false,\r\n  pagination: true,\r\n  totalRows: null,\r\n  pagServer: true,\r\n  onSelectedRowsChange: null,\r\n  clearSelectedRows: false,\r\n  sortServer: false,\r\n  persistTableHead: false\r\n}\r\n\r\nexport default RKITable;\r\n\r\n", "\n    (window.__NEXT_P = window.__NEXT_P || []).push([\n      \"/adminsettings/roles/roleTable\",\n      function () {\n        return require(\"private-next-pages/adminsettings/roles/roleTable.tsx\");\n      }\n    ]);\n    if(module.hot) {\n      module.hot.dispose(function () {\n        window.__NEXT_P.push([\"/adminsettings/roles/roleTable\"])\n      });\n    }\n  ", "//Import Library\r\nimport { useState, useEffect } from \"react\";\r\nimport Link from \"next/link\";\r\nimport { Mo<PERSON>, Button } from \"react-bootstrap\";\r\n\r\n//Import services/components\r\nimport RKITable from \"../../../components/common/RKITable\";\r\nimport apiService from \"../../../services/apiService\";\r\nimport { useTranslation } from 'next-i18next';\r\n\r\n\r\nconst RoleTable = (_props: any) => {\r\n  const [tabledata, setDataToTable] = useState([]);\r\n  const [, setLoading] = useState(false);\r\n  const [totalRows, setTotalRows] = useState(0);\r\n  const [perPage, setPerPage] = useState(10);\r\n  const [isModalShow, setModal] = useState(false);\r\n  const [selectRole, setSelectRole] = useState({});\r\n  const { t } = useTranslation('common');\r\n\r\n\r\n  const roleParams = {\r\n    \"sort\": { \"title\": \"asc\" },\r\n    \"limit\": perPage,\r\n    \"page\": 1,\r\n    \"query\": {}\r\n  };\r\n\r\n  const columns = [\r\n    {\r\n      name: 'Title',\r\n      selector: 'title',\r\n    },\r\n    {\r\n      name: 'Action',\r\n      selector: \"\",\r\n      cell: (d: any) => <div><Link href=\"/adminsettings/[...routes]\" as={`/adminsettings/edit_role/${d._id}`} ><i className=\"icon fas fa-edit\" /></Link>&nbsp;<a onClick={() => userAction(d)}><i className=\"icon fas fa-trash-alt\" /></a> </div>\r\n    }\r\n  ];\r\n\r\n  const getRoleData = async () => {\r\n    setLoading(true);\r\n    const response = await apiService.get('/roles', roleParams);\r\n    if (response && response.data && response.data.length > 0) {\r\n      setDataToTable(response.data);\r\n      setTotalRows(response.totalCount);\r\n      setLoading(false);\r\n    }\r\n  };\r\n\r\n  const handlePageChange = (page: any) => {\r\n    roleParams.limit = perPage;\r\n    roleParams.page = page;\r\n    getRoleData();\r\n  };\r\n\r\n  const handlePerRowsChange = async (newPerPage: any, page: any) => {\r\n    roleParams.limit = newPerPage;\r\n    roleParams.page = page;\r\n    setLoading(true);\r\n    const response = await apiService.get('/roles', roleParams);\r\n    if (response && response.data && response.data.length > 0) {\r\n      setDataToTable(response.data);\r\n      setPerPage(newPerPage);\r\n      setLoading(false);\r\n    }\r\n  };\r\n\r\n  const userAction = async (row: any) => {\r\n    setSelectRole(row._id);\r\n    setModal(true);\r\n  }\r\n\r\n  const modalConfirm = async () => {\r\n    await apiService.remove(`/roles/${selectRole}`);\r\n    getRoleData();\r\n    setModal(false);\r\n  }\r\n\r\n  const modalHide = () => setModal(false);\r\n\r\n  useEffect(() => {\r\n    getRoleData();\r\n  }, []);\r\n\r\n  return (\r\n    <div>\r\n      <Modal show={isModalShow} onHide={modalHide}>\r\n        <Modal.Header closeButton>\r\n          <Modal.Title>{t(\"DeleteRole\")}</Modal.Title>\r\n        </Modal.Header>\r\n        <Modal.Body>{t(\"Areyousurewanttodeletethisrole\")} </Modal.Body>\r\n        <Modal.Footer>\r\n          <Button variant=\"secondary\" onClick={modalHide}>\r\n          {t(\"cancel\")}\r\n        </Button>\r\n          <Button variant=\"primary\" onClick={modalConfirm}>\r\n          {t(\"yes\")}\r\n        </Button>\r\n        </Modal.Footer>\r\n      </Modal>\r\n\r\n      <RKITable\r\n        columns={columns}\r\n        data={tabledata}\r\n        totalRows={totalRows}\r\n        pagServer={true}\r\n        handlePerRowsChange={handlePerRowsChange}\r\n        handlePageChange={handlePageChange}\r\n      />\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default RoleTable;"], "names": ["RKITable", "props", "t", "useTranslation", "paginationComponentOptions", "rowsPerPageText", "columns", "data", "totalRows", "resetPaginationToggle", "subheader", "subHeaderComponent", "handlePerRowsChange", "handlePageChange", "rowsPerPage", "defaultRowsPerPage", "selectableRows", "loading", "pagServer", "onSelectedRowsChange", "clearSelectedRows", "sortServer", "onSort", "persistTableHead", "sortFunction", "otherProps", "dataTableProps", "noDataComponent", "<PERSON><PERSON><PERSON><PERSON>", "dense", "paginationResetDefaultPage", "subHeader", "progressPending", "pagination", "paginationServer", "paginationPerPage", "paginationRowsPerPageOptions", "paginationTotalRows", "onChangeRowsPerPage", "onChangePage", "progressComponent", "CustomLoader", "sortIcon", "i", "className", "DataTable", "defaultProps", "tabledata", "setDataToTable", "useState", "setLoading", "setTotalRows", "perPage", "setPerPage", "isModalShow", "setModal", "selectRole", "setSelectRole", "roleParams", "name", "selector", "cell", "div", "Link", "href", "as", "d", "_id", "a", "onClick", "userAction", "getRoleData", "response", "apiService", "get", "length", "totalCount", "newPerPage", "page", "limit", "row", "modalConfirm", "remove", "modalHide", "useEffect", "Modal", "show", "onHide", "Header", "closeButton", "Title", "Body", "Footer", "<PERSON><PERSON>", "variant"], "sourceRoot": "", "ignoreList": []}