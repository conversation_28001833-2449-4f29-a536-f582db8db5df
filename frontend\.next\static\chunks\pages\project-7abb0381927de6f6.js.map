{"version": 3, "file": "static/chunks/pages/project-7abb0381927de6f6.js", "mappings": "qJAoEA,MA/CkD,OAAC,MACjDA,EAAO,QAAQ,IACfC,CA6CaC,CA7CR,EAAE,SA6CkBA,EA5CzBC,EAAY,EAAE,CACdC,MAAI,MACJC,CAAI,CACJC,UAAQ,SACRC,CAAO,OACPC,CAAK,WACLC,GAAY,CAAK,CAClB,UAsBK,GAAqC,UAAxB,OAAOH,EAASI,GAAG,EAAyC,UAAxB,OAAOJ,EAASK,GAAG,CAKtE,UAACC,EAAAA,EAAMA,CAAAA,CACLN,SAAUA,EACVD,KAAMA,EACNG,MAAOA,GAASR,EAChBS,UAAWA,EACXF,QA/BgB,CA+BPM,GA9BPN,GAeFA,EAdoB,IADT,EAeHO,KAZNb,QAYmBc,IAXnBZ,OACAC,WACAE,CACF,EAGe,UACbA,EACAU,YAAa,IAAMV,CACrB,EAE6BW,EAEjC,IAIS,IAYX,qKCcA,MAxE4B,OAAC,YAC3BC,CAAU,QAuEGC,EAtEbC,CAAQ,gBAsEwBD,EAAC,IArEjCE,CAAoB,SACpBC,CAAO,CACPC,cAAY,CAOb,GACO,CAACC,EAAQC,EAAU,CAAGC,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAC,EAAE,EACjC,GAAEC,CAAC,CAAE,CAAGC,CAAAA,EAAAA,EAAAA,EAAAA,CAAcA,CAAC,UAEvBC,EAAmB,MAAOC,IAC9B,IAAMC,EAAW,MAAMC,EAAAA,CAAUA,CAACC,GAAG,CAAC,iBAAkBH,GACpDC,GAAYG,MAAMC,OAAO,CAACJ,EAASK,IAAI,GAAKX,EAAUM,EAASK,IAAI,CACzE,EASA,MAPAC,CAAAA,EAAAA,EAAAA,SAAAA,CAASA,CAAC,KACRR,EAAiB,CACfS,MAAO,CAAC,EACRC,KAAM,CAAE/B,MAAO,KAAM,CACvB,EACF,EAAG,EAAE,EAGH,UAACgC,EAAAA,CAASA,CAAAA,CAACC,KAAK,IAACC,UAAU,eACzB,WAACC,EAAAA,CAAGA,CAAAA,WACF,UAACC,EAAAA,CAAGA,CAAAA,CAACC,GAAI,EAAGH,UAAU,oCACpB,UAACI,EAAAA,CAAWA,CAAAA,CACV1C,KAAK,OACLsC,UAAU,cACVK,YAAapB,EAAE,iBACfqB,aAAW,SACXC,MAAO/B,EACPgC,SAAU9B,MAGd,UAACwB,EAAAA,CAAGA,CAAAA,UACF,UAACO,EAAAA,CAAIA,CAAAA,UACH,WAACA,EAAAA,CAAIA,CAACC,KAAK,EAACC,GAAIV,EAAAA,CAAGA,CAAEW,UAAU,yBAC7B,UAACH,EAAAA,CAAIA,CAACI,KAAK,EAACC,MAAM,IAACC,GAAG,IAAIC,GAAG,aAAI,WAGjC,UAACd,EAAAA,CAAGA,CAAAA,CAACF,UAAU,qBACb,WAACI,EAAAA,CAAWA,CAAAA,CACVO,GAAG,SACHL,aAAW,SACXE,SAAU7B,EACV4B,MAAO1B,YAEP,UAACoC,SAAAA,CAAOV,MAAO,YAAI,QAClBzB,EAAOoC,GAAG,CAAC,CAACC,EAAWC,IAEpB,UAACH,SAAAA,CAAmBV,MAAOY,EAAKE,GAAG,UAChCF,EAAKrD,KAAK,EADAsD,oBAanC,uLClEA,IAAME,EAAU,uCACVC,EAAc,OAAC,sBAAEC,CAAoB,CAAO,UAChD,GAA4BA,EAAqBC,MAAM,CAAG,EAEtD,CAFyD,EAEzD,OAACC,KAAAA,UACEF,EAAqBN,GAAG,CAAC,CAACC,EAAWC,KACpC,GAAID,EAAKQ,eAAe,CAAE,KAKGR,EAJ3B,MACE,UAACS,KAAAA,UACC,UAACC,IAAIA,CACHC,KAAK,uBACLnB,GAAI,aAFDkB,IAE4C,aAA1BV,GAAAA,EAAKQ,eAAe,EAApBR,KAAAA,EAAAA,EAAsBE,GAAtBF,WAEpBA,EAAKQ,eAAe,CAAC7D,KAAK,IALtBsD,EASb,CACF,KAIC,IACT,EA4PA,EA1PA,SAASW,CAAwB,EAC/B,IAAMC,EAASC,CAAAA,EAAAA,EAAAA,KAyPFF,IAzPEE,CAASA,GAClB,GAAEhD,CAAC,CAAE,CAAGC,CAAAA,EAAAA,EAAAA,EAAAA,CAAcA,CAAC,UACvB,aAAEgD,CAAW,iBAAEC,CAAe,CAAE,CAAGC,EACnC,CAAC5D,EAAY6D,EAAc,CAAGC,EAAAA,QAAc,CAAC,IAC7C,CAACzD,EAAc0D,EAAgB,CAAGD,EAAAA,QAAc,CAAC,IACjD,CAACE,EAAuBC,EAAyB,CAAGH,EAAAA,QAAc,CACtE,IAEI,CAACI,EAAWC,EAAe,CAAG3D,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAC,EAAE,EACzC,CAAC4D,EAASC,EAAW,CAAG7D,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,EAAC,GACjC,CAAC8D,EAAWC,EAAa,CAAG/D,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAC,GACrC,CAACgE,EAASC,EAAW,CAAGjE,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAC,IACjC,CAACkE,EAASC,EAAW,CAAGnE,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAC,GACjC,CAACoE,EAAUC,EAAY,CAAGrE,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAM,MAGxCI,EAAqB,CACzBS,KAAM,CAAEyD,WAAY,MAAO,EAC3BC,MAAM,EACNC,MAAOR,EACPS,KAAM,EACN7D,MAAO,CAAC,EACR8D,SAAU,CACR,CAAEC,KAAM,eAAgBC,OAAQ,OAAQ,EACxC,CACED,KAAMrC,EACNsC,OAAQ,mBACV,EACA,CAAED,KAAM,SAAUC,OAAQ,OAAQ,EACnC,CACDA,OACE,2NACJ,EAEM,CAACC,EAAYC,EAAc,CAAG9E,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAACI,GAEvC2E,EAAU,CACd,CACEzG,KAAM2B,EAAE,cACR+E,SAAU,QACVC,UAAU,EACVC,KAAM,GACJ,UAACrC,IAAIA,CAACC,KAAK,uBAAuBnB,GAAI,aAAjCkB,IAAyD,aAAPsC,EAAAA,KAAAA,EAAAA,EAAG9C,GAAG,WAC1D8C,EAAErG,KAAK,EAGd,EACA,CACER,KAAM2B,EAAE,WACR+E,SAAU,UACVC,UAAU,EACVC,KAAM,GACJ,UAAC3C,EAAAA,CAAYC,qBAAsB2C,EAAE3C,oBAAoB,EAE7D,EACA,CACElE,KAAM2B,EAAE,cACR+E,SAAU,eACVE,KAAM,GAAaC,EAAEC,YAAY,CAAGD,EAAEC,YAAY,CAAClD,GAAG,CAAEC,GAAcA,EAAKrD,KAAK,EAAEuG,IAAI,CAAC,MAAQ,EACjG,EACA,CACE/G,KAAM2B,EAAE,UACR+E,SAAU,SACVC,UAAU,EACVC,KAAM,GAAaC,EAAErF,MAAM,EAAIqF,EAAErF,MAAM,CAAChB,KAAK,CAAGqG,EAAErF,MAAM,CAAChB,KAAK,CAAG,EACnE,EACA,CACER,KAAM2B,EAAE,YACR+E,SAAU,YACVC,UAAU,CACZ,EACD,CAEKK,EAAkB,MAAOC,IAC7B1B,GAAW,GAEPb,EAAOpC,KAAK,EAAIoC,EAAOpC,KAAK,CAAC4E,OAAO,EAAE,CACxCD,EAAqB3E,KAAK,CAAC0B,EAAQ,CAAG,CACpCU,EAAOpC,KAAK,CAAC4E,OAAO,CACrB,EAIqB,MAAM,CAA1BrC,EAEF,OAAOoC,EAAqB3E,KAAK,CAAC,oCAAoC,CAClC,GAAG,CAA9BuC,EAAgBV,MAAM,CAE/B8C,EAAqB3E,KAAK,CAAC,oCAAoC,CAAG,CAAC,eAAe,CAGlF2E,EAAqB3E,KAAK,CAAC,oCAAoC,CAAGuC,EAGpE,IAAM9C,EAAW,MAAMC,EAAAA,CAAUA,CAACC,GAAG,CAAC,WAAYgF,GAC9ClF,GAAYG,MAAMC,OAAO,CAACJ,EAASK,IAAI,GAAG,CAC5CiD,EAAetD,EAASK,IAAI,EAC5BwC,EAAY7C,EAASK,IAAI,EACzBqD,EAAa1D,EAASoF,UAAU,GAGlC5B,GAAW,EACb,EAmBM6B,EAAsB,MAAOC,EAAiBlB,KAClDrE,EAAcoE,KAAK,CAAGmB,EACtBvF,EAAcqE,IAAI,CAAGA,EACrBZ,GAAW,GAEPb,EAAOpC,KAAK,EAAIoC,EAAOpC,KAAK,CAAC4E,OAAO,EAAE,GAC1B5E,KAAK,CAAC0B,EAAQ,CAAG,CAC7BU,EAAOpC,KAAK,CAAC4E,OAAO,CACrB,EAIqB,MAAM,CAA1BrC,EACF,OAAO/C,EAAcQ,KAAK,CAAC,oCAAoC,CAC3B,GAAG,CAA9BuC,EAAgBV,MAAM,CAC/BrC,EAAcQ,KAAK,CAAC,oCAAoC,CAAG,CAAC,eAAe,CAE3ER,EAAcQ,KAAK,CAAC,oCAAoC,CAAGuC,EAG7DtD,IAAiBO,EAAcQ,KAAK,CAAG,CAAE,GAAxBR,EAAyCQ,KAAK,CAAEd,OAAQD,EAAa,EACtFuE,IAAahE,EAAcS,IAAI,CAAGuD,CAArBhE,CAA8BS,IAAAA,EAE3C,IAAMR,EAAW,MAAMC,EAAAA,CAAUA,CAACC,GAAG,CAAC,WAAYH,GAC9CC,GAAYG,MAAMC,OAAO,CAACJ,EAASK,IAAI,GAAG,CAC5CiD,EAAetD,EAASK,IAAI,EAC5BwC,EAAY7C,EAASK,IAAI,EACzBuD,EAAW0B,GACX9B,EAAW,KAGbM,EAAWM,EACb,EAGA9D,CAAAA,EAAAA,EAAAA,SAAAA,CAASA,CAAC,KACRkE,EAAWJ,IAAI,CAAG,EAClBa,EAAgBlF,EAClB,EAAG,CAAC+C,EAAiBH,EAAO,EAE5BrC,CAAAA,EAAAA,EAAAA,SAAAA,CAASA,CAAC,KACR2E,EAAgBT,EAClB,EAAG,CAACA,EAAW,EAGf,IAAMe,EAAa,MAAO9D,EAAa+D,KACrChC,GAAW,GACXzD,EAAcS,IAAI,CAAG,CACnB,CAACiB,EAAOkD,QAAQ,CAAC,CAAEa,CACrB,EACAhG,IAAiBO,EAAcQ,KAAK,CAAG,CAAE,GAAGR,EAAcQ,KAAK,CAAEd,OAAQD,EAAa,EACtFL,MAAsBY,GAAAA,EAAcQ,KAAK,CAAG,CAAE,GAAGR,EAAcQ,KAAK,CAAE9B,MAAOU,EAAW,EAExF,MAAM8F,EAAgBlF,GACtBiE,EAAYjE,GACZyD,GAAW,EACb,EAEMiC,EAAY,CAACC,EAAQtB,KACrBsB,GAAG,EACMnF,KAAK,CAAC,KAAQ,CAAGmF,EAC5BlB,EAAWJ,IAAI,CAAGA,GAGlB,OAAOI,EAAWjE,KAAK,CAAC9B,KAAK,CAC7BgG,EAAc,CAAE,GAAGD,CAAU,EAEjC,EAEMmB,EAAoBC,CAAAA,EAAAA,EAAAA,MAAAA,CAAMA,CAC9BC,IAAAA,QAAU,CAAC,CAACH,EAAGtB,IAASqB,EAAUC,EAAGtB,GAAO0B,OAAOC,KAAgC,GAAK,MACxFC,OAAO,CAEHC,EAAyBC,CAAAA,EAAAA,EAAAA,OAAAA,CAAOA,CAAC,KAQrC,IAAMC,EAA2B,IAC/BjD,EAAgBzD,GACZA,GACF+E,EAAWjE,GADD,EACM,CAAC,MAAS,CAAGd,EAC7B+E,EAAWJ,IAAI,CAAGP,GAGlB,OAAOW,EAAWjE,KAAK,CAACd,MAAM,CAC9BgF,EAAc,CAAE,GAAGD,CAAU,EAEjC,EAOA,MACE,UAACpF,EAAAA,OAAmBA,CAAAA,CAClBC,SAPiB,CAOP+G,GANZpD,EAAc9D,EAAEmH,MAAM,CAACnF,KAAK,EAC5ByE,EAAkBzG,EAAEmH,MAAM,CAACnF,KAAK,CAAE2C,EACpC,EAKIvE,qBAAsB,GAAY6G,EAAyBjH,EAAEmH,MAAM,CAACnF,KAAK,EACzE3B,QA5BgB,CA4BP+G,IA3BPnH,IACFiE,EAAyB,CAACD,GAC1BH,EAFc,IAIlB,EAwBI7D,WAAYA,EACZK,aAAcA,GAGpB,EAAG,CAACL,EAAYK,EAAc2D,EAAuBL,EAAiBe,EAAQ,EAE9E,MACE,UAAC0C,EAAAA,CAAQA,CAAAA,CACP7B,QAASA,EACTrE,KAAMgD,EACNI,UAAWA,EACXF,QAASA,EACTiD,SAAS,IACTC,gBAAgB,IAChBC,OAAQnB,EACRoB,UAAU,IACVC,WAAW,EACXzD,sBAAuBA,EACvB0D,mBAAoBZ,EACpBZ,oBAAqBA,EACrByB,iBA3IqB,CA2IHA,GA1IpB/G,EAAcoE,KAAK,CAAGR,EACtB5D,EAAcqE,IAAI,CAAGA,EAEjB5E,IACFO,EAAcQ,KAAK,CAAG,CAAE,CADR,EACWR,EAAcQ,KAAK,CAAEd,OAAQD,EAAa,EAGvEuE,IAAahE,EAAcS,IAAI,CAAGuD,CAArBhE,CAA8BS,IAAAA,EAG3CyE,EAAgBlF,GAChB+D,EAAWM,EACb,GAiIF,6ICjJA,MAnIyB,IACvB,GAAM,MAAE2C,CAAI,CAAE,CAAGlH,CAAAA,EAAAA,EAAAA,EAAAA,CAAcA,CAAC,GAkInBmH,OAjIPC,EAAcF,EAAKG,KAiIIF,EAAC,CAjIG,CAC3B,UAAEG,CAAQ,iBAAErE,CAAe,CAAE,CAAGC,EAChC,CAACqE,EAAQC,EAAU,CAAG1H,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAQ,EAAE,EACxC,CAAC2H,EAAcC,EAAgB,CAAQ5H,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAC,CAAC,GACjD,CAAC6H,EAAYC,EAAc,CAAQ9H,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAC,CAAC,GAC7C,CAAC+H,EAAiBC,EAAmB,CAAQhI,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAC,CAAC,GAoBvDiI,EAAc,KAClBL,EAAgB,MAChBE,EAAc,KAChB,EAEMI,EAAgB,CAACC,EAAmB9I,EAAaE,KACrD0I,IACAL,EAAgBvI,GAChByI,EAAc,CACZxJ,KAAM6J,EAAa7J,IAAI,CACvBC,GAAI4J,EAAa5J,EAAE,CACnBE,UAAW0J,EAAa1J,SAAS,EAErC,EAEM2J,EAAO,GACXC,EAAQ1F,eAAe,EAAI0F,EAAQ1F,eAAe,CAAC2F,WAAW,CAC1DC,EAAQ,GACZF,EAAQ1F,eAAe,EAAI0F,EAAQ1F,eAAe,CAAC2F,WAAW,CAC1DE,EAAa,IACjB,IAAMC,EAA0B,EAAE,CAiBlC,OAhBAvC,IAAAA,OAAS,CAACwC,EAAQlG,oBAAoB,CAAG6F,IACvCM,QAAQC,GAAG,CAAC,UAAWP,GAEvBI,EAAiBI,IAAI,CAAC,CACpB/J,MAAO4J,GAAWA,EAAQ5J,KAAK,CAAG4J,EAAQ5J,KAAK,CAAG,GAClDP,GAAImK,GAAWA,EAAQrG,GAAG,CAAGqG,EAAQrG,GAAG,CAAG,GAC3CrD,IACEoJ,EAAKC,IACLS,WAAWT,EAAQ1F,eAAe,CAAC2F,WAAW,CAAC,EAAE,CAACS,QAAQ,EAC5D9J,IACEsJ,EAAMF,IACNS,WAAWT,EAAQ1F,eAAe,CAAC2F,WAAW,CAAC,EAAE,CAACU,SAAS,EAC7DC,aAAcZ,EAAQY,YAAY,CAClCxK,UAAW4J,EAAQ1F,eAAe,EAAI0F,EAAQ1F,eAAe,CAACN,GAAG,EAErE,GACOoG,CAAgB,CAAC,EAAE,EAGtBS,EAAwB,KAC5B,IAAMC,EAA6B,EAAE,CACrCjD,IAAAA,OAAS,CAACsB,EAAU,IAClB,IAAMlF,EAAUkG,EAAWE,GAC3BS,EAAoBN,IAAI,CAACvG,EAC3B,GAMAoF,EALuBxB,IAAAA,IAKbkD,EALqB,CAACD,EAAqB,SAAUE,CAAK,EAClE,GAAIlG,EAAgBV,MAAM,CAAG,EAC3B,CAD8B,MACvBU,EAAgBmG,QAAQ,CAACD,EAAMJ,YAAY,CAEtD,GAEF,EAEMM,EAA2B,KAC/B,IAAMC,EAAuB,EAAE,CAC/BtD,IAAAA,OAAS,CAACsB,EAAU,IAEhBkB,EAAQlG,oBAAoB,EAC5BkG,EAAQlG,oBAAoB,CAACC,MAAM,CAAG,GACtC,IACAyD,OAAS,CAACwC,EAAQlG,oBAAoB,CAAE,IACtCiH,EAAG3K,KAAK,CAAG4J,EAAQ5J,KAAK,CACxB2K,EAAGlL,EAAE,CAAGmK,EAAQrG,GAAG,CACnBmH,EAAcX,IAAI,CAACY,EACrB,EAEJ,GACAzB,EAAmB9B,IAAAA,OAAS,CAACsD,EAAe,uBAC9C,EAOA,MALA7I,CAAAA,EAAAA,EAAAA,SAAAA,CAASA,CAAC,KACRuI,IACAK,GACF,EAAG,CAAC/B,EAAS,EAGX,UAACkC,EAAAA,CAAOA,CAAAA,CACNC,QAAS1B,EACTV,SAAUD,EACVK,aAAcA,EACdE,WAAY,UAAC+B,IAlGf,GAAM,MAAEC,CAAI,CAAE,CAAGC,SACjB,GAAYD,EAAKpL,SAAS,EAAIsJ,CAAe,CAAC8B,EAAKpL,SAAS,CAAC,CAEzD,CAF2D,EAE3D,OAACiE,KAAAA,UACEqF,CAAe,CAAC8B,EAAKpL,SAAS,CAAC,CAACyD,GAAG,CAAC,CAACC,EAAWC,IAE7C,UAACQ,KAAAA,UACC,UAACmH,IAAAA,CAAEjH,KAAM,IAAgCX,MAAAA,CAA5BmF,EAAY,kBAAwB,OAARnF,EAAK5D,EAAE,WAAK4D,EAAKrD,KAAK,IADxDsD,MAQZ,IACT,EAmFiBwH,CAAWC,KAAMhC,aAE7BJ,EAAOhF,MAAM,EAAI,EACdgF,EAAOvF,GAAG,CAAC,CAACC,EAAMC,IAEd,UAAC5D,EAAAA,CAAYA,CAAAA,CAEXF,KAAM6D,EAAKrD,KAAK,CAChBP,GAAI4D,EAAK5D,EAAE,CACXE,UAAW0D,EAAK1D,SAAS,CACzBE,KAAM,CACJqL,IAAK,8BACP,EACAnL,QAASqJ,EACTtJ,SAAUuD,GARLC,IAYX,MAGV,6OC3DA,MAhEgB,IACd,GAAM,CAAEnC,CAAC,CAAE,CAAGC,CAAAA,EAAAA,EAAAA,EAAAA,CAAcA,CAAC,QA+DhB+J,EA9DP,CAACzC,EAAUtE,EAAY,CAAGlD,CA8DX,EA9DWA,EAAAA,QAAAA,CAAQA,CAAC,EAAE,EACrC,CAACmD,EAAiB+G,EAAmB,CAAGlK,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAC,MAEjDmK,EAAsB,IAExB,UAACtH,IAAIA,CAACC,KAAK,uBAAuBnB,GAAG,aAAhCkB,cACH,UAACuH,EAAAA,CAAMA,CAAAA,CAACC,QAAQ,YAAYC,KAAK,cAC9BrK,EAAE,kBAMLsK,EAAgBC,CAAAA,EAAAA,EAAAA,aAAAA,CAAaA,CAAC,IAAO,UAACL,EAAAA,CAAAA,IAEtCM,EAAgB,IACnBP,EAAmBQ,EACpB,EAEF,MACE,WAAC5J,EAAAA,CAASA,CAAAA,CAACC,KAAK,IAACC,UAAU,gBACzB,UAACC,EAAAA,CAAGA,CAAAA,UACF,UAACC,EAAAA,CAAGA,CAAAA,CAACC,GAAI,YACP,UAACwJ,EAAAA,CAAWA,CAAAA,CAAC7L,MAAOmB,EAAE,kBAG1B,UAACgB,EAAAA,CAAGA,CAAAA,UACF,UAACC,EAAAA,CAAGA,CAAAA,CAACC,GAAI,YACP,UAACkG,EAAAA,OAAgBA,CAAAA,CAAClE,gBAAiBA,EAAiBqE,SAAUA,QAGlE,UAACvG,EAAAA,CAAGA,CAAAA,UACF,UAACC,EAAAA,CAAGA,CAAAA,CAACC,GAAI,YACP,UAACyJ,EAAAA,CAAsBA,CAAAA,CACrBC,QAAUH,GAAOD,EAAcC,GAC/BvH,gBAAiB,EAAE,CACnBsH,cAAeA,QAIrB,UAACxJ,EAAAA,CAAGA,CAAAA,UACF,UAACC,EAAAA,CAAGA,CAAAA,CAACC,GAAI,GAAIH,UAAU,gBACrB,UAACuJ,EAAAA,CAAAA,OAGL,UAACtJ,EAAAA,CAAGA,CAAAA,CAACD,UAAU,gBACb,UAACE,EAAAA,CAAGA,CAAAA,CAACC,GAAI,YACP,UAAC4B,EAAAA,OAAaA,CAAAA,CAACI,gBAAiBA,EAAiBD,YAAaA,UAKxE,uOC9DO,IAAMsH,EAAgBM,CAAAA,EAAAA,EAAAA,CAAAA,CAAoBA,CAAC,CAChDC,sBAAuB,KACjBC,EAAMC,WAAW,IAAID,EAAMC,WAAW,CAACvC,OAAO,IAAIsC,EAAMC,WAAW,CAACvC,OAAO,CAAC,aAAa,CAK/FwC,CALiG,kBAK7E,eACtB,GAAG,EAE8BJ,CAAAA,EAAAA,EAAAA,CAAAA,CAAoBA,CAAC,CACpDC,sBAAuB,KACjBC,EAAMC,WAAW,IAAID,EAAMC,WAAW,CAACvC,OAAO,IAAIsC,EAAMC,WAAW,CAACvC,OAAO,CAAC,aAAa,CAK/FwC,CALiG,kBAK7E,oBACpBC,iBAAkB,IAAM,UAACC,EAAAA,OAAIA,CAAAA,CAAAA,EAC/B,GAAG,EAE2BN,CAAAA,EAAAA,EAAAA,CAAAA,CAAoBA,CAAC,CACjDC,sBAAuB,CAACC,EAAO5H,KAC7B,GAAI4H,EAAMC,WAAW,EAAID,EAAMC,WAAW,CAACvC,OAAO,EAAE,GAC9CsC,EAAMC,WAAW,CAACvC,OAAO,CAAC,aAAa,CACzC,CAD2C,MACpC,OAEP,GAAIsC,EAAMC,WAAW,CAACvC,OAAO,CAAC,aAAa,EACrCtF,EAAMsF,OAAO,EAAItF,EAAMsF,OAAO,CAAC2C,IAAI,EAAIjI,EAAMsF,OAAO,CAAC2C,IAAI,CAAChJ,GAAG,GAAK2I,EAAMK,IAAI,CAAChJ,GAAG,CAClF,CADoF,MAC7E,CAGb,CAEF,OAAO,CACT,EACA6I,mBAAoB,gBACtB,GAAG,EAE+BJ,CAAAA,EAAAA,EAAAA,CAAAA,CAAoBA,CAAC,CACrDC,sBAAuB,CAACC,EAAO5H,KAC7B,GAAI4H,EAAMC,WAAW,EAAID,EAAMC,WAAW,CAACvC,OAAO,EAAE,GAC9CsC,EAAMC,WAAW,CAACvC,OAAO,CAAC,aAAa,CACzC,CAD2C,MACpC,OAEP,GAAIsC,EAAMC,WAAW,CAACvC,OAAO,CAAC,aAAa,EACrCtF,EAAMsF,OAAO,EAAItF,EAAMsF,OAAO,CAAC2C,IAAI,EAAIjI,EAAMsF,OAAO,CAAC2C,IAAI,CAAChJ,GAAG,GAAK2I,EAAMK,IAAI,CAAChJ,GAAG,CAClF,CADoF,KAC7E,EAGb,CAEF,MAAO,EACT,EACA6I,mBAAoB,qBACpBC,iBAAkB,IAAM,UAACC,EAAAA,OAAIA,CAAAA,CAAAA,EAC/B,GAAG,EAEoCN,CAAAA,EAAAA,EAAAA,CAAAA,CAAoBA,CAAC,CAC1DC,sBAAuB,KACjBC,EAAMC,WAAW,IAAID,EAAMC,WAAW,CAACK,MAAM,IAAIN,EAAMC,WAAW,CAACK,MAAM,CAAC,WAAW,CAK3FJ,CAL6F,kBAKzE,yBACtB,GAAG,EAEYV,aAAaA,EAAC,8FCtC7B,SAAS5D,EAASxD,CAAoB,EACpC,GAAM,CAAEnD,CAAC,CAAE,CAAGC,CAAAA,EAAAA,EAAAA,EAAAA,CAAcA,CAAC,UACvBqL,EAA6B,CACjCC,gBAAiBvL,EAAE,cACnB,EACI,SACJ8E,CAAO,MACPrE,CAAI,WACJoD,CAAS,uBACTN,CAAqB,WACrBqD,CAAS,oBACTK,CAAkB,qBAClBxB,CAAmB,kBACnByB,CAAgB,aAChBsE,CAAW,oBACXC,CAAkB,gBAClBC,CAAc,SACd/H,CAAO,WACPqD,CAAS,CACT2E,sBAAoB,mBACpBC,CAAiB,YACjB7E,CAAU,QACVD,CAAM,kBACND,CAAgB,cAChBgF,CAAY,CAEZ,CADA,EACGC,EACJ,CAAG3I,EAGE4I,EAAiB,4BACrBT,EACAU,gBAAiBhM,EAAE,IAP0C,MAQ7DiM,UAAU,UACVnH,EACArE,KAAMA,GAAQ,EAAE,CAChByL,MAAO,GACPC,2BAA4B5I,EAC5B6I,UAAWxF,EACXyF,gBAAiB1I,qBACjBsD,EACAqF,YAAY,EACZC,iBAAkBvF,EAClBwF,kBAAmBf,GAA0C,GAC7DgB,eADwChB,cACVD,GAA4B,CAAC,GAAI,GAAI,GAAI,CAA3BA,EAA+B,GAAG,CAC9EkB,oBAAqB7I,EACrB8I,oBAAqBlH,EACrBmH,aAAc1F,iBACdwE,uBACAC,oBACAC,EACAiB,kBAAmB,UAACC,EAAAA,CAAYA,CAAAA,CAAAA,GAChCC,SAAU,UAACC,IAAAA,CAAEjM,UAAU,6CACvBgG,SACAD,eACA+E,mBACAhF,EACA9F,UAAW,WACb,EACA,MACE,UAACkM,EAAAA,EAASA,CAAAA,CAAE,GAAGlB,CAAc,EAEjC,CAEApF,EAASuG,YAAY,CAAG,CACtBd,WAAW,EACXE,YAAY,EACZzI,UAAW,KACXmD,WAAW,EACX2E,qBAAsB,KACtBC,mBAAmB,EACnB7E,YAAY,EACZF,kBAAkB,CACpB,EAEA,MAAeF,QAAQA,EAAC,8EChGxB,MARyB,OAAC,UAAEhI,CAAQ,OAQrBwO,OARuBC,CAAY,QAQnBD,EARqBE,CAAQ,CAAS,GACnE,MACE,UAACC,EAAAA,EAAUA,CAAAA,CAAC3O,SAAUA,EAAUyO,aAAcA,WAC5C,UAACG,MAAAA,UAAKF,KAGZ,ECdMG,EAAO,mBACPC,EAAQ,qBACRC,EAAO,eACPC,EAAW,kBAgTjB,EA/SkB,CAChB,CACE,YAAe,IA6SJC,OA5SX,EA4SoBA,EAAC,IA5SV,CACT,CACE,MAAS,SACX,EAEJ,EACA,CACE,YAAeJ,EACf,QAAW,CACT,CACE,MAAS,SACX,EACD,EAEH,CACE,YAAeC,EACf,QAAW,CACT,CACE,MAAS,SACX,EACD,EAEH,CACE,YAAe,iBACf,YAAe,WACf,QAAW,CACT,CACE,WAAc,KAChB,EACD,EAEH,CACE,YAAe,yBACf,YAAeE,EACf,QAAW,CACT,CACE,MAAS,SACX,EACD,EAEH,CACE,YAAe,6BACf,YAAe,SACf,QAAW,CACT,CACE,WAAc,KAChB,EACD,EAEH,CACE,YAAe,6BACf,YAAeH,EACf,QAAW,CACT,CACE,MAAS,SACX,EACD,EAEH,CACE,YAAe,0BACf,YAAeG,EACf,QAAW,CACT,CACE,MAAS,SACX,EACD,EAEH,CACE,YAAe,qBACf,YAAeA,EACf,QAAW,CACT,CACE,MAAS,SACX,EACD,EAEH,CACE,YAAe,oBACf,YAAe,WACf,QAAW,CACT,CACE,MAAS,SACX,EACD,EAEH,CACE,YAAe,MACf,QAAW,CACT,CACE,WAAc,KAChB,EACD,EAEH,CACE,YAAe,MACf,YAAe,WACf,QAAW,CACT,CACE,MAAS,SACX,EACD,EAEH,CACE,YAAe,MACf,YAAe,cACf,QAAW,CACT,CACE,WAAc,KAChB,EACD,EAEH,CACE,YAAe,MACf,YAAeH,EACf,QAAW,CACT,CACE,MAAS,SACX,EACD,EAEH,CACE,YAAe,MACf,YAAeC,EACf,QAAW,CACT,CACE,MAAS,SACX,EACD,EAEH,CACE,YAAe,WACf,YAAe,gBACf,QAAW,CACT,CACE,MAAS,SACX,EAEJ,EACA,CACE,YAAe,WACf,YAAeD,EACf,QAAW,CACT,CACE,MAAS,SACX,EACD,EAEH,CACE,YAAe,OACf,QAAW,CACT,CACE,WAAc,KAChB,EACD,EAEH,CACE,YAAe,OACf,YAAe,WACf,QAAW,CACT,CACE,MAAS,SACX,EAEJ,EACA,CACE,YAAe,OACf,YAAe,cACf,QAAW,CACT,CACE,WAAc,KAChB,EACD,EAEH,CACE,YAAe,OACf,YAAeA,EACf,QAAW,CACT,CACE,MAAS,SACX,EACD,EAEH,CACE,YAAe,OACf,YAAeC,EACf,QAAW,CACT,CACE,MAAS,SACX,EAEJ,EACA,CACE,YAAeC,EACf,YAAe,WACf,QAAW,CACT,CACE,MAAS,SACX,EACD,EAEH,CACE,YAAeA,EACf,YAAeC,EACf,QAAW,CACT,CACE,MAAS,SACX,EACD,EAEH,CACE,YAAeD,EACf,YAAeF,EACf,QAAW,CACT,CACE,MAAS,SACX,EACD,EAEH,CACE,YAAeE,EACf,YAAeD,EACf,QAAW,CACT,CACE,MAAS,SACX,EACD,EAEH,CACE,YAAe,aACf,YAAe,SACf,QAAW,CACT,CACE,WAAc,KAChB,EACD,EAEH,CACE,YAAe,UACf,QAAW,CACT,CACE,WAAc,KAChB,EACD,EAEH,CACE,YAAe,UACf,YAAeD,EACf,QAAW,CACT,CACE,MAAS,SACX,EACD,EAEH,CACE,YAAe,UACf,YAAeC,EACf,QAAW,CACT,CACE,MAAS,SACX,EACD,EAEH,CACE,YAAe,eACf,YAAe,gBACf,QAAW,CACT,CACE,MAAS,SACX,EACD,EAEH,CACE,YAAe,kBACf,YAAe,WACf,QAAW,CACT,CACE,MAAS,SACX,EACD,EAEH,CACE,YAAe,QACf,YAAe,WACf,QAAW,CACT,CACE,MAAS,SACX,EACD,EAEH,CACE,YAAe,QACf,YAAeD,EACf,QAAW,CACT,CACE,MAAS,SACX,EACD,EAEJ,2BCnND,MAzEwC,OAAC,SAyE1BK,GAxEbjG,CAAU,CACVF,EAuEoBmG,EAAC,UAvET,eACZC,CAAa,UACbT,CAAQ,QACRU,EAAS,GAAG,OACZC,EAAQ,MAAM,UACd1G,CAAQ,MACR2G,EAAO,CAAC,SACRC,EAAU,CAAC,SACXxE,CAAO,CACR,GACO,QAAEyE,CAAM,CAAE,CAAGnL,CAAAA,EAAAA,EAAAA,SAAAA,CAASA,GACtB,UAAEoL,CAAQ,CAAEC,WAAS,CAAE,CAAIC,CAAAA,EAAAA,EAAAA,CAAAA,CAAaA,UAmB9C,EAAsB,SAAP,CAAQf,MAAAA,UAAI,uBACtBa,EAGH,QAHa,EAGZb,MAAAA,CAAIxM,UAAU,yBACb,UAACwM,MAAAA,CAAIxM,UAAU,WAAWwN,MAAO,CAAEP,eAAOD,EAAQpP,SAAU,UAAW,WACrE,WAAC6P,EAAAA,EAASA,CAAAA,CACRC,kBAzBe,CACrBT,MAAOA,EACPD,OAA0B,UAAlB,OAAOA,EAAsB,GAAU,OAAPA,EAAO,MAAMA,CACvD,EAuBQW,OAhBOZ,CAgBCY,EArBM,CACpB3P,IAAK,SAIyB4P,CAH9B3P,IAAK,SACP,EAmBQiP,KAAMA,EACNW,OAhBU,CAgBFC,GAfd5M,EAAI6M,UAAU,CAAC,CACbC,OAAQnB,CACV,EACF,EAaQoB,QAAS,CACPd,EAhBWN,MAgBFM,EACTpP,WAAW,EACXmQ,mBAAmB,EACnBC,mBAAmB,EACnBC,YAAY,EACZC,gBAAgB,EAChBC,gBAAgB,EAChBC,mBAAmB,CACrB,YAECjC,EACAzF,GAAcF,GAAgBA,EAAarI,WAAW,EACrD,UAAC8N,EAAgBA,CACfxO,SAAU+I,EAAarI,SADR8N,EACmB,GAClCC,aAAc,KAEZ1E,QAAQC,GAAG,CAAC,qBACZe,GAAAA,GACF,WAEC9B,GAHC8B,QA5BQ,UAAC6D,MAAAA,UAAI,mBAsC7B,mBC3FA,4CACA,WACA,WACA,OAAe,EAAQ,KAAsC,CAC7D,EACA,SAFsB,kICmBtB,SAAS5C,EAAuBxH,CAAkC,EAChE,GAAM,SAACyH,CAAO,CAAC,CAAGzH,EACZ,CAACoM,EAAYC,EAAc,CAAGzP,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,EAAC,GACvC,CAAC0P,EAAQC,EAAU,CAAG3P,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAe,EAAE,EAC/C,CAACmD,EAAiB+G,EAAmB,CAAGlK,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAW,EAAE,EAC7D,GAAEC,CAAC,CAAE,CAAGC,CAAAA,EAAAA,EAAAA,EAAAA,CAAcA,CAAC,UACvB0P,EAAe,CACnB,MAAS,CAAC,EACV,MAAS,IACT,KAAQ,CAAE,MAAS,KAAM,CAC3B,EAEMC,EAAiB,MAAOC,IAC5B,IAAMzP,EAAW,MAAMC,EAAAA,CAAUA,CAACC,GAAG,CAAC,eAAgBuP,GACtD,GAAIzP,GAAYG,MAAMC,OAAO,CAACJ,EAASK,IAAI,EAAG,CAC5C,IAAMqP,EAA6B,EAAE,CAC/BC,EAAwB,EAAE,CAEhC9J,IAAAA,IAAM,CAAC7F,EAASK,IAAI,CAAE,CAACyB,EAAM+D,KAC3B,IAAM+J,EAAyB,CAC7B,GAAG9N,CAAI,CACP+N,WAAW,CACb,EACAH,EAAalH,IAAI,CAACoH,GAClBD,EAAYnH,IAAI,CAAC1G,EAAKE,GAAG,CAC3B,GAEAwI,EAAQmF,GACR9F,EAAmB8F,GACnBL,EAAUI,EACZ,CACF,EAEApP,CAAAA,EAAAA,EAAAA,SAAAA,CAASA,CAAC,KACRkP,EAAeD,EACjB,EAAG,EAAE,EAmBL,IAAMO,EAA+B,IACnC,IAAMC,EAAiB,IAAIV,EAAO,CAC9BW,EAAyB,IAAIlN,EAAgB,CAEjDiN,EAAeE,OAAO,CAAC,CAACnO,EAAMC,KACxBD,EAAKoO,IAAI,GAAKhR,EAAEmH,MAAM,CAACnI,EAAE,EAAE,CAC7B6R,CAAc,CAAChO,EAAM,CAAC8N,SAAS,CAAG3Q,EAAEmH,MAAM,CAAC8J,OAAO,CAC7CjR,EAAEmH,MAAM,CAAC8J,OAAO,CAGnBH,CAHqB,CAGExH,IAAI,CAAC1G,EAAKE,GAAG,EAFpCgO,EAAyBA,EAAuBI,MAAM,CAACC,GAAKA,IAAMvO,EAAKE,GAAG,EAKhF,GAEA6H,EAAmBmG,GACnBxF,EAAQwF,GACRZ,GAAc,GACdE,EAAUS,EACZ,EAcA,MACE,WAAC5C,MAAAA,CAAIxM,UAAU,qCACb,UAACS,EAAAA,CAAIA,CAACkP,KAAK,EACTjS,KAAK,WACLH,GAAK,MACLqS,MAAO3Q,EAAE,cACTuQ,QAAShB,EACThO,SAzDoBqP,CAyDVC,GAxDd,IAAMV,EAAiBV,EAAOxN,GAAG,CAACC,GAAS,EACzC,EADyC,CACtCA,CAAI,CACP+N,UAAWW,EAAMnK,MAAM,CAAC8J,OAAO,CACjC,GAEIO,EAA6B,EAC7BF,GAAMnK,MAAM,CAAC8J,OAAO,EAAE,CACxBO,EAAmBX,EAAelO,GAAG,CAACC,GAAQA,EAAKE,IAAG,EAGxDwI,EAAQkG,GACR7G,EAAmB6G,GACnBtB,EAAcoB,EAAMnK,MAAM,CAAC8J,OAAO,EAClCb,EAAUS,EACZ,IA4CKV,EAAOxN,GAAG,CAAC,CAACC,EAAMC,IAEf,UAACX,EAAAA,CAAIA,CAACkP,KAAK,EAETjS,KAAK,WACLH,GAAI4D,EAAKoO,IAAI,CACbK,MAAOzO,EAAKrD,KAAK,CACjByC,MAAOY,EAAKoO,IAAI,CAChB/O,SAAU2O,EACVK,QAASd,CAAM,CAACtN,EAAM,CAAC8N,SAAS,EAN3B9N,IAUX,UAACgI,EAAAA,CAAMA,CAAAA,CAACvL,QAlCW,CAkCFmS,IAjCnB,IAAMZ,EAAiBV,EAAOxN,GAAG,CAACC,GAAS,EACzC,EADyC,CACtCA,CAAI,CACP+N,WAAW,EACb,GAEAhG,EAAmB,EAAE,EACrBuF,EAAc,IACdE,EAAUS,GACVvF,EAAQ,EAAE,CACZ,EAwBqC7J,UAAU,0BAAkBf,EAAE,gBAGrE,CAEA2K,EAAuBuC,YAAY,CAAG,CACpCtC,QAAS,KAAS,CACpB,EAEA,MAAeD,sBAAsBA,EAAC,wCCzIvB,SAASD,EAAYvH,CAAuB,EACzD,MACE,UAAC6N,KAAAA,CAAGjQ,UAAU,wBAAgBoC,EAAMtE,KAAK,EAE7C", "sources": ["webpack://_N_E/./components/common/RKIMapMarker.tsx", "webpack://_N_E/./pages/project/ProjectsTableFilter.tsx", "webpack://_N_E/./pages/project/ProjectsTable.tsx", "webpack://_N_E/./pages/project/ListMapContainer.tsx", "webpack://_N_E/./pages/project/index.tsx", "webpack://_N_E/./pages/project/permission.tsx", "webpack://_N_E/./components/common/RKITable.tsx", "webpack://_N_E/./components/common/RKIMapInfowindow.tsx", "webpack://_N_E/./components/common/mapStyles.tsx", "webpack://_N_E/./components/common/RKIMap1.tsx", "webpack://_N_E/?919d", "webpack://_N_E/./components/common/RegionsMultiCheckboxes.tsx", "webpack://_N_E/./components/common/PageHeading.tsx"], "sourcesContent": ["import React from 'react';\r\nimport { Marker } from '@react-google-maps/api';\r\n\r\ninterface RKIMapMarkerProps {\r\n  name?: string;\r\n  id?: string;\r\n  countryId?: string;\r\n  type?: string;\r\n  icon?: {\r\n    url: string;\r\n    scaledSize?: google.maps.Size;\r\n  };\r\n  position: {\r\n    lat: number;\r\n    lng: number;\r\n  };\r\n  onClick?: (props: any, marker: any, e: any) => void;\r\n  title?: string;\r\n  draggable?: boolean;\r\n}\r\n\r\nconst RKIMapMarker: React.FC<RKIMapMarkerProps> = ({\r\n  name = 'Marker',\r\n  id = '',\r\n  countryId = '',\r\n  type,\r\n  icon,\r\n  position,\r\n  onClick,\r\n  title,\r\n  draggable = false,\r\n}) => {\r\n  const handleClick = (e: google.maps.MapMouseEvent) => {\r\n    if (onClick) {\r\n      const markerProps = {\r\n        name,\r\n        id,\r\n        countryId,\r\n        type,\r\n        position,\r\n      };\r\n\r\n      // Create a marker-like object for compatibility with old onClick signature\r\n      const marker = {\r\n        position,\r\n        getPosition: () => position,\r\n      };\r\n\r\n      onClick(markerProps, marker, e);\r\n    }\r\n  };\r\n\r\n  // Ensure position is valid\r\n  if (!position || typeof position.lat !== 'number' || typeof position.lng !== 'number') {\r\n    return null;\r\n  }\r\n\r\n  return (\r\n    <Marker\r\n      position={position}\r\n      icon={icon}\r\n      title={title || name}\r\n      draggable={draggable}\r\n      onClick={handleClick}\r\n    />\r\n  );\r\n};\r\n\r\nexport default RKIMapMarker;\r\n", "// Import Library\r\nimport { useEffect, useState } from \"react\";\r\nimport { Col, Container, FormControl, Form, Row } from \"react-bootstrap\";\r\n\r\n//Import services/components\r\nimport { useTranslation } from 'next-i18next';\r\nimport apiService from \"../../services/apiService\";\r\n\r\nconst ProjectsTableFilter = ({\r\n  filterText,\r\n  onFilter,\r\n  onFilterStatusChange,\r\n  onClear,\r\n  filterStatus,\r\n}: {\r\n  filterText: any,\r\n  onFilter: any,\r\n  onFilterStatusChange: any,\r\n  onClear: any,\r\n  filterStatus: any,\r\n}) => {\r\n  const [status, setStatus] = useState([]);\r\n  const { t } = useTranslation('common');\r\n\r\n  const getProjectStatus = async (projectParams: any) => {\r\n    const response = await apiService.get(\"/projectstatus\", projectParams);\r\n    if (response && Array.isArray(response.data)) { setStatus(response.data) }\r\n  };\r\n\r\n  useEffect(() => {\r\n    getProjectStatus({\r\n      query: {},\r\n      sort: { title: \"asc\" },\r\n    });\r\n  }, []);\r\n\r\n  return (\r\n    <Container fluid className=\"p-0\">\r\n      <Row>\r\n        <Col xs={6} className=\"ps-0 align-self-end mb-3\">\r\n          <FormControl\r\n            type=\"text\"\r\n            className=\"searchInput\"\r\n            placeholder={t(\"vspace.Search\")}\r\n            aria-label=\"Search\"\r\n            value={filterText}\r\n            onChange={onFilter}\r\n          />\r\n        </Col>\r\n        <Col>\r\n          <Form>\r\n            <Form.Group as={Row} controlId=\"statusFilter\">\r\n              <Form.Label column sm=\"3\" lg=\"2\">\r\n                Status\r\n              </Form.Label>\r\n              <Col className=\"ps-0 pe-1\">\r\n                <FormControl\r\n                  as=\"select\"\r\n                  aria-label=\"Status\"\r\n                  onChange={onFilterStatusChange}\r\n                  value={filterStatus}\r\n                >\r\n                  <option value={\"\"}>All</option>\r\n                  {status.map((item: any, index) => {\r\n                    return (\r\n                      <option key={index} value={item._id}>\r\n                        {item.title}\r\n                      </option>\r\n                    );\r\n                  })}\r\n                </FormControl>\r\n              </Col>\r\n            </Form.Group>\r\n          </Form>\r\n        </Col>\r\n      </Row>\r\n    </Container>\r\n  );\r\n};\r\n\r\nexport default ProjectsTableFilter;\r\n", "//Import Library\r\nimport Link from \"next/link\";\r\nimport React, { useEffect, useState, useMemo, useRef } from \"react\";\r\nimport _ from \"lodash\";\r\nimport { useRouter } from \"next/router\";\r\n\r\n//Import services/components\r\nimport RKITable from \"../../components/common/RKITable\";\r\nimport ProjectsTableFilter from \"./ProjectsTableFilter\";\r\nimport apiService from \"../../services/apiService\";\r\nimport { useTranslation } from 'next-i18next';\r\n\r\nconst partner = \"partner_institutions.partner_country\"\r\nconst CountryLink = ({ partner_institutions }: any) => {\r\n  if (partner_institutions && partner_institutions.length > 0) {\r\n    return (\r\n      <ul>\r\n        {partner_institutions.map((item: any, index: any) => {\r\n          if (item.partner_country) {\r\n            return (\r\n              <li key={index}>\r\n                <Link\r\n                  href=\"/country/[...routes]\"\r\n                  as={`/country/show/${item.partner_country?._id}`}\r\n                >\r\n                  {item.partner_country.title}\r\n                </Link>\r\n              </li>\r\n            );\r\n          }\r\n        })}\r\n      </ul>\r\n    );\r\n  }\r\n  return null;\r\n};\r\n\r\nfunction ProjectsTable(props: any) {\r\n  const router = useRouter();\r\n  const { t } = useTranslation('common');\r\n  const { setProjects, selectedRegions } = props;\r\n  const [filterText, setFilterText] = React.useState(\"\");\r\n  const [filterStatus, setFilterStatus] = React.useState(\"\");\r\n  const [resetPaginationToggle, setResetPaginationToggle] = React.useState(\r\n    false\r\n  );\r\n  const [tabledata, setDataToTable] = useState([]);\r\n  const [loading, setLoading] = useState(false);\r\n  const [totalRows, setTotalRows] = useState(0);\r\n  const [perPage, setPerPage] = useState(10);\r\n  const [pageNum, setPageNum] = useState(1);\r\n  const [pageSort, setPageSort] = useState<any>(null);\r\n\r\n\r\n  const projectParams: any = {\r\n    sort: { created_at: \"desc\" },\r\n    lean: true,\r\n    limit: perPage,\r\n    page: 1,\r\n    query: {},\r\n    populate: [\r\n      { path: \"area_of_work\", select: \"title\" },\r\n      {\r\n        path: partner, // \"partner_institutions.partner_country\"\r\n        select: \"coordinates title\",\r\n      },\r\n      { path: \"status\", select: \"title\" },\r\n    ],\r\n    select:\r\n      \"-website -description -start_date -end_date -country -region -partner_institutions.partner_region -partner_institutions.partner_institution -institution_invites -vspace -vspace_visibility -user -created_at -updated_at\",\r\n  };\r\n\r\n  const [projParams, setProjParams] = useState(projectParams);\r\n\r\n  const columns = [\r\n    {\r\n      name: t(\"Project(s)\"),\r\n      selector: \"title\",\r\n      sortable: true,\r\n      cell: (d: any) => (\r\n        <Link href=\"/project/[...routes]\" as={`/project/show/${d?._id}`}>\r\n          {d.title}\r\n        </Link>\r\n      ),\r\n    },\r\n    {\r\n      name: t(\"Country\"),\r\n      selector: \"country\",\r\n      sortable: true,\r\n      cell: (d: any) => (\r\n        <CountryLink partner_institutions={d.partner_institutions} />\r\n      ),\r\n    },\r\n    {\r\n      name: t(\"AreaofWork\"),\r\n      selector: \"area_of_work\",\r\n      cell: (d: any) => (d.area_of_work ? d.area_of_work.map((item: any) => item.title).join(\", \") : \"\"),\r\n    },\r\n    {\r\n      name: t(\"Status\"),\r\n      selector: \"status\",\r\n      sortable: true,\r\n      cell: (d: any) => (d.status && d.status.title ? d.status.title : \"\"),\r\n    },\r\n    {\r\n      name: t(\"Fundedby\"),\r\n      selector: \"funded_by\",\r\n      sortable: true,\r\n    },\r\n  ];\r\n\r\n  const getProjectsData = async (projectParamsinitial: any) => {\r\n    setLoading(true);\r\n\r\n    if (router.query && router.query.country) {\r\n      projectParamsinitial.query[partner] = [\r\n        router.query.country,\r\n      ];\r\n    }\r\n\r\n    // Handle selectedRegions with proper condition\r\n    if (selectedRegions === null) {\r\n      // First load: don't apply region filter\r\n      delete projectParamsinitial.query[\"partner_institutions.world_region\"];\r\n    } else if (selectedRegions.length === 0) {\r\n      // No regions selected: force zero results\r\n      projectParamsinitial.query[\"partner_institutions.world_region\"] = [\"__NO_MATCH__\"];\r\n    } else {\r\n      // Normal filtering\r\n      projectParamsinitial.query[\"partner_institutions.world_region\"] = selectedRegions;\r\n    }\r\n\r\n    const response = await apiService.get(\"/project\", projectParamsinitial);\r\n    if (response && Array.isArray(response.data)) {\r\n      setDataToTable(response.data);\r\n      setProjects(response.data);\r\n      setTotalRows(response.totalCount);\r\n    }\r\n\r\n    setLoading(false);\r\n  };\r\n\r\n\r\n  const handlePageChange = (page: any) => {\r\n    projectParams.limit = perPage;\r\n    projectParams.page = page;\r\n\r\n    if (filterStatus) {\r\n      projectParams.query = { ...projectParams.query, status: filterStatus };\r\n    }\r\n\r\n    pageSort && (projectParams.sort = pageSort.sort);\r\n\r\n    // Get the data\r\n    getProjectsData(projectParams);\r\n    setPageNum(page);\r\n  };\r\n\r\n\r\n  const handlePerRowsChange = async (newPerPage: any, page: any) => {\r\n    projectParams.limit = newPerPage;\r\n    projectParams.page = page;\r\n    setLoading(true);\r\n\r\n    if (router.query && router.query.country) {\r\n      projectParams.query[partner] = [\r\n        router.query.country,\r\n      ];\r\n    }\r\n\r\n    // Handle selected regions similarly as in `getProjectsData()`\r\n    if (selectedRegions === null) {\r\n      delete projectParams.query[\"partner_institutions.world_region\"];\r\n    } else if (selectedRegions.length === 0) {\r\n      projectParams.query[\"partner_institutions.world_region\"] = [\"__NO_MATCH__\"];\r\n    } else {\r\n      projectParams.query[\"partner_institutions.world_region\"] = selectedRegions;\r\n    }\r\n\r\n    filterStatus && (projectParams.query = { ...projectParams.query, status: filterStatus });\r\n    pageSort && (projectParams.sort = pageSort.sort);\r\n\r\n    const response = await apiService.get(\"/project\", projectParams);\r\n    if (response && Array.isArray(response.data)) {\r\n      setDataToTable(response.data);\r\n      setProjects(response.data);\r\n      setPerPage(newPerPage);\r\n      setLoading(false);\r\n    }\r\n\r\n    setPageNum(page);\r\n  };\r\n\r\n\r\n  useEffect(() => {\r\n    projParams.page = 1;\r\n    getProjectsData(projectParams);\r\n  }, [selectedRegions, router]);\r\n\r\n  useEffect(() => {\r\n    getProjectsData(projParams);\r\n  }, [projParams]);\r\n\r\n\r\n  const handleSort = async (column: any, sortDirection: any) => {\r\n    setLoading(true);\r\n    projectParams.sort = {\r\n      [column.selector]: sortDirection,\r\n    };\r\n    filterStatus && (projectParams.query = { ...projectParams.query, status: filterStatus });\r\n    filterText !== \"\" && (projectParams.query = { ...projectParams.query, title: filterText });\r\n\r\n    await getProjectsData(projectParams);\r\n    setPageSort(projectParams);\r\n    setLoading(false);\r\n  };\r\n\r\n  const sendQuery = (q: any, page: any) => {\r\n    if (q) {\r\n      projParams.query[\"title\"] = q;\r\n      projParams.page = page;\r\n      setProjParams({ ...projParams });\r\n    } else {\r\n      delete projParams.query.title;\r\n      setProjParams({ ...projParams });\r\n    }\r\n  };\r\n\r\n  const handleSearchTitle = useRef(\r\n    _.debounce((q, page) => sendQuery(q, page), Number(process.env.SEARCH_DEBOUNCE_TIME) || 300)\r\n  ).current;\r\n\r\n  const subHeaderComponentMemo = useMemo(() => {\r\n    const handleClear = () => {\r\n      if (filterText) {\r\n        setResetPaginationToggle(!resetPaginationToggle);\r\n        setFilterText(\"\");\r\n      }\r\n    };\r\n\r\n    const handleFilterStatusChange = (status: any) => {\r\n      setFilterStatus(status);\r\n      if (status) {\r\n        projParams.query[\"status\"] = status;\r\n        projParams.page = pageNum;\r\n        setProjParams({ ...projParams });\r\n      } else {\r\n        delete projParams.query.status;\r\n        setProjParams({ ...projParams });\r\n      }\r\n    };\r\n\r\n    const handleChange = (e: any) => {\r\n      setFilterText(e.target.value);\r\n      handleSearchTitle(e.target.value, pageNum);\r\n    };\r\n\r\n    return (\r\n      <ProjectsTableFilter\r\n        onFilter={handleChange}\r\n        onFilterStatusChange={(e: any) => handleFilterStatusChange(e.target.value)}\r\n        onClear={handleClear}\r\n        filterText={filterText}\r\n        filterStatus={filterStatus}\r\n      />\r\n    );\r\n  }, [filterText, filterStatus, resetPaginationToggle, selectedRegions, pageNum]);\r\n\r\n  return (\r\n    <RKITable\r\n      columns={columns}\r\n      data={tabledata}\r\n      totalRows={totalRows}\r\n      loading={loading}\r\n      subheader\r\n      persistTableHead\r\n      onSort={handleSort}\r\n      sortServer\r\n      pagServer={true}\r\n      resetPaginationToggle={resetPaginationToggle}\r\n      subHeaderComponent={subHeaderComponentMemo}\r\n      handlePerRowsChange={handlePerRowsChange}\r\n      handlePageChange={handlePageChange}\r\n    />\r\n  );\r\n}\r\n\r\nexport default ProjectsTable;\r\n", "//Import Library\r\nimport React, { useState, useEffect } from \"react\";\r\nimport _ from \"lodash\";\r\n\r\n//Import services/components\r\nimport { useTranslation } from 'next-i18next';\r\nimport RKIMAP1 from \"../../components/common/RKIMap1\";\r\nimport RKIMapMarker from \"../../components/common/RKIMapMarker\";\r\n\r\nconst ListMapContainer = (props: any) => {\r\n  const { i18n } = useTranslation('common');\r\n  const currentLang = i18n.language;\r\n  const { projects, selectedRegions } = props;\r\n  const [points, setPoints] = useState<any[]>([]);\r\n  const [activeMarker, setactiveMarker]: any = useState({});\r\n  const [markerInfo, setMarkerInfo]: any = useState({});\r\n  const [groupedProjects, setGroupedProjects]: any = useState({});\r\n\r\n  const MarkerInfo = (Markerprops: any) => {\r\n    const { info } = Markerprops;\r\n    if (info && info.countryId && groupedProjects[info.countryId]) {\r\n      return (\r\n        <ul>\r\n          {groupedProjects[info.countryId].map((item: any, index: any) => {\r\n            return (\r\n              <li key={index}>\r\n                <a href={`/${currentLang}/project/show/${item.id}`}>{item.title}</a>\r\n              </li>\r\n            );\r\n          })}\r\n        </ul>\r\n      );\r\n    }\r\n    return null;\r\n  };\r\n\r\n  const resetMarker = () => {\r\n    setactiveMarker(null);\r\n    setMarkerInfo(null);\r\n  };\r\n\r\n  const onMarkerClick = (propsinitial: any, marker: any, e: any) => {\r\n    resetMarker();\r\n    setactiveMarker(marker);\r\n    setMarkerInfo({\r\n      name: propsinitial.name,\r\n      id: propsinitial.id,\r\n      countryId: propsinitial.countryId,\r\n    });\r\n  };\r\n\r\n  const part = (pointer: any) =>\r\n    pointer.partner_country && pointer.partner_country.coordinates;\r\n  const part1 = (pointer: any) =>\r\n    pointer.partner_country && pointer.partner_country.coordinates;\r\n  const getpointer = (project: any) => {\r\n    const projectParterner: any[] = [];\r\n    _.forEach(project.partner_institutions, (pointer: any) => {\r\n      console.log(\"pointer\", pointer);\r\n\r\n      projectParterner.push({\r\n        title: project && project.title ? project.title : \"\",\r\n        id: project && project._id ? project._id : \"\",\r\n        lat:\r\n          part(pointer) &&\r\n          parseFloat(pointer.partner_country.coordinates[0].latitude),\r\n        lng:\r\n          part1(pointer) &&\r\n          parseFloat(pointer.partner_country.coordinates[0].longitude),\r\n        world_region: pointer.world_region,\r\n        countryId: pointer.partner_country && pointer.partner_country._id,\r\n      });\r\n    });\r\n    return projectParterner[0];\r\n  };\r\n\r\n  const setPointsFromProjects = () => {\r\n    const filterProjectpoints: any[] = [];\r\n    _.forEach(projects, (project) => {\r\n      const partner = getpointer(project);\r\n      filterProjectpoints.push(partner);\r\n    });\r\n    const filteredPoints = _.filter(filterProjectpoints, function (point) {\r\n      if (selectedRegions.length > 0) {\r\n        return selectedRegions.includes(point.world_region);\r\n      }\r\n    });\r\n    setPoints(filteredPoints);\r\n  };\r\n\r\n  const getProjectsGroupBCountry = () => {\r\n    const countriesList: any[] = [];\r\n    _.forEach(projects, (project) => {\r\n      if (\r\n        project.partner_institutions &&\r\n        project.partner_institutions.length > 0\r\n      ) {\r\n        _.forEach(project.partner_institutions, (pi) => {\r\n          pi.title = project.title;\r\n          pi.id = project._id;\r\n          countriesList.push(pi);\r\n        });\r\n      }\r\n    });\r\n    setGroupedProjects(_.groupBy(countriesList, \"partner_country._id\"));\r\n  };\r\n\r\n  useEffect(() => {\r\n    setPointsFromProjects();\r\n    getProjectsGroupBCountry();\r\n  }, [projects]);\r\n\r\n  return (\r\n    <RKIMAP1\r\n      onClose={resetMarker}\r\n      language={currentLang}\r\n      activeMarker={activeMarker}\r\n      markerInfo={<MarkerInfo info={markerInfo} />}\r\n    >\r\n      {points.length >= 1\r\n        ? points.map((item, index) => {\r\n            return (\r\n              <RKIMapMarker\r\n                key={index}\r\n                name={item.title}\r\n                id={item.id}\r\n                countryId={item.countryId}\r\n                icon={{\r\n                  url: \"/images/map-marker-white.svg\",\r\n                }}\r\n                onClick={onMarkerClick}\r\n                position={item}\r\n              />\r\n            );\r\n          })\r\n        : null}\r\n    </RKIMAP1>\r\n  );\r\n};\r\n\r\nexport default ListMapContainer;\r\n", "//Import Library\r\nimport React, {useState} from \"react\";\r\nimport Link from 'next/link';\r\nimport Button from 'react-bootstrap/Button';\r\nimport {Container, Col, Row} from \"react-bootstrap\";\r\n\r\n//Import services/components\r\nimport PageHeading from \"../../components/common/PageHeading\";\r\nimport ProjectsTable from \"./ProjectsTable\";\r\nimport ListMapContainer from \"./ListMapContainer\";\r\nimport { useTranslation } from 'next-i18next';\r\nimport { canAddProject } from \"./permission\";\r\nimport RegionsMultiCheckboxes from \"../../components/common/RegionsMultiCheckboxes\";\r\nimport { serverSideTranslations } from 'next-i18next/serverSideTranslations';\r\n\r\nconst Project = (_props: any) => {\r\n  const { t } = useTranslation('common');\r\n  const [projects, setProjects] = useState([]);\r\n  const [selectedRegions, setSelectedRegions] = useState(null);\r\n\r\n  const AddProjectComponent = () => {\r\n    return (\r\n      <Link href='/project/[...routes]' as='/project/create' >\r\n        <Button variant=\"secondary\" size=\"sm\">\r\n          {t('addProject')}\r\n        </Button>\r\n      </Link>\r\n    );\r\n  };\r\n\r\n  const CanAddProject = canAddProject(() =>  <AddProjectComponent />);\r\n\r\n  const regionHandler = (val: any) => {\r\n     setSelectedRegions(val);\r\n    }\r\n\r\n  return (\r\n    <Container fluid className=\"p-0\">\r\n      <Row>\r\n        <Col xs={12}>\r\n          <PageHeading title={t(\"projects\")}/>\r\n        </Col>\r\n      </Row>\r\n      <Row>\r\n        <Col xs={12}>\r\n          <ListMapContainer selectedRegions={selectedRegions} projects={projects} />\r\n        </Col>\r\n      </Row>\r\n      <Row>\r\n        <Col xs={12}>\r\n          <RegionsMultiCheckboxes\r\n            filtreg={(val)=> regionHandler(val)}\r\n            selectedRegions={[]}\r\n            regionHandler={regionHandler}\r\n          />\r\n        </Col>\r\n      </Row>\r\n      <Row>\r\n        <Col xs={12} className=\"ps-4\">\r\n          <CanAddProject />\r\n        </Col>\r\n      </Row>\r\n      <Row className=\"mt-3\">\r\n        <Col xs={12}>\r\n          <ProjectsTable selectedRegions={selectedRegions} setProjects={setProjects} />\r\n        </Col>\r\n      </Row>\r\n    </Container>\r\n  );\r\n}\r\n\r\nexport async function getStaticProps({ locale }: { locale: string }) {\r\n  return {\r\n    props: {\r\n      ...(await serverSideTranslations(locale, ['common'])),\r\n    },\r\n  }\r\n}\r\n\r\nexport default Project;", "//Import Library\r\nimport React from 'react';\r\nimport connectedAuthWrapper from 'redux-auth-wrapper/connectedAuthWrapper';\r\n\r\n//Import services/components\r\nimport R403 from \"../r403\";\r\n\r\nexport const canAddProject = connectedAuthWrapper({\r\n  authenticatedSelector: (state) => {\r\n    if (state.permissions && state.permissions.project && state.permissions.project['create:any']) {\r\n      return true;\r\n    }\r\n    return false;\r\n  },\r\n  wrapperDisplayName: 'CanAddProject',\r\n});\r\n\r\nexport const canAddProjectForm = connectedAuthWrapper({\r\n  authenticatedSelector: (state) => {\r\n    if (state.permissions && state.permissions.project && state.permissions.project['create:any']) {\r\n      return true;\r\n    }\r\n    return false;\r\n  },\r\n  wrapperDisplayName: 'CanAddProjectForm',\r\n  FailureComponent: () => <R403/>\r\n});\r\n\r\nexport const canEditProject = connectedAuthWrapper({\r\n  authenticatedSelector: (state, props) => {\r\n    if (state.permissions && state.permissions.project) {\r\n      if (state.permissions.project['update:any']) {\r\n        return true;\r\n      } else { //if update:own\r\n        if (state.permissions.project['update:own']) {\r\n          if (props.project && props.project.user && props.project.user._id === state.user._id) {\r\n            return true;\r\n          }  \r\n        }\r\n      }\r\n    }\r\n    return false;\r\n  },\r\n  wrapperDisplayName: 'CanEditProject',\r\n});\r\n\r\nexport const canEditProjectForm = connectedAuthWrapper({\r\n  authenticatedSelector: (state, props) => {\r\n    if (state.permissions && state.permissions.project) {\r\n      if (state.permissions.project['update:any']) {\r\n        return true;\r\n      } else { //if update:own\r\n        if (state.permissions.project['update:own']) {\r\n          if (props.project && props.project.user && props.project.user._id === state.user._id) {\r\n            return true;\r\n          }\r\n        }\r\n      }\r\n    }\r\n    return false;\r\n  },\r\n  wrapperDisplayName: 'CanEditProjectForm',\r\n  FailureComponent: () => <R403/>\r\n});\r\n\r\nexport const canViewDiscussionUpdate = connectedAuthWrapper({\r\n  authenticatedSelector: (state) => {\r\n    if (state.permissions && state.permissions.update && state.permissions.update['read:any']) {\r\n      return true;\r\n    }\r\n    return false;\r\n  },\r\n  wrapperDisplayName: 'CanViewDiscussionUpdate',\r\n});\r\n\r\nexport default canAddProject;", "//Import Library\r\nimport React from 'react'\r\nimport DataTable  from 'react-data-table-component';\r\n\r\n//Import services/components\r\nimport { useTranslation } from 'next-i18next';\r\nimport CustomLoader from './CustomLoader';\r\n\r\ninterface RKITableProps {\r\n  columns: any[];\r\n  data: any[];\r\n  totalRows?: number;\r\n  resetPaginationToggle?: boolean;\r\n  subheader?: boolean;\r\n  subHeaderComponent?: React.ReactNode;\r\n  handlePerRowsChange?: (newPerPage: number, page: number) => void;\r\n  handlePageChange?: (page: number) => void;\r\n  rowsPerPage?: number[];\r\n  defaultRowsPerPage?: number;\r\n  selectableRows?: boolean;\r\n  loading?: boolean;\r\n  pagServer?: boolean;\r\n  onSelectedRowsChange?: (selectedRows: any) => void;\r\n  clearSelectedRows?: boolean;\r\n  sortServer?: boolean;\r\n  onSort?: (column: any, sortDirection: string) => void;\r\n  persistTableHead?: boolean;\r\n  sortFunction?: (rows: any[], field: any, direction: 'asc' | 'desc') => any[];\r\n  noHeader?: boolean;\r\n  dense?: boolean;\r\n  pagination?: boolean;\r\n  paginationServer?: boolean;\r\n  paginationTotalRows?: number;\r\n  subHeaderAlign?: string;\r\n  [key: string]: any;\r\n}\r\n\r\nfunction RKITable(props: RKITableProps) {\r\n  const { t } = useTranslation('common');\r\n  const paginationComponentOptions = {\r\n    rowsPerPageText: t('Rowsperpage'),\r\n    };\r\n  const {\r\n    columns,\r\n    data,\r\n    totalRows,\r\n    resetPaginationToggle,\r\n    subheader,\r\n    subHeaderComponent,\r\n    handlePerRowsChange,\r\n    handlePageChange,\r\n    rowsPerPage,\r\n    defaultRowsPerPage,\r\n    selectableRows,\r\n    loading,\r\n    pagServer,\r\n    onSelectedRowsChange,\r\n    clearSelectedRows,\r\n    sortServer,\r\n    onSort,\r\n    persistTableHead,\r\n    sortFunction,\r\n    // Filter out any unknown props that might cause DOM warnings\r\n    ...otherProps\r\n  } = props;\r\n\r\n  // Only pass known DataTable props to avoid DOM warnings\r\n  const dataTableProps = {\r\n    paginationComponentOptions,\r\n    noDataComponent: t(\"NoData\"),\r\n    noHeader: true,\r\n    columns,\r\n    data: data || [],\r\n    dense: true,\r\n    paginationResetDefaultPage: resetPaginationToggle,\r\n    subHeader: subheader,\r\n    progressPending: loading,\r\n    subHeaderComponent,\r\n    pagination: true,\r\n    paginationServer: pagServer,\r\n    paginationPerPage: defaultRowsPerPage ? defaultRowsPerPage : 10,\r\n    paginationRowsPerPageOptions: rowsPerPage ? rowsPerPage : [10, 15, 20, 25, 30],\r\n    paginationTotalRows: totalRows,\r\n    onChangeRowsPerPage: handlePerRowsChange,\r\n    onChangePage: handlePageChange,\r\n    selectableRows,\r\n    onSelectedRowsChange,\r\n    clearSelectedRows,\r\n    progressComponent: <CustomLoader/>,\r\n    sortIcon: <i className=\"sort-icon fas fa-exchange-alt\" />,\r\n    sortServer,\r\n    onSort,\r\n    sortFunction,\r\n    persistTableHead,\r\n    className: \"rki-table\"\r\n  };\r\n  return (\r\n    <DataTable {...dataTableProps} />\r\n  )\r\n};\r\n\r\nRKITable.defaultProps = {\r\n  subHeader: false,\r\n  pagination: true,\r\n  totalRows: null,\r\n  pagServer: true,\r\n  onSelectedRowsChange: null,\r\n  clearSelectedRows: false,\r\n  sortServer: false,\r\n  persistTableHead: false\r\n}\r\n\r\nexport default RKITable;\r\n\r\n", "import { InfoWindow } from '@react-google-maps/api';\r\n\r\ninterface Props {\r\n  position: google.maps.LatLngLiteral;\r\n  onCloseClick?: () => void;\r\n  children?: React.ReactNode;\r\n}\r\n\r\nconst RKIMapInfowindow = ({ position, onCloseClick, children }: Props) => {\r\n  return (\r\n    <InfoWindow position={position} onCloseClick={onCloseClick}>\r\n      <div>{children}</div>\r\n    </InfoWindow>\r\n  );\r\n};\r\n\r\nexport default RKIMapInfowindow;\r\n", "const fill = \"labels.text.fill\"\r\nconst stoke = \"labels.text.stroke\"\r\nconst road = \"road.highway\"\r\nconst geometry = \"geometry.stroke\"\r\nconst mapStyles = [\r\n  {\r\n    \"elementType\": \"geometry\",\r\n    \"stylers\": [\r\n      {\r\n        \"color\": \"#1d2c4d\"\r\n      }\r\n    ]\r\n  },\r\n  {\r\n    \"elementType\": fill,\r\n    \"stylers\": [\r\n      {\r\n        \"color\": \"#8ec3b9\"\r\n      }\r\n    ]\r\n  },\r\n  {\r\n    \"elementType\": stoke,\r\n    \"stylers\": [\r\n      {\r\n        \"color\": \"#1a3646\"\r\n      }\r\n    ]\r\n  },\r\n  {\r\n    \"featureType\": \"administrative\",\r\n    \"elementType\": \"geometry\",\r\n    \"stylers\": [\r\n      {\r\n        \"visibility\": \"off\"\r\n      }\r\n    ]\r\n  },\r\n  {\r\n    \"featureType\": \"administrative.country\",\r\n    \"elementType\": geometry,\r\n    \"stylers\": [\r\n      {\r\n        \"color\": \"#4b6878\"\r\n      }\r\n    ]\r\n  },\r\n  {\r\n    \"featureType\": \"administrative.land_parcel\",\r\n    \"elementType\": \"labels\",\r\n    \"stylers\": [\r\n      {\r\n        \"visibility\": \"off\"\r\n      }\r\n    ]\r\n  },\r\n  {\r\n    \"featureType\": \"administrative.land_parcel\",\r\n    \"elementType\": fill,\r\n    \"stylers\": [\r\n      {\r\n        \"color\": \"#64779e\"\r\n      }\r\n    ]\r\n  },\r\n  {\r\n    \"featureType\": \"administrative.province\",\r\n    \"elementType\": geometry,\r\n    \"stylers\": [\r\n      {\r\n        \"color\": \"#4b6878\"\r\n      }\r\n    ]\r\n  },\r\n  {\r\n    \"featureType\": \"landscape.man_made\",\r\n    \"elementType\": geometry,\r\n    \"stylers\": [\r\n      {\r\n        \"color\": \"#334e87\"\r\n      }\r\n    ]\r\n  },\r\n  {\r\n    \"featureType\": \"landscape.natural\",\r\n    \"elementType\": \"geometry\",\r\n    \"stylers\": [\r\n      {\r\n        \"color\": \"#023e58\"\r\n      }\r\n    ]\r\n  },\r\n  {\r\n    \"featureType\": \"poi\",\r\n    \"stylers\": [\r\n      {\r\n        \"visibility\": \"off\"\r\n      }\r\n    ]\r\n  },\r\n  {\r\n    \"featureType\": \"poi\",\r\n    \"elementType\": \"geometry\",\r\n    \"stylers\": [\r\n      {\r\n        \"color\": \"#283d6a\"\r\n      }\r\n    ]\r\n  },\r\n  {\r\n    \"featureType\": \"poi\",\r\n    \"elementType\": \"labels.text\",\r\n    \"stylers\": [\r\n      {\r\n        \"visibility\": \"off\"\r\n      }\r\n    ]\r\n  },\r\n  {\r\n    \"featureType\": \"poi\",\r\n    \"elementType\": fill,\r\n    \"stylers\": [\r\n      {\r\n        \"color\": \"#6f9ba5\"\r\n      }\r\n    ]\r\n  },\r\n  {\r\n    \"featureType\": \"poi\",\r\n    \"elementType\": stoke,\r\n    \"stylers\": [\r\n      {\r\n        \"color\": \"#1d2c4d\"\r\n      }\r\n    ]\r\n  },\r\n  {\r\n    \"featureType\": \"poi.park\",\r\n    \"elementType\": \"geometry.fill\",\r\n    \"stylers\": [\r\n      {\r\n        \"color\": \"#023e58\"\r\n      }\r\n    ]\r\n  },\r\n  {\r\n    \"featureType\": \"poi.park\",\r\n    \"elementType\": fill,\r\n    \"stylers\": [\r\n      {\r\n        \"color\": \"#3C7680\"\r\n      }\r\n    ]\r\n  },\r\n  {\r\n    \"featureType\": \"road\",\r\n    \"stylers\": [\r\n      {\r\n        \"visibility\": \"off\"\r\n      }\r\n    ]\r\n  },\r\n  {\r\n    \"featureType\": \"road\",\r\n    \"elementType\": \"geometry\",\r\n    \"stylers\": [\r\n      {\r\n        \"color\": \"#304a7d\"\r\n      }\r\n    ]\r\n  },\r\n  {\r\n    \"featureType\": \"road\",\r\n    \"elementType\": \"labels.icon\",\r\n    \"stylers\": [\r\n      {\r\n        \"visibility\": \"off\"\r\n      }\r\n    ]\r\n  },\r\n  {\r\n    \"featureType\": \"road\",\r\n    \"elementType\": fill,\r\n    \"stylers\": [\r\n      {\r\n        \"color\": \"#98a5be\"\r\n      }\r\n    ]\r\n  },\r\n  {\r\n    \"featureType\": \"road\",\r\n    \"elementType\": stoke,\r\n    \"stylers\": [\r\n      {\r\n        \"color\": \"#1d2c4d\"\r\n      }\r\n    ]\r\n  },\r\n  {\r\n    \"featureType\": road,\r\n    \"elementType\": \"geometry\",\r\n    \"stylers\": [\r\n      {\r\n        \"color\": \"#2c6675\"\r\n      }\r\n    ]\r\n  },\r\n  {\r\n    \"featureType\": road,\r\n    \"elementType\": geometry,\r\n    \"stylers\": [\r\n      {\r\n        \"color\": \"#255763\"\r\n      }\r\n    ]\r\n  },\r\n  {\r\n    \"featureType\": road,\r\n    \"elementType\": fill,\r\n    \"stylers\": [\r\n      {\r\n        \"color\": \"#b0d5ce\"\r\n      }\r\n    ]\r\n  },\r\n  {\r\n    \"featureType\": road,\r\n    \"elementType\": stoke,\r\n    \"stylers\": [\r\n      {\r\n        \"color\": \"#023e58\"\r\n      }\r\n    ]\r\n  },\r\n  {\r\n    \"featureType\": \"road.local\",\r\n    \"elementType\": \"labels\",\r\n    \"stylers\": [\r\n      {\r\n        \"visibility\": \"off\"\r\n      }\r\n    ]\r\n  },\r\n  {\r\n    \"featureType\": \"transit\",\r\n    \"stylers\": [\r\n      {\r\n        \"visibility\": \"off\"\r\n      }\r\n    ]\r\n  },\r\n  {\r\n    \"featureType\": \"transit\",\r\n    \"elementType\": fill,\r\n    \"stylers\": [\r\n      {\r\n        \"color\": \"#98a5be\"\r\n      }\r\n    ]\r\n  },\r\n  {\r\n    \"featureType\": \"transit\",\r\n    \"elementType\": stoke,\r\n    \"stylers\": [\r\n      {\r\n        \"color\": \"#1d2c4d\"\r\n      }\r\n    ]\r\n  },\r\n  {\r\n    \"featureType\": \"transit.line\",\r\n    \"elementType\": \"geometry.fill\",\r\n    \"stylers\": [\r\n      {\r\n        \"color\": \"#283d6a\"\r\n      }\r\n    ]\r\n  },\r\n  {\r\n    \"featureType\": \"transit.station\",\r\n    \"elementType\": \"geometry\",\r\n    \"stylers\": [\r\n      {\r\n        \"color\": \"#3a4762\"\r\n      }\r\n    ]\r\n  },\r\n  {\r\n    \"featureType\": \"water\",\r\n    \"elementType\": \"geometry\",\r\n    \"stylers\": [\r\n      {\r\n        \"color\": \"#0e1626\"\r\n      }\r\n    ]\r\n  },\r\n  {\r\n    \"featureType\": \"water\",\r\n    \"elementType\": fill,\r\n    \"stylers\": [\r\n      {\r\n        \"color\": \"#4e6d70\"\r\n      }\r\n    ]\r\n  }\r\n];\r\n\r\nexport default mapStyles;", "import React from 'react';\r\nimport { GoogleMap, useJsApiLoader } from '@react-google-maps/api';\r\nimport RKIMapInfowindow from './RKIMapInfowindow';\r\nimport mapStyles from './mapStyles';\r\nimport { useRouter } from \"next/router\";\r\nimport { useGoogleMaps } from './GoogleMapsProvider';\r\n\r\ninterface RKIMap1Props {\r\n  markerInfo?: React.ReactNode;\r\n  activeMarker?: any;\r\n  initialCenter?: { lat: number; lng: number };\r\n  children?: React.ReactNode;\r\n  height?: number | string;\r\n  width?: string;\r\n  language?: string;\r\n  points?: any[];\r\n  zoom?: number;\r\n  minZoom?: number;\r\n  onClose?: () => void\r\n}\r\n\r\nconst RKIMap1: React.FC<RKIMap1Props> = ({\r\n  markerInfo,\r\n  activeMarker,\r\n  initialCenter,\r\n  children,\r\n  height = 300,\r\n  width = \"114%\",\r\n  language,\r\n  zoom = 1,\r\n  minZoom = 1,\r\n  onClose\r\n}) => {\r\n  const { locale } = useRouter();\r\n  const { isLoaded, loadError } =  useGoogleMaps();\r\n  const containerStyle = {\r\n    width: width,\r\n    height: typeof height === 'number' ? `${height}px` : height,\r\n  };\r\n\r\n  const defaultCenter = {\r\n    lat: 52.520017,\r\n    lng: 13.404195,\r\n  };\r\n\r\n  const center = initialCenter || defaultCenter;\r\n\r\n  const onMapLoad = (map: google.maps.Map) => {\r\n    map.setOptions({\r\n      styles: mapStyles,\r\n    });\r\n  };\r\n\r\n  if (loadError) return <div>Error loading maps</div>;\r\n  if (!isLoaded) return <div>Loading Maps...</div>;\r\n\r\n  return (\r\n    <div className=\"map-container\">\r\n      <div className=\"mapprint\" style={{ width, height, position: 'relative' }}>\r\n        <GoogleMap\r\n          mapContainerStyle={containerStyle}\r\n          center={center}\r\n          zoom={zoom}\r\n          onLoad={onMapLoad}\r\n          options={{\r\n            minZoom: minZoom,\r\n            draggable: true,\r\n            keyboardShortcuts: false,\r\n            streetViewControl: false,\r\n            panControl: false,\r\n            clickableIcons: false,\r\n            mapTypeControl: false,\r\n            fullscreenControl: true,\r\n          }}\r\n        >\r\n          {children}\r\n          {markerInfo && activeMarker && activeMarker.getPosition && (\r\n            <RKIMapInfowindow\r\n              position={activeMarker.getPosition()}\r\n              onCloseClick={() => {\r\n                // Handle close if needed\r\n                console.log('close click');\r\n                onClose?.()\r\n              }}\r\n            >\r\n              {markerInfo}\r\n            </RKIMapInfowindow>\r\n          )}\r\n        </GoogleMap>\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default RKIMap1;\r\n", "\n    (window.__NEXT_P = window.__NEXT_P || []).push([\n      \"/project\",\n      function () {\n        return require(\"private-next-pages/project/index.tsx\");\n      }\n    ]);\n    if(module.hot) {\n      module.hot.dispose(function () {\n        window.__NEXT_P.push([\"/project\"])\n      });\n    }\n  ", "//Import Library\r\nimport React, { useState, useEffect } from 'react';\r\nimport _ from 'lodash';\r\nimport { Form, Button } from \"react-bootstrap\";\r\n\r\n//Import services/components\r\nimport apiService from '../../services/apiService';\r\nimport { useTranslation } from 'next-i18next';\r\n\r\n// Define types for region items\r\ninterface RegionItem {\r\n  _id: string;\r\n  code: string;\r\n  title: string;\r\n  isChecked: boolean;\r\n}\r\n\r\ninterface RegionsMultiCheckboxesProps {\r\n  regionHandler: (regions: string[]) => void;\r\n  selectedRegions: string[];\r\n  filtreg: (regions: string[]) => void;\r\n}\r\n\r\nfunction RegionsMultiCheckboxes(props: RegionsMultiCheckboxesProps) {\r\n  const {filtreg} = props;\r\n  const [allregions, setAllregions] = useState(true);\r\n  const [region, setRegion] = useState<RegionItem[]>([]);\r\n  const [selectedRegions, setSelectedRegions] = useState<string[]>([]);\r\n  const { t } = useTranslation('common');\r\n  const RegionParams = {\r\n    \"query\": {},\r\n    \"limit\": \"~\",\r\n    \"sort\": { \"title\": \"asc\" }\r\n  };\r\n\r\n  const getworldregion = async (RegionParams_initial: typeof RegionParams) => {\r\n    const response = await apiService.get('/worldregion', RegionParams_initial);\r\n    if (response && Array.isArray(response.data)) {\r\n      const finalRegions: RegionItem[] = [];\r\n      const selectedIds: string[] = [];\r\n\r\n      _.each(response.data, (item, _) => {\r\n        const regionItem: RegionItem = {\r\n          ...item,\r\n          isChecked: true\r\n        };\r\n        finalRegions.push(regionItem);\r\n        selectedIds.push(item._id);\r\n      });\r\n\r\n      filtreg(selectedIds);\r\n      setSelectedRegions(selectedIds);\r\n      setRegion(finalRegions);\r\n    }\r\n  };\r\n\r\n  useEffect(() => {\r\n    getworldregion(RegionParams);\r\n  }, [])\r\n\r\n  const handleAllChecked = (event: React.ChangeEvent<HTMLInputElement>) => {\r\n    const updatedRegions = region.map(item => ({\r\n      ...item,\r\n      isChecked: event.target.checked\r\n    }));\r\n\r\n    let selected_Regions: string[] = [];\r\n    if (event.target.checked) {\r\n      selected_Regions = updatedRegions.map(item => item._id);\r\n    }\r\n\r\n    filtreg(selected_Regions);\r\n    setSelectedRegions(selected_Regions);\r\n    setAllregions(event.target.checked);\r\n    setRegion(updatedRegions);\r\n  };\r\n\r\n  const handleIndividualRegionChange = (e: React.ChangeEvent<HTMLInputElement>) => {\r\n    const updatedRegions = [...region];\r\n    let updatedSelectedRegions = [...selectedRegions];\r\n\r\n    updatedRegions.forEach((item, index) => {\r\n      if (item.code === e.target.id) {\r\n        updatedRegions[index].isChecked = e.target.checked;\r\n        if (!e.target.checked) {\r\n          updatedSelectedRegions = updatedSelectedRegions.filter(n => n !== item._id);\r\n        } else {\r\n          updatedSelectedRegions.push(item._id);\r\n        }\r\n      }\r\n    });\r\n\r\n    setSelectedRegions(updatedSelectedRegions);\r\n    filtreg(updatedSelectedRegions);\r\n    setAllregions(false);\r\n    setRegion(updatedRegions);\r\n  };\r\n\r\n  const resetAllRegion = () => {\r\n    const updatedRegions = region.map(item => ({\r\n      ...item,\r\n      isChecked: false\r\n    }));\r\n\r\n    setSelectedRegions([]);\r\n    setAllregions(false);\r\n    setRegion(updatedRegions);\r\n    filtreg([]);\r\n  };\r\n\r\n  return (\r\n    <div className=\"regions-multi-checkboxes\">\r\n      <Form.Check\r\n        type=\"checkbox\"\r\n        id={`all`}\r\n        label={t(\"AllRegions\")}\r\n        checked={allregions}\r\n        onChange={handleAllChecked}\r\n      />\r\n      {region.map((item, index) => {\r\n        return (\r\n          <Form.Check\r\n            key={index}\r\n            type=\"checkbox\"\r\n            id={item.code}\r\n            label={item.title}\r\n            value={item.code}\r\n            onChange={handleIndividualRegionChange}\r\n            checked={region[index].isChecked}\r\n          />\r\n        )\r\n      })}\r\n      <Button onClick={resetAllRegion} className=\"btn-plain ps-2\">{t(\"ClearAll\")}</Button>\r\n    </div>\r\n  )\r\n}\r\n\r\nRegionsMultiCheckboxes.defaultProps = {\r\n  filtreg: () => {\"\"}\r\n}\r\n\r\nexport default RegionsMultiCheckboxes;\r\n", "interface PageHeadingProps {\r\n  title: string; // Required based on actual usage\r\n}\r\n\r\nexport default function PageHeading(props: PageHeadingProps) {\r\n  return (\r\n    <h2 className=\"page-heading\">{props.title}</h2>\r\n  )\r\n}\r\n"], "names": ["name", "id", "R<PERSON>IMapMarker", "countryId", "type", "icon", "position", "onClick", "title", "draggable", "lat", "lng", "<PERSON><PERSON>", "handleClick", "markerProps", "marker", "getPosition", "e", "filterText", "ProjectsTableFilter", "onFilter", "onFilterStatusChange", "onClear", "filterStatus", "status", "setStatus", "useState", "t", "useTranslation", "getProjectStatus", "projectParams", "response", "apiService", "get", "Array", "isArray", "data", "useEffect", "query", "sort", "Container", "fluid", "className", "Row", "Col", "xs", "FormControl", "placeholder", "aria-label", "value", "onChange", "Form", "Group", "as", "controlId", "Label", "column", "sm", "lg", "option", "map", "item", "index", "_id", "partner", "CountryLink", "partner_institutions", "length", "ul", "partner_country", "li", "Link", "href", "ProjectsTable", "router", "useRouter", "setProjects", "selectedRegions", "props", "setFilterText", "React", "setFilterStatus", "resetPaginationToggle", "setResetPaginationToggle", "tabledata", "setDataToTable", "loading", "setLoading", "totalRows", "setTotalRows", "perPage", "setPerPage", "pageNum", "setPageNum", "pageSort", "setPageSort", "created_at", "lean", "limit", "page", "populate", "path", "select", "projP<PERSON><PERSON>", "setProjParams", "columns", "selector", "sortable", "cell", "d", "area_of_work", "join", "getProjectsData", "projectParamsinitial", "country", "totalCount", "handlePerRowsChange", "newPerPage", "handleSort", "sortDirection", "<PERSON><PERSON><PERSON><PERSON>", "q", "handleSearchTitle", "useRef", "_", "Number", "process", "current", "subHeaderComponentMemo", "useMemo", "handleFilterStatusChange", "handleChange", "target", "handleClear", "RKITable", "subheader", "persistTableHead", "onSort", "sortServer", "pagServer", "subHeaderComponent", "handlePageChange", "i18n", "ListMapContainer", "currentLang", "language", "projects", "points", "setPoints", "activeMarker", "set<PERSON><PERSON><PERSON><PERSON>", "markerInfo", "setMarkerInfo", "groupedProjects", "setGroupedProjects", "reset<PERSON><PERSON><PERSON>", "onMarkerClick", "propsinitial", "part", "pointer", "coordinates", "part1", "getpointer", "projectParterner", "project", "console", "log", "push", "parseFloat", "latitude", "longitude", "world_region", "setPointsFromProjects", "filterProjectpoints", "filteredPoints", "point", "includes", "getProjectsGroupBCountry", "countriesList", "pi", "RKIMAP1", "onClose", "MarkerInfo", "info", "Markerprops", "a", "url", "Project", "setSelectedRegions", "AddProjectComponent", "<PERSON><PERSON>", "variant", "size", "CanAddProject", "canAddProject", "regionHandler", "val", "PageHeading", "RegionsMultiCheckboxes", "filtreg", "connectedAuthWrapper", "authenticatedSelector", "state", "permissions", "wrapperDisplayName", "FailureComponent", "R403", "user", "update", "paginationComponentOptions", "rowsPerPageText", "rowsPerPage", "defaultRowsPerPage", "selectableRows", "onSelectedRowsChange", "clearSelectedRows", "sortFunction", "otherProps", "dataTableProps", "noDataComponent", "<PERSON><PERSON><PERSON><PERSON>", "dense", "paginationResetDefaultPage", "subHeader", "progressPending", "pagination", "paginationServer", "paginationPerPage", "paginationRowsPerPageOptions", "paginationTotalRows", "onChangeRowsPerPage", "onChangePage", "progressComponent", "CustomLoader", "sortIcon", "i", "DataTable", "defaultProps", "RKIMapInfowindow", "onCloseClick", "children", "InfoWindow", "div", "fill", "stoke", "road", "geometry", "mapStyles", "RKIMap1", "initialCenter", "height", "width", "zoom", "minZoom", "locale", "isLoaded", "loadError", "useGoogleMaps", "style", "GoogleMap", "mapContainerStyle", "center", "defaultCenter", "onLoad", "onMapLoad", "setOptions", "styles", "options", "keyboardShortcuts", "streetViewControl", "panControl", "clickableIcons", "mapTypeControl", "fullscreenControl", "allregions", "setAllregions", "region", "setRegion", "RegionParams", "getworldregion", "RegionParams_initial", "finalRegions", "selectedIds", "regionItem", "isChecked", "handleIndividualRegionChange", "updatedRegions", "updatedSelectedRegions", "for<PERSON>ach", "code", "checked", "filter", "n", "Check", "label", "event", "handleAllChecked", "selected_Regions", "resetAllRegion", "h2"], "sourceRoot": "", "ignoreList": []}