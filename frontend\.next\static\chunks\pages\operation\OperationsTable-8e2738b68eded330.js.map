{"version": 3, "file": "static/chunks/pages/operation/OperationsTable-8e2738b68eded330.js", "mappings": "kOAmFA,MA1E8B,OAAC,YAC7BA,CAAU,QAyEGC,EAxEbC,CAAQ,kBAwE0BD,EAAC,EAvEnCE,CAAoB,SACpBC,CAAO,cACPC,CAAY,CAOb,GACO,CAACC,EAAQC,EAAU,CAAGC,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAC,EAAE,EACjC,GAAEC,CAAC,CAAE,CAAGC,CAAAA,EAAAA,EAAAA,EAAAA,CAAcA,CAAC,UAEvBC,EAAsB,MAAOC,IACjC,IAAMC,EAAW,MAAMC,EAAAA,CAAUA,CAACC,GAAG,CAAC,oBAAqBH,GACvDC,GAAYG,MAAMC,OAAO,CAACJ,EAASK,IAAI,GACzCX,EAAUM,EAASK,IAAI,CAE3B,EASA,MAPAC,CAAAA,EAAAA,EAAAA,SAAAA,CAASA,CAAC,KACRR,EAAoB,CAClBS,MAAO,CAAC,EACRC,KAAM,CAAEC,MAAO,KAAM,CACvB,EACF,EAAG,EAAE,EAGH,UAACC,EAAAA,CAASA,CAAAA,CAACC,KAAK,IAACC,UAAU,eACzB,WAACC,EAAAA,CAAGA,CAAAA,WACF,UAACC,EAAAA,CAAGA,CAAAA,CAACC,GAAI,EAAGH,UAAU,oCACpB,UAACI,EAAAA,CAAWA,CAAAA,CACVC,KAAK,OACLL,UAAU,cACVM,YAAatB,EAAE,UACfuB,aAAW,SACXC,MAAOjC,EACPkC,SAAUhC,MAGd,UAACyB,EAAAA,CAAGA,CAAAA,CAACC,GAAI,WACP,UAACO,EAAAA,CAAIA,CAAAA,UACH,WAACA,EAAAA,CAAIA,CAACC,KAAK,EAACC,GAAIX,EAAAA,CAAGA,CAAEY,UAAU,yBAC7B,UAACH,EAAAA,CAAIA,CAACI,KAAK,EAACC,MAAM,IAACC,GAAG,IAAIC,GAAG,aAC5BjC,EAAE,YAEH,UAACkB,EAAAA,CAAGA,CAAAA,CAACF,UAAU,qBACb,WAACI,EAAAA,CAAWA,CAAAA,CACVQ,GAAG,SACHL,aAAW,SACXE,SAAU/B,EACV8B,MAAO5B,YAEP,UAACsC,SAAAA,CAAOV,MAAO,YAAI,QAClB3B,EAAOsC,GAAG,CAAC,CAACC,EAAWC,IAEpB,UAACH,SAAAA,CAAmBV,MAAOY,EAAKE,GAAG,UAChCF,EAAKvB,KAAK,EADAwB,oBAanC,6GC5CA,SAASE,EAASC,CAAoB,EACpC,GAAM,CAAExC,CAAC,CAAE,CAAGC,CAAAA,EAAAA,EAAAA,EAAAA,CAAcA,CAAC,UACvBwC,EAA6B,CACjCC,gBAAiB1C,EAAE,cACnB,EACI,SACJ2C,CAAO,MACPlC,CAAI,WACJmC,CAAS,uBACTC,CAAqB,WACrBC,CAAS,oBACTC,CAAkB,qBAClBC,CAAmB,kBACnBC,CAAgB,aAChBC,CAAW,oBACXC,CAAkB,gBAClBC,CAAc,SACdC,CAAO,WACPC,CAAS,sBACTC,CAAoB,mBACpBC,CAAiB,YACjBC,CAAU,QACVC,CAAM,kBACNC,CAAgB,cAChBC,CAAY,CAEZ,CADA,EACGC,EACJ,CAAGrB,EAGEsB,EAAiB,4BACrBrB,EACAsB,gBAAiB/D,EAAE,IAP0C,MAQ7DgE,UAAU,UACVrB,EACAlC,KAAMA,GAAQ,EAAE,CAChBwD,MAAO,GACPC,2BAA4BrB,EAC5BsB,UAAWrB,EACXsB,gBAAiBf,qBACjBN,EACAsB,YAAY,EACZC,iBAAkBhB,EAClBiB,kBAAmBpB,GAA0C,GAC7DqB,eADwCrB,cACVD,GAA4B,CAAC,GAAI,GAAI,GAAI,CAA3BA,EAA+B,GAAG,CAC9EuB,oBAAqB7B,EACrB8B,oBAAqB1B,EACrB2B,aAAc1B,iBACdG,uBACAG,oBACAC,EACAoB,kBAAmB,UAACC,EAAAA,CAAYA,CAAAA,CAAAA,GAChCC,SAAU,UAACC,IAAAA,CAAE/D,UAAU,kCACvByC,oBACAC,EACAE,gCACAD,EACA3C,UAAW,WACb,EACA,MACE,UAACgE,EAAAA,EAASA,CAAAA,CAAE,GAAGlB,CAAc,EAEjC,CAEAvB,EAAS0C,YAAY,CAAG,CACtBd,WAAW,EACXE,WAAY,GACZzB,UAAW,KACXU,WAAW,EACXC,qBAAsB,KACtBC,mBAAmB,EACnBC,YAAY,EACZE,kBAAkB,CACpB,EAEA,MAAepB,QAAQA,EAAC,SC/GxB,4CACA,6BACA,WACA,OAAe,EAAQ,KAAkD,CACzE,EACA,SAFsB,kMCStB,IAAM2C,EAAe,OAAC,UAAEC,CAAQ,CAAO,UACrC,GAAgBA,EAASC,MAAM,CAAG,EAE9B,UAACC,KAAAA,UACEF,EAAShD,GAAG,CAAC,CAACC,EAAWC,KACxB,GAAID,EAAKkD,WAAW,CAClB,CADoB,KAElB,UAACC,KAAAA,UACC,UAACC,IAAIA,CACHC,KAAK,2BACL7D,GAAI,SAFD4D,YAE2C,OAArBpD,EAAKkD,WAAW,CAAChD,GAAG,WAE5CF,EAAKkD,WAAW,CAACzE,KAAK,IALlBwB,EAUf,KAIC,IACT,EA0PA,EAxPA,SAASqD,CAA0B,EACjC,GAAM,CAAE1F,CAAC,CAAE,CAAGC,CAAAA,EAAAA,EAAAA,EAAAA,CAAcA,CAAC,UACvB0F,EAASC,CAAAA,EAAAA,EAAAA,SAAAA,CAASA,GAClB,eAAEC,CAAa,iBAAEC,CAAe,CAAE,CAAGtD,EACrC,CAACjD,EAAYwG,EAAc,CAAGhG,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAC,IACvC,CAACH,EAAcoG,EAAgB,CAAGjG,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAC,IAC3C,CAAC8C,EAAuBoD,EAAyB,CAAGlG,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,EAAC,GAC7D,CAACmG,EAAWC,EAAe,CAAGpG,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAC,EAAE,EACzC,CAACsD,EAAS+C,EAAW,CAAGrG,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,EAAC,GACjC,CAAC6C,EAAWyD,EAAa,CAAGtG,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAC,GACrC,CAACuG,EAASC,EAAW,CAAGxG,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAC,IACjC,CAACyG,EAASC,EAAW,CAAG1G,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAC,GACjC,CAAC2G,EAAUC,EAAY,CAAG5G,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAM,MAGxCI,EAAuB,CAC3BS,KAAM,CAAEgG,WAAY,MAAO,EAC3BC,MAAM,EACNC,SAAU,CACR,CAAEC,KAAM,kBAAmBC,OAAQ,OAAQ,EAC3C,CAAED,KAAM,uBAAwBC,OAAQ,OAAQ,EAChD,CAAED,KAAM,SAAUC,OAAQ,OAAQ,EAClC,CAAED,KAAM,UAAWC,OAAQ,aAAc,EAC1C,CACDC,MAAOX,EACPY,KAAM,EACNvG,MAAO,CAAC,EACRqG,OACE,iGACJ,EACM,CAACG,EAAUC,EAAY,CAAGrH,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAACI,GAEnCwC,EAAU,CACd,CACE0E,KAAMrH,EAAE,cACRsH,SAAU,QACVC,UAAU,EACVC,KAAM,GACJ,UAAChC,IAAIA,CAACC,KAAK,yBAAyB7D,GAAI,WAAnC4D,QAA4D,OAANiC,EAAEnF,GAAG,WAC7DmF,EAAE5G,KAAK,EAGd,EACA,CACEwG,KAAMrH,EAAE,UACRsH,SAAU,SACVC,UAAU,EACVC,KAAM,GAAYC,EAAE5H,MAAM,EAAI4H,EAAE5H,MAAM,CAACgB,KAAK,CAAG4G,EAAE5H,MAAM,CAACgB,KAAK,CAAG,EAClE,EACA,CACEwG,KAAMrH,EAAE,aACRsH,SAAU,aACVC,UAAU,EACVC,KAAM,GACJC,GAAKA,EAAEC,UAAU,CAAGC,IAAOF,EAAEC,UAAU,EAAEE,MAAM,CAAC,SAAW,EAC/D,EACA,CACEP,KAAMrH,CAHsB2H,CAGpB,YACRL,SAAU,WACVE,KAAM,GAAY,UAACtC,EAAAA,CAAaC,SAAUsC,EAAEtC,QAAQ,EACtD,EACD,CAIK0C,EAAoB,MAAOC,IAC/B1B,GAAW,GAEPT,EAAOhF,KAAK,EAAIgF,EAAOhF,KAAK,CAACoH,OAAO,EAAE,CACxCD,EAAoBnH,KAAK,CAAC,OAAU,CAAGgF,EAAOhF,KAAK,CAACoH,OAAAA,EAI9B,MAAM,CAA1BjC,EAEF,OAAOgC,EAAoBnH,KAAK,CAAC,YAAe,CACvCmF,GAA8B,GAAdV,MAAM,CAE/B0C,EAAoBnH,KAAK,CAAC,YAAe,CAAG,CAAC,eAAe,CAG5DmH,CAH8D,CAG1CnH,KAAK,CAAC,OAHkD,KAGnC,CAAGmF,EAG9C,IAAM1F,EAAW,MAAMC,EAAAA,CAAUA,CAACC,GAAG,CAAC,aAAcwH,GAChD1H,GAAYG,MAAMC,OAAO,CAACJ,EAASK,IAAI,GAAG,CAC5C0F,EAAe/F,EAASK,IAAI,EAC5BoF,EAAczF,EAASK,IAAI,EAC3B4F,EAAajG,EAAS4H,UAAU,EAChC5B,GAAW,GAEf,EA4BMpD,EAAsB,MAAOiF,EAAiBf,KAClD/G,EAAgB8G,KAAK,CAAGgB,EACxB9H,EAAgB+G,IAAI,CAAGA,EACvBd,GAAW,GAEPT,EAAOhF,KAAK,EAAIgF,EAAOhF,KAAK,CAACoH,OAAO,EAAE,CACxC5H,EAAgBQ,KAAK,CAAC,OAAU,CAAGgF,EAAOhF,KAAK,CAACoH,OAAAA,EAI1B,MAAM,CAA1BjC,EACF,OAAO3F,EAAgBQ,KAAK,CAAC,YAAe,CACnCmF,GAA8B,GAAdV,MAAM,CAC/BjF,EAAgBQ,KAAK,CAAC,YAAe,CAAG,CAAC,eAAe,CAExDR,EAAgBQ,KAAK,CAAC,YAAe,CAAGmF,EAG1ClG,IACGO,EAAgBQ,KAAK,CAAG,CACvB,GADDR,EACoBQ,KAAK,CACxBd,OAAQD,EACV,EAEF8G,IAAavG,EAAgBS,IAAI,CAAG8F,CAAvBvG,CAAgCS,IAAAA,EAE7C,IAAMR,EAAW,MAAMC,EAAAA,CAAUA,CAACC,GAAG,CAAC,aAAcH,GAChDC,GAAYG,MAAMC,OAAO,CAACJ,EAASK,IAAI,GAAG,CAC5C0F,EAAe/F,EAASK,IAAI,EAC5BoF,EAAczF,EAASK,IAAI,EAC3B8F,EAAW0B,GACX7B,GAAW,IAEbK,EAAWS,EACb,EAGAxG,CAAAA,EAAAA,EAAAA,SAAAA,CAASA,CAAC,KACRyG,EAASD,IAAI,CAAG,EAChBW,EAAkBV,EACpB,EAAG,CAACrB,EAAiBH,EAAO,EAE5BjF,CAAAA,EAAAA,EAAAA,SAAAA,CAASA,CAAC,KACRmH,EAAkBV,EACpB,EAAG,CAACA,EAAS,EAGb,CAHiB,GAGXe,EAAa,MAAOnG,EAAaoG,KACrC/B,GAAW,GACXjG,EAAgBS,IAAI,CAAG,CACrB,CAACmB,EAAOuF,QAAQ,CAAC,CAAEa,CACrB,EACAvI,IAAiBO,EAAgBQ,KAAK,CAAG,CAAE,GAAGR,EAAgBQ,KAAK,CAAEd,OAAQD,EAAa,EAC3E,KAAfL,CAAsBY,GAAAA,EAAgBQ,KAAK,CAAG,CAAE,GAAGR,EAAgBQ,KAAK,CAAEE,MAAOtB,EAAW,EAE5F,MAAMsI,EAAkB1H,GACxBwG,EAAYxG,GACZiG,EAAW,GACb,EAEMgC,EAAY,CAACC,EAAQnB,KACrBmB,GAAG,EACI1H,KAAK,CAAC,KAAQ,CAAG0H,EAC1BlB,EAASD,IAAI,CAAGA,GAGhB,OAAOC,EAASxG,KAAK,CAACE,KAAK,CAC3BuG,EAAY,CAAE,GAAGD,CAAQ,EAE7B,EAEMmB,EAAoBC,CAAAA,EAAAA,EAAAA,MAAAA,CAAMA,CAC9BC,IAAAA,QAAU,CAAC,CAACH,EAAGnB,IAASkB,EAAUC,EAAGnB,GAAOuB,OAAOC,KAAgC,GAAK,MACxFC,OAAO,CAEHC,EAAyBC,CAAAA,EAAAA,EAAAA,OAAAA,CAAOA,CAAC,KAQrC,IAAMC,EAA2B,IAC/B9C,EAAgBnG,GACZA,GACFsH,EAASxG,GADC,EACI,CAAC,MAAS,CAAGd,EAC3BsH,EAASD,IAAI,CAAGV,GAGhB,OAAOW,EAASxG,KAAK,CAACd,MAAM,CAC5BuH,EAAY,CAAE,GAAGD,CAAQ,EAE7B,EAOA,MACE,UAAC3H,EAAAA,OAAqBA,CAAAA,CACpBC,SAPiB,CAOPsJ,GANZhD,EAAciD,EAAEC,MAAM,CAACzH,KAAK,EAC5B8G,EAAkBU,EAAEC,MAAM,CAACzH,KAAK,CAAEgF,EACpC,EAKI9G,qBAAsB,GAAYoJ,EAAyBE,EAAEC,MAAM,CAACzH,KAAK,EACzE7B,QA5BgB,CA4BPuJ,IA3BP3J,IACF0G,EAAyB,CAACpD,GAC1BkD,EAAc,IAElB,EAwBIxG,WAAYA,EACZK,aAAcA,GAGpB,EAAG,CAACL,EAAYK,EAAciD,EAAuBiD,EAAgB,EACrE,MACE,UAACvD,EAAAA,CAAQA,CAAAA,CACPI,QAASA,EACTlC,KAAMyF,EACNtD,UAAWA,EACXS,QAASA,EACTP,SAAS,IACTa,gBAAgB,IAChBD,OAAQwE,EACRzE,UAAU,IACVH,WAAW,EACXT,sBAAuBA,EACvBE,mBAAoB6F,EACpB5F,oBAAqBA,EACrBC,iBArJqB,CAqJHA,GApJpB9C,EAAgB8G,KAAK,CAAGX,EACxBnG,EAAgB+G,IAAI,CAAGA,EAGJ,IAAI,CAAnB3H,GACFY,GAAgBQ,KAAK,CAAG,CAAEE,MAAOtB,EAAW,EAI9CK,IACGO,EAAgBQ,KAAK,CAAG,CACvB,GAAGR,EAAgBQ,KAAK,CACxBd,OAAQD,EACV,EAGF8G,IAAavG,EAAgBS,IAAI,CAAG8F,CAAvBvG,CAAgCS,IAAAA,EAG7CiH,EAAkB1H,GAClBsG,EAAWS,EACb,GAkIF", "sources": ["webpack://_N_E/./pages/operation/OperationsTableFilter.tsx", "webpack://_N_E/./components/common/RKITable.tsx", "webpack://_N_E/?0e75", "webpack://_N_E/./pages/operation/OperationsTable.tsx"], "sourcesContent": ["//Import Library\r\nimport { useEffect, useState } from \"react\";\r\nimport { Col, Container, FormControl, Form, Row } from \"react-bootstrap\";\r\n\r\n//Import services/components\r\nimport apiService from \"../../services/apiService\";\r\nimport { useTranslation } from 'next-i18next';\r\n\r\n\r\nconst OperationsTableFilter = ({\r\n  filterText,\r\n  onFilter,\r\n  onFilterStatusChange,\r\n  onClear,\r\n  filterStatus,\r\n}: {\r\n  filterText: any,\r\n  onFilter: any,\r\n  onFilterStatusChange: any,\r\n  onClear: any,\r\n  filterStatus: any,\r\n}) => {\r\n  const [status, setStatus] = useState([]);\r\n  const { t } = useTranslation('common');\r\n\r\n  const getOperationsStatus = async (operationParams: any) => {\r\n    const response = await apiService.get(\"/operation_status\", operationParams);\r\n    if (response && Array.isArray(response.data)) { \r\n      setStatus(response.data);\r\n    }\r\n  };\r\n\r\n  useEffect(() => {\r\n    getOperationsStatus({\r\n      query: {},\r\n      sort: { title: \"asc\" },\r\n    });\r\n  }, []);\r\n\r\n  return (\r\n    <Container fluid className=\"p-0\">\r\n      <Row>\r\n        <Col xs={6} className=\"ps-0 align-self-end mb-3\">\r\n          <FormControl\r\n            type=\"text\"\r\n            className=\"searchInput\"\r\n            placeholder={t(\"search\")}\r\n            aria-label=\"Search\"\r\n            value={filterText}\r\n            onChange={onFilter}\r\n          />\r\n        </Col>\r\n        <Col xs={6}>\r\n          <Form>\r\n            <Form.Group as={Row} controlId=\"statusFilter\">\r\n              <Form.Label column sm=\"3\" lg=\"2\">\r\n              {t(\"Status\")}\r\n              </Form.Label>\r\n              <Col className=\"ps-0 pe-1\">\r\n                <FormControl\r\n                  as=\"select\"\r\n                  aria-label=\"Status\"\r\n                  onChange={onFilterStatusChange}\r\n                  value={filterStatus}\r\n                >\r\n                  <option value={\"\"}>All</option>\r\n                  {status.map((item: any, index) => {\r\n                    return (\r\n                      <option key={index} value={item._id}>\r\n                        {item.title}\r\n                      </option>\r\n                    );\r\n                  })}\r\n                </FormControl>\r\n              </Col>\r\n            </Form.Group>\r\n          </Form>\r\n        </Col>\r\n      </Row>\r\n    </Container>\r\n  );\r\n};\r\n\r\nexport default OperationsTableFilter;\r\n", "//Import Library\r\nimport React from 'react'\r\nimport DataTable  from 'react-data-table-component';\r\n\r\n//Import services/components\r\nimport { useTranslation } from 'next-i18next';\r\nimport CustomLoader from './CustomLoader';\r\n\r\ninterface RKITableProps {\r\n  columns: any[];\r\n  data: any[];\r\n  totalRows?: number;\r\n  resetPaginationToggle?: boolean;\r\n  subheader?: boolean;\r\n  subHeaderComponent?: React.ReactNode;\r\n  handlePerRowsChange?: (newPerPage: number, page: number) => void;\r\n  handlePageChange?: (page: number) => void;\r\n  rowsPerPage?: number[];\r\n  defaultRowsPerPage?: number;\r\n  selectableRows?: boolean;\r\n  loading?: boolean;\r\n  pagServer?: boolean;\r\n  onSelectedRowsChange?: (selectedRows: any) => void;\r\n  clearSelectedRows?: boolean;\r\n  sortServer?: boolean;\r\n  onSort?: (column: any, sortDirection: string) => void;\r\n  persistTableHead?: boolean;\r\n  sortFunction?: (rows: any[], field: any, direction: 'asc' | 'desc') => any[];\r\n  noHeader?: boolean;\r\n  dense?: boolean;\r\n  pagination?: boolean;\r\n  paginationServer?: boolean;\r\n  paginationTotalRows?: number;\r\n  subHeaderAlign?: string;\r\n  [key: string]: any;\r\n}\r\n\r\nfunction RKITable(props: RKITableProps) {\r\n  const { t } = useTranslation('common');\r\n  const paginationComponentOptions = {\r\n    rowsPerPageText: t('Rowsperpage'),\r\n    };\r\n  const {\r\n    columns,\r\n    data,\r\n    totalRows,\r\n    resetPaginationToggle,\r\n    subheader,\r\n    subHeaderComponent,\r\n    handlePerRowsChange,\r\n    handlePageChange,\r\n    rowsPerPage,\r\n    defaultRowsPerPage,\r\n    selectableRows,\r\n    loading,\r\n    pagServer,\r\n    onSelectedRowsChange,\r\n    clearSelectedRows,\r\n    sortServer,\r\n    onSort,\r\n    persistTableHead,\r\n    sortFunction,\r\n    // Filter out any unknown props that might cause DOM warnings\r\n    ...otherProps\r\n  } = props;\r\n\r\n  // Only pass known DataTable props to avoid DOM warnings\r\n  const dataTableProps = {\r\n    paginationComponentOptions,\r\n    noDataComponent: t(\"NoData\"),\r\n    noHeader: true,\r\n    columns,\r\n    data: data || [],\r\n    dense: true,\r\n    paginationResetDefaultPage: resetPaginationToggle,\r\n    subHeader: subheader,\r\n    progressPending: loading,\r\n    subHeaderComponent,\r\n    pagination: true,\r\n    paginationServer: pagServer,\r\n    paginationPerPage: defaultRowsPerPage ? defaultRowsPerPage : 10,\r\n    paginationRowsPerPageOptions: rowsPerPage ? rowsPerPage : [10, 15, 20, 25, 30],\r\n    paginationTotalRows: totalRows,\r\n    onChangeRowsPerPage: handlePerRowsChange,\r\n    onChangePage: handlePageChange,\r\n    selectableRows,\r\n    onSelectedRowsChange,\r\n    clearSelectedRows,\r\n    progressComponent: <CustomLoader/>,\r\n    sortIcon: <i className=\"sort-icon fas fa-exchange-alt\" />,\r\n    sortServer,\r\n    onSort,\r\n    sortFunction,\r\n    persistTableHead,\r\n    className: \"rki-table\"\r\n  };\r\n  return (\r\n    <DataTable {...dataTableProps} />\r\n  )\r\n};\r\n\r\nRKITable.defaultProps = {\r\n  subHeader: false,\r\n  pagination: true,\r\n  totalRows: null,\r\n  pagServer: true,\r\n  onSelectedRowsChange: null,\r\n  clearSelectedRows: false,\r\n  sortServer: false,\r\n  persistTableHead: false\r\n}\r\n\r\nexport default RKITable;\r\n\r\n", "\n    (window.__NEXT_P = window.__NEXT_P || []).push([\n      \"/operation/OperationsTable\",\n      function () {\n        return require(\"private-next-pages/operation/OperationsTable.tsx\");\n      }\n    ]);\n    if(module.hot) {\n      module.hot.dispose(function () {\n        window.__NEXT_P.push([\"/operation/OperationsTable\"])\n      });\n    }\n  ", "//Import Library\r\nimport React, { useRef, useMemo, useEffect, useState } from \"react\";\r\nimport Link from \"next/link\";\r\nimport _ from \"lodash\";\r\nimport { useRouter } from \"next/router\";\r\nimport moment from \"moment\";\r\n\r\n//Import services/components\r\nimport RKITable from \"../../components/common/RKITable\";\r\nimport apiService from \"../../services/apiService\";\r\nimport OperationsTableFilter from \"./OperationsTableFilter\";\r\nimport { useTranslation } from 'next-i18next';\r\n\r\nconst PartnersLink = ({ partners }: any) => {\r\n  if (partners && partners.length > 0) {\r\n    return (\r\n      <ul>\r\n        {partners.map((item: any, index: any) => {\r\n          if (item.institution) {\r\n            return (\r\n              <li key={index}>\r\n                <Link\r\n                  href=\"/institution/[...routes]\"\r\n                  as={`/institution/show/${item.institution._id}`}\r\n                >\r\n                  {item.institution.title}\r\n                </Link>\r\n              </li>\r\n            );\r\n          }\r\n        })}\r\n      </ul>\r\n    );\r\n  }\r\n  return null;\r\n};\r\n\r\nfunction OperationsTable(props: any) {\r\n  const { t } = useTranslation('common');\r\n  const router = useRouter();\r\n  const { setOperations, selectedRegions } = props;\r\n  const [filterText, setFilterText] = useState(\"\");\r\n  const [filterStatus, setFilterStatus] = useState(\"\");\r\n  const [resetPaginationToggle, setResetPaginationToggle] = useState(false);\r\n  const [tabledata, setDataToTable] = useState([]);\r\n  const [loading, setLoading] = useState(false);\r\n  const [totalRows, setTotalRows] = useState(0);\r\n  const [perPage, setPerPage] = useState(10);\r\n  const [pageNum, setPageNum] = useState(1);\r\n  const [pageSort, setPageSort] = useState<any>(null);\r\n\r\n\r\n  const operationParams: any = {\r\n    sort: { created_at: \"desc\" },\r\n    lean: true,\r\n    populate: [\r\n      { path: \"partners.status\", select: \"title\" },\r\n      { path: \"partners.institution\", select: \"title\" },\r\n      { path: \"status\", select: \"title\" },\r\n      { path: \"country\", select: \"coordinates\" },\r\n    ],\r\n    limit: perPage,\r\n    page: 1,\r\n    query: {},\r\n    select:\r\n      \"-timeline -region -hazard -description -end_date -syndrome -hazard_type -created_at -updated_at\",\r\n  };\r\n  const [opParams, setOpParams] = useState(operationParams);\r\n\r\n  const columns = [\r\n    {\r\n      name: t(\"Operations\"),\r\n      selector: \"title\",\r\n      sortable: true,\r\n      cell: (d: any) => (\r\n        <Link href=\"/operation/[...routes]\" as={`/operation/show/${d._id}`}>\r\n          {d.title}\r\n        </Link>\r\n      ),\r\n    },\r\n    {\r\n      name: t(\"Status\"),\r\n      selector: \"status\",\r\n      sortable: true,\r\n      cell: (d: any) => d.status && d.status.title ? d.status.title : \"\"\r\n    },\r\n    {\r\n      name: t(\"StartDate\"),\r\n      selector: \"start_date\",\r\n      sortable: true,\r\n      cell: (d: any) =>\r\n        d && d.start_date ? moment(d.start_date).format(\"M/D/Y\") : \"\",\r\n    },\r\n    {\r\n      name: t(\"Partners\"),\r\n      selector: \"partners\",\r\n      cell: (d: any) => <PartnersLink partners={d.partners} />,\r\n    },\r\n  ];\r\n\r\n\r\n\r\n  const getOperationsData = async (operationParamsinit: any) => {\r\n    setLoading(true);\r\n\r\n    if (router.query && router.query.country) {\r\n      operationParamsinit.query[\"country\"] = router.query.country;\r\n    }\r\n\r\n    // Handle selectedRegions with proper condition\r\n    if (selectedRegions === null) {\r\n      // First load: don't apply region filter\r\n      delete operationParamsinit.query[\"world_region\"];\r\n    } else if (selectedRegions.length === 0) {\r\n      // No regions selected: force zero results\r\n      operationParamsinit.query[\"world_region\"] = [\"__NO_MATCH__\"]; // dummy value\r\n    } else {\r\n      // Normal filtering\r\n      operationParamsinit.query[\"world_region\"] = selectedRegions;\r\n    }\r\n\r\n    const response = await apiService.get(\"/operation\", operationParamsinit);\r\n    if (response && Array.isArray(response.data)) {\r\n      setDataToTable(response.data);\r\n      setOperations(response.data);\r\n      setTotalRows(response.totalCount);\r\n      setLoading(false);\r\n    }\r\n  };\r\n\r\n\r\n  const handlePageChange = (page: any) => {\r\n    operationParams.limit = perPage;\r\n    operationParams.page = page;\r\n\r\n    // Apply the filter for title if present\r\n    if (filterText !== \"\") {\r\n      operationParams.query = { title: filterText };\r\n    }\r\n\r\n    // Apply filter for status if present\r\n    filterStatus &&\r\n      (operationParams.query = {\r\n        ...operationParams.query,\r\n        status: filterStatus,\r\n      });\r\n\r\n    // Handle sorting\r\n    pageSort && (operationParams.sort = pageSort.sort);\r\n\r\n    // Get the data\r\n    getOperationsData(operationParams);\r\n    setPageNum(page);\r\n  };\r\n\r\n\r\n  const handlePerRowsChange = async (newPerPage: any, page: any) => {\r\n    operationParams.limit = newPerPage;\r\n    operationParams.page = page;\r\n    setLoading(true);\r\n\r\n    if (router.query && router.query.country) {\r\n      operationParams.query[\"country\"] = router.query.country;\r\n    }\r\n\r\n    // Handle selected regions similarly as in `getOperationsData()`\r\n    if (selectedRegions === null) {\r\n      delete operationParams.query[\"world_region\"];\r\n    } else if (selectedRegions.length === 0) {\r\n      operationParams.query[\"world_region\"] = [\"__NO_MATCH__\"];\r\n    } else {\r\n      operationParams.query[\"world_region\"] = selectedRegions;\r\n    }\r\n\r\n    filterStatus &&\r\n      (operationParams.query = {\r\n        ...operationParams.query,\r\n        status: filterStatus,\r\n      });\r\n\r\n    pageSort && (operationParams.sort = pageSort.sort);\r\n\r\n    const response = await apiService.get(\"/operation\", operationParams);\r\n    if (response && Array.isArray(response.data)) {\r\n      setDataToTable(response.data);\r\n      setOperations(response.data);\r\n      setPerPage(newPerPage);\r\n      setLoading(false);\r\n    }\r\n    setPageNum(page);\r\n  };\r\n\r\n\r\n  useEffect(() => {\r\n    opParams.page = 1;\r\n    getOperationsData(opParams);\r\n  }, [selectedRegions, router]);\r\n\r\n  useEffect(() => {\r\n    getOperationsData(opParams);\r\n  }, [opParams]);  // Make sure to watch `opParams` for changes\r\n\r\n\r\n  const handleSort = async (column: any, sortDirection: any) => {\r\n    setLoading(true);\r\n    operationParams.sort = {\r\n      [column.selector]: sortDirection,\r\n    };\r\n    filterStatus && (operationParams.query = { ...operationParams.query, status: filterStatus });\r\n    filterText !== \"\" && (operationParams.query = { ...operationParams.query, title: filterText });\r\n\r\n    await getOperationsData(operationParams);\r\n    setPageSort(operationParams);\r\n    setLoading(false);\r\n  };\r\n\r\n  const sendQuery = (q: any, page: any) => {\r\n    if (q) {\r\n      opParams.query[\"title\"] = q;\r\n      opParams.page = page;\r\n      setOpParams({ ...opParams });\r\n    } else {\r\n      delete opParams.query.title;\r\n      setOpParams({ ...opParams });\r\n    }\r\n  };\r\n\r\n  const handleSearchTitle = useRef(\r\n    _.debounce((q, page) => sendQuery(q, page), Number(process.env.SEARCH_DEBOUNCE_TIME) || 300)\r\n  ).current;\r\n\r\n  const subHeaderComponentMemo = useMemo(() => {\r\n    const handleClear = () => {\r\n      if (filterText) {\r\n        setResetPaginationToggle(!resetPaginationToggle);\r\n        setFilterText(\"\");\r\n      }\r\n    };\r\n\r\n    const handleFilterStatusChange = (status: any) => {\r\n      setFilterStatus(status);\r\n      if (status) {\r\n        opParams.query[\"status\"] = status;\r\n        opParams.page = pageNum;\r\n        setOpParams({ ...opParams });\r\n      } else {\r\n        delete opParams.query.status;\r\n        setOpParams({ ...opParams });\r\n      }\r\n    };\r\n\r\n    const handleChange = (e: any) => {\r\n      setFilterText(e.target.value);\r\n      handleSearchTitle(e.target.value, pageNum);\r\n    };\r\n\r\n    return (\r\n      <OperationsTableFilter\r\n        onFilter={handleChange}\r\n        onFilterStatusChange={(e: any) => handleFilterStatusChange(e.target.value)}\r\n        onClear={handleClear}\r\n        filterText={filterText}\r\n        filterStatus={filterStatus}\r\n      />\r\n    );\r\n  }, [filterText, filterStatus, resetPaginationToggle, selectedRegions]);\r\n  return (\r\n    <RKITable\r\n      columns={columns}\r\n      data={tabledata}\r\n      totalRows={totalRows}\r\n      loading={loading}\r\n      subheader\r\n      persistTableHead\r\n      onSort={handleSort}\r\n      sortServer\r\n      pagServer={true}\r\n      resetPaginationToggle={resetPaginationToggle}\r\n      subHeaderComponent={subHeaderComponentMemo}\r\n      handlePerRowsChange={handlePerRowsChange}\r\n      handlePageChange={handlePageChange}\r\n    />\r\n  );\r\n}\r\n\r\nexport default OperationsTable;\r\n"], "names": ["filterText", "OperationsTableFilter", "onFilter", "onFilterStatusChange", "onClear", "filterStatus", "status", "setStatus", "useState", "t", "useTranslation", "getOperationsStatus", "operationParams", "response", "apiService", "get", "Array", "isArray", "data", "useEffect", "query", "sort", "title", "Container", "fluid", "className", "Row", "Col", "xs", "FormControl", "type", "placeholder", "aria-label", "value", "onChange", "Form", "Group", "as", "controlId", "Label", "column", "sm", "lg", "option", "map", "item", "index", "_id", "RKITable", "props", "paginationComponentOptions", "rowsPerPageText", "columns", "totalRows", "resetPaginationToggle", "subheader", "subHeaderComponent", "handlePerRowsChange", "handlePageChange", "rowsPerPage", "defaultRowsPerPage", "selectableRows", "loading", "pagServer", "onSelectedRowsChange", "clearSelectedRows", "sortServer", "onSort", "persistTableHead", "sortFunction", "otherProps", "dataTableProps", "noDataComponent", "<PERSON><PERSON><PERSON><PERSON>", "dense", "paginationResetDefaultPage", "subHeader", "progressPending", "pagination", "paginationServer", "paginationPerPage", "paginationRowsPerPageOptions", "paginationTotalRows", "onChangeRowsPerPage", "onChangePage", "progressComponent", "CustomLoader", "sortIcon", "i", "DataTable", "defaultProps", "PartnersLink", "partners", "length", "ul", "institution", "li", "Link", "href", "OperationsTable", "router", "useRouter", "setOperations", "selectedRegions", "setFilterText", "setFilterStatus", "setResetPaginationToggle", "tabledata", "setDataToTable", "setLoading", "setTotalRows", "perPage", "setPerPage", "pageNum", "setPageNum", "pageSort", "setPageSort", "created_at", "lean", "populate", "path", "select", "limit", "page", "opParams", "setOpParams", "name", "selector", "sortable", "cell", "d", "start_date", "moment", "format", "getOperationsData", "operationParamsinit", "country", "totalCount", "newPerPage", "handleSort", "sortDirection", "<PERSON><PERSON><PERSON><PERSON>", "q", "handleSearchTitle", "useRef", "_", "Number", "process", "current", "subHeaderComponentMemo", "useMemo", "handleFilterStatusChange", "handleChange", "e", "target", "handleClear"], "sourceRoot": "", "ignoreList": []}