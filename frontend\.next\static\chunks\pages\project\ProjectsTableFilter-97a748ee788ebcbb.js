(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[7214],{23103:(e,s,a)=>{"use strict";a.r(s),a.d(s,{default:()=>o});var l=a(37876),t=a(14232),r=a(49589),c=a(56970),n=a(37784),i=a(12697),u=a(29504),d=a(31753),h=a(53718);let o=e=>{let{filterText:s,onFilter:a,onFilterStatusChange:o,onClear:p,filterStatus:j}=e,[A,x]=(0,t.useState)([]),{t:_}=(0,d.Bd)("common"),m=async e=>{let s=await h.A.get("/projectstatus",e);s&&Array.isArray(s.data)&&x(s.data)};return(0,t.useEffect)(()=>{m({query:{},sort:{title:"asc"}})},[]),(0,l.jsx)(r.A,{fluid:!0,className:"p-0",children:(0,l.jsxs)(c.A,{children:[(0,l.jsx)(n.A,{xs:6,className:"ps-0 align-self-end mb-3",children:(0,l.jsx)(i.A,{type:"text",className:"searchInput",placeholder:_("vspace.Search"),"aria-label":"Search",value:s,onChange:a})}),(0,l.jsx)(n.A,{children:(0,l.jsx)(u.A,{children:(0,l.jsxs)(u.A.Group,{as:c.A,controlId:"statusFilter",children:[(0,l.jsx)(u.A.Label,{column:!0,sm:"3",lg:"2",children:"Status"}),(0,l.jsx)(n.A,{className:"ps-0 pe-1",children:(0,l.jsxs)(i.A,{as:"select","aria-label":"Status",onChange:o,value:j,children:[(0,l.jsx)("option",{value:"",children:"All"}),A.map((e,s)=>(0,l.jsx)("option",{value:e._id,children:e.title},s))]})})]})})})]})})}},82284:(e,s,a)=>{(window.__NEXT_P=window.__NEXT_P||[]).push(["/project/ProjectsTableFilter",function(){return a(23103)}])}},e=>{var s=s=>e(e.s=s);e.O(0,[636,6593,8792],()=>s(82284)),_N_E=e.O()}]);
//# sourceMappingURL=ProjectsTableFilter-97a748ee788ebcbb.js.map