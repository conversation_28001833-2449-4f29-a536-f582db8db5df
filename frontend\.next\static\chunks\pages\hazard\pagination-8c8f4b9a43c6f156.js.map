{"version": 3, "file": "static/chunks/pages/hazard/pagination-8c8f4b9a43c6f156.js", "mappings": "gFACA,4CACA,qBACA,WACA,OAAe,EAAQ,KAA0C,CACjE,EACA,SAFsB,yHCCtB,IAAMA,EAAwBC,EAAAA,OAAb,GAA6B,CAAC,GAW5CC,IAXyB,IAAoB,QAC9CC,EAAS,EAAK,UACdC,GAAW,CAAK,WAChBC,CAAS,OACTC,CAAK,aACLC,EAAc,WAAW,UACzBC,CAAQ,CACRC,WAAS,CACTC,eAAa,IACbC,EAAKC,EAAAA,CAAM,CACX,GAAGC,EACJ,GACOC,EAAYX,GAAUC,EAAW,OAASO,EAChD,MAAoBI,CAAb,EAAaA,EAAAA,GAAAA,CAAIA,CAAC,EAAP,GAAa,CAC7Bb,IAAKA,EACLI,MAAOA,EACPD,UAAWW,IAAWX,EAAW,YAAa,EAAzBW,MACnBb,WACAC,CACF,GACAI,SAAuBS,CAAb,EAAaA,EAAAA,IAAAA,CAAKA,CAACH,CAAR,CAAmB,CACtCT,UAAWW,IAAW,YAAaN,GACnCJ,CADqBU,KACdP,EACP,GAAGI,CAAK,CACRL,SAAU,CAACA,EAAUL,GAAUI,GAA4BQ,CAAAA,EAAAA,EAAAA,GAAAA,CAAIA,CAAC,EAAlB,KAA0B,CACtEV,KADuD,KAC5C,kBACXG,SAAUD,CACZ,GAAG,EAEP,EACF,GAGA,SAASW,EAAaC,CAAI,CAAEC,CAAY,MAAEC,EAAAA,UAAAA,MAAAA,CAAAA,GAAAA,KAAAA,IAAAA,SAAAA,CAAAA,EAAAA,CAAAA,SAAAA,CAAAA,EAAAA,CAAQF,EAC1CG,EAAsBrB,EAAAA,KAAb,KAA6B,CAAC,GAG1CC,EAHuB,MAAoB,UAC5CM,CAAQ,CACR,GAAGK,EACJ,SAAuBI,CAAb,EAAaA,EAAAA,IAAAA,CAAKA,CAACjB,CAAR,CAAkB,CACtC,GAAGa,CAAK,CACRX,IAAKA,EACLM,SAAU,CAAcO,CAAAA,EAAAA,EAAAA,GAAAA,CAAIA,CAAC,OAAQ,CACnC,cAAe,OACfP,SAAUA,GAAYY,CACxB,GAAiBL,CAAAA,EAAAA,EAAAA,GAAAA,CAAIA,CAAC,OAAQ,CAC5BV,UAAW,kBACXG,SAAUa,CACZ,GAAG,KAGL,OADAC,EAAOC,WAAW,CAAGJ,EACdG,CACT,CAnBAtB,EAASuB,WAAW,CAAG,WAoBhB,IAAMC,EAAQN,EAAa,QAAS,KAAK,GACnCO,EAAOP,EAAa,OAAQ,IAAK,YAAY,EAClCA,EAAa,WAAY,IAAK,QACzCQ,EAAOR,EAAa,OAAQ,KAAK,EAC1BA,EAAa,OAAQ,KAAK,GCrDxCS,EAA0B1B,EAAAA,SAAb,CAA6B,CAAC,GAK9CC,MAL2B,EAAoB,UAChD0B,CAAQ,WACRvB,CAAS,MACTwB,CAAI,CACJ,GAAGhB,EACJ,GACOiB,EAAoBC,CAAAA,EAAAA,EAAAA,EAAAA,CAAkBA,CAACH,EAAU,cACvD,MAAoBb,CAAb,EAAaA,EAAAA,GAAAA,CAAIA,CAAC,EAAP,GAAa,CAC7Bb,IAAKA,EACL,GAAGW,CAAK,CACRR,UAAWW,IAAWX,EAAWyB,EAAmBD,GAAQ,GAAwBA,MAA/Db,CAA0Cc,EAAkB,KAAQ,OAALD,GACtF,EACF,GACAF,EAAWJ,WAAW,CAAG,aACzB,MAAeS,OAAOC,MAAM,CAACN,EAAY,CACvCH,KAAKA,CAAAA,EACLC,GADKD,CACDC,CAAAA,EACJS,EADIT,MACIS,CAAAA,EACRC,KDYanC,CCbLkC,CAERR,IAAIA,CAAAA,CDWiB1B,CCVrBoC,CDUsB,CCXlBV,EADU1B,CAEVoC,CACN,EAAE,CADIA,CACH,CCxBgB,OAAC,KAqBLT,SArBOU,CAAY,WAqBTV,CArBWW,CAAU,UAAEC,CAAQ,CAA0D,GAC1GC,EAAqB,EAAE,CACvB,CAACC,EAAYC,EAAc,CAAGC,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAyB,GAErE,IAAK,IAAIC,EAAI,EAAGA,GAAKC,KAAKC,IAAI,CAACR,EAAaD,GAAeO,IAAK,EAClDG,IAAI,CACd,UAACC,EAAKb,IAAI,EAAShC,MAAd6C,CAAsBJ,IAAMH,WAC9BG,GADaA,IAMpB,IAAMK,EAAoB,MAAOC,IAC/B,IAAMC,EAASD,EAAEC,MAAM,CACvBT,EAAcS,EAAOC,WAAW,EAChCF,EAAEC,MAAM,EAAK,MAAMZ,EAASY,EAAOC,WAAW,CAChD,EAEA,MAAO,UAACJ,EAAIA,CAACK,QAASJ,GAAVD,QAA8BR,GAC5C", "sources": ["webpack://_N_E/?65e9", "webpack://_N_E/./node_modules/react-bootstrap/esm/PageItem.js", "webpack://_N_E/./node_modules/react-bootstrap/esm/Pagination.js", "webpack://_N_E/./pages/hazard/pagination.tsx"], "sourcesContent": ["\n    (window.__NEXT_P = window.__NEXT_P || []).push([\n      \"/hazard/pagination\",\n      function () {\n        return require(\"private-next-pages/hazard/pagination.tsx\");\n      }\n    ]);\n    if(module.hot) {\n      module.hot.dispose(function () {\n        window.__NEXT_P.push([\"/hazard/pagination\"])\n      });\n    }\n  ", "import classNames from 'classnames';\nimport * as React from 'react';\nimport Anchor from '@restart/ui/Anchor';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nimport { jsxs as _jsxs } from \"react/jsx-runtime\";\nconst PageItem = /*#__PURE__*/React.forwardRef(({\n  active = false,\n  disabled = false,\n  className,\n  style,\n  activeLabel = '(current)',\n  children,\n  linkStyle,\n  linkClassName,\n  as = Anchor,\n  ...props\n}, ref) => {\n  const Component = active || disabled ? 'span' : as;\n  return /*#__PURE__*/_jsx(\"li\", {\n    ref: ref,\n    style: style,\n    className: classNames(className, 'page-item', {\n      active,\n      disabled\n    }),\n    children: /*#__PURE__*/_jsxs(Component, {\n      className: classNames('page-link', linkClassName),\n      style: linkStyle,\n      ...props,\n      children: [children, active && activeLabel && /*#__PURE__*/_jsx(\"span\", {\n        className: \"visually-hidden\",\n        children: activeLabel\n      })]\n    })\n  });\n});\nPageItem.displayName = 'PageItem';\nexport default PageItem;\nfunction createButton(name, defaultValue, label = name) {\n  const Button = /*#__PURE__*/React.forwardRef(({\n    children,\n    ...props\n  }, ref) => /*#__PURE__*/_jsxs(PageItem, {\n    ...props,\n    ref: ref,\n    children: [/*#__PURE__*/_jsx(\"span\", {\n      \"aria-hidden\": \"true\",\n      children: children || defaultValue\n    }), /*#__PURE__*/_jsx(\"span\", {\n      className: \"visually-hidden\",\n      children: label\n    })]\n  }));\n  Button.displayName = name;\n  return Button;\n}\nexport const First = createButton('First', '«');\nexport const Prev = createButton('Prev', '‹', 'Previous');\nexport const Ellipsis = createButton('Ellipsis', '…', 'More');\nexport const Next = createButton('Next', '›');\nexport const Last = createButton('Last', '»');", "\"use client\";\n\nimport classNames from 'classnames';\nimport * as React from 'react';\nimport { useBootstrapPrefix } from './ThemeProvider';\nimport PageItem, { Ellipsis, First, Last, Next, Prev } from './PageItem';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst Pagination = /*#__PURE__*/React.forwardRef(({\n  bsPrefix,\n  className,\n  size,\n  ...props\n}, ref) => {\n  const decoratedBsPrefix = useBootstrapPrefix(bsPrefix, 'pagination');\n  return /*#__PURE__*/_jsx(\"ul\", {\n    ref: ref,\n    ...props,\n    className: classNames(className, decoratedBsPrefix, size && `${decoratedBsPrefix}-${size}`)\n  });\n});\nPagination.displayName = 'Pagination';\nexport default Object.assign(Pagination, {\n  First,\n  Prev,\n  Ellipsis,\n  Item: PageItem,\n  Next,\n  Last\n});", "//Import Library\r\nimport React, { useState } from \"react\";\r\nimport { Pagination as Page } from \"react-bootstrap\";\r\n\r\nconst Pagination = ({ postsPerPage, totalPosts, paginate } : { postsPerPage: any, totalPosts: any, paginate: any }) => {\r\n  const pageNumbers: any[] = [];\r\n  const [activePage, setActivePage] = useState<string | number | null>(1);\r\n\r\n  for (let i = 1; i <= Math.ceil(totalPosts / postsPerPage); i++) {\r\n    pageNumbers.push(\r\n      <Page.Item key={i} active={i === activePage}>\r\n        {i}\r\n      </Page.Item>\r\n    );\r\n  }\r\n\r\n  const paginationHandler = async (e: React.MouseEvent<HTMLElement>) => {\r\n    const target = e.target as HTMLElement;\r\n    setActivePage(target.textContent);\r\n    e.target && (await paginate(target.textContent));\r\n  };\r\n\r\n  return <Page onClick={paginationHandler}>{pageNumbers}</Page>;\r\n};\r\n\r\nexport default Pagination;\r\n"], "names": ["PageItem", "React", "ref", "active", "disabled", "className", "style", "activeLabel", "children", "linkStyle", "linkClassName", "as", "<PERSON><PERSON>", "props", "Component", "_jsx", "classNames", "_jsxs", "createButton", "name", "defaultValue", "label", "<PERSON><PERSON>", "displayName", "First", "Prev", "Next", "Pagination", "bsPrefix", "size", "decoratedBsPrefix", "useBootstrapPrefix", "Object", "assign", "El<PERSON><PERSON>", "<PERSON><PERSON>", "Last", "postsPerPage", "totalPosts", "paginate", "pageNumbers", "activePage", "setActivePage", "useState", "i", "Math", "ceil", "push", "Page", "pagination<PERSON><PERSON><PERSON>", "e", "target", "textContent", "onClick"], "sourceRoot": "", "ignoreList": [1, 2]}