(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[2895],{6266:(e,a,r)=>{(window.__NEXT_P=window.__NEXT_P||[]).push(["/updates/LinkForm",function(){return r(76936)}])},29335:(e,a,r)=>{"use strict";r.d(a,{A:()=>C});var t=r(15039),l=r.n(t),d=r(14232),s=r(77346),i=r(37876);let n=d.forwardRef((e,a)=>{let{className:r,bsPrefix:t,as:d="div",...n}=e;return t=(0,s.oU)(t,"card-body"),(0,i.jsx)(d,{ref:a,className:l()(r,t),...n})});n.displayName="CardBody";let o=d.forwardRef((e,a)=>{let{className:r,bsPrefix:t,as:d="div",...n}=e;return t=(0,s.oU)(t,"card-footer"),(0,i.jsx)(d,{ref:a,className:l()(r,t),...n})});o.displayName="CardFooter";var c=r(81764);let u=d.forwardRef((e,a)=>{let{bsPrefix:r,className:t,as:n="div",...o}=e,u=(0,s.oU)(r,"card-header"),m=(0,d.useMemo)(()=>({cardHeaderBsPrefix:u}),[u]);return(0,i.jsx)(c.A.Provider,{value:m,children:(0,i.jsx)(n,{ref:a,...o,className:l()(t,u)})})});u.displayName="CardHeader";let m=d.forwardRef((e,a)=>{let{bsPrefix:r,className:t,variant:d,as:n="img",...o}=e,c=(0,s.oU)(r,"card-img");return(0,i.jsx)(n,{ref:a,className:l()(d?"".concat(c,"-").concat(d):c,t),...o})});m.displayName="CardImg";let p=d.forwardRef((e,a)=>{let{className:r,bsPrefix:t,as:d="div",...n}=e;return t=(0,s.oU)(t,"card-img-overlay"),(0,i.jsx)(d,{ref:a,className:l()(r,t),...n})});p.displayName="CardImgOverlay";let x=d.forwardRef((e,a)=>{let{className:r,bsPrefix:t,as:d="a",...n}=e;return t=(0,s.oU)(t,"card-link"),(0,i.jsx)(d,{ref:a,className:l()(r,t),...n})});x.displayName="CardLink";var f=r(46052);let h=(0,f.A)("h6"),j=d.forwardRef((e,a)=>{let{className:r,bsPrefix:t,as:d=h,...n}=e;return t=(0,s.oU)(t,"card-subtitle"),(0,i.jsx)(d,{ref:a,className:l()(r,t),...n})});j.displayName="CardSubtitle";let v=d.forwardRef((e,a)=>{let{className:r,bsPrefix:t,as:d="p",...n}=e;return t=(0,s.oU)(t,"card-text"),(0,i.jsx)(d,{ref:a,className:l()(r,t),...n})});v.displayName="CardText";let N=(0,f.A)("h5"),y=d.forwardRef((e,a)=>{let{className:r,bsPrefix:t,as:d=N,...n}=e;return t=(0,s.oU)(t,"card-title"),(0,i.jsx)(d,{ref:a,className:l()(r,t),...n})});y.displayName="CardTitle";let A=d.forwardRef((e,a)=>{let{bsPrefix:r,className:t,bg:d,text:o,border:c,body:u=!1,children:m,as:p="div",...x}=e,f=(0,s.oU)(r,"card");return(0,i.jsx)(p,{ref:a,...x,className:l()(t,f,d&&"bg-".concat(d),o&&"text-".concat(o),c&&"border-".concat(c)),children:u?(0,i.jsx)(n,{children:m}):m})});A.displayName="Card";let C=Object.assign(A,{Img:m,Title:y,Subtitle:j,Body:n,Link:x,Text:v,Header:u,Footer:o,ImgOverlay:p})},54773:(e,a,r)=>{"use strict";r.d(a,{A:()=>n});var t=r(37876),l=r(14232),d=r(39593),s=r(91408);let i=(0,l.forwardRef)((e,a)=>{let{children:r,onSubmit:l,autoComplete:i,className:n,onKeyPress:o,initialValues:c,...u}=e,m=s.Ik().shape({});return(0,t.jsx)(d.l1,{initialValues:c||{},validationSchema:m,onSubmit:(e,a)=>{let r={preventDefault:()=>{},stopPropagation:()=>{},currentTarget:null,target:null,nativeEvent:new Event("submit"),bubbles:!1,cancelable:!0,defaultPrevented:!1,eventPhase:0,isTrusted:!1,timeStamp:Date.now(),type:"submit",isDefaultPrevented:()=>!1,isPropagationStopped:()=>!1,persist:()=>{}};l&&l(r,e,a)},...u,children:e=>(0,t.jsx)(d.lV,{ref:a,onSubmit:e.handleSubmit,autoComplete:i,className:n,onKeyPress:o,children:"function"==typeof r?r(e):r})})});i.displayName="ValidationFormWrapper";let n=i},76936:(e,a,r)=>{"use strict";r.r(a),r.d(a,{default:()=>m});var t=r(37876);r(14232);var l=r(49589),d=r(29335),s=r(56970),i=r(37784),n=r(29504),o=r(60282),c=r(31753),u=r(54773);let m=e=>{let{t:a}=(0,c.Bd)("common"),{link:r,handleChangeforTimeline:m,removeForm:p,addform:x}=e;return(0,t.jsx)(l.A,{className:"formCard",fluid:!0,children:(0,t.jsx)(d.A,{children:(0,t.jsx)(u.A,{onSubmit:()=>{},children:(0,t.jsxs)(d.A.Body,{children:[r&&r.map((e,r)=>(0,t.jsx)("div",{children:(0,t.jsxs)(s.A,{children:[(0,t.jsx)(i.A,{children:(0,t.jsxs)(n.A.Group,{children:[(0,t.jsx)(n.A.Label,{className:"required-field",children:a("update.Title")}),(0,t.jsx)(n.A.Control,{name:"title",id:"timetitle",type:"text",value:e.title,required:!0,onChange:e=>m(e,r)}),(0,t.jsx)(n.A.Control.Feedback,{type:"invalid",children:a("update.TitleisRequired")})]})}),(0,t.jsx)(i.A,{children:(0,t.jsxs)(n.A.Group,{children:[(0,t.jsx)(n.A.Label,{className:"required-field",children:a("update.Link")}),(0,t.jsx)(n.A.Control,{name:"link",id:"link",type:"text",required:!0,value:e.link,onChange:e=>m(e,r),pattern:"http(s)?://??[\\w.-]+[-a-zA-Z0-9@:%._\\+~#=]{2,256}\\.[a-z]{2,6}.+"}),(0,t.jsx)(n.A.Control.Feedback,{type:"invalid",children:a("update.Providevalidlink")})]})}),(0,t.jsx)(i.A,{children:(0,t.jsx)(n.A.Group,{children:0===r?(0,t.jsx)("div",{}):(0,t.jsx)(o.A,{variant:"secondary",style:{marginTop:"30px"},onClick:e=>p(e,r),children:a("update.Remove")})})})]})})),(0,t.jsx)(s.A,{children:(0,t.jsx)(i.A,{md:!0,lg:"4",children:(0,t.jsx)(o.A,{variant:"secondary",style:{marginTop:"27px",marginBottom:"20px"},onClick:x,children:a("update.ADD")})})})]})})})})}},81764:(e,a,r)=>{"use strict";r.d(a,{A:()=>l});let t=r(14232).createContext(null);t.displayName="CardHeaderContext";let l=t}},e=>{var a=a=>e(e.s=a);e.O(0,[636,6593,8792],()=>a(6266)),_N_E=e.O()}]);
//# sourceMappingURL=LinkForm-ac9c88b8d3950a07.js.map