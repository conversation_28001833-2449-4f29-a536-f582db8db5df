{"version": 3, "file": "static/chunks/pages/profile/confirmation-95ecc41191733b1a.js", "mappings": "gFACA,4CACA,wBACA,WACA,OAAe,EAAQ,KAA6C,CACpE,EACA,SAFsB,uKC+DtB,MArDqB,IACjB,GAAM,QAAEA,CAAM,YAoDHC,EApDKC,CAAY,QAAEC,CAAM,EAoDZ,QApDcC,CAAQ,CAAE,CAAGC,EAC7C,GAAEC,CAAC,CAAE,CAAGC,CAAAA,EAAAA,EAAAA,EAAAA,CAAcA,CAAC,UAEvBC,EAAc,KAChBN,GAAa,EACjB,EAEMO,EAAwB,UAC1B,IAAIC,GACAN,UAAuB,GACZ,MAAMO,EAAAA,CAAUA,CAACC,MAAM,CAAC,GAAeT,MAAAA,CAAZC,EAAS,KAAU,OAAPD,IAEvC,MAAMQ,EAAAA,CAAUA,CAACE,IAAI,CAAC,GAAY,OAATT,GAAY,CAAEU,KAAMX,CAAO,MAI/DD,GAAa,GACba,IAAAA,IAAW,CAAC,SAEpB,EAEA,MACI,WAACC,EAAAA,CAAKA,CAAAA,CAACC,KAAMjB,EAAQkB,OAAQV,YACzB,UAACW,MAAAA,CAAIC,UAAU,2BACX,UAACC,EAAAA,CAAeA,CAAAA,CACZC,KAAMC,EAAAA,GAAqBA,CAC3BC,KAAK,KACLC,MAAM,YACNC,MAAO,CACHC,WAAY,UACZC,QAAS,OACTC,aAAc,MACdC,MAAO,QACPC,OAAQ,OACZ,MAGR,UAACf,EAAAA,CAAKA,CAACgB,IAAI,WAAC,UAACC,IAAAA,UAAG3B,EAAE,oDAClB,WAACU,EAAAA,CAAKA,CAACkB,MAAM,YACT,UAACC,EAAAA,CAAMA,CAAAA,CAACC,QAAQ,YAAYC,QAAS7B,WACpCF,EAAE,QAEH,UAAC6B,EAAAA,CAAMA,CAAAA,CAACC,QAAQ,SAASC,QAAS5B,WACjCH,EAAE,sBAKnB", "sources": ["webpack://_N_E/?8944", "webpack://_N_E/./pages/profile/confirmation.tsx"], "sourcesContent": ["\n    (window.__NEXT_P = window.__NEXT_P || []).push([\n      \"/profile/confirmation\",\n      function () {\n        return require(\"private-next-pages/profile/confirmation.tsx\");\n      }\n    ]);\n    if(module.hot) {\n      module.hot.dispose(function () {\n        window.__NEXT_P.push([\"/profile/confirmation\"])\n      });\n    }\n  ", "//Import Library\r\nimport React from \"react\";\r\nimport { <PERSON><PERSON>, But<PERSON> } from \"react-bootstrap\";\r\nimport { FontAwesomeIcon } from \"@fortawesome/react-fontawesome\";\r\nimport {\r\n    faExclamationTriangle\r\n} from \"@fortawesome/free-solid-svg-icons\";\r\nimport Router from 'next/router';\r\n\r\n//Import services/components\r\nimport apiService from '../../services/apiService';\r\nimport { useTranslation } from 'next-i18next';\r\n\r\n\r\nconst Confirmation = (props: any) => {\r\n    const { isopen, manageDialog, userId, endpoint } = props;\r\n    const { t } = useTranslation('common');\r\n\r\n    const handleClose = () => {\r\n        manageDialog(false)\r\n    }\r\n\r\n    const accountDeleteHandlder = async () => {\r\n        let response;\r\n        if (endpoint === \"/users\") {\r\n            response = await apiService.remove(`${endpoint}/${userId}`);\r\n        } else {\r\n            response = await apiService.post(`${endpoint}`, { code: userId })\r\n        }\r\n\r\n        if (response) {\r\n            manageDialog(false);\r\n            Router.push('/home');\r\n        }\r\n    }\r\n\r\n    return (\r\n        <Modal show={isopen} onHide={handleClose}>\r\n            <div className=\"text-center p-2\">\r\n                <FontAwesomeIcon\r\n                    icon={faExclamationTriangle}\r\n                    size=\"5x\"\r\n                    color=\"indianRed\"\r\n                    style={{\r\n                        background: \"#d6deec\",\r\n                        padding: \"19px\",\r\n                        borderRadius: \"50%\",\r\n                        width: \"100px\",\r\n                        height: \"100px\"\r\n                    }}\r\n                />\r\n            </div>\r\n            <Modal.Body><b>{t(\"AreyousureyouwishtoleavetheKnowledgePlatform\")}</b></Modal.Body>\r\n            <Modal.Footer>\r\n                <Button variant=\"secondary\" onClick={handleClose}>\r\n                {t(\"No\")}\r\n          </Button>\r\n                <Button variant=\"danger\" onClick={accountDeleteHandlder}>\r\n                {t(\"YesDeleteMe\")}\r\n          </Button>\r\n            </Modal.Footer>\r\n        </Modal>\r\n    )\r\n}\r\n\r\n\r\n\r\nexport default Confirmation;"], "names": ["isopen", "Confirmation", "manageDialog", "userId", "endpoint", "props", "t", "useTranslation", "handleClose", "accountDeleteHandlder", "response", "apiService", "remove", "post", "code", "Router", "Modal", "show", "onHide", "div", "className", "FontAwesomeIcon", "icon", "faExclamationTriangle", "size", "color", "style", "background", "padding", "borderRadius", "width", "height", "Body", "b", "Footer", "<PERSON><PERSON>", "variant", "onClick"], "sourceRoot": "", "ignoreList": []}