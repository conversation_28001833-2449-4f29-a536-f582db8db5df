"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/_app",{

/***/ "(pages-dir-browser)/./components/hoc/AuthSync.tsx":
/*!*************************************!*\
  !*** ./components/hoc/AuthSync.tsx ***!
  \*************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(pages-dir-browser)/./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(pages-dir-browser)/./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react_redux__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! react-redux */ \"(pages-dir-browser)/./node_modules/react-redux/dist/react-redux.mjs\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/router */ \"(pages-dir-browser)/./node_modules/next/router.js\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_router__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _common_CustomLoader__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../common/CustomLoader */ \"(pages-dir-browser)/./components/common/CustomLoader.tsx\");\n/* harmony import */ var _services_authService__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../../services/authService */ \"(pages-dir-browser)/./services/authService.tsx\");\n//Import Library\n\n\n\n\n\n\n// Public routes used to handle layouts\nconst publicRoutes = [\n    \"/home\",\n    \"/login\",\n    // \"/admin/login\",\n    \"/forgot-password\",\n    \"/reset-password/[passwordToken]\",\n    \"/declarationform/[...routes]\"\n];\n// Gets the display name of a JSX component for dev tools\nconst getDisplayName = (Component1)=>Component1.displayName || Component1.name || \"Component\";\nfunction withAuthSync(WrappedComponent) {\n    class MainComponent extends react__WEBPACK_IMPORTED_MODULE_1__.Component {\n        static async getInitialProps(ctx) {\n            const componentProps = WrappedComponent.getInitialProps && await WrappedComponent.getInitialProps(ctx);\n            if (ctx.ctx && ctx.ctx.req && ctx.ctx.req.cookies) {\n                const objCookies = ctx.ctx && ctx.ctx.req && ctx.ctx.req.cookies ? ctx.ctx.req.cookies : {};\n                componentProps.objCookies = objCookies;\n                return {\n                    ...componentProps\n                };\n            } else {\n                return {\n                    ...componentProps\n                };\n            }\n        }\n        async componentDidMount() {\n            const { route } = this.props.router;\n            next_router__WEBPACK_IMPORTED_MODULE_2___default().events.on(\"routeChangeComplete\", (url)=>{\n                if (url === \"/home\") {\n                    this.setState({\n                        isLoading: false\n                    });\n                }\n            });\n            // Check if user has actively logged in (not just copied cookie)\n            const hasActiveLogin = _services_authService__WEBPACK_IMPORTED_MODULE_4__[\"default\"].hasActiveLogin();\n            if (!hasActiveLogin && publicRoutes.indexOf(route) === -1) {\n                console.log(\"User has valid session but no active login - redirecting to login\");\n                // Clear any existing session storage to be safe\n                if (true) {\n                    sessionStorage.clear();\n                }\n                this.props.router.push(\"/home\");\n                return;\n            }\n            // Use backend session verification instead of cookie parsing\n            try {\n                const sessionData = await _services_authService__WEBPACK_IMPORTED_MODULE_4__[\"default\"].verifySession();\n                if (!sessionData.isAuthenticated && publicRoutes.indexOf(route) === -1) {\n                    this.props.router.push(\"/home\");\n                    return;\n                }\n                this.setState({\n                    isLoading: false,\n                    cookie: sessionData.isAuthenticated && hasActiveLogin ? \"authenticated\" : null\n                });\n            } catch (error) {\n                console.error(\"Session verification failed:\", error);\n                if (publicRoutes.indexOf(route) === -1) {\n                    this.props.router.push(\"/home\");\n                    return;\n                }\n                this.setState({\n                    isLoading: false\n                });\n            }\n        }\n        componentWillUnmount() {\n            next_router__WEBPACK_IMPORTED_MODULE_2___default().events.off(\"routeChangeComplete\", ()=>null);\n        }\n        render() {\n            const { router } = this.props;\n            const isPublicRoute = publicRoutes.indexOf(router.route) > -1;\n            return this.state.isLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_common_CustomLoader__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {}, void 0, false, {\n                fileName: \"D:\\\\rki\\\\frontend\\\\components\\\\hoc\\\\AuthSync.tsx\",\n                lineNumber: 111,\n                columnNumber: 37\n            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(WrappedComponent, {\n                isLoading: this.state.isLoading,\n                isPublicRoute: isPublicRoute,\n                ...this.props\n            }, void 0, false, {\n                fileName: \"D:\\\\rki\\\\frontend\\\\components\\\\hoc\\\\AuthSync.tsx\",\n                lineNumber: 112,\n                columnNumber: 9\n            }, this);\n        }\n        constructor(props){\n            super(props);\n            this.state = {\n                isLoading: true,\n                cookie: null\n            };\n        }\n    }\n    MainComponent.displayName = \"withAuthSync(\".concat(getDisplayName(WrappedComponent), \")\");\n    return (0,react_redux__WEBPACK_IMPORTED_MODULE_5__.connect)((state)=>state)(MainComponent);\n}\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (withAuthSync);\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-browser)/./components/hoc/AuthSync.tsx\n"));

/***/ })

});