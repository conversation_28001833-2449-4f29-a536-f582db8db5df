{"version": 3, "file": "static/chunks/pages/institution/InstitutionFocalPoint-5bd93a9bd79a55e4.js", "mappings": "2HAGA,OAAMA,EACFC,gBAAgBC,CAAa,CAAEC,CAAa,CAAE,CAG1C,IAAMC,EAAoB,EAAE,CAM5B,OALAF,EAAOG,OAAO,CAAC,IACPF,GAAuE,GAAhEG,MAAM,CAAC,GAAgBC,EAAMC,GAAG,GAAKC,EAAMC,KAAK,EAAEC,MAAM,EAC/DP,EAAWQ,IAAI,CAACH,EAAMC,KAAK,CAEnC,GACON,CACX,CAEA,MAAMS,kBAAkBC,CAAc,CAAEC,CAAgB,CAAE,CACtDD,EAAQT,OAAO,CAAC,MAAOW,IACnB,IAAIC,EAAW,MAAMC,EAAAA,CAAUA,CAACC,GAAG,CAAC,UAAiB,OAAPH,IAC1CC,EAASG,kBAAkB,CAACT,MAAM,EAAE,CACpCM,EAASG,kBAAkB,CAAGH,EAASG,kBAAkB,CAACd,MAAM,CAC5D,GAAiBe,EAAOC,aAAa,GAAKP,GAE9C,MAAMG,EAAAA,CAAUA,CAACK,KAAK,CAAC,UAAiB,OAAPP,GAAUC,GAEnD,EACJ,CAEQO,kBAAkBC,CAAkB,CAAEH,CAAkB,CAAE,CAI9D,QAAOI,EAHgBpB,MAAM,CAAC,GACnBqB,EAAGL,aAAa,GAAKA,GAEpBX,MAAM,CAGdiB,EAHiB,OAAO,QAGPN,CAAkB,CAAEO,CAAqB,CAAE,CAChE,MAAO,CACHC,gBAAiBD,EACjBP,cAAeA,EACfS,OAAQ,iBACZ,CACJ,CAEA,MAAcC,YAAYhB,CAAW,CAAEiB,CAAe,CAAEC,CAAc,CAAE,CACpE,IAAIjB,EAAgB,CAAC,EAWrB,OAVIgB,GAAaC,EACF,MAAMhB,EADM,CACIA,CAACC,GAAG,CAAE,SAAS,CACtCgB,MAAO,CACHC,MAAOH,EACPI,SAAUH,CACd,CACJ,GAEW,MAAMhB,EAAAA,CAAUA,CAACC,GAAG,CAAC,UAAiB,OAAPH,GAGlD,CAEA,MAAMsB,gBAAgBC,CAAY,CAAEjB,CAAkB,CAAEO,CAAqB,CAAE,CAC3EU,EAAMlC,OAAO,CAAC,MAAOmC,IACjB,IAAMxB,EAASwB,EAAKhC,GAAG,CACvB,GAAIQ,EAAQ,CACR,IAAIC,EAAW,MAAM,IAAI,CAACe,WAAW,CAAChB,GAClCC,IACI,EAAUG,IADJ,cACsB,GAAEH,EAASG,kBAAkB,CAAG,IAChEH,EAASG,kBAAkB,CAAGH,EAASG,kBAAkB,CAACqB,GAAG,CAAEpB,IACvDA,EAAOC,aAAa,GAAKA,GAAiBD,YAA8B,GAAvBU,MAAM,GACvDV,EAAOU,MAAM,CAAG,mBAEbV,IAEP,IAAK,CAACG,iBAAiB,CAACP,EAASG,kBAAkB,EAAI,EAAE,CAAEE,IAC3DL,EAASG,UADkE,QAChD,CAACR,IAAI,CAAC,IAAI,CAACgB,gBAAgB,CAACN,EAAeO,IAE1E,MAAMX,EAAAA,CAAUA,CAACK,KAAK,CAAC,UAAiB,OAAPP,GAAUC,GAEnD,MAEI,CAFG,GAEC,CAACyB,sBAAsB,CAACF,EAAKJ,KAAK,CAAEI,EAAKH,QAAQ,CAAEf,EAAeO,EAE9E,EACJ,CAEA,MAAaa,uBAAuBN,CAAU,CAAEC,CAAa,CAAEf,CAAkB,CAAEO,CAAqB,CAAE,CAEtG,IAAIZ,EAAW,MAAMC,EAAAA,CAAUA,CAACC,GAAG,CAAE,SAAS,CAAEgB,MADlC,OAAEC,WAAOC,CAAS,CACsB,GAClDpB,GAAYA,EAAS0B,IAAI,CAAC,EAAE,EAAE,CAE9B1B,CADAA,EAAWA,EAAS0B,IAAI,CAAC,IAChBvB,kBAAkB,CAACR,IAAI,CAAC,IAAI,CAACgB,gBAAgB,CAACN,EAAeO,IAC1D,MAAMX,EAAAA,CAAUA,CAACK,KAAK,CAAC,UAAuB,OAAbN,EAAST,GAAG,EAAIS,GAErE,CACJ,CAEA,MAAe,IAAIjB,mBAAmBA,EAAC,6PCvFhC,IAAM4C,EAAoBC,CAAAA,EAAAA,EAAAA,CAAAA,CAAoBA,CAAC,CACpDC,sBAAuB,KACjBC,EAAMC,WAAW,IAAID,EAAMC,WAAW,CAACC,WAAW,IAAIF,EAAMC,WAAW,CAACC,WAAW,CAAC,aAAa,CAKvGC,CALyG,kBAKrF,mBACtB,GAAG,EAEkCL,CAAAA,EAAAA,EAAAA,CAAAA,CAAoBA,CAAC,CACxDC,sBAAuB,KACjBC,EAAMC,WAAW,IAAID,EAAMC,WAAW,CAACC,WAAW,IAAIF,EAAMC,WAAW,CAACC,WAAW,CAAC,aAAa,CAKvGC,CALyG,kBAKrF,wBACpBC,iBAAkB,IAAM,UAACC,EAAAA,OAAIA,CAAAA,CAAAA,EAC/B,GAAG,EAE+BP,CAAAA,EAAAA,EAAAA,CAAAA,CAAoBA,CAAC,CACrDC,sBAAuB,CAACC,EAAOM,KAC7B,GAAIN,EAAMC,WAAW,EAAID,EAAMC,WAAW,CAACC,WAAW,EAAE,GAClDF,EAAMC,WAAW,CAACC,WAAW,CAAC,aAAa,CAC7C,CAD+C,MACxC,OAEP,GAAIF,EAAMC,WAAW,CAACC,WAAW,CAAC,aAAa,EAAE,EACrCA,WAAW,EAAII,EAAMJ,WAAW,CAACT,IAAI,EAAIa,EAAMJ,WAAW,CAACT,IAAI,GAAKO,EAAMP,IAAI,CAAChC,GAAG,CAC1F,CAD4F,MACrF,CAGb,CAEF,MAAO,EACT,EACA0C,mBAAoB,oBACtB,GAAG,EAEmCL,CAAAA,EAAAA,EAAAA,CAAAA,CAAoBA,CAAC,CACzDC,sBAAuB,CAACC,EAAOM,KAC7B,GAAIN,EAAMC,WAAW,EAAID,EAAMC,WAAW,CAACC,WAAW,EAAE,GAClDF,EAAMC,WAAW,CAACC,WAAW,CAAC,aAAa,CAC7C,CAD+C,MACxC,OAEP,GAAIF,EAAMC,WAAW,CAACC,WAAW,CAAC,aAAa,EAAE,EACrCA,WAAW,EAAII,EAAMJ,WAAW,CAACT,IAAI,EAAIa,EAAMJ,WAAW,CAACT,IAAI,GAAKO,EAAMP,IAAI,CAAChC,GAAG,CAC1F,CAD4F,MACrF,CAGb,CAEF,MAAO,EACT,EACA0C,mBAAoB,yBACpBC,iBAAkB,IAAM,UAACC,EAAAA,OAAIA,CAAAA,CAAAA,EAC/B,GAAG,EAEoCP,CAAAA,EAAAA,EAAAA,CAAAA,CAAoBA,CAAC,CAC1DC,sBAAuB,KACjBC,EAAMC,WAAW,IAAID,EAAMC,WAAW,CAACM,MAAM,IAAIP,EAAMC,WAAW,CAACM,MAAM,CAAC,WAAW,CAK3FJ,CAL6F,kBAKzE,yBACtB,GAAG,EAEiCL,CAAAA,EAAAA,EAAAA,CAAAA,CAAoBA,CAAC,CACvDC,sBAAuB,CAACC,EAAOM,KAC7B,GAAIN,EAAMC,WAAW,EAAID,EAAMC,WAAW,CAACO,uBAAuB,EAAE,GAC9DR,EAAMC,WAAW,CAACO,uBAAuB,CAAC,aAAa,CACzD,CAD2D,MACpD,OAEP,GAAIR,EAAMC,WAAW,CAACO,uBAAuB,CAAC,aAAa,EAAE,EAEnDN,WAAW,EACjBI,EAAMJ,WAAW,CAACT,IAAI,EACtBa,EAAMJ,WAAW,CAACT,IAAI,GAAKO,EAAMP,IAAI,CAAChC,GAAG,CAEzC,CADA,MACO,CAGb,CAEF,OAAO,CACT,EACA0C,mBAAoB,sBACtB,GAAG,EAEYN,iBAAiBA,EAAC,CChGjC,2CACA,qCACA,WACA,OAAe,EAAQ,KAA0D,CACjF,EACA,SAFsB", "sources": ["webpack://_N_E/./services/invitation.service.ts", "webpack://_N_E/./pages/institution/permission.tsx", "webpack://_N_E/?4224"], "sourcesContent": ["//Import services/components\r\nimport apiService from \"./apiService\";\r\n\r\nclass InvitationService {\r\n    arrayDifference(array1: any[], array2: any[]) {\r\n        // returns array\r\n        // element present in array one but not in array 2\r\n        const difference: any[] = [];\r\n        array1.forEach((ar1El: any) => {\r\n            if (array2.filter((ar2El: any) => ar2El._id === ar1El.value).length === 0) {\r\n                difference.push(ar1El.value);\r\n            }\r\n        });\r\n        return difference;\r\n    }\r\n\r\n    async deleteInvitations(userIds: any[], instituteId: any) {\r\n        userIds.forEach(async (userId: any) => {\r\n            let userData = await apiService.get(`/users/${userId}`);\r\n            if (userData.institutionInvites.length) {\r\n                userData.institutionInvites = userData.institutionInvites.filter(\r\n                    (invite: any) => invite.institutionId !== instituteId\r\n                );\r\n                await apiService.patch(`/users/${userId}`, userData);\r\n            }\r\n        });\r\n    }\r\n\r\n    private isDuplicateInvite(userInvites: any[], institutionId: any) {\r\n        let dups = userInvites.filter((ui: any) => {\r\n            return ui.institutionId === institutionId;\r\n        });\r\n        return dups.length ? true : false;\r\n    }\r\n\r\n    private getNewInviteMeta(institutionId: any, institutionTitle: any) {\r\n        return {\r\n            institutionName: institutionTitle,\r\n            institutionId: institutionId,\r\n            status: \"Request Pending\",\r\n        };\r\n    }\r\n\r\n    private async getUserData(userId: any, userEmail?: any, userName?: any) {\r\n        let userData: any = {};\r\n        if (userEmail && userName) {\r\n            userData = await apiService.get(`/users`, {\r\n                query: {\r\n                    email: userEmail,\r\n                    username: userName,\r\n                },\r\n            });\r\n        } else {\r\n            userData = await apiService.get(`/users/${userId}`);\r\n        }\r\n        return userData;\r\n    }\r\n\r\n    async sendInvitations(users: any[], institutionId: any, institutionTitle: any) {\r\n        users.forEach(async (user: any) => {\r\n            const userId = user._id;\r\n            if (userId) {\r\n                let userData = await this.getUserData(userId);\r\n                if (userData) {\r\n                    if (!userData.institutionInvites) userData.institutionInvites = [];\r\n                    userData.institutionInvites = userData.institutionInvites.map((invite: any) => {\r\n                        if (invite.institutionId === institutionId && invite.status === \"Rejected\") {\r\n                            invite.status = \"Request Pending\";\r\n                        }\r\n                        return invite;\r\n                    });\r\n                    if (!this.isDuplicateInvite(userData.institutionInvites || [], institutionId)) {\r\n                        userData.institutionInvites.push(this.getNewInviteMeta(institutionId, institutionTitle));\r\n                    }\r\n                    await apiService.patch(`/users/${userId}`, userData);\r\n                }\r\n            } else {\r\n                //means new user\r\n                this.inviteNewUserWithEmail(user.email, user.username, institutionId, institutionTitle);\r\n            }\r\n        });\r\n    }\r\n\r\n    public async inviteNewUserWithEmail(email: any, username: any, institutionId: any, institutionTitle: any) {\r\n        const query = { email, username };\r\n        let userData = await apiService.get(`/users`, { query });\r\n        if (userData && userData.data[0]) {\r\n            userData = userData.data[0];\r\n            userData.institutionInvites.push(this.getNewInviteMeta(institutionId, institutionTitle));\r\n            const res = await apiService.patch(`/users/${userData._id}`, userData);\r\n        }\r\n    }\r\n}\r\n\r\nexport default new InvitationService();\r\n", "//Import Library\r\nimport React from 'react';\r\nimport connectedAuthWrapper from 'redux-auth-wrapper/connectedAuthWrapper';\r\n\r\n//Import services/components\r\nimport R403 from \"../r403\";\r\n\r\nexport const canAddInstitution = connectedAuthWrapper({\r\n  authenticatedSelector: (state) => {\r\n    if (state.permissions && state.permissions.institution && state.permissions.institution['create:any']) {\r\n      return true;\r\n    }\r\n    return false;\r\n  },\r\n  wrapperDisplayName: 'CanAddInstitution',\r\n});\r\n\r\nexport const canAddInstitutionForm = connectedAuthWrapper({\r\n  authenticatedSelector: (state) => {\r\n    if (state.permissions && state.permissions.institution && state.permissions.institution['create:any']) {\r\n      return true;\r\n    }\r\n    return false;\r\n  },\r\n  wrapperDisplayName: 'CanAddInstitutionForm',\r\n  FailureComponent: () => <R403/>\r\n});\r\n\r\nexport const canEditInstitution = connectedAuthWrapper({\r\n  authenticatedSelector: (state, props) => {\r\n    if (state.permissions && state.permissions.institution) {\r\n      if (state.permissions.institution['update:any']) {\r\n        return true;\r\n      } else { //if update:own\r\n        if (state.permissions.institution['update:own']) {\r\n          if (props.institution && props.institution.user && props.institution.user === state.user._id) {\r\n            return true;\r\n          }\r\n        }\r\n      }\r\n    }\r\n    return false;\r\n  },\r\n  wrapperDisplayName: 'CanEditInstitution',\r\n});\r\n\r\nexport const canEditInstitutionForm = connectedAuthWrapper({\r\n  authenticatedSelector: (state, props) => {\r\n    if (state.permissions && state.permissions.institution) {\r\n      if (state.permissions.institution['update:any']) {\r\n        return true;\r\n      } else { //if update:own\r\n        if (state.permissions.institution['update:own']) {\r\n          if (props.institution && props.institution.user && props.institution.user === state.user._id) {\r\n            return true;\r\n          }\r\n        }\r\n      }\r\n    }\r\n    return false;\r\n  },\r\n  wrapperDisplayName: 'CanEditInstitutionForm',\r\n  FailureComponent: () => <R403/>\r\n});\r\n\r\nexport const canViewDiscussionUpdate = connectedAuthWrapper({\r\n  authenticatedSelector: (state) => {\r\n    if (state.permissions && state.permissions.update && state.permissions.update['read:any']) {\r\n      return true;\r\n    }\r\n    return false;\r\n  },\r\n  wrapperDisplayName: 'CanViewDiscussionUpdate',\r\n});\r\n\r\nexport const canManageFocalPoints = connectedAuthWrapper({\r\n  authenticatedSelector: (state, props) => {\r\n    if (state.permissions && state.permissions.institution_focal_point) {\r\n      if (state.permissions.institution_focal_point[\"update:any\"]) {\r\n        return true;\r\n      } else {\r\n        if (state.permissions.institution_focal_point[\"update:own\"]) {\r\n          if (\r\n            props.institution &&\r\n            props.institution.user &&\r\n            props.institution.user === state.user._id\r\n          ) {\r\n            return true;\r\n          }\r\n        }\r\n      }\r\n    }\r\n    return false;\r\n  },\r\n  wrapperDisplayName: \"canManageFocalPoints\",\r\n});\r\n\r\nexport default canAddInstitution;", "\n    (window.__NEXT_P = window.__NEXT_P || []).push([\n      \"/institution/InstitutionFocalPoint\",\n      function () {\n        return require(\"private-next-pages/institution/InstitutionFocalPoint.tsx\");\n      }\n    ]);\n    if(module.hot) {\n      module.hot.dispose(function () {\n        window.__NEXT_P.push([\"/institution/InstitutionFocalPoint\"])\n      });\n    }\n  "], "names": ["InvitationService", "arrayDifference", "array1", "array2", "difference", "for<PERSON>ach", "filter", "ar2El", "_id", "ar1El", "value", "length", "push", "deleteInvitations", "userIds", "instituteId", "userId", "userData", "apiService", "get", "institutionInvites", "invite", "institutionId", "patch", "isDuplicateInvite", "userInvites", "dups", "ui", "getNewInviteMeta", "institutionTitle", "institutionName", "status", "getUserData", "userEmail", "userName", "query", "email", "username", "sendInvitations", "users", "user", "map", "inviteNewUserWithEmail", "data", "canAddInstitution", "connectedAuthWrapper", "authenticatedSelector", "state", "permissions", "institution", "wrapperDisplayName", "FailureComponent", "R403", "props", "update", "institution_focal_point"], "sourceRoot": "", "ignoreList": []}