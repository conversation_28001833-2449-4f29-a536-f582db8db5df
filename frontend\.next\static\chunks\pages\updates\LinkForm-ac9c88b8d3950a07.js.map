{"version": 3, "file": "static/chunks/pages/updates/LinkForm-ac9c88b8d3950a07.js", "mappings": "+EACA,4CACA,oBACA,WACA,OAAe,EAAQ,KAAyC,CAChE,EACA,SAFsB,iGCEtB,IAAMA,EAAwBC,EAAAA,OAAb,GAA6B,CAAC,GAK5CC,IALyB,IAAoB,WAC9CC,CAAS,UACTC,CAAQ,CACRC,GAAIC,EAAY,KAAK,CACrB,GAAGC,EACJ,GAEC,OAAO,EADIC,CAAAA,EAAAA,EAAAA,EAAAA,CAAkBA,CAACJ,EAAU,aACpBK,CAAAA,EAAAA,EAAAA,GAAAA,CAAIA,CAACH,EAAW,CAClCJ,IAAKA,EACLC,UAAWO,IAAWP,EAAWC,GACjC,GAAGG,CACL,EACF,GACAP,EAASW,WAAW,CAAG,WCbvB,IAAMC,EAA0BX,EAAAA,SAAb,CAA6B,CAAC,GAK9CC,MAL2B,EAAoB,WAChDC,CAAS,UACTC,CAAQ,CACRC,GAAIC,EAAY,KAAK,CACrB,GAAGC,EACJ,GAEC,OAAO,EADIC,CAAAA,EAAAA,EAAAA,EAAAA,CAAkBA,CAACJ,EAAU,eACpBK,CAAAA,EAAAA,EAAAA,GAAAA,CAAIA,CAACH,EAAW,CAClCJ,IAAKA,EACLC,UAAWO,IAAWP,EAAWC,GACjC,GAAGG,CAAK,EAEZ,GACAK,EAAWD,WAAW,CAAG,4BCXzB,IAAME,EAA0BZ,EAAAA,SAAb,CAA6B,CAAC,GAM9CC,MAN2B,EAAoB,UAChDE,CAAQ,WACRD,CAAS,CAETE,CADA,EACIC,EAAY,KAAK,CACrB,GAAGC,EACJ,GACOO,EAASN,CAAAA,EAAAA,EAAAA,EAAAA,CAAkBA,CAACJ,EAAU,eACtCW,EAAeC,CAAAA,EAAAA,EAAAA,OAAAA,CAAOA,CAAC,IAAO,EAClCC,mBAAoBH,EACtB,EAAI,CAACA,EAAO,EACZ,MAAoBL,CAAb,EAAaA,EAAAA,GAAAA,CAAIA,CAACS,EAAP,CAAwBA,CAACC,QAAQ,CAAE,CACnDC,MAAOL,EACPM,SAAuBZ,CAAAA,EAAAA,EAAAA,GAAAA,CAAIA,CAACH,EAAP,CACnBJ,IAAKA,EACL,GAAGK,CAAK,CACRJ,UAAWO,IAAWP,EAAWW,EACnC,EACF,EACF,GACAD,EAAWF,GAJgBD,QAIL,CAAG,aCtBzB,IAAMY,EAAuBrB,EAAAA,MAAb,IAA6B,CAC7C,CACA,EAMGC,GARwB,KAE1B,CACCE,UAAQ,WACRD,CAAS,SACToB,CAAO,CACPlB,GAAIC,EAAY,KAAK,CACrB,GAAGC,EACJ,GACOO,EAASN,CAAAA,EAAAA,EAAAA,EAAAA,CAAkBA,CAACJ,EAAU,YAC5C,MAAoBK,CAAb,EAAaA,EAAAA,GAAAA,CAAIA,CAACH,EAAP,CAChBJ,IAAKA,EACLC,UAAWO,IAAWa,EAAU,GAAaA,MAAAA,CAAVT,EAAO,EAArBJ,GAAgC,OAARa,CAX0G,EAW9FT,EAAQX,GACjE,GAAGI,CAAK,EAEZ,GACAe,EAAQX,WAAW,CAAG,UChBtB,IAAMa,EAA8BvB,EAAAA,UAAgB,CAAC,EAA9B,CAKpBC,QALmD,CACpDC,CADgC,UACvB,UACTC,CAAQ,CACRC,GAAIC,EAAY,KAAK,CACrB,GAAGC,EACJ,GAEC,OADAH,EAAWI,CAAAA,EAAAA,EAAAA,EAAAA,CAAkBA,CACX,EADsB,oBACpBC,CAAAA,EAAAA,EAAAA,GAAAA,CAAIA,CAACH,EAAW,CAClCJ,IAAKA,EACLC,UAAWO,IAAWP,EAAWC,GACjC,GAAGG,CAAK,EAEZ,GACAiB,EAAeb,WAAW,CAAG,iBCb7B,IAAMc,EAAwBxB,EAAAA,OAAb,GAA6B,CAAC,GAK5CC,IALyB,IAAoB,WAC9CC,CAAS,UACTC,CAAQ,CACRC,GAAIC,EAAY,GAAG,CACnB,GAAGC,EACJ,GAEC,OADAH,EAAWI,CAAAA,EAAAA,EAAAA,EAAAA,CAAkBA,CAACJ,EAAU,aACpBK,CAAAA,EAAAA,EAAAA,GAAAA,CAAIA,CAACH,EAAW,CAClCJ,IAAKA,EACLC,UAAWO,IAAWP,EAAWC,GACjC,GAAGG,CACL,EACF,GACAkB,EAASd,WAAW,CAAG,0BCZvB,IAAMe,EAAgBC,CAAAA,EAAAA,EAAAA,CAAAA,CAAgBA,CAAC,MACjCC,EAA4B3B,EAAAA,UAAgB,CAAC,GAKhDC,QALiD,WAClDC,CAAS,UACTC,CAAQ,CACRC,GAAIC,EAAYoB,CAAa,CAC7B,GAAGnB,EACJ,GAEC,OADAH,EAAWI,CAAAA,EAAAA,EAAAA,EAAAA,CAAkBA,CACX,EADsB,iBACpBC,CAAAA,EAAAA,EAAAA,GAAAA,CAAIA,CAACH,EAAW,CAClCJ,IAAKA,EACLC,UAAWO,IAAWP,EAAWC,GACjC,GAAGG,CACL,EACF,GACAqB,EAJyBlB,WAID,CAAG,eCf3B,IAAMmB,EAAwB5B,EAAAA,OAAb,GAA6B,CAAC,GAK5CC,IALyB,IAAoB,CAC9CC,WAAS,UACTC,CAAQ,CACRC,GAAIC,EAAY,GAAG,CACnB,GAAGC,EACJ,GAEC,OADAH,EAAWI,CAAAA,EAAAA,EAAAA,EAAAA,CAAkBA,CAACJ,EAAU,aACpBK,CAAAA,EAAAA,EAAAA,GAAAA,CAAIA,CAACH,EAAW,CAClCJ,IAAKA,EACLC,UAAWO,IAAWP,EAAWC,GACjC,GAAGG,CAAK,EAEZ,GACAsB,EAJyBnB,WAIL,CAAG,WCZvB,IAAMoB,EAAgBH,CAAAA,EAAAA,EAAAA,CAAAA,CAAgBA,CAAC,MACjCI,EAAyB9B,EAAAA,QAAb,EAA6B,CAAC,GAK7CC,KAL0B,GAAoB,WAC/CC,CAAS,UACTC,CAAQ,CACRC,GAAIC,EAAYwB,CAAa,CAC7B,GAAGvB,EACJ,GAEC,OADAH,EAAWI,CAAAA,EAAAA,EAAAA,EAAAA,CAAkBA,CAACJ,EAAU,cACpBK,CAAAA,EAAAA,EAAAA,GAAAA,CAAIA,CAACH,EAAW,CAClCJ,IAAKA,EACLC,UAAWO,IAAWP,EAAWC,GACjC,GAAGG,CAAK,EAEZ,GACAwB,EAAUpB,WAAW,CAAG,YCNxB,IAAMqB,EAAoB/B,EAAAA,GAAb,OAA6B,CAAC,GAAnB,QAAoB,CAC1CG,UAAQ,WACRD,CAAS,IACT8B,CAAE,MACFC,CAAI,QACJC,CAAM,CACNC,QAAO,CAAK,UACZf,CAAQ,CAERhB,CADA,EACIC,EAAY,KAAK,CACrB,GAAGC,EACJ,GACOO,EAASN,CAAAA,EAAAA,EAAAA,EAAAA,CAAkBA,CAACJ,EAAU,QAC5C,MAAoBK,CAAAA,EAAAA,EAAAA,GAAAA,CAAIA,CAACH,EAAP,CAChBJ,IAAKA,EACL,GAAGK,CAAK,CACRJ,UAAWO,IAAWP,EAAWW,EAAQmB,GAAM,MAAS,GAAnCvB,GAAmC,CAAHuB,GAAMC,GAAQ,QAAa,OAALA,GAAQC,GAAU,UAAiB,OAAPA,IACvGd,IATyJ,KAS/Ie,EAAoB3B,CAAAA,EAAAA,EAAb,GAAaA,CAAIA,CAACT,EAAU,CAC3CqB,GAD0B,MAAerB,CAE3C,GAAKqB,CACP,EACF,GACAW,EAAKrB,WAAW,CAAG,OACnB,MAAe0B,OAAOC,MAAM,CAACN,EAAM,CACjCO,INhBajB,CMgBRA,CACLkB,KNjBoBlB,CKDPS,CCkBNA,CACPU,EAFYnB,KDjBUS,EAAC,CCmBbH,CACVc,CAFgBX,ITpBH/B,CSsBPA,CACN2C,GHrByBf,EDFZH,CLAQzB,CSwBrB4C,CTxBsB,GSsBR5C,CFtBD6B,CFAQJ,CIyBrBoB,CJzBsB,GIuBRpB,EFvBOI,CLSRhB,CKTS,CE0BtBiC,EAFcjB,KRxBDjB,CQ0BLA,CACRmC,CPlBwB,GOgBNlC,IRzBKD,EAAC,CGAXY,CK2BDA,CADMZ,CAElB,EAAC,SL5B0BY,EAAC,GK2BFA,gFCb5B,IAAMwB,EAAwBC,CAAAA,EAAAA,EAAAA,UAAAA,CAAUA,CAA8C,CAAC1C,EAAOL,KAC5F,GAAM,UAAEmB,CAAQ,UAAE6B,CAAQ,cAAEC,CAAY,WAAEhD,CAAS,YAAEiD,CAAU,CAAEC,eAAa,CAAE,GAAGC,EAAM,CAAG/C,EAGtFgD,EAAmBC,EAAAA,EAAU,GAAGC,KAAK,CAAC,CAAC,GAE7C,MACE,UAACC,EAAAA,EAAMA,CAAAA,CACLL,cAAeA,GAAiB,CAAC,EACjCE,iBAAkBA,EAClBL,SAAU,CAACS,EAA6BC,KAEtC,IAAMC,EAAuB,CAC3BC,eAAgB,KAAO,EACvBC,gBAAiB,KAAO,EACxBC,cAAe,KACfC,OAAQ,KACRC,YAAa,IAAIC,MAAM,UACvBC,SAAS,EACTC,WAAY,GACZC,kBAAkB,EAClBC,WAAY,EACZC,WAAW,EACXC,UAAWC,KAAKC,GAAG,GACnBC,KAAM,SACNC,mBAAoB,IAAM,GAC1BC,qBAAsB,KAAM,EAC5BC,QAAS,KAAO,CAClB,EAEI7B,GAEFA,EAASW,EAAWF,EAAQC,CAFhB,CAIhB,EACC,GAAGN,CAAI,UAEN0B,GACA,UAACC,EAAAA,EAAIA,CAAAA,CACH/E,IAAKA,EACLgD,SAAU8B,EAAYE,YAAY,CAClC/B,aAAcA,EACdhD,UAAWA,EACXiD,WAAYA,WAES,YAApB,OAAO/B,EAA0BA,EAAS2D,GAAe3D,KAKpE,GAEA2B,EAAsBrC,WAAW,CAAG,wBAEpC,MAAeqC,qBAAqBA,EAAC,2JCArC,MAtEiB,IACf,GAAM,GAAEmC,CAAC,CAAE,CAAGC,CAAAA,EAAAA,EAAAA,EAAAA,CAAcA,CAAC,MAqEhBC,IApEP,CAAEC,GAoEaD,EAAC,CApEV,yBAAEE,CAAuB,YAAEC,CAAU,SAAEC,CAAO,CAAE,CAAGlF,EAC/D,MACE,UAACmF,EAAAA,CAASA,CAAAA,CAACvF,UAAU,WAAWwF,KAAK,aACnC,UAAC3D,EAAAA,CAAIA,CAAAA,UACH,UAACgB,EAAAA,CAAqBA,CAAAA,CAACE,SAAU,KAAO,WACtC,WAAClB,EAAAA,CAAIA,CAACU,IAAI,YACP4C,GAAQA,EAAKM,GAAG,CAAC,CAACC,EAAWC,IAE1B,UAACC,MAAAA,UACC,WAACC,EAAAA,CAAGA,CAAAA,WACF,UAACC,EAAAA,CAAGA,CAAAA,UACF,WAAChB,EAAAA,CAAIA,CAACiB,KAAK,YACT,UAACjB,EAAAA,CAAIA,CAACkB,KAAK,EAAChG,UAAU,0BAAkBgF,EAAE,kBAC1C,UAACF,EAAAA,CAAIA,CAACmB,OAAO,EACbC,KAAK,QACLC,GAAG,YACH1B,KAAK,OACLxD,MAAOyE,EAAKU,KAAK,CACjBC,QAAQ,IACRC,SAAU,GAAOlB,EAAwBmB,EAAGZ,KAC5C,UAACb,EAAAA,CAAIA,CAACmB,OAAO,CAACO,QAAQ,EAAC/B,KAAK,mBAC3BO,EAAE,iCAIP,UAACc,EAAAA,CAAGA,CAAAA,UACF,WAAChB,EAAAA,CAAIA,CAACiB,KAAK,YACT,UAACjB,EAAAA,CAAIA,CAACkB,KAAK,EAAChG,UAAU,0BAAkBgF,EAAE,iBAC1C,UAACF,EAAAA,CAAIA,CAACmB,OAAO,EACXC,KAAK,OACLC,GAAG,OACH1B,KAAK,OACL4B,QAAQ,IACRpF,MAAOyE,EAAKP,IAAI,CAChBmB,SAAU,GAAOlB,EAAwBmB,EAAGZ,GAC5Cc,QAAQ,uEAEV,UAAC3B,EAAAA,CAAIA,CAACmB,OAAO,CAACO,QAAQ,EAAC/B,KAAK,mBAC3BO,EAAE,kCAIP,UAACc,EAAAA,CAAGA,CAAAA,UACF,UAAChB,EAAAA,CAAIA,CAACiB,KAAK,WACF,IAANJ,EACC,UAACC,MAAAA,CAAAA,GAEC,UAACc,EAAAA,CAAMA,CAAAA,CAACtF,QAAQ,YAAYuF,MAAO,CAAEC,UAAW,MAAO,EAAGC,QAAS,GAAOxB,EAAWkB,EAAGZ,YAAMX,EAAE,6BAShH,UAACa,EAAAA,CAAGA,CAAAA,UACF,UAACC,EAAAA,CAAGA,CAAAA,CAACgB,EAAE,IAACC,GAAG,aACT,UAACL,EAAAA,CAAMA,CAAAA,CAACtF,QAAQ,YAAYuF,MAAO,CAAEC,UAAW,OAAQI,aAAc,MAAO,EAAGH,QAASvB,WAAUN,EAAE,4BAQrH,iDCnFA,IAAMiC,EAAuBnH,QAAb,CAAaA,UAAF,GAAqB,CAAC,MACjDmH,EAAQzG,WAAW,CAAG,oBACtB,MAAeyG,OAAOA,EAAC", "sources": ["webpack://_N_E/?dc26", "webpack://_N_E/./node_modules/react-bootstrap/esm/CardBody.js", "webpack://_N_E/./node_modules/react-bootstrap/esm/CardFooter.js", "webpack://_N_E/./node_modules/react-bootstrap/esm/CardHeader.js", "webpack://_N_E/./node_modules/react-bootstrap/esm/CardImg.js", "webpack://_N_E/./node_modules/react-bootstrap/esm/CardImgOverlay.js", "webpack://_N_E/./node_modules/react-bootstrap/esm/CardLink.js", "webpack://_N_E/./node_modules/react-bootstrap/esm/CardSubtitle.js", "webpack://_N_E/./node_modules/react-bootstrap/esm/CardText.js", "webpack://_N_E/./node_modules/react-bootstrap/esm/CardTitle.js", "webpack://_N_E/./node_modules/react-bootstrap/esm/Card.js", "webpack://_N_E/./components/common/ValidationFormWrapper.tsx", "webpack://_N_E/./pages/updates/LinkForm.tsx", "webpack://_N_E/./node_modules/react-bootstrap/esm/CardHeaderContext.js"], "sourcesContent": ["\n    (window.__NEXT_P = window.__NEXT_P || []).push([\n      \"/updates/LinkForm\",\n      function () {\n        return require(\"private-next-pages/updates/LinkForm.tsx\");\n      }\n    ]);\n    if(module.hot) {\n      module.hot.dispose(function () {\n        window.__NEXT_P.push([\"/updates/LinkForm\"])\n      });\n    }\n  ", "\"use client\";\n\nimport * as React from 'react';\nimport classNames from 'classnames';\nimport { useBootstrapPrefix } from './ThemeProvider';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst CardBody = /*#__PURE__*/React.forwardRef(({\n  className,\n  bsPrefix,\n  as: Component = 'div',\n  ...props\n}, ref) => {\n  bsPrefix = useBootstrapPrefix(bsPrefix, 'card-body');\n  return /*#__PURE__*/_jsx(Component, {\n    ref: ref,\n    className: classNames(className, bsPrefix),\n    ...props\n  });\n});\nCardBody.displayName = 'CardBody';\nexport default CardBody;", "\"use client\";\n\nimport * as React from 'react';\nimport classNames from 'classnames';\nimport { useBootstrapPrefix } from './ThemeProvider';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst CardFooter = /*#__PURE__*/React.forwardRef(({\n  className,\n  bsPrefix,\n  as: Component = 'div',\n  ...props\n}, ref) => {\n  bsPrefix = useBootstrapPrefix(bsPrefix, 'card-footer');\n  return /*#__PURE__*/_jsx(Component, {\n    ref: ref,\n    className: classNames(className, bsPrefix),\n    ...props\n  });\n});\nCardFooter.displayName = 'CardFooter';\nexport default CardFooter;", "\"use client\";\n\nimport classNames from 'classnames';\nimport * as React from 'react';\nimport { useMemo } from 'react';\nimport { useBootstrapPrefix } from './ThemeProvider';\nimport CardHeaderContext from './CardHeaderContext';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst CardHeader = /*#__PURE__*/React.forwardRef(({\n  bsPrefix,\n  className,\n  // Need to define the default \"as\" during prop destructuring to be compatible with styled-components github.com/react-bootstrap/react-bootstrap/issues/3595\n  as: Component = 'div',\n  ...props\n}, ref) => {\n  const prefix = useBootstrapPrefix(bsPrefix, 'card-header');\n  const contextValue = useMemo(() => ({\n    cardHeaderBsPrefix: prefix\n  }), [prefix]);\n  return /*#__PURE__*/_jsx(CardHeaderContext.Provider, {\n    value: contextValue,\n    children: /*#__PURE__*/_jsx(Component, {\n      ref: ref,\n      ...props,\n      className: classNames(className, prefix)\n    })\n  });\n});\nCardHeader.displayName = 'CardHeader';\nexport default CardHeader;", "\"use client\";\n\nimport classNames from 'classnames';\nimport * as React from 'react';\nimport { useBootstrapPrefix } from './ThemeProvider';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst CardImg = /*#__PURE__*/React.forwardRef(\n// Need to define the default \"as\" during prop destructuring to be compatible with styled-components github.com/react-bootstrap/react-bootstrap/issues/3595\n({\n  bsPrefix,\n  className,\n  variant,\n  as: Component = 'img',\n  ...props\n}, ref) => {\n  const prefix = useBootstrapPrefix(bsPrefix, 'card-img');\n  return /*#__PURE__*/_jsx(Component, {\n    ref: ref,\n    className: classNames(variant ? `${prefix}-${variant}` : prefix, className),\n    ...props\n  });\n});\nCardImg.displayName = 'CardImg';\nexport default CardImg;", "\"use client\";\n\nimport * as React from 'react';\nimport classNames from 'classnames';\nimport { useBootstrapPrefix } from './ThemeProvider';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst CardImgOverlay = /*#__PURE__*/React.forwardRef(({\n  className,\n  bsPrefix,\n  as: Component = 'div',\n  ...props\n}, ref) => {\n  bsPrefix = useBootstrapPrefix(bsPrefix, 'card-img-overlay');\n  return /*#__PURE__*/_jsx(Component, {\n    ref: ref,\n    className: classNames(className, bsPrefix),\n    ...props\n  });\n});\nCardImgOverlay.displayName = 'CardImgOverlay';\nexport default CardImgOverlay;", "\"use client\";\n\nimport * as React from 'react';\nimport classNames from 'classnames';\nimport { useBootstrapPrefix } from './ThemeProvider';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst CardLink = /*#__PURE__*/React.forwardRef(({\n  className,\n  bsPrefix,\n  as: Component = 'a',\n  ...props\n}, ref) => {\n  bsPrefix = useBootstrapPrefix(bsPrefix, 'card-link');\n  return /*#__PURE__*/_jsx(Component, {\n    ref: ref,\n    className: classNames(className, bsPrefix),\n    ...props\n  });\n});\nCardLink.displayName = 'CardLink';\nexport default CardLink;", "\"use client\";\n\nimport * as React from 'react';\nimport classNames from 'classnames';\nimport { useBootstrapPrefix } from './ThemeProvider';\nimport divWithClassName from './divWithClassName';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst DivStyledAsH6 = divWithClassName('h6');\nconst CardSubtitle = /*#__PURE__*/React.forwardRef(({\n  className,\n  bsPrefix,\n  as: Component = DivStyledAsH6,\n  ...props\n}, ref) => {\n  bsPrefix = useBootstrapPrefix(bsPrefix, 'card-subtitle');\n  return /*#__PURE__*/_jsx(Component, {\n    ref: ref,\n    className: classNames(className, bsPrefix),\n    ...props\n  });\n});\nCardSubtitle.displayName = 'CardSubtitle';\nexport default CardSubtitle;", "\"use client\";\n\nimport * as React from 'react';\nimport classNames from 'classnames';\nimport { useBootstrapPrefix } from './ThemeProvider';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst CardText = /*#__PURE__*/React.forwardRef(({\n  className,\n  bsPrefix,\n  as: Component = 'p',\n  ...props\n}, ref) => {\n  bsPrefix = useBootstrapPrefix(bsPrefix, 'card-text');\n  return /*#__PURE__*/_jsx(Component, {\n    ref: ref,\n    className: classNames(className, bsPrefix),\n    ...props\n  });\n});\nCardText.displayName = 'CardText';\nexport default CardText;", "\"use client\";\n\nimport * as React from 'react';\nimport classNames from 'classnames';\nimport { useBootstrapPrefix } from './ThemeProvider';\nimport divWithClassName from './divWithClassName';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst DivStyledAsH5 = divWithClassName('h5');\nconst CardTitle = /*#__PURE__*/React.forwardRef(({\n  className,\n  bsPrefix,\n  as: Component = DivStyledAsH5,\n  ...props\n}, ref) => {\n  bsPrefix = useBootstrapPrefix(bsPrefix, 'card-title');\n  return /*#__PURE__*/_jsx(Component, {\n    ref: ref,\n    className: classNames(className, bsPrefix),\n    ...props\n  });\n});\nCardTitle.displayName = 'CardTitle';\nexport default CardTitle;", "\"use client\";\n\nimport classNames from 'classnames';\nimport * as React from 'react';\nimport { useBootstrapPrefix } from './ThemeProvider';\nimport CardBody from './CardBody';\nimport CardFooter from './CardFooter';\nimport CardHeader from './CardHeader';\nimport CardImg from './CardImg';\nimport CardImgOverlay from './CardImgOverlay';\nimport CardLink from './CardLink';\nimport CardSubtitle from './CardSubtitle';\nimport CardText from './CardText';\nimport CardTitle from './CardTitle';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst Card = /*#__PURE__*/React.forwardRef(({\n  bsPrefix,\n  className,\n  bg,\n  text,\n  border,\n  body = false,\n  children,\n  // Need to define the default \"as\" during prop destructuring to be compatible with styled-components github.com/react-bootstrap/react-bootstrap/issues/3595\n  as: Component = 'div',\n  ...props\n}, ref) => {\n  const prefix = useBootstrapPrefix(bsPrefix, 'card');\n  return /*#__PURE__*/_jsx(Component, {\n    ref: ref,\n    ...props,\n    className: classNames(className, prefix, bg && `bg-${bg}`, text && `text-${text}`, border && `border-${border}`),\n    children: body ? /*#__PURE__*/_jsx(CardBody, {\n      children: children\n    }) : children\n  });\n});\nCard.displayName = 'Card';\nexport default Object.assign(Card, {\n  Img: CardImg,\n  Title: CardTitle,\n  Subtitle: CardSubtitle,\n  Body: CardBody,\n  Link: CardLink,\n  Text: CardText,\n  Header: CardHeader,\n  Footer: CardFooter,\n  ImgOverlay: CardImgOverlay\n});", "import React, { forwardRef } from 'react';\r\nimport { Formik, Form, FormikProps, FormikHelpers } from 'formik';\r\nimport * as Yup from 'yup';\r\n\r\n// This is a wrapper component that replaces react-bootstrap4-form-validation with Formik\r\ninterface MockEvent {\r\n  preventDefault: () => void;\r\n  stopPropagation: () => void;\r\n  currentTarget: (EventTarget & Element) | null;\r\n  target: EventTarget | null;\r\n  nativeEvent: Event;\r\n  bubbles: boolean;\r\n  cancelable: boolean;\r\n  defaultPrevented: boolean;\r\n  eventPhase: number;\r\n  isTrusted: boolean;\r\n  timeStamp: number;\r\n  type: string;\r\n  isDefaultPrevented: () => boolean;\r\n  isPropagationStopped: () => boolean;\r\n  persist: () => void;\r\n}\r\n\r\ninterface ValidationFormWrapperProps {\r\n  children: React.ReactNode | ((formikProps: FormikProps<any>) => React.ReactNode);\r\n  onSubmit: (event: React.FormEvent | MockEvent, values?: Record<string, any>, actions?: FormikHelpers<Record<string, any>>) => void;\r\n  onErrorSubmit?: (errors: any) => void;\r\n  initialValues?: Record<string, any>;\r\n  enableReinitialize?: boolean;\r\n  autoComplete?: string;\r\n  className?: string;\r\n  onKeyPress?: (e: any) => void;\r\n}\r\n\r\nconst ValidationFormWrapper = forwardRef<HTMLFormElement, ValidationFormWrapperProps>((props, ref) => {\r\n  const { children, onSubmit, autoComplete, className, onKeyPress, initialValues, ...rest } = props;\r\n\r\n  // Create an empty validation schema by default\r\n  const validationSchema = Yup.object().shape({});\r\n\r\n  return (\r\n    <Formik\r\n      initialValues={initialValues || {}}\r\n      validationSchema={validationSchema}\r\n      onSubmit={(values: Record<string, any>, actions: FormikHelpers<Record<string, any>>) => {\r\n        // Create a mock event object with preventDefault method and currentTarget\r\n        const mockEvent: MockEvent = {\r\n          preventDefault: () => {},\r\n          stopPropagation: () => {},\r\n          currentTarget: null, // Set to null to avoid checkValidity errors\r\n          target: null,\r\n          nativeEvent: new Event('submit'),\r\n          bubbles: false,\r\n          cancelable: true,\r\n          defaultPrevented: false,\r\n          eventPhase: 0,\r\n          isTrusted: false,\r\n          timeStamp: Date.now(),\r\n          type: 'submit',\r\n          isDefaultPrevented: () => false,\r\n          isPropagationStopped: () => false,\r\n          persist: () => {}\r\n        };\r\n\r\n        if (onSubmit) {\r\n          // Pass the mock event object to maintain compatibility with the original code\r\n          onSubmit(mockEvent, values, actions);\r\n        }\r\n      }}\r\n      {...rest}\r\n    >\r\n      {(formikProps: FormikProps<any>) => (\r\n        <Form\r\n          ref={ref}\r\n          onSubmit={formikProps.handleSubmit}\r\n          autoComplete={autoComplete}\r\n          className={className}\r\n          onKeyPress={onKeyPress}\r\n        >\r\n          {typeof children === 'function' ? children(formikProps) : children}\r\n        </Form>\r\n      )}\r\n    </Formik>\r\n  );\r\n});\r\n\r\nValidationFormWrapper.displayName = 'ValidationFormWrapper';\r\n\r\nexport default ValidationFormWrapper;\r\n", "//Import Library\r\nimport React from \"react\";\r\nimport { <PERSON><PERSON>, Card, Form, Container, Row, Col,} from \"react-bootstrap\";\r\nimport { ValidationForm } from \"../../components/common/FormValidation\";\r\n\r\n//Import services/components\r\nimport { useTranslation } from 'next-i18next';\r\nimport ValidationFormWrapper from \"../../components/common/ValidationFormWrapper\";\r\n\r\n\r\n//TOTO refactor\r\ninterface LinkFormProps {\r\n  link: Array<{ title: string; link: string }>;\r\n  handleChangeforTimeline: (e: React.ChangeEvent<any>, i: number) => void;\r\n  removeForm: (e: React.MouseEvent, i: number) => void;\r\n  addform: () => void;\r\n}\r\n\r\nconst LinkForm = (props: LinkFormProps): React.ReactElement => {\r\n  const { t } = useTranslation('common');\r\n  const { link, handleChangeforTimeline, removeForm, addform } = props;\r\n  return (\r\n    <Container className=\"formCard\" fluid>\r\n      <Card>\r\n        <ValidationFormWrapper onSubmit={() => {}}>\r\n          <Card.Body>\r\n            {link && link.map((item: any, i: number) => {\r\n              return (\r\n                <div>\r\n                  <Row>\r\n                    <Col>\r\n                      <Form.Group>\r\n                        <Form.Label className=\"required-field\">{t(\"update.Title\")}</Form.Label>\r\n                        <Form.Control\r\n                        name=\"title\"\r\n                        id=\"timetitle\"\r\n                        type=\"text\"\r\n                        value={item.title}\r\n                        required\r\n                        onChange={(e) => handleChangeforTimeline(e, i)} />\r\n                        <Form.Control.Feedback type=\"invalid\">\r\n                        {t(\"update.TitleisRequired\")}\r\n                  </Form.Control.Feedback>\r\n                      </Form.Group>\r\n                    </Col>\r\n                    <Col>\r\n                      <Form.Group>\r\n                        <Form.Label className=\"required-field\">{t(\"update.Link\")}</Form.Label>\r\n                        <Form.Control\r\n                          name=\"link\"\r\n                          id=\"link\"\r\n                          type=\"text\"\r\n                          required\r\n                          value={item.link}\r\n                          onChange={(e) => handleChangeforTimeline(e, i)}\r\n                          pattern=\"http(s)?://??[\\w.-]+[-a-zA-Z0-9@:%._\\+~#=]{2,256}\\.[a-z]{2,6}.+\"\r\n                        />\r\n                        <Form.Control.Feedback type=\"invalid\">\r\n                        {t(\"update.Providevalidlink\")}\r\n                  </Form.Control.Feedback>\r\n                      </Form.Group>\r\n                    </Col>\r\n                    <Col>\r\n                      <Form.Group>\r\n                        {i === 0 ? (\r\n                          <div></div>\r\n                        ) : (\r\n                            <Button variant=\"secondary\" style={{ marginTop: \"30px\" }} onClick={(e) => removeForm(e, i)} >{t(\"update.Remove\")}\r\n                            </Button>\r\n                          )}\r\n                      </Form.Group>\r\n                    </Col>\r\n                  </Row>\r\n                </div>\r\n              );\r\n            })}\r\n            <Row>\r\n              <Col md lg=\"4\">\r\n                <Button variant=\"secondary\" style={{ marginTop: \"27px\", marginBottom: \"20px\" }} onClick={addform}>{t(\"update.ADD\")}</Button>\r\n              </Col>\r\n            </Row>\r\n          </Card.Body>\r\n        </ValidationFormWrapper>\r\n      </Card>\r\n    </Container>\r\n  );\r\n}\r\n\r\nexport default LinkForm;\r\n", "\"use client\";\n\nimport * as React from 'react';\nconst context = /*#__PURE__*/React.createContext(null);\ncontext.displayName = 'CardHeaderContext';\nexport default context;"], "names": ["CardBody", "React", "ref", "className", "bsPrefix", "as", "Component", "props", "useBootstrapPrefix", "_jsx", "classNames", "displayName", "<PERSON><PERSON><PERSON>er", "<PERSON><PERSON><PERSON><PERSON>", "prefix", "contextValue", "useMemo", "cardHeaderBsPrefix", "CardHeaderContext", "Provider", "value", "children", "CardImg", "variant", "CardImgOverlay", "CardLink", "DivStyledAsH6", "divWithClassName", "CardSubtitle", "CardText", "DivStyledAsH5", "CardTitle", "Card", "bg", "text", "border", "body", "Object", "assign", "Img", "Title", "Subtitle", "Body", "Link", "Text", "Header", "Footer", "ImgOverlay", "ValidationFormWrapper", "forwardRef", "onSubmit", "autoComplete", "onKeyPress", "initialValues", "rest", "validationSchema", "<PERSON><PERSON>", "shape", "<PERSON><PERSON>", "values", "actions", "mockEvent", "preventDefault", "stopPropagation", "currentTarget", "target", "nativeEvent", "Event", "bubbles", "cancelable", "defaultPrevented", "eventPhase", "isTrusted", "timeStamp", "Date", "now", "type", "isDefaultPrevented", "isPropagationStopped", "persist", "formikProps", "Form", "handleSubmit", "t", "useTranslation", "LinkForm", "link", "handleChangeforTimeline", "removeForm", "addform", "Container", "fluid", "map", "item", "i", "div", "Row", "Col", "Group", "Label", "Control", "name", "id", "title", "required", "onChange", "e", "<PERSON><PERSON><PERSON>", "pattern", "<PERSON><PERSON>", "style", "marginTop", "onClick", "md", "lg", "marginBottom", "context"], "sourceRoot": "", "ignoreList": [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 13]}