{"version": 3, "file": "static/chunks/pages/adminsettings/hazard/forms-f56063e3d57930e8.js", "mappings": "uIAwHA,MA9GkE,OAAC,OACjEA,CAAK,UACLC,CAAQ,CACRC,EA2GaC,YA3GC,QA2GmBA,EAAC,UA3GA,QAClCC,EAAS,GAAG,UACZC,GAAW,CAAK,CACjB,GACOC,EAAYC,CAAAA,EAAAA,EAAAA,MAAAA,CAAMA,CAAiB,MACnC,CAACC,EAAWC,EAAa,CAAGC,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAC,IAG3CC,CAAAA,EAAAA,EAAAA,SAAAA,CAASA,CAAC,KACJL,EAAUM,OAAO,EAAI,GAEnB,CAACJ,GAAaF,EAAUM,IAFa,GAEN,CAACC,SAAS,GAAKb,IAChDM,EAAUM,CAD6C,MACtC,CAACC,SAAS,CAAGb,GAAS,GAG7C,EAAG,CAACA,EAAOQ,EAAU,EAGrB,IAAMM,EAAc,KACdR,EAAUM,OAAO,EAAIX,GACvBA,EAASK,EAAUM,GADc,IACP,CAACC,SAAS,CAExC,EAGME,EAAc,CAACC,EAAiBhB,KACpC,GAAwB,aAApB,OAAOiB,SAA0B,KAGnCX,EAFAW,SAASF,WAAW,CAACC,GAAS,EAAOhB,GAAS,IAC9Cc,WACAR,EAAAA,EAAUM,OAAAA,GAAVN,EAAmBY,KAAK,EAC1B,CACF,CAFIZ,CAIJ,MACE,UAACa,MAAAA,CAAIC,UAAU,0BAA0BC,MAAO,CAAEC,OAAQ,gBAAiB,WAEzE,CADC,EACD,GAD8B,GAC9B,wBACE,WAACH,MAAAA,CAAIC,UAAU,UAAUC,MAAO,CAAEE,QAAS,MAAOC,aAAc,iBAAkBC,WAAY,SAAU,YACpG,UAACC,SAAAA,CACCC,KAAK,SACLC,QAAS,IAAMb,EAAY,QAC3BM,MAAO,CAAEQ,OAAQ,QAASN,QAAS,SAAU,WAE7C,UAACO,SAAAA,UAAO,QAEV,UAACJ,SAAAA,CACCC,KAAK,SACLC,QAAS,IAAMb,EAAY,UAC3BM,MAAO,CAAEQ,OAAQ,QAASN,QAAS,SAAU,WAE7C,UAACQ,KAAAA,UAAG,QAEN,UAACL,SAAAA,CACCC,KAAK,SACLC,QAAS,IAAMb,EAAY,aAC3BM,MAAO,CAAEQ,OAAQ,QAASN,QAAS,SAAU,WAE7C,UAACS,IAAAA,UAAE,QAEL,UAACN,SAAAA,CACCC,KAAK,SACLC,QAAS,IAAMb,EAAY,qBAC3BM,MAAO,CAAEQ,OAAQ,QAASN,QAAS,SAAU,WAC9C,OAGD,UAACG,SAAAA,CACCC,KAAK,SACLC,QAAS,IAAMb,EAAY,uBAC3BM,MAAO,CAAEQ,OAAQ,QAASN,QAAS,SAAU,WAC9C,OAGD,UAACG,SAAAA,CACCC,KAAK,SACLC,QAAS,KACP,IAAMK,EAAMC,OAAO,sBACfD,GAAKlB,EAAY,aAAckB,EACrC,EACAZ,MAAO,CAAEQ,OAAQ,QAASN,QAAS,SAAU,WAC9C,YAIH,UAACJ,MAAAA,CACCgB,IAAK7B,EACL8B,gBAAiB,CAAC/B,EAClBgC,QAASvB,EACTwB,QAAS,IAAM7B,GAAa,GAC5B8B,OAAQ,IAAM9B,GAAa,GAC3BY,MAAO,CACLE,QAAS,OACTiB,UAAWpC,EACXqC,UAAWrC,IACXsC,SAAU,OACVC,QAAS,MACX,EACAC,mBAAkB,EAAuB,GAAd1C,EAC3B2C,gCAAgC,QAO5C,EC7GaC,EAAmD,IAC9D,GAAM,aAAEC,CAAW,UAAE9C,CAAQ,CAAE,CAAG+C,EAElC,MACE,UAAC7C,EAAoBA,CACnBH,MAAO+C,GAAe,GACtB9C,SAAU,GAAaA,EAASgD,IAGtC,EAAE,wGCZF,IAAMC,EAAwBC,EAAAA,OAAb,GAA6B,CAAC,GAK5ChB,IALyB,IAAoB,WAC9Cf,CAAS,UACTgC,CAAQ,CACRC,GAAIC,EAAY,KAAK,CACrB,GAAGN,EACJ,GAEC,OADAI,EAAWG,CAAAA,EAAAA,EAAAA,EAAAA,CAAkBA,CAACH,EAAU,aACpBI,CAAAA,EAAAA,EAAAA,GAAAA,CAAIA,CAACF,EAAW,CAClCnB,IAAKA,EACLf,UAAWqC,IAAWrC,EAAWgC,GACjC,GAAGJ,CACL,EACF,GACAE,EAASQ,WAAW,CAAG,WCbvB,IAAMC,EAA0BR,EAAAA,SAAb,CAA6B,CAAC,GAK9ChB,MAL2B,EAAoB,WAChDf,CAAS,UACTgC,CAAQ,CACRC,GAAIC,EAAY,KAAK,CACrB,GAAGN,EACJ,GAEC,OADAI,EAAWG,CAAAA,EAAAA,EAAAA,EAAAA,CAAkBA,CACX,EADsB,eACpBC,CAAAA,EAAAA,EAAAA,GAAAA,CAAIA,CAACF,EAAW,CAClCnB,IAAKA,EACLf,UAAWqC,IAAWrC,EAAWgC,GACjC,GAAGJ,CAAK,EAEZ,GACAW,EAJyBF,WAIH,CAAG,4BCXzB,IAAMG,EAA0BT,EAAAA,SAAb,CAA6B,CAAC,GAM9ChB,MAN2B,EAAoB,CAChDiB,UAAQ,WACRhC,CAAS,CAETiC,CADA,EACIC,EAAY,KAAK,CACrB,GAAGN,EACJ,GACOa,EAASN,CAAAA,EAAAA,EAAAA,EAAAA,CAAkBA,CAACH,EAAU,eACtCU,EAAeC,CAAAA,EAAAA,EAAAA,OAAAA,CAAOA,CAAC,IAAO,EAClCC,mBAAoBH,EACtB,EAAI,CAACA,EAAO,EACZ,MAAoBL,CAAb,EAAaA,EAAAA,GAAAA,CAAIA,CAACS,EAAAA,CAAiBA,CAACC,QAAQ,CAAE,CACnDlE,MAAO8D,EACPK,SAAuBX,CAAb,EAAaA,EAAAA,GAAAA,CAAIA,CAACF,EAAW,CACrCnB,IAAKA,EACL,GAAGa,CAAK,CACR5B,UAAWqC,IAAWrC,EAAWyC,EACnC,EACF,EACF,GACAD,EAAWF,GAJgBD,QAIL,CAAG,aCtBzB,IAAMW,EAAuBjB,EAAAA,MAAb,IAA6B,CAC7C,CACA,EAMGhB,GARwB,KAE1B,UACCiB,CAAQ,WACRhC,CAAS,SACTiD,CAAO,CACPhB,GAAIC,EAAY,KAAK,CACrB,GAAGN,EACJ,GACOa,EAASN,CAAAA,EAAAA,EAAAA,EAAAA,CAAkBA,CAACH,EAAU,YAC5C,MAAoBI,CAAb,EAAaA,EAAAA,GAAAA,CAAIA,CAACF,EAAP,CAChBnB,IAAKA,EACLf,UAAWqC,IAAWY,EAAU,GAAaA,MAAAA,CAAVR,EAAO,EAArBJ,GAAgC,OAARY,CAX0G,EAW9FR,EAAQzC,GACjE,GAAG4B,CACL,EACF,EACAoB,GAAQV,WAAW,CAAG,UChBtB,IAAMY,EAA8BnB,EAAAA,UAAgB,CAAC,EAA9B,CAKpBhB,QALmD,EAApB,SAChCf,CAAS,CACTgC,UAAQ,CACRC,GAAIC,EAAY,KAAK,CACrB,GAAGN,EACJ,GAEC,OAAO,EADIO,CAAAA,EAAAA,EAAAA,EAAAA,CAAkBA,CAACH,EAAU,oBACpBI,CAAAA,EAAAA,EAAAA,GAAAA,CAAIA,CAACF,EAAW,CAClCnB,IAAKA,EACLf,UAAWqC,IAAWrC,EAAWgC,GACjC,GAAGJ,CAAK,EAEZ,GACAsB,EAJyBb,WAIC,CAAG,iBCb7B,IAAMc,EAAwBpB,EAAAA,OAAb,GAA6B,CAAC,GAK5ChB,IALyB,IAAoB,WAC9Cf,CAAS,CACTgC,UAAQ,CACRC,GAAIC,EAAY,GAAG,CACnB,GAAGN,EACJ,GAEC,OADAI,EAAWG,CAAAA,EAAAA,EAAAA,EAAAA,CAAkBA,CAACH,EAAU,aACpBI,CAAAA,EAAAA,EAAAA,GAAAA,CAAIA,CAACF,EAAW,CAClCnB,IAAKA,EACLf,UAAWqC,IAAWrC,EAAWgC,GACjC,GAAGJ,CACL,EACF,GACAuB,EAASb,WAAW,CAAG,0BCZvB,IAAMc,EAAgBC,CAAAA,EAAAA,EAAAA,CAAAA,CAAgBA,CAAC,MACjCC,EAA4BvB,EAAAA,UAAgB,CAA7B,CAA8B,EAKhDhB,QALiD,WAClDf,CAAS,UACTgC,CAAQ,CACRC,GAAIC,EAAYkB,CAAa,CAC7B,GAAGxB,EACJ,GAEC,OADAI,EAAWG,CAAAA,EAAAA,EAAAA,EAAAA,CAAkBA,CAACH,EAAU,iBACpBI,CAAAA,EAAAA,EAAAA,GAAAA,CAAIA,CAACF,EAAW,CAClCnB,IAAKA,EACLf,UAAWqC,IAAWrC,EAAWgC,GACjC,GAAGJ,CACL,EACF,GACA0B,EAAahB,WAAW,CAAG,eCf3B,IAAMiB,EAAwBxB,EAAAA,OAAb,GAA6B,CAAC,GAK5ChB,IALyB,IAAoB,WAC9Cf,CAAS,UACTgC,CAAQ,CACRC,GAAIC,EAAY,GAAG,CACnB,GAAGN,EACJ,GAEC,OADAI,EAAWG,CAAAA,EAAAA,EAAAA,EAAAA,CAAkBA,CACX,EADsB,aACpBC,CAAAA,EAAAA,EAAAA,GAAAA,CAAIA,CAACF,EAAW,CAClCnB,IAAKA,EACLf,UAAWqC,IAAWrC,EAAWgC,GACjC,GAAGJ,CAAK,EAEZ,EACA2B,GAJyBlB,WAIL,CAAG,WCZvB,IAAMmB,EAAgBH,CAAAA,EAAAA,EAAAA,CAAAA,CAAgBA,CAAC,MACjCI,EAAyB1B,EAAAA,QAAb,EAA6B,CAAC,GAK7ChB,KAL0B,GAAoB,WAC/Cf,CAAS,UACTgC,CAAQ,CACRC,GAAIC,EAAYsB,CAAa,CAC7B,GAAG5B,EACJ,GAEC,OADAI,EAAWG,CAAAA,EAAAA,EAAAA,EAAAA,CAAkBA,CAACH,EAAU,cACpBI,CAAAA,EAAAA,EAAAA,GAAAA,CAAIA,CAACF,EAAW,CAClCnB,IAAKA,EACLf,UAAWqC,IAAWrC,EAAWgC,GACjC,GAAGJ,CAAK,EAEZ,GACA6B,EAJyBpB,WAIJ,CAAG,YCNxB,IAAMqB,EAAoB3B,EAAAA,GAAb,OAA6B,CAAC,GAAnB,QAAoB,UAC1CC,CAAQ,WACRhC,CAAS,IACT2D,CAAE,MACFC,CAAI,QACJ1D,CAAM,MACN2D,GAAO,CAAK,UACZd,CAAQ,CAERd,CADA,EACIC,EAAY,KAAK,CACrB,GAAGN,EACJ,GACOa,EAASN,CAAAA,EAAAA,EAAAA,EAAAA,CAAkBA,CAACH,EAAU,QAC5C,MAAoBI,CAAb,EAAaA,EAAAA,GAAAA,CAAIA,CAACF,EAAP,CAChBnB,IAAKA,EACL,GAAGa,CAAK,CACR5B,UAAWqC,IAAWrC,EAAWyC,EAAQkB,GAAM,MAAS,GAAnCtB,GAAmC,CAAHsB,GAAMC,GAAQ,QAAa,OAALA,GAAQ1D,GAAU,UAAiB,OAAPA,IACvG6C,IATyJ,KAS/Ic,EAAoBzB,CAAAA,EAAAA,EAAb,GAAaA,CAAIA,CAACN,EAAU,CAC3CiB,GAD0B,MAAejB,CAE3C,GAAKiB,CACP,EACF,GACAW,EAAKpB,WAAW,CAAG,OACnB,MAAewB,OAAOC,MAAM,CAACL,EAAM,CACjCM,INhBahB,CMgBRA,CACLiB,KNjBoBjB,CKDPS,CCkBNA,CACPS,EAFYlB,KDjBUS,EFATH,CGmBHA,CACVa,CAFgBV,ITpBH3B,CSsBPA,CACNsC,GHrByBd,EDFZH,CLAQrB,CSwBrBuC,CTxBsB,GSsBRvC,CFtBDyB,CFAQJ,CIyBrBmB,CJzBsB,GIuBRnB,EFvBOI,CLSRf,CKTS,CE0BtB+B,EAFchB,KRxBDhB,CCSUC,COkBvBgC,CPlBwB,GOgBNhC,IRzBKD,EAAC,CGAXW,CK2BDA,CADMX,CAElB,EAAC,SL5B0BW,EAAC,GK2BFA,0ICwCrB,IAAMuB,EAAQ,CACnBC,WA1C4C,OAAC,MAC7CC,CAAI,eACJC,CAAa,UACb/F,CAAQ,cACRgG,CAAY,UACZ9B,CAAQ,CACT,GACO,QAAE+B,CAAM,SAAEC,CAAO,CAAE,CAAGC,CAAAA,EAAAA,EAAAA,EAAAA,CAAgBA,GACtCC,EAAWF,CAAO,CAACJ,EAAK,EAAIG,CAAM,CAACH,EAAK,CAGzB5C,EAAAA,OAAa,CAAC,IAAO,OAAE4C,EAAK,EAAI,CAACA,EAAK,EAG3D,IAAMO,EAAoBnD,EAAAA,QAAc,CAACoD,GAAG,CAACpC,EAAU,GACrD,EAAIhB,cAAoB,CAACqD,IAxC7B,IAwCqC,KAxC5BC,CAAmB,EAC1B,MAAwB,UAAjB,OAAOzD,GAAgC,OAAVA,CACtC,EAwCmBwD,EAAMxD,KAAK,EACfG,CADkB,CAClBA,YAAkB,CAACqD,EAA6C,MACrET,EACA,GAAGS,EAAMxD,KAAK,GAIbwD,GAGT,MACE,WAACrF,MAAAA,WACC,UAACA,MAAAA,CAAIC,UAAU,uBACZkF,IAEFD,GACC,UAAClF,MAAAA,CAAIC,UAAU,oCACZ6E,GAAiB,kBAAOC,CAAM,CAACH,EAAK,CAAgBG,CAAM,CAACH,EAAK,CAAGW,OAAOR,CAAM,CAACH,GAAK,MAKjG,EAIEY,UAhE0C,OAAC,IAAEC,CAAE,OAAEC,CAAK,OAAE7G,CAAK,MAAE+F,CAAI,UAAE1F,CAAQ,CAAE,GACzE,QAAEyG,CAAM,eAAEC,CAAa,CAAE,CAAGX,CAAAA,EAAAA,EAAAA,EAAAA,CAAgBA,GAC5CY,EAAYjB,GAAQa,EAE1B,MACE,UAACK,EAAAA,CAAIA,CAACC,KAAK,EACTvF,KAAK,QACLiF,GAAIA,EACJC,MAAOA,EACP7G,MAAOA,EACP+F,KAAMiB,EACNG,QAASL,CAAM,CAACE,EAAU,GAAKhH,EAC/BC,SAAU,IACR8G,EAAcC,EAAWI,EAAEC,MAAM,CAACrH,KAAK,CACzC,EACAK,SAAUA,EACViH,MAAM,KAGZ,CA8CA,EAAE,ECzEcC,CAAAA,CACLC,EAAAA,EAAAA,CACEC,EAAAA,EAAAA,gGCeb,IAAMC,EAAwBC,CAAAA,EAAAA,EAAAA,UAAAA,CAAUA,CAA8C,CAAC3E,EAAOb,KAC5F,GAAM,UAAEgC,CAAQ,UAAEyD,CAAQ,cAAEC,CAAY,WAAEzG,CAAS,YAAE0G,CAAU,CAAEC,eAAa,CAAE,GAAGC,EAAM,CAAGhF,EAGtFiF,EAAmBC,EAAAA,EAAU,GAAGC,KAAK,CAAC,CAAC,GAE7C,MACE,UAACC,EAAAA,EAAMA,CAAAA,CACLL,cAAeA,GAAiB,CAAC,EACjCE,iBAAkBA,EAClBL,SAAU,CAACd,EAA6BuB,KAEtC,IAAMC,EAAuB,CAC3BC,eAAgB,KAAO,EACvBC,gBAAiB,KAAO,EACxBC,cAAe,KACfpB,OAAQ,KACRqB,YAAa,IAAIC,MAAM,UACvBC,SAAS,EACTC,YAAY,EACZC,kBAAkB,EAClBC,WAAY,EACZC,WAAW,EACXC,UAAWC,KAAKC,GAAG,GACnBxH,KAAM,SACNyH,mBAAoB,IAAM,GAC1BC,qBAAsB,KAAM,EAC5BC,QAAS,KAAO,CAClB,EAEI1B,GAEFA,EAASU,EAAWxB,EAAQuB,CAFhB,CAIhB,EACC,GAAGL,CAAI,UAEP,GACC,UAACf,EAAAA,EAAIA,CAAAA,CACH9E,IAAKA,EACLyF,SAAU2B,EAAYC,YAAY,CAClC3B,aAAcA,EACdzG,UAAWA,EACX0G,WAAYA,WAES,YAApB,OAAO3D,EAA0BA,EAASoF,GAAepF,KAKpE,GAEAuD,EAAsBhE,WAAW,CAAG,wBAEpC,MAAegE,qBAAqBA,EAAC,sFClF9B,IAAMF,EAAY,OAAC,MACxBzB,CAAI,IACJa,CAAE,UACF6C,CAAQ,WACRC,CAAS,cACTzD,CAAY,UACZhG,CAAQ,CACRD,OAAK,IACLqD,CAAE,WACFsG,CAAS,MACTC,CAAI,SACJC,CAAO,CACP,GAAG7G,EACC,GAuBJ,MACE,UAAC8G,EAAAA,EAAKA,CAAAA,CAAC/D,KAAMA,EAAMgE,SAtBHC,CAsBaD,GApB7B,IAAME,EAA2B,UAAf,OAAOD,EAAmBA,EAAMtD,OAAOsD,GAAO,WAChE,GAAiB,EAACA,GAA4B,IAA7B,CAAQC,EAAUC,IAAI,EAAO,CAAC,CACtCjE,EAD0C,KAC1CA,EAAAA,KAAAA,EAAAA,EAAcyD,SAAAA,GAAa,EAA3BzD,uBAGLyD,GAAa,CAACA,EAAUM,GACnB/D,GADyB,IACzBA,EAAAA,KAAAA,EAAAA,EAAcyD,SAAAA,GAAa,EAA3BzD,cAGL4D,GAAWG,GAET,CAACG,CAFa,GACAC,OAAOP,GACdQ,IAAI,CAACL,GACP/D,GADa,IACbA,EAAAA,KAAAA,EAAAA,EAAc4D,OAAAA,GAAW,IAAzB5D,mBAKb,WAIK,OAAC,OAAEqE,CAAK,CAAEC,MAAI,CAAO,SACpB,iCACE,UAACtD,EAAAA,CAAIA,CAACuD,OAAO,EACV,GAAGF,CAAK,CACR,GAAGtH,CAAK,CACT4D,GAAIA,EACJvD,GAAIA,GAAM,QACVuG,KAAMA,EACNa,UAAWF,EAAKpE,OAAO,EAAI,CAAC,CAACoE,EAAKG,KAAK,CACvCzK,SAAU,IACRqK,EAAMrK,QAAQ,CAACmH,GACXnH,GAAUA,EAASmH,EACzB,EACApH,WAAiB2K,IAAV3K,EAAsBA,EAAQsK,EAAMtK,KAAK,GAEjDuK,EAAKpE,OAAO,EAAIoE,EAAKG,KAAK,CACzB,UAACzD,EAAAA,CAAIA,CAACuD,OAAO,CAACI,QAAQ,EAACjJ,KAAK,mBACzB4I,EAAKG,KAAK,GAEX,UAKd,EAAE,EAIyB,OAAC,MAC1B3E,CAAI,IACJa,CAAE,UACF6C,CAAQ,CACRxD,cAAY,UACZhG,CAAQ,OACRD,CAAK,UACLmE,CAAQ,CACR,GAAGnB,EACC,GAUJ,MACE,UAAC8G,EAAAA,EAAKA,CAAAA,CAAC/D,KAAMA,EAAMgE,SATJ,CAScA,GAR7B,GAAIN,GAAa,EAACO,GAAe,IAAhB,CAAQA,CAAQ,CAAC,CAChC,EADoC,IAC7B/D,CAAAA,QAAAA,KAAAA,EAAAA,EAAcyD,QAAdzD,CAAuB,GAAI,wBAItC,WAIK,OAAC,OAAEqE,CAAK,MAAEC,CAAI,CAAO,SACpB,iCACE,UAACtD,EAAAA,CAAIA,CAACuD,OAAO,EACXnH,GAAG,SACF,GAAGiH,CAAK,CACR,GAAGtH,CAAK,CACT4D,GAAIA,EACJ6D,UAAWF,EAAKpE,OAAO,EAAI,CAAC,CAACoE,EAAKG,KAAK,CACvCzK,SAAU,IACRqK,EAAMrK,QAAQ,CAACmH,GACXnH,GAAUA,EAASmH,EACzB,EACApH,MAAOA,WAAsBA,EAAQsK,EAAMtK,KAAK,UAE/CmE,IAEFoG,EAAKpE,OAAO,EAAIoE,EAAKG,KAAK,CACzB,UAACzD,EAAAA,CAAIA,CAACuD,OAAO,CAACI,QAAQ,EAACjJ,KAAK,mBACzB4I,EAAKG,KAAK,GAEX,UAKd,EAAE,+CCnHF,IAAMG,EAAuB1H,QAAb,CAAaA,UAAF,GAAqB,CAAC,MACjD0H,EAAQnH,WAAW,CAAG,oBACtB,MAAemH,OAAOA,EAAC,UCJvB,4CACA,8BACA,WACA,OAAe,EAAQ,KAAmD,CAC1E,EACA,SAFsB,4GCHtB,IAAMC,EAAiBC,IAAAA,KAAe,CAAC,CAAC,QAAS,MAAM,EAC1CC,EAAgBD,IAAAA,SAAmB,CAAC,CAACD,EAAgBC,IAAAA,KAAe,CAAC,CAChFE,GAAIH,CACN,GAAIC,IAAAA,KAAe,CAAC,CAClBG,GAAIJ,CACN,GAAIC,IAAAA,KAAe,CAAC,CAClBI,GAAIL,CACN,GAAIC,IAAAA,KAAe,CAAC,CAClBK,GAAIN,CACN,GAAIC,IAAAA,KAAe,CAAC,CAClBM,IAAKP,CACP,GAAIC,IAAAA,MAAgB,CAAC,EAAE,eCJvB,IAAMO,EAAY,CAKhB1E,GAAImE,IAAAA,MAAgB,CAEpBQ,KAAMR,IAAAA,MAAgB,CAEtBnJ,QAASmJ,IAAAA,IAAc,CAEvBS,MAAOT,IAAAA,IAAc,CAACU,UAAU,CAEhCpL,SAAU0K,IAAAA,IAAc,CAQxBW,MAAOV,EAEPW,SAAUZ,EAFUC,EAEVD,MAAgB,CAE1Ba,kBAAmBb,IAAAA,IAAc,CAMjCc,eAAgBd,IAAAA,MAAgB,CAMhCe,YAAaf,IAAAA,KAAe,CAAC,CAAC,OAAO,EAMrCgB,KAAMhB,IAAAA,IAAc,CAEpB3H,SAAU2H,IAAAA,MAAgB,CAE1B1G,QAAS0G,IAAAA,MAAgB,CAEzBiB,KAAMjB,IAAAA,MAAgB,EAYlBkB,EAA8B9I,EAAAA,UAAgB,CAAC,EAA9B,CAepBhB,QAfmD,CACpDqJ,CADgC,MAC3B,UACLrH,CAAQ,UACRf,CAAQ,gBACRyI,CAAc,SACdxH,CAAO,MACP2H,CAAI,UACJL,CAAQ,mBACRC,CAAiB,UACjBvL,CAAQ,MACRkL,CAAI,IACJ3E,CAAE,aACFkF,CAAW,MACXC,CAAI,CACJ,GAAG/I,EACJ,SAAuBkJ,CAAAA,EAAAA,EAAAA,IAAAA,CAAKA,CAACC,CAAR,CAAQA,CAAQA,CAAE,CACtChK,IAAKA,EACL,GAAGa,CAAK,CACRmB,SAAU,CAAcX,CAAAA,EAAAA,EAAAA,GAAAA,CAAIA,CAAC4I,EAAAA,CAAcA,CAAE,CAC3CxF,GAAIA,EACJ2E,KAAMA,EACNS,KAAMA,EACN3H,QAASA,EACThE,SAAUA,EACVgM,cAAejJ,EACfe,SAAUqH,CACZ,GAAiBhI,CAAAA,EAAAA,EAAAA,GAAAA,CAAIA,CAAC8I,EAAAA,CAAYA,CAAE,CAClCC,KAAMZ,EACNa,cAAeZ,EACfC,eAAgBA,EAChBxH,QAASyH,EACTC,KAAMA,EACN5H,SAAUA,CACZ,GAAG,KAEL8H,EAAevI,WAAW,CAAG,iBAC7BuI,EAAeX,SAAS,CAAGA,EAC3B,MAAeW,cAAcA,EAAC", "sources": ["webpack://_N_E/./components/common/SimpleRichTextEditor.tsx", "webpack://_N_E/./shared/quill-editor/quill-editor.component.tsx", "webpack://_N_E/./node_modules/react-bootstrap/esm/CardBody.js", "webpack://_N_E/./node_modules/react-bootstrap/esm/CardFooter.js", "webpack://_N_E/./node_modules/react-bootstrap/esm/CardHeader.js", "webpack://_N_E/./node_modules/react-bootstrap/esm/CardImg.js", "webpack://_N_E/./node_modules/react-bootstrap/esm/CardImgOverlay.js", "webpack://_N_E/./node_modules/react-bootstrap/esm/CardLink.js", "webpack://_N_E/./node_modules/react-bootstrap/esm/CardSubtitle.js", "webpack://_N_E/./node_modules/react-bootstrap/esm/CardText.js", "webpack://_N_E/./node_modules/react-bootstrap/esm/CardTitle.js", "webpack://_N_E/./node_modules/react-bootstrap/esm/Card.js", "webpack://_N_E/./components/common/FormikRadio.tsx", "webpack://_N_E/./components/common/FormValidation.tsx", "webpack://_N_E/./components/common/ValidationFormWrapper.tsx", "webpack://_N_E/./components/common/FormikTextInput.tsx", "webpack://_N_E/./node_modules/react-bootstrap/esm/CardHeaderContext.js", "webpack://_N_E/?19ec", "webpack://_N_E/./node_modules/react-bootstrap/esm/types.js", "webpack://_N_E/./node_modules/react-bootstrap/esm/DropdownButton.js"], "sourcesContent": ["import React, { useRef, useEffect, useState } from 'react';\r\n\r\ninterface SimpleRichTextEditorProps {\r\n  value: string;\r\n  onChange: (content: string) => void;\r\n  placeholder?: string;\r\n  height?: number;\r\n  disabled?: boolean;\r\n}\r\n\r\nconst SimpleRichTextEditor: React.FC<SimpleRichTextEditorProps> = ({\r\n  value,\r\n  onChange,\r\n  placeholder = 'Write something...',\r\n  height = 300,\r\n  disabled = false,\r\n}) => {\r\n  const editorRef = useRef<HTMLDivElement>(null);\r\n  const [isFocused, setIsFocused] = useState(false);\r\n\r\n  // Initialize editor with HTML content\r\n  useEffect(() => {\r\n    if (editorRef.current && typeof window !== 'undefined') {\r\n      // Only update if the editor doesn't have focus to prevent cursor jumping\r\n      if (!isFocused && editorRef.current.innerHTML !== value) {\r\n        editorRef.current.innerHTML = value || '';\r\n      }\r\n    }\r\n  }, [value, isFocused]);\r\n\r\n  // Handle content changes\r\n  const handleInput = () => {\r\n    if (editorRef.current && onChange) {\r\n      onChange(editorRef.current.innerHTML);\r\n    }\r\n  };\r\n\r\n  // Simple toolbar buttons\r\n  const execCommand = (command: string, value?: string) => {\r\n    if (typeof document !== 'undefined') {\r\n      document.execCommand(command, false, value || '');\r\n      handleInput();\r\n      editorRef.current?.focus();\r\n    }\r\n  };\r\n\r\n  return (\r\n    <div className=\"simple-rich-text-editor\" style={{ border: '1px solid #ccc' }}>\r\n      {typeof window !== 'undefined' && (\r\n      <>\r\n        <div className=\"toolbar\" style={{ padding: '8px', borderBottom: '1px solid #ccc', background: '#f5f5f5' }}>\r\n            <button\r\n              type=\"button\"\r\n              onClick={() => execCommand('bold')}\r\n              style={{ margin: '0 5px', padding: '3px 8px' }}\r\n            >\r\n              <strong>B</strong>\r\n            </button>\r\n            <button\r\n              type=\"button\"\r\n              onClick={() => execCommand('italic')}\r\n              style={{ margin: '0 5px', padding: '3px 8px' }}\r\n            >\r\n              <em>I</em>\r\n            </button>\r\n            <button\r\n              type=\"button\"\r\n              onClick={() => execCommand('underline')}\r\n              style={{ margin: '0 5px', padding: '3px 8px' }}\r\n            >\r\n              <u>U</u>\r\n            </button>\r\n            <button\r\n              type=\"button\"\r\n              onClick={() => execCommand('insertOrderedList')}\r\n              style={{ margin: '0 5px', padding: '3px 8px' }}\r\n            >\r\n              OL\r\n            </button>\r\n            <button\r\n              type=\"button\"\r\n              onClick={() => execCommand('insertUnorderedList')}\r\n              style={{ margin: '0 5px', padding: '3px 8px' }}\r\n            >\r\n              UL\r\n            </button>\r\n            <button\r\n              type=\"button\"\r\n              onClick={() => {\r\n                const url = prompt('Enter the link URL');\r\n                if (url) execCommand('createLink', url);\r\n              }}\r\n              style={{ margin: '0 5px', padding: '3px 8px' }}\r\n            >\r\n              Link\r\n            </button>\r\n          </div>\r\n          <div\r\n            ref={editorRef}\r\n            contentEditable={!disabled}\r\n            onInput={handleInput}\r\n            onFocus={() => setIsFocused(true)}\r\n            onBlur={() => setIsFocused(false)}\r\n            style={{\r\n              padding: '15px',\r\n              minHeight: height,\r\n              maxHeight: height * 2,\r\n              overflow: 'auto',\r\n              outline: 'none',\r\n            }}\r\n            data-placeholder={!value ? placeholder : ''}\r\n            suppressContentEditableWarning={true}\r\n          >\r\n          </div>\r\n      </>\r\n      )}\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default SimpleRichTextEditor;\r\n", "// React Imports\r\nimport React from \"react\";\r\nimport SimpleRichTextEditor from \"../../components/common/SimpleRichTextEditor\";\r\n\r\ninterface IEditorComponentProps {\r\n  initContent: string | undefined;\r\n  onChange: Function;\r\n}\r\n\r\nexport const EditorComponent: React.FC<IEditorComponentProps> = (props) => {\r\n  const { initContent, onChange } = props;\r\n\r\n  return (\r\n    <SimpleRichTextEditor\r\n      value={initContent || \"\"}\r\n      onChange={(content) => onChange(content)}\r\n    />\r\n  );\r\n};\r\n", "\"use client\";\n\nimport * as React from 'react';\nimport classNames from 'classnames';\nimport { useBootstrapPrefix } from './ThemeProvider';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst CardBody = /*#__PURE__*/React.forwardRef(({\n  className,\n  bsPrefix,\n  as: Component = 'div',\n  ...props\n}, ref) => {\n  bsPrefix = useBootstrapPrefix(bsPrefix, 'card-body');\n  return /*#__PURE__*/_jsx(Component, {\n    ref: ref,\n    className: classNames(className, bsPrefix),\n    ...props\n  });\n});\nCardBody.displayName = 'CardBody';\nexport default CardBody;", "\"use client\";\n\nimport * as React from 'react';\nimport classNames from 'classnames';\nimport { useBootstrapPrefix } from './ThemeProvider';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst CardFooter = /*#__PURE__*/React.forwardRef(({\n  className,\n  bsPrefix,\n  as: Component = 'div',\n  ...props\n}, ref) => {\n  bsPrefix = useBootstrapPrefix(bsPrefix, 'card-footer');\n  return /*#__PURE__*/_jsx(Component, {\n    ref: ref,\n    className: classNames(className, bsPrefix),\n    ...props\n  });\n});\nCardFooter.displayName = 'CardFooter';\nexport default CardFooter;", "\"use client\";\n\nimport classNames from 'classnames';\nimport * as React from 'react';\nimport { useMemo } from 'react';\nimport { useBootstrapPrefix } from './ThemeProvider';\nimport CardHeaderContext from './CardHeaderContext';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst CardHeader = /*#__PURE__*/React.forwardRef(({\n  bsPrefix,\n  className,\n  // Need to define the default \"as\" during prop destructuring to be compatible with styled-components github.com/react-bootstrap/react-bootstrap/issues/3595\n  as: Component = 'div',\n  ...props\n}, ref) => {\n  const prefix = useBootstrapPrefix(bsPrefix, 'card-header');\n  const contextValue = useMemo(() => ({\n    cardHeaderBsPrefix: prefix\n  }), [prefix]);\n  return /*#__PURE__*/_jsx(CardHeaderContext.Provider, {\n    value: contextValue,\n    children: /*#__PURE__*/_jsx(Component, {\n      ref: ref,\n      ...props,\n      className: classNames(className, prefix)\n    })\n  });\n});\nCardHeader.displayName = 'CardHeader';\nexport default CardHeader;", "\"use client\";\n\nimport classNames from 'classnames';\nimport * as React from 'react';\nimport { useBootstrapPrefix } from './ThemeProvider';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst CardImg = /*#__PURE__*/React.forwardRef(\n// Need to define the default \"as\" during prop destructuring to be compatible with styled-components github.com/react-bootstrap/react-bootstrap/issues/3595\n({\n  bsPrefix,\n  className,\n  variant,\n  as: Component = 'img',\n  ...props\n}, ref) => {\n  const prefix = useBootstrapPrefix(bsPrefix, 'card-img');\n  return /*#__PURE__*/_jsx(Component, {\n    ref: ref,\n    className: classNames(variant ? `${prefix}-${variant}` : prefix, className),\n    ...props\n  });\n});\nCardImg.displayName = 'CardImg';\nexport default CardImg;", "\"use client\";\n\nimport * as React from 'react';\nimport classNames from 'classnames';\nimport { useBootstrapPrefix } from './ThemeProvider';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst CardImgOverlay = /*#__PURE__*/React.forwardRef(({\n  className,\n  bsPrefix,\n  as: Component = 'div',\n  ...props\n}, ref) => {\n  bsPrefix = useBootstrapPrefix(bsPrefix, 'card-img-overlay');\n  return /*#__PURE__*/_jsx(Component, {\n    ref: ref,\n    className: classNames(className, bsPrefix),\n    ...props\n  });\n});\nCardImgOverlay.displayName = 'CardImgOverlay';\nexport default CardImgOverlay;", "\"use client\";\n\nimport * as React from 'react';\nimport classNames from 'classnames';\nimport { useBootstrapPrefix } from './ThemeProvider';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst CardLink = /*#__PURE__*/React.forwardRef(({\n  className,\n  bsPrefix,\n  as: Component = 'a',\n  ...props\n}, ref) => {\n  bsPrefix = useBootstrapPrefix(bsPrefix, 'card-link');\n  return /*#__PURE__*/_jsx(Component, {\n    ref: ref,\n    className: classNames(className, bsPrefix),\n    ...props\n  });\n});\nCardLink.displayName = 'CardLink';\nexport default CardLink;", "\"use client\";\n\nimport * as React from 'react';\nimport classNames from 'classnames';\nimport { useBootstrapPrefix } from './ThemeProvider';\nimport divWithClassName from './divWithClassName';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst DivStyledAsH6 = divWithClassName('h6');\nconst CardSubtitle = /*#__PURE__*/React.forwardRef(({\n  className,\n  bsPrefix,\n  as: Component = DivStyledAsH6,\n  ...props\n}, ref) => {\n  bsPrefix = useBootstrapPrefix(bsPrefix, 'card-subtitle');\n  return /*#__PURE__*/_jsx(Component, {\n    ref: ref,\n    className: classNames(className, bsPrefix),\n    ...props\n  });\n});\nCardSubtitle.displayName = 'CardSubtitle';\nexport default CardSubtitle;", "\"use client\";\n\nimport * as React from 'react';\nimport classNames from 'classnames';\nimport { useBootstrapPrefix } from './ThemeProvider';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst CardText = /*#__PURE__*/React.forwardRef(({\n  className,\n  bsPrefix,\n  as: Component = 'p',\n  ...props\n}, ref) => {\n  bsPrefix = useBootstrapPrefix(bsPrefix, 'card-text');\n  return /*#__PURE__*/_jsx(Component, {\n    ref: ref,\n    className: classNames(className, bsPrefix),\n    ...props\n  });\n});\nCardText.displayName = 'CardText';\nexport default CardText;", "\"use client\";\n\nimport * as React from 'react';\nimport classNames from 'classnames';\nimport { useBootstrapPrefix } from './ThemeProvider';\nimport divWithClassName from './divWithClassName';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst DivStyledAsH5 = divWithClassName('h5');\nconst CardTitle = /*#__PURE__*/React.forwardRef(({\n  className,\n  bsPrefix,\n  as: Component = DivStyledAsH5,\n  ...props\n}, ref) => {\n  bsPrefix = useBootstrapPrefix(bsPrefix, 'card-title');\n  return /*#__PURE__*/_jsx(Component, {\n    ref: ref,\n    className: classNames(className, bsPrefix),\n    ...props\n  });\n});\nCardTitle.displayName = 'CardTitle';\nexport default CardTitle;", "\"use client\";\n\nimport classNames from 'classnames';\nimport * as React from 'react';\nimport { useBootstrapPrefix } from './ThemeProvider';\nimport CardBody from './CardBody';\nimport CardFooter from './CardFooter';\nimport CardHeader from './CardHeader';\nimport CardImg from './CardImg';\nimport CardImgOverlay from './CardImgOverlay';\nimport CardLink from './CardLink';\nimport CardSubtitle from './CardSubtitle';\nimport CardText from './CardText';\nimport CardTitle from './CardTitle';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst Card = /*#__PURE__*/React.forwardRef(({\n  bsPrefix,\n  className,\n  bg,\n  text,\n  border,\n  body = false,\n  children,\n  // Need to define the default \"as\" during prop destructuring to be compatible with styled-components github.com/react-bootstrap/react-bootstrap/issues/3595\n  as: Component = 'div',\n  ...props\n}, ref) => {\n  const prefix = useBootstrapPrefix(bsPrefix, 'card');\n  return /*#__PURE__*/_jsx(Component, {\n    ref: ref,\n    ...props,\n    className: classNames(className, prefix, bg && `bg-${bg}`, text && `text-${text}`, border && `border-${border}`),\n    children: body ? /*#__PURE__*/_jsx(CardBody, {\n      children: children\n    }) : children\n  });\n});\nCard.displayName = 'Card';\nexport default Object.assign(Card, {\n  Img: CardImg,\n  Title: CardTitle,\n  Subtitle: CardSubtitle,\n  Body: CardBody,\n  Link: CardLink,\n  Text: CardText,\n  Header: CardHeader,\n  Footer: CardFooter,\n  ImgOverlay: CardImgOverlay\n});", "import React from 'react';\r\nimport { useFormikContext, Field } from 'formik';\r\nimport { Form } from 'react-bootstrap';\r\n\r\ninterface RadioItemProps {\r\n  id: string;\r\n  label: string;\r\n  value: string;\r\n  name?: string;\r\n  disabled?: boolean;\r\n}\r\n\r\ninterface RadioGroupProps {\r\n  name: string;\r\n  valueSelected: string;\r\n  onChange: (e: React.ChangeEvent<HTMLInputElement>) => void;\r\n  errorMessage?: string;\r\n  children: React.ReactNode;\r\n}\r\n\r\n// Type Guard to ensure child.props is an object\r\nfunction isObject(props: any): props is { [key: string]: any } {\r\n  return typeof props === 'object' && props !== null;\r\n}\r\n\r\nconst RadioItem: React.FC<RadioItemProps> = ({ id, label, value, name, disabled }) => {\r\n  const { values, setFieldValue } = useFormikContext<any>();\r\n  const fieldName = name || id;\r\n\r\n  return (\r\n    <Form.Check\r\n      type=\"radio\"\r\n      id={id}\r\n      label={label}\r\n      value={value}\r\n      name={fieldName}\r\n      checked={values[fieldName] === value}\r\n      onChange={(e) => {\r\n        setFieldValue(fieldName, e.target.value);\r\n      }}\r\n      disabled={disabled}\r\n      inline\r\n    />\r\n  );\r\n};\r\n\r\nconst RadioGroup: React.FC<RadioGroupProps> = ({\r\n  name,\r\n  valueSelected,\r\n  onChange,\r\n  errorMessage,\r\n  children,\r\n}) => {\r\n  const { errors, touched } = useFormikContext<any>();\r\n  const hasError = touched[name] && errors[name];\r\n\r\n  // Create a context to pass the name to RadioItems\r\n  const radioContext = React.useMemo(() => ({ name }), [name]);\r\n\r\n  // Clone children to pass the name, ensuring props are spread safely\r\n  const childrenWithProps = React.Children.map(children, (child) => {\r\n    if (React.isValidElement(child)) {\r\n      // Ensure child.props is an object before spreading\r\n      if (isObject(child.props)) {\r\n        return React.cloneElement(child as React.ReactElement<RadioItemProps>, {\r\n          name,\r\n          ...child.props, // Safely spread child.props\r\n        });\r\n      }\r\n    }\r\n    return child;\r\n  });\r\n\r\n  return (\r\n    <div>\r\n      <div className=\"radio-group\">\r\n        {childrenWithProps}\r\n      </div>\r\n      {hasError && (\r\n        <div className=\"invalid-feedback d-block\">\r\n          {errorMessage || (typeof errors[name] === 'string' ? errors[name] : String(errors[name]))}\r\n        </div>\r\n      )}\r\n    </div>\r\n  );\r\n};\r\n\r\nexport const Radio = {\r\n  RadioGroup,\r\n  RadioItem,\r\n};\r\n\r\nexport default Radio;\r\n", "// This file exports all the components needed to replace react-bootstrap4-form-validation\r\n// It serves as a drop-in replacement for the original library\r\n\r\nimport ValidationForm from './ValidationFormWrapper';\r\nimport { TextInput, SelectGroup } from './FormikTextInput';\r\nimport { Radio } from './FormikRadio';\r\n\r\n// Export all components\r\nexport {\r\n  ValidationForm,\r\n  TextInput,\r\n  SelectGroup,\r\n  Radio\r\n};\r\n\r\n// Also export as default for convenience\r\nexport default {\r\n  ValidationForm,\r\n  TextInput,\r\n  SelectGroup,\r\n  Radio\r\n};\r\n", "import React, { forwardRef } from 'react';\r\nimport { Formik, Form, FormikProps, FormikHelpers } from 'formik';\r\nimport * as Yup from 'yup';\r\n\r\n// This is a wrapper component that replaces react-bootstrap4-form-validation with Formik\r\ninterface MockEvent {\r\n  preventDefault: () => void;\r\n  stopPropagation: () => void;\r\n  currentTarget: (EventTarget & Element) | null;\r\n  target: EventTarget | null;\r\n  nativeEvent: Event;\r\n  bubbles: boolean;\r\n  cancelable: boolean;\r\n  defaultPrevented: boolean;\r\n  eventPhase: number;\r\n  isTrusted: boolean;\r\n  timeStamp: number;\r\n  type: string;\r\n  isDefaultPrevented: () => boolean;\r\n  isPropagationStopped: () => boolean;\r\n  persist: () => void;\r\n}\r\n\r\ninterface ValidationFormWrapperProps {\r\n  children: React.ReactNode | ((formikProps: FormikProps<any>) => React.ReactNode);\r\n  onSubmit: (event: React.FormEvent | MockEvent, values?: Record<string, any>, actions?: FormikHelpers<Record<string, any>>) => void;\r\n  onErrorSubmit?: (errors: any) => void;\r\n  initialValues?: Record<string, any>;\r\n  enableReinitialize?: boolean;\r\n  autoComplete?: string;\r\n  className?: string;\r\n  onKeyPress?: (e: any) => void;\r\n}\r\n\r\nconst ValidationFormWrapper = forwardRef<HTMLFormElement, ValidationFormWrapperProps>((props, ref) => {\r\n  const { children, onSubmit, autoComplete, className, onKeyPress, initialValues, ...rest } = props;\r\n\r\n  // Create an empty validation schema by default\r\n  const validationSchema = Yup.object().shape({});\r\n\r\n  return (\r\n    <Formik\r\n      initialValues={initialValues || {}}\r\n      validationSchema={validationSchema}\r\n      onSubmit={(values: Record<string, any>, actions: FormikHelpers<Record<string, any>>) => {\r\n        // Create a mock event object with preventDefault method and currentTarget\r\n        const mockEvent: MockEvent = {\r\n          preventDefault: () => {},\r\n          stopPropagation: () => {},\r\n          currentTarget: null, // Set to null to avoid checkValidity errors\r\n          target: null,\r\n          nativeEvent: new Event('submit'),\r\n          bubbles: false,\r\n          cancelable: true,\r\n          defaultPrevented: false,\r\n          eventPhase: 0,\r\n          isTrusted: false,\r\n          timeStamp: Date.now(),\r\n          type: 'submit',\r\n          isDefaultPrevented: () => false,\r\n          isPropagationStopped: () => false,\r\n          persist: () => {}\r\n        };\r\n\r\n        if (onSubmit) {\r\n          // Pass the mock event object to maintain compatibility with the original code\r\n          onSubmit(mockEvent, values, actions);\r\n        }\r\n      }}\r\n      {...rest}\r\n    >\r\n      {(formikProps: FormikProps<any>) => (\r\n        <Form\r\n          ref={ref}\r\n          onSubmit={formikProps.handleSubmit}\r\n          autoComplete={autoComplete}\r\n          className={className}\r\n          onKeyPress={onKeyPress}\r\n        >\r\n          {typeof children === 'function' ? children(formikProps) : children}\r\n        </Form>\r\n      )}\r\n    </Formik>\r\n  );\r\n});\r\n\r\nValidationFormWrapper.displayName = 'ValidationFormWrapper';\r\n\r\nexport default ValidationFormWrapper;\r\n", "import React from 'react';\r\nimport { Form } from 'react-bootstrap';\r\nimport { Field, ErrorMessage, useField } from 'formik';\r\n\r\n// This component mimics the TextInput component from react-bootstrap4-form-validation\r\n// but uses Formik under the hood\r\nexport const TextInput = ({\r\n  name,\r\n  id,\r\n  required,\r\n  validator,\r\n  errorMessage,\r\n  onChange,\r\n  value,\r\n  as,\r\n  multiline,\r\n  rows,\r\n  pattern,\r\n  ...props\r\n}: any) => {\r\n  // Create a custom validation function that mimics the original validator\r\n  const validate = (val: any) => {\r\n    // Safely convert value to string and trim\r\n    const stringVal = typeof val === 'string' ? val : String(val || '');\r\n    if (required && (!val || stringVal.trim() === '')) {\r\n      return errorMessage?.validator || 'This field is required';\r\n    }\r\n\r\n    if (validator && !validator(val)) {\r\n      return errorMessage?.validator || 'Invalid value';\r\n    }\r\n\r\n    if (pattern && val) {\r\n      const regex = new RegExp(pattern);\r\n      if (!regex.test(val)) {\r\n        return errorMessage?.pattern || 'Invalid format';\r\n      }\r\n    }\r\n\r\n    return undefined;\r\n  };\r\n\r\n  return (\r\n    <Field name={name} validate={validate}>\r\n      {({ field, meta }: any) => (\r\n        <>\r\n          <Form.Control\r\n            {...field}\r\n            {...props}\r\n            id={id}\r\n            as={as || 'input'}\r\n            rows={rows}\r\n            isInvalid={meta.touched && !!meta.error}\r\n            onChange={(e: React.ChangeEvent<HTMLInputElement>) => {\r\n              field.onChange(e);\r\n              if (onChange) onChange(e);\r\n            }}\r\n            value={value !== undefined ? value : field.value}\r\n          />\r\n          {meta.touched && meta.error ? (\r\n            <Form.Control.Feedback type=\"invalid\">\r\n              {meta.error}\r\n            </Form.Control.Feedback>\r\n          ) : null}\r\n        </>\r\n      )}\r\n    </Field>\r\n  );\r\n};\r\n\r\n// This component mimics the SelectGroup component from react-bootstrap4-form-validation\r\n// but uses Formik under the hood\r\nexport const SelectGroup = ({\r\n  name,\r\n  id,\r\n  required,\r\n  errorMessage,\r\n  onChange,\r\n  value,\r\n  children,\r\n  ...props\r\n}: any) => {\r\n  // Create a custom validation function that mimics the original validator\r\n  const validate = (val: any) => {\r\n    if (required && (!val || val === '')) {\r\n      return errorMessage?.validator || 'This field is required';\r\n    }\r\n\r\n    return undefined;\r\n  };\r\n\r\n  return (\r\n    <Field name={name} validate={validate}>\r\n      {({ field, meta }: any) => (\r\n        <>\r\n          <Form.Control\r\n            as=\"select\"\r\n            {...field}\r\n            {...props}\r\n            id={id}\r\n            isInvalid={meta.touched && !!meta.error}\r\n            onChange={(e: React.ChangeEvent<HTMLSelectElement>) => {\r\n              field.onChange(e);\r\n              if (onChange) onChange(e);\r\n            }}\r\n            value={value !== undefined ? value : field.value}\r\n          >\r\n            {children}\r\n          </Form.Control>\r\n          {meta.touched && meta.error ? (\r\n            <Form.Control.Feedback type=\"invalid\">\r\n              {meta.error}\r\n            </Form.Control.Feedback>\r\n          ) : null}\r\n        </>\r\n      )}\r\n    </Field>\r\n  );\r\n};\r\n\r\n// Export both components as named exports and as a default object\r\nexport default {\r\n  TextInput,\r\n  SelectGroup\r\n};\r\n", "\"use client\";\n\nimport * as React from 'react';\nconst context = /*#__PURE__*/React.createContext(null);\ncontext.displayName = 'CardHeaderContext';\nexport default context;", "\n    (window.__NEXT_P = window.__NEXT_P || []).push([\n      \"/adminsettings/hazard/forms\",\n      function () {\n        return require(\"private-next-pages/adminsettings/hazard/forms.tsx\");\n      }\n    ]);\n    if(module.hot) {\n      module.hot.dispose(function () {\n        window.__NEXT_P.push([\"/adminsettings/hazard/forms\"])\n      });\n    }\n  ", "import PropTypes from 'prop-types';\nconst alignDirection = PropTypes.oneOf(['start', 'end']);\nexport const alignPropType = PropTypes.oneOfType([alignDirection, PropTypes.shape({\n  sm: alignDirection\n}), PropTypes.shape({\n  md: alignDirection\n}), PropTypes.shape({\n  lg: alignDirection\n}), PropTypes.shape({\n  xl: alignDirection\n}), PropTypes.shape({\n  xxl: alignDirection\n}), PropTypes.object]);", "import * as React from 'react';\nimport PropTypes from 'prop-types';\nimport Dropdown from './Dropdown';\nimport DropdownToggle from './DropdownToggle';\nimport DropdownMenu from './DropdownMenu';\nimport { alignPropType } from './types';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nimport { jsxs as _jsxs } from \"react/jsx-runtime\";\nconst propTypes = {\n  /**\n   * An html id attribute for the Toggle button, necessary for assistive technologies, such as screen readers.\n   * @type {string}\n   */\n  id: PropTypes.string,\n  /** An `href` passed to the Toggle component */\n  href: PropTypes.string,\n  /** An `onClick` handler passed to the Toggle component */\n  onClick: PropTypes.func,\n  /** The content of the non-toggle Button.  */\n  title: PropTypes.node.isRequired,\n  /** Disables both Buttons  */\n  disabled: PropTypes.bool,\n  /**\n   * Aligns the dropdown menu.\n   *\n   * _see [DropdownMenu](#dropdown-menu-props) for more details_\n   *\n   * @type {\"start\"|\"end\"|{ sm: \"start\"|\"end\" }|{ md: \"start\"|\"end\" }|{ lg: \"start\"|\"end\" }|{ xl: \"start\"|\"end\"}|{ xxl: \"start\"|\"end\"} }\n   */\n  align: alignPropType,\n  /** An ARIA accessible role applied to the Menu component. When set to 'menu', The dropdown */\n  menuRole: PropTypes.string,\n  /** Whether to render the dropdown menu in the DOM before the first time it is shown */\n  renderMenuOnMount: PropTypes.bool,\n  /**\n   *  Which event when fired outside the component will cause it to be closed.\n   *\n   * _see [DropdownMenu](#dropdown-menu-props) for more details_\n   */\n  rootCloseEvent: PropTypes.string,\n  /**\n   * Menu color variant.\n   *\n   * Omitting this will use the default light color.\n   */\n  menuVariant: PropTypes.oneOf(['dark']),\n  /**\n   * Allow Dropdown to flip in case of an overlapping on the reference element. For more information refer to\n   * Popper.js's flip [docs](https://popper.js.org/docs/v2/modifiers/flip/).\n   *\n   */\n  flip: PropTypes.bool,\n  /** @ignore */\n  bsPrefix: PropTypes.string,\n  /** @ignore */\n  variant: PropTypes.string,\n  /** @ignore */\n  size: PropTypes.string\n};\n\n/**\n * A convenience component for simple or general use dropdowns. Renders a `Button` toggle and all `children`\n * are passed directly to the default `Dropdown.Menu`. This component accepts all of\n * [`Dropdown`'s props](#dropdown-props).\n *\n * _All unknown props are passed through to the `Dropdown` component._ Only\n * the Button `variant`, `size` and `bsPrefix` props are passed to the toggle,\n * along with menu-related props are passed to the `Dropdown.Menu`\n */\nconst DropdownButton = /*#__PURE__*/React.forwardRef(({\n  title,\n  children,\n  bsPrefix,\n  rootCloseEvent,\n  variant,\n  size,\n  menuRole,\n  renderMenuOnMount,\n  disabled,\n  href,\n  id,\n  menuVariant,\n  flip,\n  ...props\n}, ref) => /*#__PURE__*/_jsxs(Dropdown, {\n  ref: ref,\n  ...props,\n  children: [/*#__PURE__*/_jsx(DropdownToggle, {\n    id: id,\n    href: href,\n    size: size,\n    variant: variant,\n    disabled: disabled,\n    childBsPrefix: bsPrefix,\n    children: title\n  }), /*#__PURE__*/_jsx(DropdownMenu, {\n    role: menuRole,\n    renderOnMount: renderMenuOnMount,\n    rootCloseEvent: rootCloseEvent,\n    variant: menuVariant,\n    flip: flip,\n    children: children\n  })]\n}));\nDropdownButton.displayName = 'DropdownButton';\nDropdownButton.propTypes = propTypes;\nexport default DropdownButton;"], "names": ["value", "onChange", "placeholder", "SimpleRichTextEditor", "height", "disabled", "editor<PERSON><PERSON>", "useRef", "isFocused", "setIsFocused", "useState", "useEffect", "current", "innerHTML", "handleInput", "execCommand", "command", "document", "focus", "div", "className", "style", "border", "padding", "borderBottom", "background", "button", "type", "onClick", "margin", "strong", "em", "u", "url", "prompt", "ref", "contentEditable", "onInput", "onFocus", "onBlur", "minHeight", "maxHeight", "overflow", "outline", "data-placeholder", "suppressContentEditableWarning", "EditorComponent", "initContent", "props", "content", "CardBody", "React", "bsPrefix", "as", "Component", "useBootstrapPrefix", "_jsx", "classNames", "displayName", "<PERSON><PERSON><PERSON>er", "<PERSON><PERSON><PERSON><PERSON>", "prefix", "contextValue", "useMemo", "cardHeaderBsPrefix", "CardHeaderContext", "Provider", "children", "CardImg", "variant", "CardImgOverlay", "CardLink", "DivStyledAsH6", "divWithClassName", "CardSubtitle", "CardText", "DivStyledAsH5", "CardTitle", "Card", "bg", "text", "body", "Object", "assign", "Img", "Title", "Subtitle", "Body", "Link", "Text", "Header", "Footer", "ImgOverlay", "Radio", "RadioGroup", "name", "valueSelected", "errorMessage", "errors", "touched", "useFormikContext", "<PERSON><PERSON><PERSON><PERSON>", "childrenWithProps", "map", "child", "isObject", "String", "RadioItem", "id", "label", "values", "setFieldValue", "fieldName", "Form", "Check", "checked", "e", "target", "inline", "ValidationForm", "TextInput", "SelectGroup", "ValidationFormWrapper", "forwardRef", "onSubmit", "autoComplete", "onKeyPress", "initialValues", "rest", "validationSchema", "<PERSON><PERSON>", "shape", "<PERSON><PERSON>", "actions", "mockEvent", "preventDefault", "stopPropagation", "currentTarget", "nativeEvent", "Event", "bubbles", "cancelable", "defaultPrevented", "eventPhase", "isTrusted", "timeStamp", "Date", "now", "isDefaultPrevented", "isPropagationStopped", "persist", "formikProps", "handleSubmit", "required", "validator", "multiline", "rows", "pattern", "Field", "validate", "val", "stringVal", "trim", "regex", "RegExp", "test", "field", "meta", "Control", "isInvalid", "error", "undefined", "<PERSON><PERSON><PERSON>", "context", "alignDirection", "PropTypes", "alignPropType", "sm", "md", "lg", "xl", "xxl", "propTypes", "href", "title", "isRequired", "align", "menuRole", "renderMenuOnMount", "rootCloseEvent", "menuVariant", "flip", "size", "DropdownButton", "_jsxs", "Dropdown", "DropdownToggle", "childBsPrefix", "DropdownMenu", "role", "renderOnMount"], "sourceRoot": "", "ignoreList": [2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 16, 18, 19]}