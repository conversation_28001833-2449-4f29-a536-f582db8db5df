(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[5293],{12661:(t,e,i)=>{"use strict";i.r(e),i.d(e,{default:()=>o});var s=i(37876);i(14232);var n=i(32890),a=i(48477),r=i(31753);let o=t=>{let{t:e}=(0,r.Bd)("common");return(0,s.jsx)(n.A,{defaultActiveKey:"2",children:(0,s.jsxs)(n.A.Item,{eventKey:"2",children:[(0,s.jsx)(n.<PERSON><PERSON>,{children:(0,s.jsx)("div",{className:"cardTitle",children:e("Discussions")})}),(0,s.jsx)(n.A.Body,{children:(0,s.jsx)(a.A,{type:"hazard",id:t&&t.routes?t.routes[1]:null})})]})})}},34111:(t,e,i)=>{"use strict";i.r(e),i.d(e,{default:()=>o});var s=i(37876);i(14232);var n=i(32890),a=i(33458),r=i(31753);let o=t=>{let{t:e}=(0,r.Bd)("common");return(0,s.jsx)(n.A,{defaultActiveKey:"1",children:(0,s.jsxs)(n.A.Item,{eventKey:"1",children:[(0,s.jsx)(n.A.Header,{children:(0,s.jsx)("div",{className:"cardTitle",children:e("MediaGallery")})}),(0,s.jsx)(n.A.Body,{children:(0,s.jsx)(a.A,{gallery:t.images,imageSource:t.images_src})})]})})}},39896:(t,e,i)=>{"use strict";i.r(e),i.d(e,{default:()=>l});var s=i(37876);i(14232);var n=i(32890),a=i(48230),r=i.n(a),o=i(31753);let l=t=>{let{t:e}=(0,o.Bd)("common"),{institutionData:i,activeOperations:a,activeProjects:l}=t;return(0,s.jsx)(n.A,{defaultActiveKey:"0",children:(0,s.jsxs)(n.A.Item,{eventKey:"0",children:[(0,s.jsx)(n.A.Header,{children:(0,s.jsx)("div",{className:"cardTitle",children:e("MoreInfo")})}),(0,s.jsxs)(n.A.Body,{className:"institutionDetails ps-4",children:[(0,s.jsxs)("p",{children:[(0,s.jsx)("b",{children:e("OrganisationType")}),":",(0,s.jsxs)("span",{children:[" ",i.type?i.type.title:""]})]}),(0,s.jsxs)("p",{children:[(0,s.jsx)("b",{children:e("Network")}),":",(0,s.jsx)("span",{children:i.networks?i.networks.map((t,e)=>(0,s.jsx)("span",{children:(0,s.jsx)("li",{children:t.title})},e)):""})]}),(0,s.jsxs)("p",{children:[(0,s.jsx)("b",{children:e("ActiveOperation")}),":",(0,s.jsx)("span",{children:a&&a.length>0?a.map((t,e)=>(0,s.jsx)("li",{children:(0,s.jsx)(r(),{href:"/operation/[...routes]",as:"/operation/show/".concat(t._id),children:t.title})},e)):(0,s.jsx)("li",{children:e("NoActiveoperationsfound")})})]}),(0,s.jsxs)("p",{children:[(0,s.jsx)("b",{children:e("Expertise")}),":",(0,s.jsxs)("span",{children:[" ",i.expertise?i.expertise.map((t,e)=>(0,s.jsxs)("li",{children:[t.title," ",(0,s.jsx)("br",{})]},e)):""]})]}),(0,s.jsxs)("p",{children:[(0,s.jsx)("b",{children:e("ActiveProject")}),":",(0,s.jsx)("span",{children:l&&l.length>0?l.map((t,e)=>(0,s.jsx)("li",{children:(0,s.jsx)(r(),{href:"/project/[...routes]",as:"/project/show/".concat(t._id),children:t.title})},e)):(0,s.jsx)("li",{children:e("NoActiveprojectsfound")})})]}),(0,s.jsxs)("p",{children:[(0,s.jsx)("b",{children:e("Department")}),":",(0,s.jsx)("span",{children:i&&i.department?i.department:e("Nodepartmentfound")})]}),(0,s.jsxs)("p",{children:[(0,s.jsx)("b",{children:e("Unit")}),":",(0,s.jsx)("span",{children:i&&i.unit?i.unit:e("Nounitfound")})]})]})]})})}},45511:(t,e,i)=>{"use strict";i.r(e),i.d(e,{default:()=>a});var s=i(37876);i(14232);var n=i(9810);let a=t=>(0,s.jsx)(s.Fragment,{children:(0,s.jsxs)("div",{className:"institution-image-block",children:[!t.imageLoading&&t.institutionData&&t.institutionData.header&&t.institutionData.header._id?(0,s.jsx)("img",{className:"institution-image-cover",src:"".concat("http://localhost:3001/api/v1","/image/show/").concat(t.institutionData.header._id)}):"",t.imageLoading?(0,s.jsx)("div",{className:"institution-imageLoader",children:(0,s.jsx)("div",{className:"spinner-border text-primary"})}):"",t.imageLoading||!t.institutionData||t.institutionData.header?"":(0,s.jsx)("img",{className:"institution-image-cover",src:"/images/rki_institute.7cb751d6.jpg"}),(0,s.jsx)("div",{className:"institution-image-inner-content",children:(0,s.jsx)(n.default,{institutionData:t.institutionData,routeData:t.prop,prop:t.prop,editAccess:t.editAccess,focalPoints:t.focalPoints})})]})})},47678:(t,e,i)=>{"use strict";i.r(e),i.d(e,{default:()=>u});var s=i(37876),n=i(14232),a=i(11041),r=i(21772),o=i(56970),l=i(37784),c=i(31753),d=i(49244);let u=t=>{let{t:e}=(0,c.Bd)("common"),[i,u]=(0,n.useState)(!1),[h,p]=(0,n.useState)([]),[m,j]=(0,n.useState)(""),x=s=>{switch(u(!i),j(e(s)),s){case"Partners":p(t.institutionData&&t.institutionData.partners?t.institutionData.partners:[]);break;case"Operations":p(t.institutionStatus&&t.institutionStatus.operationData?t.institutionStatus.operationData:[]);break;case"Projects":p(t.institutionStatus&&t.institutionStatus.projectData?t.institutionStatus.projectData:[])}},_=t=>{u(t)};return(0,s.jsx)(s.Fragment,{children:(0,s.jsxs)("div",{className:"institution-infographic-block",children:[(0,s.jsxs)(o.A,{children:[function(t,e,i){return(0,s.jsx)(l.A,{children:(0,s.jsxs)("div",{className:"list-group-item d-flex clickable",onClick:()=>t("Partners"),children:[(0,s.jsx)("div",{className:"quickinfo-img",children:(0,s.jsx)(r.g,{icon:a.gdJ,color:"#fff",size:"2x"})}),(0,s.jsxs)("div",{className:"quickinfoDesc",children:[(0,s.jsx)("h5",{children:e("Partners")}),(0,s.jsx)("h4",{children:i&&i.partners?i.partners:0})]})]})})}(x,e,t.institutionStatus),(0,s.jsx)(l.A,{children:(0,s.jsxs)("div",{className:"list-group-item d-flex clickable",onClick:()=>x("Operations"),children:[(0,s.jsx)("div",{className:"quickinfo-img",children:(0,s.jsx)(r.g,{icon:a.qIE,color:"#fff",size:"2x"})}),(0,s.jsxs)("div",{className:"quickinfoDesc",children:[(0,s.jsx)("h5",{children:e("Operations")}),(0,s.jsx)("h4",{children:t.institutionStatus&&t.institutionStatus.operations?t.institutionStatus.operations:0})]})]})}),(0,s.jsx)(l.A,{children:function(t,e,i){return(0,s.jsxs)("div",{className:"list-group-item d-flex clickable",onClick:()=>t("Projects"),children:[(0,s.jsx)("div",{className:"quickinfo-img",children:(0,s.jsx)(r.g,{icon:a.Uj9,color:"#fff",size:"2x"})}),(0,s.jsxs)("div",{className:"quickinfoDesc",children:[(0,s.jsx)("h5",{children:e("Projects")}),(0,s.jsx)("h4",{children:i&&i.projects?i.projects:0})]})]})}(x,e,t.institutionStatus)})]}),(0,s.jsx)(d.default,{isShow:i,isClose:t=>_(t),data:h,name:m})]})})}},49244:(t,e,i)=>{"use strict";i.r(e),i.d(e,{default:()=>l});var s=i(37876);i(14232);var n=i(31195),a=i(48230),r=i.n(a),o=i(31753);let l=t=>{let{t:e}=(0,o.Bd)("common"),{isShow:i,isClose:a,data:l,name:c}=t,d="Projects"===c?"project":"Partners"===c?"institution":"operation",u=l.length>0?l.map((t,e)=>(0,s.jsxs)("span",{children:[(0,s.jsx)(r(),{href:"/".concat(d,"/show/").concat(t._id),children:t.title}),(0,s.jsx)("hr",{})]},e)):(0,s.jsxs)("p",{children:[e("No")," ",c," ",e("Found"),"."]});return(0,s.jsxs)(n.A,{centered:!0,size:"sm",show:i,onHide:()=>a(!i),"aria-labelledby":"modal_popup",children:[(0,s.jsx)(n.A.Header,{closeButton:!0,children:(0,s.jsx)(n.A.Title,{children:c})}),(0,s.jsx)(n.A.Body,{children:(0,s.jsx)("div",{children:u})})]})}},64330:(t,e,i)=>{"use strict";i.r(e),i.d(e,{default:()=>c});var s=i(37876);i(14232);var n=i(32890),a=i(12661),r=i(33859),o=i(39896),l=i(34111);let c=t=>{let e=(0,r.canViewDiscussionUpdate)(()=>(0,s.jsx)(a.default,{...t.prop}));return(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)(n.A,{className:"countryAccordionNew",children:(0,s.jsx)(o.default,{...t})}),(0,s.jsx)(n.A,{className:"countryAccordionNew",children:(0,s.jsx)(l.default,{...t.institutionData})}),(0,s.jsx)(n.A,{className:"countryAccordionNew",children:(0,s.jsx)(e,{})})]})}},84135:function(t,e,i){(function(t){"use strict";function e(t,e,i,s){var n={m:["eine Minute","einer Minute"],h:["eine Stunde","einer Stunde"],d:["ein Tag","einem Tag"],dd:[t+" Tage",t+" Tagen"],w:["eine Woche","einer Woche"],M:["ein Monat","einem Monat"],MM:[t+" Monate",t+" Monaten"],y:["ein Jahr","einem Jahr"],yy:[t+" Jahre",t+" Jahren"]};return e?n[i][0]:n[i][1]}t.defineLocale("de",{months:"Januar_Februar_M\xe4rz_April_Mai_Juni_Juli_August_September_Oktober_November_Dezember".split("_"),monthsShort:"Jan._Feb._M\xe4rz_Apr._Mai_Juni_Juli_Aug._Sep._Okt._Nov._Dez.".split("_"),monthsParseExact:!0,weekdays:"Sonntag_Montag_Dienstag_Mittwoch_Donnerstag_Freitag_Samstag".split("_"),weekdaysShort:"So._Mo._Di._Mi._Do._Fr._Sa.".split("_"),weekdaysMin:"So_Mo_Di_Mi_Do_Fr_Sa".split("_"),weekdaysParseExact:!0,longDateFormat:{LT:"HH:mm",LTS:"HH:mm:ss",L:"DD.MM.YYYY",LL:"D. MMMM YYYY",LLL:"D. MMMM YYYY HH:mm",LLLL:"dddd, D. MMMM YYYY HH:mm"},calendar:{sameDay:"[heute um] LT [Uhr]",sameElse:"L",nextDay:"[morgen um] LT [Uhr]",nextWeek:"dddd [um] LT [Uhr]",lastDay:"[gestern um] LT [Uhr]",lastWeek:"[letzten] dddd [um] LT [Uhr]"},relativeTime:{future:"in %s",past:"vor %s",s:"ein paar Sekunden",ss:"%d Sekunden",m:e,mm:"%d Minuten",h:e,hh:"%d Stunden",d:e,dd:e,w:e,ww:"%d Wochen",M:e,MM:e,y:e,yy:e},dayOfMonthOrdinalParse:/\d{1,2}\./,ordinal:"%d.",week:{dow:1,doy:4}})})(i(10841))},90627:(t,e,i)=>{"use strict";i.r(e),i.d(e,{default:()=>m});var s=i(37876),n=i(14232),a=i(31777),r=i(49589),o=i(82851),l=i.n(o),c=i(37308),d=i(53718),u=i(45511),h=i(47678),p=i(64330);let m=(0,a.Ng)(t=>t)(t=>{let[e,i]=(0,n.useState)({title:"",description:"",dial_code:"",telephone:"",address:{line_1:"",line_2:"",city:""},website:"",type:{title:""},networks:[],expertise:[],focal_points:[],primary_focal_point:"",images:[],header:"",department:"",unit:"",partners:[],images_src:[]}),[a,o]=(0,n.useState)({partners:0,operations:0,projects:0,operationData:[],projectData:[]}),[m,j]=(0,n.useState)([]),[x,_]=(0,n.useState)([]),[f,g]=(0,n.useState)([]),[y,v]=(0,n.useState)(!0),[A,D]=(0,n.useState)(!1),M=async e=>{v(!0);let s=await d.A.get("/institution/".concat(t.routes[1]));v(!1),function(t,e){D(!1),e&&e.roles&&(e.roles.includes("SUPER_ADMIN")||e.roles.includes("PLATFORM_ADMIN")&&t.user==e._id?D(!0):e.roles.includes("GENERAL_USER")&&t.user==e._id&&D(!0))}(s,e),s.dial_code=s&&s.dial_code?s.dial_code:"",i(s)},S=async()=>{o(await d.A.get("/stats/institution/".concat(t.routes[1])))},N=async()=>{let e=await d.A.post("/users/getLoggedUser",{});if(e&&e.roles&&e.roles.length)if(e.roles.includes("SUPER_ADMIN"))w("Approved",e);else{let i=e.institutionInvites.filter(e=>e.institutionId===t.routes[1]);i&&i.length?w(i[0].status,e):w("You dont have access.",e)}},w=(e,i)=>{M(i),S(),k(t.routes[1]),P(t.routes[1])};(0,n.useEffect)(()=>{t.routes&&t.routes[1]&&N()},[]),(0,n.useEffect)(()=>{e&&e.focal_points&&e.focal_points.length>0&&T(l().map(e.focal_points,"_id"))},[e]);let k=async t=>{let e=await b();if(e&&e.length>0){let i=await d.A.get("/operation",{query:{"partners.institution":[t],status:e}});i&&i.data&&i.data.length>0&&j(i.data)}},b=async()=>{let t=await d.A.get("/operation_status");if(t&&t.data&&t.data.length>0){let e=[];return l().forEach(t.data,function(t){("Deployed"===t.title||"Mobilizing"===t.title||"Monitoring"===t.title||"Ongoing"===t.title)&&e.push(t._id)}),e}return[]},P=async t=>{let e=await L();if(e&&e.length>0){let i=await d.A.get("/project",{query:{"partner_institutions.partner_institution":[t],status:e}});i&&i.data&&i.data.length>0&&_(i.data)}},L=async()=>{let t=await d.A.get("/projectStatus");if(t&&t.data&&t.data.length>0){let e=[];return l().forEach(t.data,function(t){("Ongoing"===t.title||"Planning"===t.title)&&e.push(t._id)}),e}return[]},T=async i=>{let s=await d.A.get("/users",{query:{_id:i},sort:{title:"asc"},limit:"~",select:"-firstname -lastname -password -role -country -region -institution -status -is_focal_point -mobile_number -enabled -created_at -updated_at -dataConsentPolicy -restrictedUsePolicy -acceptCookiesPolicy -withdrawConsentPolicy -medicalConsentPolicy -fullDataProtectionConsentPolicy -emailActivateToken"}),n=[];null==s||s.data.forEach(e=>{null==e||e.institutionInvites.forEach(i=>{i&&"Approved"===i.status&&i.institutionId===t.routes[1]&&n.push({...e,...{institutionId:i.institutionId,institutionName:i.institutionName,institutionStatus:i.status}})})}),n&&n.length>0&&g(n.map(t=>[{...t,isPrimary:t._id===e.primary_focal_point}]).flat().sort((t,e)=>t.isPrimary-e.isPrimary).reverse())};return(0,s.jsx)("div",{children:(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)(r.A,{fluid:!0,className:"_institutionfocalpoint",children:(0,s.jsx)(c.A,{routes:t.routes})}),(0,s.jsx)(u.default,{prop:t,imageLoading:y,institutionData:e,editAccess:A,focalPoints:f}),(0,s.jsx)(h.default,{institutionData:e,institutionStatus:a}),(0,s.jsx)(p.default,{institutionData:e,prop:t,activeProjects:x,activeOperations:m})]})})})}}]);
//# sourceMappingURL=5293-c71567210e3cdd7a.js.map