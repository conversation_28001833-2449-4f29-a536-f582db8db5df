"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[8186],{35611:(e,r,a)=>{a.d(r,{sx:()=>d,s3:()=>n.s3,ks:()=>n.ks,yk:()=>s.A});var s=a(54773),n=a(59200),i=a(37876),l=a(14232),t=a(39593),o=a(29504);let d={RadioGroup:e=>{let{name:r,valueSelected:a,onChange:s,errorMessage:n,children:o}=e,{errors:d,touched:u}=(0,t.j7)(),c=u[r]&&d[r];l.useMemo(()=>({name:r}),[r]);let h=l.Children.map(o,e=>l.isValidElement(e)&&function(e){return"object"==typeof e&&null!==e}(e.props)?l.cloneElement(e,{name:r,...e.props}):e);return(0,i.jsxs)("div",{children:[(0,i.jsx)("div",{className:"radio-group",children:h}),c&&(0,i.jsx)("div",{className:"invalid-feedback d-block",children:n||("string"==typeof d[r]?d[r]:String(d[r]))})]})},RadioItem:e=>{let{id:r,label:a,value:s,name:n,disabled:l}=e,{values:d,setFieldValue:u}=(0,t.j7)(),c=n||r;return(0,i.jsx)(o.A.Check,{type:"radio",id:r,label:a,value:s,name:c,checked:d[c]===s,onChange:e=>{u(c,e.target.value)},disabled:l,inline:!0})}};s.A,n.ks,n.s3},54773:(e,r,a)=>{a.d(r,{A:()=>o});var s=a(37876),n=a(14232),i=a(39593),l=a(91408);let t=(0,n.forwardRef)((e,r)=>{let{children:a,onSubmit:n,autoComplete:t,className:o,onKeyPress:d,initialValues:u,...c}=e,h=l.Ik().shape({});return(0,s.jsx)(i.l1,{initialValues:u||{},validationSchema:h,onSubmit:(e,r)=>{let a={preventDefault:()=>{},stopPropagation:()=>{},currentTarget:null,target:null,nativeEvent:new Event("submit"),bubbles:!1,cancelable:!0,defaultPrevented:!1,eventPhase:0,isTrusted:!1,timeStamp:Date.now(),type:"submit",isDefaultPrevented:()=>!1,isPropagationStopped:()=>!1,persist:()=>{}};n&&n(a,e,r)},...c,children:e=>(0,s.jsx)(i.lV,{ref:r,onSubmit:e.handleSubmit,autoComplete:t,className:o,onKeyPress:d,children:"function"==typeof a?a(e):a})})});t.displayName="ValidationFormWrapper";let o=t},59200:(e,r,a)=>{a.d(r,{ks:()=>l,s3:()=>t});var s=a(37876);a(14232);var n=a(29504),i=a(39593);let l=e=>{let{name:r,id:a,required:l,validator:t,errorMessage:o,onChange:d,value:u,as:c,multiline:h,rows:p,pattern:m,...x}=e;return(0,s.jsx)(i.D0,{name:r,validate:e=>{let r="string"==typeof e?e:String(e||"");return l&&(!e||""===r.trim())?(null==o?void 0:o.validator)||"This field is required":t&&!t(e)?(null==o?void 0:o.validator)||"Invalid value":m&&e&&!new RegExp(m).test(e)?(null==o?void 0:o.pattern)||"Invalid format":void 0},children:e=>{let{field:r,meta:i}=e;return(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)(n.A.Control,{...r,...x,id:a,as:c||"input",rows:p,isInvalid:i.touched&&!!i.error,onChange:e=>{r.onChange(e),d&&d(e)},value:void 0!==u?u:r.value}),i.touched&&i.error?(0,s.jsx)(n.A.Control.Feedback,{type:"invalid",children:i.error}):null]})}})},t=e=>{let{name:r,id:a,required:l,errorMessage:t,onChange:o,value:d,children:u,...c}=e;return(0,s.jsx)(i.D0,{name:r,validate:e=>{if(l&&(!e||""===e))return(null==t?void 0:t.validator)||"This field is required"},children:e=>{let{field:r,meta:i}=e;return(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)(n.A.Control,{as:"select",...r,...c,id:a,isInvalid:i.touched&&!!i.error,onChange:e=>{r.onChange(e),o&&o(e)},value:void 0!==d?d:r.value,children:u}),i.touched&&i.error?(0,s.jsx)(n.A.Control.Feedback,{type:"invalid",children:i.error}):null]})}})}},68186:(e,r,a)=>{a.r(r),a.d(r,{default:()=>w});var s=a(37876),n=a(14232),i=a(49589),l=a(29335),t=a(56970),o=a(37784),d=a(29504),u=a(60282),c=a(35611),h=a(89099),p=a.n(h),m=a(71321),x=a.n(m),j=a(67814),v=a(53718);let g={username:"",email:"",role:"",password:"",confirm_password:"",institution:"",region:[],country:"",enabled:""},A={query:{},sort:{title:"asc"},limit:"~"},w=e=>{let[r,a]=(0,n.useState)(g),[m,w]=(0,n.useState)([]),[y,b]=(0,n.useState)([]),[f,C]=(0,n.useState)([]),[_,S]=(0,n.useState)([]),[k,q]=(0,n.useState)({}),P=(0,h.useRouter)().query.routes||[],R=async()=>{let e=await v.A.get("roles");e&&e.data&&e.data.length>0&&w(e.data)},I=e=>{q(e),a(r=>({...r,username:e.username,email:e.email,role:e.role?e.role._id:"",institution:e.institution?e.institution._id:"",region:e.region?e.region._id.map((e,r)=>({label:e.title,value:e._id})):[],country:e.country?e.country._id:"",enabled:e.enabled?"true":"false"}))},N=async()=>{if(P[1]){let e=await v.A.get("users/".concat(P[1]));e&&e._id&&I(e),T(e.country)}},E=async()=>{let e=await v.A.get("institution");e&&e.data&&e.data.length>0&&b(e.data)},G=async()=>{let e=await v.A.get("/country",A);e&&e.data&&e.data.length>0&&S(e.data)};(0,n.useEffect)(()=>{R(),N(),E(),G()},[]);let M=(0,n.useRef)(null),L=e=>{a(r=>({...r,...e}))},T=async e=>{let r=[];if(e){let a=await v.A.get("/country_region/".concat(e),{});a&&a.data&&(r=a.data.map((e,r)=>({label:e.title,value:e._id}))).sort((e,r)=>e.label.localeCompare(r.label))}C(r)},D=e=>{let{name:r,value:s}=e.currentTarget;a(e=>({...e,[r]:s})),"country"===r&&(T(s),L({region:[]}))},F=async e=>{e.preventDefault();let a={username:r.username,email:r.email,role:r.role,institution:r.institution?r.institution:null,region:r.region?r.region.map((e,r)=>e.value):[],country:r.country?r.country:null,enabled:"true"===r.enabled};k&&k._id?(""!==r.password&&(a.password=r.password),await v.A.patch("/users/".concat(k._id),a)):(a.password=r.password,await v.A.post("/users",a)),p().push("/users")},U=e=>e===r.password;return(0,s.jsx)(i.A,{className:"formCard",fluid:!0,children:(0,s.jsx)(l.A,{children:(0,s.jsx)(c.yk,{onSubmit:F,ref:M,children:(0,s.jsxs)(l.A.Body,{children:[(0,s.jsx)(t.A,{children:(0,s.jsx)(o.A,{children:(0,s.jsx)(l.A.Title,{children:k&&k._id?"Edit User":"Create User"})})}),(0,s.jsx)("hr",{}),(0,s.jsx)(t.A,{children:(0,s.jsx)(o.A,{children:(0,s.jsxs)(d.A.Group,{children:[(0,s.jsx)(d.A.Label,{className:"required-field",children:"Username"}),(0,s.jsx)(c.ks,{name:"username",id:"username",required:!0,minLength:"3",value:r.username,errorMessage:"You don't have a Username?",onChange:D})]})})}),(0,s.jsx)(t.A,{children:(0,s.jsx)(o.A,{children:(0,s.jsxs)(d.A.Group,{children:[(0,s.jsx)(d.A.Label,{className:"required-field",children:"Email"}),(0,s.jsx)(c.ks,{name:"email",id:"email",type:"email",validator:x().isEmail,required:!0,errorMessage:{validator:"Please enter a valid email"},value:r.email,onChange:D})]})})}),(0,s.jsx)(t.A,{children:(0,s.jsx)(o.A,{children:(0,s.jsxs)(d.A.Group,{children:[(0,s.jsx)(d.A.Label,{children:"Role"}),(0,s.jsxs)(c.s3,{name:"role",value:r.role,errorMessage:"Please select a role",onChange:D,children:[(0,s.jsx)("option",{value:"",children:"Select Role"}),m.map((e,r)=>(0,s.jsx)("option",{value:e._id,children:e.title},r))]})]})})}),(0,s.jsx)(t.A,{children:(0,s.jsx)(o.A,{children:(0,s.jsxs)(d.A.Group,{children:[(0,s.jsx)(d.A.Label,{children:"Institution"}),(0,s.jsxs)(c.s3,{name:"institution",value:r.institution,errorMessage:"Please select a Institution.",onChange:D,children:[(0,s.jsx)("option",{value:"",children:"Select Institution"}),y.map((e,r)=>(0,s.jsx)("option",{value:e._id,children:e.title},r))]})]})})}),(0,s.jsx)(t.A,{children:(0,s.jsx)(o.A,{children:(0,s.jsxs)(d.A.Group,{children:[(0,s.jsx)(d.A.Label,{children:"Country"}),(0,s.jsxs)(c.s3,{name:"country",id:"country",value:r.country,onChange:D,children:[(0,s.jsx)("option",{value:"",children:"Select Country"}),_.map((e,r)=>(0,s.jsx)("option",{value:e._id,children:e.title},e._id))]})]})})}),(0,s.jsx)(t.A,{children:(0,s.jsx)(o.A,{children:(0,s.jsxs)(d.A.Group,{children:[(0,s.jsx)(d.A.Label,{children:"Region"}),(0,s.jsx)(j.KF,{overrideStrings:{selectSomeItems:"Select Country Regions",allItemsAreSelected:"All Regions are Selected"},options:f,value:r.region,onChange:e=>{a(r=>({...r,region:e}))},className:"region",labelledBy:"Select Country Regions"})]})})}),(0,s.jsx)(t.A,{children:(0,s.jsx)(o.A,{children:(0,s.jsxs)(d.A.Group,{children:[(0,s.jsx)(d.A.Label,{className:"required-field",children:"Password"}),k&&k._id?(0,s.jsx)(c.ks,{name:"password",id:"password",type:"password",pattern:"(?=.*[A-Z]).{8,}",errorMessage:{pattern:"Password should be at least 8 characters and contains at least one upper case letter"},value:r.password,onChange:D}):(0,s.jsx)(c.ks,{name:"password",id:"password",type:"password",required:!0,pattern:"(?=.*[A-Z]).{8,}",errorMessage:{required:"Password is required",pattern:"Password should be at least 8 characters and contains at least one upper case letter"},value:r.password,onChange:D})]})})}),(0,s.jsx)(t.A,{children:(0,s.jsx)(o.A,{children:(0,s.jsxs)(d.A.Group,{children:[(0,s.jsx)(d.A.Label,{className:"required-field",children:"Confirm Password"}),k&&k._id?(0,s.jsx)(c.ks,{name:"confirm_password",id:"confirm_password",type:"password",validator:U,errorMessage:{required:"Confirm password is required",validator:"Password does not match"},value:r.confirm_password,onChange:D}):(0,s.jsx)(c.ks,{name:"confirm_password",id:"confirm_password",type:"password",required:!0,validator:U,errorMessage:{required:"Confirm password is required",validator:"Password does not match"},value:r.confirm_password,onChange:D})]})})}),(0,s.jsx)(t.A,{children:(0,s.jsx)(o.A,{children:(0,s.jsxs)(d.A.Group,{children:[(0,s.jsx)("label",{children:"Enabled"}),(0,s.jsxs)(c.sx.RadioGroup,{name:"enabled",errorMessage:"It is required",valueSelected:r.enabled,onChange:D,children:[(0,s.jsx)(c.sx.RadioItem,{id:"yes",label:"Yes",value:"true"}),(0,s.jsx)(c.sx.RadioItem,{id:"no",label:"No",value:"false"})]})]})})}),(0,s.jsxs)(t.A,{className:"my-4",children:[(0,s.jsx)(o.A,{xs:!0,lg:"2",children:(0,s.jsx)(u.A,{type:"submit",variant:"primary",children:"Submit"})}),(0,s.jsx)(o.A,{children:(0,s.jsx)(u.A,{onClick:()=>{a(g),window.scrollTo(0,0)},variant:"info",children:"Reset"})})]})]})})})})}}}]);
//# sourceMappingURL=8186-c96fe524466d5293.js.map