(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[6914],{58308:(e,s,a)=>{(window.__NEXT_P=window.__NEXT_P||[]).push(["/vspace/permission",function(){return a(67663)}])},67663:(e,s,a)=>{"use strict";a.r(s),a.d(s,{canAddVspace:()=>n,canAddVspaceForm:()=>t,canEditVspace:()=>c,canEditVspaceForm:()=>o,canViewDiscussionUpdate:()=>u,default:()=>d});var i=a(37876);a(14232);var p=a(8178),r=a(59626);let n=(0,p.A)({authenticatedSelector:e=>!!e.permissions&&!!e.permissions.vspace&&!!e.permissions.vspace["create:any"],wrapperDisplayName:"CanAddVspace"}),t=(0,p.A)({authenticatedSelector:e=>!!e.permissions&&!!e.permissions.vspace&&!!e.permissions.vspace["create:any"],wrapperDisplayName:"CanAddVspaceForm",FailureComponent:()=>(0,i.jsx)(r.default,{})}),c=(0,p.A)({authenticatedSelector:(e,s)=>{if(e.permissions&&e.permissions.vspace){if(e.permissions.vspace["update:any"])return!0;else if(e.permissions.vspace["update:own"]&&s.vspace&&s.vspace.user&&s.vspace.user._id===e.user._id)return!0}return!1},wrapperDisplayName:"CanEditVspace"}),o=(0,p.A)({authenticatedSelector:(e,s)=>{if(e.permissions&&e.permissions.vspace){if(e.permissions.vspace["update:any"])return!0;else if(e.permissions.vspace["update:own"]&&s.vspace&&s.vspace.user&&s.vspace.user._id===e.user._id)return!0}return!1},wrapperDisplayName:"CanEditVspaceForm",FailureComponent:()=>(0,i.jsx)(r.default,{})}),u=(0,p.A)({authenticatedSelector:e=>!!e.permissions&&!!e.permissions.update&&!!e.permissions.update["read:any"],wrapperDisplayName:"CanViewDiscussionUpdate"}),d=n}},e=>{var s=s=>e(e.s=s);e.O(0,[636,6593,8792],()=>s(58308)),_N_E=e.O()}]);
//# sourceMappingURL=permission-10bd5a80d000a619.js.map