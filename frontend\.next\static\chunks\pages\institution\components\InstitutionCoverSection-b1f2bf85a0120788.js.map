{"version": 3, "file": "static/chunks/pages/institution/components/InstitutionCoverSection-b1f2bf85a0120788.js", "mappings": "8EACA,4CACA,kDACA,WACA,OAAe,EAAQ,KAAuE,CAC9F,EACA,SAFsB,2FCwEtB,MA5CgC,GAExB,yBA0COA,MAzCH,WAACC,MAAAA,CAAIC,CAyCsB,SAzCZ,oCACV,CAACC,EAAMC,YAAY,EAChBD,EAAME,eAAe,EACrBF,EAAME,eAAe,CAACC,MAAM,EAC5BH,EAAME,eAAe,CAACC,MAAM,CAACC,GAAG,CAChC,UAACC,MAAAA,CACGN,UAAU,0BACVO,IAAK,GAAwCN,MAAAA,CAArCO,8BAAsB,CAAC,gBAA+C,OAAjCP,EAAME,eAAe,CAACC,MAAM,CAACC,GAAG,IAGjF,GAEHJ,EAAMC,YAAY,CACf,UAACH,MAAAA,CAAIC,UAAU,mCACX,UAACD,MAAAA,CAAIC,UAAU,kCAGnB,GAEH,EAAOE,YAAY,GAAID,EAAME,eAAe,EAAKF,EAAD,eAAsB,CAACG,MAAM,CAM1E,GALA,UAACE,MAAAA,CACGN,UAAU,0BACVO,IAAI,uCAKZ,UAACR,MAAAA,CAAIC,UAAU,2CACX,UAACS,EAAAA,OAA8BA,CAAAA,CAC3BN,gBAAiBF,EAAME,eAAe,CACtCO,UAAWT,EAAMU,IAAI,CACrBA,KAAMV,EAAMU,IAAI,CAChBC,WAAYX,EAAMW,UAAU,CAC5BC,YAAaZ,EAAMY,WAAW", "sources": ["webpack://_N_E/?9f77", "webpack://_N_E/./pages/institution/components/InstitutionCoverSection.tsx"], "sourcesContent": ["\n    (window.__NEXT_P = window.__NEXT_P || []).push([\n      \"/institution/components/InstitutionCoverSection\",\n      function () {\n        return require(\"private-next-pages/institution/components/InstitutionCoverSection.tsx\");\n      }\n    ]);\n    if(module.hot) {\n      module.hot.dispose(function () {\n        window.__NEXT_P.push([\"/institution/components/InstitutionCoverSection\"])\n      });\n    }\n  ", "//Import Library\r\nimport React from \"react\";\r\n\r\n//Import services/components\r\nimport InstitutionCoverSectionContent from \"./InstitutionCoverSectionContent\";\r\n\r\ninterface InstitutionCoverSectionProps {\r\n  prop: any;\r\n  imageLoading: boolean;\r\n  institutionData: {\r\n    header?: {\r\n      _id: string;\r\n    };\r\n    title: string;\r\n    description: string;\r\n    website?: string;\r\n    telephone?: string;\r\n    dial_code?: string;\r\n    address?: {\r\n      line_1?: string;\r\n      line_2?: string;\r\n      city?: string;\r\n      postal_code?: string;\r\n      country?: {\r\n        title: string;\r\n      };\r\n    };\r\n  };\r\n  editAccess: boolean;\r\n  focalPoints: any[];\r\n}\r\n\r\nconst InstitutionCoverSection = (props: InstitutionCoverSectionProps) => {\r\n    return (\r\n        <>\r\n            <div className=\"institution-image-block\">\r\n                {!props.imageLoading &&\r\n                    props.institutionData &&\r\n                    props.institutionData.header &&\r\n                    props.institutionData.header._id ? (\r\n                    <img\r\n                        className=\"institution-image-cover\"\r\n                        src={`${process.env.API_SERVER}/image/show/${props.institutionData.header._id}`}\r\n                    />\r\n                ) : (\r\n                    \"\"\r\n                )}\r\n                {props.imageLoading ? (\r\n                    <div className=\"institution-imageLoader\">\r\n                        <div className=\"spinner-border text-primary\" />\r\n                    </div>\r\n                ) : (\r\n                    \"\"\r\n                )}\r\n                {!props.imageLoading && props.institutionData && !props.institutionData.header ? (\r\n                    <img\r\n                        className=\"institution-image-cover\"\r\n                        src=\"/images/rki_institute.7cb751d6.jpg\"\r\n                    />\r\n                ) : (\r\n                    \"\"\r\n                )}\r\n                <div className=\"institution-image-inner-content\">\r\n                    <InstitutionCoverSectionContent\r\n                        institutionData={props.institutionData}\r\n                        routeData={props.prop}\r\n                        prop={props.prop}\r\n                        editAccess={props.editAccess}\r\n                        focalPoints={props.focalPoints}\r\n                    />\r\n                </div>\r\n            </div>\r\n        </>\r\n    )\r\n}\r\n\r\nexport default InstitutionCoverSection;"], "names": ["InstitutionCoverSection", "div", "className", "props", "imageLoading", "institutionData", "header", "_id", "img", "src", "process", "InstitutionCoverSectionContent", "routeData", "prop", "editAccess", "focalPoints"], "sourceRoot": "", "ignoreList": []}