(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[9361],{12661:(e,i,s)=>{"use strict";s.r(i),s.d(i,{default:()=>o});var n=s(37876);s(14232);var t=s(32890),r=s(48477),a=s(31753);let o=e=>{let{t:i}=(0,a.Bd)("common");return(0,n.jsx)(t.A,{defaultActiveKey:"2",children:(0,n.jsxs)(t.A.Item,{eventKey:"2",children:[(0,n.jsx)(t.<PERSON><PERSON>,{children:(0,n.jsx)("div",{className:"cardTitle",children:i("Discussions")})}),(0,n.jsx)(t.A.Body,{children:(0,n.jsx)(r.A,{type:"hazard",id:e&&e.routes?e.routes[1]:null})})]})})}},33458:(e,i,s)=>{"use strict";s.d(i,{A:()=>c});var n=s(37876),t=s(14232),r=s(65418),a=s(21772),o=s(11041),d=s(60282),l=s(31753);s(57637);let c=e=>{let{t:i}=(0,l.Bd)("common"),[s,c]=(0,t.useState)([]),u=e=>{let s=/(http|https):\/\/(\w+:{0,1}\w*)?(\S+)(:[0-9]+)?(\/|\/([\w#!:.?+=&%!\-\/]))?/.test(e.description);return(0,n.jsxs)("div",{className:"carousel-legend",children:[(0,n.jsxs)("p",{className:"lead",children:[(0,n.jsx)("b",{children:i("Filename")})," ",e.originalName||"No Name found"]}),e.description&&(0,n.jsxs)("div",{className:"source_link",children:[(0,n.jsx)("p",{children:(0,n.jsx)("b",{children:i("Source")})}),s?(0,n.jsxs)("div",{children:[(0,n.jsx)(a.g,{icon:o.CQO,size:"1x",color:"#999",className:"me-1"}),(0,n.jsx)("a",{className:"source_link",href:e.description,target:"_blank",rel:"noopener noreferrer",children:e.description})]}):(0,n.jsx)("div",{children:(0,n.jsx)("p",{className:"ps-0 py-0",style:{wordBreak:"break-all"},children:e.description})})]}),e.downloadLink&&(0,n.jsxs)(d.A,{className:"btn btn-success mt-2 btn--download",href:e.downloadLink,children:[i("Download"),(0,n.jsx)(a.g,{icon:o.cbP,size:"1x",className:"ms-1"})]})]})};return(0,t.useEffect)(()=>{let i=[];e&&e.gallery&&Array.isArray(e.gallery)&&e.gallery.map((s,n)=>{let t,r=s&&s.name.split(".").pop();switch(r){case"JPG":case"jpg":case"jpeg":case"png":t="".concat("http://localhost:3001/api/v1","/image/show/").concat(s._id);break;case"pdf":t="/images/fileIcons/pdfFile.png";break;case"docx":t="/images/fileIcons/wordFile.png";break;case"xls":case"xlsx":t="/images/fileIcons/xlsFile.png";break;default:t="/images/fileIcons/otherFile.png"}let a=("docx"===r||"pdf"===r||"xls"===r||"xlsx"===r)&&"".concat("http://localhost:3001/api/v1","/files/download/").concat(s._id),o="".concat(s&&s.original_name?s.original_name:"No Name found"),d=e.imageSource&&Array.isArray(e.imageSource)&&e.imageSource.length>0?e.imageSource[n]:"";i.push({src:t,description:d,originalName:o,downloadLink:a})}),c(i)},[e]),(0,n.jsx)("div",{children:s&&0===s.length?(0,n.jsx)("div",{className:"border border-info my-3 mx-0",children:(0,n.jsx)("p",{className:"d-flex d-flex justify-content-center p-2 m-0",children:i("NoFilesFound!")})}):(0,n.jsx)(r.FN,{showThumbs:!0,showStatus:!0,showIndicators:!0,infiniteLoop:!0,useKeyboardArrows:!0,autoPlay:!1,stopOnHover:!0,swipeable:!0,dynamicHeight:!1,emulateTouch:!0,renderThumbs:()=>s.map((e,i)=>(0,n.jsx)("img",{src:e.src,alt:"Thumbnail ".concat(i+1),style:{width:"60px",height:"60px",objectFit:"cover"}},i)),children:s.map((e,i)=>(0,n.jsxs)("div",{children:[(0,n.jsx)("img",{src:e.src,alt:e.originalName||"Gallery image",style:{maxHeight:"500px",objectFit:"contain"}}),u(e)]},i))})})}},33859:(e,i,s)=>{"use strict";s.r(i),s.d(i,{canAddInstitution:()=>a,canAddInstitutionForm:()=>o,canEditInstitution:()=>d,canEditInstitutionForm:()=>l,canManageFocalPoints:()=>u,canViewDiscussionUpdate:()=>c,default:()=>p});var n=s(37876);s(14232);var t=s(8178),r=s(59626);let a=(0,t.A)({authenticatedSelector:e=>!!e.permissions&&!!e.permissions.institution&&!!e.permissions.institution["create:any"],wrapperDisplayName:"CanAddInstitution"}),o=(0,t.A)({authenticatedSelector:e=>!!e.permissions&&!!e.permissions.institution&&!!e.permissions.institution["create:any"],wrapperDisplayName:"CanAddInstitutionForm",FailureComponent:()=>(0,n.jsx)(r.default,{})}),d=(0,t.A)({authenticatedSelector:(e,i)=>{if(e.permissions&&e.permissions.institution){if(e.permissions.institution["update:any"])return!0;else if(e.permissions.institution["update:own"]&&i.institution&&i.institution.user&&i.institution.user===e.user._id)return!0}return!1},wrapperDisplayName:"CanEditInstitution"}),l=(0,t.A)({authenticatedSelector:(e,i)=>{if(e.permissions&&e.permissions.institution){if(e.permissions.institution["update:any"])return!0;else if(e.permissions.institution["update:own"]&&i.institution&&i.institution.user&&i.institution.user===e.user._id)return!0}return!1},wrapperDisplayName:"CanEditInstitutionForm",FailureComponent:()=>(0,n.jsx)(r.default,{})}),c=(0,t.A)({authenticatedSelector:e=>!!e.permissions&&!!e.permissions.update&&!!e.permissions.update["read:any"],wrapperDisplayName:"CanViewDiscussionUpdate"}),u=(0,t.A)({authenticatedSelector:(e,i)=>{if(e.permissions&&e.permissions.institution_focal_point){if(e.permissions.institution_focal_point["update:any"])return!0;else if(e.permissions.institution_focal_point["update:own"]&&i.institution&&i.institution.user&&i.institution.user===e.user._id)return!0}return!1},wrapperDisplayName:"canManageFocalPoints"}),p=a},34111:(e,i,s)=>{"use strict";s.r(i),s.d(i,{default:()=>o});var n=s(37876);s(14232);var t=s(32890),r=s(33458),a=s(31753);let o=e=>{let{t:i}=(0,a.Bd)("common");return(0,n.jsx)(t.A,{defaultActiveKey:"1",children:(0,n.jsxs)(t.A.Item,{eventKey:"1",children:[(0,n.jsx)(t.A.Header,{children:(0,n.jsx)("div",{className:"cardTitle",children:i("MediaGallery")})}),(0,n.jsx)(t.A.Body,{children:(0,n.jsx)(r.A,{gallery:e.images,imageSource:e.images_src})})]})})}},39896:(e,i,s)=>{"use strict";s.r(i),s.d(i,{default:()=>d});var n=s(37876);s(14232);var t=s(32890),r=s(48230),a=s.n(r),o=s(31753);let d=e=>{let{t:i}=(0,o.Bd)("common"),{institutionData:s,activeOperations:r,activeProjects:d}=e;return(0,n.jsx)(t.A,{defaultActiveKey:"0",children:(0,n.jsxs)(t.A.Item,{eventKey:"0",children:[(0,n.jsx)(t.A.Header,{children:(0,n.jsx)("div",{className:"cardTitle",children:i("MoreInfo")})}),(0,n.jsxs)(t.A.Body,{className:"institutionDetails ps-4",children:[(0,n.jsxs)("p",{children:[(0,n.jsx)("b",{children:i("OrganisationType")}),":",(0,n.jsxs)("span",{children:[" ",s.type?s.type.title:""]})]}),(0,n.jsxs)("p",{children:[(0,n.jsx)("b",{children:i("Network")}),":",(0,n.jsx)("span",{children:s.networks?s.networks.map((e,i)=>(0,n.jsx)("span",{children:(0,n.jsx)("li",{children:e.title})},i)):""})]}),(0,n.jsxs)("p",{children:[(0,n.jsx)("b",{children:i("ActiveOperation")}),":",(0,n.jsx)("span",{children:r&&r.length>0?r.map((e,i)=>(0,n.jsx)("li",{children:(0,n.jsx)(a(),{href:"/operation/[...routes]",as:"/operation/show/".concat(e._id),children:e.title})},i)):(0,n.jsx)("li",{children:i("NoActiveoperationsfound")})})]}),(0,n.jsxs)("p",{children:[(0,n.jsx)("b",{children:i("Expertise")}),":",(0,n.jsxs)("span",{children:[" ",s.expertise?s.expertise.map((e,i)=>(0,n.jsxs)("li",{children:[e.title," ",(0,n.jsx)("br",{})]},i)):""]})]}),(0,n.jsxs)("p",{children:[(0,n.jsx)("b",{children:i("ActiveProject")}),":",(0,n.jsx)("span",{children:d&&d.length>0?d.map((e,i)=>(0,n.jsx)("li",{children:(0,n.jsx)(a(),{href:"/project/[...routes]",as:"/project/show/".concat(e._id),children:e.title})},i)):(0,n.jsx)("li",{children:i("NoActiveprojectsfound")})})]}),(0,n.jsxs)("p",{children:[(0,n.jsx)("b",{children:i("Department")}),":",(0,n.jsx)("span",{children:s&&s.department?s.department:i("Nodepartmentfound")})]}),(0,n.jsxs)("p",{children:[(0,n.jsx)("b",{children:i("Unit")}),":",(0,n.jsx)("span",{children:s&&s.unit?s.unit:i("Nounitfound")})]})]})]})})}},64330:(e,i,s)=>{"use strict";s.r(i),s.d(i,{default:()=>l});var n=s(37876);s(14232);var t=s(32890),r=s(12661),a=s(33859),o=s(39896),d=s(34111);let l=e=>{let i=(0,a.canViewDiscussionUpdate)(()=>(0,n.jsx)(r.default,{...e.prop}));return(0,n.jsxs)(n.Fragment,{children:[(0,n.jsx)(t.A,{className:"countryAccordionNew",children:(0,n.jsx)(o.default,{...e})}),(0,n.jsx)(t.A,{className:"countryAccordionNew",children:(0,n.jsx)(d.default,{...e.institutionData})}),(0,n.jsx)(t.A,{className:"countryAccordionNew",children:(0,n.jsx)(i,{})})]})}},72014:(e,i,s)=>{(window.__NEXT_P=window.__NEXT_P||[]).push(["/institution/components/InstitutionAccordionSection",function(){return s(64330)}])},84135:function(e,i,s){(function(e){"use strict";function i(e,i,s,n){var t={m:["eine Minute","einer Minute"],h:["eine Stunde","einer Stunde"],d:["ein Tag","einem Tag"],dd:[e+" Tage",e+" Tagen"],w:["eine Woche","einer Woche"],M:["ein Monat","einem Monat"],MM:[e+" Monate",e+" Monaten"],y:["ein Jahr","einem Jahr"],yy:[e+" Jahre",e+" Jahren"]};return i?t[s][0]:t[s][1]}e.defineLocale("de",{months:"Januar_Februar_M\xe4rz_April_Mai_Juni_Juli_August_September_Oktober_November_Dezember".split("_"),monthsShort:"Jan._Feb._M\xe4rz_Apr._Mai_Juni_Juli_Aug._Sep._Okt._Nov._Dez.".split("_"),monthsParseExact:!0,weekdays:"Sonntag_Montag_Dienstag_Mittwoch_Donnerstag_Freitag_Samstag".split("_"),weekdaysShort:"So._Mo._Di._Mi._Do._Fr._Sa.".split("_"),weekdaysMin:"So_Mo_Di_Mi_Do_Fr_Sa".split("_"),weekdaysParseExact:!0,longDateFormat:{LT:"HH:mm",LTS:"HH:mm:ss",L:"DD.MM.YYYY",LL:"D. MMMM YYYY",LLL:"D. MMMM YYYY HH:mm",LLLL:"dddd, D. MMMM YYYY HH:mm"},calendar:{sameDay:"[heute um] LT [Uhr]",sameElse:"L",nextDay:"[morgen um] LT [Uhr]",nextWeek:"dddd [um] LT [Uhr]",lastDay:"[gestern um] LT [Uhr]",lastWeek:"[letzten] dddd [um] LT [Uhr]"},relativeTime:{future:"in %s",past:"vor %s",s:"ein paar Sekunden",ss:"%d Sekunden",m:i,mm:"%d Minuten",h:i,hh:"%d Stunden",d:i,dd:i,w:i,ww:"%d Wochen",M:i,MM:i,y:i,yy:i},dayOfMonthOrdinalParse:/\d{1,2}\./,ordinal:"%d.",week:{dow:1,doy:4}})})(s(10841))}},e=>{var i=i=>e(e.s=i);e.O(0,[7725,1772,7126,8477,636,6593,8792],()=>i(72014)),_N_E=e.O()}]);
//# sourceMappingURL=InstitutionAccordionSection-22d6a3b2cfa5703e.js.map