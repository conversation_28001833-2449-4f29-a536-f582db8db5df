{"version": 3, "file": "static/chunks/pages/event/components/MoreInformationAccordion-d2f1b6a3aeb6675b.js", "mappings": "yMAkCA,MAxBiC,IAC7B,GAAM,CAAEA,GAAC,CAAE,CAAGC,CAAAA,EAAAA,EAAAA,EAAAA,CAAcA,CAAC,MAuBlBC,IAtBL,CAACC,EAASC,EAAW,CAAGC,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,EAAC,GACvC,MACI,+BACI,WAACC,EAAAA,CAASA,CAACC,IAAI,EAACC,SAAS,cACrB,WAACF,EAAAA,CAASA,CAACG,MAAM,EAACC,QAAS,IAAMN,EAAW,CAACD,aACzC,UAACQ,MAAAA,CAAIC,UAAU,qBAAaZ,EAAE,iCAC9B,UAACW,MAAAA,CAAIC,UAAU,qBACVT,EACG,UAACU,EAAAA,CAAeA,CAAAA,CAACC,KAAMC,EAAAA,GAAOA,CAAEC,MAAM,SAEtC,UAACH,EAAAA,CAAeA,CAAAA,CAACC,KAAMG,EAAAA,GAAMA,CAAED,MAAM,cAIjD,UAACV,EAAAA,CAASA,CAACY,IAAI,WACX,UAACC,EAAAA,CAAiBA,CAAAA,CAACC,YAAaC,EAAMC,SAAS,SAKnE,mBC/BA,4CACA,6CACA,WACA,OAAe,EAAQ,IAAkE,CACzF,EACA,UAFsB,4ECoCtB,MA9B0B,IACxB,GAAM,CAAEtB,GAAC,CAAE,CAAGC,CAAAA,EAAAA,EAAAA,EAAAA,CAAcA,CAAC,MA6BhBkB,IA5BPI,EAAiBC,SAASC,EA4BFN,EAAC,CA5B6B,EACtD,CAACO,CADyD,CAAK,EACpC,CAAGrB,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,EAAC,GAO7C,MACE,iCAEIgB,EAAMD,WAAW,CACjB,UAACT,MAAAA,CACCgB,wBAAyBC,CAVZ,CAACC,EAAqBC,KAElC,CAAEC,OADe,CACPC,GAD8BH,EAAYI,MAAM,CAAGV,EAAkBM,EAAYK,SAAS,CAAC,EAAGX,GAAkB,MAAQF,EAAMD,WAAW,CACzH,CACnC,EAO8CC,EAAMD,WAAW,CAACM,GACxDd,UAAU,kBAEH,KAGTS,EAAMD,WAAW,EAAIC,EAAMD,WAAW,CAACa,MAAM,CAAGV,EAC9C,UAACY,SAAAA,CAAOC,KAAK,SAASxB,UAAU,eAAeF,QAAS,IAAM2B,EAAc,CAACX,YAChE1B,EAAb0B,EAAe,WAAgB,GAAF1B,WACjB,OAItB", "sources": ["webpack://_N_E/./pages/event/components/MoreInformationAccordion.tsx", "webpack://_N_E/?d46a", "webpack://_N_E/./components/common/readMore/readMore.tsx"], "sourcesContent": ["//Import Library\r\nimport React, { useEffect, useState } from \"react\";\r\nimport { Accordion, Card } from \"react-bootstrap\";\r\nimport { FontAwesomeIcon } from \"@fortawesome/react-fontawesome\";\r\nimport { faMinus, faPlus } from \"@fortawesome/free-solid-svg-icons\";\r\n\r\n//Import services/components\r\nimport { useTranslation } from 'next-i18next';\r\nimport ReadMoreContainer from \"../../../components/common/readMore/readMore\";\r\n\r\nconst MoreInformationAccordion = (props: any) => {\r\n    const { t } = useTranslation('common');\r\n    const [section, setSection] = useState(false);\r\n    return (\r\n        <>\r\n            <Accordion.Item eventKey=\"0\">\r\n                <Accordion.Header onClick={() => setSection(!section)}>\r\n                    <div className=\"cardTitle\">{t(\"Events.show.MoreInformation\")}</div>\r\n                    <div className=\"cardArrow\">\r\n                        {section ? (\r\n                            <FontAwesomeIcon icon={faMinus} color=\"#fff\" />\r\n                        ) : (\r\n                            <FontAwesomeIcon icon={faPlus} color=\"#fff\" />\r\n                        )}\r\n                    </div>\r\n                </Accordion.Header>\r\n                <Accordion.Body>\r\n                    <ReadMoreContainer description={props.more_info} />\r\n                </Accordion.Body>\r\n            </Accordion.Item>\r\n        </>\r\n    )\r\n};\r\n\r\nexport default MoreInformationAccordion;", "\n    (window.__NEXT_P = window.__NEXT_P || []).push([\n      \"/event/components/MoreInformationAccordion\",\n      function () {\n        return require(\"private-next-pages/event/components/MoreInformationAccordion.tsx\");\n      }\n    ]);\n    if(module.hot) {\n      module.hot.dispose(function () {\n        window.__NEXT_P.push([\"/event/components/MoreInformationAccordion\"])\n      });\n    }\n  ", "//Import Library\r\nimport { useState } from \"react\";\r\n\r\n//Import services/components\r\nimport { useTranslation } from 'next-i18next';\r\n\r\ninterface ReadMoreContainerProps {\r\n  description: string;\r\n}\r\n\r\nconst ReadMoreContainer = (props: ReadMoreContainerProps) => {\r\n  const { t } = useTranslation('common');\r\n  const readMoreLength = parseInt(process.env.READ_MORE_LENGTH || '200');\r\n  const [isReadMore, setIsReadMore] = useState(false);\r\n\r\n  const createMarkup = (htmlContent: string, isReadMoreInitial: boolean) => {\r\n    const truncateContent = (!isReadMoreInitial && htmlContent.length > readMoreLength) ? htmlContent.substring(0, readMoreLength) + \"...\" : props.description;\r\n    return { __html: truncateContent };\r\n  };\r\n\r\n  return (\r\n    <>\r\n      {\r\n        props.description  ?\r\n        <div\r\n          dangerouslySetInnerHTML={createMarkup(props.description,isReadMore)}\r\n          className=\"operationDesc\"\r\n        >\r\n        </div> : null\r\n      }\r\n      {\r\n        props.description && props.description.length > readMoreLength ?\r\n          <button type=\"button\" className=\"readMoreText\" onClick={() => setIsReadMore(!isReadMore)}>\r\n         {isReadMore ? t(\"readLess\") : t(\"readMore\")}\r\n          </button> : null\r\n      }\r\n    </>\r\n  )\r\n}\r\n\r\nexport default ReadMoreContainer;\r\n"], "names": ["t", "useTranslation", "MoreInformationAccordion", "section", "setSection", "useState", "Accordion", "<PERSON><PERSON>", "eventKey", "Header", "onClick", "div", "className", "FontAwesomeIcon", "icon", "faMinus", "color", "faPlus", "Body", "ReadMoreContainer", "description", "props", "more_info", "readMoreLength", "parseInt", "process", "isReadMore", "dangerouslySetInnerHTML", "createMarkup", "htmlContent", "isReadMoreInitial", "__html", "truncate<PERSON><PERSON><PERSON>", "length", "substring", "button", "type", "setIsReadMore"], "sourceRoot": "", "ignoreList": []}