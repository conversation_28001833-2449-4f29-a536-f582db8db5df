{"version": 3, "file": "static/chunks/2780-46454c403bb944e5.js", "mappings": "+RAGA,OAAMA,qBACJC,UAAAA,CAAa,eAAOC,CAAAA,CAAaC,CAAAA,MAAeC,EAAAA,UAAAA,MAAAA,CAAAA,GAAAA,KAAAA,IAAAA,SAAAA,CAAAA,EAAAA,CAAAA,SAAAA,CAAAA,EAAAA,CAA8B,CAAC,EAC7E,GAAI,CACF,IAAMC,EAAW,MAAMC,EAAAA,CAAUA,CAACC,GAAG,CAACL,EAAKE,GAC3C,OAAOD,EAAWE,EAASG,IAAI,CAAGH,CACpC,CAAE,MAAOI,EAAY,CACnB,OAAOA,EAAMJ,QAAQ,CAAGI,EAAMJ,QAAQ,CAAG,CAAC,CAC5C,CACF,EACF,CAEA,MAAe,IAAIL,mBAAmBA,EAAC,6BCufvC,MAlf8B,OAAC,aAAEU,CAAW,QAAEC,CAAM,CAAO,GAEjD,CAACC,EAAaC,EAAe,CAAGC,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAQ,CAgftBC,CAhfwB,CAgfvB,CA/e3B,CAACC,EAAcC,EAAgB,CAAGH,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAQ,EAAE,EACpD,CAACI,EAASC,EAAW,CAAGL,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAQ,EAAE,EAC1C,CAACM,EAAWC,EAAa,CAAGP,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,EAAU,GAC9C,CAACQ,EAAcC,EAAgB,CAAGT,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAMJ,GAChD,CAACc,EAAWC,EAAa,CAAGX,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAQ,EAAE,EAC9C,CAACY,EAAcC,EAAgB,CAAGb,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAQ,EAAE,EACpD,CAACc,EAASC,EAAW,CAAGf,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,EAAU,GAC1C,CAACgB,EAAWC,EAAa,CAAGjB,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAQ,EAAE,EAC9C,GAAEkB,CAAC,CAAE,CAAGC,CAAAA,EAAAA,EAAAA,EAAAA,CAAcA,CAAC,UAEvB,CAACC,EAAYC,EAAc,CAAGrB,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAM,CAC9CsB,SAAU,GACVC,MAAO,GACPC,IAAK,IACT,GAEMC,EAAc,CAChBC,MAAO,CAAC,EACRC,KAAM,CAAEC,MAAO,KAAM,EACrBC,MAAO,IACPC,OAAQ,wRACZ,EAEMC,EAAiB,CACnBL,MAAO,CAAC,EACRC,KAAM,CAAEL,SAAU,KAAM,EACxBO,MAAO,IACPC,OAAQ,6RACZ,EAYAE,CAAAA,EAAAA,EAAAA,SAAAA,CAASA,CAAC,KACNC,EAASF,EACb,EAAG,EAAE,EAEL,IAAMG,EAA0B,GAE5BC,EAAQA,GADAA,EAAMzC,IAAI,CAAG,IAAIyC,EAAMzC,IAAI,CAAC,CAAG,IACzB0C,MAAM,CAAC,IAIjB,GAAqC,GAAG,CAHZC,EAAKC,kBAAkB,CAACF,MAAM,CACtD,GAAiBG,EAAOC,aAAa,GAAK3C,CAAM,CAAC,EAAE,EAAsB,eAAX4C,MAAM,EAE9CC,MAAM,CAC5B,OAAOL,CAEf,GAIEJ,EAAW,MAAOU,IACpB,IAAMC,EAAY,MAAMpD,EAAAA,CAAUA,CAACC,GAAG,CAAC,SAAUkD,GACjDC,EAAUlD,IAAI,CAAGwC,EAAwBU,GACrCA,GAAaC,MAAMC,OAAO,CAACF,EAAUlD,IAAI,GAAG,EAC7BkD,EAAUlD,IAAI,CAACqD,GAAG,CAGpBC,CAHsBC,EAAWC,KACnC,CAAEC,MAAOF,EAAK3B,QAAQ,CAAE8B,MAAOH,EAAKzB,GAAG,CAAED,MAAO0B,EAAK1B,KAAK,CAAC,IAI1E,IAAMY,EAAQ,MAAM3C,EAAAA,CAAUA,CAACC,GAAG,CAAC,SAAUkD,GACzCR,GAASU,MAAMC,OAAO,CAACX,EAAMzC,IAAI,GAAG,EACrByC,EAAMzC,IAAI,CAACqD,GAAG,CAGhBC,CAHkBC,EAAWC,KAC/B,CAAEC,MAAOF,EAAK3B,QAAQ,CAAE8B,MAAOH,EAAKzB,GAAG,CAAED,MAAO0B,EAAK1B,KAAK,CAAC,GAI9E,EAEAS,CAAAA,EAAAA,EAAAA,SAAAA,CAASA,CAAC,KACFxB,EAAa6C,YAAY,EAAI7C,EAAa6C,YAAY,CAACX,MAAM,CAAG,GAAG,CAEnEY,EADoBC,IAAAA,GAAK,CAAC/C,EAAa6C,GACxBG,SADoC,CAAE,QAErDvB,EAASF,IAETvB,EAAaiD,mBAAmB,EAAE,GAG1C,EAAG,CAACjD,EAAa,EAEjB,IAAM8C,EAAiB,MAAOnB,IAC1BpB,GAAW,GAEX,IAAM2C,EAAa,MAAMlE,EAAAA,CAAUA,CAACC,GAAG,CAAC,SAAU,CAC9C,GAAGgC,CAAW,CACdC,MAAO,CAAEF,IAAKW,CAAM,CACxB,GACA,GAAIuB,EAAWhE,IAAI,EAAIgE,EAAWhE,IAAI,CAACgD,MAAM,CAAG,EAAG,CAC/C,IAAIiB,EAAiB,EAAE,OACvBD,GAAAA,EAAYhE,IAAI,CAACkE,OAAO,CAAEvB,KAA1BqB,KACIrB,GAAAA,EAAMC,YAAND,MAAwB,CAACuB,OAAO,CAAC,IAEzBC,GACAA,EAAkBrB,aAAa,GAAK3C,CAAM,CAAC,EAAE,EAChB,YAC/B,CADEgE,EAAkBpB,MAAM,EAExBkB,EAAUG,IAAI,CAAC,CACX,GAAGzB,CAAI,CACP,GAAG,CACCG,cAAeqB,EAAkBrB,aAAa,CAC9CuB,gBAAiBF,EAAkBE,eAAe,CAClDC,kBAAmBH,EAAkBpB,MACzC,CAAC,EAGb,EACJ,GACA1C,EAAe4D,GACf5C,GAAW,EACf,CACJ,EAgBMkD,EAA2B,CAC7B,CACIC,KAAMhD,EAAE,QACRiD,SAAU,oBACVC,KAAM,GAAY,UAACC,MAAAA,UAAKC,EAAEhD,QAAQ,EACtC,EACA,CACI4C,KAAMhD,EAAE,SACRiD,SAAU,qBACVC,KAAM,GAAY,UAACC,MAAAA,UAAKC,EAAE/C,KAAK,EACnC,EACA,CACI2C,KAAMhD,EAAE,gBACRiD,SAAU,2BACVC,KAAM,GAAY,UAACC,MAAAA,UAAKC,EAAE1E,WAAW,EAAI0E,EAAE1E,WAAW,CAACgC,KAAK,EAChE,EACA,CACIsC,KAAMhD,EAAE,aACRiD,SAAU,gBACVC,KAAOE,GAAW,UAACD,MAAAA,UAAK,GAAqCC,MAAAA,CAAlCA,EAAEC,SAAS,CAAGD,EAAEC,SAAS,CAAG,GAAG,KAA0C,OAAvCD,EAAEE,aAAa,CAAGF,EAAEE,aAAa,CAAG,KACrG,EACH,CACKC,EAAoB,CACtB,CACIP,KAAMhD,EAAE,QACRiD,SAAU,WACVC,KAAM,GAAY,UAACC,MAAAA,UAAKC,EAAEhD,QAAQ,GAClCoD,UAAU,CACd,EACA,CACIR,KAAMhD,EAAE,SACRiD,SAAU,QACVC,KAAM,GAAY,UAACC,MAAAA,UAAKC,EAAE/C,KAAK,GAC/BmD,UAAU,CACd,EACA,CACIR,KAAMhD,EAAE,gBACRiD,SAAU,2BACVC,KAAM,GAAY,UAACC,MAAAA,UAAKC,EAAEP,eAAe,GACzCW,UAAU,CACd,EACA,CACIR,KAAMhD,EAAE,aACRiD,SAAU,gBACVC,KAAM,GAAY,UAACC,MAAAA,UAAK,GAAqCC,MAAAA,CAAlCA,EAAEC,SAAS,CAAGD,EAAEC,SAAS,CAAG,GAAG,KAA0C,OAAvCD,EAAEE,aAAa,CAAGF,EAAEE,aAAa,CAAG,KACrG,EACH,CAEKG,EAAoBC,CAAAA,EAAAA,EAAAA,WAAAA,CAAWA,CAAC,IAClCzE,EAAgB0E,EAAM3E,YAAY,EAClCK,GAAa,EACjB,EAAG,EAAE,EAECuE,EAAuB,UACzB/D,GAAW,GACX,IAAMgE,EAAyBvE,EAAaiD,mBAAmB,CACzDuB,EAAoB,MAAMxF,EAAAA,CAAUA,CAACC,GAAG,CAAC,SAAU,CACrD,GAAGgC,CAAW,CACdC,MAAO,CAAEF,IAAKuD,CAAuB,CACzC,GACIC,EAAkBtF,IAAI,EAAIsF,EAAkBtF,IAAI,CAACgD,MAAM,CAAG,GAAG,CAC7DrC,EAAW2E,EAAkBtF,IAAI,EACjCqB,GAAW,GAEnB,EAEMkE,EAA2B,UAC7B,GAAI/E,EAAawC,MAAM,EAAI,GAAKxC,EAAawC,MAAM,CAAG,EAClDwC,CADqD,CACrDA,EAAKA,CAACvF,KAAK,CAACuB,EAAE,sDACX,CACH,IAAMiE,EAAoBjF,CAAY,CAAC,EAAE,CAACsB,GAAG,CACvCjC,EAAW,MAAMC,EAAAA,CAAUA,CAAC4F,KAAK,CAAC,gBAA0B,OAAVvF,CAAM,CAAC,EAAE,EAAI,CACjE4D,oBAAqB0B,EACrBvD,MAAOpB,EAAaoB,KAAK,GAE7B,GAAIrC,GAAYA,EAASiC,GAAG,CAAE,CAC1B,IAAM6D,EAAe9B,IAAAA,MAAQ,CAACzD,EAAa,CAAC,MAAOP,EAASkE,mBAAmB,CAAC,EAChFhD,EAAgB,CAAE,GAAGD,CAAY,CAAE,GAAG,CAAEiD,oBAAqBlE,EAASkE,mBAAmB,CAAE,GAC3FpD,EAAWgF,GACXH,EAAAA,EAAKA,CAACI,OAAO,CAACpE,EAAE,gDAChBX,GAAa,EACjB,CACJ,CACJ,EAEMgF,EAAyC,UACvCrF,EAAawC,MAAM,CAAG,EACtB8C,CADyB,EACzBA,EAAAA,EAAAA,CAAYA,CAAC,CACT5D,MAAOV,EAAE,8BACTuE,QAASvE,EAAE,qCACXwE,QAAS,CACL,CACIvC,MAAOjC,EAAE,OACTyE,QAAS,IAAMC,GAAkB1F,EACrC,EACA,CAAEiD,MAAOjC,EAAE,MAAOyE,QAAS,KAAM,CAAM,EAC1C,GAGLT,EAAAA,EAAKA,CAACvF,KAAK,CAACuB,EAAE,4CAEtB,EAEM2E,EAAiC,MAAOC,IAC1C,UAAIA,EAAAA,KAAAA,EAAAA,EAAsBpD,MAAAA,EAAQ,CAC1BoD,EAAqBC,IAAI,CAAC,GAAcC,EAAIxE,CADhDsE,EACmD,GAAKtF,EAAaiD,mBAAmB,EAAG,CACvF,IAAMlE,EAAW,MAAMC,EAAAA,CAAUA,CAAC4F,KAAK,CAAC,gBAA0B,OAAVvF,CAAM,CAAC,EAAE,EAAI,CACjE4D,oBAAqB,GACrB7B,MAAOpB,EAAaoB,KAAK,GAEzBrC,GAAYA,EAASiC,GAAG,EAAE,CAC1BnB,EAAW,EAAE,EACbE,GAAa,GAErB,CAER,EAEMqF,GAAoB,MAAOK,IAC7BJ,EAA+BI,GAC/B,IAAIC,EAAqB,EAAE,CACrBC,EAAc5C,IAAAA,GAAK,CAAC0C,EAAqB,OAC/C,GAA+D,EAA3D1C,IAAAA,OAAS,CAAC4C,EAAa3F,EAAaiD,mBAAmB,EAAO,CAC9D,IAAI2C,EAAuB7C,IAAAA,UAAY,CAACzD,EAAamG,GACrDC,EAAqBE,EACrBA,EAAuB7C,IAAAA,GAAK,CAAC6C,EAAsB,GACxC,EAAE5E,IAAKyB,EAAKzB,GAAG,CAAC,GAE3B,IAAMlC,EAAS,CACXsC,MAAOpB,EAAaoB,KAAK,CACzByB,aAAc4C,CAClB,EACAA,EAAoBrC,OAAO,CAAC,MAAOyC,IAC/BA,EAAG/D,kBAAkB,CAAG+D,EAAG/D,kBAAkB,CAACF,MAAM,CAAEG,GAAgBA,EAAOC,aAAa,EAAI3C,CAAM,CAAC,EAAE,EAC3F,MAAML,EAAAA,CAAUA,CAAC4F,KAAK,CAAC,UAAiB,OAAPiB,EAAG7E,GAAG,EAAI6E,EAC3D,GACA,IAAMC,EAAkB,MAAM9G,EAAAA,CAAUA,CAAC4F,KAAK,CAAC,gBAA0B,OAAVvF,CAAM,CAAC,EAAE,CAAC,sBAAqBP,GAC1FgH,GAAmBA,EAAgB9E,GAAG,EAAE,CACxC0D,EAAAA,EAAKA,CAACI,OAAO,CAACpE,EAAE,wDAChBnB,EAAemG,GACf3F,GAAa,GACb0B,EAASF,GAEjB,MACImD,CADG,CACHA,EAAKA,CAACvF,KAAK,CAACuB,EAAE,2CAEtB,EAEMqF,GAAoB,UACtB,GAAwB,IAApBnF,EAAWG,KAAK,CAChB2D,EAAAA,EAAKA,CAACvF,KAAK,CAACuB,EAAE,uBACX,CACH,IAAMgD,EAAO9C,EAAWG,KAAK,CAACiF,KAAK,CAAC,YAAY,CAAC,EAAE,CAEnD,GAAIC,KAAWC,IADA1F,EAAU+E,IAAI,CAAC,GAAOY,EAAEpF,KAAK,EAAIH,EAAWG,KAAK,EACtC,YACtB2D,EAAAA,EAAKA,CAACvF,KAAK,CAACuB,EAAE,oCAGlB0F,OAAOC,MAAM,CAACzF,EAAY,CAAEE,SAAU4C,CAAK,GAClB,EAAE,CACVJ,IAAI,CAAC1C,GACtB,IAAM0F,EAAiBhH,EAAYiD,GAAG,CAAC,CAACE,EAAMC,KACnC,CAAE1B,IAAKyB,EAAKzB,GAAG,CAAC,GAErBuF,EAAM,CACRnF,MAAOpB,EAAaoB,KAAK,CACzByB,aAAc,CAACjC,KAAe0F,EAAe,EAE3CR,EAAkB,MAAM9G,EAAAA,CAAUA,CAAC4F,KAAK,CAAC,gBAA0B,OAAVvF,CAAM,CAAC,EAAE,EAAIkH,GAC5E,GAAIT,GAAmBA,EAAgB9E,GAAG,CAAE,CACxC0D,EAAAA,EAAKA,CAACI,OAAO,CAACpE,EAAE,qCAChB,IAAM8F,EAAmB,MAAMC,EAAc9H,UAAU,CAAX8H,gBAAsC,OAAVpH,CAAM,CAAC,EAAE,GAAI,EAAO,CAAC,EACzFmH,IAAoBA,EAAiBxF,GAAG,EAAE,EAC1BwF,GAEpBE,EAAAA,CAAiBA,CAACC,sBAAsB,CACpC/F,EAAWG,KAAK,CAChBH,EAAWE,QAAQ,CACnBgF,EAAgB9E,GAAG,CACnB8E,EAAgB1E,KAAK,EAEzBK,EAASF,EACb,MACImD,CADG,CACHA,EAAKA,CAACvF,KAAK,CAAC2G,GAEhBjF,EAAc,CAAEE,MAAO,EAAG,EAC9B,CACJ,EAEM6F,GAAqB,UACvB,IAAMC,EAAUzG,EAAamC,GAAG,CAAC,CAACE,EAAMC,KAC7B,CAAE1B,IAAKyB,EAAKG,KAAK,CAAC,GAE7B,GAAuB,GAAG,CAAtBiE,EAAQ3E,MAAM,CACdwC,EAAAA,EAAKA,CAACvF,KAAK,CAACuB,EAAE,6BACX,CACH,IAAM6F,EAAM,CACRnF,MAAOpB,EAAaoB,KAAK,CACzByB,aAAc,IAAIvD,KAAgBuH,EAAQ,EAExCf,EAAkB,MAAM9G,EAAAA,CAAUA,CAAC4F,KAAK,CAAC,gBAA0B,OAAVvF,CAAM,CAAC,EAAE,EAAIkH,GAC5E,GAAIT,GAAmBA,EAAgB9E,GAAG,CAAE,CACxCZ,EAAagD,OAAO,CAAC,MAAOvB,QAIpBiF,EAHJ,IAAMC,EAASlF,EAAKe,KAAK,CACrBkE,EAAW,MAAM9H,EAAAA,CAAUA,CAACC,GAAG,CAAC,UAAiB,OAAP8H,IAC1CC,EAAe,EAAE,QACjBF,GAAAA,OAAAA,EAAAA,EAAUhF,OAAVgF,WAAUhF,EAAVgF,KAAAA,EAAAA,EAA8B5E,GAA9B4E,GAAoC,EAAE,GACvB,IAAIA,EAAShF,kBAAkB,CAAC,EAClBF,MAAM,CAAC,GAAYG,EAAOC,aAAa,GAAK8D,EAAgB9E,GAAG,EAC9EkB,MAAM,EAAI,EACpB8E,CADuB,CACV1D,IAAI,CAAC,CACdC,gBAAiBuC,EAAgB1E,KAAK,CACtCY,cAAe8D,EAAgB9E,GAAG,CAClCiB,OAAQ,iBACZ,GAEA+E,EAAeA,EAAazE,GAAG,CAAC,IACxBR,EAAOC,aAAa,GAAK8D,EAAgB9E,GAAG,EAAsB,YAAY,CAA9Be,EAAOE,MAAM,GAC7DF,EAAOE,MAAM,CAAG,mBAEbF,IAIfiF,EAAe,CACX,CACIzD,gBAAiBuC,EAAgB1E,KAAK,CACtCY,cAAe8D,EAAgB9E,GAAG,CAClCiB,OAAQ,iBACZ,EACH,CAEL6E,EAAShF,kBAAkB,CAAGkF,EAClB,MAAMhI,EAAAA,CAAUA,CAAC4F,KAAK,CAAC,UAAiB,OAAPmC,GAAUD,GACvDrF,EAASF,EACb,GACAmD,EAAAA,EAAKA,CAACI,OAAO,CAACpE,EAAE,qCAChB,IAAMuG,EAAmB,MAAMR,EAAc9H,UAAU,CAAX8H,gBAAsC,OAAVpH,CAAM,CAAC,EAAE,GAAI,EAAO,CAAC,GACzF4H,GAAoBA,EAAiBjG,GAAG,EAAE,EAC1BiG,EAExB,CACA5G,EAAgB,EAAE,CACtB,CACJ,EAEM6G,GAAsB,IAEpB,+BACKlH,GAAgBA,EAAagB,GAAG,CAC/B,UAACmG,IAAIA,CACDC,KAAK,KADJD,sBAEDE,GAAI,qBAA+B,OAAVhI,CAAM,CAAC,EAAE,WAEpC,WAACiI,EAAAA,CAAMA,CAAAA,CAACC,QAAQ,YAAYC,KAAK,eAC/B,UAACC,IAAAA,CAAEC,UAAU,eAAe,OACrBhH,EAAE,aAGX,OAKViH,GAAqBC,CAAAA,EAAAA,EAAAA,kBAAAA,CAAkBA,CAAC,IAAM,UAACV,GAAAA,CAAAA,IAErD,MACI,WAACW,EAAAA,CAASA,CAAAA,WACN,UAACC,EAAAA,CAAGA,CAAAA,CAACJ,UAAU,gBACX,UAACK,EAAAA,CAAGA,CAAAA,UACA,WAACC,KAAAA,CAAGN,UAAU,6BACV,UAACP,IAAIA,CAACC,KAAK,KAAND,sBAAiCE,GAAI,qBAA+B,OAAVhI,CAAM,CAAC,EAAE,WACnEW,EAAaoB,KAAK,GAEvB,UAACuG,GAAAA,CAAAA,UAIb,UAACG,EAAAA,CAAGA,CAAAA,UACA,UAACC,EAAAA,CAAGA,CAAAA,CAACL,UAAU,kBACX,UAACO,IAAAA,UAAGvH,EAAE,2BAGd,UAACoH,EAAAA,CAAGA,CAAAA,CAACJ,UAAU,0CACX,UAACQ,EAAAA,CAAQA,CAAAA,CACLC,QAAS1E,EACTvE,KAAMU,EACNU,QAASA,EACT8H,WAAW,EACXC,YAAY,EACZC,WAAW,EACXC,kBAAmBzI,MAG3B,UAACgI,EAAAA,CAAGA,CAAAA,UACA,UAACC,EAAAA,CAAGA,CAAAA,CAACL,UAAU,kBACX,UAACO,IAAAA,UAAGvH,EAAE,qBAGd,UAACoH,EAAAA,CAAGA,CAAAA,CAACJ,UAAU,gBACX,UAACQ,EAAAA,CAAQA,CAAAA,CACLC,QAASlE,EACT/E,KAAMI,EACNgB,QAASA,EACTgI,WAAW,EACXE,cAAc,IACdJ,WAAW,EACXK,qBAAsBtE,EACtBoE,kBAAmBzI,EACnB4I,gBAAgB,IAChBC,aA5ZG,CAACC,EAAaC,EAAeC,IAOrC/F,IAAAA,OAAS,CAAC6F,EANG,GAChB,CAKmBG,CALXF,EAAM,CACHG,CADK,CACDH,EAAM,CAACI,WAAW,GAE1BD,CAAG,CAACH,EAAM,CAEeC,OAwZhC,UAAChB,EAAAA,CAAGA,CAAAA,UACA,WAACC,EAAAA,CAAGA,CAAAA,WACA,UAACT,EAAAA,CAAMA,CAAAA,CAACI,UAAU,OAAOvC,QAASV,EAA0B8C,QAAQ,UAAUC,KAAK,cAC9E9G,EAAE,aAEP,UAAC4G,EAAAA,CAAMA,CAAAA,CAACnC,QAASJ,EAAwCwC,QAAQ,UAAUC,KAAK,cAC3E9G,EAAE,sBAIf,UAACwI,KAAAA,CAAAA,GACD,WAACpB,EAAAA,CAAGA,CAAAA,WACA,WAACC,EAAAA,CAAGA,CAAAA,CAACoB,GAAI,YACL,WAACC,EAAAA,CAAIA,CAACC,KAAK,EAACC,UAAU,2BAClB,UAACF,EAAAA,CAAIA,CAACG,KAAK,WAAE7I,EAAE,cACf,UAAC0I,EAAAA,CAAIA,CAACI,OAAO,EACT9F,KAAK,QACL+F,KAAK,QACLC,YAAahJ,EAAE,cACfkC,MAAOhC,EAAWG,KAAK,CACvB4I,SA3VH,CA2VaC,GA1V9B,GAAIC,EAAEC,MAAM,CAAE,CACV,GAAM,MAAEpG,CAAI,OAAEd,CAAK,CAAE,CAAGiH,EAAEC,MAAM,CAChCjJ,EAAc,GAAqB,EAC/B,GAAGkJ,CAAS,CACZ,CAACrG,CAF8B,CAEzB,CAAEd,EACZ,EACJ,CACJ,OAsVgB,UAAC0E,EAAAA,CAAMA,CAAAA,CAACnC,QAASY,GAAmBwB,QAAQ,UAAUkC,KAAK,kBACtD/I,EAAE,yBAGX,WAACqH,EAAAA,CAAGA,CAAAA,CAACoB,GAAI,YACL,WAACC,EAAAA,CAAIA,CAACC,KAAK,EAACC,UAAU,2BAClB,UAACF,EAAAA,CAAIA,CAACG,KAAK,WAAE7I,EAAE,cACf,UAACsJ,EAAAA,EAAWA,CAAAA,CACRC,gBAAiB,CACbC,gBAAiBxJ,EAAE,cACvB,EACAyJ,QAASjK,EACT0C,MAAOxC,EACPuJ,SAjWJ,CAiWcS,GAhW9B/J,EAAgBwJ,EACpB,EAgWwBnC,UAAW,cACX2C,WAAY,oBAGpB,UAAC/C,EAAAA,CAAMA,CAAAA,CAACnC,QAASyB,GAAoBW,QAAQ,UAAUkC,KAAK,kBACvD/I,EAAE,qBAM3B,gGC9dA,SAASwH,EAASoC,CAAoB,EACpC,GAAM,CAAE5J,CAAC,CAAE,CAAGC,CAAAA,EAAAA,EAAAA,EAAAA,CAAcA,CAAC,UACvB4J,EAA6B,CACjCC,gBAAiB9J,EAAE,cACnB,EACI,CACJyH,SAAO,MACPjJ,CAAI,WACJuL,CAAS,uBACTC,CAAqB,WACrBtC,CAAS,CACTuC,oBAAkB,qBAClBC,CAAmB,kBACnBC,CAAgB,aAChBC,CAAW,oBACXC,CAAkB,gBAClBvC,CAAc,SACdlI,CAAO,WACPgI,CAAS,sBACTG,CAAoB,CACpBF,mBAAiB,YACjByC,CAAU,QACVC,CAAM,kBACNvC,CAAgB,cAChBC,CAAY,CAEZ,CADA,EACGuC,EACJ,CAAGZ,EAGEa,EAAiB,4BACrBZ,EACAa,gBAAiB1K,EAAE,IAP0C,MAQ7D2K,UAAU,EACVlD,UACAjJ,KAAMA,GAAQ,EAAE,CAChBoM,OAAO,EACPC,2BAA4Bb,EAC5Bc,UAAWpD,EACXqD,gBAAiBnL,qBACjBqK,EACAtC,YAAY,EACZqD,iBAAkBpD,EAClBqD,kBAAmBZ,GAA0C,GAC7Da,eADwCb,cACVD,GAA4B,CAAC,GAAI,GAAI,GAAI,CAA3BA,EAA+B,GAAG,CAC9Ee,oBAAqBpB,EACrBqB,oBAAqBlB,EACrBmB,aAAclB,iBACdrC,uBACAC,oBACAF,EACAyD,kBAAmB,UAACC,EAAAA,CAAYA,CAAAA,CAAAA,GAChCC,SAAU,UAACzE,IAAAA,CAAEC,UAAU,6CACvBsD,SACAC,eACAtC,mBACAD,EACAhB,UAAW,WACb,EACA,MACE,UAACyE,EAAAA,EAASA,CAAAA,CAAE,GAAGhB,CAAc,EAEjC,CAEAjD,EAASkE,YAAY,CAAG,CACtBZ,WAAW,EACXnD,WAAY,GACZoC,UAAW,KACXnC,WAAW,EACXG,qBAAsB,KACtBF,mBAAmB,EACnByC,YAAY,EACZtC,kBAAkB,CACpB,EAEA,MAAeR", "sources": ["webpack://_N_E/./services/getApiService.tsx", "webpack://_N_E/./pages/institution/InstitutionFocalPoint.tsx", "webpack://_N_E/./components/common/RKITable.tsx"], "sourcesContent": ["//Import services/components\r\nimport apiService from \"./apiService\";\r\n\r\nclass GetApiDataService {\r\n  getApiData = async (url: string, withData: any, params: Record<string, any> = {}) => {\r\n    try {\r\n      const response = await apiService.get(url, params);\r\n      return withData ? response.data : response;\r\n    } catch (error: any) {\r\n      return error.response ? error.response : {};\r\n    }\r\n  };\r\n}\r\n\r\nexport default new GetApiDataService();", "//Import Library\r\nimport React, { useState, useEffect, useCallback } from \"react\";\r\nimport { <PERSON><PERSON>, Col, Container, Form, Row } from \"react-bootstrap\";\r\nimport _ from \"lodash\";\r\nimport { confirmAlert } from \"react-confirm-alert\";\r\nimport { MultiSelect } from \"react-multi-select-component\";\r\n\r\nimport toast from 'react-hot-toast';\r\nimport Link from \"next/link\";\r\n\r\n//Import services/components\r\nimport getApiService from \"../../services/getApiService\";\r\nimport RKITable from \"../../components/common/RKITable\";\r\nimport apiService from \"../../services/apiService\";\r\nimport { useTranslation } from 'next-i18next';\r\nimport invitationService from \"../../services/invitation.service\";\r\n\r\nimport { canEditInstitution } from \"./permission\";\r\n\r\nconst InstitutionFocalPoint = ({ institution, routes }: any) => {\r\n\r\n    const [focalpoints, setFocalpoints] = useState<any[]>([]);\r\n    const [selectedRows, setSelectedRows] = useState<any[]>([]);\r\n    const [primary, setPrimary] = useState<any[]>([]);\r\n    const [clearRows, setClearRows] = useState<boolean>(false);\r\n    const [initialState, setInitialState] = useState<any>(institution);\r\n    const [usersList, setUsersList] = useState<any[]>([]);\r\n    const [existingUser, setExistingUser] = useState<any[]>([]);\r\n    const [loading, setLoading] = useState<boolean>(false);\r\n    const [checkUser, setcheckUser] = useState<any[]>([]);\r\n    const { t } = useTranslation('common');\r\n\r\n    const [userInvite, setUserInvite] = useState<any>({\r\n        username: \"\",\r\n        email: \"\",\r\n        _id: null,\r\n    });\r\n\r\n    const usersParams = {\r\n        query: {},\r\n        sort: { title: \"asc\" },\r\n        limit: \"~\",\r\n        select: \"-firstname -lastname -password -role -country -region -status -is_focal_point -image   -enabled -created_at -updated_at -dataConsentPolicy -restrictedUsePolicy -acceptCookiesPolicy -withdrawConsentPolicy -medicalConsentPolicy -fullDataProtectionConsentPolicy -emailActivateToken\",\r\n    };\r\n\r\n    const userListParams = {\r\n        query: {},\r\n        sort: { username: \"asc\" },\r\n        limit: \"~\",\r\n        select: \"-firstname -lastname -password -role -country -region  -is_focal_point -image  -institution -enabled -created_at -updated_at -dataConsentPolicy -restrictedUsePolicy -acceptCookiesPolicy -withdrawConsentPolicy -medicalConsentPolicy -fullDataProtectionConsentPolicy -emailActivateToken\",\r\n    };\r\n\r\n    const customSort = (rows: any[], field: string, direction: 'asc' | 'desc') => {\r\n        const handleField = (row: any) => {\r\n            if (row[field]) {\r\n                return row[field].toLowerCase();\r\n            }\r\n            return row[field];\r\n        };\r\n        return _.orderBy(rows, handleField, direction);\r\n    };\r\n\r\n    useEffect(() => {\r\n        getUsers(userListParams);\r\n    }, []);\r\n\r\n    const getExcludedInvitedUsers = (users: any) => {\r\n        users = users.data ? [...users.data] : [];\r\n        users = users.filter((user: any) => {\r\n            let foundInstituteInvites = user.institutionInvites.filter(\r\n                (invite: any) => invite.institutionId === routes[1] && invite.status !== \"Rejected\"\r\n            );\r\n            if (foundInstituteInvites.length === 0) {\r\n                return user;\r\n            }\r\n        });\r\n        return users;\r\n    };\r\n\r\n    const getUsers = async (userListParamsinit: any) => {\r\n        const usersinit = await apiService.get(\"/users\", userListParamsinit);\r\n        usersinit.data = getExcludedInvitedUsers(usersinit);\r\n        if (usersinit && Array.isArray(usersinit.data)) {\r\n            const _users = usersinit.data.map((item: any, _i: any) => {\r\n                return { label: item.username, value: item._id, email: item.email };\r\n            });\r\n            setUsersList(_users);\r\n        }\r\n        const users = await apiService.get(\"/users\", userListParamsinit);\r\n        if (users && Array.isArray(users.data)) {\r\n            const _users = users.data.map((item: any, _i: any) => {\r\n                return { label: item.username, value: item._id, email: item.email };\r\n            });\r\n            setcheckUser(_users);\r\n        }\r\n    };\r\n\r\n    useEffect(() => {\r\n        if (initialState.focal_points && initialState.focal_points.length > 0) {\r\n            const focalPoints = _.map(initialState.focal_points, \"_id\");\r\n            fetchUsersByID(focalPoints);\r\n            getUsers(userListParams);\r\n        }\r\n        if (initialState.primary_focal_point) {\r\n            getPrimaryFocalPoint();\r\n        }\r\n    }, [initialState]);\r\n\r\n    const fetchUsersByID = async (users: any) => {\r\n        setLoading(true);\r\n\r\n        const usersQuery = await apiService.get(\"/users\", {\r\n            ...usersParams,\r\n            query: { _id: users },\r\n        });\r\n        if (usersQuery.data && usersQuery.data.length > 0) {\r\n            let tableData: any = [];\r\n            usersQuery?.data.forEach((user: any) => {\r\n                user?.institutionInvites.forEach((institutionInvite: any) => {\r\n                    if (\r\n                        institutionInvite &&\r\n                        institutionInvite.institutionId === routes[1] &&\r\n                        institutionInvite.status === \"Approved\"\r\n                    ) {\r\n                        tableData.push({\r\n                            ...user,\r\n                            ...{\r\n                                institutionId: institutionInvite.institutionId,\r\n                                institutionName: institutionInvite.institutionName,\r\n                                institutionStatus: institutionInvite.status,\r\n                            },\r\n                        });\r\n                    }\r\n                });\r\n            });\r\n            setFocalpoints(tableData);\r\n            setLoading(false);\r\n        }\r\n    };\r\n\r\n    const handleChange = (e: any) => {\r\n        if (e.target) {\r\n            const { name, value } = e.target;\r\n            setUserInvite((prevState: any) => ({\r\n                ...prevState,\r\n                [name]: value,\r\n            }));\r\n        }\r\n    };\r\n\r\n    const handleUsers = (e: any) => {\r\n        setExistingUser(e);\r\n    };\r\n\r\n    const primaryFocalPointColumns = [\r\n        {\r\n            name: t(\"Name\"),\r\n            selector: \"focal_points_name\",\r\n            cell: (d: any) => <div>{d.username}</div>,\r\n        },\r\n        {\r\n            name: t(\"Email\"),\r\n            selector: \"focal_points_email\",\r\n            cell: (d: any) => <div>{d.email}</div>,\r\n        },\r\n        {\r\n            name: t(\"Organisation\"),\r\n            selector: \"focal_points_institution\",\r\n            cell: (d: any) => <div>{d.institution && d.institution.title}</div>,\r\n        },\r\n        {\r\n            name: t(\"Telephone\"),\r\n            selector: \"mobile_number\",\r\n            cell: (d: any) => <div>{`${d.dial_code ? d.dial_code : \"\"} ${d.mobile_number ? d.mobile_number : \"\"}`}</div>,\r\n        },\r\n    ];\r\n    const focalPointColumns = [\r\n        {\r\n            name: t(\"Name\"),\r\n            selector: \"username\",\r\n            cell: (d: any) => <div>{d.username}</div>,\r\n            sortable: true,\r\n        },\r\n        {\r\n            name: t(\"Email\"),\r\n            selector: \"email\",\r\n            cell: (d: any) => <div>{d.email}</div>,\r\n            sortable: true,\r\n        },\r\n        {\r\n            name: t(\"Organisation\"),\r\n            selector: \"focal_points_institution\",\r\n            cell: (d: any) => <div>{d.institutionName}</div>,\r\n            sortable: true,\r\n        },\r\n        {\r\n            name: t(\"Telephone\"),\r\n            selector: \"mobile_number\",\r\n            cell: (d: any) => <div>{`${d.dial_code ? d.dial_code : \"\"} ${d.mobile_number ? d.mobile_number : \"\"}`}</div>,\r\n        },\r\n    ];\r\n\r\n    const handleRowSelected = useCallback((state: any) => {\r\n        setSelectedRows(state.selectedRows);\r\n        setClearRows(false);\r\n    }, []);\r\n\r\n    const getPrimaryFocalPoint = async () => {\r\n        setLoading(true);\r\n        const primary_focal_point_id = initialState.primary_focal_point;\r\n        const primaryFocalpoint = await apiService.get(\"/users\", {\r\n            ...usersParams,\r\n            query: { _id: primary_focal_point_id },\r\n        });\r\n        if (primaryFocalpoint.data && primaryFocalpoint.data.length > 0) {\r\n            setPrimary(primaryFocalpoint.data);\r\n            setLoading(false);\r\n        }\r\n    };\r\n\r\n    const handlePrimaryFocalPoints = async () => {\r\n        if (selectedRows.length <= 0 || selectedRows.length > 1) {\r\n            toast.error(t(\"toast.YoucannotaddmultipleFocalPointasPrimary\"));\r\n        } else {\r\n            const primaryFocalPoint = selectedRows[0]._id;\r\n            const response = await apiService.patch(`/institution/${routes[1]}`, {\r\n                primary_focal_point: primaryFocalPoint,\r\n                title: initialState.title,\r\n            });\r\n            if (response && response._id) {\r\n                const primaryvalue = _.filter(focalpoints, [\"_id\", response.primary_focal_point]);\r\n                setInitialState({ ...initialState, ...{ primary_focal_point: response.primary_focal_point } });\r\n                setPrimary(primaryvalue);\r\n                toast.success(t(\"toast.Primaryfocalpointschangedsuccessfully\"));\r\n                setClearRows(true);\r\n            }\r\n        }\r\n    };\r\n\r\n    const handleDeleteFocalPointFromOrganisation = async () => {\r\n        if (selectedRows.length > 0) {\r\n            confirmAlert({\r\n                title: t(\"toast.deleteFocalPtFromOrg\"),\r\n                message: t(\"toast.deleteFocalPtFromOrgConfirm\"),\r\n                buttons: [\r\n                    {\r\n                        label: t(\"yes\"),\r\n                        onClick: () => deleteFocalpoints(selectedRows),\r\n                    },\r\n                    { label: t(\"No\"), onClick: () => false },\r\n                ],\r\n            });\r\n        } else {\r\n            toast.error(t(\"toast.Pleaseselectanyfocalpointstodelete\"));\r\n        }\r\n    };\r\n\r\n    const checkAndEmptyPrimaryFocalPoint = async (_selectedFocalpoints: any) => {\r\n        if (_selectedFocalpoints?.length) {\r\n            if (_selectedFocalpoints.find((_fp: any) => _fp._id === initialState.primary_focal_point)) {\r\n                const response = await apiService.patch(`/institution/${routes[1]}`, {\r\n                    primary_focal_point: \"\",\r\n                    title: initialState.title,\r\n                });\r\n                if (response && response._id) {\r\n                    setPrimary([]);\r\n                    setClearRows(true);\r\n                }\r\n            }\r\n        }\r\n    };\r\n\r\n    const deleteFocalpoints = async (selectedFocalpoints: any[]) => {\r\n        checkAndEmptyPrimaryFocalPoint(selectedFocalpoints);\r\n        let clippedFocalPoints = [];\r\n        const selectedIds = _.map(selectedFocalpoints, \"_id\");\r\n        if (_.indexOf(selectedIds, initialState.primary_focal_point) < 0) {\r\n            let remainingFocalpoints = _.difference(focalpoints, selectedFocalpoints);\r\n            clippedFocalPoints = remainingFocalpoints;\r\n            remainingFocalpoints = _.map(remainingFocalpoints, (item: any) => {\r\n                return { _id: item._id };\r\n            });\r\n            const params = {\r\n                title: initialState.title,\r\n                focal_points: selectedFocalpoints,\r\n            };\r\n            selectedFocalpoints.forEach(async (fp: any) => {\r\n                fp.institutionInvites = fp.institutionInvites.filter((invite: any) => invite.institutionId != routes[1]);\r\n                const res = await apiService.patch(`/users/${fp._id}`, fp);\r\n            });\r\n            const institutionData = await apiService.patch(`/institution/${routes[1]}/updateFocalPoints`, params);\r\n            if (institutionData && institutionData._id) {\r\n                toast.success(t(\"toast.Focalpointsremovedfromorganisationsuccessfuly\"));\r\n                setFocalpoints(clippedFocalPoints);\r\n                setClearRows(true);\r\n                getUsers(userListParams);\r\n            }\r\n        } else {\r\n            toast.error(t(\"toast.Youcannotdeleteaprimaryfocalpoint\"));\r\n        }\r\n    };\r\n\r\n    const inviteFocalPoints = async () => {\r\n        if (userInvite.email == \"\") {\r\n            toast.error(t(\"Emailidisempty\"));\r\n        } else {\r\n            const name = userInvite.email.match(/^([^@]*)@/)[1];\r\n            const isUser = checkUser.find((x) => x.email == userInvite.email);\r\n            if (isUser !== undefined) {\r\n                toast.error(t(\"Givenexistinguserinnewuserfield\"));\r\n                return;\r\n            }\r\n            Object.assign(userInvite, { username: name });\r\n            const focalPointsarray = [];\r\n            focalPointsarray.push(userInvite);\r\n            const newFocalPoints = focalpoints.map((item, _i) => {\r\n                return { _id: item._id };\r\n            });\r\n            const obj = {\r\n                title: initialState.title,\r\n                focal_points: [userInvite, ...newFocalPoints],\r\n            };\r\n            const institutionData = await apiService.patch(`/institution/${routes[1]}`, obj);\r\n            if (institutionData && institutionData._id) {\r\n                toast.success(t(\"toast.Invitationsentsuccessfully\"));\r\n                const getInstutionData = await getApiService.getApiData(`/institution/${routes[1]}`, false, {});\r\n                if (getInstutionData && getInstutionData._id) {\r\n                    setInitialState(getInstutionData);\r\n                }\r\n                invitationService.inviteNewUserWithEmail(\r\n                    userInvite.email,\r\n                    userInvite.username,\r\n                    institutionData._id,\r\n                    institutionData.title\r\n                );\r\n                getUsers(userListParams);\r\n            } else {\r\n                toast.error(institutionData);\r\n            }\r\n            setUserInvite({ email: \"\" });\r\n        }\r\n    };\r\n\r\n    const inviteExistingUser = async () => {\r\n        const extuser = existingUser.map((item, _i) => {\r\n            return { _id: item.value };\r\n        });\r\n        if (extuser.length === 0) {\r\n            toast.error(t(\"ExistingUsersisempty\"));\r\n        } else {\r\n            const obj = {\r\n                title: initialState.title,\r\n                focal_points: [...focalpoints, ...extuser],\r\n            };\r\n            const institutionData = await apiService.patch(`/institution/${routes[1]}`, obj);\r\n            if (institutionData && institutionData._id) {\r\n                existingUser.forEach(async (user) => {\r\n                    const userId = user.value;\r\n                    let userData = await apiService.get(`/users/${userId}`);\r\n                    let newInInvites = [];\r\n                    if (userData?.institutionInvites?.length) {\r\n                        newInInvites = [...userData.institutionInvites];\r\n                        let dupInvite = newInInvites.filter((invite) => invite.institutionId === institutionData._id);\r\n                        if (dupInvite.length <= 0) {\r\n                            newInInvites.push({\r\n                                institutionName: institutionData.title,\r\n                                institutionId: institutionData._id,\r\n                                status: \"Request Pending\",\r\n                            });\r\n                        } else {\r\n                            newInInvites = newInInvites.map((invite) => {\r\n                                if (invite.institutionId === institutionData._id && invite.status === \"Rejected\") {\r\n                                    invite.status = \"Request Pending\";\r\n                                }\r\n                                return invite;\r\n                            });\r\n                        }\r\n                    } else {\r\n                        newInInvites = [\r\n                            {\r\n                                institutionName: institutionData.title,\r\n                                institutionId: institutionData._id,\r\n                                status: \"Request Pending\",\r\n                            },\r\n                        ];\r\n                    }\r\n                    userData.institutionInvites = newInInvites;\r\n                    const res = await apiService.patch(`/users/${userId}`, userData);\r\n                    getUsers(userListParams);\r\n                });\r\n                toast.success(t(\"toast.Invitationsentsuccessfully\"));\r\n                const institutionDataa = await getApiService.getApiData(`/institution/${routes[1]}`, false, {});\r\n                if (institutionDataa && institutionDataa._id) {\r\n                    setInitialState(institutionDataa);\r\n                }\r\n            }\r\n            setExistingUser([]);\r\n        }\r\n    };\r\n\r\n    const EditInstitutionLink = () => {\r\n        return (\r\n            <>\r\n                {initialState && initialState._id ? (\r\n                  <Link\r\n                      href=\"/institution/[...routes]\"\r\n                      as={`/institution/edit/${routes[1]}`}\r\n                      >\r\n                    <Button variant=\"secondary\" size=\"sm\">\r\n                      <i className=\"fas fa-pen\" />\r\n                      &nbsp;{t(\"Edit\")}\r\n                    </Button>\r\n                  </Link>\r\n                ) : null}\r\n            </>\r\n        );\r\n    };\r\n\r\n    const CanEditInstitution = canEditInstitution(() => <EditInstitutionLink />);\r\n\r\n    return (\r\n        <Container>\r\n            <Row className=\"mb-4\">\r\n                <Col>\r\n                    <h4 className=\"institutionTitle\">\r\n                        <Link href=\"/institution/[...routes]\" as={`/institution/show/${routes[1]}`}>\r\n                            {initialState.title}\r\n                        </Link>\r\n                        <CanEditInstitution />\r\n                    </h4>\r\n                </Col>\r\n            </Row>\r\n            <Row>\r\n                <Col className=\"medium\">\r\n                    <b>{t(\"PrimaryFocalPoint\")}</b>\r\n                </Col>\r\n            </Row>\r\n            <Row className=\"primary-focal-point-table mb-3\">\r\n                <RKITable\r\n                    columns={primaryFocalPointColumns}\r\n                    data={primary}\r\n                    loading={loading}\r\n                    subheader={false}\r\n                    pagination={false}\r\n                    pagServer={true}\r\n                    clearSelectedRows={clearRows}\r\n                />\r\n            </Row>\r\n            <Row>\r\n                <Col className=\"medium\">\r\n                    <b>{t(\"FocalPoints\")}</b>\r\n                </Col>\r\n            </Row>\r\n            <Row className=\"mb-3\">\r\n                <RKITable\r\n                    columns={focalPointColumns}\r\n                    data={focalpoints}\r\n                    loading={loading}\r\n                    pagServer={true}\r\n                    selectableRows\r\n                    subheader={false}\r\n                    onSelectedRowsChange={handleRowSelected}\r\n                    clearSelectedRows={clearRows}\r\n                    persistTableHead\r\n                    sortFunction={customSort}\r\n                />\r\n            </Row>\r\n            <Row>\r\n                <Col>\r\n                    <Button className=\"me-1\" onClick={handlePrimaryFocalPoints} variant=\"primary\" size=\"sm\">\r\n                        {t(\"Promote\")}\r\n                    </Button>\r\n                    <Button onClick={handleDeleteFocalPointFromOrganisation} variant=\"primary\" size=\"sm\">\r\n                        {t(\"DeleteFocal\")}\r\n                    </Button>\r\n                </Col>\r\n            </Row>\r\n            <br />\r\n            <Row>\r\n                <Col md={6}>\r\n                    <Form.Group controlId=\"formBasicEmail\">\r\n                        <Form.Label>{t(\"external\")}</Form.Label>\r\n                        <Form.Control\r\n                            name=\"email\"\r\n                            type=\"email\"\r\n                            placeholder={t(\"Enteremail\")}\r\n                            value={userInvite.email}\r\n                            onChange={handleChange}\r\n                        />\r\n                    </Form.Group>\r\n                    <Button onClick={inviteFocalPoints} variant=\"primary\" type=\"submit\">\r\n                        {t(\"InviteFocalPoint\")}\r\n                    </Button>\r\n                </Col>\r\n                <Col md={6}>\r\n                    <Form.Group controlId=\"formBasicEmail\">\r\n                        <Form.Label>{t(\"existing\")}</Form.Label>\r\n                        <MultiSelect\r\n                            overrideStrings={{\r\n                                selectSomeItems: t(\"SelectUsers\"),\r\n                            }}\r\n                            options={usersList}\r\n                            value={existingUser}\r\n                            onChange={handleUsers}\r\n                            className={\"focal-users\"}\r\n                            labelledBy={\"Select Users\"}\r\n                        />\r\n                    </Form.Group>\r\n                    <Button onClick={inviteExistingUser} variant=\"primary\" type=\"submit\">\r\n                        {t(\"Adduser\")}\r\n                    </Button>\r\n                </Col>\r\n            </Row>\r\n        </Container>\r\n    );\r\n};\r\n\r\nexport default InstitutionFocalPoint;\r\nfunction data_request(users: any, setUsersList: React.Dispatch<React.SetStateAction<any[]>>) {\r\n    if (users && Array.isArray(users.data)) {\r\n        let _users: any = [];\r\n        users.data.map((item: any, _i: any) => {\r\n            if ((item && item.status && item.status !== \"Request Pending\") || !item.status) {\r\n                _users.push({ label: item.username, value: item._id });\r\n            }\r\n        });\r\n        setUsersList(_users);\r\n    }\r\n}\r\n", "//Import Library\r\nimport React from 'react'\r\nimport DataTable  from 'react-data-table-component';\r\n\r\n//Import services/components\r\nimport { useTranslation } from 'next-i18next';\r\nimport CustomLoader from './CustomLoader';\r\n\r\ninterface RKITableProps {\r\n  columns: any[];\r\n  data: any[];\r\n  totalRows?: number;\r\n  resetPaginationToggle?: boolean;\r\n  subheader?: boolean;\r\n  subHeaderComponent?: React.ReactNode;\r\n  handlePerRowsChange?: (newPerPage: number, page: number) => void;\r\n  handlePageChange?: (page: number) => void;\r\n  rowsPerPage?: number[];\r\n  defaultRowsPerPage?: number;\r\n  selectableRows?: boolean;\r\n  loading?: boolean;\r\n  pagServer?: boolean;\r\n  onSelectedRowsChange?: (selectedRows: any) => void;\r\n  clearSelectedRows?: boolean;\r\n  sortServer?: boolean;\r\n  onSort?: (column: any, sortDirection: string) => void;\r\n  persistTableHead?: boolean;\r\n  sortFunction?: (rows: any[], field: any, direction: 'asc' | 'desc') => any[];\r\n  noHeader?: boolean;\r\n  dense?: boolean;\r\n  pagination?: boolean;\r\n  paginationServer?: boolean;\r\n  paginationTotalRows?: number;\r\n  subHeaderAlign?: string;\r\n  [key: string]: any;\r\n}\r\n\r\nfunction RKITable(props: RKITableProps) {\r\n  const { t } = useTranslation('common');\r\n  const paginationComponentOptions = {\r\n    rowsPerPageText: t('Rowsperpage'),\r\n    };\r\n  const {\r\n    columns,\r\n    data,\r\n    totalRows,\r\n    resetPaginationToggle,\r\n    subheader,\r\n    subHeaderComponent,\r\n    handlePerRowsChange,\r\n    handlePageChange,\r\n    rowsPerPage,\r\n    defaultRowsPerPage,\r\n    selectableRows,\r\n    loading,\r\n    pagServer,\r\n    onSelectedRowsChange,\r\n    clearSelectedRows,\r\n    sortServer,\r\n    onSort,\r\n    persistTableHead,\r\n    sortFunction,\r\n    // Filter out any unknown props that might cause DOM warnings\r\n    ...otherProps\r\n  } = props;\r\n\r\n  // Only pass known DataTable props to avoid DOM warnings\r\n  const dataTableProps = {\r\n    paginationComponentOptions,\r\n    noDataComponent: t(\"NoData\"),\r\n    noHeader: true,\r\n    columns,\r\n    data: data || [],\r\n    dense: true,\r\n    paginationResetDefaultPage: resetPaginationToggle,\r\n    subHeader: subheader,\r\n    progressPending: loading,\r\n    subHeaderComponent,\r\n    pagination: true,\r\n    paginationServer: pagServer,\r\n    paginationPerPage: defaultRowsPerPage ? defaultRowsPerPage : 10,\r\n    paginationRowsPerPageOptions: rowsPerPage ? rowsPerPage : [10, 15, 20, 25, 30],\r\n    paginationTotalRows: totalRows,\r\n    onChangeRowsPerPage: handlePerRowsChange,\r\n    onChangePage: handlePageChange,\r\n    selectableRows,\r\n    onSelectedRowsChange,\r\n    clearSelectedRows,\r\n    progressComponent: <CustomLoader/>,\r\n    sortIcon: <i className=\"sort-icon fas fa-exchange-alt\" />,\r\n    sortServer,\r\n    onSort,\r\n    sortFunction,\r\n    persistTableHead,\r\n    className: \"rki-table\"\r\n  };\r\n  return (\r\n    <DataTable {...dataTableProps} />\r\n  )\r\n};\r\n\r\nRKITable.defaultProps = {\r\n  subHeader: false,\r\n  pagination: true,\r\n  totalRows: null,\r\n  pagServer: true,\r\n  onSelectedRowsChange: null,\r\n  clearSelectedRows: false,\r\n  sortServer: false,\r\n  persistTableHead: false\r\n}\r\n\r\nexport default RKITable;\r\n\r\n"], "names": ["GetApiDataService", "getApiData", "url", "withData", "params", "response", "apiService", "get", "data", "error", "institution", "routes", "focalpoints", "setFocalpoints", "useState", "InstitutionFocalPoint", "selectedRows", "setSelectedRows", "primary", "setPrimary", "clearRows", "setClearRows", "initialState", "setInitialState", "usersList", "setUsersList", "existingUser", "setExistingUser", "loading", "setLoading", "checkUser", "setcheckUser", "t", "useTranslation", "userInvite", "setUserInvite", "username", "email", "_id", "usersParams", "query", "sort", "title", "limit", "select", "userListParams", "useEffect", "getUsers", "getExcludedInvitedUsers", "users", "filter", "user", "institutionInvites", "invite", "institutionId", "status", "length", "userListParamsinit", "usersinit", "Array", "isArray", "map", "_users", "item", "_i", "label", "value", "focal_points", "fetchUsersByID", "_", "focalPoints", "primary_focal_point", "usersQuery", "tableData", "for<PERSON>ach", "institutionInvite", "push", "institutionName", "institutionStatus", "primaryFocalPointColumns", "name", "selector", "cell", "div", "d", "dial_code", "mobile_number", "focalPointColumns", "sortable", "handleRowSelected", "useCallback", "state", "getPrimaryFocalPoint", "primary_focal_point_id", "primaryFocalpoint", "handlePrimaryFocalPoints", "toast", "primaryFocalPoint", "patch", "primaryvalue", "success", "handleDeleteFocalPointFromOrganisation", "<PERSON><PERSON><PERSON><PERSON>", "message", "buttons", "onClick", "deleteFocalpoints", "checkAndEmptyPrimaryFocalPoint", "_selectedFocalpoints", "find", "_fp", "selectedFocalpoints", "clippedFocalPoints", "selectedIds", "remainingFocalpoints", "fp", "institutionData", "inviteFocalPoints", "match", "isUser", "undefined", "x", "Object", "assign", "newFocalPoints", "obj", "getInstutionData", "getApiService", "invitationService", "inviteNewUserWithEmail", "inviteExistingUser", "extuser", "userData", "userId", "newInInvites", "institutionDataa", "EditInstitutionLink", "Link", "href", "as", "<PERSON><PERSON>", "variant", "size", "i", "className", "CanEditInstitution", "canEditInstitution", "Container", "Row", "Col", "h4", "b", "RKITable", "columns", "subheader", "pagination", "pagServer", "clearSelectedRows", "selectableRows", "onSelectedRowsChange", "persistTableHead", "sortFunction", "rows", "field", "direction", "handleField", "row", "toLowerCase", "br", "md", "Form", "Group", "controlId", "Label", "Control", "type", "placeholder", "onChange", "handleChange", "e", "target", "prevState", "MultiSelect", "overrideStrings", "selectSomeItems", "options", "handleUsers", "labelledBy", "props", "paginationComponentOptions", "rowsPerPageText", "totalRows", "resetPaginationToggle", "subHeaderComponent", "handlePerRowsChange", "handlePageChange", "rowsPerPage", "defaultRowsPerPage", "sortServer", "onSort", "otherProps", "dataTableProps", "noDataComponent", "<PERSON><PERSON><PERSON><PERSON>", "dense", "paginationResetDefaultPage", "subHeader", "progressPending", "paginationServer", "paginationPerPage", "paginationRowsPerPageOptions", "paginationTotalRows", "onChangeRowsPerPage", "onChangePage", "progressComponent", "CustomLoader", "sortIcon", "DataTable", "defaultProps"], "sourceRoot": "", "ignoreList": []}