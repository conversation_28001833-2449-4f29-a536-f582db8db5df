{"version": 3, "file": "static/chunks/pages/hazard/HazardAccordianSection-94a934d4d7b4246b.js", "mappings": "uMAuKA,MAnIqBA,IACnB,GAAM,GAAEC,CAAC,CAAE,CAAGC,CAAAA,EAAAA,EAAAA,EAAAA,CAAcA,CAAC,MAkIhBC,IAjIP,CAACC,EAAQC,EAAU,CAAGC,CAAAA,EAiIH,EAjIGA,QAAAA,CAAQA,CAAc,EAAE,EAG9CC,EAAqBC,IACzB,IAAMC,EAAc,8EAA8EC,IAAI,CAACF,EAAKG,WAAW,EAEvH,MACE,WAACC,MAAAA,CAAIC,UAAU,4BACb,WAACC,IAAAA,CAAED,UAAU,iBACX,UAACE,IAAAA,UAAGd,EAAE,cAAgB,IAAEO,EAAKQ,YAAY,EAAI,mBAE9CR,EAAKG,WAAW,EACf,WAACC,MAAAA,CAAIC,UAAU,wBACb,UAACC,IAAAA,UAAE,UAACC,IAAAA,UAAGd,EAAE,cACRQ,EACC,WAACG,MAAAA,WACC,UAACK,EAAAA,CAAeA,CAAAA,CAACC,KAAMC,EAAAA,GAAMA,CAAEC,KAAK,KAAKC,MAAM,OAAOR,UAAU,SAChE,UAACS,IAAAA,CAAET,UAAU,cAAcU,KAAMf,EAAKG,WAAW,CAAEa,OAAO,SAASC,IAAI,+BACpEjB,EAAKG,WAAW,MAIrB,UAACC,MAAAA,UACC,UAACE,IAAAA,CAAED,UAAU,YAAYa,MAAO,CAAEC,UAAW,WAAY,WACtDnB,EAAKG,WAAW,QAM1BH,EAAKoB,YAAY,EAChB,WAACC,EAAAA,CAAMA,CAAAA,CAAChB,UAAU,qCAAqCU,KAAMf,EAAKoB,YAAY,WAC3E3B,EAAE,YACH,UAACgB,EAAAA,CAAeA,CAAAA,CAACC,KAAMY,EAAAA,GAAUA,CAAEV,KAAK,KAAKP,UAAU,cAKjE,EA6CA,MA3CAkB,CAAAA,EAAAA,EAAAA,SAAAA,CAASA,CAAC,KACR,IAAMC,EAA8B,EAAE,CACtChC,GAASA,EAAMiC,OAAO,EAAIC,MAAMC,OAAO,CAACnC,EAAMiC,OAAO,GAAKjC,EAAMiC,OAAO,CAACG,GAAG,CAAC,CAAC5B,EAAM6B,KACjF,IACIC,EADEC,EAAW/B,GAAQA,EAAKgC,IAAI,CAACC,KAAK,CAAC,KAAKC,GAAG,GAGjD,OAAQH,GACN,IAAK,MACL,IAAK,MACL,IAAK,OACL,IAAK,MACHD,EAAS,GAAwC9B,MAAAA,CAArCmC,8BAAsB,CAAC,gBAAuB,OAATnC,EAAKoC,GAAG,EACzD,KACF,KAAK,MACHN,EAAS,gCACT,KACF,KAAK,OACHA,EAAS,iCACT,KACF,KAAK,MACL,IAAK,OACHA,EAAS,gCACT,KACF,SACEA,EAAS,iCACb,CAEA,IAAMO,EAAQ,CAAc,SAAbN,GAAoC,QAAbA,GAAmC,QAAbA,GAAmC,SAAbA,CAAa,CAAK,EAC/F,GAA4C/B,MAAAA,CAAzCmC,8BAAsB,CAAC,oBAA2B,OAATnC,EAAKoC,GAAG,EACnDE,EAAQ,GAAqE,OAAlEtC,GAAQA,EAAKuC,aAAa,CAAGvC,EAAKuC,aAAa,CAAG,iBAC7DC,EAAehD,EAAMiD,WAAW,EAAIf,MAAMC,OAAO,CAACnC,EAAMiD,WAAW,GACpEjD,EAAMiD,WAAW,CAACC,MAAM,CAAG,EAAIlD,EAAMiD,WAAW,CAACZ,EAAE,CAAG,GAE3DL,EAAemB,IAAI,CAAC,CAClBC,IAAKd,EACL3B,YAAaqC,EACbhC,aAAc8B,EACdlB,aAAciB,CAChB,EACF,GACAxC,EAAU2B,EACZ,EAAG,CAAChC,EAAM,EAGR,UAACY,MAAAA,UACER,GAA4B,IAAlBA,EAAO8C,MAAM,CACtB,UAACtC,MAAAA,CAAIC,UAAU,wCACb,UAACC,IAAAA,CAAED,UAAU,wDAAgDZ,EAAE,qBAGjE,UAACoD,EAAAA,EAAQA,CAAAA,CACPC,YAAY,EACZC,YAAY,EACZC,gBAAgB,EAChBC,cAAc,EACdC,mBAAmB,EACnBC,UAAU,EACVC,aAAa,EACbC,WAAW,EACXC,eAAe,EACfC,cAAc,EACdC,aAAc,IACZ5D,EAAOgC,GAAG,CAAC,CAAC5B,EAAMyD,IAChB,UAACC,MAAAA,CAECd,IAAK5C,EAAK4C,GAAG,CACbe,IAAK,aAAuB,OAAVF,EAAQ,GAC1BvC,MAAO,CAAE0C,MAAO,OAAQC,OAAQ,OAAQC,UAAW,OAAQ,GAHtDL,aAQV7D,EAAOgC,GAAG,CAAC,CAAC5B,EAAMyD,IACjB,WAACrD,MAAAA,WACC,UAACsD,MAAAA,CACCd,IAAK5C,EAAK4C,GAAG,CACbe,IAAK3D,EAAKQ,YAAY,EAAI,gBAC1BU,MAAO,CAAE6C,UAAW,QAASD,UAAW,SAAU,IAEnD/D,EAAkBC,KANXyD,OActB,mBCpKA,4CACA,iCACA,WACA,OAAe,EAAQ,IAAsD,CAC7E,EACA,UAFsB", "sources": ["webpack://_N_E/./components/common/ReactImages.tsx", "webpack://_N_E/?76ae"], "sourcesContent": ["\r\n//Import Library\r\nimport React, { useState, useEffect } from 'react';\r\nimport { Carousel } from 'react-responsive-carousel';\r\nimport { FontAwesomeIcon } from \"@fortawesome/react-fontawesome\";\r\nimport {\r\n  faLink, faDownload\r\n} from \"@fortawesome/free-solid-svg-icons\";\r\nimport { Button } from \"react-bootstrap\";\r\n\r\n//Import services/components\r\nimport { useTranslation } from 'next-i18next';\r\n\r\n// Import CSS for react-responsive-carousel\r\nimport \"react-responsive-carousel/lib/styles/carousel.min.css\";\r\n\r\n// Define types for image items\r\ninterface ImageItem {\r\n  src: string;\r\n  description: string;\r\n  originalName: string;\r\n  downloadLink: string | false;\r\n}\r\n\r\ninterface ReactImagesProps {\r\n  gallery?: Array<{\r\n    _id: string;\r\n    name: string;\r\n    original_name?: string;\r\n    src?: string;\r\n    caption?: string;\r\n    alt?: string;\r\n  }> | boolean;\r\n  imageSource?: string[] | boolean;\r\n}\r\n\r\nconst ReactImages = (props: ReactImagesProps) => {\r\n  const { t } = useTranslation('common');\r\n  const [images, setImages] = useState<ImageItem[]>([]);\r\n\r\n  // Render image description and metadata\r\n  const renderImageLegend = (item: ImageItem) => {\r\n    const isValidLink = /(http|https):\\/\\/(\\w+:{0,1}\\w*)?(\\S+)(:[0-9]+)?(\\/|\\/([\\w#!:.?+=&%!\\-\\/]))?/.test(item.description);\r\n\r\n    return (\r\n      <div className=\"carousel-legend\">\r\n        <p className=\"lead\">\r\n          <b>{t(\"Filename\")}</b> {item.originalName || \"No Name found\"}\r\n        </p>\r\n        {item.description && (\r\n          <div className=\"source_link\">\r\n            <p><b>{t(\"Source\")}</b></p>\r\n            {isValidLink ? (\r\n              <div>\r\n                <FontAwesomeIcon icon={faLink} size=\"1x\" color=\"#999\" className=\"me-1\" />\r\n                <a className=\"source_link\" href={item.description} target=\"_blank\" rel=\"noopener noreferrer\">\r\n                  {item.description}\r\n                </a>\r\n              </div>\r\n            ) : (\r\n              <div>\r\n                <p className=\"ps-0 py-0\" style={{ wordBreak: \"break-all\" }}>\r\n                  {item.description}\r\n                </p>\r\n              </div>\r\n            )}\r\n          </div>\r\n        )}\r\n        {item.downloadLink && (\r\n          <Button className=\"btn btn-success mt-2 btn--download\" href={item.downloadLink}>\r\n            {t(\"Download\")}\r\n            <FontAwesomeIcon icon={faDownload} size=\"1x\" className=\"ms-1\" />\r\n          </Button>\r\n        )}\r\n      </div>\r\n    );\r\n  };\r\n\r\n  useEffect(() => {\r\n    const carouselImages: ImageItem[] = [];\r\n    props && props.gallery && Array.isArray(props.gallery) && props.gallery.map((item, i) => {\r\n      const fileType = item && item.name.split('.').pop();\r\n      let imgSrc;\r\n\r\n      switch (fileType) {\r\n        case \"JPG\":\r\n        case \"jpg\":\r\n        case \"jpeg\":\r\n        case \"png\":\r\n          imgSrc = `${process.env.API_SERVER}/image/show/${item._id}`;\r\n          break;\r\n        case \"pdf\":\r\n          imgSrc = \"/images/fileIcons/pdfFile.png\";\r\n          break;\r\n        case \"docx\":\r\n          imgSrc = \"/images/fileIcons/wordFile.png\";\r\n          break;\r\n        case \"xls\":\r\n        case 'xlsx':\r\n          imgSrc = \"/images/fileIcons/xlsFile.png\";\r\n          break;\r\n        default:\r\n          imgSrc = \"/images/fileIcons/otherFile.png\";\r\n      }\r\n\r\n      const _link = (fileType === \"docx\" || fileType === \"pdf\" || fileType === \"xls\" || fileType === \"xlsx\")\r\n        && `${process.env.API_SERVER}/files/download/${item._id}`;\r\n      const _name = `${item && item.original_name ? item.original_name : \"No Name found\"}`;\r\n      const _description = props.imageSource && Array.isArray(props.imageSource)\r\n        && props.imageSource.length > 0 ? props.imageSource[i] : \"\";\r\n\r\n      carouselImages.push({\r\n        src: imgSrc,\r\n        description: _description,\r\n        originalName: _name,\r\n        downloadLink: _link\r\n      });\r\n    });\r\n    setImages(carouselImages);\r\n  }, [props]);\r\n\r\n  return (\r\n    <div>\r\n      {images && images.length === 0 ? (\r\n        <div className=\"border border-info my-3 mx-0\">\r\n          <p className=\"d-flex d-flex justify-content-center p-2 m-0\">{t(\"NoFilesFound!\")}</p>\r\n        </div>\r\n      ) : (\r\n        <Carousel\r\n          showThumbs={true}\r\n          showStatus={true}\r\n          showIndicators={true}\r\n          infiniteLoop={true}\r\n          useKeyboardArrows={true}\r\n          autoPlay={false}\r\n          stopOnHover={true}\r\n          swipeable={true}\r\n          dynamicHeight={false}\r\n          emulateTouch={true}\r\n          renderThumbs={() =>\r\n            images.map((item, index) => (\r\n              <img\r\n                key={index}\r\n                src={item.src}\r\n                alt={`Thumbnail ${index + 1}`}\r\n                style={{ width: '60px', height: '60px', objectFit: 'cover' }}\r\n              />\r\n            ))\r\n          }\r\n        >\r\n          {images.map((item, index) => (\r\n            <div key={index}>\r\n              <img\r\n                src={item.src}\r\n                alt={item.originalName || \"Gallery image\"}\r\n                style={{ maxHeight: '500px', objectFit: 'contain' }}\r\n              />\r\n              {renderImageLegend(item)}\r\n            </div>\r\n          ))}\r\n        </Carousel>\r\n      )}\r\n    </div>\r\n  );\r\n\r\n}\r\n\r\nexport default ReactImages;\r\n", "\n    (window.__NEXT_P = window.__NEXT_P || []).push([\n      \"/hazard/HazardAccordianSection\",\n      function () {\n        return require(\"private-next-pages/hazard/HazardAccordianSection.tsx\");\n      }\n    ]);\n    if(module.hot) {\n      module.hot.dispose(function () {\n        window.__NEXT_P.push([\"/hazard/HazardAccordianSection\"])\n      });\n    }\n  "], "names": ["props", "t", "useTranslation", "ReactImages", "images", "setImages", "useState", "renderImageLegend", "item", "isValidLink", "test", "description", "div", "className", "p", "b", "originalName", "FontAwesomeIcon", "icon", "faLink", "size", "color", "a", "href", "target", "rel", "style", "wordBreak", "downloadLink", "<PERSON><PERSON>", "faDownload", "useEffect", "carouselImages", "gallery", "Array", "isArray", "map", "i", "imgSrc", "fileType", "name", "split", "pop", "process", "_id", "_link", "_name", "original_name", "_description", "imageSource", "length", "push", "src", "Carousel", "showThumbs", "showStatus", "showIndicators", "infiniteLoop", "useKeyboardArrows", "autoPlay", "stopOnHover", "swipeable", "dynamicHeight", "emulate<PERSON><PERSON><PERSON>", "renderThumbs", "index", "img", "alt", "width", "height", "objectFit", "maxHeight"], "sourceRoot": "", "ignoreList": []}