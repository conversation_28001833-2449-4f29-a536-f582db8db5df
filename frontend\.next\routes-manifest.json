{"version": 3, "pages404": true, "caseSensitive": false, "basePath": "", "redirects": [{"source": "/:path+/", "destination": "/:path+", "locale": false, "internal": true, "statusCode": 308, "regex": "^(?:/((?:[^/]+?)(?:/(?:[^/]+?))*))/$"}], "headers": [], "dynamicRoutes": [{"page": "/adminsettings/[...routes]", "regex": "^/adminsettings/(.+?)(?:/)?$", "routeKeys": {"nxtProutes": "nxtProutes"}, "namedRegex": "^/adminsettings/(?<nxtProutes>.+?)(?:/)?$"}, {"page": "/country/[...routes]", "regex": "^/country/(.+?)(?:/)?$", "routeKeys": {"nxtProutes": "nxtProutes"}, "namedRegex": "^/country/(?<nxtProutes>.+?)(?:/)?$"}, {"page": "/declarationform/[...routes]", "regex": "^/declarationform/(.+?)(?:/)?$", "routeKeys": {"nxtProutes": "nxtProutes"}, "namedRegex": "^/declarationform/(?<nxtProutes>.+?)(?:/)?$"}, {"page": "/event/[...routes]", "regex": "^/event/(.+?)(?:/)?$", "routeKeys": {"nxtProutes": "nxtProutes"}, "namedRegex": "^/event/(?<nxtProutes>.+?)(?:/)?$"}, {"page": "/events-calendar/[...routes]", "regex": "^/events\\-calendar/(.+?)(?:/)?$", "routeKeys": {"nxtProutes": "nxtProutes"}, "namedRegex": "^/events\\-calendar/(?<nxtProutes>.+?)(?:/)?$"}, {"page": "/hazard/[...routes]", "regex": "^/hazard/(.+?)(?:/)?$", "routeKeys": {"nxtProutes": "nxtProutes"}, "namedRegex": "^/hazard/(?<nxtProutes>.+?)(?:/)?$"}, {"page": "/institution/[...routes]", "regex": "^/institution/(.+?)(?:/)?$", "routeKeys": {"nxtProutes": "nxtProutes"}, "namedRegex": "^/institution/(?<nxtProutes>.+?)(?:/)?$"}, {"page": "/operation/[...routes]", "regex": "^/operation/(.+?)(?:/)?$", "routeKeys": {"nxtProutes": "nxtProutes"}, "namedRegex": "^/operation/(?<nxtProutes>.+?)(?:/)?$"}, {"page": "/project/[...routes]", "regex": "^/project/(.+?)(?:/)?$", "routeKeys": {"nxtProutes": "nxtProutes"}, "namedRegex": "^/project/(?<nxtProutes>.+?)(?:/)?$"}, {"page": "/reset-password/[passwordToken]", "regex": "^/reset\\-password/([^/]+?)(?:/)?$", "routeKeys": {"nxtPpasswordToken": "nxtPpasswordToken"}, "namedRegex": "^/reset\\-password/(?<nxtPpasswordToken>[^/]+?)(?:/)?$"}, {"page": "/updates/[...routes]", "regex": "^/updates/(.+?)(?:/)?$", "routeKeys": {"nxtProutes": "nxtProutes"}, "namedRegex": "^/updates/(?<nxtProutes>.+?)(?:/)?$"}, {"page": "/users/[...routes]", "regex": "^/users/(.+?)(?:/)?$", "routeKeys": {"nxtProutes": "nxtProutes"}, "namedRegex": "^/users/(?<nxtProutes>.+?)(?:/)?$"}, {"page": "/vspace/[...routes]", "regex": "^/vspace/(.+?)(?:/)?$", "routeKeys": {"nxtProutes": "nxtProutes"}, "namedRegex": "^/vspace/(?<nxtProutes>.+?)(?:/)?$"}], "staticRoutes": [{"page": "/", "regex": "^/(?:/)?$", "routeKeys": {}, "namedRegex": "^/(?:/)?$"}, {"page": "/admin/login", "regex": "^/admin/login(?:/)?$", "routeKeys": {}, "namedRegex": "^/admin/login(?:/)?$"}, {"page": "/adminsettings", "regex": "^/adminsettings(?:/)?$", "routeKeys": {}, "namedRegex": "^/adminsettings(?:/)?$"}, {"page": "/adminsettings/approval/AdminTable", "regex": "^/adminsettings/approval/AdminTable(?:/)?$", "routeKeys": {}, "namedRegex": "^/adminsettings/approval/AdminTable(?:/)?$"}, {"page": "/adminsettings/approval/InstitutionTable", "regex": "^/adminsettings/approval/InstitutionTable(?:/)?$", "routeKeys": {}, "namedRegex": "^/adminsettings/approval/InstitutionTable(?:/)?$"}, {"page": "/adminsettings/approval/VspaceAdmin", "regex": "^/adminsettings/approval/VspaceAdmin(?:/)?$", "routeKeys": {}, "namedRegex": "^/adminsettings/approval/VspaceAdmin(?:/)?$"}, {"page": "/adminsettings/approval/focal_point_appoval", "regex": "^/adminsettings/approval/focal_point_appoval(?:/)?$", "routeKeys": {}, "namedRegex": "^/adminsettings/approval/focal_point_appoval(?:/)?$"}, {"page": "/adminsettings/approval/institution_approval", "regex": "^/adminsettings/approval/institution_approval(?:/)?$", "routeKeys": {}, "namedRegex": "^/adminsettings/approval/institution_approval(?:/)?$"}, {"page": "/adminsettings/approval/vspace_appoval", "regex": "^/adminsettings/approval/vspace_appoval(?:/)?$", "routeKeys": {}, "namedRegex": "^/adminsettings/approval/vspace_appoval(?:/)?$"}, {"page": "/adminsettings/areaOfWork", "regex": "^/adminsettings/areaOfWork(?:/)?$", "routeKeys": {}, "namedRegex": "^/adminsettings/areaOfWork(?:/)?$"}, {"page": "/adminsettings/areaOfWork/areaOfWorkTable", "regex": "^/adminsettings/areaOfWork/areaOfWorkTable(?:/)?$", "routeKeys": {}, "namedRegex": "^/adminsettings/areaOfWork/areaOfWorkTable(?:/)?$"}, {"page": "/adminsettings/areaOfWork/forms", "regex": "^/adminsettings/areaOfWork/forms(?:/)?$", "routeKeys": {}, "namedRegex": "^/adminsettings/areaOfWork/forms(?:/)?$"}, {"page": "/adminsettings/categories", "regex": "^/adminsettings/categories(?:/)?$", "routeKeys": {}, "namedRegex": "^/adminsettings/categories(?:/)?$"}, {"page": "/adminsettings/categories/categoryTable", "regex": "^/adminsettings/categories/categoryTable(?:/)?$", "routeKeys": {}, "namedRegex": "^/adminsettings/categories/categoryTable(?:/)?$"}, {"page": "/adminsettings/categories/form", "regex": "^/adminsettings/categories/form(?:/)?$", "routeKeys": {}, "namedRegex": "^/adminsettings/categories/form(?:/)?$"}, {"page": "/adminsettings/content", "regex": "^/adminsettings/content(?:/)?$", "routeKeys": {}, "namedRegex": "^/adminsettings/content(?:/)?$"}, {"page": "/adminsettings/content/ContentTableFilter", "regex": "^/adminsettings/content/ContentTableFilter(?:/)?$", "routeKeys": {}, "namedRegex": "^/adminsettings/content/ContentTableFilter(?:/)?$"}, {"page": "/adminsettings/country", "regex": "^/adminsettings/country(?:/)?$", "routeKeys": {}, "namedRegex": "^/adminsettings/country(?:/)?$"}, {"page": "/adminsettings/country/countryTable", "regex": "^/adminsettings/country/countryTable(?:/)?$", "routeKeys": {}, "namedRegex": "^/adminsettings/country/countryTable(?:/)?$"}, {"page": "/adminsettings/country/countryTableFilter", "regex": "^/adminsettings/country/countryTableFilter(?:/)?$", "routeKeys": {}, "namedRegex": "^/adminsettings/country/countryTableFilter(?:/)?$"}, {"page": "/adminsettings/country/form", "regex": "^/adminsettings/country/form(?:/)?$", "routeKeys": {}, "namedRegex": "^/adminsettings/country/form(?:/)?$"}, {"page": "/adminsettings/deploymentstatus", "regex": "^/adminsettings/deploymentstatus(?:/)?$", "routeKeys": {}, "namedRegex": "^/adminsettings/deploymentstatus(?:/)?$"}, {"page": "/adminsettings/deploymentstatus/deploymentstatusTable", "regex": "^/adminsettings/deploymentstatus/deploymentstatusTable(?:/)?$", "routeKeys": {}, "namedRegex": "^/adminsettings/deploymentstatus/deploymentstatusTable(?:/)?$"}, {"page": "/adminsettings/deploymentstatus/form", "regex": "^/adminsettings/deploymentstatus/form(?:/)?$", "routeKeys": {}, "namedRegex": "^/adminsettings/deploymentstatus/form(?:/)?$"}, {"page": "/adminsettings/eventstatuses", "regex": "^/adminsettings/eventstatuses(?:/)?$", "routeKeys": {}, "namedRegex": "^/adminsettings/eventstatuses(?:/)?$"}, {"page": "/adminsettings/eventstatuses/eventstatusTable", "regex": "^/adminsettings/eventstatuses/eventstatusTable(?:/)?$", "routeKeys": {}, "namedRegex": "^/adminsettings/eventstatuses/eventstatusTable(?:/)?$"}, {"page": "/adminsettings/eventstatuses/form", "regex": "^/adminsettings/eventstatuses/form(?:/)?$", "routeKeys": {}, "namedRegex": "^/adminsettings/eventstatuses/form(?:/)?$"}, {"page": "/adminsettings/expertise", "regex": "^/adminsettings/expertise(?:/)?$", "routeKeys": {}, "namedRegex": "^/adminsettings/expertise(?:/)?$"}, {"page": "/adminsettings/expertise/expertiseTable", "regex": "^/adminsettings/expertise/expertiseTable(?:/)?$", "routeKeys": {}, "namedRegex": "^/adminsettings/expertise/expertiseTable(?:/)?$"}, {"page": "/adminsettings/expertise/form", "regex": "^/adminsettings/expertise/form(?:/)?$", "routeKeys": {}, "namedRegex": "^/adminsettings/expertise/form(?:/)?$"}, {"page": "/adminsettings/hazard", "regex": "^/adminsettings/hazard(?:/)?$", "routeKeys": {}, "namedRegex": "^/adminsettings/hazard(?:/)?$"}, {"page": "/adminsettings/hazard/forms", "regex": "^/adminsettings/hazard/forms(?:/)?$", "routeKeys": {}, "namedRegex": "^/adminsettings/hazard/forms(?:/)?$"}, {"page": "/adminsettings/hazard/hazardReactDropZone", "regex": "^/adminsettings/hazard/hazardReactDropZone(?:/)?$", "routeKeys": {}, "namedRegex": "^/adminsettings/hazard/hazardReactDropZone(?:/)?$"}, {"page": "/adminsettings/hazard/hazardTable", "regex": "^/adminsettings/hazard/hazardTable(?:/)?$", "routeKeys": {}, "namedRegex": "^/adminsettings/hazard/hazardTable(?:/)?$"}, {"page": "/adminsettings/hazard/hazardTableFilter", "regex": "^/adminsettings/hazard/hazardTableFilter(?:/)?$", "routeKeys": {}, "namedRegex": "^/adminsettings/hazard/hazardTableFilter(?:/)?$"}, {"page": "/adminsettings/hazardtypes", "regex": "^/adminsettings/hazardtypes(?:/)?$", "routeKeys": {}, "namedRegex": "^/adminsettings/hazardtypes(?:/)?$"}, {"page": "/adminsettings/hazardtypes/forms", "regex": "^/adminsettings/hazardtypes/forms(?:/)?$", "routeKeys": {}, "namedRegex": "^/adminsettings/hazardtypes/forms(?:/)?$"}, {"page": "/adminsettings/hazardtypes/hazardTypeTable", "regex": "^/adminsettings/hazardtypes/hazardTypeTable(?:/)?$", "routeKeys": {}, "namedRegex": "^/adminsettings/hazardtypes/hazardTypeTable(?:/)?$"}, {"page": "/adminsettings/institutionNetworks", "regex": "^/adminsettings/institutionNetworks(?:/)?$", "routeKeys": {}, "namedRegex": "^/adminsettings/institutionNetworks(?:/)?$"}, {"page": "/adminsettings/institutionNetworks/form", "regex": "^/adminsettings/institutionNetworks/form(?:/)?$", "routeKeys": {}, "namedRegex": "^/adminsettings/institutionNetworks/form(?:/)?$"}, {"page": "/adminsettings/institutionNetworks/institutionNetworkTable", "regex": "^/adminsettings/institutionNetworks/institutionNetworkTable(?:/)?$", "routeKeys": {}, "namedRegex": "^/adminsettings/institutionNetworks/institutionNetworkTable(?:/)?$"}, {"page": "/adminsettings/institutiontypes", "regex": "^/adminsettings/institutiontypes(?:/)?$", "routeKeys": {}, "namedRegex": "^/adminsettings/institutiontypes(?:/)?$"}, {"page": "/adminsettings/institutiontypes/form", "regex": "^/adminsettings/institutiontypes/form(?:/)?$", "routeKeys": {}, "namedRegex": "^/adminsettings/institutiontypes/form(?:/)?$"}, {"page": "/adminsettings/institutiontypes/institutionTypeTable", "regex": "^/adminsettings/institutiontypes/institutionTypeTable(?:/)?$", "routeKeys": {}, "namedRegex": "^/adminsettings/institutiontypes/institutionTypeTable(?:/)?$"}, {"page": "/adminsettings/landingPage", "regex": "^/adminsettings/landingPage(?:/)?$", "routeKeys": {}, "namedRegex": "^/adminsettings/landingPage(?:/)?$"}, {"page": "/adminsettings/landingPage/form", "regex": "^/adminsettings/landingPage/form(?:/)?$", "routeKeys": {}, "namedRegex": "^/adminsettings/landingPage/form(?:/)?$"}, {"page": "/adminsettings/landingPage/landingPageTable", "regex": "^/adminsettings/landingPage/landingPageTable(?:/)?$", "routeKeys": {}, "namedRegex": "^/adminsettings/landingPage/landingPageTable(?:/)?$"}, {"page": "/adminsettings/mailsettings", "regex": "^/adminsettings/mailsettings(?:/)?$", "routeKeys": {}, "namedRegex": "^/adminsettings/mailsettings(?:/)?$"}, {"page": "/adminsettings/mailsettings/form", "regex": "^/adminsettings/mailsettings/form(?:/)?$", "routeKeys": {}, "namedRegex": "^/adminsettings/mailsettings/form(?:/)?$"}, {"page": "/adminsettings/operationstatuses", "regex": "^/adminsettings/operationstatuses(?:/)?$", "routeKeys": {}, "namedRegex": "^/adminsettings/operationstatuses(?:/)?$"}, {"page": "/adminsettings/operationstatuses/form", "regex": "^/adminsettings/operationstatuses/form(?:/)?$", "routeKeys": {}, "namedRegex": "^/adminsettings/operationstatuses/form(?:/)?$"}, {"page": "/adminsettings/operationstatuses/operationstatusTable", "regex": "^/adminsettings/operationstatuses/operationstatusTable(?:/)?$", "routeKeys": {}, "namedRegex": "^/adminsettings/operationstatuses/operationstatusTable(?:/)?$"}, {"page": "/adminsettings/permissions", "regex": "^/adminsettings/permissions(?:/)?$", "routeKeys": {}, "namedRegex": "^/adminsettings/permissions(?:/)?$"}, {"page": "/adminsettings/projectstatuses", "regex": "^/adminsettings/projectstatuses(?:/)?$", "routeKeys": {}, "namedRegex": "^/adminsettings/projectstatuses(?:/)?$"}, {"page": "/adminsettings/projectstatuses/form", "regex": "^/adminsettings/projectstatuses/form(?:/)?$", "routeKeys": {}, "namedRegex": "^/adminsettings/projectstatuses/form(?:/)?$"}, {"page": "/adminsettings/projectstatuses/projectstatusTable", "regex": "^/adminsettings/projectstatuses/projectstatusTable(?:/)?$", "routeKeys": {}, "namedRegex": "^/adminsettings/projectstatuses/projectstatusTable(?:/)?$"}, {"page": "/adminsettings/region", "regex": "^/adminsettings/region(?:/)?$", "routeKeys": {}, "namedRegex": "^/adminsettings/region(?:/)?$"}, {"page": "/adminsettings/region/form", "regex": "^/adminsettings/region/form(?:/)?$", "routeKeys": {}, "namedRegex": "^/adminsettings/region/form(?:/)?$"}, {"page": "/adminsettings/region/regionTable", "regex": "^/adminsettings/region/regionTable(?:/)?$", "routeKeys": {}, "namedRegex": "^/adminsettings/region/regionTable(?:/)?$"}, {"page": "/adminsettings/region/regionTableFilter", "regex": "^/adminsettings/region/regionTableFilter(?:/)?$", "routeKeys": {}, "namedRegex": "^/adminsettings/region/regionTableFilter(?:/)?$"}, {"page": "/adminsettings/risklevel", "regex": "^/adminsettings/risklevel(?:/)?$", "routeKeys": {}, "namedRegex": "^/adminsettings/risklevel(?:/)?$"}, {"page": "/adminsettings/risklevel/form", "regex": "^/adminsettings/risklevel/form(?:/)?$", "routeKeys": {}, "namedRegex": "^/adminsettings/risklevel/form(?:/)?$"}, {"page": "/adminsettings/risklevel/risklevelTable", "regex": "^/adminsettings/risklevel/risklevelTable(?:/)?$", "routeKeys": {}, "namedRegex": "^/adminsettings/risklevel/risklevelTable(?:/)?$"}, {"page": "/adminsettings/roles", "regex": "^/adminsettings/roles(?:/)?$", "routeKeys": {}, "namedRegex": "^/adminsettings/roles(?:/)?$"}, {"page": "/adminsettings/roles/form", "regex": "^/adminsettings/roles/form(?:/)?$", "routeKeys": {}, "namedRegex": "^/adminsettings/roles/form(?:/)?$"}, {"page": "/adminsettings/roles/roleTable", "regex": "^/adminsettings/roles/roleTable(?:/)?$", "routeKeys": {}, "namedRegex": "^/adminsettings/roles/roleTable(?:/)?$"}, {"page": "/adminsettings/syndrome", "regex": "^/adminsettings/syndrome(?:/)?$", "routeKeys": {}, "namedRegex": "^/adminsettings/syndrome(?:/)?$"}, {"page": "/adminsettings/syndrome/form", "regex": "^/adminsettings/syndrome/form(?:/)?$", "routeKeys": {}, "namedRegex": "^/adminsettings/syndrome/form(?:/)?$"}, {"page": "/adminsettings/syndrome/syndromeTable", "regex": "^/adminsettings/syndrome/syndromeTable(?:/)?$", "routeKeys": {}, "namedRegex": "^/adminsettings/syndrome/syndromeTable(?:/)?$"}, {"page": "/adminsettings/updateType", "regex": "^/adminsettings/updateType(?:/)?$", "routeKeys": {}, "namedRegex": "^/adminsettings/updateType(?:/)?$"}, {"page": "/adminsettings/updateType/forms", "regex": "^/adminsettings/updateType/forms(?:/)?$", "routeKeys": {}, "namedRegex": "^/adminsettings/updateType/forms(?:/)?$"}, {"page": "/adminsettings/updateType/updateTypeTable", "regex": "^/adminsettings/updateType/updateTypeTable(?:/)?$", "routeKeys": {}, "namedRegex": "^/adminsettings/updateType/updateTypeTable(?:/)?$"}, {"page": "/adminsettings/user", "regex": "^/adminsettings/user(?:/)?$", "routeKeys": {}, "namedRegex": "^/adminsettings/user(?:/)?$"}, {"page": "/adminsettings/user/forms", "regex": "^/adminsettings/user/forms(?:/)?$", "routeKeys": {}, "namedRegex": "^/adminsettings/user/forms(?:/)?$"}, {"page": "/adminsettings/user/userTable", "regex": "^/adminsettings/user/userTable(?:/)?$", "routeKeys": {}, "namedRegex": "^/adminsettings/user/userTable(?:/)?$"}, {"page": "/adminsettings/user/userTableFilter", "regex": "^/adminsettings/user/userTableFilter(?:/)?$", "routeKeys": {}, "namedRegex": "^/adminsettings/user/userTableFilter(?:/)?$"}, {"page": "/adminsettings/worldregion", "regex": "^/adminsettings/worldregion(?:/)?$", "routeKeys": {}, "namedRegex": "^/adminsettings/worldregion(?:/)?$"}, {"page": "/adminsettings/worldregion/form", "regex": "^/adminsettings/worldregion/form(?:/)?$", "routeKeys": {}, "namedRegex": "^/adminsettings/worldregion/form(?:/)?$"}, {"page": "/adminsettings/worldregion/worldregionTable", "regex": "^/adminsettings/worldregion/worldregionTable(?:/)?$", "routeKeys": {}, "namedRegex": "^/adminsettings/worldregion/worldregionTable(?:/)?$"}, {"page": "/country", "regex": "^/country(?:/)?$", "routeKeys": {}, "namedRegex": "^/country(?:/)?$"}, {"page": "/country/CountriesGlossary", "regex": "^/country/CountriesGlossary(?:/)?$", "routeKeys": {}, "namedRegex": "^/country/CountriesGlossary(?:/)?$"}, {"page": "/country/CountriesMap", "regex": "^/country/CountriesMap(?:/)?$", "routeKeys": {}, "namedRegex": "^/country/CountriesMap(?:/)?$"}, {"page": "/country/CountriesNamesListing", "regex": "^/country/CountriesNamesListing(?:/)?$", "routeKeys": {}, "namedRegex": "^/country/CountriesNamesListing(?:/)?$"}, {"page": "/country/CountryShow", "regex": "^/country/CountryShow(?:/)?$", "routeKeys": {}, "namedRegex": "^/country/CountryShow(?:/)?$"}, {"page": "/country/OrganizationTable", "regex": "^/country/OrganizationTable(?:/)?$", "routeKeys": {}, "namedRegex": "^/country/OrganizationTable(?:/)?$"}, {"page": "/country/components/CountryAccordionSection", "regex": "^/country/components/CountryAccordionSection(?:/)?$", "routeKeys": {}, "namedRegex": "^/country/components/CountryAccordionSection(?:/)?$"}, {"page": "/country/components/CountryButtonSection", "regex": "^/country/components/CountryButtonSection(?:/)?$", "routeKeys": {}, "namedRegex": "^/country/components/CountryButtonSection(?:/)?$"}, {"page": "/country/components/CountryCoverSection", "regex": "^/country/components/CountryCoverSection(?:/)?$", "routeKeys": {}, "namedRegex": "^/country/components/CountryCoverSection(?:/)?$"}, {"page": "/country/components/CountryDocumentAccordion", "regex": "^/country/components/CountryDocumentAccordion(?:/)?$", "routeKeys": {}, "namedRegex": "^/country/components/CountryDocumentAccordion(?:/)?$"}, {"page": "/country/components/CountryMediaGalleryAccordion", "regex": "^/country/components/CountryMediaGalleryAccordion(?:/)?$", "routeKeys": {}, "namedRegex": "^/country/components/CountryMediaGalleryAccordion(?:/)?$"}, {"page": "/country/components/CountryOrganisationAccordion", "regex": "^/country/components/CountryOrganisationAccordion(?:/)?$", "routeKeys": {}, "namedRegex": "^/country/components/CountryOrganisationAccordion(?:/)?$"}, {"page": "/country/components/DiscussionAccordion", "regex": "^/country/components/DiscussionAccordion(?:/)?$", "routeKeys": {}, "namedRegex": "^/country/components/DiscussionAccordion(?:/)?$"}, {"page": "/country/pagination", "regex": "^/country/pagination(?:/)?$", "routeKeys": {}, "namedRegex": "^/country/pagination(?:/)?$"}, {"page": "/country/permission", "regex": "^/country/permission(?:/)?$", "routeKeys": {}, "namedRegex": "^/country/permission(?:/)?$"}, {"page": "/dashboard/AboutUs", "regex": "^/dashboard/AboutUs(?:/)?$", "routeKeys": {}, "namedRegex": "^/dashboard/AboutUs(?:/)?$"}, {"page": "/dashboard/ActiveProjectOperations", "regex": "^/dashboard/ActiveProjectOperations(?:/)?$", "routeKeys": {}, "namedRegex": "^/dashboard/ActiveProjectOperations(?:/)?$"}, {"page": "/dashboard/Announcement", "regex": "^/dashboard/Announcement(?:/)?$", "routeKeys": {}, "namedRegex": "^/dashboard/Announcement(?:/)?$"}, {"page": "/dashboard/AnnouncementItem", "regex": "^/dashboard/AnnouncementItem(?:/)?$", "routeKeys": {}, "namedRegex": "^/dashboard/AnnouncementItem(?:/)?$"}, {"page": "/dashboard/CalendarEvents", "regex": "^/dashboard/CalendarEvents(?:/)?$", "routeKeys": {}, "namedRegex": "^/dashboard/CalendarEvents(?:/)?$"}, {"page": "/dashboard/Dashboard", "regex": "^/dashboard/Dashboard(?:/)?$", "routeKeys": {}, "namedRegex": "^/dashboard/Dashboard(?:/)?$"}, {"page": "/dashboard/ListContainer", "regex": "^/dashboard/ListContainer(?:/)?$", "routeKeys": {}, "namedRegex": "^/dashboard/ListContainer(?:/)?$"}, {"page": "/dashboard/OngoingOperations", "regex": "^/dashboard/OngoingOperations(?:/)?$", "routeKeys": {}, "namedRegex": "^/dashboard/OngoingOperations(?:/)?$"}, {"page": "/dashboard/OngoingProjects", "regex": "^/dashboard/OngoingProjects(?:/)?$", "routeKeys": {}, "namedRegex": "^/dashboard/OngoingProjects(?:/)?$"}, {"page": "/data-privacy-policy", "regex": "^/data\\-privacy\\-policy(?:/)?$", "routeKeys": {}, "namedRegex": "^/data\\-privacy\\-policy(?:/)?$"}, {"page": "/declarationform/declarationform", "regex": "^/declarationform/declarationform(?:/)?$", "routeKeys": {}, "namedRegex": "^/declarationform/declarationform(?:/)?$"}, {"page": "/declarationform/invalidLink", "regex": "^/declarationform/invalidLink(?:/)?$", "routeKeys": {}, "namedRegex": "^/declarationform/invalidLink(?:/)?$"}, {"page": "/event", "regex": "^/event(?:/)?$", "routeKeys": {}, "namedRegex": "^/event(?:/)?$"}, {"page": "/event/EventShow", "regex": "^/event/EventShow(?:/)?$", "routeKeys": {}, "namedRegex": "^/event/EventShow(?:/)?$"}, {"page": "/event/EventsTable", "regex": "^/event/EventsTable(?:/)?$", "routeKeys": {}, "namedRegex": "^/event/EventsTable(?:/)?$"}, {"page": "/event/EventsTableFilter", "regex": "^/event/EventsTableFilter(?:/)?$", "routeKeys": {}, "namedRegex": "^/event/EventsTableFilter(?:/)?$"}, {"page": "/event/Form", "regex": "^/event/Form(?:/)?$", "routeKeys": {}, "namedRegex": "^/event/Form(?:/)?$"}, {"page": "/event/ListMapcontainer", "regex": "^/event/ListMapcontainer(?:/)?$", "routeKeys": {}, "namedRegex": "^/event/ListMapcontainer(?:/)?$"}, {"page": "/event/components/DiscussionAccordion", "regex": "^/event/components/DiscussionAccordion(?:/)?$", "routeKeys": {}, "namedRegex": "^/event/components/DiscussionAccordion(?:/)?$"}, {"page": "/event/components/EventAccordionSection", "regex": "^/event/components/EventAccordionSection(?:/)?$", "routeKeys": {}, "namedRegex": "^/event/components/EventAccordionSection(?:/)?$"}, {"page": "/event/components/EventCoverSection", "regex": "^/event/components/EventCoverSection(?:/)?$", "routeKeys": {}, "namedRegex": "^/event/components/EventCoverSection(?:/)?$"}, {"page": "/event/components/EventHeaderSection", "regex": "^/event/components/EventHeaderSection(?:/)?$", "routeKeys": {}, "namedRegex": "^/event/components/EventHeaderSection(?:/)?$"}, {"page": "/event/components/MediaGalleryAccordion", "regex": "^/event/components/MediaGalleryAccordion(?:/)?$", "routeKeys": {}, "namedRegex": "^/event/components/MediaGalleryAccordion(?:/)?$"}, {"page": "/event/components/MoreInformationAccordion", "regex": "^/event/components/MoreInformationAccordion(?:/)?$", "routeKeys": {}, "namedRegex": "^/event/components/MoreInformationAccordion(?:/)?$"}, {"page": "/event/components/RiskAssessmentAccordion", "regex": "^/event/components/RiskAssessmentAccordion(?:/)?$", "routeKeys": {}, "namedRegex": "^/event/components/RiskAssessmentAccordion(?:/)?$"}, {"page": "/event/permission", "regex": "^/event/permission(?:/)?$", "routeKeys": {}, "namedRegex": "^/event/permission(?:/)?$"}, {"page": "/events-calendar", "regex": "^/events\\-calendar(?:/)?$", "routeKeys": {}, "namedRegex": "^/events\\-calendar(?:/)?$"}, {"page": "/forgot-password", "regex": "^/forgot\\-password(?:/)?$", "routeKeys": {}, "namedRegex": "^/forgot\\-password(?:/)?$"}, {"page": "/hazard", "regex": "^/hazard(?:/)?$", "routeKeys": {}, "namedRegex": "^/hazard(?:/)?$"}, {"page": "/hazard/DocumentAccordian", "regex": "^/hazard/DocumentAccordian(?:/)?$", "routeKeys": {}, "namedRegex": "^/hazard/DocumentAccordian(?:/)?$"}, {"page": "/hazard/HazardAccordianSection", "regex": "^/hazard/HazardAccordianSection(?:/)?$", "routeKeys": {}, "namedRegex": "^/hazard/HazardAccordianSection(?:/)?$"}, {"page": "/hazard/HazardCoverSection", "regex": "^/hazard/HazardCoverSection(?:/)?$", "routeKeys": {}, "namedRegex": "^/hazard/HazardCoverSection(?:/)?$"}, {"page": "/hazard/HazardCurrentEvent", "regex": "^/hazard/HazardCurrentEvent(?:/)?$", "routeKeys": {}, "namedRegex": "^/hazard/HazardCurrentEvent(?:/)?$"}, {"page": "/hazard/HazardOperation", "regex": "^/hazard/HazardOperation(?:/)?$", "routeKeys": {}, "namedRegex": "^/hazard/HazardOperation(?:/)?$"}, {"page": "/hazard/HazardOrganisation", "regex": "^/hazard/HazardOrganisation(?:/)?$", "routeKeys": {}, "namedRegex": "^/hazard/HazardOrganisation(?:/)?$"}, {"page": "/hazard/HazardPastEvent", "regex": "^/hazard/HazardPastEvent(?:/)?$", "routeKeys": {}, "namedRegex": "^/hazard/HazardPastEvent(?:/)?$"}, {"page": "/hazard/HazardSearch", "regex": "^/hazard/HazardSearch(?:/)?$", "routeKeys": {}, "namedRegex": "^/hazard/HazardSearch(?:/)?$"}, {"page": "/hazard/HazardShow", "regex": "^/hazard/HazardShow(?:/)?$", "routeKeys": {}, "namedRegex": "^/hazard/HazardShow(?:/)?$"}, {"page": "/hazard/MediaGalleryAccordion", "regex": "^/hazard/MediaGalleryAccordion(?:/)?$", "routeKeys": {}, "namedRegex": "^/hazard/MediaGalleryAccordion(?:/)?$"}, {"page": "/hazard/VirtualSpaceAccordian", "regex": "^/hazard/VirtualSpaceAccordian(?:/)?$", "routeKeys": {}, "namedRegex": "^/hazard/VirtualSpaceAccordian(?:/)?$"}, {"page": "/hazard/pagination", "regex": "^/hazard/pagination(?:/)?$", "routeKeys": {}, "namedRegex": "^/hazard/pagination(?:/)?$"}, {"page": "/hazard/permission", "regex": "^/hazard/permission(?:/)?$", "routeKeys": {}, "namedRegex": "^/hazard/permission(?:/)?$"}, {"page": "/home", "regex": "^/home(?:/)?$", "routeKeys": {}, "namedRegex": "^/home(?:/)?$"}, {"page": "/institution", "regex": "^/institution(?:/)?$", "routeKeys": {}, "namedRegex": "^/institution(?:/)?$"}, {"page": "/institution/Form", "regex": "^/institution/Form(?:/)?$", "routeKeys": {}, "namedRegex": "^/institution/Form(?:/)?$"}, {"page": "/institution/InfoPopup", "regex": "^/institution/InfoPopup(?:/)?$", "routeKeys": {}, "namedRegex": "^/institution/InfoPopup(?:/)?$"}, {"page": "/institution/InstitutionFocalPoint", "regex": "^/institution/InstitutionFocalPoint(?:/)?$", "routeKeys": {}, "namedRegex": "^/institution/InstitutionFocalPoint(?:/)?$"}, {"page": "/institution/InstitutionImageEditor", "regex": "^/institution/InstitutionImageEditor(?:/)?$", "routeKeys": {}, "namedRegex": "^/institution/InstitutionImageEditor(?:/)?$"}, {"page": "/institution/InstitutionImageHandler", "regex": "^/institution/InstitutionImageHandler(?:/)?$", "routeKeys": {}, "namedRegex": "^/institution/InstitutionImageHandler(?:/)?$"}, {"page": "/institution/InstitutionMapQuickInfo", "regex": "^/institution/InstitutionMapQuickInfo(?:/)?$", "routeKeys": {}, "namedRegex": "^/institution/InstitutionMapQuickInfo(?:/)?$"}, {"page": "/institution/InstitutionShow", "regex": "^/institution/InstitutionShow(?:/)?$", "routeKeys": {}, "namedRegex": "^/institution/InstitutionShow(?:/)?$"}, {"page": "/institution/InstitutionsFilter", "regex": "^/institution/InstitutionsFilter(?:/)?$", "routeKeys": {}, "namedRegex": "^/institution/InstitutionsFilter(?:/)?$"}, {"page": "/institution/InstitutionsTable", "regex": "^/institution/InstitutionsTable(?:/)?$", "routeKeys": {}, "namedRegex": "^/institution/InstitutionsTable(?:/)?$"}, {"page": "/institution/ListMapContainer", "regex": "^/institution/ListMapContainer(?:/)?$", "routeKeys": {}, "namedRegex": "^/institution/ListMapContainer(?:/)?$"}, {"page": "/institution/ReadMoreModal", "regex": "^/institution/ReadMoreModal(?:/)?$", "routeKeys": {}, "namedRegex": "^/institution/ReadMoreModal(?:/)?$"}, {"page": "/institution/components/DiscussionAccordion", "regex": "^/institution/components/DiscussionAccordion(?:/)?$", "routeKeys": {}, "namedRegex": "^/institution/components/DiscussionAccordion(?:/)?$"}, {"page": "/institution/components/InstitutionAccordionSection", "regex": "^/institution/components/InstitutionAccordionSection(?:/)?$", "routeKeys": {}, "namedRegex": "^/institution/components/InstitutionAccordionSection(?:/)?$"}, {"page": "/institution/components/InstitutionCoverSection", "regex": "^/institution/components/InstitutionCoverSection(?:/)?$", "routeKeys": {}, "namedRegex": "^/institution/components/InstitutionCoverSection(?:/)?$"}, {"page": "/institution/components/InstitutionCoverSectionContent", "regex": "^/institution/components/InstitutionCoverSectionContent(?:/)?$", "routeKeys": {}, "namedRegex": "^/institution/components/InstitutionCoverSectionContent(?:/)?$"}, {"page": "/institution/components/InstitutionInfoSection", "regex": "^/institution/components/InstitutionInfoSection(?:/)?$", "routeKeys": {}, "namedRegex": "^/institution/components/InstitutionInfoSection(?:/)?$"}, {"page": "/institution/components/MediaGalleryAccordion", "regex": "^/institution/components/MediaGalleryAccordion(?:/)?$", "routeKeys": {}, "namedRegex": "^/institution/components/MediaGalleryAccordion(?:/)?$"}, {"page": "/institution/components/MoreInfoAccordion", "regex": "^/institution/components/MoreInfoAccordion(?:/)?$", "routeKeys": {}, "namedRegex": "^/institution/components/MoreInfoAccordion(?:/)?$"}, {"page": "/institution/permission", "regex": "^/institution/permission(?:/)?$", "routeKeys": {}, "namedRegex": "^/institution/permission(?:/)?$"}, {"page": "/login", "regex": "^/login(?:/)?$", "routeKeys": {}, "namedRegex": "^/login(?:/)?$"}, {"page": "/operation", "regex": "^/operation(?:/)?$", "routeKeys": {}, "namedRegex": "^/operation(?:/)?$"}, {"page": "/operation/Form", "regex": "^/operation/Form(?:/)?$", "routeKeys": {}, "namedRegex": "^/operation/Form(?:/)?$"}, {"page": "/operation/ListMapContainer", "regex": "^/operation/ListMapContainer(?:/)?$", "routeKeys": {}, "namedRegex": "^/operation/ListMapContainer(?:/)?$"}, {"page": "/operation/OperationPartners", "regex": "^/operation/OperationPartners(?:/)?$", "routeKeys": {}, "namedRegex": "^/operation/OperationPartners(?:/)?$"}, {"page": "/operation/OperationShow", "regex": "^/operation/OperationShow(?:/)?$", "routeKeys": {}, "namedRegex": "^/operation/OperationShow(?:/)?$"}, {"page": "/operation/OperationsTable", "regex": "^/operation/OperationsTable(?:/)?$", "routeKeys": {}, "namedRegex": "^/operation/OperationsTable(?:/)?$"}, {"page": "/operation/OperationsTableFilter", "regex": "^/operation/OperationsTableFilter(?:/)?$", "routeKeys": {}, "namedRegex": "^/operation/OperationsTableFilter(?:/)?$"}, {"page": "/operation/components/DocumentsAccordian", "regex": "^/operation/components/DocumentsAccordian(?:/)?$", "routeKeys": {}, "namedRegex": "^/operation/components/DocumentsAccordian(?:/)?$"}, {"page": "/operation/components/MediaGalleryAccordian", "regex": "^/operation/components/MediaGalleryAccordian(?:/)?$", "routeKeys": {}, "namedRegex": "^/operation/components/MediaGalleryAccordian(?:/)?$"}, {"page": "/operation/components/OperationAccordianSection", "regex": "^/operation/components/OperationAccordianSection(?:/)?$", "routeKeys": {}, "namedRegex": "^/operation/components/OperationAccordianSection(?:/)?$"}, {"page": "/operation/components/OperationCoverSection", "regex": "^/operation/components/OperationCoverSection(?:/)?$", "routeKeys": {}, "namedRegex": "^/operation/components/OperationCoverSection(?:/)?$"}, {"page": "/operation/components/OperationDetailsAccordian", "regex": "^/operation/components/OperationDetailsAccordian(?:/)?$", "routeKeys": {}, "namedRegex": "^/operation/components/OperationDetailsAccordian(?:/)?$"}, {"page": "/operation/components/OperationInfo", "regex": "^/operation/components/OperationInfo(?:/)?$", "routeKeys": {}, "namedRegex": "^/operation/components/OperationInfo(?:/)?$"}, {"page": "/operation/components/OperationStats", "regex": "^/operation/components/OperationStats(?:/)?$", "routeKeys": {}, "namedRegex": "^/operation/components/OperationStats(?:/)?$"}, {"page": "/operation/components/OperationTimelineSection", "regex": "^/operation/components/OperationTimelineSection(?:/)?$", "routeKeys": {}, "namedRegex": "^/operation/components/OperationTimelineSection(?:/)?$"}, {"page": "/operation/components/PartnersAccordian", "regex": "^/operation/components/PartnersAccordian(?:/)?$", "routeKeys": {}, "namedRegex": "^/operation/components/PartnersAccordian(?:/)?$"}, {"page": "/operation/components/VirtualSpaceAccordian", "regex": "^/operation/components/VirtualSpaceAccordian(?:/)?$", "routeKeys": {}, "namedRegex": "^/operation/components/VirtualSpaceAccordian(?:/)?$"}, {"page": "/operation/permission", "regex": "^/operation/permission(?:/)?$", "routeKeys": {}, "namedRegex": "^/operation/permission(?:/)?$"}, {"page": "/people", "regex": "^/people(?:/)?$", "routeKeys": {}, "namedRegex": "^/people(?:/)?$"}, {"page": "/people/peopleTable", "regex": "^/people/peopleTable(?:/)?$", "routeKeys": {}, "namedRegex": "^/people/peopleTable(?:/)?$"}, {"page": "/people/peopleTableFilter", "regex": "^/people/peopleTableFilter(?:/)?$", "routeKeys": {}, "namedRegex": "^/people/peopleTableFilter(?:/)?$"}, {"page": "/profile", "regex": "^/profile(?:/)?$", "routeKeys": {}, "namedRegex": "^/profile(?:/)?$"}, {"page": "/profile/bookmarkTable", "regex": "^/profile/bookmarkTable(?:/)?$", "routeKeys": {}, "namedRegex": "^/profile/bookmarkTable(?:/)?$"}, {"page": "/profile/bookmarkTableFilter", "regex": "^/profile/bookmarkTableFilter(?:/)?$", "routeKeys": {}, "namedRegex": "^/profile/bookmarkTableFilter(?:/)?$"}, {"page": "/profile/confirmation", "regex": "^/profile/confirmation(?:/)?$", "routeKeys": {}, "namedRegex": "^/profile/confirmation(?:/)?$"}, {"page": "/profile/myConsent", "regex": "^/profile/myConsent(?:/)?$", "routeKeys": {}, "namedRegex": "^/profile/myConsent(?:/)?$"}, {"page": "/profile/profileEdit", "regex": "^/profile/profileEdit(?:/)?$", "routeKeys": {}, "namedRegex": "^/profile/profileEdit(?:/)?$"}, {"page": "/project", "regex": "^/project(?:/)?$", "routeKeys": {}, "namedRegex": "^/project(?:/)?$"}, {"page": "/project/Form", "regex": "^/project/Form(?:/)?$", "routeKeys": {}, "namedRegex": "^/project/Form(?:/)?$"}, {"page": "/project/ListMapContainer", "regex": "^/project/ListMapContainer(?:/)?$", "routeKeys": {}, "namedRegex": "^/project/ListMapContainer(?:/)?$"}, {"page": "/project/ProjectShow", "regex": "^/project/ProjectShow(?:/)?$", "routeKeys": {}, "namedRegex": "^/project/ProjectShow(?:/)?$"}, {"page": "/project/ProjectsTable", "regex": "^/project/ProjectsTable(?:/)?$", "routeKeys": {}, "namedRegex": "^/project/ProjectsTable(?:/)?$"}, {"page": "/project/ProjectsTableFilter", "regex": "^/project/ProjectsTableFilter(?:/)?$", "routeKeys": {}, "namedRegex": "^/project/ProjectsTableFilter(?:/)?$"}, {"page": "/project/components/DiscussionAccordion", "regex": "^/project/components/DiscussionAccordion(?:/)?$", "routeKeys": {}, "namedRegex": "^/project/components/DiscussionAccordion(?:/)?$"}, {"page": "/project/components/ProjectAccordianSection", "regex": "^/project/components/ProjectAccordianSection(?:/)?$", "routeKeys": {}, "namedRegex": "^/project/components/ProjectAccordianSection(?:/)?$"}, {"page": "/project/components/ProjectCoverSection", "regex": "^/project/components/ProjectCoverSection(?:/)?$", "routeKeys": {}, "namedRegex": "^/project/components/ProjectCoverSection(?:/)?$"}, {"page": "/project/components/ProjectDetailsAccordion", "regex": "^/project/components/ProjectDetailsAccordion(?:/)?$", "routeKeys": {}, "namedRegex": "^/project/components/ProjectDetailsAccordion(?:/)?$"}, {"page": "/project/components/ProjectInfoSection", "regex": "^/project/components/ProjectInfoSection(?:/)?$", "routeKeys": {}, "namedRegex": "^/project/components/ProjectInfoSection(?:/)?$"}, {"page": "/project/components/VirtualSpaceAccordion", "regex": "^/project/components/VirtualSpaceAccordion(?:/)?$", "routeKeys": {}, "namedRegex": "^/project/components/VirtualSpaceAccordion(?:/)?$"}, {"page": "/project/permission", "regex": "^/project/permission(?:/)?$", "routeKeys": {}, "namedRegex": "^/project/permission(?:/)?$"}, {"page": "/r403", "regex": "^/r403(?:/)?$", "routeKeys": {}, "namedRegex": "^/r403(?:/)?$"}, {"page": "/rNoAccess", "regex": "^/rNoAccess(?:/)?$", "routeKeys": {}, "namedRegex": "^/rNoAccess(?:/)?$"}, {"page": "/routePermissions", "regex": "^/routePermissions(?:/)?$", "routeKeys": {}, "namedRegex": "^/routePermissions(?:/)?$"}, {"page": "/search", "regex": "^/search(?:/)?$", "routeKeys": {}, "namedRegex": "^/search(?:/)?$"}, {"page": "/updates", "regex": "^/updates(?:/)?$", "routeKeys": {}, "namedRegex": "^/updates(?:/)?$"}, {"page": "/updates/CalendarEventForm", "regex": "^/updates/CalendarEventForm(?:/)?$", "routeKeys": {}, "namedRegex": "^/updates/CalendarEventForm(?:/)?$"}, {"page": "/updates/ContactForm", "regex": "^/updates/ContactForm(?:/)?$", "routeKeys": {}, "namedRegex": "^/updates/ContactForm(?:/)?$"}, {"page": "/updates/ConversationForm", "regex": "^/updates/ConversationForm(?:/)?$", "routeKeys": {}, "namedRegex": "^/updates/ConversationForm(?:/)?$"}, {"page": "/updates/DocumentForm", "regex": "^/updates/DocumentForm(?:/)?$", "routeKeys": {}, "namedRegex": "^/updates/DocumentForm(?:/)?$"}, {"page": "/updates/ImageForm", "regex": "^/updates/ImageForm(?:/)?$", "routeKeys": {}, "namedRegex": "^/updates/ImageForm(?:/)?$"}, {"page": "/updates/LinkForm", "regex": "^/updates/LinkForm(?:/)?$", "routeKeys": {}, "namedRegex": "^/updates/LinkForm(?:/)?$"}, {"page": "/users", "regex": "^/users(?:/)?$", "routeKeys": {}, "namedRegex": "^/users(?:/)?$"}, {"page": "/users/Form", "regex": "^/users/Form(?:/)?$", "routeKeys": {}, "namedRegex": "^/users/Form(?:/)?$"}, {"page": "/users/UsersTable", "regex": "^/users/UsersTable(?:/)?$", "routeKeys": {}, "namedRegex": "^/users/UsersTable(?:/)?$"}, {"page": "/users/View", "regex": "^/users/View(?:/)?$", "routeKeys": {}, "namedRegex": "^/users/View(?:/)?$"}, {"page": "/vspace", "regex": "^/vspace(?:/)?$", "routeKeys": {}, "namedRegex": "^/vspace(?:/)?$"}, {"page": "/vspace/AcceptRequestVspace", "regex": "^/vspace/AcceptRequestVspace(?:/)?$", "routeKeys": {}, "namedRegex": "^/vspace/AcceptRequestVspace(?:/)?$"}, {"page": "/vspace/AnnouncementsAccordian", "regex": "^/vspace/AnnouncementsAccordian(?:/)?$", "routeKeys": {}, "namedRegex": "^/vspace/AnnouncementsAccordian(?:/)?$"}, {"page": "/vspace/DocumentAccordian", "regex": "^/vspace/DocumentAccordian(?:/)?$", "routeKeys": {}, "namedRegex": "^/vspace/DocumentAccordian(?:/)?$"}, {"page": "/vspace/Form", "regex": "^/vspace/Form(?:/)?$", "routeKeys": {}, "namedRegex": "^/vspace/Form(?:/)?$"}, {"page": "/vspace/ManageMembers", "regex": "^/vspace/ManageMembers(?:/)?$", "routeKeys": {}, "namedRegex": "^/vspace/ManageMembers(?:/)?$"}, {"page": "/vspace/MediaGalleryAccordian", "regex": "^/vspace/MediaGalleryAccordian(?:/)?$", "routeKeys": {}, "namedRegex": "^/vspace/MediaGalleryAccordian(?:/)?$"}, {"page": "/vspace/View", "regex": "^/vspace/View(?:/)?$", "routeKeys": {}, "namedRegex": "^/vspace/View(?:/)?$"}, {"page": "/vspace/VirtualSpaceAccordionSection", "regex": "^/vspace/VirtualSpaceAccordionSection(?:/)?$", "routeKeys": {}, "namedRegex": "^/vspace/VirtualSpaceAccordionSection(?:/)?$"}, {"page": "/vspace/VirtualspaceCalendarEvents", "regex": "^/vspace/VirtualspaceCalendarEvents(?:/)?$", "routeKeys": {}, "namedRegex": "^/vspace/VirtualspaceCalendarEvents(?:/)?$"}, {"page": "/vspace/VirtualspaceMonitoringMembers", "regex": "^/vspace/VirtualspaceMonitoringMembers(?:/)?$", "routeKeys": {}, "namedRegex": "^/vspace/VirtualspaceMonitoringMembers(?:/)?$"}, {"page": "/vspace/VirtualspaceSubscribeRequestUsers", "regex": "^/vspace/VirtualspaceSubscribeRequestUsers(?:/)?$", "routeKeys": {}, "namedRegex": "^/vspace/VirtualspaceSubscribeRequestUsers(?:/)?$"}, {"page": "/vspace/permission", "regex": "^/vspace/permission(?:/)?$", "routeKeys": {}, "namedRegex": "^/vspace/permission(?:/)?$"}, {"page": "/vspace/vspace_announcement/Announcement", "regex": "^/vspace/vspace_announcement/Announcement(?:/)?$", "routeKeys": {}, "namedRegex": "^/vspace/vspace_announcement/Announcement(?:/)?$"}, {"page": "/vspace/vspace_announcement/AnnouncementItem", "regex": "^/vspace/vspace_announcement/AnnouncementItem(?:/)?$", "routeKeys": {}, "namedRegex": "^/vspace/vspace_announcement/AnnouncementItem(?:/)?$"}], "dataRoutes": [{"page": "/", "dataRouteRegex": "^/_next/data/yN9sb2e82_hGmHqnhKEMB/index\\.json$"}, {"page": "/adminsettings", "dataRouteRegex": "^/_next/data/yN9sb2e82_hGmHqnhKEMB/adminsettings\\.json$"}, {"page": "/adminsettings/approval/focal_point_appoval", "dataRouteRegex": "^/_next/data/yN9sb2e82_hGmHqnhKEMB/adminsettings/approval/focal_point_appoval\\.json$"}, {"page": "/adminsettings/approval/institution_approval", "dataRouteRegex": "^/_next/data/yN9sb2e82_hGmHqnhKEMB/adminsettings/approval/institution_approval\\.json$"}, {"page": "/adminsettings/content", "dataRouteRegex": "^/_next/data/yN9sb2e82_hGmHqnhKEMB/adminsettings/content\\.json$"}, {"page": "/adminsettings/country", "dataRouteRegex": "^/_next/data/yN9sb2e82_hGmHqnhKEMB/adminsettings/country\\.json$"}, {"page": "/adminsettings/deploymentstatus", "dataRouteRegex": "^/_next/data/yN9sb2e82_hGmHqnhKEMB/adminsettings/deploymentstatus\\.json$"}, {"page": "/adminsettings/expertise", "dataRouteRegex": "^/_next/data/yN9sb2e82_hGmHqnhKEMB/adminsettings/expertise\\.json$"}, {"page": "/adminsettings/hazard", "dataRouteRegex": "^/_next/data/yN9sb2e82_hGmHqnhKEMB/adminsettings/hazard\\.json$"}, {"page": "/adminsettings/region", "dataRouteRegex": "^/_next/data/yN9sb2e82_hGmHqnhKEMB/adminsettings/region\\.json$"}, {"page": "/adminsettings/risklevel", "dataRouteRegex": "^/_next/data/yN9sb2e82_hGmHqnhKEMB/adminsettings/risklevel\\.json$"}, {"page": "/adminsettings/syndrome", "dataRouteRegex": "^/_next/data/yN9sb2e82_hGmHqnhKEMB/adminsettings/syndrome\\.json$"}, {"page": "/adminsettings/worldregion", "dataRouteRegex": "^/_next/data/yN9sb2e82_hGmHqnhKEMB/adminsettings/worldregion\\.json$"}, {"page": "/adminsettings/[...routes]", "routeKeys": {"nxtProutes": "nxtProutes"}, "dataRouteRegex": "^/_next/data/yN9sb2e82_hGmHqnhKEMB/adminsettings/(.+?)\\.json$", "namedDataRouteRegex": "^/_next/data/yN9sb2e82_hGmHqnhKEMB/adminsettings/(?<nxtProutes>.+?)\\.json$"}, {"page": "/country", "dataRouteRegex": "^/_next/data/yN9sb2e82_hGmHqnhKEMB/country\\.json$"}, {"page": "/country/[...routes]", "routeKeys": {"nxtProutes": "nxtProutes"}, "dataRouteRegex": "^/_next/data/yN9sb2e82_hGmHqnhKEMB/country/(.+?)\\.json$", "namedDataRouteRegex": "^/_next/data/yN9sb2e82_hGmHqnhKEMB/country/(?<nxtProutes>.+?)\\.json$"}, {"page": "/dashboard/Dashboard", "dataRouteRegex": "^/_next/data/yN9sb2e82_hGmHqnhKEMB/dashboard/Dashboard\\.json$"}, {"page": "/data-privacy-policy", "dataRouteRegex": "^/_next/data/yN9sb2e82_hGmHqnhKEMB/data-privacy-policy\\.json$"}, {"page": "/declarationform/[...routes]", "routeKeys": {"nxtProutes": "nxtProutes"}, "dataRouteRegex": "^/_next/data/yN9sb2e82_hGmHqnhKEMB/declarationform/(.+?)\\.json$", "namedDataRouteRegex": "^/_next/data/yN9sb2e82_hGmHqnhKEMB/declarationform/(?<nxtProutes>.+?)\\.json$"}, {"page": "/event", "dataRouteRegex": "^/_next/data/yN9sb2e82_hGmHqnhKEMB/event\\.json$"}, {"page": "/event/[...routes]", "routeKeys": {"nxtProutes": "nxtProutes"}, "dataRouteRegex": "^/_next/data/yN9sb2e82_hGmHqnhKEMB/event/(.+?)\\.json$", "namedDataRouteRegex": "^/_next/data/yN9sb2e82_hGmHqnhKEMB/event/(?<nxtProutes>.+?)\\.json$"}, {"page": "/events-calendar", "dataRouteRegex": "^/_next/data/yN9sb2e82_hGmHqnhKEMB/events-calendar\\.json$"}, {"page": "/events-calendar/[...routes]", "routeKeys": {"nxtProutes": "nxtProutes"}, "dataRouteRegex": "^/_next/data/yN9sb2e82_hGmHqnhKEMB/events\\-calendar/(.+?)\\.json$", "namedDataRouteRegex": "^/_next/data/yN9sb2e82_hGmHqnhKEMB/events\\-calendar/(?<nxtProutes>.+?)\\.json$"}, {"page": "/hazard", "dataRouteRegex": "^/_next/data/yN9sb2e82_hGmHqnhKEMB/hazard\\.json$"}, {"page": "/hazard/[...routes]", "routeKeys": {"nxtProutes": "nxtProutes"}, "dataRouteRegex": "^/_next/data/yN9sb2e82_hGmHqnhKEMB/hazard/(.+?)\\.json$", "namedDataRouteRegex": "^/_next/data/yN9sb2e82_hGmHqnhKEMB/hazard/(?<nxtProutes>.+?)\\.json$"}, {"page": "/home", "dataRouteRegex": "^/_next/data/yN9sb2e82_hGmHqnhKEMB/home\\.json$"}, {"page": "/institution", "dataRouteRegex": "^/_next/data/yN9sb2e82_hGmHqnhKEMB/institution\\.json$"}, {"page": "/institution/[...routes]", "routeKeys": {"nxtProutes": "nxtProutes"}, "dataRouteRegex": "^/_next/data/yN9sb2e82_hGmHqnhKEMB/institution/(.+?)\\.json$", "namedDataRouteRegex": "^/_next/data/yN9sb2e82_hGmHqnhKEMB/institution/(?<nxtProutes>.+?)\\.json$"}, {"page": "/login", "dataRouteRegex": "^/_next/data/yN9sb2e82_hGmHqnhKEMB/login\\.json$"}, {"page": "/operation", "dataRouteRegex": "^/_next/data/yN9sb2e82_hGmHqnhKEMB/operation\\.json$"}, {"page": "/operation/[...routes]", "routeKeys": {"nxtProutes": "nxtProutes"}, "dataRouteRegex": "^/_next/data/yN9sb2e82_hGmHqnhKEMB/operation/(.+?)\\.json$", "namedDataRouteRegex": "^/_next/data/yN9sb2e82_hGmHqnhKEMB/operation/(?<nxtProutes>.+?)\\.json$"}, {"page": "/people", "dataRouteRegex": "^/_next/data/yN9sb2e82_hGmHqnhKEMB/people\\.json$"}, {"page": "/profile", "dataRouteRegex": "^/_next/data/yN9sb2e82_hGmHqnhKEMB/profile\\.json$"}, {"page": "/project", "dataRouteRegex": "^/_next/data/yN9sb2e82_hGmHqnhKEMB/project\\.json$"}, {"page": "/project/[...routes]", "routeKeys": {"nxtProutes": "nxtProutes"}, "dataRouteRegex": "^/_next/data/yN9sb2e82_hGmHqnhKEMB/project/(.+?)\\.json$", "namedDataRouteRegex": "^/_next/data/yN9sb2e82_hGmHqnhKEMB/project/(?<nxtProutes>.+?)\\.json$"}, {"page": "/updates", "dataRouteRegex": "^/_next/data/yN9sb2e82_hGmHqnhKEMB/updates\\.json$"}, {"page": "/updates/[...routes]", "routeKeys": {"nxtProutes": "nxtProutes"}, "dataRouteRegex": "^/_next/data/yN9sb2e82_hGmHqnhKEMB/updates/(.+?)\\.json$", "namedDataRouteRegex": "^/_next/data/yN9sb2e82_hGmHqnhKEMB/updates/(?<nxtProutes>.+?)\\.json$"}, {"page": "/users", "dataRouteRegex": "^/_next/data/yN9sb2e82_hGmHqnhKEMB/users\\.json$"}, {"page": "/users/[...routes]", "routeKeys": {"nxtProutes": "nxtProutes"}, "dataRouteRegex": "^/_next/data/yN9sb2e82_hGmHqnhKEMB/users/(.+?)\\.json$", "namedDataRouteRegex": "^/_next/data/yN9sb2e82_hGmHqnhKEMB/users/(?<nxtProutes>.+?)\\.json$"}, {"page": "/vspace", "dataRouteRegex": "^/_next/data/yN9sb2e82_hGmHqnhKEMB/vspace\\.json$"}, {"page": "/vspace/[...routes]", "routeKeys": {"nxtProutes": "nxtProutes"}, "dataRouteRegex": "^/_next/data/yN9sb2e82_hGmHqnhKEMB/vspace/(.+?)\\.json$", "namedDataRouteRegex": "^/_next/data/yN9sb2e82_hGmHqnhKEMB/vspace/(?<nxtProutes>.+?)\\.json$"}], "i18n": {"defaultLocale": "en", "locales": ["en", "de"]}, "rsc": {"header": "RSC", "varyHeader": "RSC, Next-Router-State-Tree, Next-Router-Prefetch, Next-Router-Segment-Prefetch", "prefetchHeader": "Next-Router-Prefetch", "didPostponeHeader": "x-nextjs-postponed", "contentTypeHeader": "text/x-component", "suffix": ".rsc", "prefetchSuffix": ".prefetch.rsc", "prefetchSegmentHeader": "Next-Router-Segment-Prefetch", "prefetchSegmentSuffix": ".segment.rsc", "prefetchSegmentDirSuffix": ".segments"}, "rewriteHeaders": {"pathHeader": "x-nextjs-rewritten-path", "queryHeader": "x-nextjs-rewritten-query"}, "rewrites": []}