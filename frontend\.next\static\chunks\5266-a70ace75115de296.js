(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[5266],{7940:(e,t,o)=>{"use strict";var a=o(14232),n=o(95062),r=o(15039);function i(e){return e&&"object"==typeof e&&"default"in e?e:{default:e}}var s=i(a),u=i(n),l=i(r),c=function(){return(c=Object.assign||function(e){for(var t,o=1,a=arguments.length;o<a;o++)for(var n in t=arguments[o])Object.prototype.hasOwnProperty.call(t,n)&&(e[n]=t[n]);return e}).apply(this,arguments)};function h(e,t){var o={};for(var a in e)Object.prototype.hasOwnProperty.call(e,a)&&0>t.indexOf(a)&&(o[a]=e[a]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var n=0,a=Object.getOwnPropertySymbols(e);n<a.length;n++)0>t.indexOf(a[n])&&Object.prototype.propertyIsEnumerable.call(e,a[n])&&(o[a[n]]=e[a[n]]);return o}var d=s.default.forwardRef(function(e,t){var o=e.classes,a=e.value,n=e.min,r=e.max,i=e.onChange,u=e.onMouseUpOrTouchEnd,l=e.onMouseUp,d=e.onTouchEnd,p=h(e,["classes","value","min","max","onChange","onMouseUpOrTouchEnd","onMouseUp","onTouchEnd"]);return s.default.createElement("input",c({ref:t,type:"range",value:a,min:n,max:r,onChange:function(e){return i(e,e.target.valueAsNumber)},onMouseUp:function(e){u(e),l&&l(e)},onTouchEnd:function(e){u(e),d&&d(e)},className:o,"aria-valuenow":Number(a),"aria-valuemin":Number(n),"aria-valuemax":Number(r)},p))}),p=s.default.memo(d),f=s.default.forwardRef(function(e,t){var o=e.value,n=e.onChange,r=e.onAfterChange,i=void 0===r?function(){}:r,u=e.disabled,d=void 0!==u&&u,f=e.size,g=e.min,m=void 0===g?0:g,v=e.max,y=void 0===v?100:v,b=e.step,w=e.variant,M=void 0===w?"primary":w,O=e.inputProps,P=e.tooltip,C=void 0===P?"auto":P,x=e.tooltipPlacement,I=void 0===x?"bottom":x,S=e.tooltipLabel,k=e.tooltipStyle,j=e.tooltipProps,E=e.bsPrefix,R=e.className,_=h(e,["value","onChange","onAfterChange","disabled","size","min","max","step","variant","inputProps","tooltip","tooltipPlacement","tooltipLabel","tooltipStyle","tooltipProps","bsPrefix","className"]),D=a.useState(),T=D[0],U=D[1],L=E||"range-slider",A="auto"===C||"on"===C,N=l.default(R,L,f&&L+"--"+f,d&&"disabled",M&&L+"--"+M),z=c(c({},void 0===O?{}:O),_),F=z.onMouseUp,B=z.onTouchEnd,V=h(z,["onMouseUp","onTouchEnd"]),H=a.useCallback(function(e){T!==e.target.value&&i(e,e.target.valueAsNumber),U(e.target.value)},[T,i]),X=s.default.createElement(p,c({},c({disabled:d,value:o,min:m,max:y,ref:t,step:b,classes:N,onMouseUpOrTouchEnd:H,onTouchEnd:B,onMouseUp:F,onChange:void 0===n?function(){}:n},V))),Y=l.default(L+"__wrap",f&&L+"__wrap--"+f),$=l.default(L+"__tooltip",A&&L+"__tooltip--"+C,I&&L+"__tooltip--"+I,d&&L+"__tooltip--disabled"),q="sm"===f?8:"lg"===f?12:10,W=(Number(o)-m)/(y-m);return s.default.createElement("span",{className:Y},X,A&&s.default.createElement("div",c({className:$,style:c(c({},(void 0===k?{}:k)||{}),{left:"calc("+100*W+"% + "+-((W-.5)*2*q)+"px)"})},void 0===j?{}:j),s.default.createElement("div",{className:L+"__tooltip__label"},S?S(Number(o)):o),s.default.createElement("div",{className:L+"__tooltip__caret"})))});f.propTypes={value:u.default.oneOfType([u.default.number,u.default.string]).isRequired,onChange:u.default.func,onAfterChange:u.default.func,min:u.default.number,max:u.default.number,step:u.default.number,disabled:u.default.bool,size:u.default.oneOf(["sm","lg"]),variant:u.default.oneOf(["primary","secondary","success","danger","warning","info","dark","light"]),inputProps:u.default.object,tooltip:u.default.oneOf(["auto","on","off"]),tooltipPlacement:u.default.oneOf(["top","bottom"]),tooltipLabel:u.default.func,tooltipStyle:u.default.object,tooltipProps:u.default.object,className:u.default.string,bsPrefix:u.default.string},e.exports=s.default.memo(f)},93131:function(e,t,o){e.exports=function(e,t){"use strict";function o(e){return e&&"object"==typeof e&&"default"in e?e:{default:e}}var a=o(e),n=o(t);function r(e,t){for(var o=0;o<t.length;o++){var a=t[o];a.enumerable=a.enumerable||!1,a.configurable=!0,"value"in a&&(a.writable=!0),Object.defineProperty(e,a.key,a)}}function i(e,t,o){return t in e?Object.defineProperty(e,t,{value:o,enumerable:!0,configurable:!0,writable:!0}):e[t]=o,e}function s(){return(s=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var o=arguments[t];for(var a in o)Object.prototype.hasOwnProperty.call(o,a)&&(e[a]=o[a])}return e}).apply(this,arguments)}function u(e,t){var o,a=Object.keys(e);return Object.getOwnPropertySymbols&&(o=Object.getOwnPropertySymbols(e),t&&(o=o.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),a.push.apply(a,o)),a}function l(e){for(var t=1;t<arguments.length;t++){var o=null!=arguments[t]?arguments[t]:{};t%2?u(Object(o),!0).forEach(function(t){i(e,t,o[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(o)):u(Object(o)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(o,t))})}return e}function c(e){return(c=Object.setPrototypeOf?Object.getPrototypeOf:function(e){return e.__proto__||Object.getPrototypeOf(e)})(e)}function h(e,t){return(h=Object.setPrototypeOf||function(e,t){return e.__proto__=t,e})(e,t)}function d(e){if(void 0===e)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function p(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){if("undefined"!=typeof Symbol&&Symbol.iterator in Object(e)){var o=[],a=!0,n=!1,r=void 0;try{for(var i,s=e[Symbol.iterator]();!(a=(i=s.next()).done)&&(o.push(i.value),!t||o.length!==t);a=!0);}catch(e){n=!0,r=e}finally{try{a||null==s.return||s.return()}finally{if(n)throw r}}return o}}(e,t)||f(e,t)||function(){throw TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function f(e,t){if(e){if("string"==typeof e)return g(e,t);var o=Object.prototype.toString.call(e).slice(8,-1);return"Object"===o&&e.constructor&&(o=e.constructor.name),"Map"===o||"Set"===o?Array.from(e):"Arguments"===o||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(o)?g(e,t):void 0}}function g(e,t){(null==t||t>e.length)&&(t=e.length);for(var o=0,a=Array(t);o<t;o++)a[o]=e[o];return a}function m(e,t){return new Promise(function(o,a){var n=new Image;n.onload=function(){return o(n)},n.onerror=a,!1==(null!==e&&!!e.match(/^\s*data:([a-z]+\/[a-z]+(;[a-z-]+=[a-z-]+)?)?(;base64)?,[a-z0-9!$&',()*+;=\-._~:@/?%\s]*\s*$/i))&&t&&(n.crossOrigin=t),n.src=e})}var v,y="undefined"!=typeof window&&"undefined"!=typeof navigator&&!!("ontouchstart"in window||0<navigator.msMaxTouchPoints),b="undefined"!=typeof File,w={touch:{react:{down:"onTouchStart",mouseDown:"onMouseDown",drag:"onTouchMove",move:"onTouchMove",mouseMove:"onMouseMove",up:"onTouchEnd",mouseUp:"onMouseUp"},native:{down:"touchstart",mouseDown:"mousedown",drag:"touchmove",move:"touchmove",mouseMove:"mousemove",up:"touchend",mouseUp:"mouseup"}},desktop:{react:{down:"onMouseDown",drag:"onDragOver",move:"onMouseMove",up:"onMouseUp"},native:{down:"mousedown",drag:"dragStart",move:"mousemove",up:"mouseup"}}},M=y?w.touch:w.desktop,O="undefined"!=typeof window&&window.devicePixelRatio?window.devicePixelRatio:1,P={x:.5,y:.5},C=function(){var e=n.default.Component;if("function"!=typeof e&&null!==e)throw TypeError("Super expression must either be null or a function");f.prototype=Object.create(e&&e.prototype,{constructor:{value:f,writable:!0,configurable:!0}}),e&&h(f,e);var t,o,a,u=(t=function(){if("undefined"==typeof Reflect||!Reflect.construct||Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Date.prototype.toString.call(Reflect.construct(Date,[],function(){})),!0}catch(e){return!1}}(),function(){var e,o=c(f);return e=t?Reflect.construct(o,arguments,c(this).constructor):o.apply(this,arguments),e&&("object"==typeof e||"function"==typeof e)?e:d(this)});function f(e){var t;return function(e,t){if(!(e instanceof t))throw TypeError("Cannot call a class as a function")}(this,f),i(d(t=u.call(this,e)),"state",{drag:!1,my:null,mx:null,image:P}),i(d(t),"handleImageReady",function(e){var o=t.getInitialSize(e.width,e.height);o.resource=e,o.x=.5,o.y=.5,o.backgroundColor=t.props.backgroundColor,t.setState({drag:!1,image:o},t.props.onImageReady),t.props.onLoadSuccess(o)}),i(d(t),"clearImage",function(){t.canvas.getContext("2d").clearRect(0,0,t.canvas.width,t.canvas.height),t.setState({image:P})}),i(d(t),"handleMouseDown",function(e){(e=e||window.event).preventDefault(),t.setState({drag:!0,mx:null,my:null})}),i(d(t),"handleMouseUp",function(){t.state.drag&&(t.setState({drag:!1}),t.props.onMouseUp())}),i(d(t),"handleMouseMove",function(e){var o,a,n,r,i,s,u,c,h,d,p,f,g,m,v,y;e=e||window.event,!1!==t.state.drag&&(e.preventDefault(),n={mx:o=e.targetTouches?e.targetTouches[0].pageX:e.clientX,my:a=e.targetTouches?e.targetTouches[0].pageY:e.clientY},y=(y=t.props.rotate%360)<0?y+360:y,t.state.mx&&t.state.my&&(r=t.state.mx-o,i=t.state.my-a,s=t.state.image.width*t.props.scale,u=t.state.image.height*t.props.scale,h=(c=t.getCroppingRect()).x,d=c.y,h*=s,d*=u,f=Math.cos((p=function(e){return Math.PI/180*e})(y)),m=d+-r*(g=Math.sin(p(y)))+i*f,v={x:(h+r*f+i*g)/s+1/t.props.scale*t.getXScale()/2,y:m/u+1/t.props.scale*t.getYScale()/2},t.props.onPositionChange(v),n.image=l(l({},t.state.image),v)),t.setState(n),t.props.onMouseMove(e))}),i(d(t),"setCanvas",function(e){t.canvas=e}),t.canvas=null,t}return o=[{key:"componentDidMount",value:function(){this.props.disableHiDPIScaling&&(O=1);var e,t,o=this.canvas.getContext("2d");this.props.image&&this.loadImage(this.props.image),this.paint(o),document&&(e=!!function(){var e=!1;try{var t=Object.defineProperty({},"passive",{get:function(){e=!0}});window.addEventListener("test",t,t),window.removeEventListener("test",t,t)}catch(t){e=!1}return e}()&&{passive:!1},t=M.native,document.addEventListener(t.move,this.handleMouseMove,e),document.addEventListener(t.up,this.handleMouseUp,e),y&&(document.addEventListener(t.mouseMove,this.handleMouseMove,e),document.addEventListener(t.mouseUp,this.handleMouseUp,e)))}},{key:"componentDidUpdate",value:function(e,t){this.props.image&&this.props.image!==e.image||this.props.width!==e.width||this.props.height!==e.height||this.props.backgroundColor!==e.backgroundColor?this.loadImage(this.props.image):this.props.image||t.image===P||this.clearImage();var o=this.canvas.getContext("2d");o.clearRect(0,0,this.canvas.width,this.canvas.height),this.paint(o),this.paintImage(o,this.state.image,this.props.border),e.image===this.props.image&&e.width===this.props.width&&e.height===this.props.height&&e.position===this.props.position&&e.scale===this.props.scale&&e.rotate===this.props.rotate&&t.my===this.state.my&&t.mx===this.state.mx&&t.image.x===this.state.image.x&&t.image.y===this.state.image.y&&t.backgroundColor===this.state.backgroundColor||this.props.onImageChange()}},{key:"componentWillUnmount",value:function(){var e;document&&(e=M.native,document.removeEventListener(e.move,this.handleMouseMove,!1),document.removeEventListener(e.up,this.handleMouseUp,!1),y&&(document.removeEventListener(e.mouseMove,this.handleMouseMove,!1),document.removeEventListener(e.mouseUp,this.handleMouseUp,!1)))}},{key:"isVertical",value:function(){return!this.props.disableCanvasRotation&&this.props.rotate%180!=0}},{key:"getBorders",value:function(e){var t=0<arguments.length&&void 0!==e?e:this.props.border;return Array.isArray(t)?t:[t,t]}},{key:"getDimensions",value:function(){var e=this.props,t=e.width,o=e.height,a=e.rotate,n=e.border,r={},i=p(this.getBorders(n),2),s=i[0],u=i[1];return this.isVertical()?(r.width=o,r.height=t):(r.width=t,r.height=o),r.width+=2*s,r.height+=2*u,{canvas:r,rotate:a,width:t,height:o,border:n}}},{key:"getImage",value:function(){var e=this.getCroppingRect(),t=this.state.image;e.x*=t.resource.width,e.y*=t.resource.height,e.width*=t.resource.width,e.height*=t.resource.height;var o=document.createElement("canvas");this.isVertical()?(o.width=e.height,o.height=e.width):(o.width=e.width,o.height=e.height);var a=o.getContext("2d");return a.translate(o.width/2,o.height/2),a.rotate(this.props.rotate*Math.PI/180),a.translate(-o.width/2,-o.height/2),this.isVertical()&&a.translate((o.width-o.height)/2,(o.height-o.width)/2),t.backgroundColor&&(a.fillStyle=t.backgroundColor,a.fillRect(-e.x,-e.y,t.resource.width,t.resource.height)),a.drawImage(t.resource,-e.x,-e.y),o}},{key:"getImageScaledToCanvas",value:function(){var e=this.getDimensions(),t=e.width,o=e.height,a=document.createElement("canvas");return this.isVertical()?(a.width=o,a.height=t):(a.width=t,a.height=o),this.paintImage(a.getContext("2d"),this.state.image,0,1),a}},{key:"getXScale",value:function(){return Math.min(1,this.props.width/this.props.height/(this.state.image.width/this.state.image.height))}},{key:"getYScale",value:function(){return Math.min(1,this.props.height/this.props.width/(this.state.image.height/this.state.image.width))}},{key:"getCroppingRect",value:function(){var e=this.props.position||{x:this.state.image.x,y:this.state.image.y},t=1/this.props.scale*this.getXScale(),o=1/this.props.scale*this.getYScale(),a={x:e.x-t/2,y:e.y-o/2,width:t,height:o},n=0,r=1-a.width,i=0,s=1-a.height;return(this.props.disableBoundaryChecks||1<t||1<o)&&(n=-a.width,i=-a.height,s=r=1),l(l({},a),{},{x:Math.max(n,Math.min(a.x,r)),y:Math.max(i,Math.min(a.y,s))})}},{key:"loadImage",value:function(e){b&&e instanceof File?this.loadingImage=new Promise(function(t,o){var a=new FileReader;a.onload=function(e){try{var a=m(e.target.result);t(a)}catch(e){o(e)}},a.readAsDataURL(e)}).then(this.handleImageReady).catch(this.props.onLoadFailure):"string"==typeof e&&(this.loadingImage=m(e,this.props.crossOrigin).then(this.handleImageReady).catch(this.props.onLoadFailure))}},{key:"getInitialSize",value:function(e,t){var o,a,n=this.getDimensions();return t/e<n.height/n.width?a=e*((o=this.getDimensions().height)/t):o=t*((a=this.getDimensions().width)/e),{height:o,width:a}}},{key:"paintImage",value:function(e,t,o,a){var n,r=3<arguments.length&&void 0!==a?a:O;t.resource&&(n=this.calculatePosition(t,o),e.save(),e.translate(e.canvas.width/2,e.canvas.height/2),e.rotate(this.props.rotate*Math.PI/180),e.translate(-e.canvas.width/2,-e.canvas.height/2),this.isVertical()&&e.translate((e.canvas.width-e.canvas.height)/2,(e.canvas.height-e.canvas.width)/2),e.scale(r,r),e.globalCompositeOperation="destination-over",e.drawImage(t.resource,n.x,n.y,n.width,n.height),t.backgroundColor&&(e.fillStyle=t.backgroundColor,e.fillRect(n.x,n.y,n.width,n.height)),e.restore())}},{key:"calculatePosition",value:function(e,t){e=e||this.state.image;var o=p(this.getBorders(t),2),a=o[0],n=o[1],r=this.getCroppingRect(),i=e.width*this.props.scale,s=e.height*this.props.scale,u=-r.x*i,l=-r.y*s;return this.isVertical()?(u+=n,l+=a):(u+=a,l+=n),{x:u,y:l,height:s,width:i}}},{key:"paint",value:function(e){e.save(),e.scale(O,O),e.translate(0,0),e.fillStyle="rgba("+this.props.color.slice(0,4).join(",")+")";var t,o,a,n,r,i=this.props.borderRadius,s=this.getDimensions(),u=p(this.getBorders(s.border),2),l=u[0],c=u[1],h=s.canvas.height,d=s.canvas.width,i=Math.max(i,0);i=Math.min(i,d/2-l,h/2-c),e.beginPath(),t=d-2*l,o=h-2*c,0===(a=i)?e.rect(l,c,t,o):(n=t-a,r=o-a,e.translate(l,c),e.arc(a,a,a,Math.PI,1.5*Math.PI),e.lineTo(n,0),e.arc(n,a,a,1.5*Math.PI,2*Math.PI),e.lineTo(t,r),e.arc(n,r,a,2*Math.PI,.5*Math.PI),e.lineTo(a,o),e.arc(a,r,a,.5*Math.PI,Math.PI),e.translate(-l,-c)),e.rect(d,0,-d,h),e.fill("evenodd"),e.restore()}},{key:"render",value:function(){var e=this.props,t=(e.scale,e.rotate,e.image,e.border,e.borderRadius,e.width,e.height,e.position,e.color,e.backgroundColor,e.style),o=(e.crossOrigin,e.onLoadFailure,e.onLoadSuccess,e.onImageReady,e.onImageChange,e.onMouseUp,e.onMouseMove,e.onPositionChange,e.disableBoundaryChecks,e.disableHiDPIScaling,e.disableCanvasRotation,function(e,t){if(null==e)return{};var o,a=function(e,t){if(null==e)return{};for(var o,a={},n=Object.keys(e),r=0;r<n.length;r++)o=n[r],0<=t.indexOf(o)||(a[o]=e[o]);return a}(e,t);if(Object.getOwnPropertySymbols)for(var n=Object.getOwnPropertySymbols(e),r=0;r<n.length;r++)o=n[r],0<=t.indexOf(o)||Object.prototype.propertyIsEnumerable.call(e,o)&&(a[o]=e[o]);return a}(e,["scale","rotate","image","border","borderRadius","width","height","position","color","backgroundColor","style","crossOrigin","onLoadFailure","onLoadSuccess","onImageReady","onImageChange","onMouseUp","onMouseMove","onPositionChange","disableBoundaryChecks","disableHiDPIScaling","disableCanvasRotation"])),a=this.getDimensions(),r={width:a.canvas.width,height:a.canvas.height,cursor:this.state.drag?"grabbing":"grab",touchAction:"none"},i={width:a.canvas.width*O,height:a.canvas.height*O,style:l(l({},r),t)};return i[M.react.down]=this.handleMouseDown,y&&(i[M.react.mouseDown]=this.handleMouseDown),n.default.createElement("canvas",s({ref:this.setCanvas},i,o))}}],r(f.prototype,o),a&&r(f,a),f}();return i(C,"propTypes",{scale:a.default.number,rotate:a.default.number,image:a.default.oneOfType([a.default.string].concat(function(e){if(Array.isArray(e))return g(e)}(v=b?[a.default.instanceOf(File)]:[])||function(e){if("undefined"!=typeof Symbol&&Symbol.iterator in Object(e))return Array.from(e)}(v)||f(v)||function(){throw TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}())),border:a.default.oneOfType([a.default.number,a.default.arrayOf(a.default.number)]),borderRadius:a.default.number,width:a.default.number,height:a.default.number,position:a.default.shape({x:a.default.number,y:a.default.number}),color:a.default.arrayOf(a.default.number),backgroundColor:a.default.string,crossOrigin:a.default.oneOf(["","anonymous","use-credentials"]),onLoadFailure:a.default.func,onLoadSuccess:a.default.func,onImageReady:a.default.func,onImageChange:a.default.func,onMouseUp:a.default.func,onMouseMove:a.default.func,onPositionChange:a.default.func,disableBoundaryChecks:a.default.bool,disableHiDPIScaling:a.default.bool,disableCanvasRotation:a.default.bool}),i(C,"defaultProps",{scale:1,rotate:0,border:25,borderRadius:0,width:200,height:200,color:[0,0,0,.5],onLoadFailure:function(){},onLoadSuccess:function(){},onImageReady:function(){},onImageChange:function(){},onMouseUp:function(){},onMouseMove:function(){},onPositionChange:function(){},disableBoundaryChecks:!1,disableHiDPIScaling:!1,disableCanvasRotation:!0}),C}(o(95062),o(14232))}}]);
//# sourceMappingURL=5266-a70ace75115de296.js.map