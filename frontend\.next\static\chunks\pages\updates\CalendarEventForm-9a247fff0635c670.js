(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[1107],{2582:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>u});var s=a(37876),d=a(14232),l=a(49589),r=a(37784),c=a(56970),n=a(29504),i=a(31427),o=a(12613),h=a(31753);let u=e=>{let[,t]=(0,d.useState)(!1),{validation:a,onChangeDate:u,startDate:m,endDate:x}=e,{t:p}=(0,h.Bd)("common");(0,d.useEffect)(()=>{null==x?t(!1):t(!0)},[x]);let j=t=>{e.getId(t)},A=t=>{e.getSourceCollection(t)};return(0,s.jsxs)(l.A,{className:"formCard",fluid:!0,children:[(0,s.jsx)(r.A,{className:"header-block",lg:12,children:(0,s.jsx)("h6",{children:(0,s.jsx)("span",{children:p("update.Date")})})}),(0,s.jsxs)(c.A,{children:[(0,s.jsxs)(r.A,{children:[(0,s.jsxs)(n.A.Group,{children:[(0,s.jsx)(n.A.Label,{className:"d-block required-field",children:p("update.StartDate")}),(0,s.jsx)(o.A,{selected:m,minDate:new Date,showTimeSelect:!0,timeIntervals:15,onChange:e=>u(e,"startDate"),placeholderText:p("update.Selectadate"),dateFormat:"MMMM d, yyyy h:mm aa"})]}),!0===a.startDate&&!m&&(0,s.jsx)("p",{style:{color:"red"},children:p("update.Pleaseenterthestartdate")})]}),(0,s.jsxs)(r.A,{children:[(0,s.jsxs)(n.A.Group,{children:[(0,s.jsx)(n.A.Label,{className:"d-block required-field",children:p("update.EndDate")}),(0,s.jsx)(o.A,{selected:x,showTimeSelect:!0,timeIntervals:15,onChange:e=>u(e,"endDate"),placeholderText:p("update.Selectadate"),minDate:m,dateFormat:"MMMM d, yyyy h:mm aa"})]}),!0===a.startDate&&!x&&(0,s.jsxs)("p",{style:{color:"red"},children:[p("update.Pleaseentertheenddate")," "]})]})]}),(0,s.jsx)(i.default,{data:e.data,srcText:e.imgSrc,getId:e=>j(e),getSourceCollection:e=>A(e)})]})}},12613:(e,t,a)=>{"use strict";a.d(t,{A:()=>l});var s=a(37876);a(14232);var d=a(56856);let l=e=>(0,s.jsx)(d.Ay,{...e})},74768:(e,t,a)=>{(window.__NEXT_P=window.__NEXT_P||[]).push(["/updates/CalendarEventForm",function(){return a(2582)}])}},e=>{var t=t=>e(e.s=t);e.O(0,[7725,1121,6701,1772,698,7336,8220,1427,636,6593,8792],()=>t(74768)),_N_E=e.O()}]);
//# sourceMappingURL=CalendarEventForm-9a247fff0635c670.js.map