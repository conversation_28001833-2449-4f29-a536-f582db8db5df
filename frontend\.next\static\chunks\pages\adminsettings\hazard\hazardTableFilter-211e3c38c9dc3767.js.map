{"version": 3, "file": "static/chunks/pages/adminsettings/hazard/hazardTableFilter-211e3c38c9dc3767.js", "mappings": "gMA4BA,MAtB0B,OAAC,YAAEA,CAAU,CAAEC,OAsB1BC,GAtBkC,SAAEC,CAAO,CAAM,GACxD,EAqByB,CArBvBC,CAAC,CAAE,CAAGC,CAAAA,EAAAA,EAAAA,EAAAA,CAAcA,CAAC,UAG7B,MACE,UAACC,EAAAA,CAASA,CAAAA,CAACC,KAAK,IAACC,UAAU,eACzB,UAACC,EAAAA,CAAGA,CAAAA,UACF,UAACC,EAAAA,CAAGA,CAAAA,CAACC,GAAI,EAAGH,UAAU,eACpB,UAACI,EAAAA,CAAWA,CAAAA,CACVC,KAAK,OACLL,UAAU,cACVM,YAAaV,EAAE,8BACfW,aAAW,SACXC,MAAOhB,EACPiB,SAAUhB,SAMtB,mBCzBA,4CACA,0CACA,WACA,OAAe,EAAQ,IAA+D,CACtF,EACA,UAFsB", "sources": ["webpack://_N_E/./pages/adminsettings/hazard/hazardTableFilter.tsx", "webpack://_N_E/?da26"], "sourcesContent": ["//Import Library\r\nimport {Col, Container, FormControl, Row} from \"react-bootstrap\";\r\n\r\n//Import services/components\r\nimport { useTranslation } from 'next-i18next';\r\n\r\nconst HazardTableFilter = ({ filterText, onFilter ,onClear}: any) => {\r\n  const { t } = useTranslation('common');\r\n\r\n\r\n  return (\r\n    <Container fluid className=\"p-0\">\r\n      <Row>\r\n        <Col md={4} className=\"p-0\">\r\n          <FormControl\r\n            type=\"text\"\r\n            className=\"searchInput\"\r\n            placeholder={t(\"adminsetting.hazard.Search\")}\r\n            aria-label=\"Search\"\r\n            value={filterText}\r\n            onChange={onFilter}\r\n          />\r\n        </Col>\r\n      </Row>\r\n    </Container>\r\n  )\r\n};\r\n\r\nexport default HazardTableFilter;\r\n", "\n    (window.__NEXT_P = window.__NEXT_P || []).push([\n      \"/adminsettings/hazard/hazardTableFilter\",\n      function () {\n        return require(\"private-next-pages/adminsettings/hazard/hazardTableFilter.tsx\");\n      }\n    ]);\n    if(module.hot) {\n      module.hot.dispose(function () {\n        window.__NEXT_P.push([\"/adminsettings/hazard/hazardTableFilter\"])\n      });\n    }\n  "], "names": ["filterText", "onFilter", "HazardTableFilter", "onClear", "t", "useTranslation", "Container", "fluid", "className", "Row", "Col", "md", "FormControl", "type", "placeholder", "aria-label", "value", "onChange"], "sourceRoot": "", "ignoreList": []}