//Import Library
import React from "react";
import { Row, Col, OverlayTrigger, Popover } from "react-bootstrap";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import { faInfoCircle } from "@fortawesome/free-solid-svg-icons";

//Import services/components
import FileIcons from './FileIcons';


interface DocumentProps {
  document: Array<{
    _id: string;
    original_name: string;
  }>;
  doc_src?: string[];
}

//TOTO refactor
const Document = (props: DocumentProps): React.ReactElement => {
  const { document, doc_src } = props;

  return (
    <div>
      {
        document && document.map((item: any, index: number) => {
          return (
            <Row key={index}>
              <FileIcons {...item} />
              <Col>
                <div className="d-flex">
                  <a href={`${process.env.API_SERVER}/files/download/${item._id}`} target="_blank">
                    <p>{item.original_name}</p>
                  </a>
                  <OverlayTrigger trigger="click" placement="right" overlay={<Popover id="popover-basic">
                    <Popover.Header as="h5">Source</Popover.Header>
                    <Popover.Body>
                      {doc_src && doc_src[index] || document[index].original_name }
                    </Popover.Body>
                  </Popover>}>
                    <FontAwesomeIcon className="ms-1" icon={faInfoCircle} color="#232c3d" />
                  </OverlayTrigger>


                </div>
              </Col>
            </Row>
          );
        })
      }
    </div>
  )

}

export default Document;
