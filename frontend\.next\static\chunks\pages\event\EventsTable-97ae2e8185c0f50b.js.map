{"version": 3, "file": "static/chunks/pages/event/EventsTable-97ae2e8185c0f50b.js", "mappings": "qNAqFA,MAnEqB,OAAC,YACpBA,CAAU,QAkEGC,EAjEbC,CAAQ,SACRC,CAAO,CAgEmB,qBA/D1BC,CAAoB,cACpBC,CAAY,CAOb,GACO,CAACC,EAAYC,EAAc,CAAGC,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAC,EAAE,EACzC,GAAEC,CAAC,CAAE,CAAGC,CAAAA,EAAAA,EAAAA,EAAAA,CAAcA,CAAC,UAIvBC,EAAc,MAAOC,IACzB,IAAMC,EAAW,MAAMC,EAAAA,CAAUA,CAACC,GAAG,CAAC,cAAeH,EAEjDC,IAAYG,MAAMC,OAAO,CAACJ,EAASK,IAAI,GACzCX,EAAcM,EAASK,IAAI,CAE/B,EAMA,MAJAC,CAAAA,EAAAA,EAAAA,SAAAA,CAASA,CAAC,KACRR,EAAY,CAAES,MAAO,CAAC,EAAGC,KAAM,CAAEC,MAAO,KAAM,CAAE,EAClD,EAAG,EAAE,EAGH,UAACC,EAAAA,CAASA,CAAAA,CAACC,KAAK,IAACC,UAAU,eACzB,WAACC,EAAAA,CAAGA,CAAAA,WACF,UAACC,EAAAA,CAAGA,CAAAA,CAACC,GAAI,EAAGH,UAAU,eACpB,UAACI,EAAAA,CAAWA,CAAAA,CACVC,KAAK,OACLL,UAAU,cACVM,YAActB,EAAE,uBAChBuB,aAAW,SACXC,MAAOjC,EACPkC,SAAUhC,MAId,UAACyB,EAAAA,CAAGA,CAAAA,CAACC,GAAI,WACP,WAACC,EAAAA,CAAWA,CAAAA,CACVM,GAAG,SACHH,aAAW,aACXI,mBAAiB,cACjBF,SAAU9B,EACV6B,MAAO5B,YAEP,UAACgC,SAAAA,CAAOJ,MAAO,YAAKxB,EAAE,mCACrBH,EAAWgC,GAAG,CAAC,CAACC,EAAWC,IAExB,UAACH,SAAAA,CAAmBJ,MAAOM,EAAKE,GAAG,UAChCF,EAAKjB,KAAK,EADAkB,aAU7B,6GC9CA,SAASE,EAASC,CAAoB,EACpC,GAAM,CAAElC,CAAC,CAAE,CAAGC,CAAAA,EAAAA,EAAAA,EAAAA,CAAcA,CAAC,UACvBkC,EAA6B,CACjCC,gBAAiBpC,EAAE,cACnB,EACI,SACJqC,CAAO,MACP5B,CAAI,WACJ6B,CAAS,uBACTC,CAAqB,CACrBC,WAAS,oBACTC,CAAkB,qBAClBC,CAAmB,kBACnBC,CAAgB,aAChBC,CAAW,oBACXC,CAAkB,gBAClBC,CAAc,SACdC,CAAO,CACPC,WAAS,CACTC,sBAAoB,mBACpBC,CAAiB,YACjBC,CAAU,QACVC,CAAM,kBACNC,CAAgB,cAChBC,CAAY,CAEZ,CADA,EACGC,EACJ,CAAGrB,EAGEsB,EAAiB,CACrBrB,6BACAsB,gBAAiBzD,EAAE,IAP0C,MAQ7D0D,UAAU,UACVrB,EACA5B,KAAMA,GAAQ,EAAE,CAChBkD,OAAO,EACPC,2BAA4BrB,EAC5BsB,UAAWrB,EACXsB,gBAAiBf,qBACjBN,EACAsB,YAAY,EACZC,iBAAkBhB,EAClBiB,kBAAmBpB,GAA0C,GAC7DqB,eADwCrB,cACVD,GAA4B,CAAC,GAAI,GAAI,GAAI,CAA3BA,EAA+B,GAAG,CAC9EuB,oBAAqB7B,EACrB8B,oBAAqB1B,EACrB2B,aAAc1B,iBACdG,uBACAG,oBACAC,EACAoB,kBAAmB,UAACC,EAAAA,CAAYA,CAAAA,CAAAA,GAChCC,SAAU,UAACC,IAAAA,CAAEzD,UAAU,kCACvBmC,oBACAC,eACAE,mBACAD,EACArC,UAAW,WACb,EACA,MACE,UAAC0D,EAAAA,EAASA,CAAAA,CAAE,GAAGlB,CAAc,EAEjC,CAEAvB,EAAS0C,YAAY,CAAG,CACtBd,WAAW,EACXE,YAAY,EACZzB,UAAW,KACXU,WAAW,EACXC,qBAAsB,KACtBC,mBAAmB,EACnBC,YAAY,EACZE,kBAAkB,CACpB,EAEA,MAAepB,QAAQA,EAAC,SC/GxB,4CACA,qBACA,WACA,OAAe,EAAQ,KAA0C,CACjE,EACA,SAFsB,gMCStB,IAAM2C,EAAU,IACd,GAAM,CAAEC,MAAI,CAAE,CAAG5E,CAAAA,EAAAA,EAAAA,EAAAA,CAAcA,CAAC,UAC1B6E,EAAgC,OAAlBD,EAAKE,QAAQ,CAAY,KAAOF,EAAKE,QAAQ,CAC3D,SAAEC,CAAO,CAAE,CAAG9C,EACpB,MACE,UAAC+C,KAAAA,UACED,EAAQnD,GAAG,CAAC,CAACC,EAAWC,IAEdD,GAAQA,EAAKE,GAAG,EAAIF,EAAKjB,KAAK,EAAIiB,EAAKjB,KAAK,CAACiE,EAAY,CAC9D,UAACI,KAAAA,UACC,UAACC,IAAIA,CAACC,KAAK,sBAAsB1D,GAAI,cAAhCyD,EAAyD,OAATrD,EAAKE,GAAG,WAC1DF,EAAKjB,KAAK,CAACiE,EAAY,CAACO,QAAQ,MAF5BtD,GAMT,KAMZ,EAgRA,EA9QA,SAASuD,CAAsB,EAC7B,IAAMC,EAASC,CAAAA,EAAAA,EAAAA,KA6QFF,IA7QEE,CAASA,GAClB,GAAExF,CAAC,CAAE,CAAGC,CAAAA,EAAAA,EAAAA,EAAAA,CAAcA,CAAC,UACvB,WAAEwF,CAAS,iBAAEC,CAAe,CAAE,CAAGxD,EACjC,CAAC3C,EAAYoG,EAAc,CAAGC,EAAAA,QAAc,CAAC,IAC7C,CAAChG,EAAciG,EAAgB,CAAGD,EAAAA,QAAc,CAAC,IACjD,CAACrD,EAAuBuD,EAAyB,CACrDF,EAAAA,QAAc,EAAC,GACX,CAACG,EAAWC,EAAe,CAAGjG,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAC,EAAE,EACzC,CAACgD,EAASkD,EAAW,CAAGlG,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,EAAC,GACjC,CAACuC,EAAW4D,EAAa,CAAGnG,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAC,GACrC,CAACoG,EAASC,EAAW,CAAGrG,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAC,IACjC,CAACsG,EAASC,EAAW,CAAGvG,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAC,GACjC,CAACwG,EAAUC,EAAY,CAAGzG,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAM,MAExC0G,EAAmB,CACvB7F,KAAM,CAAE8F,WAAY,MAAO,EAC3BC,MAAM,EACNC,SAAU,CACR,CAAEC,KAAM,UAAWC,OAAQ,mBAAoB,EAC/C,CAAED,KAAM,cAAeC,OAAQ,OAAQ,EACvC,CAAED,KAAM,SAAUC,OAAQ,OAAQ,EACnC,CACDC,MAAOZ,EACPa,KAAM,EACNrG,MAAO,CAAC,EACRmG,OACE,6MACJ,EAEM,CAACG,EAAUC,EAAY,CAAGnH,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAC0G,GAEnCpE,EAAU,CACd,CACE8E,KAAMnH,EAAE,wBACRoH,SAAU,QACVC,UAAU,EACVC,MAAO,MACPC,KAAM,GACJ,UAACpC,IAAIA,CAACC,KAAK,qBAAqB1D,GAAI,eAA/ByD,MAAoD,CAANqC,EAAExF,GAAG,WACrDwF,EAAE3G,KAAK,EAGd,EACA,CACEsG,KAAMnH,EAAE,wBACRoH,SAAU,UACVC,SAAU,GACVE,KAAM,GACJC,EAAEC,OAAO,EAAID,EAAEC,OAAO,CAAC5G,KAAK,CAC1B,UAACsE,IAAIA,CACHC,KAAK,uBACL1D,GAAI,aAFDyD,IAEgC,OAAdqC,EAAEC,OAAO,CAACzF,GAAG,WAEjCwF,EAAEC,OAAO,CAAC5G,KAAK,GAGlB,EAEN,EACA,CACEsG,KAAMnH,EAAE,2BACRoH,SAAU,cACVC,UAAU,EACVE,KAAM,GACJC,EAAEE,WAAW,EAAIF,EAAEE,WAAW,CAAC7G,KAAK,CAAG2G,EAAEE,WAAW,CAAC7G,KAAK,CAAG,EACjE,EACA,CACEsG,KAAMnH,EAAE,uBACRoH,SAAU,SACVG,KAAM,GAAY,UAAC3C,EAAAA,CAAQI,QAASwC,EAAEG,MAAM,EAC9C,EACA,CACER,KAAMnH,EAAE,+BACRoH,SAAU,aACVC,UAAU,EACVE,KAAM,GAAYK,IAAOJ,EAAEK,UAAU,EAAEC,MAAM,CAAC,QAChD,EACA,CACEX,KAAMnH,EAAE,EAHgB4H,0BAIxBR,SAAU,aACVC,SAAU,GACVE,KAAM,GAAYK,IAAOJ,EAAEO,UAAU,EAAED,MAAM,CAAC,QAChD,EACD,CAEKE,EAAgB,MAAOC,CAJDL,GAK1B3B,GAAW,GAGPV,EAAO5E,KAAK,EAAI4E,EAAO5E,KAAK,CAAC8G,OAAO,EAAE,CACxCQ,EAAgBtH,KAAK,CAAC,OAAU,CAAG4E,EAAO5E,KAAK,CAAC8G,OAAAA,EAI1B,MAAM,CAA1B/B,EAEF,OAAOuC,EAAgBtH,KAAK,CAAC,YAAe,CACR,GAAG,CAA9B+E,EAAgBwC,MAAM,CAE/BD,EAAgBtH,KAAK,CAAC,YAAe,CAAG,CAAC,eAAe,CAGxDsH,EAAgBtH,KAAK,CAAC,YAAe,CAAG+E,EAI1C,IAAMtF,EAAW,MAAMC,EAAAA,CAAUA,CAACC,GAAG,CAAC,SAAU2H,GAC5C7H,GAAYG,MAAMC,OAAO,CAACJ,EAASK,IAAI,GAAG,CAC5CuF,EAAe5F,EAASK,IAAI,EAC5BgF,EAAUrF,EAASK,IAAI,EACvByF,EAAa9F,EAAS+H,UAAU,GAGlClC,GAAW,EACb,EAoBMvD,EAAsB,MAAO0F,EAAiBpB,KAClDP,EAAYM,KAAK,CAAGqB,EACpB3B,EAAYO,IAAI,CAAGA,EACnBf,GAAW,GAGPV,EAAO5E,KAAK,EAAI4E,EAAO5E,KAAK,CAAC8G,OAAO,EAAE,GAC5B9G,KAAK,CAAC,OAAU,CAAG4E,EAAO5E,KAAK,CAAC8G,OAAAA,EAItB,MAAM,CAA1B/B,EACF,OAAOe,EAAY9F,KAAK,CAAC,YAAe,CACJ,GAAG,CAA9B+E,EAAgBwC,MAAM,CAC/BzB,EAAY9F,KAAK,CAAC,YAAe,CAAG,CAAC,eAAe,CAEpD8F,EAAY9F,KAAK,CAAC,YAAe,CAAG+E,EAItC9F,IACG6G,EAAY9F,KAAK,CAAG,CAAE,GAAtB8F,EAAqC9F,KAAK,CAAE+G,YAAa9H,EAAa,EAGzE2G,IAAaE,EAAY7F,IAAI,CAAG2F,CAAnBE,CAA4B7F,IAAAA,EAGzC,IAAMR,EAAW,MAAMC,EAAAA,CAAUA,CAACC,GAAG,CAAC,SAAUmG,GAC5CrG,GAAYG,MAAMC,OAAO,CAACJ,EAASK,IAAI,GAAG,CAC5CuF,EAAe5F,EAASK,IAAI,EAC5BgF,EAAUrF,EAASK,IAAI,EACvB2F,EAAWgC,GACXnC,GAAW,IAGbK,EAAWU,EACb,EAGAtG,CAAAA,EAAAA,EAAAA,SAAAA,CAASA,CAAC,KACRuG,EAASD,IAAI,CAAG,EAChBgB,EAAcf,EAChB,EAAG,CAACvB,EAAiBH,EAAO,EAE5B7E,CAAAA,EAAAA,EAAAA,SAAAA,CAASA,CAAC,KACRsH,EAAcf,EAChB,EAAG,CAACA,EAAS,EAGb,IAAMoB,EAAa,MAAOC,EAAaC,KACrCtC,GAAW,GACXQ,EAAY7F,IAAI,CAAG,CACjB,CAAC0H,EAAOlB,QAAQ,CAAC,CAAEmB,CACrB,EACA3I,IACG6G,EAAY9F,KAAK,CAAG,CAAE,GAAtB8F,EAAqC9F,KAAK,CAAE+G,YAAa9H,EAAa,EAC1D,KAAfL,CACGkH,GAAAA,EAAY9F,KAAK,CAAG,CAAE,GAAG8F,EAAY9F,KAAK,CAAEE,MAAOtB,EAAW,EAEjE,MAAMyI,EAAcvB,GACpBD,EAAYC,GACZR,GAAW,EACb,EAEMuC,EAAY,CAACC,EAAQzB,KACrByB,GAAG,EACI9H,KAAK,CAAC,KAAQ,CAAG8H,EAC1BxB,EAASD,IAAI,CAAGA,GAGhB,OAAOC,EAAStG,KAAK,CAACE,KAAK,CAC3BqG,EAAY,CAAE,GAAGD,CAAQ,EAE7B,EAEMyB,EAAoBC,CAAAA,EAAAA,EAAAA,MAAAA,CAAMA,CAC9BC,IAAAA,QAAU,CACR,CAACH,EAAGzB,IAASwB,EAAUC,EAAGzB,GAC1B6B,OAAOC,KAAgC,GAAK,MAE9CC,OAAO,CAEHC,EAAyBpD,EAAAA,OAAa,CAAC,KAQ3C,IAAMqD,EAAyB,IAC7BpD,EAAgBhG,GACZA,EACFoH,EAAStG,KAAK,CAAC,EADD,SACe,CAAGd,EAGhC,OAAOoH,EAAStG,KAAK,CAAC+G,WAAW,CACjCR,EAAY,CAAE,GAAGD,CAAQ,EAE7B,EAOA,MACE,UAACzH,EAAAA,OAAYA,CAAAA,CACXC,SAPiB,CAOPyJ,GANZvD,EAAcwD,EAAEC,MAAM,CAAC5H,KAAK,EAC5BkH,EAAkBS,EAAEC,MAAM,CAAC5H,KAAK,CAAE6E,EACpC,EAKI1G,qBAAsB,GAAYsJ,EAAuBE,EAAEC,MAAM,CAAC5H,KAAK,EACvE9B,QA3BgB,CA2BP2J,IA1BP9J,IACFuG,EAAyB,CAACvD,GAC1BoD,EAFc,IAIlB,EAuBIpG,WAAYA,EACZK,aAAcA,GAGpB,EAAG,CAACL,EAAYgD,EAAuB3C,EAAc8F,EAAgB,EAErE,MACE,UAACzD,EAAAA,CAAQA,CAAAA,CACPI,QAASA,EACT5B,KAAMsF,EACNzD,UAAWA,EACXS,QAASA,EACTP,SAAS,IACTa,gBAAgB,IAChBD,OAAQiF,EACRlF,UAAU,IACVH,WAAW,EACXP,mBAAoBuG,EACpBtG,oBAAqBA,EACrBC,iBAnJqB,CAmJHA,GAlJpB8D,EAAYM,KAAK,CAAGZ,EACpBM,EAAYO,IAAI,CAAGA,EAGnBpH,IACG6G,EAAY9F,KAAK,CAAG,CAAE,GAAtB8F,EAAqC9F,KAAK,CAAE+G,YAAa9H,EAAa,EAGzE2G,IAAaE,EAAY7F,IAAI,CAAG2F,CAAnBE,CAA4B7F,IAAAA,EAGzCoH,EAAcvB,GACdH,EAAWU,EACb,GAwIF", "sources": ["webpack://_N_E/./pages/event/EventsTableFilter.tsx", "webpack://_N_E/./components/common/RKITable.tsx", "webpack://_N_E/?4b49", "webpack://_N_E/./pages/event/EventsTable.tsx"], "sourcesContent": ["//Import Library\r\nimport { useEffect, useState } from \"react\";\r\nimport _ from \"lodash\";\r\nimport {\r\n  Col,\r\n  Container,\r\n  FormControl,\r\n  FormGroup,\r\n  FormLabel,\r\n  Row,\r\n} from \"react-bootstrap\";\r\n\r\n//Import services/components\r\nimport apiService from \"../../services/apiService\";\r\nimport React from \"react\";\r\nimport { useTranslation } from 'next-i18next';\r\n\r\n\r\nconst EventsFilter = ({\r\n  filterText,\r\n  onFilter,\r\n  onClear,\r\n  onFilterHazardChange,\r\n  filterHazard,\r\n}: {\r\n  filterText: any,\r\n  onFilter: any,\r\n  onClear: any,\r\n  onFilterHazardChange: any,\r\n  filterHazard: any,\r\n}) => {\r\n  const [hazardType, setHazardType] = useState([]);\r\n  const { t } = useTranslation('common');\r\n\r\n\r\n\r\n  const getNetworks = async (params: any) => {\r\n    const response = await apiService.get(\"/hazardtype\", params);\r\n\r\n    if (response && Array.isArray(response.data)) {\r\n      setHazardType(response.data);\r\n    }\r\n  };\r\n\r\n  useEffect(() => {\r\n    getNetworks({ query: {}, sort: { title: \"asc\" } });\r\n  }, []);\r\n\r\n  return (\r\n    <Container fluid className=\"p-0\">\r\n      <Row>\r\n        <Col xs={6} className=\"p-0\">\r\n          <FormControl\r\n            type=\"text\"\r\n            className=\"searchInput\"\r\n            placeholder= {t(\"Events.table.Search\")}\r\n            aria-label=\"Search\"\r\n            value={filterText}\r\n            onChange={onFilter}\r\n          />\r\n        </Col>\r\n\r\n        <Col xs={6}>\r\n          <FormControl\r\n            as=\"select\"\r\n            aria-label=\"HazardType\"\r\n            aria-placeholder=\"Hazard Type\"\r\n            onChange={onFilterHazardChange}\r\n            value={filterHazard}\r\n          >\r\n            <option value={\"\"}>{t(\"Events.forms.SelectHazardType\")}</option>\r\n            {hazardType.map((item: any, index) => {\r\n              return (\r\n                <option key={index} value={item._id}>\r\n                  {item.title}\r\n                </option>\r\n              );\r\n            })}\r\n          </FormControl>\r\n        </Col>\r\n      </Row>\r\n    </Container>\r\n  );\r\n};\r\n\r\nexport default EventsFilter;\r\n", "//Import Library\r\nimport React from 'react'\r\nimport DataTable  from 'react-data-table-component';\r\n\r\n//Import services/components\r\nimport { useTranslation } from 'next-i18next';\r\nimport CustomLoader from './CustomLoader';\r\n\r\ninterface RKITableProps {\r\n  columns: any[];\r\n  data: any[];\r\n  totalRows?: number;\r\n  resetPaginationToggle?: boolean;\r\n  subheader?: boolean;\r\n  subHeaderComponent?: React.ReactNode;\r\n  handlePerRowsChange?: (newPerPage: number, page: number) => void;\r\n  handlePageChange?: (page: number) => void;\r\n  rowsPerPage?: number[];\r\n  defaultRowsPerPage?: number;\r\n  selectableRows?: boolean;\r\n  loading?: boolean;\r\n  pagServer?: boolean;\r\n  onSelectedRowsChange?: (selectedRows: any) => void;\r\n  clearSelectedRows?: boolean;\r\n  sortServer?: boolean;\r\n  onSort?: (column: any, sortDirection: string) => void;\r\n  persistTableHead?: boolean;\r\n  sortFunction?: (rows: any[], field: any, direction: 'asc' | 'desc') => any[];\r\n  noHeader?: boolean;\r\n  dense?: boolean;\r\n  pagination?: boolean;\r\n  paginationServer?: boolean;\r\n  paginationTotalRows?: number;\r\n  subHeaderAlign?: string;\r\n  [key: string]: any;\r\n}\r\n\r\nfunction RKITable(props: RKITableProps) {\r\n  const { t } = useTranslation('common');\r\n  const paginationComponentOptions = {\r\n    rowsPerPageText: t('Rowsperpage'),\r\n    };\r\n  const {\r\n    columns,\r\n    data,\r\n    totalRows,\r\n    resetPaginationToggle,\r\n    subheader,\r\n    subHeaderComponent,\r\n    handlePerRowsChange,\r\n    handlePageChange,\r\n    rowsPerPage,\r\n    defaultRowsPerPage,\r\n    selectableRows,\r\n    loading,\r\n    pagServer,\r\n    onSelectedRowsChange,\r\n    clearSelectedRows,\r\n    sortServer,\r\n    onSort,\r\n    persistTableHead,\r\n    sortFunction,\r\n    // Filter out any unknown props that might cause DOM warnings\r\n    ...otherProps\r\n  } = props;\r\n\r\n  // Only pass known DataTable props to avoid DOM warnings\r\n  const dataTableProps = {\r\n    paginationComponentOptions,\r\n    noDataComponent: t(\"NoData\"),\r\n    noHeader: true,\r\n    columns,\r\n    data: data || [],\r\n    dense: true,\r\n    paginationResetDefaultPage: resetPaginationToggle,\r\n    subHeader: subheader,\r\n    progressPending: loading,\r\n    subHeaderComponent,\r\n    pagination: true,\r\n    paginationServer: pagServer,\r\n    paginationPerPage: defaultRowsPerPage ? defaultRowsPerPage : 10,\r\n    paginationRowsPerPageOptions: rowsPerPage ? rowsPerPage : [10, 15, 20, 25, 30],\r\n    paginationTotalRows: totalRows,\r\n    onChangeRowsPerPage: handlePerRowsChange,\r\n    onChangePage: handlePageChange,\r\n    selectableRows,\r\n    onSelectedRowsChange,\r\n    clearSelectedRows,\r\n    progressComponent: <CustomLoader/>,\r\n    sortIcon: <i className=\"sort-icon fas fa-exchange-alt\" />,\r\n    sortServer,\r\n    onSort,\r\n    sortFunction,\r\n    persistTableHead,\r\n    className: \"rki-table\"\r\n  };\r\n  return (\r\n    <DataTable {...dataTableProps} />\r\n  )\r\n};\r\n\r\nRKITable.defaultProps = {\r\n  subHeader: false,\r\n  pagination: true,\r\n  totalRows: null,\r\n  pagServer: true,\r\n  onSelectedRowsChange: null,\r\n  clearSelectedRows: false,\r\n  sortServer: false,\r\n  persistTableHead: false\r\n}\r\n\r\nexport default RKITable;\r\n\r\n", "\n    (window.__NEXT_P = window.__NEXT_P || []).push([\n      \"/event/EventsTable\",\n      function () {\n        return require(\"private-next-pages/event/EventsTable.tsx\");\n      }\n    ]);\n    if(module.hot) {\n      module.hot.dispose(function () {\n        window.__NEXT_P.push([\"/event/EventsTable\"])\n      });\n    }\n  ", "//Import Library\r\nimport Link from \"next/link\";\r\nimport moment from \"moment\";\r\nimport _ from \"lodash\";\r\nimport React, { useEffect, useState, useRef } from \"react\";\r\nimport { useRouter } from \"next/router\";\r\n\r\n//Import services/components\r\nimport RKITable from \"../../components/common/RKITable\";\r\nimport apiService from \"../../services/apiService\";\r\nimport EventsFilter from \"./EventsTableFilter\";\r\nimport { useTranslation } from 'next-i18next';\r\n\r\nconst Hazards = (props: any) => {\r\n  const { i18n } = useTranslation('common');\r\n  const currentLang = i18n.language === \"fr\" ? \"en\" : i18n.language;\r\n  const { hazards } = props;\r\n  return (\r\n    <ul>\r\n      {hazards.map((item: any, index: any) => {\r\n        {\r\n          return item && item._id && item.title && item.title[currentLang] ? (\r\n            <li key={index}>\r\n              <Link href=\"/hazard/[...routes]\" as={`/hazard/show/${item._id}`} >\r\n                {item.title[currentLang].toString()}\r\n              </Link>\r\n            </li>\r\n          ) : (\r\n            \"\"\r\n          );\r\n        }\r\n      })}\r\n    </ul>\r\n  );\r\n};\r\n\r\nfunction EventsTable(props: any) {\r\n  const router = useRouter();\r\n  const { t } = useTranslation('common');\r\n  const { setEvents, selectedRegions } = props;\r\n  const [filterText, setFilterText] = React.useState(\"\");\r\n  const [filterHazard, setFilterHazard] = React.useState(\"\");\r\n  const [resetPaginationToggle, setResetPaginationToggle] =\r\n    React.useState(false);\r\n  const [tabledata, setDataToTable] = useState([]);\r\n  const [loading, setLoading] = useState(false);\r\n  const [totalRows, setTotalRows] = useState(0);\r\n  const [perPage, setPerPage] = useState(10);\r\n  const [pageNum, setPageNum] = useState(1);\r\n  const [pageSort, setPageSort] = useState<any>(null);\r\n\r\n  const eventParams: any = {\r\n    sort: { created_at: \"desc\" },\r\n    lean: true,\r\n    populate: [\r\n      { path: \"country\", select: \"coordinates title\" },\r\n      { path: \"hazard_type\", select: \"title\" },\r\n      { path: \"hazard\", select: \"title\" },\r\n    ],\r\n    limit: perPage,\r\n    page: 1,\r\n    query: {},\r\n    select:\r\n      \"-description -operation -world_region -country_regions -syndrome -status -laboratory_confirmed -officially_validated -rki_monitored -risk_assessment -date -more_info -images -user -created_at -updated_at\",\r\n  };\r\n\r\n  const [evParams, setEvParams] = useState(eventParams);\r\n\r\n  const columns = [\r\n    {\r\n      name: t(\"Events.table.EventId\"),\r\n      selector: \"title\",\r\n      sortable: true,\r\n      width: \"20%\",\r\n      cell: (d: any) => (\r\n        <Link href=\"/event/[...routes]\" as={`/event/show/${d._id}`}>\r\n          {d.title}\r\n        </Link>\r\n      ),\r\n    },\r\n    {\r\n      name: t(\"Events.table.Country\"),\r\n      selector: \"country\",\r\n      sortable: true,\r\n      cell: (d: any) =>\r\n        d.country && d.country.title ? (\r\n          <Link\r\n            href=\"/country/[...routes]\"\r\n            as={`/country/show/${d.country._id}`}\r\n          >\r\n            {d.country.title}\r\n          </Link>\r\n        ) : (\r\n          \"\"\r\n        ),\r\n    },\r\n    {\r\n      name: t(\"Events.table.HazardType\"),\r\n      selector: \"hazard_type\",\r\n      sortable: true,\r\n      cell: (d: any) =>\r\n        d.hazard_type && d.hazard_type.title ? d.hazard_type.title : \"\",\r\n    },\r\n    {\r\n      name: t(\"Events.table.Hazard\"),\r\n      selector: \"hazard\",\r\n      cell: (d: any) => <Hazards hazards={d.hazard} />,\r\n    },\r\n    {\r\n      name: t(\"Events.table.InfoReceivedon\"),\r\n      selector: \"created_at\",\r\n      sortable: true,\r\n      cell: (d: any) => moment(d.start_date).format(\"M/D/Y\"),\r\n    },\r\n    {\r\n      name: t(\"Events.table.Lastupdated\"),\r\n      selector: \"updated_at\",\r\n      sortable: true,\r\n      cell: (d: any) => moment(d.updated_at).format(\"M/D/Y\"),\r\n    },\r\n  ];\r\n\r\n  const getEventsData = async (eventParamsinit: any) => {\r\n    setLoading(true);\r\n\r\n    // Check if there's a country query\r\n    if (router.query && router.query.country) {\r\n      eventParamsinit.query[\"country\"] = router.query.country;\r\n    }\r\n\r\n    // Handle selectedRegions correctly\r\n    if (selectedRegions === null) {\r\n      // No regions selected: don't filter by region\r\n      delete eventParamsinit.query[\"world_region\"];\r\n    } else if (selectedRegions.length === 0) {\r\n      // Empty region selection: force zero results\r\n      eventParamsinit.query[\"world_region\"] = [\"__NO_MATCH__\"];\r\n    } else {\r\n      // Filter by selected regions\r\n      eventParamsinit.query[\"world_region\"] = selectedRegions;\r\n    }\r\n\r\n    // Fetch data from the API\r\n    const response = await apiService.get(\"/event\", eventParamsinit);\r\n    if (response && Array.isArray(response.data)) {\r\n      setDataToTable(response.data);\r\n      setEvents(response.data);\r\n      setTotalRows(response.totalCount);\r\n    }\r\n\r\n    setLoading(false);\r\n  };\r\n\r\n\r\n  const handlePageChange = (page: any) => {\r\n    eventParams.limit = perPage;\r\n    eventParams.page = page;\r\n\r\n    // Add hazard type filter\r\n    filterHazard &&\r\n      (eventParams.query = { ...eventParams.query, hazard_type: filterHazard });\r\n\r\n    // Add sorting\r\n    pageSort && (eventParams.sort = pageSort.sort);\r\n\r\n    // Get data with updated filters\r\n    getEventsData(eventParams);\r\n    setPageNum(page);\r\n  };\r\n\r\n\r\n  const handlePerRowsChange = async (newPerPage: any, page: any) => {\r\n    eventParams.limit = newPerPage;\r\n    eventParams.page = page;\r\n    setLoading(true);\r\n\r\n    // Handle selected region in pagination\r\n    if (router.query && router.query.country) {\r\n      eventParams.query[\"country\"] = router.query.country;\r\n    }\r\n\r\n    // Handle selected region filter\r\n    if (selectedRegions === null) {\r\n      delete eventParams.query[\"world_region\"];\r\n    } else if (selectedRegions.length === 0) {\r\n      eventParams.query[\"world_region\"] = [\"__NO_MATCH__\"];\r\n    } else {\r\n      eventParams.query[\"world_region\"] = selectedRegions;\r\n    }\r\n\r\n    // Apply hazard filter\r\n    filterHazard &&\r\n      (eventParams.query = { ...eventParams.query, hazard_type: filterHazard });\r\n\r\n    // Apply sorting\r\n    pageSort && (eventParams.sort = pageSort.sort);\r\n\r\n    // Fetch data from the API\r\n    const response = await apiService.get(\"/event\", eventParams);\r\n    if (response && Array.isArray(response.data)) {\r\n      setDataToTable(response.data);\r\n      setEvents(response.data);\r\n      setPerPage(newPerPage);\r\n      setLoading(false);\r\n    }\r\n\r\n    setPageNum(page);\r\n  };\r\n\r\n\r\n  useEffect(() => {\r\n    evParams.page = 1;\r\n    getEventsData(evParams);\r\n  }, [selectedRegions, router]);\r\n\r\n  useEffect(() => {\r\n    getEventsData(evParams);\r\n  }, [evParams]);\r\n\r\n\r\n  const handleSort = async (column: any, sortDirection: any) => {\r\n    setLoading(true);\r\n    eventParams.sort = {\r\n      [column.selector]: sortDirection,\r\n    };\r\n    filterHazard &&\r\n      (eventParams.query = { ...eventParams.query, hazard_type: filterHazard });\r\n    filterText !== \"\" &&\r\n      (eventParams.query = { ...eventParams.query, title: filterText });\r\n\r\n    await getEventsData(eventParams);\r\n    setPageSort(eventParams);\r\n    setLoading(false);\r\n  };\r\n\r\n  const sendQuery = (q: any, page: any) => {\r\n    if (q) {\r\n      evParams.query[\"title\"] = q;\r\n      evParams.page = page;\r\n      setEvParams({ ...evParams });\r\n    } else {\r\n      delete evParams.query.title;\r\n      setEvParams({ ...evParams });\r\n    }\r\n  };\r\n\r\n  const handleSearchTitle = useRef(\r\n    _.debounce(\r\n      (q, page) => sendQuery(q, page),\r\n      Number(process.env.SEARCH_DEBOUNCE_TIME) || 300\r\n    )\r\n  ).current;\r\n\r\n  const subHeaderComponentMemo = React.useMemo(() => {\r\n    const handleClear = () => {\r\n      if (filterText) {\r\n        setResetPaginationToggle(!resetPaginationToggle);\r\n        setFilterText(\"\");\r\n      }\r\n    };\r\n\r\n    const handleFilterHazardType = (hazardType: any) => {\r\n      setFilterHazard(hazardType);\r\n      if (hazardType) {\r\n        evParams.query[\"hazard_type\"] = hazardType;\r\n        setEvParams({ ...evParams });\r\n      } else {\r\n        delete evParams.query.hazard_type;\r\n        setEvParams({ ...evParams });\r\n      }\r\n    };\r\n\r\n    const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {\r\n      setFilterText(e.target.value);\r\n      handleSearchTitle(e.target.value, pageNum);\r\n    };\r\n\r\n    return (\r\n      <EventsFilter\r\n        onFilter={handleChange}\r\n        onFilterHazardChange={(e: any) => handleFilterHazardType(e.target.value)}\r\n        onClear={handleClear}\r\n        filterText={filterText}\r\n        filterHazard={filterHazard}\r\n      />\r\n    );\r\n  }, [filterText, resetPaginationToggle, filterHazard, selectedRegions]);\r\n\r\n  return (\r\n    <RKITable\r\n      columns={columns}\r\n      data={tabledata}\r\n      totalRows={totalRows}\r\n      loading={loading}\r\n      subheader\r\n      persistTableHead\r\n      onSort={handleSort}\r\n      sortServer\r\n      pagServer={true}\r\n      subHeaderComponent={subHeaderComponentMemo}\r\n      handlePerRowsChange={handlePerRowsChange}\r\n      handlePageChange={handlePageChange}\r\n    />\r\n  );\r\n}\r\n\r\nexport default EventsTable;\r\n"], "names": ["filterText", "<PERSON><PERSON><PERSON>er", "onFilter", "onClear", "onFilterHazardChange", "filterHazard", "hazardType", "setHazardType", "useState", "t", "useTranslation", "getNetworks", "params", "response", "apiService", "get", "Array", "isArray", "data", "useEffect", "query", "sort", "title", "Container", "fluid", "className", "Row", "Col", "xs", "FormControl", "type", "placeholder", "aria-label", "value", "onChange", "as", "aria-placeholder", "option", "map", "item", "index", "_id", "RKITable", "props", "paginationComponentOptions", "rowsPerPageText", "columns", "totalRows", "resetPaginationToggle", "subheader", "subHeaderComponent", "handlePerRowsChange", "handlePageChange", "rowsPerPage", "defaultRowsPerPage", "selectableRows", "loading", "pagServer", "onSelectedRowsChange", "clearSelectedRows", "sortServer", "onSort", "persistTableHead", "sortFunction", "otherProps", "dataTableProps", "noDataComponent", "<PERSON><PERSON><PERSON><PERSON>", "dense", "paginationResetDefaultPage", "subHeader", "progressPending", "pagination", "paginationServer", "paginationPerPage", "paginationRowsPerPageOptions", "paginationTotalRows", "onChangeRowsPerPage", "onChangePage", "progressComponent", "CustomLoader", "sortIcon", "i", "DataTable", "defaultProps", "Hazards", "i18n", "currentLang", "language", "hazards", "ul", "li", "Link", "href", "toString", "EventsTable", "router", "useRouter", "setEvents", "selectedRegions", "setFilterText", "React", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "setResetPaginationToggle", "tabledata", "setDataToTable", "setLoading", "setTotalRows", "perPage", "setPerPage", "pageNum", "setPageNum", "pageSort", "setPageSort", "eventParams", "created_at", "lean", "populate", "path", "select", "limit", "page", "evParams", "setEvParams", "name", "selector", "sortable", "width", "cell", "d", "country", "hazard_type", "hazard", "moment", "start_date", "format", "updated_at", "getEventsData", "eventParamsinit", "length", "totalCount", "newPerPage", "handleSort", "column", "sortDirection", "<PERSON><PERSON><PERSON><PERSON>", "q", "handleSearchTitle", "useRef", "_", "Number", "process", "current", "subHeaderComponentMemo", "handleFilterHazardType", "handleChange", "e", "target", "handleClear"], "sourceRoot": "", "ignoreList": []}