{"version": 3, "file": "static/chunks/pages/institution/components/MoreInfoAccordion-5011ec22da831fa8.js", "mappings": "iMA+IA,MA1I0B,IACtB,GAAM,GAAEA,CAAC,CAAE,CAAGC,CAAAA,EAAAA,EAAAA,EAAAA,CAAcA,CAAC,MAyIlBC,IAxIL,aAwIsBA,EAAC,EAxIrBC,CAAe,kBAAEC,CAAgB,gBAAEC,CAAc,CAAE,CAAGC,EAoH9D,MACI,UAACC,EAAAA,CAASA,CAAAA,CAACC,iBAAiB,aACxB,WAACD,EAAAA,CAASA,CAACE,IAAI,EAACC,SAAS,cACrB,UAACH,EAAAA,CAASA,CAACI,MAAM,WACb,UAACC,MAAAA,CAAIC,UAAU,qBAAab,EAAE,gBAElC,WAACO,EAAAA,CAASA,CAACO,IAAI,EAACD,UAAU,oCAtHlC,WAACE,IAAAA,WACG,UAACC,IAAAA,UAAGhB,EAAE,sBAAwB,IAC9B,WAACiB,OAAAA,WACI,IACAd,EAAgBe,IAAI,CAAGf,EAAgBe,IAAI,CAACC,KAAK,CAAG,SAO7D,WAACJ,IAAAA,WACG,UAACC,IAAAA,UAAGhB,EAAE,aAAe,IACrB,UAACiB,OAAAA,UACId,EAAgBiB,QAAQ,CACnBjB,EAAgBiB,QAAQ,CAACC,GAAG,CAAC,CAACC,EAAWC,IACvC,UAACN,OAAAA,UACG,UAACO,KAAAA,UAAIF,EAAKH,KAAK,IADRI,IAIb,QAOd,WAACR,IAAAA,WACG,UAACC,IAAAA,UAAGhB,EAAE,qBAAuB,IAC7B,UAACiB,OAAAA,UACIb,GAAoBA,EAAiBqB,MAAM,CAAG,EAC3CrB,EAAiBiB,GAAG,CAAC,CAACC,EAAWI,IAC7B,UAACF,KAAAA,UACG,UAACG,IAAIA,CACDC,KAAM,yBACNC,GAAI,WAFHF,QAE+B,OAATL,EAAKQ,GAAG,WAE9BR,EAAKH,KAAK,IALVO,IAUb,UAACF,KAAAA,UAAIxB,EAAE,kCAQnB,WAACe,IAAAA,WACG,UAACC,IAAAA,UAAGhB,EAAE,eAAiB,IACvB,WAACiB,OAAAA,WACI,IACAd,EAAgB4B,SAAS,CACpB5B,EAAgB4B,SAAS,CAACV,GAAG,CAAC,CAACC,EAAWC,IACxC,WAACC,KAAAA,WACIF,EAAKH,KAAK,CAAC,IAAC,UAACa,KAAAA,CAAAA,KADTT,IAIX,SAOd,WAACR,IAAAA,WACG,UAACC,IAAAA,UAAGhB,EAAE,mBAAqB,IAC3B,UAACiB,OAAAA,UACIZ,GAAkBA,EAAeoB,MAAM,CAAG,EACvCpB,EAAegB,GAAG,CAAC,CAACC,EAAWI,IAC3B,UAACF,KAAAA,UACG,UAACG,IAAIA,CACDC,KAAM,uBACNC,GAAI,aAFHF,IAE6B,OAATL,EAAKQ,GAAG,WAE5BR,EAAKH,KAAK,IALVO,IAUb,UAACF,KAAAA,UAAIxB,EAAE,gCAQnB,WAACe,IAAAA,WACG,UAACC,IAAAA,UAAGhB,EAAE,gBAAkB,IACxB,UAACiB,OAAAA,UACId,GAAmBA,EAAgB8B,UAAU,CACxC9B,EAAgB8B,UAAU,CAC1BjC,EAAE,0BAOhB,WAACe,IAAAA,WACG,UAACC,IAAAA,UAAGhB,EAAE,UAAY,IAClB,UAACiB,OAAAA,UACId,GAAmBA,EAAgB+B,IAAI,CAClC/B,EAAgB+B,IAAI,CACpBlC,EAAE,2BAuBxB,mBC5IA,4CACA,4CACA,WACA,OAAe,EAAQ,KAAiE,CACxF,EACA,SAFsB", "sources": ["webpack://_N_E/./pages/institution/components/MoreInfoAccordion.tsx", "webpack://_N_E/?e8b9"], "sourcesContent": ["import React from \"react\";\r\nimport { Accordion } from \"react-bootstrap\";\r\nimport Link from \"next/link\";\r\nimport { useTranslation } from 'next-i18next';\r\n\r\nconst MoreInfoAccordion = (props: any) => {\r\n    const { t } = useTranslation('common');\r\n    const { institutionData, activeOperations, activeProjects } = props;\r\n\r\n    // Render organization type\r\n    const renderOrganizationType = () => (\r\n        <p>\r\n            <b>{t(\"OrganisationType\")}</b>:\r\n            <span>\r\n                {\" \"}\r\n                {institutionData.type ? institutionData.type.title : \"\"}\r\n            </span>\r\n        </p>\r\n    );\r\n\r\n    // Render networks\r\n    const renderNetworks = () => (\r\n        <p>\r\n            <b>{t(\"Network\")}</b>:\r\n            <span>\r\n                {institutionData.networks\r\n                    ? institutionData.networks.map((item: any, index: any) => (\r\n                        <span key={index}>\r\n                            <li>{item.title}</li>\r\n                        </span>\r\n                    ))\r\n                    : \"\"}\r\n            </span>\r\n        </p>\r\n    );\r\n\r\n    // Render active operations\r\n    const renderActiveOperations = () => (\r\n        <p>\r\n            <b>{t(\"ActiveOperation\")}</b>:\r\n            <span>\r\n                {activeOperations && activeOperations.length > 0 ? (\r\n                    activeOperations.map((item: any, i: any) => (\r\n                        <li key={i}>\r\n                            <Link\r\n                                href={\"/operation/[...routes]\"}\r\n                                as={`/operation/show/${item._id}`}\r\n                            >\r\n                                {item.title}\r\n                            </Link>\r\n                        </li>\r\n                    ))\r\n                ) : (\r\n                    <li>{t(\"NoActiveoperationsfound\")}</li>\r\n                )}\r\n            </span>\r\n        </p>\r\n    );\r\n\r\n    // Render expertise\r\n    const renderExpertise = () => (\r\n        <p>\r\n            <b>{t(\"Expertise\")}</b>:\r\n            <span>\r\n                {\" \"}\r\n                {institutionData.expertise\r\n                    ? institutionData.expertise.map((item: any, index: any) => (\r\n                        <li key={index}>\r\n                            {item.title} <br />\r\n                        </li>\r\n                    ))\r\n                    : \"\"}\r\n            </span>\r\n        </p>\r\n    );\r\n\r\n    // Render active projects\r\n    const renderActiveProjects = () => (\r\n        <p>\r\n            <b>{t(\"ActiveProject\")}</b>:\r\n            <span>\r\n                {activeProjects && activeProjects.length > 0 ? (\r\n                    activeProjects.map((item: any, i: any) => (\r\n                        <li key={i}>\r\n                            <Link\r\n                                href={\"/project/[...routes]\"}\r\n                                as={`/project/show/${item._id}`}\r\n                            >\r\n                                {item.title}\r\n                            </Link>\r\n                        </li>\r\n                    ))\r\n                ) : (\r\n                    <li>{t(\"NoActiveprojectsfound\")}</li>\r\n                )}\r\n            </span>\r\n        </p>\r\n    );\r\n\r\n    // Render department\r\n    const renderDepartment = () => (\r\n        <p>\r\n            <b>{t(\"Department\")}</b>:\r\n            <span>\r\n                {institutionData && institutionData.department\r\n                    ? institutionData.department\r\n                    : t(\"Nodepartmentfound\")}\r\n            </span>\r\n        </p>\r\n    );\r\n\r\n    // Render unit\r\n    const renderUnit = () => (\r\n        <p>\r\n            <b>{t(\"Unit\")}</b>:\r\n            <span>\r\n                {institutionData && institutionData.unit\r\n                    ? institutionData.unit\r\n                    : t(\"Nounitfound\")}\r\n            </span>\r\n        </p>\r\n    );\r\n\r\n    return (\r\n        <Accordion defaultActiveKey=\"0\">\r\n            <Accordion.Item eventKey=\"0\">\r\n                <Accordion.Header>\r\n                    <div className=\"cardTitle\">{t(\"MoreInfo\")}</div>\r\n                </Accordion.Header>\r\n                <Accordion.Body className=\"institutionDetails ps-4\">\r\n                    {renderOrganizationType()}\r\n                    {renderNetworks()}\r\n                    {renderActiveOperations()}\r\n                    {renderExpertise()}\r\n                    {renderActiveProjects()}\r\n                    {renderDepartment()}\r\n                    {renderUnit()}\r\n                </Accordion.Body>\r\n            </Accordion.Item>\r\n        </Accordion>\r\n    );\r\n};\r\n\r\nexport default MoreInfoAccordion;\r\n", "\n    (window.__NEXT_P = window.__NEXT_P || []).push([\n      \"/institution/components/MoreInfoAccordion\",\n      function () {\n        return require(\"private-next-pages/institution/components/MoreInfoAccordion.tsx\");\n      }\n    ]);\n    if(module.hot) {\n      module.hot.dispose(function () {\n        window.__NEXT_P.push([\"/institution/components/MoreInfoAccordion\"])\n      });\n    }\n  "], "names": ["t", "useTranslation", "MoreInfoAccordion", "institutionData", "activeOperations", "activeProjects", "props", "Accordion", "defaultActiveKey", "<PERSON><PERSON>", "eventKey", "Header", "div", "className", "Body", "p", "b", "span", "type", "title", "networks", "map", "item", "index", "li", "length", "i", "Link", "href", "as", "_id", "expertise", "br", "department", "unit"], "sourceRoot": "", "ignoreList": []}