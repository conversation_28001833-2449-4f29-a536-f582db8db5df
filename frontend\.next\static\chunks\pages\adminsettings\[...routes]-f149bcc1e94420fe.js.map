{"version": 3, "file": "static/chunks/pages/adminsettings/[...routes]-f149bcc1e94420fe.js", "mappings": "qNAcA,IAAMA,EAAQ,CACZ,CACEC,IAAK,YACLC,MAAO,YACT,EACA,CACED,IAAK,cACLC,MAAO,eACT,EACA,CACED,IAAK,QACLC,MAAO,QACT,EACA,CACED,IAAK,UACLC,MAAO,UACT,EACA,CACED,IAAK,UACLC,MAAO,SACT,EACA,CACED,IAAK,SACLC,MAAO,gBACT,EACD,CA2CD,EAxC2B,OAAC,YAACC,CAAU,QAwCxBC,EAxC0BC,CAAQ,eAwChBD,EAAC,GAxCiBE,CAAkB,SAAEC,CAAO,YAAEC,CAAU,CAA2B,GAC7G,GAAEC,CAAC,CAAE,CAAGC,CAAAA,EAAAA,EAAAA,EAAAA,CAAcA,CAAC,UAC7B,MACE,UAACC,EAAAA,CAASA,CAAAA,CAACC,KAAK,IAACC,UAAU,eACzB,WAACC,EAAAA,CAAGA,CAAAA,WACF,UAACC,EAAAA,CAAGA,CAAAA,CAACC,GAAI,EAAGC,GAAI,EAAGJ,UAAU,+BAC3B,UAACK,EAAAA,CAASA,CAAAA,UACV,UAACC,EAAAA,CAAWA,CAAAA,CACVC,KAAK,OACLP,UAAU,cACVQ,YAAaZ,EAAE,qCACfa,aAAW,SACXC,MAAOpB,EACPqB,SAAUnB,QAId,UAACU,EAAAA,CAAGA,CAAAA,CAACC,GAAI,EAAGC,GAAI,WACd,WAACC,EAAAA,CAASA,CAAAA,CAACO,GAAIX,EAAAA,CAAGA,WAChB,UAACY,EAAAA,CAASA,CAAAA,CAACC,MAAM,IAACC,GAAI,EAAGC,GAAI,EAAGhB,UAAU,gBAAQJ,EAAE,qCACpD,UAACM,EAAAA,CAAGA,CAAAA,CAACF,UAAU,mBACb,UAACM,EAAAA,CAAWA,CAAAA,CACVM,GAAG,SACHH,aAAW,OACXE,SAAU,GAAOlB,EAAmBwB,GACpCP,MAAOf,WACNR,EAAM+B,GAAG,CAAC,CAACC,EAAMC,IAEd,UAACC,SAAAA,CAAmBX,MAAOS,EAAK/B,GAAG,UAAG+B,EAAK9B,KAAK,EAAnC+B,iBAUjC,mICpDA,MAtB0B,OAAC,YAAE9B,CAAU,QAsBxBgC,EAtB0B9B,CAAQ,SAAEE,CAAO,CAAM,GACxD,EAqByB,CArBvBE,CAAC,CAAE,CAAGC,CAAAA,EAAAA,EAAAA,EAAAA,CAAcA,CAAC,UAG7B,MACE,UAACC,EAAAA,CAASA,CAAAA,CAACC,KAAK,IAACC,UAAU,eACzB,UAACC,EAAAA,CAAGA,CAAAA,UACF,UAACC,EAAAA,CAAGA,CAAAA,CAACE,GAAI,EAAGJ,UAAU,eACpB,UAACM,EAAAA,CAAWA,CAAAA,CACVC,KAAK,OACLP,UAAU,cACVQ,YAAaZ,EAAE,8BACfa,aAAW,SACXC,MAAOpB,EACPqB,SAAUnB,SAMtB,6KCyGA,MAvH2B,IACvB,GAAM,CAAEI,CAAC,CAAE,CAAGC,CAAAA,EAAAA,EAAAA,EAAAA,CAAcA,CAAC,QAsHlB0B,EArHL,CAACC,EAAWC,EAAe,CAAGC,CAAAA,EAAAA,EAAAA,KAqHPH,EAAC,CArHMG,CAAQA,CAAC,EAAE,EACzC,EAAGC,EAAW,CAAGD,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,EAAC,GAC1B,CAACE,EAAWC,EAAa,CAAGH,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAC,GACrC,CAACI,EAASC,EAAW,CAAGL,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAC,IACjC,CAACM,EAAaC,EAAS,CAAGP,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,EAAC,GACnC,CAACQ,EAAqBC,EAAuB,CAAGT,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAC,CAAC,GAE1DU,EAAsB,CACxBC,KAAM,CAAEhD,MAAO,KAAM,EACrBiD,MAAOR,EACPS,KAAM,EACNC,MAAO,CAAC,CACZ,EAEMC,EAAU,CACZ,CACIC,KAAM9C,EAAE,oCACR+C,SAAU,OACd,EACA,CACID,KAAM9C,EAAE,qCACR+C,SAAU,GACVC,KAAM,GACF,WAACC,MAAAA,WACG,UAACC,IAAIA,CAACC,KAAK,6BAA6BnC,GAAI,OAAvCkC,8BAAkF,OAANE,EAAE5D,GAAG,WAElF,UAAC6D,IAAAA,CAAEjD,UAAU,uBAEV,OAEP,UAACkD,IAAAA,CAAEC,QAAS,IAAMC,EAAWJ,YACzB,UAACC,IAAAA,CAAEjD,UAAU,4BACZ,MAGjB,EACH,CAEKqD,EAAuB,UACzB1B,GAAW,GACX,IAAM2B,EAAW,MAAMC,EAAAA,CAAUA,CAACC,GAAG,CAAC,iBAAkBpB,GACpDkB,GAAYA,EAASG,IAAI,EAAIH,EAASG,IAAI,CAACC,MAAM,CAAG,GAAG,CACvDjC,EAAe6B,EAASG,IAAI,EAC5B5B,EAAayB,EAASK,UAAU,EAChChC,EAAW,IAEnB,EAQMiC,EAAsB,MAAOC,EAAiBtB,KAChDH,EAAoBE,KAAK,CAAGuB,EAC5BzB,EAAoBG,IAAI,CAAGA,EAC3BZ,GAAW,GACX,IAAM2B,EAAW,MAAMC,EAAAA,CAAUA,CAACC,GAAG,CAAC,iBAAkBpB,GACpDkB,GAAYA,EAASG,IAAI,EAAIH,EAASG,IAAI,CAACC,MAAM,CAAG,GAAG,CACvDjC,EAAe6B,EAASG,IAAI,EAC5B1B,EAAW8B,GACXlC,EAAW,IAEnB,EAEMyB,EAAa,MAAOU,IACtB3B,EAAuB2B,EAAI1E,GAAG,EAC9B6C,GAAS,EACb,EAEM8B,EAAe,UACjB,GAAI,CACA,MAAMR,EAAAA,CAAUA,CAACS,MAAM,CAAC,kBAAsC,OAApB9B,IAC1CmB,IACApB,GAAS,GACTgC,EAAAA,EAAKA,CAACC,OAAO,CAACtE,EAAE,qEACpB,CAAE,MAAOuE,EAAO,CACZF,EAAAA,EAAKA,CAACE,KAAK,CAACvE,EAAE,+DAClB,CACJ,EAEMwE,EAAY,IAAMnC,GAAS,GAMjC,MAJAoC,CAAAA,EAAAA,EAAAA,SAAAA,CAASA,CAAC,KACNhB,GACJ,EAAG,EAAE,EAGD,WAACR,MAAAA,WACG,WAACyB,EAAAA,CAAKA,CAAAA,CAACC,KAAMvC,EAAawC,OAAQJ,YAC9B,UAACE,EAAAA,CAAKA,CAACG,MAAM,EAACC,WAAW,aACrB,UAACJ,EAAAA,CAAKA,CAACK,KAAK,WAAE/E,EAAE,sDAEpB,UAAC0E,EAAAA,CAAKA,CAACM,IAAI,WAAEhF,EAAE,wEACf,WAAC0E,EAAAA,CAAKA,CAACO,MAAM,YACT,UAACC,EAAAA,CAAMA,CAAAA,CAACC,QAAQ,YAAY5B,QAASiB,WAChCxE,EAAE,uCAEP,UAACkF,EAAAA,CAAMA,CAAAA,CAACC,QAAQ,UAAU5B,QAASY,WAC9BnE,EAAE,0CAKf,UAACoF,EAAAA,CAAQA,CAAAA,CACLvC,QAASA,EACTgB,KAAMjC,EACNI,UAAWA,EACXqD,WAAW,EACXrB,oBAAqBA,EACrBsB,iBA/Da,CA+DKA,GA9D1B9C,EAAoBE,KAAK,CAAGR,EAC5BM,EAAoBG,IAAI,CAAGA,EAC3Bc,GACJ,MA+DJ,6KCQA,MA9HyB,IACrB,GAAM,CAAEzD,CAAC,CAAE,CAAGC,CAAAA,EAAAA,EAAAA,EAAAA,CAAcA,CAAC,QA6HlBsF,EA5HL,CAAC3D,EAAWC,EAAe,CAAGC,CAAAA,EAAAA,EAAAA,GA4HTyD,EAAC,GA5HQzD,CAAQA,CAAC,EAAE,EACzC,EAAGC,EAAW,CAAGD,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,EAAC,GAC1B,CAACE,EAAWC,EAAa,CAAGH,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAC,GACrC,CAACI,EAASC,EAAW,CAAGL,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAC,IACjC,CAACM,EAAaC,EAAS,CAAGP,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,EAAC,GACnC,CAAC0D,EAAmBC,EAAqB,CAAG3D,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAC,CAAC,GAEtD4D,EAAoB,CACtBjD,KAAM,CAAEhD,MAAO,KAAM,EACrBiD,MAAOR,EACPS,KAAM,EACNC,MAAO,CAAC,CACZ,EAEMC,EAAU,CACZ,CACIC,KAAM9C,EAAE,wCACR+C,SAAU,GAAcmB,EAAIzE,KAAK,CACjCkG,SAAU,EACd,EACA,CACI7C,KAAM9C,EAAE,uCACR+C,SAAU,GAAcmB,EAAI0B,IAAI,CAChCD,UAAU,EACV3C,KAAM,GAAYI,EAAEwC,IAAI,EAE5B,CACI9C,KAAM9C,EAAE,yCACR+C,SAAU,GAAcmB,EAAI1E,GAAG,CAC/BmG,UAAU,EACV3C,KAAM,GACF,WAACC,MAAAA,WACG,UAACC,IAAIA,CAACC,KAAK,6BAA6BnC,GAAI,OAAvCkC,4BAAgF,OAANE,EAAE5D,GAAG,WAEhF,UAAC6D,IAAAA,CAAEjD,UAAU,uBAEV,OAEP,UAACkD,IAAAA,CAAEC,QAAS,IAAMC,EAAWJ,YACzB,UAACC,IAAAA,CAAEjD,UAAU,4BACZ,MAGjB,EACH,CAEKyF,EAAqB,UACvB9D,GAAW,GACX,IAAM2B,EAAW,MAAMC,EAAAA,CAAUA,CAACC,GAAG,CAAC,eAAgB8B,GAClDhC,GAAYA,EAASG,IAAI,EAAIH,EAASG,IAAI,CAACC,MAAM,CAAG,GAAG,CACvDjC,EAAe6B,EAASG,IAAI,EAC5B5B,EAAayB,EAASK,UAAU,EAChChC,GAAW,GAEnB,EAOMiC,EAAsB,MAAOC,EAAiBtB,KAChD+C,EAAkBhD,KAAK,CAAGuB,EAC1ByB,EAAkB/C,IAAI,CAAGA,EACzBZ,GAAW,GACX,IAAM2B,EAAW,MAAMC,EAAAA,CAAUA,CAACC,GAAG,CAAC,eAAgB8B,GAClDhC,GAAYA,EAASG,IAAI,EAAIH,EAASG,IAAI,CAACC,MAAM,CAAG,GAAG,CACvDjC,EAAe6B,EAASG,IAAI,EAC5B1B,EAAW8B,GACXlC,GAAW,GAEnB,EAEMyB,EAAa,MAAOU,IACtBuB,EAAqBvB,EAAI1E,GAAG,EAC5B6C,GAAS,EACb,EAEM8B,EAAe,UACjB,GAAI,CACA,MAAMR,EAAAA,CAAUA,CAACS,MAAM,CAAC,gBAAkC,OAAlBoB,IACxCK,IACAxD,GAAS,GACTgC,EAAAA,EAAKA,CAACC,OAAO,CAACtE,EAAE,iEACpB,CAAE,MAAOuE,EAAO,CACZF,EAAAA,EAAKA,CAACE,KAAK,CAACvE,EAAE,2DAClB,CACJ,EAEMwE,EAAY,IAAMnC,GAAS,GAMjC,MAJAoC,CAAAA,EAAAA,EAAAA,SAAAA,CAASA,CAAC,KACNoB,GACJ,EAAG,EAAE,EAGD,WAAC5C,MAAAA,WACG,WAACyB,EAAAA,CAAKA,CAAAA,CAACC,KAAMvC,EAAawC,OAAQJ,YAC9B,UAACE,EAAAA,CAAKA,CAACG,MAAM,EAACC,WAAW,aACrB,UAACJ,EAAAA,CAAKA,CAACK,KAAK,WAAE/E,EAAE,wDAEpB,UAAC0E,EAAAA,CAAKA,CAACM,IAAI,WAAEhF,EAAE,2EACf,WAAC0E,EAAAA,CAAKA,CAACO,MAAM,YACT,UAACC,EAAAA,CAAMA,CAAAA,CAACC,QAAQ,YAAY5B,QAASiB,WAChCxE,EAAE,2CAEP,UAACkF,EAAAA,CAAMA,CAAAA,CAACC,QAAQ,UAAU5B,QAASY,WAC9BnE,EAAE,8CAKf,UAACoF,EAAAA,CAAQA,CAAAA,CACLvC,QAASA,EACTgB,KAAMjC,EACNI,UAAWA,EACXqD,WAAW,EACXrB,oBAAqBA,EACrBsB,iBA/Da,CA+DKA,GA9D1BI,EAAkBhD,KAAK,CAAGR,EAC1BwD,EAAkB/C,IAAI,CAAGA,EACzBkD,GACJ,MA+DJ,iOCzEA,MAlDyB,QAkCjBC,EAAAA,EAjCN,GAAM,GAAE9F,CAAC,CAAE,CAAGC,CAAAA,EAAAA,EAAAA,EAAAA,CAAcA,CAAC,UACvB8F,EAAuB,EAgDAC,EA9CzB,UAAC/C,MAAAA,UACC,WAAC/C,EAAAA,CAASA,CAAAA,CAAC+F,MAAO,CAAEC,UAAW,QAAS,EAAG/F,KAAK,IAACC,UAAU,gBACzD,UAACC,EAAAA,CAAGA,CAAAA,UACF,UAACC,EAAAA,CAAGA,CAAAA,CAACC,GAAI,YACP,UAAC4F,EAAAA,CAAWA,CAAAA,CAAC1G,MAAOO,EAAE,mDAG1B,UAACK,EAAAA,CAAGA,CAAAA,UACF,UAACC,EAAAA,CAAGA,CAAAA,CAACC,GAAI,YACP,UAAC2C,IAAIA,CACHC,KAAK,6BACLnC,GAAG,OAFAkC,sCAIH,UAACgC,EAAAA,CAAMA,CAAAA,CAACC,QAAQ,YAAYiB,KAAK,cAChCpG,EAAE,wDAKT,UAACK,EAAAA,CAAGA,CAAAA,CAACD,UAAU,gBACb,UAACE,EAAAA,CAAGA,CAAAA,CAACC,GAAI,YACP,UAAEgF,EAAAA,OAAgBA,CAAAA,CAAAA,YAOxBc,EAAqBC,CAAAA,EAAAA,EAAAA,iBAAAA,CAAiBA,CAAC,IAAM,UAACP,EAAAA,CAAAA,IAC9CD,EAAYS,CAAAA,EAAAA,EAAAA,EAAAA,CAAWA,CAAC,GAAWT,SACzC,IAAI,GAAEA,GAAAA,OAAAA,EAAAA,EAAOU,IAAPV,OAAOU,GAAPV,OAAAA,EAAAA,EAAAA,WAAoBW,EAApBX,KAAAA,EAAAA,CAAiC,CAAC,GAAlCA,UAA+C,EAInD,UAACO,EAAAA,CAAAA,GAHM,UAACK,EAAAA,OAAeA,CAAAA,CAAAA,EAK3B,kOCUA,MAjDqB,QAiCbZ,EAAAA,EAhCN,GAAM,GAAE9F,CAAC,CAAE,CAAGC,CAAAA,EAAAA,EAAAA,EAAAA,CAAcA,CAAC,UACvB0G,EAAmB,IAErB,WAACzG,EAAAA,CAASA,CAAAA,CAAC+F,MAAO,CAAEC,UAAW,QAAS,EAAG/F,KAAK,IAACC,UAAU,gBACzD,UAACC,EAAAA,CAAGA,CAAAA,UACF,UAACC,EAAAA,CAAGA,CAAAA,CAACC,GAAI,YACP,UAAC4F,EAAAA,CAAWA,CAAAA,CAAC1G,MAAOO,EAAE,gDAG1B,UAACK,EAAAA,CAAGA,CAAAA,UACF,UAACC,EAAAA,CAAGA,CAAAA,CAACC,GAAI,YACP,UAAC2C,IAAIA,CACHC,KAAK,6BACLnC,GAAG,OAFAkC,kCAIH,UAACgC,EAAAA,CAAMA,CAAAA,CAACC,QAAQ,YAAYiB,KAAK,cAC/BpG,EAAE,mDAKV,UAACK,EAAAA,CAAGA,CAAAA,CAACD,UAAU,gBACb,UAACE,EAAAA,CAAGA,CAAAA,CAACC,GAAI,YACP,UAACqG,EAAAA,OAAYA,CAAAA,CAAAA,UAOjBC,EAAiBC,CAAAA,EAAAA,EAAAA,aAAAA,CAAaA,CAAC,IAAM,UAACH,EAAAA,CAAAA,IACtCb,EAAYS,CAAAA,EAAAA,EAAAA,EAAAA,CAAWA,CAAC,GAAWT,SACzC,IAAI,GAAEA,GAAAA,OAAAA,EAAAA,EAAOU,IAAPV,OAAOU,GAAPV,OAAAA,EAAAA,EAAoBiB,OAAAA,EAApBjB,KAAAA,EAAAA,CAA6B,CAAC,GAA9BA,UAA2C,EAI/C,CAJkD,EAIlD,OAACe,EAAAA,CAAAA,GAHM,UAACH,EAAAA,OAAeA,CAAAA,CAAAA,EAK3B,+MCGA,MA1C6B,QAmCrBZ,EAAAA,EAlCN,GAAM,GAAE9F,CAAC,CAAE,CAAGC,CAAAA,EAAAA,EAAAA,EAAAA,CAAcA,CAAC,UACvB+G,EAA2B,IAE7B,EAsC6BC,CAtC7B,CAsC8B,CAtC9B,KAAChE,MAAAA,UACC,WAAC/C,EAAAA,CAASA,CAAAA,CAAC+F,MAAO,CAAEC,UAAW,QAAS,EAAG/F,KAAK,IAACC,UAAU,gBACzD,UAACC,EAAAA,CAAGA,CAAAA,UACF,UAACC,EAAAA,CAAGA,CAAAA,CAACC,GAAI,YACP,UAAC4F,EAAAA,CAAWA,CAAAA,CAAC1G,MAAQO,EAAE,sDAG3B,UAACK,EAAAA,CAAGA,CAAAA,UACF,UAACC,EAAAA,CAAGA,CAAAA,CAACC,GAAI,YACP,UAAC2C,IAAIA,CACHC,KAAK,6BACLnC,GAAG,OAFAkC,0CAIH,UAACgC,EAAAA,CAAMA,CAAAA,CAACC,QAAQ,YAAYiB,KAAK,cAChCpG,EAAE,2DAKT,UAACK,EAAAA,CAAGA,CAAAA,CAACD,UAAU,gBACb,UAACE,EAAAA,CAAGA,CAAAA,CAACC,GAAI,YACP,UAAC2G,EAAAA,OAAoBA,CAAAA,CAAAA,YAQ3BC,EAAyBC,CAAAA,EAAAA,EAAAA,qBAAAA,CAAqBA,CAAC,IAAM,UAACJ,EAAAA,CAAAA,IACtDlB,EAAYS,CAAAA,EAAAA,EAAAA,EAAAA,CAAWA,CAAC,GAAWT,SACzC,IAAI,GAAEA,GAAAA,OAAAA,EAAAA,EAAOU,IAAPV,OAAOU,GAAPV,OAAAA,EAAAA,EAAAA,gBAAoBuB,EAApBvB,KAAAA,EAAAA,CAAsC,CAAC,GAAvCA,UAAoD,EAIxD,UAACqB,EAAAA,CAAAA,GAHM,UAACT,EAAAA,OAAeA,CAAAA,CAAAA,EAK3B,yPC4FA,MAhI6BY,IACzB,IAAMC,EAA0B,CAC5B9H,MAAO,EACX,EAEM,CAAC+H,EAAYC,EAAc,CAAG3F,CA2HzB4F,EA3HyB5F,EAAAA,QAAAA,CAAQA,CAAkByF,GAExDI,EAyHwBD,EAzHPE,MAAM,EAAwB,yBAApBN,EAAMM,MAAM,CAAC,EAAE,EAA+BN,EAAMM,MAAM,CAAC,EAAE,CACxF,GAAE5H,CAAC,CAAE,CAAGC,CAAAA,EAAAA,EAAAA,EAAAA,CAAcA,CAAC,UAEvB4H,EAAUC,CAAAA,EAAAA,EAAAA,MAAAA,CAAMA,CAAC,MAkBjBC,EAAe,MAAOC,QAMpBtE,EACAuE,EANJD,EAAME,cAAc,GACpB,IAAMC,EAAM,CACR1I,MAAO+H,EAAW/H,KAAK,CAAC2I,IAAI,EAChC,EAIIT,GACAM,EAAW,KADD,wCAEVvE,EAAW,MAAMC,EAAAA,CAAUA,CAAC0E,KAAK,CAAC,qBAAqC,OAAhBf,EAAMM,MAAM,CAAC,EAAE,EAAIO,KAE1EF,EAAW,mCACXvE,EAAW,MAAMC,EAAAA,CAAUA,CAAC2E,IAAI,CAAC,oBAAqBH,IAEtDzE,GAAYA,EAASlE,GAAG,EACxB6E,EAAAA,EAAKA,CAACC,OAAO,CAACtE,EAAEiI,IAChBM,IAAAA,IAAW,CAAC,mCAER7E,OAAAA,EAAAA,KAAAA,EAAAA,EAAU8E,SAAAA,CAAV9E,GAAwB,KACxBW,EAD+B,EAC1BA,CAACE,KAAK,CAACvE,EAAE,yBAEdqE,EAAAA,EAAKA,CAACE,KAAK,CAACb,EAGxB,EAiBA,MAfAe,CAAAA,EAAAA,EAAAA,SAAAA,CAASA,CAAC,KACN,IAAMgE,EAAwB,CAC1B7F,MAAO,CAAC,EACRH,KAAM,CAAEhD,MAAO,KAAM,EACrBiD,MAAO,GACX,EACIiF,GAKAe,CAJ+B,MADrB,IAEN,IAAMhF,EAA4B,MAAMC,EAAAA,CAAUA,CAACC,GAAG,CAAC,qBAAqC,OAAhB0D,EAAMM,MAAM,CAAC,EAAE,EAAIa,GAC/FhB,EAAc,GAAgB,EAAE,GAAGkB,CAAS,CAAE,EAAhB,CAAmBjF,CAAQ,CAAC,GAC9D,GAGR,EAAG,EAAE,EAGD,UAACT,MAAAA,UACG,UAAC/C,EAAAA,CAASA,CAAAA,CAACE,UAAU,WAAWD,KAAK,aACjC,UAACyI,EAAAA,CAAIA,CAAAA,CACD3C,MAAO,CACH4C,UAAW,MACXC,UAAW,kEACf,WAEA,UAACC,EAAAA,CAAqBA,CAAAA,CAACC,SAAUjB,EAAckB,IAAKpB,EAASqB,cAAe1B,EAAY2B,mBAAoB,YACxG,WAACP,EAAAA,CAAIA,CAAC5D,IAAI,YACN,UAAC3E,EAAAA,CAAGA,CAAAA,UACA,UAACC,EAAAA,CAAGA,CAAAA,UACA,UAACsI,EAAAA,CAAIA,CAAC7D,KAAK,WAAE/E,EAAE,yBAGvB,UAACoJ,KAAAA,CAAAA,GACD,UAAC/I,EAAAA,CAAGA,CAAAA,UACA,UAACC,EAAAA,CAAGA,CAAAA,CAACE,EAAE,IAACY,GAAI,EAAGD,GAAI,YACf,WAACkI,EAAAA,CAAIA,CAACC,KAAK,YACP,UAACD,EAAAA,CAAIA,CAACE,KAAK,EAACnJ,UAAU,0BAAkBJ,EAAE,qBAC1C,UAACwJ,EAAAA,EAASA,CAAAA,CACN1G,KAAK,QACL2G,GAAG,QACHC,QAAQ,IACR5I,MAAO0G,EAAW/H,KAAK,CACvBkK,UAAW,GAAmBC,YAAO9I,GAAS,IAAIsH,IAAI,GACtDyB,aAAc,CACVF,UAAW3J,EAAE,sCACjB,EACAe,SAlFnB,CAkF6B+I,GAjF9C,GAAIzI,EAAE0I,MAAM,CAAE,CACV,GAAM,CAAEjH,MAAI,OAAEhC,CAAK,CAAE,CAAGO,EAAE0I,MAAM,CAChCtC,EAAc,GAAgB,EAC1B,GAAGkB,CAAS,CACZ,CAAC7F,CAFyB,CAEpB,CAAEhC,EACZ,EACJ,CACJ,WA+EwB,UAACT,EAAAA,CAAGA,CAAAA,CAACD,UAAU,gBACX,WAACE,EAAAA,CAAGA,CAAAA,WACA,UAAC4E,EAAAA,CAAMA,CAAAA,CAAC9E,UAAU,OAAOO,KAAK,SAASwE,QAAQ,mBAC1CnF,EAAE,YAEP,UAACkF,EAAAA,CAAMA,CAAAA,CAAC9E,UAAU,OAAOmD,QAlGpC,CAkG6CyG,IAjG9DvC,EAAcF,GAEd0C,OAAOC,QAAQ,CAAC,EAAG,EACvB,EA8FgF/E,QAAQ,gBACnDnF,EAAE,WAEP,UAACkD,IAAIA,CACDC,KAAK,6BACLnC,GAAK,OAFJkC,mCAID,UAACgC,EAAAA,CAAMA,CAAAA,CAACC,QAAQ,qBAAanF,EAAE,6BAUvE,yLCpGA,MApCwB,IACtB,GAAM,CAAEA,CAAC,CAAE,CAAGC,CAAAA,EAAAA,EAAAA,EAAAA,CAAcA,CAAC,QAmChBkK,EAlCPC,EAAsB,IAExB,OAgCwBD,EAhCxB,EAACjK,EAAAA,CAASA,CAAAA,CAAC+F,MAAO,CAAEC,UAAW,QAAS,EAAG/F,KAAK,IAACC,UAAU,gBACzD,UAACC,EAAAA,CAAGA,CAAAA,UACF,UAACC,EAAAA,CAAGA,CAAAA,CAACC,GAAI,YACP,UAAC4F,EAAAA,CAAWA,CAAAA,CAAC1G,MAAOO,EAAE,6CAG1B,UAACK,EAAAA,CAAGA,CAAAA,UACF,UAACC,EAAAA,CAAGA,CAAAA,CAACC,GAAI,YACP,UAAC2C,IAAIA,CACHC,KAAK,6BACLnC,GAAG,OAFAkC,sCAIH,UAACgC,EAAAA,CAAMA,CAAAA,CAACC,QAAQ,YAAYiB,KAAK,cAChCpG,EAAE,kDAKT,UAACK,EAAAA,CAAGA,CAAAA,CAACD,UAAU,gBACb,UAACE,EAAAA,CAAGA,CAAAA,CAACC,GAAI,YACP,UAAC8J,EAAAA,OAAeA,CAAAA,CAAAA,UAOpBC,EAAqBC,CAAAA,EAAAA,EAAAA,OAAAA,CAAgBA,CAAC,IAAM,UAACH,EAAAA,CAAAA,IACnD,MACE,UAACE,EAAAA,CAAAA,EAEL,yPC2GA,MApIuB,IACnB,GAAM,CAAEtK,CAAC,CAAE,CAAGC,CAAAA,EAAAA,EAAAA,EAAAA,CAAcA,CAAC,QAmIlBuK,EAjILC,EAAqB,CACvBjL,IAAK,GACLC,EA+HqB+K,EAAC,EA/Hf,EACX,EAEM,CAAChD,EAAYC,EAAc,CAAG3F,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAa2I,GAEnD9C,EAAoB,CAAC,CAAEL,GAAMM,MAAM,EAAwB,sBAApBN,EAAMM,MAAM,CAAC,EAAE,EAA4BN,EAAMM,MAAM,CAAC,IAE/FC,EAAUC,CAAAA,EAAAA,EAAAA,MAAAA,CAAMA,CAAM,MAkBtBC,EAAe,MAAOC,QAMpBtE,EACAuE,EANJD,EAAME,cAAc,GACpB,IAAMC,EAAM,CACR1I,MAAO+H,EAAW/H,KAAK,CAAC2I,IAAI,EAChC,EAIIT,GACAM,EAAW,KADD,6BAEVvE,EAAW,MAAMC,EAAAA,CAAUA,CAAC0E,KAAK,CAAC,eAA+B,OAAhBf,EAAMM,MAAM,CAAC,EAAE,EAAIO,KAEpEF,EAAW,gCACXvE,EAAW,MAAMC,EAAAA,CAAUA,CAAC2E,IAAI,CAAC,cAAeH,IAEhDzE,GAAYA,EAASlE,GAAG,EACxB6E,EAAAA,EAAKA,CAACC,OAAO,CAACtE,EAAEiI,IAChBM,IAAAA,IAAW,CAAC,gCAER7E,OAAAA,EAAAA,KAAAA,EAAAA,EAAU8E,SAAAA,CAAV9E,GAAwB,KACxBW,EAD+B,EAC1BA,CAACE,KAAK,CAACvE,EAAE,yBAEdqE,EAAAA,EAAKA,CAACE,KAAK,CAACb,EAGxB,EAiBA,MAfAe,CAAAA,EAAAA,EAAAA,SAAAA,CAASA,CAAC,KACN,IAAMiG,EAAmB,CACrB9H,MAAO,CAAC,EACRH,KAAM,CAAEhD,MAAO,KAAM,EACrBiD,MAAO,GACX,EACIiF,GAC0B,OADhB,IAEN,IAAMjE,EAAuB,MAAMC,EAAAA,CAAUA,CAACC,GAAG,CAAC,eAA+B,OAAhB0D,EAAMM,MAAM,CAAC,EAAE,EAAI8C,GACpFjD,EAAc,GAAgB,EAAE,GAAGkB,CAAS,CAAE,EAAhB,CAAmBjF,CAAS,IAC9D,GAGR,EAAG,EAAE,EAGD,UAACT,MAAAA,UACG,UAAC/C,EAAAA,CAASA,CAAAA,CAACE,UAAU,WAAWD,KAAK,aACjC,UAACyI,EAAAA,CAAIA,CAAAA,CACD3C,MAAO,CACH4C,UAAW,MACXC,UAAW,kEACf,WAEA,UAACC,EAAAA,CAAqBA,CAAAA,CAACC,SAAUjB,EAAckB,IAAKpB,EAASqB,cAAe1B,EAAY2B,oBAAoB,WACxG,WAACP,EAAAA,CAAIA,CAAC5D,IAAI,YACN,UAAC3E,EAAAA,CAAGA,CAAAA,UACA,UAACC,EAAAA,CAAGA,CAAAA,UACA,UAACsI,EAAAA,CAAIA,CAAC7D,KAAK,WAAE/E,EAAE,kDAGvB,UAACoJ,KAAAA,CAAAA,GACD,UAAC/I,EAAAA,CAAGA,CAAAA,UACA,UAACC,EAAAA,CAAGA,CAAAA,CAACE,EAAE,IAACY,GAAI,EAAGD,GAAI,YACf,WAACkI,EAAAA,CAAIA,CAACC,KAAK,YACP,UAACD,EAAAA,CAAIA,CAACE,KAAK,EAACnJ,UAAU,0BACjBJ,EAAE,8CAEP,UAACwJ,EAAAA,EAASA,CAAAA,CACN1G,KAAK,QACL2G,GAAG,QACHC,QAAQ,IACR5I,MAAO0G,EAAW/H,KAAK,CACvBkK,UAAY7I,GAA8C,KAA/B8I,OAAO9I,GAAS,IAAIsH,IAAI,GACnDyB,aAAc,CACVF,UAAW3J,EAAE,uDACjB,EACAe,SApFlBM,CAoF4ByI,GAnF9C,GAAIzI,EAAE0I,MAAM,CAAE,CACV,GAAM,CAAEjH,MAAI,OAAEhC,CAAK,CAAE,CAAGO,EAAE0I,MAAM,CAChCtC,EAAekB,GAAe,EAC1B,GAAGA,CAAS,CACZ,CAAC7F,CAFyB,CAEpB,CAAEhC,EACZ,EACJ,CACJ,WAiFwB,UAACT,EAAAA,CAAGA,CAAAA,CAACD,UAAU,gBACX,WAACE,EAAAA,CAAGA,CAAAA,WACA,UAAC4E,EAAAA,CAAMA,CAAAA,CAAC9E,UAAU,OAAOO,KAAK,SAASwE,QAAQ,mBAC1CnF,EAAE,0CAEP,UAACkF,EAAAA,CAAMA,CAAAA,CAAC9E,UAAU,OAAOmD,QApGpC,CAoG6CyG,IAnG9DvC,EAAcgD,GAEdR,OAAOC,QAAQ,CAAC,EAAG,EACvB,EAgGgF/E,QAAQ,gBACnDnF,EAAE,yCAEP,UAACkD,IAAIA,CACDC,KAAK,6BACLnC,GAAK,OAFJkC,gCAID,UAACgC,EAAAA,CAAMA,CAAAA,CAACC,QAAQ,qBAAanF,EAAE,2DAUvE,uNCuDA,MAhMoB,KAChB,GAAM,GAAEA,CAAC,MAAE2K,CAAI,CAAE,CAAG1K,CAAAA,EAAAA,EAAAA,EA+LT2K,CA/LuB3K,CAAC,SA+Lb2K,CA9LhBC,CA8LiB,CA9Le,OAAlBF,EAAKG,QAAQ,CAAY,KAAOH,EAAKG,QAAQ,CAG3D,CAAClJ,EAAWC,EAAe,CAAGC,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAQ,EAAE,EAChD,EAAGC,EAAW,CAAGD,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,EAAC,GAC1B,CAACE,EAAWC,EAAa,CAAGH,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAC,GACrC,CAACI,EAASC,EAAW,CAAGL,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAC,IACjC,CAACpC,EAAYqL,EAAc,CAAGC,EAAAA,QAAc,CAAC,IAC7C,CAACC,EAAuBC,EAAyB,CAAGF,EAAAA,QAAc,EAAC,GACnE,CAAC5I,EAAaC,EAAS,CAAGP,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,EAAC,GACnC,CAACqJ,EAAcC,EAAgB,CAAGtJ,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAC,CAAC,GAC5C,CAACuJ,EAAS,CAAGvJ,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAVP+I,EAAc,SAAqB,OAAZA,GAAgB,YAarDS,EAAkB,MAAOjK,IAC3B,IAAMG,EAAQ+J,IAAAA,SAAW,CAAC3J,EAAW,CAAEpC,IAAK6B,EAAE0I,MAAM,CAACjH,IAAI,GACzD,GAAItB,EAAQ,CAAC,EAAG,CACZI,CAAS,CAACJ,EAAM,CAACgK,OAAO,CAAG,CAAC5J,CAAS,CAACJ,EAAM,CAACgK,OAAO,CACpD3J,EAAe,IAAID,EAAU,EAC7B,IAAM8B,EAAW,MAAMC,EAAAA,CAAUA,CAAC0E,KAAK,CAAC,WAAyB,OAAdhH,EAAE0I,MAAM,CAACjH,IAAI,EAAIlB,CAAS,CAACJ,EAAM,EAChFkC,GAAYA,EAASlE,GAAG,CACxB6E,CAD0B,CAC1BA,EAAKA,CAACC,OAAO,CAAC,GAAkCtE,MAAAA,CAA/B0D,EAASjE,KAAK,CAACoL,EAAY,CAAC,KAA4B,OAAzB7K,EAAE,yBAElDqE,EAAAA,EAAKA,CAACE,KAAK,CAACb,EAEpB,MACIW,CADG,CACHA,EAAKA,CAACE,KAAK,CAACvE,EAAE,iBAEtB,EAEMyL,EAAS,OAAC,KAAEjM,CAAG,SAAEgM,CAAO,CAA8B,SACxD,UAACnC,EAAAA,CAAIA,CAACqC,KAAK,EACPtL,UAAU,OACVO,KAAK,SACLmC,KAAMtD,EACNiK,GAAIjK,EACJmM,MAAM,GACNC,QAASJ,EACTzK,SAAU,GAAOuK,EAAgBjK,MAGnCwB,EAAU,CACZ,CACIC,KAAM9C,EAAE,gBACR+C,SAAUsI,EACVrI,KAAM,GAAaI,GAAKA,EAAE3D,KAAK,EAAI2D,EAAE3D,KAAK,CAACoL,EAAY,CAAGzH,EAAE3D,KAAK,CAACoL,EAAY,CAAG,EACrF,EACA,CACI/H,KAAM9C,EAAE,cACR+C,SAAU,cACVC,KAAM,GAAaI,GAAKA,EAAEyI,WAAW,EAAIzI,EAAEyI,WAAW,CAACpM,KAAK,CAAG2D,EAAEyI,WAAW,CAACpM,KAAK,CAAG,EACzF,EACA,CACIqD,KAAM9C,EAAE,aACR+C,SAAU,UACVC,KAAM,GAAc,UAACyI,EAAAA,CAAQ,GAAGvH,CAAG,EACvC,EACA,CACIpB,KAAM9C,EAAE,UACR+C,SAAU,GACVC,KAAM,GACF,WAACC,MAAAA,WACG,UAACC,IAAIA,CAACC,KAAK,6BAA6BnC,GAAI,OAAvCkC,uBAA2E,OAANE,EAAE5D,GAAG,WAE3E,UAAC6D,IAAAA,CAAEjD,UAAU,uBAEV,OAEP,UAAC0L,OAAAA,CAAKvI,QAAS,IAAMC,EAAWJ,GAAI6C,MAAO,CAAE8F,OAAQ,SAAU,WAC3D,UAAC1I,IAAAA,CAAEjD,UAAU,8BAI7B,EACH,CAEDqE,CAAAA,EAAAA,EAAAA,SAAAA,CAASA,CAAC,KACNuH,GACJ,EAAG,EAAE,EAEL,IAAMC,EAAe,CACjBxJ,KAAM,CAAE,CAAC4I,EAAS,CAAE,KAAM,EAC1B3I,MAAOR,EACPS,KAAM,EACNC,MAAO,CAAC,CACZ,EAEMoJ,EAAiB,UACnBjK,GAAW,GACX,IAAM2B,EAAW,MAAMC,EAAAA,CAAUA,CAACC,GAAG,CAAC,UAAWqI,GAC7CvI,GAAYA,EAASG,IAAI,EAAIH,EAASG,IAAI,CAACC,MAAM,CAAG,GAAG,CACvDjC,EAAe6B,EAASG,IAAI,EAC5B5B,EAAayB,EAASK,UAAU,EAChChC,EAAW,IAEnB,EAQMiC,EAAsB,MAAOC,EAAiBtB,KAChDsJ,EAAavJ,KAAK,CAAGuB,EACrBgI,EAAatJ,IAAI,CAAGA,EACpBZ,GAAW,GACX,IAAM2B,EAAW,MAAMC,EAAAA,CAAUA,CAACC,GAAG,CAAC,UAAWqI,GAC7CvI,GAAYA,EAASG,IAAI,EAAIH,EAASG,IAAI,CAACC,MAAM,CAAG,GAAG,CACvDjC,EAAe6B,EAASG,IAAI,EAC5B1B,EAAW8B,GACXlC,GAAW,GAEnB,EAEMyB,EAAa,MAAOU,IACtBkH,EAAgBlH,EAAI1E,GAAG,EACvB6C,GAAS,EACb,EAEM8B,EAAe,UACjB,GAAI,CACA,MAAMR,EAAAA,CAAUA,CAACS,MAAM,CAAC,WAAwB,OAAb+G,IACnCa,IACA3J,GAAS,GACTgC,EAAAA,EAAKA,CAACC,OAAO,CAACtE,EAAE,uDACpB,CAAE,MAAOuE,EAAO,CACZF,EAAAA,EAAKA,CAACE,KAAK,CAACvE,EAAE,iDAClB,CACJ,EAEMwE,EAAY,IAAMnC,GAAS,GAE3B6J,EAAyBlB,EAAAA,OAAa,CAAC,KAQzC,IAAMmB,EAAY,IACVC,GAAG,GACUxJ,KAAK,CAAG,CAAE,CAACyI,EAAS,CAAEe,EAAE,EAEzCJ,GACJ,EAEMK,EAAoBd,IAAAA,QAAU,CAAC,GAAeY,EAAUC,GAAIE,OAAOC,KAAgC,GAAK,KAO9G,MAAO,UAAC7K,EAAAA,OAAiBA,CAAAA,CAAC9B,SALL,CAKekK,GAJhCiB,EAAc1J,EAAE0I,MAAM,CAACjJ,KAAK,EAC5BuL,EAAkBhL,EAAE0I,MAAM,CAACjJ,KAAK,CACpC,EAEkDhB,QArB9B,CAqBuC0M,IApBnD9M,IACAwL,EAAyB,CAACD,GAC1BF,EAAc,IAEtB,EAgBwErL,WAAYA,GACxF,EAAG,CAACA,EAAW,EAEf,MACI,WAACuD,MAAAA,WACG,WAACyB,EAAAA,CAAKA,CAAAA,CAACC,KAAMvC,EAAawC,OAAQJ,YAC9B,UAACE,EAAAA,CAAKA,CAACG,MAAM,EAACC,WAAW,aACrB,UAACJ,EAAAA,CAAKA,CAACK,KAAK,WAAE/E,EAAE,oBAEpB,UAAC0E,EAAAA,CAAKA,CAACM,IAAI,WAAEhF,EAAE,sCACf,WAAC0E,EAAAA,CAAKA,CAACO,MAAM,YACT,UAACC,EAAAA,CAAMA,CAAAA,CAACC,QAAQ,YAAY5B,QAASiB,WAChCxE,EAAE,YAEP,UAACkF,EAAAA,CAAMA,CAAAA,CAACC,QAAQ,UAAU5B,QAASY,WAC9BnE,EAAE,eAKf,UAACoF,EAAAA,CAAQA,CAAAA,CACLvC,QAASA,EACTgB,KAAMjC,EACNI,UAAWA,EACXqD,WAAW,EACXoH,SAAS,IACTxB,sBAAuBA,EACvByB,mBAAoBR,EACpBlI,oBAAqBA,EACrBsB,iBAvFa,CAuFKA,GAtF1B2G,EAAavJ,KAAK,CAAGR,EACrB+J,EAAatJ,IAAI,CAAGA,EACpBqJ,GACJ,MAuFJ,kOC7IA,MAjDsB,QAiCdlG,EAAAA,EAhCN,GAAM,GAAE9F,CAAC,CAAE,CAAGC,CAAAA,EAAAA,EAAAA,EAAAA,CAAcA,CAAC,UACvB0M,CA+CoBC,CA/CA,CA+CC,GA7CvB,WAAC1M,EAAAA,CAASA,CAAAA,CAAC+F,MAAO,CAAEC,UAAW,QAAS,EAAG/F,KAAK,IAACC,UAAU,gBACzD,UAACC,EAAAA,CAAGA,CAAAA,UACF,UAACC,EAAAA,CAAGA,CAAAA,CAACC,GAAI,YACP,UAAC4F,EAAAA,CAAWA,CAAAA,CAAC1G,MAAOO,EAAE,yCAG1B,UAACK,EAAAA,CAAGA,CAAAA,UACF,UAACC,EAAAA,CAAGA,CAAAA,CAACC,GAAI,YACP,UAAC2C,IAAIA,CACHC,KAAK,6BACLnC,GAAG,OAFAkC,mCAIH,UAACgC,EAAAA,CAAMA,CAAAA,CAACC,QAAQ,YAAYiB,KAAK,cAChCpG,EAAE,6CAKT,UAACK,EAAAA,CAAGA,CAAAA,CAACD,UAAU,gBACb,UAACE,EAAAA,CAAGA,CAAAA,CAACC,GAAI,YACP,UAACsM,EAAAA,OAAaA,CAAAA,CAAAA,UAOlBC,EAAmBC,CAAAA,EAAAA,EAAAA,eAAAA,CAAeA,CAAC,IAAM,UAACJ,EAAAA,CAAAA,IAC1C7G,EAAYS,CAAAA,EAAAA,EAAAA,EAAAA,CAAWA,CAAC,GAAWT,SACzC,IAAI,GAAEA,GAAAA,OAAAA,EAAAA,EAAOU,IAAPV,OAAkB,GAAlBA,MAAAA,GAAAA,EAAAA,QAAoBkH,EAApBlH,KAAAA,EAAAA,CAA8B,CAAC,GAA/BA,UAA4C,EAIhD,CAJmD,EAInD,OAACgH,EAAAA,CAAAA,GAHM,UAACpG,EAAAA,OAAeA,CAAAA,CAAAA,EAK3B,yPCmHA,MArJyBY,IACrB,GAAM,CAAEtH,CAAC,CAAE,CAAGC,CAAAA,EAAAA,EAAAA,EAAAA,CAAcA,CAAC,QAoJlBgN,EAlJLC,EAAsB,CACxBzN,MAAO,GACPmG,CAgJsBqH,EAAC,EAhJjB,EACV,EAEM,CAACzF,EAAYC,EAAc,CAAG3F,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAcoL,GAEpDvF,EAAWL,EAAMM,MAAM,EAAIN,uBAAMM,MAAM,CAAC,EAAE,EAA2BN,EAAMM,MAAM,CAAC,EAAE,CAEpFC,EAAUC,CAAAA,EAAAA,EAAAA,MAAAA,CAAMA,CAAC,MAQjBgC,EAAe,IACjB,GAAIzI,EAAE0I,MAAM,CAAE,CACV,GAAM,MAAEjH,CAAI,OAAEhC,CAAK,CAAE,CAAGO,EAAE0I,MAAM,CAChCtC,EAAc,GAAgB,EAC1B,GAAGkB,CAAS,CACZ,CAAC7F,CAFyB,CAEpB,CAAEhC,EACZ,EACJ,CACJ,EAEMiH,EAAe,MAAOC,QAOpBtE,EACAuE,EAPJD,EAAME,cAAc,GACpB,IAAMC,EAAM,CACR1I,MAAO+H,EAAW/H,KAAK,CAAC2I,IAAI,GAC5BxC,KAAM4B,EAAW5B,IAAI,EAKrB+B,GACAM,EAAW,KADD,4DAEVvE,EAAW,MAAMC,EAAAA,CAAUA,CAAC0E,KAAK,CAAC,gBAAgC,OAAhBf,EAAMM,MAAM,CAAC,EAAE,EAAIO,KAErEF,EAAW,+DACXvE,EAAW,MAAMC,EAAAA,CAAUA,CAAC2E,IAAI,CAAC,eAAgBH,IAEjDzE,GAAYA,EAASlE,GAAG,EAAE,EAC1B6E,EAAKA,CAACC,OAAO,CAACtE,EAAEiI,IAChBM,IAAAA,IAAW,CAAC,+BAER7E,OAAAA,EAAAA,KAAAA,EAAAA,EAAU8E,SAAAA,CAAV9E,GAAwB,KACxBW,EAD+B,EAC1BA,CAACE,KAAK,CAACvE,EAAE,yBAEdqE,EAAAA,EAAKA,CAACE,KAAK,CAACb,EAGxB,EAiBA,MAfAe,CAAAA,EAAAA,EAAAA,SAAAA,CAASA,CAAC,KACN,IAAMiB,EAAoB,CACtB9C,MAAO,CAAC,EACRH,KAAM,CAAEhD,MAAO,KAAM,EACrBiD,MAAO,GACX,EACIiF,GAKA9B,CAJ2B,MADjB,IAEN,IAAMnC,EAAwB,MAAMC,EAAAA,CAAUA,CAACC,GAAG,CAAC,gBAAgC,OAAhB0D,EAAMM,MAAM,CAAC,EAAE,EAAIlC,GACtF+B,EAAekB,GAAe,EAAE,GAAGA,CAAS,CAAE,EAAhB,CAAmBjF,CAAQ,CAAC,GAC9D,GAGR,EAAG,EAAE,EAGD,UAACT,MAAAA,UACG,UAAC/C,EAAAA,CAASA,CAAAA,CAACE,UAAU,WAAWD,KAAK,aACjC,UAACyI,EAAAA,CAAIA,CAAAA,CACD3C,MAAO,CACH4C,UAAW,MACXC,UAAW,kEACf,WAEA,UAACC,EAAAA,CAAqBA,CAAAA,CAACC,SAAUjB,EAAckB,IAAKpB,EAASqB,cAAe1B,EAAY2B,mBAAoB,YACxG,WAACP,EAAAA,CAAIA,CAAC5D,IAAI,YACN,UAAC3E,EAAAA,CAAGA,CAAAA,UACA,UAACC,EAAAA,CAAGA,CAAAA,UACA,UAACsI,EAAAA,CAAIA,CAAC7D,KAAK,WAAE/E,EAAE,mDAGvB,UAACoJ,KAAAA,CAAAA,GACD,WAAC/I,EAAAA,CAAGA,CAAAA,WACA,UAACC,EAAAA,CAAGA,CAAAA,CAACE,EAAE,IAACY,GAAI,EAAGD,GAAI,YACf,WAACkI,EAAAA,CAAIA,CAACC,KAAK,YACP,UAACD,EAAAA,CAAIA,CAACE,KAAK,EAACnJ,UAAU,0BACjBJ,EAAE,+CAEP,UAACwJ,EAAAA,EAASA,CAAAA,CACN1G,KAAK,QACL2G,GAAG,QACHC,QAAQ,IACR5I,MAAO0G,EAAW/H,KAAK,CACvBkK,UAAW,GAAkD,KAA/BC,OAAO9I,GAAS,IAAIsH,IAAI,GACtDyB,aAAc,CACVF,UAAW3J,EAAE,wDACjB,EACAe,SAAU+I,SAItB,UAACxJ,EAAAA,CAAGA,CAAAA,CAACE,EAAE,IAACY,GAAI,EAAGD,GAAI,YACf,WAACkI,EAAAA,CAAIA,CAACC,KAAK,YACP,UAACD,EAAAA,CAAIA,CAACE,KAAK,EAACnJ,UAAU,0BACjBJ,EAAE,wCAEP,UAACwJ,EAAAA,EAASA,CAAAA,CACN1G,KAAK,OACL2G,GAAG,OACHC,QAAQ,IACR5I,MAAO0G,EAAW5B,IAAI,CACtB+D,UAAW,GAAkD,KAA/BC,OAAO9I,GAAS,IAAIsH,IAAI,GACtDyB,aAAc,CAAEF,UAAW3J,EAAE,iDAAiD,EAC9Ee,SAAU+I,YAK1B,UAACzJ,EAAAA,CAAGA,CAAAA,CAACD,UAAU,gBACX,WAACE,EAAAA,CAAGA,CAAAA,WACA,UAAC4E,EAAAA,CAAMA,CAAAA,CAAC9E,UAAU,OAAOO,KAAK,SAASwE,QAAQ,mBAC1CnF,EAAE,0CAEP,UAACkF,EAAAA,CAAMA,CAAAA,CAAC9E,UAAU,OAAOmD,QArHpC,CAqH6CyG,IApH9DvC,EAAcyF,GAEdjD,OAAOC,QAAQ,CAAC,EAAG,EACvB,EAiHgF/E,QAAQ,gBACnDnF,EAAE,yCAEP,UAACkD,IAAIA,CACDC,KAAK,6BACLnC,GAAK,OAFJkC,+BAID,UAACgC,EAAAA,CAAMA,CAAAA,CAACC,QAAQ,qBAAanF,EAAE,2DAUvE,6KC5HA,MA7B4B,QAapB8F,EAAAA,EAZN,GAAM,GAAE9F,CAAC,CAAE,CAAGC,CAAAA,EAAAA,EAAAA,EA4BDkN,CA5BelN,CAAC,UACvBmN,EAA0B,IAE5B,WAAClN,EAAAA,CAASA,CAAAA,CAACC,KAAK,IAACC,UAAU,gBACzB,UAAC+F,EAAAA,CAAWA,CAAAA,CAAC1G,MAAOO,EAAE,gDACtB,UAACqN,EAAAA,OAAgBA,CAAAA,CAAAA,MAKjBC,EAA8BC,CAAAA,EAAAA,EAAAA,0BAAAA,CAA0BA,CAAC,IAAM,UAACH,EAAAA,CAAAA,IAChEtH,EAAYS,CAAAA,EAAAA,EAAAA,EAAAA,CAAWA,CAAC,GAAgBT,SAC9C,IAAI,GAAEA,GAAAA,OAAAA,EAAAA,EAAOU,IAAPV,OAAOU,GAAPV,OAAAA,EAAAA,EAAoB0H,WAAAA,EAApB1H,KAAAA,EAAAA,CAAiC,CAAC,GAAlCA,UAA+C,EAInD,CAJsD,EAItD,OAACwH,EAAAA,CAAAA,GAHM,UAAC5G,EAAAA,OAAeA,CAAAA,CAAAA,EAK3B,mBC/BA,4CACA,6BACA,WACA,OAAe,EAAQ,KAAkD,CACzE,EACA,SAFsB,gPCoJtB,MAtI6B,IACzB,IAAM+G,EAA2B,CAC7BjO,IAAK,GACLC,MAAO,EACX,EACM,GAAEO,CAAC,CAAE,CAAGC,CAAAA,EAAAA,EAAAA,EAAAA,CAAcA,CAAC,KAiIEyN,EAAC,GAhI1B,CAAClG,EAAYC,EAAc,CAAG3F,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAmB2L,GAEzD9F,EAAoBL,EAAMM,MAAM,EAAIN,4BAAMM,MAAM,CAAC,EAAE,EAAgCN,EAAMM,MAAM,CAAC,EAAE,CAElGC,EAAUC,CAAAA,EAAAA,EAAAA,MAAAA,CAAMA,CAAC,MAkBjBC,EAAe,MAAOC,QAMpBtE,EACAuE,EANJD,EAAME,cAAc,GACpB,IAAMC,EAAM,CACR1I,MAAO+H,EAAW/H,KAAK,CAAC2I,IAAI,EAChC,EAIIT,GACAM,EAAW,KADD,uEAEVvE,EAAW,MAAMC,EAAAA,CAAUA,CAAC0E,KAAK,CAAC,qBAAqC,OAAhBf,EAAMM,MAAM,CAAC,EAAE,EAAIO,KAE1EF,EAAW,0EACXvE,EAAW,MAAMC,EAAAA,CAAUA,CAAC2E,IAAI,CAAC,oBAAqBH,IAEtDzE,GAAYA,EAASlE,GAAG,EAAE,EAC1B6E,EAAKA,CAACC,OAAO,CAACtE,EAAEiI,IAChBM,IAAAA,IAAW,CAAC,oCAER7E,OAAAA,EAAAA,KAAAA,EAAAA,EAAU8E,SAAAA,CAAV9E,GAAwB,KACxBW,EAD+B,EAC1BA,CAACE,KAAK,CAACvE,EAAE,yBAEdqE,EAAAA,EAAKA,CAACE,KAAK,CAACb,EAGxB,EAiBA,MAfAe,CAAAA,EAAAA,EAAAA,SAAAA,CAASA,CAAC,KACN,IAAMkJ,EAAyB,CAC3B/K,MAAO,CAAC,EACRH,KAAM,CAAEhD,MAAO,KAAM,EACrBiD,MAAO,GACX,EACIiF,GAKAiG,CAJgC,MADtB,IAEN,IAAMlK,EAA6B,MAAMC,EAAAA,CAAUA,CAACC,GAAG,CAAC,qBAAqC,OAAhB0D,EAAMM,MAAM,CAAC,EAAE,EAAI+F,GAChGlG,EAAc,GAAgB,EAAE,GAAGkB,CAAS,CAAE,EAAhB,CAAmBjF,CAAQ,GAC7D,IAGR,EAAG,EAAE,EAGD,UAACT,MAAAA,UACG,UAAC/C,EAAAA,CAASA,CAAAA,CAACE,UAAU,WAAWD,KAAK,aACjC,UAACyI,EAAAA,CAAIA,CAAAA,CACD3C,MAAO,CACH4C,UAAW,MACXC,UAAW,kEACf,WAEA,UAACC,EAAAA,CAAqBA,CAAAA,CAACC,SAAUjB,EAAckB,IAAKpB,EAASqB,cAAe1B,EAAY2B,oBAAoB,WACxG,WAACP,EAAAA,CAAIA,CAAC5D,IAAI,YACN,UAAC3E,EAAAA,CAAGA,CAAAA,UACA,UAACC,EAAAA,CAAGA,CAAAA,UACA,UAACsI,EAAAA,CAAIA,CAAC7D,KAAK,WAAE/E,EAAE,8DAGvB,UAACoJ,KAAAA,CAAAA,GACD,UAAC/I,EAAAA,CAAGA,CAAAA,UACA,UAACC,EAAAA,CAAGA,CAAAA,CAACE,EAAE,IAACY,GAAI,EAAGD,GAAI,YACf,WAACkI,EAAAA,CAAIA,CAACC,KAAK,YACP,UAACD,EAAAA,CAAIA,CAACE,KAAK,EAACnJ,UAAU,0BACjBJ,EAAE,0DAEP,UAACwJ,EAAAA,EAASA,CAAAA,CACN1G,KAAK,QACL2G,GAAG,QACHC,QAAQ,IACR5I,MAAO0G,EAAW/H,KAAK,CACvBkK,UAAW,GAAgB7I,OAAMsH,IAAI,GACrCyB,aAAc,CACVF,UAAW3J,EACP,mEAER,EACAe,SAtFlBM,CAsF4ByI,GArF9C,GAAIzI,EAAE0I,MAAM,CAAE,CACV,GAAM,MAAEjH,CAAI,OAAEhC,CAAK,CAAE,CAAGO,EAAE0I,MAAM,CAChCtC,EAAc,GAAgB,EAC1B,GAAGkB,CAAS,CACZ,CAAC7F,CAFyB,CAEpB,CAAEhC,EACZ,EACJ,CACJ,WAmFwB,UAACT,EAAAA,CAAGA,CAAAA,CAACD,UAAU,gBACX,WAACE,EAAAA,CAAGA,CAAAA,WACA,UAAC4E,EAAAA,CAAMA,CAAAA,CAAC9E,UAAU,OAAOO,KAAK,SAASwE,QAAQ,mBAC1CnF,EAAE,gDAEP,UAACkF,EAAAA,CAAMA,CAAAA,CAAC9E,UAAU,OAAOmD,QAtGpC,CAsG6CyG,IArG9DvC,EAAcgG,GAEdxD,OAAOC,QAAQ,CAAC,EAAG,EACvB,EAkGgF/E,QAAQ,gBACnDnF,EAAE,+CAEP,UAACkD,IAAIA,CACDC,KAAK,6BACLnC,GAAK,OAFJkC,oCAID,UAACgC,EAAAA,CAAMA,CAAAA,CAACC,QAAQ,qBACXnF,EAAE,iEAW/C,mQCuBA,MA3JqB,IACjB,IAAM6N,EAAmB,CACrBpO,MAAO,GACPmG,KAAM,GACNkI,YAuJmBC,EAtJvB,EACM,GAAE/N,CAAC,CAAE,CAAGC,CAAAA,EAAAA,EAAAA,EAAAA,CAAcA,CAAC,UACvB,CAACuH,EAAYC,EAAc,CAAG3F,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAM+L,GAE5ClG,EAAWL,EAAMM,MAAM,EAAwB,kBAApBN,EAAMM,MAAM,CAAC,EAAE,EAAwBN,EAAMM,MAAM,CAAC,EAAE,CAEjFG,EAAe,MAAOC,EAAYgG,SAQhCtK,EACAuE,EARJD,EAAME,cAAc,GACpB,IAAMC,EAAM,CACR1I,MAAO+H,EAAW/H,KAAK,CAAC2I,IAAI,GAC5BxC,KAAM4B,EAAW5B,IAAI,CACrBkI,YAAatG,EAAWsG,WAAW,EAKnCnG,GACAM,EAAW,KADD,iDAEVvE,EAAW,MAAMC,EAAAA,CAAUA,CAAC0E,KAAK,CAAC,aAA6B,OAAhBf,EAAMM,MAAM,CAAC,EAAE,EAAIO,KAElEF,EAAW,oDACXvE,EAAW,MAAMC,EAAAA,CAAUA,CAAC2E,IAAI,CAAC,YAAaH,IAE9CzE,GAAYA,EAASlE,GAAG,EAAE,EAC1B6E,EAAKA,CAACC,OAAO,CAACtE,EAAEiI,IAChBM,IAAAA,IAAW,CAAC,4BAEZlE,EAAAA,EAAKA,CAACE,KAAK,CAACb,EAEpB,EAOMoG,EAAgBzI,IAClB,GAAIA,EAAE0I,MAAM,CAAE,CACV,GAAM,MAAEjH,CAAI,CAAEhC,OAAK,CAAE,CAAGO,EAAE0I,MAAM,CAChCtC,EAAc,GAAqB,EAC/B,GAAGkB,CAAS,CACZ,CAAC7F,CAF8B,CAEzB,CAAEhC,EACZ,EACJ,CACJ,EAEMmN,EAAoB,IACtBxG,EAAc,GAAqB,EAC/B,GAAGkB,CAAS,CACZmF,EAF+B,UAElBhN,EACjB,EACJ,EAEA2D,CAAAA,EAAAA,EAAAA,SAAAA,CAASA,CAAC,KACN,IAAMyJ,EAAiB,CACnBtL,MAAO,CAAC,EACRH,KAAM,CAAEhD,MAAO,KAAM,EACrBiD,MAAO,GACX,EAEIiF,GAKAwG,CAJwB,MADd,IAEN,IAAMzK,EAAqB,MAAMC,EAAAA,CAAUA,CAACC,GAAG,CAAC,aAA6B,OAAhB0D,EAAMM,MAAM,CAAC,EAAE,EAAIsG,GAChFzG,EAAc,GAAqB,EAAE,GAAGkB,CAAS,CAAE,EAAhB,CAAmBjF,CAAQ,IAClE,GAGR,EAAG,EAAE,EAEL,IAAMmE,EAAUC,CAAAA,EAAAA,EAAAA,MAAAA,CAAMA,CAAC,MAEvB,MACI,UAAC5H,EAAAA,CAASA,CAAAA,CAACE,UAAU,WAAWD,KAAK,aACjC,UAACyI,EAAAA,CAAIA,CAAAA,CACD3C,MAAO,CACH4C,UAAW,MACXC,UAAW,kEACf,WAEA,UAACC,EAAAA,CAAqBA,CAAAA,CAACC,SAAUjB,EAAckB,IAAKpB,EAASqB,cAAe1B,EAAY2B,oBAAoB,WACxG,WAACP,EAAAA,CAAIA,CAAC5D,IAAI,YACN,UAAC3E,EAAAA,CAAGA,CAAAA,UACA,UAACC,EAAAA,CAAGA,CAAAA,UACA,UAACsI,EAAAA,CAAIA,CAAC7D,KAAK,WAAE4C,EAAW3H,EAAE,sCAAwCA,EAAE,2CAG5E,UAACoJ,KAAAA,CAAAA,GACD,WAAC/I,EAAAA,CAAGA,CAAAA,WACA,UAACC,EAAAA,CAAGA,CAAAA,CAACE,EAAE,IAACY,GAAI,EAAGD,GAAI,YACf,WAACkI,EAAAA,CAAIA,CAACC,KAAK,YACP,UAACD,EAAAA,CAAIA,CAACE,KAAK,EAACnJ,UAAU,0BACjBJ,EAAE,wCAEP,UAACwJ,EAAAA,EAASA,CAAAA,CACN1G,KAAK,QACL2G,GAAG,QACHC,QAAQ,IACR5I,MAAO0G,EAAW/H,KAAK,CACvBkK,UAAW,GAAoC,KAAjB7I,EAAMsH,IAAI,GACxCyB,aAAc,CACVF,UAAW3J,EAAE,iDACjB,EACAe,SAAU+I,SAItB,UAACxJ,EAAAA,CAAGA,CAAAA,CAACE,EAAE,IAACY,GAAI,EAAGD,GAAI,YACf,WAACkI,EAAAA,CAAIA,CAACC,KAAK,YACP,UAACD,EAAAA,CAAIA,CAACE,KAAK,WAAEvJ,EAAE,gCACf,UAACwJ,EAAAA,EAASA,CAAAA,CACN1G,KAAK,OACL2G,GAAG,OACHC,QAAQ,IACR5I,MAAO0G,EAAW5B,IAAI,CACtBiE,aAAc,CAACF,UAAW3J,EAAE,yCAAyC,EACrEe,SAAU+I,YAK1B,UAACzJ,EAAAA,CAAGA,CAAAA,UACA,UAACC,EAAAA,CAAGA,CAAAA,UACA,WAAC+I,EAAAA,CAAIA,CAACC,KAAK,YACP,UAACD,EAAAA,CAAIA,CAACE,KAAK,WAAEvJ,EAAE,uCACf,UAACoO,EAAAA,CAAeA,CAAAA,CAACC,YAAa7G,EAAWsG,WAAW,CAAE/M,SAAU,GAAiBkN,EAAkBK,YAI/G,UAACjO,EAAAA,CAAGA,CAAAA,CAACD,UAAU,gBACX,WAACE,EAAAA,CAAGA,CAAAA,WACA,UAAC4E,EAAAA,CAAMA,CAAAA,CAAC9E,UAAU,OAAOO,KAAK,SAASwE,QAAQ,mBAC1CnF,EAAE,kCAEP,UAACkF,EAAAA,CAAMA,CAAAA,CAAC9E,UAAU,OAAOmD,QAtGhC,CAsGyCyG,IArG1DvC,EAAcoG,GAEd5D,OAAOC,QAAQ,CAAC,EAAG,EACvB,EAkG4E/E,QAAQ,gBACnDnF,EAAE,iCAEP,UAACkD,IAAIA,CACDC,KAAK,6BACLnC,GAAK,OAFJkC,4BAID,UAACgC,EAAAA,CAAMA,CAAAA,CAACC,QAAQ,qBAAanF,EAAE,iDASnE,8KChCA,MAlIwB,IACpB,GAAM,CAAC4B,EAAWC,EAAe,CAAGC,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAC,EAAE,EACzC,EAAGC,EAAW,CAAGD,CAAAA,EAAAA,EAAAA,CAgIGyM,EAAC,KAhIJzM,CAAQA,EAAC,GAC1B,CAACE,EAAWC,EAAa,CAAGH,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAC,GACrC,CAACI,EAASC,EAAW,CAAGL,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAC,IACjC,CAACM,EAAaC,EAAS,CAAGP,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAC,IACnC,CAAC0M,EAAkBC,EAAoB,CAAG3M,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAC,CAAC,GACpD,GAAE9B,CAAC,CAAE,CAAGC,CAAAA,EAAAA,EAAAA,EAAAA,CAAcA,CAAC,UAGvB4C,EAAU,CACZ,CACIC,KAAM9C,EAAE,wCACR+C,SAAU,OACd,EACA,CACID,KAAM9C,EAAE,iCACR+C,SAAU,OACVC,KAAOI,GAAWA,EAAEwC,IACxB,EACA,CACI9C,KAAM9C,EAAE,eACR+C,SAAU,cACVC,KAAM,GAAYI,EAAE0K,WAAW,CAACY,OAAO,CAAC,WAAY,GACxD,EACA,CACI5L,KAAM9C,EAAE,UACR+C,SAAU,GACVC,KAAM,GACF,WAACC,MAAAA,WACG,UAACC,IAAIA,CAACC,KAAK,6BAA6BnC,GAAI,OAAvCkC,6BAAiF,OAANE,EAAE5D,GAAG,WAEjF,UAAC6D,IAAAA,CAAEjD,UAAU,uBAEV,OAEP,UAACkD,IAAAA,CAAEC,QAAS,IAAMC,EAAWJ,YACzB,UAACC,IAAAA,CAAEjD,UAAU,8BAI7B,EACH,CAEDqE,CAAAA,EAAAA,EAAAA,SAAAA,CAASA,CAAC,KACNkK,GACJ,EAAG,EAAE,EAEL,IAAMC,EAAmB,CACrBnM,KAAM,CAAEhD,MAAO,KAAM,EACrBiD,MAAOR,EACPS,KAAM,EACNC,MAAO,CAAC,CACZ,EAEM+L,EAAqB,UACvB5M,GAAW,GACX,IAAM2B,EAAW,MAAMC,EAAAA,CAAUA,CAACC,GAAG,CAAC,cAAegL,GACjDlL,GAAYA,EAASG,IAAI,EAAIH,EAASG,IAAI,CAACC,MAAM,CAAG,GAAG,CACvDjC,EAAe6B,EAASG,IAAI,EAC5B5B,EAAayB,EAASK,UAAU,EAChChC,GAAW,GAEnB,EAQMiC,EAAsB,MAAOC,EAAiBtB,KAChDiM,EAAiBlM,KAAK,CAAGuB,EACzB2K,EAAiBjM,IAAI,CAAGA,EACxBZ,EAAW,IACX,IAAM2B,EAAW,MAAMC,EAAAA,CAAUA,CAACC,GAAG,CAAC,cAAegL,GACjDlL,GAAYA,EAASG,IAAI,EAAIH,EAASG,IAAI,CAACC,MAAM,CAAG,GAAG,CACvDjC,EAAe6B,EAASG,IAAI,EAC5B1B,EAAW8B,GACXlC,EAAW,IAEnB,EAEMyB,EAAa,MAAOU,IACtBuK,EAAoBvK,EAAI1E,GAAG,EAC3B6C,GAAS,EACb,EAEM8B,EAAe,UACjB,GAAI,CACA,MAAMR,EAAAA,CAAUA,CAACS,MAAM,CAAC,eAAgC,OAAjBoK,IACvCG,IACAtM,GAAS,GACTgC,EAAAA,EAAKA,CAACC,OAAO,CAACtE,EAAE,gEACpB,CAAE,MAAOuE,EAAO,CACZF,EAAAA,EAAKA,CAACE,KAAK,CAACvE,EAAE,0DAClB,CACJ,EAEMwE,EAAY,IAAMnC,GAAS,GAEjC,MACI,WAACY,MAAAA,WACG,WAACyB,EAAAA,CAAKA,CAAAA,CAACC,KAAMvC,EAAawC,OAAQJ,YAC9B,UAACE,EAAAA,CAAKA,CAACG,MAAM,EAACC,WAAW,aACrB,UAACJ,EAAAA,CAAKA,CAACK,KAAK,WAAE/E,EAAE,iDAEpB,UAAC0E,EAAAA,CAAKA,CAACM,IAAI,WAAEhF,EAAE,mEACf,WAAC0E,EAAAA,CAAKA,CAACO,MAAM,YACT,UAACC,EAAAA,CAAMA,CAAAA,CAACC,QAAQ,YAAY5B,QAASiB,WAChCxE,EAAE,YAEP,UAACkF,EAAAA,CAAMA,CAAAA,CAACC,QAAQ,UAAU5B,QAASY,WAC9BnE,EAAE,eAKf,UAACoF,EAAAA,CAAQA,CAAAA,CACLvC,QAASA,EACTgB,KAAMjC,EACNI,UAAWA,EACXqD,WAAW,EACXrB,oBAAqBA,EACrBsB,iBA3Dc3C,CA2DI2C,GA1D1BsJ,EAAiBlM,KAAK,CAAGR,EACzB0M,EAAiBjM,IAAI,CAAGA,EACxBgM,GACJ,MA2DJ,yPCOA,MAhI4B,IACxB,IAAME,EAA0B,CAC5BpP,MAAO,EACX,EAEM,CAAC+H,EAAYC,EAAc,CAAG3F,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAkB+M,GAExDlH,EAAoBL,EAyHK,MAzHO,EAAwB,0BAApBA,EAAMM,MAAM,CAAC,EAAE,EAAgCN,EAAMM,MAAM,CAAC,EAAE,CAClG,GAAE5H,CAAC,CAAE,CAAGC,CAAAA,EAAAA,EAAAA,EAAAA,CAAcA,CAAC,UAEvB4H,EAAUC,CAAAA,EAAAA,EAAAA,MAAAA,CAAMA,CAAC,MAkBjBC,EAAe,MAAOC,QAGbR,MAGP9D,EACAuE,EANJD,EAAME,cAAc,GACpB,IAAMC,EAAM,CACR1I,KAAK,OAAE+H,GAAAA,OAAAA,EAAAA,EAAY/H,KAAK,EAAjB+H,EAAAA,GAAAA,EAAAA,EAAmBY,GAAnBZ,CAAuB,EAClC,EAIIG,GACAM,EAAW,KADD,0CAEVvE,EAAW,MAAMC,EAAAA,CAAUA,CAAC0E,KAAK,CAAC,oBAAoC,OAAhBf,EAAMM,MAAM,CAAC,EAAE,EAAIO,KAEzEF,EAAW,yCACXvE,EAAW,MAAMC,EAAAA,CAAUA,CAAC2E,IAAI,CAAC,mBAAoBH,IAErDzE,GAAYA,EAASlE,GAAG,EAAE,EAC1B6E,EAAKA,CAACC,OAAO,CAACtE,EAAEiI,IAChBM,IAAAA,IAAW,CAAC,oCAER7E,OAAAA,EAAAA,KAAAA,EAAAA,EAAU8E,SAAAA,CAAV9E,GAAwB,KACxBW,EAD+B,EAC1BA,CAACE,KAAK,CAACvE,EAAE,yBAEdqE,EAAAA,EAAKA,CAACE,KAAK,CAACb,EAGxB,EAiBA,MAfAe,CAAAA,EAAAA,EAAAA,SAAAA,CAASA,CAAC,KACN,IAAMqK,EAAwB,CAC1BlM,MAAO,CAAC,EACRH,KAAM,CAAEhD,MAAO,KAAM,EACrBiD,MAAO,GACX,EACIiF,GAKAoH,CAJ+B,MADrB,IAEN,IAAMrL,EAA4B,MAAMC,EAAAA,CAAUA,CAACC,GAAG,CAAC,oBAAoC,OAAhB0D,EAAMM,MAAM,CAAC,EAAE,EAAIkH,GAC9FrH,EAAekB,GAAe,EAAE,GAAGA,CAAS,CAAE,EAAhB,CAAmBjF,CAAQ,CAAC,GAC9D,GAGR,EAAG,EAAE,EAGD,UAACT,MAAAA,UACG,UAAC/C,EAAAA,CAASA,CAAAA,CAACE,UAAU,WAAWD,KAAK,aACjC,UAACyI,EAAAA,CAAIA,CAAAA,CACD3C,MAAO,CACH4C,UAAW,MACXC,UAAW,kEACf,WAEA,UAACC,EAAAA,CAAqBA,CAAAA,CAACC,SAAUjB,EAAckB,IAAKpB,EAASqB,cAAe1B,EAAY2B,oBAAoB,WACxG,WAACP,EAAAA,CAAIA,CAAC5D,IAAI,YACN,UAAC3E,EAAAA,CAAGA,CAAAA,UACA,UAACC,EAAAA,CAAGA,CAAAA,UACA,UAACsI,EAAAA,CAAIA,CAAC7D,KAAK,WAAE/E,EAAE,0BAGvB,UAACoJ,KAAAA,CAAAA,GACD,UAAC/I,EAAAA,CAAGA,CAAAA,UACA,UAACC,EAAAA,CAAGA,CAAAA,CAACE,EAAE,IAACY,GAAI,EAAGD,GAAI,YACf,WAACkI,EAAAA,CAAIA,CAACC,KAAK,YACP,UAACD,EAAAA,CAAIA,CAACE,KAAK,EAACnJ,UAAU,0BAAkBJ,EAAE,sBAC1C,UAACwJ,EAAAA,EAASA,CAAAA,CACN1G,KAAK,QACL2G,GAAG,QACHC,QAAQ,IACR5I,MAAO0G,EAAW/H,KAAK,CACvBkK,UAAW,GAAkD,KAA/BC,OAAO9I,GAAS,IAAIsH,IAAI,GACtDyB,aAAc,CACVF,UAAW3J,EAAE,qCACjB,EACAe,SAlFlBM,CAkF4ByI,GAjF9C,GAAIzI,EAAE0I,MAAM,CAAE,CACV,GAAM,MAAEjH,CAAI,OAAEhC,CAAK,CAAE,CAAGO,EAAE0I,MAAM,CAChCtC,EAAc,GAAgB,EAC1B,GAAGkB,CAAS,CACZ,CAAC7F,CAFyB,CAEpB,CAAEhC,EACZ,EACJ,CACJ,WA+EwB,UAACT,EAAAA,CAAGA,CAAAA,CAACD,UAAU,gBACX,WAACE,EAAAA,CAAGA,CAAAA,WACA,UAAC4E,EAAAA,CAAMA,CAAAA,CAAC9E,UAAU,OAAOO,KAAK,SAASwE,QAAQ,mBAC1CnF,EAAE,YAEP,UAACkF,EAAAA,CAAMA,CAAAA,CAAC9E,UAAU,OAAOmD,QAlGpC,CAkG6CyG,IAjG9DvC,EAAcoH,GAEd5E,OAAOC,QAAQ,CAAC,EAAG,EACvB,EA8FgF/E,QAAQ,gBACnDnF,EAAE,WAEP,UAACkD,IAAIA,CACDC,KAAK,6BACLnC,GAAK,OAFJkC,oCAID,UAACgC,EAAAA,CAAMA,CAAAA,CAACC,QAAQ,qBAAanF,EAAE,6BAUvE,mQCuCA,MArKuB,IACnB,IAAMgP,EAAqB,CACvBvP,MAAO,GACPmG,KAAM,GACNkI,YAAa,EACjB,EAEM,CAACtG,EAAYC,EAAc,CAAG3F,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAakN,GAGnDrH,EAAWL,EAAMM,MAAM,EAAwB,sBAApBN,EAAMM,MAAM,CAAC,EAAE,EAA4BN,EAAMM,MAAM,CAAC,EAAE,CACrF,GAAE5H,CAAC,CAAE,CAAGC,CAAAA,EAAAA,EAAAA,EAAAA,CAAcA,CAAC,UAEvB8H,EAAe,MAAOC,EAAYgG,SAGzBxG,EACDA,MAIN9D,EACAuE,EARJD,EAAME,cAAc,GACpB,IAAMC,EAAM,CACR1I,KAAK,QAAE+H,EAAAA,EAAW/H,KAAAA,EAAX+H,KAAAA,EAAAA,EAAkBY,GAAlBZ,CAAsB,GAC7B5B,IAAI,QAAE4B,EAAAA,EAAW5B,IAAAA,EAAX4B,KAAAA,EAAAA,EAAiBY,GAAjBZ,CAAqB,GAC3BsG,YAAatG,EAAWsG,WAAW,EAKnCnG,GACAM,EAAW,KADD,oCAEVvE,EAAW,MAAMC,EAAAA,CAAUA,CAAC0E,KAAK,CAAC,eAA+B,OAAhBf,EAAMM,MAAM,CAAC,EAAE,EAAIO,KAEpEF,EAAW,mCACXvE,EAAW,MAAMC,EAAAA,CAAUA,CAAC2E,IAAI,CAAC,cAAeH,IAEhDzE,GAAYA,EAASlE,GAAG,EAAE,EAC1B6E,EAAKA,CAACC,OAAO,CAACtE,EAAEiI,IAChBM,IAAAA,IAAW,CAAC,+BAER7E,OAAAA,EAAAA,KAAAA,EAAAA,EAAU8E,SAAAA,CAAV9E,GAAwB,KACxBW,EAAAA,EAAKA,CAACE,KAAK,CAACvE,EAAE,yBAEdqE,EAAAA,EAAKA,CAACE,KAAK,CAACb,EAGxB,EAOMoG,EAAe,IACjB,GAAIzI,EAAE0I,MAAM,CAAE,CACV,GAAM,MAAEjH,CAAI,OAAEhC,CAAK,CAAE,CAAGO,EAAE0I,MAAM,CAChCtC,EAAc,GAAgB,EAC1B,GAAGkB,CAAS,CACZ,CAAC7F,CAFyB,CAEpB,CAAEhC,EACZ,EACJ,CACJ,EAEMmN,EAAoB,IACtBxG,EAAc,GAAgB,EAC1B,GAAGkB,CAAS,CACZmF,EAF0B,UAEbhN,EACjB,EACJ,EAEA2D,CAAAA,EAAAA,EAAAA,SAAAA,CAASA,CAAC,KACN,IAAMmK,EAAmB,CACrBhM,MAAO,CAAC,EACRH,KAAM,CAAEhD,MAAO,KAAM,EACrBiD,MAAO,GACX,EAEIiF,GAKAsH,CAJ0B,MADhB,IAEN,IAAMvL,EAAuB,MAAMC,EAAAA,CAAUA,CAACC,GAAG,CAAC,eAA+B,OAAhB0D,EAAMM,MAAM,CAAC,EAAE,EAAIgH,GACpFnH,EAAc,GAAgB,EAAE,GAAGkB,CAAS,CAAE,EAAhB,CAAmBjF,CAAQ,CAAC,GAC9D,GAGR,EAAG,EAAE,EAEL,IAAMmE,EAAUC,CAAAA,EAAAA,EAAAA,MAAAA,CAAMA,CAAC,MAEvB,MACI,UAAC5H,EAAAA,CAASA,CAAAA,CAACE,UAAU,WAAWD,KAAK,aACjC,UAACyI,EAAAA,CAAIA,CAAAA,CACD3C,MAAO,CACH4C,UAAW,MACXC,UAAW,kEACf,WAEA,UAACC,EAAAA,CAAqBA,CAAAA,CAACC,SAAUjB,EAAckB,IAAKpB,EAASqB,cAAe1B,EAAY2B,oBAAoB,WACxG,WAACP,EAAAA,CAAIA,CAAC5D,IAAI,YACN,UAAC3E,EAAAA,CAAGA,CAAAA,UACA,UAACC,EAAAA,CAAGA,CAAAA,UACA,UAACsI,EAAAA,CAAIA,CAAC7D,KAAK,WAAE4C,EAAW3H,EAAE,2CAA6CA,EAAE,gDAGjF,UAACoJ,KAAAA,CAAAA,GACD,WAAC/I,EAAAA,CAAGA,CAAAA,CAACD,UAAU,iBACX,UAACE,EAAAA,CAAGA,CAAAA,CAACE,EAAE,IAACY,GAAI,EAAGD,GAAI,YACf,WAACkI,EAAAA,CAAIA,CAACC,KAAK,YACP,UAACD,EAAAA,CAAIA,CAACE,KAAK,EAACnJ,UAAU,0BACjBJ,EAAE,6CAEP,UAACwJ,EAAAA,EAASA,CAAAA,CACN1G,KAAK,QACL2G,GAAG,QACHC,QAAQ,IACR5I,MAAO0G,EAAW/H,KAAK,CACvBoK,aAAc,CACVF,UAAW3J,EAAE,+BAA+B,EAChDe,SAAU+I,SAItB,UAACxJ,EAAAA,CAAGA,CAAAA,CAACE,EAAE,IAACY,GAAI,EAAGD,GAAI,YACf,WAACkI,EAAAA,CAAIA,CAACC,KAAK,YACP,UAACD,EAAAA,CAAIA,CAACE,KAAK,EAACnJ,UAAU,0BACjBJ,EAAE,mCAEP,UAACwJ,EAAAA,EAASA,CAAAA,CACN1G,KAAK,OACL2G,GAAG,OACHC,QAAQ,IACR5I,MAAO0G,EAAW5B,IAAI,CACtB+D,UAAW,GAAkD,KAA/BC,OAAO9I,GAAS,IAAIsH,IAAI,GACtDyB,aAAc,CACVF,UAAW3J,EAAE,kCACjB,EACAe,SAAU+I,YAK1B,UAACzJ,EAAAA,CAAGA,CAAAA,UACA,UAACC,EAAAA,CAAGA,CAAAA,UACA,WAAC+I,EAAAA,CAAIA,CAACC,KAAK,YACP,UAACD,EAAAA,CAAIA,CAACE,KAAK,WAAEvJ,EAAE,iBACf,UAACoO,EAAAA,CAAeA,CAAAA,CAACC,YAAa7G,EAAWsG,WAAW,CAAE/M,SAAU,GAAckN,EAAkBK,YAI5G,UAACjO,EAAAA,CAAGA,CAAAA,CAACD,UAAU,gBACX,WAACE,EAAAA,CAAGA,CAAAA,WACA,UAAC4E,EAAAA,CAAMA,CAAAA,CAAC9E,UAAU,OAAOO,KAAK,SAASwE,QAAQ,mBAC1CnF,EAAE,YAEP,UAACkF,EAAAA,CAAMA,CAAAA,CAAC9E,UAAU,OAAOmD,QAzGhC,CAyGyCyG,IAxG1DvC,EAAcuH,GAEd/E,OAAOC,QAAQ,CAAC,EAAG,EACvB,EAqG4E/E,QAAQ,gBACnDnF,EAAE,WAEP,UAACkD,IAAIA,CACDC,KAAK,6BACLnC,GAAK,OAFJkC,+BAID,UAACgC,EAAAA,CAAMA,CAAAA,CAACC,QAAQ,qBAAanF,EAAE,2BASnE,+MChIA,MA1CyB,QAmCjB8F,EAAAA,EAlCN,GAAM,GAAE9F,CAAC,CAAE,CAAGC,CAAAA,EAAAA,EAAAA,EAyCDiP,CAzCejP,CAAC,UACvBkP,EAAuB,EAwCAD,EAtCzB,UAACjM,MAAAA,UACC,WAAC/C,EAAAA,CAASA,CAAAA,CAAC+F,MAAO,CAAEC,UAAW,QAAS,EAAG/F,KAAK,IAACC,UAAU,gBACzD,UAACC,EAAAA,CAAGA,CAAAA,UACF,UAACC,EAAAA,CAAGA,CAAAA,CAACC,GAAI,YACP,UAAC4F,EAAAA,CAAWA,CAAAA,CAAC1G,MAAOO,EAAE,uDAG1B,UAACK,EAAAA,CAAGA,CAAAA,UACF,UAACC,EAAAA,CAAGA,CAAAA,CAACC,GAAI,YACP,UAAC2C,IAAIA,CACHC,KAAK,6BACLnC,GAAG,OAFAkC,sCAIH,UAACgC,EAAAA,CAAMA,CAAAA,CAACC,QAAQ,YAAYiB,KAAK,cAChCpG,EAAE,yDAKT,UAACK,EAAAA,CAAGA,CAAAA,CAACD,UAAU,gBACb,UAACE,EAAAA,CAAGA,CAAAA,CAACC,GAAI,YACP,UAAC6O,EAAAA,OAAgBA,CAAAA,CAAAA,YAQvBC,EAAqBC,CAAAA,EAAAA,EAAAA,iBAAAA,CAAiBA,CAAC,IAAM,UAACH,EAAAA,CAAAA,IAC9CrJ,EAAYS,CAAAA,EAAAA,EAAAA,EAAAA,CAAWA,CAAC,GAAWT,SACzC,IAAI,GAAEA,GAAAA,OAAAA,EAAAA,EAAOU,IAAPV,OAAOU,GAAPV,OAAAA,EAAAA,EAAAA,YAAoByJ,EAApBzJ,KAAAA,EAAAA,CAAkC,CAAC,GAAnCA,UAAgD,EAIpD,CAJuD,EAIvD,OAACuJ,EAAAA,CAAAA,GAHM,UAAC3I,EAAAA,OAAeA,CAAAA,CAAAA,EAK3B,+MCCA,MAxC6B,QAiCrBZ,EAAAA,EAhCN,GAAM,GAAE9F,CAAC,CAAE,CAAGC,CAAAA,EAAAA,EAAAA,EAAAA,CAAcA,CAAC,UACvBuP,EAA2B,IAE7B,EAoC6BC,CApC7B,CAoC8B,CApC9B,MAACvP,EAAAA,CAASA,CAAAA,CAAC+F,MAAO,CAAEC,UAAW,QAAS,EAAG/F,KAAK,IAACC,UAAU,gBACzD,UAACC,EAAAA,CAAGA,CAAAA,UACF,UAACC,EAAAA,CAAGA,CAAAA,CAACC,GAAI,YACP,UAAC4F,EAAAA,CAAWA,CAAAA,CAAC1G,MAAOO,EAAE,yDAG1B,UAACK,EAAAA,CAAGA,CAAAA,UACF,UAACC,EAAAA,CAAGA,CAAAA,CAACC,GAAI,YACP,UAAC2C,IAAIA,CACHC,KAAK,6BACLnC,GAAG,OAFAkC,2CAIH,UAACgC,EAAAA,CAAMA,CAAAA,CAACC,QAAQ,YAAYiB,KAAK,cAChCpG,EAAE,8DAKT,UAACK,EAAAA,CAAGA,CAAAA,CAACD,UAAU,gBACb,UAACE,EAAAA,CAAGA,CAAAA,CAACC,GAAI,YACP,UAACmP,EAAAA,OAAoBA,CAAAA,CAAAA,UAOzBC,EAA2BC,CAAAA,EAAAA,EAAAA,uBAAAA,CAAuBA,CAAC,IAAM,UAACJ,EAAAA,CAAAA,IAC1D1J,EAAYS,CAAAA,EAAAA,EAAAA,EAAAA,CAAWA,CAAC,GAAWT,SACzC,IAAI,GAAEA,GAAAA,OAAAA,EAAAA,EAAOU,IAAPV,OAAOU,GAAPV,OAAAA,EAAAA,EAAAA,gBAAoB+J,EAApB/J,KAAAA,EAAAA,CAAsC,CAAC,GAAvCA,UAAoD,EAIxD,CAJ2D,EAI3D,OAAC6J,EAAAA,CAAAA,GAHM,UAACjJ,EAAAA,OAAeA,CAAAA,CAAAA,EAK3B,6MCkIA,MA1KqB,IACjB,GAAM,CAAE1G,CAAC,MAAE2K,CAAI,CAAE,CAAG1K,CAAAA,EAAAA,EAAAA,EAAAA,CAAcA,CAAC,CAyKxB2G,SAxKLkJ,EAAgC,CAwKflJ,EAAC,IAxKJ+D,EAAKG,QAAQ,CAAY,CAAEiF,SAAU,KAAM,EAAI,CAAEtQ,MAAO,KAAM,EAC5EoL,EAAcF,EAAKG,QAAQ,CAC3B,CAAClJ,EAAWC,EAAe,CAAGC,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAC,EAAE,EACzC,EAAGC,EAAW,CAAGD,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,EAAC,GAC1B,CAACE,EAAWC,EAAa,CAAGH,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAC,GACrC,CAACI,EAASC,EAAW,CAAGL,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAC,IACjC,CAACM,EAAaC,EAAS,CAAGP,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,EAAC,GACnC,CAACkO,EAAsBC,EAAiB,CAAGnO,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAC,CAAC,GACrD,CAACpC,EAAYqL,EAAc,CAAGjJ,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAC,IACvC,CAACmJ,EAAuBC,EAAyB,CAAGpJ,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,EAAC,GAG7DoO,EAAgB,CAClBzN,KAAMqN,EACNpN,MAAOR,EACPS,KAAM,EACNC,MAAO,CAAC,EACRuN,aAActF,GAA4B,IAC9C,EAEMhI,EAAU,CACZ,CACIC,CALwB+H,IAKlB7K,EAAE,wCACR+C,SAAU,GAAcmB,EAAIzE,KAAK,CACjCkG,UAAU,CACd,EACA,CACI7C,KAAM9C,EAAE,qCACR+C,SAAU,GAAcmB,EAAI0B,IAAI,CAChCD,UAAU,CACd,EACA,CACI7C,KAAM9C,EAAE,yCACR+C,SAAU,GAAcmB,EAAIkM,SAAS,CACrCzK,UAAU,CACd,EACA,CACI7C,KAAM9C,EAAE,4CACR+C,SAAU,QAAcmB,QAAAA,CAAAA,MAAAA,GAAAA,EAAImM,YAAAA,EAAJnM,KAAAA,EAAAA,EAAkBzE,GAAlByE,EAAkBzE,GAAS,IACnDkG,UAAU,CACd,EACA,CACI7C,KAAM9C,EAAE,uCACR+C,SAAU,GAAcmB,EAAI1E,GAAG,CAC/BmG,UAAU,EACV3C,KAAM,GACF,WAACC,MAAAA,WACG,UAACC,IAAIA,CAACC,KAAK,6BAA6BnC,GAAI,OAAvCkC,wBAA4E,OAANE,EAAE5D,GAAG,WAE5E,UAAC6D,IAAAA,CAAEjD,UAAU,uBAEV,OAEP,UAACkD,IAAAA,CAAEC,QAAS,IAAMC,EAAWJ,YACzB,UAACC,IAAAA,CAAEjD,UAAU,8BAI7B,EACH,CAEKkQ,EAAmB,MAAOC,IAC5BxO,GAAW,GACX,IAAM2B,EAAW,MAAMC,EAAAA,CAAUA,CAACC,GAAG,CAAC,WAAY2M,GAC9C7M,IACA7B,EAAe6B,EAASG,EADd,EACkB,EAC5B5B,EAAayB,EAASK,UAAU,EAChChC,GAAW,GAEnB,EAQMiC,EAAsB,MAAOC,EAAiBtB,KAChDuN,EAAcxN,KAAK,CAAGuB,EACtBiM,EAAcvN,IAAI,CAAGA,EACrBZ,GAAW,GACX,IAAM2B,EAAW,MAAMC,EAAAA,CAAUA,CAACC,GAAG,CAAC,WAAYsM,GAC9CxM,GAAYA,EAASG,IAAI,EAAIH,EAASG,IAAI,CAACC,MAAM,CAAG,GAAG,CACvDjC,EAAe6B,EAASG,IAAI,EAC5B1B,EAAW8B,GACXlC,GAAW,GAEnB,EAEMyB,EAAa,MAAOU,IACtB+L,EAAiB/L,EAAI1E,GAAG,EACxB6C,GAAS,EACb,EAEM8B,EAAe,UACjB,GAAI,CACA,MAAMR,EAAAA,CAAUA,CAACS,MAAM,CAAC,YAAiC,OAArB4L,IACpCM,EAAiBJ,GACjB7N,GAAS,GACTgC,EAAAA,EAAKA,CAACC,OAAO,CAACtE,EAAE,2DACpB,CAAE,MAAOuE,EAAO,CACZF,EAAAA,EAAKA,CAACE,KAAK,CAACvE,EAAE,qDAClB,CACJ,EAEMwE,EAAY,IAAMnC,GAAS,GAE3B6J,EAAyBsE,CAAAA,EAAAA,EAAAA,OAAAA,CAAOA,CAAC,KAQnC,IAAMrE,EAAY,IACVC,GAAG,CACH8D,EAActN,KAAK,CAAG,CAAEnD,MAAO2M,EAAE,EAErCkE,EAAiBJ,EACrB,EAEM7D,EAAoBd,IAAAA,QAAU,CAAC,GAAeY,EAAUC,GAAIE,OAAOC,KAAgC,GAAK,KAO9G,MAAO,UAACkE,EAAAA,OAAkBA,CAAAA,CAAC7Q,SALN,CAKgBkK,GAJjCiB,EAAc1J,EAAE0I,MAAM,CAACjJ,KAAK,EAC5BuL,EAAkBhL,EAAE0I,MAAM,CAACjJ,KAAK,CACpC,EAEmDhB,QArB/B,CAqBwC0M,IApBpD9M,IACAwL,EAAyB,CAACD,GAC1BF,EAAc,IAEtB,EAgByErL,WAAYA,GACzF,EAAG,CAACA,EAAW,EAMf,MAJA+E,CAAAA,EAAAA,EAAAA,SAAAA,CAASA,CAAC,KACN6L,EAAiBJ,EACrB,EAAG,EAAE,EAGD,WAACjN,MAAAA,WACG,WAACyB,EAAAA,CAAKA,CAAAA,CAACC,KAAMvC,EAAawC,OAAQJ,YAC9B,UAACE,EAAAA,CAAKA,CAACG,MAAM,EAACC,WAAW,aACrB,UAACJ,EAAAA,CAAKA,CAACK,KAAK,WAAE/E,EAAE,kDAEpB,UAAC0E,EAAAA,CAAKA,CAACM,IAAI,WAAEhF,EAAE,qEACf,WAAC0E,EAAAA,CAAKA,CAACO,MAAM,YACT,UAACC,EAAAA,CAAMA,CAAAA,CAACC,QAAQ,YAAY5B,QAASiB,WAChCxE,EAAE,yCAEP,UAACkF,EAAAA,CAAMA,CAAAA,CAACC,QAAQ,UAAU5B,QAASY,WAC9BnE,EAAE,4CAKf,UAACoF,EAAAA,CAAQA,CAAAA,CACLvC,QAASA,EACTgB,KAAMjC,EACNI,UAAWA,EACXyK,SAAS,IACTpH,WAAW,EACX4F,sBAAuBA,EACvByB,mBAAoBR,EACpBlI,oBAAqBA,EACrBsB,iBA3Fc3C,CA2FI2C,GA1F1B4K,EAAcxN,KAAK,CAAGR,EACtBgO,EAAcvN,IAAI,CAAGA,EACrB2N,EAAiBJ,EACrB,MA2FJ,oMCVA,MAhKgC,IAC5B,GAAM,CAACtO,EAAWC,EAAe,CAAGC,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAC,EAAE,EACzC,EAAGC,EAAW,CAAGD,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,EAAC,GAC1B,CAACE,EAAWC,EAAa,CAAGH,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAC,GACrC,CAACI,EAASC,EAAW,CAAGL,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAC,IACjC,CAACM,EAAaC,EAAS,CAAGP,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,EAAC,GACnC,CAAC4O,EAA0BC,EAA4B,CAAG7O,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAC,CAAC,GACpE0C,EAAY,IAAMnC,GAAS,GAC3B,GAAErC,CAAC,CAAE,CAAGC,CAAAA,EAAAA,EAAAA,EAAAA,CAAcA,CAAC,UAIvB2Q,EACF,WAACC,EAAAA,CAAOA,CAAAA,CAACpH,GAAG,0BACR,UAACoH,EAAAA,CAAOA,CAAChM,MAAM,EAAC7D,GAAG,KAAKZ,UAAU,uBAAc,aAGhD,UAACyQ,EAAAA,CAAOA,CAAC7L,IAAI,WACT,WAAC/B,MAAAA,CAAI7C,UAAU,gBACX,WAAC0Q,IAAAA,WACG,UAACC,IAAAA,UAAE,UAAS,4BAEhB,WAACD,IAAAA,WACG,UAACC,IAAAA,UAAE,QAAO,gCAEd,WAACD,IAAAA,WACG,UAACC,IAAAA,UAAE,SAAQ,yCAEf,WAACD,IAAAA,WACG,UAACC,IAAAA,UAAE,UAAS,iDAEhB,WAACD,IAAAA,WACG,UAACC,IAAAA,UAAE,WAAU,uEAEjB,WAACD,IAAAA,WACG,UAACC,IAAAA,UAAE,WAAU,uEAEjB,WAACD,IAAAA,WACG,UAACC,IAAAA,UAAE,UAAS,+DAgB1BlO,EAAU,CACZ,CACIC,KAXJ,CAWUkO,EAXV,OAACC,EAAAA,CAAcA,CAAAA,CAACC,QAAQ,QAAQC,UAAU,QAAQC,QAASR,WACvD,WAAC9E,OAAAA,WACI9L,EAAE,SAAS,eACZ,UAACqD,IAAAA,CAAEjD,UAAU,oBAAoB6F,MAAO,CAAE8F,OAAQ,SAAU,EAAGsF,cAAY,cAS/EtO,SAAU,OACd,EACA,CACID,KAAM9C,EAAE,UACR+C,SAAU,GACVC,KAAM,GACF,WAACC,MAAAA,WACG,UAACC,IAAIA,CAACC,KAAK,6BAA6BnC,GAAI,OAAvCkC,oCAAwF,OAANE,EAAE5D,GAAG,WAExF,UAAC6D,IAAAA,CAAEjD,UAAU,uBAEV,OAEP,UAACkD,IAAAA,CAAEC,QAAS,IAAMC,EAAWJ,YACzB,UAACC,IAAAA,CAAEjD,UAAU,8BAI7B,EACH,CACKkR,EAA2B,CAC7B7O,KAAM,CAAEhD,MAAO,KAAM,EACrBiD,MAAOR,EACPS,KAAM,EACNC,MAAO,CAAC,CACZ,EAEA6B,CAAAA,EAAAA,EAAAA,SAAAA,CAASA,CAAC,KACN8M,GACJ,EAAG,EAAE,EAEL,IAAMA,EAA4B,UAC9BxP,GAAW,GACX,IAAM2B,EAAW,MAAMC,EAAAA,CAAUA,CAACC,GAAG,CAAC,sBAAuB0N,GACzD5N,GAAYA,EAASG,IAAI,EAAIH,EAASG,IAAI,CAACC,MAAM,CAAG,GAAG,CACvDjC,EAAe6B,EAASG,IAAI,EAC5B5B,EAAayB,EAASK,UAAU,EAChChC,GAAW,GAEnB,EAQMiC,EAAsB,MAAOC,EAAiBtB,KAChD2O,EAAyB5O,KAAK,CAAGuB,EACjCqN,EAAyB3O,IAAI,CAAGA,EAChCZ,GAAW,GACX,IAAM2B,EAAW,MAAMC,EAAAA,CAAUA,CAACC,GAAG,CAAC,sBAAuB0N,GACzD5N,GAAYA,EAASG,IAAI,EAAIH,EAASG,IAAI,CAACC,MAAM,CAAG,GAAG,CACvDjC,EAAe6B,EAASG,IAAI,EAC5B1B,EAAW8B,GACXlC,GAAW,GAEnB,EAEMoC,EAAe,UACjB,GAAI,CACA,MAAMR,EAAAA,CAAUA,CAACS,MAAM,CAAC,uBAAgD,OAAzBsM,IAC/Ca,IACAlP,GAAS,GACTgC,EAAAA,EAAKA,CAACC,OAAO,CAACtE,EAAE,yEACpB,CAAE,MAAOuE,EAAO,CACZF,EAAAA,EAAKA,CAACE,KAAK,CAACvE,EAAE,mEAClB,CACJ,EAEMwD,EAAa,MAAOU,IACtByM,EAA4BzM,EAAI1E,GAAG,EACnC6C,EAAS,GACb,EAEA,MACI,WAACY,MAAAA,WACG,WAACyB,EAAAA,CAAKA,CAAAA,CAACC,KAAMvC,EAAawC,OAAQJ,YAC9B,UAACE,EAAAA,CAAKA,CAACG,MAAM,EAACC,WAAW,aACrB,UAACJ,EAAAA,CAAKA,CAACK,KAAK,WAAE/E,EAAE,gDAEpB,UAAC0E,EAAAA,CAAKA,CAACM,IAAI,WAAEhF,EAAE,4CACf,WAAC0E,EAAAA,CAAKA,CAACO,MAAM,YACT,UAACC,EAAAA,CAAMA,CAAAA,CAACC,QAAQ,YAAY5B,QAASiB,WAChCxE,EAAE,YAEP,UAACkF,EAAAA,CAAMA,CAAAA,CAACC,QAAQ,UAAU5B,QAASY,WAC9BnE,EAAE,eAKf,UAACoF,EAAAA,CAAQA,CAAAA,CACLvC,QAASA,EACTgB,KAAMjC,EACNI,UAAWA,EACXqD,WAAW,EACXrB,oBAAqBA,EACrBsB,iBAzDa,CAyDKA,GAxD1BgM,EAAyB5O,KAAK,CAAGR,EACjCoP,EAAyB3O,IAAI,CAAGA,EAChC4O,GACJ,MAyDJ,kOC3GA,MAlD8B,QAkCtBzL,EAAAA,EAjCN,GAAM,GAAE9F,CAAC,CAAE,CAAGC,CAAAA,EAAAA,EAAAA,EAiDDuR,CAjDevR,CAAC,UACvBwR,EAA4B,IAE9B,UAACxO,MAAAA,UACC,WAAC/C,EAAAA,CAASA,CAAAA,CAAC+F,MAAO,CAAEC,UAAW,QAAS,EAAG/F,KAAK,IAACC,UAAU,gBACzD,UAACC,EAAAA,CAAGA,CAAAA,UACF,UAACC,EAAAA,CAAGA,CAAAA,CAACC,GAAI,YACP,UAAC4F,EAAAA,CAAWA,CAAAA,CAAC1G,MAAOO,EAAE,8DAG1B,UAACK,EAAAA,CAAGA,CAAAA,UACF,UAACC,EAAAA,CAAGA,CAAAA,CAACC,GAAI,YACP,UAAC2C,IAAIA,CACHC,KAAK,6BACLnC,GAAG,OAFAkC,2CAIH,UAACgC,EAAAA,CAAMA,CAAAA,CAACC,QAAQ,YAAYiB,KAAK,cAChCpG,EAAE,mEAKT,UAACK,EAAAA,CAAGA,CAAAA,CAACD,UAAU,gBACb,UAACE,EAAAA,CAAGA,CAAAA,CAACC,GAAI,YACP,UAACmR,EAAAA,OAAqBA,CAAAA,CAAAA,YAO5BC,EAA0BC,CAAAA,EAAAA,EAAAA,sBAAAA,CAAsBA,CAAC,IAAM,UAACH,EAAAA,CAAAA,IACxD3L,EAAYS,CAAAA,EAAAA,EAAAA,EAAAA,CAAWA,CAAC,GAAWT,SACzC,IAAI,GAAEA,GAAAA,OAAAA,EAAAA,EAAOU,IAAPV,OAAOU,GAAPV,OAAAA,EAAAA,EAAAA,iBAAqC,EAArCA,KAAAA,EAAAA,CAAuC,CAAC,GAAxCA,UAAqD,EAIvD,UAAC6L,EAAAA,CAAAA,GAHI,UAACjL,EAAAA,OAAeA,CAAAA,CAAAA,EAK3B,yPC4GA,MA/IwBY,IACpB,IAAMuK,EAAqB,CACvBpS,MAAO,GACPqS,KAAM,EACV,CA2IWC,CA1IL,GAAE/R,CAAC,CAAE,CAAGC,CAAAA,EAAAA,EAAAA,EAAAA,CAAcA,CAAC,UACvB,CAACuH,EAAYC,EAAc,CAAG3F,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAa+P,GAEnDlK,EAAWL,EAAMM,MAAM,EAAwB,uBAAdA,MAAM,CAAC,EAAE,EAA2BN,EAAMM,MAAM,CAAC,EAAE,CAEpFC,EAAUC,CAAAA,EAAAA,EAAAA,MAAAA,CAAMA,CAAC,MAQjBgC,EAAe,IACjB,GAAIzI,EAAE0I,MAAM,CAAE,CACV,GAAM,MAAEjH,CAAI,OAAEhC,CAAK,CAAE,CAAGO,EAAE0I,MAAM,CAChCtC,EAAc,GAAgB,EAC1B,GAAGkB,CAAS,CACZ,CAAC7F,CAFyB,CAEpB,CAAEhC,EACZ,EACJ,CACJ,EAEMiH,EAAe,MAAOC,EAAYgG,SAOhCtK,EACAuE,EAPJD,EAAME,cAAc,GACpB,IAAMC,EAAM,CACR1I,MAAO+H,EAAW/H,KAAK,CAAC2I,IAAI,GAC5B0J,KAAMtK,EAAWsK,IAAI,EAKrBnK,GACAM,EAAW,KADD,sDAEVvE,EAAW,MAAMC,EAAAA,CAAUA,CAAC0E,KAAK,CAAC,eAA+B,OAAhBf,EAAMM,MAAM,CAAC,EAAE,EAAIO,KAEpEF,EAAW,yDACXvE,EAAW,MAAMC,EAAAA,CAAUA,CAAC2E,IAAI,CAAC,cAAeH,IAEhDzE,GAAYA,EAASlE,GAAG,EACxB6E,EAAAA,EAAKA,CAACC,OAAO,CAACtE,EAAEiI,IAChBM,IAAAA,IAAW,CAAC,+BAER7E,SAAAA,KAAAA,EAAAA,EAAU8E,GAAV9E,MAAU8E,IAAc,KACxBnE,EAAAA,EAAKA,CAACE,KAAK,CAACvE,EAAE,yBAEdqE,EAAAA,EAAKA,CAACE,KAAK,CAACb,EAGxB,EAiBA,MAfAe,CAAAA,EAAAA,EAAAA,SAAAA,CAASA,CAAC,KACN,IAAMuN,EAAmB,CACrBpP,MAAO,CAAC,EACRH,KAAM,CAAEhD,MAAO,KAAM,EACrBiD,MAAO,GACX,EACIiF,GAKAsK,CAJ0B,MADhB,IAEN,IAAMvO,EAAuB,MAAMC,EAAAA,CAAUA,CAACC,GAAG,CAAC,eAA+B,OAAhB0D,EAAMM,MAAM,CAAC,EAAE,EAAIoK,GACpFvK,EAAc,GAAgB,EAAE,GAAGkB,CAAS,CAAE,EAAhB,CAAmBjF,CAAQ,CAAC,GAC9D,GAGR,EAAG,EAAE,EAGD,UAACT,MAAAA,UACG,UAAC/C,EAAAA,CAASA,CAAAA,CAACE,UAAU,WAAWD,KAAK,aACjC,UAACyI,EAAAA,CAAIA,CAAAA,CACD3C,MAAO,CACH4C,UAAW,MACXC,UAAW,kEACf,WAEA,UAACC,EAAAA,CAAqBA,CAAAA,CAACC,SAAUjB,EAAckB,IAAKpB,EAASqB,cAAe1B,EAAY2B,oBAAoB,WACxG,WAACP,EAAAA,CAAIA,CAAC5D,IAAI,YACN,UAAC3E,EAAAA,CAAGA,CAAAA,UACA,UAACC,EAAAA,CAAGA,CAAAA,UACA,UAACsI,EAAAA,CAAIA,CAAC7D,KAAK,WAAE/E,EAAE,6CAGvB,UAACoJ,KAAAA,CAAAA,GACD,WAAC/I,EAAAA,CAAGA,CAAAA,WACA,UAACC,EAAAA,CAAGA,CAAAA,CAACE,EAAE,IAACY,GAAI,EAAGD,GAAI,YACf,WAACkI,EAAAA,CAAIA,CAACC,KAAK,YACP,UAACD,EAAAA,CAAIA,CAACE,KAAK,EAACnJ,UAAU,0BACjBJ,EAAE,yCAEP,UAACwJ,EAAAA,EAASA,CAAAA,CACN1G,KAAK,QACL2G,GAAG,QACHC,QAAQ,IACR5I,MAAO0G,EAAW/H,KAAK,CACvBkK,UAAW,GAAoC,OAAXvB,IAAI,GACxCyB,aAAc,CACVF,UAAW3J,EAAE,kDACjB,EACAe,SAAU+I,SAItB,UAACxJ,EAAAA,CAAGA,CAAAA,CAACE,EAAE,IAACY,GAAI,EAAGD,GAAI,YACf,WAACkI,EAAAA,CAAIA,CAACC,KAAK,YACP,UAACD,EAAAA,CAAIA,CAACE,KAAK,WAAEvJ,EAAE,mCACf,UAACwJ,EAAAA,EAASA,CAAAA,CACN1G,KAAK,OACL2G,GAAG,OACH3I,MAAO0G,EAAWsK,IAAI,CACtBjI,aAAc7J,EAAE,6CAChBe,SAAU+I,YAK1B,UAACzJ,EAAAA,CAAGA,CAAAA,CAACD,UAAU,gBACX,WAACE,EAAAA,CAAGA,CAAAA,WACA,UAAC4E,EAAAA,CAAMA,CAAAA,CAAC9E,UAAU,OAAOO,KAAK,SAASwE,QAAQ,mBAC1CnF,EAAE,qCAEP,UAACkF,EAAAA,CAAMA,CAAAA,CAAC9E,UAAU,OAAOmD,QAjHpC,CAiH6CyG,IAhH9DvC,EAAcoK,GAEd5H,OAAOC,QAAQ,CAAC,EAAG,EACvB,EA6GgF/E,QAAQ,gBACnDnF,EAAE,oCAEP,UAACkD,IAAIA,CACDC,KAAK,6BACLnC,GAAK,OAFJkC,+BAID,UAACgC,EAAAA,CAAMA,CAAAA,CAACC,QAAQ,qBAAanF,EAAE,sDAUvE,8KC3BA,MAzHwB,IACpB,GAAM,CAAEA,CAAC,CAAE,CAAGC,CAAAA,EAAAA,EAAAA,EAAAA,CAAcA,CAAC,QAwHlBoK,EAvHL,CAACzI,EAAWC,EAAe,CAAGC,CAAAA,EAAAA,EAAAA,EAuHVuI,EAAC,IAvHSvI,CAAQA,CAAC,EAAE,EACzC,EAAGC,EAAW,CAAGD,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,EAAC,GAC1B,CAACE,EAAWC,EAAa,CAAGH,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAC,GACrC,CAACI,EAASC,EAAW,CAAGL,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAC,IACjC,CAACM,EAAaC,EAAS,CAAGP,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,EAAC,GACnC,CAACoQ,EAAkBC,EAAoB,CAAGrQ,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAC,CAAC,GACpD0C,EAAY,IAAMnC,GAAS,GAE3BQ,EAAU,CACZ,CACIC,KAAM9C,EAAE,kCACR+C,SAAU,OACd,EACA,CACID,KAAM9C,EAAE,iCACR+C,SAAU,OACVC,KAAM,GAAY,UAACK,IAAAA,CAAEjD,UAAW,OAAc,OAAPgD,EAAE0O,IAAI,GACjD,EACA,CACIhP,KAAM9C,EAAE,mCACR+C,SAAU,GACVC,KAAOI,GACH,WAACH,MAAAA,WACG,UAACC,IAAIA,CAACC,KAAK,6BAA6BnC,GAAI,OAAvCkC,4BAAgF,OAANE,EAAE5D,GAAG,WAEhF,UAAC6D,IAAAA,CAAEjD,UAAU,uBAEV,OAEP,UAACkD,IAAAA,CAAEC,QAAS,IAAMC,EAAWJ,YACzB,UAACC,IAAAA,CAAEjD,UAAU,4BACZ,MAGjB,EACH,CACK4R,EAAmB,CACrBvP,KAAM,CAAEhD,MAAO,KAAM,EACrBiD,MAAOR,EACPS,KAAM,EACNC,MAAO,CAAC,CACZ,EAEA6B,CAAAA,EAAAA,EAAAA,SAAAA,CAASA,CAAC,KACNwN,EAAkBD,EACtB,EAAG,EAAE,EAEL,IAAMC,EAAoB,MAAOG,IAC7BrQ,GAAW,GACX,IAAM2B,EAAW,MAAMC,EAAAA,CAAUA,CAACC,GAAG,CAAC,cAAewO,GACjD1O,GAAYA,EAASG,IAAI,EAAIH,EAASG,IAAI,CAACC,MAAM,CAAG,GAAG,CACvDjC,EAAe6B,EAASG,IAAI,EAC5B5B,EAAayB,EAASK,UAAU,EAChChC,GAAW,GAEnB,EAQMiC,EAAsB,MAAOC,EAAiBtB,KAChDqP,EAAiBtP,KAAK,CAAGuB,EACzB+N,EAAiBrP,IAAI,CAAGA,EACxBZ,GAAW,GACX,IAAM2B,EAAW,MAAMC,EAAAA,CAAUA,CAACC,GAAG,CAAC,cAAeoO,GACjDtO,GAAYA,EAASG,IAAI,EAAIH,EAASG,IAAI,CAACC,MAAM,CAAG,GAAG,CACvDjC,EAAe6B,EAASG,IAAI,EAC5B1B,EAAW8B,GACXlC,GAAW,GAEnB,EAEMoC,EAAe,UACjB,GAAI,CACA,MAAMR,EAAAA,CAAUA,CAACS,MAAM,CAAC,eAAgC,OAAjB8N,IACvCD,EAAkBD,GAClB3P,GAAS,GACTgC,EAAAA,EAAKA,CAACC,OAAO,CAACtE,EAAE,gEACpB,CAAE,MAAOuE,EAAO,CACZF,EAAAA,EAAKA,CAACE,KAAK,CAACvE,EAAE,0DAClB,CACJ,EAEMwD,EAAa,MAAOU,IACtBiO,EAAoBjO,EAAI1E,GAAG,EAC3B6C,GAAS,EACb,EAEA,MACI,WAACY,MAAAA,WACG,WAACyB,EAAAA,CAAKA,CAAAA,CAACC,KAAMvC,EAAawC,OAAQJ,YAC9B,UAACE,EAAAA,CAAKA,CAACG,MAAM,EAACC,WAAW,aACrB,UAACJ,EAAAA,CAAKA,CAACK,KAAK,WAAE/E,EAAE,iDAEpB,UAAC0E,EAAAA,CAAKA,CAACM,IAAI,WAAEhF,EAAE,oEACf,WAAC0E,EAAAA,CAAKA,CAACO,MAAM,YACT,UAACC,EAAAA,CAAMA,CAAAA,CAACC,QAAQ,YAAY5B,QAASiB,WAChCxE,EAAE,qCAEP,UAACkF,EAAAA,CAAMA,CAAAA,CAACC,QAAQ,UAAU5B,QAASY,WAC9BnE,EAAE,wCAKf,UAACoF,EAAAA,CAAQA,CAAAA,CACLvC,QAASA,EACTgB,KAAMjC,EACNI,UAAWA,EACXqD,WAAW,EACXrB,oBAAqBA,EACrBsB,iBAzDa,CAyDKA,GAxD1B0M,EAAiBtP,KAAK,CAAGR,EACzB8P,EAAiBrP,IAAI,CAAGA,EACxBsP,EAAkBD,EACtB,MAyDJ,6KC3FA,MA7BwBK,QAahBvM,EAAAA,EAZN,GAAM,CAAE9F,GAAC,CAAE,CAAGC,CAAAA,EAAAA,EAAAA,EA4BDqS,CA5BerS,CAAC,UACvBsS,EA2BqBD,EAAC,EAzBxB,WAACpS,EAAAA,CAASA,CAAAA,CAACC,KAAK,IAACC,UAAU,gBACzB,UAAC+F,EAAAA,CAAWA,CAAAA,CAAC1G,MAAOO,EAAE,sCACtB,UAACwS,EAAAA,OAAUA,CAAAA,CAAAA,MAKXC,EAA4BC,CAAAA,EAAAA,EAAAA,wBAAAA,CAAwBA,CAAC,IAAM,UAACH,EAAAA,CAAAA,IAC5DzM,EAAYS,CAAAA,EAAAA,EAAAA,EAAAA,CAAWA,CAAC,GAAgBT,SAC9C,IAAI,GAAEA,GAAAA,OAAAA,EAAAA,EAAOU,IAAPV,OAAOU,GAAPV,OAAAA,EAAAA,EAAAA,uBAAoB6M,EAApB7M,KAAAA,EAAAA,CAA6C,CAAC,GAA9CA,UAA2D,EAI/D,UAAC2M,EAAAA,CAAAA,GAHM,UAAC/L,EAAAA,OAAeA,CAAAA,CAAAA,EAK3B,mKCmFA,MAvGsB,IACpB,GAAM,CAAC9E,EAAWC,EAAe,CAAGC,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAC,EAAE,EACzC,EAAGC,EAAW,CAAGD,CAAAA,EAAAA,CAqGG8Q,CArGH9Q,CAqGI,OArGJA,CAAQA,EAAC,GAC1B,CAACE,EAAWC,EAAa,CAAGH,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAC,GACrC,CAACI,EAASC,EAAW,CAAGL,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAC,IACjC,CAACM,EAAaC,EAAS,CAAGP,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,EAAC,GACnC,CAAC+Q,EAAgBC,EAAkB,CAAGhR,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAC,CAAC,GAChD,GAAE9B,CAAC,CAAE,CAAGC,CAAAA,EAAAA,EAAAA,EAAAA,CAAcA,CAAC,UAGvB8S,EAAiB,CACrB,KAAQ,CAAE,MAAS,KAAM,EACzB,MAAS7Q,EACT,KAAQ,EACR,MAAS,CAAC,CACZ,EAEMW,EAAU,CACd,CACEC,KAAM,QACNC,SAAU,OACZ,EACA,CACED,KAAM,SACNC,SAAU,GACVC,KAAM,GAAY,WAACC,MAAAA,WAAI,UAACC,IAAIA,CAACC,KAAK,6BAA6BnC,GAAI,OAAvCkC,yBAA6E,OAANE,EAAE5D,GAAG,WAAK,UAAC6D,IAAAA,CAAEjD,UAAU,uBAA4B,OAAM,UAAC8C,IAAIA,CAACC,KAAK,IAAII,QAAS,IAAMC,EAAWJ,YAAI,SAAxCF,CAAyCG,IAAAA,CAAEjD,UAAU,4BAAiC,MACzP,EACD,CAEK4S,EAAkB,UACtBjR,GAAW,GACX,IAAM2B,EAAW,MAAMC,EAAAA,CAAUA,CAACC,GAAG,CAAC,YAAamP,GAC/CrP,GAAYA,EAASG,IAAI,EAAIH,EAASG,IAAI,CAACC,MAAM,CAAG,GAAG,CACzDjC,EAAe6B,EAASG,IAAI,EAC5B5B,EAAayB,EAASK,UAAU,EAChChC,GAAW,GAEf,EAQMiC,EAAsB,MAAOC,EAAiBtB,KAClDoQ,EAAerQ,KAAK,CAAGuB,EACvB8O,EAAepQ,IAAI,CAAGA,EACtBZ,GAAW,GACX,IAAM2B,EAAW,MAAMC,EAAAA,CAAUA,CAACC,GAAG,CAAC,YAAamP,GAC/CrP,GAAYA,EAASG,IAAI,EAAIH,EAASG,IAAI,CAACC,MAAM,CAAG,GAAG,CACzDjC,EAAe6B,EAASG,IAAI,EAC5B1B,EAAW8B,GACXlC,EAAW,IAEf,EAEMyB,EAAa,MAAOU,IACxB4O,EAAkB5O,EAAI1E,GAAG,EACzB6C,GAAS,EACX,EAEM8B,EAAe,UACnB,MAAMR,EAAAA,CAAUA,CAACS,MAAM,CAAC,aAA4B,OAAfyO,IACrCG,IACA3Q,GAAS,EACX,EAEMmC,EAAY,IAAMnC,GAAS,GAMjC,MAJAoC,CAAAA,EAAAA,EAAAA,SAAAA,CAASA,CAAC,KACRuO,GACF,EAAG,EAAE,EAGH,WAAC/P,MAAAA,WACC,WAACyB,EAAAA,CAAKA,CAAAA,CAACC,KAAMvC,EAAawC,OAAQJ,YAChC,UAACE,EAAAA,CAAKA,CAACG,MAAM,EAACC,WAAW,aACvB,UAACJ,EAAAA,CAAKA,CAACK,KAAK,WAAE/E,EAAE,sBAElB,WAAC0E,EAAAA,CAAKA,CAACM,IAAI,YAAEhF,EAAE,sCAAsC,OACrD,WAAC0E,EAAAA,CAAKA,CAACO,MAAM,YACX,UAACC,EAAAA,CAAMA,CAAAA,CAACC,QAAQ,YAAY5B,QAASiB,WACpCxE,EAAE,YAEH,UAACkF,EAAAA,CAAMA,CAAAA,CAACC,QAAQ,UAAU5B,QAASY,WAClCnE,EAAE,eAKP,UAACoF,EAAAA,CAAQA,CAAAA,CACPvC,QAASA,EACTgB,KAAMjC,EACNI,UAAWA,EACXqD,WAAW,EACXrB,oBAAqBA,EACrBsB,iBA1DoB3C,CA0DF2C,GAzDtByN,EAAerQ,KAAK,CAAGR,EACvB6Q,EAAepQ,IAAI,CAAGA,EACtBqQ,GACF,MA0DF,+MC3DA,MAxCwB,QAmChBlN,EAAAA,EAlCN,GAAM,GAAE9F,CAAC,CAAE,CAAGC,CAAAA,EAAAA,EAAAA,EAuCDgT,CAvCehT,CAAC,UACvBiT,EAAsB,CAsCAD,EAAA,CApCxB,UAAChQ,MAAAA,UACC,WAAC/C,EAAAA,CAASA,CAAAA,CAAC+F,MAAO,CAAEC,UAAW,QAAS,EAAG/F,KAAK,IAACC,UAAU,gBACzD,UAACC,EAAAA,CAAGA,CAAAA,UACF,UAACC,EAAAA,CAAGA,CAAAA,CAACC,GAAI,YACP,UAAC4F,EAAAA,CAAWA,CAAAA,CAAC1G,MAAOO,EAAE,qDAG1B,UAACK,EAAAA,CAAGA,CAAAA,UACF,UAACC,EAAAA,CAAGA,CAAAA,CAACC,GAAI,YACP,UAAC2C,IAAIA,CACHC,KAAK,6BACLnC,GAAG,OAFAkC,uCAIH,UAACgC,EAAAA,CAAMA,CAAAA,CAACC,QAAQ,YAAYiB,KAAK,cAC9BpG,EAAE,uDAKX,UAACK,EAAAA,CAAGA,CAAAA,CAACD,UAAU,gBACb,UAACE,EAAAA,CAAGA,CAAAA,CAACC,GAAI,YACP,UAAC4S,EAAAA,OAAeA,CAAAA,CAAAA,YAQtBC,EAAoB7I,CAAAA,EAAAA,EAAAA,OAAAA,CAAgBA,CAAC,IAAM,UAAC2I,EAAAA,CAAAA,IAC5CpN,EAAYS,CAAAA,EAAAA,EAAAA,EAAAA,CAAWA,CAAC,GAAgBT,SAC9C,IAAI,GAAEA,GAAAA,OAAAA,EAAAA,EAAOU,IAAPV,OAAkB,GAAlBA,OAAAA,EAAAA,EAAAA,YAAoBuN,EAApBvN,KAAAA,EAAAA,CAAkC,CAAC,GAAnCA,UAAgD,EAG/C,CAHkD,EAGlD,OAACsN,EAAAA,CAAAA,GAFC,UAAC1M,EAAAA,OAAeA,CAAAA,CAAAA,EAG3B,8KC+EA,MAxHyB,IACrB,GAAM,CAAE1G,CAAC,CAAE,CAAGC,CAAAA,EAAAA,EAAAA,EAAAA,CAAcA,CAAC,QAuHlBmP,EAtHL,CAACxN,EAAWC,EAAe,CAAGC,CAAAA,EAAAA,EAAAA,GAsHTsN,EAAC,GAtHQtN,CAAQA,CAAC,EAAE,EACzC,EAAGC,EAAW,CAAGD,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,EAAC,GAC1B,CAACE,EAAWC,EAAa,CAAGH,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAC,GACrC,CAACI,EAASC,EAAW,CAAGL,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAC,IACjC,CAACM,EAAaC,EAAS,CAAGP,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,EAAC,GACnC,CAACwR,EAAmBC,EAAqB,CAAGzR,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAC,CAAC,GAGtD0R,EAAoB,CACtB/Q,KAAM,CAAEhD,MAAO,KAAM,EACrBiD,MAAOR,EACPS,KAAM,EACNC,MAAO,CAAC,CACZ,EAEMC,EAAU,CACZ,CACIC,KAAM9C,EAAE,wCACR+C,SAAU,OACd,EACA,CACID,KAAM9C,EAAE,yCACR+C,SAAU,GACVC,KAAM,GACF,WAACC,MAAAA,WACG,UAACC,IAAIA,CAACC,KAAK,6BAA6BnC,GAAI,OAAvCkC,4BAAgF,OAANE,EAAE5D,GAAG,WAEhF,UAAC6D,IAAAA,CAAEjD,UAAU,uBAEV,OAEP,UAACkD,IAAAA,CAAEC,QAAS,IAAMC,EAAWJ,YACzB,UAACC,IAAAA,CAAEjD,UAAU,8BAI7B,EACH,CAEKqT,EAAqB,UACvB1R,GAAW,GACX,IAAM2B,EAAW,MAAMC,EAAAA,CAAUA,CAACC,GAAG,CAAC,eAAgB4P,GAClD9P,GAAYA,EAASG,IAAI,EAAIH,EAASG,IAAI,CAACC,MAAM,CAAG,GAAG,CACvDjC,EAAe6B,EAASG,IAAI,EAC5B5B,EAAayB,EAASK,UAAU,EAChChC,GAAW,GAEnB,EAQMiC,EAAsB,MAAOC,EAAiBtB,KAChD6Q,EAAkB9Q,KAAK,CAAGuB,EAC1BuP,EAAkB7Q,IAAI,CAAGA,EACzBZ,GAAW,GACX,IAAM2B,EAAW,MAAMC,EAAAA,CAAUA,CAACC,GAAG,CAAC,eAAgB4P,GAClD9P,GAAYA,EAASG,IAAI,EAAIH,EAASG,IAAI,CAACC,MAAM,CAAG,GAAG,CACvDjC,EAAe6B,EAASG,IAAI,EAC5B1B,EAAW8B,GACXlC,GAAW,GAEnB,EAEMyB,EAAa,MAAOU,IACtBqP,EAAqBrP,EAAI1E,GAAG,EAC5B6C,GAAS,EACb,EAEM8B,EAAe,UACjB,GAAI,CACA,MAAMR,EAAAA,CAAUA,CAACS,MAAM,CAAC,gBAAkC,OAAlBkP,IACxCG,IACApR,GAAS,GACTgC,EAAAA,EAAKA,CAACC,OAAO,CAACtE,EAAE,iEACpB,CAAE,MAAOuE,EAAO,CACZF,EAAAA,EAAKA,CAACE,KAAK,CAACvE,EAAE,2DAClB,CACJ,EAEMwE,EAAY,IAAMnC,GAAS,GAMjC,MAJAoC,CAAAA,EAAAA,EAAAA,SAAAA,CAASA,CAAC,KACNgP,GACJ,EAAG,EAAE,EAGD,WAACxQ,MAAAA,WACG,WAACyB,EAAAA,CAAKA,CAAAA,CAACC,KAAMvC,EAAawC,OAAQJ,YAC9B,UAACE,EAAAA,CAAKA,CAACG,MAAM,EAACC,WAAW,aACrB,UAACJ,EAAAA,CAAKA,CAACK,KAAK,WAAE/E,EAAE,wDAEpB,UAAC0E,EAAAA,CAAKA,CAACM,IAAI,WAAEhF,EAAE,2EACf,WAAC0E,EAAAA,CAAKA,CAACO,MAAM,YACT,UAACC,EAAAA,CAAMA,CAAAA,CAACC,QAAQ,YAAY5B,QAASiB,WAChCxE,EAAE,2CAEP,UAACkF,EAAAA,CAAMA,CAAAA,CAACC,QAAQ,UAAU5B,QAASY,WAC9BnE,EAAE,8CAKf,UAACoF,EAAAA,CAAQA,CAAAA,CACLvC,QAASA,EACTgB,KAAMjC,EACNI,UAAWA,EACXqD,WAAW,EACXrB,oBAAqBA,EACrBsB,iBA/Da,CA+DKA,GA9D1BkO,EAAkB9Q,KAAK,CAAGR,EAC1BsR,EAAkB7Q,IAAI,CAAGA,EACzB8Q,GACJ,MA+DJ,kOChEA,MAnDuB,QAmCf3N,EAAAA,EAlCN,GAAM,GAAE9F,CAAC,CAAE,CAAGC,CAAAA,EAAAA,EAAAA,EAkDDyT,CAlDezT,CAAC,UACvB0T,EAAqB,EAiDC,EA/CxB,UAAC1Q,MAAAA,UACC,WAAC/C,EAAAA,CAASA,CAAAA,CAAC+F,MAAO,CAAEC,UAAW,QAAS,EAAG/F,KAAK,IAACC,UAAU,gBACzD,UAACC,EAAAA,CAAGA,CAAAA,UACF,UAACC,EAAAA,CAAGA,CAAAA,CAACC,GAAI,YACP,UAAC4F,EAAAA,CAAWA,CAAAA,CAAC1G,MAAOO,EAAE,0CAG1B,UAACK,EAAAA,CAAGA,CAAAA,UACF,UAACC,EAAAA,CAAGA,CAAAA,CAACC,GAAI,YACP,UAAC2C,IAAIA,CACHC,KAAK,6BACLnC,GAAG,OAFAkC,oCAIH,UAACgC,EAAAA,CAAMA,CAAAA,CAACC,QAAQ,YAAYiB,KAAK,cAChCpG,EAAE,+CAKT,UAACK,EAAAA,CAAGA,CAAAA,CAACD,UAAU,gBACb,UAACE,EAAAA,CAAGA,CAAAA,CAACC,GAAI,YACP,UAAEqT,EAAAA,OAAcA,CAAAA,CAAAA,YAQtBC,EAAoBC,CAAAA,EAAAA,EAAAA,gBAAAA,CAAgBA,CAAC,IAAM,UAACH,EAAAA,CAAAA,IAC5C7N,EAAYS,CAAAA,EAAAA,EAAAA,EAAAA,CAAWA,CAAC,GAAWT,SACzC,IAAI,GAAEA,GAAAA,OAAAA,EAAAA,EAAOU,IAAPV,OAAOU,GAAPV,OAAAA,EAAAA,EAAAA,UAAoBiO,EAApBjO,KAAAA,EAAAA,CAAgC,CAAC,GAAjCA,UAA8C,EAIlD,CAJqD,EAIrD,OAAC+N,EAAAA,CAAAA,GAHM,UAACnN,EAAAA,OAAeA,CAAAA,CAAAA,EAK3B,8KCdA,MA/BkB,IAChB,GAAM,CAAE1G,CAAC,CAAE,CAAGC,CAAAA,EAAAA,EAAAA,EAAAA,CAAcA,CAAC,QA8BhB+T,EA7Bb,MACE,GA4BqB,EA5BrB,KAAC/Q,MAAAA,UACC,WAAC/C,EAAAA,CAASA,CAAAA,CAAC+F,MAAO,CAAEC,UAAW,QAAS,EAAG/F,KAAK,IAACC,UAAU,gBACzD,UAACC,EAAAA,CAAGA,CAAAA,UACF,UAACC,EAAAA,CAAGA,CAAAA,CAACC,GAAI,YACP,UAAC4F,EAAAA,CAAWA,CAAAA,CAAC1G,MAAM,cAGvB,UAACY,EAAAA,CAAGA,CAAAA,UACF,UAACC,EAAAA,CAAGA,CAAAA,CAACC,GAAI,YACP,UAAC2C,IAAIA,CACHC,KAAK,6BACLnC,GAAG,OAFAkC,+BAIH,UAACgC,EAAAA,CAAMA,CAAAA,CAACC,QAAQ,YAAYiB,KAAK,cAChCpG,EAAE,mBAKT,UAACK,EAAAA,CAAGA,CAAAA,CAACD,UAAU,gBACb,UAACE,EAAAA,CAAGA,CAAAA,CAACC,GAAI,YACP,UAAC0T,EAAAA,OAASA,CAAAA,CAAAA,WAMtB,8KCuFA,MApH6B,IACzB,GAAM,CAACrS,EAAWC,EAAe,CAAGC,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAC,EAAE,EACzC,EAAGC,EAAW,CAAGD,CAAAA,EAAAA,EAAAA,MAkHQ4N,EAlHR5N,CAAQA,EAAC,GAC1B,CAACE,EAAWC,EAAa,CAAGH,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAC,GACrC,CAACI,EAASC,EAAW,CAAGL,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAC,IACjC,CAACM,EAAaC,EAAS,CAAGP,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,EAAC,GACnC,CAACoS,EAAuBC,EAAyB,CAAGrS,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAC,CAAC,GAC9D0C,EAAY,IAAMnC,GAAS,GAC3B,GAAErC,CAAC,CAAE,CAAGC,CAAAA,EAAAA,EAAAA,EAAAA,CAAcA,CAAC,UAEvB4C,EAAU,CACZ,CACIC,KAAM9C,EAAE,SACR+C,SAAU,OACd,EACA,CACID,KAAM9C,EAAE,UACR+C,SAAU,GACVC,KAAOI,GACH,WAACH,MAAAA,WACG,UAACC,IAAIA,CAACC,KAAK,6BAA6BnC,GAAI,OAAvCkC,iCAAqF,OAANE,EAAE5D,GAAG,WAErF,UAAC6D,IAAAA,CAAEjD,UAAU,uBAEV,OAEP,UAACkD,IAAAA,CAAEC,QAAS,IAAMC,EAAWJ,YACzB,UAACC,IAAAA,CAAEjD,UAAU,4BACZ,MAGjB,EACH,CACK0O,EAAwB,CAC1BrM,KAAM,CAAEhD,MAAO,KAAM,EACrBiD,MAAOR,EACPS,KAAM,EACNC,MAAO,CAAC,CACZ,EAEA6B,CAAAA,EAAAA,EAAAA,SAAAA,CAASA,CAAC,KACNsK,GACJ,EAAG,EAAE,EAEL,IAAMA,EAAyB,UAC3BhN,GAAW,GACX,IAAM2B,EAAW,MAAMC,EAAAA,CAAUA,CAACC,GAAG,CAAC,mBAAoBkL,GACtDpL,GAAYA,EAASG,IAAI,EAAIH,EAASG,IAAI,CAACC,MAAM,CAAG,GAAG,CACvDjC,EAAe6B,EAASG,IAAI,EAC5B5B,EAAayB,EAASK,UAAU,EAChChC,GAAW,GAEnB,EAQMiC,EAAsB,MAAOC,EAAiBtB,KAChDmM,EAAsBpM,KAAK,CAAGuB,EAC9B6K,EAAsBnM,IAAI,CAAGA,EAC7BZ,GAAW,GACX,IAAM2B,EAAW,MAAMC,EAAAA,CAAUA,CAACC,GAAG,CAAC,mBAAoBkL,GACtDpL,GAAYA,EAASG,IAAI,EAAIH,EAASG,IAAI,CAACC,MAAM,CAAG,GAAG,CACvDjC,EAAe6B,EAASG,IAAI,EAC5B1B,EAAW8B,GACXlC,GAAW,GAEnB,EAEMoC,EAAe,UACjB,GAAI,CACA,MAAMR,EAAAA,CAAUA,CAACS,MAAM,CAAC,oBAA0C,OAAtB8P,IAC5CnF,IACA1M,GAAS,GACTgC,EAAAA,EAAKA,CAACC,OAAO,CAACtE,EAAE,mEACpB,CAAE,MAAOuE,EAAO,CACZF,EAAAA,EAAKA,CAACE,KAAK,CAACvE,EAAE,6DAClB,CACJ,EAEMwD,EAAa,MAAOU,IACtBiQ,EAAyBjQ,EAAI1E,GAAG,EAChC6C,EAAS,GACb,EAEA,MACI,WAACY,MAAAA,WACG,WAACyB,EAAAA,CAAKA,CAAAA,CAACC,KAAMvC,EAAawC,OAAQJ,YAC9B,UAACE,EAAAA,CAAKA,CAACG,MAAM,EAACC,WAAW,aACrB,UAACJ,EAAAA,CAAKA,CAACK,KAAK,WAAE/E,EAAE,6CAEpB,UAAC0E,EAAAA,CAAKA,CAACM,IAAI,WAAEhF,EAAE,yCACf,WAAC0E,EAAAA,CAAKA,CAACO,MAAM,YACT,UAACC,EAAAA,CAAMA,CAAAA,CAACC,QAAQ,YAAY5B,QAASiB,WAChCxE,EAAE,YAEP,UAACkF,EAAAA,CAAMA,CAAAA,CAACC,QAAQ,UAAU5B,QAASY,WAC9BnE,EAAE,eAKf,UAACoF,EAAAA,CAAQA,CAAAA,CACLvC,QAASA,EACTgB,KAAMjC,EACNI,UAAWA,EACXqD,WAAW,EACXrB,oBAAqBA,EACrBsB,iBAzDc3C,CAyDI2C,GAxD1BwJ,EAAsBpM,KAAK,CAAGR,EAC9B4M,EAAsBnM,IAAI,CAAGA,EAC7BoM,GACJ,MAyDJ,+MCxEA,MAzCwB,QAiChBjJ,EAAAA,EAhCN,GAAM,GAAE9F,CAAC,CAAE,CAAGC,CAAAA,EAAAA,EAAAA,EAAAA,CAAcA,CAAC,UACvBmU,EAAsB,CAuCAC,EAAC,CArCzB,WAACnU,EAAAA,CAASA,CAAAA,CAAC+F,MAAO,CAAEC,UAAW,QAAS,EAAG/F,KAAK,IAACC,UAAU,gBACzD,UAACC,EAAAA,CAAGA,CAAAA,UACF,UAACC,EAAAA,CAAGA,CAAAA,CAACC,GAAI,YACP,UAAC4F,EAAAA,CAAWA,CAAAA,CAAC1G,MAAQO,EAAE,8CAG3B,UAACK,EAAAA,CAAGA,CAAAA,UACF,UAACC,EAAAA,CAAGA,CAAAA,CAACC,GAAI,YACP,UAAC2C,IAAIA,CACHC,KAAK,6BACLnC,GAAG,OAFAkC,uCAIH,UAACgC,EAAAA,CAAMA,CAAAA,CAACC,QAAQ,YAAYiB,KAAK,cAC9BpG,EAAE,yCAKX,UAACK,EAAAA,CAAGA,CAAAA,CAACD,UAAU,gBACb,UAACE,EAAAA,CAAGA,CAAAA,CAACC,GAAI,YACP,UAACgO,EAAAA,OAAeA,CAAAA,CAAAA,UAOpB+F,EAAqBC,CAAAA,EAAAA,EAAAA,iBAAAA,CAAiBA,CAAC,IAAM,UAACH,EAAAA,CAAAA,IAC9CtO,EAAYS,CAAAA,EAAAA,EAAAA,EAAAA,CAAWA,CAAC,GAAWT,SACzC,IAAI,GAAEA,GAAAA,OAAAA,EAAAA,EAAOU,IAAPV,OAAOU,GAAPV,MAAAA,GAAAA,EAAAA,WAA+B,EAA/BA,KAAAA,EAAAA,CAAiC,CAAC,GAAlCA,UAA+C,EAInD,CAJsD,EAItD,OAACwO,EAAAA,CAAAA,GAHM,UAAC5N,EAAAA,OAAeA,CAAAA,CAAAA,EAK3B,yPCgFA,MAhHqB,IAEnB,IAAM8N,EAAmB,CACvBhV,IAAK,GACLC,MAAO,EACT,EAEM,CAAC+H,EAAYC,EAAc,CAAG3F,CAAAA,EAAAA,EAAAA,CAyGX2S,EAAC,KAzGU3S,CAAQA,CAAW0S,GAEjD7M,EAAoB,CAAC,CAAEL,CAAAA,EAAMM,MAAM,EAAwB,oBAAdA,MAAM,CAAC,EAAE,EAAwBN,EAAMM,MAAM,CAAC,IAC3F,GAAE5H,CAAC,CAAE,CAAGC,CAAAA,EAAAA,EAAAA,EAAAA,CAAcA,CAAC,UAGvB4H,EAAUC,CAAAA,EAAAA,EAAAA,MAAAA,CAAMA,CAAC,MAkBjBC,EAAe,MAAOC,QAOtBtE,EANJsE,EAAME,cAAc,GACpB,IAAMC,EAAM,CACV1I,MAAO+H,EAAW/H,KAAK,CAAC2I,IAAI,EAC9B,CASI1E,EAJFA,EADEiE,EACS,MAAMhE,EAAAA,CAAUA,CAAC0E,KAAK,CAAC,aAA6B,OAAhBf,EAAMM,MAAM,CAAC,EAAE,EAAIO,GAEvD,MAAMxE,EAAAA,CAAUA,CAAC2E,IAAI,CAAC,YAAaH,KAEhCzE,EAASlE,GAAG,EAAE,EAC5B6E,EAAKA,CAACC,OAAO,CAACtE,EAAE,gCAChBuI,IAAAA,IAAW,CAAC,4BAEZlE,EAAAA,EAAKA,CAACE,KAAK,CAACb,EAEhB,EAiBA,MAfAe,CAAAA,EAAAA,EAAAA,SAAAA,CAASA,CAAC,KACR,IAAMsO,EAAiB,CACrBnQ,MAAO,CAAC,EACRH,KAAM,CAAEhD,MAAO,KAAM,EACrBiD,MAAO,GACT,EACIiF,GACsB,OADZ,IAEV,IAAMjE,EAAqB,MAAMC,EAAAA,CAAUA,CAACC,GAAG,CAAC,aAA6B,OAAhB0D,EAAMM,MAAM,CAAC,EAAE,EAAImL,GAChFtL,EAAekB,GAAe,EAAE,GAAGA,CAAS,CAAE,EAAhB,CAAmBjF,CAAQ,CAAC,GAC5D,GAGJ,EAAG,EAAE,EAGH,UAACT,MAAAA,UACC,UAAC/C,EAAAA,CAASA,CAAAA,CAACE,UAAU,WAAWD,KAAK,aACnC,UAACyI,EAAAA,CAAIA,CAAAA,CAAC3C,MAAO,CAAE4C,UAAW,MAAOC,UAAW,kEAAmE,WAC7G,UAACC,EAAAA,CAAqBA,CAAAA,CAACC,SAAUjB,EAAckB,IAAKpB,EAASqB,cAAe1B,EAAY2B,oBAAoB,WAC1G,WAACP,EAAAA,CAAIA,CAAC5D,IAAI,YACR,UAAC3E,EAAAA,CAAGA,CAAAA,UACF,UAACC,EAAAA,CAAGA,CAAAA,UACF,UAACsI,EAAAA,CAAIA,CAAC7D,KAAK,WAAE/E,EAAE,kBAGnB,UAACoJ,KAAAA,CAAAA,GACD,UAAC/I,EAAAA,CAAGA,CAAAA,UACF,UAACC,EAAAA,CAAGA,CAAAA,CAACE,EAAE,IAACY,GAAI,EAAGD,GAAI,YACjB,WAACkI,EAAAA,CAAIA,CAACC,KAAK,YACT,UAACD,EAAAA,CAAIA,CAACE,KAAK,EAACnJ,UAAU,0BAAkBJ,EAAE,cAC1C,UAACwJ,EAAAA,EAASA,CAAAA,CACR1G,KAAK,QACL2G,GAAG,QACHC,QAAQ,IAAC5I,MAAO0G,EAAW/H,KAAK,CAChCkK,UAAY,GAA+C,KAA/BC,OAAO9I,GAAS,IAAIsH,IAAI,GACpDyB,aAAc,CACZF,UAAW3J,EAAE,uBAAuB,EACtCe,SArEEM,CAqEQyI,GApE5B,GAAIzI,EAAE0I,MAAM,CAAE,CACZ,GAAM,MAAEjH,CAAI,OAAEhC,CAAK,CAAE,CAAGO,EAAE0I,MAAM,CAChCtC,EAAckB,GAAc,EAC1B,GAAGA,CAAS,CACZ,CAAC7F,CAFyB,CAEpB,CAAEhC,EACV,EACF,CACF,WAkEY,UAACT,EAAAA,CAAGA,CAAAA,CAACD,UAAU,gBACb,WAACE,EAAAA,CAAGA,CAAAA,WACF,UAAC4E,EAAAA,CAAMA,CAAAA,CAAC9E,UAAU,OAAOO,KAAK,SAASwE,QAAQ,mBAAWnF,EAAE,YAC5D,UAACkF,EAAAA,CAAMA,CAAAA,CAAC9E,UAAU,OAAOmD,QAnFpB,CAmF6ByG,IAlFhDvC,EAAc+M,GAEdvK,OAAOC,QAAQ,CAAC,EAAG,EACrB,EA+EgE/E,QAAQ,gBAAQnF,EAAE,WAClE,UAACkD,IAAIA,CACHC,KAAK,6BACLnC,GAAK,OAFFkC,4BAGF,UAACgC,EAAAA,CAAMA,CAAAA,CAACC,QAAQ,qBAAanF,EAAE,6BASpD,0JCpGA,MApByB,QAajB8F,EAAAA,EAZN,GAAM,GAAE9F,CAAC,CAAE,CAAGC,CAAAA,EAAAA,EAAAA,EAAAA,CAAcA,CAAC,UACvByU,EAAmB,EAkBIC,EAhBzB,WAACzU,EAAAA,CAASA,CAAAA,CAACC,KAAK,IAACC,UAAU,gBACzB,UAAC+F,EAAAA,CAAWA,CAAAA,CAAC1G,MAAOO,EAAE,uCACtB,UAAC4U,EAAAA,OAAWA,CAAAA,CAAAA,MAKZC,EAAwBC,CAAAA,EAAAA,EAAAA,oBAAAA,CAAoBA,CAAC,IAAM,UAACJ,EAAAA,CAAAA,IACpD5O,EAAYS,CAAAA,EAAAA,EAAAA,EAAAA,CAAWA,CAAC,GAAgBT,SAC9C,IAAI,GAAEA,GAAAA,OAAAA,EAAAA,EAAOU,IAAPV,OAAOU,GAAPV,OAAAA,EAAAA,EAAAA,uBAAoB6M,EAApB7M,KAAAA,EAAAA,CAA6C,CAAC,GAA9CA,UAA2D,EAI/D,CAJkE,EAIlE,OAAC+O,EAAAA,CAAAA,GAHM,UAACnO,EAAAA,OAAeA,CAAAA,CAAAA,EAK3B,yPCgKA,MA3KmB,IACf,GAAM,CAAE1G,CAAC,MAAE2K,CAAI,CAAE,CAAG1K,CAAAA,EAAAA,EAAAA,EAAAA,CAAcA,CAAC,CA0KxB8U,SAzKLjF,CAyKeiF,CAzKDpK,CAyKE,QAzKGG,QAAQ,CAAY,CAAEiF,SAAU,KAAM,EAAI,CAAEtQ,MAAO,KAAM,EAC5EoL,EAAcF,EAAKG,QAAQ,CAC3BkK,EAAiB,CACnBvV,MAAO,GACPsH,QAAS,IACb,EACM,CAACS,EAAYC,EAAc,CAAG3F,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAASkT,GAC/C,CAACjO,EAASkO,EAAW,CAAGnT,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAY,EAAE,EAE9C6F,EAAWL,EAAMM,MAAM,EAAwB,gBAApBN,EAAMM,MAAM,CAAC,EAAE,EAAsBN,EAAMM,MAAM,CAAC,EAAE,CAE/EC,EAAUC,CAAAA,EAAAA,EAAAA,MAAAA,CAAMA,CAAC,MAQjBC,EAAe,MAAOC,QAOpBtE,EACAuE,EAPJD,EAAME,cAAc,GACpB,IAAMC,EAAM,CACR1I,MAAO+H,EAAW/H,KAAK,CAAC2I,IAAI,GAC5BrB,QAASS,EAAWT,OACxB,EAIIY,GACAM,EAAW,KADD,8CAEVvE,EAAW,MAAMC,EAAAA,CAAUA,CAAC0E,KAAK,CAAC,WAA2B,OAAhBf,EAAMM,MAAM,CAAC,EAAE,EAAIO,KAEhEF,EAAW,iDACXvE,EAAW,MAAMC,EAAAA,CAAUA,CAAC2E,IAAI,CAAC,UAAWH,IAE5CzE,GAAYA,EAASlE,GAAG,EAAE,EAC1B6E,EAAKA,CAACC,OAAO,CAACtE,EAAEiI,IAChBM,IAAAA,IAAW,CAAC,0BAEZlE,EAAAA,EAAKA,CAACE,KAAK,CAACb,EAEpB,EAEMoG,EAAe,IACjB,GAAIzI,EAAE0I,MAAM,CAAE,CACV,GAAM,MAAEjH,CAAI,OAAEhC,CAAK,CAAE,CAAGO,EAAE0I,MAAM,CAChCtC,EAAc,GAAgB,EAC1B,GAAGkB,CAAS,CACZ,CAAC7F,CAFyB,CAEpB,CAAEhC,EACZ,EACJ,CACJ,EAEMoU,EAAa,MAAOC,IACtB,IAAMzR,EAAmC,MAAMC,EAAAA,CAAUA,CAACC,GAAG,CAAC,WAAYuR,GACtEzR,GACAuR,EAAWvR,EAASG,GADV,CACc,CAEhC,EAwBA,MAtBAY,CAAAA,EAAAA,EAAAA,SAAAA,CAASA,CAAC,KACN,IAAM0Q,EAAe,CACjBvS,MAAO,CAAC,EACRH,KAAMqN,EACNpN,MAAO,IACPyN,aAActF,CAClB,EAEIlD,GASAyN,CARsB,MADZ,IAEN,IAAM1R,EAAW,MAAMC,EAAAA,CAAUA,CAACC,GAAG,CAAC,WAA2B,OAAhB0D,EAAMM,MAAM,CAAC,EAAE,EAAIuN,GACpE,GAAIzR,EAAU,CACV,GAAM,SAAEqD,CAAO,CAAE,CAAGrD,EACpBA,EAASqD,OAAO,CAAGA,GAAWA,EAAQvH,GAAG,CAAGuH,EAAQvH,GAAG,CAAG,GAC1DiI,EAAc,GAAgB,EAAE,GAAGkB,CAAS,CAAE,EAAhB,CAAmBjF,CAAQ,CAAC,EAC9D,EACJ,IAGJwR,EAAWC,EACf,EAAG,EAAE,EAGD,UAAClS,MAAAA,UACG,UAAC/C,EAAAA,CAASA,CAAAA,CAACE,UAAU,WAAWD,KAAK,aACjC,UAACyI,EAAAA,CAAIA,CAAAA,CACD3C,MAAO,CACH4C,UAAW,MACXC,UAAW,kEACf,WAEA,UAACC,EAAAA,CAAqBA,CAAAA,CAACC,SAAUjB,EAAckB,IAAKpB,EAASqB,cAAe1B,EAAY2B,oBAAoB,WACxG,WAACP,EAAAA,CAAIA,CAAC5D,IAAI,YACN,UAAC3E,EAAAA,CAAGA,CAAAA,UACA,UAACC,EAAAA,CAAGA,CAAAA,UACA,UAACsI,EAAAA,CAAIA,CAAC7D,KAAK,WAAE4C,EAAW3H,EAAE,mCAAqCA,EAAE,wCAGzE,UAACoJ,KAAAA,CAAAA,GACD,WAAC/I,EAAAA,CAAGA,CAAAA,WACA,UAACC,EAAAA,CAAGA,CAAAA,CAACE,EAAE,IAACY,GAAI,EAAGD,GAAI,YACf,WAACkI,EAAAA,CAAIA,CAACC,KAAK,YACP,UAACD,EAAAA,CAAIA,CAACE,KAAK,EAACnJ,UAAU,0BACjBJ,EAAE,kCAEP,WAACqV,EAAAA,EAAWA,CAAAA,CACRvS,KAAK,UACL2G,GAAG,UACHC,QAAQ,IACR5I,MAAO0G,EAAWT,OAAO,CACzB4C,UAAW,GAAkD,KAA/BC,OAAO9I,GAAS,IAAIsH,IAAI,GACtDyB,aAAc,CACVF,UAAW3J,EAAE,2CACjB,EACAe,SAAU+I,YAEV,UAACrI,SAAAA,CAAOX,MAAM,YAAId,EAAE,wCACnB+G,EAAQjD,MAAM,EAAI,EACbiD,EAAQzF,GAAG,CAAC,CAACC,EAAM+T,IAEX,UAAC7T,SAAAA,CAAsBX,MAAOS,EAAK/B,GAAG,UACjC+B,EAAK9B,KAAK,EADF8B,EAAK/B,GAAG,GAK7B,aAIlB,UAACc,EAAAA,CAAGA,CAAAA,CAACE,EAAE,IAACY,GAAI,EAAGD,GAAI,YACf,WAACkI,EAAAA,CAAIA,CAACC,KAAK,YACP,UAACD,EAAAA,CAAIA,CAACE,KAAK,EAACnJ,UAAU,0BACjBJ,EAAE,iCAEP,UAACwJ,EAAAA,EAASA,CAAAA,CACN1G,KAAK,QACL2G,GAAG,QACHC,QAAQ,IACR5I,MAAO0G,EAAW/H,KAAK,CACvBoK,aAAc,CAACF,UAAW3J,EAAE,0CAA0C,EACtEe,SAAU+I,YAK1B,UAACzJ,EAAAA,CAAGA,CAAAA,CAACD,UAAU,gBACX,WAACE,EAAAA,CAAGA,CAAAA,WACA,UAAC4E,EAAAA,CAAMA,CAAAA,CAAC9E,UAAU,OAAOO,KAAK,SAASwE,QAAQ,mBAC1CnF,EAAE,iCAEP,UAACkF,EAAAA,CAAMA,CAAAA,CAAC9E,UAAU,OAAOmD,QAzIpC,CAyI6CyG,IAxI9DvC,EAAcuN,GAEd/K,OAAOC,QAAQ,CAAC,EAAG,EACvB,EAqIgF/E,QAAQ,gBACnDnF,EAAE,gCAEP,UAACkD,IAAIA,CACDC,KAAK,6BACLnC,GAAK,OAFJkC,0BAID,UAACgC,EAAAA,CAAMA,CAAAA,CAACC,QAAQ,qBAAanF,EAAE,kDAUvE,yPCxCA,MAlI0B,IACtB,GAAM,CAAEA,CAAC,CAAE,CAAGC,CAAAA,EAAAA,EAAAA,EAAAA,CAAcA,CAAC,QAiIlBsV,EAhILC,EAAwB,CAC1B/V,MAAO,EACX,EAEM,CAAC+H,CA4HqB+N,CA5HT9N,CA4HU,CA5HI,CAAG3F,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAgB0T,GAEtD7N,EAAWL,EAAMM,MAAM,EAAwB,uBAApBN,EAAMM,MAAM,CAAC,EAAE,EAA6BN,EAAMM,MAAM,CAAC,EAAE,CAEtFC,EAAUC,CAAAA,EAAAA,EAAAA,MAAAA,CAAMA,CAAC,MAkBjBC,EAAe,MAAOC,QAMpBtE,EACAuE,EANJD,EAAME,cAAc,GACpB,IAAMC,EAAM,CACR1I,MAAO+H,EAAW/H,KAAK,CAAC2I,IAAI,EAChC,EAIIT,GACAM,EAAW,KADD,sCAEVvE,EAAW,MAAMC,EAAAA,CAAUA,CAAC0E,KAAK,CAAC,kBAAkC,OAAhBf,EAAMM,MAAM,CAAC,EAAE,EAAIO,KAEvEF,EAAW,yCACXvE,EAAW,MAAMC,EAAAA,CAAUA,CAAC2E,IAAI,CAAC,iBAAkBH,IAEnDzE,GAAYA,EAASlE,GAAG,EAAE,EAC1B6E,EAAKA,CAACC,OAAO,CAACtE,EAAEiI,IAChBM,IAAAA,IAAW,CAAC,iCAER7E,OAAAA,EAAAA,KAAAA,EAAAA,EAAU8E,SAAAA,CAAV9E,GAAwB,KACxBW,EAD+B,EAC1BA,CAACE,KAAK,CAACvE,EAAE,yBAEdqE,EAAAA,EAAKA,CAACE,KAAK,CAACb,EAGxB,EAiBA,MAfAe,CAAAA,EAAAA,EAAAA,SAAAA,CAASA,CAAC,KACN,IAAMjC,EAAsB,CACxBI,MAAO,CAAC,EACRH,KAAM,CAAEhD,MAAO,KAAM,EACrBiD,MAAO,GACX,EACIiF,GAKAlE,CAJ6B,MADnB,IAEN,IAAMC,EAA0B,MAAMC,EAAAA,CAAUA,CAACC,GAAG,CAAC,kBAAkC,OAAhB0D,EAAMM,MAAM,CAAC,EAAE,EAAIpF,GAC1FiF,EAAc,GAAgB,EAAE,GAAGkB,CAAS,CAAE,EAAhB,CAAmBjF,CAAQ,CAAC,EAC9D,IAGR,EAAG,EAAE,EAGD,UAACT,MAAAA,UACG,UAAC/C,EAAAA,CAASA,CAAAA,CAACE,UAAU,WAAWD,KAAK,aACjC,UAACyI,EAAAA,CAAIA,CAAAA,CACD3C,MAAO,CACH4C,UAAW,MACXC,UAAW,kEACf,WAEA,UAACC,EAAAA,CAAqBA,CAAAA,CAACC,SAAUjB,EAAckB,IAAKpB,EAASqB,cAAe1B,EAAY2B,oBAAoB,WACxG,WAACP,EAAAA,CAAIA,CAAC5D,IAAI,YACN,UAAC3E,EAAAA,CAAGA,CAAAA,UACA,UAACC,EAAAA,CAAGA,CAAAA,UACA,UAACsI,EAAAA,CAAIA,CAAC7D,KAAK,WAAE/E,EAAE,kDAGvB,UAACoJ,KAAAA,CAAAA,GACD,UAAC/I,EAAAA,CAAGA,CAAAA,UACA,UAACC,EAAAA,CAAGA,CAAAA,CAACE,EAAE,IAACY,GAAI,EAAGD,GAAI,YACf,WAACkI,EAAAA,CAAIA,CAACC,KAAK,YACP,UAACD,EAAAA,CAAIA,CAACE,KAAK,EAACnJ,UAAU,0BACjBJ,EAAE,8CAEP,UAACwJ,EAAAA,EAASA,CAAAA,CACN1G,KAAK,QACL2G,GAAG,QACHC,QAAQ,IACR5I,MAAO0G,EAAW/H,KAAK,CACvBkK,UAAW,GAAoC,KAAjB7I,EAAMsH,IAAI,GACxCyB,aAAc,CACVF,UAAW3J,EAAE,uDACjB,EACAe,SApFlBM,CAoF4ByI,GAnF9C,GAAIzI,EAAE0I,MAAM,CAAE,CACV,GAAM,MAAEjH,CAAI,OAAEhC,CAAK,CAAE,CAAGO,EAAE0I,MAAM,CAChCtC,EAAekB,GAAe,EAC1B,GAAGA,CAAS,CACZ,CAAC7F,CAFyB,CAEpB,CAAEhC,CACZ,GACJ,CACJ,WAiFwB,UAACT,EAAAA,CAAGA,CAAAA,CAACD,UAAU,gBACX,WAACE,EAAAA,CAAGA,CAAAA,WACA,UAAC4E,EAAAA,CAAMA,CAAAA,CAAC9E,UAAU,OAAOO,KAAK,SAASwE,QAAQ,mBAC1CnF,EAAE,uCAEP,UAACkF,EAAAA,CAAMA,CAAAA,CAAC9E,UAAU,OAAOmD,QApGpC,CAoG6CyG,IAnG9DvC,EAAc+N,GAEdvL,OAAOC,QAAQ,CAAC,EAAG,EACvB,EAgGgF/E,QAAQ,gBACnDnF,EAAE,sCAEP,UAACkD,IAAIA,CACDC,KAAK,6BACLnC,GAAK,OAFJkC,iCAID,UAACgC,EAAAA,CAAMA,CAAAA,CAACC,QAAQ,qBAAanF,EAAE,wDAUvE,yPCIA,MArIwB,IACpB,IAAMyV,EAAsB,CACxBjW,IAAK,GACLC,MAAO,EACX,EACM,GAAEO,CAAC,CAAE,CAAGC,CAAAA,EAAAA,EAAAA,EAAAA,CAAcA,CAAC,EAgIF,QA/HrB,CAACuH,EAAYC,EAAc,CAAG3F,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAc2T,GAEpD9N,EAAoBL,EAAMM,MAAM,EAAwB,qBAApBN,EAAMM,MAAM,CAAC,EAAE,EAA2BN,EAAMM,MAAM,CAAC,EAAE,CAE7FC,EAAUC,CAAAA,EAAAA,EAAAA,MAAAA,CAAMA,CAAC,MAkBjBC,EAAe,MAAOC,QAOpBtE,EACAuE,EAPJD,EAAME,cAAc,GAEpB,IAAMC,EAAM,CACR1I,MAAO+H,EAAW/H,KAAK,CAAC2I,IAAI,EAChC,EAIIT,GACAM,EAAW,KADD,6DAEVvE,EAAW,MAAMC,EAAAA,CAAUA,CAAC0E,KAAK,CAAC,gBAAgC,OAAhBf,EAAMM,MAAM,CAAC,EAAE,EAAIO,KAErEF,EAAW,gEACXvE,EAAW,MAAMC,EAAAA,CAAUA,CAAC2E,IAAI,CAAC,eAAgBH,IAEjDzE,GAAYA,EAASlE,GAAG,EAAE,EAC1B6E,EAAKA,CAACC,OAAO,CAACtE,EAAEiI,IAChBM,IAAAA,IAAW,CAAC,+BAER7E,OAAAA,EAAAA,KAAAA,EAAAA,EAAU8E,SAAAA,CAAV9E,GAAwB,KACxBW,EAAAA,EAAKA,CAACE,KAAK,CAACvE,EAAE,yBAEdqE,EAAAA,EAAKA,CAACE,KAAK,CAACb,EAGxB,EAiBA,MAfAe,CAAAA,EAAAA,EAAAA,SAAAA,CAASA,CAAC,KACN,IAAM+O,EAAoB,CACtB5Q,MAAO,CAAC,EACRH,KAAM,CAAEhD,MAAO,KAAM,EACrBiD,MAAO,GACX,EACIiF,GAKA+N,CAJ2B,MADjB,IAEN,IAAMhS,EAAwB,MAAMC,EAAAA,CAAUA,CAACC,GAAG,CAAC,gBAAgC,OAAhB0D,EAAMM,MAAM,CAAC,EAAE,EAAI4L,GACtF/L,EAAc,GAAgB,EAAE,GAAGkB,CAAS,CAAE,EAAhB,CAAmBjF,CAAQ,IAC7D,GAGR,EAAG,EAAE,EAGD,UAACT,MAAAA,UACG,UAAC/C,EAAAA,CAASA,CAAAA,CAACE,UAAU,WAAWD,KAAK,aACjC,UAACyI,EAAAA,CAAIA,CAAAA,CACD3C,MAAO,CACH4C,UAAW,MACXC,UAAW,kEACf,WAEA,UAACC,EAAAA,CAAqBA,CAAAA,CAACC,SAAUjB,EAAckB,IAAKpB,EAASqB,cAAe1B,EAAY2B,oBAAoB,WACxG,WAACP,EAAAA,CAAIA,CAAC5D,IAAI,YACN,UAAC3E,EAAAA,CAAGA,CAAAA,UACA,UAACC,EAAAA,CAAGA,CAAAA,UACA,UAACsI,EAAAA,CAAIA,CAAC7D,KAAK,WAAE/E,EAAE,oDAGvB,UAACoJ,KAAAA,CAAAA,GACD,UAAC/I,EAAAA,CAAGA,CAAAA,UACA,UAACC,EAAAA,CAAGA,CAAAA,CAACE,EAAE,IAACY,GAAI,EAAGD,GAAI,YACf,WAACkI,EAAAA,CAAIA,CAACC,KAAK,YACP,UAACD,EAAAA,CAAIA,CAACE,KAAK,EAACnJ,UAAU,0BACjBJ,EAAE,gDAEP,UAACwJ,EAAAA,EAASA,CAAAA,CACN1G,KAAK,QACL2G,GAAG,QACHC,QAAQ,IACR5I,MAAO0G,EAAW/H,KAAK,CACvBkK,UAAW,GAA+C,KAA/BC,OAAO9I,GAAS,IAAIsH,IAAI,GACnDyB,aAAc,CACVF,UAAW3J,EAAE,yDACjB,EACAe,SArFlBM,CAqF4ByI,GApF9C,GAAIzI,EAAE0I,MAAM,CAAE,CACV,GAAM,MAAEjH,CAAI,OAAEhC,CAAK,CAAE,CAAGO,EAAE0I,MAAM,CAChCtC,EAAc,GAAgB,EAC1B,GAAGkB,CAAS,CACZ,CAAC7F,CAFyB,CAEpB,CAAEhC,EACZ,EACJ,CACJ,WAkFwB,UAACT,EAAAA,CAAGA,CAAAA,CAACD,UAAU,gBACX,WAACE,EAAAA,CAAGA,CAAAA,WACA,UAAC4E,EAAAA,CAAMA,CAAAA,CAAC9E,UAAU,OAAOO,KAAK,SAASwE,QAAQ,mBAC1CnF,EAAE,2CAEP,UAACkF,EAAAA,CAAMA,CAAAA,CAAC9E,UAAU,OAAOmD,QArGpC,CAqG6CyG,IApG9DvC,EAAcgO,GAEdxL,OAAOC,QAAQ,CAAC,EAAG,EACvB,EAiGgF/E,QAAQ,gBACnDnF,EAAE,0CAEP,UAACkD,IAAIA,CACDC,KAAK,6BACLnC,GAAK,OAFJkC,+BAID,UAACgC,EAAAA,CAAMA,CAAAA,CAACC,QAAQ,qBACXnF,EAAE,4DAW/C,yRCrIA,IAAM2V,EAAwB,CAAChV,EAAciV,KACzC,OAAQjV,GACJ,IAAK,YACDiV,EAAOC,MAAM,CACT,8KACJ,KACJ,KAAK,cACDD,EAAOC,MAAM,CACT,mRACJ,KACJ,KAAK,QACDD,EAAOC,MAAM,CACT,6OACJ,KACJ,KAAK,UACDD,EAAOC,MAAM,CACT,sKACJ,KACJ,KAAK,UACDD,EAAOC,MAAM,CACT,+OACJ,KACJ,KAAK,SACDD,EAAOC,MAAM,CACT,kMACJ,KACJ,SACID,EAAOC,MAAM,CACT,sIAEZ,CACA,OAAOD,CACX,WA4QA,MAtQgB,IACZ,GAAM,CAACE,EAAWC,EAAa,CAAGjU,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAgB,EAAE,EACtD,GAAE9B,CAAC,CAAE,CAAGC,CAAAA,EAAAA,EAAAA,EAAAA,CAAcA,CAAC,UACvB,CAACU,EAAMqV,EAAQ,CAAGlU,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAS,aACnC,CAACmJ,EAAuBC,EAAyB,CAAGF,EAAAA,QAAc,EAAU,GAC5E,CAACtL,EAAYqL,EAAc,CAAGC,EAAAA,QAAc,CAAS,IACrD,CAAChJ,EAAWC,EAAa,CAAGH,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAS,GAC7C,CAACI,EAASC,EAAW,CAAGL,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAS,IACzC,CAACmU,EAASlU,EAAW,CAAGD,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,EAAU,GAC1C,CAACM,EAAaC,EAAS,CAAGP,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,EAAU,GAC5C,CAACoU,EAAaC,EAAe,CAAGrU,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAM,MAC9C,CAACsU,EAAaC,EAAe,CAAGvU,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAS,GAEjD,CAACwU,EAAaC,EAAe,CAAGzU,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAc,MAExD8T,EAAc,CACdlT,MAAOR,EACPO,KAAM,CAAE+T,WAAY,MAAO,CAC/B,EAEM3T,EAAU,CACZ,CACIC,KAAM9C,EAAE,oCACR+C,SAAWmB,GAAaA,EAAIzE,KAAK,CACjCuD,KAAM,QAgPII,CAAc,QA/OpBA,EAAEzC,IAAI,CACF,UAACuC,IAAIA,CAACC,KAAM,IAAW,OAAPC,EAAEzC,IAAI,CAAC,gBAAeK,GAAI,EAArCkC,EAAwDE,MAAAA,CAAfA,EAAEzC,IAAI,CAAC,UAA8BA,MAAAA,CAAtByC,CAAC,CAACqT,EAAarT,EA+OrF,UAAiB,OAAPA,EAAEzC,IAAI,EA/OwE,CAAC,KAAWyC,MAAAA,CAARzC,EAAK,KAAS,OAANyC,EAAE5D,GAAG,WAC3F4D,EAAE3D,KAAK,GAGZ,UAACyD,IAAIA,CAACC,KAAM,IAAS,OAALxC,EAAK,gBAAeK,GAAI,IAAiBoC,GAApDF,GAAoDE,CAAbzC,EAAK,UAAc,OAANyC,EAAE5D,GAAG,WACzD4D,EAAE3D,KAAK,IAGpBkG,UAAU,CACd,EACA,CACI7C,KAAM9C,EAAE,qCACR+C,SAAU,QAAsBmB,QAAAA,CAAAA,OAAAA,EAAAA,EAAIwS,IAAI,EAARxS,KAAAA,EAAAA,EAAUyS,GAAVzS,KAAUyS,GAAY,IACtD3T,KAAM,GAAqBI,EAAEsT,IAAI,CAAGtT,EAAEsT,IAAI,CAACC,QAAQ,CAAG,GACtDhR,UAAU,CACd,EACA,CACI7C,KAAM9C,EAAE,sCACR+C,SAAWmB,GAAqBA,EAAIsS,UAAU,CAC9CxT,KAAM,GAAoB4T,IAAOxT,EAAEoT,UAAU,EAAEK,MAAM,CAAC,SACtDlR,UAAU,CACd,EACA,CACI7C,KAAM9C,EAAE,sCACR+C,SAAU,GAAsBmB,EAAI4S,UAAU,CAC9C9T,KAAM,GAAoB4T,IAAOxT,EAAE0T,UAAU,EAAED,MAAM,CAAC,SACtDlR,UAAU,CADsBiR,EAGpC,CACI9T,KAAM9C,EAAE,qCACR+C,SAAWmB,GAAqBA,EAAI1E,GAAG,CACvCmG,UAAU,EACV3C,KAAOI,QAEDkT,EAAgDA,EAAuElT,EAclHkT,EAAgDA,QAfvD,+BACC,QAACA,GAAAA,OAAAA,EAAAA,EAAaS,KAAAA,EAAbT,GAAAA,EAAAA,EAAAA,EAAoBU,GAApBV,KAA4B,CAAC,yBAAmBA,GAAAA,OAAAA,EAAAA,EAAaS,KAAAA,EAAbT,GAAAA,EAAAA,EAAAA,EAAoBU,GAApBV,KAA4B,CAAC,mBAAgB,EAAMA,OAAAA,EAAAA,KAAAA,EAAAA,EAAa9W,GAAAA,UAAb8W,GAAoBlT,OAAAA,EAAAA,EAAAA,IAAGsT,EAAHtT,KAAAA,EAAAA,EAAS5D,GAAT4D,EACpH,WAACH,MAAAA,WACE,UAACC,IAAIA,CAACC,KAAM,IAAS,OAALxC,EAAK,gBAAeK,GAAI,IAAiBoC,GAApDF,GAAoDE,CAAbzC,EAAK,UAAc,OAANyC,EAAE5D,GAAG,WAE1D,UAAC6D,IAAAA,CAAEjD,UAAU,uBAEV,OAEP,UAAC8C,IAAIA,CAACC,KAAK,IAAII,QAAS,GAAOC,EAAWJ,EAAG/B,YAEzC,QAFC6B,EAEAG,IAAAA,CAAEjD,UAAU,+BAIpB,CAAEkW,MAAAA,GAAAA,OAAAA,EAAAA,EAAaS,KAAAA,EAAbT,GAAAA,EAAAA,EAAAA,EAAoBU,GAApBV,KAA4B,CAAC,yBAAmBA,GAAAA,OAAAA,EAAAA,EAAaS,KAAAA,EAAbT,GAAAA,EAAAA,EAAAA,EAAoBU,GAApBV,KAA4B,CAAC,kBAAgB,CAavF,GAZR,WAACrT,MAAAA,WACE,UAACC,IAAIA,CAACC,KAAM,IAAS,OAALxC,EAAK,gBAAeK,GAAI,IAAiBoC,GAApDF,GAAoDE,CAAbzC,EAAK,UAAc,OAANyC,EAAE5D,GAAG,WAE1D,UAAC6D,IAAAA,CAAEjD,UAAU,uBAEV,OAEP,UAAC8C,IAAIA,CAACC,KAAK,IAAII,QAAS,GAAOC,EAAWJ,EAAG/B,YAEzC,QAFC6B,EAEAG,IAAAA,CAAEjD,UAAU,iCAQjC,EACH,CAEK6W,EAAY,MAAOrB,IACrB7T,EAAW,IACX6T,EAASD,EAAsBhV,EAAMiV,GACrC,IAAMsB,EAAsB,MAAMvT,EAAAA,CAAUA,CAAC2E,IAAI,CAAC,uBAAwB,CAAC,GACvE4O,GAAuBA,EAAoBP,QAAQ,EAAE,EACtCO,GAEnB,IAAMxT,EAAuC,MAAMC,EAAAA,CAAUA,CAACC,GAAG,CAAC,IAAS,OAALjD,GAAQiV,GAC1ElS,GAAYA,EAASG,IAAI,EAAIsT,MAAMC,OAAO,CAAC1T,EAASG,IAAI,GAAKH,EAASG,IAAI,CAACC,MAAM,CAAG,GAAG,EAC1EJ,EAASG,IAAI,EAC1B5B,EAAayB,EAASK,UAAU,EAAI,IAEpCgS,EAAa,EAAE,EAEnBhU,GAAW,EACf,EAEMyB,EAAa,MAAOU,EAAkB7C,KACxCA,EAAE6G,cAAc,GAChBiO,EAAe,CAAE1M,GAAIvF,EAAI1E,GAAG,CAAEmB,KAAMA,CAAK,GACzC0B,GAAS,EACb,EAWM2B,EAAsB,MAAOC,EAAoBtB,KACnDZ,GAAW,GACX6T,EAAOlT,KAAK,CAAGuB,EACf2R,EAAOjT,IAAI,CAAGA,EACd0T,EAAe1T,GACfiT,EAASD,EAAsBhV,EAAMiV,GACrC,IAAMlS,EAAuC,MAAMC,EAAAA,CAAUA,CAACC,GAAG,CAAC,IAAS,OAALjD,GAAQiV,GAC1ElS,GAAYyT,MAAMC,OAAO,CAAC1T,EAASG,IAAI,GAAG,CAC1CkS,EAAarS,EAASG,IAAI,EAC1B1B,EAAW8B,GACXlC,GAAW,GAEnB,EAEMyC,EAAY,IAAMnC,GAAS,GAE3B8B,EAAe,UACjB,GAAI,CACA,MAAMR,EAAAA,CAAUA,CAACS,MAAM,CAAC,IAAwB8R,MAAAA,CAApBA,EAAYvV,IAAI,CAAC,KAAkB,OAAfuV,EAAYzM,EAAE,GAC9DmM,EAAOjT,IAAI,CAAGyT,EACda,EAAUrB,GACVvT,GAAS,GACTgC,EAAAA,EAAKA,CAACC,OAAO,CAACtE,EAAE,yDACpB,CAAE,MAAOuE,EAAO,CACZF,EAAAA,EAAKA,CAACE,KAAK,CAACvE,EAAE,mDAClB,CACJ,EAEAyE,CAAAA,EAAAA,EAAAA,SAAAA,CAASA,CAAC,KACNwS,EAAUrB,EACd,EAAG,CAACjV,EAAK,EAET,IAAM0W,EAAa,MAAOnW,EAAaoW,KACnCvV,GAAW,GACX6T,EAAOnT,IAAI,CAAG,CACV,CAACvB,EAAO6B,QAAQ,CAAC,CAAEuU,CACvB,EACA,MAAML,EAAUrB,GAChB7T,GAAW,EACf,EAEMmK,EAAyBlB,EAAAA,OAAa,CAAC,KAQzC,IAAMuM,EAAyB,IAC3BvB,EAAQwB,EACZ,EAEMrL,EAAY,IACVC,GAAG,CACHwJ,EAAOhT,KAAK,CAAG,CAAEnD,MAAO2M,CAAE,GAE9B6K,EAAUrB,EACd,EAEMvJ,EAAoBd,IAAAA,QAAU,CAAC,GAAeY,EAAUC,GAAIE,OAAOC,KAAgC,GAAK,KAO9G,MACI,UAAC5M,EAAAA,OAAkBA,CAAAA,CACfC,SAPa,CAOHkK,GANdiB,EAAc1J,EAAE0I,MAAM,CAACjJ,KAAK,EAC5BuL,EAAkBhL,EAAE0I,MAAM,CAACjJ,KAAK,CACpC,EAKQhB,QA5BY,CA4BH0M,IA3BT9M,IACAwL,EAAyB,CAACD,GAC1BF,EAFY,IAIpB,EAwBQrL,WAAYA,EACZG,mBAAoB,GAA6C0X,EAAuBlW,EAAE0I,MAAM,CAACjJ,KAAK,EACtGf,WAAYY,GAGxB,EAAG,CAACjB,EAAYiB,EAAMsK,EAAsB,EAE5C,MACI,WAAC/K,EAAAA,CAASA,CAAAA,CAAC+F,MAAO,CAAEC,UAAW,QAAS,EAAG/F,KAAK,IAACC,UAAU,gBACvD,UAACC,EAAAA,CAAGA,CAAAA,UACA,UAACC,EAAAA,CAAGA,CAAAA,CAACC,GAAI,YACL,UAAC4F,EAAAA,CAAWA,CAAAA,CAAC1G,MAAOO,EAAE,4CAG9B,UAACK,EAAAA,CAAGA,CAAAA,CAACD,UAAU,gBACX,WAACE,EAAAA,CAAGA,CAAAA,CAACC,GAAI,aACL,WAACmE,EAAAA,CAAKA,CAAAA,CAACC,KAAMvC,EAAawC,OAAQJ,YAC9B,UAACE,EAAAA,CAAKA,CAACG,MAAM,EAACC,WAAW,aACrB,UAACJ,EAAAA,CAAKA,CAACK,KAAK,WAAE/E,EAAE,gDAEpB,UAAC0E,EAAAA,CAAKA,CAACM,IAAI,WAAEhF,EAAE,mEACf,WAAC0E,EAAAA,CAAKA,CAACO,MAAM,YACT,UAACC,EAAAA,CAAMA,CAAAA,CAACC,QAAQ,YAAY5B,QAASiB,WAChCxE,EAAE,uCAEP,UAACkF,EAAAA,CAAMA,CAAAA,CAACC,QAAQ,UAAU5B,QAASY,WAC9BnE,EAAE,0CAIf,UAACoF,EAAAA,CAAQA,CAAAA,CACLvC,QAASA,EACToT,QAASA,EACTpS,KAAMiS,EACN9T,UAAWA,EACXyV,mBAAoBvV,EACpBuK,SAAS,IACTiL,OAAQL,EACRM,UAAU,IACVtS,WAAW,EACXqH,mBAAoBR,EACpB0L,gBAAgB,IAChB3M,sBAAuBA,EACvBjH,oBAAqBA,EACrBsB,iBA5HK,CA4HaA,GA3HlCsQ,EAAOjT,IAAI,CAAGA,EACK,IAAI,CAAnBjD,IACAkW,EAAOhT,KAAK,CAAG,CAAEnD,MAAOC,EAAW,EAEvC2W,EAAe1T,GACfsU,EAAUrB,EACd,WA2HJ,8KC1KA,MA1H8B,IAC1B,GAAM,CAAE5V,CAAC,CAAE,CAAGC,CAAAA,EAAAA,EAAAA,EAAAA,CAAcA,CAAC,QAyHlByR,EAxHL,CAAC9P,EAAWC,EAAe,CAAGC,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAwHX,EAxHc,EACzC,EAAGC,EAAW,CAAGD,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAC,IAC1B,CAACE,EAAWC,EAAa,CAAGH,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAC,GACrC,CAACI,EAASC,EAAW,CAAGL,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAC,IACjC,CAACM,EAAaC,EAAS,CAAGP,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,EAAC,GACnC,CAAC+V,EAAwBC,EAA0B,CAAGhW,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAC,CAAC,GAGhE6L,EAAyB,CAC3BlL,KAAM,CAAEhD,MAAO,KAAM,EACrBiD,MAAOR,EACPS,KAAM,EACNC,MAAO,CAAC,CACZ,EAEMC,EAAU,CACZ,CACIC,KAAM9C,EAAE,6CACR+C,SAAU,OACd,EACA,CACID,KAAM9C,EAAE,8CACR+C,SAAU,GACVC,KAAOI,GACH,WAACH,MAAAA,WACG,UAACC,IAAIA,CAACC,KAAK,6BAA6BnC,GAAI,OAAvCkC,iCAAqF,OAANE,EAAE5D,GAAG,WAErF,UAAC6D,IAAAA,CAAEjD,UAAU,uBAEV,OAEP,UAACkD,IAAAA,CAAEC,QAAS,IAAMC,EAAWJ,YACzB,UAACC,IAAAA,CAAEjD,UAAU,8BAI7B,EACH,CAEK2X,EAA0B,UAC5BhW,GAAW,GACX,IAAM2B,EAAW,MAAMC,EAAAA,CAAUA,CAACC,GAAG,CAAC,oBAAqB+J,GACvDjK,GAAYA,EAASG,IAAI,EAAIH,EAASG,IAAI,CAACC,MAAM,CAAG,GAAG,CACvDjC,EAAe6B,EAASG,IAAI,EAC5B5B,EAAayB,EAASK,UAAU,EAChChC,GAAW,GAEnB,EAQMiC,EAAsB,MAAOC,EAAiBtB,KAChDgL,EAAuBjL,KAAK,CAAGuB,EAC/B0J,EAAuBhL,IAAI,CAAGA,EAC9BZ,GAAW,GACX,IAAM2B,EAAW,MAAMC,EAAAA,CAAUA,CAACC,GAAG,CAAC,oBAAqB+J,GACvDjK,GAAYA,EAASG,IAAI,EAAIH,EAASG,IAAI,CAACC,MAAM,CAAG,GAAG,CACvDjC,EAAe6B,EAASG,IAAI,EAC5B1B,EAAW8B,GACXlC,GAAW,GAEnB,EAEMyB,EAAa,MAAOU,IACtB4T,EAA0B5T,EAAI1E,GAAG,EACjC6C,GAAS,EACb,EAEM8B,EAAe,UACjB,GAAI,CACA,MAAMR,EAAAA,CAAUA,CAACS,MAAM,CAAC,qBAA4C,OAAvByT,IAC7CE,IACA1V,GAAS,GACTgC,EAAAA,EAAKA,CAACC,OAAO,CAACtE,EAAE,2EACpB,CAAE,MAAOuE,EAAO,CACZF,EAAAA,EAAKA,CAACE,KAAK,CAACvE,EAAE,qEAClB,CACJ,EAEMwE,EAAY,IAAMnC,GAAS,GAMjC,MAJAoC,CAAAA,EAAAA,EAAAA,SAAAA,CAASA,CAAC,KACNsT,GACJ,EAAG,EAAE,EAGD,WAAC9U,MAAAA,WACG,WAACyB,EAAAA,CAAKA,CAAAA,CAACC,KAAMvC,EAAawC,OAAQJ,YAC9B,UAACE,EAAAA,CAAKA,CAACG,MAAM,EAACC,WAAW,aACrB,UAACJ,EAAAA,CAAKA,CAACK,KAAK,WAAE/E,EAAE,kEAEpB,UAAC0E,EAAAA,CAAKA,CAACM,IAAI,WACNhF,EAAE,qFAEP,WAAC0E,EAAAA,CAAKA,CAACO,MAAM,YACT,UAACC,EAAAA,CAAMA,CAAAA,CAACC,QAAQ,YAAY5B,QAASiB,WAChCxE,EAAE,gDAEP,UAACkF,EAAAA,CAAMA,CAAAA,CAACC,QAAQ,UAAU5B,QAASY,WAC9BnE,EAAE,mDAKf,UAACoF,EAAAA,CAAQA,CAAAA,CACLvC,QAASA,EACTgB,KAAMjC,EACNI,UAAWA,EACXqD,WAAW,EACXrB,oBAAqBA,EACrBsB,iBAjEa,CAiEKA,GAhE1BqI,EAAuBjL,KAAK,CAAGR,EAC/ByL,EAAuBhL,IAAI,CAAGA,EAC9BoV,GACJ,MAiEJ,kOCnEA,MAnDuB,QAmCfjS,EAAAA,EAlCN,GAAM,GAAE9F,CAAC,CAAE,CAAGC,CAAAA,EAAAA,EAAAA,EAAAA,CAAcA,CAAC,UACvB+X,EAAqB,EAiDC,EA/CxB,UAAC/U,MAAAA,UACC,WAAC/C,EAAAA,CAASA,CAAAA,CAAC+F,MAAO,CAAEC,UAAW,QAAS,EAAG/F,KAAK,IAACC,UAAU,gBACzD,UAACC,EAAAA,CAAGA,CAAAA,UACF,UAACC,EAAAA,CAAGA,CAAAA,CAACC,GAAI,YACP,UAAC4F,EAAAA,CAAWA,CAAAA,CAAC1G,MAAOO,EAAE,gDAG1B,UAACK,EAAAA,CAAGA,CAAAA,UACF,UAACC,EAAAA,CAAGA,CAAAA,CAACC,GAAI,YACP,UAAC2C,IAAIA,CACHC,KAAK,6BACLnC,GAAG,OAFAkC,oCAIH,UAACgC,EAAAA,CAAMA,CAAAA,CAACC,QAAQ,YAAYiB,KAAK,cAChCpG,EAAE,qDAKT,UAACK,EAAAA,CAAGA,CAAAA,CAACD,UAAU,gBACb,UAACE,EAAAA,CAAGA,CAAAA,CAACC,GAAI,YACP,UAAC0X,EAAAA,OAAcA,CAAAA,CAAAA,YAQrBC,EAAmBC,CAAAA,EAAAA,EAAAA,eAAAA,CAAeA,CAAC,IAAM,UAACH,EAAAA,CAAAA,IAC1ClS,EAAYS,CAAAA,EAAAA,EAAAA,EAAAA,CAAWA,CAAC,GAAWT,SACzC,IAAI,GAAEA,GAAAA,OAAAA,EAAAA,EAAOU,IAAPV,OAAkB,GAAlBA,OAAAA,EAAAA,EAAAA,SAAoBsS,EAApBtS,KAAAA,EAAAA,CAA+B,CAAC,GAAhCA,UAA6C,EAIjD,UAACoS,EAAAA,CAAAA,GAHM,UAACxR,EAAAA,OAAeA,CAAAA,CAAAA,EAK3B,0JCoHA,MAhKA,SAASkO,CAAuB,EAC9B,GAAM,CAAE5U,CAAC,CAAE,CAAGC,CAAAA,EAAAA,EAAAA,EAAAA,CAAcA,CAAC,UACvB,CA8JkB2U,EA9JN/S,EAAe,CAAGC,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAQ,EAAE,EAChD,EAAGC,EAAW,CAAGD,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,EAAU,GACnC,CAACE,EAAWC,EAAa,CAAGH,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAS,GAC7C,CAACI,EAASC,EAAW,CAAGL,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAS,IACzC,CAACM,EAAaC,EAAS,CAAGP,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,EAAU,GAC5C,CAACuW,EAAWC,EAAa,CAAGxW,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAS,IAC7C,CAACyW,EAAmBC,EAAqB,CAAG1W,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAM,CAAC,GAG3D2W,EAAc,CAClBhW,KAAM,CAAE+T,WAAY,MAAO,EAC3B9T,MAAOR,EACPS,KAAM,EACNC,MAAO,CAAE8V,cAAe,iBAAkB,CAC5C,EAEM7V,EAAU,CACd,CACEC,KAAM9C,EAAE,kDACR+C,SAAU,WACVC,KAAOI,GAAWA,EAAEuT,QAAQ,EAE9B,CACE7T,KAAM9C,EAAE,+CACR+C,SAAU,QACVC,KAAOI,GAAWA,EAAEuV,KAAK,EAE3B,CACE7V,KAAM9C,EAAE,gDACR+C,SAAU,GACVC,KAAM,GACJ,WAACC,MAAAA,WACC,UAACiC,EAAAA,CAAMA,CAAAA,CACLC,QAAQ,UACRiB,KAAK,KACL7C,QAAS,IAAMC,EAAWJ,EAAG,oBAE5BpD,EAAE,iDACI,OAET,UAACkF,EAAAA,CAAMA,CAAAA,CACLC,QAAQ,YACRiB,KAAK,KACL7C,QAAS,IAAMC,EAAWJ,EAAG,mBAE5BpD,EAAE,oDAIX,EACD,CAEK4Y,EAAe,UAEnB7W,GAAW,GACX,IAAM2B,EAAW,MAAMC,EAAAA,CAAUA,CAACC,GAAG,CAAC,SAAU6U,GAC5C/U,GAAYA,EAASG,IAAI,EAAE,CAC7BhC,EAAe6B,EAASG,IAAI,EAC5B5B,EAAayB,EAASK,UAAU,EAChChC,GAAW,GAEf,EAOMiC,EAAsB,MAAOC,EAAoBtB,KACrD8V,EAAY/V,KAAK,CAAGuB,EACpBwU,EAAY9V,IAAI,CAAGA,EACnBZ,GAAW,GACX,IAAM2B,EAAW,MAAMC,EAAAA,CAAUA,CAACC,GAAG,CAAC,SAAU6U,GAC5C/U,GAAYA,EAASG,IAAI,EAAIH,EAASG,IAAI,CAACC,MAAM,CAAG,GAAG,CACzDjC,EAAe6B,EAASG,IAAI,EAC5B1B,EAAW8B,GACXlC,GAAW,GAEf,EAEA0C,CAAAA,EAAAA,EAAAA,SAAAA,CAASA,CAAC,KACRmU,GACF,EAAG,EAAE,EAEL,IAAMpV,EAAa,MAAOJ,EAAQyV,KAChCxW,GAAS,GACTiW,EAAaO,GACTzV,GAAKA,EAAE5D,GAAG,EAAE,EAEO,CAAE,GAAG4D,CAAC,CAAEsV,cADA,CACeI,WAD1BD,EAAuB,WAAY,UACC,EAG1D,EAEM1U,EAAe,UAEnB,GAA2C,YAAY,CAAnDoU,EAAkB,aAAgB,CAEpC,CAFmB,KAEb5U,EAAAA,CAAUA,CAACS,MAAM,CAAC,UAAmC,OAAzBmU,EAAkB,GAAM,GAC1DK,IACAvU,EAAAA,EAAKA,CAF8C,KAExC,CAACrE,EAAE,mDACdwY,EAAqB,CAAC,GACtBnW,EAAS,QAEJ,CACL,IAAM0W,EAAc,MAAMpV,EAAAA,CAAUA,CAAC0E,KAAK,CACxC,UAAmC,OAAzBkQ,EAAkB,GAAM,EAClCA,GAEF,GAAIQ,GAAsC,CAHb,KAGVA,EAAYF,MAAM,CAAU,YAC7CxU,EAAAA,EAAKA,CAACE,KAAK,CACTwU,EAAYrV,QAAQ,EAAIqV,EAAYrV,QAAQ,CAACsV,OAAO,CAChDD,EAAYrV,QAAQ,CAACsV,OAAO,CAC5BhZ,EAAE,8DAIR4Y,IACAvU,EAAAA,EAAKA,CAACC,OAAO,CAACtE,EAAE,oDAChBwY,EAAqB,CAAC,GACtBnW,GAAS,EAEb,CACF,EAEMmC,EAAY,IAAMnC,GAAS,GAEjC,MACE,WAACY,MAAAA,WACC,WAACyB,EAAAA,CAAKA,CAAAA,CAACC,KAAMvC,EAAawC,OAAQJ,YAChC,UAACE,EAAAA,CAAKA,CAACG,MAAM,EAACC,WAAW,aACvB,WAACJ,EAAAA,CAAKA,CAACK,KAAK,YACTsT,EAAUY,MAAM,CAAC,GAAGC,WAAW,GAAKb,EAAUc,KAAK,CAAC,GAAG,IAAEnZ,EAAE,mDAGhE,WAAC0E,EAAAA,CAAKA,CAACM,IAAI,YAAEhF,EAAE,0DAA0D,IAAEqY,EAAU,IAAErY,EAAE,sDACzF,WAAC0E,EAAAA,CAAKA,CAACO,MAAM,YACX,UAACC,EAAAA,CAAMA,CAAAA,CAACC,QAAQ,YAAY5B,QAASiB,WAClCxE,EAAE,kDAEL,UAACkF,EAAAA,CAAMA,CAAAA,CAACC,QAAQ,UAAU5B,QAASY,WAChCnE,EAAE,qDAKT,UAACoF,EAAAA,CAAQA,CAAAA,CACPvC,QAASA,EACTgB,KAAMjC,EACNI,UAAWA,EACXqD,UAAW,GACXrB,oBAAqBA,EACrBsB,iBA1FmB,CA0FDA,GAzFtBmT,EAAY/V,KAAK,CAAGR,EACpBuW,EAAY9V,IAAI,CAAGA,EACnBiW,GACF,MA0FF,8MClHA,MA1C2B,QAmCnB9S,EAAAA,EAlCN,GAAM,CAAE9F,GAAC,CAAE,CAAGC,CAAAA,EAAAA,EAAAA,EAAAA,CAAcA,CAAC,UACvBmZ,EAAyB,IAE3B,EAsC4B,CAtC5B,OAACnW,MAAAA,UACC,WAAC/C,EAAAA,CAASA,CAAAA,CAAC+F,MAAO,CAAEC,UAAW,QAAS,EAAG/F,KAAK,IAACC,UAAU,gBACzD,UAACC,EAAAA,CAAGA,CAAAA,UACF,UAACC,EAAAA,CAAGA,CAAAA,CAACC,GAAI,YACP,UAAC4F,EAAAA,CAAWA,CAAAA,CAAC1G,MAAOO,EAAE,kDAG1B,UAACK,EAAAA,CAAGA,CAAAA,UACF,UAACC,EAAAA,CAAGA,CAAAA,CAACC,GAAI,YACP,UAAC2C,IAAIA,CACHC,KAAK,6BACLnC,GAAG,OAFAkC,wCAIH,UAACgC,EAAAA,CAAMA,CAAAA,CAACC,QAAQ,YAAYiB,KAAK,cAC9BpG,EAAE,uDAKX,UAACK,EAAAA,CAAGA,CAAAA,CAACD,UAAU,gBACb,UAACE,EAAAA,CAAGA,CAAAA,CAACC,GAAI,YACP,UAACoB,EAAAA,OAAkBA,CAAAA,CAAAA,YAQzB0X,EAAuBC,CAAAA,EAAAA,EAAAA,mBAAAA,CAAmBA,CAAC,IAAM,UAACF,EAAAA,CAAAA,IAClDtT,EAAYS,CAAAA,EAAAA,EAAAA,EAAAA,CAAWA,CAAET,GAAUA,SACzC,IAAI,GAAEA,GAAAA,OAAAA,EAAAA,EAAOU,IAAPV,OAAOU,GAAPV,OAAAA,EAAAA,EAAoByT,cAAAA,EAApBzT,KAAAA,EAAAA,CAAoC,CAAC,GAArCA,UAAkD,EAItD,UAACuT,EAAAA,CAAAA,GAHM,UAAC3S,EAAAA,OAAeA,CAAAA,CAAAA,EAK3B,0JC8HA,MAxKA,SAAS2G,CAA4B,EACnC,GAAM,CAACzL,EAAWC,EAAe,CAAGC,CAAAA,EAAAA,EAAAA,EAuKvBuL,MAvKuBvL,CAAQA,CAAQ,EAAE,EAChD,EAAGC,EAAW,CAAGD,CAsKO,EAtKPA,EAAAA,QAAAA,CAAQA,EAAU,GACnC,CAACE,EAAWC,EAAa,CAAGH,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAS,GAC7C,CAACI,EAASC,EAAW,CAAGL,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAS,IACzC,CAACM,EAAaC,EAAS,CAAGP,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,EAAU,GAC5C,CAACuW,EAAWC,EAAa,CAAGxW,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAS,IAC7C,CAACyW,EAAmBC,EAAqB,CAAG1W,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAM,CAAC,GAC3D,GAAE9B,CAAC,CAAE,CAAGC,CAAAA,EAAAA,EAAAA,EAAAA,CAAcA,CAAC,UAIvBuZ,EAAa,CACjB/W,KAAM,CAAE+T,WAAY,MAAO,EAC3B9T,MAAOR,EACPS,KAAM,EACNC,MAAO,CAAEiW,OAAQ,iBAAkB,CACrC,EAEMhW,EAAU,CACd,CACEC,KAAM9C,EAAE,SACR+C,SAAU,QACVC,KAAM,GAAYI,EAAE3D,KACtB,EACA,CACEqD,KAAM9C,EAAE,SACR+C,SAAU,QACVC,KAAM,GAAYI,EAAEuV,KAAK,EAE3B,CACE7V,KAAO,cACPC,SAAU,eACVC,KAAM,GAAYI,EAAEqW,YACtB,EACA,CACE3W,KAAM9C,EAAE,UACR+C,SAAU,GACVC,KAAM,GACJ,WAACC,MAAAA,WACC,UAACiC,EAAAA,CAAMA,CAAAA,CACLC,QAAQ,UACRiB,KAAK,KACL7C,QAAS,IAAMmW,EAAWtW,EAAG,oBAE5BpD,EAAE,oBACI,OAET,UAACkF,EAAAA,CAAMA,CAAAA,CACLC,QAAQ,YACRiB,KAAK,KACL7C,QAAS,IAAMmW,EAAWtW,EAAG,mBAE5BpD,EAAE,oBAIX,EACD,CAEK2Z,EAAc,UAClB5X,EAAW,IACX,IAAM2B,EAAW,MAAMC,EAAAA,CAAUA,CAACC,GAAG,CAAC,eAAgB4V,GAClD9V,GAAYA,EAASG,IAAI,EAAE,CAC7BhC,EAAe6B,EAASG,IAAI,EAC5B5B,EAAayB,EAASK,UAAU,EAChChC,GAAW,GAEf,EAOMiC,EAAsB,MAAOC,EAAoBtB,KACrD6W,EAAW9W,KAAK,CAAGuB,EACnBuV,EAAW7W,IAAI,CAAGA,EAClBZ,GAAW,GACX,IAAM2B,EAAW,MAAMC,EAAAA,CAAUA,CAACC,GAAG,CAAC,eAAgB4V,GAClD9V,GAAYA,EAASG,IAAI,EAAIH,EAASG,IAAI,CAACC,MAAM,CAAG,GAAG,CACzDjC,EAAe6B,EAASG,IAAI,EAC5B1B,EAAW8B,GACXlC,EAAW,IAEf,EAEA0C,CAAAA,EAAAA,EAAAA,SAAAA,CAASA,CAAC,KACRkV,GACF,EAAG,EAAE,EAEL,IAAMD,EAAa,MAAOtW,EAAQyV,KAChCxW,GAAS,GACTiW,EAAaO,GACTzV,GAAKA,EAAE5D,GAAG,EAAE,EAEO,CAAE,GAAG4D,CAAC,CAAEyV,OADA,CACQC,WADnBD,EAAuB,WAAa,UACP,EAEnD,EAEM1U,EAAe,UAEnB,GAAoC,YAAW,CAA3CoU,EAAkB,MAAS,CAC7B,MAAM5U,EADa,CACHA,CAACS,MAAM,CAAC,gBAAyC,OAAzBmU,EAAkB,GAAM,GAChEoB,IACAtV,EAAAA,EAAKA,CAACE,KAAK,CAACvE,EAAE,mBACdwY,EAAqB,CAAC,GACtBnW,EAAS,QAEL,CACJ,IAAM0W,EAAc,MAAMpV,EAAAA,CAAUA,CAAC0E,KAAK,CACxC,gBAAyC,OAAzBkQ,EAAkB,GAAM,EACxCA,GAEF,GAAIQ,GAAeA,CAHgB,OAGJF,MAAM,CAAU,YAC7CxU,EAAAA,EAAKA,CAACE,KAAK,CACTwU,EAAYrV,QAAQ,EAAIqV,EAAYrV,QAAQ,CAACsV,OAAO,CAChDD,EAAYrV,QAAQ,CAACsV,OAAO,CAC5BhZ,EAAE,8DAIR2Z,IACAtV,EAAAA,EAAKA,CAACC,OAAO,CAACtE,EAAE,kBAChBwY,EAAqB,CAAC,GACtBnW,GAAS,EAEb,CACAsX,IACAnB,EAAqB,CAAC,GACtBnW,GAAS,EACX,EAEMmC,EAAY,IAAMnC,GAAS,GAEjC,MACE,WAACY,MAAAA,WACC,WAACyB,EAAAA,CAAKA,CAAAA,CAACC,KAAMvC,EAAawC,OAAQJ,YAChC,UAACE,EAAAA,CAAKA,CAACG,MAAM,EAACC,WAAW,aACvB,WAACJ,EAAAA,CAAKA,CAACK,KAAK,YACTsT,EAAUY,MAAM,CAAC,GAAGC,WAAW,GAAKb,EAAUc,KAAK,CAAC,GAAG,IAAEnZ,EAAE,qBAGhE,WAAC0E,EAAAA,CAAKA,CAACM,IAAI,YACVhF,EAAE,0DAA0D,KAAGqY,EAAU,IAAErY,EAAE,wBAE9E,WAAC0E,EAAAA,CAAKA,CAACO,MAAM,YACX,UAACC,EAAAA,CAAMA,CAAAA,CAACC,QAAQ,YAAY5B,QAASiB,WACpCxE,EAAE,YAEH,UAACkF,EAAAA,CAAMA,CAAAA,CAACC,QAAQ,UAAU5B,QAASY,WAClCnE,EAAE,eAKP,UAACoF,EAAAA,CAAQA,CAAAA,CACPvC,QAASA,EACTgB,KAAMjC,EACNI,UAAWA,EACXqD,WAAW,EACXrB,oBAAqBA,EACrBsB,iBA7FmB,CA6FDA,GA5FtBkU,EAAW9W,KAAK,CAAGR,EACnBsX,EAAW7W,IAAI,CAAGA,EAClBgX,GACF,MA6FF,yLCxBA,MA5IoB,IAChB,GAAM,CAAE3Z,CAAC,CAAE,CAAGC,CAAAA,EAAAA,EAAAA,EAAAA,CAAcA,CAAC,QA2IlB2Z,EA1IL,CAAChY,EAAWC,EAAe,CAAGC,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAC,EAAE,EACzC,EAAGC,EAAW,CAAGD,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,EAAC,GAC1B,CAACE,EAAWC,EAAa,CAAGH,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAC,GACrC,CAACI,EAASC,EAAW,CAAGL,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAC,IACjC,CAACM,EAAaC,EAAS,CAAGP,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,EAAC,GACnC,CAAC+X,EAAqBC,EAAgB,CAAGhY,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAC,CAAC,GACnD,CAACiY,EAAWC,EAAa,CAAQlY,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAC,IAE1CqT,EAAe,CACjB1S,KAAM,CAAEhD,MAAO,KAAM,EACrBiD,MAAOR,EACPS,KAAM,EACNC,MAAO,CAAC,CACZ,EAEMC,EAAU,CACZ,CACIC,KAAM9C,EAAE,+BACR+C,SAAU,GAAcmB,EAAIzE,KAAK,CACjCkG,UAAU,CACd,EACA,CACI7C,KAAM9C,EAAE,gCACR+C,SAAU,QAAcmB,QAAAA,CAAAA,OAAAA,EAAAA,EAAI6C,OAAO,EAAX7C,KAAAA,EAAAA,EAAazE,GAAbyE,EAAazE,GAAS,IAC9CkG,SAAU,GACV3C,KAAM,GAAaI,EAAE2D,OAAO,EAAI3D,EAAE2D,OAAO,CAACtH,KAAK,CAAG2D,EAAE2D,OAAO,CAACtH,KAAK,CAAG,EACxE,EACA,CACIqD,KAAM9C,EAAE,+BACR+C,SAAU,GAAcmB,EAAI1E,GAAG,CAC/BmG,UAAU,EACV3C,KAAM,GACF,WAACC,MAAAA,WACG,UAACC,IAAIA,CAACC,KAAK,6BAA6BnC,GAAI,OAAvCkC,uBAA2E,OAANE,EAAE5D,GAAG,WAE3E,UAAC6D,IAAAA,CAAEjD,UAAU,uBAEV,OAEP,UAACkD,IAAAA,CAAEC,QAAS,IAAMC,EAAWJ,YACzB,UAACC,IAAAA,CAAEjD,UAAU,4BACZ,MAGjB,EACH,CAEKgV,EAAgB,UAClBrT,GAAW,GACX,IAAM2B,EAAW,MAAMC,EAAAA,CAAUA,CAACC,GAAG,CAAC,UAAWuR,GAC7CzR,GAAYA,EAASG,IAAI,EAAE,CAC3BhC,EAAe6B,EAASG,IAAI,EAC5B5B,EAAayB,EAASK,UAAU,EAChChC,EAAW,IAEnB,EASMiC,EAAsB,MAAOC,EAAiBtB,KAChDwS,EAAazS,KAAK,CAAGuB,EACrBkR,EAAaxS,IAAI,CAAGA,EACpBoX,IAAc5E,EAAavS,KAAK,CAAG,CAAEmE,QAASgT,EAAUjZ,KAAK,CAAC,EAC9DiB,EAAW,IACX,IAAM2B,EAAW,MAAMC,EAAAA,CAAUA,CAACC,GAAG,CAAC,UAAWuR,GAC7CzR,GAAYA,EAASG,IAAI,EAAIH,EAASG,IAAI,CAACC,MAAM,CAAG,GAAG,CACvDjC,EAAe6B,EAASG,IAAI,EAC5B1B,EAAW8B,GACXlC,EAAW,IAEnB,EAEMyB,EAAa,MAAOU,IACtB4V,EAAgB5V,EAAI1E,GAAG,EACvB6C,GAAS,EACb,EAEM8B,EAAe,UACjB,GAAI,CACA,MAAMR,EAAAA,CAAUA,CAACS,MAAM,CAAC,WAA+B,OAApByV,IACnCzE,IACA/S,GAAS,GACTgC,EAAAA,EAAKA,CAACC,OAAO,CAACtE,EAAE,wDACpB,CAAE,MAAOuE,EAAO,CACZF,EAAAA,EAAKA,CAACE,KAAK,CAACvE,EAAE,kDAClB,CACJ,EAEMwE,EAAY,IAAMnC,GAAS,GAgBjC,MATAoC,CAAAA,EAAAA,EAAAA,SAAAA,CAASA,CAAC,KACN2Q,GACJ,EAAG,EAAE,EAEL3Q,CAAAA,EAAAA,EAAAA,SAAAA,CAASA,CAAC,KACNsV,IAAc5E,EAAavS,KAAK,CAAG,CAArBuS,QAAgC4E,EAAUjZ,KAAK,CAAC,EAC9DsU,GACJ,EAAG,CAAC2E,EAAU,EAGV,WAAC9W,MAAAA,CAAI7C,UAAU,0BACX,WAACsE,EAAAA,CAAKA,CAAAA,CAACC,KAAMvC,EAAawC,OAAQJ,YAC9B,UAACE,EAAAA,CAAKA,CAACG,MAAM,EAACC,WAAW,aACrB,UAACJ,EAAAA,CAAKA,CAACK,KAAK,WAAE/E,EAAE,yCAEpB,UAAC0E,EAAAA,CAAKA,CAACM,IAAI,WAAEhF,EAAE,4DACf,WAAC0E,EAAAA,CAAKA,CAACO,MAAM,YACT,UAACC,EAAAA,CAAMA,CAAAA,CAACC,QAAQ,YAAY5B,QAASiB,WAChCxE,EAAE,iCAEP,UAACkF,EAAAA,CAAMA,CAAAA,CAACC,QAAQ,UAAU5B,QAASY,WAC9BnE,EAAE,oCAIf,UAACia,EAAAA,OAAiBA,CAAAA,CAACC,eA7BL,CA6BqBC,GA5BvCH,EAAajT,EACjB,EA2B0DjG,MAAOiZ,IACzD,UAAC3U,EAAAA,CAAQA,CAAAA,CACLvC,QAASA,EACTgB,KAAMjC,EACNI,UAAWA,EACXqD,WAAW,EACXrB,oBAAqBA,EACrBsB,iBA3Ec3C,CA2EI2C,GA1E1B6P,EAAazS,KAAK,CAAGR,EACrBiT,EAAaxS,IAAI,CAAGA,EACpBoX,IAAc5E,EAAavS,KAAK,CAAG,CAAEmE,QAASgT,EAAUjZ,KAAK,CAAC,EAC9DsU,GACJ,MA0EJ,qOCzBA,MA7GyB,IAMvB,GAAM,CAAC5N,EAAYC,EAAc,CAAG3F,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAMsY,CAHhD3a,MAAO,EACT,GAGQkI,EAAWL,EAAMM,EAsGK,IAtGC,EAAwB,cAApBN,EAAMM,MAAM,CAAC,EAAE,EAAoBN,EAAMM,MAAM,CAAC,EAAE,CAC/E,CAAE5H,GAAC,CAAE,CAAGC,CAAAA,EAAAA,EAAAA,EAAAA,CAAcA,CAAC,UAGvB4H,EAAUC,CAAAA,EAAAA,EAAAA,MAAAA,CAAMA,CAAC,MAiBjBC,EAAe,MAAOC,EAAYgG,SAQlCtK,EAPAsE,GAAOA,EAAME,cAAc,GAG/B,IAAMC,EAAM,CACV1I,MAAO4a,CAFUrM,GAAUxG,CAAAA,EAET/H,KAAK,CAAC2I,IAAI,EAC9B,CAQI1E,EAJFA,EADEiE,EACS,MAAMhE,EAAAA,CAAUA,CAAC0E,KAAK,CAAC,UAA0B,OAAhBf,EAAMM,MAAM,CAAC,EAAE,EAAIO,GAEpD,MAAMxE,EAAAA,CAAUA,CAAC2E,IAAI,CAAC,SAAUH,KAE7BzE,EAASlE,GAAG,EAC1B6E,EAAAA,EAAKA,CAACC,OAAO,CAACtE,EAAE,4BAChBuI,IAAAA,IAAW,CAAC,wBAEZlE,EAAAA,EAAKA,CAACE,KAAK,CAACb,EAEhB,EAiBA,MAfAe,CAAAA,EAAAA,EAAAA,SAAAA,CAASA,CAAC,KACR,IAAM6V,EAAa,CACjB1X,MAAO,CAAC,EACRH,KAAM,CAAEhD,MAAO,KAAM,EACrBiD,MAAO,GACT,EACIiF,GACkB,OADR,IAEV,IAAMjE,EAAW,MAAMC,EAAAA,CAAUA,CAACC,GAAG,CAAC,UAA0B,OAAhB0D,EAAMM,MAAM,CAAC,EAAE,EAAI0S,GACnE7S,EAAc,GAAqB,EAAE,GAAGkB,CAAS,CAAE,EAAhB,CAAmBjF,CAAQ,CAAC,GACjE,GAGJ,EAAG,EAAE,EAGH,UAACT,MAAAA,UACC,UAAC/C,EAAAA,CAASA,CAAAA,CAACE,UAAU,WAAWD,KAAK,aACnC,UAACyI,EAAAA,CAAIA,CAAAA,CAAC3C,MAAO,CAAE4C,UAAW,MAAOC,UAAW,kEAAmE,WAC7G,UAACC,EAAAA,CAAqBA,CAAAA,CAACC,SAAUjB,EAAckB,IAAKpB,EAASqB,cAAe1B,EAAY2B,mBAAoB,YAC1G,WAACP,EAAAA,CAAIA,CAAC5D,IAAI,YACR,UAAC3E,EAAAA,CAAGA,CAAAA,UACF,UAACC,EAAAA,CAAGA,CAAAA,UACF,UAACsI,EAAAA,CAAIA,CAAC7D,KAAK,WAAE/E,EAAE,cAGnB,UAACoJ,KAAAA,CAAAA,GACD,UAAC/I,EAAAA,CAAGA,CAAAA,UACF,UAACC,EAAAA,CAAGA,CAAAA,CAACE,EAAE,IAACY,GAAI,EAAGD,GAAI,YACjB,WAACkI,EAAAA,CAAIA,CAACC,KAAK,YACT,UAACD,EAAAA,CAAIA,CAACE,KAAK,EAACnJ,UAAU,0BAAkBJ,EAAE,UAC1C,UAACwJ,EAAAA,EAASA,CAAAA,CACR1G,KAAK,QACL2G,GAAG,QACHC,QAAQ,IAAC5I,MAAO0G,EAAW/H,KAAK,CAChCkK,UAAY,GAAkD,KAA/BC,OAAO9I,GAAS,IAAIsH,IAAI,GACtDmS,eAAiBva,EAAE,aACnB6J,aAAc,CACbF,UAAW3J,EAAE,mBAAmB,EAClCe,SAvEC,CAuES+I,GAtE5B,GAAIzI,EAAE0I,MAAM,CAAE,CACZ,GAAM,MAAEjH,CAAI,OAAEhC,CAAK,CAAE,CAAGO,EAAE0I,MAAM,CAChCtC,EAAc,GAAqB,EACjC,GAAGkB,CAAS,CACZ,CAAC7F,CAFgC,CAE3B,CAAEhC,EACV,EACF,CACF,WAoEY,WAACT,EAAAA,CAAGA,CAAAA,CAACD,UAAU,iBACb,UAACE,EAAAA,CAAGA,CAAAA,CAACC,EAAE,IAACa,GAAG,aACT,UAAC8D,EAAAA,CAAMA,CAAAA,CAACvE,KAAK,SAASwE,QAAQ,mBAAWnF,EAAE,cAE7C,UAACM,EAAAA,CAAGA,CAAAA,UACF,UAAC4E,EAAAA,CAAMA,CAAAA,CAAC3B,QAtFH,CAsFYyG,IApF/BC,OAAOC,QAAQ,CAAC,EAAG,EACrB,EAmF+C/E,QAAQ,gBAAQnF,EAAE,0BASnE,+MCvEA,MAxCiCqS,QAiCzBvM,EAAAA,EAhCN,GAAM,CAAE9F,GAAC,CAAE,CAAGC,CAAAA,EAAAA,EAAAA,EAAAA,CAAcA,CAAC,UACvBua,EAA8B,IAEhC,OAoCiC,EApCjC,EAACta,EAAAA,CAASA,CAAAA,CAAC+F,MAAO,CAAEC,UAAW,QAAS,EAAG/F,KAAK,IAACC,UAAU,gBACzD,UAACC,EAAAA,CAAGA,CAAAA,UACF,UAACC,EAAAA,CAAGA,CAAAA,CAACC,GAAI,YACP,UAAC4F,EAAAA,CAAWA,CAAAA,CAAC1G,MAAQO,EAAE,gEAG3B,UAACK,EAAAA,CAAGA,CAAAA,UACF,UAACC,EAAAA,CAAGA,CAAAA,CAACC,GAAI,YACP,UAAC2C,IAAIA,CACHC,KAAK,6BACLnC,GAAG,OAFAkC,8CAIH,UAACgC,EAAAA,CAAMA,CAAAA,CAACC,QAAQ,YAAYiB,KAAK,cAChCpG,EAAE,oEAKT,UAACK,EAAAA,CAAGA,CAAAA,CAACD,UAAU,gBACb,UAACE,EAAAA,CAAGA,CAAAA,CAACC,GAAI,YACP,UAACka,EAAAA,OAAuBA,CAAAA,CAAAA,UAO5BC,EAA8BC,CAAAA,EAAAA,EAAAA,0BAAAA,CAA0BA,CAAC,IAAM,UAACH,EAAAA,CAAAA,IAChE1U,EAAYS,CAAAA,EAAAA,EAAAA,EAAAA,CAAWA,CAAC,GAAWT,SACzC,IAAI,GAAEA,GAAAA,OAAAA,EAAAA,EAAOU,IAAPV,OAAOU,GAAPV,OAAAA,EAAAA,EAAAA,mBAAuC,EAAvCA,KAAAA,EAAAA,CAAyC,CAAC,GAA1CA,UAAuD,EAI3D,CAJ8D,EAI9D,OAAC4U,EAAAA,CAAAA,GAHM,UAAChU,EAAAA,OAAeA,CAAAA,CAAAA,EAK3B,yPC6HA,MAhKsB,IAClB,IAAMkU,EAAoB,CACtBnb,MAAO,GACPob,MAAO,EACX,EAEM,CAACrT,EAAYC,EAAc,CAAG3F,CAAAA,EAAAA,EAAAA,EA0JX,MA1JWA,CAAQA,CAAY8Y,GAElDjT,EAAWL,EAAMM,MAAM,EAAwB,mBAApBN,EAAMM,MAAM,CAAC,EAAE,EAAyBN,EAAMM,MAAM,CAAC,EAAE,CAClF,GAAE5H,CAAC,CAAE,CAAGC,CAAAA,EAAAA,EAAAA,EAAAA,CAAcA,CAAC,UAEvB4H,EAAUC,CAAAA,EAAAA,EAAAA,MAAAA,CAAMA,CAAC,MAQjBgC,EAAgBzI,IAClB,GAAIA,EAAE0I,MAAM,CAAE,CACV,GAAM,MAAEjH,CAAI,OAAEhC,CAAK,CAAE,CAAGO,EAAE0I,MAAM,CAChCtC,EAAc,GAAgB,EAC1B,GAAGkB,CAAS,CACZ,CAAC7F,CAFyB,CAEpB,CAAEhC,CACZ,GACJ,CACJ,EAEMga,EAAc,IACZ,IAAOC,EAAO,IACXA,EAAK9B,MAAM,CAAC,GAAGC,WAAW,GAAK6B,EAAK5B,KAAK,CAAC,IAG/CpR,EAAe,MAAOC,EAAYgG,SAShCtK,EACAuE,EATJD,EAAME,cAAc,GAEpB,IAAMmS,EAAarM,GAAUxG,EACvBW,EAAM,CACR1I,MAAO4a,EAAW5a,KAAK,CAAC2I,IAAI,GAC5ByS,MAAOG,SAASX,EAAWQ,KAAK,CACpC,EAIIlT,GACAM,EAAW,KADD,kCAEVvE,EAAW,MAAMC,EAAAA,CAAUA,CAAC0E,KAAK,CAAC,cAA8B,OAAhBf,EAAMM,MAAM,CAAC,EAAE,EAAIO,KAEnEF,EAAW,iCACXvE,EAAW,MAAMC,EAAAA,CAAUA,CAAC2E,IAAI,CAAC,aAAcH,IAE/CzE,GAAYA,EAASlE,GAAG,EACxB6E,EAAAA,EAAKA,CAACC,OAAO,CAACtE,EAAEiI,IAChBM,IAAAA,IAAW,CAAC,6BAER7E,OAAAA,EAAAA,KAAAA,EAAAA,EAAU8E,SAAAA,CAAV9E,GAAwB,KACxBW,EAD+B,EAC1BA,CAACE,KAAK,CAACvE,EAAE,yBAEdqE,EAAAA,EAAKA,CAACE,KAAK,CAACuW,EAAYpX,GAGpC,EAiBA,MAfAe,CAAAA,EAAAA,EAAAA,SAAAA,CAASA,CAAC,KACN,IAAMwW,EAAkB,CACpBrY,MAAO,CAAC,EACRH,KAAM,CAAEhD,MAAO,KAAM,EACrBiD,MAAO,GACX,EACIiF,GAKAuT,CAJyB,MADf,IAEN,IAAMxX,EAAsB,MAAMC,EAAAA,CAAUA,CAACC,GAAG,CAAC,cAA8B,OAAhB0D,EAAMM,MAAM,CAAC,EAAE,EAAIqT,GAClFxT,EAAc,GAAgB,EAAE,GAAGkB,CAAS,CAAE,EAAhB,CAAmBjF,CAAQ,CAAC,GAC9D,GAGR,EAAG,EAAE,EAGD,UAACT,MAAAA,UACG,UAAC/C,EAAAA,CAASA,CAAAA,CAACE,UAAU,WAAWD,KAAK,aACjC,UAACyI,EAAAA,CAAIA,CAAAA,CACD3C,MAAO,CACH4C,UAAW,MACXC,UAAW,kEACf,WAEA,UAACC,EAAAA,CAAqBA,CAAAA,CAACC,SAAUjB,EAAckB,IAAKpB,EAASqB,cAAe1B,EAAY2B,oBAAoB,WACxG,WAACP,EAAAA,CAAIA,CAAC5D,IAAI,YACN,UAAC3E,EAAAA,CAAGA,CAAAA,UACA,UAACC,EAAAA,CAAGA,CAAAA,UACA,UAACsI,EAAAA,CAAIA,CAAC7D,KAAK,WAAE/E,EAAE,0CAGvB,UAACoJ,KAAAA,CAAAA,GACD,WAAC/I,EAAAA,CAAGA,CAAAA,WACA,UAACC,EAAAA,CAAGA,CAAAA,CAACE,EAAE,IAACY,GAAI,EAAGD,GAAI,YACf,WAACkI,EAAAA,CAAIA,CAACC,KAAK,YACP,UAACD,EAAAA,CAAIA,CAACE,KAAK,EAACnJ,UAAU,0BACjBJ,EAAE,0CAEP,UAACwJ,EAAAA,EAASA,CAAAA,CACN1G,KAAK,QACL2G,GAAG,QACHC,QAAQ,IACR5I,MAAO0G,EAAW/H,KAAK,CACvBkK,UAAW,GAAoC,KAAjB7I,EAAMsH,IAAI,GACxCyB,aAAc,CACVF,UAAW3J,EAAE,6BACjB,EACAe,SAAU+I,SAItB,UAACxJ,EAAAA,CAAGA,CAAAA,CAACE,EAAE,IAACY,GAAI,EAAGD,GAAI,YACf,WAACkI,EAAAA,CAAIA,CAACC,KAAK,YACP,UAACD,EAAAA,CAAIA,CAACE,KAAK,EAACnJ,UAAU,0BACjBJ,EAAE,2CAEP,UAACwJ,EAAAA,EAASA,CAAAA,CACN2R,IAAI,IACJxa,KAAK,SACLmC,KAAK,QACL2G,GAAG,QACHC,QAAQ,IACR5I,MAAO0G,EAAWqT,KAAK,CACvBhR,aAAc,CACVF,UAAW3J,EAAE,gCACbmb,IAAKnb,EAAE,mCACP0J,SAAU1J,EAAE,+BAChB,EACAe,SAAU+I,YAK1B,UAACzJ,EAAAA,CAAGA,CAAAA,CAACD,UAAU,gBACX,WAACE,EAAAA,CAAGA,CAAAA,WACA,UAAC4E,EAAAA,CAAMA,CAAAA,CAAC9E,UAAU,OAAOO,KAAK,SAASwE,QAAQ,mBAC1CnF,EAAE,YAEP,UAACkF,EAAAA,CAAMA,CAAAA,CAAC9E,UAAU,OAAOmD,QAjIpC,CAiI6CyG,IAhI9DvC,EAAcmT,GAEd3Q,OAAOC,QAAQ,CAAC,EAAG,EACvB,EA6HgF/E,QAAQ,gBACnDnF,EAAE,WAEP,UAACkD,IAAIA,CACDC,KAAK,6BACLnC,GAAK,OAFJkC,6BAID,UAACgC,EAAAA,CAAMA,CAAAA,CAACC,QAAQ,qBAAanF,EAAE,6BAUvE,kOCnHA,MAjDoB,QAiCZ8F,EAAAA,EAhCN,GAAM,CAAE9F,GAAC,CAAE,CAAGC,CAAAA,EAAAA,EAAAA,EAAAA,CAAcA,CAAC,SAgDLmb,CA/ClBC,CA+CmB,CA/CD,IAEpB,WAACnb,EAAAA,CAASA,CAAAA,CAAC+F,MAAO,CAAEC,UAAW,QAAS,EAAG/F,KAAK,IAACC,UAAU,gBACzD,UAACC,EAAAA,CAAGA,CAAAA,UACF,UAACC,EAAAA,CAAGA,CAAAA,CAACC,GAAI,YACP,UAAC4F,EAAAA,CAAWA,CAAAA,CAAC1G,MAAOO,EAAE,sBAG1B,UAACK,EAAAA,CAAGA,CAAAA,UACF,UAACC,EAAAA,CAAGA,CAAAA,CAACC,GAAI,YACP,UAAC2C,IAAIA,CACHC,KAAK,6BACLnC,GAAG,OAFAkC,iCAIH,UAACgC,EAAAA,CAAMA,CAAAA,CAACC,QAAQ,YAAYiB,KAAK,cAC9BpG,EAAE,qBAKX,UAACK,EAAAA,CAAGA,CAAAA,CAACD,UAAU,gBACb,UAACE,EAAAA,CAAGA,CAAAA,CAACC,GAAI,YACP,UAACqK,EAAAA,OAAWA,CAAAA,CAAAA,UAOhB0Q,EAAiBC,CAAAA,EAAAA,EAAAA,aAAAA,CAAaA,CAAC,IAAM,UAACF,EAAAA,CAAAA,IACtCvV,EAAYS,CAAAA,EAAAA,EAAAA,EAAAA,CAAWA,CAAC,GAAWT,SACzC,IAAI,GAAEA,GAAAA,OAAAA,EAAAA,EAAOU,IAAPV,OAAOU,GAAPV,OAAAA,EAAAA,EAAoB0V,MAAAA,EAApB1V,KAAAA,EAAAA,CAA4B,CAAC,GAA7BA,UAA0C,EAI9C,UAACwV,EAAAA,CAAAA,GAHM,UAAC5U,EAAAA,OAAeA,CAAAA,CAAAA,EAK3B,0lBC6KA,MA9Ke,KAEb,IAAMkB,EAAc6T,CADLC,EAAAA,EAAAA,SAAAA,CAASA,EA6KXnT,CA5Kc3F,KAAK,CAACgF,CA4Kb,KA5KmB,EAAI,EAAE,CAG7C,OAAQA,CAAM,CAAC,EAAE,EACf,IAAK,cACH,MAAO,UAAC0K,EAAAA,OAAcA,CAAAA,CAAC1K,OAAQA,GAEjC,KAAK,eACD,MAAO,UAAC+M,EAAAA,OAAgBA,CAAAA,CAAC/M,OAAQA,GAErC,KAAK,uBACH,MAAO,UAACuF,EAAAA,OAAmBA,CAAAA,CAACvF,OAAQA,GAEtC,KAAK,UACH,MAAO,UAAC+T,EAAAA,OAAYA,CAAAA,CAAC/T,OAAQA,GAE/B,KAAK,iBACL,IAAK,eACH,MAAO,UAACgU,EAAAA,OAAWA,CAAAA,CAAChU,OAAQA,GAE9B,KAAK,SACH,MAAO,UAACwT,EAAAA,OAAWA,CAAAA,CAACxT,OAAQA,GAE9B,KAAK,gBACL,IAAK,cACH,MAAO,UAACiU,EAAAA,OAAUA,CAAAA,CAACjU,OAAQA,GAE7B,KAAK,cACH,MAAO,UAACyM,EAAAA,OAAeA,CAAAA,CAACzM,OAAQA,GAElC,KAAK,sBACL,IAAK,oBACH,MAAO,UAACkU,EAAAA,OAAcA,CAAAA,CAAClU,OAAQA,GAEjC,KAAK,SACH,MAAO,UAACmU,EAAAA,OAAWA,CAAAA,CAACnU,OAAQA,GAE9B,KAAK,gBACL,IAAK,cACH,MAAO,UAACmN,EAAAA,OAAUA,CAAAA,CAACnN,OAAQA,GAE7B,KAAK,cACH,MAAO,UAACuC,EAAAA,OAAeA,CAAAA,CAACvC,OAAQA,GAElC,KAAK,qBACL,IAAK,mBACH,MAAO,UAACmK,EAAAA,OAAcA,CAAAA,CAACnK,OAAQA,GAEjC,KAAK,eACH,MAAO,UAACqL,EAAAA,OAAeA,CAAAA,CAACrL,OAAQA,GAElC,KAAK,sBACL,IAAK,oBACH,MAAO,UAAC4C,EAAAA,OAAcA,CAAAA,CAAC5C,OAAQA,GAEjC,KAAK,WACH,MAAO,UAACgF,EAAAA,OAAaA,CAAAA,CAAChF,OAAQA,GAEhC,KAAK,kBACL,IAAK,gBACH,MAAO,UAACmG,EAAAA,OAAYA,CAAAA,CAACnG,OAAQA,GAE/B,KAAK,UACH,MAAO,UAACoU,EAAAA,OAAgBA,CAAAA,CAACpU,OAAQA,GAEnC,KAAK,iBACL,IAAK,eACH,MAAO,UAACqU,EAAAA,OAAeA,CAAAA,CAACrU,OAAQA,GAElC,KAAK,WACH,MAAO,UAACsU,EAAAA,OAAaA,CAAAA,CAACtU,OAAQA,GAEhC,KAAK,kBACL,IAAK,gBACH,MAAO,UAAC6M,EAAAA,OAAYA,CAAAA,CAAC7M,OAAQA,GAE/B,KAAK,mBACH,MAAO,UAAC4J,EAAAA,OAAqBA,CAAAA,CAAC5J,OAAQA,GAExC,KAAK,0BACL,IAAK,wBACH,MAAO,UAAC8F,EAAAA,OAAoBA,CAAAA,CAAC9F,OAAQA,GAEvC,KAAK,cACH,MAAO,UAACsH,EAAAA,OAAgBA,CAAAA,CAACtH,OAAQA,GAEnC,KAAK,qBACL,IAAK,mBACH,MAAO,UAACuU,EAAAA,OAAeA,CAAAA,CAACvU,OAAQA,GAElC,KAAK,kBACH,MAAO,UAACX,EAAAA,OAAoBA,CAAAA,CAACW,OAAQA,GAEvC,KAAK,yBACL,IAAK,uBACH,MAAO,UAACF,EAAAA,OAAmBA,CAAAA,CAACE,OAAQA,GAEtC,KAAK,gBACH,MAAO,UAACwU,EAAAA,OAAkBA,CAAAA,CAACxU,OAAQA,GAErC,KAAK,uBACL,IAAK,qBACH,MAAO,UAAC2N,EAAAA,OAAiBA,CAAAA,CAAC3N,OAAQA,GAEpC,KAAK,YACH,MAAO,UAACyU,EAAAA,OAAcA,CAAAA,CAACzU,OAAQA,GAEjC,KAAK,mBACL,IAAK,iBACH,MAAO,UAAC0U,EAAAA,OAAaA,CAAAA,CAAC1U,OAAQA,GAEhC,KAAK,YACH,MAAO,UAAC8L,EAAAA,OAAcA,CAAAA,CAAC9L,OAAQA,GAEjC,KAAK,mBACL,IAAK,iBACH,MAAO,UAAC2U,EAAAA,OAAaA,CAAAA,CAAC3U,OAAQA,GAEhC,KAAK,OACH,MAAO,UAACoM,EAAAA,OAASA,CAAAA,CAACpM,OAAQA,GAE5B,KAAK,cACL,IAAK,YACH,MAAO,UAAC4U,EAAAA,OAAQA,CAAAA,CAAC5U,OAAQA,GAE3B,KAAK,QACH,MAAO,UAAC6U,EAAAA,OAASA,CAAAA,CAAC7U,OAAQA,GAE5B,KAAK,cACL,IAAK,YACH,MAAO,UAAC8U,EAAAA,OAAQA,CAAAA,CAAC9U,OAAQA,GAE3B,KAAK,OACH,MAAO,UAAC+U,EAAAA,OAAgBA,CAAAA,CAAC/U,OAAQA,GAEnC,KAAK,cACH,MAAO,UAAC5B,EAAAA,OAAgBA,CAAAA,CAAC4B,OAAQA,GAEnC,KAAK,qBACL,IAAK,mBACH,MAAO,UAACqF,EAAAA,OAAeA,CAAAA,CAACrF,OAAQA,GAElC,KAAK,mBACH,MAAO,UAAC6H,EAAAA,OAAoBA,CAAAA,CAAC7H,OAAQA,GAEvC,KAAK,0BACL,IAAK,wBACH,MAAO,UAACgV,EAAAA,OAAmBA,CAAAA,CAAChV,OAAQA,GAEtC,KAAK,sBACH,MAAO,UAACiV,EAAAA,OAAuBA,CAAAA,CAACjV,OAAQA,GAE1C,KAAK,6BACL,IAAK,2BACH,MAAO,UAACkV,EAAAA,OAAsBA,CAAAA,CAAClV,OAAQA,GAEzC,KAAK,UACH,MAAO,UAACmV,EAAAA,OAAOA,CAAAA,CAACnV,OAAQA,GAE1B,SACE,OAAO,IACX,CACF,8KC7EA,MA9HuB,IACnB,GAAM,CAAChG,EAAWC,EAAe,CAAGC,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAC,EAAE,EACzC,EAAGC,EAAW,CAAGD,CAAAA,EAAAA,EAAAA,EA4HG,MA5HHA,CAAQA,EAAC,GAC1B,CAACE,EAAWC,EAAa,CAAGH,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAC,GACrC,CAACI,EAASC,EAAW,CAAGL,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAC,IACjC,CAACM,EAAaC,EAAS,CAAGP,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,EAAC,GACnC,CAACkb,EAAiBC,EAAmB,CAAGnb,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAC,CAAC,GAClD,GAAE9B,CAAC,CAAE,CAAGC,CAAAA,EAAAA,EAAAA,EAAAA,CAAcA,CAAC,UAEvBgb,EAAkB,CACpBxY,KAAM,CAAEhD,MAAO,KAAM,EACrBiD,MAAOR,EACPS,KAAM,EACNC,MAAO,CAAC,CACZ,EAEMC,EAAU,CACZ,CACIC,KAAM9C,EAAE,SACR+C,SAAU,GAAcmB,EAAIzE,KAAK,CACjCkG,SAAU,EACd,EACA,CACI7C,KAAM9C,EAAE,gCACR+C,SAAU,GAAcmB,EAAI2W,KAAK,CACjClV,UAAU,EACV3C,KAAM,GAAYI,EAAEyX,KAAK,EAE7B,CACI/X,KAAM9C,EAAE,UACR+C,SAAU,GAAcmB,EAAI1E,GAAG,CAC/BmG,UAAU,EACV3C,KAAM,GACF,WAACC,MAAAA,WACG,UAACC,IAAIA,CAACC,KAAK,6BAA6BnC,GAAI,OAAvCkC,0BAA8E,OAANE,EAAE5D,GAAG,WAE9E,UAAC6D,IAAAA,CAAEjD,UAAU,uBAEV,OAEP,UAACkD,IAAAA,CAAEC,QAAS,IAAMC,EAAWJ,YACzB,UAACC,IAAAA,CAAEjD,UAAU,4BACZ,MAGjB,EACH,CAEK8a,EAAmB,UACrBnZ,GAAW,GACX,IAAM2B,EAAW,MAAMC,EAAAA,CAAUA,CAACC,GAAG,CAAC,aAAcqX,GAChDvX,GAAYA,EAASG,IAAI,EAAIH,EAASG,IAAI,CAACC,MAAM,CAAG,GAAG,CACvDjC,EAAe6B,EAASG,IAAI,EAC5B5B,EAAayB,EAASK,UAAU,EAChChC,GAAW,GAEnB,EAOMiC,EAAsB,MAAOC,EAAiBtB,KAChDsY,EAAgBvY,KAAK,CAAGuB,EACxBgX,EAAgBtY,IAAI,CAAGA,EACvBZ,GAAW,GACX,IAAM2B,EAAW,MAAMC,EAAAA,CAAUA,CAACC,GAAG,CAAC,aAAcqX,GAChDvX,GAAYA,EAASG,IAAI,EAAIH,EAASG,IAAI,CAACC,MAAM,CAAG,GAAG,CACvDjC,EAAe6B,EAASG,IAAI,EAC5B1B,EAAW8B,GACXlC,GAAW,GAEnB,EAEMyB,EAAa,MAAOU,IACtB+Y,EAAmB/Y,EAAI1E,GAAG,EAC1B6C,GAAS,EACb,EAEM8B,EAAe,UACjB,GAAI,CACA,MAAMR,EAAAA,CAAUA,CAACS,MAAM,CAAC,cAA8B,OAAhB4Y,IACtC9B,IACA7Y,GAAS,GACTgC,EAAAA,EAAKA,CAACC,OAAO,CAACtE,EAAE,6DACpB,CAAE,MAAOuE,EAAO,CACZF,EAAAA,EAAKA,CAACE,KAAK,CAACvE,EAAE,uDAClB,CACJ,EAEMwE,EAAY,IAAMnC,GAAS,GAMjC,MAJAoC,CAAAA,EAAAA,EAAAA,SAAAA,CAASA,CAAC,KACNyW,GACJ,EAAG,EAAE,EAGD,WAACjY,MAAAA,WACG,WAACyB,EAAAA,CAAKA,CAAAA,CAACC,KAAMvC,EAAawC,OAAQJ,YAC9B,UAACE,EAAAA,CAAKA,CAACG,MAAM,EAACC,WAAW,aACrB,UAACJ,EAAAA,CAAKA,CAACK,KAAK,WAAE/E,EAAE,8CAEpB,WAAC0E,EAAAA,CAAKA,CAACM,IAAI,YAAEhF,EAAE,8DAA8D,OAC7E,WAAC0E,EAAAA,CAAKA,CAACO,MAAM,YACT,UAACC,EAAAA,CAAMA,CAAAA,CAACC,QAAQ,YAAY5B,QAASiB,WAChCxE,EAAE,YAEP,UAACkF,EAAAA,CAAMA,CAAAA,CAACC,QAAQ,UAAU5B,QAASY,WAC9BnE,EAAE,eAKf,UAACoF,EAAAA,CAAQA,CAAAA,CACLvC,QAASA,EACTgB,KAAMjC,EACNI,UAAWA,EACXqD,WAAW,EACXrB,oBAAqBA,EACrBsB,iBA/Da,CA+DKA,GA9D1B2V,EAAgBvY,KAAK,CAAGR,EACxB+Y,EAAgBtY,IAAI,CAAGA,EACvBuY,GACJ,MA+DJ,mKCtBA,MAvGkB,IAChB,GAAM,CAACtZ,EAAWC,EAAe,CAAGC,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAC,EAAE,EACzC,EAAGC,EAAW,CAAGD,CAAAA,CAqGA,CArGAA,EAAAA,QAAAA,CAAQA,EAAC,GAC1B,CAACE,EAAWC,EAAa,CAAGH,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAC,GACrC,CAACI,EAASC,EAAW,CAAGL,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAC,IACjC,CAACM,EAAaC,EAAS,CAAGP,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAC,IACnC,CAACob,EAAYC,EAAc,CAAGrb,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAC,CAAC,GACxC,GAAE9B,CAAC,CAAE,CAAGC,CAAAA,EAAAA,EAAAA,EAAAA,CAAcA,CAAC,UAGvBqa,EAAa,CACjB,KAAQ,CAAE,MAAS,KAAM,EACzB,MAASpY,EACT,KAAQ,EACR,MAAS,CAAC,CACZ,EAEMW,EAAU,CACd,CACEC,KAAM,QACNC,SAAU,OACZ,EACA,CACED,KAAM,SACNC,SAAU,GACVC,KAAOI,GAAW,WAACH,MAAAA,WAAI,UAACC,IAAIA,CAACC,KAAK,6BAA6BnC,GAAI,OAAvCkC,qBAAyE,OAANE,EAAE5D,GAAG,WAAK,UAAC6D,IAAAA,CAAEjD,UAAU,uBAA4B,OAAM,UAACkD,IAAAA,CAAEC,QAAS,IAAMC,EAAWJ,YAAI,UAACC,IAAAA,CAAEjD,UAAU,4BAA8B,MACtO,EACD,CAEKgd,EAAc,UAClBrb,GAAW,GACX,IAAM2B,EAAW,MAAMC,EAAAA,CAAUA,CAACC,GAAG,CAAC,SAAU0W,GAC5C5W,GAAYA,EAASG,IAAI,EAAIH,EAASG,IAAI,CAACC,MAAM,CAAG,GAAG,CACzDjC,EAAe6B,EAASG,IAAI,EAC5B5B,EAAayB,EAASK,UAAU,EAChChC,GAAW,GAEf,EAQMiC,EAAsB,MAAOC,EAAiBtB,KAClD2X,EAAW5X,KAAK,CAAGuB,EACnBqW,EAAW3X,IAAI,CAAGA,EAClBZ,GAAW,GACX,IAAM2B,EAAW,MAAMC,EAAAA,CAAUA,CAACC,GAAG,CAAC,SAAU0W,GAC5C5W,GAAYA,EAASG,IAAI,EAAIH,EAASG,IAAI,CAACC,MAAM,CAAG,GAAG,CACzDjC,EAAe6B,EAASG,IAAI,EAC5B1B,EAAW8B,GACXlC,GAAW,GAEf,EAEMyB,EAAa,MAAOU,IACxBiZ,EAAcjZ,EAAI1E,GAAG,EACrB6C,GAAS,EACX,EAEM8B,EAAe,UACnB,MAAMR,EAAAA,CAAUA,CAACS,MAAM,CAAC,UAAqB,OAAX8Y,IAClCE,IACA/a,GAAS,EACX,EAEMmC,EAAY,IAAMnC,EAAS,IAMjC,MAJAoC,CAAAA,EAAAA,EAAAA,SAAAA,CAASA,CAAC,KACR2Y,GACF,EAAG,EAAE,EAGH,WAACna,MAAAA,WACC,WAACyB,EAAAA,CAAKA,CAAAA,CAACC,KAAMvC,EAAawC,OAAQJ,YAChC,UAACE,EAAAA,CAAKA,CAACG,MAAM,EAACC,WAAW,aACvB,UAACJ,EAAAA,CAAKA,CAACK,KAAK,WAAE/E,EAAE,kBAElB,WAAC0E,EAAAA,CAAKA,CAACM,IAAI,YAAEhF,EAAE,kCAAkC,OACjD,WAAC0E,EAAAA,CAAKA,CAACO,MAAM,YACX,UAACC,EAAAA,CAAMA,CAAAA,CAACC,QAAQ,YAAY5B,QAASiB,WACpCxE,EAAE,YAEH,UAACkF,EAAAA,CAAMA,CAAAA,CAACC,QAAQ,UAAU5B,QAASY,WAClCnE,EAAE,eAKP,UAACoF,EAAAA,CAAQA,CAAAA,CACPvC,QAASA,EACTgB,KAAMjC,EACNI,UAAWA,EACXqD,UAAW,GACXrB,oBAAqBA,EACrBsB,iBA1DmB,CA0DDA,GAzDtBgV,EAAW5X,KAAK,CAAGR,EACnBoY,EAAW3X,IAAI,CAAGA,EAClBya,GACF,MA0DF,yPCoCA,MAlIsB,IAClB,IAAMC,EAAoB,CACtB7d,IAAK,GACLC,MAAO,EACX,EACM,CAAEO,GAAC,CAAE,CAAGC,CAAAA,EAAAA,EAAAA,EAAAA,CAAcA,CAAC,UACvB,CAACuH,EAAYC,EAAc,CAAG3F,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAYub,GAElD1V,EAAoBL,EAAMM,MAAM,EAAwB,mBAApBN,EAAMM,MAAM,CAAC,EAAE,EAAyBN,EAAMM,MAAM,CAAC,EAAE,CAE3FC,EAAUC,CAAAA,EAAAA,EAAAA,MAAAA,CAAMA,CAAC,MAkBjBC,EAAe,MAAOC,QAMpBtE,EACAuE,EANJD,EAAME,cAAc,GACpB,IAAMC,EAAM,CACR1I,MAAO+H,EAAW/H,KAAK,CAAC2I,IAAI,EAChC,EAIIT,GACAM,EAAW,KADD,yDAEVvE,EAAW,MAAMC,EAAAA,CAAUA,CAAC0E,KAAK,CAAC,cAA8B,OAAhBf,EAAMM,MAAM,CAAC,EAAE,EAAIO,KAEnEF,EAAW,4DACXvE,EAAW,MAAMC,EAAAA,CAAUA,CAAC2E,IAAI,CAAC,aAAcH,IAE/CzE,GAAYA,EAASlE,GAAG,EAAE,EAC1B6E,EAAKA,CAACC,OAAO,CAACtE,EAAEiI,IAChBM,IAAAA,IAAW,CAAC,6BAER7E,OAAAA,EAAAA,KAAAA,EAAAA,EAAU8E,SAAS,CAAnB9E,GAAwB,KACxBW,EAD+B,EAC1BA,CAACE,KAAK,CAACvE,EAAE,yBAEdqE,EAAAA,EAAKA,CAACE,KAAK,CAACb,EAGxB,EAiBA,MAfAe,CAAAA,EAAAA,EAAAA,SAAAA,CAASA,CAAC,KACN,IAAM6Y,EAAkB,CACpB1a,MAAO,CAAC,EACRH,KAAM,CAAEhD,MAAO,KAAM,EACrBiD,MAAO,GACX,EACIiF,GAKA4V,CAJyB,MADf,IAEN,IAAM7Z,EAAsB,MAAMC,EAAAA,CAAUA,CAACC,GAAG,CAAC,cAA8B,OAAhB0D,EAAMM,MAAM,CAAC,EAAE,EAAI0V,GAClF7V,EAAc,GAAgB,EAAE,GAAGkB,CAAS,CAAE,EAAhB,CAAmBjF,CAAQ,IAC7D,GAGR,EAAG,EAAE,EAGD,UAACT,MAAAA,UACG,UAAC/C,EAAAA,CAASA,CAAAA,CAACE,UAAU,WAAWD,KAAK,aACjC,UAACyI,EAAAA,CAAIA,CAAAA,CACD3C,MAAO,CACH4C,UAAW,MACXC,UAAW,kEACf,WAEA,UAACC,EAAAA,CAAqBA,CAAAA,CAACC,SAAUjB,EAAckB,IAAKpB,EAASqB,cAAe1B,EAAY2B,oBAAoB,WACxG,WAACP,EAAAA,CAAIA,CAAC5D,IAAI,YACN,UAAC3E,EAAAA,CAAGA,CAAAA,UACA,UAACC,EAAAA,CAAGA,CAAAA,UACA,UAACsI,EAAAA,CAAIA,CAAC7D,KAAK,WAAE/E,EAAE,gDAGvB,UAACoJ,KAAAA,CAAAA,GACD,UAAC/I,EAAAA,CAAGA,CAAAA,UACA,UAACC,EAAAA,CAAGA,CAAAA,CAACE,EAAE,IAACY,GAAI,EAAGD,GAAI,YACf,WAACkI,EAAAA,CAAIA,CAACC,KAAK,YACP,UAACD,EAAAA,CAAIA,CAACE,KAAK,EAACnJ,UAAU,0BACjBJ,EAAE,4CAEP,UAACwJ,EAAAA,EAASA,CAAAA,CACN1G,KAAK,QACL2G,GAAG,QACHC,QAAQ,IACR5I,MAAO0G,EAAW/H,KAAK,CACvBkK,UAAW,GAA+C,KAA/BC,OAAO9I,GAAS,IAAIsH,IAAI,GACnDyB,aAAc,CACVF,UAAW3J,EAAE,qDACjB,EACAe,SApFnB,CAoF6B+I,GAnF9C,GAAIzI,EAAE0I,MAAM,CAAE,CACV,GAAM,MAAEjH,CAAI,OAAEhC,CAAK,CAAE,CAAGO,EAAE0I,MAAM,CAChCtC,EAAc,GAAgB,EAC1B,GAAGkB,CAAS,CACZ,CAAC7F,CAFyB,CAEpB,CAAEhC,EACZ,EACJ,CACJ,WAiFwB,UAACT,EAAAA,CAAGA,CAAAA,CAACD,UAAU,gBACX,WAACE,EAAAA,CAAGA,CAAAA,WACA,UAAC4E,EAAAA,CAAMA,CAAAA,CAAC9E,UAAU,OAAOO,KAAK,SAASwE,QAAQ,mBAC1CnF,EAAE,yCAEP,UAACkF,EAAAA,CAAMA,CAAAA,CAAC9E,UAAU,OAAOmD,QApGpC,CAoG6CyG,IAnG9DvC,EAAc4V,GAEdpT,OAAOC,QAAQ,CAAC,EAAG,EACvB,EAgGgF/E,QAAQ,gBACnDnF,EAAE,wCAEP,UAACkD,IAAIA,CACDC,KAAK,6BACLnC,GAAK,OAFJkC,6BAID,UAACgC,EAAAA,CAAMA,CAAAA,CAACC,QAAQ,qBAAanF,EAAE,0DAUvE,gLCxGA,MA/ByB,QAuBjB8F,EAAAA,EAtBN,GAAM,GAAE9F,CAAC,CAAE,CAAGC,CAAAA,EAAAA,EAAAA,EAAAA,CAAcA,CAAC,UACvBud,EAAuB,EA6BAxB,EA3BzB,WAAC9b,EAAAA,CAASA,CAAAA,CAAC+F,MAAO,CAAEC,UAAW,QAAS,EAAG/F,KAAK,IAACC,UAAU,gBACzD,UAACC,EAAAA,CAAGA,CAAAA,UACF,UAACC,EAAAA,CAAGA,CAAAA,CAACC,GAAI,YACP,UAAC4F,EAAAA,CAAWA,CAAAA,CAAC1G,MAAOO,EAAE,mDAG1B,UAACK,EAAAA,CAAGA,CAAAA,CAACD,UAAU,gBACb,UAACE,EAAAA,CAAGA,CAAAA,CAACC,GAAI,YACP,UAACkd,EAAAA,OAAgBA,CAAAA,CAAAA,UASrBC,EAAsBC,CAAAA,EAAAA,EAAAA,iBAAAA,CAAiBA,CAAC,IAAM,UAACH,EAAAA,CAAAA,IAC/C1X,EAAYS,CAAAA,EAAAA,EAAAA,EAAAA,CAAWA,CAAC,GAAWT,SACzC,IAAI,GAAEA,GAAAA,OAAAA,EAAAA,EAAOU,IAAPV,OAAOU,GAAPV,OAAAA,EAAAA,EAAoB8X,YAAY,EAAhC9X,KAAAA,EAAAA,CAAkC,CAAC,GAAnCA,UAAgD,EAIpD,CAJuD,EAIvD,OAAC4X,EAAAA,CAAAA,GAHM,UAAChX,EAAAA,OAAeA,CAAAA,CAAAA,EAK3B,8KC2FA,MAzHuB,IACnB,GAAM,CAAE1G,CAAC,CAAE,CAAGC,CAAAA,EAAAA,EAAAA,EAAAA,CAAcA,CAAC,QAwHlBgY,EAvHL,CAACrW,EAAWC,EAAe,CAAGC,CAAAA,EAAAA,EAAAA,CAuHXmW,EAAC,KAvHUnW,CAAQA,CAAC,EAAE,EACzC,EAAGC,EAAW,CAAGD,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,EAAC,GAC1B,CAACE,EAAWC,EAAa,CAAGH,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAC,GACrC,CAACI,EAASC,EAAW,CAAGL,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAC,IACjC,CAACM,EAAaC,EAAS,CAAGP,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,EAAC,GACnC,CAAC+b,EAAiBC,EAAmB,CAAGhc,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAC,CAAC,GAGlDwb,EAAkB,CACpB7a,KAAM,CAAEhD,MAAO,KAAM,EACrBiD,MAAOR,EACPS,KAAM,EACNC,MAAO,CAAC,CACZ,EAEMC,EAAU,CACZ,CACIC,KAAM9C,EAAE,sCACR+C,SAAU,OACd,EACA,CACID,KAAM9C,EAAE,uCACR+C,SAAU,GACVC,KAAM,GACF,WAACC,MAAAA,WACG,UAACC,IAAIA,CAACC,KAAK,6BAA6BnC,GAAI,OAAvCkC,0BAA8E,OAANE,EAAE5D,GAAG,WAE9E,UAAC6D,IAAAA,CAAEjD,UAAU,uBAEV,OAEP,UAACkD,IAAAA,CAAEC,QAAS,GAAOC,EAAWJ,EAAG/B,YAC7B,UAACgC,IAAAA,CAAEjD,UAAU,4BACZ,MAGjB,EACH,CAEKmd,EAAmB,UACrBxb,GAAW,GACX,IAAM2B,EAAW,MAAMC,EAAAA,CAAUA,CAACC,GAAG,CAAC,aAAc0Z,GAChD5Z,GAAYA,EAASG,IAAI,EAAIH,EAASG,IAAI,CAACC,MAAM,CAAG,GAAG,CACvDjC,EAAe6B,EAASG,IAAI,EAC5B5B,EAAayB,EAASK,UAAU,EAChChC,EAAW,IAEnB,EAQMiC,EAAsB,MAAOC,EAAiBtB,KAChD2a,EAAgB5a,KAAK,CAAGuB,EACxBqZ,EAAgB3a,IAAI,CAAGA,EACvBZ,GAAW,GACX,IAAM2B,EAAW,MAAMC,EAAAA,CAAUA,CAACC,GAAG,CAAC,aAAc0Z,GAChD5Z,GAAYA,EAASG,IAAI,EAAIH,EAASG,IAAI,CAACC,MAAM,CAAG,GAAG,CACvDjC,EAAe6B,EAASG,IAAI,EAC5B1B,EAAW8B,GACXlC,GAAW,GAEnB,EAEMyB,EAAa,MAAOU,EAAU7C,KAChCA,EAAE6G,cAAc,GAChB4V,EAAmB5Z,EAAI1E,GAAG,EAC1B6C,GAAS,EACb,EAEM8B,EAAe,UACjB,GAAI,CACA,MAAMR,EAAAA,CAAUA,CAACS,MAAM,CAAC,cAA8B,OAAhByZ,IACtCN,IACAlb,GAAS,GACTgC,EAAAA,EAAKA,CAACC,OAAO,CAACtE,EAAE,6DACpB,CAAE,MAAOuE,EAAO,CACZF,EAAAA,EAAKA,CAACE,KAAK,CAACvE,EAAE,uDAClB,CACJ,EAEMwE,EAAY,IAAMnC,GAAS,GAMjC,MAJAoC,CAAAA,EAAAA,EAAAA,SAAAA,CAASA,CAAC,KACN8Y,GACJ,EAAG,EAAE,EAGD,WAACta,MAAAA,WACG,WAACyB,EAAAA,CAAKA,CAAAA,CAACC,KAAMvC,EAAawC,OAAQJ,YAC9B,UAACE,EAAAA,CAAKA,CAACG,MAAM,EAACC,WAAW,aACrB,WAACJ,EAAAA,CAAKA,CAACK,KAAK,YAAC,IAAE/E,EAAE,qDAErB,UAAC0E,EAAAA,CAAKA,CAACM,IAAI,WAAEhF,EAAE,uEACf,WAAC0E,EAAAA,CAAKA,CAACO,MAAM,YACT,UAACC,EAAAA,CAAMA,CAAAA,CAACC,QAAQ,YAAY5B,QAASiB,WAChCxE,EAAE,yCAEP,UAACkF,EAAAA,CAAMA,CAAAA,CAACC,QAAQ,UAAU5B,QAASY,WAC9BnE,EAAE,4CAKf,UAACoF,EAAAA,CAAQA,CAAAA,CACLvC,QAASA,EACTgB,KAAMjC,EACNI,UAAWA,EACXqD,UAAW,GACXrB,oBAAqBA,EACrBsB,iBAhEc3C,CAgEI2C,GA/D1BgY,EAAgB5a,KAAK,CAAGR,EACxBob,EAAgB3a,IAAI,CAAGA,EACvB4a,GACJ,MAgEJ,8KCUA,MAjIsB,IAClB,GAAM,CAAEvd,CAAC,CAAE,CAAGC,CAAAA,EAAAA,EAAAA,EAAAA,CAAcA,CAAC,QAgIlB4M,EA/HL,CAACjL,EAAWC,EAAe,CAAGC,CAAAA,EAAAA,EAAAA,EA+HX,MA/HWA,CAAQA,CAAC,EAAE,EACzC,EAAGC,EAAW,CAAGD,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAC,IAC1B,CAACE,EAAWC,EAAa,CAAGH,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAC,GACrC,CAACI,EAASC,EAAW,CAAGL,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAC,IACjC,CAACM,EAAaC,EAAS,CAAGP,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,EAAC,GACnC,CAACic,EAAgBC,EAAkB,CAAGlc,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAC,CAAC,GAEhDe,EAAU,CACZ,CACIC,KAAM9C,EAAE,mCACR+C,SAAU,OACd,EACA,CACID,KAAM9C,EAAE,8BACR+C,SAAU,OACVC,KAAM,GAAYI,EAAEwC,IAAI,EAE5B,CACI9C,KAAM9C,EAAE,qCACR+C,SAAU,cACVC,KAAM,GAAYI,EAAE0K,WAAW,CAACY,OAAO,CAAC,WAAY,GACxD,EACA,CACI5L,KAAM9C,EAAE,gCACR+C,SAAU,GACVC,KAAM,GACF,WAACC,MAAAA,WACG,UAACC,IAAIA,CAACC,KAAK,6BAA6BnC,GAAI,OAAvCkC,yBAA6E,OAANE,EAAE5D,GAAG,WAE7E,UAAC6D,IAAAA,CAAEjD,UAAU,uBAEV,OAEP,UAACkD,IAAAA,CAAEC,QAAS,IAAMC,EAAWJ,YACzB,UAACC,IAAAA,CAAEjD,UAAU,4BACZ,MAGjB,EACH,CAEDqE,CAAAA,EAAAA,EAAAA,SAAAA,CAASA,CAAC,KACN0J,GACJ,EAAG,EAAE,EAEL,IAAMD,EAAiB,CACnBzL,KAAM,CAAEhD,MAAO,KAAM,EACrBiD,MAAOR,EACPS,KAAM,EACNC,MAAO,CAAC,CACZ,EAEMuL,EAAkB,UACpBpM,GAAW,GACX,IAAM2B,EAAW,MAAMC,EAAAA,CAAUA,CAACC,GAAG,CAAC,YAAasK,GAC/CxK,GAAYA,EAASG,IAAI,EAAIH,EAASG,IAAI,CAACC,MAAM,CAAG,GAAG,CACvDjC,EAAe6B,EAASG,IAAI,EAC5B5B,EAAayB,EAASK,UAAU,EAChChC,GAAW,GAEnB,EAQMiC,EAAsB,MAAOC,EAAiBtB,KAChDuL,EAAexL,KAAK,CAAGuB,EACvBiK,EAAevL,IAAI,CAAGA,EACtBZ,GAAW,GACX,IAAM2B,EAAW,MAAMC,EAAAA,CAAUA,CAACC,GAAG,CAAC,YAAasK,GAC/CxK,GAAYA,EAASG,IAAI,EAAIH,EAASG,IAAI,CAACC,MAAM,CAAG,GAAG,CACvDjC,EAAe6B,EAASG,IAAI,EAC5B1B,EAAW8B,GACXlC,EAAW,IAEnB,EAEMyB,EAAa,MAAOU,IACtB8Z,EAAkB9Z,EAAI1E,GAAG,EACzB6C,GAAS,EACb,EAEM8B,EAAe,UACjB,GAAI,CACA,MAAMR,EAAAA,CAAUA,CAACS,MAAM,CAAC,aAA4B,OAAf2Z,IACrC5P,IACA9L,GAAS,GACTgC,EAAAA,EAAKA,CAACC,OAAO,CAACtE,EAAE,2DACpB,CAAE,MAAOuE,EAAO,CACZF,EAAAA,EAAKA,CAACE,KAAK,CAACvE,EAAE,qDAClB,CACJ,EAEMwE,EAAY,IAAMnC,EAAS,IAEjC,MACI,WAACY,MAAAA,WACG,WAACyB,EAAAA,CAAKA,CAAAA,CAACC,KAAMvC,EAAawC,OAAQJ,YAC9B,UAACE,EAAAA,CAAKA,CAACG,MAAM,EAACC,WAAW,aACrB,UAACJ,EAAAA,CAAKA,CAACK,KAAK,WAAE/E,EAAE,4CAEpB,UAAC0E,EAAAA,CAAKA,CAACM,IAAI,WAAEhF,EAAE,+DACf,WAAC0E,EAAAA,CAAKA,CAACO,MAAM,YACT,UAACC,EAAAA,CAAMA,CAAAA,CAACC,QAAQ,YAAY5B,QAASiB,WAChCxE,EAAE,kCAEP,UAACkF,EAAAA,CAAMA,CAAAA,CAACC,QAAQ,UAAU5B,QAASY,WAC9BnE,EAAE,qCAKf,UAACoF,EAAAA,CAAQA,CAAAA,CACLvC,QAASA,EACTgB,KAAMjC,EACNI,UAAWA,EACXqD,WAAW,EACXrB,oBAAqBA,EACrBsB,iBA3Da,CA2DKA,GA1D1B4I,EAAexL,KAAK,CAAGR,EACvBgM,EAAevL,IAAI,CAAGA,EACtBwL,GACJ,MA2DJ,8KChGA,MAhCuBkE,IACrB,GAAM,CAAErS,CAAC,CAAE,CAAGC,CAAAA,EAAAA,EAAAA,EAAAA,CAAcA,CAAC,QA+BhBic,EA7Bb,MACE,OA4ByB,CA5BzB,EAACjZ,MAAAA,UACC,WAAC/C,EAAAA,CAASA,CAAAA,CAAC+F,MAAO,CAAEC,UAAW,QAAS,EAAG/F,KAAK,IAACC,UAAU,gBACzD,UAACC,EAAAA,CAAGA,CAAAA,UACF,UAACC,EAAAA,CAAGA,CAAAA,CAACC,GAAI,YACP,UAAC4F,EAAAA,CAAWA,CAAAA,CAAC1G,MAAM,mBAGvB,UAACY,EAAAA,CAAGA,CAAAA,UACF,UAACC,EAAAA,CAAGA,CAAAA,CAACC,GAAI,YACP,UAAC2C,IAAIA,CACHC,KAAK,6BACLnC,GAAG,OAFAkC,mCAIH,UAACgC,EAAAA,CAAMA,CAAAA,CAACC,QAAQ,YAAYiB,KAAK,cAChCpG,EAAE,uBAKT,UAACK,EAAAA,CAAGA,CAAAA,CAACD,UAAU,gBACb,UAACE,EAAAA,CAAGA,CAAAA,CAACC,GAAI,YACP,UAACqS,EAAAA,OAAaA,CAAAA,CAAAA,WAM1B,oICfA,MApB2B,OAAC,YAAElT,CAAU,CAAEE,OAoB3B6Q,GApBmC,SAAC3Q,CAAO,CAAO,GACzD,CAmByB2Q,EAnBvBzQ,CAAC,CAAE,CAAGC,CAAAA,EAAAA,EAAAA,EAAAA,CAAcA,CAAC,UAC7B,MACE,UAACC,EAAAA,CAASA,CAAAA,CAACC,KAAK,IAACC,UAAU,eACzB,UAACC,EAAAA,CAAGA,CAAAA,UACF,UAACC,EAAAA,CAAGA,CAAAA,CAACE,GAAI,EAAGJ,UAAU,eACpB,UAACM,EAAAA,CAAWA,CAAAA,CACVC,KAAK,OACLP,UAAU,cACVQ,YAAaZ,EAAE,uCACfa,aAAW,SACXC,MAAOpB,EACPqB,SAAUnB,SAMtB,kOCsCA,MAjDoB,QAiCZkG,EAAAA,EAhCN,GAAM,GAAE9F,CAAC,CAAE,CAAGC,CAAAA,EAAAA,EAAAA,EAAAA,CAAcA,CAAC,SAgDL8b,CA/ClBkC,CA+CmB,CA/CF,IAEnB,WAAC/d,EAAAA,CAASA,CAAAA,CAAC+F,MAAO,CAAEiY,SAAU,QAAS,EAAG/d,KAAK,IAACC,UAAU,gBACxD,UAACC,EAAAA,CAAGA,CAAAA,UACF,UAACC,EAAAA,CAAGA,CAAAA,CAACC,GAAI,YACP,UAAC4F,EAAAA,CAAWA,CAAAA,CAAC1G,MAAOO,EAAE,sCAG1B,UAACK,EAAAA,CAAGA,CAAAA,UACF,UAACC,EAAAA,CAAGA,CAAAA,CAACC,GAAI,YACP,UAAC2C,IAAIA,CACHC,KAAK,6BACLnC,GAAG,OAFAkC,iCAIH,UAACgC,EAAAA,CAAMA,CAAAA,CAACC,QAAQ,YAAYiB,KAAK,cAC9BpG,EAAE,0CAKX,UAACK,EAAAA,CAAGA,CAAAA,CAACD,UAAU,gBACb,UAACE,EAAAA,CAAGA,CAAAA,CAACC,GAAI,YACP,UAACqZ,EAAAA,OAAWA,CAAAA,CAAAA,UAOhBuE,EAAiBC,CAAAA,EAAAA,EAAAA,aAAAA,CAAaA,CAAC,IAAM,UAACH,EAAAA,CAAAA,IACtCnY,EAAYS,CAAAA,EAAAA,EAAAA,EAAAA,CAAWA,CAAET,GAAUA,SACzC,IAAI,GAAEA,GAAAA,OAAAA,EAAAA,EAAOU,IAAPV,OAAOU,GAAPV,OAAAA,EAAAA,EAAAA,MAAoBuY,EAApBvY,KAAAA,EAAAA,CAA4B,CAAC,GAA7BA,UAA0C,EAI9C,UAACqY,EAAAA,CAAAA,GAHM,UAACzX,EAAAA,OAAeA,CAAAA,CAAAA,EAK3B,8KC+EA,MAvH6B,IACzB,GAAM,CAAC9E,EAAWC,EAAe,CAAGC,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAC,EAAE,EACzC,EAAGC,EAAW,CAAGD,CAAAA,EAAAA,EAAAA,MAqHQoF,EArHRpF,CAAQA,EAAC,GAC1B,CAACE,EAAWC,EAAa,CAAGH,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAC,GACrC,CAACI,EAASC,EAAW,CAAGL,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAC,IACjC,CAACM,EAAaC,EAAS,CAAGP,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,EAAC,GACnC,CAACwc,EAAuBC,EAAyB,CAAGzc,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAC,CAAC,GAC9D,GAAE9B,CAAC,CAAE,CAAGC,CAAAA,EAAAA,EAAAA,EAAAA,CAAcA,CAAC,UAEvBwI,EAAwB,CAC1BhG,KAAM,CAAEhD,MAAO,KAAM,EACrBiD,MAAOR,EACPS,KAAM,EACNC,MAAO,CAAC,CACZ,EAEMC,EAAU,CACZ,CACIC,KAAM9C,EAAE,SACR+C,SAAU,OACd,EACA,CACID,KAAM9C,EAAE,UACR+C,SAAU,GACVC,KAAM,GACF,WAACC,MAAAA,WACG,UAACC,IAAIA,CAACC,KAAK,6BAA6BnC,GAAI,OAAvCkC,gCAAoF,OAANE,EAAE5D,GAAG,WAEpF,UAAC6D,IAAAA,CAAEjD,UAAU,uBAEV,OAEP,UAACkD,IAAAA,CAAEC,QAAS,IAAMC,EAAWJ,YACzB,UAACC,IAAAA,CAAEjD,UAAU,4BACZ,MAGjB,EACH,CAEKoe,EAAyB,UAC3Bzc,GAAW,GACX,IAAM2B,EAAW,MAAMC,EAAAA,CAAUA,CAACC,GAAG,CAAC,oBAAqB6E,GACvD/E,GAAYA,EAASG,IAAI,EAAIH,EAASG,IAAI,CAACC,MAAM,CAAG,GAAG,CACvDjC,EAAe6B,EAASG,IAAI,EAC5B5B,EAAayB,EAASK,UAAU,EAChChC,GAAW,GAEnB,EAQMiC,EAAsB,MAAOC,EAAiBtB,KAChD8F,EAAsB/F,KAAK,CAAGuB,EAC9BwE,EAAsB9F,IAAI,CAAGA,EAC7BZ,EAAW,IACX,IAAM2B,EAAW,MAAMC,EAAAA,CAAUA,CAACC,GAAG,CAAC,oBAAqB6E,GACvD/E,GAAYA,EAASG,IAAI,EAAIH,EAASG,IAAI,CAACC,MAAM,CAAG,GAAG,CACvDjC,EAAe6B,EAASG,IAAI,EAC5B1B,EAAW8B,GACXlC,GAAW,GAEnB,EAEMyB,EAAa,MAAOU,IACtBqa,EAAyBra,EAAI1E,GAAG,EAChC6C,GAAS,EACb,EAEM8B,EAAe,UACjB,GAAI,CACA,MAAMR,EAAAA,CAAUA,CAACS,MAAM,CAAC,qBAA2C,OAAtBka,IAC7CE,IACAnc,GAAS,GACTgC,EAAAA,EAAKA,CAACC,OAAO,CAACtE,EAAE,kEACpB,CAAE,MAAOuE,EAAO,CACZF,EAAAA,EAAKA,CAACE,KAAK,CAACvE,EAAE,4DAClB,CACJ,EAEMwE,EAAY,IAAMnC,GAAS,GAMjC,MAJAoC,CAAAA,EAAAA,EAAAA,SAAAA,CAASA,CAAC,KACN+Z,GACJ,EAAG,EAAE,EAGD,WAACvb,MAAAA,WACG,WAACyB,EAAAA,CAAKA,CAAAA,CAACC,KAAMvC,EAAawC,OAAQJ,YAC9B,UAACE,EAAAA,CAAKA,CAACG,MAAM,EAACC,WAAW,aACrB,UAACJ,EAAAA,CAAKA,CAACK,KAAK,WAAE/E,EAAE,2CAEpB,UAAC0E,EAAAA,CAAKA,CAACM,IAAI,WAAEhF,EAAE,uCACf,WAAC0E,EAAAA,CAAKA,CAACO,MAAM,YACT,UAACC,EAAAA,CAAMA,CAAAA,CAACC,QAAQ,YAAY5B,QAASiB,WAChCxE,EAAE,YAEP,UAACkF,EAAAA,CAAMA,CAAAA,CAACC,QAAQ,UAAU5B,QAASY,WAC9BnE,EAAE,eAKf,UAACoF,EAAAA,CAAQA,CAAAA,CACLvC,QAASA,EACTgB,KAAMjC,EACNI,UAAWA,EACXqD,WAAW,EACXrB,oBAAqBA,EACrBsB,iBA/Dc3C,CA+DI2C,GA9D1BmD,EAAsB/F,KAAK,CAAGR,EAC9BuG,EAAsB9F,IAAI,CAAGA,EAC7B6b,GACJ,MA+DJ,yPCwBA,MAvI+B,IAC3B,IAAMC,EAA6B,CAC/Bhf,MAAO,EACX,EAEM,CAAC+H,EAAYC,EAAc,CAAG3F,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAqB2c,GAE3D9W,EAAWL,EAAMM,CAgIUkV,EAAC,GAhIL,EAAwB,6BAApBxV,EAAMM,MAAM,CAAC,EAAE,EAAmCN,EAAMM,MAAM,CAAC,EAAE,CAC5F,CAAE5H,GAAC,CAAE,CAAGC,CAAAA,EAAAA,EAAAA,EAAAA,CAAcA,CAAC,UAEvB4H,EAAUC,CAAAA,EAAAA,EAAAA,MAAAA,CAAMA,CAAC,MAkBjBC,EAAe,MAAOC,QAMpBtE,EACAuE,EANJD,EAAME,cAAc,GACpB,IAAMC,EAAM,CACR1I,MAAO+H,EAAW/H,KAAK,CAAC2I,IAAI,EAChC,EAIIT,GACAM,EAAW,KADD,6CAEVvE,EAAW,MAAMC,EAAAA,CAAUA,CAAC0E,KAAK,CAAC,uBAAuC,OAAhBf,EAAMM,MAAM,CAAC,EAAE,EAAIO,KAE5EF,EAAW,4CACXvE,EAAW,MAAMC,EAAAA,CAAUA,CAAC2E,IAAI,CAAC,sBAAuBH,IAExDzE,GAAYA,EAASlE,GAAG,EAAE,EAC1B6E,EAAKA,CAACC,OAAO,CAACtE,EAAEiI,IAChBM,IAAAA,IAAW,CAAC,uCAER7E,OAAAA,EAAAA,KAAAA,EAAAA,EAAU8E,SAAAA,CAAV9E,GAAwB,KACxBW,EAAAA,EAAKA,CAACE,KAAK,CAACvE,EAAE,yBAEdqE,EAAAA,EAAKA,CAACE,KAAK,CAACb,EAGxB,EAoBA,MAlBAe,CAAAA,EAAAA,EAAAA,SAAAA,CAASA,CAAC,KACN,IAAM6M,EAA2B,CAC7B1O,MAAO,CAAC,EACRH,KAAM,CAAEhD,MAAO,KAAM,EACrBiD,MAAO,GACX,EACIiF,GAQA4J,CAPkC,MADxB,IAEN,IAAM7N,EAA+B,MAAMC,EAAAA,CAAUA,CAACC,GAAG,CACrD,uBAAuC,OAAhB0D,EAAMM,MAAM,CAAC,EAAE,EACtC0J,GAEJ7J,EAAc,GAAgB,EAAE,GAAGkB,CAAS,CAAE,EAAhB,CAAmBjF,CAAQ,CAAC,GAC9D,GAGR,EAAG,EAAE,EAGD,UAACT,MAAAA,UACG,UAAC/C,EAAAA,CAASA,CAAAA,CAACE,UAAU,WAAWD,KAAK,aACjC,UAACyI,EAAAA,CAAIA,CAAAA,CACD3C,MAAO,CACH4C,UAAW,MACXC,UAAW,kEACf,WAEA,UAACC,EAAAA,CAAqBA,CAAAA,CAACC,SAAUjB,EAAckB,IAAKpB,EAASqB,cAAe1B,EAAY2B,oBAAoB,WACxG,WAACP,EAAAA,CAAIA,CAAC5D,IAAI,YACN,UAAC3E,EAAAA,CAAGA,CAAAA,UACA,UAACC,EAAAA,CAAGA,CAAAA,UACA,UAACsI,EAAAA,CAAIA,CAAC7D,KAAK,WACN/E,EAAE,gEAIf,UAACoJ,KAAAA,CAAAA,GACD,UAAC/I,EAAAA,CAAGA,CAAAA,UACA,UAACC,EAAAA,CAAGA,CAAAA,CAACE,EAAE,IAACY,GAAI,EAAGD,GAAI,YACf,WAACkI,EAAAA,CAAIA,CAACC,KAAK,YACP,UAACD,EAAAA,CAAIA,CAACE,KAAK,EAACnJ,UAAU,0BACjBJ,EAAE,4DAEP,UAACwJ,EAAAA,EAASA,CAAAA,CACN1G,KAAK,QACL2G,GAAG,QACHC,QAAQ,IACR5I,MAAO0G,EAAW/H,KAAK,CACvBkK,UAAW,GAAoC,KAAjB7I,EAAMsH,IAAI,GACxCyB,aAAc,CACVF,UAAW3J,EAAE,wCACjB,EACAe,SAzFlBM,CAyF4ByI,GAxF9C,GAAIzI,EAAE0I,MAAM,CAAE,CACV,GAAM,MAAEjH,CAAI,OAAEhC,CAAK,CAAE,CAAGO,EAAE0I,MAAM,CAChCtC,EAAc,GAAgB,EAC1B,GAAGkB,CAAS,CACZ,CAAC7F,CAFyB,CAEpB,CAAEhC,EACZ,EACJ,CACJ,WAsFwB,UAACT,EAAAA,CAAGA,CAAAA,CAACD,UAAU,gBACX,WAACE,EAAAA,CAAGA,CAAAA,WACA,UAAC4E,EAAAA,CAAMA,CAAAA,CAAC9E,UAAU,OAAOO,KAAK,SAASwE,QAAQ,mBAC1CnF,EAAE,YAEP,UAACkF,EAAAA,CAAMA,CAAAA,CAAC9E,UAAU,OAAOmD,QAzGpC,CAyG6CyG,IAxG9DvC,EAAcgX,GAEdxU,OAAOC,QAAQ,CAAC,EAAG,EACvB,EAqGgF/E,QAAQ,gBACnDnF,EAAE,WAEP,UAACkD,IAAIA,CACDC,KAAK,6BACLnC,GAAK,OAFJkC,uCAID,UAACgC,EAAAA,CAAMA,CAAAA,CAACC,QAAQ,qBAAanF,EAAE,6BAUvE,yJCzGA,MArC0B,OAAC,gBAAEka,CAAc,IAqC5BD,GArC8BnZ,CAAK,CAAO,GACjD,GAAEd,CAAC,CAAC2K,IAoCoBsP,EApChB,CAAE,CAAGha,CAAAA,EAAAA,EAAAA,EAAAA,CAAcA,CAAC,UAC5B6P,EAAgC,OAAlBnF,EAAKG,QAAQ,CAAW,CAACiF,SAAU,KAAK,EAAI,CAACtQ,MAAO,KAAK,EACvEoL,EAAcF,EAAKG,QAAQ,CAC3B,CAAC4T,EAAWC,EAAa,CAAG7c,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAQ,EAAE,EAC9CoO,EAAgB,CACpBzN,KAAMqN,EACNpN,MAAO,IACPyN,aAAatF,CACf,EAWA,MATApG,CAAAA,EAAAA,EAAAA,SAAAA,CAASA,CAAC,KAORma,CANuB,UACrB,IAAMlb,EAAW,MAAMC,EAAAA,CAAUA,CAACC,GAAG,CAAC,WAAYsM,GAC9CxM,GAAYA,EAASG,IAAI,EAAIH,EAASG,IAAI,CAACC,MAAM,CAAG,GAAG,EAC5CJ,EAASG,IAAI,EAE9B,GAEF,EAAG,EAAE,EAEH,UAAC3D,EAAAA,CAASA,CAAAA,CAACC,KAAK,aACd,UAACE,EAAAA,CAAGA,CAAAA,UACF,UAACC,EAAAA,CAAGA,CAAAA,CAACE,GAAI,EAAGJ,UAAU,gBACpB,UAACye,EAAAA,EAAMA,CAAAA,CACL/d,MAAO,CAACA,EAAM,CACdF,YAAaZ,EAAE,sCACf8e,aAAa,EACb/d,SAAUmZ,EACV6E,QAASL,EAAU5a,MAAM,CAAG,EAAI4a,EAAUpd,GAAG,CAAC,CAACC,EAAM+T,IAAQ,EAAExU,MAAOS,EAAK/B,GAAG,CAAEmM,MAAOpK,EAAK9B,KAAK,CAAC,GAAM,EAAE,QAMtH,8KCuFA,MAzHwB,IACpB,GAAM,CAAEO,CAAC,CAAE,CAAGC,CAAAA,EAAAA,EAAAA,EAAAA,CAAcA,CAAC,QAwHlBkT,EAvHL,CAACvR,EAAWC,EAAe,CAAGC,CAAAA,EAAAA,EAAAA,EAuHVqR,EAAC,IAvHSrR,CAAQA,CAAC,EAAE,EACzC,EAAGC,EAAW,CAAGD,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,EAAC,GAC1B,CAACE,EAAWC,EAAa,CAAGH,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAC,GACrC,CAACI,EAASC,EAAW,CAAGL,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAC,IACjC,CAACM,EAAaC,EAAS,CAAGP,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,EAAC,GACnC,CAACkd,EAAmBC,EAAqB,CAAGnd,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAC,CAAC,GAGtD4I,EAAmB,CACrBjI,KAAM,CAAEhD,MAAO,KAAM,EACrBiD,MAAOR,EACPS,KAAM,EACNC,MAAO,CAAC,CACZ,EAEMC,EAAU,CACZ,CACIC,KAAM9C,EAAE,uCACR+C,SAAU,OACd,EACA,CACID,KAAM9C,EAAE,wCACR+C,SAAU,GACVC,KAAM,GACF,WAACC,MAAAA,WACG,WAACC,IAAIA,CAACC,KAAK,6BAA6BnC,GAAI,OAAvCkC,6BAAiF,OAANE,EAAE5D,GAAG,YAEhF,IACD,UAAC6D,IAAAA,CAAEjD,UAAU,wBAEV,OAEP,UAACkD,IAAAA,CAAEC,QAAS,IAAMC,EAAWJ,YACzB,UAACC,IAAAA,CAAEjD,UAAU,4BACZ,MAGjB,EACH,CAEK8e,EAAoB,MAAOC,IAC7Bpd,GAAW,GACX,IAAM2B,EAAW,MAAMC,EAAAA,CAAUA,CAACC,GAAG,CAAC,cAAeub,GACjDzb,GAAYA,EAASG,IAAI,EAAIH,EAASG,IAAI,CAACC,MAAM,CAAG,GAAG,CACvDjC,EAAe6B,EAASG,IAAI,EAC5B5B,EAAayB,EAASK,UAAU,EAChChC,GAAW,GAEnB,EAQMiC,EAAsB,MAAOC,EAAiBtB,KAChD+H,EAAiBhI,KAAK,CAAGuB,EACzByG,EAAiB/H,IAAI,CAAGA,EACxBZ,GAAW,GACX,IAAM2B,EAAW,MAAMC,EAAAA,CAAUA,CAACC,GAAG,CAAC,cAAe8G,GACjDhH,GAAYA,EAASG,IAAI,EAAIH,EAASG,IAAI,CAACC,MAAM,CAAG,GAAG,CACvDjC,EAAe6B,EAASG,IAAI,EAC5B1B,EAAW8B,GACXlC,GAAW,GAEnB,EAEMyB,EAAa,MAAOU,IACtB+a,EAAqB/a,EAAI1E,GAAG,EAC5B6C,GAAS,EACb,EAEM8B,EAAe,UACjB,GAAI,CACA,MAAMR,EAAAA,CAAUA,CAACS,MAAM,CAAC,eAAiC,OAAlB4a,IACvCE,EAAkBxU,GAClBrI,GAAS,GACTgC,EAAAA,EAAKA,CAACC,OAAO,CAACtE,EAAE,+DACpB,CAAE,MAAOuE,EAAO,CACZF,EAAAA,EAAKA,CAACE,KAAK,CAACvE,EAAE,yDAClB,CACJ,EAEMwE,EAAY,IAAMnC,GAAS,GAMjC,MAJAoC,CAAAA,EAAAA,EAAAA,SAAAA,CAASA,CAAC,KACNya,EAAkBxU,EACtB,EAAG,EAAE,EAGD,WAACzH,MAAAA,WACG,WAACyB,EAAAA,CAAKA,CAAAA,CAACC,KAAMvC,EAAawC,OAAQJ,YAC9B,UAACE,EAAAA,CAAKA,CAACG,MAAM,EAACC,WAAW,aACrB,UAACJ,EAAAA,CAAKA,CAACK,KAAK,WAAE/E,EAAE,sDAEpB,UAAC0E,EAAAA,CAAKA,CAACM,IAAI,WAAEhF,EAAE,yEACf,WAAC0E,EAAAA,CAAKA,CAACO,MAAM,YACT,UAACC,EAAAA,CAAMA,CAAAA,CAACC,QAAQ,YAAY5B,QAASiB,WAChCxE,EAAE,0CAEP,UAACkF,EAAAA,CAAMA,CAAAA,CAACC,QAAQ,UAAU5B,QAASY,WAC9BnE,EAAE,6CAKf,UAACoF,EAAAA,CAAQA,CAAAA,CACLvC,QAASA,EACTgB,KAAMjC,EACNI,UAAWA,EACXqD,WAAW,EACXrB,oBAAqBA,EACrBsB,iBA/Da,CA+DKA,GA9D1BoF,EAAiBhI,KAAK,CAAGR,EACzBwI,EAAiB/H,IAAI,CAAGA,EACxBuc,EAAkBxU,EACtB,MA+DJ,0JCkGA,MA9MA,SAAS8H,CAAsB,EAC3B,GAAM,CAAExS,CAAC,CAAE,CAAGC,CAAAA,EAAAA,EAAAA,EAAAA,CAAcA,CAAC,UACvB,CAAC2B,CA4Me,CA5MJC,EAAe,CAAGC,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAQ,EAAE,EAChD,EAAGC,EAAW,CAAGD,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,EAAU,GACnC,CAACE,EAAWC,EAAa,CAAGH,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAS,GAC7C,CAACI,EAASC,EAAW,CAAGL,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAS,IACzC,CAACM,EAAaC,EAAS,CAAGP,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,EAAU,GAC5C,CAACuW,EAAWC,EAAa,CAAGxW,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAS,IAC7C,CAACyW,EAAmBC,EAAqB,CAAG1W,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAM,CAAC,GAG3D2W,EAAc,CAChBhW,KAAM,CAAE+T,WAAY,MAAO,EAC3B9T,MAAO,IACPC,KAAM,EACNC,MAAO,CAAE,4BAA6B,iBAAkB,CAC5D,EAEMC,EAAU,CACZ,CACIC,KAAM9C,EAAE,kDACR+C,SAAU,WACVC,KAAM,GAAYI,EAAEuT,QAAQ,EAEhC,CACI7T,KAAM9C,EAAE,oBACR+C,SAAU,kBACVC,KAAM,GAAYI,EAAEgc,eAAe,EAEvC,CACItc,KAAM9C,EAAE,+CACR+C,SAAU,QACVC,KAAOI,GAAWA,EAAEuV,KAAK,EAE7B,CACI7V,KAAM9C,EAAE,gDACR+C,SAAU,GACVC,KAAM,GACF,WAACC,MAAAA,WACIG,eAAEic,iBAAiB,EAA2C,oBAAxBjc,EAAEic,iBAAiB,CACtD,iCACI,UAACna,EAAAA,CAAMA,CAAAA,CAACC,QAAQ,UAAUiB,KAAK,KAAK7C,QAAS,IAAMC,EAAWJ,EAAG,oBAC5DpD,EAAE,oBACE,UAIb,yBAEqB,aAAxBoD,EAAEic,iBAAiB,EAA2C,oBAAxBjc,EAAEic,iBAAiB,CACtD,UAACna,EAAAA,CAAMA,CAAAA,CAACC,QAAQ,YAAYiB,KAAK,KAAK7C,QAAS,IAAMC,EAAWJ,EAAG,mBAC9DpD,EAAE,kBAGP,2BAIhB,EACH,CAEKsf,EAAe,IACjB,IAAIxJ,EAAmB,EAAE,CAezB,OAdAjS,EAAK0b,OAAO,CAAC,UACT7I,GAAAA,EAAM8I,YAAN9I,MAAwB,CAAC6I,OAAO,CAAC,IACzBE,GAAkD,mBAAmB,CAAhDA,EAAkB5G,MAAM,EAC7C/C,EAAU4J,IAAI,CAAC,CACX,GAAGhJ,CAAI,CACP,GAAG,CACCiJ,cAAeF,EAAkBE,aAAa,CAC9CP,gBAAiBK,EAAkBL,eAAe,CAClDC,kBAAmBI,EAAkB5G,MAAM,CAC9C,EAGb,EACJ,GACO/C,CACX,EAEM8C,EAAe,UACjB7W,GAAW,GACX,IAAM2B,EAAW,MAAMC,EAAAA,CAAUA,CAACC,GAAG,CAAC,SAAU6U,GAChD,GAAI/U,GAAYA,EAASG,IAAI,CAAE,CAC3B,IAAIiS,EAAYwJ,EAAa5b,EAASG,IAAI,EAC1ChC,EAAe+d,EAAc9J,EAAW5T,EAAS,IACjDD,EAAa6T,EAAUhS,MAAM,EAC7B/B,GAAW,EACf,CACJ,EACMuD,EAAmB,MAAO3C,IAC5BZ,EAAW,IACX,IAAM2B,EAAW,MAAMC,EAAAA,CAAUA,CAACC,GAAG,CAAC,SAAU6U,GAC5C/U,GAAYA,EAASG,IAAI,EAAIH,EAASG,IAAI,CAACC,MAAM,CAAG,GAAG,CAEvDjC,EAAe+d,EADCN,EAAa5b,EAASG,IAAI,EACF3B,EAAX4T,IAC7B/T,GAAW,GAEnB,EAEMiC,EAAsB,MAAOC,EAAoBtB,KACnDZ,GAAW,GACX,IAAM2B,EAAW,MAAMC,EAAAA,CAAUA,CAACC,GAAG,CAAC,SAAU6U,GAC5C/U,GAAYA,EAASG,IAAI,EAAIH,EAASG,IAAI,CAACC,MAAM,CAAG,GAAG,CAEvDjC,EAAe+d,EADCN,EAAa5b,EAASG,IAAI,EACFI,EAAX6R,IAC7B3T,EAAW8B,GACXlC,EAAW,IAEnB,EAEM6d,EAAgB,CAACC,EAAcC,EAAmBC,IAC7CF,EAAM1G,KAAK,CAAC,CAAC4G,GAAc,EAAKD,EAAWC,EAAcD,GAGpErb,CAAAA,EAAAA,EAAAA,SAAAA,CAASA,CAAC,KACNmU,GACJ,EAAG,EAAE,EAEL,IAAMpV,EAAa,MAAOJ,EAAQyV,KAC9BmH,QAAQC,GAAG,CAAC7c,EAAGyV,GACfxW,GAAS,GACTiW,EAAaO,GACTzV,GAAKA,EAAE5D,GAAG,EAAE,EAES,CAAE,GAAG4D,CAAC,CAAEyV,OADA,CACQC,WADnBD,EAAuB,WAAa,UACP,EAEvD,EAEM1U,EAAe,cAeb4U,EAMJ,GApBAR,EAAkB,cAAiB,CAAlB,CAAqB,EACtCA,EAAkB,MAAS,CAAG,QAAb,GAEbA,GACAA,EAAkB,eAAD,GAAsB,EACvCA,EAAkB,eAAD,GAAsB,CAACzU,MAAM,EAChD,EACoB,eAAD,GAAsB,CAACxC,GAAG,CAAE4e,IACrCA,EAAOP,aAAa,GAAKpH,EAAkB,aAAgB,EAAjB,CAC1C2H,EAAOrH,MAAM,CAAiB,YAAdR,EAA0B,WAAa,YAEpD6H,IAIE,WAAW,CAAzB7H,EACC,MAAM1U,EAAAA,CAAUA,CAACS,MAAM,CAAC,UAAmC,OAAzBmU,EAAkB,GAAM,GAE1DQ,EAAc,MAAMpV,CAF+B,CAE/BA,CAAUA,CAAC0E,KAAK,CAAC,UAAmC,OAAzBkQ,EAAkB,GAAM,EAAIA,GAE3EQ,GAAsC,IAF0B,EAEjDA,EAAYF,MAAM,CAAU,YAC3CxU,EAAAA,EAAKA,CAACE,KAAK,CACPwU,EAAYrV,QAAQ,EAAIqV,EAAYrV,QAAQ,CAACsV,OAAO,CAC9CD,EAAYrV,QAAQ,CAACsV,OAAO,CAC5BhZ,EAAE,8DAIZ4Y,IACkB,WAAW,CAAzBP,EACAhU,EAAAA,EAAKA,CAACC,OAAO,CAACtE,EAAE,oDAEhBqE,EAAAA,EAAKA,CAACE,KAAK,CAACvE,EAAE,mDAElBwY,EAAqB,CAAC,GACtBnW,GAAS,EAEjB,EAEMmC,EAAY,IAAMnC,GAAS,GAEjC,MACI,WAACY,MAAAA,WACG,WAACyB,EAAAA,CAAKA,CAAAA,CAACC,KAAMvC,EAAawC,OAAQJ,YAC9B,UAACE,EAAAA,CAAKA,CAACG,MAAM,EAACC,WAAW,aACrB,WAACJ,EAAAA,CAAKA,CAACK,KAAK,YACPsT,EAAUY,MAAM,CAAC,GAAGC,WAAW,GAAKb,EAAUc,KAAK,CAAC,GAAI,IACxDnZ,EAAE,mDAGX,WAAC0E,EAAAA,CAAKA,CAACM,IAAI,YACNhF,EAAE,0DAA0D,IAAEqY,EAAW,IACzErY,EAAE,sDAEP,WAAC0E,EAAAA,CAAKA,CAACO,MAAM,YACT,UAACC,EAAAA,CAAMA,CAAAA,CAACC,QAAQ,YAAY5B,QAASiB,WAChCxE,EAAE,kDAEP,UAACkF,EAAAA,CAAMA,CAAAA,CAACC,QAAQ,UAAU5B,QAASY,WAC9BnE,EAAE,qDAKf,UAACoF,EAAAA,CAAQA,CAAAA,CACLvC,QAASA,EACTgB,KAAMjC,EACNI,UAAWA,EACXqD,WAAW,EACXrB,oBAAqBA,EACrBsB,iBAAkBA,MAIlC,8KC5FA,MA1HyB,IACrB,GAAM,CAAEtF,CAAC,MAAE2K,CAAI,CAAE,CAAG1K,CAAAA,EAAAA,EAAAA,EAAAA,CAAcA,CAAC,CAyHxBwd,SAxHS9S,EAAKG,KAwHE2S,EAAC,CAxHK,EAAG9S,EAAKG,QAAQ,CAEjD,EAFoD,CAE9C,CAAClJ,EAAWC,EAAe,CAAGC,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAC,EAAE,EACzC,EAAGC,EAAW,CAAGD,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,EAAC,GAC1B,CAACE,EAAWC,EAAa,CAAGH,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAC,GACrC,CAACI,EAASC,EAAW,CAAGL,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAC,IACjC,CAACM,EAAaC,EAAS,CAAGP,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,EAAC,GACnC,CAACqe,EAAmBC,EAAqB,CAAGte,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAC,CAAC,GACtD0C,EAAY,IAAMnC,GAAS,GAE3BQ,EAAU,CACZ,CACIC,KAAM9C,EAAE,oCACR+C,SAAU,QACVC,KAAM,GAAYI,EAAE3D,KAAK,EAE7B,CACIqD,KAAM9C,EAAE,sCACR+C,SAAU,YACVC,KAAM,GAAaI,EAAEid,SAAS,CAAG,MAAQ,IAC7C,EACA,CACIvd,KAAM9C,EAAE,qCACR+C,SAAU,GACVC,KAAM,GACF,WAACC,MAAAA,WACG,UAACC,IAAIA,CAACC,KAAK,6BAA6BnC,GAAI,OAAvCkC,wBAA4E,OAANE,EAAE5D,GAAG,WAE5E,UAAC6D,IAAAA,CAAEjD,UAAU,uBAEV,OAEN,MAGb,EACH,CACKkgB,EAAoB,CACtB7d,KAAM,CAAEhD,MAAO,KAAM,EACrBiD,MAAOR,EACPS,KAAM,CACV,EAEA8B,CAAAA,EAAAA,EAAAA,SAAAA,CAASA,CAAC,KACN8b,GACJ,EAAG,EAAE,EAEL,IAAMA,EAAqB,UACvBxe,GAAW,GACX,IAAM2B,EAAW,MAAMC,EAAAA,CAAUA,CAACC,GAAG,CAAC,eAAgB0c,GAClD5c,IACA7B,EAAe6B,EAASG,EADd,EACkB,EAC5B5B,EAAayB,EAASK,UAAU,EAChChC,GAAW,GAEnB,EAQMiC,EAAsB,MAAOC,EAAiBtB,KAChD2d,EAAkB5d,KAAK,CAAGuB,EAC1Bqc,EAAkB3d,IAAI,CAAGA,EACzBZ,GAAW,GACX,IAAM2B,EAAW,MAAMC,EAAAA,CAAUA,CAACC,GAAG,CAAC,eAAgB0c,GAClD5c,GAAYA,EAASG,IAAI,EAAIH,EAASG,IAAI,CAACC,MAAM,CAAG,GAAG,CACvDjC,EAAe6B,EAASG,IAAI,EAC5B1B,EAAW8B,GACXlC,GAAW,GAEnB,EAEMoC,EAAe,UACjB,GAAI,CACA,MAAMR,EAAAA,CAAUA,CAACS,MAAM,CAAC,gBAAkC,OAAlB+b,IACxCI,IACAle,GAAS,GACTgC,EAAAA,EAAKA,CAACC,OAAO,CAACtE,EAAE,yDACpB,CAAE,MAAOuE,EAAO,CACZF,EAAAA,EAAKA,CAACE,KAAK,CAACvE,EAAE,mDAClB,CACJ,EAOA,MACI,WAACiD,MAAAA,WACG,WAACyB,EAAAA,CAAKA,CAAAA,CAACC,KAAMvC,EAAawC,OAAQJ,YAC9B,UAACE,EAAAA,CAAKA,CAACG,MAAM,EAACC,WAAW,aACrB,UAACJ,EAAAA,CAAKA,CAACK,KAAK,WAAE/E,EAAE,wDAEpB,UAAC0E,EAAAA,CAAKA,CAACM,IAAI,WAAEhF,EAAE,2EACf,WAAC0E,EAAAA,CAAKA,CAACO,MAAM,YACT,UAACC,EAAAA,CAAMA,CAAAA,CAACC,QAAQ,YAAY5B,QAASiB,WAChCxE,EAAE,uCAEP,UAACkF,EAAAA,CAAMA,CAAAA,CAACC,QAAQ,UAAU5B,QAASY,WAC9BnE,EAAE,0CAKf,UAACoF,EAAAA,CAAQA,CAAAA,CACLvC,QAASA,EACTgB,KAAMjC,EACNI,UAAWA,EACXqD,WAAW,EACXrB,oBAAqBA,EACrBsB,iBAzDc3C,CAyDI2C,GAxD1Bgb,EAAkB5d,KAAK,CAAGR,EAC1Boe,EAAkB3d,IAAI,CAAGA,EACzB4d,GACJ,MAyDJ,yPCHA,MA/GiB,IAEf,IAAMnG,EAAe,CACnB3a,MAAO,EACT,EAEM,CAAC+H,EAAYC,EAAc,CAAG3F,CAAAA,EAAAA,EAAAA,IAyGf0a,EAAC,EAzGc1a,CAAQA,CAAOsY,GAC3CzS,EAAWL,EAAMM,MAAM,EAAwB,cAApBN,EAAMM,MAAM,CAAC,EAAE,EAAoBN,EAAMM,MAAM,CAAC,EAAE,CAC/E,GAAE5H,CAAC,CAAE,CAAGC,CAAAA,EAAAA,EAAAA,EAAAA,CAAcA,CAAC,UAGvB4H,EAAUC,CAAAA,EAAAA,EAAAA,MAAAA,CAAMA,CAAC,MAkBjBC,EAAe,MAAOC,EAAYgG,SAQlCtK,EAPAsE,GAAOA,EAAME,cAAc,GAG/B,IAAMC,EAAM,CACV1I,MAAO4a,CAFUrM,GAAUxG,CAAAA,EAET/H,KAAK,CAAC2I,IAAI,EAC9B,CAQI1E,EAJFA,EADEiE,EACS,MAAMhE,EADL,CACeA,CAAC0E,KAAK,CAAC,UAA0B,OAAhBf,EAAMM,MAAM,CAAC,EAAE,EAAIO,GAEpD,MAAMxE,EAAAA,CAAUA,CAAC2E,IAAI,CAAC,SAAUH,KAE7BzE,EAASlE,GAAG,EAAE,EAC5B6E,EAAKA,CAACC,OAAO,CAACtE,EAAE,4BAChBuI,IAAAA,IAAW,CAAC,wBAEZlE,EAAAA,EAAKA,CAACE,KAAK,CAACb,EAEhB,EAiBA,MAfAe,CAAAA,EAAAA,EAAAA,SAAAA,CAASA,CAAC,KACR,IAAM6V,EAAa,CACjB1X,MAAO,CAAC,EACRH,KAAM,CAAEhD,MAAO,KAAM,EACrBiD,MAAO,GACT,EACIiF,GAKFyV,CAJoB,MADR,IAEV,IAAM1Z,EAAiB,MAAMC,EAAAA,CAAUA,CAACC,GAAG,CAAC,UAA0B,OAAhB0D,EAAMM,MAAM,CAAC,EAAE,EAAI0S,GACzE7S,EAAc,GAAgB,EAAE,GAAGkB,CAAS,CAAE,EAAhB,CAAmBjF,CAAQ,CAAC,GAC5D,GAGJ,EAAG,EAAE,EAGH,UAACT,MAAAA,UACC,UAAC/C,EAAAA,CAASA,CAAAA,CAACE,UAAU,WAAWD,KAAK,aACnC,UAACyI,EAAAA,CAAIA,CAAAA,CAAC3C,MAAO,CAAE4C,UAAW,MAAOC,UAAW,kEAAmE,WAC7G,UAACC,EAAAA,CAAqBA,CAAAA,CAACC,SAAUjB,EAAckB,IAAKpB,EAASqB,cAAe1B,EAAY2B,oBAAoB,WAC1G,WAACP,EAAAA,CAAIA,CAAC5D,IAAI,YACR,UAAC3E,EAAAA,CAAGA,CAAAA,UACF,UAACC,EAAAA,CAAGA,CAAAA,UACF,UAACsI,EAAAA,CAAIA,CAAC7D,KAAK,WAAE/E,EAAE,cAGnB,UAACoJ,KAAAA,CAAAA,GACD,UAAC/I,EAAAA,CAAGA,CAAAA,UACF,UAACC,EAAAA,CAAGA,CAAAA,CAACE,EAAE,IAACY,GAAI,EAAGD,GAAI,YACjB,WAACkI,EAAAA,CAAIA,CAACC,KAAK,YACT,UAACD,EAAAA,CAAIA,CAACE,KAAK,EAACnJ,UAAU,0BAAkBJ,EAAE,UAC1C,UAACwJ,EAAAA,EAASA,CAAAA,CACR1G,KAAK,QACL2G,GAAG,QACHC,QAAQ,IAAC5I,MAAO0G,EAAW/H,KAAK,CAChCkK,UAAY,GAAkD,KAA/BC,OAAO9I,GAAS,IAAIsH,IAAI,GACvDyB,aAAc,CACZF,UAAW3J,EAAE,mBAAmB,EAClCe,SAtEEM,CAsEQyI,GArE5B,GAAIzI,EAAE0I,MAAM,CAAE,CACZ,GAAM,MAAEjH,CAAI,OAAEhC,CAAK,CAAE,CAAGO,EAAE0I,MAAM,CAChCtC,EAAckB,GAAc,EAC1B,GAAGA,CAAS,CACZ,CAAC7F,CAFyB,CAEpB,CAAEhC,EACV,EACF,CACF,WAmEY,UAACT,EAAAA,CAAGA,CAAAA,CAACD,UAAU,gBACb,WAACE,EAAAA,CAAGA,CAAAA,WACF,UAAC4E,EAAAA,CAAMA,CAAAA,CAAC9E,UAAU,OAAOO,KAAK,SAASwE,QAAQ,mBAAWnF,EAAE,YAC5D,UAACkF,EAAAA,CAAMA,CAAAA,CAAC9E,UAAU,OAAOmD,QApFpB,CAoF6ByG,IAnFhDvC,EAAc2S,GAEdnQ,OAAOC,QAAQ,CAAC,EAAG,EACrB,EAgFgE/E,QAAQ,gBAAQnF,EAAE,WAClE,UAACkD,IAAIA,CACHC,KAAK,6BACLnC,GAAK,OAFFkC,wBAGF,UAACgC,EAAAA,CAAMA,CAAAA,CAACC,QAAQ,qBAAanF,EAAE,6BASpD", "sources": ["webpack://_N_E/./pages/adminsettings/content/ContentTableFilter.tsx", "webpack://_N_E/./pages/adminsettings/hazard/hazardTableFilter.tsx", "webpack://_N_E/./pages/adminsettings/projectstatuses/projectstatusTable.tsx", "webpack://_N_E/./pages/adminsettings/worldregion/worldregionTable.tsx", "webpack://_N_E/./pages/adminsettings/worldregion/index.tsx", "webpack://_N_E/./pages/adminsettings/country/index.tsx", "webpack://_N_E/./pages/adminsettings/operationstatuses/index.tsx", "webpack://_N_E/./pages/adminsettings/operationstatuses/form.tsx", "webpack://_N_E/./pages/adminsettings/updateType/index.tsx", "webpack://_N_E/./pages/adminsettings/areaOfWork/forms.tsx", "webpack://_N_E/./pages/adminsettings/hazard/hazardTable.tsx", "webpack://_N_E/./pages/adminsettings/syndrome/index.tsx", "webpack://_N_E/./pages/adminsettings/worldregion/form.tsx", "webpack://_N_E/./pages/adminsettings/approval/institution_approval.tsx", "webpack://_N_E/?5ea3", "webpack://_N_E/./pages/adminsettings/deploymentstatus/form.tsx", "webpack://_N_E/./pages/adminsettings/syndrome/form.tsx", "webpack://_N_E/./pages/adminsettings/hazardtypes/hazardTypeTable.tsx", "webpack://_N_E/./pages/adminsettings/institutiontypes/form.tsx", "webpack://_N_E/./pages/adminsettings/hazardtypes/forms.tsx", "webpack://_N_E/./pages/adminsettings/eventstatuses/index.tsx", "webpack://_N_E/./pages/adminsettings/institutiontypes/index.tsx", "webpack://_N_E/./pages/adminsettings/country/countryTable.tsx", "webpack://_N_E/./pages/adminsettings/institutionNetworks/institutionNetworkTable.tsx", "webpack://_N_E/./pages/adminsettings/deploymentstatus/index.tsx", "webpack://_N_E/./pages/adminsettings/updateType/forms.tsx", "webpack://_N_E/./pages/adminsettings/updateType/updateTypeTable.tsx", "webpack://_N_E/./pages/adminsettings/approval/focal_point_appoval.tsx", "webpack://_N_E/./pages/adminsettings/categories/categoryTable.tsx", "webpack://_N_E/./pages/adminsettings/areaOfWork/index.tsx", "webpack://_N_E/./pages/adminsettings/eventstatuses/eventstatusTable.tsx", "webpack://_N_E/./pages/adminsettings/risklevel/index.tsx", "webpack://_N_E/./pages/adminsettings/roles/index.tsx", "webpack://_N_E/./pages/adminsettings/institutiontypes/institutionTypeTable.tsx", "webpack://_N_E/./pages/adminsettings/hazardtypes/index.tsx", "webpack://_N_E/./pages/adminsettings/categories/form.tsx", "webpack://_N_E/./pages/adminsettings/approval/vspace_appoval.tsx", "webpack://_N_E/./pages/adminsettings/region/form.tsx", "webpack://_N_E/./pages/adminsettings/projectstatuses/form.tsx", "webpack://_N_E/./pages/adminsettings/eventstatuses/form.tsx", "webpack://_N_E/./pages/adminsettings/content/index.tsx", "webpack://_N_E/./pages/adminsettings/deploymentstatus/deploymentstatusTable.tsx", "webpack://_N_E/./pages/adminsettings/expertise/index.tsx", "webpack://_N_E/./pages/adminsettings/approval/VspaceAdmin.tsx", "webpack://_N_E/./pages/adminsettings/projectstatuses/index.tsx", "webpack://_N_E/./pages/adminsettings/approval/InstitutionTable.tsx", "webpack://_N_E/./pages/adminsettings/region/regionTable.tsx", "webpack://_N_E/./pages/adminsettings/mailsettings/form.tsx", "webpack://_N_E/./pages/adminsettings/institutionNetworks/index.tsx", "webpack://_N_E/./pages/adminsettings/risklevel/form.tsx", "webpack://_N_E/./pages/adminsettings/hazard/index.tsx", "webpack://_N_E/./pages/adminsettings/[...routes].tsx", "webpack://_N_E/./pages/adminsettings/risklevel/risklevelTable.tsx", "webpack://_N_E/./pages/adminsettings/roles/roleTable.tsx", "webpack://_N_E/./pages/adminsettings/expertise/form.tsx", "webpack://_N_E/./pages/adminsettings/landingPage/index.tsx", "webpack://_N_E/./pages/adminsettings/expertise/expertiseTable.tsx", "webpack://_N_E/./pages/adminsettings/syndrome/syndromeTable.tsx", "webpack://_N_E/./pages/adminsettings/categories/index.tsx", "webpack://_N_E/./pages/adminsettings/country/countryTableFilter.tsx", "webpack://_N_E/./pages/adminsettings/region/index.tsx", "webpack://_N_E/./pages/adminsettings/operationstatuses/operationstatusTable.tsx", "webpack://_N_E/./pages/adminsettings/institutionNetworks/form.tsx", "webpack://_N_E/./pages/adminsettings/region/regionTableFilter.tsx", "webpack://_N_E/./pages/adminsettings/areaOfWork/areaOfWorkTable.tsx", "webpack://_N_E/./pages/adminsettings/approval/AdminTable.tsx", "webpack://_N_E/./pages/adminsettings/landingPage/landingPageTable.tsx", "webpack://_N_E/./pages/adminsettings/roles/form.tsx"], "sourcesContent": ["//Import Library\r\nimport {useEffect, useState} from \"react\";\r\nimport {Col, Container, FormControl, FormGroup, FormLabel, Row} from \"react-bootstrap\";\r\n\r\n//Import services/components\r\nimport { useTranslation } from 'next-i18next';\r\n\r\ninterface ContentTableFilterProps {\r\n  filterText: string;\r\n  onFilter: (e: React.ChangeEvent<HTMLInputElement>) => void;\r\n  onFilterTypeChange: (e: React.ChangeEvent<HTMLSelectElement>) => void;\r\n  onClear: () => void;\r\n  filterType: string;\r\n}\r\nconst types = [\r\n  {\r\n    _id: \"operation\",\r\n    title: \"Operations\"\r\n  },\r\n  {\r\n    _id: \"institution\",\r\n    title: \"Organisations\"\r\n  },\r\n  {\r\n    _id: \"event\",\r\n    title: \"Events\"\r\n  },\r\n  {\r\n    _id: \"project\",\r\n    title: \"Projects\"\r\n  },\r\n  {\r\n    _id: \"updates\",\r\n    title: \"Updates\"\r\n  },\r\n  {\r\n    _id: \"vspace\",\r\n    title: \"Virtual Spaces\"\r\n  }\r\n];\r\n\r\n\r\nconst ContentTableFilter = ({filterText, onFilter, onFilterTypeChange, onClear, filterType }: ContentTableFilterProps) => {\r\n  const { t } = useTranslation('common');\r\n  return (\r\n    <Container fluid className=\"p-0\">\r\n      <Row>\r\n        <Col xs={6} md={4} className=\"ps-0 align-self-end\" >\r\n          <FormGroup>\r\n          <FormControl\r\n            type=\"text\"\r\n            className=\"searchInput\"\r\n            placeholder={t(\"adminsetting.content.table.Search\")}\r\n            aria-label=\"Search\"\r\n            value={filterText}\r\n            onChange={onFilter}\r\n          />\r\n          </FormGroup>\r\n        </Col>\r\n        <Col xs={6} md={4}>\r\n          <FormGroup as={Row}>\r\n            <FormLabel column sm={3} lg={2} className=\"me-2\">{t('adminsetting.content.table.Type')}</FormLabel>\r\n            <Col className=\"ps-md-0\">\r\n              <FormControl\r\n                as=\"select\"\r\n                aria-label=\"Type\"\r\n                onChange={(e) => onFilterTypeChange(e as unknown as React.ChangeEvent<HTMLSelectElement>)}\r\n                value={filterType}>\r\n                {types.map((item, index) => {\r\n                  return (\r\n                    <option key={index} value={item._id}>{item.title}</option>\r\n                  )\r\n                })}\r\n              </FormControl>\r\n            </Col>\r\n          </FormGroup>\r\n        </Col>\r\n      </Row>\r\n    </Container>\r\n  )\r\n};\r\n\r\nexport default ContentTableFilter;\r\n", "//Import Library\r\nimport {Col, Container, FormControl, Row} from \"react-bootstrap\";\r\n\r\n//Import services/components\r\nimport { useTranslation } from 'next-i18next';\r\n\r\nconst HazardTableFilter = ({ filterText, onFilter ,onClear}: any) => {\r\n  const { t } = useTranslation('common');\r\n\r\n\r\n  return (\r\n    <Container fluid className=\"p-0\">\r\n      <Row>\r\n        <Col md={4} className=\"p-0\">\r\n          <FormControl\r\n            type=\"text\"\r\n            className=\"searchInput\"\r\n            placeholder={t(\"adminsetting.hazard.Search\")}\r\n            aria-label=\"Search\"\r\n            value={filterText}\r\n            onChange={onFilter}\r\n          />\r\n        </Col>\r\n      </Row>\r\n    </Container>\r\n  )\r\n};\r\n\r\nexport default HazardTableFilter;\r\n", "//Import Library\r\nimport { useState, useEffect } from \"react\";\r\nimport Link from \"next/link\";\r\nimport { <PERSON><PERSON>, But<PERSON> } from \"react-bootstrap\";\r\n\r\nimport toast from 'react-hot-toast';\r\n\r\n//Import services/components\r\nimport RKITable from \"../../../components/common/RKITable\";\r\nimport apiService from \"../../../services/apiService\";\r\nimport { useTranslation } from 'next-i18next';\r\n\r\nconst ProjectstatusTable = (_props: any) => {\r\n    const { t } = useTranslation('common');\r\n    const [tabledata, setDataToTable] = useState([]);\r\n    const [, setLoading] = useState(false);\r\n    const [totalRows, setTotalRows] = useState(0);\r\n    const [perPage, setPerPage] = useState(10);\r\n    const [isModalShow, setModal] = useState(false);\r\n    const [selectProjectstatus, setSelectProjectstatus] = useState({});\r\n    \r\n    const projectstatusParams = {\r\n        sort: { title: \"asc\" },\r\n        limit: perPage,\r\n        page: 1,\r\n        query: {},\r\n    };\r\n\r\n    const columns = [\r\n        {\r\n            name: t(\"adminsetting.ProjectStatus.Title\"),\r\n            selector: \"title\",\r\n        },\r\n        {\r\n            name: t(\"adminsetting.ProjectStatus.Action\"),\r\n            selector: \"\",\r\n            cell: (d: any) => (\r\n                <div>\r\n                    <Link href=\"/adminsettings/[...routes]\" as={`/adminsettings/edit_projectstatus/${d._id}`}>\r\n\r\n                        <i className=\"icon fas fa-edit\" />\r\n\r\n                    </Link>\r\n                    &nbsp;\r\n                    <a onClick={() => userAction(d)}>\r\n                        <i className=\"icon fas fa-trash-alt\" />\r\n                    </a>{\" \"}\r\n                </div>\r\n            ),\r\n        },\r\n    ];\r\n\r\n    const getProjectstatusData = async () => {\r\n        setLoading(true);\r\n        const response = await apiService.get(\"/projectstatus\", projectstatusParams);\r\n        if (response && response.data && response.data.length > 0) {\r\n            setDataToTable(response.data);\r\n            setTotalRows(response.totalCount);\r\n            setLoading(false);\r\n        }\r\n    };\r\n\r\n    const handlePageChange = (page: any) => {\r\n        projectstatusParams.limit = perPage;\r\n        projectstatusParams.page = page;\r\n        getProjectstatusData();\r\n    };\r\n\r\n    const handlePerRowsChange = async (newPerPage: any, page: any) => {\r\n        projectstatusParams.limit = newPerPage;\r\n        projectstatusParams.page = page;\r\n        setLoading(true);\r\n        const response = await apiService.get(\"/projectstatus\", projectstatusParams);\r\n        if (response && response.data && response.data.length > 0) {\r\n            setDataToTable(response.data);\r\n            setPerPage(newPerPage);\r\n            setLoading(false);\r\n        }\r\n    };\r\n\r\n    const userAction = async (row: any) => {\r\n        setSelectProjectstatus(row._id);\r\n        setModal(true);\r\n    };\r\n\r\n    const modalConfirm = async () => {\r\n        try {\r\n            await apiService.remove(`/projectstatus/${selectProjectstatus}`);\r\n            getProjectstatusData();\r\n            setModal(false);\r\n            toast.success(t(\"adminsetting.ProjectStatus.Table.projectStatusDeletedSuccessfully\"));\r\n        } catch (error) {\r\n            toast.error(t(\"adminsetting.ProjectStatus.Table.errorDeletingProjectStatus\"));\r\n        }\r\n    };\r\n\r\n    const modalHide = () => setModal(false);\r\n\r\n    useEffect(() => {\r\n        getProjectstatusData();\r\n    }, []);\r\n\r\n    return (\r\n        <div>\r\n            <Modal show={isModalShow} onHide={modalHide}>\r\n                <Modal.Header closeButton>\r\n                    <Modal.Title>{t(\"adminsetting.ProjectStatus.DeleteProjectstatus\")}</Modal.Title>\r\n                </Modal.Header>\r\n                <Modal.Body>{t(\"adminsetting.ProjectStatus.Areyousurewanttodeletethisprojectstatus\")}</Modal.Body>\r\n                <Modal.Footer>\r\n                    <Button variant=\"secondary\" onClick={modalHide}>\r\n                        {t(\"adminsetting.ProjectStatus.Cancel\")}\r\n                    </Button>\r\n                    <Button variant=\"primary\" onClick={modalConfirm}>\r\n                        {t(\"adminsetting.ProjectStatus.Yes\")}\r\n                    </Button>\r\n                </Modal.Footer>\r\n            </Modal>\r\n\r\n            <RKITable\r\n                columns={columns}\r\n                data={tabledata}\r\n                totalRows={totalRows}\r\n                pagServer={true}\r\n                handlePerRowsChange={handlePerRowsChange}\r\n                handlePageChange={handlePageChange}\r\n            />\r\n        </div>\r\n    );\r\n};\r\n\r\nexport default ProjectstatusTable;\r\n", "//Import Library\r\nimport { useState, useEffect } from \"react\";\r\nimport Link from \"next/link\";\r\nimport { <PERSON><PERSON>, Button } from \"react-bootstrap\";\r\n\r\nimport toast from 'react-hot-toast';\r\n\r\n//Import services/components\r\nimport RKITable from \"../../../components/common/RKITable\";\r\nimport apiService from \"../../../services/apiService\";\r\nimport { useTranslation } from 'next-i18next';\r\nconst WorldregionTable = (_props: any) => {\r\n    const { t } = useTranslation('common');\r\n    const [tabledata, setDataToTable] = useState([]);\r\n    const [, setLoading] = useState(false);\r\n    const [totalRows, setTotalRows] = useState(0);\r\n    const [perPage, setPerPage] = useState(10);\r\n    const [isModalShow, setModal] = useState(false);\r\n    const [selectWorldregion, setSelectWorldregion] = useState({});\r\n\r\n    const worldregionParams = {\r\n        sort: { title: \"asc\" },\r\n        limit: perPage,\r\n        page: 1,\r\n        query: {},\r\n    };\r\n\r\n    const columns = [\r\n        {\r\n            name: t(\"adminsetting.worldregion.table.Title\"),\r\n            selector: (row: any) => row.title,\r\n            sortable: true,\r\n        },\r\n        {\r\n            name: t(\"adminsetting.worldregion.table.Code\"),\r\n            selector: (row: any) => row.code,\r\n            sortable: true,\r\n            cell: (d: any) => d.code,\r\n        },\r\n        {\r\n            name: t(\"adminsetting.worldregion.table.Action\"),\r\n            selector: (row: any) => row._id,\r\n            sortable: false,\r\n            cell: (d: any) => (\r\n                <div>\r\n                    <Link href=\"/adminsettings/[...routes]\" as={`/adminsettings/edit_worldregion/${d._id}`}>\r\n\r\n                        <i className=\"icon fas fa-edit\" />\r\n\r\n                    </Link>\r\n                    &nbsp;\r\n                    <a onClick={() => userAction(d)}>\r\n                        <i className=\"icon fas fa-trash-alt\" />\r\n                    </a>{\" \"}\r\n                </div>\r\n            ),\r\n        },\r\n    ];\r\n\r\n    const getWorldregionData = async () => {\r\n        setLoading(true);\r\n        const response = await apiService.get(\"/worldregion\", worldregionParams);\r\n        if (response && response.data && response.data.length > 0) {\r\n            setDataToTable(response.data);\r\n            setTotalRows(response.totalCount);\r\n            setLoading(false);\r\n        }\r\n    };\r\n    const handlePageChange = (page: any) => {\r\n        worldregionParams.limit = perPage;\r\n        worldregionParams.page = page;\r\n        getWorldregionData();\r\n    };\r\n\r\n    const handlePerRowsChange = async (newPerPage: any, page: any) => {\r\n        worldregionParams.limit = newPerPage;\r\n        worldregionParams.page = page;\r\n        setLoading(true);\r\n        const response = await apiService.get(\"/worldregion\", worldregionParams);\r\n        if (response && response.data && response.data.length > 0) {\r\n            setDataToTable(response.data);\r\n            setPerPage(newPerPage);\r\n            setLoading(false);\r\n        }\r\n    };\r\n\r\n    const userAction = async (row: any) => {\r\n        setSelectWorldregion(row._id);\r\n        setModal(true);\r\n    };\r\n\r\n    const modalConfirm = async () => {\r\n        try {\r\n            await apiService.remove(`/worldregion/${selectWorldregion}`);\r\n            getWorldregionData();\r\n            setModal(false);\r\n            toast.success(t(\"adminsetting.worldregion.table.worldRegionDeletedSuccessfully\"));\r\n        } catch (error) {\r\n            toast.error(t(\"adminsetting.worldregion.table.errorDeletingWorldRegion\"));\r\n        }\r\n    };\r\n\r\n    const modalHide = () => setModal(false);\r\n\r\n    useEffect(() => {\r\n        getWorldregionData();\r\n    }, []);\r\n\r\n    return (\r\n        <div>\r\n            <Modal show={isModalShow} onHide={modalHide}>\r\n                <Modal.Header closeButton>\r\n                    <Modal.Title>{t(\"adminsetting.worldregion.table.DeleteWorldregion\")}</Modal.Title>\r\n                </Modal.Header>\r\n                <Modal.Body>{t(\"adminsetting.worldregion.table.Areyousurewanttodeletethisworldregion?\")}</Modal.Body>\r\n                <Modal.Footer>\r\n                    <Button variant=\"secondary\" onClick={modalHide}>\r\n                        {t(\"adminsetting.worldregion.table.Cancel\")}\r\n                    </Button>\r\n                    <Button variant=\"primary\" onClick={modalConfirm}>\r\n                        {t(\"adminsetting.worldregion.table.Yes\")}\r\n                    </Button>\r\n                </Modal.Footer>\r\n            </Modal>\r\n\r\n            <RKITable\r\n                columns={columns}\r\n                data={tabledata}\r\n                totalRows={totalRows}\r\n                pagServer={true}\r\n                handlePerRowsChange={handlePerRowsChange}\r\n                handlePageChange={handlePageChange}\r\n            />\r\n        </div>\r\n    );\r\n};\r\n\r\nexport default WorldregionTable;\r\n", "//Import Library\r\nimport { Contain<PERSON>, <PERSON>, <PERSON>, Button } from \"react-bootstrap\";\r\nimport Link from \"next/link\";\r\n\r\n//Import services/components\r\nimport PageHeading from \"../../../components/common/PageHeading\";\r\nimport WorldregionTable from \"./worldregionTable\";\r\nimport { useTranslation } from 'next-i18next';\r\nimport { serverSideTranslations } from 'next-i18next/serverSideTranslations';\r\nimport { canAddWorldRegion } from \"../permissions\";\r\nimport { useSelector } from \"react-redux\";\r\nimport NoAccessMessage from \"../../rNoAccess\";\r\nconst WorldregionIndex = (_props: any) => {\r\n  const { t } = useTranslation('common');\r\n  const ShowWorldregionIndex = () => {\r\n    return (\r\n      <div>\r\n        <Container style={{ overflowX: \"hidden\" }} fluid className=\"p-0\">\r\n          <Row>\r\n            <Col xs={12}>\r\n              <PageHeading title={t(\"adminsetting.worldregion.form.WorldRegion\")}/>\r\n            </Col>\r\n          </Row>\r\n          <Row>\r\n            <Col xs={12}>\r\n              <Link\r\n                href=\"/adminsettings/[...routes]\"\r\n                as=\"/adminsettings/create_worldregion\"\r\n                >\r\n                <Button variant=\"secondary\" size=\"sm\">\r\n                {t(\"adminsetting.worldregion.form.Addworldregion\")}\r\n              </Button>\r\n              </Link>\r\n            </Col>\r\n          </Row>\r\n          <Row className=\"mt-3\">\r\n            <Col xs={12}>\r\n              < WorldregionTable />\r\n            </Col>\r\n          </Row>\r\n        </Container>\r\n      </div>\r\n    );\r\n  }\r\n  const ShowAddWorldRegion = canAddWorldRegion(() => <ShowWorldregionIndex />);\r\n  const state:any = useSelector((state) => state);\r\n  if (!(state?.permissions?.worl_region?.['create:any'])) {\r\n    return <NoAccessMessage />\r\n  }\r\n  return(\r\n    <ShowAddWorldRegion />\r\n  );\r\n}\r\n\r\nexport async function getServerSideProps({ locale } : { locale: string }) {\r\n  return {\r\n    props: {\r\n      ...(await serverSideTranslations(locale, ['common'])),\r\n    },\r\n  }\r\n}\r\n\r\nexport default WorldregionIndex;", "//Import Library\r\nimport { Container, <PERSON>, <PERSON>, Button } from \"react-bootstrap\";\r\nimport Link from \"next/link\";\r\n\r\n//Import services/components\r\nimport { useTranslation } from 'next-i18next';\r\nimport { serverSideTranslations } from 'next-i18next/serverSideTranslations';\r\nimport CountryTable from \"./countryTable\";\r\nimport PageHeading from \"../../../components/common/PageHeading\";\r\nimport { canAddCountry } from \"../permissions\";\r\nimport { useSelector } from \"react-redux\";\r\nimport NoAccessMessage from \"../../rNoAccess\";\r\n\r\nconst CountryIndex = (_props: any) => {\r\n  const { t } = useTranslation('common');\r\n  const ShowCountryIndex = () => {\r\n    return (\r\n      <Container style={{ overflowX: \"hidden\" }} fluid className=\"p-0\">\r\n        <Row>\r\n          <Col xs={12}>\r\n            <PageHeading title={t(\"adminsetting.Countries.Table.Countries\")} />\r\n          </Col>\r\n        </Row>\r\n        <Row>\r\n          <Col xs={12}>\r\n            <Link\r\n              href=\"/adminsettings/[...routes]\"\r\n              as=\"/adminsettings/create_country\"\r\n              >\r\n              <Button variant=\"secondary\" size=\"sm\">\r\n               {t(\"adminsetting.Countries.Table.AddCountry\")}\r\n              </Button>\r\n            </Link>\r\n          </Col>\r\n        </Row>\r\n        <Row className=\"mt-3\">\r\n          <Col xs={12}>\r\n            <CountryTable />\r\n          </Col>\r\n        </Row>\r\n      </Container>\r\n    );\r\n  }\r\n\r\n  const ShowAddCountry = canAddCountry(() => <ShowCountryIndex />);\r\n  const state:any = useSelector((state) => state);\r\n  if (!(state?.permissions?.country?.['create:any'])) {\r\n    return <NoAccessMessage />\r\n  }\r\n  return(\r\n    <ShowAddCountry />\r\n  )\r\n};\r\n\r\nexport async function getServerSideProps({ locale }: any) {\r\n  return {\r\n    props: {\r\n      ...(await serverSideTranslations(locale, ['common'])),\r\n    },\r\n  }\r\n}\r\n\r\nexport default CountryIndex;\r\n", "//Import Library\r\nimport { Container, <PERSON>, <PERSON>, Button } from \"react-bootstrap\";\r\nimport Link from \"next/link\";\r\n\r\n//Import services/components\r\nimport PageHeading from \"../../../components/common/PageHeading\";\r\nimport OperationstatusTable from \"./operationstatusTable\";\r\nimport { useTranslation } from 'next-i18next';\r\nimport { canAddOperationStatus } from \"../permissions\";\r\nimport { useSelector } from \"react-redux\";\r\nimport NoAccessMessage from \"../../rNoAccess\";\r\n\r\n\r\nconst OperationstatusIndex = (_props: any) => {\r\n  const { t } = useTranslation('common');\r\n  const ShowOperationstatusIndex = () => {\r\n    return (\r\n      <div>\r\n        <Container style={{ overflowX: \"hidden\" }} fluid className=\"p-0\">\r\n          <Row>\r\n            <Col xs={12}>\r\n              <PageHeading title= {t(\"adminsetting.OperationStatus.Operationstatus\")} />\r\n            </Col>\r\n          </Row>\r\n          <Row>\r\n            <Col xs={12}>\r\n              <Link\r\n                href=\"/adminsettings/[...routes]\"\r\n                as=\"/adminsettings/create_operationstatus\"\r\n                >\r\n                <Button variant=\"secondary\" size=\"sm\">\r\n                {t(\"adminsetting.OperationStatus.AddOperationstatus\")}\r\n              </Button>\r\n              </Link>\r\n            </Col>\r\n          </Row>\r\n          <Row className=\"mt-3\">\r\n            <Col xs={12}>\r\n              <OperationstatusTable />\r\n            </Col>\r\n          </Row>\r\n        </Container>\r\n      </div>\r\n    );\r\n  }\r\n\r\n  const ShowAddOperationStatus = canAddOperationStatus(() => <ShowOperationstatusIndex />);\r\n  const state:any = useSelector((state) => state);\r\n  if (!(state?.permissions?.operation_status?.['create:any'])) {\r\n    return <NoAccessMessage />\r\n  }\r\n  return(\r\n    <ShowAddOperationStatus />\r\n  )  \r\n}\r\nexport default OperationstatusIndex;", "//Import Library\r\nimport { useState, useRef, useEffect } from \"react\";\r\nimport { Container, Card, Row, Col, Form, Button } from \"react-bootstrap\";\r\nimport { TextInput } from \"../../../components/common/FormValidation\";\r\nimport ValidationFormWrapper from \"../../../components/common/ValidationFormWrapper\";\r\nimport Router from \"next/router\";\r\nimport toast from 'react-hot-toast';\r\nimport Link from \"next/link\";\r\n\r\n//Import services/components\r\nimport { OperationStatus } from \"../../../types\";\r\nimport apiService from \"../../../services/apiService\";\r\nimport { useTranslation } from 'next-i18next';\r\n\r\ninterface OperationstatusFormProps {\r\n    [key: string]: any;\r\n}\r\n\r\nconst OperationstatusForm = (props: OperationstatusFormProps) => {\r\n    const _initialoperationstatus = {\r\n        title: \"\",\r\n    };\r\n\r\n    const [initialVal, setInitialVal] = useState<OperationStatus>(_initialoperationstatus);\r\n\r\n    const editform = props.routes && props.routes[0] === \"edit_operationstatus\" && props.routes[1];\r\n    const { t } = useTranslation('common');\r\n\r\n    const formRef = useRef(null);\r\n\r\n    const resetHandler = () => {\r\n        setInitialVal(_initialoperationstatus);\r\n        // Reset validation state (Formik handles this automatically)\r\n        window.scrollTo(0, 0);\r\n    };\r\n\r\n    const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement>) => {\r\n        if (e.target) {\r\n            const { name, value } = e.target;\r\n            setInitialVal((prevState) => ({\r\n                ...prevState,\r\n                [name]: value,\r\n            }));\r\n        }\r\n    };\r\n\r\n    const handleSubmit = async (event: any) => {\r\n        event.preventDefault();\r\n        const obj = {\r\n            title: initialVal.title.trim(),\r\n        };\r\n\r\n        let response;\r\n        let toastMsg;\r\n        if (editform) {\r\n            toastMsg = \"adminsetting.OperationStatus.updatesuccess\";\r\n            response = await apiService.patch(`/operation_status/${props.routes[1]}`, obj);\r\n        } else {\r\n            toastMsg = \"adminsetting.OperationStatus.add\";\r\n            response = await apiService.post(\"/operation_status\", obj);\r\n        }\r\n        if (response && response._id) {\r\n            toast.success(t(toastMsg));\r\n            Router.push(\"/adminsettings/operationstatus\");\r\n        } else {\r\n            if (response?.errorCode === 11000) {\r\n                toast.error(t(\"duplicatesNotAllowed\"));\r\n            } else {\r\n                toast.error(response);\r\n            }\r\n        }\r\n    };\r\n\r\n    useEffect(() => {\r\n        const operationstatusParams = {\r\n            query: {},\r\n            sort: { title: \"asc\" },\r\n            limit: \"~\",\r\n        };\r\n        if (editform) {\r\n            const getOperationstatusData = async () => {\r\n                const response: OperationStatus = await apiService.get(`/operation_status/${props.routes[1]}`, operationstatusParams);\r\n                setInitialVal((prevState) => ({ ...prevState, ...response }));\r\n            };\r\n            getOperationstatusData();\r\n        }\r\n    }, []);\r\n\r\n    return (\r\n        <div>\r\n            <Container className=\"formCard\" fluid>\r\n                <Card\r\n                    style={{\r\n                        marginTop: \"5px\",\r\n                        boxShadow: \"0 4px 8px 0 rgba(0, 0, 0, 0.2), 0 6px 20px 0 rgba(0, 0, 0, 0.19)\",\r\n                    }}\r\n                >\r\n                    <ValidationFormWrapper onSubmit={handleSubmit} ref={formRef} initialValues={initialVal} enableReinitialize={true}>\r\n                        <Card.Body>\r\n                            <Row>\r\n                                <Col>\r\n                                    <Card.Title>{t(\"OperationStatus\")}</Card.Title>\r\n                                </Col>\r\n                            </Row>\r\n                            <hr />\r\n                            <Row>\r\n                                <Col md lg={6} sm={12}>\r\n                                    <Form.Group>\r\n                                        <Form.Label className=\"required-field\">{t(\"OperationStatus\")}</Form.Label>\r\n                                        <TextInput\r\n                                            name=\"title\"\r\n                                            id=\"title\"\r\n                                            required\r\n                                            value={initialVal.title}\r\n                                            validator={(value: string) => String(value || '').trim() !== \"\"}\r\n                                            errorMessage={{\r\n                                                validator: t(\"adminsetting.OperationStatus.please\"),\r\n                                            }}\r\n                                            onChange={handleChange}\r\n                                        />\r\n                                    </Form.Group>\r\n                                </Col>\r\n                            </Row>\r\n                            <Row className=\"my-4\">\r\n                                <Col>\r\n                                    <Button className=\"me-2\" type=\"submit\" variant=\"primary\">\r\n                                        {t(\"submit\")}\r\n                                    </Button>\r\n                                    <Button className=\"me-2\" onClick={resetHandler} variant=\"info\">\r\n                                        {t(\"reset\")}\r\n                                    </Button>\r\n                                    <Link\r\n                                        href=\"/adminsettings/[...routes]\"\r\n                                        as={`/adminsettings/operationstatus`}\r\n                                        >\r\n                                        <Button variant=\"secondary\">{t(\"Cancel\")}</Button>\r\n                                    </Link>\r\n                                </Col>\r\n                            </Row>\r\n                        </Card.Body>\r\n                    </ValidationFormWrapper>\r\n                </Card>\r\n            </Container>\r\n        </div>\r\n    );\r\n};\r\nexport default OperationstatusForm;\r\n", "//Import Library\r\nimport { Container, <PERSON>, <PERSON>, Button } from \"react-bootstrap\";\r\nimport Link from \"next/link\";\r\n\r\n//Import services/components\r\nimport PageHeading from \"../../../components/common/PageHeading\";\r\nimport UpdateTypeTable from \"./updateTypeTable\";\r\nimport { useTranslation } from 'next-i18next';\r\nimport canAddAreaOfWork from \"../permissions\";\r\nconst UpdateTypeIndex = (_props: any) => {\r\n  const { t } = useTranslation('common');\r\n  const ShowUpdateTypeIndex = () => {\r\n    return (\r\n      <Container style={{ overflowX: \"hidden\" }} fluid className=\"p-0\">\r\n        <Row>\r\n          <Col xs={12}>\r\n            <PageHeading title={t(\"adminsetting.updatestype.UpdateType\")} />\r\n          </Col>\r\n        </Row>\r\n        <Row>\r\n          <Col xs={12}>\r\n            <Link\r\n              href=\"/adminsettings/[...routes]\"\r\n              as=\"/adminsettings/create_update_type\"\r\n              >\r\n              <Button variant=\"secondary\" size=\"sm\">\r\n              {t(\"adminsetting.updatestype.AddUpdateType\")}\r\n              </Button>\r\n            </Link>\r\n          </Col>\r\n        </Row>\r\n        <Row className=\"mt-3\">\r\n          <Col xs={12}>\r\n            <UpdateTypeTable />\r\n          </Col>\r\n        </Row>\r\n      </Container>\r\n    );\r\n  }\r\n  \r\n  const ShowAddUpdateTypes = canAddAreaOfWork(() => <ShowUpdateTypeIndex />);\r\n  return(\r\n    <ShowAddUpdateTypes />\r\n  )\r\n}\r\nexport default UpdateTypeIndex;", "//Import Library\r\nimport { useState, useRef, useEffect } from \"react\";\r\nimport { Container, Card, Row, Col, Form, Button } from \"react-bootstrap\";\r\n// import { ValidationForm, TextInput } from \"../../../components/common/FormValidation\";\r\nimport ValidationFormWrapper from \"../../../components/common/ValidationFormWrapper\";\r\nimport { TextInput, SelectGroup } from \"../../../components/common/FormValidation\";\r\nimport Router from \"next/router\";\r\nimport toast from 'react-hot-toast';\r\nimport Link from \"next/link\";\r\n\r\n//Import services/components\r\nimport { AreaOfWork } from \"../../../types\";\r\nimport apiService from \"../../../services/apiService\";\r\nimport { useTranslation } from 'next-i18next';\r\n\r\ninterface AreaOfWorkFormProps {\r\n    routes: string[];\r\n}\r\n\r\nconst AreaOfWorkForm = (props: AreaOfWorkFormProps) => {\r\n    const { t } = useTranslation('common');\r\n\r\n    const _initialareaOfWork = {\r\n        _id: \"\",\r\n        title: \"\",\r\n    };\r\n\r\n    const [initialVal, setInitialVal] = useState<AreaOfWork>(_initialareaOfWork);\r\n\r\n    const editform: boolean = !!(props.routes && props.routes[0] === \"edit_area_of_work\" && props.routes[1]);\r\n\r\n    const formRef = useRef<any>(null);\r\n\r\n    const resetHandler = () => {\r\n        setInitialVal(_initialareaOfWork);\r\n        // Reset validation state (Formik handles this automatically)\r\n        window.scrollTo(0, 0);\r\n    };\r\n\r\n    const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {\r\n        if (e.target) {\r\n            const { name, value } = e.target;\r\n            setInitialVal((prevState) => ({\r\n                ...prevState,\r\n                [name]: value,\r\n            }));\r\n        }\r\n    };\r\n\r\n    const handleSubmit = async (event: any) => {\r\n        event.preventDefault();\r\n        const obj = {\r\n            title: initialVal.title.trim(),\r\n        };\r\n\r\n        let response;\r\n        let toastMsg;\r\n        if (editform) {\r\n            toastMsg = \"Areaofworkisupdatedsuccessfully\";\r\n            response = await apiService.patch(`/areaofwork/${props.routes[1]}`, obj);\r\n        } else {\r\n            toastMsg = \"Areaofworkisaddedsuccessfully\";\r\n            response = await apiService.post(\"/areaofwork\", obj);\r\n        }\r\n        if (response && response._id) {\r\n            toast.success(t(toastMsg));\r\n            Router.push(\"/adminsettings/area_of_work\");\r\n        } else {\r\n            if (response?.errorCode === 11000) {\r\n                toast.error(t(\"duplicatesNotAllowed\"));\r\n            } else {\r\n                toast.error(response);\r\n            }\r\n        }\r\n    };\r\n\r\n    useEffect(() => {\r\n        const areaOfWorkParams = {\r\n            query: {},\r\n            sort: { title: \"asc\" },\r\n            limit: \"~\",\r\n        };\r\n        if (editform) {\r\n            const getAreaOfWorkData = async () => {\r\n                const response: AreaOfWork = await apiService.get(`/areaofwork/${props.routes[1]}`, areaOfWorkParams);\r\n                setInitialVal((prevState) => ({ ...prevState, ...response }));\r\n            };\r\n            getAreaOfWorkData();\r\n        }\r\n    }, []);\r\n\r\n    return (\r\n        <div>\r\n            <Container className=\"formCard\" fluid>\r\n                <Card\r\n                    style={{\r\n                        marginTop: \"5px\",\r\n                        boxShadow: \"0 4px 8px 0 rgba(0, 0, 0, 0.2), 0 6px 20px 0 rgba(0, 0, 0, 0.19)\",\r\n                    }}\r\n                >\r\n                    <ValidationFormWrapper onSubmit={handleSubmit} ref={formRef} initialValues={initialVal} enableReinitialize={true}>\r\n                        <Card.Body>\r\n                            <Row>\r\n                                <Col>\r\n                                    <Card.Title>{t(\"adminsetting.areaofwork.Forms.AreaOfWork\")}</Card.Title>\r\n                                </Col>\r\n                            </Row>\r\n                            <hr />\r\n                            <Row>\r\n                                <Col md lg={6} sm={12}>\r\n                                    <Form.Group>\r\n                                        <Form.Label className=\"required-field\">\r\n                                            {t(\"adminsetting.areaofwork.Forms.AreaOfWork\")}\r\n                                        </Form.Label>\r\n                                        <TextInput\r\n                                            name=\"title\"\r\n                                            id=\"title\"\r\n                                            required\r\n                                            value={initialVal.title}\r\n                                            validator={(value: any) => String(value || '').trim() !== \"\"}\r\n                                            errorMessage={{\r\n                                                validator: t(\"adminsetting.areaofwork.Forms.PleaseAddtheAreaofWork\"),\r\n                                            }}\r\n                                            onChange={handleChange}\r\n                                        />\r\n                                    </Form.Group>\r\n                                </Col>\r\n                            </Row>\r\n                            <Row className=\"my-4\">\r\n                                <Col>\r\n                                    <Button className=\"me-2\" type=\"submit\" variant=\"primary\">\r\n                                        {t(\"adminsetting.areaofwork.Forms.Submit\")}\r\n                                    </Button>\r\n                                    <Button className=\"me-2\" onClick={resetHandler} variant=\"info\">\r\n                                        {t(\"adminsetting.areaofwork.Forms.Reset\")}\r\n                                    </Button>\r\n                                    <Link\r\n                                        href=\"/adminsettings/[...routes]\"\r\n                                        as={`/adminsettings/area_of_work`}\r\n                                        >\r\n                                        <Button variant=\"secondary\">{t(\"adminsetting.areaofwork.Forms.Cancel\")}</Button>\r\n                                    </Link>\r\n                                </Col>\r\n                            </Row>\r\n                        </Card.Body>\r\n                    </ValidationFormWrapper>\r\n                </Card>\r\n            </Container>\r\n        </div>\r\n    );\r\n};\r\nexport default AreaOfWorkForm;\r\n", "//Import Library\r\nimport Link from \"next/link\";\r\nimport React, { useState, useEffect } from \"react\";\r\nimport { <PERSON>dal, Button, Form } from \"react-bootstrap\";\r\nimport _ from \"lodash\";\r\nimport toast from 'react-hot-toast';\r\n\r\n//Import services/components\r\nimport RKITable from \"../../../components/common/RKITable\";\r\nimport apiService from \"../../../services/apiService\";\r\nimport HazardTableFilter from \"./hazardTableFilter\";\r\nimport { useTranslation } from 'next-i18next';\r\n\r\nconst HazardTable = () => {\r\n    const { t, i18n } = useTranslation(\"common\");\r\n    const currentLang = i18n.language === \"fr\" ? \"en\" : i18n.language;\r\n    const titleSearch = currentLang ? `title.${currentLang}` : \"title.en\";\r\n\r\n    const [tabledata, setDataToTable] = useState<any[]>([]);\r\n    const [, setLoading] = useState(false);\r\n    const [totalRows, setTotalRows] = useState(0);\r\n    const [perPage, setPerPage] = useState(10);\r\n    const [filterText, setFilterText] = React.useState(\"\");\r\n    const [resetPaginationToggle, setResetPaginationToggle] = React.useState(false);\r\n    const [isModalShow, setModal] = useState(false);\r\n    const [selectHazard, setSelectHazard] = useState({});\r\n    const [currLang] = useState(titleSearch);\r\n\r\n\r\n    const handleMonitored = async (e: React.ChangeEvent<HTMLInputElement>) => {\r\n        const index = _.findIndex(tabledata, { _id: e.target.name });\r\n        if (index > -1) {\r\n            tabledata[index].enabled = !tabledata[index].enabled;\r\n            setDataToTable([...tabledata]);\r\n            const response = await apiService.patch(`/hazard/${e.target.name}`, tabledata[index]);\r\n            if (response && response._id) {\r\n                toast.success(`${response.title[currentLang]} ${t(\"updatedSuccessfully\")}`);\r\n            } else {\r\n                toast.error(response);\r\n            }\r\n        } else {\r\n            toast.error(t(\"indexNotFound\"));\r\n        }\r\n    };\r\n\r\n    const Toggle = ({ _id, enabled } : { _id: any, enabled: any}) => (\r\n        <Form.Check\r\n            className=\"ms-4\"\r\n            type=\"switch\"\r\n            name={_id}\r\n            id={_id}\r\n            label=\"\"\r\n            checked={enabled}\r\n            onChange={(e) => handleMonitored(e)}\r\n        />\r\n    );\r\n    const columns = [\r\n        {\r\n            name: t(\"menu.hazards\"),\r\n            selector: currLang,\r\n            cell: (d: any) => (d && d.title && d.title[currentLang] ? d.title[currentLang] : \"\"),\r\n        },\r\n        {\r\n            name: t(\"hazardType\"),\r\n            selector: \"hazard_type\",\r\n            cell: (d :any) => (d && d.hazard_type && d.hazard_type.title ? d.hazard_type.title : \"\"),\r\n        },\r\n        {\r\n            name: t(\"published\"),\r\n            selector: \"enabled\",\r\n            cell: (row :any) => <Toggle {...row} />,\r\n        },\r\n        {\r\n            name: t(\"action\"),\r\n            selector: \"\",\r\n            cell: (d :any) => (\r\n                <div>\r\n                    <Link href=\"/adminsettings/[...routes]\" as={`/adminsettings/edit_hazard/${d._id}`}>\r\n\r\n                        <i className=\"icon fas fa-edit\" />\r\n\r\n                    </Link>\r\n                    &nbsp;\r\n                    <span onClick={() => userAction(d)} style={{ cursor: \"pointer\" }}>\r\n                        <i className=\"icon fas fa-trash-alt\" />\r\n                    </span>\r\n                </div>\r\n            ),\r\n        },\r\n    ];\r\n\r\n    useEffect(() => {\r\n        getHazardsData();\r\n    }, []);\r\n\r\n    const hazardParams = {\r\n        sort: { [currLang]: \"asc\" },\r\n        limit: perPage,\r\n        page: 1,\r\n        query: {},\r\n    };\r\n\r\n    const getHazardsData = async () => {\r\n        setLoading(true);\r\n        const response = await apiService.get(\"/hazard\", hazardParams);\r\n        if (response && response.data && response.data.length > 0) {\r\n            setDataToTable(response.data);\r\n            setTotalRows(response.totalCount);\r\n            setLoading(false);\r\n        }\r\n    };\r\n\r\n    const handlePageChange = (page: any) => {\r\n        hazardParams.limit = perPage;\r\n        hazardParams.page = page;\r\n        getHazardsData();\r\n    };\r\n\r\n    const handlePerRowsChange = async (newPerPage: any, page: any) => {\r\n        hazardParams.limit = newPerPage;\r\n        hazardParams.page = page;\r\n        setLoading(true);\r\n        const response = await apiService.get(\"/hazard\", hazardParams);\r\n        if (response && response.data && response.data.length > 0) {\r\n            setDataToTable(response.data);\r\n            setPerPage(newPerPage);\r\n            setLoading(false);\r\n        }\r\n    };\r\n\r\n    const userAction = async (row: any) => {\r\n        setSelectHazard(row._id);\r\n        setModal(true);\r\n    };\r\n\r\n    const modalConfirm = async () => {\r\n        try {\r\n            await apiService.remove(`/hazard/${selectHazard}`);\r\n            getHazardsData();\r\n            setModal(false);\r\n            toast.success(t(\"adminsetting.hazard.Table.hazardDeletedSuccessfully\"));\r\n        } catch (error) {\r\n            toast.error(t(\"adminsetting.hazard.Table.errorDeletingHazard\"));\r\n        }\r\n    };\r\n\r\n    const modalHide = () => setModal(false);\r\n\r\n    const subHeaderComponentMemo = React.useMemo(() => {\r\n        const handleClear = () => {\r\n            if (filterText) {\r\n                setResetPaginationToggle(!resetPaginationToggle);\r\n                setFilterText(\"\");\r\n            }\r\n        };\r\n\r\n        const sendQuery = (q: any) => {\r\n            if (q) {\r\n                hazardParams.query = { [currLang]: q };\r\n            }\r\n            getHazardsData();\r\n        };\r\n\r\n        const handleSearchTitle = _.debounce((q: string) => sendQuery(q), Number(process.env.SEARCH_DEBOUNCE_TIME) || 300);\r\n\r\n        const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {\r\n            setFilterText(e.target.value);\r\n            handleSearchTitle(e.target.value);\r\n        };\r\n\r\n        return <HazardTableFilter onFilter={handleChange} onClear={handleClear} filterText={filterText} />;\r\n    }, [filterText]);\r\n\r\n    return (\r\n        <div>\r\n            <Modal show={isModalShow} onHide={modalHide}>\r\n                <Modal.Header closeButton>\r\n                    <Modal.Title>{t(\"deleteHazard\")}</Modal.Title>\r\n                </Modal.Header>\r\n                <Modal.Body>{t(\"areYouSureWantToDeleteThisHazard\")}</Modal.Body>\r\n                <Modal.Footer>\r\n                    <Button variant=\"secondary\" onClick={modalHide}>\r\n                        {t(\"cancel\")}\r\n                    </Button>\r\n                    <Button variant=\"primary\" onClick={modalConfirm}>\r\n                        {t(\"yes\")}\r\n                    </Button>\r\n                </Modal.Footer>\r\n            </Modal>\r\n\r\n            <RKITable\r\n                columns={columns}\r\n                data={tabledata}\r\n                totalRows={totalRows}\r\n                pagServer={true}\r\n                subheader\r\n                resetPaginationToggle={resetPaginationToggle}\r\n                subHeaderComponent={subHeaderComponentMemo}\r\n                handlePerRowsChange={handlePerRowsChange}\r\n                handlePageChange={handlePageChange}\r\n            />\r\n        </div>\r\n    );\r\n};\r\n\r\nexport default HazardTable;\r\n", "//Import Library\r\nimport { Contain<PERSON>, <PERSON>, <PERSON>, Button } from \"react-bootstrap\";\r\nimport Link from \"next/link\";\r\n\r\n//Import services/components\r\nimport PageHeading from \"../../../components/common/PageHeading\";\r\nimport SyndromeTable from \"./syndromeTable\";\r\nimport { useTranslation } from 'next-i18next';\r\nimport { serverSideTranslations } from 'next-i18next/serverSideTranslations';\r\nimport { canAddSyndromes } from \"../permissions\";\r\nimport { useSelector } from \"react-redux\";\r\nimport NoAccessMessage from \"../../rNoAccess\";\r\n\r\nconst SyndromeIndex = (_props: any) => {\r\n  const { t } = useTranslation('common');\r\n  const ShowSyndromeIndex = () => {\r\n    return (\r\n      <Container style={{ overflowX: \"hidden\" }} fluid className=\"p-0\">\r\n        <Row>\r\n          <Col xs={12}>\r\n            <PageHeading title={t(\"adminsetting.syndrome.Syndromes\")} />\r\n          </Col>\r\n        </Row>\r\n        <Row>\r\n          <Col xs={12}>\r\n            <Link\r\n              href=\"/adminsettings/[...routes]\"\r\n              as=\"/adminsettings/create_syndrome\"\r\n              >\r\n              <Button variant=\"secondary\" size=\"sm\">\r\n              {t(\"adminsetting.syndrome.AddSyndrome\")}\r\n              </Button>\r\n            </Link>\r\n          </Col>\r\n        </Row>\r\n        <Row className=\"mt-3\">\r\n          <Col xs={12}>\r\n            <SyndromeTable />\r\n          </Col>\r\n        </Row>\r\n      </Container>\r\n    );\r\n  }\r\n\r\n  const ShowAddSyndromes = canAddSyndromes(() => <ShowSyndromeIndex />);\r\n  const state:any = useSelector((state) => state);\r\n  if (!(state?.permissions?.syndrome?.['create:any'])) {\r\n    return <NoAccessMessage />\r\n  }\r\n  return(\r\n    <ShowAddSyndromes />\r\n  )\r\n};\r\n\r\nexport async function getServerSideProps({ locale } : { locale: string }) {\r\n  return {\r\n    props: {\r\n      ...(await serverSideTranslations(locale, ['common'])),\r\n    },\r\n  }\r\n}\r\n\r\nexport default SyndromeIndex;\r\n", "//Import Library\r\nimport { useState, useRef, useEffect } from \"react\";\r\nimport { Container, Card, Row, Col, Form, Button } from \"react-bootstrap\";\r\nimport { ValidationForm, TextInput } from \"../../../components/common/FormValidation\";\r\nimport Router from \"next/router\";\r\nimport toast from 'react-hot-toast';\r\nimport Link from \"next/link\";\r\n\r\n//Import services/components\r\nimport { WorldRegion } from \"../../../types\";\r\nimport apiService from \"../../../services/apiService\";\r\nimport { useTranslation } from 'next-i18next';\r\nimport ValidationFormWrapper from \"../../../components/common/ValidationFormWrapper\";\r\n\r\ninterface WorldregionFormProps {\r\n    [key: string]: any;\r\n}\r\n\r\nconst WorldregionForm = (props: WorldregionFormProps) => {\r\n    const { t } = useTranslation('common');\r\n\r\n    const _initialworldregion = {\r\n        title: \"\",\r\n        code: \"\",\r\n    };\r\n\r\n    const [initialVal, setInitialVal] = useState<WorldRegion>(_initialworldregion);\r\n\r\n    const editform = props.routes && props.routes[0] === \"edit_worldregion\" && props.routes[1];\r\n\r\n    const formRef = useRef(null);\r\n\r\n    const resetHandler = () => {\r\n        setInitialVal(_initialworldregion);\r\n        // Reset validation state (Formik handles this automatically)\r\n        window.scrollTo(0, 0);\r\n    };\r\n\r\n    const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement>) => {\r\n        if (e.target) {\r\n            const { name, value } = e.target;\r\n            setInitialVal((prevState) => ({\r\n                ...prevState,\r\n                [name]: value,\r\n            }));\r\n        }\r\n    };\r\n\r\n    const handleSubmit = async (event: any) => {\r\n        event.preventDefault();\r\n        const obj = {\r\n            title: initialVal.title.trim(),\r\n            code: initialVal.code,\r\n        };\r\n\r\n        let response;\r\n        let toastMsg;\r\n        if (editform) {\r\n            toastMsg = \"adminsetting.worldregion.form.Worldregionisupdatedsuccessfully\";\r\n            response = await apiService.patch(`/worldregion/${props.routes[1]}`, obj);\r\n        } else {\r\n            toastMsg = \"adminsetting.worldregion.form.Worldregionisaddedsuccessfully\";\r\n            response = await apiService.post(\"/worldregion\", obj);\r\n        }\r\n        if (response && response._id) {\r\n            toast.success(t(toastMsg));\r\n            Router.push(\"/adminsettings/worldregion\");\r\n        } else {\r\n            if (response?.errorCode === 11000) {\r\n                toast.error(t(\"duplicatesNotAllowed\"));\r\n            } else {\r\n                toast.error(response);\r\n            }\r\n        }\r\n    };\r\n\r\n    useEffect(() => {\r\n        const worldregionParams = {\r\n            query: {},\r\n            sort: { title: \"asc\" },\r\n            limit: \"~\",\r\n        };\r\n        if (editform) {\r\n            const getWorldregionData = async () => {\r\n                const response: WorldRegion = await apiService.get(`/worldregion/${props.routes[1]}`, worldregionParams);\r\n                setInitialVal((prevState) => ({ ...prevState, ...response }));\r\n            };\r\n            getWorldregionData();\r\n        }\r\n    }, []);\r\n\r\n    return (\r\n        <div>\r\n            <Container className=\"formCard\" fluid>\r\n                <Card\r\n                    style={{\r\n                        marginTop: \"5px\",\r\n                        boxShadow: \"0 4px 8px 0 rgba(0, 0, 0, 0.2), 0 6px 20px 0 rgba(0, 0, 0, 0.19)\",\r\n                    }}\r\n                >\r\n                    <ValidationFormWrapper onSubmit={handleSubmit} ref={formRef} initialValues={initialVal} enableReinitialize={true}>\r\n                        <Card.Body>\r\n                            <Row>\r\n                                <Col>\r\n                                    <Card.Title>{t(\"adminsetting.worldregion.form.WorldRegion\")}</Card.Title>\r\n                                </Col>\r\n                            </Row>\r\n                            <hr />\r\n                            <Row>\r\n                                <Col md lg={6} sm={12}>\r\n                                    <Form.Group>\r\n                                        <Form.Label className=\"required-field\">\r\n                                            {t(\"adminsetting.worldregion.form.WorldRegion\")}\r\n                                        </Form.Label>\r\n                                        <TextInput\r\n                                            name=\"title\"\r\n                                            id=\"title\"\r\n                                            required\r\n                                            value={initialVal.title}\r\n                                            validator={(value: string) => String(value || '').trim() !== \"\"}\r\n                                            errorMessage={{\r\n                                                validator: t(\"adminsetting.worldregion.form.PleaseAddtheWorldRegion\"),\r\n                                            }}\r\n                                            onChange={handleChange}\r\n                                        />\r\n                                    </Form.Group>\r\n                                </Col>\r\n                                <Col md lg={6} sm={12}>\r\n                                    <Form.Group>\r\n                                        <Form.Label className=\"required-field\">\r\n                                            {t(\"adminsetting.worldregion.form.Code\")}\r\n                                        </Form.Label>\r\n                                        <TextInput\r\n                                            name=\"code\"\r\n                                            id=\"code\"\r\n                                            required\r\n                                            value={initialVal.code}\r\n                                            validator={(value: string) => String(value || '').trim() !== \"\"}\r\n                                            errorMessage={{ validator: t(\"adminsetting.worldregion.form.PleaseAddthecode\")}}\r\n                                            onChange={handleChange}\r\n                                        />\r\n                                    </Form.Group>\r\n                                </Col>\r\n                            </Row>\r\n                            <Row className=\"my-4\">\r\n                                <Col>\r\n                                    <Button className=\"me-2\" type=\"submit\" variant=\"primary\">\r\n                                        {t(\"adminsetting.worldregion.form.Submit\")}\r\n                                    </Button>\r\n                                    <Button className=\"me-2\" onClick={resetHandler} variant=\"info\">\r\n                                        {t(\"adminsetting.worldregion.form.Reset\")}\r\n                                    </Button>\r\n                                    <Link\r\n                                        href=\"/adminsettings/[...routes]\"\r\n                                        as={`/adminsettings/worldregion`}\r\n                                        >\r\n                                        <Button variant=\"secondary\">{t(\"adminsetting.worldregion.form.Cancel\")}</Button>\r\n                                    </Link>\r\n                                </Col>\r\n                            </Row>\r\n                        </Card.Body>\r\n                    </ValidationFormWrapper>\r\n                </Card>\r\n            </Container>\r\n        </div>\r\n    );\r\n};\r\nexport default WorldregionForm;\r\n", "//Import Library\r\nimport { Container } from \"react-bootstrap\";\r\n\r\n//Import services/components\r\nimport InstitutionTable from \"./InstitutionTable\";\r\nimport PageHeading from \"../../../components/common/PageHeading\";\r\nimport { useTranslation } from 'next-i18next';\r\nimport { serverSideTranslations } from 'next-i18next/serverSideTranslations';\r\nimport { canAddOrganisationApproval } from \"../permissions\";\r\nimport { useSelector } from \"react-redux\";\r\nimport NoAccessMessage from \"../../rNoAccess\";\r\n\r\n\r\nconst InstitutionApproval = (_props: any) => {\r\n  const { t } = useTranslation('common');\r\n  const ShowInstitutionApproval = () => {\r\n    return (\r\n      <Container fluid className=\"p-0\">\r\n        <PageHeading title={t(\"adminsetting.approval.OrganisationApproval\")} />\r\n        <InstitutionTable />\r\n      </Container>\r\n    )\r\n  };\r\n\r\n  const ShowAddOrganisationApproval = canAddOrganisationApproval(() => <ShowInstitutionApproval />);\r\n  const state:any = useSelector((state: any) => state);\r\n  if (!(state?.permissions?.institution?.['create:any'])) {\r\n    return <NoAccessMessage />\r\n  }\r\n  return(\r\n    <ShowAddOrganisationApproval />\r\n  )\r\n}\r\n\r\nexport async function getServerSideProps({ locale }: { locale: string }) {\r\n  return {\r\n    props: {\r\n      ...(await serverSideTranslations(locale, ['common'])),\r\n    },\r\n  }\r\n}\r\n\r\nexport default InstitutionApproval;\r\n", "\n    (window.__NEXT_P = window.__NEXT_P || []).push([\n      \"/adminsettings/[...routes]\",\n      function () {\n        return require(\"private-next-pages/adminsettings/[...routes].tsx\");\n      }\n    ]);\n    if(module.hot) {\n      module.hot.dispose(function () {\n        window.__NEXT_P.push([\"/adminsettings/[...routes]\"])\n      });\n    }\n  ", "//Import Library\r\nimport { useState, useRef, useEffect } from \"react\";\r\nimport { Container, Card, Row, Col, Form, Button } from \"react-bootstrap\";\r\nimport { TextInput } from \"../../../components/common/FormValidation\";\r\nimport ValidationFormWrapper from \"../../../components/common/ValidationFormWrapper\";\r\nimport Router from \"next/router\";\r\nimport toast from 'react-hot-toast';\r\nimport Link from \"next/link\";\r\n\r\n//Import services/components\r\nimport { useTranslation } from 'next-i18next';\r\nimport { DeploymentStatus } from \"../../../types\";\r\nimport apiService from \"../../../services/apiService\";\r\n\r\ninterface DeploymentstatusFormProps {\r\n    [key: string]: any;\r\n}\r\n\r\nconst DeploymentstatusForm = (props: DeploymentstatusFormProps) => {\r\n    const _initialdeploymentstatus = {\r\n        _id: \"\",\r\n        title: \"\",\r\n    };\r\n    const { t } = useTranslation('common');\r\n    const [initialVal, setInitialVal] = useState<DeploymentStatus>(_initialdeploymentstatus);\r\n\r\n    const editform: boolean = props.routes && props.routes[0] === \"edit_deploymentstatus\" && props.routes[1];\r\n\r\n    const formRef = useRef(null);\r\n\r\n    const resetHandler = () => {\r\n        setInitialVal(_initialdeploymentstatus);\r\n        // Reset validation state (Formik handles this automatically)\r\n        window.scrollTo(0, 0);\r\n    };\r\n\r\n    const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement>) => {\r\n        if (e.target) {\r\n            const { name, value } = e.target;\r\n            setInitialVal((prevState) => ({\r\n                ...prevState,\r\n                [name]: value,\r\n            }));\r\n        }\r\n    };\r\n\r\n    const handleSubmit = async (event: any) => {\r\n        event.preventDefault();\r\n        const obj = {\r\n            title: initialVal.title.trim(),\r\n        };\r\n\r\n        let response;\r\n        let toastMsg;\r\n        if (editform) {\r\n            toastMsg = \"adminsetting.DeploymentStatus.Forms.DeploymentstatusisUpdatedsuccessfully\";\r\n            response = await apiService.patch(`/deploymentstatus/${props.routes[1]}`, obj);\r\n        } else {\r\n            toastMsg = \"adminsetting.DeploymentStatus.Forms.Deploymentstatusisaddedsuccessfully\";\r\n            response = await apiService.post(\"/deploymentstatus\", obj);\r\n        }\r\n        if (response && response._id) {\r\n            toast.success(t(toastMsg));\r\n            Router.push(\"/adminsettings/deploymentstatus\");\r\n        } else {\r\n            if (response?.errorCode === 11000) {\r\n                toast.error(t(\"duplicatesNotAllowed\"));\r\n            } else {\r\n                toast.error(response);\r\n            }\r\n        }\r\n    };\r\n\r\n    useEffect(() => {\r\n        const deploymentstatusParams = {\r\n            query: {},\r\n            sort: { title: \"asc\" },\r\n            limit: \"~\",\r\n        };\r\n        if (editform) {\r\n            const getDeploymentstatusData = async () => {\r\n                const response: DeploymentStatus = await apiService.get(`/deploymentstatus/${props.routes[1]}`, deploymentstatusParams);\r\n                setInitialVal((prevState) => ({ ...prevState, ...response }));\r\n            };\r\n            getDeploymentstatusData();\r\n        }\r\n    }, []);\r\n\r\n    return (\r\n        <div>\r\n            <Container className=\"formCard\" fluid>\r\n                <Card\r\n                    style={{\r\n                        marginTop: \"5px\",\r\n                        boxShadow: \"0 4px 8px 0 rgba(0, 0, 0, 0.2), 0 6px 20px 0 rgba(0, 0, 0, 0.19)\",\r\n                    }}\r\n                >\r\n                    <ValidationFormWrapper onSubmit={handleSubmit} ref={formRef} initialValues={initialVal} enableReinitialize={true}>\r\n                        <Card.Body>\r\n                            <Row>\r\n                                <Col>\r\n                                    <Card.Title>{t(\"adminsetting.DeploymentStatus.Forms.DeploymentStatus\")}</Card.Title>\r\n                                </Col>\r\n                            </Row>\r\n                            <hr />\r\n                            <Row>\r\n                                <Col md lg={6} sm={12}>\r\n                                    <Form.Group>\r\n                                        <Form.Label className=\"required-field\">\r\n                                            {t(\"adminsetting.DeploymentStatus.Forms.DeploymentStatus\")}\r\n                                        </Form.Label>\r\n                                        <TextInput\r\n                                            name=\"title\"\r\n                                            id=\"title\"\r\n                                            required\r\n                                            value={initialVal.title}\r\n                                            validator={(value: any) => value.trim() !== \"\"}\r\n                                            errorMessage={{\r\n                                                validator: t(\r\n                                                    \"adminsetting.DeploymentStatus.Forms.PleaseAddtheDeploymentstatus\"\r\n                                                ),\r\n                                            }}\r\n                                            onChange={handleChange}\r\n                                        />\r\n                                    </Form.Group>\r\n                                </Col>\r\n                            </Row>\r\n                            <Row className=\"my-4\">\r\n                                <Col>\r\n                                    <Button className=\"me-2\" type=\"submit\" variant=\"primary\">\r\n                                        {t(\"adminsetting.DeploymentStatus.Forms.Submit\")}\r\n                                    </Button>\r\n                                    <Button className=\"me-2\" onClick={resetHandler} variant=\"info\">\r\n                                        {t(\"adminsetting.DeploymentStatus.Forms.Reset\")}\r\n                                    </Button>\r\n                                    <Link\r\n                                        href=\"/adminsettings/[...routes]\"\r\n                                        as={`/adminsettings/deploymentstatus`}\r\n                                        >\r\n                                        <Button variant=\"secondary\">\r\n                                            {t(\"adminsetting.DeploymentStatus.Forms.Cancel\")}\r\n                                        </Button>\r\n                                    </Link>\r\n                                </Col>\r\n                            </Row>\r\n                        </Card.Body>\r\n                    </ValidationFormWrapper>\r\n                </Card>\r\n            </Container>\r\n        </div>\r\n    );\r\n};\r\nexport default DeploymentstatusForm;\r\n", "//Import Library\r\nimport { Con<PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, Button } from \"react-bootstrap\";\r\nimport { TextInput } from \"../../../components/common/FormValidation\";\r\nimport ValidationFormWrapper from \"../../../components/common/ValidationFormWrapper\";\r\nimport { useRef, useState, useEffect } from \"react\";\r\nimport toast from 'react-hot-toast';\r\nimport Router from \"next/router\";\r\nimport Link from \"next/link\";\r\n\r\n//Import services/components\r\nimport apiService from \"../../../services/apiService\";\r\nimport { Syndrome } from \"../../../types\";\r\nimport { useTranslation } from 'next-i18next';\r\nimport { EditorComponent } from \"../../../shared/quill-editor/quill-editor.component\";\r\n\r\ninterface SyndromeFormProps {\r\n    [key: string]: any;\r\n}\r\n\r\nconst SyndromeForm = (props: SyndromeFormProps) => {\r\n    const _initialSyndrome = {\r\n        title: \"\",\r\n        code: \"\",\r\n        description: \"\",\r\n    };\r\n    const { t } = useTranslation('common');\r\n    const [initialVal, setInitialVal] = useState<any>(_initialSyndrome);\r\n\r\n    const editform = props.routes && props.routes[0] === \"edit_syndrome\" && props.routes[1];\r\n\r\n    const handleSubmit = async (event: any, values?: any) => {\r\n        event.preventDefault();\r\n        const obj = {\r\n            title: initialVal.title.trim(),\r\n            code: initialVal.code,\r\n            description: initialVal.description,\r\n        };\r\n\r\n        let response;\r\n        let toastMsg;\r\n        if (editform) {\r\n            toastMsg = \"adminsetting.syndrome.Syndromeisupdatedsuccessfully\";\r\n            response = await apiService.patch(`/syndrome/${props.routes[1]}`, obj);\r\n        } else {\r\n            toastMsg = \"adminsetting.syndrome.Syndromeisaddedsuccessfully\";\r\n            response = await apiService.post(\"/syndrome\", obj);\r\n        }\r\n        if (response && response._id) {\r\n            toast.success(t(toastMsg));\r\n            Router.push(\"/adminsettings/syndrome\");\r\n        } else {\r\n            toast.error(response);\r\n        }\r\n    };\r\n\r\n    const resetHandler = () => {\r\n        setInitialVal(_initialSyndrome);\r\n        // Reset validation state (Formik handles this automatically)\r\n        window.scrollTo(0, 0);\r\n    };\r\n    const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement>) => {\r\n        if (e.target) {\r\n            const { name, value } = e.target;\r\n            setInitialVal((prevState: any) => ({\r\n                ...prevState,\r\n                [name]: value,\r\n            }));\r\n        }\r\n    };\r\n\r\n    const handleDescription = (value: string) => {\r\n        setInitialVal((prevState: any) => ({\r\n            ...prevState,\r\n            description: value,\r\n        }));\r\n    };\r\n\r\n    useEffect(() => {\r\n        const syndromeParams = {\r\n            query: {},\r\n            sort: { title: \"asc\" },\r\n            limit: \"~\",\r\n        };\r\n\r\n        if (editform) {\r\n            const getSyndromeData = async () => {\r\n                const response: Syndrome = await apiService.get(`/syndrome/${props.routes[1]}`, syndromeParams);\r\n                setInitialVal((prevState: any) => ({ ...prevState, ...response }));\r\n            };\r\n            getSyndromeData();\r\n        }\r\n    }, []);\r\n\r\n    const formRef = useRef(null);\r\n\r\n    return (\r\n        <Container className=\"formCard\" fluid>\r\n            <Card\r\n                style={{\r\n                    marginTop: \"5px\",\r\n                    boxShadow: \"0 4px 8px 0 rgba(0, 0, 0, 0.2), 0 6px 20px 0 rgba(0, 0, 0, 0.19)\",\r\n                }}\r\n            >\r\n                <ValidationFormWrapper onSubmit={handleSubmit} ref={formRef} initialValues={initialVal} enableReinitialize={true}>\r\n                    <Card.Body>\r\n                        <Row>\r\n                            <Col>\r\n                                <Card.Title>{editform ? t(\"adminsetting.syndrome.EditSyndrome\") : t(\"adminsetting.syndrome.AddSyndrome\")}</Card.Title>\r\n                            </Col>\r\n                        </Row>\r\n                        <hr />\r\n                        <Row>\r\n                            <Col md lg={6} sm={12}>\r\n                                <Form.Group>\r\n                                    <Form.Label className=\"required-field\">\r\n                                        {t(\"adminsetting.syndrome.SyndromeName\")}\r\n                                    </Form.Label>\r\n                                    <TextInput\r\n                                        name=\"title\"\r\n                                        id=\"title\"\r\n                                        required\r\n                                        value={initialVal.title}\r\n                                        validator={(value: string) => value.trim() !== \"\"}\r\n                                        errorMessage={{\r\n                                            validator: t(\"adminsetting.syndrome.PleaseAddtheSyndromeName\"),\r\n                                        }}\r\n                                        onChange={handleChange}\r\n                                    />\r\n                                </Form.Group>\r\n                            </Col>\r\n                            <Col md lg={6} sm={12}>\r\n                                <Form.Group>\r\n                                    <Form.Label>{t(\"adminsetting.syndrome.Code\")}</Form.Label>\r\n                                    <TextInput\r\n                                        name=\"code\"\r\n                                        id=\"code\"\r\n                                        required\r\n                                        value={initialVal.code}\r\n                                        errorMessage={{validator: t(\"adminsetting.syndrome.PleaseAddtheCode\")}}\r\n                                        onChange={handleChange}\r\n                                    />\r\n                                </Form.Group>\r\n                            </Col>\r\n                        </Row>\r\n                        <Row>\r\n                            <Col>\r\n                                <Form.Group>\r\n                                    <Form.Label>{t(\"adminsetting.syndrome.Description\")}</Form.Label>\r\n                                    <EditorComponent initContent={initialVal.description} onChange={(evt: string) => handleDescription(evt)} />\r\n                                </Form.Group>\r\n                            </Col>\r\n                        </Row>\r\n                        <Row className=\"my-4\">\r\n                            <Col>\r\n                                <Button className=\"me-2\" type=\"submit\" variant=\"primary\">\r\n                                    {t(\"adminsetting.syndrome.Submit\")}\r\n                                </Button>\r\n                                <Button className=\"me-2\" onClick={resetHandler} variant=\"info\">\r\n                                    {t(\"adminsetting.syndrome.Reset\")}\r\n                                </Button>\r\n                                <Link\r\n                                    href=\"/adminsettings/[...routes]\"\r\n                                    as={`/adminsettings/syndrome`}\r\n                                    >\r\n                                    <Button variant=\"secondary\">{t(\"adminsetting.syndrome.Cancel\")}</Button>\r\n                                </Link>\r\n                            </Col>\r\n                        </Row>\r\n                    </Card.Body>\r\n                </ValidationFormWrapper>\r\n            </Card>\r\n        </Container>\r\n    );\r\n};\r\nexport default SyndromeForm;\r\n", "//Import Library\r\nimport Link from \"next/link\";\r\nimport { useState, useEffect } from \"react\";\r\nimport { <PERSON><PERSON>, Button } from \"react-bootstrap\";\r\nimport toast from 'react-hot-toast';\r\n\r\n//Import services/components\r\nimport RKITable from \"../../../components/common/RKITable\";\r\nimport apiService from \"../../../services/apiService\";\r\nimport { useTranslation } from 'next-i18next';\r\n\r\nconst HazardTypeTable = (_props: any) => {\r\n    const [tabledata, setDataToTable] = useState([]);\r\n    const [, setLoading] = useState(false);\r\n    const [totalRows, setTotalRows] = useState(0);\r\n    const [perPage, setPerPage] = useState(10);\r\n    const [isModalShow, setModal] = useState(false);\r\n    const [selectHazardType, setSelectHazardType] = useState({});\r\n    const { t } = useTranslation('common');\r\n\r\n\r\n    const columns = [\r\n        {\r\n            name: t(\"adminsetting.hazardtypes.HarzardType\"),\r\n            selector: \"title\",\r\n        },\r\n        {\r\n            name: t(\"adminsetting.hazardtypes.Code\"),\r\n            selector: \"code\",\r\n            cell: (d: any) => d.code,\r\n        },\r\n        {\r\n            name: t(\"Description\"),\r\n            selector: \"description\",\r\n            cell: (d: any) => d.description.replace(/<[^>]+>/g, \"\"),\r\n        },\r\n        {\r\n            name: t(\"action\"),\r\n            selector: \"\",\r\n            cell: (d: any) => (\r\n                <div>\r\n                    <Link href=\"/adminsettings/[...routes]\" as={`/adminsettings/edit_hazard_types/${d._id}`}>\r\n\r\n                        <i className=\"icon fas fa-edit\" />\r\n\r\n                    </Link>\r\n                    &nbsp;\r\n                    <a onClick={() => userAction(d)}>\r\n                        <i className=\"icon fas fa-trash-alt\" />\r\n                    </a>\r\n                </div>\r\n            ),\r\n        },\r\n    ];\r\n\r\n    useEffect(() => {\r\n        getHazardsTypeData();\r\n    }, []);\r\n\r\n    const hazardTypeParams = {\r\n        sort: { title: \"asc\" },\r\n        limit: perPage,\r\n        page: 1,\r\n        query: {},\r\n    };\r\n\r\n    const getHazardsTypeData = async () => {\r\n        setLoading(true);\r\n        const response = await apiService.get(\"/hazardtype\", hazardTypeParams);\r\n        if (response && response.data && response.data.length > 0) {\r\n            setDataToTable(response.data);\r\n            setTotalRows(response.totalCount);\r\n            setLoading(false);\r\n        }\r\n    };\r\n\r\n    const handlePageChange = (page: any) => {\r\n        hazardTypeParams.limit = perPage;\r\n        hazardTypeParams.page = page;\r\n        getHazardsTypeData();\r\n    };\r\n\r\n    const handlePerRowsChange = async (newPerPage: any, page: any) => {\r\n        hazardTypeParams.limit = newPerPage;\r\n        hazardTypeParams.page = page;\r\n        setLoading(true);\r\n        const response = await apiService.get(\"/hazardtype\", hazardTypeParams);\r\n        if (response && response.data && response.data.length > 0) {\r\n            setDataToTable(response.data);\r\n            setPerPage(newPerPage);\r\n            setLoading(false);\r\n        }\r\n    };\r\n\r\n    const userAction = async (row: any) => {\r\n        setSelectHazardType(row._id);\r\n        setModal(true);\r\n    };\r\n\r\n    const modalConfirm = async () => {\r\n        try {\r\n            await apiService.remove(`/hazardtype/${selectHazardType}`);\r\n            getHazardsTypeData();\r\n            setModal(false);\r\n            toast.success(t(\"adminsetting.hazardtypes.Table.hazardTypeDeletedSuccessfully\"));\r\n        } catch (error) {\r\n            toast.error(t(\"adminsetting.hazardtypes.Table.errorDeletingHazardType\"));\r\n        }\r\n    };\r\n\r\n    const modalHide = () => setModal(false);\r\n\r\n    return (\r\n        <div>\r\n            <Modal show={isModalShow} onHide={modalHide}>\r\n                <Modal.Header closeButton>\r\n                    <Modal.Title>{t(\"adminsetting.hazardtypes.Deletehazardtype\")}</Modal.Title>\r\n                </Modal.Header>\r\n                <Modal.Body>{t(\"adminsetting.hazardtypes.Areyousurewanttodeletethishazardtype\")}</Modal.Body>\r\n                <Modal.Footer>\r\n                    <Button variant=\"secondary\" onClick={modalHide}>\r\n                        {t(\"cancel\")}\r\n                    </Button>\r\n                    <Button variant=\"primary\" onClick={modalConfirm}>\r\n                        {t(\"yes\")}\r\n                    </Button>\r\n                </Modal.Footer>\r\n            </Modal>\r\n\r\n            <RKITable\r\n                columns={columns}\r\n                data={tabledata}\r\n                totalRows={totalRows}\r\n                pagServer={true}\r\n                handlePerRowsChange={handlePerRowsChange}\r\n                handlePageChange={handlePageChange}\r\n            />\r\n        </div>\r\n    );\r\n};\r\n\r\nexport default HazardTypeTable;\r\n", "//Import Library\r\nimport { useState, useRef, useEffect } from \"react\";\r\nimport { Container, Card, Row, Col, Form, Button } from \"react-bootstrap\";\r\nimport { TextInput } from \"../../../components/common/FormValidation\";\r\nimport ValidationFormWrapper from \"../../../components/common/ValidationFormWrapper\";\r\nimport Router from \"next/router\";\r\nimport toast from 'react-hot-toast';\r\nimport Link from \"next/link\";\r\n\r\n//Import services/components\r\nimport apiService from \"../../../services/apiService\";\r\nimport { InstitutionType } from \"../../../types\";\r\nimport { useTranslation } from 'next-i18next';\r\n\r\ninterface InstitutionTypeFormProps {\r\n    [key: string]: any;\r\n}\r\n\r\nconst InstitutionTypeForm = (props: InstitutionTypeFormProps) => {\r\n    const _initialinstitutionType = {\r\n        title: \"\",\r\n    };\r\n\r\n    const [initialVal, setInitialVal] = useState<InstitutionType>(_initialinstitutionType);\r\n\r\n    const editform: boolean = props.routes && props.routes[0] === \"edit_institution_type\" && props.routes[1];\r\n    const { t } = useTranslation('common');\r\n\r\n    const formRef = useRef(null);\r\n\r\n    const resetHandler = () => {\r\n        setInitialVal(_initialinstitutionType);\r\n        // Reset validation state (Formik handles this automatically)\r\n        window.scrollTo(0, 0);\r\n    };\r\n\r\n    const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement>) => {\r\n        if (e.target) {\r\n            const { name, value } = e.target;\r\n            setInitialVal((prevState) => ({\r\n                ...prevState,\r\n                [name]: value,\r\n            }));\r\n        }\r\n    };\r\n\r\n    const handleSubmit = async (event: any) => {\r\n        event.preventDefault();\r\n        const obj = {\r\n            title: initialVal?.title?.trim(),\r\n        };\r\n\r\n        let response;\r\n        let toastMsg;\r\n        if (editform) {\r\n            toastMsg = \"adminsetting.Organisationtypes.updatesuccess\";\r\n            response = await apiService.patch(`/institutiontype/${props.routes[1]}`, obj);\r\n        } else {\r\n            toastMsg = \"adminsetting.Organisationtypes.success\";\r\n            response = await apiService.post(\"/institutiontype\", obj);\r\n        }\r\n        if (response && response._id) {\r\n            toast.success(t(toastMsg));\r\n            Router.push(\"/adminsettings/institution_type\");\r\n        } else {\r\n            if (response?.errorCode === 11000) {\r\n                toast.error(t(\"duplicatesNotAllowed\"));\r\n            } else {\r\n                toast.error(response);\r\n            }\r\n        }\r\n    };\r\n\r\n    useEffect(() => {\r\n        const institutionTypeParams = {\r\n            query: {},\r\n            sort: { title: \"asc\" },\r\n            limit: \"~\",\r\n        };\r\n        if (editform) {\r\n            const getInstitutionTypeData = async () => {\r\n                const response: InstitutionType = await apiService.get(`/institutiontype/${props.routes[1]}`, institutionTypeParams);\r\n                setInitialVal((prevState) => ({ ...prevState, ...response }));\r\n            };\r\n            getInstitutionTypeData();\r\n        }\r\n    }, []);\r\n\r\n    return (\r\n        <div>\r\n            <Container className=\"formCard\" fluid>\r\n                <Card\r\n                    style={{\r\n                        marginTop: \"5px\",\r\n                        boxShadow: \"0 4px 8px 0 rgba(0, 0, 0, 0.2), 0 6px 20px 0 rgba(0, 0, 0, 0.19)\",\r\n                    }}\r\n                >\r\n                    <ValidationFormWrapper onSubmit={handleSubmit} ref={formRef} initialValues={initialVal} enableReinitialize={true}>\r\n                        <Card.Body>\r\n                            <Row>\r\n                                <Col>\r\n                                    <Card.Title>{t(\"OrganisationType\")}</Card.Title>\r\n                                </Col>\r\n                            </Row>\r\n                            <hr />\r\n                            <Row>\r\n                                <Col md lg={6} sm={12}>\r\n                                    <Form.Group>\r\n                                        <Form.Label className=\"required-field\">{t(\"OrganisationType\")}</Form.Label>\r\n                                        <TextInput\r\n                                            name=\"title\"\r\n                                            id=\"title\"\r\n                                            required\r\n                                            value={initialVal.title}\r\n                                            validator={(value: string) => String(value || '').trim() !== \"\"}\r\n                                            errorMessage={{\r\n                                                validator: t(\"adminsetting.Organisationtypes.add\"),\r\n                                            }}\r\n                                            onChange={handleChange}\r\n                                        />\r\n                                    </Form.Group>\r\n                                </Col>\r\n                            </Row>\r\n                            <Row className=\"my-4\">\r\n                                <Col>\r\n                                    <Button className=\"me-2\" type=\"submit\" variant=\"primary\">\r\n                                        {t(\"submit\")}\r\n                                    </Button>\r\n                                    <Button className=\"me-2\" onClick={resetHandler} variant=\"info\">\r\n                                        {t(\"reset\")}\r\n                                    </Button>\r\n                                    <Link\r\n                                        href=\"/adminsettings/[...routes]\"\r\n                                        as={`/adminsettings/institution_type`}\r\n                                        >\r\n                                        <Button variant=\"secondary\">{t(\"Cancel\")}</Button>\r\n                                    </Link>\r\n                                </Col>\r\n                            </Row>\r\n                        </Card.Body>\r\n                    </ValidationFormWrapper>\r\n                </Card>\r\n            </Container>\r\n        </div>\r\n    );\r\n};\r\nexport default InstitutionTypeForm;\r\n", "//Import Library\r\nimport { Con<PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, Button } from \"react-bootstrap\";\r\nimport { TextInput } from \"../../../components/common/FormValidation\";\r\nimport ValidationFormWrapper from \"../../../components/common/ValidationFormWrapper\";\r\nimport { useRef, useState, useEffect } from \"react\";\r\nimport toast from 'react-hot-toast';\r\nimport Router from \"next/router\";\r\nimport Link from \"next/link\";\r\n\r\n//Import services/components\r\nimport apiService from \"../../../services/apiService\";\r\nimport { HazardType } from \"../../../types\";\r\nimport { useTranslation } from 'next-i18next';\r\nimport { EditorComponent } from \"../../../shared/quill-editor/quill-editor.component\";\r\n\r\ninterface HazardTypeFormProps {\r\n    [key: string]: any;\r\n}\r\n\r\nconst HazardTypeForm = (props: HazardTypeFormProps) => {\r\n    const _initialHazardType = {\r\n        title: \"\",\r\n        code: \"\",\r\n        description: \"\",\r\n    };\r\n\r\n    const [initialVal, setInitialVal] = useState<HazardType>(_initialHazardType);\r\n\r\n\r\n    const editform = props.routes && props.routes[0] === \"edit_hazard_types\" && props.routes[1];\r\n    const { t } = useTranslation('common');\r\n\r\n    const handleSubmit = async (event: any, values?: any) => {\r\n        event.preventDefault();\r\n        const obj = {\r\n            title: initialVal.title?.trim(),\r\n            code: initialVal.code?.trim(),\r\n            description: initialVal.description,\r\n        };\r\n\r\n        let response;\r\n        let toastMsg;\r\n        if (editform) {\r\n            toastMsg = \"adminsetting.hazardtypes.updatesuccess\";\r\n            response = await apiService.patch(`/hazardtype/${props.routes[1]}`, obj);\r\n        } else {\r\n            toastMsg = \"adminsetting.hazardtypes.success\";\r\n            response = await apiService.post(\"/hazardtype\", obj);\r\n        }\r\n        if (response && response._id) {\r\n            toast.success(t(toastMsg));\r\n            Router.push(\"/adminsettings/hazardTypes\");\r\n        } else {\r\n            if (response?.errorCode === 11000) {\r\n                toast.error(t(\"duplicatesNotAllowed\"));\r\n            } else {\r\n                toast.error(response);\r\n            }\r\n        }\r\n    };\r\n\r\n    const resetHandler = () => {\r\n        setInitialVal(_initialHazardType);\r\n        // Reset validation state (Formik handles this automatically)\r\n        window.scrollTo(0, 0);\r\n    };\r\n    const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement>) => {\r\n        if (e.target) {\r\n            const { name, value } = e.target;\r\n            setInitialVal((prevState) => ({\r\n                ...prevState,\r\n                [name]: value,\r\n            }));\r\n        }\r\n    };\r\n\r\n    const handleDescription = (value: string) => {\r\n        setInitialVal((prevState) => ({\r\n            ...prevState,\r\n            description: value,\r\n        }));\r\n    };\r\n\r\n    useEffect(() => {\r\n        const hazardTypeParams = {\r\n            query: {},\r\n            sort: { title: \"asc\" },\r\n            limit: \"~\",\r\n        };\r\n\r\n        if (editform) {\r\n            const getHazardTypeData = async () => {\r\n                const response: HazardType = await apiService.get(`/hazardtype/${props.routes[1]}`, hazardTypeParams);\r\n                setInitialVal((prevState) => ({ ...prevState, ...response }));\r\n            };\r\n            getHazardTypeData();\r\n        }\r\n    }, []);\r\n\r\n    const formRef = useRef(null);\r\n\r\n    return (\r\n        <Container className=\"formCard\" fluid>\r\n            <Card\r\n                style={{\r\n                    marginTop: \"5px\",\r\n                    boxShadow: \"0 4px 8px 0 rgba(0, 0, 0, 0.2), 0 6px 20px 0 rgba(0, 0, 0, 0.19)\",\r\n                }}\r\n            >\r\n                <ValidationFormWrapper onSubmit={handleSubmit} ref={formRef} initialValues={initialVal} enableReinitialize={true}>\r\n                    <Card.Body>\r\n                        <Row>\r\n                            <Col>\r\n                                <Card.Title>{editform ? t(\"adminsetting.hazardtypes.EditHazardType\") : t(\"adminsetting.hazardtypes.AddHazardType\")}</Card.Title>\r\n                            </Col>\r\n                        </Row>\r\n                        <hr />\r\n                        <Row className=\"mb-3\">\r\n                            <Col md lg={6} sm={12}>\r\n                                <Form.Group>\r\n                                    <Form.Label className=\"required-field\">\r\n                                        {t(\"adminsetting.hazardtypes.HazardTypeName\")}\r\n                                    </Form.Label>\r\n                                    <TextInput\r\n                                        name=\"title\"\r\n                                        id=\"title\"\r\n                                        required\r\n                                        value={initialVal.title}\r\n                                        errorMessage={{\r\n                                            validator: t(\"adminsetting.hazardtypes.Add\")}}\r\n                                        onChange={handleChange}\r\n                                    />\r\n                                </Form.Group>\r\n                            </Col>\r\n                            <Col md lg={6} sm={12}>\r\n                                <Form.Group>\r\n                                    <Form.Label className=\"required-field\">\r\n                                        {t(\"adminsetting.hazardtypes.Code\")}\r\n                                    </Form.Label>\r\n                                    <TextInput\r\n                                        name=\"code\"\r\n                                        id=\"code\"\r\n                                        required\r\n                                        value={initialVal.code}\r\n                                        validator={(value: string) => String(value || '').trim() !== \"\"}\r\n                                        errorMessage={{\r\n                                            validator: t(\"adminsetting.hazardtypes.Please\"),\r\n                                        }}\r\n                                        onChange={handleChange}\r\n                                    />\r\n                                </Form.Group>\r\n                            </Col>\r\n                        </Row>\r\n                        <Row>\r\n                            <Col>\r\n                                <Form.Group>\r\n                                    <Form.Label>{t(\"Description\")}</Form.Label>\r\n                                    <EditorComponent initContent={initialVal.description} onChange={(evt: any) => handleDescription(evt)} />\r\n                                </Form.Group>\r\n                            </Col>\r\n                        </Row>\r\n                        <Row className=\"my-4\">\r\n                            <Col>\r\n                                <Button className=\"me-2\" type=\"submit\" variant=\"primary\">\r\n                                    {t(\"submit\")}\r\n                                </Button>\r\n                                <Button className=\"me-2\" onClick={resetHandler} variant=\"info\">\r\n                                    {t(\"reset\")}\r\n                                </Button>\r\n                                <Link\r\n                                    href=\"/adminsettings/[...routes]\"\r\n                                    as={`/adminsettings/hazardTypes`}\r\n                                    >\r\n                                    <Button variant=\"secondary\">{t(\"Cancel\")}</Button>\r\n                                </Link>\r\n                            </Col>\r\n                        </Row>\r\n                    </Card.Body>\r\n                </ValidationFormWrapper>\r\n            </Card>\r\n        </Container>\r\n    );\r\n};\r\n\r\nexport default HazardTypeForm;\r\n", "//Import Library\r\nimport { Container, <PERSON>, <PERSON>, Button } from \"react-bootstrap\";\r\nimport Link from \"next/link\";\r\n\r\n//Import services/components\r\nimport PageHeading from \"../../../components/common/PageHeading\";\r\nimport EventstatusTable from \"./eventstatusTable\";\r\nimport { useTranslation } from 'next-i18next';\r\nimport { canAddEventStatus } from \"../permissions\";\r\nimport { useSelector } from \"react-redux\";\r\nimport NoAccessMessage from \"../../rNoAccess\";\r\n\r\nconst EventstatusIndex = (props: any) => {\r\n  const { t } = useTranslation('common');\r\n  const ShowEventstatusIndex = () => {\r\n    return (\r\n      <div>\r\n        <Container style={{ overflowX: \"hidden\" }} fluid className=\"p-0\">\r\n          <Row>\r\n            <Col xs={12}>\r\n              <PageHeading title={t(\"adminsetting.EventStatus.Forms.AddEventStatus\")} />\r\n            </Col>\r\n          </Row>\r\n          <Row>\r\n            <Col xs={12}>\r\n              <Link\r\n                href=\"/adminsettings/[...routes]\"\r\n                as=\"/adminsettings/create_eventstatus\"\r\n                >\r\n                <Button variant=\"secondary\" size=\"sm\">\r\n                {t(\"adminsetting.EventStatus.Forms.AddEventStatus\")}\r\n              </Button>\r\n              </Link>\r\n            </Col>\r\n          </Row>\r\n          <Row className=\"mt-3\">\r\n            <Col xs={12}>\r\n              <EventstatusTable />\r\n            </Col>\r\n          </Row>\r\n        </Container>\r\n      </div>\r\n    );\r\n  }\r\n\r\n  const ShowAddEventStatus = canAddEventStatus(() => <ShowEventstatusIndex />);\r\n  const state:any = useSelector((state) => state);\r\n  if (!(state?.permissions?.event_status?.['create:any'])) {\r\n    return <NoAccessMessage />\r\n  }\r\n  return(\r\n    <ShowAddEventStatus />\r\n  )\r\n}\r\nexport default EventstatusIndex;", "//Import Library\r\nimport { Contain<PERSON>, <PERSON>, <PERSON>, Button } from \"react-bootstrap\";\r\nimport Link from \"next/link\";\r\n\r\n//Import services/components\r\nimport PageHeading from \"../../../components/common/PageHeading\";\r\nimport InstitutionTypeTable from \"./institutionTypeTable\";\r\nimport { useTranslation } from 'next-i18next';\r\nimport { canAddOrganisationTypes } from \"../permissions\";\r\nimport { useSelector } from \"react-redux\";\r\nimport NoAccessMessage from \"../../rNoAccess\";\r\n\r\n\r\n\r\nconst InstitutionTypeIndex = (_props: any) => {\r\n  const { t } = useTranslation('common');\r\n  const ShowInstitutionTypeIndex = () => {\r\n    return (\r\n      <Container style={{ overflowX: \"hidden\" }} fluid className=\"p-0\">\r\n        <Row>\r\n          <Col xs={12}>\r\n            <PageHeading title={t(\"adminsetting.Organisationtypes.OrganisationType\")} />\r\n          </Col>\r\n        </Row>\r\n        <Row>\r\n          <Col xs={12}>\r\n            <Link\r\n              href=\"/adminsettings/[...routes]\"\r\n              as=\"/adminsettings/create_institution_type\"\r\n              >\r\n              <Button variant=\"secondary\" size=\"sm\">\r\n              {t(\"adminsetting.Organisationtypes.AddOrganisationType\")}\r\n              </Button>\r\n            </Link>\r\n          </Col>\r\n        </Row>\r\n        <Row className=\"mt-3\">\r\n          <Col xs={12}>\r\n            <InstitutionTypeTable />\r\n          </Col>\r\n        </Row>\r\n      </Container>\r\n    );\r\n  }\r\n  \r\n  const ShowAddOrganisationTypes = canAddOrganisationTypes(() => <ShowInstitutionTypeIndex />);\r\n  const state:any = useSelector((state) => state);\r\n  if (!(state?.permissions?.institution_type?.['create:any'])) {\r\n    return <NoAccessMessage />\r\n  }\r\n  return(\r\n    <ShowAddOrganisationTypes />\r\n  )\r\n}\r\nexport default InstitutionTypeIndex;", "//Import Library\r\nimport { useState, useEffect, useMemo } from \"react\";\r\nimport Link from \"next/link\";\r\nimport { <PERSON><PERSON>, But<PERSON> } from \"react-bootstrap\";\r\nimport _ from \"lodash\";\r\nimport toast from 'react-hot-toast';\r\n\r\n//Import services/components\r\nimport RKITable from \"../../../components/common/RKITable\";\r\nimport apiService from \"../../../services/apiService\";\r\nimport CountryTableFilter from \"./countryTableFilter\";\r\nimport { useTranslation } from 'next-i18next';\r\n\r\nconst CountryTable = (_props: any) => {\r\n    const { t, i18n } = useTranslation('common');\r\n    const titleSearch = i18n.language === \"de\" ? { title_de: \"asc\" } : { title: \"asc\" };\r\n    const currentLang = i18n.language;\r\n    const [tabledata, setDataToTable] = useState([]);\r\n    const [, setLoading] = useState(false);\r\n    const [totalRows, setTotalRows] = useState(0);\r\n    const [perPage, setPerPage] = useState(10);\r\n    const [isModalShow, setModal] = useState(false);\r\n    const [selectCountryDetails, setSelectCountry] = useState({});\r\n    const [filterText, setFilterText] = useState(\"\");\r\n    const [resetPaginationToggle, setResetPaginationToggle] = useState(false);\r\n\r\n\r\n    const countryParams = {\r\n        sort: titleSearch,\r\n        limit: perPage,\r\n        page: 1,\r\n        query: {},\r\n        languageCode: currentLang ? currentLang : \"en\",\r\n    };\r\n\r\n    const columns = [\r\n        {\r\n            name: t(\"adminsetting.Countries.Table.Country\"),\r\n            selector: (row: any) => row.title,\r\n            sortable: true,\r\n        },\r\n        {\r\n            name: t(\"adminsetting.Countries.Table.Code\"),\r\n            selector: (row: any) => row.code,\r\n            sortable: true,\r\n        },\r\n        {\r\n            name: t(\"adminsetting.Countries.Table.DialCode\"),\r\n            selector: (row: any) => row.dial_code,\r\n            sortable: true,\r\n        },\r\n        {\r\n            name: t(\"adminsetting.Countries.Table.WorldRegion\"),\r\n            selector: (row: any) => row.world_region?.title || '',\r\n            sortable: true,\r\n        },\r\n        {\r\n            name: t(\"adminsetting.Countries.Table.Action\"),\r\n            selector: (row: any) => row._id,\r\n            sortable: false,\r\n            cell: (d: any) => (\r\n                <div>\r\n                    <Link href=\"/adminsettings/[...routes]\" as={`/adminsettings/edit_country/${d._id}`}>\r\n\r\n                        <i className=\"icon fas fa-edit\" />\r\n\r\n                    </Link>\r\n                    &nbsp;\r\n                    <a onClick={() => userAction(d)}>\r\n                        <i className=\"icon fas fa-trash-alt\" />\r\n                    </a>\r\n                </div>\r\n            ),\r\n        },\r\n    ];\r\n\r\n    const getCountriesData = async (countryParams_initial: any) => {\r\n        setLoading(true);\r\n        const response = await apiService.get(\"/country\", countryParams_initial);\r\n        if (response) {\r\n            setDataToTable(response.data);\r\n            setTotalRows(response.totalCount);\r\n            setLoading(false);\r\n        }\r\n    };\r\n\r\n    const handlePageChange = (page: any) => {\r\n        countryParams.limit = perPage;\r\n        countryParams.page = page;\r\n        getCountriesData(countryParams);\r\n    };\r\n\r\n    const handlePerRowsChange = async (newPerPage: any, page: any) => {\r\n        countryParams.limit = newPerPage;\r\n        countryParams.page = page;\r\n        setLoading(true);\r\n        const response = await apiService.get(\"/country\", countryParams);\r\n        if (response && response.data && response.data.length > 0) {\r\n            setDataToTable(response.data);\r\n            setPerPage(newPerPage);\r\n            setLoading(false);\r\n        }\r\n    };\r\n\r\n    const userAction = async (row: any) => {\r\n        setSelectCountry(row._id);\r\n        setModal(true);\r\n    };\r\n\r\n    const modalConfirm = async () => {\r\n        try {\r\n            await apiService.remove(`/country/${selectCountryDetails}`);\r\n            getCountriesData(countryParams);\r\n            setModal(false);\r\n            toast.success(t(\"adminsetting.Countries.Table.countryDeletedSuccessfully\"));\r\n        } catch (error) {\r\n            toast.error(t(\"adminsetting.Countries.Table.errorDeletingcountry\"));\r\n        }\r\n    };\r\n\r\n    const modalHide = () => setModal(false);\r\n\r\n    const subHeaderComponentMemo = useMemo(() => {\r\n        const handleClear = () => {\r\n            if (filterText) {\r\n                setResetPaginationToggle(!resetPaginationToggle);\r\n                setFilterText(\"\");\r\n            }\r\n        };\r\n\r\n        const sendQuery = (q: any) => {\r\n            if (q) {\r\n                countryParams.query = { title: q };\r\n            }\r\n            getCountriesData(countryParams);\r\n        };\r\n\r\n        const handleSearchTitle = _.debounce((q: string) => sendQuery(q), Number(process.env.SEARCH_DEBOUNCE_TIME) || 300);\r\n\r\n        const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {\r\n            setFilterText(e.target.value);\r\n            handleSearchTitle(e.target.value);\r\n        };\r\n\r\n        return <CountryTableFilter onFilter={handleChange} onClear={handleClear} filterText={filterText} />;\r\n    }, [filterText]);\r\n\r\n    useEffect(() => {\r\n        getCountriesData(countryParams);\r\n    }, []);\r\n\r\n    return (\r\n        <div>\r\n            <Modal show={isModalShow} onHide={modalHide}>\r\n                <Modal.Header closeButton>\r\n                    <Modal.Title>{t(\"adminsetting.Countries.Table.DeleteCountry\")}</Modal.Title>\r\n                </Modal.Header>\r\n                <Modal.Body>{t(\"adminsetting.Countries.Table.Areyousurewanttodeletethiscountry?\")}</Modal.Body>\r\n                <Modal.Footer>\r\n                    <Button variant=\"secondary\" onClick={modalHide}>\r\n                        {t(\"adminsetting.Countries.Table.Cancel\")}\r\n                    </Button>\r\n                    <Button variant=\"primary\" onClick={modalConfirm}>\r\n                        {t(\"adminsetting.Countries.Table.Yes\")}\r\n                    </Button>\r\n                </Modal.Footer>\r\n            </Modal>\r\n\r\n            <RKITable\r\n                columns={columns}\r\n                data={tabledata}\r\n                totalRows={totalRows}\r\n                subheader\r\n                pagServer={true}\r\n                resetPaginationToggle={resetPaginationToggle}\r\n                subHeaderComponent={subHeaderComponentMemo}\r\n                handlePerRowsChange={handlePerRowsChange}\r\n                handlePageChange={handlePageChange}\r\n            />\r\n        </div>\r\n    );\r\n};\r\n\r\nexport default CountryTable;\r\n", "//Import Library\r\nimport Link from \"next/link\";\r\nimport { useState, useEffect } from \"react\";\r\nimport { Modal, Button, Popover, OverlayTrigger } from \"react-bootstrap\";\r\nimport toast from 'react-hot-toast';\r\n\r\n//Import services/components\r\nimport RKITable from \"../../../components/common/RKITable\";\r\nimport apiService from \"../../../services/apiService\";\r\nimport { useTranslation } from 'next-i18next';\r\n\r\nconst InstitutionNetworkTable = (_props: any) => {\r\n    const [tabledata, setDataToTable] = useState([]);\r\n    const [, setLoading] = useState(false);\r\n    const [totalRows, setTotalRows] = useState(0);\r\n    const [perPage, setPerPage] = useState(10);\r\n    const [isModalShow, setModal] = useState(false);\r\n    const [selectInstitutionNetwork, setSelectInstitutionNetwork] = useState({});\r\n    const modalHide = () => setModal(false);\r\n    const { t } = useTranslation('common');\r\n\r\n\r\n    // For network popover\r\n    const Networkpopover = (\r\n        <Popover id=\"popover-basic\">\r\n            <Popover.Header as=\"h3\" className=\"text-center\">\r\n                NETWORKS\r\n            </Popover.Header>\r\n            <Popover.Body>\r\n                <div className=\"m-2\">\r\n                    <p>\r\n                        <b>EMLab</b> - European Mobile Lab\r\n                    </p>\r\n                    <p>\r\n                        <b>EMT</b> - Emergency Medical Teams\r\n                    </p>\r\n                    <p>\r\n                        <b>GHPP</b> - Global Health Protection Program\r\n                    </p>\r\n                    <p>\r\n                        <b>GOARN</b> - Global Outbreak Alert & Response Network\r\n                    </p>\r\n                    <p>\r\n                        <b>IANPHI</b> - International Association of National Public Health Institutes\r\n                    </p>\r\n                    <p>\r\n                        <b>STAKOB</b> - Ständiger Arbeitskreis der Kompetenz-und Behandlungszentren\r\n                    </p>\r\n                    <p>\r\n                        <b>WHOCC</b>- World Health Organization Collaborating Centres\r\n                    </p>\r\n                </div>\r\n            </Popover.Body>\r\n        </Popover>\r\n    );\r\n    const icons = (\r\n        <OverlayTrigger trigger=\"click\" placement=\"right\" overlay={Networkpopover}>\r\n            <span>\r\n                {t(\"Title\")}&nbsp;&nbsp;&nbsp;\r\n                <i className=\"fa fa-info-circle\" style={{ cursor: \"pointer\" }} aria-hidden=\"true\"></i>\r\n            </span>\r\n        </OverlayTrigger>\r\n    );\r\n    // End\r\n\r\n    const columns = [\r\n        {\r\n            name: icons,\r\n            selector: \"title\",\r\n        },\r\n        {\r\n            name: t(\"action\"),\r\n            selector: \"\",\r\n            cell: (d: any) => (\r\n                <div>\r\n                    <Link href=\"/adminsettings/[...routes]\" as={`/adminsettings/edit_institution_network/${d._id}`}>\r\n\r\n                        <i className=\"icon fas fa-edit\" />\r\n\r\n                    </Link>\r\n                    &nbsp;\r\n                    <a onClick={() => userAction(d)}>\r\n                        <i className=\"icon fas fa-trash-alt\" />\r\n                    </a>\r\n                </div>\r\n            ),\r\n        },\r\n    ];\r\n    const institutionNetworkParams = {\r\n        sort: { title: \"asc\" },\r\n        limit: perPage,\r\n        page: 1,\r\n        query: {},\r\n    };\r\n\r\n    useEffect(() => {\r\n        getInstitutionNetworkData();\r\n    }, []);\r\n\r\n    const getInstitutionNetworkData = async () => {\r\n        setLoading(true);\r\n        const response = await apiService.get(\"/institutionnetwork\", institutionNetworkParams);\r\n        if (response && response.data && response.data.length > 0) {\r\n            setDataToTable(response.data);\r\n            setTotalRows(response.totalCount);\r\n            setLoading(false);\r\n        }\r\n    };\r\n\r\n    const handlePageChange = (page: any) => {\r\n        institutionNetworkParams.limit = perPage;\r\n        institutionNetworkParams.page = page;\r\n        getInstitutionNetworkData();\r\n    };\r\n\r\n    const handlePerRowsChange = async (newPerPage: any, page: any) => {\r\n        institutionNetworkParams.limit = newPerPage;\r\n        institutionNetworkParams.page = page;\r\n        setLoading(true);\r\n        const response = await apiService.get(\"/institutionnetwork\", institutionNetworkParams);\r\n        if (response && response.data && response.data.length > 0) {\r\n            setDataToTable(response.data);\r\n            setPerPage(newPerPage);\r\n            setLoading(false);\r\n        }\r\n    };\r\n\r\n    const modalConfirm = async () => {\r\n        try {\r\n            await apiService.remove(`/institutionnetwork/${selectInstitutionNetwork}`);\r\n            getInstitutionNetworkData();\r\n            setModal(false);\r\n            toast.success(t(\"adminsetting.Organisationnetworks.Table.orgNetworkDeletedSuccessfully\"));\r\n        } catch (error) {\r\n            toast.error(t(\"adminsetting.Organisationnetworks.Table.errorDeletingOrgNetwork\"));\r\n        }\r\n    };\r\n\r\n    const userAction = async (row: any) => {\r\n        setSelectInstitutionNetwork(row._id);\r\n        setModal(true);\r\n    };\r\n\r\n    return (\r\n        <div>\r\n            <Modal show={isModalShow} onHide={modalHide}>\r\n                <Modal.Header closeButton>\r\n                    <Modal.Title>{t(\"adminsetting.Organisationnetworks.Delete\")}</Modal.Title>\r\n                </Modal.Header>\r\n                <Modal.Body>{t(\"adminsetting.Organisationnetworks.sure\")}</Modal.Body>\r\n                <Modal.Footer>\r\n                    <Button variant=\"secondary\" onClick={modalHide}>\r\n                        {t(\"cancel\")}\r\n                    </Button>\r\n                    <Button variant=\"primary\" onClick={modalConfirm}>\r\n                        {t(\"yes\")}\r\n                    </Button>\r\n                </Modal.Footer>\r\n            </Modal>\r\n\r\n            <RKITable\r\n                columns={columns}\r\n                data={tabledata}\r\n                totalRows={totalRows}\r\n                pagServer={true}\r\n                handlePerRowsChange={handlePerRowsChange}\r\n                handlePageChange={handlePageChange}\r\n            />\r\n        </div>\r\n    );\r\n};\r\nexport default InstitutionNetworkTable;\r\n", "//Import Library\r\nimport { Container, <PERSON>, <PERSON>, Button } from \"react-bootstrap\";\r\nimport Link from \"next/link\";\r\n\r\n//Import services/components\r\nimport DeploymentstatusTable from \"./deploymentstatusTable\";\r\nimport { useTranslation } from 'next-i18next';\r\nimport { serverSideTranslations } from 'next-i18next/serverSideTranslations';\r\nimport PageHeading from \"../../../components/common/PageHeading\";\r\nimport { canAddDeploymentStatus } from \"../permissions\";\r\nimport { useSelector } from \"react-redux\";\r\nimport NoAccessMessage from \"../../rNoAccess\";\r\n\r\nconst DeploymentstatusIndex = (_props: any) => {\r\n  const { t } = useTranslation('common');\r\n  const ShowDeploymentstatusIndex = () => {\r\n    return (\r\n      <div>\r\n        <Container style={{ overflowX: \"hidden\" }} fluid className=\"p-0\">\r\n          <Row>\r\n            <Col xs={12}>\r\n              <PageHeading title={t(\"adminsetting.DeploymentStatus.Forms.DeploymentStatus\")} />\r\n            </Col>\r\n          </Row>\r\n          <Row>\r\n            <Col xs={12}>\r\n              <Link\r\n                href=\"/adminsettings/[...routes]\"\r\n                as=\"/adminsettings/create_deploymentstatus\"\r\n                >\r\n                <Button variant=\"secondary\" size=\"sm\">\r\n                {t(\"adminsetting.DeploymentStatus.Forms.AddDeploymentStatus\")}\r\n              </Button>\r\n              </Link>\r\n            </Col>\r\n          </Row>\r\n          <Row className=\"mt-3\">\r\n            <Col xs={12}>\r\n              <DeploymentstatusTable />\r\n            </Col>\r\n          </Row>\r\n        </Container>\r\n      </div>\r\n    );\r\n  }\r\n  const ShowAddDeploymentStatus = canAddDeploymentStatus(() => <ShowDeploymentstatusIndex />);\r\n  const state:any = useSelector((state) => state);\r\n  if (!(state?.permissions?.deployment_status?.['create:any'])) {\r\n    return <NoAccessMessage />\r\n  }\r\n  return(\r\n      <ShowAddDeploymentStatus />\r\n  )\r\n}\r\n\r\nexport async function getServerSideProps({ locale }: any) {\r\n  return {\r\n    props: {\r\n      ...(await serverSideTranslations(locale, ['common'])),\r\n    },\r\n  }\r\n}\r\n\r\nexport default DeploymentstatusIndex;", "//Import Library\r\nimport { useState, useRef, useEffect } from \"react\";\r\nimport { Container, Card, Row, Col, Form, Button } from \"react-bootstrap\";\r\nimport { TextInput } from \"../../../components/common/FormValidation\";\r\nimport ValidationFormWrapper from \"../../../components/common/ValidationFormWrapper\";\r\nimport Router from \"next/router\";\r\nimport toast from 'react-hot-toast';\r\nimport Link from \"next/link\";\r\n\r\n//Import services/components\r\nimport { UpdateType } from \"../../../types\";\r\nimport apiService from \"../../../services/apiService\";\r\nimport { useTranslation } from 'next-i18next';\r\n\r\ninterface UpdateTypeFormProps {\r\n    [key: string]: any;\r\n}\r\n\r\nconst UpdateTypeForm = (props: UpdateTypeFormProps) => {\r\n    const _initialupdateType = {\r\n        title: \"\",\r\n        icon: \"\",\r\n    };\r\n    const { t } = useTranslation('common');\r\n    const [initialVal, setInitialVal] = useState<UpdateType>(_initialupdateType);\r\n\r\n    const editform = props.routes && props.routes[0] === \"edit_update_type\" && props.routes[1];\r\n\r\n    const formRef = useRef(null);\r\n\r\n    const resetHandler = () => {\r\n        setInitialVal(_initialupdateType);\r\n        // Reset validation state (Formik handles this automatically)\r\n        window.scrollTo(0, 0);\r\n    };\r\n\r\n    const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement>) => {\r\n        if (e.target) {\r\n            const { name, value } = e.target;\r\n            setInitialVal((prevState) => ({\r\n                ...prevState,\r\n                [name]: value,\r\n            }));\r\n        }\r\n    };\r\n\r\n    const handleSubmit = async (event: any, values?: any) => {\r\n        event.preventDefault();\r\n        const obj = {\r\n            title: initialVal.title.trim(),\r\n            icon: initialVal.icon,\r\n        };\r\n\r\n        let response;\r\n        let toastMsg;\r\n        if (editform) {\r\n            toastMsg = \"adminsetting.updatestype.Updatetypeisupdatedsuccessfully\";\r\n            response = await apiService.patch(`/updatetype/${props.routes[1]}`, obj);\r\n        } else {\r\n            toastMsg = \"adminsetting.updatestype.Updatetypeisaddedsuccessfully\";\r\n            response = await apiService.post(\"/updatetype\", obj);\r\n        }\r\n        if (response && response._id) {\r\n            toast.success(t(toastMsg));\r\n            Router.push(\"/adminsettings/update_type\");\r\n        } else {\r\n            if (response?.errorCode === 11000) {\r\n                toast.error(t(\"duplicatesNotAllowed\"));\r\n            } else {\r\n                toast.error(response);\r\n            }\r\n        }\r\n    };\r\n\r\n    useEffect(() => {\r\n        const updateTypeParams = {\r\n            query: {},\r\n            sort: { title: \"asc\" },\r\n            limit: \"~\",\r\n        };\r\n        if (editform) {\r\n            const getUpdateTypeData = async () => {\r\n                const response: UpdateType = await apiService.get(`/updatetype/${props.routes[1]}`, updateTypeParams);\r\n                setInitialVal((prevState) => ({ ...prevState, ...response }));\r\n            };\r\n            getUpdateTypeData();\r\n        }\r\n    }, []);\r\n\r\n    return (\r\n        <div>\r\n            <Container className=\"formCard\" fluid>\r\n                <Card\r\n                    style={{\r\n                        marginTop: \"5px\",\r\n                        boxShadow: \"0 4px 8px 0 rgba(0, 0, 0, 0.2), 0 6px 20px 0 rgba(0, 0, 0, 0.19)\",\r\n                    }}\r\n                >\r\n                    <ValidationFormWrapper onSubmit={handleSubmit} ref={formRef} initialValues={initialVal} enableReinitialize={true}>\r\n                        <Card.Body>\r\n                            <Row>\r\n                                <Col>\r\n                                    <Card.Title>{t(\"adminsetting.updatestype.UpdateType\")}</Card.Title>\r\n                                </Col>\r\n                            </Row>\r\n                            <hr />\r\n                            <Row>\r\n                                <Col md lg={6} sm={12}>\r\n                                    <Form.Group>\r\n                                        <Form.Label className=\"required-field\">\r\n                                            {t(\"adminsetting.updatestype.UpdateType\")}\r\n                                        </Form.Label>\r\n                                        <TextInput\r\n                                            name=\"title\"\r\n                                            id=\"title\"\r\n                                            required\r\n                                            value={initialVal.title}\r\n                                            validator={(value: string) => value.trim() !== \"\"}\r\n                                            errorMessage={{\r\n                                                validator: t(\"adminsetting.updatestype.PleaseAddtheUpdateType\"),\r\n                                            }}\r\n                                            onChange={handleChange}\r\n                                        />\r\n                                    </Form.Group>\r\n                                </Col>\r\n                                <Col md lg={6} sm={12}>\r\n                                    <Form.Group>\r\n                                        <Form.Label>{t(\"adminsetting.updatestype.Icon\")}</Form.Label>\r\n                                        <TextInput\r\n                                            name=\"icon\"\r\n                                            id=\"icon\"\r\n                                            value={initialVal.icon}\r\n                                            errorMessage={t(\"adminsetting.updatestype.PleaseAddtheicon\")}\r\n                                            onChange={handleChange}\r\n                                        />\r\n                                    </Form.Group>\r\n                                </Col>\r\n                            </Row>\r\n                            <Row className=\"my-4\">\r\n                                <Col>\r\n                                    <Button className=\"me-2\" type=\"submit\" variant=\"primary\">\r\n                                        {t(\"adminsetting.updatestype.Submit\")}\r\n                                    </Button>\r\n                                    <Button className=\"me-2\" onClick={resetHandler} variant=\"info\">\r\n                                        {t(\"adminsetting.updatestype.Reset\")}\r\n                                    </Button>\r\n                                    <Link\r\n                                        href=\"/adminsettings/[...routes]\"\r\n                                        as={`/adminsettings/update_type`}\r\n                                        >\r\n                                        <Button variant=\"secondary\">{t(\"adminsetting.updatestype.Cancel\")}</Button>\r\n                                    </Link>\r\n                                </Col>\r\n                            </Row>\r\n                        </Card.Body>\r\n                    </ValidationFormWrapper>\r\n                </Card>\r\n            </Container>\r\n        </div>\r\n    );\r\n};\r\nexport default UpdateTypeForm;\r\n", "//Import Library\r\nimport Link from \"next/link\";\r\nimport { useState, useEffect } from \"react\";\r\nimport { <PERSON><PERSON>, But<PERSON> } from \"react-bootstrap\";\r\n\r\nimport toast from 'react-hot-toast';\r\n\r\n//Import services/components\r\nimport RKITable from \"../../../components/common/RKITable\";\r\nimport { useTranslation } from 'next-i18next';\r\nimport apiService from \"../../../services/apiService\";\r\n\r\nconst UpdateTypeTable = (_props: any) => {\r\n    const { t } = useTranslation('common');\r\n    const [tabledata, setDataToTable] = useState([]);\r\n    const [, setLoading] = useState(false);\r\n    const [totalRows, setTotalRows] = useState(0);\r\n    const [perPage, setPerPage] = useState(10);\r\n    const [isModalShow, setModal] = useState(false);\r\n    const [selectUpdateType, setSelectUpdateType] = useState({});\r\n    const modalHide = () => setModal(false);\r\n    \r\n    const columns = [\r\n        {\r\n            name: t(\"adminsetting.updatestype.Title\"),\r\n            selector: \"title\",\r\n        },\r\n        {\r\n            name: t(\"adminsetting.updatestype.Icon\"),\r\n            selector: \"icon\",\r\n            cell: (d: any) => <i className={`fas ${d.icon}`}></i>,\r\n        },\r\n        {\r\n            name: t(\"adminsetting.updatestype.Action\"),\r\n            selector: \"\",\r\n            cell: (d: any) => (\r\n                <div>\r\n                    <Link href=\"/adminsettings/[...routes]\" as={`/adminsettings/edit_update_type/${d._id}`}>\r\n\r\n                        <i className=\"icon fas fa-edit\" />\r\n\r\n                    </Link>\r\n                    &nbsp;\r\n                    <a onClick={() => userAction(d)}>\r\n                        <i className=\"icon fas fa-trash-alt\" />\r\n                    </a>{\" \"}\r\n                </div>\r\n            ),\r\n        },\r\n    ];\r\n    const updateTypeParams = {\r\n        sort: { title: \"asc\" },\r\n        limit: perPage,\r\n        page: 1,\r\n        query: {},\r\n    };\r\n\r\n    useEffect(() => {\r\n        getUpdateTypeData(updateTypeParams);\r\n    }, []);\r\n\r\n    const getUpdateTypeData = async (updateTypeParams_initials: any) => {\r\n        setLoading(true);\r\n        const response = await apiService.get(\"/updatetype\", updateTypeParams_initials);\r\n        if (response && response.data && response.data.length > 0) {\r\n            setDataToTable(response.data);\r\n            setTotalRows(response.totalCount);\r\n            setLoading(false);\r\n        }\r\n    };\r\n\r\n    const handlePageChange = (page: any) => {\r\n        updateTypeParams.limit = perPage;\r\n        updateTypeParams.page = page;\r\n        getUpdateTypeData(updateTypeParams);\r\n    };\r\n\r\n    const handlePerRowsChange = async (newPerPage: any, page: any) => {\r\n        updateTypeParams.limit = newPerPage;\r\n        updateTypeParams.page = page;\r\n        setLoading(true);\r\n        const response = await apiService.get(\"/updatetype\", updateTypeParams);\r\n        if (response && response.data && response.data.length > 0) {\r\n            setDataToTable(response.data);\r\n            setPerPage(newPerPage);\r\n            setLoading(false);\r\n        }\r\n    };\r\n\r\n    const modalConfirm = async () => {\r\n        try {\r\n            await apiService.remove(`/updatetype/${selectUpdateType}`);\r\n            getUpdateTypeData(updateTypeParams);\r\n            setModal(false);\r\n            toast.success(t(\"adminsetting.updatestype.Table.updateTypeDeletedSuccessfully\"));\r\n        } catch (error) {\r\n            toast.error(t(\"adminsetting.updatestype.Table.errorDeletingUpdateType\"));\r\n        }\r\n    };\r\n\r\n    const userAction = async (row: any) => {\r\n        setSelectUpdateType(row._id);\r\n        setModal(true);\r\n    };\r\n\r\n    return (\r\n        <div>\r\n            <Modal show={isModalShow} onHide={modalHide}>\r\n                <Modal.Header closeButton>\r\n                    <Modal.Title>{t(\"adminsetting.updatestype.DeleteUpdateType\")}</Modal.Title>\r\n                </Modal.Header>\r\n                <Modal.Body>{t(\"adminsetting.updatestype.Areyousurewanttodeletethisupdatetype?\")}</Modal.Body>\r\n                <Modal.Footer>\r\n                    <Button variant=\"secondary\" onClick={modalHide}>\r\n                        {t(\"adminsetting.updatestype.Cancel\")}\r\n                    </Button>\r\n                    <Button variant=\"primary\" onClick={modalConfirm}>\r\n                        {t(\"adminsetting.updatestype.Yes\")}\r\n                    </Button>\r\n                </Modal.Footer>\r\n            </Modal>\r\n\r\n            <RKITable\r\n                columns={columns}\r\n                data={tabledata}\r\n                totalRows={totalRows}\r\n                pagServer={true}\r\n                handlePerRowsChange={handlePerRowsChange}\r\n                handlePageChange={handlePageChange}\r\n            />\r\n        </div>\r\n    );\r\n};\r\nexport default UpdateTypeTable;\r\n", "//Import Library\r\nimport { Container } from \"react-bootstrap\";\r\n\r\n//Import services/components\r\nimport AdminTable from \"./AdminTable\";\r\nimport PageHeading from \"../../../components/common/PageHeading\";\r\nimport { useTranslation } from 'next-i18next';\r\nimport { serverSideTranslations } from 'next-i18next/serverSideTranslations';\r\nimport { canAddFocalPointApproval } from \"../permissions\";\r\nimport { useSelector } from \"react-redux\";\r\nimport NoAccessMessage from \"../../rNoAccess\";\r\n\r\nconst FocalPointShow = (_props: any) => {\r\n  const { t } = useTranslation('common');\r\n  const ShowFocalPoint = () => {\r\n    return (\r\n      <Container fluid className=\"p-0\">\r\n        <PageHeading title={t(\"adminsetting.FocalPointsApproval\")} />\r\n        <AdminTable />\r\n      </Container>\r\n    )\r\n  };\r\n\r\n  const ShowAddFocalPointApproval = canAddFocalPointApproval(() => <ShowFocalPoint />);\r\n  const state:any = useSelector((state: any) => state);\r\n  if (!(state?.permissions?.institution_focal_point?.['create:any'])) {\r\n    return <NoAccessMessage />\r\n  }\r\n  return(\r\n    <ShowAddFocalPointApproval />\r\n  );\r\n}\r\n\r\nexport async function getServerSideProps({ locale }: { locale: string }) {\r\n  return {\r\n    props: {\r\n      ...(await serverSideTranslations(locale, ['common'])),\r\n    },\r\n  }\r\n}\r\n\r\nexport default FocalPointShow;\r\n", "//Import Library\r\nimport { useState, useEffect } from \"react\";\r\nimport { <PERSON><PERSON>, <PERSON><PERSON> } from \"react-bootstrap\";\r\nimport Link from \"next/link\";\r\n\r\n//Import services/components\r\nimport RKITable from \"../../../components/common/RKITable\";\r\nimport apiService from \"../../../services/apiService\";\r\nimport { useTranslation } from 'next-i18next';\r\n\r\n\r\nconst CategoryTable = (_props: any) => {\r\n  const [tabledata, setDataToTable] = useState([]);\r\n  const [ ,setLoading] = useState(false);\r\n  const [totalRows, setTotalRows] = useState(0);\r\n  const [perPage, setPerPage] = useState(10);\r\n  const [isModalShow, setModal] = useState(false);\r\n  const [selectCategory, setSelectCategory] = useState({});\r\n  const { t } = useTranslation('common');\r\n\r\n\r\n  const categoryParams = {\r\n    \"sort\": { \"title\": \"asc\" },\r\n    \"limit\": perPage,\r\n    \"page\": 1,\r\n    \"query\": {}\r\n  };\r\n\r\n  const columns = [\r\n    {\r\n      name: 'Title',\r\n      selector: 'title',\r\n    },\r\n    {\r\n      name: 'Action',\r\n      selector: \"\",\r\n      cell: (d: any) => <div><Link href=\"/adminsettings/[...routes]\" as={`/adminsettings/edit_category/${d._id}`} ><i className=\"icon fas fa-edit\" /></Link>&nbsp;<Link href=\"#\" onClick={() => userAction(d)}><i className=\"icon fas fa-trash-alt\" /></Link> </div>\r\n    }\r\n  ];\r\n\r\n  const getCategoryData = async () => {\r\n    setLoading(true);\r\n    const response = await apiService.get('/category', categoryParams);\r\n    if (response && response.data && response.data.length > 0) {\r\n      setDataToTable(response.data);\r\n      setTotalRows(response.totalCount);\r\n      setLoading(false);\r\n    }\r\n  };\r\n\r\n  const handlePageChange = (page: any) => {\r\n    categoryParams.limit = perPage;\r\n    categoryParams.page = page;\r\n    getCategoryData();\r\n  };\r\n\r\n  const handlePerRowsChange = async (newPerPage: any, page: any) => {\r\n    categoryParams.limit = newPerPage;\r\n    categoryParams.page = page;\r\n    setLoading(true);\r\n    const response = await apiService.get('/category', categoryParams);\r\n    if (response && response.data && response.data.length > 0) {\r\n      setDataToTable(response.data);\r\n      setPerPage(newPerPage);\r\n      setLoading(false);\r\n    }\r\n  };\r\n\r\n  const userAction = async (row: any) => {\r\n    setSelectCategory(row._id);\r\n    setModal(true);\r\n  }\r\n\r\n  const modalConfirm = async () => {\r\n    await apiService.remove(`/category/${selectCategory}`);\r\n    getCategoryData();\r\n    setModal(false);\r\n  }\r\n\r\n  const modalHide = () => setModal(false);\r\n\r\n  useEffect(() => {\r\n    getCategoryData();\r\n  }, []);\r\n\r\n  return (\r\n    <div>\r\n      <Modal show={isModalShow} onHide={modalHide}>\r\n        <Modal.Header closeButton>\r\n          <Modal.Title>{t(\"DeleteCategory\")}</Modal.Title>\r\n        </Modal.Header>\r\n        <Modal.Body>{t(\"Areyousurewanttodeletethiscategory\")} </Modal.Body>\r\n        <Modal.Footer>\r\n          <Button variant=\"secondary\" onClick={modalHide}>\r\n          {t(\"cancel\")}\r\n        </Button>\r\n          <Button variant=\"primary\" onClick={modalConfirm}>\r\n          {t(\"yes\")}\r\n        </Button>\r\n        </Modal.Footer>\r\n      </Modal>\r\n\r\n      <RKITable\r\n        columns={columns}\r\n        data={tabledata}\r\n        totalRows={totalRows}\r\n        pagServer={true}\r\n        handlePerRowsChange={handlePerRowsChange}\r\n        handlePageChange={handlePageChange}\r\n      />\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default CategoryTable;", "//Import Library\r\nimport { Container, <PERSON>, <PERSON>, Button } from \"react-bootstrap\";\r\nimport Link from \"next/link\";\r\n\r\n//Import services/components\r\nimport PageHeading from \"../../../components/common/PageHeading\";\r\nimport AreaOfWorkTable from \"./areaOfWorkTable\";\r\nimport { useTranslation } from 'next-i18next';\r\nimport canAddAreaOfWork from \"../permissions\";\r\nimport { useSelector } from \"react-redux\";\r\nimport NoAccessMessage from \"../../rNoAccess\";\r\nimport { useEffect, useState } from \"react\";\r\n\r\nconst AreaOfWorkIndex = (_props: any) => {\r\n  const { t } = useTranslation('common');\r\n  const ShowAreaOfWorkIndex = () => {\r\n    return (\r\n      <div>\r\n        <Container style={{ overflowX: \"hidden\" }} fluid className=\"p-0\">\r\n          <Row>\r\n            <Col xs={12}>\r\n              <PageHeading title={t(\"adminsetting.areaofwork.Forms.Addareaofwork\")} />\r\n            </Col>\r\n          </Row>\r\n          <Row>\r\n            <Col xs={12}>\r\n              <Link\r\n                href=\"/adminsettings/[...routes]\"\r\n                as=\"/adminsettings/create_area_of_work\"\r\n                >\r\n                <Button variant=\"secondary\" size=\"sm\">\r\n                  {t(\"adminsetting.areaofwork.Forms.Addareaofwork\")}\r\n                </Button>\r\n              </Link>\r\n            </Col>\r\n          </Row>\r\n          <Row className=\"mt-3\">\r\n            <Col xs={12}>\r\n              <AreaOfWorkTable />\r\n            </Col>\r\n          </Row>\r\n        </Container>\r\n      </div>\r\n    );\r\n  }\r\n\r\n  const ShowAddAreaOfWork = canAddAreaOfWork(() => <ShowAreaOfWorkIndex />);\r\n  const state:any = useSelector((state: any) => state);\r\n  if (!(state?.permissions?.area_of_work?.['create:any'])) {\r\n    return <NoAccessMessage />\r\n  }\r\n  return <ShowAddAreaOfWork />\r\n}\r\nexport default AreaOfWorkIndex", "//Import Library\r\nimport { useState, useEffect } from \"react\";\r\nimport Link from \"next/link\";\r\nimport { <PERSON><PERSON>, Button } from \"react-bootstrap\";\r\nimport toast from 'react-hot-toast';\r\n\r\n//Import services/components\r\nimport { useTranslation } from 'next-i18next';\r\nimport RKITable from \"../../../components/common/RKITable\";\r\nimport apiService from \"../../../services/apiService\";\r\n\r\nconst EventstatusTable = (_props: any) => {\r\n    const { t } = useTranslation('common');\r\n    const [tabledata, setDataToTable] = useState([]);\r\n    const [, setLoading] = useState(false);\r\n    const [totalRows, setTotalRows] = useState(0);\r\n    const [perPage, setPerPage] = useState(10);\r\n    const [isModalShow, setModal] = useState(false);\r\n    const [selectEventstatus, setSelectEventstatus] = useState({});\r\n\r\n\r\n    const eventstatusParams = {\r\n        sort: { title: \"asc\" },\r\n        limit: perPage,\r\n        page: 1,\r\n        query: {},\r\n    };\r\n\r\n    const columns = [\r\n        {\r\n            name: t(\"adminsetting.EventStatus.Table.Title\"),\r\n            selector: \"title\",\r\n        },\r\n        {\r\n            name: t(\"adminsetting.EventStatus.Table.Action\"),\r\n            selector: \"\",\r\n            cell: (d: any) => (\r\n                <div>\r\n                    <Link href=\"/adminsettings/[...routes]\" as={`/adminsettings/edit_eventstatus/${d._id}`}>\r\n\r\n                        <i className=\"icon fas fa-edit\" />\r\n\r\n                    </Link>\r\n                    &nbsp;\r\n                    <a onClick={() => userAction(d)}>\r\n                        <i className=\"icon fas fa-trash-alt\" />\r\n                    </a>\r\n                </div>\r\n            ),\r\n        },\r\n    ];\r\n\r\n    const geteventstatusData = async () => {\r\n        setLoading(true);\r\n        const response = await apiService.get(\"/eventstatus\", eventstatusParams);\r\n        if (response && response.data && response.data.length > 0) {\r\n            setDataToTable(response.data);\r\n            setTotalRows(response.totalCount);\r\n            setLoading(false);\r\n        }\r\n    };\r\n\r\n    const handlePageChange = (page: any) => {\r\n        eventstatusParams.limit = perPage;\r\n        eventstatusParams.page = page;\r\n        geteventstatusData();\r\n    };\r\n\r\n    const handlePerRowsChange = async (newPerPage: any, page: any) => {\r\n        eventstatusParams.limit = newPerPage;\r\n        eventstatusParams.page = page;\r\n        setLoading(true);\r\n        const response = await apiService.get(\"/eventstatus\", eventstatusParams);\r\n        if (response && response.data && response.data.length > 0) {\r\n            setDataToTable(response.data);\r\n            setPerPage(newPerPage);\r\n            setLoading(false);\r\n        }\r\n    };\r\n\r\n    const userAction = async (row: any) => {\r\n        setSelectEventstatus(row._id);\r\n        setModal(true);\r\n    };\r\n\r\n    const modalConfirm = async () => {\r\n        try {\r\n            await apiService.remove(`/eventstatus/${selectEventstatus}`);\r\n            geteventstatusData();\r\n            setModal(false);\r\n            toast.success(t(\"adminsetting.EventStatus.Table.eventStatusDeletedSuccessfully\"));\r\n        } catch (error) {\r\n            toast.error(t(\"adminsetting.EventStatus.Table.errorDeletingEventStatus\"));\r\n        }\r\n    };\r\n\r\n    const modalHide = () => setModal(false);\r\n\r\n    useEffect(() => {\r\n        geteventstatusData();\r\n    }, []);\r\n\r\n    return (\r\n        <div>\r\n            <Modal show={isModalShow} onHide={modalHide}>\r\n                <Modal.Header closeButton>\r\n                    <Modal.Title>{t(\"adminsetting.EventStatus.Table.DeleteEventstatus\")}</Modal.Title>\r\n                </Modal.Header>\r\n                <Modal.Body>{t(\"adminsetting.EventStatus.Table.Areyousurewanttodeletethiseventstatus?\")}</Modal.Body>\r\n                <Modal.Footer>\r\n                    <Button variant=\"secondary\" onClick={modalHide}>\r\n                        {t(\"adminsetting.EventStatus.Table.Cancel\")}\r\n                    </Button>\r\n                    <Button variant=\"primary\" onClick={modalConfirm}>\r\n                        {t(\"adminsetting.EventStatus.Table.Yes\")}\r\n                    </Button>\r\n                </Modal.Footer>\r\n            </Modal>\r\n\r\n            <RKITable\r\n                columns={columns}\r\n                data={tabledata}\r\n                totalRows={totalRows}\r\n                pagServer={true}\r\n                handlePerRowsChange={handlePerRowsChange}\r\n                handlePageChange={handlePageChange}\r\n            />\r\n        </div>\r\n    );\r\n};\r\n\r\nexport default EventstatusTable;\r\n", "//Import Library\r\nimport { Contain<PERSON>, <PERSON>, <PERSON>, Button } from \"react-bootstrap\";\r\nimport Link from \"next/link\";\r\n\r\n//Import services/components\r\nimport RisklevelTable from \"./risklevelTable\";\r\nimport { useTranslation } from 'next-i18next';\r\nimport { serverSideTranslations } from 'next-i18next/serverSideTranslations';\r\nimport PageHeading from \"../../../components/common/PageHeading\";\r\nimport { canAddRiskLevels } from \"../permissions\";\r\nimport { useSelector } from \"react-redux\";\r\nimport NoAccessMessage from \"../../rNoAccess\";\r\n\r\n\r\nconst RisklevelIndex = (_props: any) => {\r\n  const { t } = useTranslation('common');\r\n  const ShowRisklevelIndex = () => {\r\n    return (\r\n      <div>\r\n        <Container style={{ overflowX: \"hidden\" }} fluid className=\"p-0\">\r\n          <Row>\r\n            <Col xs={12}>\r\n              <PageHeading title={t(\"adminsetting.RiskLevel.Risklevel\")} />\r\n            </Col>\r\n          </Row>\r\n          <Row>\r\n            <Col xs={12}>\r\n              <Link\r\n                href=\"/adminsettings/[...routes]\"\r\n                as=\"/adminsettings/create_risklevel\"\r\n                >\r\n                <Button variant=\"secondary\" size=\"sm\">\r\n                {t(\"adminsetting.RiskLevel.AddRisklevel\")}\r\n              </Button>\r\n              </Link>\r\n            </Col>\r\n          </Row>\r\n          <Row className=\"mt-3\">\r\n            <Col xs={12}>\r\n              < RisklevelTable />\r\n            </Col>\r\n          </Row>\r\n        </Container>\r\n      </div>\r\n    );\r\n  }\r\n\r\n  const ShowAddRiskLevels = canAddRiskLevels(() => <ShowRisklevelIndex />);\r\n  const state:any = useSelector((state) => state);\r\n  if (!(state?.permissions?.risk_level?.['create:any'])) {\r\n    return <NoAccessMessage />\r\n  }\r\n  return(\r\n    <ShowAddRiskLevels />\r\n  )\r\n}\r\n\r\nexport async function getServerSideProps({ locale } : { locale: string }) {\r\n  return {\r\n    props: {\r\n      ...(await serverSideTranslations(locale, ['common'])),\r\n    },\r\n  }\r\n}\r\n\r\nexport default RisklevelIndex;", "//Import Library\r\nimport { Contain<PERSON>, <PERSON>, <PERSON>, Button } from \"react-bootstrap\";\r\nimport Link from \"next/link\";\r\n\r\n//Import services/components\r\nimport PageHeading from \"../../../components/common/PageHeading\";\r\nimport RoleTable from \"./roleTable\";\r\nimport { useTranslation } from 'next-i18next';\r\n\r\n\r\nconst RoleIndex = (_props: any) => {\r\n  const { t } = useTranslation('common');\r\n  return (\r\n    <div>\r\n      <Container style={{ overflowX: \"hidden\" }} fluid className=\"p-0\">\r\n        <Row>\r\n          <Col xs={12}>\r\n            <PageHeading title=\"Roles\" />\r\n          </Col>\r\n        </Row>\r\n        <Row>\r\n          <Col xs={12}>\r\n            <Link\r\n              href=\"/adminsettings/[...routes]\"\r\n              as=\"/adminsettings/create_role\"\r\n              >\r\n              <Button variant=\"secondary\" size=\"sm\">\r\n              {t(\"AddRole\")}\r\n            </Button>\r\n            </Link>\r\n          </Col>\r\n        </Row>\r\n        <Row className=\"mt-3\">\r\n          <Col xs={12}>\r\n            <RoleTable />\r\n          </Col>\r\n        </Row>\r\n      </Container>\r\n    </div>\r\n  );\r\n}\r\nexport default RoleIndex;", "//Import Library\r\nimport Link from \"next/link\";\r\nimport { useState, useEffect } from \"react\";\r\nimport { <PERSON><PERSON>, But<PERSON> } from \"react-bootstrap\";\r\nimport toast from 'react-hot-toast';\r\n\r\n//Import services/components\r\nimport RKITable from \"../../../components/common/RKITable\";\r\nimport apiService from \"../../../services/apiService\";\r\nimport { useTranslation } from 'next-i18next';\r\n\r\nconst InstitutionTypeTable = (_props: any) => {\r\n    const [tabledata, setDataToTable] = useState([]);\r\n    const [, setLoading] = useState(false);\r\n    const [totalRows, setTotalRows] = useState(0);\r\n    const [perPage, setPerPage] = useState(10);\r\n    const [isModalShow, setModal] = useState(false);\r\n    const [selectInstitutionType, setSelectInstitutionType] = useState({});\r\n    const modalHide = () => setModal(false);\r\n    const { t } = useTranslation('common');\r\n\r\n    const columns = [\r\n        {\r\n            name: t(\"Title\"),\r\n            selector: \"title\",\r\n        },\r\n        {\r\n            name: t(\"action\"),\r\n            selector: \"\",\r\n            cell: (d: any) => (\r\n                <div>\r\n                    <Link href=\"/adminsettings/[...routes]\" as={`/adminsettings/edit_institution_type/${d._id}`}>\r\n\r\n                        <i className=\"icon fas fa-edit\" />\r\n\r\n                    </Link>\r\n                    &nbsp;\r\n                    <a onClick={() => userAction(d)}>\r\n                        <i className=\"icon fas fa-trash-alt\" />\r\n                    </a>{\" \"}\r\n                </div>\r\n            ),\r\n        },\r\n    ];\r\n    const institutionTypeParams = {\r\n        sort: { title: \"asc\" },\r\n        limit: perPage,\r\n        page: 1,\r\n        query: {},\r\n    };\r\n\r\n    useEffect(() => {\r\n        getInstitutionTypeData();\r\n    }, []);\r\n\r\n    const getInstitutionTypeData = async () => {\r\n        setLoading(true);\r\n        const response = await apiService.get(\"/institutiontype\", institutionTypeParams);\r\n        if (response && response.data && response.data.length > 0) {\r\n            setDataToTable(response.data);\r\n            setTotalRows(response.totalCount);\r\n            setLoading(false);\r\n        }\r\n    };\r\n\r\n    const handlePageChange = (page: any) => {\r\n        institutionTypeParams.limit = perPage;\r\n        institutionTypeParams.page = page;\r\n        getInstitutionTypeData();\r\n    };\r\n\r\n    const handlePerRowsChange = async (newPerPage: any, page: any) => {\r\n        institutionTypeParams.limit = newPerPage;\r\n        institutionTypeParams.page = page;\r\n        setLoading(true);\r\n        const response = await apiService.get(\"/institutiontype\", institutionTypeParams);\r\n        if (response && response.data && response.data.length > 0) {\r\n            setDataToTable(response.data);\r\n            setPerPage(newPerPage);\r\n            setLoading(false);\r\n        }\r\n    };\r\n\r\n    const modalConfirm = async () => {\r\n        try {\r\n            await apiService.remove(`/institutiontype/${selectInstitutionType}`);\r\n            getInstitutionTypeData();\r\n            setModal(false);\r\n            toast.success(t(\"adminsetting.Organisationtypes.Table.orgTypeDeletedSuccessfully\"));\r\n        } catch (error) {\r\n            toast.error(t(\"adminsetting.Organisationtypes.Table.errorDeletingOrgType\"));\r\n        }\r\n    };\r\n\r\n    const userAction = async (row: any) => {\r\n        setSelectInstitutionType(row._id);\r\n        setModal(true);\r\n    };\r\n\r\n    return (\r\n        <div>\r\n            <Modal show={isModalShow} onHide={modalHide}>\r\n                <Modal.Header closeButton>\r\n                    <Modal.Title>{t(\"adminsetting.Organisationtypes.Delete\")}</Modal.Title>\r\n                </Modal.Header>\r\n                <Modal.Body>{t(\"adminsetting.Organisationtypes.sure\")}</Modal.Body>\r\n                <Modal.Footer>\r\n                    <Button variant=\"secondary\" onClick={modalHide}>\r\n                        {t(\"Cancel\")}\r\n                    </Button>\r\n                    <Button variant=\"primary\" onClick={modalConfirm}>\r\n                        {t(\"yes\")}\r\n                    </Button>\r\n                </Modal.Footer>\r\n            </Modal>\r\n\r\n            <RKITable\r\n                columns={columns}\r\n                data={tabledata}\r\n                totalRows={totalRows}\r\n                pagServer={true}\r\n                handlePerRowsChange={handlePerRowsChange}\r\n                handlePageChange={handlePageChange}\r\n            />\r\n        </div>\r\n    );\r\n};\r\nexport default InstitutionTypeTable;\r\n", "//Import Library\r\nimport { Container, <PERSON>, <PERSON>, Button } from \"react-bootstrap\";\r\nimport Link from \"next/link\";\r\nimport HazardTypeTable from \"./hazardTypeTable\";\r\n\r\n//Import services/components\r\nimport PageHeading from \"../../../components/common/PageHeading\";\r\nimport { useTranslation } from 'next-i18next';\r\nimport { canAddHazardTypes } from \"../permissions\";\r\nimport NoAccessMessage from \"../../rNoAccess\";\r\nimport { useSelector } from \"react-redux\";\r\n\r\n\r\nconst HazardTypeIndex = (_props: any) => {\r\n  const { t } = useTranslation('common');\r\n  const ShowHazardTypeIndex = () => {\r\n    return (\r\n      <Container style={{ overflowX: \"hidden\" }} fluid className=\"p-0\">\r\n        <Row>\r\n          <Col xs={12}>\r\n            <PageHeading title= {t(\"adminsetting.hazardtypes.HarzardType\")} />\r\n          </Col>\r\n        </Row>\r\n        <Row>\r\n          <Col xs={12}>\r\n            <Link\r\n              href=\"/adminsettings/[...routes]\"\r\n              as=\"/adminsettings/create_hazard_types\"\r\n              >\r\n              <Button variant=\"secondary\" size=\"sm\">\r\n                {t(\"adminsetting.hazardtypes.type\")}\r\n              </Button>\r\n            </Link>\r\n          </Col>\r\n        </Row>\r\n        <Row className=\"mt-3\">\r\n          <Col xs={12}>\r\n            <HazardTypeTable />\r\n          </Col>\r\n        </Row>\r\n      </Container>\r\n    );\r\n  };\r\n  \r\n  const ShowAddHazardTypes = canAddHazardTypes(() => <ShowHazardTypeIndex />);\r\n  const state:any = useSelector((state) => state);\r\n  if (!(state?.permissions?.hazard_type?.['create:any'])) {\r\n    return <NoAccessMessage />\r\n  }\r\n  return(\r\n    <ShowAddHazardTypes />\r\n  );\r\n};\r\n\r\nexport default HazardTypeIndex;\r\n", "//Import Library\r\nimport { useState, useRef, useEffect } from \"react\";\r\nimport { Container, Card, Row, Col, Form, Button } from \"react-bootstrap\";\r\n// import { ValidationForm } from \"../../../components/common/FormValidation\";\r\nimport ValidationFormWrapper from \"../../../components/common/ValidationFormWrapper\";\r\nimport { TextInput } from \"../../../components/common/FormValidation\";\r\nimport Router from \"next/router\";\r\nimport toast from 'react-hot-toast';\r\nimport Link from 'next/link';\r\n\r\n//Import services/components\r\nimport { Category } from \"../../../types\";\r\nimport apiService from \"../../../services/apiService\";\r\nimport { useTranslation } from 'next-i18next';\r\n\r\n\r\ninterface CategoryFormProps {\r\n    routes: string[];\r\n}\r\n\r\nconst CategoryForm = (props: CategoryFormProps) => {\r\n\r\n  const _initialcategory = {\r\n    _id: '',\r\n    title: '',\r\n  }\r\n\r\n  const [initialVal, setInitialVal] = useState<Category>(_initialcategory);\r\n\r\n  const editform: boolean = !!(props.routes && props.routes[0] === \"edit_category\" && props.routes[1]);\r\n  const { t } = useTranslation('common');\r\n\r\n\r\n  const formRef = useRef(null);\r\n\r\n  const resetHandler = () => {\r\n    setInitialVal(_initialcategory);\r\n    // Reset validation state (Formik handles this automatically)\r\n    window.scrollTo(0, 0);\r\n  };\r\n\r\n  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement>) => {\r\n    if (e.target) {\r\n      const { name, value } = e.target;\r\n      setInitialVal(prevState => ({\r\n        ...prevState,\r\n        [name]: value\r\n      }));\r\n    }\r\n  }\r\n\r\n  const handleSubmit = async (event: any) => {\r\n    event.preventDefault();\r\n    const obj = {\r\n      title: initialVal.title.trim(),\r\n    };\r\n\r\n\r\n    let response;\r\n    if (editform) {\r\n      response = await apiService.patch(`/category/${props.routes[1]}`, obj);\r\n    } else {\r\n      response = await apiService.post(\"/category\", obj);\r\n    }\r\n    if (response && response._id) {\r\n      toast.success(t(\"Categoryisaddedsuccessfully\"));\r\n      Router.push(\"/adminsettings/category\");\r\n    } else {\r\n      toast.error(response);\r\n    }\r\n  }\r\n\r\n  useEffect(() => {\r\n    const categoryParams = {\r\n      query: {},\r\n      sort: { title: \"asc\" },\r\n      limit: \"~\",\r\n    };\r\n    if (editform) {\r\n      const getCategoryData = async () => {\r\n        const response: Category = await apiService.get(`/category/${props.routes[1]}`, categoryParams);\r\n        setInitialVal((prevState) => ({ ...prevState, ...response }));\r\n      }\r\n      getCategoryData();\r\n    }\r\n  }, []);\r\n\r\n  return (\r\n    <div>\r\n      <Container className=\"formCard\" fluid>\r\n        <Card style={{ marginTop: \"5px\", boxShadow: \"0 4px 8px 0 rgba(0, 0, 0, 0.2), 0 6px 20px 0 rgba(0, 0, 0, 0.19)\" }}>\r\n          <ValidationFormWrapper onSubmit={handleSubmit} ref={formRef} initialValues={initialVal} enableReinitialize={true}>\r\n            <Card.Body>\r\n              <Row>\r\n                <Col>\r\n                  <Card.Title>{t(\"Category\")}</Card.Title>\r\n                </Col>\r\n              </Row>\r\n              <hr />\r\n              <Row>\r\n                <Col md lg={6} sm={12}>\r\n                  <Form.Group>\r\n                    <Form.Label className=\"required-field\">{t(\"Category\")}</Form.Label>\r\n                    <TextInput\r\n                      name=\"title\"\r\n                      id=\"title\"\r\n                      required value={initialVal.title}\r\n                      validator={((value: any) => String(value || '').trim() !== \"\")}\r\n                      errorMessage={{\r\n                        validator: t(\"PleaseAddtheCategory\")}}\r\n                      onChange={handleChange}\r\n                    />\r\n                  </Form.Group>\r\n                </Col>\r\n              </Row>\r\n              <Row className=\"my-4\">\r\n                <Col>\r\n                  <Button className=\"me-2\" type=\"submit\" variant=\"primary\">{t(\"submit\")}</Button>\r\n                  <Button className=\"me-2\" onClick={resetHandler} variant=\"info\">{t(\"reset\")}</Button>\r\n                  <Link\r\n                    href=\"/adminsettings/[...routes]\"\r\n                    as={`/adminsettings/category`}\r\n                    ><Button variant=\"secondary\">{t(\"Cancel\")}</Button></Link>\r\n                </Col>\r\n              </Row>\r\n            </Card.Body>\r\n          </ValidationFormWrapper>\r\n        </Card>\r\n      </Container>\r\n    </div>\r\n  );\r\n}\r\nexport default CategoryForm;\r\n", "//Import Library\r\nimport { Container } from \"react-bootstrap\";\r\n\r\n//Import services/components\r\nimport PageHeading from \"../../../components/common/PageHeading\";\r\nimport { useTranslation } from 'next-i18next';\r\nimport VspaceAdmin from \"./VspaceAdmin\";\r\nimport { canAddVspaceApproval } from \"../permissions\";\r\nimport { useSelector } from \"react-redux\";\r\nimport NoAccessMessage from \"../../rNoAccess\";\r\n\r\nconst VirtualSpaceShow = (_props: any) => {\r\n  const { t } = useTranslation('common');\r\n  const ShowVirtualSpace = () => {\r\n    return (\r\n      <Container fluid className=\"p-0\">\r\n        <PageHeading title={t(\"adminsetting.VirtualspaceApproval\")} />\r\n        <VspaceAdmin />\r\n      </Container>\r\n    )\r\n  };\r\n\r\n  const ShowAddVspaceApproval = canAddVspaceApproval(() => <ShowVirtualSpace />);\r\n  const state:any = useSelector((state: any) => state);\r\n  if (!(state?.permissions?.institution_focal_point?.['create:any'])) {\r\n    return <NoAccessMessage />\r\n  }\r\n  return(\r\n    <ShowAddVspaceApproval />\r\n  );  \r\n}\r\nexport default VirtualSpaceShow;\r\n", "//Import Library\r\nimport { Contain<PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, Button } from \"react-bootstrap\";\r\n// import { ValidationForm, TextInput, SelectGroup } from \"../../../components/common/FormValidation\";\r\nimport ValidationFormWrapper from \"../../../components/common/ValidationFormWrapper\";\r\nimport { TextInput, SelectGroup } from \"../../../components/common/FormValidation\";\r\nimport { useRef, useState, useEffect } from \"react\";\r\nimport Router from \"next/router\";\r\nimport toast from 'react-hot-toast';\r\nimport Link from \"next/link\";\r\n\r\n//Import services/components\r\nimport apiService from \"../../../services/apiService\";\r\nimport { Region, Country, ApiResponse } from \"../../../types\";\r\nimport { useTranslation } from 'next-i18next';\r\n\r\ninterface RegionFormProps {\r\n    [key: string]: any;\r\n}\r\n\r\nconst RegionForm = (props: RegionFormProps) => {\r\n    const { t, i18n } = useTranslation('common');\r\n    const titleSearch = i18n.language === \"de\" ? { title_de: \"asc\" } : { title: \"asc\" };\r\n    const currentLang = i18n.language;\r\n    const _initialRegion = {\r\n        title: \"\",\r\n        country: null,\r\n    };\r\n    const [initialVal, setInitialVal] = useState<Region>(_initialRegion);\r\n    const [country, setCountry] = useState<Country[]>([]);\r\n\r\n    const editform = props.routes && props.routes[0] === \"edit_region\" && props.routes[1];\r\n\r\n    const formRef = useRef(null);\r\n\r\n    const resetHandler = () => {\r\n        setInitialVal(_initialRegion);\r\n        // Reset validation state (Formik handles this automatically)\r\n        window.scrollTo(0, 0);\r\n    };\r\n\r\n    const handleSubmit = async (event: any) => {\r\n        event.preventDefault();\r\n        const obj = {\r\n            title: initialVal.title.trim(),\r\n            country: initialVal.country,\r\n        };\r\n\r\n        let response;\r\n        let toastMsg;\r\n        if (editform) {\r\n            toastMsg = \"adminsetting.Regions.Regionisupdatedsuccessfully\";\r\n            response = await apiService.patch(`/region/${props.routes[1]}`, obj);\r\n        } else {\r\n            toastMsg = \"adminsetting.Regions.Regionisaddedsuccessfully\";\r\n            response = await apiService.post(\"/region\", obj);\r\n        }\r\n        if (response && response._id) {\r\n            toast.success(t(toastMsg));\r\n            Router.push(\"/adminsettings/region\");\r\n        } else {\r\n            toast.error(response);\r\n        }\r\n    };\r\n\r\n    const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement>) => {\r\n        if (e.target) {\r\n            const { name, value } = e.target;\r\n            setInitialVal((prevState) => ({\r\n                ...prevState,\r\n                [name]: value,\r\n            }));\r\n        }\r\n    };\r\n\r\n    const getCountry = async (regionParams: any) => {\r\n        const response: ApiResponse<Country[]> = await apiService.get(\"/country\", regionParams);\r\n        if (response) {\r\n            setCountry(response.data);\r\n        }\r\n    };\r\n\r\n    useEffect(() => {\r\n        const regionParams = {\r\n            query: {},\r\n            sort: titleSearch,\r\n            limit: \"~\",\r\n            languageCode: currentLang,\r\n        };\r\n\r\n        if (editform) {\r\n            const getRegionData = async () => {\r\n                const response = await apiService.get(`/region/${props.routes[1]}`, regionParams);\r\n                if (response) {\r\n                    const { country } = response;\r\n                    response.country = country && country._id ? country._id : '';\r\n                    setInitialVal((prevState) => ({ ...prevState, ...response }));\r\n                }\r\n            };\r\n            getRegionData();\r\n        }\r\n        getCountry(regionParams);\r\n    }, []);\r\n\r\n    return (\r\n        <div>\r\n            <Container className=\"formCard\" fluid>\r\n                <Card\r\n                    style={{\r\n                        marginTop: \"5px\",\r\n                        boxShadow: \"0 4px 8px 0 rgba(0, 0, 0, 0.2), 0 6px 20px 0 rgba(0, 0, 0, 0.19)\",\r\n                    }}\r\n                >\r\n                    <ValidationFormWrapper onSubmit={handleSubmit} ref={formRef} initialValues={initialVal} enableReinitialize={true}>\r\n                        <Card.Body>\r\n                            <Row>\r\n                                <Col>\r\n                                    <Card.Title>{editform ? t(\"adminsetting.Regions.EditRegion\") : t(\"adminsetting.Regions.AddRegion\")}</Card.Title>\r\n                                </Col>\r\n                            </Row>\r\n                            <hr />\r\n                            <Row>\r\n                                <Col md lg={6} sm={12}>\r\n                                    <Form.Group>\r\n                                        <Form.Label className=\"required-field\">\r\n                                            {t(\"adminsetting.Regions.Country\")}\r\n                                        </Form.Label>\r\n                                        <SelectGroup\r\n                                            name=\"country\"\r\n                                            id=\"country\"\r\n                                            required\r\n                                            value={initialVal.country}\r\n                                            validator={(value: string) => String(value || '').trim() !== \"\"}\r\n                                            errorMessage={{\r\n                                                validator: t(\"adminsetting.Regions.PleaseAddtheCountry\"),\r\n                                            }}\r\n                                            onChange={handleChange}\r\n                                        >\r\n                                            <option value=\"\">{t(\"adminsetting.Regions.SelectCountry\")}</option>\r\n                                            {country.length >= 1\r\n                                                ? country.map((item, _i) => {\r\n                                                      return (\r\n                                                          <option key={item._id} value={item._id}>\r\n                                                              {item.title}\r\n                                                          </option>\r\n                                                      );\r\n                                                  })\r\n                                                : null}\r\n                                        </SelectGroup>\r\n                                    </Form.Group>\r\n                                </Col>\r\n                                <Col md lg={6} sm={12}>\r\n                                    <Form.Group>\r\n                                        <Form.Label className=\"required-field\">\r\n                                            {t(\"adminsetting.Regions.Region\")}\r\n                                        </Form.Label>\r\n                                        <TextInput\r\n                                            name=\"title\"\r\n                                            id=\"title\"\r\n                                            required\r\n                                            value={initialVal.title}\r\n                                            errorMessage={{validator: t(\"adminsetting.Regions.PleaseAddtheRegion\")}}\r\n                                            onChange={handleChange}\r\n                                        />\r\n                                    </Form.Group>\r\n                                </Col>\r\n                            </Row>\r\n                            <Row className=\"my-4\">\r\n                                <Col>\r\n                                    <Button className=\"me-2\" type=\"submit\" variant=\"primary\">\r\n                                        {t(\"adminsetting.Regions.Submit\")}\r\n                                    </Button>\r\n                                    <Button className=\"me-2\" onClick={resetHandler} variant=\"info\">\r\n                                        {t(\"adminsetting.Regions.Reset\")}\r\n                                    </Button>\r\n                                    <Link\r\n                                        href=\"/adminsettings/[...routes]\"\r\n                                        as={`/adminsettings/region`}\r\n                                        >\r\n                                        <Button variant=\"secondary\">{t(\"adminsetting.Regions.Cancel\")}</Button>\r\n                                    </Link>\r\n                                </Col>\r\n                            </Row>\r\n                        </Card.Body>\r\n                    </ValidationFormWrapper>\r\n                </Card>\r\n            </Container>\r\n        </div>\r\n    );\r\n};\r\n\r\nexport default RegionForm;\r\n", "//Import Library\r\nimport { useState, useRef, useEffect } from \"react\";\r\nimport { Container, Card, Row, Col, Form, Button } from \"react-bootstrap\";\r\nimport { TextInput } from \"../../../components/common/FormValidation\";\r\nimport ValidationFormWrapper from \"../../../components/common/ValidationFormWrapper\";\r\nimport Router from \"next/router\";\r\nimport toast from 'react-hot-toast';\r\nimport Link from \"next/link\";\r\n\r\n//Import services/components\r\nimport { useTranslation } from 'next-i18next';\r\nimport { ProjectStatus } from \"../../../types\";\r\nimport apiService from \"../../../services/apiService\";\r\n\r\ninterface ProjectstatusFormProps {\r\n    [key: string]: any;\r\n}\r\n\r\nconst ProjectstatusForm = (props: ProjectstatusFormProps) => {\r\n    const { t } = useTranslation('common');\r\n    const _initialprojectstatus = {\r\n        title: \"\",\r\n    };\r\n\r\n    const [initialVal, setInitialVal] = useState<ProjectStatus>(_initialprojectstatus);\r\n\r\n    const editform = props.routes && props.routes[0] === \"edit_projectstatus\" && props.routes[1];\r\n\r\n    const formRef = useRef(null);\r\n\r\n    const resetHandler = () => {\r\n        setInitialVal(_initialprojectstatus);\r\n        // Reset validation state (Formik handles this automatically)\r\n        window.scrollTo(0, 0);\r\n    };\r\n\r\n    const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement>) => {\r\n        if (e.target) {\r\n            const { name, value } = e.target;\r\n            setInitialVal((prevState) => ({\r\n                ...prevState,\r\n                [name]: value,\r\n            }));\r\n        }\r\n    };\r\n\r\n    const handleSubmit = async (event: any) => {\r\n        event.preventDefault();\r\n        const obj = {\r\n            title: initialVal.title.trim(),\r\n        };\r\n\r\n        let response;\r\n        let toastMsg;\r\n        if (editform) {\r\n            toastMsg = \"toast.Projectstatusisupdatedsuccessfully\";\r\n            response = await apiService.patch(`/projectstatus/${props.routes[1]}`, obj);\r\n        } else {\r\n            toastMsg = \"toast.Projectstatusisaddedsuccessfully\";\r\n            response = await apiService.post(\"/projectstatus\", obj);\r\n        }\r\n        if (response && response._id) {\r\n            toast.success(t(toastMsg));\r\n            Router.push(\"/adminsettings/projectstatus\");\r\n        } else {\r\n            if (response?.errorCode === 11000) {\r\n                toast.error(t(\"duplicatesNotAllowed\"));\r\n            } else {\r\n                toast.error(response);\r\n            }\r\n        }\r\n    };\r\n\r\n    useEffect(() => {\r\n        const projectstatusParams = {\r\n            query: {},\r\n            sort: { title: \"asc\" },\r\n            limit: \"~\",\r\n        };\r\n        if (editform) {\r\n            const getProjectstatusData = async () => {\r\n                const response: ProjectStatus = await apiService.get(`/projectstatus/${props.routes[1]}`, projectstatusParams);\r\n                setInitialVal((prevState) => ({ ...prevState, ...response }));\r\n            };\r\n            getProjectstatusData();\r\n        }\r\n    }, []);\r\n\r\n    return (\r\n        <div>\r\n            <Container className=\"formCard\" fluid>\r\n                <Card\r\n                    style={{\r\n                        marginTop: \"5px\",\r\n                        boxShadow: \"0 4px 8px 0 rgba(0, 0, 0, 0.2), 0 6px 20px 0 rgba(0, 0, 0, 0.19)\",\r\n                    }}\r\n                >\r\n                    <ValidationFormWrapper onSubmit={handleSubmit} ref={formRef} initialValues={initialVal} enableReinitialize={true}>\r\n                        <Card.Body>\r\n                            <Row>\r\n                                <Col>\r\n                                    <Card.Title>{t(\"adminsetting.ProjectStatus.ProjectStatus\")}</Card.Title>\r\n                                </Col>\r\n                            </Row>\r\n                            <hr />\r\n                            <Row>\r\n                                <Col md lg={6} sm={12}>\r\n                                    <Form.Group>\r\n                                        <Form.Label className=\"required-field\">\r\n                                            {t(\"adminsetting.ProjectStatus.ProjectStatus\")}\r\n                                        </Form.Label>\r\n                                        <TextInput\r\n                                            name=\"title\"\r\n                                            id=\"title\"\r\n                                            required\r\n                                            value={initialVal.title}\r\n                                            validator={(value: string) => value.trim() !== \"\"}\r\n                                            errorMessage={{\r\n                                                validator: t(\"adminsetting.ProjectStatus.PleaseAddtheProjectStatus\"),\r\n                                            }}\r\n                                            onChange={handleChange}\r\n                                        />\r\n                                    </Form.Group>\r\n                                </Col>\r\n                            </Row>\r\n                            <Row className=\"my-4\">\r\n                                <Col>\r\n                                    <Button className=\"me-2\" type=\"submit\" variant=\"primary\">\r\n                                        {t(\"adminsetting.ProjectStatus.Submit\")}\r\n                                    </Button>\r\n                                    <Button className=\"me-2\" onClick={resetHandler} variant=\"info\">\r\n                                        {t(\"adminsetting.ProjectStatus.Reset\")}\r\n                                    </Button>\r\n                                    <Link\r\n                                        href=\"/adminsettings/[...routes]\"\r\n                                        as={`/adminsettings/projectstatus`}\r\n                                        >\r\n                                        <Button variant=\"secondary\">{t(\"adminsetting.ProjectStatus.Cancel\")}</Button>\r\n                                    </Link>\r\n                                </Col>\r\n                            </Row>\r\n                        </Card.Body>\r\n                    </ValidationFormWrapper>\r\n                </Card>\r\n            </Container>\r\n        </div>\r\n    );\r\n};\r\nexport default ProjectstatusForm;\r\n", "//Import Library\r\nimport { useState, useRef, useEffect } from \"react\";\r\nimport { Container, Card, Row, Col, Form, Button } from \"react-bootstrap\";\r\nimport { TextInput } from \"../../../components/common/FormValidation\";\r\nimport ValidationFormWrapper from \"../../../components/common/ValidationFormWrapper\";\r\nimport Router from \"next/router\";\r\nimport toast from 'react-hot-toast';\r\nimport Link from \"next/link\";\r\n\r\n//Import services/components\r\nimport { EventStatus } from \"../../../types\";\r\nimport apiService from \"../../../services/apiService\";\r\nimport { useTranslation } from 'next-i18next';\r\n\r\ninterface EventstatusFormProps {\r\n    [key: string]: any;\r\n}\r\n\r\nconst EventstatusForm = (props: EventstatusFormProps) => {\r\n    const _initialeventstatus = {\r\n        _id: \"\",\r\n        title: \"\",\r\n    };\r\n    const { t } = useTranslation('common');\r\n    const [initialVal, setInitialVal] = useState<EventStatus>(_initialeventstatus);\r\n\r\n    const editform: boolean = props.routes && props.routes[0] === \"edit_eventstatus\" && props.routes[1];\r\n\r\n    const formRef = useRef(null);\r\n\r\n    const resetHandler = () => {\r\n        setInitialVal(_initialeventstatus);\r\n        // Reset validation state (Formik handles this automatically)\r\n        window.scrollTo(0, 0);\r\n    };\r\n\r\n    const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement>) => {\r\n        if (e.target) {\r\n            const { name, value } = e.target;\r\n            setInitialVal((prevState) => ({\r\n                ...prevState,\r\n                [name]: value,\r\n            }));\r\n        }\r\n    };\r\n\r\n    const handleSubmit = async (event: any) => {\r\n        event.preventDefault();\r\n\r\n        const obj = {\r\n            title: initialVal.title.trim(),\r\n        };\r\n\r\n        let response;\r\n        let toastMsg;\r\n        if (editform) {\r\n            toastMsg = \"adminsetting.EventStatus.Forms.Eventstatusisupdatedsuccessfully\";\r\n            response = await apiService.patch(`/eventstatus/${props.routes[1]}`, obj);\r\n        } else {\r\n            toastMsg = \"adminsetting.EventStatus.Forms.Eventstatusisaddedsuccessfully\";\r\n            response = await apiService.post(\"/eventstatus\", obj);\r\n        }\r\n        if (response && response._id) {\r\n            toast.success(t(toastMsg));\r\n            Router.push(\"/adminsettings/eventstatus\");\r\n        } else {\r\n            if (response?.errorCode === 11000) {\r\n                toast.error(t(\"duplicatesNotAllowed\"));\r\n            } else {\r\n                toast.error(response);\r\n            }\r\n        }\r\n    };\r\n\r\n    useEffect(() => {\r\n        const eventstatusParams = {\r\n            query: {},\r\n            sort: { title: \"asc\" },\r\n            limit: \"~\",\r\n        };\r\n        if (editform) {\r\n            const getEventstatusData = async () => {\r\n                const response: EventStatus = await apiService.get(`/eventstatus/${props.routes[1]}`, eventstatusParams);\r\n                setInitialVal((prevState) => ({ ...prevState, ...response }));\r\n            };\r\n            getEventstatusData();\r\n        }\r\n    }, []);\r\n\r\n    return (\r\n        <div>\r\n            <Container className=\"formCard\" fluid>\r\n                <Card\r\n                    style={{\r\n                        marginTop: \"5px\",\r\n                        boxShadow: \"0 4px 8px 0 rgba(0, 0, 0, 0.2), 0 6px 20px 0 rgba(0, 0, 0, 0.19)\",\r\n                    }}\r\n                >\r\n                    <ValidationFormWrapper onSubmit={handleSubmit} ref={formRef} initialValues={initialVal} enableReinitialize={true}>\r\n                        <Card.Body>\r\n                            <Row>\r\n                                <Col>\r\n                                    <Card.Title>{t(\"adminsetting.EventStatus.Forms.EventStatus\")}</Card.Title>\r\n                                </Col>\r\n                            </Row>\r\n                            <hr />\r\n                            <Row>\r\n                                <Col md lg={6} sm={12}>\r\n                                    <Form.Group>\r\n                                        <Form.Label className=\"required-field\">\r\n                                            {t(\"adminsetting.EventStatus.Forms.EventStatus\")}\r\n                                        </Form.Label>\r\n                                        <TextInput\r\n                                            name=\"title\"\r\n                                            id=\"title\"\r\n                                            required\r\n                                            value={initialVal.title}\r\n                                            validator={(value: any) => String(value || '').trim() !== \"\"}\r\n                                            errorMessage={{\r\n                                                validator: t(\"adminsetting.EventStatus.Forms.PleaseAddtheEventStatus\"),\r\n                                            }}\r\n                                            onChange={handleChange}\r\n                                        />\r\n                                    </Form.Group>\r\n                                </Col>\r\n                            </Row>\r\n                            <Row className=\"my-4\">\r\n                                <Col>\r\n                                    <Button className=\"me-2\" type=\"submit\" variant=\"primary\">\r\n                                        {t(\"adminsetting.EventStatus.Forms.Submit\")}\r\n                                    </Button>\r\n                                    <Button className=\"me-2\" onClick={resetHandler} variant=\"info\">\r\n                                        {t(\"adminsetting.EventStatus.Forms.Reset\")}\r\n                                    </Button>\r\n                                    <Link\r\n                                        href=\"/adminsettings/[...routes]\"\r\n                                        as={`/adminsettings/eventstatus`}\r\n                                        >\r\n                                        <Button variant=\"secondary\">\r\n                                            {t(\"adminsetting.EventStatus.Forms.Cancel\")}\r\n                                        </Button>\r\n                                    </Link>\r\n                                </Col>\r\n                            </Row>\r\n                        </Card.Body>\r\n                    </ValidationFormWrapper>\r\n                </Card>\r\n            </Container>\r\n        </div>\r\n    );\r\n};\r\nexport default EventstatusForm;\r\n", "//Import Library\r\nimport Link from \"next/link\";\r\nimport _ from \"lodash\";\r\nimport React, { useEffect, useState } from \"react\";\r\nimport moment from \"moment\";\r\nimport { <PERSON><PERSON>, But<PERSON>, Container, Row, Col } from \"react-bootstrap\";\r\nimport toast from 'react-hot-toast';\r\n\r\n//Import services/components\r\nimport RKITable from \"../../../components/common/RKITable\";\r\nimport apiService from \"../../../services/apiService\";\r\nimport ContentTableFilter from \"./ContentTableFilter\";\r\nimport PageHeading from \"../../../components/common/PageHeading\";\r\nimport { useTranslation } from 'next-i18next';\r\nimport { serverSideTranslations } from 'next-i18next/serverSideTranslations';\r\nimport { ContentItem, User, ApiResponse, PaginationParams } from \"../../../types\";\r\n\r\nconst getSelectFieldOptions = (type: string, params: PaginationParams) => {\r\n    switch (type) {\r\n        case \"operation\":\r\n            params.select =\r\n                \"-status -start_date -images -timeline -country -world_region -region -hazard -description -end_date -syndrome -hazard_type -partners -images -document -doc_src -images_src\";\r\n            break;\r\n        case \"institution\":\r\n            params.select =\r\n                \"-partners -primary_focal_point -email -status -images -use_default_header -header -twitter -dial_code -telephone -department -unit -contact_name -description -type -networks -expertise -hazard_types -hazards -address -focal_points -website  -document -doc_src -images_src \";\r\n            break;\r\n        case \"event\":\r\n            params.select =\r\n                \"-images -more_info -date -risk_assessment -rki_monitored -officially_validated -laboratory_confirmed -status -syndrome -hazard -hazard_type -country_regions -world_region -country -operation -description -document -doc_src -images_src\";\r\n            break;\r\n        case \"project\":\r\n            params.select =\r\n                \"-vspace_visibility -vspace -institution_invites -partner_institutions -area_of_work -region -country -end_date -start_date -status -funded_by -description -website\";\r\n            break;\r\n        case \"updates\":\r\n            params.select =\r\n                \" -region -country -end_date -start_date -status -category -contact_details -reply -show_as_announcement -technical_guidance -use_in_media_gallery -field_report -media -images -document -doc_src -images_src -location -link -description  \";\r\n            break;\r\n        case \"vspace\":\r\n            params.select =\r\n                \"-vspace_email_invite -file_category -subscribers -images -members -visibility -topic -end_date -start_date -description -images -document -doc_src -images_src -nonMembers -private_user_invite\";\r\n            break;\r\n        default:\r\n            params.select =\r\n                \"-status -start_date -images -timeline -country -world_region -region -hazard -description -end_date -syndrome -hazard_type -partners\";\r\n            break;\r\n    }\r\n    return params;\r\n};\r\n\r\ninterface ContentProps {\r\n    [key: string]: any;\r\n}\r\n\r\nconst Content = (_props: ContentProps) => {\r\n    const [tableData, setTableData] = useState<ContentItem[]>([]);\r\n    const { t } = useTranslation('common');\r\n    const [type, setType] = useState<string>(\"operation\");\r\n    const [resetPaginationToggle, setResetPaginationToggle] = React.useState<boolean>(false);\r\n    const [filterText, setFilterText] = React.useState<string>(\"\");\r\n    const [totalRows, setTotalRows] = useState<number>(0);\r\n    const [perPage, setPerPage] = useState<number>(25);\r\n    const [loading, setLoading] = useState<boolean>(false);\r\n    const [isModalShow, setModal] = useState<boolean>(false);\r\n    const [contentItem, setContentItem] = useState<any>(null);\r\n    const [currentPage, setCurrentPage] = useState<number>(1);\r\n\r\n    const [currentUser, setcurrentUser] = useState<User | null>(null);\r\n\r\n    let params: any = {\r\n        limit: perPage,\r\n        sort: { created_at: \"desc\" },\r\n    };\r\n\r\n    const columns = [\r\n        {\r\n            name: t(\"adminsetting.content.table.Title\"),\r\n            selector: (row: any) => row.title,\r\n            cell: (d: any) =>\r\n                d.type ? (\r\n                    <Link href={`/${d.type}/[...routes]`} as={`/${d.type}/show/${d[modules_func(d)]}/${type}/${d._id}`}>\r\n                        {d.title}\r\n                    </Link>\r\n                ) : (\r\n                    <Link href={`/${type}/[...routes]`} as={`/${type}/show/${d._id}`}>\r\n                        {d.title}\r\n                    </Link>\r\n                ),\r\n            sortable: true,\r\n        },\r\n        {\r\n            name: t(\"adminsetting.content.table.Author\"),\r\n            selector: (row: ContentItem) => row.user?.username || '',\r\n            cell: (d: ContentItem) => (d.user ? d.user.username : \"\"),\r\n            sortable: true,\r\n        },\r\n        {\r\n            name: t(\"adminsetting.content.table.Created\"),\r\n            selector: (row: ContentItem) => row.created_at,\r\n            cell: (d: ContentItem) => moment(d.created_at).format(\"M/D/Y\"),\r\n            sortable: true,\r\n        },\r\n        {\r\n            name: t(\"adminsetting.content.table.Updated\"),\r\n            selector: (row: ContentItem) => row.updated_at,\r\n            cell: (d: ContentItem) => moment(d.updated_at).format(\"M/D/Y\"),\r\n            sortable: true,\r\n        },\r\n        {\r\n            name: t(\"adminsetting.content.table.Action\"),\r\n            selector: (row: ContentItem) => row._id,\r\n            sortable: false,\r\n            cell: (d: ContentItem) => (\r\n                <>\r\n                {(currentUser?.roles?.includes('GENERAL_USER') || currentUser?.roles?.includes('PLATFORM_ADMIN')) && currentUser?._id == d?.user?._id ?\r\n                    (<div>\r\n                        <Link href={`/${type}/[...routes]`} as={`/${type}/edit/${d._id}`}>\r\n\r\n                            <i className=\"icon fas fa-edit\" />\r\n\r\n                        </Link>\r\n                        &nbsp;\r\n                        <Link href=\"#\" onClick={(e) => userAction(d, e)}>\r\n\r\n                            <i className=\"icon fas fa-trash-alt\" />\r\n\r\n                        </Link>\r\n                    </div>) :\r\n                    (!(currentUser?.roles?.includes('GENERAL_USER') || currentUser?.roles?.includes('PLATFORM_ADMIN')) ?\r\n                    (<div>\r\n                        <Link href={`/${type}/[...routes]`} as={`/${type}/edit/${d._id}`}>\r\n\r\n                            <i className=\"icon fas fa-edit\" />\r\n\r\n                        </Link>\r\n                        &nbsp;\r\n                        <Link href=\"#\" onClick={(e) => userAction(d, e)}>\r\n\r\n                            <i className=\"icon fas fa-trash-alt\" />\r\n\r\n                        </Link>\r\n                    </div>): \"\")\r\n                }\r\n                </>\r\n\r\n            ),\r\n        },\r\n    ];\r\n\r\n    const fetchData = async (params: PaginationParams) => {\r\n        setLoading(true);\r\n        params = getSelectFieldOptions(type, params);\r\n        const currentUserResponse = await apiService.post(\"/users/getLoggedUser\", {});\r\n        if (currentUserResponse && currentUserResponse.username) {\r\n            setcurrentUser(currentUserResponse);\r\n        }\r\n        const response: ApiResponse<ContentItem[]> = await apiService.get(`/${type}`, params);\r\n        if (response && response.data && Array.isArray(response.data) && response.data.length > 0) {\r\n            setTableData(response.data);\r\n            setTotalRows(response.totalCount || 0);\r\n        } else {\r\n            setTableData([]);\r\n        }\r\n        setLoading(false);\r\n    };\r\n\r\n    const userAction = async (row: ContentItem, e: React.MouseEvent) => {\r\n        e.preventDefault();\r\n        setContentItem({ id: row._id, type: type });\r\n        setModal(true);\r\n    };\r\n\r\n    const handlePageChange = (page: number) => {\r\n        params.page = page;\r\n        if (filterText !== \"\") {\r\n            params.query = { title: filterText };\r\n        }\r\n        setCurrentPage(page);\r\n        fetchData(params);\r\n    };\r\n\r\n    const handlePerRowsChange = async (newPerPage: number, page: number) => {\r\n        setLoading(true);\r\n        params.limit = newPerPage;\r\n        params.page = page;\r\n        setCurrentPage(page);\r\n        params = getSelectFieldOptions(type, params);\r\n        const response: ApiResponse<ContentItem[]> = await apiService.get(`/${type}`, params);\r\n        if (response && Array.isArray(response.data)) {\r\n            setTableData(response.data);\r\n            setPerPage(newPerPage);\r\n            setLoading(false);\r\n        }\r\n    };\r\n\r\n    const modalHide = () => setModal(false);\r\n\r\n    const modalConfirm = async () => {\r\n        try {\r\n            await apiService.remove(`/${contentItem.type}/${contentItem.id}`);\r\n            params.page = currentPage;\r\n            fetchData(params);\r\n            setModal(false);\r\n            toast.success(t(\"adminsetting.content.table.contentDeletedSuccessfully\"));\r\n        } catch (error) {\r\n            toast.error(t(\"adminsetting.content.table.errorDeletingContent\"));\r\n        }\r\n    };\r\n\r\n    useEffect(() => {\r\n        fetchData(params);\r\n    }, [type]);\r\n\r\n    const handleSort = async (column: any, sortDirection: string) => {\r\n        setLoading(true);\r\n        params.sort = {\r\n            [column.selector]: sortDirection,\r\n        };\r\n        await fetchData(params);\r\n        setLoading(false);\r\n    };\r\n\r\n    const subHeaderComponentMemo = React.useMemo(() => {\r\n        const handleClear = () => {\r\n            if (filterText) {\r\n                setResetPaginationToggle(!resetPaginationToggle);\r\n                setFilterText(\"\");\r\n            }\r\n        };\r\n\r\n        const handleFilterTypeChange = (type_initial: string) => {\r\n            setType(type_initial);\r\n        };\r\n\r\n        const sendQuery = (q: string) => {\r\n            if (q) {\r\n                params.query = { title: q };\r\n            }\r\n            fetchData(params);\r\n        };\r\n\r\n        const handleSearchTitle = _.debounce((q: string) => sendQuery(q), Number(process.env.SEARCH_DEBOUNCE_TIME) || 300);\r\n\r\n        const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {\r\n            setFilterText(e.target.value);\r\n            handleSearchTitle(e.target.value);\r\n        };\r\n\r\n        return (\r\n            <ContentTableFilter\r\n                onFilter={handleChange}\r\n                onClear={handleClear}\r\n                filterText={filterText}\r\n                onFilterTypeChange={(e: React.ChangeEvent<HTMLSelectElement>) => handleFilterTypeChange(e.target.value)}\r\n                filterType={type}\r\n            />\r\n        );\r\n    }, [filterText, type, resetPaginationToggle]);\r\n\r\n    return (\r\n        <Container style={{ overflowX: \"hidden\" }} fluid className=\"p-0\">\r\n            <Row>\r\n                <Col xs={12}>\r\n                    <PageHeading title={t(\"adminsetting.content.table.content\")} />\r\n                </Col>\r\n            </Row>\r\n            <Row className=\"mt-3\">\r\n                <Col xs={12}>\r\n                    <Modal show={isModalShow} onHide={modalHide}>\r\n                        <Modal.Header closeButton>\r\n                            <Modal.Title>{t(\"adminsetting.content.table.DeleteContent\")}</Modal.Title>\r\n                        </Modal.Header>\r\n                        <Modal.Body>{t(\"adminsetting.content.table.Areyousurewanttodeletethiscontent?\")}</Modal.Body>\r\n                        <Modal.Footer>\r\n                            <Button variant=\"secondary\" onClick={modalHide}>\r\n                                {t(\"adminsetting.content.table.Cancel\")}\r\n                            </Button>\r\n                            <Button variant=\"primary\" onClick={modalConfirm}>\r\n                                {t(\"adminsetting.content.table.Yes\")}\r\n                            </Button>\r\n                        </Modal.Footer>\r\n                    </Modal>\r\n                    <RKITable\r\n                        columns={columns}\r\n                        loading={loading}\r\n                        data={tableData}\r\n                        totalRows={totalRows}\r\n                        defaultRowsPerPage={perPage}\r\n                        subheader\r\n                        onSort={handleSort}\r\n                        sortServer\r\n                        pagServer={true}\r\n                        subHeaderComponent={subHeaderComponentMemo}\r\n                        persistTableHead\r\n                        resetPaginationToggle={resetPaginationToggle}\r\n                        handlePerRowsChange={handlePerRowsChange}\r\n                        handlePageChange={handlePageChange}\r\n                    />\r\n                </Col>\r\n            </Row>\r\n        </Container>\r\n    );\r\n};\r\n\r\ninterface ServerSidePropsContext {\r\n  locale: string;\r\n}\r\n\r\nexport async function getServerSideProps({ locale }: ServerSidePropsContext) {\r\n  return {\r\n    props: {\r\n      ...(await serverSideTranslations(locale, ['common'])),\r\n    },\r\n  }\r\n}\r\n\r\nexport default Content;\r\n\r\nfunction modules_func(d: ContentItem) {\r\n    return `parent_${d.type}`;\r\n}\r\n", "//Import Library\r\nimport { useState, useEffect } from \"react\";\r\nimport Link from \"next/link\";\r\nimport { <PERSON><PERSON>, Button } from \"react-bootstrap\";\r\nimport toast from 'react-hot-toast';\r\n\r\n//Import services/components\r\nimport RKITable from \"../../../components/common/RKITable\";\r\nimport apiService from \"../../../services/apiService\";\r\nimport { useTranslation } from 'next-i18next';\r\n\r\nconst DeploymentstatusTable = (props: any) => {\r\n    const { t } = useTranslation('common');\r\n    const [tabledata, setDataToTable] = useState([]);\r\n    const [, setLoading] = useState(false);\r\n    const [totalRows, setTotalRows] = useState(0);\r\n    const [perPage, setPerPage] = useState(10);\r\n    const [isModalShow, setModal] = useState(false);\r\n    const [selectdeploymentstatus, setSelectdeploymentstatus] = useState({});\r\n\r\n\r\n    const deploymentstatusParams = {\r\n        sort: { title: \"asc\" },\r\n        limit: perPage,\r\n        page: 1,\r\n        query: {},\r\n    };\r\n\r\n    const columns = [\r\n        {\r\n            name: t(\"adminsetting.DeploymentStatus.Table.Title\"),\r\n            selector: \"title\",\r\n        },\r\n        {\r\n            name: t(\"adminsetting.DeploymentStatus.Table.Action\"),\r\n            selector: \"\",\r\n            cell: (d: any) => (\r\n                <div>\r\n                    <Link href=\"/adminsettings/[...routes]\" as={`/adminsettings/edit_deploymentstatus/${d._id}`}>\r\n\r\n                        <i className=\"icon fas fa-edit\" />\r\n\r\n                    </Link>\r\n                    &nbsp;\r\n                    <a onClick={() => userAction(d)}>\r\n                        <i className=\"icon fas fa-trash-alt\" />\r\n                    </a>\r\n                </div>\r\n            ),\r\n        },\r\n    ];\r\n\r\n    const getdeploymentstatusData = async () => {\r\n        setLoading(true);\r\n        const response = await apiService.get(\"/deploymentstatus\", deploymentstatusParams);\r\n        if (response && response.data && response.data.length > 0) {\r\n            setDataToTable(response.data);\r\n            setTotalRows(response.totalCount);\r\n            setLoading(false);\r\n        }\r\n    };\r\n\r\n    const handlePageChange = (page: any) => {\r\n        deploymentstatusParams.limit = perPage;\r\n        deploymentstatusParams.page = page;\r\n        getdeploymentstatusData();\r\n    };\r\n\r\n    const handlePerRowsChange = async (newPerPage: any, page: any) => {\r\n        deploymentstatusParams.limit = newPerPage;\r\n        deploymentstatusParams.page = page;\r\n        setLoading(true);\r\n        const response = await apiService.get(\"/deploymentstatus\", deploymentstatusParams);\r\n        if (response && response.data && response.data.length > 0) {\r\n            setDataToTable(response.data);\r\n            setPerPage(newPerPage);\r\n            setLoading(false);\r\n        }\r\n    };\r\n\r\n    const userAction = async (row: any) => {\r\n        setSelectdeploymentstatus(row._id);\r\n        setModal(true);\r\n    };\r\n\r\n    const modalConfirm = async () => {\r\n        try {\r\n            await apiService.remove(`/deploymentstatus/${selectdeploymentstatus}`);\r\n            getdeploymentstatusData();\r\n            setModal(false);\r\n            toast.success(t(\"adminsetting.DeploymentStatus.Table.deploymentStatusDeletedSuccessfully\"));\r\n        } catch (error) {\r\n            toast.error(t(\"adminsetting.DeploymentStatus.Table.errorDeletingDeploymentStatus\"));\r\n        }\r\n    };\r\n\r\n    const modalHide = () => setModal(false);\r\n\r\n    useEffect(() => {\r\n        getdeploymentstatusData();\r\n    }, []);\r\n\r\n    return (\r\n        <div>\r\n            <Modal show={isModalShow} onHide={modalHide}>\r\n                <Modal.Header closeButton>\r\n                    <Modal.Title>{t(\"adminsetting.DeploymentStatus.Table.DeleteDeploymentstatus\")}</Modal.Title>\r\n                </Modal.Header>\r\n                <Modal.Body>\r\n                    {t(\"adminsetting.DeploymentStatus.Table.Areyousurewanttodeletethisdeploymentstatus?\")}\r\n                </Modal.Body>\r\n                <Modal.Footer>\r\n                    <Button variant=\"secondary\" onClick={modalHide}>\r\n                        {t(\"adminsetting.DeploymentStatus.Table.Cancel\")}\r\n                    </Button>\r\n                    <Button variant=\"primary\" onClick={modalConfirm}>\r\n                        {t(\"adminsetting.DeploymentStatus.Table.Yes\")}\r\n                    </Button>\r\n                </Modal.Footer>\r\n            </Modal>\r\n\r\n            <RKITable\r\n                columns={columns}\r\n                data={tabledata}\r\n                totalRows={totalRows}\r\n                pagServer={true}\r\n                handlePerRowsChange={handlePerRowsChange}\r\n                handlePageChange={handlePageChange}\r\n            />\r\n        </div>\r\n    );\r\n};\r\n\r\nexport default DeploymentstatusTable;\r\n", "//Import Library\r\nimport { Contain<PERSON>, <PERSON>, <PERSON>, Button } from \"react-bootstrap\";\r\nimport Link from \"next/link\";\r\n\r\n//Import services/components\r\nimport ExpertiseTable from \"./expertiseTable\";\r\nimport { useTranslation } from 'next-i18next';\r\nimport { serverSideTranslations } from 'next-i18next/serverSideTranslations';\r\nimport PageHeading from \"../../../components/common/PageHeading\";\r\nimport { canAddExpertise } from \"../permissions\";\r\nimport NoAccessMessage from \"../../rNoAccess\";\r\nimport { useSelector } from \"react-redux\";\r\n\r\nconst ExpertiseIndex = (props: any) => {\r\n  const { t } = useTranslation('common');\r\n  const ShowExpertiseIndex = () => {\r\n    return (\r\n      <div>\r\n        <Container style={{ overflowX: \"hidden\" }} fluid className=\"p-0\">\r\n          <Row>\r\n            <Col xs={12}>\r\n              <PageHeading title={t(\"adminsetting.Expertise.Forms.Expertise\")} />\r\n            </Col>\r\n          </Row>\r\n          <Row>\r\n            <Col xs={12}>\r\n              <Link\r\n                href=\"/adminsettings/[...routes]\"\r\n                as=\"/adminsettings/create_expertise\"\r\n                >\r\n                <Button variant=\"secondary\" size=\"sm\">\r\n                {t('adminsetting.Expertise.Forms.AddExpertise')}\r\n              </Button>\r\n              </Link>\r\n            </Col>\r\n          </Row>\r\n          <Row className=\"mt-3\">\r\n            <Col xs={12}>\r\n              <ExpertiseTable />\r\n            </Col>\r\n          </Row>\r\n        </Container>\r\n      </div>\r\n    );\r\n  }\r\n\r\n  const ShowAddExpertise = canAddExpertise(() => <ShowExpertiseIndex />);\r\n  const state:any = useSelector((state) => state);\r\n  if (!(state?.permissions?.expertise?.['create:any'])) {\r\n    return <NoAccessMessage />\r\n  }\r\n  return(\r\n    <ShowAddExpertise />\r\n  )\r\n}\r\n\r\nexport async function getServerSideProps({ locale }: any) {\r\n  return {\r\n    props: {\r\n      ...(await serverSideTranslations(locale, ['common'])),\r\n    },\r\n  }\r\n}\r\n\r\nexport default ExpertiseIndex;", "//Import Library\r\nimport React, { useEffect, useState } from \"react\";\r\nimport { <PERSON><PERSON>, <PERSON><PERSON> } from \"react-bootstrap\";\r\nimport toast from 'react-hot-toast';\r\n\r\n//Import services/components\r\nimport apiService from \"../../../services/apiService\";\r\nimport RKITable from \"../../../components/common/RKITable\";\r\nimport { useTranslation } from 'next-i18next';\r\n\r\nfunction VspaceAdmin(_props: any) {\r\n  const { t } = useTranslation('common');\r\n  const [tabledata, setDataToTable] = useState<any[]>([]);\r\n  const [, setLoading] = useState<boolean>(false);\r\n  const [totalRows, setTotalRows] = useState<number>(0);\r\n  const [perPage, setPerPage] = useState<number>(10);\r\n  const [isModalShow, setModal] = useState<boolean>(false);\r\n  const [newStatus, setNewStatus] = useState<string>(\"\");\r\n  const [selectUserDetails, setSelectUserDetails] = useState<any>({});\r\n\r\n\r\n  const usersParams = {\r\n    sort: { created_at: \"desc\" },\r\n    limit: perPage,\r\n    page: 1,\r\n    query: { vspace_status: \"Request Pending\" },\r\n  };\r\n\r\n  const columns = [\r\n    {\r\n      name: t(\"adminsetting.FocalPointsApprovalTable.Username\"),\r\n      selector: \"username\",\r\n      cell: (d: any) => d.username,\r\n    },\r\n    {\r\n      name: t(\"adminsetting.FocalPointsApprovalTable.Email\"),\r\n      selector: \"email\",\r\n      cell: (d: any) => d.email,\r\n    },\r\n    {\r\n      name: t(\"adminsetting.FocalPointsApprovalTable.Action\"),\r\n      selector: \"\",\r\n      cell: (d: any) => (\r\n        <div>\r\n          <Button\r\n            variant=\"primary\"\r\n            size=\"sm\"\r\n            onClick={() => userAction(d, \"approve\")}\r\n          >\r\n            {t(\"adminsetting.FocalPointsApprovalTable.aprov\")}\r\n          </Button>\r\n          &nbsp;\r\n          <Button\r\n            variant=\"secondary\"\r\n            size=\"sm\"\r\n            onClick={() => userAction(d, \"reject\")}\r\n          >\r\n            {t(\"adminsetting.FocalPointsApprovalTable.Reject\")}\r\n          </Button>\r\n        </div>\r\n      ),\r\n    },\r\n  ];\r\n\r\n  const getUsersData = async () => {\r\n\r\n    setLoading(true);\r\n    const response = await apiService.get(\"/users\", usersParams);\r\n    if (response && response.data) {\r\n      setDataToTable(response.data);\r\n      setTotalRows(response.totalCount);\r\n      setLoading(false);\r\n    }\r\n  };\r\n  const handlePageChange = (page: number) => {\r\n    usersParams.limit = perPage;\r\n    usersParams.page = page;\r\n    getUsersData();\r\n  };\r\n\r\n  const handlePerRowsChange = async (newPerPage: number, page: number) => {\r\n    usersParams.limit = newPerPage;\r\n    usersParams.page = page;\r\n    setLoading(true);\r\n    const response = await apiService.get(\"/users\", usersParams);\r\n    if (response && response.data && response.data.length > 0) {\r\n      setDataToTable(response.data);\r\n      setPerPage(newPerPage);\r\n      setLoading(false);\r\n    }\r\n  };\r\n\r\n  useEffect(() => {\r\n    getUsersData();\r\n  }, []);\r\n\r\n  const userAction = async (d: any, status: string) => {\r\n    setModal(true);\r\n    setNewStatus(status);\r\n    if (d && d._id) {\r\n      const setStatus = status === \"approve\" ? \"Approved\" :\"Rejected\";\r\n      setSelectUserDetails({ ...d, vspace_status: setStatus });\r\n    }\r\n\r\n  };\r\n\r\n  const modalConfirm = async () => {\r\n\r\n    if( selectUserDetails['vspace_status'] === \"Rejected\" ){\r\n\r\n      await apiService.remove(`/users/${selectUserDetails[\"_id\"]}`);\r\n      getUsersData();\r\n      toast.error(t(\"adminsetting.FocalPointsApprovalTable.Rejected\"));\r\n      setSelectUserDetails({});\r\n      setModal(false);\r\n\r\n    } else {\r\n      const updatedData = await apiService.patch(\r\n        `/users/${selectUserDetails[\"_id\"]}`,\r\n        selectUserDetails\r\n      );\r\n      if (updatedData && updatedData.status === 403) {\r\n        toast.error(\r\n          updatedData.response && updatedData.response.message\r\n            ? updatedData.response.message\r\n            : t(\"adminsetting.FocalPointsApprovalTable.Somethingwentswrong\")\r\n        );\r\n        return;\r\n      } else {\r\n        getUsersData();\r\n        toast.success(t(\"adminsetting.FocalPointsApprovalTable.Approvemm\"));\r\n        setSelectUserDetails({});\r\n        setModal(false);\r\n      }\r\n    }\r\n  };\r\n\r\n  const modalHide = () => setModal(false);\r\n\r\n  return (\r\n    <div>\r\n      <Modal show={isModalShow} onHide={modalHide}>\r\n        <Modal.Header closeButton>\r\n          <Modal.Title>\r\n            {newStatus.charAt(0).toUpperCase() + newStatus.slice(1)} {t(\"adminsetting.FocalPointsApprovalTable.User\")}\r\n          </Modal.Title>\r\n        </Modal.Header>\r\n        <Modal.Body>{t(\"adminsetting.FocalPointsApprovalTable.Areyousurewantto\")} {newStatus} {t(\"adminsetting.FocalPointsApprovalTable.thisuser?\")}</Modal.Body>\r\n        <Modal.Footer>\r\n          <Button variant=\"secondary\" onClick={modalHide}>\r\n            {t(\"adminsetting.FocalPointsApprovalTable.Cancel\")}\r\n          </Button>\r\n          <Button variant=\"primary\" onClick={modalConfirm}>\r\n            {t(\"adminsetting.FocalPointsApprovalTable.Yes\")}\r\n          </Button>\r\n        </Modal.Footer>\r\n      </Modal>\r\n\r\n      <RKITable\r\n        columns={columns}\r\n        data={tabledata}\r\n        totalRows={totalRows}\r\n        pagServer={true}\r\n        handlePerRowsChange={handlePerRowsChange}\r\n        handlePageChange={handlePageChange}\r\n      />\r\n    </div>\r\n  );\r\n}\r\n\r\nexport default VspaceAdmin;\r\n", "//Import Library\r\nimport { Container, <PERSON>, <PERSON>, Button } from \"react-bootstrap\";\r\nimport Link from \"next/link\";\r\n\r\n//Import services/components\r\nimport PageHeading from \"../../../components/common/PageHeading\";\r\nimport ProjectstatusTable from \"./projectstatusTable\";\r\nimport { useTranslation } from 'next-i18next';\r\nimport { canAddProjectStatus } from \"../permissions\";\r\nimport { useSelector } from \"react-redux\";\r\nimport NoAccessMessage from \"../../rNoAccess\";\r\n\r\nconst ProjectstatusIndex = (_props: any) => {\r\n  const { t } = useTranslation('common');\r\n  const ShowProjectstatusIndex = () => {\r\n    return (\r\n      <div>\r\n        <Container style={{ overflowX: \"hidden\" }} fluid className=\"p-0\">\r\n          <Row>\r\n            <Col xs={12}>\r\n              <PageHeading title={t(\"adminsetting.ProjectStatus.ProjectStatus\")} />\r\n            </Col>\r\n          </Row>\r\n          <Row>\r\n            <Col xs={12}>\r\n              <Link\r\n                href=\"/adminsettings/[...routes]\"\r\n                as=\"/adminsettings/create_projectstatus\"\r\n                >\r\n                <Button variant=\"secondary\" size=\"sm\">\r\n                  {t(\"adminsetting.ProjectStatus.AddProjectStatus\")}\r\n              </Button>\r\n              </Link>\r\n            </Col>\r\n          </Row>\r\n          <Row className=\"mt-3\">\r\n            <Col xs={12}>\r\n              <ProjectstatusTable />\r\n            </Col>\r\n          </Row>\r\n        </Container>\r\n      </div>\r\n    );\r\n  }\r\n\r\n  const ShowAddProjectStatus = canAddProjectStatus(() => <ShowProjectstatusIndex />);\r\n  const state:any = useSelector((state) => state);\r\n  if (!(state?.permissions?.project_status?.['create:any'])) {\r\n    return <NoAccessMessage />\r\n  }\r\n  return(\r\n    <ShowAddProjectStatus />\r\n  )  \r\n}\r\nexport default ProjectstatusIndex;", "//Import Library\r\nimport React, { useEffect, useState } from \"react\";\r\nimport { <PERSON><PERSON>, <PERSON><PERSON> } from \"react-bootstrap\";\r\nimport toast from 'react-hot-toast';\r\n\r\n//Import services/components\r\nimport apiService from \"../../../services/apiService\";\r\nimport { useTranslation } from 'next-i18next';\r\nimport RKITable from \"../../../components/common/RKITable\";\r\n\r\n\r\nfunction InstitutionTable(_props: any) {\r\n  const [tabledata, setDataToTable] = useState<any[]>([]);\r\n  const [, setLoading] = useState<boolean>(false);\r\n  const [totalRows, setTotalRows] = useState<number>(0);\r\n  const [perPage, setPerPage] = useState<number>(10);\r\n  const [isModalShow, setModal] = useState<boolean>(false);\r\n  const [newStatus, setNewStatus] = useState<string>(\"\");\r\n  const [selectUserDetails, setSelectUserDetails] = useState<any>({});\r\n  const { t } = useTranslation('common');\r\n\r\n\r\n\r\n  const instParams = {\r\n    sort: { created_at: \"desc\" },\r\n    limit: perPage,\r\n    page: 1,\r\n    query: { status: \"Request Pending\" },\r\n  };\r\n\r\n  const columns = [\r\n    {\r\n      name: t(\"Title\"),\r\n      selector: \"title\",\r\n      cell: (d: any) => d.title,\r\n    },\r\n    {\r\n      name: t(\"Email\"),\r\n      selector: \"email\",\r\n      cell: (d: any) => d.email,\r\n    },\r\n    {\r\n      name: (\"ContactName\"),\r\n      selector: \"contact_name\",\r\n      cell: (d: any) => d.contact_name,\r\n    },\r\n    {\r\n      name: t(\"Action\"),\r\n      selector: \"\",\r\n      cell: (d: any) => (\r\n        <div>\r\n          <Button\r\n            variant=\"primary\"\r\n            size=\"sm\"\r\n            onClick={() => instAction(d, \"approve\")}\r\n          >\r\n            {t(\"instu.Approves\")}\r\n          </Button>\r\n          &nbsp;\r\n          <Button\r\n            variant=\"secondary\"\r\n            size=\"sm\"\r\n            onClick={() => instAction(d, \"reject\")}\r\n          >\r\n            {t(\"instu.Reject\")}\r\n          </Button>\r\n        </div>\r\n      ),\r\n    },\r\n  ];\r\n\r\n  const getInstData = async () => {\r\n    setLoading(true);\r\n    const response = await apiService.get(\"/institution\", instParams);\r\n    if (response && response.data) {\r\n      setDataToTable(response.data);\r\n      setTotalRows(response.totalCount);\r\n      setLoading(false);\r\n    }\r\n  };\r\n  const handlePageChange = (page: number) => {\r\n    instParams.limit = perPage;\r\n    instParams.page = page;\r\n    getInstData();\r\n  };\r\n\r\n  const handlePerRowsChange = async (newPerPage: number, page: number) => {\r\n    instParams.limit = newPerPage;\r\n    instParams.page = page;\r\n    setLoading(true);\r\n    const response = await apiService.get(\"/institution\", instParams);\r\n    if (response && response.data && response.data.length > 0) {\r\n      setDataToTable(response.data);\r\n      setPerPage(newPerPage);\r\n      setLoading(false);\r\n    }\r\n  };\r\n\r\n  useEffect(() => {\r\n    getInstData();\r\n  }, []);\r\n\r\n  const instAction = async (d: any, status: string) => {\r\n    setModal(true);\r\n    setNewStatus(status);\r\n    if (d && d._id) {\r\n      const setStatus = status === \"approve\" ? \"Approved\" : \"Rejected\";\r\n      setSelectUserDetails({ ...d, status: setStatus });\r\n    }\r\n  };\r\n\r\n  const modalConfirm = async () => {\r\n\r\n    if( selectUserDetails['status'] === \"Rejected\"){\r\n      await apiService.remove(`/institution/${selectUserDetails[\"_id\"]}`);\r\n      getInstData();\r\n      toast.error(t(\"instu.Rejected\"));\r\n      setSelectUserDetails({});\r\n      setModal(false);\r\n\r\n    }else {\r\n      const updatedData = await apiService.patch(\r\n        `/institution/${selectUserDetails[\"_id\"]}`,\r\n        selectUserDetails\r\n      );\r\n      if (updatedData && updatedData.status === 403) {\r\n        toast.error(\r\n          updatedData.response && updatedData.response.message\r\n            ? updatedData.response.message\r\n            : t(\"adminsetting.FocalPointsApprovalTable.Somethingwentswrong\")\r\n        );\r\n        return;\r\n      } else {\r\n        getInstData();\r\n        toast.success(t(\"instu.Approve\"));\r\n        setSelectUserDetails({});\r\n        setModal(false);\r\n      }\r\n    }\r\n    getInstData();\r\n    setSelectUserDetails({});\r\n    setModal(false);\r\n  };\r\n\r\n  const modalHide = () => setModal(false);\r\n\r\n  return (\r\n    <div>\r\n      <Modal show={isModalShow} onHide={modalHide}>\r\n        <Modal.Header closeButton>\r\n          <Modal.Title>\r\n            {newStatus.charAt(0).toUpperCase() + newStatus.slice(1)} {t(\"Organisation\")}\r\n          </Modal.Title>\r\n        </Modal.Header>\r\n        <Modal.Body>\r\n        {t(\"adminsetting.FocalPointsApprovalTable.Areyousurewantto\")}  {newStatus} {t(\"thisOrganisation?\")}\r\n        </Modal.Body>\r\n        <Modal.Footer>\r\n          <Button variant=\"secondary\" onClick={modalHide}>\r\n          {t(\"cancel\")}\r\n          </Button>\r\n          <Button variant=\"primary\" onClick={modalConfirm}>\r\n          {t(\"yes\")}\r\n          </Button>\r\n        </Modal.Footer>\r\n      </Modal>\r\n\r\n      <RKITable\r\n        columns={columns}\r\n        data={tabledata}\r\n        totalRows={totalRows}\r\n        pagServer={true}\r\n        handlePerRowsChange={handlePerRowsChange}\r\n        handlePageChange={handlePageChange}\r\n      />\r\n    </div>\r\n  );\r\n}\r\n\r\nexport default InstitutionTable;\r\n", "//Import Library\r\nimport { useState, useEffect } from \"react\";\r\n\r\nimport toast from 'react-hot-toast';\r\nimport Link from \"next/link\";\r\nimport { Mo<PERSON>, Button } from \"react-bootstrap\";\r\n\r\n//Import services/components\r\nimport apiService from \"../../../services/apiService\";\r\nimport RKITable from \"../../../components/common/RKITable\";\r\nimport RegionTableFilter from \"./regionTableFilter\";\r\nimport { useTranslation } from 'next-i18next';\r\n\r\nconst RegionTable = (_props: any) => {\r\n    const { t } = useTranslation('common');\r\n    const [tabledata, setDataToTable] = useState([]);\r\n    const [, setLoading] = useState(false);\r\n    const [totalRows, setTotalRows] = useState(0);\r\n    const [perPage, setPerPage] = useState(10);\r\n    const [isModalShow, setModal] = useState(false);\r\n    const [selectRegionDetails, setSelectRegion] = useState({});\r\n    const [countryId, setCountryId]: any = useState(\"\");\r\n\r\n    const regionParams = {\r\n        sort: { title: \"asc\" },\r\n        limit: perPage,\r\n        page: 1,\r\n        query: {},\r\n    };\r\n\r\n    const columns = [\r\n        {\r\n            name: t(\"adminsetting.Regions.Region\"),\r\n            selector: (row: any) => row.title,\r\n            sortable: true,\r\n        },\r\n        {\r\n            name: t(\"adminsetting.Regions.Country\"),\r\n            selector: (row: any) => row.country?.title || '',\r\n            sortable: true,\r\n            cell: (d: any) => (d.country && d.country.title ? d.country.title : \"\"),\r\n        },\r\n        {\r\n            name: t(\"adminsetting.Regions.Action\"),\r\n            selector: (row: any) => row._id,\r\n            sortable: false,\r\n            cell: (d: any) => (\r\n                <div>\r\n                    <Link href=\"/adminsettings/[...routes]\" as={`/adminsettings/edit_region/${d._id}`}>\r\n\r\n                        <i className=\"icon fas fa-edit\" />\r\n\r\n                    </Link>\r\n                    &nbsp;\r\n                    <a onClick={() => userAction(d)}>\r\n                        <i className=\"icon fas fa-trash-alt\" />\r\n                    </a>{\" \"}\r\n                </div>\r\n            ),\r\n        },\r\n    ];\r\n\r\n    const getRegionData = async () => {\r\n        setLoading(true);\r\n        const response = await apiService.get(\"/region\", regionParams);\r\n        if (response && response.data) {\r\n            setDataToTable(response.data);\r\n            setTotalRows(response.totalCount);\r\n            setLoading(false);\r\n        }\r\n    };\r\n\r\n    const handlePageChange = (page: any) => {\r\n        regionParams.limit = perPage;\r\n        regionParams.page = page;\r\n        countryId && (regionParams.query = { country: countryId.value });\r\n        getRegionData();\r\n    };\r\n\r\n    const handlePerRowsChange = async (newPerPage: any, page: any) => {\r\n        regionParams.limit = newPerPage;\r\n        regionParams.page = page;\r\n        countryId && (regionParams.query = { country: countryId.value });\r\n        setLoading(true);\r\n        const response = await apiService.get(\"/region\", regionParams);\r\n        if (response && response.data && response.data.length > 0) {\r\n            setDataToTable(response.data);\r\n            setPerPage(newPerPage);\r\n            setLoading(false);\r\n        }\r\n    };\r\n\r\n    const userAction = async (row: any) => {\r\n        setSelectRegion(row._id);\r\n        setModal(true);\r\n    };\r\n\r\n    const modalConfirm = async () => {\r\n        try {\r\n            await apiService.remove(`/region/${selectRegionDetails}`);\r\n            getRegionData();\r\n            setModal(false);\r\n            toast.success(t(\"adminsetting.Regions.Table.regionDeletedSuccessfully\"));\r\n        } catch (error) {\r\n            toast.error(t(\"adminsetting.Regions.Table.errorDeletingRegion\"));\r\n        }\r\n    };\r\n\r\n    const modalHide = () => setModal(false);\r\n\r\n    //Handle Country Search\r\n    const handleCountry = (country: any) => {\r\n        setCountryId(country);\r\n    };\r\n\r\n    useEffect(() => {\r\n        getRegionData();\r\n    }, []);\r\n\r\n    useEffect(() => {\r\n        countryId && (regionParams.query = { country: countryId.value });\r\n        getRegionData();\r\n    }, [countryId]);\r\n\r\n    return (\r\n        <div className=\"region__table\">\r\n            <Modal show={isModalShow} onHide={modalHide}>\r\n                <Modal.Header closeButton>\r\n                    <Modal.Title>{t(\"adminsetting.Regions.DeleteRegion\")}</Modal.Title>\r\n                </Modal.Header>\r\n                <Modal.Body>{t(\"adminsetting.Regions.Areyousurewanttodeletethisregion?\")}</Modal.Body>\r\n                <Modal.Footer>\r\n                    <Button variant=\"secondary\" onClick={modalHide}>\r\n                        {t(\"adminsetting.Regions.Cancel\")}\r\n                    </Button>\r\n                    <Button variant=\"primary\" onClick={modalConfirm}>\r\n                        {t(\"adminsetting.Regions.Yes\")}\r\n                    </Button>\r\n                </Modal.Footer>\r\n            </Modal>\r\n            <RegionTableFilter countryHandler={handleCountry} value={countryId} />\r\n            <RKITable\r\n                columns={columns}\r\n                data={tabledata}\r\n                totalRows={totalRows}\r\n                pagServer={true}\r\n                handlePerRowsChange={handlePerRowsChange}\r\n                handlePageChange={handlePageChange}\r\n            />\r\n        </div>\r\n    );\r\n};\r\n\r\nexport default RegionTable;\r\n", "//Import Library\r\nimport { useState, useRef, useEffect } from \"react\";\r\nimport { Container, Card, Row, Col, Form, Button } from \"react-bootstrap\";\r\nimport ValidationFormWrapper from \"../../../components/common/ValidationFormWrapper\";\r\nimport { TextInput } from \"../../../components/common/FormValidation\";\r\nimport Router from \"next/router\";\r\n\r\nimport toast from 'react-hot-toast';\r\n\r\n//Import services/components\r\nimport apiService from \"../../../services/apiService\";\r\nimport { useTranslation } from 'next-i18next';\r\n\r\ninterface MailSettingsFormProps {\r\n    [key: string]: any;\r\n}\r\n\r\nconst MailSettingsForm = (props: MailSettingsFormProps) => {\r\n\r\n  const _initialrole = {\r\n    title: '',\r\n  }\r\n\r\n  const [initialVal, setInitialVal] = useState<any>(_initialrole);\r\n    const editform = props.routes && props.routes[0] === \"edit_role\" && props.routes[1];\r\n  const { t } = useTranslation('common');\r\n\r\n\r\n  const formRef = useRef(null);\r\n\r\n  const resetHandler = () => {\r\n    // Reset validation state (Formik handles this automatically)\r\n    window.scrollTo(0, 0);\r\n  };\r\n\r\n  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement>) => {\r\n    if (e.target) {\r\n      const { name, value } = e.target;\r\n      setInitialVal((prevState: any) => ({\r\n        ...prevState,\r\n        [name]: value\r\n      }));\r\n    }\r\n  }\r\n\r\n  const handleSubmit = async (event: any, values?: any) => {\r\n    if (event) event.preventDefault();\r\n    // Use Formik values if available, otherwise fall back to initialVal\r\n    const formValues = values || initialVal;\r\n    const obj = {\r\n      title: formValues.title.trim(),\r\n    };\r\n\r\n    let response;\r\n    if (editform) {\r\n      response = await apiService.patch(`/roles/${props.routes[1]}`, obj);\r\n    } else {\r\n      response = await apiService.post(\"/roles\", obj);\r\n    }\r\n    if (response && response._id) {\r\n      toast.success(t(\"Roleisaddedsuccessfully\"));\r\n      Router.push(\"/adminsettings/role\");\r\n    } else {\r\n      toast.error(response)\r\n    }\r\n  }\r\n\r\n  useEffect(() => {\r\n    const roleParams = {\r\n      query: {},\r\n      sort: { title: \"asc\" },\r\n      limit: \"~\",\r\n    };\r\n    if (editform) {\r\n      const getRoleData = async () => {\r\n        const response = await apiService.get(`/roles/${props.routes[1]}`, roleParams);\r\n        setInitialVal((prevState: any) => ({ ...prevState, ...response }));\r\n      }\r\n      getRoleData();\r\n    }\r\n  }, []);\r\n\r\n  return (\r\n    <div>\r\n      <Container className=\"formCard\" fluid>\r\n        <Card style={{ marginTop: \"5px\", boxShadow: \"0 4px 8px 0 rgba(0, 0, 0, 0.2), 0 6px 20px 0 rgba(0, 0, 0, 0.19)\" }}>\r\n          <ValidationFormWrapper onSubmit={handleSubmit} ref={formRef} initialValues={initialVal} enableReinitialize={true}>\r\n            <Card.Body>\r\n              <Row>\r\n                <Col>\r\n                  <Card.Title>{t(\"Role\")}</Card.Title>\r\n                </Col>\r\n              </Row>\r\n              <hr />\r\n              <Row>\r\n                <Col md lg={6} sm={12}>\r\n                  <Form.Group>\r\n                    <Form.Label className=\"required-field\">{t(\"Role\")}</Form.Label>\r\n                    <TextInput\r\n                      name=\"title\"\r\n                      id=\"title\"\r\n                      required value={initialVal.title}\r\n                      validator={((value: string) => String(value || '').trim() !== \"\")}\r\n                       successMessage= {t(\"Looksgood\")}\r\n                       errorMessage={{\r\n                        validator: t(\"PleaseAddtheRole\")}}\r\n                      onChange={handleChange}\r\n                    />\r\n                  </Form.Group>\r\n                </Col>\r\n              </Row>\r\n              <Row className=\"my-4\">\r\n                <Col xs lg=\"2\">\r\n                  <Button type=\"submit\" variant=\"primary\">{t(\"submit\")}</Button>\r\n                </Col>\r\n                <Col>\r\n                  <Button onClick={resetHandler} variant=\"info\">{t(\"reset\")}</Button>\r\n                </Col>\r\n              </Row>\r\n            </Card.Body>\r\n          </ValidationFormWrapper>\r\n        </Card>\r\n      </Container>\r\n    </div>\r\n  );\r\n}\r\nexport default MailSettingsForm;\r\n", "//Import Library\r\nimport { Contain<PERSON>, <PERSON>, <PERSON>, Button } from \"react-bootstrap\";\r\nimport Link from \"next/link\";\r\n\r\n//Import services/components\r\nimport PageHeading from \"../../../components/common/PageHeading\";\r\nimport InstitutionNetworkTable from \"./institutionNetworkTable\";\r\nimport { useTranslation } from 'next-i18next';\r\nimport { canAddOrganisationNetworks } from \"../permissions\";\r\nimport { useSelector } from \"react-redux\";\r\nimport NoAccessMessage from \"../../rNoAccess\";\r\n\r\n\r\n\r\nconst InstitutionNetworkIndex = (_props: any) => {\r\n  const { t } = useTranslation('common');\r\n  const ShowInstitutionNetworkIndex = () => {\r\n    return (\r\n      <Container style={{ overflowX: \"hidden\" }} fluid className=\"p-0\">\r\n        <Row>\r\n          <Col xs={12}>\r\n            <PageHeading title= {t(\"adminsetting.Organisationnetworks.OrganisationNetworks\")} />\r\n          </Col>\r\n        </Row>\r\n        <Row>\r\n          <Col xs={12}>\r\n            <Link\r\n              href=\"/adminsettings/[...routes]\"\r\n              as=\"/adminsettings/create_institution_network\"\r\n              >\r\n              <Button variant=\"secondary\" size=\"sm\">\r\n              {t(\"adminsetting.Organisationnetworks.AddOrganisationNetwork\")}\r\n              </Button>\r\n            </Link>\r\n          </Col>\r\n        </Row>\r\n        <Row className=\"mt-3\">\r\n          <Col xs={12}>\r\n            <InstitutionNetworkTable />\r\n          </Col>\r\n        </Row>\r\n      </Container>\r\n    );\r\n  }\r\n  \r\n  const ShowAddOrganisationNetworks = canAddOrganisationNetworks(() => <ShowInstitutionNetworkIndex />);\r\n  const state:any = useSelector((state) => state);\r\n  if (!(state?.permissions?.institution_network?.['create:any'])) {\r\n    return <NoAccessMessage />\r\n  }\r\n  return(\r\n    <ShowAddOrganisationNetworks />\r\n  )\r\n}\r\nexport default InstitutionNetworkIndex;", "//Import Library\r\nimport { useState, useRef, useEffect } from \"react\";\r\nimport { Container, Card, Row, Col, Form, Button } from \"react-bootstrap\";\r\nimport ValidationFormWrapper from \"../../../components/common/ValidationFormWrapper\";\r\nimport { TextInput } from \"../../../components/common/FormValidation\";\r\nimport Router from \"next/router\";\r\nimport toast from 'react-hot-toast';\r\nimport Link from \"next/link\";\r\n\r\n//Import services/components\r\nimport apiService from \"../../../services/apiService\";\r\nimport { useTranslation } from 'next-i18next';\r\nimport { RiskLevel } from \"../../../types\";\r\n\r\ninterface RisklevelFormProps {\r\n    [key: string]: any;\r\n}\r\n\r\nconst RisklevelForm = (props: RisklevelFormProps) => {\r\n    const _initialrisklevel = {\r\n        title: \"\",\r\n        level: \"\",\r\n    };\r\n\r\n    const [initialVal, setInitialVal] = useState<RiskLevel>(_initialrisklevel);\r\n\r\n    const editform = props.routes && props.routes[0] === \"edit_risklevel\" && props.routes[1];\r\n    const { t } = useTranslation('common');\r\n\r\n    const formRef = useRef(null);\r\n\r\n    const resetHandler = () => {\r\n        setInitialVal(_initialrisklevel);\r\n        // Reset validation state (Formik handles this automatically)\r\n        window.scrollTo(0, 0);\r\n    };\r\n\r\n    const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement>) => {\r\n        if (e.target) {\r\n            const { name, value } = e.target;\r\n            setInitialVal((prevState) => ({\r\n                ...prevState,\r\n                [name]: value,\r\n            }));\r\n        }\r\n    };\r\n\r\n    const toUppercase = (_str: string) => {\r\n        if (!_str) _str = \"\";\r\n        return _str.charAt(0).toUpperCase() + _str.slice(1);\r\n    };\r\n\r\n    const handleSubmit = async (event: any, values?: any) => {\r\n        event.preventDefault();\r\n        // Use Formik values if available, otherwise fall back to initialVal\r\n        const formValues = values || initialVal;\r\n        const obj = {\r\n            title: formValues.title.trim(),\r\n            level: parseInt(formValues.level),\r\n        };\r\n\r\n        let response;\r\n        let toastMsg;\r\n        if (editform) {\r\n            toastMsg = \"adminsetting.RiskLevel.updatesuccess\";\r\n            response = await apiService.patch(`/risklevel/${props.routes[1]}`, obj);\r\n        } else {\r\n            toastMsg = \"adminsetting.RiskLevel.success\";\r\n            response = await apiService.post(\"/risklevel\", obj);\r\n        }\r\n        if (response && response._id) {\r\n            toast.success(t(toastMsg))\r\n            Router.push(\"/adminsettings/risklevel\");\r\n        } else {\r\n            if (response?.errorCode === 11000) {\r\n                toast.error(t(\"duplicatesNotAllowed\"))\r\n            } else {\r\n                toast.error(toUppercase(response));\r\n            }\r\n        }\r\n    };\r\n\r\n    useEffect(() => {\r\n        const risklevelParams = {\r\n            query: {},\r\n            sort: { title: \"asc\" },\r\n            limit: \"~\",\r\n        };\r\n        if (editform) {\r\n            const getRisklevelData = async () => {\r\n                const response: RiskLevel = await apiService.get(`/risklevel/${props.routes[1]}`, risklevelParams);\r\n                setInitialVal((prevState) => ({ ...prevState, ...response }));\r\n            };\r\n            getRisklevelData();\r\n        }\r\n    }, []);\r\n\r\n    return (\r\n        <div>\r\n            <Container className=\"formCard\" fluid>\r\n                <Card\r\n                    style={{\r\n                        marginTop: \"5px\",\r\n                        boxShadow: \"0 4px 8px 0 rgba(0, 0, 0, 0.2), 0 6px 20px 0 rgba(0, 0, 0, 0.19)\",\r\n                    }}\r\n                >\r\n                    <ValidationFormWrapper onSubmit={handleSubmit} ref={formRef} initialValues={initialVal} enableReinitialize={true}>\r\n                        <Card.Body>\r\n                            <Row>\r\n                                <Col>\r\n                                    <Card.Title>{t(\"adminsetting.RiskLevel.Risklevel\")}</Card.Title>\r\n                                </Col>\r\n                            </Row>\r\n                            <hr />\r\n                            <Row>\r\n                                <Col md lg={6} sm={12}>\r\n                                    <Form.Group>\r\n                                        <Form.Label className=\"required-field\">\r\n                                            {t(\"adminsetting.RiskLevel.Risklevelname\")}\r\n                                        </Form.Label>\r\n                                        <TextInput\r\n                                            name=\"title\"\r\n                                            id=\"title\"\r\n                                            required\r\n                                            value={initialVal.title}\r\n                                            validator={(value: string) => value.trim() !== \"\"}\r\n                                            errorMessage={{\r\n                                                validator: t(\"adminsetting.RiskLevel.add\"),\r\n                                            }}\r\n                                            onChange={handleChange}\r\n                                        />\r\n                                    </Form.Group>\r\n                                </Col>\r\n                                <Col md lg={6} sm={12}>\r\n                                    <Form.Group>\r\n                                        <Form.Label className=\"required-field\">\r\n                                            {t(\"adminsetting.RiskLevel.Risklevelvalue\")}\r\n                                        </Form.Label>\r\n                                        <TextInput\r\n                                            min=\"0\"\r\n                                            type=\"number\"\r\n                                            name=\"level\"\r\n                                            id=\"level\"\r\n                                            required\r\n                                            value={initialVal.level}\r\n                                            errorMessage={{\r\n                                                validator: t(\"adminsetting.RiskLevel.value\"),\r\n                                                min: t(\"adminsetting.RiskLevel.minValue\"),\r\n                                                required: t(\"adminsetting.RiskLevel.value\"),\r\n                                            }}\r\n                                            onChange={handleChange}\r\n                                        />\r\n                                    </Form.Group>\r\n                                </Col>\r\n                            </Row>\r\n                            <Row className=\"my-4\">\r\n                                <Col>\r\n                                    <Button className=\"me-2\" type=\"submit\" variant=\"primary\">\r\n                                        {t(\"submit\")}\r\n                                    </Button>\r\n                                    <Button className=\"me-2\" onClick={resetHandler} variant=\"info\">\r\n                                        {t(\"reset\")}\r\n                                    </Button>\r\n                                    <Link\r\n                                        href=\"/adminsettings/[...routes]\"\r\n                                        as={`/adminsettings/risklevel`}\r\n                                        >\r\n                                        <Button variant=\"secondary\">{t(\"Cancel\")}</Button>\r\n                                    </Link>\r\n                                </Col>\r\n                            </Row>\r\n                        </Card.Body>\r\n                    </ValidationFormWrapper>\r\n                </Card>\r\n            </Container>\r\n        </div>\r\n    );\r\n};\r\nexport default RisklevelForm;\r\n", "//Import Library\r\nimport { Container, <PERSON>, <PERSON>, Button } from \"react-bootstrap\";\r\nimport Link from \"next/link\";\r\n\r\n//Import services/components\r\nimport PageHeading from \"../../../components/common/PageHeading\";\r\nimport HazardTable from \"./hazardTable\";\r\nimport { useTranslation } from 'next-i18next';\r\nimport { serverSideTranslations } from 'next-i18next/serverSideTranslations';\r\nimport { canAddHazards } from \"../permissions\";\r\nimport { useSelector } from \"react-redux\";\r\nimport NoAccessMessage from \"../../rNoAccess\";\r\n\r\nconst HazardIndex = (_props: any) => {\r\n  const { t } = useTranslation(\"common\");\r\n  const ShowHazardIndex = () => {\r\n    return (\r\n      <Container style={{ overflowX: \"hidden\" }} fluid className=\"p-0\">\r\n        <Row>\r\n          <Col xs={12}>\r\n            <PageHeading title={t(\"menu.hazards\")} />\r\n          </Col>\r\n        </Row>\r\n        <Row>\r\n          <Col xs={12}>\r\n            <Link\r\n              href=\"/adminsettings/[...routes]\"\r\n              as=\"/adminsettings/create_hazard\"\r\n              >\r\n              <Button variant=\"secondary\" size=\"sm\">\r\n                {t(\"addHazard\")}\r\n              </Button>\r\n            </Link>\r\n          </Col>\r\n        </Row>\r\n        <Row className=\"mt-3\">\r\n          <Col xs={12}>\r\n            <HazardTable />\r\n          </Col>\r\n        </Row>\r\n      </Container>\r\n    );\r\n  }\r\n\r\n  const ShowAddHazards = canAddHazards(() => <ShowHazardIndex />);\r\n  const state:any = useSelector((state) => state);\r\n  if (!(state?.permissions?.hazard?.['create:any'])) {\r\n    return <NoAccessMessage />\r\n  }\r\n  return(\r\n    <ShowAddHazards />\r\n  );\r\n};\r\n\r\nexport async function getStaticProps({ locale } : { locale: string }) {\r\n  return {\r\n    props: {\r\n      ...(await serverSideTranslations(locale, ['common'])),\r\n    },\r\n  }\r\n}\r\n\r\nexport default HazardIndex;\r\n", "//Import Library\r\nimport { useRouter } from 'next/router';\r\n\r\n//Import services/components\r\nimport FocalPointShow from './approval/focal_point_appoval';\r\nimport VirtualSpaceShow from './approval/vspace_appoval'\r\nimport InstitutionApproval from './approval/institution_approval';\r\nimport CountryIndex from './country';\r\nimport CountryForm from './country/form';\r\nimport RegionForm from './region/form';\r\nimport RegionIndex from './region';\r\nimport HazardIndex from './hazard';\r\nimport HazardForm from './hazard/forms';\r\nimport HazardTypeIndex from './hazardtypes';\r\nimport HazardTypeForm from './hazardtypes/forms';\r\nimport UpdateTypeIndex from './updateType';\r\nimport UpdateTypeForm from './updateType/forms';\r\nimport AreaOfWorkIndex from './areaOfWork';\r\nimport AreaOfWorkForm from './areaOfWork/forms';\r\nimport SyndromeIndex from './syndrome';\r\nimport SyndromeForm from './syndrome/form';\r\nimport CategoryIndex from './categories';\r\nimport CategoryForm from './categories/form';\r\nimport DeploymentstatusIndex from './deploymentstatus';\r\nimport DeploymentstatusForm from './deploymentstatus/form';\r\nimport ExpertiseIndex from './expertise';\r\nimport ExpertiseForm from './expertise/form';\r\nimport RisklevelIndex from './risklevel';\r\nimport RisklevelForm from './risklevel/form';\r\nimport RoleIndex from './roles';\r\nimport RoleForm from './roles/form';\r\nimport Content from './content';\r\nimport MailSettingsForm from './mailsettings/form';\r\nimport WorldregionIndex from './worldregion';\r\nimport WorldregionForm from './worldregion/form';\r\nimport InstitutionTypeIndex from './institutiontypes';\r\nimport InstitutionTypeForm from './institutiontypes/form';\r\nimport InstitutionNetworkIndex from './institutionNetworks';\r\nimport InstitutionNetworkForm from './institutionNetworks/form';\r\nimport EventstatusForm from './eventstatuses/form';\r\nimport EventstatusIndex from './eventstatuses';\r\nimport OperationstatusIndex from './operationstatuses';\r\nimport OperationstatusForm from './operationstatuses/form';\r\nimport ProjectstatusIndex from './projectstatuses';\r\nimport ProjectstatusForm from './projectstatuses/form';\r\nimport UserIndex from './user';\r\nimport UserForm from './user/forms';\r\nimport LandingPageForm from './landingPage/form'\r\nimport LandingPageIndex from './landingPage';\r\nimport { serverSideTranslations } from 'next-i18next/serverSideTranslations';\r\n\r\nconst Router = () => {\r\n  const router = useRouter();\r\n  const routes: any = router.query.routes || [];\r\n\r\n\r\n  switch (routes[0]) {\r\n    case \"focal_point\":\r\n      return <FocalPointShow routes={routes} />;\r\n\r\n    case \"Vspace_point\":\r\n        return <VirtualSpaceShow routes={routes} />;\r\n\r\n    case \"institution_approval\":\r\n      return <InstitutionApproval routes={routes} />;\r\n\r\n    case \"country\":\r\n      return <CountryIndex routes={routes} />;\r\n\r\n    case \"create_country\":\r\n    case \"edit_country\":\r\n      return <CountryForm routes={routes} />;\r\n\r\n    case \"hazard\":\r\n      return <HazardIndex routes={routes} />;\r\n\r\n    case \"create_hazard\":\r\n    case \"edit_hazard\":\r\n      return <HazardForm routes={routes} />;\r\n\r\n    case \"hazardTypes\":\r\n      return <HazardTypeIndex routes={routes} />;\r\n\r\n    case \"create_hazard_types\":\r\n    case \"edit_hazard_types\":\r\n      return <HazardTypeForm routes={routes} />;\r\n\r\n    case \"region\":\r\n      return <RegionIndex routes={routes} />;\r\n\r\n    case \"create_region\":\r\n    case \"edit_region\":\r\n      return <RegionForm routes={routes} />;\r\n\r\n    case \"update_type\":\r\n      return <UpdateTypeIndex routes={routes} />;\r\n\r\n    case \"create_update_type\":\r\n    case \"edit_update_type\":\r\n      return <UpdateTypeForm routes={routes} />;\r\n\r\n    case \"area_of_work\":\r\n      return <AreaOfWorkIndex routes={routes} />;\r\n\r\n    case \"create_area_of_work\":\r\n    case \"edit_area_of_work\":\r\n      return <AreaOfWorkForm routes={routes} />;\r\n\r\n    case \"syndrome\":\r\n      return <SyndromeIndex routes={routes} />;\r\n\r\n    case \"create_syndrome\":\r\n    case \"edit_syndrome\":\r\n      return <SyndromeForm routes={routes} />;\r\n\r\n    case \"landing\":\r\n      return <LandingPageIndex routes={routes} />;\r\n\r\n    case \"create_landing\":\r\n    case \"edit_landing\":\r\n      return <LandingPageForm routes={routes} />;\r\n\r\n    case \"category\":\r\n      return <CategoryIndex routes={routes} />;\r\n\r\n    case \"create_category\":\r\n    case \"edit_category\":\r\n      return <CategoryForm routes={routes} />;\r\n\r\n    case \"deploymentstatus\":\r\n      return <DeploymentstatusIndex routes={routes} />;\r\n\r\n    case \"create_deploymentstatus\":\r\n    case \"edit_deploymentstatus\":\r\n      return <DeploymentstatusForm routes={routes} />;\r\n\r\n    case \"eventstatus\":\r\n      return <EventstatusIndex routes={routes} />;\r\n\r\n    case \"create_eventstatus\":\r\n    case \"edit_eventstatus\":\r\n      return <EventstatusForm routes={routes} />;\r\n\r\n    case \"operationstatus\":\r\n      return <OperationstatusIndex routes={routes} />;\r\n\r\n    case \"create_operationstatus\":\r\n    case \"edit_operationstatus\":\r\n      return <OperationstatusForm routes={routes} />;\r\n\r\n    case \"projectstatus\":\r\n      return <ProjectstatusIndex routes={routes} />;\r\n\r\n    case \"create_projectstatus\":\r\n    case \"edit_projectstatus\":\r\n      return <ProjectstatusForm routes={routes} />;\r\n\r\n    case \"expertise\":\r\n      return <ExpertiseIndex routes={routes} />;\r\n\r\n    case \"create_expertise\":\r\n    case \"edit_expertise\":\r\n      return <ExpertiseForm routes={routes} />;\r\n\r\n    case \"risklevel\":\r\n      return <RisklevelIndex routes={routes} />;\r\n\r\n    case \"create_risklevel\":\r\n    case \"edit_risklevel\":\r\n      return <RisklevelForm routes={routes} />;\r\n\r\n    case \"role\":\r\n      return <RoleIndex routes={routes} />;\r\n\r\n    case \"create_role\":\r\n    case \"edit_role\":\r\n      return <RoleForm routes={routes} />;\r\n\r\n    case \"users\":\r\n      return <UserIndex routes={routes} />;\r\n\r\n    case \"create_user\":\r\n    case \"edit_user\":\r\n      return <UserForm routes={routes} />;\r\n\r\n    case \"smtp\":\r\n      return <MailSettingsForm routes={routes} />;\r\n\r\n    case \"worldregion\":\r\n      return <WorldregionIndex routes={routes} />;\r\n\r\n    case \"create_worldregion\":\r\n    case \"edit_worldregion\":\r\n      return <WorldregionForm routes={routes} />;\r\n\r\n    case \"institution_type\":\r\n      return <InstitutionTypeIndex routes={routes} />;\r\n\r\n    case \"create_institution_type\":\r\n    case \"edit_institution_type\":\r\n      return <InstitutionTypeForm routes={routes} />;\r\n\r\n    case \"institution_network\":\r\n      return <InstitutionNetworkIndex routes={routes} />;\r\n\r\n    case \"create_institution_network\":\r\n    case \"edit_institution_network\":\r\n      return <InstitutionNetworkForm routes={routes} />;\r\n\r\n    case \"content\":\r\n      return <Content routes={routes} />;\r\n\r\n    default:\r\n      return null;\r\n  }\r\n};\r\n\r\nexport async function getServerSideProps({ locale }: { locale: string }) {\r\n  return {\r\n    props: {\r\n      ...(await serverSideTranslations(locale, ['common'])),\r\n    },\r\n  }\r\n}\r\n\r\nexport default Router;\r\n", "//Import Library\r\nimport { useState, useEffect } from \"react\";\r\nimport Link from \"next/link\";\r\nimport { <PERSON><PERSON>, Button } from \"react-bootstrap\";\r\n\r\nimport toast from 'react-hot-toast';\r\n\r\n//Import services/components\r\nimport RKITable from \"../../../components/common/RKITable\";\r\nimport apiService from \"../../../services/apiService\";\r\nimport { useTranslation } from 'next-i18next';\r\n\r\nconst RisklevelTable = (_props: any) => {\r\n    const [tabledata, setDataToTable] = useState([]);\r\n    const [, setLoading] = useState(false);\r\n    const [totalRows, setTotalRows] = useState(0);\r\n    const [perPage, setPerPage] = useState(10);\r\n    const [isModalShow, setModal] = useState(false);\r\n    const [selectRisklevel, setSelectRisklevel] = useState({});\r\n    const { t } = useTranslation('common');\r\n\r\n    const risklevelParams = {\r\n        sort: { title: \"asc\" },\r\n        limit: perPage,\r\n        page: 1,\r\n        query: {},\r\n    };\r\n\r\n    const columns = [\r\n        {\r\n            name: t(\"Title\"),\r\n            selector: (row: any) => row.title,\r\n            sortable: true,\r\n        },\r\n        {\r\n            name: t(\"adminsetting.RiskLevel.Level\"),\r\n            selector: (row: any) => row.level,\r\n            sortable: true,\r\n            cell: (d: any) => d.level,\r\n        },\r\n        {\r\n            name: t(\"action\"),\r\n            selector: (row: any) => row._id,\r\n            sortable: false,\r\n            cell: (d: any) => (\r\n                <div>\r\n                    <Link href=\"/adminsettings/[...routes]\" as={`/adminsettings/edit_risklevel/${d._id}`}>\r\n\r\n                        <i className=\"icon fas fa-edit\" />\r\n\r\n                    </Link>\r\n                    &nbsp;\r\n                    <a onClick={() => userAction(d)}>\r\n                        <i className=\"icon fas fa-trash-alt\" />\r\n                    </a>{\" \"}\r\n                </div>\r\n            ),\r\n        },\r\n    ];\r\n\r\n    const getRisklevelData = async () => {\r\n        setLoading(true);\r\n        const response = await apiService.get(\"/risklevel\", risklevelParams);\r\n        if (response && response.data && response.data.length > 0) {\r\n            setDataToTable(response.data);\r\n            setTotalRows(response.totalCount);\r\n            setLoading(false);\r\n        }\r\n    };\r\n    const handlePageChange = (page: any) => {\r\n        risklevelParams.limit = perPage;\r\n        risklevelParams.page = page;\r\n        getRisklevelData();\r\n    };\r\n\r\n    const handlePerRowsChange = async (newPerPage: any, page: any) => {\r\n        risklevelParams.limit = newPerPage;\r\n        risklevelParams.page = page;\r\n        setLoading(true);\r\n        const response = await apiService.get(\"/risklevel\", risklevelParams);\r\n        if (response && response.data && response.data.length > 0) {\r\n            setDataToTable(response.data);\r\n            setPerPage(newPerPage);\r\n            setLoading(false);\r\n        }\r\n    };\r\n\r\n    const userAction = async (row: any) => {\r\n        setSelectRisklevel(row._id);\r\n        setModal(true);\r\n    };\r\n\r\n    const modalConfirm = async () => {\r\n        try {\r\n            await apiService.remove(`/risklevel/${selectRisklevel}`);\r\n            getRisklevelData();\r\n            setModal(false);\r\n            toast.success(t(\"adminsetting.RiskLevel.Table.riskLevelDeletedSuccessfully\"));\r\n        } catch (error) {\r\n            toast.error(t(\"adminsetting.RiskLevel.Table.errorDeletingRiskLevel\"));\r\n        }\r\n    };\r\n\r\n    const modalHide = () => setModal(false);\r\n\r\n    useEffect(() => {\r\n        getRisklevelData();\r\n    }, []);\r\n\r\n    return (\r\n        <div>\r\n            <Modal show={isModalShow} onHide={modalHide}>\r\n                <Modal.Header closeButton>\r\n                    <Modal.Title>{t(\"adminsetting.RiskLevel.DeleteRisklevel\")}</Modal.Title>\r\n                </Modal.Header>\r\n                <Modal.Body>{t(\"adminsetting.RiskLevel.Areyousurewanttodeletethisrisklevel\")} </Modal.Body>\r\n                <Modal.Footer>\r\n                    <Button variant=\"secondary\" onClick={modalHide}>\r\n                        {t(\"Cancel\")}\r\n                    </Button>\r\n                    <Button variant=\"primary\" onClick={modalConfirm}>\r\n                        {t(\"yes\")}\r\n                    </Button>\r\n                </Modal.Footer>\r\n            </Modal>\r\n\r\n            <RKITable\r\n                columns={columns}\r\n                data={tabledata}\r\n                totalRows={totalRows}\r\n                pagServer={true}\r\n                handlePerRowsChange={handlePerRowsChange}\r\n                handlePageChange={handlePageChange}\r\n            />\r\n        </div>\r\n    );\r\n};\r\n\r\nexport default RisklevelTable;\r\n", "//Import Library\r\nimport { useState, useEffect } from \"react\";\r\nimport Link from \"next/link\";\r\nimport { Mo<PERSON>, Button } from \"react-bootstrap\";\r\n\r\n//Import services/components\r\nimport RKITable from \"../../../components/common/RKITable\";\r\nimport apiService from \"../../../services/apiService\";\r\nimport { useTranslation } from 'next-i18next';\r\n\r\n\r\nconst RoleTable = (_props: any) => {\r\n  const [tabledata, setDataToTable] = useState([]);\r\n  const [, setLoading] = useState(false);\r\n  const [totalRows, setTotalRows] = useState(0);\r\n  const [perPage, setPerPage] = useState(10);\r\n  const [isModalShow, setModal] = useState(false);\r\n  const [selectRole, setSelectRole] = useState({});\r\n  const { t } = useTranslation('common');\r\n\r\n\r\n  const roleParams = {\r\n    \"sort\": { \"title\": \"asc\" },\r\n    \"limit\": perPage,\r\n    \"page\": 1,\r\n    \"query\": {}\r\n  };\r\n\r\n  const columns = [\r\n    {\r\n      name: 'Title',\r\n      selector: 'title',\r\n    },\r\n    {\r\n      name: 'Action',\r\n      selector: \"\",\r\n      cell: (d: any) => <div><Link href=\"/adminsettings/[...routes]\" as={`/adminsettings/edit_role/${d._id}`} ><i className=\"icon fas fa-edit\" /></Link>&nbsp;<a onClick={() => userAction(d)}><i className=\"icon fas fa-trash-alt\" /></a> </div>\r\n    }\r\n  ];\r\n\r\n  const getRoleData = async () => {\r\n    setLoading(true);\r\n    const response = await apiService.get('/roles', roleParams);\r\n    if (response && response.data && response.data.length > 0) {\r\n      setDataToTable(response.data);\r\n      setTotalRows(response.totalCount);\r\n      setLoading(false);\r\n    }\r\n  };\r\n\r\n  const handlePageChange = (page: any) => {\r\n    roleParams.limit = perPage;\r\n    roleParams.page = page;\r\n    getRoleData();\r\n  };\r\n\r\n  const handlePerRowsChange = async (newPerPage: any, page: any) => {\r\n    roleParams.limit = newPerPage;\r\n    roleParams.page = page;\r\n    setLoading(true);\r\n    const response = await apiService.get('/roles', roleParams);\r\n    if (response && response.data && response.data.length > 0) {\r\n      setDataToTable(response.data);\r\n      setPerPage(newPerPage);\r\n      setLoading(false);\r\n    }\r\n  };\r\n\r\n  const userAction = async (row: any) => {\r\n    setSelectRole(row._id);\r\n    setModal(true);\r\n  }\r\n\r\n  const modalConfirm = async () => {\r\n    await apiService.remove(`/roles/${selectRole}`);\r\n    getRoleData();\r\n    setModal(false);\r\n  }\r\n\r\n  const modalHide = () => setModal(false);\r\n\r\n  useEffect(() => {\r\n    getRoleData();\r\n  }, []);\r\n\r\n  return (\r\n    <div>\r\n      <Modal show={isModalShow} onHide={modalHide}>\r\n        <Modal.Header closeButton>\r\n          <Modal.Title>{t(\"DeleteRole\")}</Modal.Title>\r\n        </Modal.Header>\r\n        <Modal.Body>{t(\"Areyousurewanttodeletethisrole\")} </Modal.Body>\r\n        <Modal.Footer>\r\n          <Button variant=\"secondary\" onClick={modalHide}>\r\n          {t(\"cancel\")}\r\n        </Button>\r\n          <Button variant=\"primary\" onClick={modalConfirm}>\r\n          {t(\"yes\")}\r\n        </Button>\r\n        </Modal.Footer>\r\n      </Modal>\r\n\r\n      <RKITable\r\n        columns={columns}\r\n        data={tabledata}\r\n        totalRows={totalRows}\r\n        pagServer={true}\r\n        handlePerRowsChange={handlePerRowsChange}\r\n        handlePageChange={handlePageChange}\r\n      />\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default RoleTable;", "//Import Library\r\nimport { useState, useRef, useEffect } from \"react\";\r\nimport { Container, Card, Row, Col, Form, Button } from \"react-bootstrap\";\r\nimport { TextInput } from \"../../../components/common/FormValidation\";\r\nimport ValidationFormWrapper from \"../../../components/common/ValidationFormWrapper\";\r\nimport Router from \"next/router\";\r\nimport toast from 'react-hot-toast';\r\nimport Link from \"next/link\";\r\n\r\n//Import services/components\r\nimport { Expertise } from \"../../../types\";\r\nimport apiService from \"../../../services/apiService\";\r\nimport { useTranslation } from 'next-i18next';\r\n\r\ninterface ExpertiseFormProps {\r\n    [key: string]: any;\r\n}\r\n\r\nconst ExpertiseForm = (props: ExpertiseFormProps) => {\r\n    const _initialexpertise = {\r\n        _id: \"\",\r\n        title: \"\",\r\n    };\r\n    const { t } = useTranslation('common');\r\n    const [initialVal, setInitialVal] = useState<Expertise>(_initialexpertise);\r\n\r\n    const editform: boolean = props.routes && props.routes[0] === \"edit_expertise\" && props.routes[1];\r\n\r\n    const formRef = useRef(null);\r\n\r\n    const resetHandler = () => {\r\n        setInitialVal(_initialexpertise);\r\n        // Reset validation state (Formik handles this automatically)\r\n        window.scrollTo(0, 0);\r\n    };\r\n\r\n    const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement>) => {\r\n        if (e.target) {\r\n            const { name, value } = e.target;\r\n            setInitialVal((prevState) => ({\r\n                ...prevState,\r\n                [name]: value,\r\n            }));\r\n        }\r\n    };\r\n\r\n    const handleSubmit = async (event: any) => {\r\n        event.preventDefault();\r\n        const obj = {\r\n            title: initialVal.title.trim(),\r\n        };\r\n\r\n        let response;\r\n        let toastMsg;\r\n        if (editform) {\r\n            toastMsg = \"adminsetting.Expertise.Forms.Expertiseisupdatedsuccessfully\";\r\n            response = await apiService.patch(`/expertise/${props.routes[1]}`, obj);\r\n        } else {\r\n            toastMsg = \"adminsetting.Expertise.Forms.Expertiseisaddedsuccessfully\";\r\n            response = await apiService.post(\"/expertise\", obj);\r\n        }\r\n        if (response && response._id) {\r\n            toast.success(t(toastMsg));\r\n            Router.push(\"/adminsettings/expertise\");\r\n        } else {\r\n            if (response?.errorCode === 11000) {\r\n                toast.error(t(\"duplicatesNotAllowed\"));\r\n            } else {\r\n                toast.error(response);\r\n            }\r\n        }\r\n    };\r\n\r\n    useEffect(() => {\r\n        const expertiseParams = {\r\n            query: {},\r\n            sort: { title: \"asc\" },\r\n            limit: \"~\",\r\n        };\r\n        if (editform) {\r\n            const getExpertiseData = async () => {\r\n                const response: Expertise = await apiService.get(`/expertise/${props.routes[1]}`, expertiseParams);\r\n                setInitialVal((prevState) => ({ ...prevState, ...response }));\r\n            };\r\n            getExpertiseData();\r\n        }\r\n    }, []);\r\n\r\n    return (\r\n        <div>\r\n            <Container className=\"formCard\" fluid>\r\n                <Card\r\n                    style={{\r\n                        marginTop: \"5px\",\r\n                        boxShadow: \"0 4px 8px 0 rgba(0, 0, 0, 0.2), 0 6px 20px 0 rgba(0, 0, 0, 0.19)\",\r\n                    }}\r\n                >\r\n                    <ValidationFormWrapper onSubmit={handleSubmit} ref={formRef} initialValues={initialVal} enableReinitialize={true}>\r\n                        <Card.Body>\r\n                            <Row>\r\n                                <Col>\r\n                                    <Card.Title>{t(\"adminsetting.Expertise.Forms.Expertise\")}</Card.Title>\r\n                                </Col>\r\n                            </Row>\r\n                            <hr />\r\n                            <Row>\r\n                                <Col md lg={6} sm={12}>\r\n                                    <Form.Group>\r\n                                        <Form.Label className=\"required-field\">\r\n                                            {t(\"adminsetting.Expertise.Forms.Expertise\")}\r\n                                        </Form.Label>\r\n                                        <TextInput\r\n                                            name=\"title\"\r\n                                            id=\"title\"\r\n                                            required\r\n                                            value={initialVal.title}\r\n                                            validator={(value: any) => String(value || '').trim() !== \"\"}\r\n                                            errorMessage={{\r\n                                                validator: t(\"adminsetting.Expertise.Forms.PleaseAddtheExpertise\"),\r\n                                            }}\r\n                                            onChange={handleChange}\r\n                                        />\r\n                                    </Form.Group>\r\n                                </Col>\r\n                            </Row>\r\n                            <Row className=\"my-4\">\r\n                                <Col>\r\n                                    <Button className=\"me-2\" type=\"submit\" variant=\"primary\">\r\n                                        {t(\"adminsetting.Expertise.Forms.Submit\")}\r\n                                    </Button>\r\n                                    <Button className=\"me-2\" onClick={resetHandler} variant=\"info\">\r\n                                        {t(\"adminsetting.Expertise.Forms.Reset\")}\r\n                                    </Button>\r\n                                    <Link\r\n                                        href=\"/adminsettings/[...routes]\"\r\n                                        as={`/adminsettings/expertise`}\r\n                                        >\r\n                                        <Button variant=\"secondary\">{t(\"adminsetting.Expertise.Forms.Cancel\")}</Button>\r\n                                    </Link>\r\n                                </Col>\r\n                            </Row>\r\n                        </Card.Body>\r\n                    </ValidationFormWrapper>\r\n                </Card>\r\n            </Container>\r\n        </div>\r\n    );\r\n};\r\nexport default ExpertiseForm;\r\n", "//Import Library\r\nimport { Contain<PERSON>, <PERSON>, <PERSON>, But<PERSON> } from \"react-bootstrap\";\r\nimport Link from \"next/link\";\r\n\r\n//Import services/components\r\nimport LandingPageTable from \"./landingPageTable\";\r\nimport { useTranslation } from 'next-i18next';\r\nimport PageHeading from \"../../../components/common/PageHeading\";\r\nimport { canAddLandingPage } from \"../permissions\";\r\nimport NoAccessMessage from \"../../rNoAccess\";\r\nimport { useSelector } from \"react-redux\";\r\n\r\nconst LandingPageIndex = (_props: any) => {\r\n  const { t } = useTranslation('common');\r\n  const ShowLandingPageIndex = () => {\r\n    return(\r\n      <Container style={{ overflowX: \"hidden\" }} fluid className=\"p-0\">\r\n        <Row>\r\n          <Col xs={12}>\r\n            <PageHeading title={t(\"adminsetting.landing.form.EditableContent\")} />\r\n          </Col>\r\n        </Row>\r\n        <Row className=\"mt-3\">\r\n          <Col xs={12}>\r\n            <LandingPageTable />\r\n          </Col>\r\n        </Row>\r\n    </Container>\r\n\r\n      \r\n    );\r\n  }\r\n  \r\n  const ShowcAddLandingPage = canAddLandingPage(() => <ShowLandingPageIndex />);\r\n  const state:any = useSelector((state) => state);\r\n  if (!(state?.permissions?.landing_page?.['create:any'])) {\r\n    return <NoAccessMessage />\r\n  }\r\n  return(    \r\n    <ShowcAddLandingPage />\r\n  );\r\n}\r\n\r\nexport default LandingPageIndex;", "//Import Library\r\nimport { useState, useEffect } from \"react\";\r\nimport Link from \"next/link\";\r\nimport { <PERSON><PERSON>, Button } from \"react-bootstrap\";\r\nimport toast from 'react-hot-toast';\r\n\r\n//Import services/components\r\nimport RKITable from \"../../../components/common/RKITable\";\r\nimport apiService from \"../../../services/apiService\";\r\nimport { useTranslation } from 'next-i18next';\r\n\r\nconst ExpertiseTable = (props: any) => {\r\n    const { t } = useTranslation('common');\r\n    const [tabledata, setDataToTable] = useState([]);\r\n    const [, setLoading] = useState(false);\r\n    const [totalRows, setTotalRows] = useState(0);\r\n    const [perPage, setPerPage] = useState(10);\r\n    const [isModalShow, setModal] = useState(false);\r\n    const [selectExpertise, setSelectExpertise] = useState({});\r\n\r\n\r\n    const expertiseParams = {\r\n        sort: { title: \"asc\" },\r\n        limit: perPage,\r\n        page: 1,\r\n        query: {},\r\n    };\r\n\r\n    const columns = [\r\n        {\r\n            name: t(\"adminsetting.Expertise.Table.Title\"),\r\n            selector: \"title\",\r\n        },\r\n        {\r\n            name: t(\"adminsetting.Expertise.Table.Action\"),\r\n            selector: \"\",\r\n            cell: (d: any) => (\r\n                <div>\r\n                    <Link href=\"/adminsettings/[...routes]\" as={`/adminsettings/edit_expertise/${d._id}`}>\r\n\r\n                        <i className=\"icon fas fa-edit\" />\r\n\r\n                    </Link>\r\n                    &nbsp;\r\n                    <a onClick={(e) => userAction(d, e)}>\r\n                        <i className=\"icon fas fa-trash-alt\" />\r\n                    </a>{\" \"}\r\n                </div>\r\n            ),\r\n        },\r\n    ];\r\n\r\n    const getExpertiseData = async () => {\r\n        setLoading(true);\r\n        const response = await apiService.get(\"/expertise\", expertiseParams);\r\n        if (response && response.data && response.data.length > 0) {\r\n            setDataToTable(response.data);\r\n            setTotalRows(response.totalCount);\r\n            setLoading(false);\r\n        }\r\n    };\r\n\r\n    const handlePageChange = (page: any) => {\r\n        expertiseParams.limit = perPage;\r\n        expertiseParams.page = page;\r\n        getExpertiseData();\r\n    };\r\n\r\n    const handlePerRowsChange = async (newPerPage: any, page: any) => {\r\n        expertiseParams.limit = newPerPage;\r\n        expertiseParams.page = page;\r\n        setLoading(true);\r\n        const response = await apiService.get(\"/expertise\", expertiseParams);\r\n        if (response && response.data && response.data.length > 0) {\r\n            setDataToTable(response.data);\r\n            setPerPage(newPerPage);\r\n            setLoading(false);\r\n        }\r\n    };\r\n\r\n    const userAction = async (row: any, e: any) => {\r\n        e.preventDefault();\r\n        setSelectExpertise(row._id);\r\n        setModal(true);\r\n    };\r\n\r\n    const modalConfirm = async () => {\r\n        try {\r\n            await apiService.remove(`/expertise/${selectExpertise}`);\r\n            getExpertiseData();\r\n            setModal(false);\r\n            toast.success(t(\"adminsetting.Expertise.Table.expertiseDeletedSuccessfully\"));\r\n        } catch (error) {\r\n            toast.error(t(\"adminsetting.Expertise.Table.errorDeletingExpertise\"));\r\n        }\r\n    };\r\n\r\n    const modalHide = () => setModal(false);\r\n\r\n    useEffect(() => {\r\n        getExpertiseData();\r\n    }, []);\r\n\r\n    return (\r\n        <div>\r\n            <Modal show={isModalShow} onHide={modalHide}>\r\n                <Modal.Header closeButton>\r\n                    <Modal.Title> {t(\"adminsetting.Expertise.Table.DeleteExpertise\")}</Modal.Title>\r\n                </Modal.Header>\r\n                <Modal.Body>{t(\"adminsetting.Expertise.Table.AreyousurewanttodeletethisExpertise?\")}</Modal.Body>\r\n                <Modal.Footer>\r\n                    <Button variant=\"secondary\" onClick={modalHide}>\r\n                        {t(\"adminsetting.Expertise.Table.Cancel\")}\r\n                    </Button>\r\n                    <Button variant=\"primary\" onClick={modalConfirm}>\r\n                        {t(\"adminsetting.Expertise.Table.Yes\")}\r\n                    </Button>\r\n                </Modal.Footer>\r\n            </Modal>\r\n\r\n            <RKITable\r\n                columns={columns}\r\n                data={tabledata}\r\n                totalRows={totalRows}\r\n                pagServer={true}\r\n                handlePerRowsChange={handlePerRowsChange}\r\n                handlePageChange={handlePageChange}\r\n            />\r\n        </div>\r\n    );\r\n};\r\n\r\nexport default ExpertiseTable;\r\n", "//Import Library\r\nimport Link from \"next/link\";\r\nimport { useState, useEffect } from \"react\";\r\nimport { <PERSON><PERSON>, Button } from \"react-bootstrap\";\r\n\r\nimport toast from 'react-hot-toast';\r\n\r\n//Import services/components\r\nimport RKITable from \"../../../components/common/RKITable\";\r\nimport apiService from \"../../../services/apiService\";\r\nimport { useTranslation } from 'next-i18next';\r\nconst SyndromeTable = (_props: any) => {\r\n    const { t } = useTranslation('common');\r\n    const [tabledata, setDataToTable] = useState([]);\r\n    const [, setLoading] = useState(false);\r\n    const [totalRows, setTotalRows] = useState(0);\r\n    const [perPage, setPerPage] = useState(10);\r\n    const [isModalShow, setModal] = useState(false);\r\n    const [selectSyndrome, setSelectSyndrome] = useState({});\r\n    \r\n    const columns = [\r\n        {\r\n            name: t(\"adminsetting.syndrome.Syndromes\"),\r\n            selector: \"title\",\r\n        },\r\n        {\r\n            name: t(\"adminsetting.syndrome.Code\"),\r\n            selector: \"code\",\r\n            cell: (d: any) => d.code,\r\n        },\r\n        {\r\n            name: t(\"adminsetting.syndrome.Description\"),\r\n            selector: \"description\",\r\n            cell: (d: any) => d.description.replace(/<[^>]+>/g, \"\"),\r\n        },\r\n        {\r\n            name: t(\"adminsetting.syndrome.Action\"),\r\n            selector: \"\",\r\n            cell: (d: any) => (\r\n                <div>\r\n                    <Link href=\"/adminsettings/[...routes]\" as={`/adminsettings/edit_syndrome/${d._id}`}>\r\n\r\n                        <i className=\"icon fas fa-edit\" />\r\n\r\n                    </Link>\r\n                    &nbsp;\r\n                    <a onClick={() => userAction(d)}>\r\n                        <i className=\"icon fas fa-trash-alt\" />\r\n                    </a>{\" \"}\r\n                </div>\r\n            ),\r\n        },\r\n    ];\r\n\r\n    useEffect(() => {\r\n        getSyndromeData();\r\n    }, []);\r\n\r\n    const syndromeParams = {\r\n        sort: { title: \"asc\" },\r\n        limit: perPage,\r\n        page: 1,\r\n        query: {},\r\n    };\r\n\r\n    const getSyndromeData = async () => {\r\n        setLoading(true);\r\n        const response = await apiService.get(\"/syndrome\", syndromeParams);\r\n        if (response && response.data && response.data.length > 0) {\r\n            setDataToTable(response.data);\r\n            setTotalRows(response.totalCount);\r\n            setLoading(false);\r\n        }\r\n    };\r\n\r\n    const handlePageChange = (page: any) => {\r\n        syndromeParams.limit = perPage;\r\n        syndromeParams.page = page;\r\n        getSyndromeData();\r\n    };\r\n\r\n    const handlePerRowsChange = async (newPerPage: any, page: any) => {\r\n        syndromeParams.limit = newPerPage;\r\n        syndromeParams.page = page;\r\n        setLoading(true);\r\n        const response = await apiService.get(\"/syndrome\", syndromeParams);\r\n        if (response && response.data && response.data.length > 0) {\r\n            setDataToTable(response.data);\r\n            setPerPage(newPerPage);\r\n            setLoading(false);\r\n        }\r\n    };\r\n\r\n    const userAction = async (row: any) => {\r\n        setSelectSyndrome(row._id);\r\n        setModal(true);\r\n    };\r\n\r\n    const modalConfirm = async () => {\r\n        try {\r\n            await apiService.remove(`/syndrome/${selectSyndrome}`);\r\n            getSyndromeData();\r\n            setModal(false);\r\n            toast.success(t(\"adminsetting.syndrome.Table.syndromeDeletedSuccessfully\"));\r\n        } catch (error) {\r\n            toast.error(t(\"adminsetting.syndrome.Table.errorDeletingSyndrome\"));\r\n        }\r\n    };\r\n\r\n    const modalHide = () => setModal(false);\r\n\r\n    return (\r\n        <div>\r\n            <Modal show={isModalShow} onHide={modalHide}>\r\n                <Modal.Header closeButton>\r\n                    <Modal.Title>{t(\"adminsetting.syndrome.Deletesyndrome\")}</Modal.Title>\r\n                </Modal.Header>\r\n                <Modal.Body>{t(\"adminsetting.syndrome.Areyousurewanttodeletethissyndrome?\")}</Modal.Body>\r\n                <Modal.Footer>\r\n                    <Button variant=\"secondary\" onClick={modalHide}>\r\n                        {t(\"adminsetting.syndrome.Cancel\")}\r\n                    </Button>\r\n                    <Button variant=\"primary\" onClick={modalConfirm}>\r\n                        {t(\"adminsetting.syndrome.Yes\")}\r\n                    </Button>\r\n                </Modal.Footer>\r\n            </Modal>\r\n\r\n            <RKITable\r\n                columns={columns}\r\n                data={tabledata}\r\n                totalRows={totalRows}\r\n                pagServer={true}\r\n                handlePerRowsChange={handlePerRowsChange}\r\n                handlePageChange={handlePageChange}\r\n            />\r\n        </div>\r\n    );\r\n};\r\n\r\nexport default SyndromeTable;\r\n", "//Import Library\r\nimport { Contain<PERSON>, <PERSON>, <PERSON>, Button } from \"react-bootstrap\";\r\nimport Link from \"next/link\";\r\n\r\n//Import services/components\r\nimport PageHeading from \"../../../components/common/PageHeading\";\r\nimport CategoryTable from \"./categoryTable\";\r\nimport { useTranslation } from 'next-i18next';\r\n\r\n\r\nconst CategoryIndex = (_props: any) => {\r\n  const { t } = useTranslation('common');\r\n\r\n  return (\r\n    <div>\r\n      <Container style={{ overflowX: \"hidden\" }} fluid className=\"p-0\">\r\n        <Row>\r\n          <Col xs={12}>\r\n            <PageHeading title=\"Categories\" />\r\n          </Col>\r\n        </Row>\r\n        <Row>\r\n          <Col xs={12}>\r\n            <Link\r\n              href=\"/adminsettings/[...routes]\"\r\n              as=\"/adminsettings/create_category\"\r\n              >\r\n              <Button variant=\"secondary\" size=\"sm\">\r\n              {t(\"Addcategory\")}\r\n            </Button>\r\n            </Link>\r\n          </Col>\r\n        </Row>\r\n        <Row className=\"mt-3\">\r\n          <Col xs={12}>\r\n            <CategoryTable />\r\n          </Col>\r\n        </Row>\r\n      </Container>\r\n    </div>\r\n  );\r\n}\r\nexport default CategoryIndex;", "//Import Library\r\nimport {Col, Container, FormControl, Row} from \"react-bootstrap\";\r\n\r\n//Import services/components\r\nimport { useTranslation } from 'next-i18next';\r\n\r\nconst CountryTableFilter = ({ filterText, onFilter,onClear }: any) => {\r\n  const { t } = useTranslation('common');\r\n  return (\r\n    <Container fluid className=\"p-0\">\r\n      <Row>\r\n        <Col md={4} className=\"p-0\">\r\n          <FormControl\r\n            type=\"text\"\r\n            className=\"searchInput\"\r\n            placeholder={t(\"adminsetting.Countries.Forms.Search\")}\r\n            aria-label=\"Search\"\r\n            value={filterText}\r\n            onChange={onFilter}\r\n          />\r\n        </Col>\r\n      </Row>\r\n    </Container>\r\n  )\r\n};\r\n\r\nexport default CountryTableFilter;", "//Import Library\r\nimport { Container, <PERSON>, <PERSON>, Button } from \"react-bootstrap\";\r\nimport Link from \"next/link\";\r\n\r\n//Import services/components\r\nimport PageHeading from \"../../../components/common/PageHeading\";\r\nimport RegionTable from \"./regionTable\";\r\nimport { useTranslation } from 'next-i18next';\r\nimport { serverSideTranslations } from 'next-i18next/serverSideTranslations';\r\nimport { canAddRegions } from \"../permissions\";\r\nimport { useSelector } from \"react-redux\";\r\nimport NoAccessMessage from \"../../rNoAccess\";\r\n\r\nconst RegionIndex = (_props: any) => {\r\n  const { t } = useTranslation('common');\r\n  const SowRegionIndex = () => {\r\n    return (\r\n      <Container style={{ overflow: \"hidden\" }} fluid className=\"p-0\">\r\n        <Row>\r\n          <Col xs={12}>\r\n            <PageHeading title={t(\"adminsetting.Regions.Regions\")} />\r\n          </Col>\r\n        </Row>\r\n        <Row>\r\n          <Col xs={12}>\r\n            <Link\r\n              href=\"/adminsettings/[...routes]\"\r\n              as=\"/adminsettings/create_region\"\r\n              >\r\n              <Button variant=\"secondary\" size=\"sm\">\r\n                {t(\"adminsetting.Regions.AddRegion\")}\r\n              </Button>\r\n            </Link>\r\n          </Col>\r\n        </Row>\r\n        <Row className=\"mt-3\">\r\n          <Col xs={12}>\r\n            <RegionTable />\r\n          </Col>\r\n        </Row>\r\n      </Container>\r\n    );\r\n  }\r\n\r\n  const ShowAddRegions = canAddRegions(() => <SowRegionIndex />);\r\n  const state:any = useSelector((state) => state);\r\n  if (!(state?.permissions?.region?.['create:any'])) {\r\n    return <NoAccessMessage />\r\n  }\r\n  return(\r\n    <ShowAddRegions />\r\n  )\r\n};\r\n\r\nexport async function getServerSideProps({ locale } : { locale: string }) {\r\n  return {\r\n    props: {\r\n      ...(await serverSideTranslations(locale, ['common'])),\r\n    },\r\n  }\r\n}\r\n\r\nexport default RegionIndex;\r\n", "//Import Library\r\nimport { useState, useEffect } from \"react\";\r\nimport Link from \"next/link\";\r\nimport { <PERSON><PERSON>, Button } from \"react-bootstrap\";\r\n\r\nimport toast from 'react-hot-toast';\r\n\r\n//Import services/components\r\nimport RKITable from \"../../../components/common/RKITable\";\r\nimport apiService from \"../../../services/apiService\";\r\nimport { useTranslation } from 'next-i18next';\r\n\r\nconst OperationstatusTable = (_props: any) => {\r\n    const [tabledata, setDataToTable] = useState([]);\r\n    const [, setLoading] = useState(false);\r\n    const [totalRows, setTotalRows] = useState(0);\r\n    const [perPage, setPerPage] = useState(10);\r\n    const [isModalShow, setModal] = useState(false);\r\n    const [selectOperationstatus, setSelectOperationstatus] = useState({});\r\n    const { t } = useTranslation('common');\r\n    \r\n    const operationstatusParams = {\r\n        sort: { title: \"asc\" },\r\n        limit: perPage,\r\n        page: 1,\r\n        query: {},\r\n    };\r\n\r\n    const columns = [\r\n        {\r\n            name: t(\"Title\"),\r\n            selector: \"title\",\r\n        },\r\n        {\r\n            name: t(\"action\"),\r\n            selector: \"\",\r\n            cell: (d: any) => (\r\n                <div>\r\n                    <Link href=\"/adminsettings/[...routes]\" as={`/adminsettings/edit_operationstatus/${d._id}`}>\r\n\r\n                        <i className=\"icon fas fa-edit\" />\r\n\r\n                    </Link>\r\n                    &nbsp;\r\n                    <a onClick={() => userAction(d)}>\r\n                        <i className=\"icon fas fa-trash-alt\" />\r\n                    </a>{\" \"}\r\n                </div>\r\n            ),\r\n        },\r\n    ];\r\n\r\n    const getoperationstatusData = async () => {\r\n        setLoading(true);\r\n        const response = await apiService.get(\"/operation_status\", operationstatusParams);\r\n        if (response && response.data && response.data.length > 0) {\r\n            setDataToTable(response.data);\r\n            setTotalRows(response.totalCount);\r\n            setLoading(false);\r\n        }\r\n    };\r\n\r\n    const handlePageChange = (page: any) => {\r\n        operationstatusParams.limit = perPage;\r\n        operationstatusParams.page = page;\r\n        getoperationstatusData();\r\n    };\r\n\r\n    const handlePerRowsChange = async (newPerPage: any, page: any) => {\r\n        operationstatusParams.limit = newPerPage;\r\n        operationstatusParams.page = page;\r\n        setLoading(true);\r\n        const response = await apiService.get(\"/operation_status\", operationstatusParams);\r\n        if (response && response.data && response.data.length > 0) {\r\n            setDataToTable(response.data);\r\n            setPerPage(newPerPage);\r\n            setLoading(false);\r\n        }\r\n    };\r\n\r\n    const userAction = async (row: any) => {\r\n        setSelectOperationstatus(row._id);\r\n        setModal(true);\r\n    };\r\n\r\n    const modalConfirm = async () => {\r\n        try {\r\n            await apiService.remove(`/operation_status/${selectOperationstatus}`);\r\n            getoperationstatusData();\r\n            setModal(false);\r\n            toast.success(t(\"adminsetting.OperationStatus.Table.opStatusDeletedSuccessfully\"));\r\n        } catch (error) {\r\n            toast.error(t(\"adminsetting.OperationStatus.Table.errorDeletingOpStatus\"));\r\n        }\r\n    };\r\n\r\n    const modalHide = () => setModal(false);\r\n\r\n    useEffect(() => {\r\n        getoperationstatusData();\r\n    }, []);\r\n\r\n    return (\r\n        <div>\r\n            <Modal show={isModalShow} onHide={modalHide}>\r\n                <Modal.Header closeButton>\r\n                    <Modal.Title>{t(\"adminsetting.OperationStatus.Delete\")}</Modal.Title>\r\n                </Modal.Header>\r\n                <Modal.Body>{t(\"adminsetting.OperationStatus.sure\")}</Modal.Body>\r\n                <Modal.Footer>\r\n                    <Button variant=\"secondary\" onClick={modalHide}>\r\n                        {t(\"Cancel\")}\r\n                    </Button>\r\n                    <Button variant=\"primary\" onClick={modalConfirm}>\r\n                        {t(\"yes\")}\r\n                    </Button>\r\n                </Modal.Footer>\r\n            </Modal>\r\n\r\n            <RKITable\r\n                columns={columns}\r\n                data={tabledata}\r\n                totalRows={totalRows}\r\n                pagServer={true}\r\n                handlePerRowsChange={handlePerRowsChange}\r\n                handlePageChange={handlePageChange}\r\n            />\r\n        </div>\r\n    );\r\n};\r\n\r\nexport default OperationstatusTable;\r\n", "//Import Library\r\nimport { useState, useRef, useEffect } from \"react\";\r\nimport { Container, Card, Row, Col, Form, Button } from \"react-bootstrap\";\r\nimport { TextInput } from \"../../../components/common/FormValidation\";\r\nimport ValidationFormWrapper from \"../../../components/common/ValidationFormWrapper\";\r\nimport Router from \"next/router\";\r\nimport toast from 'react-hot-toast';\r\nimport Link from \"next/link\";\r\n\r\n//Import services/components\r\nimport { InstitutionNetwork } from \"../../../types\";\r\nimport apiService from \"../../../services/apiService\";\r\nimport { useTranslation } from 'next-i18next';\r\n\r\ninterface InstitutionNetworkFormProps {\r\n    [key: string]: any;\r\n}\r\n\r\nconst InstitutionNetworkForm = (props: InstitutionNetworkFormProps) => {\r\n    const _initialinstitutionNetowrk = {\r\n        title: \"\",\r\n    };\r\n\r\n    const [initialVal, setInitialVal] = useState<InstitutionNetwork>(_initialinstitutionNetowrk);\r\n\r\n    const editform = props.routes && props.routes[0] === \"edit_institution_network\" && props.routes[1];\r\n    const { t } = useTranslation('common');\r\n\r\n    const formRef = useRef(null);\r\n\r\n    const resetHandler = () => {\r\n        setInitialVal(_initialinstitutionNetowrk);\r\n        // Reset validation state (Formik handles this automatically)\r\n        window.scrollTo(0, 0);\r\n    };\r\n\r\n    const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement>) => {\r\n        if (e.target) {\r\n            const { name, value } = e.target;\r\n            setInitialVal((prevState) => ({\r\n                ...prevState,\r\n                [name]: value,\r\n            }));\r\n        }\r\n    };\r\n\r\n    const handleSubmit = async (event: any) => {\r\n        event.preventDefault();\r\n        const obj = {\r\n            title: initialVal.title.trim(),\r\n        };\r\n\r\n        let response;\r\n        let toastMsg;\r\n        if (editform) {\r\n            toastMsg = \"adminsetting.Organisationnetworks.updatesuccess\";\r\n            response = await apiService.patch(`/institutionnetwork/${props.routes[1]}`, obj);\r\n        } else {\r\n            toastMsg = \"adminsetting.Organisationnetworks.success\";\r\n            response = await apiService.post(\"/institutionnetwork\", obj);\r\n        }\r\n        if (response && response._id) {\r\n            toast.success(t(toastMsg));\r\n            Router.push(\"/adminsettings/institution_network\");\r\n        } else {\r\n            if (response?.errorCode === 11000) {\r\n                toast.error(t(\"duplicatesNotAllowed\"));\r\n            } else {\r\n                toast.error(response);\r\n            }\r\n        }\r\n    };\r\n\r\n    useEffect(() => {\r\n        const institutionNetworkParams = {\r\n            query: {},\r\n            sort: { title: \"asc\" },\r\n            limit: \"~\",\r\n        };\r\n        if (editform) {\r\n            const getInstitutionNetworkData = async () => {\r\n                const response: InstitutionNetwork = await apiService.get(\r\n                    `/institutionnetwork/${props.routes[1]}`,\r\n                    institutionNetworkParams\r\n                );\r\n                setInitialVal((prevState) => ({ ...prevState, ...response }));\r\n            };\r\n            getInstitutionNetworkData();\r\n        }\r\n    }, []);\r\n\r\n    return (\r\n        <div>\r\n            <Container className=\"formCard\" fluid>\r\n                <Card\r\n                    style={{\r\n                        marginTop: \"5px\",\r\n                        boxShadow: \"0 4px 8px 0 rgba(0, 0, 0, 0.2), 0 6px 20px 0 rgba(0, 0, 0, 0.19)\",\r\n                    }}\r\n                >\r\n                    <ValidationFormWrapper onSubmit={handleSubmit} ref={formRef} initialValues={initialVal} enableReinitialize={true}>\r\n                        <Card.Body>\r\n                            <Row>\r\n                                <Col>\r\n                                    <Card.Title>\r\n                                        {t(\"adminsetting.Organisationnetworks.OrganisationNetworks\")}\r\n                                    </Card.Title>\r\n                                </Col>\r\n                            </Row>\r\n                            <hr />\r\n                            <Row>\r\n                                <Col md lg={6} sm={12}>\r\n                                    <Form.Group>\r\n                                        <Form.Label className=\"required-field\">\r\n                                            {t(\"adminsetting.Organisationnetworks.OrganisationNetworks\")}\r\n                                        </Form.Label>\r\n                                        <TextInput\r\n                                            name=\"title\"\r\n                                            id=\"title\"\r\n                                            required\r\n                                            value={initialVal.title}\r\n                                            validator={(value: string) => value.trim() !== \"\"}\r\n                                            errorMessage={{\r\n                                                validator: t(\"adminsetting.Organisationnetworks.Add\"),\r\n                                            }}\r\n                                            onChange={handleChange}\r\n                                        />\r\n                                    </Form.Group>\r\n                                </Col>\r\n                            </Row>\r\n                            <Row className=\"my-4\">\r\n                                <Col>\r\n                                    <Button className=\"me-2\" type=\"submit\" variant=\"primary\">\r\n                                        {t(\"submit\")}\r\n                                    </Button>\r\n                                    <Button className=\"me-2\" onClick={resetHandler} variant=\"info\">\r\n                                        {t(\"reset\")}\r\n                                    </Button>\r\n                                    <Link\r\n                                        href=\"/adminsettings/[...routes]\"\r\n                                        as={`/adminsettings/institution_network`}\r\n                                        >\r\n                                        <Button variant=\"secondary\">{t(\"Cancel\")}</Button>\r\n                                    </Link>\r\n                                </Col>\r\n                            </Row>\r\n                        </Card.Body>\r\n                    </ValidationFormWrapper>\r\n                </Card>\r\n            </Container>\r\n        </div>\r\n    );\r\n};\r\nexport default InstitutionNetworkForm;\r\n", "//Import Library\r\nimport { Col, Container, Row } from \"react-bootstrap\";\r\nimport Select from 'react-select';\r\nimport React, { useEffect, useState } from \"react\";\r\n\r\n//Import services/components\r\nimport apiService from \"../../../services/apiService\";\r\nimport { useTranslation } from 'next-i18next';\r\n\r\n\r\nconst RegionTableFilter = ({ countryHandler, value }: any) => {\r\n  const { t,i18n } = useTranslation('common');\r\n  const titleSearch = i18n.language === 'de'? {title_de: \"asc\"} : {title: \"asc\"};\r\n  const currentLang = i18n.language;\r\n  const [countries, setCountreis] = useState<any[]>([]);\r\n  const countryParams = {\r\n    sort: titleSearch,\r\n    limit: \"~\",\r\n    languageCode:currentLang\r\n  };\r\n\r\n  useEffect(() => {\r\n    const fetchCountries = async () => {\r\n      const response = await apiService.get(\"/country\", countryParams);\r\n      if (response && response.data && response.data.length > 0) {\r\n        setCountreis(response.data);\r\n      }\r\n    }\r\n    fetchCountries();\r\n  }, [])\r\n  return (\r\n    <Container fluid>\r\n      <Row>\r\n        <Col md={4} className=\"ps-1\">\r\n          <Select\r\n            value={[value]}\r\n            placeholder={t(\"adminsetting.Regions.SelectCountry\")}\r\n            isClearable={true}\r\n            onChange={countryHandler}\r\n            options={countries.length > 0 ? countries.map((item, _i) => ({ value: item._id, label: item.title })) : []}\r\n          />\r\n        </Col>\r\n      </Row>\r\n    </Container>\r\n  )\r\n};\r\n\r\nexport default RegionTableFilter;", "//Import Library\r\nimport { useState, useEffect } from \"react\";\r\nimport Link from \"next/link\";\r\nimport { <PERSON><PERSON>, Button } from \"react-bootstrap\";\r\nimport toast from 'react-hot-toast';\r\n\r\n//Import services/components\r\nimport RKITable from \"../../../components/common/RKITable\";\r\nimport apiService from \"../../../services/apiService\";\r\nimport { useTranslation } from 'next-i18next';\r\n\r\nconst AreaOfWorkTable = (_props: any) => {\r\n    const { t } = useTranslation('common');\r\n    const [tabledata, setDataToTable] = useState([]);\r\n    const [, setLoading] = useState(false);\r\n    const [totalRows, setTotalRows] = useState(0);\r\n    const [perPage, setPerPage] = useState(10);\r\n    const [isModalShow, setModal] = useState(false);\r\n    const [AreaOfWorkCountry, setAreaOfWorkCountry] = useState({});\r\n\r\n\r\n    const areaOfWorkParams = {\r\n        sort: { title: \"asc\" },\r\n        limit: perPage,\r\n        page: 1,\r\n        query: {},\r\n    };\r\n\r\n    const columns = [\r\n        {\r\n            name: t(\"adminsetting.areaofwork.Table.Title\"),\r\n            selector: \"title\",\r\n        },\r\n        {\r\n            name: t(\"adminsetting.areaofwork.Table.Action\"),\r\n            selector: \"\",\r\n            cell: (d: any) => (\r\n                <div>\r\n                    <Link href=\"/adminsettings/[...routes]\" as={`/adminsettings/edit_area_of_work/${d._id}`}>\r\n\r\n                        {\" \"}\r\n                        <i className=\"icon fas fa-edit\" />\r\n\r\n                    </Link>\r\n                    &nbsp;\r\n                    <a onClick={() => userAction(d)}>\r\n                        <i className=\"icon fas fa-trash-alt\" />\r\n                    </a>{\" \"}\r\n                </div>\r\n            ),\r\n        },\r\n    ];\r\n\r\n    const getAredOfWorkData = async (areaOfWorkParamsinitials: any) => {\r\n        setLoading(true);\r\n        const response = await apiService.get(\"/areaofwork\", areaOfWorkParamsinitials);\r\n        if (response && response.data && response.data.length > 0) {\r\n            setDataToTable(response.data);\r\n            setTotalRows(response.totalCount);\r\n            setLoading(false);\r\n        }\r\n    };\r\n\r\n    const handlePageChange = (page: any) => {\r\n        areaOfWorkParams.limit = perPage;\r\n        areaOfWorkParams.page = page;\r\n        getAredOfWorkData(areaOfWorkParams);\r\n    };\r\n\r\n    const handlePerRowsChange = async (newPerPage: any, page: any) => {\r\n        areaOfWorkParams.limit = newPerPage;\r\n        areaOfWorkParams.page = page;\r\n        setLoading(true);\r\n        const response = await apiService.get(\"/areaofwork\", areaOfWorkParams);\r\n        if (response && response.data && response.data.length > 0) {\r\n            setDataToTable(response.data);\r\n            setPerPage(newPerPage);\r\n            setLoading(false);\r\n        }\r\n    };\r\n\r\n    const userAction = async (row: any) => {\r\n        setAreaOfWorkCountry(row._id);\r\n        setModal(true);\r\n    };\r\n\r\n    const modalConfirm = async () => {\r\n        try {\r\n            await apiService.remove(`/areaofwork/${AreaOfWorkCountry}`);\r\n            getAredOfWorkData(areaOfWorkParams);\r\n            setModal(false);\r\n            toast.success(t(\"adminsetting.areaofwork.Table.areaOfWorkDeletedSuccessfully\"));\r\n        } catch (error) {\r\n            toast.error(t(\"adminsetting.areaofwork.Table.errorDeletingAreaOfWork\"));\r\n        }\r\n    };\r\n\r\n    const modalHide = () => setModal(false);\r\n\r\n    useEffect(() => {\r\n        getAredOfWorkData(areaOfWorkParams);\r\n    }, []);\r\n\r\n    return (\r\n        <div>\r\n            <Modal show={isModalShow} onHide={modalHide}>\r\n                <Modal.Header closeButton>\r\n                    <Modal.Title>{t(\"adminsetting.areaofwork.Table.DeleteAreaofwork\")}</Modal.Title>\r\n                </Modal.Header>\r\n                <Modal.Body>{t(\"adminsetting.areaofwork.Table.Areyousurewanttodeletethisareaofwork?\")}</Modal.Body>\r\n                <Modal.Footer>\r\n                    <Button variant=\"secondary\" onClick={modalHide}>\r\n                        {t(\"adminsetting.areaofwork.Table.Cancel\")}\r\n                    </Button>\r\n                    <Button variant=\"primary\" onClick={modalConfirm}>\r\n                        {t(\"adminsetting.areaofwork.Table.Yes\")}\r\n                    </Button>\r\n                </Modal.Footer>\r\n            </Modal>\r\n\r\n            <RKITable\r\n                columns={columns}\r\n                data={tabledata}\r\n                totalRows={totalRows}\r\n                pagServer={true}\r\n                handlePerRowsChange={handlePerRowsChange}\r\n                handlePageChange={handlePageChange}\r\n            />\r\n        </div>\r\n    );\r\n};\r\n\r\nexport default AreaOfWorkTable;\r\n", "//Import Library\r\nimport React, { useEffect, useState } from \"react\";\r\nimport { <PERSON><PERSON>, <PERSON><PERSON> } from \"react-bootstrap\";\r\nimport toast from 'react-hot-toast';\r\n\r\n//Import services/components\r\nimport RKITable from \"../../../components/common/RKITable\";\r\nimport apiService from \"../../../services/apiService\";\r\nimport { useTranslation } from 'next-i18next';\r\n\r\ninterface UserData {\r\n    _id: string;\r\n    username: string;\r\n    email: string;\r\n    institutionInvites: Array<{\r\n        institutionId: string;\r\n        institutionName: string;\r\n        status: string;\r\n    }>;\r\n    [key: string]: any;\r\n}\r\n\r\nfunction AdminTable(_props: any) {\r\n    const { t } = useTranslation('common');\r\n    const [tabledata, setDataToTable] = useState<any[]>([]);\r\n    const [, setLoading] = useState<boolean>(false);\r\n    const [totalRows, setTotalRows] = useState<number>(0);\r\n    const [perPage, setPerPage] = useState<number>(10);\r\n    const [isModalShow, setModal] = useState<boolean>(false);\r\n    const [newStatus, setNewStatus] = useState<string>(\"\");\r\n    const [selectUserDetails, setSelectUserDetails] = useState<any>({});\r\n\r\n\r\n    const usersParams = {\r\n        sort: { created_at: \"desc\" },\r\n        limit: \"~\",\r\n        page: 1,\r\n        query: { \"institutionInvites.status\": \"Request Pending\" },\r\n    };\r\n\r\n    const columns = [\r\n        {\r\n            name: t(\"adminsetting.FocalPointsApprovalTable.Username\"),\r\n            selector: \"username\",\r\n            cell: (d: any) => d.username,\r\n        },\r\n        {\r\n            name: t(\"OrganisationName\"),\r\n            selector: \"institutionName\",\r\n            cell: (d: any) => d.institutionName,\r\n        },\r\n        {\r\n            name: t(\"adminsetting.FocalPointsApprovalTable.Email\"),\r\n            selector: \"email\",\r\n            cell: (d: any) => d.email,\r\n        },\r\n        {\r\n            name: t(\"adminsetting.FocalPointsApprovalTable.Action\"),\r\n            selector: \"\",\r\n            cell: (d: any) => (\r\n                <div>\r\n                    {d.institutionStatus === \"Rejected\" || d.institutionStatus === \"Request Pending\" ? (\r\n                        <>\r\n                            <Button variant=\"primary\" size=\"sm\" onClick={() => userAction(d, \"approve\")}>\r\n                                {t(\"instu.Approves\")}\r\n                            </Button>\r\n                            &nbsp;\r\n                        </>\r\n                    ) : (\r\n                        <></>\r\n                    )}\r\n                    {d.institutionStatus === \"Approved\" || d.institutionStatus === \"Request Pending\" ? (\r\n                        <Button variant=\"secondary\" size=\"sm\" onClick={() => userAction(d, \"reject\")}>\r\n                            {t(\"instu.Reject\")}\r\n                        </Button>\r\n                    ) : (\r\n                        <></>\r\n                    )}\r\n                </div>\r\n            ),\r\n        },\r\n    ];\r\n\r\n    const getTableData = (data: UserData[]) => {\r\n        let tableData: any[] = [];\r\n        data.forEach((user) => {\r\n            user?.institutionInvites.forEach((institutionInvite) => {\r\n                if (institutionInvite && institutionInvite.status === \"Request Pending\") {\r\n                    tableData.push({\r\n                        ...user,\r\n                        ...{\r\n                            institutionId: institutionInvite.institutionId,\r\n                            institutionName: institutionInvite.institutionName,\r\n                            institutionStatus: institutionInvite.status,\r\n                        },\r\n                    });\r\n                }\r\n            });\r\n        });\r\n        return tableData;\r\n    };\r\n\r\n    const getUsersData = async () => {\r\n        setLoading(true);\r\n        const response = await apiService.get(\"/users\", usersParams);\r\n        if (response && response.data) {\r\n            let tableData = getTableData(response.data);\r\n            setDataToTable(localPaginate(tableData, perPage, 1));\r\n            setTotalRows(tableData.length);\r\n            setLoading(false);\r\n        }\r\n    };\r\n    const handlePageChange = async (page: number) => {\r\n        setLoading(true);\r\n        const response = await apiService.get(\"/users\", usersParams);\r\n        if (response && response.data && response.data.length > 0) {\r\n            let tableData = getTableData(response.data);\r\n            setDataToTable(localPaginate(tableData, perPage, page));\r\n            setLoading(false);\r\n        }\r\n    };\r\n\r\n    const handlePerRowsChange = async (newPerPage: number, page: number) => {\r\n        setLoading(true);\r\n        const response = await apiService.get(\"/users\", usersParams);\r\n        if (response && response.data && response.data.length > 0) {\r\n            let tableData = getTableData(response.data);\r\n            setDataToTable(localPaginate(tableData, newPerPage, page));\r\n            setPerPage(newPerPage);\r\n            setLoading(false);\r\n        }\r\n    };\r\n\r\n    const localPaginate = (array: any[], page_size: number, page_number: number) => {\r\n        return array.slice((page_number - 1) * page_size, page_number * page_size);\r\n    };\r\n\r\n    useEffect(() => {\r\n        getUsersData();\r\n    }, []);\r\n\r\n    const userAction = async (d: any, status: string) => {\r\n        console.log(d, status);\r\n        setModal(true);\r\n        setNewStatus(status);\r\n        if (d && d._id) {\r\n            const setStatus = status === \"approve\" ? \"Approved\" : \"Rejected\";\r\n            setSelectUserDetails({ ...d, status: setStatus });\r\n        }\r\n    };\r\n\r\n    const modalConfirm = async () => {\r\n        selectUserDetails[\"is_focal_point\"] = true;\r\n        selectUserDetails[\"status\"] = \"Approved\";\r\n        if (\r\n            selectUserDetails &&\r\n            selectUserDetails[\"institutionInvites\"] &&\r\n            selectUserDetails[\"institutionInvites\"].length\r\n        ) {\r\n            selectUserDetails[\"institutionInvites\"].map((invite: any) => {\r\n                if (invite.institutionId === selectUserDetails[\"institutionId\"]) {\r\n                    invite.status = newStatus === \"approve\" ? \"Approved\" : \"Rejected\";\r\n                }\r\n                return invite;\r\n            });\r\n        }\r\n        let updatedData;\r\n        if(newStatus !== \"approve\") {\r\n            await apiService.remove(`/users/${selectUserDetails[\"_id\"]}`);\r\n        } else {\r\n            updatedData = await apiService.patch(`/users/${selectUserDetails[\"_id\"]}`, selectUserDetails);\r\n        }\r\n        if (updatedData && updatedData.status === 403) {\r\n            toast.error(\r\n                updatedData.response && updatedData.response.message\r\n                    ? updatedData.response.message\r\n                    : t(\"adminsetting.FocalPointsApprovalTable.Somethingwentswrong\")\r\n            );\r\n            return;\r\n        } else {\r\n            getUsersData();\r\n            if (newStatus === \"approve\") {\r\n                toast.success(t(\"adminsetting.FocalPointsApprovalTable.Approvemm\"));\r\n            } else {\r\n                toast.error(t(\"adminsetting.FocalPointsApprovalTable.Rejected\"));\r\n            }\r\n            setSelectUserDetails({});\r\n            setModal(false);\r\n        }\r\n    };\r\n\r\n    const modalHide = () => setModal(false);\r\n\r\n    return (\r\n        <div>\r\n            <Modal show={isModalShow} onHide={modalHide}>\r\n                <Modal.Header closeButton>\r\n                    <Modal.Title>\r\n                        {newStatus.charAt(0).toUpperCase() + newStatus.slice(1)}{\" \"}\r\n                        {t(\"adminsetting.FocalPointsApprovalTable.User\")}\r\n                    </Modal.Title>\r\n                </Modal.Header>\r\n                <Modal.Body>\r\n                    {t(\"adminsetting.FocalPointsApprovalTable.Areyousurewantto\")} {newStatus}{\" \"}\r\n                    {t(\"adminsetting.FocalPointsApprovalTable.thisuser?\")}\r\n                </Modal.Body>\r\n                <Modal.Footer>\r\n                    <Button variant=\"secondary\" onClick={modalHide}>\r\n                        {t(\"adminsetting.FocalPointsApprovalTable.Cancel\")}\r\n                    </Button>\r\n                    <Button variant=\"primary\" onClick={modalConfirm}>\r\n                        {t(\"adminsetting.FocalPointsApprovalTable.Yes\")}\r\n                    </Button>\r\n                </Modal.Footer>\r\n            </Modal>\r\n\r\n            <RKITable\r\n                columns={columns}\r\n                data={tabledata}\r\n                totalRows={totalRows}\r\n                pagServer={true}\r\n                handlePerRowsChange={handlePerRowsChange}\r\n                handlePageChange={handlePageChange}\r\n            />\r\n        </div>\r\n    );\r\n}\r\n\r\nexport default AdminTable;\r\n", "//Import Library\r\nimport Link from \"next/link\";\r\nimport { useState, useEffect } from \"react\";\r\nimport { <PERSON><PERSON>, But<PERSON> } from \"react-bootstrap\";\r\n\r\nimport toast from 'react-hot-toast';\r\n\r\n//Import services/components\r\nimport RKITable from \"../../../components/common/RKITable\";\r\nimport apiService from \"../../../services/apiService\";\r\nimport { useTranslation } from 'next-i18next';\r\n\r\nconst LandingPageTable = (_props: any) => {\r\n    const { t, i18n } = useTranslation('common');\r\n    const currentLang = i18n.language ? i18n.language : \"en\";\r\n\r\n    const [tabledata, setDataToTable] = useState([]);\r\n    const [, setLoading] = useState(false);\r\n    const [totalRows, setTotalRows] = useState(0);\r\n    const [perPage, setPerPage] = useState(10);\r\n    const [isModalShow, setModal] = useState(false);\r\n    const [selectLandingPage, setSelectLandingPage] = useState({});\r\n    const modalHide = () => setModal(false);\r\n    \r\n    const columns = [\r\n        {\r\n            name: t(\"adminsetting.landing.table.Title\"),\r\n            selector: \"title\",\r\n            cell: (d: any) => d.title,\r\n        },\r\n        {\r\n            name: t(\"adminsetting.landing.table.Enabled\"),\r\n            selector: \"isEnabled\",\r\n            cell: (d: any) => (d.isEnabled ? \"Yes\" : \"No\"),\r\n        },\r\n        {\r\n            name: t(\"adminsetting.landing.table.Action\"),\r\n            selector: \"\",\r\n            cell: (d: any) => (\r\n                <div>\r\n                    <Link href=\"/adminsettings/[...routes]\" as={`/adminsettings/edit_landing/${d._id}`}>\r\n\r\n                        <i className=\"icon fas fa-edit\" />\r\n\r\n                    </Link>\r\n                    &nbsp;\r\n                    {\" \"}\r\n                </div>\r\n            ),\r\n        },\r\n    ];\r\n    const landingPageParams = {\r\n        sort: { title: \"asc\" },\r\n        limit: perPage,\r\n        page: 1,\r\n    };\r\n\r\n    useEffect(() => {\r\n        getlandingPageData();\r\n    }, []);\r\n\r\n    const getlandingPageData = async () => {\r\n        setLoading(true);\r\n        const response = await apiService.get(\"/landingPage\", landingPageParams);\r\n        if (response) {\r\n            setDataToTable(response.data);\r\n            setTotalRows(response.totalCount);\r\n            setLoading(false);\r\n        }\r\n    };\r\n\r\n    const handlePageChange = (page: any) => {\r\n        landingPageParams.limit = perPage;\r\n        landingPageParams.page = page;\r\n        getlandingPageData();\r\n    };\r\n\r\n    const handlePerRowsChange = async (newPerPage: any, page: any) => {\r\n        landingPageParams.limit = newPerPage;\r\n        landingPageParams.page = page;\r\n        setLoading(true);\r\n        const response = await apiService.get(\"/landingPage\", landingPageParams);\r\n        if (response && response.data && response.data.length > 0) {\r\n            setDataToTable(response.data);\r\n            setPerPage(newPerPage);\r\n            setLoading(false);\r\n        }\r\n    };\r\n\r\n    const modalConfirm = async () => {\r\n        try {\r\n            await apiService.remove(`/landingPage/${selectLandingPage}`);\r\n            getlandingPageData();\r\n            setModal(false);\r\n            toast.success(t(\"adminsetting.landing.table.landingDeletedSuccessfully\"));\r\n        } catch (error) {\r\n            toast.error(t(\"adminsetting.landing.table.errorDeletingLanding\"));\r\n        }\r\n    };\r\n\r\n    const userAction = async (row: any) => {\r\n        setSelectLandingPage(row._id);\r\n        setModal(true);\r\n    };\r\n\r\n    return (\r\n        <div>\r\n            <Modal show={isModalShow} onHide={modalHide}>\r\n                <Modal.Header closeButton>\r\n                    <Modal.Title>{t(\"adminsetting.landing.table.DeleteEditableContent\")}</Modal.Title>\r\n                </Modal.Header>\r\n                <Modal.Body>{t(\"adminsetting.landing.table.AreyousurewanttodeletethisEditableContent?\")}</Modal.Body>\r\n                <Modal.Footer>\r\n                    <Button variant=\"secondary\" onClick={modalHide}>\r\n                        {t(\"adminsetting.landing.table.Cancel\")}\r\n                    </Button>\r\n                    <Button variant=\"primary\" onClick={modalConfirm}>\r\n                        {t(\"adminsetting.landing.table.Yes\")}\r\n                    </Button>\r\n                </Modal.Footer>\r\n            </Modal>\r\n\r\n            <RKITable\r\n                columns={columns}\r\n                data={tabledata}\r\n                totalRows={totalRows}\r\n                pagServer={true}\r\n                handlePerRowsChange={handlePerRowsChange}\r\n                handlePageChange={handlePageChange}\r\n            />\r\n        </div>\r\n    );\r\n};\r\n\r\nexport default LandingPageTable;\r\n", "//Import Library\r\nimport { useState, useRef, useEffect } from \"react\";\r\nimport { Container, Card, Row, Col, Form, Button } from \"react-bootstrap\";\r\nimport ValidationFormWrapper from \"../../../components/common/ValidationFormWrapper\";\r\nimport { TextInput } from \"../../../components/common/FormValidation\";\r\nimport Router from \"next/router\";\r\nimport toast from 'react-hot-toast';\r\nimport Link from 'next/link';\r\n\r\n//Import services/components\r\nimport { Role } from \"../../../types\";\r\nimport { useTranslation } from 'next-i18next';\r\nimport apiService from \"../../../services/apiService\";\r\n\r\ninterface RoleFormProps {\r\n    [key: string]: any;\r\n}\r\n\r\nconst RoleForm = (props: RoleFormProps) => {\r\n\r\n  const _initialrole = {\r\n    title: '',\r\n  }\r\n\r\n  const [initialVal, setInitialVal] = useState<Role>(_initialrole);\r\n    const editform = props.routes && props.routes[0] === \"edit_role\" && props.routes[1];\r\n  const { t } = useTranslation('common');\r\n\r\n\r\n  const formRef = useRef(null);\r\n\r\n  const resetHandler = () => {\r\n    setInitialVal(_initialrole);\r\n    // Reset validation state (Formik handles this automatically)\r\n    window.scrollTo(0, 0);\r\n  };\r\n\r\n  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement>) => {\r\n    if (e.target) {\r\n      const { name, value } = e.target;\r\n      setInitialVal(prevState => ({\r\n        ...prevState,\r\n        [name]: value\r\n      }));\r\n    }\r\n  }\r\n\r\n  const handleSubmit = async (event: any, values?: any) => {\r\n    if (event) event.preventDefault();\r\n    // Use Formik values if available, otherwise fall back to initialVal\r\n    const formValues = values || initialVal;\r\n    const obj = {\r\n      title: formValues.title.trim(),\r\n    };\r\n\r\n    let response;\r\n    if (editform) {\r\n      response = await apiService.patch(`/roles/${props.routes[1]}`, obj);\r\n    } else {\r\n      response = await apiService.post(\"/roles\", obj);\r\n    }\r\n    if (response && response._id) {\r\n      toast.success(t(\"Roleisaddedsuccessfully\"));\r\n      Router.push(\"/adminsettings/role\");\r\n    } else {\r\n      toast.error(response)\r\n    }\r\n  }\r\n\r\n  useEffect(() => {\r\n    const roleParams = {\r\n      query: {},\r\n      sort: { title: \"asc\" },\r\n      limit: \"~\",\r\n    };\r\n    if (editform) {\r\n      const getRoleData = async () => {\r\n        const response: Role = await apiService.get(`/roles/${props.routes[1]}`, roleParams);\r\n        setInitialVal((prevState) => ({ ...prevState, ...response }));\r\n      }\r\n      getRoleData();\r\n    }\r\n  }, []);\r\n\r\n  return (\r\n    <div>\r\n      <Container className=\"formCard\" fluid>\r\n        <Card style={{ marginTop: \"5px\", boxShadow: \"0 4px 8px 0 rgba(0, 0, 0, 0.2), 0 6px 20px 0 rgba(0, 0, 0, 0.19)\" }}>\r\n          <ValidationFormWrapper onSubmit={handleSubmit} ref={formRef} initialValues={initialVal} enableReinitialize={true}>\r\n            <Card.Body>\r\n              <Row>\r\n                <Col>\r\n                  <Card.Title>{t(\"Role\")}</Card.Title>\r\n                </Col>\r\n              </Row>\r\n              <hr />\r\n              <Row>\r\n                <Col md lg={6} sm={12}>\r\n                  <Form.Group>\r\n                    <Form.Label className=\"required-field\">{t(\"Role\")}</Form.Label>\r\n                    <TextInput\r\n                      name=\"title\"\r\n                      id=\"title\"\r\n                      required value={initialVal.title}\r\n                      validator={((value: string) => String(value || '').trim() !== \"\")}\r\n                      errorMessage={{\r\n                        validator: t(\"PleaseAddtheRole\")}}\r\n                      onChange={handleChange}\r\n                    />\r\n                  </Form.Group>\r\n                </Col>\r\n              </Row>\r\n              <Row className=\"my-4\">\r\n                <Col>\r\n                  <Button className=\"me-2\" type=\"submit\" variant=\"primary\">{t(\"submit\")}</Button>\r\n                  <Button className=\"me-2\" onClick={resetHandler} variant=\"info\">{t(\"reset\")}</Button>\r\n                  <Link\r\n                    href=\"/adminsettings/[...routes]\"\r\n                    as={`/adminsettings/role`}\r\n                    ><Button variant=\"secondary\">{t(\"cancel\")}</Button></Link>\r\n                </Col>\r\n              </Row>\r\n            </Card.Body>\r\n          </ValidationFormWrapper>\r\n        </Card>\r\n      </Container>\r\n    </div>\r\n  );\r\n}\r\nexport default RoleForm;\r\n"], "names": ["types", "_id", "title", "filterText", "ContentTableFilter", "onFilter", "onFilterTypeChange", "onClear", "filterType", "t", "useTranslation", "Container", "fluid", "className", "Row", "Col", "xs", "md", "FormGroup", "FormControl", "type", "placeholder", "aria-label", "value", "onChange", "as", "FormLabel", "column", "sm", "lg", "e", "map", "item", "index", "option", "HazardTableFilter", "ProjectstatusTable", "tabledata", "setDataToTable", "useState", "setLoading", "totalRows", "setTotalRows", "perPage", "setPerPage", "isModalShow", "setModal", "selectProjectstatus", "setSelectProjectstatus", "projectstatusParams", "sort", "limit", "page", "query", "columns", "name", "selector", "cell", "div", "Link", "href", "d", "i", "a", "onClick", "userAction", "getProjectstatusData", "response", "apiService", "get", "data", "length", "totalCount", "handlePerRowsChange", "newPerPage", "row", "modalConfirm", "remove", "toast", "success", "error", "modalHide", "useEffect", "Modal", "show", "onHide", "Header", "closeButton", "Title", "Body", "Footer", "<PERSON><PERSON>", "variant", "RKITable", "pagServer", "handlePageChange", "WorldregionTable", "selectWorldregion", "setSelectWorldregion", "worldregionParams", "sortable", "code", "getWorldregionData", "state", "ShowWorldregionIndex", "WorldregionIndex", "style", "overflowX", "PageHeading", "size", "ShowAddWorldRegion", "canAddWorldRegion", "useSelector", "permissions", "worl_region", "NoAccessMessage", "ShowCountryIndex", "CountryTable", "ShowAddCountry", "canAddCountry", "country", "ShowOperationstatusIndex", "OperationstatusIndex", "OperationstatusTable", "ShowAddOperationStatus", "canAddOperationStatus", "operation_status", "props", "_initialoperationstatus", "initialVal", "setInitialVal", "OperationstatusForm", "editform", "routes", "formRef", "useRef", "handleSubmit", "event", "toastMsg", "preventDefault", "obj", "trim", "patch", "post", "Router", "errorCode", "operationstatusParams", "getOperationstatusData", "prevState", "Card", "marginTop", "boxShadow", "ValidationFormWrapper", "onSubmit", "ref", "initialValues", "enableReinitialize", "hr", "Form", "Group", "Label", "TextInput", "id", "required", "validator", "String", "errorMessage", "handleChange", "target", "re<PERSON><PERSON><PERSON><PERSON>", "window", "scrollTo", "UpdateTypeIndex", "ShowUpdateTypeIndex", "UpdateTypeTable", "ShowAddUpdateTypes", "canAddAreaOfWork", "AreaOfWorkForm", "_initialareaOfWork", "areaOfWorkParams", "i18n", "HazardTable", "currentLang", "language", "setFilterText", "React", "resetPaginationToggle", "setResetPaginationToggle", "selectHazard", "setSelectHazard", "currLang", "handleMonitored", "_", "enabled", "Toggle", "Check", "label", "checked", "hazard_type", "span", "cursor", "getHazardsData", "hazardParams", "subHeaderComponentMemo", "<PERSON><PERSON><PERSON><PERSON>", "q", "handleSearchTitle", "Number", "process", "handleClear", "subheader", "subHeaderComponent", "ShowSyndromeIndex", "SyndromeIndex", "SyndromeTable", "ShowAddSyndromes", "canAddSyndromes", "syndrome", "WorldregionForm", "_initialworldregion", "InstitutionApproval", "ShowInstitutionApproval", "InstitutionTable", "ShowAddOrganisationApproval", "canAddOrganisationApproval", "institution", "_initialdeploymentstatus", "DeploymentstatusForm", "deploymentstatusParams", "getDeploymentstatusData", "_initialSyndrome", "description", "SyndromeForm", "values", "handleDescription", "syndromeParams", "getSyndromeData", "EditorComponent", "initContent", "evt", "HazardTypeTable", "selectHazardType", "setSelectHazardType", "replace", "getHazardsTypeData", "hazardTypeParams", "_initialinstitutionType", "institutionTypeParams", "getInstitutionTypeData", "_initialHazardType", "getHazardTypeData", "EventstatusIndex", "ShowEventstatusIndex", "EventstatusTable", "ShowAddEventStatus", "canAddEventStatus", "event_status", "ShowInstitutionTypeIndex", "InstitutionTypeIndex", "InstitutionTypeTable", "ShowAddOrganisationTypes", "canAddOrganisationTypes", "institution_type", "titleSearch", "title_de", "selectCountryDetails", "setSelectCountry", "countryParams", "languageCode", "dial_code", "world_region", "getCountriesData", "countryParams_initial", "useMemo", "CountryTableFilter", "selectInstitutionNetwork", "setSelectInstitutionNetwork", "Networkpopover", "Popover", "p", "b", "icons", "OverlayTrigger", "trigger", "placement", "overlay", "aria-hidden", "institutionNetworkParams", "getInstitutionNetworkData", "DeploymentstatusIndex", "ShowDeploymentstatusIndex", "DeploymentstatusTable", "ShowAddDeploymentStatus", "canAddDeploymentStatus", "_initialupdateType", "icon", "UpdateTypeForm", "updateTypeParams", "getUpdateTypeData", "selectUpdateType", "setSelectUpdateType", "updateTypeParams_initials", "_props", "FocalPointShow", "ShowFocalPoint", "AdminTable", "ShowAddFocalPointApproval", "canAddFocalPointApproval", "institution_focal_point", "CategoryTable", "selectCategory", "setSelectCategory", "categoryParams", "getCategoryData", "AreaOfWorkIndex", "ShowAreaOfWorkIndex", "AreaOfWorkTable", "ShowAddAreaOfWork", "area_of_work", "selectEventstatus", "setSelectEventstatus", "eventstatusParams", "geteventstatusData", "RisklevelIndex", "ShowRisklevelIndex", "RisklevelTable", "ShowAddRiskLevels", "canAddRiskLevels", "risk_level", "RoleIndex", "RoleTable", "selectInstitutionType", "setSelectInstitutionType", "ShowHazardTypeIndex", "HazardTypeIndex", "ShowAddHazardTypes", "canAddHazardTypes", "_initialcategory", "CategoryForm", "ShowVirtualSpace", "VirtualSpaceShow", "VspaceAdmin", "ShowAddVspaceApproval", "canAddVspaceApproval", "RegionForm", "_initialRegion", "setCountry", "getCountry", "regionParams", "getRegionData", "SelectGroup", "_i", "ProjectstatusForm", "_initialprojectstatus", "_initialeventstatus", "getEventstatusData", "getSelectFieldOptions", "params", "select", "tableData", "setTableData", "setType", "loading", "contentItem", "setContentItem", "currentPage", "setCurrentPage", "currentUser", "setcurrentUser", "created_at", "modules_func", "user", "username", "moment", "format", "updated_at", "roles", "includes", "fetchData", "currentUserResponse", "Array", "isArray", "handleSort", "sortDirection", "handleFilterTypeChange", "type_initial", "defaultRowsPerPage", "onSort", "sortServer", "persistTableHead", "selectdeploymentstatus", "setSelectdeploymentstatus", "getdeploymentstatusData", "ShowExpertiseIndex", "ExpertiseTable", "ShowAddExpertise", "canAddExpertise", "expertise", "newStatus", "setNewStatus", "selectUserDetails", "setSelectUserDetails", "usersParams", "vspace_status", "email", "getUsersData", "status", "setStatus", "updatedData", "message", "char<PERSON>t", "toUpperCase", "slice", "ShowProjectstatusIndex", "ShowAddProjectStatus", "canAddProjectStatus", "project_status", "instParams", "contact_name", "instAction", "getInstData", "RegionTable", "selectRegionDetails", "setSelectRegion", "countryId", "setCountryId", "RegionTableFilter", "<PERSON><PERSON><PERSON><PERSON>", "handleCountry", "_initialrole", "formValues", "roleParams", "successMessage", "ShowInstitutionNetworkIndex", "InstitutionNetworkTable", "ShowAddOrganisationNetworks", "canAddOrganisationNetworks", "_<PERSON>risklevel", "level", "toUppercase", "_str", "parseInt", "risklevelParams", "getRisklevelData", "min", "HazardIndex", "ShowHazardIndex", "ShowAddHazards", "canAddHazards", "hazard", "router", "useRouter", "CountryIndex", "CountryForm", "HazardForm", "HazardTypeForm", "RegionIndex", "LandingPageIndex", "LandingPageForm", "CategoryIndex", "EventstatusForm", "ProjectstatusIndex", "ExpertiseIndex", "ExpertiseForm", "RisklevelForm", "RoleForm", "UserIndex", "UserForm", "MailSettingsForm", "InstitutionTypeForm", "InstitutionNetworkIndex", "InstitutionNetworkForm", "Content", "selectRisklevel", "setSelectRisklevel", "selectRole", "setSelectRole", "getRoleData", "_initialexpertise", "expertiseParams", "getExpertiseData", "ShowLandingPageIndex", "LandingPageTable", "ShowcAddLandingPage", "canAddLandingPage", "landing_page", "selectExpertise", "setSelectExpertise", "selectSyndrome", "setSelectSyndrome", "SowRegionIndex", "overflow", "ShowAddRegions", "canAddRegions", "region", "selectOperationstatus", "setSelectOperationstatus", "getoperationstatusData", "_initialinstitutionNetowrk", "countries", "setCountreis", "fetchCountries", "Select", "isClearable", "options", "AreaOfWorkCountry", "setAreaOfWorkCountry", "getAredOfWorkData", "areaOfWorkParamsinitials", "institutionName", "institutionStatus", "getTableData", "for<PERSON>ach", "institutionInvites", "institutionInvite", "push", "institutionId", "localPaginate", "array", "page_size", "page_number", "console", "log", "invite", "selectLandingPage", "setSelectLandingPage", "isEnabled", "landingPageParams", "getlandingPageData"], "sourceRoot": "", "ignoreList": []}