"use strict";exports.id=2491,exports.ids=[2491],exports.modules={24047:(e,s,a)=>{a.d(s,{x:()=>r});var t=a(8732),i=a(82015);let n=({value:e,onChange:s,placeholder:a="Write something...",height:n=300,disabled:r=!1})=>{let l=(0,i.useRef)(null),[c,d]=(0,i.useState)(!1);return(0,i.useEffect)(()=>{l.current},[e,c]),(0,t.jsx)("div",{className:"simple-rich-text-editor",style:{border:"1px solid #ccc"},children:!1})},r=e=>{let{initContent:s,onChange:a}=e;return(0,t.jsx)(n,{value:s||"",onChange:e=>a(e)})}},47749:(e,s,a)=>{a.d(s,{A:()=>o});var t=a(8732),i=a(82015),n=a(59549),r=a(83551),l=a(49481),c=a(91353),d=a(88751);let o=e=>{let[s,a]=(0,i.useState)(""),{t:o}=(0,d.useTranslation)("common"),u=()=>{m()},m=()=>{e.handleDiscussSubmit(s),a("")};return(0,t.jsx)(n.A,{children:(0,t.jsxs)(r.A,{children:[(0,t.jsx)(l.A,{sm:9,lg:10,children:(0,t.jsx)(n.A.Control,{className:"mb-2",type:"text",value:s,onKeyPress:e=>{"Enter"===e.key&&(u(),e.preventDefault())},onChange:e=>{let{value:s}=e.target;a(s)}})}),(0,t.jsx)(l.A,{sm:3,lg:2,children:(0,t.jsx)(c.A,{onClick:m,children:o("submit")})})]})})}},58478:(e,s,a)=>{a.d(s,{b:()=>i});var t=a(81366);let i=a.n(t)()({authenticatedSelector:e=>!!e.permissions&&!!e.permissions.update&&!!e.permissions.update["create:any"],wrapperDisplayName:"CanAddDiscussionUpdate"})},82491:(e,s,a)=>{a.a(e,async(e,t)=>{try{a.d(s,{A:()=>_});var i=a(8732),n=a(82015),r=a(74716),l=a.n(r);a(86843);var c=a(49481),d=a(91353),o=a(7082),u=a(83551),m=a(82053),p=a(54131),x=a(27825),h=a.n(x),j=a(63487),y=a(214),v=a(58478),f=a(47749),g=a(88751),N=a(24047),A=e([p,j]);[p,j]=A.then?(await A)():A;let _=e=>{let{t:s,i18n:a}=(0,g.useTranslation)("common"),t="fr"===a.language?"en":a.language,{updates:r,setUpdates:x}=(0,n.useContext)(y.bM),[A,_]=(0,n.useState)([]),[S,b]=(0,n.useState)(-1),[w,C]=(0,n.useState)(""),[k,I]=(0,n.useState)(""),[T,U]=(0,n.useState)(""),[z]=(0,n.useState)(t),[D,B]=(0,n.useState)(""),F=async()=>{let e=await j.A.get("/updatetype");if(e&&e.data&&e.data.length>0){let s=h().find(e.data,{title:"Conversation"});if(s&&s._id)return U(s._id),s._id}},E=async(a,t)=>{if(t){let s=await j.A.get("/updates",{query:{type:a,update_type:t},sort:{title:"asc"},limit:"~"});if(s&&s.data){let t=`parent_${a}`,i=h().filter(s.data,{[t]:e.id});_(i)}}B(A&&A.length>0?"":s("NoFilesFound!"))};(0,n.useEffect)(()=>{(async()=>{let s=await j.A.post("/users/getLoggedUser",{});s&&s.username&&I(s.username);let a=await F();await E(e.type,a)})()},[]);let L=async s=>{if(s){let a={title:s,reply:[],type:e.type,update_type:T,show_as_announcement:!1,user:{username:k}};switch(e.type){case"operation":a.parent_operation=e.id;break;case"event":a.parent_event=e.id;break;case"project":a.parent_project=e.id;break;case"vspace":a.parent_vspace=e.id;break;case"country":a.parent_country=e.id;break;case"hazard":a.parent_hazard=e.id;break;case"institution":a.parent_institution=e.id}let t=await j.A.post("updates",a);_([...A,t]),localStorage.setItem("discuss",JSON.stringify([...A,a])),x([t,...r])}},R=e=>{C(e)},J=async e=>{A[e].reply.push({user:k,msg:w,time:new Date}),_([...A]),b(-1),C(""),await j.A.patch(`/updates/${A[e]._id}`,A[e]),localStorage.setItem("discuss",JSON.stringify(A))},M=e=>({__html:e}),O=e=>(0,i.jsx)(c.A,{sm:2,children:(0,i.jsx)(d.A,{variant:"secondary",size:"sm",onClick:()=>{b(e.i)},children:s("Reply")})}),$=(0,v.b)(()=>(0,i.jsx)(f.A,{handleDiscussSubmit:L})),q=(0,v.b)(e=>(0,i.jsx)(O,{i:e.i}));return(0,i.jsxs)(o.A,{fluid:!0,children:[(0,i.jsx)($,{}),A&&0==A.length?(0,i.jsx)("div",{children:(0,i.jsx)("p",{className:"d-flex d-flex justify-content-center p-2 m-0",children:D})}):(0,i.jsx)(i.Fragment,{children:A.map((e,a)=>e.title?(0,i.jsxs)("div",{className:"discItem",children:[(0,i.jsxs)(u.A,{children:[(0,i.jsx)(c.A,{sm:10,children:(0,i.jsxs)("div",{className:"discThread",children:[(0,i.jsx)("div",{className:"discAvatar",children:(0,i.jsx)(m.FontAwesomeIcon,{icon:p.faUser,color:"#fff",size:"lg"})}),(0,i.jsxs)("div",{className:"discBody",children:[(0,i.jsxs)("div",{className:"discUser",children:[(0,i.jsx)("span",{className:"discUserName",children:e&&e.user&&e.user.username})," ","-"," ",(0,i.jsx)("span",{className:"discTime",children:l().utc(e.created_at).locale(z).fromNow()})]}),(0,i.jsx)("div",{className:"discBodyInnner",children:e.title})]})]})}),-1!==S&&a===S?null:(0,i.jsx)(q,{i:a})]}),e.reply&&e.reply.map((e,s)=>(function(e,s,a,t){return(0,i.jsx)("div",{className:"discReply",children:(0,i.jsx)("div",{style:{marginLeft:"55px",marginBottom:"10px",marginTop:"10px"},children:(0,i.jsxs)("div",{className:"discThread",children:[(0,i.jsx)("div",{className:"discAvatar",children:(0,i.jsx)(m.FontAwesomeIcon,{icon:p.faUser,color:"#fff",size:"xs"})}),(0,i.jsxs)("div",{className:"discBody",children:[(0,i.jsxs)("div",{className:"discUser",children:[(0,i.jsx)("span",{className:"discUserName",children:s&&s.user})," -"," ",(0,i.jsx)("span",{className:"discTime",children:s.time?l().utc(s.time).locale(a).fromNow():l()().fromNow()})]}),(0,i.jsx)("div",{className:"discBodyInnner",children:(0,i.jsx)("div",{dangerouslySetInnerHTML:t(s.msg)})})]})]})})},e)})(s,e,z,M)),-1!==S&&a===S?(0,i.jsxs)("div",{children:[(0,i.jsx)(N.x,{initContent:w,onChange:e=>R(e)})," ",(0,i.jsx)("br",{}),(0,i.jsx)(d.A,{disabled:!w.length,size:"sm",variant:"info",onClick:()=>J(a),children:s("Send")})," ","\xa0",(0,i.jsx)(d.A,{variant:"secondary",size:"sm",onClick:()=>{b(-1)},children:s("Cancel")})]}):null]},a):null)})]})};t()}catch(e){t(e)}})}};