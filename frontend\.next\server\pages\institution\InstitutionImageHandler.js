"use strict";(()=>{var e={};e.id=6540,e.ids=[636,3220,6540],e.modules={123:e=>{e.exports=require("dom-helpers/removeEventListener")},1332:e=>{e.exports=require("react-custom-scrollbars-2")},1428:e=>{e.exports=import("axios")},1680:e=>{e.exports=require("@restart/ui/utils")},1919:e=>{e.exports=require("react-transition-group/Transition")},3892:e=>{e.exports=require("classnames")},4048:e=>{e.exports=require("react-truncate")},6009:e=>{e.exports=require("dom-helpers/ownerDocument")},6952:e=>{e.exports=require("@restart/ui/Modal")},7374:e=>{e.exports=require("@restart/ui/Dropdown")},8732:e=>{e.exports=require("react/jsx-runtime")},9653:e=>{e.exports=require("@restart/ui/Overlay")},11242:e=>{e.exports=require("redux-persist/integration/react")},11688:e=>{e.exports=require("@restart/hooks/useWillUnmount")},12103:e=>{e.exports=require("react-avatar-editor")},12388:e=>{e.exports=require("dom-helpers/addEventListener")},13364:e=>{e.exports=require("redux-saga/effects")},14062:e=>{e.exports=import("react-redux")},14078:e=>{e.exports=import("swr")},14332:e=>{e.exports=require("uncontrollable")},16029:e=>{e.exports=require("react-dropzone")},16116:e=>{e.exports=require("invariant")},18511:(e,r,t)=>{t.a(e,async(e,s)=>{try{t.r(r),t.d(r,{default:()=>q});var o=t(8732),i=t(82015),a=t(12103),n=t.n(a),l=t(19442),u=t.n(l),p=t(12403),d=t(83551),c=t(49481),x=t(91353),m=t(42893),h=t(63487),g=t(88751),f=e([m,h]);[m,h]=f.then?(await f)():f;let q=({isOpen:e,onModalClose:r,image:t,getId:s,fileName:a,getBlob:l})=>{let[f,q]=(0,i.useState)(1),[b,y]=(0,i.useState)(""),[j,w]=(0,i.useState)(null),v=(0,i.useRef)(null),{t:S}=(0,g.useTranslation)("common");(0,i.useEffect)(()=>{y(a)},[a]);let A=async()=>{let e=v.current.getImage().toDataURL("image/jpeg",.6),t=(e=>{let r=e.split(","),t=r[0].match(/:(.*?);/)?.[1],s=atob(r[1]),o=s.length,i=new Uint8Array(o);for(;o--;)i[o]=s.charCodeAt(o);return new Blob([i],{type:t})})(e),o=(window.URL||window.webkitURL).createObjectURL(t);l(o);let i=new FormData;i.append("file",t,b);try{let e=await h.A.post("/image",i,{"Content-Type":"multipart/form-data"});e&&e._id&&s(e._id)}catch{throw"Something wrong in server || your data!"}m.default.success(S("toast.CroppedtheimageSuccessfully")),r(!1),w(null),y("none"),q(1)};return(0,o.jsx)(o.Fragment,{children:(0,o.jsx)("div",{children:(0,o.jsxs)(p.A,{show:e,size:"lg","aria-labelledby":"ProfileEdit",onHide:()=>r(!1),centered:!0,children:[(0,o.jsxs)(p.A.Body,{children:[(0,o.jsxs)("div",{className:"d-flex flex-column justify-content-center align-items-center imgRotate",children:[(0,o.jsx)(n(),{ref:v,width:700,height:400,borderRadius:2,scale:f,color:[0,0,0,.6],image:j||t,style:{width:"100%",height:"auto"}}),(0,o.jsx)("div",{className:"info-identifier",children:(0,o.jsx)("span",{children:S("ThisareawillcontainyourInstitutionandfocalpointinformation")})})]}),(0,o.jsx)("div",{className:"mx-2 my-3",children:(0,o.jsxs)(d.A,{children:[(0,o.jsx)(c.A,{sm:1,md:1,lg:1,className:"pe-0",children:(0,o.jsx)("b",{children:S("Zoom")})}),(0,o.jsx)(c.A,{sm:11,md:11,lg:11,children:(0,o.jsx)(u(),{value:f,tooltip:"auto",min:1,max:10,step:.01,variant:"primary",onChange:e=>q(Number(e.target.value))})})]})})]}),(0,o.jsxs)(p.A.Footer,{children:[(0,o.jsx)(x.A,{onClick:A,children:S("Crop")}),(0,o.jsx)(x.A,{variant:"danger",onClick:()=>r(!1),children:S("Cancel")})]})]})})})};s()}catch(e){s(e)}})},18622:e=>{e.exports=require("yup")},19121:e=>{e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},19442:e=>{e.exports=require("react-bootstrap-range-slider")},22326:e=>{e.exports=require("react-dom")},22541:e=>{e.exports=require("redux-persist/lib/storage")},25303:e=>{e.exports=require("@restart/ui/NavItem")},26324:e=>{e.exports=require("warning")},27825:e=>{e.exports=require("lodash")},27910:e=>{e.exports=require("stream")},28217:e=>{e.exports=require("@restart/ui/DropdownItem")},29021:e=>{e.exports=require("fs")},29294:e=>{e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},29628:e=>{e.exports=require("dom-helpers/addClass")},29825:e=>{e.exports=require("prop-types")},29841:e=>{e.exports=require("dom-helpers/hasClass")},30362:e=>{e.exports=require("@restart/ui/DropdownToggle")},33873:e=>{e.exports=require("path")},36653:e=>{e.exports=require("nprogress")},36955:e=>{e.exports=require("@restart/hooks/useBreakpoint")},37766:e=>{e.exports=require("@restart/hooks/useMergedRefs")},39756:e=>{e.exports=import("redux")},40051:e=>{e.exports=require("dom-helpers/removeClass")},40069:(e,r,t)=>{t.a(e,async(e,s)=>{try{t.r(r),t.d(r,{config:()=>g,default:()=>c,getServerSideProps:()=>h,getStaticPaths:()=>m,getStaticProps:()=>x,reportWebVitals:()=>f,routeModule:()=>v,unstable_getServerProps:()=>j,unstable_getServerSideProps:()=>w,unstable_getStaticParams:()=>y,unstable_getStaticPaths:()=>b,unstable_getStaticProps:()=>q});var o=t(63885),i=t(80237),a=t(81413),n=t(9616),l=t.n(n),u=t(72386),p=t(55122),d=e([u,p]);[u,p]=d.then?(await d)():d;let c=(0,a.M)(p,"default"),x=(0,a.M)(p,"getStaticProps"),m=(0,a.M)(p,"getStaticPaths"),h=(0,a.M)(p,"getServerSideProps"),g=(0,a.M)(p,"config"),f=(0,a.M)(p,"reportWebVitals"),q=(0,a.M)(p,"unstable_getStaticProps"),b=(0,a.M)(p,"unstable_getStaticPaths"),y=(0,a.M)(p,"unstable_getStaticParams"),j=(0,a.M)(p,"unstable_getServerProps"),w=(0,a.M)(p,"unstable_getServerSideProps"),v=new o.PagesRouteModule({definition:{kind:i.A.PAGES,page:"/institution/InstitutionImageHandler",pathname:"/institution/InstitutionImageHandler",bundlePath:"",filename:""},components:{App:u.default,Document:l()},userland:p});s()}catch(e){s(e)}})},40361:e=>{e.exports=require("next/dist/compiled/next-server/pages.runtime.prod.js")},42893:e=>{e.exports=import("react-hot-toast")},43294:e=>{e.exports=require("formik")},50009:e=>{e.exports=require("@restart/ui/DropdownMenu")},54131:e=>{e.exports=import("@fortawesome/free-solid-svg-icons")},55122:(e,r,t)=>{t.a(e,async(e,s)=>{try{t.r(r),t.d(r,{default:()=>m});var o=t(8732),i=t(82015),a=t(16029),n=t(82053),l=t(54131),u=t(42893),p=t(18511),d=t(63487),c=t(88751),x=e([l,u,p,d]);[l,u,p,d]=x.then?(await x)():x;let m=({getId:e,header:r,type:t})=>{let{t:s}=(0,c.useTranslation)("common"),[x,m]=(0,i.useState)(!1),[h,g]=(0,i.useState)([]),[f,q]=(0,i.useState)(""),[b,y]=(0,i.useState)(""),j="application"===t?"/files":"/image";(0,i.useEffect)(()=>{r?y(`http://localhost:3001/api/v1/image/show/${r}`):y(null)},[r]);let w={flex:1,display:"flex",flexDirection:"column",alignItems:"center",justifyContent:"center",width:"100%",height:"100%",borderWidth:.1,borderColor:"#fafafa",backgroundColor:"#fafafa",color:"black",transition:"border  .24s ease-in-out"},v={borderColor:"#2196f3"},S=e=>{m(e)},{getRootProps:A,getInputProps:P,isDragActive:C,isDragAccept:k,isDragReject:M,fileRejections:E}=(0,a.useDropzone)({noClick:!1,accept:"image/*",multiple:!1,minSize:0,maxSize:2e6,onDrop:e=>{let r=e.map((e,r)=>Object.assign(e,{preview:URL.createObjectURL(e)}));g(r),e.length>0&&m(!0)},validator:function(e){return"/image"===j?"image"===e.type.substring(0,5)||u.default.error(s("toast.filetypenotsupport")):"/files"===j&&"image"===e.type.substring(0,5)&&u.default.error(s("toast.filetypenotsupport")),null}}),I=(0,i.useMemo)(()=>({...w,...C?v:{outline:"2px dashed #bbb"},...k?{outline:"2px dashed #595959"}:{outline:"2px dashed #bbb"},...M?{outline:"2px dashed red"}:{activeStyle:v}}),[C,M]),_=E.length>0&&E[0].file.size>2e6,R=r=>{q(r),e(r)},D=async()=>{let e;(e=r?await d.A.remove(`image/${r}`):await d.A.remove(`image/${f}`))&&e._id&&(y(null),R(null))},z=e=>{y(e)};return(0,o.jsxs)(o.Fragment,{children:[(0,o.jsx)("div",{className:" d-flex justify-content-center align-items-center mt-3",style:{width:"100%",height:"180px"},children:(0,o.jsxs)("div",{...A({style:I}),children:[(0,o.jsx)("input",{...P()}),(0,o.jsx)(n.FontAwesomeIcon,{icon:l.faCloudUploadAlt,size:"4x",color:"#999"}),(0,o.jsx)("p",{style:{color:"#595959",marginBottom:"0px"},children:s("Drag'n'dropsomefileshere,orclicktoselectfiles")}),(0,o.jsxs)("small",{style:{color:"#595959"},children:[(0,o.jsx)("b",{children:s("Note:")})," ",s("Onesingleimagewillbeaccepted")]}),_&&(0,o.jsxs)("small",{className:"text-danger mt-2",children:[(0,o.jsx)(n.FontAwesomeIcon,{icon:l.faExclamationCircle,size:"1x",color:"red"}),"\xa0",s("FileistoolargeItshouldbelessthan2MB")]}),M&&(0,o.jsxs)("small",{className:"text-danger",style:{color:"red"},children:[(0,o.jsx)(n.FontAwesomeIcon,{icon:l.faExclamationCircle,size:"1x",color:"red"}),s("Filetypenotacceptedsorr")]})]})}),b&&(0,o.jsx)(o.Fragment,{children:(0,o.jsx)("div",{style:{display:"flex",flexDirection:"row",justifyContent:"flex-start",flexWrap:"wrap",marginTop:20},children:(0,o.jsxs)("div",{style:{display:"inline-flex",borderRadius:2,border:"1px solid #ddd",marginBottom:8,marginRight:20,width:170,height:100,padding:2,position:"relative",boxShadow:"0 0 15px 0.25px rgba(0,0,0,0.25)",boxSizing:"border-box"},children:[(0,o.jsx)("div",{style:{display:"flex"},children:(0,o.jsx)("img",{src:b,style:{display:"block",height:"100%"}})}),(0,o.jsx)(n.FontAwesomeIcon,{icon:l.faTimesCircle,style:{position:"absolute",fontSize:"22px",top:"-10px",right:"-10px",zIndex:1e3,cursor:"pointer",backgroundColor:"#fff",color:"#000",borderRadius:"50%"},color:"black",onClick:D})]})})}),(0,o.jsx)(p.default,{isOpen:x,getId:e=>R(e),image:h&&h[0]?h[0].preview:"",onModalClose:e=>S(e),fileName:h&&h[0]?h[0].name:"",getBlob:e=>z(e)})]})};s()}catch(e){s(e)}})},57664:e=>{e.exports=require("dom-helpers/scrollbarSize")},58928:e=>{e.exports=require("dom-helpers/canUseDOM")},59717:e=>{e.exports=require("@restart/ui/DropdownContext")},60560:e=>{e.exports=require("dom-helpers/contains")},63033:e=>{e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},65447:e=>{e.exports=require("@restart/ui/Button")},67364:e=>{e.exports=require("@restart/hooks/useCallbackRef")},68455:e=>{e.exports=import("redux-saga")},69722:e=>{e.exports=require("es6-promise")},74075:e=>{e.exports=require("zlib")},74716:e=>{e.exports=require("moment")},74987:e=>{e.exports=require("@restart/ui/ModalManager")},78097:e=>{e.exports=require("next-redux-saga")},78634:e=>{e.exports=require("@restart/ui/Anchor")},80237:(e,r)=>{Object.defineProperty(r,"A",{enumerable:!0,get:function(){return t}});var t=function(e){return e.PAGES="PAGES",e.PAGES_API="PAGES_API",e.APP_PAGE="APP_PAGE",e.APP_ROUTE="APP_ROUTE",e.IMAGE="IMAGE",e}({})},81366:e=>{e.exports=require("redux-auth-wrapper/connectedAuthWrapper")},81413:(e,r)=>{Object.defineProperty(r,"M",{enumerable:!0,get:function(){return function e(r,t){return t in r?r[t]:"then"in r&&"function"==typeof r.then?r.then(r=>e(r,t)):"function"==typeof r&&"default"===t?r:void 0}}})},81521:e=>{e.exports=require("dom-helpers/querySelectorAll")},81895:e=>{e.exports=require("@restart/hooks/useEventCallback")},82015:e=>{e.exports=require("react")},82053:e=>{e.exports=require("@fortawesome/react-fontawesome")},86842:e=>{e.exports=require("@restart/ui/SelectableContext")},87571:e=>{e.exports=require("dom-helpers/transitionEnd")},88751:e=>{e.exports=require("next-i18next")},93787:e=>{e.exports=require("redux-persist")},94947:e=>{e.exports=require("@restart/hooks/useTimeout")},96196:e=>{e.exports=require("next-redux-wrapper")},98320:e=>{e.exports=require("@restart/hooks/useIsomorphicEffect")},99460:e=>{e.exports=require("dom-helpers/css")}};var r=require("../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[6089,9216,9616,2386],()=>t(40069));module.exports=s})();