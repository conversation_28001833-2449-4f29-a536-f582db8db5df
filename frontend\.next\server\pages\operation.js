"use strict";(()=>{var e={};e.id=3451,e.ids=[636,3220,3451],e.modules={123:e=>{e.exports=require("dom-helpers/removeEventListener")},1332:e=>{e.exports=require("react-custom-scrollbars-2")},1428:e=>{e.exports=import("axios")},1680:e=>{e.exports=require("@restart/ui/utils")},1919:e=>{e.exports=require("react-transition-group/Transition")},3892:e=>{e.exports=require("classnames")},4048:e=>{e.exports=require("react-truncate")},6009:e=>{e.exports=require("dom-helpers/ownerDocument")},6952:e=>{e.exports=require("@restart/ui/Modal")},7374:e=>{e.exports=require("@restart/ui/Dropdown")},8732:e=>{e.exports=require("react/jsx-runtime")},9653:e=>{e.exports=require("@restart/ui/Overlay")},11242:e=>{e.exports=require("redux-persist/integration/react")},11688:e=>{e.exports=require("@restart/hooks/useWillUnmount")},12388:e=>{e.exports=require("dom-helpers/addEventListener")},13364:e=>{e.exports=require("redux-saga/effects")},14062:e=>{e.exports=import("react-redux")},14078:e=>{e.exports=import("swr")},14332:e=>{e.exports=require("uncontrollable")},14446:(e,r,t)=>{t.a(e,async(e,s)=>{try{t.r(r),t.d(r,{config:()=>q,default:()=>c,getServerSideProps:()=>h,getStaticPaths:()=>m,getStaticProps:()=>x,reportWebVitals:()=>y,routeModule:()=>_,unstable_getServerProps:()=>j,unstable_getServerSideProps:()=>A,unstable_getStaticParams:()=>f,unstable_getStaticPaths:()=>S,unstable_getStaticProps:()=>g});var a=t(63885),o=t(80237),i=t(81413),n=t(9616),u=t.n(n),l=t(72386),p=t(99352),d=e([l,p]);[l,p]=d.then?(await d)():d;let c=(0,i.M)(p,"default"),x=(0,i.M)(p,"getStaticProps"),m=(0,i.M)(p,"getStaticPaths"),h=(0,i.M)(p,"getServerSideProps"),q=(0,i.M)(p,"config"),y=(0,i.M)(p,"reportWebVitals"),g=(0,i.M)(p,"unstable_getStaticProps"),S=(0,i.M)(p,"unstable_getStaticPaths"),f=(0,i.M)(p,"unstable_getStaticParams"),j=(0,i.M)(p,"unstable_getServerProps"),A=(0,i.M)(p,"unstable_getServerSideProps"),_=new a.PagesRouteModule({definition:{kind:o.A.PAGES,page:"/operation",pathname:"/operation",bundlePath:"",filename:""},components:{App:l.default,Document:u()},userland:p});s()}catch(e){s(e)}})},16116:e=>{e.exports=require("invariant")},18622:e=>{e.exports=require("yup")},19121:e=>{e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},22326:e=>{e.exports=require("react-dom")},22541:e=>{e.exports=require("redux-persist/lib/storage")},25303:e=>{e.exports=require("@restart/ui/NavItem")},26324:e=>{e.exports=require("warning")},27825:e=>{e.exports=require("lodash")},27910:e=>{e.exports=require("stream")},28163:(e,r,t)=>{t.r(r),t.d(r,{canAddOperation:()=>n,canAddOperationForm:()=>u,canEditOperation:()=>l,canEditOperationForm:()=>p,canViewDiscussionUpdate:()=>d,default:()=>c});var s=t(8732);t(82015);var a=t(81366),o=t.n(a),i=t(61421);let n=o()({authenticatedSelector:e=>!!e.permissions&&!!e.permissions.operation&&!!e.permissions.operation["create:any"],wrapperDisplayName:"CanAddOperation"}),u=o()({authenticatedSelector:e=>!!e.permissions&&!!e.permissions.operation&&!!e.permissions.operation["create:any"],wrapperDisplayName:"CanAddOperationForm",FailureComponent:()=>(0,s.jsx)(i.default,{})}),l=o()({authenticatedSelector:(e,r)=>{if(e.permissions&&e.permissions.operation){if(e.permissions.operation["update:any"])return!0;else if(e.permissions.operation["update:own"]&&r.operation&&r.operation.user&&r.operation.user._id===e.user._id)return!0}return!1},wrapperDisplayName:"CanEditOperation"}),p=o()({authenticatedSelector:(e,r)=>{if(e.permissions&&e.permissions.operation){if(e.permissions.operation["update:any"])return!0;else if(e.permissions.operation["update:own"]&&r.operation&&r.operation.user&&r.operation.user._id===e.user._id)return!0}return!1},wrapperDisplayName:"CanEditOperationForm",FailureComponent:()=>(0,s.jsx)(i.default,{})}),d=o()({authenticatedSelector:e=>!!e.permissions&&!!e.permissions.update&&!!e.permissions.update["read:any"],wrapperDisplayName:"CanViewDiscussionUpdate"}),c=n},28217:e=>{e.exports=require("@restart/ui/DropdownItem")},29021:e=>{e.exports=require("fs")},29294:e=>{e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},29628:e=>{e.exports=require("dom-helpers/addClass")},29825:e=>{e.exports=require("prop-types")},29841:e=>{e.exports=require("dom-helpers/hasClass")},30362:e=>{e.exports=require("@restart/ui/DropdownToggle")},33873:e=>{e.exports=require("path")},35576:e=>{e.exports=require("next-i18next/serverSideTranslations")},36653:e=>{e.exports=require("nprogress")},36955:e=>{e.exports=require("@restart/hooks/useBreakpoint")},37766:e=>{e.exports=require("@restart/hooks/useMergedRefs")},38609:e=>{e.exports=require("react-data-table-component")},39756:e=>{e.exports=import("redux")},39982:(e,r,t)=>{t.a(e,async(e,s)=>{try{t.r(r),t.d(r,{default:()=>S});var a=t(8732),o=t(82015),i=t(19918),n=t.n(i),u=t(27825),l=t.n(u),p=t(44233),d=t(74716),c=t.n(d),x=t(56084),m=t(63487),h=t(44164),q=t(88751),y=e([m,h]);[m,h]=y.then?(await y)():y;let g=({partners:e})=>e&&e.length>0?(0,a.jsx)("ul",{children:e.map((e,r)=>{if(e.institution)return(0,a.jsx)("li",{children:(0,a.jsx)(n(),{href:"/institution/[...routes]",as:`/institution/show/${e.institution._id}`,children:e.institution.title})},r)})}):null,S=function(e){let{t:r}=(0,q.useTranslation)("common"),t=(0,p.useRouter)(),{setOperations:s,selectedRegions:i}=e,[u,d]=(0,o.useState)(""),[y,S]=(0,o.useState)(""),[f,j]=(0,o.useState)(!1),[A,_]=(0,o.useState)([]),[w,v]=(0,o.useState)(!1),[b,C]=(0,o.useState)(0),[M,P]=(0,o.useState)(10),[k,D]=(0,o.useState)(1),[N,O]=(0,o.useState)(null),I={sort:{created_at:"desc"},lean:!0,populate:[{path:"partners.status",select:"title"},{path:"partners.institution",select:"title"},{path:"status",select:"title"},{path:"country",select:"coordinates"}],limit:M,page:1,query:{},select:"-timeline -region -hazard -description -end_date -syndrome -hazard_type -created_at -updated_at"},[T,E]=(0,o.useState)(I),F=[{name:r("Operations"),selector:"title",sortable:!0,cell:e=>(0,a.jsx)(n(),{href:"/operation/[...routes]",as:`/operation/show/${e._id}`,children:e.title})},{name:r("Status"),selector:"status",sortable:!0,cell:e=>e.status&&e.status.title?e.status.title:""},{name:r("StartDate"),selector:"start_date",sortable:!0,cell:e=>e&&e.start_date?c()(e.start_date).format("M/D/Y"):""},{name:r("Partners"),selector:"partners",cell:e=>(0,a.jsx)(g,{partners:e.partners})}],R=async e=>{v(!0),t.query&&t.query.country&&(e.query.country=t.query.country),null===i?delete e.query.world_region:0===i.length?e.query.world_region=["__NO_MATCH__"]:e.query.world_region=i;let r=await m.A.get("/operation",e);r&&Array.isArray(r.data)&&(_(r.data),s(r.data),C(r.totalCount),v(!1))},z=async(e,r)=>{I.limit=e,I.page=r,v(!0),t.query&&t.query.country&&(I.query.country=t.query.country),null===i?delete I.query.world_region:0===i.length?I.query.world_region=["__NO_MATCH__"]:I.query.world_region=i,y&&(I.query={...I.query,status:y}),N&&(I.sort=N.sort);let a=await m.A.get("/operation",I);a&&Array.isArray(a.data)&&(_(a.data),s(a.data),P(e),v(!1)),D(r)},H=async(e,r)=>{v(!0),I.sort={[e.selector]:r},y&&(I.query={...I.query,status:y}),""!==u&&(I.query={...I.query,title:u}),await R(I),O(I),v(!1)},U=(e,r)=>{e?(T.query.title=e,T.page=r):delete T.query.title,E({...T})},V=(0,o.useRef)(l().debounce((e,r)=>U(e,r),Number("500")||300)).current,W=(0,o.useMemo)(()=>{let e=e=>{S(e),e?(T.query.status=e,T.page=k):delete T.query.status,E({...T})};return(0,a.jsx)(h.default,{onFilter:e=>{d(e.target.value),V(e.target.value,k)},onFilterStatusChange:r=>e(r.target.value),onClear:()=>{u&&(j(!f),d(""))},filterText:u,filterStatus:y})},[u,y,f,i]);return(0,a.jsx)(x.A,{columns:F,data:A,totalRows:b,loading:w,subheader:!0,persistTableHead:!0,onSort:H,sortServer:!0,pagServer:!0,resetPaginationToggle:f,subHeaderComponent:W,handlePerRowsChange:z,handlePageChange:e=>{I.limit=M,I.page=e,""!==u&&(I.query={title:u}),y&&(I.query={...I.query,status:y}),N&&(I.sort=N.sort),R(I),D(e)}})};s()}catch(e){s(e)}})},40051:e=>{e.exports=require("dom-helpers/removeClass")},40361:e=>{e.exports=require("next/dist/compiled/next-server/pages.runtime.prod.js")},42893:e=>{e.exports=import("react-hot-toast")},43294:e=>{e.exports=require("formik")},44164:(e,r,t)=>{t.a(e,async(e,s)=>{try{t.r(r),t.d(r,{default:()=>m});var a=t(8732),o=t(82015),i=t(7082),n=t(83551),u=t(49481),l=t(84517),p=t(59549),d=t(63487),c=t(88751),x=e([d]);d=(x.then?(await x)():x)[0];let m=({filterText:e,onFilter:r,onFilterStatusChange:t,onClear:s,filterStatus:x})=>{let[m,h]=(0,o.useState)([]),{t:q}=(0,c.useTranslation)("common"),y=async e=>{let r=await d.A.get("/operation_status",e);r&&Array.isArray(r.data)&&h(r.data)};return(0,o.useEffect)(()=>{y({query:{},sort:{title:"asc"}})},[]),(0,a.jsx)(i.A,{fluid:!0,className:"p-0",children:(0,a.jsxs)(n.A,{children:[(0,a.jsx)(u.A,{xs:6,className:"ps-0 align-self-end mb-3",children:(0,a.jsx)(l.A,{type:"text",className:"searchInput",placeholder:q("search"),"aria-label":"Search",value:e,onChange:r})}),(0,a.jsx)(u.A,{xs:6,children:(0,a.jsx)(p.A,{children:(0,a.jsxs)(p.A.Group,{as:n.A,controlId:"statusFilter",children:[(0,a.jsx)(p.A.Label,{column:!0,sm:"3",lg:"2",children:q("Status")}),(0,a.jsx)(u.A,{className:"ps-0 pe-1",children:(0,a.jsxs)(l.A,{as:"select","aria-label":"Status",onChange:t,value:x,children:[(0,a.jsx)("option",{value:"",children:"All"}),m.map((e,r)=>(0,a.jsx)("option",{value:e._id,children:e.title},r))]})})]})})})]})})};s()}catch(e){s(e)}})},50009:e=>{e.exports=require("@restart/ui/DropdownMenu")},53033:(e,r,t)=>{t.r(r),t.d(r,{default:()=>p});var s=t(8732),a=t(82015),o=t(27825),i=t.n(o),n=t(72953),u=t(89364),l=t(88751);let p=e=>{let{i18n:r}=(0,l.useTranslation)("common"),t=r.language,{operations:o}=e,[p,d]=(0,a.useState)([]),[c,x]=(0,a.useState)({}),[m,h]=(0,a.useState)({}),[q,y]=(0,a.useState)({}),g=()=>{x(null),h(null)},S=(e,r,t)=>{g(),x(r),h({name:e.name,id:e.id,countryId:e.countryId})},f=()=>{let e=[];i().forEach(o,r=>{e.push({title:r.title,id:r._id,lat:r.country&&r.country.coordinates&&parseFloat(r.country.coordinates[0].latitude),lng:r.country&&r.country.coordinates&&parseFloat(r.country.coordinates[0].longitude),countryId:r.country&&r.country._id})}),d([...e])};return(0,a.useEffect)(()=>{f(),o&&o.length>0&&y(i().groupBy(o,"country._id"))},[o]),(0,s.jsx)(n.A,{onClose:g,points:p,language:t,activeMarker:c,markerInfo:(0,s.jsx)(e=>{let{info:r}=e;return r&&r.countryId&&q[r.countryId]?(0,s.jsx)("ul",{children:q[r.countryId].map((e,r)=>(0,s.jsx)("li",{children:(0,s.jsx)("a",{href:`${t}/operation/show/${e._id}`,children:e.title})},r))}):null},{info:m}),children:p.length>=1?p.map((e,r)=>(0,s.jsx)(u.A,{name:e.title,id:e.id,countryId:e.countryId,icon:{url:"/images/map-marker-white.svg"},onClick:S,position:e},r)):null})}},57664:e=>{e.exports=require("dom-helpers/scrollbarSize")},58928:e=>{e.exports=require("dom-helpers/canUseDOM")},59717:e=>{e.exports=require("@restart/ui/DropdownContext")},60560:e=>{e.exports=require("dom-helpers/contains")},63033:e=>{e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},65447:e=>{e.exports=require("@restart/ui/Button")},67364:e=>{e.exports=require("@restart/hooks/useCallbackRef")},68455:e=>{e.exports=import("redux-saga")},69722:e=>{e.exports=require("es6-promise")},74075:e=>{e.exports=require("zlib")},74716:e=>{e.exports=require("moment")},74987:e=>{e.exports=require("@restart/ui/ModalManager")},78097:e=>{e.exports=require("next-redux-saga")},78634:e=>{e.exports=require("@restart/ui/Anchor")},81366:e=>{e.exports=require("redux-auth-wrapper/connectedAuthWrapper")},81521:e=>{e.exports=require("dom-helpers/querySelectorAll")},81895:e=>{e.exports=require("@restart/hooks/useEventCallback")},82015:e=>{e.exports=require("react")},86842:e=>{e.exports=require("@restart/ui/SelectableContext")},87571:e=>{e.exports=require("dom-helpers/transitionEnd")},88751:e=>{e.exports=require("next-i18next")},93787:e=>{e.exports=require("redux-persist")},94696:e=>{e.exports=require("@react-google-maps/api")},94947:e=>{e.exports=require("@restart/hooks/useTimeout")},96196:e=>{e.exports=require("next-redux-wrapper")},98320:e=>{e.exports=require("@restart/hooks/useIsomorphicEffect")},99352:(e,r,t)=>{t.a(e,async(e,s)=>{try{t.r(r),t.d(r,{default:()=>A,getStaticProps:()=>j});var a=t(8732),o=t(7082),i=t(83551),n=t(49481),u=t(19918),l=t.n(u),p=t(20156),d=t.n(p),c=t(82015),x=t(20181),m=t(27053),h=t(39982),q=t(53033),y=t(88751),g=t(35576),S=t(28163),f=e([x,h]);async function j({locale:e}){return{props:{...await (0,g.serverSideTranslations)(e,["common"])}}}[x,h]=f.then?(await f)():f;let A=e=>{let{t:r}=(0,y.useTranslation)("common"),[t,s]=(0,c.useState)([]),[u,p]=(0,c.useState)(null),g=()=>(0,a.jsx)(l(),{href:"/operation/[...routes]",as:"/operation/create",children:(0,a.jsx)(d(),{variant:"secondary",size:"sm",children:r("addOperation")})}),f=(0,S.canAddOperation)(()=>(0,a.jsx)(g,{})),j=e=>{p(e)};return(0,a.jsxs)(o.A,{fluid:!0,className:"p-0",children:[(0,a.jsx)(i.A,{children:(0,a.jsx)(n.A,{xs:12,children:(0,a.jsx)(m.A,{title:r("menu.operations")})})}),(0,a.jsx)(i.A,{children:(0,a.jsx)(n.A,{xs:!0,lg:12,children:(0,a.jsx)(q.default,{operations:t})})}),(0,a.jsx)(i.A,{children:(0,a.jsx)(n.A,{xs:!0,lg:12,children:(0,a.jsx)(x.A,{filtreg:e=>j(e),selectedRegions:[],regionHandler:j})})}),(0,a.jsx)(i.A,{children:(0,a.jsx)(n.A,{xs:12,className:"ps-4",children:(0,a.jsx)(f,{})})}),(0,a.jsx)(i.A,{className:"mt-3",children:(0,a.jsx)(n.A,{xs:12,children:(0,a.jsx)(h.default,{selectedRegions:u,setOperations:s})})})]})};s()}catch(e){s(e)}})},99460:e=>{e.exports=require("dom-helpers/css")}};var r=require("../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[6089,9216,9616,2386,5016],()=>t(14446));module.exports=s})();