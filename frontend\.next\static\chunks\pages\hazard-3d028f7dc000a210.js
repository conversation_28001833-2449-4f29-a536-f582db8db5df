(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[1944],{5422:(e,a,t)=>{"use strict";t.d(a,{E:()=>n,M:()=>r});let r=["A","B","C","D","E","F","G","H","I","J","K","L","M","N","O","P","Q","R","S","T","U","V","W","X","Y","Z","Alle"],n=["A","B","C","D","E","F","G","H","I","J","K","L","M","N","O","P","Q","R","S","T","U","V","W","X","Y","Z","All"]},29494:(e,a,t)=>{"use strict";t.r(a),t.d(a,{default:()=>u});var r=t(37876),n=t(49589),i=t(56970),s=t(53538),l=t(29504),o=t(21772),c=t(11041),p=t(31753);let u=e=>{let{filterText:a,onFilter:t}=e,{t:u}=(0,p.Bd)("common");return(0,r.jsx)(n.A,{fluid:!0,className:"p-0",children:(0,r.jsx)(i.A,{children:(0,r.jsxs)(s.A,{children:[(0,r.jsx)(l.A.Control,{className:"rounded",type:"text",placeholder:u("SearchHazards"),value:a,onChange:t}),(0,r.jsx)("div",{className:"search-icon",children:(0,r.jsx)(o.g,{icon:c.MjD})})]})})})}},53538:(e,a,t)=>{"use strict";t.d(a,{A:()=>d});var r=t(15039),n=t.n(r),i=t(14232),s=t(77346),l=t(40856),o=t(20348),c=t(37876);let p=i.forwardRef((e,a)=>{let{className:t,bsPrefix:r,as:i="span",...l}=e;return r=(0,s.oU)(r,"input-group-text"),(0,c.jsx)(i,{ref:a,className:n()(t,r),...l})});p.displayName="InputGroupText";let u=i.forwardRef((e,a)=>{let{bsPrefix:t,size:r,hasValidation:l,className:p,as:u="div",...d}=e;t=(0,s.oU)(t,"input-group");let g=(0,i.useMemo)(()=>({}),[]);return(0,c.jsx)(o.A.Provider,{value:g,children:(0,c.jsx)(u,{ref:a,...d,className:n()(p,t,r&&"".concat(t,"-").concat(r),l&&"has-validation")})})});u.displayName="InputGroup";let d=Object.assign(u,{Text:p,Radio:e=>(0,c.jsx)(p,{children:(0,c.jsx)(l.A,{type:"radio",...e})}),Checkbox:e=>(0,c.jsx)(p,{children:(0,c.jsx)(l.A,{type:"checkbox",...e})})})},55724:(e,a,t)=>{"use strict";t.r(a),t.d(a,{__N_SSG:()=>N,default:()=>P});var r=t(37876),n=t(14232),i=t(91238),s=t.n(i),l=t(48230),o=t.n(l),c=t(33939),p=t(49589),u=t(56970),d=t(37784),g=t(29494),h=t(82851),f=t.n(h),m=t(29504),b=t(60282),v=t(53718),y=t(31753);function C(e){let{t:a}=(0,y.Bd)("common"),{filthaz:t}=e,[i,s]=(0,n.useState)(!0),[l,o]=(0,n.useState)([]),[c,p]=(0,n.useState)([]),u={query:{},limit:"~",sort:{title:"asc"}},d=async e=>{let a=await v.A.get("/hazardtype",e);if(a&&Array.isArray(a.data)){let e=[],r=[];f().each(a.data,(a,t)=>{let n={...a,isChecked:!0};e.push(n),r.push(a._id)}),t(r),p(r),o(e)}};(0,n.useEffect)(()=>{d(u)},[]);let g=e=>{let a=[...l],r=[...c];a.forEach((t,n)=>{t.code===e.target.id&&(a[n].isChecked=e.target.checked,e.target.checked?r.push(t._id):r=r.filter(e=>e!==t._id))}),p(r),t(r),s(!1),o(a)};return(0,r.jsxs)("div",{className:"hazards-multi-checkboxes",children:[(0,r.jsx)(m.A.Check,{type:"checkbox",id:"all",checked:i,label:a("Events.forms.AllHazards"),onChange:e=>{let a=l.map(a=>({...a,isChecked:e.target.checked})),r=[];e.target.checked&&(r=a.map(e=>e._id)),t(r),p(r),s(e.target.checked),o(a)}}),l.map((e,a)=>(0,r.jsx)(m.A.Check,{type:"checkbox",id:e.code,label:e.title,value:e.code,onChange:g,checked:l[a].isChecked},a)),(0,r.jsx)(b.A,{onClick:()=>{let e=l.map(e=>({...e,isChecked:!1}));p([]),s(!1),o(e),t([])},className:"btn-plain ps-2",children:a("ClearAll")})]})}C.defaultProps={filthaz:()=>{}};var x=t(5422),k=t(69600),N=!0;let P=()=>{let{t:e,i18n:a}=(0,y.Bd)("common"),t="fr"===a.language?"en":a.language,i="en"===t?x.E:x.M,[l]=(0,n.useState)(i),[h,m]=(0,n.useState)([]),[b,N]=(0,n.useState)([]),[P,L]=(0,n.useState)([]),[w,A]=(0,n.useState)(0),[j,E]=(0,n.useState)(1),[O]=(0,n.useState)(50),[R,_]=(0,n.useState)("All"),[S,T]=(0,n.useState)(!0),[B,z]=(0,n.useState)([]),[D,q]=(0,n.useState)(""),[I]=(0,n.useState)(t?"title.".concat(t):"title.en"),[H,M]=(0,n.useState)(!0),[U,J]=(0,n.useState)({}),F=async e=>{await T(!0);let a=await v.A.get("/hazard",e);a&&a.data&&(await T(!1),await N(a.data),await A(a.totalCount))},K=async(a,r)=>{if(await _(l[r]),l[r]===e("All")||l[r]===e("Alle")){if(M(!0),await L([]),Array.isArray(B)&&0!==B.length){let e={query:{},limit:O,page:j,sort:{[I]:"asc"}};(async()=>{e.query={hazard_type:B},e.page=1;let a=await v.A.get("/hazard",e);await m(a.data),await A(a.totalCount)})()}}else{let e=[];q("");let a=l[r].toLowerCase();M(!1),b.map(async(r,n)=>{if(r.title[t].toLowerCase().split("")[0]===a&&r.enabled){let a={title:r.title,_id:r._id,coordinates:r.coordinates};e.push(a)}}),e.length>=1?(await L(e),A(e.length)):(await L([]),await m([]),await A([]),await J({}))}},G=async e=>{let a=e.selected+1;if(await E(a),Array.isArray(B)&&0!==B.length){let e={query:{},limit:O,page:a,sort:{[I]:"asc"}};(async()=>{e.query={hazard_type:B};let a=await v.A.get("/hazard",e);await m(a.data),await A(a.totalCount),await J(a)})()}else{let e=async()=>{m((await v.A.get("/hazard",t)).data)},t={query:{},limit:O,page:a,sort:{[I]:"asc"}};await e()}},V=async e=>{if(M(!0),Array.isArray(e)&&0===e.length){await m([]),await A(0),await _("All"),await J({}),await N([]),L([]),z([]);return}F({query:{hazard_type:e},limit:"~",sort:{[I]:"asc"}}),z(e);let a=[],t={query:{},limit:O,page:j,sort:{[I]:"asc"}};(async()=>{t.sort={[I]:"asc"},t.query={hazard_type:e},t.page=1;let r=await v.A.get("/hazard",t);a=r.data,await m(a),await A(r.totalCount),await _("All"),await J(r),L([])})()},W={query:{},sort:{[I]:"asc"}},Z=async()=>{m((await v.A.get("/hazard",W)).data)},X=(e,a)=>{e?W.query={[I]:e&&e[0].toUpperCase()+e.slice(1).toLowerCase()}:W.query={},"ACTIVE"!==R&&(W.query={...W.query,hazard_type:a}),Z()},Y=(0,n.useRef)(f().debounce((e,a)=>X(e,a),Number("500")||300)).current;return(0,r.jsxs)(p.A,{fluid:!0,className:"p-0",children:[(0,r.jsx)(u.A,{children:(0,r.jsx)(d.A,{xs:12,children:(0,r.jsx)(k.A,{title:e("menu.hazards")})})}),(0,r.jsxs)("div",{className:"hazard-image-block",children:[(0,r.jsx)("img",{className:"hazard-image-cover",src:"/images/hazard.838eccb4.jpg",alt:"Logo"}),(0,r.jsx)(C,{currentLang:t,filterByletter:R,filthaz:V})]}),(0,r.jsxs)("div",{className:"alphabetBlock",children:[(0,r.jsx)("div",{className:"alphabetContainer",children:l.map((e,a)=>(0,r.jsx)("span",{className:"alphabetItems ".concat(R===e?"active":""),onClick:e=>K(e,a),children:e},a))}),H&&(0,r.jsx)("div",{children:(0,r.jsx)(u.A,{children:(0,r.jsx)(d.A,{className:"mt-3 mx-3",children:(0,r.jsx)(g.default,{onFilter:e=>{q(e.target.value),Y(e.target.value,B)},filterText:D})})})}),(0,r.jsx)("div",{className:"alphabetLists",children:S?(0,r.jsx)(c.A,{animation:"border",variant:"primary"}):0!==P.length?P.map((e,a)=>(0,r.jsx)("li",{className:"alphaListItems clearfix",children:(0,r.jsx)(o(),{href:"/hazard/[...routes]",as:"/hazard/show/".concat(e._id),children:e.title&&e.title[t]?e.title[t]:""})},a)):h.length>=1?(0,r.jsx)("ul",{children:h.map((e,a)=>e.enabled?(0,r.jsx)("li",{className:"alphaListItems clearfix",children:(0,r.jsx)(o(),{href:"/hazard/[...routes]",as:"/hazard/show/".concat(e._id),children:e.title&&e.title[t]?e.title[t]:""})},a):null)}):(0,r.jsxs)("div",{className:"noresultFound",children:[" ",e("Noresultsfound")," "]})}),w>O&&!D?(0,r.jsx)("div",{className:"hazards-pagination",children:(0,r.jsx)(s(),{pageCount:Math.ceil(U.totalCount/U.limit),pageRangeDisplayed:5,marginPagesDisplayed:2,onPageChange:G,forcePage:U.page-1,containerClassName:"pagination",pageClassName:"page-item",pageLinkClassName:"page-link",previousClassName:"page-item",previousLinkClassName:"page-link",nextClassName:"page-item",nextLinkClassName:"page-link",activeClassName:"active",disabledClassName:"disabled",previousLabel:"‹",nextLabel:"›"})}):null]})]})}},69600:(e,a,t)=>{"use strict";t.d(a,{A:()=>n});var r=t(37876);function n(e){return(0,r.jsx)("h2",{className:"page-heading",children:e.title})}},90735:(e,a,t)=>{(window.__NEXT_P=window.__NEXT_P||[]).push(["/hazard",function(){return t(55724)}])},91238:function(e,a,t){let r;r=t(14232),e.exports=(()=>{var e={703:(e,a,t)=>{"use strict";var r=t(414);function n(){}function i(){}i.resetWarningCache=n,e.exports=function(){function e(e,a,t,n,i,s){if(s!==r){var l=Error("Calling PropTypes validators directly is not supported by the `prop-types` package. Use PropTypes.checkPropTypes() to call them. Read more at http://fb.me/use-check-prop-types");throw l.name="Invariant Violation",l}}function a(){return e}e.isRequired=e;var t={array:e,bigint:e,bool:e,func:e,number:e,object:e,string:e,symbol:e,any:e,arrayOf:a,element:e,elementType:e,instanceOf:a,node:e,objectOf:a,oneOf:a,oneOfType:a,shape:a,exact:a,checkPropTypes:i,resetWarningCache:n};return t.PropTypes=t,t}},697:(e,a,t)=>{e.exports=t(703)()},414:e=>{"use strict";e.exports="SECRET_DO_NOT_PASS_THIS_OR_YOU_WILL_BE_FIRED"},98:e=>{"use strict";e.exports=r}},a={};function t(r){var n=a[r];if(void 0!==n)return n.exports;var i=a[r]={exports:{}};return e[r](i,i.exports,t),i.exports}t.n=e=>{var a=e&&e.__esModule?()=>e.default:()=>e;return t.d(a,{a}),a},t.d=(e,a)=>{for(var r in a)t.o(a,r)&&!t.o(e,r)&&Object.defineProperty(e,r,{enumerable:!0,get:a[r]})},t.o=(e,a)=>Object.prototype.hasOwnProperty.call(e,a),t.r=e=>{"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})};var n={};return(()=>{"use strict";t.r(n),t.d(n,{default:()=>v});var e=t(98),a=t.n(e),r=t(697),i=t.n(r);function s(){return(s=Object.assign?Object.assign.bind():function(e){for(var a=1;a<arguments.length;a++){var t=arguments[a];for(var r in t)Object.prototype.hasOwnProperty.call(t,r)&&(e[r]=t[r])}return e}).apply(this,arguments)}var l=function(e){var t=e.pageClassName,r=e.pageLinkClassName,n=e.page,i=e.selected,l=e.activeClassName,o=e.activeLinkClassName,c=e.getEventListener,p=e.pageSelectedHandler,u=e.href,d=e.extraAriaContext,g=e.pageLabelBuilder,h=e.rel,f=e.ariaLabel||"Page "+n+(d?" "+d:""),m=null;return i&&(m="page",f=e.ariaLabel||"Page "+n+" is your current page",t=void 0!==t?t+" "+l:l,void 0!==r?void 0!==o&&(r=r+" "+o):r=o),a().createElement("li",{className:t},a().createElement("a",s({rel:h,role:u?void 0:"button",className:r,href:u,tabIndex:i?"-1":"0","aria-label":f,"aria-current":m,onKeyPress:p},c(p)),g(n)))};function o(){return(o=Object.assign?Object.assign.bind():function(e){for(var a=1;a<arguments.length;a++){var t=arguments[a];for(var r in t)Object.prototype.hasOwnProperty.call(t,r)&&(e[r]=t[r])}return e}).apply(this,arguments)}l.propTypes={pageSelectedHandler:i().func.isRequired,selected:i().bool.isRequired,pageClassName:i().string,pageLinkClassName:i().string,activeClassName:i().string,activeLinkClassName:i().string,extraAriaContext:i().string,href:i().string,ariaLabel:i().string,page:i().number.isRequired,getEventListener:i().func.isRequired,pageLabelBuilder:i().func.isRequired,rel:i().string};var c=function(e){var t=e.breakLabel,r=e.breakAriaLabel,n=e.breakClassName,i=e.breakLinkClassName,s=e.breakHandler,l=e.getEventListener;return a().createElement("li",{className:n||"break"},a().createElement("a",o({className:i,role:"button",tabIndex:"0","aria-label":r,onKeyPress:s},l(s)),t))};function p(e){var a=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"";return null!=e?e:a}function u(e){return(u="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function d(){return(d=Object.assign?Object.assign.bind():function(e){for(var a=1;a<arguments.length;a++){var t=arguments[a];for(var r in t)Object.prototype.hasOwnProperty.call(t,r)&&(e[r]=t[r])}return e}).apply(this,arguments)}function g(e,a){return(g=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,a){return e.__proto__=a,e})(e,a)}function h(e){if(void 0===e)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function f(e){return(f=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)})(e)}function m(e,a,t){return a in e?Object.defineProperty(e,a,{value:t,enumerable:!0,configurable:!0,writable:!0}):e[a]=t,e}c.propTypes={breakLabel:i().oneOfType([i().string,i().node]),breakAriaLabel:i().string,breakClassName:i().string,breakLinkClassName:i().string,breakHandler:i().func.isRequired,getEventListener:i().func.isRequired};var b=function(e){if("function"!=typeof e&&null!==e)throw TypeError("Super expression must either be null or a function");i.prototype=Object.create(e&&e.prototype,{constructor:{value:i,writable:!0,configurable:!0}}),Object.defineProperty(i,"prototype",{writable:!1}),e&&g(i,e);var t,r,n=(r=function(){if("undefined"==typeof Reflect||!Reflect.construct||Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){})),!0}catch(e){return!1}}(),function(){var e,a=f(i);if(e=r?Reflect.construct(a,arguments,f(this).constructor):a.apply(this,arguments),e&&("object"===u(e)||"function"==typeof e))return e;if(void 0!==e)throw TypeError("Derived constructors may only return object or undefined");return h(this)});function i(e){var t;return function(e,a){if(!(e instanceof a))throw TypeError("Cannot call a class as a function")}(this,i),m(h(t=n.call(this,e)),"handlePreviousPage",function(e){var a=t.state.selected;t.handleClick(e,null,a>0?a-1:void 0,{isPrevious:!0})}),m(h(t),"handleNextPage",function(e){var a=t.state.selected,r=t.props.pageCount;t.handleClick(e,null,a<r-1?a+1:void 0,{isNext:!0})}),m(h(t),"handlePageSelected",function(e,a){if(t.state.selected===e)return t.callActiveCallback(e),void t.handleClick(a,null,void 0,{isActive:!0});t.handleClick(a,null,e)}),m(h(t),"handlePageChange",function(e){t.state.selected!==e&&(t.setState({selected:e}),t.callCallback(e))}),m(h(t),"getEventListener",function(e){return m({},t.props.eventListener,e)}),m(h(t),"handleClick",function(e,a,r){var n=arguments.length>3&&void 0!==arguments[3]?arguments[3]:{},i=n.isPrevious,s=n.isNext,l=n.isBreak,o=n.isActive;e.preventDefault?e.preventDefault():e.returnValue=!1;var c=t.state.selected,p=t.props.onClick,u=r;if(p){var d=p({index:a,selected:c,nextSelectedPage:r,event:e,isPrevious:void 0!==i&&i,isNext:void 0!==s&&s,isBreak:void 0!==l&&l,isActive:void 0!==o&&o});if(!1===d)return;Number.isInteger(d)&&(u=d)}void 0!==u&&t.handlePageChange(u)}),m(h(t),"handleBreakClick",function(e,a){var r=t.state.selected;t.handleClick(a,e,r<e?t.getForwardJump():t.getBackwardJump(),{isBreak:!0})}),m(h(t),"callCallback",function(e){void 0!==t.props.onPageChange&&"function"==typeof t.props.onPageChange&&t.props.onPageChange({selected:e})}),m(h(t),"callActiveCallback",function(e){void 0!==t.props.onPageActive&&"function"==typeof t.props.onPageActive&&t.props.onPageActive({selected:e})}),m(h(t),"getElementPageRel",function(e){var a=t.state.selected,r=t.props,n=r.nextPageRel,i=r.prevPageRel,s=r.selectedPageRel;return a-1===e?i:a===e?s:a+1===e?n:void 0}),m(h(t),"pagination",function(){var e=[],r=t.props,n=r.pageRangeDisplayed,i=r.pageCount,s=r.marginPagesDisplayed,l=r.breakLabel,o=r.breakClassName,p=r.breakLinkClassName,u=r.breakAriaLabels,d=t.state.selected;if(i<=n)for(var g=0;g<i;g++)e.push(t.getPageElement(g));else{var h=n/2,f=n-h;d>i-n/2?h=n-(f=i-d):d<n/2&&(f=n-(h=d));var m,b,v=function(e){return t.getPageElement(e)},y=[];for(m=0;m<i;m++){var C=m+1;if(C<=s)y.push({type:"page",index:m,display:v(m)});else if(C>i-s)y.push({type:"page",index:m,display:v(m)});else if(m>=d-h&&m<=d+(0===d&&n>1?f-1:f))y.push({type:"page",index:m,display:v(m)});else if(l&&y.length>0&&y[y.length-1].display!==b&&(n>0||s>0)){var x=m<d?u.backward:u.forward;b=a().createElement(c,{key:m,breakAriaLabel:x,breakLabel:l,breakClassName:o,breakLinkClassName:p,breakHandler:t.handleBreakClick.bind(null,m),getEventListener:t.getEventListener}),y.push({type:"break",index:m,display:b})}}y.forEach(function(a,t){var r=a;"break"===a.type&&y[t-1]&&"page"===y[t-1].type&&y[t+1]&&"page"===y[t+1].type&&y[t+1].index-y[t-1].index<=2&&(r={type:"page",index:a.index,display:v(a.index)}),e.push(r.display)})}return e}),void 0!==e.initialPage&&void 0!==e.forcePage&&console.warn("(react-paginate): Both initialPage (".concat(e.initialPage,") and forcePage (").concat(e.forcePage,") props are provided, which is discouraged.")+" Use exclusively forcePage prop for a controlled component.\nSee https://reactjs.org/docs/forms.html#controlled-components"),t.state={selected:e.initialPage?e.initialPage:e.forcePage?e.forcePage:0},t}return t=[{key:"componentDidMount",value:function(){var e=this.props,a=e.initialPage,t=e.disableInitialCallback,r=e.extraAriaContext,n=e.pageCount,i=e.forcePage;void 0===a||t||this.callCallback(a),r&&console.warn("DEPRECATED (react-paginate): The extraAriaContext prop is deprecated. You should now use the ariaLabelBuilder instead."),Number.isInteger(n)||console.warn("(react-paginate): The pageCount prop value provided is not an integer (".concat(n,"). Did you forget a Math.ceil()?")),void 0!==a&&a>n-1&&console.warn("(react-paginate): The initialPage prop provided is greater than the maximum page index from pageCount prop (".concat(a," > ").concat(n-1,").")),void 0!==i&&i>n-1&&console.warn("(react-paginate): The forcePage prop provided is greater than the maximum page index from pageCount prop (".concat(i," > ").concat(n-1,")."))}},{key:"componentDidUpdate",value:function(e){void 0!==this.props.forcePage&&this.props.forcePage!==e.forcePage&&(this.props.forcePage>this.props.pageCount-1&&console.warn("(react-paginate): The forcePage prop provided is greater than the maximum page index from pageCount prop (".concat(this.props.forcePage," > ").concat(this.props.pageCount-1,").")),this.setState({selected:this.props.forcePage})),Number.isInteger(e.pageCount)&&!Number.isInteger(this.props.pageCount)&&console.warn("(react-paginate): The pageCount prop value provided is not an integer (".concat(this.props.pageCount,"). Did you forget a Math.ceil()?"))}},{key:"getForwardJump",value:function(){var e=this.state.selected,a=this.props,t=a.pageCount,r=e+a.pageRangeDisplayed;return r>=t?t-1:r}},{key:"getBackwardJump",value:function(){var e=this.state.selected-this.props.pageRangeDisplayed;return e<0?0:e}},{key:"getElementHref",value:function(e){var a=this.props,t=a.hrefBuilder,r=a.pageCount,n=a.hrefAllControls;if(t)return n||e>=0&&e<r?t(e+1,r,this.state.selected):void 0}},{key:"ariaLabelBuilder",value:function(e){var a=e===this.state.selected;if(this.props.ariaLabelBuilder&&e>=0&&e<this.props.pageCount){var t=this.props.ariaLabelBuilder(e+1,a);return this.props.extraAriaContext&&!a&&(t=t+" "+this.props.extraAriaContext),t}}},{key:"getPageElement",value:function(e){var t=this.state.selected,r=this.props,n=r.pageClassName,i=r.pageLinkClassName,s=r.activeClassName,o=r.activeLinkClassName,c=r.extraAriaContext,p=r.pageLabelBuilder;return a().createElement(l,{key:e,pageSelectedHandler:this.handlePageSelected.bind(null,e),selected:t===e,rel:this.getElementPageRel(e),pageClassName:n,pageLinkClassName:i,activeClassName:s,activeLinkClassName:o,extraAriaContext:c,href:this.getElementHref(e),ariaLabel:this.ariaLabelBuilder(e),page:e+1,pageLabelBuilder:p,getEventListener:this.getEventListener})}},{key:"render",value:function(){var e=this.props.renderOnZeroPageCount;if(0===this.props.pageCount&&void 0!==e)return e?e(this.props):e;var t=this.props,r=t.disabledClassName,n=t.disabledLinkClassName,i=t.pageCount,s=t.className,l=t.containerClassName,o=t.previousLabel,c=t.previousClassName,u=t.previousLinkClassName,g=t.previousAriaLabel,h=t.prevRel,f=t.nextLabel,m=t.nextClassName,b=t.nextLinkClassName,v=t.nextAriaLabel,y=t.nextRel,C=this.state.selected,x=0===C,k=C===i-1,N="".concat(p(c)).concat(x?" ".concat(p(r)):""),P="".concat(p(m)).concat(k?" ".concat(p(r)):""),L="".concat(p(u)).concat(x?" ".concat(p(n)):""),w="".concat(p(b)).concat(k?" ".concat(p(n)):"");return a().createElement("ul",{className:s||l,role:"navigation","aria-label":"Pagination"},a().createElement("li",{className:N},a().createElement("a",d({className:L,href:this.getElementHref(C-1),tabIndex:x?"-1":"0",role:"button",onKeyPress:this.handlePreviousPage,"aria-disabled":x?"true":"false","aria-label":g,rel:h},this.getEventListener(this.handlePreviousPage)),o)),this.pagination(),a().createElement("li",{className:P},a().createElement("a",d({className:w,href:this.getElementHref(C+1),tabIndex:k?"-1":"0",role:"button",onKeyPress:this.handleNextPage,"aria-disabled":k?"true":"false","aria-label":v,rel:y},this.getEventListener(this.handleNextPage)),f)))}}],function(e,a){for(var t=0;t<a.length;t++){var r=a[t];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,r.key,r)}}(i.prototype,t),Object.defineProperty(i,"prototype",{writable:!1}),i}(e.Component);m(b,"propTypes",{pageCount:i().number.isRequired,pageRangeDisplayed:i().number,marginPagesDisplayed:i().number,previousLabel:i().node,previousAriaLabel:i().string,prevPageRel:i().string,prevRel:i().string,nextLabel:i().node,nextAriaLabel:i().string,nextPageRel:i().string,nextRel:i().string,breakLabel:i().oneOfType([i().string,i().node]),breakAriaLabels:i().shape({forward:i().string,backward:i().string}),hrefBuilder:i().func,hrefAllControls:i().bool,onPageChange:i().func,onPageActive:i().func,onClick:i().func,initialPage:i().number,forcePage:i().number,disableInitialCallback:i().bool,containerClassName:i().string,className:i().string,pageClassName:i().string,pageLinkClassName:i().string,pageLabelBuilder:i().func,activeClassName:i().string,activeLinkClassName:i().string,previousClassName:i().string,nextClassName:i().string,previousLinkClassName:i().string,nextLinkClassName:i().string,disabledClassName:i().string,disabledLinkClassName:i().string,breakClassName:i().string,breakLinkClassName:i().string,extraAriaContext:i().string,ariaLabelBuilder:i().func,eventListener:i().string,renderOnZeroPageCount:i().func,selectedPageRel:i().string}),m(b,"defaultProps",{pageRangeDisplayed:2,marginPagesDisplayed:3,activeClassName:"selected",previousLabel:"Previous",previousClassName:"previous",previousAriaLabel:"Previous page",prevPageRel:"prev",prevRel:"prev",nextLabel:"Next",nextClassName:"next",nextAriaLabel:"Next page",nextPageRel:"next",nextRel:"next",breakLabel:"...",breakAriaLabels:{forward:"Jump forward",backward:"Jump backward"},disabledClassName:"disabled",disableInitialCallback:!1,pageLabelBuilder:function(e){return e},eventListener:"onClick",renderOnZeroPageCount:void 0,selectedPageRel:"canonical",hrefAllControls:!1});let v=b})(),n})()}},e=>{var a=a=>e(e.s=a);e.O(0,[7725,1772,636,6593,8792],()=>a(90735)),_N_E=e.O()}]);
//# sourceMappingURL=hazard-3d028f7dc000a210.js.map