(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[2202],{29335:(e,a,t)=>{"use strict";t.d(a,{A:()=>g});var r=t(15039),n=t.n(r),l=t(14232),s=t(77346),i=t(37876);let c=l.forwardRef((e,a)=>{let{className:t,bsPrefix:r,as:l="div",...c}=e;return r=(0,s.oU)(r,"card-body"),(0,i.jsx)(l,{ref:a,className:n()(t,r),...c})});c.displayName="CardBody";let o=l.forwardRef((e,a)=>{let{className:t,bsPrefix:r,as:l="div",...c}=e;return r=(0,s.oU)(r,"card-footer"),(0,i.jsx)(l,{ref:a,className:n()(t,r),...c})});o.displayName="CardFooter";var d=t(81764);let u=l.forwardRef((e,a)=>{let{bsPrefix:t,className:r,as:c="div",...o}=e,u=(0,s.oU)(t,"card-header"),m=(0,l.useMemo)(()=>({cardHeaderBsPrefix:u}),[u]);return(0,i.jsx)(d.A.Provider,{value:m,children:(0,i.jsx)(c,{ref:a,...o,className:n()(r,u)})})});u.displayName="CardHeader";let m=l.forwardRef((e,a)=>{let{bsPrefix:t,className:r,variant:l,as:c="img",...o}=e,d=(0,s.oU)(t,"card-img");return(0,i.jsx)(c,{ref:a,className:n()(l?"".concat(d,"-").concat(l):d,r),...o})});m.displayName="CardImg";let f=l.forwardRef((e,a)=>{let{className:t,bsPrefix:r,as:l="div",...c}=e;return r=(0,s.oU)(r,"card-img-overlay"),(0,i.jsx)(l,{ref:a,className:n()(t,r),...c})});f.displayName="CardImgOverlay";let h=l.forwardRef((e,a)=>{let{className:t,bsPrefix:r,as:l="a",...c}=e;return r=(0,s.oU)(r,"card-link"),(0,i.jsx)(l,{ref:a,className:n()(t,r),...c})});h.displayName="CardLink";var p=t(46052);let v=(0,p.A)("h6"),y=l.forwardRef((e,a)=>{let{className:t,bsPrefix:r,as:l=v,...c}=e;return r=(0,s.oU)(r,"card-subtitle"),(0,i.jsx)(l,{ref:a,className:n()(t,r),...c})});y.displayName="CardSubtitle";let N=l.forwardRef((e,a)=>{let{className:t,bsPrefix:r,as:l="p",...c}=e;return r=(0,s.oU)(r,"card-text"),(0,i.jsx)(l,{ref:a,className:n()(t,r),...c})});N.displayName="CardText";let _=(0,p.A)("h5"),x=l.forwardRef((e,a)=>{let{className:t,bsPrefix:r,as:l=_,...c}=e;return r=(0,s.oU)(r,"card-title"),(0,i.jsx)(l,{ref:a,className:n()(t,r),...c})});x.displayName="CardTitle";let M=l.forwardRef((e,a)=>{let{bsPrefix:t,className:r,bg:l,text:o,border:d,body:u=!1,children:m,as:f="div",...h}=e,p=(0,s.oU)(t,"card");return(0,i.jsx)(f,{ref:a,...h,className:n()(r,p,l&&"bg-".concat(l),o&&"text-".concat(o),d&&"border-".concat(d)),children:u?(0,i.jsx)(c,{children:m}):m})});M.displayName="Card";let g=Object.assign(M,{Img:m,Title:x,Subtitle:y,Body:c,Link:h,Text:N,Header:u,Footer:o,ImgOverlay:f})},50650:(e,a,t)=>{"use strict";t.d(a,{A:()=>M});var r=t(76959),n=t(14232);let l=function(e,a){let t=(0,n.useRef)(!0);(0,n.useEffect)(()=>{if(t.current){t.current=!1;return}return e()},a)};var s=t(84467),i=t(55987),c=t(10401),o=t(15039),d=t.n(o),u=t(22631),m=t(77346),f=t(37876);let h=n.forwardRef((e,a)=>{let{className:t,bsPrefix:r,as:n="div",...l}=e;return r=(0,m.oU)(r,"carousel-caption"),(0,f.jsx)(n,{ref:a,className:d()(t,r),...l})});h.displayName="CarouselCaption";let p=n.forwardRef((e,a)=>{let{as:t="div",bsPrefix:r,className:n,...l}=e,s=d()(n,(0,m.oU)(r,"carousel-item"));return(0,f.jsx)(t,{ref:a,...l,className:s})});p.displayName="CarouselItem";var v=t(49285),y=t(66270),N=t(79043),_=t(56640);let x=n.forwardRef((e,a)=>{let t,{defaultActiveIndex:o=0,...h}=e,{as:p="div",bsPrefix:x,slide:M=!0,fade:g=!1,controls:C=!0,indicators:w=!0,indicatorLabels:b=[],activeIndex:j,onSelect:k,onSlide:S,onSlid:L,interval:A=5e3,keyboard:T=!0,onKeyDown:D,pause:R="hover",onMouseOver:U,onMouseOut:Y,wrap:E=!0,touch:H=!0,onTouchStart:J,onTouchMove:I,onTouchEnd:O,prevIcon:F=(0,f.jsx)("span",{"aria-hidden":"true",className:"carousel-control-prev-icon"}),prevLabel:z="Previous",nextIcon:P=(0,f.jsx)("span",{"aria-hidden":"true",className:"carousel-control-next-icon"}),nextLabel:W="Next",variant:B,className:X,children:K,...Z}=(0,u.Zw)({defaultActiveIndex:o,...h},{activeIndex:"onSelect"}),q=(0,m.oU)(x,"carousel"),G=(0,m.Wz)(),Q=(0,n.useRef)(null),[V,$]=(0,n.useState)("next"),[ee,ea]=(0,n.useState)(!1),[et,er]=(0,n.useState)(!1),[en,el]=(0,n.useState)(j||0);(0,n.useEffect)(()=>{et||j===en||(Q.current?$(Q.current):$((j||0)>en?"next":"prev"),M&&er(!0),el(j||0))},[j,et,en,M]),(0,n.useEffect)(()=>{Q.current&&(Q.current=null)});let es=0;(0,v.jJ)(K,(e,a)=>{++es,a===j&&(t=e.props.interval)});let ei=(0,s.A)(t),ec=(0,n.useCallback)(e=>{if(et)return;let a=en-1;if(a<0){if(!E)return;a=es-1}Q.current="prev",null==k||k(a,e)},[et,en,k,E,es]),eo=(0,r.A)(e=>{if(et)return;let a=en+1;if(a>=es){if(!E)return;a=0}Q.current="next",null==k||k(a,e)}),ed=(0,n.useRef)();(0,n.useImperativeHandle)(a,()=>({element:ed.current,prev:ec,next:eo}));let eu=(0,r.A)(()=>{!document.hidden&&function(e){if(!e||!e.style||!e.parentNode||!e.parentNode.style)return!1;let a=getComputedStyle(e);return"none"!==a.display&&"hidden"!==a.visibility&&"none"!==getComputedStyle(e.parentNode).display}(ed.current)&&(G?ec():eo())}),em="next"===V?"start":"end";l(()=>{M||(null==S||S(en,em),null==L||L(en,em))},[en]);let ef="".concat(q,"-item-").concat(V),eh="".concat(q,"-item-").concat(em),ep=(0,n.useCallback)(e=>{(0,N.A)(e),null==S||S(en,em)},[S,en,em]),ev=(0,n.useCallback)(()=>{er(!1),null==L||L(en,em)},[L,en,em]),ey=(0,n.useCallback)(e=>{if(T&&!/input|textarea/i.test(e.target.tagName))switch(e.key){case"ArrowLeft":e.preventDefault(),G?eo(e):ec(e);return;case"ArrowRight":e.preventDefault(),G?ec(e):eo(e);return}null==D||D(e)},[T,D,ec,eo,G]),eN=(0,n.useCallback)(e=>{"hover"===R&&ea(!0),null==U||U(e)},[R,U]),e_=(0,n.useCallback)(e=>{ea(!1),null==Y||Y(e)},[Y]),ex=(0,n.useRef)(0),eM=(0,n.useRef)(0),eg=(0,i.A)(),eC=(0,n.useCallback)(e=>{ex.current=e.touches[0].clientX,eM.current=0,"hover"===R&&ea(!0),null==J||J(e)},[R,J]),ew=(0,n.useCallback)(e=>{e.touches&&e.touches.length>1?eM.current=0:eM.current=e.touches[0].clientX-ex.current,null==I||I(e)},[I]),eb=(0,n.useCallback)(e=>{if(H){let a=eM.current;Math.abs(a)>40&&(a>0?ec(e):eo(e))}"hover"===R&&eg.set(()=>{ea(!1)},A||void 0),null==O||O(e)},[H,R,ec,eo,eg,A,O]),ej=null!=A&&!ee&&!et,ek=(0,n.useRef)();(0,n.useEffect)(()=>{var e,a;if(!ej)return;let t=G?ec:eo;return ek.current=window.setInterval(document.visibilityState?eu:t,null!=(e=null!=(a=ei.current)?a:A)?e:void 0),()=>{null!==ek.current&&clearInterval(ek.current)}},[ej,ec,eo,ei,A,eu,G]);let eS=(0,n.useMemo)(()=>w&&Array.from({length:es},(e,a)=>e=>{null==k||k(a,e)}),[w,es,k]);return(0,f.jsxs)(p,{ref:ed,...Z,onKeyDown:ey,onMouseOver:eN,onMouseOut:e_,onTouchStart:eC,onTouchMove:ew,onTouchEnd:eb,className:d()(X,q,M&&"slide",g&&"".concat(q,"-fade"),B&&"".concat(q,"-").concat(B)),children:[w&&(0,f.jsx)("div",{className:"".concat(q,"-indicators"),children:(0,v.Tj)(K,(e,a)=>(0,f.jsx)("button",{type:"button","data-bs-target":"","aria-label":null!=b&&b.length?b[a]:"Slide ".concat(a+1),className:a===en?"active":void 0,onClick:eS?eS[a]:void 0,"aria-current":a===en},a))}),(0,f.jsx)("div",{className:"".concat(q,"-inner"),children:(0,v.Tj)(K,(e,a)=>{let t=a===en;return M?(0,f.jsx)(_.A,{in:t,onEnter:t?ep:void 0,onEntered:t?ev:void 0,addEndListener:y.A,children:(a,r)=>n.cloneElement(e,{...r,className:d()(e.props.className,t&&"entered"!==a&&ef,("entered"===a||"exiting"===a)&&"active",("entering"===a||"exiting"===a)&&eh)})}):n.cloneElement(e,{className:d()(e.props.className,t&&"active")})})}),C&&(0,f.jsxs)(f.Fragment,{children:[(E||0!==j)&&(0,f.jsxs)(c.A,{className:"".concat(q,"-control-prev"),onClick:ec,children:[F,z&&(0,f.jsx)("span",{className:"visually-hidden",children:z})]}),(E||j!==es-1)&&(0,f.jsxs)(c.A,{className:"".concat(q,"-control-next"),onClick:eo,children:[P,W&&(0,f.jsx)("span",{className:"visually-hidden",children:W})]})]})]})});x.displayName="Carousel";let M=Object.assign(x,{Caption:h,Item:p})},81764:(e,a,t)=>{"use strict";t.d(a,{A:()=>n});let r=t(14232).createContext(null);r.displayName="CardHeaderContext";let n=r},84135:function(e,a,t){(function(e){"use strict";function a(e,a,t,r){var n={m:["eine Minute","einer Minute"],h:["eine Stunde","einer Stunde"],d:["ein Tag","einem Tag"],dd:[e+" Tage",e+" Tagen"],w:["eine Woche","einer Woche"],M:["ein Monat","einem Monat"],MM:[e+" Monate",e+" Monaten"],y:["ein Jahr","einem Jahr"],yy:[e+" Jahre",e+" Jahren"]};return a?n[t][0]:n[t][1]}e.defineLocale("de",{months:"Januar_Februar_M\xe4rz_April_Mai_Juni_Juli_August_September_Oktober_November_Dezember".split("_"),monthsShort:"Jan._Feb._M\xe4rz_Apr._Mai_Juni_Juli_Aug._Sep._Okt._Nov._Dez.".split("_"),monthsParseExact:!0,weekdays:"Sonntag_Montag_Dienstag_Mittwoch_Donnerstag_Freitag_Samstag".split("_"),weekdaysShort:"So._Mo._Di._Mi._Do._Fr._Sa.".split("_"),weekdaysMin:"So_Mo_Di_Mi_Do_Fr_Sa".split("_"),weekdaysParseExact:!0,longDateFormat:{LT:"HH:mm",LTS:"HH:mm:ss",L:"DD.MM.YYYY",LL:"D. MMMM YYYY",LLL:"D. MMMM YYYY HH:mm",LLLL:"dddd, D. MMMM YYYY HH:mm"},calendar:{sameDay:"[heute um] LT [Uhr]",sameElse:"L",nextDay:"[morgen um] LT [Uhr]",nextWeek:"dddd [um] LT [Uhr]",lastDay:"[gestern um] LT [Uhr]",lastWeek:"[letzten] dddd [um] LT [Uhr]"},relativeTime:{future:"in %s",past:"vor %s",s:"ein paar Sekunden",ss:"%d Sekunden",m:a,mm:"%d Minuten",h:a,hh:"%d Stunden",d:a,dd:a,w:a,ww:"%d Wochen",M:a,MM:a,y:a,yy:a},dayOfMonthOrdinalParse:/\d{1,2}\./,ordinal:"%d.",week:{dow:1,doy:4}})})(t(10841))}}]);
//# sourceMappingURL=2202-3295a537956513f5.js.map