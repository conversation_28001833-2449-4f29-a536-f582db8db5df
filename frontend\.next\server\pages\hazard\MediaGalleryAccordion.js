(()=>{var e={};e.id=8213,e.ids=[636,3220,8213],e.modules={123:e=>{"use strict";e.exports=require("dom-helpers/removeEventListener")},1332:e=>{"use strict";e.exports=require("react-custom-scrollbars-2")},1428:e=>{"use strict";e.exports=import("axios")},1680:e=>{"use strict";e.exports=require("@restart/ui/utils")},1919:e=>{"use strict";e.exports=require("react-transition-group/Transition")},3892:e=>{"use strict";e.exports=require("classnames")},4048:e=>{"use strict";e.exports=require("react-truncate")},6009:e=>{"use strict";e.exports=require("dom-helpers/ownerDocument")},6952:e=>{"use strict";e.exports=require("@restart/ui/Modal")},7374:e=>{"use strict";e.exports=require("@restart/ui/Dropdown")},8732:e=>{"use strict";e.exports=require("react/jsx-runtime")},9653:e=>{"use strict";e.exports=require("@restart/ui/Overlay")},11242:e=>{"use strict";e.exports=require("redux-persist/integration/react")},11688:e=>{"use strict";e.exports=require("@restart/hooks/useWillUnmount")},12388:e=>{"use strict";e.exports=require("dom-helpers/addEventListener")},12543:(e,r,t)=>{"use strict";t.a(e,async(e,s)=>{try{t.r(r),t.d(r,{config:()=>g,default:()=>d,getServerSideProps:()=>h,getStaticPaths:()=>m,getStaticProps:()=>x,reportWebVitals:()=>q,routeModule:()=>y,unstable_getServerProps:()=>v,unstable_getServerSideProps:()=>j,unstable_getStaticParams:()=>w,unstable_getStaticPaths:()=>b,unstable_getStaticProps:()=>f});var i=t(63885),o=t(80237),a=t(81413),u=t(9616),c=t.n(u),n=t(72386),l=t(53873),p=e([n,l]);[n,l]=p.then?(await p)():p;let d=(0,a.M)(l,"default"),x=(0,a.M)(l,"getStaticProps"),m=(0,a.M)(l,"getStaticPaths"),h=(0,a.M)(l,"getServerSideProps"),g=(0,a.M)(l,"config"),q=(0,a.M)(l,"reportWebVitals"),f=(0,a.M)(l,"unstable_getStaticProps"),b=(0,a.M)(l,"unstable_getStaticPaths"),w=(0,a.M)(l,"unstable_getStaticParams"),v=(0,a.M)(l,"unstable_getServerProps"),j=(0,a.M)(l,"unstable_getServerSideProps"),y=new i.PagesRouteModule({definition:{kind:o.A.PAGES,page:"/hazard/MediaGalleryAccordion",pathname:"/hazard/MediaGalleryAccordion",bundlePath:"",filename:""},components:{App:n.default,Document:c()},userland:l});s()}catch(e){s(e)}})},13364:e=>{"use strict";e.exports=require("redux-saga/effects")},14062:e=>{"use strict";e.exports=import("react-redux")},14078:e=>{"use strict";e.exports=import("swr")},14332:e=>{"use strict";e.exports=require("uncontrollable")},16116:e=>{"use strict";e.exports=require("invariant")},18622:e=>{"use strict";e.exports=require("yup")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},22326:e=>{"use strict";e.exports=require("react-dom")},22541:e=>{"use strict";e.exports=require("redux-persist/lib/storage")},25303:e=>{"use strict";e.exports=require("@restart/ui/NavItem")},26324:e=>{"use strict";e.exports=require("warning")},27825:e=>{"use strict";e.exports=require("lodash")},27910:e=>{"use strict";e.exports=require("stream")},28217:e=>{"use strict";e.exports=require("@restart/ui/DropdownItem")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},29628:e=>{"use strict";e.exports=require("dom-helpers/addClass")},29825:e=>{"use strict";e.exports=require("prop-types")},29841:e=>{"use strict";e.exports=require("dom-helpers/hasClass")},30362:e=>{"use strict";e.exports=require("@restart/ui/DropdownToggle")},33873:e=>{"use strict";e.exports=require("path")},36653:e=>{"use strict";e.exports=require("nprogress")},36955:e=>{"use strict";e.exports=require("@restart/hooks/useBreakpoint")},37766:e=>{"use strict";e.exports=require("@restart/hooks/useMergedRefs")},39756:e=>{"use strict";e.exports=import("redux")},40051:e=>{"use strict";e.exports=require("dom-helpers/removeClass")},40361:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/pages.runtime.prod.js")},42447:(e,r,t)=>{"use strict";t.a(e,async(e,s)=>{try{t.d(r,{A:()=>d});var i=t(8732),o=t(82015),a=t(81149),u=t(82053),c=t(54131),n=t(91353),l=t(88751);t(72025);var p=e([c]);c=(p.then?(await p)():p)[0];let d=e=>{let{t:r}=(0,l.useTranslation)("common"),[t,s]=(0,o.useState)([]),p=e=>{let t=/(http|https):\/\/(\w+:{0,1}\w*)?(\S+)(:[0-9]+)?(\/|\/([\w#!:.?+=&%!\-\/]))?/.test(e.description);return(0,i.jsxs)("div",{className:"carousel-legend",children:[(0,i.jsxs)("p",{className:"lead",children:[(0,i.jsx)("b",{children:r("Filename")})," ",e.originalName||"No Name found"]}),e.description&&(0,i.jsxs)("div",{className:"source_link",children:[(0,i.jsx)("p",{children:(0,i.jsx)("b",{children:r("Source")})}),t?(0,i.jsxs)("div",{children:[(0,i.jsx)(u.FontAwesomeIcon,{icon:c.faLink,size:"1x",color:"#999",className:"me-1"}),(0,i.jsx)("a",{className:"source_link",href:e.description,target:"_blank",rel:"noopener noreferrer",children:e.description})]}):(0,i.jsx)("div",{children:(0,i.jsx)("p",{className:"ps-0 py-0",style:{wordBreak:"break-all"},children:e.description})})]}),e.downloadLink&&(0,i.jsxs)(n.A,{className:"btn btn-success mt-2 btn--download",href:e.downloadLink,children:[r("Download"),(0,i.jsx)(u.FontAwesomeIcon,{icon:c.faDownload,size:"1x",className:"ms-1"})]})]})};return(0,o.useEffect)(()=>{let r=[];e&&e.gallery&&Array.isArray(e.gallery)&&e.gallery.map((t,s)=>{let i,o=t&&t.name.split(".").pop();switch(o){case"JPG":case"jpg":case"jpeg":case"png":i=`http://localhost:3001/api/v1/image/show/${t._id}`;break;case"pdf":i="/images/fileIcons/pdfFile.png";break;case"docx":i="/images/fileIcons/wordFile.png";break;case"xls":case"xlsx":i="/images/fileIcons/xlsFile.png";break;default:i="/images/fileIcons/otherFile.png"}let a=("docx"===o||"pdf"===o||"xls"===o||"xlsx"===o)&&`http://localhost:3001/api/v1/files/download/${t._id}`,u=`${t&&t.original_name?t.original_name:"No Name found"}`,c=e.imageSource&&Array.isArray(e.imageSource)&&e.imageSource.length>0?e.imageSource[s]:"";r.push({src:i,description:c,originalName:u,downloadLink:a})}),s(r)},[e]),(0,i.jsx)("div",{children:t&&0===t.length?(0,i.jsx)("div",{className:"border border-info my-3 mx-0",children:(0,i.jsx)("p",{className:"d-flex d-flex justify-content-center p-2 m-0",children:r("NoFilesFound!")})}):(0,i.jsx)(a.Carousel,{showThumbs:!0,showStatus:!0,showIndicators:!0,infiniteLoop:!0,useKeyboardArrows:!0,autoPlay:!1,stopOnHover:!0,swipeable:!0,dynamicHeight:!1,emulateTouch:!0,renderThumbs:()=>t.map((e,r)=>(0,i.jsx)("img",{src:e.src,alt:`Thumbnail ${r+1}`,style:{width:"60px",height:"60px",objectFit:"cover"}},r)),children:t.map((e,r)=>(0,i.jsxs)("div",{children:[(0,i.jsx)("img",{src:e.src,alt:e.originalName||"Gallery image",style:{maxHeight:"500px",objectFit:"contain"}}),p(e)]},r))})})};s()}catch(e){s(e)}})},42893:e=>{"use strict";e.exports=import("react-hot-toast")},43294:e=>{"use strict";e.exports=require("formik")},50009:e=>{"use strict";e.exports=require("@restart/ui/DropdownMenu")},53873:(e,r,t)=>{"use strict";t.a(e,async(e,s)=>{try{t.r(r),t.d(r,{default:()=>d});var i=t(8732),o=t(54131),a=t(82053),u=t(93024),c=t(82015),n=t(42447),l=t(88751),p=e([o,n]);[o,n]=p.then?(await p)():p;let d=e=>{let{t:r}=(0,l.useTranslation)("common"),[t,s]=(0,c.useState)(!1);return(0,i.jsx)(i.Fragment,{children:(0,i.jsxs)(u.A.Item,{eventKey:"0",children:[(0,i.jsxs)(u.A.Header,{onClick:()=>s(!t),children:[(0,i.jsx)("div",{className:"cardTitle",children:r("mediaGallery")}),(0,i.jsx)("div",{className:"cardArrow",children:t?(0,i.jsx)(a.FontAwesomeIcon,{icon:o.faMinus,color:"#fff"}):(0,i.jsx)(a.FontAwesomeIcon,{icon:o.faPlus,color:"#fff"})})]}),(0,i.jsx)(u.A.Body,{children:(0,i.jsx)(n.A,{gallery:e.images,imageSource:e.imgSrc})})]})})};s()}catch(e){s(e)}})},54131:e=>{"use strict";e.exports=import("@fortawesome/free-solid-svg-icons")},57664:e=>{"use strict";e.exports=require("dom-helpers/scrollbarSize")},58928:e=>{"use strict";e.exports=require("dom-helpers/canUseDOM")},59717:e=>{"use strict";e.exports=require("@restart/ui/DropdownContext")},60560:e=>{"use strict";e.exports=require("dom-helpers/contains")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},65447:e=>{"use strict";e.exports=require("@restart/ui/Button")},67364:e=>{"use strict";e.exports=require("@restart/hooks/useCallbackRef")},68455:e=>{"use strict";e.exports=import("redux-saga")},69722:e=>{"use strict";e.exports=require("es6-promise")},72025:()=>{},74075:e=>{"use strict";e.exports=require("zlib")},74716:e=>{"use strict";e.exports=require("moment")},74987:e=>{"use strict";e.exports=require("@restart/ui/ModalManager")},78097:e=>{"use strict";e.exports=require("next-redux-saga")},78634:e=>{"use strict";e.exports=require("@restart/ui/Anchor")},80237:(e,r)=>{"use strict";Object.defineProperty(r,"A",{enumerable:!0,get:function(){return t}});var t=function(e){return e.PAGES="PAGES",e.PAGES_API="PAGES_API",e.APP_PAGE="APP_PAGE",e.APP_ROUTE="APP_ROUTE",e.IMAGE="IMAGE",e}({})},81149:e=>{"use strict";e.exports=require("react-responsive-carousel")},81366:e=>{"use strict";e.exports=require("redux-auth-wrapper/connectedAuthWrapper")},81413:(e,r)=>{"use strict";Object.defineProperty(r,"M",{enumerable:!0,get:function(){return function e(r,t){return t in r?r[t]:"then"in r&&"function"==typeof r.then?r.then(r=>e(r,t)):"function"==typeof r&&"default"===t?r:void 0}}})},81521:e=>{"use strict";e.exports=require("dom-helpers/querySelectorAll")},81895:e=>{"use strict";e.exports=require("@restart/hooks/useEventCallback")},82015:e=>{"use strict";e.exports=require("react")},82053:e=>{"use strict";e.exports=require("@fortawesome/react-fontawesome")},86842:e=>{"use strict";e.exports=require("@restart/ui/SelectableContext")},87571:e=>{"use strict";e.exports=require("dom-helpers/transitionEnd")},88751:e=>{"use strict";e.exports=require("next-i18next")},93787:e=>{"use strict";e.exports=require("redux-persist")},94947:e=>{"use strict";e.exports=require("@restart/hooks/useTimeout")},96196:e=>{"use strict";e.exports=require("next-redux-wrapper")},98320:e=>{"use strict";e.exports=require("@restart/hooks/useIsomorphicEffect")},99460:e=>{"use strict";e.exports=require("dom-helpers/css")}};var r=require("../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[6089,9216,9616,2386],()=>t(12543));module.exports=s})();