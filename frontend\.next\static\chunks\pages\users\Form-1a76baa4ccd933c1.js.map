{"version": 3, "file": "static/chunks/pages/users/Form-1a76baa4ccd933c1.js", "mappings": "gFACA,4CACA,cACA,WACA,OAAe,EAAQ,KAAmC,CAC1D,EACA,SAFsB", "sources": ["webpack://_N_E/?fe0b"], "sourcesContent": ["\n    (window.__NEXT_P = window.__NEXT_P || []).push([\n      \"/users/Form\",\n      function () {\n        return require(\"private-next-pages/users/Form.tsx\");\n      }\n    ]);\n    if(module.hot) {\n      module.hot.dispose(function () {\n        window.__NEXT_P.push([\"/users/Form\"])\n      });\n    }\n  "], "names": [], "sourceRoot": "", "ignoreList": []}