{"version": 3, "file": "static/chunks/8601-1185d09862d4fa36.js", "mappings": "8UAmBA,IAAMA,EACJ,uIAgUF,EAvToBC,YAkVCC,CAA0B,CAAEC,IAJSC,UA7UxD,GAAM,GAAEF,CAAC,CAiVmE,CAJM,CAvB3DG,EAAC,CAtTbC,CAAI,CAAE,CAAGC,CAAAA,EAAAA,EAAAA,EAAAA,CAAcA,CAAC,UAC7BC,EAAgC,OAAlBF,EAAKG,QAAQ,CAAY,KAAOH,EAAKG,QAAQ,CAO3D,CAACC,EAAYC,EAAc,CAAGC,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CANzB,CACjBC,MAAO,GACPC,YAAa,GACbC,QAAS,EACX,GAGM,CAACC,EAAmBC,EAAqB,CAAGL,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,EAAU,GAC9D,CAACM,EAAqBC,EAAuB,CAAGP,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAQ,EAAE,EAClE,CAACT,EAAwBiB,EAA0B,CAAGR,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAQ,EAAE,EACxE,CAACR,EAAqBiB,EAAuB,CAAGT,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAQ,EAAE,EAClE,CAACU,EAAuBC,EAAyB,CAAGX,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAQ,EAAE,EACtE,CAACY,EAAYC,EAAc,CAAGb,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,EAAU,GAChD,CAACc,EAAYC,EAAc,CAAGf,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,EAAU,GAChD,CAACgB,EAAcC,EAAgB,CAAGjB,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,EAAU,GACpD,CAACkB,EAAQ,CAAGlB,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,EAAU,GAC9B,CAACmB,EAAQC,GAAU,CAAGpB,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAS,IAEvC,CAACqB,GAAUC,GAAa,CAAGtB,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAQ,EAAE,EAC7C,CAACuB,GAAgBC,GAAkB,CAAGxB,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAQ,EAAE,EACxD,CAACyB,GAAQC,GAAU,CAAG1B,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAQ,EAAE,EACxC,CAAC2B,GAAO,CAAG3B,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAQ,EAAE,EAC7B,CAAC4B,GAAQC,GAAU,CAAG7B,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAQ,EAAE,EACxC,CAAC8B,GAAaC,GAAe,CAAG/B,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,EAAU,GAGlDgC,GAAoB,CACxBC,MAAO,CAAC,EACRC,MAAO,IACPC,KAAM,CAAElC,MAAO,KAAM,CACvB,EA4BMmC,GAAe,MAAOC,IAC1B,IAAMC,EAAiB,EAAE,CAEnBC,EAAoB,EAAE,CACtBC,EAAiB,EAAE,CACnBC,EAAW,MAAMC,EAAAA,CAAUA,CAACC,GAAG,CACnC,WAA2B,OAAhBtD,EAAMuD,MAAM,CAAC,EAAE,EAC1BP,GAEEI,GAAYA,EAASI,IAAI,EAAIJ,EAASI,IAAI,CAACC,MAAM,CAAG,GAAG,CACzDL,EAASI,IAAI,CAACE,OAAO,CAAC,CAACC,EAAcC,KACnCD,EAAQ3B,QAAQ,EACd2B,EAAQ3B,QAAQ,CAACyB,MAAM,CAAG,GAC1BE,EAAQ3B,QAAQ,CAAC6B,GAAG,CAAC,CAACC,EAAUC,KAE9BD,EAAIjD,WAAW,CADK8C,EACF9C,QADkB,CAACkD,EAAE,CAACC,MAAM,CAE9Cd,EAAWe,IAAI,CAACH,EAClB,GACFH,EAAQvB,MAAM,EACZuB,EAAQvB,MAAM,CAACqB,MAAM,CAAG,GACxBE,EAAQvB,MAAM,CAACyB,GAAG,CAAC,CAACK,EAAYC,KAC9BhB,EAAQc,IAAI,CAACC,EACf,GAEFP,EAAQS,UAAU,EAChBT,EAAQS,UAAU,CAACX,MAAM,CAAG,GAC5BE,EAAQS,UAAU,CAACP,GAAG,CAAC,CAACQ,EAAUC,KAChCrB,EAAQgB,IAAI,CAACI,EACf,EACJ,GACApC,GAAaiB,GAWbb,GAAUkC,OAVapB,EAAAA,KAAAA,EAAAA,EAASqB,MAAM,CAAC,CAACC,CAAjBtB,CAAsBuB,KAC3C,IAAMC,EAAeF,EAAIG,IAAI,CAAC,GAAeF,EAAIG,GAAG,GAAKC,EAAKD,GAAG,EAOjE,OANGF,EACAA,EAAaI,KAAK,GAGlBN,EAJc,IAIN,CAAC,CAAC,GAAGC,CAAG,CAAEK,MAAO,CAAC,GAEtBN,CACV,EAAG,EAAE,GACuBO,IAAI,CAACC,MAChCzC,GAAUS,EAAQ+B,IAAI,CAACC,MAE3B,EAEMC,GAAqB,MAAOC,IAGhC,IAAMjC,EAAoB,EAAE,CAEtBE,EAAW,MAAMC,EAAAA,CAAUA,CAACC,GAAG,CACnC,WAA2B,OAAhBtD,EAAMuD,MAAM,CAAC,EAAE,EAC1B4B,GAEE/B,GAAYA,EAASI,IAAI,EAAIJ,EAASI,IAAI,CAACC,MAAM,CAAG,GAAG,CACzDL,EAASI,IAAI,CAACE,OAAO,CAAC,CAACC,EAAcC,KACnCD,EAAQ3B,QAAQ,EACd2B,EAAQ3B,QAAQ,CAACyB,MAAM,CAAG,GAC1BE,EAAQ3B,QAAQ,CAAC6B,GAAG,CAAC,CAACC,EAAUC,KAE9BD,EAAIjD,WAAW,CADK8C,EAAQ3B,QAAQ,CAAC+B,EAAE,CAACC,MAAM,CAE9Cd,EAAWe,IAAI,CAACH,EAClB,EACJ,GACA3B,GAAkBe,GAEtB,EACMkC,GAAgB,MAAOC,IAC3BrE,GAAqB,GACrB,IAAMoC,EAAW,MAAMC,EAAAA,CAAUA,CAACC,GAAG,CACnC,WAA2B,OAAhBtD,EAAMuD,MAAM,CAAC,EAAE,EAC1B8B,GAGEjC,IACFA,EAAS,IADG,EACJ,CAAW,CACjBA,OAAAA,EAAAA,KAAAA,EAAAA,EAAUtC,OAAAA,GAAVsC,EAA8BtC,OAAO,CAAC+D,GAAG,CACrC,GAAwCzB,MAAAA,CAArCkC,8BAAsB,CAAC,gBAAmC,OAArBlC,EAAStC,OAAO,CAAC+D,GAAG,EAC5D,kDAEFzB,EAAAA,KAAAA,EAAAA,EAAUmC,UAAVnC,IAAUmC,EAAgB,CAC5BxD,GAAUqB,EAASmC,cAAc,EAEnC7E,EAAc0C,GACdpC,GAAqB,IAEvBA,GAAqB,EACvB,EAEMwE,GAA0B,MAAOC,IACrC,IAAMrC,EAAW,MAAMC,EAAAA,CAAUA,CAACC,GAAG,CACnC,WAA2B,OAAhBtD,EAAMuD,MAAM,CAAC,EAAE,CAAC,kBAC3BkC,GAGFvE,EADqBkC,GAAYA,EAASI,IAAI,CAAGJ,EAASI,IAAI,CAAG,EAAE,CAErE,CADyBkC,CAGnBC,GAA6B,MAAOF,IACxC,IAAMrC,EAAW,MAAMC,EAAAA,CAAUA,CAACC,GAAG,CACnC,WAA2B,OAAhBtD,EAAMuD,MAAM,CAAC,EAAE,CAAC,mBAC3BkC,GAGFtE,EADqBiC,GAAYA,EAASI,IAAI,CAAGJ,EAASI,IAAI,CAAG,EAAE,CAErE,EAEMoC,EAHsBF,CAGG,MAAO1C,IACpC,IAAMI,EAAW,MAAMC,EAAAA,CAAUA,CAACC,GAAG,CACnC,WAA2B,OAAhBtD,EAAMuD,MAAM,CAAC,EAAE,CAAC,eAC3BP,GAGF5B,EADqBgC,GAAYA,EAASI,IAAI,CAAGJ,EAASI,IAAI,CAAG,EAAE,CAErE,CADyBkC,CAGnBG,GAA0B,MAAOC,IACrC,IAAM1C,EAAW,MAAMC,EAAAA,CAAUA,CAACC,GAAG,CACnC,WAA2B,OAAhBtD,EAAMuD,MAAM,CAAC,EAAE,CAAC,iBAC3BuC,GAGFxE,EADqB8B,GAAYA,EAASI,IAAI,CAAGJ,EAASI,IAAI,CAAG,EAAE,CAErE,EAEAuC,CAH2BL,EAG3BK,EAAAA,SAAAA,CAASA,CAAC,KAeJ/F,EAAMuD,MAAM,EAAIvD,EAAMuD,MAAM,CAAC,EAAE,EAAE,CACnC6B,GAAc,CAAC,GACfI,GAAwB7C,IACxBgD,GAA2BhD,IAC3BiD,GAAuBjD,IACvBkD,GAAwBlD,IACxBI,GApBsB,CACtBD,KAAM,CAAEkD,GAmBKhD,YAnBW,KAAM,EAC9BH,MAAO,IACPoD,UAAU,EACVC,UAAW,KACXC,OAAQpG,CACV,GAeEmF,GAdmB,CACnBpC,KAAM,CAAEkD,SAaWb,MAbK,KAAM,EAC9BtC,MAAO,IACPuD,gBAAgB,EAChBF,UAAW,KACXC,OAAQpG,CACV,GAUF,EAAG,EAAE,EAEL,IAAMsG,GAAsB,IAExB,WAACC,EAAAA,CAASA,CAACC,IAAI,EAACC,SAAS,cACvB,WAACF,EAAAA,CAASA,CAACG,MAAM,EAACC,QAAS,IAAM9E,EAAgB,CAACD,aAChD,UAACgF,MAAAA,CAAIC,UAAU,qBAAa3G,EAAE,iBAC9B,UAAC0G,MAAAA,CAAIC,UAAU,qBACZjF,EACC,UAACkF,EAAAA,CAAeA,CAAAA,CAACC,KAAMC,EAAAA,GAAMA,CAAEC,MAAM,SAErC,UAACH,EAAAA,CAAeA,CAAAA,CAACC,KAAMG,EAAAA,GAAOA,CAAED,MAAM,cAI5C,UAACV,EAAAA,CAASA,CAACY,IAAI,WACb,UAACC,EAAAA,CAAUA,CAAAA,CACTC,KAAK,SACLC,GAAIrH,GAASA,EAAMuD,MAAM,CAAGvD,EAAMuD,MAAM,CAAC,EAAE,CAAG,YAOxB+D,CAAAA,EAAAA,EAAAA,uBAAAA,CAAuBA,CAAC,IACtD,UAACjB,GAAAA,CAAAA,IAKH,IAAMkB,GAAcC,OAiEX,+HAjEiBC,IAAI,CAAC3F,GAgB/B,MACE,WAAC6E,MAAAA,CAAIC,UAAU,0BACb,UAACc,EAAAA,CAAWA,CAAAA,CAACnE,OAAQvD,EAAMuD,MAAM,GAC/BxC,GAAsBN,EAAWG,KAAK,CAGtC,UAHqB,CAGrB,sBACGH,GAAcA,EAAWK,OAAO,CAC/B,kCAwDeL,EAvDMA,EAuDWF,EAvDCA,EAwDpC,EADiC,CACjC,IADsD,CACtD,EAACoH,EAAAA,OAAkBA,CAAAA,CAAClH,WAAYA,EAAYF,YAAaA,KAvDpD,UAACqH,EAAAA,CAAGA,CAAAA,UACF,UAACC,EAAAA,CAAGA,CAAAA,CAACjB,UAAU,YAAYkB,GAAI,CAAEC,KAAM,EAAGC,OAAQ,CAAE,WAClD,UAACrB,MAAAA,UACC,WAACsB,IAAAA,CAAErB,UAAU,QAAQsB,MAAO,CAAEC,SAAU,MAAO,YAC7C,WAACpE,IAAAA,WAAG9D,EAAE,qBAAqB,QAC1B6B,EACCsG,SAsCfA,CAA+B,CAAEtG,CAAc,EACtD,OAAOyF,GAAezF,EACpB,UAACuG,IAAAA,CAAEC,OAAO,SAASC,KAAMzG,WACtBA,IAGH,UAACiG,OAAAA,CAAKG,MAAO,CAAElB,MAAO,SAAU,WAAIlF,GAExC,EA9CmCyF,GAAazF,GAExB,UAACiG,OAAAA,CAAKG,MAAO,CAAElB,MAAO,SAAU,WAC7B/G,EAAE,+BASjB,UAAC0G,MAAAA,CAAIC,UAAU,6CACb,UAAC4B,EAAAA,CAAOA,CAAAA,CAACC,UAAU,WAGvB,UAACC,KAAAA,CAAAA,GACD,WAACd,EAAAA,CAAGA,CAAAA,WACF,UAACC,EAAAA,CAAGA,CAAAA,WAAEc,EAAY1I,IAAGC,EA+CxB,UAAC0I,EAAAA,OAAkBA,CAAAA,CAAC3I,EAAGA,EAAGC,uBAAwBA,OA9C/C,UAAC2H,EAAAA,CAAGA,CAAAA,WAAEgB,CAyCsC,CAzCjB5I,IAAGE,EA0CjC,UAAC2I,EAAAA,OAAeA,CAAAA,CAAC7I,EAAGA,EAAGE,oBAAqBA,UAxC3C,UAACuI,KAAAA,CAAAA,GACD,WAACd,EAAAA,CAAGA,CAAAA,WACF,UAACC,EAAAA,CAAGA,CAAAA,WAAEkB,CAiC0C,CAjCjB9I,EAiCmBoB,EAjChBA,EAkCrC,UAAC2H,EAAAA,KADgF,EAC9DA,CAAAA,CAAC/I,EAAGA,EAAGoB,sBAAuBA,OAjC9C,UAACwG,EAAAA,CAAGA,CAAAA,WA4BW5H,CAA0B,CA5BjBA,EA4BmBgB,EA5BhBA,EA6B9B,UAACgI,EAAAA,GADuE,IACxDA,CAAAA,CAAChJ,EAAGA,EAAGgB,oBAAqBA,UA3B3C,UAACiI,EAAAA,OAAsBA,CAAAA,CAxD7BjJ,EAAGA,EACHmC,OAAQA,GACRG,OAAQA,GACR4G,UAAWnJ,EACXoJ,wBAAyB,CACvBvH,QAASA,EACTwH,SAAUrH,GACVE,eAAgBA,GAChBoH,cAzNkB,CAyNHA,GAxNjB,IAAMC,EAAqB,CACzBzG,KAAM,CAAC,EACPD,MAAO,IACPoD,UAAU,EACVC,UAAW,KACXC,OAAQpG,CACV,EACAwJ,EAAmBzG,IAAI,CAAG,CACxB,CAACU,EAAKgG,cAAc,CAAC,CAAEhG,EAAKiG,aAAa,EAE3C1G,GAAawG,EACf,EA8MIG,oBA7MwB,CA6MHA,GA5MvB,IAAMvE,EAAe,CACnBrC,KAAM,CAAC,EACPD,MAAO,IACPuD,gBAAgB,EAChBF,UAAW,KACXC,OAAQpG,CACV,EACAoF,EAAarC,IAAI,CAAG,CAClB,CAACU,EAAKgG,cAAc,CAAC,CAAEhG,EAAKiG,aAAa,EAE3CvE,GAAmBC,EACrB,EAkMI7C,OAAQA,EACV,OAOI,UAACqE,MAAAA,CAAIC,UAAU,uBAAe3G,EAAE,8BA+CxC,6FC5UA,IAAM0J,EAAwBC,EAAAA,OAAb,GAA6B,CAAC,GAK5CC,IALyB,IAAoB,WAC9CjD,CAAS,UACTkD,CAAQ,CACRC,GAAIC,EAAY,KAAK,CACrB,GAAGhK,EACJ,GAEC,OAAO,EADIiK,CAAAA,EAAAA,EAAAA,EAAAA,CAAkBA,CAACH,EAAU,aACpBI,CAAAA,EAAAA,EAAAA,GAAAA,CAAIA,CAACF,EAAW,CAClCH,IAAKA,EACLjD,UAAWuD,IAAWvD,EAAWkD,GACjC,GAAG9J,CAAK,EAEZ,GACA2J,EAASS,WAAW,CAAG,WCbvB,IAAMC,EAA0BT,EAAAA,SAAb,CAA6B,CAAC,GAK9CC,MAL2B,EAAoB,CAChDjD,WAAS,UACTkD,CAAQ,CACRC,GAAIC,EAAY,KAAK,CACrB,GAAGhK,EACJ,GAEC,OADA8J,EAAWG,CAAAA,EAAAA,EAAAA,EAAAA,CAAkBA,CAACH,EAAU,eACpBI,CAAAA,EAAAA,EAAAA,GAAAA,CAAIA,CAACF,EAAW,CAClCH,IAAKA,EACLjD,UAAWuD,IAAWvD,EAAWkD,GACjC,GAAG9J,CAAK,EAEZ,GACAqK,EAJyBF,WAIH,CAAG,4BCXzB,IAAMG,EAA0BV,EAAAA,SAAb,CAA6B,CAAC,GAM9CC,MAN2B,EAAoB,UAChDC,CAAQ,WACRlD,CAAS,CAETmD,CADA,EACIC,EAAY,KAAK,CACrB,GAAGhK,EACJ,GACOuK,EAASN,CAAAA,EAAAA,EAAAA,EAAAA,CAAkBA,CAACH,EAAU,eACtCU,EAAeC,CAAAA,EAAAA,EAAAA,OAAAA,CAAOA,CAAC,IAAO,EAClCC,mBAAoBH,EACtB,EAAI,CAACA,EAAO,EACZ,MAAoBL,CAAb,EAAaA,EAAAA,GAAAA,CAAIA,CAACS,EAAAA,CAAiBA,CAACC,QAAQ,CAAE,CACnDC,MAAOL,EACPM,SAAuBZ,CAAb,EAAaA,EAAAA,GAAAA,CAAIA,CAACF,EAAP,CACnBH,IAAKA,EACL,GAAG7J,CAAK,CACR4G,UAAWuD,IAAWvD,EAAW2D,EACnC,EACF,EACF,GACAD,EAAWF,GAJgBD,QAIL,CAAG,aCtBzB,IAAMY,EAAuBnB,EAAAA,MAAb,IAA6B,CAC7C,CACA,EAMGC,GARwB,KAE1B,UACCC,CAAQ,WACRlD,CAAS,SACToE,CAAO,CACPjB,GAAIC,EAAY,KAAK,CACrB,GAAGhK,EACJ,GACOuK,EAASN,CAAAA,EAAAA,EAAAA,EAAAA,CAAkBA,CAACH,EAAU,YAC5C,MAAoBI,CAAb,EAAaA,EAAAA,GAAAA,CAAIA,CAACF,EAAW,CAClCH,IAAKA,EACLjD,UAAWuD,IAAWa,EAAU,GAAaA,MAAAA,CAAVT,EAAO,EAArBJ,GAAgC,OAARa,CAX0G,EAW9FT,EAAQ3D,GACjE,GAAG5G,CAAK,EAEZ,GACA+K,EAAQX,WAAW,CAAG,UChBtB,IAAMa,EAA8BrB,EAAAA,UAAgB,CAAC,EAA9B,CAKpBC,QALmD,EAApB,SAChCjD,CAAS,UACTkD,CAAQ,CACRC,GAAIC,EAAY,KAAK,CACrB,GAAGhK,EACJ,GAEC,OADA8J,EAAWG,CAAAA,EAAAA,EAAAA,EAAAA,CAAkBA,CAACH,EAAU,oBACpBI,CAAAA,EAAAA,EAAAA,GAAAA,CAAIA,CAACF,EAAW,CAClCH,IAAKA,EACLjD,UAAWuD,IAAWvD,EAAWkD,GACjC,GAAG9J,CAAK,EAEZ,EACAiL,GAJyBd,WAIC,CAAG,iBCb7B,IAAMe,EAAwBtB,EAAAA,OAAb,GAA6B,CAAC,GAK5CC,IALyB,IAAoB,WAC9CjD,CAAS,UACTkD,CAAQ,CACRC,GAAIC,EAAY,GAAG,CACnB,GAAGhK,EACJ,GAEC,OADA8J,EAAWG,CAAAA,EAAAA,EAAAA,EAAAA,CAAkBA,CAACH,EAAU,aACpBI,CAAAA,EAAAA,EAAAA,GAAAA,CAAIA,CAACF,EAAW,CAClCH,IAAKA,EACLjD,UAAWuD,IAAWvD,EAAWkD,GACjC,GAAG9J,CAAK,EAEZ,GACAkL,EAJyBf,WAIL,CAAG,0BCZvB,IAAMgB,EAAgBC,CAAAA,EAAAA,EAAAA,CAAAA,CAAgBA,CAAC,MACjCC,EAA4BzB,EAAAA,UAAgB,CAA7B,CAA8B,EAKhDC,QAL6B,WAC9BjD,CAAS,UACTkD,CAAQ,CACRC,GAAIC,EAAYmB,CAAa,CAC7B,GAAGnL,EACJ,GAEC,OADA8J,EAAWG,CAAAA,EAAAA,EAAAA,EAAAA,CAAkBA,CAACH,EAAU,iBACpBI,CAAAA,EAAAA,EAAAA,GAAAA,CAAIA,CAACF,EAAW,CAClCH,IAAKA,EACLjD,UAAWuD,IAAWvD,EAAWkD,GACjC,GAAG9J,CAAK,EAEZ,GACAqL,EAAajB,WAAW,CAAG,eCf3B,IAAMkB,EAAwB1B,EAAAA,OAAb,GAA6B,CAAC,GAK5CC,IALyB,IAAoB,WAC9CjD,CAAS,UACTkD,CAAQ,CACRC,GAAIC,EAAY,GAAG,CACnB,GAAGhK,EACJ,GAEC,OADA8J,EAAWG,CAAAA,EAAAA,EAAAA,EAAAA,CAAkBA,CAACH,EAAU,aACpBI,CAAAA,EAAAA,EAAAA,GAAAA,CAAIA,CAACF,EAAW,CAClCH,IAAKA,EACLjD,UAAWuD,IAAWvD,EAAWkD,GACjC,GAAG9J,CAAK,EAEZ,GACAsL,EAJyBnB,WAIL,CAAG,WCZvB,IAAMoB,EAAgBH,CAAAA,EAAAA,EAAAA,CAAAA,CAAgBA,CAAC,MACjCI,EAAyB5B,EAAAA,QAAb,EAA6B,CAAC,GAK7CC,KAL0B,GAAoB,WAC/CjD,CAAS,UACTkD,CAAQ,CACRC,GAAIC,EAAYuB,CAAa,CAC7B,GAAGvL,EACJ,GAEC,OAAO,EADIiK,CAAAA,EAAAA,EAAAA,EAAAA,CAAkBA,CAACH,EAAU,cACpBI,CAAAA,EAAAA,EAAAA,GAAAA,CAAIA,CAACF,EAAW,CAClCH,IAAKA,EACLjD,UAAWuD,IAAWvD,EAAWkD,GACjC,GAAG9J,CAAK,EAEZ,GACAwL,EAJyBrB,WAIJ,CAAG,YCNxB,IAAMsB,EAAoB7B,EAAAA,GAAb,OAA6B,CAAC,GAAnB,QAAoB,UAC1CE,CAAQ,WACRlD,CAAS,CACT8E,IAAE,MACFC,CAAI,QACJC,CAAM,MACNC,EAAO,EAAK,UACZf,CAAQ,CAERf,CADA,EACIC,EAAY,KAAK,CACrB,GAAGhK,EACJ,GACOuK,EAASN,CAAAA,EAAAA,EAAAA,EAAAA,CAAkBA,CAACH,EAAU,QAC5C,MAAoBI,CAAb,EAAaA,EAAAA,GAAAA,CAAIA,CAACF,EAAP,CAChBH,IAAKA,EACL,GAAG7J,CAAK,CACR4G,UAAWuD,IAAWvD,EAAW2D,EAAQmB,GAAM,MAAS,GAAnCvB,GAAmC,CAAHuB,GAAMC,GAAQ,QAAa,OAALA,GAAQC,GAAU,UAAiB,OAAPA,IACvGd,IATyJ,KAS/Ie,EAAoB3B,CAAAA,EAAAA,EAAb,GAAaA,CAAIA,CAACP,EAAU,CAC3CmB,GAD0B,MAAenB,CAE3C,GAAKmB,CACP,EACF,GACAW,EAAKrB,WAAW,CAAG,OACnB,MAAe0B,OAAOC,MAAM,CAACN,EAAM,CACjCO,INhBajB,CMgBRA,CACLkB,KNjBoBlB,CKDPS,CLCQ,CMkBrBU,EAFYnB,KDjBUS,EAAC,CCmBbH,CACVnE,CAFgBsE,ITpBH7B,CSsBPA,CACNwC,GHrByBd,EDFZH,CIuBPA,CACNkB,CTxBsB,GSsBRzC,CFtBD2B,CFAQJ,CIyBrBzE,CJzBsB,GIuBRyE,EFvBOI,CLSRhB,CKTS,CE0BtB+B,EAFcf,KRxBDjB,CCSUC,COkBvBgC,CPlBwB,GOgBNhC,IRzBKD,EAAC,CGAXY,CK2BDA,CADMZ,CAElB,EAAC,SL5B0BY,EAAC,GK2BFA,yFCI5B,MAvCwB,IACpB,IAAMhK,EAAsBjB,EAAMiB,gBAsCvBgI,GAtC0C,CAC/C,CAAEhJ,CAAC,CAAE,CAAGK,CAAAA,EAAAA,EAAAA,EAAAA,CAAcA,CAAC,UAC7B,MACI,+BACI,UAACqG,MAAAA,CAAIC,UAAU,6BACX,WAAC6E,EAAAA,CAAIA,CAAAA,CAAC7E,UAAU,qBACZ,UAAC6E,EAAAA,CAAIA,CAAChF,MAAM,EAACG,UAAU,uBACtB3G,EAAE,2BAEH,UAACwL,EAAAA,CAAIA,CAACvE,IAAI,EAACN,UAAU,sBACpB3F,GAAuBA,EAAoBwC,MAAM,CAAG,EACjDxC,EAAoB4C,GAAG,CAAC,CAACiB,EAAMlB,IAC/B,UAAC2I,KAAAA,CAAG3F,UAAU,mBACV,WAAC4F,KAAAA,CAAe5F,UAAU,oBAC1B,UAACuF,IAAIA,CAED5D,KAAK,qBACLwB,GAAI,eAHHoC,MAG2B,CAATrH,EAAKD,GAAG,WAE1BC,GAAQA,EAAKlE,KAAK,CAAG,GAAc,OAAXkE,EAAKlE,KAAK,EAAK,IAJnCkE,EAAKD,GAAG,EAMjB,WAACkD,OAAAA,WACI,IAAI,IACHjD,GAAQA,EAAK2H,OAAO,CAAG,GAAsB,OAAnB3H,EAAK2H,OAAO,CAAC7L,KAAK,EAAK,GAAG,SAVlD,YAgBZ,UAACmH,OAAAA,CAAKnB,UAAU,uBAAe3G,EAAE,2BAOzD,0GCIA,MAzC2B,IACvB,IAAIoB,EAAwBrB,EAAMqB,gBAwCvB2H,KAxC4C,CACjD,CAAE/I,CAAC,CAAE,CAAGK,CAAAA,EAAAA,EAAAA,EAAAA,CAAcA,CAAC,CAuCC,SAtC9B,MACI,+BACI,UAACqG,MAAAA,CAAIC,UAAU,6BACX,WAAC6E,EAAAA,CAAIA,CAAAA,CAAC7E,UAAU,qBACZ,UAAC6E,EAAAA,CAAIA,CAAChF,MAAM,EAACG,UAAU,uBACtB3G,EAAE,8BAEH,UAACwL,EAAAA,CAAIA,CAACvE,IAAI,EAACN,UAAU,sBACpBvF,GAAyBA,EAAsBoC,MAAM,CAAG,EACrDpC,EAAsBwC,GAAG,CAAC,CAACiB,EAAMlB,IACjC,UAAC2I,KAAAA,CAAG3F,UAAU,mBACV,WAAC4F,KAAAA,CAAe5F,UAAU,oBAC1B,UAACuF,IAAIA,CACD5D,KAAK,2BACLwB,GAAI,SAFHoC,YAEiC,OAATrH,EAAKD,GAAG,WAEhCC,GAAQA,EAAKlE,KAAK,CAAG,GAAc,OAAXkE,EAAKlE,KAAK,EAAK,KAE5C,WAACmH,OAAAA,WACI,IAAI,IAEJjD,GAAQA,EAAK4H,OAAO,EAAI5H,EAAK4H,OAAO,CAACD,OAAO,CAC3C,GAA8B,OAA3B3H,EAAK4H,OAAO,CAACD,OAAO,CAAC7L,KAAK,EAC7B,GAAG,SAZAgD,MAmBb,UAACmE,OAAAA,CAAKnB,UAAU,uBAAe3G,EAAE,2BAOzD,0GCCA,MAzC2B,IACvB,IAAMC,EAAyBF,EAAME,gBAwC1B0I,MAxCgD,CACrD,CAAE3I,CAAC,CAAE,CAAGK,CAAAA,EAAAA,EAAAA,EAAAA,CAAcA,CAAC,UAC7B,MACI,+BACI,UAACqG,MAAAA,CAAIC,UAAU,6BACX,WAAC6E,EAAAA,CAAIA,CAAAA,CAAC7E,UAAU,qBACZ,UAAC6E,EAAAA,CAAIA,CAAChF,MAAM,EAACG,UAAU,uBACtB3G,EAAE,8BAEH,UAACwL,EAAAA,CAAIA,CAACvE,IAAI,EAACN,UAAU,sBACpB1G,GAA0BA,EAAuBuD,MAAM,CAAG,EACvDvD,EAAuB2D,GAAG,CAAC,CAACiB,EAAMlB,IAE9B,UAAC2I,KAAAA,CAAG3F,UAAU,mBACV,WAAC4F,KAAAA,CAAe5F,UAAU,oBACtB,UAACuF,IAAIA,CAEL5D,KAAK,qBACLwB,GAAI,eAHCoC,MAGuB,CAATrH,EAAKD,GAAG,WAE1BC,GAAQA,EAAKlE,KAAK,CAAG,GAAc,OAAXkE,EAAKlE,KAAK,EAAK,IAJnCkE,EAAKD,GAAG,EAMb,WAACkD,OAAAA,WACA,IAAI,IACHjD,GAAQA,EAAK2H,OAAO,CAAG,GAAsB,OAAnB3H,EAAK2H,OAAO,CAAC7L,KAAK,EAAK,GAAG,SAVjDgD,MAiBjB,UAACmE,OAAAA,CAAKnB,UAAU,uBAAe3G,EAAE,2BAOzD,0GCCA,MAxCwB,IACpB,IAAIE,EAAsBH,EAAMG,gBAuCrB2I,GAvCwC,CAC7C,CAAE7I,CAAC,CAAE,CAAGK,CAAAA,EAAAA,EAAAA,EAAAA,CAAcA,CAAC,UAC7B,MACI,+BACI,UAACqG,MAAAA,CAAIC,UAAU,6BACX,WAAC6E,EAAAA,CAAIA,CAAAA,CAAC7E,UAAU,qBACZ,UAAC6E,EAAAA,CAAIA,CAAChF,MAAM,EAACG,UAAU,uBACtB3G,EAAE,kCAGH,UAACwL,EAAAA,CAAIA,CAACvE,IAAI,EAACN,UAAU,sBACpBzG,GAAuBA,EAAoBsD,MAAM,CAAG,EACjDtD,EAAoB0D,GAAG,CAAC,CAACiB,EAAMlB,IAC/B,UAAC2I,KAAAA,CAAG3F,UAAU,mBACV,WAAC4F,KAAAA,CAAe5F,UAAU,oBAC1B,UAACuF,IAAIA,CAED5D,KAAK,yBACLwB,GAAI,WAHHoC,QAG+B,OAATrH,EAAKD,GAAG,WAE9BC,GAAQA,EAAKlE,KAAK,CAAG,GAAc,OAAXkE,EAAKlE,KAAK,EAAK,IAJnCkE,EAAKD,GAAG,EAMjB,WAACkD,OAAAA,WACI,IAAI,IACHjD,GAAQA,EAAK2H,OAAO,CAAG,GAAsB,OAAnB3H,EAAK2H,OAAO,CAAC7L,KAAK,EAAK,GAAG,SAVjDgD,MAgBb,UAACmE,OAAAA,CAAKnB,UAAU,uBAAe3G,EAAE,2BAOzD,oCC/CA,IAAM0M,EAAuB/C,QAAb,CAAaA,UAAF,GAAqB,CAAC,MACjD+C,EAAQvC,WAAW,CAAG,oBACtB,MAAeuC,OAAOA,EAAC,wFC8CvB,MAhC2B,IACvB,IAAMlM,EAAaT,EAAMS,UAAU,CAC7BF,EAAcP,EAAMO,CA8BfoH,UA9B0B,CACrC,MACI,+BACI,WAACC,EAAAA,CAAGA,CAAAA,WACA,WAACC,EAAAA,CAAGA,CAAAA,CAACjB,UAAU,iBACX,UAACgG,KAAAA,UACAnM,EAAWG,KAAK,EAAIH,EAAWG,KAAK,CAACL,EAAY,CAC5CE,EAAWG,KAAK,CAACL,EAAY,CAC7B,KAEN,UAACsM,EAAAA,CAAiBA,CAAAA,CAClBhM,YACIJ,EAAWI,WAAW,EAAIJ,EAAWI,WAAW,CAACN,EAAY,CAC3DE,EAAWI,WAAW,CAACN,EAAY,CACnC,QAIV,UAACsH,EAAAA,CAAGA,CAAAA,CAACK,MAAO,CAAE4E,QAAS,MAAO,WAC1B,UAACC,MAAAA,CACD1I,IAAK5D,EAAWK,OAAO,CACvBoH,MAAO,CAAE8E,MAAO,OAAQC,OAAQ,QAASC,eAAgB,OAAQ,EACjEC,IAAI,iBAMxB", "sources": ["webpack://_N_E/./pages/hazard/HazardShow.tsx", "webpack://_N_E/./node_modules/react-bootstrap/esm/CardBody.js", "webpack://_N_E/./node_modules/react-bootstrap/esm/CardFooter.js", "webpack://_N_E/./node_modules/react-bootstrap/esm/CardHeader.js", "webpack://_N_E/./node_modules/react-bootstrap/esm/CardImg.js", "webpack://_N_E/./node_modules/react-bootstrap/esm/CardImgOverlay.js", "webpack://_N_E/./node_modules/react-bootstrap/esm/CardLink.js", "webpack://_N_E/./node_modules/react-bootstrap/esm/CardSubtitle.js", "webpack://_N_E/./node_modules/react-bootstrap/esm/CardText.js", "webpack://_N_E/./node_modules/react-bootstrap/esm/CardTitle.js", "webpack://_N_E/./node_modules/react-bootstrap/esm/Card.js", "webpack://_N_E/./pages/hazard/HazardPastEvent.tsx", "webpack://_N_E/./pages/hazard/HazardOrganisation.tsx", "webpack://_N_E/./pages/hazard/HazardCurrentEvent.tsx", "webpack://_N_E/./pages/hazard/HazardOperation.tsx", "webpack://_N_E/./node_modules/react-bootstrap/esm/CardHeaderContext.js", "webpack://_N_E/./pages/hazard/HazardCoverSection.tsx"], "sourcesContent": ["//Import Library\r\nimport React, { useEffect, useState } from \"react\";\r\nimport { Accordion, Card, Row, Col, Spinner } from \"react-bootstrap\";\r\nimport { FontAwesomeIcon } from \"@fortawesome/react-fontawesome\";\r\nimport { faPlus, faMinus } from \"@fortawesome/free-solid-svg-icons\";\r\n\r\n//Import services/components\r\nimport UpdatePopup from \"../../components/updates/UpdatePopup\";\r\nimport { useTranslation } from 'next-i18next';\r\nimport { canViewDiscussionUpdate } from \"./permission\";\r\nimport HazardCoverSection from \"./HazardCoverSection\";\r\nimport HazardOperation from \"./HazardOperation\";\r\nimport HazardOrganisation from \"./HazardOrganisation\";\r\nimport HazardCurrentEvent from \"./HazardCurrentEvent\";\r\nimport HazardPastEvent from \"./HazardPastEvent\";\r\nimport HazardAccordianSection from \"./HazardAccordianSection\";\r\nimport Discussion from \"../../components/common/disussion\";\r\nimport apiService from \"../../services/apiService\";\r\n\r\nconst countrydescription =\r\n  \"-country -description -hazard_type -created_at -region -start_date -status -syndrome -title -timeline -user -world_region -_id -part\";\r\nconst createMarkup = (htmlContent: string) => {\r\n  return { __html: htmlContent };\r\n};\r\n\r\ninterface HazardShowProps {\r\n  routes: string[];\r\n}\r\n\r\nconst HazardShow = (props: HazardShowProps) => {\r\n  const { t, i18n } = useTranslation('common');\r\n  const currentLang = i18n.language === \"fr\" ? \"en\" : i18n.language;\r\n  const initialVal = {\r\n    title: \"\",\r\n    description: \"\",\r\n    picture: \"\",\r\n  };\r\n\r\n  const [hazardData, setHazardData] = useState<any>(initialVal);\r\n  const [hazardDataLoading, setHazardDataLoading] = useState<boolean>(false);\r\n  const [hazardPastEventData, setHazardPastEventData] = useState<any[]>([]);\r\n  const [hazardCurrentEventData, setHazardCurrentEventData] = useState<any[]>([]);\r\n  const [hazardOperationData, setHazardOperationData] = useState<any[]>([]);\r\n  const [hazardInstitutionData, setHazardInstitutionData] = useState<any[]>([]);\r\n  const [sectionOne, setSectionOne] = useState<boolean>(false);\r\n  const [sectionTwo, setSectionTwo] = useState<boolean>(false);\r\n  const [sectionThree, setSectionThree] = useState<boolean>(true);\r\n  const [loading] = useState<boolean>(false);\r\n  const [source, setSource] = useState<string>(\"\");\r\n  //Showing Image & document\r\n  const [document, setDocuments] = useState<any[]>([]);\r\n  const [updateDocument, setUpdateDocument] = useState<any[]>([]);\r\n  const [images, setImages] = useState<any[]>([]);\r\n  const [docSrc] = useState<any[]>([]);\r\n  const [imgSrc, setImgSrc] = useState<any[]>([]);\r\n  const [sectionFour, setSectionFour] = useState<boolean>(false);\r\n  //end\r\n\r\n  const hazardEventParams = {\r\n    query: {},\r\n    limit: \"~\",\r\n    sort: { title: \"asc\" },\r\n  };\r\n\r\n  const hazardDocSort = (data: { columnSelector: string; sortDirection: string }) => {\r\n    const operationDocParams = {\r\n      sort: {},\r\n      limit: \"~\",\r\n      Doctable: true,\r\n      collation: \"en\",\r\n      select: countrydescription,\r\n    };\r\n    operationDocParams.sort = {\r\n      [data.columnSelector]: data.sortDirection,\r\n    };\r\n    getDocuments(operationDocParams);\r\n  };\r\n  const hazardDocUpdateSort = (data: { columnSelector: string; sortDirection: string }) => {\r\n    const UpdateParams = {\r\n      sort: {},\r\n      limit: \"~\",\r\n      DocUpdatetable: true,\r\n      collation: \"en\",\r\n      select: countrydescription,\r\n    };\r\n    UpdateParams.sort = {\r\n      [data.columnSelector]: data.sortDirection,\r\n    };\r\n    getUpdateDocuments(UpdateParams);\r\n  };\r\n  const getDocuments = async (operationParams: any) => {\r\n    const _imgSrc: any[] = [];\r\n    const _docSrc: any[] = [];\r\n    const _documents: any[] = [];\r\n    const _images: any[] = [];\r\n    const response = await apiService.get(\r\n      `/hazard/${props.routes[1]}`,\r\n      operationParams\r\n    );\r\n    if (response && response.data && response.data.length > 0) {\r\n      response.data.forEach((element: any, index: any) => {\r\n        element.document &&\r\n          element.document.length > 0 &&\r\n          element.document.map((ele: any, i: number) => {\r\n            const description = element.document[i].docsrc;\r\n            ele.description = description;\r\n            _documents.push(ele);\r\n          });\r\n        element.images &&\r\n          element.images.length > 0 &&\r\n          element.images.map((image: any, imageIndex: any) => {\r\n            _images.push(image);\r\n          });\r\n\r\n        element.images_src &&\r\n          element.images_src.length > 0 &&\r\n          element.images_src.map((src: any, srcIndex: any) => {\r\n            _imgSrc.push(src);\r\n          });\r\n      });\r\n      setDocuments(_documents);\r\n      var distinctImageIds = _images?.reduce((acc, cur) => {\r\n        const existingItem = acc.find((item: any) => cur._id === item._id);\r\n        if(existingItem) {\r\n           existingItem.count++;\r\n        }\r\n        else {\r\n           acc.push({...cur, count: 1});\r\n        }\r\n        return acc;\r\n     }, []);\r\n      setImages(distinctImageIds.flat(Infinity));\r\n      setImgSrc(_imgSrc.flat(Infinity));\r\n    }\r\n  };\r\n\r\n  const getUpdateDocuments = async (UpdateParams: any) => {\r\n    const _imgSrc: any[] = [];\r\n    const _docSrc: any[] = [];\r\n    const _documents: any[] = [];\r\n    const _images: any[] = [];\r\n    const response = await apiService.get(\r\n      `/hazard/${props.routes[1]}`,\r\n      UpdateParams\r\n    );\r\n    if (response && response.data && response.data.length > 0) {\r\n      response.data.forEach((element: any, index: any) => {\r\n        element.document &&\r\n          element.document.length > 0 &&\r\n          element.document.map((ele: any, i: number) => {\r\n            const description = element.document[i].docsrc;\r\n            ele.description = description;\r\n            _documents.push(ele);\r\n          });\r\n      });\r\n      setUpdateDocument(_documents);\r\n    }\r\n  };\r\n  const getHazardData = async (hazardParams: any) => {\r\n    setHazardDataLoading(true);\r\n    const response = await apiService.get(\r\n      `/hazard/${props.routes[1]}`,\r\n      hazardParams\r\n    );\r\n\r\n    if (response) {\r\n      response[\"picture\"] =\r\n        response?.picture && response.picture._id\r\n          ? `${process.env.API_SERVER}/image/show/${response.picture._id}`\r\n          : \"/images/disease-placeholder.3f65b286.jpg\";\r\n\r\n      if (response?.picture_source) {\r\n        setSource(response.picture_source);\r\n      }\r\n      setHazardData(response);\r\n      setHazardDataLoading(false);\r\n    }\r\n    setHazardDataLoading(false);\r\n  };\r\n\r\n  const getPastEventsForCountry = async (eventParams: any) => {\r\n    const response = await apiService.get(\r\n      `/hazard/${props.routes[1]}/events/Closed`,\r\n      eventParams\r\n    );\r\n    const dataResponse = response && response.data ? response.data : [];\r\n    setHazardPastEventData(dataResponse);\r\n  };\r\n\r\n  const getCurrentEventsForCountry = async (eventParams: any) => {\r\n    const response = await apiService.get(\r\n      `/hazard/${props.routes[1]}/events/Current`,\r\n      eventParams\r\n    );\r\n    const dataResponse = response && response.data ? response.data : [];\r\n    setHazardCurrentEventData(dataResponse);\r\n  };\r\n\r\n  const getOperationForCountry = async (operationParams: any) => {\r\n    const response = await apiService.get(\r\n      `/hazard/${props.routes[1]}/operations`,\r\n      operationParams\r\n    );\r\n    const dataResponse = response && response.data ? response.data : [];\r\n    setHazardOperationData(dataResponse);\r\n  };\r\n\r\n  const getInsitutionForCountry = async (insitutionParams: any) => {\r\n    const response = await apiService.get(\r\n      `/hazard/${props.routes[1]}/institutions`,\r\n      insitutionParams\r\n    );\r\n    const dataResponse = response && response.data ? response.data : [];\r\n    setHazardInstitutionData(dataResponse);\r\n  };\r\n\r\n  useEffect(() => {\r\n    const operationParams = {\r\n      sort: { doc_created_at: \"asc\" },\r\n      limit: \"~\",\r\n      Doctable: true,\r\n      collation: \"en\",\r\n      select: countrydescription,\r\n    };\r\n    const UpdateParams = {\r\n      sort: { doc_created_at: \"asc\" },\r\n      limit: \"~\",\r\n      DocUpdatetable: true,\r\n      collation: \"en\",\r\n      select: countrydescription,\r\n    };\r\n    if (props.routes && props.routes[1]) {\r\n      getHazardData({});\r\n      getPastEventsForCountry(hazardEventParams);\r\n      getCurrentEventsForCountry(hazardEventParams);\r\n      getOperationForCountry(hazardEventParams);\r\n      getInsitutionForCountry(hazardEventParams);\r\n      getDocuments(operationParams);\r\n      getUpdateDocuments(UpdateParams);\r\n    }\r\n  }, []);\r\n\r\n  const DiscussionComponent = () => {\r\n    return (\r\n      <Accordion.Item eventKey=\"1\">\r\n        <Accordion.Header onClick={() => setSectionThree(!sectionThree)}>\r\n          <div className=\"cardTitle\">{t(\"discussions\")}</div>\r\n          <div className=\"cardArrow\">\r\n            {sectionThree ? (\r\n              <FontAwesomeIcon icon={faPlus} color=\"#fff\" />\r\n            ) : (\r\n              <FontAwesomeIcon icon={faMinus} color=\"#fff\" />\r\n            )}\r\n          </div>\r\n        </Accordion.Header>\r\n        <Accordion.Body>\r\n          <Discussion\r\n            type=\"hazard\"\r\n            id={props && props.routes ? props.routes[1] : null}\r\n          />\r\n        </Accordion.Body>\r\n      </Accordion.Item>\r\n    );\r\n  };\r\n\r\n  const CanViewDiscussionUpdate = canViewDiscussionUpdate(() => (\r\n    <DiscussionComponent />\r\n  ));\r\n\r\n  const regex = new RegExp(Regex_symbol());\r\n\r\n  const isValidLink = regex.test(source);\r\n  let propData = {\r\n    t: t,\r\n    images: images,\r\n    imgSrc: imgSrc,\r\n    routeData: props,\r\n    documentAccoirdianProps: {\r\n      loading: loading,\r\n      Document: document,\r\n      updateDocument: updateDocument,\r\n      hazardDocSort: hazardDocSort,\r\n      hazardDocUpdateSort: hazardDocUpdateSort,\r\n      docSrc: docSrc,\r\n    }\r\n  }\r\n\r\n  return (\r\n    <div className=\"hazardDetails\">\r\n      <UpdatePopup routes={props.routes} />\r\n      {!hazardDataLoading && !hazardData.title ? (\r\n        <div className=\"nodataFound\">{t(\"vspace.Nodataavailable\")}</div>\r\n      ) : (\r\n        <>\r\n          {hazardData && hazardData.picture ? (\r\n            <>\r\n              {hazard_title_func(hazardData, currentLang)}\r\n              <Row>\r\n                <Col className=\"mt-2 ps-4\" md={{ span: 6, offset: 6 }}>\r\n                  <div>\r\n                    <p className=\" py-1\" style={{ fontSize: \"12px\" }}>\r\n                      <i>{t(\"imageSourceCredit\")}: </i>\r\n                      {source ? (\r\n                        valid_func(isValidLink, source)\r\n                      ) : (\r\n                        <span style={{ color: \"#234799\" }}>\r\n                          {t(\"noSourceFound\")}\r\n                        </span>\r\n                      )}\r\n                    </p>\r\n                  </div>\r\n                </Col>\r\n              </Row>\r\n            </>\r\n          ) : (\r\n            <div className=\"d-flex justify-content-center p-5\">\r\n              <Spinner animation=\"grow\" />\r\n            </div>\r\n          )}\r\n          <br />\r\n          <Row>\r\n            <Col>{hazard_func(t, hazardCurrentEventData)}</Col>\r\n            <Col>{hazardOperation_func(t, hazardOperationData)}</Col>\r\n          </Row>\r\n          <br />\r\n          <Row>\r\n            <Col>{hazard_organisation_func(t, hazardInstitutionData)}</Col>\r\n            <Col>{hazard_event_func(t, hazardPastEventData)}</Col>\r\n          </Row>\r\n          <HazardAccordianSection {...propData} />\r\n        </>\r\n      )}\r\n    </div>\r\n  );\r\n\r\n  function Regex_symbol(): string | RegExp {\r\n    return \"^(http[s]?:\\\\/\\\\/(www\\\\.)?|ftp:\\\\/\\\\/(www\\\\.)?|www\\\\.){1}([0-9A-Za-z-\\\\.@:%_+~#=]+)+((\\\\.[a-zA-Z]{2,3})+)(/(.)*)?(\\\\?(.)*)?\";\r\n  }\r\n};\r\n\r\nexport default HazardShow;\r\nfunction valid_func(isValidLink: boolean, source: string): React.ReactNode {\r\n  return isValidLink && source ? (\r\n    <a target=\"_blank\" href={source}>\r\n      {source}\r\n    </a>\r\n  ) : (\r\n    <span style={{ color: \"#234799\" }}>{source}</span>\r\n  );\r\n}\r\n\r\nfunction hazard_title_func(hazardData: any, currentLang: string) {\r\n  return <HazardCoverSection hazardData={hazardData} currentLang={currentLang} />\r\n}\r\n\r\nfunction hazard_event_func(t: (key: string) => string, hazardPastEventData: any[]) {\r\n  return <HazardPastEvent t={t} hazardPastEventData={hazardPastEventData} />\r\n}\r\n\r\nfunction hazard_organisation_func(t: (key: string) => string, hazardInstitutionData: any[]) {\r\n  return <HazardOrganisation t={t} hazardInstitutionData={hazardInstitutionData} />\r\n}\r\n\r\nfunction hazardOperation_func(t: (key: string) => string, hazardOperationData: any[]) {\r\n  return <HazardOperation t={t} hazardOperationData={hazardOperationData} />\r\n}\r\n\r\nfunction hazard_func(t: (key: string) => string, hazardCurrentEventData: any[]) {\r\n  return <HazardCurrentEvent t={t} hazardCurrentEventData={hazardCurrentEventData} />\r\n}\r\n", "\"use client\";\n\nimport * as React from 'react';\nimport classNames from 'classnames';\nimport { useBootstrapPrefix } from './ThemeProvider';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst CardBody = /*#__PURE__*/React.forwardRef(({\n  className,\n  bsPrefix,\n  as: Component = 'div',\n  ...props\n}, ref) => {\n  bsPrefix = useBootstrapPrefix(bsPrefix, 'card-body');\n  return /*#__PURE__*/_jsx(Component, {\n    ref: ref,\n    className: classNames(className, bsPrefix),\n    ...props\n  });\n});\nCardBody.displayName = 'CardBody';\nexport default CardBody;", "\"use client\";\n\nimport * as React from 'react';\nimport classNames from 'classnames';\nimport { useBootstrapPrefix } from './ThemeProvider';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst CardFooter = /*#__PURE__*/React.forwardRef(({\n  className,\n  bsPrefix,\n  as: Component = 'div',\n  ...props\n}, ref) => {\n  bsPrefix = useBootstrapPrefix(bsPrefix, 'card-footer');\n  return /*#__PURE__*/_jsx(Component, {\n    ref: ref,\n    className: classNames(className, bsPrefix),\n    ...props\n  });\n});\nCardFooter.displayName = 'CardFooter';\nexport default CardFooter;", "\"use client\";\n\nimport classNames from 'classnames';\nimport * as React from 'react';\nimport { useMemo } from 'react';\nimport { useBootstrapPrefix } from './ThemeProvider';\nimport CardHeaderContext from './CardHeaderContext';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst CardHeader = /*#__PURE__*/React.forwardRef(({\n  bsPrefix,\n  className,\n  // Need to define the default \"as\" during prop destructuring to be compatible with styled-components github.com/react-bootstrap/react-bootstrap/issues/3595\n  as: Component = 'div',\n  ...props\n}, ref) => {\n  const prefix = useBootstrapPrefix(bsPrefix, 'card-header');\n  const contextValue = useMemo(() => ({\n    cardHeaderBsPrefix: prefix\n  }), [prefix]);\n  return /*#__PURE__*/_jsx(CardHeaderContext.Provider, {\n    value: contextValue,\n    children: /*#__PURE__*/_jsx(Component, {\n      ref: ref,\n      ...props,\n      className: classNames(className, prefix)\n    })\n  });\n});\nCardHeader.displayName = 'CardHeader';\nexport default CardHeader;", "\"use client\";\n\nimport classNames from 'classnames';\nimport * as React from 'react';\nimport { useBootstrapPrefix } from './ThemeProvider';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst CardImg = /*#__PURE__*/React.forwardRef(\n// Need to define the default \"as\" during prop destructuring to be compatible with styled-components github.com/react-bootstrap/react-bootstrap/issues/3595\n({\n  bsPrefix,\n  className,\n  variant,\n  as: Component = 'img',\n  ...props\n}, ref) => {\n  const prefix = useBootstrapPrefix(bsPrefix, 'card-img');\n  return /*#__PURE__*/_jsx(Component, {\n    ref: ref,\n    className: classNames(variant ? `${prefix}-${variant}` : prefix, className),\n    ...props\n  });\n});\nCardImg.displayName = 'CardImg';\nexport default CardImg;", "\"use client\";\n\nimport * as React from 'react';\nimport classNames from 'classnames';\nimport { useBootstrapPrefix } from './ThemeProvider';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst CardImgOverlay = /*#__PURE__*/React.forwardRef(({\n  className,\n  bsPrefix,\n  as: Component = 'div',\n  ...props\n}, ref) => {\n  bsPrefix = useBootstrapPrefix(bsPrefix, 'card-img-overlay');\n  return /*#__PURE__*/_jsx(Component, {\n    ref: ref,\n    className: classNames(className, bsPrefix),\n    ...props\n  });\n});\nCardImgOverlay.displayName = 'CardImgOverlay';\nexport default CardImgOverlay;", "\"use client\";\n\nimport * as React from 'react';\nimport classNames from 'classnames';\nimport { useBootstrapPrefix } from './ThemeProvider';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst CardLink = /*#__PURE__*/React.forwardRef(({\n  className,\n  bsPrefix,\n  as: Component = 'a',\n  ...props\n}, ref) => {\n  bsPrefix = useBootstrapPrefix(bsPrefix, 'card-link');\n  return /*#__PURE__*/_jsx(Component, {\n    ref: ref,\n    className: classNames(className, bsPrefix),\n    ...props\n  });\n});\nCardLink.displayName = 'CardLink';\nexport default CardLink;", "\"use client\";\n\nimport * as React from 'react';\nimport classNames from 'classnames';\nimport { useBootstrapPrefix } from './ThemeProvider';\nimport divWithClassName from './divWithClassName';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst DivStyledAsH6 = divWithClassName('h6');\nconst CardSubtitle = /*#__PURE__*/React.forwardRef(({\n  className,\n  bsPrefix,\n  as: Component = DivStyledAsH6,\n  ...props\n}, ref) => {\n  bsPrefix = useBootstrapPrefix(bsPrefix, 'card-subtitle');\n  return /*#__PURE__*/_jsx(Component, {\n    ref: ref,\n    className: classNames(className, bsPrefix),\n    ...props\n  });\n});\nCardSubtitle.displayName = 'CardSubtitle';\nexport default CardSubtitle;", "\"use client\";\n\nimport * as React from 'react';\nimport classNames from 'classnames';\nimport { useBootstrapPrefix } from './ThemeProvider';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst CardText = /*#__PURE__*/React.forwardRef(({\n  className,\n  bsPrefix,\n  as: Component = 'p',\n  ...props\n}, ref) => {\n  bsPrefix = useBootstrapPrefix(bsPrefix, 'card-text');\n  return /*#__PURE__*/_jsx(Component, {\n    ref: ref,\n    className: classNames(className, bsPrefix),\n    ...props\n  });\n});\nCardText.displayName = 'CardText';\nexport default CardText;", "\"use client\";\n\nimport * as React from 'react';\nimport classNames from 'classnames';\nimport { useBootstrapPrefix } from './ThemeProvider';\nimport divWithClassName from './divWithClassName';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst DivStyledAsH5 = divWithClassName('h5');\nconst CardTitle = /*#__PURE__*/React.forwardRef(({\n  className,\n  bsPrefix,\n  as: Component = DivStyledAsH5,\n  ...props\n}, ref) => {\n  bsPrefix = useBootstrapPrefix(bsPrefix, 'card-title');\n  return /*#__PURE__*/_jsx(Component, {\n    ref: ref,\n    className: classNames(className, bsPrefix),\n    ...props\n  });\n});\nCardTitle.displayName = 'CardTitle';\nexport default CardTitle;", "\"use client\";\n\nimport classNames from 'classnames';\nimport * as React from 'react';\nimport { useBootstrapPrefix } from './ThemeProvider';\nimport CardBody from './CardBody';\nimport CardFooter from './CardFooter';\nimport CardHeader from './CardHeader';\nimport CardImg from './CardImg';\nimport CardImgOverlay from './CardImgOverlay';\nimport CardLink from './CardLink';\nimport CardSubtitle from './CardSubtitle';\nimport CardText from './CardText';\nimport CardTitle from './CardTitle';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst Card = /*#__PURE__*/React.forwardRef(({\n  bsPrefix,\n  className,\n  bg,\n  text,\n  border,\n  body = false,\n  children,\n  // Need to define the default \"as\" during prop destructuring to be compatible with styled-components github.com/react-bootstrap/react-bootstrap/issues/3595\n  as: Component = 'div',\n  ...props\n}, ref) => {\n  const prefix = useBootstrapPrefix(bsPrefix, 'card');\n  return /*#__PURE__*/_jsx(Component, {\n    ref: ref,\n    ...props,\n    className: classNames(className, prefix, bg && `bg-${bg}`, text && `text-${text}`, border && `border-${border}`),\n    children: body ? /*#__PURE__*/_jsx(CardBody, {\n      children: children\n    }) : children\n  });\n});\nCard.displayName = 'Card';\nexport default Object.assign(Card, {\n  Img: CardImg,\n  Title: CardTitle,\n  Subtitle: CardSubtitle,\n  Body: CardBody,\n  Link: CardLink,\n  Text: CardText,\n  Header: CardHeader,\n  Footer: CardFooter,\n  ImgOverlay: CardImgOverlay\n});", "//Import Library\r\nimport { Card } from \"react-bootstrap\";\r\nimport Link from \"next/link\";\r\n\r\n//Import services/components\r\nimport { useTranslation } from 'next-i18next';\r\n\r\ninterface HazardPastEventProps {\r\n  t: (key: string) => string;\r\n  hazardPastEventData: any[];\r\n}\r\n\r\nconst HazardPastEvent = (props: HazardPastEventProps) => {\r\n    const hazardPastEventData = props.hazardPastEventData;\r\n    const { t } = useTranslation('common');\r\n    return (\r\n        <>\r\n            <div className=\"rki-carousel-card\">\r\n                <Card className=\"infoCard\">\r\n                    <Card.Header className=\"text-center\">\r\n                    {t(\"hazardshow.pastevents\")}\r\n                    </Card.Header>\r\n                    <Card.Body className=\"hazardBody\">\r\n                    {hazardPastEventData && hazardPastEventData.length > 0 ? (\r\n                        hazardPastEventData.map((item, index) => (\r\n                        <ul className=\"ulItems\">\r\n                            <li key=\"index\" className=\"liItems\">\r\n                            <Link\r\n                                key={item._id}\r\n                                href=\"/event/[...routes]\"\r\n                                as={`/event/show/${item._id}`}\r\n                            >\r\n                                {item && item.title ? `${item.title}` : \"\"}\r\n                            </Link>\r\n                            <span>\r\n                                {\" \"}\r\n                                ({item && item.country ? `${item.country.title}` : \"\"})\r\n                            </span>\r\n                            </li>\r\n                        </ul>\r\n                        ))\r\n                    ) : (\r\n                        <span className=\"text-center\">{t(\"noRecordFound\")}</span>\r\n                    )}\r\n                    </Card.Body>\r\n                </Card>\r\n            </div>\r\n        </>\r\n    );\r\n};\r\n\r\nexport default HazardPastEvent;", "//Import Library\r\nimport { Card } from \"react-bootstrap\";\r\nimport Link from \"next/link\";\r\n\r\n//Import services/components\r\nimport { useTranslation } from 'next-i18next';\r\n\r\ninterface HazardOrganisationProps {\r\n  t: (key: string) => string;\r\n  hazardInstitutionData: any[];\r\n}\r\n\r\nconst HazardOrganisation = (props: HazardOrganisationProps) => {\r\n    let hazardInstitutionData = props.hazardInstitutionData;\r\n    const { t } = useTranslation('common');\r\n    return (\r\n        <>\r\n            <div className=\"rki-carousel-card\">\r\n                <Card className=\"infoCard\">\r\n                    <Card.Header className=\"text-center\">\r\n                    {t(\"hazardshow.organisations\")}\r\n                    </Card.Header>\r\n                    <Card.Body className=\"hazardBody\">\r\n                    {hazardInstitutionData && hazardInstitutionData.length > 0 ? (\r\n                        hazardInstitutionData.map((item, index) => (\r\n                        <ul className=\"ulItems\">\r\n                            <li key={index} className=\"liItems\">\r\n                            <Link\r\n                                href=\"/institution/[...routes]\"\r\n                                as={`/institution/show/${item._id}`}\r\n                            >\r\n                                {item && item.title ? `${item.title}` : \"\"}\r\n                            </Link>\r\n                            <span>\r\n                                {\" \"}\r\n                                (\r\n                                {item && item.address && item.address.country\r\n                                ? `${item.address.country.title}`\r\n                                : \"\"}\r\n                                )\r\n                            </span>\r\n                            </li>\r\n                        </ul>\r\n                        ))\r\n                    ) : (\r\n                        <span className=\"text-center\">{t(\"noRecordFound\")}</span>\r\n                    )}\r\n                    </Card.Body>\r\n                </Card>\r\n            </div>\r\n        </>\r\n    );\r\n};\r\nexport default HazardOrganisation;", "//Import Library\r\nimport { Card } from \"react-bootstrap\";\r\nimport Link from \"next/link\";\r\n\r\n//Import services/components\r\nimport { useTranslation } from 'next-i18next';\r\n\r\ninterface HazardCurrentEventProps {\r\n  t: (key: string) => string;\r\n  hazardCurrentEventData: any[];\r\n}\r\n\r\nconst HazardCurrentEvent = (props: HazardCurrentEventProps) => {\r\n    const hazardCurrentEventData = props.hazardCurrentEventData;\r\n    const { t } = useTranslation('common');\r\n    return (\r\n        <>\r\n            <div className=\"rki-carousel-card\">\r\n                <Card className=\"infoCard\">\r\n                    <Card.Header className=\"text-center\">\r\n                    {t(\"hazardshow.currentevents\")}\r\n                    </Card.Header>\r\n                    <Card.Body className=\"hazardBody\">\r\n                    {hazardCurrentEventData && hazardCurrentEventData.length > 0 ? (\r\n                        hazardCurrentEventData.map((item, index) => {\r\n                        return (\r\n                            <ul className=\"ulItems\">\r\n                                <li key={index} className=\"liItems\">\r\n                                    <Link\r\n                                    key={item._id}\r\n                                    href=\"/event/[...routes]\"\r\n                                    as={`/event/show/${item._id}`}\r\n                                    >\r\n                                    {item && item.title ? `${item.title}` : \"\"}\r\n                                    </Link>\r\n                                    <span>\r\n                                    {\" \"}\r\n                                    ({item && item.country ? `${item.country.title}` : \"\"})\r\n                                    </span>\r\n                                </li>\r\n                            </ul>\r\n                        );\r\n                        })\r\n                    ) : (\r\n                        <span className=\"text-center\">{t(\"noRecordFound\")}</span>\r\n                    )}\r\n                    </Card.Body>\r\n                </Card>\r\n            </div>\r\n        </>\r\n    );\r\n}\r\n\r\nexport default HazardCurrentEvent;", "//Import Library\r\nimport Link from \"next/link\";\r\nimport { Card } from \"react-bootstrap\";\r\n\r\n//Import services/components\r\nimport { useTranslation } from 'next-i18next';\r\n\r\ninterface HazardOperationProps {\r\n  t: (key: string) => string;\r\n  hazardOperationData: any[];\r\n}\r\n\r\nconst HazardOperation = (props: HazardOperationProps) => {\r\n    let hazardOperationData = props.hazardOperationData;\r\n    const { t } = useTranslation('common');\r\n    return (\r\n        <>\r\n            <div className=\"rki-carousel-card\">\r\n                <Card className=\"infoCard\">\r\n                    <Card.Header className=\"text-center\">\r\n                    {t(\"hazardshow.currentoperations\")}\r\n                    </Card.Header>\r\n\r\n                    <Card.Body className=\"hazardBody\">\r\n                    {hazardOperationData && hazardOperationData.length > 0 ? (\r\n                        hazardOperationData.map((item, index) => (\r\n                        <ul className=\"ulItems\">\r\n                            <li key={index} className=\"liItems\">\r\n                            <Link\r\n                                key={item._id}\r\n                                href=\"/operation/[...routes]\"\r\n                                as={`/operation/show/${item._id}`}\r\n                            >\r\n                                {item && item.title ? `${item.title}` : \"\"}\r\n                            </Link>\r\n                            <span>\r\n                                {\" \"}\r\n                                ({item && item.country ? `${item.country.title}` : \"\"})\r\n                            </span>\r\n                            </li>\r\n                        </ul>\r\n                        ))\r\n                    ) : (\r\n                        <span className=\"text-center\">{t(\"noSourceFound\")}</span>\r\n                    )}\r\n                    </Card.Body>\r\n                </Card>\r\n            </div>\r\n        </>\r\n    );\r\n};\r\n\r\nexport default HazardOperation;", "\"use client\";\n\nimport * as React from 'react';\nconst context = /*#__PURE__*/React.createContext(null);\ncontext.displayName = 'CardHeaderContext';\nexport default context;", "//Import Library\r\nimport { Col, Row } from \"react-bootstrap\";\r\n\r\n//Import services/components\r\nimport ReadMoreContainer from \"../../components/common/readMore/readMore\";\r\n\r\ninterface HazardCoverSectionProps {\r\n  hazardData: {\r\n    title?: {\r\n      [key: string]: string;\r\n    };\r\n    description?: {\r\n      [key: string]: string;\r\n    };\r\n    picture?: string;\r\n  };\r\n  currentLang: string;\r\n}\r\n\r\nconst HazardCoverSection = (props: HazardCoverSectionProps) => {\r\n    const hazardData = props.hazardData;\r\n    const currentLang = props.currentLang;\r\n    return(\r\n        <>\r\n            <Row>\r\n                <Col className=\"ps-4\">\r\n                    <h2>\r\n                    {hazardData.title && hazardData.title[currentLang]\r\n                        ? hazardData.title[currentLang]\r\n                        : \"\"}\r\n                    </h2>\r\n                    <ReadMoreContainer\r\n                    description={\r\n                        hazardData.description && hazardData.description[currentLang]\r\n                        ? hazardData.description[currentLang]\r\n                        : \"\"\r\n                    }\r\n                    />\r\n                </Col>\r\n                <Col style={{ display: \"flex\" }}>\r\n                    <img\r\n                    src={hazardData.picture}\r\n                    style={{ width: \"100%\", height: \"400px\", backgroundSize: \"cover\" }}\r\n                    alt=\"banner\"\r\n                    />\r\n                </Col>\r\n            </Row>\r\n        </>\r\n    )\r\n};\r\n\r\nexport default HazardCoverSection;"], "names": ["countrydescription", "props", "t", "hazardCurrentEventData", "hazardOperationData", "HazardShow", "i18n", "useTranslation", "currentLang", "language", "hazardData", "setHazardData", "useState", "title", "description", "picture", "hazardDataLoading", "setHazardDataLoading", "hazardPastEventData", "setHazardPastEventData", "setHazardCurrentEventData", "setHazardOperationData", "hazardInstitutionData", "setHazardInstitutionData", "sectionOne", "setSectionOne", "sectionTwo", "setSectionTwo", "sectionThree", "setSectionThree", "loading", "source", "setSource", "document", "setDocuments", "updateDocument", "setUpdateDocument", "images", "setImages", "docSrc", "imgSrc", "setImgSrc", "sectionFour", "setSectionFour", "hazardEventParams", "query", "limit", "sort", "getDocuments", "operationParams", "_imgSrc", "_documents", "_images", "response", "apiService", "get", "routes", "data", "length", "for<PERSON>ach", "element", "index", "map", "ele", "i", "docsrc", "push", "image", "imageIndex", "images_src", "src", "srcIndex", "distinctImageIds", "reduce", "acc", "cur", "existingItem", "find", "_id", "item", "count", "flat", "Infinity", "getUpdateDocuments", "UpdateParams", "getHazardData", "hazardParams", "process", "picture_source", "getPastEventsForCountry", "eventParams", "dataResponse", "getCurrentEventsForCountry", "getOperationForCountry", "getInsitutionForCountry", "insitutionParams", "useEffect", "doc_created_at", "Doctable", "collation", "select", "DocUpdatetable", "DiscussionComponent", "Accordion", "<PERSON><PERSON>", "eventKey", "Header", "onClick", "div", "className", "FontAwesomeIcon", "icon", "faPlus", "color", "faMinus", "Body", "Discussion", "type", "id", "canViewDiscussionUpdate", "isValidLink", "regex", "test", "UpdatePopup", "HazardCoverSection", "Row", "Col", "md", "span", "offset", "p", "style", "fontSize", "valid_func", "a", "target", "href", "Spinner", "animation", "br", "hazard_func", "HazardCurrentEvent", "hazardOperation_func", "HazardOperation", "hazard_organisation_func", "HazardOrganisation", "HazardPastEvent", "HazardAccordianSection", "routeData", "documentAccoirdianProps", "Document", "hazardDocSort", "operationDocParams", "columnSelector", "sortDirection", "hazardDocUpdateSort", "CardBody", "React", "ref", "bsPrefix", "as", "Component", "useBootstrapPrefix", "_jsx", "classNames", "displayName", "<PERSON><PERSON><PERSON>er", "<PERSON><PERSON><PERSON><PERSON>", "prefix", "contextValue", "useMemo", "cardHeaderBsPrefix", "CardHeaderContext", "Provider", "value", "children", "CardImg", "variant", "CardImgOverlay", "CardLink", "DivStyledAsH6", "divWithClassName", "CardSubtitle", "CardText", "DivStyledAsH5", "CardTitle", "Card", "bg", "text", "border", "body", "Object", "assign", "Img", "Title", "Subtitle", "Link", "Text", "Footer", "ImgOverlay", "ul", "li", "country", "address", "context", "h2", "ReadMoreContainer", "display", "img", "width", "height", "backgroundSize", "alt"], "sourceRoot": "", "ignoreList": [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 15]}