{"version": 3, "file": "static/chunks/1147-e70d423b6a38a16c.js", "mappings": "0KAae,SAASA,EAAwBC,CAAmC,EAEjF,GAAM,CAACC,CAAC,mBAAEC,CAAiB,iBAAEC,CAAe,CAAC,CAAGH,EAC1C,CAACI,EAAYC,EAAc,CAAGC,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAQ,EAAE,EAChDC,EAAc,UAClB,IAAMC,EAAmB,CACvBC,MAAO,CAAEC,OAAQ,EAAE,EACnBC,KAAM,CAAEC,WAAY,MAAO,EAC3BC,MAAO,GACPC,OAAQ,kOACV,EACMC,EAAoB,MAAMC,GAC5BD,IACFP,EAAYC,KAAK,CAACC,MAAM,CAACO,IAAI,CAACF,GAEhC,IAAMG,EAAW,MAAMC,EAAAA,CAAUA,CAACC,GAAG,CAAC,QAASZ,GAC3CU,GAAYG,MAAMC,OAAO,CAACJ,EAASK,IAAI,GAAKL,EAASK,IAAI,CAACC,MAAM,CAAG,GAAG,EAC1DN,EAASK,IAAI,CAE/B,EAEMP,EAAoB,UACxB,IAAME,EAAW,MAAMC,EAAAA,CAAUA,CAACC,GAAG,CAAC,eAAgB,CAACX,MAAO,CAACgB,MAAO,SAAS,CAAC,SAChF,EAAIP,KAAYA,EAASK,IAAI,EAAIL,EAASK,IAAI,CAACC,MAAM,CAAG,GAAG,EACzCD,IAAI,CAAC,EAAE,CAACG,GAAG,EAS/B,MAJAC,CAAAA,EAAAA,EAAAA,SAAAA,CAASA,CAAC,KACRpB,GACF,EAAE,EAAE,EAGF,WAACqB,MAAAA,CAAIC,UAAU,0CACb,UAACC,KAAAA,UAAI7B,EAAE,iBACP,UAAC8B,EAAAA,OAAaA,CAAAA,CAAC9B,EAAGA,EAAG+B,cAAe5B,EAAYD,gBAAiBA,EAAiBD,kBAAmBA,MAG3G,2ECgBA,MA/CkD,OAAC,CACjD+B,OAAO,QAAQ,CACfC,IA6CaC,CA7CR,EAAE,SA6CkBA,EA5CzBC,EAAY,EAAE,MACdC,CAAI,MACJC,CAAI,UACJC,CAAQ,SACRC,CAAO,OACPf,CAAK,WACLgB,GAAY,CAAK,CAClB,UAsBC,GAAyC,UAAxB,OAAOF,EAASG,GAAG,EAAyC,UAAxB,OAAOH,EAASI,GAAG,CAKtE,UAACC,EAAAA,EAAMA,CAAAA,CACLL,SAAUA,EACVD,KAAMA,EACNb,MAAOA,GAASQ,EAChBQ,UAAWA,EACXD,QA/BgB,CA+BPK,GA9BPL,GAeFA,EAdoB,IADT,EAeHM,KAZNZ,QAYmBa,IAXnBX,OACAC,WACAE,CACF,EAGe,UACbA,EACAS,YAAa,IAAMT,CACrB,EAE6BU,EAEjC,IAIS,IAYX,gICrDA,IAAMC,EAAa,IACjB,GAAM,CAAEjD,CAAC,CAAE,CAAGD,EACRmD,EAAQlD,EAAE,SAChB,MACE,UAAC2B,MAAAA,CAAIC,UAAU,uBACb,WAACuB,KAAAA,WACC,WAACC,KAAAA,CAAGxB,UAAU,iCACZ,UAACyB,IAAAA,CAAEzB,UAAU,kBAAkB,IAAE5B,EAAE,eAErC,WAACoD,KAAAA,CAAGxB,UAAU,gCACZ,UAACyB,IAAAA,CAAEzB,UAAU,kBAAkB,IAAE5B,EAAE,iBAErC,WAACoD,KAAAA,CAAGxB,UAAU,8BACZ,UAACyB,IAAAA,CAAEzB,UAAU,kBAAkB,IAAEsB,EAAO,WAKlD,EA2MA,EAlMyB,IACvB,GAAM,MAAEI,CAAI,CAAE,CAAGC,CAAAA,EAAAA,EAAAA,EAAAA,CAAcA,CAAC,GAiMnBC,OAhMPC,EAAcH,EAAKI,KAgMIF,EAAC,CAhMG,CAC3B,GAAExD,CAAC,mBAAEC,CAAiB,iBAAEC,CAAe,eAAE6B,CAAa,CAAE,CAAGhC,EAC3D,CAAC4D,EAAeC,EAAiB,CAAGvD,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAM,CACtDwD,QAAQ,EACRC,UAAU,EACVC,YAAY,CACd,GACM,CAACC,EAASC,EAAW,CAAG5D,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAQ,EAAE,EAC1C,CAAC6D,EAAcC,EAAgB,CAAQ9D,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAC,CAAC,GACjD,CAAC+D,EAAYC,EAAc,CAAQhE,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAC,CAAC,GAgD7CiE,EAAc,KAClBH,EAAgB,MAChBE,EAAc,KAChB,EAEME,EAAgB,MAAOxE,EAAY+C,EAAaE,KACpDsB,IACAH,EAAgBrB,GAChBuB,EAAc,CACZrC,KAAMjC,EAAMiC,IAAI,CAChBC,GAAIlC,EAAMkC,EAAE,CACZG,KAAMrC,EAAMqC,IAAI,CAChBD,UAAWpC,EAAMoC,SAAS,EAE9B,EAEMqC,EAAkB,KAEtB,IAAMC,EAAkC,EAAE,CAC1CC,IAAAA,OAAS,CAACX,EAAY,IAChBY,EAAGC,OAAO,EAAE,EACW5D,IAAI,CAAC,CAC5BQ,MAAOmD,EAAGnD,KAAK,CACfY,KAAM,YACNH,GAAI0C,EAAGlD,GAAG,CACVU,UAAWwC,EAAGC,OAAO,EAAID,EAAGC,OAAO,CAACnD,GAAG,CACvCgB,IAAKkC,EAAGC,OAAO,CAACC,WAAW,CAAC,EAAE,CAACC,QAAQ,CACvCpC,IAAKiC,EAAGC,OAAO,CAACC,WAAW,CAAC,EAAE,CAACE,SAAS,CACxC1C,KAAM,8BACR,EAEJ,GACAsB,EAAcI,UAAU,EAAG,EACdH,EAAkBD,GAC/BM,EAAW,IAAID,KAAYS,CA+GZd,CA/GqC,CACtD,EAEMqB,EAAgB,KAEpB,IAAMC,EAAiC,EAAE,CACzCP,IAAAA,OAAS,CAFQxE,EAEG,IACdgF,EAAIC,oBAAoB,EAAID,EAAIC,oBAAoB,CAAC5D,MAAM,CAAG,GAChEmD,IAAAA,OAAS,CAACQ,EAAIC,oBAAoB,CAAE,IAC9BP,EAAQQ,eAAe,EAAE,EACHpE,IAAI,CAAC,CAC3BQ,MAAO0D,EAAI1D,KAAK,CAChBY,KAAM,UACNH,GAAIiD,EAAIzD,GAAG,CACXU,UACE+C,EAAIC,oBAAoB,CAAC5D,MAAM,CAAG,GAClC2D,EAAIC,oBAAoB,CAAC,EAAE,CAACC,eAAe,EAC3CF,EAAIC,oBAAoB,CAAC,EAAE,CAACC,eAAe,CAAC3D,GAAG,CACjDgB,IAAKmC,EAAQQ,eAAe,CAACP,WAAW,CAAC,EAAE,CAACC,QAAQ,CACpDpC,IAAKkC,EAAQQ,eAAe,CAACP,WAAW,CAAC,EAAE,CAACE,SAAS,CACrD1C,KAAM,+BACR,EAEJ,EAEJ,GACAsB,EAAcG,QAAQ,EAAG,EACNF,EAAkBD,GACrCM,EAAW,IAAID,KAAYiB,CA+EZtB,CA/EoC,CACrD,EAEMrD,EAAc,KAClB,IAAM+E,EAA6B,EAAE,CACrCX,IAAAA,OAAS,CAAC3C,EAAe,IACnBuD,EAAMV,OAAO,EAAE,EACG5D,IAAI,CAAC,CACvBQ,MAAO8D,EAAM9D,KAAK,CAClBY,KAAM,QACNH,GAAIqD,EAAM7D,GAAG,CACbU,UAAWmD,EAAMV,OAAO,EAAIU,EAAMV,OAAO,CAACnD,GAAG,CAC7CgB,IAAK6C,EAAMV,OAAO,CAACC,WAAW,CAAC,EAAE,CAACC,QAAQ,CAC1CpC,IAAK4C,EAAMV,OAAO,CAACC,WAAW,CAAC,EAAE,CAACE,SAAS,CAC3C1C,KAAM,4BACR,EAEJ,GACAsB,EAAcE,MAAM,EAAG,EACXD,EAAkBD,GAC9BM,EAAW,IAAID,KAAYqB,CAuDZ1B,CAvDgC,CACjD,EAcA,MAZAjC,CAAAA,EAAAA,EAAAA,SAAAA,CAASA,CAAC,KACRsD,GACF,EAAG,CAAC9E,EAAgB,EAEpBwB,CAAAA,EAAAA,EAAAA,SAAAA,CAASA,CAAC,KACR8C,GACF,EAAG,CAACvE,EAAkB,EAEtByB,CAAAA,EAAAA,EAAAA,SAAAA,CAASA,CAAC,KACRpB,GACF,EAAG,CAACyB,EAAc,EAGhB,iCACE,UAACwD,EAAAA,CAAOA,CAAAA,CACNC,QAASlB,EACTZ,SAAUD,EACVgC,OAAQzB,EACRE,aAAcA,EACdE,WAAY,UAtJC,IACjB,GAAM,MAAEsB,CAAI,CAAE,CAAGC,EACXC,EAAqBC,SAqBlBA,CAA2B,EAClC,OAAQC,QAAAA,KAAAA,EAAAA,EAAU1D,IAAV0D,EACN,IAAK,YACH,OAAO7F,EAAkB8F,MAAM,CAC7B,GAAOC,EAAEpB,OAAO,EAAIoB,EAAEpB,OAAO,CAACnD,GAAG,EAAIqE,EAAS3D,SAAS,CAE3D,KAAK,UACH,OAAOjC,EAAgB6F,MAAM,CAC3B,GACEC,EAAEb,oBAAoB,EACtBa,EAAEb,oBAAoB,CAAC5D,MAAM,CAAG,GAChCyE,EAAEb,oBAAoB,CAAC,EAAE,CAACC,eAAe,EACzCY,EAAEb,oBAAoB,CAAC,EAAE,CAACC,eAAe,CAAC3D,GAAG,EAC3CqE,EAAS3D,SAAS,CAE1B,KAAK,QACH,OAAOJ,EAAcgE,MAAM,CACxBC,GAAMA,EAAEpB,OAAO,EAAIoB,EAAEpB,OAAO,CAACnD,GAAG,EAAIqE,EAAS3D,SAAS,CAE7D,CACF,EAzCyCuD,UACzC,GAEEO,OAAOC,IAAI,CAACR,GAAMnE,MAAM,CAAG,QACL4E,GAAtBP,EAGE,MAFF,EAEE,EAACzC,KAAAA,UACEyC,EAAmBQ,GAAG,CAAC,CAACC,EAAWC,IAEhC,UAAClD,KAAAA,UACC,UAACmD,IAAAA,CAAEC,KAAM,WAAI/C,EAAY,kBAAGiC,EAAAA,KAAAA,EAAAA,EAAMtD,IAAI,CAAC,CAAXsD,SAA4B,aAATA,EAAAA,KAAAA,EAAAA,EAAMzD,EAAE,IAARyD,aAAaW,EAAAA,KAAAA,EAAAA,EAAM7E,KAAK,CAAX6E,GADrDC,MAQV,IAwBX,EA0GmBG,CAAWf,KAAMtB,aAE7BT,EAAcG,QAAQ,EACvBH,EAAcI,UAAU,EACxBJ,EAAcE,MAAM,EACpBG,EAAQzC,MAAM,EAAI,EACdyC,EAAQoC,GAAG,CAAC,CAACC,EAAWC,KACtB,GAAID,EAAK5D,GAAG,EAAI4D,EAAK3D,GAAG,CACtB,CADwB,KAEtB,UAACR,EAAAA,CAAYA,CAAAA,CAEXF,KAAMqE,EAAK7E,KAAK,CAChBS,GAAIoE,EAAKpE,EAAE,CACXE,UAAWkE,EAAKlE,SAAS,CACzBC,KAAMiE,EAAKjE,IAAI,CACfC,KAAM,CACJqE,IAAKL,EAAKhE,IAAI,EAEhBE,QAASgC,EACTjC,SAAU+D,GATLC,EAab,GACA,OAEN,UAACrD,EAAAA,CAAWjD,EAAGA,MAGrB,2ECxNA,MARyB,OAAC,UAAEsC,CAAQ,OAQrBqE,OARuBC,CAAY,CAAEC,OAQrBF,EAAC,CAR4B,CAAS,GACnE,MACE,UAACG,EAAAA,EAAUA,CAAAA,CAACxE,SAAUA,EAAUsE,aAAcA,WAC5C,UAACjF,MAAAA,UAAKkF,KAGZ,ECdME,EAAO,mBACPC,EAAQ,qBACRC,EAAO,eACPC,EAAW,kBAgTjB,EA/SkB,CAChB,CACE,YAAe,IA6SJC,OA5SX,EA4SoBA,EAAC,IA5SV,CACT,CACE,MAAS,SACX,EACD,EAEH,CACE,YAAeJ,EACf,QAAW,CACT,CACE,MAAS,SACX,EAEJ,EACA,CACE,YAAeC,EACf,QAAW,CACT,CACE,MAAS,SACX,EACD,EAEH,CACE,YAAe,iBACf,YAAe,WACf,QAAW,CACT,CACE,WAAc,KAChB,EACD,EAEH,CACE,YAAe,yBACf,YAAeE,EACf,QAAW,CACT,CACE,MAAS,SACX,EACD,EAEH,CACE,YAAe,6BACf,YAAe,SACf,QAAW,CACT,CACE,WAAc,KAChB,EACD,EAEH,CACE,YAAe,6BACf,YAAeH,EACf,QAAW,CACT,CACE,MAAS,SACX,EACD,EAEH,CACE,YAAe,0BACf,YAAeG,EACf,QAAW,CACT,CACE,MAAS,SACX,EACD,EAEH,CACE,YAAe,qBACf,YAAeA,EACf,QAAW,CACT,CACE,MAAS,SACX,EACD,EAEH,CACE,YAAe,oBACf,YAAe,WACf,QAAW,CACT,CACE,MAAS,SACX,EACD,EAEH,CACE,YAAe,MACf,QAAW,CACT,CACE,WAAc,KAChB,EACD,EAEH,CACE,YAAe,MACf,YAAe,WACf,QAAW,CACT,CACE,MAAS,SACX,EACD,EAEH,CACE,YAAe,MACf,YAAe,cACf,QAAW,CACT,CACE,WAAc,KAChB,EACD,EAEH,CACE,YAAe,MACf,YAAeH,EACf,QAAW,CACT,CACE,MAAS,SACX,EACD,EAEH,CACE,YAAe,MACf,YAAeC,EACf,QAAW,CACT,CACE,MAAS,SACX,EACD,EAEH,CACE,YAAe,WACf,YAAe,gBACf,QAAW,CACT,CACE,MAAS,SACX,EACD,EAEH,CACE,YAAe,WACf,YAAeD,EACf,QAAW,CACT,CACE,MAAS,SACX,EACD,EAEH,CACE,YAAe,OACf,QAAW,CACT,CACE,WAAc,KAChB,EACD,EAEH,CACE,YAAe,OACf,YAAe,WACf,QAAW,CACT,CACE,MAAS,SACX,EACD,EAEH,CACE,YAAe,OACf,YAAe,cACf,QAAW,CACT,CACE,WAAc,KAChB,EACD,EAEH,CACE,YAAe,OACf,YAAeA,EACf,QAAW,CACT,CACE,MAAS,SACX,EACD,EAEH,CACE,YAAe,OACf,YAAeC,EACf,QAAW,CACT,CACE,MAAS,SACX,EAEJ,EACA,CACE,YAAeC,EACf,YAAe,WACf,QAAW,CACT,CACE,MAAS,SACX,EACD,EAEH,CACE,YAAeA,EACf,YAAeC,EACf,QAAW,CACT,CACE,MAAS,SACX,EACD,EAEH,CACE,YAAeD,EACf,YAAeF,EACf,QAAW,CACT,CACE,MAAS,SACX,EACD,EAEH,CACE,YAAeE,EACf,YAAeD,EACf,QAAW,CACT,CACE,MAAS,SACX,EACD,EAEH,CACE,YAAe,aACf,YAAe,SACf,QAAW,CACT,CACE,WAAc,KAChB,EACD,EAEH,CACE,YAAe,UACf,QAAW,CACT,CACE,WAAc,KAChB,EACD,EAEH,CACE,YAAe,UACf,YAAeD,EACf,QAAW,CACT,CACE,MAAS,SACX,EACD,EAEH,CACE,YAAe,UACf,YAAeC,EACf,QAAW,CACT,CACE,MAAS,SACX,EACD,EAEH,CACE,YAAe,eACf,YAAe,gBACf,QAAW,CACT,CACE,MAAS,SACX,EACD,EAEH,CACE,YAAe,kBACf,YAAe,WACf,QAAW,CACT,CACE,MAAS,SACX,EACD,EAEH,CACE,YAAe,QACf,YAAe,WACf,QAAW,CACT,CACE,MAAS,SACX,EACD,EAEH,CACE,YAAe,QACf,YAAeD,EACf,QAAW,CACT,CACE,MAAS,SACX,EACD,EAEJ,2BCnND,MAzEwC,OAAC,SAyE1BK,GAxEbhD,CAAU,GAwEUgD,EAAC,SAvErBlD,CAAY,eACZmD,CAAa,UACbR,CAAQ,QACRS,EAAS,GAAG,CACZC,QAAQ,MAAM,UACd7D,CAAQ,MACR8D,EAAO,CAAC,SACRC,EAAU,CAAC,SACXjC,CAAO,CACR,GACO,QAAEkC,CAAM,CAAE,CAAGC,CAAAA,EAAAA,EAAAA,SAAAA,CAASA,GACtB,UAAEC,CAAQ,WAAEC,CAAS,CAAE,CAAIC,CAAAA,EAAAA,EAAAA,CAAAA,CAAaA,UAmB9C,EAAsB,SAAP,CAAQnG,MAAAA,UAAI,uBACtBiG,EAGH,QAHa,EAGZjG,MAAAA,CAAIC,UAAU,yBACb,UAACD,MAAAA,CAAIC,UAAU,WAAWmG,MAAO,OAAER,SAAOD,EAAQhF,SAAU,UAAW,WACrE,WAAC0F,EAAAA,EAASA,CAAAA,CACRC,kBAzBe,CAyBIC,MAxBlBX,EACPD,OAA0B,UAAlB,OAAOA,EAAsB,GAAU,OAAPA,EAAO,MAAMA,CACvD,EAuBQa,OAhBOd,CAgBCc,EArBM,CACpB1F,IAAK,SAIyB2F,CAH9B1F,IAAK,SACP,EAmBQ8E,KAAMA,EACNa,OAhBU,CAgBFC,GAfdlC,EAAImC,UAAU,CAAC,CACbC,OAAQrB,CACV,EACF,EAaQsB,QAAS,CACPhB,EAhBWN,MAgBFM,EACTjF,WAAW,EACXkG,mBAAmB,EACnBC,mBAAmB,EACnBC,WAAY,GACZC,eAAgB,GAChBC,gBAAgB,EAChBC,mBAAmB,CACrB,YAEClC,EACAzC,GAAcF,GAAgBA,EAAanB,WAAW,EACrD,UAAC4D,EAAgBA,CACfrE,SAAU4B,EAAanB,SADR4D,EACmB,GAClCC,aAAc,KAEZoC,QAAQC,GAAG,CAAC,qBACZzD,GAAAA,GACF,WAECpB,GAHCoB,QA5BQ,UAAC7D,MAAAA,UAAI,mBAsC7B", "sources": ["webpack://_N_E/./pages/dashboard/ActiveProjectOperations.tsx", "webpack://_N_E/./components/common/RKIMapMarker.tsx", "webpack://_N_E/./pages/dashboard/ListContainer.tsx", "webpack://_N_E/./components/common/RKIMapInfowindow.tsx", "webpack://_N_E/./components/common/mapStyles.tsx", "webpack://_N_E/./components/common/RKIMap1.tsx"], "sourcesContent": ["//Import Library\r\nimport {useEffect, useState} from \"react\";\r\n\r\n//Import services/components\r\nimport ListContainer from \"./ListContainer\"\r\nimport apiService from \"../../services/apiService\";\r\n\r\ninterface ActiveProjectOperationsProps {\r\n  t: (key: string) => string;\r\n  ongoingProjects: any[];\r\n  ongoingOperations: any[];\r\n}\r\n\r\nexport default function ActiveProjectOperations(props: ActiveProjectOperationsProps) {\r\n\r\n  const {t, ongoingOperations, ongoingProjects} = props;\r\n  const [eventsData, setEventsData] = useState<any[]>([]);\r\n  const fetchEvents = async () => {\r\n    const eventParams: any = {\r\n      query: { status: [] },\r\n      sort: { created_at: \"desc\" },\r\n      limit: 10,\r\n      select: \"-description -operation -world_region -country_regions -hazard_type -hazard -syndrome -status -laboratory_confirmed -officially_validated -rki_monitored -risk_assessment -date -more_info -images -user -created_at -updated_at\"\r\n    };\r\n    const fetchEventsStatus = await fetchEventtStatus();\r\n    if (fetchEventsStatus) {\r\n      eventParams.query.status.push(fetchEventsStatus);\r\n    }\r\n    const response = await apiService.get('event', eventParams);\r\n    if (response && Array.isArray(response.data) && response.data.length > 0) {\r\n      setEventsData(response.data);\r\n    }\r\n  }\r\n\r\n  const fetchEventtStatus = async () => {\r\n    const response = await apiService.get('/eventStatus', {query: {title: 'Current'}});\r\n    if (response && response.data && response.data.length > 0) {\r\n      return response.data[0]._id;\r\n    }\r\n    return false;\r\n  };\r\n\r\n  useEffect(() => {\r\n    fetchEvents();\r\n  },[]);\r\n\r\n  return (\r\n    <div className=\"active-projects-announcements\">\r\n      <h4>{t('allActivity')}</h4>\r\n      <ListContainer t={t} currentEvents={eventsData} ongoingProjects={ongoingProjects} ongoingOperations={ongoingOperations} />\r\n    </div>\r\n  )\r\n}", "import React from 'react';\r\nimport { Marker } from '@react-google-maps/api';\r\n\r\ninterface RKIMapMarkerProps {\r\n  name?: string;\r\n  id?: string;\r\n  countryId?: string;\r\n  type?: string;\r\n  icon?: {\r\n    url: string;\r\n    scaledSize?: google.maps.Size;\r\n  };\r\n  position: {\r\n    lat: number;\r\n    lng: number;\r\n  };\r\n  onClick?: (props: any, marker: any, e: any) => void;\r\n  title?: string;\r\n  draggable?: boolean;\r\n}\r\n\r\nconst RKIMapMarker: React.FC<RKIMapMarkerProps> = ({\r\n  name = 'Marker',\r\n  id = '',\r\n  countryId = '',\r\n  type,\r\n  icon,\r\n  position,\r\n  onClick,\r\n  title,\r\n  draggable = false,\r\n}) => {\r\n  const handleClick = (e: google.maps.MapMouseEvent) => {\r\n    if (onClick) {\r\n      const markerProps = {\r\n        name,\r\n        id,\r\n        countryId,\r\n        type,\r\n        position,\r\n      };\r\n\r\n      // Create a marker-like object for compatibility with old onClick signature\r\n      const marker = {\r\n        position,\r\n        getPosition: () => position,\r\n      };\r\n\r\n      onClick(markerProps, marker, e);\r\n    }\r\n  };\r\n\r\n  // Ensure position is valid\r\n  if (!position || typeof position.lat !== 'number' || typeof position.lng !== 'number') {\r\n    return null;\r\n  }\r\n\r\n  return (\r\n    <Marker\r\n      position={position}\r\n      icon={icon}\r\n      title={title || name}\r\n      draggable={draggable}\r\n      onClick={handleClick}\r\n    />\r\n  );\r\n};\r\n\r\nexport default RKIMapMarker;\r\n", "//Import Library\r\nimport React, { useState, useEffect } from \"react\";\r\nimport _ from \"lodash\";\r\n\r\n//Import services/components\r\nimport RKIMAP1 from \"../../components/common/RKIMap1\";\r\nimport RKIMapMarker from \"../../components/common/RKIMapMarker\";\r\nimport { useTranslation } from 'next-i18next';\r\n\r\ninterface MapLegendsProps {\r\n  t: (key: string) => string;\r\n}\r\n\r\nconst MapLegends = (props: MapLegendsProps) => {\r\n  const { t } = props;\r\n  const Event = t(\"Event\");\r\n  return (\r\n    <div className=\"map-legends\">\r\n      <ul>\r\n        <li className=\"marker-yellow-legend\">\r\n          <i className=\"fas fa-circle\" /> {t(\"Projects\")}\r\n        </li>\r\n        <li className=\"marker-green-legend\">\r\n          <i className=\"fas fa-circle\" /> {t(\"Operations\")}\r\n        </li>\r\n        <li className=\"marker-red-legend\">\r\n          <i className=\"fas fa-circle\" /> {Event}{\" \"}\r\n        </li>\r\n      </ul>\r\n    </div>\r\n  );\r\n};\r\n\r\ninterface ListMapContainerProps {\r\n  t: (key: string) => string;\r\n  ongoingOperations: any[];\r\n  ongoingProjects: any[];\r\n  currentEvents: any[];\r\n}\r\n\r\nconst ListMapContainer = (props: ListMapContainerProps) => {\r\n  const { i18n } = useTranslation('common');\r\n  const currentLang = i18n.language;\r\n  const { t, ongoingOperations, ongoingProjects, currentEvents } = props;\r\n  const [dataCollector, setDataCollector] = useState<any>({\r\n    events: false,\r\n    projects: false,\r\n    operations: false,\r\n  });\r\n  const [mapdata, setMapdata] = useState<any[]>([]);\r\n  const [activeMarker, setactiveMarker]: any = useState({});\r\n  const [markerInfo, setMarkerInfo]: any = useState({});\r\n\r\n  const MarkerInfo = (Markerprops: any) => {\r\n    const { info } = Markerprops;\r\n    const markersInformation = markerDetails(info);\r\n    if (\r\n      info &&\r\n      Object.keys(info).length > 0 &&\r\n      markersInformation != undefined\r\n    ) {\r\n      return (\r\n        <ul>\r\n          {markersInformation.map((item: any, index: number) => {\r\n            return (\r\n              <li key={index}>\r\n                <a href={`/${currentLang}/${info?.type}/show/${info?.id}`}>{item?.title}</a>\r\n              </li>\r\n            );\r\n          })}\r\n        </ul>\r\n      );\r\n    } else {\r\n      return null;\r\n    }\r\n\r\n    function markerDetails(infoinit: any) {\r\n      switch (infoinit?.type) {\r\n        case \"operation\":\r\n          return ongoingOperations.filter(\r\n            (x) => x.country && x.country._id == infoinit.countryId\r\n          );\r\n        case \"project\":\r\n          return ongoingProjects.filter(\r\n            (x) =>\r\n              x.partner_institutions &&\r\n              x.partner_institutions.length > 0 &&\r\n              x.partner_institutions[0].partner_country &&\r\n              x.partner_institutions[0].partner_country._id ==\r\n                infoinit.countryId\r\n          );\r\n        case \"event\":\r\n          return currentEvents.filter(\r\n            (x) => x.country && x.country._id == infoinit.countryId\r\n          );\r\n      }\r\n    }\r\n  };\r\n\r\n  const resetMarker = () => {\r\n    setactiveMarker(null);\r\n    setMarkerInfo(null);\r\n  };\r\n\r\n  const onMarkerClick = async (props: any, marker: any, e: any) => {\r\n    resetMarker();\r\n    setactiveMarker(marker);\r\n    setMarkerInfo({\r\n      name: props.name,\r\n      id: props.id,\r\n      type: props.type,\r\n      countryId: props.countryId,\r\n    });\r\n  };\r\n\r\n  const fetchOperations = () => {\r\n    const operations = ongoingOperations;\r\n    const dashboardOperationFilter: any[] = [];\r\n    _.forEach(operations, (op: any) => {\r\n      if (op.country) {\r\n        dashboardOperationFilter.push({\r\n          title: op.title,\r\n          type: \"operation\",\r\n          id: op._id,\r\n          countryId: op.country && op.country._id,\r\n          lat: op.country.coordinates[0].latitude,\r\n          lng: op.country.coordinates[0].longitude,\r\n          icon: \"/images/map-marker-green.svg\",\r\n        });\r\n      }\r\n    });\r\n    dataCollector.operations = true;\r\n    Data_setfunc(setDataCollector, dataCollector);\r\n    setMapdata([...mapdata, ...dashboardOperationFilter]);\r\n  };\r\n\r\n  const fetchProjects = () => {\r\n    const projects = ongoingProjects;\r\n    const dashboardProjectsFilter: any[] = [];\r\n    _.forEach(projects, (val: any) => {\r\n      if (val.partner_institutions && val.partner_institutions.length > 0) {\r\n        _.forEach(val.partner_institutions, (country: any) => {\r\n          if (country.partner_country) {\r\n            dashboardProjectsFilter.push({\r\n              title: val.title,\r\n              type: \"project\",\r\n              id: val._id,\r\n              countryId:\r\n                val.partner_institutions.length > 0 &&\r\n                val.partner_institutions[0].partner_country &&\r\n                val.partner_institutions[0].partner_country._id,\r\n              lat: country.partner_country.coordinates[0].latitude,\r\n              lng: country.partner_country.coordinates[0].longitude,\r\n              icon: \"/images/map-marker-yellow.svg\",\r\n            });\r\n          }\r\n        });\r\n      }\r\n    });\r\n    dataCollector.projects = true;\r\n    DataCollector_func(setDataCollector, dataCollector);\r\n    setMapdata([...mapdata, ...dashboardProjectsFilter]);\r\n  };\r\n\r\n  const fetchEvents = () => {\r\n    const dashbordEventFilter: any[] = [];\r\n    _.forEach(currentEvents, (event: any) => {\r\n      if (event.country) {\r\n        dashbordEventFilter.push({\r\n          title: event.title,\r\n          type: \"event\",\r\n          id: event._id,\r\n          countryId: event.country && event.country._id,\r\n          lat: event.country.coordinates[0].latitude,\r\n          lng: event.country.coordinates[0].longitude,\r\n          icon: \"/images/map-marker-red.svg\",\r\n        });\r\n      }\r\n    });\r\n    dataCollector.events = true;\r\n    data_select(setDataCollector, dataCollector);\r\n    setMapdata([...mapdata, ...dashbordEventFilter]);\r\n  };\r\n\r\n  useEffect(() => {\r\n    fetchProjects();\r\n  }, [ongoingProjects]);\r\n\r\n  useEffect(() => {\r\n    fetchOperations();\r\n  }, [ongoingOperations]);\r\n\r\n  useEffect(() => {\r\n    fetchEvents();\r\n  }, [currentEvents]);\r\n\r\n  return (\r\n    <>\r\n      <RKIMAP1\r\n        onClose={resetMarker}\r\n        language={currentLang}\r\n        points={mapdata}\r\n        activeMarker={activeMarker}\r\n        markerInfo={<MarkerInfo info={markerInfo} />}\r\n      >\r\n        {dataCollector.projects &&\r\n        dataCollector.operations &&\r\n        dataCollector.events &&\r\n        mapdata.length >= 1\r\n          ? mapdata.map((item: any, index: number) => {\r\n              if (item.lat && item.lng) {\r\n                return (\r\n                  <RKIMapMarker\r\n                    key={index}\r\n                    name={item.title}\r\n                    id={item.id}\r\n                    countryId={item.countryId}\r\n                    type={item.type}\r\n                    icon={{\r\n                      url: item.icon,\r\n                    }}\r\n                    onClick={onMarkerClick}\r\n                    position={item}\r\n                  />\r\n                );\r\n              }\r\n            })\r\n          : null}\r\n      </RKIMAP1>\r\n      <MapLegends t={t} />\r\n    </>\r\n  );\r\n};\r\n\r\nexport default ListMapContainer;\r\nfunction data_select(setDataCollector: any, dataCollector: any) {\r\n  setDataCollector(dataCollector);\r\n}\r\n\r\nfunction DataCollector_func(setDataCollector: any, dataCollector: any) {\r\n  setDataCollector(dataCollector);\r\n}\r\n\r\nfunction Data_setfunc(setDataCollector: any, dataCollector: any) {\r\n  setDataCollector(dataCollector);\r\n}\r\n", "import { InfoWindow } from '@react-google-maps/api';\r\n\r\ninterface Props {\r\n  position: google.maps.LatLngLiteral;\r\n  onCloseClick?: () => void;\r\n  children?: React.ReactNode;\r\n}\r\n\r\nconst RKIMapInfowindow = ({ position, onCloseClick, children }: Props) => {\r\n  return (\r\n    <InfoWindow position={position} onCloseClick={onCloseClick}>\r\n      <div>{children}</div>\r\n    </InfoWindow>\r\n  );\r\n};\r\n\r\nexport default RKIMapInfowindow;\r\n", "const fill = \"labels.text.fill\"\r\nconst stoke = \"labels.text.stroke\"\r\nconst road = \"road.highway\"\r\nconst geometry = \"geometry.stroke\"\r\nconst mapStyles = [\r\n  {\r\n    \"elementType\": \"geometry\",\r\n    \"stylers\": [\r\n      {\r\n        \"color\": \"#1d2c4d\"\r\n      }\r\n    ]\r\n  },\r\n  {\r\n    \"elementType\": fill,\r\n    \"stylers\": [\r\n      {\r\n        \"color\": \"#8ec3b9\"\r\n      }\r\n    ]\r\n  },\r\n  {\r\n    \"elementType\": stoke,\r\n    \"stylers\": [\r\n      {\r\n        \"color\": \"#1a3646\"\r\n      }\r\n    ]\r\n  },\r\n  {\r\n    \"featureType\": \"administrative\",\r\n    \"elementType\": \"geometry\",\r\n    \"stylers\": [\r\n      {\r\n        \"visibility\": \"off\"\r\n      }\r\n    ]\r\n  },\r\n  {\r\n    \"featureType\": \"administrative.country\",\r\n    \"elementType\": geometry,\r\n    \"stylers\": [\r\n      {\r\n        \"color\": \"#4b6878\"\r\n      }\r\n    ]\r\n  },\r\n  {\r\n    \"featureType\": \"administrative.land_parcel\",\r\n    \"elementType\": \"labels\",\r\n    \"stylers\": [\r\n      {\r\n        \"visibility\": \"off\"\r\n      }\r\n    ]\r\n  },\r\n  {\r\n    \"featureType\": \"administrative.land_parcel\",\r\n    \"elementType\": fill,\r\n    \"stylers\": [\r\n      {\r\n        \"color\": \"#64779e\"\r\n      }\r\n    ]\r\n  },\r\n  {\r\n    \"featureType\": \"administrative.province\",\r\n    \"elementType\": geometry,\r\n    \"stylers\": [\r\n      {\r\n        \"color\": \"#4b6878\"\r\n      }\r\n    ]\r\n  },\r\n  {\r\n    \"featureType\": \"landscape.man_made\",\r\n    \"elementType\": geometry,\r\n    \"stylers\": [\r\n      {\r\n        \"color\": \"#334e87\"\r\n      }\r\n    ]\r\n  },\r\n  {\r\n    \"featureType\": \"landscape.natural\",\r\n    \"elementType\": \"geometry\",\r\n    \"stylers\": [\r\n      {\r\n        \"color\": \"#023e58\"\r\n      }\r\n    ]\r\n  },\r\n  {\r\n    \"featureType\": \"poi\",\r\n    \"stylers\": [\r\n      {\r\n        \"visibility\": \"off\"\r\n      }\r\n    ]\r\n  },\r\n  {\r\n    \"featureType\": \"poi\",\r\n    \"elementType\": \"geometry\",\r\n    \"stylers\": [\r\n      {\r\n        \"color\": \"#283d6a\"\r\n      }\r\n    ]\r\n  },\r\n  {\r\n    \"featureType\": \"poi\",\r\n    \"elementType\": \"labels.text\",\r\n    \"stylers\": [\r\n      {\r\n        \"visibility\": \"off\"\r\n      }\r\n    ]\r\n  },\r\n  {\r\n    \"featureType\": \"poi\",\r\n    \"elementType\": fill,\r\n    \"stylers\": [\r\n      {\r\n        \"color\": \"#6f9ba5\"\r\n      }\r\n    ]\r\n  },\r\n  {\r\n    \"featureType\": \"poi\",\r\n    \"elementType\": stoke,\r\n    \"stylers\": [\r\n      {\r\n        \"color\": \"#1d2c4d\"\r\n      }\r\n    ]\r\n  },\r\n  {\r\n    \"featureType\": \"poi.park\",\r\n    \"elementType\": \"geometry.fill\",\r\n    \"stylers\": [\r\n      {\r\n        \"color\": \"#023e58\"\r\n      }\r\n    ]\r\n  },\r\n  {\r\n    \"featureType\": \"poi.park\",\r\n    \"elementType\": fill,\r\n    \"stylers\": [\r\n      {\r\n        \"color\": \"#3C7680\"\r\n      }\r\n    ]\r\n  },\r\n  {\r\n    \"featureType\": \"road\",\r\n    \"stylers\": [\r\n      {\r\n        \"visibility\": \"off\"\r\n      }\r\n    ]\r\n  },\r\n  {\r\n    \"featureType\": \"road\",\r\n    \"elementType\": \"geometry\",\r\n    \"stylers\": [\r\n      {\r\n        \"color\": \"#304a7d\"\r\n      }\r\n    ]\r\n  },\r\n  {\r\n    \"featureType\": \"road\",\r\n    \"elementType\": \"labels.icon\",\r\n    \"stylers\": [\r\n      {\r\n        \"visibility\": \"off\"\r\n      }\r\n    ]\r\n  },\r\n  {\r\n    \"featureType\": \"road\",\r\n    \"elementType\": fill,\r\n    \"stylers\": [\r\n      {\r\n        \"color\": \"#98a5be\"\r\n      }\r\n    ]\r\n  },\r\n  {\r\n    \"featureType\": \"road\",\r\n    \"elementType\": stoke,\r\n    \"stylers\": [\r\n      {\r\n        \"color\": \"#1d2c4d\"\r\n      }\r\n    ]\r\n  },\r\n  {\r\n    \"featureType\": road,\r\n    \"elementType\": \"geometry\",\r\n    \"stylers\": [\r\n      {\r\n        \"color\": \"#2c6675\"\r\n      }\r\n    ]\r\n  },\r\n  {\r\n    \"featureType\": road,\r\n    \"elementType\": geometry,\r\n    \"stylers\": [\r\n      {\r\n        \"color\": \"#255763\"\r\n      }\r\n    ]\r\n  },\r\n  {\r\n    \"featureType\": road,\r\n    \"elementType\": fill,\r\n    \"stylers\": [\r\n      {\r\n        \"color\": \"#b0d5ce\"\r\n      }\r\n    ]\r\n  },\r\n  {\r\n    \"featureType\": road,\r\n    \"elementType\": stoke,\r\n    \"stylers\": [\r\n      {\r\n        \"color\": \"#023e58\"\r\n      }\r\n    ]\r\n  },\r\n  {\r\n    \"featureType\": \"road.local\",\r\n    \"elementType\": \"labels\",\r\n    \"stylers\": [\r\n      {\r\n        \"visibility\": \"off\"\r\n      }\r\n    ]\r\n  },\r\n  {\r\n    \"featureType\": \"transit\",\r\n    \"stylers\": [\r\n      {\r\n        \"visibility\": \"off\"\r\n      }\r\n    ]\r\n  },\r\n  {\r\n    \"featureType\": \"transit\",\r\n    \"elementType\": fill,\r\n    \"stylers\": [\r\n      {\r\n        \"color\": \"#98a5be\"\r\n      }\r\n    ]\r\n  },\r\n  {\r\n    \"featureType\": \"transit\",\r\n    \"elementType\": stoke,\r\n    \"stylers\": [\r\n      {\r\n        \"color\": \"#1d2c4d\"\r\n      }\r\n    ]\r\n  },\r\n  {\r\n    \"featureType\": \"transit.line\",\r\n    \"elementType\": \"geometry.fill\",\r\n    \"stylers\": [\r\n      {\r\n        \"color\": \"#283d6a\"\r\n      }\r\n    ]\r\n  },\r\n  {\r\n    \"featureType\": \"transit.station\",\r\n    \"elementType\": \"geometry\",\r\n    \"stylers\": [\r\n      {\r\n        \"color\": \"#3a4762\"\r\n      }\r\n    ]\r\n  },\r\n  {\r\n    \"featureType\": \"water\",\r\n    \"elementType\": \"geometry\",\r\n    \"stylers\": [\r\n      {\r\n        \"color\": \"#0e1626\"\r\n      }\r\n    ]\r\n  },\r\n  {\r\n    \"featureType\": \"water\",\r\n    \"elementType\": fill,\r\n    \"stylers\": [\r\n      {\r\n        \"color\": \"#4e6d70\"\r\n      }\r\n    ]\r\n  }\r\n];\r\n\r\nexport default mapStyles;", "import React from 'react';\r\nimport { GoogleMap, useJsApiLoader } from '@react-google-maps/api';\r\nimport RKIMapInfowindow from './RKIMapInfowindow';\r\nimport mapStyles from './mapStyles';\r\nimport { useRouter } from \"next/router\";\r\nimport { useGoogleMaps } from './GoogleMapsProvider';\r\n\r\ninterface RKIMap1Props {\r\n  markerInfo?: React.ReactNode;\r\n  activeMarker?: any;\r\n  initialCenter?: { lat: number; lng: number };\r\n  children?: React.ReactNode;\r\n  height?: number | string;\r\n  width?: string;\r\n  language?: string;\r\n  points?: any[];\r\n  zoom?: number;\r\n  minZoom?: number;\r\n  onClose?: () => void\r\n}\r\n\r\nconst RKIMap1: React.FC<RKIMap1Props> = ({\r\n  markerInfo,\r\n  activeMarker,\r\n  initialCenter,\r\n  children,\r\n  height = 300,\r\n  width = \"114%\",\r\n  language,\r\n  zoom = 1,\r\n  minZoom = 1,\r\n  onClose\r\n}) => {\r\n  const { locale } = useRouter();\r\n  const { isLoaded, loadError } =  useGoogleMaps();\r\n  const containerStyle = {\r\n    width: width,\r\n    height: typeof height === 'number' ? `${height}px` : height,\r\n  };\r\n\r\n  const defaultCenter = {\r\n    lat: 52.520017,\r\n    lng: 13.404195,\r\n  };\r\n\r\n  const center = initialCenter || defaultCenter;\r\n\r\n  const onMapLoad = (map: google.maps.Map) => {\r\n    map.setOptions({\r\n      styles: mapStyles,\r\n    });\r\n  };\r\n\r\n  if (loadError) return <div>Error loading maps</div>;\r\n  if (!isLoaded) return <div>Loading Maps...</div>;\r\n\r\n  return (\r\n    <div className=\"map-container\">\r\n      <div className=\"mapprint\" style={{ width, height, position: 'relative' }}>\r\n        <GoogleMap\r\n          mapContainerStyle={containerStyle}\r\n          center={center}\r\n          zoom={zoom}\r\n          onLoad={onMapLoad}\r\n          options={{\r\n            minZoom: minZoom,\r\n            draggable: true,\r\n            keyboardShortcuts: false,\r\n            streetViewControl: false,\r\n            panControl: false,\r\n            clickableIcons: false,\r\n            mapTypeControl: false,\r\n            fullscreenControl: true,\r\n          }}\r\n        >\r\n          {children}\r\n          {markerInfo && activeMarker && activeMarker.getPosition && (\r\n            <RKIMapInfowindow\r\n              position={activeMarker.getPosition()}\r\n              onCloseClick={() => {\r\n                // Handle close if needed\r\n                console.log('close click');\r\n                onClose?.()\r\n              }}\r\n            >\r\n              {markerInfo}\r\n            </RKIMapInfowindow>\r\n          )}\r\n        </GoogleMap>\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default RKIMap1;\r\n"], "names": ["ActiveProjectOperations", "props", "t", "ongoingOperations", "ongoingProjects", "eventsData", "setEventsData", "useState", "fetchEvents", "eventParams", "query", "status", "sort", "created_at", "limit", "select", "fetchEventsStatus", "fetchEventtStatus", "push", "response", "apiService", "get", "Array", "isArray", "data", "length", "title", "_id", "useEffect", "div", "className", "h4", "ListContainer", "currentEvents", "name", "id", "R<PERSON>IMapMarker", "countryId", "type", "icon", "position", "onClick", "draggable", "lat", "lng", "<PERSON><PERSON>", "handleClick", "markerProps", "marker", "getPosition", "e", "MapLegends", "Event", "ul", "li", "i", "i18n", "useTranslation", "ListMapContainer", "currentLang", "language", "dataCollector", "setDataCollector", "events", "projects", "operations", "mapdata", "setMapdata", "activeMarker", "set<PERSON><PERSON><PERSON><PERSON>", "markerInfo", "setMarkerInfo", "reset<PERSON><PERSON><PERSON>", "onMarkerClick", "fetchOperations", "dashboardOperationFilter", "_", "op", "country", "coordinates", "latitude", "longitude", "fetchProjects", "dashboardProjectsFilter", "val", "partner_institutions", "partner_country", "dashbordEventFilter", "event", "RKIMAP1", "onClose", "points", "info", "Markerprops", "markersInformation", "markerDetails", "infoinit", "filter", "x", "Object", "keys", "undefined", "map", "item", "index", "a", "href", "MarkerInfo", "url", "RKIMapInfowindow", "onCloseClick", "children", "InfoWindow", "fill", "stoke", "road", "geometry", "mapStyles", "RKIMap1", "initialCenter", "height", "width", "zoom", "minZoom", "locale", "useRouter", "isLoaded", "loadError", "useGoogleMaps", "style", "GoogleMap", "mapContainerStyle", "containerStyle", "center", "defaultCenter", "onLoad", "onMapLoad", "setOptions", "styles", "options", "keyboardShortcuts", "streetViewControl", "panControl", "clickableIcons", "mapTypeControl", "fullscreenControl", "console", "log"], "sourceRoot": "", "ignoreList": []}