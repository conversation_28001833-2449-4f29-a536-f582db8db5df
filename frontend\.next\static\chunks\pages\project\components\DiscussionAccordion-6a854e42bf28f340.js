(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[6841],{14634:(e,n,t)=>{(window.__NEXT_P=window.__NEXT_P||[]).push(["/project/components/DiscussionAccordion",function(){return t(65910)}])},65910:(e,n,t)=>{"use strict";t.r(n),t.d(n,{default:()=>u});var a=t(37876),r=t(14232),i=t(32890),s=t(21772),o=t(11041),d=t(31753),_=t(48477);let u=e=>{var n;let{t}=(0,d.Bd)("common"),[u,M]=(0,r.useState)(!0);return(0,a.jsxs)(i.A.Item,{eventKey:"2",children:[(0,a.jsxs)(i.<PERSON><PERSON>,{onClick:()=>M(!u),children:[(0,a.jsx)("div",{className:"cardTitle",children:t("Discussions")}),(0,a.jsx)("div",{className:"cardArrow",children:u?(0,a.jsx)(s.g,{icon:o.QLR,color:"#fff"}):(0,a.jsx)(s.g,{icon:o.EZy,color:"#fff"})})]}),(0,a.jsx)(i.A.Body,{children:(0,a.jsx)(_.A,{type:"project",id:(null==e||null==(n=e.routeData)?void 0:n.routes)?e.routeData.routes[1]:""})})]})}},84135:function(e,n,t){(function(e){"use strict";function n(e,n,t,a){var r={m:["eine Minute","einer Minute"],h:["eine Stunde","einer Stunde"],d:["ein Tag","einem Tag"],dd:[e+" Tage",e+" Tagen"],w:["eine Woche","einer Woche"],M:["ein Monat","einem Monat"],MM:[e+" Monate",e+" Monaten"],y:["ein Jahr","einem Jahr"],yy:[e+" Jahre",e+" Jahren"]};return n?r[t][0]:r[t][1]}e.defineLocale("de",{months:"Januar_Februar_M\xe4rz_April_Mai_Juni_Juli_August_September_Oktober_November_Dezember".split("_"),monthsShort:"Jan._Feb._M\xe4rz_Apr._Mai_Juni_Juli_Aug._Sep._Okt._Nov._Dez.".split("_"),monthsParseExact:!0,weekdays:"Sonntag_Montag_Dienstag_Mittwoch_Donnerstag_Freitag_Samstag".split("_"),weekdaysShort:"So._Mo._Di._Mi._Do._Fr._Sa.".split("_"),weekdaysMin:"So_Mo_Di_Mi_Do_Fr_Sa".split("_"),weekdaysParseExact:!0,longDateFormat:{LT:"HH:mm",LTS:"HH:mm:ss",L:"DD.MM.YYYY",LL:"D. MMMM YYYY",LLL:"D. MMMM YYYY HH:mm",LLLL:"dddd, D. MMMM YYYY HH:mm"},calendar:{sameDay:"[heute um] LT [Uhr]",sameElse:"L",nextDay:"[morgen um] LT [Uhr]",nextWeek:"dddd [um] LT [Uhr]",lastDay:"[gestern um] LT [Uhr]",lastWeek:"[letzten] dddd [um] LT [Uhr]"},relativeTime:{future:"in %s",past:"vor %s",s:"ein paar Sekunden",ss:"%d Sekunden",m:n,mm:"%d Minuten",h:n,hh:"%d Stunden",d:n,dd:n,w:n,ww:"%d Wochen",M:n,MM:n,y:n,yy:n},dayOfMonthOrdinalParse:/\d{1,2}\./,ordinal:"%d.",week:{dow:1,doy:4}})})(t(10841))}},e=>{var n=n=>e(e.s=n);e.O(0,[7725,1772,8477,636,6593,8792],()=>n(14634)),_N_E=e.O()}]);
//# sourceMappingURL=DiscussionAccordion-6a854e42bf28f340.js.map