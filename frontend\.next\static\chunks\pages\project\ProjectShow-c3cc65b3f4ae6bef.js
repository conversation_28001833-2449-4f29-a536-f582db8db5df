(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[1766],{24892:(e,a,r)=>{(window.__NEXT_P=window.__NEXT_P||[]).push(["/project/ProjectShow",function(){return r(87975)}])},29335:(e,a,r)=>{"use strict";r.d(a,{A:()=>_});var t=r(15039),s=r.n(t),d=r(14232),l=r(77346),o=r(37876);let c=d.forwardRef((e,a)=>{let{className:r,bsPrefix:t,as:d="div",...c}=e;return t=(0,l.oU)(t,"card-body"),(0,o.jsx)(d,{ref:a,className:s()(r,t),...c})});c.displayName="CardBody";let i=d.forwardRef((e,a)=>{let{className:r,bsPrefix:t,as:d="div",...c}=e;return t=(0,l.oU)(t,"card-footer"),(0,o.jsx)(d,{ref:a,className:s()(r,t),...c})});i.displayName="CardFooter";var f=r(81764);let n=d.forwardRef((e,a)=>{let{bsPrefix:r,className:t,as:c="div",...i}=e,n=(0,l.oU)(r,"card-header"),m=(0,d.useMemo)(()=>({cardHeaderBsPrefix:n}),[n]);return(0,o.jsx)(f.A.Provider,{value:m,children:(0,o.jsx)(c,{ref:a,...i,className:s()(t,n)})})});n.displayName="CardHeader";let m=d.forwardRef((e,a)=>{let{bsPrefix:r,className:t,variant:d,as:c="img",...i}=e,f=(0,l.oU)(r,"card-img");return(0,o.jsx)(c,{ref:a,className:s()(d?"".concat(f,"-").concat(d):f,t),...i})});m.displayName="CardImg";let N=d.forwardRef((e,a)=>{let{className:r,bsPrefix:t,as:d="div",...c}=e;return t=(0,l.oU)(t,"card-img-overlay"),(0,o.jsx)(d,{ref:a,className:s()(r,t),...c})});N.displayName="CardImgOverlay";let u=d.forwardRef((e,a)=>{let{className:r,bsPrefix:t,as:d="a",...c}=e;return t=(0,l.oU)(t,"card-link"),(0,o.jsx)(d,{ref:a,className:s()(r,t),...c})});u.displayName="CardLink";var x=r(46052);let p=(0,x.A)("h6"),w=d.forwardRef((e,a)=>{let{className:r,bsPrefix:t,as:d=p,...c}=e;return t=(0,l.oU)(t,"card-subtitle"),(0,o.jsx)(d,{ref:a,className:s()(r,t),...c})});w.displayName="CardSubtitle";let y=d.forwardRef((e,a)=>{let{className:r,bsPrefix:t,as:d="p",...c}=e;return t=(0,l.oU)(t,"card-text"),(0,o.jsx)(d,{ref:a,className:s()(r,t),...c})});y.displayName="CardText";let j=(0,x.A)("h5"),C=d.forwardRef((e,a)=>{let{className:r,bsPrefix:t,as:d=j,...c}=e;return t=(0,l.oU)(t,"card-title"),(0,o.jsx)(d,{ref:a,className:s()(r,t),...c})});C.displayName="CardTitle";let v=d.forwardRef((e,a)=>{let{bsPrefix:r,className:t,bg:d,text:i,border:f,body:n=!1,children:m,as:N="div",...u}=e,x=(0,l.oU)(r,"card");return(0,o.jsx)(N,{ref:a,...u,className:s()(t,x,d&&"bg-".concat(d),i&&"text-".concat(i),f&&"border-".concat(f)),children:n?(0,o.jsx)(c,{children:m}):m})});v.displayName="Card";let _=Object.assign(v,{Img:m,Title:C,Subtitle:w,Body:c,Link:u,Text:y,Header:n,Footer:i,ImgOverlay:N})},81764:(e,a,r)=>{"use strict";r.d(a,{A:()=>s});let t=r(14232).createContext(null);t.displayName="CardHeaderContext";let s=t}},e=>{var a=a=>e(e.s=a);e.O(0,[7725,9773,1772,7126,8477,7308,9660,7975,636,6593,8792],()=>a(24892)),_N_E=e.O()}]);
//# sourceMappingURL=ProjectShow-c3cc65b3f4ae6bef.js.map