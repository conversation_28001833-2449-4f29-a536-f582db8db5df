(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[6853],{108:(e,r,t)=>{(window.__NEXT_P=window.__NEXT_P||[]).push(["/project/components/ProjectInfoSection",function(){return t(18576)}])},18576:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>c});var a=t(37876);t(14232);var s=t(56970),l=t(37784),n=t(31753),d=t(98661),i=t(72800);let o=e=>{let r=[];return null==e||e.forEach(e=>{var t;null==(t=e.partner_institution)||t.forEach(e=>{r.push({_id:e._id,title:e.title})})}),r=r.filter((e,r,t)=>t.findIndex(r=>r._id===e._id)===r)},c=e=>{let{t:r}=(0,n.Bd)("common"),{description:t,partner_institutions:c}=e.project,u=o(c);return(0,a.jsx)(a.Fragment,{children:(0,a.jsxs)(s.A,{className:"projectInfoBlock",children:[(0,a.jsx)(l.A,{className:"projectDescBlock",md:8,children:(0,a.jsx)(i.A,{description:t})}),(0,a.jsxs)(l.A,{md:4,className:"projectInfo",children:[(0,a.jsx)(d.A,{header:r("ProjectInformation"),body:function(e,r){let{area_of_work:t,funded_by:s}=r;return(0,a.jsxs)("div",{children:[(0,a.jsxs)("div",{className:"projetInfoItems",children:[(0,a.jsxs)("h6",{children:[e("AreaofWork"),": "]}),(0,a.jsx)("p",{children:null==t?void 0:t.map(e=>e.title).join(", ")})]}),(0,a.jsxs)("div",{className:"projetInfoItems",children:[(0,a.jsxs)("h6",{children:[e("FundedBy"),": "]}),(0,a.jsx)("p",{children:s})]})]})}(r,e.project)}),(0,a.jsx)(d.A,{header:r("PartnerOrganisation"),body:function(e){return(0,a.jsx)("ul",{className:"projectPartner",children:null==e?void 0:e.map((e,r)=>(0,a.jsx)("li",{children:(null==e?void 0:e.title)||""},r))})}(u)}),(0,a.jsx)(d.A,{header:r("CountriesCoveredbyProject"),body:function(e){return(0,a.jsx)("ul",{className:"projectPartner",children:null==e?void 0:e.map((e,r)=>{var t;return(0,a.jsx)("li",{children:(null==e||null==(t=e.partner_country)?void 0:t.title)||""},r)})})}(c)})]})]})})}},29335:(e,r,t)=>{"use strict";t.d(r,{A:()=>A});var a=t(15039),s=t.n(a),l=t(14232),n=t(77346),d=t(37876);let i=l.forwardRef((e,r)=>{let{className:t,bsPrefix:a,as:l="div",...i}=e;return a=(0,n.oU)(a,"card-body"),(0,d.jsx)(l,{ref:r,className:s()(t,a),...i})});i.displayName="CardBody";let o=l.forwardRef((e,r)=>{let{className:t,bsPrefix:a,as:l="div",...i}=e;return a=(0,n.oU)(a,"card-footer"),(0,d.jsx)(l,{ref:r,className:s()(t,a),...i})});o.displayName="CardFooter";var c=t(81764);let u=l.forwardRef((e,r)=>{let{bsPrefix:t,className:a,as:i="div",...o}=e,u=(0,n.oU)(t,"card-header"),j=(0,l.useMemo)(()=>({cardHeaderBsPrefix:u}),[u]);return(0,d.jsx)(c.A.Provider,{value:j,children:(0,d.jsx)(i,{ref:r,...o,className:s()(a,u)})})});u.displayName="CardHeader";let j=l.forwardRef((e,r)=>{let{bsPrefix:t,className:a,variant:l,as:i="img",...o}=e,c=(0,n.oU)(t,"card-img");return(0,d.jsx)(i,{ref:r,className:s()(l?"".concat(c,"-").concat(l):c,a),...o})});j.displayName="CardImg";let m=l.forwardRef((e,r)=>{let{className:t,bsPrefix:a,as:l="div",...i}=e;return a=(0,n.oU)(a,"card-img-overlay"),(0,d.jsx)(l,{ref:r,className:s()(t,a),...i})});m.displayName="CardImgOverlay";let x=l.forwardRef((e,r)=>{let{className:t,bsPrefix:a,as:l="a",...i}=e;return a=(0,n.oU)(a,"card-link"),(0,d.jsx)(l,{ref:r,className:s()(t,a),...i})});x.displayName="CardLink";var f=t(46052);let h=(0,f.A)("h6"),p=l.forwardRef((e,r)=>{let{className:t,bsPrefix:a,as:l=h,...i}=e;return a=(0,n.oU)(a,"card-subtitle"),(0,d.jsx)(l,{ref:r,className:s()(t,a),...i})});p.displayName="CardSubtitle";let N=l.forwardRef((e,r)=>{let{className:t,bsPrefix:a,as:l="p",...i}=e;return a=(0,n.oU)(a,"card-text"),(0,d.jsx)(l,{ref:r,className:s()(t,a),...i})});N.displayName="CardText";let v=(0,f.A)("h5"),y=l.forwardRef((e,r)=>{let{className:t,bsPrefix:a,as:l=v,...i}=e;return a=(0,n.oU)(a,"card-title"),(0,d.jsx)(l,{ref:r,className:s()(t,a),...i})});y.displayName="CardTitle";let b=l.forwardRef((e,r)=>{let{bsPrefix:t,className:a,bg:l,text:o,border:c,body:u=!1,children:j,as:m="div",...x}=e,f=(0,n.oU)(t,"card");return(0,d.jsx)(m,{ref:r,...x,className:s()(a,f,l&&"bg-".concat(l),o&&"text-".concat(o),c&&"border-".concat(c)),children:u?(0,d.jsx)(i,{children:j}):j})});b.displayName="Card";let A=Object.assign(b,{Img:j,Title:y,Subtitle:p,Body:i,Link:x,Text:N,Header:u,Footer:o,ImgOverlay:m})},72800:(e,r,t)=>{"use strict";t.d(r,{A:()=>n});var a=t(37876),s=t(14232),l=t(31753);let n=e=>{let{t:r}=(0,l.Bd)("common"),t=parseInt("255"),[n,d]=(0,s.useState)(!1);return(0,a.jsxs)(a.Fragment,{children:[e.description?(0,a.jsx)("div",{dangerouslySetInnerHTML:((r,a)=>({__html:!a&&r.length>t?r.substring(0,t)+"...":e.description}))(e.description,n),className:"operationDesc"}):null,e.description&&e.description.length>t?(0,a.jsx)("button",{type:"button",className:"readMoreText",onClick:()=>d(!n),children:r(n?"readLess":"readMore")}):null]})}},81764:(e,r,t)=>{"use strict";t.d(r,{A:()=>s});let a=t(14232).createContext(null);a.displayName="CardHeaderContext";let s=a},98661:(e,r,t)=>{"use strict";t.d(r,{A:()=>o});var a=t(37876),s=t(14232),l=t(29335),n=t(31195);function d(e){let{list:r,dialogClassName:t}=e;return(0,a.jsxs)(n.A,{...e,dialogClassName:t,"aria-labelledby":"contained-modal-title-vcenter",centered:!0,children:[(0,a.jsx)(n.A.Header,{closeButton:!0,children:(0,a.jsx)(n.A.Title,{id:"contained-modal-title-vcenter",children:r.heading})}),(0,a.jsx)(n.A.Body,{children:r.body})]})}function i(e){let{list:r}=e,[t,n]=s.useState(!1);return r&&r.body?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)("button",{type:"button",onClick:()=>n(!0),style:{border:"none",background:"none",padding:0},children:(0,a.jsx)(l.A.Footer,{children:(0,a.jsx)("i",{className:"fas fa-chevron-down"})})}),e.list&&(0,a.jsx)(d,{list:e.list,show:t,onHide:()=>n(!1),dialogClassName:e.dialogClassName})]}):null}let o=function(e){let{header:r,body:t}=e;return(0,a.jsxs)(l.A,{className:"text-center infoCard",children:[(0,a.jsx)(l.A.Header,{children:r}),(0,a.jsx)(l.A.Body,{children:(0,a.jsx)(l.A.Text,{children:t})}),(0,a.jsx)(i,{...e})]})}}},e=>{var r=r=>e(e.s=r);e.O(0,[636,6593,8792],()=>r(108)),_N_E=e.O()}]);
//# sourceMappingURL=ProjectInfoSection-d051899a830e9d75.js.map