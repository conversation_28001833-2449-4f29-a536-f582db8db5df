"use strict";exports.id=4535,exports.ids=[4535],exports.modules={28966:(i,s,e)=>{e.r(s),e.d(s,{canAddInstitution:()=>o,canAddInstitutionForm:()=>l,canEditInstitution:()=>c,canEditInstitutionForm:()=>d,canManageFocalPoints:()=>m,canViewDiscussionUpdate:()=>u,default:()=>p});var n=e(8732);e(82015);var t=e(81366),a=e.n(t),r=e(61421);let o=a()({authenticatedSelector:i=>!!i.permissions&&!!i.permissions.institution&&!!i.permissions.institution["create:any"],wrapperDisplayName:"CanAddInstitution"}),l=a()({authenticatedSelector:i=>!!i.permissions&&!!i.permissions.institution&&!!i.permissions.institution["create:any"],wrapperDisplayName:"CanAddInstitutionForm",FailureComponent:()=>(0,n.jsx)(r.default,{})}),c=a()({authenticatedSelector:(i,s)=>{if(i.permissions&&i.permissions.institution){if(i.permissions.institution["update:any"])return!0;else if(i.permissions.institution["update:own"]&&s.institution&&s.institution.user&&s.institution.user===i.user._id)return!0}return!1},wrapperDisplayName:"CanEditInstitution"}),d=a()({authenticatedSelector:(i,s)=>{if(i.permissions&&i.permissions.institution){if(i.permissions.institution["update:any"])return!0;else if(i.permissions.institution["update:own"]&&s.institution&&s.institution.user&&s.institution.user===i.user._id)return!0}return!1},wrapperDisplayName:"CanEditInstitutionForm",FailureComponent:()=>(0,n.jsx)(r.default,{})}),u=a()({authenticatedSelector:i=>!!i.permissions&&!!i.permissions.update&&!!i.permissions.update["read:any"],wrapperDisplayName:"CanViewDiscussionUpdate"}),m=a()({authenticatedSelector:(i,s)=>{if(i.permissions&&i.permissions.institution_focal_point){if(i.permissions.institution_focal_point["update:any"])return!0;else if(i.permissions.institution_focal_point["update:own"]&&s.institution&&s.institution.user&&s.institution.user===i.user._id)return!0}return!1},wrapperDisplayName:"canManageFocalPoints"}),p=o},34535:(i,s,e)=>{e.a(i,async(i,n)=>{try{e.r(s),e.d(s,{default:()=>y});var t=e(8732);e(82015);var a=e(91353),r=e(7082),o=e(83551),l=e(49481),c=e(19918),d=e.n(c),u=e(54131),m=e(82053),p=e(66713),h=e(81426),x=e(28966),j=e(88751),f=i([u,h]);[u,h]=f.then?(await f)():f;let y=i=>{let{t:s}=(0,j.useTranslation)("common"),e=()=>(0,t.jsx)(d(),{href:"/institution/[...routes]",as:`/institution/edit/${i.prop.routes[1]}`,children:(0,t.jsxs)(a.A,{variant:"secondary",size:"sm",children:[(0,t.jsx)(m.FontAwesomeIcon,{icon:u.faPen}),"\xa0",s("Edit")]})}),n=()=>(0,t.jsx)("li",{children:(0,t.jsx)("span",{className:"image-container",children:(0,t.jsx)(d(),{href:"/institution/[...routes]",as:`/institution/focalpoint/${i?.prop?.routes[1]}`,children:(0,t.jsx)("i",{className:"fas fa-plus"})})})}),c=(0,x.canEditInstitution)(()=>(0,t.jsx)(e,{})),f=(0,x.canManageFocalPoints)(()=>(0,t.jsx)(n,{}));return(0,t.jsx)(t.Fragment,{children:(0,t.jsx)(r.A,{fluid:!0,children:(0,t.jsxs)(o.A,{children:[function(i,s,e,n){return(0,t.jsx)(l.A,{xs:12,children:(0,t.jsxs)("div",{className:"d-flex justify-content-between",children:[(0,t.jsxs)("h4",{className:"institutionTitle",children:[i.title," \xa0\xa0",n&&s.routes&&s.routes[1]?(0,t.jsx)(e,{institution:i}):null]}),(0,t.jsx)(h.A,{entityId:s.routes[1],entityType:"institution"})]})})}(i.institutionData,i.prop,c,i.editAccess),(0,t.jsxs)(l.A,{xs:6,children:[(0,t.jsx)("div",{children:(0,t.jsx)(p.default,{description:i.institutionData.description})}),function(i,s){return(0,t.jsx)("div",{className:"institutionInfo",children:(0,t.jsxs)("ul",{children:[(0,t.jsxs)("li",{children:[(0,t.jsxs)("label",{children:[i("Telephone"),":"]}),(0,t.jsx)("span",{className:"field-value",children:`${"undefined"!==s.dial_code&&s.dial_code} ${s.telephone}`})]}),(0,t.jsxs)("li",{children:[(0,t.jsxs)("label",{children:[i("Address"),":"]}),(0,t.jsxs)("span",{className:"field-value",children:[s&&s.address?s.address.line_1:null," ",s&&s.address?s.address.line_2:null," ",s&&s.address?s.address.city:null]})]}),(0,t.jsxs)("li",{children:[(0,t.jsxs)("label",{children:[i("Website"),":"]}),(0,t.jsx)("span",{className:"field-value",children:s.website})]})]})})}(s,i.institutionData)]}),(0,t.jsx)(l.A,{xs:6,children:(0,t.jsxs)(o.A,{children:[(0,t.jsxs)(l.A,{md:4,className:"p-0",children:[i.focalPoints.length>=1?i.focalPoints.map((i,e)=>{if(i.isPrimary)return(0,t.jsx)("h6",{className:"other-focal-points-header",children:s("PrimaryFocalPoint")})}):"",(0,t.jsx)("ul",{className:"focalPoints primary",children:i.focalPoints.length>=1?i.focalPoints.map((i,s)=>{if(i.isPrimary)return(0,t.jsxs)("li",{className:i.isPrimary?"isPrimary":"",children:[(0,t.jsx)("span",{className:"image-container",children:i.image&&i.image._id?(0,t.jsx)("img",{src:`http://localhost:3001/api/v1/image/show/${i.image._id}`}):(0,t.jsx)("img",{src:"/images/rkiProfile.jpg"})}),(0,t.jsx)("div",{className:"focalpointDetails",children:(0,t.jsxs)("p",{className:"fpDetailsFixed",children:[(0,t.jsx)("b",{children:i.username}),(0,t.jsx)("span",{children:i.email}),(0,t.jsx)("span",{children:i.focal_points_institution}),(0,t.jsx)("span",{children:i.mobile_number})]})})]},s)}):""})]}),function(i,s,e,n,a){return(0,t.jsxs)(l.A,{md:8,children:[(0,t.jsx)("h6",{className:"other-focal-points-header",children:i("OtherFocalPoints")}),(0,t.jsxs)("ul",{className:"focalPoints",children:[s.length>=1?s.map((i,s)=>(0,t.jsxs)("li",{className:i.isPrimary?"isPrimary":"",children:[(0,t.jsx)("span",{className:"image-container",children:i.image&&i.image._id?(0,t.jsx)("img",{src:`http://localhost:3001/api/v1/image/show/${i.image._id}`}):(0,t.jsx)("img",{src:"/images/rkiProfile.jpg"})}),(0,t.jsx)("div",{className:"focalpointDetails",children:(0,t.jsxs)("p",{className:"fpDetailsFixed",children:[(0,t.jsx)("b",{children:i.username}),(0,t.jsx)("span",{children:i.email}),(0,t.jsx)("span",{children:i.focal_points_institution}),(0,t.jsx)("span",{children:i.mobile_number})]})})]},s)):"",e?.prop?.routes&&e?.prop?.routes[1]?(0,t.jsx)(n,{institution:a}):null]})]})}(s,i.focalPoints,i,f,i.institutionData)]})})]})})})};n()}catch(i){n(i)}})},66713:(i,s,e)=>{e.r(s),e.d(s,{default:()=>l});var n=e(8732),t=e(82015),a=e(12403),r=e(91353),o=e(88751);let l=({description:i})=>{let[s,e]=(0,t.useState)(!1),{t:l}=(0,o.useTranslation)("common");return(0,n.jsxs)(n.Fragment,{children:[(0,n.jsxs)(a.A,{show:s,onHide:()=>e(!s),backdrop:"static",keyboard:!1,children:[(0,n.jsx)(a.A.Header,{closeButton:!0,children:(0,n.jsx)(a.A.Title,{children:l("Description")})}),(0,n.jsx)(a.A.Body,{children:(0,n.jsx)("div",{className:"_readmore_d",dangerouslySetInnerHTML:{__html:void 0==i?"":i}})})]}),i&&i.length<130?(0,n.jsx)("div",{dangerouslySetInnerHTML:{__html:void 0==i?"":i}}):""!=i?(0,n.jsxs)("div",{children:[(0,n.jsx)("div",{className:"_tabelw",dangerouslySetInnerHTML:{__html:void 0==i?"":i.substring(0,130)+`${i.includes("<p")?"...":""}`}}),(0,n.jsx)("div",{className:"pt-3",children:(0,n.jsx)(r.A,{onClick:()=>e(!s),className:"readMoreBtn mb-3",variant:"outline-light",children:l("ReadMore")})})]}):""]})}},81426:(i,s,e)=>{e.a(i,async(i,n)=>{try{e.d(s,{A:()=>m});var t=e(8732),a=e(14062),r=e(54131),o=e(82015),l=e(82053),c=e(63487),d=i([a,r,c]);[a,r,c]=d.then?(await d)():d;let u={operation:"Operation",institution:"Institution",event:"Event",project:"Project",vspace:"Vspace"},m=(0,a.connect)(i=>i)(i=>{let{user:s,entityId:e,entityType:n}=i,[a,d]=(0,o.useState)(!1),[m,p]=(0,o.useState)(""),h=async()=>{if(!s?._id)return;let i=await c.A.get("/flag",{query:{entity_id:e,user:s._id,onModel:u[n]}});i&&i.data&&i.data.length>0&&(p(i.data[0]),d(!0))},x=async i=>{if(i.preventDefault(),!s?._id)return;let t=!a,r={entity_type:n,entity_id:e,user:s._id,onModel:u[n]};if(t){let i=await c.A.post("/flag",r);i&&i._id&&(p(i),d(t))}else{let i=await c.A.remove(`/flag/${m._id}`);i&&i.n&&d(t)}};return(0,o.useEffect)(()=>{h()},[]),(0,t.jsx)("div",{className:"subscribe-flag",children:(0,t.jsxs)("a",{href:"",onClick:x,children:[(0,t.jsx)("span",{className:"check",children:a?(0,t.jsx)(l.FontAwesomeIcon,{className:"clickable checkIcon",icon:r.faCheckCircle,color:"#00CC00"}):(0,t.jsx)(l.FontAwesomeIcon,{className:"clickable minusIcon",icon:r.faPlusCircle,color:"#fff"})}),(0,t.jsx)(l.FontAwesomeIcon,{className:"bookmark",icon:r.faBookmark,color:"#d4d4d4"})]})})});n()}catch(i){n(i)}})}};