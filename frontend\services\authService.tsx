//Import Library
import * as axiosLib from 'axios';
const axios = axiosLib.default;

const application = "application/json";
class AuthService {
  auth = async (params: { username: string; password: string }) => {
    try {
      let url = `${process.env.API_SERVER}/auth/login`;
      // if (isAdminLogin) {
      //   url = `${process.env.API_SERVER}/auth/admin/login`;
      // }
      const response = await axios.post(
        url,
        {
          username: params.username,
          password: params.password,
        },
        { headers: { "Content-Type": application }, withCredentials: true }
      );
      //if any one of these exist, then there is a field error
      if (response.status === 201 && response.data) {
        // Set client-side authentication flag when user actually logs in
        if (typeof window !== 'undefined') {
          sessionStorage.setItem('userLoggedIn', 'true');
          sessionStorage.setItem('loginTimestamp', Date.now().toString());
        }
        return response;
      }
    } catch (error: unknown) {
      const axiosError = error as { response?: any };
      return axiosError.response ? axiosError.response : {};
    }
  };

  logout = async () => {
    try {
      const _response = await axios.post(
        `${process.env.API_SERVER}/auth/logout`,
        {},
        { headers: { "Content-Type": application }, withCredentials: true }
      );
      localStorage.removeItem("persist:root");
      // Clear client-side authentication flags
      if (typeof window !== 'undefined') {
        sessionStorage.removeItem('userLoggedIn');
        sessionStorage.removeItem('loginTimestamp');
      }
    } catch (error: unknown) {
      const axiosError = error as { response?: any };
      return axiosError.response ? axiosError.response : {};
    }
  };

  verifySession = async () => {
    try {
      const response = await axios.get(
        `${process.env.API_SERVER}/auth/verify-session`,
        {
          headers: { "Content-Type": application },
          withCredentials: true
        }
      );
      if (response.status === 200 && response.data) {
        return response.data;
      }
      return { isAuthenticated: false, user: null };
    } catch (error: unknown) {
      return { isAuthenticated: false, user: null };
    }
  };

  hasActiveLogin = () => {
    if (typeof window === 'undefined') return false;

    const userLoggedIn = sessionStorage.getItem('userLoggedIn');
    const loginTimestamp = sessionStorage.getItem('loginTimestamp');

    if (!userLoggedIn || !loginTimestamp) {
      return false;
    }

    // Optional: Check if login is recent (e.g., within last 24 hours)
    const loginTime = parseInt(loginTimestamp);
    const now = Date.now();
    const twentyFourHours = 24 * 60 * 60 * 1000;

    if (now - loginTime > twentyFourHours) {
      // Login is too old, clear it
      sessionStorage.removeItem('userLoggedIn');
      sessionStorage.removeItem('loginTimestamp');
      return false;
    }

    return true;
  };

  getAuthHeader = async () => {
    return {
      "Content-Type": application,
    };
  };
}

export default new AuthService();
