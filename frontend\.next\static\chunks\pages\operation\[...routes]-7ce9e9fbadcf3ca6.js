(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[1008],{1071:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>m});var r=s(37876),a=s(11041),n=s(21772),o=s(10841),i=s.n(o),l=s(14232),c=s(56970),d=s(37784),u=s(31753);let m=e=>{let{t}=(0,u.Bd)("common"),[s,o]=(0,l.useState)(40);(0,l.useEffect)(()=>{var e;null==(e=document.getElementById("timeline-container"))||e.scroll(s,5e3)},[s]);let m={1:"/images/home/<USER>",2:"/images/home/<USER>",3:"/images/home/<USER>",4:"/images/home/<USER>",5:"/images/home/<USER>",6:"/images/home/<USER>",7:"/images/home/<USER>"};return(0,r.jsx)(r.Fragment,{children:(0,r.jsx)(c.A,{children:(0,r.jsx)(d.A,{className:"operatinTimeline",xs:12,children:(0,r.jsxs)("div",{className:"progress_main_sec",style:{marginTop:"90px"},children:[e.operation&&e.operation.timeline.length>2&&(0,r.jsxs)("div",{children:[(0,r.jsx)("div",{className:"prev",onClick:()=>{let e=s-50;o(e<0?0:e)},style:{cursor:"pointer"},children:(0,r.jsx)("span",{children:(0,r.jsx)(n.g,{icon:a.Uec})})}),(0,r.jsx)("div",{className:"next",onClick:()=>{o(s+50)},style:{cursor:"pointer"},children:(0,r.jsx)("span",{children:(0,r.jsx)(n.g,{icon:a.vmR})})})]}),(0,r.jsx)("div",{className:"progressbar-container",id:"timeline-container",children:(0,r.jsx)("ul",{className:"progressbar",children:e.operation&&e.operation.timeline&&e.operation.timeline.map((s,a)=>(0,r.jsxs)("li",{style:{zIndex:e.operation.timeline.length-a},children:[(0,r.jsx)("div",{className:"timelineIcon",children:(0,r.jsx)("img",{src:m[s.iconclass],width:"80px",height:"80px"})}),s.timetitle?(0,r.jsx)("p",{className:"step-label",children:s.timetitle}):(0,r.jsx)("p",{className:"step-label",children:t("NoTitle")}),s.date?(0,r.jsx)("p",{className:"step-text",children:i()(s.date).format("MM-D-YYYY")}):(0,r.jsx)("p",{className:"step-text",children:t("NoDate")})]},a))})})]})})})})}},9839:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>m});var r=s(37876),a=s(14232),n=s(49589),o=s(53718),i=s(37308),l=s(31753),c=s(77053),d=s(64990),u=s(1071);let m=e=>{let{t}=(0,l.Bd)("common"),[s,m]=(0,a.useState)({title:"",timeline:[],description:"",hazard_type:{title:""},hazard:[],syndrome:{title:""},created_at:"",updated_at:"",country:{title:""},status:{title:""},start_date:"",end_date:"",partners:[],images:[],images_src:[],document:[],doc_src:[]}),[p,h]=(0,a.useState)(!1),[g,_]=(0,a.useState)(!1),[x,j]=(0,a.useState)([]),[f,N]=(0,a.useState)([]),[A,E]=(0,a.useState)(!1),y={sort:{doc_created_at:"asc"},Doctable:!0},v={sort:{doc_created_at:"asc"},DocUpdatetable:!0},D=async()=>{let t=[];_(!0);let s=await o.A.get("/operation/".concat(e.routes[1]),y);s&&Array.isArray(s)&&s.length>=1&&s[0].document&&s[0].document.length>=1&&(s.forEach(e=>{e.document&&e.document.length>0&&e.document.map((s,r)=>{s.description=e.document[r].docsrc,t.push(s)})}),j(t)),_(!1)},S=async()=>{let t=[];_(!0);let s=await o.A.get("/operation/".concat(e.routes[1]),v);s&&Array.isArray(s)&&s.length>=1&&s[0].document&&s[0].document.length>=1&&(s.forEach(e=>{e.document&&e.document.length>0&&e.document.map((s,r)=>{s.description=e.document[r].docsrc,t.push(s)})}),N(t)),_(!1)};(0,a.useEffect)(()=>{w()},[]);let w=async()=>{let t=await o.A.post("/users/getLoggedUser",{});t&&t.roles&&t.roles.length&&e.routes&&e.routes[1]&&((async s=>{h(!0);let r=await o.A.get("/operation/".concat(e.routes[1]),s);r&&(m(r),function(e,t){E(!1),t&&t.roles&&(t.roles.includes("SUPER_ADMIN")||t.roles.filter(e=>"EMT_NATIONAL_FOCALPOINT"==e).length>0&&e.user._id==t._id||t.roles.filter(e=>"EMT"==e).length>0&&e.user._id==t._id||t.roles.filter(e=>"INIG_STAKEHOLDER"==e).length>0&&e.user._id==t._id||t.roles.filter(e=>"GENERAL_USER"==e).length>0&&e.user._id==t._id?E(!0):t.roles.filter(e=>"PLATFORM_ADMIN"==e).length>0&&e.user._id==t._id&&E(!0))}(r,t)),h(!1)})({}),D(),S())},T={operationData:s,routeData:e,editAccess:A,documentAccoirdianProps:{loading:g,sortProps:e=>{y.sort={[e.columnSelector]:e.sortDirection},D()},Document:x,updateDocument:f,sortUpdateProps:e=>{v.sort={[e.columnSelector]:e.sortDirection},S()}}};return(0,r.jsx)(r.Fragment,{children:(null==s?void 0:s.title)?(0,r.jsxs)(n.A,{className:"operationDetail",fluid:!0,children:[(0,r.jsx)(i.A,{routes:e.routes}),(0,r.jsx)(c.default,{...T}),(0,r.jsx)(u.default,{operation:s}),(0,r.jsx)(d.default,{...T})]}):(0,r.jsx)(r.Fragment,{})})}},27164:(e,t,s)=>{(window.__NEXT_P=window.__NEXT_P||[]).push(["/operation/[...routes]",function(){return s(95273)}])},95273:(e,t,s)=>{"use strict";s.r(t),s.d(t,{__N_SSP:()=>l,default:()=>c});var r=s(37876),a=s(89099),n=s(16331),o=s(9839),i=s(22352),l=!0;let c=()=>{let e=(0,a.useRouter)().query.routes||[],t=(0,i.canAddOperationForm)(()=>(0,r.jsx)(n.default,{routes:e}));switch(e[0]){case"create":return(0,r.jsx)(t,{});case"edit":return(0,r.jsx)(n.default,{routes:e});case"show":return(0,r.jsx)(o.default,{routes:e});default:return null}}}},e=>{var t=t=>e(e.s=t);e.O(0,[7725,9759,1121,6701,9773,1772,7126,698,7336,8220,5939,8477,7308,4672,7053,6331,636,6593,8792],()=>t(27164)),_N_E=e.O()}]);
//# sourceMappingURL=[...routes]-7ce9e9fbadcf3ca6.js.map