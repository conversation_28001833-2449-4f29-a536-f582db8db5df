"use strict";(()=>{var e={};e.id=9049,e.ids=[636,3220,9049],e.modules={123:e=>{e.exports=require("dom-helpers/removeEventListener")},1332:e=>{e.exports=require("react-custom-scrollbars-2")},1428:e=>{e.exports=import("axios")},1680:e=>{e.exports=require("@restart/ui/utils")},1919:e=>{e.exports=require("react-transition-group/Transition")},3892:e=>{e.exports=require("classnames")},4048:e=>{e.exports=require("react-truncate")},6009:e=>{e.exports=require("dom-helpers/ownerDocument")},6417:(e,r,t)=>{t.d(r,{A:()=>a});let s=t(82015).createContext(null);s.displayName="CardHeaderContext";let a=s},6952:e=>{e.exports=require("@restart/ui/Modal")},7374:e=>{e.exports=require("@restart/ui/Dropdown")},8732:e=>{e.exports=require("react/jsx-runtime")},9653:e=>{e.exports=require("@restart/ui/Overlay")},11242:e=>{e.exports=require("redux-persist/integration/react")},11688:e=>{e.exports=require("@restart/hooks/useWillUnmount")},12388:e=>{e.exports=require("dom-helpers/addEventListener")},13364:e=>{e.exports=require("redux-saga/effects")},14062:e=>{e.exports=import("react-redux")},14078:e=>{e.exports=import("swr")},14332:e=>{e.exports=require("uncontrollable")},15653:(e,r,t)=>{t.d(r,{ks:()=>o,s3:()=>n});var s=t(8732);t(82015);var a=t(59549),i=t(43294);let o=({name:e,id:r,required:t,validator:o,errorMessage:n,onChange:d,value:l,as:u,multiline:p,rows:c,pattern:m,...x})=>(0,s.jsx)(i.Field,{name:e,validate:e=>{let r="string"==typeof e?e:String(e||"");return t&&(!e||""===r.trim())?n?.validator||"This field is required":o&&!o(e)?n?.validator||"Invalid value":m&&e&&!new RegExp(m).test(e)?n?.pattern||"Invalid format":void 0},children:({field:e,meta:t})=>(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)(a.A.Control,{...e,...x,id:r,as:u||"input",rows:c,isInvalid:t.touched&&!!t.error,onChange:r=>{e.onChange(r),d&&d(r)},value:void 0!==l?l:e.value}),t.touched&&t.error?(0,s.jsx)(a.A.Control.Feedback,{type:"invalid",children:t.error}):null]})}),n=({name:e,id:r,required:t,errorMessage:o,onChange:n,value:d,children:l,...u})=>(0,s.jsx)(i.Field,{name:e,validate:e=>{if(t&&(!e||""===e))return o?.validator||"This field is required"},children:({field:e,meta:t})=>(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)(a.A.Control,{as:"select",...e,...u,id:r,isInvalid:t.touched&&!!t.error,onChange:r=>{e.onChange(r),n&&n(r)},value:void 0!==d?d:e.value,children:l}),t.touched&&t.error?(0,s.jsx)(a.A.Control.Feedback,{type:"invalid",children:t.error}):null]})})},16116:e=>{e.exports=require("invariant")},18597:(e,r,t)=>{t.d(r,{A:()=>A});var s=t(3892),a=t.n(s),i=t(82015),o=t(80739),n=t(8732);let d=i.forwardRef(({className:e,bsPrefix:r,as:t="div",...s},i)=>(r=(0,o.oU)(r,"card-body"),(0,n.jsx)(t,{ref:i,className:a()(e,r),...s})));d.displayName="CardBody";let l=i.forwardRef(({className:e,bsPrefix:r,as:t="div",...s},i)=>(r=(0,o.oU)(r,"card-footer"),(0,n.jsx)(t,{ref:i,className:a()(e,r),...s})));l.displayName="CardFooter";var u=t(6417);let p=i.forwardRef(({bsPrefix:e,className:r,as:t="div",...s},d)=>{let l=(0,o.oU)(e,"card-header"),p=(0,i.useMemo)(()=>({cardHeaderBsPrefix:l}),[l]);return(0,n.jsx)(u.A.Provider,{value:p,children:(0,n.jsx)(t,{ref:d,...s,className:a()(r,l)})})});p.displayName="CardHeader";let c=i.forwardRef(({bsPrefix:e,className:r,variant:t,as:s="img",...i},d)=>{let l=(0,o.oU)(e,"card-img");return(0,n.jsx)(s,{ref:d,className:a()(t?`${l}-${t}`:l,r),...i})});c.displayName="CardImg";let m=i.forwardRef(({className:e,bsPrefix:r,as:t="div",...s},i)=>(r=(0,o.oU)(r,"card-img-overlay"),(0,n.jsx)(t,{ref:i,className:a()(e,r),...s})));m.displayName="CardImgOverlay";let x=i.forwardRef(({className:e,bsPrefix:r,as:t="a",...s},i)=>(r=(0,o.oU)(r,"card-link"),(0,n.jsx)(t,{ref:i,className:a()(e,r),...s})));x.displayName="CardLink";var h=t(7783);let g=(0,h.A)("h6"),f=i.forwardRef(({className:e,bsPrefix:r,as:t=g,...s},i)=>(r=(0,o.oU)(r,"card-subtitle"),(0,n.jsx)(t,{ref:i,className:a()(e,r),...s})));f.displayName="CardSubtitle";let v=i.forwardRef(({className:e,bsPrefix:r,as:t="p",...s},i)=>(r=(0,o.oU)(r,"card-text"),(0,n.jsx)(t,{ref:i,className:a()(e,r),...s})));v.displayName="CardText";let y=(0,h.A)("h5"),q=i.forwardRef(({className:e,bsPrefix:r,as:t=y,...s},i)=>(r=(0,o.oU)(r,"card-title"),(0,n.jsx)(t,{ref:i,className:a()(e,r),...s})));q.displayName="CardTitle";let j=i.forwardRef(({bsPrefix:e,className:r,bg:t,text:s,border:i,body:l=!1,children:u,as:p="div",...c},m)=>{let x=(0,o.oU)(e,"card");return(0,n.jsx)(p,{ref:m,...c,className:a()(r,x,t&&`bg-${t}`,s&&`text-${s}`,i&&`border-${i}`),children:l?(0,n.jsx)(d,{children:u}):u})});j.displayName="Card";let A=Object.assign(j,{Img:c,Title:q,Subtitle:f,Body:d,Link:x,Text:v,Header:p,Footer:l,ImgOverlay:m})},18622:e=>{e.exports=require("yup")},19121:e=>{e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},22326:e=>{e.exports=require("react-dom")},22541:e=>{e.exports=require("redux-persist/lib/storage")},23579:(e,r,t)=>{t.d(r,{sx:()=>u,s3:()=>a.s3,ks:()=>a.ks,yk:()=>s.A});var s=t(66994),a=t(15653),i=t(8732),o=t(82015),n=t.n(o),d=t(43294),l=t(59549);let u={RadioGroup:({name:e,valueSelected:r,onChange:t,errorMessage:s,children:a})=>{let{errors:o,touched:l}=(0,d.useFormikContext)(),u=l[e]&&o[e];n().useMemo(()=>({name:e}),[e]);let p=n().Children.map(a,r=>n().isValidElement(r)&&function(e){return"object"==typeof e&&null!==e}(r.props)?n().cloneElement(r,{name:e,...r.props}):r);return(0,i.jsxs)("div",{children:[(0,i.jsx)("div",{className:"radio-group",children:p}),u&&(0,i.jsx)("div",{className:"invalid-feedback d-block",children:s||("string"==typeof o[e]?o[e]:String(o[e]))})]})},RadioItem:({id:e,label:r,value:t,name:s,disabled:a})=>{let{values:o,setFieldValue:n}=(0,d.useFormikContext)(),u=s||e;return(0,i.jsx)(l.A.Check,{type:"radio",id:e,label:r,value:t,name:u,checked:o[u]===t,onChange:e=>{n(u,e.target.value)},disabled:a,inline:!0})}};s.A,a.ks,a.s3},24047:(e,r,t)=>{t.d(r,{x:()=>o});var s=t(8732),a=t(82015);let i=({value:e,onChange:r,placeholder:t="Write something...",height:i=300,disabled:o=!1})=>{let n=(0,a.useRef)(null),[d,l]=(0,a.useState)(!1);return(0,a.useEffect)(()=>{n.current},[e,d]),(0,s.jsx)("div",{className:"simple-rich-text-editor",style:{border:"1px solid #ccc"},children:!1})},o=e=>{let{initContent:r,onChange:t}=e;return(0,s.jsx)(i,{value:r||"",onChange:e=>t(e)})}},25303:e=>{e.exports=require("@restart/ui/NavItem")},26324:e=>{e.exports=require("warning")},27825:e=>{e.exports=require("lodash")},27910:e=>{e.exports=require("stream")},28217:e=>{e.exports=require("@restart/ui/DropdownItem")},29021:e=>{e.exports=require("fs")},29294:e=>{e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},29628:e=>{e.exports=require("dom-helpers/addClass")},29825:e=>{e.exports=require("prop-types")},29841:e=>{e.exports=require("dom-helpers/hasClass")},30362:e=>{e.exports=require("@restart/ui/DropdownToggle")},33873:e=>{e.exports=require("path")},36653:e=>{e.exports=require("nprogress")},36955:e=>{e.exports=require("@restart/hooks/useBreakpoint")},37766:e=>{e.exports=require("@restart/hooks/useMergedRefs")},39756:e=>{e.exports=import("redux")},40051:e=>{e.exports=require("dom-helpers/removeClass")},40361:e=>{e.exports=require("next/dist/compiled/next-server/pages.runtime.prod.js")},42893:e=>{e.exports=import("react-hot-toast")},43294:e=>{e.exports=require("formik")},46621:(e,r,t)=>{t.a(e,async(e,s)=>{try{t.r(r),t.d(r,{default:()=>b});var a=t(8732),i=t(7082),o=t(18597),n=t(83551),d=t(49481),l=t(59549),u=t(91353),p=t(23579),c=t(66994),m=t(82015),x=t(42893),h=t(44233),g=t.n(h),f=t(19918),v=t.n(f),y=t(63487),q=t(88751),j=t(24047),A=e([x,y]);[x,y]=A.then?(await A)():A;let b=e=>{let r={title:"",code:"",description:""},{t}=(0,q.useTranslation)("common"),[s,h]=(0,m.useState)(r),f=e.routes&&"edit_syndrome"===e.routes[0]&&e.routes[1],A=async(r,a)=>{let i,o;r.preventDefault();let n={title:s.title.trim(),code:s.code,description:s.description};f?(o="adminsetting.syndrome.Syndromeisupdatedsuccessfully",i=await y.A.patch(`/syndrome/${e.routes[1]}`,n)):(o="adminsetting.syndrome.Syndromeisaddedsuccessfully",i=await y.A.post("/syndrome",n)),i&&i._id?(x.default.success(t(o)),g().push("/adminsettings/syndrome")):x.default.error(i)},b=e=>{if(e.target){let{name:r,value:t}=e.target;h(e=>({...e,[r]:t}))}},S=e=>{h(r=>({...r,description:e}))};(0,m.useEffect)(()=>{let r={query:{},sort:{title:"asc"},limit:"~"};f&&(async()=>{let t=await y.A.get(`/syndrome/${e.routes[1]}`,r);h(e=>({...e,...t}))})()},[]);let P=(0,m.useRef)(null);return(0,a.jsx)(i.A,{className:"formCard",fluid:!0,children:(0,a.jsx)(o.A,{style:{marginTop:"5px",boxShadow:"0 4px 8px 0 rgba(0, 0, 0, 0.2), 0 6px 20px 0 rgba(0, 0, 0, 0.19)"},children:(0,a.jsx)(c.A,{onSubmit:A,ref:P,initialValues:s,enableReinitialize:!0,children:(0,a.jsxs)(o.A.Body,{children:[(0,a.jsx)(n.A,{children:(0,a.jsx)(d.A,{children:(0,a.jsx)(o.A.Title,{children:f?t("adminsetting.syndrome.EditSyndrome"):t("adminsetting.syndrome.AddSyndrome")})})}),(0,a.jsx)("hr",{}),(0,a.jsxs)(n.A,{children:[(0,a.jsx)(d.A,{md:!0,lg:6,sm:12,children:(0,a.jsxs)(l.A.Group,{children:[(0,a.jsx)(l.A.Label,{className:"required-field",children:t("adminsetting.syndrome.SyndromeName")}),(0,a.jsx)(p.ks,{name:"title",id:"title",required:!0,value:s.title,validator:e=>""!==e.trim(),errorMessage:{validator:t("adminsetting.syndrome.PleaseAddtheSyndromeName")},onChange:b})]})}),(0,a.jsx)(d.A,{md:!0,lg:6,sm:12,children:(0,a.jsxs)(l.A.Group,{children:[(0,a.jsx)(l.A.Label,{children:t("adminsetting.syndrome.Code")}),(0,a.jsx)(p.ks,{name:"code",id:"code",required:!0,value:s.code,errorMessage:{validator:t("adminsetting.syndrome.PleaseAddtheCode")},onChange:b})]})})]}),(0,a.jsx)(n.A,{children:(0,a.jsx)(d.A,{children:(0,a.jsxs)(l.A.Group,{children:[(0,a.jsx)(l.A.Label,{children:t("adminsetting.syndrome.Description")}),(0,a.jsx)(j.x,{initContent:s.description,onChange:e=>S(e)})]})})}),(0,a.jsx)(n.A,{className:"my-4",children:(0,a.jsxs)(d.A,{children:[(0,a.jsx)(u.A,{className:"me-2",type:"submit",variant:"primary",children:t("adminsetting.syndrome.Submit")}),(0,a.jsx)(u.A,{className:"me-2",onClick:()=>{h(r),window.scrollTo(0,0)},variant:"info",children:t("adminsetting.syndrome.Reset")}),(0,a.jsx)(v(),{href:"/adminsettings/[...routes]",as:"/adminsettings/syndrome",children:(0,a.jsx)(u.A,{variant:"secondary",children:t("adminsetting.syndrome.Cancel")})})]})})]})})})})};s()}catch(e){s(e)}})},49575:(e,r,t)=>{t.a(e,async(e,s)=>{try{t.r(r),t.d(r,{config:()=>g,default:()=>c,getServerSideProps:()=>h,getStaticPaths:()=>x,getStaticProps:()=>m,reportWebVitals:()=>f,routeModule:()=>b,unstable_getServerProps:()=>j,unstable_getServerSideProps:()=>A,unstable_getStaticParams:()=>q,unstable_getStaticPaths:()=>y,unstable_getStaticProps:()=>v});var a=t(63885),i=t(80237),o=t(81413),n=t(9616),d=t.n(n),l=t(72386),u=t(46621),p=e([l,u]);[l,u]=p.then?(await p)():p;let c=(0,o.M)(u,"default"),m=(0,o.M)(u,"getStaticProps"),x=(0,o.M)(u,"getStaticPaths"),h=(0,o.M)(u,"getServerSideProps"),g=(0,o.M)(u,"config"),f=(0,o.M)(u,"reportWebVitals"),v=(0,o.M)(u,"unstable_getStaticProps"),y=(0,o.M)(u,"unstable_getStaticPaths"),q=(0,o.M)(u,"unstable_getStaticParams"),j=(0,o.M)(u,"unstable_getServerProps"),A=(0,o.M)(u,"unstable_getServerSideProps"),b=new a.PagesRouteModule({definition:{kind:i.A.PAGES,page:"/adminsettings/syndrome/form",pathname:"/adminsettings/syndrome/form",bundlePath:"",filename:""},components:{App:l.default,Document:d()},userland:u});s()}catch(e){s(e)}})},50009:e=>{e.exports=require("@restart/ui/DropdownMenu")},57664:e=>{e.exports=require("dom-helpers/scrollbarSize")},58928:e=>{e.exports=require("dom-helpers/canUseDOM")},59717:e=>{e.exports=require("@restart/ui/DropdownContext")},60560:e=>{e.exports=require("dom-helpers/contains")},63033:e=>{e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},65447:e=>{e.exports=require("@restart/ui/Button")},66994:(e,r,t)=>{t.d(r,{A:()=>d});var s=t(8732),a=t(82015),i=t(43294),o=t(18622);let n=(0,a.forwardRef)((e,r)=>{let{children:t,onSubmit:a,autoComplete:n,className:d,onKeyPress:l,initialValues:u,...p}=e,c=o.object().shape({});return(0,s.jsx)(i.Formik,{initialValues:u||{},validationSchema:c,onSubmit:(e,r)=>{let t={preventDefault:()=>{},stopPropagation:()=>{},currentTarget:null,target:null,nativeEvent:new Event("submit"),bubbles:!1,cancelable:!0,defaultPrevented:!1,eventPhase:0,isTrusted:!1,timeStamp:Date.now(),type:"submit",isDefaultPrevented:()=>!1,isPropagationStopped:()=>!1,persist:()=>{}};a&&a(t,e,r)},...p,children:e=>(0,s.jsx)(i.Form,{ref:r,onSubmit:e.handleSubmit,autoComplete:n,className:d,onKeyPress:l,children:"function"==typeof t?t(e):t})})});n.displayName="ValidationFormWrapper";let d=n},67364:e=>{e.exports=require("@restart/hooks/useCallbackRef")},68455:e=>{e.exports=import("redux-saga")},69722:e=>{e.exports=require("es6-promise")},74075:e=>{e.exports=require("zlib")},74716:e=>{e.exports=require("moment")},74987:e=>{e.exports=require("@restart/ui/ModalManager")},78097:e=>{e.exports=require("next-redux-saga")},78634:e=>{e.exports=require("@restart/ui/Anchor")},80237:(e,r)=>{Object.defineProperty(r,"A",{enumerable:!0,get:function(){return t}});var t=function(e){return e.PAGES="PAGES",e.PAGES_API="PAGES_API",e.APP_PAGE="APP_PAGE",e.APP_ROUTE="APP_ROUTE",e.IMAGE="IMAGE",e}({})},81366:e=>{e.exports=require("redux-auth-wrapper/connectedAuthWrapper")},81413:(e,r)=>{Object.defineProperty(r,"M",{enumerable:!0,get:function(){return function e(r,t){return t in r?r[t]:"then"in r&&"function"==typeof r.then?r.then(r=>e(r,t)):"function"==typeof r&&"default"===t?r:void 0}}})},81521:e=>{e.exports=require("dom-helpers/querySelectorAll")},81895:e=>{e.exports=require("@restart/hooks/useEventCallback")},82015:e=>{e.exports=require("react")},86842:e=>{e.exports=require("@restart/ui/SelectableContext")},87571:e=>{e.exports=require("dom-helpers/transitionEnd")},88751:e=>{e.exports=require("next-i18next")},93787:e=>{e.exports=require("redux-persist")},94947:e=>{e.exports=require("@restart/hooks/useTimeout")},96196:e=>{e.exports=require("next-redux-wrapper")},98320:e=>{e.exports=require("@restart/hooks/useIsomorphicEffect")},99460:e=>{e.exports=require("dom-helpers/css")}};var r=require("../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[6089,9216,9616,2386],()=>t(49575));module.exports=s})();