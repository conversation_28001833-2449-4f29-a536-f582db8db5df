(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[1656],{810:(e,s,n)=>{"use strict";n.r(s),n.d(s,{default:()=>m});var i=n(37876),r=n(14232),t=n(11041),a=n(21772),d=n(32890),o=n(31753),c=n(72800);let l={Low:"risk0",Medium:"risk1",High:"risk2","Very High":"risk3"},m=e=>{let{t:s}=(0,o.Bd)("common"),[n,m]=(0,r.useState)(!1),{risk_assessment:u}=e;return(0,i.jsx)(i.Fragment,{children:(0,i.jsxs)(d.A.Item,{eventKey:"0",children:[(0,i.jsxs)(d<PERSON><PERSON><PERSON>,{onClick:()=>m(!n),children:[(0,i.jsx)("div",{className:"cardTitle",children:s("Events.show.RiskAssessment")}),(0,i.jsx)("div",{className:"cardArrow",children:n?(0,i.jsx)(a.g,{icon:t.EZy,color:"#fff"}):(0,i.jsx)(a.g,{icon:t.QLR,color:"#fff"})})]}),(0,i.jsx)(d.A.Body,{children:u.country?(0,i.jsxs)("div",{children:[function(e,s){return(0,i.jsxs)("div",{className:"riskDetails",children:[e.country?(0,i.jsxs)("div",{className:"riskItems",children:[(0,i.jsx)("div",{className:"riskIcon ".concat(l[e.country.title]),children:(0,i.jsx)("img",{src:"/images/event_country.png",width:"30",height:"30",alt:"Risk Assessment Info"})}),(0,i.jsxs)("div",{className:"riskInfo",children:[(0,i.jsx)("h5",{children:s("Events.show.Country")}),(0,i.jsx)("h4",{children:e.country.title})]})]}):(0,i.jsx)(i.Fragment,{}),e.region?(0,i.jsxs)("div",{className:"riskItems",children:[(0,i.jsx)("div",{className:"riskIcon ".concat(e&&e.region?l[e.region.title]:""),children:(0,i.jsx)("img",{src:"/images/event_region.png",width:"35",height:"26",alt:"Risk Assessment Info"})}),(0,i.jsxs)("div",{className:"riskInfo",children:[(0,i.jsx)("h5",{children:s("Events.show.Region")}),(0,i.jsx)("h4",{children:e&&e.region?e.region.title:""})]})]}):(0,i.jsx)(i.Fragment,{}),e.international?(0,i.jsxs)("div",{className:"riskItems",children:[(0,i.jsx)("div",{className:"riskIcon ".concat(e&&e.international?l[e.international.title]:""),children:(0,i.jsx)("img",{src:"/images/event_international.png",width:"38",height:"38",alt:"Risk Assessment Info"})}),(0,i.jsxs)("div",{className:"riskInfo",children:[(0,i.jsx)("h5",{children:s("Events.show.International")}),(0,i.jsx)("h4",{children:e&&e.international?e.international.title:""})]})]}):(0,i.jsx)(i.Fragment,{})]})}(u,s),function(e){return(0,i.jsx)("div",{className:"mt-4",children:(0,i.jsx)(c.A,{description:e&&e.risk_assessment.description?e.risk_assessment.description:""})})}(e)]}):null})]})})}},2481:(e,s,n)=>{"use strict";n.r(s),n.d(s,{canAddEvent:()=>a,canAddEventForm:()=>d,canEditEvent:()=>o,canEditEventForm:()=>c,canViewDiscussionUpdate:()=>l,default:()=>m});var i=n(37876);n(14232);var r=n(8178),t=n(59626);let a=(0,r.A)({authenticatedSelector:e=>!!e.permissions&&!!e.permissions.event&&!!e.permissions.event["create:any"],wrapperDisplayName:"CanAddEvent"}),d=(0,r.A)({authenticatedSelector:e=>!!e.permissions&&!!e.permissions.event&&!!e.permissions.event["create:any"],wrapperDisplayName:"CanAddEventForm",FailureComponent:()=>(0,i.jsx)(t.default,{})}),o=(0,r.A)({authenticatedSelector:(e,s)=>{if(e.permissions&&e.permissions.event){if(e.permissions.event["update:any"])return!0;else if(e.permissions.event["update:own"]&&s.event&&s.event.user&&s.event.user._id===e.user._id)return!0}return!1},wrapperDisplayName:"CanEditEvent"}),c=(0,r.A)({authenticatedSelector:(e,s)=>{if(e.permissions&&e.permissions.event){if(e.permissions.event["update:any"])return!0;else if(e.permissions.event["update:own"]&&s.event&&s.event.user&&s.event.user._id===e.user._id)return!0}return!1},wrapperDisplayName:"CanEditEventForm",FailureComponent:()=>(0,i.jsx)(t.default,{})}),l=(0,r.A)({authenticatedSelector:e=>!!e.permissions&&!!e.permissions.update&&!!e.permissions.update["read:any"],wrapperDisplayName:"CanViewDiscussionUpdate"}),m=a},5850:(e,s,n)=>{"use strict";n.r(s),n.d(s,{default:()=>u});var i=n(37876);n(14232);var r=n(56970),t=n(37784),a=n(32890),d=n(810),o=n(2481),c=n(79499),l=n(7176),m=n(24117);let u=e=>{let s=(0,o.canViewDiscussionUpdate)(()=>(0,i.jsx)(c.default,{...e.routeData}));return(0,i.jsx)(i.Fragment,{children:(0,i.jsx)(r.A,{children:(0,i.jsxs)(t.A,{className:"eventAccordion",xs:12,children:[(0,i.jsx)(a.A,{children:(0,i.jsx)(d.default,{...e.eventData})}),(0,i.jsx)(a.A,{children:(0,i.jsx)(l.default,{...e.eventData})}),(0,i.jsx)(a.A,{children:(0,i.jsx)(m.default,{...e.eventData})}),(0,i.jsx)(a.A,{children:(0,i.jsx)(s,{})})]})})})}},7176:(e,s,n)=>{"use strict";n.r(s),n.d(s,{default:()=>l});var i=n(37876),r=n(14232),t=n(32890),a=n(21772),d=n(11041),o=n(31753),c=n(72800);let l=e=>{let{t:s}=(0,o.Bd)("common"),[n,l]=(0,r.useState)(!1);return(0,i.jsx)(i.Fragment,{children:(0,i.jsxs)(t.A.Item,{eventKey:"0",children:[(0,i.jsxs)(t.A.Header,{onClick:()=>l(!n),children:[(0,i.jsx)("div",{className:"cardTitle",children:s("Events.show.MoreInformation")}),(0,i.jsx)("div",{className:"cardArrow",children:n?(0,i.jsx)(a.g,{icon:d.EZy,color:"#fff"}):(0,i.jsx)(a.g,{icon:d.QLR,color:"#fff"})})]}),(0,i.jsx)(t.A.Body,{children:(0,i.jsx)(c.A,{description:e.more_info})})]})})}},24117:(e,s,n)=>{"use strict";n.r(s),n.d(s,{default:()=>l});var i=n(37876),r=n(14232),t=n(11041),a=n(21772),d=n(32890),o=n(33458),c=n(31753);let l=e=>{let{t:s}=(0,c.Bd)("common"),[n,l]=(0,r.useState)(!1);return(0,i.jsx)(i.Fragment,{children:(0,i.jsxs)(d.A.Item,{eventKey:"0",children:[(0,i.jsxs)(d.A.Header,{onClick:()=>l(!n),children:[(0,i.jsx)("div",{className:"cardTitle",children:s("Events.show.MediaGallery")}),(0,i.jsx)("div",{className:"cardArrow",children:n?(0,i.jsx)(a.g,{icon:t.EZy,color:"#fff"}):(0,i.jsx)(a.g,{icon:t.QLR,color:"#fff"})})]}),(0,i.jsx)(d.A.Body,{children:(0,i.jsx)(o.A,{gallery:e.images,imageSource:e.images_src})})]})})}},79499:(e,s,n)=>{"use strict";n.r(s),n.d(s,{default:()=>l});var i=n(37876),r=n(14232),t=n(32890),a=n(21772),d=n(11041),o=n(31753),c=n(48477);let l=e=>{let{t:s}=(0,o.Bd)("common"),[n,l]=(0,r.useState)(!1);return(0,i.jsx)(i.Fragment,{children:(0,i.jsxs)(t.A.Item,{eventKey:"0",children:[(0,i.jsxs)(t.A.Header,{onClick:()=>l(!n),children:[(0,i.jsx)("div",{className:"cardTitle",children:s("Events.show.Discussions")}),(0,i.jsx)("div",{className:"cardArrow",children:n?(0,i.jsx)(a.g,{icon:d.EZy,color:"#fff"}):(0,i.jsx)(a.g,{icon:d.QLR,color:"#fff"})})]}),(0,i.jsx)(t.A.Body,{children:(0,i.jsx)(c.A,{type:"event",id:(null==e?void 0:e.routes)?e.routes[1]:null})})]})})}},84135:function(e,s,n){(function(e){"use strict";function s(e,s,n,i){var r={m:["eine Minute","einer Minute"],h:["eine Stunde","einer Stunde"],d:["ein Tag","einem Tag"],dd:[e+" Tage",e+" Tagen"],w:["eine Woche","einer Woche"],M:["ein Monat","einem Monat"],MM:[e+" Monate",e+" Monaten"],y:["ein Jahr","einem Jahr"],yy:[e+" Jahre",e+" Jahren"]};return s?r[n][0]:r[n][1]}e.defineLocale("de",{months:"Januar_Februar_M\xe4rz_April_Mai_Juni_Juli_August_September_Oktober_November_Dezember".split("_"),monthsShort:"Jan._Feb._M\xe4rz_Apr._Mai_Juni_Juli_Aug._Sep._Okt._Nov._Dez.".split("_"),monthsParseExact:!0,weekdays:"Sonntag_Montag_Dienstag_Mittwoch_Donnerstag_Freitag_Samstag".split("_"),weekdaysShort:"So._Mo._Di._Mi._Do._Fr._Sa.".split("_"),weekdaysMin:"So_Mo_Di_Mi_Do_Fr_Sa".split("_"),weekdaysParseExact:!0,longDateFormat:{LT:"HH:mm",LTS:"HH:mm:ss",L:"DD.MM.YYYY",LL:"D. MMMM YYYY",LLL:"D. MMMM YYYY HH:mm",LLLL:"dddd, D. MMMM YYYY HH:mm"},calendar:{sameDay:"[heute um] LT [Uhr]",sameElse:"L",nextDay:"[morgen um] LT [Uhr]",nextWeek:"dddd [um] LT [Uhr]",lastDay:"[gestern um] LT [Uhr]",lastWeek:"[letzten] dddd [um] LT [Uhr]"},relativeTime:{future:"in %s",past:"vor %s",s:"ein paar Sekunden",ss:"%d Sekunden",m:s,mm:"%d Minuten",h:s,hh:"%d Stunden",d:s,dd:s,w:s,ww:"%d Wochen",M:s,MM:s,y:s,yy:s},dayOfMonthOrdinalParse:/\d{1,2}\./,ordinal:"%d.",week:{dow:1,doy:4}})})(n(10841))}}]);
//# sourceMappingURL=1656-97f00f4fbdb37d6c.js.map