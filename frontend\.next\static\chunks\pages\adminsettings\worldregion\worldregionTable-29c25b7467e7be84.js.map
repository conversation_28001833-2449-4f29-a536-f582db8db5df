{"version": 3, "file": "static/chunks/pages/adminsettings/worldregion/worldregionTable-29c25b7467e7be84.js", "mappings": "yOAyIA,MA9H0BA,IACtB,GAAM,GAAEC,CAAC,CAAE,CAAGC,CAAAA,EAAAA,EAAAA,EAAAA,CAAcA,CAAC,MA6HlBC,IA5HL,CAACC,EAAWC,EAAe,CAAGC,CAAAA,EAAAA,EAAAA,CA4HTH,EAAC,KA5HQG,CAAQA,CAAC,EAAE,EACzC,EAAGC,EAAW,CAAGD,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,EAAC,GAC1B,CAACE,EAAWC,EAAa,CAAGH,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAC,GACrC,CAACI,EAASC,EAAW,CAAGL,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAC,IACjC,CAACM,EAAaC,EAAS,CAAGP,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,EAAC,GACnC,CAACQ,EAAmBC,EAAqB,CAAGT,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAC,CAAC,GAEtDU,EAAoB,CACtBC,KAAM,CAAEC,MAAO,KAAM,EACrBC,MAAOT,EACPU,KAAM,EACNC,MAAO,CAAC,CACZ,EAEMC,EAAU,CACZ,CACIC,KAAMtB,EAAE,wCACRuB,SAAU,GAAcC,EAAIP,KAAK,CACjCQ,UAAU,CACd,EACA,CACIH,KAAMtB,EAAE,uCACRuB,SAAU,GAAcC,EAAIE,IAAI,CAChCD,UAAU,EACVE,KAAOC,GAAWA,EAAEF,IAAI,EAE5B,CACIJ,KAAMtB,EAAE,yCACRuB,SAAU,GAAcC,EAAIK,GAAG,CAC/BJ,UAAU,EACVE,KAAM,GACF,WAACG,MAAAA,WACG,UAACC,IAAIA,CAACC,KAAK,6BAA6BC,GAAI,OAAvCF,4BAAgF,OAANH,EAAEC,GAAG,WAEhF,UAACK,IAAAA,CAAEC,UAAU,uBAEV,OAEP,UAACC,IAAAA,CAAEC,QAAS,IAAMC,EAAWV,YACzB,UAACM,IAAAA,CAAEC,UAAU,4BACZ,MAGjB,EACH,CAEKI,EAAqB,UACvBjC,EAAW,IACX,IAAMkC,EAAW,MAAMC,EAAAA,CAAUA,CAACC,GAAG,CAAC,eAAgB3B,GAClDyB,GAAYA,EAASG,IAAI,EAAIH,EAASG,IAAI,CAACC,MAAM,CAAG,GAAG,CACvDxC,EAAeoC,EAASG,IAAI,EAC5BnC,EAAagC,EAASK,UAAU,EAChCvC,GAAW,GAEnB,EAOMwC,EAAsB,MAAOC,EAAiB5B,KAChDJ,EAAkBG,KAAK,CAAG6B,EAC1BhC,EAAkBI,IAAI,CAAGA,EACzBb,GAAW,GACX,IAAMkC,EAAW,MAAMC,EAAAA,CAAUA,CAACC,GAAG,CAAC,eAAgB3B,GAClDyB,GAAYA,EAASG,IAAI,EAAIH,EAASG,IAAI,CAACC,MAAM,CAAG,GAAG,CACvDxC,EAAeoC,EAASG,IAAI,EAC5BjC,EAAWqC,GACXzC,GAAW,GAEnB,EAEMgC,EAAa,MAAOd,IACtBV,EAAqBU,EAAIK,GAAG,EAC5BjB,GAAS,EACb,EAEMoC,EAAe,UACjB,GAAI,CACA,MAAMP,EAAAA,CAAUA,CAACQ,MAAM,CAAC,gBAAkC,OAAlBpC,IACxC0B,IACA3B,GAAS,GACTsC,EAAAA,EAAKA,CAACC,OAAO,CAACnD,EAAE,iEACpB,CAAE,MAAOoD,EAAO,CACZF,EAAAA,EAAKA,CAACE,KAAK,CAACpD,EAAE,2DAClB,CACJ,EAEMqD,EAAY,IAAMzC,GAAS,GAMjC,MAJA0C,CAAAA,EAAAA,EAAAA,SAAAA,CAASA,CAAC,KACNf,GACJ,EAAG,EAAE,EAGD,WAACT,MAAAA,WACG,WAACyB,EAAAA,CAAKA,CAAAA,CAACC,KAAM7C,EAAa8C,OAAQJ,YAC9B,UAACE,EAAAA,CAAKA,CAACG,MAAM,EAACC,WAAW,aACrB,UAACJ,EAAAA,CAAKA,CAACK,KAAK,WAAE5D,EAAE,wDAEpB,UAACuD,EAAAA,CAAKA,CAACM,IAAI,WAAE7D,EAAE,2EACf,WAACuD,EAAAA,CAAKA,CAACO,MAAM,YACT,UAACC,EAAAA,CAAMA,CAAAA,CAACC,QAAQ,YAAY3B,QAASgB,WAChCrD,EAAE,2CAEP,UAAC+D,EAAAA,CAAMA,CAAAA,CAACC,QAAQ,UAAU3B,QAASW,WAC9BhD,EAAE,8CAKf,UAACiE,EAAAA,CAAQA,CAAAA,CACL5C,QAASA,EACTsB,KAAMxC,EACNI,UAAWA,EACX2D,WAAW,EACXpB,oBAAqBA,EACrBqB,iBA/Da,CA+DKA,GA9D1BpD,EAAkBG,KAAK,CAAGT,EAC1BM,EAAkBI,IAAI,CAAGA,EACzBoB,GACJ,MA+DJ,6GClGA,SAAS0B,EAASG,CAAoB,EACpC,GAAM,GAAEpE,CAAC,CAAE,CAAGC,CAAAA,EAAAA,EAAAA,EAAAA,CAAcA,CAAC,UACvBoE,EAA6B,CACjCC,gBAAiBtE,EAAE,cACnB,EACI,SACJqB,CAAO,MACPsB,CAAI,WACJpC,CAAS,uBACTgE,CAAqB,WACrBC,CAAS,oBACTC,CAAkB,qBAClB3B,CAAmB,kBACnBqB,CAAgB,aAChBO,CAAW,oBACXC,CAAkB,gBAClBC,CAAc,SACdC,CAAO,CACPX,WAAS,sBACTY,CAAoB,mBACpBC,CAAiB,YACjBC,CAAU,QACVC,CAAM,kBACNC,CAAgB,cAChBC,CAAY,CAEZ,CADA,EACGC,EACJ,CAAGhB,EAGEiB,EAAiB,4BACrBhB,EACAiB,gBAAiBtF,EAAE,IAP0C,MAQ7DuF,UAAU,UACVlE,EACAsB,KAAMA,GAAQ,EAAE,CAChB6C,OAAO,EACPC,2BAA4BlB,EAC5BmB,UAAWlB,EACXmB,gBAAiBd,qBACjBJ,EACAmB,YAAY,EACZC,iBAAkB3B,EAClB4B,kBAAmBnB,GAA0C,GAC7DoB,eADwCpB,cACVD,GAA4B,CAAC,GAAI,GAAI,GAAI,CAA3BA,EAA+B,GAAG,CAC9EsB,oBAAqBzF,EACrB0F,oBAAqBnD,EACrBoD,aAAc/B,iBACdS,uBACAE,oBACAC,EACAoB,kBAAmB,UAACC,EAAAA,CAAYA,CAAAA,CAAAA,GAChCC,SAAU,UAACnE,IAAAA,CAAEC,UAAU,6CACvB6C,SACAC,eACAE,mBACAD,EACA/C,UAAW,WACb,EACA,MACE,UAACmE,EAAAA,EAASA,CAAAA,CAAE,GAAGjB,CAAc,EAEjC,CAEApB,EAASsC,YAAY,CAAG,CACtBb,UAAW,GACXE,YAAY,EACZrF,UAAW,KACX2D,WAAW,EACXY,qBAAsB,KACtBC,mBAAmB,EACnBC,YAAY,EACZE,kBAAkB,CACpB,EAEA,MAAejB,QAAQA,EAAC,SC/GxB,4CACA,8CACA,WACA,OAAe,EAAQ,IAAmE,CAC1F,EACA,UAFsB", "sources": ["webpack://_N_E/./pages/adminsettings/worldregion/worldregionTable.tsx", "webpack://_N_E/./components/common/RKITable.tsx", "webpack://_N_E/?8c13"], "sourcesContent": ["//Import Library\r\nimport { useState, useEffect } from \"react\";\r\nimport Link from \"next/link\";\r\nimport { <PERSON><PERSON>, Button } from \"react-bootstrap\";\r\n\r\nimport toast from 'react-hot-toast';\r\n\r\n//Import services/components\r\nimport RKITable from \"../../../components/common/RKITable\";\r\nimport apiService from \"../../../services/apiService\";\r\nimport { useTranslation } from 'next-i18next';\r\nconst WorldregionTable = (_props: any) => {\r\n    const { t } = useTranslation('common');\r\n    const [tabledata, setDataToTable] = useState([]);\r\n    const [, setLoading] = useState(false);\r\n    const [totalRows, setTotalRows] = useState(0);\r\n    const [perPage, setPerPage] = useState(10);\r\n    const [isModalShow, setModal] = useState(false);\r\n    const [selectWorldregion, setSelectWorldregion] = useState({});\r\n\r\n    const worldregionParams = {\r\n        sort: { title: \"asc\" },\r\n        limit: perPage,\r\n        page: 1,\r\n        query: {},\r\n    };\r\n\r\n    const columns = [\r\n        {\r\n            name: t(\"adminsetting.worldregion.table.Title\"),\r\n            selector: (row: any) => row.title,\r\n            sortable: true,\r\n        },\r\n        {\r\n            name: t(\"adminsetting.worldregion.table.Code\"),\r\n            selector: (row: any) => row.code,\r\n            sortable: true,\r\n            cell: (d: any) => d.code,\r\n        },\r\n        {\r\n            name: t(\"adminsetting.worldregion.table.Action\"),\r\n            selector: (row: any) => row._id,\r\n            sortable: false,\r\n            cell: (d: any) => (\r\n                <div>\r\n                    <Link href=\"/adminsettings/[...routes]\" as={`/adminsettings/edit_worldregion/${d._id}`}>\r\n\r\n                        <i className=\"icon fas fa-edit\" />\r\n\r\n                    </Link>\r\n                    &nbsp;\r\n                    <a onClick={() => userAction(d)}>\r\n                        <i className=\"icon fas fa-trash-alt\" />\r\n                    </a>{\" \"}\r\n                </div>\r\n            ),\r\n        },\r\n    ];\r\n\r\n    const getWorldregionData = async () => {\r\n        setLoading(true);\r\n        const response = await apiService.get(\"/worldregion\", worldregionParams);\r\n        if (response && response.data && response.data.length > 0) {\r\n            setDataToTable(response.data);\r\n            setTotalRows(response.totalCount);\r\n            setLoading(false);\r\n        }\r\n    };\r\n    const handlePageChange = (page: any) => {\r\n        worldregionParams.limit = perPage;\r\n        worldregionParams.page = page;\r\n        getWorldregionData();\r\n    };\r\n\r\n    const handlePerRowsChange = async (newPerPage: any, page: any) => {\r\n        worldregionParams.limit = newPerPage;\r\n        worldregionParams.page = page;\r\n        setLoading(true);\r\n        const response = await apiService.get(\"/worldregion\", worldregionParams);\r\n        if (response && response.data && response.data.length > 0) {\r\n            setDataToTable(response.data);\r\n            setPerPage(newPerPage);\r\n            setLoading(false);\r\n        }\r\n    };\r\n\r\n    const userAction = async (row: any) => {\r\n        setSelectWorldregion(row._id);\r\n        setModal(true);\r\n    };\r\n\r\n    const modalConfirm = async () => {\r\n        try {\r\n            await apiService.remove(`/worldregion/${selectWorldregion}`);\r\n            getWorldregionData();\r\n            setModal(false);\r\n            toast.success(t(\"adminsetting.worldregion.table.worldRegionDeletedSuccessfully\"));\r\n        } catch (error) {\r\n            toast.error(t(\"adminsetting.worldregion.table.errorDeletingWorldRegion\"));\r\n        }\r\n    };\r\n\r\n    const modalHide = () => setModal(false);\r\n\r\n    useEffect(() => {\r\n        getWorldregionData();\r\n    }, []);\r\n\r\n    return (\r\n        <div>\r\n            <Modal show={isModalShow} onHide={modalHide}>\r\n                <Modal.Header closeButton>\r\n                    <Modal.Title>{t(\"adminsetting.worldregion.table.DeleteWorldregion\")}</Modal.Title>\r\n                </Modal.Header>\r\n                <Modal.Body>{t(\"adminsetting.worldregion.table.Areyousurewanttodeletethisworldregion?\")}</Modal.Body>\r\n                <Modal.Footer>\r\n                    <Button variant=\"secondary\" onClick={modalHide}>\r\n                        {t(\"adminsetting.worldregion.table.Cancel\")}\r\n                    </Button>\r\n                    <Button variant=\"primary\" onClick={modalConfirm}>\r\n                        {t(\"adminsetting.worldregion.table.Yes\")}\r\n                    </Button>\r\n                </Modal.Footer>\r\n            </Modal>\r\n\r\n            <RKITable\r\n                columns={columns}\r\n                data={tabledata}\r\n                totalRows={totalRows}\r\n                pagServer={true}\r\n                handlePerRowsChange={handlePerRowsChange}\r\n                handlePageChange={handlePageChange}\r\n            />\r\n        </div>\r\n    );\r\n};\r\n\r\nexport default WorldregionTable;\r\n", "//Import Library\r\nimport React from 'react'\r\nimport DataTable  from 'react-data-table-component';\r\n\r\n//Import services/components\r\nimport { useTranslation } from 'next-i18next';\r\nimport CustomLoader from './CustomLoader';\r\n\r\ninterface RKITableProps {\r\n  columns: any[];\r\n  data: any[];\r\n  totalRows?: number;\r\n  resetPaginationToggle?: boolean;\r\n  subheader?: boolean;\r\n  subHeaderComponent?: React.ReactNode;\r\n  handlePerRowsChange?: (newPerPage: number, page: number) => void;\r\n  handlePageChange?: (page: number) => void;\r\n  rowsPerPage?: number[];\r\n  defaultRowsPerPage?: number;\r\n  selectableRows?: boolean;\r\n  loading?: boolean;\r\n  pagServer?: boolean;\r\n  onSelectedRowsChange?: (selectedRows: any) => void;\r\n  clearSelectedRows?: boolean;\r\n  sortServer?: boolean;\r\n  onSort?: (column: any, sortDirection: string) => void;\r\n  persistTableHead?: boolean;\r\n  sortFunction?: (rows: any[], field: any, direction: 'asc' | 'desc') => any[];\r\n  noHeader?: boolean;\r\n  dense?: boolean;\r\n  pagination?: boolean;\r\n  paginationServer?: boolean;\r\n  paginationTotalRows?: number;\r\n  subHeaderAlign?: string;\r\n  [key: string]: any;\r\n}\r\n\r\nfunction RKITable(props: RKITableProps) {\r\n  const { t } = useTranslation('common');\r\n  const paginationComponentOptions = {\r\n    rowsPerPageText: t('Rowsperpage'),\r\n    };\r\n  const {\r\n    columns,\r\n    data,\r\n    totalRows,\r\n    resetPaginationToggle,\r\n    subheader,\r\n    subHeaderComponent,\r\n    handlePerRowsChange,\r\n    handlePageChange,\r\n    rowsPerPage,\r\n    defaultRowsPerPage,\r\n    selectableRows,\r\n    loading,\r\n    pagServer,\r\n    onSelectedRowsChange,\r\n    clearSelectedRows,\r\n    sortServer,\r\n    onSort,\r\n    persistTableHead,\r\n    sortFunction,\r\n    // Filter out any unknown props that might cause DOM warnings\r\n    ...otherProps\r\n  } = props;\r\n\r\n  // Only pass known DataTable props to avoid DOM warnings\r\n  const dataTableProps = {\r\n    paginationComponentOptions,\r\n    noDataComponent: t(\"NoData\"),\r\n    noHeader: true,\r\n    columns,\r\n    data: data || [],\r\n    dense: true,\r\n    paginationResetDefaultPage: resetPaginationToggle,\r\n    subHeader: subheader,\r\n    progressPending: loading,\r\n    subHeaderComponent,\r\n    pagination: true,\r\n    paginationServer: pagServer,\r\n    paginationPerPage: defaultRowsPerPage ? defaultRowsPerPage : 10,\r\n    paginationRowsPerPageOptions: rowsPerPage ? rowsPerPage : [10, 15, 20, 25, 30],\r\n    paginationTotalRows: totalRows,\r\n    onChangeRowsPerPage: handlePerRowsChange,\r\n    onChangePage: handlePageChange,\r\n    selectableRows,\r\n    onSelectedRowsChange,\r\n    clearSelectedRows,\r\n    progressComponent: <CustomLoader/>,\r\n    sortIcon: <i className=\"sort-icon fas fa-exchange-alt\" />,\r\n    sortServer,\r\n    onSort,\r\n    sortFunction,\r\n    persistTableHead,\r\n    className: \"rki-table\"\r\n  };\r\n  return (\r\n    <DataTable {...dataTableProps} />\r\n  )\r\n};\r\n\r\nRKITable.defaultProps = {\r\n  subHeader: false,\r\n  pagination: true,\r\n  totalRows: null,\r\n  pagServer: true,\r\n  onSelectedRowsChange: null,\r\n  clearSelectedRows: false,\r\n  sortServer: false,\r\n  persistTableHead: false\r\n}\r\n\r\nexport default RKITable;\r\n\r\n", "\n    (window.__NEXT_P = window.__NEXT_P || []).push([\n      \"/adminsettings/worldregion/worldregionTable\",\n      function () {\n        return require(\"private-next-pages/adminsettings/worldregion/worldregionTable.tsx\");\n      }\n    ]);\n    if(module.hot) {\n      module.hot.dispose(function () {\n        window.__NEXT_P.push([\"/adminsettings/worldregion/worldregionTable\"])\n      });\n    }\n  "], "names": ["_props", "t", "useTranslation", "WorldregionTable", "tabledata", "setDataToTable", "useState", "setLoading", "totalRows", "setTotalRows", "perPage", "setPerPage", "isModalShow", "setModal", "selectWorldregion", "setSelectWorldregion", "worldregionParams", "sort", "title", "limit", "page", "query", "columns", "name", "selector", "row", "sortable", "code", "cell", "d", "_id", "div", "Link", "href", "as", "i", "className", "a", "onClick", "userAction", "getWorldregionData", "response", "apiService", "get", "data", "length", "totalCount", "handlePerRowsChange", "newPerPage", "modalConfirm", "remove", "toast", "success", "error", "modalHide", "useEffect", "Modal", "show", "onHide", "Header", "closeButton", "Title", "Body", "Footer", "<PERSON><PERSON>", "variant", "RKITable", "pagServer", "handlePageChange", "props", "paginationComponentOptions", "rowsPerPageText", "resetPaginationToggle", "subheader", "subHeaderComponent", "rowsPerPage", "defaultRowsPerPage", "selectableRows", "loading", "onSelectedRowsChange", "clearSelectedRows", "sortServer", "onSort", "persistTableHead", "sortFunction", "otherProps", "dataTableProps", "noDataComponent", "<PERSON><PERSON><PERSON><PERSON>", "dense", "paginationResetDefaultPage", "subHeader", "progressPending", "pagination", "paginationServer", "paginationPerPage", "paginationRowsPerPageOptions", "paginationTotalRows", "onChangeRowsPerPage", "onChangePage", "progressComponent", "CustomLoader", "sortIcon", "DataTable", "defaultProps"], "sourceRoot": "", "ignoreList": []}