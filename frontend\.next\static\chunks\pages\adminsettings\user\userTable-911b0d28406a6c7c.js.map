{"version": 3, "file": "static/chunks/pages/adminsettings/user/userTable-911b0d28406a6c7c.js", "mappings": "oNA4DA,MAnDwB,OAAC,YAAEA,CAAU,QAmDtBC,EAnDwBC,CAAQ,YAmDjBD,EAAC,EAnDkBE,CAAc,SAAEC,CAAO,YAAEC,CAAU,CAAO,GACjF,GAAEC,CAAC,CAAE,CAAGC,CAAAA,EAAAA,EAAAA,EAAAA,CAAcA,CAAC,UACvB,CAACC,EAAMC,EAAQ,CAAGC,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAC,EAAE,EAC7B,EAAGC,EAAW,CAAGD,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,EAAC,GAE1BE,EAAa,CACfC,KAAM,CAAEC,WAAY,MAAO,EAC3BC,MAAO,IACPC,KAAM,EACNC,MAAO,CAAC,EACRC,OAAQ,+dACZ,EAEMC,EAAc,UAChBR,EAAW,IACX,IAAMS,EAAW,MAAMC,EAAAA,CAAUA,CAACC,GAAG,CAAC,SAAUV,GAC5CQ,GAAYG,MAAMC,OAAO,CAACJ,EAASK,IAAI,GAAG,CAI1ChB,EAHeW,EAASK,IAGhBC,CAHqBC,GAAG,CAAC,CAACC,EAAWC,KAClC,CAAEC,MAAOF,EAAKG,QAAQ,CAAEC,MAAOJ,EAAKK,GAAG,CAAC,IAInDtB,GAAW,GAEnB,EAMA,MAJAuB,CAAAA,EAAAA,EAAAA,SAAAA,CAASA,CAAC,KACNf,GACJ,EAAG,EAAE,EAGD,UAACgB,EAAAA,CAASA,CAAAA,CAACC,KAAK,IAACC,UAAU,eACvB,UAACC,EAAAA,CAAGA,CAAAA,UACA,UAACC,EAAAA,CAAGA,CAAAA,CAACC,GAAI,EAAGC,GAAI,EAAGJ,UAAU,eAEzB,UAACK,EAAAA,EAAMA,CAAAA,CACHC,UAAW,GACXC,aAAa,EACbC,cAAc,EACdC,UAAWzC,EACX0C,SAAU7C,EACV8C,YAAa1C,EAAE,2CACf2C,QAASzC,SAOjC,6GCrBA,SAAS0C,EAASC,CAAoB,EACpC,GAAM,CAAE7C,CAAC,CAAE,CAAGC,CAAAA,EAAAA,EAAAA,EAAAA,CAAcA,CAAC,UACvB6C,EAA6B,CACjCC,gBAAiB/C,EAAE,cACnB,EACI,SACJgD,CAAO,MACP7B,CAAI,WACJ8B,CAAS,uBACTC,CAAqB,WACrBC,CAAS,oBACTC,CAAkB,qBAClBC,CAAmB,kBACnBC,CAAgB,aAChBC,CAAW,oBACXC,CAAkB,gBAClBC,CAAc,SACdC,CAAO,WACPC,CAAS,sBACTC,CAAoB,mBACpBC,CAAiB,YACjBC,CAAU,QACVC,CAAM,kBACNC,CAAgB,cAChBC,CAAY,CAEZ,CADA,EACGC,EACJ,CAAGrB,EAGEsB,EAAiB,4BACrBrB,EACAsB,gBAAiBpE,EAAE,IAP0C,MAQ7DqE,UAAU,UACVrB,EACA7B,KAAMA,GAAQ,EAAE,CAChBmD,OAAO,EACPC,2BAA4BrB,EAC5BsB,UAAWrB,EACXsB,gBAAiBf,qBACjBN,EACAsB,WAAY,GACZC,iBAAkBhB,EAClBiB,kBAAmBpB,GAA0C,GAC7DqB,eADwCrB,cACVD,GAA4B,CAAC,GAAI,GAAI,GAAI,CAA3BA,EAA+B,GAAG,CAC9EuB,oBAAqB7B,EACrB8B,oBAAqB1B,EACrB2B,aAAc1B,iBACdG,uBACAG,oBACAC,EACAoB,kBAAmB,UAACC,EAAAA,CAAYA,CAAAA,CAAAA,GAChCC,SAAU,UAACC,IAAAA,CAAErD,UAAU,6CACvB+B,SACAC,eACAE,mBACAD,EACAjC,UAAW,WACb,EACA,MACE,UAACsD,EAAAA,EAASA,CAAAA,CAAE,GAAGlB,CAAc,EAEjC,CAEAvB,EAAS0C,YAAY,CAAG,CACtBd,WAAW,EACXE,WAAY,GACZzB,UAAW,KACXU,WAAW,EACXC,qBAAsB,KACtBC,mBAAmB,EACnBC,YAAY,EACZE,kBAAkB,CACpB,EAEA,MAAepB,QAAQA,EAAC,6KCnGT,SAAS2C,EAAU1C,CAAU,EACxC,GAAM,CAAE7C,CAAC,CAAE,CAAGC,CAAAA,EAAAA,EAAAA,EAAAA,CAAcA,CAAC,UACvB,CAACuF,EAAWC,EAAe,CAAGrF,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAC,EAAE,EACzC,CAACsD,EAASrD,EAAW,CAAGD,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,EAAC,GACjC,CAAC6C,EAAWyC,EAAa,CAAGtF,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAC,GACrC,CAACuF,EAASC,EAAW,CAAGxF,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAC,IACjC,CAACyF,EAAcC,EAAgB,CAAG1F,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAC,IAC3C,CAAC2F,EAAaC,EAAS,CAAG5F,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,EAAC,GACnC,CAAC6F,EAAYC,EAAc,CAAG9F,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAC,CAAC,GACxC,CAACV,EAAYyG,EAAc,CAAGC,EAAAA,QAAc,CAAC,IAC7C,CAAClD,EAAuBmD,EAAyB,CAAGD,EAAAA,QAAc,EAAC,GAC/D,CAACE,EAAcC,EAAgB,CAAGnG,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAM,CAAC,GAErDE,EAAa,CACfC,KAAM,CAAEC,WAAY,MAAO,EAC3BC,MAAOoF,EACPnF,KAAM,EACNC,MAAO,CAAC,CACZ,EAEIqC,EAAU,CACV,CACIwD,KAAMxG,EAAE,oCACRyG,SAAU,WACVC,KAAM,GAAYC,EAAElF,QAAQ,EAEhC,CACI+E,KAAMxG,EAAE,iCACRyG,SAAU,QACVC,KAAM,GAAYC,EAAEC,KAAK,EAE7B,CACIJ,KAAMxG,EAAE,gCACRyG,SAAU,OACVC,KAAM,GAAaC,EAAEE,KAAK,CAAGF,EAAEE,KAAK,CAAG,EAC3C,EACA,CACIL,KAAMxG,EAAE,wCACRyG,SAAU,cACVC,KAAM,GAAaC,EAAEG,WAAW,CAAGH,EAAEG,WAAW,CAACC,KAAK,CAAG,EAC7D,EACA,CACIP,KAAMxG,EAAE,kCACRyG,SAAU,GACVC,KAAM,GACF,+BACKC,EAAEK,MAAM,CACL,WAACC,MAAAA,WACG,UAACC,IAAIA,CAACC,KAAK,6BAA6BC,GAAI,OAAvCF,qBAAyE,OAANP,EAAEhF,GAAG,WAEzE,UAACyD,IAAAA,CAAErD,UAAU,uBAEV,OAEP,UAACsF,IAAAA,CAAEC,QAAS,IAAMC,EAAWZ,YACzB,UAACvB,IAAAA,CAAErD,UAAU,+BAIrB,IAKhB,EACH,CAEKlB,EAAc,MAAO2G,IACvBnH,EAAW,IACX,IAAMS,EAAW,MAAMC,EAAAA,CAAUA,CAACC,GAAG,CAAC,SAAUwG,GAChD,GAAI1G,GAAYG,MAAMC,OAAO,CAACJ,EAASK,IAAI,EAAG,KACrCmF,EAAD,QAACA,EAAAA,EAAa,OAAbA,GAAY,EAAZA,EAAAA,EAAuBmB,GAAvBnB,KAA+B,CAAC,gBAKjCxF,EALiD,IAKpC,CAACO,GAAG,CAAC,GAAYqG,EAAEV,MAAM,EAAG,IAJzClG,EAASK,IAAI,CAACwG,MAAM,CAAC,GAAYD,EAAEb,KAAK,CAACY,QAAQ,CAAC,gBAAgBpG,GAAG,CAAEqG,GAAWA,EAAEV,MAAM,EAAG,GAC7FlG,EAASK,IAAI,CAACwG,MAAM,CAAC,GAAY,CAACD,EAAEb,KAAK,CAACY,QAAQ,CAAC,gBAAgBpG,GAAG,CAAC,GAAYqG,EAAEV,MAAM,EAAG,IAKlGvB,EAAe3E,EAASK,IAAI,EAC5BuE,EAAa5E,EAAS8G,UAAU,EAChCvH,GAAW,GACXwH,EAAiB/G,EAASK,IAAI,CAClC,CACJ,EAQMkC,EAAsB,MAAOyE,EAAiBpH,KAChDJ,EAAWG,KAAK,CAAGqH,EACnBxH,EAAWI,IAAI,CAAGA,EAClBL,GAAW,GACX,IAAMS,EAAW,MAAMC,EAAAA,CAAUA,CAACC,GAAG,CAAC,SAAUV,GAChD,GAAIQ,GAAYG,MAAMC,OAAO,CAACJ,EAASK,IAAI,EAAG,KACrCmF,EAAD,QAACA,EAAAA,EAAa,OAAbA,GAAY,EAAZA,EAAAA,EAAuBmB,GAAvBnB,KAA+B,CAAC,gBAKjCxF,EALiD,IAKpC,CAACO,GAAG,CAAC,GAAYqG,EAAEV,MAAM,EAAG,IAJzClG,EAASK,IAAI,CAACwG,MAAM,CAAC,GAAYD,EAAEb,KAAK,CAACY,QAAQ,CAAC,gBAAgBpG,GAAG,CAAC,GAAYqG,EAAEV,MAAM,EAAG,GAC7FlG,EAASK,IAAI,CAACwG,MAAM,CAAC,GAAY,CAACD,EAAEb,KAAK,CAACY,QAAQ,CAAC,gBAAgBpG,GAAG,CAAC,GAAYqG,EAAEV,MAAM,EAAG,IAKlGvB,EAAe3E,EAASK,IAAI,EAC5B2E,EAAgBgC,GAChBzH,GAAW,EACf,CACJ,EAEAuB,CAAAA,EAAAA,EAAAA,SAAAA,CAASA,CAAC,KACNf,EAAYP,EAChB,EAAG,EAAE,EAEL,IAAMuH,EAAmB,MAAOE,IAC5B,IAAMC,EAAc,MAAMjH,EAAAA,CAAUA,CAACkH,IAAI,CAAC,uBAAwB,CAAC,GAC/DD,GAAeA,EAAYvG,QAAQ,EAAE,CACrC8E,EAAgByB,GAChBE,QAAQC,GAAG,CAACJ,GACPC,EAAYnB,KAAK,CAACY,QAAQ,CAAC,eAK5BM,CAL4C,CAKhC1G,GAAG,CAAC,GAAYqG,EAAEV,MAAM,EAAG,IAJvCe,EAAYJ,MAAM,CAAC,GAAYD,EAAEb,KAAK,CAACY,QAAQ,CAAC,gBAAgBpG,GAAG,CAAC,GAAYqG,EAAEV,MAAM,EAAG,GAC3Fe,EAAYJ,MAAM,CAAC,GAAY,CAACD,EAAEb,KAAK,CAACY,QAAQ,CAAC,gBAAgBpG,GAAG,CAAC,GAAYqG,EAAEV,MAAM,EAAG,IAKhGvB,EAAesC,GACfnC,EAAWmC,GAEnB,EACMR,EAAa,MAAOa,IACtBlC,EAAckC,EAAIzG,GAAG,EACrBqE,EAAS,GACb,EAEMqC,EAAe,UACjB,GAAI,CACA,MAAMtH,EAAAA,CAAUA,CAACuH,MAAM,CAAC,UAAqB,OAAXrC,IAClCpF,EAAYP,GACZ0F,GAAS,GACTuC,EAAAA,EAAKA,CAACC,OAAO,CAACxI,EAAE,mDACpB,CAAE,MAAOyI,EAAO,CACZF,EAAAA,EAAKA,CAACE,KAAK,CAACzI,EAAE,6CAClB,CACJ,EAEM0I,EAAY,IAAM1C,GAAS,GAE3B2C,EAAyBvC,EAAAA,OAAa,CAAC,KAQzC,IAAMwC,EAAoB,IAClBjI,IACmB,GADZ,IACuBkI,yBACfC,IAAI,CAACnI,EAAMoI,WAAW,IACjCzI,CADsC,CAC3BK,KAAK,CAAG,CAAEiG,MAAOjG,CAAM,EAElCL,EAAWK,KAAK,CAAG,CAAEc,SAAUd,CAAM,GAG7CE,EAAYP,GACZA,EAAWK,KAAK,CAAG,CAAC,CAMxB,EAaMqI,EAAW,KACbJ,EAAkBlJ,EACtB,EAEMuJ,EAAiB,KACnBD,GACJ,EAQA,MACI,UAACrJ,EAAAA,OAAeA,CAAAA,CACZC,SA3Ba,CA2BHsJ,GA1BVC,GAAKA,EAAE3H,KAAK,EAAE,EACA2H,EAAE3H,KAAK,EACrBoH,EAAkBO,EAAE3H,KAAK,IAEzBlB,EAAWK,KAAK,CAAG,CAAC,EACpBwF,EAAc,IACdtF,EAAYP,GAEpB,EAmBQR,QArDY,CAqDHsJ,IApDT1J,IACA2G,EAAyB,CAACnD,GAC1BiD,EAFY,IAIpB,EAiDQzG,WAAYA,EACZG,eAAgBmJ,EAChBjJ,WAZiB,CAYLsJ,GAXE,SAAS,CAAvBC,EAAMC,GAAG,EACTN,GAER,GAWJ,EAAG,CAACvJ,EAAW,EAEf,MACI,WAACuH,MAAAA,WACG,WAACuC,EAAAA,CAAKA,CAAAA,CAACC,KAAM1D,EAAa2D,OAAQhB,YAC9B,UAACc,EAAAA,CAAKA,CAACG,MAAM,EAACC,WAAW,aACrB,UAACJ,EAAAA,CAAKA,CAACK,KAAK,WAAE7J,EAAE,0CAEpB,UAACwJ,EAAAA,CAAKA,CAACM,IAAI,WAAE9J,EAAE,6DACf,WAACwJ,EAAAA,CAAKA,CAACO,MAAM,YACT,UAACC,EAAAA,CAAMA,CAAAA,CAACC,QAAQ,YAAY3C,QAASoB,WAChC1I,EAAE,oCAEP,UAACgK,EAAAA,CAAMA,CAAAA,CAACC,QAAQ,UAAU3C,QAASe,WAC9BrI,EAAE,uCAKf,UAAC4C,EAAAA,CAAQA,CAAAA,CACLI,QAASH,EAAMqH,IAAI,EAAmB,YAAfrH,EAAMqH,IAAI,CAAiBlH,EAAQmH,KAAK,CAAC,EAAG,CAAC,GAAKnH,EACzE7B,KAAMqE,EACNvC,UAAWA,EACXS,QAASA,EACTP,SAAS,IACTQ,WAAW,EACXT,sBAAuBA,EACvBE,mBAAoBuF,EACpBtF,oBAAqBA,EACrBC,iBAxJa,CAwJKA,GAvJ1BhD,EAAWG,KAAK,CAAGoF,EACnBvF,EAAWI,IAAI,CAAGA,EAClBG,EAAYP,EAChB,MAwJJ,mBC7PA,4CACA,gCACA,WACA,OAAe,EAAQ,KAAqD,CAC5E,EACA,SAFsB", "sources": ["webpack://_N_E/./pages/adminsettings/user/userTableFilter.tsx", "webpack://_N_E/./components/common/RKITable.tsx", "webpack://_N_E/./pages/adminsettings/user/userTable.tsx", "webpack://_N_E/?cf50"], "sourcesContent": ["//Import Library\r\nimport { <PERSON>, Con<PERSON><PERSON>, <PERSON>, <PERSON><PERSON> } from \"react-bootstrap\";\r\nimport Select from \"react-select\";\r\nimport { useEffect, useState } from \"react\";\r\n\r\n//Import services/components\r\nimport { useTranslation } from 'next-i18next';\r\nimport apiService from \"../../../services/apiService\";\r\n\r\nconst UserTableFilter = ({ filterText, onFilter, onHandleSearch, onClear, onKeyPress }: any) => {\r\n    const { t } = useTranslation('common');\r\n    const [user, setUser] = useState([]);\r\n    const [, setLoading] = useState(false);\r\n\r\n    const userParams = {\r\n        sort: { created_at: \"desc\" },\r\n        limit: \"~\",\r\n        page: 1,\r\n        query: {},\r\n        select: \"-country -dial_code -firstname -region -institution.address -institution.contact_name -institution.description -institution.dial_code -institution.document -institution.doc_src -institution.images -institution.images_src -institution.expertise -institution.hazards -institution.hazard_types -institution.header -institution.networks -institution.partners -institution.telephone -institution.twitter -institution.type -institution.unit -institution.user -mobile_number -position\",\r\n    };\r\n\r\n    const getUserData = async () => {\r\n        setLoading(true);\r\n        const response = await apiService.get(\"/users\", userParams);\r\n        if (response && Array.isArray(response.data)) {\r\n            const _users = response.data.map((item: any, _i: any) => {\r\n                return { label: item.username, value: item._id };\r\n            });\r\n            setUser(_users);\r\n\r\n            setLoading(false);\r\n        }\r\n    };\r\n\r\n    useEffect(() => {\r\n        getUserData();\r\n    }, []);\r\n\r\n    return (\r\n        <Container fluid className=\"p-0\">\r\n            <Row>\r\n                <Col xs={6} md={4} className=\"p-0\">\r\n\r\n                    <Select\r\n                        autoFocus={true}\r\n                        isClearable={true}\r\n                        isSearchable={true}\r\n                        onKeyDown={onKeyPress}\r\n                        onChange={onFilter}\r\n                        placeholder={t(\"adminsetting.user.table.Usernameoremail\")}\r\n                        options={user}\r\n                    />\r\n                </Col>\r\n\r\n            </Row>\r\n        </Container>\r\n    );\r\n};\r\n\r\nexport default UserTableFilter;\r\n", "//Import Library\r\nimport React from 'react'\r\nimport DataTable  from 'react-data-table-component';\r\n\r\n//Import services/components\r\nimport { useTranslation } from 'next-i18next';\r\nimport CustomLoader from './CustomLoader';\r\n\r\ninterface RKITableProps {\r\n  columns: any[];\r\n  data: any[];\r\n  totalRows?: number;\r\n  resetPaginationToggle?: boolean;\r\n  subheader?: boolean;\r\n  subHeaderComponent?: React.ReactNode;\r\n  handlePerRowsChange?: (newPerPage: number, page: number) => void;\r\n  handlePageChange?: (page: number) => void;\r\n  rowsPerPage?: number[];\r\n  defaultRowsPerPage?: number;\r\n  selectableRows?: boolean;\r\n  loading?: boolean;\r\n  pagServer?: boolean;\r\n  onSelectedRowsChange?: (selectedRows: any) => void;\r\n  clearSelectedRows?: boolean;\r\n  sortServer?: boolean;\r\n  onSort?: (column: any, sortDirection: string) => void;\r\n  persistTableHead?: boolean;\r\n  sortFunction?: (rows: any[], field: any, direction: 'asc' | 'desc') => any[];\r\n  noHeader?: boolean;\r\n  dense?: boolean;\r\n  pagination?: boolean;\r\n  paginationServer?: boolean;\r\n  paginationTotalRows?: number;\r\n  subHeaderAlign?: string;\r\n  [key: string]: any;\r\n}\r\n\r\nfunction RKITable(props: RKITableProps) {\r\n  const { t } = useTranslation('common');\r\n  const paginationComponentOptions = {\r\n    rowsPerPageText: t('Rowsperpage'),\r\n    };\r\n  const {\r\n    columns,\r\n    data,\r\n    totalRows,\r\n    resetPaginationToggle,\r\n    subheader,\r\n    subHeaderComponent,\r\n    handlePerRowsChange,\r\n    handlePageChange,\r\n    rowsPerPage,\r\n    defaultRowsPerPage,\r\n    selectableRows,\r\n    loading,\r\n    pagServer,\r\n    onSelectedRowsChange,\r\n    clearSelectedRows,\r\n    sortServer,\r\n    onSort,\r\n    persistTableHead,\r\n    sortFunction,\r\n    // Filter out any unknown props that might cause DOM warnings\r\n    ...otherProps\r\n  } = props;\r\n\r\n  // Only pass known DataTable props to avoid DOM warnings\r\n  const dataTableProps = {\r\n    paginationComponentOptions,\r\n    noDataComponent: t(\"NoData\"),\r\n    noHeader: true,\r\n    columns,\r\n    data: data || [],\r\n    dense: true,\r\n    paginationResetDefaultPage: resetPaginationToggle,\r\n    subHeader: subheader,\r\n    progressPending: loading,\r\n    subHeaderComponent,\r\n    pagination: true,\r\n    paginationServer: pagServer,\r\n    paginationPerPage: defaultRowsPerPage ? defaultRowsPerPage : 10,\r\n    paginationRowsPerPageOptions: rowsPerPage ? rowsPerPage : [10, 15, 20, 25, 30],\r\n    paginationTotalRows: totalRows,\r\n    onChangeRowsPerPage: handlePerRowsChange,\r\n    onChangePage: handlePageChange,\r\n    selectableRows,\r\n    onSelectedRowsChange,\r\n    clearSelectedRows,\r\n    progressComponent: <CustomLoader/>,\r\n    sortIcon: <i className=\"sort-icon fas fa-exchange-alt\" />,\r\n    sortServer,\r\n    onSort,\r\n    sortFunction,\r\n    persistTableHead,\r\n    className: \"rki-table\"\r\n  };\r\n  return (\r\n    <DataTable {...dataTableProps} />\r\n  )\r\n};\r\n\r\nRKITable.defaultProps = {\r\n  subHeader: false,\r\n  pagination: true,\r\n  totalRows: null,\r\n  pagServer: true,\r\n  onSelectedRowsChange: null,\r\n  clearSelectedRows: false,\r\n  sortServer: false,\r\n  persistTableHead: false\r\n}\r\n\r\nexport default RKITable;\r\n\r\n", "//Import Library\r\nimport Link from \"next/link\";\r\nimport React, { useEffect, useState } from \"react\";\r\nimport { <PERSON><PERSON>, But<PERSON> } from \"react-bootstrap\";\r\n\r\nimport toast from 'react-hot-toast';\r\n\r\n//Import services/components\r\nimport RKITable from \"../../../components/common/RKITable\";\r\nimport apiService from \"../../../services/apiService\";\r\nimport UserTableFilter from \"./userTableFilter\";\r\nimport { useTranslation } from 'next-i18next';\r\n\r\nexport default function UserTable(props: any) {\r\n    const { t } = useTranslation('common');\r\n    const [tabledata, setDataToTable] = useState([]);\r\n    const [loading, setLoading] = useState(false);\r\n    const [totalRows, setTotalRows] = useState(0);\r\n    const [perPage, setPerPage] = useState(10);\r\n    const [perPageCount, setPerPageCount] = useState(10);\r\n    const [isModalShow, setModal] = useState(false);\r\n    const [selectUser, setSelectUser] = useState({});\r\n    const [filterText, setFilterText] = React.useState(\"\");\r\n    const [resetPaginationToggle, setResetPaginationToggle] = React.useState(false);\r\n        const [loggedInUser, setloggedInUser] = useState<any>({});\r\n\r\n    const userParams = {\r\n        sort: { created_at: \"desc\" },\r\n        limit: perPageCount,\r\n        page: 1,\r\n        query: {},\r\n    };\r\n\r\n    let columns = [\r\n        {\r\n            name: t(\"adminsetting.user.table.Username\"),\r\n            selector: \"username\",\r\n            cell: (d: any) => d.username,\r\n        },\r\n        {\r\n            name: t(\"adminsetting.user.table.Email\"),\r\n            selector: \"email\",\r\n            cell: (d: any) => d.email,\r\n        },\r\n        {\r\n            name: t(\"adminsetting.user.table.Role\"),\r\n            selector: \"role\",\r\n            cell: (d: any) => (d.roles ? d.roles : \"\"),\r\n        },\r\n        {\r\n            name: t(\"adminsetting.user.table.Organisation\"),\r\n            selector: \"institution\",\r\n            cell: (d: any) => (d.institution ? d.institution.title : \"\"),\r\n        },\r\n        {\r\n            name: t(\"adminsetting.user.table.Action\"),\r\n            selector: \"\",\r\n            cell: (d: any) => (\r\n                <>\r\n                    {d.isEdit ?\r\n                        <div>\r\n                            <Link href=\"/adminsettings/[...routes]\" as={`/adminsettings/edit_user/${d._id}`}>\r\n\r\n                                <i className=\"icon fas fa-edit\" />\r\n\r\n                            </Link>\r\n                            &nbsp;\r\n                            <a onClick={() => userAction(d)}>\r\n                                <i className=\"icon fas fa-trash-alt\" />\r\n                            </a>\r\n                        </div>\r\n                    :\r\n                        \"\"\r\n                    }\r\n\r\n                </>\r\n            ),\r\n        },\r\n    ];\r\n\r\n    const getUserData = async (userParamsinit: any) => {\r\n        setLoading(true);\r\n        const response = await apiService.get(\"/users\", userParamsinit);\r\n        if (response && Array.isArray(response.data)) {\r\n            if (!loggedInUser['roles']?.includes('SUPER_ADMIN')) {\r\n                response.data.filter((x: any) => x.roles.includes('SUPER_ADMIN')).map((x: any) => x.isEdit = false);\r\n                response.data.filter((x: any) => !x.roles.includes('SUPER_ADMIN')).map((x: any) => x.isEdit = true);\r\n\r\n            } else {\r\n                response.data.map((x: any) => x.isEdit = true);\r\n            }\r\n            setDataToTable(response.data);\r\n            setTotalRows(response.totalCount);\r\n            setLoading(false);\r\n            loggedInUserData(response.data);\r\n        }\r\n    };\r\n\r\n    const handlePageChange = (page: any) => {\r\n        userParams.limit = perPageCount;\r\n        userParams.page = page;\r\n        getUserData(userParams);\r\n    };\r\n\r\n    const handlePerRowsChange = async (newPerPage: any, page: any) => {\r\n        userParams.limit = newPerPage;\r\n        userParams.page = page;\r\n        setLoading(true);\r\n        const response = await apiService.get(\"/users\", userParams);\r\n        if (response && Array.isArray(response.data)) {\r\n            if (!loggedInUser['roles']?.includes('SUPER_ADMIN')) {\r\n                response.data.filter((x: any) => x.roles.includes('SUPER_ADMIN')).map((x: any) => x.isEdit = false);\r\n                response.data.filter((x: any) => !x.roles.includes('SUPER_ADMIN')).map((x: any) => x.isEdit = true);\r\n\r\n            } else {\r\n                response.data.map((x: any) => x.isEdit = true);\r\n            }\r\n            setDataToTable(response.data);\r\n            setPerPageCount(newPerPage);\r\n            setLoading(false);\r\n        }\r\n    };\r\n\r\n    useEffect(() => {\r\n        getUserData(userParams);\r\n    }, []);\r\n\r\n    const loggedInUserData = async (allUserData: any) => {\r\n        const currentUser = await apiService.post(\"/users/getLoggedUser\", {});\r\n        if (currentUser && currentUser.username) {\r\n            setloggedInUser(currentUser);\r\n            console.log(allUserData);\r\n            if (!currentUser.roles.includes('SUPER_ADMIN')) {\r\n                allUserData.filter((x: any) => x.roles.includes('SUPER_ADMIN')).map((x: any) => x.isEdit = false);\r\n                allUserData.filter((x: any) => !x.roles.includes('SUPER_ADMIN')).map((x: any) => x.isEdit = true);\r\n\r\n            } else {\r\n                allUserData.map((x: any) => x.isEdit = true);\r\n            }\r\n            setDataToTable(allUserData);\r\n            setPerPage(allUserData);\r\n        }\r\n    };\r\n    const userAction = async (row: any) => {\r\n        setSelectUser(row._id);\r\n        setModal(true);\r\n    };\r\n\r\n    const modalConfirm = async () => {\r\n        try {\r\n            await apiService.remove(`/users/${selectUser}`);\r\n            getUserData(userParams);\r\n            setModal(false);\r\n            toast.success(t(\"adminsetting.user.table.userDeletedSuccessfully\"));\r\n        } catch (error) {\r\n            toast.error(t(\"adminsetting.user.table.errorDeletingUser\"));\r\n        }\r\n    };\r\n\r\n    const modalHide = () => setModal(false);\r\n\r\n    const subHeaderComponentMemo = React.useMemo(() => {\r\n        const handleClear = () => {\r\n            if (filterText) {\r\n                setResetPaginationToggle(!resetPaginationToggle);\r\n                setFilterText(\"\");\r\n            }\r\n        };\r\n\r\n        const handleSearchTitle = (query: any) => {\r\n            if (query) {\r\n                const emailRegex = new RegExp(Regexp());\r\n                if (emailRegex.test(query.toLowerCase())) {\r\n                    userParams.query = { email: query };\r\n                } else {\r\n                    userParams.query = { username: query };\r\n                }\r\n            }\r\n            getUserData(userParams);\r\n            userParams.query = {};\r\n            // setFilterText(\"\")\r\n\r\n            function Regexp(): string | RegExp {\r\n                return \"^[^@]+@[^@]+\\\\.[^@]+$\";\r\n            }\r\n        };\r\n\r\n        const handleChange = (e: any) => {\r\n            if (e && e.label) {\r\n                setFilterText(e.label);\r\n                handleSearchTitle(e.label);\r\n            } else {\r\n                userParams.query = {};\r\n                setFilterText(\"\");\r\n                getUserData(userParams);\r\n            }\r\n        };\r\n\r\n        const onSearch = () => {\r\n            handleSearchTitle(filterText);\r\n        };\r\n\r\n        const handleKeypress = () => {\r\n            onSearch();\r\n        };\r\n\r\n        const userdataKeypress = (event: any) => {\r\n            if (event.key === \"Enter\") {\r\n                handleKeypress();\r\n            }\r\n        };\r\n\r\n        return (\r\n            <UserTableFilter\r\n                onFilter={handleChange}\r\n                onClear={handleClear}\r\n                filterText={filterText}\r\n                onHandleSearch={onSearch}\r\n                onKeyPress={userdataKeypress}\r\n            />\r\n        );\r\n    }, [filterText]);\r\n\r\n    return (\r\n        <div>\r\n            <Modal show={isModalShow} onHide={modalHide}>\r\n                <Modal.Header closeButton>\r\n                    <Modal.Title>{t(\"adminsetting.user.table.DeleteUser\")}</Modal.Title>\r\n                </Modal.Header>\r\n                <Modal.Body>{t(\"adminsetting.user.table.Areyousurewanttodeletethisuser?\")}</Modal.Body>\r\n                <Modal.Footer>\r\n                    <Button variant=\"secondary\" onClick={modalHide}>\r\n                        {t(\"adminsetting.user.table.Cancel\")}\r\n                    </Button>\r\n                    <Button variant=\"primary\" onClick={modalConfirm}>\r\n                        {t(\"adminsetting.user.table.Yes\")}\r\n                    </Button>\r\n                </Modal.Footer>\r\n            </Modal>\r\n\r\n            <RKITable\r\n                columns={props.trim && props.trim === \"actions\" ? columns.slice(0, -1) : columns}\r\n                data={tabledata}\r\n                totalRows={totalRows}\r\n                loading={loading}\r\n                subheader\r\n                pagServer={true}\r\n                resetPaginationToggle={resetPaginationToggle}\r\n                subHeaderComponent={subHeaderComponentMemo}\r\n                handlePerRowsChange={handlePerRowsChange}\r\n                handlePageChange={handlePageChange}\r\n            />\r\n        </div>\r\n    );\r\n}\r\n", "\n    (window.__NEXT_P = window.__NEXT_P || []).push([\n      \"/adminsettings/user/userTable\",\n      function () {\n        return require(\"private-next-pages/adminsettings/user/userTable.tsx\");\n      }\n    ]);\n    if(module.hot) {\n      module.hot.dispose(function () {\n        window.__NEXT_P.push([\"/adminsettings/user/userTable\"])\n      });\n    }\n  "], "names": ["filterText", "UserTableFilter", "onFilter", "onHandleSearch", "onClear", "onKeyPress", "t", "useTranslation", "user", "setUser", "useState", "setLoading", "userParams", "sort", "created_at", "limit", "page", "query", "select", "getUserData", "response", "apiService", "get", "Array", "isArray", "data", "_users", "map", "item", "_i", "label", "username", "value", "_id", "useEffect", "Container", "fluid", "className", "Row", "Col", "xs", "md", "Select", "autoFocus", "isClearable", "isSearchable", "onKeyDown", "onChange", "placeholder", "options", "RKITable", "props", "paginationComponentOptions", "rowsPerPageText", "columns", "totalRows", "resetPaginationToggle", "subheader", "subHeaderComponent", "handlePerRowsChange", "handlePageChange", "rowsPerPage", "defaultRowsPerPage", "selectableRows", "loading", "pagServer", "onSelectedRowsChange", "clearSelectedRows", "sortServer", "onSort", "persistTableHead", "sortFunction", "otherProps", "dataTableProps", "noDataComponent", "<PERSON><PERSON><PERSON><PERSON>", "dense", "paginationResetDefaultPage", "subHeader", "progressPending", "pagination", "paginationServer", "paginationPerPage", "paginationRowsPerPageOptions", "paginationTotalRows", "onChangeRowsPerPage", "onChangePage", "progressComponent", "CustomLoader", "sortIcon", "i", "DataTable", "defaultProps", "UserTable", "tabledata", "setDataToTable", "setTotalRows", "perPage", "setPerPage", "perPageCount", "setPerPageCount", "isModalShow", "setModal", "selectUser", "setSelectUser", "setFilterText", "React", "setResetPaginationToggle", "loggedInUser", "setloggedInUser", "name", "selector", "cell", "d", "email", "roles", "institution", "title", "isEdit", "div", "Link", "href", "as", "a", "onClick", "userAction", "userParamsinit", "includes", "x", "filter", "totalCount", "loggedInUserData", "newPerPage", "allUserData", "currentUser", "post", "console", "log", "row", "modalConfirm", "remove", "toast", "success", "error", "modalHide", "subHeaderComponentMemo", "handleSearchTitle", "Regexp", "test", "toLowerCase", "onSearch", "handleKeypress", "handleChange", "e", "handleClear", "userdataKeypress", "event", "key", "Modal", "show", "onHide", "Header", "closeButton", "Title", "Body", "Footer", "<PERSON><PERSON>", "variant", "trim", "slice"], "sourceRoot": "", "ignoreList": []}