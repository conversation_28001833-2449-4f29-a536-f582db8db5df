{"version": 3, "file": "static/chunks/pages/country/components/CountryButtonSection-dd6fb290022343f2.js", "mappings": "gFACA,4CACA,2CACA,WACA,OAAe,EAAQ,KAAgE,CACvF,EACA,SAFsB,6HCwCtB,MAhC6B,IACzB,GAAM,GAAEA,CAAC,CAAE,CAAGC,CAAAA,EAAAA,EAAAA,EAAAA,CAAcA,CAAC,MA+BlBC,IA9BX,MACI,+BACI,WAACC,EAAAA,CAAGA,CAAAA,WACA,UAACC,EAAAA,CAAGA,CAAAA,UACCC,EAAMC,cAAc,CACjB,UAACC,IAAAA,CAAEC,KAAMH,EAAMC,cAAc,CAAEG,OAAO,kBAClC,UAACC,EAAAA,CAAMA,CAAAA,CAACC,UAAU,oBAAoBC,QAAQ,UAAUC,KAAK,cACxDb,EAAE,qBAIX,KAGR,UAACI,EAAAA,CAAGA,CAAAA,UACCC,EAAMS,eAAe,CAClB,UAACP,IAAAA,CAAEC,KAAMH,EAAMS,eAAe,CAAEL,OAAO,kBACnC,UAACC,EAAAA,CAAMA,CAAAA,CAACC,UAAU,oBAAoBC,QAAQ,UAAUC,KAAK,cACxDb,EAAE,sBAIX,SAMxB", "sources": ["webpack://_N_E/?3b8c", "webpack://_N_E/./pages/country/components/CountryButtonSection.tsx"], "sourcesContent": ["\n    (window.__NEXT_P = window.__NEXT_P || []).push([\n      \"/country/components/CountryButtonSection\",\n      function () {\n        return require(\"private-next-pages/country/components/CountryButtonSection.tsx\");\n      }\n    ]);\n    if(module.hot) {\n      module.hot.dispose(function () {\n        window.__NEXT_P.push([\"/country/components/CountryButtonSection\"])\n      });\n    }\n  ", "//Import Library\r\nimport React from \"react\";\r\nimport { Button, Col, Row } from \"react-bootstrap\";\r\n\r\n//Import services/components\r\nimport { useTranslation } from 'next-i18next';\r\n\r\ninterface CountryButtonSectionProps {\r\n  health_profile?: string;\r\n  security_advice?: string;\r\n}\r\n\r\nconst CountryButtonSection = (props: CountryButtonSectionProps) => {\r\n    const { t } = useTranslation('common');\r\n    return (\r\n        <>\r\n            <Row>\r\n                <Col>\r\n                    {props.health_profile ? (\r\n                        <a href={props.health_profile} target=\"_blank\">\r\n                            <Button className=\"countryBtn d-grid\" variant=\"primary\" size=\"lg\">\r\n                                {t(\"healthprofile\")}\r\n                            </Button>\r\n                        </a>\r\n                    ) : (\r\n                        \"\"\r\n                    )}\r\n                </Col>\r\n                <Col>\r\n                    {props.security_advice ? (\r\n                        <a href={props.security_advice} target=\"_blank\">\r\n                            <Button className=\"countryBtn d-grid\" variant=\"primary\" size=\"lg\">\r\n                                {t(\"securityadvice\")}\r\n                            </Button>\r\n                        </a>\r\n                    ) : (\r\n                        \"\"\r\n                    )}\r\n                </Col>\r\n            </Row>\r\n        </>\r\n    )\r\n}\r\n\r\nexport default CountryButtonSection;"], "names": ["t", "useTranslation", "CountryButtonSection", "Row", "Col", "props", "health_profile", "a", "href", "target", "<PERSON><PERSON>", "className", "variant", "size", "security_advice"], "sourceRoot": "", "ignoreList": []}