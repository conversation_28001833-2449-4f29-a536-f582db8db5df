"use strict";exports.id=1821,exports.ids=[1821],exports.modules={6417:(e,t,s)=>{s.d(t,{A:()=>i});let a=s(82015).createContext(null);a.displayName="CardHeaderContext";let i=a},15653:(e,t,s)=>{s.d(t,{ks:()=>r,s3:()=>n});var a=s(8732);s(82015);var i=s(59549),l=s(43294);let r=({name:e,id:t,required:s,validator:r,errorMessage:n,onChange:o,value:c,as:d,multiline:u,rows:m,pattern:p,...h})=>(0,a.jsx)(l.<PERSON>,{name:e,validate:e=>{let t="string"==typeof e?e:String(e||"");return s&&(!e||""===t.trim())?n?.validator||"This field is required":r&&!r(e)?n?.validator||"Invalid value":p&&e&&!new RegExp(p).test(e)?n?.pattern||"Invalid format":void 0},children:({field:e,meta:s})=>(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(i.A.Control,{...e,...h,id:t,as:d||"input",rows:m,isInvalid:s.touched&&!!s.error,onChange:t=>{e.onChange(t),o&&o(t)},value:void 0!==c?c:e.value}),s.touched&&s.error?(0,a.jsx)(i.A.Control.Feedback,{type:"invalid",children:s.error}):null]})}),n=({name:e,id:t,required:s,errorMessage:r,onChange:n,value:o,children:c,...d})=>(0,a.jsx)(l.Field,{name:e,validate:e=>{if(s&&(!e||""===e))return r?.validator||"This field is required"},children:({field:e,meta:s})=>(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(i.A.Control,{as:"select",...e,...d,id:t,isInvalid:s.touched&&!!s.error,onChange:t=>{e.onChange(t),n&&n(t)},value:void 0!==o?o:e.value,children:c}),s.touched&&s.error?(0,a.jsx)(i.A.Control.Feedback,{type:"invalid",children:s.error}):null]})})},18597:(e,t,s)=>{s.d(t,{A:()=>A});var a=s(3892),i=s.n(a),l=s(82015),r=s(80739),n=s(8732);let o=l.forwardRef(({className:e,bsPrefix:t,as:s="div",...a},l)=>(t=(0,r.oU)(t,"card-body"),(0,n.jsx)(s,{ref:l,className:i()(e,t),...a})));o.displayName="CardBody";let c=l.forwardRef(({className:e,bsPrefix:t,as:s="div",...a},l)=>(t=(0,r.oU)(t,"card-footer"),(0,n.jsx)(s,{ref:l,className:i()(e,t),...a})));c.displayName="CardFooter";var d=s(6417);let u=l.forwardRef(({bsPrefix:e,className:t,as:s="div",...a},o)=>{let c=(0,r.oU)(e,"card-header"),u=(0,l.useMemo)(()=>({cardHeaderBsPrefix:c}),[c]);return(0,n.jsx)(d.A.Provider,{value:u,children:(0,n.jsx)(s,{ref:o,...a,className:i()(t,c)})})});u.displayName="CardHeader";let m=l.forwardRef(({bsPrefix:e,className:t,variant:s,as:a="img",...l},o)=>{let c=(0,r.oU)(e,"card-img");return(0,n.jsx)(a,{ref:o,className:i()(s?`${c}-${s}`:c,t),...l})});m.displayName="CardImg";let p=l.forwardRef(({className:e,bsPrefix:t,as:s="div",...a},l)=>(t=(0,r.oU)(t,"card-img-overlay"),(0,n.jsx)(s,{ref:l,className:i()(e,t),...a})));p.displayName="CardImgOverlay";let h=l.forwardRef(({className:e,bsPrefix:t,as:s="a",...a},l)=>(t=(0,r.oU)(t,"card-link"),(0,n.jsx)(s,{ref:l,className:i()(e,t),...a})));h.displayName="CardLink";var x=s(7783);let g=(0,x.A)("h6"),v=l.forwardRef(({className:e,bsPrefix:t,as:s=g,...a},l)=>(t=(0,r.oU)(t,"card-subtitle"),(0,n.jsx)(s,{ref:l,className:i()(e,t),...a})));v.displayName="CardSubtitle";let y=l.forwardRef(({className:e,bsPrefix:t,as:s="p",...a},l)=>(t=(0,r.oU)(t,"card-text"),(0,n.jsx)(s,{ref:l,className:i()(e,t),...a})));y.displayName="CardText";let f=(0,x.A)("h5"),j=l.forwardRef(({className:e,bsPrefix:t,as:s=f,...a},l)=>(t=(0,r.oU)(t,"card-title"),(0,n.jsx)(s,{ref:l,className:i()(e,t),...a})));j.displayName="CardTitle";let b=l.forwardRef(({bsPrefix:e,className:t,bg:s,text:a,border:l,body:c=!1,children:d,as:u="div",...m},p)=>{let h=(0,r.oU)(e,"card");return(0,n.jsx)(u,{ref:p,...m,className:i()(t,h,s&&`bg-${s}`,a&&`text-${a}`,l&&`border-${l}`),children:c?(0,n.jsx)(o,{children:d}):d})});b.displayName="Card";let A=Object.assign(b,{Img:m,Title:j,Subtitle:v,Body:o,Link:h,Text:y,Header:u,Footer:c,ImgOverlay:p})},22053:(e,t,s)=>{s.a(e,async(e,a)=>{try{s.d(t,{A:()=>v});var i=s(8732),l=s(82015),r=s(49481),n=s(83551),o=s(59549),c=s(11e3),d=s(99800),u=s(59984),m=s(42738),p=s(63487),h=s(88751),x=e([d,u,m,p]);[d,u,m,p]=x.then?(await x)():x;let g=e=>{let t=(0,u.default)(),[s,a]=(0,l.useState)([]),[x,g]=(0,l.useState)(""),[v,y]=(0,l.useState)([]),[f,j]=(0,l.useState)([]),[b,A]=(0,l.useState)(!1),[C,S]=(0,l.useState)(!1),[w,N]=(0,l.useState)(""),{t:k,i18n:_}=(0,h.useTranslation)("common"),I="de"===_.language?{title_de:"asc"}:{title:"asc"},D=_.language;(0,l.useEffect)(()=>{(async e=>{let t=await p.A.get("/country",e);if(t&&Array.isArray(t.data)){let e=t.data.map((e,t)=>({label:e.title,value:e._id}));a(e)}let s=await p.A.get("/institutiontype",e);if(s&&Array.isArray(s.data)){let e=s.data.map((e,t)=>({label:e.title,value:e._id}));j(e)}})({query:{},sort:I,limit:"~",languageCode:D})},[]),(0,l.useEffect)(()=>{v&&e.nonMember&&e.nonMember(v)},[v]);let O=async e=>{if(e)if(/\S+@\S+\.\S+/.test(e)){let t=await p.A.post("/vspace/filterNonmember",{email:e});""===t.message?y([...v||[],{label:e,value:e}]):(N(e),S(!0),setTimeout(()=>{S(!1),N("")},1200)),g("")}else A(!0),g(""),setTimeout(()=>{A(!1)},1200)},T=async e=>{if(x)switch(e.key){case"Enter":case"Tab":await O(x),e.preventDefault()}},M=async()=>{x&&x.trim()&&await O(x.trim())},{invitesCountry:P,invitesRegion:E,invitesOrganisationType:L,invitesOrganisation:R,invitesExpertise:F,invitesNetWork:U,visibility:q,userList:G}=e,B="visibility-space";return(0,i.jsxs)("div",{children:[(0,i.jsx)(r.A,{className:"header-block",lg:12,children:(0,i.jsx)("h6",{children:(0,i.jsx)("span",{children:k("vspace.Admin")})})}),(0,i.jsxs)(n.A,{children:[(0,i.jsx)(r.A,{children:(0,i.jsxs)(o.A.Group,{children:[(0,i.jsx)(o.A.Label,{children:k("vspace.GroupVisibility")}),(0,i.jsx)(o.A.Check,{className:"check",checked:q,name:"visibility",onClick:e.handleVisibility,type:"radio",label:k("vspace.Public")})]})}),(0,i.jsx)(r.A,{style:{marginTop:"1.6rem"},children:(0,i.jsx)(o.A.Check,{type:"radio",checked:!q,name:"visibility",value:"off",onClick:e.handleVisibility,label:k("vspace.Private")})})]}),!q&&(0,i.jsxs)("div",{children:[(0,i.jsx)(r.A,{className:"header-block",lg:12,children:(0,i.jsx)("h6",{children:(0,i.jsx)("span",{children:k("vspace.Invites")})})}),(0,i.jsxs)(n.A,{children:[(0,i.jsx)(r.A,{md:4,sm:4,lg:4,children:(0,i.jsxs)(o.A.Group,{children:[(0,i.jsx)(o.A.Label,{children:k("CountryOrTerritory")}),(0,i.jsx)(c.MultiSelect,{overrideStrings:{selectSomeItems:k("SelectCountry")},options:s||[],onChange:t=>e.onChange(t,"invitesCountry"),value:P,className:B,labelledBy:k("SelectCountry")})]})}),(0,i.jsx)(r.A,{md:4,sm:4,lg:4,children:(0,i.jsxs)(o.A.Group,{children:[(0,i.jsx)(o.A.Label,{children:k("CountryRegions")}),(0,i.jsx)(c.MultiSelect,{overrideStrings:{selectSomeItems:k("SelectRegions")},options:e.multiRegionOptions||[],value:E,onChange:t=>e.onChange(t,"invitesRegion"),className:B,labelledBy:k("SelectRegions")})]})}),(0,i.jsx)(r.A,{md:4,sm:4,lg:4,children:(0,i.jsxs)(o.A.Group,{children:[(0,i.jsx)(o.A.Label,{children:k("OrganisationType")}),(0,i.jsx)(c.MultiSelect,{overrideStrings:{selectSomeItems:k("SelectOrganisationType")},options:f||[],onChange:t=>e.onChange(t,"invitesOrganisationType"),value:L,className:B,labelledBy:k("vspace.SelectOrganisationType")})]})})]}),(0,i.jsxs)(n.A,{children:[(0,i.jsx)(r.A,{md:4,sm:4,lg:4,children:(0,i.jsxs)(o.A.Group,{children:[(0,i.jsx)(o.A.Label,{children:k("Organisation")}),(0,i.jsx)(c.MultiSelect,{overrideStrings:{selectSomeItems:k("SelectOrganisation")},options:e.multiOrganisationOptions||[],onChange:t=>e.onChange(t,"invitesOrganisation"),value:R,className:B,labelledBy:k("SelectOrganisation")})]})}),(0,i.jsx)(r.A,{md:4,sm:4,lg:4,children:(0,i.jsxs)(o.A.Group,{style:{maxWidth:"450px"},children:[(0,i.jsx)(o.A.Label,{children:k("Expertise")}),(0,i.jsx)(c.MultiSelect,{overrideStrings:{selectSomeItems:k("SelectExpertise")},options:e.multiExpertsOptions||[],onChange:t=>e.onChange(t,"invitesExpertise"),value:F,className:"visibility-space",labelledBy:"Select Organisation Type"})]})}),(0,i.jsx)(r.A,{md:4,sm:4,lg:4,children:(0,i.jsxs)(o.A.Group,{children:[(0,i.jsx)(o.A.Label,{children:k("Network")}),(0,i.jsx)(c.MultiSelect,{overrideStrings:{selectSomeItems:k("SelectNetwork")},options:e.multiNetworkOptions||[],onChange:t=>e.onChange(t,"invitesNetWork"),value:U,className:"visibility-space",labelledBy:"Select Organisation Type"})]})})]}),(0,i.jsx)(r.A,{className:"header-block",lg:12,children:(0,i.jsx)("h6",{children:(0,i.jsx)("span",{children:k("vspace.PlatformMemberInvites")})})}),(0,i.jsx)(n.A,{children:(0,i.jsx)(r.A,{md:12,lg:12,sm:12,children:(0,i.jsx)(d.default,{closeMenuOnSelect:!1,components:t,isMulti:!0,value:G||[],placeholder:k("SelectUsers"),onChange:t=>{let s=e.allOption?.value||"*";return t&&t.length>0&&t[t.length-1]?.value===s?e.onChange(e.multiUserOptions||[],"userList"):e.onChange(t||[],"userList")},options:[e.allOption||{label:"All users",value:"*"},...e.multiUserOptions||[]]})})}),(0,i.jsx)(r.A,{className:"header-block",lg:12,children:(0,i.jsx)("h6",{className:"mb-1",children:(0,i.jsx)("span",{children:k("vspace.NonPlatform")})})}),(0,i.jsx)(n.A,{children:(0,i.jsxs)(r.A,{md:12,lg:12,sm:12,children:[(0,i.jsx)("small",{children:k("vspace.PressTab")}),(0,i.jsx)(m.default,{components:t,inputValue:x||"",isClearable:!0,isMulti:!0,menuIsOpen:!1,onChange:e=>y(e&&Array.isArray(e)?e:[]),onInputChange:e=>g(e||""),onKeyDown:T,onBlur:M,placeholder:k("vspace.Typeemail"),value:v||[]}),b&&(0,i.jsx)("small",{className:"text-danger",children:k("PleaseenterValidEmailid")}),C&&(0,i.jsxs)("small",{className:"text-danger",children:[" ",w,"  ",k("isalreadyexist")]})]})})]})]})};g.defaultProps={allOption:{label:"All users",value:"*"}};let v=g;a()}catch(e){a(e)}})},23579:(e,t,s)=>{s.d(t,{sx:()=>d,s3:()=>i.s3,ks:()=>i.ks,yk:()=>a.A});var a=s(66994),i=s(15653),l=s(8732),r=s(82015),n=s.n(r),o=s(43294),c=s(59549);let d={RadioGroup:({name:e,valueSelected:t,onChange:s,errorMessage:a,children:i})=>{let{errors:r,touched:c}=(0,o.useFormikContext)(),d=c[e]&&r[e];n().useMemo(()=>({name:e}),[e]);let u=n().Children.map(i,t=>n().isValidElement(t)&&function(e){return"object"==typeof e&&null!==e}(t.props)?n().cloneElement(t,{name:e,...t.props}):t);return(0,l.jsxs)("div",{children:[(0,l.jsx)("div",{className:"radio-group",children:u}),d&&(0,l.jsx)("div",{className:"invalid-feedback d-block",children:a||("string"==typeof r[e]?r[e]:String(r[e]))})]})},RadioItem:({id:e,label:t,value:s,name:a,disabled:i})=>{let{values:r,setFieldValue:n}=(0,o.useFormikContext)(),d=a||e;return(0,l.jsx)(c.A.Check,{type:"radio",id:e,label:t,value:s,name:d,checked:r[d]===s,onChange:e=>{n(d,e.target.value)},disabled:i,inline:!0})}};a.A,i.ks,i.s3},51821:(e,t,s)=>{s.a(e,async(e,a)=>{try{s.r(t),s.d(t,{default:()=>I});var i=s(8732),l=s(82015),r=s.n(l),n=s(7082),o=s(18597),c=s(83551),d=s(49481),u=s(59549),m=s(91353),p=s(44233),h=s.n(p),x=s(19918),g=s.n(x),v=s(74716),y=s.n(v),f=s(42893),j=s(23579),b=s(63487),A=s(58070),C=s(88751),S=s(98178),w=s(22053),N=s(24047),k=e([f,b,S,w]);[f,b,S,w]=k.then?(await k)():k;let _={title:"",description:"",startDate:null,endDate:null,searchData:"",visibility:!0,images:[],checked:!1,file_category:"",nonMembers:[],images_src:[],members:[],doc_src:[],document:[]},I=e=>{let t=(0,l.useRef)(null),s=(0,p.useRouter)(),{t:a}=(0,C.useTranslation)("common"),[x,v]=(0,l.useState)([]),[k,I]=(0,l.useState)([]),[D,O]=(0,l.useState)([]),[T,M]=(0,l.useState)([]),[P,E]=(0,l.useState)(_),[L,R]=(0,l.useState)([]),[F,U]=(0,l.useState)([]),[q,G]=(0,l.useState)([]),[B,V]=(0,l.useState)([]),[$,z]=(0,l.useState)([]),[,W]=(0,l.useState)(!1),[H,K]=(0,l.useState)(null),[J,Q]=(0,l.useState)(null),[X,Y]=(0,l.useState)({invitesCountry:[],invitesRegion:[],invitesOrganisationType:[],invitesOrganisation:[],invitesExpertise:[],invitesNetWork:[],userList:[]}),Z=(0,l.useRef)(null),ee=e=>{E(t=>({...t,description:e}))},et=(e,t)=>{E(s=>({...s,[t]:e}))},es=async s=>{let i,l;if(t.current&&t.current.setAttribute("disabled","disabled"),s.preventDefault(),!P.title||P.title.length<5){f.default.error(a("minimum5CharsReq")),t.current&&t.current.removeAttribute("disabled");return}W(!0);let r=X.userList.length>0?X.userList.map(e=>e.value):[],n={title:P.title,description:P.description,start_date:P.startDate,end_date:P.endDate,visibility:!0===P.visibility,images:P.images,images_src:P.images_src,members:r,nonMembers:P.nonMembers&&P.nonMembers.length>0?P.nonMembers.map(e=>e.value):"",document:P.document,doc_src:P.doc_src};"Operation"===J?n.operation=H:n.project=H;try{e.routes&&"edit"===e.routes[0]&&e.routes[1]?(l="vspace.virtualspaceupdatedsuccessfully",i=await b.A.patch(`/vspace/${e.routes[1]}`,n)):(l="vspace.virtualspaceaddedsuccessfully",i=await b.A.post("/vspace",n)),i&&i._id?(f.default.success(a(l)),h().push("/vspace/[...routes]",`/vspace/show/${i._id}`)):(f.default.error(a("An error occurred while saving the virtual space")),t.current&&t.current.removeAttribute("disabled"))}catch(e){console.error("Error saving virtual space:",e),f.default.error(a("An error occurred while saving the virtual space")),t.current&&t.current.removeAttribute("disabled")}},ea=e=>{e&&e.images?v(e.images):v([]),e&&e.images_src?I(e.images_src):I([]),e&&e.document?M(e.document):M([]),e&&e.doc_src?O(e.doc_src):O([])},ei=async()=>{let t=await b.A.post("/users/getLoggedUser",{}),s=await b.A.get(`/vspace/${e.routes[1]}`),a={title:s.title,description:s.description,startDate:s.start_date?y()(s.start_date).toDate():null,endDate:s.end_date?y()(s.end_date).toDate():null,file_category:s.file_category?s.file_category:null,visibility:!!s.visibility,user:s.user?s.user._id:"",images:s.images,images_src:s.images_src,nonMembers:""!==s.nonMembers[0]?s.nonMembers.map(e=>({label:e,value:e})):[]};if(s.members.length>0){let e=[];s.members.forEach(t=>{e.push({label:t.username,value:t._id})}),Y({...X,userList:e})}return ea(s),s.user&&s.user._id!==t._id&&h().push("/vspace"),E(a),s.end_date?E(e=>({...e,checked:!0})):null};(0,l.useEffect)(()=>{e.routes&&"edit"===e.routes[0]&&e.routes[1]&&ei(),K(s&&s.query&&s.query.id?s.query.id:null),Q(s&&s.query&&s.query.source?s.query.source:null)},[]),(0,l.useEffect)(()=>{let e={query:{},sort:{username:"asc"},limit:"~",select:"-acceptCookiesPolicy -country -created_at -dataConsentPolicy -dial_code -enabled -firstname -image -institution -is_focal_point -password -position -region -restrictedUsePolicy -withdrawConsentPolicy -medicalConsentPolicy -fullDataProtectionConsentPolicy -email -roles -updated_at -emailActivateToken -lastname -mobile_number "};(async()=>{let t=await b.A.get("/users",e);if(t?.data?.length&&(t.data=t.data.filter(e=>"Request Pending"!==e.vspace_status&&"Request Pending"!==e.status)),t){let e=t.data.map((e,t)=>({label:e.username,value:e._id}));R(e)}})()},[X]),r().useEffect(()=>{if(X){let e={};Object.keys(X).forEach((t,s)=>{let a=X[t].length>0&&X[t].map(e=>e.value);e[t]=a||[]}),eo(e)}else console.log("No threshold reached.")},[X]);let el=e=>{if(e&&Array.isArray(e.data)){let t=e.data.map(e=>({label:e.title,value:e._id}));G(t)}},er=e=>{if(e&&Array.isArray(e.data)){let t=e.data.map((e,t)=>({label:e.title,value:e._id}));z(t)}},en=e=>{if(e&&Array.isArray(e.data)){let t=e.data.map((e,t)=>({label:e.title,value:e._id}));V(t)}},eo=async e=>{let{invitesCountry:t,invitesRegion:s,invitesOrganisationType:a,invitesOrganisation:i,invitesExpertise:l,invitesNetWork:r}=e,n=[],o=[],c=[],d=[],u=await b.A.post("vspace/filterUser",{query:{country:t,country_region:s,institution_type:a,institution:i,expertises:l,networks:r,type:"public"}});if(u&&Array.isArray(u)){if(u[0].regions&&u[0].regions.length>0&&(n=u[0].regions.map((e,t)=>({label:e.title,value:e._id})),U(n)),u[1].organisation&&u[1].organisation.length>0)o=u[1].organisation.map((e,t)=>(d=e.networks.map(e=>({label:e.title,value:e._id})),c=e.expertise.map(e=>({label:e.title,value:e._id})),{label:e.title,value:e._id})),G(o),V(d),z(c);else if(0===u[1].organisation.length){let e={query:{},sort:{title:"asc"},limit:"~"},t=await b.A.get("/institution",e);el(t);let s=await b.A.get("/expertise",e);er(s);let a=await b.A.get("/institutionnetwork",e);en(a)}if(u[2].usersList&&u[2].usersList.length>0){let e=u[2].usersList.map((e,t)=>({label:e.username,value:e._id}));R(e)}}},ec=e=>{let t=[],s=[];e.length>0&&e.map(e=>{e.type&&(e.type.includes("pdf")||e.type.includes("docx")||e.type.includes("xlsx")||e.type.includes("xls"))?s.push(e.serverID):t.push(e.serverID)}),E(e=>({...e,images:t})),E(e=>({...e,document:s}))},ed=e=>{E(t=>({...t,images_src:e}))},eu=e=>{E(t=>({...t,doc_src:e}))},em=e=>{let{name:t,value:s}=e.target;E(e=>({...e,[t]:s}))};return(0,i.jsx)(n.A,{className:"formCard",fluid:!0,children:(0,i.jsx)(o.A,{children:(0,i.jsx)(j.yk,{onSubmit:es,ref:Z,onKeyPress:e=>{"Enter"===e.key&&e.preventDefault()},children:(0,i.jsxs)(o.A.Body,{children:[(0,i.jsx)(c.A,{children:(0,i.jsx)(d.A,{children:(0,i.jsx)(o.A.Title,{children:"edit"===e.routes[0]?a("vspace.editVirtualSpace"):a("vspace.addVirtualSpace")})})}),(0,i.jsx)("hr",{}),(0,i.jsx)(c.A,{className:"mb-3",children:(0,i.jsx)(d.A,{children:(0,i.jsxs)(u.A.Group,{children:[(0,i.jsx)(u.A.Label,{className:"required-field",children:a("vspace.title")}),(0,i.jsx)(u.A.Control,{minLength:5,required:!0,type:"text",name:"title",value:P.title,onChange:em}),(0,i.jsx)(u.A.Control.Feedback,{type:"invalid",children:0===P.title.length?a("Pleaseprovideatitle"):a("minimum5CharsReq")})]})})}),(0,i.jsx)(c.A,{className:"mb-3",children:(0,i.jsx)(d.A,{children:(0,i.jsxs)(u.A.Group,{children:[(0,i.jsx)(u.A.Label,{children:a("vspace.Body")}),(0,i.jsx)(N.x,{initContent:P.description,onChange:e=>ee(e)})]})})}),(0,i.jsx)(c.A,{className:"mb-3",children:(0,i.jsx)(d.A,{lg:12,children:(0,i.jsxs)(u.A.Group,{children:[(0,i.jsx)(u.A.Label,{children:a("vspace.Image")}),(0,i.jsx)(S.A,{datas:x,srcText:k,getImgID:e=>ec(e),getImageSource:e=>ed(e)})]})})}),(0,i.jsx)(c.A,{className:"mb-3",children:(0,i.jsx)(d.A,{lg:12,children:(0,i.jsxs)(u.A.Group,{children:[(0,i.jsx)(u.A.Label,{children:a("vspace.Documents")}),(0,i.jsx)(S.A,{type:"application",datas:T,srcText:D,getImgID:e=>ec(e),getImageSource:e=>eu(e)})]})})}),(0,i.jsxs)(c.A,{className:"mb-3",children:[(0,i.jsx)(d.A,{md:!0,lg:3,sm:12,children:(0,i.jsxs)(u.A.Group,{children:[(0,i.jsx)(u.A.Label,{className:"d-block",children:a("vspace.StartDate")}),(0,i.jsx)(A.A,{selected:P.startDate,onChange:e=>et(e,"startDate"),dateFormat:"MMMM d, yyyy",placeholderText:a("vspace.Selectadate")})]})}),(0,i.jsx)(d.A,{md:!0,lg:2,sm:12,className:"col-md",children:(0,i.jsx)(u.A.Check,{type:"checkbox",checked:P.checked,onChange:()=>{E(e=>({...e,checked:!e.checked}))},label:a("vspace.ShowEndDate")})}),P.checked&&(0,i.jsx)(d.A,{md:!0,lg:3,sm:12,children:(0,i.jsxs)(u.A.Group,{children:[(0,i.jsx)(u.A.Label,{className:"d-block",children:a("vspace.EndDate")}),(0,i.jsx)(A.A,{selected:P.endDate,minDate:P.startDate,onChange:e=>et(e,"endDate"),dateFormat:"MMMM d, yyyy",placeholderText:a("vspace.Selectadate")})]})})]}),(0,i.jsx)(w.A,{...X,...P,allOption:{label:"All users",value:"*"},multiUserOptions:L,multiRegionOptions:F,multiOrganisationOptions:q,multiExpertsOptions:$,multiNetworkOptions:B,onChange:(e,t)=>{Y(s=>({...s,[t]:null==e?[]:e}))},handleVisibility:()=>{E(e=>({...e,visibility:!e.visibility}))},onHandleChange:em,nonMember:e=>{E(t=>({...t,nonMembers:e}))}}),(0,i.jsx)(c.A,{className:"my-4",children:(0,i.jsxs)(d.A,{children:[(0,i.jsx)(m.A,{className:"me-2",type:"submit",variant:"primary",ref:t,children:a("submit")}),(0,i.jsx)(m.A,{className:"me-2",onClick:()=>{E(_),v([]),I([]),M([]),O([]),Y({invitesCountry:[],invitesRegion:[],invitesOrganisationType:[],invitesOrganisation:[],invitesExpertise:[],invitesNetWork:[],userList:[]}),W(!1),window.scrollTo(0,0)},variant:"info",children:a("reset")}),(0,i.jsx)(g(),{href:"/vspace",as:"/vspace",children:(0,i.jsx)(m.A,{variant:"secondary",children:a("Cancel")})})]})})]})})})})};a()}catch(e){a(e)}})},58070:(e,t,s)=>{s.d(t,{A:()=>r});var a=s(8732);s(82015);var i=s(29780),l=s.n(i);let r=e=>(0,a.jsx)(l(),{...e})},66994:(e,t,s)=>{s.d(t,{A:()=>o});var a=s(8732),i=s(82015),l=s(43294),r=s(18622);let n=(0,i.forwardRef)((e,t)=>{let{children:s,onSubmit:i,autoComplete:n,className:o,onKeyPress:c,initialValues:d,...u}=e,m=r.object().shape({});return(0,a.jsx)(l.Formik,{initialValues:d||{},validationSchema:m,onSubmit:(e,t)=>{let s={preventDefault:()=>{},stopPropagation:()=>{},currentTarget:null,target:null,nativeEvent:new Event("submit"),bubbles:!1,cancelable:!0,defaultPrevented:!1,eventPhase:0,isTrusted:!1,timeStamp:Date.now(),type:"submit",isDefaultPrevented:()=>!1,isPropagationStopped:()=>!1,persist:()=>{}};i&&i(s,e,t)},...u,children:e=>(0,a.jsx)(l.Form,{ref:t,onSubmit:e.handleSubmit,autoComplete:n,className:o,onKeyPress:c,children:"function"==typeof s?s(e):s})})});n.displayName="ValidationFormWrapper";let o=n},98178:(e,t,s)=>{s.a(e,async(e,a)=>{try{s.d(t,{A:()=>S});var i=s(8732),l=s(82015),r=s(16029),n=s(82053),o=s(54131),c=s(49481),d=s(59549),u=s(91353),m=s(12403),p=s(27825),h=s.n(p),x=s(42893),g=s(63487),v=s(88751),y=e([o,x,g]);[o,x,g]=y.then?(await y)():y;let f=[],j={flex:1,display:"flex",flexDirection:"column",alignItems:"center",justifyContent:"center",width:"100%",height:"100%",borderWidth:.1,borderColor:"#fafafa",backgroundColor:"#fafafa",color:"black",transition:"border  .24s ease-in-out",padding:"15px"},b={display:"flex",padding:"10px",width:"100%",border:"2px solid gray",flexDirection:"column",justifyContent:"flex-start",flexWrap:"wrap",marginTop:20},A={width:"150px"},C={borderColor:"#2196f3"},S=e=>{let t,{t:s}=(0,v.useTranslation)("common"),[a,p]=(0,l.useState)(!1),[y,S]=(0,l.useState)(),w="application"==e.type?0x1400000:"20971520",[N,k]=(0,l.useState)([]),[_,I]=(0,l.useState)(!0),[D,O]=(0,l.useState)([]),T=e&&"application"===e.type?"/files":"/image",M=async e=>{await g.A.remove(`${T}/${e}`)},P=e=>{S(e),p(!0)},E=(e,t)=>{let s=[...D];s[t]=e.target.value,O(s)},L=t=>{switch(t&&t.name.split(".").pop()){case"JPG":case"jpg":case"jpeg":case"jpg":case"png":return(0,i.jsx)("img",{src:t.preview,style:A});case"pdf":return(0,i.jsx)("img",{src:"/images/fileIcons/pdfFile.png",className:"application"===e.type?"docPreview":"imgPreview"});case"docx":default:return(0,i.jsx)("img",{src:"/images/fileIcons/wordFile.png",className:"application"===e.type?"docPreview":"imgPreview"});case"xls":case"xlsx":return(0,i.jsx)("img",{src:"/images/fileIcons/xlsFile.png",className:"application"===e.type?"docPreview":"imgPreview"})}},R=()=>p(!1),F=()=>{p(!1)},U=t=>{let s=(t=y)&&t._id?{serverID:t._id}:{file:t},a=h().findIndex(f,s),i=[...D];i.splice(a,1),O(i),M(f[a].serverID),f.splice(a,1),e.getImgID(f,e.index?e.index:0);let l=[...N];l.splice(l.indexOf(t),1),k(l),p(!1)},q=N.map((t,l)=>(0,i.jsxs)("div",{children:[(0,i.jsx)(c.A,{xs:12,children:(0,i.jsxs)("div",{className:"row",children:[(0,i.jsx)(c.A,{md:4,lg:3,className:"application text-center align-self-center"===e.type?"docImagePreview text-center align-self-center":"imgPreview text-center align-self-center",children:L(t)}),(0,i.jsx)(c.A,{md:5,lg:7,className:"align-self-center",children:(0,i.jsxs)(d.A,{children:[(0,i.jsxs)(d.A.Group,{controlId:"filename",children:[(0,i.jsx)(d.A.Label,{className:"mt-2",children:s("FileName")}),(0,i.jsx)(d.A.Control,{size:"sm",type:"text",disabled:!0,value:t.original_name?t.original_name:t.name})]}),(0,i.jsxs)(d.A.Group,{controlId:"description",children:[(0,i.jsx)(d.A.Label,{children:"application"===e.type?s("ShortDescription/(Max255Characters)"):s("Source/Description")}),(0,i.jsx)(d.A.Control,{maxLength:"application"===e.type?255:void 0,size:"sm",type:"text",placeholder:"application"===e.type?s("`Enteryourdocumentdescription`"):s("`Enteryourimagesource/description`"),value:D[l],onChange:e=>E(e,l)})]})]})}),(0,i.jsx)(c.A,{md:3,lg:2,className:"align-self-center text-center",onClick:()=>P(t),children:(0,i.jsx)(u.A,{variant:"dark",children:s("Remove")})})]})}),(0,i.jsxs)(m.A,{show:a,onHide:R,children:[(0,i.jsx)(m.A.Header,{closeButton:!0,children:(0,i.jsx)(m.A.Title,{children:s("DeleteFile")})}),(0,i.jsx)(m.A.Body,{children:s("Areyousurewanttodeletethisfile?")}),(0,i.jsxs)(m.A.Footer,{children:[(0,i.jsx)(u.A,{variant:"secondary",onClick:F,children:s("Cancel")}),(0,i.jsx)(u.A,{variant:"primary",onClick:()=>U(t),children:s("yes")})]})]})]},l));(0,l.useEffect)(()=>{N.forEach(e=>URL.revokeObjectURL(e.preview)),f=[]},[]),(0,l.useEffect)(()=>{e.getImageSource(D)},[D]),(0,l.useEffect)(()=>{O(e.srcText)},[e.srcText]),(0,l.useEffect)(()=>{if(e&&"true"===e.singleUpload&&I(!1),e&&e.datas){let t=e.datas.map((t,s)=>(f.push({serverID:t._id,index:e.index?e.index:0,type:t.name.split(".")[1]}),{...t,preview:`http://localhost:3001/api/v1/image/show/${t._id}`}));k([...t])}},[e.datas]);let G=async(t,s)=>{if(t.length>s)try{let a=new FormData;a.append("file",t[s]);let i=await g.A.post(T,a,{"Content-Type":"multipart/form-data"});f.push({serverID:i._id,file:t[s],index:e.index?e.index:0,type:t[s].name.split(".")[1]}),G(t,s+1)}catch(e){G(t,s+1)}else e.getImgID(f,e.index?e.index:0)},B=(0,l.useCallback)(async e=>{await G(e,0);let t=e.map(e=>Object.assign(e,{preview:URL.createObjectURL(e)}));_?k(e=>[...e,...t]):k([...t])},[]),{getRootProps:V,getInputProps:$,isDragActive:z,isDragAccept:W,isDragReject:H,fileRejections:K}=(0,r.useDropzone)({accept:e&&e.type?"application/pdf, application/vnd.openxmlformats-officedocument.wordprocessingml.document, application/vnd.oasis.opendocument.text,application/vnd.openxmlformats-officedocument.spreadsheetml.sheet,text/csv":"image/*",multiple:_,minSize:0,maxSize:w,onDrop:B,validator:function(e){if("/image"===T){if("image"!==e.type.substring(0,5))return x.default.error(s("toast.filetypenotsupport")),{code:"file-invalid-type",message:"File type not supported"}}else if("/files"===T&&"image"===e.type.substring(0,5))return x.default.error(s("toast.filetypenotsupport")),{code:"file-invalid-type",message:"File type not supported"};return null}}),J=(0,l.useMemo)(()=>({...j,...z?C:{outline:"2px dashed #bbb"},...W?{outline:"2px dashed #595959"}:{outline:"2px dashed #bbb"},...H?{outline:"2px dashed red"}:{activeStyle:C}}),[z,H]);t=e&&"application"===e.type?(0,i.jsx)("small",{style:{color:"#595959"},children:s("DocumentWeSupport")}):(0,i.jsx)("small",{style:{color:"#595959"},children:s("ImageWeSupport")});let Q=K.length>0&&K[0].file.size>w;return(0,i.jsxs)(i.Fragment,{children:[(0,i.jsx)("div",{className:" d-flex justify-content-center align-items-center mt-3",style:{width:"100%",height:"180px"},children:(0,i.jsxs)("div",{...V({style:J}),children:[(0,i.jsx)("input",{...$()}),(0,i.jsx)(n.FontAwesomeIcon,{icon:o.faCloudUploadAlt,size:"4x",color:"#999"}),(0,i.jsx)("p",{style:{color:"#595959",marginBottom:"0px"},children:s("Drag'n'dropsomefileshere,orclicktoselectfiles")}),!_&&(0,i.jsxs)("small",{style:{color:"#595959"},children:[(0,i.jsx)("b",{children:"Note:"})," One single image will be accepted"]}),t,(e.type,Q&&(0,i.jsxs)("small",{className:"text-danger mt-2",children:[(0,i.jsx)(n.FontAwesomeIcon,{icon:o.faExclamationCircle,size:"1x",color:"red"})," ",s("FileistoolargeItshouldbelessthan20MB")]})),H&&(0,i.jsxs)("small",{className:"text-danger",style:{color:"#595959"},children:[(0,i.jsx)(n.FontAwesomeIcon,{icon:o.faExclamationCircle,size:"1x",color:"red"})," ",s("Filetypenotacceptedsorr")]})]})}),N.length>0&&(0,i.jsx)("div",{style:b,children:q})]})};a()}catch(e){a(e)}})}};