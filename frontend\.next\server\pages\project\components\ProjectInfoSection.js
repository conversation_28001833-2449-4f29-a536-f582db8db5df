"use strict";(()=>{var e={};e.id=6853,e.ids=[636,3220,6853],e.modules={123:e=>{e.exports=require("dom-helpers/removeEventListener")},1332:e=>{e.exports=require("react-custom-scrollbars-2")},1428:e=>{e.exports=import("axios")},1680:e=>{e.exports=require("@restart/ui/utils")},1919:e=>{e.exports=require("react-transition-group/Transition")},3892:e=>{e.exports=require("classnames")},4048:e=>{e.exports=require("react-truncate")},4897:(e,r,t)=>{t.r(r),t.d(r,{default:()=>u});var s=t(8732);t(82015);var a=t(83551),o=t(49481),i=t(88751),n=t(6858),l=t(63349);let d=e=>{let r=[];return e?.forEach(e=>{e.partner_institution?.forEach(e=>{r.push({_id:e._id,title:e.title})})}),r=r.filter((e,r,t)=>t.findIndex(r=>r._id===e._id)===r)},u=e=>{var r,t;let{t:u}=(0,i.useTranslation)("common"),{description:p,partner_institutions:c}=e.project,x=d(c);return(0,s.jsx)(s.Fragment,{children:(0,s.jsxs)(a.A,{className:"projectInfoBlock",children:[(0,s.jsx)(o.A,{className:"projectDescBlock",md:8,children:(0,s.jsx)(l.A,{description:p})}),(0,s.jsxs)(o.A,{md:4,className:"projectInfo",children:[(0,s.jsx)(n.A,{header:u("ProjectInformation"),body:function(e,r){let{area_of_work:t,funded_by:a}=r;return(0,s.jsxs)("div",{children:[(0,s.jsxs)("div",{className:"projetInfoItems",children:[(0,s.jsxs)("h6",{children:[e("AreaofWork"),": "]}),(0,s.jsx)("p",{children:t?.map(e=>e.title).join(", ")})]}),(0,s.jsxs)("div",{className:"projetInfoItems",children:[(0,s.jsxs)("h6",{children:[e("FundedBy"),": "]}),(0,s.jsx)("p",{children:a})]})]})}(u,e.project)}),(0,s.jsx)(n.A,{header:u("PartnerOrganisation"),body:(r=x,(0,s.jsx)("ul",{className:"projectPartner",children:r?.map((e,r)=>(0,s.jsx)("li",{children:e?.title||""},r))}))}),(0,s.jsx)(n.A,{header:u("CountriesCoveredbyProject"),body:(t=c,(0,s.jsx)("ul",{className:"projectPartner",children:t?.map((e,r)=>(0,s.jsx)("li",{children:e?.partner_country?.title||""},r))}))})]})]})})}},6009:e=>{e.exports=require("dom-helpers/ownerDocument")},6417:(e,r,t)=>{t.d(r,{A:()=>a});let s=t(82015).createContext(null);s.displayName="CardHeaderContext";let a=s},6858:(e,r,t)=>{t.d(r,{A:()=>p});var s=t(8732),a=t(82015),o=t.n(a),i=t(18597),n=t(78219),l=t.n(n);function d(e){let{list:r,dialogClassName:t}=e;return(0,s.jsxs)(l(),{...e,dialogClassName:t,"aria-labelledby":"contained-modal-title-vcenter",centered:!0,children:[(0,s.jsx)(l().Header,{closeButton:!0,children:(0,s.jsx)(l().Title,{id:"contained-modal-title-vcenter",children:r.heading})}),(0,s.jsx)(l().Body,{children:r.body})]})}function u(e){let{list:r}=e,[t,a]=o().useState(!1);return r&&r.body?(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)("button",{type:"button",onClick:()=>a(!0),style:{border:"none",background:"none",padding:0},children:(0,s.jsx)(i.A.Footer,{children:(0,s.jsx)("i",{className:"fas fa-chevron-down"})})}),e.list&&(0,s.jsx)(d,{list:e.list,show:t,onHide:()=>a(!1),dialogClassName:e.dialogClassName})]}):null}let p=function(e){let{header:r,body:t}=e;return(0,s.jsxs)(i.A,{className:"text-center infoCard",children:[(0,s.jsx)(i.A.Header,{children:r}),(0,s.jsx)(i.A.Body,{children:(0,s.jsx)(i.A.Text,{children:t})}),(0,s.jsx)(u,{...e})]})}},6952:e=>{e.exports=require("@restart/ui/Modal")},7374:e=>{e.exports=require("@restart/ui/Dropdown")},8732:e=>{e.exports=require("react/jsx-runtime")},9653:e=>{e.exports=require("@restart/ui/Overlay")},11242:e=>{e.exports=require("redux-persist/integration/react")},11688:e=>{e.exports=require("@restart/hooks/useWillUnmount")},12388:e=>{e.exports=require("dom-helpers/addEventListener")},13364:e=>{e.exports=require("redux-saga/effects")},14062:e=>{e.exports=import("react-redux")},14078:e=>{e.exports=import("swr")},14332:e=>{e.exports=require("uncontrollable")},16116:e=>{e.exports=require("invariant")},18597:(e,r,t)=>{t.d(r,{A:()=>P});var s=t(3892),a=t.n(s),o=t(82015),i=t(80739),n=t(8732);let l=o.forwardRef(({className:e,bsPrefix:r,as:t="div",...s},o)=>(r=(0,i.oU)(r,"card-body"),(0,n.jsx)(t,{ref:o,className:a()(e,r),...s})));l.displayName="CardBody";let d=o.forwardRef(({className:e,bsPrefix:r,as:t="div",...s},o)=>(r=(0,i.oU)(r,"card-footer"),(0,n.jsx)(t,{ref:o,className:a()(e,r),...s})));d.displayName="CardFooter";var u=t(6417);let p=o.forwardRef(({bsPrefix:e,className:r,as:t="div",...s},l)=>{let d=(0,i.oU)(e,"card-header"),p=(0,o.useMemo)(()=>({cardHeaderBsPrefix:d}),[d]);return(0,n.jsx)(u.A.Provider,{value:p,children:(0,n.jsx)(t,{ref:l,...s,className:a()(r,d)})})});p.displayName="CardHeader";let c=o.forwardRef(({bsPrefix:e,className:r,variant:t,as:s="img",...o},l)=>{let d=(0,i.oU)(e,"card-img");return(0,n.jsx)(s,{ref:l,className:a()(t?`${d}-${t}`:d,r),...o})});c.displayName="CardImg";let x=o.forwardRef(({className:e,bsPrefix:r,as:t="div",...s},o)=>(r=(0,i.oU)(r,"card-img-overlay"),(0,n.jsx)(t,{ref:o,className:a()(e,r),...s})));x.displayName="CardImgOverlay";let m=o.forwardRef(({className:e,bsPrefix:r,as:t="a",...s},o)=>(r=(0,i.oU)(r,"card-link"),(0,n.jsx)(t,{ref:o,className:a()(e,r),...s})));m.displayName="CardLink";var h=t(7783);let f=(0,h.A)("h6"),j=o.forwardRef(({className:e,bsPrefix:r,as:t=f,...s},o)=>(r=(0,i.oU)(r,"card-subtitle"),(0,n.jsx)(t,{ref:o,className:a()(e,r),...s})));j.displayName="CardSubtitle";let q=o.forwardRef(({className:e,bsPrefix:r,as:t="p",...s},o)=>(r=(0,i.oU)(r,"card-text"),(0,n.jsx)(t,{ref:o,className:a()(e,r),...s})));q.displayName="CardText";let g=(0,h.A)("h5"),v=o.forwardRef(({className:e,bsPrefix:r,as:t=g,...s},o)=>(r=(0,i.oU)(r,"card-title"),(0,n.jsx)(t,{ref:o,className:a()(e,r),...s})));v.displayName="CardTitle";let b=o.forwardRef(({bsPrefix:e,className:r,bg:t,text:s,border:o,body:d=!1,children:u,as:p="div",...c},x)=>{let m=(0,i.oU)(e,"card");return(0,n.jsx)(p,{ref:x,...c,className:a()(r,m,t&&`bg-${t}`,s&&`text-${s}`,o&&`border-${o}`),children:d?(0,n.jsx)(l,{children:u}):u})});b.displayName="Card";let P=Object.assign(b,{Img:c,Title:v,Subtitle:j,Body:l,Link:m,Text:q,Header:p,Footer:d,ImgOverlay:x})},18622:e=>{e.exports=require("yup")},19121:e=>{e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},22326:e=>{e.exports=require("react-dom")},22541:e=>{e.exports=require("redux-persist/lib/storage")},25303:e=>{e.exports=require("@restart/ui/NavItem")},26324:e=>{e.exports=require("warning")},27825:e=>{e.exports=require("lodash")},27910:e=>{e.exports=require("stream")},28217:e=>{e.exports=require("@restart/ui/DropdownItem")},29021:e=>{e.exports=require("fs")},29294:e=>{e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},29628:e=>{e.exports=require("dom-helpers/addClass")},29825:e=>{e.exports=require("prop-types")},29841:e=>{e.exports=require("dom-helpers/hasClass")},30362:e=>{e.exports=require("@restart/ui/DropdownToggle")},33873:e=>{e.exports=require("path")},36653:e=>{e.exports=require("nprogress")},36955:e=>{e.exports=require("@restart/hooks/useBreakpoint")},37766:e=>{e.exports=require("@restart/hooks/useMergedRefs")},39756:e=>{e.exports=import("redux")},40051:e=>{e.exports=require("dom-helpers/removeClass")},40361:e=>{e.exports=require("next/dist/compiled/next-server/pages.runtime.prod.js")},42893:e=>{e.exports=import("react-hot-toast")},43294:e=>{e.exports=require("formik")},50009:e=>{e.exports=require("@restart/ui/DropdownMenu")},57664:e=>{e.exports=require("dom-helpers/scrollbarSize")},58928:e=>{e.exports=require("dom-helpers/canUseDOM")},59717:e=>{e.exports=require("@restart/ui/DropdownContext")},60560:e=>{e.exports=require("dom-helpers/contains")},63033:e=>{e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},63349:(e,r,t)=>{t.d(r,{A:()=>i});var s=t(8732),a=t(82015),o=t(88751);let i=e=>{let{t:r}=(0,o.useTranslation)("common"),t=parseInt("255"),[i,n]=(0,a.useState)(!1);return(0,s.jsxs)(s.Fragment,{children:[e.description?(0,s.jsx)("div",{dangerouslySetInnerHTML:((r,s)=>({__html:!s&&r.length>t?r.substring(0,t)+"...":e.description}))(e.description,i),className:"operationDesc"}):null,e.description&&e.description.length>t?(0,s.jsx)("button",{type:"button",className:"readMoreText",onClick:()=>n(!i),children:r(i?"readLess":"readMore")}):null]})}},65447:e=>{e.exports=require("@restart/ui/Button")},67364:e=>{e.exports=require("@restart/hooks/useCallbackRef")},68455:e=>{e.exports=import("redux-saga")},69722:e=>{e.exports=require("es6-promise")},74075:e=>{e.exports=require("zlib")},74716:e=>{e.exports=require("moment")},74987:e=>{e.exports=require("@restart/ui/ModalManager")},78097:e=>{e.exports=require("next-redux-saga")},78634:e=>{e.exports=require("@restart/ui/Anchor")},80237:(e,r)=>{Object.defineProperty(r,"A",{enumerable:!0,get:function(){return t}});var t=function(e){return e.PAGES="PAGES",e.PAGES_API="PAGES_API",e.APP_PAGE="APP_PAGE",e.APP_ROUTE="APP_ROUTE",e.IMAGE="IMAGE",e}({})},81366:e=>{e.exports=require("redux-auth-wrapper/connectedAuthWrapper")},81413:(e,r)=>{Object.defineProperty(r,"M",{enumerable:!0,get:function(){return function e(r,t){return t in r?r[t]:"then"in r&&"function"==typeof r.then?r.then(r=>e(r,t)):"function"==typeof r&&"default"===t?r:void 0}}})},81521:e=>{e.exports=require("dom-helpers/querySelectorAll")},81895:e=>{e.exports=require("@restart/hooks/useEventCallback")},82015:e=>{e.exports=require("react")},86842:e=>{e.exports=require("@restart/ui/SelectableContext")},87571:e=>{e.exports=require("dom-helpers/transitionEnd")},88751:e=>{e.exports=require("next-i18next")},93787:e=>{e.exports=require("redux-persist")},94947:e=>{e.exports=require("@restart/hooks/useTimeout")},96196:e=>{e.exports=require("next-redux-wrapper")},98320:e=>{e.exports=require("@restart/hooks/useIsomorphicEffect")},99295:(e,r,t)=>{t.a(e,async(e,s)=>{try{t.r(r),t.d(r,{config:()=>f,default:()=>c,getServerSideProps:()=>h,getStaticPaths:()=>m,getStaticProps:()=>x,reportWebVitals:()=>j,routeModule:()=>y,unstable_getServerProps:()=>b,unstable_getServerSideProps:()=>P,unstable_getStaticParams:()=>v,unstable_getStaticPaths:()=>g,unstable_getStaticProps:()=>q});var a=t(63885),o=t(80237),i=t(81413),n=t(9616),l=t.n(n),d=t(72386),u=t(4897),p=e([d]);d=(p.then?(await p)():p)[0];let c=(0,i.M)(u,"default"),x=(0,i.M)(u,"getStaticProps"),m=(0,i.M)(u,"getStaticPaths"),h=(0,i.M)(u,"getServerSideProps"),f=(0,i.M)(u,"config"),j=(0,i.M)(u,"reportWebVitals"),q=(0,i.M)(u,"unstable_getStaticProps"),g=(0,i.M)(u,"unstable_getStaticPaths"),v=(0,i.M)(u,"unstable_getStaticParams"),b=(0,i.M)(u,"unstable_getServerProps"),P=(0,i.M)(u,"unstable_getServerSideProps"),y=new a.PagesRouteModule({definition:{kind:o.A.PAGES,page:"/project/components/ProjectInfoSection",pathname:"/project/components/ProjectInfoSection",bundlePath:"",filename:""},components:{App:d.default,Document:l()},userland:u});s()}catch(e){s(e)}})},99460:e=>{e.exports=require("dom-helpers/css")}};var r=require("../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[6089,9216,9616,2386],()=>t(99295));module.exports=s})();