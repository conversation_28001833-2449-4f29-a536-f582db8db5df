(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[3843],{189:(e,t,r)=>{"use strict";function n(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(<PERSON><PERSON><PERSON>,[],function(){}))}catch(e){}return(n=function(){return!!e})()}r.d(t,{A:()=>n})},1584:(e,t,r)=>{"use strict";function n(e){if(void 0===e)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return e}r.d(t,{A:()=>n})},8758:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>l});var n=r(37876),s=r(14232),a=r(82851),o=r.n(a),c=r(51337),i=r(53718);let l=function(e){let[t,r]=(0,s.useState)([]),a=async()=>{let e=await i.A.get("/updateType",{query:{title:"Calendar Event"}});e&&e.data&&l(e.data[0]._id)},l=async t=>{let n=await i.A.get("/updates",{query:{type:"vspace",update_type:t},sort:{created_at:"desc"},limit:20});if(n&&n.data&&e.id){let t=o().filter(n.data,{parent_vspace:e.id});o().forEach(t,function(e,r){t[r].allDay=!1}),r(t)}};return(0,s.useEffect)(()=>{e.id&&a()},[e.id]),(0,n.jsx)(c.A,{eventsList:t,minicalendar:!0,showEventCounts:!0})}},22055:(e,t,r)=>{"use strict";r.d(t,{A:()=>a});var n=r(54945),s=r(1584);function a(e,t){if(t&&("object"==(0,n.A)(t)||"function"==typeof t))return t;if(void 0!==t)throw TypeError("Derived constructors may only return object or undefined");return(0,s.A)(e)}},27196:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});var n=r(74767);function s(e,t){if("function"!=typeof t&&null!==t)throw TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&(0,n.A)(e,t)}},38333:(e,t,r)=>{"use strict";r.d(t,{A:()=>a});var n=Number.isNaN||function(e){return"number"==typeof e&&e!=e};function s(e,t){if(e.length!==t.length)return!1;for(var r,s,a=0;a<e.length;a++)if(!((r=e[a])===(s=t[a])||n(r)&&n(s))&&1)return!1;return!0}function a(e,t){void 0===t&&(t=s);var r=null;function n(){for(var n=[],s=0;s<arguments.length;s++)n[s]=arguments[s];if(r&&r.lastThis===this&&t(n,r.lastArgs))return r.lastResult;var a=e.apply(this,n);return r={lastResult:a,lastArgs:n,lastThis:this},a}return n.clear=function(){r=null},n}},38380:(e,t,r)=>{(window.__NEXT_P=window.__NEXT_P||[]).push(["/vspace/VirtualspaceCalendarEvents",function(){return r(8758)}])},44212:(e,t,r)=>{"use strict";r.d(t,{A:()=>a});var n=r(10810);function s(e,t){for(var r=0;r<t.length;r++){var s=t[r];s.enumerable=s.enumerable||!1,s.configurable=!0,"value"in s&&(s.writable=!0),Object.defineProperty(e,(0,n.A)(s.key),s)}}function a(e,t,r){return t&&s(e.prototype,t),r&&s(e,r),Object.defineProperty(e,"prototype",{writable:!1}),e}},51337:(e,t,r)=>{"use strict";r.d(t,{A:()=>A});var n=r(37876),s=r(30523),a=r(10841),o=r.n(a);r(90932);var c=r(89099),i=r.n(c),l=r(49589),u=r(56970),d=r(37784),f=r(21772),p=r(11041),y=r(82851),h=r.n(y),v=r(31753);let b=(0,s.ye)(o());function j(e){let{t,i18n:r}=(0,v.Bd)("common"),a=r.language,{eventsList:o,style:c,minicalendar:l,views:u}=e,d={};return e.showEventCounts&&(d={eventWrapper:w,month:{dateHeader:e=>(0,n.jsx)(O,{eventsList:o,...e})}}),l&&(d.toolbar=m),(0,n.jsx)(s.Vv,{culture:a,localizer:b,events:o,views:u,startAccessor:"start_date",endAccessor:"end_date",style:c,components:d,messages:{today:t("today"),previous:t("back"),next:t("Next"),month:t("Month"),week:t("Week"),day:t("Day")},onSelectEvent:e=>{let t=Object.keys(e).filter(e=>e.includes("parent")).toString(),r=t.split("_")[1];i().push("/".concat(r,"/show/").concat(e[t],"/update/").concat(e._id))}})}function m(e){return(0,n.jsx)(l.A,{className:"mb-1",children:(0,n.jsxs)(u.A,{children:[(0,n.jsx)(d.A,{className:"p-0",md:1,children:(0,n.jsx)("i",{style:{cursor:"pointer"},onClick:()=>e.onNavigate("PREV"),className:"fas fa-chevron-left"})}),(0,n.jsx)(d.A,{className:"text-center",md:10,children:(0,n.jsx)("span",{className:"rbc-toolbar-label",children:e.label})}),(0,n.jsx)(d.A,{className:"p-0 text-end",md:1,children:(0,n.jsx)("i",{style:{cursor:"pointer"},onClick:()=>e.onNavigate("NEXT"),className:"fas fa-chevron-right"})})]})})}j.defaultProps={minicalendar:!1,views:["month"]};let g=(e,t)=>{let r=0;return h().forEach(e,e=>{let n=o()(e.start_date).set({hour:0,minute:0,second:0,millisecond:0}),s=o()(e.end_date).set({hour:0,minute:0,second:0,millisecond:0});o()(t).isBetween(n,s,null,"[]")&&(r+=1)}),r},O=e=>{let{date:t,label:r,eventsList:s}=e,a=g(s,t),c=o()(t).isBefore(new Date,"day");return(0,n.jsxs)("div",{className:"rbc-date-cell",onClick:()=>i().push("/events-calendar"),children:[(0,n.jsx)("a",{href:"#",children:r}),a>0&&(0,n.jsxs)("span",{className:"d-flex justify-content-start align-items-center fa-stack",children:[(0,n.jsx)(f.g,{icon:p.yy,color:c?"grey":"#04A6FB",size:"lg"}),(0,n.jsx)("span",{className:"eventCount",children:a})]})]})},w=e=>(0,n.jsx)("div",{onSelect:e.onSelect}),A=j},57078:(e,t,r)=>{"use strict";r.d(t,{A:()=>a});var n=r(85190);function s(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function a(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?s(Object(r),!0).forEach(function(t){(0,n.A)(e,t,r[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):s(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}},59524:(e,t,r)=>{"use strict";function n(e,t){if(!(e instanceof t))throw TypeError("Cannot call a class as a function")}r.d(t,{A:()=>n})},61371:(e,t,r)=>{"use strict";function n(e){return(n=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)})(e)}r.d(t,{A:()=>n})}},e=>{var t=t=>e(e.s=t);e.O(0,[7725,8531,1772,30,636,6593,8792],()=>t(38380)),_N_E=e.O()}]);
//# sourceMappingURL=VirtualspaceCalendarEvents-5bca0631cd0fa790.js.map