"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/_app",{

/***/ "(pages-dir-browser)/./store.tsx":
/*!*******************!*\
  !*** ./store.tsx ***!
  \*******************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var redux__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! redux */ \"(pages-dir-browser)/./node_modules/redux/dist/redux.mjs\");\n/* harmony import */ var redux_persist__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! redux-persist */ \"(pages-dir-browser)/./node_modules/redux-persist/es/index.js\");\n/* harmony import */ var redux_persist_lib_storage__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! redux-persist/lib/storage */ \"(pages-dir-browser)/./node_modules/redux-persist/lib/storage/index.js\");\n/* harmony import */ var redux_saga__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! redux-saga */ \"(pages-dir-browser)/./node_modules/redux-saga/dist/redux-saga-core-npm-proxy.esm.js\");\n/* harmony import */ var _stores_userReducer__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./stores/userReducer */ \"(pages-dir-browser)/./stores/userReducer.tsx\");\n/* harmony import */ var _stores_permissionsReducer__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./stores/permissionsReducer */ \"(pages-dir-browser)/./stores/permissionsReducer.tsx\");\n/* harmony import */ var _saga__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./saga */ \"(pages-dir-browser)/./saga.tsx\");\n//Import Library\n\n\n\n\n// Fix for redux-persist SSR warning\nconst createNoopStorage = ()=>{\n    return {\n        getItem (_key) {\n            return Promise.resolve(null);\n        },\n        setItem (_key, value) {\n            return Promise.resolve(value);\n        },\n        removeItem (_key) {\n            return Promise.resolve();\n        }\n    };\n};\nconst persistStorage =  true ? redux_persist_lib_storage__WEBPACK_IMPORTED_MODULE_1__[\"default\"] : 0;\n//Import services/components\n\n\n\nconst rootReducer = (0,redux__WEBPACK_IMPORTED_MODULE_6__.combineReducers)({\n    user: _stores_userReducer__WEBPACK_IMPORTED_MODULE_3__[\"default\"],\n    permissions: _stores_permissionsReducer__WEBPACK_IMPORTED_MODULE_4__[\"default\"]\n});\nconst persistConfig = {\n    key: 'root',\n    storage: redux_persist_lib_storage__WEBPACK_IMPORTED_MODULE_1__[\"default\"]\n};\nconst persistedReducer = (0,redux_persist__WEBPACK_IMPORTED_MODULE_0__.persistReducer)(persistConfig, rootReducer);\nconst bindMiddleware = (middleware)=>{\n    if (true) {\n        const { composeWithDevTools } = __webpack_require__(/*! @redux-devtools/extension */ \"(pages-dir-browser)/./node_modules/@redux-devtools/extension/lib/esm/index.js\");\n        return composeWithDevTools((0,redux__WEBPACK_IMPORTED_MODULE_6__.applyMiddleware)(...middleware));\n    }\n    return (0,redux__WEBPACK_IMPORTED_MODULE_6__.applyMiddleware)(...middleware);\n};\nfunction configureStore(initialState) {\n    const sagaMiddleware = (0,redux_saga__WEBPACK_IMPORTED_MODULE_2__[\"default\"])();\n    const store = (0,redux__WEBPACK_IMPORTED_MODULE_6__.createStore)(persistedReducer, initialState, bindMiddleware([\n        sagaMiddleware\n    ]));\n    store.sagaTask = sagaMiddleware.run(_saga__WEBPACK_IMPORTED_MODULE_5__[\"default\"]);\n    return store;\n}\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (configureStore);\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-browser)/./store.tsx\n"));

/***/ })

});