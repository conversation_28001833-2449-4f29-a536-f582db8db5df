{"version": 3, "file": "static/chunks/pages/country/CountriesMap-36d2f6b9758ff1c1.js", "mappings": "qJAoEA,MA/CkD,OAAC,MACjDA,EAAO,QAAQ,IACfC,CA6CaC,CA7CR,EAAE,SA6CkBA,EA5CzBC,EAAY,EAAE,MACdC,CAAI,MACJC,CAAI,UACJC,CAAQ,SACRC,CAAO,OACPC,CAAK,WACLC,GAAY,CAAK,CAClB,UAsBC,GAAyC,UAAxB,OAAOH,EAASI,GAAG,EAAyC,UAAxB,OAAOJ,EAASK,GAAG,CAKtE,UAACC,EAAAA,EAAMA,CAAAA,CACLN,SAAUA,EACVD,KAAMA,EACNG,MAAOA,GAASR,EAChBS,UAAWA,EACXF,QA/BgB,CA+BPM,GA9BPN,GAeFA,EAdoB,IADT,EAeHO,KAZNb,QAYmBc,IAXnBZ,OACAC,WACAE,CACF,EAGe,UACbA,EACAU,YAAa,IAAMV,CACrB,EAE6BW,EAEjC,IAIS,IAYX,mBCjEA,4CACA,wBACA,WACA,OAAe,EAAQ,KAA6C,CACpE,EACA,SAFsB,+ECYtB,MARyB,OAAC,UAAEX,CAAQ,OAQrBY,OARuBC,CAAY,QAQnBD,EAAC,CAR4B,CAAS,GACnE,MACE,UAACE,EAAAA,EAAUA,CAAAA,CAACd,SAAUA,EAAUa,aAAcA,WAC5C,UAACE,MAAAA,UAAKC,KAGZ,ECdMC,EAAO,mBACPC,EAAQ,qBACRC,EAAO,eACPC,EAAW,kBAgTjB,EA/SkB,CAChB,CACE,YAAe,IA6SJC,OA5SX,EA4SoBA,EAAC,IA5SV,CACT,CACE,MAAS,SACX,EACD,EAEH,CACE,YAAeJ,EACf,QAAW,CACT,CACE,MAAS,SACX,EACD,EAEH,CACE,YAAeC,EACf,QAAW,CACT,CACE,MAAS,SACX,EAEJ,EACA,CACE,YAAe,iBACf,YAAe,WACf,QAAW,CACT,CACE,WAAc,KAChB,EAEJ,EACA,CACE,YAAe,yBACf,YAAeE,EACf,QAAW,CACT,CACE,MAAS,SACX,EACD,EAEH,CACE,YAAe,6BACf,YAAe,SACf,QAAW,CACT,CACE,WAAc,KAChB,EAEJ,EACA,CACE,YAAe,6BACf,YAAeH,EACf,QAAW,CACT,CACE,MAAS,SACX,EACD,EAEH,CACE,YAAe,0BACf,YAAeG,EACf,QAAW,CACT,CACE,MAAS,SACX,EACD,EAEH,CACE,YAAe,qBACf,YAAeA,EACf,QAAW,CACT,CACE,MAAS,SACX,EACD,EAEH,CACE,YAAe,oBACf,YAAe,WACf,QAAW,CACT,CACE,MAAS,SACX,EACD,EAEH,CACE,YAAe,MACf,QAAW,CACT,CACE,WAAc,KAChB,EACD,EAEH,CACE,YAAe,MACf,YAAe,WACf,QAAW,CACT,CACE,MAAS,SACX,EACD,EAEH,CACE,YAAe,MACf,YAAe,cACf,QAAW,CACT,CACE,WAAc,KAChB,EACD,EAEH,CACE,YAAe,MACf,YAAeH,EACf,QAAW,CACT,CACE,MAAS,SACX,EACD,EAEH,CACE,YAAe,MACf,YAAeC,EACf,QAAW,CACT,CACE,MAAS,SACX,EACD,EAEH,CACE,YAAe,WACf,YAAe,gBACf,QAAW,CACT,CACE,MAAS,SACX,EACD,EAEH,CACE,YAAe,WACf,YAAeD,EACf,QAAW,CACT,CACE,MAAS,SACX,EACD,EAEH,CACE,YAAe,OACf,QAAW,CACT,CACE,WAAc,KAChB,EACD,EAEH,CACE,YAAe,OACf,YAAe,WACf,QAAW,CACT,CACE,MAAS,SACX,EACD,EAEH,CACE,YAAe,OACf,YAAe,cACf,QAAW,CACT,CACE,WAAc,KAChB,EACD,EAEH,CACE,YAAe,OACf,YAAeA,EACf,QAAW,CACT,CACE,MAAS,SACX,EACD,EAEH,CACE,YAAe,OACf,YAAeC,EACf,QAAW,CACT,CACE,MAAS,SACX,EACD,EAEH,CACE,YAAeC,EACf,YAAe,WACf,QAAW,CACT,CACE,MAAS,SACX,EACD,EAEH,CACE,YAAeA,EACf,YAAeC,EACf,QAAW,CACT,CACE,MAAS,SACX,EACD,EAEH,CACE,YAAeD,EACf,YAAeF,EACf,QAAW,CACT,CACE,MAAS,SACX,EACD,EAEH,CACE,YAAeE,EACf,YAAeD,EACf,QAAW,CACT,CACE,MAAS,SACX,EACD,EAEH,CACE,YAAe,aACf,YAAe,SACf,QAAW,CACT,CACE,WAAc,KAChB,EACD,EAEH,CACE,YAAe,UACf,QAAW,CACT,CACE,WAAc,KAChB,EACD,EAEH,CACE,YAAe,UACf,YAAeD,EACf,QAAW,CACT,CACE,MAAS,SACX,EACD,EAEH,CACE,YAAe,UACf,YAAeC,EACf,QAAW,CACT,CACE,MAAS,SACX,EAEJ,EACA,CACE,YAAe,eACf,YAAe,gBACf,QAAW,CACT,CACE,MAAS,SACX,EACD,EAEH,CACE,YAAe,kBACf,YAAe,WACf,QAAW,CACT,CACE,MAAS,SACX,EACD,EAEH,CACE,YAAe,QACf,YAAe,WACf,QAAW,CACT,CACE,MAAS,SACX,EACD,EAEH,CACE,YAAe,QACf,YAAeD,EACf,QAAW,CACT,CACE,MAAS,SACX,EACD,EAEJ,2BCnND,MAzEwC,OAAC,SAyE1BK,GAxEbC,CAAU,GAwEUD,EAAC,SAvErBE,CAAY,eACZC,CAAa,UACbT,CAAQ,QACRU,EAAS,GAAG,OACZC,EAAQ,MAAM,UACdC,CAAQ,CACRC,OAAO,CAAC,SACRC,EAAU,CAAC,SACXC,CAAO,CACR,GACO,QAAEC,CAAM,CAAE,CAAGC,CAAAA,EAAAA,EAAAA,SAAAA,CAASA,GACtB,UAAEC,CAAQ,WAAEC,CAAS,CAAE,CAAIC,CAAAA,EAAAA,EAAAA,CAAAA,CAAaA,UAmB9C,EAAsB,SAAP,CAAQrB,MAAAA,UAAI,uBACtBmB,EAGH,QAHa,EAGZnB,MAAAA,CAAIsB,UAAU,yBACb,UAACtB,MAAAA,CAAIsB,UAAU,WAAWC,MAAO,OAAEX,SAAOD,EAAQ1B,SAAU,UAAW,WACrE,WAACuC,EAAAA,EAASA,CAAAA,CACRC,kBAzBe,CACrBb,MAAOA,EACPD,OAA0B,UAAlB,OAAOA,EAAsB,GAAU,OAAPA,EAAO,MAAMA,CACvD,EAuBQe,OAhBOhB,CAgBCgB,EArBM,CACpBrC,IAAK,SAIyBsC,CAH9BrC,IAAK,SACP,EAmBQwB,KAAMA,EACNc,OAhBU,CAgBFC,GAfdC,EAAIC,UAAU,CAAC,CACbC,OAAQ1B,CACV,EACF,EAaQ2B,QAAS,CACPlB,EAhBWT,MAgBFS,EACT3B,WAAW,EACX8C,kBAAmB,GACnBC,mBAAmB,EACnBC,YAAY,EACZC,gBAAgB,EAChBC,eAAgB,GAChBC,mBAAmB,CACrB,YAECtC,EACAO,GAAcC,GAAgBA,EAAad,WAAW,EACrD,UAACE,EAAgBA,CACfZ,SAAUwB,EAAad,SADRE,EACmB,GAClCC,aAAc,KAEZ0C,QAAQC,GAAG,CAAC,eACZzB,SAAAA,GACF,QADEA,GAGDR,WA/BS,UAACR,MAAAA,UAAI,mBAsC7B,6ICtEA,IAAM0C,EAAe,IACnB,GAAM,MAAEC,CAAI,CAAE,CAAGC,CAAAA,EAAAA,EAAAA,EAAAA,CAAcA,CAAC,UAC1BC,EAAcF,EAAK9B,QAAQ,CAC3B,CAACJ,EAAcqC,EAAgB,CAAGC,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAM,CAAC,GACjD,CAACvC,EAAYwC,EAAc,CAAGD,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAM,CAAC,GAC7C,CAACE,EAAQC,EAAU,CAAGH,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAQ,EAAE,EACxC,WAAEI,CAAS,CAAE,CAAGC,EAOhBC,EAAc,KAClBP,EAAgB,MAChBE,EAAc,KAChB,EAEMM,EAAgB,CAACC,EAAyC7D,EAAa8D,KAC3EH,IACAP,EAAgBpD,GAChBsD,EAAc,CACZrE,KAAM4E,EAAU5E,IAAI,CACpBC,GAAI2E,EAAU3E,EAAE,EAEpB,EAEM6E,EAAyB,KAC7B,IAAMC,EAAqB,EAAE,CACzBP,GAAaA,EAAUQ,IAAI,EAAE,IAC/BC,OAAS,CAACT,EAAUQ,IAAI,CAAE,IACxBD,EAAYG,IAAI,CAAC,CACf1E,MAAO2E,EAAQ3E,KAAK,CACpBP,GAAIkF,EAAQC,GAAG,CACf1E,IACEyE,EAAQE,WAAW,EAAIF,EAAQE,WAAW,CAAC,EAAE,CACzCF,EAAQE,WAAW,CAAC,EAAE,CAACC,QAAQ,CAC/B,KACN3E,IACEwE,EAAQE,WAAW,EAAIF,EAAQE,WAAW,CAAC,EAAE,CACzCF,EAAQE,WAAW,CAAC,EAAE,CAACE,SAAS,CAChC,IACR,EACF,GAEFhB,EAAU,IAAIQ,EAAY,CAC5B,EAMA,MAJAS,CAAAA,EAAAA,EAAAA,SAAAA,CAASA,CAAC,KACRV,GACF,EAAG,CAACN,EAAU,EAGZ,UAACiB,EAAAA,CAAOA,CAAAA,CACNvD,SAAUgC,EACVI,OAAQA,EACRtC,OAAQ,IACRF,aAAcA,EACdD,WAAY,UAlDG,IACjB,GAAM,MAAE6D,CAAI,CAAE,CAAGC,EACjB,MAAO,UAACC,IAAAA,CAAEC,KAAM,WAAI3B,EAAY,kBAAyB,aAATwB,EAAAA,KAAAA,EAAAA,EAAMzF,EAAE,IAARyF,aAAaA,EAAAA,KAAAA,EAAAA,EAAM1F,IAAI,EAAV0F,EAgD9CI,CAAWJ,KAAM7D,IAC9BQ,QAASqC,WAERJ,GAAUA,EAAOyB,MAAM,EAAI,EACxBzB,EAAOnB,GAAG,CAAC,CAAC6C,EAAMC,KAChB,GAAID,GAAQA,EAAKtF,GAAG,CAClB,CADoB,KAElB,UAACR,EAAAA,CAAYA,CAAAA,CAEXF,KAAMgG,EAAKxF,KAAK,CAChBP,GAAI+F,EAAK/F,EAAE,CACXI,KAAM,CACJ6F,IAAK,8BACP,EACA3F,QAASoE,EACTrE,SAAU0F,GAPLC,EAWb,GACA,MAGV,EAEAlC,EAAaoC,YAAY,CAAG,CAC1B3B,UAAW,CAAEQ,KAAM,EAAE,CACvB,EAEA,MAAejB,YAAYA,EAAC", "sources": ["webpack://_N_E/./components/common/RKIMapMarker.tsx", "webpack://_N_E/?08fd", "webpack://_N_E/./components/common/RKIMapInfowindow.tsx", "webpack://_N_E/./components/common/mapStyles.tsx", "webpack://_N_E/./components/common/RKIMap1.tsx", "webpack://_N_E/./pages/country/CountriesMap.tsx"], "sourcesContent": ["import React from 'react';\r\nimport { Marker } from '@react-google-maps/api';\r\n\r\ninterface RKIMapMarkerProps {\r\n  name?: string;\r\n  id?: string;\r\n  countryId?: string;\r\n  type?: string;\r\n  icon?: {\r\n    url: string;\r\n    scaledSize?: google.maps.Size;\r\n  };\r\n  position: {\r\n    lat: number;\r\n    lng: number;\r\n  };\r\n  onClick?: (props: any, marker: any, e: any) => void;\r\n  title?: string;\r\n  draggable?: boolean;\r\n}\r\n\r\nconst RKIMapMarker: React.FC<RKIMapMarkerProps> = ({\r\n  name = 'Marker',\r\n  id = '',\r\n  countryId = '',\r\n  type,\r\n  icon,\r\n  position,\r\n  onClick,\r\n  title,\r\n  draggable = false,\r\n}) => {\r\n  const handleClick = (e: google.maps.MapMouseEvent) => {\r\n    if (onClick) {\r\n      const markerProps = {\r\n        name,\r\n        id,\r\n        countryId,\r\n        type,\r\n        position,\r\n      };\r\n\r\n      // Create a marker-like object for compatibility with old onClick signature\r\n      const marker = {\r\n        position,\r\n        getPosition: () => position,\r\n      };\r\n\r\n      onClick(markerProps, marker, e);\r\n    }\r\n  };\r\n\r\n  // Ensure position is valid\r\n  if (!position || typeof position.lat !== 'number' || typeof position.lng !== 'number') {\r\n    return null;\r\n  }\r\n\r\n  return (\r\n    <Marker\r\n      position={position}\r\n      icon={icon}\r\n      title={title || name}\r\n      draggable={draggable}\r\n      onClick={handleClick}\r\n    />\r\n  );\r\n};\r\n\r\nexport default RKIMapMarker;\r\n", "\n    (window.__NEXT_P = window.__NEXT_P || []).push([\n      \"/country/CountriesMap\",\n      function () {\n        return require(\"private-next-pages/country/CountriesMap.tsx\");\n      }\n    ]);\n    if(module.hot) {\n      module.hot.dispose(function () {\n        window.__NEXT_P.push([\"/country/CountriesMap\"])\n      });\n    }\n  ", "import { InfoWindow } from '@react-google-maps/api';\r\n\r\ninterface Props {\r\n  position: google.maps.LatLngLiteral;\r\n  onCloseClick?: () => void;\r\n  children?: React.ReactNode;\r\n}\r\n\r\nconst RKIMapInfowindow = ({ position, onCloseClick, children }: Props) => {\r\n  return (\r\n    <InfoWindow position={position} onCloseClick={onCloseClick}>\r\n      <div>{children}</div>\r\n    </InfoWindow>\r\n  );\r\n};\r\n\r\nexport default RKIMapInfowindow;\r\n", "const fill = \"labels.text.fill\"\r\nconst stoke = \"labels.text.stroke\"\r\nconst road = \"road.highway\"\r\nconst geometry = \"geometry.stroke\"\r\nconst mapStyles = [\r\n  {\r\n    \"elementType\": \"geometry\",\r\n    \"stylers\": [\r\n      {\r\n        \"color\": \"#1d2c4d\"\r\n      }\r\n    ]\r\n  },\r\n  {\r\n    \"elementType\": fill,\r\n    \"stylers\": [\r\n      {\r\n        \"color\": \"#8ec3b9\"\r\n      }\r\n    ]\r\n  },\r\n  {\r\n    \"elementType\": stoke,\r\n    \"stylers\": [\r\n      {\r\n        \"color\": \"#1a3646\"\r\n      }\r\n    ]\r\n  },\r\n  {\r\n    \"featureType\": \"administrative\",\r\n    \"elementType\": \"geometry\",\r\n    \"stylers\": [\r\n      {\r\n        \"visibility\": \"off\"\r\n      }\r\n    ]\r\n  },\r\n  {\r\n    \"featureType\": \"administrative.country\",\r\n    \"elementType\": geometry,\r\n    \"stylers\": [\r\n      {\r\n        \"color\": \"#4b6878\"\r\n      }\r\n    ]\r\n  },\r\n  {\r\n    \"featureType\": \"administrative.land_parcel\",\r\n    \"elementType\": \"labels\",\r\n    \"stylers\": [\r\n      {\r\n        \"visibility\": \"off\"\r\n      }\r\n    ]\r\n  },\r\n  {\r\n    \"featureType\": \"administrative.land_parcel\",\r\n    \"elementType\": fill,\r\n    \"stylers\": [\r\n      {\r\n        \"color\": \"#64779e\"\r\n      }\r\n    ]\r\n  },\r\n  {\r\n    \"featureType\": \"administrative.province\",\r\n    \"elementType\": geometry,\r\n    \"stylers\": [\r\n      {\r\n        \"color\": \"#4b6878\"\r\n      }\r\n    ]\r\n  },\r\n  {\r\n    \"featureType\": \"landscape.man_made\",\r\n    \"elementType\": geometry,\r\n    \"stylers\": [\r\n      {\r\n        \"color\": \"#334e87\"\r\n      }\r\n    ]\r\n  },\r\n  {\r\n    \"featureType\": \"landscape.natural\",\r\n    \"elementType\": \"geometry\",\r\n    \"stylers\": [\r\n      {\r\n        \"color\": \"#023e58\"\r\n      }\r\n    ]\r\n  },\r\n  {\r\n    \"featureType\": \"poi\",\r\n    \"stylers\": [\r\n      {\r\n        \"visibility\": \"off\"\r\n      }\r\n    ]\r\n  },\r\n  {\r\n    \"featureType\": \"poi\",\r\n    \"elementType\": \"geometry\",\r\n    \"stylers\": [\r\n      {\r\n        \"color\": \"#283d6a\"\r\n      }\r\n    ]\r\n  },\r\n  {\r\n    \"featureType\": \"poi\",\r\n    \"elementType\": \"labels.text\",\r\n    \"stylers\": [\r\n      {\r\n        \"visibility\": \"off\"\r\n      }\r\n    ]\r\n  },\r\n  {\r\n    \"featureType\": \"poi\",\r\n    \"elementType\": fill,\r\n    \"stylers\": [\r\n      {\r\n        \"color\": \"#6f9ba5\"\r\n      }\r\n    ]\r\n  },\r\n  {\r\n    \"featureType\": \"poi\",\r\n    \"elementType\": stoke,\r\n    \"stylers\": [\r\n      {\r\n        \"color\": \"#1d2c4d\"\r\n      }\r\n    ]\r\n  },\r\n  {\r\n    \"featureType\": \"poi.park\",\r\n    \"elementType\": \"geometry.fill\",\r\n    \"stylers\": [\r\n      {\r\n        \"color\": \"#023e58\"\r\n      }\r\n    ]\r\n  },\r\n  {\r\n    \"featureType\": \"poi.park\",\r\n    \"elementType\": fill,\r\n    \"stylers\": [\r\n      {\r\n        \"color\": \"#3C7680\"\r\n      }\r\n    ]\r\n  },\r\n  {\r\n    \"featureType\": \"road\",\r\n    \"stylers\": [\r\n      {\r\n        \"visibility\": \"off\"\r\n      }\r\n    ]\r\n  },\r\n  {\r\n    \"featureType\": \"road\",\r\n    \"elementType\": \"geometry\",\r\n    \"stylers\": [\r\n      {\r\n        \"color\": \"#304a7d\"\r\n      }\r\n    ]\r\n  },\r\n  {\r\n    \"featureType\": \"road\",\r\n    \"elementType\": \"labels.icon\",\r\n    \"stylers\": [\r\n      {\r\n        \"visibility\": \"off\"\r\n      }\r\n    ]\r\n  },\r\n  {\r\n    \"featureType\": \"road\",\r\n    \"elementType\": fill,\r\n    \"stylers\": [\r\n      {\r\n        \"color\": \"#98a5be\"\r\n      }\r\n    ]\r\n  },\r\n  {\r\n    \"featureType\": \"road\",\r\n    \"elementType\": stoke,\r\n    \"stylers\": [\r\n      {\r\n        \"color\": \"#1d2c4d\"\r\n      }\r\n    ]\r\n  },\r\n  {\r\n    \"featureType\": road,\r\n    \"elementType\": \"geometry\",\r\n    \"stylers\": [\r\n      {\r\n        \"color\": \"#2c6675\"\r\n      }\r\n    ]\r\n  },\r\n  {\r\n    \"featureType\": road,\r\n    \"elementType\": geometry,\r\n    \"stylers\": [\r\n      {\r\n        \"color\": \"#255763\"\r\n      }\r\n    ]\r\n  },\r\n  {\r\n    \"featureType\": road,\r\n    \"elementType\": fill,\r\n    \"stylers\": [\r\n      {\r\n        \"color\": \"#b0d5ce\"\r\n      }\r\n    ]\r\n  },\r\n  {\r\n    \"featureType\": road,\r\n    \"elementType\": stoke,\r\n    \"stylers\": [\r\n      {\r\n        \"color\": \"#023e58\"\r\n      }\r\n    ]\r\n  },\r\n  {\r\n    \"featureType\": \"road.local\",\r\n    \"elementType\": \"labels\",\r\n    \"stylers\": [\r\n      {\r\n        \"visibility\": \"off\"\r\n      }\r\n    ]\r\n  },\r\n  {\r\n    \"featureType\": \"transit\",\r\n    \"stylers\": [\r\n      {\r\n        \"visibility\": \"off\"\r\n      }\r\n    ]\r\n  },\r\n  {\r\n    \"featureType\": \"transit\",\r\n    \"elementType\": fill,\r\n    \"stylers\": [\r\n      {\r\n        \"color\": \"#98a5be\"\r\n      }\r\n    ]\r\n  },\r\n  {\r\n    \"featureType\": \"transit\",\r\n    \"elementType\": stoke,\r\n    \"stylers\": [\r\n      {\r\n        \"color\": \"#1d2c4d\"\r\n      }\r\n    ]\r\n  },\r\n  {\r\n    \"featureType\": \"transit.line\",\r\n    \"elementType\": \"geometry.fill\",\r\n    \"stylers\": [\r\n      {\r\n        \"color\": \"#283d6a\"\r\n      }\r\n    ]\r\n  },\r\n  {\r\n    \"featureType\": \"transit.station\",\r\n    \"elementType\": \"geometry\",\r\n    \"stylers\": [\r\n      {\r\n        \"color\": \"#3a4762\"\r\n      }\r\n    ]\r\n  },\r\n  {\r\n    \"featureType\": \"water\",\r\n    \"elementType\": \"geometry\",\r\n    \"stylers\": [\r\n      {\r\n        \"color\": \"#0e1626\"\r\n      }\r\n    ]\r\n  },\r\n  {\r\n    \"featureType\": \"water\",\r\n    \"elementType\": fill,\r\n    \"stylers\": [\r\n      {\r\n        \"color\": \"#4e6d70\"\r\n      }\r\n    ]\r\n  }\r\n];\r\n\r\nexport default mapStyles;", "import React from 'react';\r\nimport { GoogleMap, useJsApiLoader } from '@react-google-maps/api';\r\nimport RKIMapInfowindow from './RKIMapInfowindow';\r\nimport mapStyles from './mapStyles';\r\nimport { useRouter } from \"next/router\";\r\nimport { useGoogleMaps } from './GoogleMapsProvider';\r\n\r\ninterface RKIMap1Props {\r\n  markerInfo?: React.ReactNode;\r\n  activeMarker?: any;\r\n  initialCenter?: { lat: number; lng: number };\r\n  children?: React.ReactNode;\r\n  height?: number | string;\r\n  width?: string;\r\n  language?: string;\r\n  points?: any[];\r\n  zoom?: number;\r\n  minZoom?: number;\r\n  onClose?: () => void\r\n}\r\n\r\nconst RKIMap1: React.FC<RKIMap1Props> = ({\r\n  markerInfo,\r\n  activeMarker,\r\n  initialCenter,\r\n  children,\r\n  height = 300,\r\n  width = \"114%\",\r\n  language,\r\n  zoom = 1,\r\n  minZoom = 1,\r\n  onClose\r\n}) => {\r\n  const { locale } = useRouter();\r\n  const { isLoaded, loadError } =  useGoogleMaps();\r\n  const containerStyle = {\r\n    width: width,\r\n    height: typeof height === 'number' ? `${height}px` : height,\r\n  };\r\n\r\n  const defaultCenter = {\r\n    lat: 52.520017,\r\n    lng: 13.404195,\r\n  };\r\n\r\n  const center = initialCenter || defaultCenter;\r\n\r\n  const onMapLoad = (map: google.maps.Map) => {\r\n    map.setOptions({\r\n      styles: mapStyles,\r\n    });\r\n  };\r\n\r\n  if (loadError) return <div>Error loading maps</div>;\r\n  if (!isLoaded) return <div>Loading Maps...</div>;\r\n\r\n  return (\r\n    <div className=\"map-container\">\r\n      <div className=\"mapprint\" style={{ width, height, position: 'relative' }}>\r\n        <GoogleMap\r\n          mapContainerStyle={containerStyle}\r\n          center={center}\r\n          zoom={zoom}\r\n          onLoad={onMapLoad}\r\n          options={{\r\n            minZoom: minZoom,\r\n            draggable: true,\r\n            keyboardShortcuts: false,\r\n            streetViewControl: false,\r\n            panControl: false,\r\n            clickableIcons: false,\r\n            mapTypeControl: false,\r\n            fullscreenControl: true,\r\n          }}\r\n        >\r\n          {children}\r\n          {markerInfo && activeMarker && activeMarker.getPosition && (\r\n            <RKIMapInfowindow\r\n              position={activeMarker.getPosition()}\r\n              onCloseClick={() => {\r\n                // Handle close if needed\r\n                console.log('close click');\r\n                onClose?.()\r\n              }}\r\n            >\r\n              {markerInfo}\r\n            </RKIMapInfowindow>\r\n          )}\r\n        </GoogleMap>\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default RKIMap1;\r\n", "//Import Library\r\nimport { useEffect, useState } from \"react\";\r\nimport _ from \"lodash\";\r\n\r\n//Import services/components\r\nimport RKIMapMarker from \"../../components/common/RKIMapMarker\";\r\nimport RKIMAP1 from \"../../components/common/RKIMap1\";\r\nimport { useTranslation } from 'next-i18next';\r\n\r\ninterface CountriesMapProps {\r\n  countries: {\r\n    data: Array<{\r\n      _id: string;\r\n      title: string;\r\n      coordinates?: Array<{\r\n        latitude: string;\r\n        longitude: string;\r\n      }>;\r\n    }>;\r\n  };\r\n}\r\n\r\nconst CountriesMap = (props: CountriesMapProps) => {\r\n  const { i18n } = useTranslation('common');\r\n  const currentLang = i18n.language;\r\n  const [activeMarker, setactiveMarker] = useState<any>({});\r\n  const [markerInfo, setMarkerInfo] = useState<any>({});\r\n  const [points, setPoints] = useState<any[]>([]);\r\n  const { countries } = props;\r\n\r\n  const MarkerInfo = (Props: { info: { id?: string; name?: string } }) => {\r\n    const { info } = Props;\r\n    return <a href={`/${currentLang}/country/show/${info?.id}`}>{info?.name}</a>;\r\n  };\r\n\r\n  const resetMarker = () => {\r\n    setactiveMarker(null);\r\n    setMarkerInfo(null);\r\n  };\r\n\r\n  const onMarkerClick = (propsinit: { name: string; id: string }, marker: any, _e: any) => {\r\n    resetMarker();\r\n    setactiveMarker(marker);\r\n    setMarkerInfo({\r\n      name: propsinit.name,\r\n      id: propsinit.id,\r\n    });\r\n  };\r\n\r\n  const setPointsFromCountries = () => {\r\n    const pointsvalue: any[] = [];\r\n    if (countries && countries.data) {\r\n      _.forEach(countries.data, (country) => {\r\n        pointsvalue.push({\r\n          title: country.title,\r\n          id: country._id,\r\n          lat:\r\n            country.coordinates && country.coordinates[0]\r\n              ? country.coordinates[0].latitude\r\n              : null,\r\n          lng:\r\n            country.coordinates && country.coordinates[0]\r\n              ? country.coordinates[0].longitude\r\n              : null,\r\n        });\r\n      });\r\n    }\r\n    setPoints([...pointsvalue]);\r\n  };\r\n\r\n  useEffect(() => {\r\n    setPointsFromCountries();\r\n  }, [countries]);\r\n\r\n  return (\r\n    <RKIMAP1\r\n      language={currentLang}\r\n      points={points}\r\n      height={300}\r\n      activeMarker={activeMarker}\r\n      markerInfo={<MarkerInfo info={markerInfo} />}\r\n      onClose={resetMarker}\r\n    >\r\n      {points && points.length >= 1\r\n        ? points.map((item, index) => {\r\n            if (item && item.lat) {\r\n              return (\r\n                <RKIMapMarker\r\n                  key={index}\r\n                  name={item.title}\r\n                  id={item.id}\r\n                  icon={{\r\n                    url: \"/images/map-marker-white.svg\",\r\n                  }}\r\n                  onClick={onMarkerClick}\r\n                  position={item}\r\n                />\r\n              );\r\n            }\r\n          })\r\n        : null}\r\n    </RKIMAP1>\r\n  );\r\n};\r\n\r\nCountriesMap.defaultProps = {\r\n  countries: { data: [] },\r\n};\r\n\r\nexport default CountriesMap;\r\n"], "names": ["name", "id", "R<PERSON>IMapMarker", "countryId", "type", "icon", "position", "onClick", "title", "draggable", "lat", "lng", "<PERSON><PERSON>", "handleClick", "markerProps", "marker", "getPosition", "e", "RKIMapInfowindow", "onCloseClick", "InfoWindow", "div", "children", "fill", "stoke", "road", "geometry", "mapStyles", "RKIMap1", "markerInfo", "activeMarker", "initialCenter", "height", "width", "language", "zoom", "minZoom", "onClose", "locale", "useRouter", "isLoaded", "loadError", "useGoogleMaps", "className", "style", "GoogleMap", "mapContainerStyle", "center", "defaultCenter", "onLoad", "onMapLoad", "map", "setOptions", "styles", "options", "keyboardShortcuts", "streetViewControl", "panControl", "clickableIcons", "mapTypeControl", "fullscreenControl", "console", "log", "CountriesMap", "i18n", "useTranslation", "currentLang", "set<PERSON><PERSON><PERSON><PERSON>", "useState", "setMarkerInfo", "points", "setPoints", "countries", "props", "reset<PERSON><PERSON><PERSON>", "onMarkerClick", "propsinit", "_e", "setPointsFromCountries", "pointsvalue", "data", "_", "push", "country", "_id", "coordinates", "latitude", "longitude", "useEffect", "RKIMAP1", "info", "Props", "a", "href", "MarkerInfo", "length", "item", "index", "url", "defaultProps"], "sourceRoot": "", "ignoreList": []}