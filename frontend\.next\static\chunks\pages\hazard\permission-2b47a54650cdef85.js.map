{"version": 3, "file": "static/chunks/pages/hazard/permission-2b47a54650cdef85.js", "mappings": "gFACA,4CACA,qBACA,WACA,OAAe,EAAQ,KAA0C,CACjE,EACA,SAFsB,mFCDf,IAAMA,EAA0BC,CAAAA,EAAAA,QAAAA,CAAAA,CAAoBA,CAAC,CAC1DC,sBAAuB,KACjBC,EAAMC,WAAW,IAAID,EAAMC,WAAW,CAACC,MAAM,IAAIF,EAAMC,WAAW,CAACC,MAAM,CAAC,WAAW,CAK3FC,CAL6F,kBAKzE,yBACtB,GAAG,EAEYN,uBAAuBA,EAAC", "sources": ["webpack://_N_E/?81bb", "webpack://_N_E/./pages/hazard/permission.tsx"], "sourcesContent": ["\n    (window.__NEXT_P = window.__NEXT_P || []).push([\n      \"/hazard/permission\",\n      function () {\n        return require(\"private-next-pages/hazard/permission.tsx\");\n      }\n    ]);\n    if(module.hot) {\n      module.hot.dispose(function () {\n        window.__NEXT_P.push([\"/hazard/permission\"])\n      });\n    }\n  ", "//Import Library\r\nimport connectedAuthWrapper from 'redux-auth-wrapper/connectedAuthWrapper';\r\n\r\nexport const canViewDiscussionUpdate = connectedAuthWrapper({\r\n  authenticatedSelector: (state) => {\r\n    if (state.permissions && state.permissions.update && state.permissions.update['read:any']) {\r\n      return true;\r\n    }\r\n    return false;\r\n  },\r\n  wrapperDisplayName: 'CanViewDiscussionUpdate',\r\n});\r\n\r\nexport default canViewDiscussionUpdate;"], "names": ["canViewDiscussionUpdate", "connectedAuthWrapper", "authenticatedSelector", "state", "permissions", "update", "wrapperDisplayName"], "sourceRoot": "", "ignoreList": []}