{"version": 3, "file": "static/chunks/pages/project/permission-f81ce61f30decd18.js", "mappings": "oSAOO,IAAMA,EAAgBC,CAAAA,EAAAA,EAAAA,CAAAA,CAAoBA,CAAC,CAChDC,sBAAuB,KACjBC,EAAMC,WAAW,IAAID,EAAMC,WAAW,CAACC,OAAO,IAAIF,EAAMC,WAAW,CAACC,OAAO,CAAC,aAAa,CAK/FC,CALiG,kBAK7E,eACtB,GAAG,EAE8BL,CAAAA,EAAAA,EAAAA,CAAAA,CAAoBA,CAAC,CACpDC,sBAAuB,KACjBC,EAAMC,WAAW,IAAID,EAAMC,WAAW,CAACC,OAAO,IAAIF,EAAMC,WAAW,CAACC,OAAO,CAAC,aAAa,CAK/FC,CALiG,kBAK7E,oBACpBC,iBAAkB,IAAM,UAACC,EAAAA,OAAIA,CAAAA,CAAAA,EAC/B,GAAG,EAE2BP,CAAAA,EAAAA,EAAAA,CAAAA,CAAoBA,CAAC,CACjDC,sBAAuB,CAACC,EAAOM,KAC7B,GAAIN,EAAMC,WAAW,EAAID,EAAMC,WAAW,CAACC,OAAO,EAAE,GAC9CF,EAAMC,WAAW,CAACC,OAAO,CAAC,aAAa,CACzC,CAD2C,KACpC,QAEP,GAAIF,EAAMC,WAAW,CAACC,OAAO,CAAC,aAAa,EAAE,EACjCA,OAAO,EAAII,EAAMJ,OAAO,CAACK,IAAI,EAAID,EAAMJ,OAAO,CAACK,IAAI,CAACC,GAAG,GAAKR,EAAMO,IAAI,CAACC,GAAG,CAClF,CADoF,MAC7E,CAGb,CAEF,OAAO,CACT,EACAL,mBAAoB,gBACtB,GAAG,EAE+BL,CAAAA,EAAAA,EAAAA,CAAAA,CAAoBA,CAAC,CACrDC,sBAAuB,CAACC,EAAOM,KAC7B,GAAIN,EAAMC,WAAW,EAAID,EAAMC,WAAW,CAACC,OAAO,EAAE,GAC9CF,EAAMC,WAAW,CAACC,OAAO,CAAC,aAAa,CACzC,CAD2C,MACpC,OAEP,GAAIF,EAAMC,WAAW,CAACC,OAAO,CAAC,aAAa,EAAE,EACjCA,OAAO,EAAII,EAAMJ,OAAO,CAACK,IAAI,EAAID,EAAMJ,OAAO,CAACK,IAAI,CAACC,GAAG,GAAKR,EAAMO,IAAI,CAACC,GAAG,CAClF,CADoF,MAC7E,CAGb,CAEF,OAAO,CACT,EACAL,mBAAoB,qBACpBC,iBAAkB,IAAM,UAACC,EAAAA,OAAIA,CAAAA,CAAAA,EAC/B,GAEaI,EAA0BX,CAAAA,EAAAA,EAAAA,CAAAA,CAAoBA,CAAC,CAC1DC,sBAAuB,KACjBC,EAAMC,WAAW,IAAID,EAAMC,WAAW,CAACS,MAAM,IAAIV,EAAMC,WAAW,CAACS,MAAM,CAAC,WAAW,CAK3FP,CAL6F,kBAKzE,yBACtB,GAAG,EAEYN,aAAaA,EAAC,IC1E7B,4CACA,sBACA,WACA,OAAe,EAAQ,KAA2C,CAClE,EACA,SAFsB", "sources": ["webpack://_N_E/./pages/project/permission.tsx", "webpack://_N_E/?7baf"], "sourcesContent": ["//Import Library\r\nimport React from 'react';\r\nimport connectedAuthWrapper from 'redux-auth-wrapper/connectedAuthWrapper';\r\n\r\n//Import services/components\r\nimport R403 from \"../r403\";\r\n\r\nexport const canAddProject = connectedAuthWrapper({\r\n  authenticatedSelector: (state) => {\r\n    if (state.permissions && state.permissions.project && state.permissions.project['create:any']) {\r\n      return true;\r\n    }\r\n    return false;\r\n  },\r\n  wrapperDisplayName: 'CanAddProject',\r\n});\r\n\r\nexport const canAddProjectForm = connectedAuthWrapper({\r\n  authenticatedSelector: (state) => {\r\n    if (state.permissions && state.permissions.project && state.permissions.project['create:any']) {\r\n      return true;\r\n    }\r\n    return false;\r\n  },\r\n  wrapperDisplayName: 'CanAddProjectForm',\r\n  FailureComponent: () => <R403/>\r\n});\r\n\r\nexport const canEditProject = connectedAuthWrapper({\r\n  authenticatedSelector: (state, props) => {\r\n    if (state.permissions && state.permissions.project) {\r\n      if (state.permissions.project['update:any']) {\r\n        return true;\r\n      } else { //if update:own\r\n        if (state.permissions.project['update:own']) {\r\n          if (props.project && props.project.user && props.project.user._id === state.user._id) {\r\n            return true;\r\n          }  \r\n        }\r\n      }\r\n    }\r\n    return false;\r\n  },\r\n  wrapperDisplayName: 'CanEditProject',\r\n});\r\n\r\nexport const canEditProjectForm = connectedAuthWrapper({\r\n  authenticatedSelector: (state, props) => {\r\n    if (state.permissions && state.permissions.project) {\r\n      if (state.permissions.project['update:any']) {\r\n        return true;\r\n      } else { //if update:own\r\n        if (state.permissions.project['update:own']) {\r\n          if (props.project && props.project.user && props.project.user._id === state.user._id) {\r\n            return true;\r\n          }\r\n        }\r\n      }\r\n    }\r\n    return false;\r\n  },\r\n  wrapperDisplayName: 'CanEditProjectForm',\r\n  FailureComponent: () => <R403/>\r\n});\r\n\r\nexport const canViewDiscussionUpdate = connectedAuthWrapper({\r\n  authenticatedSelector: (state) => {\r\n    if (state.permissions && state.permissions.update && state.permissions.update['read:any']) {\r\n      return true;\r\n    }\r\n    return false;\r\n  },\r\n  wrapperDisplayName: 'CanViewDiscussionUpdate',\r\n});\r\n\r\nexport default canAddProject;", "\n    (window.__NEXT_P = window.__NEXT_P || []).push([\n      \"/project/permission\",\n      function () {\n        return require(\"private-next-pages/project/permission.tsx\");\n      }\n    ]);\n    if(module.hot) {\n      module.hot.dispose(function () {\n        window.__NEXT_P.push([\"/project/permission\"])\n      });\n    }\n  "], "names": ["canAddProject", "connectedAuthWrapper", "authenticatedSelector", "state", "permissions", "project", "wrapperDisplayName", "FailureComponent", "R403", "props", "user", "_id", "canViewDiscussionUpdate", "update"], "sourceRoot": "", "ignoreList": []}