{"version": 3, "file": "static/chunks/pages/rNoAccess-faa27d84e376cff1.js", "mappings": "0IAAe,SAASA,EAAgBC,CAAW,EAC/C,MACE,UAACC,MAAAA,CAAIC,UAAU,sDACb,UAACD,MAAAA,CAAIC,UAAU,mBAAU,yCAG/B,mBCLF,4CACA,aACA,WACA,OAAe,EAAQ,KAAkC,CACzD,EACA,SAFsB", "sources": ["webpack://_N_E/./pages/rNoAccess.tsx", "webpack://_N_E/?6721"], "sourcesContent": ["export default function NoAccessMessage(_props: any) {\r\n    return (\r\n      <div className=\"container-fluid p-0 response-message-block\">\r\n        <div className=\"message\">you don't have permission to access</div>\r\n      </div>\r\n    )\r\n  }", "\n    (window.__NEXT_P = window.__NEXT_P || []).push([\n      \"/rNoAccess\",\n      function () {\n        return require(\"private-next-pages/rNoAccess.tsx\");\n      }\n    ]);\n    if(module.hot) {\n      module.hot.dispose(function () {\n        window.__NEXT_P.push([\"/rNoAccess\"])\n      });\n    }\n  "], "names": ["NoAccessMessage", "_props", "div", "className"], "sourceRoot": "", "ignoreList": []}