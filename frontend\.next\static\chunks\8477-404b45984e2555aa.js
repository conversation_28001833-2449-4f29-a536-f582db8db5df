"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[8477],{5671:(e,s,t)=>{t.d(s,{x:()=>r});var n=t(37876),i=t(14232);let a=e=>{let{value:s,onChange:t,placeholder:a="Write something...",height:r=300,disabled:l=!1}=e,d=(0,i.useRef)(null),[c,o]=(0,i.useState)(!1);(0,i.useEffect)(()=>{d.current&&1&&!c&&d.current.innerHTML!==s&&(d.current.innerHTML=s||"")},[s,c]);let p=()=>{d.current&&t&&t(d.current.innerHTML)},u=(e,s)=>{if("undefined"!=typeof document){var t;document.execCommand(e,!1,s||""),p(),null==(t=d.current)||t.focus()}};return(0,n.jsx)("div",{className:"simple-rich-text-editor",style:{border:"1px solid #ccc"},children:(0,n.jsxs)(n.Fragment,{children:[(0,n.jsxs)("div",{className:"toolbar",style:{padding:"8px",borderBottom:"1px solid #ccc",background:"#f5f5f5"},children:[(0,n.jsx)("button",{type:"button",onClick:()=>u("bold"),style:{margin:"0 5px",padding:"3px 8px"},children:(0,n.jsx)("strong",{children:"B"})}),(0,n.jsx)("button",{type:"button",onClick:()=>u("italic"),style:{margin:"0 5px",padding:"3px 8px"},children:(0,n.jsx)("em",{children:"I"})}),(0,n.jsx)("button",{type:"button",onClick:()=>u("underline"),style:{margin:"0 5px",padding:"3px 8px"},children:(0,n.jsx)("u",{children:"U"})}),(0,n.jsx)("button",{type:"button",onClick:()=>u("insertOrderedList"),style:{margin:"0 5px",padding:"3px 8px"},children:"OL"}),(0,n.jsx)("button",{type:"button",onClick:()=>u("insertUnorderedList"),style:{margin:"0 5px",padding:"3px 8px"},children:"UL"}),(0,n.jsx)("button",{type:"button",onClick:()=>{let e=prompt("Enter the link URL");e&&u("createLink",e)},style:{margin:"0 5px",padding:"3px 8px"},children:"Link"})]}),(0,n.jsx)("div",{ref:d,contentEditable:!l,onInput:p,onFocus:()=>o(!0),onBlur:()=>o(!1),style:{padding:"15px",minHeight:r,maxHeight:2*r,overflow:"auto",outline:"none"},"data-placeholder":s?"":a,suppressContentEditableWarning:!0})]})})},r=e=>{let{initContent:s,onChange:t}=e;return(0,n.jsx)(a,{value:s||"",onChange:e=>t(e)})}},48477:(e,s,t)=>{t.d(s,{A:()=>N});var n=t(37876),i=t(14232),a=t(10841),r=t.n(a);t(84135);var l=t(37784),d=t(60282),c=t(49589),o=t(56970),p=t(21772),u=t(11041),x=t(82851),m=t.n(x),h=t(53718),j=t(96235);let g=(0,t(8178).A)({authenticatedSelector:e=>!!e.permissions&&!!e.permissions.update&&!!e.permissions.update["create:any"],wrapperDisplayName:"CanAddDiscussionUpdate"});var y=t(29504),f=t(31753);let v=e=>{let[s,t]=(0,i.useState)(""),{t:a}=(0,f.Bd)("common"),r=()=>{c()},c=()=>{e.handleDiscussSubmit(s),t("")};return(0,n.jsx)(y.A,{children:(0,n.jsxs)(o.A,{children:[(0,n.jsx)(l.A,{sm:9,lg:10,children:(0,n.jsx)(y.A.Control,{className:"mb-2",type:"text",value:s,onKeyPress:e=>{"Enter"===e.key&&(r(),e.preventDefault())},onChange:e=>{let{value:s}=e.target;t(s)}})}),(0,n.jsx)(l.A,{sm:3,lg:2,children:(0,n.jsx)(d.A,{onClick:c,children:a("submit")})})]})})};var b=t(5671);let N=e=>{let{t:s,i18n:t}=(0,f.Bd)("common"),a="fr"===t.language?"en":t.language,{updates:x,setUpdates:y}=(0,i.useContext)(j.bM),[N,k]=(0,i.useState)([]),[A,C]=(0,i.useState)(-1),[_,S]=(0,i.useState)(""),[w,L]=(0,i.useState)(""),[B,U]=(0,i.useState)(""),[T]=(0,i.useState)(a),[E,I]=(0,i.useState)(""),z=async()=>{let e=await h.A.get("/updatetype");if(e&&e.data&&e.data.length>0){let s=m().find(e.data,{title:"Conversation"});if(s&&s._id)return U(s._id),s._id}},D=async(t,n)=>{if(n){let s=await h.A.get("/updates",{query:{type:t,update_type:n},sort:{title:"asc"},limit:"~"});s&&s.data&&k(m().filter(s.data,{["parent_".concat(t)]:e.id}))}I(N&&N.length>0?"":s("NoFilesFound!"))};(0,i.useEffect)(()=>{(async()=>{let s=await h.A.post("/users/getLoggedUser",{});s&&s.username&&L(s.username);let t=await z();await D(e.type,t)})()},[]);let H=async s=>{if(s){let t={title:s,reply:[],type:e.type,update_type:B,show_as_announcement:!1,user:{username:w}};switch(e.type){case"operation":t.parent_operation=e.id;break;case"event":t.parent_event=e.id;break;case"project":t.parent_project=e.id;break;case"vspace":t.parent_vspace=e.id;break;case"country":t.parent_country=e.id;break;case"hazard":t.parent_hazard=e.id;break;case"institution":t.parent_institution=e.id}let n=await h.A.post("updates",t);k([...N,n]),localStorage.setItem("discuss",JSON.stringify([...N,t])),y([n,...x])}},F=e=>{S(e)},M=async e=>{N[e].reply.push({user:w,msg:_,time:new Date}),k([...N]),C(-1),S(""),await h.A.patch("/updates/".concat(N[e]._id),N[e]),localStorage.setItem("discuss",JSON.stringify(N))},O=e=>({__html:e}),R=e=>(0,n.jsx)(l.A,{sm:2,children:(0,n.jsx)(d.A,{variant:"secondary",size:"sm",onClick:()=>{C(e.i)},children:s("Reply")})}),J=g(()=>(0,n.jsx)(v,{handleDiscussSubmit:H})),W=g(e=>(0,n.jsx)(R,{i:e.i}));return(0,n.jsxs)(c.A,{fluid:!0,children:[(0,n.jsx)(J,{}),N&&0==N.length?(0,n.jsx)("div",{children:(0,n.jsx)("p",{className:"d-flex d-flex justify-content-center p-2 m-0",children:E})}):(0,n.jsx)(n.Fragment,{children:N.map((e,t)=>e.title?(0,n.jsxs)("div",{className:"discItem",children:[(0,n.jsxs)(o.A,{children:[(0,n.jsx)(l.A,{sm:10,children:(0,n.jsxs)("div",{className:"discThread",children:[(0,n.jsx)("div",{className:"discAvatar",children:(0,n.jsx)(p.g,{icon:u.X46,color:"#fff",size:"lg"})}),(0,n.jsxs)("div",{className:"discBody",children:[(0,n.jsxs)("div",{className:"discUser",children:[(0,n.jsx)("span",{className:"discUserName",children:e&&e.user&&e.user.username})," ","-"," ",(0,n.jsx)("span",{className:"discTime",children:r().utc(e.created_at).locale(T).fromNow()})]}),(0,n.jsx)("div",{className:"discBodyInnner",children:e.title})]})]})}),-1!==A&&t===A?null:(0,n.jsx)(W,{i:t})]}),e.reply&&e.reply.map((e,s)=>(function(e,s,t,i){return(0,n.jsx)("div",{className:"discReply",children:(0,n.jsx)("div",{style:{marginLeft:"55px",marginBottom:"10px",marginTop:"10px"},children:(0,n.jsxs)("div",{className:"discThread",children:[(0,n.jsx)("div",{className:"discAvatar",children:(0,n.jsx)(p.g,{icon:u.X46,color:"#fff",size:"xs"})}),(0,n.jsxs)("div",{className:"discBody",children:[(0,n.jsxs)("div",{className:"discUser",children:[(0,n.jsx)("span",{className:"discUserName",children:s&&s.user})," -"," ",(0,n.jsx)("span",{className:"discTime",children:s.time?r().utc(s.time).locale(t).fromNow():r()().fromNow()})]}),(0,n.jsx)("div",{className:"discBodyInnner",children:(0,n.jsx)("div",{dangerouslySetInnerHTML:i(s.msg)})})]})]})})},e)})(s,e,T,O)),-1!==A&&t===A?(0,n.jsxs)("div",{children:[(0,n.jsx)(b.x,{initContent:_,onChange:e=>F(e)})," ",(0,n.jsx)("br",{}),(0,n.jsx)(d.A,{disabled:!_.length,size:"sm",variant:"info",onClick:()=>M(t),children:s("Send")})," ","\xa0",(0,n.jsx)(d.A,{variant:"secondary",size:"sm",onClick:()=>{C(-1)},children:s("Cancel")})]}):null]},t):null)})]})}}}]);
//# sourceMappingURL=8477-404b45984e2555aa.js.map