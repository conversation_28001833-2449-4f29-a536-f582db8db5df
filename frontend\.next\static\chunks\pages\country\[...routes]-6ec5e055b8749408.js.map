{"version": 3, "file": "static/chunks/pages/country/[...routes]-6ec5e055b8749408.js", "mappings": "iLA2BA,MApBe,KAEb,IAAMA,EADSC,CAAAA,EAAAA,EAAAA,SAAAA,CAASA,EAmBXC,CAlBaC,KAAK,CAACH,CAkBZ,KAlBkB,EAAI,EAAE,OAE5C,SAAQA,CAAM,CAAC,EAAE,CAEL,UAACI,EAAAA,OAAWA,CAAAA,CAACJ,OAAQA,IAEtB,IAEb,mBChBA,4CACA,uBACA,WACA,OAAe,EAAQ,KAA4C,CACnE,EACA,SAFsB", "sources": ["webpack://_N_E/./pages/country/[...routes].tsx", "webpack://_N_E/?254d"], "sourcesContent": ["//Import Library\r\nimport { useRouter } from 'next/router';\r\n\r\n//Import services/components\r\nimport CountryShow from './CountryShow';\r\nimport { serverSideTranslations } from 'next-i18next/serverSideTranslations';\r\n\r\nconst Router = () => {\r\n  const router = useRouter()\r\n  const routes:any = router.query.routes || []\r\n\r\n  switch (routes[0]) {\r\n    case 'show':\r\n      return (<CountryShow routes={routes} />)\r\n    default:\r\n      return null;\r\n  }\r\n}\r\n\r\nexport async function getServerSideProps({ locale }: { locale: string }) {\r\n  return {\r\n    props: {\r\n      ...(await serverSideTranslations(locale, ['common'])),\r\n    },\r\n  }\r\n}\r\n\r\nexport default Router;\r\n", "\n    (window.__NEXT_P = window.__NEXT_P || []).push([\n      \"/country/[...routes]\",\n      function () {\n        return require(\"private-next-pages/country/[...routes].tsx\");\n      }\n    ]);\n    if(module.hot) {\n      module.hot.dispose(function () {\n        window.__NEXT_P.push([\"/country/[...routes]\"])\n      });\n    }\n  "], "names": ["routes", "useRouter", "Router", "query", "CountryShow"], "sourceRoot": "", "ignoreList": []}