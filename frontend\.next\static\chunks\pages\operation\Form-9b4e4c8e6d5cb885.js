(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[6364],{5671:(n,e,t)=>{"use strict";t.d(e,{x:()=>l});var r=t(37876),i=t(14232);let o=n=>{let{value:e,onChange:t,placeholder:o="Write something...",height:l=300,disabled:d=!1}=n,s=(0,i.useRef)(null),[p,c]=(0,i.useState)(!1);(0,i.useEffect)(()=>{s.current&&1&&!p&&s.current.innerHTML!==e&&(s.current.innerHTML=e||"")},[e,p]);let u=()=>{s.current&&t&&t(s.current.innerHTML)},a=(n,e)=>{if("undefined"!=typeof document){var t;document.execCommand(n,!1,e||""),u(),null==(t=s.current)||t.focus()}};return(0,r.jsx)("div",{className:"simple-rich-text-editor",style:{border:"1px solid #ccc"},children:(0,r.jsxs)(r.Fragment,{children:[(0,r.jsxs)("div",{className:"toolbar",style:{padding:"8px",borderBottom:"1px solid #ccc",background:"#f5f5f5"},children:[(0,r.jsx)("button",{type:"button",onClick:()=>a("bold"),style:{margin:"0 5px",padding:"3px 8px"},children:(0,r.jsx)("strong",{children:"B"})}),(0,r.jsx)("button",{type:"button",onClick:()=>a("italic"),style:{margin:"0 5px",padding:"3px 8px"},children:(0,r.jsx)("em",{children:"I"})}),(0,r.jsx)("button",{type:"button",onClick:()=>a("underline"),style:{margin:"0 5px",padding:"3px 8px"},children:(0,r.jsx)("u",{children:"U"})}),(0,r.jsx)("button",{type:"button",onClick:()=>a("insertOrderedList"),style:{margin:"0 5px",padding:"3px 8px"},children:"OL"}),(0,r.jsx)("button",{type:"button",onClick:()=>a("insertUnorderedList"),style:{margin:"0 5px",padding:"3px 8px"},children:"UL"}),(0,r.jsx)("button",{type:"button",onClick:()=>{let n=prompt("Enter the link URL");n&&a("createLink",n)},style:{margin:"0 5px",padding:"3px 8px"},children:"Link"})]}),(0,r.jsx)("div",{ref:s,contentEditable:!d,onInput:u,onFocus:()=>c(!0),onBlur:()=>c(!1),style:{padding:"15px",minHeight:l,maxHeight:2*l,overflow:"auto",outline:"none"},"data-placeholder":e?"":o,suppressContentEditableWarning:!0})]})})},l=n=>{let{initContent:e,onChange:t}=n;return(0,r.jsx)(o,{value:e||"",onChange:n=>t(n)})}},56032:(n,e,t)=>{(window.__NEXT_P=window.__NEXT_P||[]).push(["/operation/Form",function(){return t(16331)}])}},n=>{var e=e=>n(n.s=e);n.O(0,[7725,1121,6701,1772,698,7336,8220,5939,6331,636,6593,8792],()=>e(56032)),_N_E=n.O()}]);
//# sourceMappingURL=Form-9b4e4c8e6d5cb885.js.map