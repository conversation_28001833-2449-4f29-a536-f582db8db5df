{"version": 3, "file": "static/chunks/pages/people/peopleTableFilter-16eea01de13c7a37.js", "mappings": "+EACA,4CACA,4BACA,WACA,OAAe,EAAQ,KAAiD,CACxE,EACA,SAFsB,gJCoDtB,MA/C0B,OAAC,YAAEA,CAAU,QA+CxBC,EA/C0BC,CAAQ,SAAEC,CAAO,IA+C1BF,EAAC,CA/C2BG,CAAK,CAAEC,gBAAc,cAAEC,CAAY,YAAEC,CAAU,CAA0H,GAC3N,GAAEC,CAAC,CAAE,CAAGC,CAAAA,EAAAA,EAAAA,EAAAA,CAAcA,CAAC,UACvB,CAACC,EAAMC,EAAQ,CAAGC,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAC,EAAE,EAC7B,EAAGC,EAAW,CAAGD,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,EAAC,GAE1BE,EAAa,CACfC,KAAM,CAAEC,WAAY,MAAO,EAC3BC,MAAO,IACPC,KAAM,EACNC,MAAO,CAAC,EACRC,OAAQ,+dACZ,EAEMC,EAAc,UAChBR,GAAW,GACX,IAAMS,EAAW,MAAMC,EAAAA,CAAUA,CAACC,GAAG,CAAC,SAAUV,GAC5CQ,GAAYG,MAAMC,OAAO,CAACJ,EAASK,IAAI,GAAG,CAI1ChB,EAHeW,EAASK,IAAI,CAACC,GAAG,CAAC,CAACC,EAAWC,KAClC,CAAEC,MAAOF,EAAKG,QAAQ,CAAEC,MAAOJ,EAAKK,GAAG,CAAC,IAGnDrB,GAAW,GAEnB,EAKA,MAHAsB,CAAAA,EAAAA,EAAAA,SAAAA,CAASA,CAAC,KACNd,GACJ,EAAG,EAAE,EAED,UAACe,EAAAA,CAASA,CAAAA,CAACC,KAAK,IAACC,UAAU,eACvB,UAACC,EAAAA,CAAGA,CAAAA,UACA,UAACC,EAAAA,CAAGA,CAAAA,CAACC,GAAI,GAAIC,GAAI,EAAGC,GAAI,EAAGL,UAAU,oBACjC,UAACM,EAAAA,EAAMA,CAAAA,CACHC,WAAW,EACXC,aAAa,EACbC,cAAc,EACdC,UAAWzC,EACX0C,SAAU/C,EACVgD,YAAa1C,EAAE,+BACf2C,QAASzC,SAMjC", "sources": ["webpack://_N_E/?2dec", "webpack://_N_E/./pages/people/peopleTableFilter.tsx"], "sourcesContent": ["\n    (window.__NEXT_P = window.__NEXT_P || []).push([\n      \"/people/peopleTableFilter\",\n      function () {\n        return require(\"private-next-pages/people/peopleTableFilter.tsx\");\n      }\n    ]);\n    if(module.hot) {\n      module.hot.dispose(function () {\n        window.__NEXT_P.push([\"/people/peopleTableFilter\"])\n      });\n    }\n  ", "//Import Library\r\nimport { Col, Container, Row } from \"react-bootstrap\";\r\nimport Select from \"react-select\";\r\nimport { useEffect, useState } from \"react\";\r\n\r\n//Import services/components\r\nimport apiService from \"../../services/apiService\";\r\nimport { useTranslation } from 'next-i18next';\r\n\r\nconst PeopleTableFilter = ({ filterText, onFilter, onClear, roles, onHandleSearch, institutions, onKeyPress } : { filterText: any, onFilter: any, onClear: any, roles: any, onHandleSearch: any, institutions: any, onKeyPress: any }) => {\r\n    const { t } = useTranslation('common');\r\n    const [user, setUser] = useState([]);\r\n    const [, setLoading] = useState(false);\r\n\r\n    const userParams = {\r\n        sort: { created_at: \"desc\" },\r\n        limit: \"~\",\r\n        page: 1,\r\n        query: {},\r\n        select: \"-country -dial_code -firstname -region -institution.address -institution.contact_name -institution.description -institution.dial_code -institution.document -institution.doc_src -institution.images -institution.images_src -institution.expertise -institution.hazards -institution.hazard_types -institution.header -institution.networks -institution.partners -institution.telephone -institution.twitter -institution.type -institution.unit -institution.user -mobile_number -position\",\r\n    };\r\n\r\n    const getUserData = async () => {\r\n        setLoading(true);\r\n        const response = await apiService.get(\"/users\", userParams);\r\n        if (response && Array.isArray(response.data)) {\r\n            const _users = response.data.map((item: any, _i: any) => {\r\n                return { label: item.username, value: item._id };\r\n            });\r\n            setUser(_users);\r\n            setLoading(false);\r\n        }\r\n    };\r\n\r\n    useEffect(() => {\r\n        getUserData();\r\n    }, []);\r\n    return (\r\n        <Container fluid className=\"p-0\">\r\n            <Row>\r\n                <Col xs={12} md={6} lg={4} className=\"p-0 me-3\">\r\n                    <Select\r\n                        autoFocus={true}\r\n                        isClearable={true}\r\n                        isSearchable={true}\r\n                        onKeyDown={onKeyPress}\r\n                        onChange={onFilter}\r\n                        placeholder={t(\"People.form.UsernameorEmail\")}\r\n                        options={user}\r\n                    />\r\n                </Col>\r\n            </Row>\r\n        </Container>\r\n    );\r\n};\r\n\r\nexport default PeopleTableFilter;\r\n"], "names": ["filterText", "PeopleTableFilter", "onFilter", "onClear", "roles", "onHandleSearch", "institutions", "onKeyPress", "t", "useTranslation", "user", "setUser", "useState", "setLoading", "userParams", "sort", "created_at", "limit", "page", "query", "select", "getUserData", "response", "apiService", "get", "Array", "isArray", "data", "map", "item", "_i", "label", "username", "value", "_id", "useEffect", "Container", "fluid", "className", "Row", "Col", "xs", "md", "lg", "Select", "autoFocus", "isClearable", "isSearchable", "onKeyDown", "onChange", "placeholder", "options"], "sourceRoot": "", "ignoreList": []}