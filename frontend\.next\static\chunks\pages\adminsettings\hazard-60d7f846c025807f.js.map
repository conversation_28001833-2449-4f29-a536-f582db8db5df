{"version": 3, "file": "static/chunks/pages/adminsettings/hazard-60d7f846c025807f.js", "mappings": "gMA4BA,MAtB0B,OAAC,YAAEA,CAAU,QAsBxBC,EAtB0BC,CAAQ,SAAEC,CAAO,CAAM,GACxD,EAqByB,CArBvBC,CAAC,CAAE,CAAGC,CAAAA,EAAAA,EAAAA,EAAAA,CAAcA,CAAC,UAG7B,MACE,UAACC,EAAAA,CAASA,CAAAA,CAACC,KAAK,IAACC,UAAU,eACzB,UAACC,EAAAA,CAAGA,CAAAA,UACF,UAACC,EAAAA,CAAGA,CAAAA,CAACC,GAAI,EAAGH,UAAU,eACpB,UAACI,EAAAA,CAAWA,CAAAA,CACVC,KAAK,OACLL,UAAU,cACVM,YAAaV,EAAE,8BACfW,aAAW,SACXC,MAAOhB,EACPiB,SAAUf,SAMtB,uNCmLA,MAhMoB,KAChB,GAAM,GAAEE,CAAC,CAAEc,MAAI,CAAE,CAAGb,CAAAA,EAAAA,EAAAA,EAAAA,CAAcA,CAAC,SA+Lbc,CA9LhBC,CA8LiB,CA9Le,OAAlBF,EAAKG,QAAQ,CAAY,KAAOH,EAAKG,QAAQ,CAG3D,CAACC,EAAWC,EAAe,CAAGC,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAQ,EAAE,EAChD,EAAGC,EAAW,CAAGD,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAC,IAC1B,CAACE,EAAWC,EAAa,CAAGH,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAC,GACrC,CAACI,EAASC,EAAW,CAAGL,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAC,IACjC,CAACxB,EAAY8B,EAAc,CAAGC,EAAAA,QAAc,CAAC,IAC7C,CAACC,EAAuBC,EAAyB,CAAGF,EAAAA,QAAc,EAAC,GACnE,CAACG,EAAaC,EAAS,CAAGX,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,EAAC,GACnC,CAACY,EAAcC,EAAgB,CAAGb,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAC,CAAC,GAC5C,CAACc,EAAS,CAAGd,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAVPJ,EAAc,SAAqB,OAAZA,GAAgB,YAarDmB,EAAkB,MAAOC,IAC3B,IAAMC,EAAQC,IAAAA,SAAW,CAACpB,EAAW,CAAEqB,IAAKH,EAAEI,MAAM,CAACC,IAAI,GACzD,GAAIJ,EAAQ,CAAC,EAAG,CACZnB,CAAS,CAACmB,EAAM,CAACK,OAAO,CAAG,CAACxB,CAAS,CAACmB,EAAM,CAACK,OAAO,CACpDvB,EAAe,IAAID,EAAU,EAC7B,IAAMyB,EAAW,MAAMC,EAAAA,CAAUA,CAACC,KAAK,CAAC,WAAyB,OAAdT,EAAEI,MAAM,CAACC,IAAI,EAAIvB,CAAS,CAACmB,EAAM,EAChFM,GAAYA,EAASJ,GAAG,CACxBO,CAD0B,CAC1BA,EAAKA,CAACC,OAAO,CAAC,GAAkC/C,MAAAA,CAA/B2C,EAASK,KAAK,CAAChC,EAAY,CAAC,KAA4B,OAAzBhB,EAAE,yBAElD8C,EAAAA,EAAKA,CAACG,KAAK,CAACN,EAEpB,MACIG,CADG,CACHA,EAAKA,CAACG,KAAK,CAACjD,EAAE,iBAEtB,EAEMkD,EAAS,OAAC,KAAEX,CAAG,SAAEG,CAAO,CAA8B,SACxD,UAACS,EAAAA,CAAIA,CAACC,KAAK,EACPhD,UAAU,OACVK,KAAK,SACLgC,KAAMF,EACNc,GAAId,EACJe,MAAM,GACNC,QAASb,EACT7B,SAAWuB,GAAMD,EAAgBC,MAGnCoB,EAAU,CACZ,CACIf,KAAMzC,EAAE,gBACRyD,SAAUvB,EACVwB,KAAM,GAAaC,GAAKA,EAAEX,KAAK,EAAIW,EAAEX,KAAK,CAAChC,EAAY,CAAG2C,EAAEX,KAAK,CAAChC,EAAY,CAAG,EACrF,EACA,CACIyB,KAAMzC,EAAE,cACRyD,SAAU,cACVC,KAAOC,GAAYA,GAAKA,EAAEC,WAAW,EAAID,EAAEC,WAAW,CAACZ,KAAK,CAAGW,EAAEC,WAAW,CAACZ,KAAK,CAAG,EACzF,EACA,CACIP,KAAMzC,EAAE,aACRyD,SAAU,UACVC,KAAM,GAAc,UAACR,EAAAA,CAAQ,GAAGW,CAAG,EACvC,EACA,CACIpB,KAAMzC,EAAE,UACRyD,SAAU,GACVC,KAAM,GACF,WAACI,MAAAA,WACG,UAACC,IAAIA,CAACC,KAAK,6BAA6BC,GAAI,OAAvCF,uBAA2E,OAANJ,EAAEpB,GAAG,WAE3E,UAAC2B,IAAAA,CAAE9D,UAAU,uBAEV,OAEP,UAAC+D,OAAAA,CAAKC,QAAS,IAAMC,EAAWV,GAAIW,MAAO,CAAEC,OAAQ,SAAU,WAC3D,UAACL,IAAAA,CAAE9D,UAAU,8BAI7B,EACH,CAEDoE,CAAAA,EAAAA,EAAAA,SAAAA,CAASA,CAAC,KACNC,GACJ,EAAG,EAAE,EAEL,IAAMC,EAAe,CACjBC,KAAM,CAAE,CAACzC,EAAS,CAAE,KAAM,EAC1B0C,MAAOpD,EACPqD,KAAM,EACNC,MAAO,CAAC,CACZ,EAEML,EAAiB,UACnBpD,EAAW,IACX,IAAMsB,EAAW,MAAMC,EAAAA,CAAUA,CAACmC,GAAG,CAAC,UAAWL,GAC7C/B,GAAYA,EAASqC,IAAI,EAAIrC,EAASqC,IAAI,CAACC,MAAM,CAAG,GAAG,CACvD9D,EAAewB,EAASqC,IAAI,EAC5BzD,EAAaoB,EAASuC,UAAU,EAChC7D,GAAW,GAEnB,EAQM8D,EAAsB,MAAOC,EAAiBP,KAChDH,EAAaE,KAAK,CAAGQ,EACrBV,EAAaG,IAAI,CAAGA,EACpBxD,GAAW,GACX,IAAMsB,EAAW,MAAMC,EAAAA,CAAUA,CAACmC,GAAG,CAAC,UAAWL,GAC7C/B,GAAYA,EAASqC,IAAI,EAAIrC,EAASqC,IAAI,CAACC,MAAM,CAAG,GAAG,CACvD9D,EAAewB,EAASqC,IAAI,EAC5BvD,EAAW2D,GACX/D,GAAW,GAEnB,EAEMgD,EAAa,MAAOR,IACtB5B,EAAgB4B,EAAItB,GAAG,EACvBR,GAAS,EACb,EAEMsD,EAAe,UACjB,GAAI,CACA,MAAMzC,EAAAA,CAAUA,CAAC0C,MAAM,CAAC,WAAwB,OAAbtD,IACnCyC,IACA1C,GAAS,GACTe,EAAAA,EAAKA,CAACC,OAAO,CAAC/C,EAAE,uDACpB,CAAE,MAAOiD,EAAO,CACZH,EAAAA,EAAKA,CAACG,KAAK,CAACjD,EAAE,iDAClB,CACJ,EAEMuF,EAAY,IAAMxD,GAAS,GAE3ByD,EAAyB7D,EAAAA,OAAa,CAAC,KAQzC,IAAM8D,EAAaC,IACXA,GAAG,CACHhB,EAAaI,KAAK,CAAG,CAAE,CAAC5C,EAAS,CAAEwD,CAAE,GAEzCjB,GACJ,EAEMkB,EAAoBrD,IAAAA,QAAU,CAAC,GAAemD,EAAUC,GAAIE,OAAOC,KAAgC,GAAK,KAO9G,MAAO,UAAChG,EAAAA,OAAiBA,CAAAA,CAACC,SALL,CAKegG,GAJhCpE,EAAcU,EAAEI,MAAM,CAAC5B,KAAK,EAC5B+E,EAAkBvD,EAAEI,MAAM,CAAC5B,KAAK,CACpC,EAEkDb,QArB9B,CAqBuCgG,IApBnDnG,IACAiC,EAAyB,CAACD,GAC1BF,EAAc,IAEtB,EAgBwE9B,WAAYA,GACxF,EAAG,CAACA,EAAW,EAEf,MACI,WAACkE,MAAAA,WACG,WAACkC,EAAAA,CAAKA,CAAAA,CAACC,KAAMnE,EAAaoE,OAAQX,YAC9B,UAACS,EAAAA,CAAKA,CAACG,MAAM,EAACC,WAAW,aACrB,UAACJ,EAAAA,CAAKA,CAACK,KAAK,WAAErG,EAAE,oBAEpB,UAACgG,EAAAA,CAAKA,CAACM,IAAI,WAAEtG,EAAE,sCACf,WAACgG,EAAAA,CAAKA,CAACO,MAAM,YACT,UAACC,EAAAA,CAAMA,CAAAA,CAACC,QAAQ,YAAYrC,QAASmB,WAChCvF,EAAE,YAEP,UAACwG,EAAAA,CAAMA,CAAAA,CAACC,QAAQ,UAAUrC,QAASiB,WAC9BrF,EAAE,eAKf,UAAC0G,EAAAA,CAAQA,CAAAA,CACLlD,QAASA,EACTwB,KAAM9D,EACNI,UAAWA,EACXqF,UAAW,GACXC,SAAS,IACThF,sBAAuBA,EACvBiF,mBAAoBrB,EACpBL,oBAAqBA,EACrB2B,iBAvFcjC,CAuFIiC,GAtF1BpC,EAAaE,KAAK,CAAGpD,EACrBkD,EAAaG,IAAI,CAAGA,EACpBJ,GACJ,MAuFJ,6mBCxMA,IAAMsC,EAAS,aACFC,EAAmBC,CAAAA,EAAAA,EAAAA,CAAAA,CAAoBA,CAAC,CACnDC,sBAAuB,KACjBC,EAAMC,WAAW,IAAID,EAAMC,WAAW,CAACC,YAAY,IAAIF,EAAMC,WAAW,CAACC,YAAY,CAACN,EAAO,CAKnGO,CALqG,kBAKjF,kBACtB,GAAG,EAE0BL,CAAAA,EAAAA,EAAAA,CAAAA,CAAoBA,CAAC,CAChDC,sBAAuB,KACjBC,EAAMC,WAAW,IAAID,EAAMC,WAAW,CAACG,OAAO,IAAIJ,EAAMC,WAAW,CAACG,OAAO,CAACR,EAAO,CAKzFO,CAL2F,kBAKvE,eACtB,GAAG,EAEmCL,CAAAA,EAAAA,EAAAA,CAAAA,CAAoBA,CAAC,CACzDC,sBAAuB,KACjBC,EAAMC,WAAW,IAAID,EAAMC,WAAW,CAACI,iBAAiB,IAAIL,EAAMC,WAAW,CAACI,iBAAiB,CAACT,EAAO,CAK7GO,CAL+G,kBAK3F,wBACtB,GAAG,EAE8BL,CAAAA,EAAAA,EAAAA,CAAAA,CAAoBA,CAAC,CACpDC,sBAAuB,KACjBC,EAAMC,WAAW,IAAID,EAAMC,WAAW,CAACK,YAAY,IAAIN,EAAMC,WAAW,CAACK,YAAY,CAACV,EAAO,CAKnGO,CALqG,kBAKjF,mBACtB,GAAG,EAE4BL,CAAAA,EAAAA,EAAAA,CAAAA,CAAoBA,CAAC,CAClDC,sBAAuB,KACjBC,EAAMC,WAAW,IAAID,EAAMC,WAAW,CAACM,SAAS,IAAIP,EAAMC,WAAW,CAACM,SAAS,CAACX,EAAO,CAK7FO,CAL+F,kBAK3E,iBACtB,GAAG,EAEqCL,CAAAA,EAAAA,EAAAA,CAAAA,CAAoBA,CAAC,CAC3DC,sBAAuB,GACjBC,IAAMC,WAAW,IAAID,EAAMC,WAAW,CAACO,uBAAuB,IAAIR,EAAMC,WAAW,CAACO,uBAAuB,CAACZ,EAAO,CAKzHO,CAL2H,kBAKvG,0BACtB,GAAG,EAEiCL,CAAAA,EAAAA,EAAAA,CAAAA,CAAoBA,CAAC,CACvDC,sBAAuB,KACjBC,EAAMC,WAAW,IAAID,EAAMC,WAAW,CAACO,uBAAuB,IAAIR,EAAMC,WAAW,CAACO,uBAAuB,CAACZ,EAAO,CAKzHO,CAL2H,kBAKvG,sBACtB,GACaM,EAAgBX,CAAAA,EAAAA,EAAAA,CAAAA,CAAoBA,CAAC,CAChDC,sBAAuB,KACjBC,EAAMC,WAAW,IAAID,EAAMC,WAAW,CAACS,MAAM,IAAIV,EAAMC,WAAW,CAACS,MAAM,CAACd,EAAO,CAKvFO,CALyF,kBAKrE,eACtB,GAAG,EAE8BL,CAAAA,EAAAA,EAAAA,CAAAA,CAAoBA,CAAC,CACpDC,sBAAuB,KACjBC,EAAMC,WAAW,IAAID,EAAMC,WAAW,CAACxD,WAAW,IAAIuD,EAAMC,WAAW,CAACxD,WAAW,CAACmD,EAAO,CAKjGO,CALmG,kBAK/E,mBACtB,GAAG,EAEuCL,CAAAA,EAAAA,EAAAA,CAAAA,CAAoBA,CAAC,CAC7DC,sBAAuB,KACjBC,EAAMC,WAAW,IAAID,EAAMC,WAAW,CAACU,WAAW,IAAIX,EAAMC,WAAW,CAACU,WAAW,CAACf,EAAO,CAKjGO,CALmG,kBAK/E,4BACtB,GAAG,EAEuCL,CAAAA,EAAAA,EAAAA,CAAAA,CAAoBA,CAAC,CAC7DC,sBAAuB,KACjBC,EAAMC,WAAW,IAAID,EAAMC,WAAW,CAACW,mBAAmB,IAAIZ,EAAMC,WAAW,CAACW,mBAAmB,CAAChB,EAAO,CAKjHO,CALmH,kBAK/F,4BACtB,GAAG,EAEoCL,CAAAA,EAAAA,EAAAA,CAAAA,CAAoBA,CAAC,CAC1DC,sBAAuB,KACjBC,EAAMC,WAAW,IAAID,EAAMC,WAAW,CAACY,gBAAgB,IAAIb,EAAMC,WAAW,CAACY,gBAAgB,CAACjB,EAAO,CAK3GO,CAL6G,kBAKzF,yBACtB,GAEaW,EAAwBhB,CAAAA,EAAAA,EAAAA,CAAAA,CAAoBA,CAAC,CACxDC,sBAAuB,KACjBC,EAAMC,WAAW,IAAID,EAAMC,WAAW,CAACc,gBAAgB,IAAIf,EAAMC,WAAW,CAACc,gBAAgB,CAACnB,EAAO,CAK3GO,CAL6G,kBAKzF,uBACtB,GAAG,EAEgCL,CAAAA,EAAAA,EAAAA,CAAAA,CAAoBA,CAAC,CACtDC,sBAAuB,KACjBC,EAAMC,WAAW,IAAID,EAAMC,WAAW,CAACe,cAAc,IAAIhB,EAAMC,WAAW,CAACe,cAAc,CAACpB,EAAO,CAKvGO,CALyG,kBAKrF,qBACtB,GAAG,EAE0BL,CAAAA,EAAAA,EAAAA,CAAAA,CAAoBA,CAAC,CAChDC,sBAAuB,KACjBC,EAAMC,WAAW,IAAID,EAAMC,WAAW,CAACgB,MAAM,IAAIjB,EAAMC,WAAW,CAACgB,MAAM,CAACrB,EAAO,CAKvFO,CALyF,kBAKrE,eACtB,GAAG,EAE6BL,CAAAA,EAAAA,EAAAA,CAAAA,CAAoBA,CAAC,CACnDC,sBAAuB,KACjBC,EAAMC,WAAW,IAAID,EAAMC,WAAW,CAACiB,UAAU,IAAIlB,EAAMC,WAAW,CAACiB,UAAU,CAACtB,EAAO,CAK/FO,CALiG,kBAK7E,kBACtB,GAAG,EAE4BL,CAAAA,EAAAA,EAAAA,CAAAA,CAAoBA,CAAC,CAClDC,sBAAuB,KACjBC,EAAMC,WAAW,IAAID,EAAMC,WAAW,CAACkB,QAAQ,IAAInB,EAAMC,WAAW,CAACkB,QAAQ,CAACvB,EAAO,CAK3FO,CAL6F,kBAKzE,iBACtB,GAAG,EAE8BL,CAAAA,EAAAA,EAAAA,CAAAA,CAAoBA,CAAC,CACpDC,sBAAuB,KACjBC,EAAMC,WAAW,IAAID,EAAMC,WAAW,CAACmB,WAAW,IAAIpB,EAAMC,WAAW,CAACmB,WAAW,CAACxB,EAAO,CAKjGO,CALmG,kBAK/E,mBACtB,GAAG,EAEwBL,CAAAA,EAAAA,EAAAA,CAAAA,CAAoBA,CAAC,CAC9CC,sBAAuB,KACjBC,EAAMC,WAAW,IAAID,EAAMC,WAAW,CAACoB,KAAK,IAAIrB,EAAMC,WAAW,CAACoB,KAAK,CAACzB,EAAO,CAKrFO,CALuF,kBAKnE,aACtB,GAEamB,EAAoBxB,CAAAA,EAAAA,EAAAA,CAAAA,CAAoBA,CAAC,CACpDC,sBAAuB,KACjBC,EAAMC,WAAW,IAAID,EAAMC,WAAW,CAACsB,WAAW,IAAIvB,EAAMC,WAAW,CAACsB,WAAW,CAAC3B,EAAO,CAKjGO,CALmG,kBAK/E,mBACtB,GAAG,EAE8BL,CAAAA,EAAAA,EAAAA,CAAAA,CAAoBA,CAAC,CACpDC,sBAAuB,KACjBC,EAAMC,WAAW,IAAID,EAAMC,WAAW,CAACuB,YAAY,IAAIxB,EAAMC,WAAW,CAACuB,YAAY,CAAC5B,EAAO,CAKnGO,CALqG,kBAKjF,mBACtB,GAAG,EAE0BL,CAAAA,EAAAA,EAAAA,CAAAA,CAAoBA,CAAC,CAChDC,sBAAuB,GACrB,EAAIC,EAAMC,WAAW,IAAID,EAAMC,WAAW,CAACwB,SAAS,IAAIzB,EAAMC,WAAW,CAACwB,SAAS,CAAC7B,EAAO,IAAII,EAAMC,WAAW,CAACyB,OAAO,IAAI1B,EAAMC,WAAW,CAACyB,OAAO,CAAC9B,EAAO,IAAGI,EAAMC,WAAW,CAAC0B,KAAK,IAAI3B,EAAMC,WAAW,CAAC0B,KAAK,CAAC/B,EAAO,IAAGI,EAAMC,WAAW,CAAC2B,MAAM,IAAI5B,EAAMC,WAAW,CAAC2B,MAAM,CAAChC,EAAO,IAAGI,EAAMC,WAAW,CAACU,WAAW,IAAIX,EAAMC,WAAW,CAACU,WAAW,CAACf,EAAO,IAAGI,EAAMC,WAAW,CAAC4B,MAAM,IAAI7B,EAAMC,WAAW,CAAC4B,MAAM,CAACjC,EAAO,EAAE,CAG5Z,EAETO,mBAAoB,eACtB,GAAG,EAEYN,gBAAgBA,EAAC,2FC1LhC,SAASN,EAASuC,CAAoB,EACpC,GAAM,GAAEjJ,CAAC,CAAE,CAAGC,CAAAA,EAAAA,EAAAA,EAAAA,CAAcA,CAAC,UACvBiJ,EAA6B,CACjCC,gBAAiBnJ,EAAE,cACnB,EACI,CACJwD,SAAO,MACPwB,CAAI,WACJ1D,CAAS,uBACTM,CAAqB,WACrBgF,CAAS,oBACTC,CAAkB,qBAClB1B,CAAmB,CACnB2B,kBAAgB,aAChBsC,CAAW,oBACXC,CAAkB,gBAClBC,CAAc,SACdC,CAAO,CACP5C,WAAS,sBACT6C,CAAoB,mBACpBC,CAAiB,YACjBC,CAAU,QACVC,CAAM,kBACNC,CAAgB,cAChBC,CAAY,CAEZ,CADA,EACGC,EACJ,CAAGb,EAGEc,EAAiB,4BACrBb,EACAc,gBAAiBhK,EAAE,IAP0C,MAQ7DiK,UAAU,EACVzG,UACAwB,KAAMA,GAAQ,EAAE,CAChBkF,OAAO,EACPC,2BAA4BvI,EAC5BwI,UAAWxD,EACXyD,gBAAiBd,qBACjB1C,EACAyD,YAAY,EACZC,iBAAkB5D,EAClB6D,kBAAmBnB,GAA0C,GAC7DoB,eADwCpB,cACVD,GAA4B,CAAC,GAAI,GAAI,GAAI,CAA3BA,EAA+B,GAAG,CAC9EsB,oBAAqBpJ,EACrBqJ,oBAAqBxF,EACrByF,aAAc9D,iBACdwC,uBACAE,oBACAC,EACAoB,kBAAmB,UAACC,EAAAA,CAAYA,CAAAA,CAAAA,GAChCC,SAAU,UAAC7G,IAAAA,CAAE9D,UAAU,6CACvBsJ,SACAC,eACAE,mBACAD,EACAxJ,UAAW,WACb,EACA,MACE,UAAC4K,EAAAA,EAASA,CAAAA,CAAE,GAAGjB,CAAc,EAEjC,CAEArD,EAASuE,YAAY,CAAG,CACtBb,WAAW,EACXE,YAAY,EACZhJ,UAAW,KACXqF,WAAW,EACX6C,qBAAsB,KACtBC,mBAAmB,EACnBC,YAAY,EACZE,iBAAkB,EACpB,EAEA,MAAelD,QAAQA,EAAC,mEChHT,SAASwE,EAAgBC,CAAW,EAC/C,MACE,UAACrH,MAAAA,CAAI1D,UAAU,sDACb,UAAC0D,MAAAA,CAAI1D,UAAU,mBAAU,yCAG/B,mBCLF,4CACA,wBACA,WACA,OAAe,EAAQ,KAAmD,CAC1E,EACA,SAFsB,uDCAP,SAASgL,EAAYnC,CAAuB,EACzD,MACE,UAACoC,KAAAA,CAAGjL,UAAU,wBAAgB6I,EAAMjG,KAAK,EAE7C,kOCsDA,MAjDoB,QAiCZmE,EAAAA,EAhCN,GAAM,GAAEnH,CAAC,CAAE,CAAGC,CAAAA,EAAAA,EAAAA,EAAAA,CAAcA,CAAC,SAgDLqL,CA/ClBC,CA+CmB,CA/CD,IAEpB,WAACrL,EAAAA,CAASA,CAAAA,CAACoE,MAAO,CAAEkH,UAAW,QAAS,EAAGrL,KAAK,IAACC,UAAU,gBACzD,UAACC,EAAAA,CAAGA,CAAAA,UACF,UAACC,EAAAA,CAAGA,CAAAA,CAACmL,GAAI,YACP,UAACL,EAAAA,CAAWA,CAAAA,CAACpI,MAAOhD,EAAE,sBAG1B,UAACK,EAAAA,CAAGA,CAAAA,UACF,UAACC,EAAAA,CAAGA,CAAAA,CAACmL,GAAI,YACP,UAAC1H,IAAIA,CACHC,KAAK,6BACLC,GAAG,OAFAF,iCAIH,UAACyC,EAAAA,CAAMA,CAAAA,CAACC,QAAQ,YAAYiF,KAAK,cAC9B1L,EAAE,qBAKX,UAACK,EAAAA,CAAGA,CAAAA,CAACD,UAAU,gBACb,UAACE,EAAAA,CAAGA,CAAAA,CAACmL,GAAI,YACP,UAAC1K,EAAAA,OAAWA,CAAAA,CAAAA,UAOhB4K,EAAiB/D,CAAAA,EAAAA,EAAAA,aAAAA,CAAaA,CAAC,IAAM,UAAC2D,EAAAA,CAAAA,IACtCpE,EAAYyE,CAAAA,EAAAA,EAAAA,EAAAA,CAAWA,CAAC,GAAWzE,SACzC,IAAI,GAAEA,GAAAA,OAAAA,EAAAA,EAAOC,IAAPD,OAAOC,GAAPD,MAAAA,GAAAA,EAAAA,MAAoBU,EAApBV,KAAAA,EAAAA,CAA4B,CAAC,GAA7BA,UAA0C,EAI9C,CAJiD,EAIjD,OAACwE,EAAAA,CAAAA,GAHM,UAACT,EAAAA,OAAeA,CAAAA,CAAAA,EAK3B", "sources": ["webpack://_N_E/./pages/adminsettings/hazard/hazardTableFilter.tsx", "webpack://_N_E/./pages/adminsettings/hazard/hazardTable.tsx", "webpack://_N_E/./pages/adminsettings/permissions.tsx", "webpack://_N_E/./components/common/RKITable.tsx", "webpack://_N_E/./pages/rNoAccess.tsx", "webpack://_N_E/?fe16", "webpack://_N_E/./components/common/PageHeading.tsx", "webpack://_N_E/./pages/adminsettings/hazard/index.tsx"], "sourcesContent": ["//Import Library\r\nimport {Col, Container, FormControl, Row} from \"react-bootstrap\";\r\n\r\n//Import services/components\r\nimport { useTranslation } from 'next-i18next';\r\n\r\nconst HazardTableFilter = ({ filterText, onFilter ,onClear}: any) => {\r\n  const { t } = useTranslation('common');\r\n\r\n\r\n  return (\r\n    <Container fluid className=\"p-0\">\r\n      <Row>\r\n        <Col md={4} className=\"p-0\">\r\n          <FormControl\r\n            type=\"text\"\r\n            className=\"searchInput\"\r\n            placeholder={t(\"adminsetting.hazard.Search\")}\r\n            aria-label=\"Search\"\r\n            value={filterText}\r\n            onChange={onFilter}\r\n          />\r\n        </Col>\r\n      </Row>\r\n    </Container>\r\n  )\r\n};\r\n\r\nexport default HazardTableFilter;\r\n", "//Import Library\r\nimport Link from \"next/link\";\r\nimport React, { useState, useEffect } from \"react\";\r\nimport { <PERSON>dal, Button, Form } from \"react-bootstrap\";\r\nimport _ from \"lodash\";\r\nimport toast from 'react-hot-toast';\r\n\r\n//Import services/components\r\nimport RKITable from \"../../../components/common/RKITable\";\r\nimport apiService from \"../../../services/apiService\";\r\nimport HazardTableFilter from \"./hazardTableFilter\";\r\nimport { useTranslation } from 'next-i18next';\r\n\r\nconst HazardTable = () => {\r\n    const { t, i18n } = useTranslation(\"common\");\r\n    const currentLang = i18n.language === \"fr\" ? \"en\" : i18n.language;\r\n    const titleSearch = currentLang ? `title.${currentLang}` : \"title.en\";\r\n\r\n    const [tabledata, setDataToTable] = useState<any[]>([]);\r\n    const [, setLoading] = useState(false);\r\n    const [totalRows, setTotalRows] = useState(0);\r\n    const [perPage, setPerPage] = useState(10);\r\n    const [filterText, setFilterText] = React.useState(\"\");\r\n    const [resetPaginationToggle, setResetPaginationToggle] = React.useState(false);\r\n    const [isModalShow, setModal] = useState(false);\r\n    const [selectHazard, setSelectHazard] = useState({});\r\n    const [currLang] = useState(titleSearch);\r\n\r\n\r\n    const handleMonitored = async (e: React.ChangeEvent<HTMLInputElement>) => {\r\n        const index = _.findIndex(tabledata, { _id: e.target.name });\r\n        if (index > -1) {\r\n            tabledata[index].enabled = !tabledata[index].enabled;\r\n            setDataToTable([...tabledata]);\r\n            const response = await apiService.patch(`/hazard/${e.target.name}`, tabledata[index]);\r\n            if (response && response._id) {\r\n                toast.success(`${response.title[currentLang]} ${t(\"updatedSuccessfully\")}`);\r\n            } else {\r\n                toast.error(response);\r\n            }\r\n        } else {\r\n            toast.error(t(\"indexNotFound\"));\r\n        }\r\n    };\r\n\r\n    const Toggle = ({ _id, enabled } : { _id: any, enabled: any}) => (\r\n        <Form.Check\r\n            className=\"ms-4\"\r\n            type=\"switch\"\r\n            name={_id}\r\n            id={_id}\r\n            label=\"\"\r\n            checked={enabled}\r\n            onChange={(e) => handleMonitored(e)}\r\n        />\r\n    );\r\n    const columns = [\r\n        {\r\n            name: t(\"menu.hazards\"),\r\n            selector: currLang,\r\n            cell: (d: any) => (d && d.title && d.title[currentLang] ? d.title[currentLang] : \"\"),\r\n        },\r\n        {\r\n            name: t(\"hazardType\"),\r\n            selector: \"hazard_type\",\r\n            cell: (d :any) => (d && d.hazard_type && d.hazard_type.title ? d.hazard_type.title : \"\"),\r\n        },\r\n        {\r\n            name: t(\"published\"),\r\n            selector: \"enabled\",\r\n            cell: (row :any) => <Toggle {...row} />,\r\n        },\r\n        {\r\n            name: t(\"action\"),\r\n            selector: \"\",\r\n            cell: (d :any) => (\r\n                <div>\r\n                    <Link href=\"/adminsettings/[...routes]\" as={`/adminsettings/edit_hazard/${d._id}`}>\r\n\r\n                        <i className=\"icon fas fa-edit\" />\r\n\r\n                    </Link>\r\n                    &nbsp;\r\n                    <span onClick={() => userAction(d)} style={{ cursor: \"pointer\" }}>\r\n                        <i className=\"icon fas fa-trash-alt\" />\r\n                    </span>\r\n                </div>\r\n            ),\r\n        },\r\n    ];\r\n\r\n    useEffect(() => {\r\n        getHazardsData();\r\n    }, []);\r\n\r\n    const hazardParams = {\r\n        sort: { [currLang]: \"asc\" },\r\n        limit: perPage,\r\n        page: 1,\r\n        query: {},\r\n    };\r\n\r\n    const getHazardsData = async () => {\r\n        setLoading(true);\r\n        const response = await apiService.get(\"/hazard\", hazardParams);\r\n        if (response && response.data && response.data.length > 0) {\r\n            setDataToTable(response.data);\r\n            setTotalRows(response.totalCount);\r\n            setLoading(false);\r\n        }\r\n    };\r\n\r\n    const handlePageChange = (page: any) => {\r\n        hazardParams.limit = perPage;\r\n        hazardParams.page = page;\r\n        getHazardsData();\r\n    };\r\n\r\n    const handlePerRowsChange = async (newPerPage: any, page: any) => {\r\n        hazardParams.limit = newPerPage;\r\n        hazardParams.page = page;\r\n        setLoading(true);\r\n        const response = await apiService.get(\"/hazard\", hazardParams);\r\n        if (response && response.data && response.data.length > 0) {\r\n            setDataToTable(response.data);\r\n            setPerPage(newPerPage);\r\n            setLoading(false);\r\n        }\r\n    };\r\n\r\n    const userAction = async (row: any) => {\r\n        setSelectHazard(row._id);\r\n        setModal(true);\r\n    };\r\n\r\n    const modalConfirm = async () => {\r\n        try {\r\n            await apiService.remove(`/hazard/${selectHazard}`);\r\n            getHazardsData();\r\n            setModal(false);\r\n            toast.success(t(\"adminsetting.hazard.Table.hazardDeletedSuccessfully\"));\r\n        } catch (error) {\r\n            toast.error(t(\"adminsetting.hazard.Table.errorDeletingHazard\"));\r\n        }\r\n    };\r\n\r\n    const modalHide = () => setModal(false);\r\n\r\n    const subHeaderComponentMemo = React.useMemo(() => {\r\n        const handleClear = () => {\r\n            if (filterText) {\r\n                setResetPaginationToggle(!resetPaginationToggle);\r\n                setFilterText(\"\");\r\n            }\r\n        };\r\n\r\n        const sendQuery = (q: any) => {\r\n            if (q) {\r\n                hazardParams.query = { [currLang]: q };\r\n            }\r\n            getHazardsData();\r\n        };\r\n\r\n        const handleSearchTitle = _.debounce((q: string) => sendQuery(q), Number(process.env.SEARCH_DEBOUNCE_TIME) || 300);\r\n\r\n        const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {\r\n            setFilterText(e.target.value);\r\n            handleSearchTitle(e.target.value);\r\n        };\r\n\r\n        return <HazardTableFilter onFilter={handleChange} onClear={handleClear} filterText={filterText} />;\r\n    }, [filterText]);\r\n\r\n    return (\r\n        <div>\r\n            <Modal show={isModalShow} onHide={modalHide}>\r\n                <Modal.Header closeButton>\r\n                    <Modal.Title>{t(\"deleteHazard\")}</Modal.Title>\r\n                </Modal.Header>\r\n                <Modal.Body>{t(\"areYouSureWantToDeleteThisHazard\")}</Modal.Body>\r\n                <Modal.Footer>\r\n                    <Button variant=\"secondary\" onClick={modalHide}>\r\n                        {t(\"cancel\")}\r\n                    </Button>\r\n                    <Button variant=\"primary\" onClick={modalConfirm}>\r\n                        {t(\"yes\")}\r\n                    </Button>\r\n                </Modal.Footer>\r\n            </Modal>\r\n\r\n            <RKITable\r\n                columns={columns}\r\n                data={tabledata}\r\n                totalRows={totalRows}\r\n                pagServer={true}\r\n                subheader\r\n                resetPaginationToggle={resetPaginationToggle}\r\n                subHeaderComponent={subHeaderComponentMemo}\r\n                handlePerRowsChange={handlePerRowsChange}\r\n                handlePageChange={handlePageChange}\r\n            />\r\n        </div>\r\n    );\r\n};\r\n\r\nexport default HazardTable;\r\n", "//Import services/components\r\nimport connectedAuthWrapper from 'redux-auth-wrapper/connectedAuthWrapper';\r\n\r\nconst create = \"create:any\";\r\nexport const canAddAreaOfWork = connectedAuthWrapper({\r\n  authenticatedSelector: (state) => {\r\n    if (state.permissions && state.permissions.area_of_work && state.permissions.area_of_work[create]) {\r\n      return true;\r\n    }\r\n    return false;\r\n  },\r\n  wrapperDisplayName: 'CanAddAreaOfWork',\r\n});\r\n\r\nexport const canAddCountry = connectedAuthWrapper({\r\n  authenticatedSelector: (state) => {\r\n    if (state.permissions && state.permissions.country && state.permissions.country[create]) {\r\n      return true;\r\n    }\r\n    return false;\r\n  },\r\n  wrapperDisplayName: 'CanAddCountry',\r\n});\r\n\r\nexport const canAddDeploymentStatus = connectedAuthWrapper({\r\n  authenticatedSelector: (state) => {\r\n    if (state.permissions && state.permissions.deployment_status && state.permissions.deployment_status[create]) {\r\n      return true;\r\n    }\r\n    return false;\r\n  },\r\n  wrapperDisplayName: 'CanAddDeploymentStatus',\r\n});\r\n\r\nexport const canAddEventStatus = connectedAuthWrapper({\r\n  authenticatedSelector: (state) => {\r\n    if (state.permissions && state.permissions.event_status && state.permissions.event_status[create]) {\r\n      return true;\r\n    }\r\n    return false;\r\n  },\r\n  wrapperDisplayName: 'CanAddEventStatus',\r\n});\r\n\r\nexport const canAddExpertise = connectedAuthWrapper({\r\n  authenticatedSelector: (state) => {\r\n    if (state.permissions && state.permissions.expertise && state.permissions.expertise[create]) {\r\n      return true;\r\n    }\r\n    return false;\r\n  },\r\n  wrapperDisplayName: 'CanAddExpertise',\r\n});\r\n\r\nexport const canAddFocalPointApproval = connectedAuthWrapper({\r\n  authenticatedSelector: (state) => {\r\n    if (state.permissions && state.permissions.institution_focal_point && state.permissions.institution_focal_point[create]) {\r\n      return true;\r\n    }\r\n    return false;\r\n  },\r\n  wrapperDisplayName: 'CanAddFocalPointApproval',\r\n});\r\n\r\nexport const canAddVspaceApproval = connectedAuthWrapper({\r\n  authenticatedSelector: (state) => {\r\n    if (state.permissions && state.permissions.institution_focal_point && state.permissions.institution_focal_point[create]) {\r\n      return true;\r\n    }\r\n    return false;\r\n  },\r\n  wrapperDisplayName: 'CanAddVspaceApproval',\r\n});\r\nexport const canAddHazards = connectedAuthWrapper({\r\n  authenticatedSelector: (state) => {\r\n    if (state.permissions && state.permissions.hazard && state.permissions.hazard[create]) {\r\n      return true;\r\n    }\r\n    return false;\r\n  },\r\n  wrapperDisplayName: 'CanAddHazards',\r\n});\r\n\r\nexport const canAddHazardTypes = connectedAuthWrapper({\r\n  authenticatedSelector: (state) => {\r\n    if (state.permissions && state.permissions.hazard_type && state.permissions.hazard_type[create]) {\r\n      return true;\r\n    }\r\n    return false;\r\n  },\r\n  wrapperDisplayName: 'CanAddHazardTypes',\r\n}); \r\n\r\nexport const canAddOrganisationApproval = connectedAuthWrapper({\r\n  authenticatedSelector: (state) => {\r\n    if (state.permissions && state.permissions.institution && state.permissions.institution[create]) {\r\n      return true;\r\n    }\r\n    return false;\r\n  },\r\n  wrapperDisplayName: 'CanAddOrganisationApproval',\r\n}); \r\n\r\nexport const canAddOrganisationNetworks = connectedAuthWrapper({\r\n  authenticatedSelector: (state) => {\r\n    if (state.permissions && state.permissions.institution_network && state.permissions.institution_network[create]) {\r\n      return true;\r\n    }\r\n    return false;\r\n  },\r\n  wrapperDisplayName: 'CanAddOrganisationNetworks',\r\n});\r\n\r\nexport const canAddOrganisationTypes = connectedAuthWrapper({\r\n  authenticatedSelector: (state) => {\r\n    if (state.permissions && state.permissions.institution_type && state.permissions.institution_type[create]) {\r\n      return true;\r\n    }\r\n    return false;\r\n  },\r\n  wrapperDisplayName: 'CanAddOrganisationTypes',\r\n});\r\n\r\nexport const canAddOperationStatus = connectedAuthWrapper({\r\n  authenticatedSelector: (state) => {\r\n    if (state.permissions && state.permissions.operation_status && state.permissions.operation_status[create]) {\r\n      return true;\r\n    }\r\n    return false;\r\n  },\r\n  wrapperDisplayName: 'CanAddOperationStatus',\r\n});\r\n\r\nexport const canAddProjectStatus = connectedAuthWrapper({\r\n  authenticatedSelector: (state) => {\r\n    if (state.permissions && state.permissions.project_status && state.permissions.project_status[create]) {\r\n      return true;\r\n    }\r\n    return false;\r\n  },\r\n  wrapperDisplayName: 'CanAddProjectStatus',\r\n});\r\n\r\nexport const canAddRegions = connectedAuthWrapper({\r\n  authenticatedSelector: (state) => {\r\n    if (state.permissions && state.permissions.region && state.permissions.region[create]) {\r\n      return true;\r\n    }\r\n    return false;\r\n  },\r\n  wrapperDisplayName: 'CanAddRegions',\r\n});\r\n\r\nexport const canAddRiskLevels = connectedAuthWrapper({\r\n  authenticatedSelector: (state) => {\r\n    if (state.permissions && state.permissions.risk_level && state.permissions.risk_level[create]) {\r\n      return true;\r\n    }\r\n    return false;\r\n  },\r\n  wrapperDisplayName: 'CanAddRiskLevels',\r\n});\r\n\r\nexport const canAddSyndromes = connectedAuthWrapper({\r\n  authenticatedSelector: (state) => {\r\n    if (state.permissions && state.permissions.syndrome && state.permissions.syndrome[create]) {\r\n      return true;\r\n    }\r\n    return false;\r\n  },\r\n  wrapperDisplayName: 'CanAddSyndromes',\r\n});\r\n\r\nexport const canAddUpdateTypes = connectedAuthWrapper({\r\n  authenticatedSelector: (state) => {\r\n    if (state.permissions && state.permissions.update_type && state.permissions.update_type[create]) {\r\n      return true;\r\n    }\r\n    return false;\r\n  },\r\n  wrapperDisplayName: 'CanAddUpdateTypes',\r\n});\r\n\r\nexport const canAddUsers = connectedAuthWrapper({\r\n  authenticatedSelector: (state) => {\r\n    if (state.permissions && state.permissions.users && state.permissions.users[create]) {\r\n      return true;\r\n    }\r\n    return false;\r\n  },\r\n  wrapperDisplayName: 'CanAddUsers',\r\n});\r\n\r\nexport const canAddWorldRegion = connectedAuthWrapper({\r\n  authenticatedSelector: (state) => {\r\n    if (state.permissions && state.permissions.worl_region && state.permissions.worl_region[create]) {\r\n      return true;\r\n    }\r\n    return false;\r\n  },\r\n  wrapperDisplayName: 'CanAddWorldRegion',\r\n});\r\n\r\nexport const canAddLandingPage = connectedAuthWrapper({\r\n  authenticatedSelector: (state) => {\r\n    if (state.permissions && state.permissions.landing_page && state.permissions.landing_page[create]) {\r\n      return true;\r\n    }\r\n    return false;\r\n  },\r\n  wrapperDisplayName: 'CanAddLandingPage',\r\n});\r\n\r\nexport const canAddContent = connectedAuthWrapper({\r\n  authenticatedSelector: (state) => {\r\n    if (state.permissions && state.permissions.operation && state.permissions.operation[create] && state.permissions.project && state.permissions.project[create]&& state.permissions.event && state.permissions.event[create]&& state.permissions.vspace && state.permissions.vspace[create]&& state.permissions.institution && state.permissions.institution[create]&& state.permissions.update && state.permissions.update[create]) {\r\n      return true;\r\n    }\r\n    return false;\r\n  },\r\n  wrapperDisplayName: 'CanAddContent',\r\n});\r\n\r\nexport default canAddAreaOfWork;", "//Import Library\r\nimport React from 'react'\r\nimport DataTable  from 'react-data-table-component';\r\n\r\n//Import services/components\r\nimport { useTranslation } from 'next-i18next';\r\nimport CustomLoader from './CustomLoader';\r\n\r\ninterface RKITableProps {\r\n  columns: any[];\r\n  data: any[];\r\n  totalRows?: number;\r\n  resetPaginationToggle?: boolean;\r\n  subheader?: boolean;\r\n  subHeaderComponent?: React.ReactNode;\r\n  handlePerRowsChange?: (newPerPage: number, page: number) => void;\r\n  handlePageChange?: (page: number) => void;\r\n  rowsPerPage?: number[];\r\n  defaultRowsPerPage?: number;\r\n  selectableRows?: boolean;\r\n  loading?: boolean;\r\n  pagServer?: boolean;\r\n  onSelectedRowsChange?: (selectedRows: any) => void;\r\n  clearSelectedRows?: boolean;\r\n  sortServer?: boolean;\r\n  onSort?: (column: any, sortDirection: string) => void;\r\n  persistTableHead?: boolean;\r\n  sortFunction?: (rows: any[], field: any, direction: 'asc' | 'desc') => any[];\r\n  noHeader?: boolean;\r\n  dense?: boolean;\r\n  pagination?: boolean;\r\n  paginationServer?: boolean;\r\n  paginationTotalRows?: number;\r\n  subHeaderAlign?: string;\r\n  [key: string]: any;\r\n}\r\n\r\nfunction RKITable(props: RKITableProps) {\r\n  const { t } = useTranslation('common');\r\n  const paginationComponentOptions = {\r\n    rowsPerPageText: t('Rowsperpage'),\r\n    };\r\n  const {\r\n    columns,\r\n    data,\r\n    totalRows,\r\n    resetPaginationToggle,\r\n    subheader,\r\n    subHeaderComponent,\r\n    handlePerRowsChange,\r\n    handlePageChange,\r\n    rowsPerPage,\r\n    defaultRowsPerPage,\r\n    selectableRows,\r\n    loading,\r\n    pagServer,\r\n    onSelectedRowsChange,\r\n    clearSelectedRows,\r\n    sortServer,\r\n    onSort,\r\n    persistTableHead,\r\n    sortFunction,\r\n    // Filter out any unknown props that might cause DOM warnings\r\n    ...otherProps\r\n  } = props;\r\n\r\n  // Only pass known DataTable props to avoid DOM warnings\r\n  const dataTableProps = {\r\n    paginationComponentOptions,\r\n    noDataComponent: t(\"NoData\"),\r\n    noHeader: true,\r\n    columns,\r\n    data: data || [],\r\n    dense: true,\r\n    paginationResetDefaultPage: resetPaginationToggle,\r\n    subHeader: subheader,\r\n    progressPending: loading,\r\n    subHeaderComponent,\r\n    pagination: true,\r\n    paginationServer: pagServer,\r\n    paginationPerPage: defaultRowsPerPage ? defaultRowsPerPage : 10,\r\n    paginationRowsPerPageOptions: rowsPerPage ? rowsPerPage : [10, 15, 20, 25, 30],\r\n    paginationTotalRows: totalRows,\r\n    onChangeRowsPerPage: handlePerRowsChange,\r\n    onChangePage: handlePageChange,\r\n    selectableRows,\r\n    onSelectedRowsChange,\r\n    clearSelectedRows,\r\n    progressComponent: <CustomLoader/>,\r\n    sortIcon: <i className=\"sort-icon fas fa-exchange-alt\" />,\r\n    sortServer,\r\n    onSort,\r\n    sortFunction,\r\n    persistTableHead,\r\n    className: \"rki-table\"\r\n  };\r\n  return (\r\n    <DataTable {...dataTableProps} />\r\n  )\r\n};\r\n\r\nRKITable.defaultProps = {\r\n  subHeader: false,\r\n  pagination: true,\r\n  totalRows: null,\r\n  pagServer: true,\r\n  onSelectedRowsChange: null,\r\n  clearSelectedRows: false,\r\n  sortServer: false,\r\n  persistTableHead: false\r\n}\r\n\r\nexport default RKITable;\r\n\r\n", "export default function NoAccessMessage(_props: any) {\r\n    return (\r\n      <div className=\"container-fluid p-0 response-message-block\">\r\n        <div className=\"message\">you don't have permission to access</div>\r\n      </div>\r\n    )\r\n  }", "\n    (window.__NEXT_P = window.__NEXT_P || []).push([\n      \"/adminsettings/hazard\",\n      function () {\n        return require(\"private-next-pages/adminsettings/hazard/index.tsx\");\n      }\n    ]);\n    if(module.hot) {\n      module.hot.dispose(function () {\n        window.__NEXT_P.push([\"/adminsettings/hazard\"])\n      });\n    }\n  ", "interface PageHeadingProps {\r\n  title: string; // Required based on actual usage\r\n}\r\n\r\nexport default function PageHeading(props: PageHeadingProps) {\r\n  return (\r\n    <h2 className=\"page-heading\">{props.title}</h2>\r\n  )\r\n}\r\n", "//Import Library\r\nimport { Container, <PERSON>, <PERSON>, Button } from \"react-bootstrap\";\r\nimport Link from \"next/link\";\r\n\r\n//Import services/components\r\nimport PageHeading from \"../../../components/common/PageHeading\";\r\nimport HazardTable from \"./hazardTable\";\r\nimport { useTranslation } from 'next-i18next';\r\nimport { serverSideTranslations } from 'next-i18next/serverSideTranslations';\r\nimport { canAddHazards } from \"../permissions\";\r\nimport { useSelector } from \"react-redux\";\r\nimport NoAccessMessage from \"../../rNoAccess\";\r\n\r\nconst HazardIndex = (_props: any) => {\r\n  const { t } = useTranslation(\"common\");\r\n  const ShowHazardIndex = () => {\r\n    return (\r\n      <Container style={{ overflowX: \"hidden\" }} fluid className=\"p-0\">\r\n        <Row>\r\n          <Col xs={12}>\r\n            <PageHeading title={t(\"menu.hazards\")} />\r\n          </Col>\r\n        </Row>\r\n        <Row>\r\n          <Col xs={12}>\r\n            <Link\r\n              href=\"/adminsettings/[...routes]\"\r\n              as=\"/adminsettings/create_hazard\"\r\n              >\r\n              <Button variant=\"secondary\" size=\"sm\">\r\n                {t(\"addHazard\")}\r\n              </Button>\r\n            </Link>\r\n          </Col>\r\n        </Row>\r\n        <Row className=\"mt-3\">\r\n          <Col xs={12}>\r\n            <HazardTable />\r\n          </Col>\r\n        </Row>\r\n      </Container>\r\n    );\r\n  }\r\n\r\n  const ShowAddHazards = canAddHazards(() => <ShowHazardIndex />);\r\n  const state:any = useSelector((state) => state);\r\n  if (!(state?.permissions?.hazard?.['create:any'])) {\r\n    return <NoAccessMessage />\r\n  }\r\n  return(\r\n    <ShowAddHazards />\r\n  );\r\n};\r\n\r\nexport async function getStaticProps({ locale } : { locale: string }) {\r\n  return {\r\n    props: {\r\n      ...(await serverSideTranslations(locale, ['common'])),\r\n    },\r\n  }\r\n}\r\n\r\nexport default HazardIndex;\r\n"], "names": ["filterText", "HazardTableFilter", "onFilter", "onClear", "t", "useTranslation", "Container", "fluid", "className", "Row", "Col", "md", "FormControl", "type", "placeholder", "aria-label", "value", "onChange", "i18n", "HazardTable", "currentLang", "language", "tabledata", "setDataToTable", "useState", "setLoading", "totalRows", "setTotalRows", "perPage", "setPerPage", "setFilterText", "React", "resetPaginationToggle", "setResetPaginationToggle", "isModalShow", "setModal", "selectHazard", "setSelectHazard", "currLang", "handleMonitored", "e", "index", "_", "_id", "target", "name", "enabled", "response", "apiService", "patch", "toast", "success", "title", "error", "Toggle", "Form", "Check", "id", "label", "checked", "columns", "selector", "cell", "d", "hazard_type", "row", "div", "Link", "href", "as", "i", "span", "onClick", "userAction", "style", "cursor", "useEffect", "getHazardsData", "hazardParams", "sort", "limit", "page", "query", "get", "data", "length", "totalCount", "handlePerRowsChange", "newPerPage", "modalConfirm", "remove", "modalHide", "subHeaderComponentMemo", "<PERSON><PERSON><PERSON><PERSON>", "q", "handleSearchTitle", "Number", "process", "handleChange", "handleClear", "Modal", "show", "onHide", "Header", "closeButton", "Title", "Body", "Footer", "<PERSON><PERSON>", "variant", "RKITable", "pagServer", "subheader", "subHeaderComponent", "handlePageChange", "create", "canAddAreaOfWork", "connectedAuthWrapper", "authenticatedSelector", "state", "permissions", "area_of_work", "wrapperDisplayName", "country", "deployment_status", "event_status", "expertise", "institution_focal_point", "canAddHazards", "hazard", "institution", "institution_network", "institution_type", "canAddOperationStatus", "operation_status", "project_status", "region", "risk_level", "syndrome", "update_type", "users", "canAddWorldRegion", "worl_region", "landing_page", "operation", "project", "event", "vspace", "update", "props", "paginationComponentOptions", "rowsPerPageText", "rowsPerPage", "defaultRowsPerPage", "selectableRows", "loading", "onSelectedRowsChange", "clearSelectedRows", "sortServer", "onSort", "persistTableHead", "sortFunction", "otherProps", "dataTableProps", "noDataComponent", "<PERSON><PERSON><PERSON><PERSON>", "dense", "paginationResetDefaultPage", "subHeader", "progressPending", "pagination", "paginationServer", "paginationPerPage", "paginationRowsPerPageOptions", "paginationTotalRows", "onChangeRowsPerPage", "onChangePage", "progressComponent", "CustomLoader", "sortIcon", "DataTable", "defaultProps", "NoAccessMessage", "_props", "PageHeading", "h2", "HazardIndex", "ShowHazardIndex", "overflowX", "xs", "size", "ShowAddHazards", "useSelector"], "sourceRoot": "", "ignoreList": []}