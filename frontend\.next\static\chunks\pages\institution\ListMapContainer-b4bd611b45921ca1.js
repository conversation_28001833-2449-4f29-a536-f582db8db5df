(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[6254],{15641:(e,t,r)=>{"use strict";r.d(t,{A:()=>o});var l=r(37876);r(14232);var s=r(62945);let o=e=>{let{name:t="Marker",id:r="",countryId:o="",type:a,icon:n,position:i,onClick:y,title:d,draggable:c=!1}=e;return i&&"number"==typeof i.lat&&"number"==typeof i.lng?(0,l.jsx)(s.pH,{position:i,icon:n,title:d||t,draggable:c,onClick:e=>{y&&y({name:t,id:r,countryId:o,type:a,position:i},{position:i,getPosition:()=>i},e)}}):null}},44188:(e,t,r)=>{(window.__NEXT_P=window.__NEXT_P||[]).push(["/institution/ListMapContainer",function(){return r(71377)}])},66619:(e,t,r)=>{"use strict";r.d(t,{A:()=>u});var l=r(37876);r(14232);var s=r(62945);let o=e=>{let{position:t,onCloseClick:r,children:o}=e;return(0,l.jsx)(s.Fu,{position:t,onCloseClick:r,children:(0,l.jsx)("div",{children:o})})},a="labels.text.fill",n="labels.text.stroke",i="road.highway",y="geometry.stroke",d=[{elementType:"geometry",stylers:[{color:"#1d2c4d"}]},{elementType:a,stylers:[{color:"#8ec3b9"}]},{elementType:n,stylers:[{color:"#1a3646"}]},{featureType:"administrative",elementType:"geometry",stylers:[{visibility:"off"}]},{featureType:"administrative.country",elementType:y,stylers:[{color:"#4b6878"}]},{featureType:"administrative.land_parcel",elementType:"labels",stylers:[{visibility:"off"}]},{featureType:"administrative.land_parcel",elementType:a,stylers:[{color:"#64779e"}]},{featureType:"administrative.province",elementType:y,stylers:[{color:"#4b6878"}]},{featureType:"landscape.man_made",elementType:y,stylers:[{color:"#334e87"}]},{featureType:"landscape.natural",elementType:"geometry",stylers:[{color:"#023e58"}]},{featureType:"poi",stylers:[{visibility:"off"}]},{featureType:"poi",elementType:"geometry",stylers:[{color:"#283d6a"}]},{featureType:"poi",elementType:"labels.text",stylers:[{visibility:"off"}]},{featureType:"poi",elementType:a,stylers:[{color:"#6f9ba5"}]},{featureType:"poi",elementType:n,stylers:[{color:"#1d2c4d"}]},{featureType:"poi.park",elementType:"geometry.fill",stylers:[{color:"#023e58"}]},{featureType:"poi.park",elementType:a,stylers:[{color:"#3C7680"}]},{featureType:"road",stylers:[{visibility:"off"}]},{featureType:"road",elementType:"geometry",stylers:[{color:"#304a7d"}]},{featureType:"road",elementType:"labels.icon",stylers:[{visibility:"off"}]},{featureType:"road",elementType:a,stylers:[{color:"#98a5be"}]},{featureType:"road",elementType:n,stylers:[{color:"#1d2c4d"}]},{featureType:i,elementType:"geometry",stylers:[{color:"#2c6675"}]},{featureType:i,elementType:y,stylers:[{color:"#255763"}]},{featureType:i,elementType:a,stylers:[{color:"#b0d5ce"}]},{featureType:i,elementType:n,stylers:[{color:"#023e58"}]},{featureType:"road.local",elementType:"labels",stylers:[{visibility:"off"}]},{featureType:"transit",stylers:[{visibility:"off"}]},{featureType:"transit",elementType:a,stylers:[{color:"#98a5be"}]},{featureType:"transit",elementType:n,stylers:[{color:"#1d2c4d"}]},{featureType:"transit.line",elementType:"geometry.fill",stylers:[{color:"#283d6a"}]},{featureType:"transit.station",elementType:"geometry",stylers:[{color:"#3a4762"}]},{featureType:"water",elementType:"geometry",stylers:[{color:"#0e1626"}]},{featureType:"water",elementType:a,stylers:[{color:"#4e6d70"}]}];var c=r(89099),p=r(55316);let u=e=>{let{markerInfo:t,activeMarker:r,initialCenter:a,children:n,height:i=300,width:y="114%",language:u,zoom:m=1,minZoom:f=1,onClose:T}=e,{locale:g}=(0,c.useRouter)(),{isLoaded:b,loadError:v}=(0,p._)();return v?(0,l.jsx)("div",{children:"Error loading maps"}):b?(0,l.jsx)("div",{className:"map-container",children:(0,l.jsx)("div",{className:"mapprint",style:{width:y,height:i,position:"relative"},children:(0,l.jsxs)(s.u6,{mapContainerStyle:{width:y,height:"number"==typeof i?"".concat(i,"px"):i},center:a||{lat:52.520017,lng:13.404195},zoom:m,onLoad:e=>{e.setOptions({styles:d})},options:{minZoom:f,draggable:!0,keyboardShortcuts:!1,streetViewControl:!1,panControl:!1,clickableIcons:!1,mapTypeControl:!1,fullscreenControl:!0},children:[n,t&&r&&r.getPosition&&(0,l.jsx)(o,{position:r.getPosition(),onCloseClick:()=>{console.log("close click"),null==T||T()},children:t})]})})}):(0,l.jsx)("div",{children:"Loading Maps..."})}},71377:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>d});var l=r(37876),s=r(14232),o=r(82851),a=r.n(o),n=r(66619),i=r(15641),y=r(31753);let d=e=>{let{i18n:t}=(0,y.Bd)("common"),r=t.language,{institutions:o}=e,[d,c]=(0,s.useState)({}),[p,u]=(0,s.useState)([]),[m,f]=(0,s.useState)({}),T=()=>{c(null),f(null)},g=(e,t,r)=>{T(),c(t),f({name:e.name,id:e.id,countryId:e.countryId})},b=()=>{let e=[],t=o.filter(e=>"Request Pending"!=e.status);a().forEach(t,t=>{e.push({title:t.title,id:t._id,countryId:t&&t.address&&t.address.country&&t.address.country._id,lat:t.address&&t.address.country&&t.address.country.coordinates&&parseFloat(t.address.country.coordinates[0].latitude),lng:t.address&&t.address.country&&t.address.country.coordinates&&parseFloat(t.address.country.coordinates[0].longitude)})}),u([...e])};return(0,s.useEffect)(()=>{b()},[o]),(0,l.jsx)(n.A,{onClose:T,language:r,points:p,activeMarker:d,markerInfo:(0,l.jsx)(e=>{let{info:t}=e;if(t&&t.countryId){let e=o.filter(e=>"Request Pending"!=e.status).filter(e=>e.address&&e.address.country&&e.address.country._id==t.countryId);return(0,l.jsx)("ul",{children:e.map((e,t)=>(0,l.jsx)("li",{children:(0,l.jsx)("a",{href:"/".concat(r,"/institution/show/").concat(e._id),children:e.title})},t))})}return null},{info:m}),children:p.length>=1?p.map((e,t)=>(0,l.jsx)(i.A,{name:e.title,id:e.id,countryId:e.countryId,icon:{url:"/images/map-marker-white.svg"},onClick:g,position:e},t)):null})}}},e=>{var t=t=>e(e.s=t);e.O(0,[9759,636,6593,8792],()=>t(44188)),_N_E=e.O()}]);
//# sourceMappingURL=ListMapContainer-b4bd611b45921ca1.js.map