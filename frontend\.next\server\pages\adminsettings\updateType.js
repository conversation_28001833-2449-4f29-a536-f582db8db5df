"use strict";(()=>{var e={};e.id=7210,e.ids=[636,3220,7210],e.modules={123:e=>{e.exports=require("dom-helpers/removeEventListener")},1332:e=>{e.exports=require("react-custom-scrollbars-2")},1428:e=>{e.exports=import("axios")},1680:e=>{e.exports=require("@restart/ui/utils")},1919:e=>{e.exports=require("react-transition-group/Transition")},3892:e=>{e.exports=require("classnames")},4048:e=>{e.exports=require("react-truncate")},6009:e=>{e.exports=require("dom-helpers/ownerDocument")},6952:e=>{e.exports=require("@restart/ui/Modal")},7374:e=>{e.exports=require("@restart/ui/Dropdown")},8732:e=>{e.exports=require("react/jsx-runtime")},9653:e=>{e.exports=require("@restart/ui/Overlay")},11242:e=>{e.exports=require("redux-persist/integration/react")},11688:e=>{e.exports=require("@restart/hooks/useWillUnmount")},12388:e=>{e.exports=require("dom-helpers/addEventListener")},13364:e=>{e.exports=require("redux-saga/effects")},14062:e=>{e.exports=import("react-redux")},14078:e=>{e.exports=import("swr")},14332:e=>{e.exports=require("uncontrollable")},16116:e=>{e.exports=require("invariant")},18622:e=>{e.exports=require("yup")},19121:e=>{e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},22326:e=>{e.exports=require("react-dom")},22541:e=>{e.exports=require("redux-persist/lib/storage")},25303:e=>{e.exports=require("@restart/ui/NavItem")},26324:e=>{e.exports=require("warning")},27053:(e,t,r)=>{r.d(t,{A:()=>a});var s=r(8732);function a(e){return(0,s.jsx)("h2",{className:"page-heading",children:e.title})}},27825:e=>{e.exports=require("lodash")},27910:e=>{e.exports=require("stream")},28217:e=>{e.exports=require("@restart/ui/DropdownItem")},29021:e=>{e.exports=require("fs")},29294:e=>{e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},29628:e=>{e.exports=require("dom-helpers/addClass")},29825:e=>{e.exports=require("prop-types")},29841:e=>{e.exports=require("dom-helpers/hasClass")},30103:(e,t,r)=>{r.a(e,async(e,s)=>{try{r.r(t),r.d(t,{default:()=>h});var a=r(8732),i=r(7082),n=r(83551),o=r(49481),p=r(91353),d=r(19918),u=r.n(d),l=r(27053),c=r(95864),m=r(88751),x=r(45927),g=e([c]);c=(g.then?(await g)():g)[0];let h=e=>{let{t}=(0,m.useTranslation)("common"),r=()=>(0,a.jsxs)(i.A,{style:{overflowX:"hidden"},fluid:!0,className:"p-0",children:[(0,a.jsx)(n.A,{children:(0,a.jsx)(o.A,{xs:12,children:(0,a.jsx)(l.A,{title:t("adminsetting.updatestype.UpdateType")})})}),(0,a.jsx)(n.A,{children:(0,a.jsx)(o.A,{xs:12,children:(0,a.jsx)(u(),{href:"/adminsettings/[...routes]",as:"/adminsettings/create_update_type",children:(0,a.jsx)(p.A,{variant:"secondary",size:"sm",children:t("adminsetting.updatestype.AddUpdateType")})})})}),(0,a.jsx)(n.A,{className:"mt-3",children:(0,a.jsx)(o.A,{xs:12,children:(0,a.jsx)(c.default,{})})})]}),s=(0,x.default)(()=>(0,a.jsx)(r,{}));return(0,a.jsx)(s,{})};s()}catch(e){s(e)}})},30362:e=>{e.exports=require("@restart/ui/DropdownToggle")},33873:e=>{e.exports=require("path")},36653:e=>{e.exports=require("nprogress")},36955:e=>{e.exports=require("@restart/hooks/useBreakpoint")},37766:e=>{e.exports=require("@restart/hooks/useMergedRefs")},38609:e=>{e.exports=require("react-data-table-component")},39756:e=>{e.exports=import("redux")},40051:e=>{e.exports=require("dom-helpers/removeClass")},40361:e=>{e.exports=require("next/dist/compiled/next-server/pages.runtime.prod.js")},42893:e=>{e.exports=import("react-hot-toast")},43294:e=>{e.exports=require("formik")},43762:(e,t,r)=>{r.a(e,async(e,s)=>{try{r.r(t),r.d(t,{config:()=>h,default:()=>c,getServerSideProps:()=>g,getStaticPaths:()=>x,getStaticProps:()=>m,reportWebVitals:()=>y,routeModule:()=>v,unstable_getServerProps:()=>w,unstable_getServerSideProps:()=>f,unstable_getStaticParams:()=>q,unstable_getStaticPaths:()=>S,unstable_getStaticProps:()=>A});var a=r(63885),i=r(80237),n=r(81413),o=r(9616),p=r.n(o),d=r(72386),u=r(30103),l=e([d,u]);[d,u]=l.then?(await l)():l;let c=(0,n.M)(u,"default"),m=(0,n.M)(u,"getStaticProps"),x=(0,n.M)(u,"getStaticPaths"),g=(0,n.M)(u,"getServerSideProps"),h=(0,n.M)(u,"config"),y=(0,n.M)(u,"reportWebVitals"),A=(0,n.M)(u,"unstable_getStaticProps"),S=(0,n.M)(u,"unstable_getStaticPaths"),q=(0,n.M)(u,"unstable_getStaticParams"),w=(0,n.M)(u,"unstable_getServerProps"),f=(0,n.M)(u,"unstable_getServerSideProps"),v=new a.PagesRouteModule({definition:{kind:i.A.PAGES,page:"/adminsettings/updateType",pathname:"/adminsettings/updateType",bundlePath:"",filename:""},components:{App:d.default,Document:p()},userland:u});s()}catch(e){s(e)}})},45927:(e,t,r)=>{r.r(t),r.d(t,{canAddAreaOfWork:()=>n,canAddContent:()=>C,canAddCountry:()=>o,canAddDeploymentStatus:()=>p,canAddEventStatus:()=>d,canAddExpertise:()=>u,canAddFocalPointApproval:()=>l,canAddHazardTypes:()=>x,canAddHazards:()=>m,canAddLandingPage:()=>j,canAddOperationStatus:()=>A,canAddOrganisationApproval:()=>g,canAddOrganisationNetworks:()=>h,canAddOrganisationTypes:()=>y,canAddProjectStatus:()=>S,canAddRegions:()=>q,canAddRiskLevels:()=>w,canAddSyndromes:()=>f,canAddUpdateTypes:()=>v,canAddUsers:()=>P,canAddVspaceApproval:()=>c,canAddWorldRegion:()=>_,default:()=>D});var s=r(81366),a=r.n(s);let i="create:any",n=a()({authenticatedSelector:e=>!!e.permissions&&!!e.permissions.area_of_work&&!!e.permissions.area_of_work[i],wrapperDisplayName:"CanAddAreaOfWork"}),o=a()({authenticatedSelector:e=>!!e.permissions&&!!e.permissions.country&&!!e.permissions.country[i],wrapperDisplayName:"CanAddCountry"}),p=a()({authenticatedSelector:e=>!!e.permissions&&!!e.permissions.deployment_status&&!!e.permissions.deployment_status[i],wrapperDisplayName:"CanAddDeploymentStatus"}),d=a()({authenticatedSelector:e=>!!e.permissions&&!!e.permissions.event_status&&!!e.permissions.event_status[i],wrapperDisplayName:"CanAddEventStatus"}),u=a()({authenticatedSelector:e=>!!e.permissions&&!!e.permissions.expertise&&!!e.permissions.expertise[i],wrapperDisplayName:"CanAddExpertise"}),l=a()({authenticatedSelector:e=>!!e.permissions&&!!e.permissions.institution_focal_point&&!!e.permissions.institution_focal_point[i],wrapperDisplayName:"CanAddFocalPointApproval"}),c=a()({authenticatedSelector:e=>!!e.permissions&&!!e.permissions.institution_focal_point&&!!e.permissions.institution_focal_point[i],wrapperDisplayName:"CanAddVspaceApproval"}),m=a()({authenticatedSelector:e=>!!e.permissions&&!!e.permissions.hazard&&!!e.permissions.hazard[i],wrapperDisplayName:"CanAddHazards"}),x=a()({authenticatedSelector:e=>!!e.permissions&&!!e.permissions.hazard_type&&!!e.permissions.hazard_type[i],wrapperDisplayName:"CanAddHazardTypes"}),g=a()({authenticatedSelector:e=>!!e.permissions&&!!e.permissions.institution&&!!e.permissions.institution[i],wrapperDisplayName:"CanAddOrganisationApproval"}),h=a()({authenticatedSelector:e=>!!e.permissions&&!!e.permissions.institution_network&&!!e.permissions.institution_network[i],wrapperDisplayName:"CanAddOrganisationNetworks"}),y=a()({authenticatedSelector:e=>!!e.permissions&&!!e.permissions.institution_type&&!!e.permissions.institution_type[i],wrapperDisplayName:"CanAddOrganisationTypes"}),A=a()({authenticatedSelector:e=>!!e.permissions&&!!e.permissions.operation_status&&!!e.permissions.operation_status[i],wrapperDisplayName:"CanAddOperationStatus"}),S=a()({authenticatedSelector:e=>!!e.permissions&&!!e.permissions.project_status&&!!e.permissions.project_status[i],wrapperDisplayName:"CanAddProjectStatus"}),q=a()({authenticatedSelector:e=>!!e.permissions&&!!e.permissions.region&&!!e.permissions.region[i],wrapperDisplayName:"CanAddRegions"}),w=a()({authenticatedSelector:e=>!!e.permissions&&!!e.permissions.risk_level&&!!e.permissions.risk_level[i],wrapperDisplayName:"CanAddRiskLevels"}),f=a()({authenticatedSelector:e=>!!e.permissions&&!!e.permissions.syndrome&&!!e.permissions.syndrome[i],wrapperDisplayName:"CanAddSyndromes"}),v=a()({authenticatedSelector:e=>!!e.permissions&&!!e.permissions.update_type&&!!e.permissions.update_type[i],wrapperDisplayName:"CanAddUpdateTypes"}),P=a()({authenticatedSelector:e=>!!e.permissions&&!!e.permissions.users&&!!e.permissions.users[i],wrapperDisplayName:"CanAddUsers"}),_=a()({authenticatedSelector:e=>!!e.permissions&&!!e.permissions.worl_region&&!!e.permissions.worl_region[i],wrapperDisplayName:"CanAddWorldRegion"}),j=a()({authenticatedSelector:e=>!!e.permissions&&!!e.permissions.landing_page&&!!e.permissions.landing_page[i],wrapperDisplayName:"CanAddLandingPage"}),C=a()({authenticatedSelector:e=>!!e.permissions&&!!e.permissions.operation&&!!e.permissions.operation[i]&&!!e.permissions.project&&!!e.permissions.project[i]&&!!e.permissions.event&&!!e.permissions.event[i]&&!!e.permissions.vspace&&!!e.permissions.vspace[i]&&!!e.permissions.institution&&!!e.permissions.institution[i]&&!!e.permissions.update&&!!e.permissions.update[i]||!1,wrapperDisplayName:"CanAddContent"}),D=n},50009:e=>{e.exports=require("@restart/ui/DropdownMenu")},56084:(e,t,r)=>{r.d(t,{A:()=>d});var s=r(8732);r(82015);var a=r(38609),i=r.n(a),n=r(88751),o=r(30370);function p(e){let{t}=(0,n.useTranslation)("common"),r={rowsPerPageText:t("Rowsperpage")},{columns:a,data:p,totalRows:d,resetPaginationToggle:u,subheader:l,subHeaderComponent:c,handlePerRowsChange:m,handlePageChange:x,rowsPerPage:g,defaultRowsPerPage:h,selectableRows:y,loading:A,pagServer:S,onSelectedRowsChange:q,clearSelectedRows:w,sortServer:f,onSort:v,persistTableHead:P,sortFunction:_,...j}=e,C={paginationComponentOptions:r,noDataComponent:t("NoData"),noHeader:!0,columns:a,data:p||[],dense:!0,paginationResetDefaultPage:u,subHeader:l,progressPending:A,subHeaderComponent:c,pagination:!0,paginationServer:S,paginationPerPage:h||10,paginationRowsPerPageOptions:g||[10,15,20,25,30],paginationTotalRows:d,onChangeRowsPerPage:m,onChangePage:x,selectableRows:y,onSelectedRowsChange:q,clearSelectedRows:w,progressComponent:(0,s.jsx)(o.A,{}),sortIcon:(0,s.jsx)("i",{className:"sort-icon fas fa-exchange-alt"}),sortServer:f,onSort:v,sortFunction:_,persistTableHead:P,className:"rki-table"};return(0,s.jsx)(i(),{...C})}p.defaultProps={subHeader:!1,pagination:!0,totalRows:null,pagServer:!0,onSelectedRowsChange:null,clearSelectedRows:!1,sortServer:!1,persistTableHead:!1};let d=p},57664:e=>{e.exports=require("dom-helpers/scrollbarSize")},58928:e=>{e.exports=require("dom-helpers/canUseDOM")},59717:e=>{e.exports=require("@restart/ui/DropdownContext")},60560:e=>{e.exports=require("dom-helpers/contains")},63033:e=>{e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},65447:e=>{e.exports=require("@restart/ui/Button")},67364:e=>{e.exports=require("@restart/hooks/useCallbackRef")},68455:e=>{e.exports=import("redux-saga")},69722:e=>{e.exports=require("es6-promise")},74075:e=>{e.exports=require("zlib")},74716:e=>{e.exports=require("moment")},74987:e=>{e.exports=require("@restart/ui/ModalManager")},78097:e=>{e.exports=require("next-redux-saga")},78634:e=>{e.exports=require("@restart/ui/Anchor")},80237:(e,t)=>{Object.defineProperty(t,"A",{enumerable:!0,get:function(){return r}});var r=function(e){return e.PAGES="PAGES",e.PAGES_API="PAGES_API",e.APP_PAGE="APP_PAGE",e.APP_ROUTE="APP_ROUTE",e.IMAGE="IMAGE",e}({})},81366:e=>{e.exports=require("redux-auth-wrapper/connectedAuthWrapper")},81413:(e,t)=>{Object.defineProperty(t,"M",{enumerable:!0,get:function(){return function e(t,r){return r in t?t[r]:"then"in t&&"function"==typeof t.then?t.then(t=>e(t,r)):"function"==typeof t&&"default"===r?t:void 0}}})},81521:e=>{e.exports=require("dom-helpers/querySelectorAll")},81895:e=>{e.exports=require("@restart/hooks/useEventCallback")},82015:e=>{e.exports=require("react")},86842:e=>{e.exports=require("@restart/ui/SelectableContext")},87571:e=>{e.exports=require("dom-helpers/transitionEnd")},88751:e=>{e.exports=require("next-i18next")},93787:e=>{e.exports=require("redux-persist")},94947:e=>{e.exports=require("@restart/hooks/useTimeout")},95864:(e,t,r)=>{r.a(e,async(e,s)=>{try{r.r(t),r.d(t,{default:()=>g});var a=r(8732),i=r(19918),n=r.n(i),o=r(82015),p=r(12403),d=r(91353),u=r(42893),l=r(56084),c=r(88751),m=r(63487),x=e([u,m]);[u,m]=x.then?(await x)():x;let g=e=>{let{t}=(0,c.useTranslation)("common"),[r,s]=(0,o.useState)([]),[,i]=(0,o.useState)(!1),[x,g]=(0,o.useState)(0),[h,y]=(0,o.useState)(10),[A,S]=(0,o.useState)(!1),[q,w]=(0,o.useState)({}),f=()=>S(!1),v=[{name:t("adminsetting.updatestype.Title"),selector:"title"},{name:t("adminsetting.updatestype.Icon"),selector:"icon",cell:e=>(0,a.jsx)("i",{className:`fas ${e.icon}`})},{name:t("adminsetting.updatestype.Action"),selector:"",cell:e=>(0,a.jsxs)("div",{children:[(0,a.jsx)(n(),{href:"/adminsettings/[...routes]",as:`/adminsettings/edit_update_type/${e._id}`,children:(0,a.jsx)("i",{className:"icon fas fa-edit"})}),"\xa0",(0,a.jsx)("a",{onClick:()=>D(e),children:(0,a.jsx)("i",{className:"icon fas fa-trash-alt"})})," "]})}],P={sort:{title:"asc"},limit:h,page:1,query:{}};(0,o.useEffect)(()=>{_(P)},[]);let _=async e=>{i(!0);let t=await m.A.get("/updatetype",e);t&&t.data&&t.data.length>0&&(s(t.data),g(t.totalCount),i(!1))},j=async(e,t)=>{P.limit=e,P.page=t,i(!0);let r=await m.A.get("/updatetype",P);r&&r.data&&r.data.length>0&&(s(r.data),y(e),i(!1))},C=async()=>{try{await m.A.remove(`/updatetype/${q}`),_(P),S(!1),u.default.success(t("adminsetting.updatestype.Table.updateTypeDeletedSuccessfully"))}catch(e){u.default.error(t("adminsetting.updatestype.Table.errorDeletingUpdateType"))}},D=async e=>{w(e._id),S(!0)};return(0,a.jsxs)("div",{children:[(0,a.jsxs)(p.A,{show:A,onHide:f,children:[(0,a.jsx)(p.A.Header,{closeButton:!0,children:(0,a.jsx)(p.A.Title,{children:t("adminsetting.updatestype.DeleteUpdateType")})}),(0,a.jsx)(p.A.Body,{children:t("adminsetting.updatestype.Areyousurewanttodeletethisupdatetype?")}),(0,a.jsxs)(p.A.Footer,{children:[(0,a.jsx)(d.A,{variant:"secondary",onClick:f,children:t("adminsetting.updatestype.Cancel")}),(0,a.jsx)(d.A,{variant:"primary",onClick:C,children:t("adminsetting.updatestype.Yes")})]})]}),(0,a.jsx)(l.A,{columns:v,data:r,totalRows:x,pagServer:!0,handlePerRowsChange:j,handlePageChange:e=>{P.limit=h,P.page=e,_(P)}})]})};s()}catch(e){s(e)}})},96196:e=>{e.exports=require("next-redux-wrapper")},98320:e=>{e.exports=require("@restart/hooks/useIsomorphicEffect")},99460:e=>{e.exports=require("dom-helpers/css")}};var t=require("../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[6089,9216,9616,2386],()=>r(43762));module.exports=s})();