{"version": 3, "file": "static/chunks/pages/operation/components/OperationStats-e3f68fe1b47b72da.js", "mappings": "8MAwCA,MA9BwBA,IACpB,GAAM,GAAEC,CAAC,CAAE,CAAGC,CAAAA,EAAAA,EAAAA,EAAAA,CAAcA,CAAC,MA6BlBC,IA5BX,MACI,IA2BqBA,CA3BrB,CA2BsB,EA3BtB,uBACI,UAACC,MAAAA,CAAIC,UAAU,0BACX,WAACC,EAAAA,CAAGA,CAAAA,WACA,WAACC,EAAAA,CAAGA,CAAAA,CAACF,UAAU,sBAAsBG,GAAI,YACrC,UAACJ,MAAAA,CAAIC,UAAU,yBACX,UAACI,EAAAA,CAAeA,CAAAA,CAACC,KAAMC,EAAAA,GAAOA,CAAEC,MAAM,OAAOC,KAAK,SAEtD,WAACT,MAAAA,CAAIC,UAAU,0BACX,UAACS,KAAAA,UAAIb,EAAE,cACP,UAACc,KAAAA,UAAIf,EAAMgB,SAAS,CAACC,QAAQ,CAACC,MAAM,SAG5C,WAACX,EAAAA,CAAGA,CAAAA,CAACF,UAAU,sBAAsBG,GAAI,YACrC,UAACJ,MAAAA,CAAIC,UAAU,yBACX,UAACI,EAAAA,CAAeA,CAAAA,CAACC,KAAMS,EAAAA,GAAMA,CAAEP,MAAM,OAAOC,KAAK,SAErD,WAACT,MAAAA,CAAIC,UAAU,0BACX,UAACS,KAAAA,UAAIb,EAAE,uBACP,UAACc,KAAAA,UAAIf,EAAMgB,SAAS,CAACI,QAAQ,CAACF,MAAM,eAOhE,mBCrCA,4CACA,uCACA,WACA,OAAe,EAAQ,KAA4D,CACnF,EACA,SAFsB", "sources": ["webpack://_N_E/./pages/operation/components/OperationStats.tsx", "webpack://_N_E/?d97a"], "sourcesContent": ["//Import Library\r\nimport React from \"react\";\r\nimport { faBell, faUsers } from \"@fortawesome/free-solid-svg-icons\";\r\nimport { FontAwesomeIcon } from \"@fortawesome/react-fontawesome\";\r\nimport { Col, Row } from \"react-bootstrap\";\r\n\r\n//Import services/components\r\nimport { useTranslation } from 'next-i18next';\r\n\r\n\r\nconst operationStats = (props: any) => {\r\n    const { t } = useTranslation('common');\r\n    return (\r\n        <>\r\n            <div className=\"operationStats\">\r\n                <Row>\r\n                    <Col className=\"operationInfo-Items\" md={6}>\r\n                        <div className=\"operationIcon\">\r\n                            <FontAwesomeIcon icon={faUsers} color=\"#fff\" size=\"2x\" />\r\n                        </div>\r\n                        <div className=\"operationInfo\">\r\n                            <h5>{t(\"Partners\")}</h5>\r\n                            <h4>{props.operation.partners.length}</h4>\r\n                        </div>\r\n                    </Col>\r\n                    <Col className=\"operationInfo-Items\" md={6}>\r\n                        <div className=\"operationIcon\">\r\n                            <FontAwesomeIcon icon={faBell} color=\"#fff\" size=\"2x\" />\r\n                        </div>\r\n                        <div className=\"operationInfo\">\r\n                            <h5>{t(\"ActivitiesinField\")}</h5>\r\n                            <h4>{props.operation.timeline.length}</h4>\r\n                        </div>\r\n                    </Col>\r\n                </Row>\r\n            </div>\r\n        </>\r\n    )\r\n}\r\n\r\nexport default operationStats;", "\n    (window.__NEXT_P = window.__NEXT_P || []).push([\n      \"/operation/components/OperationStats\",\n      function () {\n        return require(\"private-next-pages/operation/components/OperationStats.tsx\");\n      }\n    ]);\n    if(module.hot) {\n      module.hot.dispose(function () {\n        window.__NEXT_P.push([\"/operation/components/OperationStats\"])\n      });\n    }\n  "], "names": ["props", "t", "useTranslation", "operationStats", "div", "className", "Row", "Col", "md", "FontAwesomeIcon", "icon", "faUsers", "color", "size", "h5", "h4", "operation", "partners", "length", "faBell", "timeline"], "sourceRoot": "", "ignoreList": []}