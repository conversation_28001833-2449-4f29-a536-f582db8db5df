{"version": 3, "file": "static/chunks/pages/updates/CalendarEventForm-9a247fff0635c670.js", "mappings": "iOAoFA,MA/D2BA,IACzB,GAAM,EAAGC,EAAW,CAAGC,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,EAAU,GACnC,YAAEC,CAAU,CA6DYC,EAAC,WA7DXC,CAAY,WAAEC,CAAS,SAAEC,CAAO,CAAE,CAAGP,EACnD,GAAEQ,CAAC,CAAE,CAAGC,CAAAA,EAAAA,EAAAA,EAAAA,CAAcA,CAAC,UAE7BC,CAAAA,EAAAA,EAAAA,SAAAA,CAASA,CAAC,KACG,MAAXH,EAAkBN,GAAW,GAASA,GAAW,EACnD,EAAG,CAACM,EAAQ,EAEZ,IAAMI,EAAgB,IACpBX,EAAMY,KAAK,CAACC,EACd,EAEMC,EAAgB,IACpBd,EAAMe,mBAAmB,CAACC,EAC5B,EAEA,MACE,WAACC,EAAAA,CAASA,CAAAA,CAACC,UAAU,WAAWC,KAAK,cACnC,UAACC,EAAAA,CAAGA,CAAAA,CAACF,UAAU,eAAeG,GAAI,YAChC,UAACC,KAAAA,UACC,UAACC,OAAAA,UAAMf,EAAE,qBAGb,WAACgB,EAAAA,CAAGA,CAAAA,WACF,WAACJ,EAAAA,CAAGA,CAAAA,WACF,WAACK,EAAAA,CAAIA,CAACC,KAAK,YACT,UAACD,EAAAA,CAAIA,CAACE,KAAK,EAACT,UAAU,kCAA0BV,EAAE,sBAClD,UAACoB,EAAAA,CAAaA,CAAAA,CACZC,SAAUvB,EACVwB,QAAS,IAAIC,KACbC,cAAc,IACdC,cAAe,GACfC,SAAU,GAAgB7B,EAAa8B,EAAM,aAC7CC,gBAAkB5B,EAAE,sBACpB6B,WAAW,6BAGe,IAAzBlC,EAAWG,SAAS,EAAc,CAACA,GAAgB,UAACgC,IAAAA,CAAEC,MAAO,CAAGC,MAAO,KAAK,WAAIhC,EAAE,uCAGzF,WAACY,EAAAA,CAAGA,CAAAA,WAEA,WAACK,EAAAA,CAAIA,CAACC,KAAK,YACT,UAACD,EAAAA,CAAIA,CAACE,KAAK,EAACT,UAAU,kCAA0BV,EAAE,oBAClD,UAACoB,EAAAA,CAAaA,CAAAA,CACZC,SAAUtB,EACVyB,cAAc,IACdC,cAAe,GACfC,SAAU,GAAgB7B,EAAa8B,EAAM,WAC7CC,gBAAiB5B,EAAE,sBACnBsB,QAASxB,EACT+B,WAAW,6BAGe,IAAzBlC,EAAWG,SAAS,EAAc,CAACC,GAAc,WAAC+B,IAAAA,CAAEC,MAAO,CAAGC,MAAO,KAAK,YAAIhC,EAAE,gCAAgC,aAI3H,UAACiC,EAAAA,OAAYA,CAAAA,CAACC,KAAM1C,EAAM0C,IAAI,CAAEC,QAAS3C,EAAM4C,MAAM,CAAEhC,MAAO,GAAQD,EAAcE,GAAKE,oBAAsBC,GAAcF,EAAcE,OAGjJ,wFCrEA,MANsB,GAElB,UAAC6B,EAAAA,EAAUA,CAAAA,CAAE,GAAG7C,CAAK,KAIV4B,aAAaA,EAAC,CCb7B,2CACA,6BACA,WACA,OAAe,EAAQ,IAAkD,CACzE,EACA,UAFsB", "sources": ["webpack://_N_E/./pages/updates/CalendarEventForm.tsx", "webpack://_N_E/./components/common/RKIDatePicker.tsx", "webpack://_N_E/?f1fe"], "sourcesContent": ["//Import Library\r\nimport React, { useEffect, useState } from \"react\";\r\nimport { Form, Container, Row, Col } from \"react-bootstrap\";\r\n\r\n//Import services/components\r\nimport DocumentForm from \"./DocumentForm\";\r\nimport RKIDatePicker from \"../../components/common/RKIDatePicker\";\r\nimport { useTranslation } from 'next-i18next';\r\n\r\n//TOTO refactor\r\ninterface CalendarEventFormProps {\r\n  validation: any;\r\n  onChangeDate: (date: Date, key: string) => void;\r\n  startDate: Date | null;\r\n  endDate: Date | null;\r\n  data: any[];\r\n  getId: (id: any[]) => void;\r\n  imgSrc: any[];\r\n  getSourceCollection: (docSrcArr: any[]) => void;\r\n}\r\n\r\nconst CalendarEventForm = (props: CalendarEventFormProps): React.ReactElement => {\r\n  const [, setEndDate] = useState<boolean>(false);\r\n  const { validation, onChangeDate, startDate, endDate } = props;\r\n  const { t } = useTranslation('common');\r\n\r\n  useEffect(() => {\r\n    endDate == null ? setEndDate(false) : setEndDate(true);\r\n  }, [endDate]);\r\n\r\n  const uploadHandler = (id: any[]) => {\r\n    props.getId(id);\r\n  };\r\n\r\n  const getSourceText = (imgSrcArr: any[]) => {\r\n    props.getSourceCollection(imgSrcArr);\r\n  }\r\n\r\n  return (\r\n    <Container className=\"formCard\" fluid>\r\n      <Col className=\"header-block\" lg={12}>\r\n        <h6>\r\n          <span>{t(\"update.Date\")}</span>\r\n        </h6>\r\n      </Col>\r\n      <Row>\r\n        <Col>\r\n          <Form.Group>\r\n            <Form.Label className=\"d-block required-field\">{t(\"update.StartDate\")}</Form.Label>\r\n            <RKIDatePicker\r\n              selected={startDate}\r\n              minDate={new Date()}\r\n              showTimeSelect\r\n              timeIntervals={15}\r\n              onChange={(date: Date) => onChangeDate(date, \"startDate\")}\r\n              placeholderText= {t(\"update.Selectadate\")}\r\n              dateFormat=\"MMMM d, yyyy h:mm aa\"\r\n            />\r\n          </Form.Group>\r\n          { (  validation.startDate === true && (!startDate)) &&  <p style={{  color: \"red\"}}>{t(\"update.Pleaseenterthestartdate\")}</p>}\r\n\r\n        </Col>\r\n        <Col>\r\n\r\n            <Form.Group>\r\n              <Form.Label className=\"d-block required-field\">{t(\"update.EndDate\")}</Form.Label>\r\n              <RKIDatePicker\r\n                selected={endDate}\r\n                showTimeSelect\r\n                timeIntervals={15}\r\n                onChange={(date: Date) => onChangeDate(date, \"endDate\")}\r\n                placeholderText={t(\"update.Selectadate\")}\r\n                minDate={startDate}\r\n                dateFormat=\"MMMM d, yyyy h:mm aa\"\r\n              />\r\n            </Form.Group>\r\n            { (  validation.startDate === true && (!endDate)) &&  <p style={{  color: \"red\"}}>{t(\"update.Pleaseentertheenddate\")} </p>}\r\n\r\n        </Col>\r\n      </Row>\r\n      <DocumentForm data={props.data} srcText={props.imgSrc} getId={(id) => uploadHandler(id)} getSourceCollection={(imgSrcArr) => getSourceText(imgSrcArr)} />\r\n    </Container>\r\n  );\r\n};\r\nexport default CalendarEventForm;\r\n", "//Import Library\r\nimport React from 'react'\r\nimport DatePicker from \"react-datepicker\";\r\n\r\ninterface RKIDatePickerProps {\r\n  [key: string]: any;\r\n}\r\n\r\nconst RKIDatePicker = (props: RKIDatePickerProps) => {\r\n  return (\r\n    <DatePicker {...props}  />\r\n  )\r\n};\r\n\r\nexport default RKIDatePicker;\r\n", "\n    (window.__NEXT_P = window.__NEXT_P || []).push([\n      \"/updates/CalendarEventForm\",\n      function () {\n        return require(\"private-next-pages/updates/CalendarEventForm.tsx\");\n      }\n    ]);\n    if(module.hot) {\n      module.hot.dispose(function () {\n        window.__NEXT_P.push([\"/updates/CalendarEventForm\"])\n      });\n    }\n  "], "names": ["props", "setEndDate", "useState", "validation", "CalendarEventForm", "onChangeDate", "startDate", "endDate", "t", "useTranslation", "useEffect", "uploadHandler", "getId", "id", "getSourceText", "getSourceCollection", "imgSrcArr", "Container", "className", "fluid", "Col", "lg", "h6", "span", "Row", "Form", "Group", "Label", "RKIDatePicker", "selected", "minDate", "Date", "showTimeSelect", "timeIntervals", "onChange", "date", "placeholderText", "dateFormat", "p", "style", "color", "DocumentForm", "data", "srcText", "imgSrc", "DatePicker"], "sourceRoot": "", "ignoreList": []}