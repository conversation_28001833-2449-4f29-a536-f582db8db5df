(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[1654],{57484:(e,r,t)=>{(window.__NEXT_P=window.__NEXT_P||[]).push(["/institution/InstitutionsFilter",function(){return t(86097)}])},67814:(e,r,t)=>{"use strict";t.d(r,{KF:()=>j});var a=t(14232),s=t(37876);!function(e,{insertAt:r}={}){if(!e||typeof document>"u")return;let t=document.head||document.getElementsByTagName("head")[0],a=document.createElement("style");a.type="text/css","top"===r&&t.firstChild?t.insertBefore(a,t.firstChild):t.appendChild(a),a.styleSheet?a.styleSheet.cssText=e:a.appendChild(document.createTextNode(e))}(`.rmsc{--rmsc-main: #4285f4;--rmsc-hover: #f1f3f5;--rmsc-selected: #e2e6ea;--rmsc-border: #ccc;--rmsc-gray: #aaa;--rmsc-bg: #fff;--rmsc-p: 10px;--rmsc-radius: 4px;--rmsc-h: 38px}.rmsc *{box-sizing:border-box;transition:all .2s ease}.rmsc .gray{color:var(--rmsc-gray)}.rmsc .dropdown-content{position:absolute;z-index:1;top:100%;width:100%;padding-top:8px}.rmsc .dropdown-content .panel-content{overflow:hidden;border-radius:var(--rmsc-radius);background:var(--rmsc-bg);box-shadow:0 0 0 1px #0000001a,0 4px 11px #0000001a}.rmsc .dropdown-container{position:relative;outline:0;background-color:var(--rmsc-bg);border:1px solid var(--rmsc-border);border-radius:var(--rmsc-radius)}.rmsc .dropdown-container[aria-disabled=true]:focus-within{box-shadow:var(--rmsc-gray) 0 0 0 1px;border-color:var(--rmsc-gray)}.rmsc .dropdown-container:focus-within{box-shadow:var(--rmsc-main) 0 0 0 1px;border-color:var(--rmsc-main)}.rmsc .dropdown-heading{position:relative;padding:0 var(--rmsc-p);display:flex;align-items:center;width:100%;height:var(--rmsc-h);cursor:default;outline:0}.rmsc .dropdown-heading .dropdown-heading-value{overflow:hidden;text-overflow:ellipsis;white-space:nowrap;flex:1}.rmsc .clear-selected-button{cursor:pointer;background:none;border:0;padding:0;display:flex}.rmsc .options{max-height:260px;overflow-y:auto;margin:0;padding-left:0}.rmsc .options li{list-style:none;margin:0}.rmsc .select-item{box-sizing:border-box;cursor:pointer;display:block;padding:var(--rmsc-p);outline-offset:-1px;outline-color:var(--rmsc-primary)}.rmsc .select-item:hover{background:var(--rmsc-hover)}.rmsc .select-item.selected{background:var(--rmsc-selected)}.rmsc .no-options{padding:var(--rmsc-p);text-align:center;color:var(--rmsc-gray)}.rmsc .search{width:100%;position:relative;border-bottom:1px solid var(--rmsc-border)}.rmsc .search input{background:none;height:var(--rmsc-h);padding:0 var(--rmsc-p);width:100%;outline:0;border:0;font-size:1em}.rmsc .search input:focus{background:var(--rmsc-hover)}.rmsc .search-clear-button{cursor:pointer;position:absolute;top:0;right:0;bottom:0;background:none;border:0;padding:0 calc(var(--rmsc-p) / 2)}.rmsc .search-clear-button [hidden]{display:none}.rmsc .item-renderer{display:flex;align-items:baseline}.rmsc .item-renderer input{margin:0 5px 0 0}.rmsc .item-renderer.disabled{opacity:.5}.rmsc .spinner{animation:rotate 2s linear infinite}.rmsc .spinner .path{stroke:var(--rmsc-border);stroke-width:4px;stroke-linecap:round;animation:dash 1.5s ease-in-out infinite}@keyframes rotate{to{transform:rotate(360deg)}}@keyframes dash{0%{stroke-dasharray:1,150;stroke-dashoffset:0}50%{stroke-dasharray:90,150;stroke-dashoffset:-35}to{stroke-dasharray:90,150;stroke-dashoffset:-124}}
`);var l={allItemsAreSelected:"All items are selected.",clearSearch:"Clear Search",clearSelected:"Clear Selected",noOptions:"No options",search:"Search",selectAll:"Select All",selectAllFiltered:"Select All (Filtered)",selectSomeItems:"Select...",create:"Create"},n={value:[],hasSelectAll:!0,className:"multi-select",debounceDuration:200,options:[]},o=a.createContext({}),i=({props:e,children:r})=>{let[t,i]=(0,a.useState)(e.options);return(0,a.useEffect)(()=>{i(e.options)},[e.options]),(0,s.jsx)(o.Provider,{value:{t:r=>{var t;return(null==(t=e.overrideStrings)?void 0:t[r])||l[r]},...n,...e,options:t,setOptions:i},children:r})},c=()=>a.useContext(o),d={when:!0,eventTypes:["keydown"]};function u(e,r,t){let s=(0,a.useMemo)(()=>Array.isArray(e)?e:[e],[e]),l=Object.assign({},d,t),{when:n,eventTypes:o}=l,i=(0,a.useRef)(r),{target:c}=l;(0,a.useEffect)(()=>{i.current=r});let u=(0,a.useCallback)(e=>{s.some(r=>e.key===r||e.code===r)&&i.current(e)},[s]);(0,a.useEffect)(()=>{if(n&&"u">typeof window){let e=c?c.current:window;return o.forEach(r=>{e&&e.addEventListener(r,u)}),()=>{o.forEach(r=>{e&&e.removeEventListener(r,u)})}}},[n,o,s,c,r])}var p={ARROW_DOWN:"ArrowDown",ARROW_UP:"ArrowUp",ENTER:"Enter",ESCAPE:"Escape",SPACE:"Space"},h=(e,r)=>{let t;return function(...a){clearTimeout(t),t=setTimeout(()=>{e.apply(null,a)},r)}},m=()=>(0,s.jsxs)("svg",{width:"24",height:"24",fill:"none",stroke:"currentColor",strokeWidth:"2",className:"dropdown-search-clear-icon gray",children:[(0,s.jsx)("line",{x1:"18",y1:"6",x2:"6",y2:"18"}),(0,s.jsx)("line",{x1:"6",y1:"6",x2:"18",y2:"18"})]}),x=({checked:e,option:r,onClick:t,disabled:a})=>(0,s.jsxs)("div",{className:`item-renderer ${a?"disabled":""}`,children:[(0,s.jsx)("input",{type:"checkbox",onChange:t,checked:e,tabIndex:-1,disabled:a}),(0,s.jsx)("span",{children:r.label})]}),v=({itemRenderer:e=x,option:r,checked:t,tabIndex:l,disabled:n,onSelectionChanged:o,onClick:i})=>{let c=(0,a.useRef)(),d=()=>{n||o(!t)};return u([p.ENTER,p.SPACE],e=>{d(),e.preventDefault()},{target:c}),(0,s.jsx)("label",{className:`select-item ${t?"selected":""}`,role:"option","aria-selected":t,tabIndex:l,ref:c,children:(0,s.jsx)(e,{option:r,checked:t,onClick:e=>{d(),i(e)},disabled:n})})},b=({options:e,onClick:r,skipIndex:t})=>{let{disabled:a,value:l,onChange:n,ItemRenderer:o}=c(),i=(e,r)=>{a||n(r?[...l,e]:l.filter(r=>r.value!==e.value))};return(0,s.jsx)(s.Fragment,{children:e.map((e,n)=>{let c=n+t;return(0,s.jsx)("li",{children:(0,s.jsx)(v,{tabIndex:c,option:e,onSelectionChanged:r=>i(e,r),checked:!!l.find(r=>r.value===e.value),onClick:e=>r(e,c),itemRenderer:o,disabled:e.disabled||a})},(null==e?void 0:e.key)||n)})})},f=()=>{let{t:e,onChange:r,options:t,setOptions:l,value:n,filterOptions:o,ItemRenderer:i,disabled:d,disableSearch:x,hasSelectAll:f,ClearIcon:g,debounceDuration:y,isCreatable:w,onCreateOption:k}=c(),j=(0,a.useRef)(),C=(0,a.useRef)(),[S,N]=(0,a.useState)(""),[A,E]=(0,a.useState)(t),[R,I]=(0,a.useState)(""),[_,O]=(0,a.useState)(0),T=(0,a.useCallback)(h(e=>I(e),y),[]),P=(0,a.useMemo)(()=>{let e=0;return x||(e+=1),f&&(e+=1),e},[x,f]),W={label:e(S?"selectAllFiltered":"selectAll"),value:""},M=e=>{let r=A.filter(e=>!e.disabled).map(e=>e.value);if(e){let e=[...n.map(e=>e.value),...r];return(o?A:t).filter(r=>e.includes(r.value))}return n.filter(e=>!r.includes(e.value))},D=()=>{var e;I(""),N(""),null==(e=null==C?void 0:C.current)||e.focus()},F=e=>O(e);u([p.ARROW_DOWN,p.ARROW_UP],e=>{switch(e.code){case p.ARROW_UP:$(-1);break;case p.ARROW_DOWN:$(1);break;default:return}e.stopPropagation(),e.preventDefault()},{target:j});let B=async()=>{let e={label:S,value:S,__isNew__:!0};k&&(e=await k(S)),l([e,...t]),D(),r([...n,e])},L=async()=>o?await o(t,R):function(e,r){return r?e.filter(({label:e,value:t})=>null!=e&&null!=t&&e.toLowerCase().includes(r.toLowerCase())):e}(t,R),$=e=>{let r=_+e;O(r=Math.min(r=Math.max(0,r),t.length+Math.max(P-1,0)))};(0,a.useEffect)(()=>{var e,r;null==(r=null==(e=null==j?void 0:j.current)?void 0:e.querySelector(`[tabIndex='${_}']`))||r.focus()},[_]);let[z,U]=(0,a.useMemo)(()=>{let e=A.filter(e=>!e.disabled);return[e.every(e=>-1!==n.findIndex(r=>r.value===e.value)),0!==e.length]},[A,n]);(0,a.useEffect)(()=>{L().then(E)},[R,t]);let q=(0,a.useRef)();u([p.ENTER],B,{target:q});let K=w&&S&&!A.some(e=>(null==e?void 0:e.value)===S);return(0,s.jsxs)("div",{className:"select-panel",role:"listbox",ref:j,children:[!x&&(0,s.jsxs)("div",{className:"search",children:[(0,s.jsx)("input",{placeholder:e("search"),type:"text","aria-describedby":e("search"),onChange:e=>{T(e.target.value),N(e.target.value),O(0)},onFocus:()=>{O(0)},value:S,ref:C,tabIndex:0}),(0,s.jsx)("button",{type:"button",className:"search-clear-button",hidden:!S,onClick:D,"aria-label":e("clearSearch"),children:g||(0,s.jsx)(m,{})})]}),(0,s.jsxs)("ul",{className:"options",children:[f&&U&&(0,s.jsx)(v,{tabIndex:+(1!==P),checked:z,option:W,onSelectionChanged:e=>{r(M(e))},onClick:()=>F(1),itemRenderer:i,disabled:d}),A.length?(0,s.jsx)(b,{skipIndex:P,options:A,onClick:(e,r)=>F(r)}):K?(0,s.jsx)("li",{onClick:B,className:"select-item creatable",tabIndex:1,ref:q,children:`${e("create")} "${S}"`}):(0,s.jsx)("li",{className:"no-options",children:e("noOptions")})]})]})},g=({expanded:e})=>(0,s.jsx)("svg",{width:"24",height:"24",fill:"none",stroke:"currentColor",strokeWidth:"2",className:"dropdown-heading-dropdown-arrow gray",children:(0,s.jsx)("path",{d:e?"M18 15 12 9 6 15":"M6 9L12 15 18 9"})}),y=()=>{let{t:e,value:r,options:t,valueRenderer:a}=c(),l=0===r.length,n=r.length===t.length,o=a&&a(r,t);return l?(0,s.jsx)("span",{className:"gray",children:o||e("selectSomeItems")}):(0,s.jsx)("span",{children:o||(n?e("allItemsAreSelected"):r.map(e=>e.label).join(", "))})},w=({size:e=24})=>(0,s.jsx)("span",{style:{width:e,marginRight:"0.2rem"},children:(0,s.jsx)("svg",{width:e,height:e,className:"spinner",viewBox:"0 0 50 50",style:{display:"inline",verticalAlign:"middle"},children:(0,s.jsx)("circle",{cx:"25",cy:"25",r:"20",fill:"none",className:"path"})})}),k=()=>{let{t:e,onMenuToggle:r,ArrowRenderer:t,shouldToggleOnHover:l,isLoading:n,disabled:o,onChange:i,labelledBy:d,value:h,isOpen:x,defaultIsOpen:v,ClearSelectedIcon:b,closeOnChangedValue:k}=c();(0,a.useEffect)(()=>{k&&N(!1)},[h]);let[j,C]=(0,a.useState)(!0),[S,N]=(0,a.useState)(v),[A,E]=(0,a.useState)(!1),R=(0,a.useRef)();(function(e,r){let t=(0,a.useRef)(!1);(0,a.useEffect)(()=>{t.current?e():t.current=!0},r)})(()=>{r&&r(S)},[S]),(0,a.useEffect)(()=>{void 0===v&&"boolean"==typeof x&&(C(!1),N(x))},[x]),u([p.ENTER,p.ARROW_DOWN,p.SPACE,p.ESCAPE],e=>{var r;["text","button"].includes(e.target.type)&&[p.SPACE,p.ENTER].includes(e.code)||(j&&(e.code===p.ESCAPE?(N(!1),null==(r=null==R?void 0:R.current)||r.focus()):N(!0)),e.preventDefault())},{target:R});let I=e=>{j&&l&&N(e)};return(0,s.jsxs)("div",{tabIndex:0,className:"dropdown-container","aria-labelledby":d,"aria-expanded":S,"aria-readonly":!0,"aria-disabled":o,ref:R,onFocus:()=>!A&&E(!0),onBlur:e=>{!e.currentTarget.contains(e.relatedTarget)&&j&&(E(!1),N(!1))},onMouseEnter:()=>I(!0),onMouseLeave:()=>I(!1),children:[(0,s.jsxs)("div",{className:"dropdown-heading",onClick:()=>{j&&N(!n&&!o&&!S)},children:[(0,s.jsx)("div",{className:"dropdown-heading-value",children:(0,s.jsx)(y,{})}),n&&(0,s.jsx)(w,{}),h.length>0&&null!==b&&(0,s.jsx)("button",{type:"button",className:"clear-selected-button",onClick:e=>{e.stopPropagation(),i([]),j&&N(!1)},disabled:o,"aria-label":e("clearSelected"),children:b||(0,s.jsx)(m,{})}),(0,s.jsx)(t||g,{expanded:S})]}),S&&(0,s.jsx)("div",{className:"dropdown-content",children:(0,s.jsx)("div",{className:"panel-content",children:(0,s.jsx)(f,{})})})]})},j=e=>(0,s.jsx)(i,{props:e,children:(0,s.jsx)("div",{className:`rmsc ${e.className||"multi-select"}`,children:(0,s.jsx)(k,{})})})},86097:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>m});var a=t(37876),s=t(14232),l=t(82851),n=t.n(l),o=t(49589),i=t(56970),c=t(37784),d=t(12697),u=t(67814),p=t(53718),h=t(31753);let m=e=>{let{filterText:r,onFilter:t,onFilterTypeChange:l,onClear:m,filterType:x,onFilterNetworkChange:v,filterNetwork:b}=e,[f,g]=(0,s.useState)([]),[y,w]=(0,s.useState)([]),{t:k}=(0,h.Bd)("common"),j=async e=>{let r=await p.A.get("/institutionType",e);r&&Array.isArray(r.data)&&g(n().map(r.data,e=>({label:e.title,value:e._id})))},C=async e=>{let r=await p.A.get("/institutionNetwork",e);r&&Array.isArray(r.data)&&w(r.data)};return(0,s.useEffect)(()=>{j({query:{},sort:{title:"asc"}}),C({query:{},sort:{title:"asc"}})},[]),(0,a.jsx)(o.A,{fluid:!0,className:"p-0",children:(0,a.jsxs)(i.A,{children:[(0,a.jsx)(c.A,{xs:4,className:"p-0",children:(0,a.jsx)(d.A,{type:"text",className:"searchInput",placeholder:k("search"),"aria-label":"Search",value:r,onChange:t})}),(0,a.jsx)(c.A,{xs:4,children:(0,a.jsx)(u.KF,{overrideStrings:{selectSomeItems:k("SelectType"),allItemsAreSelected:"All Types are Selected"},onChange:l,value:x,options:f,className:"select-type",labelledBy:k("SelectType")})}),(0,a.jsx)(c.A,{xs:4,className:"p-0",children:(0,a.jsxs)(d.A,{as:"select","aria-label":"Network","aria-placeholder":"Network",onChange:v,value:b,children:[(0,a.jsx)("option",{value:"",children:k("SelectNetwork")}),y.map((e,r)=>(0,a.jsx)("option",{value:e._id,children:e.title},r))]})})]})})}}},e=>{var r=r=>e(e.s=r);e.O(0,[636,6593,8792],()=>r(57484)),_N_E=e.O()}]);
//# sourceMappingURL=InstitutionsFilter-6152da4cdde18518.js.map