"use strict";exports.id=4764,exports.ids=[4764],exports.modules={25782:(e,t,i)=>{i.a(e,async(e,s)=>{try{i.r(t),i.d(t,{default:()=>p});var n=i(8732);i(82015);var a=i(7082),l=i(49481),r=i(83551),o=i(98178),c=i(88751),d=e([o]);o=(d.then?(await d)():d)[0];let p=e=>{let{t}=(0,c.useTranslation)("common"),i=t=>{e.getId(t)},s=t=>{e.getSourceCollection(t)};return(0,n.jsx)(a.A,{className:"formCard mt-0 p-0",fluid:!0,children:(0,n.jsxs)(l.A,{children:[(0,n.jsx)(r.A,{className:"header-block",lg:12,children:(0,n.jsx)("h6",{children:(0,n.jsx)("span",{children:t("update.Documents")})})}),(0,n.jsx)(r.A,{children:(0,n.jsx)(o.A,{datas:e.data,type:"application",srcText:e.srcText,getImgID:e=>i(e),getImageSource:e=>s(e)})})]})})};s()}catch(e){s(e)}})},80237:(e,t)=>{Object.defineProperty(t,"A",{enumerable:!0,get:function(){return i}});var i=function(e){return e.PAGES="PAGES",e.PAGES_API="PAGES_API",e.APP_PAGE="APP_PAGE",e.APP_ROUTE="APP_ROUTE",e.IMAGE="IMAGE",e}({})},81413:(e,t)=>{Object.defineProperty(t,"M",{enumerable:!0,get:function(){return function e(t,i){return i in t?t[i]:"then"in t&&"function"==typeof t.then?t.then(t=>e(t,i)):"function"==typeof t&&"default"===i?t:void 0}}})},98178:(e,t,i)=>{i.a(e,async(e,s)=>{try{i.d(t,{A:()=>I});var n=i(8732),a=i(82015),l=i(16029),r=i(82053),o=i(54131),c=i(49481),d=i(59549),p=i(91353),m=i(12403),x=i(27825),u=i.n(x),f=i(42893),g=i(63487),h=i(88751),y=e([o,f,g]);[o,f,g]=y.then?(await y)():y;let j=[],A={flex:1,display:"flex",flexDirection:"column",alignItems:"center",justifyContent:"center",width:"100%",height:"100%",borderWidth:.1,borderColor:"#fafafa",backgroundColor:"#fafafa",color:"black",transition:"border  .24s ease-in-out",padding:"15px"},v={display:"flex",padding:"10px",width:"100%",border:"2px solid gray",flexDirection:"column",justifyContent:"flex-start",flexWrap:"wrap",marginTop:20},w={width:"150px"},b={borderColor:"#2196f3"},I=e=>{let t,{t:i}=(0,h.useTranslation)("common"),[s,x]=(0,a.useState)(!1),[y,I]=(0,a.useState)(),P="application"==e.type?0x1400000:"20971520",[C,E]=(0,a.useState)([]),[S,D]=(0,a.useState)(!0),[F,N]=(0,a.useState)([]),_=e&&"application"===e.type?"/files":"/image",G=async e=>{await g.A.remove(`${_}/${e}`)},T=e=>{I(e),x(!0)},k=(e,t)=>{let i=[...F];i[t]=e.target.value,N(i)},z=t=>{switch(t&&t.name.split(".").pop()){case"JPG":case"jpg":case"jpeg":case"jpg":case"png":return(0,n.jsx)("img",{src:t.preview,style:w});case"pdf":return(0,n.jsx)("img",{src:"/images/fileIcons/pdfFile.png",className:"application"===e.type?"docPreview":"imgPreview"});case"docx":default:return(0,n.jsx)("img",{src:"/images/fileIcons/wordFile.png",className:"application"===e.type?"docPreview":"imgPreview"});case"xls":case"xlsx":return(0,n.jsx)("img",{src:"/images/fileIcons/xlsFile.png",className:"application"===e.type?"docPreview":"imgPreview"})}},O=()=>x(!1),U=()=>{x(!1)},L=t=>{let i=(t=y)&&t._id?{serverID:t._id}:{file:t},s=u().findIndex(j,i),n=[...F];n.splice(s,1),N(n),G(j[s].serverID),j.splice(s,1),e.getImgID(j,e.index?e.index:0);let a=[...C];a.splice(a.indexOf(t),1),E(a),x(!1)},R=C.map((t,a)=>(0,n.jsxs)("div",{children:[(0,n.jsx)(c.A,{xs:12,children:(0,n.jsxs)("div",{className:"row",children:[(0,n.jsx)(c.A,{md:4,lg:3,className:"application text-center align-self-center"===e.type?"docImagePreview text-center align-self-center":"imgPreview text-center align-self-center",children:z(t)}),(0,n.jsx)(c.A,{md:5,lg:7,className:"align-self-center",children:(0,n.jsxs)(d.A,{children:[(0,n.jsxs)(d.A.Group,{controlId:"filename",children:[(0,n.jsx)(d.A.Label,{className:"mt-2",children:i("FileName")}),(0,n.jsx)(d.A.Control,{size:"sm",type:"text",disabled:!0,value:t.original_name?t.original_name:t.name})]}),(0,n.jsxs)(d.A.Group,{controlId:"description",children:[(0,n.jsx)(d.A.Label,{children:"application"===e.type?i("ShortDescription/(Max255Characters)"):i("Source/Description")}),(0,n.jsx)(d.A.Control,{maxLength:"application"===e.type?255:void 0,size:"sm",type:"text",placeholder:"application"===e.type?i("`Enteryourdocumentdescription`"):i("`Enteryourimagesource/description`"),value:F[a],onChange:e=>k(e,a)})]})]})}),(0,n.jsx)(c.A,{md:3,lg:2,className:"align-self-center text-center",onClick:()=>T(t),children:(0,n.jsx)(p.A,{variant:"dark",children:i("Remove")})})]})}),(0,n.jsxs)(m.A,{show:s,onHide:O,children:[(0,n.jsx)(m.A.Header,{closeButton:!0,children:(0,n.jsx)(m.A.Title,{children:i("DeleteFile")})}),(0,n.jsx)(m.A.Body,{children:i("Areyousurewanttodeletethisfile?")}),(0,n.jsxs)(m.A.Footer,{children:[(0,n.jsx)(p.A,{variant:"secondary",onClick:U,children:i("Cancel")}),(0,n.jsx)(p.A,{variant:"primary",onClick:()=>L(t),children:i("yes")})]})]})]},a));(0,a.useEffect)(()=>{C.forEach(e=>URL.revokeObjectURL(e.preview)),j=[]},[]),(0,a.useEffect)(()=>{e.getImageSource(F)},[F]),(0,a.useEffect)(()=>{N(e.srcText)},[e.srcText]),(0,a.useEffect)(()=>{if(e&&"true"===e.singleUpload&&D(!1),e&&e.datas){let t=e.datas.map((t,i)=>(j.push({serverID:t._id,index:e.index?e.index:0,type:t.name.split(".")[1]}),{...t,preview:`http://localhost:3001/api/v1/image/show/${t._id}`}));E([...t])}},[e.datas]);let M=async(t,i)=>{if(t.length>i)try{let s=new FormData;s.append("file",t[i]);let n=await g.A.post(_,s,{"Content-Type":"multipart/form-data"});j.push({serverID:n._id,file:t[i],index:e.index?e.index:0,type:t[i].name.split(".")[1]}),M(t,i+1)}catch(e){M(t,i+1)}else e.getImgID(j,e.index?e.index:0)},B=(0,a.useCallback)(async e=>{await M(e,0);let t=e.map(e=>Object.assign(e,{preview:URL.createObjectURL(e)}));S?E(e=>[...e,...t]):E([...t])},[]),{getRootProps:W,getInputProps:$,isDragActive:H,isDragAccept:J,isDragReject:q,fileRejections:K}=(0,l.useDropzone)({accept:e&&e.type?"application/pdf, application/vnd.openxmlformats-officedocument.wordprocessingml.document, application/vnd.oasis.opendocument.text,application/vnd.openxmlformats-officedocument.spreadsheetml.sheet,text/csv":"image/*",multiple:S,minSize:0,maxSize:P,onDrop:B,validator:function(e){if("/image"===_){if("image"!==e.type.substring(0,5))return f.default.error(i("toast.filetypenotsupport")),{code:"file-invalid-type",message:"File type not supported"}}else if("/files"===_&&"image"===e.type.substring(0,5))return f.default.error(i("toast.filetypenotsupport")),{code:"file-invalid-type",message:"File type not supported"};return null}}),Q=(0,a.useMemo)(()=>({...A,...H?b:{outline:"2px dashed #bbb"},...J?{outline:"2px dashed #595959"}:{outline:"2px dashed #bbb"},...q?{outline:"2px dashed red"}:{activeStyle:b}}),[H,q]);t=e&&"application"===e.type?(0,n.jsx)("small",{style:{color:"#595959"},children:i("DocumentWeSupport")}):(0,n.jsx)("small",{style:{color:"#595959"},children:i("ImageWeSupport")});let V=K.length>0&&K[0].file.size>P;return(0,n.jsxs)(n.Fragment,{children:[(0,n.jsx)("div",{className:" d-flex justify-content-center align-items-center mt-3",style:{width:"100%",height:"180px"},children:(0,n.jsxs)("div",{...W({style:Q}),children:[(0,n.jsx)("input",{...$()}),(0,n.jsx)(r.FontAwesomeIcon,{icon:o.faCloudUploadAlt,size:"4x",color:"#999"}),(0,n.jsx)("p",{style:{color:"#595959",marginBottom:"0px"},children:i("Drag'n'dropsomefileshere,orclicktoselectfiles")}),!S&&(0,n.jsxs)("small",{style:{color:"#595959"},children:[(0,n.jsx)("b",{children:"Note:"})," One single image will be accepted"]}),t,(e.type,V&&(0,n.jsxs)("small",{className:"text-danger mt-2",children:[(0,n.jsx)(r.FontAwesomeIcon,{icon:o.faExclamationCircle,size:"1x",color:"red"})," ",i("FileistoolargeItshouldbelessthan20MB")]})),q&&(0,n.jsxs)("small",{className:"text-danger",style:{color:"#595959"},children:[(0,n.jsx)(r.FontAwesomeIcon,{icon:o.faExclamationCircle,size:"1x",color:"red"})," ",i("Filetypenotacceptedsorr")]})]})}),C.length>0&&(0,n.jsx)("div",{style:v,children:R})]})};s()}catch(e){s(e)}})}};