"use strict";(()=>{var e={};e.id=2737,e.ids=[636,2737,3220],e.modules={123:e=>{e.exports=require("dom-helpers/removeEventListener")},1332:e=>{e.exports=require("react-custom-scrollbars-2")},1428:e=>{e.exports=import("axios")},1680:e=>{e.exports=require("@restart/ui/utils")},1919:e=>{e.exports=require("react-transition-group/Transition")},3892:e=>{e.exports=require("classnames")},4048:e=>{e.exports=require("react-truncate")},6009:e=>{e.exports=require("dom-helpers/ownerDocument")},6952:e=>{e.exports=require("@restart/ui/Modal")},7374:e=>{e.exports=require("@restart/ui/Dropdown")},8732:e=>{e.exports=require("react/jsx-runtime")},9653:e=>{e.exports=require("@restart/ui/Overlay")},11242:e=>{e.exports=require("redux-persist/integration/react")},11688:e=>{e.exports=require("@restart/hooks/useWillUnmount")},12388:e=>{e.exports=require("dom-helpers/addEventListener")},13364:e=>{e.exports=require("redux-saga/effects")},14062:e=>{e.exports=import("react-redux")},14078:e=>{e.exports=import("swr")},14332:e=>{e.exports=require("uncontrollable")},16029:e=>{e.exports=require("react-dropzone")},16116:e=>{e.exports=require("invariant")},18622:e=>{e.exports=require("yup")},19121:e=>{e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},22326:e=>{e.exports=require("react-dom")},22541:e=>{e.exports=require("redux-persist/lib/storage")},25303:e=>{e.exports=require("@restart/ui/NavItem")},26324:e=>{e.exports=require("warning")},27825:e=>{e.exports=require("lodash")},27910:e=>{e.exports=require("stream")},28217:e=>{e.exports=require("@restart/ui/DropdownItem")},29021:e=>{e.exports=require("fs")},29294:e=>{e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},29628:e=>{e.exports=require("dom-helpers/addClass")},29780:e=>{e.exports=require("react-datepicker")},29825:e=>{e.exports=require("prop-types")},29841:e=>{e.exports=require("dom-helpers/hasClass")},30362:e=>{e.exports=require("@restart/ui/DropdownToggle")},31845:(e,r,t)=>{t.a(e,async(e,s)=>{try{t.r(r),t.d(r,{default:()=>x,getServerSideProps:()=>n});var o=t(8732),a=t(44233),i=t(49679),u=t(35576),p=e([i]);async function n({locale:e}){return{props:{...await (0,u.serverSideTranslations)(e,["common"])}}}i=(p.then?(await p)():p)[0];let x=()=>{let e=(0,a.useRouter)();switch((e.query.routes||[])[0]){case"add":case"edit":return(0,o.jsx)(i.default,{router:e});default:return null}};s()}catch(e){s(e)}})},33873:e=>{e.exports=require("path")},35576:e=>{e.exports=require("next-i18next/serverSideTranslations")},36653:e=>{e.exports=require("nprogress")},36955:e=>{e.exports=require("@restart/hooks/useBreakpoint")},37766:e=>{e.exports=require("@restart/hooks/useMergedRefs")},39756:e=>{e.exports=import("redux")},40051:e=>{e.exports=require("dom-helpers/removeClass")},40361:e=>{e.exports=require("next/dist/compiled/next-server/pages.runtime.prod.js")},42893:e=>{e.exports=import("react-hot-toast")},43294:e=>{e.exports=require("formik")},50009:e=>{e.exports=require("@restart/ui/DropdownMenu")},54131:e=>{e.exports=import("@fortawesome/free-solid-svg-icons")},57664:e=>{e.exports=require("dom-helpers/scrollbarSize")},58928:e=>{e.exports=require("dom-helpers/canUseDOM")},59717:e=>{e.exports=require("@restart/ui/DropdownContext")},60560:e=>{e.exports=require("dom-helpers/contains")},63033:e=>{e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},65447:e=>{e.exports=require("@restart/ui/Button")},67364:e=>{e.exports=require("@restart/hooks/useCallbackRef")},68455:e=>{e.exports=import("redux-saga")},69722:e=>{e.exports=require("es6-promise")},74075:e=>{e.exports=require("zlib")},74716:e=>{e.exports=require("moment")},74987:e=>{e.exports=require("@restart/ui/ModalManager")},78097:e=>{e.exports=require("next-redux-saga")},78634:e=>{e.exports=require("@restart/ui/Anchor")},81366:e=>{e.exports=require("redux-auth-wrapper/connectedAuthWrapper")},81521:e=>{e.exports=require("dom-helpers/querySelectorAll")},81895:e=>{e.exports=require("@restart/hooks/useEventCallback")},82015:e=>{e.exports=require("react")},82053:e=>{e.exports=require("@fortawesome/react-fontawesome")},83507:(e,r,t)=>{t.a(e,async(e,s)=>{try{t.r(r),t.d(r,{config:()=>g,default:()=>l,getServerSideProps:()=>m,getStaticPaths:()=>c,getStaticProps:()=>q,reportWebVitals:()=>h,routeModule:()=>P,unstable_getServerProps:()=>b,unstable_getServerSideProps:()=>w,unstable_getStaticParams:()=>f,unstable_getStaticPaths:()=>v,unstable_getStaticProps:()=>S});var o=t(63885),a=t(80237),i=t(81413),u=t(9616),p=t.n(u),n=t(72386),x=t(31845),d=e([n,x]);[n,x]=d.then?(await d)():d;let l=(0,i.M)(x,"default"),q=(0,i.M)(x,"getStaticProps"),c=(0,i.M)(x,"getStaticPaths"),m=(0,i.M)(x,"getServerSideProps"),g=(0,i.M)(x,"config"),h=(0,i.M)(x,"reportWebVitals"),S=(0,i.M)(x,"unstable_getStaticProps"),v=(0,i.M)(x,"unstable_getStaticPaths"),f=(0,i.M)(x,"unstable_getStaticParams"),b=(0,i.M)(x,"unstable_getServerProps"),w=(0,i.M)(x,"unstable_getServerSideProps"),P=new o.PagesRouteModule({definition:{kind:a.A.PAGES,page:"/updates/[...routes]",pathname:"/updates/[...routes]",bundlePath:"",filename:""},components:{App:n.default,Document:p()},userland:x});s()}catch(e){s(e)}})},86842:e=>{e.exports=require("@restart/ui/SelectableContext")},87571:e=>{e.exports=require("dom-helpers/transitionEnd")},88751:e=>{e.exports=require("next-i18next")},93787:e=>{e.exports=require("redux-persist")},94947:e=>{e.exports=require("@restart/hooks/useTimeout")},96196:e=>{e.exports=require("next-redux-wrapper")},98320:e=>{e.exports=require("@restart/hooks/useIsomorphicEffect")},99460:e=>{e.exports=require("dom-helpers/css")}};var r=require("../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[6089,9216,9616,2386,4764,9679],()=>t(83507));module.exports=s})();