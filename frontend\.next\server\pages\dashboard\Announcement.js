"use strict";(()=>{var e={};e.id=4834,e.ids=[636,3220,4834],e.modules={123:e=>{e.exports=require("dom-helpers/removeEventListener")},1332:e=>{e.exports=require("react-custom-scrollbars-2")},1428:e=>{e.exports=import("axios")},1680:e=>{e.exports=require("@restart/ui/utils")},1919:e=>{e.exports=require("react-transition-group/Transition")},3892:e=>{e.exports=require("classnames")},4048:e=>{e.exports=require("react-truncate")},6009:e=>{e.exports=require("dom-helpers/ownerDocument")},6952:e=>{e.exports=require("@restart/ui/Modal")},7374:e=>{e.exports=require("@restart/ui/Dropdown")},8732:e=>{e.exports=require("react/jsx-runtime")},9653:e=>{e.exports=require("@restart/ui/Overlay")},11242:e=>{e.exports=require("redux-persist/integration/react")},11688:e=>{e.exports=require("@restart/hooks/useWillUnmount")},12388:e=>{e.exports=require("dom-helpers/addEventListener")},13364:e=>{e.exports=require("redux-saga/effects")},13408:e=>{e.exports=require("@restart/hooks/useUpdateEffect")},14062:e=>{e.exports=import("react-redux")},14078:e=>{e.exports=import("swr")},14332:e=>{e.exports=require("uncontrollable")},16116:e=>{e.exports=require("invariant")},18622:e=>{e.exports=require("yup")},19121:e=>{e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},22326:e=>{e.exports=require("react-dom")},22541:e=>{e.exports=require("redux-persist/lib/storage")},25303:e=>{e.exports=require("@restart/ui/NavItem")},26324:e=>{e.exports=require("warning")},27825:e=>{e.exports=require("lodash")},27910:e=>{e.exports=require("stream")},28217:e=>{e.exports=require("@restart/ui/DropdownItem")},29021:e=>{e.exports=require("fs")},29294:e=>{e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},29628:e=>{e.exports=require("dom-helpers/addClass")},29825:e=>{e.exports=require("prop-types")},29841:e=>{e.exports=require("dom-helpers/hasClass")},30362:e=>{e.exports=require("@restart/ui/DropdownToggle")},33873:e=>{e.exports=require("path")},36653:e=>{e.exports=require("nprogress")},36955:e=>{e.exports=require("@restart/hooks/useBreakpoint")},37766:e=>{e.exports=require("@restart/hooks/useMergedRefs")},39756:e=>{e.exports=import("redux")},40051:e=>{e.exports=require("dom-helpers/removeClass")},40361:e=>{e.exports=require("next/dist/compiled/next-server/pages.runtime.prod.js")},42051:e=>{e.exports=require("@restart/hooks/useCommittedRef")},42893:e=>{e.exports=import("react-hot-toast")},43294:e=>{e.exports=require("formik")},50009:e=>{e.exports=require("@restart/ui/DropdownMenu")},55176:(e,r,t)=>{t.a(e,async(e,s)=>{try{t.r(r),t.d(r,{default:()=>m});var a=t(8732),o=t(82015),i=t(83551),n=t(7082);t(27825);var u=t(13866),p=t.n(u),l=t(20174),x=t(85795),c=t(63487),d=e([c]);function h(e){let{announcements:r}=e;return(0,a.jsx)("div",{children:r.map((e,r)=>(0,a.jsx)(i.A,{className:"announcementItem",children:(0,a.jsx)(x.default,{item:e})},r))})}c=(d.then?(await d)():d)[0];let m=function({t:e}){let[r,t]=(0,o.useState)([]),[s,u]=(0,o.useState)(0),[x]=(0,o.useState)(3),c=e=>{let t=s,[a,o]=[0,x-1];"next"===e&&t<o?t++:"prev"===e&&t>0&&t--,r.length-t==1&&(t=r.length-1),r.length-t==0&&(t=1),u(t)};return(0,a.jsx)("div",{className:"announcements",children:r&&r.length>0?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(n.A,{fluid:!0,children:(0,a.jsxs)(i.A,{children:[(0,a.jsx)(l.Ay,{xs:10,className:"p-0",children:(0,a.jsx)("h4",{children:e("announcements")})}),r&&r.length>1?(0,a.jsx)(l.Ay,{xs:2,className:"text-end carousel-control p-0",children:(0,a.jsxs)("div",{className:"carousel-navigation",children:[(0,a.jsx)("a",{className:"left carousel-control",onClick:()=>c("prev"),children:(0,a.jsx)("i",{className:"fa fa-chevron-left"})}),(0,a.jsx)("a",{className:"right carousel-control",onClick:()=>c("next"),children:(0,a.jsx)("i",{className:"fa fa-chevron-right"})})]})}):null]})}),(0,a.jsx)(n.A,{fluid:!0,children:(0,a.jsx)(i.A,{children:(0,a.jsx)(l.Ay,{xs:12,className:"p-0",children:(0,a.jsx)(p(),{indicators:!1,controls:!1,interval:null,activeIndex:s,children:r.map((e,r)=>(0,a.jsx)(p().Item,{children:(0,a.jsx)(h,{announcements:e})},r))})})})})]}):(0,a.jsx)(n.A,{fluid:!0,children:(0,a.jsxs)(i.A,{children:[(0,a.jsx)(l.Ay,{xs:10,className:"p-0",children:(0,a.jsx)("h4",{children:e("announcements")})}),(0,a.jsxs)(l.Ay,{xs:12,className:"p-0",children:[(0,a.jsx)("div",{className:"border p-3",children:e("NoAnnouncementsavailabletodisplay")}),(0,a.jsx)("br",{})]})]})})})};s()}catch(e){s(e)}})},57664:e=>{e.exports=require("dom-helpers/scrollbarSize")},58928:e=>{e.exports=require("dom-helpers/canUseDOM")},59717:e=>{e.exports=require("@restart/ui/DropdownContext")},60560:e=>{e.exports=require("dom-helpers/contains")},63033:e=>{e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},65447:e=>{e.exports=require("@restart/ui/Button")},67364:e=>{e.exports=require("@restart/hooks/useCallbackRef")},68455:e=>{e.exports=import("redux-saga")},69722:e=>{e.exports=require("es6-promise")},74075:e=>{e.exports=require("zlib")},74716:e=>{e.exports=require("moment")},74987:e=>{e.exports=require("@restart/ui/ModalManager")},76863:(e,r,t)=>{t.a(e,async(e,s)=>{try{t.r(r),t.d(r,{config:()=>q,default:()=>c,getServerSideProps:()=>m,getStaticPaths:()=>h,getStaticProps:()=>d,reportWebVitals:()=>g,routeModule:()=>y,unstable_getServerProps:()=>b,unstable_getServerSideProps:()=>S,unstable_getStaticParams:()=>f,unstable_getStaticPaths:()=>v,unstable_getStaticProps:()=>j});var a=t(63885),o=t(80237),i=t(81413),n=t(9616),u=t.n(n),p=t(72386),l=t(55176),x=e([p,l]);[p,l]=x.then?(await x)():x;let c=(0,i.M)(l,"default"),d=(0,i.M)(l,"getStaticProps"),h=(0,i.M)(l,"getStaticPaths"),m=(0,i.M)(l,"getServerSideProps"),q=(0,i.M)(l,"config"),g=(0,i.M)(l,"reportWebVitals"),j=(0,i.M)(l,"unstable_getStaticProps"),v=(0,i.M)(l,"unstable_getStaticPaths"),f=(0,i.M)(l,"unstable_getStaticParams"),b=(0,i.M)(l,"unstable_getServerProps"),S=(0,i.M)(l,"unstable_getServerSideProps"),y=new a.PagesRouteModule({definition:{kind:o.A.PAGES,page:"/dashboard/Announcement",pathname:"/dashboard/Announcement",bundlePath:"",filename:""},components:{App:p.default,Document:u()},userland:l});s()}catch(e){s(e)}})},78097:e=>{e.exports=require("next-redux-saga")},78634:e=>{e.exports=require("@restart/ui/Anchor")},81366:e=>{e.exports=require("redux-auth-wrapper/connectedAuthWrapper")},81521:e=>{e.exports=require("dom-helpers/querySelectorAll")},81895:e=>{e.exports=require("@restart/hooks/useEventCallback")},82015:e=>{e.exports=require("react")},85795:(e,r,t)=>{t.r(r),t.d(r,{default:()=>n});var s=t(8732),a=t(49481),o=t(19918),i=t.n(o);function n(e){let{item:r}=e,t=r.images.length-1,o=`parent_${r.type}`;return(0,s.jsxs)(a.A,{className:"p-0",xs:12,children:[(0,s.jsx)(i(),{href:`/${r.type}/[...routes]`,as:`/${r.type}/show/${r[o]}/update/${r._id}`,children:r.images&&r.images[t]?(0,s.jsx)("img",{src:`http://localhost:3001/api/v1/image/show/${r.images[t]._id}`,alt:"announcement",className:"announceImg"}):(0,s.jsx)("i",{className:"fa fa-bullhorn announceImg"})}),(0,s.jsxs)("div",{className:"announceDesc",children:[(0,s.jsx)(i(),{href:`/${r.type}/[...routes]`,as:`/${r.type}/show/${r[o]}/update/${r._id}`,children:r&&r.title?r.title:""}),(0,s.jsx)("p",{children:r&&r.description?(e=>{let r=document.createElement("div");r.innerHTML=e;let t=r.textContent||r.innerText||"";return t.length>260?`${t.substring(0,257)}...`:t})(r.description):null})]})]})}},86842:e=>{e.exports=require("@restart/ui/SelectableContext")},87571:e=>{e.exports=require("dom-helpers/transitionEnd")},88751:e=>{e.exports=require("next-i18next")},93787:e=>{e.exports=require("redux-persist")},94947:e=>{e.exports=require("@restart/hooks/useTimeout")},96196:e=>{e.exports=require("next-redux-wrapper")},98320:e=>{e.exports=require("@restart/hooks/useIsomorphicEffect")},99460:e=>{e.exports=require("dom-helpers/css")}};var r=require("../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[6089,9216,9616,4033,2386],()=>t(76863));module.exports=s})();