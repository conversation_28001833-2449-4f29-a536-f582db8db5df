{"version": 3, "file": "static/chunks/pages/adminsettings/mailsettings-3e73da7c8c303914.js", "mappings": "2HAEA,MAFoB,IAAM,oBCC1B,IDCeA,ICDf,ODC0BA,CCD1B,CDC2B,OCD3B,oBACA,8BACA,WACA,OAAe,EAAQ,KAAyD,CAChF,EACA,SAFsB", "sources": ["webpack://_N_E/./pages/adminsettings/mailsettings/index.tsx", "webpack://_N_E/?00a8"], "sourcesContent": ["const MailSetting = () => \"\";\r\n\r\nexport default MailSetting;", "\n    (window.__NEXT_P = window.__NEXT_P || []).push([\n      \"/adminsettings/mailsettings\",\n      function () {\n        return require(\"private-next-pages/adminsettings/mailsettings/index.tsx\");\n      }\n    ]);\n    if(module.hot) {\n      module.hot.dispose(function () {\n        window.__NEXT_P.push([\"/adminsettings/mailsettings\"])\n      });\n    }\n  "], "names": ["MailSetting"], "sourceRoot": "", "ignoreList": []}