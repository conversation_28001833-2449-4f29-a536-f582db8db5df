"use strict";(()=>{var e={};e.id=85,e.ids=[85,636,3220],e.modules={123:e=>{e.exports=require("dom-helpers/removeEventListener")},1332:e=>{e.exports=require("react-custom-scrollbars-2")},1428:e=>{e.exports=import("axios")},1680:e=>{e.exports=require("@restart/ui/utils")},1919:e=>{e.exports=require("react-transition-group/Transition")},3892:e=>{e.exports=require("classnames")},4048:e=>{e.exports=require("react-truncate")},6009:e=>{e.exports=require("dom-helpers/ownerDocument")},6952:e=>{e.exports=require("@restart/ui/Modal")},7374:e=>{e.exports=require("@restart/ui/Dropdown")},8732:e=>{e.exports=require("react/jsx-runtime")},9653:e=>{e.exports=require("@restart/ui/Overlay")},11242:e=>{e.exports=require("redux-persist/integration/react")},11688:e=>{e.exports=require("@restart/hooks/useWillUnmount")},12388:e=>{e.exports=require("dom-helpers/addEventListener")},13054:(e,r,t)=>{t.a(e,async(e,s)=>{try{t.d(r,{A:()=>b});var a=t(8732),o=t(64801),n=t(74716),i=t.n(n);t(16444);var u=t(44233),p=t.n(u),l=t(7082),c=t(83551),d=t(49481),x=t(82053),m=t(54131),h=t(27825),q=t.n(h),f=t(88751),g=e([m]);m=(g.then?(await g)():g)[0];let P=(0,o.momentLocalizer)(i());function v(e){let{t:r,i18n:t}=(0,f.useTranslation)("common"),s=t.language,{eventsList:n,style:i,minicalendar:u,views:l}=e,c={};return e.showEventCounts&&(c={eventWrapper:A,month:{dateHeader:e=>(0,a.jsx)(w,{eventsList:n,...e})}}),u&&(c.toolbar=y),(0,a.jsx)(o.Calendar,{culture:s,localizer:P,events:n,views:l,startAccessor:"start_date",endAccessor:"end_date",style:i,components:c,messages:{today:r("today"),previous:r("back"),next:r("Next"),month:r("Month"),week:r("Week"),day:r("Day")},onSelectEvent:e=>{let r=Object.keys(e).filter(e=>e.includes("parent")).toString(),t=r.split("_")[1];p().push(`/${t}/show/${e[r]}/update/${e._id}`)}})}function y(e){return(0,a.jsx)(l.A,{className:"mb-1",children:(0,a.jsxs)(c.A,{children:[(0,a.jsx)(d.A,{className:"p-0",md:1,children:(0,a.jsx)("i",{style:{cursor:"pointer"},onClick:()=>e.onNavigate("PREV"),className:"fas fa-chevron-left"})}),(0,a.jsx)(d.A,{className:"text-center",md:10,children:(0,a.jsx)("span",{className:"rbc-toolbar-label",children:e.label})}),(0,a.jsx)(d.A,{className:"p-0 text-end",md:1,children:(0,a.jsx)("i",{style:{cursor:"pointer"},onClick:()=>e.onNavigate("NEXT"),className:"fas fa-chevron-right"})})]})})}v.defaultProps={minicalendar:!1,views:["month"]};let S=(e,r)=>{let t=0;return q().forEach(e,e=>{let s=i()(e.start_date).set({hour:0,minute:0,second:0,millisecond:0}),a=i()(e.end_date).set({hour:0,minute:0,second:0,millisecond:0});i()(r).isBetween(s,a,null,"[]")&&(t+=1)}),t},w=({date:e,label:r,eventsList:t})=>{let s=S(t,e),o=i()(e).isBefore(new Date,"day");return(0,a.jsxs)("div",{className:"rbc-date-cell",onClick:()=>p().push("/events-calendar"),children:[(0,a.jsx)("a",{href:"#",children:r}),s>0&&(0,a.jsxs)("span",{className:"d-flex justify-content-start align-items-center fa-stack",children:[(0,a.jsx)(x.FontAwesomeIcon,{icon:m.faStar,color:o?"grey":"#04A6FB",size:"lg"}),(0,a.jsx)("span",{className:"eventCount",children:s})]})]})},A=e=>(0,a.jsx)("div",{onSelect:e.onSelect}),b=v;s()}catch(e){s(e)}})},13364:e=>{e.exports=require("redux-saga/effects")},14062:e=>{e.exports=import("react-redux")},14078:e=>{e.exports=import("swr")},14332:e=>{e.exports=require("uncontrollable")},16116:e=>{e.exports=require("invariant")},16444:e=>{e.exports=require("moment/locale/fr")},18622:e=>{e.exports=require("yup")},19121:e=>{e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},22326:e=>{e.exports=require("react-dom")},22431:(e,r,t)=>{t.a(e,async(e,s)=>{try{t.r(r),t.d(r,{config:()=>q,default:()=>d,getServerSideProps:()=>h,getStaticPaths:()=>m,getStaticProps:()=>x,reportWebVitals:()=>f,routeModule:()=>w,unstable_getServerProps:()=>P,unstable_getServerSideProps:()=>S,unstable_getStaticParams:()=>y,unstable_getStaticPaths:()=>v,unstable_getStaticProps:()=>g});var a=t(63885),o=t(80237),n=t(81413),i=t(9616),u=t.n(i),p=t(72386),l=t(68353),c=e([p,l]);[p,l]=c.then?(await c)():c;let d=(0,n.M)(l,"default"),x=(0,n.M)(l,"getStaticProps"),m=(0,n.M)(l,"getStaticPaths"),h=(0,n.M)(l,"getServerSideProps"),q=(0,n.M)(l,"config"),f=(0,n.M)(l,"reportWebVitals"),g=(0,n.M)(l,"unstable_getStaticProps"),v=(0,n.M)(l,"unstable_getStaticPaths"),y=(0,n.M)(l,"unstable_getStaticParams"),P=(0,n.M)(l,"unstable_getServerProps"),S=(0,n.M)(l,"unstable_getServerSideProps"),w=new a.PagesRouteModule({definition:{kind:o.A.PAGES,page:"/events-calendar/[...routes]",pathname:"/events-calendar/[...routes]",bundlePath:"",filename:""},components:{App:p.default,Document:u()},userland:l});s()}catch(e){s(e)}})},22541:e=>{e.exports=require("redux-persist/lib/storage")},25303:e=>{e.exports=require("@restart/ui/NavItem")},26324:e=>{e.exports=require("warning")},27825:e=>{e.exports=require("lodash")},27910:e=>{e.exports=require("stream")},28217:e=>{e.exports=require("@restart/ui/DropdownItem")},29021:e=>{e.exports=require("fs")},29294:e=>{e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},29628:e=>{e.exports=require("dom-helpers/addClass")},29825:e=>{e.exports=require("prop-types")},29841:e=>{e.exports=require("dom-helpers/hasClass")},30362:e=>{e.exports=require("@restart/ui/DropdownToggle")},33873:e=>{e.exports=require("path")},35507:(e,r,t)=>{t.a(e,async(e,s)=>{try{t.r(r),t.d(r,{default:()=>q,getStaticProps:()=>h});var a=t(8732),o=t(7082),n=t(83551),i=t(49481),u=t(27825),p=t.n(u),l=t(82015),c=t(13054),d=t(63487),x=t(35576),m=e([c,d]);async function h({locale:e}){return{props:{...await (0,x.serverSideTranslations)(e,["common"])}}}[c,d]=m.then?(await m)():m;let q=e=>{let[r,t]=(0,l.useState)([]),s=async()=>{let e=await d.A.get("/updateType",{query:{title:"Calendar Event"}});e&&e.data&&u(e.data[0]._id)},u=async e=>{let r=await d.A.get("/updates",{query:{update_type:e},sort:{created_at:"desc"},limit:20,select:"-created_at -updated_at -update_type -contact_details -description -document -images -link -media -parent_operation -reply -show_as_announcement -type"});r&&r.data&&(p().forEach(r.data,function(e,t){r.data[t].start_date=new Date(e.start_date),r.data[t].end_date=new Date(e.end_date)}),t(r.data))};return(0,l.useEffect)(()=>{s()},[]),(0,a.jsx)(o.A,{children:(0,a.jsx)(n.A,{children:(0,a.jsx)(i.A,{className:"pe-0",xs:"12",children:(0,a.jsx)(c.A,{views:["month","week","day"],style:{height:800},eventsList:r})})})})};s()}catch(e){s(e)}})},35576:e=>{e.exports=require("next-i18next/serverSideTranslations")},36653:e=>{e.exports=require("nprogress")},36955:e=>{e.exports=require("@restart/hooks/useBreakpoint")},37766:e=>{e.exports=require("@restart/hooks/useMergedRefs")},39756:e=>{e.exports=import("redux")},40051:e=>{e.exports=require("dom-helpers/removeClass")},40361:e=>{e.exports=require("next/dist/compiled/next-server/pages.runtime.prod.js")},42893:e=>{e.exports=import("react-hot-toast")},43294:e=>{e.exports=require("formik")},50009:e=>{e.exports=require("@restart/ui/DropdownMenu")},54131:e=>{e.exports=import("@fortawesome/free-solid-svg-icons")},57664:e=>{e.exports=require("dom-helpers/scrollbarSize")},58928:e=>{e.exports=require("dom-helpers/canUseDOM")},59717:e=>{e.exports=require("@restart/ui/DropdownContext")},60560:e=>{e.exports=require("dom-helpers/contains")},63033:e=>{e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},64801:e=>{e.exports=require("react-big-calendar")},65447:e=>{e.exports=require("@restart/ui/Button")},67364:e=>{e.exports=require("@restart/hooks/useCallbackRef")},68353:(e,r,t)=>{t.a(e,async(e,s)=>{try{t.r(r),t.d(r,{default:()=>l,getServerSideProps:()=>p});var a=t(8732),o=t(44233),n=t(35507),i=t(35576),u=e([n]);async function p({locale:e}){return{props:{...await (0,i.serverSideTranslations)(e,["common"])}}}n=(u.then?(await u)():u)[0];let l=()=>{let e=(0,o.useRouter)().query.routes||[];return"events-calendar"===e[0]?(0,a.jsx)(n.default,{routes:e}):null};s()}catch(e){s(e)}})},68455:e=>{e.exports=import("redux-saga")},69722:e=>{e.exports=require("es6-promise")},74075:e=>{e.exports=require("zlib")},74716:e=>{e.exports=require("moment")},74987:e=>{e.exports=require("@restart/ui/ModalManager")},78097:e=>{e.exports=require("next-redux-saga")},78634:e=>{e.exports=require("@restart/ui/Anchor")},80237:(e,r)=>{Object.defineProperty(r,"A",{enumerable:!0,get:function(){return t}});var t=function(e){return e.PAGES="PAGES",e.PAGES_API="PAGES_API",e.APP_PAGE="APP_PAGE",e.APP_ROUTE="APP_ROUTE",e.IMAGE="IMAGE",e}({})},81366:e=>{e.exports=require("redux-auth-wrapper/connectedAuthWrapper")},81413:(e,r)=>{Object.defineProperty(r,"M",{enumerable:!0,get:function(){return function e(r,t){return t in r?r[t]:"then"in r&&"function"==typeof r.then?r.then(r=>e(r,t)):"function"==typeof r&&"default"===t?r:void 0}}})},81521:e=>{e.exports=require("dom-helpers/querySelectorAll")},81895:e=>{e.exports=require("@restart/hooks/useEventCallback")},82015:e=>{e.exports=require("react")},82053:e=>{e.exports=require("@fortawesome/react-fontawesome")},86842:e=>{e.exports=require("@restart/ui/SelectableContext")},87571:e=>{e.exports=require("dom-helpers/transitionEnd")},88751:e=>{e.exports=require("next-i18next")},93787:e=>{e.exports=require("redux-persist")},94947:e=>{e.exports=require("@restart/hooks/useTimeout")},96196:e=>{e.exports=require("next-redux-wrapper")},98320:e=>{e.exports=require("@restart/hooks/useIsomorphicEffect")},99460:e=>{e.exports=require("dom-helpers/css")}};var r=require("../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[6089,9216,9616,2386],()=>t(22431));module.exports=s})();