(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[9424],{21244:(e,a,s)=>{"use strict";s.r(a),s.d(a,{default:()=>u});var t=s(37876),n=s(48230),r=s.n(n),l=s(14232),i=s(31195),o=s(60282),c=s(50749),d=s(53718);let u=function(e){let[a,s]=(0,l.useState)([]),[,n]=(0,l.useState)(!1),[u,h]=(0,l.useState)(0),[g,x]=(0,l.useState)(10),[m,p]=(0,l.useState)(!1),[j,A]=(0,l.useState)({}),w={sort:{created_at:"desc"},limit:g,page:1,query:{}},_=[{name:"<PERSON>rna<PERSON>",selector:"username",cell:e=>e.username},{name:"<PERSON><PERSON>",selector:"email",cell:e=>e.email},{name:"Role",selector:"role",cell:e=>e.role?e.role.title:""},{name:"Institution",selector:"institution",cell:e=>e.institution?e.institution.title:""},{name:"Action",selector:"",cell:e=>(0,t.jsxs)("div",{children:[(0,t.jsx)(r(),{href:"/users/[...routes]",as:"/users/edit/".concat(e._id),children:"Edit"}),"\xa0",(0,t.jsx)("a",{onClick:()=>P(e),children:"Delete"})]})}],f=async()=>{n(!0);let e=await d.A.get("/users",w);e&&e.data&&e.data.length>0&&(s(e.data),h(e.totalCount),n(!1))},v=async(e,a)=>{w.limit=e,w.page=a,n(!0);let t=await d.A.get("/users",w);t&&t.data&&t.data.length>0&&(s(t.data),x(e),n(!1))};(0,l.useEffect)(()=>{f()},[]);let P=async e=>{A(e),p(!0)},C=async()=>{await d.A.remove("/users/".concat(j._id)),f(),A({}),p(!1)},S=()=>p(!1);return(0,t.jsxs)("div",{children:[(0,t.jsxs)(i.A,{show:m,onHide:S,children:[(0,t.jsx)(i.A.Header,{closeButton:!0,children:(0,t.jsx)(i.A.Title,{children:"Delete User"})}),(0,t.jsx)(i.A.Body,{children:"Are you sure want to delete this user ?"}),(0,t.jsxs)(i.A.Footer,{children:[(0,t.jsx)(o.A,{variant:"secondary",onClick:S,children:"Cancel"}),(0,t.jsx)(o.A,{variant:"primary",onClick:C,children:"Yes"})]})]}),(0,t.jsx)(c.A,{columns:_,data:a,totalRows:u,pagServer:!0,handlePerRowsChange:v,handlePageChange:e=>{w.limit=g,w.page=e,f()}})]})}},34681:(e,a,s)=>{(window.__NEXT_P=window.__NEXT_P||[]).push(["/users",function(){return s(71852)}])},50749:(e,a,s)=>{"use strict";s.d(a,{A:()=>o});var t=s(37876);s(14232);var n=s(89773),r=s(31753),l=s(5507);function i(e){let{t:a}=(0,r.Bd)("common"),s={rowsPerPageText:a("Rowsperpage")},{columns:i,data:o,totalRows:c,resetPaginationToggle:d,subheader:u,subHeaderComponent:h,handlePerRowsChange:g,handlePageChange:x,rowsPerPage:m,defaultRowsPerPage:p,selectableRows:j,loading:A,pagServer:w,onSelectedRowsChange:_,clearSelectedRows:f,sortServer:v,onSort:P,persistTableHead:C,sortFunction:S,...N}=e,y={paginationComponentOptions:s,noDataComponent:a("NoData"),noHeader:!0,columns:i,data:o||[],dense:!0,paginationResetDefaultPage:d,subHeader:u,progressPending:A,subHeaderComponent:h,pagination:!0,paginationServer:w,paginationPerPage:p||10,paginationRowsPerPageOptions:m||[10,15,20,25,30],paginationTotalRows:c,onChangeRowsPerPage:g,onChangePage:x,selectableRows:j,onSelectedRowsChange:_,clearSelectedRows:f,progressComponent:(0,t.jsx)(l.A,{}),sortIcon:(0,t.jsx)("i",{className:"sort-icon fas fa-exchange-alt"}),sortServer:v,onSort:P,sortFunction:S,persistTableHead:C,className:"rki-table"};return(0,t.jsx)(n.Ay,{...y})}i.defaultProps={subHeader:!1,pagination:!0,totalRows:null,pagServer:!0,onSelectedRowsChange:null,clearSelectedRows:!1,sortServer:!1,persistTableHead:!1};let o=i},71852:(e,a,s)=>{"use strict";s.r(a),s.d(a,{__N_SSG:()=>u,default:()=>h});var t=s(37876);s(14232);var n=s(48230),r=s.n(n),l=s(49589),i=s(56970),o=s(37784),c=s(60282),d=s(21244),u=!0;let h=()=>(0,t.jsxs)(l.A,{className:"users-page",children:[(0,t.jsx)(i.A,{className:"page-header",children:(0,t.jsx)("h4",{children:"User List"})}),(0,t.jsx)(i.A,{children:(0,t.jsx)(o.A,{xs:12,children:(0,t.jsx)(r(),{href:"/users/[...routes]",as:"/users/create",children:(0,t.jsx)(c.A,{variant:"secondary",size:"sm",children:"Add Users"})})})}),(0,t.jsx)(i.A,{className:"mt-3",children:(0,t.jsx)(o.A,{xs:12,children:(0,t.jsx)(d.default,{})})})]})}},e=>{var a=a=>e(e.s=a);e.O(0,[9773,636,6593,8792],()=>a(34681)),_N_E=e.O()}]);
//# sourceMappingURL=users-3f2035400053ab65.js.map