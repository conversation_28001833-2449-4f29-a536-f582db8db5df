{"version": 3, "file": "static/chunks/pages/vspace/View-26e1051cb6f8706e.js", "mappings": "2FAAA,aACA,IACA,+EAAyF,EACzF,CAAI,UACJ,oBACA,SACA,EAAG,EACH,+CCPA,cACA,gGACA,QACA,mCCFA,4CACA,eACA,WACA,OAAe,EAAQ,KAAoC,CAC3D,EACA,SAFsB,iECFtB,gBACA,iBAAwB,OAAO,oCAC/B,0FACA,MAAS,OAAqB,GAC9B,gECLA,gBACA,uGACA,2CACA,aACA,QACA,YACA,eACA,CACA,CAAG,uCACH,WACA,CAAG,KAAQ,OAAc,KACzB,iDCZA,oBACA,YACA,8BACA,EAUA,gBACA,uBACA,SAEA,QAbA,IAaA,IAAoB,WAAsB,IAC1C,MAdA,EAcA,SAdA,EAcA,OAVA,eAWA,SAGA,QACA,CAEA,gBACA,aAA8B,KAC9B,WACA,aAEA,QADA,KACA,IAAyB,mBAAuB,IAChD,kBAEA,yCACA,oBAEA,sBAMA,OALA,GACA,aACA,WACA,eAEA,CACA,CAIA,OAHA,mBACA,MACA,EACA,CACA,gEC/CA,gBACA,YAAkB,WAAc,KAChC,WACA,qGAAwH,OAAa,UACrI,CACA,CACA,kBACA,0EACA,WACA,CAAG,GACH,gECVA,gBACA,qBACA,iCACA,qCACA,4BACA,wDACK,mBACL,CACA,QACA,CACA,cACA,YAAkB,mBAAsB,KACxC,wCACA,yCACM,OAAc,UACpB,CAAK,mIACL,+DACA,CAAK,CACL,CACA,QACA,gCCrBA,gBACA,yEACA,gDCFA,cACA,wEACA,4CACA,EAAG,GACH", "sources": ["webpack://_N_E/./node_modules/@babel/runtime/helpers/esm/isNativeReflectConstruct.js", "webpack://_N_E/./node_modules/@babel/runtime/helpers/esm/assertThisInitialized.js", "webpack://_N_E/?cbcc", "webpack://_N_E/./node_modules/@babel/runtime/helpers/esm/possibleConstructorReturn.js", "webpack://_N_E/./node_modules/@babel/runtime/helpers/esm/inherits.js", "webpack://_N_E/./node_modules/memoize-one/dist/memoize-one.esm.js", "webpack://_N_E/./node_modules/@babel/runtime/helpers/esm/createClass.js", "webpack://_N_E/./node_modules/@babel/runtime/helpers/esm/objectSpread2.js", "webpack://_N_E/./node_modules/@babel/runtime/helpers/esm/classCallCheck.js", "webpack://_N_E/./node_modules/@babel/runtime/helpers/esm/getPrototypeOf.js"], "sourcesContent": ["function _isNativeReflectConstruct() {\n  try {\n    var t = !Boolean.prototype.valueOf.call(Reflect.construct(<PERSON><PERSON><PERSON>, [], function () {}));\n  } catch (t) {}\n  return (_isNativeReflectConstruct = function _isNativeReflectConstruct() {\n    return !!t;\n  })();\n}\nexport { _isNativeReflectConstruct as default };", "function _assertThisInitialized(e) {\n  if (void 0 === e) throw new ReferenceError(\"this hasn't been initialised - super() hasn't been called\");\n  return e;\n}\nexport { _assertThisInitialized as default };", "\n    (window.__NEXT_P = window.__NEXT_P || []).push([\n      \"/vspace/View\",\n      function () {\n        return require(\"private-next-pages/vspace/View.tsx\");\n      }\n    ]);\n    if(module.hot) {\n      module.hot.dispose(function () {\n        window.__NEXT_P.push([\"/vspace/View\"])\n      });\n    }\n  ", "import _typeof from \"./typeof.js\";\nimport assertThisInitialized from \"./assertThisInitialized.js\";\nfunction _possibleConstructorReturn(t, e) {\n  if (e && (\"object\" == _typeof(e) || \"function\" == typeof e)) return e;\n  if (void 0 !== e) throw new TypeError(\"Derived constructors may only return object or undefined\");\n  return assertThisInitialized(t);\n}\nexport { _possibleConstructorReturn as default };", "import setPrototypeOf from \"./setPrototypeOf.js\";\nfunction _inherits(t, e) {\n  if (\"function\" != typeof e && null !== e) throw new TypeError(\"Super expression must either be null or a function\");\n  t.prototype = Object.create(e && e.prototype, {\n    constructor: {\n      value: t,\n      writable: !0,\n      configurable: !0\n    }\n  }), Object.defineProperty(t, \"prototype\", {\n    writable: !1\n  }), e && setPrototypeOf(t, e);\n}\nexport { _inherits as default };", "var safeIsNaN = Number.isNaN ||\n    function ponyfill(value) {\n        return typeof value === 'number' && value !== value;\n    };\nfunction isEqual(first, second) {\n    if (first === second) {\n        return true;\n    }\n    if (safeIsNaN(first) && safeIsNaN(second)) {\n        return true;\n    }\n    return false;\n}\nfunction areInputsEqual(newInputs, lastInputs) {\n    if (newInputs.length !== lastInputs.length) {\n        return false;\n    }\n    for (var i = 0; i < newInputs.length; i++) {\n        if (!isEqual(newInputs[i], lastInputs[i])) {\n            return false;\n        }\n    }\n    return true;\n}\n\nfunction memoizeOne(resultFn, isEqual) {\n    if (isEqual === void 0) { isEqual = areInputsEqual; }\n    var cache = null;\n    function memoized() {\n        var newArgs = [];\n        for (var _i = 0; _i < arguments.length; _i++) {\n            newArgs[_i] = arguments[_i];\n        }\n        if (cache && cache.lastThis === this && isEqual(newArgs, cache.lastArgs)) {\n            return cache.lastResult;\n        }\n        var lastResult = resultFn.apply(this, newArgs);\n        cache = {\n            lastResult: lastResult,\n            lastArgs: newArgs,\n            lastThis: this,\n        };\n        return lastResult;\n    }\n    memoized.clear = function clear() {\n        cache = null;\n    };\n    return memoized;\n}\n\nexport { memoizeOne as default };\n", "import toPropertyKey from \"./toPropertyKey.js\";\nfunction _defineProperties(e, r) {\n  for (var t = 0; t < r.length; t++) {\n    var o = r[t];\n    o.enumerable = o.enumerable || !1, o.configurable = !0, \"value\" in o && (o.writable = !0), Object.defineProperty(e, toPropertyKey(o.key), o);\n  }\n}\nfunction _createClass(e, r, t) {\n  return r && _defineProperties(e.prototype, r), t && _defineProperties(e, t), Object.defineProperty(e, \"prototype\", {\n    writable: !1\n  }), e;\n}\nexport { _createClass as default };", "import defineProperty from \"./defineProperty.js\";\nfunction ownKeys(e, r) {\n  var t = Object.keys(e);\n  if (Object.getOwnPropertySymbols) {\n    var o = Object.getOwnPropertySymbols(e);\n    r && (o = o.filter(function (r) {\n      return Object.getOwnPropertyDescriptor(e, r).enumerable;\n    })), t.push.apply(t, o);\n  }\n  return t;\n}\nfunction _objectSpread2(e) {\n  for (var r = 1; r < arguments.length; r++) {\n    var t = null != arguments[r] ? arguments[r] : {};\n    r % 2 ? ownKeys(Object(t), !0).forEach(function (r) {\n      defineProperty(e, r, t[r]);\n    }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) {\n      Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r));\n    });\n  }\n  return e;\n}\nexport { _objectSpread2 as default };", "function _classCallCheck(a, n) {\n  if (!(a instanceof n)) throw new TypeError(\"Cannot call a class as a function\");\n}\nexport { _classCallCheck as default };", "function _getPrototypeOf(t) {\n  return _getPrototypeOf = Object.setPrototypeOf ? Object.getPrototypeOf.bind() : function (t) {\n    return t.__proto__ || Object.getPrototypeOf(t);\n  }, _getPrototypeOf(t);\n}\nexport { _getPrototypeOf as default };"], "names": [], "sourceRoot": "", "ignoreList": [0, 1, 3, 4, 5, 6, 7, 8, 9]}