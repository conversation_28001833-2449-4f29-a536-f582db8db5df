{"version": 3, "file": "static/chunks/7308-8f56fb0c7f749c44.js", "mappings": "wMAuKA,MAnIqBA,IACnB,GAAM,GAAEC,CAAC,CAAE,CAAGC,CAAAA,EAAAA,EAAAA,EAAAA,CAAcA,CAAC,MAkIhBC,IAjIP,CAACC,EAAQC,EAAU,CAAGC,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAc,EAAE,EAG9CC,EAAqBC,IACzB,IAAMC,EAAc,8EAA8EC,IAAI,CAACF,EAAKG,WAAW,EAEvH,MACE,WAACC,MAAAA,CAAIC,UAAU,4BACb,WAACC,IAAAA,CAAED,UAAU,iBACX,UAACE,IAAAA,UAAGd,EAAE,cAAgB,IAAEO,EAAKQ,YAAY,EAAI,mBAE9CR,EAAKG,WAAW,EACf,WAACC,MAAAA,CAAIC,UAAU,wBACb,UAACC,IAAAA,UAAE,UAACC,IAAAA,UAAGd,EAAE,cACRQ,EACC,WAACG,MAAAA,WACC,UAACK,EAAAA,CAAeA,CAAAA,CAACC,KAAMC,EAAAA,GAAMA,CAAEC,KAAK,KAAKC,MAAM,OAAOR,UAAU,SAChE,UAACS,IAAAA,CAAET,UAAU,cAAcU,KAAMf,EAAKG,WAAW,CAAEa,OAAO,SAASC,IAAI,+BACpEjB,EAAKG,WAAW,MAIrB,UAACC,MAAAA,UACC,UAACE,IAAAA,CAAED,UAAU,YAAYa,MAAO,CAAEC,UAAW,WAAY,WACtDnB,EAAKG,WAAW,QAM1BH,EAAKoB,YAAY,EAChB,WAACC,EAAAA,CAAMA,CAAAA,CAAChB,UAAU,qCAAqCU,KAAMf,EAAKoB,YAAY,WAC3E3B,EAAE,YACH,UAACgB,EAAAA,CAAeA,CAAAA,CAACC,KAAMY,EAAAA,GAAUA,CAAEV,KAAK,KAAKP,UAAU,cAKjE,EA6CA,MA3CAkB,CAAAA,EAAAA,EAAAA,SAAAA,CAASA,CAAC,KACR,IAAMC,EAA8B,EAAE,CACtChC,GAASA,EAAMiC,OAAO,EAAIC,MAAMC,OAAO,CAACnC,EAAMiC,OAAO,GAAKjC,EAAMiC,OAAO,CAACG,GAAG,CAAC,CAAC5B,EAAM6B,KACjF,IACIC,EADEC,EAAW/B,GAAQA,EAAKgC,IAAI,CAACC,KAAK,CAAC,KAAKC,GAAG,GAGjD,OAAQH,GACN,IAAK,MACL,IAAK,MACL,IAAK,OACL,IAAK,MACHD,EAAS,GAAwC9B,MAAAA,CAArCmC,8BAAsB,CAAC,gBAAuB,OAATnC,EAAKoC,GAAG,EACzD,KACF,KAAK,MACHN,EAAS,gCACT,KACF,KAAK,OACHA,EAAS,iCACT,KACF,KAAK,MACL,IAAK,OACHA,EAAS,gCACT,KACF,SACEA,EAAS,iCACb,CAEA,IAAMO,EAAQ,CAAc,SAAbN,GAAoC,QAAbA,GAAmC,QAAbA,GAAmC,SAAbA,CAAa,CAAK,EAC/F,GAA4C/B,MAAAA,CAAzCmC,8BAAsB,CAAC,oBAA2B,OAATnC,EAAKoC,GAAG,EACnDE,EAAQ,GAAqE,OAAlEtC,GAAQA,EAAKuC,aAAa,CAAGvC,EAAKuC,aAAa,CAAG,iBAC7DC,EAAehD,EAAMiD,WAAW,EAAIf,MAAMC,OAAO,CAACnC,EAAMiD,WAAW,GACpEjD,EAAMiD,WAAW,CAACC,MAAM,CAAG,EAAIlD,EAAMiD,WAAW,CAACZ,EAAE,CAAG,GAE3DL,EAAemB,IAAI,CAAC,CAClBC,IAAKd,EACL3B,YAAaqC,EACbhC,aAAc8B,EACdlB,aAAciB,CAChB,EACF,GACAxC,EAAU2B,EACZ,EAAG,CAAChC,EAAM,EAGR,UAACY,MAAAA,UACER,GAA4B,IAAlBA,EAAO8C,MAAM,CACtB,UAACtC,MAAAA,CAAIC,UAAU,wCACb,UAACC,IAAAA,CAAED,UAAU,wDAAgDZ,EAAE,qBAGjE,UAACoD,EAAAA,EAAQA,CAAAA,CACPC,YAAY,EACZC,YAAY,EACZC,gBAAgB,EAChBC,cAAc,EACdC,mBAAmB,EACnBC,UAAU,EACVC,aAAa,EACbC,WAAW,EACXC,cAAe,GACfC,aAAc,GACdC,aAAc,IACZ5D,EAAOgC,GAAG,CAAC,CAAC5B,EAAMyD,IAChB,UAACC,MAAAA,CAECd,IAAK5C,EAAK4C,GAAG,CACbe,IAAK,aAAuB,OAAVF,EAAQ,GAC1BvC,MAAO,CAAE0C,MAAO,OAAQC,OAAQ,OAAQC,UAAW,OAAQ,GAHtDL,aAQV7D,EAAOgC,GAAG,CAAC,CAAC5B,EAAMyD,IACjB,WAACrD,MAAAA,WACC,UAACsD,MAAAA,CACCd,IAAK5C,EAAK4C,GAAG,CACbe,IAAK3D,EAAKQ,YAAY,EAAI,gBAC1BU,MAAO,CAAE6C,UAAW,QAASD,UAAW,SAAU,IAEnD/D,EAAkBC,KANXyD,OActB,yQCxIA,MAnBkB,IAEhB,IASMzD,EAAOgE,CARX,IAAO,CAAEtD,CAgBEuD,IAhBIC,EAAAA,GAASA,CAAErD,CAgBL,KAhBY,SAAU,EAC3C,KAAQ,CAAEH,KAAMyD,EAAAA,GAAMA,CAAEtD,MAAO,SAAU,EACzC,IAAO,CAAEH,KAAMyD,EAAAA,GAAMA,CAAEtD,MAAO,SAAU,EACxC,KAAQ,CAAEH,KAAM0D,EAAAA,GAAWA,CAAEvD,MAAO,SAAU,EAC9C,IAAO,CAAEH,KAAM0D,EAAAA,GAAWA,CAAEvD,MAAO,SAAU,EAC7C,IAAO,CAAEH,KAAM0D,EAAAA,GAAWA,CAAEvD,MAAO,SAAU,CAC/C,CAE6B,CAACwD,EADZC,SAAS,CAACC,OAAO,CAAC,IAAK,KACPC,IAAI,GAAG,CAEzC,MACE,UAAC1D,IAAAA,CAAEC,KAAM,GAA4CvB,MAAAA,CAAzC2C,8BAAsB,CAAC,oBAA4B,OAAV3C,EAAM4C,GAAG,EAAIpB,OAAO,kBACvE,UAACP,EAAAA,CAAeA,CAAAA,CAACC,KAAMV,EAAKU,IAAI,CAAEG,MAAOb,EAAKa,KAAK,IAGzD,EC2BA,EApCiB,IACf,GAAM,SAmCO4D,CAnCLC,CAAQ,MAmCKD,EAAC,CAnCJE,CAAO,CAAE,CAAGnF,EAE9B,MACE,UAACY,MAAAA,UAEGsE,GAAYA,EAAS9C,GAAG,CAAC,CAAC5B,EAAWyD,IAEjC,WAACmB,EAAAA,CAAGA,CAAAA,WACF,UAACX,EAASA,CAAE,GAAGjE,CAAI,GACnB,UAAC6E,EAAAA,CAAGA,CAAAA,UACF,WAACzE,MAAAA,CAAIC,UAAU,mBACb,UAACS,IAAAA,CAAEC,KAAM,GAA4Cf,MAAAA,CAAzCmC,8BAAsB,CAAC,oBAA2B,OAATnC,EAAKoC,GAAG,EAAIpB,OAAO,kBACtE,UAACV,IAAAA,UAAGN,EAAKuC,aAAa,KAExB,UAACuC,EAAAA,CAAcA,CAAAA,CAACC,QAAQ,QAAQC,UAAU,QAAQC,QAAS,WAACC,EAAAA,CAAOA,CAAAA,CAACC,GAAG,0BACrE,UAACD,EAAAA,CAAOA,CAACE,MAAM,EAACC,GAAG,cAAK,WACxB,UAACH,EAAAA,CAAOA,CAACI,IAAI,WACVX,GAAWA,CAAO,CAAClB,EAAM,eAG5B,UAAChD,EAAAA,CAAeA,CAAAA,CAACJ,UAAU,OAAOK,KAAM6E,EAAAA,GAAYA,CAAE1E,MAAM,qBAb1D4C,KA0BtB,ECbA,EA1Bc,IACV,EAyBW+B,CAzBL,IAyBUA,EAzBRC,CAAI,CAAE,CAAGjG,EAEjB,MACI,UAACY,MAAAA,UAEOqF,GAAQA,EAAK7D,GAAG,CAAC,CAAC5B,EAAWyD,IAErB,UAACmB,EAAAA,CAAGA,CAAAA,UACA,UAACC,EAAAA,CAAGA,CAAAA,CAACxE,UAAU,eACX,WAACD,MAAAA,CAAIC,UAAU,2CACX,UAACI,EAAAA,CAAeA,CAAAA,CAACC,KAAMgF,EAAAA,GAAOA,CAAE7E,MAAM,YAAY,WAClD,UAACC,IAAAA,CAAEC,KAAMf,EAAKyF,IAAI,CAAEzE,OAAO,kBACvB,UAAC2E,OAAAA,UAAM3F,EAAK4F,KAAK,WALvBnC,KAgBlC,iBChBA,MATc,IACZ,GAAM,MAAEoC,CAAI,IAQMC,EAAC,GARLC,CAAO,CAAE,CAAGvG,EAC1B,MACE,UAACY,MAAAA,UACC,UAACT,EAAAA,CAAWA,CAAAA,CAAC8B,QAASoE,EAAMpD,YAAasD,KAG/C,4BCIA,IAAMC,EAAa,qBACbC,EAAW,iBA4NjB,EApNoB,IAClB,GAAM,CAAExG,GAAC,CAAE,CAAGC,CAAAA,EAAAA,EAAAA,EAAAA,CAmNDwG,CAnNgB,UACvB,CAACC,CAkNkB,CAlNVC,EAAU,CAAGtG,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAC,CACnCuG,KAAM,GACNT,MAAO,GACPzF,YAAa,GACbmG,WAAY,GACZC,WAAY,GACZd,KAAM,EAAE,CACRe,MAAO,EAAE,CACTpE,IAAK,GACLqE,YAAa,GACb/B,SAAU,EAAE,CACZ9E,OAAQ,EAAE,CACV8G,WAAY,EAAE,CACdC,gBAAiB,EACnB,GAEM,CAFF,EAEUC,EAAS,CAAG9G,CAAAA,EAAAA,EAAAA,OAFL,CAEKA,CAAQA,CAAC,CAAE+G,QAAQ,EAAOC,MAAO,OAAQ,GAC7D,CAACC,EAAYC,EAAc,CAAGlH,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAC,IACvC,CAACmH,EAAQC,EAAU,CAAGpH,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,EAAC,GAC/BqH,EAAgB,IAAMD,GAAU,GAEhCE,EAAU,MAAOC,IACrB,MAAMC,EAAAA,CAAUA,CAACC,MAAM,CAAC,YAA4B,OAAhBF,EAAYjF,GAAG,GACnD8E,GAAU,GACV,IAAMM,EAAa,UAA2B,OAAjBH,EAAYhB,IAAI,EAC7CoB,IAAAA,IAAW,CAAC,IAA6BJ,MAAAA,CAAzBA,EAAYhB,IAAI,CAAC,UAAgC,OAAxBgB,CAAW,CAACG,EAAW,EAClE,EAEAjG,CAAAA,EAAAA,EAAAA,SAAAA,CAASA,CAAC,KACR,IAAImG,EAAuC,KACrCC,EAAkB,MAAOxC,IAC7B,IAAMyC,EAAW,MAAMN,EAAAA,CAAUA,CAACO,GAAG,CAAC,YAAe,OAAH1C,IAClD,GAAIyC,GAAYA,EAASvB,IAAI,CAAE,CAC7B,IAAMyB,EAAgB,MAAMR,EAAAA,CAAUA,CAACO,GAAG,CACxC,eAAoC,OAArBD,EAASnB,WAAW,GAGrCiB,EAAgBK,WAAW,KACzBf,EAAcc,EAAgBA,EAAclC,KAAK,CAAG,IACpDQ,EAAUwB,GACVhB,EAAS,GAAgB,EAAE,GAAGoB,CAAS,CAAOnB,EAArB,MAA6B,EAAO,EAC/D,EAAG,IACL,CACF,CAFa,CAKXrH,GACAA,EAAMyI,MAAM,EACQ,WAApBzI,EAAMyI,EAPkC,IAO5B,CAAC,EAAE,EACfzI,EAAMyI,MAAM,CAAC,EAAE,EACf,CACIP,GAAeQ,aAAaR,GAChCd,EAAS,CAAEC,QAAQ,EAAOC,MAAO,MAAO,GACxCa,EAAgBnI,EAAMyI,MAAM,CAAC,EAAE,EAEnC,EAAG,CAACzI,EAAMyI,MAAM,CAAC,EA6BjB,IAAME,EAAW,IAEb,UAACC,IAAIA,CACHrH,KAAM,CACJsH,IAFCD,KAEU,uBACXE,MAAO,CACLC,YAAapC,EAAOE,IAAI,CACxBI,YAAaN,EAAOM,WAAW,CAEnC,EACApB,GAAI,iBAA2Cc,MAAAA,CAA1BA,EAAO/D,GAAG,CAAC,iBAA0C+D,MAAAA,CAA3BA,EAAOE,IAAI,CAAC,iBAAkC,OAAnBF,EAAOM,WAAW,WAE5F,UAAC5E,IAAAA,CAAExB,UAAU,iBAKbmI,EAAa,IAEf,iCACE,UAAC3G,IAAAA,CAAExB,UAAU,mBAAmBoI,QAAS,IAAMvB,GAAU,KACzD,WAACwB,EAAAA,CAAKA,CAAAA,CACJ9H,KAAK,KACL+H,KAAM1B,EACN2B,OAAQ,IAAM1B,GAAU,GACxB2B,kBAAgB,yCAEhB,UAACH,EAAAA,CAAKA,CAACtD,MAAM,EAAC0D,WAAW,aACvB,UAACJ,EAAAA,CAAKA,CAACK,KAAK,EAAC5D,GAAG,wCACbgB,EAAOP,KAAK,KAGjB,WAAC8C,EAAAA,CAAKA,CAACpD,IAAI,YACR7F,EAAE,6BAA6B,IAAE0G,EAAOP,KAAK,CAAC,OAEjD,WAAC8C,EAAAA,CAAKA,CAACM,MAAM,YACX,UAAC3H,EAAAA,CAAMA,CAAAA,CAAC4H,QAAQ,YAAYR,QAAStB,WAClC1H,EAAE,YAEL,UAAC4B,EAAAA,CAAMA,CAAAA,CAAC4H,QAAQ,UAAUR,QAAS,IAAMrB,EAAQjB,YAC9C1G,EAAE,iBAQTyJ,EAAgBC,CAAAA,EAAAA,EAAAA,EAAAA,CAAaA,CAAC,IAAM,UAAChB,EAAAA,CAAAA,IAErCiB,EAAkBC,CAAAA,EAAAA,EAAAA,EAAAA,CAAeA,CAAC,IAAM,UAACb,EAAAA,CAAAA,IAE/C,MACE,WAACpI,MAAAA,CAAIC,UAAU,2BACZ,EAAOwG,MAAM,EAAoB,SAAhByC,EAAMxC,KAAK,CAEzB,KADF,UAACyC,EAAAA,CAAOA,CAAAA,CAACC,UAAU,SAASP,QAAQ,YAErC9C,GAAUA,EAAO/D,GAAG,EAAI5C,EAAMyI,MAAM,CACnC,UAACwB,EAAAA,CAAQA,CAAAA,CAACC,GAAIJ,EAAMzC,MAAM,UACxB,WAACzG,MAAAA,CAAIC,UAAU,8BACb,WAACD,MAAAA,CAAIC,UAAU,0BACb,UAAC6I,EAAAA,CAAc/C,OAAQA,IACvB,UAACiD,EAAAA,CAAgBjD,OAAQA,IACzB,UAACtE,IAAAA,CACCxB,UAAU,eACVoI,QAAS,IACP7B,EAAS,CAAEC,OAAQ,GAAOC,MAAO,OAAQ,EAC3C,OAGJ,UAAClC,EAAAA,CAAGA,CAAAA,UACF,UAAC+E,EAAAA,CAAIA,CAACrE,IAAI,EAACjF,UAAU,gBACnB,WAACuE,EAAAA,CAAGA,CAAAA,CAACvE,UAAU,0BACb,WAACwE,EAAAA,CAAGA,CAAAA,WACF,UAAC+E,KAAAA,UAAIzD,EAAOP,KAAK,GACjB,UAACiE,EAAAA,CAAiBA,CAAAA,CAAC1J,YAAagG,EAAOhG,WAAW,GAClD,UAAC0E,EAAAA,CAAGA,CAAAA,UAAEiF,CAvGH,KACnB,OAAQ/C,GACN,IAAK,WAGL,KAAKd,EAFH,MAAO,UAACxB,EAAQA,CAACC,SAAUyB,EAAX1B,QAA0B,CAAEE,QAASwB,EAAOO,UAAU,EAAI,EAAE,EAK9E,KAAK,QACH,MACE,UAACZ,EAAKA,CAACD,KAAMM,EAAOvG,CAAdkG,KAAoB,CAAG,GAAGtG,CAAK,CAAEuG,QAASI,EAAOO,UAAU,EAGrE,KAAK,mBACH,MAAQ,WAACtG,MAAAA,WAAI,UAACqE,EAAQA,CAACC,SAAUyB,EAAX1B,QAA0B,CAAEE,QAASwB,EAAOO,UAAU,EAAI,EAAE,GACjF,UAACZ,EAAKA,CAACD,KAAMM,EAAOvG,CAAdkG,KAAoB,CAAG,GAAGtG,CAAK,CAAEuG,QAASI,EAAOO,UAAU,KAIpE,KAAK,OACH,MAAO,UAAClB,EAAKA,CAAE,CAAFA,EAAKW,CAAM,EAE1B,SACE,OAAO,IACX,EACF,SAgFgB,WAACtB,EAAAA,CAAGA,CAAAA,CAACkF,GAAI,YACP,WAACzJ,IAAAA,WACC,UAACC,IAAAA,UAAGd,EAAE,gBAAkB,IAAC,WAACkG,OAAAA,WAAK,IAAEoB,EAAW,UAE7B,YAAfA,GAA4B,EAAgBJ,eAAe,EAC5D,WAACrG,IAAAA,WACD,UAACC,IAAAA,UAAGd,EAAE,cAAgB,IAAC,WAACkG,OAAAA,WAAK,IAAGQ,EAAeQ,eAAe,CAACqD,QAAQ,CAAC,UAGxD,YAAfjD,GAA4B,EAAgBJ,eAAe,EAC5D,WAACrG,IAAAA,WACD,UAACC,IAAAA,UAAGd,EAAE,iBAAmB,IAAC,WAACkG,OAAAA,WAAK,IAAGQ,EAAeQ,eAAe,CAACsD,WAAW,CAAC,UAG9ElD,IAAed,GAAY,EAAgBiE,UAAU,EACpD,WAAC5J,IAAAA,WACC,UAACC,IAAAA,UAAGd,EAAE,eAAiB,IAAC,WAACkG,OAAAA,WAAK,IAAEwE,IAAO,EAAgBD,UAAjBC,EAA6BC,MAAM,CAACpE,SAI7Ee,IAAed,GAAY,EAAgBoE,QAAQ,EAClD,WAAC/J,IAAAA,WACC,UAACC,IAAAA,UAAGd,EAAE,aAAe,IAAC,UAACkG,OAAAA,UAAMwE,IAAO,EAAgBE,QAAQ,EAAzBF,MAAiC,CAACnE,QAGzE,WAAC1F,IAAAA,WACC,UAACC,IAAAA,UAAGd,EAAE,aAAe,IACrB,UAACkG,OAAAA,UACEwE,IAAOhE,EAAOG,UAAR6D,EAAoBC,MAAM,CAACpE,QAGtC,WAAC1F,IAAAA,WACC,UAACC,IAAAA,UAAGd,EAAE,kBAAoB,IAC1B,UAACkG,OAAAA,UACEwE,IAAOhE,EAAOI,UAAR4D,EAAoBC,MAAM,CAACpE,uBAShD,OAGV,yEC3MA,MA9B0B,IACxB,GAAM,GAAEvG,CAAC,CAAE,CAAGC,CAAAA,EAAAA,EAAAA,EAAAA,CAAcA,CAAC,MA6BhBmK,IA5BPS,EAAiBC,SAASpI,EA4BF0H,EAAC,CA5B6B,EACtD,CAACW,CADyD,CAAK,EACpC,CAAG1K,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,EAAC,GAO7C,MACE,iCAEIN,EAAMW,WAAW,CACjB,UAACC,MAAAA,CACCqK,wBAAyBC,CAVZ,CAACC,EAAqBC,KAElC,CAAEC,OADe,CACPC,GAD8BH,EAAYjI,MAAM,CAAG4H,EAAkBK,EAAYI,SAAS,CAAC,EAAGT,GAAkB,MAAQ9K,EAAMW,WAAW,CACzH,CACnC,EAO8CX,EAAMW,WAAW,CAACqK,GACxDnK,UAAU,kBAEH,KAGTb,EAAMW,WAAW,EAAIX,EAAMW,WAAW,CAACuC,MAAM,CAAG4H,EAC9C,UAACU,SAAAA,CAAO3E,KAAK,SAAShG,UAAU,eAAeoI,QAAS,IAAMwC,EAAc,CAACT,YAChE/K,EAAb+K,EAAe,WAAgB,GAAF/K,WACjB,OAItB", "sources": ["webpack://_N_E/./components/common/ReactImages.tsx", "webpack://_N_E/./components/updates/utils/FileIcons.tsx", "webpack://_N_E/./components/updates/utils/Document.tsx", "webpack://_N_E/./components/updates/utils/Link.tsx", "webpack://_N_E/./components/updates/utils/Image.tsx", "webpack://_N_E/./components/updates/UpdatePopup.tsx", "webpack://_N_E/./components/common/readMore/readMore.tsx"], "sourcesContent": ["\r\n//Import Library\r\nimport React, { useState, useEffect } from 'react';\r\nimport { Carousel } from 'react-responsive-carousel';\r\nimport { FontAwesomeIcon } from \"@fortawesome/react-fontawesome\";\r\nimport {\r\n  faLink, faDownload\r\n} from \"@fortawesome/free-solid-svg-icons\";\r\nimport { Button } from \"react-bootstrap\";\r\n\r\n//Import services/components\r\nimport { useTranslation } from 'next-i18next';\r\n\r\n// Import CSS for react-responsive-carousel\r\nimport \"react-responsive-carousel/lib/styles/carousel.min.css\";\r\n\r\n// Define types for image items\r\ninterface ImageItem {\r\n  src: string;\r\n  description: string;\r\n  originalName: string;\r\n  downloadLink: string | false;\r\n}\r\n\r\ninterface ReactImagesProps {\r\n  gallery?: Array<{\r\n    _id: string;\r\n    name: string;\r\n    original_name?: string;\r\n    src?: string;\r\n    caption?: string;\r\n    alt?: string;\r\n  }> | boolean;\r\n  imageSource?: string[] | boolean;\r\n}\r\n\r\nconst ReactImages = (props: ReactImagesProps) => {\r\n  const { t } = useTranslation('common');\r\n  const [images, setImages] = useState<ImageItem[]>([]);\r\n\r\n  // Render image description and metadata\r\n  const renderImageLegend = (item: ImageItem) => {\r\n    const isValidLink = /(http|https):\\/\\/(\\w+:{0,1}\\w*)?(\\S+)(:[0-9]+)?(\\/|\\/([\\w#!:.?+=&%!\\-\\/]))?/.test(item.description);\r\n\r\n    return (\r\n      <div className=\"carousel-legend\">\r\n        <p className=\"lead\">\r\n          <b>{t(\"Filename\")}</b> {item.originalName || \"No Name found\"}\r\n        </p>\r\n        {item.description && (\r\n          <div className=\"source_link\">\r\n            <p><b>{t(\"Source\")}</b></p>\r\n            {isValidLink ? (\r\n              <div>\r\n                <FontAwesomeIcon icon={faLink} size=\"1x\" color=\"#999\" className=\"me-1\" />\r\n                <a className=\"source_link\" href={item.description} target=\"_blank\" rel=\"noopener noreferrer\">\r\n                  {item.description}\r\n                </a>\r\n              </div>\r\n            ) : (\r\n              <div>\r\n                <p className=\"ps-0 py-0\" style={{ wordBreak: \"break-all\" }}>\r\n                  {item.description}\r\n                </p>\r\n              </div>\r\n            )}\r\n          </div>\r\n        )}\r\n        {item.downloadLink && (\r\n          <Button className=\"btn btn-success mt-2 btn--download\" href={item.downloadLink}>\r\n            {t(\"Download\")}\r\n            <FontAwesomeIcon icon={faDownload} size=\"1x\" className=\"ms-1\" />\r\n          </Button>\r\n        )}\r\n      </div>\r\n    );\r\n  };\r\n\r\n  useEffect(() => {\r\n    const carouselImages: ImageItem[] = [];\r\n    props && props.gallery && Array.isArray(props.gallery) && props.gallery.map((item, i) => {\r\n      const fileType = item && item.name.split('.').pop();\r\n      let imgSrc;\r\n\r\n      switch (fileType) {\r\n        case \"JPG\":\r\n        case \"jpg\":\r\n        case \"jpeg\":\r\n        case \"png\":\r\n          imgSrc = `${process.env.API_SERVER}/image/show/${item._id}`;\r\n          break;\r\n        case \"pdf\":\r\n          imgSrc = \"/images/fileIcons/pdfFile.png\";\r\n          break;\r\n        case \"docx\":\r\n          imgSrc = \"/images/fileIcons/wordFile.png\";\r\n          break;\r\n        case \"xls\":\r\n        case 'xlsx':\r\n          imgSrc = \"/images/fileIcons/xlsFile.png\";\r\n          break;\r\n        default:\r\n          imgSrc = \"/images/fileIcons/otherFile.png\";\r\n      }\r\n\r\n      const _link = (fileType === \"docx\" || fileType === \"pdf\" || fileType === \"xls\" || fileType === \"xlsx\")\r\n        && `${process.env.API_SERVER}/files/download/${item._id}`;\r\n      const _name = `${item && item.original_name ? item.original_name : \"No Name found\"}`;\r\n      const _description = props.imageSource && Array.isArray(props.imageSource)\r\n        && props.imageSource.length > 0 ? props.imageSource[i] : \"\";\r\n\r\n      carouselImages.push({\r\n        src: imgSrc,\r\n        description: _description,\r\n        originalName: _name,\r\n        downloadLink: _link\r\n      });\r\n    });\r\n    setImages(carouselImages);\r\n  }, [props]);\r\n\r\n  return (\r\n    <div>\r\n      {images && images.length === 0 ? (\r\n        <div className=\"border border-info my-3 mx-0\">\r\n          <p className=\"d-flex d-flex justify-content-center p-2 m-0\">{t(\"NoFilesFound!\")}</p>\r\n        </div>\r\n      ) : (\r\n        <Carousel\r\n          showThumbs={true}\r\n          showStatus={true}\r\n          showIndicators={true}\r\n          infiniteLoop={true}\r\n          useKeyboardArrows={true}\r\n          autoPlay={false}\r\n          stopOnHover={true}\r\n          swipeable={true}\r\n          dynamicHeight={false}\r\n          emulateTouch={true}\r\n          renderThumbs={() =>\r\n            images.map((item, index) => (\r\n              <img\r\n                key={index}\r\n                src={item.src}\r\n                alt={`Thumbnail ${index + 1}`}\r\n                style={{ width: '60px', height: '60px', objectFit: 'cover' }}\r\n              />\r\n            ))\r\n          }\r\n        >\r\n          {images.map((item, index) => (\r\n            <div key={index}>\r\n              <img\r\n                src={item.src}\r\n                alt={item.originalName || \"Gallery image\"}\r\n                style={{ maxHeight: '500px', objectFit: 'contain' }}\r\n              />\r\n              {renderImageLegend(item)}\r\n            </div>\r\n          ))}\r\n        </Carousel>\r\n      )}\r\n    </div>\r\n  );\r\n\r\n}\r\n\r\nexport default ReactImages;\r\n", "\r\n//Import Library\r\nimport { FontAwesomeIcon } from \"@fortawesome/react-fontawesome\";\r\nimport { faFilePdf, faFile, faFileExcel } from \"@fortawesome/free-solid-svg-icons\";\r\n\r\ninterface FileIconsProps {\r\n  _id: string;\r\n  extension: string;\r\n}\r\n\r\nconst FileIcons = (props: FileIconsProps) => {\r\n\r\n  const updateTypeByIcon: { [key: string]: { icon: any; color: string } } = {\r\n    'pdf': { icon: faFilePdf, color: '#FF0000' },\r\n    'docx': { icon: faFile, color: '#87CEFA' },\r\n    'doc': { icon: faFile, color: '#87CEFA' },\r\n    'xlsx': { icon: faFileExcel, color: '#1B4D3E' },\r\n    'xls': { icon: faFileExcel, color: '#1B4D3E' },\r\n    'csv': { icon: faFileExcel, color: '#1B4D3E' }\r\n  }\r\n  const rep = props.extension.replace(\".\", \" \")\r\n  const item = updateTypeByIcon[rep.trim()];\r\n\r\n  return (\r\n    <a href={`${process.env.API_SERVER}/files/download/${props._id}`} target=\"_blank\">\r\n      <FontAwesomeIcon icon={item.icon} color={item.color} />\r\n    </a>\r\n  );\r\n}\r\nexport default FileIcons;", "//Import Library\r\nimport React from \"react\";\r\nimport { Row, Col, OverlayTrigger, Popover } from \"react-bootstrap\";\r\nimport { FontAwesomeIcon } from \"@fortawesome/react-fontawesome\";\r\nimport { faInfoCircle } from \"@fortawesome/free-solid-svg-icons\";\r\n\r\n//Import services/components\r\nimport FileIcons from './FileIcons';\r\n\r\n\r\ninterface DocumentProps {\r\n  document: Array<{\r\n    _id: string;\r\n    original_name: string;\r\n  }>;\r\n  doc_src: string[];\r\n}\r\n\r\n//TOTO refactor\r\nconst Document = (props: DocumentProps): React.ReactElement => {\r\n  const { document, doc_src } = props;\r\n\r\n  return (\r\n    <div>\r\n      {\r\n        document && document.map((item: any, index: number) => {\r\n          return (\r\n            <Row key={index}>\r\n              <FileIcons {...item} />\r\n              <Col>\r\n                <div className=\"d-flex\">\r\n                  <a href={`${process.env.API_SERVER}/files/download/${item._id}`} target=\"_blank\">\r\n                    <p>{item.original_name}</p>\r\n                  </a>\r\n                  <OverlayTrigger trigger=\"click\" placement=\"right\" overlay={<Popover id=\"popover-basic\">\r\n                    <Popover.Header as=\"h5\">Source</Popover.Header>\r\n                    <Popover.Body>\r\n                      {doc_src && doc_src[index] }\r\n                    </Popover.Body>\r\n                  </Popover>}>\r\n                    <FontAwesomeIcon className=\"ms-1\" icon={faInfoCircle} color=\"#232c3d\" />\r\n                  </OverlayTrigger>\r\n\r\n\r\n                </div>\r\n              </Col>\r\n            </Row>\r\n          );\r\n        })\r\n      }\r\n    </div>\r\n  )\r\n\r\n}\r\n\r\nexport default Document;\r\n", "//Import Library\r\nimport React from \"react\";\r\nimport { Row, Col } from \"react-bootstrap\";\r\nimport { FontAwesomeIcon } from \"@fortawesome/react-fontawesome\";\r\nimport { faGlobe } from \"@fortawesome/free-solid-svg-icons\";\r\n\r\n//TOTO refactor\r\ninterface LinksProps {\r\n  link: Array<{\r\n    url: string;\r\n    title: string;\r\n  }>;\r\n}\r\n\r\nconst Links = (props: LinksProps): React.ReactElement => {\r\n    const { link } = props;\r\n\r\n    return (\r\n        <div>\r\n            {\r\n                link && link.map((item: any, index: number) => {\r\n                    return (\r\n                        <Row key={index}>\r\n                            <Col className=\"p-0\" >\r\n                                <div className=\"d-flex align-items-center mb-2\" >\r\n                                    <FontAwesomeIcon icon={faGlobe} color=\"#87CEFA\" />&nbsp;&nbsp;\r\n                                    <a href={item.link} target=\"_blank\">\r\n                                        <span>{item.title}</span>\r\n                                    </a>\r\n                                </div>\r\n                            </Col>\r\n                        </Row>\r\n                    );\r\n                })\r\n            }\r\n        </div >\r\n    )\r\n\r\n}\r\n\r\nexport default Links;", "//Import Library\r\nimport React, { useEffect, useState } from \"react\";\r\nimport { Button, Card, Form, Container, Row, Col, Tab, Tabs } from \"react-bootstrap\";\r\n\r\n//Import services/components\r\nimport ReactImages from \"../../../components/common/ReactImages\";\r\n\r\ninterface ImageProps {\r\n  data: any[];\r\n  srcText: string[];\r\n}\r\n\r\n//TOTO refactor\r\nconst Image = (props: ImageProps): React.ReactElement => {\r\n  const { data, srcText } = props;\r\n  return (\r\n    <div>\r\n      <ReactImages gallery={data} imageSource={srcText} />\r\n    </div>\r\n  )\r\n}\r\n\r\nexport default Image;\r\n", "//Import Library\r\nimport React, { useState, useEffect } from \"react\";\r\nimport Link from \"next/link\";\r\nimport {\r\n  Card,\r\n  Button,\r\n  Row,\r\n  Col,\r\n  <PERSON><PERSON><PERSON>,\r\n  Spinner,\r\n} from \"react-bootstrap\";\r\nimport moment from \"moment\";\r\nimport Modal from \"react-bootstrap/Modal\";\r\nimport Router from \"next/router\";\r\n\r\n//Import services/components\r\nimport apiService from \"../../services/apiService\";\r\nimport ReadMoreContainer from \"../common/readMore/readMore\";\r\nimport Document from \"./utils/Document\";\r\nimport Links from \"./utils/Link\";\r\nimport Image from \"./utils/Image\";\r\nimport { canDeleteUpdate, canEditUpdate } from \"./permissions\";\r\nimport { useTranslation } from 'next-i18next';\r\n\r\nconst formatDate = \"MM-D-YYYY HH:mm:ss\";\r\nconst Calendar = \"Calendar Event\"\r\ninterface UpdatePopupProps {\r\n  item?: any;\r\n  onRemoveUpdate?: (id: string) => void;\r\n  routes?: string[];\r\n  [key: string]: any;\r\n}\r\n\r\nconst UpdatePopup = (props: UpdatePopupProps) => {\r\n  const { t } = useTranslation('common');\r\n  const [update, setUpdate] = useState({\r\n    type: \"\",\r\n    title: \"\",\r\n    description: \"\",\r\n    created_at: \"\",\r\n    updated_at: \"\",\r\n    link: [],\r\n    reply: [],\r\n    _id: \"\",\r\n    update_type: \"\",\r\n    document: [],\r\n    images: [],\r\n    images_src: [],\r\n    contact_details: []\r\n  }); // Initial Schema\r\n\r\n  const [popup, setPopup] = useState({ enable: false, event: \"close\" });\r\n  const [updatetype, setUpdateType] = useState(\"\");\r\n  const [smShow, setSmShow] = useState(false);\r\n  const smhandleClose = () => setSmShow(false);\r\n\r\n  const confirm = async (itemInitial: any) => {\r\n    await apiService.remove(`/updates/${itemInitial._id}`);\r\n    setSmShow(false);\r\n    const parentType = `parent_${itemInitial.type}`;\r\n    Router.push(`/${itemInitial.type}/show/${itemInitial[parentType]}`);\r\n  };\r\n\r\n  useEffect(() => {\r\n    let _cleatTimeout: NodeJS.Timeout | null = null;\r\n    const fetchUpdateData = async (id: string) => {\r\n      const respData = await apiService.get(`/updates/${id}`);\r\n      if (respData && respData.type) {\r\n        const getUpdateType = await apiService.get(\r\n          `/updateType/${respData.update_type}`\r\n        );\r\n\r\n        _cleatTimeout = setTimeout(() => {\r\n          setUpdateType(getUpdateType ? getUpdateType.title : \"\");\r\n          setUpdate(respData);\r\n          setPopup((prevState) => ({ ...prevState, ...{ enable: true } }));\r\n        }, 500); // timeout for Collapse delay\r\n      }\r\n    };\r\n\r\n    if (\r\n      props &&\r\n      props.routes &&\r\n      props.routes[2] === \"update\" &&\r\n      props.routes[3]\r\n    ) {\r\n      if (_cleatTimeout) clearTimeout(_cleatTimeout);\r\n      setPopup({ enable: false, event: \"open\" });\r\n      fetchUpdateData(props.routes[3]);\r\n    }\r\n  }, [props.routes]);\r\n\r\n  const getComponent = () => {\r\n    switch (updatetype) {\r\n      case \"Document\":\r\n        return <Document document={update.document} doc_src={update.images_src || []} />;\r\n\r\n      case Calendar:\r\n        return <Document document={update.document} doc_src={update.images_src || []} />;\r\n\r\n      case \"Image\":\r\n        return (\r\n          <Image data={update.images} {...props} srcText={update.images_src} />\r\n        );\r\n\r\n      case \"General / Notice\":\r\n        return (<div><Document document={update.document} doc_src={update.images_src || []} />\r\n         <Image data={update.images} {...props} srcText={update.images_src} />\r\n         </div>);\r\n\r\n\r\n      case \"Link\":\r\n        return <Links {...update} />;\r\n\r\n      default:\r\n        return null;\r\n    }\r\n  };\r\n\r\n  const EditLink = () => {\r\n    return (\r\n      <Link\r\n        href={{\r\n          pathname: `/updates/[...routes]`,\r\n          query: {\r\n            parent_type: update.type,\r\n            update_type: update.update_type,\r\n          },\r\n        }}\r\n        as={`/updates/edit/${update._id}?parent_type=${update.type}&update_type=${update.update_type}`}\r\n        >\r\n        <i className=\"fas fa-pen\"></i>\r\n      </Link>\r\n    );\r\n  }\r\n\r\n  const DeleteLink = () => {\r\n    return (\r\n      <>\r\n        <i className=\"fas fa-trash-alt\" onClick={() => setSmShow(true)} />\r\n        <Modal\r\n          size=\"sm\"\r\n          show={smShow}\r\n          onHide={() => setSmShow(false)}\r\n          aria-labelledby=\"example-modal-sizes-title-sm\"\r\n        >\r\n          <Modal.Header closeButton>\r\n            <Modal.Title id=\"example-modal-sizes-title-sm\">\r\n              {update.title}\r\n            </Modal.Title>\r\n          </Modal.Header>\r\n          <Modal.Body>\r\n            {t(\"Areyousureyouwanttodelete\")} {update.title}?\r\n          </Modal.Body>\r\n          <Modal.Footer>\r\n            <Button variant=\"secondary\" onClick={smhandleClose}>\r\n              {t(\"Cancel\")}\r\n            </Button>\r\n            <Button variant=\"primary\" onClick={() => confirm(update)}>\r\n              {t(\"OK\")}\r\n            </Button>\r\n          </Modal.Footer>\r\n        </Modal>\r\n      </>\r\n    )\r\n  }\r\n\r\n  const CanEditUpdate = canEditUpdate(() => <EditLink />)\r\n\r\n  const CanDeleteUpdate = canDeleteUpdate(() => <DeleteLink />)\r\n\r\n  return (\r\n    <div className=\"updatesPopMain\">\r\n      {!popup.enable && popup.event === \"open\" ? (\r\n        <Spinner animation=\"border\" variant=\"primary\" />\r\n      ) : null}\r\n      {update && update._id && props.routes ? (\r\n        <Collapse in={popup.enable}>\r\n          <div className=\"updatesPopupBlock\">\r\n            <div className=\"updateActions\">\r\n              <CanEditUpdate update={update} />\r\n              <CanDeleteUpdate update={update} />\r\n              <i\r\n                className=\"fas fa-times\"\r\n                onClick={(e) => {\r\n                  setPopup({ enable: false, event: \"close\" });\r\n                }}\r\n              ></i>\r\n            </div>\r\n            <Row>\r\n              <Card.Body className=\"mt-4\">\r\n                <Row className=\"operationData\">\r\n                  <Col>\r\n                    <h3>{update.title}</h3>\r\n                    <ReadMoreContainer description={update.description} />\r\n                    <Col>{getComponent()}</Col>\r\n                  </Col>\r\n                  <Col lg={5}>\r\n                    <p>\r\n                      <b>{t(\"UpdateType\")}</b>:<span> {updatetype} </span>\r\n                    </p>\r\n                    { updatetype === \"Contact\" && (update as any).contact_details &&\r\n                     <p>\r\n                     <b>{t(\"MobileNo\")}</b>:<span> {(update as any).contact_details.mobileNo} </span>\r\n                   </p>\r\n                    }\r\n                    { updatetype === \"Contact\" && (update as any).contact_details &&\r\n                     <p>\r\n                     <b>{t(\"TelephoneNo\")}</b>:<span> {(update as any).contact_details.telephoneNo} </span>\r\n                   </p>\r\n                    }\r\n                    {updatetype === Calendar && (update as any).start_date &&\r\n                      <p>\r\n                        <b>{t(\"StartDate\")}</b>:<span> {moment((update as any).start_date).format(formatDate)}</span>\r\n\r\n                      </p>\r\n                    }\r\n                    {updatetype === Calendar && (update as any).end_date &&\r\n                      <p>\r\n                        <b>{t(\"EndDate\")}</b>:<span>{moment((update as any).end_date).format(formatDate)}</span>\r\n                      </p>\r\n                    }\r\n                    <p>\r\n                      <b>{t(\"Created\")}</b>:\r\n                      <span>\r\n                        {moment(update.created_at).format(formatDate)}\r\n                      </span>\r\n                    </p>\r\n                    <p>\r\n                      <b>{t(\"LastModified\")}</b>:\r\n                      <span>\r\n                        {moment(update.updated_at).format(formatDate)}\r\n                      </span>\r\n                    </p>\r\n                  </Col>\r\n                </Row>\r\n              </Card.Body>\r\n            </Row>\r\n          </div>\r\n        </Collapse>\r\n      ) : null}\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default UpdatePopup;\r\n", "//Import Library\r\nimport { useState } from \"react\";\r\n\r\n//Import services/components\r\nimport { useTranslation } from 'next-i18next';\r\n\r\ninterface ReadMoreContainerProps {\r\n  description: string;\r\n}\r\n\r\nconst ReadMoreContainer = (props: ReadMoreContainerProps) => {\r\n  const { t } = useTranslation('common');\r\n  const readMoreLength = parseInt(process.env.READ_MORE_LENGTH || '200');\r\n  const [isReadMore, setIsReadMore] = useState(false);\r\n\r\n  const createMarkup = (htmlContent: string, isReadMoreInitial: boolean) => {\r\n    const truncateContent = (!isReadMoreInitial && htmlContent.length > readMoreLength) ? htmlContent.substring(0, readMoreLength) + \"...\" : props.description;\r\n    return { __html: truncateContent };\r\n  };\r\n\r\n  return (\r\n    <>\r\n      {\r\n        props.description  ?\r\n        <div\r\n          dangerouslySetInnerHTML={createMarkup(props.description,isReadMore)}\r\n          className=\"operationDesc\"\r\n        >\r\n        </div> : null\r\n      }\r\n      {\r\n        props.description && props.description.length > readMoreLength ?\r\n          <button type=\"button\" className=\"readMoreText\" onClick={() => setIsReadMore(!isReadMore)}>\r\n         {isReadMore ? t(\"readLess\") : t(\"readMore\")}\r\n          </button> : null\r\n      }\r\n    </>\r\n  )\r\n}\r\n\r\nexport default ReadMoreContainer;\r\n"], "names": ["props", "t", "useTranslation", "ReactImages", "images", "setImages", "useState", "renderImageLegend", "item", "isValidLink", "test", "description", "div", "className", "p", "b", "originalName", "FontAwesomeIcon", "icon", "faLink", "size", "color", "a", "href", "target", "rel", "style", "wordBreak", "downloadLink", "<PERSON><PERSON>", "faDownload", "useEffect", "carouselImages", "gallery", "Array", "isArray", "map", "i", "imgSrc", "fileType", "name", "split", "pop", "process", "_id", "_link", "_name", "original_name", "_description", "imageSource", "length", "push", "src", "Carousel", "showThumbs", "showStatus", "showIndicators", "infiniteLoop", "useKeyboardArrows", "autoPlay", "stopOnHover", "swipeable", "dynamicHeight", "emulate<PERSON><PERSON><PERSON>", "renderThumbs", "index", "img", "alt", "width", "height", "objectFit", "maxHeight", "updateTypeByIcon", "FileIcons", "faFilePdf", "faFile", "faFileExcel", "rep", "extension", "replace", "trim", "Document", "document", "doc_src", "Row", "Col", "OverlayTrigger", "trigger", "placement", "overlay", "Popover", "id", "Header", "as", "Body", "faInfoCircle", "Links", "link", "faGlobe", "span", "title", "data", "Image", "srcText", "formatDate", "Calendar", "UpdatePopup", "update", "setUpdate", "type", "created_at", "updated_at", "reply", "update_type", "images_src", "contact_details", "setPopup", "enable", "event", "updatetype", "setUpdateType", "smShow", "setSmShow", "smhandleClose", "confirm", "itemInitial", "apiService", "remove", "parentType", "Router", "_cleatTimeout", "fetchUpdateData", "respData", "get", "getUpdateType", "setTimeout", "prevState", "routes", "clearTimeout", "EditLink", "Link", "pathname", "query", "parent_type", "DeleteLink", "onClick", "Modal", "show", "onHide", "aria-<PERSON>by", "closeButton", "Title", "Footer", "variant", "CanEditUpdate", "canEditUpdate", "CanDeleteUpdate", "canDeleteUpdate", "popup", "Spinner", "animation", "Collapse", "in", "Card", "h3", "ReadMoreContainer", "getComponent", "lg", "mobileNo", "telephoneNo", "start_date", "moment", "format", "end_date", "readMoreLength", "parseInt", "isReadMore", "dangerouslySetInnerHTML", "createMarkup", "htmlContent", "isReadMoreInitial", "__html", "truncate<PERSON><PERSON><PERSON>", "substring", "button", "setIsReadMore"], "sourceRoot": "", "ignoreList": []}