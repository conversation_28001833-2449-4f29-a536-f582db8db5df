{"version": 3, "file": "static/chunks/pages/events-calendar-333c95ab1e10e8a3.js", "mappings": "2FAAA,aACA,IACA,+EAAyF,EACzF,CAAI,UACJ,oBACA,SACA,EAAG,EACH,+CCPA,cACA,gGACA,QACA,0FCDA,gBACA,iBAAwB,OAAO,oCAC/B,0FACA,MAAS,OAAqB,GAC9B,gECLA,gBACA,uGACA,2CACA,aACA,QACA,YACA,eACA,CACA,CAAG,uCACH,WACA,CAAG,KAAQ,OAAc,KACzB,iDCZA,oBACA,YACA,8BACA,EAUA,gBACA,uBACA,SAEA,QAbA,IAaA,IAAoB,WAAsB,IAC1C,MAdA,EAcA,SAdA,EAcA,OAVA,eAWA,SAGA,QACA,CAEA,gBACA,aAA8B,KAC9B,WACA,aAEA,QADA,KACA,IAAyB,mBAAuB,IAChD,kBAEA,yCACA,oBAEA,sBAMA,OALA,GACA,aACA,WACA,eAEA,CACA,CAIA,OAHA,mBACA,MACA,EACA,CACA,gEC/CA,gBACA,YAAkB,WAAc,KAChC,WACA,qGAAwH,OAAa,UACrI,CACA,CACA,kBACA,0EACA,WACA,CAAG,GACH,sNCHA,IAAMA,EAAYC,CAAAA,EAAAA,EAAAA,EAAAA,CAAeA,CAACC,KA0BlC,SAASC,EAAYC,CAAuB,EAC1C,GAAM,CAAEC,CAAC,MAAEC,CAAI,CAAE,CAAGC,CAAAA,EAAAA,EAAAA,EAAAA,CAAcA,CAAC,IA3BGL,MA4BhCM,EAAcF,EAAKG,QAAQ,CAC3B,YAAEC,CAAU,OAAEC,CAAK,cAAEC,CAAY,OAAEC,CAAK,CAAE,CAAGT,EAC/CU,EAAyB,CAAC,EAc9B,OAbIV,EAAMW,eAAe,EAAE,CACzBD,EAAoB,CAClBE,aAAcC,EACdC,MAAO,CACLC,WAAY,GACT,UAACC,EAAAA,CAAWV,WAAYA,EAAa,GAAGW,CAAW,EACxD,EACF,EAEET,GACFE,GAAkBQ,OAAO,CADT,CACYC,EAI5B,UAACC,EAAAA,EAAQA,CAAAA,CACPC,QAASjB,EACTR,UAAWA,EACX0B,OAAQhB,EACRG,MAAOA,EACPc,cAAc,aACdC,YAAY,WACZjB,MAAOA,EACPkB,WAAYf,EACZgB,SAAU,CAACC,MAAM1B,EAAE,SAAS2B,SAAS3B,EAAE,QAAQ4B,KAAK5B,EAAE,QAAQa,MAAMb,EAAE,SAAS6B,KAAK7B,EAAE,QAAQ8B,IAAI9B,EAAE,MAAM,EAC1G+B,cAAe,IACb,IAAMC,EAAaC,OAAOC,IAAI,CAACC,GAC5BC,MAAM,CAAC,GAAUC,EAAKC,QAAQ,CAAC,WAC/BC,QAAQ,GACLC,EAAYR,EAAWS,KAAK,CAAC,IAAI,CAAC,EAAE,CAC1CC,IAAAA,IAAW,CACT,IAAsBP,MAAAA,CAAlBK,EAAU,UAAoCL,MAAAA,CAA5BA,CAAK,CAACH,EAAW,CAAC,YAAoB,OAAVG,EAAMQ,GAAG,EAE/D,GAGN,CAOA,SAASzB,EAAenB,CAAmB,EACzC,MACE,UAAC6C,EAAAA,CAASA,CAAAA,CAACC,UAAU,gBACnB,WAACC,EAAAA,CAAGA,CAAAA,WACF,UAACC,EAAAA,CAAGA,CAAAA,CAACF,UAAU,MAAMG,GAAI,WACvB,UAACC,IAAAA,CACC3C,MAAO,CAAE4C,OAAQ,SAAU,EAC3BC,QAAS,IAAMpD,EAAMqD,UAAU,CAAC,QAChCP,UAAY,0BAGhB,UAACE,EAAAA,CAAGA,CAAAA,CAACF,UAAU,cAAcG,GAAI,YAC/B,UAACK,OAAAA,CAAKR,UAAU,6BAAqB9C,EAAMuD,KAAK,KAElD,UAACP,EAAAA,CAAGA,CAAAA,CAACF,UAAU,eAAeG,GAAI,WAChC,UAACC,IAAAA,CACC3C,MAAO,CAAE4C,OAAQ,SAAU,EAC3BC,QAAS,IAAMpD,EAAMqD,UAAU,CAAC,QAChCP,UAAY,+BAMxB,CA7BA/C,EAAYyD,YAAY,CAAG,CACzBhD,cAAc,EACdC,MAAO,CAAC,QAAQ,EA8BlB,IAAMgD,EAAiB,CAACnD,EAA6BoD,KACnD,IAAIC,EAAc,EAoBlB,OAnBAC,IAAAA,OAAS,CAACtD,EAAY,IACpB,IAAMuD,EAAY/D,IAAOgE,EAAEC,UAAU,EAAEC,GAAG,CAAC,CACzCC,KAAM,EACNC,OAAQ,EACRC,MAHsBrE,CAGd,EACRsE,YAAa,CACf,GACMC,EAAUvE,IAAOgE,EAAEQ,QAAQ,EAAEN,GAAG,CAAC,CACrCC,KAAM,EACNC,OAAQ,EACRC,OAAQ,CAHYrE,CAIpBsE,YAAa,CACf,EAGIG,CADYzE,IAAO4D,GAAMc,CAChB,QADyB,CAACX,EAAWQ,EAAS,KAAM,QAE/DV,IAAe,CAEnB,GACOA,CACT,EAEM3C,EAAa,OAAC,MAAE0C,CAAI,CAAEH,OAAK,YAAEjD,CAAU,CAA8D,GACnGqD,EAAcF,EAAenD,EAAYoD,GACzCe,EAAmB3E,IAAO4D,GAAMgB,QAAQ,CAAC,IAAIC,KAAQ,OAE3D,MACE,OAH6B7E,EAG7B,EAAC8E,MAAAA,CACC9B,UAAU,gBACVM,QAAS,IAAMT,IAAAA,IAAW,CAAC,8BAE3B,UAACkC,IAAAA,CAAEC,KAAK,aAAKvB,IACZI,EAAc,GACb,WAACL,OAAAA,CAAKR,UAAU,qEACd,UAACiC,EAAAA,CAAeA,CAAAA,CACdC,KAAMC,EAAAA,EAAMA,CACZC,MAAOT,EAAmB,OAAS,UACnCU,KAAK,OAEP,UAAC7B,OAAAA,CAAKR,UAAU,sBAAca,SAKxC,EAEM9C,EAAe,GACZ,UAAC+D,MAAAA,CAAIQ,SAAUpF,EAAMoF,QAAQ,GAGtC,EAAerF,WAAWA,EAAC,MC/J3B,4CACA,mBACA,WACA,OAAe,EAAQ,KAA8C,CACrE,EACA,SAFsB,uDCHtB,gBACA,qBACA,iCACA,qCACA,4BACA,wDACK,mBACL,CACA,QACA,CACA,cACA,YAAkB,mBAAsB,KACxC,wCACA,yCACM,OAAc,UACpB,CAAK,mIACL,+DACA,CAAK,CACL,CACA,QACA,gCCrBA,gBACA,yEACA,gDCFA,cACA,wEACA,4CACA,EAAG,GACH,sMC+DA,MAzDuB,IACrB,GAAM,CAACuB,EAAQ+D,EAAU,CAAGC,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAC,EAAE,EAEjCC,EAAkB,QAsDGC,EArDzB,IAAMC,EAAW,MAAMC,EAAAA,CAAUA,CAACC,GAAG,CAAC,cAAe,CACnDC,MAAO,CAAEC,MAAO,gBAAiB,CACnC,GACIJ,GAAYA,EAASK,IAAI,EAC3BC,EAAoBN,EAASK,IAAI,CAAC,EAAE,CAAClD,GAAG,CAE5C,EAEMmD,EAAsB,MAAOC,IAQjC,IAAMP,EAAW,MAAMC,EAAAA,CAAUA,CAACC,GAAG,CAAC,WAPT,CAC3BC,MAAO,CAAEK,YAAaD,CAAa,EACnCE,KAAM,CAAEC,WAAY,MAAO,EAC3BC,MAAO,GACPC,OACE,wJACJ,GAEIZ,GAAYA,EAASK,IAAI,EAAE,CAC7BlC,IAAAA,OAAS,CAAC6B,EAASK,IAAI,CAAE,SAAUQ,CAAG,CAAEpD,CAAC,EACvCuC,EAASK,IAAI,CAAC5C,EAAE,CAACa,UAAU,CAAG,IAAIY,KAAK2B,EAAIvC,UAAU,EACrD0B,EAASK,IAAI,CAAC5C,EAAE,CAACoB,QAAQ,CAAG,IAAIK,KAAK2B,EAAIhC,QAAQ,CACnD,GACAe,EAAUI,EAASK,IAAI,EAE3B,EAMA,MAJAS,CAAAA,EAAAA,EAAAA,SAAAA,CAASA,CAAC,KACRhB,GACF,EAAG,EAAE,EAGH,UAAC1C,EAAAA,CAASA,CAAAA,UACR,UAACE,EAAAA,CAAGA,CAAAA,UACF,UAACC,EAAAA,CAAGA,CAAAA,CAACF,UAAU,OAAO0D,GAAG,cACvB,UAACzG,EAAAA,CAAWA,CAAAA,CACVU,MAAO,CAAC,QAAS,OAAQ,MAAM,CAC/BF,MAAO,CAAEkG,OAAQ,GAAI,EACrBnG,WAAYgB,SAMxB", "sources": ["webpack://_N_E/./node_modules/@babel/runtime/helpers/esm/isNativeReflectConstruct.js", "webpack://_N_E/./node_modules/@babel/runtime/helpers/esm/assertThisInitialized.js", "webpack://_N_E/./node_modules/@babel/runtime/helpers/esm/possibleConstructorReturn.js", "webpack://_N_E/./node_modules/@babel/runtime/helpers/esm/inherits.js", "webpack://_N_E/./node_modules/memoize-one/dist/memoize-one.esm.js", "webpack://_N_E/./node_modules/@babel/runtime/helpers/esm/createClass.js", "webpack://_N_E/./components/common/RKICalendar.tsx", "webpack://_N_E/?e88f", "webpack://_N_E/./node_modules/@babel/runtime/helpers/esm/objectSpread2.js", "webpack://_N_E/./node_modules/@babel/runtime/helpers/esm/classCallCheck.js", "webpack://_N_E/./node_modules/@babel/runtime/helpers/esm/getPrototypeOf.js", "webpack://_N_E/./pages/events-calendar/index.tsx"], "sourcesContent": ["function _isNativeReflectConstruct() {\n  try {\n    var t = !Boolean.prototype.valueOf.call(Reflect.construct(<PERSON><PERSON><PERSON>, [], function () {}));\n  } catch (t) {}\n  return (_isNativeReflectConstruct = function _isNativeReflectConstruct() {\n    return !!t;\n  })();\n}\nexport { _isNativeReflectConstruct as default };", "function _assertThisInitialized(e) {\n  if (void 0 === e) throw new ReferenceError(\"this hasn't been initialised - super() hasn't been called\");\n  return e;\n}\nexport { _assertThisInitialized as default };", "import _typeof from \"./typeof.js\";\nimport assertThisInitialized from \"./assertThisInitialized.js\";\nfunction _possibleConstructorReturn(t, e) {\n  if (e && (\"object\" == _typeof(e) || \"function\" == typeof e)) return e;\n  if (void 0 !== e) throw new TypeError(\"Derived constructors may only return object or undefined\");\n  return assertThisInitialized(t);\n}\nexport { _possibleConstructorReturn as default };", "import setPrototypeOf from \"./setPrototypeOf.js\";\nfunction _inherits(t, e) {\n  if (\"function\" != typeof e && null !== e) throw new TypeError(\"Super expression must either be null or a function\");\n  t.prototype = Object.create(e && e.prototype, {\n    constructor: {\n      value: t,\n      writable: !0,\n      configurable: !0\n    }\n  }), Object.defineProperty(t, \"prototype\", {\n    writable: !1\n  }), e && setPrototypeOf(t, e);\n}\nexport { _inherits as default };", "var safeIsNaN = Number.isNaN ||\n    function ponyfill(value) {\n        return typeof value === 'number' && value !== value;\n    };\nfunction isEqual(first, second) {\n    if (first === second) {\n        return true;\n    }\n    if (safeIsNaN(first) && safeIsNaN(second)) {\n        return true;\n    }\n    return false;\n}\nfunction areInputsEqual(newInputs, lastInputs) {\n    if (newInputs.length !== lastInputs.length) {\n        return false;\n    }\n    for (var i = 0; i < newInputs.length; i++) {\n        if (!isEqual(newInputs[i], lastInputs[i])) {\n            return false;\n        }\n    }\n    return true;\n}\n\nfunction memoizeOne(resultFn, isEqual) {\n    if (isEqual === void 0) { isEqual = areInputsEqual; }\n    var cache = null;\n    function memoized() {\n        var newArgs = [];\n        for (var _i = 0; _i < arguments.length; _i++) {\n            newArgs[_i] = arguments[_i];\n        }\n        if (cache && cache.lastThis === this && isEqual(newArgs, cache.lastArgs)) {\n            return cache.lastResult;\n        }\n        var lastResult = resultFn.apply(this, newArgs);\n        cache = {\n            lastResult: lastResult,\n            lastArgs: newArgs,\n            lastThis: this,\n        };\n        return lastResult;\n    }\n    memoized.clear = function clear() {\n        cache = null;\n    };\n    return memoized;\n}\n\nexport { memoizeOne as default };\n", "import toPropertyKey from \"./toPropertyKey.js\";\nfunction _defineProperties(e, r) {\n  for (var t = 0; t < r.length; t++) {\n    var o = r[t];\n    o.enumerable = o.enumerable || !1, o.configurable = !0, \"value\" in o && (o.writable = !0), Object.defineProperty(e, toPropertyKey(o.key), o);\n  }\n}\nfunction _createClass(e, r, t) {\n  return r && _defineProperties(e.prototype, r), t && _defineProperties(e, t), Object.defineProperty(e, \"prototype\", {\n    writable: !1\n  }), e;\n}\nexport { _createClass as default };", "//Import Library\r\nimport { Calendar, momentLocalizer, View } from \"react-big-calendar\";\r\nimport moment from \"moment\";\r\nimport 'moment/locale/fr';\r\nimport Router from \"next/router\";\r\nimport { Col, Container, Row } from \"react-bootstrap\";\r\nimport { FontAwesomeIcon } from \"@fortawesome/react-fontawesome\";\r\nimport { faStar } from \"@fortawesome/free-solid-svg-icons\";\r\nconst localizer = momentLocalizer(moment);\r\nimport _ from \"lodash\";\r\n\r\n//Import services/components\r\nimport { useTranslation } from 'next-i18next';\r\n\r\ninterface CalendarEvent {\r\n  _id: string;\r\n  start_date: string | Date;\r\n  end_date: string | Date;\r\n  [key: string]: any;\r\n}\r\n\r\ninterface ToolbarProps {\r\n  label: string;\r\n  onNavigate: (action: string) => void;\r\n}\r\n\r\ninterface RKICalendarProps {\r\n  eventsList: CalendarEvent[];\r\n  style?: React.CSSProperties;\r\n  minicalendar?: boolean;\r\n  views?: View[];\r\n  showEventCounts?: boolean;\r\n}\r\n\r\nfunction RKICalendar(props: RKICalendarProps) {\r\n  const { t, i18n } = useTranslation('common');\r\n  const currentLang = i18n.language\r\n  const { eventsList, style, minicalendar, views } = props;\r\n  let calendarComponent: any = {};\r\n  if (props.showEventCounts) {\r\n    calendarComponent = {\r\n      eventWrapper: EventWrapper,\r\n      month: {\r\n        dateHeader: (headerProps: any) =>\r\n           <DateHeader eventsList={eventsList} {...headerProps} />\r\n      },\r\n    };\r\n  }\r\n  if (minicalendar) {\r\n    calendarComponent.toolbar = MinimalToolbar;\r\n  }\r\n\r\n  return (\r\n    <Calendar\r\n      culture={currentLang}\r\n      localizer={localizer}\r\n      events={eventsList}\r\n      views={views}\r\n      startAccessor=\"start_date\"\r\n      endAccessor=\"end_date\"\r\n      style={style}\r\n      components={calendarComponent}\r\n      messages={{today:t(\"today\"),previous:t(\"back\"),next:t(\"Next\"),month:t(\"Month\"),week:t(\"Week\"),day:t(\"Day\")}}\r\n      onSelectEvent={(event: CalendarEvent) => {\r\n        const findOption = Object.keys(event)\r\n          .filter((item) => item.includes(\"parent\"))\r\n          .toString();\r\n        const urlAppend = findOption.split(\"_\")[1];\r\n        Router.push(\r\n          `/${urlAppend}/show/${event[findOption]}/update/${event._id}`\r\n        );\r\n      }}\r\n    />\r\n  );\r\n}\r\n\r\nRKICalendar.defaultProps = {\r\n  minicalendar: false,\r\n  views: [\"month\"],\r\n};\r\n\r\nfunction MinimalToolbar(props: ToolbarProps) {\r\n  return (\r\n    <Container className=\"mb-1\">\r\n      <Row>\r\n        <Col className=\"p-0\" md={1}>\r\n          <i\r\n            style={{ cursor: \"pointer\" }}\r\n            onClick={() => props.onNavigate(\"PREV\")}\r\n            className={`fas fa-chevron-left`}\r\n          />\r\n        </Col>\r\n        <Col className=\"text-center\" md={10}>\r\n          <span className=\"rbc-toolbar-label\">{props.label}</span>\r\n        </Col>\r\n        <Col className=\"p-0 text-end\" md={1}>\r\n          <i\r\n            style={{ cursor: \"pointer\" }}\r\n            onClick={() => props.onNavigate(\"NEXT\")}\r\n            className={`fas fa-chevron-right`}\r\n          />\r\n        </Col>\r\n      </Row>\r\n    </Container>\r\n  );\r\n}\r\n\r\n// Generates event counts based on the given date\r\nconst getEventsCount = (eventsList: CalendarEvent[], date: Date): number => {\r\n  let eventsCount = 0;\r\n  _.forEach(eventsList, (e) => {\r\n    const startDate = moment(e.start_date).set({\r\n      hour: 0,\r\n      minute: 0,\r\n      second: 0,\r\n      millisecond: 0,\r\n    });\r\n    const endDate = moment(e.end_date).set({\r\n      hour: 0,\r\n      minute: 0,\r\n      second: 0,\r\n      millisecond: 0,\r\n    });\r\n\r\n    const isEvent = moment(date).isBetween(startDate, endDate, null, \"[]\");\r\n    if (isEvent) {\r\n      eventsCount += 1;\r\n    }\r\n  });\r\n  return eventsCount;\r\n};\r\n\r\nconst DateHeader = ({ date, label, eventsList }: { date: Date; label: string; eventsList: CalendarEvent[] }) => {\r\n  const eventsCount = getEventsCount(eventsList, date);\r\n  const isEventCompleted = moment(date).isBefore(new Date(), \"day\");\r\n\r\n  return (\r\n    <div\r\n      className=\"rbc-date-cell\"\r\n      onClick={() => Router.push(\"/events-calendar\")}\r\n    >\r\n      <a href=\"#\">{label}</a>\r\n      {eventsCount > 0 && (\r\n        <span className=\"d-flex justify-content-start align-items-center fa-stack\">\r\n          <FontAwesomeIcon\r\n            icon={faStar}\r\n            color={isEventCompleted ? \"grey\" : \"#04A6FB\"}\r\n            size=\"lg\"\r\n          />\r\n          <span className=\"eventCount\">{eventsCount}</span>\r\n        </span>\r\n      )}\r\n    </div>\r\n  );\r\n};\r\n\r\nconst EventWrapper = (props: { onSelect?: () => void }) => {\r\n  return <div onSelect={props.onSelect} />;\r\n};\r\n\r\nexport default RKICalendar;\r\n", "\n    (window.__NEXT_P = window.__NEXT_P || []).push([\n      \"/events-calendar\",\n      function () {\n        return require(\"private-next-pages/events-calendar/index.tsx\");\n      }\n    ]);\n    if(module.hot) {\n      module.hot.dispose(function () {\n        window.__NEXT_P.push([\"/events-calendar\"])\n      });\n    }\n  ", "import defineProperty from \"./defineProperty.js\";\nfunction ownKeys(e, r) {\n  var t = Object.keys(e);\n  if (Object.getOwnPropertySymbols) {\n    var o = Object.getOwnPropertySymbols(e);\n    r && (o = o.filter(function (r) {\n      return Object.getOwnPropertyDescriptor(e, r).enumerable;\n    })), t.push.apply(t, o);\n  }\n  return t;\n}\nfunction _objectSpread2(e) {\n  for (var r = 1; r < arguments.length; r++) {\n    var t = null != arguments[r] ? arguments[r] : {};\n    r % 2 ? ownKeys(Object(t), !0).forEach(function (r) {\n      defineProperty(e, r, t[r]);\n    }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) {\n      Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r));\n    });\n  }\n  return e;\n}\nexport { _objectSpread2 as default };", "function _classCallCheck(a, n) {\n  if (!(a instanceof n)) throw new TypeError(\"Cannot call a class as a function\");\n}\nexport { _classCallCheck as default };", "function _getPrototypeOf(t) {\n  return _getPrototypeOf = Object.setPrototypeOf ? Object.getPrototypeOf.bind() : function (t) {\n    return t.__proto__ || Object.getPrototypeOf(t);\n  }, _getPrototypeOf(t);\n}\nexport { _getPrototypeOf as default };", "//Import Library\r\nimport { Container, <PERSON>, <PERSON> } from \"react-bootstrap\";\r\nimport _ from \"lodash\";\r\nimport { useEffect, useState } from \"react\";\r\n\r\n//Import services/components\r\nimport RKICalendar from \"../../components/common/RKICalendar\";\r\nimport apiService from \"../../services/apiService\";\r\nimport { serverSideTranslations } from 'next-i18next/serverSideTranslations';\r\n\r\nconst eventsCalendar = (_props: any) => {\r\n  const [events, setEvents] = useState([]);\r\n\r\n  const fetchUpdateType = async () => {\r\n    const response = await apiService.get(\"/updateType\", {\r\n      query: { title: \"Calendar Event\" },\r\n    });\r\n    if (response && response.data) {\r\n      fetchCalendarEvents(response.data[0]._id);\r\n    }\r\n  };\r\n\r\n  const fetchCalendarEvents = async (updateTypeId: any) => {\r\n    const calendarEventsParams = {\r\n      query: { update_type: updateTypeId },\r\n      sort: { created_at: \"desc\" },\r\n      limit: 20,\r\n      select:\r\n        \"-created_at -updated_at -update_type -contact_details -description -document -images -link -media -parent_operation -reply -show_as_announcement -type\",\r\n    };\r\n    const response = await apiService.get(\"/updates\", calendarEventsParams);\r\n    if (response && response.data) {\r\n      _.forEach(response.data, function (val, i) {\r\n        response.data[i].start_date = new Date(val.start_date);\r\n        response.data[i].end_date = new Date(val.end_date);\r\n      });\r\n      setEvents(response.data);\r\n    }\r\n  };\r\n\r\n  useEffect(() => {\r\n    fetchUpdateType();\r\n  }, []);\r\n\r\n  return (\r\n    <Container>\r\n      <Row>\r\n        <Col className=\"pe-0\" xs=\"12\">\r\n          <RKICalendar\r\n            views={[\"month\", \"week\", \"day\"]}\r\n            style={{ height: 800 }}\r\n            eventsList={events}\r\n          />\r\n        </Col>\r\n      </Row>\r\n    </Container>\r\n  );\r\n};\r\n\r\nexport async function getStaticProps({ locale }: { locale: string }) {\r\n  return {\r\n    props: {\r\n      ...(await serverSideTranslations(locale, ['common'])),\r\n    },\r\n  }\r\n}\r\n\r\nexport default eventsCalendar;\r\n"], "names": ["localizer", "momentLocalizer", "moment", "RKICalendar", "props", "t", "i18n", "useTranslation", "currentLang", "language", "eventsList", "style", "minicalendar", "views", "calendarComponent", "showEventCounts", "eventWrapper", "EventWrapper", "month", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "headerProps", "toolbar", "MinimalToolbar", "Calendar", "culture", "events", "startAccessor", "endAccessor", "components", "messages", "today", "previous", "next", "week", "day", "onSelectEvent", "findOption", "Object", "keys", "event", "filter", "item", "includes", "toString", "urlAppend", "split", "Router", "_id", "Container", "className", "Row", "Col", "md", "i", "cursor", "onClick", "onNavigate", "span", "label", "defaultProps", "getEventsCount", "date", "eventsCount", "_", "startDate", "e", "start_date", "set", "hour", "minute", "second", "millisecond", "endDate", "end_date", "isEvent", "isBetween", "isEventCompleted", "isBefore", "Date", "div", "a", "href", "FontAwesomeIcon", "icon", "faStar", "color", "size", "onSelect", "setEvents", "useState", "fetchUpdateType", "eventsCalendar", "response", "apiService", "get", "query", "title", "data", "fetchCalendarEvents", "updateTypeId", "update_type", "sort", "created_at", "limit", "select", "val", "useEffect", "xs", "height"], "sourceRoot": "", "ignoreList": [0, 1, 2, 3, 4, 5, 8, 9, 10]}