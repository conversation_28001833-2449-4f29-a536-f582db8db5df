(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[5841],{15641:(e,t,r)=>{"use strict";r.d(t,{A:()=>n});var l=r(37876);r(14232);var o=r(62945);let n=e=>{let{name:t="Marker",id:r="",countryId:n="",type:s,icon:i,position:a,onClick:y,title:p,draggable:c=!1}=e;return a&&"number"==typeof a.lat&&"number"==typeof a.lng?(0,l.jsx)(o.pH,{position:a,icon:i,title:p||t,draggable:c,onClick:e=>{y&&y({name:t,id:r,countryId:n,type:s,position:a},{position:a,getPosition:()=>a},e)}}):null}},37578:(e,t,r)=>{(window.__NEXT_P=window.__NEXT_P||[]).push(["/project/ListMapContainer",function(){return r(39598)}])},39598:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>p});var l=r(37876),o=r(14232),n=r(82851),s=r.n(n),i=r(31753),a=r(66619),y=r(15641);let p=e=>{let{i18n:t}=(0,i.Bd)("common"),r=t.language,{projects:n,selectedRegions:p}=e,[c,u]=(0,o.useState)([]),[d,m]=(0,o.useState)({}),[f,T]=(0,o.useState)({}),[g,_]=(0,o.useState)({}),h=()=>{m(null),T(null)},b=(e,t,r)=>{h(),m(t),T({name:e.name,id:e.id,countryId:e.countryId})},v=e=>e.partner_country&&e.partner_country.coordinates,k=e=>e.partner_country&&e.partner_country.coordinates,x=e=>{let t=[];return s().forEach(e.partner_institutions,r=>{console.log("pointer",r),t.push({title:e&&e.title?e.title:"",id:e&&e._id?e._id:"",lat:v(r)&&parseFloat(r.partner_country.coordinates[0].latitude),lng:k(r)&&parseFloat(r.partner_country.coordinates[0].longitude),world_region:r.world_region,countryId:r.partner_country&&r.partner_country._id})}),t[0]},j=()=>{let e=[];s().forEach(n,t=>{let r=x(t);e.push(r)}),u(s().filter(e,function(e){if(p.length>0)return p.includes(e.world_region)}))},w=()=>{let e=[];s().forEach(n,t=>{t.partner_institutions&&t.partner_institutions.length>0&&s().forEach(t.partner_institutions,r=>{r.title=t.title,r.id=t._id,e.push(r)})}),_(s().groupBy(e,"partner_country._id"))};return(0,o.useEffect)(()=>{j(),w()},[n]),(0,l.jsx)(a.A,{onClose:h,language:r,activeMarker:d,markerInfo:(0,l.jsx)(e=>{let{info:t}=e;return t&&t.countryId&&g[t.countryId]?(0,l.jsx)("ul",{children:g[t.countryId].map((e,t)=>(0,l.jsx)("li",{children:(0,l.jsx)("a",{href:"/".concat(r,"/project/show/").concat(e.id),children:e.title})},t))}):null},{info:f}),children:c.length>=1?c.map((e,t)=>(0,l.jsx)(y.A,{name:e.title,id:e.id,countryId:e.countryId,icon:{url:"/images/map-marker-white.svg"},onClick:b,position:e},t)):null})}},66619:(e,t,r)=>{"use strict";r.d(t,{A:()=>d});var l=r(37876);r(14232);var o=r(62945);let n=e=>{let{position:t,onCloseClick:r,children:n}=e;return(0,l.jsx)(o.Fu,{position:t,onCloseClick:r,children:(0,l.jsx)("div",{children:n})})},s="labels.text.fill",i="labels.text.stroke",a="road.highway",y="geometry.stroke",p=[{elementType:"geometry",stylers:[{color:"#1d2c4d"}]},{elementType:s,stylers:[{color:"#8ec3b9"}]},{elementType:i,stylers:[{color:"#1a3646"}]},{featureType:"administrative",elementType:"geometry",stylers:[{visibility:"off"}]},{featureType:"administrative.country",elementType:y,stylers:[{color:"#4b6878"}]},{featureType:"administrative.land_parcel",elementType:"labels",stylers:[{visibility:"off"}]},{featureType:"administrative.land_parcel",elementType:s,stylers:[{color:"#64779e"}]},{featureType:"administrative.province",elementType:y,stylers:[{color:"#4b6878"}]},{featureType:"landscape.man_made",elementType:y,stylers:[{color:"#334e87"}]},{featureType:"landscape.natural",elementType:"geometry",stylers:[{color:"#023e58"}]},{featureType:"poi",stylers:[{visibility:"off"}]},{featureType:"poi",elementType:"geometry",stylers:[{color:"#283d6a"}]},{featureType:"poi",elementType:"labels.text",stylers:[{visibility:"off"}]},{featureType:"poi",elementType:s,stylers:[{color:"#6f9ba5"}]},{featureType:"poi",elementType:i,stylers:[{color:"#1d2c4d"}]},{featureType:"poi.park",elementType:"geometry.fill",stylers:[{color:"#023e58"}]},{featureType:"poi.park",elementType:s,stylers:[{color:"#3C7680"}]},{featureType:"road",stylers:[{visibility:"off"}]},{featureType:"road",elementType:"geometry",stylers:[{color:"#304a7d"}]},{featureType:"road",elementType:"labels.icon",stylers:[{visibility:"off"}]},{featureType:"road",elementType:s,stylers:[{color:"#98a5be"}]},{featureType:"road",elementType:i,stylers:[{color:"#1d2c4d"}]},{featureType:a,elementType:"geometry",stylers:[{color:"#2c6675"}]},{featureType:a,elementType:y,stylers:[{color:"#255763"}]},{featureType:a,elementType:s,stylers:[{color:"#b0d5ce"}]},{featureType:a,elementType:i,stylers:[{color:"#023e58"}]},{featureType:"road.local",elementType:"labels",stylers:[{visibility:"off"}]},{featureType:"transit",stylers:[{visibility:"off"}]},{featureType:"transit",elementType:s,stylers:[{color:"#98a5be"}]},{featureType:"transit",elementType:i,stylers:[{color:"#1d2c4d"}]},{featureType:"transit.line",elementType:"geometry.fill",stylers:[{color:"#283d6a"}]},{featureType:"transit.station",elementType:"geometry",stylers:[{color:"#3a4762"}]},{featureType:"water",elementType:"geometry",stylers:[{color:"#0e1626"}]},{featureType:"water",elementType:s,stylers:[{color:"#4e6d70"}]}];var c=r(89099),u=r(55316);let d=e=>{let{markerInfo:t,activeMarker:r,initialCenter:s,children:i,height:a=300,width:y="114%",language:d,zoom:m=1,minZoom:f=1,onClose:T}=e,{locale:g}=(0,c.useRouter)(),{isLoaded:_,loadError:h}=(0,u._)();return h?(0,l.jsx)("div",{children:"Error loading maps"}):_?(0,l.jsx)("div",{className:"map-container",children:(0,l.jsx)("div",{className:"mapprint",style:{width:y,height:a,position:"relative"},children:(0,l.jsxs)(o.u6,{mapContainerStyle:{width:y,height:"number"==typeof a?"".concat(a,"px"):a},center:s||{lat:52.520017,lng:13.404195},zoom:m,onLoad:e=>{e.setOptions({styles:p})},options:{minZoom:f,draggable:!0,keyboardShortcuts:!1,streetViewControl:!1,panControl:!1,clickableIcons:!1,mapTypeControl:!1,fullscreenControl:!0},children:[i,t&&r&&r.getPosition&&(0,l.jsx)(n,{position:r.getPosition(),onCloseClick:()=>{console.log("close click"),null==T||T()},children:t})]})})}):(0,l.jsx)("div",{children:"Loading Maps..."})}}},e=>{var t=t=>e(e.s=t);e.O(0,[9759,636,6593,8792],()=>t(37578)),_N_E=e.O()}]);
//# sourceMappingURL=ListMapContainer-47bcaecaa44aa2e1.js.map