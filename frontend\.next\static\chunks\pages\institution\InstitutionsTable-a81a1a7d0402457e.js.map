{"version": 3, "file": "static/chunks/pages/institution/InstitutionsTable-a81a1a7d0402457e.js", "mappings": "gFACA,4CACA,iCACA,WACA,OAAe,EAAQ,KAAsD,CAC7E,EACA,SAFsB", "sources": ["webpack://_N_E/?4960"], "sourcesContent": ["\n    (window.__NEXT_P = window.__NEXT_P || []).push([\n      \"/institution/InstitutionsTable\",\n      function () {\n        return require(\"private-next-pages/institution/InstitutionsTable.tsx\");\n      }\n    ]);\n    if(module.hot) {\n      module.hot.dispose(function () {\n        window.__NEXT_P.push([\"/institution/InstitutionsTable\"])\n      });\n    }\n  "], "names": [], "sourceRoot": "", "ignoreList": []}