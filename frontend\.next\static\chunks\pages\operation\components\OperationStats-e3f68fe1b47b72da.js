(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[8689],{10248:(e,s,n)=>{"use strict";n.r(s),n.d(s,{default:()=>l});var i=n(37876);n(14232);var o=n(11041),r=n(21772),a=n(56970),t=n(37784),c=n(31753);let l=e=>{let{t:s}=(0,c.Bd)("common");return(0,i.jsx)(i.Fragment,{children:(0,i.jsx)("div",{className:"operationStats",children:(0,i.jsxs)(a.A,{children:[(0,i.jsxs)(t.A,{className:"operationInfo-Items",md:6,children:[(0,i.jsx)("div",{className:"operationIcon",children:(0,i.jsx)(r.g,{icon:o.gdJ,color:"#fff",size:"2x"})}),(0,i.jsxs)("div",{className:"operationInfo",children:[(0,i.jsx)("h5",{children:s("Partners")}),(0,i.jsx)("h4",{children:e.operation.partners.length})]})]}),(0,i.jsxs)(t.A,{className:"operationInfo-Items",md:6,children:[(0,i.jsx)("div",{className:"operationIcon",children:(0,i.jsx)(r.g,{icon:o.z$e,color:"#fff",size:"2x"})}),(0,i.jsxs)("div",{className:"operationInfo",children:[(0,i.jsx)("h5",{children:s("ActivitiesinField")}),(0,i.jsx)("h4",{children:e.operation.timeline.length})]})]})]})})})}},68348:(e,s,n)=>{(window.__NEXT_P=window.__NEXT_P||[]).push(["/operation/components/OperationStats",function(){return n(10248)}])}},e=>{var s=s=>e(e.s=s);e.O(0,[7725,1772,636,6593,8792],()=>s(68348)),_N_E=e.O()}]);
//# sourceMappingURL=OperationStats-e3f68fe1b47b72da.js.map