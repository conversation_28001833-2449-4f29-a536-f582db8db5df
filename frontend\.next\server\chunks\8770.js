"use strict";exports.id=8770,exports.ids=[8770],exports.modules={6417:(e,a,t)=>{t.d(a,{A:()=>r});let s=t(82015).createContext(null);s.displayName="CardHeaderContext";let r=s},8770:(e,a,t)=>{t.a(e,async(e,s)=>{try{t.r(a),t.d(a,{default:()=>S});var r=t(8732),d=t(82015),l=t(93024),i=t(83551),n=t(49481),c=t(13524),o=t(82053),u=t(54131),h=t(77136),m=t(88751),x=t(93268),j=t(31977),p=t(87824),f=t(89497),g=t(25920),N=t(94441),y=t(43388),v=t(82491),A=t(63487),w=e([u,h,y,v,A]);[u,h,y,v,A]=w.then?(await w)():w;let z="-country -description -hazard_type -created_at -region -start_date -status -syndrome -title -timeline -user -world_region -_id -part",S=e=>{var a,t,s,w,S,_,$,C,D,b;let{t:F,i18n:I}=(0,m.useTranslation)("common"),R="fr"===I.language?"en":I.language,[U,B]=(0,d.useState)({title:"",description:"",picture:""}),[k,T]=(0,d.useState)(!1),[H,E]=(0,d.useState)([]),[P,O]=(0,d.useState)([]),[L,M]=(0,d.useState)([]),[Z,q]=(0,d.useState)([]),[K,V]=(0,d.useState)(!1),[G,J]=(0,d.useState)(!1),[Q,W]=(0,d.useState)(!0),[X]=(0,d.useState)(!1),[Y,ee]=(0,d.useState)(""),[ea,et]=(0,d.useState)([]),[es,er]=(0,d.useState)([]),[ed,el]=(0,d.useState)([]),[ei]=(0,d.useState)([]),[en,ec]=(0,d.useState)([]),[eo,eu]=(0,d.useState)(!1),eh={query:{},limit:"~",sort:{title:"asc"}},em=async a=>{let t=[],s=[],r=[],d=await A.A.get(`/hazard/${e.routes[1]}`,a);if(d&&d.data&&d.data.length>0){d.data.forEach((e,a)=>{e.document&&e.document.length>0&&e.document.map((a,t)=>{a.description=e.document[t].docsrc,s.push(a)}),e.images&&e.images.length>0&&e.images.map((e,a)=>{r.push(e)}),e.images_src&&e.images_src.length>0&&e.images_src.map((e,a)=>{t.push(e)})}),et(s);var l=r?.reduce((e,a)=>{let t=e.find(e=>a._id===e._id);return t?t.count++:e.push({...a,count:1}),e},[]);el(l.flat(1/0)),ec(t.flat(1/0))}},ex=async a=>{let t=[],s=await A.A.get(`/hazard/${e.routes[1]}`,a);s&&s.data&&s.data.length>0&&(s.data.forEach((e,a)=>{e.document&&e.document.length>0&&e.document.map((a,s)=>{a.description=e.document[s].docsrc,t.push(a)})}),er(t))},ej=async a=>{T(!0);let t=await A.A.get(`/hazard/${e.routes[1]}`,a);t&&(t.picture=t?.picture&&t.picture._id?`http://localhost:3001/api/v1/image/show/${t.picture._id}`:"/images/disease-placeholder.3f65b286.jpg",t?.picture_source&&ee(t.picture_source),B(t),T(!1)),T(!1)},ep=async a=>{let t=await A.A.get(`/hazard/${e.routes[1]}/events/Closed`,a),s=t&&t.data?t.data:[];E(s)},ef=async a=>{let t=await A.A.get(`/hazard/${e.routes[1]}/events/Current`,a),s=t&&t.data?t.data:[];O(s)},eg=async a=>{let t=await A.A.get(`/hazard/${e.routes[1]}/operations`,a),s=t&&t.data?t.data:[];M(s)},eN=async a=>{let t=await A.A.get(`/hazard/${e.routes[1]}/institutions`,a),s=t&&t.data?t.data:[];q(s)};(0,d.useEffect)(()=>{e.routes&&e.routes[1]&&(ej({}),ep(eh),ef(eh),eg(eh),eN(eh),em({sort:{doc_created_at:"asc"},limit:"~",Doctable:!0,collation:"en",select:z}),ex({sort:{doc_created_at:"asc"},limit:"~",DocUpdatetable:!0,collation:"en",select:z}))},[]);let ey=()=>(0,r.jsxs)(l.A.Item,{eventKey:"1",children:[(0,r.jsxs)(l.A.Header,{onClick:()=>W(!Q),children:[(0,r.jsx)("div",{className:"cardTitle",children:F("discussions")}),(0,r.jsx)("div",{className:"cardArrow",children:Q?(0,r.jsx)(o.FontAwesomeIcon,{icon:u.faPlus,color:"#fff"}):(0,r.jsx)(o.FontAwesomeIcon,{icon:u.faMinus,color:"#fff"})})]}),(0,r.jsx)(l.A.Body,{children:(0,r.jsx)(v.A,{type:"hazard",id:e&&e.routes?e.routes[1]:null})})]});(0,x.canViewDiscussionUpdate)(()=>(0,r.jsx)(ey,{}));let ev=RegExp("^(http[s]?:\\/\\/(www\\.)?|ftp:\\/\\/(www\\.)?|www\\.){1}([0-9A-Za-z-\\.@:%_+~#=]+)+((\\.[a-zA-Z]{2,3})+)(/(.)*)?(\\?(.)*)?").test(Y);return(0,r.jsxs)("div",{className:"hazardDetails",children:[(0,r.jsx)(h.A,{routes:e.routes}),k||U.title?(0,r.jsxs)(r.Fragment,{children:[U&&U.picture?(0,r.jsxs)(r.Fragment,{children:[(a=U,t=R,(0,r.jsx)(j.default,{hazardData:a,currentLang:t})),(0,r.jsx)(i.A,{children:(0,r.jsx)(n.A,{className:"mt-2 ps-4",md:{span:6,offset:6},children:(0,r.jsx)("div",{children:(0,r.jsxs)("p",{className:" py-1",style:{fontSize:"12px"},children:[(0,r.jsxs)("i",{children:[F("imageSourceCredit"),": "]}),Y?function(e,a){return e&&a?(0,r.jsx)("a",{target:"_blank",href:a,children:a}):(0,r.jsx)("span",{style:{color:"#234799"},children:a})}(ev,Y):(0,r.jsx)("span",{style:{color:"#234799"},children:F("noSourceFound")})]})})})})]}):(0,r.jsx)("div",{className:"d-flex justify-content-center p-5",children:(0,r.jsx)(c.A,{animation:"grow"})}),(0,r.jsx)("br",{}),(0,r.jsxs)(i.A,{children:[(0,r.jsx)(n.A,{children:(s=F,w=P,(0,r.jsx)(g.default,{t:s,hazardCurrentEventData:w}))}),(0,r.jsx)(n.A,{children:(S=F,_=L,(0,r.jsx)(p.default,{t:S,hazardOperationData:_}))})]}),(0,r.jsx)("br",{}),(0,r.jsxs)(i.A,{children:[(0,r.jsx)(n.A,{children:($=F,C=Z,(0,r.jsx)(f.default,{t:$,hazardInstitutionData:C}))}),(0,r.jsx)(n.A,{children:(D=F,b=H,(0,r.jsx)(N.default,{t:D,hazardPastEventData:b}))})]}),(0,r.jsx)(y.default,{t:F,images:ed,imgSrc:en,routeData:e,documentAccoirdianProps:{loading:X,Document:ea,updateDocument:es,hazardDocSort:e=>{let a={sort:{},limit:"~",Doctable:!0,collation:"en",select:z};a.sort={[e.columnSelector]:e.sortDirection},em(a)},hazardDocUpdateSort:e=>{let a={sort:{},limit:"~",DocUpdatetable:!0,collation:"en",select:z};a.sort={[e.columnSelector]:e.sortDirection},ex(a)},docSrc:ei}})]}):(0,r.jsx)("div",{className:"nodataFound",children:F("vspace.Nodataavailable")})]})};s()}catch(e){s(e)}})},18597:(e,a,t)=>{t.d(a,{A:()=>A});var s=t(3892),r=t.n(s),d=t(82015),l=t(80739),i=t(8732);let n=d.forwardRef(({className:e,bsPrefix:a,as:t="div",...s},d)=>(a=(0,l.oU)(a,"card-body"),(0,i.jsx)(t,{ref:d,className:r()(e,a),...s})));n.displayName="CardBody";let c=d.forwardRef(({className:e,bsPrefix:a,as:t="div",...s},d)=>(a=(0,l.oU)(a,"card-footer"),(0,i.jsx)(t,{ref:d,className:r()(e,a),...s})));c.displayName="CardFooter";var o=t(6417);let u=d.forwardRef(({bsPrefix:e,className:a,as:t="div",...s},n)=>{let c=(0,l.oU)(e,"card-header"),u=(0,d.useMemo)(()=>({cardHeaderBsPrefix:c}),[c]);return(0,i.jsx)(o.A.Provider,{value:u,children:(0,i.jsx)(t,{ref:n,...s,className:r()(a,c)})})});u.displayName="CardHeader";let h=d.forwardRef(({bsPrefix:e,className:a,variant:t,as:s="img",...d},n)=>{let c=(0,l.oU)(e,"card-img");return(0,i.jsx)(s,{ref:n,className:r()(t?`${c}-${t}`:c,a),...d})});h.displayName="CardImg";let m=d.forwardRef(({className:e,bsPrefix:a,as:t="div",...s},d)=>(a=(0,l.oU)(a,"card-img-overlay"),(0,i.jsx)(t,{ref:d,className:r()(e,a),...s})));m.displayName="CardImgOverlay";let x=d.forwardRef(({className:e,bsPrefix:a,as:t="a",...s},d)=>(a=(0,l.oU)(a,"card-link"),(0,i.jsx)(t,{ref:d,className:r()(e,a),...s})));x.displayName="CardLink";var j=t(7783);let p=(0,j.A)("h6"),f=d.forwardRef(({className:e,bsPrefix:a,as:t=p,...s},d)=>(a=(0,l.oU)(a,"card-subtitle"),(0,i.jsx)(t,{ref:d,className:r()(e,a),...s})));f.displayName="CardSubtitle";let g=d.forwardRef(({className:e,bsPrefix:a,as:t="p",...s},d)=>(a=(0,l.oU)(a,"card-text"),(0,i.jsx)(t,{ref:d,className:r()(e,a),...s})));g.displayName="CardText";let N=(0,j.A)("h5"),y=d.forwardRef(({className:e,bsPrefix:a,as:t=N,...s},d)=>(a=(0,l.oU)(a,"card-title"),(0,i.jsx)(t,{ref:d,className:r()(e,a),...s})));y.displayName="CardTitle";let v=d.forwardRef(({bsPrefix:e,className:a,bg:t,text:s,border:d,body:c=!1,children:o,as:u="div",...h},m)=>{let x=(0,l.oU)(e,"card");return(0,i.jsx)(u,{ref:m,...h,className:r()(a,x,t&&`bg-${t}`,s&&`text-${s}`,d&&`border-${d}`),children:c?(0,i.jsx)(n,{children:o}):o})});v.displayName="Card";let A=Object.assign(v,{Img:h,Title:y,Subtitle:f,Body:n,Link:x,Text:g,Header:u,Footer:c,ImgOverlay:m})},25920:(e,a,t)=>{t.r(a),t.d(a,{default:()=>n});var s=t(8732),r=t(18597),d=t(19918),l=t.n(d),i=t(88751);let n=e=>{let a=e.hazardCurrentEventData,{t}=(0,i.useTranslation)("common");return(0,s.jsx)(s.Fragment,{children:(0,s.jsx)("div",{className:"rki-carousel-card",children:(0,s.jsxs)(r.A,{className:"infoCard",children:[(0,s.jsx)(r.A.Header,{className:"text-center",children:t("hazardshow.currentevents")}),(0,s.jsx)(r.A.Body,{className:"hazardBody",children:a&&a.length>0?a.map((e,a)=>(0,s.jsx)("ul",{className:"ulItems",children:(0,s.jsxs)("li",{className:"liItems",children:[(0,s.jsx)(l(),{href:"/event/[...routes]",as:`/event/show/${e._id}`,children:e&&e.title?`${e.title}`:""},e._id),(0,s.jsxs)("span",{children:[" ","(",e&&e.country?`${e.country.title}`:"",")"]})]},a)})):(0,s.jsx)("span",{className:"text-center",children:t("noRecordFound")})})]})})})}},31977:(e,a,t)=>{t.r(a),t.d(a,{default:()=>i});var s=t(8732),r=t(83551),d=t(49481),l=t(63349);let i=e=>{let a=e.hazardData,t=e.currentLang;return(0,s.jsx)(s.Fragment,{children:(0,s.jsxs)(r.A,{children:[(0,s.jsxs)(d.A,{className:"ps-4",children:[(0,s.jsx)("h2",{children:a.title&&a.title[t]?a.title[t]:""}),(0,s.jsx)(l.A,{description:a.description&&a.description[t]?a.description[t]:""})]}),(0,s.jsx)(d.A,{style:{display:"flex"},children:(0,s.jsx)("img",{src:a.picture,style:{width:"100%",height:"400px",backgroundSize:"cover"},alt:"banner"})})]})})}},87824:(e,a,t)=>{t.r(a),t.d(a,{default:()=>n});var s=t(8732),r=t(19918),d=t.n(r),l=t(18597),i=t(88751);let n=e=>{let a=e.hazardOperationData,{t}=(0,i.useTranslation)("common");return(0,s.jsx)(s.Fragment,{children:(0,s.jsx)("div",{className:"rki-carousel-card",children:(0,s.jsxs)(l.A,{className:"infoCard",children:[(0,s.jsx)(l.A.Header,{className:"text-center",children:t("hazardshow.currentoperations")}),(0,s.jsx)(l.A.Body,{className:"hazardBody",children:a&&a.length>0?a.map((e,a)=>(0,s.jsx)("ul",{className:"ulItems",children:(0,s.jsxs)("li",{className:"liItems",children:[(0,s.jsx)(d(),{href:"/operation/[...routes]",as:`/operation/show/${e._id}`,children:e&&e.title?`${e.title}`:""},e._id),(0,s.jsxs)("span",{children:[" ","(",e&&e.country?`${e.country.title}`:"",")"]})]},a)})):(0,s.jsx)("span",{className:"text-center",children:t("noSourceFound")})})]})})})}},89497:(e,a,t)=>{t.r(a),t.d(a,{default:()=>n});var s=t(8732),r=t(18597),d=t(19918),l=t.n(d),i=t(88751);let n=e=>{let a=e.hazardInstitutionData,{t}=(0,i.useTranslation)("common");return(0,s.jsx)(s.Fragment,{children:(0,s.jsx)("div",{className:"rki-carousel-card",children:(0,s.jsxs)(r.A,{className:"infoCard",children:[(0,s.jsx)(r.A.Header,{className:"text-center",children:t("hazardshow.organisations")}),(0,s.jsx)(r.A.Body,{className:"hazardBody",children:a&&a.length>0?a.map((e,a)=>(0,s.jsx)("ul",{className:"ulItems",children:(0,s.jsxs)("li",{className:"liItems",children:[(0,s.jsx)(l(),{href:"/institution/[...routes]",as:`/institution/show/${e._id}`,children:e&&e.title?`${e.title}`:""}),(0,s.jsxs)("span",{children:[" ","(",e&&e.address&&e.address.country?`${e.address.country.title}`:"",")"]})]},a)})):(0,s.jsx)("span",{className:"text-center",children:t("noRecordFound")})})]})})})}},94441:(e,a,t)=>{t.r(a),t.d(a,{default:()=>n});var s=t(8732),r=t(18597),d=t(19918),l=t.n(d),i=t(88751);let n=e=>{let a=e.hazardPastEventData,{t}=(0,i.useTranslation)("common");return(0,s.jsx)(s.Fragment,{children:(0,s.jsx)("div",{className:"rki-carousel-card",children:(0,s.jsxs)(r.A,{className:"infoCard",children:[(0,s.jsx)(r.A.Header,{className:"text-center",children:t("hazardshow.pastevents")}),(0,s.jsx)(r.A.Body,{className:"hazardBody",children:a&&a.length>0?a.map((e,a)=>(0,s.jsx)("ul",{className:"ulItems",children:(0,s.jsxs)("li",{className:"liItems",children:[(0,s.jsx)(l(),{href:"/event/[...routes]",as:`/event/show/${e._id}`,children:e&&e.title?`${e.title}`:""},e._id),(0,s.jsxs)("span",{children:[" ","(",e&&e.country?`${e.country.title}`:"",")"]})]},"index")})):(0,s.jsx)("span",{className:"text-center",children:t("noRecordFound")})})]})})})}}};