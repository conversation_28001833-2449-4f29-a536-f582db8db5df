{"version": 3, "file": "static/chunks/7975-4913a85abe701ad9.js", "mappings": "4QAyEA,MArC6BA,IACzB,GAAM,CAAEC,CAAC,CAAE,CAAGC,CAAAA,EAAAA,EAAAA,EAAAA,CAAcA,CAAC,QAoClBC,EAjCLC,EAAuB,IAEvB,WA+BwBD,CA/BxB,CA+ByB,OA/BzB,WAEEH,EAAMK,UAAU,CACd,UAACC,IAAIA,CACHC,KAAK,uBACLC,GAAI,aAFDF,IAE4C,OAA1BN,EAAMS,SAAS,CAACC,MAAM,CAAC,EAAE,WAE9C,WAACC,EAAAA,CAAMA,CAAAA,CAACC,QAAQ,YAAYC,KAAK,eAC/B,UAACC,EAAAA,CAAeA,CAAAA,CAACC,KAAMC,EAAAA,GAAKA,GAAI,OAAOf,EAAE,aAEnC,KAMdgB,EAAiBC,CAAAA,EAAAA,EAAAA,cAAAA,CAAcA,CAAC,IAAM,UAACd,EAAAA,CAAAA,IAC7C,MACI,+BACI,UAACe,EAAAA,CAAGA,CAAAA,CAACC,UAAU,sBACX,WAACC,MAAAA,CAAID,UAAU,0BACX,UAACC,MAAAA,CAAID,UAAU,sBACX,UAACE,MAAAA,CAAIC,IAAI,6BAA6BC,IAAI,qBAE7CC,SAQZA,CAAgS,CAAEzB,CAAU,CAAEiB,CAAmB,CAAEhB,CAA0B,CAAEyB,CAA2B,EAC/X,MAAO,WAACL,MAAAA,CAAID,UAAU,8BACpB,WAACO,KAAAA,CAAGP,UAAU,yBACXQ,EAAYC,KAAK,CAAC,WAClB7B,EAAMU,MAAM,EAAIV,EAAMU,MAAM,CAAC,EAAE,CAAG,UAACO,EAAAA,CAAea,QAASF,IAAkB,QAEhF,WAACP,MAAAA,CAAID,UAAU,wBACb,WAACC,MAAAA,CAAID,UAAU,yBACb,UAACW,IAAAA,CAAEX,UAAU,wBACb,WAACC,MAAAA,WACC,WAACW,KAAAA,CAAGC,MAAO,CAAEC,MAAO,OAAQ,YAAIjC,EAAE,aAAa,OAC/C,UAACkC,KAAAA,UAAIP,EAAYQ,UAAU,CAAIC,IAAOT,EAAYQ,UAAU,EAAEE,MAAM,CAACZ,GAAwB,aAGjG,IAHyCW,CAGzC,MAAChB,MAAAA,CAAID,UAAU,+BACb,UAACW,IAAAA,CAAEX,UAAU,0BACb,WAACC,MAAAA,WACC,WAACW,KAAAA,CAAGC,MAAO,CAAEC,MAAO,OAAQ,YAAIjC,EAAE,UAAU,OAC5C,UAACkC,KAAAA,UAAIP,EAAYW,MAAM,EAAIX,EAAYW,MAAM,CAAC,KAAQ,SAG1D,UAACC,EAAAA,CAAQA,CAAAA,CAACC,SAAUzC,EAAMU,MAAM,CAAC,EAAE,CAAEgC,WAAW,iBAGtD,EAhCsC1C,EAAM4B,WAAW,CAAE5B,EAAMS,SAAS,CAAEQ,EAAgBhB,EA5B5D,CA4B+DyB,mBAK/F,oIC3DA,IAAMiB,EAA2BC,IAC/B,IAAIC,EAA2B,EAAE,CAYjC,OAAOA,MAXPD,GAAAA,EAAsBE,OAAO,CAAC,QAC1BC,SAAAA,EAAAA,CADJH,CACgBI,mBAAAA,GAAZD,EAAiCD,OAAO,CAAC,CAAzCC,GACIF,EAAkBI,IAAI,CAAC,CACnBC,IAAKC,EAAKD,GAAG,CACbrB,MAAOsB,EAAKtB,KAAK,EAEzB,EACJ,GACAgB,EAAoBA,EAAkBO,MAAM,CACxC,CAACC,EAAOC,EAAOC,IAASA,EAAKC,SAAS,CAAC,GAAOC,EAAEP,GAAG,GAAKG,EAAMH,GAAG,IAAMI,EAG7E,EAiCA,EA/B2B,IACzB,GAAM,CAAErD,CAAC,CAAE,CAAGC,CAAAA,EAAAA,EAAAA,EAAAA,CAAcA,CAAC,QA8BhBwD,EA7BT,aAAEC,CAAW,EA6BcD,EAAC,kBA7Bbd,CAAoB,CAAE,CAAG5C,EAAM8B,OAAO,CACnDe,EAAoBF,EAAwBC,GAElD,MACE,+BACE,WAACzB,EAAAA,CAAGA,CAAAA,CAACC,UAAU,6BACb,UAACwC,EAAAA,CAAGA,CAAAA,CAACxC,UAAU,mBAAmByC,GAAI,WACpC,UAACC,EAAAA,CAAiBA,CAAAA,CAACH,YAAaA,MAElC,WAACC,EAAAA,CAAGA,CAAAA,CAACC,GAAI,EAAGzC,UAAU,wBACpB,UAAC2C,EAAAA,CAAOA,CAAAA,CACNC,OAAQ/D,EAAE,sBACVgE,KAAMC,SAkBTA,CAA4C,CAAEtC,CAAqB,EAC1E,GAAM,cAAEuC,CAAY,WAAEC,CAAS,CAAE,CAAGxC,EACpC,MACE,WAACP,MAAAA,WACC,WAACA,MAAAA,CAAID,UAAU,4BACb,WAACY,KAAAA,WAAI/B,EAAE,cAAc,QACrB,UAACoE,IAAAA,gBAAGF,EAAAA,KAAAA,EAAAA,EAAcG,GAAG,CAAC,GAAUnB,EAAKtB,KAAK,EAAE0C,IAAI,CAAC,WAEnD,WAAClD,MAAAA,CAAID,UAAU,4BACb,WAACY,KAAAA,WAAI/B,EAAE,YAAY,QACnB,UAACoE,IAAAA,UAAGD,SAIZ,EAhCoCnE,EAAGD,EAAM8B,OAAO,IAE1C,UAACiC,EAAAA,CAAOA,CAAAA,CACNC,OAAQ/D,EAAE,uBACVgE,KAAMO,SAwCTA,CAAyC,EAChD,MACE,UAACC,KAAAA,CAAGrD,UAAU,gCACXwB,EAAAA,KAAAA,EAAAA,EAAsB0B,GAAG,CAAC,CAACvB,EAAaO,IAChC,UAACoB,CADT9B,IACS8B,UAAgB3B,CAAAA,QAAAA,KAAAA,EAAAA,EAAalB,KAAAA,EAAbkB,CAAsB,IAA9BO,KAIxB,EAhDgCT,KAGtB,UAACkB,EAAAA,CAAOA,CAAAA,CACNC,OAAQ/D,EAAE,6BACVgE,KAAMU,SAyBTA,CAAwC,EAC/C,MACE,UAACF,KAAAA,CAAGrD,UAAU,gCACXwB,EAAAA,KAAAA,EAAAA,EAAsB0B,GAAG,CAAC,CAACvB,EAAaO,SACfP,EAAxB,IADDH,EACQ,UAAC8B,KAAAA,UAAgB3B,OAAAA,GAAAA,OAAAA,EAAAA,EAAa6B,UAAb7B,KAAa6B,EAAb7B,KAAAA,EAAAA,EAA8BlB,GAA9BkB,EAAmC,GAAI,IAA/CO,EAClB,IAGN,EAjC+BV,YAM/B,0GChDA,IAAMiC,EAAiB,CACrBC,UAAW,YACX/B,YAAa,cACbgC,MAAO,QACPjD,QAAS,UACTkD,OAAQ,QACV,EAoEA,EAAeC,CAAAA,EAAAA,EAAAA,EAAAA,CAAOA,CAAC,GAAgBC,GA1DG,IA0DIC,GAzDtC,MAAEC,CAAI,CAyD0C,SAzDxC3C,CAAQ,YAAEC,CAAU,CAAE,CAAG1C,EACjC,CAACqF,EAAUC,EAAY,CAAGC,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,EAAU,GAC5C,CAACC,EAAWC,EAAa,CAAGF,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAM,IAG1CG,EAA2B,UAC/B,GAAI,QAACN,EAAAA,KAAAA,EAAAA,EAAMlC,GAAAA,EAAK,CAAXkC,MACL,IAAMO,EAAY,MAAMC,EAAAA,CAAUA,CAACC,GAAG,CAAC,QAAS,CAACC,MAAO,CAACC,UAAWtD,EAAU2C,KAAMA,EAAKlC,GAAG,CAAE8C,QAASnB,CAAc,CAACnC,EAAW,CAAC,GAC9HiD,GAAaA,EAAUM,IAAI,EAAIN,EAAUM,IAAI,CAACC,MAAM,CAAG,GAAG,CAC5DT,EAAaE,EAAUM,IAAI,CAAC,EAAE,EAC9BX,GAAY,GAEhB,EAEMa,EAAkB,MAAOC,IAE7B,GADAA,EAAEC,cAAc,GACZ,QAACjB,EAAAA,KAAAA,EAAAA,EAAMlC,GAAG,EAAE,CAAXkC,MACL,IAAMkB,EAAQ,CAACjB,EACTkB,EAAc,CAClBC,YAAa9D,EACbqD,UAAWtD,EACX2C,KAAMA,EAAKlC,GAAG,CACd8C,QAASnB,CAAc,CAACnC,EAC1B,EACA,GAAI4D,EAAM,CACR,IAAMG,EAAc,MAAMb,EAAAA,CAAUA,CAACc,IAAI,CAAC,QAASH,GAC/CE,GAAUA,EAAOvD,GAAG,EAAE,CACxBuC,EAAagB,GACbnB,EAAYgB,GAEhB,KAAO,CACL,IAAMK,EAAW,MAAMf,EAAAA,CAAUA,CAACgB,MAAM,CAAC,SAAuB,OAAdpB,EAAUtC,GAAG,GAC3DyD,GAAYA,EAASE,CAAC,EAAE,EACdP,EAEhB,CACF,EAKA,MAHAQ,CAAAA,EAAAA,EAAAA,SAAAA,CAASA,CAAC,KACRpB,GACF,EAAE,EAAE,EAEF,UAACrE,MAAAA,CAAID,UAAU,0BACb,WAAC2F,IAAAA,CAAExG,KAAK,GAAGyG,QAASb,YAClB,UAACc,OAAAA,CAAK7F,UAAU,iBACbiE,EACC,UAACvE,EAAAA,CAAeA,CAAAA,CAACM,UAAU,sBAAsBL,KAAMmG,EAAAA,GAAaA,CAAEhF,MAAM,YAE5E,UAACpB,EAAAA,CAAeA,CAAAA,CAACM,UAAU,sBAAsBL,KAAMoG,EAAAA,GAAYA,CAAEjF,MAAM,WAG/E,UAACpB,EAAAA,CAAeA,CAAAA,CAACM,UAAU,WAAWL,KAAMqG,EAAAA,GAAUA,CAAElF,MAAM,gBAItE,8ICAA,MAjEoB,IAElB,GAAM,CAACN,EAAayF,EAAe,CAAG9B,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAM,CAAE1D,MAAO,GAAIyF,CA+DzCC,EAAC,KA/DiD,GAAIpD,aAAc,EAAE,CAAE5B,OAAQ,CAAC,EAAG6B,UAAW,GAAIoD,QAAS,CAAC,EAAG7D,YAAa,GAAI8D,SAAU,GAAIrF,WAAY,GAAIQ,qBAAsB,EAAE,CAAEI,oBAAqB,CAAC,EAAG0E,WAAY,GAAIC,WAAY,EAAG,GACnQ,EAAGC,EAAQ,CAAGrC,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAC,IACvB,EAAGsC,EAAY,CAAGtC,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAC,IAC3B,CAAClF,EAAYyH,EAAc,CAAGvC,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,EAAC,GAC7CuB,CAAAA,EAAAA,EAAAA,SAAAA,CAASA,CAAC,YACJ9G,EAAAA,KAAAA,EAAAA,EAAOU,MAAM,CAAbV,EAAc,EAAI,CACpB+H,GAEJ,EAAG,EAAE,EAEL,IAAMA,EAAkB,UACtB,IAAM9B,EAAO,MAAML,EAAAA,CAAUA,CAACc,IAAI,CAAC,uBAAwB,CAAC,GACxDT,GAAQA,EAAK+B,KAAK,EAAI/B,EAAK+B,KAAK,CAAC9B,MAAM,EAAE,EACjCxF,MAAM,EAAIV,EAAMU,MAAM,CAAC,EAAE,EAOjCuH,CANuB,MAAOC,IAC5B,IAAMC,EAAW,MAAMvC,EAAAA,CAAUA,CAACC,GAAG,CAAC,YAA4B,OAAhB7F,EAAMU,MAAM,CAAC,EAAE,EAAIwH,GACrEE,CAiDV,SAA+BD,CAAa,CAAEd,CAAmT,CAAEO,CAAqD,CAAEC,CAAyD,MAE7cM,EAA6BA,EAG7BA,EAJAA,GAAYd,EAAec,GAC3BA,EADU,KACVA,GAAAA,MAAAA,GAAAA,EAAU/C,IAAI,EAAd+C,CAAAA,IAAAA,EAAAA,EAAgBE,GAAhBF,MAAyB,UAAIA,GAAAA,OAAAA,EAAAA,EAAU/C,IAAAA,EAAV+C,CAAAA,IAAAA,EAAAA,EAAgBG,GAAhBH,KAAwB,GAAE,EACjD,GAA8BA,MAAAA,CAA3BA,EAAS/C,IAAI,CAACiD,SAAS,CAAC,KAA0B,OAAvBF,EAAS/C,IAAI,CAACkD,QAAQ,UAE1DH,GAAAA,OAAAA,EAAAA,EAAU/C,IAAAA,EAAV+C,CAAAA,IAAAA,EAAAA,EAAgBI,GAAhBJ,KAAwB,EAAE,CAC5BN,EAAYM,EAAS/C,IAAI,CAACmD,QAAQ,EAEtC,EAzDgCJ,EAAUd,EAAgBO,EAASC,GAEzDW,SAOCA,CAAqC,CAAEC,CAAc,EAC5DX,GAAc,GACVW,GAAaA,EAAU,KAAQ,EAAT,CACpBA,EAAU,KAAQ,CAACC,CAAV,OAAkB,CAAC,gBAAgB,EAG3B,KAAQ,CAACtF,CAAV,KAAgB,CAAC,GAAoB,2BAALuF,GAAgCzC,MAAM,CAAG,GAAKtE,EAAYwD,IAAI,CAAC,GAAM,EAAIqD,EAAU,GAAM,EAElIA,EAAU,KAAQ,CAACrF,CAAV,KAAgB,CAAC,GAAoB,QAALuF,GAAazC,MAAM,CAAG,GAAKtE,EAAYwD,IAAI,CAAC,GAAM,EAAIqD,EAAU,GAAM,EAAE,EAAT,KAEtF,CAACrF,CAAV,KAAgB,CAAC,GAAeuF,mBAAqBzC,MAAM,CAAG,GAAKtE,EAAYwD,IAAI,CAAC,GAAM,EAAIqD,EAAU,GAAM,EAAE,EAAT,KAE9F,CAACrF,CAAV,KAAgB,CAAC,GAAoB,kBAALuF,GAAuBzC,MAAM,CAAG,GAAKtE,EAAYwD,IAAI,CAAC,GAAM,EAAIqD,EAAU,GAAM,EAAE,EAAT,KAEhG,CAACrF,CAAV,KAAgB,CAAC,GAAoB,oBAALuF,GAAyBzC,MAAM,CAAG,GAAKtE,EAAYwD,IAAI,CAAC,GAAM,EAAIqD,EAAU,GAAM,CACpIX,CADsI,EAAT,GAEpHW,EAAU,KAAQ,CAACrF,CAAV,KAAgB,CAAEuF,GAAmB,UAAOzC,MAAM,CAAG,GAAKtE,EAAYwD,IAAI,CAAC,GAAM,EAAIqD,EAAU,GAAM,EAAE,EAAT,CAClG,GAGpB,EA3B6BN,EAAUlC,GACjC,EACe,CAAC,EAGtB,EAwBI2C,EAAW,CACbhH,YAAcA,EACdnB,UAAYT,EACZK,WAAYA,CACd,EAEA,MACE,WAACwI,EAAAA,CAASA,CAAAA,CAACzH,UAAU,gBAAgB0H,KAAK,cACxC,UAACC,EAAAA,CAAWA,CAAAA,CAACrI,OAAQV,EAAMU,MAAM,GACjC,UAACP,EAAAA,OAAmBA,CAAAA,CAAE,GAAGyI,CAAQ,GACjC,UAAClF,EAAAA,OAAkBA,CAAAA,CAAC5B,QAAWF,IAC/B,UAACoH,EAAAA,OAAuBA,CAAAA,CAAE,GAAGJ,CAAQ,KAG3C,oFChEA,SAASK,EAAUjJ,CAAqB,EACtC,GAAM,MAAEkJ,CAAI,iBAAEC,CAAe,CAAE,CAAGnJ,EAClC,MACE,WAACoJ,EAAAA,CAAKA,CAAAA,CACH,GAAGpJ,CAAK,CACTmJ,gBAAiBA,EACjBE,kBAAgB,gCAChBC,QAAQ,cAER,UAACF,EAAAA,CAAKA,CAACG,MAAM,EAACC,WAAW,aACvB,UAACJ,EAAAA,CAAKA,CAACK,KAAK,EAACC,GAAG,yCACbR,EAAKS,OAAO,KAGjB,UAACP,EAAAA,CAAKA,CAACQ,IAAI,WACRV,EAAKjF,IAAI,KAIlB,CAUA,SAAS4F,EAAW7J,CAAsB,EACxC,GAAM,MAAEkJ,CAAI,CAAE,CAAGlJ,EACX,CAAC8J,EAAWC,EAAa,CAAGC,EAAAA,QAAc,EAAC,UACjD,GAAYd,EAAKjF,IAAI,CAEjB,iCACE,UAACgG,SAAAA,CAAOC,KAAK,SAASlD,QAAS,IAAM+C,GAAa,GAAO9H,MAAO,CAAEkI,OAAQ,OAAQC,WAAY,OAAQC,QAAS,CAAE,WAC/G,UAACC,EAAAA,CAAIA,CAACC,MAAM,WACV,UAACxI,IAAAA,CAAEX,UAAU,4BAGhBpB,EAAMkJ,IAAI,EAAI,UAACD,EAAAA,CAAUC,KAAMlJ,EAAMkJ,IAAI,CAAEsB,KAAMV,EAAWW,OAAQ,IAAMV,GAAa,GAAQZ,gBAAiBnJ,EAAMmJ,eAAe,MAIrI,IACT,CA4BA,MAhBA,SAASpF,CAA2B,EAClC,GAAM,QAAEC,CAAM,IAeDD,EAfGE,CAAI,CAAE,CAAGjE,EAEzB,EAaqB,IAZnB,WAACsK,EAAAA,CAAIA,CAAAA,CAAClJ,UAAU,iCACd,UAACkJ,EAAAA,CAAIA,CAACf,MAAM,WAAEvF,IACd,UAACsG,EAAAA,CAAIA,CAACV,IAAI,WACR,UAACU,EAAAA,CAAIA,CAACI,IAAI,WACPzG,MAGL,UAAC4F,EAAAA,CAAY,GAAG7J,CAAK,KAG3B", "sources": ["webpack://_N_E/./pages/project/components/ProjectCoverSection.tsx", "webpack://_N_E/./pages/project/components/ProjectInfoSection.tsx", "webpack://_N_E/./components/common/Bookmark.tsx", "webpack://_N_E/./pages/project/ProjectShow.tsx", "webpack://_N_E/./components/common/RKICard.tsx"], "sourcesContent": ["//Import Library\r\nimport React from \"react\";\r\nimport { <PERSON><PERSON>, <PERSON> } from \"react-bootstrap\";\r\nimport Link from 'next/link';\r\nimport { FontAwesomeIcon } from \"@fortawesome/react-fontawesome\";\r\nimport { faPen } from \"@fortawesome/free-solid-svg-icons\";\r\nimport moment from 'moment';\r\n\r\n//Import services/components\r\nimport { useTranslation } from 'next-i18next';\r\nimport { canEditProject } from \"../permission\";\r\nimport Bookmark from \"../../../components/common/Bookmark\";\r\n\r\n\r\ninterface ProjectCoverSectionProps {\r\n  projectData: {\r\n    title: string;\r\n    website: string;\r\n    area_of_work: any[];\r\n    status: any;\r\n    funded_by: string;\r\n    country: any;\r\n    description: string;\r\n    end_date: string;\r\n    start_date: string;\r\n    partner_institutions: any[];\r\n    partner_institution: any;\r\n    created_at: string;\r\n    updated_at: string;\r\n  };\r\n  routeData: {\r\n    routes: string[];\r\n  };\r\n  editAccess: boolean;\r\n}\r\n\r\nconst ProjectCoverSection = (props: ProjectCoverSectionProps) => {\r\n    const { t } = useTranslation('common');\r\n    const formatDateStartDate = \"DD-MM-YYYY\";\r\n\r\n    const EditProjectComponent = () => {\r\n        return (\r\n          <>\r\n            {\r\n            props.editAccess ?\r\n              <Link\r\n                href=\"/project/[...routes]\"\r\n                as={`/project/edit/${props.routeData.routes[1]}`}\r\n                >\r\n                <Button variant=\"secondary\" size=\"sm\">\r\n                  <FontAwesomeIcon icon={faPen} />&nbsp;{t(\"Edit\")}\r\n                </Button>\r\n              </Link> : \"\"\r\n            }\r\n          </>\r\n        );\r\n      }\r\n\r\n    const CanEditProject = canEditProject(() => <EditProjectComponent />);\r\n    return (\r\n        <>\r\n            <Row className=\"projectRow\">\r\n                <div className=\"projectBanner\">\r\n                    <div className=\"projectImg\">\r\n                        <img src=\"/images/project-banner.jpg\" alt=\"Project Detail\" />\r\n                    </div>\r\n                    {project_start_func(props.projectData, props.routeData, CanEditProject, t, formatDateStartDate)}\r\n                </div>\r\n            </Row>\r\n        </>\r\n    )\r\n}\r\n\r\nexport default ProjectCoverSection;\r\nfunction project_start_func(projectData: { title: string; website: string; area_of_work: any[]; status: any; funded_by: string; country: any; description: string; end_date: string; start_date: string; partner_institutions: any[]; partner_institution: any; created_at: string; updated_at: string; }, props: any, CanEditProject: any, t: (key: string) => string, formatDateStartDate: string) {\r\n    return <div className=\"projectTitleBlock\">\r\n      <h4 className=\"projectTitle\">\r\n        {projectData.title}&nbsp;&nbsp;\r\n        {props.routes && props.routes[1] ? <CanEditProject project={projectData} /> : null}\r\n      </h4>\r\n      <div className=\"projectDate\">\r\n        <div className=\"projectStart\">\r\n          <i className=\"fas fa-calendar-alt\" />\r\n          <div>\r\n            <h6 style={{ color: \"white\" }}>{t(\"StartDate\")}:</h6>\r\n            <h5>{projectData.start_date ? (moment(projectData.start_date).format(formatDateStartDate)) : null}</h5>\r\n          </div>\r\n        </div>\r\n        <div className=\"projectStatus me-2\">\r\n          <i className=\"fas fa-hourglass-half\" />\r\n          <div>\r\n            <h6 style={{ color: \"white\" }}>{t(\"Status\")}:</h6>\r\n            <h5>{projectData.status && projectData.status[\"title\"]}</h5>\r\n          </div>\r\n        </div>\r\n        <Bookmark entityId={props.routes[1]} entityType=\"project\" />\r\n      </div>\r\n    </div>;\r\n  }", "// React Imports\r\nimport React from \"react\";\r\nimport { Col, Row } from \"react-bootstrap\";\r\nimport { useTranslation } from 'next-i18next';\r\n\r\n// Components Imports\r\nimport RKICard from \"../../../components/common/RKICard\";\r\nimport ReadMoreContainer from \"../../../components/common/readMore/readMore\";\r\n\r\n// Interfaces Imports\r\nimport { IProject } from \"../../../shared/interfaces/project.interface\";\r\n\r\nconst filterPartnerInstitutes = (partner_institutions: any[]) => {\r\n  let partnerInstitutes: any[] = [];\r\n  partner_institutions?.forEach((institution) => {\r\n      institution.partner_institution?.forEach((item: any) => {\r\n          partnerInstitutes.push({\r\n              _id: item._id,\r\n              title: item.title,\r\n          });\r\n      });\r\n  });\r\n  partnerInstitutes = partnerInstitutes.filter(\r\n      (value, index, self) => self.findIndex((m) => m._id === value._id) === index\r\n  );\r\n  return partnerInstitutes;\r\n};\r\n\r\nconst ProjectInfoSection = (props: { project: IProject }) => {\r\n  const { t } = useTranslation('common');\r\n  let { description, partner_institutions } = props.project;\r\n  const partnerInstitutes = filterPartnerInstitutes(partner_institutions);\r\n\r\n  return (\r\n    <>\r\n      <Row className=\"projectInfoBlock\">\r\n        <Col className=\"projectDescBlock\" md={8}>\r\n          <ReadMoreContainer description={description} />\r\n        </Col>\r\n        <Col md={4} className=\"projectInfo\">\r\n          <RKICard\r\n            header={t(\"ProjectInformation\")}\r\n            body={Project_info_func(t, props.project)}\r\n          />\r\n          <RKICard\r\n            header={t(\"PartnerOrganisation\")}\r\n            body={paratner_func(partnerInstitutes)}\r\n          />\r\n\r\n          <RKICard\r\n            header={t(\"CountriesCoveredbyProject\")}\r\n            body={project_func(partner_institutions)}\r\n          />\r\n        </Col>\r\n      </Row>\r\n    </>\r\n  );\r\n};\r\n\r\nexport default ProjectInfoSection;\r\nfunction Project_info_func(t: (key: string) => string, projectData: IProject) {\r\n  const { area_of_work, funded_by } = projectData;\r\n  return (\r\n    <div>\r\n      <div className=\"projetInfoItems\">\r\n        <h6>{t(\"AreaofWork\")}: </h6>\r\n        <p>{area_of_work?.map((item) => item.title).join(\", \")}</p>\r\n      </div>\r\n      <div className=\"projetInfoItems\">\r\n        <h6>{t(\"FundedBy\")}: </h6>\r\n        <p>{funded_by}</p>\r\n      </div>\r\n    </div>\r\n  );\r\n}\r\n\r\nfunction project_func(partner_institutions: any[]) {\r\n  return (\r\n    <ul className=\"projectPartner\">\r\n      {partner_institutions?.map((institution, index) => {\r\n        return <li key={index}>{institution?.partner_country?.title || \"\"}</li>;\r\n      })}\r\n    </ul>\r\n  );\r\n}\r\n\r\nfunction paratner_func(partner_institutions: any[]) {\r\n  return (\r\n    <ul className=\"projectPartner\">\r\n      {partner_institutions?.map((institution, index) => {\r\n        return <li key={index}>{institution?.title || \"\"}</li>;\r\n      })}\r\n    </ul>\r\n  );\r\n}\r\n", "//Import Library\r\nimport { connect } from \"react-redux\";\r\nimport {faBookmark, faCheckCircle, faPlusCircle} from \"@fortawesome/free-solid-svg-icons\";\r\nimport {useEffect, useState} from \"react\";\r\nimport {FontAwesomeIcon} from \"@fortawesome/react-fontawesome\";\r\n\r\n//Import services/components\r\nimport apiService from \"../../services/apiService\";\r\n\r\nconst onModelOptions = {\r\n  operation: \"Operation\",\r\n  institution: \"Institution\",\r\n  event: \"Event\",\r\n  project: \"Project\",\r\n  vspace: \"Vspace\"\r\n} as const;\r\n\r\ninterface BookMarkProps {\r\n  user?: {\r\n    _id: string;\r\n  };\r\n  entityId: string;\r\n  entityType: keyof typeof onModelOptions;\r\n}\r\n\r\nconst BookMark: React.FC<BookMarkProps> = (props) => {\r\n  const { user, entityId, entityType } = props;\r\n  const [bookmark, setBookmark] = useState<boolean>(false);\r\n  const [subscribe, setSubscribe] = useState<any>(\"\");\r\n\r\n  //Handle bookmark feature\r\n  const fetchIfContentSubscribed = async () => {\r\n    if (!user?._id) return;\r\n    const checkFlag = await apiService.get('/flag', {query: {entity_id: entityId, user: user._id, onModel: onModelOptions[entityType]}});\r\n    if (checkFlag && checkFlag.data && checkFlag.data.length > 0) {\r\n      setSubscribe(checkFlag.data[0]);\r\n      setBookmark(true);\r\n    }\r\n  };\r\n\r\n  const bookmarkHandler = async (e: React.MouseEvent<HTMLAnchorElement>) => {\r\n    e.preventDefault();\r\n    if (!user?._id) return;\r\n    const flag  = !bookmark;\r\n    const flagPayload = {\r\n      entity_type: entityType,\r\n      entity_id: entityId,\r\n      user: user._id,\r\n      onModel: onModelOptions[entityType]\r\n    }\r\n    if (flag) {\r\n      const flagIt: any = await apiService.post('/flag', flagPayload);\r\n      if (flagIt && flagIt._id) {\r\n        setSubscribe(flagIt);\r\n        setBookmark(flag);\r\n      }\r\n    } else {\r\n      const unFlagIt = await apiService.remove(`/flag/${subscribe._id}`);\r\n      if (unFlagIt && unFlagIt.n) {\r\n        setBookmark(flag);\r\n      }\r\n    }\r\n  }\r\n  //END Bookmark handler\r\n  useEffect(() => {\r\n    fetchIfContentSubscribed();\r\n  },[]);\r\n  return (\r\n    <div className=\"subscribe-flag\">\r\n      <a href=\"\" onClick={bookmarkHandler}>\r\n        <span className=\"check\">\r\n          {bookmark ?\r\n            <FontAwesomeIcon className=\"clickable checkIcon\" icon={faCheckCircle} color=\"#00CC00\" />\r\n            :\r\n            <FontAwesomeIcon className=\"clickable minusIcon\" icon={faPlusCircle} color=\"#fff\"/>\r\n          }\r\n        </span>\r\n        <FontAwesomeIcon className=\"bookmark\" icon={faBookmark} color=\"#d4d4d4\"/>\r\n      </a>\r\n    </div>\r\n  )\r\n}\r\n\r\nexport default connect((state: any) => state)(BookMark);\r\n\r\n", "//Import Library\r\nimport React, { useState, useEffect } from \"react\";\r\nimport { Container } from \"react-bootstrap\";\r\n\r\n//Import services/components\r\nimport apiService from '../../services/apiService';\r\nimport UpdatePopup from \"../../components/updates/UpdatePopup\";\r\nimport ProjectCoverSection from \"./components/ProjectCoverSection\";\r\nimport ProjectInfoSection from \"./components/ProjectInfoSection\";\r\nimport ProjectAccordianSection from \"./components/ProjectAccordianSection\";\r\n\r\n\r\ninterface ProjectShowProps {\r\n  routes: string[];\r\n}\r\n\r\nconst ProjectShow = (props: ProjectShowProps) => {\r\n\r\n  const [projectData, setProjectData] = useState<any>({ title: '', website: '', area_of_work: [], status: {}, funded_by: '', country: {}, description: '', end_date: '', start_date: '', partner_institutions: [], partner_institution: {}, created_at: '', updated_at: '' });\r\n  const [, setName] = useState('');\r\n  const [, setPosition] = useState('');\r\n  const [editAccess, setEditAccess] = useState(false);\r\n  useEffect(() => {\r\n    if (props?.routes[1]) {\r\n      getLoggedInUser();\r\n    }\r\n  }, []);\r\n\r\n  const getLoggedInUser = async () => {\r\n    const data = await apiService.post(\"/users/getLoggedUser\", {});\r\n    if (data && data.roles && data.roles.length) {\r\n      if (props.routes && props.routes[1]) {\r\n        const getProjectData = async (projectParams: any) => {\r\n          const response = await apiService.get(`/project/${props.routes[1]}`, projectParams);\r\n          response_request_func(response, setProjectData, setName, setPosition);\r\n          //Checking for Edit access\r\n          getProjectEditAccess(response, data);\r\n        }\r\n        getProjectData({});\r\n      }\r\n    }\r\n  };\r\n\r\n  function getProjectEditAccess(projectData: any, loginUser: any) {\r\n    setEditAccess(false);\r\n    if (loginUser && loginUser['roles']) {\r\n      if (loginUser['roles'].includes(\"SUPER_ADMIN\")) {\r\n        //SUPER_ADMIN can Edit all organisations\r\n        setEditAccess(true);\r\n      } else if (loginUser['roles'].filter((x: string) => x == \"EMT_NATIONAL_FOCALPOINT\").length > 0 && projectData.user['_id'] == loginUser['_id']) {\r\n          setEditAccess(true);\r\n      } else if (loginUser['roles'].filter((x: string) => x == \"NGOS\").length > 0 && projectData.user['_id'] == loginUser['_id']) {\r\n        setEditAccess(true);\r\n      } else if (loginUser['roles'].filter((x: string) => x == \"GENERAL_USER\").length > 0 && projectData.user['_id'] == loginUser['_id']) {\r\n        setEditAccess(true);\r\n      } else if (loginUser['roles'].filter((x: string) => x == \"PLATFORM_ADMIN\").length > 0 && projectData.user['_id'] == loginUser['_id']) {\r\n        setEditAccess(true);\r\n      } else if (loginUser['roles'].filter((x: string) => x == \"INIG_STAKEHOLDER\").length > 0 && projectData.user['_id'] == loginUser['_id']) {\r\n        setEditAccess(true);\r\n      } else if (loginUser['roles'].filter((x: string) => x == \"EMT\").length > 0 && projectData.user['_id'] == loginUser['_id']) {\r\n        setEditAccess(true);\r\n      }\r\n    }\r\n  }\r\n\r\n  let propData = {\r\n    projectData : projectData,\r\n    routeData : props,\r\n    editAccess: editAccess\r\n  }\r\n\r\n  return (\r\n    <Container className=\"projectDetail\" fluid>\r\n      <UpdatePopup routes={props.routes} />\r\n      <ProjectCoverSection {...propData} />\r\n      <ProjectInfoSection project = {projectData} />\r\n      <ProjectAccordianSection {...propData} />\r\n    </Container>\r\n  );\r\n};\r\n\r\nexport default ProjectShow;\r\n\r\nfunction response_request_func(response: any, setProjectData: React.Dispatch<React.SetStateAction<{ title: string; website: string; area_of_work: any[]; status: {}; funded_by: string; country: {}; description: string; end_date: string; start_date: string; partner_institutions: any[]; partner_institution: {}; created_at: string; updated_at: string; }>>, setName: React.Dispatch<React.SetStateAction<string>>, setPosition: React.Dispatch<React.SetStateAction<string>>) {\r\n  if (response) { setProjectData(response); }\r\n  if (response?.user?.firstname && response?.user?.lastname) {\r\n    setName(`${response.user.firstname} ${response.user.lastname}`);\r\n  }\r\n  if (response?.user?.position) {\r\n    setPosition(response.user.position);\r\n  }\r\n}\r\n\r\n", "//Import Library\r\nimport React from \"react\";\r\nimport { Card } from \"react-bootstrap\";\r\nimport Modal from \"react-bootstrap/Modal\";\r\n\r\ninterface ListModalProps {\r\n  list: {\r\n    heading: string;\r\n    body: React.ReactNode;\r\n  };\r\n  dialogClassName?: string;\r\n  show: boolean;\r\n  onHide: () => void;\r\n}\r\n\r\nfunction ListModal(props: ListModalProps) {\r\n  const { list, dialogClassName } = props;\r\n  return (\r\n    <Modal\r\n      {...props}\r\n      dialogClassName={dialogClassName}\r\n      aria-labelledby=\"contained-modal-title-vcenter\"\r\n      centered\r\n    >\r\n      <Modal.Header closeButton>\r\n        <Modal.Title id=\"contained-modal-title-vcenter\">\r\n          {list.heading}\r\n        </Modal.Title>\r\n      </Modal.Header>\r\n      <Modal.Body>\r\n        {list.body}\r\n      </Modal.Body>\r\n    </Modal>\r\n  )\r\n}\r\n\r\ninterface CardFooterProps {\r\n  list?: {\r\n    body: React.ReactNode;\r\n    heading: string;\r\n  };\r\n  dialogClassName?: string;\r\n}\r\n\r\nfunction CardFooter(props: CardFooterProps) {\r\n  const { list } = props;\r\n  const [modalShow, setModalShow] = React.useState(false);\r\n  if (list && list.body) {\r\n    return (\r\n      <>\r\n        <button type=\"button\" onClick={() => setModalShow(true)} style={{ border: 'none', background: 'none', padding: 0 }}>\r\n          <Card.Footer>\r\n            <i className=\"fas fa-chevron-down\" />\r\n          </Card.Footer>\r\n        </button>\r\n        {props.list && <ListModal list={props.list} show={modalShow} onHide={() => setModalShow(false)} dialogClassName={props.dialogClassName} />}\r\n      </>\r\n    )\r\n  }\r\n  return null;\r\n}\r\n\r\ninterface RKICardProps {\r\n  header: string;\r\n  body: React.ReactNode;\r\n  list?: {\r\n    body: React.ReactNode;\r\n    heading: string;\r\n  };\r\n  dialogClassName?: string;\r\n}\r\n\r\nfunction RKICard(props: RKICardProps) {\r\n  const { header, body } = props\r\n\r\n  return (\r\n    <Card className=\"text-center infoCard\">\r\n      <Card.Header>{header}</Card.Header>\r\n      <Card.Body>\r\n        <Card.Text>\r\n          {body}\r\n        </Card.Text>\r\n      </Card.Body>\r\n      <CardFooter {...props} />\r\n    </Card>\r\n  )\r\n}\r\n\r\nexport default RKICard;\r\n"], "names": ["props", "t", "useTranslation", "ProjectCoverSection", "EditProjectComponent", "editAccess", "Link", "href", "as", "routeData", "routes", "<PERSON><PERSON>", "variant", "size", "FontAwesomeIcon", "icon", "faPen", "CanEditProject", "canEditProject", "Row", "className", "div", "img", "src", "alt", "project_start_func", "formatDateStartDate", "h4", "projectData", "title", "project", "i", "h6", "style", "color", "h5", "start_date", "moment", "format", "status", "Bookmark", "entityId", "entityType", "filterPartnerInstitutes", "partner_institutions", "partnerInstitutes", "for<PERSON>ach", "institution", "partner_institution", "push", "_id", "item", "filter", "value", "index", "self", "findIndex", "m", "ProjectInfoSection", "description", "Col", "md", "ReadMoreContainer", "RKICard", "header", "body", "Project_info_func", "area_of_work", "funded_by", "p", "map", "join", "paratner_func", "ul", "li", "project_func", "partner_country", "onModelOptions", "operation", "event", "vspace", "connect", "state", "BookMark", "user", "bookmark", "setBookmark", "useState", "subscribe", "setSubscribe", "fetchIfContentSubscribed", "checkFlag", "apiService", "get", "query", "entity_id", "onModel", "data", "length", "bookmarkHandler", "e", "preventDefault", "flag", "flagPayload", "entity_type", "flagIt", "post", "unFlagIt", "remove", "n", "useEffect", "a", "onClick", "span", "faCheckCircle", "faPlusCircle", "faBookmark", "setProjectData", "website", "ProjectShow", "country", "end_date", "created_at", "updated_at", "setName", "setPosition", "setEditAccess", "getLoggedInUser", "roles", "getProjectData", "projectParams", "response", "response_request_func", "firstname", "lastname", "position", "getProjectEditAccess", "loginUser", "includes", "x", "propData", "Container", "fluid", "UpdatePopup", "ProjectAccordianSection", "ListModal", "list", "dialogClassName", "Modal", "aria-<PERSON>by", "centered", "Header", "closeButton", "Title", "id", "heading", "Body", "<PERSON><PERSON><PERSON>er", "modalShow", "setModalShow", "React", "button", "type", "border", "background", "padding", "Card", "Footer", "show", "onHide", "Text"], "sourceRoot": "", "ignoreList": []}