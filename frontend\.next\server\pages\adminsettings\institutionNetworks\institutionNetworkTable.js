"use strict";(()=>{var e={};e.id=9717,e.ids=[636,3220,9717],e.modules={123:e=>{e.exports=require("dom-helpers/removeEventListener")},1332:e=>{e.exports=require("react-custom-scrollbars-2")},1428:e=>{e.exports=import("axios")},1680:e=>{e.exports=require("@restart/ui/utils")},1919:e=>{e.exports=require("react-transition-group/Transition")},3892:e=>{e.exports=require("classnames")},4048:e=>{e.exports=require("react-truncate")},6009:e=>{e.exports=require("dom-helpers/ownerDocument")},6945:(e,r,t)=>{t.a(e,async(e,s)=>{try{t.r(r),t.d(r,{default:()=>q});var a=t(8732),i=t(19918),o=t.n(i),n=t(82015),u=t(81181),l=t(63241),p=t(12403),d=t(91353),c=t(42893),x=t(56084),g=t(63487),h=t(88751),m=e([c,g]);[c,g]=m.then?(await m)():m;let q=e=>{let[r,t]=(0,n.useState)([]),[,s]=(0,n.useState)(!1),[i,m]=(0,n.useState)(0),[q,P]=(0,n.useState)(10),[b,f]=(0,n.useState)(!1),[j,w]=(0,n.useState)({}),A=()=>f(!1),{t:S}=(0,h.useTranslation)("common"),v=(0,a.jsxs)(u.A,{id:"popover-basic",children:[(0,a.jsx)(u.A.Header,{as:"h3",className:"text-center",children:"NETWORKS"}),(0,a.jsx)(u.A.Body,{children:(0,a.jsxs)("div",{className:"m-2",children:[(0,a.jsxs)("p",{children:[(0,a.jsx)("b",{children:"EMLab"})," - European Mobile Lab"]}),(0,a.jsxs)("p",{children:[(0,a.jsx)("b",{children:"EMT"})," - Emergency Medical Teams"]}),(0,a.jsxs)("p",{children:[(0,a.jsx)("b",{children:"GHPP"})," - Global Health Protection Program"]}),(0,a.jsxs)("p",{children:[(0,a.jsx)("b",{children:"GOARN"})," - Global Outbreak Alert & Response Network"]}),(0,a.jsxs)("p",{children:[(0,a.jsx)("b",{children:"IANPHI"})," - International Association of National Public Health Institutes"]}),(0,a.jsxs)("p",{children:[(0,a.jsx)("b",{children:"STAKOB"})," - St\xe4ndiger Arbeitskreis der Kompetenz-und Behandlungszentren"]}),(0,a.jsxs)("p",{children:[(0,a.jsx)("b",{children:"WHOCC"}),"- World Health Organization Collaborating Centres"]})]})})]}),k=[{name:(0,a.jsx)(l.A,{trigger:"click",placement:"right",overlay:v,children:(0,a.jsxs)("span",{children:[S("Title"),"\xa0\xa0\xa0",(0,a.jsx)("i",{className:"fa fa-info-circle",style:{cursor:"pointer"},"aria-hidden":"true"})]})}),selector:"title"},{name:S("action"),selector:"",cell:e=>(0,a.jsxs)("div",{children:[(0,a.jsx)(o(),{href:"/adminsettings/[...routes]",as:`/adminsettings/edit_institution_network/${e._id}`,children:(0,a.jsx)("i",{className:"icon fas fa-edit"})}),"\xa0",(0,a.jsx)("a",{onClick:()=>N(e),children:(0,a.jsx)("i",{className:"icon fas fa-trash-alt"})})]})}],y={sort:{title:"asc"},limit:q,page:1,query:{}};(0,n.useEffect)(()=>{M()},[]);let M=async()=>{s(!0);let e=await g.A.get("/institutionnetwork",y);e&&e.data&&e.data.length>0&&(t(e.data),m(e.totalCount),s(!1))},C=async(e,r)=>{y.limit=e,y.page=r,s(!0);let a=await g.A.get("/institutionnetwork",y);a&&a.data&&a.data.length>0&&(t(a.data),P(e),s(!1))},E=async()=>{try{await g.A.remove(`/institutionnetwork/${j}`),M(),f(!1),c.default.success(S("adminsetting.Organisationnetworks.Table.orgNetworkDeletedSuccessfully"))}catch(e){c.default.error(S("adminsetting.Organisationnetworks.Table.errorDeletingOrgNetwork"))}},N=async e=>{w(e._id),f(!0)};return(0,a.jsxs)("div",{children:[(0,a.jsxs)(p.A,{show:b,onHide:A,children:[(0,a.jsx)(p.A.Header,{closeButton:!0,children:(0,a.jsx)(p.A.Title,{children:S("adminsetting.Organisationnetworks.Delete")})}),(0,a.jsx)(p.A.Body,{children:S("adminsetting.Organisationnetworks.sure")}),(0,a.jsxs)(p.A.Footer,{children:[(0,a.jsx)(d.A,{variant:"secondary",onClick:A,children:S("cancel")}),(0,a.jsx)(d.A,{variant:"primary",onClick:E,children:S("yes")})]})]}),(0,a.jsx)(x.A,{columns:k,data:r,totalRows:i,pagServer:!0,handlePerRowsChange:C,handlePageChange:e=>{y.limit=q,y.page=e,M()}})]})};s()}catch(e){s(e)}})},6952:e=>{e.exports=require("@restart/ui/Modal")},7374:e=>{e.exports=require("@restart/ui/Dropdown")},8732:e=>{e.exports=require("react/jsx-runtime")},9653:e=>{e.exports=require("@restart/ui/Overlay")},11242:e=>{e.exports=require("redux-persist/integration/react")},11688:e=>{e.exports=require("@restart/hooks/useWillUnmount")},12388:e=>{e.exports=require("dom-helpers/addEventListener")},13364:e=>{e.exports=require("redux-saga/effects")},14062:e=>{e.exports=import("react-redux")},14078:e=>{e.exports=import("swr")},14332:e=>{e.exports=require("uncontrollable")},16116:e=>{e.exports=require("invariant")},18622:e=>{e.exports=require("yup")},19121:e=>{e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},22326:e=>{e.exports=require("react-dom")},22541:e=>{e.exports=require("redux-persist/lib/storage")},25303:e=>{e.exports=require("@restart/ui/NavItem")},26324:e=>{e.exports=require("warning")},27825:e=>{e.exports=require("lodash")},27910:e=>{e.exports=require("stream")},28217:e=>{e.exports=require("@restart/ui/DropdownItem")},29021:e=>{e.exports=require("fs")},29294:e=>{e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},29628:e=>{e.exports=require("dom-helpers/addClass")},29825:e=>{e.exports=require("prop-types")},29841:e=>{e.exports=require("dom-helpers/hasClass")},30362:e=>{e.exports=require("@restart/ui/DropdownToggle")},33873:e=>{e.exports=require("path")},36653:e=>{e.exports=require("nprogress")},36955:e=>{e.exports=require("@restart/hooks/useBreakpoint")},37766:e=>{e.exports=require("@restart/hooks/useMergedRefs")},38609:e=>{e.exports=require("react-data-table-component")},39756:e=>{e.exports=import("redux")},40051:e=>{e.exports=require("dom-helpers/removeClass")},40361:e=>{e.exports=require("next/dist/compiled/next-server/pages.runtime.prod.js")},42893:e=>{e.exports=import("react-hot-toast")},43294:e=>{e.exports=require("formik")},50009:e=>{e.exports=require("@restart/ui/DropdownMenu")},56084:(e,r,t)=>{t.d(r,{A:()=>l});var s=t(8732);t(82015);var a=t(38609),i=t.n(a),o=t(88751),n=t(30370);function u(e){let{t:r}=(0,o.useTranslation)("common"),t={rowsPerPageText:r("Rowsperpage")},{columns:a,data:u,totalRows:l,resetPaginationToggle:p,subheader:d,subHeaderComponent:c,handlePerRowsChange:x,handlePageChange:g,rowsPerPage:h,defaultRowsPerPage:m,selectableRows:q,loading:P,pagServer:b,onSelectedRowsChange:f,clearSelectedRows:j,sortServer:w,onSort:A,persistTableHead:S,sortFunction:v,...k}=e,y={paginationComponentOptions:t,noDataComponent:r("NoData"),noHeader:!0,columns:a,data:u||[],dense:!0,paginationResetDefaultPage:p,subHeader:d,progressPending:P,subHeaderComponent:c,pagination:!0,paginationServer:b,paginationPerPage:m||10,paginationRowsPerPageOptions:h||[10,15,20,25,30],paginationTotalRows:l,onChangeRowsPerPage:x,onChangePage:g,selectableRows:q,onSelectedRowsChange:f,clearSelectedRows:j,progressComponent:(0,s.jsx)(n.A,{}),sortIcon:(0,s.jsx)("i",{className:"sort-icon fas fa-exchange-alt"}),sortServer:w,onSort:A,sortFunction:v,persistTableHead:S,className:"rki-table"};return(0,s.jsx)(i(),{...y})}u.defaultProps={subHeader:!1,pagination:!0,totalRows:null,pagServer:!0,onSelectedRowsChange:null,clearSelectedRows:!1,sortServer:!1,persistTableHead:!1};let l=u},57664:e=>{e.exports=require("dom-helpers/scrollbarSize")},58928:e=>{e.exports=require("dom-helpers/canUseDOM")},59717:e=>{e.exports=require("@restart/ui/DropdownContext")},60560:e=>{e.exports=require("dom-helpers/contains")},63033:e=>{e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},65447:e=>{e.exports=require("@restart/ui/Button")},67364:e=>{e.exports=require("@restart/hooks/useCallbackRef")},68455:e=>{e.exports=import("redux-saga")},69722:e=>{e.exports=require("es6-promise")},74075:e=>{e.exports=require("zlib")},74716:e=>{e.exports=require("moment")},74987:e=>{e.exports=require("@restart/ui/ModalManager")},78097:e=>{e.exports=require("next-redux-saga")},78634:e=>{e.exports=require("@restart/ui/Anchor")},80237:(e,r)=>{Object.defineProperty(r,"A",{enumerable:!0,get:function(){return t}});var t=function(e){return e.PAGES="PAGES",e.PAGES_API="PAGES_API",e.APP_PAGE="APP_PAGE",e.APP_ROUTE="APP_ROUTE",e.IMAGE="IMAGE",e}({})},81311:(e,r,t)=>{t.a(e,async(e,s)=>{try{t.r(r),t.d(r,{config:()=>m,default:()=>c,getServerSideProps:()=>h,getStaticPaths:()=>g,getStaticProps:()=>x,reportWebVitals:()=>q,routeModule:()=>A,unstable_getServerProps:()=>j,unstable_getServerSideProps:()=>w,unstable_getStaticParams:()=>f,unstable_getStaticPaths:()=>b,unstable_getStaticProps:()=>P});var a=t(63885),i=t(80237),o=t(81413),n=t(9616),u=t.n(n),l=t(72386),p=t(6945),d=e([l,p]);[l,p]=d.then?(await d)():d;let c=(0,o.M)(p,"default"),x=(0,o.M)(p,"getStaticProps"),g=(0,o.M)(p,"getStaticPaths"),h=(0,o.M)(p,"getServerSideProps"),m=(0,o.M)(p,"config"),q=(0,o.M)(p,"reportWebVitals"),P=(0,o.M)(p,"unstable_getStaticProps"),b=(0,o.M)(p,"unstable_getStaticPaths"),f=(0,o.M)(p,"unstable_getStaticParams"),j=(0,o.M)(p,"unstable_getServerProps"),w=(0,o.M)(p,"unstable_getServerSideProps"),A=new a.PagesRouteModule({definition:{kind:i.A.PAGES,page:"/adminsettings/institutionNetworks/institutionNetworkTable",pathname:"/adminsettings/institutionNetworks/institutionNetworkTable",bundlePath:"",filename:""},components:{App:l.default,Document:u()},userland:p});s()}catch(e){s(e)}})},81366:e=>{e.exports=require("redux-auth-wrapper/connectedAuthWrapper")},81413:(e,r)=>{Object.defineProperty(r,"M",{enumerable:!0,get:function(){return function e(r,t){return t in r?r[t]:"then"in r&&"function"==typeof r.then?r.then(r=>e(r,t)):"function"==typeof r&&"default"===t?r:void 0}}})},81521:e=>{e.exports=require("dom-helpers/querySelectorAll")},81895:e=>{e.exports=require("@restart/hooks/useEventCallback")},82015:e=>{e.exports=require("react")},86842:e=>{e.exports=require("@restart/ui/SelectableContext")},87571:e=>{e.exports=require("dom-helpers/transitionEnd")},88751:e=>{e.exports=require("next-i18next")},93787:e=>{e.exports=require("redux-persist")},94947:e=>{e.exports=require("@restart/hooks/useTimeout")},96196:e=>{e.exports=require("next-redux-wrapper")},98320:e=>{e.exports=require("@restart/hooks/useIsomorphicEffect")},99460:e=>{e.exports=require("dom-helpers/css")}};var r=require("../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[6089,9216,9616,2386],()=>t(81311));module.exports=s})();