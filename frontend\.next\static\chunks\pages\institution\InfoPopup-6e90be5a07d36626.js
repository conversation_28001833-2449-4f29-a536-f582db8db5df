(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[9409],{32684:(e,n,s)=>{(window.__NEXT_P=window.__NEXT_P||[]).push(["/institution/InfoPopup",function(){return s(49244)}])},49244:(e,n,s)=>{"use strict";s.r(n),s.d(n,{default:()=>c});var r=s(37876);s(14232);var t=s(31195),i=s(48230),o=s.n(i),d=s(31753);let c=e=>{let{t:n}=(0,d.Bd)("common"),{isShow:s,isClose:i,data:c,name:l}=e,a="Projects"===l?"project":"Partners"===l?"institution":"operation",h=c.length>0?c.map((e,n)=>(0,r.jsxs)("span",{children:[(0,r.jsx)(o(),{href:"/".concat(a,"/show/").concat(e._id),children:e.title}),(0,r.jsx)("hr",{})]},n)):(0,r.jsxs)("p",{children:[n("No")," ",l," ",n("Found"),"."]});return(0,r.jsxs)(t.A,{centered:!0,size:"sm",show:s,onHide:()=>i(!s),"aria-labelledby":"modal_popup",children:[(0,r.jsx)(t.A.Header,{closeButton:!0,children:(0,r.jsx)(t.A.Title,{children:l})}),(0,r.jsx)(t.A.Body,{children:(0,r.jsx)("div",{children:h})})]})}}},e=>{var n=n=>e(e.s=n);e.O(0,[636,6593,8792],()=>n(32684)),_N_E=e.O()}]);
//# sourceMappingURL=InfoPopup-6e90be5a07d36626.js.map