"use strict";(()=>{var e={};e.id=5221,e.ids=[636,3220,5221],e.modules={123:e=>{e.exports=require("dom-helpers/removeEventListener")},1332:e=>{e.exports=require("react-custom-scrollbars-2")},1428:e=>{e.exports=import("axios")},1680:e=>{e.exports=require("@restart/ui/utils")},1823:(e,t,s)=>{s.a(e,async(e,a)=>{try{s.r(t),s.d(t,{default:()=>g,getServerSideProps:()=>x});var i=s(8732),r=s(7082),n=s(14845),l=s(27053),d=s(88751),o=s(35576),c=s(45927),u=s(14062),m=s(35557),h=e([n,u]);async function x({locale:e}){return{props:{...await (0,o.serverSideTranslations)(e,["common"])}}}[n,u]=h.then?(await h)():h;let g=e=>{let{t}=(0,d.useTranslation)("common"),s=()=>(0,i.jsxs)(r.A,{fluid:!0,className:"p-0",children:[(0,i.jsx)(l.A,{title:t("adminsetting.approval.OrganisationApproval")}),(0,i.jsx)(n.default,{})]}),a=(0,c.canAddOrganisationApproval)(()=>(0,i.jsx)(s,{})),o=(0,u.useSelector)(e=>e);return o?.permissions?.institution?.["create:any"]?(0,i.jsx)(a,{}):(0,i.jsx)(m.default,{})};a()}catch(e){a(e)}})},1919:e=>{e.exports=require("react-transition-group/Transition")},3892:e=>{e.exports=require("classnames")},4048:e=>{e.exports=require("react-truncate")},4439:(e,t,s)=>{s.a(e,async(e,a)=>{try{s.r(t),s.d(t,{default:()=>A});var i=s(8732),r=s(7082),n=s(83551),l=s(49481),d=s(91353),o=s(19918),c=s.n(o),u=s(27053),m=s(83616),h=s(88751),x=s(45927),g=s(14062),p=s(35557),j=e([m,g]);[m,g]=j.then?(await j)():j;let A=e=>{let{t}=(0,h.useTranslation)("common"),s=()=>(0,i.jsx)("div",{children:(0,i.jsxs)(r.A,{style:{overflowX:"hidden"},fluid:!0,className:"p-0",children:[(0,i.jsx)(n.A,{children:(0,i.jsx)(l.A,{xs:12,children:(0,i.jsx)(u.A,{title:t("adminsetting.areaofwork.Forms.Addareaofwork")})})}),(0,i.jsx)(n.A,{children:(0,i.jsx)(l.A,{xs:12,children:(0,i.jsx)(c(),{href:"/adminsettings/[...routes]",as:"/adminsettings/create_area_of_work",children:(0,i.jsx)(d.A,{variant:"secondary",size:"sm",children:t("adminsetting.areaofwork.Forms.Addareaofwork")})})})}),(0,i.jsx)(n.A,{className:"mt-3",children:(0,i.jsx)(l.A,{xs:12,children:(0,i.jsx)(m.default,{})})})]})}),a=(0,x.default)(()=>(0,i.jsx)(s,{})),o=(0,g.useSelector)(e=>e);return o?.permissions?.area_of_work?.["create:any"]?(0,i.jsx)(a,{}):(0,i.jsx)(p.default,{})};a()}catch(e){a(e)}})},5561:(e,t,s)=>{s.a(e,async(e,a)=>{try{s.r(t),s.d(t,{default:()=>g});var i=s(8732),r=s(19918),n=s.n(r),l=s(82015),d=s(12403),o=s(91353),c=s(42893),u=s(56084),m=s(63487),h=s(88751),x=e([c,m]);[c,m]=x.then?(await x)():x;let g=e=>{let[t,s]=(0,l.useState)([]),[,a]=(0,l.useState)(!1),[r,x]=(0,l.useState)(0),[g,p]=(0,l.useState)(10),[j,A]=(0,l.useState)(!1),[y,f]=(0,l.useState)({}),v=()=>A(!1),{t:w}=(0,h.useTranslation)("common"),S=[{name:w("Title"),selector:"title"},{name:w("action"),selector:"",cell:e=>(0,i.jsxs)("div",{children:[(0,i.jsx)(n(),{href:"/adminsettings/[...routes]",as:`/adminsettings/edit_institution_type/${e._id}`,children:(0,i.jsx)("i",{className:"icon fas fa-edit"})}),"\xa0",(0,i.jsx)("a",{onClick:()=>k(e),children:(0,i.jsx)("i",{className:"icon fas fa-trash-alt"})})," "]})}],b={sort:{title:"asc"},limit:g,page:1,query:{}};(0,l.useEffect)(()=>{_()},[]);let _=async()=>{a(!0);let e=await m.A.get("/institutiontype",b);e&&e.data&&e.data.length>0&&(s(e.data),x(e.totalCount),a(!1))},C=async(e,t)=>{b.limit=e,b.page=t,a(!0);let i=await m.A.get("/institutiontype",b);i&&i.data&&i.data.length>0&&(s(i.data),p(e),a(!1))},T=async()=>{try{await m.A.remove(`/institutiontype/${y}`),_(),A(!1),c.default.success(w("adminsetting.Organisationtypes.Table.orgTypeDeletedSuccessfully"))}catch(e){c.default.error(w("adminsetting.Organisationtypes.Table.errorDeletingOrgType"))}},k=async e=>{f(e._id),A(!0)};return(0,i.jsxs)("div",{children:[(0,i.jsxs)(d.A,{show:j,onHide:v,children:[(0,i.jsx)(d.A.Header,{closeButton:!0,children:(0,i.jsx)(d.A.Title,{children:w("adminsetting.Organisationtypes.Delete")})}),(0,i.jsx)(d.A.Body,{children:w("adminsetting.Organisationtypes.sure")}),(0,i.jsxs)(d.A.Footer,{children:[(0,i.jsx)(o.A,{variant:"secondary",onClick:v,children:w("Cancel")}),(0,i.jsx)(o.A,{variant:"primary",onClick:T,children:w("yes")})]})]}),(0,i.jsx)(u.A,{columns:S,data:t,totalRows:r,pagServer:!0,handlePerRowsChange:C,handlePageChange:e=>{b.limit=g,b.page=e,_()}})]})};a()}catch(e){a(e)}})},6009:e=>{e.exports=require("dom-helpers/ownerDocument")},6945:(e,t,s)=>{s.a(e,async(e,a)=>{try{s.r(t),s.d(t,{default:()=>j});var i=s(8732),r=s(19918),n=s.n(r),l=s(82015),d=s(81181),o=s(63241),c=s(12403),u=s(91353),m=s(42893),h=s(56084),x=s(63487),g=s(88751),p=e([m,x]);[m,x]=p.then?(await p)():p;let j=e=>{let[t,s]=(0,l.useState)([]),[,a]=(0,l.useState)(!1),[r,p]=(0,l.useState)(0),[j,A]=(0,l.useState)(10),[y,f]=(0,l.useState)(!1),[v,w]=(0,l.useState)({}),S=()=>f(!1),{t:b}=(0,g.useTranslation)("common"),_=(0,i.jsxs)(d.A,{id:"popover-basic",children:[(0,i.jsx)(d.A.Header,{as:"h3",className:"text-center",children:"NETWORKS"}),(0,i.jsx)(d.A.Body,{children:(0,i.jsxs)("div",{className:"m-2",children:[(0,i.jsxs)("p",{children:[(0,i.jsx)("b",{children:"EMLab"})," - European Mobile Lab"]}),(0,i.jsxs)("p",{children:[(0,i.jsx)("b",{children:"EMT"})," - Emergency Medical Teams"]}),(0,i.jsxs)("p",{children:[(0,i.jsx)("b",{children:"GHPP"})," - Global Health Protection Program"]}),(0,i.jsxs)("p",{children:[(0,i.jsx)("b",{children:"GOARN"})," - Global Outbreak Alert & Response Network"]}),(0,i.jsxs)("p",{children:[(0,i.jsx)("b",{children:"IANPHI"})," - International Association of National Public Health Institutes"]}),(0,i.jsxs)("p",{children:[(0,i.jsx)("b",{children:"STAKOB"})," - St\xe4ndiger Arbeitskreis der Kompetenz-und Behandlungszentren"]}),(0,i.jsxs)("p",{children:[(0,i.jsx)("b",{children:"WHOCC"}),"- World Health Organization Collaborating Centres"]})]})})]}),C=[{name:(0,i.jsx)(o.A,{trigger:"click",placement:"right",overlay:_,children:(0,i.jsxs)("span",{children:[b("Title"),"\xa0\xa0\xa0",(0,i.jsx)("i",{className:"fa fa-info-circle",style:{cursor:"pointer"},"aria-hidden":"true"})]})}),selector:"title"},{name:b("action"),selector:"",cell:e=>(0,i.jsxs)("div",{children:[(0,i.jsx)(n(),{href:"/adminsettings/[...routes]",as:`/adminsettings/edit_institution_network/${e._id}`,children:(0,i.jsx)("i",{className:"icon fas fa-edit"})}),"\xa0",(0,i.jsx)("a",{onClick:()=>P(e),children:(0,i.jsx)("i",{className:"icon fas fa-trash-alt"})})]})}],T={sort:{title:"asc"},limit:j,page:1,query:{}};(0,l.useEffect)(()=>{k()},[]);let k=async()=>{a(!0);let e=await x.A.get("/institutionnetwork",T);e&&e.data&&e.data.length>0&&(s(e.data),p(e.totalCount),a(!1))},N=async(e,t)=>{T.limit=e,T.page=t,a(!0);let i=await x.A.get("/institutionnetwork",T);i&&i.data&&i.data.length>0&&(s(i.data),A(e),a(!1))},R=async()=>{try{await x.A.remove(`/institutionnetwork/${v}`),k(),f(!1),m.default.success(b("adminsetting.Organisationnetworks.Table.orgNetworkDeletedSuccessfully"))}catch(e){m.default.error(b("adminsetting.Organisationnetworks.Table.errorDeletingOrgNetwork"))}},P=async e=>{w(e._id),f(!0)};return(0,i.jsxs)("div",{children:[(0,i.jsxs)(c.A,{show:y,onHide:S,children:[(0,i.jsx)(c.A.Header,{closeButton:!0,children:(0,i.jsx)(c.A.Title,{children:b("adminsetting.Organisationnetworks.Delete")})}),(0,i.jsx)(c.A.Body,{children:b("adminsetting.Organisationnetworks.sure")}),(0,i.jsxs)(c.A.Footer,{children:[(0,i.jsx)(u.A,{variant:"secondary",onClick:S,children:b("cancel")}),(0,i.jsx)(u.A,{variant:"primary",onClick:R,children:b("yes")})]})]}),(0,i.jsx)(h.A,{columns:C,data:t,totalRows:r,pagServer:!0,handlePerRowsChange:N,handlePageChange:e=>{T.limit=j,T.page=e,k()}})]})};a()}catch(e){a(e)}})},6952:e=>{e.exports=require("@restart/ui/Modal")},7374:e=>{e.exports=require("@restart/ui/Dropdown")},8732:e=>{e.exports=require("react/jsx-runtime")},9653:e=>{e.exports=require("@restart/ui/Overlay")},11e3:e=>{e.exports=require("react-multi-select-component")},11242:e=>{e.exports=require("redux-persist/integration/react")},11688:e=>{e.exports=require("@restart/hooks/useWillUnmount")},11768:(e,t,s)=>{s.a(e,async(e,a)=>{try{s.r(t),s.d(t,{default:()=>w});var i=s(8732),r=s(82015),n=s(7082),l=s(18597),d=s(83551),o=s(49481),c=s(59549),u=s(91353),m=s(23579),h=s(66994),x=s(44233),g=s.n(x),p=s(42893),j=s(19918),A=s.n(j),y=s(63487),f=s(88751),v=e([p,y]);[p,y]=v.then?(await v)():v;let w=e=>{let t={title:"",icon:""},{t:s}=(0,f.useTranslation)("common"),[a,x]=(0,r.useState)(t),j=e.routes&&"edit_update_type"===e.routes[0]&&e.routes[1],v=(0,r.useRef)(null),w=e=>{if(e.target){let{name:t,value:s}=e.target;x(e=>({...e,[t]:s}))}},S=async(t,i)=>{let r,n;t.preventDefault();let l={title:a.title.trim(),icon:a.icon};j?(n="adminsetting.updatestype.Updatetypeisupdatedsuccessfully",r=await y.A.patch(`/updatetype/${e.routes[1]}`,l)):(n="adminsetting.updatestype.Updatetypeisaddedsuccessfully",r=await y.A.post("/updatetype",l)),r&&r._id?(p.default.success(s(n)),g().push("/adminsettings/update_type")):r?.errorCode===11e3?p.default.error(s("duplicatesNotAllowed")):p.default.error(r)};return(0,r.useEffect)(()=>{let t={query:{},sort:{title:"asc"},limit:"~"};j&&(async()=>{let s=await y.A.get(`/updatetype/${e.routes[1]}`,t);x(e=>({...e,...s}))})()},[]),(0,i.jsx)("div",{children:(0,i.jsx)(n.A,{className:"formCard",fluid:!0,children:(0,i.jsx)(l.A,{style:{marginTop:"5px",boxShadow:"0 4px 8px 0 rgba(0, 0, 0, 0.2), 0 6px 20px 0 rgba(0, 0, 0, 0.19)"},children:(0,i.jsx)(h.A,{onSubmit:S,ref:v,initialValues:a,enableReinitialize:!0,children:(0,i.jsxs)(l.A.Body,{children:[(0,i.jsx)(d.A,{children:(0,i.jsx)(o.A,{children:(0,i.jsx)(l.A.Title,{children:s("adminsetting.updatestype.UpdateType")})})}),(0,i.jsx)("hr",{}),(0,i.jsxs)(d.A,{children:[(0,i.jsx)(o.A,{md:!0,lg:6,sm:12,children:(0,i.jsxs)(c.A.Group,{children:[(0,i.jsx)(c.A.Label,{className:"required-field",children:s("adminsetting.updatestype.UpdateType")}),(0,i.jsx)(m.ks,{name:"title",id:"title",required:!0,value:a.title,validator:e=>""!==e.trim(),errorMessage:{validator:s("adminsetting.updatestype.PleaseAddtheUpdateType")},onChange:w})]})}),(0,i.jsx)(o.A,{md:!0,lg:6,sm:12,children:(0,i.jsxs)(c.A.Group,{children:[(0,i.jsx)(c.A.Label,{children:s("adminsetting.updatestype.Icon")}),(0,i.jsx)(m.ks,{name:"icon",id:"icon",value:a.icon,errorMessage:s("adminsetting.updatestype.PleaseAddtheicon"),onChange:w})]})})]}),(0,i.jsx)(d.A,{className:"my-4",children:(0,i.jsxs)(o.A,{children:[(0,i.jsx)(u.A,{className:"me-2",type:"submit",variant:"primary",children:s("adminsetting.updatestype.Submit")}),(0,i.jsx)(u.A,{className:"me-2",onClick:()=>{x(t),window.scrollTo(0,0)},variant:"info",children:s("adminsetting.updatestype.Reset")}),(0,i.jsx)(A(),{href:"/adminsettings/[...routes]",as:"/adminsettings/update_type",children:(0,i.jsx)(u.A,{variant:"secondary",children:s("adminsetting.updatestype.Cancel")})})]})})]})})})})})};a()}catch(e){a(e)}})},12388:e=>{e.exports=require("dom-helpers/addEventListener")},13364:e=>{e.exports=require("redux-saga/effects")},14062:e=>{e.exports=import("react-redux")},14078:e=>{e.exports=import("swr")},14332:e=>{e.exports=require("uncontrollable")},14845:(e,t,s)=>{s.a(e,async(e,a)=>{try{s.r(t),s.d(t,{default:()=>h});var i=s(8732),r=s(82015),n=s(91353),l=s(12403),d=s(42893),o=s(63487),c=s(88751),u=s(56084),m=e([d,o]);[d,o]=m.then?(await m)():m;let h=function(e){let[t,s]=(0,r.useState)([]),[,a]=(0,r.useState)(!1),[m,h]=(0,r.useState)(0),[x,g]=(0,r.useState)(10),[p,j]=(0,r.useState)(!1),[A,y]=(0,r.useState)(""),[f,v]=(0,r.useState)({}),{t:w}=(0,c.useTranslation)("common"),S={sort:{created_at:"desc"},limit:x,page:1,query:{status:"Request Pending"}},b=[{name:w("Title"),selector:"title",cell:e=>e.title},{name:w("Email"),selector:"email",cell:e=>e.email},{name:"ContactName",selector:"contact_name",cell:e=>e.contact_name},{name:w("Action"),selector:"",cell:e=>(0,i.jsxs)("div",{children:[(0,i.jsx)(n.A,{variant:"primary",size:"sm",onClick:()=>T(e,"approve"),children:w("instu.Approves")}),"\xa0",(0,i.jsx)(n.A,{variant:"secondary",size:"sm",onClick:()=>T(e,"reject"),children:w("instu.Reject")})]})}],_=async()=>{a(!0);let e=await o.A.get("/institution",S);e&&e.data&&(s(e.data),h(e.totalCount),a(!1))},C=async(e,t)=>{S.limit=e,S.page=t,a(!0);let i=await o.A.get("/institution",S);i&&i.data&&i.data.length>0&&(s(i.data),g(e),a(!1))},T=async(e,t)=>{j(!0),y(t),e&&e._id&&v({...e,status:"approve"===t?"Approved":"Rejected"})},k=async()=>{if("Rejected"===f.status)await o.A.remove(`/institution/${f._id}`),_(),d.default.error(w("instu.Rejected")),v({}),j(!1);else{let e=await o.A.patch(`/institution/${f._id}`,f);if(e&&403===e.status)return void d.default.error(e.response&&e.response.message?e.response.message:w("adminsetting.FocalPointsApprovalTable.Somethingwentswrong"));_(),d.default.success(w("instu.Approve")),v({}),j(!1)}_(),v({}),j(!1)},N=()=>j(!1);return(0,i.jsxs)("div",{children:[(0,i.jsxs)(l.A,{show:p,onHide:N,children:[(0,i.jsx)(l.A.Header,{closeButton:!0,children:(0,i.jsxs)(l.A.Title,{children:[A.charAt(0).toUpperCase()+A.slice(1)," ",w("Organisation")]})}),(0,i.jsxs)(l.A.Body,{children:[w("adminsetting.FocalPointsApprovalTable.Areyousurewantto"),"  ",A," ",w("thisOrganisation?")]}),(0,i.jsxs)(l.A.Footer,{children:[(0,i.jsx)(n.A,{variant:"secondary",onClick:N,children:w("cancel")}),(0,i.jsx)(n.A,{variant:"primary",onClick:k,children:w("yes")})]})]}),(0,i.jsx)(u.A,{columns:b,data:t,totalRows:m,pagServer:!0,handlePerRowsChange:C,handlePageChange:e=>{S.limit=x,S.page=e,_()}})]})};a()}catch(e){a(e)}})},16029:e=>{e.exports=require("react-dropzone")},16116:e=>{e.exports=require("invariant")},17098:(e,t,s)=>{s.a(e,async(e,a)=>{try{s.r(t),s.d(t,{default:()=>f});var i=s(8732),r=s(19918),n=s.n(r),l=s(82015),d=s.n(l),o=s(59549),c=s(12403),u=s(91353),m=s(27825),h=s.n(m),x=s(42893),g=s(56084),p=s(63487),j=s(94544),A=s(88751),y=e([x,p]);[x,p]=y.then?(await y)():y;let f=()=>{let{t:e,i18n:t}=(0,A.useTranslation)("common"),s="fr"===t.language?"en":t.language,a=s?`title.${s}`:"title.en",[r,m]=(0,l.useState)([]),[,y]=(0,l.useState)(!1),[f,v]=(0,l.useState)(0),[w,S]=(0,l.useState)(10),[b,_]=d().useState(""),[C,T]=d().useState(!1),[k,N]=(0,l.useState)(!1),[R,P]=(0,l.useState)({}),[q]=(0,l.useState)(a),E=async t=>{let a=h().findIndex(r,{_id:t.target.name});if(a>-1){r[a].enabled=!r[a].enabled,m([...r]);let i=await p.A.patch(`/hazard/${t.target.name}`,r[a]);i&&i._id?x.default.success(`${i.title[s]} ${e("updatedSuccessfully")}`):x.default.error(i)}else x.default.error(e("indexNotFound"))},z=({_id:e,enabled:t})=>(0,i.jsx)(o.A.Check,{className:"ms-4",type:"switch",name:e,id:e,label:"",checked:t,onChange:e=>E(e)}),D=[{name:e("menu.hazards"),selector:q,cell:e=>e&&e.title&&e.title[s]?e.title[s]:""},{name:e("hazardType"),selector:"hazard_type",cell:e=>e&&e.hazard_type&&e.hazard_type.title?e.hazard_type.title:""},{name:e("published"),selector:"enabled",cell:e=>(0,i.jsx)(z,{...e})},{name:e("action"),selector:"",cell:e=>(0,i.jsxs)("div",{children:[(0,i.jsx)(n(),{href:"/adminsettings/[...routes]",as:`/adminsettings/edit_hazard/${e._id}`,children:(0,i.jsx)("i",{className:"icon fas fa-edit"})}),"\xa0",(0,i.jsx)("span",{onClick:()=>B(e),style:{cursor:"pointer"},children:(0,i.jsx)("i",{className:"icon fas fa-trash-alt"})})]})}];(0,l.useEffect)(()=>{F()},[]);let $={sort:{[q]:"asc"},limit:w,page:1,query:{}},F=async()=>{y(!0);let e=await p.A.get("/hazard",$);e&&e.data&&e.data.length>0&&(m(e.data),v(e.totalCount),y(!1))},H=async(e,t)=>{$.limit=e,$.page=t,y(!0);let s=await p.A.get("/hazard",$);s&&s.data&&s.data.length>0&&(m(s.data),S(e),y(!1))},B=async e=>{P(e._id),N(!0)},O=async()=>{try{await p.A.remove(`/hazard/${R}`),F(),N(!1),x.default.success(e("adminsetting.hazard.Table.hazardDeletedSuccessfully"))}catch(t){x.default.error(e("adminsetting.hazard.Table.errorDeletingHazard"))}},L=()=>N(!1),M=d().useMemo(()=>{let e=e=>{e&&($.query={[q]:e}),F()},t=h().debounce(t=>e(t),Number("500")||300);return(0,i.jsx)(j.default,{onFilter:e=>{_(e.target.value),t(e.target.value)},onClear:()=>{b&&(T(!C),_(""))},filterText:b})},[b]);return(0,i.jsxs)("div",{children:[(0,i.jsxs)(c.A,{show:k,onHide:L,children:[(0,i.jsx)(c.A.Header,{closeButton:!0,children:(0,i.jsx)(c.A.Title,{children:e("deleteHazard")})}),(0,i.jsx)(c.A.Body,{children:e("areYouSureWantToDeleteThisHazard")}),(0,i.jsxs)(c.A.Footer,{children:[(0,i.jsx)(u.A,{variant:"secondary",onClick:L,children:e("cancel")}),(0,i.jsx)(u.A,{variant:"primary",onClick:O,children:e("yes")})]})]}),(0,i.jsx)(g.A,{columns:D,data:r,totalRows:f,pagServer:!0,subheader:!0,resetPaginationToggle:C,subHeaderComponent:M,handlePerRowsChange:H,handlePageChange:e=>{$.limit=w,$.page=e,F()}})]})};a()}catch(e){a(e)}})},17101:(e,t,s)=>{s.a(e,async(e,a)=>{try{s.r(t),s.d(t,{default:()=>A});var i=s(8732),r=s(7082),n=s(83551),l=s(49481),d=s(91353),o=s(19918),c=s.n(o),u=s(27053),m=s(5561),h=s(88751),x=s(45927),g=s(14062),p=s(35557),j=e([m,g]);[m,g]=j.then?(await j)():j;let A=e=>{let{t}=(0,h.useTranslation)("common"),s=()=>(0,i.jsxs)(r.A,{style:{overflowX:"hidden"},fluid:!0,className:"p-0",children:[(0,i.jsx)(n.A,{children:(0,i.jsx)(l.A,{xs:12,children:(0,i.jsx)(u.A,{title:t("adminsetting.Organisationtypes.OrganisationType")})})}),(0,i.jsx)(n.A,{children:(0,i.jsx)(l.A,{xs:12,children:(0,i.jsx)(c(),{href:"/adminsettings/[...routes]",as:"/adminsettings/create_institution_type",children:(0,i.jsx)(d.A,{variant:"secondary",size:"sm",children:t("adminsetting.Organisationtypes.AddOrganisationType")})})})}),(0,i.jsx)(n.A,{className:"mt-3",children:(0,i.jsx)(l.A,{xs:12,children:(0,i.jsx)(m.default,{})})})]}),a=(0,x.canAddOrganisationTypes)(()=>(0,i.jsx)(s,{})),o=(0,g.useSelector)(e=>e);return o?.permissions?.institution_type?.["create:any"]?(0,i.jsx)(a,{}):(0,i.jsx)(p.default,{})};a()}catch(e){a(e)}})},17675:(e,t,s)=>{s.a(e,async(e,a)=>{try{s.r(t),s.d(t,{default:()=>h});var i=s(8732),r=s(7082),n=s(27053),l=s(88751),d=s(47818),o=s(45927),c=s(14062),u=s(35557),m=e([d,c]);[d,c]=m.then?(await m)():m;let h=e=>{let{t}=(0,l.useTranslation)("common"),s=()=>(0,i.jsxs)(r.A,{fluid:!0,className:"p-0",children:[(0,i.jsx)(n.A,{title:t("adminsetting.VirtualspaceApproval")}),(0,i.jsx)(d.default,{})]}),a=(0,o.canAddVspaceApproval)(()=>(0,i.jsx)(s,{})),m=(0,c.useSelector)(e=>e);return m?.permissions?.institution_focal_point?.["create:any"]?(0,i.jsx)(a,{}):(0,i.jsx)(u.default,{})};a()}catch(e){a(e)}})},17906:e=>{e.exports=require("validator")},18622:e=>{e.exports=require("yup")},19121:e=>{e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},19544:(e,t,s)=>{s.a(e,async(e,a)=>{try{s.r(t),s.d(t,{default:()=>g});var i=s(8732),r=s(82015),n=s(19918),l=s.n(n),d=s(12403),o=s(91353),c=s(42893),u=s(56084),m=s(63487),h=s(88751),x=e([c,m]);[c,m]=x.then?(await x)():x;let g=e=>{let[t,s]=(0,r.useState)([]),[,a]=(0,r.useState)(!1),[n,x]=(0,r.useState)(0),[g,p]=(0,r.useState)(10),[j,A]=(0,r.useState)(!1),[y,f]=(0,r.useState)({}),{t:v}=(0,h.useTranslation)("common"),w={sort:{title:"asc"},limit:g,page:1,query:{}},S=[{name:v("Title"),selector:e=>e.title,sortable:!0},{name:v("adminsetting.RiskLevel.Level"),selector:e=>e.level,sortable:!0,cell:e=>e.level},{name:v("action"),selector:e=>e._id,sortable:!1,cell:e=>(0,i.jsxs)("div",{children:[(0,i.jsx)(l(),{href:"/adminsettings/[...routes]",as:`/adminsettings/edit_risklevel/${e._id}`,children:(0,i.jsx)("i",{className:"icon fas fa-edit"})}),"\xa0",(0,i.jsx)("a",{onClick:()=>C(e),children:(0,i.jsx)("i",{className:"icon fas fa-trash-alt"})})," "]})}],b=async()=>{a(!0);let e=await m.A.get("/risklevel",w);e&&e.data&&e.data.length>0&&(s(e.data),x(e.totalCount),a(!1))},_=async(e,t)=>{w.limit=e,w.page=t,a(!0);let i=await m.A.get("/risklevel",w);i&&i.data&&i.data.length>0&&(s(i.data),p(e),a(!1))},C=async e=>{f(e._id),A(!0)},T=async()=>{try{await m.A.remove(`/risklevel/${y}`),b(),A(!1),c.default.success(v("adminsetting.RiskLevel.Table.riskLevelDeletedSuccessfully"))}catch(e){c.default.error(v("adminsetting.RiskLevel.Table.errorDeletingRiskLevel"))}},k=()=>A(!1);return(0,r.useEffect)(()=>{b()},[]),(0,i.jsxs)("div",{children:[(0,i.jsxs)(d.A,{show:j,onHide:k,children:[(0,i.jsx)(d.A.Header,{closeButton:!0,children:(0,i.jsx)(d.A.Title,{children:v("adminsetting.RiskLevel.DeleteRisklevel")})}),(0,i.jsxs)(d.A.Body,{children:[v("adminsetting.RiskLevel.Areyousurewanttodeletethisrisklevel")," "]}),(0,i.jsxs)(d.A.Footer,{children:[(0,i.jsx)(o.A,{variant:"secondary",onClick:k,children:v("Cancel")}),(0,i.jsx)(o.A,{variant:"primary",onClick:T,children:v("yes")})]})]}),(0,i.jsx)(u.A,{columns:S,data:t,totalRows:n,pagServer:!0,handlePerRowsChange:_,handlePageChange:e=>{w.limit=g,w.page=e,b()}})]})};a()}catch(e){a(e)}})},20143:(e,t,s)=>{s.a(e,async(e,a)=>{try{s.r(t),s.d(t,{default:()=>f,getServerSideProps:()=>y});var i=s(8732),r=s(7082),n=s(83551),l=s(49481),d=s(91353),o=s(19918),c=s.n(o),u=s(19544),m=s(88751),h=s(35576),x=s(27053),g=s(45927),p=s(14062),j=s(35557),A=e([u,p]);async function y({locale:e}){return{props:{...await (0,h.serverSideTranslations)(e,["common"])}}}[u,p]=A.then?(await A)():A;let f=e=>{let{t}=(0,m.useTranslation)("common"),s=()=>(0,i.jsx)("div",{children:(0,i.jsxs)(r.A,{style:{overflowX:"hidden"},fluid:!0,className:"p-0",children:[(0,i.jsx)(n.A,{children:(0,i.jsx)(l.A,{xs:12,children:(0,i.jsx)(x.A,{title:t("adminsetting.RiskLevel.Risklevel")})})}),(0,i.jsx)(n.A,{children:(0,i.jsx)(l.A,{xs:12,children:(0,i.jsx)(c(),{href:"/adminsettings/[...routes]",as:"/adminsettings/create_risklevel",children:(0,i.jsx)(d.A,{variant:"secondary",size:"sm",children:t("adminsetting.RiskLevel.AddRisklevel")})})})}),(0,i.jsx)(n.A,{className:"mt-3",children:(0,i.jsx)(l.A,{xs:12,children:(0,i.jsx)(u.default,{})})})]})}),a=(0,g.canAddRiskLevels)(()=>(0,i.jsx)(s,{})),o=(0,p.useSelector)(e=>e);return o?.permissions?.risk_level?.["create:any"]?(0,i.jsx)(a,{}):(0,i.jsx)(j.default,{})};a()}catch(e){a(e)}})},22326:e=>{e.exports=require("react-dom")},22541:e=>{e.exports=require("redux-persist/lib/storage")},24138:(e,t,s)=>{s.a(e,async(e,a)=>{try{s.r(t),s.d(t,{default:()=>f,getServerSideProps:()=>y});var i=s(8732),r=s(7082),n=s(83551),l=s(49481),d=s(91353),o=s(19918),c=s.n(o),u=s(88751),m=s(35576),h=s(34304),x=s(27053),g=s(45927),p=s(14062),j=s(35557),A=e([h,p]);async function y({locale:e}){return{props:{...await (0,m.serverSideTranslations)(e,["common"])}}}[h,p]=A.then?(await A)():A;let f=e=>{let{t}=(0,u.useTranslation)("common"),s=()=>(0,i.jsxs)(r.A,{style:{overflowX:"hidden"},fluid:!0,className:"p-0",children:[(0,i.jsx)(n.A,{children:(0,i.jsx)(l.A,{xs:12,children:(0,i.jsx)(x.A,{title:t("adminsetting.Countries.Table.Countries")})})}),(0,i.jsx)(n.A,{children:(0,i.jsx)(l.A,{xs:12,children:(0,i.jsx)(c(),{href:"/adminsettings/[...routes]",as:"/adminsettings/create_country",children:(0,i.jsx)(d.A,{variant:"secondary",size:"sm",children:t("adminsetting.Countries.Table.AddCountry")})})})}),(0,i.jsx)(n.A,{className:"mt-3",children:(0,i.jsx)(l.A,{xs:12,children:(0,i.jsx)(h.default,{})})})]}),a=(0,g.canAddCountry)(()=>(0,i.jsx)(s,{})),o=(0,p.useSelector)(e=>e);return o?.permissions?.country?.["create:any"]?(0,i.jsx)(a,{}):(0,i.jsx)(j.default,{})};a()}catch(e){a(e)}})},24296:(e,t,s)=>{s.a(e,async(e,a)=>{try{s.r(t),s.d(t,{default:()=>w});var i=s(8732),r=s(82015),n=s(7082),l=s(18597),d=s(83551),o=s(49481),c=s(59549),u=s(91353),m=s(23579),h=s(66994),x=s(44233),g=s.n(x),p=s(42893),j=s(19918),A=s.n(j),y=s(63487),f=s(88751),v=e([p,y]);[p,y]=v.then?(await v)():v;let w=e=>{let t={_id:"",title:""},{t:s}=(0,f.useTranslation)("common"),[a,x]=(0,r.useState)(t),j=e.routes&&"edit_eventstatus"===e.routes[0]&&e.routes[1],v=(0,r.useRef)(null),w=async t=>{let i,r;t.preventDefault();let n={title:a.title.trim()};j?(r="adminsetting.EventStatus.Forms.Eventstatusisupdatedsuccessfully",i=await y.A.patch(`/eventstatus/${e.routes[1]}`,n)):(r="adminsetting.EventStatus.Forms.Eventstatusisaddedsuccessfully",i=await y.A.post("/eventstatus",n)),i&&i._id?(p.default.success(s(r)),g().push("/adminsettings/eventstatus")):i?.errorCode===11e3?p.default.error(s("duplicatesNotAllowed")):p.default.error(i)};return(0,r.useEffect)(()=>{let t={query:{},sort:{title:"asc"},limit:"~"};j&&(async()=>{let s=await y.A.get(`/eventstatus/${e.routes[1]}`,t);x(e=>({...e,...s}))})()},[]),(0,i.jsx)("div",{children:(0,i.jsx)(n.A,{className:"formCard",fluid:!0,children:(0,i.jsx)(l.A,{style:{marginTop:"5px",boxShadow:"0 4px 8px 0 rgba(0, 0, 0, 0.2), 0 6px 20px 0 rgba(0, 0, 0, 0.19)"},children:(0,i.jsx)(h.A,{onSubmit:w,ref:v,initialValues:a,enableReinitialize:!0,children:(0,i.jsxs)(l.A.Body,{children:[(0,i.jsx)(d.A,{children:(0,i.jsx)(o.A,{children:(0,i.jsx)(l.A.Title,{children:s("adminsetting.EventStatus.Forms.EventStatus")})})}),(0,i.jsx)("hr",{}),(0,i.jsx)(d.A,{children:(0,i.jsx)(o.A,{md:!0,lg:6,sm:12,children:(0,i.jsxs)(c.A.Group,{children:[(0,i.jsx)(c.A.Label,{className:"required-field",children:s("adminsetting.EventStatus.Forms.EventStatus")}),(0,i.jsx)(m.ks,{name:"title",id:"title",required:!0,value:a.title,validator:e=>""!==String(e||"").trim(),errorMessage:{validator:s("adminsetting.EventStatus.Forms.PleaseAddtheEventStatus")},onChange:e=>{if(e.target){let{name:t,value:s}=e.target;x(e=>({...e,[t]:s}))}}})]})})}),(0,i.jsx)(d.A,{className:"my-4",children:(0,i.jsxs)(o.A,{children:[(0,i.jsx)(u.A,{className:"me-2",type:"submit",variant:"primary",children:s("adminsetting.EventStatus.Forms.Submit")}),(0,i.jsx)(u.A,{className:"me-2",onClick:()=>{x(t),window.scrollTo(0,0)},variant:"info",children:s("adminsetting.EventStatus.Forms.Reset")}),(0,i.jsx)(A(),{href:"/adminsettings/[...routes]",as:"/adminsettings/eventstatus",children:(0,i.jsx)(u.A,{variant:"secondary",children:s("adminsetting.EventStatus.Forms.Cancel")})})]})})]})})})})})};a()}catch(e){a(e)}})},25303:e=>{e.exports=require("@restart/ui/NavItem")},26045:(e,t,s)=>{s.a(e,async(e,a)=>{try{s.r(t),s.d(t,{default:()=>x});var i=s(8732),r=s(82015),n=s(19918),l=s.n(n),d=s(12403),o=s(91353),c=s(56084),u=s(63487),m=s(88751),h=e([u]);u=(h.then?(await h)():h)[0];let x=e=>{let[t,s]=(0,r.useState)([]),[,a]=(0,r.useState)(!1),[n,h]=(0,r.useState)(0),[x,g]=(0,r.useState)(10),[p,j]=(0,r.useState)(!1),[A,y]=(0,r.useState)({}),{t:f}=(0,m.useTranslation)("common"),v={sort:{title:"asc"},limit:x,page:1,query:{}},w=[{name:"Title",selector:"title"},{name:"Action",selector:"",cell:e=>(0,i.jsxs)("div",{children:[(0,i.jsx)(l(),{href:"/adminsettings/[...routes]",as:`/adminsettings/edit_role/${e._id}`,children:(0,i.jsx)("i",{className:"icon fas fa-edit"})}),"\xa0",(0,i.jsx)("a",{onClick:()=>_(e),children:(0,i.jsx)("i",{className:"icon fas fa-trash-alt"})})," "]})}],S=async()=>{a(!0);let e=await u.A.get("/roles",v);e&&e.data&&e.data.length>0&&(s(e.data),h(e.totalCount),a(!1))},b=async(e,t)=>{v.limit=e,v.page=t,a(!0);let i=await u.A.get("/roles",v);i&&i.data&&i.data.length>0&&(s(i.data),g(e),a(!1))},_=async e=>{y(e._id),j(!0)},C=async()=>{await u.A.remove(`/roles/${A}`),S(),j(!1)},T=()=>j(!1);return(0,r.useEffect)(()=>{S()},[]),(0,i.jsxs)("div",{children:[(0,i.jsxs)(d.A,{show:p,onHide:T,children:[(0,i.jsx)(d.A.Header,{closeButton:!0,children:(0,i.jsx)(d.A.Title,{children:f("DeleteRole")})}),(0,i.jsxs)(d.A.Body,{children:[f("Areyousurewanttodeletethisrole")," "]}),(0,i.jsxs)(d.A.Footer,{children:[(0,i.jsx)(o.A,{variant:"secondary",onClick:T,children:f("cancel")}),(0,i.jsx)(o.A,{variant:"primary",onClick:C,children:f("yes")})]})]}),(0,i.jsx)(c.A,{columns:w,data:t,totalRows:n,pagServer:!0,handlePerRowsChange:b,handlePageChange:e=>{v.limit=x,v.page=e,S()}})]})};a()}catch(e){a(e)}})},26324:e=>{e.exports=require("warning")},27232:(e,t,s)=>{s.a(e,async(e,a)=>{try{s.r(t),s.d(t,{default:()=>w});var i=s(8732),r=s(7082),n=s(18597),l=s(83551),d=s(49481),o=s(59549),c=s(91353),u=s(66994),m=s(23579),h=s(82015),x=s(44233),g=s.n(x),p=s(42893),j=s(19918),A=s.n(j),y=s(63487),f=s(88751),v=e([p,y]);[p,y]=v.then?(await v)():v;let w=e=>{let{t,i18n:s}=(0,f.useTranslation)("common"),a="de"===s.language?{title_de:"asc"}:{title:"asc"},x=s.language,j={title:"",country:null},[v,w]=(0,h.useState)(j),[S,b]=(0,h.useState)([]),_=e.routes&&"edit_region"===e.routes[0]&&e.routes[1],C=(0,h.useRef)(null),T=async s=>{let a,i;s.preventDefault();let r={title:v.title.trim(),country:v.country};_?(i="adminsetting.Regions.Regionisupdatedsuccessfully",a=await y.A.patch(`/region/${e.routes[1]}`,r)):(i="adminsetting.Regions.Regionisaddedsuccessfully",a=await y.A.post("/region",r)),a&&a._id?(p.default.success(t(i)),g().push("/adminsettings/region")):p.default.error(a)},k=e=>{if(e.target){let{name:t,value:s}=e.target;w(e=>({...e,[t]:s}))}},N=async e=>{let t=await y.A.get("/country",e);t&&b(t.data)};return(0,h.useEffect)(()=>{let t={query:{},sort:a,limit:"~",languageCode:x};_&&(async()=>{let s=await y.A.get(`/region/${e.routes[1]}`,t);if(s){let{country:e}=s;s.country=e&&e._id?e._id:"",w(e=>({...e,...s}))}})(),N(t)},[]),(0,i.jsx)("div",{children:(0,i.jsx)(r.A,{className:"formCard",fluid:!0,children:(0,i.jsx)(n.A,{style:{marginTop:"5px",boxShadow:"0 4px 8px 0 rgba(0, 0, 0, 0.2), 0 6px 20px 0 rgba(0, 0, 0, 0.19)"},children:(0,i.jsx)(u.A,{onSubmit:T,ref:C,initialValues:v,enableReinitialize:!0,children:(0,i.jsxs)(n.A.Body,{children:[(0,i.jsx)(l.A,{children:(0,i.jsx)(d.A,{children:(0,i.jsx)(n.A.Title,{children:_?t("adminsetting.Regions.EditRegion"):t("adminsetting.Regions.AddRegion")})})}),(0,i.jsx)("hr",{}),(0,i.jsxs)(l.A,{children:[(0,i.jsx)(d.A,{md:!0,lg:6,sm:12,children:(0,i.jsxs)(o.A.Group,{children:[(0,i.jsx)(o.A.Label,{className:"required-field",children:t("adminsetting.Regions.Country")}),(0,i.jsxs)(m.s3,{name:"country",id:"country",required:!0,value:v.country,validator:e=>""!==String(e||"").trim(),errorMessage:{validator:t("adminsetting.Regions.PleaseAddtheCountry")},onChange:k,children:[(0,i.jsx)("option",{value:"",children:t("adminsetting.Regions.SelectCountry")}),S.length>=1?S.map((e,t)=>(0,i.jsx)("option",{value:e._id,children:e.title},e._id)):null]})]})}),(0,i.jsx)(d.A,{md:!0,lg:6,sm:12,children:(0,i.jsxs)(o.A.Group,{children:[(0,i.jsx)(o.A.Label,{className:"required-field",children:t("adminsetting.Regions.Region")}),(0,i.jsx)(m.ks,{name:"title",id:"title",required:!0,value:v.title,errorMessage:{validator:t("adminsetting.Regions.PleaseAddtheRegion")},onChange:k})]})})]}),(0,i.jsx)(l.A,{className:"my-4",children:(0,i.jsxs)(d.A,{children:[(0,i.jsx)(c.A,{className:"me-2",type:"submit",variant:"primary",children:t("adminsetting.Regions.Submit")}),(0,i.jsx)(c.A,{className:"me-2",onClick:()=>{w(j),window.scrollTo(0,0)},variant:"info",children:t("adminsetting.Regions.Reset")}),(0,i.jsx)(A(),{href:"/adminsettings/[...routes]",as:"/adminsettings/region",children:(0,i.jsx)(c.A,{variant:"secondary",children:t("adminsetting.Regions.Cancel")})})]})})]})})})})})};a()}catch(e){a(e)}})},27406:(e,t,s)=>{s.a(e,async(e,a)=>{try{s.r(t),s.d(t,{default:()=>S});var i=s(8732),r=s(7082),n=s(18597),l=s(83551),d=s(49481),o=s(59549),c=s(91353),u=s(23579),m=s(66994),h=s(82015),x=s(42893),g=s(44233),p=s.n(g),j=s(19918),A=s.n(j),y=s(63487),f=s(88751),v=s(24047),w=e([x,y]);[x,y]=w.then?(await w)():w;let S=e=>{let t={title:"",code:"",description:""},[s,a]=(0,h.useState)(t),g=e.routes&&"edit_hazard_types"===e.routes[0]&&e.routes[1],{t:j}=(0,f.useTranslation)("common"),w=async(t,a)=>{let i,r;t.preventDefault();let n={title:s.title?.trim(),code:s.code?.trim(),description:s.description};g?(r="adminsetting.hazardtypes.updatesuccess",i=await y.A.patch(`/hazardtype/${e.routes[1]}`,n)):(r="adminsetting.hazardtypes.success",i=await y.A.post("/hazardtype",n)),i&&i._id?(x.default.success(j(r)),p().push("/adminsettings/hazardTypes")):i?.errorCode===11e3?x.default.error(j("duplicatesNotAllowed")):x.default.error(i)},S=e=>{if(e.target){let{name:t,value:s}=e.target;a(e=>({...e,[t]:s}))}},b=e=>{a(t=>({...t,description:e}))};(0,h.useEffect)(()=>{let t={query:{},sort:{title:"asc"},limit:"~"};g&&(async()=>{let s=await y.A.get(`/hazardtype/${e.routes[1]}`,t);a(e=>({...e,...s}))})()},[]);let _=(0,h.useRef)(null);return(0,i.jsx)(r.A,{className:"formCard",fluid:!0,children:(0,i.jsx)(n.A,{style:{marginTop:"5px",boxShadow:"0 4px 8px 0 rgba(0, 0, 0, 0.2), 0 6px 20px 0 rgba(0, 0, 0, 0.19)"},children:(0,i.jsx)(m.A,{onSubmit:w,ref:_,initialValues:s,enableReinitialize:!0,children:(0,i.jsxs)(n.A.Body,{children:[(0,i.jsx)(l.A,{children:(0,i.jsx)(d.A,{children:(0,i.jsx)(n.A.Title,{children:g?j("adminsetting.hazardtypes.EditHazardType"):j("adminsetting.hazardtypes.AddHazardType")})})}),(0,i.jsx)("hr",{}),(0,i.jsxs)(l.A,{className:"mb-3",children:[(0,i.jsx)(d.A,{md:!0,lg:6,sm:12,children:(0,i.jsxs)(o.A.Group,{children:[(0,i.jsx)(o.A.Label,{className:"required-field",children:j("adminsetting.hazardtypes.HazardTypeName")}),(0,i.jsx)(u.ks,{name:"title",id:"title",required:!0,value:s.title,errorMessage:{validator:j("adminsetting.hazardtypes.Add")},onChange:S})]})}),(0,i.jsx)(d.A,{md:!0,lg:6,sm:12,children:(0,i.jsxs)(o.A.Group,{children:[(0,i.jsx)(o.A.Label,{className:"required-field",children:j("adminsetting.hazardtypes.Code")}),(0,i.jsx)(u.ks,{name:"code",id:"code",required:!0,value:s.code,validator:e=>""!==String(e||"").trim(),errorMessage:{validator:j("adminsetting.hazardtypes.Please")},onChange:S})]})})]}),(0,i.jsx)(l.A,{children:(0,i.jsx)(d.A,{children:(0,i.jsxs)(o.A.Group,{children:[(0,i.jsx)(o.A.Label,{children:j("Description")}),(0,i.jsx)(v.x,{initContent:s.description,onChange:e=>b(e)})]})})}),(0,i.jsx)(l.A,{className:"my-4",children:(0,i.jsxs)(d.A,{children:[(0,i.jsx)(c.A,{className:"me-2",type:"submit",variant:"primary",children:j("submit")}),(0,i.jsx)(c.A,{className:"me-2",onClick:()=>{a(t),window.scrollTo(0,0)},variant:"info",children:j("reset")}),(0,i.jsx)(A(),{href:"/adminsettings/[...routes]",as:"/adminsettings/hazardTypes",children:(0,i.jsx)(c.A,{variant:"secondary",children:j("Cancel")})})]})})]})})})})};a()}catch(e){a(e)}})},27825:e=>{e.exports=require("lodash")},27910:e=>{e.exports=require("stream")},28217:e=>{e.exports=require("@restart/ui/DropdownItem")},28690:(e,t,s)=>{s.a(e,async(e,a)=>{try{s.r(t),s.d(t,{default:()=>f,getServerSideProps:()=>y});var i=s(8732),r=s(7082),n=s(83551),l=s(49481),d=s(91353),o=s(19918),c=s.n(o),u=s(27053),m=s(51758),h=s(88751),x=s(35576),g=s(45927),p=s(14062),j=s(35557),A=e([m,p]);async function y({locale:e}){return{props:{...await (0,x.serverSideTranslations)(e,["common"])}}}[m,p]=A.then?(await A)():A;let f=e=>{let{t}=(0,h.useTranslation)("common"),s=()=>(0,i.jsxs)(r.A,{style:{overflow:"hidden"},fluid:!0,className:"p-0",children:[(0,i.jsx)(n.A,{children:(0,i.jsx)(l.A,{xs:12,children:(0,i.jsx)(u.A,{title:t("adminsetting.Regions.Regions")})})}),(0,i.jsx)(n.A,{children:(0,i.jsx)(l.A,{xs:12,children:(0,i.jsx)(c(),{href:"/adminsettings/[...routes]",as:"/adminsettings/create_region",children:(0,i.jsx)(d.A,{variant:"secondary",size:"sm",children:t("adminsetting.Regions.AddRegion")})})})}),(0,i.jsx)(n.A,{className:"mt-3",children:(0,i.jsx)(l.A,{xs:12,children:(0,i.jsx)(m.default,{})})})]}),a=(0,g.canAddRegions)(()=>(0,i.jsx)(s,{})),o=(0,p.useSelector)(e=>e);return o?.permissions?.region?.["create:any"]?(0,i.jsx)(a,{}):(0,i.jsx)(j.default,{})};a()}catch(e){a(e)}})},28842:(e,t,s)=>{s.a(e,async(e,a)=>{try{s.r(t),s.d(t,{default:()=>h});var i=s(8732),r=s(82015),n=s(91353),l=s(12403),d=s(42893),o=s(56084),c=s(63487),u=s(88751),m=e([d,c]);[d,c]=m.then?(await m)():m;let h=function(e){let{t}=(0,u.useTranslation)("common"),[s,a]=(0,r.useState)([]),[,m]=(0,r.useState)(!1),[h,x]=(0,r.useState)(0),[g,p]=(0,r.useState)(10),[j,A]=(0,r.useState)(!1),[y,f]=(0,r.useState)(""),[v,w]=(0,r.useState)({}),S={sort:{created_at:"desc"},limit:"~",page:1,query:{"institutionInvites.status":"Request Pending"}},b=[{name:t("adminsetting.FocalPointsApprovalTable.Username"),selector:"username",cell:e=>e.username},{name:t("OrganisationName"),selector:"institutionName",cell:e=>e.institutionName},{name:t("adminsetting.FocalPointsApprovalTable.Email"),selector:"email",cell:e=>e.email},{name:t("adminsetting.FocalPointsApprovalTable.Action"),selector:"",cell:e=>(0,i.jsxs)("div",{children:["Rejected"===e.institutionStatus||"Request Pending"===e.institutionStatus?(0,i.jsxs)(i.Fragment,{children:[(0,i.jsx)(n.A,{variant:"primary",size:"sm",onClick:()=>R(e,"approve"),children:t("instu.Approves")}),"\xa0"]}):(0,i.jsx)(i.Fragment,{}),"Approved"===e.institutionStatus||"Request Pending"===e.institutionStatus?(0,i.jsx)(n.A,{variant:"secondary",size:"sm",onClick:()=>R(e,"reject"),children:t("instu.Reject")}):(0,i.jsx)(i.Fragment,{})]})}],_=e=>{let t=[];return e.forEach(e=>{e?.institutionInvites.forEach(s=>{s&&"Request Pending"===s.status&&t.push({...e,...{institutionId:s.institutionId,institutionName:s.institutionName,institutionStatus:s.status}})})}),t},C=async()=>{m(!0);let e=await c.A.get("/users",S);if(e&&e.data){let t=_(e.data);a(N(t,g,1)),x(t.length),m(!1)}},T=async e=>{m(!0);let t=await c.A.get("/users",S);if(t&&t.data&&t.data.length>0){let s=_(t.data);a(N(s,g,e)),m(!1)}},k=async(e,t)=>{m(!0);let s=await c.A.get("/users",S);if(s&&s.data&&s.data.length>0){let i=_(s.data);a(N(i,e,t)),p(e),m(!1)}},N=(e,t,s)=>e.slice((s-1)*t,s*t),R=async(e,t)=>{console.log(e,t),A(!0),f(t),e&&e._id&&w({...e,status:"approve"===t?"Approved":"Rejected"})},P=async()=>{let e;if(v.is_focal_point=!0,v.status="Approved",v&&v.institutionInvites&&v.institutionInvites.length&&v.institutionInvites.map(e=>(e.institutionId===v.institutionId&&(e.status="approve"===y?"Approved":"Rejected"),e)),"approve"!==y?await c.A.remove(`/users/${v._id}`):e=await c.A.patch(`/users/${v._id}`,v),e&&403===e.status)return void d.default.error(e.response&&e.response.message?e.response.message:t("adminsetting.FocalPointsApprovalTable.Somethingwentswrong"));C(),"approve"===y?d.default.success(t("adminsetting.FocalPointsApprovalTable.Approvemm")):d.default.error(t("adminsetting.FocalPointsApprovalTable.Rejected")),w({}),A(!1)},q=()=>A(!1);return(0,i.jsxs)("div",{children:[(0,i.jsxs)(l.A,{show:j,onHide:q,children:[(0,i.jsx)(l.A.Header,{closeButton:!0,children:(0,i.jsxs)(l.A.Title,{children:[y.charAt(0).toUpperCase()+y.slice(1)," ",t("adminsetting.FocalPointsApprovalTable.User")]})}),(0,i.jsxs)(l.A.Body,{children:[t("adminsetting.FocalPointsApprovalTable.Areyousurewantto")," ",y," ",t("adminsetting.FocalPointsApprovalTable.thisuser?")]}),(0,i.jsxs)(l.A.Footer,{children:[(0,i.jsx)(n.A,{variant:"secondary",onClick:q,children:t("adminsetting.FocalPointsApprovalTable.Cancel")}),(0,i.jsx)(n.A,{variant:"primary",onClick:P,children:t("adminsetting.FocalPointsApprovalTable.Yes")})]})]}),(0,i.jsx)(o.A,{columns:b,data:s,totalRows:h,pagServer:!0,handlePerRowsChange:k,handlePageChange:T})]})};a()}catch(e){a(e)}})},29021:e=>{e.exports=require("fs")},29294:e=>{e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},29628:e=>{e.exports=require("dom-helpers/addClass")},29825:e=>{e.exports=require("prop-types")},29841:e=>{e.exports=require("dom-helpers/hasClass")},29853:(e,t,s)=>{s.a(e,async(e,a)=>{try{s.r(t),s.d(t,{default:()=>A});var i=s(8732),r=s(7082),n=s(83551),l=s(49481),d=s(91353),o=s(19918),c=s.n(o),u=s(27053),m=s(6945),h=s(88751),x=s(45927),g=s(14062),p=s(35557),j=e([m,g]);[m,g]=j.then?(await j)():j;let A=e=>{let{t}=(0,h.useTranslation)("common"),s=()=>(0,i.jsxs)(r.A,{style:{overflowX:"hidden"},fluid:!0,className:"p-0",children:[(0,i.jsx)(n.A,{children:(0,i.jsx)(l.A,{xs:12,children:(0,i.jsx)(u.A,{title:t("adminsetting.Organisationnetworks.OrganisationNetworks")})})}),(0,i.jsx)(n.A,{children:(0,i.jsx)(l.A,{xs:12,children:(0,i.jsx)(c(),{href:"/adminsettings/[...routes]",as:"/adminsettings/create_institution_network",children:(0,i.jsx)(d.A,{variant:"secondary",size:"sm",children:t("adminsetting.Organisationnetworks.AddOrganisationNetwork")})})})}),(0,i.jsx)(n.A,{className:"mt-3",children:(0,i.jsx)(l.A,{xs:12,children:(0,i.jsx)(m.default,{})})})]}),a=(0,x.canAddOrganisationNetworks)(()=>(0,i.jsx)(s,{})),o=(0,g.useSelector)(e=>e);return o?.permissions?.institution_network?.["create:any"]?(0,i.jsx)(a,{}):(0,i.jsx)(p.default,{})};a()}catch(e){a(e)}})},30103:(e,t,s)=>{s.a(e,async(e,a)=>{try{s.r(t),s.d(t,{default:()=>p});var i=s(8732),r=s(7082),n=s(83551),l=s(49481),d=s(91353),o=s(19918),c=s.n(o),u=s(27053),m=s(95864),h=s(88751),x=s(45927),g=e([m]);m=(g.then?(await g)():g)[0];let p=e=>{let{t}=(0,h.useTranslation)("common"),s=()=>(0,i.jsxs)(r.A,{style:{overflowX:"hidden"},fluid:!0,className:"p-0",children:[(0,i.jsx)(n.A,{children:(0,i.jsx)(l.A,{xs:12,children:(0,i.jsx)(u.A,{title:t("adminsetting.updatestype.UpdateType")})})}),(0,i.jsx)(n.A,{children:(0,i.jsx)(l.A,{xs:12,children:(0,i.jsx)(c(),{href:"/adminsettings/[...routes]",as:"/adminsettings/create_update_type",children:(0,i.jsx)(d.A,{variant:"secondary",size:"sm",children:t("adminsetting.updatestype.AddUpdateType")})})})}),(0,i.jsx)(n.A,{className:"mt-3",children:(0,i.jsx)(l.A,{xs:12,children:(0,i.jsx)(m.default,{})})})]}),a=(0,x.default)(()=>(0,i.jsx)(s,{}));return(0,i.jsx)(a,{})};a()}catch(e){a(e)}})},30362:e=>{e.exports=require("@restart/ui/DropdownToggle")},32777:(e,t,s)=>{s.a(e,async(e,a)=>{try{s.r(t),s.d(t,{default:()=>w});var i=s(8732),r=s(82015),n=s(7082),l=s(18597),d=s(83551),o=s(49481),c=s(59549),u=s(91353),m=s(23579),h=s(66994),x=s(44233),g=s.n(x),p=s(42893),j=s(19918),A=s.n(j),y=s(63487),f=s(88751),v=e([p,y]);[p,y]=v.then?(await v)():v;let w=e=>{let t={title:""},[s,a]=(0,r.useState)(t),x=e.routes&&"edit_institution_network"===e.routes[0]&&e.routes[1],{t:j}=(0,f.useTranslation)("common"),v=(0,r.useRef)(null),w=async t=>{let a,i;t.preventDefault();let r={title:s.title.trim()};x?(i="adminsetting.Organisationnetworks.updatesuccess",a=await y.A.patch(`/institutionnetwork/${e.routes[1]}`,r)):(i="adminsetting.Organisationnetworks.success",a=await y.A.post("/institutionnetwork",r)),a&&a._id?(p.default.success(j(i)),g().push("/adminsettings/institution_network")):a?.errorCode===11e3?p.default.error(j("duplicatesNotAllowed")):p.default.error(a)};return(0,r.useEffect)(()=>{let t={query:{},sort:{title:"asc"},limit:"~"};x&&(async()=>{let s=await y.A.get(`/institutionnetwork/${e.routes[1]}`,t);a(e=>({...e,...s}))})()},[]),(0,i.jsx)("div",{children:(0,i.jsx)(n.A,{className:"formCard",fluid:!0,children:(0,i.jsx)(l.A,{style:{marginTop:"5px",boxShadow:"0 4px 8px 0 rgba(0, 0, 0, 0.2), 0 6px 20px 0 rgba(0, 0, 0, 0.19)"},children:(0,i.jsx)(h.A,{onSubmit:w,ref:v,initialValues:s,enableReinitialize:!0,children:(0,i.jsxs)(l.A.Body,{children:[(0,i.jsx)(d.A,{children:(0,i.jsx)(o.A,{children:(0,i.jsx)(l.A.Title,{children:j("adminsetting.Organisationnetworks.OrganisationNetworks")})})}),(0,i.jsx)("hr",{}),(0,i.jsx)(d.A,{children:(0,i.jsx)(o.A,{md:!0,lg:6,sm:12,children:(0,i.jsxs)(c.A.Group,{children:[(0,i.jsx)(c.A.Label,{className:"required-field",children:j("adminsetting.Organisationnetworks.OrganisationNetworks")}),(0,i.jsx)(m.ks,{name:"title",id:"title",required:!0,value:s.title,validator:e=>""!==e.trim(),errorMessage:{validator:j("adminsetting.Organisationnetworks.Add")},onChange:e=>{if(e.target){let{name:t,value:s}=e.target;a(e=>({...e,[t]:s}))}}})]})})}),(0,i.jsx)(d.A,{className:"my-4",children:(0,i.jsxs)(o.A,{children:[(0,i.jsx)(u.A,{className:"me-2",type:"submit",variant:"primary",children:j("submit")}),(0,i.jsx)(u.A,{className:"me-2",onClick:()=>{a(t),window.scrollTo(0,0)},variant:"info",children:j("reset")}),(0,i.jsx)(A(),{href:"/adminsettings/[...routes]",as:"/adminsettings/institution_network",children:(0,i.jsx)(u.A,{variant:"secondary",children:j("Cancel")})})]})})]})})})})})};a()}catch(e){a(e)}})},32900:(e,t,s)=>{s.a(e,async(e,a)=>{try{s.r(t),s.d(t,{default:()=>g});var i=s(8732),r=s(7082),n=s(83551),l=s(49481),d=s(38368),o=s(88751),c=s(27053),u=s(45927),m=s(35557),h=s(14062),x=e([d,h]);[d,h]=x.then?(await x)():x;let g=e=>{let{t}=(0,o.useTranslation)("common"),s=()=>(0,i.jsxs)(r.A,{style:{overflowX:"hidden"},fluid:!0,className:"p-0",children:[(0,i.jsx)(n.A,{children:(0,i.jsx)(l.A,{xs:12,children:(0,i.jsx)(c.A,{title:t("adminsetting.landing.form.EditableContent")})})}),(0,i.jsx)(n.A,{className:"mt-3",children:(0,i.jsx)(l.A,{xs:12,children:(0,i.jsx)(d.default,{})})})]}),a=(0,u.canAddLandingPage)(()=>(0,i.jsx)(s,{})),x=(0,h.useSelector)(e=>e);return x?.permissions?.landing_page?.["create:any"]?(0,i.jsx)(a,{}):(0,i.jsx)(m.default,{})};a()}catch(e){a(e)}})},33873:e=>{e.exports=require("path")},34304:(e,t,s)=>{s.a(e,async(e,a)=>{try{s.r(t),s.d(t,{default:()=>A});var i=s(8732),r=s(82015),n=s(19918),l=s.n(n),d=s(12403),o=s(91353),c=s(27825),u=s.n(c),m=s(42893),h=s(56084),x=s(63487),g=s(73462),p=s(88751),j=e([m,x]);[m,x]=j.then?(await j)():j;let A=e=>{let{t,i18n:s}=(0,p.useTranslation)("common"),a="de"===s.language?{title_de:"asc"}:{title:"asc"},n=s.language,[c,j]=(0,r.useState)([]),[,A]=(0,r.useState)(!1),[y,f]=(0,r.useState)(0),[v,w]=(0,r.useState)(10),[S,b]=(0,r.useState)(!1),[_,C]=(0,r.useState)({}),[T,k]=(0,r.useState)(""),[N,R]=(0,r.useState)(!1),P={sort:a,limit:v,page:1,query:{},languageCode:n||"en"},q=[{name:t("adminsetting.Countries.Table.Country"),selector:e=>e.title,sortable:!0},{name:t("adminsetting.Countries.Table.Code"),selector:e=>e.code,sortable:!0},{name:t("adminsetting.Countries.Table.DialCode"),selector:e=>e.dial_code,sortable:!0},{name:t("adminsetting.Countries.Table.WorldRegion"),selector:e=>e.world_region?.title||"",sortable:!0},{name:t("adminsetting.Countries.Table.Action"),selector:e=>e._id,sortable:!1,cell:e=>(0,i.jsxs)("div",{children:[(0,i.jsx)(l(),{href:"/adminsettings/[...routes]",as:`/adminsettings/edit_country/${e._id}`,children:(0,i.jsx)("i",{className:"icon fas fa-edit"})}),"\xa0",(0,i.jsx)("a",{onClick:()=>D(e),children:(0,i.jsx)("i",{className:"icon fas fa-trash-alt"})})]})}],E=async e=>{A(!0);let t=await x.A.get("/country",e);t&&(j(t.data),f(t.totalCount),A(!1))},z=async(e,t)=>{P.limit=e,P.page=t,A(!0);let s=await x.A.get("/country",P);s&&s.data&&s.data.length>0&&(j(s.data),w(e),A(!1))},D=async e=>{C(e._id),b(!0)},$=async()=>{try{await x.A.remove(`/country/${_}`),E(P),b(!1),m.default.success(t("adminsetting.Countries.Table.countryDeletedSuccessfully"))}catch(e){m.default.error(t("adminsetting.Countries.Table.errorDeletingcountry"))}},F=()=>b(!1),H=(0,r.useMemo)(()=>{let e=e=>{e&&(P.query={title:e}),E(P)},t=u().debounce(t=>e(t),Number("500")||300);return(0,i.jsx)(g.default,{onFilter:e=>{k(e.target.value),t(e.target.value)},onClear:()=>{T&&(R(!N),k(""))},filterText:T})},[T]);return(0,r.useEffect)(()=>{E(P)},[]),(0,i.jsxs)("div",{children:[(0,i.jsxs)(d.A,{show:S,onHide:F,children:[(0,i.jsx)(d.A.Header,{closeButton:!0,children:(0,i.jsx)(d.A.Title,{children:t("adminsetting.Countries.Table.DeleteCountry")})}),(0,i.jsx)(d.A.Body,{children:t("adminsetting.Countries.Table.Areyousurewanttodeletethiscountry?")}),(0,i.jsxs)(d.A.Footer,{children:[(0,i.jsx)(o.A,{variant:"secondary",onClick:F,children:t("adminsetting.Countries.Table.Cancel")}),(0,i.jsx)(o.A,{variant:"primary",onClick:$,children:t("adminsetting.Countries.Table.Yes")})]})]}),(0,i.jsx)(h.A,{columns:q,data:c,totalRows:y,subheader:!0,pagServer:!0,resetPaginationToggle:N,subHeaderComponent:H,handlePerRowsChange:z,handlePageChange:e=>{P.limit=v,P.page=e,E(P)}})]})};a()}catch(e){a(e)}})},34912:(e,t,s)=>{s.a(e,async(e,a)=>{try{s.r(t),s.d(t,{default:()=>g});var i=s(8732),r=s(82015),n=s(19918),l=s.n(n),d=s(12403),o=s(91353),c=s(42893),u=s(56084),m=s(63487),h=s(88751),x=e([c,m]);[c,m]=x.then?(await x)():x;let g=e=>{let{t}=(0,h.useTranslation)("common"),[s,a]=(0,r.useState)([]),[,n]=(0,r.useState)(!1),[x,g]=(0,r.useState)(0),[p,j]=(0,r.useState)(10),[A,y]=(0,r.useState)(!1),[f,v]=(0,r.useState)({}),w={sort:{title:"asc"},limit:p,page:1,query:{}},S=[{name:t("adminsetting.Expertise.Table.Title"),selector:"title"},{name:t("adminsetting.Expertise.Table.Action"),selector:"",cell:e=>(0,i.jsxs)("div",{children:[(0,i.jsx)(l(),{href:"/adminsettings/[...routes]",as:`/adminsettings/edit_expertise/${e._id}`,children:(0,i.jsx)("i",{className:"icon fas fa-edit"})}),"\xa0",(0,i.jsx)("a",{onClick:t=>C(e,t),children:(0,i.jsx)("i",{className:"icon fas fa-trash-alt"})})," "]})}],b=async()=>{n(!0);let e=await m.A.get("/expertise",w);e&&e.data&&e.data.length>0&&(a(e.data),g(e.totalCount),n(!1))},_=async(e,t)=>{w.limit=e,w.page=t,n(!0);let s=await m.A.get("/expertise",w);s&&s.data&&s.data.length>0&&(a(s.data),j(e),n(!1))},C=async(e,t)=>{t.preventDefault(),v(e._id),y(!0)},T=async()=>{try{await m.A.remove(`/expertise/${f}`),b(),y(!1),c.default.success(t("adminsetting.Expertise.Table.expertiseDeletedSuccessfully"))}catch(e){c.default.error(t("adminsetting.Expertise.Table.errorDeletingExpertise"))}},k=()=>y(!1);return(0,r.useEffect)(()=>{b()},[]),(0,i.jsxs)("div",{children:[(0,i.jsxs)(d.A,{show:A,onHide:k,children:[(0,i.jsx)(d.A.Header,{closeButton:!0,children:(0,i.jsxs)(d.A.Title,{children:[" ",t("adminsetting.Expertise.Table.DeleteExpertise")]})}),(0,i.jsx)(d.A.Body,{children:t("adminsetting.Expertise.Table.AreyousurewanttodeletethisExpertise?")}),(0,i.jsxs)(d.A.Footer,{children:[(0,i.jsx)(o.A,{variant:"secondary",onClick:k,children:t("adminsetting.Expertise.Table.Cancel")}),(0,i.jsx)(o.A,{variant:"primary",onClick:T,children:t("adminsetting.Expertise.Table.Yes")})]})]}),(0,i.jsx)(u.A,{columns:S,data:s,totalRows:x,pagServer:!0,handlePerRowsChange:_,handlePageChange:e=>{w.limit=p,w.page=e,b()}})]})};a()}catch(e){a(e)}})},35576:e=>{e.exports=require("next-i18next/serverSideTranslations")},36228:(e,t,s)=>{s.a(e,async(e,a)=>{try{s.r(t),s.d(t,{default:()=>f,getServerSideProps:()=>y});var i=s(8732),r=s(7082),n=s(83551),l=s(49481),d=s(91353),o=s(19918),c=s.n(o),u=s(27053),m=s(66116),h=s(88751),x=s(35576),g=s(45927),p=s(14062),j=s(35557),A=e([m,p]);async function y({locale:e}){return{props:{...await (0,x.serverSideTranslations)(e,["common"])}}}[m,p]=A.then?(await A)():A;let f=e=>{let{t}=(0,h.useTranslation)("common"),s=()=>(0,i.jsx)("div",{children:(0,i.jsxs)(r.A,{style:{overflowX:"hidden"},fluid:!0,className:"p-0",children:[(0,i.jsx)(n.A,{children:(0,i.jsx)(l.A,{xs:12,children:(0,i.jsx)(u.A,{title:t("adminsetting.worldregion.form.WorldRegion")})})}),(0,i.jsx)(n.A,{children:(0,i.jsx)(l.A,{xs:12,children:(0,i.jsx)(c(),{href:"/adminsettings/[...routes]",as:"/adminsettings/create_worldregion",children:(0,i.jsx)(d.A,{variant:"secondary",size:"sm",children:t("adminsetting.worldregion.form.Addworldregion")})})})}),(0,i.jsx)(n.A,{className:"mt-3",children:(0,i.jsx)(l.A,{xs:12,children:(0,i.jsx)(m.default,{})})})]})}),a=(0,g.canAddWorldRegion)(()=>(0,i.jsx)(s,{})),o=(0,p.useSelector)(e=>e);return o?.permissions?.worl_region?.["create:any"]?(0,i.jsx)(a,{}):(0,i.jsx)(j.default,{})};a()}catch(e){a(e)}})},36653:e=>{e.exports=require("nprogress")},36955:e=>{e.exports=require("@restart/hooks/useBreakpoint")},37766:e=>{e.exports=require("@restart/hooks/useMergedRefs")},38368:(e,t,s)=>{s.a(e,async(e,a)=>{try{s.r(t),s.d(t,{default:()=>g});var i=s(8732),r=s(19918),n=s.n(r),l=s(82015),d=s(12403),o=s(91353),c=s(42893),u=s(56084),m=s(63487),h=s(88751),x=e([c,m]);[c,m]=x.then?(await x)():x;let g=e=>{let{t,i18n:s}=(0,h.useTranslation)("common");s.language&&s.language;let[a,r]=(0,l.useState)([]),[,x]=(0,l.useState)(!1),[g,p]=(0,l.useState)(0),[j,A]=(0,l.useState)(10),[y,f]=(0,l.useState)(!1),[v,w]=(0,l.useState)({}),S=()=>f(!1),b=[{name:t("adminsetting.landing.table.Title"),selector:"title",cell:e=>e.title},{name:t("adminsetting.landing.table.Enabled"),selector:"isEnabled",cell:e=>e.isEnabled?"Yes":"No"},{name:t("adminsetting.landing.table.Action"),selector:"",cell:e=>(0,i.jsxs)("div",{children:[(0,i.jsx)(n(),{href:"/adminsettings/[...routes]",as:`/adminsettings/edit_landing/${e._id}`,children:(0,i.jsx)("i",{className:"icon fas fa-edit"})}),"\xa0"," "]})}],_={sort:{title:"asc"},limit:j,page:1};(0,l.useEffect)(()=>{C()},[]);let C=async()=>{x(!0);let e=await m.A.get("/landingPage",_);e&&(r(e.data),p(e.totalCount),x(!1))},T=async(e,t)=>{_.limit=e,_.page=t,x(!0);let s=await m.A.get("/landingPage",_);s&&s.data&&s.data.length>0&&(r(s.data),A(e),x(!1))},k=async()=>{try{await m.A.remove(`/landingPage/${v}`),C(),f(!1),c.default.success(t("adminsetting.landing.table.landingDeletedSuccessfully"))}catch(e){c.default.error(t("adminsetting.landing.table.errorDeletingLanding"))}};return(0,i.jsxs)("div",{children:[(0,i.jsxs)(d.A,{show:y,onHide:S,children:[(0,i.jsx)(d.A.Header,{closeButton:!0,children:(0,i.jsx)(d.A.Title,{children:t("adminsetting.landing.table.DeleteEditableContent")})}),(0,i.jsx)(d.A.Body,{children:t("adminsetting.landing.table.AreyousurewanttodeletethisEditableContent?")}),(0,i.jsxs)(d.A.Footer,{children:[(0,i.jsx)(o.A,{variant:"secondary",onClick:S,children:t("adminsetting.landing.table.Cancel")}),(0,i.jsx)(o.A,{variant:"primary",onClick:k,children:t("adminsetting.landing.table.Yes")})]})]}),(0,i.jsx)(u.A,{columns:b,data:a,totalRows:g,pagServer:!0,handlePerRowsChange:T,handlePageChange:e=>{_.limit=j,_.page=e,C()}})]})};a()}catch(e){a(e)}})},38609:e=>{e.exports=require("react-data-table-component")},39756:e=>{e.exports=import("redux")},40051:e=>{e.exports=require("dom-helpers/removeClass")},40361:e=>{e.exports=require("next/dist/compiled/next-server/pages.runtime.prod.js")},42404:(e,t,s)=>{s.a(e,async(e,a)=>{try{s.r(t),s.d(t,{default:()=>h});var i=s(8732),r=s(7082),n=s(83551),l=s(49481),d=s(99800),o=s(82015),c=s(63487),u=s(88751),m=e([d,c]);[d,c]=m.then?(await m)():m;let h=({countryHandler:e,value:t})=>{let{t:s,i18n:a}=(0,u.useTranslation)("common"),m="de"===a.language?{title_de:"asc"}:{title:"asc"},h=a.language,[x,g]=(0,o.useState)([]),p={sort:m,limit:"~",languageCode:h};return(0,o.useEffect)(()=>{(async()=>{let e=await c.A.get("/country",p);e&&e.data&&e.data.length>0&&g(e.data)})()},[]),(0,i.jsx)(r.A,{fluid:!0,children:(0,i.jsx)(n.A,{children:(0,i.jsx)(l.A,{md:4,className:"ps-1",children:(0,i.jsx)(d.default,{value:[t],placeholder:s("adminsetting.Regions.SelectCountry"),isClearable:!0,onChange:e,options:x.length>0?x.map((e,t)=>({value:e._id,label:e.title})):[]})})})})};a()}catch(e){a(e)}})},42893:e=>{e.exports=import("react-hot-toast")},43252:(e,t,s)=>{s.a(e,async(e,a)=>{try{s.r(t),s.d(t,{default:()=>w});var i=s(8732),r=s(82015),n=s(7082),l=s(18597),d=s(83551),o=s(49481),c=s(59549),u=s(91353),m=s(66994),h=s(23579),x=s(44233),g=s.n(x),p=s(42893),j=s(19918),A=s.n(j),y=s(63487),f=s(88751),v=e([p,y]);[p,y]=v.then?(await v)():v;let w=e=>{let t={_id:"",title:""},[s,a]=(0,r.useState)(t),x=!!(e.routes&&"edit_category"===e.routes[0]&&e.routes[1]),{t:j}=(0,f.useTranslation)("common"),v=(0,r.useRef)(null),w=async t=>{let a;t.preventDefault();let i={title:s.title.trim()};(a=x?await y.A.patch(`/category/${e.routes[1]}`,i):await y.A.post("/category",i))&&a._id?(p.default.success(j("Categoryisaddedsuccessfully")),g().push("/adminsettings/category")):p.default.error(a)};return(0,r.useEffect)(()=>{let t={query:{},sort:{title:"asc"},limit:"~"};x&&(async()=>{let s=await y.A.get(`/category/${e.routes[1]}`,t);a(e=>({...e,...s}))})()},[]),(0,i.jsx)("div",{children:(0,i.jsx)(n.A,{className:"formCard",fluid:!0,children:(0,i.jsx)(l.A,{style:{marginTop:"5px",boxShadow:"0 4px 8px 0 rgba(0, 0, 0, 0.2), 0 6px 20px 0 rgba(0, 0, 0, 0.19)"},children:(0,i.jsx)(m.A,{onSubmit:w,ref:v,initialValues:s,enableReinitialize:!0,children:(0,i.jsxs)(l.A.Body,{children:[(0,i.jsx)(d.A,{children:(0,i.jsx)(o.A,{children:(0,i.jsx)(l.A.Title,{children:j("Category")})})}),(0,i.jsx)("hr",{}),(0,i.jsx)(d.A,{children:(0,i.jsx)(o.A,{md:!0,lg:6,sm:12,children:(0,i.jsxs)(c.A.Group,{children:[(0,i.jsx)(c.A.Label,{className:"required-field",children:j("Category")}),(0,i.jsx)(h.ks,{name:"title",id:"title",required:!0,value:s.title,validator:e=>""!==String(e||"").trim(),errorMessage:{validator:j("PleaseAddtheCategory")},onChange:e=>{if(e.target){let{name:t,value:s}=e.target;a(e=>({...e,[t]:s}))}}})]})})}),(0,i.jsx)(d.A,{className:"my-4",children:(0,i.jsxs)(o.A,{children:[(0,i.jsx)(u.A,{className:"me-2",type:"submit",variant:"primary",children:j("submit")}),(0,i.jsx)(u.A,{className:"me-2",onClick:()=>{a(t),window.scrollTo(0,0)},variant:"info",children:j("reset")}),(0,i.jsx)(A(),{href:"/adminsettings/[...routes]",as:"/adminsettings/category",children:(0,i.jsx)(u.A,{variant:"secondary",children:j("Cancel")})})]})})]})})})})})};a()}catch(e){a(e)}})},43294:e=>{e.exports=require("formik")},43619:(e,t,s)=>{s.a(e,async(e,a)=>{try{s.r(t),s.d(t,{default:()=>f,getServerSideProps:()=>y});var i=s(8732),r=s(7082),n=s(83551),l=s(49481),d=s(91353),o=s(19918),c=s.n(o),u=s(89568),m=s(88751),h=s(35576),x=s(27053),g=s(45927),p=s(14062),j=s(35557),A=e([u,p]);async function y({locale:e}){return{props:{...await (0,h.serverSideTranslations)(e,["common"])}}}[u,p]=A.then?(await A)():A;let f=e=>{let{t}=(0,m.useTranslation)("common"),s=()=>(0,i.jsx)("div",{children:(0,i.jsxs)(r.A,{style:{overflowX:"hidden"},fluid:!0,className:"p-0",children:[(0,i.jsx)(n.A,{children:(0,i.jsx)(l.A,{xs:12,children:(0,i.jsx)(x.A,{title:t("adminsetting.DeploymentStatus.Forms.DeploymentStatus")})})}),(0,i.jsx)(n.A,{children:(0,i.jsx)(l.A,{xs:12,children:(0,i.jsx)(c(),{href:"/adminsettings/[...routes]",as:"/adminsettings/create_deploymentstatus",children:(0,i.jsx)(d.A,{variant:"secondary",size:"sm",children:t("adminsetting.DeploymentStatus.Forms.AddDeploymentStatus")})})})}),(0,i.jsx)(n.A,{className:"mt-3",children:(0,i.jsx)(l.A,{xs:12,children:(0,i.jsx)(u.default,{})})})]})}),a=(0,g.canAddDeploymentStatus)(()=>(0,i.jsx)(s,{})),o=(0,p.useSelector)(e=>e);return o?.permissions?.deployment_status?.["create:any"]?(0,i.jsx)(a,{}):(0,i.jsx)(j.default,{})};a()}catch(e){a(e)}})},44457:(e,t,s)=>{s.a(e,async(e,a)=>{try{s.r(t),s.d(t,{default:()=>A});var i=s(8732),r=s(7082),n=s(83551),l=s(49481),d=s(91353),o=s(19918),c=s.n(o),u=s(94149),m=s(27053),h=s(88751),x=s(45927),g=s(35557),p=s(14062),j=e([u,p]);[u,p]=j.then?(await j)():j;let A=e=>{let{t}=(0,h.useTranslation)("common"),s=()=>(0,i.jsxs)(r.A,{style:{overflowX:"hidden"},fluid:!0,className:"p-0",children:[(0,i.jsx)(n.A,{children:(0,i.jsx)(l.A,{xs:12,children:(0,i.jsx)(m.A,{title:t("adminsetting.hazardtypes.HarzardType")})})}),(0,i.jsx)(n.A,{children:(0,i.jsx)(l.A,{xs:12,children:(0,i.jsx)(c(),{href:"/adminsettings/[...routes]",as:"/adminsettings/create_hazard_types",children:(0,i.jsx)(d.A,{variant:"secondary",size:"sm",children:t("adminsetting.hazardtypes.type")})})})}),(0,i.jsx)(n.A,{className:"mt-3",children:(0,i.jsx)(l.A,{xs:12,children:(0,i.jsx)(u.default,{})})})]}),a=(0,x.canAddHazardTypes)(()=>(0,i.jsx)(s,{})),o=(0,p.useSelector)(e=>e);return o?.permissions?.hazard_type?.["create:any"]?(0,i.jsx)(a,{}):(0,i.jsx)(g.default,{})};a()}catch(e){a(e)}})},44971:(e,t,s)=>{s.a(e,async(e,a)=>{try{s.r(t),s.d(t,{default:()=>w});var i=s(8732),r=s(82015),n=s(7082),l=s(18597),d=s(83551),o=s(49481),c=s(59549),u=s(91353),m=s(23579),h=s(66994),x=s(44233),g=s.n(x),p=s(42893),j=s(19918),A=s.n(j),y=s(63487),f=s(88751),v=e([p,y]);[p,y]=v.then?(await v)():v;let w=e=>{let t={_id:"",title:""},{t:s}=(0,f.useTranslation)("common"),[a,x]=(0,r.useState)(t),j=e.routes&&"edit_expertise"===e.routes[0]&&e.routes[1],v=(0,r.useRef)(null),w=async t=>{let i,r;t.preventDefault();let n={title:a.title.trim()};j?(r="adminsetting.Expertise.Forms.Expertiseisupdatedsuccessfully",i=await y.A.patch(`/expertise/${e.routes[1]}`,n)):(r="adminsetting.Expertise.Forms.Expertiseisaddedsuccessfully",i=await y.A.post("/expertise",n)),i&&i._id?(p.default.success(s(r)),g().push("/adminsettings/expertise")):i?.errorCode===11e3?p.default.error(s("duplicatesNotAllowed")):p.default.error(i)};return(0,r.useEffect)(()=>{let t={query:{},sort:{title:"asc"},limit:"~"};j&&(async()=>{let s=await y.A.get(`/expertise/${e.routes[1]}`,t);x(e=>({...e,...s}))})()},[]),(0,i.jsx)("div",{children:(0,i.jsx)(n.A,{className:"formCard",fluid:!0,children:(0,i.jsx)(l.A,{style:{marginTop:"5px",boxShadow:"0 4px 8px 0 rgba(0, 0, 0, 0.2), 0 6px 20px 0 rgba(0, 0, 0, 0.19)"},children:(0,i.jsx)(h.A,{onSubmit:w,ref:v,initialValues:a,enableReinitialize:!0,children:(0,i.jsxs)(l.A.Body,{children:[(0,i.jsx)(d.A,{children:(0,i.jsx)(o.A,{children:(0,i.jsx)(l.A.Title,{children:s("adminsetting.Expertise.Forms.Expertise")})})}),(0,i.jsx)("hr",{}),(0,i.jsx)(d.A,{children:(0,i.jsx)(o.A,{md:!0,lg:6,sm:12,children:(0,i.jsxs)(c.A.Group,{children:[(0,i.jsx)(c.A.Label,{className:"required-field",children:s("adminsetting.Expertise.Forms.Expertise")}),(0,i.jsx)(m.ks,{name:"title",id:"title",required:!0,value:a.title,validator:e=>""!==String(e||"").trim(),errorMessage:{validator:s("adminsetting.Expertise.Forms.PleaseAddtheExpertise")},onChange:e=>{if(e.target){let{name:t,value:s}=e.target;x(e=>({...e,[t]:s}))}}})]})})}),(0,i.jsx)(d.A,{className:"my-4",children:(0,i.jsxs)(o.A,{children:[(0,i.jsx)(u.A,{className:"me-2",type:"submit",variant:"primary",children:s("adminsetting.Expertise.Forms.Submit")}),(0,i.jsx)(u.A,{className:"me-2",onClick:()=>{x(t),window.scrollTo(0,0)},variant:"info",children:s("adminsetting.Expertise.Forms.Reset")}),(0,i.jsx)(A(),{href:"/adminsettings/[...routes]",as:"/adminsettings/expertise",children:(0,i.jsx)(u.A,{variant:"secondary",children:s("adminsetting.Expertise.Forms.Cancel")})})]})})]})})})})})};a()}catch(e){a(e)}})},45080:(e,t,s)=>{s.a(e,async(e,a)=>{try{s.r(t),s.d(t,{default:()=>g});var i=s(8732),r=s(82015),n=s(19918),l=s.n(n),d=s(12403),o=s(91353),c=s(42893),u=s(56084),m=s(63487),h=s(88751),x=e([c,m]);[c,m]=x.then?(await x)():x;let g=e=>{let{t}=(0,h.useTranslation)("common"),[s,a]=(0,r.useState)([]),[,n]=(0,r.useState)(!1),[x,g]=(0,r.useState)(0),[p,j]=(0,r.useState)(10),[A,y]=(0,r.useState)(!1),[f,v]=(0,r.useState)({}),w={sort:{title:"asc"},limit:p,page:1,query:{}},S=[{name:t("adminsetting.ProjectStatus.Title"),selector:"title"},{name:t("adminsetting.ProjectStatus.Action"),selector:"",cell:e=>(0,i.jsxs)("div",{children:[(0,i.jsx)(l(),{href:"/adminsettings/[...routes]",as:`/adminsettings/edit_projectstatus/${e._id}`,children:(0,i.jsx)("i",{className:"icon fas fa-edit"})}),"\xa0",(0,i.jsx)("a",{onClick:()=>C(e),children:(0,i.jsx)("i",{className:"icon fas fa-trash-alt"})})," "]})}],b=async()=>{n(!0);let e=await m.A.get("/projectstatus",w);e&&e.data&&e.data.length>0&&(a(e.data),g(e.totalCount),n(!1))},_=async(e,t)=>{w.limit=e,w.page=t,n(!0);let s=await m.A.get("/projectstatus",w);s&&s.data&&s.data.length>0&&(a(s.data),j(e),n(!1))},C=async e=>{v(e._id),y(!0)},T=async()=>{try{await m.A.remove(`/projectstatus/${f}`),b(),y(!1),c.default.success(t("adminsetting.ProjectStatus.Table.projectStatusDeletedSuccessfully"))}catch(e){c.default.error(t("adminsetting.ProjectStatus.Table.errorDeletingProjectStatus"))}},k=()=>y(!1);return(0,r.useEffect)(()=>{b()},[]),(0,i.jsxs)("div",{children:[(0,i.jsxs)(d.A,{show:A,onHide:k,children:[(0,i.jsx)(d.A.Header,{closeButton:!0,children:(0,i.jsx)(d.A.Title,{children:t("adminsetting.ProjectStatus.DeleteProjectstatus")})}),(0,i.jsx)(d.A.Body,{children:t("adminsetting.ProjectStatus.Areyousurewanttodeletethisprojectstatus")}),(0,i.jsxs)(d.A.Footer,{children:[(0,i.jsx)(o.A,{variant:"secondary",onClick:k,children:t("adminsetting.ProjectStatus.Cancel")}),(0,i.jsx)(o.A,{variant:"primary",onClick:T,children:t("adminsetting.ProjectStatus.Yes")})]})]}),(0,i.jsx)(u.A,{columns:S,data:s,totalRows:x,pagServer:!0,handlePerRowsChange:_,handlePageChange:e=>{w.limit=p,w.page=e,b()}})]})};a()}catch(e){a(e)}})},45962:(e,t,s)=>{s.a(e,async(e,a)=>{try{s.r(t),s.d(t,{default:()=>f,getStaticProps:()=>y});var i=s(8732),r=s(7082),n=s(83551),l=s(49481),d=s(91353),o=s(19918),c=s.n(o),u=s(27053),m=s(17098),h=s(88751),x=s(35576),g=s(45927),p=s(14062),j=s(35557),A=e([m,p]);async function y({locale:e}){return{props:{...await (0,x.serverSideTranslations)(e,["common"])}}}[m,p]=A.then?(await A)():A;let f=e=>{let{t}=(0,h.useTranslation)("common"),s=()=>(0,i.jsxs)(r.A,{style:{overflowX:"hidden"},fluid:!0,className:"p-0",children:[(0,i.jsx)(n.A,{children:(0,i.jsx)(l.A,{xs:12,children:(0,i.jsx)(u.A,{title:t("menu.hazards")})})}),(0,i.jsx)(n.A,{children:(0,i.jsx)(l.A,{xs:12,children:(0,i.jsx)(c(),{href:"/adminsettings/[...routes]",as:"/adminsettings/create_hazard",children:(0,i.jsx)(d.A,{variant:"secondary",size:"sm",children:t("addHazard")})})})}),(0,i.jsx)(n.A,{className:"mt-3",children:(0,i.jsx)(l.A,{xs:12,children:(0,i.jsx)(m.default,{})})})]}),a=(0,g.canAddHazards)(()=>(0,i.jsx)(s,{})),o=(0,p.useSelector)(e=>e);return o?.permissions?.hazard?.["create:any"]?(0,i.jsx)(a,{}):(0,i.jsx)(j.default,{})};a()}catch(e){a(e)}})},46621:(e,t,s)=>{s.a(e,async(e,a)=>{try{s.r(t),s.d(t,{default:()=>S});var i=s(8732),r=s(7082),n=s(18597),l=s(83551),d=s(49481),o=s(59549),c=s(91353),u=s(23579),m=s(66994),h=s(82015),x=s(42893),g=s(44233),p=s.n(g),j=s(19918),A=s.n(j),y=s(63487),f=s(88751),v=s(24047),w=e([x,y]);[x,y]=w.then?(await w)():w;let S=e=>{let t={title:"",code:"",description:""},{t:s}=(0,f.useTranslation)("common"),[a,g]=(0,h.useState)(t),j=e.routes&&"edit_syndrome"===e.routes[0]&&e.routes[1],w=async(t,i)=>{let r,n;t.preventDefault();let l={title:a.title.trim(),code:a.code,description:a.description};j?(n="adminsetting.syndrome.Syndromeisupdatedsuccessfully",r=await y.A.patch(`/syndrome/${e.routes[1]}`,l)):(n="adminsetting.syndrome.Syndromeisaddedsuccessfully",r=await y.A.post("/syndrome",l)),r&&r._id?(x.default.success(s(n)),p().push("/adminsettings/syndrome")):x.default.error(r)},S=e=>{if(e.target){let{name:t,value:s}=e.target;g(e=>({...e,[t]:s}))}},b=e=>{g(t=>({...t,description:e}))};(0,h.useEffect)(()=>{let t={query:{},sort:{title:"asc"},limit:"~"};j&&(async()=>{let s=await y.A.get(`/syndrome/${e.routes[1]}`,t);g(e=>({...e,...s}))})()},[]);let _=(0,h.useRef)(null);return(0,i.jsx)(r.A,{className:"formCard",fluid:!0,children:(0,i.jsx)(n.A,{style:{marginTop:"5px",boxShadow:"0 4px 8px 0 rgba(0, 0, 0, 0.2), 0 6px 20px 0 rgba(0, 0, 0, 0.19)"},children:(0,i.jsx)(m.A,{onSubmit:w,ref:_,initialValues:a,enableReinitialize:!0,children:(0,i.jsxs)(n.A.Body,{children:[(0,i.jsx)(l.A,{children:(0,i.jsx)(d.A,{children:(0,i.jsx)(n.A.Title,{children:j?s("adminsetting.syndrome.EditSyndrome"):s("adminsetting.syndrome.AddSyndrome")})})}),(0,i.jsx)("hr",{}),(0,i.jsxs)(l.A,{children:[(0,i.jsx)(d.A,{md:!0,lg:6,sm:12,children:(0,i.jsxs)(o.A.Group,{children:[(0,i.jsx)(o.A.Label,{className:"required-field",children:s("adminsetting.syndrome.SyndromeName")}),(0,i.jsx)(u.ks,{name:"title",id:"title",required:!0,value:a.title,validator:e=>""!==e.trim(),errorMessage:{validator:s("adminsetting.syndrome.PleaseAddtheSyndromeName")},onChange:S})]})}),(0,i.jsx)(d.A,{md:!0,lg:6,sm:12,children:(0,i.jsxs)(o.A.Group,{children:[(0,i.jsx)(o.A.Label,{children:s("adminsetting.syndrome.Code")}),(0,i.jsx)(u.ks,{name:"code",id:"code",required:!0,value:a.code,errorMessage:{validator:s("adminsetting.syndrome.PleaseAddtheCode")},onChange:S})]})})]}),(0,i.jsx)(l.A,{children:(0,i.jsx)(d.A,{children:(0,i.jsxs)(o.A.Group,{children:[(0,i.jsx)(o.A.Label,{children:s("adminsetting.syndrome.Description")}),(0,i.jsx)(v.x,{initContent:a.description,onChange:e=>b(e)})]})})}),(0,i.jsx)(l.A,{className:"my-4",children:(0,i.jsxs)(d.A,{children:[(0,i.jsx)(c.A,{className:"me-2",type:"submit",variant:"primary",children:s("adminsetting.syndrome.Submit")}),(0,i.jsx)(c.A,{className:"me-2",onClick:()=>{g(t),window.scrollTo(0,0)},variant:"info",children:s("adminsetting.syndrome.Reset")}),(0,i.jsx)(A(),{href:"/adminsettings/[...routes]",as:"/adminsettings/syndrome",children:(0,i.jsx)(c.A,{variant:"secondary",children:s("adminsetting.syndrome.Cancel")})})]})})]})})})})};a()}catch(e){a(e)}})},47818:(e,t,s)=>{s.a(e,async(e,a)=>{try{s.r(t),s.d(t,{default:()=>h});var i=s(8732),r=s(82015),n=s(91353),l=s(12403),d=s(42893),o=s(63487),c=s(56084),u=s(88751),m=e([d,o]);[d,o]=m.then?(await m)():m;let h=function(e){let{t}=(0,u.useTranslation)("common"),[s,a]=(0,r.useState)([]),[,m]=(0,r.useState)(!1),[h,x]=(0,r.useState)(0),[g,p]=(0,r.useState)(10),[j,A]=(0,r.useState)(!1),[y,f]=(0,r.useState)(""),[v,w]=(0,r.useState)({}),S={sort:{created_at:"desc"},limit:g,page:1,query:{vspace_status:"Request Pending"}},b=[{name:t("adminsetting.FocalPointsApprovalTable.Username"),selector:"username",cell:e=>e.username},{name:t("adminsetting.FocalPointsApprovalTable.Email"),selector:"email",cell:e=>e.email},{name:t("adminsetting.FocalPointsApprovalTable.Action"),selector:"",cell:e=>(0,i.jsxs)("div",{children:[(0,i.jsx)(n.A,{variant:"primary",size:"sm",onClick:()=>T(e,"approve"),children:t("adminsetting.FocalPointsApprovalTable.aprov")}),"\xa0",(0,i.jsx)(n.A,{variant:"secondary",size:"sm",onClick:()=>T(e,"reject"),children:t("adminsetting.FocalPointsApprovalTable.Reject")})]})}],_=async()=>{m(!0);let e=await o.A.get("/users",S);e&&e.data&&(a(e.data),x(e.totalCount),m(!1))},C=async(e,t)=>{S.limit=e,S.page=t,m(!0);let s=await o.A.get("/users",S);s&&s.data&&s.data.length>0&&(a(s.data),p(e),m(!1))},T=async(e,t)=>{A(!0),f(t),e&&e._id&&w({...e,vspace_status:"approve"===t?"Approved":"Rejected"})},k=async()=>{if("Rejected"===v.vspace_status)await o.A.remove(`/users/${v._id}`),_(),d.default.error(t("adminsetting.FocalPointsApprovalTable.Rejected")),w({}),A(!1);else{let e=await o.A.patch(`/users/${v._id}`,v);if(e&&403===e.status)return void d.default.error(e.response&&e.response.message?e.response.message:t("adminsetting.FocalPointsApprovalTable.Somethingwentswrong"));_(),d.default.success(t("adminsetting.FocalPointsApprovalTable.Approvemm")),w({}),A(!1)}},N=()=>A(!1);return(0,i.jsxs)("div",{children:[(0,i.jsxs)(l.A,{show:j,onHide:N,children:[(0,i.jsx)(l.A.Header,{closeButton:!0,children:(0,i.jsxs)(l.A.Title,{children:[y.charAt(0).toUpperCase()+y.slice(1)," ",t("adminsetting.FocalPointsApprovalTable.User")]})}),(0,i.jsxs)(l.A.Body,{children:[t("adminsetting.FocalPointsApprovalTable.Areyousurewantto")," ",y," ",t("adminsetting.FocalPointsApprovalTable.thisuser?")]}),(0,i.jsxs)(l.A.Footer,{children:[(0,i.jsx)(n.A,{variant:"secondary",onClick:N,children:t("adminsetting.FocalPointsApprovalTable.Cancel")}),(0,i.jsx)(n.A,{variant:"primary",onClick:k,children:t("adminsetting.FocalPointsApprovalTable.Yes")})]})]}),(0,i.jsx)(c.A,{columns:b,data:s,totalRows:h,pagServer:!0,handlePerRowsChange:C,handlePageChange:e=>{S.limit=g,S.page=e,_()}})]})};a()}catch(e){a(e)}})},50009:e=>{e.exports=require("@restart/ui/DropdownMenu")},51559:(e,t,s)=>{s.a(e,async(e,a)=>{try{s.r(t),s.d(t,{default:()=>A});var i=s(8732),r=s(7082),n=s(83551),l=s(49481),d=s(91353),o=s(19918),c=s.n(o),u=s(27053),m=s(45080),h=s(88751),x=s(45927),g=s(14062),p=s(35557),j=e([m,g]);[m,g]=j.then?(await j)():j;let A=e=>{let{t}=(0,h.useTranslation)("common"),s=()=>(0,i.jsx)("div",{children:(0,i.jsxs)(r.A,{style:{overflowX:"hidden"},fluid:!0,className:"p-0",children:[(0,i.jsx)(n.A,{children:(0,i.jsx)(l.A,{xs:12,children:(0,i.jsx)(u.A,{title:t("adminsetting.ProjectStatus.ProjectStatus")})})}),(0,i.jsx)(n.A,{children:(0,i.jsx)(l.A,{xs:12,children:(0,i.jsx)(c(),{href:"/adminsettings/[...routes]",as:"/adminsettings/create_projectstatus",children:(0,i.jsx)(d.A,{variant:"secondary",size:"sm",children:t("adminsetting.ProjectStatus.AddProjectStatus")})})})}),(0,i.jsx)(n.A,{className:"mt-3",children:(0,i.jsx)(l.A,{xs:12,children:(0,i.jsx)(m.default,{})})})]})}),a=(0,x.canAddProjectStatus)(()=>(0,i.jsx)(s,{})),o=(0,g.useSelector)(e=>e);return o?.permissions?.project_status?.["create:any"]?(0,i.jsx)(a,{}):(0,i.jsx)(p.default,{})};a()}catch(e){a(e)}})},51758:(e,t,s)=>{s.a(e,async(e,a)=>{try{s.r(t),s.d(t,{default:()=>p});var i=s(8732),r=s(82015),n=s(42893),l=s(19918),d=s.n(l),o=s(12403),c=s(91353),u=s(63487),m=s(56084),h=s(42404),x=s(88751),g=e([n,u,h]);[n,u,h]=g.then?(await g)():g;let p=e=>{let{t}=(0,x.useTranslation)("common"),[s,a]=(0,r.useState)([]),[,l]=(0,r.useState)(!1),[g,p]=(0,r.useState)(0),[j,A]=(0,r.useState)(10),[y,f]=(0,r.useState)(!1),[v,w]=(0,r.useState)({}),[S,b]=(0,r.useState)(""),_={sort:{title:"asc"},limit:j,page:1,query:{}},C=[{name:t("adminsetting.Regions.Region"),selector:e=>e.title,sortable:!0},{name:t("adminsetting.Regions.Country"),selector:e=>e.country?.title||"",sortable:!0,cell:e=>e.country&&e.country.title?e.country.title:""},{name:t("adminsetting.Regions.Action"),selector:e=>e._id,sortable:!1,cell:e=>(0,i.jsxs)("div",{children:[(0,i.jsx)(d(),{href:"/adminsettings/[...routes]",as:`/adminsettings/edit_region/${e._id}`,children:(0,i.jsx)("i",{className:"icon fas fa-edit"})}),"\xa0",(0,i.jsx)("a",{onClick:()=>N(e),children:(0,i.jsx)("i",{className:"icon fas fa-trash-alt"})})," "]})}],T=async()=>{l(!0);let e=await u.A.get("/region",_);e&&e.data&&(a(e.data),p(e.totalCount),l(!1))},k=async(e,t)=>{_.limit=e,_.page=t,S&&(_.query={country:S.value}),l(!0);let s=await u.A.get("/region",_);s&&s.data&&s.data.length>0&&(a(s.data),A(e),l(!1))},N=async e=>{w(e._id),f(!0)},R=async()=>{try{await u.A.remove(`/region/${v}`),T(),f(!1),n.default.success(t("adminsetting.Regions.Table.regionDeletedSuccessfully"))}catch(e){n.default.error(t("adminsetting.Regions.Table.errorDeletingRegion"))}},P=()=>f(!1);return(0,r.useEffect)(()=>{T()},[]),(0,r.useEffect)(()=>{S&&(_.query={country:S.value}),T()},[S]),(0,i.jsxs)("div",{className:"region__table",children:[(0,i.jsxs)(o.A,{show:y,onHide:P,children:[(0,i.jsx)(o.A.Header,{closeButton:!0,children:(0,i.jsx)(o.A.Title,{children:t("adminsetting.Regions.DeleteRegion")})}),(0,i.jsx)(o.A.Body,{children:t("adminsetting.Regions.Areyousurewanttodeletethisregion?")}),(0,i.jsxs)(o.A.Footer,{children:[(0,i.jsx)(c.A,{variant:"secondary",onClick:P,children:t("adminsetting.Regions.Cancel")}),(0,i.jsx)(c.A,{variant:"primary",onClick:R,children:t("adminsetting.Regions.Yes")})]})]}),(0,i.jsx)(h.default,{countryHandler:e=>{b(e)},value:S}),(0,i.jsx)(m.A,{columns:C,data:s,totalRows:g,pagServer:!0,handlePerRowsChange:k,handlePageChange:e=>{_.limit=j,_.page=e,S&&(_.query={country:S.value}),T()}})]})};a()}catch(e){a(e)}})},53120:(e,t,s)=>{s.a(e,async(e,a)=>{try{s.r(t),s.d(t,{default:()=>g});var i=s(8732),r=s(82015),n=s(19918),l=s.n(n),d=s(12403),o=s(91353),c=s(42893),u=s(88751),m=s(56084),h=s(63487),x=e([c,h]);[c,h]=x.then?(await x)():x;let g=e=>{let{t}=(0,u.useTranslation)("common"),[s,a]=(0,r.useState)([]),[,n]=(0,r.useState)(!1),[x,g]=(0,r.useState)(0),[p,j]=(0,r.useState)(10),[A,y]=(0,r.useState)(!1),[f,v]=(0,r.useState)({}),w={sort:{title:"asc"},limit:p,page:1,query:{}},S=[{name:t("adminsetting.EventStatus.Table.Title"),selector:"title"},{name:t("adminsetting.EventStatus.Table.Action"),selector:"",cell:e=>(0,i.jsxs)("div",{children:[(0,i.jsx)(l(),{href:"/adminsettings/[...routes]",as:`/adminsettings/edit_eventstatus/${e._id}`,children:(0,i.jsx)("i",{className:"icon fas fa-edit"})}),"\xa0",(0,i.jsx)("a",{onClick:()=>C(e),children:(0,i.jsx)("i",{className:"icon fas fa-trash-alt"})})]})}],b=async()=>{n(!0);let e=await h.A.get("/eventstatus",w);e&&e.data&&e.data.length>0&&(a(e.data),g(e.totalCount),n(!1))},_=async(e,t)=>{w.limit=e,w.page=t,n(!0);let s=await h.A.get("/eventstatus",w);s&&s.data&&s.data.length>0&&(a(s.data),j(e),n(!1))},C=async e=>{v(e._id),y(!0)},T=async()=>{try{await h.A.remove(`/eventstatus/${f}`),b(),y(!1),c.default.success(t("adminsetting.EventStatus.Table.eventStatusDeletedSuccessfully"))}catch(e){c.default.error(t("adminsetting.EventStatus.Table.errorDeletingEventStatus"))}},k=()=>y(!1);return(0,r.useEffect)(()=>{b()},[]),(0,i.jsxs)("div",{children:[(0,i.jsxs)(d.A,{show:A,onHide:k,children:[(0,i.jsx)(d.A.Header,{closeButton:!0,children:(0,i.jsx)(d.A.Title,{children:t("adminsetting.EventStatus.Table.DeleteEventstatus")})}),(0,i.jsx)(d.A.Body,{children:t("adminsetting.EventStatus.Table.Areyousurewanttodeletethiseventstatus?")}),(0,i.jsxs)(d.A.Footer,{children:[(0,i.jsx)(o.A,{variant:"secondary",onClick:k,children:t("adminsetting.EventStatus.Table.Cancel")}),(0,i.jsx)(o.A,{variant:"primary",onClick:T,children:t("adminsetting.EventStatus.Table.Yes")})]})]}),(0,i.jsx)(m.A,{columns:S,data:s,totalRows:x,pagServer:!0,handlePerRowsChange:_,handlePageChange:e=>{w.limit=p,w.page=e,b()}})]})};a()}catch(e){a(e)}})},54094:(e,t,s)=>{s.r(t),s.d(t,{default:()=>m});var a=s(8732),i=s(7082),r=s(83551),n=s(49481),l=s(98132),d=s(84517),o=s(89555),c=s(88751);let u=[{_id:"operation",title:"Operations"},{_id:"institution",title:"Organisations"},{_id:"event",title:"Events"},{_id:"project",title:"Projects"},{_id:"updates",title:"Updates"},{_id:"vspace",title:"Virtual Spaces"}],m=({filterText:e,onFilter:t,onFilterTypeChange:s,onClear:m,filterType:h})=>{let{t:x}=(0,c.useTranslation)("common");return(0,a.jsx)(i.A,{fluid:!0,className:"p-0",children:(0,a.jsxs)(r.A,{children:[(0,a.jsx)(n.A,{xs:6,md:4,className:"ps-0 align-self-end",children:(0,a.jsx)(l.A,{children:(0,a.jsx)(d.A,{type:"text",className:"searchInput",placeholder:x("adminsetting.content.table.Search"),"aria-label":"Search",value:e,onChange:t})})}),(0,a.jsx)(n.A,{xs:6,md:4,children:(0,a.jsxs)(l.A,{as:r.A,children:[(0,a.jsx)(o.A,{column:!0,sm:3,lg:2,className:"me-2",children:x("adminsetting.content.table.Type")}),(0,a.jsx)(n.A,{className:"ps-md-0",children:(0,a.jsx)(d.A,{as:"select","aria-label":"Type",onChange:e=>s(e),value:h,children:u.map((e,t)=>(0,a.jsx)("option",{value:e._id,children:e.title},t))})})]})})]})})}},54131:e=>{e.exports=import("@fortawesome/free-solid-svg-icons")},55807:(e,t,s)=>{s.a(e,async(e,a)=>{try{s.r(t),s.d(t,{default:()=>w});var i=s(8732),r=s(82015),n=s(7082),l=s(18597),d=s(83551),o=s(49481),c=s(59549),u=s(91353),m=s(23579),h=s(66994),x=s(44233),g=s.n(x),p=s(42893),j=s(19918),A=s.n(j),y=s(88751),f=s(63487),v=e([p,f]);[p,f]=v.then?(await v)():v;let w=e=>{let{t}=(0,y.useTranslation)("common"),s={title:""},[a,x]=(0,r.useState)(s),j=e.routes&&"edit_projectstatus"===e.routes[0]&&e.routes[1],v=(0,r.useRef)(null),w=async s=>{let i,r;s.preventDefault();let n={title:a.title.trim()};j?(r="toast.Projectstatusisupdatedsuccessfully",i=await f.A.patch(`/projectstatus/${e.routes[1]}`,n)):(r="toast.Projectstatusisaddedsuccessfully",i=await f.A.post("/projectstatus",n)),i&&i._id?(p.default.success(t(r)),g().push("/adminsettings/projectstatus")):i?.errorCode===11e3?p.default.error(t("duplicatesNotAllowed")):p.default.error(i)};return(0,r.useEffect)(()=>{let t={query:{},sort:{title:"asc"},limit:"~"};j&&(async()=>{let s=await f.A.get(`/projectstatus/${e.routes[1]}`,t);x(e=>({...e,...s}))})()},[]),(0,i.jsx)("div",{children:(0,i.jsx)(n.A,{className:"formCard",fluid:!0,children:(0,i.jsx)(l.A,{style:{marginTop:"5px",boxShadow:"0 4px 8px 0 rgba(0, 0, 0, 0.2), 0 6px 20px 0 rgba(0, 0, 0, 0.19)"},children:(0,i.jsx)(h.A,{onSubmit:w,ref:v,initialValues:a,enableReinitialize:!0,children:(0,i.jsxs)(l.A.Body,{children:[(0,i.jsx)(d.A,{children:(0,i.jsx)(o.A,{children:(0,i.jsx)(l.A.Title,{children:t("adminsetting.ProjectStatus.ProjectStatus")})})}),(0,i.jsx)("hr",{}),(0,i.jsx)(d.A,{children:(0,i.jsx)(o.A,{md:!0,lg:6,sm:12,children:(0,i.jsxs)(c.A.Group,{children:[(0,i.jsx)(c.A.Label,{className:"required-field",children:t("adminsetting.ProjectStatus.ProjectStatus")}),(0,i.jsx)(m.ks,{name:"title",id:"title",required:!0,value:a.title,validator:e=>""!==e.trim(),errorMessage:{validator:t("adminsetting.ProjectStatus.PleaseAddtheProjectStatus")},onChange:e=>{if(e.target){let{name:t,value:s}=e.target;x(e=>({...e,[t]:s}))}}})]})})}),(0,i.jsx)(d.A,{className:"my-4",children:(0,i.jsxs)(o.A,{children:[(0,i.jsx)(u.A,{className:"me-2",type:"submit",variant:"primary",children:t("adminsetting.ProjectStatus.Submit")}),(0,i.jsx)(u.A,{className:"me-2",onClick:()=>{x(s),window.scrollTo(0,0)},variant:"info",children:t("adminsetting.ProjectStatus.Reset")}),(0,i.jsx)(A(),{href:"/adminsettings/[...routes]",as:"/adminsettings/projectstatus",children:(0,i.jsx)(u.A,{variant:"secondary",children:t("adminsetting.ProjectStatus.Cancel")})})]})})]})})})})})};a()}catch(e){a(e)}})},57664:e=>{e.exports=require("dom-helpers/scrollbarSize")},58928:e=>{e.exports=require("dom-helpers/canUseDOM")},59481:(e,t,s)=>{s.a(e,async(e,a)=>{try{s.r(t),s.d(t,{default:()=>w});var i=s(8732),r=s(82015),n=s(7082),l=s(18597),d=s(83551),o=s(49481),c=s(59549),u=s(91353),m=s(23579),h=s(66994),x=s(44233),g=s.n(x),p=s(42893),j=s(19918),A=s.n(j),y=s(63487),f=s(88751),v=e([p,y]);[p,y]=v.then?(await v)():v;let w=e=>{let t={title:""},[s,a]=(0,r.useState)(t),x=e.routes&&"edit_institution_type"===e.routes[0]&&e.routes[1],{t:j}=(0,f.useTranslation)("common"),v=(0,r.useRef)(null),w=async t=>{let a,i;t.preventDefault();let r={title:s?.title?.trim()};x?(i="adminsetting.Organisationtypes.updatesuccess",a=await y.A.patch(`/institutiontype/${e.routes[1]}`,r)):(i="adminsetting.Organisationtypes.success",a=await y.A.post("/institutiontype",r)),a&&a._id?(p.default.success(j(i)),g().push("/adminsettings/institution_type")):a?.errorCode===11e3?p.default.error(j("duplicatesNotAllowed")):p.default.error(a)};return(0,r.useEffect)(()=>{let t={query:{},sort:{title:"asc"},limit:"~"};x&&(async()=>{let s=await y.A.get(`/institutiontype/${e.routes[1]}`,t);a(e=>({...e,...s}))})()},[]),(0,i.jsx)("div",{children:(0,i.jsx)(n.A,{className:"formCard",fluid:!0,children:(0,i.jsx)(l.A,{style:{marginTop:"5px",boxShadow:"0 4px 8px 0 rgba(0, 0, 0, 0.2), 0 6px 20px 0 rgba(0, 0, 0, 0.19)"},children:(0,i.jsx)(h.A,{onSubmit:w,ref:v,initialValues:s,enableReinitialize:!0,children:(0,i.jsxs)(l.A.Body,{children:[(0,i.jsx)(d.A,{children:(0,i.jsx)(o.A,{children:(0,i.jsx)(l.A.Title,{children:j("OrganisationType")})})}),(0,i.jsx)("hr",{}),(0,i.jsx)(d.A,{children:(0,i.jsx)(o.A,{md:!0,lg:6,sm:12,children:(0,i.jsxs)(c.A.Group,{children:[(0,i.jsx)(c.A.Label,{className:"required-field",children:j("OrganisationType")}),(0,i.jsx)(m.ks,{name:"title",id:"title",required:!0,value:s.title,validator:e=>""!==String(e||"").trim(),errorMessage:{validator:j("adminsetting.Organisationtypes.add")},onChange:e=>{if(e.target){let{name:t,value:s}=e.target;a(e=>({...e,[t]:s}))}}})]})})}),(0,i.jsx)(d.A,{className:"my-4",children:(0,i.jsxs)(o.A,{children:[(0,i.jsx)(u.A,{className:"me-2",type:"submit",variant:"primary",children:j("submit")}),(0,i.jsx)(u.A,{className:"me-2",onClick:()=>{a(t),window.scrollTo(0,0)},variant:"info",children:j("reset")}),(0,i.jsx)(A(),{href:"/adminsettings/[...routes]",as:"/adminsettings/institution_type",children:(0,i.jsx)(u.A,{variant:"secondary",children:j("Cancel")})})]})})]})})})})})};a()}catch(e){a(e)}})},59717:e=>{e.exports=require("@restart/ui/DropdownContext")},60560:e=>{e.exports=require("dom-helpers/contains")},60952:(e,t,s)=>{s.a(e,async(e,a)=>{try{s.r(t),s.d(t,{default:()=>g});var i=s(8732),r=s(82015),n=s(19918),l=s.n(n),d=s(12403),o=s(91353),c=s(42893),u=s(56084),m=s(63487),h=s(88751),x=e([c,m]);[c,m]=x.then?(await x)():x;let g=e=>{let[t,s]=(0,r.useState)([]),[,a]=(0,r.useState)(!1),[n,x]=(0,r.useState)(0),[g,p]=(0,r.useState)(10),[j,A]=(0,r.useState)(!1),[y,f]=(0,r.useState)({}),{t:v}=(0,h.useTranslation)("common"),w={sort:{title:"asc"},limit:g,page:1,query:{}},S=[{name:v("Title"),selector:"title"},{name:v("action"),selector:"",cell:e=>(0,i.jsxs)("div",{children:[(0,i.jsx)(l(),{href:"/adminsettings/[...routes]",as:`/adminsettings/edit_operationstatus/${e._id}`,children:(0,i.jsx)("i",{className:"icon fas fa-edit"})}),"\xa0",(0,i.jsx)("a",{onClick:()=>C(e),children:(0,i.jsx)("i",{className:"icon fas fa-trash-alt"})})," "]})}],b=async()=>{a(!0);let e=await m.A.get("/operation_status",w);e&&e.data&&e.data.length>0&&(s(e.data),x(e.totalCount),a(!1))},_=async(e,t)=>{w.limit=e,w.page=t,a(!0);let i=await m.A.get("/operation_status",w);i&&i.data&&i.data.length>0&&(s(i.data),p(e),a(!1))},C=async e=>{f(e._id),A(!0)},T=async()=>{try{await m.A.remove(`/operation_status/${y}`),b(),A(!1),c.default.success(v("adminsetting.OperationStatus.Table.opStatusDeletedSuccessfully"))}catch(e){c.default.error(v("adminsetting.OperationStatus.Table.errorDeletingOpStatus"))}},k=()=>A(!1);return(0,r.useEffect)(()=>{b()},[]),(0,i.jsxs)("div",{children:[(0,i.jsxs)(d.A,{show:j,onHide:k,children:[(0,i.jsx)(d.A.Header,{closeButton:!0,children:(0,i.jsx)(d.A.Title,{children:v("adminsetting.OperationStatus.Delete")})}),(0,i.jsx)(d.A.Body,{children:v("adminsetting.OperationStatus.sure")}),(0,i.jsxs)(d.A.Footer,{children:[(0,i.jsx)(o.A,{variant:"secondary",onClick:k,children:v("Cancel")}),(0,i.jsx)(o.A,{variant:"primary",onClick:T,children:v("yes")})]})]}),(0,i.jsx)(u.A,{columns:S,data:t,totalRows:n,pagServer:!0,handlePerRowsChange:_,handlePageChange:e=>{w.limit=g,w.page=e,b()}})]})};a()}catch(e){a(e)}})},62085:(e,t,s)=>{s.a(e,async(e,a)=>{try{s.r(t),s.d(t,{default:()=>k,getServerSideProps:()=>C});var i=s(8732),r=s(19918),n=s.n(r),l=s(27825),d=s.n(l),o=s(82015),c=s.n(o),u=s(74716),m=s.n(u),h=s(7082),x=s(83551),g=s(49481),p=s(12403),j=s(91353),A=s(42893),y=s(56084),f=s(63487),v=s(54094),w=s(27053),S=s(88751),b=s(35576),_=e([A,f]);[A,f]=_.then?(await _)():_;let T=(e,t)=>{switch(e){case"operation":t.select="-status -start_date -images -timeline -country -world_region -region -hazard -description -end_date -syndrome -hazard_type -partners -images -document -doc_src -images_src";break;case"institution":t.select="-partners -primary_focal_point -email -status -images -use_default_header -header -twitter -dial_code -telephone -department -unit -contact_name -description -type -networks -expertise -hazard_types -hazards -address -focal_points -website  -document -doc_src -images_src ";break;case"event":t.select="-images -more_info -date -risk_assessment -rki_monitored -officially_validated -laboratory_confirmed -status -syndrome -hazard -hazard_type -country_regions -world_region -country -operation -description -document -doc_src -images_src";break;case"project":t.select="-vspace_visibility -vspace -institution_invites -partner_institutions -area_of_work -region -country -end_date -start_date -status -funded_by -description -website";break;case"updates":t.select=" -region -country -end_date -start_date -status -category -contact_details -reply -show_as_announcement -technical_guidance -use_in_media_gallery -field_report -media -images -document -doc_src -images_src -location -link -description  ";break;case"vspace":t.select="-vspace_email_invite -file_category -subscribers -images -members -visibility -topic -end_date -start_date -description -images -document -doc_src -images_src -nonMembers -private_user_invite";break;default:t.select="-status -start_date -images -timeline -country -world_region -region -hazard -description -end_date -syndrome -hazard_type -partners"}return t};async function C({locale:e}){return{props:{...await (0,b.serverSideTranslations)(e,["common"])}}}let k=e=>{let[t,s]=(0,o.useState)([]),{t:a}=(0,S.useTranslation)("common"),[r,l]=(0,o.useState)("operation"),[u,b]=c().useState(!1),[_,C]=c().useState(""),[k,N]=(0,o.useState)(0),[R,P]=(0,o.useState)(25),[q,E]=(0,o.useState)(!1),[z,D]=(0,o.useState)(!1),[$,F]=(0,o.useState)(null),[H,B]=(0,o.useState)(1),[O,L]=(0,o.useState)(null),M={limit:R,sort:{created_at:"desc"}},G=[{name:a("adminsetting.content.table.Title"),selector:e=>e.title,cell:e=>{var t;return e.type?(0,i.jsx)(n(),{href:`/${e.type}/[...routes]`,as:`/${e.type}/show/${e[t=e,`parent_${t.type}`]}/${r}/${e._id}`,children:e.title}):(0,i.jsx)(n(),{href:`/${r}/[...routes]`,as:`/${r}/show/${e._id}`,children:e.title})},sortable:!0},{name:a("adminsetting.content.table.Author"),selector:e=>e.user?.username||"",cell:e=>e.user?e.user.username:"",sortable:!0},{name:a("adminsetting.content.table.Created"),selector:e=>e.created_at,cell:e=>m()(e.created_at).format("M/D/Y"),sortable:!0},{name:a("adminsetting.content.table.Updated"),selector:e=>e.updated_at,cell:e=>m()(e.updated_at).format("M/D/Y"),sortable:!0},{name:a("adminsetting.content.table.Action"),selector:e=>e._id,sortable:!1,cell:e=>(0,i.jsx)(i.Fragment,{children:(O?.roles?.includes("GENERAL_USER")||O?.roles?.includes("PLATFORM_ADMIN"))&&O?._id==e?.user?._id?(0,i.jsxs)("div",{children:[(0,i.jsx)(n(),{href:`/${r}/[...routes]`,as:`/${r}/edit/${e._id}`,children:(0,i.jsx)("i",{className:"icon fas fa-edit"})}),"\xa0",(0,i.jsx)(n(),{href:"#",onClick:t=>U(e,t),children:(0,i.jsx)("i",{className:"icon fas fa-trash-alt"})})]}):O?.roles?.includes("GENERAL_USER")||O?.roles?.includes("PLATFORM_ADMIN")?"":(0,i.jsxs)("div",{children:[(0,i.jsx)(n(),{href:`/${r}/[...routes]`,as:`/${r}/edit/${e._id}`,children:(0,i.jsx)("i",{className:"icon fas fa-edit"})}),"\xa0",(0,i.jsx)(n(),{href:"#",onClick:t=>U(e,t),children:(0,i.jsx)("i",{className:"icon fas fa-trash-alt"})})]})})}],I=async e=>{E(!0),e=T(r,e);let t=await f.A.post("/users/getLoggedUser",{});t&&t.username&&L(t);let a=await f.A.get(`/${r}`,e);a&&a.data&&Array.isArray(a.data)&&a.data.length>0?(s(a.data),N(a.totalCount||0)):s([]),E(!1)},U=async(e,t)=>{t.preventDefault(),F({id:e._id,type:r}),D(!0)},V=async(e,t)=>{E(!0),M.limit=e,M.page=t,B(t),M=T(r,M);let a=await f.A.get(`/${r}`,M);a&&Array.isArray(a.data)&&(s(a.data),P(e),E(!1))},W=()=>D(!1),X=async()=>{try{await f.A.remove(`/${$.type}/${$.id}`),M.page=H,I(M),D(!1),A.default.success(a("adminsetting.content.table.contentDeletedSuccessfully"))}catch(e){A.default.error(a("adminsetting.content.table.errorDeletingContent"))}};(0,o.useEffect)(()=>{I(M)},[r]);let Y=async(e,t)=>{E(!0),M.sort={[e.selector]:t},await I(M),E(!1)},K=c().useMemo(()=>{let e=e=>{l(e)},t=e=>{e&&(M.query={title:e}),I(M)},s=d().debounce(e=>t(e),Number("500")||300);return(0,i.jsx)(v.default,{onFilter:e=>{C(e.target.value),s(e.target.value)},onClear:()=>{_&&(b(!u),C(""))},filterText:_,onFilterTypeChange:t=>e(t.target.value),filterType:r})},[_,r,u]);return(0,i.jsxs)(h.A,{style:{overflowX:"hidden"},fluid:!0,className:"p-0",children:[(0,i.jsx)(x.A,{children:(0,i.jsx)(g.A,{xs:12,children:(0,i.jsx)(w.A,{title:a("adminsetting.content.table.content")})})}),(0,i.jsx)(x.A,{className:"mt-3",children:(0,i.jsxs)(g.A,{xs:12,children:[(0,i.jsxs)(p.A,{show:z,onHide:W,children:[(0,i.jsx)(p.A.Header,{closeButton:!0,children:(0,i.jsx)(p.A.Title,{children:a("adminsetting.content.table.DeleteContent")})}),(0,i.jsx)(p.A.Body,{children:a("adminsetting.content.table.Areyousurewanttodeletethiscontent?")}),(0,i.jsxs)(p.A.Footer,{children:[(0,i.jsx)(j.A,{variant:"secondary",onClick:W,children:a("adminsetting.content.table.Cancel")}),(0,i.jsx)(j.A,{variant:"primary",onClick:X,children:a("adminsetting.content.table.Yes")})]})]}),(0,i.jsx)(y.A,{columns:G,loading:q,data:t,totalRows:k,defaultRowsPerPage:R,subheader:!0,onSort:Y,sortServer:!0,pagServer:!0,subHeaderComponent:K,persistTableHead:!0,resetPaginationToggle:u,handlePerRowsChange:V,handlePageChange:e=>{M.page=e,""!==_&&(M.query={title:_}),B(e),I(M)}})]})})]})};a()}catch(e){a(e)}})},62969:(e,t,s)=>{s.a(e,async(e,a)=>{try{s.r(t),s.d(t,{default:()=>f,getServerSideProps:()=>y});var i=s(8732),r=s(7082),n=s(83551),l=s(49481),d=s(91353),o=s(19918),c=s.n(o),u=s(27053),m=s(79348),h=s(88751),x=s(35576),g=s(45927),p=s(14062),j=s(35557),A=e([m,p]);async function y({locale:e}){return{props:{...await (0,x.serverSideTranslations)(e,["common"])}}}[m,p]=A.then?(await A)():A;let f=e=>{let{t}=(0,h.useTranslation)("common"),s=()=>(0,i.jsxs)(r.A,{style:{overflowX:"hidden"},fluid:!0,className:"p-0",children:[(0,i.jsx)(n.A,{children:(0,i.jsx)(l.A,{xs:12,children:(0,i.jsx)(u.A,{title:t("adminsetting.syndrome.Syndromes")})})}),(0,i.jsx)(n.A,{children:(0,i.jsx)(l.A,{xs:12,children:(0,i.jsx)(c(),{href:"/adminsettings/[...routes]",as:"/adminsettings/create_syndrome",children:(0,i.jsx)(d.A,{variant:"secondary",size:"sm",children:t("adminsetting.syndrome.AddSyndrome")})})})}),(0,i.jsx)(n.A,{className:"mt-3",children:(0,i.jsx)(l.A,{xs:12,children:(0,i.jsx)(m.default,{})})})]}),a=(0,g.canAddSyndromes)(()=>(0,i.jsx)(s,{})),o=(0,p.useSelector)(e=>e);return o?.permissions?.syndrome?.["create:any"]?(0,i.jsx)(a,{}):(0,i.jsx)(j.default,{})};a()}catch(e){a(e)}})},63033:e=>{e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},65447:e=>{e.exports=require("@restart/ui/Button")},66116:(e,t,s)=>{s.a(e,async(e,a)=>{try{s.r(t),s.d(t,{default:()=>g});var i=s(8732),r=s(82015),n=s(19918),l=s.n(n),d=s(12403),o=s(91353),c=s(42893),u=s(56084),m=s(63487),h=s(88751),x=e([c,m]);[c,m]=x.then?(await x)():x;let g=e=>{let{t}=(0,h.useTranslation)("common"),[s,a]=(0,r.useState)([]),[,n]=(0,r.useState)(!1),[x,g]=(0,r.useState)(0),[p,j]=(0,r.useState)(10),[A,y]=(0,r.useState)(!1),[f,v]=(0,r.useState)({}),w={sort:{title:"asc"},limit:p,page:1,query:{}},S=[{name:t("adminsetting.worldregion.table.Title"),selector:e=>e.title,sortable:!0},{name:t("adminsetting.worldregion.table.Code"),selector:e=>e.code,sortable:!0,cell:e=>e.code},{name:t("adminsetting.worldregion.table.Action"),selector:e=>e._id,sortable:!1,cell:e=>(0,i.jsxs)("div",{children:[(0,i.jsx)(l(),{href:"/adminsettings/[...routes]",as:`/adminsettings/edit_worldregion/${e._id}`,children:(0,i.jsx)("i",{className:"icon fas fa-edit"})}),"\xa0",(0,i.jsx)("a",{onClick:()=>C(e),children:(0,i.jsx)("i",{className:"icon fas fa-trash-alt"})})," "]})}],b=async()=>{n(!0);let e=await m.A.get("/worldregion",w);e&&e.data&&e.data.length>0&&(a(e.data),g(e.totalCount),n(!1))},_=async(e,t)=>{w.limit=e,w.page=t,n(!0);let s=await m.A.get("/worldregion",w);s&&s.data&&s.data.length>0&&(a(s.data),j(e),n(!1))},C=async e=>{v(e._id),y(!0)},T=async()=>{try{await m.A.remove(`/worldregion/${f}`),b(),y(!1),c.default.success(t("adminsetting.worldregion.table.worldRegionDeletedSuccessfully"))}catch(e){c.default.error(t("adminsetting.worldregion.table.errorDeletingWorldRegion"))}},k=()=>y(!1);return(0,r.useEffect)(()=>{b()},[]),(0,i.jsxs)("div",{children:[(0,i.jsxs)(d.A,{show:A,onHide:k,children:[(0,i.jsx)(d.A.Header,{closeButton:!0,children:(0,i.jsx)(d.A.Title,{children:t("adminsetting.worldregion.table.DeleteWorldregion")})}),(0,i.jsx)(d.A.Body,{children:t("adminsetting.worldregion.table.Areyousurewanttodeletethisworldregion?")}),(0,i.jsxs)(d.A.Footer,{children:[(0,i.jsx)(o.A,{variant:"secondary",onClick:k,children:t("adminsetting.worldregion.table.Cancel")}),(0,i.jsx)(o.A,{variant:"primary",onClick:T,children:t("adminsetting.worldregion.table.Yes")})]})]}),(0,i.jsx)(u.A,{columns:S,data:s,totalRows:x,pagServer:!0,handlePerRowsChange:_,handlePageChange:e=>{w.limit=p,w.page=e,b()}})]})};a()}catch(e){a(e)}})},67364:e=>{e.exports=require("@restart/hooks/useCallbackRef")},68455:e=>{e.exports=import("redux-saga")},68963:(e,t,s)=>{s.a(e,async(e,a)=>{try{s.r(t),s.d(t,{default:()=>w});var i=s(8732),r=s(82015),n=s(7082),l=s(18597),d=s(83551),o=s(49481),c=s(59549),u=s(91353),m=s(23579),h=s(66994),x=s(44233),g=s.n(x),p=s(42893),j=s(19918),A=s.n(j),y=s(88751),f=s(63487),v=e([p,f]);[p,f]=v.then?(await v)():v;let w=e=>{let t={_id:"",title:""},{t:s}=(0,y.useTranslation)("common"),[a,x]=(0,r.useState)(t),j=e.routes&&"edit_deploymentstatus"===e.routes[0]&&e.routes[1],v=(0,r.useRef)(null),w=async t=>{let i,r;t.preventDefault();let n={title:a.title.trim()};j?(r="adminsetting.DeploymentStatus.Forms.DeploymentstatusisUpdatedsuccessfully",i=await f.A.patch(`/deploymentstatus/${e.routes[1]}`,n)):(r="adminsetting.DeploymentStatus.Forms.Deploymentstatusisaddedsuccessfully",i=await f.A.post("/deploymentstatus",n)),i&&i._id?(p.default.success(s(r)),g().push("/adminsettings/deploymentstatus")):i?.errorCode===11e3?p.default.error(s("duplicatesNotAllowed")):p.default.error(i)};return(0,r.useEffect)(()=>{let t={query:{},sort:{title:"asc"},limit:"~"};j&&(async()=>{let s=await f.A.get(`/deploymentstatus/${e.routes[1]}`,t);x(e=>({...e,...s}))})()},[]),(0,i.jsx)("div",{children:(0,i.jsx)(n.A,{className:"formCard",fluid:!0,children:(0,i.jsx)(l.A,{style:{marginTop:"5px",boxShadow:"0 4px 8px 0 rgba(0, 0, 0, 0.2), 0 6px 20px 0 rgba(0, 0, 0, 0.19)"},children:(0,i.jsx)(h.A,{onSubmit:w,ref:v,initialValues:a,enableReinitialize:!0,children:(0,i.jsxs)(l.A.Body,{children:[(0,i.jsx)(d.A,{children:(0,i.jsx)(o.A,{children:(0,i.jsx)(l.A.Title,{children:s("adminsetting.DeploymentStatus.Forms.DeploymentStatus")})})}),(0,i.jsx)("hr",{}),(0,i.jsx)(d.A,{children:(0,i.jsx)(o.A,{md:!0,lg:6,sm:12,children:(0,i.jsxs)(c.A.Group,{children:[(0,i.jsx)(c.A.Label,{className:"required-field",children:s("adminsetting.DeploymentStatus.Forms.DeploymentStatus")}),(0,i.jsx)(m.ks,{name:"title",id:"title",required:!0,value:a.title,validator:e=>""!==e.trim(),errorMessage:{validator:s("adminsetting.DeploymentStatus.Forms.PleaseAddtheDeploymentstatus")},onChange:e=>{if(e.target){let{name:t,value:s}=e.target;x(e=>({...e,[t]:s}))}}})]})})}),(0,i.jsx)(d.A,{className:"my-4",children:(0,i.jsxs)(o.A,{children:[(0,i.jsx)(u.A,{className:"me-2",type:"submit",variant:"primary",children:s("adminsetting.DeploymentStatus.Forms.Submit")}),(0,i.jsx)(u.A,{className:"me-2",onClick:()=>{x(t),window.scrollTo(0,0)},variant:"info",children:s("adminsetting.DeploymentStatus.Forms.Reset")}),(0,i.jsx)(A(),{href:"/adminsettings/[...routes]",as:"/adminsettings/deploymentstatus",children:(0,i.jsx)(u.A,{variant:"secondary",children:s("adminsetting.DeploymentStatus.Forms.Cancel")})})]})})]})})})})})};a()}catch(e){a(e)}})},69301:(e,t,s)=>{s.a(e,async(e,a)=>{try{s.r(t),s.d(t,{default:()=>g});var i=s(8732),r=s(7082),n=s(83551),l=s(49481),d=s(91353),o=s(19918),c=s.n(o),u=s(27053),m=s(26045),h=s(88751),x=e([m]);m=(x.then?(await x)():x)[0];let g=e=>{let{t}=(0,h.useTranslation)("common");return(0,i.jsx)("div",{children:(0,i.jsxs)(r.A,{style:{overflowX:"hidden"},fluid:!0,className:"p-0",children:[(0,i.jsx)(n.A,{children:(0,i.jsx)(l.A,{xs:12,children:(0,i.jsx)(u.A,{title:"Roles"})})}),(0,i.jsx)(n.A,{children:(0,i.jsx)(l.A,{xs:12,children:(0,i.jsx)(c(),{href:"/adminsettings/[...routes]",as:"/adminsettings/create_role",children:(0,i.jsx)(d.A,{variant:"secondary",size:"sm",children:t("AddRole")})})})}),(0,i.jsx)(n.A,{className:"mt-3",children:(0,i.jsx)(l.A,{xs:12,children:(0,i.jsx)(m.default,{})})})]})})};a()}catch(e){a(e)}})},69722:e=>{e.exports=require("es6-promise")},72685:(e,t,s)=>{s.a(e,async(e,a)=>{try{s.r(t),s.d(t,{default:()=>g,getServerSideProps:()=>x});var i=s(8732),r=s(7082),n=s(28842),l=s(27053),d=s(88751),o=s(35576),c=s(45927),u=s(14062),m=s(35557),h=e([n,u]);async function x({locale:e}){return{props:{...await (0,o.serverSideTranslations)(e,["common"])}}}[n,u]=h.then?(await h)():h;let g=e=>{let{t}=(0,d.useTranslation)("common"),s=()=>(0,i.jsxs)(r.A,{fluid:!0,className:"p-0",children:[(0,i.jsx)(l.A,{title:t("adminsetting.FocalPointsApproval")}),(0,i.jsx)(n.default,{})]}),a=(0,c.canAddFocalPointApproval)(()=>(0,i.jsx)(s,{})),o=(0,u.useSelector)(e=>e);return o?.permissions?.institution_focal_point?.["create:any"]?(0,i.jsx)(a,{}):(0,i.jsx)(m.default,{})};a()}catch(e){a(e)}})},72922:(e,t,s)=>{s.a(e,async(e,a)=>{try{s.r(t),s.d(t,{default:()=>A});var i=s(8732),r=s(7082),n=s(83551),l=s(49481),d=s(91353),o=s(19918),c=s.n(o),u=s(27053),m=s(53120),h=s(88751),x=s(45927),g=s(14062),p=s(35557),j=e([m,g]);[m,g]=j.then?(await j)():j;let A=e=>{let{t}=(0,h.useTranslation)("common"),s=()=>(0,i.jsx)("div",{children:(0,i.jsxs)(r.A,{style:{overflowX:"hidden"},fluid:!0,className:"p-0",children:[(0,i.jsx)(n.A,{children:(0,i.jsx)(l.A,{xs:12,children:(0,i.jsx)(u.A,{title:t("adminsetting.EventStatus.Forms.AddEventStatus")})})}),(0,i.jsx)(n.A,{children:(0,i.jsx)(l.A,{xs:12,children:(0,i.jsx)(c(),{href:"/adminsettings/[...routes]",as:"/adminsettings/create_eventstatus",children:(0,i.jsx)(d.A,{variant:"secondary",size:"sm",children:t("adminsetting.EventStatus.Forms.AddEventStatus")})})})}),(0,i.jsx)(n.A,{className:"mt-3",children:(0,i.jsx)(l.A,{xs:12,children:(0,i.jsx)(m.default,{})})})]})}),a=(0,x.canAddEventStatus)(()=>(0,i.jsx)(s,{})),o=(0,g.useSelector)(e=>e);return o?.permissions?.event_status?.["create:any"]?(0,i.jsx)(a,{}):(0,i.jsx)(p.default,{})};a()}catch(e){a(e)}})},73407:(e,t,s)=>{s.a(e,async(e,a)=>{try{s.r(t),s.d(t,{default:()=>w});var i=s(8732),r=s(82015),n=s(7082),l=s(18597),d=s(83551),o=s(49481),c=s(59549),u=s(91353),m=s(23579),h=s(66994),x=s(44233),g=s.n(x),p=s(42893),j=s(19918),A=s.n(j),y=s(63487),f=s(88751),v=e([p,y]);[p,y]=v.then?(await v)():v;let w=e=>{let t={title:""},[s,a]=(0,r.useState)(t),x=e.routes&&"edit_operationstatus"===e.routes[0]&&e.routes[1],{t:j}=(0,f.useTranslation)("common"),v=(0,r.useRef)(null),w=async t=>{let a,i;t.preventDefault();let r={title:s.title.trim()};x?(i="adminsetting.OperationStatus.updatesuccess",a=await y.A.patch(`/operation_status/${e.routes[1]}`,r)):(i="adminsetting.OperationStatus.add",a=await y.A.post("/operation_status",r)),a&&a._id?(p.default.success(j(i)),g().push("/adminsettings/operationstatus")):a?.errorCode===11e3?p.default.error(j("duplicatesNotAllowed")):p.default.error(a)};return(0,r.useEffect)(()=>{let t={query:{},sort:{title:"asc"},limit:"~"};x&&(async()=>{let s=await y.A.get(`/operation_status/${e.routes[1]}`,t);a(e=>({...e,...s}))})()},[]),(0,i.jsx)("div",{children:(0,i.jsx)(n.A,{className:"formCard",fluid:!0,children:(0,i.jsx)(l.A,{style:{marginTop:"5px",boxShadow:"0 4px 8px 0 rgba(0, 0, 0, 0.2), 0 6px 20px 0 rgba(0, 0, 0, 0.19)"},children:(0,i.jsx)(h.A,{onSubmit:w,ref:v,initialValues:s,enableReinitialize:!0,children:(0,i.jsxs)(l.A.Body,{children:[(0,i.jsx)(d.A,{children:(0,i.jsx)(o.A,{children:(0,i.jsx)(l.A.Title,{children:j("OperationStatus")})})}),(0,i.jsx)("hr",{}),(0,i.jsx)(d.A,{children:(0,i.jsx)(o.A,{md:!0,lg:6,sm:12,children:(0,i.jsxs)(c.A.Group,{children:[(0,i.jsx)(c.A.Label,{className:"required-field",children:j("OperationStatus")}),(0,i.jsx)(m.ks,{name:"title",id:"title",required:!0,value:s.title,validator:e=>""!==String(e||"").trim(),errorMessage:{validator:j("adminsetting.OperationStatus.please")},onChange:e=>{if(e.target){let{name:t,value:s}=e.target;a(e=>({...e,[t]:s}))}}})]})})}),(0,i.jsx)(d.A,{className:"my-4",children:(0,i.jsxs)(o.A,{children:[(0,i.jsx)(u.A,{className:"me-2",type:"submit",variant:"primary",children:j("submit")}),(0,i.jsx)(u.A,{className:"me-2",onClick:()=>{a(t),window.scrollTo(0,0)},variant:"info",children:j("reset")}),(0,i.jsx)(A(),{href:"/adminsettings/[...routes]",as:"/adminsettings/operationstatus",children:(0,i.jsx)(u.A,{variant:"secondary",children:j("Cancel")})})]})})]})})})})})};a()}catch(e){a(e)}})},73462:(e,t,s)=>{s.r(t),s.d(t,{default:()=>o});var a=s(8732),i=s(7082),r=s(83551),n=s(49481),l=s(84517),d=s(88751);let o=({filterText:e,onFilter:t,onClear:s})=>{let{t:o}=(0,d.useTranslation)("common");return(0,a.jsx)(i.A,{fluid:!0,className:"p-0",children:(0,a.jsx)(r.A,{children:(0,a.jsx)(n.A,{md:4,className:"p-0",children:(0,a.jsx)(l.A,{type:"text",className:"searchInput",placeholder:o("adminsetting.Countries.Forms.Search"),"aria-label":"Search",value:e,onChange:t})})})})}},74075:e=>{e.exports=require("zlib")},74716:e=>{e.exports=require("moment")},74987:e=>{e.exports=require("@restart/ui/ModalManager")},76167:(e,t,s)=>{s.a(e,async(e,a)=>{try{s.r(t),s.d(t,{default:()=>A});var i=s(8732),r=s(7082),n=s(83551),l=s(49481),d=s(91353),o=s(19918),c=s.n(o),u=s(27053),m=s(60952),h=s(88751),x=s(45927),g=s(14062),p=s(35557),j=e([m,g]);[m,g]=j.then?(await j)():j;let A=e=>{let{t}=(0,h.useTranslation)("common"),s=()=>(0,i.jsx)("div",{children:(0,i.jsxs)(r.A,{style:{overflowX:"hidden"},fluid:!0,className:"p-0",children:[(0,i.jsx)(n.A,{children:(0,i.jsx)(l.A,{xs:12,children:(0,i.jsx)(u.A,{title:t("adminsetting.OperationStatus.Operationstatus")})})}),(0,i.jsx)(n.A,{children:(0,i.jsx)(l.A,{xs:12,children:(0,i.jsx)(c(),{href:"/adminsettings/[...routes]",as:"/adminsettings/create_operationstatus",children:(0,i.jsx)(d.A,{variant:"secondary",size:"sm",children:t("adminsetting.OperationStatus.AddOperationstatus")})})})}),(0,i.jsx)(n.A,{className:"mt-3",children:(0,i.jsx)(l.A,{xs:12,children:(0,i.jsx)(m.default,{})})})]})}),a=(0,x.canAddOperationStatus)(()=>(0,i.jsx)(s,{})),o=(0,g.useSelector)(e=>e);return o?.permissions?.operation_status?.["create:any"]?(0,i.jsx)(a,{}):(0,i.jsx)(p.default,{})};a()}catch(e){a(e)}})},78097:e=>{e.exports=require("next-redux-saga")},78634:e=>{e.exports=require("@restart/ui/Anchor")},78929:(e,t,s)=>{s.a(e,async(e,a)=>{try{s.r(t),s.d(t,{default:()=>ee,getServerSideProps:()=>Z});var i=s(8732),r=s(44233),n=s(72685),l=s(17675),d=s(1823),o=s(24138),c=s(67032),u=s(27232),m=s(28690),h=s(45962),x=s(30009),g=s(44457),p=s(27406),j=s(30103),A=s(11768),y=s(4439),f=s(80760),v=s(62969),w=s(46621),S=s(87470),b=s(43252),_=s(43619),C=s(68963),T=s(89835),k=s(44971),N=s(20143),R=s(83623),P=s(69301),q=s(90785),E=s(62085),z=s(98630),D=s(36228),$=s(92774),F=s(17101),H=s(59481),B=s(29853),O=s(32777),L=s(24296),M=s(72922),G=s(76167),I=s(73407),U=s(51559),V=s(55807),W=s(83929),X=s(71102),Y=s(4710),K=s(32900),J=s(35576),Q=e([n,l,d,o,c,u,m,h,x,g,p,j,A,y,f,v,w,S,b,_,C,T,k,N,R,P,q,E,z,D,$,F,H,B,O,L,M,G,I,U,V,W,X,Y,K]);async function Z({locale:e}){return{props:{...await (0,J.serverSideTranslations)(e,["common"])}}}[n,l,d,o,c,u,m,h,x,g,p,j,A,y,f,v,w,S,b,_,C,T,k,N,R,P,q,E,z,D,$,F,H,B,O,L,M,G,I,U,V,W,X,Y,K]=Q.then?(await Q)():Q;let ee=()=>{let e=(0,r.useRouter)().query.routes||[];switch(e[0]){case"focal_point":return(0,i.jsx)(n.default,{routes:e});case"Vspace_point":return(0,i.jsx)(l.default,{routes:e});case"institution_approval":return(0,i.jsx)(d.default,{routes:e});case"country":return(0,i.jsx)(o.default,{routes:e});case"create_country":case"edit_country":return(0,i.jsx)(c.default,{routes:e});case"hazard":return(0,i.jsx)(h.default,{routes:e});case"create_hazard":case"edit_hazard":return(0,i.jsx)(x.default,{routes:e});case"hazardTypes":return(0,i.jsx)(g.default,{routes:e});case"create_hazard_types":case"edit_hazard_types":return(0,i.jsx)(p.default,{routes:e});case"region":return(0,i.jsx)(m.default,{routes:e});case"create_region":case"edit_region":return(0,i.jsx)(u.default,{routes:e});case"update_type":return(0,i.jsx)(j.default,{routes:e});case"create_update_type":case"edit_update_type":return(0,i.jsx)(A.default,{routes:e});case"area_of_work":return(0,i.jsx)(y.default,{routes:e});case"create_area_of_work":case"edit_area_of_work":return(0,i.jsx)(f.default,{routes:e});case"syndrome":return(0,i.jsx)(v.default,{routes:e});case"create_syndrome":case"edit_syndrome":return(0,i.jsx)(w.default,{routes:e});case"landing":return(0,i.jsx)(K.default,{routes:e});case"create_landing":case"edit_landing":return(0,i.jsx)(Y.default,{routes:e});case"category":return(0,i.jsx)(S.default,{routes:e});case"create_category":case"edit_category":return(0,i.jsx)(b.default,{routes:e});case"deploymentstatus":return(0,i.jsx)(_.default,{routes:e});case"create_deploymentstatus":case"edit_deploymentstatus":return(0,i.jsx)(C.default,{routes:e});case"eventstatus":return(0,i.jsx)(M.default,{routes:e});case"create_eventstatus":case"edit_eventstatus":return(0,i.jsx)(L.default,{routes:e});case"operationstatus":return(0,i.jsx)(G.default,{routes:e});case"create_operationstatus":case"edit_operationstatus":return(0,i.jsx)(I.default,{routes:e});case"projectstatus":return(0,i.jsx)(U.default,{routes:e});case"create_projectstatus":case"edit_projectstatus":return(0,i.jsx)(V.default,{routes:e});case"expertise":return(0,i.jsx)(T.default,{routes:e});case"create_expertise":case"edit_expertise":return(0,i.jsx)(k.default,{routes:e});case"risklevel":return(0,i.jsx)(N.default,{routes:e});case"create_risklevel":case"edit_risklevel":return(0,i.jsx)(R.default,{routes:e});case"role":return(0,i.jsx)(P.default,{routes:e});case"create_role":case"edit_role":return(0,i.jsx)(q.default,{routes:e});case"users":return(0,i.jsx)(W.default,{routes:e});case"create_user":case"edit_user":return(0,i.jsx)(X.default,{routes:e});case"smtp":return(0,i.jsx)(z.default,{routes:e});case"worldregion":return(0,i.jsx)(D.default,{routes:e});case"create_worldregion":case"edit_worldregion":return(0,i.jsx)($.default,{routes:e});case"institution_type":return(0,i.jsx)(F.default,{routes:e});case"create_institution_type":case"edit_institution_type":return(0,i.jsx)(H.default,{routes:e});case"institution_network":return(0,i.jsx)(B.default,{routes:e});case"create_institution_network":case"edit_institution_network":return(0,i.jsx)(O.default,{routes:e});case"content":return(0,i.jsx)(E.default,{routes:e});default:return null}};a()}catch(e){a(e)}})},79348:(e,t,s)=>{s.a(e,async(e,a)=>{try{s.r(t),s.d(t,{default:()=>g});var i=s(8732),r=s(19918),n=s.n(r),l=s(82015),d=s(12403),o=s(91353),c=s(42893),u=s(56084),m=s(63487),h=s(88751),x=e([c,m]);[c,m]=x.then?(await x)():x;let g=e=>{let{t}=(0,h.useTranslation)("common"),[s,a]=(0,l.useState)([]),[,r]=(0,l.useState)(!1),[x,g]=(0,l.useState)(0),[p,j]=(0,l.useState)(10),[A,y]=(0,l.useState)(!1),[f,v]=(0,l.useState)({}),w=[{name:t("adminsetting.syndrome.Syndromes"),selector:"title"},{name:t("adminsetting.syndrome.Code"),selector:"code",cell:e=>e.code},{name:t("adminsetting.syndrome.Description"),selector:"description",cell:e=>e.description.replace(/<[^>]+>/g,"")},{name:t("adminsetting.syndrome.Action"),selector:"",cell:e=>(0,i.jsxs)("div",{children:[(0,i.jsx)(n(),{href:"/adminsettings/[...routes]",as:`/adminsettings/edit_syndrome/${e._id}`,children:(0,i.jsx)("i",{className:"icon fas fa-edit"})}),"\xa0",(0,i.jsx)("a",{onClick:()=>C(e),children:(0,i.jsx)("i",{className:"icon fas fa-trash-alt"})})," "]})}];(0,l.useEffect)(()=>{b()},[]);let S={sort:{title:"asc"},limit:p,page:1,query:{}},b=async()=>{r(!0);let e=await m.A.get("/syndrome",S);e&&e.data&&e.data.length>0&&(a(e.data),g(e.totalCount),r(!1))},_=async(e,t)=>{S.limit=e,S.page=t,r(!0);let s=await m.A.get("/syndrome",S);s&&s.data&&s.data.length>0&&(a(s.data),j(e),r(!1))},C=async e=>{v(e._id),y(!0)},T=async()=>{try{await m.A.remove(`/syndrome/${f}`),b(),y(!1),c.default.success(t("adminsetting.syndrome.Table.syndromeDeletedSuccessfully"))}catch(e){c.default.error(t("adminsetting.syndrome.Table.errorDeletingSyndrome"))}},k=()=>y(!1);return(0,i.jsxs)("div",{children:[(0,i.jsxs)(d.A,{show:A,onHide:k,children:[(0,i.jsx)(d.A.Header,{closeButton:!0,children:(0,i.jsx)(d.A.Title,{children:t("adminsetting.syndrome.Deletesyndrome")})}),(0,i.jsx)(d.A.Body,{children:t("adminsetting.syndrome.Areyousurewanttodeletethissyndrome?")}),(0,i.jsxs)(d.A.Footer,{children:[(0,i.jsx)(o.A,{variant:"secondary",onClick:k,children:t("adminsetting.syndrome.Cancel")}),(0,i.jsx)(o.A,{variant:"primary",onClick:T,children:t("adminsetting.syndrome.Yes")})]})]}),(0,i.jsx)(u.A,{columns:w,data:s,totalRows:x,pagServer:!0,handlePerRowsChange:_,handlePageChange:e=>{S.limit=p,S.page=e,b()}})]})};a()}catch(e){a(e)}})},80760:(e,t,s)=>{s.a(e,async(e,a)=>{try{s.r(t),s.d(t,{default:()=>w});var i=s(8732),r=s(82015),n=s(7082),l=s(18597),d=s(83551),o=s(49481),c=s(59549),u=s(91353),m=s(66994),h=s(23579),x=s(44233),g=s.n(x),p=s(42893),j=s(19918),A=s.n(j),y=s(63487),f=s(88751),v=e([p,y]);[p,y]=v.then?(await v)():v;let w=e=>{let{t}=(0,f.useTranslation)("common"),s={_id:"",title:""},[a,x]=(0,r.useState)(s),j=!!(e.routes&&"edit_area_of_work"===e.routes[0]&&e.routes[1]),v=(0,r.useRef)(null),w=async s=>{let i,r;s.preventDefault();let n={title:a.title.trim()};j?(r="Areaofworkisupdatedsuccessfully",i=await y.A.patch(`/areaofwork/${e.routes[1]}`,n)):(r="Areaofworkisaddedsuccessfully",i=await y.A.post("/areaofwork",n)),i&&i._id?(p.default.success(t(r)),g().push("/adminsettings/area_of_work")):i?.errorCode===11e3?p.default.error(t("duplicatesNotAllowed")):p.default.error(i)};return(0,r.useEffect)(()=>{let t={query:{},sort:{title:"asc"},limit:"~"};j&&(async()=>{let s=await y.A.get(`/areaofwork/${e.routes[1]}`,t);x(e=>({...e,...s}))})()},[]),(0,i.jsx)("div",{children:(0,i.jsx)(n.A,{className:"formCard",fluid:!0,children:(0,i.jsx)(l.A,{style:{marginTop:"5px",boxShadow:"0 4px 8px 0 rgba(0, 0, 0, 0.2), 0 6px 20px 0 rgba(0, 0, 0, 0.19)"},children:(0,i.jsx)(m.A,{onSubmit:w,ref:v,initialValues:a,enableReinitialize:!0,children:(0,i.jsxs)(l.A.Body,{children:[(0,i.jsx)(d.A,{children:(0,i.jsx)(o.A,{children:(0,i.jsx)(l.A.Title,{children:t("adminsetting.areaofwork.Forms.AreaOfWork")})})}),(0,i.jsx)("hr",{}),(0,i.jsx)(d.A,{children:(0,i.jsx)(o.A,{md:!0,lg:6,sm:12,children:(0,i.jsxs)(c.A.Group,{children:[(0,i.jsx)(c.A.Label,{className:"required-field",children:t("adminsetting.areaofwork.Forms.AreaOfWork")}),(0,i.jsx)(h.ks,{name:"title",id:"title",required:!0,value:a.title,validator:e=>""!==String(e||"").trim(),errorMessage:{validator:t("adminsetting.areaofwork.Forms.PleaseAddtheAreaofWork")},onChange:e=>{if(e.target){let{name:t,value:s}=e.target;x(e=>({...e,[t]:s}))}}})]})})}),(0,i.jsx)(d.A,{className:"my-4",children:(0,i.jsxs)(o.A,{children:[(0,i.jsx)(u.A,{className:"me-2",type:"submit",variant:"primary",children:t("adminsetting.areaofwork.Forms.Submit")}),(0,i.jsx)(u.A,{className:"me-2",onClick:()=>{x(s),window.scrollTo(0,0)},variant:"info",children:t("adminsetting.areaofwork.Forms.Reset")}),(0,i.jsx)(A(),{href:"/adminsettings/[...routes]",as:"/adminsettings/area_of_work",children:(0,i.jsx)(u.A,{variant:"secondary",children:t("adminsetting.areaofwork.Forms.Cancel")})})]})})]})})})})})};a()}catch(e){a(e)}})},81366:e=>{e.exports=require("redux-auth-wrapper/connectedAuthWrapper")},81521:e=>{e.exports=require("dom-helpers/querySelectorAll")},81895:e=>{e.exports=require("@restart/hooks/useEventCallback")},82015:e=>{e.exports=require("react")},82053:e=>{e.exports=require("@fortawesome/react-fontawesome")},83616:(e,t,s)=>{s.a(e,async(e,a)=>{try{s.r(t),s.d(t,{default:()=>g});var i=s(8732),r=s(82015),n=s(19918),l=s.n(n),d=s(12403),o=s(91353),c=s(42893),u=s(56084),m=s(63487),h=s(88751),x=e([c,m]);[c,m]=x.then?(await x)():x;let g=e=>{let{t}=(0,h.useTranslation)("common"),[s,a]=(0,r.useState)([]),[,n]=(0,r.useState)(!1),[x,g]=(0,r.useState)(0),[p,j]=(0,r.useState)(10),[A,y]=(0,r.useState)(!1),[f,v]=(0,r.useState)({}),w={sort:{title:"asc"},limit:p,page:1,query:{}},S=[{name:t("adminsetting.areaofwork.Table.Title"),selector:"title"},{name:t("adminsetting.areaofwork.Table.Action"),selector:"",cell:e=>(0,i.jsxs)("div",{children:[(0,i.jsxs)(l(),{href:"/adminsettings/[...routes]",as:`/adminsettings/edit_area_of_work/${e._id}`,children:[" ",(0,i.jsx)("i",{className:"icon fas fa-edit"})]}),"\xa0",(0,i.jsx)("a",{onClick:()=>C(e),children:(0,i.jsx)("i",{className:"icon fas fa-trash-alt"})})," "]})}],b=async e=>{n(!0);let t=await m.A.get("/areaofwork",e);t&&t.data&&t.data.length>0&&(a(t.data),g(t.totalCount),n(!1))},_=async(e,t)=>{w.limit=e,w.page=t,n(!0);let s=await m.A.get("/areaofwork",w);s&&s.data&&s.data.length>0&&(a(s.data),j(e),n(!1))},C=async e=>{v(e._id),y(!0)},T=async()=>{try{await m.A.remove(`/areaofwork/${f}`),b(w),y(!1),c.default.success(t("adminsetting.areaofwork.Table.areaOfWorkDeletedSuccessfully"))}catch(e){c.default.error(t("adminsetting.areaofwork.Table.errorDeletingAreaOfWork"))}},k=()=>y(!1);return(0,r.useEffect)(()=>{b(w)},[]),(0,i.jsxs)("div",{children:[(0,i.jsxs)(d.A,{show:A,onHide:k,children:[(0,i.jsx)(d.A.Header,{closeButton:!0,children:(0,i.jsx)(d.A.Title,{children:t("adminsetting.areaofwork.Table.DeleteAreaofwork")})}),(0,i.jsx)(d.A.Body,{children:t("adminsetting.areaofwork.Table.Areyousurewanttodeletethisareaofwork?")}),(0,i.jsxs)(d.A.Footer,{children:[(0,i.jsx)(o.A,{variant:"secondary",onClick:k,children:t("adminsetting.areaofwork.Table.Cancel")}),(0,i.jsx)(o.A,{variant:"primary",onClick:T,children:t("adminsetting.areaofwork.Table.Yes")})]})]}),(0,i.jsx)(u.A,{columns:S,data:s,totalRows:x,pagServer:!0,handlePerRowsChange:_,handlePageChange:e=>{w.limit=p,w.page=e,b(w)}})]})};a()}catch(e){a(e)}})},83623:(e,t,s)=>{s.a(e,async(e,a)=>{try{s.r(t),s.d(t,{default:()=>w});var i=s(8732),r=s(82015),n=s(7082),l=s(18597),d=s(83551),o=s(49481),c=s(59549),u=s(91353),m=s(66994),h=s(23579),x=s(44233),g=s.n(x),p=s(42893),j=s(19918),A=s.n(j),y=s(63487),f=s(88751),v=e([p,y]);[p,y]=v.then?(await v)():v;let w=e=>{let t={title:"",level:""},[s,a]=(0,r.useState)(t),x=e.routes&&"edit_risklevel"===e.routes[0]&&e.routes[1],{t:j}=(0,f.useTranslation)("common"),v=(0,r.useRef)(null),w=e=>{if(e.target){let{name:t,value:s}=e.target;a(e=>({...e,[t]:s}))}},S=e=>(e||(e=""),e.charAt(0).toUpperCase()+e.slice(1)),b=async(t,a)=>{let i,r;t.preventDefault();let n=a||s,l={title:n.title.trim(),level:parseInt(n.level)};x?(r="adminsetting.RiskLevel.updatesuccess",i=await y.A.patch(`/risklevel/${e.routes[1]}`,l)):(r="adminsetting.RiskLevel.success",i=await y.A.post("/risklevel",l)),i&&i._id?(p.default.success(j(r)),g().push("/adminsettings/risklevel")):i?.errorCode===11e3?p.default.error(j("duplicatesNotAllowed")):p.default.error(S(i))};return(0,r.useEffect)(()=>{let t={query:{},sort:{title:"asc"},limit:"~"};x&&(async()=>{let s=await y.A.get(`/risklevel/${e.routes[1]}`,t);a(e=>({...e,...s}))})()},[]),(0,i.jsx)("div",{children:(0,i.jsx)(n.A,{className:"formCard",fluid:!0,children:(0,i.jsx)(l.A,{style:{marginTop:"5px",boxShadow:"0 4px 8px 0 rgba(0, 0, 0, 0.2), 0 6px 20px 0 rgba(0, 0, 0, 0.19)"},children:(0,i.jsx)(m.A,{onSubmit:b,ref:v,initialValues:s,enableReinitialize:!0,children:(0,i.jsxs)(l.A.Body,{children:[(0,i.jsx)(d.A,{children:(0,i.jsx)(o.A,{children:(0,i.jsx)(l.A.Title,{children:j("adminsetting.RiskLevel.Risklevel")})})}),(0,i.jsx)("hr",{}),(0,i.jsxs)(d.A,{children:[(0,i.jsx)(o.A,{md:!0,lg:6,sm:12,children:(0,i.jsxs)(c.A.Group,{children:[(0,i.jsx)(c.A.Label,{className:"required-field",children:j("adminsetting.RiskLevel.Risklevelname")}),(0,i.jsx)(h.ks,{name:"title",id:"title",required:!0,value:s.title,validator:e=>""!==e.trim(),errorMessage:{validator:j("adminsetting.RiskLevel.add")},onChange:w})]})}),(0,i.jsx)(o.A,{md:!0,lg:6,sm:12,children:(0,i.jsxs)(c.A.Group,{children:[(0,i.jsx)(c.A.Label,{className:"required-field",children:j("adminsetting.RiskLevel.Risklevelvalue")}),(0,i.jsx)(h.ks,{min:"0",type:"number",name:"level",id:"level",required:!0,value:s.level,errorMessage:{validator:j("adminsetting.RiskLevel.value"),min:j("adminsetting.RiskLevel.minValue"),required:j("adminsetting.RiskLevel.value")},onChange:w})]})})]}),(0,i.jsx)(d.A,{className:"my-4",children:(0,i.jsxs)(o.A,{children:[(0,i.jsx)(u.A,{className:"me-2",type:"submit",variant:"primary",children:j("submit")}),(0,i.jsx)(u.A,{className:"me-2",onClick:()=>{a(t),window.scrollTo(0,0)},variant:"info",children:j("reset")}),(0,i.jsx)(A(),{href:"/adminsettings/[...routes]",as:"/adminsettings/risklevel",children:(0,i.jsx)(u.A,{variant:"secondary",children:j("Cancel")})})]})})]})})})})})};a()}catch(e){a(e)}})},86842:e=>{e.exports=require("@restart/ui/SelectableContext")},87470:(e,t,s)=>{s.a(e,async(e,a)=>{try{s.r(t),s.d(t,{default:()=>g});var i=s(8732),r=s(7082),n=s(83551),l=s(49481),d=s(91353),o=s(19918),c=s.n(o),u=s(27053),m=s(87710),h=s(88751),x=e([m]);m=(x.then?(await x)():x)[0];let g=e=>{let{t}=(0,h.useTranslation)("common");return(0,i.jsx)("div",{children:(0,i.jsxs)(r.A,{style:{overflowX:"hidden"},fluid:!0,className:"p-0",children:[(0,i.jsx)(n.A,{children:(0,i.jsx)(l.A,{xs:12,children:(0,i.jsx)(u.A,{title:"Categories"})})}),(0,i.jsx)(n.A,{children:(0,i.jsx)(l.A,{xs:12,children:(0,i.jsx)(c(),{href:"/adminsettings/[...routes]",as:"/adminsettings/create_category",children:(0,i.jsx)(d.A,{variant:"secondary",size:"sm",children:t("Addcategory")})})})}),(0,i.jsx)(n.A,{className:"mt-3",children:(0,i.jsx)(l.A,{xs:12,children:(0,i.jsx)(m.default,{})})})]})})};a()}catch(e){a(e)}})},87571:e=>{e.exports=require("dom-helpers/transitionEnd")},87627:(e,t,s)=>{s.a(e,async(e,a)=>{try{s.r(t),s.d(t,{config:()=>p,default:()=>m,getServerSideProps:()=>g,getStaticPaths:()=>x,getStaticProps:()=>h,reportWebVitals:()=>j,routeModule:()=>S,unstable_getServerProps:()=>v,unstable_getServerSideProps:()=>w,unstable_getStaticParams:()=>f,unstable_getStaticPaths:()=>y,unstable_getStaticProps:()=>A});var i=s(63885),r=s(80237),n=s(81413),l=s(9616),d=s.n(l),o=s(72386),c=s(78929),u=e([o,c]);[o,c]=u.then?(await u)():u;let m=(0,n.M)(c,"default"),h=(0,n.M)(c,"getStaticProps"),x=(0,n.M)(c,"getStaticPaths"),g=(0,n.M)(c,"getServerSideProps"),p=(0,n.M)(c,"config"),j=(0,n.M)(c,"reportWebVitals"),A=(0,n.M)(c,"unstable_getStaticProps"),y=(0,n.M)(c,"unstable_getStaticPaths"),f=(0,n.M)(c,"unstable_getStaticParams"),v=(0,n.M)(c,"unstable_getServerProps"),w=(0,n.M)(c,"unstable_getServerSideProps"),S=new i.PagesRouteModule({definition:{kind:r.A.PAGES,page:"/adminsettings/[...routes]",pathname:"/adminsettings/[...routes]",bundlePath:"",filename:""},components:{App:o.default,Document:d()},userland:c});a()}catch(e){a(e)}})},87710:(e,t,s)=>{s.a(e,async(e,a)=>{try{s.r(t),s.d(t,{default:()=>x});var i=s(8732),r=s(82015),n=s(12403),l=s(91353),d=s(19918),o=s.n(d),c=s(56084),u=s(63487),m=s(88751),h=e([u]);u=(h.then?(await h)():h)[0];let x=e=>{let[t,s]=(0,r.useState)([]),[,a]=(0,r.useState)(!1),[d,h]=(0,r.useState)(0),[x,g]=(0,r.useState)(10),[p,j]=(0,r.useState)(!1),[A,y]=(0,r.useState)({}),{t:f}=(0,m.useTranslation)("common"),v={sort:{title:"asc"},limit:x,page:1,query:{}},w=[{name:"Title",selector:"title"},{name:"Action",selector:"",cell:e=>(0,i.jsxs)("div",{children:[(0,i.jsx)(o(),{href:"/adminsettings/[...routes]",as:`/adminsettings/edit_category/${e._id}`,children:(0,i.jsx)("i",{className:"icon fas fa-edit"})}),"\xa0",(0,i.jsx)(o(),{href:"#",onClick:()=>_(e),children:(0,i.jsx)("i",{className:"icon fas fa-trash-alt"})})," "]})}],S=async()=>{a(!0);let e=await u.A.get("/category",v);e&&e.data&&e.data.length>0&&(s(e.data),h(e.totalCount),a(!1))},b=async(e,t)=>{v.limit=e,v.page=t,a(!0);let i=await u.A.get("/category",v);i&&i.data&&i.data.length>0&&(s(i.data),g(e),a(!1))},_=async e=>{y(e._id),j(!0)},C=async()=>{await u.A.remove(`/category/${A}`),S(),j(!1)},T=()=>j(!1);return(0,r.useEffect)(()=>{S()},[]),(0,i.jsxs)("div",{children:[(0,i.jsxs)(n.A,{show:p,onHide:T,children:[(0,i.jsx)(n.A.Header,{closeButton:!0,children:(0,i.jsx)(n.A.Title,{children:f("DeleteCategory")})}),(0,i.jsxs)(n.A.Body,{children:[f("Areyousurewanttodeletethiscategory")," "]}),(0,i.jsxs)(n.A.Footer,{children:[(0,i.jsx)(l.A,{variant:"secondary",onClick:T,children:f("cancel")}),(0,i.jsx)(l.A,{variant:"primary",onClick:C,children:f("yes")})]})]}),(0,i.jsx)(c.A,{columns:w,data:t,totalRows:d,pagServer:!0,handlePerRowsChange:b,handlePageChange:e=>{v.limit=x,v.page=e,S()}})]})};a()}catch(e){a(e)}})},88751:e=>{e.exports=require("next-i18next")},89568:(e,t,s)=>{s.a(e,async(e,a)=>{try{s.r(t),s.d(t,{default:()=>g});var i=s(8732),r=s(82015),n=s(19918),l=s.n(n),d=s(12403),o=s(91353),c=s(42893),u=s(56084),m=s(63487),h=s(88751),x=e([c,m]);[c,m]=x.then?(await x)():x;let g=e=>{let{t}=(0,h.useTranslation)("common"),[s,a]=(0,r.useState)([]),[,n]=(0,r.useState)(!1),[x,g]=(0,r.useState)(0),[p,j]=(0,r.useState)(10),[A,y]=(0,r.useState)(!1),[f,v]=(0,r.useState)({}),w={sort:{title:"asc"},limit:p,page:1,query:{}},S=[{name:t("adminsetting.DeploymentStatus.Table.Title"),selector:"title"},{name:t("adminsetting.DeploymentStatus.Table.Action"),selector:"",cell:e=>(0,i.jsxs)("div",{children:[(0,i.jsx)(l(),{href:"/adminsettings/[...routes]",as:`/adminsettings/edit_deploymentstatus/${e._id}`,children:(0,i.jsx)("i",{className:"icon fas fa-edit"})}),"\xa0",(0,i.jsx)("a",{onClick:()=>C(e),children:(0,i.jsx)("i",{className:"icon fas fa-trash-alt"})})]})}],b=async()=>{n(!0);let e=await m.A.get("/deploymentstatus",w);e&&e.data&&e.data.length>0&&(a(e.data),g(e.totalCount),n(!1))},_=async(e,t)=>{w.limit=e,w.page=t,n(!0);let s=await m.A.get("/deploymentstatus",w);s&&s.data&&s.data.length>0&&(a(s.data),j(e),n(!1))},C=async e=>{v(e._id),y(!0)},T=async()=>{try{await m.A.remove(`/deploymentstatus/${f}`),b(),y(!1),c.default.success(t("adminsetting.DeploymentStatus.Table.deploymentStatusDeletedSuccessfully"))}catch(e){c.default.error(t("adminsetting.DeploymentStatus.Table.errorDeletingDeploymentStatus"))}},k=()=>y(!1);return(0,r.useEffect)(()=>{b()},[]),(0,i.jsxs)("div",{children:[(0,i.jsxs)(d.A,{show:A,onHide:k,children:[(0,i.jsx)(d.A.Header,{closeButton:!0,children:(0,i.jsx)(d.A.Title,{children:t("adminsetting.DeploymentStatus.Table.DeleteDeploymentstatus")})}),(0,i.jsx)(d.A.Body,{children:t("adminsetting.DeploymentStatus.Table.Areyousurewanttodeletethisdeploymentstatus?")}),(0,i.jsxs)(d.A.Footer,{children:[(0,i.jsx)(o.A,{variant:"secondary",onClick:k,children:t("adminsetting.DeploymentStatus.Table.Cancel")}),(0,i.jsx)(o.A,{variant:"primary",onClick:T,children:t("adminsetting.DeploymentStatus.Table.Yes")})]})]}),(0,i.jsx)(u.A,{columns:S,data:s,totalRows:x,pagServer:!0,handlePerRowsChange:_,handlePageChange:e=>{w.limit=p,w.page=e,b()}})]})};a()}catch(e){a(e)}})},89835:(e,t,s)=>{s.a(e,async(e,a)=>{try{s.r(t),s.d(t,{default:()=>f,getServerSideProps:()=>y});var i=s(8732),r=s(7082),n=s(83551),l=s(49481),d=s(91353),o=s(19918),c=s.n(o),u=s(34912),m=s(88751),h=s(35576),x=s(27053),g=s(45927),p=s(35557),j=s(14062),A=e([u,j]);async function y({locale:e}){return{props:{...await (0,h.serverSideTranslations)(e,["common"])}}}[u,j]=A.then?(await A)():A;let f=e=>{let{t}=(0,m.useTranslation)("common"),s=()=>(0,i.jsx)("div",{children:(0,i.jsxs)(r.A,{style:{overflowX:"hidden"},fluid:!0,className:"p-0",children:[(0,i.jsx)(n.A,{children:(0,i.jsx)(l.A,{xs:12,children:(0,i.jsx)(x.A,{title:t("adminsetting.Expertise.Forms.Expertise")})})}),(0,i.jsx)(n.A,{children:(0,i.jsx)(l.A,{xs:12,children:(0,i.jsx)(c(),{href:"/adminsettings/[...routes]",as:"/adminsettings/create_expertise",children:(0,i.jsx)(d.A,{variant:"secondary",size:"sm",children:t("adminsetting.Expertise.Forms.AddExpertise")})})})}),(0,i.jsx)(n.A,{className:"mt-3",children:(0,i.jsx)(l.A,{xs:12,children:(0,i.jsx)(u.default,{})})})]})}),a=(0,g.canAddExpertise)(()=>(0,i.jsx)(s,{})),o=(0,j.useSelector)(e=>e);return o?.permissions?.expertise?.["create:any"]?(0,i.jsx)(a,{}):(0,i.jsx)(p.default,{})};a()}catch(e){a(e)}})},90785:(e,t,s)=>{s.a(e,async(e,a)=>{try{s.r(t),s.d(t,{default:()=>w});var i=s(8732),r=s(82015),n=s(7082),l=s(18597),d=s(83551),o=s(49481),c=s(59549),u=s(91353),m=s(66994),h=s(23579),x=s(44233),g=s.n(x),p=s(42893),j=s(19918),A=s.n(j),y=s(88751),f=s(63487),v=e([p,f]);[p,f]=v.then?(await v)():v;let w=e=>{let t={title:""},[s,a]=(0,r.useState)(t),x=e.routes&&"edit_role"===e.routes[0]&&e.routes[1],{t:j}=(0,y.useTranslation)("common"),v=(0,r.useRef)(null),w=async(t,a)=>{let i;t&&t.preventDefault();let r={title:(a||s).title.trim()};(i=x?await f.A.patch(`/roles/${e.routes[1]}`,r):await f.A.post("/roles",r))&&i._id?(p.default.success(j("Roleisaddedsuccessfully")),g().push("/adminsettings/role")):p.default.error(i)};return(0,r.useEffect)(()=>{let t={query:{},sort:{title:"asc"},limit:"~"};x&&(async()=>{let s=await f.A.get(`/roles/${e.routes[1]}`,t);a(e=>({...e,...s}))})()},[]),(0,i.jsx)("div",{children:(0,i.jsx)(n.A,{className:"formCard",fluid:!0,children:(0,i.jsx)(l.A,{style:{marginTop:"5px",boxShadow:"0 4px 8px 0 rgba(0, 0, 0, 0.2), 0 6px 20px 0 rgba(0, 0, 0, 0.19)"},children:(0,i.jsx)(m.A,{onSubmit:w,ref:v,initialValues:s,enableReinitialize:!0,children:(0,i.jsxs)(l.A.Body,{children:[(0,i.jsx)(d.A,{children:(0,i.jsx)(o.A,{children:(0,i.jsx)(l.A.Title,{children:j("Role")})})}),(0,i.jsx)("hr",{}),(0,i.jsx)(d.A,{children:(0,i.jsx)(o.A,{md:!0,lg:6,sm:12,children:(0,i.jsxs)(c.A.Group,{children:[(0,i.jsx)(c.A.Label,{className:"required-field",children:j("Role")}),(0,i.jsx)(h.ks,{name:"title",id:"title",required:!0,value:s.title,validator:e=>""!==String(e||"").trim(),errorMessage:{validator:j("PleaseAddtheRole")},onChange:e=>{if(e.target){let{name:t,value:s}=e.target;a(e=>({...e,[t]:s}))}}})]})})}),(0,i.jsx)(d.A,{className:"my-4",children:(0,i.jsxs)(o.A,{children:[(0,i.jsx)(u.A,{className:"me-2",type:"submit",variant:"primary",children:j("submit")}),(0,i.jsx)(u.A,{className:"me-2",onClick:()=>{a(t),window.scrollTo(0,0)},variant:"info",children:j("reset")}),(0,i.jsx)(A(),{href:"/adminsettings/[...routes]",as:"/adminsettings/role",children:(0,i.jsx)(u.A,{variant:"secondary",children:j("cancel")})})]})})]})})})})})};a()}catch(e){a(e)}})},92774:(e,t,s)=>{s.a(e,async(e,a)=>{try{s.r(t),s.d(t,{default:()=>w});var i=s(8732),r=s(82015),n=s(7082),l=s(18597),d=s(83551),o=s(49481),c=s(59549),u=s(91353),m=s(23579),h=s(44233),x=s.n(h),g=s(42893),p=s(19918),j=s.n(p),A=s(63487),y=s(88751),f=s(66994),v=e([g,A]);[g,A]=v.then?(await v)():v;let w=e=>{let{t}=(0,y.useTranslation)("common"),s={title:"",code:""},[a,h]=(0,r.useState)(s),p=e.routes&&"edit_worldregion"===e.routes[0]&&e.routes[1],v=(0,r.useRef)(null),w=e=>{if(e.target){let{name:t,value:s}=e.target;h(e=>({...e,[t]:s}))}},S=async s=>{let i,r;s.preventDefault();let n={title:a.title.trim(),code:a.code};p?(r="adminsetting.worldregion.form.Worldregionisupdatedsuccessfully",i=await A.A.patch(`/worldregion/${e.routes[1]}`,n)):(r="adminsetting.worldregion.form.Worldregionisaddedsuccessfully",i=await A.A.post("/worldregion",n)),i&&i._id?(g.default.success(t(r)),x().push("/adminsettings/worldregion")):i?.errorCode===11e3?g.default.error(t("duplicatesNotAllowed")):g.default.error(i)};return(0,r.useEffect)(()=>{let t={query:{},sort:{title:"asc"},limit:"~"};p&&(async()=>{let s=await A.A.get(`/worldregion/${e.routes[1]}`,t);h(e=>({...e,...s}))})()},[]),(0,i.jsx)("div",{children:(0,i.jsx)(n.A,{className:"formCard",fluid:!0,children:(0,i.jsx)(l.A,{style:{marginTop:"5px",boxShadow:"0 4px 8px 0 rgba(0, 0, 0, 0.2), 0 6px 20px 0 rgba(0, 0, 0, 0.19)"},children:(0,i.jsx)(f.A,{onSubmit:S,ref:v,initialValues:a,enableReinitialize:!0,children:(0,i.jsxs)(l.A.Body,{children:[(0,i.jsx)(d.A,{children:(0,i.jsx)(o.A,{children:(0,i.jsx)(l.A.Title,{children:t("adminsetting.worldregion.form.WorldRegion")})})}),(0,i.jsx)("hr",{}),(0,i.jsxs)(d.A,{children:[(0,i.jsx)(o.A,{md:!0,lg:6,sm:12,children:(0,i.jsxs)(c.A.Group,{children:[(0,i.jsx)(c.A.Label,{className:"required-field",children:t("adminsetting.worldregion.form.WorldRegion")}),(0,i.jsx)(m.ks,{name:"title",id:"title",required:!0,value:a.title,validator:e=>""!==String(e||"").trim(),errorMessage:{validator:t("adminsetting.worldregion.form.PleaseAddtheWorldRegion")},onChange:w})]})}),(0,i.jsx)(o.A,{md:!0,lg:6,sm:12,children:(0,i.jsxs)(c.A.Group,{children:[(0,i.jsx)(c.A.Label,{className:"required-field",children:t("adminsetting.worldregion.form.Code")}),(0,i.jsx)(m.ks,{name:"code",id:"code",required:!0,value:a.code,validator:e=>""!==String(e||"").trim(),errorMessage:{validator:t("adminsetting.worldregion.form.PleaseAddthecode")},onChange:w})]})})]}),(0,i.jsx)(d.A,{className:"my-4",children:(0,i.jsxs)(o.A,{children:[(0,i.jsx)(u.A,{className:"me-2",type:"submit",variant:"primary",children:t("adminsetting.worldregion.form.Submit")}),(0,i.jsx)(u.A,{className:"me-2",onClick:()=>{h(s),window.scrollTo(0,0)},variant:"info",children:t("adminsetting.worldregion.form.Reset")}),(0,i.jsx)(j(),{href:"/adminsettings/[...routes]",as:"/adminsettings/worldregion",children:(0,i.jsx)(u.A,{variant:"secondary",children:t("adminsetting.worldregion.form.Cancel")})})]})})]})})})})})};a()}catch(e){a(e)}})},93787:e=>{e.exports=require("redux-persist")},94149:(e,t,s)=>{s.a(e,async(e,a)=>{try{s.r(t),s.d(t,{default:()=>g});var i=s(8732),r=s(19918),n=s.n(r),l=s(82015),d=s(12403),o=s(91353),c=s(42893),u=s(56084),m=s(63487),h=s(88751),x=e([c,m]);[c,m]=x.then?(await x)():x;let g=e=>{let[t,s]=(0,l.useState)([]),[,a]=(0,l.useState)(!1),[r,x]=(0,l.useState)(0),[g,p]=(0,l.useState)(10),[j,A]=(0,l.useState)(!1),[y,f]=(0,l.useState)({}),{t:v}=(0,h.useTranslation)("common"),w=[{name:v("adminsetting.hazardtypes.HarzardType"),selector:"title"},{name:v("adminsetting.hazardtypes.Code"),selector:"code",cell:e=>e.code},{name:v("Description"),selector:"description",cell:e=>e.description.replace(/<[^>]+>/g,"")},{name:v("action"),selector:"",cell:e=>(0,i.jsxs)("div",{children:[(0,i.jsx)(n(),{href:"/adminsettings/[...routes]",as:`/adminsettings/edit_hazard_types/${e._id}`,children:(0,i.jsx)("i",{className:"icon fas fa-edit"})}),"\xa0",(0,i.jsx)("a",{onClick:()=>C(e),children:(0,i.jsx)("i",{className:"icon fas fa-trash-alt"})})]})}];(0,l.useEffect)(()=>{b()},[]);let S={sort:{title:"asc"},limit:g,page:1,query:{}},b=async()=>{a(!0);let e=await m.A.get("/hazardtype",S);e&&e.data&&e.data.length>0&&(s(e.data),x(e.totalCount),a(!1))},_=async(e,t)=>{S.limit=e,S.page=t,a(!0);let i=await m.A.get("/hazardtype",S);i&&i.data&&i.data.length>0&&(s(i.data),p(e),a(!1))},C=async e=>{f(e._id),A(!0)},T=async()=>{try{await m.A.remove(`/hazardtype/${y}`),b(),A(!1),c.default.success(v("adminsetting.hazardtypes.Table.hazardTypeDeletedSuccessfully"))}catch(e){c.default.error(v("adminsetting.hazardtypes.Table.errorDeletingHazardType"))}},k=()=>A(!1);return(0,i.jsxs)("div",{children:[(0,i.jsxs)(d.A,{show:j,onHide:k,children:[(0,i.jsx)(d.A.Header,{closeButton:!0,children:(0,i.jsx)(d.A.Title,{children:v("adminsetting.hazardtypes.Deletehazardtype")})}),(0,i.jsx)(d.A.Body,{children:v("adminsetting.hazardtypes.Areyousurewanttodeletethishazardtype")}),(0,i.jsxs)(d.A.Footer,{children:[(0,i.jsx)(o.A,{variant:"secondary",onClick:k,children:v("cancel")}),(0,i.jsx)(o.A,{variant:"primary",onClick:T,children:v("yes")})]})]}),(0,i.jsx)(u.A,{columns:w,data:t,totalRows:r,pagServer:!0,handlePerRowsChange:_,handlePageChange:e=>{S.limit=g,S.page=e,b()}})]})};a()}catch(e){a(e)}})},94544:(e,t,s)=>{s.r(t),s.d(t,{default:()=>o});var a=s(8732),i=s(7082),r=s(83551),n=s(49481),l=s(84517),d=s(88751);let o=({filterText:e,onFilter:t,onClear:s})=>{let{t:o}=(0,d.useTranslation)("common");return(0,a.jsx)(i.A,{fluid:!0,className:"p-0",children:(0,a.jsx)(r.A,{children:(0,a.jsx)(n.A,{md:4,className:"p-0",children:(0,a.jsx)(l.A,{type:"text",className:"searchInput",placeholder:o("adminsetting.hazard.Search"),"aria-label":"Search",value:e,onChange:t})})})})}},94947:e=>{e.exports=require("@restart/hooks/useTimeout")},95864:(e,t,s)=>{s.a(e,async(e,a)=>{try{s.r(t),s.d(t,{default:()=>g});var i=s(8732),r=s(19918),n=s.n(r),l=s(82015),d=s(12403),o=s(91353),c=s(42893),u=s(56084),m=s(88751),h=s(63487),x=e([c,h]);[c,h]=x.then?(await x)():x;let g=e=>{let{t}=(0,m.useTranslation)("common"),[s,a]=(0,l.useState)([]),[,r]=(0,l.useState)(!1),[x,g]=(0,l.useState)(0),[p,j]=(0,l.useState)(10),[A,y]=(0,l.useState)(!1),[f,v]=(0,l.useState)({}),w=()=>y(!1),S=[{name:t("adminsetting.updatestype.Title"),selector:"title"},{name:t("adminsetting.updatestype.Icon"),selector:"icon",cell:e=>(0,i.jsx)("i",{className:`fas ${e.icon}`})},{name:t("adminsetting.updatestype.Action"),selector:"",cell:e=>(0,i.jsxs)("div",{children:[(0,i.jsx)(n(),{href:"/adminsettings/[...routes]",as:`/adminsettings/edit_update_type/${e._id}`,children:(0,i.jsx)("i",{className:"icon fas fa-edit"})}),"\xa0",(0,i.jsx)("a",{onClick:()=>k(e),children:(0,i.jsx)("i",{className:"icon fas fa-trash-alt"})})," "]})}],b={sort:{title:"asc"},limit:p,page:1,query:{}};(0,l.useEffect)(()=>{_(b)},[]);let _=async e=>{r(!0);let t=await h.A.get("/updatetype",e);t&&t.data&&t.data.length>0&&(a(t.data),g(t.totalCount),r(!1))},C=async(e,t)=>{b.limit=e,b.page=t,r(!0);let s=await h.A.get("/updatetype",b);s&&s.data&&s.data.length>0&&(a(s.data),j(e),r(!1))},T=async()=>{try{await h.A.remove(`/updatetype/${f}`),_(b),y(!1),c.default.success(t("adminsetting.updatestype.Table.updateTypeDeletedSuccessfully"))}catch(e){c.default.error(t("adminsetting.updatestype.Table.errorDeletingUpdateType"))}},k=async e=>{v(e._id),y(!0)};return(0,i.jsxs)("div",{children:[(0,i.jsxs)(d.A,{show:A,onHide:w,children:[(0,i.jsx)(d.A.Header,{closeButton:!0,children:(0,i.jsx)(d.A.Title,{children:t("adminsetting.updatestype.DeleteUpdateType")})}),(0,i.jsx)(d.A.Body,{children:t("adminsetting.updatestype.Areyousurewanttodeletethisupdatetype?")}),(0,i.jsxs)(d.A.Footer,{children:[(0,i.jsx)(o.A,{variant:"secondary",onClick:w,children:t("adminsetting.updatestype.Cancel")}),(0,i.jsx)(o.A,{variant:"primary",onClick:T,children:t("adminsetting.updatestype.Yes")})]})]}),(0,i.jsx)(u.A,{columns:S,data:s,totalRows:x,pagServer:!0,handlePerRowsChange:C,handlePageChange:e=>{b.limit=p,b.page=e,_(b)}})]})};a()}catch(e){a(e)}})},96196:e=>{e.exports=require("next-redux-wrapper")},98320:e=>{e.exports=require("@restart/hooks/useIsomorphicEffect")},98630:(e,t,s)=>{s.a(e,async(e,a)=>{try{s.r(t),s.d(t,{default:()=>f});var i=s(8732),r=s(82015),n=s(7082),l=s(18597),d=s(83551),o=s(49481),c=s(59549),u=s(91353),m=s(66994),h=s(23579),x=s(44233),g=s.n(x),p=s(42893),j=s(63487),A=s(88751),y=e([p,j]);[p,j]=y.then?(await y)():y;let f=e=>{let[t,s]=(0,r.useState)({title:""}),a=e.routes&&"edit_role"===e.routes[0]&&e.routes[1],{t:x}=(0,A.useTranslation)("common"),y=(0,r.useRef)(null),f=async(s,i)=>{let r;s&&s.preventDefault();let n={title:(i||t).title.trim()};(r=a?await j.A.patch(`/roles/${e.routes[1]}`,n):await j.A.post("/roles",n))&&r._id?(p.default.success(x("Roleisaddedsuccessfully")),g().push("/adminsettings/role")):p.default.error(r)};return(0,r.useEffect)(()=>{let t={query:{},sort:{title:"asc"},limit:"~"};a&&(async()=>{let a=await j.A.get(`/roles/${e.routes[1]}`,t);s(e=>({...e,...a}))})()},[]),(0,i.jsx)("div",{children:(0,i.jsx)(n.A,{className:"formCard",fluid:!0,children:(0,i.jsx)(l.A,{style:{marginTop:"5px",boxShadow:"0 4px 8px 0 rgba(0, 0, 0, 0.2), 0 6px 20px 0 rgba(0, 0, 0, 0.19)"},children:(0,i.jsx)(m.A,{onSubmit:f,ref:y,initialValues:t,enableReinitialize:!0,children:(0,i.jsxs)(l.A.Body,{children:[(0,i.jsx)(d.A,{children:(0,i.jsx)(o.A,{children:(0,i.jsx)(l.A.Title,{children:x("Role")})})}),(0,i.jsx)("hr",{}),(0,i.jsx)(d.A,{children:(0,i.jsx)(o.A,{md:!0,lg:6,sm:12,children:(0,i.jsxs)(c.A.Group,{children:[(0,i.jsx)(c.A.Label,{className:"required-field",children:x("Role")}),(0,i.jsx)(h.ks,{name:"title",id:"title",required:!0,value:t.title,validator:e=>""!==String(e||"").trim(),successMessage:x("Looksgood"),errorMessage:{validator:x("PleaseAddtheRole")},onChange:e=>{if(e.target){let{name:t,value:a}=e.target;s(e=>({...e,[t]:a}))}}})]})})}),(0,i.jsxs)(d.A,{className:"my-4",children:[(0,i.jsx)(o.A,{xs:!0,lg:"2",children:(0,i.jsx)(u.A,{type:"submit",variant:"primary",children:x("submit")})}),(0,i.jsx)(o.A,{children:(0,i.jsx)(u.A,{onClick:()=>{window.scrollTo(0,0)},variant:"info",children:x("reset")})})]})]})})})})})};a()}catch(e){a(e)}})},99460:e=>{e.exports=require("dom-helpers/css")},99800:e=>{e.exports=import("react-select")}};var t=require("../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),a=t.X(0,[6089,9216,9616,2386,6349,1102,4710,9,7032,3929],()=>s(87627));module.exports=a})();