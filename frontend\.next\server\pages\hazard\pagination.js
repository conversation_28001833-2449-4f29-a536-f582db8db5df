"use strict";(()=>{var e={};e.id=8795,e.ids=[636,3220,8795],e.modules={123:e=>{e.exports=require("dom-helpers/removeEventListener")},1332:e=>{e.exports=require("react-custom-scrollbars-2")},1428:e=>{e.exports=import("axios")},1680:e=>{e.exports=require("@restart/ui/utils")},1919:e=>{e.exports=require("react-transition-group/Transition")},3892:e=>{e.exports=require("classnames")},4048:e=>{e.exports=require("react-truncate")},6009:e=>{e.exports=require("dom-helpers/ownerDocument")},6952:e=>{e.exports=require("@restart/ui/Modal")},7374:e=>{e.exports=require("@restart/ui/Dropdown")},8732:e=>{e.exports=require("react/jsx-runtime")},9653:e=>{e.exports=require("@restart/ui/Overlay")},11242:e=>{e.exports=require("redux-persist/integration/react")},11688:e=>{e.exports=require("@restart/hooks/useWillUnmount")},12388:e=>{e.exports=require("dom-helpers/addEventListener")},13364:e=>{e.exports=require("redux-saga/effects")},14062:e=>{e.exports=import("react-redux")},14078:e=>{e.exports=import("swr")},14332:e=>{e.exports=require("uncontrollable")},16116:e=>{e.exports=require("invariant")},18622:e=>{e.exports=require("yup")},19121:e=>{e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},22326:e=>{e.exports=require("react-dom")},22541:e=>{e.exports=require("redux-persist/lib/storage")},25303:e=>{e.exports=require("@restart/ui/NavItem")},26324:e=>{e.exports=require("warning")},27743:(e,r,t)=>{t.a(e,async(e,s)=>{try{t.r(r),t.d(r,{config:()=>h,default:()=>d,getServerSideProps:()=>m,getStaticPaths:()=>q,getStaticProps:()=>c,reportWebVitals:()=>g,routeModule:()=>M,unstable_getServerProps:()=>S,unstable_getServerSideProps:()=>b,unstable_getStaticParams:()=>v,unstable_getStaticPaths:()=>f,unstable_getStaticProps:()=>P});var o=t(63885),i=t(80237),a=t(81413),u=t(9616),p=t.n(u),n=t(72386),l=t(69970),x=e([n]);n=(x.then?(await x)():x)[0];let d=(0,a.M)(l,"default"),c=(0,a.M)(l,"getStaticProps"),q=(0,a.M)(l,"getStaticPaths"),m=(0,a.M)(l,"getServerSideProps"),h=(0,a.M)(l,"config"),g=(0,a.M)(l,"reportWebVitals"),P=(0,a.M)(l,"unstable_getStaticProps"),f=(0,a.M)(l,"unstable_getStaticPaths"),v=(0,a.M)(l,"unstable_getStaticParams"),S=(0,a.M)(l,"unstable_getServerProps"),b=(0,a.M)(l,"unstable_getServerSideProps"),M=new o.PagesRouteModule({definition:{kind:i.A.PAGES,page:"/hazard/pagination",pathname:"/hazard/pagination",bundlePath:"",filename:""},components:{App:n.default,Document:p()},userland:l});s()}catch(e){s(e)}})},27825:e=>{e.exports=require("lodash")},27910:e=>{e.exports=require("stream")},28217:e=>{e.exports=require("@restart/ui/DropdownItem")},29021:e=>{e.exports=require("fs")},29294:e=>{e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},29628:e=>{e.exports=require("dom-helpers/addClass")},29825:e=>{e.exports=require("prop-types")},29841:e=>{e.exports=require("dom-helpers/hasClass")},30362:e=>{e.exports=require("@restart/ui/DropdownToggle")},33873:e=>{e.exports=require("path")},36653:e=>{e.exports=require("nprogress")},36955:e=>{e.exports=require("@restart/hooks/useBreakpoint")},37766:e=>{e.exports=require("@restart/hooks/useMergedRefs")},39756:e=>{e.exports=import("redux")},40051:e=>{e.exports=require("dom-helpers/removeClass")},40361:e=>{e.exports=require("next/dist/compiled/next-server/pages.runtime.prod.js")},42893:e=>{e.exports=import("react-hot-toast")},43294:e=>{e.exports=require("formik")},50009:e=>{e.exports=require("@restart/ui/DropdownMenu")},57664:e=>{e.exports=require("dom-helpers/scrollbarSize")},58928:e=>{e.exports=require("dom-helpers/canUseDOM")},59717:e=>{e.exports=require("@restart/ui/DropdownContext")},60560:e=>{e.exports=require("dom-helpers/contains")},63033:e=>{e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},65447:e=>{e.exports=require("@restart/ui/Button")},67364:e=>{e.exports=require("@restart/hooks/useCallbackRef")},68455:e=>{e.exports=import("redux-saga")},69722:e=>{e.exports=require("es6-promise")},69970:(e,r,t)=>{t.r(r),t.d(r,{default:()=>f});var s=t(8732),o=t(82015),i=t(3892),a=t.n(i),u=t(80739),p=t(78634),n=t.n(p);let l=o.forwardRef(({active:e=!1,disabled:r=!1,className:t,style:o,activeLabel:i="(current)",children:u,linkStyle:p,linkClassName:l,as:x=n(),...d},c)=>{let q=e||r?"span":x;return(0,s.jsx)("li",{ref:c,style:o,className:a()(t,"page-item",{active:e,disabled:r}),children:(0,s.jsxs)(q,{className:a()("page-link",l),style:p,...d,children:[u,e&&i&&(0,s.jsx)("span",{className:"visually-hidden",children:i})]})})});function x(e,r,t=e){let i=o.forwardRef(({children:e,...o},i)=>(0,s.jsxs)(l,{...o,ref:i,children:[(0,s.jsx)("span",{"aria-hidden":"true",children:e||r}),(0,s.jsx)("span",{className:"visually-hidden",children:t})]}));return i.displayName=e,i}l.displayName="PageItem";let d=x("First","\xab"),c=x("Prev","‹","Previous"),q=x("Ellipsis","…","More"),m=x("Next","›"),h=x("Last","\xbb"),g=o.forwardRef(({bsPrefix:e,className:r,size:t,...o},i)=>{let p=(0,u.oU)(e,"pagination");return(0,s.jsx)("ul",{ref:i,...o,className:a()(r,p,t&&`${p}-${t}`)})});g.displayName="Pagination";let P=Object.assign(g,{First:d,Prev:c,Ellipsis:q,Item:l,Next:m,Last:h}),f=({postsPerPage:e,totalPosts:r,paginate:t})=>{let i=[],[a,u]=(0,o.useState)(1);for(let t=1;t<=Math.ceil(r/e);t++)i.push((0,s.jsx)(P.Item,{active:t===a,children:t},t));let p=async e=>{let r=e.target;u(r.textContent),e.target&&await t(r.textContent)};return(0,s.jsx)(P,{onClick:p,children:i})}},74075:e=>{e.exports=require("zlib")},74716:e=>{e.exports=require("moment")},74987:e=>{e.exports=require("@restart/ui/ModalManager")},78097:e=>{e.exports=require("next-redux-saga")},78634:e=>{e.exports=require("@restart/ui/Anchor")},80237:(e,r)=>{Object.defineProperty(r,"A",{enumerable:!0,get:function(){return t}});var t=function(e){return e.PAGES="PAGES",e.PAGES_API="PAGES_API",e.APP_PAGE="APP_PAGE",e.APP_ROUTE="APP_ROUTE",e.IMAGE="IMAGE",e}({})},81366:e=>{e.exports=require("redux-auth-wrapper/connectedAuthWrapper")},81413:(e,r)=>{Object.defineProperty(r,"M",{enumerable:!0,get:function(){return function e(r,t){return t in r?r[t]:"then"in r&&"function"==typeof r.then?r.then(r=>e(r,t)):"function"==typeof r&&"default"===t?r:void 0}}})},81521:e=>{e.exports=require("dom-helpers/querySelectorAll")},81895:e=>{e.exports=require("@restart/hooks/useEventCallback")},82015:e=>{e.exports=require("react")},86842:e=>{e.exports=require("@restart/ui/SelectableContext")},87571:e=>{e.exports=require("dom-helpers/transitionEnd")},88751:e=>{e.exports=require("next-i18next")},93787:e=>{e.exports=require("redux-persist")},94947:e=>{e.exports=require("@restart/hooks/useTimeout")},96196:e=>{e.exports=require("next-redux-wrapper")},98320:e=>{e.exports=require("@restart/hooks/useIsomorphicEffect")},99460:e=>{e.exports=require("dom-helpers/css")}};var r=require("../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[6089,9216,9616,2386],()=>t(27743));module.exports=s})();