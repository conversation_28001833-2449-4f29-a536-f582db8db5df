{"version": 3, "file": "static/chunks/pages/adminsettings/approval/vspace_appoval-c5676ba9572e818d.js", "mappings": "0qBAGA,IAAMA,EAAS,aACFC,EAAmBC,CAAAA,EAAAA,EAAAA,CAAAA,CAAoBA,CAAC,CACnDC,sBAAuB,KACjBC,EAAMC,WAAW,IAAID,EAAMC,WAAW,CAACC,YAAY,IAAIF,EAAMC,WAAW,CAACC,YAAY,CAACN,EAAO,CAKnGO,CALqG,kBAKjF,kBACtB,GAAG,EAE0BL,CAAAA,EAAAA,EAAAA,CAAAA,CAAoBA,CAAC,CAChDC,sBAAuB,KACjBC,EAAMC,WAAW,IAAID,EAAMC,WAAW,CAACG,OAAO,IAAIJ,EAAMC,WAAW,CAACG,OAAO,CAACR,EAAO,CAKzFO,CAL2F,kBAKvE,eACtB,GAAG,EAEmCL,CAAAA,EAAAA,EAAAA,CAAAA,CAAoBA,CAAC,CACzDC,sBAAuB,KACjBC,EAAMC,WAAW,IAAID,EAAMC,WAAW,CAACI,iBAAiB,IAAIL,EAAMC,WAAW,CAACI,iBAAiB,CAACT,EAAO,CAK7GO,CAL+G,kBAK3F,wBACtB,GAAG,EAE8BL,CAAAA,EAAAA,EAAAA,CAAAA,CAAoBA,CAAC,CACpDC,sBAAuB,KACjBC,EAAMC,WAAW,IAAID,EAAMC,WAAW,CAACK,YAAY,IAAIN,EAAMC,WAAW,CAACK,YAAY,CAACV,EAAO,CAKnGO,CALqG,kBAKjF,mBACtB,GAAG,EAE4BL,CAAAA,EAAAA,EAAAA,CAAAA,CAAoBA,CAAC,CAClDC,sBAAuB,KACjBC,EAAMC,WAAW,IAAID,EAAMC,WAAW,CAACM,SAAS,IAAIP,EAAMC,WAAW,CAACM,SAAS,CAACX,EAAO,CAK7FO,CAL+F,kBAK3E,iBACtB,GAAG,EAEqCL,CAAAA,EAAAA,EAAAA,CAAAA,CAAoBA,CAAC,CAC3DC,sBAAuB,KACjBC,EAAMC,WAAW,IAAID,EAAMC,WAAW,CAACO,uBAAuB,IAAIR,EAAMC,WAAW,CAACO,uBAAuB,CAACZ,EAAO,CAKzHO,CAL2H,kBAKvG,0BACtB,GAAG,EAEiCL,CAAAA,EAAAA,EAAAA,CAAAA,CAAoBA,CAAC,CACvDC,sBAAwBC,KAClBA,EAAMC,WAAW,IAAID,EAAMC,WAAW,CAACO,uBAAuB,IAAIR,EAAMC,WAAW,CAACO,uBAAuB,CAACZ,EAAO,CAKzHO,CAL2H,kBAKvG,sBACtB,GAAG,EAC0BL,CAAAA,EAAAA,EAAAA,CAAAA,CAAoBA,CAAC,CAChDC,sBAAuB,KACjBC,EAAMC,WAAW,IAAID,EAAMC,WAAW,CAACQ,MAAM,IAAIT,EAAMC,WAAW,CAACQ,MAAM,CAACb,EAAO,CAKvFO,CALyF,kBAKrE,eACtB,GAAG,EAE8BL,CAAAA,EAAAA,EAAAA,CAAAA,CAAoBA,CAAC,CACpDC,sBAAuB,KACjBC,EAAMC,WAAW,IAAID,EAAMC,WAAW,CAACS,WAAW,IAAIV,EAAMC,WAAW,CAACS,WAAW,CAACd,EAAO,CAKjGO,CALmG,kBAK/E,mBACtB,GAAG,EAEuCL,CAAAA,EAAAA,EAAAA,CAAAA,CAAoBA,CAAC,CAC7DC,sBAAuB,GACjBC,IAAMC,WAAW,IAAID,EAAMC,WAAW,CAACU,WAAW,IAAIX,EAAMC,WAAW,CAACU,WAAW,CAACf,EAAO,CAKjGO,CALmG,kBAK/E,4BACtB,GAAG,EAEuCL,CAAAA,EAAAA,EAAAA,CAAAA,CAAoBA,CAAC,CAC7DC,sBAAuB,GACjBC,IAAMC,WAAW,IAAID,EAAMC,WAAW,CAACW,mBAAmB,IAAIZ,EAAMC,WAAW,CAACW,mBAAmB,CAAChB,EAAO,CAKjHO,CALmH,kBAK/F,4BACtB,GAEaU,EAA0Bf,CAAAA,EAAAA,EAAAA,CAAAA,CAAoBA,CAAC,CAC1DC,sBAAuB,KACjBC,EAAMC,WAAW,IAAID,EAAMC,WAAW,CAACa,gBAAgB,IAAId,EAAMC,WAAW,CAACa,gBAAgB,CAAClB,EAAO,CAK3GO,CAL6G,kBAKzF,yBACtB,GAAG,EAEkCL,CAAAA,EAAAA,EAAAA,CAAAA,CAAoBA,CAAC,CACxDC,sBAAuB,GACjBC,IAAMC,WAAW,IAAID,EAAMC,WAAW,CAACc,gBAAgB,IAAIf,EAAMC,WAAW,CAACc,gBAAgB,CAACnB,EAAO,CAK3GO,CAL6G,kBAKzF,uBACtB,GAAG,EAEgCL,CAAAA,EAAAA,EAAAA,CAAAA,CAAoBA,CAAC,CACtDC,sBAAuB,KACjBC,EAAMC,WAAW,IAAID,EAAMC,WAAW,CAACe,cAAc,IAAIhB,EAAMC,WAAW,CAACe,cAAc,CAACpB,EAAO,CAKvGO,CALyG,kBAKrF,qBACtB,GAAG,EAE0BL,CAAAA,EAAAA,EAAAA,CAAAA,CAAoBA,CAAC,CAChDC,sBAAwBC,KAClBA,EAAMC,WAAW,IAAID,EAAMC,WAAW,CAACgB,MAAM,IAAIjB,EAAMC,WAAW,CAACgB,MAAM,CAACrB,EAAO,CAKvFO,CALyF,kBAKrE,eACtB,GAAG,EAE6BL,CAAAA,EAAAA,EAAAA,CAAAA,CAAoBA,CAAC,CACnDC,sBAAuB,GACjBC,IAAMC,WAAW,IAAID,EAAMC,WAAW,CAACiB,UAAU,IAAIlB,EAAMC,WAAW,CAACiB,UAAU,CAACtB,EAAO,CAK/FO,CALiG,kBAK7E,kBACtB,GAAG,EAE4BL,CAAAA,EAAAA,EAAAA,CAAAA,CAAoBA,CAAC,CAClDC,sBAAuB,KACjBC,EAAMC,WAAW,IAAID,EAAMC,WAAW,CAACkB,QAAQ,IAAInB,EAAMC,WAAW,CAACkB,QAAQ,CAACvB,EAAO,CAK3FO,CAL6F,kBAKzE,iBACtB,GAAG,EAE8BL,CAAAA,EAAAA,EAAAA,CAAAA,CAAoBA,CAAC,CACpDC,sBAAuB,KACjBC,EAAMC,WAAW,IAAID,EAAMC,WAAW,CAACmB,WAAW,IAAIpB,EAAMC,WAAW,CAACmB,WAAW,CAACxB,EAAO,CAKjGO,CALmG,kBAK/E,mBACtB,GAAG,EAEwBL,CAAAA,EAAAA,EAAAA,CAAAA,CAAoBA,CAAC,CAC9CC,sBAAuB,KACjBC,EAAMC,WAAW,IAAID,EAAMC,WAAW,CAACoB,KAAK,IAAIrB,EAAMC,WAAW,CAACoB,KAAK,CAACzB,EAAO,CAKrFO,CALuF,kBAKnE,aACtB,GAAG,EAE8BL,CAAAA,EAAAA,EAAAA,CAAAA,CAAoBA,CAAC,CACpDC,sBAAuB,KACjBC,EAAMC,WAAW,IAAID,EAAMC,WAAW,CAACqB,WAAW,IAAItB,EAAMC,WAAW,CAACqB,WAAW,CAAC1B,EAAO,CAKjGO,CALmG,kBAK/E,mBACtB,GAAG,EAE8BL,CAAAA,EAAAA,EAAAA,CAAAA,CAAoBA,CAAC,CACpDC,sBAAuB,KACjBC,EAAMC,WAAW,IAAID,EAAMC,WAAW,CAACsB,YAAY,IAAIvB,EAAMC,WAAW,CAACsB,YAAY,CAAC3B,EAAO,CAKnGO,CALqG,kBAKjF,mBACtB,GAEaqB,EAAgB1B,CAAAA,EAAAA,EAAAA,CAAAA,CAAoBA,CAAC,CAChDC,sBAAuB,GACrB,EAAIC,EAAMC,WAAW,IAAID,EAAMC,WAAW,CAACwB,SAAS,IAAIzB,EAAMC,WAAW,CAACwB,SAAS,CAAC7B,EAAO,IAAII,EAAMC,WAAW,CAACyB,OAAO,IAAI1B,EAAMC,WAAW,CAACyB,OAAO,CAAC9B,EAAO,IAAGI,EAAMC,WAAW,CAAC0B,KAAK,IAAI3B,EAAMC,WAAW,CAAC0B,KAAK,CAAC/B,EAAO,IAAGI,EAAMC,WAAW,CAAC2B,MAAM,IAAI5B,EAAMC,WAAW,CAAC2B,MAAM,CAAChC,EAAO,IAAGI,EAAMC,WAAW,CAACU,WAAW,IAAIX,EAAMC,WAAW,CAACU,WAAW,CAACf,EAAO,IAAGI,EAAMC,WAAW,CAAC4B,MAAM,IAAI7B,EAAMC,WAAW,CAAC4B,MAAM,CAACjC,EAAO,EAAE,CAG5Z,EAETO,mBAAoB,eACtB,GAAG,EAEYN,gBAAgBA,EAAC,2FC1LhC,SAASiC,EAASC,CAAoB,EACpC,GAAM,CAAEC,GAAC,CAAE,CAAGC,CAAAA,EAAAA,EAAAA,EAAAA,CAAcA,CAAC,UACvBC,EAA6B,CACjCC,gBAAiBH,EAAE,cACnB,EACI,SACJI,CAAO,MACPC,CAAI,WACJC,CAAS,uBACTC,CAAqB,WACrBC,CAAS,oBACTC,CAAkB,qBAClBC,CAAmB,kBACnBC,CAAgB,aAChBC,CAAW,oBACXC,CAAkB,CAClBC,gBAAc,SACdC,CAAO,WACPC,CAAS,sBACTC,CAAoB,mBACpBC,CAAiB,YACjBC,CAAU,QACVC,CAAM,kBACNC,CAAgB,cAChBC,CAAY,CAEZ,CADA,EACGC,EACJ,CAAGxB,EAGEyB,EAAiB,4BACrBtB,EACAuB,gBAAiBzB,EAAE,IAP0C,MAQ7D0B,UAAU,UACVtB,EACAC,KAAMA,GAAQ,EAAE,CAChBsB,OAAO,EACPC,2BAA4BrB,EAC5BsB,UAAWrB,EACXsB,gBAAiBf,EACjBN,qBACAsB,WAAY,GACZC,iBAAkBhB,EAClBiB,kBAAmBpB,GAA0C,GAC7DqB,eADwCrB,cACVD,GAA4B,CAAC,GAAI,GAAI,GAAI,CAA3BA,EAA+B,GAAG,CAC9EuB,oBAAqB7B,EACrB8B,oBAAqB1B,EACrB2B,aAAc1B,EACdG,sCACAG,oBACAC,EACAoB,kBAAmB,UAACC,EAAAA,CAAYA,CAAAA,CAAAA,GAChCC,SAAU,UAACC,IAAAA,CAAEC,UAAU,6CACvBvB,SACAC,eACAE,mBACAD,EACAqB,UAAW,WACb,EACA,MACE,UAACC,EAAAA,EAASA,CAAAA,CAAE,GAAGnB,CAAc,EAEjC,CAEA1B,EAAS8C,YAAY,CAAG,CACtBf,WAAW,EACXE,YAAY,EACZzB,UAAW,KACXU,WAAW,EACXC,qBAAsB,KACtBC,mBAAmB,EACnBC,YAAY,EACZE,kBAAkB,CACpB,EAEA,MAAevB,QAAQA,EAAC,mEChHT,SAAS+C,EAAgBC,CAAW,EAC/C,MACE,UAACC,MAAAA,CAAIL,UAAU,sDACb,UAACK,MAAAA,CAAIL,UAAU,mBAAU,yCAG/B,0JCyBF,MApByB,QAajB1E,EAAAA,EAZN,GAAM,GAAEgC,CAAC,CAAE,CAAGC,CAAAA,EAAAA,EAAAA,EAAAA,CAAcA,CAAC,UACvB+C,EAAmB,EAkBIC,EAhBzB,WAACC,EAAAA,CAASA,CAAAA,CAACC,KAAK,IAACT,UAAU,gBACzB,UAACU,EAAAA,CAAWA,CAAAA,CAACC,MAAOrD,EAAE,uCACtB,UAACsD,EAAAA,OAAWA,CAAAA,CAAAA,MAKZC,EAAwBC,CAAAA,EAAAA,EAAAA,oBAAAA,CAAoBA,CAAC,IAAM,UAACR,EAAAA,CAAAA,IACpDhF,EAAYyF,CAAAA,EAAAA,EAAAA,EAAAA,CAAWA,CAAC,GAAgBzF,SAC9C,IAAI,GAAEA,GAAAA,OAAAA,EAAAA,EAAOC,IAAPD,OAAkB,GAAlBA,OAAAA,EAAAA,EAAAA,uBAAoBQ,EAApBR,KAAAA,EAAAA,CAA6C,CAAC,GAA9CA,UAA2D,EAI/D,CAJkE,EAIlE,OAACuF,EAAAA,CAAAA,GAHM,UAACV,EAAAA,OAAeA,CAAAA,CAAAA,EAK3B,0JC4IA,MAhKA,SAASS,CAAuB,EAC9B,GAAM,GAAEtD,CAAC,CAAE,CAAGC,CAAAA,EAAAA,EAAAA,EAAAA,CAAcA,CAAC,SA+JLqD,CA9JlB,CAACI,EAAWC,EAAe,CAAGC,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAQ,EAAE,EAChD,EAAGC,EAAW,CAAGD,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,EAAU,GACnC,CAACtD,EAAWwD,EAAa,CAAGF,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAS,GAC7C,CAACG,EAASC,EAAW,CAAGJ,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAS,IACzC,CAACK,EAAaC,EAAS,CAAGN,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,EAAU,GAC5C,CAACO,EAAWC,EAAa,CAAGR,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAS,IAC7C,CAACS,EAAmBC,EAAqB,CAAGV,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAM,CAAC,GAG3DW,EAAc,CAClBC,KAAM,CAAEC,WAAY,MAAO,EAC3BC,MAAOX,EACPY,KAAM,EACNC,MAAO,CAAEC,cAAe,iBAAkB,CAC5C,EAEMzE,EAAU,CACd,CACE0E,KAAM9E,EAAE,kDACR+E,SAAU,WACVC,KAAOC,GAAWA,EAAEC,QAAQ,EAE9B,CACEJ,KAAM9E,EAAE,+CACR+E,SAAU,QACVC,KAAM,GAAYC,EAAEE,KAAK,EAE3B,CACEL,KAAM9E,EAAE,gDACR+E,SAAU,GACVC,KAAM,GACJ,WAACjC,MAAAA,WACC,UAACqC,EAAAA,CAAMA,CAAAA,CACLC,QAAQ,UACRC,KAAK,KACLC,QAAS,IAAMC,EAAWP,EAAG,oBAE5BjF,EAAE,iDACI,OAET,UAACoF,EAAAA,CAAMA,CAAAA,CACLC,QAAQ,YACRC,KAAK,KACLC,QAAS,IAAMC,EAAWP,EAAG,mBAE5BjF,EAAE,oDAIX,EACD,CAEKyF,EAAe,UAEnB5B,GAAW,GACX,IAAM6B,EAAW,MAAMC,EAAAA,CAAUA,CAACC,GAAG,CAAC,SAAUrB,GAC5CmB,GAAYA,EAASrF,IAAI,EAAE,CAC7BsD,EAAe+B,EAASrF,IAAI,EAC5ByD,EAAa4B,EAASG,UAAU,EAChChC,GAAW,GAEf,EAOMnD,EAAsB,MAAOoF,EAAoBnB,KACrDJ,EAAYG,KAAK,CAAGoB,EACpBvB,EAAYI,IAAI,CAAGA,EACnBd,GAAW,GACX,IAAM6B,EAAW,MAAMC,EAAAA,CAAUA,CAACC,GAAG,CAAC,SAAUrB,GAC5CmB,GAAYA,EAASrF,IAAI,EAAIqF,EAASrF,IAAI,CAAC0F,MAAM,CAAG,GAAG,CACzDpC,EAAe+B,EAASrF,IAAI,EAC5B2D,EAAW8B,GACXjC,GAAW,GAEf,EAEAmC,CAAAA,EAAAA,EAAAA,SAAAA,CAASA,CAAC,KACRP,GACF,EAAG,EAAE,EAEL,IAAMD,EAAa,MAAOP,EAAQgB,KAChC/B,GAAS,GACTE,EAAa6B,GACThB,GAAKA,EAAEiB,GAAG,EAAE,EAEO,CAAE,GAAGjB,CAAC,CAAEJ,cADA,CACesB,WAD1BF,EAAuB,WAAY,UACC,EAG1D,EAEMG,EAAe,UAEnB,GAA2C,YAAY,CAAnD/B,EAAkB,aAAgB,CAEpC,CAFmB,KAEbsB,EAAAA,CAAUA,CAACU,MAAM,CAAC,UAAmC,OAAzBhC,EAAkB,GAAM,GAC1DoB,IACAa,EAAAA,EAAKA,CAACC,KAAK,CAACvG,EAAE,mDACdsE,EAAqB,CAAC,GACtBJ,GAAS,OAEJ,CACL,IAAMsC,EAAc,MAAMb,EAAAA,CAAUA,CAACc,KAAK,CACxC,UAAmC,OAAzBpC,EAAkB,GAAM,EAClCA,GAEF,GAAImC,GAAsC,CAHb,KAGVA,EAAYP,MAAM,CAAU,YAC7CK,EAAAA,EAAKA,CAACC,KAAK,CACTC,EAAYd,QAAQ,EAAIc,EAAYd,QAAQ,CAACgB,OAAO,CAChDF,EAAYd,QAAQ,CAACgB,OAAO,CAC5B1G,EAAE,8DAIRyF,IACAa,EAAAA,EAAKA,CAACK,OAAO,CAAC3G,EAAE,oDAChBsE,EAAqB,CAAC,GACtBJ,GAAS,EAEb,CACF,EAEM0C,EAAY,IAAM1C,GAAS,GAEjC,MACE,WAACnB,MAAAA,WACC,WAAC8D,EAAAA,CAAKA,CAAAA,CAACC,KAAM7C,EAAa8C,OAAQH,YAChC,UAACC,EAAAA,CAAKA,CAACG,MAAM,EAACC,WAAW,aACvB,WAACJ,EAAAA,CAAKA,CAACK,KAAK,YACT/C,EAAUgD,MAAM,CAAC,GAAGC,WAAW,GAAKjD,EAAUkD,KAAK,CAAC,GAAG,IAAErH,EAAE,mDAGhE,WAAC6G,EAAAA,CAAKA,CAACS,IAAI,YAAEtH,EAAE,0DAA0D,IAAEmE,EAAU,IAAEnE,EAAE,sDACzF,WAAC6G,EAAAA,CAAKA,CAACU,MAAM,YACX,UAACnC,EAAAA,CAAMA,CAAAA,CAACC,QAAQ,YAAYE,QAASqB,WAClC5G,EAAE,kDAEL,UAACoF,EAAAA,CAAMA,CAAAA,CAACC,QAAQ,UAAUE,QAASa,WAChCpG,EAAE,qDAKT,UAACF,EAAAA,CAAQA,CAAAA,CACPM,QAASA,EACTC,KAAMqD,EACNpD,UAAWA,EACXU,WAAW,EACXN,oBAAqBA,EACrBC,iBA1FmB,CA0FDA,GAzFtB4D,EAAYG,KAAK,CAAGX,EACpBQ,EAAYI,IAAI,CAAGA,EACnBc,GACF,MA0FF,gECpKe,SAASrC,EAAYrD,CAAuB,EACzD,MACE,UAACyH,KAAAA,CAAG9E,UAAU,wBAAgB3C,EAAMsD,KAAK,EAE7C,mBCPA,4CACA,yCACA,WACA,OAAe,EAAQ,KAA8D,CACrF,EACA,SAFsB", "sources": ["webpack://_N_E/./pages/adminsettings/permissions.tsx", "webpack://_N_E/./components/common/RKITable.tsx", "webpack://_N_E/./pages/rNoAccess.tsx", "webpack://_N_E/./pages/adminsettings/approval/vspace_appoval.tsx", "webpack://_N_E/./pages/adminsettings/approval/VspaceAdmin.tsx", "webpack://_N_E/./components/common/PageHeading.tsx", "webpack://_N_E/?a769"], "sourcesContent": ["//Import services/components\r\nimport connectedAuthWrapper from 'redux-auth-wrapper/connectedAuthWrapper';\r\n\r\nconst create = \"create:any\";\r\nexport const canAddAreaOfWork = connectedAuthWrapper({\r\n  authenticatedSelector: (state) => {\r\n    if (state.permissions && state.permissions.area_of_work && state.permissions.area_of_work[create]) {\r\n      return true;\r\n    }\r\n    return false;\r\n  },\r\n  wrapperDisplayName: 'CanAddAreaOfWork',\r\n});\r\n\r\nexport const canAddCountry = connectedAuthWrapper({\r\n  authenticatedSelector: (state) => {\r\n    if (state.permissions && state.permissions.country && state.permissions.country[create]) {\r\n      return true;\r\n    }\r\n    return false;\r\n  },\r\n  wrapperDisplayName: 'CanAddCountry',\r\n});\r\n\r\nexport const canAddDeploymentStatus = connectedAuthWrapper({\r\n  authenticatedSelector: (state) => {\r\n    if (state.permissions && state.permissions.deployment_status && state.permissions.deployment_status[create]) {\r\n      return true;\r\n    }\r\n    return false;\r\n  },\r\n  wrapperDisplayName: 'CanAddDeploymentStatus',\r\n});\r\n\r\nexport const canAddEventStatus = connectedAuthWrapper({\r\n  authenticatedSelector: (state) => {\r\n    if (state.permissions && state.permissions.event_status && state.permissions.event_status[create]) {\r\n      return true;\r\n    }\r\n    return false;\r\n  },\r\n  wrapperDisplayName: 'CanAddEventStatus',\r\n});\r\n\r\nexport const canAddExpertise = connectedAuthWrapper({\r\n  authenticatedSelector: (state) => {\r\n    if (state.permissions && state.permissions.expertise && state.permissions.expertise[create]) {\r\n      return true;\r\n    }\r\n    return false;\r\n  },\r\n  wrapperDisplayName: 'CanAddExpertise',\r\n});\r\n\r\nexport const canAddFocalPointApproval = connectedAuthWrapper({\r\n  authenticatedSelector: (state) => {\r\n    if (state.permissions && state.permissions.institution_focal_point && state.permissions.institution_focal_point[create]) {\r\n      return true;\r\n    }\r\n    return false;\r\n  },\r\n  wrapperDisplayName: 'CanAddFocalPointApproval',\r\n});\r\n\r\nexport const canAddVspaceApproval = connectedAuthWrapper({\r\n  authenticatedSelector: (state) => {\r\n    if (state.permissions && state.permissions.institution_focal_point && state.permissions.institution_focal_point[create]) {\r\n      return true;\r\n    }\r\n    return false;\r\n  },\r\n  wrapperDisplayName: 'CanAddVspaceApproval',\r\n});\r\nexport const canAddHazards = connectedAuthWrapper({\r\n  authenticatedSelector: (state) => {\r\n    if (state.permissions && state.permissions.hazard && state.permissions.hazard[create]) {\r\n      return true;\r\n    }\r\n    return false;\r\n  },\r\n  wrapperDisplayName: 'CanAddHazards',\r\n});\r\n\r\nexport const canAddHazardTypes = connectedAuthWrapper({\r\n  authenticatedSelector: (state) => {\r\n    if (state.permissions && state.permissions.hazard_type && state.permissions.hazard_type[create]) {\r\n      return true;\r\n    }\r\n    return false;\r\n  },\r\n  wrapperDisplayName: 'CanAddHazardTypes',\r\n}); \r\n\r\nexport const canAddOrganisationApproval = connectedAuthWrapper({\r\n  authenticatedSelector: (state) => {\r\n    if (state.permissions && state.permissions.institution && state.permissions.institution[create]) {\r\n      return true;\r\n    }\r\n    return false;\r\n  },\r\n  wrapperDisplayName: 'CanAddOrganisationApproval',\r\n}); \r\n\r\nexport const canAddOrganisationNetworks = connectedAuthWrapper({\r\n  authenticatedSelector: (state) => {\r\n    if (state.permissions && state.permissions.institution_network && state.permissions.institution_network[create]) {\r\n      return true;\r\n    }\r\n    return false;\r\n  },\r\n  wrapperDisplayName: 'CanAddOrganisationNetworks',\r\n});\r\n\r\nexport const canAddOrganisationTypes = connectedAuthWrapper({\r\n  authenticatedSelector: (state) => {\r\n    if (state.permissions && state.permissions.institution_type && state.permissions.institution_type[create]) {\r\n      return true;\r\n    }\r\n    return false;\r\n  },\r\n  wrapperDisplayName: 'CanAddOrganisationTypes',\r\n});\r\n\r\nexport const canAddOperationStatus = connectedAuthWrapper({\r\n  authenticatedSelector: (state) => {\r\n    if (state.permissions && state.permissions.operation_status && state.permissions.operation_status[create]) {\r\n      return true;\r\n    }\r\n    return false;\r\n  },\r\n  wrapperDisplayName: 'CanAddOperationStatus',\r\n});\r\n\r\nexport const canAddProjectStatus = connectedAuthWrapper({\r\n  authenticatedSelector: (state) => {\r\n    if (state.permissions && state.permissions.project_status && state.permissions.project_status[create]) {\r\n      return true;\r\n    }\r\n    return false;\r\n  },\r\n  wrapperDisplayName: 'CanAddProjectStatus',\r\n});\r\n\r\nexport const canAddRegions = connectedAuthWrapper({\r\n  authenticatedSelector: (state) => {\r\n    if (state.permissions && state.permissions.region && state.permissions.region[create]) {\r\n      return true;\r\n    }\r\n    return false;\r\n  },\r\n  wrapperDisplayName: 'CanAddRegions',\r\n});\r\n\r\nexport const canAddRiskLevels = connectedAuthWrapper({\r\n  authenticatedSelector: (state) => {\r\n    if (state.permissions && state.permissions.risk_level && state.permissions.risk_level[create]) {\r\n      return true;\r\n    }\r\n    return false;\r\n  },\r\n  wrapperDisplayName: 'CanAddRiskLevels',\r\n});\r\n\r\nexport const canAddSyndromes = connectedAuthWrapper({\r\n  authenticatedSelector: (state) => {\r\n    if (state.permissions && state.permissions.syndrome && state.permissions.syndrome[create]) {\r\n      return true;\r\n    }\r\n    return false;\r\n  },\r\n  wrapperDisplayName: 'CanAddSyndromes',\r\n});\r\n\r\nexport const canAddUpdateTypes = connectedAuthWrapper({\r\n  authenticatedSelector: (state) => {\r\n    if (state.permissions && state.permissions.update_type && state.permissions.update_type[create]) {\r\n      return true;\r\n    }\r\n    return false;\r\n  },\r\n  wrapperDisplayName: 'CanAddUpdateTypes',\r\n});\r\n\r\nexport const canAddUsers = connectedAuthWrapper({\r\n  authenticatedSelector: (state) => {\r\n    if (state.permissions && state.permissions.users && state.permissions.users[create]) {\r\n      return true;\r\n    }\r\n    return false;\r\n  },\r\n  wrapperDisplayName: 'CanAddUsers',\r\n});\r\n\r\nexport const canAddWorldRegion = connectedAuthWrapper({\r\n  authenticatedSelector: (state) => {\r\n    if (state.permissions && state.permissions.worl_region && state.permissions.worl_region[create]) {\r\n      return true;\r\n    }\r\n    return false;\r\n  },\r\n  wrapperDisplayName: 'CanAddWorldRegion',\r\n});\r\n\r\nexport const canAddLandingPage = connectedAuthWrapper({\r\n  authenticatedSelector: (state) => {\r\n    if (state.permissions && state.permissions.landing_page && state.permissions.landing_page[create]) {\r\n      return true;\r\n    }\r\n    return false;\r\n  },\r\n  wrapperDisplayName: 'CanAddLandingPage',\r\n});\r\n\r\nexport const canAddContent = connectedAuthWrapper({\r\n  authenticatedSelector: (state) => {\r\n    if (state.permissions && state.permissions.operation && state.permissions.operation[create] && state.permissions.project && state.permissions.project[create]&& state.permissions.event && state.permissions.event[create]&& state.permissions.vspace && state.permissions.vspace[create]&& state.permissions.institution && state.permissions.institution[create]&& state.permissions.update && state.permissions.update[create]) {\r\n      return true;\r\n    }\r\n    return false;\r\n  },\r\n  wrapperDisplayName: 'CanAddContent',\r\n});\r\n\r\nexport default canAddAreaOfWork;", "//Import Library\r\nimport React from 'react'\r\nimport DataTable  from 'react-data-table-component';\r\n\r\n//Import services/components\r\nimport { useTranslation } from 'next-i18next';\r\nimport CustomLoader from './CustomLoader';\r\n\r\ninterface RKITableProps {\r\n  columns: any[];\r\n  data: any[];\r\n  totalRows?: number;\r\n  resetPaginationToggle?: boolean;\r\n  subheader?: boolean;\r\n  subHeaderComponent?: React.ReactNode;\r\n  handlePerRowsChange?: (newPerPage: number, page: number) => void;\r\n  handlePageChange?: (page: number) => void;\r\n  rowsPerPage?: number[];\r\n  defaultRowsPerPage?: number;\r\n  selectableRows?: boolean;\r\n  loading?: boolean;\r\n  pagServer?: boolean;\r\n  onSelectedRowsChange?: (selectedRows: any) => void;\r\n  clearSelectedRows?: boolean;\r\n  sortServer?: boolean;\r\n  onSort?: (column: any, sortDirection: string) => void;\r\n  persistTableHead?: boolean;\r\n  sortFunction?: (rows: any[], field: any, direction: 'asc' | 'desc') => any[];\r\n  noHeader?: boolean;\r\n  dense?: boolean;\r\n  pagination?: boolean;\r\n  paginationServer?: boolean;\r\n  paginationTotalRows?: number;\r\n  subHeaderAlign?: string;\r\n  [key: string]: any;\r\n}\r\n\r\nfunction RKITable(props: RKITableProps) {\r\n  const { t } = useTranslation('common');\r\n  const paginationComponentOptions = {\r\n    rowsPerPageText: t('Rowsperpage'),\r\n    };\r\n  const {\r\n    columns,\r\n    data,\r\n    totalRows,\r\n    resetPaginationToggle,\r\n    subheader,\r\n    subHeaderComponent,\r\n    handlePerRowsChange,\r\n    handlePageChange,\r\n    rowsPerPage,\r\n    defaultRowsPerPage,\r\n    selectableRows,\r\n    loading,\r\n    pagServer,\r\n    onSelectedRowsChange,\r\n    clearSelectedRows,\r\n    sortServer,\r\n    onSort,\r\n    persistTableHead,\r\n    sortFunction,\r\n    // Filter out any unknown props that might cause DOM warnings\r\n    ...otherProps\r\n  } = props;\r\n\r\n  // Only pass known DataTable props to avoid DOM warnings\r\n  const dataTableProps = {\r\n    paginationComponentOptions,\r\n    noDataComponent: t(\"NoData\"),\r\n    noHeader: true,\r\n    columns,\r\n    data: data || [],\r\n    dense: true,\r\n    paginationResetDefaultPage: resetPaginationToggle,\r\n    subHeader: subheader,\r\n    progressPending: loading,\r\n    subHeaderComponent,\r\n    pagination: true,\r\n    paginationServer: pagServer,\r\n    paginationPerPage: defaultRowsPerPage ? defaultRowsPerPage : 10,\r\n    paginationRowsPerPageOptions: rowsPerPage ? rowsPerPage : [10, 15, 20, 25, 30],\r\n    paginationTotalRows: totalRows,\r\n    onChangeRowsPerPage: handlePerRowsChange,\r\n    onChangePage: handlePageChange,\r\n    selectableRows,\r\n    onSelectedRowsChange,\r\n    clearSelectedRows,\r\n    progressComponent: <CustomLoader/>,\r\n    sortIcon: <i className=\"sort-icon fas fa-exchange-alt\" />,\r\n    sortServer,\r\n    onSort,\r\n    sortFunction,\r\n    persistTableHead,\r\n    className: \"rki-table\"\r\n  };\r\n  return (\r\n    <DataTable {...dataTableProps} />\r\n  )\r\n};\r\n\r\nRKITable.defaultProps = {\r\n  subHeader: false,\r\n  pagination: true,\r\n  totalRows: null,\r\n  pagServer: true,\r\n  onSelectedRowsChange: null,\r\n  clearSelectedRows: false,\r\n  sortServer: false,\r\n  persistTableHead: false\r\n}\r\n\r\nexport default RKITable;\r\n\r\n", "export default function NoAccessMessage(_props: any) {\r\n    return (\r\n      <div className=\"container-fluid p-0 response-message-block\">\r\n        <div className=\"message\">you don't have permission to access</div>\r\n      </div>\r\n    )\r\n  }", "//Import Library\r\nimport { Container } from \"react-bootstrap\";\r\n\r\n//Import services/components\r\nimport PageHeading from \"../../../components/common/PageHeading\";\r\nimport { useTranslation } from 'next-i18next';\r\nimport VspaceAdmin from \"./VspaceAdmin\";\r\nimport { canAddVspaceApproval } from \"../permissions\";\r\nimport { useSelector } from \"react-redux\";\r\nimport NoAccessMessage from \"../../rNoAccess\";\r\n\r\nconst VirtualSpaceShow = (_props: any) => {\r\n  const { t } = useTranslation('common');\r\n  const ShowVirtualSpace = () => {\r\n    return (\r\n      <Container fluid className=\"p-0\">\r\n        <PageHeading title={t(\"adminsetting.VirtualspaceApproval\")} />\r\n        <VspaceAdmin />\r\n      </Container>\r\n    )\r\n  };\r\n\r\n  const ShowAddVspaceApproval = canAddVspaceApproval(() => <ShowVirtualSpace />);\r\n  const state:any = useSelector((state: any) => state);\r\n  if (!(state?.permissions?.institution_focal_point?.['create:any'])) {\r\n    return <NoAccessMessage />\r\n  }\r\n  return(\r\n    <ShowAddVspaceApproval />\r\n  );  \r\n}\r\nexport default VirtualSpaceShow;\r\n", "//Import Library\r\nimport React, { useEffect, useState } from \"react\";\r\nimport { <PERSON><PERSON>, <PERSON><PERSON> } from \"react-bootstrap\";\r\nimport toast from 'react-hot-toast';\r\n\r\n//Import services/components\r\nimport apiService from \"../../../services/apiService\";\r\nimport RKITable from \"../../../components/common/RKITable\";\r\nimport { useTranslation } from 'next-i18next';\r\n\r\nfunction VspaceAdmin(_props: any) {\r\n  const { t } = useTranslation('common');\r\n  const [tabledata, setDataToTable] = useState<any[]>([]);\r\n  const [, setLoading] = useState<boolean>(false);\r\n  const [totalRows, setTotalRows] = useState<number>(0);\r\n  const [perPage, setPerPage] = useState<number>(10);\r\n  const [isModalShow, setModal] = useState<boolean>(false);\r\n  const [newStatus, setNewStatus] = useState<string>(\"\");\r\n  const [selectUserDetails, setSelectUserDetails] = useState<any>({});\r\n\r\n\r\n  const usersParams = {\r\n    sort: { created_at: \"desc\" },\r\n    limit: perPage,\r\n    page: 1,\r\n    query: { vspace_status: \"Request Pending\" },\r\n  };\r\n\r\n  const columns = [\r\n    {\r\n      name: t(\"adminsetting.FocalPointsApprovalTable.Username\"),\r\n      selector: \"username\",\r\n      cell: (d: any) => d.username,\r\n    },\r\n    {\r\n      name: t(\"adminsetting.FocalPointsApprovalTable.Email\"),\r\n      selector: \"email\",\r\n      cell: (d: any) => d.email,\r\n    },\r\n    {\r\n      name: t(\"adminsetting.FocalPointsApprovalTable.Action\"),\r\n      selector: \"\",\r\n      cell: (d: any) => (\r\n        <div>\r\n          <Button\r\n            variant=\"primary\"\r\n            size=\"sm\"\r\n            onClick={() => userAction(d, \"approve\")}\r\n          >\r\n            {t(\"adminsetting.FocalPointsApprovalTable.aprov\")}\r\n          </Button>\r\n          &nbsp;\r\n          <Button\r\n            variant=\"secondary\"\r\n            size=\"sm\"\r\n            onClick={() => userAction(d, \"reject\")}\r\n          >\r\n            {t(\"adminsetting.FocalPointsApprovalTable.Reject\")}\r\n          </Button>\r\n        </div>\r\n      ),\r\n    },\r\n  ];\r\n\r\n  const getUsersData = async () => {\r\n\r\n    setLoading(true);\r\n    const response = await apiService.get(\"/users\", usersParams);\r\n    if (response && response.data) {\r\n      setDataToTable(response.data);\r\n      setTotalRows(response.totalCount);\r\n      setLoading(false);\r\n    }\r\n  };\r\n  const handlePageChange = (page: number) => {\r\n    usersParams.limit = perPage;\r\n    usersParams.page = page;\r\n    getUsersData();\r\n  };\r\n\r\n  const handlePerRowsChange = async (newPerPage: number, page: number) => {\r\n    usersParams.limit = newPerPage;\r\n    usersParams.page = page;\r\n    setLoading(true);\r\n    const response = await apiService.get(\"/users\", usersParams);\r\n    if (response && response.data && response.data.length > 0) {\r\n      setDataToTable(response.data);\r\n      setPerPage(newPerPage);\r\n      setLoading(false);\r\n    }\r\n  };\r\n\r\n  useEffect(() => {\r\n    getUsersData();\r\n  }, []);\r\n\r\n  const userAction = async (d: any, status: string) => {\r\n    setModal(true);\r\n    setNewStatus(status);\r\n    if (d && d._id) {\r\n      const setStatus = status === \"approve\" ? \"Approved\" :\"Rejected\";\r\n      setSelectUserDetails({ ...d, vspace_status: setStatus });\r\n    }\r\n\r\n  };\r\n\r\n  const modalConfirm = async () => {\r\n\r\n    if( selectUserDetails['vspace_status'] === \"Rejected\" ){\r\n\r\n      await apiService.remove(`/users/${selectUserDetails[\"_id\"]}`);\r\n      getUsersData();\r\n      toast.error(t(\"adminsetting.FocalPointsApprovalTable.Rejected\"));\r\n      setSelectUserDetails({});\r\n      setModal(false);\r\n\r\n    } else {\r\n      const updatedData = await apiService.patch(\r\n        `/users/${selectUserDetails[\"_id\"]}`,\r\n        selectUserDetails\r\n      );\r\n      if (updatedData && updatedData.status === 403) {\r\n        toast.error(\r\n          updatedData.response && updatedData.response.message\r\n            ? updatedData.response.message\r\n            : t(\"adminsetting.FocalPointsApprovalTable.Somethingwentswrong\")\r\n        );\r\n        return;\r\n      } else {\r\n        getUsersData();\r\n        toast.success(t(\"adminsetting.FocalPointsApprovalTable.Approvemm\"));\r\n        setSelectUserDetails({});\r\n        setModal(false);\r\n      }\r\n    }\r\n  };\r\n\r\n  const modalHide = () => setModal(false);\r\n\r\n  return (\r\n    <div>\r\n      <Modal show={isModalShow} onHide={modalHide}>\r\n        <Modal.Header closeButton>\r\n          <Modal.Title>\r\n            {newStatus.charAt(0).toUpperCase() + newStatus.slice(1)} {t(\"adminsetting.FocalPointsApprovalTable.User\")}\r\n          </Modal.Title>\r\n        </Modal.Header>\r\n        <Modal.Body>{t(\"adminsetting.FocalPointsApprovalTable.Areyousurewantto\")} {newStatus} {t(\"adminsetting.FocalPointsApprovalTable.thisuser?\")}</Modal.Body>\r\n        <Modal.Footer>\r\n          <Button variant=\"secondary\" onClick={modalHide}>\r\n            {t(\"adminsetting.FocalPointsApprovalTable.Cancel\")}\r\n          </Button>\r\n          <Button variant=\"primary\" onClick={modalConfirm}>\r\n            {t(\"adminsetting.FocalPointsApprovalTable.Yes\")}\r\n          </Button>\r\n        </Modal.Footer>\r\n      </Modal>\r\n\r\n      <RKITable\r\n        columns={columns}\r\n        data={tabledata}\r\n        totalRows={totalRows}\r\n        pagServer={true}\r\n        handlePerRowsChange={handlePerRowsChange}\r\n        handlePageChange={handlePageChange}\r\n      />\r\n    </div>\r\n  );\r\n}\r\n\r\nexport default VspaceAdmin;\r\n", "interface PageHeadingProps {\r\n  title: string; // Required based on actual usage\r\n}\r\n\r\nexport default function PageHeading(props: PageHeadingProps) {\r\n  return (\r\n    <h2 className=\"page-heading\">{props.title}</h2>\r\n  )\r\n}\r\n", "\n    (window.__NEXT_P = window.__NEXT_P || []).push([\n      \"/adminsettings/approval/vspace_appoval\",\n      function () {\n        return require(\"private-next-pages/adminsettings/approval/vspace_appoval.tsx\");\n      }\n    ]);\n    if(module.hot) {\n      module.hot.dispose(function () {\n        window.__NEXT_P.push([\"/adminsettings/approval/vspace_appoval\"])\n      });\n    }\n  "], "names": ["create", "canAddAreaOfWork", "connectedAuthWrapper", "authenticatedSelector", "state", "permissions", "area_of_work", "wrapperDisplayName", "country", "deployment_status", "event_status", "expertise", "institution_focal_point", "hazard", "hazard_type", "institution", "institution_network", "canAddOrganisationTypes", "institution_type", "operation_status", "project_status", "region", "risk_level", "syndrome", "update_type", "users", "worl_region", "landing_page", "canAddContent", "operation", "project", "event", "vspace", "update", "RKITable", "props", "t", "useTranslation", "paginationComponentOptions", "rowsPerPageText", "columns", "data", "totalRows", "resetPaginationToggle", "subheader", "subHeaderComponent", "handlePerRowsChange", "handlePageChange", "rowsPerPage", "defaultRowsPerPage", "selectableRows", "loading", "pagServer", "onSelectedRowsChange", "clearSelectedRows", "sortServer", "onSort", "persistTableHead", "sortFunction", "otherProps", "dataTableProps", "noDataComponent", "<PERSON><PERSON><PERSON><PERSON>", "dense", "paginationResetDefaultPage", "subHeader", "progressPending", "pagination", "paginationServer", "paginationPerPage", "paginationRowsPerPageOptions", "paginationTotalRows", "onChangeRowsPerPage", "onChangePage", "progressComponent", "CustomLoader", "sortIcon", "i", "className", "DataTable", "defaultProps", "NoAccessMessage", "_props", "div", "ShowVirtualSpace", "VirtualSpaceShow", "Container", "fluid", "PageHeading", "title", "VspaceAdmin", "ShowAddVspaceApproval", "canAddVspaceApproval", "useSelector", "tabledata", "setDataToTable", "useState", "setLoading", "setTotalRows", "perPage", "setPerPage", "isModalShow", "setModal", "newStatus", "setNewStatus", "selectUserDetails", "setSelectUserDetails", "usersParams", "sort", "created_at", "limit", "page", "query", "vspace_status", "name", "selector", "cell", "d", "username", "email", "<PERSON><PERSON>", "variant", "size", "onClick", "userAction", "getUsersData", "response", "apiService", "get", "totalCount", "newPerPage", "length", "useEffect", "status", "_id", "setStatus", "modalConfirm", "remove", "toast", "error", "updatedData", "patch", "message", "success", "modalHide", "Modal", "show", "onHide", "Header", "closeButton", "Title", "char<PERSON>t", "toUpperCase", "slice", "Body", "Footer", "h2"], "sourceRoot": "", "ignoreList": []}