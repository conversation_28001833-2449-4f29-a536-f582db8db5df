{"version": 3, "file": "static/chunks/pages/vspace/DocumentAccordian-da2e9956020764cb.js", "mappings": "+EACA,4CACA,4BACA,WACA,OAAe,EAAQ,KAAiD,CACxE,EACA,SAFsB,sIC6BtB,MAvB0B,IACtB,GAAM,GAAEA,CAAC,CAAE,CAAGC,CAAAA,EAAAA,EAAAA,EAAAA,CAAcA,CAAC,MAsBlBC,IArBL,CAACC,EAASC,EAAW,CAAGC,CAAAA,EAAAA,EAAAA,EAqBFH,EAAC,IArBCG,CAAQA,EAAC,GACvC,MACI,+BACI,WAACC,EAAAA,CAASA,CAACC,IAAI,EAACC,SAAS,cACrB,WAACF,EAAAA,CAASA,CAACG,MAAM,EAACC,QAAS,IAAMN,EAAW,CAACD,aACzC,UAACQ,MAAAA,CAAIC,UAAU,qBAAaZ,EAAE,sBAC9B,UAACW,MAAAA,CAAIC,UAAU,qBACdT,EAAU,UAACU,EAAAA,CAAeA,CAAAA,CAACC,KAAMC,EAAAA,GAAOA,CAAEC,MAAM,SAC7C,UAACH,EAAAA,CAAeA,CAAAA,CAACC,KAAMG,EAAAA,GAAMA,CAAED,MAAM,cAG7C,WAACV,EAAAA,CAASA,CAACY,IAAI,YACX,UAACC,EAAAA,CAAaA,CAAAA,CAACC,QAASC,EAAMC,aAAa,CAAEC,UAAWF,EAAMG,UAAU,CAAEC,KAAMJ,EAAMK,uBAAuB,CAACC,UAAU,EAAI,EAAE,CAAEC,gBAAiBP,EAAMQ,cAAc,CAACC,MAAM,CAAG,GAAKT,EAAMQ,cAAc,CAACE,GAAG,CAAC,GAAeC,EAAKC,OAAO,EAAEC,IAAI,CAAC,KAC/O,UAACC,KAAAA,CAAGvB,UAAU,gBAAQZ,EAAE,iCACxB,UAACmB,EAAAA,CAAaA,CAAAA,CAACC,QAASC,EAAMK,uBAAuB,CAACU,aAAa,CAAEb,UAAWF,EAAMK,uBAAuB,CAACW,UAAU,CAAEZ,KAAMJ,EAAMiB,QAAQ,EAAI,EAAE,CAAEV,gBAAiBP,EAAMK,uBAAuB,CAACa,MAAM,UAK/N,6GCMA,SAASC,EAASnB,CAAoB,EACpC,GAAM,GAAErB,CAAC,CAAE,CAAGC,CAAAA,EAAAA,EAAAA,EAAAA,CAAcA,CAAC,UACvBwC,EAA6B,CACjCC,gBAAiB1C,EAAE,cACnB,EACI,SACJ2C,CAAO,MACPC,CAAI,WACJC,CAAS,CACTC,uBAAqB,WACrBC,CAAS,oBACTC,CAAkB,qBAClBC,CAAmB,kBACnBC,CAAgB,aAChBC,CAAW,oBACXC,CAAkB,gBAClBC,CAAc,SACdjC,CAAO,CACPkC,WAAS,sBACTC,CAAoB,mBACpBC,CAAiB,YACjBC,CAAU,QACVC,CAAM,kBACNC,CAAgB,cAChBC,CAAY,CAEZ,CADA,EACGC,EACJ,CAAGxC,EAGEyC,EAAiB,4BACrBrB,EACAsB,gBAAiB/D,EAAE,IAP0C,MAQ7DgE,UAAU,UACVrB,EACAC,KAAMA,GAAQ,EAAE,CAChBqB,OAAO,EACPC,2BAA4BpB,EAC5BqB,UAAWpB,EACXqB,gBAAiBhD,EACjB4B,qBACAqB,YAAY,EACZC,iBAAkBhB,EAClBiB,kBAAmBnB,GAA0C,GAC7DoB,eADwCpB,cACVD,GAA4B,CAAC,GAAI,GAAI,GAAI,CAA3BA,EAA+B,GAAG,CAC9EsB,oBAAqB5B,EACrB6B,oBAAqBzB,EACrB0B,aAAczB,iBACdG,EACAE,yCACAC,EACAoB,kBAAmB,UAACC,EAAAA,CAAYA,CAAAA,CAAAA,GAChCC,SAAU,UAACC,IAAAA,CAAEnE,UAAU,6CACvB6C,SACAC,EACAE,gCACAD,EACA/C,UAAW,WACb,EACA,MACE,UAACoE,EAAAA,EAASA,CAAAA,CAAE,GAAGlB,CAAc,EAEjC,CAEAtB,EAASyC,YAAY,CAAG,CACtBd,WAAW,EACXE,YAAY,EACZxB,UAAW,KACXS,WAAW,EACXC,qBAAsB,KACtBC,mBAAmB,EACnBC,YAAY,EACZE,kBAAkB,CACpB,EAEA,MAAenB,QAAQA,EAAC,6GC3CxB,MAtDoD,OAAC,MAAEf,CAAI,cAsD5CN,GAtD8CS,CAAe,SAsDhDT,EAtDkDI,CAAS,SAAEH,CAAO,CAAE,GAE1F8D,EAAa,MAAOC,EAAaC,KAKrC7D,EAJiB,CACf8D,OAGQC,QAHQH,EAAOI,QAAQ,CAC/BH,cAAeA,CACjB,EAEF,EAEM,GAAEpF,CAAC,CAAE,CAAGC,CAAAA,EAAAA,EAAAA,EAAAA,CAAcA,CAAC,UAGvB0C,EAAU,CACd,CACE6C,KAAMxF,EAAE,YACRyF,MAAO,MACPF,SAAU,YACVG,KAAM,GAAYC,GAAKA,EAAEC,SAAS,EAAID,EAAEC,SAAS,EAEnD,CACEJ,KAAMxF,EAAE,YACRyF,MAAO,MACPF,SAAU,iBACVG,KAAM,GAAYC,GAAKA,EAAEE,aAAa,EAAI,UAACC,IAAAA,CAAEC,KAAM,GAA4CJ,MAAAA,CAAzCK,8BAAsB,CAAC,oBAAwB,OAANL,EAAEM,GAAG,EAAIC,OAAO,kBAAUP,EAAEE,aAAa,CAACM,KAAK,CAAC,KAAKC,KAAK,CAAC,EAAG,CAAC,GAAGC,IAAI,CAAC,OACtKC,UAAU,CACZ,EACA,CACEd,KAAMxF,EAAE,eACRuF,SAAU,cACVG,KAAM,GAAYC,GAAKA,EAAEY,WAAW,EAAIZ,EAAEY,WAAW,EAEvD,CACEf,KAAMxF,EAAE,gBACRyF,MAAO,MACPF,SAAU,iBACVG,KAAM,GAAYC,GAAKA,EAAEa,UAAU,EAAIC,IAAOd,EAAEa,UAAU,EAAEE,MAAM,CAAC,cACnEJ,MAD6CG,IACnC,CACZ,EACD,CAED,MACE,UAACjE,EAAAA,CAAQA,CAAAA,CACPG,QAASA,EACTC,KAAMnB,EACN6B,WAAW,EACXI,OAAQwB,EACRvB,gBAAgB,IAChBvC,QAASA,GAIf", "sources": ["webpack://_N_E/?1f1f", "webpack://_N_E/./pages/vspace/DocumentAccordian.tsx", "webpack://_N_E/./components/common/RKITable.tsx", "webpack://_N_E/./components/common/DocumentTable.tsx"], "sourcesContent": ["\n    (window.__NEXT_P = window.__NEXT_P || []).push([\n      \"/vspace/DocumentAccordian\",\n      function () {\n        return require(\"private-next-pages/vspace/DocumentAccordian.tsx\");\n      }\n    ]);\n    if(module.hot) {\n      module.hot.dispose(function () {\n        window.__NEXT_P.push([\"/vspace/DocumentAccordian\"])\n      });\n    }\n  ", "//Import Library\r\nimport { faPlus, faMinus } from \"@fortawesome/free-solid-svg-icons\";\r\nimport { FontAwesomeIcon } from \"@fortawesome/react-fontawesome\";\r\nimport { Card, Accordion } from \"react-bootstrap\";\r\nimport { useState } from \"react\";\r\n\r\n//Import services/components\r\nimport DocumentTable from \"../../components/common/DocumentTable\";\r\nimport { useTranslation } from 'next-i18next';\r\n\r\nconst DocumentAccordian = (props: any) => {\r\n    const { t } = useTranslation('common');\r\n    const [section, setSection] = useState(false);\r\n    return (\r\n        <>\r\n            <Accordion.Item eventKey=\"0\">\r\n                <Accordion.Header onClick={() => setSection(!section)}>\r\n                    <div className=\"cardTitle\">{t(\"vspace.Documents\")}</div>\r\n                    <div className=\"cardArrow\">\r\n                    {section ? <FontAwesomeIcon icon={faMinus} color=\"#fff\" /> :\r\n                        <FontAwesomeIcon icon={faPlus} color=\"#fff\" />}\r\n                    </div>\r\n                </Accordion.Header>\r\n                <Accordion.Body>\r\n                    <DocumentTable loading={props.vSpaceLoading} sortProps={props.vSpaceSort} docs={props.documentAccoirdianProps.vSpaceDocs || []} docsDescription={props.calenderEvents.length > 0 && props.calenderEvents.map((item: any) => item.doc_src).flat(1)} />\r\n                    <h6 className=\"mt-3\">{t(\"vspace.DocumentsfromUpdates\")}</h6>\r\n                    <DocumentTable loading={props.documentAccoirdianProps.updateLoading} sortProps={props.documentAccoirdianProps.updateSort} docs={props.document || []} docsDescription={props.documentAccoirdianProps.docSrc} />\r\n                </Accordion.Body>\r\n            </Accordion.Item>\r\n        </>\r\n    )\r\n};\r\n\r\nexport default DocumentAccordian;", "//Import Library\r\nimport React from 'react'\r\nimport DataTable  from 'react-data-table-component';\r\n\r\n//Import services/components\r\nimport { useTranslation } from 'next-i18next';\r\nimport CustomLoader from './CustomLoader';\r\n\r\ninterface RKITableProps {\r\n  columns: any[];\r\n  data: any[];\r\n  totalRows?: number;\r\n  resetPaginationToggle?: boolean;\r\n  subheader?: boolean;\r\n  subHeaderComponent?: React.ReactNode;\r\n  handlePerRowsChange?: (newPerPage: number, page: number) => void;\r\n  handlePageChange?: (page: number) => void;\r\n  rowsPerPage?: number[];\r\n  defaultRowsPerPage?: number;\r\n  selectableRows?: boolean;\r\n  loading?: boolean;\r\n  pagServer?: boolean;\r\n  onSelectedRowsChange?: (selectedRows: any) => void;\r\n  clearSelectedRows?: boolean;\r\n  sortServer?: boolean;\r\n  onSort?: (column: any, sortDirection: string) => void;\r\n  persistTableHead?: boolean;\r\n  sortFunction?: (rows: any[], field: any, direction: 'asc' | 'desc') => any[];\r\n  noHeader?: boolean;\r\n  dense?: boolean;\r\n  pagination?: boolean;\r\n  paginationServer?: boolean;\r\n  paginationTotalRows?: number;\r\n  subHeaderAlign?: string;\r\n  [key: string]: any;\r\n}\r\n\r\nfunction RKITable(props: RKITableProps) {\r\n  const { t } = useTranslation('common');\r\n  const paginationComponentOptions = {\r\n    rowsPerPageText: t('Rowsperpage'),\r\n    };\r\n  const {\r\n    columns,\r\n    data,\r\n    totalRows,\r\n    resetPaginationToggle,\r\n    subheader,\r\n    subHeaderComponent,\r\n    handlePerRowsChange,\r\n    handlePageChange,\r\n    rowsPerPage,\r\n    defaultRowsPerPage,\r\n    selectableRows,\r\n    loading,\r\n    pagServer,\r\n    onSelectedRowsChange,\r\n    clearSelectedRows,\r\n    sortServer,\r\n    onSort,\r\n    persistTableHead,\r\n    sortFunction,\r\n    // Filter out any unknown props that might cause DOM warnings\r\n    ...otherProps\r\n  } = props;\r\n\r\n  // Only pass known DataTable props to avoid DOM warnings\r\n  const dataTableProps = {\r\n    paginationComponentOptions,\r\n    noDataComponent: t(\"NoData\"),\r\n    noHeader: true,\r\n    columns,\r\n    data: data || [],\r\n    dense: true,\r\n    paginationResetDefaultPage: resetPaginationToggle,\r\n    subHeader: subheader,\r\n    progressPending: loading,\r\n    subHeaderComponent,\r\n    pagination: true,\r\n    paginationServer: pagServer,\r\n    paginationPerPage: defaultRowsPerPage ? defaultRowsPerPage : 10,\r\n    paginationRowsPerPageOptions: rowsPerPage ? rowsPerPage : [10, 15, 20, 25, 30],\r\n    paginationTotalRows: totalRows,\r\n    onChangeRowsPerPage: handlePerRowsChange,\r\n    onChangePage: handlePageChange,\r\n    selectableRows,\r\n    onSelectedRowsChange,\r\n    clearSelectedRows,\r\n    progressComponent: <CustomLoader/>,\r\n    sortIcon: <i className=\"sort-icon fas fa-exchange-alt\" />,\r\n    sortServer,\r\n    onSort,\r\n    sortFunction,\r\n    persistTableHead,\r\n    className: \"rki-table\"\r\n  };\r\n  return (\r\n    <DataTable {...dataTableProps} />\r\n  )\r\n};\r\n\r\nRKITable.defaultProps = {\r\n  subHeader: false,\r\n  pagination: true,\r\n  totalRows: null,\r\n  pagServer: true,\r\n  onSelectedRowsChange: null,\r\n  clearSelectedRows: false,\r\n  sortServer: false,\r\n  persistTableHead: false\r\n}\r\n\r\nexport default RKITable;\r\n\r\n", "//Import Library\r\nimport React from \"react\";\r\nimport moment from \"moment\";\r\n\r\n//Import services/components\r\nimport RKITable from '../../components/common/RKITable';\r\nimport { useTranslation } from 'next-i18next';\r\n\r\ninterface DocumentTableProps {\r\n  docs: any[];\r\n  docsDescription: string;\r\n  sortProps: (sortObj: { columnSelector: string; sortDirection: string }) => void;\r\n  loading: boolean;\r\n}\r\n\r\nconst DocumentTable: React.FC<DocumentTableProps> = ({ docs, docsDescription, sortProps, loading }) => {\r\n\r\n  const handleSort = async (column: any, sortDirection: string) => {\r\n    const objSlect = {\r\n      columnSelector: column.selector,\r\n      sortDirection: sortDirection\r\n    }\r\n    sortProps(objSlect);\r\n  };\r\n\r\n  const { t } = useTranslation('common');\r\n\r\n\r\n  const columns = [\r\n    {\r\n      name: t(\"FileType\"),\r\n      width: \"15%\",\r\n      selector: 'extension',\r\n      cell: (d: any) => d && d.extension && d.extension,\r\n    },\r\n    {\r\n      name: t(\"FileName\"),\r\n      width: \"25%\",\r\n      selector: \"document_title\",\r\n      cell: (d: any) => d && d.original_name && <a href={`${process.env.API_SERVER}/files/download/${d._id}`} target=\"_blank\">{d.original_name.split('.').slice(0, -1).join('.')}</a>,\r\n      sortable: true\r\n    },\r\n    {\r\n      name: t(\"Description\"),\r\n      selector: 'description',\r\n      cell: (d: any) => d && d.description && d.description,\r\n    },\r\n    {\r\n      name: t(\"UploadedDate\"),\r\n      width: \"25%\",\r\n      selector: 'doc_created_at',\r\n      cell: (d: any) => d && d.updated_at && moment(d.updated_at).format('MM/DD/YYYY'),\r\n      sortable: true\r\n    }\r\n  ];\r\n\r\n  return (\r\n    <RKITable\r\n      columns={columns}\r\n      data={docs}\r\n      pagServer={true}\r\n      onSort={handleSort}\r\n      persistTableHead\r\n      loading={loading}\r\n    />\r\n\r\n  )\r\n}\r\n\r\nexport default DocumentTable;\r\n"], "names": ["t", "useTranslation", "DocumentAccordian", "section", "setSection", "useState", "Accordion", "<PERSON><PERSON>", "eventKey", "Header", "onClick", "div", "className", "FontAwesomeIcon", "icon", "faMinus", "color", "faPlus", "Body", "DocumentTable", "loading", "props", "vSpaceLoading", "sortProps", "vSpaceSort", "docs", "documentAccoirdianProps", "vSpaceDocs", "docsDescription", "calenderEvents", "length", "map", "item", "doc_src", "flat", "h6", "updateLoading", "updateSort", "document", "docSrc", "RKITable", "paginationComponentOptions", "rowsPerPageText", "columns", "data", "totalRows", "resetPaginationToggle", "subheader", "subHeaderComponent", "handlePerRowsChange", "handlePageChange", "rowsPerPage", "defaultRowsPerPage", "selectableRows", "pagServer", "onSelectedRowsChange", "clearSelectedRows", "sortServer", "onSort", "persistTableHead", "sortFunction", "otherProps", "dataTableProps", "noDataComponent", "<PERSON><PERSON><PERSON><PERSON>", "dense", "paginationResetDefaultPage", "subHeader", "progressPending", "pagination", "paginationServer", "paginationPerPage", "paginationRowsPerPageOptions", "paginationTotalRows", "onChangeRowsPerPage", "onChangePage", "progressComponent", "CustomLoader", "sortIcon", "i", "DataTable", "defaultProps", "handleSort", "column", "sortDirection", "columnSelector", "objSlect", "selector", "name", "width", "cell", "d", "extension", "original_name", "a", "href", "process", "_id", "target", "split", "slice", "join", "sortable", "description", "updated_at", "moment", "format"], "sourceRoot": "", "ignoreList": []}