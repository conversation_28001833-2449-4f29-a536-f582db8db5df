{"version": 3, "file": "static/chunks/pages/adminsettings/syndrome-d083a9cb64cd8fca.js", "mappings": "+RA8DA,MAjDuBA,QAiCfC,EAAAA,EAhCN,GAAM,GAAEC,CAAC,CAAE,CAAGC,CAAAA,EAAAA,EAAAA,EAAAA,CAAcA,CAAC,UACvBC,CA+CoBC,CA/CA,CA+CC,GA7CvB,WAACC,EAAAA,CAASA,CAAAA,CAACC,MAAO,CAAEC,UAAW,QAAS,EAAGC,KAAK,IAACC,UAAU,gBACzD,UAACC,EAAAA,CAAGA,CAAAA,UACF,UAACC,EAAAA,CAAGA,CAAAA,CAACC,GAAI,YACP,UAACC,EAAAA,CAAWA,CAAAA,CAACC,MAAOb,EAAE,yCAG1B,UAACS,EAAAA,CAAGA,CAAAA,UACF,UAACC,EAAAA,CAAGA,CAAAA,CAACC,GAAI,YACP,UAACG,IAAIA,CACHC,KAAK,6BACLC,GAAG,OAFAF,mCAIH,UAACG,EAAAA,CAAMA,CAAAA,CAACC,QAAQ,YAAYC,KAAK,cAChCnB,EAAE,6CAKT,UAACS,EAAAA,CAAGA,CAAAA,CAACD,UAAU,gBACb,UAACE,EAAAA,CAAGA,CAAAA,CAACC,GAAI,YACP,UAACS,EAAAA,OAAaA,CAAAA,CAAAA,UAOlBC,EAAmBC,CAAAA,EAAAA,EAAAA,eAAAA,CAAeA,CAAC,IAAM,UAACpB,EAAAA,CAAAA,IAC1CH,EAAYwB,CAAAA,EAAAA,EAAAA,EAAAA,CAAWA,CAAC,GAAWxB,SACzC,IAAI,GAAEA,GAAAA,OAAAA,EAAAA,EAAOyB,IAAPzB,OAAOyB,GAAPzB,OAAAA,EAAAA,EAAAA,QAAoB0B,EAApB1B,KAAAA,EAAAA,CAA8B,CAAC,GAA/BA,UAA4C,EAIhD,UAACsB,EAAAA,CAAAA,GAHM,UAACK,EAAAA,OAAeA,CAAAA,CAAAA,EAK3B,6mBCjDA,IAAMC,EAAS,aACFC,EAAmBC,CAAAA,EAAAA,EAAAA,CAAAA,CAAoBA,CAAC,CACnDC,sBAAuB,KACjB/B,EAAMyB,WAAW,IAAIzB,EAAMyB,WAAW,CAACO,YAAY,IAAIhC,EAAMyB,WAAW,CAACO,YAAY,CAACJ,EAAO,CAKnGK,CALqG,kBAKjF,kBACtB,GAAG,EAE0BH,CAAAA,EAAAA,EAAAA,CAAAA,CAAoBA,CAAC,CAChDC,sBAAuB,KACjB/B,EAAMyB,WAAW,IAAIzB,EAAMyB,WAAW,CAACS,OAAO,IAAIlC,EAAMyB,WAAW,CAACS,OAAO,CAACN,EAAO,CAKzFK,CAL2F,kBAKvE,eACtB,GAAG,EAEmCH,CAAAA,EAAAA,EAAAA,CAAAA,CAAoBA,CAAC,CACzDC,sBAAwB/B,KAClBA,EAAMyB,WAAW,IAAIzB,EAAMyB,WAAW,CAACU,iBAAiB,IAAInC,EAAMyB,WAAW,CAACU,iBAAiB,CAACP,EAAO,CAK7GK,CAL+G,kBAK3F,wBACtB,GAAG,EAE8BH,CAAAA,EAAAA,EAAAA,CAAAA,CAAoBA,CAAC,CACpDC,sBAAuB,KACjB/B,EAAMyB,WAAW,IAAIzB,EAAMyB,WAAW,CAACW,YAAY,IAAIpC,EAAMyB,WAAW,CAACW,YAAY,CAACR,EAAO,CAKnGK,CALqG,kBAKjF,mBACtB,GAAG,EAE4BH,CAAAA,EAAAA,EAAAA,CAAAA,CAAoBA,CAAC,CAClDC,sBAAuB,GACjB/B,IAAMyB,WAAW,IAAIzB,EAAMyB,WAAW,CAACY,SAAS,IAAIrC,EAAMyB,WAAW,CAACY,SAAS,CAACT,EAAO,CAK7FK,CAL+F,kBAK3E,iBACtB,GAAG,EAEqCH,CAAAA,EAAAA,EAAAA,CAAAA,CAAoBA,CAAC,CAC3DC,sBAAuB,KACjB/B,EAAMyB,WAAW,IAAIzB,EAAMyB,WAAW,CAACa,uBAAuB,IAAItC,EAAMyB,WAAW,CAACa,uBAAuB,CAACV,EAAO,CAKzHK,CAL2H,kBAKvG,0BACtB,GAAG,EAEiCH,CAAAA,EAAAA,EAAAA,CAAAA,CAAoBA,CAAC,CACvDC,sBAAuB,KACjB/B,EAAMyB,WAAW,IAAIzB,EAAMyB,WAAW,CAACa,uBAAuB,IAAItC,EAAMyB,WAAW,CAACa,uBAAuB,CAACV,EAAO,CAKzHK,CAL2H,kBAKvG,sBACtB,GAAG,EAC0BH,CAAAA,EAAAA,EAAAA,CAAAA,CAAoBA,CAAC,CAChDC,sBAAuB,KACjB/B,EAAMyB,WAAW,IAAIzB,EAAMyB,WAAW,CAACc,MAAM,IAAIvC,EAAMyB,WAAW,CAACc,MAAM,CAACX,EAAO,CAKvFK,CALyF,kBAKrE,eACtB,GAAG,EAE8BH,CAAAA,EAAAA,EAAAA,CAAAA,CAAoBA,CAAC,CACpDC,sBAAuB,KACjB/B,EAAMyB,WAAW,IAAIzB,EAAMyB,WAAW,CAACe,WAAW,IAAIxC,EAAMyB,WAAW,CAACe,WAAW,CAACZ,EAAO,CAKjGK,CALmG,kBAK/E,mBACtB,GAAG,EAEuCH,CAAAA,EAAAA,EAAAA,CAAAA,CAAoBA,CAAC,CAC7DC,sBAAuB,KACjB/B,EAAMyB,WAAW,IAAIzB,EAAMyB,WAAW,CAACgB,WAAW,IAAIzC,EAAMyB,WAAW,CAACgB,WAAW,CAACb,EAAO,CAKjGK,CALmG,kBAK/E,4BACtB,GAAG,EAEuCH,CAAAA,EAAAA,EAAAA,CAAAA,CAAoBA,CAAC,CAC7DC,sBAAuB,KACjB/B,EAAMyB,WAAW,IAAIzB,EAAMyB,WAAW,CAACiB,mBAAmB,IAAI1C,EAAMyB,WAAW,CAACiB,mBAAmB,CAACd,EAAO,CAKjHK,CALmH,kBAK/F,4BACtB,GAEaU,EAA0Bb,CAAAA,EAAAA,EAAAA,CAAAA,CAAoBA,CAAC,CAC1DC,sBAAuB,KACjB/B,EAAMyB,WAAW,IAAIzB,EAAMyB,WAAW,CAACmB,gBAAgB,IAAI5C,EAAMyB,WAAW,CAACmB,gBAAgB,CAAChB,EAAO,CAK3GK,CAL6G,kBAKzF,yBACtB,GAAG,EAEkCH,CAAAA,EAAAA,EAAAA,CAAAA,CAAoBA,CAAC,CACxDC,sBAAuB,KACjB/B,EAAMyB,WAAW,IAAIzB,EAAMyB,WAAW,CAACoB,gBAAgB,IAAI7C,EAAMyB,WAAW,CAACoB,gBAAgB,CAACjB,EAAO,CAK3GK,CAL6G,kBAKzF,uBACtB,GAAG,EAEgCH,CAAAA,EAAAA,EAAAA,CAAAA,CAAoBA,CAAC,CACtDC,sBAAuB,KACjB/B,EAAMyB,WAAW,IAAIzB,EAAMyB,WAAW,CAACqB,cAAc,IAAI9C,EAAMyB,WAAW,CAACqB,cAAc,CAAClB,EAAO,CAKvGK,CALyG,kBAKrF,qBACtB,GAAG,EAE0BH,CAAAA,EAAAA,EAAAA,CAAAA,CAAoBA,CAAC,CAChDC,sBAAwB/B,KAClBA,EAAMyB,WAAW,IAAIzB,EAAMyB,WAAW,CAACsB,MAAM,IAAI/C,EAAMyB,WAAW,CAACsB,MAAM,CAACnB,EAAO,CAKvFK,CALyF,kBAKrE,eACtB,GAAG,EAE6BH,CAAAA,EAAAA,EAAAA,CAAAA,CAAoBA,CAAC,CACnDC,sBAAuB,KACjB/B,EAAMyB,WAAW,IAAIzB,EAAMyB,WAAW,CAACuB,UAAU,IAAIhD,EAAMyB,WAAW,CAACuB,UAAU,CAACpB,EAAO,CAK/FK,CALiG,kBAK7E,kBACtB,GAAG,EAE4BH,CAAAA,EAAAA,EAAAA,CAAAA,CAAoBA,CAAC,CAClDC,sBAAuB,KACjB/B,EAAMyB,WAAW,IAAIzB,EAAMyB,WAAW,CAACC,QAAQ,IAAI1B,EAAMyB,WAAW,CAACC,QAAQ,CAACE,EAAO,CAK3FK,CAL6F,kBAKzE,iBACtB,GAAG,EAE8BH,CAAAA,EAAAA,EAAAA,CAAAA,CAAoBA,CAAC,CACpDC,sBAAuB,KACjB/B,EAAMyB,WAAW,IAAIzB,EAAMyB,WAAW,CAACwB,WAAW,IAAIjD,EAAMyB,WAAW,CAACwB,WAAW,CAACrB,EAAO,CAKjGK,CALmG,kBAK/E,mBACtB,GAAG,EAEwBH,CAAAA,EAAAA,EAAAA,CAAAA,CAAoBA,CAAC,CAC9CC,sBAAuB,KACjB/B,EAAMyB,WAAW,IAAIzB,EAAMyB,WAAW,CAACyB,KAAK,IAAIlD,EAAMyB,WAAW,CAACyB,KAAK,CAACtB,EAAO,CAKrFK,CALuF,kBAKnE,aACtB,GAAG,EAE8BH,CAAAA,EAAAA,EAAAA,CAAAA,CAAoBA,CAAC,CACpDC,sBAAuB,KACjB/B,EAAMyB,WAAW,IAAIzB,EAAMyB,WAAW,CAAC0B,WAAW,IAAInD,EAAMyB,WAAW,CAAC0B,WAAW,CAACvB,EAAO,CAKjGK,CALmG,kBAK/E,mBACtB,GAAG,EAE8BH,CAAAA,EAAAA,EAAAA,CAAAA,CAAoBA,CAAC,CACpDC,sBAAuB,KACjB/B,EAAMyB,WAAW,IAAIzB,EAAMyB,WAAW,CAAC2B,YAAY,IAAIpD,EAAMyB,WAAW,CAAC2B,YAAY,CAACxB,EAAO,CAKnGK,CALqG,kBAKjF,mBACtB,GAAG,EAE0BH,CAAAA,EAAAA,EAAAA,CAAAA,CAAoBA,CAAC,CAChDC,sBAAuB,GACrB,EAAI/B,EAAMyB,WAAW,IAAIzB,EAAMyB,WAAW,CAAC4B,SAAS,IAAIrD,EAAMyB,WAAW,CAAC4B,SAAS,CAACzB,EAAO,IAAI5B,EAAMyB,WAAW,CAAC6B,OAAO,IAAItD,EAAMyB,WAAW,CAAC6B,OAAO,CAAC1B,EAAO,IAAG5B,EAAMyB,WAAW,CAAC8B,KAAK,IAAIvD,EAAMyB,WAAW,CAAC8B,KAAK,CAAC3B,EAAO,IAAG5B,EAAMyB,WAAW,CAAC+B,MAAM,IAAIxD,EAAMyB,WAAW,CAAC+B,MAAM,CAAC5B,EAAO,IAAG5B,EAAMyB,WAAW,CAACgB,WAAW,IAAIzC,EAAMyB,WAAW,CAACgB,WAAW,CAACb,EAAO,IAAG5B,EAAMyB,WAAW,CAACgC,MAAM,IAAIzD,EAAMyB,WAAW,CAACgC,MAAM,CAAC7B,EAAO,EAAE,GAKraK,mBAAoB,eACtB,GAAG,EAEYJ,gBAAgBA,EAAC,2FC1LhC,SAAS6B,EAASC,CAAoB,EACpC,GAAM,GAAE1D,CAAC,CAAE,CAAGC,CAAAA,EAAAA,EAAAA,EAAAA,CAAcA,CAAC,UACvB0D,EAA6B,CACjCC,gBAAiB5D,EAAE,cACnB,EACI,SACJ6D,CAAO,MACPC,CAAI,WACJC,CAAS,CACTC,uBAAqB,CACrBC,WAAS,CACTC,oBAAkB,qBAClBC,CAAmB,kBACnBC,CAAgB,aAChBC,CAAW,oBACXC,CAAkB,gBAClBC,CAAc,SACdC,CAAO,WACPC,CAAS,sBACTC,CAAoB,mBACpBC,CAAiB,YACjBC,CAAU,QACVC,CAAM,kBACNC,CAAgB,cAChBC,CAAY,CAEZ,CADA,EACGC,EACJ,CAAGtB,EAGEuB,EAAiB,4BACrBtB,EACAuB,gBAAiBlF,EAAE,IAP0C,MAQ7DmF,UAAU,UACVtB,EACAC,KAAMA,GAAQ,EAAE,CAChBsB,OAAO,EACPC,2BAA4BrB,EAC5BsB,UAAWrB,EACXsB,gBAAiBf,qBACjBN,EACAsB,YAAY,EACZC,iBAAkBhB,EAClBiB,kBAAmBpB,GAA0C,GAC7DqB,eADwCrB,cACVD,GAA4B,CAAC,GAAI,GAAI,GAAI,CAA3BA,EAA+B,GAAG,CAC9EuB,oBAAqB7B,EACrB8B,oBAAqB1B,EACrB2B,aAAc1B,iBACdG,uBACAG,oBACAC,EACAoB,kBAAmB,UAACC,EAAAA,CAAYA,CAAAA,CAAAA,GAChCC,SAAU,UAACC,IAAAA,CAAE1F,UAAU,6CACvBoE,SACAC,eACAE,mBACAD,EACAtE,UAAW,WACb,EACA,MACE,UAAC2F,EAAAA,EAASA,CAAAA,CAAE,GAAGlB,CAAc,EAEjC,CAEAxB,EAAS2C,YAAY,CAAG,CACtBd,WAAW,EACXE,WAAY,GACZzB,UAAW,KACXU,WAAW,EACXC,qBAAsB,KACtBC,mBAAmB,EACnBC,YAAY,EACZE,kBAAkB,CACpB,EAEA,MAAerB,QAAQA,EAAC,mEChHT,SAAS/B,EAAgB5B,CAAW,EAC/C,MACE,UAACuG,MAAAA,CAAI7F,UAAU,sDACb,UAAC6F,MAAAA,CAAI7F,UAAU,mBAAU,yCAG/B,gECFa,SAASI,EAAY8C,CAAuB,EACzD,MACE,UAAC4C,KAAAA,CAAG9F,UAAU,wBAAgBkD,EAAM7C,KAAK,EAE7C,8KCoIA,MAjIsB,IAClB,GAAM,GAAEb,CAAC,CAAE,CAAGC,CAAAA,EAAAA,EAAAA,EAAAA,CAAcA,CAAC,MAgIlBmB,IA/HL,CAACmF,EAAWC,EAAe,CAAGC,CAAAA,EAAAA,EA+HX,QA/HWA,CAAQA,CAAC,EAAE,EACzC,EAAGC,EAAW,CAAGD,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,EAAC,GAC1B,CAAC1C,EAAW4C,EAAa,CAAGF,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAC,GACrC,CAACG,EAASC,EAAW,CAAGJ,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAC,IACjC,CAACK,EAAaC,EAAS,CAAGN,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,EAAC,GACnC,CAACO,EAAgBC,EAAkB,CAAGR,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAC,CAAC,GAEhD5C,EAAU,CACZ,CACIqD,KAAMlH,EAAE,mCACRmH,SAAU,OACd,EACA,CACID,KAAMlH,EAAE,8BACRmH,SAAU,OACVC,KAAOC,GAAWA,EAAEC,IAAI,EAE5B,CACIJ,KAAMlH,EAAE,qCACRmH,SAAU,cACVC,KAAM,GAAYC,EAAEE,WAAW,CAACC,OAAO,CAAC,WAAY,GACxD,EACA,CACIN,KAAMlH,EAAE,gCACRmH,SAAU,GACVC,KAAM,GACF,WAACf,MAAAA,WACG,UAACvF,IAAIA,CAACC,KAAK,6BAA6BC,GAAI,OAAvCF,yBAA6E,OAANuG,EAAEI,GAAG,WAE7E,UAACvB,IAAAA,CAAE1F,UAAU,uBAEV,OAEP,UAACkH,IAAAA,CAAEC,QAAS,IAAMC,EAAWP,YACzB,UAACnB,IAAAA,CAAE1F,UAAU,4BACZ,MAGjB,EACH,CAEDqH,CAAAA,EAAAA,EAAAA,SAAAA,CAASA,CAAC,KACNC,GACJ,EAAG,EAAE,EAEL,IAAMC,EAAiB,CACnBC,KAAM,CAAEnH,MAAO,KAAM,EACrBoH,MAAOrB,EACPsB,KAAM,EACNC,MAAO,CAAC,CACZ,EAEML,EAAkB,UACpBpB,GAAW,GACX,IAAM0B,EAAW,MAAMC,EAAAA,CAAUA,CAACC,GAAG,CAAC,YAAaP,GAC/CK,GAAYA,EAAStE,IAAI,EAAIsE,EAAStE,IAAI,CAACyE,MAAM,CAAG,GAAG,CACvD/B,EAAe4B,EAAStE,IAAI,EAC5B6C,EAAayB,EAASI,UAAU,EAChC9B,GAAW,GAEnB,EAQMvC,EAAsB,MAAOsE,EAAiBP,KAChDH,EAAeE,KAAK,CAAGQ,EACvBV,EAAeG,IAAI,CAAGA,EACtBxB,GAAW,GACX,IAAM0B,EAAW,MAAMC,EAAAA,CAAUA,CAACC,GAAG,CAAC,YAAaP,GAC/CK,GAAYA,EAAStE,IAAI,EAAIsE,EAAStE,IAAI,CAACyE,MAAM,CAAG,GAAG,CACvD/B,EAAe4B,EAAStE,IAAI,EAC5B+C,EAAW4B,GACX/B,EAAW,IAEnB,EAEMkB,EAAa,MAAOc,IACtBzB,EAAkByB,EAAIjB,GAAG,EACzBV,GAAS,EACb,EAEM4B,EAAe,UACjB,GAAI,CACA,MAAMN,EAAAA,CAAUA,CAACO,MAAM,CAAC,aAA4B,OAAf5B,IACrCc,IACAf,GAAS,GACT8B,EAAAA,EAAKA,CAACC,OAAO,CAAC9I,EAAE,2DACpB,CAAE,MAAO+I,EAAO,CACZF,EAAAA,EAAKA,CAACE,KAAK,CAAC/I,EAAE,qDAClB,CACJ,EAEMgJ,EAAY,IAAMjC,GAAS,GAEjC,MACI,WAACV,MAAAA,WACG,WAAC4C,EAAAA,CAAKA,CAAAA,CAACC,KAAMpC,EAAaqC,OAAQH,YAC9B,UAACC,EAAAA,CAAKA,CAACG,MAAM,EAACC,WAAW,aACrB,UAACJ,EAAAA,CAAKA,CAACK,KAAK,WAAEtJ,EAAE,4CAEpB,UAACiJ,EAAAA,CAAKA,CAACM,IAAI,WAAEvJ,EAAE,+DACf,WAACiJ,EAAAA,CAAKA,CAACO,MAAM,YACT,UAACvI,EAAAA,CAAMA,CAAAA,CAACC,QAAQ,YAAYyG,QAASqB,WAChChJ,EAAE,kCAEP,UAACiB,EAAAA,CAAMA,CAAAA,CAACC,QAAQ,UAAUyG,QAASgB,WAC9B3I,EAAE,qCAKf,UAACyD,EAAAA,CAAQA,CAAAA,CACLI,QAASA,EACTC,KAAMyC,EACNxC,UAAWA,EACXU,WAAW,EACXN,oBAAqBA,EACrBC,iBA3Dc8D,CA2DI9D,GA1D1B2D,EAAeE,KAAK,CAAGrB,EACvBmB,EAAeG,IAAI,CAAGA,EACtBJ,GACJ,MA2DJ,mBCzIA,4CACA,0BACA,WACA,OAAe,EAAQ,KAAqD,CAC5E,EACA,SAFsB", "sources": ["webpack://_N_E/./pages/adminsettings/syndrome/index.tsx", "webpack://_N_E/./pages/adminsettings/permissions.tsx", "webpack://_N_E/./components/common/RKITable.tsx", "webpack://_N_E/./pages/rNoAccess.tsx", "webpack://_N_E/./components/common/PageHeading.tsx", "webpack://_N_E/./pages/adminsettings/syndrome/syndromeTable.tsx", "webpack://_N_E/?3399"], "sourcesContent": ["//Import Library\r\nimport { Contain<PERSON>, <PERSON>, <PERSON>, Button } from \"react-bootstrap\";\r\nimport Link from \"next/link\";\r\n\r\n//Import services/components\r\nimport PageHeading from \"../../../components/common/PageHeading\";\r\nimport SyndromeTable from \"./syndromeTable\";\r\nimport { useTranslation } from 'next-i18next';\r\nimport { serverSideTranslations } from 'next-i18next/serverSideTranslations';\r\nimport { canAddSyndromes } from \"../permissions\";\r\nimport { useSelector } from \"react-redux\";\r\nimport NoAccessMessage from \"../../rNoAccess\";\r\n\r\nconst SyndromeIndex = (_props: any) => {\r\n  const { t } = useTranslation('common');\r\n  const ShowSyndromeIndex = () => {\r\n    return (\r\n      <Container style={{ overflowX: \"hidden\" }} fluid className=\"p-0\">\r\n        <Row>\r\n          <Col xs={12}>\r\n            <PageHeading title={t(\"adminsetting.syndrome.Syndromes\")} />\r\n          </Col>\r\n        </Row>\r\n        <Row>\r\n          <Col xs={12}>\r\n            <Link\r\n              href=\"/adminsettings/[...routes]\"\r\n              as=\"/adminsettings/create_syndrome\"\r\n              >\r\n              <Button variant=\"secondary\" size=\"sm\">\r\n              {t(\"adminsetting.syndrome.AddSyndrome\")}\r\n              </Button>\r\n            </Link>\r\n          </Col>\r\n        </Row>\r\n        <Row className=\"mt-3\">\r\n          <Col xs={12}>\r\n            <SyndromeTable />\r\n          </Col>\r\n        </Row>\r\n      </Container>\r\n    );\r\n  }\r\n\r\n  const ShowAddSyndromes = canAddSyndromes(() => <ShowSyndromeIndex />);\r\n  const state:any = useSelector((state) => state);\r\n  if (!(state?.permissions?.syndrome?.['create:any'])) {\r\n    return <NoAccessMessage />\r\n  }\r\n  return(\r\n    <ShowAddSyndromes />\r\n  )\r\n};\r\n\r\nexport async function getServerSideProps({ locale } : { locale: string }) {\r\n  return {\r\n    props: {\r\n      ...(await serverSideTranslations(locale, ['common'])),\r\n    },\r\n  }\r\n}\r\n\r\nexport default SyndromeIndex;\r\n", "//Import services/components\r\nimport connectedAuthWrapper from 'redux-auth-wrapper/connectedAuthWrapper';\r\n\r\nconst create = \"create:any\";\r\nexport const canAddAreaOfWork = connectedAuthWrapper({\r\n  authenticatedSelector: (state) => {\r\n    if (state.permissions && state.permissions.area_of_work && state.permissions.area_of_work[create]) {\r\n      return true;\r\n    }\r\n    return false;\r\n  },\r\n  wrapperDisplayName: 'CanAddAreaOfWork',\r\n});\r\n\r\nexport const canAddCountry = connectedAuthWrapper({\r\n  authenticatedSelector: (state) => {\r\n    if (state.permissions && state.permissions.country && state.permissions.country[create]) {\r\n      return true;\r\n    }\r\n    return false;\r\n  },\r\n  wrapperDisplayName: 'CanAddCountry',\r\n});\r\n\r\nexport const canAddDeploymentStatus = connectedAuthWrapper({\r\n  authenticatedSelector: (state) => {\r\n    if (state.permissions && state.permissions.deployment_status && state.permissions.deployment_status[create]) {\r\n      return true;\r\n    }\r\n    return false;\r\n  },\r\n  wrapperDisplayName: 'CanAddDeploymentStatus',\r\n});\r\n\r\nexport const canAddEventStatus = connectedAuthWrapper({\r\n  authenticatedSelector: (state) => {\r\n    if (state.permissions && state.permissions.event_status && state.permissions.event_status[create]) {\r\n      return true;\r\n    }\r\n    return false;\r\n  },\r\n  wrapperDisplayName: 'CanAddEventStatus',\r\n});\r\n\r\nexport const canAddExpertise = connectedAuthWrapper({\r\n  authenticatedSelector: (state) => {\r\n    if (state.permissions && state.permissions.expertise && state.permissions.expertise[create]) {\r\n      return true;\r\n    }\r\n    return false;\r\n  },\r\n  wrapperDisplayName: 'CanAddExpertise',\r\n});\r\n\r\nexport const canAddFocalPointApproval = connectedAuthWrapper({\r\n  authenticatedSelector: (state) => {\r\n    if (state.permissions && state.permissions.institution_focal_point && state.permissions.institution_focal_point[create]) {\r\n      return true;\r\n    }\r\n    return false;\r\n  },\r\n  wrapperDisplayName: 'CanAddFocalPointApproval',\r\n});\r\n\r\nexport const canAddVspaceApproval = connectedAuthWrapper({\r\n  authenticatedSelector: (state) => {\r\n    if (state.permissions && state.permissions.institution_focal_point && state.permissions.institution_focal_point[create]) {\r\n      return true;\r\n    }\r\n    return false;\r\n  },\r\n  wrapperDisplayName: 'CanAddVspaceApproval',\r\n});\r\nexport const canAddHazards = connectedAuthWrapper({\r\n  authenticatedSelector: (state) => {\r\n    if (state.permissions && state.permissions.hazard && state.permissions.hazard[create]) {\r\n      return true;\r\n    }\r\n    return false;\r\n  },\r\n  wrapperDisplayName: 'CanAddHazards',\r\n});\r\n\r\nexport const canAddHazardTypes = connectedAuthWrapper({\r\n  authenticatedSelector: (state) => {\r\n    if (state.permissions && state.permissions.hazard_type && state.permissions.hazard_type[create]) {\r\n      return true;\r\n    }\r\n    return false;\r\n  },\r\n  wrapperDisplayName: 'CanAddHazardTypes',\r\n}); \r\n\r\nexport const canAddOrganisationApproval = connectedAuthWrapper({\r\n  authenticatedSelector: (state) => {\r\n    if (state.permissions && state.permissions.institution && state.permissions.institution[create]) {\r\n      return true;\r\n    }\r\n    return false;\r\n  },\r\n  wrapperDisplayName: 'CanAddOrganisationApproval',\r\n}); \r\n\r\nexport const canAddOrganisationNetworks = connectedAuthWrapper({\r\n  authenticatedSelector: (state) => {\r\n    if (state.permissions && state.permissions.institution_network && state.permissions.institution_network[create]) {\r\n      return true;\r\n    }\r\n    return false;\r\n  },\r\n  wrapperDisplayName: 'CanAddOrganisationNetworks',\r\n});\r\n\r\nexport const canAddOrganisationTypes = connectedAuthWrapper({\r\n  authenticatedSelector: (state) => {\r\n    if (state.permissions && state.permissions.institution_type && state.permissions.institution_type[create]) {\r\n      return true;\r\n    }\r\n    return false;\r\n  },\r\n  wrapperDisplayName: 'CanAddOrganisationTypes',\r\n});\r\n\r\nexport const canAddOperationStatus = connectedAuthWrapper({\r\n  authenticatedSelector: (state) => {\r\n    if (state.permissions && state.permissions.operation_status && state.permissions.operation_status[create]) {\r\n      return true;\r\n    }\r\n    return false;\r\n  },\r\n  wrapperDisplayName: 'CanAddOperationStatus',\r\n});\r\n\r\nexport const canAddProjectStatus = connectedAuthWrapper({\r\n  authenticatedSelector: (state) => {\r\n    if (state.permissions && state.permissions.project_status && state.permissions.project_status[create]) {\r\n      return true;\r\n    }\r\n    return false;\r\n  },\r\n  wrapperDisplayName: 'CanAddProjectStatus',\r\n});\r\n\r\nexport const canAddRegions = connectedAuthWrapper({\r\n  authenticatedSelector: (state) => {\r\n    if (state.permissions && state.permissions.region && state.permissions.region[create]) {\r\n      return true;\r\n    }\r\n    return false;\r\n  },\r\n  wrapperDisplayName: 'CanAddRegions',\r\n});\r\n\r\nexport const canAddRiskLevels = connectedAuthWrapper({\r\n  authenticatedSelector: (state) => {\r\n    if (state.permissions && state.permissions.risk_level && state.permissions.risk_level[create]) {\r\n      return true;\r\n    }\r\n    return false;\r\n  },\r\n  wrapperDisplayName: 'CanAddRiskLevels',\r\n});\r\n\r\nexport const canAddSyndromes = connectedAuthWrapper({\r\n  authenticatedSelector: (state) => {\r\n    if (state.permissions && state.permissions.syndrome && state.permissions.syndrome[create]) {\r\n      return true;\r\n    }\r\n    return false;\r\n  },\r\n  wrapperDisplayName: 'CanAddSyndromes',\r\n});\r\n\r\nexport const canAddUpdateTypes = connectedAuthWrapper({\r\n  authenticatedSelector: (state) => {\r\n    if (state.permissions && state.permissions.update_type && state.permissions.update_type[create]) {\r\n      return true;\r\n    }\r\n    return false;\r\n  },\r\n  wrapperDisplayName: 'CanAddUpdateTypes',\r\n});\r\n\r\nexport const canAddUsers = connectedAuthWrapper({\r\n  authenticatedSelector: (state) => {\r\n    if (state.permissions && state.permissions.users && state.permissions.users[create]) {\r\n      return true;\r\n    }\r\n    return false;\r\n  },\r\n  wrapperDisplayName: 'CanAddUsers',\r\n});\r\n\r\nexport const canAddWorldRegion = connectedAuthWrapper({\r\n  authenticatedSelector: (state) => {\r\n    if (state.permissions && state.permissions.worl_region && state.permissions.worl_region[create]) {\r\n      return true;\r\n    }\r\n    return false;\r\n  },\r\n  wrapperDisplayName: 'CanAddWorldRegion',\r\n});\r\n\r\nexport const canAddLandingPage = connectedAuthWrapper({\r\n  authenticatedSelector: (state) => {\r\n    if (state.permissions && state.permissions.landing_page && state.permissions.landing_page[create]) {\r\n      return true;\r\n    }\r\n    return false;\r\n  },\r\n  wrapperDisplayName: 'CanAddLandingPage',\r\n});\r\n\r\nexport const canAddContent = connectedAuthWrapper({\r\n  authenticatedSelector: (state) => {\r\n    if (state.permissions && state.permissions.operation && state.permissions.operation[create] && state.permissions.project && state.permissions.project[create]&& state.permissions.event && state.permissions.event[create]&& state.permissions.vspace && state.permissions.vspace[create]&& state.permissions.institution && state.permissions.institution[create]&& state.permissions.update && state.permissions.update[create]) {\r\n      return true;\r\n    }\r\n    return false;\r\n  },\r\n  wrapperDisplayName: 'CanAddContent',\r\n});\r\n\r\nexport default canAddAreaOfWork;", "//Import Library\r\nimport React from 'react'\r\nimport DataTable  from 'react-data-table-component';\r\n\r\n//Import services/components\r\nimport { useTranslation } from 'next-i18next';\r\nimport CustomLoader from './CustomLoader';\r\n\r\ninterface RKITableProps {\r\n  columns: any[];\r\n  data: any[];\r\n  totalRows?: number;\r\n  resetPaginationToggle?: boolean;\r\n  subheader?: boolean;\r\n  subHeaderComponent?: React.ReactNode;\r\n  handlePerRowsChange?: (newPerPage: number, page: number) => void;\r\n  handlePageChange?: (page: number) => void;\r\n  rowsPerPage?: number[];\r\n  defaultRowsPerPage?: number;\r\n  selectableRows?: boolean;\r\n  loading?: boolean;\r\n  pagServer?: boolean;\r\n  onSelectedRowsChange?: (selectedRows: any) => void;\r\n  clearSelectedRows?: boolean;\r\n  sortServer?: boolean;\r\n  onSort?: (column: any, sortDirection: string) => void;\r\n  persistTableHead?: boolean;\r\n  sortFunction?: (rows: any[], field: any, direction: 'asc' | 'desc') => any[];\r\n  noHeader?: boolean;\r\n  dense?: boolean;\r\n  pagination?: boolean;\r\n  paginationServer?: boolean;\r\n  paginationTotalRows?: number;\r\n  subHeaderAlign?: string;\r\n  [key: string]: any;\r\n}\r\n\r\nfunction RKITable(props: RKITableProps) {\r\n  const { t } = useTranslation('common');\r\n  const paginationComponentOptions = {\r\n    rowsPerPageText: t('Rowsperpage'),\r\n    };\r\n  const {\r\n    columns,\r\n    data,\r\n    totalRows,\r\n    resetPaginationToggle,\r\n    subheader,\r\n    subHeaderComponent,\r\n    handlePerRowsChange,\r\n    handlePageChange,\r\n    rowsPerPage,\r\n    defaultRowsPerPage,\r\n    selectableRows,\r\n    loading,\r\n    pagServer,\r\n    onSelectedRowsChange,\r\n    clearSelectedRows,\r\n    sortServer,\r\n    onSort,\r\n    persistTableHead,\r\n    sortFunction,\r\n    // Filter out any unknown props that might cause DOM warnings\r\n    ...otherProps\r\n  } = props;\r\n\r\n  // Only pass known DataTable props to avoid DOM warnings\r\n  const dataTableProps = {\r\n    paginationComponentOptions,\r\n    noDataComponent: t(\"NoData\"),\r\n    noHeader: true,\r\n    columns,\r\n    data: data || [],\r\n    dense: true,\r\n    paginationResetDefaultPage: resetPaginationToggle,\r\n    subHeader: subheader,\r\n    progressPending: loading,\r\n    subHeaderComponent,\r\n    pagination: true,\r\n    paginationServer: pagServer,\r\n    paginationPerPage: defaultRowsPerPage ? defaultRowsPerPage : 10,\r\n    paginationRowsPerPageOptions: rowsPerPage ? rowsPerPage : [10, 15, 20, 25, 30],\r\n    paginationTotalRows: totalRows,\r\n    onChangeRowsPerPage: handlePerRowsChange,\r\n    onChangePage: handlePageChange,\r\n    selectableRows,\r\n    onSelectedRowsChange,\r\n    clearSelectedRows,\r\n    progressComponent: <CustomLoader/>,\r\n    sortIcon: <i className=\"sort-icon fas fa-exchange-alt\" />,\r\n    sortServer,\r\n    onSort,\r\n    sortFunction,\r\n    persistTableHead,\r\n    className: \"rki-table\"\r\n  };\r\n  return (\r\n    <DataTable {...dataTableProps} />\r\n  )\r\n};\r\n\r\nRKITable.defaultProps = {\r\n  subHeader: false,\r\n  pagination: true,\r\n  totalRows: null,\r\n  pagServer: true,\r\n  onSelectedRowsChange: null,\r\n  clearSelectedRows: false,\r\n  sortServer: false,\r\n  persistTableHead: false\r\n}\r\n\r\nexport default RKITable;\r\n\r\n", "export default function NoAccessMessage(_props: any) {\r\n    return (\r\n      <div className=\"container-fluid p-0 response-message-block\">\r\n        <div className=\"message\">you don't have permission to access</div>\r\n      </div>\r\n    )\r\n  }", "interface PageHeadingProps {\r\n  title: string; // Required based on actual usage\r\n}\r\n\r\nexport default function PageHeading(props: PageHeadingProps) {\r\n  return (\r\n    <h2 className=\"page-heading\">{props.title}</h2>\r\n  )\r\n}\r\n", "//Import Library\r\nimport Link from \"next/link\";\r\nimport { useState, useEffect } from \"react\";\r\nimport { <PERSON><PERSON>, Button } from \"react-bootstrap\";\r\n\r\nimport toast from 'react-hot-toast';\r\n\r\n//Import services/components\r\nimport RKITable from \"../../../components/common/RKITable\";\r\nimport apiService from \"../../../services/apiService\";\r\nimport { useTranslation } from 'next-i18next';\r\nconst SyndromeTable = (_props: any) => {\r\n    const { t } = useTranslation('common');\r\n    const [tabledata, setDataToTable] = useState([]);\r\n    const [, setLoading] = useState(false);\r\n    const [totalRows, setTotalRows] = useState(0);\r\n    const [perPage, setPerPage] = useState(10);\r\n    const [isModalShow, setModal] = useState(false);\r\n    const [selectSyndrome, setSelectSyndrome] = useState({});\r\n    \r\n    const columns = [\r\n        {\r\n            name: t(\"adminsetting.syndrome.Syndromes\"),\r\n            selector: \"title\",\r\n        },\r\n        {\r\n            name: t(\"adminsetting.syndrome.Code\"),\r\n            selector: \"code\",\r\n            cell: (d: any) => d.code,\r\n        },\r\n        {\r\n            name: t(\"adminsetting.syndrome.Description\"),\r\n            selector: \"description\",\r\n            cell: (d: any) => d.description.replace(/<[^>]+>/g, \"\"),\r\n        },\r\n        {\r\n            name: t(\"adminsetting.syndrome.Action\"),\r\n            selector: \"\",\r\n            cell: (d: any) => (\r\n                <div>\r\n                    <Link href=\"/adminsettings/[...routes]\" as={`/adminsettings/edit_syndrome/${d._id}`}>\r\n\r\n                        <i className=\"icon fas fa-edit\" />\r\n\r\n                    </Link>\r\n                    &nbsp;\r\n                    <a onClick={() => userAction(d)}>\r\n                        <i className=\"icon fas fa-trash-alt\" />\r\n                    </a>{\" \"}\r\n                </div>\r\n            ),\r\n        },\r\n    ];\r\n\r\n    useEffect(() => {\r\n        getSyndromeData();\r\n    }, []);\r\n\r\n    const syndromeParams = {\r\n        sort: { title: \"asc\" },\r\n        limit: perPage,\r\n        page: 1,\r\n        query: {},\r\n    };\r\n\r\n    const getSyndromeData = async () => {\r\n        setLoading(true);\r\n        const response = await apiService.get(\"/syndrome\", syndromeParams);\r\n        if (response && response.data && response.data.length > 0) {\r\n            setDataToTable(response.data);\r\n            setTotalRows(response.totalCount);\r\n            setLoading(false);\r\n        }\r\n    };\r\n\r\n    const handlePageChange = (page: any) => {\r\n        syndromeParams.limit = perPage;\r\n        syndromeParams.page = page;\r\n        getSyndromeData();\r\n    };\r\n\r\n    const handlePerRowsChange = async (newPerPage: any, page: any) => {\r\n        syndromeParams.limit = newPerPage;\r\n        syndromeParams.page = page;\r\n        setLoading(true);\r\n        const response = await apiService.get(\"/syndrome\", syndromeParams);\r\n        if (response && response.data && response.data.length > 0) {\r\n            setDataToTable(response.data);\r\n            setPerPage(newPerPage);\r\n            setLoading(false);\r\n        }\r\n    };\r\n\r\n    const userAction = async (row: any) => {\r\n        setSelectSyndrome(row._id);\r\n        setModal(true);\r\n    };\r\n\r\n    const modalConfirm = async () => {\r\n        try {\r\n            await apiService.remove(`/syndrome/${selectSyndrome}`);\r\n            getSyndromeData();\r\n            setModal(false);\r\n            toast.success(t(\"adminsetting.syndrome.Table.syndromeDeletedSuccessfully\"));\r\n        } catch (error) {\r\n            toast.error(t(\"adminsetting.syndrome.Table.errorDeletingSyndrome\"));\r\n        }\r\n    };\r\n\r\n    const modalHide = () => setModal(false);\r\n\r\n    return (\r\n        <div>\r\n            <Modal show={isModalShow} onHide={modalHide}>\r\n                <Modal.Header closeButton>\r\n                    <Modal.Title>{t(\"adminsetting.syndrome.Deletesyndrome\")}</Modal.Title>\r\n                </Modal.Header>\r\n                <Modal.Body>{t(\"adminsetting.syndrome.Areyousurewanttodeletethissyndrome?\")}</Modal.Body>\r\n                <Modal.Footer>\r\n                    <Button variant=\"secondary\" onClick={modalHide}>\r\n                        {t(\"adminsetting.syndrome.Cancel\")}\r\n                    </Button>\r\n                    <Button variant=\"primary\" onClick={modalConfirm}>\r\n                        {t(\"adminsetting.syndrome.Yes\")}\r\n                    </Button>\r\n                </Modal.Footer>\r\n            </Modal>\r\n\r\n            <RKITable\r\n                columns={columns}\r\n                data={tabledata}\r\n                totalRows={totalRows}\r\n                pagServer={true}\r\n                handlePerRowsChange={handlePerRowsChange}\r\n                handlePageChange={handlePageChange}\r\n            />\r\n        </div>\r\n    );\r\n};\r\n\r\nexport default SyndromeTable;\r\n", "\n    (window.__NEXT_P = window.__NEXT_P || []).push([\n      \"/adminsettings/syndrome\",\n      function () {\n        return require(\"private-next-pages/adminsettings/syndrome/index.tsx\");\n      }\n    ]);\n    if(module.hot) {\n      module.hot.dispose(function () {\n        window.__NEXT_P.push([\"/adminsettings/syndrome\"])\n      });\n    }\n  "], "names": ["_props", "state", "t", "useTranslation", "ShowSyndromeIndex", "SyndromeIndex", "Container", "style", "overflowX", "fluid", "className", "Row", "Col", "xs", "PageHeading", "title", "Link", "href", "as", "<PERSON><PERSON>", "variant", "size", "SyndromeTable", "ShowAddSyndromes", "canAddSyndromes", "useSelector", "permissions", "syndrome", "NoAccessMessage", "create", "canAddAreaOfWork", "connectedAuthWrapper", "authenticatedSelector", "area_of_work", "wrapperDisplayName", "country", "deployment_status", "event_status", "expertise", "institution_focal_point", "hazard", "hazard_type", "institution", "institution_network", "canAddOrganisationTypes", "institution_type", "operation_status", "project_status", "region", "risk_level", "update_type", "users", "worl_region", "landing_page", "operation", "project", "event", "vspace", "update", "RKITable", "props", "paginationComponentOptions", "rowsPerPageText", "columns", "data", "totalRows", "resetPaginationToggle", "subheader", "subHeaderComponent", "handlePerRowsChange", "handlePageChange", "rowsPerPage", "defaultRowsPerPage", "selectableRows", "loading", "pagServer", "onSelectedRowsChange", "clearSelectedRows", "sortServer", "onSort", "persistTableHead", "sortFunction", "otherProps", "dataTableProps", "noDataComponent", "<PERSON><PERSON><PERSON><PERSON>", "dense", "paginationResetDefaultPage", "subHeader", "progressPending", "pagination", "paginationServer", "paginationPerPage", "paginationRowsPerPageOptions", "paginationTotalRows", "onChangeRowsPerPage", "onChangePage", "progressComponent", "CustomLoader", "sortIcon", "i", "DataTable", "defaultProps", "div", "h2", "tabledata", "setDataToTable", "useState", "setLoading", "setTotalRows", "perPage", "setPerPage", "isModalShow", "setModal", "selectSyndrome", "setSelectSyndrome", "name", "selector", "cell", "d", "code", "description", "replace", "_id", "a", "onClick", "userAction", "useEffect", "getSyndromeData", "syndromeParams", "sort", "limit", "page", "query", "response", "apiService", "get", "length", "totalCount", "newPerPage", "row", "modalConfirm", "remove", "toast", "success", "error", "modalHide", "Modal", "show", "onHide", "Header", "closeButton", "Title", "Body", "Footer"], "sourceRoot": "", "ignoreList": []}