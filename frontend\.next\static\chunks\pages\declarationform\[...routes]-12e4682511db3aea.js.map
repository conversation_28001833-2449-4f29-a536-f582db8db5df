{"version": 3, "file": "static/chunks/pages/declarationform/[...routes]-12e4682511db3aea.js", "mappings": "+NAuDA,MA3CmB,KACjB,IAAMA,EAASC,CAAAA,EAAAA,EAAAA,SAAAA,CAASA,EA0CXC,CAzCP,CAACC,EAAWC,EAAa,CAAGC,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAiB,MACrD,CAACC,EAAUC,EAAY,CAAGF,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAC,MACnCG,EAAQR,EAAOS,KAAK,CAACC,MAAM,CAAE,EAAE,CAE/BC,EAAa,MAAOC,IACxB,IAAMC,EAAW,MAAMC,EAAAA,CAAUA,CAACC,IAAI,CAAC,oBAAqBH,GACtDI,EAAOhB,EAAOS,KAAK,CAACQ,YAAY,CAAGjB,EAAOS,KAAK,CAACQ,YAAY,CAAG,KACrE,OAAOJ,EAAW,CAACT,EAAa,IAAOG,EAAY,CAAC,GAAGM,CAAQ,CAACI,aAAaD,CAAI,GAAG,CAAGZ,GAAa,EACtG,EAEAc,CAAAA,EAAAA,EAAAA,SAAAA,CAASA,CAAC,KACRP,EAAW,CAACQ,KAAMX,CAAK,EACzB,EAAG,EAAE,EAEL,IAAIY,EAAQ,UAACC,MAAAA,CAAIC,UAAU,mDAAmDC,MAAO,CAACC,QAAS,MAAM,WACnG,UAACC,EAAAA,CAAOA,CAAAA,CAACC,UAAU,SAASC,QAAQ,cAGtC,OAAQxB,GACN,KAAK,EACHiB,EAAO,UAACQ,EAAAA,OAAeA,CAAAA,CAACC,YAAavB,EAAUwB,UAAWtB,IAC1D,KACF,MAAK,EACHY,EAAO,UAACW,EAAAA,OAAWA,CAAAA,CAAAA,EACvB,CAEA,MACE,UAACV,MAAAA,UACED,GAGP,0JCQA,MA3CmB,IAOX,UAACC,MAAAA,CAAIC,OAoCEU,GApCQ,OAoCEA,EAAC,qDAnCd,WAACC,EAAAA,CAAIA,CAAAA,CAACX,UAAU,sBAAsBC,MAAO,CAAEW,UAAW,mBAAoBC,aAAc,OAAQC,MAAO,MAAO,YAC9G,UAACf,MAAAA,CAAIC,UAAU,4BACX,UAACe,EAAAA,CAAeA,CAAAA,CACZC,KAAMC,EAAAA,GAAqBA,CAE3BC,MAAM,YACNjB,MAAO,CACHkB,WAAY,UACZjB,QAAS,OACTW,aAAc,MACdC,MAAO,QACPM,OAAQ,QAERC,SAAU,OACd,MAGR,WAACV,EAAAA,CAAIA,CAACW,IAAI,YACN,UAACC,KAAAA,UAAI,UAACC,IAAAA,UAAE,oCACR,WAACC,EAAAA,CAAMA,CAAAA,CAACzB,UAAU,OAAOK,QAAQ,SAASqB,QAzBrC,CAyB8CC,IAxB/DC,IAAAA,IAAW,CAAC,QAChB,YAwBoB,UAACb,EAAAA,CAAeA,CAAAA,CACZC,KAAMa,EAAAA,GAAiBA,CACvBX,MAAM,UACR,uDCxC1B,4CACA,+BACA,WACA,OAAe,EAAQ,KAAoD,CAC3E,EACA,SAFsB", "sources": ["webpack://_N_E/./pages/declarationform/[...routes].tsx", "webpack://_N_E/./pages/declarationform/invalidLink.tsx", "webpack://_N_E/?9b92"], "sourcesContent": ["//Import Library\r\nimport React, {useState, useEffect} from 'react';\r\nimport {useRouter} from \"next/router\";\r\nimport {Spinner} from 'react-bootstrap';\r\n\r\n//Import services/components\r\nimport apiService from '../../services/apiService';\r\nimport DeclarationForm from \"./declarationform\";\r\nimport InvalidLink from \"./invalidLink\";\r\nimport Router from \"../operation/[...routes]\";\r\nimport { serverSideTranslations } from 'next-i18next/serverSideTranslations';\r\n\r\nconst Validation = () => {\r\n  const router = useRouter();\r\n  const [linkValid, setLinkValid] = useState<boolean | null>(null);\r\n  const [userData, setUserData] = useState(null);\r\n  const token = router.query.routes![0];\r\n\r\n  const checkToken = async (authParams: any) => {\r\n    const response = await apiService.post(\"/userLinkValidate\", authParams);\r\n    const lang = router.query.languageCode ? router.query.languageCode : \"en\";\r\n    return response ? [setLinkValid(true), setUserData({...response,languageCode:lang})] : setLinkValid(false)\r\n  };\r\n\r\n  useEffect(() => {\r\n    checkToken({code: token})\r\n  }, [])\r\n\r\n  let page = (<div className=\"d-flex justify-content-center align-items-center\" style={{padding: '24px'}}>\r\n    <Spinner animation=\"border\" variant=\"primary\"/>\r\n  </div>)\r\n\r\n  switch (linkValid) {\r\n    case true:\r\n      page = <DeclarationForm userProfile={userData} authToken={token}/>\r\n      break;\r\n    case false:\r\n      page = <InvalidLink/>\r\n  }\r\n\r\n  return (\r\n    <div>\r\n      {page}\r\n    </div>\r\n  )\r\n}\r\n\r\nexport async function getServerSideProps({ locale }: { locale: string}) {\r\n  return {\r\n    props: {\r\n      ...(await serverSideTranslations(locale, ['common'])),\r\n    },\r\n  }\r\n}\r\n\r\nexport default Validation;", "//Import Library\r\nimport React from 'react';\r\nimport { <PERSON>, Button, Container } from \"react-bootstrap\";\r\nimport {\r\n    faArrowCircleLeft,\r\n    faExclamationTriangle\r\n} from \"@fortawesome/free-solid-svg-icons\";\r\nimport { FontAwesomeIcon } from \"@fortawesome/react-fontawesome\";\r\nimport Router from \"next/router\"\r\n\r\nconst InvaidLink = () => {\r\n\r\n    const clickHandler = () => {\r\n        Router.push(\"/home\")\r\n    }\r\n\r\n    return (\r\n        <div className=\"d-flex justify-content-center align-content-center\">\r\n            <Card className=\"text-center m-4 p-5\" style={{ boxShadow: \"0 10px 20px #777\", borderRadius: \"10px\", width: '50vw' }}>\r\n                <div className=\"text-center pt-2\">\r\n                    <FontAwesomeIcon\r\n                        icon={faExclamationTriangle}\r\n\r\n                        color=\"indianRed\"\r\n                        style={{\r\n                            background: \"#d6deec\",\r\n                            padding: \"60px\",\r\n                            borderRadius: \"50%\",\r\n                            width: \"300px\",\r\n                            height: \"300px\",\r\n\r\n                            fontSize: \"100px\"\r\n                        }}\r\n                    />\r\n                </div>\r\n                <Card.Body>\r\n                    <h4 ><b>Huh! Looks like invalid link.</b></h4>\r\n                    <Button className=\"mt-3\" variant=\"danger\" onClick={clickHandler}>\r\n                        <FontAwesomeIcon\r\n                            icon={faArrowCircleLeft}\r\n                            color=\"white\"\r\n                        />\r\n                    &nbsp;&nbsp;Back to RKI Home</Button>\r\n                </Card.Body>\r\n\r\n\r\n            </Card>\r\n        </div>\r\n    )\r\n}\r\n\r\n\r\n\r\nexport default InvaidLink;", "\n    (window.__NEXT_P = window.__NEXT_P || []).push([\n      \"/declarationform/[...routes]\",\n      function () {\n        return require(\"private-next-pages/declarationform/[...routes].tsx\");\n      }\n    ]);\n    if(module.hot) {\n      module.hot.dispose(function () {\n        window.__NEXT_P.push([\"/declarationform/[...routes]\"])\n      });\n    }\n  "], "names": ["router", "useRouter", "Validation", "linkValid", "setLinkValid", "useState", "userData", "setUserData", "token", "query", "routes", "checkToken", "authParams", "response", "apiService", "post", "lang", "languageCode", "useEffect", "code", "page", "div", "className", "style", "padding", "Spinner", "animation", "variant", "DeclarationForm", "userProfile", "authToken", "InvalidLink", "InvaidLink", "Card", "boxShadow", "borderRadius", "width", "FontAwesomeIcon", "icon", "faExclamationTriangle", "color", "background", "height", "fontSize", "Body", "h4", "b", "<PERSON><PERSON>", "onClick", "clickHandler", "Router", "faArrowCircleLeft"], "sourceRoot": "", "ignoreList": []}