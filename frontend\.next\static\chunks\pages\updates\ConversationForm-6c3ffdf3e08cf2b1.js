(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[9702],{25820:(e,r,t)=>{(window.__NEXT_P=window.__NEXT_P||[]).push(["/updates/ConversationForm",function(){return t(44117)}])},35611:(e,r,t)=>{"use strict";t.d(r,{sx:()=>o,s3:()=>i.s3,ks:()=>i.ks,yk:()=>l.A});var l=t(54773),i=t(59200),a=t(37876),n=t(14232),s=t(39593),d=t(29504);let o={RadioGroup:e=>{let{name:r,valueSelected:t,onChange:l,errorMessage:i,children:d}=e,{errors:o,touched:u}=(0,s.j7)(),c=u[r]&&o[r];n.useMemo(()=>({name:r}),[r]);let v=n.Children.map(d,e=>n.isValidElement(e)&&function(e){return"object"==typeof e&&null!==e}(e.props)?n.cloneElement(e,{name:r,...e.props}):e);return(0,a.jsxs)("div",{children:[(0,a.jsx)("div",{className:"radio-group",children:v}),c&&(0,a.jsx)("div",{className:"invalid-feedback d-block",children:i||("string"==typeof o[r]?o[r]:String(o[r]))})]})},RadioItem:e=>{let{id:r,label:t,value:l,name:i,disabled:n}=e,{values:o,setFieldValue:u}=(0,s.j7)(),c=i||r;return(0,a.jsx)(d.A.Check,{type:"radio",id:r,label:t,value:l,name:c,checked:o[c]===l,onChange:e=>{u(c,e.target.value)},disabled:n,inline:!0})}};l.A,i.ks,i.s3},44117:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>u});var l=t(37876);t(14232);var i=t(49589),a=t(56970),n=t(37784),s=t(29504),d=t(35611),o=t(31753);let u=e=>{let{t:r}=(0,o.Bd)("common"),{title:t,onHandleChange:u}=e;return(0,l.jsx)(i.A,{className:"formCard",fluid:!0,children:(0,l.jsx)(a.A,{children:(0,l.jsx)(n.A,{children:(0,l.jsxs)(s.A.Group,{children:[(0,l.jsxs)(s.A.Label,{className:"required-field",children:[r("update.Conversation")," ",r("update.Title")]}),(0,l.jsx)(d.ks,{name:"title",id:"title",required:!0,value:t,onChange:u,validator:e=>""!=e.trim(),errorMessage:{validator:r("Pleaseprovideconversationatitle")}})]})})})})}},54773:(e,r,t)=>{"use strict";t.d(r,{A:()=>d});var l=t(37876),i=t(14232),a=t(39593),n=t(91408);let s=(0,i.forwardRef)((e,r)=>{let{children:t,onSubmit:i,autoComplete:s,className:d,onKeyPress:o,initialValues:u,...c}=e,v=n.Ik().shape({});return(0,l.jsx)(a.l1,{initialValues:u||{},validationSchema:v,onSubmit:(e,r)=>{let t={preventDefault:()=>{},stopPropagation:()=>{},currentTarget:null,target:null,nativeEvent:new Event("submit"),bubbles:!1,cancelable:!0,defaultPrevented:!1,eventPhase:0,isTrusted:!1,timeStamp:Date.now(),type:"submit",isDefaultPrevented:()=>!1,isPropagationStopped:()=>!1,persist:()=>{}};i&&i(t,e,r)},...c,children:e=>(0,l.jsx)(a.lV,{ref:r,onSubmit:e.handleSubmit,autoComplete:s,className:d,onKeyPress:o,children:"function"==typeof t?t(e):t})})});s.displayName="ValidationFormWrapper";let d=s},59200:(e,r,t)=>{"use strict";t.d(r,{ks:()=>n,s3:()=>s});var l=t(37876);t(14232);var i=t(29504),a=t(39593);let n=e=>{let{name:r,id:t,required:n,validator:s,errorMessage:d,onChange:o,value:u,as:c,multiline:v,rows:h,pattern:p,...m}=e;return(0,l.jsx)(a.D0,{name:r,validate:e=>{let r="string"==typeof e?e:String(e||"");return n&&(!e||""===r.trim())?(null==d?void 0:d.validator)||"This field is required":s&&!s(e)?(null==d?void 0:d.validator)||"Invalid value":p&&e&&!new RegExp(p).test(e)?(null==d?void 0:d.pattern)||"Invalid format":void 0},children:e=>{let{field:r,meta:a}=e;return(0,l.jsxs)(l.Fragment,{children:[(0,l.jsx)(i.A.Control,{...r,...m,id:t,as:c||"input",rows:h,isInvalid:a.touched&&!!a.error,onChange:e=>{r.onChange(e),o&&o(e)},value:void 0!==u?u:r.value}),a.touched&&a.error?(0,l.jsx)(i.A.Control.Feedback,{type:"invalid",children:a.error}):null]})}})},s=e=>{let{name:r,id:t,required:n,errorMessage:s,onChange:d,value:o,children:u,...c}=e;return(0,l.jsx)(a.D0,{name:r,validate:e=>{if(n&&(!e||""===e))return(null==s?void 0:s.validator)||"This field is required"},children:e=>{let{field:r,meta:a}=e;return(0,l.jsxs)(l.Fragment,{children:[(0,l.jsx)(i.A.Control,{as:"select",...r,...c,id:t,isInvalid:a.touched&&!!a.error,onChange:e=>{r.onChange(e),d&&d(e)},value:void 0!==o?o:r.value,children:u}),a.touched&&a.error?(0,l.jsx)(i.A.Control.Feedback,{type:"invalid",children:a.error}):null]})}})}}},e=>{var r=r=>e(e.s=r);e.O(0,[636,6593,8792],()=>r(25820)),_N_E=e.O()}]);
//# sourceMappingURL=ConversationForm-6c3ffdf3e08cf2b1.js.map