{"version": 3, "file": "static/chunks/e098907b-28cd164d1c549b26.js", "mappings": "uIAkDA,EACA,EA00BA,EAgOA,EA4nHA,EACA,EAmmDA,mCAxzMA,cAGA,iFACA,eACA,EAAI,YACJ,mGACA,EAAG,GACH,CAkBA,sBAJA,EAKA,OALA,WAZA,KACA,+BACA,4BACA,eACA,6BACA,0BACA,gEACA,CACA,qCACA,EAQA,EALA,UAKA,GAJA,qBAIA,kCACA,QACA,cACA,gBACA,WACA,CAAG,UACH,CAEA,cACA,qFACA,CAqDA,QA1CA,KACA,IAoCA,EAtBA,0BAMA,OACA,MACA,cACA,sCAAwD,uGAChD,CACR,oBACA,GAIA,CAHA,oCACA,cACS,IACT,0BACA,CAEA,MADA,gBACA,CACA,CACA,IAQA,EAAiB,mBAAa,OA+D9B,cACA,mCACA,CACA,aACA,gEACA,YACA,CACA,cACA,IA9DA,IA2BA,EApBA,EAuDA,CACA,aACA,WACA,YACA,YACA,WACA,CAAI,EACJ,GArEA,EA4CA,aAEA,GAIA,MAHA,yBACA,qCAqBA,EArBA,EAqBA,CArBA,MAEA,CACA,CAAG,CAnDH,EAmDG,GAlDH,YAoEA,GApEA,qBACA,kBACA,CAAG,KAoEH,OA5CA,KApBA,EAsBA,QACA,MAwCA,CAxCA,IACA,WACA,OACA,EAqCA,EArCA,GAEA,EA3BA,YA8DA,GA9DA,WACA,EA6DA,CA7DA,QA8DA,CACA,CAEA,OACA,sBACA,oBACA,wBACA,uCACA,wBACA,sBACA,wBACA,wBACA,oBACA,0BACA,4BACA,iCACA,iCACA,gBACA,cACA,mCACA,cACA,yCACA,kBACA,6BACA,4BACA,EACA,GACA,mBACA,wBACA,2BACA,CAAK,CACL,CAAG,CACH,YACA,cACA,CAAG,CACH,oBACA,sBACA,CAAG,CACH,aACA,eACA,CAAG,CACH,eACA,iBACA,CAAG,CACH,aACA,eACA,CAAG,CACH,gBACA,kBACA,CAAG,CACH,UACA,YACA,CAAG,CACH,UACA,YACA,CACA,EAyLA,UAAI,CAvLJ,YACA,IACA,WACA,UACA,KACA,oBACA,wBACA,SAKA,UACA,aACA,SACA,YACA,cACA,cACA,aACA,cACA,cACA,YACA,eAIA,kBAOA,SACA,YACA,CAAI,EACJ,MAAsB,cAAQ,OAC9B,EAAY,YAAM,OAElB,MAA0D,cAAQ,OAClE,MAAgD,cAAQ,OACxD,MAA8C,cAAQ,OACtD,MAAkD,cAAQ,OAC1D,MAAkD,cAAQ,OAC1D,MAAkD,cAAQ,OAC1D,MAAgD,cAAQ,OACxD,MAAkD,cAAQ,OAC1D,MAA8C,cAAQ,OACtD,MAAoD,cAAQ,OAC5D,MAA0C,cAAQ,OAClD,MAAwC,cAAQ,OA0HhD,MAxHE,eAAS,MACX,aACA,eAEA,CAAG,QACD,eAAS,MACX,sBACA,cAEA,CAAG,QACD,eAAS,MACX,OACA,UACA,oCAEA,iDAEA,CAAG,MACD,eAAS,MACX,OACA,UACA,oCAEA,gDAEA,CAAG,MACD,eAAS,MACX,OACA,UACA,oCAEA,kDAEA,CAAG,MACD,eAAS,MACX,OACA,UACA,oCAEA,kDAEA,CAAG,MACD,eAAS,MACX,OACA,UACA,oCAEA,kDAEA,CAAG,MACD,eAAS,MACX,OACA,UACA,oCAEA,iDAEA,CAAG,MACD,eAAS,MACX,OACA,UACA,oCAEA,kDAEA,CAAG,MACD,eAAS,MACX,OACA,UACA,oCAEA,gDAEA,CAAG,MACD,eAAS,MACX,OACA,UACA,oCAEA,mDAEA,CAAG,MACD,eAAS,MACX,OACA,UACA,oCAEA,8CAEA,CAAG,MACD,eAAS,MACX,OACA,UACA,oCAEA,6CAEA,CAAG,MACD,eAAS,MACX,OACA,UACA,oCAEA,uDAEA,CAAG,MACD,eAAS,MACX,6DAKA,OAJA,KACA,aACA,KAEA,KACA,UACA,GACA,IAGA,CACA,CAAG,KACM,SAAG,QACZ,KACA,MACA,QACA,YACA,SAAc,SAAG,aACjB,QACA,wBACA,CAAK,CACL,CAAG,CACH,EAEA,iBAAwB,eAAa,CACrC,cACA,oBACA,gBACA,QACA,CAAK,EACL,8BACA,sBACA,yBACA,mBACA,KAEA,qDAEA,mBACA,wBACA,IACA,UAEA,CAAK,EACL,6BACA,uBACA,mBACA,iCAGA,CAAK,EACL,oBACA,aACA,CAAK,CACL,CACA,oBACA,wBACA,0BACA,aACA,WACA,YAAmB,CACnB,qBACA,UACA,CAAK,EACL,yBACA,OACA,KACA,CACA,CAAK,qBACL,CACA,sBACA,wBACA,yBACA,yBACA,aACA,WACA,YACA,qBACA,wBACO,EAEP,CACA,uBACA,wBACA,sBACA,qCAEA,yBAEA,CACA,SACA,MAAW,SAAG,QACd,iBACA,gBACA,mCACA,2CACA,SAAgB,SAAG,aACnB,qBACA,uDACA,CAAO,CACP,CAAK,CACL,CACA,CAEA,0BACA,IACA,cACA,UACI,SACJ,gBACA,CACA,wCACA,CACA,cACA,kBACA,WACA,YACA,iCACA,mBACA,cACA,qBACA,CACA,cACA,sBACA,CACA,SACA,CAAK,CACL,CACA,CAEA,cACA,IACA,mBACA,qBACA,mBACA,WACA,SACA,YACA,UACA,SACA,qBACA,CAAI,EACJ,KA8BA,OA7BA,2KACA,EACA,yBACI,GACJ,4BAEA,GACA,uBAEA,GACA,8BAEA,GACA,4BAEA,aACA,gDAEA,GACA,6BAEA,aACA,uCAEA,GACA,0CAEA,wBACA,2BACA,8DACA,CAEA,mCAEA,cACA,IACA,MACA,KACA,QAEA,CAAI,SACJ,EAGA,0BACA,iCACA,SACA,MAEA,mCACA,0BACA,eACA,gBACU,CACV,gBACA,WACA,sBACA,GACA,IAEA,IACA,EACA,sBACA,GACA,KAEA,IACA,EACA,MACA,CAMA,UAEA,CACA,sCACA,0BACA,QACA,OACA,WACA,cACA,sBACA,qCACA,IACA,EACA,qBACA,qCACA,IACA,EACA,4BACA,CAAG,YAEH,MADA,wCACA,CACA,CAAG,EAtDH,8CAuDA,CAEA,cAEA,mBACA,oJAOA,mCAGA,cAGA,sBAGA,kEAGA,wBACA,IAMA,mCAGA,aAGA,yDAGA,eACA,IAMA,oCAGA,eAGA,YAIA,CAEA,aAGA,+CACA,MACA,4BAEA,8BAIA,OAHA,MACA,yBAEA,CACA,EACA,2BAEA,2BAIA,OAHA,MACA,uBAEA,CACA,CACA,CACA,CAEA,SACA,aACA,MAAS,SAAG,QACZ,qBACA,CAAG,CACH,CACA,OACA,mBACA,gBACA,CACA,iBAAyB,eAAa,CACtC,cACA,oBACA,qBACA,gBACA,SACA,CAAK,EACL,8BAGA,0BACA,mBACA,CAAK,EACL,oCAgBA,mBAfA,YACA,KAGA,MACA,oCACA,IACA,wBACA,IAEA,CAAa,GACb,MATA,GAYA,EAEA,CAAK,GACL,sBACA,KACA,4CACA,kBACA,4BAEA,uFACA,+DACA,CAAO,sBACP,cACA,2BAEA,CAAO,EACP,qFACA,4FACO,sBACP,cACA,2BAEA,CAAO,EACP,sFACA,+EACA,CAAO,sBACP,cACA,2BAEA,CAAO,CACP,CAAK,EACL,2BACA,sCACA,IAEA,oFAMA,EALA,CACA,iBACA,uBACA,iBACA,GACA,UACA,mBACA,oBAEA,yBACA,OACA,SACA,CACA,CAAS,CAET,CAAO,YACP,oBACA,sBAEA,oUACA,CAAO,CACP,CAAK,EACL,oBACA,YACA,CAAK,CACL,CACA,oBACA,MACA,qDACA,iDAGA,8DACA,gEACA,CAAO,CACP,CACA,CACA,sBACA,oCACA,iUAEA,sCACA,eAEA,yBACA,OACA,SACA,CACA,CAAO,uBAEP,CACA,uBACA,IACA,eASA,kBARA,KACA,aAGA,qBACA,KAEA,EACA,GACA,sBACA,uBAGA,CACA,SACA,MAAW,UAAI,CAAC,UAAQ,EACxB,UAAiB,SAAG,QACpB,eACA,CAAO,mEAA0E,SAAG,KAA0B,GACzG,CACL,CACA,CAaA,gBACA,oBACA,MACA,EACA,EAdA,cACA,oBACA,SACA,sBAAyB,2BACzB,yBACA,UACA,CACA,QACA,EAMA,KACA,iCACA,sCACA,QAAgB,WAAc,8BAAkC,4CAChE,CACA,QACA,CAtBA,sBA+GA,mEACA,EAA4B,SAAG,KAA0B,EAiClC,UAAI,CAhC3B,YACA,IACA,iBACA,SACA,UACA,YACA,WACA,CAAM,EAEN,CACA,WACA,YACA,CAnGA,YACA,IACA,UACA,oBACA,QACA,mBACA,qBACA,WACA,SACA,YACA,4BACA,UACA,SACA,qBACA,CAAI,EACJ,EAAkB,YAAM,KACxB,MAA8B,cAAQ,KACtC,MAAkC,cAAQ,SACxC,eAAS,YAEX,OADA,aACA,KACA,YACA,CACA,CAAG,KACD,eAAS,YACX,MACA,GAEA,CAAG,MACD,eAAS,YACX,GACA,gHAEA,CAAG,MACH,SACA,UACA,mBACA,qBACA,WACA,SACA,YACA,UACA,SACA,oBACA,CAAG,EACD,eAAS,YACX,MASA,wDACA,IAGA,GACA,KACA,MACA,OACA,CAAK,4BACL,WACA,KAEA,sPACA,gBACA,CAAK,EApBL,aACA,YACA,MACA,IAEA,CAgBA,CAAG,UACH,MAAsB,YAAM,SAO5B,MANE,eAAS,YACX,0BACA,iUAEA,WACA,CAAG,MACH,CACA,WACA,YACA,KACA,CACA,EAYA,QAsBA,MAjBE,eAAS,YACX,yBACA,GAEA,CAAG,QACD,eAAS,YACX,yBACA,IAEA,CAAG,QACD,eAAS,YACX,WACA,GACA,GAEA,CACA,CAAG,MACH,QACA,GA8CA,oDAuDA,8BAKA,YACA,iCACA,yBACA,yBACA,wBACA,CAAC,UAAoC,CAoBrC,SAUA,eACA,IACA,SACA,qBACA,UACA,SACA,OACA,WACA,eACA,SACA,QACA,SACA,YACA,gDACA,UACA,CAAM,EAkBN,GAjBA,kBACA,aACA,gBACA,eACA,cACA,0BACA,eACA,cACA,aACA,UADgC,GAChC,GACA,iBACA,cACA,aACA,cACA,eACA,WACA,eACA,YACA,KA3F2B,MA2F3B,iCACA,WA5FmE,CA4FnE,oJAEA,kBAEA,gBAEA,cACA,OACA,qBACA,mBACA,qBACA,mBACA,WACA,yBACA,uBACA,mBACA,mBACA,iBACA,aACA,2CAEA,CACA,oBACA,mBACA,UAEA,UACA,UAEA,aACA,UAEA,cAEA,aACA,mEACA,CAOA,YACA,eA6BA,OA5BA,kDACA,aACA,iCAEA,cACA,sCAEA,aACA,oCAEA,yBACA,oDAEA,eACA,wCAEA,aACA,oCAEA,cACA,gCAEA,aACA,+CAEA,yBACA,8DAEA,CACA,CACA,eACA,sCACA,IACA,UAEA,CAKA,OACA,yBACA,CAOA,cACA,2BACA,sBACA,EAGA,WAFA,gBAIA,CAAO,CACP,CAAK,CACL,CACA,iBAEA,OADA,eACA,4BACA,CAKA,gBACA,uBACA,cACA,CAIA,YAEA,gDAEA,gBAGA,IAqBA,EACA,EACA,EACA,EACA,EACA,EACA,EACA,EACA,EAIA,EACA,EACA,EACA,EA1CA,IAMA,GACA,gBACA,qBACA,mBACA,gDACA,eACA,mBACA,uBACA,mBACA,4CAGA,uBAEA,uBACA,+FASA,mCACA,WACA,kBACA,WACA,WAKA,EAFA,IAFA,SAEA,cAA8B,EAE9B,iBAAsC,CACtC,UACA,sBACA,MAEA,8BAlUA,eAkUA,KAlUA,EAkUA,OAlUA,EAkUA,YACA,MAKA,SAJA,kCACA,aACA,6BAkBO,EAhBP,0DACA,+BACA,qBACA,OACA,+CAEA,oFACA,gBACA,CAAW,CA1UX,kCACA,cACA,IACA,YACA,CAAQ,SACR,IACA,CACA,CACA,cACA,IACA,aACA,CAAQ,SACR,IACA,CACA,CACA,kBApBA,CAqBA,mBApBA,EADA,EAqBA,mBApBA,sBACA,IACA,EAAK,EAkBL,SACA,CACA,2BACA,CAAG,GAuTQ,EAEX,kDAMO,GANP,iBACA,kDAA8F,IAAa,IAC3G,oBAEA,2CACA,GAOA,kDAEA,WACA,mCAEA,4CACA,8BACA,OACA,CAAO,EACP,CADU,GACV,qBACA,CAAK,CACL,CAIA,QACA,oBACA,aACA,gBACA,eACA,sBACA,CACA,wBACA,aACA,YAEA,CACA,qBAEA,GADA,oBACA,kCACA,wDACA,kFACA,gBACA,oBACA,gBACA,CAAO,GACP,EAAM,IACN,oBACA,eAEA,CACA,WACA,aACA,gBACA,2BACA,oBACA,CAAK,EACL,kBAEA,UAEA,GADA,8BACA,aAIA,aACA,oBACM,CAEN,kEACA,sKACA,gBACA,MACA,CACA,gBACA,gBACA,CACA,CACA,CAwEA,gBAA2B,qBAAwB,iCAAoC,sCAAyC,2BAAkC,wDAA0D,oBAA0B,SACtP,cAA8B,YAAgB,mBAAsB,KAAO,yCAAkD,wCAAwD,YAA8B,mIAAqJ,gEAAqE,EAAK,SAClb,SACA,GACA,aACA,eACA,CACA,EAuCoB,UAAI,CAtCxB,YACA,IACA,UACA,SACA,YACA,CAAI,EACJ,EAAY,gBAAU,IACtB,MAAgC,cAAQ,OA6BxC,MA3BE,eAAS,MACX,UACA,WAEA,CAAG,MACD,eAAS,MACX,aACA,eAEA,CAAG,QACD,eAAS,MACX,yCAAsF,MAAc,EACpG,KACA,CAAK,GAKL,OAJA,KACA,GACA,KAEA,KACA,WACA,GACA,KAEA,eAEA,CACA,CAAG,KACH,IACA,EAEA,iBAA2B,eAAa,CACxC,cACA,oBACA,gBACA,iBACA,CAAK,EACL,sCACA,mDACA,0CAEA,CAAK,EACL,6BACA,CACA,oBACA,yCAAsF,uBAAyB,EAC/G,iBACK,EACL,0BACA,aACA,WACA,YAAmB,CACnB,qBACA,UACA,CAAK,EACL,yBACA,OACA,cACA,CACA,CAAK,8BACL,CACA,sBACA,iCACA,yBACA,yBACA,aACA,WACA,YACA,qBACA,iCACO,EAEP,CACA,uBACA,iCACA,sBACA,8CAEA,yBACA,qCAEA,CACA,SACA,WACA,CACA,CACA,qBAiCsB,UAAI,CA/B1B,YACA,IACA,SACA,YACA,CAAI,EACJ,EAAY,gBAAU,IACtB,MAAgC,cAAQ,OAuBxC,MArBE,eAAS,MACX,UACA,WAEA,CAAG,MACD,eAAS,MACX,qCAMA,OALA,KACA,YACA,GACA,KAEA,KACA,WACA,GACA,KAEA,eAEA,CACA,CAAG,KACH,IACA,EAEA,iBAA6B,eAAa,CAC1C,cACA,oBACA,gBACA,mBACA,CAAK,EACL,wCACA,mCACA,+CACA,mBACA,6CAGA,CAAK,CACL,CACA,oBACA,qCACA,kBACA,EACA,gBACA,GACK,+BACL,CACA,uBACA,mCACA,sBACA,gDAEA,uCAEA,CACA,SACA,WACA,CACA,CACA,qBAiCoB,UAAI,CA/BxB,YACA,IACA,SACA,YACA,CAAI,EACJ,EAAY,gBAAU,IACtB,MAAgC,cAAQ,OAuBxC,MArBE,eAAS,MACX,UACA,WAEA,CAAG,MACD,eAAS,MACX,mCAMA,OALA,KACA,YACA,GACA,KAEA,KACA,WACA,GACA,KAEA,eAEA,CACA,CAAG,KACH,IACA,EAEA,iBAA2B,eAAa,CACxC,cACA,oBACA,gBACA,iBACA,CAAK,EACL,sCACA,iCACA,6CACA,mBACA,2CAGA,CAAK,CACL,CACA,oBACA,mCACA,yBACA,OACA,cACA,CACA,CAAK,8BACL,CACA,uBACA,iCACA,sBACA,8CAEA,qCAEA,CACA,SACA,WACA,CACA,CAGA,gBAA2B,qBAAwB,iCAAoC,sCAAyC,2BAAkC,wDAA0D,oBAA0B,SACtP,cAA8B,YAAgB,mBAAsB,KAAO,yCAAkD,wCAAwD,YAA8B,mIAAqJ,gEAAqE,EAAK,SAHlb,qBAIA,OACA,kCACA,kCACA,oCACA,oCACA,sCACA,uCACA,EACA,GACA,iBACA,mBACA,CAAG,CACH,aACA,eACA,CACA,EAiJsB,UAAI,CAhJ1B,YACA,IACA,UACA,cACA,mBACA,mBACA,oBACA,oBACA,qBACA,sBACA,SACA,YACA,CAAI,EACJ,EAAY,gBAAU,IACtB,MAAgC,cAAQ,OACxC,MAA4D,cAAQ,OACpE,MAA4D,cAAQ,OACpE,MAA8D,cAAQ,OACtE,MAA8D,cAAQ,OACtE,MAAgE,cAAQ,OACxE,MAAkE,cAAQ,OA0H1E,MAxHE,eAAS,MACX,UACA,WAEA,CAAG,MACD,eAAS,MACX,aACA,eAEA,CAAG,QACD,eAAS,MACX,UACA,gCAEA,CAAG,QACD,eAAS,MACX,OACA,UACA,oCAEA,uDAEA,CAAG,QACD,eAAS,MACX,OACA,UACA,oCAEA,uDAEA,CAAG,QACD,eAAS,MACX,OACA,UACA,oCAEA,wDAEA,CAAG,QACD,eAAS,MACX,OACA,UACA,oCAEA,wDAEA,CAAG,QACD,eAAS,MACX,OACA,UACA,oCAEA,yDAEA,CAAG,QACD,eAAS,MACX,OACA,UACA,oCAEA,0DAEA,CAAG,QACD,eAAS,MACX,yDAAsE,aAAa,qCACnF,mDAAkG,MAAc,EAChH,KACA,CAAK,GA0BL,OAzBA,GACA,oBAEA,GACA,uDAEA,GACA,uDAEA,GACA,wDAEA,GACA,wDAEA,GACA,yDAEA,GACA,0DAEA,KACA,GACA,KAEA,KACA,WACA,GACA,oCAEA,GACA,oCAEA,GACA,oCAEA,GACA,oCAEA,GACA,oCAEA,GACA,oCAEA,GACA,KAEA,eAEA,CACA,CAAG,KACH,IACA,EAEA,iBAA6B,eAAa,CAC1C,eACA,SACA,8BACA,gBACA,mBACA,CAAK,EACL,wCACA,qDACA,4CAEA,CAAK,EACL,yDAAsE,aAAa,oCACnF,CACA,oBACA,mDAAkG,uBAAyB,EAC3H,iBACK,EACL,0BACA,aACA,WACA,YAAmB,CACnB,qBACA,UACA,CAAK,EACL,yBACA,OACA,gBACA,CACA,CAAK,gCACL,CACA,sBACA,mCACA,yBACA,yBACA,aACA,WACA,YACA,qBACA,mCACO,EAEP,CACA,uBACA,mCACA,sBACA,gDAEA,yBACA,uCAEA,CACA,SACA,WACA,CACA,CAGA,gBAA2B,qBAAwB,iCAAoC,sCAAyC,2BAAkC,wDAA0D,oBAA0B,SACtP,cAA8B,YAAgB,mBAAsB,KAAO,yCAAkD,wCAAwD,YAA8B,mIAAqJ,gEAAqE,EAAK,SAHlb,qBAIA,QACA,uCACA,gBACA,uCACA,iCACA,sBACA,cACA,oBACA,uCACA,wBACA,6BACA,6BACA,wBACA,sBACA,wBACA,oBACA,qCACA,0BACA,+BACA,+BACA,mCACA,gCACA,EACA,IACA,eACA,iBACA,CAAG,CACH,eACA,iBACA,CAAG,CACH,YACA,cACA,CAAG,CACH,eACA,iBACA,CAAG,CACH,UACA,YACA,CAAG,CACH,WACA,aACA,CAAG,CACH,SACA,WACA,CAAG,CACH,aACA,eACA,CAAG,CACH,aACA,eACA,CAAG,CACH,cACA,gBACA,CAAG,CACH,WACA,aACA,CAAG,CACH,WACA,aACA,CAAG,CACH,aACA,eACA,CAAG,CACH,YACA,cACA,CACA,EACA,MAkfc,UAAI,CAjflB,YACA,IACA,WACA,UACA,YACA,oBACA,WACA,YACA,UACA,YACA,YACA,SACA,OACA,QACA,UACA,QACA,QACA,SACA,UACA,aACA,SACA,YACA,cACA,aACA,cACA,YACA,cACA,eACA,qBACA,kBACA,qBACA,qBACA,gBACA,gBACA,oBACA,iBACA,iBACA,mBACA,kBACA,SACA,YACA,CAAI,EACJ,EAAY,gBAAU,IACtB,MAAgC,cAAQ,OACxC,MAAgD,cAAQ,OACxD,MAA8C,cAAQ,OACtD,MAAkD,cAAQ,OAC1D,OAAkD,cAAQ,OAC1D,QAAgD,cAAQ,OACxD,QAAkD,cAAQ,OAC1D,QAA8C,cAAQ,OACtD,QAAoD,cAAQ,OAC5D,QAA0C,cAAQ,OAClD,QAAwC,cAAQ,OAChD,QAAgE,cAAQ,OACxE,QAA0D,cAAQ,OAClE,QAAgE,cAAQ,OACxE,QAAgE,cAAQ,OACxE,QAAsD,cAAQ,OAC9D,QAAsD,cAAQ,OAC9D,QAA8D,cAAQ,OACtE,QAAwD,cAAQ,OAChE,QAAwD,cAAQ,OAChE,QAA4D,cAAQ,OACpE,QAA0D,cAAQ,OAEhE,eAAS,MACX,UACA,WAEA,CAAG,MACD,eAAS,MACX,sBACA,eAEA,CAAG,QACD,eAAS,MACX,sBACA,iBAEA,CAAG,QACD,eAAS,MACX,aACA,gBAEA,CAAG,QACD,eAAS,MACX,sBACA,eAEA,CAAG,QACD,eAAS,MACX,0BACA,CAAG,QACD,eAAS,MACX,eACA,iBAEA,CAAG,QACD,eAAS,MACX,eACA,cAEA,CAAG,QACD,eAAS,MACX,eACA,YAEA,CAAG,QACD,eAAS,MACX,eACA,aAEA,CAAG,QACD,eAAS,MACX,eACA,eAEA,CAAG,QACD,eAAS,MACX,eACA,aAEA,CAAG,QACD,eAAS,MACX,eACA,aAEA,CAAG,QACD,eAAS,MACX,eACA,cAEA,CAAG,QACD,eAAS,MACX,OACA,UACA,oCAEA,iDAEA,CAAG,MACD,eAAS,MACX,OACA,UACA,oCAEA,gDAEA,CAAG,MACD,eAAS,MACX,OACA,UACA,oCAEA,kDAEA,CAAG,MACD,eAAS,MACX,OACA,UACA,oCAEA,mDAEA,CAAG,MACD,eAAS,MACX,OACA,WACA,qCAEA,kDAEA,CAAG,MACD,eAAS,MACX,OACA,WACA,qCAEA,mDAEA,CAAG,MACD,eAAS,MACX,OACA,WACA,qCAEA,iDAEA,CAAG,MACD,eAAS,MACX,OACA,WACA,qCAEA,oDAEA,CAAG,MACD,eAAS,MACX,OACA,WACA,qCAEA,+CAEA,CAAG,MACD,eAAS,MACX,OACA,WACA,qCAEA,8CAEA,CAAG,MACD,eAAS,MACX,OACA,WACA,qCAEA,2DAEA,CAAG,MACD,eAAS,MACX,OACA,WACA,qCAEA,wDAEA,CAAG,MACD,eAAS,MACX,OACA,WACA,qCAEA,2DAEA,CAAG,MACD,eAAS,MACX,OACA,WACA,qCAEA,2DAEA,CAAG,MACD,eAAS,MACX,OACA,WACA,qCAEA,sDAEA,CAAG,MACD,eAAS,MACX,OACA,WACA,qCAEA,sDAEA,CAAG,MACD,eAAS,MACX,OACA,WACA,qCAEA,0DAEA,CAAG,MACD,eAAS,MACX,OACA,WACA,qCAEA,uDAEA,CAAG,MACD,eAAS,MACX,OACA,WACA,qCAEA,uDAEA,CAAG,MACD,eAAS,MACX,OACA,WACA,qCAEA,yDAEA,CAAG,MACD,eAAS,MACX,OACA,WACA,qCAEA,wDAEA,CAAG,MACD,eAAS,MACX,cAA0E,cAC1E,KACA,CAAK,IAAK,EACV,UACA,CAAK,EACL,4BA0GA,OAzGA,EACA,mBAEA,YAEA,GACA,iBAEA,YACA,gBAEA,YACA,kBAEA,YACA,kBAEA,oBACA,eAEA,GACA,aAEA,YACA,cAEA,YACA,gBAEA,GACA,cAEA,oBACA,cAEA,oBACA,eAEA,GACA,iDAEA,GACA,gDAEA,GACA,kDAEA,GACA,mDAEA,GACA,kDAEA,GACA,mDAEA,GACA,iDAEA,GACA,oDAEA,GACA,+CAEA,GACA,8CAEA,GACA,2DAEA,GACA,wDAEA,GACA,2DAEA,GACA,2DAEA,GACA,sDAEA,GACA,sDAEA,GACA,0DAEA,GACA,uDAEA,GACA,uDAEA,GACA,yDAEA,GACA,wDAEA,KACA,GACA,KAEA,KACA,UACA,oCAEA,UACA,oCAEA,UACA,oCAEA,UACA,oCAEA,WACA,qCAEA,WACA,qCAEA,WACA,qCAEA,WACA,qCAEA,WACA,qCAEA,WACA,qCAEA,WACA,qCAEA,WACA,qCAEA,WACA,qCAEA,WACA,qCAEA,WACA,qCAEA,WACA,qCAEA,WACA,qCAEA,WACA,qCAEA,WACA,qCAEA,GACA,KAEA,EACA,sBACQ,GACR,cAEA,CACA,CAAG,KACH,OAAY,aAAO,KACnB,EAAsB,UAAQ,UAC9B,CAAW,mBAAc,IAIZ,kBAAY,CADzB,EACyB,CACzB,QACA,CAAO,EALP,GAMK,KACF,OACH,MAAS,SAAG,CAAC,UAAQ,EACrB,WACA,CAAG,OACH,EAEA,kBAAqB,eAAa,CAClC,cACA,oBACA,6BACA,CACA,oBACA,WACA,qBACA,cAA4E,4CAC5E,cACO,IAAK,EACZ,0BACO,CAGP,oCACA,kBACA,kEAEA,2BAEA,sBACA,cACA,YACA,YAAqB,CACrB,kBACA,kBACO,EACP,gBACA,wBAEA,CAAK,GACL,CACA,sBACA,cACA,yBACA,yBACA,cACA,YACA,YACA,qBACA,qBACO,EAEP,CACA,uBACA,cAGA,sBACA,kCAEA,yBACA,qBACA,8EACM,aACN,yBAEA,CACA,SAUA,OATA,oBAAyC,UAAQ,4BACjD,CAAW,mBAAc,IAIZ,kBAAY,CADzB,EACyB,CACzB,kBACA,CAAO,EALP,GAMK,OACL,IACA,CACA,CACA,sBAEA,kBACA,gBACA,mDACA,eACA,oEACA,qCACA,cACA,mBACA,cACA,eACA,gBACA,gCACA,YACA,cACA,aACA,sBACA,sBACA,uBACA,iBACA,2BACA,uBACA,wBACA,mCACA,8BACA,8BACA,gCACA,kBACA,wBACA,CADmC,GACnC,iDACA,6CACA,qCACA,6CACA,2CACA,iCACA,uCACA,+BACA,+BACA,+BACA,uCACA,yCACA,sDACA,CAwLA,OAvLA,uCACA,qDAEA,mCACA,4BACA,6BACA,EACA,gCAEA,GADA,4BACA,6BACA,kCAWA,GAJA,kDACA,yDAGA,CAHkF,CAGlF,kBAEA,qBACA,2BACA,YACA,4BACA,eAGA,0CACA,iBACA,aACA,iBACA,eAEA,oBAEA,gBACA,cAEA,CACA,CAAS,KACT,CAEA,kBACA,mBACA,mBAEA,CACA,EACA,mCAOA,+EACA,EACA,kCAOA,8EACA,EACA,6BAEA,uCACA,kCACA,cACA,YAEA,sEACA,IAPA,EAOA,eACA,YAEA,kGACA,wDACA,gDACA,wDACA,sDAEA,EACA,gCACA,gCACA,YACA,mCACA,6DAEA,2DACA,mDACA,2DACA,yDACA,0CACA,sBACA,kCACA,mBAEA,cAEA,EACA,4BACA,+CACA,wCACA,qDACA,oDACA,CACA,EACA,4BACA,UACA,gCAEA,eACA,EACA,4BACA,gBACA,0BACA,8HAEA,qCACA,uEACA,uEACA,oCACA,mCACA,gDAAuD,oBAAoB,kDAA8D,oDAAgE,iCAAkC,mCAAoC,IAC/Q,mCACA,SACA,eACA,mBACA,qBACA,4CAAqD,qBAA4B,yBACjF,+CACA,iHAEA,oCACA,4CAAyD,sCAAuC,wCAAyC,mCAAoC,wCAAyC,0CAA2C,0CAA2C,uCAAwC,kDAAmD,oBAAoB,iCAAkC,0CAC7b,gGACA,gGACA,sBACA,wBACA,wBACA,iBACA,yBACA,CACA,eACA,EACA,iCACA,YACA,8CACA,gDACA,IACA,eACA,qBACA,mBACA,aACA,0EAEA,oCACA,2DACA,oCACA,6BACA,6CACA,qCACA,qCACA,iDACA,oDAEA,EACA,kCACA,aACA,EACA,yCACA,mDAKA,OAJA,WACA,wBACA,yBAEA,CACA,EACA,CACA,CAAC,GAGD,cACA,cACA,uBACA,uCACA,iDACA,iEACA,2DACA,gBACA,mBACA,iBACA,+DACA,qCACA,2CACA,yCACA,mCACA,+CACA,yCACA,mCACA,yCACA,qEACA,qDACA,2CACA,8DACA,CA6HA,OA5HA,+BACA,4BAEA,kCACA,qBAEA,iCACA,kBACA,EACA,8BACA,iBAEA,oCACA,6BAEA,iCAGA,QAFA,wDACA,oBACA,IAA0C,EAA1C,EAA0C,OAAuB,KAEjE,MAFA,CACA,IACA,aACA,IACA,WAEA,CACA,QACA,EACA,8BACA,8BACA,gBAGA,qBAEA,kCAEA,gCACA,SAEA,eAOA,wBACA,sBACA,MACA,2BACA,6GACA,sBACA,CACA,MAdA,CACA,IALA,EAKA,kBACA,IACA,cACA,uBAEA,CAUA,CAVM,CAUN,WACA,qBACA,0BACA,oCACA,wCACA,6BAEA,uBACA,wBAEM,yBAEN,uBACA,wBAEM,2BAEN,2BAA0C,WAAgB,IAC1D,KACA,kBAGA,eAEA,QACA,EACA,gDACA,uBACA,sBACA,KACA,8BAEA,CACA,QACA,EACA,uCACA,yGACA,EACA,kCAEA,IADA,EACA,sBACA,oCACA,wCACA,8BAIA,sBAJA,YACA,uBAQA,cACA,wCAEA,sHACA,uBACA,EACA,6CACA,yBACA,gCAEA,YAAoB,sBAAyB,IAC7C,uBACA,SAGA,QACA,EACA,CACA,CAAC,GAQD,iBACA,eAEA,WADA,oBACA,GACA,OACA,kBACA,QACA,QACA,CACA,CAKA,wBAEA,cACA,kBACA,YACA,OAEA,YACA,OAEA,iEACA,iEACA,+DACA,+DACA,6DACA,yDACA,yDACA,yDACA,uDACA,uDACA,uDACA,qDACA,qDACA,qDACA,qDACA,qDACA,qDACA,mDACA,mDACA,mDACA,mDACA,mDACA,iDACA,iDACA,iDACA,iDACA,iDACA,iDACA,iDACA,+CACA,+CACA,+CACA,+CACA,+CACA,6CACA,6CACA,6CACA,6CACA,2CACA,2CACA,2CACA,2CACA,yCACA,yCACA,yCACA,uCACA,uCACA,uCACA,qCACA,mCACA,mCACA,iCACA,+BACA,mCACA,uCACA,gBACA,iBACA,kBACA,oBACA,cACA,6BACA,4CACA,6BACA,yBACA,uBACA,oBACA,wBACA,iCAEA,sBACA,0BACA,qCAEA,qBACA,yBACA,mCAEA,0BACA,8BACA,6CAEA,4BA7FA,yFA8FA,sCA7FA,MA8FA,iCACA,iCACA,4BAnGA,IAoGA,gCAnGA,IAoGA,kCAhGA,UAiGA,wDAEA,kCAEA,yBACA,mBACA,sBACA,cACA,CA4eA,GA7esB,IAEtB,qCACA,QACA,uBAMA,+IACA,sCAEA,EACA,8BACA,aACA,EACA,6BACA,mBACA,kBACA,cACA,eACA,UAEA,yIAEA,EACA,gCAEA,2BAAwC,WAAgB,KACxD,UACA,8BACA,wBAEA,CAEA,4BAAyC,WAAgB,IACzD,KACA,QAEA,kBAEA,6BAA0C,WAAgB,KAC1D,WACA,mCACA,CACA,kBACA,oBACA,aACA,EACA,4BAEA,EACA,8BACA,WACA,EACA,gCACA,WACA,EACA,qCACA,OACA,sCACA,WACA,CAAO,CACP,gCACA,WACA,CAAO,CACP,sCACA,WACA,CAAO,CACP,gCACA,WACA,CAAO,CACP,4BACA,WACA,CAAO,CACP,yBACA,QACA,CACA,CACA,EACA,8BAEA,EACA,mCACA,OACA,kBAEA,CACA,CACA,EACA,8BAEA,EACA,2BAEA,EACA,8BAEA,EACA,2BAEA,EACA,iCAEA,EACA,8BAEA,EACA,iCAEA,EACA,mCACA,2BAGA,YAAoB,yBAA4B,IAChD,kBACA,oEACA,6BACA,2BACA,CAAO,CAEP,EACA,uCAGA,QAFA,oBACA,+BACA,IAA0C,WAAuB,KAEjE,MAFA,CACA,IACA,aACA,IACA,WAEA,CARA,IASA,eACA,4BACA,cAEA,EACA,mCACA,sBAEA,oCACA,eACA,EACA,6CACA,4BAEA,8CACA,qBACA,EACA,kCACA,qBAEA,mCACA,cACA,EACA,iCACA,oBAEA,kCACA,aACA,EACA,gCACA,mBAEA,iCACA,YACA,EACA,sCACA,yBAEA,uCACA,kBACA,EACA,wCACA,2BAEA,yCACA,oBACA,EACA,uCACA,wBACA,EACA,wCACA,mBACA,EACA,4CACA,6BACA,EACA,6CACA,wBACA,EACA,yCACA,4BAEA,0CACA,qBACA,EACA,oCACA,uBAEA,qCACA,gBACA,EACA,qCACA,wBAEA,sCACA,iBACA,EACA,qCACA,wBAEA,sCACA,iBACA,EACA,sCACA,yBAEA,uCACA,kBACA,EACA,uCACA,0BAEA,wCACA,mBACA,EACA,kCACA,qBAEA,uCACA,4BAEA,mCACA,oBACA,EACA,wCACA,6BAEA,oCACA,qBACA,GACA,aAEA,EACA,qCACA,eACA,8CACA,UACA,IACA,oBAEA,CAEA,GACA,aAEA,EACA,qCACA,WAEA,kBACA,qDACA,UACA,aACA,YAEA,CAAO,EAEP,aACA,oBACA,EACA,sCACA,SACA,wBACA,+BAEA,YAAsB,sBAAyB,IAC/C,wBACA,IACA,KACA,QAGA,SAIA,eACA,yBACA,CADmC,EAEnC,EACA,uCACA,4BAIA,MAHA,OACA,eAEA,CACA,EACA,wCAEA,QADA,KACA,IAA0C,EAA1C,EAA0C,OAAuB,KACjE,WACA,0BACA,CAIA,MAHA,OACA,eAEA,CACA,EACA,oCACA,uBACA,iBAEA,+BACA,2BACA,kBACA,uBACA,cAGA,sBACA,YAAoD,EAApD,EAAoD,OAA2B,IAA/E,CACA,IACA,QAEA,CAAK,GACL,EACA,0CACA,2BAEA,yBAEA,sEACA,YACA,mBACA,oBAEA,6BAEA,uEAMA,GALA,WACA,mBACA,oBAGA,UAEA,+BACA,WACA,WAEA,CACA,aAEA,+BACA,WACA,WAEA,CACA,QACA,EACA,8BAEA,sBACA,EACA,sCAEA,4BAAyC,WAAgB,IAEzD,CADA,IACA,QAEA,kBAEA,2BAAwC,WAAgB,KACxD,WACA,aACA,GACA,cAEA,CACA,EACA,gDAEA,oCACA,gCACA,sHACA,iDAJA,IAKA,EALkB,EAMlB,yCACA,4BACA,KACA,aAGA,EACA,4CAIA,QAHA,EACA,MACA,GAD0B,IAE1B,oBAAyC,WAAgB,KAGzD,MADA,GADA,MAEA,YACA,kBACA,SACA,sCACA,MACA,IACA,IAEA,CACA,CACA,gCACA,gBAGA,CADA,gBACA,aACA,sBAEA,EACA,uCACA,WACA,eAIA,QAQA,uDACA,6BACA,yCAGA,6BAaA,QAVA,gBACA,iDAMA,GALA,iCAKA,oPACA,4BACA,iDACA,IAAyB,IAAW,KACpC,qBACA,qGACA,2BAEA,CACA,yBACA,iDACA,mBACA,CAAO,QACD,CACN,yBAQA,qDACA,4BAA2C,WAAgB,IAC3D,KACA,YAEA,EACA,EACA,iCACA,mBACA,yBAKA,eAHA,EAGA,aAHA,EAGA,CAEA,YACA,CAAK,aACL,EACA,CACA,CAAC,GAED,iBAA2B,qBAAwB,iCAAoC,sCAAyC,2BAAkC,wDAA0D,oBAA0B,SAEtP,QACA,gBACA,oCACA,gCACA,sBACA,uBACA,EACA,IACA,mBACA,qBACA,CAAG,CACH,iBACA,mBACA,CAAG,CACH,gBACA,kBACA,CAAG,CACH,kBACA,oBACA,CAAG,CACH,uBACA,yBACA,CAAG,CACH,cACA,gBACA,CAAG,CACH,kBACA,oBACA,CAAG,CACH,oBACA,sBACA,CAAG,CACH,eACA,iBACA,CAAG,CACH,gBACA,kBACA,CAAG,CACH,aACA,eACA,CAAG,CACH,wBACA,0BACA,CAAG,CACH,YACA,cACA,CAAG,CACH,WACA,aACA,CAAG,CACH,iBACA,mBACA,CACA,EACA,MAiPuB,UAAI,CAhP3B,YACA,IACA,WACA,UACA,gBACA,cACA,aACA,eACA,oBACA,WACA,eACA,iBACA,YACA,aACA,UACA,qBACA,SACA,QACA,cACA,UACA,oBACA,kBACA,cACA,aACA,SACA,YACA,CAAI,EACJ,MAAgC,cAAQ,OACxC,EAAY,gBAAU,IACtB,MAA0C,cAAQ,OAClD,MAA8D,cAAQ,OACtE,MAA0D,cAAQ,OAClE,MAAgD,cAAQ,OACxD,MAAkD,cAAQ,OA6M1D,MA5ME,eAAS,MACX,OACA,UACA,oCAEA,oDAEA,CAAG,MACD,eAAS,MACX,OACA,UACA,oCAEA,qDAEA,CAAG,MACD,eAAS,MACX,OACA,UACA,oCAEA,iDAEA,CAAG,MACD,eAAS,MACX,OACA,UACA,oCAEA,2DAEA,CAAG,MACD,eAAS,MACX,OACA,UACA,oCAEA,yDAEA,CAAG,MACD,eAAS,MACX,sBACA,qBAEA,CAAG,QACD,eAAS,MACX,sBACA,mBAEA,CAAG,QACD,eAAS,MACX,sBACA,kBAEA,CAAG,QACD,eAAS,MACX,sBACA,oBAEA,CAAG,QACD,eAAS,MACX,sBACA,yBAEA,CAAG,QACD,eAAS,MACX,sBACA,gBAEA,CAAG,QACD,eAAS,MACX,sBACA,oBAEA,CAAG,QACD,eAAS,MACX,sBACA,sBAEA,CAAG,QACD,eAAS,MACX,sBACA,iBAEA,CAAG,QACD,eAAS,MACX,sBACA,kBAEA,CAAG,QACD,eAAS,MACX,sBACA,eAEA,CAAG,QACD,eAAS,MACX,sBACA,0BAEA,CAAG,QACD,eAAS,MACX,sBACA,cAEA,CAAG,QACD,eAAS,MACX,sBACA,aAEA,CAAG,QACD,eAAS,MACX,sBACA,mBAEA,CAAG,QACD,eAAS,MACX,MAEA,kBAhNA,YAA8B,YAAgB,mBAAsB,KAAO,yCAAkD,yCAAwD,YAA8B,oIAAqJ,gEAAqE,EAAK,UA+Mlb,EAA6C,SAkE7C,OAhEA,GACA,sBAEA,GACA,oBAEA,GACA,mBAEA,GACA,qBAEA,GACA,0BAEA,GACA,iBAEA,GACA,qBAEA,GACA,uBAEA,GACA,kBAEA,GACA,mBAEA,GACA,gBAEA,GACA,2BAEA,GACA,eAEA,GACA,cAEA,GACA,oBAEA,GACA,oDAEA,GACA,qDAEA,GACA,iDAEA,GACA,2DAEA,GACA,yDAEA,KACA,GACA,KAEA,KACA,UACA,oCAEA,UACA,oCAEA,UACA,oCAEA,UACA,oCAEA,UACA,oCAEA,GACA,IAEA,EACA,CAAG,KACH,oBACA,EAEA,kBAAiC,eAAa,CAC9C,cACA,oBACA,8BACA,gBACA,oBACA,CAAK,EACL,mCACA,sDACA,6CAEA,CAAK,CACL,CACA,oBACA,iBACA,gDACA,0BACA,cACA,YACA,YAAqB,CACrB,qBACA,UACA,CAAO,EACP,kBACA,EACA,iBACA,GACO,0BACP,CACA,CACA,sBACA,6BACA,yBACA,yBACA,cACA,YACA,YACA,qBACA,oCACO,EAEP,CACA,uBACA,oCACA,sBACA,iDAEA,yBAGA,wCAEA,CACA,SACA,6FACA,CACA,CAIA,eACA,kBACA,mBACA,mBAEA,CARA,sBASA,kBACA,cACA,YACA,OAEA,+DACA,yDACA,uDACA,qDACA,mDACA,+CACA,6CACA,6CACA,6CACA,2CACA,2CACA,2CACA,2CACA,2CACA,yCACA,yCACA,uCACA,mCACA,mCACA,iCACA,+BACA,+BACA,+BACA,+BACA,uCAEA,2BACA,yCACA,4BACA,0DACA,sDACA,2BAEA,oCACA,6BACA,4CACA,sFACA,oBACA,sBAEA,oEACA,qBACA,oBACA,aAEA,uBAGA,yBACA,mCACA,8BACA,yDACA,cACA,wBACA,uBACA,sBACA,0BACA,yBACA,uBACA,CA8dA,OA7dA,wCACA,WAYA,cACA,uCACA,mBACA,8BACA,uDAEA,yCACA,oCAEA,sBAKA,GAJA,UACA,mCAEA,CAFgD,GAEhD,mBACA,qBACA,2BAEA,yDACA,wCACA,0BACU,CAEV,yBACA,+DACA,qBACA,CAGA,GADA,iCACA,8BACA,uBAIA,cADA,sGAC4C,WAAsB,KAClE,WACA,sEACA,CAGA,uFACA,OACA,8BAEA,CAAS,EACT,CACA,0EAtDA,YACA,iBACA,kBACA,mBAEA,0BACA,KAEA,GAoDA,0CACA,CACA,EACA,sCACA,SAYA,MAXA,uBASA,2CANA,8EAKA,qCAAmD,EACnD,EAEA,CACA,EACA,uCACA,mKACA,EACA,0CAEA,kBACA,mBACA,oBAOA,6CACA,YACA,EACA,4CACA,+BAEA,+BACA,iBAGA,oBAEA,iCACA,QACA,IACA,eACA,gCAGA,2BAEA,iBAGA,gBAGA,iBACA,yBACA,0BACA,uBACA,wBACA,8BACA,+BAIA,EADA,qBACA,yCACA,YACA,SACA,UACY,aACZ,gBAEA,iBACA,WACA,YACc,WACd,cAGA,SACA,UACc,aACd,iBAIA,gBAEA,YAEA,CACA,CACA,EACA,mCACA,aAEA,iCAEA,0BAEA,oBACA,eACA,2CAGA,yBAOA,GAFA,+CAEA,8DAEA,4CAGA,wFACA,gDACA,CAEA,mCACA,mCACA,oBACA,uCAEA,yBACA,gCAEA,CACA,EACA,oCACA,OACA,MACA,SACA,OACA,OACA,EACA,aACA,SAEA,yBACA,6BACA,oEACA,IAEA,2CACA,iDACA,6CACA,+CAEA,EAAM,OAGN,sCACA,CAGA,4BACA,IAEA,2CACA,iDACA,6CACA,+CAEA,CACA,QACA,EACA,gCACA,gCACA,0CACA,cAEA,EACA,4BAEA,GADA,wBACA,UAIA,MADA,qBACA,mCACA,YACA,oDACA,iBACA,0DAEA,qDAGA,cACA,mCAEA,mCAEA,CACA,EACA,mCACA,YACA,OAEA,sBAEA,yBACA,oBAEA,sBAEA,yBACA,oBAEA,oBACA,2BAEA,2BACA,uCAEA,qBACA,2BAEA,wBACA,iCAEA,wBACA,iCAEA,qBACA,6BAEA,mBACA,yBAEA,2BACA,uCAEA,wBACA,iCAEA,6BACA,2CAEA,qBACA,2BAEA,oBACA,2BAEA,mCACA,uDAEA,UACA,WAEA,EACA,mCACA,eACA,WACA,qBACA,qDACA,yBAGA,oBACA,0BAEA,mBACA,4CAEA,yCACA,yBAIA,qBACA,+CACA,mBACA,4CAEA,yCACA,0BAGA,wBAOA,iDACA,EACA,oCACA,gBACA,UACA,YAOA,kDACA,EACA,mCACA,iBACA,UACA,4DAEA,EACA,kCACA,cACA,UACA,6BAOA,gDACA,EACA,kCACA,qBAEA,mCACA,sBAEA,iCACA,oBAEA,kCAEA,aADA,eACA,gBAEA,4BACA,iBACA,UACA,qCAEA,EACA,4BACA,iBACA,UACA,oCAEA,EACA,+BACA,WACA,IAGA,8BACA,gFAGA,sBACA,gBACA,CAAO,EACP,0EAGA,eACA,CAAO,GAEP,eACA,UACA,aAEA,EACA,6BAKA,GAJA,qBACA,qDACA,yBAEA,qBACA,kCAAiD,WAAgB,KACjE,WACA,mCACA,CACA,wBACA,CACA,oBACA,oDACA,wBAEA,mBACA,mDACA,uBAEA,uBACA,uDACA,2BAIA,iBACA,EACA,iCACA,mBACA,yBACA,8CAGA,mCAGA,YACA,CAAK,aACL,EACA,CACA,CAAC,GAED,gBACA,gBACA,iBAA2B,qBAAwB,iCAAoC,sCAAyC,2BAAkC,wDAA0D,oBAA0B,SACtP,eAA8B,YAAgB,mBAAsB,KAAO,yCAAkD,yCAAwD,YAA8B,oIAAqJ,gEAAqE,EAAK,SAClb,QACA,0BACA,mCACA,sBACA,qCACA,gCACA,EACA,IACA,aACA,eACA,CAAG,CACH,cACA,gCACA,iBAEA,kDAEA,CAAG,CACH,aACA,eACA,CAAG,CACH,YACA,cACA,CACA,EACA,MAqKe,UAAI,CApKnB,YACA,IACA,WACA,SACA,UACA,WACA,SACA,eACA,aACA,mBACA,oBACA,kBACA,SACA,YACA,CAAI,EACJ,EAAY,gBAAU,IACtB,MAAgC,cAAQ,OACxC,MAAoD,cAAQ,OAC5D,MAA0D,cAAQ,OAClE,MAAsE,cAAQ,OAC9E,MAAwE,cAAQ,OAChF,MAAoE,cAAQ,OAC5E,EAA4B,YAAM,OA4IlC,MA1IE,eAAS,MACX,cACA,UACA,EACA,YACQ,iBACR,UAGA,CAAG,UACD,eAAS,MACX,aACA,eAEA,CAAG,QACD,eAAS,MACX,gBACA,wCAGA,oCACA,gBACA,CACA,CAAG,MACD,eAAS,MACX,8BACA,cAEA,CAAG,MACD,eAAS,MACX,OACA,UACA,oCAEA,mDAEA,CAAG,MACD,eAAS,MACX,OACA,UACA,oCAEA,iDAEA,CAAG,MACD,eAAS,MACX,OACA,UACA,oCAEA,wDAEA,CAAG,MACD,eAAS,MACX,OACA,UACA,oCAEA,yDAEA,CAAG,MACD,eAAS,MACX,OACA,UACA,oCAEA,uDAEA,CAAG,MACD,eAAS,MACX,MACA,IAKA,EALA,QACA,CACA,WACA,CAAU,EACV,SAEA,sCAGA,wCAEA,qBAAkE,OAClE,UACA,EAAQ,EAAI,EACZ,yCACA,KACA,GACA,mDAEA,GACA,iDAEA,GACA,wDAEA,GACA,yDAEA,GACA,uDAEA,wBACA,EACA,YACQ,gBACR,UAEA,4EAEA,GACA,IAEA,CACA,WACA,WACA,GACA,oCAEA,GACA,oCAEA,GACA,oCAEA,GACA,oCAEA,GACA,oCAEA,GACA,KAEA,UAEA,CACA,CAAG,KACH,UAAuC,kBAAY,CAAC,UAAQ,wBAC5D,EAEA,kBAA+B,eAAa,CAC5C,cACA,oBACA,8BACA,gCACA,gBACA,YACA,CAAK,EACL,sBACA,EACA,qBACA,uBAEQ,gBACR,qBACA,qBAGA,2EAEA,CAAK,EACL,iCACA,0DACA,qDACA,gDACA,mBACA,sCAGA,CAAK,CACL,CACA,oBACA,IAKA,EALA,wBAAwC,CACxC,CACA,WACA,CAAQ,EACR,SAEA,sCAGA,wCAEA,qBAAgE,OAChE,UACA,EAAM,EAAI,EACV,qDACA,yBACA,cACA,YACA,YAAmB,CACnB,qBACA,UACA,CAAK,EACL,eACA,SACA,CAAK,yBACL,CACA,sBACA,IACA,UACA,CAAM,kBACN,IACA,yBACA,yBACA,cACA,YACA,YACA,qBACA,UACA,CAAO,EAEP,CACA,uBACA,IACA,YACA,CAAM,WACN,CACA,UACA,CAAM,kBACN,IACA,GACA,KAEA,yBACA,UAEA,CACA,SACA,6BAAmC,kBAAY,CAAC,UAAQ,sDACxD,CACA,CACA,sBA0CA,SArCA,KACA,IAIA,kBACA,kBACA,iDACA,0CAEA,qBAEA,GADA,cACA,kBACA,QAAyB,EAAU,CAAV,KAAU,0BACnC,QACA,CACA,wEACA,yEACA,6EAGA,GADA,IADA,kBACA,UACA,+BACA,QAAuB,EAAU,CAAV,KAAU,0DACjC,QAAuB,EAAU,CAAV,KAAU,CACjC,IAfA,MAeA,OACA,yBACA,CACA,QACA,CAGA,iBACA,IAOA,0HAMA,GAKA,eACA,+BACA,wDAEA,+BACA,WACA,8DAEA,WACA,MAhBiB,CAgBjB,EACA,6DAjBA,EAiBA,MAEA,eACA,MACA,wCAEA,8BACA,2BACA,sBACA,CASA,eACA,gEACA,sEACA,yCACA,0EACA,kBACA,6CACA,iBACA,oDACA,iCACA,uCACA,0CACA,YACA,OACA,2DAEA,8BAEA,YACA,2CArDA,EAqDA,GACA,oDACA,cACA,oBAGA,mCACA,2CA5DA,EA4DA,GACA,oDACA,YACA,kBAGA,8CACA,oCACA,oCAEA,CAQA,SACA,mBAIA,OAHA,cACA,2BACA,2BACA,CACA,CAKA,SACA,mBACA,qBACA,kFAKA,OAFA,SA8HA,eACA,cACA,aAIA,CAJ6B,SAqB7B,eACA,WACA,YACA,YACA,QACA,cACA,qBACA,2CACA,oCACA,wCACA,cACA,CACA,eACA,IACA,IAGA,IAFA,YACA,wBACA,MAIA,IAHA,YACA,IACA,IACA,gBACA,oBACA,CACA,yBAAsE,GAEtE,WAEA,cACA,aACA,EACA,EAhDA,aAGA,mBACA,mBACA,EAzIA,wDACA,kBACA,KAWA,eACA,8EAUA,IATA,IACA,MACA,SACA,WACA,CAAM,KACN,mBACA,KAGA,WACA,iBACA,aACA,aAGA,WACA,YAA2B,KAAY,KACvC,aACA,WACA,oCACA,CACA,QACA,CAGA,aAGA,SACA,WACA,qCAGA,oBACA,UACA,YACA,aAEA,oBACA,YACA,UACA,YAEA,CACA,QACA,CASA,cACA,8EAWA,IAVA,IACA,MACA,SACA,WACA,CAAM,KACN,mBACA,KACA,MAGA,WACA,iBACA,aACA,aAGA,WACA,YAA2B,KAAY,IACvC,yCAEA,QACA,CAGA,aAGA,SACA,WACA,6BAGA,wBACA,UACA,YACA,aAEA,wBACA,YACA,UACA,YAEA,CACA,QACA,CACA,CAwEA,qBACA,UACA,cACA,iBACA,CAOA,mBACA,WACA,UACA,MACA,CAQA,qBACA,UACA,MACA,cACA,CAEA,QACA,UAEA,WAEA,YAEA,UAEA,WAEA,YAEA,OAIA,cAEA,YAIA,QACA,EACA,wBACA,QACA,KACA,CAAC,qBAMD,UACA,eACA,gDACA,yCACA,oCACA,qBAEA,QACA,IACA,MACA,UACA,UACA,CAAM,aACN,8BACA,2CACA,oBACA,cAIA,QADA,KACA,IAAoB,WAAmB,KACvC,WACA,eACA,gCACA,YACA,YAEA,WAEA,IAEA,EAEA,GAEA,GAEA,+BACA,CAD6C,IAE7C,qCACA,uBAIA,YAA0B,KAAc,KACxC,kBAGA,qDACA,oEACA,CAEA,OADA,iCACA,KAEA,iBACA,mCACA,kCACA,8CACA,kCACA,kBACA,OACA,WACM,QACN,sCACA,mCACA,kBACA,CACA,qCACA,mCACA,SACA,KACA,gBACA,oBACA,WA3EA,EA2EA,6CA7EA,EA6EA,EACA,CACA,QACA,CACA,eACA,2BACA,yBACA,sCACA,gBACA,qBACA,aACA,0CACA,gEACA,mBACA,qBACA,kBACA,KACA,gBACA,oBACA,IA/FA,EA+FA,MACA,WA/FA,EA+FA,6CAjGA,EAiGA,EAEA,CACA,+BACA,QACA,CACA,iBACA,QACA,OACA,SAEA,OADA,8BACA,CACA,CACA,eACA,qCACA,gBACA,CACA,SACA,SACA,CAAM,aACN,MACA,UACA,YACA,GACA,aASA,OAPA,qEACA,OACA,2DAEA,SACA,0DAEA,wBACA,CACA,2BAEA,IADA,+BACA,0BACA,0BAEA,GADA,IACA,mBACA,6BAEA,QACA,CACA,yBAEA,aADA,oBACA,CACA,mBAiBA,GAhBA,aACA,mBAEA,iBAGA,2CAGQ,IAER,IAGA,UAEA,kBACA,CACA,QACA,CACA,eAEA,QADA,oEACA,IAAoB,WAAiB,kCAGrC,OAFA,WACA,SACA,CACA,CACA,8BACA,gBACA,oBACA,MA9KA,EA8KA,GACA,SACA,SACA,SACA,KACA,4BACA,OACA,aACQ,CACR,sBAzLA,EAyLA,EACA,eACA,gCACA,QACA,OACA,CACA,OACA,OACA,6FACA,MACA,EAGA,QAQA,WALA,EAFA,2BAEA,IAzMA,EAyMA,CAGA,gBA5MA,EA4MA,OAEA,SACA,kBACA,CACA,CACA,cACA,qFACA,CACA,cAaA,OAZA,CACA,SACA,SACA,SACA,YACA,CAAM,aACN,sBACA,SACA,KACA,cAGA,IAAoB,WAAiB,KAErC,SArOA,EAqOA,MACA,IAtOA,EAsOA,GAGA,WACA,SACA,0BACA,MAzOA,EAyOA,CACA,IAGA,gBACA,SAEA,KAnPA,EAmPA,YAhPA,EAgPA,CACA,CAGA,cACA,UACA,MACA,SACA,KAGA,wCACA,gBACA,UACA,SAjQA,EAiQA,MACA,IAlQA,EAkQA,GAEA,IAFyC,EAEzC,IAjQA,EAiQA,CACA,UACA,UADuC,EAEvC,IArQA,EAqQA,GACA,IACA,IACA,oBACA,2BACA,2BAEA,qBAEA,CACA,IA/QA,EA+QA,GACA,2BACA,YACA,EAAQ,IAER,YAAwB,IAAY,mBACpC,OACA,gBACA,UACA,SA1RA,EA0RA,MACA,IA3RA,EA2RA,GACA,YAA6B,IAAa,mBAC1C,CAEA,EAEA,QACA,CAGA,gBACA,8BACA,CAGA,kBACA,+BACA,CACA,YACA,OA3SA,EA2SA,IACA,4BA3SA,EA2SA,EACA,yBAAqC,KACrC,CACA,sBAjTA,EAiTA,aACA,sBACA,gCAA0D,KAC1D,CACA,CACA,mBACA,OACA,eACA,OAzTA,EAyTA,CACA,qBACA,UACA,aACA,aA6BA,CA7BA,KA6BA,QAIA,uBADA,SAhCA,QAgCA,cACA,WAjCA,CAEA,CACA,CACA,mBACA,UAhUA,EAgUA,CACA,uFACA,MAjUA,EAiUA,CAEA,qBADA,UAAyC,gBAAkB,OAC3D,CACA,WACA,eAxUA,EAwUA,CACA,cACA,yBACA,CAAG,CACH,CAGA,eACA,eACA,CACA,eACA,8BACA,uCACA,oBACA,CA0BA,iBACA,SACA,sFACA,0GAA4H,WAAc,IAC1I,yFAEA,QACA,CAqBA,SACA,oCACA,iFACA,CACA,2BACA,iFAEA,mBACA,yBACA,QAEA,WAEA,CACA,sBAEA,6BACA,eACA,4CACA,kBAGA,kCACA,4DAEA,CACA,mCACA,CACA,sBACA,CACA,2BACA,4BAUA,cACA,CACA,CAiBA,SACA,eACA,IACA,UACA,WACA,CAAM,CACN,gBACA,IACA,gCACA,iBAEA,yCAGA,CACA,aACA,4CAGA,kEACA,0BACA,4BAEA,SACA,CACA,eACA,8CACA,CAIA,YACA,uDAKA,QACA,oBACA,CAIA,SACA,cACA,4BACA,oBAEA,qBACA,CACA,CA0BA,mBACA,4BACA,iDACA,EAIA,aACA,IACA,YACA,YACA,CAAI,QAKJ,UAJA,IACA,YACA,WACA,CAAG,IACH,EACA,EAIA,aACA,gBACA,mBACA,mBACA,yCAOA,WAEA,gCACA,4BACA,gBACA,gBACA,kEAEA,OADA,0CANA,IAQA,EAMA,UACA,EACA,mDACA,kDACA,GAOA,WACA,IACA,YACA,YACA,CAAI,EAKJ,OAJA,OACA,OACA,OACA,OACA,CACA,YACA,WACA,CACA,EAIA,WACA,IACA,YACA,YACA,CAAI,EACJ,4BACA,4BACA,wCACA,CAoBA,UACA,eACA,IACA,aACA,CAAM,CACN,eACA,CAaA,QACA,IACA,UACA,CAAM,EACN,YACA,CACA,CAOA,oBACA,eACA,IACA,qBACA,CAAQ,EAER,MADA,2BAEA,wBACA,sBACA,CACA,aACA,IACA,UACA,MACA,sBACA,CAAM,SACN,0BACA,CACA,oBACA,SACA,CAAS,EACT,UACA,EAEA,CACA,uBACA,uCACA,MACA,qBACA,CAAO,CACP,CACA,CACA,CAIA,UACA,iBACA,2BACA,YACG,EAwKH,qBACA,eACA,IACA,UACA,YACA,CAAQ,EACR,6BACA,OACA,SACA,CAAK,EACL,YACA,OACA,EACA,wCACA,qBACA,QACA,CAAK,IACL,CACA,aACA,SACA,GACA,oBACA,EACA,gCACA,KAEA,4BACA,2BACA,wBAEA,OACA,eACA,UACA,aACA,YALA,kBAMW,CACX,YACA,QACA,CACA,CACA,CAAO,EACP,yBACA,CAUA,MATA,IACA,uDACA,sBAGA,aACA,GACA,gCAEA,CACA,uBACA,SACA,CACA,CACA,WACA,IACA,MACA,CAAM,EACN,gHACA,CACA,oBACA,IACA,UACA,kBACO,CACP,aACA,CAAM,EACN,aACA,eACA,kFACA,UACA,MACA,KACA,CACA,CAAO,EAEP,eACA,eACA,YACA,0BACA,CAAK,CACL,CACA,CAoIA,SACA,iBACA,cACA,cAEA,wBACA,wBACA,gBACA,eACA,SACA,gBACA,MACA,mBACA,kBACA,CACA,CACA,CACA,CACA,SAuCA,cACA,IACA,QACA,WACA,CAAM,EAEN,6DAEA,ibACA,qCAEA,0CACA,oCAGA,MADA,gBACA,0DACA,8CAQA,6CAPA,CACA,MACA,WACA,SACA,QACA,SACA,EAEA,CACA,OACA,WACA,SACA,QACA,MACA,wBAAiC,yBACjC,mCACA,CACA,EACA,gCACA,CACA,CAmCA,SACA,eAMA,SAjBA,KAGA,yBACA,+BAaA,2BACA,CACA,EAkBA,YACA,qCACA,iCACA,uBACA,CAAC,UAAsD,EACvD,iBACA,qBACA,CAMA,qBACA,eACA,IACA,MACA,aACA,qBAA2B,CAC3B,sBACA,kBACA,oBACA,CAAM,EACN,QACA,oBACA,iBACA,iBACA,gBACA,sBACA,GACA,cAEA,CACA,gBACA,2BAGA,qBACA,GACA,cAEA,CACA,gBACA,cACA,oBACA,CAAK,EACL,GACA,aAEA,CACA,kBACA,qCACA,SAIA,kBACA,yBACA,CADmC,EAEnC,cAEA,GACA,CACA,mBACA,SAOA,OANA,cACA,4BACA,CAAK,EACL,OACA,cAEA,CACA,CACA,gBACA,sBACA,GACA,aAEA,CAIA,SACA,oBACA,oDACA,wDACA,IACA,WACA,UACA,CAAQ,0BACR,qBACA,MACA,wCACA,CAAO,EAEP,iBAIA,cACA,eACA,qBACA,oBAGA,SAEA,2BACA,iBAGA,oBACA,iBAIA,yBAIA,iBAGA,iBACA,sBAEA,0DACA,CACA,qDACA,CACA,CACA,QACA,2EACA,aACA,CACA,WACA,oDACA,YACA,CACA,QACA,2CACA,qCACA,gBACA,CACA,iBAEA,yCACA,gBACA,0BACA,qBACA,uBAGA,qCAEA,wCACA,qBACA,6BACA,IACA,kDACA,0BACA,CAAW,GAGX,qBACA,CAAK,CACL,CACA,CA0BA,iBAA2B,qBAAwB,iCAAoC,sCAAyC,2BAAkC,wDAA0D,oBAA0B,SACtP,eAA8B,YAAgB,mBAAsB,KAAO,yCAAkD,yCAAwD,YAA8B,oIAAqJ,gEAAqE,EAAK,SA0BpZ,UAAI,CARlC,YACA,IACA,WACA,UACA,CAAI,EACJ,EAtBA,YACA,IA95MA,EA85MA,GA/5MA,IAAc,YAAU,gEAExB,KADA,EAAY,gBAAU,KACtB,2DACA,GA65MA,MAA8C,cAAQ,OAStD,MARE,eAAS,MACX,aAIA,EAHA,eAAgF,MAAc,EAC9F,KACA,CAAO,GAGP,CAAG,MACH,CACA,EAUA,GACA,yBACA,GAGA,QACA,0BACA,mCACA,sBACA,qCACA,gCACA,EACA,IACA,aACA,eACA,CAAG,CACH,cACA,gBACA,CAAG,CACH,YACA,cACA,CACA,EAsJkB,UAAI,CArJtB,YACA,IACA,WACA,SACA,UACA,WACA,SACA,eACA,aACA,mBACA,oBACA,kBACA,SACA,YACA,CAAI,EACJ,EAAY,gBAAU,IACtB,MAAgC,cAAQ,OACxC,MAAoD,cAAQ,OAC5D,MAA0D,cAAQ,OAClE,MAAsE,cAAQ,OAC9E,MAAwE,cAAQ,OAChF,MAAoE,cAAQ,OAC5E,EAA4B,YAAM,OA6HlC,MA3HE,eAAS,MACX,WACA,UACA,EACA,YACQ,iBACR,UAGA,CAAG,UACD,eAAS,MACX,aACA,eAEA,CAAG,QACD,eAAS,MACX,aACA,gBAEA,CAAG,MACD,eAAS,MACX,8BACA,cAEA,CAAG,MACD,eAAS,MACX,OACA,UACA,oCAEA,mDAEA,CAAG,MACD,eAAS,MACX,OACA,UACA,oCAEA,iDAEA,CAAG,MACD,eAAS,MACX,OACA,UACA,oCAEA,wDAEA,CAAG,MACD,eAAS,MACX,OACA,UACA,oCAEA,yDAEA,CAAG,MACD,eAAS,MACX,OACA,UACA,oCAEA,uDAEA,CAAG,MACD,eAAS,MACX,oCAmCA,OAlCA,KACA,wCACA,GACA,mDAEA,GACA,iDAEA,GACA,wDAEA,GACA,yDAEA,GACA,uDAEA,wBACA,GACA,iBAEA,GACA,eAEA,EACA,YACM,gBACN,UAEA,wHAEA,GACA,KAEA,KACA,GACA,oCAEA,GACA,oCAEA,GACA,oCAEA,GACA,oCAEA,GACA,oCAEA,GACA,KAEA,SACA,CACA,CAAG,KACH,UAAuC,kBAAY,CAAC,UAAQ,wBAC5D,EAEA,kBAAyB,eAAa,CACtC,cACA,oBACA,8BACA,gCACA,gBACA,eACA,CAAK,EACL,sBACA,EACA,uBACQ,gBACR,qBAEA,uHAEA,CAAK,EACL,oCACA,6DACA,wDACA,mDACA,mBACA,yCAGA,CAAK,CACL,CACA,oBACA,oDACA,qDACA,yBACA,cACA,YACA,YAAmB,CACnB,qBACA,UACA,CAAK,EACL,kBACA,EACA,YACA,GACK,2BACL,CACA,sBACA,+BACA,yBACA,yBACA,cACA,YACA,YACA,qBACA,+BACO,EAEP,CACA,uBACA,+BACA,yBACA,sBACA,4CAEA,8BAEA,CACA,SACA,6BAAmC,kBAAY,CAAC,UAAQ,sDACxD,CACA,CAGA,iBAA2B,qBAAwB,iCAAoC,sCAAyC,2BAAkC,wDAA0D,oBAA0B,SACtP,eAA8B,YAAgB,mBAAsB,KAAO,yCAAkD,yCAAwD,YAA8B,oIAAqJ,gEAAqE,EAAK,SAHlb,sBAIA,QACA,gBACA,sBACA,cACA,oBACA,wBACA,wBACA,wBACA,sBACA,wBACA,oBACA,yBACA,EACA,IACA,eACA,iBACA,CAAG,CACH,cACA,gBACA,CAAG,CACH,SACA,WACA,CAAG,CACH,aACA,eACA,CAAG,CACH,UACA,YACA,CAAG,CACH,aACA,eACA,CACA,EACA,MAsPgB,UAAI,CArPpB,YACA,IACA,UACA,YACA,WACA,UACA,OACA,aACA,YACA,cACA,cACA,cACA,aACA,cACA,YACA,eACA,UACA,SACA,SACA,YACA,CAAI,EACJ,EAAY,gBAAU,IACtB,MAAgC,cAAQ,OACxC,MAAgD,cAAQ,OACxD,MAA8C,cAAQ,OACtD,MAAkD,cAAQ,OAC1D,MAAkD,cAAQ,OAC1D,MAAkD,cAAQ,OAC1D,MAAgD,cAAQ,OACxD,MAAkD,cAAQ,OAC1D,MAA8C,cAAQ,OACtD,MAAoD,cAAQ,OAC5D,MAA0C,cAAQ,OAClD,MAAwC,cAAQ,OAkNhD,MAhNE,eAAS,MACX,UACA,WAEA,CAAG,MACD,eAAS,MACX,sBACA,eAEA,CAAG,QACD,eAAS,MACX,sBACA,iBAEA,CAAG,QACD,eAAS,MACX,sBACA,gBAEA,CAAG,QACD,eAAS,MACX,sBACA,eAEA,CAAG,QACD,eAAS,MACX,sBACA,YAEA,CAAG,QACD,eAAS,MACX,OACA,UACA,oCAEA,iDAEA,CAAG,MACD,eAAS,MACX,OACA,UACA,oCAEA,gDAEA,CAAG,MACD,eAAS,MACX,OACA,UACA,oCAEA,kDAEA,CAAG,MACD,eAAS,MACX,OACA,UACA,oCAEA,kDAEA,CAAG,MACD,eAAS,MACX,OACA,UACA,oCAEA,kDAEA,CAAG,MACD,eAAS,MACX,OACA,UACA,oCAEA,iDAEA,CAAG,MACD,eAAS,MACX,OACA,UACA,oCAEA,kDAEA,CAAG,MACD,eAAS,MACX,OACA,UACA,oCAEA,gDAEA,CAAG,MACD,eAAS,MACX,OACA,UACA,oCAEA,mDAEA,CAAG,MACD,eAAS,MACX,OACA,UACA,oCAEA,8CAEA,CAAG,MACD,eAAS,MACX,OACA,UACA,oCAEA,6CAEA,CAAG,MACD,eAAS,MACX,uCAA8E,UAAkC,EAChH,KACA,CAAK,GAkDL,OAjDA,GACA,aAEA,YACA,gBAEA,YACA,iBAEA,YACA,kBAEA,GACA,iDAEA,GACA,gDAEA,GACA,kDAEA,GACA,kDAEA,GACA,kDAEA,GACA,iDAEA,GACA,kDAEA,GACA,gDAEA,GACA,mDAEA,GACA,8CAEA,GACA,6CAEA,KACA,GACA,KAEA,KACA,UACA,oCAEA,UACA,oCAEA,UACA,oCAEA,UACA,oCAEA,UACA,oCAEA,UACA,oCAEA,UACA,oCAEA,UACA,oCAEA,UACA,oCAEA,UACA,oCAEA,GACA,KAEA,cACA,CACA,CAAG,KACH,IACA,EAEA,kBAAuB,eAAa,CACpC,cACA,oBACA,8BACA,gBACA,aACA,CAAK,EACL,kCACA,+CACA,sCAEA,CAAK,CACL,CACA,oBACA,uCAA8E,uBAAyB,EACvG,iBACK,EACL,0BACA,cACA,YACA,YAAmB,CACnB,qBACA,UACA,CAAK,EACL,yBACA,OACA,UACA,CACA,CAAK,0BACL,CACA,sBACA,6BACA,yBACA,yBACA,cACA,YACA,YACA,qBACA,6BACO,EAEP,CACA,uBACA,6BAGA,sBACA,0CAEA,yBACA,iCACA,CACA,SACA,WACA,CACA,CAGA,iBAA2B,qBAAwB,iCAAoC,sCAAyC,2BAAkC,wDAA0D,oBAA0B,SACtP,eAA8B,YAAgB,mBAAsB,KAAO,yCAAkD,yCAAwD,YAA8B,oIAAqJ,gEAAqE,EAAK,SAHlb,sBAIA,QACA,gBACA,sBACA,cACA,oBACA,wBACA,wBACA,wBACA,sBACA,wBACA,oBACA,yBACA,EACA,IACA,eACA,iBACA,CAAG,CACH,cACA,gBACA,CAAG,CACH,SACA,WACA,CAAG,CACH,aACA,eACA,CAAG,CACH,UACA,YACA,CAAG,CACH,WACA,aACA,CAAG,CACH,aACA,eACA,CACA,EA8Qe,UAAI,CA7QnB,YACA,IACA,UACA,YACA,WACA,UACA,OACA,QACA,aACA,YACA,cACA,cACA,cACA,aACA,cACA,YACA,eACA,UACA,SACA,SACA,YACA,SACA,CAAI,EACJ,EAAY,gBAAU,IACtB,MAAgC,cAAQ,OACxC,MAAgD,cAAQ,OACxD,MAA8C,cAAQ,OACtD,MAAkD,cAAQ,OAC1D,MAAkD,cAAQ,OAC1D,MAAkD,cAAQ,OAC1D,MAAgD,cAAQ,OACxD,MAAkD,cAAQ,OAC1D,MAA8C,cAAQ,OACtD,MAAoD,cAAQ,OAC5D,MAA0C,cAAQ,OAClD,MAAwC,cAAQ,OAwOhD,MAtOE,eAAS,MACX,UACA,WAEA,CAAG,MACD,eAAS,MACX,sBACA,eAEA,CAAG,QACD,eAAS,MACX,sBACA,iBAEA,CAAG,QACD,eAAS,MACX,sBACA,gBAEA,CAAG,QACD,eAAS,MACX,sBACA,eAEA,CAAG,QACD,eAAS,MACX,sBACA,YAEA,CAAG,QACD,eAAS,MACX,sBACA,aAEA,CAAG,QACD,eAAS,MACX,0BACA,UACA,oCAEA,iDAEA,CAAG,MACD,eAAS,MACX,IAGA,2DACA,aACA,CAAK,EACL,wDACA,aACA,CAAK,EACL,2DACA,aACA,CAAK,EACL,CAAG,QACD,eAAS,MACX,0BACA,UACA,oCAEA,gDAEA,CAAG,MACD,eAAS,MACX,0BACA,UACA,oCAEA,kDAEA,CAAG,MACD,eAAS,MACX,0BACA,UACA,oCAEA,kDAEA,CAAG,MACD,eAAS,MACX,0BACA,UACA,oCAEA,kDAEA,CAAG,MACD,eAAS,MACX,0BACA,UACA,oCAEA,iDAEA,CAAG,MACD,eAAS,MACX,0BACA,UACA,oCAEA,kDAEA,CAAG,MACD,eAAS,MACX,0BACA,UACA,oCAEA,gDAEA,CAAG,MACD,eAAS,MACX,0BACA,UACA,oCAEA,mDAEA,CAAG,MACD,eAAS,MACX,0BACA,UACA,oCAEA,8CAEA,CAAG,MACD,eAAS,MACX,0BACA,UACA,oCAEA,6CAEA,CAAG,MACD,eAAS,MACX,sCAA4E,MAAc,EAC1F,KACA,CAAK,GAqDL,OApDA,GACA,aAEA,GACA,cAEA,YACA,gBAEA,YACA,iBAEA,YACA,kBAEA,GACA,iDAEA,GACA,gDAEA,GACA,kDAEA,GACA,kDAEA,GACA,kDAEA,GACA,iDAEA,GACA,kDAEA,GACA,gDAEA,GACA,mDAEA,GACA,8CAEA,GACA,6CAEA,KACA,GACA,KAEA,KACA,UACA,oCAEA,UACA,oCAEA,UACA,oCAEA,UACA,oCAEA,UACA,oCAEA,UACA,oCAEA,UACA,oCAEA,UACA,oCAEA,UACA,oCAEA,UACA,oCAEA,GACA,KAEA,cACA,CACA,CAAG,KACH,IACA,EAEA,kBAAsB,eAAa,CACnC,cACA,oBACA,6BACA,CACA,oBACA,4BACA,yCACA,kCACA,yBACA,cACA,YACA,YAAmB,CACnB,qBACA,sBACK,EACL,mBACA,+BAEA,CACA,sBACA,eACA,yBACA,yBACA,cACA,YACA,YACA,qBACA,sBACO,EAEP,CACA,uBACA,eACA,sBACA,mCAEA,yBACA,cACA,0BAGA,CACA,SACA,WACA,CACA,CAGA,iBAA2B,qBAAwB,iCAAoC,sCAAyC,2BAAkC,wDAA0D,oBAA0B,SACtP,eAA8B,YAAgB,mBAAsB,KAAO,yCAAkD,yCAAwD,YAA8B,oIAAqJ,gEAAqE,EAAK,SAHlb,sBAIA,QACA,iCACA,gBACA,sBACA,cACA,oBACA,wBACA,wBACA,wBACA,sBACA,wBACA,oBACA,yBACA,EACA,IACA,YACA,cACA,CAAG,CACH,eACA,iBACA,CAAG,CACH,cACA,gBACA,CAAG,CACH,SACA,WACA,CAAG,CACH,aACA,eACA,CAAG,CACH,aACA,eACA,CACA,EAyQiB,UAAI,CAxQrB,YACA,IACA,UACA,SACA,YACA,WACA,UACA,aACA,YACA,cACA,cACA,cACA,aACA,cACA,YACA,eACA,UACA,SACA,kBACA,SACA,YACA,CAAI,EACJ,EAAY,gBAAU,IACtB,MAAgC,cAAQ,OACxC,MAAgD,cAAQ,OACxD,MAA8C,cAAQ,OACtD,MAAkD,cAAQ,OAC1D,MAAkD,cAAQ,OAC1D,MAAkD,cAAQ,OAC1D,MAAgD,cAAQ,OACxD,MAAkD,cAAQ,OAC1D,MAA8C,cAAQ,OACtD,MAAoD,cAAQ,OAC5D,MAA0C,cAAQ,OAClD,MAAwC,cAAQ,OAChD,MAA0D,cAAQ,OAmOlE,MAjOE,eAAS,MACX,UACA,WAEA,CAAG,MACD,eAAS,MACX,sBACA,eAEA,CAAG,QACD,eAAS,MACX,sBACA,iBAEA,CAAG,QACD,eAAS,MACX,sBACA,gBAEA,CAAG,QACD,eAAS,MACX,sBACA,eAEA,CAAG,QACD,eAAS,MACX,sBACA,cAEA,CAAG,QACD,eAAS,MACX,OACA,UACA,oCAEA,iDAEA,CAAG,MACD,eAAS,MACX,OACA,UACA,oCAEA,gDAEA,CAAG,MACD,eAAS,MACX,OACA,UACA,oCAEA,kDAEA,CAAG,MACD,eAAS,MACX,OACA,UACA,oCAEA,kDAEA,CAAG,MACD,eAAS,MACX,OACA,UACA,oCAEA,kDAEA,CAAG,MACD,eAAS,MACX,OACA,UACA,oCAEA,iDAEA,CAAG,MACD,eAAS,MACX,OACA,UACA,oCAEA,kDAEA,CAAG,MACD,eAAS,MACX,OACA,UACA,oCAEA,gDAEA,CAAG,MACD,eAAS,MACX,OACA,UACA,oCAEA,mDAEA,CAAG,MACD,eAAS,MACX,OACA,UACA,oCAEA,8CAEA,CAAG,MACD,eAAS,MACX,OACA,UACA,oCAEA,6CAEA,CAAG,MACD,eAAS,MACX,OACA,UACA,oCAEA,uDAEA,CAAG,MACD,eAAS,MACX,wCAAgF,MAAc,EAC9F,KACA,CAAK,GAqDL,OApDA,YACA,gBAEA,YACA,iBAEA,YACA,kBAEA,YACA,eAEA,GACA,iDAEA,GACA,gDAEA,GACA,kDAEA,GACA,kDAEA,GACA,kDAEA,GACA,iDAEA,GACA,kDAEA,GACA,gDAEA,GACA,mDAEA,GACA,8CAEA,GACA,6CAEA,GACA,uDAEA,KACA,GACA,KAEA,KACA,UACA,oCAEA,UACA,oCAEA,UACA,oCAEA,UACA,oCAEA,UACA,oCAEA,UACA,oCAEA,UACA,oCAEA,UACA,oCAEA,UACA,oCAEA,UACA,oCAEA,UACA,oCAEA,UACA,oCAEA,GACA,KAEA,cACA,CACA,CAAG,KACH,IACA,EAEA,kBAAwB,eAAa,CACrC,cACA,oBACA,8BACA,gBACA,cACA,CAAK,EACL,mCACA,gDACA,uCAEA,CAAK,CACL,CACA,oBACA,wCAAgF,uBAAyB,EACzG,gBACA,CAAK,EACL,0BACA,cACA,YACA,YAAmB,CACnB,qBACA,UACA,CAAK,EACL,yBACA,OACA,WACA,CACA,CAAK,2BACL,CACA,sBACA,8BACA,yBACA,yBACA,cACA,YACA,YACA,qBACA,8BACO,EAEP,CACA,uBACA,8BACA,sBACA,2CAEA,yBACA,kCAEA,CACA,SACA,WACA,CACA,CAGA,iBAA2B,qBAAwB,iCAAoC,sCAAyC,2BAAkC,wDAA0D,oBAA0B,SACtP,eAA8B,YAAgB,mBAAsB,KAAO,yCAAkD,yCAAwD,YAA8B,oIAAqJ,gEAAqE,EAAK,SAHlb,sBAIA,QACA,iCACA,iCACA,gBACA,sBACA,cACA,oBACA,wBACA,wBACA,wBACA,sBACA,wBACA,oBACA,yBACA,EACA,IACA,YACA,cACA,CAAG,CACH,eACA,iBACA,CAAG,CACH,cACA,gBACA,CAAG,CACH,SACA,WACA,CAAG,CACH,aACA,eACA,CAAG,CACH,YACA,cACA,CAAG,CACH,aACA,eACA,CACA,EACA,MAkSc,UAAI,CAjSlB,YACA,IACA,UACA,SACA,SACA,YACA,WACA,UACA,aACA,YACA,cACA,cACA,cACA,aACA,cACA,YACA,eACA,UACA,SACA,kBACA,kBACA,SACA,YACA,CAAI,EACJ,EAAY,gBAAU,IACtB,MAAgC,cAAQ,OACxC,MAAgD,cAAQ,OACxD,MAA8C,cAAQ,OACtD,MAAkD,cAAQ,OAC1D,MAAkD,cAAQ,OAC1D,MAAkD,cAAQ,OAC1D,MAAgD,cAAQ,OACxD,MAAkD,cAAQ,OAC1D,MAA8C,cAAQ,OACtD,MAAoD,cAAQ,OAC5D,MAA0C,cAAQ,OAClD,MAAwC,cAAQ,OAChD,MAA0D,cAAQ,OAClE,MAA0D,cAAQ,OAyPlE,MAvPE,eAAS,MACX,UACA,WAEA,CAAG,MACD,eAAS,MACX,sBACA,eAEA,CAAG,QACD,eAAS,MACX,sBACA,iBAEA,CAAG,QACD,eAAS,MACX,sBACA,gBAEA,CAAG,QACD,eAAS,MACX,sBACA,eAEA,CAAG,QACD,eAAS,MACX,8BACA,cAEA,CAAG,QACD,eAAS,MACX,sBACA,cAEA,CAAG,QACD,eAAS,MACX,OACA,UACA,oCAEA,iDAEA,CAAG,MACD,eAAS,MACX,OACA,UACA,oCAEA,gDAEA,CAAG,MACD,eAAS,MACX,OACA,UACA,oCAEA,kDAEA,CAAG,MACD,eAAS,MACX,OACA,UACA,oCAEA,kDAEA,CAAG,MACD,eAAS,MACX,OACA,UACA,oCAEA,kDAEA,CAAG,MACD,eAAS,MACX,OACA,UACA,oCAEA,iDAEA,CAAG,MACD,eAAS,MACX,OACA,UACA,oCAEA,kDAEA,CAAG,MACD,eAAS,MACX,OACA,UACA,oCAEA,gDAEA,CAAG,MACD,eAAS,MACX,OACA,UACA,oCAEA,mDAEA,CAAG,MACD,eAAS,MACX,OACA,UACA,oCAEA,8CAEA,CAAG,MACD,eAAS,MACX,OACA,UACA,oCAEA,6CAEA,CAAG,MACD,eAAS,MACX,OACA,UACA,oCAEA,uDAEA,CAAG,MACD,eAAS,MACX,OACA,UACA,oCAEA,uDAEA,CAAG,MACD,eAAS,MACX,qCAA0E,UAAgC,EAC1G,KACA,CAAK,GA8DL,MA7DA,oBACA,eAEA,YACA,eAEA,oBACA,eAEA,YACA,gBAEA,YACA,iBAEA,YACA,kBAEA,GACA,iDAEA,GACA,gDAEA,GACA,kDAEA,GACA,kDAEA,GACA,kDAEA,GACA,iDAEA,GACA,kDAEA,GACA,gDAEA,GACA,mDAEA,GACA,8CAEA,GACA,6CAEA,GACA,uDAEA,GACA,uDAEA,KACA,GACA,KAEA,KACA,UACA,oCAEA,UACA,oCAEA,UACA,oCAEA,UACA,oCAEA,UACA,oCAEA,UACA,oCAEA,UACA,oCAEA,UACA,oCAEA,UACA,oCAEA,UACA,oCAEA,UACA,oCAEA,UACA,oCAEA,GACA,KAEA,cACA,CACA,CAAG,KACH,IACA,EAEA,kBAAqB,eAAa,CAClC,cACA,oBACA,8BACA,gBACA,WACA,CAAK,EACL,gCACA,6CACA,oCAEA,CAAK,CACL,CACA,oBACA,qCAA0E,uBAAyB,EACnG,iBACK,EACL,0BACA,cACA,YACA,YAAmB,CACnB,qBACA,UACA,CAAK,EACL,yBACA,OACA,QACA,CACA,CAAK,wBACL,CACA,sBACA,2BACA,yBACA,yBACA,cACA,YACA,YACA,qBACA,2BACO,EAEP,CACA,uBACA,6BACA,KACA,uBACA,wCAEA,yBACA,2CACA,CACA,CACA,SACA,WACA,CACA,CAGA,iBAA2B,qBAAwB,iCAAoC,sCAAyC,2BAAkC,wDAA0D,oBAA0B,SACtP,eAA8B,YAAgB,mBAAsB,KAAO,yCAAkD,yCAAwD,YAA8B,oIAAqJ,gEAAqE,EAAK,SAHlb,sBAIA,QACA,gBACA,sBACA,wBACA,sBACA,wBACA,oBACA,0BACA,0BACA,gCACA,kCACA,4BACA,2BACA,EACA,IACA,SACA,QACA,CAAG,CACH,kBACA,iBACA,CAAG,CACH,cACA,aACA,CAAG,CACH,aACA,YACA,CAAG,CACH,qBACA,oBACA,CAAG,CACH,qBACA,oBACA,CAAG,CACH,YACA,WACA,CAAG,CACH,iBACA,gBACA,CAAG,CACH,qBACA,uBACA,CAAG,CACH,cACA,gBACA,CAAG,CACH,iBACA,mBACA,CAAG,CACH,SACA,WACA,CAAG,CACH,WACA,aACA,CAAG,CACH,eACA,cACA,CACA,EAoPY,UAAI,CAnPhB,YACA,IACA,UACA,UACA,aACA,cACA,cACA,aACA,cACA,YACA,eACA,eACA,kBACA,mBACA,gBACA,gBACA,SACA,YACA,CAAI,EACJ,EAAY,gBAAU,IACtB,MAAgC,cAAQ,OACxC,MAAgD,cAAQ,OACxD,MAAkD,cAAQ,OAC1D,MAAkD,cAAQ,OAC1D,MAAgD,cAAQ,OACxD,MAAkD,cAAQ,OAC1D,MAA8C,cAAQ,OACtD,MAAoD,cAAQ,OAC5D,MAA0C,cAAQ,OAClD,MAAoD,cAAQ,OAC5D,MAA0D,cAAQ,OAClE,MAA4D,cAAQ,OACpE,MAAsD,cAAQ,OAC9D,MAAsD,cAAQ,OAgN9D,MA9ME,eAAS,MACX,UACA,WAEA,CAAG,MACD,eAAS,MACX,OACA,UACA,oCAEA,iDAEA,CAAG,MACD,eAAS,MACX,OACA,UACA,oCAEA,kDAEA,CAAG,MACD,eAAS,MACX,OACA,UACA,oCAEA,kDAEA,CAAG,MACD,eAAS,MACX,OACA,UACA,oCAEA,iDAEA,CAAG,MACD,eAAS,MACX,OACA,UACA,oCAEA,kDAEA,CAAG,MACD,eAAS,MACX,OACA,UACA,oCAEA,gDAEA,CAAG,MACD,eAAS,MACX,OACA,UACA,oCAEA,mDAEA,CAAG,MACD,eAAS,MACX,OACA,UACA,oCAEA,8CAEA,CAAG,MACD,eAAS,MACX,OACA,UACA,oCAEA,mDAEA,CAAG,MACD,eAAS,MACX,OACA,UACA,oCAEA,sDAEA,CAAG,MACD,eAAS,MACX,OACA,UACA,oCAEA,uDAEA,CAAG,MACD,eAAS,MACX,OACA,UACA,oCAEA,oDAEA,CAAG,MACD,eAAS,MACX,OACA,UACA,oCAEA,oDAEA,CAAG,MACD,eAAS,MACX,aACA,mCAAwE,MAAc,EACtF,KACA,CAAO,EACP,IACA,iDAEA,GACA,kDAEA,GACA,kDAEA,GACA,iDAEA,GACA,kDAEA,GACA,gDAEA,GACA,mDAEA,GACA,8CAEA,GACA,mDAEA,GACA,sDAEA,GACA,uDAEA,GACA,oDAEA,GACA,oDAEA,KACA,GACA,IAEA,CACA,WACA,IACA,UACA,oCAEA,UACA,oCAEA,UACA,oCAEA,UACA,oCAEA,UACA,oCAEA,UACA,oCAEA,UACA,oCAEA,UACA,oCAEA,UACA,oCAEA,UACA,oCAEA,UACA,oCAEA,UACA,oCAEA,UACA,oCAEA,GACA,KAEA,eAEA,CACA,CAAG,KACH,IACA,EAEA,kBAAmB,eAAa,CAChC,cACA,oBACA,8BACA,gBACA,SACA,CAAK,EACL,8BACA,2CACA,kCAEA,CAAK,CACL,CACA,oBACA,wBACA,mCAAwE,uBAAyB,EACjG,gBACA,CAAO,EACP,0BACA,cACA,YACA,YAAqB,CACrB,qBACA,UACA,CAAO,EACP,kBACA,EACA,MACA,GACO,qBACP,CACA,CACA,sBACA,yBACA,yBACA,yBACA,cACA,YACA,YACA,qBACA,yBACO,EAEP,CACA,uBACA,yBACA,sBACA,sCAEA,yBACA,iBACA,6BAGA,CACA,SACA,WACA,CACA,CAGA,iBAA2B,qBAAwB,iCAAoC,sCAAyC,2BAAkC,wDAA0D,oBAA0B,SACtP,eAA8B,YAAgB,mBAAsB,KAAO,yCAAkD,yCAAwD,YAA8B,oIAAqJ,gEAAqE,EAAK,SAHlb,sBAIA,QACA,gBACA,mDACA,gCACA,EACA,IACA,aACA,eACA,CAAG,CACH,SACA,WACA,CAAG,CACH,YACA,cACA,CACA,CACA,kBAAuB,eAAa,CACpC,cACA,oBACA,8BACA,gBACA,aACA,CAAK,EACL,kCACA,+CACA,sCAEA,CAAK,CACL,CACA,oBACA,uCAA8E,uBAAyB,EACvG,gBACA,CAAK,EACL,0BACA,cACA,YACA,YAAmB,CACnB,qBACA,UACA,CAAK,EACL,yBACA,OACA,UACA,CACA,CAAK,0BACL,CACA,sBACA,6BACA,yBACA,yBACA,cACA,YACA,YACA,qBACA,4BACA,CAAO,EAEP,CACA,uBACA,6BACA,sBACA,0CAEA,yBACA,iCAEA,CACA,SACA,WACA,CACA,CAGA,iBACA,4DACA,IACA,GACA,CACA,CAEA,iBACA,yBACA,CAEA,iBACA,iGACA,CAqDA,2BAjCA,EACA,EACA,IAgCA,mBAzCA,EAyCA,yBAlCA,EAkCA,aApCA,EAoCA,OApCA,GAGA,8CACA,8CACA,KACA,CACA,6BACA,kBA4BA,EA5BA,QACA,kCACA,yBA0BA,EA1BA,OACA,EAEA,CACA,eACA,aACA,GAoBA,SAlBA,OACA,mCACA,MACA,IACA,IACA,IACA,CAAM,EACN,OACA,2BACA,yBACA,CACA,CACA,OACA,eACA,aACA,CACA,EAEA,KAlDA,EAkDA,gCA7CA,EA6CA,OA7CA,IA8CA,CAKA,iBAA2B,qBAAwB,iCAAoC,sCAAyC,2BAAkC,wDAA0D,oBAA0B,SAsCtP,iBAA2B,qBAAwB,iCAAoC,sCAAyC,2BAAkC,wDAA0D,oBAA0B,SAEtP,sBACA,EAGA,wEACA,GAHA,EAIA,CACA,sBACA,EAGA,sJACA,GAHA,EAIA,CAhIA,sBAwKmB,UAAI,CAlCvB,YACA,IACA,WACA,SACA,cACA,SACA,SACA,YACA,yBACA,WACA,CAAI,EACJ,EAAY,gBAAU,IACtB,EAAkB,aAAO,MACzB,oCAEA,OADA,4BACA,CACA,CAAG,KACH,EAAgB,aAAO,KACvB,CA3EA,oBACA,wCACA,qBACA,QACA,iBACA,YACA,gBACA,aACA,CACA,QAEA,IADA,EACA,sDACA,gCACA,CACA,OAOA,8BADA,GALA,qBACA,SAjBA,GAA8B,YAAgB,mBAAsB,KAAO,yCAAkD,yCAAwD,YAA8B,oIAAqJ,gEAAqE,EAAK,UAiBlb,EAAqC,sCACrC,IACA,GACA,CAAO,EACP,4BAIA,yBAEA,CACA,WACA,kCACA,qDAEA,CACA,CACA,sBACA,EAyCA,WACG,WAaH,MAZE,eAAS,MACX,cACA,qBACA,KACA,cACA,uBACA,GACG,OAED,eAAS,MACX,2BACA,CAAG,QACM,cAAqB,KAC9B,EAEA,kBAA0B,eAAa,CACvC,eACA,SACA,gBACA,YACA,gBAEA,mBACA,CACA,CAAK,EACL,yBACA,6BAEA,8BACA,6DACA,EACA,eACA,YACS,EAET,eACA,WACA,CAAS,CAET,CAAK,EACL,oBACA,QACA,kBACA,2DACA,CAAK,EACL,gCAMA,IAeA,QAfA,KALA,iCACA,SAvFA,GAA8B,YAAgB,mBAAsB,KAAO,yCAAkD,yCAAwD,YAA8B,oIAAqJ,gEAAqE,EAAK,UAuFlb,CACA,IACA,GACA,CAAO,6FAAkH,EACzH,sCACA,EAvIA,cACA,8EACA,EAqIA,GAGA,oCAGA,kCAGA,sCAGA,wCACO,GAEP,eACA,gBACA,wBACA,0BACA,4BACA,8BACA,mBACA,CACA,CAAS,CAET,CAAK,EACL,mBACA,wBACA,CAAK,EACL,uBACA,QACA,oBACA,WACA,EAAO,EACP,8DACA,CAAK,EACL,kBAAwB,eAAS,GAEjC,kCACA,mBACA,iBACA,yBACA,kBACA,CACA,oBACA,qCACA,CACA,sBACA,qBACA,0BACA,eACA,uBACA,iBACA,wBAEA,wCACA,iBAEA,CACA,uBACA,6BACA,CACA,SACA,+BACA,EACa,cAAqB,CAAC,SAAG,QACtC,sBACA,gCACA,SAAkB,UAAQ,0BAC1B,CAAO,KAEP,IAEA,CACA,CAYA,iBAA2B,qBAAwB,iCAAoC,sCAAyC,2BAAkC,wDAA0D,oBAA0B,SACtP,eAA8B,YAAgB,mBAAsB,KAAO,yCAAkD,yCAAwD,YAA8B,oIAAqJ,gEAAqE,EAAK,SAZlb,+BACA,2BACA,mCACA,qCACA,kDACA,sBAQA,QACA,sBACA,eACA,EACA,IACA,aACA,eACA,CACA,EAsCqB,UAAI,CArCzB,YACA,IACA,MACA,SACA,UACA,UACA,CAAI,EACJ,EAAY,gBAAU,IACtB,8GACA,EAAsB,aAAO,KAC7B,qCACG,IAwBH,MAvBE,eAAS,MACX,UACA,WAEA,CAAG,MACD,eAAS,MACX,uBACA,eACA,YAEA,CAAG,QACD,eAAS,MACX,sBACA,kBAEA,CAAG,QACD,eAAS,MACX,iHACA,wBACA,kBACA,YAEA,CAAG,QACH,IACA,EAEA,kBAA4B,eAAa,CACzC,cACA,oBACA,8BACA,gBACA,kBACA,CAAK,EACL,uCACA,oDACA,2CAEA,CAAK,CACL,CACA,oBACA,sjBAAqkB,IAAI,yFACzkB,6EAA2H,uBAAyB,EACpJ,iBACK,EACL,0BACA,cACA,YACA,YAAmB,CACnB,qBACA,UACA,CAAK,EACL,yBACA,OACA,eACA,CACA,CAAK,+BACL,CACA,sBACA,kCACA,yBACA,yBACA,cACA,YACA,YACA,qBACA,kCACO,EAEP,CACA,uBACA,2BACA,sBACA,+CAEA,sCAEA,CACA,SACA,WACA,CACA,CAMA,iBAAyB,qBAAwB,iCAAoC,sCAAyC,2BAAkC,wDAA0D,oBAA0B,SACpP,eAA4B,YAAgB,mBAAsB,KAAO,yCAAkD,yCAAsD,YAA8B,oIAAmJ,gEAAqE,EAAK,SAN5a,qBACA,OA7GA,WAEA,CA4GA,CAAC,EACD,sBAIA,UACA,IACA,UACA,YACA,CAAG,CACH,SACA,WACA,CAAG,CACH,aACA,eACA,CACA,EAiDoB,UAAI,CAhDxB,YACA,IACA,OACA,SACA,YACA,UACA,CAAI,EACJ,EAAY,gBAAU,IACtB,MAAgC,cAAQ,OAsCxC,MArCE,eAAS,MACX,2BACA,+DAA8E,mBAAmB,iDAEjG,CAAG,KACD,eAAS,MACX,uDACA,CAAG,MAED,eAAS,MACX,UACA,WAEA,CAAG,MACD,eAAS,MACX,aACA,eAEA,CAAG,QACD,eAAS,MACX,yDAAgG,MAAc,EAC9G,OACA,KACA,CAAK,GAKL,OAJA,KACA,GACA,KAEA,KACA,WACA,GACA,KAEA,eAEA,CACA,CAAG,KACH,IACA,EAEA,kBAA2B,eAAa,CACxC,cACA,oBACA,8BACA,gBACA,iBACA,CAAK,EACL,sCACA,mDACA,0CAEA,CAAK,CACL,CACA,oBACA,+DAA4E,mBAAmB,kDAC/F,oFACA,yDAAgG,uBAAyB,EACzH,qBACA,iBACK,EACL,0BACA,cACA,YACA,YAAmB,CACnB,qBACA,UACA,CAAK,EACL,yBACA,OACA,cACA,CACA,CAAK,8BACL,CACA,sBACA,yBACA,yBACA,cACA,YACA,YACA,qBACA,iCACK,CACL,CACA,uBACA,iCACA,sBACA,8CAEA,yBACA,qCAEA,CACA,SACA,WACA,CACA,CACA,sBAEA,QACA,0BACA,6BACA,qCACA,2BACA,kBACA,iCACA,mCACA,4BACA,EACA,IACA,gBACA,2BACA,CAAG,CACH,WACA,aACA,CAAG,CACH,oBACA,sBACA,CAAG,CACH,aACA,eACA,CAAG,CACH,UACA,YACA,CAAG,CACH,cACA,gBACA,CAAG,CACH,SACA,WACA,CAAG,CACH,aACA,eACA,CAAG,CACH,UACA,YACA,CACA,CACA,kBAAiC,eAAa,CAC9C,cACA,oBACA,8BACA,gBACA,uBACA,CAAK,EACL,4CACA,yDACA,gDAEA,CAAK,CACL,CACA,oBAEA,IADA,IACA,kEACA,0BACA,cACA,YACA,YAAmB,CACnB,qBACA,UACA,CAAK,EACL,kBACA,EACA,oBACA,GACK,mCACL,CACA,sBACA,uCACA,yBACA,yBACA,cACA,YACA,YACA,qBACA,uCACO,EAEP,CACA,uBACA,uCACA,sBACA,oDAEA,yBACA,6CAEA,CACA,SACA,WACA,CACA,CACA,qBAEA,kBAAgC,eAAa,CAC7C,cACA,oBACA,gBACA,sBACA,CAAK,EACL,2CACA,wDACA,+CAEA,CAAK,CACL,CACA,oBACA,wCACA,yBACA,OACA,mBACA,CACA,CAAK,mCACL,CACA,uBACA,2DACA,kDAEA,CACA,SACA,WACA,CACA,CACA,sBAEgC,eAAa,CAsC7C,QACA,wCACA,EACA,IACA,gBACA,kBACA,CAAG,CACH,SACA,WACA,CAAG,CACH,aACA,eACA,CAAG,CACH,WACA,aACA,CAAG,CACH,gBACA,kBACA,CACA,CACA,kBAAiC,eAAa,CAC9C,cACA,oBACA,8BACA,gBACA,uBACA,CAAK,EACL,4CACA,uCACA,mDACA,mBACA,iDAGA,CAAK,CACL,CACA,oBACA,4DACA,0BACA,cACA,YACA,YAAmB,CACnB,qBACA,UACA,CAAK,EACL,yBACA,OACA,oBACA,CACA,CAAK,oCACL,CACA,sBACA,uCACA,yBACA,yBACA,cACA,YACA,YACA,qBACA,uCACO,EAEP,CACA,uBACA,uCACA,sBACA,oDAEA,yBACA,+BACA,2CAGA,CACA,SACA,WACA,CACA,CACA,sBAEoC,eAAa,CAoCjD,QACA,gCACA,EACA,IACA,YACA,cACA,CACA,CACA,kBAAkC,eAAa,CAC/C,cACA,oBACA,8BACA,0BAA8C,eAAS,IACvD,gBACA,cACA,CAAK,EACL,mCACA,gDACA,uCAEA,CAAK,CACL,CACA,oBAEA,GADA,uDAAoE,YAAY,yDAChF,oEACA,2DACA,aACA,4DACA,0BACA,cACA,YACA,YAAuB,CACvB,qBACA,UACA,CAAS,EACT,yBACA,OACA,WACA,CACA,CAAS,2BACT,CACA,CACA,CACA,sBACA,8BACA,yBACA,yBACA,cACA,YACA,YACA,qBACA,8BACO,EAEP,CACA,uBACA,8BACA,sBACA,2CAEA,yBAEA,CACA,SACA,MAAW,SAAG,QACd,0BACA,SAAgB,UAAQ,0BACxB,CAAK,CACL,CACA,CACA,sBAEA,QACA,8BACA,EACA,IACA,YACA,cACA,CAAG,CACH,kBACA,6BACA,CAAG,CACH,YACA,cACA,CAAG,CACH,aACA,eACA,CAAG,CACH,WACA,aACA,CACA,CACA,kBAA2B,eAAa,CACxC,cACA,oBACA,8BACA,0BAA8C,eAAS,IACvD,gBACA,iBACA,CAAK,EACL,sCACA,mDACA,0CAEA,CAAK,CACL,CACA,oBAEA,uDAAoE,YAAY,yDAEhF,IAHA,EAGA,0EACA,MACA,+DACA,0BACA,cACA,YACA,YAAqB,CACrB,qBACA,UACA,CAAO,EACP,kBACA,EACA,cACA,GACO,6BACP,CACA,CACA,sBACA,yBACA,yBACA,cACA,YACA,YACA,qBACA,iCACK,CACL,CACA,uBACA,gCACA,wBAEA,CACA,SACA,MAAW,SAAG,QACd,0BACA,+BACA,SAAgB,UAAQ,0BACxB,CAAK,CACL,CACA,CACA,qBACA,YACA,CAAC,EACD", "sources": ["webpack://_N_E/./node_modules/@react-google-maps/api/dist/esm.js"], "sourcesContent": ["import { jsx, jsxs, Fragment } from 'react/jsx-runtime';\nimport { createContext, useContext, memo, useState, useRef, useEffect, PureComponent, useMemo, Children, isValidElement, cloneElement, createRef } from 'react';\nimport * as ReactDOM from 'react-dom';\nimport { createPortal } from 'react-dom';\n\nfunction _typeof(o) {\n  \"@babel/helpers - typeof\";\n\n  return _typeof = \"function\" == typeof Symbol && \"symbol\" == typeof Symbol.iterator ? function (o) {\n    return typeof o;\n  } : function (o) {\n    return o && \"function\" == typeof Symbol && o.constructor === Symbol && o !== Symbol.prototype ? \"symbol\" : typeof o;\n  }, _typeof(o);\n}\n\nfunction toPrimitive(t, r) {\n  if (\"object\" != _typeof(t) || !t) return t;\n  var e = t[Symbol.toPrimitive];\n  if (void 0 !== e) {\n    var i = e.call(t, r || \"default\");\n    if (\"object\" != _typeof(i)) return i;\n    throw new TypeError(\"@@toPrimitive must return a primitive value.\");\n  }\n  return (\"string\" === r ? String : Number)(t);\n}\n\nfunction toPropertyKey(t) {\n  var i = toPrimitive(t, \"string\");\n  return \"symbol\" == _typeof(i) ? i : i + \"\";\n}\n\nfunction _defineProperty(e, r, t) {\n  return (r = toPropertyKey(r)) in e ? Object.defineProperty(e, r, {\n    value: t,\n    enumerable: !0,\n    configurable: !0,\n    writable: !0\n  }) : e[r] = t, e;\n}\n\nfunction getDefaultExportFromCjs$1 (x) {\n\treturn x && x.__esModule && Object.prototype.hasOwnProperty.call(x, 'default') ? x['default'] : x;\n}\n\n/**\n * Copyright (c) 2013-present, Facebook, Inc.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\nvar invariant_1;\nvar hasRequiredInvariant;\nfunction requireInvariant() {\n  if (hasRequiredInvariant) return invariant_1;\n  hasRequiredInvariant = 1;\n\n  /**\n   * Use invariant() to assert state which your program assumes to be true.\n   *\n   * Provide sprintf-style format (only %s is supported) and arguments\n   * to provide information about what broke and what you were\n   * expecting.\n   *\n   * The invariant message will be stripped in production, but the invariant\n   * will remain to ensure logic does not differ in production.\n   */\n\n  var NODE_ENV = process.env.NODE_ENV;\n  var invariant = function invariant(condition, format, a, b, c, d, e, f) {\n    if (NODE_ENV !== 'production') {\n      if (format === undefined) {\n        throw new Error('invariant requires an error message argument');\n      }\n    }\n    if (!condition) {\n      var error;\n      if (format === undefined) {\n        error = new Error('Minified exception occurred; use the non-minified dev environment ' + 'for the full error message and additional helpful warnings.');\n      } else {\n        var args = [a, b, c, d, e, f];\n        var argIndex = 0;\n        error = new Error(format.replace(/%s/g, function () {\n          return args[argIndex++];\n        }));\n        error.name = 'Invariant Violation';\n      }\n      error.framesToPop = 1; // we don't care about invariant's own frame\n      throw error;\n    }\n  };\n  invariant_1 = invariant;\n  return invariant_1;\n}\n\nvar invariantExports = requireInvariant();\nvar invariant = /*@__PURE__*/getDefaultExportFromCjs$1(invariantExports);\n\nvar MapContext = createContext(null);\nfunction useGoogleMap() {\n  invariant(!!useContext, 'useGoogleMap is React hook and requires React version 16.8+');\n  var map = useContext(MapContext);\n  invariant(!!map, 'useGoogleMap needs a GoogleMap available up in the tree');\n  return map;\n}\n\n// eslint-disable-next-line @typescript-eslint/no-explicit-any\nfunction reduce(obj, fn, acc) {\n  return Object.keys(obj).reduce(function reducer(newAcc, key) {\n    return fn(newAcc, obj[key], key);\n  }, acc);\n}\n\n// eslint-disable-next-line @typescript-eslint/no-explicit-any\nfunction forEach(obj, fn) {\n  Object.keys(obj).forEach(key => {\n    return fn(obj[key], key);\n  });\n}\n\n/* global google */\n/* eslint-disable filenames/match-regex */\nfunction applyUpdaterToNextProps(\n// eslint-disable-next-line @typescript-eslint/no-explicit-any\nupdaterMap,\n// eslint-disable-next-line @typescript-eslint/no-explicit-any\nprevProps,\n// eslint-disable-next-line @typescript-eslint/no-explicit-any\nnextProps,\n// eslint-disable-next-line @typescript-eslint/no-explicit-any\ninstance\n// eslint-disable-next-line @typescript-eslint/no-explicit-any\n) {\n  // eslint-disable-next-line @typescript-eslint/no-explicit-any\n  var map = {};\n  // eslint-disable-next-line @typescript-eslint/no-explicit-any\n  var iter = (fn, key) => {\n    var nextValue = nextProps[key];\n    if (nextValue !== prevProps[key]) {\n      map[key] = nextValue;\n      fn(instance, nextValue);\n    }\n  };\n  forEach(updaterMap, iter);\n  return map;\n}\nfunction registerEvents(\n// eslint-disable-next-line @typescript-eslint/no-explicit-any\nprops,\n// eslint-disable-next-line @typescript-eslint/no-explicit-any\ninstance, eventMap) {\n  var registeredList = reduce(eventMap, function reducer(acc, googleEventName,\n  // eslint-disable-next-line @typescript-eslint/no-explicit-any\n  onEventName) {\n    if (typeof props[onEventName] === 'function') {\n      acc.push(google.maps.event.addListener(instance, googleEventName, props[onEventName]));\n    }\n    return acc;\n  }, []);\n  return registeredList;\n}\nfunction unregisterEvent(registered) {\n  google.maps.event.removeListener(registered);\n}\nfunction unregisterEvents() {\n  var events = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : [];\n  events.forEach(unregisterEvent);\n}\nfunction applyUpdatersToPropsAndRegisterEvents(_ref) {\n  var {\n    updaterMap,\n    eventMap,\n    prevProps,\n    nextProps,\n    instance\n  } = _ref;\n  var registeredEvents = registerEvents(nextProps, instance, eventMap);\n  applyUpdaterToNextProps(updaterMap, prevProps, nextProps, instance);\n  return registeredEvents;\n}\n\nvar eventMap$i = {\n  onDblClick: 'dblclick',\n  onDragEnd: 'dragend',\n  onDragStart: 'dragstart',\n  onMapTypeIdChanged: 'maptypeid_changed',\n  onMouseMove: 'mousemove',\n  onMouseOut: 'mouseout',\n  onMouseOver: 'mouseover',\n  onMouseDown: 'mousedown',\n  onMouseUp: 'mouseup',\n  onRightClick: 'rightclick',\n  onTilesLoaded: 'tilesloaded',\n  onBoundsChanged: 'bounds_changed',\n  onCenterChanged: 'center_changed',\n  onClick: 'click',\n  onDrag: 'drag',\n  onHeadingChanged: 'heading_changed',\n  onIdle: 'idle',\n  onProjectionChanged: 'projection_changed',\n  onResize: 'resize',\n  onTiltChanged: 'tilt_changed',\n  onZoomChanged: 'zoom_changed'\n};\nvar updaterMap$i = {\n  extraMapTypes(map, extra) {\n    extra.forEach(function forEachExtra(it, i) {\n      map.mapTypes.set(String(i), it);\n    });\n  },\n  center(map, center) {\n    map.setCenter(center);\n  },\n  clickableIcons(map, clickable) {\n    map.setClickableIcons(clickable);\n  },\n  heading(map, heading) {\n    map.setHeading(heading);\n  },\n  mapTypeId(map, mapTypeId) {\n    map.setMapTypeId(mapTypeId);\n  },\n  options(map, options) {\n    map.setOptions(options);\n  },\n  streetView(map, streetView) {\n    map.setStreetView(streetView);\n  },\n  tilt(map, tilt) {\n    map.setTilt(tilt);\n  },\n  zoom(map, zoom) {\n    map.setZoom(zoom);\n  }\n};\n// TODO: unfinished!\nfunction GoogleMapFunctional(_ref) {\n  var {\n    children,\n    options,\n    id,\n    mapContainerStyle,\n    mapContainerClassName,\n    center,\n    // clickableIcons,\n    // extraMapTypes,\n    // heading,\n    // mapTypeId,\n    onClick,\n    onDblClick,\n    onDrag,\n    onDragEnd,\n    onDragStart,\n    onMouseMove,\n    onMouseOut,\n    onMouseOver,\n    onMouseDown,\n    onMouseUp,\n    onRightClick,\n    // onMapTypeIdChanged,\n    // onTilesLoaded,\n    // onBoundsChanged,\n    onCenterChanged,\n    // onHeadingChanged,\n    // onIdle,\n    // onProjectionChanged,\n    // onResize,\n    // onTiltChanged,\n    // onZoomChanged,\n    onLoad,\n    onUnmount\n  } = _ref;\n  var [map, setMap] = useState(null);\n  var ref = useRef(null);\n  // const [extraMapTypesListener, setExtraMapTypesListener] = useState<google.maps.MapsEventListener | null>(null)\n  var [centerChangedListener, setCenterChangedListener] = useState(null);\n  var [dblclickListener, setDblclickListener] = useState(null);\n  var [dragendListener, setDragendListener] = useState(null);\n  var [dragstartListener, setDragstartListener] = useState(null);\n  var [mousedownListener, setMousedownListener] = useState(null);\n  var [mousemoveListener, setMousemoveListener] = useState(null);\n  var [mouseoutListener, setMouseoutListener] = useState(null);\n  var [mouseoverListener, setMouseoverListener] = useState(null);\n  var [mouseupListener, setMouseupListener] = useState(null);\n  var [rightclickListener, setRightclickListener] = useState(null);\n  var [clickListener, setClickListener] = useState(null);\n  var [dragListener, setDragListener] = useState(null);\n  // Order does matter\n  useEffect(() => {\n    if (options && map !== null) {\n      map.setOptions(options);\n    }\n  }, [map, options]);\n  useEffect(() => {\n    if (map !== null && typeof center !== 'undefined') {\n      map.setCenter(center);\n    }\n  }, [map, center]);\n  useEffect(() => {\n    if (map && onDblClick) {\n      if (dblclickListener !== null) {\n        google.maps.event.removeListener(dblclickListener);\n      }\n      setDblclickListener(google.maps.event.addListener(map, 'dblclick', onDblClick));\n    }\n  }, [onDblClick]);\n  useEffect(() => {\n    if (map && onDragEnd) {\n      if (dragendListener !== null) {\n        google.maps.event.removeListener(dragendListener);\n      }\n      setDragendListener(google.maps.event.addListener(map, 'dragend', onDragEnd));\n    }\n  }, [onDragEnd]);\n  useEffect(() => {\n    if (map && onDragStart) {\n      if (dragstartListener !== null) {\n        google.maps.event.removeListener(dragstartListener);\n      }\n      setDragstartListener(google.maps.event.addListener(map, 'dragstart', onDragStart));\n    }\n  }, [onDragStart]);\n  useEffect(() => {\n    if (map && onMouseDown) {\n      if (mousedownListener !== null) {\n        google.maps.event.removeListener(mousedownListener);\n      }\n      setMousedownListener(google.maps.event.addListener(map, 'mousedown', onMouseDown));\n    }\n  }, [onMouseDown]);\n  useEffect(() => {\n    if (map && onMouseMove) {\n      if (mousemoveListener !== null) {\n        google.maps.event.removeListener(mousemoveListener);\n      }\n      setMousemoveListener(google.maps.event.addListener(map, 'mousemove', onMouseMove));\n    }\n  }, [onMouseMove]);\n  useEffect(() => {\n    if (map && onMouseOut) {\n      if (mouseoutListener !== null) {\n        google.maps.event.removeListener(mouseoutListener);\n      }\n      setMouseoutListener(google.maps.event.addListener(map, 'mouseout', onMouseOut));\n    }\n  }, [onMouseOut]);\n  useEffect(() => {\n    if (map && onMouseOver) {\n      if (mouseoverListener !== null) {\n        google.maps.event.removeListener(mouseoverListener);\n      }\n      setMouseoverListener(google.maps.event.addListener(map, 'mouseover', onMouseOver));\n    }\n  }, [onMouseOver]);\n  useEffect(() => {\n    if (map && onMouseUp) {\n      if (mouseupListener !== null) {\n        google.maps.event.removeListener(mouseupListener);\n      }\n      setMouseupListener(google.maps.event.addListener(map, 'mouseup', onMouseUp));\n    }\n  }, [onMouseUp]);\n  useEffect(() => {\n    if (map && onRightClick) {\n      if (rightclickListener !== null) {\n        google.maps.event.removeListener(rightclickListener);\n      }\n      setRightclickListener(google.maps.event.addListener(map, 'rightclick', onRightClick));\n    }\n  }, [onRightClick]);\n  useEffect(() => {\n    if (map && onClick) {\n      if (clickListener !== null) {\n        google.maps.event.removeListener(clickListener);\n      }\n      setClickListener(google.maps.event.addListener(map, 'click', onClick));\n    }\n  }, [onClick]);\n  useEffect(() => {\n    if (map && onDrag) {\n      if (dragListener !== null) {\n        google.maps.event.removeListener(dragListener);\n      }\n      setDragListener(google.maps.event.addListener(map, 'drag', onDrag));\n    }\n  }, [onDrag]);\n  useEffect(() => {\n    if (map && onCenterChanged) {\n      if (centerChangedListener !== null) {\n        google.maps.event.removeListener(centerChangedListener);\n      }\n      setCenterChangedListener(google.maps.event.addListener(map, 'center_changed', onCenterChanged));\n    }\n  }, [onClick]);\n  useEffect(() => {\n    var map = ref.current === null ? null : new google.maps.Map(ref.current, options);\n    setMap(map);\n    if (map !== null && onLoad) {\n      onLoad(map);\n    }\n    return () => {\n      if (map !== null) {\n        if (onUnmount) {\n          onUnmount(map);\n        }\n      }\n    };\n  }, []);\n  return jsx(\"div\", {\n    id: id,\n    ref: ref,\n    style: mapContainerStyle,\n    className: mapContainerClassName,\n    children: jsx(MapContext.Provider, {\n      value: map,\n      children: map !== null ? children : null\n    })\n  });\n}\nmemo(GoogleMapFunctional);\nclass GoogleMap extends PureComponent {\n  constructor() {\n    super(...arguments);\n    _defineProperty(this, \"state\", {\n      map: null\n    });\n    _defineProperty(this, \"registeredEvents\", []);\n    _defineProperty(this, \"mapRef\", null);\n    _defineProperty(this, \"getInstance\", () => {\n      if (this.mapRef === null) {\n        return null;\n      }\n      return new google.maps.Map(this.mapRef, this.props.options);\n    });\n    _defineProperty(this, \"panTo\", latLng => {\n      var map = this.getInstance();\n      if (map) {\n        map.panTo(latLng);\n      }\n    });\n    _defineProperty(this, \"setMapCallback\", () => {\n      if (this.state.map !== null) {\n        if (this.props.onLoad) {\n          this.props.onLoad(this.state.map);\n        }\n      }\n    });\n    _defineProperty(this, \"getRef\", ref => {\n      this.mapRef = ref;\n    });\n  }\n  componentDidMount() {\n    var map = this.getInstance();\n    this.registeredEvents = applyUpdatersToPropsAndRegisterEvents({\n      updaterMap: updaterMap$i,\n      eventMap: eventMap$i,\n      prevProps: {},\n      nextProps: this.props,\n      instance: map\n    });\n    this.setState(function setMap() {\n      return {\n        map\n      };\n    }, this.setMapCallback);\n  }\n  componentDidUpdate(prevProps) {\n    if (this.state.map !== null) {\n      unregisterEvents(this.registeredEvents);\n      this.registeredEvents = applyUpdatersToPropsAndRegisterEvents({\n        updaterMap: updaterMap$i,\n        eventMap: eventMap$i,\n        prevProps,\n        nextProps: this.props,\n        instance: this.state.map\n      });\n    }\n  }\n  componentWillUnmount() {\n    if (this.state.map !== null) {\n      if (this.props.onUnmount) {\n        this.props.onUnmount(this.state.map);\n      }\n      unregisterEvents(this.registeredEvents);\n    }\n  }\n  render() {\n    return jsx(\"div\", {\n      id: this.props.id,\n      ref: this.getRef,\n      style: this.props.mapContainerStyle,\n      className: this.props.mapContainerClassName,\n      children: jsx(MapContext.Provider, {\n        value: this.state.map,\n        children: this.state.map !== null ? this.props.children : null\n      })\n    });\n  }\n}\n\nfunction asyncGeneratorStep(n, t, e, r, o, a, c) {\n  try {\n    var i = n[a](c),\n      u = i.value;\n  } catch (n) {\n    return void e(n);\n  }\n  i.done ? t(u) : Promise.resolve(u).then(r, o);\n}\nfunction _asyncToGenerator(n) {\n  return function () {\n    var t = this,\n      e = arguments;\n    return new Promise(function (r, o) {\n      var a = n.apply(t, e);\n      function _next(n) {\n        asyncGeneratorStep(a, r, o, _next, _throw, \"next\", n);\n      }\n      function _throw(n) {\n        asyncGeneratorStep(a, r, o, _next, _throw, \"throw\", n);\n      }\n      _next(void 0);\n    });\n  };\n}\n\nfunction makeLoadScriptUrl(_ref) {\n  var {\n    googleMapsApiKey,\n    googleMapsClientId,\n    version = 'weekly',\n    language,\n    region,\n    libraries,\n    channel,\n    mapIds,\n    authReferrerPolicy\n  } = _ref;\n  var params = [];\n  invariant(googleMapsApiKey && googleMapsClientId || !(googleMapsApiKey && googleMapsClientId), 'You need to specify either googleMapsApiKey or googleMapsClientId for @react-google-maps/api load script to work. You cannot use both at the same time.');\n  if (googleMapsApiKey) {\n    params.push(\"key=\".concat(googleMapsApiKey));\n  } else if (googleMapsClientId) {\n    params.push(\"client=\".concat(googleMapsClientId));\n  }\n  if (version) {\n    params.push(\"v=\".concat(version));\n  }\n  if (language) {\n    params.push(\"language=\".concat(language));\n  }\n  if (region) {\n    params.push(\"region=\".concat(region));\n  }\n  if (libraries && libraries.length) {\n    params.push(\"libraries=\".concat(libraries.sort().join(',')));\n  }\n  if (channel) {\n    params.push(\"channel=\".concat(channel));\n  }\n  if (mapIds && mapIds.length) {\n    params.push(\"map_ids=\".concat(mapIds.join(',')));\n  }\n  if (authReferrerPolicy) {\n    params.push(\"auth_referrer_policy=\".concat(authReferrerPolicy));\n  }\n  params.push('loading=async');\n  params.push('callback=initMap');\n  return \"https://maps.googleapis.com/maps/api/js?\".concat(params.join('&'));\n}\n\nvar isBrowser = typeof document !== 'undefined';\n\nfunction injectScript(_ref) {\n  var {\n    url,\n    id,\n    nonce\n    // eslint-disable-next-line @typescript-eslint/no-explicit-any\n  } = _ref;\n  if (!isBrowser) {\n    return Promise.reject(new Error('document is undefined'));\n  }\n  return new Promise(function injectScriptCallback(resolve, reject) {\n    var existingScript = document.getElementById(id);\n    var windowWithGoogleMap = window;\n    if (existingScript) {\n      // Same script id/url: keep same script\n      var dataStateAttribute = existingScript.getAttribute('data-state');\n      if (existingScript.src === url && dataStateAttribute !== 'error') {\n        if (dataStateAttribute === 'ready') {\n          return resolve(id);\n        } else {\n          var originalInitMap = windowWithGoogleMap.initMap;\n          var originalErrorCallback = existingScript.onerror;\n          windowWithGoogleMap.initMap = function initMap() {\n            if (originalInitMap) {\n              originalInitMap();\n            }\n            resolve(id);\n          };\n          existingScript.onerror = function (err) {\n            if (originalErrorCallback) {\n              originalErrorCallback(err);\n            }\n            reject(err);\n          };\n          return;\n        }\n      }\n      // Same script id, but either\n      // 1. requested URL is different\n      // 2. script failed to load\n      else {\n        existingScript.remove();\n      }\n    }\n    var script = document.createElement('script');\n    script.type = 'text/javascript';\n    script.src = url;\n    script.id = id;\n    script.async = true;\n    script.nonce = nonce || '';\n    script.onerror = function onerror(err) {\n      script.setAttribute('data-state', 'error');\n      reject(err);\n    };\n    windowWithGoogleMap.initMap = function onload() {\n      script.setAttribute('data-state', 'ready');\n      resolve(id);\n    };\n    document.head.appendChild(script);\n  }).catch(err => {\n    console.error('injectScript error: ', err);\n    throw err;\n  });\n}\n\nfunction isGoogleFontStyle(element) {\n  // 'Roboto' or 'Google Sans Text' font download\n  var href = element.href;\n  if (href && (href.indexOf('https://fonts.googleapis.com/css?family=Roboto') === 0 || href.indexOf('https://fonts.googleapis.com/css?family=Google+Sans+Text') === 0)) {\n    return true;\n  }\n  // font style elements\n  if (\n  // eslint-disable-next-line @typescript-eslint/ban-ts-comment\n  // @ts-ignore\n  element.tagName.toLowerCase() === 'style' &&\n  // eslint-disable-next-line @typescript-eslint/ban-ts-comment\n  // @ts-ignore\n  element.styleSheet &&\n  // eslint-disable-next-line @typescript-eslint/ban-ts-comment\n  // @ts-ignore\n  element.styleSheet.cssText &&\n  // eslint-disable-next-line @typescript-eslint/ban-ts-comment\n  // @ts-ignore\n  element.styleSheet.cssText.replace('\\r\\n', '').indexOf('.gm-style') === 0) {\n    // eslint-disable-next-line @typescript-eslint/ban-ts-comment\n    // @ts-ignore\n    element.styleSheet.cssText = '';\n    return true;\n  }\n  // font style elements for other browsers\n  if (\n  // eslint-disable-next-line @typescript-eslint/ban-ts-comment\n  // @ts-ignore\n  element.tagName.toLowerCase() === 'style' &&\n  // eslint-disable-next-line @typescript-eslint/ban-ts-comment\n  // @ts-ignore\n  element.innerHTML &&\n  // eslint-disable-next-line @typescript-eslint/ban-ts-comment\n  // @ts-ignore\n  element.innerHTML.replace('\\r\\n', '').indexOf('.gm-style') === 0) {\n    // eslint-disable-next-line @typescript-eslint/ban-ts-comment\n    // @ts-ignore\n    element.innerHTML = '';\n    return true;\n  }\n  // when google tries to add empty style\n  if (\n  // eslint-disable-next-line @typescript-eslint/ban-ts-comment\n  // @ts-ignore\n  element.tagName.toLowerCase() === 'style' &&\n  // eslint-disable-next-line @typescript-eslint/ban-ts-comment\n  // @ts-ignore\n  !element.styleSheet &&\n  // eslint-disable-next-line @typescript-eslint/ban-ts-comment\n  // @ts-ignore\n  !element.innerHTML) {\n    return true;\n  }\n  return false;\n}\n// Preventing the Google Maps library from downloading an extra font\nfunction preventGoogleFonts() {\n  // we override these methods only for one particular head element\n  // default methods for other elements are not affected\n  var head = document.getElementsByTagName('head')[0];\n  if (head) {\n    var trueInsertBefore = head.insertBefore.bind(head);\n    // TODO: adding return before reflect solves the TS issue\n    head.insertBefore = function insertBefore(newElement, referenceElement) {\n      if (!isGoogleFontStyle(newElement)) {\n        Reflect.apply(trueInsertBefore, head, [newElement, referenceElement]);\n      }\n      return newElement;\n    };\n    var trueAppend = head.appendChild.bind(head);\n    // TODO: adding return before reflect solves the TS issue\n    head.appendChild = function appendChild(textNode) {\n      if (!isGoogleFontStyle(textNode)) {\n        Reflect.apply(trueAppend, head, [textNode]);\n      }\n      return textNode;\n    };\n  }\n}\n\nvar cleaningUp = false;\nfunction DefaultLoadingElement() {\n  return jsx(\"div\", {\n    children: \"Loading...\"\n  });\n}\nvar defaultLoadScriptProps = {\n  id: 'script-loader',\n  version: 'weekly'\n};\nclass LoadScript extends PureComponent {\n  constructor() {\n    super(...arguments);\n    _defineProperty(this, \"check\", null);\n    _defineProperty(this, \"state\", {\n      loaded: false\n    });\n    _defineProperty(this, \"cleanupCallback\", () => {\n      // eslint-disable-next-line @typescript-eslint/ban-ts-comment\n      // @ts-ignore\n      delete window.google.maps;\n      this.injectScript();\n    });\n    _defineProperty(this, \"isCleaningUp\", /*#__PURE__*/_asyncToGenerator(function* () {\n      function promiseCallback(resolve) {\n        if (!cleaningUp) {\n          resolve();\n        } else {\n          if (isBrowser) {\n            var timer = window.setInterval(function interval() {\n              if (!cleaningUp) {\n                window.clearInterval(timer);\n                resolve();\n              }\n            }, 1);\n          }\n        }\n        return;\n      }\n      return new Promise(promiseCallback);\n    }));\n    _defineProperty(this, \"cleanup\", () => {\n      cleaningUp = true;\n      var script = document.getElementById(this.props.id);\n      if (script && script.parentNode) {\n        script.parentNode.removeChild(script);\n      }\n      Array.prototype.slice.call(document.getElementsByTagName('script')).filter(function filter(script) {\n        return typeof script.src === 'string' && script.src.includes('maps.googleapis');\n      }).forEach(function forEach(script) {\n        if (script.parentNode) {\n          script.parentNode.removeChild(script);\n        }\n      });\n      Array.prototype.slice.call(document.getElementsByTagName('link')).filter(function filter(link) {\n        return link.href === 'https://fonts.googleapis.com/css?family=Roboto:300,400,500,700|Google+Sans';\n      }).forEach(function forEach(link) {\n        if (link.parentNode) {\n          link.parentNode.removeChild(link);\n        }\n      });\n      Array.prototype.slice.call(document.getElementsByTagName('style')).filter(function filter(style) {\n        return style.innerText !== undefined && style.innerText.length > 0 && style.innerText.includes('.gm-');\n      }).forEach(function forEach(style) {\n        if (style.parentNode) {\n          style.parentNode.removeChild(style);\n        }\n      });\n    });\n    _defineProperty(this, \"injectScript\", () => {\n      if (this.props.preventGoogleFontsLoading) {\n        preventGoogleFonts();\n      }\n      invariant(!!this.props.id, 'LoadScript requires \"id\" prop to be a string: %s', this.props.id);\n      var injectScriptOptions = {\n        id: this.props.id,\n        nonce: this.props.nonce,\n        url: makeLoadScriptUrl(this.props)\n      };\n      injectScript(injectScriptOptions).then(() => {\n        if (this.props.onLoad) {\n          this.props.onLoad();\n        }\n        this.setState(function setLoaded() {\n          return {\n            loaded: true\n          };\n        });\n        return;\n      }).catch(err => {\n        if (this.props.onError) {\n          this.props.onError(err);\n        }\n        console.error(\"\\n          There has been an Error with loading Google Maps API script, please check that you provided correct google API key (\".concat(this.props.googleMapsApiKey || '-', \") or Client ID (\").concat(this.props.googleMapsClientId || '-', \") to <LoadScript />\\n          Otherwise it is a Network issue.\\n        \"));\n      });\n    });\n    _defineProperty(this, \"getRef\", el => {\n      this.check = el;\n    });\n  }\n  componentDidMount() {\n    if (isBrowser) {\n      if (window.google && window.google.maps && !cleaningUp) {\n        console.error('google api is already presented');\n        return;\n      }\n      this.isCleaningUp().then(this.injectScript).catch(function error(err) {\n        console.error('Error at injecting script after cleaning up: ', err);\n      });\n    }\n  }\n  componentDidUpdate(prevProps) {\n    if (this.props.libraries !== prevProps.libraries) {\n      console.warn('Performance warning! LoadScript has been reloaded unintentionally! You should not pass `libraries` prop as new array. Please keep an array of libraries as static class property for Components and PureComponents, or just a const variable outside of component, or somewhere in config files or ENV variables');\n    }\n    if (isBrowser && prevProps.language !== this.props.language) {\n      this.cleanup();\n      // TODO: refactor to use gDSFP maybe... wait for hooks refactoring.\n      this.setState(function setLoaded() {\n        return {\n          loaded: false\n        };\n      }, this.cleanupCallback);\n    }\n  }\n  componentWillUnmount() {\n    if (isBrowser) {\n      this.cleanup();\n      var timeoutCallback = () => {\n        if (!this.check) {\n          // eslint-disable-next-line @typescript-eslint/ban-ts-comment\n          // @ts-ignore\n          delete window.google;\n          cleaningUp = false;\n        }\n      };\n      window.setTimeout(timeoutCallback, 1);\n      if (this.props.onUnmount) {\n        this.props.onUnmount();\n      }\n    }\n  }\n  render() {\n    return jsxs(Fragment, {\n      children: [jsx(\"div\", {\n        ref: this.getRef\n      }), this.state.loaded ? this.props.children : this.props.loadingElement || jsx(DefaultLoadingElement, {})]\n    });\n  }\n}\n_defineProperty(LoadScript, \"defaultProps\", defaultLoadScriptProps);\n\nfunction _objectWithoutPropertiesLoose(r, e) {\n  if (null == r) return {};\n  var t = {};\n  for (var n in r) if ({}.hasOwnProperty.call(r, n)) {\n    if (e.includes(n)) continue;\n    t[n] = r[n];\n  }\n  return t;\n}\n\nfunction _objectWithoutProperties(e, t) {\n  if (null == e) return {};\n  var o,\n    r,\n    i = _objectWithoutPropertiesLoose(e, t);\n  if (Object.getOwnPropertySymbols) {\n    var s = Object.getOwnPropertySymbols(e);\n    for (r = 0; r < s.length; r++) o = s[r], t.includes(o) || {}.propertyIsEnumerable.call(e, o) && (i[o] = e[o]);\n  }\n  return i;\n}\n\n/* eslint-disable filenames/match-regex */\nvar previouslyLoadedUrl;\nfunction useLoadScript(_ref) {\n  var {\n    id = defaultLoadScriptProps.id,\n    version = defaultLoadScriptProps.version,\n    nonce,\n    googleMapsApiKey,\n    googleMapsClientId,\n    language,\n    region,\n    libraries,\n    preventGoogleFontsLoading,\n    channel,\n    mapIds,\n    authReferrerPolicy\n  } = _ref;\n  var isMounted = useRef(false);\n  var [isLoaded, setLoaded] = useState(false);\n  var [loadError, setLoadError] = useState(undefined);\n  useEffect(function trackMountedState() {\n    isMounted.current = true;\n    return () => {\n      isMounted.current = false;\n    };\n  }, []);\n  useEffect(function applyPreventGoogleFonts() {\n    if (isBrowser && preventGoogleFontsLoading) {\n      preventGoogleFonts();\n    }\n  }, [preventGoogleFontsLoading]);\n  useEffect(function validateLoadedState() {\n    if (isLoaded) {\n      invariant(!!window.google, 'useLoadScript was marked as loaded, but window.google is not present. Something went wrong.');\n    }\n  }, [isLoaded]);\n  var url = makeLoadScriptUrl({\n    version,\n    googleMapsApiKey,\n    googleMapsClientId,\n    language,\n    region,\n    libraries,\n    channel,\n    mapIds,\n    authReferrerPolicy\n  });\n  useEffect(function loadScriptAndModifyLoadedState() {\n    if (!isBrowser) {\n      return;\n    }\n    function setLoadedIfMounted() {\n      if (isMounted.current) {\n        setLoaded(true);\n        previouslyLoadedUrl = url;\n      }\n    }\n    if (window.google && window.google.maps && previouslyLoadedUrl === url) {\n      setLoadedIfMounted();\n      return;\n    }\n    injectScript({\n      id,\n      url,\n      nonce\n    }).then(setLoadedIfMounted).catch(function handleInjectError(err) {\n      if (isMounted.current) {\n        setLoadError(err);\n      }\n      console.warn(\"\\n        There has been an Error with loading Google Maps API script, please check that you provided correct google API key (\".concat(googleMapsApiKey || '-', \") or Client ID (\").concat(googleMapsClientId || '-', \")\\n        Otherwise it is a Network issue.\\n      \"));\n      console.error(err);\n    });\n  }, [id, url, nonce]);\n  var prevLibraries = useRef(undefined);\n  useEffect(function checkPerformance() {\n    if (prevLibraries.current && libraries !== prevLibraries.current) {\n      console.warn('Performance warning! LoadScript has been reloaded unintentionally! You should not pass `libraries` prop as new array. Please keep an array of libraries as static class property for Components and PureComponents, or just a const variable outside of component, or somewhere in config files or ENV variables');\n    }\n    prevLibraries.current = libraries;\n  }, [libraries]);\n  return {\n    isLoaded,\n    loadError,\n    url\n  };\n}\n\nvar _excluded$1 = [\"loadingElement\", \"onLoad\", \"onError\", \"onUnmount\", \"children\"];\nvar defaultLoadingElement = jsx(DefaultLoadingElement, {});\nfunction LoadScriptNext(_ref) {\n  var {\n      loadingElement,\n      onLoad,\n      onError,\n      onUnmount,\n      children\n    } = _ref,\n    hookOptions = _objectWithoutProperties(_ref, _excluded$1);\n  var {\n    isLoaded,\n    loadError\n  } = useLoadScript(hookOptions);\n  useEffect(function handleOnLoad() {\n    if (isLoaded && typeof onLoad === 'function') {\n      onLoad();\n    }\n  }, [isLoaded, onLoad]);\n  useEffect(function handleOnError() {\n    if (loadError && typeof onError === 'function') {\n      onError(loadError);\n    }\n  }, [loadError, onError]);\n  useEffect(function handleOnUnmount() {\n    return () => {\n      if (onUnmount) {\n        onUnmount();\n      }\n    };\n  }, [onUnmount]);\n  return isLoaded ? children : loadingElement || defaultLoadingElement;\n}\nvar LoadScriptNext$1 = memo(LoadScriptNext);\n\n/******************************************************************************\r\nCopyright (c) Microsoft Corporation.\r\n\r\nPermission to use, copy, modify, and/or distribute this software for any\r\npurpose with or without fee is hereby granted.\r\n\r\nTHE SOFTWARE IS PROVIDED \"AS IS\" AND THE AUTHOR DISCLAIMS ALL WARRANTIES WITH\r\nREGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF MERCHANTABILITY\r\nAND FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY SPECIAL, DIRECT,\r\nINDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES WHATSOEVER RESULTING FROM\r\nLOSS OF USE, DATA OR PROFITS, WHETHER IN AN ACTION OF CONTRACT, NEGLIGENCE OR\r\nOTHER TORTIOUS ACTION, ARISING OUT OF OR IN CONNECTION WITH THE USE OR\r\nPERFORMANCE OF THIS SOFTWARE.\r\n***************************************************************************** */\n/* global Reflect, Promise, SuppressedError, Symbol */\n\nfunction __awaiter(thisArg, _arguments, P, generator) {\n  function adopt(value) {\n    return value instanceof P ? value : new P(function (resolve) {\n      resolve(value);\n    });\n  }\n  return new (P || (P = Promise))(function (resolve, reject) {\n    function fulfilled(value) {\n      try {\n        step(generator.next(value));\n      } catch (e) {\n        reject(e);\n      }\n    }\n    function rejected(value) {\n      try {\n        step(generator[\"throw\"](value));\n      } catch (e) {\n        reject(e);\n      }\n    }\n    function step(result) {\n      result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected);\n    }\n    step((generator = generator.apply(thisArg, [])).next());\n  });\n}\ntypeof SuppressedError === \"function\" ? SuppressedError : function (error, suppressed, message) {\n  var e = new Error(message);\n  return e.name = \"SuppressedError\", e.error = error, e.suppressed = suppressed, e;\n};\nfunction getDefaultExportFromCjs(x) {\n  return x && x.__esModule && Object.prototype.hasOwnProperty.call(x, 'default') ? x['default'] : x;\n}\n\n// do not edit .js files directly - edit src/index.jst\n\nvar fastDeepEqual$1 = function equal(a, b) {\n  if (a === b) return true;\n  if (a && b && typeof a == 'object' && typeof b == 'object') {\n    if (a.constructor !== b.constructor) return false;\n    var length, i, keys;\n    if (Array.isArray(a)) {\n      length = a.length;\n      if (length != b.length) return false;\n      for (i = length; i-- !== 0;) if (!equal(a[i], b[i])) return false;\n      return true;\n    }\n    if (a.constructor === RegExp) return a.source === b.source && a.flags === b.flags;\n    if (a.valueOf !== Object.prototype.valueOf) return a.valueOf() === b.valueOf();\n    if (a.toString !== Object.prototype.toString) return a.toString() === b.toString();\n    keys = Object.keys(a);\n    length = keys.length;\n    if (length !== Object.keys(b).length) return false;\n    for (i = length; i-- !== 0;) if (!Object.prototype.hasOwnProperty.call(b, keys[i])) return false;\n    for (i = length; i-- !== 0;) {\n      var key = keys[i];\n      if (!equal(a[key], b[key])) return false;\n    }\n    return true;\n  }\n\n  // true if both NaN, false otherwise\n  return a !== a && b !== b;\n};\nvar isEqual = /*@__PURE__*/getDefaultExportFromCjs(fastDeepEqual$1);\n\n/**\n * Copyright 2019 Google LLC. All Rights Reserved.\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at.\n *\n *      Http://www.apache.org/licenses/LICENSE-2.0.\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nvar DEFAULT_ID = \"__googleMapsScriptId\";\n/**\n * The status of the [[Loader]].\n */\nvar LoaderStatus;\n(function (LoaderStatus) {\n  LoaderStatus[LoaderStatus[\"INITIALIZED\"] = 0] = \"INITIALIZED\";\n  LoaderStatus[LoaderStatus[\"LOADING\"] = 1] = \"LOADING\";\n  LoaderStatus[LoaderStatus[\"SUCCESS\"] = 2] = \"SUCCESS\";\n  LoaderStatus[LoaderStatus[\"FAILURE\"] = 3] = \"FAILURE\";\n})(LoaderStatus || (LoaderStatus = {}));\n/**\n * [[Loader]] makes it easier to add Google Maps JavaScript API to your application\n * dynamically using\n * [Promises](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Promise).\n * It works by dynamically creating and appending a script node to the the\n * document head and wrapping the callback function so as to return a promise.\n *\n * ```\n * const loader = new Loader({\n *   apiKey: \"\",\n *   version: \"weekly\",\n *   libraries: [\"places\"]\n * });\n *\n * loader.load().then((google) => {\n *   const map = new google.maps.Map(...)\n * })\n * ```\n */\nclass Loader {\n  /**\n   * Creates an instance of Loader using [[LoaderOptions]]. No defaults are set\n   * using this library, instead the defaults are set by the Google Maps\n   * JavaScript API server.\n   *\n   * ```\n   * const loader = Loader({apiKey, version: 'weekly', libraries: ['places']});\n   * ```\n   */\n  constructor(_ref) {\n    var {\n      apiKey,\n      authReferrerPolicy,\n      channel,\n      client,\n      id = DEFAULT_ID,\n      language,\n      libraries = [],\n      mapIds,\n      nonce,\n      region,\n      retries = 3,\n      url = \"https://maps.googleapis.com/maps/api/js\",\n      version\n    } = _ref;\n    this.callbacks = [];\n    this.done = false;\n    this.loading = false;\n    this.errors = [];\n    this.apiKey = apiKey;\n    this.authReferrerPolicy = authReferrerPolicy;\n    this.channel = channel;\n    this.client = client;\n    this.id = id || DEFAULT_ID; // Do not allow empty string\n    this.language = language;\n    this.libraries = libraries;\n    this.mapIds = mapIds;\n    this.nonce = nonce;\n    this.region = region;\n    this.retries = retries;\n    this.url = url;\n    this.version = version;\n    if (Loader.instance) {\n      if (!isEqual(this.options, Loader.instance.options)) {\n        throw new Error(\"Loader must not be called again with different options. \".concat(JSON.stringify(this.options), \" !== \").concat(JSON.stringify(Loader.instance.options)));\n      }\n      return Loader.instance;\n    }\n    Loader.instance = this;\n  }\n  get options() {\n    return {\n      version: this.version,\n      apiKey: this.apiKey,\n      channel: this.channel,\n      client: this.client,\n      id: this.id,\n      libraries: this.libraries,\n      language: this.language,\n      region: this.region,\n      mapIds: this.mapIds,\n      nonce: this.nonce,\n      url: this.url,\n      authReferrerPolicy: this.authReferrerPolicy\n    };\n  }\n  get status() {\n    if (this.errors.length) {\n      return LoaderStatus.FAILURE;\n    }\n    if (this.done) {\n      return LoaderStatus.SUCCESS;\n    }\n    if (this.loading) {\n      return LoaderStatus.LOADING;\n    }\n    return LoaderStatus.INITIALIZED;\n  }\n  get failed() {\n    return this.done && !this.loading && this.errors.length >= this.retries + 1;\n  }\n  /**\n   * CreateUrl returns the Google Maps JavaScript API script url given the [[LoaderOptions]].\n   *\n   * @ignore\n   * @deprecated\n   */\n  createUrl() {\n    var url = this.url;\n    url += \"?callback=__googleMapsCallback&loading=async\";\n    if (this.apiKey) {\n      url += \"&key=\".concat(this.apiKey);\n    }\n    if (this.channel) {\n      url += \"&channel=\".concat(this.channel);\n    }\n    if (this.client) {\n      url += \"&client=\".concat(this.client);\n    }\n    if (this.libraries.length > 0) {\n      url += \"&libraries=\".concat(this.libraries.join(\",\"));\n    }\n    if (this.language) {\n      url += \"&language=\".concat(this.language);\n    }\n    if (this.region) {\n      url += \"&region=\".concat(this.region);\n    }\n    if (this.version) {\n      url += \"&v=\".concat(this.version);\n    }\n    if (this.mapIds) {\n      url += \"&map_ids=\".concat(this.mapIds.join(\",\"));\n    }\n    if (this.authReferrerPolicy) {\n      url += \"&auth_referrer_policy=\".concat(this.authReferrerPolicy);\n    }\n    return url;\n  }\n  deleteScript() {\n    var script = document.getElementById(this.id);\n    if (script) {\n      script.remove();\n    }\n  }\n  /**\n   * Load the Google Maps JavaScript API script and return a Promise.\n   * @deprecated, use importLibrary() instead.\n   */\n  load() {\n    return this.loadPromise();\n  }\n  /**\n   * Load the Google Maps JavaScript API script and return a Promise.\n   *\n   * @ignore\n   * @deprecated, use importLibrary() instead.\n   */\n  loadPromise() {\n    return new Promise((resolve, reject) => {\n      this.loadCallback(err => {\n        if (!err) {\n          resolve(window.google);\n        } else {\n          reject(err.error);\n        }\n      });\n    });\n  }\n  importLibrary(name) {\n    this.execute();\n    return google.maps.importLibrary(name);\n  }\n  /**\n   * Load the Google Maps JavaScript API script with a callback.\n   * @deprecated, use importLibrary() instead.\n   */\n  loadCallback(fn) {\n    this.callbacks.push(fn);\n    this.execute();\n  }\n  /**\n   * Set the script on document.\n   */\n  setScript() {\n    var _a, _b;\n    if (document.getElementById(this.id)) {\n      // TODO wrap onerror callback for cases where the script was loaded elsewhere\n      this.callback();\n      return;\n    }\n    var params = {\n      key: this.apiKey,\n      channel: this.channel,\n      client: this.client,\n      libraries: this.libraries.length && this.libraries,\n      v: this.version,\n      mapIds: this.mapIds,\n      language: this.language,\n      region: this.region,\n      authReferrerPolicy: this.authReferrerPolicy\n    };\n    // keep the URL minimal:\n    Object.keys(params).forEach(\n    // eslint-disable-next-line @typescript-eslint/no-explicit-any\n    key => !params[key] && delete params[key]);\n    if (!((_b = (_a = window === null || window === void 0 ? void 0 : window.google) === null || _a === void 0 ? void 0 : _a.maps) === null || _b === void 0 ? void 0 : _b.importLibrary)) {\n      // tweaked copy of https://developers.google.com/maps/documentation/javascript/load-maps-js-api#dynamic-library-import\n      // which also sets the base url, the id, and the nonce\n      /* eslint-disable */\n      (g => {\n        // @ts-ignore\n        var h,\n          a,\n          k,\n          p = \"The Google Maps JavaScript API\",\n          c = \"google\",\n          l = \"importLibrary\",\n          q = \"__ib__\",\n          m = document,\n          b = window;\n        // @ts-ignore\n        b = b[c] || (b[c] = {});\n        // @ts-ignore\n        var d = b.maps || (b.maps = {}),\n          r = new Set(),\n          e = new URLSearchParams(),\n          u = () =>\n          // @ts-ignore\n          h || (h = new Promise((f, n) => __awaiter(this, void 0, void 0, function* () {\n            var _a;\n            yield a = m.createElement(\"script\");\n            a.id = this.id;\n            e.set(\"libraries\", [...r] + \"\");\n            // @ts-ignore\n            for (k in g) e.set(k.replace(/[A-Z]/g, t => \"_\" + t[0].toLowerCase()), g[k]);\n            e.set(\"callback\", c + \".maps.\" + q);\n            a.src = this.url + \"?\" + e;\n            d[q] = f;\n            a.onerror = () => h = n(Error(p + \" could not load.\"));\n            // @ts-ignore\n            a.nonce = this.nonce || ((_a = m.querySelector(\"script[nonce]\")) === null || _a === void 0 ? void 0 : _a.nonce) || \"\";\n            m.head.append(a);\n          })));\n        // @ts-ignore\n        d[l] ? console.warn(p + \" only loads once. Ignoring:\", g) : d[l] = function (f) {\n          for (var _len = arguments.length, n = new Array(_len > 1 ? _len - 1 : 0), _key = 1; _key < _len; _key++) {\n            n[_key - 1] = arguments[_key];\n          }\n          return r.add(f) && u().then(() => d[l](f, ...n));\n        };\n      })(params);\n      /* eslint-enable */\n    }\n    // While most libraries populate the global namespace when loaded via bootstrap params,\n    // this is not the case for \"marker\" when used with the inline bootstrap loader\n    // (and maybe others in the future). So ensure there is an importLibrary for each:\n    var libraryPromises = this.libraries.map(library => this.importLibrary(library));\n    // ensure at least one library, to kick off loading...\n    if (!libraryPromises.length) {\n      libraryPromises.push(this.importLibrary(\"core\"));\n    }\n    Promise.all(libraryPromises).then(() => this.callback(), error => {\n      var event = new ErrorEvent(\"error\", {\n        error\n      }); // for backwards compat\n      this.loadErrorCallback(event);\n    });\n  }\n  /**\n   * Reset the loader state.\n   */\n  reset() {\n    this.deleteScript();\n    this.done = false;\n    this.loading = false;\n    this.errors = [];\n    this.onerrorEvent = null;\n  }\n  resetIfRetryingFailed() {\n    if (this.failed) {\n      this.reset();\n    }\n  }\n  loadErrorCallback(e) {\n    this.errors.push(e);\n    if (this.errors.length <= this.retries) {\n      var delay = this.errors.length * Math.pow(2, this.errors.length);\n      console.error(\"Failed to load Google Maps script, retrying in \".concat(delay, \" ms.\"));\n      setTimeout(() => {\n        this.deleteScript();\n        this.setScript();\n      }, delay);\n    } else {\n      this.onerrorEvent = e;\n      this.callback();\n    }\n  }\n  callback() {\n    this.done = true;\n    this.loading = false;\n    this.callbacks.forEach(cb => {\n      cb(this.onerrorEvent);\n    });\n    this.callbacks = [];\n  }\n  execute() {\n    this.resetIfRetryingFailed();\n    if (this.loading) {\n      // do nothing but wait\n      return;\n    }\n    if (this.done) {\n      this.callback();\n    } else {\n      // short circuit and warn if google.maps is already loaded\n      if (window.google && window.google.maps && window.google.maps.version) {\n        console.warn(\"Google Maps already loaded outside @googlemaps/js-api-loader. \" + \"This may result in undesirable behavior as options and script parameters may not match.\");\n        this.callback();\n        return;\n      }\n      this.loading = true;\n      this.setScript();\n    }\n  }\n}\n\nvar defaultLibraries = ['maps'];\nfunction useJsApiLoader(_ref) {\n  var {\n    id = defaultLoadScriptProps.id,\n    version = defaultLoadScriptProps.version,\n    nonce,\n    googleMapsApiKey,\n    // googleMapsClientId,\n    language,\n    region,\n    libraries = defaultLibraries,\n    preventGoogleFontsLoading,\n    // channel,\n    mapIds,\n    authReferrerPolicy\n  } = _ref;\n  var isMounted = useRef(false);\n  var [isLoaded, setLoaded] = useState(false);\n  var [loadError, setLoadError] = useState(undefined);\n  useEffect(function trackMountedState() {\n    isMounted.current = true;\n    return () => {\n      isMounted.current = false;\n    };\n  }, []);\n  var loader = useMemo(() => {\n    return new Loader({\n      id,\n      apiKey: googleMapsApiKey,\n      version,\n      libraries,\n      language: language || 'en',\n      region: region || 'US',\n      mapIds: mapIds || [],\n      nonce: nonce || '',\n      authReferrerPolicy: authReferrerPolicy || 'origin'\n    });\n  }, [id, googleMapsApiKey, version, libraries, language, region, mapIds, nonce, authReferrerPolicy]);\n  useEffect(function effect() {\n    if (isLoaded) {\n      return;\n    } else {\n      loader.load().then(() => {\n        if (isMounted.current) {\n          setLoaded(true);\n        }\n        return;\n      }).catch(error => {\n        setLoadError(error);\n      });\n    }\n  }, []);\n  useEffect(() => {\n    if (isBrowser && preventGoogleFontsLoading) {\n      preventGoogleFonts();\n    }\n  }, [preventGoogleFontsLoading]);\n  var prevLibraries = useRef();\n  useEffect(() => {\n    if (prevLibraries.current && libraries !== prevLibraries.current) {\n      console.warn('Performance warning! LoadScript has been reloaded unintentionally! You should not pass `libraries` prop as new array. Please keep an array of libraries as static class property for Components and PureComponents, or just a const variable outside of component, or somewhere in config files or ENV variables');\n    }\n    prevLibraries.current = libraries;\n  }, [libraries]);\n  return {\n    isLoaded,\n    loadError\n  };\n}\n\nfunction ownKeys$f(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }\nfunction _objectSpread$f(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys$f(Object(t), !0).forEach(function (r) { _defineProperty(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys$f(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }\nvar eventMap$h = {};\nvar updaterMap$h = {\n  options(instance, options) {\n    instance.setOptions(options);\n  }\n};\nfunction TrafficLayerFunctional(_ref) {\n  var {\n    options,\n    onLoad,\n    onUnmount\n  } = _ref;\n  var map = useContext(MapContext);\n  var [instance, setInstance] = useState(null);\n  // Order does matter\n  useEffect(() => {\n    if (instance !== null) {\n      instance.setMap(map);\n    }\n  }, [map]);\n  useEffect(() => {\n    if (options && instance !== null) {\n      instance.setOptions(options);\n    }\n  }, [instance, options]);\n  useEffect(() => {\n    var trafficLayer = new google.maps.TrafficLayer(_objectSpread$f(_objectSpread$f({}, options), {}, {\n      map\n    }));\n    setInstance(trafficLayer);\n    if (onLoad) {\n      onLoad(trafficLayer);\n    }\n    return () => {\n      if (instance !== null) {\n        if (onUnmount) {\n          onUnmount(instance);\n        }\n        instance.setMap(null);\n      }\n    };\n  }, []);\n  return null;\n}\nvar TrafficLayerF = memo(TrafficLayerFunctional);\nclass TrafficLayer extends PureComponent {\n  constructor() {\n    super(...arguments);\n    _defineProperty(this, \"state\", {\n      trafficLayer: null\n    });\n    _defineProperty(this, \"setTrafficLayerCallback\", () => {\n      if (this.state.trafficLayer !== null && this.props.onLoad) {\n        this.props.onLoad(this.state.trafficLayer);\n      }\n    });\n    _defineProperty(this, \"registeredEvents\", []);\n  }\n  componentDidMount() {\n    var trafficLayer = new google.maps.TrafficLayer(_objectSpread$f(_objectSpread$f({}, this.props.options), {}, {\n      map: this.context\n    }));\n    this.registeredEvents = applyUpdatersToPropsAndRegisterEvents({\n      updaterMap: updaterMap$h,\n      eventMap: eventMap$h,\n      prevProps: {},\n      nextProps: this.props,\n      instance: trafficLayer\n    });\n    this.setState(function setTrafficLayer() {\n      return {\n        trafficLayer\n      };\n    }, this.setTrafficLayerCallback);\n  }\n  componentDidUpdate(prevProps) {\n    if (this.state.trafficLayer !== null) {\n      unregisterEvents(this.registeredEvents);\n      this.registeredEvents = applyUpdatersToPropsAndRegisterEvents({\n        updaterMap: updaterMap$h,\n        eventMap: eventMap$h,\n        prevProps,\n        nextProps: this.props,\n        instance: this.state.trafficLayer\n      });\n    }\n  }\n  componentWillUnmount() {\n    if (this.state.trafficLayer !== null) {\n      if (this.props.onUnmount) {\n        this.props.onUnmount(this.state.trafficLayer);\n      }\n      unregisterEvents(this.registeredEvents);\n      this.state.trafficLayer.setMap(null);\n    }\n  }\n  render() {\n    return null;\n  }\n}\n_defineProperty(TrafficLayer, \"contextType\", MapContext);\n\nfunction BicyclingLayerFunctional(_ref) {\n  var {\n    onLoad,\n    onUnmount\n  } = _ref;\n  var map = useContext(MapContext);\n  var [instance, setInstance] = useState(null);\n  // Order does matter\n  useEffect(() => {\n    if (instance !== null) {\n      instance.setMap(map);\n    }\n  }, [map]);\n  useEffect(() => {\n    var bicyclingLayer = new google.maps.BicyclingLayer();\n    setInstance(bicyclingLayer);\n    bicyclingLayer.setMap(map);\n    if (onLoad) {\n      onLoad(bicyclingLayer);\n    }\n    return () => {\n      if (bicyclingLayer !== null) {\n        if (onUnmount) {\n          onUnmount(bicyclingLayer);\n        }\n        bicyclingLayer.setMap(null);\n      }\n    };\n  }, []);\n  return null;\n}\nvar BicyclingLayerF = memo(BicyclingLayerFunctional);\nclass BicyclingLayer extends PureComponent {\n  constructor() {\n    super(...arguments);\n    _defineProperty(this, \"state\", {\n      bicyclingLayer: null\n    });\n    _defineProperty(this, \"setBicyclingLayerCallback\", () => {\n      if (this.state.bicyclingLayer !== null) {\n        this.state.bicyclingLayer.setMap(this.context);\n        if (this.props.onLoad) {\n          this.props.onLoad(this.state.bicyclingLayer);\n        }\n      }\n    });\n  }\n  componentDidMount() {\n    var bicyclingLayer = new google.maps.BicyclingLayer();\n    this.setState(() => {\n      return {\n        bicyclingLayer\n      };\n    }, this.setBicyclingLayerCallback);\n  }\n  componentWillUnmount() {\n    if (this.state.bicyclingLayer !== null) {\n      if (this.props.onUnmount) {\n        this.props.onUnmount(this.state.bicyclingLayer);\n      }\n      this.state.bicyclingLayer.setMap(null);\n    }\n  }\n  render() {\n    return null;\n  }\n}\n_defineProperty(BicyclingLayer, \"contextType\", MapContext);\n\nfunction TransitLayerFunctional(_ref) {\n  var {\n    onLoad,\n    onUnmount\n  } = _ref;\n  var map = useContext(MapContext);\n  var [instance, setInstance] = useState(null);\n  // Order does matter\n  useEffect(() => {\n    if (instance !== null) {\n      instance.setMap(map);\n    }\n  }, [map]);\n  useEffect(() => {\n    var transitLayer = new google.maps.TransitLayer();\n    setInstance(transitLayer);\n    transitLayer.setMap(map);\n    if (onLoad) {\n      onLoad(transitLayer);\n    }\n    return () => {\n      if (instance !== null) {\n        if (onUnmount) {\n          onUnmount(instance);\n        }\n        instance.setMap(null);\n      }\n    };\n  }, []);\n  return null;\n}\nvar TransitLayerF = memo(TransitLayerFunctional);\nclass TransitLayer extends PureComponent {\n  constructor() {\n    super(...arguments);\n    _defineProperty(this, \"state\", {\n      transitLayer: null\n    });\n    _defineProperty(this, \"setTransitLayerCallback\", () => {\n      if (this.state.transitLayer !== null) {\n        this.state.transitLayer.setMap(this.context);\n        if (this.props.onLoad) {\n          this.props.onLoad(this.state.transitLayer);\n        }\n      }\n    });\n  }\n  componentDidMount() {\n    var transitLayer = new google.maps.TransitLayer();\n    this.setState(function setTransitLayer() {\n      return {\n        transitLayer\n      };\n    }, this.setTransitLayerCallback);\n  }\n  componentWillUnmount() {\n    if (this.state.transitLayer !== null) {\n      if (this.props.onUnmount) {\n        this.props.onUnmount(this.state.transitLayer);\n      }\n      this.state.transitLayer.setMap(null);\n    }\n  }\n  render() {\n    return null;\n  }\n}\n_defineProperty(TransitLayer, \"contextType\", MapContext);\n\nfunction ownKeys$e(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }\nfunction _objectSpread$e(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys$e(Object(t), !0).forEach(function (r) { _defineProperty(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys$e(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }\nvar eventMap$g = {\n  onCircleComplete: 'circlecomplete',\n  onMarkerComplete: 'markercomplete',\n  onOverlayComplete: 'overlaycomplete',\n  onPolygonComplete: 'polygoncomplete',\n  onPolylineComplete: 'polylinecomplete',\n  onRectangleComplete: 'rectanglecomplete'\n};\nvar updaterMap$g = {\n  drawingMode(instance, drawingMode) {\n    instance.setDrawingMode(drawingMode);\n  },\n  options(instance, options) {\n    instance.setOptions(options);\n  }\n};\nfunction DrawingManagerFunctional(_ref) {\n  var {\n    options,\n    drawingMode,\n    onCircleComplete,\n    onMarkerComplete,\n    onOverlayComplete,\n    onPolygonComplete,\n    onPolylineComplete,\n    onRectangleComplete,\n    onLoad,\n    onUnmount\n  } = _ref;\n  var map = useContext(MapContext);\n  var [instance, setInstance] = useState(null);\n  var [circlecompleteListener, setCircleCompleteListener] = useState(null);\n  var [markercompleteListener, setMarkerCompleteListener] = useState(null);\n  var [overlaycompleteListener, setOverlayCompleteListener] = useState(null);\n  var [polygoncompleteListener, setPolygonCompleteListener] = useState(null);\n  var [polylinecompleteListener, setPolylineCompleteListener] = useState(null);\n  var [rectanglecompleteListener, setRectangleCompleteListener] = useState(null);\n  // Order does matter\n  useEffect(() => {\n    if (instance !== null) {\n      instance.setMap(map);\n    }\n  }, [map]);\n  useEffect(() => {\n    if (options && instance !== null) {\n      instance.setOptions(options);\n    }\n  }, [instance, options]);\n  useEffect(() => {\n    if (instance !== null) {\n      instance.setDrawingMode(drawingMode !== null && drawingMode !== void 0 ? drawingMode : null);\n    }\n  }, [instance, drawingMode]);\n  useEffect(() => {\n    if (instance && onCircleComplete) {\n      if (circlecompleteListener !== null) {\n        google.maps.event.removeListener(circlecompleteListener);\n      }\n      setCircleCompleteListener(google.maps.event.addListener(instance, 'circlecomplete', onCircleComplete));\n    }\n  }, [instance, onCircleComplete]);\n  useEffect(() => {\n    if (instance && onMarkerComplete) {\n      if (markercompleteListener !== null) {\n        google.maps.event.removeListener(markercompleteListener);\n      }\n      setMarkerCompleteListener(google.maps.event.addListener(instance, 'markercomplete', onMarkerComplete));\n    }\n  }, [instance, onMarkerComplete]);\n  useEffect(() => {\n    if (instance && onOverlayComplete) {\n      if (overlaycompleteListener !== null) {\n        google.maps.event.removeListener(overlaycompleteListener);\n      }\n      setOverlayCompleteListener(google.maps.event.addListener(instance, 'overlaycomplete', onOverlayComplete));\n    }\n  }, [instance, onOverlayComplete]);\n  useEffect(() => {\n    if (instance && onPolygonComplete) {\n      if (polygoncompleteListener !== null) {\n        google.maps.event.removeListener(polygoncompleteListener);\n      }\n      setPolygonCompleteListener(google.maps.event.addListener(instance, 'polygoncomplete', onPolygonComplete));\n    }\n  }, [instance, onPolygonComplete]);\n  useEffect(() => {\n    if (instance && onPolylineComplete) {\n      if (polylinecompleteListener !== null) {\n        google.maps.event.removeListener(polylinecompleteListener);\n      }\n      setPolylineCompleteListener(google.maps.event.addListener(instance, 'polylinecomplete', onPolylineComplete));\n    }\n  }, [instance, onPolylineComplete]);\n  useEffect(() => {\n    if (instance && onRectangleComplete) {\n      if (rectanglecompleteListener !== null) {\n        google.maps.event.removeListener(rectanglecompleteListener);\n      }\n      setRectangleCompleteListener(google.maps.event.addListener(instance, 'rectanglecomplete', onRectangleComplete));\n    }\n  }, [instance, onRectangleComplete]);\n  useEffect(() => {\n    invariant(!!google.maps.drawing, \"Did you include prop libraries={['drawing']} in the URL? %s\", google.maps.drawing);\n    var drawingManager = new google.maps.drawing.DrawingManager(_objectSpread$e(_objectSpread$e({}, options), {}, {\n      map\n    }));\n    if (drawingMode) {\n      drawingManager.setDrawingMode(drawingMode);\n    }\n    if (onCircleComplete) {\n      setCircleCompleteListener(google.maps.event.addListener(drawingManager, 'circlecomplete', onCircleComplete));\n    }\n    if (onMarkerComplete) {\n      setMarkerCompleteListener(google.maps.event.addListener(drawingManager, 'markercomplete', onMarkerComplete));\n    }\n    if (onOverlayComplete) {\n      setOverlayCompleteListener(google.maps.event.addListener(drawingManager, 'overlaycomplete', onOverlayComplete));\n    }\n    if (onPolygonComplete) {\n      setPolygonCompleteListener(google.maps.event.addListener(drawingManager, 'polygoncomplete', onPolygonComplete));\n    }\n    if (onPolylineComplete) {\n      setPolylineCompleteListener(google.maps.event.addListener(drawingManager, 'polylinecomplete', onPolylineComplete));\n    }\n    if (onRectangleComplete) {\n      setRectangleCompleteListener(google.maps.event.addListener(drawingManager, 'rectanglecomplete', onRectangleComplete));\n    }\n    setInstance(drawingManager);\n    if (onLoad) {\n      onLoad(drawingManager);\n    }\n    return () => {\n      if (instance !== null) {\n        if (circlecompleteListener) {\n          google.maps.event.removeListener(circlecompleteListener);\n        }\n        if (markercompleteListener) {\n          google.maps.event.removeListener(markercompleteListener);\n        }\n        if (overlaycompleteListener) {\n          google.maps.event.removeListener(overlaycompleteListener);\n        }\n        if (polygoncompleteListener) {\n          google.maps.event.removeListener(polygoncompleteListener);\n        }\n        if (polylinecompleteListener) {\n          google.maps.event.removeListener(polylinecompleteListener);\n        }\n        if (rectanglecompleteListener) {\n          google.maps.event.removeListener(rectanglecompleteListener);\n        }\n        if (onUnmount) {\n          onUnmount(instance);\n        }\n        instance.setMap(null);\n      }\n    };\n  }, []);\n  return null;\n}\nvar DrawingManagerF = memo(DrawingManagerFunctional);\nclass DrawingManager extends PureComponent {\n  constructor(props) {\n    super(props);\n    _defineProperty(this, \"registeredEvents\", []);\n    _defineProperty(this, \"state\", {\n      drawingManager: null\n    });\n    _defineProperty(this, \"setDrawingManagerCallback\", () => {\n      if (this.state.drawingManager !== null && this.props.onLoad) {\n        this.props.onLoad(this.state.drawingManager);\n      }\n    });\n    invariant(!!google.maps.drawing, \"Did you include prop libraries={['drawing']} in the URL? %s\", google.maps.drawing);\n  }\n  componentDidMount() {\n    var drawingManager = new google.maps.drawing.DrawingManager(_objectSpread$e(_objectSpread$e({}, this.props.options), {}, {\n      map: this.context\n    }));\n    this.registeredEvents = applyUpdatersToPropsAndRegisterEvents({\n      updaterMap: updaterMap$g,\n      eventMap: eventMap$g,\n      prevProps: {},\n      nextProps: this.props,\n      instance: drawingManager\n    });\n    this.setState(function setDrawingManager() {\n      return {\n        drawingManager\n      };\n    }, this.setDrawingManagerCallback);\n  }\n  componentDidUpdate(prevProps) {\n    if (this.state.drawingManager !== null) {\n      unregisterEvents(this.registeredEvents);\n      this.registeredEvents = applyUpdatersToPropsAndRegisterEvents({\n        updaterMap: updaterMap$g,\n        eventMap: eventMap$g,\n        prevProps,\n        nextProps: this.props,\n        instance: this.state.drawingManager\n      });\n    }\n  }\n  componentWillUnmount() {\n    if (this.state.drawingManager !== null) {\n      if (this.props.onUnmount) {\n        this.props.onUnmount(this.state.drawingManager);\n      }\n      unregisterEvents(this.registeredEvents);\n      this.state.drawingManager.setMap(null);\n    }\n  }\n  render() {\n    return null;\n  }\n}\n_defineProperty(DrawingManager, \"contextType\", MapContext);\n\nfunction ownKeys$d(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }\nfunction _objectSpread$d(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys$d(Object(t), !0).forEach(function (r) { _defineProperty(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys$d(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }\nvar eventMap$f = {\n  onAnimationChanged: 'animation_changed',\n  onClick: 'click',\n  onClickableChanged: 'clickable_changed',\n  onCursorChanged: 'cursor_changed',\n  onDblClick: 'dblclick',\n  onDrag: 'drag',\n  onDragEnd: 'dragend',\n  onDraggableChanged: 'draggable_changed',\n  onDragStart: 'dragstart',\n  onFlatChanged: 'flat_changed',\n  onIconChanged: 'icon_changed',\n  onMouseDown: 'mousedown',\n  onMouseOut: 'mouseout',\n  onMouseOver: 'mouseover',\n  onMouseUp: 'mouseup',\n  onPositionChanged: 'position_changed',\n  onRightClick: 'rightclick',\n  onShapeChanged: 'shape_changed',\n  onTitleChanged: 'title_changed',\n  onVisibleChanged: 'visible_changed',\n  onZindexChanged: 'zindex_changed'\n};\nvar updaterMap$f = {\n  animation(instance, animation) {\n    instance.setAnimation(animation);\n  },\n  clickable(instance, clickable) {\n    instance.setClickable(clickable);\n  },\n  cursor(instance, cursor) {\n    instance.setCursor(cursor);\n  },\n  draggable(instance, draggable) {\n    instance.setDraggable(draggable);\n  },\n  icon(instance, icon) {\n    instance.setIcon(icon);\n  },\n  label(instance, label) {\n    instance.setLabel(label);\n  },\n  map(instance, map) {\n    instance.setMap(map);\n  },\n  opacity(instance, opacity) {\n    instance.setOpacity(opacity);\n  },\n  options(instance, options) {\n    instance.setOptions(options);\n  },\n  position(instance, position) {\n    instance.setPosition(position);\n  },\n  shape(instance, shape) {\n    instance.setShape(shape);\n  },\n  title(instance, title) {\n    instance.setTitle(title);\n  },\n  visible(instance, visible) {\n    instance.setVisible(visible);\n  },\n  zIndex(instance, zIndex) {\n    instance.setZIndex(zIndex);\n  }\n};\nvar defaultOptions$5 = {};\nfunction MarkerFunctional(_ref) {\n  var {\n    position,\n    options,\n    clusterer,\n    noClustererRedraw,\n    children,\n    draggable,\n    visible,\n    animation,\n    clickable,\n    cursor,\n    icon,\n    label,\n    opacity,\n    shape,\n    title,\n    zIndex,\n    onClick,\n    onDblClick,\n    onDrag,\n    onDragEnd,\n    onDragStart,\n    onMouseOut,\n    onMouseOver,\n    onMouseUp,\n    onMouseDown,\n    onRightClick,\n    onClickableChanged,\n    onCursorChanged,\n    onAnimationChanged,\n    onDraggableChanged,\n    onFlatChanged,\n    onIconChanged,\n    onPositionChanged,\n    onShapeChanged,\n    onTitleChanged,\n    onVisibleChanged,\n    onZindexChanged,\n    onLoad,\n    onUnmount\n  } = _ref;\n  var map = useContext(MapContext);\n  var [instance, setInstance] = useState(null);\n  var [dblclickListener, setDblclickListener] = useState(null);\n  var [dragendListener, setDragendListener] = useState(null);\n  var [dragstartListener, setDragstartListener] = useState(null);\n  var [mousedownListener, setMousedownListener] = useState(null);\n  var [mouseoutListener, setMouseoutListener] = useState(null);\n  var [mouseoverListener, setMouseoverListener] = useState(null);\n  var [mouseupListener, setMouseupListener] = useState(null);\n  var [rightclickListener, setRightclickListener] = useState(null);\n  var [clickListener, setClickListener] = useState(null);\n  var [dragListener, setDragListener] = useState(null);\n  var [clickableChangedListener, setClickableChangedListener] = useState(null);\n  var [cursorChangedListener, setCursorChangedListener] = useState(null);\n  var [animationChangedListener, setAnimationChangedListener] = useState(null);\n  var [draggableChangedListener, setDraggableChangedListener] = useState(null);\n  var [flatChangedListener, setFlatChangedListener] = useState(null);\n  var [iconChangedListener, setIconChangedListener] = useState(null);\n  var [positionChangedListener, setPositionChangedListener] = useState(null);\n  var [shapeChangedListener, setShapeChangedListener] = useState(null);\n  var [titleChangedListener, setTitleChangedListener] = useState(null);\n  var [visibleChangedListener, setVisibleChangedListener] = useState(null);\n  var [zIndexChangedListener, setZindexChangedListener] = useState(null);\n  // Order does matter\n  useEffect(() => {\n    if (instance !== null) {\n      instance.setMap(map);\n    }\n  }, [map]);\n  useEffect(() => {\n    if (typeof options !== 'undefined' && instance !== null) {\n      instance.setOptions(options);\n    }\n  }, [instance, options]);\n  useEffect(() => {\n    if (typeof draggable !== 'undefined' && instance !== null) {\n      instance.setDraggable(draggable);\n    }\n  }, [instance, draggable]);\n  useEffect(() => {\n    if (position && instance !== null) {\n      instance.setPosition(position);\n    }\n  }, [instance, position]);\n  useEffect(() => {\n    if (typeof visible !== 'undefined' && instance !== null) {\n      instance.setVisible(visible);\n    }\n  }, [instance, visible]);\n  useEffect(() => {\n    instance === null || instance === void 0 || instance.setAnimation(animation);\n  }, [instance, animation]);\n  useEffect(() => {\n    if (instance && clickable !== undefined) {\n      instance.setClickable(clickable);\n    }\n  }, [instance, clickable]);\n  useEffect(() => {\n    if (instance && cursor !== undefined) {\n      instance.setCursor(cursor);\n    }\n  }, [instance, cursor]);\n  useEffect(() => {\n    if (instance && icon !== undefined) {\n      instance.setIcon(icon);\n    }\n  }, [instance, icon]);\n  useEffect(() => {\n    if (instance && label !== undefined) {\n      instance.setLabel(label);\n    }\n  }, [instance, label]);\n  useEffect(() => {\n    if (instance && opacity !== undefined) {\n      instance.setOpacity(opacity);\n    }\n  }, [instance, opacity]);\n  useEffect(() => {\n    if (instance && shape !== undefined) {\n      instance.setShape(shape);\n    }\n  }, [instance, shape]);\n  useEffect(() => {\n    if (instance && title !== undefined) {\n      instance.setTitle(title);\n    }\n  }, [instance, title]);\n  useEffect(() => {\n    if (instance && zIndex !== undefined) {\n      instance.setZIndex(zIndex);\n    }\n  }, [instance, zIndex]);\n  useEffect(() => {\n    if (instance && onDblClick) {\n      if (dblclickListener !== null) {\n        google.maps.event.removeListener(dblclickListener);\n      }\n      setDblclickListener(google.maps.event.addListener(instance, 'dblclick', onDblClick));\n    }\n  }, [onDblClick]);\n  useEffect(() => {\n    if (instance && onDragEnd) {\n      if (dragendListener !== null) {\n        google.maps.event.removeListener(dragendListener);\n      }\n      setDragendListener(google.maps.event.addListener(instance, 'dragend', onDragEnd));\n    }\n  }, [onDragEnd]);\n  useEffect(() => {\n    if (instance && onDragStart) {\n      if (dragstartListener !== null) {\n        google.maps.event.removeListener(dragstartListener);\n      }\n      setDragstartListener(google.maps.event.addListener(instance, 'dragstart', onDragStart));\n    }\n  }, [onDragStart]);\n  useEffect(() => {\n    if (instance && onMouseDown) {\n      if (mousedownListener !== null) {\n        google.maps.event.removeListener(mousedownListener);\n      }\n      setMousedownListener(google.maps.event.addListener(instance, 'mousedown', onMouseDown));\n    }\n  }, [onMouseDown]);\n  useEffect(() => {\n    if (instance && onMouseOut) {\n      if (mouseoutListener !== null) {\n        google.maps.event.removeListener(mouseoutListener);\n      }\n      setMouseoutListener(google.maps.event.addListener(instance, 'mouseout', onMouseOut));\n    }\n  }, [onMouseOut]);\n  useEffect(() => {\n    if (instance && onMouseOver) {\n      if (mouseoverListener !== null) {\n        google.maps.event.removeListener(mouseoverListener);\n      }\n      setMouseoverListener(google.maps.event.addListener(instance, 'mouseover', onMouseOver));\n    }\n  }, [onMouseOver]);\n  useEffect(() => {\n    if (instance && onMouseUp) {\n      if (mouseupListener !== null) {\n        google.maps.event.removeListener(mouseupListener);\n      }\n      setMouseupListener(google.maps.event.addListener(instance, 'mouseup', onMouseUp));\n    }\n  }, [onMouseUp]);\n  useEffect(() => {\n    if (instance && onRightClick) {\n      if (rightclickListener !== null) {\n        google.maps.event.removeListener(rightclickListener);\n      }\n      setRightclickListener(google.maps.event.addListener(instance, 'rightclick', onRightClick));\n    }\n  }, [onRightClick]);\n  useEffect(() => {\n    if (instance && onClick) {\n      if (clickListener !== null) {\n        google.maps.event.removeListener(clickListener);\n      }\n      setClickListener(google.maps.event.addListener(instance, 'click', onClick));\n    }\n  }, [onClick]);\n  useEffect(() => {\n    if (instance && onDrag) {\n      if (dragListener !== null) {\n        google.maps.event.removeListener(dragListener);\n      }\n      setDragListener(google.maps.event.addListener(instance, 'drag', onDrag));\n    }\n  }, [onDrag]);\n  useEffect(() => {\n    if (instance && onClickableChanged) {\n      if (clickableChangedListener !== null) {\n        google.maps.event.removeListener(clickableChangedListener);\n      }\n      setClickableChangedListener(google.maps.event.addListener(instance, 'clickable_changed', onClickableChanged));\n    }\n  }, [onClickableChanged]);\n  useEffect(() => {\n    if (instance && onCursorChanged) {\n      if (cursorChangedListener !== null) {\n        google.maps.event.removeListener(cursorChangedListener);\n      }\n      setCursorChangedListener(google.maps.event.addListener(instance, 'cursor_changed', onCursorChanged));\n    }\n  }, [onCursorChanged]);\n  useEffect(() => {\n    if (instance && onAnimationChanged) {\n      if (animationChangedListener !== null) {\n        google.maps.event.removeListener(animationChangedListener);\n      }\n      setAnimationChangedListener(google.maps.event.addListener(instance, 'animation_changed', onAnimationChanged));\n    }\n  }, [onAnimationChanged]);\n  useEffect(() => {\n    if (instance && onDraggableChanged) {\n      if (draggableChangedListener !== null) {\n        google.maps.event.removeListener(draggableChangedListener);\n      }\n      setDraggableChangedListener(google.maps.event.addListener(instance, 'draggable_changed', onDraggableChanged));\n    }\n  }, [onDraggableChanged]);\n  useEffect(() => {\n    if (instance && onFlatChanged) {\n      if (flatChangedListener !== null) {\n        google.maps.event.removeListener(flatChangedListener);\n      }\n      setFlatChangedListener(google.maps.event.addListener(instance, 'flat_changed', onFlatChanged));\n    }\n  }, [onFlatChanged]);\n  useEffect(() => {\n    if (instance && onIconChanged) {\n      if (iconChangedListener !== null) {\n        google.maps.event.removeListener(iconChangedListener);\n      }\n      setIconChangedListener(google.maps.event.addListener(instance, 'icon_changed', onIconChanged));\n    }\n  }, [onIconChanged]);\n  useEffect(() => {\n    if (instance && onPositionChanged) {\n      if (positionChangedListener !== null) {\n        google.maps.event.removeListener(positionChangedListener);\n      }\n      setPositionChangedListener(google.maps.event.addListener(instance, 'position_changed', onPositionChanged));\n    }\n  }, [onPositionChanged]);\n  useEffect(() => {\n    if (instance && onShapeChanged) {\n      if (shapeChangedListener !== null) {\n        google.maps.event.removeListener(shapeChangedListener);\n      }\n      setShapeChangedListener(google.maps.event.addListener(instance, 'shape_changed', onShapeChanged));\n    }\n  }, [onShapeChanged]);\n  useEffect(() => {\n    if (instance && onTitleChanged) {\n      if (titleChangedListener !== null) {\n        google.maps.event.removeListener(titleChangedListener);\n      }\n      setTitleChangedListener(google.maps.event.addListener(instance, 'title_changed', onTitleChanged));\n    }\n  }, [onTitleChanged]);\n  useEffect(() => {\n    if (instance && onVisibleChanged) {\n      if (visibleChangedListener !== null) {\n        google.maps.event.removeListener(visibleChangedListener);\n      }\n      setVisibleChangedListener(google.maps.event.addListener(instance, 'visible_changed', onVisibleChanged));\n    }\n  }, [onVisibleChanged]);\n  useEffect(() => {\n    if (instance && onZindexChanged) {\n      if (zIndexChangedListener !== null) {\n        google.maps.event.removeListener(zIndexChangedListener);\n      }\n      setZindexChangedListener(google.maps.event.addListener(instance, 'zindex_changed', onZindexChanged));\n    }\n  }, [onZindexChanged]);\n  useEffect(() => {\n    var markerOptions = _objectSpread$d(_objectSpread$d(_objectSpread$d({}, options || defaultOptions$5), clusterer ? defaultOptions$5 : {\n      map\n    }), {}, {\n      position\n    });\n    var marker = new google.maps.Marker(markerOptions);\n    if (clusterer) {\n      clusterer.addMarker(marker, !!noClustererRedraw);\n    } else {\n      marker.setMap(map);\n    }\n    if (position) {\n      marker.setPosition(position);\n    }\n    if (typeof visible !== 'undefined') {\n      marker.setVisible(visible);\n    }\n    if (typeof draggable !== 'undefined') {\n      marker.setDraggable(draggable);\n    }\n    if (typeof clickable !== 'undefined') {\n      marker.setClickable(clickable);\n    }\n    if (typeof cursor === 'string') {\n      marker.setCursor(cursor);\n    }\n    if (icon) {\n      marker.setIcon(icon);\n    }\n    if (typeof label !== 'undefined') {\n      marker.setLabel(label);\n    }\n    if (typeof opacity !== 'undefined') {\n      marker.setOpacity(opacity);\n    }\n    if (shape) {\n      marker.setShape(shape);\n    }\n    if (typeof title === 'string') {\n      marker.setTitle(title);\n    }\n    if (typeof zIndex === 'number') {\n      marker.setZIndex(zIndex);\n    }\n    if (onDblClick) {\n      setDblclickListener(google.maps.event.addListener(marker, 'dblclick', onDblClick));\n    }\n    if (onDragEnd) {\n      setDragendListener(google.maps.event.addListener(marker, 'dragend', onDragEnd));\n    }\n    if (onDragStart) {\n      setDragstartListener(google.maps.event.addListener(marker, 'dragstart', onDragStart));\n    }\n    if (onMouseDown) {\n      setMousedownListener(google.maps.event.addListener(marker, 'mousedown', onMouseDown));\n    }\n    if (onMouseOut) {\n      setMouseoutListener(google.maps.event.addListener(marker, 'mouseout', onMouseOut));\n    }\n    if (onMouseOver) {\n      setMouseoverListener(google.maps.event.addListener(marker, 'mouseover', onMouseOver));\n    }\n    if (onMouseUp) {\n      setMouseupListener(google.maps.event.addListener(marker, 'mouseup', onMouseUp));\n    }\n    if (onRightClick) {\n      setRightclickListener(google.maps.event.addListener(marker, 'rightclick', onRightClick));\n    }\n    if (onClick) {\n      setClickListener(google.maps.event.addListener(marker, 'click', onClick));\n    }\n    if (onDrag) {\n      setDragListener(google.maps.event.addListener(marker, 'drag', onDrag));\n    }\n    if (onClickableChanged) {\n      setClickableChangedListener(google.maps.event.addListener(marker, 'clickable_changed', onClickableChanged));\n    }\n    if (onCursorChanged) {\n      setCursorChangedListener(google.maps.event.addListener(marker, 'cursor_changed', onCursorChanged));\n    }\n    if (onAnimationChanged) {\n      setAnimationChangedListener(google.maps.event.addListener(marker, 'animation_changed', onAnimationChanged));\n    }\n    if (onDraggableChanged) {\n      setDraggableChangedListener(google.maps.event.addListener(marker, 'draggable_changed', onDraggableChanged));\n    }\n    if (onFlatChanged) {\n      setFlatChangedListener(google.maps.event.addListener(marker, 'flat_changed', onFlatChanged));\n    }\n    if (onIconChanged) {\n      setIconChangedListener(google.maps.event.addListener(marker, 'icon_changed', onIconChanged));\n    }\n    if (onPositionChanged) {\n      setPositionChangedListener(google.maps.event.addListener(marker, 'position_changed', onPositionChanged));\n    }\n    if (onShapeChanged) {\n      setShapeChangedListener(google.maps.event.addListener(marker, 'shape_changed', onShapeChanged));\n    }\n    if (onTitleChanged) {\n      setTitleChangedListener(google.maps.event.addListener(marker, 'title_changed', onTitleChanged));\n    }\n    if (onVisibleChanged) {\n      setVisibleChangedListener(google.maps.event.addListener(marker, 'visible_changed', onVisibleChanged));\n    }\n    if (onZindexChanged) {\n      setZindexChangedListener(google.maps.event.addListener(marker, 'zindex_changed', onZindexChanged));\n    }\n    setInstance(marker);\n    if (onLoad) {\n      onLoad(marker);\n    }\n    return () => {\n      if (dblclickListener !== null) {\n        google.maps.event.removeListener(dblclickListener);\n      }\n      if (dragendListener !== null) {\n        google.maps.event.removeListener(dragendListener);\n      }\n      if (dragstartListener !== null) {\n        google.maps.event.removeListener(dragstartListener);\n      }\n      if (mousedownListener !== null) {\n        google.maps.event.removeListener(mousedownListener);\n      }\n      if (mouseoutListener !== null) {\n        google.maps.event.removeListener(mouseoutListener);\n      }\n      if (mouseoverListener !== null) {\n        google.maps.event.removeListener(mouseoverListener);\n      }\n      if (mouseupListener !== null) {\n        google.maps.event.removeListener(mouseupListener);\n      }\n      if (rightclickListener !== null) {\n        google.maps.event.removeListener(rightclickListener);\n      }\n      if (clickListener !== null) {\n        google.maps.event.removeListener(clickListener);\n      }\n      if (clickableChangedListener !== null) {\n        google.maps.event.removeListener(clickableChangedListener);\n      }\n      if (cursorChangedListener !== null) {\n        google.maps.event.removeListener(cursorChangedListener);\n      }\n      if (animationChangedListener !== null) {\n        google.maps.event.removeListener(animationChangedListener);\n      }\n      if (draggableChangedListener !== null) {\n        google.maps.event.removeListener(draggableChangedListener);\n      }\n      if (flatChangedListener !== null) {\n        google.maps.event.removeListener(flatChangedListener);\n      }\n      if (iconChangedListener !== null) {\n        google.maps.event.removeListener(iconChangedListener);\n      }\n      if (positionChangedListener !== null) {\n        google.maps.event.removeListener(positionChangedListener);\n      }\n      if (titleChangedListener !== null) {\n        google.maps.event.removeListener(titleChangedListener);\n      }\n      if (visibleChangedListener !== null) {\n        google.maps.event.removeListener(visibleChangedListener);\n      }\n      if (zIndexChangedListener !== null) {\n        google.maps.event.removeListener(zIndexChangedListener);\n      }\n      if (onUnmount) {\n        onUnmount(marker);\n      }\n      if (clusterer) {\n        clusterer.removeMarker(marker, !!noClustererRedraw);\n      } else if (marker) {\n        marker.setMap(null);\n      }\n    };\n  }, []);\n  var chx = useMemo(() => {\n    return children ? Children.map(children, child => {\n      if (!isValidElement(child)) {\n        return child;\n      }\n      var elementChild = child;\n      return cloneElement(elementChild, {\n        anchor: instance\n      });\n    }) : null;\n  }, [children, instance]);\n  return jsx(Fragment, {\n    children: chx\n  }) || null;\n}\nvar MarkerF = memo(MarkerFunctional);\nclass Marker extends PureComponent {\n  constructor() {\n    super(...arguments);\n    _defineProperty(this, \"registeredEvents\", []);\n  }\n  componentDidMount() {\n    var _this = this;\n    return _asyncToGenerator(function* () {\n      var markerOptions = _objectSpread$d(_objectSpread$d(_objectSpread$d({}, _this.props.options || defaultOptions$5), _this.props.clusterer ? defaultOptions$5 : {\n        map: _this.context\n      }), {}, {\n        position: _this.props.position\n      });\n      // Unfortunately we can't just do this in the contstructor, because the\n      // `MapContext` might not be filled in yet.\n      _this.marker = new google.maps.Marker(markerOptions);\n      if (_this.props.clusterer) {\n        _this.props.clusterer.addMarker(_this.marker, !!_this.props.noClustererRedraw);\n      } else {\n        _this.marker.setMap(_this.context);\n      }\n      _this.registeredEvents = applyUpdatersToPropsAndRegisterEvents({\n        updaterMap: updaterMap$f,\n        eventMap: eventMap$f,\n        prevProps: {},\n        nextProps: _this.props,\n        instance: _this.marker\n      });\n      if (_this.props.onLoad) {\n        _this.props.onLoad(_this.marker);\n      }\n    })();\n  }\n  componentDidUpdate(prevProps) {\n    if (this.marker) {\n      unregisterEvents(this.registeredEvents);\n      this.registeredEvents = applyUpdatersToPropsAndRegisterEvents({\n        updaterMap: updaterMap$f,\n        eventMap: eventMap$f,\n        prevProps,\n        nextProps: this.props,\n        instance: this.marker\n      });\n    }\n  }\n  componentWillUnmount() {\n    if (!this.marker) {\n      return;\n    }\n    if (this.props.onUnmount) {\n      this.props.onUnmount(this.marker);\n    }\n    unregisterEvents(this.registeredEvents);\n    if (this.props.clusterer) {\n      this.props.clusterer.removeMarker(this.marker, !!this.props.noClustererRedraw);\n    } else if (this.marker) {\n      this.marker.setMap(null);\n    }\n  }\n  render() {\n    var children = this.props.children ? Children.map(this.props.children, child => {\n      if (!isValidElement(child)) {\n        return child;\n      }\n      var elementChild = child;\n      return cloneElement(elementChild, {\n        anchor: this.marker\n      });\n    }) : null;\n    return children || null;\n  }\n}\n_defineProperty(Marker, \"contextType\", MapContext);\n\nvar ClusterIcon = /** @class */function () {\n  function ClusterIcon(cluster, styles) {\n    cluster.getClusterer().extend(ClusterIcon, google.maps.OverlayView);\n    this.cluster = cluster;\n    this.clusterClassName = this.cluster.getClusterer().getClusterClass();\n    this.className = this.clusterClassName;\n    this.styles = styles;\n    this.center = undefined;\n    this.div = null;\n    this.sums = null;\n    this.visible = false;\n    this.boundsChangedListener = null;\n    this.url = '';\n    this.height = 0;\n    this.width = 0;\n    this.anchorText = [0, 0];\n    this.anchorIcon = [0, 0];\n    this.textColor = 'black';\n    this.textSize = 11;\n    this.textDecoration = 'none';\n    this.fontWeight = 'bold';\n    this.fontStyle = 'normal';\n    this.fontFamily = 'Arial,sans-serif';\n    this.backgroundPosition = '0 0';\n    this.cMouseDownInCluster = null;\n    this.cDraggingMapByCluster = null;\n    this.timeOut = null;\n    this.setMap(cluster.getMap()); // Note: this causes onAdd to be called\n    this.onBoundsChanged = this.onBoundsChanged.bind(this);\n    this.onMouseDown = this.onMouseDown.bind(this);\n    this.onClick = this.onClick.bind(this);\n    this.onMouseOver = this.onMouseOver.bind(this);\n    this.onMouseOut = this.onMouseOut.bind(this);\n    this.onAdd = this.onAdd.bind(this);\n    this.onRemove = this.onRemove.bind(this);\n    this.draw = this.draw.bind(this);\n    this.hide = this.hide.bind(this);\n    this.show = this.show.bind(this);\n    this.useStyle = this.useStyle.bind(this);\n    this.setCenter = this.setCenter.bind(this);\n    this.getPosFromLatLng = this.getPosFromLatLng.bind(this);\n  }\n  ClusterIcon.prototype.onBoundsChanged = function () {\n    this.cDraggingMapByCluster = this.cMouseDownInCluster;\n  };\n  ClusterIcon.prototype.onMouseDown = function () {\n    this.cMouseDownInCluster = true;\n    this.cDraggingMapByCluster = false;\n  };\n  ClusterIcon.prototype.onClick = function (event) {\n    this.cMouseDownInCluster = false;\n    if (!this.cDraggingMapByCluster) {\n      var markerClusterer_1 = this.cluster.getClusterer();\n      /**\n       * This event is fired when a cluster marker is clicked.\n       * @name MarkerClusterer#click\n       * @param {Cluster} c The cluster that was clicked.\n       * @event\n       */\n      google.maps.event.trigger(markerClusterer_1, 'click', this.cluster);\n      google.maps.event.trigger(markerClusterer_1, 'clusterclick', this.cluster); // deprecated name\n      // The default click handler follows. Disable it by setting\n      // the zoomOnClick property to false.\n      if (markerClusterer_1.getZoomOnClick()) {\n        // Zoom into the cluster.\n        var maxZoom_1 = markerClusterer_1.getMaxZoom();\n        var bounds_1 = this.cluster.getBounds();\n        var map = markerClusterer_1.getMap();\n        if (map !== null && 'fitBounds' in map) {\n          map.fitBounds(bounds_1);\n        }\n        // There is a fix for Issue 170 here:\n        this.timeOut = window.setTimeout(function () {\n          var map = markerClusterer_1.getMap();\n          if (map !== null) {\n            if ('fitBounds' in map) {\n              map.fitBounds(bounds_1);\n            }\n            var zoom = map.getZoom() || 0;\n            // Don't zoom beyond the max zoom level\n            if (maxZoom_1 !== null && zoom > maxZoom_1) {\n              map.setZoom(maxZoom_1 + 1);\n            }\n          }\n        }, 100);\n      }\n      // Prevent event propagation to the map:\n      event.cancelBubble = true;\n      if (event.stopPropagation) {\n        event.stopPropagation();\n      }\n    }\n  };\n  ClusterIcon.prototype.onMouseOver = function () {\n    /**\n     * This event is fired when the mouse moves over a cluster marker.\n     * @name MarkerClusterer#mouseover\n     * @param {Cluster} c The cluster that the mouse moved over.\n     * @event\n     */\n    google.maps.event.trigger(this.cluster.getClusterer(), 'mouseover', this.cluster);\n  };\n  ClusterIcon.prototype.onMouseOut = function () {\n    /**\n     * This event is fired when the mouse moves out of a cluster marker.\n     * @name MarkerClusterer#mouseout\n     * @param {Cluster} c The cluster that the mouse moved out of.\n     * @event\n     */\n    google.maps.event.trigger(this.cluster.getClusterer(), 'mouseout', this.cluster);\n  };\n  ClusterIcon.prototype.onAdd = function () {\n    var _a;\n    this.div = document.createElement('div');\n    this.div.className = this.className;\n    if (this.visible) {\n      this.show();\n    }\n    (_a = this.getPanes()) === null || _a === void 0 ? void 0 : _a.overlayMouseTarget.appendChild(this.div);\n    var map = this.getMap();\n    if (map !== null) {\n      // Fix for Issue 157\n      this.boundsChangedListener = google.maps.event.addListener(map, 'bounds_changed', this.onBoundsChanged);\n      this.div.addEventListener('mousedown', this.onMouseDown);\n      this.div.addEventListener('click', this.onClick);\n      this.div.addEventListener('mouseover', this.onMouseOver);\n      this.div.addEventListener('mouseout', this.onMouseOut);\n    }\n  };\n  ClusterIcon.prototype.onRemove = function () {\n    if (this.div && this.div.parentNode) {\n      this.hide();\n      if (this.boundsChangedListener !== null) {\n        google.maps.event.removeListener(this.boundsChangedListener);\n      }\n      this.div.removeEventListener('mousedown', this.onMouseDown);\n      this.div.removeEventListener('click', this.onClick);\n      this.div.removeEventListener('mouseover', this.onMouseOver);\n      this.div.removeEventListener('mouseout', this.onMouseOut);\n      this.div.parentNode.removeChild(this.div);\n      if (this.timeOut !== null) {\n        window.clearTimeout(this.timeOut);\n        this.timeOut = null;\n      }\n      this.div = null;\n    }\n  };\n  ClusterIcon.prototype.draw = function () {\n    if (this.visible && this.div !== null && this.center) {\n      var pos = this.getPosFromLatLng(this.center);\n      this.div.style.top = pos !== null ? \"\".concat(pos.y, \"px\") : '0';\n      this.div.style.left = pos !== null ? \"\".concat(pos.x, \"px\") : '0';\n    }\n  };\n  ClusterIcon.prototype.hide = function () {\n    if (this.div) {\n      this.div.style.display = 'none';\n    }\n    this.visible = false;\n  };\n  ClusterIcon.prototype.show = function () {\n    var _a, _b, _c, _d, _e, _f;\n    if (this.div && this.center) {\n      var divTitle = this.sums === null || typeof this.sums.title === 'undefined' || this.sums.title === '' ? this.cluster.getClusterer().getTitle() : this.sums.title;\n      // NOTE: values must be specified in px units\n      var bp = this.backgroundPosition.split(' ');\n      var spriteH = parseInt(((_a = bp[0]) === null || _a === void 0 ? void 0 : _a.replace(/^\\s+|\\s+$/g, '')) || '0', 10);\n      var spriteV = parseInt(((_b = bp[1]) === null || _b === void 0 ? void 0 : _b.replace(/^\\s+|\\s+$/g, '')) || '0', 10);\n      var pos = this.getPosFromLatLng(this.center);\n      this.div.className = this.className;\n      this.div.setAttribute('style', \"cursor: pointer; position: absolute; top: \".concat(pos !== null ? \"\".concat(pos.y, \"px\") : '0', \"; left: \").concat(pos !== null ? \"\".concat(pos.x, \"px\") : '0', \"; width: \").concat(this.width, \"px; height: \").concat(this.height, \"px; \"));\n      var img = document.createElement('img');\n      img.alt = divTitle;\n      img.src = this.url;\n      img.width = this.width;\n      img.height = this.height;\n      img.setAttribute('style', \"position: absolute; top: \".concat(spriteV, \"px; left: \").concat(spriteH, \"px\"));\n      if (!this.cluster.getClusterer().enableRetinaIcons) {\n        img.style.clip = \"rect(-\".concat(spriteV, \"px, -\").concat(spriteH + this.width, \"px, -\").concat(spriteV + this.height, \", -\").concat(spriteH, \")\");\n      }\n      var textElm = document.createElement('div');\n      textElm.setAttribute('style', \"position: absolute; top: \".concat(this.anchorText[0], \"px; left: \").concat(this.anchorText[1], \"px; color: \").concat(this.textColor, \"; font-size: \").concat(this.textSize, \"px; font-family: \").concat(this.fontFamily, \"; font-weight: \").concat(this.fontWeight, \"; fontStyle: \").concat(this.fontStyle, \"; text-decoration: \").concat(this.textDecoration, \"; text-align: center; width: \").concat(this.width, \"px; line-height: \").concat(this.height, \"px\"));\n      if ((_c = this.sums) === null || _c === void 0 ? void 0 : _c.text) textElm.innerText = \"\".concat((_d = this.sums) === null || _d === void 0 ? void 0 : _d.text);\n      if ((_e = this.sums) === null || _e === void 0 ? void 0 : _e.html) textElm.innerHTML = \"\".concat((_f = this.sums) === null || _f === void 0 ? void 0 : _f.html);\n      this.div.innerHTML = '';\n      this.div.appendChild(img);\n      this.div.appendChild(textElm);\n      this.div.title = divTitle;\n      this.div.style.display = '';\n    }\n    this.visible = true;\n  };\n  ClusterIcon.prototype.useStyle = function (sums) {\n    this.sums = sums;\n    var styles = this.cluster.getClusterer().getStyles();\n    var style = styles[Math.min(styles.length - 1, Math.max(0, sums.index - 1))];\n    if (style) {\n      this.url = style.url;\n      this.height = style.height;\n      this.width = style.width;\n      if (style.className) {\n        this.className = \"\".concat(this.clusterClassName, \" \").concat(style.className);\n      }\n      this.anchorText = style.anchorText || [0, 0];\n      this.anchorIcon = style.anchorIcon || [this.height / 2, this.width / 2];\n      this.textColor = style.textColor || 'black';\n      this.textSize = style.textSize || 11;\n      this.textDecoration = style.textDecoration || 'none';\n      this.fontWeight = style.fontWeight || 'bold';\n      this.fontStyle = style.fontStyle || 'normal';\n      this.fontFamily = style.fontFamily || 'Arial,sans-serif';\n      this.backgroundPosition = style.backgroundPosition || '0 0';\n    }\n  };\n  ClusterIcon.prototype.setCenter = function (center) {\n    this.center = center;\n  };\n  ClusterIcon.prototype.getPosFromLatLng = function (latlng) {\n    var pos = this.getProjection().fromLatLngToDivPixel(latlng);\n    if (pos !== null) {\n      pos.x -= this.anchorIcon[1];\n      pos.y -= this.anchorIcon[0];\n    }\n    return pos;\n  };\n  return ClusterIcon;\n}();\n\n/* global google */\nvar Cluster$1 = /** @class */function () {\n  function Cluster(markerClusterer) {\n    this.markerClusterer = markerClusterer;\n    this.map = this.markerClusterer.getMap();\n    this.gridSize = this.markerClusterer.getGridSize();\n    this.minClusterSize = this.markerClusterer.getMinimumClusterSize();\n    this.averageCenter = this.markerClusterer.getAverageCenter();\n    this.markers = [];\n    this.center = undefined;\n    this.bounds = null;\n    this.clusterIcon = new ClusterIcon(this, this.markerClusterer.getStyles());\n    this.getSize = this.getSize.bind(this);\n    this.getMarkers = this.getMarkers.bind(this);\n    this.getCenter = this.getCenter.bind(this);\n    this.getMap = this.getMap.bind(this);\n    this.getClusterer = this.getClusterer.bind(this);\n    this.getBounds = this.getBounds.bind(this);\n    this.remove = this.remove.bind(this);\n    this.addMarker = this.addMarker.bind(this);\n    this.isMarkerInClusterBounds = this.isMarkerInClusterBounds.bind(this);\n    this.calculateBounds = this.calculateBounds.bind(this);\n    this.updateIcon = this.updateIcon.bind(this);\n    this.isMarkerAlreadyAdded = this.isMarkerAlreadyAdded.bind(this);\n  }\n  Cluster.prototype.getSize = function () {\n    return this.markers.length;\n  };\n  Cluster.prototype.getMarkers = function () {\n    return this.markers;\n  };\n  Cluster.prototype.getCenter = function () {\n    return this.center;\n  };\n  Cluster.prototype.getMap = function () {\n    return this.map;\n  };\n  Cluster.prototype.getClusterer = function () {\n    return this.markerClusterer;\n  };\n  Cluster.prototype.getBounds = function () {\n    var bounds = new google.maps.LatLngBounds(this.center, this.center);\n    var markers = this.getMarkers();\n    for (var _i = 0, markers_1 = markers; _i < markers_1.length; _i++) {\n      var marker = markers_1[_i];\n      var position = marker.getPosition();\n      if (position) {\n        bounds.extend(position);\n      }\n    }\n    return bounds;\n  };\n  Cluster.prototype.remove = function () {\n    this.clusterIcon.setMap(null);\n    this.markers = [];\n    // eslint-disable-next-line @typescript-eslint/ban-ts-comment\n    // @ts-ignore\n    delete this.markers;\n  };\n  Cluster.prototype.addMarker = function (marker) {\n    var _a;\n    if (this.isMarkerAlreadyAdded(marker)) {\n      return false;\n    }\n    if (!this.center) {\n      var position = marker.getPosition();\n      if (position) {\n        this.center = position;\n        this.calculateBounds();\n      }\n    } else {\n      if (this.averageCenter) {\n        var position = marker.getPosition();\n        if (position) {\n          var length_1 = this.markers.length + 1;\n          this.center = new google.maps.LatLng((this.center.lat() * (length_1 - 1) + position.lat()) / length_1, (this.center.lng() * (length_1 - 1) + position.lng()) / length_1);\n          this.calculateBounds();\n        }\n      }\n    }\n    marker.isAdded = true;\n    this.markers.push(marker);\n    var mCount = this.markers.length;\n    var maxZoom = this.markerClusterer.getMaxZoom();\n    var zoom = (_a = this.map) === null || _a === void 0 ? void 0 : _a.getZoom();\n    if (maxZoom !== null && typeof zoom !== 'undefined' && zoom > maxZoom) {\n      // Zoomed in past max zoom, so show the marker.\n      if (marker.getMap() !== this.map) {\n        marker.setMap(this.map);\n      }\n    } else if (mCount < this.minClusterSize) {\n      // Min cluster size not reached so show the marker.\n      if (marker.getMap() !== this.map) {\n        marker.setMap(this.map);\n      }\n    } else if (mCount === this.minClusterSize) {\n      // Hide the markers that were showing.\n      for (var _i = 0, _b = this.markers; _i < _b.length; _i++) {\n        var markerElement = _b[_i];\n        markerElement.setMap(null);\n      }\n    } else {\n      marker.setMap(null);\n    }\n    return true;\n  };\n  Cluster.prototype.isMarkerInClusterBounds = function (marker) {\n    if (this.bounds !== null) {\n      var position = marker.getPosition();\n      if (position) {\n        return this.bounds.contains(position);\n      }\n    }\n    return false;\n  };\n  Cluster.prototype.calculateBounds = function () {\n    this.bounds = this.markerClusterer.getExtendedBounds(new google.maps.LatLngBounds(this.center, this.center));\n  };\n  Cluster.prototype.updateIcon = function () {\n    var _a;\n    var mCount = this.markers.length;\n    var maxZoom = this.markerClusterer.getMaxZoom();\n    var zoom = (_a = this.map) === null || _a === void 0 ? void 0 : _a.getZoom();\n    if (maxZoom !== null && typeof zoom !== 'undefined' && zoom > maxZoom) {\n      this.clusterIcon.hide();\n      return;\n    }\n    if (mCount < this.minClusterSize) {\n      // Min cluster size not yet reached.\n      this.clusterIcon.hide();\n      return;\n    }\n    if (this.center) {\n      this.clusterIcon.setCenter(this.center);\n    }\n    this.clusterIcon.useStyle(this.markerClusterer.getCalculator()(this.markers, this.markerClusterer.getStyles().length));\n    this.clusterIcon.show();\n  };\n  Cluster.prototype.isMarkerAlreadyAdded = function (marker) {\n    if (this.markers.includes) {\n      return this.markers.includes(marker);\n    }\n    for (var i = 0; i < this.markers.length; i++) {\n      if (marker === this.markers[i]) {\n        return true;\n      }\n    }\n    return false;\n  };\n  return Cluster;\n}();\n\n/* global google */\n/* eslint-disable filenames/match-regex */\n/**\n * Supports up to 9007199254740991 (Number.MAX_SAFE_INTEGER) markers\n * which is not a problem as max array length is 4294967296 (2**32)\n */\nfunction CALCULATOR(markers, numStyles) {\n  var count = markers.length;\n  var numberOfDigits = count.toString().length;\n  var index = Math.min(numberOfDigits, numStyles);\n  return {\n    text: count.toString(),\n    index: index,\n    title: ''\n  };\n}\nvar BATCH_SIZE = 2000;\nvar BATCH_SIZE_IE = 500;\nvar IMAGE_PATH = 'https://developers.google.com/maps/documentation/javascript/examples/markerclusterer/m';\nvar IMAGE_EXTENSION = 'png';\nvar IMAGE_SIZES = [53, 56, 66, 78, 90];\nvar CLUSTERER_CLASS = 'cluster';\nvar Clusterer = /** @class */function () {\n  function Clusterer(map, optMarkers, optOptions) {\n    if (optMarkers === void 0) {\n      optMarkers = [];\n    }\n    if (optOptions === void 0) {\n      optOptions = {};\n    }\n    this.getMinimumClusterSize = this.getMinimumClusterSize.bind(this);\n    this.setMinimumClusterSize = this.setMinimumClusterSize.bind(this);\n    this.getEnableRetinaIcons = this.getEnableRetinaIcons.bind(this);\n    this.setEnableRetinaIcons = this.setEnableRetinaIcons.bind(this);\n    this.addToClosestCluster = this.addToClosestCluster.bind(this);\n    this.getImageExtension = this.getImageExtension.bind(this);\n    this.setImageExtension = this.setImageExtension.bind(this);\n    this.getExtendedBounds = this.getExtendedBounds.bind(this);\n    this.getAverageCenter = this.getAverageCenter.bind(this);\n    this.setAverageCenter = this.setAverageCenter.bind(this);\n    this.getTotalClusters = this.getTotalClusters.bind(this);\n    this.fitMapToMarkers = this.fitMapToMarkers.bind(this);\n    this.getIgnoreHidden = this.getIgnoreHidden.bind(this);\n    this.setIgnoreHidden = this.setIgnoreHidden.bind(this);\n    this.getClusterClass = this.getClusterClass.bind(this);\n    this.setClusterClass = this.setClusterClass.bind(this);\n    this.getTotalMarkers = this.getTotalMarkers.bind(this);\n    this.getZoomOnClick = this.getZoomOnClick.bind(this);\n    this.setZoomOnClick = this.setZoomOnClick.bind(this);\n    this.getBatchSizeIE = this.getBatchSizeIE.bind(this);\n    this.setBatchSizeIE = this.setBatchSizeIE.bind(this);\n    this.createClusters = this.createClusters.bind(this);\n    this.onZoomChanged = this.onZoomChanged.bind(this);\n    this.getImageSizes = this.getImageSizes.bind(this);\n    this.setImageSizes = this.setImageSizes.bind(this);\n    this.getCalculator = this.getCalculator.bind(this);\n    this.setCalculator = this.setCalculator.bind(this);\n    this.removeMarkers = this.removeMarkers.bind(this);\n    this.resetViewport = this.resetViewport.bind(this);\n    this.getImagePath = this.getImagePath.bind(this);\n    this.setImagePath = this.setImagePath.bind(this);\n    this.pushMarkerTo = this.pushMarkerTo.bind(this);\n    this.removeMarker = this.removeMarker.bind(this);\n    this.clearMarkers = this.clearMarkers.bind(this);\n    this.setupStyles = this.setupStyles.bind(this);\n    this.getGridSize = this.getGridSize.bind(this);\n    this.setGridSize = this.setGridSize.bind(this);\n    this.getClusters = this.getClusters.bind(this);\n    this.getMaxZoom = this.getMaxZoom.bind(this);\n    this.setMaxZoom = this.setMaxZoom.bind(this);\n    this.getMarkers = this.getMarkers.bind(this);\n    this.addMarkers = this.addMarkers.bind(this);\n    this.getStyles = this.getStyles.bind(this);\n    this.setStyles = this.setStyles.bind(this);\n    this.addMarker = this.addMarker.bind(this);\n    this.onRemove = this.onRemove.bind(this);\n    this.getTitle = this.getTitle.bind(this);\n    this.setTitle = this.setTitle.bind(this);\n    this.repaint = this.repaint.bind(this);\n    this.onIdle = this.onIdle.bind(this);\n    this.redraw = this.redraw.bind(this);\n    this.onAdd = this.onAdd.bind(this);\n    this.draw = this.draw.bind(this);\n    this.extend = this.extend.bind(this);\n    this.extend(Clusterer, google.maps.OverlayView);\n    this.markers = [];\n    this.clusters = [];\n    this.listeners = [];\n    this.activeMap = null;\n    this.ready = false;\n    this.gridSize = optOptions.gridSize || 60;\n    this.minClusterSize = optOptions.minimumClusterSize || 2;\n    this.maxZoom = optOptions.maxZoom || null;\n    this.styles = optOptions.styles || [];\n    this.title = optOptions.title || '';\n    this.zoomOnClick = true;\n    if (optOptions.zoomOnClick !== undefined) {\n      this.zoomOnClick = optOptions.zoomOnClick;\n    }\n    this.averageCenter = false;\n    if (optOptions.averageCenter !== undefined) {\n      this.averageCenter = optOptions.averageCenter;\n    }\n    this.ignoreHidden = false;\n    if (optOptions.ignoreHidden !== undefined) {\n      this.ignoreHidden = optOptions.ignoreHidden;\n    }\n    this.enableRetinaIcons = false;\n    if (optOptions.enableRetinaIcons !== undefined) {\n      this.enableRetinaIcons = optOptions.enableRetinaIcons;\n    }\n    this.imagePath = optOptions.imagePath || IMAGE_PATH;\n    this.imageExtension = optOptions.imageExtension || IMAGE_EXTENSION;\n    this.imageSizes = optOptions.imageSizes || IMAGE_SIZES;\n    this.calculator = optOptions.calculator || CALCULATOR;\n    this.batchSize = optOptions.batchSize || BATCH_SIZE;\n    this.batchSizeIE = optOptions.batchSizeIE || BATCH_SIZE_IE;\n    this.clusterClass = optOptions.clusterClass || CLUSTERER_CLASS;\n    if (navigator.userAgent.toLowerCase().indexOf('msie') !== -1) {\n      // Try to avoid IE timeout when processing a huge number of markers:\n      this.batchSize = this.batchSizeIE;\n    }\n    this.timerRefStatic = null;\n    this.setupStyles();\n    this.addMarkers(optMarkers, true);\n    this.setMap(map); // Note: this causes onAdd to be called\n  }\n  Clusterer.prototype.onZoomChanged = function () {\n    var _a, _b;\n    this.resetViewport(false);\n    // Workaround for this Google bug: when map is at level 0 and \"-\" of\n    // zoom slider is clicked, a \"zoom_changed\" event is fired even though\n    // the map doesn't zoom out any further. In this situation, no \"idle\"\n    // event is triggered so the cluster markers that have been removed\n    // do not get redrawn. Same goes for a zoom in at maxZoom.\n    if (((_a = this.getMap()) === null || _a === void 0 ? void 0 : _a.getZoom()) === (this.get('minZoom') || 0) || ((_b = this.getMap()) === null || _b === void 0 ? void 0 : _b.getZoom()) === this.get('maxZoom')) {\n      google.maps.event.trigger(this, 'idle');\n    }\n  };\n  Clusterer.prototype.onIdle = function () {\n    this.redraw();\n  };\n  Clusterer.prototype.onAdd = function () {\n    var map = this.getMap();\n    this.activeMap = map;\n    this.ready = true;\n    this.repaint();\n    if (map !== null) {\n      // Add the map event listeners\n      this.listeners = [google.maps.event.addListener(map, 'zoom_changed', this.onZoomChanged), google.maps.event.addListener(map, 'idle', this.onIdle)];\n    }\n  };\n  Clusterer.prototype.onRemove = function () {\n    // Put all the managed markers back on the map:\n    for (var _i = 0, _a = this.markers; _i < _a.length; _i++) {\n      var marker = _a[_i];\n      if (marker.getMap() !== this.activeMap) {\n        marker.setMap(this.activeMap);\n      }\n    }\n    // Remove all clusters:\n    for (var _b = 0, _c = this.clusters; _b < _c.length; _b++) {\n      var cluster = _c[_b];\n      cluster.remove();\n    }\n    this.clusters = [];\n    // Remove map event listeners:\n    for (var _d = 0, _e = this.listeners; _d < _e.length; _d++) {\n      var listener = _e[_d];\n      google.maps.event.removeListener(listener);\n    }\n    this.listeners = [];\n    this.activeMap = null;\n    this.ready = false;\n  };\n  Clusterer.prototype.draw = function () {\n    return;\n  };\n  Clusterer.prototype.getMap = function () {\n    return null;\n  };\n  Clusterer.prototype.getPanes = function () {\n    return null;\n  };\n  Clusterer.prototype.getProjection = function () {\n    return {\n      fromContainerPixelToLatLng: function fromContainerPixelToLatLng() {\n        return null;\n      },\n      fromDivPixelToLatLng: function fromDivPixelToLatLng() {\n        return null;\n      },\n      fromLatLngToContainerPixel: function fromLatLngToContainerPixel() {\n        return null;\n      },\n      fromLatLngToDivPixel: function fromLatLngToDivPixel() {\n        return null;\n      },\n      getVisibleRegion: function getVisibleRegion() {\n        return null;\n      },\n      getWorldWidth: function getWorldWidth() {\n        return 0;\n      }\n    };\n  };\n  Clusterer.prototype.setMap = function () {\n    return;\n  };\n  Clusterer.prototype.addListener = function () {\n    return {\n      remove: function remove() {\n        return;\n      }\n    };\n  };\n  Clusterer.prototype.bindTo = function () {\n    return;\n  };\n  Clusterer.prototype.get = function () {\n    return;\n  };\n  Clusterer.prototype.notify = function () {\n    return;\n  };\n  Clusterer.prototype.set = function () {\n    return;\n  };\n  Clusterer.prototype.setValues = function () {\n    return;\n  };\n  Clusterer.prototype.unbind = function () {\n    return;\n  };\n  Clusterer.prototype.unbindAll = function () {\n    return;\n  };\n  Clusterer.prototype.setupStyles = function () {\n    if (this.styles.length > 0) {\n      return;\n    }\n    for (var i = 0; i < this.imageSizes.length; i++) {\n      this.styles.push({\n        url: \"\".concat(this.imagePath + (i + 1), \".\").concat(this.imageExtension),\n        height: this.imageSizes[i] || 0,\n        width: this.imageSizes[i] || 0\n      });\n    }\n  };\n  Clusterer.prototype.fitMapToMarkers = function () {\n    var markers = this.getMarkers();\n    var bounds = new google.maps.LatLngBounds();\n    for (var _i = 0, markers_1 = markers; _i < markers_1.length; _i++) {\n      var marker = markers_1[_i];\n      var position = marker.getPosition();\n      if (position) {\n        bounds.extend(position);\n      }\n    }\n    var map = this.getMap();\n    if (map !== null && 'fitBounds' in map) {\n      map.fitBounds(bounds);\n    }\n  };\n  Clusterer.prototype.getGridSize = function () {\n    return this.gridSize;\n  };\n  Clusterer.prototype.setGridSize = function (gridSize) {\n    this.gridSize = gridSize;\n  };\n  Clusterer.prototype.getMinimumClusterSize = function () {\n    return this.minClusterSize;\n  };\n  Clusterer.prototype.setMinimumClusterSize = function (minimumClusterSize) {\n    this.minClusterSize = minimumClusterSize;\n  };\n  Clusterer.prototype.getMaxZoom = function () {\n    return this.maxZoom;\n  };\n  Clusterer.prototype.setMaxZoom = function (maxZoom) {\n    this.maxZoom = maxZoom;\n  };\n  Clusterer.prototype.getStyles = function () {\n    return this.styles;\n  };\n  Clusterer.prototype.setStyles = function (styles) {\n    this.styles = styles;\n  };\n  Clusterer.prototype.getTitle = function () {\n    return this.title;\n  };\n  Clusterer.prototype.setTitle = function (title) {\n    this.title = title;\n  };\n  Clusterer.prototype.getZoomOnClick = function () {\n    return this.zoomOnClick;\n  };\n  Clusterer.prototype.setZoomOnClick = function (zoomOnClick) {\n    this.zoomOnClick = zoomOnClick;\n  };\n  Clusterer.prototype.getAverageCenter = function () {\n    return this.averageCenter;\n  };\n  Clusterer.prototype.setAverageCenter = function (averageCenter) {\n    this.averageCenter = averageCenter;\n  };\n  Clusterer.prototype.getIgnoreHidden = function () {\n    return this.ignoreHidden;\n  };\n  Clusterer.prototype.setIgnoreHidden = function (ignoreHidden) {\n    this.ignoreHidden = ignoreHidden;\n  };\n  Clusterer.prototype.getEnableRetinaIcons = function () {\n    return this.enableRetinaIcons;\n  };\n  Clusterer.prototype.setEnableRetinaIcons = function (enableRetinaIcons) {\n    this.enableRetinaIcons = enableRetinaIcons;\n  };\n  Clusterer.prototype.getImageExtension = function () {\n    return this.imageExtension;\n  };\n  Clusterer.prototype.setImageExtension = function (imageExtension) {\n    this.imageExtension = imageExtension;\n  };\n  Clusterer.prototype.getImagePath = function () {\n    return this.imagePath;\n  };\n  Clusterer.prototype.setImagePath = function (imagePath) {\n    this.imagePath = imagePath;\n  };\n  Clusterer.prototype.getImageSizes = function () {\n    return this.imageSizes;\n  };\n  Clusterer.prototype.setImageSizes = function (imageSizes) {\n    this.imageSizes = imageSizes;\n  };\n  Clusterer.prototype.getCalculator = function () {\n    return this.calculator;\n  };\n  Clusterer.prototype.setCalculator = function (calculator) {\n    this.calculator = calculator;\n  };\n  Clusterer.prototype.getBatchSizeIE = function () {\n    return this.batchSizeIE;\n  };\n  Clusterer.prototype.setBatchSizeIE = function (batchSizeIE) {\n    this.batchSizeIE = batchSizeIE;\n  };\n  Clusterer.prototype.getClusterClass = function () {\n    return this.clusterClass;\n  };\n  Clusterer.prototype.setClusterClass = function (clusterClass) {\n    this.clusterClass = clusterClass;\n  };\n  Clusterer.prototype.getMarkers = function () {\n    return this.markers;\n  };\n  Clusterer.prototype.getTotalMarkers = function () {\n    return this.markers.length;\n  };\n  Clusterer.prototype.getClusters = function () {\n    return this.clusters;\n  };\n  Clusterer.prototype.getTotalClusters = function () {\n    return this.clusters.length;\n  };\n  Clusterer.prototype.addMarker = function (marker, optNoDraw) {\n    this.pushMarkerTo(marker);\n    if (!optNoDraw) {\n      this.redraw();\n    }\n  };\n  Clusterer.prototype.addMarkers = function (markers, optNoDraw) {\n    for (var key in markers) {\n      if (Object.prototype.hasOwnProperty.call(markers, key)) {\n        var marker = markers[key];\n        if (marker) {\n          this.pushMarkerTo(marker);\n        }\n      }\n    }\n    if (!optNoDraw) {\n      this.redraw();\n    }\n  };\n  Clusterer.prototype.pushMarkerTo = function (marker) {\n    var _this = this;\n    // If the marker is draggable add a listener so we can update the clusters on the dragend:\n    if (marker.getDraggable()) {\n      google.maps.event.addListener(marker, 'dragend', function () {\n        if (_this.ready) {\n          marker.isAdded = false;\n          _this.repaint();\n        }\n      });\n    }\n    marker.isAdded = false;\n    this.markers.push(marker);\n  };\n  Clusterer.prototype.removeMarker_ = function (marker) {\n    var index = -1;\n    if (this.markers.indexOf) {\n      index = this.markers.indexOf(marker);\n    } else {\n      for (var i = 0; i < this.markers.length; i++) {\n        if (marker === this.markers[i]) {\n          index = i;\n          break;\n        }\n      }\n    }\n    if (index === -1) {\n      // Marker is not in our list of markers, so do nothing:\n      return false;\n    }\n    marker.setMap(null);\n    this.markers.splice(index, 1); // Remove the marker from the list of managed markers\n    return true;\n  };\n  Clusterer.prototype.removeMarker = function (marker, optNoDraw) {\n    var removed = this.removeMarker_(marker);\n    if (!optNoDraw && removed) {\n      this.repaint();\n    }\n    return removed;\n  };\n  Clusterer.prototype.removeMarkers = function (markers, optNoDraw) {\n    var removed = false;\n    for (var _i = 0, markers_2 = markers; _i < markers_2.length; _i++) {\n      var marker = markers_2[_i];\n      removed = removed || this.removeMarker_(marker);\n    }\n    if (!optNoDraw && removed) {\n      this.repaint();\n    }\n    return removed;\n  };\n  Clusterer.prototype.clearMarkers = function () {\n    this.resetViewport(true);\n    this.markers = [];\n  };\n  Clusterer.prototype.repaint = function () {\n    var oldClusters = this.clusters.slice();\n    this.clusters = [];\n    this.resetViewport(false);\n    this.redraw();\n    // Remove the old clusters.\n    // Do it in a timeout to prevent blinking effect.\n    setTimeout(function timeout() {\n      for (var _i = 0, oldClusters_1 = oldClusters; _i < oldClusters_1.length; _i++) {\n        var oldCluster = oldClusters_1[_i];\n        oldCluster.remove();\n      }\n    }, 0);\n  };\n  Clusterer.prototype.getExtendedBounds = function (bounds) {\n    var projection = this.getProjection();\n    // Convert the points to pixels and the extend out by the grid size.\n    var trPix = projection.fromLatLngToDivPixel(\n    // Turn the bounds into latlng.\n    new google.maps.LatLng(bounds.getNorthEast().lat(), bounds.getNorthEast().lng()));\n    if (trPix !== null) {\n      trPix.x += this.gridSize;\n      trPix.y -= this.gridSize;\n    }\n    var blPix = projection.fromLatLngToDivPixel(\n    // Turn the bounds into latlng.\n    new google.maps.LatLng(bounds.getSouthWest().lat(), bounds.getSouthWest().lng()));\n    if (blPix !== null) {\n      blPix.x -= this.gridSize;\n      blPix.y += this.gridSize;\n    }\n    // Extend the bounds to contain the new bounds.\n    if (trPix !== null) {\n      // Convert the pixel points back to LatLng nw\n      var point1 = projection.fromDivPixelToLatLng(trPix);\n      if (point1 !== null) {\n        bounds.extend(point1);\n      }\n    }\n    if (blPix !== null) {\n      // Convert the pixel points back to LatLng sw\n      var point2 = projection.fromDivPixelToLatLng(blPix);\n      if (point2 !== null) {\n        bounds.extend(point2);\n      }\n    }\n    return bounds;\n  };\n  Clusterer.prototype.redraw = function () {\n    // Redraws all the clusters.\n    this.createClusters(0);\n  };\n  Clusterer.prototype.resetViewport = function (optHide) {\n    // Remove all the clusters\n    for (var _i = 0, _a = this.clusters; _i < _a.length; _i++) {\n      var cluster = _a[_i];\n      cluster.remove();\n    }\n    this.clusters = [];\n    // Reset the markers to not be added and to be removed from the map.\n    for (var _b = 0, _c = this.markers; _b < _c.length; _b++) {\n      var marker = _c[_b];\n      marker.isAdded = false;\n      if (optHide) {\n        marker.setMap(null);\n      }\n    }\n  };\n  Clusterer.prototype.distanceBetweenPoints = function (p1, p2) {\n    var R = 6371; // Radius of the Earth in km\n    var dLat = (p2.lat() - p1.lat()) * Math.PI / 180;\n    var dLon = (p2.lng() - p1.lng()) * Math.PI / 180;\n    var a = Math.sin(dLat / 2) * Math.sin(dLat / 2) + Math.cos(p1.lat() * Math.PI / 180) * Math.cos(p2.lat() * Math.PI / 180) * Math.sin(dLon / 2) * Math.sin(dLon / 2);\n    return R * (2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a)));\n  };\n  Clusterer.prototype.isMarkerInBounds = function (marker, bounds) {\n    var position = marker.getPosition();\n    if (position) {\n      return bounds.contains(position);\n    }\n    return false;\n  };\n  Clusterer.prototype.addToClosestCluster = function (marker) {\n    var cluster;\n    var distance = 40000; // Some large number\n    var clusterToAddTo = null;\n    for (var _i = 0, _a = this.clusters; _i < _a.length; _i++) {\n      var clusterElement = _a[_i];\n      cluster = clusterElement;\n      var center = cluster.getCenter();\n      var position = marker.getPosition();\n      if (center && position) {\n        var d = this.distanceBetweenPoints(center, position);\n        if (d < distance) {\n          distance = d;\n          clusterToAddTo = cluster;\n        }\n      }\n    }\n    if (clusterToAddTo && clusterToAddTo.isMarkerInClusterBounds(marker)) {\n      clusterToAddTo.addMarker(marker);\n    } else {\n      cluster = new Cluster$1(this);\n      cluster.addMarker(marker);\n      this.clusters.push(cluster);\n    }\n  };\n  Clusterer.prototype.createClusters = function (iFirst) {\n    var _this = this;\n    if (!this.ready) {\n      return;\n    }\n    // Cancel previous batch processing if we're working on the first batch:\n    if (iFirst === 0) {\n      /**\n       * This event is fired when the <code>Clusterer</code> begins\n       *  clustering markers.\n       * @name Clusterer#clusteringbegin\n       * @param {Clusterer} mc The Clusterer whose markers are being clustered.\n       * @event\n       */\n      google.maps.event.trigger(this, 'clusteringbegin', this);\n      if (this.timerRefStatic !== null) {\n        window.clearTimeout(this.timerRefStatic);\n        // eslint-disable-next-line @typescript-eslint/ban-ts-comment\n        // @ts-ignore\n        delete this.timerRefStatic;\n      }\n    }\n    var map = this.getMap();\n    var bounds = map !== null && 'getBounds' in map ? map.getBounds() : null;\n    var zoom = (map === null || map === void 0 ? void 0 : map.getZoom()) || 0;\n    // Get our current map view bounds.\n    // Create a new bounds object so we don't affect the map.\n    //\n    // See Comments 9 & 11 on Issue 3651 relating to this workaround for a Google Maps bug:\n    var mapBounds = zoom > 3 ? new google.maps.LatLngBounds(bounds === null || bounds === void 0 ? void 0 : bounds.getSouthWest(), bounds === null || bounds === void 0 ? void 0 : bounds.getNorthEast()) : new google.maps.LatLngBounds(new google.maps.LatLng(85.02070771743472, -178.48388434375), new google.maps.LatLng(-85.08136444384544, 178.00048865625));\n    var extendedMapBounds = this.getExtendedBounds(mapBounds);\n    var iLast = Math.min(iFirst + this.batchSize, this.markers.length);\n    for (var i = iFirst; i < iLast; i++) {\n      var marker = this.markers[i];\n      if (marker && !marker.isAdded && this.isMarkerInBounds(marker, extendedMapBounds) && (!this.ignoreHidden || this.ignoreHidden && marker.getVisible())) {\n        this.addToClosestCluster(marker);\n      }\n    }\n    if (iLast < this.markers.length) {\n      this.timerRefStatic = window.setTimeout(function () {\n        _this.createClusters(iLast);\n      }, 0);\n    } else {\n      this.timerRefStatic = null;\n      /**\n       * This event is fired when the <code>Clusterer</code> stops\n       *  clustering markers.\n       * @name Clusterer#clusteringend\n       * @param {Clusterer} mc The Clusterer whose markers are being clustered.\n       * @event\n       */\n      google.maps.event.trigger(this, 'clusteringend', this);\n      for (var _i = 0, _a = this.clusters; _i < _a.length; _i++) {\n        var cluster = _a[_i];\n        cluster.updateIcon();\n      }\n    }\n  };\n  Clusterer.prototype.extend = function (obj1, obj2) {\n    return function applyExtend(object) {\n      for (var property in object.prototype) {\n        // eslint-disable-next-line @typescript-eslint/ban-types\n        var prop = property;\n        // eslint-disable-next-line @typescript-eslint/ban-ts-comment\n        // @ts-ignore\n        this.prototype[prop] = object.prototype[prop];\n      }\n      return this;\n    }.apply(obj1, [obj2]);\n  };\n  return Clusterer;\n}();\n\nfunction ownKeys$c(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }\nfunction _objectSpread$c(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys$c(Object(t), !0).forEach(function (r) { _defineProperty(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys$c(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }\nvar eventMap$e = {\n  onClick: 'click',\n  onClusteringBegin: 'clusteringbegin',\n  onClusteringEnd: 'clusteringend',\n  onMouseOut: 'mouseout',\n  onMouseOver: 'mouseover'\n};\nvar updaterMap$e = {\n  averageCenter(instance, averageCenter) {\n    instance.setAverageCenter(averageCenter);\n  },\n  batchSizeIE(instance, batchSizeIE) {\n    instance.setBatchSizeIE(batchSizeIE);\n  },\n  calculator(instance, calculator) {\n    instance.setCalculator(calculator);\n  },\n  clusterClass(instance, clusterClass) {\n    instance.setClusterClass(clusterClass);\n  },\n  enableRetinaIcons(instance, enableRetinaIcons) {\n    instance.setEnableRetinaIcons(enableRetinaIcons);\n  },\n  gridSize(instance, gridSize) {\n    instance.setGridSize(gridSize);\n  },\n  ignoreHidden(instance, ignoreHidden) {\n    instance.setIgnoreHidden(ignoreHidden);\n  },\n  imageExtension(instance, imageExtension) {\n    instance.setImageExtension(imageExtension);\n  },\n  imagePath(instance, imagePath) {\n    instance.setImagePath(imagePath);\n  },\n  imageSizes(instance, imageSizes) {\n    instance.setImageSizes(imageSizes);\n  },\n  maxZoom(instance, maxZoom) {\n    instance.setMaxZoom(maxZoom);\n  },\n  minimumClusterSize(instance, minimumClusterSize) {\n    instance.setMinimumClusterSize(minimumClusterSize);\n  },\n  styles(instance, styles) {\n    instance.setStyles(styles);\n  },\n  title(instance, title) {\n    instance.setTitle(title);\n  },\n  zoomOnClick(instance, zoomOnClick) {\n    instance.setZoomOnClick(zoomOnClick);\n  }\n};\nvar defaultOptions$4 = {};\nfunction MarkerClustererFunctional(props) {\n  var {\n    children,\n    options,\n    averageCenter,\n    batchSizeIE,\n    calculator,\n    clusterClass,\n    enableRetinaIcons,\n    gridSize,\n    ignoreHidden,\n    imageExtension,\n    imagePath,\n    imageSizes,\n    maxZoom,\n    minimumClusterSize,\n    styles,\n    title,\n    zoomOnClick,\n    onClick,\n    onClusteringBegin,\n    onClusteringEnd,\n    onMouseOver,\n    onMouseOut,\n    onLoad,\n    onUnmount\n  } = props;\n  var [instance, setInstance] = useState(null);\n  var map = useContext(MapContext);\n  var [clickListener, setClickListener] = useState(null);\n  var [clusteringBeginListener, setClusteringBeginListener] = useState(null);\n  var [clusteringEndListener, setClusteringEndListener] = useState(null);\n  var [mouseoutListener, setMouseoutListener] = useState(null);\n  var [mouseoverListener, setMouseoverListener] = useState(null);\n  useEffect(() => {\n    if (instance && onMouseOut) {\n      if (mouseoutListener !== null) {\n        google.maps.event.removeListener(mouseoutListener);\n      }\n      setMouseoutListener(google.maps.event.addListener(instance, eventMap$e.onMouseOut, onMouseOut));\n    }\n  }, [onMouseOut]);\n  useEffect(() => {\n    if (instance && onMouseOver) {\n      if (mouseoverListener !== null) {\n        google.maps.event.removeListener(mouseoverListener);\n      }\n      setMouseoverListener(google.maps.event.addListener(instance, eventMap$e.onMouseOver, onMouseOver));\n    }\n  }, [onMouseOver]);\n  useEffect(() => {\n    if (instance && onClick) {\n      if (clickListener !== null) {\n        google.maps.event.removeListener(clickListener);\n      }\n      setClickListener(google.maps.event.addListener(instance, eventMap$e.onClick, onClick));\n    }\n  }, [onClick]);\n  useEffect(() => {\n    if (instance && onClusteringBegin) {\n      if (clusteringBeginListener !== null) {\n        google.maps.event.removeListener(clusteringBeginListener);\n      }\n      setClusteringBeginListener(google.maps.event.addListener(instance, eventMap$e.onClusteringBegin, onClusteringBegin));\n    }\n  }, [onClusteringBegin]);\n  useEffect(() => {\n    if (instance && onClusteringEnd) {\n      if (clusteringEndListener !== null) {\n        google.maps.event.removeListener(clusteringEndListener);\n      }\n      setClusteringBeginListener(google.maps.event.addListener(instance, eventMap$e.onClusteringEnd, onClusteringEnd));\n    }\n  }, [onClusteringEnd]);\n  useEffect(() => {\n    if (typeof averageCenter !== 'undefined' && instance !== null) {\n      updaterMap$e.averageCenter(instance, averageCenter);\n    }\n  }, [instance, averageCenter]);\n  useEffect(() => {\n    if (typeof batchSizeIE !== 'undefined' && instance !== null) {\n      updaterMap$e.batchSizeIE(instance, batchSizeIE);\n    }\n  }, [instance, batchSizeIE]);\n  useEffect(() => {\n    if (typeof calculator !== 'undefined' && instance !== null) {\n      updaterMap$e.calculator(instance, calculator);\n    }\n  }, [instance, calculator]);\n  useEffect(() => {\n    if (typeof clusterClass !== 'undefined' && instance !== null) {\n      updaterMap$e.clusterClass(instance, clusterClass);\n    }\n  }, [instance, clusterClass]);\n  useEffect(() => {\n    if (typeof enableRetinaIcons !== 'undefined' && instance !== null) {\n      updaterMap$e.enableRetinaIcons(instance, enableRetinaIcons);\n    }\n  }, [instance, enableRetinaIcons]);\n  useEffect(() => {\n    if (typeof gridSize !== 'undefined' && instance !== null) {\n      updaterMap$e.gridSize(instance, gridSize);\n    }\n  }, [instance, gridSize]);\n  useEffect(() => {\n    if (typeof ignoreHidden !== 'undefined' && instance !== null) {\n      updaterMap$e.ignoreHidden(instance, ignoreHidden);\n    }\n  }, [instance, ignoreHidden]);\n  useEffect(() => {\n    if (typeof imageExtension !== 'undefined' && instance !== null) {\n      updaterMap$e.imageExtension(instance, imageExtension);\n    }\n  }, [instance, imageExtension]);\n  useEffect(() => {\n    if (typeof imagePath !== 'undefined' && instance !== null) {\n      updaterMap$e.imagePath(instance, imagePath);\n    }\n  }, [instance, imagePath]);\n  useEffect(() => {\n    if (typeof imageSizes !== 'undefined' && instance !== null) {\n      updaterMap$e.imageSizes(instance, imageSizes);\n    }\n  }, [instance, imageSizes]);\n  useEffect(() => {\n    if (typeof maxZoom !== 'undefined' && instance !== null) {\n      updaterMap$e.maxZoom(instance, maxZoom);\n    }\n  }, [instance, maxZoom]);\n  useEffect(() => {\n    if (typeof minimumClusterSize !== 'undefined' && instance !== null) {\n      updaterMap$e.minimumClusterSize(instance, minimumClusterSize);\n    }\n  }, [instance, minimumClusterSize]);\n  useEffect(() => {\n    if (typeof styles !== 'undefined' && instance !== null) {\n      updaterMap$e.styles(instance, styles);\n    }\n  }, [instance, styles]);\n  useEffect(() => {\n    if (typeof title !== 'undefined' && instance !== null) {\n      updaterMap$e.title(instance, title);\n    }\n  }, [instance, title]);\n  useEffect(() => {\n    if (typeof zoomOnClick !== 'undefined' && instance !== null) {\n      updaterMap$e.zoomOnClick(instance, zoomOnClick);\n    }\n  }, [instance, zoomOnClick]);\n  useEffect(() => {\n    if (!map) return;\n    var clustererOptions = _objectSpread$c({}, options || defaultOptions$4);\n    var clusterer = new Clusterer(map, [], clustererOptions);\n    if (averageCenter) {\n      updaterMap$e.averageCenter(clusterer, averageCenter);\n    }\n    if (batchSizeIE) {\n      updaterMap$e.batchSizeIE(clusterer, batchSizeIE);\n    }\n    if (calculator) {\n      updaterMap$e.calculator(clusterer, calculator);\n    }\n    if (clusterClass) {\n      updaterMap$e.clusterClass(clusterer, clusterClass);\n    }\n    if (enableRetinaIcons) {\n      updaterMap$e.enableRetinaIcons(clusterer, enableRetinaIcons);\n    }\n    if (gridSize) {\n      updaterMap$e.gridSize(clusterer, gridSize);\n    }\n    if (ignoreHidden) {\n      updaterMap$e.ignoreHidden(clusterer, ignoreHidden);\n    }\n    if (imageExtension) {\n      updaterMap$e.imageExtension(clusterer, imageExtension);\n    }\n    if (imagePath) {\n      updaterMap$e.imagePath(clusterer, imagePath);\n    }\n    if (imageSizes) {\n      updaterMap$e.imageSizes(clusterer, imageSizes);\n    }\n    if (maxZoom) {\n      updaterMap$e.maxZoom(clusterer, maxZoom);\n    }\n    if (minimumClusterSize) {\n      updaterMap$e.minimumClusterSize(clusterer, minimumClusterSize);\n    }\n    if (styles) {\n      updaterMap$e.styles(clusterer, styles);\n    }\n    if (title) {\n      updaterMap$e.title(clusterer, title);\n    }\n    if (zoomOnClick) {\n      updaterMap$e.zoomOnClick(clusterer, zoomOnClick);\n    }\n    if (onMouseOut) {\n      setMouseoutListener(google.maps.event.addListener(clusterer, eventMap$e.onMouseOut, onMouseOut));\n    }\n    if (onMouseOver) {\n      setMouseoverListener(google.maps.event.addListener(clusterer, eventMap$e.onMouseOver, onMouseOver));\n    }\n    if (onClick) {\n      setClickListener(google.maps.event.addListener(clusterer, eventMap$e.onClick, onClick));\n    }\n    if (onClusteringBegin) {\n      setClusteringBeginListener(google.maps.event.addListener(clusterer, eventMap$e.onClusteringBegin, onClusteringBegin));\n    }\n    if (onClusteringEnd) {\n      setClusteringEndListener(google.maps.event.addListener(clusterer, eventMap$e.onClusteringEnd, onClusteringEnd));\n    }\n    setInstance(clusterer);\n    if (onLoad) {\n      onLoad(clusterer);\n    }\n    return () => {\n      if (mouseoutListener !== null) {\n        google.maps.event.removeListener(mouseoutListener);\n      }\n      if (mouseoverListener !== null) {\n        google.maps.event.removeListener(mouseoverListener);\n      }\n      if (clickListener !== null) {\n        google.maps.event.removeListener(clickListener);\n      }\n      if (clusteringBeginListener !== null) {\n        google.maps.event.removeListener(clusteringBeginListener);\n      }\n      if (clusteringEndListener !== null) {\n        google.maps.event.removeListener(clusteringEndListener);\n      }\n      if (onUnmount) {\n        onUnmount(clusterer);\n      }\n    };\n  }, []);\n  return instance !== null ? children(instance) || null : null;\n}\nvar MarkerClustererF = memo(MarkerClustererFunctional);\nclass ClustererComponent extends PureComponent {\n  constructor() {\n    super(...arguments);\n    _defineProperty(this, \"registeredEvents\", []);\n    _defineProperty(this, \"state\", {\n      markerClusterer: null\n    });\n    _defineProperty(this, \"setClustererCallback\", () => {\n      if (this.state.markerClusterer !== null && this.props.onLoad) {\n        this.props.onLoad(this.state.markerClusterer);\n      }\n    });\n  }\n  componentDidMount() {\n    if (this.context) {\n      var markerClusterer = new Clusterer(this.context, [], this.props.options);\n      this.registeredEvents = applyUpdatersToPropsAndRegisterEvents({\n        updaterMap: updaterMap$e,\n        eventMap: eventMap$e,\n        prevProps: {},\n        nextProps: this.props,\n        instance: markerClusterer\n      });\n      this.setState(() => {\n        return {\n          markerClusterer\n        };\n      }, this.setClustererCallback);\n    }\n  }\n  componentDidUpdate(prevProps) {\n    if (this.state.markerClusterer) {\n      unregisterEvents(this.registeredEvents);\n      this.registeredEvents = applyUpdatersToPropsAndRegisterEvents({\n        updaterMap: updaterMap$e,\n        eventMap: eventMap$e,\n        prevProps,\n        nextProps: this.props,\n        instance: this.state.markerClusterer\n      });\n    }\n  }\n  componentWillUnmount() {\n    if (this.state.markerClusterer !== null) {\n      if (this.props.onUnmount) {\n        this.props.onUnmount(this.state.markerClusterer);\n      }\n      unregisterEvents(this.registeredEvents);\n      // eslint-disable-next-line @typescript-eslint/ban-ts-comment\n      // @ts-ignore\n      this.state.markerClusterer.setMap(null);\n    }\n  }\n  render() {\n    return this.state.markerClusterer !== null ? this.props.children(this.state.markerClusterer) : null;\n  }\n}\n_defineProperty(ClustererComponent, \"contextType\", MapContext);\n\n// This handler prevents an event in the InfoBox from being passed on to the map.\nfunction cancelHandler(event) {\n  event.cancelBubble = true;\n  if (event.stopPropagation) {\n    event.stopPropagation();\n  }\n}\nvar InfoBox = /** @class */function () {\n  function InfoBox(options) {\n    if (options === void 0) {\n      options = {};\n    }\n    this.getCloseClickHandler = this.getCloseClickHandler.bind(this);\n    this.closeClickHandler = this.closeClickHandler.bind(this);\n    this.createInfoBoxDiv = this.createInfoBoxDiv.bind(this);\n    this.addClickHandler = this.addClickHandler.bind(this);\n    this.getCloseBoxImg = this.getCloseBoxImg.bind(this);\n    this.getBoxWidths = this.getBoxWidths.bind(this);\n    this.setBoxStyle = this.setBoxStyle.bind(this);\n    this.setPosition = this.setPosition.bind(this);\n    this.getPosition = this.getPosition.bind(this);\n    this.setOptions = this.setOptions.bind(this);\n    this.setContent = this.setContent.bind(this);\n    this.setVisible = this.setVisible.bind(this);\n    this.getContent = this.getContent.bind(this);\n    this.getVisible = this.getVisible.bind(this);\n    this.setZIndex = this.setZIndex.bind(this);\n    this.getZIndex = this.getZIndex.bind(this);\n    this.onRemove = this.onRemove.bind(this);\n    this.panBox = this.panBox.bind(this);\n    this.extend = this.extend.bind(this);\n    this.close = this.close.bind(this);\n    this.draw = this.draw.bind(this);\n    this.show = this.show.bind(this);\n    this.hide = this.hide.bind(this);\n    this.open = this.open.bind(this);\n    this.extend(InfoBox, google.maps.OverlayView);\n    // Standard options (in common with google.maps.InfoWindow):\n    this.content = options.content || '';\n    this.disableAutoPan = options.disableAutoPan || false;\n    this.maxWidth = options.maxWidth || 0;\n    this.pixelOffset = options.pixelOffset || new google.maps.Size(0, 0);\n    this.position = options.position || new google.maps.LatLng(0, 0);\n    this.zIndex = options.zIndex || null;\n    // Additional options (unique to InfoBox):\n    this.boxClass = options.boxClass || 'infoBox';\n    this.boxStyle = options.boxStyle || {};\n    this.closeBoxMargin = options.closeBoxMargin || '2px';\n    this.closeBoxURL = options.closeBoxURL || 'http://www.google.com/intl/en_us/mapfiles/close.gif';\n    if (options.closeBoxURL === '') {\n      this.closeBoxURL = '';\n    }\n    this.infoBoxClearance = options.infoBoxClearance || new google.maps.Size(1, 1);\n    if (typeof options.visible === 'undefined') {\n      if (typeof options.isHidden === 'undefined') {\n        options.visible = true;\n      } else {\n        options.visible = !options.isHidden;\n      }\n    }\n    this.isHidden = !options.visible;\n    this.alignBottom = options.alignBottom || false;\n    this.pane = options.pane || 'floatPane';\n    this.enableEventPropagation = options.enableEventPropagation || false;\n    this.div = null;\n    this.closeListener = null;\n    this.moveListener = null;\n    this.mapListener = null;\n    this.contextListener = null;\n    this.eventListeners = null;\n    this.fixedWidthSet = null;\n  }\n  InfoBox.prototype.createInfoBoxDiv = function () {\n    var _this = this;\n    // This handler ignores the current event in the InfoBox and conditionally prevents\n    // the event from being passed on to the map. It is used for the contextmenu event.\n    var ignoreHandler = function ignoreHandler(event) {\n      event.returnValue = false;\n      if (event.preventDefault) {\n        event.preventDefault();\n      }\n      if (!_this.enableEventPropagation) {\n        cancelHandler(event);\n      }\n    };\n    if (!this.div) {\n      this.div = document.createElement('div');\n      this.setBoxStyle();\n      if (typeof this.content === 'string') {\n        this.div.innerHTML = this.getCloseBoxImg() + this.content;\n      } else {\n        this.div.innerHTML = this.getCloseBoxImg();\n        this.div.appendChild(this.content);\n      }\n      var panes = this.getPanes();\n      if (panes !== null) {\n        panes[this.pane].appendChild(this.div); // Add the InfoBox div to the DOM\n      }\n      this.addClickHandler();\n      if (this.div.style.width) {\n        this.fixedWidthSet = true;\n      } else {\n        if (this.maxWidth !== 0 && this.div.offsetWidth > this.maxWidth) {\n          this.div.style.width = this.maxWidth + 'px';\n          this.fixedWidthSet = true;\n        } else {\n          // The following code is needed to overcome problems with MSIE\n          var bw = this.getBoxWidths();\n          this.div.style.width = this.div.offsetWidth - bw.left - bw.right + 'px';\n          this.fixedWidthSet = false;\n        }\n      }\n      this.panBox(this.disableAutoPan);\n      if (!this.enableEventPropagation) {\n        this.eventListeners = [];\n        // Cancel event propagation.\n        // Note: mousemove not included (to resolve Issue 152)\n        var events = ['mousedown', 'mouseover', 'mouseout', 'mouseup', 'click', 'dblclick', 'touchstart', 'touchend', 'touchmove'];\n        for (var _i = 0, events_1 = events; _i < events_1.length; _i++) {\n          var event_1 = events_1[_i];\n          this.eventListeners.push(google.maps.event.addListener(this.div, event_1, cancelHandler));\n        }\n        // Workaround for Google bug that causes the cursor to change to a pointer\n        // when the mouse moves over a marker underneath InfoBox.\n        this.eventListeners.push(google.maps.event.addListener(this.div, 'mouseover', function () {\n          if (_this.div) {\n            _this.div.style.cursor = 'default';\n          }\n        }));\n      }\n      this.contextListener = google.maps.event.addListener(this.div, 'contextmenu', ignoreHandler);\n      /**\n       * This event is fired when the DIV containing the InfoBox's content is attached to the DOM.\n       * @name InfoBox#domready\n       * @event\n       */\n      google.maps.event.trigger(this, 'domready');\n    }\n  };\n  InfoBox.prototype.getCloseBoxImg = function () {\n    var img = '';\n    if (this.closeBoxURL !== '') {\n      img = '<img alt=\"\"';\n      img += ' aria-hidden=\"true\"';\n      img += \" src='\" + this.closeBoxURL + \"'\";\n      img += ' align=right'; // Do this because Opera chokes on style='float: right;'\n      img += \" style='\";\n      img += ' position: relative;'; // Required by MSIE\n      img += ' cursor: pointer;';\n      img += ' margin: ' + this.closeBoxMargin + ';';\n      img += \"'>\";\n    }\n    return img;\n  };\n  InfoBox.prototype.addClickHandler = function () {\n    this.closeListener = this.div && this.div.firstChild && this.closeBoxURL !== '' ? google.maps.event.addListener(this.div.firstChild, 'click', this.getCloseClickHandler()) : null;\n  };\n  InfoBox.prototype.closeClickHandler = function (event) {\n    // 1.0.3 fix: Always prevent propagation of a close box click to the map:\n    event.cancelBubble = true;\n    if (event.stopPropagation) {\n      event.stopPropagation();\n    }\n    /**\n     * This event is fired when the InfoBox's close box is clicked.\n     * @name InfoBox#closeclick\n     * @event\n     */\n    google.maps.event.trigger(this, 'closeclick');\n    this.close();\n  };\n  InfoBox.prototype.getCloseClickHandler = function () {\n    return this.closeClickHandler;\n  };\n  InfoBox.prototype.panBox = function (disablePan) {\n    if (this.div && !disablePan) {\n      // eslint-disable-next-line @typescript-eslint/ban-ts-comment\n      // @ts-ignore\n      var map = this.getMap();\n      // Only pan if attached to map, not panorama\n      if (map instanceof google.maps.Map) {\n        var xOffset = 0;\n        var yOffset = 0;\n        var bounds = map.getBounds();\n        if (bounds && !bounds.contains(this.position)) {\n          // Marker not in visible area of map, so set center\n          // of map to the marker position first.\n          map.setCenter(this.position);\n        }\n        var mapDiv = map.getDiv();\n        // eslint-disable-next-line @typescript-eslint/ban-ts-comment\n        // @ts-ignore\n        var mapWidth = mapDiv.offsetWidth;\n        // eslint-disable-next-line @typescript-eslint/ban-ts-comment\n        // @ts-ignore\n        var mapHeight = mapDiv.offsetHeight;\n        var iwOffsetX = this.pixelOffset.width;\n        var iwOffsetY = this.pixelOffset.height;\n        var iwWidth = this.div.offsetWidth;\n        var iwHeight = this.div.offsetHeight;\n        var padX = this.infoBoxClearance.width;\n        var padY = this.infoBoxClearance.height;\n        // eslint-disable-next-line @typescript-eslint/ban-ts-comment\n        // @ts-ignore\n        var projection = this.getProjection();\n        var pixPosition = projection.fromLatLngToContainerPixel(this.position);\n        if (pixPosition !== null) {\n          if (pixPosition.x < -iwOffsetX + padX) {\n            xOffset = pixPosition.x + iwOffsetX - padX;\n          } else if (pixPosition.x + iwWidth + iwOffsetX + padX > mapWidth) {\n            xOffset = pixPosition.x + iwWidth + iwOffsetX + padX - mapWidth;\n          }\n          if (this.alignBottom) {\n            if (pixPosition.y < -iwOffsetY + padY + iwHeight) {\n              yOffset = pixPosition.y + iwOffsetY - padY - iwHeight;\n            } else if (pixPosition.y + iwOffsetY + padY > mapHeight) {\n              yOffset = pixPosition.y + iwOffsetY + padY - mapHeight;\n            }\n          } else {\n            if (pixPosition.y < -iwOffsetY + padY) {\n              yOffset = pixPosition.y + iwOffsetY - padY;\n            } else if (pixPosition.y + iwHeight + iwOffsetY + padY > mapHeight) {\n              yOffset = pixPosition.y + iwHeight + iwOffsetY + padY - mapHeight;\n            }\n          }\n        }\n        if (!(xOffset === 0 && yOffset === 0)) {\n          // Move the map to the shifted center.\n          map.panBy(xOffset, yOffset);\n        }\n      }\n    }\n  };\n  InfoBox.prototype.setBoxStyle = function () {\n    if (this.div) {\n      // Apply style values from the style sheet defined in the boxClass parameter:\n      this.div.className = this.boxClass;\n      // Clear existing inline style values:\n      this.div.style.cssText = '';\n      // Apply style values defined in the boxStyle parameter:\n      var boxStyle = this.boxStyle;\n      for (var i in boxStyle) {\n        if (Object.prototype.hasOwnProperty.call(boxStyle, i)) {\n          // eslint-disable-next-line @typescript-eslint/ban-ts-comment\n          // @ts-ignore\n          this.div.style[i] = boxStyle[i];\n        }\n      }\n      // Fix for iOS disappearing InfoBox problem\n      // See http://stackoverflow.com/questions/9229535/google-maps-markers-disappear-at-certain-zoom-level-only-on-iphone-ipad\n      this.div.style.webkitTransform = 'translateZ(0)';\n      // Fix up opacity style for benefit of MSIE\n      if (typeof this.div.style.opacity !== 'undefined' && this.div.style.opacity !== '') {\n        // See http://www.quirksmode.org/css/opacity.html\n        var opacity = parseFloat(this.div.style.opacity || '');\n        // eslint-disable-next-line @typescript-eslint/ban-ts-comment\n        // @ts-ignore\n        this.div.style.msFilter = '\"progid:DXImageTransform.Microsoft.Alpha(Opacity=' + opacity * 100 + ')\"';\n        this.div.style.filter = 'alpha(opacity=' + opacity * 100 + ')';\n      }\n      // Apply required styles\n      this.div.style.position = 'absolute';\n      this.div.style.visibility = 'hidden';\n      if (this.zIndex !== null) {\n        this.div.style.zIndex = this.zIndex + '';\n      }\n      if (!this.div.style.overflow) {\n        this.div.style.overflow = 'auto';\n      }\n    }\n  };\n  InfoBox.prototype.getBoxWidths = function () {\n    var bw = {\n      top: 0,\n      bottom: 0,\n      left: 0,\n      right: 0\n    };\n    if (!this.div) {\n      return bw;\n    }\n    if (document.defaultView) {\n      var ownerDocument = this.div.ownerDocument;\n      var computedStyle = ownerDocument && ownerDocument.defaultView ? ownerDocument.defaultView.getComputedStyle(this.div, '') : null;\n      if (computedStyle) {\n        // The computed styles are always in pixel units (good!)\n        bw.top = parseInt(computedStyle.borderTopWidth || '', 10) || 0;\n        bw.bottom = parseInt(computedStyle.borderBottomWidth || '', 10) || 0;\n        bw.left = parseInt(computedStyle.borderLeftWidth || '', 10) || 0;\n        bw.right = parseInt(computedStyle.borderRightWidth || '', 10) || 0;\n      }\n    } else if (\n    // eslint-disable-next-line @typescript-eslint/ban-ts-comment\n    // @ts-ignore\n    document.documentElement.currentStyle // MSIE\n    ) {\n      // eslint-disable-next-line @typescript-eslint/ban-ts-comment\n      // @ts-ignore\n      var currentStyle = this.div.currentStyle;\n      if (currentStyle) {\n        // The current styles may not be in pixel units, but assume they are (bad!)\n        bw.top = parseInt(currentStyle.borderTopWidth || '', 10) || 0;\n        bw.bottom = parseInt(currentStyle.borderBottomWidth || '', 10) || 0;\n        bw.left = parseInt(currentStyle.borderLeftWidth || '', 10) || 0;\n        bw.right = parseInt(currentStyle.borderRightWidth || '', 10) || 0;\n      }\n    }\n    return bw;\n  };\n  InfoBox.prototype.onRemove = function () {\n    if (this.div && this.div.parentNode) {\n      this.div.parentNode.removeChild(this.div);\n      this.div = null;\n    }\n  };\n  InfoBox.prototype.draw = function () {\n    this.createInfoBoxDiv();\n    if (this.div) {\n      // eslint-disable-next-line @typescript-eslint/ban-ts-comment\n      // @ts-ignore\n      var projection = this.getProjection();\n      var pixPosition = projection.fromLatLngToDivPixel(this.position);\n      if (pixPosition !== null) {\n        this.div.style.left = pixPosition.x + this.pixelOffset.width + 'px';\n        if (this.alignBottom) {\n          this.div.style.bottom = -(pixPosition.y + this.pixelOffset.height) + 'px';\n        } else {\n          this.div.style.top = pixPosition.y + this.pixelOffset.height + 'px';\n        }\n      }\n      if (this.isHidden) {\n        this.div.style.visibility = 'hidden';\n      } else {\n        this.div.style.visibility = 'visible';\n      }\n    }\n  };\n  InfoBox.prototype.setOptions = function (options) {\n    if (options === void 0) {\n      options = {};\n    }\n    if (typeof options.boxClass !== 'undefined') {\n      // Must be first\n      this.boxClass = options.boxClass;\n      this.setBoxStyle();\n    }\n    if (typeof options.boxStyle !== 'undefined') {\n      // Must be second\n      this.boxStyle = options.boxStyle;\n      this.setBoxStyle();\n    }\n    if (typeof options.content !== 'undefined') {\n      this.setContent(options.content);\n    }\n    if (typeof options.disableAutoPan !== 'undefined') {\n      this.disableAutoPan = options.disableAutoPan;\n    }\n    if (typeof options.maxWidth !== 'undefined') {\n      this.maxWidth = options.maxWidth;\n    }\n    if (typeof options.pixelOffset !== 'undefined') {\n      this.pixelOffset = options.pixelOffset;\n    }\n    if (typeof options.alignBottom !== 'undefined') {\n      this.alignBottom = options.alignBottom;\n    }\n    if (typeof options.position !== 'undefined') {\n      this.setPosition(options.position);\n    }\n    if (typeof options.zIndex !== 'undefined') {\n      this.setZIndex(options.zIndex);\n    }\n    if (typeof options.closeBoxMargin !== 'undefined') {\n      this.closeBoxMargin = options.closeBoxMargin;\n    }\n    if (typeof options.closeBoxURL !== 'undefined') {\n      this.closeBoxURL = options.closeBoxURL;\n    }\n    if (typeof options.infoBoxClearance !== 'undefined') {\n      this.infoBoxClearance = options.infoBoxClearance;\n    }\n    if (typeof options.isHidden !== 'undefined') {\n      this.isHidden = options.isHidden;\n    }\n    if (typeof options.visible !== 'undefined') {\n      this.isHidden = !options.visible;\n    }\n    if (typeof options.enableEventPropagation !== 'undefined') {\n      this.enableEventPropagation = options.enableEventPropagation;\n    }\n    if (this.div) {\n      this.draw();\n    }\n  };\n  InfoBox.prototype.setContent = function (content) {\n    this.content = content;\n    if (this.div) {\n      if (this.closeListener) {\n        google.maps.event.removeListener(this.closeListener);\n        this.closeListener = null;\n      }\n      // Odd code required to make things work with MSIE.\n      if (!this.fixedWidthSet) {\n        this.div.style.width = '';\n      }\n      if (typeof content === 'string') {\n        this.div.innerHTML = this.getCloseBoxImg() + content;\n      } else {\n        this.div.innerHTML = this.getCloseBoxImg();\n        this.div.appendChild(content);\n      }\n      // Perverse code required to make things work with MSIE.\n      // (Ensures the close box does, in fact, float to the right.)\n      if (!this.fixedWidthSet) {\n        this.div.style.width = this.div.offsetWidth + 'px';\n        if (typeof content === 'string') {\n          this.div.innerHTML = this.getCloseBoxImg() + content;\n        } else {\n          this.div.innerHTML = this.getCloseBoxImg();\n          this.div.appendChild(content);\n        }\n      }\n      this.addClickHandler();\n    }\n    /**\n     * This event is fired when the content of the InfoBox changes.\n     * @name InfoBox#content_changed\n     * @event\n     */\n    google.maps.event.trigger(this, 'content_changed');\n  };\n  InfoBox.prototype.setPosition = function (latLng) {\n    this.position = latLng;\n    if (this.div) {\n      this.draw();\n    }\n    /**\n     * This event is fired when the position of the InfoBox changes.\n     * @name InfoBox#position_changed\n     * @event\n     */\n    google.maps.event.trigger(this, 'position_changed');\n  };\n  InfoBox.prototype.setVisible = function (isVisible) {\n    this.isHidden = !isVisible;\n    if (this.div) {\n      this.div.style.visibility = this.isHidden ? 'hidden' : 'visible';\n    }\n  };\n  InfoBox.prototype.setZIndex = function (index) {\n    this.zIndex = index;\n    if (this.div) {\n      this.div.style.zIndex = index + '';\n    }\n    /**\n     * This event is fired when the zIndex of the InfoBox changes.\n     * @name InfoBox#zindex_changed\n     * @event\n     */\n    google.maps.event.trigger(this, 'zindex_changed');\n  };\n  InfoBox.prototype.getContent = function () {\n    return this.content;\n  };\n  InfoBox.prototype.getPosition = function () {\n    return this.position;\n  };\n  InfoBox.prototype.getZIndex = function () {\n    return this.zIndex;\n  };\n  InfoBox.prototype.getVisible = function () {\n    var map = this.getMap();\n    return typeof map === 'undefined' || map === null ? false : !this.isHidden;\n  };\n  InfoBox.prototype.show = function () {\n    this.isHidden = false;\n    if (this.div) {\n      this.div.style.visibility = 'visible';\n    }\n  };\n  InfoBox.prototype.hide = function () {\n    this.isHidden = true;\n    if (this.div) {\n      this.div.style.visibility = 'hidden';\n    }\n  };\n  InfoBox.prototype.open = function (map, anchor) {\n    var _this = this;\n    if (anchor) {\n      // eslint-disable-next-line @typescript-eslint/ban-ts-comment\n      // @ts-ignore\n      this.position = anchor.getPosition();\n      this.moveListener = google.maps.event.addListener(anchor, 'position_changed', function () {\n        // eslint-disable-next-line @typescript-eslint/ban-ts-comment\n        // @ts-ignore\n        var position = anchor.getPosition();\n        _this.setPosition(position);\n      });\n      this.mapListener = google.maps.event.addListener(anchor, 'map_changed', function () {\n        // eslint-disable-next-line @typescript-eslint/ban-ts-comment\n        // @ts-ignore\n        _this.setMap(anchor.map);\n      });\n    }\n    this.setMap(map);\n    if (this.div) {\n      this.panBox();\n    }\n  };\n  InfoBox.prototype.close = function () {\n    if (this.closeListener) {\n      google.maps.event.removeListener(this.closeListener);\n      this.closeListener = null;\n    }\n    if (this.eventListeners) {\n      for (var _i = 0, _a = this.eventListeners; _i < _a.length; _i++) {\n        var eventListener = _a[_i];\n        google.maps.event.removeListener(eventListener);\n      }\n      this.eventListeners = null;\n    }\n    if (this.moveListener) {\n      google.maps.event.removeListener(this.moveListener);\n      this.moveListener = null;\n    }\n    if (this.mapListener) {\n      google.maps.event.removeListener(this.mapListener);\n      this.mapListener = null;\n    }\n    if (this.contextListener) {\n      google.maps.event.removeListener(this.contextListener);\n      this.contextListener = null;\n    }\n    // eslint-disable-next-line @typescript-eslint/ban-ts-comment\n    // @ts-ignore\n    this.setMap(null);\n  };\n  InfoBox.prototype.extend = function (obj1, obj2) {\n    return function applyExtend(object) {\n      for (var property in object.prototype) {\n        if (!Object.prototype.hasOwnProperty.call(this, property)) {\n          // eslint-disable-next-line @typescript-eslint/ban-ts-comment\n          // @ts-ignore\n          this.prototype[property] = object.prototype[property];\n        }\n      }\n      return this;\n    }.apply(obj1, [obj2]);\n  };\n  return InfoBox;\n}();\n\nvar _excluded = [\"position\"],\n  _excluded2 = [\"position\"];\nfunction ownKeys$b(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }\nfunction _objectSpread$b(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys$b(Object(t), !0).forEach(function (r) { _defineProperty(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys$b(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }\nvar eventMap$d = {\n  onCloseClick: 'closeclick',\n  onContentChanged: 'content_changed',\n  onDomReady: 'domready',\n  onPositionChanged: 'position_changed',\n  onZindexChanged: 'zindex_changed'\n};\nvar updaterMap$d = {\n  options(instance, options) {\n    instance.setOptions(options);\n  },\n  position(instance, position) {\n    if (position instanceof google.maps.LatLng) {\n      instance.setPosition(position);\n    } else {\n      instance.setPosition(new google.maps.LatLng(position.lat, position.lng));\n    }\n  },\n  visible(instance, visible) {\n    instance.setVisible(visible);\n  },\n  zIndex(instance, zIndex) {\n    instance.setZIndex(zIndex);\n  }\n};\nvar defaultOptions$3 = {};\nfunction InfoBoxFunctional(_ref) {\n  var {\n    children,\n    anchor,\n    options,\n    position,\n    zIndex,\n    onCloseClick,\n    onDomReady,\n    onContentChanged,\n    onPositionChanged,\n    onZindexChanged,\n    onLoad,\n    onUnmount\n  } = _ref;\n  var map = useContext(MapContext);\n  var [instance, setInstance] = useState(null);\n  var [closeClickListener, setCloseClickListener] = useState(null);\n  var [domReadyClickListener, setDomReadyClickListener] = useState(null);\n  var [contentChangedClickListener, setContentChangedClickListener] = useState(null);\n  var [positionChangedClickListener, setPositionChangedClickListener] = useState(null);\n  var [zIndexChangedClickListener, setZindexChangedClickListener] = useState(null);\n  var containerElementRef = useRef(null);\n  // Order does matter\n  useEffect(() => {\n    if (map && instance !== null) {\n      instance.close();\n      if (anchor) {\n        instance.open(map, anchor);\n      } else if (instance.getPosition()) {\n        instance.open(map);\n      }\n    }\n  }, [map, instance, anchor]);\n  useEffect(() => {\n    if (options && instance !== null) {\n      instance.setOptions(options);\n    }\n  }, [instance, options]);\n  useEffect(() => {\n    if (position && instance !== null) {\n      var positionLatLng = position instanceof google.maps.LatLng ? position :\n      // eslint-disable-next-line @typescript-eslint/ban-ts-comment\n      // @ts-ignore\n      new google.maps.LatLng(position.lat, position.lng);\n      instance.setPosition(positionLatLng);\n    }\n  }, [position]);\n  useEffect(() => {\n    if (typeof zIndex === 'number' && instance !== null) {\n      instance.setZIndex(zIndex);\n    }\n  }, [zIndex]);\n  useEffect(() => {\n    if (instance && onCloseClick) {\n      if (closeClickListener !== null) {\n        google.maps.event.removeListener(closeClickListener);\n      }\n      setCloseClickListener(google.maps.event.addListener(instance, 'closeclick', onCloseClick));\n    }\n  }, [onCloseClick]);\n  useEffect(() => {\n    if (instance && onDomReady) {\n      if (domReadyClickListener !== null) {\n        google.maps.event.removeListener(domReadyClickListener);\n      }\n      setDomReadyClickListener(google.maps.event.addListener(instance, 'domready', onDomReady));\n    }\n  }, [onDomReady]);\n  useEffect(() => {\n    if (instance && onContentChanged) {\n      if (contentChangedClickListener !== null) {\n        google.maps.event.removeListener(contentChangedClickListener);\n      }\n      setContentChangedClickListener(google.maps.event.addListener(instance, 'content_changed', onContentChanged));\n    }\n  }, [onContentChanged]);\n  useEffect(() => {\n    if (instance && onPositionChanged) {\n      if (positionChangedClickListener !== null) {\n        google.maps.event.removeListener(positionChangedClickListener);\n      }\n      setPositionChangedClickListener(google.maps.event.addListener(instance, 'position_changed', onPositionChanged));\n    }\n  }, [onPositionChanged]);\n  useEffect(() => {\n    if (instance && onZindexChanged) {\n      if (zIndexChangedClickListener !== null) {\n        google.maps.event.removeListener(zIndexChangedClickListener);\n      }\n      setZindexChangedClickListener(google.maps.event.addListener(instance, 'zindex_changed', onZindexChanged));\n    }\n  }, [onZindexChanged]);\n  useEffect(() => {\n    if (map) {\n      var _ref2 = options || defaultOptions$3,\n        {\n          position: _position\n        } = _ref2,\n        infoBoxOptions = _objectWithoutProperties(_ref2, _excluded);\n      var positionLatLng;\n      if (_position && !(_position instanceof google.maps.LatLng)) {\n        // eslint-disable-next-line @typescript-eslint/ban-ts-comment\n        // @ts-ignore\n        positionLatLng = new google.maps.LatLng(_position.lat, _position.lng);\n      }\n      var infoBox = new InfoBox(_objectSpread$b(_objectSpread$b({}, infoBoxOptions), positionLatLng ? {\n        position: positionLatLng\n      } : {}));\n      containerElementRef.current = document.createElement('div');\n      setInstance(infoBox);\n      if (onCloseClick) {\n        setCloseClickListener(google.maps.event.addListener(infoBox, 'closeclick', onCloseClick));\n      }\n      if (onDomReady) {\n        setDomReadyClickListener(google.maps.event.addListener(infoBox, 'domready', onDomReady));\n      }\n      if (onContentChanged) {\n        setContentChangedClickListener(google.maps.event.addListener(infoBox, 'content_changed', onContentChanged));\n      }\n      if (onPositionChanged) {\n        setPositionChangedClickListener(google.maps.event.addListener(infoBox, 'position_changed', onPositionChanged));\n      }\n      if (onZindexChanged) {\n        setZindexChangedClickListener(google.maps.event.addListener(infoBox, 'zindex_changed', onZindexChanged));\n      }\n      infoBox.setContent(containerElementRef.current);\n      if (anchor) {\n        infoBox.open(map, anchor);\n      } else if (infoBox.getPosition()) {\n        infoBox.open(map);\n      } else {\n        invariant(false, 'You must provide either an anchor or a position prop for <InfoBox>.');\n      }\n      if (onLoad) {\n        onLoad(infoBox);\n      }\n    }\n    return () => {\n      if (instance !== null) {\n        if (closeClickListener) {\n          google.maps.event.removeListener(closeClickListener);\n        }\n        if (contentChangedClickListener) {\n          google.maps.event.removeListener(contentChangedClickListener);\n        }\n        if (domReadyClickListener) {\n          google.maps.event.removeListener(domReadyClickListener);\n        }\n        if (positionChangedClickListener) {\n          google.maps.event.removeListener(positionChangedClickListener);\n        }\n        if (zIndexChangedClickListener) {\n          google.maps.event.removeListener(zIndexChangedClickListener);\n        }\n        if (onUnmount) {\n          onUnmount(instance);\n        }\n        instance.close();\n      }\n    };\n  }, []);\n  return containerElementRef.current ? createPortal(Children.only(children), containerElementRef.current) : null;\n}\nvar InfoBoxF = memo(InfoBoxFunctional);\nclass InfoBoxComponent extends PureComponent {\n  constructor() {\n    super(...arguments);\n    _defineProperty(this, \"registeredEvents\", []);\n    _defineProperty(this, \"containerElement\", null);\n    _defineProperty(this, \"state\", {\n      infoBox: null\n    });\n    _defineProperty(this, \"open\", (infoBox, anchor) => {\n      if (anchor) {\n        if (this.context !== null) {\n          infoBox.open(this.context, anchor);\n        }\n      } else if (infoBox.getPosition()) {\n        if (this.context !== null) {\n          infoBox.open(this.context);\n        }\n      } else {\n        invariant(false, 'You must provide either an anchor or a position prop for <InfoBox>.');\n      }\n    });\n    _defineProperty(this, \"setInfoBoxCallback\", () => {\n      if (this.state.infoBox !== null && this.containerElement !== null) {\n        this.state.infoBox.setContent(this.containerElement);\n        this.open(this.state.infoBox, this.props.anchor);\n        if (this.props.onLoad) {\n          this.props.onLoad(this.state.infoBox);\n        }\n      }\n    });\n  }\n  componentDidMount() {\n    var _ref3 = this.props.options || {},\n      {\n        position\n      } = _ref3,\n      infoBoxOptions = _objectWithoutProperties(_ref3, _excluded2);\n    var positionLatLng;\n    if (position && !(position instanceof google.maps.LatLng)) {\n      // eslint-disable-next-line @typescript-eslint/ban-ts-comment\n      // @ts-ignore\n      positionLatLng = new google.maps.LatLng(position.lat, position.lng);\n    }\n    var infoBox = new InfoBox(_objectSpread$b(_objectSpread$b({}, infoBoxOptions), positionLatLng ? {\n      position: positionLatLng\n    } : {}));\n    this.containerElement = document.createElement('div');\n    this.registeredEvents = applyUpdatersToPropsAndRegisterEvents({\n      updaterMap: updaterMap$d,\n      eventMap: eventMap$d,\n      prevProps: {},\n      nextProps: this.props,\n      instance: infoBox\n    });\n    this.setState({\n      infoBox\n    }, this.setInfoBoxCallback);\n  }\n  componentDidUpdate(prevProps) {\n    var {\n      infoBox\n    } = this.state;\n    if (infoBox !== null) {\n      unregisterEvents(this.registeredEvents);\n      this.registeredEvents = applyUpdatersToPropsAndRegisterEvents({\n        updaterMap: updaterMap$d,\n        eventMap: eventMap$d,\n        prevProps,\n        nextProps: this.props,\n        instance: infoBox\n      });\n    }\n  }\n  componentWillUnmount() {\n    var {\n      onUnmount\n    } = this.props;\n    var {\n      infoBox\n    } = this.state;\n    if (infoBox !== null) {\n      if (onUnmount) {\n        onUnmount(infoBox);\n      }\n      unregisterEvents(this.registeredEvents);\n      infoBox.close();\n    }\n  }\n  render() {\n    return this.containerElement ? createPortal(Children.only(this.props.children), this.containerElement) : null;\n  }\n}\n_defineProperty(InfoBoxComponent, \"contextType\", MapContext);\n\nvar fastDeepEqual;\nvar hasRequiredFastDeepEqual;\nfunction requireFastDeepEqual() {\n  if (hasRequiredFastDeepEqual) return fastDeepEqual;\n  hasRequiredFastDeepEqual = 1;\n\n  // do not edit .js files directly - edit src/index.jst\n\n  fastDeepEqual = function equal(a, b) {\n    if (a === b) return true;\n    if (a && b && typeof a == 'object' && typeof b == 'object') {\n      if (a.constructor !== b.constructor) return false;\n      var length, i, keys;\n      if (Array.isArray(a)) {\n        length = a.length;\n        if (length != b.length) return false;\n        for (i = length; i-- !== 0;) if (!equal(a[i], b[i])) return false;\n        return true;\n      }\n      if (a.constructor === RegExp) return a.source === b.source && a.flags === b.flags;\n      if (a.valueOf !== Object.prototype.valueOf) return a.valueOf() === b.valueOf();\n      if (a.toString !== Object.prototype.toString) return a.toString() === b.toString();\n      keys = Object.keys(a);\n      length = keys.length;\n      if (length !== Object.keys(b).length) return false;\n      for (i = length; i-- !== 0;) if (!Object.prototype.hasOwnProperty.call(b, keys[i])) return false;\n      for (i = length; i-- !== 0;) {\n        var key = keys[i];\n        if (!equal(a[key], b[key])) return false;\n      }\n      return true;\n    }\n\n    // true if both NaN, false otherwise\n    return a !== a && b !== b;\n  };\n  return fastDeepEqual;\n}\n\nvar fastDeepEqualExports = requireFastDeepEqual();\nvar equal = /*@__PURE__*/getDefaultExportFromCjs$1(fastDeepEqualExports);\n\nvar ARRAY_TYPES = [Int8Array, Uint8Array, Uint8ClampedArray, Int16Array, Uint16Array, Int32Array, Uint32Array, Float32Array, Float64Array];\n\n/** @typedef {Int8ArrayConstructor | Uint8ArrayConstructor | Uint8ClampedArrayConstructor | Int16ArrayConstructor | Uint16ArrayConstructor | Int32ArrayConstructor | Uint32ArrayConstructor | Float32ArrayConstructor | Float64ArrayConstructor} TypedArrayConstructor */\n\nvar VERSION = 1; // serialized format version\nvar HEADER_SIZE = 8;\nclass KDBush {\n  /**\n   * Creates an index from raw `ArrayBuffer` data.\n   * @param {ArrayBuffer} data\n   */\n  static from(data) {\n    if (!(data instanceof ArrayBuffer)) {\n      throw new Error('Data must be an instance of ArrayBuffer.');\n    }\n    var [magic, versionAndType] = new Uint8Array(data, 0, 2);\n    if (magic !== 0xdb) {\n      throw new Error('Data does not appear to be in a KDBush format.');\n    }\n    var version = versionAndType >> 4;\n    if (version !== VERSION) {\n      throw new Error(\"Got v\".concat(version, \" data when expected v\").concat(VERSION, \".\"));\n    }\n    var ArrayType = ARRAY_TYPES[versionAndType & 0x0f];\n    if (!ArrayType) {\n      throw new Error('Unrecognized array type.');\n    }\n    var [nodeSize] = new Uint16Array(data, 2, 1);\n    var [numItems] = new Uint32Array(data, 4, 1);\n    return new KDBush(numItems, nodeSize, ArrayType, data);\n  }\n\n  /**\n   * Creates an index that will hold a given number of items.\n   * @param {number} numItems\n   * @param {number} [nodeSize=64] Size of the KD-tree node (64 by default).\n   * @param {TypedArrayConstructor} [ArrayType=Float64Array] The array type used for coordinates storage (`Float64Array` by default).\n   * @param {ArrayBuffer} [data] (For internal use only)\n   */\n  constructor(numItems) {\n    var nodeSize = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : 64;\n    var ArrayType = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : Float64Array;\n    var data = arguments.length > 3 ? arguments[3] : undefined;\n    if (isNaN(numItems) || numItems < 0) throw new Error(\"Unpexpected numItems value: \".concat(numItems, \".\"));\n    this.numItems = +numItems;\n    this.nodeSize = Math.min(Math.max(+nodeSize, 2), 65535);\n    this.ArrayType = ArrayType;\n    this.IndexArrayType = numItems < 65536 ? Uint16Array : Uint32Array;\n    var arrayTypeIndex = ARRAY_TYPES.indexOf(this.ArrayType);\n    var coordsByteSize = numItems * 2 * this.ArrayType.BYTES_PER_ELEMENT;\n    var idsByteSize = numItems * this.IndexArrayType.BYTES_PER_ELEMENT;\n    var padCoords = (8 - idsByteSize % 8) % 8;\n    if (arrayTypeIndex < 0) {\n      throw new Error(\"Unexpected typed array class: \".concat(ArrayType, \".\"));\n    }\n    if (data && data instanceof ArrayBuffer) {\n      // reconstruct an index from a buffer\n      this.data = data;\n      this.ids = new this.IndexArrayType(this.data, HEADER_SIZE, numItems);\n      this.coords = new this.ArrayType(this.data, HEADER_SIZE + idsByteSize + padCoords, numItems * 2);\n      this._pos = numItems * 2;\n      this._finished = true;\n    } else {\n      // initialize a new index\n      this.data = new ArrayBuffer(HEADER_SIZE + coordsByteSize + idsByteSize + padCoords);\n      this.ids = new this.IndexArrayType(this.data, HEADER_SIZE, numItems);\n      this.coords = new this.ArrayType(this.data, HEADER_SIZE + idsByteSize + padCoords, numItems * 2);\n      this._pos = 0;\n      this._finished = false;\n\n      // set header\n      new Uint8Array(this.data, 0, 2).set([0xdb, (VERSION << 4) + arrayTypeIndex]);\n      new Uint16Array(this.data, 2, 1)[0] = nodeSize;\n      new Uint32Array(this.data, 4, 1)[0] = numItems;\n    }\n  }\n\n  /**\n   * Add a point to the index.\n   * @param {number} x\n   * @param {number} y\n   * @returns {number} An incremental index associated with the added item (starting from `0`).\n   */\n  add(x, y) {\n    var index = this._pos >> 1;\n    this.ids[index] = index;\n    this.coords[this._pos++] = x;\n    this.coords[this._pos++] = y;\n    return index;\n  }\n\n  /**\n   * Perform indexing of the added points.\n   */\n  finish() {\n    var numAdded = this._pos >> 1;\n    if (numAdded !== this.numItems) {\n      throw new Error(\"Added \".concat(numAdded, \" items when expected \").concat(this.numItems, \".\"));\n    }\n    // kd-sort both arrays for efficient search\n    sort(this.ids, this.coords, this.nodeSize, 0, this.numItems - 1, 0);\n    this._finished = true;\n    return this;\n  }\n\n  /**\n   * Search the index for items within a given bounding box.\n   * @param {number} minX\n   * @param {number} minY\n   * @param {number} maxX\n   * @param {number} maxY\n   * @returns {number[]} An array of indices correponding to the found items.\n   */\n  range(minX, minY, maxX, maxY) {\n    if (!this._finished) throw new Error('Data not yet indexed - call index.finish().');\n    var {\n      ids,\n      coords,\n      nodeSize\n    } = this;\n    var stack = [0, ids.length - 1, 0];\n    var result = [];\n\n    // recursively search for items in range in the kd-sorted arrays\n    while (stack.length) {\n      var axis = stack.pop() || 0;\n      var right = stack.pop() || 0;\n      var left = stack.pop() || 0;\n\n      // if we reached \"tree node\", search linearly\n      if (right - left <= nodeSize) {\n        for (var i = left; i <= right; i++) {\n          var _x = coords[2 * i];\n          var _y = coords[2 * i + 1];\n          if (_x >= minX && _x <= maxX && _y >= minY && _y <= maxY) result.push(ids[i]);\n        }\n        continue;\n      }\n\n      // otherwise find the middle index\n      var m = left + right >> 1;\n\n      // include the middle item if it's in range\n      var x = coords[2 * m];\n      var y = coords[2 * m + 1];\n      if (x >= minX && x <= maxX && y >= minY && y <= maxY) result.push(ids[m]);\n\n      // queue search in halves that intersect the query\n      if (axis === 0 ? minX <= x : minY <= y) {\n        stack.push(left);\n        stack.push(m - 1);\n        stack.push(1 - axis);\n      }\n      if (axis === 0 ? maxX >= x : maxY >= y) {\n        stack.push(m + 1);\n        stack.push(right);\n        stack.push(1 - axis);\n      }\n    }\n    return result;\n  }\n\n  /**\n   * Search the index for items within a given radius.\n   * @param {number} qx\n   * @param {number} qy\n   * @param {number} r Query radius.\n   * @returns {number[]} An array of indices correponding to the found items.\n   */\n  within(qx, qy, r) {\n    if (!this._finished) throw new Error('Data not yet indexed - call index.finish().');\n    var {\n      ids,\n      coords,\n      nodeSize\n    } = this;\n    var stack = [0, ids.length - 1, 0];\n    var result = [];\n    var r2 = r * r;\n\n    // recursively search for items within radius in the kd-sorted arrays\n    while (stack.length) {\n      var axis = stack.pop() || 0;\n      var right = stack.pop() || 0;\n      var left = stack.pop() || 0;\n\n      // if we reached \"tree node\", search linearly\n      if (right - left <= nodeSize) {\n        for (var i = left; i <= right; i++) {\n          if (sqDist(coords[2 * i], coords[2 * i + 1], qx, qy) <= r2) result.push(ids[i]);\n        }\n        continue;\n      }\n\n      // otherwise find the middle index\n      var m = left + right >> 1;\n\n      // include the middle item if it's in range\n      var x = coords[2 * m];\n      var y = coords[2 * m + 1];\n      if (sqDist(x, y, qx, qy) <= r2) result.push(ids[m]);\n\n      // queue search in halves that intersect the query\n      if (axis === 0 ? qx - r <= x : qy - r <= y) {\n        stack.push(left);\n        stack.push(m - 1);\n        stack.push(1 - axis);\n      }\n      if (axis === 0 ? qx + r >= x : qy + r >= y) {\n        stack.push(m + 1);\n        stack.push(right);\n        stack.push(1 - axis);\n      }\n    }\n    return result;\n  }\n}\n\n/**\n * @param {Uint16Array | Uint32Array} ids\n * @param {InstanceType<TypedArrayConstructor>} coords\n * @param {number} nodeSize\n * @param {number} left\n * @param {number} right\n * @param {number} axis\n */\nfunction sort(ids, coords, nodeSize, left, right, axis) {\n  if (right - left <= nodeSize) return;\n  var m = left + right >> 1; // middle index\n\n  // sort ids and coords around the middle index so that the halves lie\n  // either left/right or top/bottom correspondingly (taking turns)\n  select(ids, coords, m, left, right, axis);\n\n  // recursively kd-sort first half and second half on the opposite axis\n  sort(ids, coords, nodeSize, left, m - 1, 1 - axis);\n  sort(ids, coords, nodeSize, m + 1, right, 1 - axis);\n}\n\n/**\n * Custom Floyd-Rivest selection algorithm: sort ids and coords so that\n * [left..k-1] items are smaller than k-th item (on either x or y axis)\n * @param {Uint16Array | Uint32Array} ids\n * @param {InstanceType<TypedArrayConstructor>} coords\n * @param {number} k\n * @param {number} left\n * @param {number} right\n * @param {number} axis\n */\nfunction select(ids, coords, k, left, right, axis) {\n  while (right > left) {\n    if (right - left > 600) {\n      var n = right - left + 1;\n      var m = k - left + 1;\n      var z = Math.log(n);\n      var s = 0.5 * Math.exp(2 * z / 3);\n      var sd = 0.5 * Math.sqrt(z * s * (n - s) / n) * (m - n / 2 < 0 ? -1 : 1);\n      var newLeft = Math.max(left, Math.floor(k - m * s / n + sd));\n      var newRight = Math.min(right, Math.floor(k + (n - m) * s / n + sd));\n      select(ids, coords, k, newLeft, newRight, axis);\n    }\n    var t = coords[2 * k + axis];\n    var i = left;\n    var j = right;\n    swapItem(ids, coords, left, k);\n    if (coords[2 * right + axis] > t) swapItem(ids, coords, left, right);\n    while (i < j) {\n      swapItem(ids, coords, i, j);\n      i++;\n      j--;\n      while (coords[2 * i + axis] < t) i++;\n      while (coords[2 * j + axis] > t) j--;\n    }\n    if (coords[2 * left + axis] === t) swapItem(ids, coords, left, j);else {\n      j++;\n      swapItem(ids, coords, j, right);\n    }\n    if (j <= k) left = j + 1;\n    if (k <= j) right = j - 1;\n  }\n}\n\n/**\n * @param {Uint16Array | Uint32Array} ids\n * @param {InstanceType<TypedArrayConstructor>} coords\n * @param {number} i\n * @param {number} j\n */\nfunction swapItem(ids, coords, i, j) {\n  swap(ids, i, j);\n  swap(coords, 2 * i, 2 * j);\n  swap(coords, 2 * i + 1, 2 * j + 1);\n}\n\n/**\n * @param {InstanceType<TypedArrayConstructor>} arr\n * @param {number} i\n * @param {number} j\n */\nfunction swap(arr, i, j) {\n  var tmp = arr[i];\n  arr[i] = arr[j];\n  arr[j] = tmp;\n}\n\n/**\n * @param {number} ax\n * @param {number} ay\n * @param {number} bx\n * @param {number} by\n */\nfunction sqDist(ax, ay, bx, by) {\n  var dx = ax - bx;\n  var dy = ay - by;\n  return dx * dx + dy * dy;\n}\n\nvar defaultOptions$2 = {\n  minZoom: 0,\n  // min zoom to generate clusters on\n  maxZoom: 16,\n  // max zoom level to cluster the points on\n  minPoints: 2,\n  // minimum points to form a cluster\n  radius: 40,\n  // cluster radius in pixels\n  extent: 512,\n  // tile extent (radius is calculated relative to it)\n  nodeSize: 64,\n  // size of the KD-tree leaf node, affects performance\n  log: false,\n  // whether to log timing info\n\n  // whether to generate numeric ids for input features (in vector tiles)\n  generateId: false,\n  // a reduce function for calculating custom cluster properties\n  reduce: null,\n  // (accumulated, props) => { accumulated.sum += props.sum; }\n\n  // properties to use for individual points when running the reducer\n  map: props => props // props => ({sum: props.my_value})\n};\nvar fround = Math.fround || (tmp => x => {\n  tmp[0] = +x;\n  return tmp[0];\n})(new Float32Array(1));\nvar OFFSET_ZOOM = 2;\nvar OFFSET_ID = 3;\nvar OFFSET_PARENT = 4;\nvar OFFSET_NUM = 5;\nvar OFFSET_PROP = 6;\nclass Supercluster {\n  constructor(options) {\n    this.options = Object.assign(Object.create(defaultOptions$2), options);\n    this.trees = new Array(this.options.maxZoom + 1);\n    this.stride = this.options.reduce ? 7 : 6;\n    this.clusterProps = [];\n  }\n  load(points) {\n    var {\n      log,\n      minZoom,\n      maxZoom\n    } = this.options;\n    if (log) console.time('total time');\n    var timerId = \"prepare \".concat(points.length, \" points\");\n    if (log) console.time(timerId);\n    this.points = points;\n\n    // generate a cluster object for each point and index input points into a KD-tree\n    var data = [];\n    for (var i = 0; i < points.length; i++) {\n      var p = points[i];\n      if (!p.geometry) continue;\n      var [lng, lat] = p.geometry.coordinates;\n      var x = fround(lngX(lng));\n      var y = fround(latY(lat));\n      // store internal point/cluster data in flat numeric arrays for performance\n      data.push(x, y,\n      // projected point coordinates\n      Infinity,\n      // the last zoom the point was processed at\n      i,\n      // index of the source feature in the original input array\n      -1,\n      // parent cluster id\n      1 // number of points in a cluster\n      );\n      if (this.options.reduce) data.push(0); // noop\n    }\n    var tree = this.trees[maxZoom + 1] = this._createTree(data);\n    if (log) console.timeEnd(timerId);\n\n    // cluster points on max zoom, then cluster the results on previous zoom, etc.;\n    // results in a cluster hierarchy across zoom levels\n    for (var z = maxZoom; z >= minZoom; z--) {\n      var now = +Date.now();\n\n      // create a new set of clusters for the zoom and index them with a KD-tree\n      tree = this.trees[z] = this._createTree(this._cluster(tree, z));\n      if (log) console.log('z%d: %d clusters in %dms', z, tree.numItems, +Date.now() - now);\n    }\n    if (log) console.timeEnd('total time');\n    return this;\n  }\n  getClusters(bbox, zoom) {\n    var minLng = ((bbox[0] + 180) % 360 + 360) % 360 - 180;\n    var minLat = Math.max(-90, Math.min(90, bbox[1]));\n    var maxLng = bbox[2] === 180 ? 180 : ((bbox[2] + 180) % 360 + 360) % 360 - 180;\n    var maxLat = Math.max(-90, Math.min(90, bbox[3]));\n    if (bbox[2] - bbox[0] >= 360) {\n      minLng = -180;\n      maxLng = 180;\n    } else if (minLng > maxLng) {\n      var easternHem = this.getClusters([minLng, minLat, 180, maxLat], zoom);\n      var westernHem = this.getClusters([-180, minLat, maxLng, maxLat], zoom);\n      return easternHem.concat(westernHem);\n    }\n    var tree = this.trees[this._limitZoom(zoom)];\n    var ids = tree.range(lngX(minLng), latY(maxLat), lngX(maxLng), latY(minLat));\n    var data = tree.data;\n    var clusters = [];\n    for (var id of ids) {\n      var k = this.stride * id;\n      clusters.push(data[k + OFFSET_NUM] > 1 ? getClusterJSON(data, k, this.clusterProps) : this.points[data[k + OFFSET_ID]]);\n    }\n    return clusters;\n  }\n  getChildren(clusterId) {\n    var originId = this._getOriginId(clusterId);\n    var originZoom = this._getOriginZoom(clusterId);\n    var errorMsg = 'No cluster with the specified id.';\n    var tree = this.trees[originZoom];\n    if (!tree) throw new Error(errorMsg);\n    var data = tree.data;\n    if (originId * this.stride >= data.length) throw new Error(errorMsg);\n    var r = this.options.radius / (this.options.extent * Math.pow(2, originZoom - 1));\n    var x = data[originId * this.stride];\n    var y = data[originId * this.stride + 1];\n    var ids = tree.within(x, y, r);\n    var children = [];\n    for (var id of ids) {\n      var k = id * this.stride;\n      if (data[k + OFFSET_PARENT] === clusterId) {\n        children.push(data[k + OFFSET_NUM] > 1 ? getClusterJSON(data, k, this.clusterProps) : this.points[data[k + OFFSET_ID]]);\n      }\n    }\n    if (children.length === 0) throw new Error(errorMsg);\n    return children;\n  }\n  getLeaves(clusterId, limit, offset) {\n    limit = limit || 10;\n    offset = offset || 0;\n    var leaves = [];\n    this._appendLeaves(leaves, clusterId, limit, offset, 0);\n    return leaves;\n  }\n  getTile(z, x, y) {\n    var tree = this.trees[this._limitZoom(z)];\n    var z2 = Math.pow(2, z);\n    var {\n      extent,\n      radius\n    } = this.options;\n    var p = radius / extent;\n    var top = (y - p) / z2;\n    var bottom = (y + 1 + p) / z2;\n    var tile = {\n      features: []\n    };\n    this._addTileFeatures(tree.range((x - p) / z2, top, (x + 1 + p) / z2, bottom), tree.data, x, y, z2, tile);\n    if (x === 0) {\n      this._addTileFeatures(tree.range(1 - p / z2, top, 1, bottom), tree.data, z2, y, z2, tile);\n    }\n    if (x === z2 - 1) {\n      this._addTileFeatures(tree.range(0, top, p / z2, bottom), tree.data, -1, y, z2, tile);\n    }\n    return tile.features.length ? tile : null;\n  }\n  getClusterExpansionZoom(clusterId) {\n    var expansionZoom = this._getOriginZoom(clusterId) - 1;\n    while (expansionZoom <= this.options.maxZoom) {\n      var children = this.getChildren(clusterId);\n      expansionZoom++;\n      if (children.length !== 1) break;\n      clusterId = children[0].properties.cluster_id;\n    }\n    return expansionZoom;\n  }\n  _appendLeaves(result, clusterId, limit, offset, skipped) {\n    var children = this.getChildren(clusterId);\n    for (var child of children) {\n      var props = child.properties;\n      if (props && props.cluster) {\n        if (skipped + props.point_count <= offset) {\n          // skip the whole cluster\n          skipped += props.point_count;\n        } else {\n          // enter the cluster\n          skipped = this._appendLeaves(result, props.cluster_id, limit, offset, skipped);\n          // exit the cluster\n        }\n      } else if (skipped < offset) {\n        // skip a single point\n        skipped++;\n      } else {\n        // add a single point\n        result.push(child);\n      }\n      if (result.length === limit) break;\n    }\n    return skipped;\n  }\n  _createTree(data) {\n    var tree = new KDBush(data.length / this.stride | 0, this.options.nodeSize, Float32Array);\n    for (var i = 0; i < data.length; i += this.stride) tree.add(data[i], data[i + 1]);\n    tree.finish();\n    tree.data = data;\n    return tree;\n  }\n  _addTileFeatures(ids, data, x, y, z2, tile) {\n    for (var i of ids) {\n      var k = i * this.stride;\n      var isCluster = data[k + OFFSET_NUM] > 1;\n      var tags = void 0,\n        px = void 0,\n        py = void 0;\n      if (isCluster) {\n        tags = getClusterProperties(data, k, this.clusterProps);\n        px = data[k];\n        py = data[k + 1];\n      } else {\n        var p = this.points[data[k + OFFSET_ID]];\n        tags = p.properties;\n        var [lng, lat] = p.geometry.coordinates;\n        px = lngX(lng);\n        py = latY(lat);\n      }\n      var f = {\n        type: 1,\n        geometry: [[Math.round(this.options.extent * (px * z2 - x)), Math.round(this.options.extent * (py * z2 - y))]],\n        tags\n      };\n\n      // assign id\n      var id = void 0;\n      if (isCluster || this.options.generateId) {\n        // optionally generate id for points\n        id = data[k + OFFSET_ID];\n      } else {\n        // keep id if already assigned\n        id = this.points[data[k + OFFSET_ID]].id;\n      }\n      if (id !== undefined) f.id = id;\n      tile.features.push(f);\n    }\n  }\n  _limitZoom(z) {\n    return Math.max(this.options.minZoom, Math.min(Math.floor(+z), this.options.maxZoom + 1));\n  }\n  _cluster(tree, zoom) {\n    var {\n      radius,\n      extent,\n      reduce,\n      minPoints\n    } = this.options;\n    var r = radius / (extent * Math.pow(2, zoom));\n    var data = tree.data;\n    var nextData = [];\n    var stride = this.stride;\n\n    // loop through each point\n    for (var i = 0; i < data.length; i += stride) {\n      // if we've already visited the point at this zoom level, skip it\n      if (data[i + OFFSET_ZOOM] <= zoom) continue;\n      data[i + OFFSET_ZOOM] = zoom;\n\n      // find all nearby points\n      var x = data[i];\n      var y = data[i + 1];\n      var neighborIds = tree.within(data[i], data[i + 1], r);\n      var numPointsOrigin = data[i + OFFSET_NUM];\n      var numPoints = numPointsOrigin;\n\n      // count the number of points in a potential cluster\n      for (var neighborId of neighborIds) {\n        var k = neighborId * stride;\n        // filter out neighbors that are already processed\n        if (data[k + OFFSET_ZOOM] > zoom) numPoints += data[k + OFFSET_NUM];\n      }\n\n      // if there were neighbors to merge, and there are enough points to form a cluster\n      if (numPoints > numPointsOrigin && numPoints >= minPoints) {\n        var wx = x * numPointsOrigin;\n        var wy = y * numPointsOrigin;\n        var clusterProperties = void 0;\n        var clusterPropIndex = -1;\n\n        // encode both zoom and point index on which the cluster originated -- offset by total length of features\n        var id = ((i / stride | 0) << 5) + (zoom + 1) + this.points.length;\n        for (var _neighborId of neighborIds) {\n          var _k = _neighborId * stride;\n          if (data[_k + OFFSET_ZOOM] <= zoom) continue;\n          data[_k + OFFSET_ZOOM] = zoom; // save the zoom (so it doesn't get processed twice)\n\n          var numPoints2 = data[_k + OFFSET_NUM];\n          wx += data[_k] * numPoints2; // accumulate coordinates for calculating weighted center\n          wy += data[_k + 1] * numPoints2;\n          data[_k + OFFSET_PARENT] = id;\n          if (reduce) {\n            if (!clusterProperties) {\n              clusterProperties = this._map(data, i, true);\n              clusterPropIndex = this.clusterProps.length;\n              this.clusterProps.push(clusterProperties);\n            }\n            reduce(clusterProperties, this._map(data, _k));\n          }\n        }\n        data[i + OFFSET_PARENT] = id;\n        nextData.push(wx / numPoints, wy / numPoints, Infinity, id, -1, numPoints);\n        if (reduce) nextData.push(clusterPropIndex);\n      } else {\n        // left points as unclustered\n        for (var j = 0; j < stride; j++) nextData.push(data[i + j]);\n        if (numPoints > 1) {\n          for (var _neighborId2 of neighborIds) {\n            var _k2 = _neighborId2 * stride;\n            if (data[_k2 + OFFSET_ZOOM] <= zoom) continue;\n            data[_k2 + OFFSET_ZOOM] = zoom;\n            for (var _j = 0; _j < stride; _j++) nextData.push(data[_k2 + _j]);\n          }\n        }\n      }\n    }\n    return nextData;\n  }\n\n  // get index of the point from which the cluster originated\n  _getOriginId(clusterId) {\n    return clusterId - this.points.length >> 5;\n  }\n\n  // get zoom of the point from which the cluster originated\n  _getOriginZoom(clusterId) {\n    return (clusterId - this.points.length) % 32;\n  }\n  _map(data, i, clone) {\n    if (data[i + OFFSET_NUM] > 1) {\n      var props = this.clusterProps[data[i + OFFSET_PROP]];\n      return clone ? Object.assign({}, props) : props;\n    }\n    var original = this.points[data[i + OFFSET_ID]].properties;\n    var result = this.options.map(original);\n    return clone && result === original ? Object.assign({}, result) : result;\n  }\n}\nfunction getClusterJSON(data, i, clusterProps) {\n  return {\n    type: 'Feature',\n    id: data[i + OFFSET_ID],\n    properties: getClusterProperties(data, i, clusterProps),\n    geometry: {\n      type: 'Point',\n      coordinates: [xLng(data[i]), yLat(data[i + 1])]\n    }\n  };\n}\nfunction getClusterProperties(data, i, clusterProps) {\n  var count = data[i + OFFSET_NUM];\n  var abbrev = count >= 10000 ? \"\".concat(Math.round(count / 1000), \"k\") : count >= 1000 ? \"\".concat(Math.round(count / 100) / 10, \"k\") : count;\n  var propIndex = data[i + OFFSET_PROP];\n  var properties = propIndex === -1 ? {} : Object.assign({}, clusterProps[propIndex]);\n  return Object.assign(properties, {\n    cluster: true,\n    cluster_id: data[i + OFFSET_ID],\n    point_count: count,\n    point_count_abbreviated: abbrev\n  });\n}\n\n// longitude/latitude to spherical mercator in [0..1] range\nfunction lngX(lng) {\n  return lng / 360 + 0.5;\n}\nfunction latY(lat) {\n  var sin = Math.sin(lat * Math.PI / 180);\n  var y = 0.5 - 0.25 * Math.log((1 + sin) / (1 - sin)) / Math.PI;\n  return y < 0 ? 0 : y > 1 ? 1 : y;\n}\n\n// spherical mercator to longitude/latitude\nfunction xLng(x) {\n  return (x - 0.5) * 360;\n}\nfunction yLat(y) {\n  var y2 = (180 - y * 360) * Math.PI / 180;\n  return 360 * Math.atan(Math.exp(y2)) / Math.PI - 90;\n}\n\n/*! *****************************************************************************\r\nCopyright (c) Microsoft Corporation.\r\n\r\nPermission to use, copy, modify, and/or distribute this software for any\r\npurpose with or without fee is hereby granted.\r\n\r\nTHE SOFTWARE IS PROVIDED \"AS IS\" AND THE AUTHOR DISCLAIMS ALL WARRANTIES WITH\r\nREGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF MERCHANTABILITY\r\nAND FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY SPECIAL, DIRECT,\r\nINDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES WHATSOEVER RESULTING FROM\r\nLOSS OF USE, DATA OR PROFITS, WHETHER IN AN ACTION OF CONTRACT, NEGLIGENCE OR\r\nOTHER TORTIOUS ACTION, ARISING OUT OF OR IN CONNECTION WITH THE USE OR\r\nPERFORMANCE OF THIS SOFTWARE.\r\n***************************************************************************** */\n\nfunction __rest(s, e) {\n  var t = {};\n  for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0) t[p] = s[p];\n  if (s != null && typeof Object.getOwnPropertySymbols === \"function\") for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {\n    if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i])) t[p[i]] = s[p[i]];\n  }\n  return t;\n}\n\n/**\n * Copyright 2023 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n/**\n * util class that creates a common set of convenience functions to wrap\n * shared behavior of Advanced Markers and Markers.\n */\nclass MarkerUtils {\n  static isAdvancedMarkerAvailable(map) {\n    return google.maps.marker && map.getMapCapabilities().isAdvancedMarkersAvailable === true;\n  }\n  static isAdvancedMarker(marker) {\n    return google.maps.marker && marker instanceof google.maps.marker.AdvancedMarkerElement;\n  }\n  static setMap(marker, map) {\n    if (this.isAdvancedMarker(marker)) {\n      marker.map = map;\n    } else {\n      marker.setMap(map);\n    }\n  }\n  static getPosition(marker) {\n    // SuperClusterAlgorithm.calculate expects a LatLng instance so we fake it for Adv Markers\n    if (this.isAdvancedMarker(marker)) {\n      if (marker.position) {\n        if (marker.position instanceof google.maps.LatLng) {\n          return marker.position;\n        }\n        // since we can't cast to LatLngLiteral for reasons =(\n        if (marker.position.lat && marker.position.lng) {\n          return new google.maps.LatLng(marker.position.lat, marker.position.lng);\n        }\n      }\n      return new google.maps.LatLng(null);\n    }\n    return marker.getPosition();\n  }\n  static getVisible(marker) {\n    if (this.isAdvancedMarker(marker)) {\n      /**\n       * Always return true for Advanced Markers because the clusterer\n       * uses getVisible as a way to count legacy markers not as an actual\n       * indicator of visibility for some reason. Even when markers are hidden\n       * Marker.getVisible returns `true` and this is used to set the marker count\n       * on the cluster. See the behavior of Cluster.count\n       */\n      return true;\n    }\n    return marker.getVisible();\n  }\n}\n\n/**\n * Copyright 2021 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nclass Cluster {\n  constructor(_ref) {\n    var {\n      markers,\n      position\n    } = _ref;\n    this.markers = markers;\n    if (position) {\n      if (position instanceof google.maps.LatLng) {\n        this._position = position;\n      } else {\n        this._position = new google.maps.LatLng(position);\n      }\n    }\n  }\n  get bounds() {\n    if (this.markers.length === 0 && !this._position) {\n      return;\n    }\n    var bounds = new google.maps.LatLngBounds(this._position, this._position);\n    for (var marker of this.markers) {\n      bounds.extend(MarkerUtils.getPosition(marker));\n    }\n    return bounds;\n  }\n  get position() {\n    return this._position || this.bounds.getCenter();\n  }\n  /**\n   * Get the count of **visible** markers.\n   */\n  get count() {\n    return this.markers.filter(m => MarkerUtils.getVisible(m)).length;\n  }\n  /**\n   * Add a marker to the cluster.\n   */\n  push(marker) {\n    this.markers.push(marker);\n  }\n  /**\n   * Cleanup references and remove marker from map.\n   */\n  delete() {\n    if (this.marker) {\n      MarkerUtils.setMap(this.marker, null);\n      this.marker = undefined;\n    }\n    this.markers.length = 0;\n  }\n}\n\n/**\n * Copyright 2021 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n/**\n * Returns the markers visible in a padded map viewport\n *\n * @param map\n * @param mapCanvasProjection\n * @param markers The list of marker to filter\n * @param viewportPaddingPixels The padding in pixel\n * @returns The list of markers in the padded viewport\n */\nvar filterMarkersToPaddedViewport = (map, mapCanvasProjection, markers, viewportPaddingPixels) => {\n  var extendedMapBounds = extendBoundsToPaddedViewport(map.getBounds(), mapCanvasProjection, viewportPaddingPixels);\n  return markers.filter(marker => extendedMapBounds.contains(MarkerUtils.getPosition(marker)));\n};\n/**\n * Extends a bounds by a number of pixels in each direction\n */\nvar extendBoundsToPaddedViewport = (bounds, projection, numPixels) => {\n  var {\n    northEast,\n    southWest\n  } = latLngBoundsToPixelBounds(bounds, projection);\n  var extendedPixelBounds = extendPixelBounds({\n    northEast,\n    southWest\n  }, numPixels);\n  return pixelBoundsToLatLngBounds(extendedPixelBounds, projection);\n};\n/**\n * Gets the extended bounds as a bbox [westLng, southLat, eastLng, northLat]\n */\nvar getPaddedViewport = (bounds, projection, pixels) => {\n  var extended = extendBoundsToPaddedViewport(bounds, projection, pixels);\n  var ne = extended.getNorthEast();\n  var sw = extended.getSouthWest();\n  return [sw.lng(), sw.lat(), ne.lng(), ne.lat()];\n};\n/**\n * Returns the distance between 2 positions.\n *\n * @hidden\n */\nvar distanceBetweenPoints = (p1, p2) => {\n  var R = 6371; // Radius of the Earth in km\n  var dLat = (p2.lat - p1.lat) * Math.PI / 180;\n  var dLon = (p2.lng - p1.lng) * Math.PI / 180;\n  var sinDLat = Math.sin(dLat / 2);\n  var sinDLon = Math.sin(dLon / 2);\n  var a = sinDLat * sinDLat + Math.cos(p1.lat * Math.PI / 180) * Math.cos(p2.lat * Math.PI / 180) * sinDLon * sinDLon;\n  var c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));\n  return R * c;\n};\n/**\n * Converts a LatLng bound to pixels.\n *\n * @hidden\n */\nvar latLngBoundsToPixelBounds = (bounds, projection) => {\n  return {\n    northEast: projection.fromLatLngToDivPixel(bounds.getNorthEast()),\n    southWest: projection.fromLatLngToDivPixel(bounds.getSouthWest())\n  };\n};\n/**\n * Extends a pixel bounds by numPixels in all directions.\n *\n * @hidden\n */\nvar extendPixelBounds = (_ref2, numPixels) => {\n  var {\n    northEast,\n    southWest\n  } = _ref2;\n  northEast.x += numPixels;\n  northEast.y -= numPixels;\n  southWest.x -= numPixels;\n  southWest.y += numPixels;\n  return {\n    northEast,\n    southWest\n  };\n};\n/**\n * @hidden\n */\nvar pixelBoundsToLatLngBounds = (_ref3, projection) => {\n  var {\n    northEast,\n    southWest\n  } = _ref3;\n  var sw = projection.fromDivPixelToLatLng(southWest);\n  var ne = projection.fromDivPixelToLatLng(northEast);\n  return new google.maps.LatLngBounds(sw, ne);\n};\n\n/**\n * Copyright 2021 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n/**\n * @hidden\n */\nclass AbstractAlgorithm {\n  constructor(_ref4) {\n    var {\n      maxZoom = 16\n    } = _ref4;\n    this.maxZoom = maxZoom;\n  }\n  /**\n   * Helper function to bypass clustering based upon some map state such as\n   * zoom, number of markers, etc.\n   *\n   * ```typescript\n   *  cluster({markers, map}: AlgorithmInput): Cluster[] {\n   *    if (shouldBypassClustering(map)) {\n   *      return this.noop({markers})\n   *    }\n   * }\n   * ```\n   */\n  noop(_ref5) {\n    var {\n      markers\n    } = _ref5;\n    return noop$1(markers);\n  }\n}\n/**\n * Abstract viewport algorithm proves a class to filter markers by a padded\n * viewport. This is a common optimization.\n *\n * @hidden\n */\nclass AbstractViewportAlgorithm extends AbstractAlgorithm {\n  constructor(_a) {\n    var {\n        viewportPadding = 60\n      } = _a,\n      options = __rest(_a, [\"viewportPadding\"]);\n    super(options);\n    this.viewportPadding = 60;\n    this.viewportPadding = viewportPadding;\n  }\n  calculate(_ref6) {\n    var {\n      markers,\n      map,\n      mapCanvasProjection\n    } = _ref6;\n    if (map.getZoom() >= this.maxZoom) {\n      return {\n        clusters: this.noop({\n          markers\n        }),\n        changed: false\n      };\n    }\n    return {\n      clusters: this.cluster({\n        markers: filterMarkersToPaddedViewport(map, mapCanvasProjection, markers, this.viewportPadding),\n        map,\n        mapCanvasProjection\n      })\n    };\n  }\n}\n/**\n * @hidden\n */\nvar noop$1 = markers => {\n  var clusters = markers.map(marker => new Cluster({\n    position: MarkerUtils.getPosition(marker),\n    markers: [marker]\n  }));\n  return clusters;\n};\n\n/**\n * Copyright 2021 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n/**\n * The default Grid algorithm historically used in Google Maps marker\n * clustering.\n *\n * The Grid algorithm does not implement caching and markers may flash as the\n * viewport changes. Instead use {@link SuperClusterAlgorithm}.\n */\nclass GridAlgorithm extends AbstractViewportAlgorithm {\n  constructor(_a) {\n    var {\n        maxDistance = 40000,\n        gridSize = 40\n      } = _a,\n      options = __rest(_a, [\"maxDistance\", \"gridSize\"]);\n    super(options);\n    this.clusters = [];\n    this.state = {\n      zoom: -1\n    };\n    this.maxDistance = maxDistance;\n    this.gridSize = gridSize;\n  }\n  calculate(_ref7) {\n    var {\n      markers,\n      map,\n      mapCanvasProjection\n    } = _ref7;\n    var state = {\n      zoom: map.getZoom()\n    };\n    var changed = false;\n    if (this.state.zoom >= this.maxZoom && state.zoom >= this.maxZoom) ;else {\n      changed = !equal(this.state, state);\n    }\n    this.state = state;\n    if (map.getZoom() >= this.maxZoom) {\n      return {\n        clusters: this.noop({\n          markers\n        }),\n        changed\n      };\n    }\n    return {\n      clusters: this.cluster({\n        markers: filterMarkersToPaddedViewport(map, mapCanvasProjection, markers, this.viewportPadding),\n        map,\n        mapCanvasProjection\n      })\n    };\n  }\n  cluster(_ref8) {\n    var {\n      markers,\n      map,\n      mapCanvasProjection\n    } = _ref8;\n    this.clusters = [];\n    markers.forEach(marker => {\n      this.addToClosestCluster(marker, map, mapCanvasProjection);\n    });\n    return this.clusters;\n  }\n  addToClosestCluster(marker, map, projection) {\n    var maxDistance = this.maxDistance; // Some large number\n    var cluster = null;\n    for (var i = 0; i < this.clusters.length; i++) {\n      var candidate = this.clusters[i];\n      var distance = distanceBetweenPoints(candidate.bounds.getCenter().toJSON(), MarkerUtils.getPosition(marker).toJSON());\n      if (distance < maxDistance) {\n        maxDistance = distance;\n        cluster = candidate;\n      }\n    }\n    if (cluster && extendBoundsToPaddedViewport(cluster.bounds, projection, this.gridSize).contains(MarkerUtils.getPosition(marker))) {\n      cluster.push(marker);\n    } else {\n      var _cluster = new Cluster({\n        markers: [marker]\n      });\n      this.clusters.push(_cluster);\n    }\n  }\n}\n\n/**\n * Copyright 2021 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n/**\n * Noop algorithm does not generate any clusters or filter markers by the an extended viewport.\n */\nclass NoopAlgorithm extends AbstractAlgorithm {\n  constructor(_a) {\n    var options = __rest(_a, []);\n    super(options);\n  }\n  calculate(_ref9) {\n    var {\n      markers,\n      map,\n      mapCanvasProjection\n    } = _ref9;\n    return {\n      clusters: this.cluster({\n        markers,\n        map,\n        mapCanvasProjection\n      }),\n      changed: false\n    };\n  }\n  cluster(input) {\n    return this.noop(input);\n  }\n}\n\n/**\n * Copyright 2021 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n/**\n * A very fast JavaScript algorithm for geospatial point clustering using KD trees.\n *\n * @see https://www.npmjs.com/package/supercluster for more information on options.\n */\nclass SuperClusterAlgorithm extends AbstractAlgorithm {\n  constructor(_a) {\n    var {\n        maxZoom,\n        radius = 60\n      } = _a,\n      options = __rest(_a, [\"maxZoom\", \"radius\"]);\n    super({\n      maxZoom\n    });\n    this.state = {\n      zoom: -1\n    };\n    this.superCluster = new Supercluster(Object.assign({\n      maxZoom: this.maxZoom,\n      radius\n    }, options));\n  }\n  calculate(input) {\n    var changed = false;\n    var state = {\n      zoom: input.map.getZoom()\n    };\n    if (!equal(input.markers, this.markers)) {\n      changed = true;\n      // TODO use proxy to avoid copy?\n      this.markers = [...input.markers];\n      var points = this.markers.map(marker => {\n        var position = MarkerUtils.getPosition(marker);\n        var coordinates = [position.lng(), position.lat()];\n        return {\n          type: \"Feature\",\n          geometry: {\n            type: \"Point\",\n            coordinates\n          },\n          properties: {\n            marker\n          }\n        };\n      });\n      this.superCluster.load(points);\n    }\n    if (!changed) {\n      if (this.state.zoom <= this.maxZoom || state.zoom <= this.maxZoom) {\n        changed = !equal(this.state, state);\n      }\n    }\n    this.state = state;\n    if (changed) {\n      this.clusters = this.cluster(input);\n    }\n    return {\n      clusters: this.clusters,\n      changed\n    };\n  }\n  cluster(_ref10) {\n    var {\n      map\n    } = _ref10;\n    return this.superCluster.getClusters([-180, -90, 180, 90], Math.round(map.getZoom())).map(feature => this.transformCluster(feature));\n  }\n  transformCluster(_ref11) {\n    var {\n      geometry: {\n        coordinates: [lng, lat]\n      },\n      properties\n    } = _ref11;\n    if (properties.cluster) {\n      return new Cluster({\n        markers: this.superCluster.getLeaves(properties.cluster_id, Infinity).map(leaf => leaf.properties.marker),\n        position: {\n          lat,\n          lng\n        }\n      });\n    }\n    var marker = properties.marker;\n    return new Cluster({\n      markers: [marker],\n      position: MarkerUtils.getPosition(marker)\n    });\n  }\n}\n\n/**\n * Copyright 2021 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n/**\n * A very fast JavaScript algorithm for geospatial point clustering using KD trees.\n *\n * @see https://www.npmjs.com/package/supercluster for more information on options.\n */\nclass SuperClusterViewportAlgorithm extends AbstractViewportAlgorithm {\n  constructor(_a) {\n    var {\n        maxZoom,\n        radius = 60,\n        viewportPadding = 60\n      } = _a,\n      options = __rest(_a, [\"maxZoom\", \"radius\", \"viewportPadding\"]);\n    super({\n      maxZoom,\n      viewportPadding\n    });\n    this.superCluster = new Supercluster(Object.assign({\n      maxZoom: this.maxZoom,\n      radius\n    }, options));\n    this.state = {\n      zoom: -1,\n      view: [0, 0, 0, 0]\n    };\n  }\n  calculate(input) {\n    var state = {\n      zoom: Math.round(input.map.getZoom()),\n      view: getPaddedViewport(input.map.getBounds(), input.mapCanvasProjection, this.viewportPadding)\n    };\n    var changed = !equal(this.state, state);\n    if (!equal(input.markers, this.markers)) {\n      changed = true;\n      // TODO use proxy to avoid copy?\n      this.markers = [...input.markers];\n      var points = this.markers.map(marker => {\n        var position = MarkerUtils.getPosition(marker);\n        var coordinates = [position.lng(), position.lat()];\n        return {\n          type: \"Feature\",\n          geometry: {\n            type: \"Point\",\n            coordinates\n          },\n          properties: {\n            marker\n          }\n        };\n      });\n      this.superCluster.load(points);\n    }\n    if (changed) {\n      this.clusters = this.cluster(input);\n      this.state = state;\n    }\n    return {\n      clusters: this.clusters,\n      changed\n    };\n  }\n  cluster(_ref12) {\n    var {\n      map,\n      mapCanvasProjection\n    } = _ref12;\n    /* recalculate new state because we can't use the cached version. */\n    var state = {\n      zoom: Math.round(map.getZoom()),\n      view: getPaddedViewport(map.getBounds(), mapCanvasProjection, this.viewportPadding)\n    };\n    return this.superCluster.getClusters(state.view, state.zoom).map(feature => this.transformCluster(feature));\n  }\n  transformCluster(_ref13) {\n    var {\n      geometry: {\n        coordinates: [lng, lat]\n      },\n      properties\n    } = _ref13;\n    if (properties.cluster) {\n      return new Cluster({\n        markers: this.superCluster.getLeaves(properties.cluster_id, Infinity).map(leaf => leaf.properties.marker),\n        position: {\n          lat,\n          lng\n        }\n      });\n    }\n    var marker = properties.marker;\n    return new Cluster({\n      markers: [marker],\n      position: MarkerUtils.getPosition(marker)\n    });\n  }\n}\n\n/**\n * Copyright 2021 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n/**\n * Provides statistics on all clusters in the current render cycle for use in {@link Renderer.render}.\n */\nclass ClusterStats {\n  constructor(markers, clusters) {\n    this.markers = {\n      sum: markers.length\n    };\n    var clusterMarkerCounts = clusters.map(a => a.count);\n    var clusterMarkerSum = clusterMarkerCounts.reduce((a, b) => a + b, 0);\n    this.clusters = {\n      count: clusters.length,\n      markers: {\n        mean: clusterMarkerSum / clusters.length,\n        sum: clusterMarkerSum,\n        min: Math.min(...clusterMarkerCounts),\n        max: Math.max(...clusterMarkerCounts)\n      }\n    };\n  }\n}\nclass DefaultRenderer {\n  /**\n   * The default render function for the library used by {@link MarkerClusterer}.\n   *\n   * Currently set to use the following:\n   *\n   * ```typescript\n   * // change color if this cluster has more markers than the mean cluster\n   * const color =\n   *   count > Math.max(10, stats.clusters.markers.mean)\n   *     ? \"#ff0000\"\n   *     : \"#0000ff\";\n   *\n   * // create svg url with fill color\n   * const svg = window.btoa(`\n   * <svg fill=\"${color}\" xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 240 240\">\n   *   <circle cx=\"120\" cy=\"120\" opacity=\".6\" r=\"70\" />\n   *   <circle cx=\"120\" cy=\"120\" opacity=\".3\" r=\"90\" />\n   *   <circle cx=\"120\" cy=\"120\" opacity=\".2\" r=\"110\" />\n   *   <circle cx=\"120\" cy=\"120\" opacity=\".1\" r=\"130\" />\n   * </svg>`);\n   *\n   * // create marker using svg icon\n   * return new google.maps.Marker({\n   *   position,\n   *   icon: {\n   *     url: `data:image/svg+xml;base64,${svg}`,\n   *     scaledSize: new google.maps.Size(45, 45),\n   *   },\n   *   label: {\n   *     text: String(count),\n   *     color: \"rgba(255,255,255,0.9)\",\n   *     fontSize: \"12px\",\n   *   },\n   *   // adjust zIndex to be above other markers\n   *   zIndex: 1000 + count,\n   * });\n   * ```\n   */\n  render(_ref14, stats, map) {\n    var {\n      count,\n      position\n    } = _ref14;\n    // change color if this cluster has more markers than the mean cluster\n    var color = count > Math.max(10, stats.clusters.markers.mean) ? \"#ff0000\" : \"#0000ff\";\n    // create svg literal with fill color\n    var svg = \"<svg fill=\\\"\".concat(color, \"\\\" xmlns=\\\"http://www.w3.org/2000/svg\\\" viewBox=\\\"0 0 240 240\\\" width=\\\"50\\\" height=\\\"50\\\">\\n<circle cx=\\\"120\\\" cy=\\\"120\\\" opacity=\\\".6\\\" r=\\\"70\\\" />\\n<circle cx=\\\"120\\\" cy=\\\"120\\\" opacity=\\\".3\\\" r=\\\"90\\\" />\\n<circle cx=\\\"120\\\" cy=\\\"120\\\" opacity=\\\".2\\\" r=\\\"110\\\" />\\n<text x=\\\"50%\\\" y=\\\"50%\\\" style=\\\"fill:#fff\\\" text-anchor=\\\"middle\\\" font-size=\\\"50\\\" dominant-baseline=\\\"middle\\\" font-family=\\\"roboto,arial,sans-serif\\\">\").concat(count, \"</text>\\n</svg>\");\n    var title = \"Cluster of \".concat(count, \" markers\"),\n      // adjust zIndex to be above other markers\n      zIndex = Number(google.maps.Marker.MAX_ZINDEX) + count;\n    if (MarkerUtils.isAdvancedMarkerAvailable(map)) {\n      // create cluster SVG element\n      var parser = new DOMParser();\n      var svgEl = parser.parseFromString(svg, \"image/svg+xml\").documentElement;\n      svgEl.setAttribute(\"transform\", \"translate(0 25)\");\n      var _clusterOptions = {\n        map,\n        position,\n        zIndex,\n        title,\n        content: svgEl\n      };\n      return new google.maps.marker.AdvancedMarkerElement(_clusterOptions);\n    }\n    var clusterOptions = {\n      position,\n      zIndex,\n      title,\n      icon: {\n        url: \"data:image/svg+xml;base64,\".concat(btoa(svg)),\n        anchor: new google.maps.Point(25, 25)\n      }\n    };\n    return new google.maps.Marker(clusterOptions);\n  }\n}\n\n/**\n * Copyright 2019 Google LLC. All Rights Reserved.\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n/**\n * Extends an object's prototype by another's.\n *\n * @param type1 The Type to be extended.\n * @param type2 The Type to extend with.\n * @ignore\n */\n// eslint-disable-next-line @typescript-eslint/no-explicit-any\nfunction extend(type1, type2) {\n  /* istanbul ignore next */\n  // eslint-disable-next-line prefer-const\n  for (var property in type2.prototype) {\n    type1.prototype[property] = type2.prototype[property];\n  }\n}\n/**\n * @ignore\n */\nclass OverlayViewSafe {\n  constructor() {\n    // MarkerClusterer implements google.maps.OverlayView interface. We use the\n    // extend function to extend MarkerClusterer with google.maps.OverlayView\n    // because it might not always be available when the code is defined so we\n    // look for it at the last possible moment. If it doesn't exist now then\n    // there is no point going ahead :)\n    extend(OverlayViewSafe, google.maps.OverlayView);\n  }\n}\n\n/**\n * Copyright 2021 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nvar MarkerClustererEvents;\n(function (MarkerClustererEvents) {\n  MarkerClustererEvents[\"CLUSTERING_BEGIN\"] = \"clusteringbegin\";\n  MarkerClustererEvents[\"CLUSTERING_END\"] = \"clusteringend\";\n  MarkerClustererEvents[\"CLUSTER_CLICK\"] = \"click\";\n})(MarkerClustererEvents || (MarkerClustererEvents = {}));\nvar defaultOnClusterClickHandler = (_, cluster, map) => {\n  map.fitBounds(cluster.bounds);\n};\n/**\n * MarkerClusterer creates and manages per-zoom-level clusters for large amounts\n * of markers. See {@link MarkerClustererOptions} for more details.\n *\n */\nclass MarkerClusterer extends OverlayViewSafe {\n  constructor(_ref15) {\n    var {\n      map,\n      markers = [],\n      algorithmOptions = {},\n      algorithm = new SuperClusterAlgorithm(algorithmOptions),\n      renderer = new DefaultRenderer(),\n      onClusterClick = defaultOnClusterClickHandler\n    } = _ref15;\n    super();\n    this.markers = [...markers];\n    this.clusters = [];\n    this.algorithm = algorithm;\n    this.renderer = renderer;\n    this.onClusterClick = onClusterClick;\n    if (map) {\n      this.setMap(map);\n    }\n  }\n  addMarker(marker, noDraw) {\n    if (this.markers.includes(marker)) {\n      return;\n    }\n    this.markers.push(marker);\n    if (!noDraw) {\n      this.render();\n    }\n  }\n  addMarkers(markers, noDraw) {\n    markers.forEach(marker => {\n      this.addMarker(marker, true);\n    });\n    if (!noDraw) {\n      this.render();\n    }\n  }\n  removeMarker(marker, noDraw) {\n    var index = this.markers.indexOf(marker);\n    if (index === -1) {\n      // Marker is not in our list of markers, so do nothing:\n      return false;\n    }\n    MarkerUtils.setMap(marker, null);\n    this.markers.splice(index, 1); // Remove the marker from the list of managed markers\n    if (!noDraw) {\n      this.render();\n    }\n    return true;\n  }\n  removeMarkers(markers, noDraw) {\n    var removed = false;\n    markers.forEach(marker => {\n      removed = this.removeMarker(marker, true) || removed;\n    });\n    if (removed && !noDraw) {\n      this.render();\n    }\n    return removed;\n  }\n  clearMarkers(noDraw) {\n    this.markers.length = 0;\n    if (!noDraw) {\n      this.render();\n    }\n  }\n  /**\n   * Recalculates and draws all the marker clusters.\n   */\n  render() {\n    var map = this.getMap();\n    if (map instanceof google.maps.Map && map.getProjection()) {\n      google.maps.event.trigger(this, MarkerClustererEvents.CLUSTERING_BEGIN, this);\n      var {\n        clusters,\n        changed\n      } = this.algorithm.calculate({\n        markers: this.markers,\n        map,\n        mapCanvasProjection: this.getProjection()\n      });\n      // Allow algorithms to return flag on whether the clusters/markers have changed.\n      if (changed || changed == undefined) {\n        // Accumulate the markers of the clusters composed of a single marker.\n        // Those clusters directly use the marker.\n        // Clusters with more than one markers use a group marker generated by a renderer.\n        var singleMarker = new Set();\n        for (var cluster of clusters) {\n          if (cluster.markers.length == 1) {\n            singleMarker.add(cluster.markers[0]);\n          }\n        }\n        var groupMarkers = [];\n        // Iterate the clusters that are currently rendered.\n        for (var _cluster2 of this.clusters) {\n          if (_cluster2.marker == null) {\n            continue;\n          }\n          if (_cluster2.markers.length == 1) {\n            if (!singleMarker.has(_cluster2.marker)) {\n              // The marker:\n              // - was previously rendered because it is from a cluster with 1 marker,\n              // - should no more be rendered as it is not in singleMarker.\n              MarkerUtils.setMap(_cluster2.marker, null);\n            }\n          } else {\n            // Delay the removal of old group markers to avoid flickering.\n            groupMarkers.push(_cluster2.marker);\n          }\n        }\n        this.clusters = clusters;\n        this.renderClusters();\n        // Delayed removal of the markers of the former groups.\n        requestAnimationFrame(() => groupMarkers.forEach(marker => MarkerUtils.setMap(marker, null)));\n      }\n      google.maps.event.trigger(this, MarkerClustererEvents.CLUSTERING_END, this);\n    }\n  }\n  onAdd() {\n    this.idleListener = this.getMap().addListener(\"idle\", this.render.bind(this));\n    this.render();\n  }\n  onRemove() {\n    google.maps.event.removeListener(this.idleListener);\n    this.reset();\n  }\n  reset() {\n    this.markers.forEach(marker => MarkerUtils.setMap(marker, null));\n    this.clusters.forEach(cluster => cluster.delete());\n    this.clusters = [];\n  }\n  renderClusters() {\n    // Generate stats to pass to renderers.\n    var stats = new ClusterStats(this.markers, this.clusters);\n    var map = this.getMap();\n    this.clusters.forEach(cluster => {\n      if (cluster.markers.length === 1) {\n        cluster.marker = cluster.markers[0];\n      } else {\n        // Generate the marker to represent the group.\n        cluster.marker = this.renderer.render(cluster, stats, map);\n        // Make sure all individual markers are removed from the map.\n        cluster.markers.forEach(marker => MarkerUtils.setMap(marker, null));\n        if (this.onClusterClick) {\n          cluster.marker.addListener(\"click\", /* istanbul ignore next */\n          event => {\n            google.maps.event.trigger(this, MarkerClustererEvents.CLUSTER_CLICK, cluster);\n            this.onClusterClick(event, cluster, map);\n          });\n        }\n      }\n      MarkerUtils.setMap(cluster.marker, map);\n    });\n  }\n}\n\nvar index_esm = /*#__PURE__*/Object.freeze({\n  __proto__: null,\n  AbstractAlgorithm: AbstractAlgorithm,\n  AbstractViewportAlgorithm: AbstractViewportAlgorithm,\n  Cluster: Cluster,\n  ClusterStats: ClusterStats,\n  DefaultRenderer: DefaultRenderer,\n  GridAlgorithm: GridAlgorithm,\n  MarkerClusterer: MarkerClusterer,\n  get MarkerClustererEvents () { return MarkerClustererEvents; },\n  MarkerUtils: MarkerUtils,\n  NoopAlgorithm: NoopAlgorithm,\n  SuperClusterAlgorithm: SuperClusterAlgorithm,\n  SuperClusterViewportAlgorithm: SuperClusterViewportAlgorithm,\n  defaultOnClusterClickHandler: defaultOnClusterClickHandler,\n  distanceBetweenPoints: distanceBetweenPoints,\n  extendBoundsToPaddedViewport: extendBoundsToPaddedViewport,\n  extendPixelBounds: extendPixelBounds,\n  filterMarkersToPaddedViewport: filterMarkersToPaddedViewport,\n  getPaddedViewport: getPaddedViewport,\n  noop: noop$1,\n  pixelBoundsToLatLngBounds: pixelBoundsToLatLngBounds\n});\n\nfunction ownKeys$a(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }\nfunction _objectSpread$a(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys$a(Object(t), !0).forEach(function (r) { _defineProperty(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys$a(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }\nfunction useGoogleMarkerClusterer(options) {\n  var map = useGoogleMap();\n  var [markerClusterer, setMarkerClusterer] = useState(null);\n  useEffect(() => {\n    if (map && markerClusterer === null) {\n      var markerCluster = new MarkerClusterer(_objectSpread$a(_objectSpread$a({}, options), {}, {\n        map\n      }));\n      setMarkerClusterer(markerCluster);\n    }\n  }, [map]);\n  return markerClusterer;\n}\n/** Wrapper around [@googlemaps/markerclusterer](https://github.com/googlemaps/js-markerclusterer)\n *\n * Accepts {@link  MarkerClustererOptionsSubset} which is a subset of  {@link MarkerClustererOptions}\n */\nfunction GoogleMarkerClusterer(_ref) {\n  var {\n    children,\n    options\n  } = _ref;\n  var markerClusterer = useGoogleMarkerClusterer(options);\n  return markerClusterer !== null ? children(markerClusterer) : null;\n}\nvar GoogleMarkerClusterer$1 = memo(GoogleMarkerClusterer);\n\nvar eventMap$c = {\n  onCloseClick: 'closeclick',\n  onContentChanged: 'content_changed',\n  onDomReady: 'domready',\n  onPositionChanged: 'position_changed',\n  onZindexChanged: 'zindex_changed'\n};\nvar updaterMap$c = {\n  options(instance, options) {\n    instance.setOptions(options);\n  },\n  position(instance, position) {\n    instance.setPosition(position);\n  },\n  zIndex(instance, zIndex) {\n    instance.setZIndex(zIndex);\n  }\n};\nfunction InfoWindowFunctional(_ref) {\n  var {\n    children,\n    anchor,\n    options,\n    position,\n    zIndex,\n    onCloseClick,\n    onDomReady,\n    onContentChanged,\n    onPositionChanged,\n    onZindexChanged,\n    onLoad,\n    onUnmount\n  } = _ref;\n  var map = useContext(MapContext);\n  var [instance, setInstance] = useState(null);\n  var [closeclickListener, setCloseClickListener] = useState(null);\n  var [domreadyclickListener, setDomReadyClickListener] = useState(null);\n  var [contentchangedclickListener, setContentChangedClickListener] = useState(null);\n  var [positionchangedclickListener, setPositionChangedClickListener] = useState(null);\n  var [zindexchangedclickListener, setZindexChangedClickListener] = useState(null);\n  var containerElementRef = useRef(null);\n  // Order does matter\n  useEffect(() => {\n    if (instance !== null) {\n      instance.close();\n      if (anchor) {\n        instance.open(map, anchor);\n      } else if (instance.getPosition()) {\n        instance.open(map);\n      }\n    }\n  }, [map, instance, anchor]);\n  useEffect(() => {\n    if (options && instance !== null) {\n      instance.setOptions(options);\n    }\n  }, [instance, options]);\n  useEffect(() => {\n    if (position && instance !== null) {\n      instance.setPosition(position);\n    }\n  }, [position]);\n  useEffect(() => {\n    if (typeof zIndex === 'number' && instance !== null) {\n      instance.setZIndex(zIndex);\n    }\n  }, [zIndex]);\n  useEffect(() => {\n    if (instance && onCloseClick) {\n      if (closeclickListener !== null) {\n        google.maps.event.removeListener(closeclickListener);\n      }\n      setCloseClickListener(google.maps.event.addListener(instance, 'closeclick', onCloseClick));\n    }\n  }, [onCloseClick]);\n  useEffect(() => {\n    if (instance && onDomReady) {\n      if (domreadyclickListener !== null) {\n        google.maps.event.removeListener(domreadyclickListener);\n      }\n      setDomReadyClickListener(google.maps.event.addListener(instance, 'domready', onDomReady));\n    }\n  }, [onDomReady]);\n  useEffect(() => {\n    if (instance && onContentChanged) {\n      if (contentchangedclickListener !== null) {\n        google.maps.event.removeListener(contentchangedclickListener);\n      }\n      setContentChangedClickListener(google.maps.event.addListener(instance, 'content_changed', onContentChanged));\n    }\n  }, [onContentChanged]);\n  useEffect(() => {\n    if (instance && onPositionChanged) {\n      if (positionchangedclickListener !== null) {\n        google.maps.event.removeListener(positionchangedclickListener);\n      }\n      setPositionChangedClickListener(google.maps.event.addListener(instance, 'position_changed', onPositionChanged));\n    }\n  }, [onPositionChanged]);\n  useEffect(() => {\n    if (instance && onZindexChanged) {\n      if (zindexchangedclickListener !== null) {\n        google.maps.event.removeListener(zindexchangedclickListener);\n      }\n      setZindexChangedClickListener(google.maps.event.addListener(instance, 'zindex_changed', onZindexChanged));\n    }\n  }, [onZindexChanged]);\n  useEffect(() => {\n    var infoWindow = new google.maps.InfoWindow(options);\n    setInstance(infoWindow);\n    containerElementRef.current = document.createElement('div');\n    if (onCloseClick) {\n      setCloseClickListener(google.maps.event.addListener(infoWindow, 'closeclick', onCloseClick));\n    }\n    if (onDomReady) {\n      setDomReadyClickListener(google.maps.event.addListener(infoWindow, 'domready', onDomReady));\n    }\n    if (onContentChanged) {\n      setContentChangedClickListener(google.maps.event.addListener(infoWindow, 'content_changed', onContentChanged));\n    }\n    if (onPositionChanged) {\n      setPositionChangedClickListener(google.maps.event.addListener(infoWindow, 'position_changed', onPositionChanged));\n    }\n    if (onZindexChanged) {\n      setZindexChangedClickListener(google.maps.event.addListener(infoWindow, 'zindex_changed', onZindexChanged));\n    }\n    infoWindow.setContent(containerElementRef.current);\n    if (position) {\n      infoWindow.setPosition(position);\n    }\n    if (zIndex) {\n      infoWindow.setZIndex(zIndex);\n    }\n    if (anchor) {\n      infoWindow.open(map, anchor);\n    } else if (infoWindow.getPosition()) {\n      infoWindow.open(map);\n    } else {\n      invariant(false, \"You must provide either an anchor (typically render it inside a <Marker>) or a position props for <InfoWindow>.\");\n    }\n    if (onLoad) {\n      onLoad(infoWindow);\n    }\n    return () => {\n      if (closeclickListener) {\n        google.maps.event.removeListener(closeclickListener);\n      }\n      if (contentchangedclickListener) {\n        google.maps.event.removeListener(contentchangedclickListener);\n      }\n      if (domreadyclickListener) {\n        google.maps.event.removeListener(domreadyclickListener);\n      }\n      if (positionchangedclickListener) {\n        google.maps.event.removeListener(positionchangedclickListener);\n      }\n      if (zindexchangedclickListener) {\n        google.maps.event.removeListener(zindexchangedclickListener);\n      }\n      if (onUnmount) {\n        onUnmount(infoWindow);\n      }\n      infoWindow.close();\n    };\n  }, []);\n  return containerElementRef.current ? createPortal(Children.only(children), containerElementRef.current) : null;\n}\nvar InfoWindowF = memo(InfoWindowFunctional);\nclass InfoWindow extends PureComponent {\n  constructor() {\n    super(...arguments);\n    _defineProperty(this, \"registeredEvents\", []);\n    _defineProperty(this, \"containerElement\", null);\n    _defineProperty(this, \"state\", {\n      infoWindow: null\n    });\n    _defineProperty(this, \"open\", (infoWindow, anchor) => {\n      if (anchor) {\n        infoWindow.open(this.context, anchor);\n      } else if (infoWindow.getPosition()) {\n        infoWindow.open(this.context);\n      } else {\n        invariant(false, \"You must provide either an anchor (typically render it inside a <Marker>) or a position props for <InfoWindow>.\");\n      }\n    });\n    _defineProperty(this, \"setInfoWindowCallback\", () => {\n      if (this.state.infoWindow !== null && this.containerElement !== null) {\n        this.state.infoWindow.setContent(this.containerElement);\n        this.open(this.state.infoWindow, this.props.anchor);\n        if (this.props.onLoad) {\n          this.props.onLoad(this.state.infoWindow);\n        }\n      }\n    });\n  }\n  componentDidMount() {\n    var infoWindow = new google.maps.InfoWindow(this.props.options);\n    this.containerElement = document.createElement('div');\n    this.registeredEvents = applyUpdatersToPropsAndRegisterEvents({\n      updaterMap: updaterMap$c,\n      eventMap: eventMap$c,\n      prevProps: {},\n      nextProps: this.props,\n      instance: infoWindow\n    });\n    this.setState(() => {\n      return {\n        infoWindow\n      };\n    }, this.setInfoWindowCallback);\n  }\n  componentDidUpdate(prevProps) {\n    if (this.state.infoWindow !== null) {\n      unregisterEvents(this.registeredEvents);\n      this.registeredEvents = applyUpdatersToPropsAndRegisterEvents({\n        updaterMap: updaterMap$c,\n        eventMap: eventMap$c,\n        prevProps,\n        nextProps: this.props,\n        instance: this.state.infoWindow\n      });\n    }\n  }\n  componentWillUnmount() {\n    if (this.state.infoWindow !== null) {\n      unregisterEvents(this.registeredEvents);\n      if (this.props.onUnmount) {\n        this.props.onUnmount(this.state.infoWindow);\n      }\n      this.state.infoWindow.close();\n    }\n  }\n  render() {\n    return this.containerElement ? createPortal(Children.only(this.props.children), this.containerElement) : null;\n  }\n}\n_defineProperty(InfoWindow, \"contextType\", MapContext);\n\nfunction ownKeys$9(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }\nfunction _objectSpread$9(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys$9(Object(t), !0).forEach(function (r) { _defineProperty(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys$9(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }\nvar eventMap$b = {\n  onClick: 'click',\n  onDblClick: 'dblclick',\n  onDrag: 'drag',\n  onDragEnd: 'dragend',\n  onDragStart: 'dragstart',\n  onMouseDown: 'mousedown',\n  onMouseMove: 'mousemove',\n  onMouseOut: 'mouseout',\n  onMouseOver: 'mouseover',\n  onMouseUp: 'mouseup',\n  onRightClick: 'rightclick'\n};\nvar updaterMap$b = {\n  draggable(instance, draggable) {\n    instance.setDraggable(draggable);\n  },\n  editable(instance, editable) {\n    instance.setEditable(editable);\n  },\n  map(instance, map) {\n    instance.setMap(map);\n  },\n  options(instance, options) {\n    instance.setOptions(options);\n  },\n  path(instance, path) {\n    instance.setPath(path);\n  },\n  visible(instance, visible) {\n    instance.setVisible(visible);\n  }\n};\nvar defaultOptions$1 = {};\nfunction PolylineFunctional(_ref) {\n  var {\n    options,\n    draggable,\n    editable,\n    visible,\n    path,\n    onDblClick,\n    onDragEnd,\n    onDragStart,\n    onMouseDown,\n    onMouseMove,\n    onMouseOut,\n    onMouseOver,\n    onMouseUp,\n    onRightClick,\n    onClick,\n    onDrag,\n    onLoad,\n    onUnmount\n  } = _ref;\n  var map = useContext(MapContext);\n  var [instance, setInstance] = useState(null);\n  var [dblclickListener, setDblclickListener] = useState(null);\n  var [dragendListener, setDragendListener] = useState(null);\n  var [dragstartListener, setDragstartListener] = useState(null);\n  var [mousedownListener, setMousedownListener] = useState(null);\n  var [mousemoveListener, setMousemoveListener] = useState(null);\n  var [mouseoutListener, setMouseoutListener] = useState(null);\n  var [mouseoverListener, setMouseoverListener] = useState(null);\n  var [mouseupListener, setMouseupListener] = useState(null);\n  var [rightclickListener, setRightclickListener] = useState(null);\n  var [clickListener, setClickListener] = useState(null);\n  var [dragListener, setDragListener] = useState(null);\n  // Order does matter\n  useEffect(() => {\n    if (instance !== null) {\n      instance.setMap(map);\n    }\n  }, [map]);\n  useEffect(() => {\n    if (typeof options !== 'undefined' && instance !== null) {\n      instance.setOptions(options);\n    }\n  }, [instance, options]);\n  useEffect(() => {\n    if (typeof draggable !== 'undefined' && instance !== null) {\n      instance.setDraggable(draggable);\n    }\n  }, [instance, draggable]);\n  useEffect(() => {\n    if (typeof editable !== 'undefined' && instance !== null) {\n      instance.setEditable(editable);\n    }\n  }, [instance, editable]);\n  useEffect(() => {\n    if (typeof visible !== 'undefined' && instance !== null) {\n      instance.setVisible(visible);\n    }\n  }, [instance, visible]);\n  useEffect(() => {\n    if (typeof path !== 'undefined' && instance !== null) {\n      instance.setPath(path);\n    }\n  }, [instance, path]);\n  useEffect(() => {\n    if (instance && onDblClick) {\n      if (dblclickListener !== null) {\n        google.maps.event.removeListener(dblclickListener);\n      }\n      setDblclickListener(google.maps.event.addListener(instance, 'dblclick', onDblClick));\n    }\n  }, [onDblClick]);\n  useEffect(() => {\n    if (instance && onDragEnd) {\n      if (dragendListener !== null) {\n        google.maps.event.removeListener(dragendListener);\n      }\n      setDragendListener(google.maps.event.addListener(instance, 'dragend', onDragEnd));\n    }\n  }, [onDragEnd]);\n  useEffect(() => {\n    if (instance && onDragStart) {\n      if (dragstartListener !== null) {\n        google.maps.event.removeListener(dragstartListener);\n      }\n      setDragstartListener(google.maps.event.addListener(instance, 'dragstart', onDragStart));\n    }\n  }, [onDragStart]);\n  useEffect(() => {\n    if (instance && onMouseDown) {\n      if (mousedownListener !== null) {\n        google.maps.event.removeListener(mousedownListener);\n      }\n      setMousedownListener(google.maps.event.addListener(instance, 'mousedown', onMouseDown));\n    }\n  }, [onMouseDown]);\n  useEffect(() => {\n    if (instance && onMouseMove) {\n      if (mousemoveListener !== null) {\n        google.maps.event.removeListener(mousemoveListener);\n      }\n      setMousemoveListener(google.maps.event.addListener(instance, 'mousemove', onMouseMove));\n    }\n  }, [onMouseMove]);\n  useEffect(() => {\n    if (instance && onMouseOut) {\n      if (mouseoutListener !== null) {\n        google.maps.event.removeListener(mouseoutListener);\n      }\n      setMouseoutListener(google.maps.event.addListener(instance, 'mouseout', onMouseOut));\n    }\n  }, [onMouseOut]);\n  useEffect(() => {\n    if (instance && onMouseOver) {\n      if (mouseoverListener !== null) {\n        google.maps.event.removeListener(mouseoverListener);\n      }\n      setMouseoverListener(google.maps.event.addListener(instance, 'mouseover', onMouseOver));\n    }\n  }, [onMouseOver]);\n  useEffect(() => {\n    if (instance && onMouseUp) {\n      if (mouseupListener !== null) {\n        google.maps.event.removeListener(mouseupListener);\n      }\n      setMouseupListener(google.maps.event.addListener(instance, 'mouseup', onMouseUp));\n    }\n  }, [onMouseUp]);\n  useEffect(() => {\n    if (instance && onRightClick) {\n      if (rightclickListener !== null) {\n        google.maps.event.removeListener(rightclickListener);\n      }\n      setRightclickListener(google.maps.event.addListener(instance, 'rightclick', onRightClick));\n    }\n  }, [onRightClick]);\n  useEffect(() => {\n    if (instance && onClick) {\n      if (clickListener !== null) {\n        google.maps.event.removeListener(clickListener);\n      }\n      setClickListener(google.maps.event.addListener(instance, 'click', onClick));\n    }\n  }, [onClick]);\n  useEffect(() => {\n    if (instance && onDrag) {\n      if (dragListener !== null) {\n        google.maps.event.removeListener(dragListener);\n      }\n      setDragListener(google.maps.event.addListener(instance, 'drag', onDrag));\n    }\n  }, [onDrag]);\n  useEffect(() => {\n    var polyline = new google.maps.Polyline(_objectSpread$9(_objectSpread$9({}, options || defaultOptions$1), {}, {\n      map\n    }));\n    if (path) {\n      polyline.setPath(path);\n    }\n    if (typeof visible !== 'undefined') {\n      polyline.setVisible(visible);\n    }\n    if (typeof editable !== 'undefined') {\n      polyline.setEditable(editable);\n    }\n    if (typeof draggable !== 'undefined') {\n      polyline.setDraggable(draggable);\n    }\n    if (onDblClick) {\n      setDblclickListener(google.maps.event.addListener(polyline, 'dblclick', onDblClick));\n    }\n    if (onDragEnd) {\n      setDragendListener(google.maps.event.addListener(polyline, 'dragend', onDragEnd));\n    }\n    if (onDragStart) {\n      setDragstartListener(google.maps.event.addListener(polyline, 'dragstart', onDragStart));\n    }\n    if (onMouseDown) {\n      setMousedownListener(google.maps.event.addListener(polyline, 'mousedown', onMouseDown));\n    }\n    if (onMouseMove) {\n      setMousemoveListener(google.maps.event.addListener(polyline, 'mousemove', onMouseMove));\n    }\n    if (onMouseOut) {\n      setMouseoutListener(google.maps.event.addListener(polyline, 'mouseout', onMouseOut));\n    }\n    if (onMouseOver) {\n      setMouseoverListener(google.maps.event.addListener(polyline, 'mouseover', onMouseOver));\n    }\n    if (onMouseUp) {\n      setMouseupListener(google.maps.event.addListener(polyline, 'mouseup', onMouseUp));\n    }\n    if (onRightClick) {\n      setRightclickListener(google.maps.event.addListener(polyline, 'rightclick', onRightClick));\n    }\n    if (onClick) {\n      setClickListener(google.maps.event.addListener(polyline, 'click', onClick));\n    }\n    if (onDrag) {\n      setDragListener(google.maps.event.addListener(polyline, 'drag', onDrag));\n    }\n    setInstance(polyline);\n    if (onLoad) {\n      onLoad(polyline);\n    }\n    return () => {\n      if (dblclickListener !== null) {\n        google.maps.event.removeListener(dblclickListener);\n      }\n      if (dragendListener !== null) {\n        google.maps.event.removeListener(dragendListener);\n      }\n      if (dragstartListener !== null) {\n        google.maps.event.removeListener(dragstartListener);\n      }\n      if (mousedownListener !== null) {\n        google.maps.event.removeListener(mousedownListener);\n      }\n      if (mousemoveListener !== null) {\n        google.maps.event.removeListener(mousemoveListener);\n      }\n      if (mouseoutListener !== null) {\n        google.maps.event.removeListener(mouseoutListener);\n      }\n      if (mouseoverListener !== null) {\n        google.maps.event.removeListener(mouseoverListener);\n      }\n      if (mouseupListener !== null) {\n        google.maps.event.removeListener(mouseupListener);\n      }\n      if (rightclickListener !== null) {\n        google.maps.event.removeListener(rightclickListener);\n      }\n      if (clickListener !== null) {\n        google.maps.event.removeListener(clickListener);\n      }\n      if (onUnmount) {\n        onUnmount(polyline);\n      }\n      polyline.setMap(null);\n    };\n  }, []);\n  return null;\n}\nvar PolylineF = memo(PolylineFunctional);\nclass Polyline extends PureComponent {\n  constructor() {\n    super(...arguments);\n    _defineProperty(this, \"registeredEvents\", []);\n    _defineProperty(this, \"state\", {\n      polyline: null\n    });\n    _defineProperty(this, \"setPolylineCallback\", () => {\n      if (this.state.polyline !== null && this.props.onLoad) {\n        this.props.onLoad(this.state.polyline);\n      }\n    });\n  }\n  componentDidMount() {\n    var polyline = new google.maps.Polyline(_objectSpread$9(_objectSpread$9({}, this.props.options), {}, {\n      map: this.context\n    }));\n    this.registeredEvents = applyUpdatersToPropsAndRegisterEvents({\n      updaterMap: updaterMap$b,\n      eventMap: eventMap$b,\n      prevProps: {},\n      nextProps: this.props,\n      instance: polyline\n    });\n    this.setState(function setPolyline() {\n      return {\n        polyline\n      };\n    }, this.setPolylineCallback);\n  }\n  componentDidUpdate(prevProps) {\n    if (this.state.polyline !== null) {\n      unregisterEvents(this.registeredEvents);\n      this.registeredEvents = applyUpdatersToPropsAndRegisterEvents({\n        updaterMap: updaterMap$b,\n        eventMap: eventMap$b,\n        prevProps,\n        nextProps: this.props,\n        instance: this.state.polyline\n      });\n    }\n  }\n  componentWillUnmount() {\n    if (this.state.polyline === null) {\n      return;\n    }\n    if (this.props.onUnmount) {\n      this.props.onUnmount(this.state.polyline);\n    }\n    unregisterEvents(this.registeredEvents);\n    this.state.polyline.setMap(null);\n  }\n  render() {\n    return null;\n  }\n}\n_defineProperty(Polyline, \"contextType\", MapContext);\n\nfunction ownKeys$8(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }\nfunction _objectSpread$8(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys$8(Object(t), !0).forEach(function (r) { _defineProperty(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys$8(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }\nvar eventMap$a = {\n  onClick: 'click',\n  onDblClick: 'dblclick',\n  onDrag: 'drag',\n  onDragEnd: 'dragend',\n  onDragStart: 'dragstart',\n  onMouseDown: 'mousedown',\n  onMouseMove: 'mousemove',\n  onMouseOut: 'mouseout',\n  onMouseOver: 'mouseover',\n  onMouseUp: 'mouseup',\n  onRightClick: 'rightclick'\n};\nvar updaterMap$a = {\n  draggable(instance, draggable) {\n    instance.setDraggable(draggable);\n  },\n  editable(instance, editable) {\n    instance.setEditable(editable);\n  },\n  map(instance, map) {\n    instance.setMap(map);\n  },\n  options(instance, options) {\n    instance.setOptions(options);\n  },\n  path(instance, path) {\n    instance.setPath(path);\n  },\n  paths(instance, paths) {\n    instance.setPaths(paths);\n  },\n  visible(instance, visible) {\n    instance.setVisible(visible);\n  }\n};\nfunction PolygonFunctional(_ref) {\n  var {\n    options,\n    draggable,\n    editable,\n    visible,\n    path,\n    paths,\n    onDblClick,\n    onDragEnd,\n    onDragStart,\n    onMouseDown,\n    onMouseMove,\n    onMouseOut,\n    onMouseOver,\n    onMouseUp,\n    onRightClick,\n    onClick,\n    onDrag,\n    onLoad,\n    onUnmount,\n    onEdit\n  } = _ref;\n  var map = useContext(MapContext);\n  var [instance, setInstance] = useState(null);\n  var [dblclickListener, setDblclickListener] = useState(null);\n  var [dragendListener, setDragendListener] = useState(null);\n  var [dragstartListener, setDragstartListener] = useState(null);\n  var [mousedownListener, setMousedownListener] = useState(null);\n  var [mousemoveListener, setMousemoveListener] = useState(null);\n  var [mouseoutListener, setMouseoutListener] = useState(null);\n  var [mouseoverListener, setMouseoverListener] = useState(null);\n  var [mouseupListener, setMouseupListener] = useState(null);\n  var [rightclickListener, setRightclickListener] = useState(null);\n  var [clickListener, setClickListener] = useState(null);\n  var [dragListener, setDragListener] = useState(null);\n  // Order does matter\n  useEffect(() => {\n    if (instance !== null) {\n      instance.setMap(map);\n    }\n  }, [map]);\n  useEffect(() => {\n    if (typeof options !== 'undefined' && instance !== null) {\n      instance.setOptions(options);\n    }\n  }, [instance, options]);\n  useEffect(() => {\n    if (typeof draggable !== 'undefined' && instance !== null) {\n      instance.setDraggable(draggable);\n    }\n  }, [instance, draggable]);\n  useEffect(() => {\n    if (typeof editable !== 'undefined' && instance !== null) {\n      instance.setEditable(editable);\n    }\n  }, [instance, editable]);\n  useEffect(() => {\n    if (typeof visible !== 'undefined' && instance !== null) {\n      instance.setVisible(visible);\n    }\n  }, [instance, visible]);\n  useEffect(() => {\n    if (typeof path !== 'undefined' && instance !== null) {\n      instance.setPath(path);\n    }\n  }, [instance, path]);\n  useEffect(() => {\n    if (typeof paths !== 'undefined' && instance !== null) {\n      instance.setPaths(paths);\n    }\n  }, [instance, paths]);\n  useEffect(() => {\n    if (instance && typeof onDblClick === 'function') {\n      if (dblclickListener !== null) {\n        google.maps.event.removeListener(dblclickListener);\n      }\n      setDblclickListener(google.maps.event.addListener(instance, 'dblclick', onDblClick));\n    }\n  }, [onDblClick]);\n  useEffect(() => {\n    if (!instance) {\n      return;\n    }\n    google.maps.event.addListener(instance.getPath(), 'insert_at', () => {\n      onEdit === null || onEdit === void 0 || onEdit(instance);\n    });\n    google.maps.event.addListener(instance.getPath(), 'set_at', () => {\n      onEdit === null || onEdit === void 0 || onEdit(instance);\n    });\n    google.maps.event.addListener(instance.getPath(), 'remove_at', () => {\n      onEdit === null || onEdit === void 0 || onEdit(instance);\n    });\n  }, [instance, onEdit]);\n  useEffect(() => {\n    if (instance && typeof onDragEnd === 'function') {\n      if (dragendListener !== null) {\n        google.maps.event.removeListener(dragendListener);\n      }\n      setDragendListener(google.maps.event.addListener(instance, 'dragend', onDragEnd));\n    }\n  }, [onDragEnd]);\n  useEffect(() => {\n    if (instance && typeof onDragStart === 'function') {\n      if (dragstartListener !== null) {\n        google.maps.event.removeListener(dragstartListener);\n      }\n      setDragstartListener(google.maps.event.addListener(instance, 'dragstart', onDragStart));\n    }\n  }, [onDragStart]);\n  useEffect(() => {\n    if (instance && typeof onMouseDown === 'function') {\n      if (mousedownListener !== null) {\n        google.maps.event.removeListener(mousedownListener);\n      }\n      setMousedownListener(google.maps.event.addListener(instance, 'mousedown', onMouseDown));\n    }\n  }, [onMouseDown]);\n  useEffect(() => {\n    if (instance && typeof onMouseMove === 'function') {\n      if (mousemoveListener !== null) {\n        google.maps.event.removeListener(mousemoveListener);\n      }\n      setMousemoveListener(google.maps.event.addListener(instance, 'mousemove', onMouseMove));\n    }\n  }, [onMouseMove]);\n  useEffect(() => {\n    if (instance && typeof onMouseOut === 'function') {\n      if (mouseoutListener !== null) {\n        google.maps.event.removeListener(mouseoutListener);\n      }\n      setMouseoutListener(google.maps.event.addListener(instance, 'mouseout', onMouseOut));\n    }\n  }, [onMouseOut]);\n  useEffect(() => {\n    if (instance && typeof onMouseOver === 'function') {\n      if (mouseoverListener !== null) {\n        google.maps.event.removeListener(mouseoverListener);\n      }\n      setMouseoverListener(google.maps.event.addListener(instance, 'mouseover', onMouseOver));\n    }\n  }, [onMouseOver]);\n  useEffect(() => {\n    if (instance && typeof onMouseUp === 'function') {\n      if (mouseupListener !== null) {\n        google.maps.event.removeListener(mouseupListener);\n      }\n      setMouseupListener(google.maps.event.addListener(instance, 'mouseup', onMouseUp));\n    }\n  }, [onMouseUp]);\n  useEffect(() => {\n    if (instance && typeof onRightClick === 'function') {\n      if (rightclickListener !== null) {\n        google.maps.event.removeListener(rightclickListener);\n      }\n      setRightclickListener(google.maps.event.addListener(instance, 'rightclick', onRightClick));\n    }\n  }, [onRightClick]);\n  useEffect(() => {\n    if (instance && typeof onClick === 'function') {\n      if (clickListener !== null) {\n        google.maps.event.removeListener(clickListener);\n      }\n      setClickListener(google.maps.event.addListener(instance, 'click', onClick));\n    }\n  }, [onClick]);\n  useEffect(() => {\n    if (instance && typeof onDrag === 'function') {\n      if (dragListener !== null) {\n        google.maps.event.removeListener(dragListener);\n      }\n      setDragListener(google.maps.event.addListener(instance, 'drag', onDrag));\n    }\n  }, [onDrag]);\n  useEffect(() => {\n    var polygon = new google.maps.Polygon(_objectSpread$8(_objectSpread$8({}, options), {}, {\n      map\n    }));\n    if (path) {\n      polygon.setPath(path);\n    }\n    if (paths) {\n      polygon.setPaths(paths);\n    }\n    if (typeof visible !== 'undefined') {\n      polygon.setVisible(visible);\n    }\n    if (typeof editable !== 'undefined') {\n      polygon.setEditable(editable);\n    }\n    if (typeof draggable !== 'undefined') {\n      polygon.setDraggable(draggable);\n    }\n    if (onDblClick) {\n      setDblclickListener(google.maps.event.addListener(polygon, 'dblclick', onDblClick));\n    }\n    if (onDragEnd) {\n      setDragendListener(google.maps.event.addListener(polygon, 'dragend', onDragEnd));\n    }\n    if (onDragStart) {\n      setDragstartListener(google.maps.event.addListener(polygon, 'dragstart', onDragStart));\n    }\n    if (onMouseDown) {\n      setMousedownListener(google.maps.event.addListener(polygon, 'mousedown', onMouseDown));\n    }\n    if (onMouseMove) {\n      setMousemoveListener(google.maps.event.addListener(polygon, 'mousemove', onMouseMove));\n    }\n    if (onMouseOut) {\n      setMouseoutListener(google.maps.event.addListener(polygon, 'mouseout', onMouseOut));\n    }\n    if (onMouseOver) {\n      setMouseoverListener(google.maps.event.addListener(polygon, 'mouseover', onMouseOver));\n    }\n    if (onMouseUp) {\n      setMouseupListener(google.maps.event.addListener(polygon, 'mouseup', onMouseUp));\n    }\n    if (onRightClick) {\n      setRightclickListener(google.maps.event.addListener(polygon, 'rightclick', onRightClick));\n    }\n    if (onClick) {\n      setClickListener(google.maps.event.addListener(polygon, 'click', onClick));\n    }\n    if (onDrag) {\n      setDragListener(google.maps.event.addListener(polygon, 'drag', onDrag));\n    }\n    setInstance(polygon);\n    if (onLoad) {\n      onLoad(polygon);\n    }\n    return () => {\n      if (dblclickListener !== null) {\n        google.maps.event.removeListener(dblclickListener);\n      }\n      if (dragendListener !== null) {\n        google.maps.event.removeListener(dragendListener);\n      }\n      if (dragstartListener !== null) {\n        google.maps.event.removeListener(dragstartListener);\n      }\n      if (mousedownListener !== null) {\n        google.maps.event.removeListener(mousedownListener);\n      }\n      if (mousemoveListener !== null) {\n        google.maps.event.removeListener(mousemoveListener);\n      }\n      if (mouseoutListener !== null) {\n        google.maps.event.removeListener(mouseoutListener);\n      }\n      if (mouseoverListener !== null) {\n        google.maps.event.removeListener(mouseoverListener);\n      }\n      if (mouseupListener !== null) {\n        google.maps.event.removeListener(mouseupListener);\n      }\n      if (rightclickListener !== null) {\n        google.maps.event.removeListener(rightclickListener);\n      }\n      if (clickListener !== null) {\n        google.maps.event.removeListener(clickListener);\n      }\n      if (onUnmount) {\n        onUnmount(polygon);\n      }\n      polygon.setMap(null);\n    };\n  }, []);\n  return null;\n}\nvar PolygonF = memo(PolygonFunctional);\nclass Polygon extends PureComponent {\n  constructor() {\n    super(...arguments);\n    _defineProperty(this, \"registeredEvents\", []);\n  }\n  componentDidMount() {\n    var polygonOptions = this.props.options || {};\n    this.polygon = new google.maps.Polygon(polygonOptions);\n    this.polygon.setMap(this.context);\n    this.registeredEvents = applyUpdatersToPropsAndRegisterEvents({\n      updaterMap: updaterMap$a,\n      eventMap: eventMap$a,\n      prevProps: {},\n      nextProps: this.props,\n      instance: this.polygon\n    });\n    if (this.props.onLoad) {\n      this.props.onLoad(this.polygon);\n    }\n  }\n  componentDidUpdate(prevProps) {\n    if (this.polygon) {\n      unregisterEvents(this.registeredEvents);\n      this.registeredEvents = applyUpdatersToPropsAndRegisterEvents({\n        updaterMap: updaterMap$a,\n        eventMap: eventMap$a,\n        prevProps,\n        nextProps: this.props,\n        instance: this.polygon\n      });\n    }\n  }\n  componentWillUnmount() {\n    if (this.polygon) {\n      if (this.props.onUnmount) {\n        this.props.onUnmount(this.polygon);\n      }\n      unregisterEvents(this.registeredEvents);\n      if (this.polygon) {\n        this.polygon.setMap(null);\n      }\n    }\n  }\n  render() {\n    return null;\n  }\n}\n_defineProperty(Polygon, \"contextType\", MapContext);\n\nfunction ownKeys$7(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }\nfunction _objectSpread$7(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys$7(Object(t), !0).forEach(function (r) { _defineProperty(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys$7(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }\nvar eventMap$9 = {\n  onBoundsChanged: 'bounds_changed',\n  onClick: 'click',\n  onDblClick: 'dblclick',\n  onDrag: 'drag',\n  onDragEnd: 'dragend',\n  onDragStart: 'dragstart',\n  onMouseDown: 'mousedown',\n  onMouseMove: 'mousemove',\n  onMouseOut: 'mouseout',\n  onMouseOver: 'mouseover',\n  onMouseUp: 'mouseup',\n  onRightClick: 'rightclick'\n};\nvar updaterMap$9 = {\n  bounds(instance, bounds) {\n    instance.setBounds(bounds);\n  },\n  draggable(instance, draggable) {\n    instance.setDraggable(draggable);\n  },\n  editable(instance, editable) {\n    instance.setEditable(editable);\n  },\n  map(instance, map) {\n    instance.setMap(map);\n  },\n  options(instance, options) {\n    instance.setOptions(options);\n  },\n  visible(instance, visible) {\n    instance.setVisible(visible);\n  }\n};\nfunction RectangleFunctional(_ref) {\n  var {\n    options,\n    bounds,\n    draggable,\n    editable,\n    visible,\n    onDblClick,\n    onDragEnd,\n    onDragStart,\n    onMouseDown,\n    onMouseMove,\n    onMouseOut,\n    onMouseOver,\n    onMouseUp,\n    onRightClick,\n    onClick,\n    onDrag,\n    onBoundsChanged,\n    onLoad,\n    onUnmount\n  } = _ref;\n  var map = useContext(MapContext);\n  var [instance, setInstance] = useState(null);\n  var [dblclickListener, setDblclickListener] = useState(null);\n  var [dragendListener, setDragendListener] = useState(null);\n  var [dragstartListener, setDragstartListener] = useState(null);\n  var [mousedownListener, setMousedownListener] = useState(null);\n  var [mousemoveListener, setMousemoveListener] = useState(null);\n  var [mouseoutListener, setMouseoutListener] = useState(null);\n  var [mouseoverListener, setMouseoverListener] = useState(null);\n  var [mouseupListener, setMouseupListener] = useState(null);\n  var [rightClickListener, setRightClickListener] = useState(null);\n  var [clickListener, setClickListener] = useState(null);\n  var [dragListener, setDragListener] = useState(null);\n  var [boundsChangedListener, setBoundsChangedListener] = useState(null);\n  // Order does matter\n  useEffect(() => {\n    if (instance !== null) {\n      instance.setMap(map);\n    }\n  }, [map]);\n  useEffect(() => {\n    if (typeof options !== 'undefined' && instance !== null) {\n      instance.setOptions(options);\n    }\n  }, [instance, options]);\n  useEffect(() => {\n    if (typeof draggable !== 'undefined' && instance !== null) {\n      instance.setDraggable(draggable);\n    }\n  }, [instance, draggable]);\n  useEffect(() => {\n    if (typeof editable !== 'undefined' && instance !== null) {\n      instance.setEditable(editable);\n    }\n  }, [instance, editable]);\n  useEffect(() => {\n    if (typeof visible !== 'undefined' && instance !== null) {\n      instance.setVisible(visible);\n    }\n  }, [instance, visible]);\n  useEffect(() => {\n    if (typeof bounds !== 'undefined' && instance !== null) {\n      instance.setBounds(bounds);\n    }\n  }, [instance, bounds]);\n  useEffect(() => {\n    if (instance && onDblClick) {\n      if (dblclickListener !== null) {\n        google.maps.event.removeListener(dblclickListener);\n      }\n      setDblclickListener(google.maps.event.addListener(instance, 'dblclick', onDblClick));\n    }\n  }, [onDblClick]);\n  useEffect(() => {\n    if (instance && onDragEnd) {\n      if (dragendListener !== null) {\n        google.maps.event.removeListener(dragendListener);\n      }\n      setDragendListener(google.maps.event.addListener(instance, 'dragend', onDragEnd));\n    }\n  }, [onDragEnd]);\n  useEffect(() => {\n    if (instance && onDragStart) {\n      if (dragstartListener !== null) {\n        google.maps.event.removeListener(dragstartListener);\n      }\n      setDragstartListener(google.maps.event.addListener(instance, 'dragstart', onDragStart));\n    }\n  }, [onDragStart]);\n  useEffect(() => {\n    if (instance && onMouseDown) {\n      if (mousedownListener !== null) {\n        google.maps.event.removeListener(mousedownListener);\n      }\n      setMousedownListener(google.maps.event.addListener(instance, 'mousedown', onMouseDown));\n    }\n  }, [onMouseDown]);\n  useEffect(() => {\n    if (instance && onMouseMove) {\n      if (mousemoveListener !== null) {\n        google.maps.event.removeListener(mousemoveListener);\n      }\n      setMousemoveListener(google.maps.event.addListener(instance, 'mousemove', onMouseMove));\n    }\n  }, [onMouseMove]);\n  useEffect(() => {\n    if (instance && onMouseOut) {\n      if (mouseoutListener !== null) {\n        google.maps.event.removeListener(mouseoutListener);\n      }\n      setMouseoutListener(google.maps.event.addListener(instance, 'mouseout', onMouseOut));\n    }\n  }, [onMouseOut]);\n  useEffect(() => {\n    if (instance && onMouseOver) {\n      if (mouseoverListener !== null) {\n        google.maps.event.removeListener(mouseoverListener);\n      }\n      setMouseoverListener(google.maps.event.addListener(instance, 'mouseover', onMouseOver));\n    }\n  }, [onMouseOver]);\n  useEffect(() => {\n    if (instance && onMouseUp) {\n      if (mouseupListener !== null) {\n        google.maps.event.removeListener(mouseupListener);\n      }\n      setMouseupListener(google.maps.event.addListener(instance, 'mouseup', onMouseUp));\n    }\n  }, [onMouseUp]);\n  useEffect(() => {\n    if (instance && onRightClick) {\n      if (rightClickListener !== null) {\n        google.maps.event.removeListener(rightClickListener);\n      }\n      setRightClickListener(google.maps.event.addListener(instance, 'rightclick', onRightClick));\n    }\n  }, [onRightClick]);\n  useEffect(() => {\n    if (instance && onClick) {\n      if (clickListener !== null) {\n        google.maps.event.removeListener(clickListener);\n      }\n      setClickListener(google.maps.event.addListener(instance, 'click', onClick));\n    }\n  }, [onClick]);\n  useEffect(() => {\n    if (instance && onDrag) {\n      if (dragListener !== null) {\n        google.maps.event.removeListener(dragListener);\n      }\n      setDragListener(google.maps.event.addListener(instance, 'drag', onDrag));\n    }\n  }, [onDrag]);\n  useEffect(() => {\n    if (instance && onBoundsChanged) {\n      if (boundsChangedListener !== null) {\n        google.maps.event.removeListener(boundsChangedListener);\n      }\n      setBoundsChangedListener(google.maps.event.addListener(instance, 'bounds_changed', onBoundsChanged));\n    }\n  }, [onBoundsChanged]);\n  useEffect(() => {\n    var rectangle = new google.maps.Rectangle(_objectSpread$7(_objectSpread$7({}, options), {}, {\n      map\n    }));\n    if (typeof visible !== 'undefined') {\n      rectangle.setVisible(visible);\n    }\n    if (typeof editable !== 'undefined') {\n      rectangle.setEditable(editable);\n    }\n    if (typeof draggable !== 'undefined') {\n      rectangle.setDraggable(draggable);\n    }\n    if (typeof bounds !== 'undefined') {\n      rectangle.setBounds(bounds);\n    }\n    if (onDblClick) {\n      setDblclickListener(google.maps.event.addListener(rectangle, 'dblclick', onDblClick));\n    }\n    if (onDragEnd) {\n      setDragendListener(google.maps.event.addListener(rectangle, 'dragend', onDragEnd));\n    }\n    if (onDragStart) {\n      setDragstartListener(google.maps.event.addListener(rectangle, 'dragstart', onDragStart));\n    }\n    if (onMouseDown) {\n      setMousedownListener(google.maps.event.addListener(rectangle, 'mousedown', onMouseDown));\n    }\n    if (onMouseMove) {\n      setMousemoveListener(google.maps.event.addListener(rectangle, 'mousemove', onMouseMove));\n    }\n    if (onMouseOut) {\n      setMouseoutListener(google.maps.event.addListener(rectangle, 'mouseout', onMouseOut));\n    }\n    if (onMouseOver) {\n      setMouseoverListener(google.maps.event.addListener(rectangle, 'mouseover', onMouseOver));\n    }\n    if (onMouseUp) {\n      setMouseupListener(google.maps.event.addListener(rectangle, 'mouseup', onMouseUp));\n    }\n    if (onRightClick) {\n      setRightClickListener(google.maps.event.addListener(rectangle, 'rightclick', onRightClick));\n    }\n    if (onClick) {\n      setClickListener(google.maps.event.addListener(rectangle, 'click', onClick));\n    }\n    if (onDrag) {\n      setDragListener(google.maps.event.addListener(rectangle, 'drag', onDrag));\n    }\n    if (onBoundsChanged) {\n      setBoundsChangedListener(google.maps.event.addListener(rectangle, 'bounds_changed', onBoundsChanged));\n    }\n    setInstance(rectangle);\n    if (onLoad) {\n      onLoad(rectangle);\n    }\n    return () => {\n      if (dblclickListener !== null) {\n        google.maps.event.removeListener(dblclickListener);\n      }\n      if (dragendListener !== null) {\n        google.maps.event.removeListener(dragendListener);\n      }\n      if (dragstartListener !== null) {\n        google.maps.event.removeListener(dragstartListener);\n      }\n      if (mousedownListener !== null) {\n        google.maps.event.removeListener(mousedownListener);\n      }\n      if (mousemoveListener !== null) {\n        google.maps.event.removeListener(mousemoveListener);\n      }\n      if (mouseoutListener !== null) {\n        google.maps.event.removeListener(mouseoutListener);\n      }\n      if (mouseoverListener !== null) {\n        google.maps.event.removeListener(mouseoverListener);\n      }\n      if (mouseupListener !== null) {\n        google.maps.event.removeListener(mouseupListener);\n      }\n      if (rightClickListener !== null) {\n        google.maps.event.removeListener(rightClickListener);\n      }\n      if (clickListener !== null) {\n        google.maps.event.removeListener(clickListener);\n      }\n      if (dragListener !== null) {\n        google.maps.event.removeListener(dragListener);\n      }\n      if (boundsChangedListener !== null) {\n        google.maps.event.removeListener(boundsChangedListener);\n      }\n      if (onUnmount) {\n        onUnmount(rectangle);\n      }\n      rectangle.setMap(null);\n    };\n  }, []);\n  return null;\n}\nvar RectangleF = memo(RectangleFunctional);\nclass Rectangle extends PureComponent {\n  constructor() {\n    super(...arguments);\n    _defineProperty(this, \"registeredEvents\", []);\n    _defineProperty(this, \"state\", {\n      rectangle: null\n    });\n    _defineProperty(this, \"setRectangleCallback\", () => {\n      if (this.state.rectangle !== null && this.props.onLoad) {\n        this.props.onLoad(this.state.rectangle);\n      }\n    });\n  }\n  componentDidMount() {\n    var rectangle = new google.maps.Rectangle(_objectSpread$7(_objectSpread$7({}, this.props.options), {}, {\n      map: this.context\n    }));\n    this.registeredEvents = applyUpdatersToPropsAndRegisterEvents({\n      updaterMap: updaterMap$9,\n      eventMap: eventMap$9,\n      prevProps: {},\n      nextProps: this.props,\n      instance: rectangle\n    });\n    this.setState(function setRectangle() {\n      return {\n        rectangle\n      };\n    }, this.setRectangleCallback);\n  }\n  componentDidUpdate(prevProps) {\n    if (this.state.rectangle !== null) {\n      unregisterEvents(this.registeredEvents);\n      this.registeredEvents = applyUpdatersToPropsAndRegisterEvents({\n        updaterMap: updaterMap$9,\n        eventMap: eventMap$9,\n        prevProps,\n        nextProps: this.props,\n        instance: this.state.rectangle\n      });\n    }\n  }\n  componentWillUnmount() {\n    if (this.state.rectangle !== null) {\n      if (this.props.onUnmount) {\n        this.props.onUnmount(this.state.rectangle);\n      }\n      unregisterEvents(this.registeredEvents);\n      this.state.rectangle.setMap(null);\n    }\n  }\n  render() {\n    return null;\n  }\n}\n_defineProperty(Rectangle, \"contextType\", MapContext);\n\nfunction ownKeys$6(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }\nfunction _objectSpread$6(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys$6(Object(t), !0).forEach(function (r) { _defineProperty(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys$6(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }\nvar eventMap$8 = {\n  onCenterChanged: 'center_changed',\n  onRadiusChanged: 'radius_changed',\n  onClick: 'click',\n  onDblClick: 'dblclick',\n  onDrag: 'drag',\n  onDragEnd: 'dragend',\n  onDragStart: 'dragstart',\n  onMouseDown: 'mousedown',\n  onMouseMove: 'mousemove',\n  onMouseOut: 'mouseout',\n  onMouseOver: 'mouseover',\n  onMouseUp: 'mouseup',\n  onRightClick: 'rightclick'\n};\nvar updaterMap$8 = {\n  center(instance, center) {\n    instance.setCenter(center);\n  },\n  draggable(instance, draggable) {\n    instance.setDraggable(draggable);\n  },\n  editable(instance, editable) {\n    instance.setEditable(editable);\n  },\n  map(instance, map) {\n    instance.setMap(map);\n  },\n  options(instance, options) {\n    instance.setOptions(options);\n  },\n  radius(instance, radius) {\n    instance.setRadius(radius);\n  },\n  visible(instance, visible) {\n    instance.setVisible(visible);\n  }\n};\nvar defaultOptions = {};\nfunction CircleFunctional(_ref) {\n  var {\n    options,\n    center,\n    radius,\n    draggable,\n    editable,\n    visible,\n    onDblClick,\n    onDragEnd,\n    onDragStart,\n    onMouseDown,\n    onMouseMove,\n    onMouseOut,\n    onMouseOver,\n    onMouseUp,\n    onRightClick,\n    onClick,\n    onDrag,\n    onCenterChanged,\n    onRadiusChanged,\n    onLoad,\n    onUnmount\n  } = _ref;\n  var map = useContext(MapContext);\n  var [instance, setInstance] = useState(null);\n  var [dblclickListener, setDblclickListener] = useState(null);\n  var [dragendListener, setDragendListener] = useState(null);\n  var [dragstartListener, setDragstartListener] = useState(null);\n  var [mousedownListener, setMousedownListener] = useState(null);\n  var [mousemoveListener, setMousemoveListener] = useState(null);\n  var [mouseoutListener, setMouseoutListener] = useState(null);\n  var [mouseoverListener, setMouseoverListener] = useState(null);\n  var [mouseupListener, setMouseupListener] = useState(null);\n  var [rightclickListener, setRightclickListener] = useState(null);\n  var [clickListener, setClickListener] = useState(null);\n  var [dragListener, setDragListener] = useState(null);\n  var [centerChangedListener, setCenterChangedListener] = useState(null);\n  var [radiusChangedListener, setRadiusChangedListener] = useState(null);\n  // Order does matter\n  useEffect(() => {\n    if (instance !== null) {\n      instance.setMap(map);\n    }\n  }, [map]);\n  useEffect(() => {\n    if (typeof options !== 'undefined' && instance !== null) {\n      instance.setOptions(options);\n    }\n  }, [instance, options]);\n  useEffect(() => {\n    if (typeof draggable !== 'undefined' && instance !== null) {\n      instance.setDraggable(draggable);\n    }\n  }, [instance, draggable]);\n  useEffect(() => {\n    if (typeof editable !== 'undefined' && instance !== null) {\n      instance.setEditable(editable);\n    }\n  }, [instance, editable]);\n  useEffect(() => {\n    if (typeof visible !== 'undefined' && instance !== null) {\n      instance.setVisible(visible);\n    }\n  }, [instance, visible]);\n  useEffect(() => {\n    if (typeof radius === 'number' && instance !== null) {\n      instance.setRadius(radius);\n    }\n  }, [instance, radius]);\n  useEffect(() => {\n    if (typeof center !== 'undefined' && instance !== null) {\n      instance.setCenter(center);\n    }\n  }, [instance, center]);\n  useEffect(() => {\n    if (instance && onDblClick) {\n      if (dblclickListener !== null) {\n        google.maps.event.removeListener(dblclickListener);\n      }\n      setDblclickListener(google.maps.event.addListener(instance, 'dblclick', onDblClick));\n    }\n  }, [onDblClick]);\n  useEffect(() => {\n    if (instance && onDragEnd) {\n      if (dragendListener !== null) {\n        google.maps.event.removeListener(dragendListener);\n      }\n      setDragendListener(google.maps.event.addListener(instance, 'dragend', onDragEnd));\n    }\n  }, [onDragEnd]);\n  useEffect(() => {\n    if (instance && onDragStart) {\n      if (dragstartListener !== null) {\n        google.maps.event.removeListener(dragstartListener);\n      }\n      setDragstartListener(google.maps.event.addListener(instance, 'dragstart', onDragStart));\n    }\n  }, [onDragStart]);\n  useEffect(() => {\n    if (instance && onMouseDown) {\n      if (mousedownListener !== null) {\n        google.maps.event.removeListener(mousedownListener);\n      }\n      setMousedownListener(google.maps.event.addListener(instance, 'mousedown', onMouseDown));\n    }\n  }, [onMouseDown]);\n  useEffect(() => {\n    if (instance && onMouseMove) {\n      if (mousemoveListener !== null) {\n        google.maps.event.removeListener(mousemoveListener);\n      }\n      setMousemoveListener(google.maps.event.addListener(instance, 'mousemove', onMouseMove));\n    }\n  }, [onMouseMove]);\n  useEffect(() => {\n    if (instance && onMouseOut) {\n      if (mouseoutListener !== null) {\n        google.maps.event.removeListener(mouseoutListener);\n      }\n      setMouseoutListener(google.maps.event.addListener(instance, 'mouseout', onMouseOut));\n    }\n  }, [onMouseOut]);\n  useEffect(() => {\n    if (instance && onMouseOver) {\n      if (mouseoverListener !== null) {\n        google.maps.event.removeListener(mouseoverListener);\n      }\n      setMouseoverListener(google.maps.event.addListener(instance, 'mouseover', onMouseOver));\n    }\n  }, [onMouseOver]);\n  useEffect(() => {\n    if (instance && onMouseUp) {\n      if (mouseupListener !== null) {\n        google.maps.event.removeListener(mouseupListener);\n      }\n      setMouseupListener(google.maps.event.addListener(instance, 'mouseup', onMouseUp));\n    }\n  }, [onMouseUp]);\n  useEffect(() => {\n    if (instance && onRightClick) {\n      if (rightclickListener !== null) {\n        google.maps.event.removeListener(rightclickListener);\n      }\n      setRightclickListener(google.maps.event.addListener(instance, 'rightclick', onRightClick));\n    }\n  }, [onRightClick]);\n  useEffect(() => {\n    if (instance && onClick) {\n      if (clickListener !== null) {\n        google.maps.event.removeListener(clickListener);\n      }\n      setClickListener(google.maps.event.addListener(instance, 'click', onClick));\n    }\n  }, [onClick]);\n  useEffect(() => {\n    if (instance && onDrag) {\n      if (dragListener !== null) {\n        google.maps.event.removeListener(dragListener);\n      }\n      setDragListener(google.maps.event.addListener(instance, 'drag', onDrag));\n    }\n  }, [onDrag]);\n  useEffect(() => {\n    if (instance && onCenterChanged) {\n      if (centerChangedListener !== null) {\n        google.maps.event.removeListener(centerChangedListener);\n      }\n      setCenterChangedListener(google.maps.event.addListener(instance, 'center_changed', onCenterChanged));\n    }\n  }, [onClick]);\n  useEffect(() => {\n    if (instance && onRadiusChanged) {\n      if (radiusChangedListener !== null) {\n        google.maps.event.removeListener(radiusChangedListener);\n      }\n      setRadiusChangedListener(google.maps.event.addListener(instance, 'radius_changed', onRadiusChanged));\n    }\n  }, [onRadiusChanged]);\n  useEffect(() => {\n    var circle = new google.maps.Circle(_objectSpread$6(_objectSpread$6({}, options || defaultOptions), {}, {\n      map\n    }));\n    if (typeof radius === 'number') {\n      circle.setRadius(radius);\n    }\n    if (typeof center !== 'undefined') {\n      circle.setCenter(center);\n    }\n    if (typeof radius === 'number') {\n      circle.setRadius(radius);\n    }\n    if (typeof visible !== 'undefined') {\n      circle.setVisible(visible);\n    }\n    if (typeof editable !== 'undefined') {\n      circle.setEditable(editable);\n    }\n    if (typeof draggable !== 'undefined') {\n      circle.setDraggable(draggable);\n    }\n    if (onDblClick) {\n      setDblclickListener(google.maps.event.addListener(circle, 'dblclick', onDblClick));\n    }\n    if (onDragEnd) {\n      setDragendListener(google.maps.event.addListener(circle, 'dragend', onDragEnd));\n    }\n    if (onDragStart) {\n      setDragstartListener(google.maps.event.addListener(circle, 'dragstart', onDragStart));\n    }\n    if (onMouseDown) {\n      setMousedownListener(google.maps.event.addListener(circle, 'mousedown', onMouseDown));\n    }\n    if (onMouseMove) {\n      setMousemoveListener(google.maps.event.addListener(circle, 'mousemove', onMouseMove));\n    }\n    if (onMouseOut) {\n      setMouseoutListener(google.maps.event.addListener(circle, 'mouseout', onMouseOut));\n    }\n    if (onMouseOver) {\n      setMouseoverListener(google.maps.event.addListener(circle, 'mouseover', onMouseOver));\n    }\n    if (onMouseUp) {\n      setMouseupListener(google.maps.event.addListener(circle, 'mouseup', onMouseUp));\n    }\n    if (onRightClick) {\n      setRightclickListener(google.maps.event.addListener(circle, 'rightclick', onRightClick));\n    }\n    if (onClick) {\n      setClickListener(google.maps.event.addListener(circle, 'click', onClick));\n    }\n    if (onDrag) {\n      setDragListener(google.maps.event.addListener(circle, 'drag', onDrag));\n    }\n    if (onCenterChanged) {\n      setCenterChangedListener(google.maps.event.addListener(circle, 'center_changed', onCenterChanged));\n    }\n    if (onRadiusChanged) {\n      setRadiusChangedListener(google.maps.event.addListener(circle, 'radius_changed', onRadiusChanged));\n    }\n    setInstance(circle);\n    if (onLoad) {\n      onLoad(circle);\n    }\n    return () => {\n      if (dblclickListener !== null) {\n        google.maps.event.removeListener(dblclickListener);\n      }\n      if (dragendListener !== null) {\n        google.maps.event.removeListener(dragendListener);\n      }\n      if (dragstartListener !== null) {\n        google.maps.event.removeListener(dragstartListener);\n      }\n      if (mousedownListener !== null) {\n        google.maps.event.removeListener(mousedownListener);\n      }\n      if (mousemoveListener !== null) {\n        google.maps.event.removeListener(mousemoveListener);\n      }\n      if (mouseoutListener !== null) {\n        google.maps.event.removeListener(mouseoutListener);\n      }\n      if (mouseoverListener !== null) {\n        google.maps.event.removeListener(mouseoverListener);\n      }\n      if (mouseupListener !== null) {\n        google.maps.event.removeListener(mouseupListener);\n      }\n      if (rightclickListener !== null) {\n        google.maps.event.removeListener(rightclickListener);\n      }\n      if (clickListener !== null) {\n        google.maps.event.removeListener(clickListener);\n      }\n      if (centerChangedListener !== null) {\n        google.maps.event.removeListener(centerChangedListener);\n      }\n      if (radiusChangedListener !== null) {\n        google.maps.event.removeListener(radiusChangedListener);\n      }\n      if (onUnmount) {\n        onUnmount(circle);\n      }\n      circle.setMap(null);\n    };\n  }, []);\n  return null;\n}\nvar CircleF = memo(CircleFunctional);\nclass Circle extends PureComponent {\n  constructor() {\n    super(...arguments);\n    _defineProperty(this, \"registeredEvents\", []);\n    _defineProperty(this, \"state\", {\n      circle: null\n    });\n    _defineProperty(this, \"setCircleCallback\", () => {\n      if (this.state.circle !== null && this.props.onLoad) {\n        this.props.onLoad(this.state.circle);\n      }\n    });\n  }\n  componentDidMount() {\n    var circle = new google.maps.Circle(_objectSpread$6(_objectSpread$6({}, this.props.options), {}, {\n      map: this.context\n    }));\n    this.registeredEvents = applyUpdatersToPropsAndRegisterEvents({\n      updaterMap: updaterMap$8,\n      eventMap: eventMap$8,\n      prevProps: {},\n      nextProps: this.props,\n      instance: circle\n    });\n    this.setState(function setCircle() {\n      return {\n        circle\n      };\n    }, this.setCircleCallback);\n  }\n  componentDidUpdate(prevProps) {\n    if (this.state.circle !== null) {\n      unregisterEvents(this.registeredEvents);\n      this.registeredEvents = applyUpdatersToPropsAndRegisterEvents({\n        updaterMap: updaterMap$8,\n        eventMap: eventMap$8,\n        prevProps,\n        nextProps: this.props,\n        instance: this.state.circle\n      });\n    }\n  }\n  componentWillUnmount() {\n    if (this.state.circle !== null) {\n      var _this$state$circle;\n      if (this.props.onUnmount) {\n        this.props.onUnmount(this.state.circle);\n      }\n      unregisterEvents(this.registeredEvents);\n      (_this$state$circle = this.state.circle) === null || _this$state$circle === void 0 || _this$state$circle.setMap(null);\n    }\n  }\n  render() {\n    return null;\n  }\n}\n_defineProperty(Circle, \"contextType\", MapContext);\n\nfunction ownKeys$5(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }\nfunction _objectSpread$5(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys$5(Object(t), !0).forEach(function (r) { _defineProperty(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys$5(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }\nvar eventMap$7 = {\n  onClick: 'click',\n  onDblClick: 'dblclick',\n  onMouseDown: 'mousedown',\n  onMouseOut: 'mouseout',\n  onMouseOver: 'mouseover',\n  onMouseUp: 'mouseup',\n  onRightClick: 'rightclick',\n  onAddFeature: 'addfeature',\n  onRemoveFeature: 'removefeature',\n  onRemoveProperty: 'removeproperty',\n  onSetGeometry: 'setgeometry',\n  onSetProperty: 'setproperty'\n};\nvar updaterMap$7 = {\n  add(instance, feature) {\n    instance.add(feature);\n  },\n  addgeojson(instance, geojson, options) {\n    instance.addGeoJson(geojson, options);\n  },\n  contains(instance, feature) {\n    instance.contains(feature);\n  },\n  foreach(instance, callback) {\n    instance.forEach(callback);\n  },\n  loadgeojson(instance, url, options, callback) {\n    instance.loadGeoJson(url, options, callback);\n  },\n  overridestyle(instance, feature, style) {\n    instance.overrideStyle(feature, style);\n  },\n  remove(instance, feature) {\n    instance.remove(feature);\n  },\n  revertstyle(instance, feature) {\n    instance.revertStyle(feature);\n  },\n  controlposition(instance, controlPosition) {\n    instance.setControlPosition(controlPosition);\n  },\n  controls(instance, controls) {\n    instance.setControls(controls);\n  },\n  drawingmode(instance, mode) {\n    instance.setDrawingMode(mode);\n  },\n  map(instance, map) {\n    instance.setMap(map);\n  },\n  style(instance, style) {\n    instance.setStyle(style);\n  },\n  togeojson(instance, callback) {\n    instance.toGeoJson(callback);\n  }\n};\nfunction DataFunctional(_ref) {\n  var {\n    options,\n    onClick,\n    onDblClick,\n    onMouseDown,\n    onMouseMove,\n    onMouseOut,\n    onMouseOver,\n    onMouseUp,\n    onRightClick,\n    onAddFeature,\n    onRemoveFeature,\n    onRemoveProperty,\n    onSetGeometry,\n    onSetProperty,\n    onLoad,\n    onUnmount\n  } = _ref;\n  var map = useContext(MapContext);\n  var [instance, setInstance] = useState(null);\n  var [dblclickListener, setDblclickListener] = useState(null);\n  var [mousedownListener, setMousedownListener] = useState(null);\n  var [mousemoveListener, setMousemoveListener] = useState(null);\n  var [mouseoutListener, setMouseoutListener] = useState(null);\n  var [mouseoverListener, setMouseoverListener] = useState(null);\n  var [mouseupListener, setMouseupListener] = useState(null);\n  var [rightclickListener, setRightclickListener] = useState(null);\n  var [clickListener, setClickListener] = useState(null);\n  var [addFeatureListener, setAddFeatureListener] = useState(null);\n  var [removeFeatureListener, setRemoveFeatureListener] = useState(null);\n  var [removePropertyListener, setRemovePropertyListener] = useState(null);\n  var [setGeometryListener, setSetGeometryListener] = useState(null);\n  var [setPropertyListener, setSetPropertyListener] = useState(null);\n  // Order does matter\n  useEffect(() => {\n    if (instance !== null) {\n      instance.setMap(map);\n    }\n  }, [map]);\n  useEffect(() => {\n    if (instance && onDblClick) {\n      if (dblclickListener !== null) {\n        google.maps.event.removeListener(dblclickListener);\n      }\n      setDblclickListener(google.maps.event.addListener(instance, 'dblclick', onDblClick));\n    }\n  }, [onDblClick]);\n  useEffect(() => {\n    if (instance && onMouseDown) {\n      if (mousedownListener !== null) {\n        google.maps.event.removeListener(mousedownListener);\n      }\n      setMousedownListener(google.maps.event.addListener(instance, 'mousedown', onMouseDown));\n    }\n  }, [onMouseDown]);\n  useEffect(() => {\n    if (instance && onMouseMove) {\n      if (mousemoveListener !== null) {\n        google.maps.event.removeListener(mousemoveListener);\n      }\n      setMousemoveListener(google.maps.event.addListener(instance, 'mousemove', onMouseMove));\n    }\n  }, [onMouseMove]);\n  useEffect(() => {\n    if (instance && onMouseOut) {\n      if (mouseoutListener !== null) {\n        google.maps.event.removeListener(mouseoutListener);\n      }\n      setMouseoutListener(google.maps.event.addListener(instance, 'mouseout', onMouseOut));\n    }\n  }, [onMouseOut]);\n  useEffect(() => {\n    if (instance && onMouseOver) {\n      if (mouseoverListener !== null) {\n        google.maps.event.removeListener(mouseoverListener);\n      }\n      setMouseoverListener(google.maps.event.addListener(instance, 'mouseover', onMouseOver));\n    }\n  }, [onMouseOver]);\n  useEffect(() => {\n    if (instance && onMouseUp) {\n      if (mouseupListener !== null) {\n        google.maps.event.removeListener(mouseupListener);\n      }\n      setMouseupListener(google.maps.event.addListener(instance, 'mouseup', onMouseUp));\n    }\n  }, [onMouseUp]);\n  useEffect(() => {\n    if (instance && onRightClick) {\n      if (rightclickListener !== null) {\n        google.maps.event.removeListener(rightclickListener);\n      }\n      setRightclickListener(google.maps.event.addListener(instance, 'rightclick', onRightClick));\n    }\n  }, [onRightClick]);\n  useEffect(() => {\n    if (instance && onClick) {\n      if (clickListener !== null) {\n        google.maps.event.removeListener(clickListener);\n      }\n      setClickListener(google.maps.event.addListener(instance, 'click', onClick));\n    }\n  }, [onClick]);\n  useEffect(() => {\n    if (instance && onAddFeature) {\n      if (addFeatureListener !== null) {\n        google.maps.event.removeListener(addFeatureListener);\n      }\n      setAddFeatureListener(google.maps.event.addListener(instance, 'addfeature', onAddFeature));\n    }\n  }, [onAddFeature]);\n  useEffect(() => {\n    if (instance && onRemoveFeature) {\n      if (removeFeatureListener !== null) {\n        google.maps.event.removeListener(removeFeatureListener);\n      }\n      setRemoveFeatureListener(google.maps.event.addListener(instance, 'removefeature', onRemoveFeature));\n    }\n  }, [onRemoveFeature]);\n  useEffect(() => {\n    if (instance && onRemoveProperty) {\n      if (removePropertyListener !== null) {\n        google.maps.event.removeListener(removePropertyListener);\n      }\n      setRemovePropertyListener(google.maps.event.addListener(instance, 'removeproperty', onRemoveProperty));\n    }\n  }, [onRemoveProperty]);\n  useEffect(() => {\n    if (instance && onSetGeometry) {\n      if (setGeometryListener !== null) {\n        google.maps.event.removeListener(setGeometryListener);\n      }\n      setSetGeometryListener(google.maps.event.addListener(instance, 'setgeometry', onSetGeometry));\n    }\n  }, [onSetGeometry]);\n  useEffect(() => {\n    if (instance && onSetProperty) {\n      if (setPropertyListener !== null) {\n        google.maps.event.removeListener(setPropertyListener);\n      }\n      setSetPropertyListener(google.maps.event.addListener(instance, 'setproperty', onSetProperty));\n    }\n  }, [onSetProperty]);\n  useEffect(() => {\n    if (map !== null) {\n      var data = new google.maps.Data(_objectSpread$5(_objectSpread$5({}, options), {}, {\n        map\n      }));\n      if (onDblClick) {\n        setDblclickListener(google.maps.event.addListener(data, 'dblclick', onDblClick));\n      }\n      if (onMouseDown) {\n        setMousedownListener(google.maps.event.addListener(data, 'mousedown', onMouseDown));\n      }\n      if (onMouseMove) {\n        setMousemoveListener(google.maps.event.addListener(data, 'mousemove', onMouseMove));\n      }\n      if (onMouseOut) {\n        setMouseoutListener(google.maps.event.addListener(data, 'mouseout', onMouseOut));\n      }\n      if (onMouseOver) {\n        setMouseoverListener(google.maps.event.addListener(data, 'mouseover', onMouseOver));\n      }\n      if (onMouseUp) {\n        setMouseupListener(google.maps.event.addListener(data, 'mouseup', onMouseUp));\n      }\n      if (onRightClick) {\n        setRightclickListener(google.maps.event.addListener(data, 'rightclick', onRightClick));\n      }\n      if (onClick) {\n        setClickListener(google.maps.event.addListener(data, 'click', onClick));\n      }\n      if (onAddFeature) {\n        setAddFeatureListener(google.maps.event.addListener(data, 'addfeature', onAddFeature));\n      }\n      if (onRemoveFeature) {\n        setRemoveFeatureListener(google.maps.event.addListener(data, 'removefeature', onRemoveFeature));\n      }\n      if (onRemoveProperty) {\n        setRemovePropertyListener(google.maps.event.addListener(data, 'removeproperty', onRemoveProperty));\n      }\n      if (onSetGeometry) {\n        setSetGeometryListener(google.maps.event.addListener(data, 'setgeometry', onSetGeometry));\n      }\n      if (onSetProperty) {\n        setSetPropertyListener(google.maps.event.addListener(data, 'setproperty', onSetProperty));\n      }\n      setInstance(data);\n      if (onLoad) {\n        onLoad(data);\n      }\n    }\n    return () => {\n      if (instance) {\n        if (dblclickListener !== null) {\n          google.maps.event.removeListener(dblclickListener);\n        }\n        if (mousedownListener !== null) {\n          google.maps.event.removeListener(mousedownListener);\n        }\n        if (mousemoveListener !== null) {\n          google.maps.event.removeListener(mousemoveListener);\n        }\n        if (mouseoutListener !== null) {\n          google.maps.event.removeListener(mouseoutListener);\n        }\n        if (mouseoverListener !== null) {\n          google.maps.event.removeListener(mouseoverListener);\n        }\n        if (mouseupListener !== null) {\n          google.maps.event.removeListener(mouseupListener);\n        }\n        if (rightclickListener !== null) {\n          google.maps.event.removeListener(rightclickListener);\n        }\n        if (clickListener !== null) {\n          google.maps.event.removeListener(clickListener);\n        }\n        if (addFeatureListener !== null) {\n          google.maps.event.removeListener(addFeatureListener);\n        }\n        if (removeFeatureListener !== null) {\n          google.maps.event.removeListener(removeFeatureListener);\n        }\n        if (removePropertyListener !== null) {\n          google.maps.event.removeListener(removePropertyListener);\n        }\n        if (setGeometryListener !== null) {\n          google.maps.event.removeListener(setGeometryListener);\n        }\n        if (setPropertyListener !== null) {\n          google.maps.event.removeListener(setPropertyListener);\n        }\n        if (onUnmount) {\n          onUnmount(instance);\n        }\n        instance.setMap(null);\n      }\n    };\n  }, []);\n  return null;\n}\nvar DataF = memo(DataFunctional);\nclass Data extends PureComponent {\n  constructor() {\n    super(...arguments);\n    _defineProperty(this, \"registeredEvents\", []);\n    _defineProperty(this, \"state\", {\n      data: null\n    });\n    _defineProperty(this, \"setDataCallback\", () => {\n      if (this.state.data !== null && this.props.onLoad) {\n        this.props.onLoad(this.state.data);\n      }\n    });\n  }\n  componentDidMount() {\n    if (this.context !== null) {\n      var data = new google.maps.Data(_objectSpread$5(_objectSpread$5({}, this.props.options), {}, {\n        map: this.context\n      }));\n      this.registeredEvents = applyUpdatersToPropsAndRegisterEvents({\n        updaterMap: updaterMap$7,\n        eventMap: eventMap$7,\n        prevProps: {},\n        nextProps: this.props,\n        instance: data\n      });\n      this.setState(() => {\n        return {\n          data\n        };\n      }, this.setDataCallback);\n    }\n  }\n  componentDidUpdate(prevProps) {\n    if (this.state.data !== null) {\n      unregisterEvents(this.registeredEvents);\n      this.registeredEvents = applyUpdatersToPropsAndRegisterEvents({\n        updaterMap: updaterMap$7,\n        eventMap: eventMap$7,\n        prevProps,\n        nextProps: this.props,\n        instance: this.state.data\n      });\n    }\n  }\n  componentWillUnmount() {\n    if (this.state.data !== null) {\n      if (this.props.onUnmount) {\n        this.props.onUnmount(this.state.data);\n      }\n      unregisterEvents(this.registeredEvents);\n      if (this.state.data) {\n        this.state.data.setMap(null);\n      }\n    }\n  }\n  render() {\n    return null;\n  }\n}\n_defineProperty(Data, \"contextType\", MapContext);\n\nfunction ownKeys$4(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }\nfunction _objectSpread$4(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys$4(Object(t), !0).forEach(function (r) { _defineProperty(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys$4(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }\nvar eventMap$6 = {\n  onClick: 'click',\n  onDefaultViewportChanged: 'defaultviewport_changed',\n  onStatusChanged: 'status_changed'\n};\nvar updaterMap$6 = {\n  options(instance, options) {\n    instance.setOptions(options);\n  },\n  url(instance, url) {\n    instance.setUrl(url);\n  },\n  zIndex(instance, zIndex) {\n    instance.setZIndex(zIndex);\n  }\n};\nclass KmlLayer extends PureComponent {\n  constructor() {\n    super(...arguments);\n    _defineProperty(this, \"registeredEvents\", []);\n    _defineProperty(this, \"state\", {\n      kmlLayer: null\n    });\n    _defineProperty(this, \"setKmlLayerCallback\", () => {\n      if (this.state.kmlLayer !== null && this.props.onLoad) {\n        this.props.onLoad(this.state.kmlLayer);\n      }\n    });\n  }\n  componentDidMount() {\n    var kmlLayer = new google.maps.KmlLayer(_objectSpread$4(_objectSpread$4({}, this.props.options), {}, {\n      map: this.context\n    }));\n    this.registeredEvents = applyUpdatersToPropsAndRegisterEvents({\n      updaterMap: updaterMap$6,\n      eventMap: eventMap$6,\n      prevProps: {},\n      nextProps: this.props,\n      instance: kmlLayer\n    });\n    this.setState(function setLmlLayer() {\n      return {\n        kmlLayer\n      };\n    }, this.setKmlLayerCallback);\n  }\n  componentDidUpdate(prevProps) {\n    if (this.state.kmlLayer !== null) {\n      unregisterEvents(this.registeredEvents);\n      this.registeredEvents = applyUpdatersToPropsAndRegisterEvents({\n        updaterMap: updaterMap$6,\n        eventMap: eventMap$6,\n        prevProps,\n        nextProps: this.props,\n        instance: this.state.kmlLayer\n      });\n    }\n  }\n  componentWillUnmount() {\n    if (this.state.kmlLayer !== null) {\n      if (this.props.onUnmount) {\n        this.props.onUnmount(this.state.kmlLayer);\n      }\n      unregisterEvents(this.registeredEvents);\n      this.state.kmlLayer.setMap(null);\n    }\n  }\n  render() {\n    return null;\n  }\n}\n_defineProperty(KmlLayer, \"contextType\", MapContext);\n\nfunction getOffsetOverride(containerElement, getPixelPositionOffset) {\n  return typeof getPixelPositionOffset === 'function' ? getPixelPositionOffset(containerElement.offsetWidth, containerElement.offsetHeight) : {\n    x: 0,\n    y: 0\n  };\n}\n// eslint-disable-next-line @typescript-eslint/no-explicit-any\nfunction createLatLng(inst, Type) {\n  return new Type(inst.lat, inst.lng);\n}\n// eslint-disable-next-line @typescript-eslint/no-explicit-any\nfunction createLatLngBounds(inst, Type) {\n  return new Type(new google.maps.LatLng(inst.ne.lat, inst.ne.lng), new google.maps.LatLng(inst.sw.lat, inst.sw.lng));\n}\n// eslint-disable-next-line @typescript-eslint/no-explicit-any\nfunction ensureOfType(inst,\n// eslint-disable-next-line @typescript-eslint/no-explicit-any\ntype,\n// eslint-disable-next-line @typescript-eslint/no-explicit-any\nfactory\n// eslint-disable-next-line @typescript-eslint/no-explicit-any\n) {\n  return inst instanceof type ? inst : factory(inst, type);\n}\nfunction ensureOfTypeBounds(inst,\n// eslint-disable-next-line @typescript-eslint/no-explicit-any\ntype,\n// eslint-disable-next-line @typescript-eslint/no-explicit-any\nfactory\n// eslint-disable-next-line @typescript-eslint/no-explicit-any\n) {\n  return inst instanceof type ? inst : factory(inst, type);\n}\nfunction getLayoutStylesByBounds(mapCanvasProjection, offset, bounds) {\n  var ne = mapCanvasProjection && mapCanvasProjection.fromLatLngToDivPixel(bounds.getNorthEast());\n  var sw = mapCanvasProjection && mapCanvasProjection.fromLatLngToDivPixel(bounds.getSouthWest());\n  if (ne && sw) {\n    return {\n      left: \"\".concat(sw.x + offset.x, \"px\"),\n      top: \"\".concat(ne.y + offset.y, \"px\"),\n      width: \"\".concat(ne.x - sw.x - offset.x, \"px\"),\n      height: \"\".concat(sw.y - ne.y - offset.y, \"px\")\n    };\n  }\n  return {\n    left: '-9999px',\n    top: '-9999px'\n  };\n}\nfunction getLayoutStylesByPosition(mapCanvasProjection, offset, position) {\n  var point = mapCanvasProjection && mapCanvasProjection.fromLatLngToDivPixel(position);\n  if (point) {\n    var {\n      x,\n      y\n    } = point;\n    return {\n      left: \"\".concat(x + offset.x, \"px\"),\n      top: \"\".concat(y + offset.y, \"px\")\n    };\n  }\n  return {\n    left: '-9999px',\n    top: '-9999px'\n  };\n}\nfunction getLayoutStyles(mapCanvasProjection, offset, bounds, position) {\n  return bounds !== undefined ? getLayoutStylesByBounds(mapCanvasProjection, offset, ensureOfTypeBounds(bounds, google.maps.LatLngBounds, createLatLngBounds)) : getLayoutStylesByPosition(mapCanvasProjection, offset, ensureOfType(position, google.maps.LatLng, createLatLng));\n}\nfunction arePositionsEqual(currentPosition, previousPosition) {\n  return currentPosition.left === previousPosition.left && currentPosition.top === previousPosition.top && currentPosition.width === previousPosition.height && currentPosition.height === previousPosition.height;\n}\n\nfunction ownKeys$3(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }\nfunction _objectSpread$3(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys$3(Object(t), !0).forEach(function (r) { _defineProperty(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys$3(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }\nfunction createOverlay(container, pane, position, bounds, getPixelPositionOffset) {\n  class Overlay extends google.maps.OverlayView {\n    constructor(container, pane, position, bounds) {\n      super();\n      this.container = container;\n      this.pane = pane;\n      this.position = position;\n      this.bounds = bounds;\n    }\n    onAdd() {\n      var _this$getPanes;\n      var pane = (_this$getPanes = this.getPanes()) === null || _this$getPanes === void 0 ? void 0 : _this$getPanes[this.pane];\n      pane === null || pane === void 0 || pane.appendChild(this.container);\n    }\n    draw() {\n      var projection = this.getProjection();\n      var offset = _objectSpread$3({}, this.container ? getOffsetOverride(this.container, getPixelPositionOffset) : {\n        x: 0,\n        y: 0\n      });\n      var layoutStyles = getLayoutStyles(projection, offset, this.bounds, this.position);\n      for (var [key, value] of Object.entries(layoutStyles)) {\n        // eslint-disable-next-line @typescript-eslint/ban-ts-comment\n        // @ts-ignore\n        this.container.style[key] = value;\n      }\n    }\n    onRemove() {\n      if (this.container.parentNode !== null) {\n        this.container.parentNode.removeChild(this.container);\n      }\n    }\n  }\n  return new Overlay(container, pane, position, bounds);\n}\n\nfunction ownKeys$2(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }\nfunction _objectSpread$2(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys$2(Object(t), !0).forEach(function (r) { _defineProperty(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys$2(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }\nfunction convertToLatLngString(latLngLike) {\n  if (!latLngLike) {\n    return '';\n  }\n  var latLng = latLngLike instanceof google.maps.LatLng ? latLngLike : new google.maps.LatLng(latLngLike.lat, latLngLike.lng);\n  return latLng + '';\n}\nfunction convertToLatLngBoundsString(latLngBoundsLike) {\n  if (!latLngBoundsLike) {\n    return '';\n  }\n  var latLngBounds = latLngBoundsLike instanceof google.maps.LatLngBounds ? latLngBoundsLike : new google.maps.LatLngBounds(new google.maps.LatLng(latLngBoundsLike.south, latLngBoundsLike.east), new google.maps.LatLng(latLngBoundsLike.north, latLngBoundsLike.west));\n  return latLngBounds + '';\n}\nvar FLOAT_PANE = \"floatPane\";\nvar MAP_PANE = \"mapPane\";\nvar MARKER_LAYER = \"markerLayer\";\nvar OVERLAY_LAYER = \"overlayLayer\";\nvar OVERLAY_MOUSE_TARGET = \"overlayMouseTarget\";\nfunction OverlayViewFunctional(_ref) {\n  var {\n    position,\n    bounds,\n    mapPaneName,\n    zIndex,\n    onLoad,\n    onUnmount,\n    getPixelPositionOffset,\n    children\n  } = _ref;\n  var map = useContext(MapContext);\n  var container = useMemo(() => {\n    var div = document.createElement('div');\n    div.style.position = 'absolute';\n    return div;\n  }, []);\n  var overlay = useMemo(() => {\n    return createOverlay(container, mapPaneName, position, bounds, getPixelPositionOffset);\n  }, [container, mapPaneName, position, bounds]);\n  useEffect(() => {\n    onLoad === null || onLoad === void 0 || onLoad(overlay);\n    overlay === null || overlay === void 0 || overlay.setMap(map);\n    return () => {\n      onUnmount === null || onUnmount === void 0 || onUnmount(overlay);\n      overlay === null || overlay === void 0 || overlay.setMap(null);\n    };\n  }, [map, overlay]);\n  // to move the container to the foreground and background\n  useEffect(() => {\n    container.style.zIndex = \"\".concat(zIndex);\n  }, [zIndex, container]);\n  return ReactDOM.createPortal(children, container);\n}\nvar OverlayViewF = memo(OverlayViewFunctional);\nclass OverlayView extends PureComponent {\n  constructor(props) {\n    super(props);\n    _defineProperty(this, \"state\", {\n      paneEl: null,\n      containerStyle: {\n        // set initial position\n        position: 'absolute'\n      }\n    });\n    _defineProperty(this, \"updatePane\", () => {\n      var mapPaneName = this.props.mapPaneName;\n      // https://developers.google.com/maps/documentation/javascript/3.exp/reference#MapPanes\n      var mapPanes = this.overlayView.getPanes();\n      invariant(!!mapPaneName, \"OverlayView requires props.mapPaneName but got %s\", mapPaneName);\n      if (mapPanes) {\n        this.setState({\n          paneEl: mapPanes[mapPaneName]\n        });\n      } else {\n        this.setState({\n          paneEl: null\n        });\n      }\n    });\n    _defineProperty(this, \"onAdd\", () => {\n      var _this$props$onLoad, _this$props;\n      this.updatePane();\n      (_this$props$onLoad = (_this$props = this.props).onLoad) === null || _this$props$onLoad === void 0 || _this$props$onLoad.call(_this$props, this.overlayView);\n    });\n    _defineProperty(this, \"onPositionElement\", () => {\n      var mapCanvasProjection = this.overlayView.getProjection();\n      var offset = _objectSpread$2({\n        x: 0,\n        y: 0\n      }, this.containerRef.current ? getOffsetOverride(this.containerRef.current, this.props.getPixelPositionOffset) : {});\n      var layoutStyles = getLayoutStyles(mapCanvasProjection, offset, this.props.bounds, this.props.position);\n      if (!arePositionsEqual(layoutStyles, {\n        // eslint-disable-next-line @typescript-eslint/ban-ts-comment\n        // @ts-ignore\n        left: this.state.containerStyle.left,\n        // eslint-disable-next-line @typescript-eslint/ban-ts-comment\n        // @ts-ignore\n        top: this.state.containerStyle.top,\n        // eslint-disable-next-line @typescript-eslint/ban-ts-comment\n        // @ts-ignore\n        width: this.state.containerStyle.width,\n        // eslint-disable-next-line @typescript-eslint/ban-ts-comment\n        // @ts-ignore\n        height: this.state.containerStyle.height\n      })) {\n        var _layoutStyles$top, _layoutStyles$left, _layoutStyles$width, _layoutStyles$height;\n        this.setState({\n          containerStyle: {\n            top: (_layoutStyles$top = layoutStyles.top) !== null && _layoutStyles$top !== void 0 ? _layoutStyles$top : 0,\n            left: (_layoutStyles$left = layoutStyles.left) !== null && _layoutStyles$left !== void 0 ? _layoutStyles$left : 0,\n            width: (_layoutStyles$width = layoutStyles.width) !== null && _layoutStyles$width !== void 0 ? _layoutStyles$width : 0,\n            height: (_layoutStyles$height = layoutStyles.height) !== null && _layoutStyles$height !== void 0 ? _layoutStyles$height : 0,\n            position: 'absolute'\n          }\n        });\n      }\n    });\n    _defineProperty(this, \"draw\", () => {\n      this.onPositionElement();\n    });\n    _defineProperty(this, \"onRemove\", () => {\n      var _this$props$onUnmount, _this$props2;\n      this.setState(() => ({\n        paneEl: null\n      }));\n      (_this$props$onUnmount = (_this$props2 = this.props).onUnmount) === null || _this$props$onUnmount === void 0 || _this$props$onUnmount.call(_this$props2, this.overlayView);\n    });\n    this.containerRef = createRef();\n    // You must implement three methods: onAdd(), draw(), and onRemove().\n    var overlayView = new google.maps.OverlayView();\n    overlayView.onAdd = this.onAdd;\n    overlayView.draw = this.draw;\n    overlayView.onRemove = this.onRemove;\n    this.overlayView = overlayView;\n  }\n  componentDidMount() {\n    this.overlayView.setMap(this.context);\n  }\n  componentDidUpdate(prevProps) {\n    var prevPositionString = convertToLatLngString(prevProps.position);\n    var positionString = convertToLatLngString(this.props.position);\n    var prevBoundsString = convertToLatLngBoundsString(prevProps.bounds);\n    var boundsString = convertToLatLngBoundsString(this.props.bounds);\n    if (prevPositionString !== positionString || prevBoundsString !== boundsString) {\n      this.overlayView.draw();\n    }\n    if (prevProps.mapPaneName !== this.props.mapPaneName) {\n      this.updatePane();\n    }\n  }\n  componentWillUnmount() {\n    this.overlayView.setMap(null);\n  }\n  render() {\n    var paneEl = this.state.paneEl;\n    if (paneEl) {\n      return ReactDOM.createPortal(jsx(\"div\", {\n        ref: this.containerRef,\n        style: this.state.containerStyle,\n        children: Children.only(this.props.children)\n      }), paneEl);\n    } else {\n      return null;\n    }\n  }\n}\n_defineProperty(OverlayView, \"FLOAT_PANE\", \"floatPane\");\n_defineProperty(OverlayView, \"MAP_PANE\", \"mapPane\");\n_defineProperty(OverlayView, \"MARKER_LAYER\", \"markerLayer\");\n_defineProperty(OverlayView, \"OVERLAY_LAYER\", \"overlayLayer\");\n_defineProperty(OverlayView, \"OVERLAY_MOUSE_TARGET\", \"overlayMouseTarget\");\n_defineProperty(OverlayView, \"contextType\", MapContext);\n\nfunction noop() {\n  return;\n}\n\nfunction ownKeys$1(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }\nfunction _objectSpread$1(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys$1(Object(t), !0).forEach(function (r) { _defineProperty(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys$1(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }\nvar eventMap$5 = {\n  onDblClick: 'dblclick',\n  onClick: 'click'\n};\nvar updaterMap$5 = {\n  opacity(instance, opacity) {\n    instance.setOpacity(opacity);\n  }\n};\nfunction GroundOverlayFunctional(_ref) {\n  var {\n    url,\n    bounds,\n    options,\n    visible\n  } = _ref;\n  var map = useContext(MapContext);\n  var imageBounds = new google.maps.LatLngBounds(new google.maps.LatLng(bounds.south, bounds.west), new google.maps.LatLng(bounds.north, bounds.east));\n  var groundOverlay = useMemo(() => {\n    return new google.maps.GroundOverlay(url, imageBounds, options);\n  }, []);\n  useEffect(() => {\n    if (groundOverlay !== null) {\n      groundOverlay.setMap(map);\n    }\n  }, [map]);\n  useEffect(() => {\n    if (typeof url !== 'undefined' && groundOverlay !== null) {\n      groundOverlay.set('url', url);\n      groundOverlay.setMap(map);\n    }\n  }, [groundOverlay, url]);\n  useEffect(() => {\n    if (typeof visible !== 'undefined' && groundOverlay !== null) {\n      groundOverlay.setOpacity(visible ? 1 : 0);\n    }\n  }, [groundOverlay, visible]);\n  useEffect(() => {\n    var newBounds = new google.maps.LatLngBounds(new google.maps.LatLng(bounds.south, bounds.west), new google.maps.LatLng(bounds.north, bounds.east));\n    if (typeof bounds !== 'undefined' && groundOverlay !== null) {\n      groundOverlay.set('bounds', newBounds);\n      groundOverlay.setMap(map);\n    }\n  }, [groundOverlay, bounds]);\n  return null;\n}\nvar GroundOverlayF = memo(GroundOverlayFunctional);\nclass GroundOverlay extends PureComponent {\n  constructor() {\n    super(...arguments);\n    _defineProperty(this, \"registeredEvents\", []);\n    _defineProperty(this, \"state\", {\n      groundOverlay: null\n    });\n    _defineProperty(this, \"setGroundOverlayCallback\", () => {\n      if (this.state.groundOverlay !== null && this.props.onLoad) {\n        this.props.onLoad(this.state.groundOverlay);\n      }\n    });\n  }\n  componentDidMount() {\n    invariant(!!this.props.url || !!this.props.bounds, \"For GroundOverlay, url and bounds are passed in to constructor and are immutable after instantiated. This is the behavior of Google Maps JavaScript API v3 ( See https://developers.google.com/maps/documentation/javascript/reference#GroundOverlay) Hence, use the corresponding two props provided by `react-google-maps-api`, url and bounds. In some cases, you'll need the GroundOverlay component to reflect the changes of url and bounds. You can leverage the React's key property to remount the component. Typically, just `key={url}` would serve your need. See https://github.com/tomchentw/react-google-maps/issues/655\");\n    var groundOverlay = new google.maps.GroundOverlay(this.props.url, this.props.bounds, _objectSpread$1(_objectSpread$1({}, this.props.options), {}, {\n      map: this.context\n    }));\n    this.registeredEvents = applyUpdatersToPropsAndRegisterEvents({\n      updaterMap: updaterMap$5,\n      eventMap: eventMap$5,\n      prevProps: {},\n      nextProps: this.props,\n      instance: groundOverlay\n    });\n    this.setState(function setGroundOverlay() {\n      return {\n        groundOverlay\n      };\n    }, this.setGroundOverlayCallback);\n  }\n  componentDidUpdate(prevProps) {\n    if (this.state.groundOverlay !== null) {\n      unregisterEvents(this.registeredEvents);\n      this.registeredEvents = applyUpdatersToPropsAndRegisterEvents({\n        updaterMap: updaterMap$5,\n        eventMap: eventMap$5,\n        prevProps,\n        nextProps: this.props,\n        instance: this.state.groundOverlay\n      });\n    }\n  }\n  componentWillUnmount() {\n    if (this.state.groundOverlay) {\n      if (this.props.onUnmount) {\n        this.props.onUnmount(this.state.groundOverlay);\n      }\n      this.state.groundOverlay.setMap(null);\n    }\n  }\n  render() {\n    return null;\n  }\n}\n_defineProperty(GroundOverlay, \"defaultProps\", {\n  onLoad: noop\n});\n_defineProperty(GroundOverlay, \"contextType\", MapContext);\n\nfunction ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }\nfunction _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { _defineProperty(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }\nvar eventMap$4 = {};\nvar updaterMap$4 = {\n  data(instance, data) {\n    instance.setData(data);\n  },\n  map(instance, map) {\n    instance.setMap(map);\n  },\n  options(instance, options) {\n    instance.setOptions(options);\n  }\n};\nfunction HeatmapLayerFunctional(_ref) {\n  var {\n    data,\n    onLoad,\n    onUnmount,\n    options\n  } = _ref;\n  var map = useContext(MapContext);\n  var [instance, setInstance] = useState(null);\n  useEffect(() => {\n    if (!google.maps.visualization) {\n      invariant(!!google.maps.visualization, 'Did you include prop libraries={[\"visualization\"]} in useJsApiScript? %s', google.maps.visualization);\n    }\n  }, []);\n  useEffect(() => {\n    invariant(!!data, 'data property is required in HeatmapLayer %s', data);\n  }, [data]);\n  // Order does matter\n  useEffect(() => {\n    if (instance !== null) {\n      instance.setMap(map);\n    }\n  }, [map]);\n  useEffect(() => {\n    if (options && instance !== null) {\n      instance.setOptions(options);\n    }\n  }, [instance, options]);\n  useEffect(() => {\n    var heatmapLayer = new google.maps.visualization.HeatmapLayer(_objectSpread(_objectSpread({}, options), {}, {\n      data,\n      map\n    }));\n    setInstance(heatmapLayer);\n    if (onLoad) {\n      onLoad(heatmapLayer);\n    }\n    return () => {\n      if (instance !== null) {\n        if (onUnmount) {\n          onUnmount(instance);\n        }\n        instance.setMap(null);\n      }\n    };\n  }, []);\n  return null;\n}\nvar HeatmapLayerF = memo(HeatmapLayerFunctional);\nclass HeatmapLayer extends PureComponent {\n  constructor() {\n    super(...arguments);\n    _defineProperty(this, \"registeredEvents\", []);\n    _defineProperty(this, \"state\", {\n      heatmapLayer: null\n    });\n    _defineProperty(this, \"setHeatmapLayerCallback\", () => {\n      if (this.state.heatmapLayer !== null && this.props.onLoad) {\n        this.props.onLoad(this.state.heatmapLayer);\n      }\n    });\n  }\n  componentDidMount() {\n    invariant(!!google.maps.visualization, 'Did you include prop libraries={[\"visualization\"]} to <LoadScript />? %s', google.maps.visualization);\n    invariant(!!this.props.data, 'data property is required in HeatmapLayer %s', this.props.data);\n    var heatmapLayer = new google.maps.visualization.HeatmapLayer(_objectSpread(_objectSpread({}, this.props.options), {}, {\n      data: this.props.data,\n      map: this.context\n    }));\n    this.registeredEvents = applyUpdatersToPropsAndRegisterEvents({\n      updaterMap: updaterMap$4,\n      eventMap: eventMap$4,\n      prevProps: {},\n      nextProps: this.props,\n      instance: heatmapLayer\n    });\n    this.setState(function setHeatmapLayer() {\n      return {\n        heatmapLayer\n      };\n    }, this.setHeatmapLayerCallback);\n  }\n  componentDidUpdate(prevProps) {\n    unregisterEvents(this.registeredEvents);\n    this.registeredEvents = applyUpdatersToPropsAndRegisterEvents({\n      updaterMap: updaterMap$4,\n      eventMap: eventMap$4,\n      prevProps,\n      nextProps: this.props,\n      instance: this.state.heatmapLayer\n    });\n  }\n  componentWillUnmount() {\n    if (this.state.heatmapLayer !== null) {\n      if (this.props.onUnmount) {\n        this.props.onUnmount(this.state.heatmapLayer);\n      }\n      unregisterEvents(this.registeredEvents);\n      this.state.heatmapLayer.setMap(null);\n    }\n  }\n  render() {\n    return null;\n  }\n}\n_defineProperty(HeatmapLayer, \"contextType\", MapContext);\n\nvar eventMap$3 = {\n  onCloseClick: 'closeclick',\n  onPanoChanged: 'pano_changed',\n  onPositionChanged: 'position_changed',\n  onPovChanged: 'pov_changed',\n  onResize: 'resize',\n  onStatusChanged: 'status_changed',\n  onVisibleChanged: 'visible_changed',\n  onZoomChanged: 'zoom_changed'\n};\nvar updaterMap$3 = {\n  register(instance, provider, options) {\n    instance.registerPanoProvider(provider, options);\n  },\n  links(instance, links) {\n    instance.setLinks(links);\n  },\n  motionTracking(instance, motionTracking) {\n    instance.setMotionTracking(motionTracking);\n  },\n  options(instance, options) {\n    instance.setOptions(options);\n  },\n  pano(instance, pano) {\n    instance.setPano(pano);\n  },\n  position(instance, position) {\n    instance.setPosition(position);\n  },\n  pov(instance, pov) {\n    instance.setPov(pov);\n  },\n  visible(instance, visible) {\n    instance.setVisible(visible);\n  },\n  zoom(instance, zoom) {\n    instance.setZoom(zoom);\n  }\n};\nclass StreetViewPanorama extends PureComponent {\n  constructor() {\n    super(...arguments);\n    _defineProperty(this, \"registeredEvents\", []);\n    _defineProperty(this, \"state\", {\n      streetViewPanorama: null\n    });\n    _defineProperty(this, \"setStreetViewPanoramaCallback\", () => {\n      if (this.state.streetViewPanorama !== null && this.props.onLoad) {\n        this.props.onLoad(this.state.streetViewPanorama);\n      }\n    });\n  }\n  componentDidMount() {\n    var _this$context$getStre, _this$context;\n    var streetViewPanorama = (_this$context$getStre = (_this$context = this.context) === null || _this$context === void 0 ? void 0 : _this$context.getStreetView()) !== null && _this$context$getStre !== void 0 ? _this$context$getStre : null;\n    this.registeredEvents = applyUpdatersToPropsAndRegisterEvents({\n      updaterMap: updaterMap$3,\n      eventMap: eventMap$3,\n      prevProps: {},\n      nextProps: this.props,\n      instance: streetViewPanorama\n    });\n    this.setState(() => {\n      return {\n        streetViewPanorama\n      };\n    }, this.setStreetViewPanoramaCallback);\n  }\n  componentDidUpdate(prevProps) {\n    if (this.state.streetViewPanorama !== null) {\n      unregisterEvents(this.registeredEvents);\n      this.registeredEvents = applyUpdatersToPropsAndRegisterEvents({\n        updaterMap: updaterMap$3,\n        eventMap: eventMap$3,\n        prevProps,\n        nextProps: this.props,\n        instance: this.state.streetViewPanorama\n      });\n    }\n  }\n  componentWillUnmount() {\n    if (this.state.streetViewPanorama !== null) {\n      if (this.props.onUnmount) {\n        this.props.onUnmount(this.state.streetViewPanorama);\n      }\n      unregisterEvents(this.registeredEvents);\n      this.state.streetViewPanorama.setVisible(false);\n    }\n  }\n  render() {\n    return null;\n  }\n}\n_defineProperty(StreetViewPanorama, \"contextType\", MapContext);\n\nclass StreetViewService extends PureComponent {\n  constructor() {\n    super(...arguments);\n    _defineProperty(this, \"state\", {\n      streetViewService: null\n    });\n    _defineProperty(this, \"setStreetViewServiceCallback\", () => {\n      if (this.state.streetViewService !== null && this.props.onLoad) {\n        this.props.onLoad(this.state.streetViewService);\n      }\n    });\n  }\n  componentDidMount() {\n    var streetViewService = new google.maps.StreetViewService();\n    this.setState(function setStreetViewService() {\n      return {\n        streetViewService\n      };\n    }, this.setStreetViewServiceCallback);\n  }\n  componentWillUnmount() {\n    if (this.state.streetViewService !== null && this.props.onUnmount) {\n      this.props.onUnmount(this.state.streetViewService);\n    }\n  }\n  render() {\n    return null;\n  }\n}\n_defineProperty(StreetViewService, \"contextType\", MapContext);\n\nclass DirectionsService extends PureComponent {\n  constructor() {\n    super(...arguments);\n    _defineProperty(this, \"state\", {\n      directionsService: null\n    });\n    _defineProperty(this, \"setDirectionsServiceCallback\", () => {\n      if (this.state.directionsService !== null && this.props.onLoad) {\n        this.props.onLoad(this.state.directionsService);\n      }\n    });\n  }\n  componentDidMount() {\n    invariant(!!this.props.options, 'DirectionsService expected options object as parameter, but got %s', this.props.options);\n    var directionsService = new google.maps.DirectionsService();\n    this.setState(function setDirectionsService() {\n      return {\n        directionsService\n      };\n    }, this.setDirectionsServiceCallback);\n  }\n  componentDidUpdate() {\n    if (this.state.directionsService !== null) {\n      this.state.directionsService.route(this.props.options, this.props.callback);\n    }\n  }\n  componentWillUnmount() {\n    if (this.state.directionsService !== null) {\n      if (this.props.onUnmount) {\n        this.props.onUnmount(this.state.directionsService);\n      }\n    }\n  }\n  render() {\n    return null;\n  }\n}\n\nvar eventMap$2 = {\n  onDirectionsChanged: 'directions_changed'\n};\nvar updaterMap$2 = {\n  directions(instance, directions) {\n    instance.setDirections(directions);\n  },\n  map(instance, map) {\n    instance.setMap(map);\n  },\n  options(instance, options) {\n    instance.setOptions(options);\n  },\n  panel(instance, panel) {\n    instance.setPanel(panel);\n  },\n  routeIndex(instance, routeIndex) {\n    instance.setRouteIndex(routeIndex);\n  }\n};\nclass DirectionsRenderer extends PureComponent {\n  constructor() {\n    super(...arguments);\n    _defineProperty(this, \"registeredEvents\", []);\n    _defineProperty(this, \"state\", {\n      directionsRenderer: null\n    });\n    _defineProperty(this, \"setDirectionsRendererCallback\", () => {\n      if (this.state.directionsRenderer !== null) {\n        this.state.directionsRenderer.setMap(this.context);\n        if (this.props.onLoad) {\n          this.props.onLoad(this.state.directionsRenderer);\n        }\n      }\n    });\n  }\n  componentDidMount() {\n    var directionsRenderer = new google.maps.DirectionsRenderer(this.props.options);\n    this.registeredEvents = applyUpdatersToPropsAndRegisterEvents({\n      updaterMap: updaterMap$2,\n      eventMap: eventMap$2,\n      prevProps: {},\n      nextProps: this.props,\n      instance: directionsRenderer\n    });\n    this.setState(function setDirectionsRenderer() {\n      return {\n        directionsRenderer\n      };\n    }, this.setDirectionsRendererCallback);\n  }\n  componentDidUpdate(prevProps) {\n    if (this.state.directionsRenderer !== null) {\n      unregisterEvents(this.registeredEvents);\n      this.registeredEvents = applyUpdatersToPropsAndRegisterEvents({\n        updaterMap: updaterMap$2,\n        eventMap: eventMap$2,\n        prevProps,\n        nextProps: this.props,\n        instance: this.state.directionsRenderer\n      });\n    }\n  }\n  componentWillUnmount() {\n    if (this.state.directionsRenderer !== null) {\n      if (this.props.onUnmount) {\n        this.props.onUnmount(this.state.directionsRenderer);\n      }\n      unregisterEvents(this.registeredEvents);\n      if (this.state.directionsRenderer) {\n        this.state.directionsRenderer.setMap(null);\n      }\n    }\n  }\n  render() {\n    return null;\n  }\n}\n_defineProperty(DirectionsRenderer, \"contextType\", MapContext);\n\nclass DistanceMatrixService extends PureComponent {\n  constructor() {\n    super(...arguments);\n    _defineProperty(this, \"state\", {\n      distanceMatrixService: null\n    });\n    _defineProperty(this, \"setDistanceMatrixServiceCallback\", () => {\n      if (this.state.distanceMatrixService !== null && this.props.onLoad) {\n        this.props.onLoad(this.state.distanceMatrixService);\n      }\n    });\n  }\n  componentDidMount() {\n    invariant(!!this.props.options, 'DistanceMatrixService expected options object as parameter, but go %s', this.props.options);\n    var distanceMatrixService = new google.maps.DistanceMatrixService();\n    this.setState(function setDistanceMatrixService() {\n      return {\n        distanceMatrixService\n      };\n    }, this.setDistanceMatrixServiceCallback);\n  }\n  componentDidUpdate() {\n    if (this.state.distanceMatrixService !== null) {\n      this.state.distanceMatrixService.getDistanceMatrix(this.props.options, this.props.callback);\n    }\n  }\n  componentWillUnmount() {\n    if (this.state.distanceMatrixService !== null && this.props.onUnmount) {\n      this.props.onUnmount(this.state.distanceMatrixService);\n    }\n  }\n  render() {\n    return null;\n  }\n}\n\nvar eventMap$1 = {\n  onPlacesChanged: 'places_changed'\n};\nvar updaterMap$1 = {\n  bounds(instance, bounds) {\n    instance.setBounds(bounds);\n  }\n};\nclass StandaloneSearchBox extends PureComponent {\n  constructor() {\n    super(...arguments);\n    _defineProperty(this, \"registeredEvents\", []);\n    _defineProperty(this, \"containerElement\", createRef());\n    _defineProperty(this, \"state\", {\n      searchBox: null\n    });\n    _defineProperty(this, \"setSearchBoxCallback\", () => {\n      if (this.state.searchBox !== null && this.props.onLoad) {\n        this.props.onLoad(this.state.searchBox);\n      }\n    });\n  }\n  componentDidMount() {\n    invariant(!!google.maps.places, 'You need to provide libraries={[\"places\"]} prop to <LoadScript /> component %s', google.maps.places);\n    if (this.containerElement !== null && this.containerElement.current !== null) {\n      var input = this.containerElement.current.querySelector('input');\n      if (input !== null) {\n        var searchBox = new google.maps.places.SearchBox(input, this.props.options);\n        this.registeredEvents = applyUpdatersToPropsAndRegisterEvents({\n          updaterMap: updaterMap$1,\n          eventMap: eventMap$1,\n          prevProps: {},\n          nextProps: this.props,\n          instance: searchBox\n        });\n        this.setState(function setSearchBox() {\n          return {\n            searchBox\n          };\n        }, this.setSearchBoxCallback);\n      }\n    }\n  }\n  componentDidUpdate(prevProps) {\n    if (this.state.searchBox !== null) {\n      unregisterEvents(this.registeredEvents);\n      this.registeredEvents = applyUpdatersToPropsAndRegisterEvents({\n        updaterMap: updaterMap$1,\n        eventMap: eventMap$1,\n        prevProps,\n        nextProps: this.props,\n        instance: this.state.searchBox\n      });\n    }\n  }\n  componentWillUnmount() {\n    if (this.state.searchBox !== null) {\n      if (this.props.onUnmount) {\n        this.props.onUnmount(this.state.searchBox);\n      }\n      unregisterEvents(this.registeredEvents);\n    }\n  }\n  render() {\n    return jsx(\"div\", {\n      ref: this.containerElement,\n      children: Children.only(this.props.children)\n    });\n  }\n}\n_defineProperty(StandaloneSearchBox, \"contextType\", MapContext);\n\nvar eventMap = {\n  onPlaceChanged: 'place_changed'\n};\nvar updaterMap = {\n  bounds(instance, bounds) {\n    instance.setBounds(bounds);\n  },\n  restrictions(instance, restrictions) {\n    instance.setComponentRestrictions(restrictions);\n  },\n  fields(instance, fields) {\n    instance.setFields(fields);\n  },\n  options(instance, options) {\n    instance.setOptions(options);\n  },\n  types(instance, types) {\n    instance.setTypes(types);\n  }\n};\nclass Autocomplete extends PureComponent {\n  constructor() {\n    super(...arguments);\n    _defineProperty(this, \"registeredEvents\", []);\n    _defineProperty(this, \"containerElement\", createRef());\n    _defineProperty(this, \"state\", {\n      autocomplete: null\n    });\n    _defineProperty(this, \"setAutocompleteCallback\", () => {\n      if (this.state.autocomplete !== null && this.props.onLoad) {\n        this.props.onLoad(this.state.autocomplete);\n      }\n    });\n  }\n  componentDidMount() {\n    var _this$containerElemen;\n    invariant(!!google.maps.places, 'You need to provide libraries={[\"places\"]} prop to <LoadScript /> component %s', google.maps.places);\n    // TODO: why current could be equal null?\n    var input = (_this$containerElemen = this.containerElement.current) === null || _this$containerElemen === void 0 ? void 0 : _this$containerElemen.querySelector('input');\n    if (input) {\n      var autocomplete = new google.maps.places.Autocomplete(input, this.props.options);\n      this.registeredEvents = applyUpdatersToPropsAndRegisterEvents({\n        updaterMap,\n        eventMap,\n        prevProps: {},\n        nextProps: this.props,\n        instance: autocomplete\n      });\n      this.setState(() => {\n        return {\n          autocomplete\n        };\n      }, this.setAutocompleteCallback);\n    }\n  }\n  componentDidUpdate(prevProps) {\n    unregisterEvents(this.registeredEvents);\n    this.registeredEvents = applyUpdatersToPropsAndRegisterEvents({\n      updaterMap,\n      eventMap,\n      prevProps,\n      nextProps: this.props,\n      instance: this.state.autocomplete\n    });\n  }\n  componentWillUnmount() {\n    if (this.state.autocomplete !== null) {\n      unregisterEvents(this.registeredEvents);\n    }\n  }\n  render() {\n    return jsx(\"div\", {\n      ref: this.containerElement,\n      className: this.props.className,\n      children: Children.only(this.props.children)\n    });\n  }\n}\n_defineProperty(Autocomplete, \"defaultProps\", {\n  className: ''\n});\n_defineProperty(Autocomplete, \"contextType\", MapContext);\n\nexport { Autocomplete, BicyclingLayer, BicyclingLayerF, Circle, CircleF, Data, DataF, DirectionsRenderer, DirectionsService, DistanceMatrixService, DrawingManager, DrawingManagerF, FLOAT_PANE, GoogleMap, index_esm as GoogleMapsMarkerClusterer, GoogleMarkerClusterer$1 as GoogleMarkerClusterer, GroundOverlay, GroundOverlayF, HeatmapLayer, HeatmapLayerF, InfoBoxComponent as InfoBox, InfoBoxF, InfoWindow, InfoWindowF, KmlLayer, LoadScript, LoadScriptNext$1 as LoadScriptNext, MAP_PANE, MARKER_LAYER, MapContext, Marker, ClustererComponent as MarkerClusterer, MarkerClustererF, MarkerF, OVERLAY_LAYER, OVERLAY_MOUSE_TARGET, OverlayView, OverlayViewF, Polygon, PolygonF, Polyline, PolylineF, Rectangle, RectangleF, StandaloneSearchBox, StreetViewPanorama, StreetViewService, TrafficLayer, TrafficLayerF, TransitLayer, TransitLayerF, useGoogleMap, useJsApiLoader, useLoadScript };\n//# sourceMappingURL=esm.js.map\n"], "names": [], "sourceRoot": "", "ignoreList": [0]}