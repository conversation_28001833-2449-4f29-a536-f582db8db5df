{"version": 3, "file": "static/chunks/pages/adminsettings/hazardtypes/hazardTypeTable-a0e1397fea8598af.js", "mappings": "2OA6IA,MAlIyBA,IACrB,GAAM,CAACC,EAAWC,EAAe,CAAGC,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAiIjCC,EAjIoC,EACzC,EAAGC,EAAW,CAAGF,CAAAA,EAAAA,EAAAA,CAgIGC,EAAC,KAhIJD,CAAQA,EAAC,GAC1B,CAACG,EAAWC,EAAa,CAAGJ,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAC,GACrC,CAACK,EAASC,EAAW,CAAGN,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAC,IACjC,CAACO,EAAaC,EAAS,CAAGR,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,EAAC,GACnC,CAACS,EAAkBC,EAAoB,CAAGV,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAC,CAAC,GACpD,GAAEW,CAAC,CAAE,CAAGC,CAAAA,EAAAA,EAAAA,EAAAA,CAAcA,CAAC,UAGvBC,EAAU,CACZ,CACIC,KAAMH,EAAE,wCACRI,SAAU,OACd,EACA,CACID,KAAMH,EAAE,iCACRI,SAAU,OACVC,KAAM,GAAYC,EAAEC,IAAI,EAE5B,CACIJ,KAAMH,EAAE,eACRI,SAAU,cACVC,KAAM,GAAYC,EAAEE,WAAW,CAACC,OAAO,CAAC,WAAY,GACxD,EACA,CACIN,KAAMH,EAAE,UACRI,SAAU,GACVC,KAAM,GACF,WAACK,MAAAA,WACG,UAACC,IAAIA,CAACC,KAAK,6BAA6BC,GAAI,OAAvCF,6BAAiF,OAANL,EAAEQ,GAAG,WAEjF,UAACC,IAAAA,CAAEC,UAAU,uBAEV,OAEP,UAACC,IAAAA,CAAEC,QAAS,IAAMC,EAAWb,YACzB,UAACS,IAAAA,CAAEC,UAAU,8BAI7B,EACH,CAEDI,CAAAA,EAAAA,EAAAA,SAAAA,CAASA,CAAC,KACNC,GACJ,EAAG,EAAE,EAEL,IAAMC,EAAmB,CACrBC,KAAM,CAAEC,MAAO,KAAM,EACrBC,MAAO/B,EACPgC,KAAM,EACNC,MAAO,CAAC,CACZ,EAEMN,EAAqB,UACvB9B,GAAW,GACX,IAAMqC,EAAW,MAAMC,EAAAA,CAAUA,CAACC,GAAG,CAAC,cAAeR,GACjDM,GAAYA,EAASG,IAAI,EAAIH,EAASG,IAAI,CAACC,MAAM,CAAG,GAAG,CACvD5C,EAAewC,EAASG,IAAI,EAC5BtC,EAAamC,EAASK,UAAU,EAChC1C,GAAW,GAEnB,EAQM2C,EAAsB,MAAOC,EAAiBT,KAChDJ,EAAiBG,KAAK,CAAGU,EACzBb,EAAiBI,IAAI,CAAGA,EACxBnC,GAAW,GACX,IAAMqC,EAAW,MAAMC,EAAAA,CAAUA,CAACC,GAAG,CAAC,cAAeR,GACjDM,GAAYA,EAASG,IAAI,EAAIH,EAASG,IAAI,CAACC,MAAM,CAAG,GAAG,CACvD5C,EAAewC,EAASG,IAAI,EAC5BpC,EAAWwC,GACX5C,GAAW,GAEnB,EAEM4B,EAAa,MAAOiB,IACtBrC,EAAoBqC,EAAItB,GAAG,EAC3BjB,GAAS,EACb,EAEMwC,EAAe,UACjB,GAAI,CACA,MAAMR,EAAAA,CAAUA,CAACS,MAAM,CAAC,eAAgC,OAAjBxC,IACvCuB,IACAxB,GAAS,GACT0C,EAAAA,EAAKA,CAACC,OAAO,CAACxC,EAAE,gEACpB,CAAE,MAAOyC,EAAO,CACZF,EAAAA,EAAKA,CAACE,KAAK,CAACzC,EAAE,0DAClB,CACJ,EAEM0C,EAAY,IAAM7C,GAAS,GAEjC,MACI,WAACa,MAAAA,WACG,WAACiC,EAAAA,CAAKA,CAAAA,CAACC,KAAMhD,EAAaiD,OAAQH,YAC9B,UAACC,EAAAA,CAAKA,CAACG,MAAM,EAACC,WAAW,aACrB,UAACJ,EAAAA,CAAKA,CAACK,KAAK,WAAEhD,EAAE,iDAEpB,UAAC2C,EAAAA,CAAKA,CAACM,IAAI,WAAEjD,EAAE,mEACf,WAAC2C,EAAAA,CAAKA,CAACO,MAAM,YACT,UAACC,EAAAA,CAAMA,CAAAA,CAACC,QAAQ,YAAYlC,QAASwB,WAChC1C,EAAE,YAEP,UAACmD,EAAAA,CAAMA,CAAAA,CAACC,QAAQ,UAAUlC,QAASmB,WAC9BrC,EAAE,eAKf,UAACqD,EAAAA,CAAQA,CAAAA,CACLnD,QAASA,EACT6B,KAAM5C,EACNK,UAAWA,EACX8D,WAAW,EACXpB,oBAAqBA,EACrBqB,iBA3Da,CA2DKA,GA1D1BjC,EAAiBG,KAAK,CAAG/B,EACzB4B,EAAiBI,IAAI,CAAGA,EACxBL,GACJ,MA2DJ,mBC1IA,4CACA,6CACA,WACA,OAAe,EAAQ,KAAkE,CACzF,EACA,SAFsB,oGCiCtB,SAASgC,EAASG,CAAoB,EACpC,GAAM,GAAExD,CAAC,CAAE,CAAGC,CAAAA,EAAAA,EAAAA,EAAAA,CAAcA,CAAC,UACvBwD,EAA6B,CACjCC,gBAAiB1D,EAAE,cACnB,EACI,SACJE,CAAO,MACP6B,CAAI,WACJvC,CAAS,CACTmE,uBAAqB,WACrBC,CAAS,oBACTC,CAAkB,qBAClB3B,CAAmB,kBACnBqB,CAAgB,aAChBO,CAAW,oBACXC,CAAkB,gBAClBC,CAAc,SACdC,CAAO,WACPX,CAAS,sBACTY,CAAoB,mBACpBC,CAAiB,YACjBC,CAAU,QACVC,CAAM,kBACNC,CAAgB,CAChBC,cAAY,CAEZ,CADA,EACGC,EACJ,CAAGhB,EAGEiB,EAAiB,4BACrBhB,EACAiB,gBAAiB1E,EAAE,IAP0C,MAQ7D2E,UAAU,UACVzE,EACA6B,KAAMA,GAAQ,EAAE,CAChB6C,OAAO,EACPC,2BAA4BlB,EAC5BmB,UAAWlB,EACXmB,gBAAiBd,qBACjBJ,EACAmB,YAAY,EACZC,iBAAkB3B,EAClB4B,kBAAmBnB,GAA0C,GAC7DoB,eADwCpB,cACVD,GAA4B,CAAC,GAAI,GAAI,GAAI,CAA3BA,EAA+B,GAAG,CAC9EsB,oBAAqB5F,EACrB6F,oBAAqBnD,EACrBoD,aAAc/B,iBACdS,uBACAE,oBACAC,EACAoB,kBAAmB,UAACC,EAAAA,CAAYA,CAAAA,CAAAA,GAChCC,SAAU,UAAC1E,IAAAA,CAAEC,UAAU,6CACvBoD,SACAC,eACAE,EACAD,mBACAtD,UAAW,WACb,EACA,MACE,UAAC0E,EAAAA,EAASA,CAAAA,CAAE,GAAGjB,CAAc,EAEjC,CAEApB,EAASsC,YAAY,CAAG,CACtBb,WAAW,EACXE,YAAY,EACZxF,UAAW,KACX8D,WAAW,EACXY,qBAAsB,KACtBC,mBAAmB,EACnBC,YAAY,EACZE,iBAAkB,EACpB,EAEA,MAAejB,QAAQA,EAAC", "sources": ["webpack://_N_E/./pages/adminsettings/hazardtypes/hazardTypeTable.tsx", "webpack://_N_E/?85ee", "webpack://_N_E/./components/common/RKITable.tsx"], "sourcesContent": ["//Import Library\r\nimport Link from \"next/link\";\r\nimport { useState, useEffect } from \"react\";\r\nimport { <PERSON><PERSON>, Button } from \"react-bootstrap\";\r\nimport toast from 'react-hot-toast';\r\n\r\n//Import services/components\r\nimport RKITable from \"../../../components/common/RKITable\";\r\nimport apiService from \"../../../services/apiService\";\r\nimport { useTranslation } from 'next-i18next';\r\n\r\nconst HazardTypeTable = (_props: any) => {\r\n    const [tabledata, setDataToTable] = useState([]);\r\n    const [, setLoading] = useState(false);\r\n    const [totalRows, setTotalRows] = useState(0);\r\n    const [perPage, setPerPage] = useState(10);\r\n    const [isModalShow, setModal] = useState(false);\r\n    const [selectHazardType, setSelectHazardType] = useState({});\r\n    const { t } = useTranslation('common');\r\n\r\n\r\n    const columns = [\r\n        {\r\n            name: t(\"adminsetting.hazardtypes.HarzardType\"),\r\n            selector: \"title\",\r\n        },\r\n        {\r\n            name: t(\"adminsetting.hazardtypes.Code\"),\r\n            selector: \"code\",\r\n            cell: (d: any) => d.code,\r\n        },\r\n        {\r\n            name: t(\"Description\"),\r\n            selector: \"description\",\r\n            cell: (d: any) => d.description.replace(/<[^>]+>/g, \"\"),\r\n        },\r\n        {\r\n            name: t(\"action\"),\r\n            selector: \"\",\r\n            cell: (d: any) => (\r\n                <div>\r\n                    <Link href=\"/adminsettings/[...routes]\" as={`/adminsettings/edit_hazard_types/${d._id}`}>\r\n\r\n                        <i className=\"icon fas fa-edit\" />\r\n\r\n                    </Link>\r\n                    &nbsp;\r\n                    <a onClick={() => userAction(d)}>\r\n                        <i className=\"icon fas fa-trash-alt\" />\r\n                    </a>\r\n                </div>\r\n            ),\r\n        },\r\n    ];\r\n\r\n    useEffect(() => {\r\n        getHazardsTypeData();\r\n    }, []);\r\n\r\n    const hazardTypeParams = {\r\n        sort: { title: \"asc\" },\r\n        limit: perPage,\r\n        page: 1,\r\n        query: {},\r\n    };\r\n\r\n    const getHazardsTypeData = async () => {\r\n        setLoading(true);\r\n        const response = await apiService.get(\"/hazardtype\", hazardTypeParams);\r\n        if (response && response.data && response.data.length > 0) {\r\n            setDataToTable(response.data);\r\n            setTotalRows(response.totalCount);\r\n            setLoading(false);\r\n        }\r\n    };\r\n\r\n    const handlePageChange = (page: any) => {\r\n        hazardTypeParams.limit = perPage;\r\n        hazardTypeParams.page = page;\r\n        getHazardsTypeData();\r\n    };\r\n\r\n    const handlePerRowsChange = async (newPerPage: any, page: any) => {\r\n        hazardTypeParams.limit = newPerPage;\r\n        hazardTypeParams.page = page;\r\n        setLoading(true);\r\n        const response = await apiService.get(\"/hazardtype\", hazardTypeParams);\r\n        if (response && response.data && response.data.length > 0) {\r\n            setDataToTable(response.data);\r\n            setPerPage(newPerPage);\r\n            setLoading(false);\r\n        }\r\n    };\r\n\r\n    const userAction = async (row: any) => {\r\n        setSelectHazardType(row._id);\r\n        setModal(true);\r\n    };\r\n\r\n    const modalConfirm = async () => {\r\n        try {\r\n            await apiService.remove(`/hazardtype/${selectHazardType}`);\r\n            getHazardsTypeData();\r\n            setModal(false);\r\n            toast.success(t(\"adminsetting.hazardtypes.Table.hazardTypeDeletedSuccessfully\"));\r\n        } catch (error) {\r\n            toast.error(t(\"adminsetting.hazardtypes.Table.errorDeletingHazardType\"));\r\n        }\r\n    };\r\n\r\n    const modalHide = () => setModal(false);\r\n\r\n    return (\r\n        <div>\r\n            <Modal show={isModalShow} onHide={modalHide}>\r\n                <Modal.Header closeButton>\r\n                    <Modal.Title>{t(\"adminsetting.hazardtypes.Deletehazardtype\")}</Modal.Title>\r\n                </Modal.Header>\r\n                <Modal.Body>{t(\"adminsetting.hazardtypes.Areyousurewanttodeletethishazardtype\")}</Modal.Body>\r\n                <Modal.Footer>\r\n                    <Button variant=\"secondary\" onClick={modalHide}>\r\n                        {t(\"cancel\")}\r\n                    </Button>\r\n                    <Button variant=\"primary\" onClick={modalConfirm}>\r\n                        {t(\"yes\")}\r\n                    </Button>\r\n                </Modal.Footer>\r\n            </Modal>\r\n\r\n            <RKITable\r\n                columns={columns}\r\n                data={tabledata}\r\n                totalRows={totalRows}\r\n                pagServer={true}\r\n                handlePerRowsChange={handlePerRowsChange}\r\n                handlePageChange={handlePageChange}\r\n            />\r\n        </div>\r\n    );\r\n};\r\n\r\nexport default HazardTypeTable;\r\n", "\n    (window.__NEXT_P = window.__NEXT_P || []).push([\n      \"/adminsettings/hazardtypes/hazardTypeTable\",\n      function () {\n        return require(\"private-next-pages/adminsettings/hazardtypes/hazardTypeTable.tsx\");\n      }\n    ]);\n    if(module.hot) {\n      module.hot.dispose(function () {\n        window.__NEXT_P.push([\"/adminsettings/hazardtypes/hazardTypeTable\"])\n      });\n    }\n  ", "//Import Library\r\nimport React from 'react'\r\nimport DataTable  from 'react-data-table-component';\r\n\r\n//Import services/components\r\nimport { useTranslation } from 'next-i18next';\r\nimport CustomLoader from './CustomLoader';\r\n\r\ninterface RKITableProps {\r\n  columns: any[];\r\n  data: any[];\r\n  totalRows?: number;\r\n  resetPaginationToggle?: boolean;\r\n  subheader?: boolean;\r\n  subHeaderComponent?: React.ReactNode;\r\n  handlePerRowsChange?: (newPerPage: number, page: number) => void;\r\n  handlePageChange?: (page: number) => void;\r\n  rowsPerPage?: number[];\r\n  defaultRowsPerPage?: number;\r\n  selectableRows?: boolean;\r\n  loading?: boolean;\r\n  pagServer?: boolean;\r\n  onSelectedRowsChange?: (selectedRows: any) => void;\r\n  clearSelectedRows?: boolean;\r\n  sortServer?: boolean;\r\n  onSort?: (column: any, sortDirection: string) => void;\r\n  persistTableHead?: boolean;\r\n  sortFunction?: (rows: any[], field: any, direction: 'asc' | 'desc') => any[];\r\n  noHeader?: boolean;\r\n  dense?: boolean;\r\n  pagination?: boolean;\r\n  paginationServer?: boolean;\r\n  paginationTotalRows?: number;\r\n  subHeaderAlign?: string;\r\n  [key: string]: any;\r\n}\r\n\r\nfunction RKITable(props: RKITableProps) {\r\n  const { t } = useTranslation('common');\r\n  const paginationComponentOptions = {\r\n    rowsPerPageText: t('Rowsperpage'),\r\n    };\r\n  const {\r\n    columns,\r\n    data,\r\n    totalRows,\r\n    resetPaginationToggle,\r\n    subheader,\r\n    subHeaderComponent,\r\n    handlePerRowsChange,\r\n    handlePageChange,\r\n    rowsPerPage,\r\n    defaultRowsPerPage,\r\n    selectableRows,\r\n    loading,\r\n    pagServer,\r\n    onSelectedRowsChange,\r\n    clearSelectedRows,\r\n    sortServer,\r\n    onSort,\r\n    persistTableHead,\r\n    sortFunction,\r\n    // Filter out any unknown props that might cause DOM warnings\r\n    ...otherProps\r\n  } = props;\r\n\r\n  // Only pass known DataTable props to avoid DOM warnings\r\n  const dataTableProps = {\r\n    paginationComponentOptions,\r\n    noDataComponent: t(\"NoData\"),\r\n    noHeader: true,\r\n    columns,\r\n    data: data || [],\r\n    dense: true,\r\n    paginationResetDefaultPage: resetPaginationToggle,\r\n    subHeader: subheader,\r\n    progressPending: loading,\r\n    subHeaderComponent,\r\n    pagination: true,\r\n    paginationServer: pagServer,\r\n    paginationPerPage: defaultRowsPerPage ? defaultRowsPerPage : 10,\r\n    paginationRowsPerPageOptions: rowsPerPage ? rowsPerPage : [10, 15, 20, 25, 30],\r\n    paginationTotalRows: totalRows,\r\n    onChangeRowsPerPage: handlePerRowsChange,\r\n    onChangePage: handlePageChange,\r\n    selectableRows,\r\n    onSelectedRowsChange,\r\n    clearSelectedRows,\r\n    progressComponent: <CustomLoader/>,\r\n    sortIcon: <i className=\"sort-icon fas fa-exchange-alt\" />,\r\n    sortServer,\r\n    onSort,\r\n    sortFunction,\r\n    persistTableHead,\r\n    className: \"rki-table\"\r\n  };\r\n  return (\r\n    <DataTable {...dataTableProps} />\r\n  )\r\n};\r\n\r\nRKITable.defaultProps = {\r\n  subHeader: false,\r\n  pagination: true,\r\n  totalRows: null,\r\n  pagServer: true,\r\n  onSelectedRowsChange: null,\r\n  clearSelectedRows: false,\r\n  sortServer: false,\r\n  persistTableHead: false\r\n}\r\n\r\nexport default RKITable;\r\n\r\n"], "names": ["_props", "tabledata", "setDataToTable", "useState", "HazardTypeTable", "setLoading", "totalRows", "setTotalRows", "perPage", "setPerPage", "isModalShow", "setModal", "selectHazardType", "setSelectHazardType", "t", "useTranslation", "columns", "name", "selector", "cell", "d", "code", "description", "replace", "div", "Link", "href", "as", "_id", "i", "className", "a", "onClick", "userAction", "useEffect", "getHazardsTypeData", "hazardTypeParams", "sort", "title", "limit", "page", "query", "response", "apiService", "get", "data", "length", "totalCount", "handlePerRowsChange", "newPerPage", "row", "modalConfirm", "remove", "toast", "success", "error", "modalHide", "Modal", "show", "onHide", "Header", "closeButton", "Title", "Body", "Footer", "<PERSON><PERSON>", "variant", "RKITable", "pagServer", "handlePageChange", "props", "paginationComponentOptions", "rowsPerPageText", "resetPaginationToggle", "subheader", "subHeaderComponent", "rowsPerPage", "defaultRowsPerPage", "selectableRows", "loading", "onSelectedRowsChange", "clearSelectedRows", "sortServer", "onSort", "persistTableHead", "sortFunction", "otherProps", "dataTableProps", "noDataComponent", "<PERSON><PERSON><PERSON><PERSON>", "dense", "paginationResetDefaultPage", "subHeader", "progressPending", "pagination", "paginationServer", "paginationPerPage", "paginationRowsPerPageOptions", "paginationTotalRows", "onChangeRowsPerPage", "onChangePage", "progressComponent", "CustomLoader", "sortIcon", "DataTable", "defaultProps"], "sourceRoot": "", "ignoreList": []}