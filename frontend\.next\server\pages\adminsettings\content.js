"use strict";(()=>{var e={};e.id=4952,e.ids=[636,3220,4952],e.modules={123:e=>{e.exports=require("dom-helpers/removeEventListener")},1332:e=>{e.exports=require("react-custom-scrollbars-2")},1428:e=>{e.exports=import("axios")},1680:e=>{e.exports=require("@restart/ui/utils")},1919:e=>{e.exports=require("react-transition-group/Transition")},3892:e=>{e.exports=require("classnames")},4048:e=>{e.exports=require("react-truncate")},6009:e=>{e.exports=require("dom-helpers/ownerDocument")},6952:e=>{e.exports=require("@restart/ui/Modal")},7374:e=>{e.exports=require("@restart/ui/Dropdown")},8732:e=>{e.exports=require("react/jsx-runtime")},9653:e=>{e.exports=require("@restart/ui/Overlay")},11242:e=>{e.exports=require("redux-persist/integration/react")},11688:e=>{e.exports=require("@restart/hooks/useWillUnmount")},12388:e=>{e.exports=require("dom-helpers/addEventListener")},13364:e=>{e.exports=require("redux-saga/effects")},14062:e=>{e.exports=import("react-redux")},14078:e=>{e.exports=import("swr")},14332:e=>{e.exports=require("uncontrollable")},16116:e=>{e.exports=require("invariant")},18622:e=>{e.exports=require("yup")},19121:e=>{e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},22326:e=>{e.exports=require("react-dom")},22541:e=>{e.exports=require("redux-persist/lib/storage")},25303:e=>{e.exports=require("@restart/ui/NavItem")},26324:e=>{e.exports=require("warning")},27053:(e,t,r)=>{r.d(t,{A:()=>a});var s=r(8732);function a(e){return(0,s.jsx)("h2",{className:"page-heading",children:e.title})}},27825:e=>{e.exports=require("lodash")},27910:e=>{e.exports=require("stream")},28217:e=>{e.exports=require("@restart/ui/DropdownItem")},29021:e=>{e.exports=require("fs")},29294:e=>{e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},29628:e=>{e.exports=require("dom-helpers/addClass")},29825:e=>{e.exports=require("prop-types")},29841:e=>{e.exports=require("dom-helpers/hasClass")},30362:e=>{e.exports=require("@restart/ui/DropdownToggle")},33873:e=>{e.exports=require("path")},35576:e=>{e.exports=require("next-i18next/serverSideTranslations")},36653:e=>{e.exports=require("nprogress")},36955:e=>{e.exports=require("@restart/hooks/useBreakpoint")},37766:e=>{e.exports=require("@restart/hooks/useMergedRefs")},38609:e=>{e.exports=require("react-data-table-component")},39756:e=>{e.exports=import("redux")},40051:e=>{e.exports=require("dom-helpers/removeClass")},40361:e=>{e.exports=require("next/dist/compiled/next-server/pages.runtime.prod.js")},42893:e=>{e.exports=import("react-hot-toast")},43294:e=>{e.exports=require("formik")},50009:e=>{e.exports=require("@restart/ui/DropdownMenu")},54094:(e,t,r)=>{r.r(t),r.d(t,{default:()=>p});var s=r(8732),a=r(7082),i=r(83551),n=r(49481),o=r(98132),l=r(84517),d=r(89555),u=r(88751);let c=[{_id:"operation",title:"Operations"},{_id:"institution",title:"Organisations"},{_id:"event",title:"Events"},{_id:"project",title:"Projects"},{_id:"updates",title:"Updates"},{_id:"vspace",title:"Virtual Spaces"}],p=({filterText:e,onFilter:t,onFilterTypeChange:r,onClear:p,filterType:m})=>{let{t:x}=(0,u.useTranslation)("common");return(0,s.jsx)(a.A,{fluid:!0,className:"p-0",children:(0,s.jsxs)(i.A,{children:[(0,s.jsx)(n.A,{xs:6,md:4,className:"ps-0 align-self-end",children:(0,s.jsx)(o.A,{children:(0,s.jsx)(l.A,{type:"text",className:"searchInput",placeholder:x("adminsetting.content.table.Search"),"aria-label":"Search",value:e,onChange:t})})}),(0,s.jsx)(n.A,{xs:6,md:4,children:(0,s.jsxs)(o.A,{as:i.A,children:[(0,s.jsx)(d.A,{column:!0,sm:3,lg:2,className:"me-2",children:x("adminsetting.content.table.Type")}),(0,s.jsx)(n.A,{className:"ps-md-0",children:(0,s.jsx)(l.A,{as:"select","aria-label":"Type",onChange:e=>r(e),value:m,children:c.map((e,t)=>(0,s.jsx)("option",{value:e._id,children:e.title},t))})})]})})]})})}},56084:(e,t,r)=>{r.d(t,{A:()=>d});var s=r(8732);r(82015);var a=r(38609),i=r.n(a),n=r(88751),o=r(30370);function l(e){let{t}=(0,n.useTranslation)("common"),r={rowsPerPageText:t("Rowsperpage")},{columns:a,data:l,totalRows:d,resetPaginationToggle:u,subheader:c,subHeaderComponent:p,handlePerRowsChange:m,handlePageChange:x,rowsPerPage:g,defaultRowsPerPage:h,selectableRows:_,loading:f,pagServer:b,onSelectedRowsChange:q,clearSelectedRows:y,sortServer:v,onSort:A,persistTableHead:P,sortFunction:S,...j}=e,w={paginationComponentOptions:r,noDataComponent:t("NoData"),noHeader:!0,columns:a,data:l||[],dense:!0,paginationResetDefaultPage:u,subHeader:c,progressPending:f,subHeaderComponent:p,pagination:!0,paginationServer:b,paginationPerPage:h||10,paginationRowsPerPageOptions:g||[10,15,20,25,30],paginationTotalRows:d,onChangeRowsPerPage:m,onChangePage:x,selectableRows:_,onSelectedRowsChange:q,clearSelectedRows:y,progressComponent:(0,s.jsx)(o.A,{}),sortIcon:(0,s.jsx)("i",{className:"sort-icon fas fa-exchange-alt"}),sortServer:v,onSort:A,sortFunction:S,persistTableHead:P,className:"rki-table"};return(0,s.jsx)(i(),{...w})}l.defaultProps={subHeader:!1,pagination:!0,totalRows:null,pagServer:!0,onSelectedRowsChange:null,clearSelectedRows:!1,sortServer:!1,persistTableHead:!1};let d=l},57664:e=>{e.exports=require("dom-helpers/scrollbarSize")},58620:(e,t,r)=>{r.a(e,async(e,s)=>{try{r.r(t),r.d(t,{config:()=>h,default:()=>p,getServerSideProps:()=>g,getStaticPaths:()=>x,getStaticProps:()=>m,reportWebVitals:()=>_,routeModule:()=>A,unstable_getServerProps:()=>y,unstable_getServerSideProps:()=>v,unstable_getStaticParams:()=>q,unstable_getStaticPaths:()=>b,unstable_getStaticProps:()=>f});var a=r(63885),i=r(80237),n=r(81413),o=r(9616),l=r.n(o),d=r(72386),u=r(62085),c=e([d,u]);[d,u]=c.then?(await c)():c;let p=(0,n.M)(u,"default"),m=(0,n.M)(u,"getStaticProps"),x=(0,n.M)(u,"getStaticPaths"),g=(0,n.M)(u,"getServerSideProps"),h=(0,n.M)(u,"config"),_=(0,n.M)(u,"reportWebVitals"),f=(0,n.M)(u,"unstable_getStaticProps"),b=(0,n.M)(u,"unstable_getStaticPaths"),q=(0,n.M)(u,"unstable_getStaticParams"),y=(0,n.M)(u,"unstable_getServerProps"),v=(0,n.M)(u,"unstable_getServerSideProps"),A=new a.PagesRouteModule({definition:{kind:i.A.PAGES,page:"/adminsettings/content",pathname:"/adminsettings/content",bundlePath:"",filename:""},components:{App:d.default,Document:l()},userland:u});s()}catch(e){s(e)}})},58928:e=>{e.exports=require("dom-helpers/canUseDOM")},59717:e=>{e.exports=require("@restart/ui/DropdownContext")},60560:e=>{e.exports=require("dom-helpers/contains")},62085:(e,t,r)=>{r.a(e,async(e,s)=>{try{r.r(t),r.d(t,{default:()=>k,getServerSideProps:()=>j});var a=r(8732),i=r(19918),n=r.n(i),o=r(27825),l=r.n(o),d=r(82015),u=r.n(d),c=r(74716),p=r.n(c),m=r(7082),x=r(83551),g=r(49481),h=r(12403),_=r(91353),f=r(42893),b=r(56084),q=r(63487),y=r(54094),v=r(27053),A=r(88751),P=r(35576),S=e([f,q]);[f,q]=S.then?(await S)():S;let w=(e,t)=>{switch(e){case"operation":t.select="-status -start_date -images -timeline -country -world_region -region -hazard -description -end_date -syndrome -hazard_type -partners -images -document -doc_src -images_src";break;case"institution":t.select="-partners -primary_focal_point -email -status -images -use_default_header -header -twitter -dial_code -telephone -department -unit -contact_name -description -type -networks -expertise -hazard_types -hazards -address -focal_points -website  -document -doc_src -images_src ";break;case"event":t.select="-images -more_info -date -risk_assessment -rki_monitored -officially_validated -laboratory_confirmed -status -syndrome -hazard -hazard_type -country_regions -world_region -country -operation -description -document -doc_src -images_src";break;case"project":t.select="-vspace_visibility -vspace -institution_invites -partner_institutions -area_of_work -region -country -end_date -start_date -status -funded_by -description -website";break;case"updates":t.select=" -region -country -end_date -start_date -status -category -contact_details -reply -show_as_announcement -technical_guidance -use_in_media_gallery -field_report -media -images -document -doc_src -images_src -location -link -description  ";break;case"vspace":t.select="-vspace_email_invite -file_category -subscribers -images -members -visibility -topic -end_date -start_date -description -images -document -doc_src -images_src -nonMembers -private_user_invite";break;default:t.select="-status -start_date -images -timeline -country -world_region -region -hazard -description -end_date -syndrome -hazard_type -partners"}return t};async function j({locale:e}){return{props:{...await (0,P.serverSideTranslations)(e,["common"])}}}let k=e=>{let[t,r]=(0,d.useState)([]),{t:s}=(0,A.useTranslation)("common"),[i,o]=(0,d.useState)("operation"),[c,P]=u().useState(!1),[S,j]=u().useState(""),[k,M]=(0,d.useState)(0),[C,E]=(0,d.useState)(25),[T,R]=(0,d.useState)(!1),[N,D]=(0,d.useState)(!1),[$,G]=(0,d.useState)(null),[I,O]=(0,d.useState)(1),[z,U]=(0,d.useState)(null),H={limit:C,sort:{created_at:"desc"}},L=[{name:s("adminsetting.content.table.Title"),selector:e=>e.title,cell:e=>{var t;return e.type?(0,a.jsx)(n(),{href:`/${e.type}/[...routes]`,as:`/${e.type}/show/${e[t=e,`parent_${t.type}`]}/${i}/${e._id}`,children:e.title}):(0,a.jsx)(n(),{href:`/${i}/[...routes]`,as:`/${i}/show/${e._id}`,children:e.title})},sortable:!0},{name:s("adminsetting.content.table.Author"),selector:e=>e.user?.username||"",cell:e=>e.user?e.user.username:"",sortable:!0},{name:s("adminsetting.content.table.Created"),selector:e=>e.created_at,cell:e=>p()(e.created_at).format("M/D/Y"),sortable:!0},{name:s("adminsetting.content.table.Updated"),selector:e=>e.updated_at,cell:e=>p()(e.updated_at).format("M/D/Y"),sortable:!0},{name:s("adminsetting.content.table.Action"),selector:e=>e._id,sortable:!1,cell:e=>(0,a.jsx)(a.Fragment,{children:(z?.roles?.includes("GENERAL_USER")||z?.roles?.includes("PLATFORM_ADMIN"))&&z?._id==e?.user?._id?(0,a.jsxs)("div",{children:[(0,a.jsx)(n(),{href:`/${i}/[...routes]`,as:`/${i}/edit/${e._id}`,children:(0,a.jsx)("i",{className:"icon fas fa-edit"})}),"\xa0",(0,a.jsx)(n(),{href:"#",onClick:t=>B(e,t),children:(0,a.jsx)("i",{className:"icon fas fa-trash-alt"})})]}):z?.roles?.includes("GENERAL_USER")||z?.roles?.includes("PLATFORM_ADMIN")?"":(0,a.jsxs)("div",{children:[(0,a.jsx)(n(),{href:`/${i}/[...routes]`,as:`/${i}/edit/${e._id}`,children:(0,a.jsx)("i",{className:"icon fas fa-edit"})}),"\xa0",(0,a.jsx)(n(),{href:"#",onClick:t=>B(e,t),children:(0,a.jsx)("i",{className:"icon fas fa-trash-alt"})})]})})}],F=async e=>{R(!0),e=w(i,e);let t=await q.A.post("/users/getLoggedUser",{});t&&t.username&&U(t);let s=await q.A.get(`/${i}`,e);s&&s.data&&Array.isArray(s.data)&&s.data.length>0?(r(s.data),M(s.totalCount||0)):r([]),R(!1)},B=async(e,t)=>{t.preventDefault(),G({id:e._id,type:i}),D(!0)},W=async(e,t)=>{R(!0),H.limit=e,H.page=t,O(t),H=w(i,H);let s=await q.A.get(`/${i}`,H);s&&Array.isArray(s.data)&&(r(s.data),E(e),R(!1))},V=()=>D(!1),Y=async()=>{try{await q.A.remove(`/${$.type}/${$.id}`),H.page=I,F(H),D(!1),f.default.success(s("adminsetting.content.table.contentDeletedSuccessfully"))}catch(e){f.default.error(s("adminsetting.content.table.errorDeletingContent"))}};(0,d.useEffect)(()=>{F(H)},[i]);let X=async(e,t)=>{R(!0),H.sort={[e.selector]:t},await F(H),R(!1)},J=u().useMemo(()=>{let e=e=>{o(e)},t=e=>{e&&(H.query={title:e}),F(H)},r=l().debounce(e=>t(e),Number("500")||300);return(0,a.jsx)(y.default,{onFilter:e=>{j(e.target.value),r(e.target.value)},onClear:()=>{S&&(P(!c),j(""))},filterText:S,onFilterTypeChange:t=>e(t.target.value),filterType:i})},[S,i,c]);return(0,a.jsxs)(m.A,{style:{overflowX:"hidden"},fluid:!0,className:"p-0",children:[(0,a.jsx)(x.A,{children:(0,a.jsx)(g.A,{xs:12,children:(0,a.jsx)(v.A,{title:s("adminsetting.content.table.content")})})}),(0,a.jsx)(x.A,{className:"mt-3",children:(0,a.jsxs)(g.A,{xs:12,children:[(0,a.jsxs)(h.A,{show:N,onHide:V,children:[(0,a.jsx)(h.A.Header,{closeButton:!0,children:(0,a.jsx)(h.A.Title,{children:s("adminsetting.content.table.DeleteContent")})}),(0,a.jsx)(h.A.Body,{children:s("adminsetting.content.table.Areyousurewanttodeletethiscontent?")}),(0,a.jsxs)(h.A.Footer,{children:[(0,a.jsx)(_.A,{variant:"secondary",onClick:V,children:s("adminsetting.content.table.Cancel")}),(0,a.jsx)(_.A,{variant:"primary",onClick:Y,children:s("adminsetting.content.table.Yes")})]})]}),(0,a.jsx)(b.A,{columns:L,loading:T,data:t,totalRows:k,defaultRowsPerPage:C,subheader:!0,onSort:X,sortServer:!0,pagServer:!0,subHeaderComponent:J,persistTableHead:!0,resetPaginationToggle:c,handlePerRowsChange:W,handlePageChange:e=>{H.page=e,""!==S&&(H.query={title:S}),O(e),F(H)}})]})})]})};s()}catch(e){s(e)}})},63033:e=>{e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},65447:e=>{e.exports=require("@restart/ui/Button")},67364:e=>{e.exports=require("@restart/hooks/useCallbackRef")},68455:e=>{e.exports=import("redux-saga")},69722:e=>{e.exports=require("es6-promise")},74075:e=>{e.exports=require("zlib")},74716:e=>{e.exports=require("moment")},74987:e=>{e.exports=require("@restart/ui/ModalManager")},78097:e=>{e.exports=require("next-redux-saga")},78634:e=>{e.exports=require("@restart/ui/Anchor")},80237:(e,t)=>{Object.defineProperty(t,"A",{enumerable:!0,get:function(){return r}});var r=function(e){return e.PAGES="PAGES",e.PAGES_API="PAGES_API",e.APP_PAGE="APP_PAGE",e.APP_ROUTE="APP_ROUTE",e.IMAGE="IMAGE",e}({})},81366:e=>{e.exports=require("redux-auth-wrapper/connectedAuthWrapper")},81413:(e,t)=>{Object.defineProperty(t,"M",{enumerable:!0,get:function(){return function e(t,r){return r in t?t[r]:"then"in t&&"function"==typeof t.then?t.then(t=>e(t,r)):"function"==typeof t&&"default"===r?t:void 0}}})},81521:e=>{e.exports=require("dom-helpers/querySelectorAll")},81895:e=>{e.exports=require("@restart/hooks/useEventCallback")},82015:e=>{e.exports=require("react")},86842:e=>{e.exports=require("@restart/ui/SelectableContext")},87571:e=>{e.exports=require("dom-helpers/transitionEnd")},88751:e=>{e.exports=require("next-i18next")},93787:e=>{e.exports=require("redux-persist")},94947:e=>{e.exports=require("@restart/hooks/useTimeout")},96196:e=>{e.exports=require("next-redux-wrapper")},98320:e=>{e.exports=require("@restart/hooks/useIsomorphicEffect")},99460:e=>{e.exports=require("dom-helpers/css")}};var t=require("../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[6089,9216,9616,2386],()=>r(58620));module.exports=s})();