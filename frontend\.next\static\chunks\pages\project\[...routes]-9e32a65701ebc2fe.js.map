{"version": 3, "file": "static/chunks/pages/project/[...routes]-9e32a65701ebc2fe.js", "mappings": "wMAsCA,MA7Be,KAEb,IAAMA,EADSC,CAAAA,EAAAA,EAAAA,SAAAA,CAASA,EA4BXC,CA3BaC,KAAK,CAACH,CA2Bb,KA3BmB,EAAI,EAAE,CAEtCI,EAAuBC,CAAAA,EAAAA,EAAAA,iBAAAA,CAAiBA,CAAC,IAAM,UAACC,EAAAA,OAAWA,CAAAA,CAACN,OAAQA,KAE1E,OAAQA,CAAM,CAAC,EAAE,EACf,IAAK,SACH,MAAO,UAACI,EAAAA,CAAAA,EAEV,KAAK,OACH,MAAO,UAACE,EAAAA,OAAWA,CAAAA,CAACN,OAAQA,GAE9B,KAAK,OACH,MAAQ,UAACO,EAAAA,OAAWA,CAAAA,CAACP,OAAQA,GAE/B,SACE,OAAO,IACX,CACF,mBC3BA,4CACA,uBACA,WACA,OAAe,EAAQ,KAA4C,CACnE,EACA,SAFsB", "sources": ["webpack://_N_E/./pages/project/[...routes].tsx", "webpack://_N_E/?af6f"], "sourcesContent": ["//Import Library\r\nimport { useRouter } from 'next/router';\r\n\r\n//Import services/components\r\nimport ProjectForm from './Form';\r\nimport ProjectShow from './ProjectShow';\r\nimport { canAddProjectForm } from \"./permission\";\r\nimport { serverSideTranslations } from 'next-i18next/serverSideTranslations';\r\n\r\nconst Router = () => {\r\n  const router = useRouter()\r\n  const routes:any = router.query.routes || []\r\n\r\n  const CanAccessCreateForm  = canAddProjectForm(() => <ProjectForm routes={routes} />)\r\n\r\n  switch (routes[0]) {\r\n    case 'create':\r\n      return <CanAccessCreateForm />\r\n\r\n    case 'edit':\r\n      return <ProjectForm routes={routes} />\r\n\r\n    case 'show':\r\n      return (<ProjectShow routes={routes} />)\r\n\r\n    default:\r\n      return null;\r\n  }\r\n}\r\n\r\nexport async function getServerSideProps({ locale }: { locale: string }) {\r\n  return {\r\n    props: {\r\n      ...(await serverSideTranslations(locale, ['common'])),\r\n    },\r\n  }\r\n}\r\n\r\nexport default Router\r\n", "\n    (window.__NEXT_P = window.__NEXT_P || []).push([\n      \"/project/[...routes]\",\n      function () {\n        return require(\"private-next-pages/project/[...routes].tsx\");\n      }\n    ]);\n    if(module.hot) {\n      module.hot.dispose(function () {\n        window.__NEXT_P.push([\"/project/[...routes]\"])\n      });\n    }\n  "], "names": ["routes", "useRouter", "Router", "query", "CanAccessCreateForm", "canAddProjectForm", "ProjectForm", "ProjectShow"], "sourceRoot": "", "ignoreList": []}