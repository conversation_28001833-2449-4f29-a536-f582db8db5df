"use strict";exports.id=9679,exports.ids=[9679],exports.modules={6417:(e,t,r)=>{r.d(t,{A:()=>n});let a=r(82015).createContext(null);a.displayName="CardHeaderContext";let n=a},15653:(e,t,r)=>{r.d(t,{ks:()=>l,s3:()=>i});var a=r(8732);r(82015);var n=r(59549),s=r(43294);let l=({name:e,id:t,required:r,validator:l,errorMessage:i,onChange:d,value:o,as:c,multiline:u,rows:p,pattern:m,...h})=>(0,a.jsx)(s.Field,{name:e,validate:e=>{let t="string"==typeof e?e:String(e||"");return r&&(!e||""===t.trim())?i?.validator||"This field is required":l&&!l(e)?i?.validator||"Invalid value":m&&e&&!new RegExp(m).test(e)?i?.pattern||"Invalid format":void 0},children:({field:e,meta:r})=>(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(n.A.Control,{...e,...h,id:t,as:c||"input",rows:p,isInvalid:r.touched&&!!r.error,onChange:t=>{e.onChange(t),d&&d(t)},value:void 0!==o?o:e.value}),r.touched&&r.error?(0,a.jsx)(n.A.Control.Feedback,{type:"invalid",children:r.error}):null]})}),i=({name:e,id:t,required:r,errorMessage:l,onChange:i,value:d,children:o,...c})=>(0,a.jsx)(s.Field,{name:e,validate:e=>{if(r&&(!e||""===e))return l?.validator||"This field is required"},children:({field:e,meta:r})=>(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(n.A.Control,{as:"select",...e,...c,id:t,isInvalid:r.touched&&!!r.error,onChange:t=>{e.onChange(t),i&&i(t)},value:void 0!==d?d:e.value,children:o}),r.touched&&r.error?(0,a.jsx)(n.A.Control.Feedback,{type:"invalid",children:r.error}):null]})})},18597:(e,t,r)=>{r.d(t,{A:()=>C});var a=r(3892),n=r.n(a),s=r(82015),l=r(80739),i=r(8732);let d=s.forwardRef(({className:e,bsPrefix:t,as:r="div",...a},s)=>(t=(0,l.oU)(t,"card-body"),(0,i.jsx)(r,{ref:s,className:n()(e,t),...a})));d.displayName="CardBody";let o=s.forwardRef(({className:e,bsPrefix:t,as:r="div",...a},s)=>(t=(0,l.oU)(t,"card-footer"),(0,i.jsx)(r,{ref:s,className:n()(e,t),...a})));o.displayName="CardFooter";var c=r(6417);let u=s.forwardRef(({bsPrefix:e,className:t,as:r="div",...a},d)=>{let o=(0,l.oU)(e,"card-header"),u=(0,s.useMemo)(()=>({cardHeaderBsPrefix:o}),[o]);return(0,i.jsx)(c.A.Provider,{value:u,children:(0,i.jsx)(r,{ref:d,...a,className:n()(t,o)})})});u.displayName="CardHeader";let p=s.forwardRef(({bsPrefix:e,className:t,variant:r,as:a="img",...s},d)=>{let o=(0,l.oU)(e,"card-img");return(0,i.jsx)(a,{ref:d,className:n()(r?`${o}-${r}`:o,t),...s})});p.displayName="CardImg";let m=s.forwardRef(({className:e,bsPrefix:t,as:r="div",...a},s)=>(t=(0,l.oU)(t,"card-img-overlay"),(0,i.jsx)(r,{ref:s,className:n()(e,t),...a})));m.displayName="CardImgOverlay";let h=s.forwardRef(({className:e,bsPrefix:t,as:r="a",...a},s)=>(t=(0,l.oU)(t,"card-link"),(0,i.jsx)(r,{ref:s,className:n()(e,t),...a})));h.displayName="CardLink";var x=r(7783);let j=(0,x.A)("h6"),y=s.forwardRef(({className:e,bsPrefix:t,as:r=j,...a},s)=>(t=(0,l.oU)(t,"card-subtitle"),(0,i.jsx)(r,{ref:s,className:n()(e,t),...a})));y.displayName="CardSubtitle";let v=s.forwardRef(({className:e,bsPrefix:t,as:r="p",...a},s)=>(t=(0,l.oU)(t,"card-text"),(0,i.jsx)(r,{ref:s,className:n()(e,t),...a})));v.displayName="CardText";let f=(0,x.A)("h5"),g=s.forwardRef(({className:e,bsPrefix:t,as:r=f,...a},s)=>(t=(0,l.oU)(t,"card-title"),(0,i.jsx)(r,{ref:s,className:n()(e,t),...a})));g.displayName="CardTitle";let A=s.forwardRef(({bsPrefix:e,className:t,bg:r,text:a,border:s,body:o=!1,children:c,as:u="div",...p},m)=>{let h=(0,l.oU)(e,"card");return(0,i.jsx)(u,{ref:m,...p,className:n()(t,h,r&&`bg-${r}`,a&&`text-${a}`,s&&`border-${s}`),children:o?(0,i.jsx)(d,{children:c}):c})});A.displayName="Card";let C=Object.assign(A,{Img:p,Title:g,Subtitle:y,Body:d,Link:h,Text:v,Header:u,Footer:o,ImgOverlay:m})},23579:(e,t,r)=>{r.d(t,{sx:()=>c,s3:()=>n.s3,ks:()=>n.ks,yk:()=>a.A});var a=r(66994),n=r(15653),s=r(8732),l=r(82015),i=r.n(l),d=r(43294),o=r(59549);let c={RadioGroup:({name:e,valueSelected:t,onChange:r,errorMessage:a,children:n})=>{let{errors:l,touched:o}=(0,d.useFormikContext)(),c=o[e]&&l[e];i().useMemo(()=>({name:e}),[e]);let u=i().Children.map(n,t=>i().isValidElement(t)&&function(e){return"object"==typeof e&&null!==e}(t.props)?i().cloneElement(t,{name:e,...t.props}):t);return(0,s.jsxs)("div",{children:[(0,s.jsx)("div",{className:"radio-group",children:u}),c&&(0,s.jsx)("div",{className:"invalid-feedback d-block",children:a||("string"==typeof l[e]?l[e]:String(l[e]))})]})},RadioItem:({id:e,label:t,value:r,name:a,disabled:n})=>{let{values:l,setFieldValue:i}=(0,d.useFormikContext)(),c=a||e;return(0,s.jsx)(o.A.Check,{type:"radio",id:e,label:t,value:r,name:c,checked:l[c]===r,onChange:e=>{i(c,e.target.value)},disabled:n,inline:!0})}};a.A,n.ks,n.s3},24047:(e,t,r)=>{r.d(t,{x:()=>l});var a=r(8732),n=r(82015);let s=({value:e,onChange:t,placeholder:r="Write something...",height:s=300,disabled:l=!1})=>{let i=(0,n.useRef)(null),[d,o]=(0,n.useState)(!1);return(0,n.useEffect)(()=>{i.current},[e,d]),(0,a.jsx)("div",{className:"simple-rich-text-editor",style:{border:"1px solid #ccc"},children:!1})},l=e=>{let{initContent:t,onChange:r}=e;return(0,a.jsx)(s,{value:t||"",onChange:e=>r(e)})}},32472:(e,t,r)=>{r.a(e,async(e,a)=>{try{r.r(t),r.d(t,{default:()=>u});var n=r(8732);r(82015);var s=r(7082),l=r(49481),i=r(83551),d=r(98178),o=r(88751),c=e([d]);d=(c.then?(await c)():c)[0];let u=e=>{let{t}=(0,o.useTranslation)("common"),r=t=>{e.getId(t)},a=t=>{e.getSourceCollection(t)};return(0,n.jsx)(s.A,{className:"formCard",fluid:!0,children:(0,n.jsxs)(l.A,{children:[(0,n.jsx)(i.A,{className:"header-block",lg:12,children:(0,n.jsx)("h6",{children:(0,n.jsx)("span",{children:t("update.Image")})})}),(0,n.jsx)(i.A,{children:(0,n.jsx)(d.A,{datas:e.data,srcText:e.imgSrc,getImgID:e=>r(e),getImageSource:e=>a(e)})})]})})};a()}catch(e){a(e)}})},36835:(e,t,r)=>{r.r(t),r.d(t,{default:()=>p});var a=r(8732);r(82015);var n=r(7082),s=r(18597),l=r(83551),i=r(49481),d=r(59549),o=r(91353),c=r(88751),u=r(66994);let p=e=>{let{t}=(0,c.useTranslation)("common"),{link:r,handleChangeforTimeline:p,removeForm:m,addform:h}=e;return(0,a.jsx)(n.A,{className:"formCard",fluid:!0,children:(0,a.jsx)(s.A,{children:(0,a.jsx)(u.A,{onSubmit:()=>{},children:(0,a.jsxs)(s.A.Body,{children:[r&&r.map((e,r)=>(0,a.jsx)("div",{children:(0,a.jsxs)(l.A,{children:[(0,a.jsx)(i.A,{children:(0,a.jsxs)(d.A.Group,{children:[(0,a.jsx)(d.A.Label,{className:"required-field",children:t("update.Title")}),(0,a.jsx)(d.A.Control,{name:"title",id:"timetitle",type:"text",value:e.title,required:!0,onChange:e=>p(e,r)}),(0,a.jsx)(d.A.Control.Feedback,{type:"invalid",children:t("update.TitleisRequired")})]})}),(0,a.jsx)(i.A,{children:(0,a.jsxs)(d.A.Group,{children:[(0,a.jsx)(d.A.Label,{className:"required-field",children:t("update.Link")}),(0,a.jsx)(d.A.Control,{name:"link",id:"link",type:"text",required:!0,value:e.link,onChange:e=>p(e,r),pattern:"http(s)?://??[\\w.-]+[-a-zA-Z0-9@:%._\\+~#=]{2,256}\\.[a-z]{2,6}.+"}),(0,a.jsx)(d.A.Control.Feedback,{type:"invalid",children:t("update.Providevalidlink")})]})}),(0,a.jsx)(i.A,{children:(0,a.jsx)(d.A.Group,{children:0===r?(0,a.jsx)("div",{}):(0,a.jsx)(o.A,{variant:"secondary",style:{marginTop:"30px"},onClick:e=>m(e,r),children:t("update.Remove")})})})]})})),(0,a.jsx)(l.A,{children:(0,a.jsx)(i.A,{md:!0,lg:"4",children:(0,a.jsx)(o.A,{variant:"secondary",style:{marginTop:"27px",marginBottom:"20px"},onClick:h,children:t("update.ADD")})})})]})})})})}},45268:(e,t,r)=>{r.r(t),r.d(t,{default:()=>c});var a=r(8732);r(82015);var n=r(7082),s=r(83551),l=r(49481),i=r(59549),d=r(23579),o=r(88751);let c=e=>{let{t}=(0,o.useTranslation)("common"),{title:r,onHandleChange:c}=e;return(0,a.jsx)(n.A,{className:"formCard",fluid:!0,children:(0,a.jsx)(s.A,{children:(0,a.jsx)(l.A,{children:(0,a.jsxs)(i.A.Group,{children:[(0,a.jsxs)(i.A.Label,{className:"required-field",children:[t("update.Conversation")," ",t("update.Title")]}),(0,a.jsx)(d.ks,{name:"title",id:"title",required:!0,value:r,onChange:c,validator:e=>""!=e.trim(),errorMessage:{validator:t("Pleaseprovideconversationatitle")}})]})})})})}},49679:(e,t,r)=>{r.a(e,async(e,a)=>{try{r.r(t),r.d(t,{default:()=>U,getStaticProps:()=>q});var n=r(8732),s=r(82015),l=r(7082),i=r(18597),d=r(83551),o=r(49481),c=r(59549),u=r(91353),p=r(44233),m=r.n(p),h=r(74716),x=r.n(h),j=r(27825),y=r.n(j),v=r(23579),f=r(66994),g=r(36835),A=r(45268),C=r(25782),b=r(83065),k=r(81279),_=r(32472),N=r(63487),w=r(88751),S=r(35576),T=r(24047),D=e([C,k,_,N]);[C,k,_,N]=D.then?(await D)():D;let I={title:"",description:null,startDate:null,endDate:null,classification:null,parentType:null,parentId:null,showAsAnnouncement:!1,images:[],document:[],images_src:[],doc_src:[]},F={title:"",description:null,startDate:null,endDate:null,classification:null,showAsAnnouncement:!1,images:[],document:[],images_src:[],doc_src:[]},R={telephoneNo:"",mobileNo:""},$=[{title:"",link:""}],P={startDate:!1,endDate:!1};async function q({locale:e}){return{props:{...await (0,S.serverSideTranslations)(e,["common"])}}}let U=e=>{let t=(0,s.useRef)(null),{t:r}=(0,w.useTranslation)("common"),{query:a}=(0,p.useRouter)(),[h,j]=(0,s.useState)(I),[S,D]=(0,s.useState)({title:""}),[q,U]=(0,s.useState)([{title:"",link:""}]),[L,M]=(0,s.useState)(R),[E,G]=(0,s.useState)(P),[,z]=(0,s.useState)(!1),[B,H]=(0,s.useState)(""),[V,O]=(0,s.useState)([]),[W,K]=(0,s.useState)([]),[Z,J]=(0,s.useState)([]),[Q,X]=(0,s.useState)([]),Y=e.router&&e.router.query&&e.router.query.routes&&e.router.query.routes.length>1&&e.router.query.routes[1],ee=e=>{let{name:t,value:r}=e.target;D(e=>({...e,[t]:r}))},et=e=>{let{name:t,value:r}=e.target;M(e=>({...e,[t]:r}))},er=(e,t)=>{let{name:r,value:a}=e.target,n=[...q];n[t][r]=a,U(n)},ea=(e,t)=>{q.splice(t,1),U([...q]),0===q.length&&en()},en=()=>{let e={title:"",link:""};U(t=>[...t,e])},es=e=>{j(t=>({...t,description:e}))},el=(e,t)=>{j(r=>({...r,[t]:e})),G(e=>({...e,startDate:"startDate"===t,endDate:"endDate"===t}))},ei=()=>!!h.startDate||(G(e=>({...e,startDate:!0})),!1),ed=e=>{let{startDate:t,endDate:r}=h;if("Calendar Event"===B&&(G(e=>({...e,startDate:!0})),!t||!r))return e.preventDefault(),!0;if("Link"===B){let t=y().findIndex(q,{title:""}),r=y().findIndex(q,{link:""});if(-1!==t||-1!==r)return e.preventDefault(),!0}},eo=async t=>{t.type=h.parentType;let r=await N.A.patch(`/updates/${e.router.query.routes[1]}`,t);r&&r._id&&m().push(`/${h.parentType}/[...routes]`,`/${h.parentType}/show/${h.parentId}`)},ec=async t=>{switch(e.router.query.parent_type){case"operation":t.parent_operation=e.router.query.parent_id;break;case"event":t.parent_event=e.router.query.parent_id;break;case"project":t.parent_project=e.router.query.parent_id;break;case"vspace":t.parent_vspace=e.router.query.parent_id;break;case"country":t.parent_country=e.router.query.parent_id;break;case"hazard":t.parent_hazard=e.router.query.parent_id;break;case"institution":t.parent_institution=e.router.query.parent_id}let r=await N.A.post("/updates",t);r&&r._id&&m().push(`/${e.router.query.parent_type}/[...routes]`,`/${e.router.query.parent_type}/show/${e.router.query.parent_id}`)},eu=async(t,r)=>{let{title:a,description:n,startDate:s,endDate:l,showAsAnnouncement:i,document:d,doc_src:o,images:c,images_src:u}=r||h;if(ed(t))return;if(t&&t.preventDefault&&t.preventDefault(),t&&t.currentTarget&&!1===ei()){let e=t.currentTarget;if(!1===e.checkValidity())return void t.stopPropagation()}let p=q.find(e=>""==e.title||""==e.link);if("Link"==B&&p)return;z(!0);let m={title:"Conversation"===B?S.title.trim():a.trim(),type:e.router.query.parent_type,description:n,show_as_announcement:i,start_date:s,end_date:l,update_type:e.router.query.update_type,link:q,document:d||[],contact_details:L||{},images:c||[],images_src:u||[],doc_src:o||[]};e.router.query&&e.router.query.routes.length>1&&e.router.query.routes[1]?eo(m):ec(m)},ep=async()=>{let t=await N.A.get(`/updateType/${e.router.query.update_type}`);t&&H(t.title)},em=(e,t)=>{switch(t.type){case"operation":e.parentId=t.parent_operation;break;case"event":e.parentId=t.parent_event;break;case"project":e.parentId=t.parent_project;break;case"vspace":e.parentId=t.parent_vspace;break;case"country":e.parentId=t.parent_country;break;case"hazard":e.parentId=t.parent_hazard;break;case"institution":e.parentId=t.parent_institution}},eh=(e,t)=>{O(e.document?e.document:[]),K(e.doc_src?e.doc_src:[]),J(e.images?e.images:[]),X(e.images_src?e.images_src:[]),U(e.link.length?e.link:$),M(e.contact_details?e.contact_details:L),em(t,e)};(0,s.useEffect)(()=>{let t=async()=>{let t=await N.A.get(`/updates/${e.router.query.routes[1]}`),r={title:t.title,description:t.description,startDate:t.start_date?x()(t.start_date).toDate():null,endDate:t.end_date?x()(t.end_date).toDate():null,classification:null,parentType:t.type,showAsAnnouncement:t.show_as_announcement,document:t.document?t.document:[],doc_src:t.doc_src?t.doc_src:[],images:t.images?t.images:[],images_src:t.images_src?t.images_src:[]};eh(t,r),j(r)};ep(),Y&&t()},[]);let ex=e=>{let t=e.map(e=>e.serverID);j(e=>({...e,document:t}))},ej=e=>{let t=e.map(e=>e.serverID);j(e=>({...e,images:t}))},ey=e=>{j(t=>({...t,images_src:e}))},ev=e=>{j(t=>({...t,doc_src:e}))};return(0,n.jsx)(l.A,{className:"formCard",fluid:!0,children:(0,n.jsx)(i.A,{children:(0,n.jsx)(f.A,{onSubmit:eu,ref:t,initialValues:h,enableReinitialize:!0,onErrorSubmit:e=>{window.scrollTo(0,0)},children:(0,n.jsxs)(i.A.Body,{children:[(0,n.jsx)(d.A,{children:(0,n.jsx)(o.A,{children:(0,n.jsxs)(i.A.Title,{children:[`${r(Y?"update.Edit":"update.Create")}`," ",r("update.Update")," "]})})}),(0,n.jsx)("hr",{}),(0,n.jsx)(d.A,{children:(0,n.jsx)(o.A,{children:(0,n.jsxs)(c.A.Group,{children:[(0,n.jsx)(c.A.Label,{className:"required-field",children:r("update.Title")}),(0,n.jsx)(v.ks,{required:!0,validator:e=>""!==String(e||"").trim(),errorMessage:{validator:r("update.provide")},type:"text",name:"title",value:h.title,onChange:e=>{let{name:t,value:r}=e.target;j(e=>({...e,[t]:r}))}})]})})}),(0,n.jsx)(d.A,{children:(0,n.jsx)(o.A,{children:(0,n.jsxs)(c.A.Group,{children:[(0,n.jsx)(c.A.Label,{children:r("update.Description")}),(0,n.jsx)(T.x,{initContent:h.description,onChange:e=>es(e)})]})})}),(()=>{switch(B){case"Conversation":return(0,n.jsx)(A.default,{onHandleChange:ee,...S});case"Link":return(0,n.jsx)(g.default,{link:q,handleChangeforTimeline:er,removeForm:ea,addform:en});case"Document":return(0,n.jsx)(C.default,{srcText:W,getSourceCollection:e=>ev(e),data:V,getId:e=>ex(e)});case"Contact":return(0,n.jsx)(b.default,{onHandleChange:et,...L});case"Calendar Event":return(0,n.jsx)(k.default,{imgSrc:W,getSourceCollection:e=>ev(e),validation:E,onChangeDate:el,...h,data:V,getId:e=>ex(e)});case"Image":return(0,n.jsx)(_.default,{data:Z,imgSrc:Q,getId:e=>ej(e),getSourceCollection:e=>ey(e)});case"General / Notice":return(0,n.jsxs)(n.Fragment,{children:[(0,n.jsx)(C.default,{srcText:W,getSourceCollection:e=>ev(e),data:V,getId:e=>ex(e)}),(0,n.jsx)(_.default,{imgSrc:Q,getSourceCollection:e=>ey(e),data:Z,getId:e=>ej(e)})]});default:return null}})(),(0,n.jsx)(d.A,{className:"mt-3",children:(0,n.jsx)(o.A,{children:(0,n.jsx)(c.A.Group,{controlId:"showAsAnnouncement",children:(0,n.jsx)(c.A.Check,{className:"check",id:"showAsAnnouncement",name:"showAsAnnouncement",type:"checkbox",label:r("update.Showasanannouncement"),checked:h.showAsAnnouncement,onChange:e=>{let{checked:t}=e.target;j(e=>({...e,showAsAnnouncement:t}))}})})})}),(0,n.jsx)(d.A,{className:"my-4",children:(0,n.jsxs)(o.A,{children:[(0,n.jsx)(u.A,{className:"me-2",type:"submit",variant:"primary",onClick:()=>{G(e=>({...e,startDate:!0}))},children:r("submit")}),(0,n.jsx)(u.A,{className:"me-2",onClick:()=>{j(e=>({...e,...F})),O([]),K([]),J([]),X([]),M(R),U($),window.scrollTo(0,0)},variant:"info",children:r("reset")}),(0,n.jsx)(u.A,{onClick:()=>{a&&"add"===a.routes[0]?m().push(`/${a.parent_type}/show/${a.parent_id}`):e&&e.router&&e.router.query&&m().push(`/${h.parentType}/[...routes]`,`/${h.parentType}/show/${h.parentId}`)},variant:"secondary",children:r("Cancel")})]})})]})})})})};a()}catch(e){a(e)}})},58070:(e,t,r)=>{r.d(t,{A:()=>l});var a=r(8732);r(82015);var n=r(29780),s=r.n(n);let l=e=>(0,a.jsx)(s(),{...e})},66994:(e,t,r)=>{r.d(t,{A:()=>d});var a=r(8732),n=r(82015),s=r(43294),l=r(18622);let i=(0,n.forwardRef)((e,t)=>{let{children:r,onSubmit:n,autoComplete:i,className:d,onKeyPress:o,initialValues:c,...u}=e,p=l.object().shape({});return(0,a.jsx)(s.Formik,{initialValues:c||{},validationSchema:p,onSubmit:(e,t)=>{let r={preventDefault:()=>{},stopPropagation:()=>{},currentTarget:null,target:null,nativeEvent:new Event("submit"),bubbles:!1,cancelable:!0,defaultPrevented:!1,eventPhase:0,isTrusted:!1,timeStamp:Date.now(),type:"submit",isDefaultPrevented:()=>!1,isPropagationStopped:()=>!1,persist:()=>{}};n&&n(r,e,t)},...u,children:e=>(0,a.jsx)(s.Form,{ref:t,onSubmit:e.handleSubmit,autoComplete:i,className:d,onKeyPress:o,children:"function"==typeof r?r(e):r})})});i.displayName="ValidationFormWrapper";let d=i},81279:(e,t,r)=>{r.a(e,async(e,a)=>{try{r.r(t),r.d(t,{default:()=>h});var n=r(8732),s=r(82015),l=r(7082),i=r(49481),d=r(83551),o=r(59549),c=r(25782),u=r(58070),p=r(88751),m=e([c]);c=(m.then?(await m)():m)[0];let h=e=>{let[,t]=(0,s.useState)(!1),{validation:r,onChangeDate:a,startDate:m,endDate:h}=e,{t:x}=(0,p.useTranslation)("common");(0,s.useEffect)(()=>{null==h?t(!1):t(!0)},[h]);let j=t=>{e.getId(t)},y=t=>{e.getSourceCollection(t)};return(0,n.jsxs)(l.A,{className:"formCard",fluid:!0,children:[(0,n.jsx)(i.A,{className:"header-block",lg:12,children:(0,n.jsx)("h6",{children:(0,n.jsx)("span",{children:x("update.Date")})})}),(0,n.jsxs)(d.A,{children:[(0,n.jsxs)(i.A,{children:[(0,n.jsxs)(o.A.Group,{children:[(0,n.jsx)(o.A.Label,{className:"d-block required-field",children:x("update.StartDate")}),(0,n.jsx)(u.A,{selected:m,minDate:new Date,showTimeSelect:!0,timeIntervals:15,onChange:e=>a(e,"startDate"),placeholderText:x("update.Selectadate"),dateFormat:"MMMM d, yyyy h:mm aa"})]}),!0===r.startDate&&!m&&(0,n.jsx)("p",{style:{color:"red"},children:x("update.Pleaseenterthestartdate")})]}),(0,n.jsxs)(i.A,{children:[(0,n.jsxs)(o.A.Group,{children:[(0,n.jsx)(o.A.Label,{className:"d-block required-field",children:x("update.EndDate")}),(0,n.jsx)(u.A,{selected:h,showTimeSelect:!0,timeIntervals:15,onChange:e=>a(e,"endDate"),placeholderText:x("update.Selectadate"),minDate:m,dateFormat:"MMMM d, yyyy h:mm aa"})]}),!0===r.startDate&&!h&&(0,n.jsxs)("p",{style:{color:"red"},children:[x("update.Pleaseentertheenddate")," "]})]})]}),(0,n.jsx)(c.default,{data:e.data,srcText:e.imgSrc,getId:e=>j(e),getSourceCollection:e=>y(e)})]})};a()}catch(e){a(e)}})},83065:(e,t,r)=>{r.r(t),r.d(t,{default:()=>o});var a=r(8732);r(82015);var n=r(7082),s=r(83551),l=r(49481),i=r(59549),d=r(88751);let o=e=>{let{t}=(0,d.useTranslation)("common"),{onHandleChange:r,telephoneNo:o,mobileNo:c}=e;return(0,a.jsx)(n.A,{className:"formCard",fluid:!0,children:(0,a.jsx)(s.A,{children:(0,a.jsxs)(l.A,{children:[(0,a.jsxs)(i.A.Group,{children:[(0,a.jsx)(i.A.Label,{className:"required-field",children:t("Updates.TelephoneNo")}),(0,a.jsx)(i.A.Control,{type:"number",name:"telephoneNo",placeholder:t("Updates.TelephoneNumber"),required:!0,value:o,onChange:r}),(0,a.jsx)(i.A.Control.Feedback,{type:"invalid",children:t("Updates.PleaseTelephoneNumber")})]}),(0,a.jsxs)(i.A.Group,{children:[(0,a.jsx)(i.A.Label,{className:"required-field",children:t("Updates.MobileNo")}),(0,a.jsx)(i.A.Control,{type:"number",name:"mobileNo",placeholder:t("Updates.MobileNumber"),required:!0,value:c,onChange:r}),(0,a.jsx)(i.A.Control.Feedback,{type:"invalid",children:t("Updates.PleaseProvideMobile")})]})]})})})}}};