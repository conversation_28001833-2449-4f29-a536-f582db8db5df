"use strict";(()=>{var e={};e.id=4312,e.ids=[636,3220,4312],e.modules={123:e=>{e.exports=require("dom-helpers/removeEventListener")},1332:e=>{e.exports=require("react-custom-scrollbars-2")},1428:e=>{e.exports=import("axios")},1680:e=>{e.exports=require("@restart/ui/utils")},1919:e=>{e.exports=require("react-transition-group/Transition")},3892:e=>{e.exports=require("classnames")},4048:e=>{e.exports=require("react-truncate")},6009:e=>{e.exports=require("dom-helpers/ownerDocument")},6952:e=>{e.exports=require("@restart/ui/Modal")},7374:e=>{e.exports=require("@restart/ui/Dropdown")},8732:e=>{e.exports=require("react/jsx-runtime")},9653:e=>{e.exports=require("@restart/ui/Overlay")},10390:(e,r,t)=>{t.a(e,async(e,s)=>{try{t.r(r),t.d(r,{default:()=>_});var a=t(8732),o=t(19918),n=t.n(o),i=t(82015),u=t.n(i),l=t(27825),p=t.n(l),c=t(44233),d=t(56084),x=t(63276),q=t(63487),g=t(88751),h=e([x,q]);[x,q]=h.then?(await h)():h;let m="partner_institutions.partner_country",y=({partner_institutions:e})=>e&&e.length>0?(0,a.jsx)("ul",{children:e.map((e,r)=>{if(e.partner_country)return(0,a.jsx)("li",{children:(0,a.jsx)(n(),{href:"/country/[...routes]",as:`/country/show/${e.partner_country?._id}`,children:e.partner_country.title})},r)})}):null,_=function(e){let r=(0,c.useRouter)(),{t}=(0,g.useTranslation)("common"),{setProjects:s,selectedRegions:o}=e,[l,h]=u().useState(""),[_,P]=u().useState(""),[S,f]=u().useState(!1),[b,A]=(0,i.useState)([]),[v,w]=(0,i.useState)(!1),[j,M]=(0,i.useState)(0),[C,k]=(0,i.useState)(10),[T,E]=(0,i.useState)(1),[R,D]=(0,i.useState)(null),N={sort:{created_at:"desc"},lean:!0,limit:C,page:1,query:{},populate:[{path:"area_of_work",select:"title"},{path:m,select:"coordinates title"},{path:"status",select:"title"}],select:"-website -description -start_date -end_date -country -region -partner_institutions.partner_region -partner_institutions.partner_institution -institution_invites -vspace -vspace_visibility -user -created_at -updated_at"},[G,I]=(0,i.useState)(N),O=[{name:t("Project(s)"),selector:"title",sortable:!0,cell:e=>(0,a.jsx)(n(),{href:"/project/[...routes]",as:`/project/show/${e?._id}`,children:e.title})},{name:t("Country"),selector:"country",sortable:!0,cell:e=>(0,a.jsx)(y,{partner_institutions:e.partner_institutions})},{name:t("AreaofWork"),selector:"area_of_work",cell:e=>e.area_of_work?e.area_of_work.map(e=>e.title).join(", "):""},{name:t("Status"),selector:"status",sortable:!0,cell:e=>e.status&&e.status.title?e.status.title:""},{name:t("Fundedby"),selector:"funded_by",sortable:!0}],H=async e=>{w(!0),r.query&&r.query.country&&(e.query[m]=[r.query.country]),null===o?delete e.query["partner_institutions.world_region"]:0===o.length?e.query["partner_institutions.world_region"]=["__NO_MATCH__"]:e.query["partner_institutions.world_region"]=o;let t=await q.A.get("/project",e);t&&Array.isArray(t.data)&&(A(t.data),s(t.data),M(t.totalCount)),w(!1)},W=async(e,t)=>{N.limit=e,N.page=t,w(!0),r.query&&r.query.country&&(N.query[m]=[r.query.country]),null===o?delete N.query["partner_institutions.world_region"]:0===o.length?N.query["partner_institutions.world_region"]=["__NO_MATCH__"]:N.query["partner_institutions.world_region"]=o,_&&(N.query={...N.query,status:_}),R&&(N.sort=R.sort);let a=await q.A.get("/project",N);a&&Array.isArray(a.data)&&(A(a.data),s(a.data),k(e),w(!1)),E(t)},F=async(e,r)=>{w(!0),N.sort={[e.selector]:r},_&&(N.query={...N.query,status:_}),""!==l&&(N.query={...N.query,title:l}),await H(N),D(N),w(!1)},U=(e,r)=>{e?(G.query.title=e,G.page=r):delete G.query.title,I({...G})},L=(0,i.useRef)(p().debounce((e,r)=>U(e,r),Number("500")||300)).current,z=(0,i.useMemo)(()=>{let e=e=>{P(e),e?(G.query.status=e,G.page=T):delete G.query.status,I({...G})};return(0,a.jsx)(x.default,{onFilter:e=>{h(e.target.value),L(e.target.value,T)},onFilterStatusChange:r=>e(r.target.value),onClear:()=>{l&&(f(!S),h(""))},filterText:l,filterStatus:_})},[l,_,S,o,T]);return(0,a.jsx)(d.A,{columns:O,data:b,totalRows:j,loading:v,subheader:!0,persistTableHead:!0,onSort:F,sortServer:!0,pagServer:!0,resetPaginationToggle:S,subHeaderComponent:z,handlePerRowsChange:W,handlePageChange:e=>{N.limit=C,N.page=e,_&&(N.query={...N.query,status:_}),R&&(N.sort=R.sort),H(N),E(e)}})};s()}catch(e){s(e)}})},11242:e=>{e.exports=require("redux-persist/integration/react")},11688:e=>{e.exports=require("@restart/hooks/useWillUnmount")},12388:e=>{e.exports=require("dom-helpers/addEventListener")},13364:e=>{e.exports=require("redux-saga/effects")},14062:e=>{e.exports=import("react-redux")},14078:e=>{e.exports=import("swr")},14332:e=>{e.exports=require("uncontrollable")},16116:e=>{e.exports=require("invariant")},17085:(e,r,t)=>{t.a(e,async(e,s)=>{try{t.r(r),t.d(r,{config:()=>h,default:()=>d,getServerSideProps:()=>g,getStaticPaths:()=>q,getStaticProps:()=>x,reportWebVitals:()=>m,routeModule:()=>b,unstable_getServerProps:()=>S,unstable_getServerSideProps:()=>f,unstable_getStaticParams:()=>P,unstable_getStaticPaths:()=>_,unstable_getStaticProps:()=>y});var a=t(63885),o=t(80237),n=t(81413),i=t(9616),u=t.n(i),l=t(72386),p=t(10390),c=e([l,p]);[l,p]=c.then?(await c)():c;let d=(0,n.M)(p,"default"),x=(0,n.M)(p,"getStaticProps"),q=(0,n.M)(p,"getStaticPaths"),g=(0,n.M)(p,"getServerSideProps"),h=(0,n.M)(p,"config"),m=(0,n.M)(p,"reportWebVitals"),y=(0,n.M)(p,"unstable_getStaticProps"),_=(0,n.M)(p,"unstable_getStaticPaths"),P=(0,n.M)(p,"unstable_getStaticParams"),S=(0,n.M)(p,"unstable_getServerProps"),f=(0,n.M)(p,"unstable_getServerSideProps"),b=new a.PagesRouteModule({definition:{kind:o.A.PAGES,page:"/project/ProjectsTable",pathname:"/project/ProjectsTable",bundlePath:"",filename:""},components:{App:l.default,Document:u()},userland:p});s()}catch(e){s(e)}})},18622:e=>{e.exports=require("yup")},19121:e=>{e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},22326:e=>{e.exports=require("react-dom")},22541:e=>{e.exports=require("redux-persist/lib/storage")},25303:e=>{e.exports=require("@restart/ui/NavItem")},26324:e=>{e.exports=require("warning")},27825:e=>{e.exports=require("lodash")},27910:e=>{e.exports=require("stream")},28217:e=>{e.exports=require("@restart/ui/DropdownItem")},29021:e=>{e.exports=require("fs")},29294:e=>{e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},29628:e=>{e.exports=require("dom-helpers/addClass")},29825:e=>{e.exports=require("prop-types")},29841:e=>{e.exports=require("dom-helpers/hasClass")},30362:e=>{e.exports=require("@restart/ui/DropdownToggle")},33873:e=>{e.exports=require("path")},36653:e=>{e.exports=require("nprogress")},36955:e=>{e.exports=require("@restart/hooks/useBreakpoint")},37766:e=>{e.exports=require("@restart/hooks/useMergedRefs")},38609:e=>{e.exports=require("react-data-table-component")},39756:e=>{e.exports=import("redux")},40051:e=>{e.exports=require("dom-helpers/removeClass")},40361:e=>{e.exports=require("next/dist/compiled/next-server/pages.runtime.prod.js")},42893:e=>{e.exports=import("react-hot-toast")},43294:e=>{e.exports=require("formik")},50009:e=>{e.exports=require("@restart/ui/DropdownMenu")},56084:(e,r,t)=>{t.d(r,{A:()=>l});var s=t(8732);t(82015);var a=t(38609),o=t.n(a),n=t(88751),i=t(30370);function u(e){let{t:r}=(0,n.useTranslation)("common"),t={rowsPerPageText:r("Rowsperpage")},{columns:a,data:u,totalRows:l,resetPaginationToggle:p,subheader:c,subHeaderComponent:d,handlePerRowsChange:x,handlePageChange:q,rowsPerPage:g,defaultRowsPerPage:h,selectableRows:m,loading:y,pagServer:_,onSelectedRowsChange:P,clearSelectedRows:S,sortServer:f,onSort:b,persistTableHead:A,sortFunction:v,...w}=e,j={paginationComponentOptions:t,noDataComponent:r("NoData"),noHeader:!0,columns:a,data:u||[],dense:!0,paginationResetDefaultPage:p,subHeader:c,progressPending:y,subHeaderComponent:d,pagination:!0,paginationServer:_,paginationPerPage:h||10,paginationRowsPerPageOptions:g||[10,15,20,25,30],paginationTotalRows:l,onChangeRowsPerPage:x,onChangePage:q,selectableRows:m,onSelectedRowsChange:P,clearSelectedRows:S,progressComponent:(0,s.jsx)(i.A,{}),sortIcon:(0,s.jsx)("i",{className:"sort-icon fas fa-exchange-alt"}),sortServer:f,onSort:b,sortFunction:v,persistTableHead:A,className:"rki-table"};return(0,s.jsx)(o(),{...j})}u.defaultProps={subHeader:!1,pagination:!0,totalRows:null,pagServer:!0,onSelectedRowsChange:null,clearSelectedRows:!1,sortServer:!1,persistTableHead:!1};let l=u},57664:e=>{e.exports=require("dom-helpers/scrollbarSize")},58928:e=>{e.exports=require("dom-helpers/canUseDOM")},59717:e=>{e.exports=require("@restart/ui/DropdownContext")},60560:e=>{e.exports=require("dom-helpers/contains")},63033:e=>{e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},63276:(e,r,t)=>{t.a(e,async(e,s)=>{try{t.r(r),t.d(r,{default:()=>q});var a=t(8732),o=t(82015),n=t(7082),i=t(83551),u=t(49481),l=t(84517),p=t(59549),c=t(88751),d=t(63487),x=e([d]);d=(x.then?(await x)():x)[0];let q=({filterText:e,onFilter:r,onFilterStatusChange:t,onClear:s,filterStatus:x})=>{let[q,g]=(0,o.useState)([]),{t:h}=(0,c.useTranslation)("common"),m=async e=>{let r=await d.A.get("/projectstatus",e);r&&Array.isArray(r.data)&&g(r.data)};return(0,o.useEffect)(()=>{m({query:{},sort:{title:"asc"}})},[]),(0,a.jsx)(n.A,{fluid:!0,className:"p-0",children:(0,a.jsxs)(i.A,{children:[(0,a.jsx)(u.A,{xs:6,className:"ps-0 align-self-end mb-3",children:(0,a.jsx)(l.A,{type:"text",className:"searchInput",placeholder:h("vspace.Search"),"aria-label":"Search",value:e,onChange:r})}),(0,a.jsx)(u.A,{children:(0,a.jsx)(p.A,{children:(0,a.jsxs)(p.A.Group,{as:i.A,controlId:"statusFilter",children:[(0,a.jsx)(p.A.Label,{column:!0,sm:"3",lg:"2",children:"Status"}),(0,a.jsx)(u.A,{className:"ps-0 pe-1",children:(0,a.jsxs)(l.A,{as:"select","aria-label":"Status",onChange:t,value:x,children:[(0,a.jsx)("option",{value:"",children:"All"}),q.map((e,r)=>(0,a.jsx)("option",{value:e._id,children:e.title},r))]})})]})})})]})})};s()}catch(e){s(e)}})},65447:e=>{e.exports=require("@restart/ui/Button")},67364:e=>{e.exports=require("@restart/hooks/useCallbackRef")},68455:e=>{e.exports=import("redux-saga")},69722:e=>{e.exports=require("es6-promise")},74075:e=>{e.exports=require("zlib")},74716:e=>{e.exports=require("moment")},74987:e=>{e.exports=require("@restart/ui/ModalManager")},78097:e=>{e.exports=require("next-redux-saga")},78634:e=>{e.exports=require("@restart/ui/Anchor")},80237:(e,r)=>{Object.defineProperty(r,"A",{enumerable:!0,get:function(){return t}});var t=function(e){return e.PAGES="PAGES",e.PAGES_API="PAGES_API",e.APP_PAGE="APP_PAGE",e.APP_ROUTE="APP_ROUTE",e.IMAGE="IMAGE",e}({})},81366:e=>{e.exports=require("redux-auth-wrapper/connectedAuthWrapper")},81413:(e,r)=>{Object.defineProperty(r,"M",{enumerable:!0,get:function(){return function e(r,t){return t in r?r[t]:"then"in r&&"function"==typeof r.then?r.then(r=>e(r,t)):"function"==typeof r&&"default"===t?r:void 0}}})},81521:e=>{e.exports=require("dom-helpers/querySelectorAll")},81895:e=>{e.exports=require("@restart/hooks/useEventCallback")},82015:e=>{e.exports=require("react")},86842:e=>{e.exports=require("@restart/ui/SelectableContext")},87571:e=>{e.exports=require("dom-helpers/transitionEnd")},88751:e=>{e.exports=require("next-i18next")},93787:e=>{e.exports=require("redux-persist")},94947:e=>{e.exports=require("@restart/hooks/useTimeout")},96196:e=>{e.exports=require("next-redux-wrapper")},98320:e=>{e.exports=require("@restart/hooks/useIsomorphicEffect")},99460:e=>{e.exports=require("dom-helpers/css")}};var r=require("../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[6089,9216,9616,2386],()=>t(17085));module.exports=s})();