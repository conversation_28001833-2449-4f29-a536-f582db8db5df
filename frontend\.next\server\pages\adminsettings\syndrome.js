"use strict";(()=>{var e={};e.id=6196,e.ids=[636,3220,6196],e.modules={123:e=>{e.exports=require("dom-helpers/removeEventListener")},1332:e=>{e.exports=require("react-custom-scrollbars-2")},1428:e=>{e.exports=import("axios")},1680:e=>{e.exports=require("@restart/ui/utils")},1919:e=>{e.exports=require("react-transition-group/Transition")},3892:e=>{e.exports=require("classnames")},4048:e=>{e.exports=require("react-truncate")},6009:e=>{e.exports=require("dom-helpers/ownerDocument")},6952:e=>{e.exports=require("@restart/ui/Modal")},7374:e=>{e.exports=require("@restart/ui/Dropdown")},8732:e=>{e.exports=require("react/jsx-runtime")},9653:e=>{e.exports=require("@restart/ui/Overlay")},11242:e=>{e.exports=require("redux-persist/integration/react")},11688:e=>{e.exports=require("@restart/hooks/useWillUnmount")},12388:e=>{e.exports=require("dom-helpers/addEventListener")},13364:e=>{e.exports=require("redux-saga/effects")},14062:e=>{e.exports=import("react-redux")},14078:e=>{e.exports=import("swr")},14332:e=>{e.exports=require("uncontrollable")},16116:e=>{e.exports=require("invariant")},18622:e=>{e.exports=require("yup")},19121:e=>{e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},22326:e=>{e.exports=require("react-dom")},22541:e=>{e.exports=require("redux-persist/lib/storage")},25303:e=>{e.exports=require("@restart/ui/NavItem")},26324:e=>{e.exports=require("warning")},27053:(e,r,s)=>{s.d(r,{A:()=>a});var t=s(8732);function a(e){return(0,t.jsx)("h2",{className:"page-heading",children:e.title})}},27825:e=>{e.exports=require("lodash")},27910:e=>{e.exports=require("stream")},28217:e=>{e.exports=require("@restart/ui/DropdownItem")},29021:e=>{e.exports=require("fs")},29294:e=>{e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},29628:e=>{e.exports=require("dom-helpers/addClass")},29825:e=>{e.exports=require("prop-types")},29841:e=>{e.exports=require("dom-helpers/hasClass")},30362:e=>{e.exports=require("@restart/ui/DropdownToggle")},33873:e=>{e.exports=require("path")},35557:(e,r,s)=>{s.r(r),s.d(r,{default:()=>a});var t=s(8732);function a(e){return(0,t.jsx)("div",{className:"container-fluid p-0 response-message-block",children:(0,t.jsx)("div",{className:"message",children:"you don't have permission to access"})})}},35576:e=>{e.exports=require("next-i18next/serverSideTranslations")},36653:e=>{e.exports=require("nprogress")},36955:e=>{e.exports=require("@restart/hooks/useBreakpoint")},37766:e=>{e.exports=require("@restart/hooks/useMergedRefs")},38609:e=>{e.exports=require("react-data-table-component")},39756:e=>{e.exports=import("redux")},40051:e=>{e.exports=require("dom-helpers/removeClass")},40361:e=>{e.exports=require("next/dist/compiled/next-server/pages.runtime.prod.js")},42893:e=>{e.exports=import("react-hot-toast")},43294:e=>{e.exports=require("formik")},45927:(e,r,s)=>{s.r(r),s.d(r,{canAddAreaOfWork:()=>n,canAddContent:()=>C,canAddCountry:()=>o,canAddDeploymentStatus:()=>p,canAddEventStatus:()=>d,canAddExpertise:()=>u,canAddFocalPointApproval:()=>l,canAddHazardTypes:()=>x,canAddHazards:()=>m,canAddLandingPage:()=>j,canAddOperationStatus:()=>A,canAddOrganisationApproval:()=>g,canAddOrganisationNetworks:()=>h,canAddOrganisationTypes:()=>y,canAddProjectStatus:()=>S,canAddRegions:()=>q,canAddRiskLevels:()=>v,canAddSyndromes:()=>w,canAddUpdateTypes:()=>f,canAddUsers:()=>P,canAddVspaceApproval:()=>c,canAddWorldRegion:()=>_,default:()=>D});var t=s(81366),a=s.n(t);let i="create:any",n=a()({authenticatedSelector:e=>!!e.permissions&&!!e.permissions.area_of_work&&!!e.permissions.area_of_work[i],wrapperDisplayName:"CanAddAreaOfWork"}),o=a()({authenticatedSelector:e=>!!e.permissions&&!!e.permissions.country&&!!e.permissions.country[i],wrapperDisplayName:"CanAddCountry"}),p=a()({authenticatedSelector:e=>!!e.permissions&&!!e.permissions.deployment_status&&!!e.permissions.deployment_status[i],wrapperDisplayName:"CanAddDeploymentStatus"}),d=a()({authenticatedSelector:e=>!!e.permissions&&!!e.permissions.event_status&&!!e.permissions.event_status[i],wrapperDisplayName:"CanAddEventStatus"}),u=a()({authenticatedSelector:e=>!!e.permissions&&!!e.permissions.expertise&&!!e.permissions.expertise[i],wrapperDisplayName:"CanAddExpertise"}),l=a()({authenticatedSelector:e=>!!e.permissions&&!!e.permissions.institution_focal_point&&!!e.permissions.institution_focal_point[i],wrapperDisplayName:"CanAddFocalPointApproval"}),c=a()({authenticatedSelector:e=>!!e.permissions&&!!e.permissions.institution_focal_point&&!!e.permissions.institution_focal_point[i],wrapperDisplayName:"CanAddVspaceApproval"}),m=a()({authenticatedSelector:e=>!!e.permissions&&!!e.permissions.hazard&&!!e.permissions.hazard[i],wrapperDisplayName:"CanAddHazards"}),x=a()({authenticatedSelector:e=>!!e.permissions&&!!e.permissions.hazard_type&&!!e.permissions.hazard_type[i],wrapperDisplayName:"CanAddHazardTypes"}),g=a()({authenticatedSelector:e=>!!e.permissions&&!!e.permissions.institution&&!!e.permissions.institution[i],wrapperDisplayName:"CanAddOrganisationApproval"}),h=a()({authenticatedSelector:e=>!!e.permissions&&!!e.permissions.institution_network&&!!e.permissions.institution_network[i],wrapperDisplayName:"CanAddOrganisationNetworks"}),y=a()({authenticatedSelector:e=>!!e.permissions&&!!e.permissions.institution_type&&!!e.permissions.institution_type[i],wrapperDisplayName:"CanAddOrganisationTypes"}),A=a()({authenticatedSelector:e=>!!e.permissions&&!!e.permissions.operation_status&&!!e.permissions.operation_status[i],wrapperDisplayName:"CanAddOperationStatus"}),S=a()({authenticatedSelector:e=>!!e.permissions&&!!e.permissions.project_status&&!!e.permissions.project_status[i],wrapperDisplayName:"CanAddProjectStatus"}),q=a()({authenticatedSelector:e=>!!e.permissions&&!!e.permissions.region&&!!e.permissions.region[i],wrapperDisplayName:"CanAddRegions"}),v=a()({authenticatedSelector:e=>!!e.permissions&&!!e.permissions.risk_level&&!!e.permissions.risk_level[i],wrapperDisplayName:"CanAddRiskLevels"}),w=a()({authenticatedSelector:e=>!!e.permissions&&!!e.permissions.syndrome&&!!e.permissions.syndrome[i],wrapperDisplayName:"CanAddSyndromes"}),f=a()({authenticatedSelector:e=>!!e.permissions&&!!e.permissions.update_type&&!!e.permissions.update_type[i],wrapperDisplayName:"CanAddUpdateTypes"}),P=a()({authenticatedSelector:e=>!!e.permissions&&!!e.permissions.users&&!!e.permissions.users[i],wrapperDisplayName:"CanAddUsers"}),_=a()({authenticatedSelector:e=>!!e.permissions&&!!e.permissions.worl_region&&!!e.permissions.worl_region[i],wrapperDisplayName:"CanAddWorldRegion"}),j=a()({authenticatedSelector:e=>!!e.permissions&&!!e.permissions.landing_page&&!!e.permissions.landing_page[i],wrapperDisplayName:"CanAddLandingPage"}),C=a()({authenticatedSelector:e=>!!e.permissions&&!!e.permissions.operation&&!!e.permissions.operation[i]&&!!e.permissions.project&&!!e.permissions.project[i]&&!!e.permissions.event&&!!e.permissions.event[i]&&!!e.permissions.vspace&&!!e.permissions.vspace[i]&&!!e.permissions.institution&&!!e.permissions.institution[i]&&!!e.permissions.update&&!!e.permissions.update[i]||!1,wrapperDisplayName:"CanAddContent"}),D=n},47214:(e,r,s)=>{s.a(e,async(e,t)=>{try{s.r(r),s.d(r,{config:()=>h,default:()=>c,getServerSideProps:()=>g,getStaticPaths:()=>x,getStaticProps:()=>m,reportWebVitals:()=>y,routeModule:()=>f,unstable_getServerProps:()=>v,unstable_getServerSideProps:()=>w,unstable_getStaticParams:()=>q,unstable_getStaticPaths:()=>S,unstable_getStaticProps:()=>A});var a=s(63885),i=s(80237),n=s(81413),o=s(9616),p=s.n(o),d=s(72386),u=s(62969),l=e([d,u]);[d,u]=l.then?(await l)():l;let c=(0,n.M)(u,"default"),m=(0,n.M)(u,"getStaticProps"),x=(0,n.M)(u,"getStaticPaths"),g=(0,n.M)(u,"getServerSideProps"),h=(0,n.M)(u,"config"),y=(0,n.M)(u,"reportWebVitals"),A=(0,n.M)(u,"unstable_getStaticProps"),S=(0,n.M)(u,"unstable_getStaticPaths"),q=(0,n.M)(u,"unstable_getStaticParams"),v=(0,n.M)(u,"unstable_getServerProps"),w=(0,n.M)(u,"unstable_getServerSideProps"),f=new a.PagesRouteModule({definition:{kind:i.A.PAGES,page:"/adminsettings/syndrome",pathname:"/adminsettings/syndrome",bundlePath:"",filename:""},components:{App:d.default,Document:p()},userland:u});t()}catch(e){t(e)}})},50009:e=>{e.exports=require("@restart/ui/DropdownMenu")},56084:(e,r,s)=>{s.d(r,{A:()=>d});var t=s(8732);s(82015);var a=s(38609),i=s.n(a),n=s(88751),o=s(30370);function p(e){let{t:r}=(0,n.useTranslation)("common"),s={rowsPerPageText:r("Rowsperpage")},{columns:a,data:p,totalRows:d,resetPaginationToggle:u,subheader:l,subHeaderComponent:c,handlePerRowsChange:m,handlePageChange:x,rowsPerPage:g,defaultRowsPerPage:h,selectableRows:y,loading:A,pagServer:S,onSelectedRowsChange:q,clearSelectedRows:v,sortServer:w,onSort:f,persistTableHead:P,sortFunction:_,...j}=e,C={paginationComponentOptions:s,noDataComponent:r("NoData"),noHeader:!0,columns:a,data:p||[],dense:!0,paginationResetDefaultPage:u,subHeader:l,progressPending:A,subHeaderComponent:c,pagination:!0,paginationServer:S,paginationPerPage:h||10,paginationRowsPerPageOptions:g||[10,15,20,25,30],paginationTotalRows:d,onChangeRowsPerPage:m,onChangePage:x,selectableRows:y,onSelectedRowsChange:q,clearSelectedRows:v,progressComponent:(0,t.jsx)(o.A,{}),sortIcon:(0,t.jsx)("i",{className:"sort-icon fas fa-exchange-alt"}),sortServer:w,onSort:f,sortFunction:_,persistTableHead:P,className:"rki-table"};return(0,t.jsx)(i(),{...C})}p.defaultProps={subHeader:!1,pagination:!0,totalRows:null,pagServer:!0,onSelectedRowsChange:null,clearSelectedRows:!1,sortServer:!1,persistTableHead:!1};let d=p},57664:e=>{e.exports=require("dom-helpers/scrollbarSize")},58928:e=>{e.exports=require("dom-helpers/canUseDOM")},59717:e=>{e.exports=require("@restart/ui/DropdownContext")},60560:e=>{e.exports=require("dom-helpers/contains")},62969:(e,r,s)=>{s.a(e,async(e,t)=>{try{s.r(r),s.d(r,{default:()=>q,getServerSideProps:()=>S});var a=s(8732),i=s(7082),n=s(83551),o=s(49481),p=s(91353),d=s(19918),u=s.n(d),l=s(27053),c=s(79348),m=s(88751),x=s(35576),g=s(45927),h=s(14062),y=s(35557),A=e([c,h]);async function S({locale:e}){return{props:{...await (0,x.serverSideTranslations)(e,["common"])}}}[c,h]=A.then?(await A)():A;let q=e=>{let{t:r}=(0,m.useTranslation)("common"),s=()=>(0,a.jsxs)(i.A,{style:{overflowX:"hidden"},fluid:!0,className:"p-0",children:[(0,a.jsx)(n.A,{children:(0,a.jsx)(o.A,{xs:12,children:(0,a.jsx)(l.A,{title:r("adminsetting.syndrome.Syndromes")})})}),(0,a.jsx)(n.A,{children:(0,a.jsx)(o.A,{xs:12,children:(0,a.jsx)(u(),{href:"/adminsettings/[...routes]",as:"/adminsettings/create_syndrome",children:(0,a.jsx)(p.A,{variant:"secondary",size:"sm",children:r("adminsetting.syndrome.AddSyndrome")})})})}),(0,a.jsx)(n.A,{className:"mt-3",children:(0,a.jsx)(o.A,{xs:12,children:(0,a.jsx)(c.default,{})})})]}),t=(0,g.canAddSyndromes)(()=>(0,a.jsx)(s,{})),d=(0,h.useSelector)(e=>e);return d?.permissions?.syndrome?.["create:any"]?(0,a.jsx)(t,{}):(0,a.jsx)(y.default,{})};t()}catch(e){t(e)}})},63033:e=>{e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},65447:e=>{e.exports=require("@restart/ui/Button")},67364:e=>{e.exports=require("@restart/hooks/useCallbackRef")},68455:e=>{e.exports=import("redux-saga")},69722:e=>{e.exports=require("es6-promise")},74075:e=>{e.exports=require("zlib")},74716:e=>{e.exports=require("moment")},74987:e=>{e.exports=require("@restart/ui/ModalManager")},78097:e=>{e.exports=require("next-redux-saga")},78634:e=>{e.exports=require("@restart/ui/Anchor")},79348:(e,r,s)=>{s.a(e,async(e,t)=>{try{s.r(r),s.d(r,{default:()=>g});var a=s(8732),i=s(19918),n=s.n(i),o=s(82015),p=s(12403),d=s(91353),u=s(42893),l=s(56084),c=s(63487),m=s(88751),x=e([u,c]);[u,c]=x.then?(await x)():x;let g=e=>{let{t:r}=(0,m.useTranslation)("common"),[s,t]=(0,o.useState)([]),[,i]=(0,o.useState)(!1),[x,g]=(0,o.useState)(0),[h,y]=(0,o.useState)(10),[A,S]=(0,o.useState)(!1),[q,v]=(0,o.useState)({}),w=[{name:r("adminsetting.syndrome.Syndromes"),selector:"title"},{name:r("adminsetting.syndrome.Code"),selector:"code",cell:e=>e.code},{name:r("adminsetting.syndrome.Description"),selector:"description",cell:e=>e.description.replace(/<[^>]+>/g,"")},{name:r("adminsetting.syndrome.Action"),selector:"",cell:e=>(0,a.jsxs)("div",{children:[(0,a.jsx)(n(),{href:"/adminsettings/[...routes]",as:`/adminsettings/edit_syndrome/${e._id}`,children:(0,a.jsx)("i",{className:"icon fas fa-edit"})}),"\xa0",(0,a.jsx)("a",{onClick:()=>j(e),children:(0,a.jsx)("i",{className:"icon fas fa-trash-alt"})})," "]})}];(0,o.useEffect)(()=>{P()},[]);let f={sort:{title:"asc"},limit:h,page:1,query:{}},P=async()=>{i(!0);let e=await c.A.get("/syndrome",f);e&&e.data&&e.data.length>0&&(t(e.data),g(e.totalCount),i(!1))},_=async(e,r)=>{f.limit=e,f.page=r,i(!0);let s=await c.A.get("/syndrome",f);s&&s.data&&s.data.length>0&&(t(s.data),y(e),i(!1))},j=async e=>{v(e._id),S(!0)},C=async()=>{try{await c.A.remove(`/syndrome/${q}`),P(),S(!1),u.default.success(r("adminsetting.syndrome.Table.syndromeDeletedSuccessfully"))}catch(e){u.default.error(r("adminsetting.syndrome.Table.errorDeletingSyndrome"))}},D=()=>S(!1);return(0,a.jsxs)("div",{children:[(0,a.jsxs)(p.A,{show:A,onHide:D,children:[(0,a.jsx)(p.A.Header,{closeButton:!0,children:(0,a.jsx)(p.A.Title,{children:r("adminsetting.syndrome.Deletesyndrome")})}),(0,a.jsx)(p.A.Body,{children:r("adminsetting.syndrome.Areyousurewanttodeletethissyndrome?")}),(0,a.jsxs)(p.A.Footer,{children:[(0,a.jsx)(d.A,{variant:"secondary",onClick:D,children:r("adminsetting.syndrome.Cancel")}),(0,a.jsx)(d.A,{variant:"primary",onClick:C,children:r("adminsetting.syndrome.Yes")})]})]}),(0,a.jsx)(l.A,{columns:w,data:s,totalRows:x,pagServer:!0,handlePerRowsChange:_,handlePageChange:e=>{f.limit=h,f.page=e,P()}})]})};t()}catch(e){t(e)}})},80237:(e,r)=>{Object.defineProperty(r,"A",{enumerable:!0,get:function(){return s}});var s=function(e){return e.PAGES="PAGES",e.PAGES_API="PAGES_API",e.APP_PAGE="APP_PAGE",e.APP_ROUTE="APP_ROUTE",e.IMAGE="IMAGE",e}({})},81366:e=>{e.exports=require("redux-auth-wrapper/connectedAuthWrapper")},81413:(e,r)=>{Object.defineProperty(r,"M",{enumerable:!0,get:function(){return function e(r,s){return s in r?r[s]:"then"in r&&"function"==typeof r.then?r.then(r=>e(r,s)):"function"==typeof r&&"default"===s?r:void 0}}})},81521:e=>{e.exports=require("dom-helpers/querySelectorAll")},81895:e=>{e.exports=require("@restart/hooks/useEventCallback")},82015:e=>{e.exports=require("react")},86842:e=>{e.exports=require("@restart/ui/SelectableContext")},87571:e=>{e.exports=require("dom-helpers/transitionEnd")},88751:e=>{e.exports=require("next-i18next")},93787:e=>{e.exports=require("redux-persist")},94947:e=>{e.exports=require("@restart/hooks/useTimeout")},96196:e=>{e.exports=require("next-redux-wrapper")},98320:e=>{e.exports=require("@restart/hooks/useIsomorphicEffect")},99460:e=>{e.exports=require("dom-helpers/css")}};var r=require("../../webpack-runtime.js");r.C(e);var s=e=>r(r.s=e),t=r.X(0,[6089,9216,9616,2386],()=>s(47214));module.exports=t})();