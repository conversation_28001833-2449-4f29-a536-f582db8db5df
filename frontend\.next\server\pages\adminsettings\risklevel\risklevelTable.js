"use strict";(()=>{var e={};e.id=6962,e.ids=[636,3220,6962],e.modules={123:e=>{e.exports=require("dom-helpers/removeEventListener")},1332:e=>{e.exports=require("react-custom-scrollbars-2")},1428:e=>{e.exports=import("axios")},1680:e=>{e.exports=require("@restart/ui/utils")},1919:e=>{e.exports=require("react-transition-group/Transition")},3892:e=>{e.exports=require("classnames")},4048:e=>{e.exports=require("react-truncate")},6009:e=>{e.exports=require("dom-helpers/ownerDocument")},6952:e=>{e.exports=require("@restart/ui/Modal")},7374:e=>{e.exports=require("@restart/ui/Dropdown")},8732:e=>{e.exports=require("react/jsx-runtime")},9653:e=>{e.exports=require("@restart/ui/Overlay")},11242:e=>{e.exports=require("redux-persist/integration/react")},11688:e=>{e.exports=require("@restart/hooks/useWillUnmount")},12388:e=>{e.exports=require("dom-helpers/addEventListener")},13364:e=>{e.exports=require("redux-saga/effects")},14062:e=>{e.exports=import("react-redux")},14078:e=>{e.exports=import("swr")},14332:e=>{e.exports=require("uncontrollable")},16116:e=>{e.exports=require("invariant")},18622:e=>{e.exports=require("yup")},19121:e=>{e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},19544:(e,r,t)=>{t.a(e,async(e,s)=>{try{t.r(r),t.d(r,{default:()=>m});var a=t(8732),o=t(82015),i=t(19918),n=t.n(i),l=t(12403),u=t(91353),p=t(42893),d=t(56084),x=t(63487),c=t(88751),g=e([p,x]);[p,x]=g.then?(await g)():g;let m=e=>{let[r,t]=(0,o.useState)([]),[,s]=(0,o.useState)(!1),[i,g]=(0,o.useState)(0),[m,q]=(0,o.useState)(10),[h,v]=(0,o.useState)(!1),[P,f]=(0,o.useState)({}),{t:S}=(0,c.useTranslation)("common"),b={sort:{title:"asc"},limit:m,page:1,query:{}},k=[{name:S("Title"),selector:e=>e.title,sortable:!0},{name:S("adminsetting.RiskLevel.Level"),selector:e=>e.level,sortable:!0,cell:e=>e.level},{name:S("action"),selector:e=>e._id,sortable:!1,cell:e=>(0,a.jsxs)("div",{children:[(0,a.jsx)(n(),{href:"/adminsettings/[...routes]",as:`/adminsettings/edit_risklevel/${e._id}`,children:(0,a.jsx)("i",{className:"icon fas fa-edit"})}),"\xa0",(0,a.jsx)("a",{onClick:()=>y(e),children:(0,a.jsx)("i",{className:"icon fas fa-trash-alt"})})," "]})}],A=async()=>{s(!0);let e=await x.A.get("/risklevel",b);e&&e.data&&e.data.length>0&&(t(e.data),g(e.totalCount),s(!1))},w=async(e,r)=>{b.limit=e,b.page=r,s(!0);let a=await x.A.get("/risklevel",b);a&&a.data&&a.data.length>0&&(t(a.data),q(e),s(!1))},y=async e=>{f(e._id),v(!0)},j=async()=>{try{await x.A.remove(`/risklevel/${P}`),A(),v(!1),p.default.success(S("adminsetting.RiskLevel.Table.riskLevelDeletedSuccessfully"))}catch(e){p.default.error(S("adminsetting.RiskLevel.Table.errorDeletingRiskLevel"))}},M=()=>v(!1);return(0,o.useEffect)(()=>{A()},[]),(0,a.jsxs)("div",{children:[(0,a.jsxs)(l.A,{show:h,onHide:M,children:[(0,a.jsx)(l.A.Header,{closeButton:!0,children:(0,a.jsx)(l.A.Title,{children:S("adminsetting.RiskLevel.DeleteRisklevel")})}),(0,a.jsxs)(l.A.Body,{children:[S("adminsetting.RiskLevel.Areyousurewanttodeletethisrisklevel")," "]}),(0,a.jsxs)(l.A.Footer,{children:[(0,a.jsx)(u.A,{variant:"secondary",onClick:M,children:S("Cancel")}),(0,a.jsx)(u.A,{variant:"primary",onClick:j,children:S("yes")})]})]}),(0,a.jsx)(d.A,{columns:k,data:r,totalRows:i,pagServer:!0,handlePerRowsChange:w,handlePageChange:e=>{b.limit=m,b.page=e,A()}})]})};s()}catch(e){s(e)}})},22326:e=>{e.exports=require("react-dom")},22541:e=>{e.exports=require("redux-persist/lib/storage")},25303:e=>{e.exports=require("@restart/ui/NavItem")},26324:e=>{e.exports=require("warning")},27825:e=>{e.exports=require("lodash")},27910:e=>{e.exports=require("stream")},28217:e=>{e.exports=require("@restart/ui/DropdownItem")},29021:e=>{e.exports=require("fs")},29294:e=>{e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},29628:e=>{e.exports=require("dom-helpers/addClass")},29825:e=>{e.exports=require("prop-types")},29841:e=>{e.exports=require("dom-helpers/hasClass")},30362:e=>{e.exports=require("@restart/ui/DropdownToggle")},33873:e=>{e.exports=require("path")},36653:e=>{e.exports=require("nprogress")},36955:e=>{e.exports=require("@restart/hooks/useBreakpoint")},37766:e=>{e.exports=require("@restart/hooks/useMergedRefs")},38609:e=>{e.exports=require("react-data-table-component")},39756:e=>{e.exports=import("redux")},40051:e=>{e.exports=require("dom-helpers/removeClass")},40361:e=>{e.exports=require("next/dist/compiled/next-server/pages.runtime.prod.js")},42893:e=>{e.exports=import("react-hot-toast")},43294:e=>{e.exports=require("formik")},50009:e=>{e.exports=require("@restart/ui/DropdownMenu")},56084:(e,r,t)=>{t.d(r,{A:()=>u});var s=t(8732);t(82015);var a=t(38609),o=t.n(a),i=t(88751),n=t(30370);function l(e){let{t:r}=(0,i.useTranslation)("common"),t={rowsPerPageText:r("Rowsperpage")},{columns:a,data:l,totalRows:u,resetPaginationToggle:p,subheader:d,subHeaderComponent:x,handlePerRowsChange:c,handlePageChange:g,rowsPerPage:m,defaultRowsPerPage:q,selectableRows:h,loading:v,pagServer:P,onSelectedRowsChange:f,clearSelectedRows:S,sortServer:b,onSort:k,persistTableHead:A,sortFunction:w,...y}=e,j={paginationComponentOptions:t,noDataComponent:r("NoData"),noHeader:!0,columns:a,data:l||[],dense:!0,paginationResetDefaultPage:p,subHeader:d,progressPending:v,subHeaderComponent:x,pagination:!0,paginationServer:P,paginationPerPage:q||10,paginationRowsPerPageOptions:m||[10,15,20,25,30],paginationTotalRows:u,onChangeRowsPerPage:c,onChangePage:g,selectableRows:h,onSelectedRowsChange:f,clearSelectedRows:S,progressComponent:(0,s.jsx)(n.A,{}),sortIcon:(0,s.jsx)("i",{className:"sort-icon fas fa-exchange-alt"}),sortServer:b,onSort:k,sortFunction:w,persistTableHead:A,className:"rki-table"};return(0,s.jsx)(o(),{...j})}l.defaultProps={subHeader:!1,pagination:!0,totalRows:null,pagServer:!0,onSelectedRowsChange:null,clearSelectedRows:!1,sortServer:!1,persistTableHead:!1};let u=l},57664:e=>{e.exports=require("dom-helpers/scrollbarSize")},58928:e=>{e.exports=require("dom-helpers/canUseDOM")},59717:e=>{e.exports=require("@restart/ui/DropdownContext")},60560:e=>{e.exports=require("dom-helpers/contains")},63033:e=>{e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},65447:e=>{e.exports=require("@restart/ui/Button")},67364:e=>{e.exports=require("@restart/hooks/useCallbackRef")},67959:(e,r,t)=>{t.a(e,async(e,s)=>{try{t.r(r),t.d(r,{config:()=>q,default:()=>x,getServerSideProps:()=>m,getStaticPaths:()=>g,getStaticProps:()=>c,reportWebVitals:()=>h,routeModule:()=>k,unstable_getServerProps:()=>S,unstable_getServerSideProps:()=>b,unstable_getStaticParams:()=>f,unstable_getStaticPaths:()=>P,unstable_getStaticProps:()=>v});var a=t(63885),o=t(80237),i=t(81413),n=t(9616),l=t.n(n),u=t(72386),p=t(19544),d=e([u,p]);[u,p]=d.then?(await d)():d;let x=(0,i.M)(p,"default"),c=(0,i.M)(p,"getStaticProps"),g=(0,i.M)(p,"getStaticPaths"),m=(0,i.M)(p,"getServerSideProps"),q=(0,i.M)(p,"config"),h=(0,i.M)(p,"reportWebVitals"),v=(0,i.M)(p,"unstable_getStaticProps"),P=(0,i.M)(p,"unstable_getStaticPaths"),f=(0,i.M)(p,"unstable_getStaticParams"),S=(0,i.M)(p,"unstable_getServerProps"),b=(0,i.M)(p,"unstable_getServerSideProps"),k=new a.PagesRouteModule({definition:{kind:o.A.PAGES,page:"/adminsettings/risklevel/risklevelTable",pathname:"/adminsettings/risklevel/risklevelTable",bundlePath:"",filename:""},components:{App:u.default,Document:l()},userland:p});s()}catch(e){s(e)}})},68455:e=>{e.exports=import("redux-saga")},69722:e=>{e.exports=require("es6-promise")},74075:e=>{e.exports=require("zlib")},74716:e=>{e.exports=require("moment")},74987:e=>{e.exports=require("@restart/ui/ModalManager")},78097:e=>{e.exports=require("next-redux-saga")},78634:e=>{e.exports=require("@restart/ui/Anchor")},80237:(e,r)=>{Object.defineProperty(r,"A",{enumerable:!0,get:function(){return t}});var t=function(e){return e.PAGES="PAGES",e.PAGES_API="PAGES_API",e.APP_PAGE="APP_PAGE",e.APP_ROUTE="APP_ROUTE",e.IMAGE="IMAGE",e}({})},81366:e=>{e.exports=require("redux-auth-wrapper/connectedAuthWrapper")},81413:(e,r)=>{Object.defineProperty(r,"M",{enumerable:!0,get:function(){return function e(r,t){return t in r?r[t]:"then"in r&&"function"==typeof r.then?r.then(r=>e(r,t)):"function"==typeof r&&"default"===t?r:void 0}}})},81521:e=>{e.exports=require("dom-helpers/querySelectorAll")},81895:e=>{e.exports=require("@restart/hooks/useEventCallback")},82015:e=>{e.exports=require("react")},86842:e=>{e.exports=require("@restart/ui/SelectableContext")},87571:e=>{e.exports=require("dom-helpers/transitionEnd")},88751:e=>{e.exports=require("next-i18next")},93787:e=>{e.exports=require("redux-persist")},94947:e=>{e.exports=require("@restart/hooks/useTimeout")},96196:e=>{e.exports=require("next-redux-wrapper")},98320:e=>{e.exports=require("@restart/hooks/useIsomorphicEffect")},99460:e=>{e.exports=require("dom-helpers/css")}};var r=require("../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[6089,9216,9616,2386],()=>t(67959));module.exports=s})();