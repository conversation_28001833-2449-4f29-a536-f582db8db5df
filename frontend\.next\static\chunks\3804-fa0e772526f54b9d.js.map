{"version": 3, "file": "static/chunks/3804-fa0e772526f54b9d.js", "mappings": "qLA8FA,MAtFgB,KACd,GAAM,MAAEA,CAAI,CAAE,CAAGC,CAAAA,EAAAA,EAAAA,EAAAA,CAAcA,CAAC,EAqFnBC,OAAOA,CApFAF,CAoFC,CApFIG,QAAQ,EAAGH,EAAKG,QAAQ,CASjD,EAToD,CAS9C,CAACC,EAASC,EAAW,CAAGC,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAMC,CAP1CC,MAAO,GACPC,YAAa,GACbC,aAAc,GACdC,OAAQ,GACRC,WAAW,CACb,GAGM,CAACC,EAASC,EAAW,CAAGR,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,EAAC,GAKjCS,EAAoB,CACxBC,MAAO,CAAEN,aAAc,EAAG,EAC1BO,KAAM,CAAET,MAAO,KAAM,EACrBU,MAAO,GACT,EAEMC,EAAe,UAInB,IAAMC,EAAsB,MAAMC,IAElC,GADAP,GAAW,GACPM,EAAeE,MAAM,CAAG,EAAG,CAC7BP,EAAkBC,KAAK,CAACN,YAAY,CAAGU,EACvC,IAAMG,EAAW,MAAMC,EAAAA,CAAUA,CAACC,GAAG,CAAC,eAAgBV,GACtD,GAAIW,MAAMC,OAAO,CAACJ,EAASK,IAAI,GAAKL,EAASK,IAAI,CAACN,MAAM,CAAG,EAAG,CAC5D,IAAMO,EAAkBN,EAASK,IAAI,CAACL,EAASK,IAAI,CAACN,MAAM,CAAG,EAAE,CAC/DO,EAAgBlB,MAAM,CACpBY,GACAM,EAAgBlB,MAAM,CAACW,MAAM,CAAG,IACF,IAA9BO,EAAgBjB,SAAS,CACrBiB,EAAgBlB,MAAM,CAACmB,GAAG,CAAC,CAACC,EAAWC,IACrCC,OAAO,GAAwCF,MAAAA,CAArCG,8BAAsB,CAAC,gBAAuB,OAATH,EAAKI,GAAG,IAEzD,mBACFN,EAAgBpB,WAAW,CAC7Bc,GACAM,EAAgBpB,WAAW,CAACa,MAAM,CAAG,GACrCO,OAAgBjB,SAAS,CACrBiB,EAAgBpB,WAAW,CArBnC,EAsBQ2B,waACN/B,EAAWwB,GACXR,IACAP,GAAW,EACb,CACF,CACF,EAEMO,EAAoB,UACxB,IAAME,EAAW,MAAMC,EAAAA,CAAUA,CAACC,GAAG,CAAC,gBAAiB,CACrDT,MAAO,CAAER,MAAO,SAAU,CAC5B,SACA,EAAIe,KAAYA,EAASK,IAAI,EAAIL,EAASK,IAAI,CAACN,MAAM,CAAG,GAAG,EACzBM,IAAI,CAAC,EAAE,CAACO,GAAG,EAM/CE,CAAAA,EAAAA,EAAAA,SAAAA,CAASA,CAAC,KACRlB,GACF,EAAG,EAAE,EAEL,IAAMmB,EAAOlC,EAAQK,WAAW,CAAC8B,OAAO,CAAC,WAAY,KAErD,MACE,UAACC,MAAAA,CAAIC,UAAU,oBACA,IAAZ5B,EACC,UAAC6B,EAAAA,CAAeA,CAAAA,CAAAA,GAEhB,WAACF,MAAAA,WACC,UAACG,MAAAA,CAAIF,UAAU,UAAUG,IAAKxC,EAAQO,MAAM,CAAEkC,IAAI,KAClD,UAACL,MAAAA,CAAIM,wBAjEJ,CAAEC,OAiEwCT,CAjEhCU,IAiE8C,QAKnE,+FC3Ee,SAASC,EAAiBC,CAA4B,EAEnE,GAAM,MAACnB,CAAI,CAAC,CAAGmB,EASTC,EAAYpB,EAAKpB,MAAM,CAACW,MAAM,CAAE,EAChC8B,EAAS,UAAoB,OAAVrB,EAAKsB,IAAI,EAElC,MACE,WAACC,EAAAA,CAAGA,CAAAA,CAACb,UAAU,MAAMc,GAAI,aACvB,UAACC,IAAIA,CAACC,KAAM,IAAc,OAAV1B,EAAKsB,IAAI,CAAC,gBAAeK,GAAI,EAAxCF,EAA8DzB,MAAAA,CAAlBA,EAAKsB,IAAI,CAAC,UAA+BtB,MAAAA,CAAvBA,CAAI,CAACqB,EAAO,CAAC,YAAmB,OAATrB,EAAKI,GAAG,WAE/F,EAAMxB,MAAM,EAAIoB,EAAKpB,MAAM,CAACwC,EAAS,CACpC,UAACR,MAAAA,CAAIC,IAAK,GAAwCb,MAAAA,CAArCG,8BAAsB,CAAC,gBAAwC,OAA1BH,EAAKpB,MAAM,CAACwC,EAAS,CAAChB,GAAG,EAAIU,IAAI,eAC9EJ,UAAU,gBACb,UAACkB,IAAAA,CAAElB,UAAU,iCAGnB,WAACD,MAAAA,CAAIC,UAAU,yBACb,UAACe,IAAIA,CAACC,KAAM,IAAc,OAAV1B,EAAKsB,IAAI,CAAC,gBAAeK,GAAI,EAAxCF,EAA8DzB,MAAAA,CAAlBA,EAAKsB,IAAI,CAAC,UAA+BtB,MAAAA,CAAvBA,CAAI,CAACqB,EAAO,CAAC,YAAmB,OAATrB,EAAKI,GAAG,WAC/FJ,GAAQA,EAAKvB,KAAK,CAAGuB,EAAKvB,KAAK,CAAG,KAErC,UAACoD,IAAAA,UACE7B,GAAQA,EAAKtB,WAAW,CAAGoD,CAzBVC,IACxB,IAAMtB,EAAMuB,SAASC,aAAa,CAAC,OACnCxB,EAAIyB,SAAS,CAAGH,EAChB,IAAMI,EAAS1B,EAAI2B,WAAW,EAAI3B,EAAI4B,SAAS,EAAI,GACnD,OAAQF,EAAO5C,MAAM,CArBF,EAqBK+C,EAAiB,GAA2C,OAAxCH,EAAOI,SAAS,CAAC,EAAGD,KAAoB,OAAOH,EAC7F,EAoBqDnC,CArB8B,CAqBzBtB,WAAW,EAAI,YAK3E,+JCnCA,SAAS8D,EAAUrB,CAAqB,EACtC,GAAM,MAAEsB,CAAI,CAAE,CAAGtB,SACjB,EAAS5B,MAAM,CAAG,EAEd,CAFiB,EAEjB,OAACmD,EAAAA,CAASA,CAAAA,UACPD,EAAK1C,GAAG,CAAC,CAACC,EAAW2C,IAElB,UAACD,EAAAA,CAASA,CAACE,IAAI,WAEb,UAACnB,IAAIA,CAACC,KAAK,uBAAuBC,GAAI,aAAjCF,IAA2D,OAATzB,EAAKI,GAAG,WAE5DJ,EAAKvB,KAAK,IAHRkE,MAYV,IACT,CASA,SAASE,EAAY1B,CAAuB,EAC1C,GAAM,SAAE2B,CAAO,CAAE,CAAG3B,EACpB,MACE,UAACM,IAAIA,CACHC,KAAK,uBACLC,GAAI,aAFDF,IAE6B,OAAXqB,EAAQC,EAAE,EAC/BrC,UAAU,6BAEV,UAACsC,OAAAA,CAAKtC,UAAU,8BAAsBoC,EAAQG,IAAI,IAIxD,CA+EA,MAxEA,SAASC,CAA2C,EAClD,GAAM,CAACC,CAAC,WAuEKD,WAvEHE,CAAoB,CAAC,CAAGjC,CAuEN+B,CAtEtBG,CAsEuB,CAtEVF,EAAE,mBACf,CAACL,EAASQ,EAAW,CAAG/E,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAA4C,CAAE0E,KAAM,GAAIF,GAAI,GAAIN,KAAM,EAAE,GACxG,CAAC3D,EAASC,EAAW,CAAGR,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,EAAC,GAEjCgF,EAAiB,KACrBD,EAAW,CAAEL,KAAME,EAAE,sBAAuBJ,GAAI,GAAIN,KAAM,EAAE,EAC9D,EAEMe,EAAgB,UACpB,IAAMC,EAAgB,CACpBxE,MAAO,CAAEyE,OAAQ,EAAE,EACnBxE,KAAM,CAAEyE,WAAY,MAAO,EAC3BxE,MAAO,GACPyE,OAAQ,sRACV,EACMC,EAAgB,MAAMC,IAC5B,GAAID,EAAU,CACZJ,EAAcxE,KAAK,CAACyE,MAAM,CAAGG,EAE7B,GAAI,CACF9E,GAAW,GACX,IAAMgF,EAAW,MAAMtE,EAAAA,CAAUA,CAACC,GAAG,CAAC,WAAY+D,GAClD1E,GAAW,GACPY,MAAMC,OAAO,CAACmE,EAASlE,IAAI,GAAKkE,EAASlE,IAAI,CAACN,MAAM,CAAG,GAAG,EACjD,CAAE0D,KAAMc,EAASlE,IAAI,CAAC,EAAE,CAACpB,KAAK,CAAEsE,GAAIgB,EAASlE,IAAI,CAAC,EAAE,CAACO,GAAG,CAAEqC,KAAMsB,EAASlE,IAAI,GACxFuD,EAAqBW,EAASlE,IAAI,GAElC0D,GAEJ,CAAE,MAAOS,EAAG,CACVT,GACF,CACF,MACEA,CADK,EAGT,EAEMO,EAAqB,UACzB,IAAMtE,EAAW,MAAMC,EAAAA,CAAUA,CAACC,GAAG,CAAC,kBACtC,GAAIF,GAAYA,EAASK,IAAI,EAAIL,EAASK,IAAI,CAACN,MAAM,CAAG,EAAG,CACzD,IAAMsE,EAAkB,EAAE,CAM1B,OALAI,IAAAA,OAAS,CAACzE,EAASK,IAAI,CAAE,SAAUG,CAAS,EACvB,WAAW,CAA1BA,EAAKvB,KAAK,EACZoF,EAASK,IAAI,CAAClE,EAAKI,GAAG,CAE1B,GACOyD,CACT,CACA,OAAO,CACT,EAEAvD,CAAAA,EAAAA,EAAAA,SAAAA,CAASA,CAAC,KACRkD,GACF,EAAG,EAAE,EAEL,IAAMf,EAAO,CACX0B,QAASd,EACTJ,KAAM,UAACT,EAAAA,CAAUC,KAAMK,EAAQL,IAAI,EACrC,EAEA,MACE,UAAC2B,EAAAA,CAAOA,CAAAA,CACNC,gBAAiB,uBACjB5B,KAAMA,EACN6B,OAAQjB,EACRJ,KAAMnE,EAAU,UAAC6B,EAAAA,CAAeA,CAAAA,CAAAA,GAAM,UAACkC,EAAAA,CAAYC,QAASA,KAGlE,yMC/HA,IAAMyB,EAAYC,CAAAA,EAAAA,EAAAA,EAAAA,CAAeA,CAACC,KA0BlC,SAASC,EAAYvD,CAAuB,EAC1C,GAAM,CAAEgC,CAAC,MAAElF,CAAI,CAAE,CAAGC,CAAAA,EAAAA,EAAAA,EAAAA,CAAcA,CAAC,IA3BGuG,MA4BhCE,EAAc1G,EAAKG,QAAQ,CAC3B,YAAEwG,CAAU,OAAEC,CAAK,cAAEC,CAAY,OAAEC,CAAK,CAAE,CAAG5D,EAC/C6D,EAAyB,CAAC,EAc9B,OAbI7D,EAAM8D,eAAe,EAAE,GACL,CAClBC,aAAcC,EACdC,MAAO,CACLC,WAAY,GACT,UAACC,EAAAA,CAAWV,WAAYA,EAAa,GAAGW,CAAW,EACxD,EACF,EAEET,IACFE,EAAkBQ,OAAO,CADT,CACYC,EAI5B,UAACC,EAAAA,EAAQA,CAAAA,CACPC,QAAShB,EACTJ,UAAWA,EACXqB,OAAQhB,EACRG,MAAOA,EACPc,cAAc,aACdC,YAAY,WACZjB,MAAOA,EACPkB,WAAYf,EACZgB,SAAU,CAACC,MAAM9C,EAAE,SAAS+C,SAAS/C,EAAE,QAAQgD,KAAKhD,EAAE,QAAQiC,MAAMjC,EAAE,SAASiD,KAAKjD,EAAE,QAAQkD,IAAIlD,EAAE,MAAM,EAC1GmD,cAAgBC,IACd,IAAMC,EAAaC,OAAOC,IAAI,CAACH,GAC5BI,MAAM,CAAC,GAAU3G,EAAK4G,QAAQ,CAAC,WAC/BC,QAAQ,GACLC,EAAYN,EAAWO,KAAK,CAAC,IAAI,CAAC,EAAE,CAC1CC,IAAAA,IAAW,CACT,IAAsBT,MAAAA,CAAlBO,EAAU,UAAoCP,MAAAA,CAA5BA,CAAK,CAACC,EAAW,CAAC,YAAoB,OAAVD,EAAMnG,GAAG,EAE/D,GAGN,CAOA,SAASqF,EAAetE,CAAmB,EACzC,MACE,UAAC8F,EAAAA,CAASA,CAAAA,CAACvG,UAAU,gBACnB,WAACwG,EAAAA,CAAGA,CAAAA,WACF,UAAC3F,EAAAA,CAAGA,CAAAA,CAACb,UAAU,MAAMyG,GAAI,WACvB,UAACvF,IAAAA,CACCiD,MAAO,CAAEuC,OAAQ,SAAU,EAC3BC,QAAS,IAAMlG,EAAMmG,UAAU,CAAC,QAChC5G,UAAY,0BAGhB,UAACa,EAAAA,CAAGA,CAAAA,CAACb,UAAU,cAAcyG,GAAI,YAC/B,UAACnE,OAAAA,CAAKtC,UAAU,6BAAqBS,EAAMoG,KAAK,KAElD,UAAChG,EAAAA,CAAGA,CAAAA,CAACb,UAAU,eAAeyG,GAAI,WAChC,UAACvF,IAAAA,CACCiD,MAAO,CAAEuC,OAAQ,SAAU,EAC3BC,QAAS,IAAMlG,EAAMmG,UAAU,CAAC,QAChC5G,UAAY,+BAMxB,CA7BAgE,EAAY8C,YAAY,CAAG,CACzB1C,cAAc,EACdC,MAAO,CAAC,QAAQ,EA8BlB,IAAM0C,EAAiB,CAAC7C,EAA6B8C,KACnD,IAAIC,EAAc,EAoBlB,OAnBA1D,IAAAA,OAAS,CAACW,EAAY,IACpB,IAAMgD,EAAYnD,IAAOT,EAAE6D,UAAU,EAAEC,GAAG,CAAC,CACzCC,KAAM,EACNC,OAAQ,EACRC,MAHsBxD,CAGd,EACRyD,YAAa,CACf,GACMC,EAAU1D,IAAOT,EAAEoE,QAAQ,EAAEN,GAAG,CAAC,CACrCC,KAAM,EACNC,OAAQ,EACRC,OAAQ,CAHYxD,CAIpByD,YAAa,CACf,EAGIG,CADY5D,IAAOiD,GAAMY,CAChB,QADyB,CAACV,EAAWO,EAAS,KAAM,QAE/DR,GAAe,EAEnB,GACOA,CACT,EAN0BlD,EAQP,OAAC,MAAEiD,CAAI,OAAEH,CAAK,YAAE3C,CAAU,CAA8D,GACnG+C,EAAcF,EAAe7C,EAAY8C,GACzCa,EAAmB9D,IAAOiD,GAAMc,QAAQ,CAAC,IAAIC,KAAQ,OAE3D,MACE,OAH6BhE,EAG7B,EAAChE,MAAAA,CACCC,UAAU,gBACV2G,QAAS,IAAML,IAAAA,IAAW,CAAC,8BAE3B,UAAC0B,IAAAA,CAAEhH,KAAK,aAAK6F,IACZI,EAAc,GACb,WAAC3E,OAAAA,CAAKtC,UAAU,qEACd,UAACiI,EAAAA,CAAeA,CAAAA,CACdC,KAAMC,EAAAA,EAAMA,CACZC,MAAOP,EAAmB,OAAS,UACnCQ,KAAK,OAEP,UAAC/F,OAAAA,CAAKtC,UAAU,sBAAciH,SAKxC,EAEMxC,EAAe,GACZ,UAAC1E,MAAAA,CAAIuI,SAAU7H,EAAM6H,QAAQ,GAGtC,EAAetE,WAAWA,EAAC,oJC/I3B,SAASuE,EAAuB9H,CAAkC,EAChE,GAAM,eAAE+H,CAAa,CAAE,CAAG/H,EAC1B,MACE,UAACV,MAAAA,UACEyI,EAAcnJ,GAAG,CAAC,CAACC,EAAW2C,IAE3B,UAACuE,EAAAA,CAAGA,CAAAA,CAACxG,UAAU,4BACb,UAACQ,EAAAA,OAAgBA,CAAAA,CAAClB,KAAMA,KADa2C,KAOjD,CAmHA,MA7GA,SAASwG,CAAqC,KAAxB,CAAEhG,CAAC,CAAqB,CAAxB,EACd,CAAC+F,EAAeE,EAAiB,CAAG7K,CA4G7B4K,EA5G6B5K,EAAAA,QAAAA,CAAQA,CAAU,EAAE,EAExD,CAAC8K,EAAQC,EAAU,CAAG/K,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAC,GAE/B,CAACgL,EAAkB,CAAGhL,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAC,GAE/BgF,EAAiB,KACrB6F,EAAiB,EAAE,CACrB,EAEMI,EAAgB,CACpBvK,MAAO,CAAEwK,sBAAsB,CAAK,EACpCvK,KAAM,CAAEyE,WAAY,MAAO,EAC3BxE,MAAO,EACPyE,OAAQ,iHACV,EAEM8F,EAAqB,qBAAOC,EAAAA,UAAAA,MAAAA,CAAAA,GAAAA,KAAAA,IAAAA,SAAAA,CAAAA,EAAAA,CAAAA,SAAAA,CAAAA,EAAAA,CAASH,EACnChK,EAAW,MAAMC,EAAAA,CAAUA,CAACC,GAAG,CAAC,WAAYiK,GAC9CnK,GAAYA,EAASK,IAAI,EAAIL,EAASK,IAAI,CAACN,MAAM,CAAG,EAEtD6J,CAFyD,CACvCnF,IAAAA,KAAO,CAACzE,EAASK,GAClB+J,CADsB,CAAE,IAGzCrG,GAEJ,EAEAjD,CAAAA,EAAAA,EAAAA,SAAAA,CAASA,CAAC,KACRoJ,GACF,EAAG,EAAE,EAEL,IAAMG,EAAiB,IACrB,IAAIlH,EAAQ0G,EACN,CAACS,EAAMC,EAAI,CAAG,CAAC,EAAGR,EAAoB,EAAE,CAE5B,SAAdS,GAAwBrH,EAAQoH,EAClCpH,GADuC,CAGlB,SAAdqH,GAAwBrH,EAAQ,GAAI,IAKzC,EAAepD,MAAM,CAAGoD,GAAW,GAAG,CACxCA,EAAQuG,EAAc3J,MAAM,EAAG,EAG7B,EAAeA,MAAM,CAAGoD,GAAW,GAAG,CACxCA,EAAQ,GAEV2G,EAAU3G,EACZ,EAGA,MACE,UAAClC,MAAAA,CAAIC,UAAU,yBACZwI,GAAiBA,EAAc3J,MAAM,CAAG,EACvC,iCACE,UAAC0H,EAAAA,CAASA,CAAAA,CAACgD,KAAK,aACd,WAAC/C,EAAAA,CAAGA,CAAAA,WACF,UAAC3F,EAAAA,CAAGA,CAAAA,CAACC,GAAI,GAAId,UAAU,eACrB,UAACwJ,KAAAA,UAAI/G,EAAE,qBAER+F,GAAiBA,EAAc3J,MAAM,CAAG,EACvC,UAACgC,EAAAA,CAAGA,CAAAA,CAACC,GAAI,EAAGd,UAAU,yCACpB,WAACD,MAAAA,CAAIC,UAAU,gCACb,UAACgI,IAAAA,CAAEhI,UAAU,wBAAwB2G,QAAS,IAAMwC,EAAe,iBACjE,UAACjI,IAAAA,CAAElB,UAAU,yBAEf,UAACgI,IAAAA,CAAEhI,UAAU,yBAAyB2G,QAAS,IAAMwC,EAAe,iBAClE,UAACjI,IAAAA,CAAElB,UAAU,+BAIjB,UAGR,UAACuG,EAAAA,CAASA,CAAAA,CAACgD,KAAK,aACd,UAAC/C,EAAAA,CAAGA,CAAAA,UACF,UAAC3F,EAAAA,CAAGA,CAAAA,CAACC,GAAI,GAAId,UAAU,eACrB,UAACyJ,EAAAA,CAAQA,CAAAA,CAACC,YAAY,EAAOC,SAAU,GAAOC,SAAU,KAAMC,YAAalB,WACxEH,EAAcnJ,GAAG,CAAC,CAACC,EAAW2C,IAE3B,UAACwH,EAAAA,CAAQA,CAACvH,IAAI,WACZ,UAACqG,EAAAA,CAAuBC,cAAelJ,KADrB2C,eAWhC,UAACsE,EAAAA,CAASA,CAAAA,CAACgD,OAAO,WAChB,WAAC/C,EAAAA,CAAGA,CAAAA,WACF,UAAC3F,EAAAA,CAAGA,CAAAA,CAACC,GAAI,GAAId,UAAU,eACrB,UAACwJ,KAAAA,UAAI/G,EAAE,qBAET,WAAC5B,EAAAA,CAAGA,CAAAA,CAACC,GAAI,GAAId,UAAU,gBACrB,UAACD,MAAAA,CAAIC,UAAU,sBAAcyC,EAAE,uCAA2C,UAACqH,KAAAA,CAAAA,YAM3F,qNCDA,MAAeC,CAAAA,EAAAA,EAAAA,EAAAA,CAAOA,CAAC,GAAgBC,GAhHvC,IAgH8CC,KAhHrCA,CAAqC,IAgHUA,CAhHrC,CAAExH,CAAC,CAAEyH,MAAI,CAAkB,CAA3B,EACX,CAACC,EAAaC,EAAY,CAAGvM,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAC,IACtC,CAACwM,EAAmBC,EAAqB,CAAGzM,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAQ,EAAE,EAC9D,CAAC0M,EAAiBC,EAAmB,CAAG3M,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAQ,EAAE,EAC1D,CAAC4M,EAAkBC,EAAqB,CAAG7M,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAE,YACrD,CAAC8M,EAAYC,EAAc,CAAG/M,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAM,IAE5CgN,EAAgB,UACpB,IAAM1L,EAAO,MAAMJ,EAAAA,CAAUA,CAAC+L,IAAI,CAAC,uBAAwB,CAAC,EACzD3L,QAAAA,EAAAA,KAAAA,EAAAA,EAAM4L,MAAN5L,QAAM4L,KAAmB,GAAS5L,OAAAA,EAAAA,KAAAA,EAAAA,EAAM6L,MAAN7L,GAAM6L,KAAc,EACvDN,EAAqB,GADyC,UAG1DvL,QAAAA,KAAAA,EAAAA,EAAM6D,MAAM,EAAE,EACAiI,SAUXA,CAAqC,EAC5C,OAAQjI,GACN,IAAK,kBAAmB,MAAO,sBAC/B,KAAK,WAAY,MAAO,qBAC1B,CACF,EAf2C7D,EAAK6D,MAAM,UAEvC7D,EAAAA,KAAAA,EAAAA,EAAM+L,MAAN/L,OAAM+L,EAEfN,EADgBO,SAefA,CAAiC,EAdpBnI,OAeZA,GACN,IAAK,kBAAmB,MAAO,kBAC/B,KAAK,WAAY,MAAO,iBAE1B,CACF,EArB2C7D,EAAK+L,aAAa,GAGrDN,EAAc,wBAUtB,EAuBA,MAdAhL,CAAAA,EAAAA,EAAAA,SAAAA,CAASA,CAAC,KACLsK,EAAKa,cAAc,EAAC,EACY,YAAfb,EAAKlH,KACJoI,CADU,CAAiB,WAAY,WAG3DlB,EAAKc,SAAS,EAAC,EAC0B,YAAtBd,EAAKgB,KACJE,QADiB,CAAiB,WAAY,WAInEhB,EADkB,GAASF,EAAKmB,KACpBC,GAD4B,CAAIpB,EAAKmB,QAAQ,CAAG,IAE5DR,GACF,EAAG,CAACX,EAAK,EAGP,+BACCO,cACC,WAAClE,EAAAA,CAASA,CAAAA,CAACgD,OAAO,EAAMvJ,UAAU,gCAClC,WAACuL,KAAAA,WAAI9I,EAAE,SAAS,IAAE0H,KAClB,WAAC5D,EAAAA,CAASA,CAAAA,CAACgD,OAAO,YAChB,WAAC/C,EAAAA,CAAGA,CAAAA,WACF,WAAC3F,EAAAA,CAAGA,CAAAA,CAACb,UAAU,wBAAwBwL,GAAG,cACxC,UAAChF,EAAAA,CAAGA,CAAAA,UACF,UAAC3F,EAAAA,CAAGA,CAAAA,CAACC,GAAG,cACN,UAACrD,EAAAA,OAAOA,CAAAA,CAAAA,OAGZ,UAAC+I,EAAAA,CAAGA,CAAAA,UACF,UAAC3F,EAAAA,CAAGA,CAAAA,CAACC,GAAI,YACP,WAAC0F,EAAAA,CAAGA,CAAAA,WACJ,UAAC3F,EAAAA,CAAGA,CAAAA,CAAC4F,GAAG,IAAIzG,UAAU,wBACpB,UAACyL,EAAAA,OAAiBA,CAAAA,CAAChJ,EAAGA,EAAGiJ,uBAAwBpB,MAEnD,UAACzJ,EAAAA,CAAGA,CAAAA,CAAC4F,GAAG,IAAIzG,UAAU,wBACpB,UAACwC,EAAAA,OAAeA,CAAAA,CAACC,EAAGA,EAAGC,qBAAsB8H,gBAMrD,UAAC3J,EAAAA,CAAGA,CAAAA,CAACb,UAAU,6BAA6BwL,GAAG,aAC7C,UAACG,EAAAA,OAAcA,CAAAA,CAAAA,QAGnB,UAACnF,EAAAA,CAAGA,CAAAA,UACJ,UAAC3F,EAAAA,CAAGA,CAAAA,CAACb,UAAU,SAASc,GAAG,cACvB,UAAC2H,EAAAA,OAAYA,CAAAA,CAAChG,EAAGA,QAGrB,UAAC+D,EAAAA,CAAGA,CAAAA,UACJ,UAAC3F,EAAAA,CAAGA,CAAAA,CAACb,UAAU,SAASc,GAAI,YAC1B,UAAC8K,EAAAA,OAAuBA,CAAAA,CAACnJ,EAAGA,EAAG8H,gBAAiBA,EAAiBF,kBAAmBA,cAKtF,WAACtK,MAAAA,CAAIC,UAAU,uBAAa,UAACuL,KAAAA,UAAG,sCAClC,UAACxL,MAAAA,UACD,UAAC8L,KAAAA,CAAG7L,UAAU,sBAAcyC,EAAEkI,WAMpC,+DChIe,SAAS1K,IACtB,MACE,WAAC6L,EAAAA,EAAaA,CAAAA,CACZC,QAAQ,aACRC,OAAQ,GACRC,MAAO,IACPC,MAAO,EACPnO,MAAO,UACPoO,gBAAgB,UAChBC,gBAAgB,UAChBC,UAAW,sBAEX,UAACC,OAAAA,CAAKC,EAAE,KAAKC,EAAE,IAAIC,GAAG,IAAIC,GAAG,IAAIT,MAAM,MAAMD,OAAO,OACpD,UAACM,OAAAA,CAAKC,EAAE,KAAKC,EAAE,KAAKC,GAAG,IAAIC,GAAG,IAAIT,MAAM,MAAMD,OAAO,SAG3D,qHC6BA,MArCA,SAASL,CAA0C,EAEjD,GAAM,CAACzG,EAAQyH,EAAU,CAAG9O,CAAAA,EAAAA,EAAAA,EAmCf8N,MAnCe9N,CAAQA,CAAQ,EAAE,EAExC+O,EAAkB,EAiCI,QAhC1B,IAAM9N,EAAW,MAAMC,EAAAA,CAAUA,CAACC,GAAG,CAAC,cAAe,CAAC,MAAS,CAAC,MAAS,gBAAgB,CAAC,GACtFF,GAAYA,EAASK,IAAI,EAAIL,EAASK,IAAI,CAACN,MAAM,CAAG,GAAI,EACtCC,EAASK,IAAI,CAAC,EAAE,CAACO,GAAG,CAE5C,EAEMmN,EAAsB,MAAOC,IAQjC,IAAMhO,EAAW,MAAMC,EAAAA,CAAUA,CAACC,GAAG,CAAC,WAPT,CAOqB+N,MANzC,CAACC,YAAaF,CAAY,EACjCtO,KAAM,CAACyE,WAAY,MAAO,EAC1BxE,MAAO,GACPyE,OAAQ,yHACV,GAGIpE,GAAYA,EAASK,IAAI,EAAE,CAC7BoE,IAAAA,OAAS,CAACzE,EAASK,IAAI,CAAE,SAAS8N,CAAG,CAAE/L,CAAC,EACtCpC,EAASK,IAAI,CAAC+B,EAAE,CAACgM,MAAM,EAAG,CAC5B,GACAP,EAAU7N,EAASK,IAAI,EAE3B,EAMA,MAJAS,CAAAA,EAAAA,EAAAA,SAAAA,CAASA,CAAC,KACRgN,GACF,EAAE,EAAE,EAGF,UAAC5I,EAAAA,CAAWA,CAAAA,CAACE,WAAYgB,EAAQd,YAAY,IAACG,eAAe,KAEjE,+JC/BA,SAASzC,EAAUrB,CAAqB,EACtC,GAAM,MAAEsB,CAAI,CAAE,CAAGtB,SACjB,EAAS5B,MAAM,CAAG,EAEd,UAACmD,EAAAA,CAASA,CAAAA,UACPD,EAAK1C,GAAG,CAAC,CAACC,EAAW2C,IAElB,UAACD,EAAAA,CAASA,CAACE,IAAI,WAEb,UAACnB,IAAIA,CAACC,KAAK,yBAAyBC,GAAI,WAAnCF,QAA+D,OAATzB,EAAKI,GAAG,WAEhEJ,EAAKvB,KAAK,IAHRkE,MAYV,IACT,CASA,SAASE,EAAY1B,CAAuB,EAC1C,GAAM,WAAE0M,CAAS,CAAE,CAAG1M,EACtB,MACE,UAACM,IAAIA,CACHC,KAAK,yBACLC,GAAI,WAFDF,QAEiC,OAAboM,EAAU9K,EAAE,EACnCrC,UAAU,6BAEV,UAACsC,OAAAA,CAAKtC,UAAU,8BAAsBmN,EAAU5K,IAAI,IAI1D,CAgFA,MAzEA,SAASkJ,CAA+C,EACtD,GAAM,CAAChJ,CAAC,WAwEKgJ,aAxEHC,CAAsB,CAAC,CAAGjL,CAwENgL,CAvExB9I,CAuEyB,CAvEZF,EAAE,qBACf,CAAC0K,EAAWC,EAAa,CAAGvP,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAA4C,CAAE0E,KAAM,GAAIF,GAAI,GAAIN,KAAM,EAAE,GAC5G,CAAC3D,EAASC,EAAW,CAAGR,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAC,IAEjCgF,EAAiB,KACrBuK,EAAa,CAAE7K,KAAME,EAAE,wBAAyBJ,GAAI,GAAIN,KAAM,EAAG,EACnE,EAEMsL,EAAiB,UACrB,IAAMC,EAAkB,CACtB/O,MAAO,CAAEyE,OAAQ,EAAE,EACnBxE,KAAM,CAAEyE,WAAY,MAAO,EAC3BxE,MAAO,GACPyE,OAAQ,sLACV,EACMC,EAAgB,MAAMoK,IAE5B,GAAIpK,EAAU,CACZmK,EAAgB/O,KAAK,CAACyE,MAAM,CAAGG,EAC/B,GAAI,CACF9E,EAAW,IACX,IAAMmP,EAAa,MAAMzO,EAAAA,CAAUA,CAACC,GAAG,CAAC,aAAcsO,GACtDjP,GAAW,GACPY,MAAMC,OAAO,CAACsO,EAAWrO,IAAI,GAAKqO,EAAWrO,IAAI,CAACN,MAAM,CAAG,GAC7DuO,EAAa,CAAE7K,KAAMiL,EAAWrO,IAAI,CAAC,EAAE,CAACpB,KAAK,CAAEsE,GAAImL,EAAWrO,IAAI,CAAC,EAAE,CAACO,GAAG,CAAEqC,KAAMyL,EAAWrO,IAAI,GAChGuM,EAAuB8B,EAAWrO,IAAI,GAEtC0D,GAEJ,CAAE,MAAOS,EAAG,CACVT,GACF,CACF,MACEA,CADK,EAIT,EAEM0K,EAAuB,UAC3B,IAAMzO,EAAW,MAAMC,EAAAA,CAAUA,CAACC,GAAG,CAAC,qBACtC,GAAIF,GAAYA,EAASK,IAAI,EAAIL,EAASK,IAAI,CAACN,MAAM,CAAG,EAAG,CACzD,IAAMsE,EAAkB,EAAE,CAM1B,OALAI,IAAAA,OAAS,CAACzE,EAASK,IAAI,CAAE,SAAUG,CAAS,EACxB,WAAdA,EAAKvB,KAAK,EACZoF,EAASK,IAAI,CAAClE,EAAKI,GAAG,CAE1B,GACOyD,CACT,CACA,OAAO,CACT,EAEAvD,CAAAA,EAAAA,EAAAA,SAAAA,CAASA,CAAC,KACRyN,GACF,EAAG,EAAE,EAEL,IAAMtL,EAAO,CACX0B,QAASd,EACTJ,KAAM,UAACT,EAAAA,CAAUC,KAAMoL,EAAUpL,IAAI,EACvC,EAEA,MACE,UAAC2B,EAAAA,CAAOA,CAAAA,CACNC,gBAAiB,uBACjB5B,KAAMA,EACN6B,OAAQjB,EACRJ,KAAMnE,EAAU,UAAC6B,EAAAA,CAAeA,CAAAA,CAAAA,GAAM,UAACkC,EAAAA,CAAYgL,UAAWA,KAGpE,oFCzHA,SAASM,EAAUhN,CAAqB,EACtC,GAAM,MAAEsB,CAAI,iBAAE4B,CAAe,CAAE,CAAGlD,EAClC,MACE,WAACiN,EAAAA,CAAKA,CAAAA,CACH,GAAGjN,CAAK,CACTkD,gBAAiBA,EACjBgK,kBAAgB,gCAChBC,QAAQ,cAER,UAACF,EAAAA,CAAKA,CAACG,MAAM,EAACC,WAAW,aACvB,UAACJ,EAAAA,CAAKA,CAACK,KAAK,EAAC1L,GAAG,yCACbN,EAAK0B,OAAO,KAGjB,UAACiK,EAAAA,CAAKA,CAACM,IAAI,WACRjM,EAAKQ,IAAI,KAIlB,CAUA,SAAS0L,EAAWxN,CAAsB,EACxC,GAAM,MAAEsB,CAAI,CAAE,CAAGtB,EACX,CAACyN,EAAWC,EAAa,CAAGC,EAAAA,QAAc,EAAC,UACjD,GAAYrM,EAAKQ,IAAI,CAEjB,CAFmB,EAEnB,8BACE,UAAC8L,SAAAA,CAAOzN,KAAK,SAAS+F,QAAS,IAAMwH,GAAa,GAAOhK,MAAO,CAAEmK,OAAQ,OAAQC,WAAY,OAAQC,QAAS,CAAE,WAC/G,UAACC,EAAAA,CAAIA,CAACC,MAAM,WACV,UAACxN,IAAAA,CAAElB,UAAU,4BAGhBS,EAAMsB,IAAI,EAAI,UAAC0L,EAAAA,CAAU1L,KAAMtB,EAAMsB,IAAI,CAAE4M,KAAMT,EAAWU,OAAQ,IAAMT,GAAa,GAAQxK,gBAAiBlD,EAAMkD,eAAe,MAIrI,IACT,CA4BA,MAhBA,SAASD,CAA2B,EAClC,GAAM,CAAEE,QAAM,IAeDF,EAfGnB,CAAI,CAAE,CAAG9B,EAEzB,EAaqB,IAZnB,WAACgO,EAAAA,CAAIA,CAAAA,CAACzO,UAAU,iCACd,UAACyO,EAAAA,CAAIA,CAACZ,MAAM,WAAEjK,IACd,UAAC6K,EAAAA,CAAIA,CAACT,IAAI,WACR,UAACS,EAAAA,CAAIA,CAACI,IAAI,WACPtM,MAGL,UAAC0L,EAAAA,CAAY,GAAGxN,CAAK,KAG3B", "sources": ["webpack://_N_E/./pages/dashboard/AboutUs.tsx", "webpack://_N_E/./pages/dashboard/AnnouncementItem.tsx", "webpack://_N_E/./pages/dashboard/OngoingProjects.tsx", "webpack://_N_E/./components/common/RKICalendar.tsx", "webpack://_N_E/./pages/dashboard/Announcement.tsx", "webpack://_N_E/./pages/dashboard/Dashboard.tsx", "webpack://_N_E/./components/common/placeholders/CardPlaceholder.tsx", "webpack://_N_E/./pages/dashboard/CalendarEvents.tsx", "webpack://_N_E/./pages/dashboard/OngoingOperations.tsx", "webpack://_N_E/./components/common/RKICard.tsx"], "sourcesContent": ["//Import Library\nimport React, { useEffect, useState } from \"react\";\n\n//Import services/components\nimport apiService from \"../../services/apiService\";\nimport CardPlaceholder from \"./../../components/common/placeholders/CardPlaceholder\";\nimport { useTranslation } from 'next-i18next';\n\nconst AboutUs = () => {\n  const { i18n } = useTranslation('common');\n  const currentLang = i18n.language ? i18n.language : \"en\";\n  const _initialVal = {\n    title: \"\",\n    description: \"\",\n    pageCategory: \"\",\n    images: \"\",\n    isEnabled: true,\n  };\n\n  const [aboutUs, setAboutUs] = useState<any>(_initialVal);\n  const [loading, setLoading] = useState(false);\n  const createMarkup = (htmlContent: string) => {\n    return { __html: htmlContent };\n  };\n\n  const landingPageParams = {\n    query: { pageCategory: \"\" },\n    sort: { title: \"asc\" },\n    limit: \"~\",\n  };\n\n  const fetchAboutUs = async () => {\n    const defautDesc =\n      \"The Robert Koch Institut is taking over the coordination of the “WHO AMR Surveillance and Quality Assessment Collaborating Centres Network” this autumn 2019. The network supports the World Health Organization (WHO) to reduce drug-resistant infections globally. It focuses on further developing the global antimicrobial resistance (AMR) surveillance system (GLASS), and promoting exchange and peer support between countries.\";\n\n    const pageCategoryId: any = await fetchPageCategory();\n    setLoading(true);\n    if (pageCategoryId.length > 0) {\n      landingPageParams.query.pageCategory = pageCategoryId;\n      const response = await apiService.get(\"/landingPage\", landingPageParams);\n      if (Array.isArray(response.data) && response.data.length > 0) {\n        const landingPageData = response.data[response.data.length - 1];\n        landingPageData.images =\n          response &&\n          landingPageData.images.length > 0 &&\n          landingPageData.isEnabled === true\n            ? landingPageData.images.map((item: any, _i: any) =>\n                String(`${process.env.API_SERVER}/image/show/${item._id}`)\n              )\n            : \"/images/logo.jpg\";\n            landingPageData.description =\n          response &&\n          landingPageData.description.length > 0 &&\n          landingPageData.isEnabled === true\n            ? landingPageData.description\n            : defautDesc;\n        setAboutUs(landingPageData);\n        fetchPageCategory();\n        setLoading(false);\n      }\n    }\n  };\n\n  const fetchPageCategory = async () => {\n    const response = await apiService.get(\"/pagecategory\", {\n      query: { title: \"AboutUs\" },\n    });\n    if (response && response.data && response.data.length > 0) {\n      const pageCategoryId = response.data[0]._id;\n      return pageCategoryId;\n    }\n    return false;\n  };\n\n  useEffect(() => {\n    fetchAboutUs();\n  }, []);\n\n  const desc = aboutUs.description.replace(/\\&nbsp;/g, \" \");\n\n  return (\n    <div className=\"aboutUs\">\n      {loading === true ? (\n        <CardPlaceholder />\n      ) : (\n        <div>\n          <img className=\"logoImg\" src={aboutUs.images} alt=\"\" />\n          <div dangerouslySetInnerHTML={createMarkup(desc)}></div>{\" \"}\n        </div>\n      )}\n    </div>\n  );\n};\n\nexport default AboutUs;\n", "//Import Library\r\nimport {Col} from \"react-bootstrap\";\r\nimport <PERSON> from \"next/link\";\r\n\r\nconst truncateLength = 260;\r\n\r\n//TODO: Remove the maths random number for image after updates completed with image upload\r\ninterface AnnouncementItemProps {\r\n  item: {\r\n    _id: string;\r\n    title: string;\r\n    description: string;\r\n    type: string;\r\n    [key: string]: any;\r\n  };\r\n}\r\n\r\nexport default function AnnouncementItem(props: AnnouncementItemProps) {\r\n\r\n  const {item} = props;\r\n\r\n  const getTrimmedString = (html: string) => {\r\n    const div = document.createElement(\"div\");\r\n    div.innerHTML = html;\r\n    const string = div.textContent || div.innerText || \"\";\r\n    return (string.length > truncateLength ? `${string.substring(0, truncateLength - 3)}...` : string);\r\n  }\r\n\r\n  const indexNew =  item.images.length -1;\r\n  const parent = `parent_${item.type}`;\r\n\r\n  return (\r\n    <Col className=\"p-0\" xs={12}>\r\n      <Link href={`/${item.type}/[...routes]`} as={`/${item.type}/show/${item[parent]}/update/${item._id}`}>\r\n\r\n        {(item.images && item.images[indexNew]) ?\r\n          <img src={`${process.env.API_SERVER}/image/show/${item.images[indexNew]._id}`} alt=\"announcement\"\r\n               className=\"announceImg\"/>\r\n          : <i className=\"fa fa-bullhorn announceImg\"/>}\r\n\r\n      </Link>\r\n      <div className=\"announceDesc\">\r\n        <Link href={`/${item.type}/[...routes]`} as={`/${item.type}/show/${item[parent]}/update/${item._id}`}>\r\n          {item && item.title ? item.title : ''}\r\n        </Link>\r\n        <p>\r\n          {item && item.description ? getTrimmedString(item.description) : null}\r\n        </p>\r\n      </div>\r\n    </Col>\r\n  );\r\n}", "//Import Library\r\nimport React, { useEffect, useState } from 'react';\r\nimport _ from \"lodash\";\r\nimport Link from \"next/link\";\r\nimport { ListGroup } from 'react-bootstrap';\r\n\r\n//Import services/components\r\nimport RKICard from \"../../components/common/RKICard\";\r\nimport CardPlaceholder from \"../../components/common/placeholders/CardPlaceholder\";\r\nimport apiService from \"../../services/apiService\";\r\n\r\n\r\ninterface ListItemsProps {\r\n  list: any[];\r\n}\r\n\r\nfunction ListItems(props: ListItemsProps) {\r\n  const { list } = props;\r\n  if (list.length > 0) {\r\n    return (\r\n      <ListGroup>\r\n        {list.map((item: any, index: number) => {\r\n          return (\r\n            <ListGroup.Item\r\n              key={index}>\r\n              <Link href=\"/project/[...routes]\" as={`/project/show/${item._id}`}>\r\n\r\n                {item.title}\r\n\r\n              </Link>\r\n            </ListGroup.Item>\r\n          );\r\n        })}\r\n      </ListGroup>\r\n    );\r\n  }\r\n  return null;\r\n}\r\n\r\ninterface CardDetailsProps {\r\n  project: {\r\n    body: string;\r\n    id: string;\r\n  };\r\n}\r\n\r\nfunction CardDetails(props: CardDetailsProps) {\r\n  const { project } = props;\r\n  return (\r\n    <Link\r\n      href='/project/[...routes]'\r\n      as={`/project/show/${project.id}`}\r\n      className='active-op-project'>\r\n\r\n      <span className=\"project-title link\">{project.body}</span>\r\n\r\n    </Link>\r\n  );\r\n}\r\n\r\ninterface OngoingProjectsProps {\r\n  t: (key: string) => string;\r\n  fetchOngoingProjects: (projects: any[]) => void;\r\n}\r\n\r\nfunction OngoingProjects(props: OngoingProjectsProps) {\r\n  const {t, fetchOngoingProjects} = props;\r\n  const cardHeader = t(\"OngoingProjects\");\r\n  const [project, setProject] = useState<{ body: string; id: string; list: any[] }>({ body: \"\", id: \"\", list: [] });\r\n  const [loading, setLoading] = useState(true);\r\n\r\n  const setEmptyNotice = () => {\r\n    setProject({ body: t(\"NoProjectavailable\"), id: \"\", list: [] })\r\n  };\r\n\r\n  const fetchProjects = async () => {\r\n    const projectParams = {\r\n      query: { status: [] },\r\n      sort: { created_at: \"desc\" },\r\n      limit: 10,\r\n      select: \"-website -description -funded_by -status -start_date -end_date -region -area_of_work -institution_invites -vspace -vspace_visibility -user -created_at -updated_at -partner_institutions.partner_region -partner_institutions.partner_institution -partner_institutions.world_region\"\r\n    };\r\n    const statusId: any = await fetchProjectStatus();\r\n    if (statusId) {\r\n      projectParams.query.status = statusId;\r\n\r\n      try {\r\n        setLoading(true);\r\n        const projects = await apiService.get('/project', projectParams);\r\n        setLoading(false);\r\n        if (Array.isArray(projects.data) && projects.data.length > 0) {\r\n          setProject({ body: projects.data[0].title, id: projects.data[0]._id, list: projects.data })\r\n          fetchOngoingProjects(projects.data);\r\n        } else {\r\n          setEmptyNotice()\r\n        }\r\n      } catch (e) {\r\n        setEmptyNotice()\r\n      }\r\n    } else {\r\n      setEmptyNotice()\r\n    }\r\n  };\r\n\r\n  const fetchProjectStatus = async () => {\r\n    const response = await apiService.get('/projectStatus');\r\n    if (response && response.data && response.data.length > 0) {\r\n      const statusId: any[] = []\r\n      _.forEach(response.data, function (item: any) {\r\n        if (item.title === \"Ongoing\") {\r\n          statusId.push(item._id);\r\n        }\r\n      });\r\n      return statusId;\r\n    }\r\n    return false;\r\n  };\r\n\r\n  useEffect(() => {\r\n    fetchProjects();\r\n  }, []);\r\n\r\n  const list = {\r\n    heading: cardHeader,\r\n    body: <ListItems list={project.list} />\r\n  };\r\n\r\n  return (\r\n    <RKICard\r\n      dialogClassName={\"ongoing-project-list\"}\r\n      list={list}\r\n      header={cardHeader}\r\n      body={loading ? <CardPlaceholder /> : <CardDetails project={project} />}\r\n    />\r\n  )\r\n}\r\n\r\nexport default OngoingProjects;", "//Import Library\r\nimport { Calendar, momentLocalizer, View } from \"react-big-calendar\";\r\nimport moment from \"moment\";\r\nimport 'moment/locale/fr';\r\nimport Router from \"next/router\";\r\nimport { Col, Container, Row } from \"react-bootstrap\";\r\nimport { FontAwesomeIcon } from \"@fortawesome/react-fontawesome\";\r\nimport { faStar } from \"@fortawesome/free-solid-svg-icons\";\r\nconst localizer = momentLocalizer(moment);\r\nimport _ from \"lodash\";\r\n\r\n//Import services/components\r\nimport { useTranslation } from 'next-i18next';\r\n\r\ninterface CalendarEvent {\r\n  _id: string;\r\n  start_date: string | Date;\r\n  end_date: string | Date;\r\n  [key: string]: any;\r\n}\r\n\r\ninterface ToolbarProps {\r\n  label: string;\r\n  onNavigate: (action: string) => void;\r\n}\r\n\r\ninterface RKICalendarProps {\r\n  eventsList: CalendarEvent[];\r\n  style?: React.CSSProperties;\r\n  minicalendar?: boolean;\r\n  views?: View[];\r\n  showEventCounts?: boolean;\r\n}\r\n\r\nfunction RKICalendar(props: RKICalendarProps) {\r\n  const { t, i18n } = useTranslation('common');\r\n  const currentLang = i18n.language\r\n  const { eventsList, style, minicalendar, views } = props;\r\n  let calendarComponent: any = {};\r\n  if (props.showEventCounts) {\r\n    calendarComponent = {\r\n      eventWrapper: EventWrapper,\r\n      month: {\r\n        dateHeader: (headerProps: any) =>\r\n           <DateHeader eventsList={eventsList} {...headerProps} />\r\n      },\r\n    };\r\n  }\r\n  if (minicalendar) {\r\n    calendarComponent.toolbar = MinimalToolbar;\r\n  }\r\n\r\n  return (\r\n    <Calendar\r\n      culture={currentLang}\r\n      localizer={localizer}\r\n      events={eventsList}\r\n      views={views}\r\n      startAccessor=\"start_date\"\r\n      endAccessor=\"end_date\"\r\n      style={style}\r\n      components={calendarComponent}\r\n      messages={{today:t(\"today\"),previous:t(\"back\"),next:t(\"Next\"),month:t(\"Month\"),week:t(\"Week\"),day:t(\"Day\")}}\r\n      onSelectEvent={(event: CalendarEvent) => {\r\n        const findOption = Object.keys(event)\r\n          .filter((item) => item.includes(\"parent\"))\r\n          .toString();\r\n        const urlAppend = findOption.split(\"_\")[1];\r\n        Router.push(\r\n          `/${urlAppend}/show/${event[findOption]}/update/${event._id}`\r\n        );\r\n      }}\r\n    />\r\n  );\r\n}\r\n\r\nRKICalendar.defaultProps = {\r\n  minicalendar: false,\r\n  views: [\"month\"],\r\n};\r\n\r\nfunction MinimalToolbar(props: ToolbarProps) {\r\n  return (\r\n    <Container className=\"mb-1\">\r\n      <Row>\r\n        <Col className=\"p-0\" md={1}>\r\n          <i\r\n            style={{ cursor: \"pointer\" }}\r\n            onClick={() => props.onNavigate(\"PREV\")}\r\n            className={`fas fa-chevron-left`}\r\n          />\r\n        </Col>\r\n        <Col className=\"text-center\" md={10}>\r\n          <span className=\"rbc-toolbar-label\">{props.label}</span>\r\n        </Col>\r\n        <Col className=\"p-0 text-end\" md={1}>\r\n          <i\r\n            style={{ cursor: \"pointer\" }}\r\n            onClick={() => props.onNavigate(\"NEXT\")}\r\n            className={`fas fa-chevron-right`}\r\n          />\r\n        </Col>\r\n      </Row>\r\n    </Container>\r\n  );\r\n}\r\n\r\n// Generates event counts based on the given date\r\nconst getEventsCount = (eventsList: CalendarEvent[], date: Date): number => {\r\n  let eventsCount = 0;\r\n  _.forEach(eventsList, (e) => {\r\n    const startDate = moment(e.start_date).set({\r\n      hour: 0,\r\n      minute: 0,\r\n      second: 0,\r\n      millisecond: 0,\r\n    });\r\n    const endDate = moment(e.end_date).set({\r\n      hour: 0,\r\n      minute: 0,\r\n      second: 0,\r\n      millisecond: 0,\r\n    });\r\n\r\n    const isEvent = moment(date).isBetween(startDate, endDate, null, \"[]\");\r\n    if (isEvent) {\r\n      eventsCount += 1;\r\n    }\r\n  });\r\n  return eventsCount;\r\n};\r\n\r\nconst DateHeader = ({ date, label, eventsList }: { date: Date; label: string; eventsList: CalendarEvent[] }) => {\r\n  const eventsCount = getEventsCount(eventsList, date);\r\n  const isEventCompleted = moment(date).isBefore(new Date(), \"day\");\r\n\r\n  return (\r\n    <div\r\n      className=\"rbc-date-cell\"\r\n      onClick={() => Router.push(\"/events-calendar\")}\r\n    >\r\n      <a href=\"#\">{label}</a>\r\n      {eventsCount > 0 && (\r\n        <span className=\"d-flex justify-content-start align-items-center fa-stack\">\r\n          <FontAwesomeIcon\r\n            icon={faStar}\r\n            color={isEventCompleted ? \"grey\" : \"#04A6FB\"}\r\n            size=\"lg\"\r\n          />\r\n          <span className=\"eventCount\">{eventsCount}</span>\r\n        </span>\r\n      )}\r\n    </div>\r\n  );\r\n};\r\n\r\nconst EventWrapper = (props: { onSelect?: () => void }) => {\r\n  return <div onSelect={props.onSelect} />;\r\n};\r\n\r\nexport default RKICalendar;\r\n", "//Import Library\r\nimport React, { useEffect, useState } from 'react';\r\nimport { Container, Row } from \"react-bootstrap\";\r\nimport _ from 'lodash';\r\nimport Carousel from 'react-bootstrap/Carousel'\r\nimport Col from \"react-bootstrap/Col\";\r\n\r\n//Import services/components\r\nimport AnnouncementItem from \"./AnnouncementItem\";\r\nimport apiService from \"../../services/apiService\";\r\n\r\n\r\n//TODO: Need to use RKISingleItemCarousel component to reuse our exisiting component\r\ninterface ListOfAnnouncementItemProps {\r\n  announcements: any[][];\r\n}\r\n\r\nfunction ListOfAnnouncementItem(props: ListOfAnnouncementItemProps) {\r\n  const { announcements } = props;\r\n  return (\r\n    <div>\r\n      {announcements.map((item: any, index: number) => {\r\n        return (\r\n          <Row className=\"announcementItem\" key={index}>\r\n            <AnnouncementItem item={item} />\r\n          </Row>\r\n        )\r\n      })}\r\n    </div>\r\n  )\r\n}\r\n\r\ninterface AnnouncementProps {\r\n  t: (key: string) => string;\r\n}\r\n\r\nfunction Announcement({ t }: AnnouncementProps) {\r\n  const [announcements, setAnnouncements] = useState<any[][]>([]);\r\n\r\n  const [cindex, setCindex] = useState(0);\r\n\r\n  const [carouselItemCount] = useState(3);\r\n\r\n  const setEmptyNotice = () => {\r\n    setAnnouncements([]);\r\n  };\r\n\r\n  const updatesParams = {\r\n    query: { show_as_announcement: true },\r\n    sort: { created_at: \"desc\" },\r\n    limit: 9,\r\n    select: \"-created_at -update_type -contact_details -document -end_date -link -media -reply -start_date -updated_at -user\"\r\n  };\r\n\r\n  const fetchAnnouncements = async (params = updatesParams) => {\r\n    const response = await apiService.get('/updates', params);\r\n    if (response && response.data && response.data.length > 0) {\r\n      const partition = _.chunk(response.data, 3);\r\n      setAnnouncements(partition)\r\n    } else {\r\n      setEmptyNotice()\r\n    }\r\n  };\r\n\r\n  useEffect(() => {\r\n    fetchAnnouncements();\r\n  }, [])\r\n\r\n  const toggleCarousel = (direction: 'next' | 'prev') => {\r\n    let index = cindex;\r\n    const [_min, max] = [0, carouselItemCount - 1];\r\n\r\n    if (direction === 'next' && index < max) {\r\n      index++\r\n    }\r\n    else if (direction === 'prev' && index > 0 ) {\r\n      index--\r\n    }\r\n\r\n\r\n    if ((announcements.length - index) === 1) {\r\n      index = announcements.length - 1;\r\n    }\r\n\r\n    if ((announcements.length - index) === 0) {\r\n      index = 1;\r\n    }\r\n    setCindex(index);\r\n  };\r\n\r\n\r\n  return (\r\n    <div className=\"announcements\">\r\n      {announcements && announcements.length > 0 ? (\r\n        <>\r\n          <Container fluid>\r\n            <Row>\r\n              <Col xs={10} className=\"p-0\">\r\n                <h4>{t('announcements')}</h4>\r\n              </Col>\r\n              {announcements && announcements.length > 1 ?\r\n                <Col xs={2} className=\"text-end carousel-control p-0\">\r\n                  <div className=\"carousel-navigation\">\r\n                    <a className=\"left carousel-control\" onClick={() => toggleCarousel('prev')}>\r\n                      <i className=\"fa fa-chevron-left\" />\r\n                    </a>\r\n                    <a className=\"right carousel-control\" onClick={() => toggleCarousel('next')}>\r\n                      <i className=\"fa fa-chevron-right\" />\r\n                    </a>\r\n                  </div>\r\n                </Col>\r\n                : null}\r\n            </Row>\r\n          </Container>\r\n          <Container fluid>\r\n            <Row>\r\n              <Col xs={12} className=\"p-0\">\r\n                <Carousel indicators={false} controls={false} interval={null} activeIndex={cindex}>\r\n                  {announcements.map((item: any, index: number) => {\r\n                    return (\r\n                      <Carousel.Item key={index}>\r\n                        <ListOfAnnouncementItem announcements={item} />\r\n                      </Carousel.Item>\r\n                    )\r\n                  })}\r\n                </Carousel>\r\n              </Col>\r\n            </Row>\r\n          </Container>\r\n        </>\r\n      ) : (\r\n          <Container fluid={true}>\r\n            <Row>\r\n              <Col xs={10} className=\"p-0\">\r\n                <h4>{t('announcements')}</h4>\r\n              </Col>\r\n              <Col xs={12} className=\"p-0\">\r\n                <div className=\"border p-3\">{t(\"NoAnnouncementsavailabletodisplay\")}</div><br /></Col>\r\n            </Row>\r\n          </Container>\r\n        )}\r\n    </div>\r\n  )\r\n}\r\n\r\nexport default Announcement;", "//Import Library\r\nimport {Container, <PERSON>, Col} from \"react-bootstrap\";\r\nimport { connect } from \"react-redux\";\r\nimport { useEffect, useState } from \"react\";\r\nimport { serverSideTranslations } from 'next-i18next/serverSideTranslations';\r\n\r\n//Import services/components\r\nimport AboutUs from \"./AboutUs\";\r\nimport OngoingOperations from \"./OngoingOperations\";\r\nimport OngoingProjects from \"./OngoingProjects\";\r\nimport Announcement from \"./Announcement\"\r\nimport ActiveProjectOperations from \"./ActiveProjectOperations\";\r\nimport CalendarEvents from \"./CalendarEvents\";\r\nimport apiService from \"../../services/apiService\";\r\n\r\n\r\nconst events = [\r\n  {\r\n    title: \"Global RKI summit\",\r\n    start: new Date(),\r\n    end: new Date(),\r\n    allDay: true\r\n  }\r\n]\r\n\r\ninterface DashboardProps {\r\n  t: (key: string) => string;\r\n  user: any;\r\n}\r\n\r\nfunction Dashboard({ t, user }: DashboardProps) {\r\n  const [getUserName, setUserName] = useState(\"\");\r\n  const [ongoingOperations, setOngoingOperations] = useState<any[]>([]);\r\n  const [ongoingProjects, setOngoingProjects] = useState<any[]>([]);\r\n  const [focalPointApprove,setFocalPointApprove] = useState (\"Approved\");\r\n  const [isApproval, setIsApproval] = useState<any>(\"\");\r\n\r\n  const getLoggedUser = async () => {\r\n    const data = await apiService.post(\"/users/getLoggedUser\", {});\r\n    if(data?.is_focal_point === false && data?.is_vspace === false) {\r\n      setFocalPointApprove(\"Approved\");\r\n    } else {\r\n      if (data?.status) {\r\n        const status =  getStatusForFocalPoint(data.status);\r\n        setIsApproval(status);\r\n        } else if (data?.vspace_status) {\r\n          const status =  getStatusForVspace(data.vspace_status);\r\n          setIsApproval(status);\r\n        } else {\r\n          setIsApproval(\"RejectedForFocalPoint\");\r\n        }\r\n    }\r\n\r\n    function getStatusForFocalPoint(status: string) {\r\n      switch (status) {\r\n        case \"Request Pending\": return \"WaitingForFocalPoint\";\r\n        case \"Approved\": return \"SucessForFocalPoint\";\r\n      }\r\n    }\r\n  };\r\n\r\n  function getStatusForVspace(status: string) {\r\n    switch (status) {\r\n      case \"Request Pending\": return \"WaitingForVspace\";\r\n      case \"Approved\": return \"SucessForVspace\";\r\n\r\n    }\r\n  }\r\n  useEffect(() => {\r\n    if(user.is_focal_point){\r\n    const is_Approved = user.status == \"Approved\" ? \"Approved\": \"Pending\";\r\n    setFocalPointApprove(is_Approved);\r\n  }\r\n  if(user.is_vspace){\r\n    const is_Approved = user.vspace_status == \"Approved\" ? \"Approved\": \"Pending\";\r\n    setFocalPointApprove(is_Approved);\r\n  }\r\n    const _username = (user && user.username) ? user.username : \"\";\r\n    setUserName(_username);\r\n    getLoggedUser();\r\n  }, [user]);\r\n\r\n  return (\r\n    <>\r\n    {focalPointApprove == \"Approved\" ? (\r\n      <Container fluid={true} className=\"p-0 dashboardScreen\">\r\n      <h2>{t('hello')} {getUserName}</h2>\r\n      <Container fluid={true}>\r\n        <Row>\r\n          <Col className=\"ps-lg-0 dashboardLeft\" lg=\"8\">\r\n            <Row>\r\n              <Col xs=\"12\">\r\n                <AboutUs/>\r\n              </Col>\r\n            </Row>\r\n            <Row>\r\n              <Col xs={12}>\r\n                <Row>\r\n                <Col md=\"6\" className=\"ongoingBlock\">\r\n                  <OngoingOperations t={t} fetchOngoingOperations={setOngoingOperations} />\r\n                </Col>\r\n                <Col md=\"6\" className=\"ongoingBlock\">\r\n                  <OngoingProjects t={t} fetchOngoingProjects={setOngoingProjects} />\r\n                </Col>\r\n                </Row>\r\n              </Col>\r\n            </Row>\r\n          </Col>\r\n          <Col className=\"pe-lg-0 dashboard-calendar\" lg=\"4\">\r\n            <CalendarEvents />\r\n          </Col>\r\n        </Row>\r\n        <Row>\r\n        <Col className=\"p-lg-0\" xs=\"12\">\r\n            <Announcement t={t} />\r\n          </Col>\r\n        </Row>\r\n        <Row>\r\n        <Col className=\"p-lg-0\" xs={12}>\r\n          <ActiveProjectOperations t={t} ongoingProjects={ongoingProjects} ongoingOperations={ongoingOperations} />\r\n          </Col>\r\n        </Row>\r\n      </Container>\r\n    </Container>\r\n    ) : <div className=\"Focalpoint\"><h2>Welcome to Robert Koch Institut !</h2>\r\n      <div>\r\n      <h5 className=\"text-muted\">{t(isApproval)}</h5>\r\n      </div>\r\n      </div>}\r\n    </>\r\n\r\n  )\r\n}\r\n\r\nexport async function getStaticProps({ locale }: { locale: string }) {\r\n  return {\r\n    props: {\r\n      ...(await serverSideTranslations(locale, ['common'])),\r\n    },\r\n  }\r\n}\r\n\r\nexport default connect((state: any) => state)(Dashboard);", "//Import Library\r\nimport ContentLoader from 'react-content-loader';\r\n\r\n// No props needed - component uses hardcoded values\r\nexport default function CardPlaceholder() {\r\n  return(\r\n    <ContentLoader\r\n      viewBox=\"0 0 380 70\"\r\n      height={50}\r\n      width={317}\r\n      speed={2}\r\n      title={'Loading'}\r\n      foregroundColor=\"#f7f7f7\"\r\n      backgroundColor=\"#ecebeb\"\r\n      uniqueKey={\"operation\"}\r\n    >\r\n      <rect x=\"10\" y=\"0\" rx=\"4\" ry=\"4\" width=\"320\" height=\"25\" />\r\n      <rect x=\"40\" y=\"40\" rx=\"3\" ry=\"3\" width=\"250\" height=\"20\" />\r\n    </ContentLoader>\r\n  )\r\n}\r\n", "//Import Library\r\nimport {useEffect, useState} from \"react\";\r\nimport _ from \"lodash\";\r\n\r\n//Import services/components\r\nimport RKICalendar from \"../../components/common/RKICalendar\";\r\nimport apiService from \"../../services/apiService\";\r\n\r\ninterface CalendarEventsProps {\r\n  [key: string]: any;\r\n}\r\n\r\nfunction CalendarEvents(_props: CalendarEventsProps) {\r\n\r\n  const [events, setEvents] = useState<any[]>([]);\r\n\r\n  const fetchUpdateType = async () => {\r\n    const response = await apiService.get('/updateType', {\"query\": {\"title\": \"Calendar Event\"}});\r\n    if (response && response.data && response.data.length > 0 ) {\r\n      fetchCalendarEvents(response.data[0]._id);\r\n    }\r\n  };\r\n\r\n  const fetchCalendarEvents = async (updateTypeId: any) => {\r\n    const calendarEventsParams = {\r\n      query: {update_type: updateTypeId},\r\n      sort: {created_at: \"desc\" },\r\n      limit: 20,\r\n      select: \"-contact_details -description -document -images -link -media -parent_operation -reply -show_as_announcement -type -user\"\r\n    }\r\n\r\n    const response = await apiService.get('/updates', calendarEventsParams);\r\n    if (response && response.data) {\r\n      _.forEach(response.data, function(val, i) {\r\n        response.data[i].allDay = false;\r\n      });\r\n      setEvents(response.data)\r\n    }\r\n  };\r\n\r\n  useEffect(() => {\r\n    fetchUpdateType();\r\n  },[])\r\n\r\n  return (\r\n    <RKICalendar eventsList={events} minicalendar showEventCounts />\r\n  )\r\n}\r\n\r\nexport default CalendarEvents;", "//Import Library\r\nimport React, { useEffect, useState } from 'react';\r\nimport _ from \"lodash\";\r\nimport Link from \"next/link\";\r\nimport ListGroup from \"react-bootstrap/ListGroup\";\r\n\r\n//Import services/components\r\nimport RKICard from \"../../components/common/RKICard\";\r\nimport apiService from '../../services/apiService';\r\nimport CardPlaceholder from \"../../components/common/placeholders/CardPlaceholder\";\r\n\r\n\r\ninterface ListItemsProps {\r\n  list: any[];\r\n}\r\n\r\nfunction ListItems(props: ListItemsProps) {\r\n  const { list } = props;\r\n  if (list.length > 0) {\r\n    return (\r\n      <ListGroup>\r\n        {list.map((item: any, index: number) => {\r\n          return (\r\n            <ListGroup.Item\r\n              key={index}>\r\n              <Link href=\"/operation/[...routes]\" as={`/operation/show/${item._id}`}>\r\n\r\n                {item.title}\r\n\r\n              </Link>\r\n            </ListGroup.Item>\r\n          );\r\n        })}\r\n      </ListGroup>\r\n    );\r\n  }\r\n  return null;\r\n}\r\n\r\ninterface CardDetailsProps {\r\n  operation: {\r\n    body: string;\r\n    id: string;\r\n  };\r\n}\r\n\r\nfunction CardDetails(props: CardDetailsProps) {\r\n  const { operation } = props;\r\n  return (\r\n    <Link\r\n      href='/operation/[...routes]'\r\n      as={`/operation/show/${operation.id}`}\r\n      className='active-op-project'>\r\n\r\n      <span className=\"project-title link\">{operation.body}</span>\r\n\r\n    </Link>\r\n  );\r\n}\r\n\r\ninterface OngoingOperationsProps {\r\n  t: (key: string) => string;\r\n  fetchOngoingOperations: (operations: any[]) => void;\r\n}\r\n\r\nfunction OngoingOperations(props: OngoingOperationsProps) {\r\n  const {t, fetchOngoingOperations} = props;\r\n  const cardHeader = t(\"OngoingOperations\");\r\n  const [operation, setOperation] = useState<{ body: string; id: string; list: any[] }>({ body: \"\", id: \"\", list: [] });\r\n  const [loading, setLoading] = useState(true);\r\n\r\n  const setEmptyNotice = () => {\r\n    setOperation({ body: t(\"Nooperationavailable\"), id: \"\", list: [] })\r\n  };\r\n\r\n  const fetchOperation = async () => {\r\n    const operationParams = {\r\n      query: { status: [] },\r\n      sort: { created_at: \"desc\" },\r\n      limit: 10,\r\n      select: \"-description -status -start_date -end_date -timeline -world_region -region -hazard_type -hazard -syndrome -partners -vspace -vspace_visibility -images -user -created_at -updated_at\"\r\n    };\r\n    const statusId: any = await fetchOperationStatus();\r\n\r\n    if (statusId) {\r\n      operationParams.query.status = statusId;\r\n      try {\r\n        setLoading(true);\r\n        const operations = await apiService.get('/operation', operationParams);\r\n        setLoading(false);\r\n        if (Array.isArray(operations.data) && operations.data.length > 0) {\r\n          setOperation({ body: operations.data[0].title, id: operations.data[0]._id, list: operations.data })\r\n          fetchOngoingOperations(operations.data);\r\n        } else {\r\n          setEmptyNotice()\r\n        }\r\n      } catch (e) {\r\n        setEmptyNotice()\r\n      }\r\n    } else {\r\n      setEmptyNotice()\r\n    }\r\n\r\n  };\r\n\r\n  const fetchOperationStatus = async () => {\r\n    const response = await apiService.get('/operation_status');\r\n    if (response && response.data && response.data.length > 0) {\r\n      const statusId: any[] = []\r\n      _.forEach(response.data, function (item: any) {\r\n        if (item.title == 'Ongoing') {\r\n          statusId.push(item._id);\r\n        }\r\n      });\r\n      return statusId;\r\n    }\r\n    return false;\r\n  };\r\n\r\n  useEffect(() => {\r\n    fetchOperation();\r\n  }, []);\r\n\r\n  const list = {\r\n    heading: cardHeader,\r\n    body: <ListItems list={operation.list} />\r\n  };\r\n\r\n  return (\r\n    <RKICard\r\n      dialogClassName={\"ongoing-project-list\"}\r\n      list={list}\r\n      header={cardHeader}\r\n      body={loading ? <CardPlaceholder /> : <CardDetails operation={operation} />}\r\n    />\r\n  )\r\n}\r\n\r\nexport default OngoingOperations;", "//Import Library\r\nimport React from \"react\";\r\nimport { Card } from \"react-bootstrap\";\r\nimport Modal from \"react-bootstrap/Modal\";\r\n\r\ninterface ListModalProps {\r\n  list: {\r\n    heading: string;\r\n    body: React.ReactNode;\r\n  };\r\n  dialogClassName?: string;\r\n  show: boolean;\r\n  onHide: () => void;\r\n}\r\n\r\nfunction ListModal(props: ListModalProps) {\r\n  const { list, dialogClassName } = props;\r\n  return (\r\n    <Modal\r\n      {...props}\r\n      dialogClassName={dialogClassName}\r\n      aria-labelledby=\"contained-modal-title-vcenter\"\r\n      centered\r\n    >\r\n      <Modal.Header closeButton>\r\n        <Modal.Title id=\"contained-modal-title-vcenter\">\r\n          {list.heading}\r\n        </Modal.Title>\r\n      </Modal.Header>\r\n      <Modal.Body>\r\n        {list.body}\r\n      </Modal.Body>\r\n    </Modal>\r\n  )\r\n}\r\n\r\ninterface CardFooterProps {\r\n  list?: {\r\n    body: React.ReactNode;\r\n    heading: string;\r\n  };\r\n  dialogClassName?: string;\r\n}\r\n\r\nfunction CardFooter(props: CardFooterProps) {\r\n  const { list } = props;\r\n  const [modalShow, setModalShow] = React.useState(false);\r\n  if (list && list.body) {\r\n    return (\r\n      <>\r\n        <button type=\"button\" onClick={() => setModalShow(true)} style={{ border: 'none', background: 'none', padding: 0 }}>\r\n          <Card.Footer>\r\n            <i className=\"fas fa-chevron-down\" />\r\n          </Card.Footer>\r\n        </button>\r\n        {props.list && <ListModal list={props.list} show={modalShow} onHide={() => setModalShow(false)} dialogClassName={props.dialogClassName} />}\r\n      </>\r\n    )\r\n  }\r\n  return null;\r\n}\r\n\r\ninterface RKICardProps {\r\n  header: string;\r\n  body: React.ReactNode;\r\n  list?: {\r\n    body: React.ReactNode;\r\n    heading: string;\r\n  };\r\n  dialogClassName?: string;\r\n}\r\n\r\nfunction RKICard(props: RKICardProps) {\r\n  const { header, body } = props\r\n\r\n  return (\r\n    <Card className=\"text-center infoCard\">\r\n      <Card.Header>{header}</Card.Header>\r\n      <Card.Body>\r\n        <Card.Text>\r\n          {body}\r\n        </Card.Text>\r\n      </Card.Body>\r\n      <CardFooter {...props} />\r\n    </Card>\r\n  )\r\n}\r\n\r\nexport default RKICard;\r\n"], "names": ["i18n", "useTranslation", "AboutUs", "language", "aboutUs", "setAboutUs", "useState", "_initialVal", "title", "description", "pageCategory", "images", "isEnabled", "loading", "setLoading", "landingPageParams", "query", "sort", "limit", "fetchAboutUs", "pageCategoryId", "fetchPageCategory", "length", "response", "apiService", "get", "Array", "isArray", "data", "landingPageData", "map", "item", "_i", "String", "process", "_id", "defautDesc", "useEffect", "desc", "replace", "div", "className", "CardPlaceholder", "img", "src", "alt", "dangerouslySetInnerHTML", "__html", "htmlContent", "AnnouncementItem", "props", "indexNew", "parent", "type", "Col", "xs", "Link", "href", "as", "i", "p", "getTrimmedString", "html", "document", "createElement", "innerHTML", "string", "textContent", "innerText", "truncate<PERSON><PERSON>th", "substring", "ListItems", "list", "ListGroup", "index", "<PERSON><PERSON>", "CardDetails", "project", "id", "span", "body", "OngoingProjects", "t", "fetchOngoingProjects", "<PERSON><PERSON><PERSON><PERSON>", "setProject", "setEmptyNotice", "fetchProjects", "projectParams", "status", "created_at", "select", "statusId", "fetchProjectStatus", "projects", "e", "_", "push", "heading", "RKICard", "dialogClassName", "header", "localizer", "momentLocalizer", "moment", "RKICalendar", "currentLang", "eventsList", "style", "minicalendar", "views", "calendarComponent", "showEventCounts", "eventWrapper", "EventWrapper", "month", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "headerProps", "toolbar", "MinimalToolbar", "Calendar", "culture", "events", "startAccessor", "endAccessor", "components", "messages", "today", "previous", "next", "week", "day", "onSelectEvent", "event", "findOption", "Object", "keys", "filter", "includes", "toString", "urlAppend", "split", "Router", "Container", "Row", "md", "cursor", "onClick", "onNavigate", "label", "defaultProps", "getEventsCount", "date", "eventsCount", "startDate", "start_date", "set", "hour", "minute", "second", "millisecond", "endDate", "end_date", "isEvent", "isBetween", "isEventCompleted", "isBefore", "Date", "a", "FontAwesomeIcon", "icon", "faStar", "color", "size", "onSelect", "ListOfAnnouncementItem", "announcements", "Announcement", "setAnnouncements", "cindex", "setCindex", "carouselItemCount", "updatesParams", "show_as_announcement", "fetchAnnouncements", "params", "partition", "toggleCarousel", "_min", "max", "direction", "fluid", "h4", "Carousel", "indicators", "controls", "interval", "activeIndex", "br", "connect", "state", "Dashboard", "user", "getUserName", "setUserName", "ongoingOperations", "setOngoingOperations", "ongoingProjects", "setOngoingProjects", "focalPointApprove", "setFocalPointApprove", "isApproval", "setIsApproval", "getLogged<PERSON>ser", "post", "is_focal_point", "is_vspace", "getStatusForFocalPoint", "vspace_status", "getStatusForVspace", "is_Approved", "username", "_username", "h2", "lg", "OngoingOperations", "fetchOngoingOperations", "CalendarEvents", "ActiveProjectOperations", "h5", "ContentLoader", "viewBox", "height", "width", "speed", "foregroundColor", "backgroundColor", "<PERSON><PERSON><PERSON>", "rect", "x", "y", "rx", "ry", "setEvents", "fetchUpdateType", "fetchCalendarEvents", "updateTypeId", "calendarEventsParams", "update_type", "val", "allDay", "operation", "setOperation", "fetchOperation", "operationParams", "fetchOperationStatus", "operations", "ListModal", "Modal", "aria-<PERSON>by", "centered", "Header", "closeButton", "Title", "Body", "<PERSON><PERSON><PERSON>er", "modalShow", "setModalShow", "React", "button", "border", "background", "padding", "Card", "Footer", "show", "onHide", "Text"], "sourceRoot": "", "ignoreList": []}