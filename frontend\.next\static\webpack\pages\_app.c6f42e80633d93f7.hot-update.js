"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/_app",{

/***/ "(pages-dir-browser)/./components/hoc/AuthSync.tsx":
/*!*************************************!*\
  !*** ./components/hoc/AuthSync.tsx ***!
  \*************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(pages-dir-browser)/./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(pages-dir-browser)/./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react_redux__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! react-redux */ \"(pages-dir-browser)/./node_modules/react-redux/dist/react-redux.mjs\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/router */ \"(pages-dir-browser)/./node_modules/next/router.js\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_router__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _common_CustomLoader__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../common/CustomLoader */ \"(pages-dir-browser)/./components/common/CustomLoader.tsx\");\n/* harmony import */ var _services_authService__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../../services/authService */ \"(pages-dir-browser)/./services/authService.tsx\");\n//Import Library\n\n\n\n\n\n\n// Public routes used to handle layouts\nconst publicRoutes = [\n    \"/home\",\n    \"/login\",\n    // \"/admin/login\",\n    \"/forgot-password\",\n    \"/reset-password/[passwordToken]\",\n    \"/declarationform/[...routes]\"\n];\n// Gets the display name of a JSX component for dev tools\nconst getDisplayName = (Component1)=>Component1.displayName || Component1.name || \"Component\";\nfunction withAuthSync(WrappedComponent) {\n    class MainComponent extends react__WEBPACK_IMPORTED_MODULE_1__.Component {\n        static async getInitialProps(ctx) {\n            const componentProps = WrappedComponent.getInitialProps && await WrappedComponent.getInitialProps(ctx);\n            if (ctx.ctx && ctx.ctx.req && ctx.ctx.req.cookies) {\n                const objCookies = ctx.ctx && ctx.ctx.req && ctx.ctx.req.cookies ? ctx.ctx.req.cookies : {};\n                componentProps.objCookies = objCookies;\n                return {\n                    ...componentProps\n                };\n            } else {\n                return {\n                    ...componentProps\n                };\n            }\n        }\n        async componentDidMount() {\n            const { route } = this.props.router;\n            next_router__WEBPACK_IMPORTED_MODULE_2___default().events.on(\"routeChangeComplete\", (url)=>{\n                if (url === \"/home\") {\n                    this.setState({\n                        isLoading: false\n                    });\n                }\n            });\n            // Check if user has actively logged in (not just copied cookie)\n            const hasActiveLogin = _services_authService__WEBPACK_IMPORTED_MODULE_4__[\"default\"].hasActiveLogin();\n            if (!hasActiveLogin && publicRoutes.indexOf(route) === -1) {\n                console.log(\"User has valid session but no active login - redirecting to login\");\n                // Clear any existing session storage to be safe\n                if (true) {\n                    sessionStorage.clear();\n                }\n                this.props.router.push(\"/home\");\n                return;\n            }\n            // Use backend session verification instead of cookie parsing\n            try {\n                const sessionData = await _services_authService__WEBPACK_IMPORTED_MODULE_4__[\"default\"].verifySession();\n                if (!sessionData.isAuthenticated && publicRoutes.indexOf(route) === -1) {\n                    this.props.router.push(\"/home\");\n                    return;\n                }\n                this.setState({\n                    isLoading: false,\n                    cookie: sessionData.isAuthenticated && hasActiveLogin ? \"authenticated\" : null\n                });\n            } catch (error) {\n                console.error(\"Session verification failed:\", error);\n                if (publicRoutes.indexOf(route) === -1) {\n                    this.props.router.push(\"/home\");\n                    return;\n                }\n                this.setState({\n                    isLoading: false\n                });\n            }\n        }\n        componentWillUnmount() {\n            next_router__WEBPACK_IMPORTED_MODULE_2___default().events.off(\"routeChangeComplete\", ()=>null);\n        }\n        render() {\n            const { router } = this.props;\n            const isPublicRoute = publicRoutes.indexOf(router.route) > -1;\n            return this.state.isLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_common_CustomLoader__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {}, void 0, false, {\n                fileName: \"D:\\\\rki\\\\frontend\\\\components\\\\hoc\\\\AuthSync.tsx\",\n                lineNumber: 116,\n                columnNumber: 37\n            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(WrappedComponent, {\n                isLoading: this.state.isLoading,\n                isPublicRoute: isPublicRoute,\n                ...this.props\n            }, void 0, false, {\n                fileName: \"D:\\\\rki\\\\frontend\\\\components\\\\hoc\\\\AuthSync.tsx\",\n                lineNumber: 117,\n                columnNumber: 9\n            }, this);\n        }\n        constructor(props){\n            super(props);\n            this.state = {\n                isLoading: true,\n                cookie: this.props && this.props.objCookies && this.props.objCookies[\"connect.sid\"] ? this.props.objCookies[\"connect.sid\"] : null\n            };\n        }\n    }\n    MainComponent.displayName = \"withAuthSync(\".concat(getDisplayName(WrappedComponent), \")\");\n    return (0,react_redux__WEBPACK_IMPORTED_MODULE_5__.connect)((state)=>state)(MainComponent);\n}\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (withAuthSync);\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-browser)/./components/hoc/AuthSync.tsx\n"));

/***/ })

});