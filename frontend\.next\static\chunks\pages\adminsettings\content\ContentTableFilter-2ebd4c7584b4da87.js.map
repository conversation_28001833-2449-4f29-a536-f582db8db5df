{"version": 3, "file": "static/chunks/pages/adminsettings/content/ContentTableFilter-2ebd4c7584b4da87.js", "mappings": "qNAcA,IAAMA,EAAQ,CACZ,CACEC,IAAK,YACLC,MAAO,YACT,EACA,CACED,IAAK,cACLC,MAAO,eACT,EACA,CACED,IAAK,QACLC,MAAO,QACT,EACA,CACED,IAAK,UACLC,MAAO,UACT,EACA,CACED,IAAK,UACLC,MAAO,SACT,EACA,CACED,IAAK,SACLC,MAAO,gBACT,EACD,CA2CD,EAxC2B,OAAC,YAACC,CAAU,QAwCxBC,EAxC0BC,CAAQ,eAwChBD,EAAC,GAxCiBE,CAAkB,SAAEC,CAAO,YAAEC,CAAU,CAA2B,GAC7G,GAAEC,CAAC,CAAE,CAAGC,CAAAA,EAAAA,EAAAA,EAAAA,CAAcA,CAAC,UAC7B,MACE,UAACC,EAAAA,CAASA,CAAAA,CAACC,KAAK,IAACC,UAAU,eACzB,WAACC,EAAAA,CAAGA,CAAAA,WACF,UAACC,EAAAA,CAAGA,CAAAA,CAACC,GAAI,EAAGC,GAAI,EAAGJ,UAAU,+BAC3B,UAACK,EAAAA,CAASA,CAAAA,UACV,UAACC,EAAAA,CAAWA,CAAAA,CACVC,KAAK,OACLP,UAAU,cACVQ,YAAaZ,EAAE,qCACfa,aAAW,SACXC,MAAOpB,EACPqB,SAAUnB,QAId,UAACU,EAAAA,CAAGA,CAAAA,CAACC,GAAI,EAAGC,GAAI,WACd,WAACC,EAAAA,CAASA,CAAAA,CAACO,GAAIX,EAAAA,CAAGA,WAChB,UAACY,EAAAA,CAASA,CAAAA,CAACC,MAAM,IAACC,GAAI,EAAGC,GAAI,EAAGhB,UAAU,gBAAQJ,EAAE,qCACpD,UAACM,EAAAA,CAAGA,CAAAA,CAACF,UAAU,mBACb,UAACM,EAAAA,CAAWA,CAAAA,CACVM,GAAG,SACHH,aAAW,OACXE,SAAWM,GAAMxB,EAAmBwB,GACpCP,MAAOf,WACNR,EAAM+B,GAAG,CAAC,CAACC,EAAMC,IAEd,UAACC,SAAAA,CAAmBX,MAAOS,EAAK/B,GAAG,UAAG+B,EAAK9B,KAAK,EAAnC+B,iBAUjC,mBC/EA,4CACA,4CACA,WACA,OAAe,EAAQ,GAAiE,CACxF,EACA,WAFsB", "sources": ["webpack://_N_E/./pages/adminsettings/content/ContentTableFilter.tsx", "webpack://_N_E/?4b2f"], "sourcesContent": ["//Import Library\r\nimport {useEffect, useState} from \"react\";\r\nimport {Col, Container, FormControl, FormGroup, FormLabel, Row} from \"react-bootstrap\";\r\n\r\n//Import services/components\r\nimport { useTranslation } from 'next-i18next';\r\n\r\ninterface ContentTableFilterProps {\r\n  filterText: string;\r\n  onFilter: (e: React.ChangeEvent<HTMLInputElement>) => void;\r\n  onFilterTypeChange: (e: React.ChangeEvent<HTMLSelectElement>) => void;\r\n  onClear: () => void;\r\n  filterType: string;\r\n}\r\nconst types = [\r\n  {\r\n    _id: \"operation\",\r\n    title: \"Operations\"\r\n  },\r\n  {\r\n    _id: \"institution\",\r\n    title: \"Organisations\"\r\n  },\r\n  {\r\n    _id: \"event\",\r\n    title: \"Events\"\r\n  },\r\n  {\r\n    _id: \"project\",\r\n    title: \"Projects\"\r\n  },\r\n  {\r\n    _id: \"updates\",\r\n    title: \"Updates\"\r\n  },\r\n  {\r\n    _id: \"vspace\",\r\n    title: \"Virtual Spaces\"\r\n  }\r\n];\r\n\r\n\r\nconst ContentTableFilter = ({filterText, onFilter, onFilterTypeChange, onClear, filterType }: ContentTableFilterProps) => {\r\n  const { t } = useTranslation('common');\r\n  return (\r\n    <Container fluid className=\"p-0\">\r\n      <Row>\r\n        <Col xs={6} md={4} className=\"ps-0 align-self-end\" >\r\n          <FormGroup>\r\n          <FormControl\r\n            type=\"text\"\r\n            className=\"searchInput\"\r\n            placeholder={t(\"adminsetting.content.table.Search\")}\r\n            aria-label=\"Search\"\r\n            value={filterText}\r\n            onChange={onFilter}\r\n          />\r\n          </FormGroup>\r\n        </Col>\r\n        <Col xs={6} md={4}>\r\n          <FormGroup as={Row}>\r\n            <FormLabel column sm={3} lg={2} className=\"me-2\">{t('adminsetting.content.table.Type')}</FormLabel>\r\n            <Col className=\"ps-md-0\">\r\n              <FormControl\r\n                as=\"select\"\r\n                aria-label=\"Type\"\r\n                onChange={(e) => onFilterTypeChange(e as unknown as React.ChangeEvent<HTMLSelectElement>)}\r\n                value={filterType}>\r\n                {types.map((item, index) => {\r\n                  return (\r\n                    <option key={index} value={item._id}>{item.title}</option>\r\n                  )\r\n                })}\r\n              </FormControl>\r\n            </Col>\r\n          </FormGroup>\r\n        </Col>\r\n      </Row>\r\n    </Container>\r\n  )\r\n};\r\n\r\nexport default ContentTableFilter;\r\n", "\n    (window.__NEXT_P = window.__NEXT_P || []).push([\n      \"/adminsettings/content/ContentTableFilter\",\n      function () {\n        return require(\"private-next-pages/adminsettings/content/ContentTableFilter.tsx\");\n      }\n    ]);\n    if(module.hot) {\n      module.hot.dispose(function () {\n        window.__NEXT_P.push([\"/adminsettings/content/ContentTableFilter\"])\n      });\n    }\n  "], "names": ["types", "_id", "title", "filterText", "ContentTableFilter", "onFilter", "onFilterTypeChange", "onClear", "filterType", "t", "useTranslation", "Container", "fluid", "className", "Row", "Col", "xs", "md", "FormGroup", "FormControl", "type", "placeholder", "aria-label", "value", "onChange", "as", "FormLabel", "column", "sm", "lg", "e", "map", "item", "index", "option"], "sourceRoot": "", "ignoreList": []}