"use strict";exports.id=4756,exports.ids=[4756],exports.modules={4897:(e,t,s)=>{s.r(t),s.d(t,{default:()=>d});var a=s(8732);s(82015);var r=s(83551),n=s(49481),i=s(88751),o=s(6858),c=s(63349);let l=e=>{let t=[];return e?.forEach(e=>{e.partner_institution?.forEach(e=>{t.push({_id:e._id,title:e.title})})}),t=t.filter((e,t,s)=>s.findIndex(t=>t._id===e._id)===t)},d=e=>{var t,s;let{t:d}=(0,i.useTranslation)("common"),{description:j,partner_institutions:u}=e.project,p=l(u);return(0,a.jsx)(a.Fragment,{children:(0,a.jsxs)(r.A,{className:"projectInfoBlock",children:[(0,a.jsx)(n.A,{className:"projectDescBlock",md:8,children:(0,a.jsx)(c.A,{description:j})}),(0,a.jsxs)(n.A,{md:4,className:"projectInfo",children:[(0,a.jsx)(o.A,{header:d("ProjectInformation"),body:function(e,t){let{area_of_work:s,funded_by:r}=t;return(0,a.jsxs)("div",{children:[(0,a.jsxs)("div",{className:"projetInfoItems",children:[(0,a.jsxs)("h6",{children:[e("AreaofWork"),": "]}),(0,a.jsx)("p",{children:s?.map(e=>e.title).join(", ")})]}),(0,a.jsxs)("div",{className:"projetInfoItems",children:[(0,a.jsxs)("h6",{children:[e("FundedBy"),": "]}),(0,a.jsx)("p",{children:r})]})]})}(d,e.project)}),(0,a.jsx)(o.A,{header:d("PartnerOrganisation"),body:(t=p,(0,a.jsx)("ul",{className:"projectPartner",children:t?.map((e,t)=>(0,a.jsx)("li",{children:e?.title||""},t))}))}),(0,a.jsx)(o.A,{header:d("CountriesCoveredbyProject"),body:(s=u,(0,a.jsx)("ul",{className:"projectPartner",children:s?.map((e,t)=>(0,a.jsx)("li",{children:e?.partner_country?.title||""},t))}))})]})]})})}},6858:(e,t,s)=>{s.d(t,{A:()=>j});var a=s(8732),r=s(82015),n=s.n(r),i=s(18597),o=s(78219),c=s.n(o);function l(e){let{list:t,dialogClassName:s}=e;return(0,a.jsxs)(c(),{...e,dialogClassName:s,"aria-labelledby":"contained-modal-title-vcenter",centered:!0,children:[(0,a.jsx)(c().Header,{closeButton:!0,children:(0,a.jsx)(c().Title,{id:"contained-modal-title-vcenter",children:t.heading})}),(0,a.jsx)(c().Body,{children:t.body})]})}function d(e){let{list:t}=e,[s,r]=n().useState(!1);return t&&t.body?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)("button",{type:"button",onClick:()=>r(!0),style:{border:"none",background:"none",padding:0},children:(0,a.jsx)(i.A.Footer,{children:(0,a.jsx)("i",{className:"fas fa-chevron-down"})})}),e.list&&(0,a.jsx)(l,{list:e.list,show:s,onHide:()=>r(!1),dialogClassName:e.dialogClassName})]}):null}let j=function(e){let{header:t,body:s}=e;return(0,a.jsxs)(i.A,{className:"text-center infoCard",children:[(0,a.jsx)(i.A.Header,{children:t}),(0,a.jsx)(i.A.Body,{children:(0,a.jsx)(i.A.Text,{children:s})}),(0,a.jsx)(d,{...e})]})}},23252:(e,t,s)=>{s.a(e,async(e,a)=>{try{s.r(t),s.d(t,{default:()=>f});var r=s(8732);s(82015);var n=s(91353),i=s(83551),o=s(19918),c=s.n(o),l=s(82053),d=s(54131),j=s(74716),u=s.n(j),p=s(88751),h=s(99775),m=s(81426),x=e([d,m]);[d,m]=x.then?(await x)():x;let f=e=>{let{t}=(0,p.useTranslation)("common"),s=()=>(0,r.jsx)(r.Fragment,{children:e.editAccess?(0,r.jsx)(c(),{href:"/project/[...routes]",as:`/project/edit/${e.routeData.routes[1]}`,children:(0,r.jsxs)(n.A,{variant:"secondary",size:"sm",children:[(0,r.jsx)(l.FontAwesomeIcon,{icon:d.faPen}),"\xa0",t("Edit")]})}):""}),a=(0,h.canEditProject)(()=>(0,r.jsx)(s,{}));return(0,r.jsx)(r.Fragment,{children:(0,r.jsx)(i.A,{className:"projectRow",children:(0,r.jsxs)("div",{className:"projectBanner",children:[(0,r.jsx)("div",{className:"projectImg",children:(0,r.jsx)("img",{src:"/images/project-banner.jpg",alt:"Project Detail"})}),function(e,t,s,a,n){return(0,r.jsxs)("div",{className:"projectTitleBlock",children:[(0,r.jsxs)("h4",{className:"projectTitle",children:[e.title,"\xa0\xa0",t.routes&&t.routes[1]?(0,r.jsx)(s,{project:e}):null]}),(0,r.jsxs)("div",{className:"projectDate",children:[(0,r.jsxs)("div",{className:"projectStart",children:[(0,r.jsx)("i",{className:"fas fa-calendar-alt"}),(0,r.jsxs)("div",{children:[(0,r.jsxs)("h6",{style:{color:"white"},children:[a("StartDate"),":"]}),(0,r.jsx)("h5",{children:e.start_date?u()(e.start_date).format(n):null})]})]}),(0,r.jsxs)("div",{className:"projectStatus me-2",children:[(0,r.jsx)("i",{className:"fas fa-hourglass-half"}),(0,r.jsxs)("div",{children:[(0,r.jsxs)("h6",{style:{color:"white"},children:[a("Status"),":"]}),(0,r.jsx)("h5",{children:e.status&&e.status.title})]})]}),(0,r.jsx)(m.A,{entityId:t.routes[1],entityType:"project"})]})]})}(e.projectData,e.routeData,a,t,"DD-MM-YYYY")]})})})};a()}catch(e){a(e)}})},38058:(e,t,s)=>{s.a(e,async(e,a)=>{try{s.d(t,{A:()=>u});var r=s(8732),n=s(82015),i=s(19918),o=s.n(i),c=s(56084),l=s(63487),d=s(88751),j=e([l]);l=(j.then?(await j)():j)[0];let u=e=>{let{t}=(0,d.useTranslation)("common"),{type:s,id:a}=e,[i,j]=(0,n.useState)([]),[u,p]=(0,n.useState)(!1),[h,m]=(0,n.useState)(0),[x,f]=(0,n.useState)(10),[g]=(0,n.useState)(!1),A={sort:{created_at:"asc"},limit:x,page:1,query:{}},y=[{name:t("Title"),selector:"title",cell:e=>e&&e.title&&e._id?(0,r.jsx)(o(),{href:"/vspace/[...routes]",as:`/vspace/show/${e._id}`,children:e.title}):""},{name:t("Owner"),selector:"users",cell:e=>e&&e.user&&e.user.firstname?`${e.user.firstname} ${e.user.lastname}`:""},{name:t("PublicPrivate"),selector:"visibility",cell:e=>e&&e.visibility?"Public":"Private"},{name:t("NumberofMembers"),selector:"members",cell:e=>e&&e.members?e.members.length:"-"}],v=async e=>{p(!0);let t=await l.A.get(`stats/get${s}WithVspace/${a}`,A);t&&("Operation"===s?j(t.operation):j(t.project),m(t.totalCount),p(!1))},w=async(e,t)=>{A.limit=e,A.page=t,p(!0);let r=await l.A.get(`stats/get${s}WithVspace/${a}`,A);r&&("Operation"===s?j(r.operation):j(r.project),f(e),p(!1))};return(0,n.useEffect)(()=>{v(A)},[]),(0,r.jsx)("div",{children:(0,r.jsx)(c.A,{columns:y,data:i,totalRows:h,loading:u,resetPaginationToggle:g,handlePerRowsChange:w,handlePageChange:e=>{A.limit=x,A.page=e,v(A)}})})};a()}catch(e){a(e)}})},46472:(e,t,s)=>{s.a(e,async(e,a)=>{try{s.r(t),s.d(t,{default:()=>u});var r=s(8732),n=s(82015),i=s(54131),o=s(82053),c=s(93024),l=s(38058),d=s(88751),j=e([i,l]);[i,l]=j.then?(await j)():j;let u=e=>{let{t}=(0,d.useTranslation)("common"),[s,a]=(0,n.useState)(!1);return(0,r.jsx)(r.Fragment,{children:(0,r.jsxs)(c.A.Item,{eventKey:"0",children:[(0,r.jsxs)(c.A.Header,{onClick:()=>a(!s),children:[(0,r.jsx)("div",{className:"cardTitle",children:t("LinkedVirtualSpace")}),(0,r.jsx)("div",{className:"cardArrow",children:s?(0,r.jsx)(o.FontAwesomeIcon,{icon:i.faMinus,color:"#fff"}):(0,r.jsx)(o.FontAwesomeIcon,{icon:i.faPlus,color:"#fff"})})]}),(0,r.jsx)(c.A.Body,{children:(0,r.jsx)(l.A,{id:e.routeData?.routes?.[1]||"",type:"Project",vspaceData:[],vspaceDataLoading:!1,vspaceDataTotalRows:0,vspaceDataPerPage:10,vspaceDataCurrentPage:1})})]})})};a()}catch(e){a(e)}})},53238:(e,t,s)=>{s.a(e,async(e,a)=>{try{s.r(t),s.d(t,{default:()=>h});var r=s(8732),n=s(82015),i=s(54131),o=s(82053),c=s(74716),l=s.n(c),d=s(93024),j=s(18597),u=s(88751),p=e([i]);i=(p.then?(await p)():p)[0];let h=e=>{let t="DD-MM-YYYY HH:mm:ss",{t:s}=(0,u.useTranslation)("common"),[a,c]=(0,n.useState)(!0);return(0,r.jsx)(r.Fragment,{children:(0,r.jsxs)(d.A.Item,{eventKey:"0",children:[(0,r.jsxs)(d.A.Header,{onClick:()=>c(!a),children:[(0,r.jsx)("div",{className:"cardTitle",children:s("ProjectDetails")}),(0,r.jsx)("div",{className:"cardArrow",children:a?(0,r.jsx)(o.FontAwesomeIcon,{icon:i.faPlus,color:"#fff"}):(0,r.jsx)(o.FontAwesomeIcon,{icon:i.faMinus,color:"#fff"})})]}),(0,r.jsx)(d.A.Body,{children:(0,r.jsxs)(j.A.Text,{className:"projectDetails ps-0",children:[(0,r.jsxs)("p",{children:[(0,r.jsx)("b",{children:s("Status")}),":",(0,r.jsxs)("span",{children:[" ",e.project.status?e.project.status.title:""]})]}),(0,r.jsxs)("p",{children:[(0,r.jsx)("b",{children:s("Created")}),":",(0,r.jsxs)("span",{children:[" ",e.project.created_at?l()(e.project.created_at).format(t):null," "]})]}),(0,r.jsxs)("p",{children:[(0,r.jsx)("b",{children:s("EndDate")}),":",(0,r.jsxs)("span",{children:[" ",e.project.end_date?l()(e.project.end_date).format("DD-MM-YYYY"):null]})]}),(0,r.jsxs)("p",{children:[(0,r.jsx)("b",{children:s("LastModified")}),":",(0,r.jsxs)("span",{children:[" ",e.project.updated_at?l()(e.project.updated_at).format(t):null," "]})]}),(0,r.jsxs)("p",{children:[(0,r.jsx)("b",{children:s("WeblinktoProject")}),":",(0,r.jsx)("span",{children:e.project?.website&&(0,r.jsx)("a",{href:e.project.website,target:"_blank",children:e.project.website})})]})]})})]})})};a()}catch(e){a(e)}})},56084:(e,t,s)=>{s.d(t,{A:()=>l});var a=s(8732);s(82015);var r=s(38609),n=s.n(r),i=s(88751),o=s(30370);function c(e){let{t}=(0,i.useTranslation)("common"),s={rowsPerPageText:t("Rowsperpage")},{columns:r,data:c,totalRows:l,resetPaginationToggle:d,subheader:j,subHeaderComponent:u,handlePerRowsChange:p,handlePageChange:h,rowsPerPage:m,defaultRowsPerPage:x,selectableRows:f,loading:g,pagServer:A,onSelectedRowsChange:y,clearSelectedRows:v,sortServer:w,onSort:_,persistTableHead:N,sortFunction:P,...D}=e,b={paginationComponentOptions:s,noDataComponent:t("NoData"),noHeader:!0,columns:r,data:c||[],dense:!0,paginationResetDefaultPage:d,subHeader:j,progressPending:g,subHeaderComponent:u,pagination:!0,paginationServer:A,paginationPerPage:x||10,paginationRowsPerPageOptions:m||[10,15,20,25,30],paginationTotalRows:l,onChangeRowsPerPage:p,onChangePage:h,selectableRows:f,onSelectedRowsChange:y,clearSelectedRows:v,progressComponent:(0,a.jsx)(o.A,{}),sortIcon:(0,a.jsx)("i",{className:"sort-icon fas fa-exchange-alt"}),sortServer:w,onSort:_,sortFunction:P,persistTableHead:N,className:"rki-table"};return(0,a.jsx)(n(),{...b})}c.defaultProps={subHeader:!1,pagination:!0,totalRows:null,pagServer:!0,onSelectedRowsChange:null,clearSelectedRows:!1,sortServer:!1,persistTableHead:!1};let l=c},74756:(e,t,s)=>{s.a(e,async(e,a)=>{try{s.r(t),s.d(t,{default:()=>p});var r=s(8732),n=s(82015),i=s(7082),o=s(63487),c=s(77136),l=s(23252),d=s(4897),j=s(95433),u=e([o,c,l,j]);[o,c,l,j]=u.then?(await u)():u;let p=e=>{let[t,s]=(0,n.useState)({title:"",website:"",area_of_work:[],status:{},funded_by:"",country:{},description:"",end_date:"",start_date:"",partner_institutions:[],partner_institution:{},created_at:"",updated_at:""}),[,a]=(0,n.useState)(""),[,u]=(0,n.useState)(""),[p,h]=(0,n.useState)(!1);(0,n.useEffect)(()=>{e?.routes[1]&&m()},[]);let m=async()=>{let t=await o.A.post("/users/getLoggedUser",{});t&&t.roles&&t.roles.length&&e.routes&&e.routes[1]&&(async r=>{let n=await o.A.get(`/project/${e.routes[1]}`,r);(function(e,t,s,a){e&&t(e),e?.user?.firstname&&e?.user?.lastname&&s(`${e.user.firstname} ${e.user.lastname}`),e?.user?.position&&a(e.user.position)})(n,s,a,u),function(e,t){h(!1),t&&t.roles&&(t.roles.includes("SUPER_ADMIN")||t.roles.filter(e=>"EMT_NATIONAL_FOCALPOINT"==e).length>0&&e.user._id==t._id||t.roles.filter(e=>"NGOS"==e).length>0&&e.user._id==t._id||t.roles.filter(e=>"GENERAL_USER"==e).length>0&&e.user._id==t._id||t.roles.filter(e=>"PLATFORM_ADMIN"==e).length>0&&e.user._id==t._id||t.roles.filter(e=>"INIG_STAKEHOLDER"==e).length>0&&e.user._id==t._id?h(!0):t.roles.filter(e=>"EMT"==e).length>0&&e.user._id==t._id&&h(!0))}(n,t)})({})},x={projectData:t,routeData:e,editAccess:p};return(0,r.jsxs)(i.A,{className:"projectDetail",fluid:!0,children:[(0,r.jsx)(c.A,{routes:e.routes}),(0,r.jsx)(l.default,{...x}),(0,r.jsx)(d.default,{project:t}),(0,r.jsx)(j.default,{...x})]})};a()}catch(e){a(e)}})},76429:(e,t,s)=>{s.a(e,async(e,a)=>{try{s.r(t),s.d(t,{default:()=>u});var r=s(8732),n=s(82015),i=s(93024),o=s(82053),c=s(54131),l=s(88751),d=s(82491),j=e([c,d]);[c,d]=j.then?(await j)():j;let u=e=>{let{t}=(0,l.useTranslation)("common"),[s,a]=(0,n.useState)(!0);return(0,r.jsxs)(i.A.Item,{eventKey:"2",children:[(0,r.jsxs)(i.A.Header,{onClick:()=>a(!s),children:[(0,r.jsx)("div",{className:"cardTitle",children:t("Discussions")}),(0,r.jsx)("div",{className:"cardArrow",children:s?(0,r.jsx)(o.FontAwesomeIcon,{icon:c.faPlus,color:"#fff"}):(0,r.jsx)(o.FontAwesomeIcon,{icon:c.faMinus,color:"#fff"})})]}),(0,r.jsx)(i.A.Body,{children:(0,r.jsx)(d.A,{type:"project",id:e?.routeData?.routes?e.routeData.routes[1]:""})})]})};a()}catch(e){a(e)}})},81426:(e,t,s)=>{s.a(e,async(e,a)=>{try{s.d(t,{A:()=>u});var r=s(8732),n=s(14062),i=s(54131),o=s(82015),c=s(82053),l=s(63487),d=e([n,i,l]);[n,i,l]=d.then?(await d)():d;let j={operation:"Operation",institution:"Institution",event:"Event",project:"Project",vspace:"Vspace"},u=(0,n.connect)(e=>e)(e=>{let{user:t,entityId:s,entityType:a}=e,[n,d]=(0,o.useState)(!1),[u,p]=(0,o.useState)(""),h=async()=>{if(!t?._id)return;let e=await l.A.get("/flag",{query:{entity_id:s,user:t._id,onModel:j[a]}});e&&e.data&&e.data.length>0&&(p(e.data[0]),d(!0))},m=async e=>{if(e.preventDefault(),!t?._id)return;let r=!n,i={entity_type:a,entity_id:s,user:t._id,onModel:j[a]};if(r){let e=await l.A.post("/flag",i);e&&e._id&&(p(e),d(r))}else{let e=await l.A.remove(`/flag/${u._id}`);e&&e.n&&d(r)}};return(0,o.useEffect)(()=>{h()},[]),(0,r.jsx)("div",{className:"subscribe-flag",children:(0,r.jsxs)("a",{href:"",onClick:m,children:[(0,r.jsx)("span",{className:"check",children:n?(0,r.jsx)(c.FontAwesomeIcon,{className:"clickable checkIcon",icon:i.faCheckCircle,color:"#00CC00"}):(0,r.jsx)(c.FontAwesomeIcon,{className:"clickable minusIcon",icon:i.faPlusCircle,color:"#fff"})}),(0,r.jsx)(c.FontAwesomeIcon,{className:"bookmark",icon:i.faBookmark,color:"#d4d4d4"})]})})});a()}catch(e){a(e)}})},95433:(e,t,s)=>{s.a(e,async(e,a)=>{try{s.r(t),s.d(t,{default:()=>p});var r=s(8732);s(82015);var n=s(83551),i=s(49481),o=s(93024),c=s(99775),l=s(53238),d=s(46472),j=s(76429),u=e([l,d,j]);[l,d,j]=u.then?(await u)():u;let p=e=>{let t=(0,c.canViewDiscussionUpdate)(()=>(0,r.jsx)(j.default,{routeData:e.routeData}));return(0,r.jsx)(r.Fragment,{children:(0,r.jsx)(n.A,{children:(0,r.jsxs)(i.A,{className:"projectAccordion",xs:12,children:[(0,r.jsx)(o.A,{children:(0,r.jsx)(l.default,{project:e.projectData})}),(0,r.jsx)(o.A,{children:(0,r.jsx)(t,{})}),(0,r.jsx)(o.A,{children:(0,r.jsx)(d.default,{routeData:e.routeData})})]})})})};a()}catch(e){a(e)}})},99775:(e,t,s)=>{s.r(t),s.d(t,{canAddProject:()=>o,canAddProjectForm:()=>c,canEditProject:()=>l,canEditProjectForm:()=>d,canViewDiscussionUpdate:()=>j,default:()=>u});var a=s(8732);s(82015);var r=s(81366),n=s.n(r),i=s(61421);let o=n()({authenticatedSelector:e=>!!e.permissions&&!!e.permissions.project&&!!e.permissions.project["create:any"],wrapperDisplayName:"CanAddProject"}),c=n()({authenticatedSelector:e=>!!e.permissions&&!!e.permissions.project&&!!e.permissions.project["create:any"],wrapperDisplayName:"CanAddProjectForm",FailureComponent:()=>(0,a.jsx)(i.default,{})}),l=n()({authenticatedSelector:(e,t)=>{if(e.permissions&&e.permissions.project){if(e.permissions.project["update:any"])return!0;else if(e.permissions.project["update:own"]&&t.project&&t.project.user&&t.project.user._id===e.user._id)return!0}return!1},wrapperDisplayName:"CanEditProject"}),d=n()({authenticatedSelector:(e,t)=>{if(e.permissions&&e.permissions.project){if(e.permissions.project["update:any"])return!0;else if(e.permissions.project["update:own"]&&t.project&&t.project.user&&t.project.user._id===e.user._id)return!0}return!1},wrapperDisplayName:"CanEditProjectForm",FailureComponent:()=>(0,a.jsx)(i.default,{})}),j=n()({authenticatedSelector:e=>!!e.permissions&&!!e.permissions.update&&!!e.permissions.update["read:any"],wrapperDisplayName:"CanViewDiscussionUpdate"}),u=o}};