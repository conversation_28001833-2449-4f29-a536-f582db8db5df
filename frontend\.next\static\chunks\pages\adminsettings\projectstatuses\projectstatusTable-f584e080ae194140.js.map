{"version": 3, "file": "static/chunks/pages/adminsettings/projectstatuses/projectstatusTable-f584e080ae194140.js", "mappings": "yOAmIA,MAvH4BA,IACxB,GAAM,CAAEC,CAAC,CAAE,CAAGC,CAAAA,EAAAA,EAAAA,EAAAA,CAAcA,CAAC,QAsHlBC,EArHL,CAACC,EAAWC,EAAe,CAAGC,CAAAA,EAAAA,EAAAA,KAqHPH,EAAC,CArHMG,CAAQA,CAAC,EAAE,EACzC,EAAGC,EAAW,CAAGD,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,EAAC,GAC1B,CAACE,EAAWC,EAAa,CAAGH,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAC,GACrC,CAACI,EAASC,EAAW,CAAGL,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAC,IACjC,CAACM,EAAaC,EAAS,CAAGP,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,EAAC,GACnC,CAACQ,EAAqBC,EAAuB,CAAGT,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAC,CAAC,GAE1DU,EAAsB,CACxBC,KAAM,CAAEC,MAAO,KAAM,EACrBC,MAAOT,EACPU,KAAM,EACNC,MAAO,CAAC,CACZ,EAEMC,EAAU,CACZ,CACIC,KAAMtB,EAAE,oCACRuB,SAAU,OACd,EACA,CACID,KAAMtB,EAAE,qCACRuB,SAAU,GACVC,KAAM,GACF,WAACC,MAAAA,WACG,UAACC,IAAIA,CAACC,KAAK,6BAA6BC,GAAI,OAAvCF,8BAAkF,OAANG,EAAEC,GAAG,WAElF,UAACC,IAAAA,CAAEC,UAAU,uBAEV,OAEP,UAACC,IAAAA,CAAEC,QAAS,IAAMC,EAAWN,YACzB,UAACE,IAAAA,CAAEC,UAAU,4BACZ,MAGjB,EACH,CAEKI,EAAuB,UACzB9B,EAAW,IACX,IAAM+B,EAAW,MAAMC,EAAAA,CAAUA,CAACC,GAAG,CAAC,iBAAkBxB,GACpDsB,GAAYA,EAASG,IAAI,EAAIH,EAASG,IAAI,CAACC,MAAM,CAAG,GAAG,CACvDrC,EAAeiC,EAASG,IAAI,EAC5BhC,EAAa6B,EAASK,UAAU,EAChCpC,GAAW,GAEnB,EAQMqC,EAAsB,MAAOC,EAAiBzB,KAChDJ,EAAoBG,KAAK,CAAG0B,EAC5B7B,EAAoBI,IAAI,CAAGA,EAC3Bb,GAAW,GACX,IAAM+B,EAAW,MAAMC,EAAAA,CAAUA,CAACC,GAAG,CAAC,iBAAkBxB,GACpDsB,GAAYA,EAASG,IAAI,EAAIH,EAASG,IAAI,CAACC,MAAM,CAAG,GAAG,CACvDrC,EAAeiC,EAASG,IAAI,EAC5B9B,EAAWkC,GACXtC,GAAW,GAEnB,EAEM6B,EAAa,MAAOU,IACtB/B,EAAuB+B,EAAIf,GAAG,EAC9BlB,GAAS,EACb,EAEMkC,EAAe,UACjB,GAAI,CACA,MAAMR,EAAAA,CAAUA,CAACS,MAAM,CAAC,kBAAsC,OAApBlC,IAC1CuB,IACAxB,GAAS,GACToC,EAAAA,EAAKA,CAACC,OAAO,CAACjD,EAAE,qEACpB,CAAE,MAAOkD,EAAO,CACZF,EAAAA,EAAKA,CAACE,KAAK,CAAClD,EAAE,+DAClB,CACJ,EAEMmD,EAAY,IAAMvC,GAAS,GAMjC,MAJAwC,CAAAA,EAAAA,EAAAA,SAAAA,CAASA,CAAC,KACNhB,GACJ,EAAG,EAAE,EAGD,WAACX,MAAAA,WACG,WAAC4B,EAAAA,CAAKA,CAAAA,CAACC,KAAM3C,EAAa4C,OAAQJ,YAC9B,UAACE,EAAAA,CAAKA,CAACG,MAAM,EAACC,WAAW,aACrB,UAACJ,EAAAA,CAAKA,CAACK,KAAK,WAAE1D,EAAE,sDAEpB,UAACqD,EAAAA,CAAKA,CAACM,IAAI,WAAE3D,EAAE,wEACf,WAACqD,EAAAA,CAAKA,CAACO,MAAM,YACT,UAACC,EAAAA,CAAMA,CAAAA,CAACC,QAAQ,YAAY5B,QAASiB,WAChCnD,EAAE,uCAEP,UAAC6D,EAAAA,CAAMA,CAAAA,CAACC,QAAQ,UAAU5B,QAASY,WAC9B9C,EAAE,0CAKf,UAAC+D,EAAAA,CAAQA,CAAAA,CACL1C,QAASA,EACTmB,KAAMrC,EACNI,UAAWA,EACXyD,WAAW,EACXrB,oBAAqBA,EACrBsB,iBA/Da,CA+DKA,GA9D1BlD,EAAoBG,KAAK,CAAGT,EAC5BM,EAAoBI,IAAI,CAAGA,EAC3BiB,GACJ,MA+DJ,6GC5FA,SAAS2B,EAASG,CAAoB,EACpC,GAAM,CAAElE,CAAC,CAAE,CAAGC,CAAAA,EAAAA,EAAAA,EAAAA,CAAcA,CAAC,UACvBkE,EAA6B,CACjCC,gBAAiBpE,EAAE,cACnB,EACI,SACJqB,CAAO,MACPmB,CAAI,WACJjC,CAAS,uBACT8D,CAAqB,WACrBC,CAAS,oBACTC,CAAkB,qBAClB5B,CAAmB,kBACnBsB,CAAgB,aAChBO,CAAW,oBACXC,CAAkB,gBAClBC,CAAc,SACdC,CAAO,WACPX,CAAS,sBACTY,CAAoB,CACpBC,mBAAiB,YACjBC,CAAU,QACVC,CAAM,kBACNC,CAAgB,cAChBC,CAAY,CAEZ,CADA,EACGC,EACJ,CAAGhB,EAGEiB,EAAiB,CACrBhB,6BACAiB,gBAAiBpF,EAAE,IAP0C,MAQ7DqF,UAAU,UACVhE,EACAmB,KAAMA,GAAQ,EAAE,CAChB8C,OAAO,EACPC,2BAA4BlB,EAC5BmB,UAAWlB,EACXmB,gBAAiBd,qBACjBJ,EACAmB,WAAY,GACZC,iBAAkB3B,EAClB4B,kBAAmBnB,GAA0C,GAC7DoB,eADwCpB,cACVD,GAA4B,CAAC,GAAI,GAAI,GAAI,CAA3BA,EAA+B,GAAG,CAC9EsB,oBAAqBvF,EACrBwF,oBAAqBpD,EACrBqD,aAAc/B,iBACdS,uBACAE,oBACAC,EACAoB,kBAAmB,UAACC,EAAAA,CAAYA,CAAAA,CAAAA,GAChCC,SAAU,UAACpE,IAAAA,CAAEC,UAAU,6CACvB8C,SACAC,eACAE,mBACAD,EACAhD,UAAW,WACb,EACA,MACE,UAACoE,EAAAA,EAASA,CAAAA,CAAE,GAAGjB,CAAc,EAEjC,CAEApB,EAASsC,YAAY,CAAG,CACtBb,WAAW,EACXE,YAAY,EACZnF,UAAW,KACXyD,WAAW,EACXY,qBAAsB,KACtBC,mBAAmB,EACnBC,YAAY,EACZE,kBAAkB,CACpB,EAEA,MAAejB,QAAQA,EAAC,SC/GxB,4CACA,oDACA,WACA,OAAe,EAAQ,IAAyE,CAChG,EACA,UAFsB", "sources": ["webpack://_N_E/./pages/adminsettings/projectstatuses/projectstatusTable.tsx", "webpack://_N_E/./components/common/RKITable.tsx", "webpack://_N_E/?ce38"], "sourcesContent": ["//Import Library\r\nimport { useState, useEffect } from \"react\";\r\nimport Link from \"next/link\";\r\nimport { <PERSON><PERSON>, But<PERSON> } from \"react-bootstrap\";\r\n\r\nimport toast from 'react-hot-toast';\r\n\r\n//Import services/components\r\nimport RKITable from \"../../../components/common/RKITable\";\r\nimport apiService from \"../../../services/apiService\";\r\nimport { useTranslation } from 'next-i18next';\r\n\r\nconst ProjectstatusTable = (_props: any) => {\r\n    const { t } = useTranslation('common');\r\n    const [tabledata, setDataToTable] = useState([]);\r\n    const [, setLoading] = useState(false);\r\n    const [totalRows, setTotalRows] = useState(0);\r\n    const [perPage, setPerPage] = useState(10);\r\n    const [isModalShow, setModal] = useState(false);\r\n    const [selectProjectstatus, setSelectProjectstatus] = useState({});\r\n    \r\n    const projectstatusParams = {\r\n        sort: { title: \"asc\" },\r\n        limit: perPage,\r\n        page: 1,\r\n        query: {},\r\n    };\r\n\r\n    const columns = [\r\n        {\r\n            name: t(\"adminsetting.ProjectStatus.Title\"),\r\n            selector: \"title\",\r\n        },\r\n        {\r\n            name: t(\"adminsetting.ProjectStatus.Action\"),\r\n            selector: \"\",\r\n            cell: (d: any) => (\r\n                <div>\r\n                    <Link href=\"/adminsettings/[...routes]\" as={`/adminsettings/edit_projectstatus/${d._id}`}>\r\n\r\n                        <i className=\"icon fas fa-edit\" />\r\n\r\n                    </Link>\r\n                    &nbsp;\r\n                    <a onClick={() => userAction(d)}>\r\n                        <i className=\"icon fas fa-trash-alt\" />\r\n                    </a>{\" \"}\r\n                </div>\r\n            ),\r\n        },\r\n    ];\r\n\r\n    const getProjectstatusData = async () => {\r\n        setLoading(true);\r\n        const response = await apiService.get(\"/projectstatus\", projectstatusParams);\r\n        if (response && response.data && response.data.length > 0) {\r\n            setDataToTable(response.data);\r\n            setTotalRows(response.totalCount);\r\n            setLoading(false);\r\n        }\r\n    };\r\n\r\n    const handlePageChange = (page: any) => {\r\n        projectstatusParams.limit = perPage;\r\n        projectstatusParams.page = page;\r\n        getProjectstatusData();\r\n    };\r\n\r\n    const handlePerRowsChange = async (newPerPage: any, page: any) => {\r\n        projectstatusParams.limit = newPerPage;\r\n        projectstatusParams.page = page;\r\n        setLoading(true);\r\n        const response = await apiService.get(\"/projectstatus\", projectstatusParams);\r\n        if (response && response.data && response.data.length > 0) {\r\n            setDataToTable(response.data);\r\n            setPerPage(newPerPage);\r\n            setLoading(false);\r\n        }\r\n    };\r\n\r\n    const userAction = async (row: any) => {\r\n        setSelectProjectstatus(row._id);\r\n        setModal(true);\r\n    };\r\n\r\n    const modalConfirm = async () => {\r\n        try {\r\n            await apiService.remove(`/projectstatus/${selectProjectstatus}`);\r\n            getProjectstatusData();\r\n            setModal(false);\r\n            toast.success(t(\"adminsetting.ProjectStatus.Table.projectStatusDeletedSuccessfully\"));\r\n        } catch (error) {\r\n            toast.error(t(\"adminsetting.ProjectStatus.Table.errorDeletingProjectStatus\"));\r\n        }\r\n    };\r\n\r\n    const modalHide = () => setModal(false);\r\n\r\n    useEffect(() => {\r\n        getProjectstatusData();\r\n    }, []);\r\n\r\n    return (\r\n        <div>\r\n            <Modal show={isModalShow} onHide={modalHide}>\r\n                <Modal.Header closeButton>\r\n                    <Modal.Title>{t(\"adminsetting.ProjectStatus.DeleteProjectstatus\")}</Modal.Title>\r\n                </Modal.Header>\r\n                <Modal.Body>{t(\"adminsetting.ProjectStatus.Areyousurewanttodeletethisprojectstatus\")}</Modal.Body>\r\n                <Modal.Footer>\r\n                    <Button variant=\"secondary\" onClick={modalHide}>\r\n                        {t(\"adminsetting.ProjectStatus.Cancel\")}\r\n                    </Button>\r\n                    <Button variant=\"primary\" onClick={modalConfirm}>\r\n                        {t(\"adminsetting.ProjectStatus.Yes\")}\r\n                    </Button>\r\n                </Modal.Footer>\r\n            </Modal>\r\n\r\n            <RKITable\r\n                columns={columns}\r\n                data={tabledata}\r\n                totalRows={totalRows}\r\n                pagServer={true}\r\n                handlePerRowsChange={handlePerRowsChange}\r\n                handlePageChange={handlePageChange}\r\n            />\r\n        </div>\r\n    );\r\n};\r\n\r\nexport default ProjectstatusTable;\r\n", "//Import Library\r\nimport React from 'react'\r\nimport DataTable  from 'react-data-table-component';\r\n\r\n//Import services/components\r\nimport { useTranslation } from 'next-i18next';\r\nimport CustomLoader from './CustomLoader';\r\n\r\ninterface RKITableProps {\r\n  columns: any[];\r\n  data: any[];\r\n  totalRows?: number;\r\n  resetPaginationToggle?: boolean;\r\n  subheader?: boolean;\r\n  subHeaderComponent?: React.ReactNode;\r\n  handlePerRowsChange?: (newPerPage: number, page: number) => void;\r\n  handlePageChange?: (page: number) => void;\r\n  rowsPerPage?: number[];\r\n  defaultRowsPerPage?: number;\r\n  selectableRows?: boolean;\r\n  loading?: boolean;\r\n  pagServer?: boolean;\r\n  onSelectedRowsChange?: (selectedRows: any) => void;\r\n  clearSelectedRows?: boolean;\r\n  sortServer?: boolean;\r\n  onSort?: (column: any, sortDirection: string) => void;\r\n  persistTableHead?: boolean;\r\n  sortFunction?: (rows: any[], field: any, direction: 'asc' | 'desc') => any[];\r\n  noHeader?: boolean;\r\n  dense?: boolean;\r\n  pagination?: boolean;\r\n  paginationServer?: boolean;\r\n  paginationTotalRows?: number;\r\n  subHeaderAlign?: string;\r\n  [key: string]: any;\r\n}\r\n\r\nfunction RKITable(props: RKITableProps) {\r\n  const { t } = useTranslation('common');\r\n  const paginationComponentOptions = {\r\n    rowsPerPageText: t('Rowsperpage'),\r\n    };\r\n  const {\r\n    columns,\r\n    data,\r\n    totalRows,\r\n    resetPaginationToggle,\r\n    subheader,\r\n    subHeaderComponent,\r\n    handlePerRowsChange,\r\n    handlePageChange,\r\n    rowsPerPage,\r\n    defaultRowsPerPage,\r\n    selectableRows,\r\n    loading,\r\n    pagServer,\r\n    onSelectedRowsChange,\r\n    clearSelectedRows,\r\n    sortServer,\r\n    onSort,\r\n    persistTableHead,\r\n    sortFunction,\r\n    // Filter out any unknown props that might cause DOM warnings\r\n    ...otherProps\r\n  } = props;\r\n\r\n  // Only pass known DataTable props to avoid DOM warnings\r\n  const dataTableProps = {\r\n    paginationComponentOptions,\r\n    noDataComponent: t(\"NoData\"),\r\n    noHeader: true,\r\n    columns,\r\n    data: data || [],\r\n    dense: true,\r\n    paginationResetDefaultPage: resetPaginationToggle,\r\n    subHeader: subheader,\r\n    progressPending: loading,\r\n    subHeaderComponent,\r\n    pagination: true,\r\n    paginationServer: pagServer,\r\n    paginationPerPage: defaultRowsPerPage ? defaultRowsPerPage : 10,\r\n    paginationRowsPerPageOptions: rowsPerPage ? rowsPerPage : [10, 15, 20, 25, 30],\r\n    paginationTotalRows: totalRows,\r\n    onChangeRowsPerPage: handlePerRowsChange,\r\n    onChangePage: handlePageChange,\r\n    selectableRows,\r\n    onSelectedRowsChange,\r\n    clearSelectedRows,\r\n    progressComponent: <CustomLoader/>,\r\n    sortIcon: <i className=\"sort-icon fas fa-exchange-alt\" />,\r\n    sortServer,\r\n    onSort,\r\n    sortFunction,\r\n    persistTableHead,\r\n    className: \"rki-table\"\r\n  };\r\n  return (\r\n    <DataTable {...dataTableProps} />\r\n  )\r\n};\r\n\r\nRKITable.defaultProps = {\r\n  subHeader: false,\r\n  pagination: true,\r\n  totalRows: null,\r\n  pagServer: true,\r\n  onSelectedRowsChange: null,\r\n  clearSelectedRows: false,\r\n  sortServer: false,\r\n  persistTableHead: false\r\n}\r\n\r\nexport default RKITable;\r\n\r\n", "\n    (window.__NEXT_P = window.__NEXT_P || []).push([\n      \"/adminsettings/projectstatuses/projectstatusTable\",\n      function () {\n        return require(\"private-next-pages/adminsettings/projectstatuses/projectstatusTable.tsx\");\n      }\n    ]);\n    if(module.hot) {\n      module.hot.dispose(function () {\n        window.__NEXT_P.push([\"/adminsettings/projectstatuses/projectstatusTable\"])\n      });\n    }\n  "], "names": ["_props", "t", "useTranslation", "ProjectstatusTable", "tabledata", "setDataToTable", "useState", "setLoading", "totalRows", "setTotalRows", "perPage", "setPerPage", "isModalShow", "setModal", "selectProjectstatus", "setSelectProjectstatus", "projectstatusParams", "sort", "title", "limit", "page", "query", "columns", "name", "selector", "cell", "div", "Link", "href", "as", "d", "_id", "i", "className", "a", "onClick", "userAction", "getProjectstatusData", "response", "apiService", "get", "data", "length", "totalCount", "handlePerRowsChange", "newPerPage", "row", "modalConfirm", "remove", "toast", "success", "error", "modalHide", "useEffect", "Modal", "show", "onHide", "Header", "closeButton", "Title", "Body", "Footer", "<PERSON><PERSON>", "variant", "RKITable", "pagServer", "handlePageChange", "props", "paginationComponentOptions", "rowsPerPageText", "resetPaginationToggle", "subheader", "subHeaderComponent", "rowsPerPage", "defaultRowsPerPage", "selectableRows", "loading", "onSelectedRowsChange", "clearSelectedRows", "sortServer", "onSort", "persistTableHead", "sortFunction", "otherProps", "dataTableProps", "noDataComponent", "<PERSON><PERSON><PERSON><PERSON>", "dense", "paginationResetDefaultPage", "subHeader", "progressPending", "pagination", "paginationServer", "paginationPerPage", "paginationRowsPerPageOptions", "paginationTotalRows", "onChangeRowsPerPage", "onChangePage", "progressComponent", "CustomLoader", "sortIcon", "DataTable", "defaultProps"], "sourceRoot": "", "ignoreList": []}