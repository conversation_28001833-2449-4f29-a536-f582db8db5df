"use strict";exports.id=72,exports.ids=[72],exports.modules={60072:(e,t,r)=>{r.a(e,async(e,a)=>{try{r.r(t),r.d(t,{default:()=>p});var o=r(8732);r(82015);var s=r(83551),l=r(49481),n=r(95970),i=r(63349),c=r(92730),y=r(68277),d=e([n,y]);[n,y]=d.then?(await d)():d;let p=e=>(0,o.jsx)(o.Fragment,{children:(0,o.jsxs)(s.A,{children:[(0,o.jsxs)(l.A,{className:"ps-md-0",md:7,children:[(0,o.jsx)(n.default,{operation:e.operationData,routeData:e.routeData,editData:e.editAccess}),(0,o.jsx)(i.A,{description:e.operationData.description}),(0,o.jsx)(y.default,{operation:e.operationData})]}),(0,o.jsx)(l.A,{className:"pe-md-0",md:5,children:(0,o.jsx)(c.A,{mapdata:e.operationData})})]})});a()}catch(e){a(e)}})},68277:(e,t,r)=>{r.a(e,async(e,a)=>{try{r.r(t),r.d(t,{default:()=>d});var o=r(8732);r(82015);var s=r(54131),l=r(82053),n=r(83551),i=r(49481),c=r(88751),y=e([s]);s=(y.then?(await y)():y)[0];let d=e=>{let{t}=(0,c.useTranslation)("common");return(0,o.jsx)(o.Fragment,{children:(0,o.jsx)("div",{className:"operationStats",children:(0,o.jsxs)(n.A,{children:[(0,o.jsxs)(i.A,{className:"operationInfo-Items",md:6,children:[(0,o.jsx)("div",{className:"operationIcon",children:(0,o.jsx)(l.FontAwesomeIcon,{icon:s.faUsers,color:"#fff",size:"2x"})}),(0,o.jsxs)("div",{className:"operationInfo",children:[(0,o.jsx)("h5",{children:t("Partners")}),(0,o.jsx)("h4",{children:e.operation.partners.length})]})]}),(0,o.jsxs)(i.A,{className:"operationInfo-Items",md:6,children:[(0,o.jsx)("div",{className:"operationIcon",children:(0,o.jsx)(l.FontAwesomeIcon,{icon:s.faBell,color:"#fff",size:"2x"})}),(0,o.jsxs)("div",{className:"operationInfo",children:[(0,o.jsx)("h5",{children:t("ActivitiesinField")}),(0,o.jsx)("h4",{children:e.operation.timeline.length})]})]})]})})})};a()}catch(e){a(e)}})},72953:(e,t,r)=>{r.d(t,{A:()=>m});var a=r(8732);r(82015);var o=r(94696);let s=({position:e,onCloseClick:t,children:r})=>(0,a.jsx)(o.InfoWindow,{position:e,onCloseClick:t,children:(0,a.jsx)("div",{children:r})}),l="labels.text.fill",n="labels.text.stroke",i="road.highway",c="geometry.stroke",y=[{elementType:"geometry",stylers:[{color:"#1d2c4d"}]},{elementType:l,stylers:[{color:"#8ec3b9"}]},{elementType:n,stylers:[{color:"#1a3646"}]},{featureType:"administrative",elementType:"geometry",stylers:[{visibility:"off"}]},{featureType:"administrative.country",elementType:c,stylers:[{color:"#4b6878"}]},{featureType:"administrative.land_parcel",elementType:"labels",stylers:[{visibility:"off"}]},{featureType:"administrative.land_parcel",elementType:l,stylers:[{color:"#64779e"}]},{featureType:"administrative.province",elementType:c,stylers:[{color:"#4b6878"}]},{featureType:"landscape.man_made",elementType:c,stylers:[{color:"#334e87"}]},{featureType:"landscape.natural",elementType:"geometry",stylers:[{color:"#023e58"}]},{featureType:"poi",stylers:[{visibility:"off"}]},{featureType:"poi",elementType:"geometry",stylers:[{color:"#283d6a"}]},{featureType:"poi",elementType:"labels.text",stylers:[{visibility:"off"}]},{featureType:"poi",elementType:l,stylers:[{color:"#6f9ba5"}]},{featureType:"poi",elementType:n,stylers:[{color:"#1d2c4d"}]},{featureType:"poi.park",elementType:"geometry.fill",stylers:[{color:"#023e58"}]},{featureType:"poi.park",elementType:l,stylers:[{color:"#3C7680"}]},{featureType:"road",stylers:[{visibility:"off"}]},{featureType:"road",elementType:"geometry",stylers:[{color:"#304a7d"}]},{featureType:"road",elementType:"labels.icon",stylers:[{visibility:"off"}]},{featureType:"road",elementType:l,stylers:[{color:"#98a5be"}]},{featureType:"road",elementType:n,stylers:[{color:"#1d2c4d"}]},{featureType:i,elementType:"geometry",stylers:[{color:"#2c6675"}]},{featureType:i,elementType:c,stylers:[{color:"#255763"}]},{featureType:i,elementType:l,stylers:[{color:"#b0d5ce"}]},{featureType:i,elementType:n,stylers:[{color:"#023e58"}]},{featureType:"road.local",elementType:"labels",stylers:[{visibility:"off"}]},{featureType:"transit",stylers:[{visibility:"off"}]},{featureType:"transit",elementType:l,stylers:[{color:"#98a5be"}]},{featureType:"transit",elementType:n,stylers:[{color:"#1d2c4d"}]},{featureType:"transit.line",elementType:"geometry.fill",stylers:[{color:"#283d6a"}]},{featureType:"transit.station",elementType:"geometry",stylers:[{color:"#3a4762"}]},{featureType:"water",elementType:"geometry",stylers:[{color:"#0e1626"}]},{featureType:"water",elementType:l,stylers:[{color:"#4e6d70"}]}];var d=r(44233),p=r(40691);let m=({markerInfo:e,activeMarker:t,initialCenter:r,children:l,height:n=300,width:i="114%",language:c,zoom:m=1,minZoom:u=1,onClose:f})=>{let{locale:T}=(0,d.useRouter)(),{isLoaded:h,loadError:x}=(0,p._)(),j={width:i,height:"number"==typeof n?`${n}px`:n};return x?(0,a.jsx)("div",{children:"Error loading maps"}):h?(0,a.jsx)("div",{className:"map-container",children:(0,a.jsx)("div",{className:"mapprint",style:{width:i,height:n,position:"relative"},children:(0,a.jsxs)(o.GoogleMap,{mapContainerStyle:j,center:r||{lat:52.520017,lng:13.404195},zoom:m,onLoad:e=>{e.setOptions({styles:y})},options:{minZoom:u,draggable:!0,keyboardShortcuts:!1,streetViewControl:!1,panControl:!1,clickableIcons:!1,mapTypeControl:!1,fullscreenControl:!0},children:[l,e&&t&&t.getPosition&&(0,a.jsx)(s,{position:t.getPosition(),onCloseClick:()=>{console.log("close click"),f?.()},children:e})]})})}):(0,a.jsx)("div",{children:"Loading Maps..."})}},81426:(e,t,r)=>{r.a(e,async(e,a)=>{try{r.d(t,{A:()=>p});var o=r(8732),s=r(14062),l=r(54131),n=r(82015),i=r(82053),c=r(63487),y=e([s,l,c]);[s,l,c]=y.then?(await y)():y;let d={operation:"Operation",institution:"Institution",event:"Event",project:"Project",vspace:"Vspace"},p=(0,s.connect)(e=>e)(e=>{let{user:t,entityId:r,entityType:a}=e,[s,y]=(0,n.useState)(!1),[p,m]=(0,n.useState)(""),u=async()=>{if(!t?._id)return;let e=await c.A.get("/flag",{query:{entity_id:r,user:t._id,onModel:d[a]}});e&&e.data&&e.data.length>0&&(m(e.data[0]),y(!0))},f=async e=>{if(e.preventDefault(),!t?._id)return;let o=!s,l={entity_type:a,entity_id:r,user:t._id,onModel:d[a]};if(o){let e=await c.A.post("/flag",l);e&&e._id&&(m(e),y(o))}else{let e=await c.A.remove(`/flag/${p._id}`);e&&e.n&&y(o)}};return(0,n.useEffect)(()=>{u()},[]),(0,o.jsx)("div",{className:"subscribe-flag",children:(0,o.jsxs)("a",{href:"",onClick:f,children:[(0,o.jsx)("span",{className:"check",children:s?(0,o.jsx)(i.FontAwesomeIcon,{className:"clickable checkIcon",icon:l.faCheckCircle,color:"#00CC00"}):(0,o.jsx)(i.FontAwesomeIcon,{className:"clickable minusIcon",icon:l.faPlusCircle,color:"#fff"})}),(0,o.jsx)(i.FontAwesomeIcon,{className:"bookmark",icon:l.faBookmark,color:"#d4d4d4"})]})})});a()}catch(e){a(e)}})},89364:(e,t,r)=>{r.d(t,{A:()=>s});var a=r(8732);r(82015);var o=r(94696);let s=({name:e="Marker",id:t="",countryId:r="",type:s,icon:l,position:n,onClick:i,title:c,draggable:y=!1})=>n&&"number"==typeof n.lat&&"number"==typeof n.lng?(0,a.jsx)(o.Marker,{position:n,icon:l,title:c||e,draggable:y,onClick:a=>{i&&i({name:e,id:t,countryId:r,type:s,position:n},{position:n,getPosition:()=>n},a)}}):null},92730:(e,t,r)=>{r.d(t,{A:()=>i});var a=r(8732),o=r(82015),s=r(72953),l=r(89364),n=r(88751);let i=e=>{let{i18n:t}=(0,n.useTranslation)("common"),r=t.language,{mapdata:i}=e,[c,y]=(0,o.useState)({}),[d,p]=(0,o.useState)({}),[m,u]=(0,o.useState)({}),f=()=>{p(null),u(null)},T=()=>{y({title:i.title,id:i._id,lat:i.country&&i.country.coordinates?parseFloat(i.country.coordinates[0].latitude):null,lng:i.country&&i.country.coordinates?parseFloat(i.country.coordinates[0].longitude):null})};return(0,o.useEffect)(()=>{T()},[i]),(0,a.jsxs)(a.Fragment,{children:[" ",c&&c.id?(0,a.jsx)(s.A,{onClose:f,language:r,initialCenter:{lat:c.lat,lng:c.lng},activeMarker:d,markerInfo:(0,a.jsx)(e=>{let{info:t}=e;return(0,a.jsx)("a",{children:t?.name})},{info:m}),children:(0,a.jsx)(l.A,{name:c.title,icon:{url:"/images/map-marker-white.svg"},onClick:(e,t,r)=>{f(),p(t),u({name:e.name})},position:c})}):null]})}},95970:(e,t,r)=>{r.a(e,async(e,a)=>{try{r.r(t),r.d(t,{default:()=>u});var o=r(8732);r(82015);var s=r(91353),l=r(82053),n=r(19918),i=r.n(n),c=r(54131),y=r(81426),d=r(28163),p=r(88751),m=e([c,y]);[c,y]=m.then?(await m)():m;let u=e=>{let{t}=(0,p.useTranslation)("common"),r=()=>(0,o.jsx)(o.Fragment,{children:e.editData?(0,o.jsx)(i(),{href:"/operation/[...routes]",as:`/operation/edit/${e.routeData.routes[1]}`,children:(0,o.jsxs)(s.A,{variant:"secondary",size:"sm",children:[(0,o.jsx)(l.FontAwesomeIcon,{icon:c.faPen}),"\xa0",t("Edit")]})}):""}),a=(0,d.canEditOperation)(()=>(0,o.jsx)(r,{}));return(0,o.jsx)(o.Fragment,{children:(0,o.jsxs)("section",{className:"d-flex justify-content-between",children:[(0,o.jsxs)("h4",{className:"operationTitle",children:[e.operation.title,"\xa0\xa0",e.routeData.routes&&e.routeData.routes[1]?(0,o.jsx)(a,{operation:e.operation}):null]}),(0,o.jsx)(y.A,{entityId:e.routeData.routes[1],entityType:"operation"})]})})};a()}catch(e){a(e)}})}};