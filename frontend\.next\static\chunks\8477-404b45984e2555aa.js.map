{"version": 3, "file": "static/chunks/8477-404b45984e2555aa.js", "mappings": "uIAwHA,MA9GkE,OAAC,OACjEA,CAAK,UACLC,CAAQ,GA4GKC,UA3GbC,EAAc,QA2GmBD,EAAC,UA3GA,QAClCE,EAAS,GAAG,UACZC,GAAW,CAAK,CACjB,GACOC,EAAYC,CAAAA,EAAAA,EAAAA,MAAAA,CAAMA,CAAiB,MACnC,CAACC,EAAWC,EAAa,CAAGC,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,EAAC,GAG3CC,CAAAA,EAAAA,EAAAA,SAAAA,CAASA,CAAC,KACJL,EAAUM,OAAO,EAAI,GAEnB,CAACJ,GAAaF,EAAUM,IAFa,GAEN,CAACC,SAFkB,GAEJb,IAChDM,EAAUM,CAD6C,MACtC,CAACC,SAAS,CAAGb,GAAS,GAG7C,EAAG,CAACA,EAAOQ,EAAU,EAGrB,IAAMM,EAAc,KACdR,EAAUM,OAAO,EAAIX,GACvBA,EAASK,EAAUM,GADc,IACP,CAACC,SAAS,CAExC,EAGME,EAAc,CAACC,EAAiBhB,KACpC,GAAI,oBAAOiB,SAA0B,KAGnCX,EAFAW,SAASF,WAAW,CAACC,GAAS,EAAOhB,GAAS,IAC9Cc,WACAR,EAAAA,EAAUM,OAAAA,GAAVN,EAAmBY,KAAK,EAC1B,CACF,CAFIZ,CAIJ,MACE,UAACa,MAAAA,CAAIC,UAAU,0BAA0BC,MAAO,CAAEC,OAAQ,gBAAiB,WAEzE,CADC,EACD,GAD8B,GAC9B,wBACE,WAACH,MAAAA,CAAIC,UAAU,UAAUC,MAAO,CAAEE,QAAS,MAAOC,aAAc,iBAAkBC,WAAY,SAAU,YACpG,UAACC,SAAAA,CACCC,KAAK,SACLC,QAAS,IAAMb,EAAY,QAC3BM,MAAO,CAAEQ,OAAQ,QAASN,QAAS,SAAU,WAE7C,UAACO,SAAAA,UAAO,QAEV,UAACJ,SAAAA,CACCC,KAAK,SACLC,QAAS,IAAMb,EAAY,UAC3BM,MAAO,CAAEQ,OAAQ,QAASN,QAAS,SAAU,WAE7C,UAACQ,KAAAA,UAAG,QAEN,UAACL,SAAAA,CACCC,KAAK,SACLC,QAAS,IAAMb,EAAY,aAC3BM,MAAO,CAAEQ,OAAQ,QAASN,QAAS,SAAU,WAE7C,UAACS,IAAAA,UAAE,QAEL,UAACN,SAAAA,CACCC,KAAK,SACLC,QAAS,IAAMb,EAAY,qBAC3BM,MAAO,CAAEQ,OAAQ,QAASN,QAAS,SAAU,WAC9C,OAGD,UAACG,SAAAA,CACCC,KAAK,SACLC,QAAS,IAAMb,EAAY,uBAC3BM,MAAO,CAAEQ,OAAQ,QAASN,QAAS,SAAU,WAC9C,OAGD,UAACG,SAAAA,CACCC,KAAK,SACLC,QAAS,KACP,IAAMK,EAAMC,OAAO,sBACfD,GAAKlB,EAAY,aAAckB,EACrC,EACAZ,MAAO,CAAEQ,OAAQ,QAASN,QAAS,SAAU,WAC9C,YAIH,UAACJ,MAAAA,CACCgB,IAAK7B,EACL8B,gBAAiB,CAAC/B,EAClBgC,QAASvB,EACTwB,QAAS,IAAM7B,GAAa,GAC5B8B,OAAQ,IAAM9B,GAAa,GAC3BY,MAAO,CACLE,QAAS,OACTiB,UAAWpC,EACXqC,UAAoB,EAATrC,EACXsC,SAAU,OACVC,QAAS,MACX,EACAC,mBAAkB,EAAuB,GAAdzC,EAC3B0C,gCAAgC,QAO5C,EC7GaC,EAAmD,IAC9D,GAAM,aAAEC,CAAW,UAAE9C,CAAQ,CAAE,CAAG+C,EAElC,MACE,UAAC9C,EAAoBA,CACnBF,MAAO+C,GAAe,GACtB9C,SAAU,GAAaA,EAASgD,IAGtC,EAAE,yMCPK,IAAMC,EAAyBC,CAAAA,EAAAA,QAAAA,CAAAA,CAAoBA,CAAC,CACzDC,sBAAuB,KACjBC,EAAMC,WAAW,IAAID,EAAMC,WAAW,CAACC,MAAM,IAAIF,EAAMC,WAAW,CAACC,MAAM,CAAC,aAAa,CAK7FC,CAL+F,kBAK3E,wBACtB,GAAG,0BCkCH,MA1CoD,IAClD,GAAM,CAACC,EAAYC,EAAc,CAAGhD,CAAAA,EAAAA,EAAAA,IAyCvBiD,IAzCuBjD,CAAQA,CAAS,IAC/C,GAAEkD,CAAC,CAAE,CAAGC,CAAAA,EAAAA,EAAAA,EAAAA,CAAcA,CAAC,UAOvBC,EAAiB,KACrBC,GAEF,EACMA,EAAsB,KAC1Bf,EAAMe,mBAAmB,CAACN,GAC1BC,EAAc,GAGhB,EACA,MACE,UAACM,EAAAA,CAAIA,CAAAA,UACH,WAACC,EAAAA,CAAGA,CAAAA,WACF,UAACC,EAAAA,CAAGA,CAAAA,CAACC,GAAI,EAAGC,GAAI,YACd,UAACJ,EAAAA,CAAIA,CAACK,OAAO,EACXjD,UAAU,OACVO,KAAK,OACL3B,MAAOyD,EACPa,WAAY,IACQ,SAAS,CAAvBC,EAAMC,GAAG,GACXV,IACAS,EAAME,cAAc,GAExB,EACAxE,SA7BkB,CA6BRyE,GA5BlB,GAAM,OAAE1E,CAAK,CAAE,CAAG2E,EAAEC,MAAM,CAC1BlB,EAAc1D,EAChB,MA4BM,UAACkE,EAAAA,CAAGA,CAAAA,CAACC,GAAI,EAAGC,GAAI,WACd,UAACS,EAAAA,CAAMA,CAAAA,CAACjD,QAASmC,WAAsBH,EAAE,kBAKnD,gBC8MA,MA5N8C,IAC1C,GAAM,GAAEA,CAAC,MAAEkB,CAAI,CAAE,CAAGjB,CAAAA,EAAAA,EAAAA,EAAAA,CAAcA,CAAC,UAC7BkB,EAAgC,OAAlBD,EAAKE,QAAQ,CAAY,KAAOF,EAAKE,QAAQ,CAC3D,SAAEC,CAAO,YAAEC,CAAU,CAAE,CAAGC,CAAAA,EAAAA,EAAAA,UAAAA,CAAUA,CAACC,EAAAA,EAAaA,EAClD,CAACC,EAAaC,EAAe,CAAG5E,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAmB,EAAE,EAC7D,CAAC6E,EAAaC,EAAS,CAAG9E,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAC,CAAC,GACpC,CAAC+E,EAAaC,EAAe,CAAGhF,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAC,IACzC,CAACiF,EAAUC,EAAY,CAAGlF,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAC,IACnC,CAACmF,EAAcC,EAAgB,CAAGpF,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAC,IAC3C,CAACqF,EAAQ,CAAGrF,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAACqE,GACrB,CAACiB,EAAQC,EAAU,CAAGvF,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAC,IAE/BwF,EAAkB,UACpB,IAAMC,EAAW,MAAMC,EAAAA,CAAUA,CAACC,GAAG,CAAC,eACtC,GAAIF,GAAYA,EAASG,IAAI,EAAIH,EAASG,IAAI,CAACC,MAAM,CAAG,EAAG,CACvD,IAAMC,EAAaC,IAAAA,IAAM,CAACN,EAASG,IAAI,CAAE,CAAEI,MAAO,cAAe,GACjE,GAAIF,GAAcA,EAAWG,GAAG,CAE5B,CAF8B,MAC9Bb,EAAgBU,EAAWG,GAAG,EACvBH,EAAWG,GAAG,CAGjC,EAEMC,EAAY,MAAOjF,EAAckF,KACnC,GAAIA,EAAqB,CAMrB,IAAMV,EAAW,MAAMC,EAAAA,CAAUA,CAACC,GAAG,CAAC,WALd,CAK0BS,MAJvC,CAAEnF,KAAMA,EAAMoF,YAAaF,CAAoB,EACtDG,KAAM,CAAEN,MAAO,KAAM,EACrBO,MAAO,GACX,GAEId,GAAYA,EAASG,IAAI,EAAE,EAEbG,IAAAA,MAAQ,CAACN,EACRe,IADqB,CAAE,CAAE,CAAC1C,UADd,OAAL7C,GACuB,CAAEqB,EAAMmE,EAAE,GAG/D,CACAlB,EAAUZ,GAAeA,EAAYkB,MAAM,CAAG,EAAI,GAAK3C,EAAE,iBAC7D,EAEAjD,CAAAA,EAAAA,EAAAA,SAAAA,CAASA,CAAC,KASNyG,CARoB,UAChB,IAAMC,EAAc,MAAMjB,EAAAA,CAAUA,CAACkB,IAAI,CAAC,uBAAwB,CAAC,GAC/DD,GAAeA,EAAY1B,QAAQ,EAAE,EACzB0B,EAAY1B,QAAQ,EAEpC,IAAM4B,EAAkB,MAAMrB,GAC9B,OAAMU,EAAU5D,EAAMrB,IAAI,CAAE4F,GAChC,GAEJ,EAAG,EAAE,EAEL,IAAMxD,EAAsB,MAAON,IAC/B,GAAIA,EAAY,CACZ,IAAM6C,EAAY,CACdI,MAAOjD,EACP+D,MAAO,EAAE,CACT7F,KAAMqB,EAAMrB,IAAI,CAChBoF,YAAalB,EACb4B,sBAAsB,EACtBC,KAAM,CACF/B,SAAUA,CACd,CACJ,EACA,OAAQ3C,EAAMrB,IAAI,EACd,IAAK,YACD2E,EAAK,EAAD,cAAoB,CAAGtD,EAAMmE,EAAE,CACnC,KACJ,KAAK,QACDb,EAAK,EAAD,UAAgB,CAAGtD,EAAMmE,EAAE,CAC/B,KACJ,KAAK,UACDb,EAAK,EAAD,YAAkB,CAAGtD,EAAMmE,EAAE,CACjC,KACJ,KAAK,SACDb,EAAK,EAAD,WAAiB,CAAGtD,EAAMmE,EAAE,CAChC,KACJ,KAAK,UACDb,EAAK,EAAD,YAAkB,CAAGtD,EAAMmE,EAAE,CACjC,KACJ,KAAK,SACDb,EAAK,EAAD,WAAiB,CAAGtD,EAAMmE,EAAE,CAChC,KACJ,KAAK,cACDb,EAAK,EAAD,gBAAsB,CAAGtD,EAAMmE,EAAE,CAK7C,IAAMQ,EAAa,MAAMvB,EAAAA,CAAUA,CAACkB,IAAI,CAAC,UAAWhB,GACpDhB,EAAe,IAAID,EAAasC,EAAW,EAC3CC,aAAaC,OAAO,CAAC,UAAWC,KAAKC,SAAS,CAAC,IAAI1C,EAAaiB,EAAK,GACrEpB,EAAW,CAACyC,KAAe1C,EAAQ,CACvC,CACJ,EAEM+C,EAA0B,IAC5BtC,EAAeuC,EACnB,EAEMC,EAAY,MAAO1D,IAErBa,CAAW,CAACb,EAAI,CAACgD,KAAK,CAACW,IAAI,CAAC,CACxBT,KAAM/B,EACNyC,IAAK3C,EACL4C,KAAM,IAAIC,IACd,GAGAhD,EAAe,IAAID,EAAY,EAC/BG,EAAS,CAAC,GACVE,EAAe,IAGf,MAAMU,EAAAA,CAAUA,CAACmC,KAAK,CAAC,YAAiC,OAArBlD,CAAW,CAACb,EAAI,CAACmC,GAAG,EAAItB,CAAW,CAACb,EAAI,EAG3EoD,aAAaC,OAAO,CAAC,UAAWC,KAAKC,SAAS,CAAC1C,GACnD,EAEMmD,EAAe,IACV,CAAEC,OAAQC,EAAY,EAE3BC,EAAoB,GAElB,UAACzE,EAAAA,CAAGA,CAAAA,CAACC,GAAI,WACL,UAACU,EAAAA,CAAMA,CAAAA,CACH+D,QAAQ,YACRC,KAAK,KACLjH,QAAS,KACL4D,EAASsD,EAAcC,CAAC,CAC5B,WAECnF,EAAE,aAKboF,EAAmB9F,EAAuB,IAAM,UAACS,EAAaA,CAACI,GAAtBb,cAAqBS,GAAsBI,KACpFkF,EAAuB/F,EAAuB,GAChD,UAACyF,EAAAA,CAAkBI,EAAGG,EAAgBH,CAAC,IAE3C,MACI,WAACI,EAAAA,CAASA,CAAAA,CAACC,KAAK,cACZ,UAACJ,EAAAA,CAAAA,GACA3D,GAAqC,GAAtBA,EAAYkB,MAAM,CAC9B,UAACpF,MAAAA,UACG,UAACkI,IAAAA,CAAEjI,UAAU,wDAAgD4E,MAGjE,+BACKX,EAAYiE,GAAG,CAAC,CAACC,EAAKR,IACnB,EAAQrC,KAAK,CAEL,WAACvF,MAAAA,CAAIC,UAAU,qBACX,WAAC6C,EAAAA,CAAGA,CAAAA,WACA,UAACC,EAAAA,CAAGA,CAAAA,CAACC,GAAI,YACL,WAAChD,MAAAA,CAAIC,UAAU,uBACX,UAACD,MAAAA,CAAIC,UAAU,sBACX,UAACoI,EAAAA,CAAeA,CAAAA,CAACC,KAAMC,EAAAA,GAAMA,CAAEC,MAAM,OAAOd,KAAK,SAErD,WAAC1H,MAAAA,CAAIC,UAAU,qBACX,WAACD,MAAAA,CAAIC,UAAU,qBACX,UAACwI,OAAAA,CAAKxI,UAAU,wBACXmI,GAAOA,EAAI7B,IAAI,EAAI6B,EAAI7B,IAAI,CAAC/B,QAAQ,GACjC,IAAI,IACV,IACF,UAACiE,OAAAA,CAAKxI,UAAU,oBACXyI,IAAAA,GAAU,CAACN,EAAIO,UAAU,EAAEC,MAAM,CAAChE,GAASiE,OAAO,QAG3D,UAAC7I,MAAAA,CAAIC,UAAU,0BAAkBmI,EAAI7C,KAAK,WAIrC,CAAC,IAAjBnB,GAAsBwD,IAAMxD,EAAc,KACvC,UAAC0D,EAAAA,CAAqBF,EAAGA,OAIhCQ,EAAI/B,KAAK,EACN+B,EAAI/B,KAAK,CAAC8B,GAAG,CAAC,CAACW,EAAWC,IACfC,CAuCnD,SAASA,CAAsB,CAAEF,CAAc,CAAElE,CAAe,CAAEyC,CAAyD,EACvH,MACI,UAACrH,MAAAA,CAAIC,UAAU,qBACX,UAACD,MAAAA,CAAIE,MAAO,CAAE+I,WAAY,OAAQC,aAAc,OAAQC,UAAW,MAAO,WACtE,WAACnJ,MAAAA,CAAIC,UAAU,uBACX,UAACD,MAAAA,CAAIC,UAAU,sBACX,UAACoI,EAAAA,CAAeA,CAAAA,CAACC,KAAMC,EAAAA,GAAMA,CAAEC,MAAM,OAAOd,KAAK,SAErD,WAAC1H,MAAAA,CAAIC,UAAU,qBACX,WAACD,MAAAA,CAAIC,UAAU,qBACX,UAACwI,OAAAA,CAAKxI,UAAU,wBAAgB6I,GAAaA,EAAUvC,IAAI,GAAQ,KAAG,IACtE,UAACkC,OAAAA,CAAKxI,UAAU,oBACX6I,EAAU5B,IAAI,CACTwB,IAAAA,GAAU,CAACI,EAAU5B,IAAI,EAAE0B,MAAM,CAAChE,GAASiE,OAAO,GAClDH,MAASG,OAAO,GAAVH,KAGpB,UAAC1I,MAAAA,CAAIC,UAAU,0BACX,UAACD,MAAAA,CAAIoJ,wBAAyB/B,EAAayB,EAAU7B,GAAG,eAhB5C8B,GAuBxC,EAhE4DA,EAAOD,EAAWlE,EAASyC,IAElC,CAAC,IAAjBjD,GAAsBwD,IAAMxD,EACzB,WAACpE,MAAAA,WACG,UAAC2B,EAAAA,CAAeA,CAAAA,CAACC,YAAa0C,EAAaxF,SAAU,GAAiB+H,EAAwBwC,KAAS,IACvG,UAACC,KAAAA,CAAAA,GACD,UAAC5F,EAAAA,CAAMA,CAAAA,CACHxE,SAAU,CAACoF,EAAYc,MAAM,CAC7BsC,KAAK,KACLD,QAAQ,OACRhH,QAAS,IAAMsG,EAAUa,YAExBnF,EAAE,UACG,IAAI,OAEd,UAACiB,EAAAA,CAAMA,CAAAA,CACH+D,QAAQ,YACRC,KAAK,KACLjH,QAAS,KACL4D,EAAS,CAAC,EACd,WAEC5B,EAAE,eAGX,OArDuBmF,GAyD5B,UAOnC", "sources": ["webpack://_N_E/./components/common/SimpleRichTextEditor.tsx", "webpack://_N_E/./shared/quill-editor/quill-editor.component.tsx", "webpack://_N_E/./components/common/permissions.tsx", "webpack://_N_E/./components/common/AddDiscussion.tsx", "webpack://_N_E/./components/common/disussion.tsx"], "sourcesContent": ["import React, { useRef, useEffect, useState } from 'react';\r\n\r\ninterface SimpleRichTextEditorProps {\r\n  value: string;\r\n  onChange: (content: string) => void;\r\n  placeholder?: string;\r\n  height?: number;\r\n  disabled?: boolean;\r\n}\r\n\r\nconst SimpleRichTextEditor: React.FC<SimpleRichTextEditorProps> = ({\r\n  value,\r\n  onChange,\r\n  placeholder = 'Write something...',\r\n  height = 300,\r\n  disabled = false,\r\n}) => {\r\n  const editorRef = useRef<HTMLDivElement>(null);\r\n  const [isFocused, setIsFocused] = useState(false);\r\n\r\n  // Initialize editor with HTML content\r\n  useEffect(() => {\r\n    if (editorRef.current && typeof window !== 'undefined') {\r\n      // Only update if the editor doesn't have focus to prevent cursor jumping\r\n      if (!isFocused && editorRef.current.innerHTML !== value) {\r\n        editorRef.current.innerHTML = value || '';\r\n      }\r\n    }\r\n  }, [value, isFocused]);\r\n\r\n  // Handle content changes\r\n  const handleInput = () => {\r\n    if (editorRef.current && onChange) {\r\n      onChange(editorRef.current.innerHTML);\r\n    }\r\n  };\r\n\r\n  // Simple toolbar buttons\r\n  const execCommand = (command: string, value?: string) => {\r\n    if (typeof document !== 'undefined') {\r\n      document.execCommand(command, false, value || '');\r\n      handleInput();\r\n      editorRef.current?.focus();\r\n    }\r\n  };\r\n\r\n  return (\r\n    <div className=\"simple-rich-text-editor\" style={{ border: '1px solid #ccc' }}>\r\n      {typeof window !== 'undefined' && (\r\n      <>\r\n        <div className=\"toolbar\" style={{ padding: '8px', borderBottom: '1px solid #ccc', background: '#f5f5f5' }}>\r\n            <button\r\n              type=\"button\"\r\n              onClick={() => execCommand('bold')}\r\n              style={{ margin: '0 5px', padding: '3px 8px' }}\r\n            >\r\n              <strong>B</strong>\r\n            </button>\r\n            <button\r\n              type=\"button\"\r\n              onClick={() => execCommand('italic')}\r\n              style={{ margin: '0 5px', padding: '3px 8px' }}\r\n            >\r\n              <em>I</em>\r\n            </button>\r\n            <button\r\n              type=\"button\"\r\n              onClick={() => execCommand('underline')}\r\n              style={{ margin: '0 5px', padding: '3px 8px' }}\r\n            >\r\n              <u>U</u>\r\n            </button>\r\n            <button\r\n              type=\"button\"\r\n              onClick={() => execCommand('insertOrderedList')}\r\n              style={{ margin: '0 5px', padding: '3px 8px' }}\r\n            >\r\n              OL\r\n            </button>\r\n            <button\r\n              type=\"button\"\r\n              onClick={() => execCommand('insertUnorderedList')}\r\n              style={{ margin: '0 5px', padding: '3px 8px' }}\r\n            >\r\n              UL\r\n            </button>\r\n            <button\r\n              type=\"button\"\r\n              onClick={() => {\r\n                const url = prompt('Enter the link URL');\r\n                if (url) execCommand('createLink', url);\r\n              }}\r\n              style={{ margin: '0 5px', padding: '3px 8px' }}\r\n            >\r\n              Link\r\n            </button>\r\n          </div>\r\n          <div\r\n            ref={editorRef}\r\n            contentEditable={!disabled}\r\n            onInput={handleInput}\r\n            onFocus={() => setIsFocused(true)}\r\n            onBlur={() => setIsFocused(false)}\r\n            style={{\r\n              padding: '15px',\r\n              minHeight: height,\r\n              maxHeight: height * 2,\r\n              overflow: 'auto',\r\n              outline: 'none',\r\n            }}\r\n            data-placeholder={!value ? placeholder : ''}\r\n            suppressContentEditableWarning={true}\r\n          >\r\n          </div>\r\n      </>\r\n      )}\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default SimpleRichTextEditor;\r\n", "// React Imports\r\nimport React from \"react\";\r\nimport SimpleRichTextEditor from \"../../components/common/SimpleRichTextEditor\";\r\n\r\ninterface IEditorComponentProps {\r\n  initContent: string | undefined;\r\n  onChange: Function;\r\n}\r\n\r\nexport const EditorComponent: React.FC<IEditorComponentProps> = (props) => {\r\n  const { initContent, onChange } = props;\r\n\r\n  return (\r\n    <SimpleRichTextEditor\r\n      value={initContent || \"\"}\r\n      onChange={(content) => onChange(content)}\r\n    />\r\n  );\r\n};\r\n", "//Import services/components\r\nimport connectedAuthWrapper from 'redux-auth-wrapper/connectedAuthWrapper';\r\n\r\ninterface AppState {\r\n  permissions?: {\r\n    update?: {\r\n      [key: string]: boolean;\r\n    };\r\n  };\r\n}\r\n\r\nexport const canAddDiscussionUpdate = connectedAuthWrapper({\r\n  authenticatedSelector: (state: AppState) => {\r\n    if (state.permissions && state.permissions.update && state.permissions.update['create:any']) {\r\n      return true;\r\n    }\r\n    return false;\r\n  },\r\n  wrapperDisplayName: 'CanAddDiscussionUpdate',\r\n});\r\n\r\nexport default canAddDiscussionUpdate;\r\n", "//Import Library\r\nimport React, { useState } from 'react'\r\nimport { Button, Col, Form, Row } from \"react-bootstrap\";\r\n\r\n//Import services/components\r\nimport { useTranslation } from 'next-i18next';\r\n\r\ninterface AddDiscussionProps {\r\n  handleDiscussSubmit: (discussion: string) => void;\r\n}\r\n\r\nconst AddDiscussion: React.FC<AddDiscussionProps> = (props) => {\r\n  const [discussion, setDiscussion] = useState<string>(\"\");\r\n  const { t } = useTranslation('common')\r\n\r\n  const handleDiscussChange = (e: React.ChangeEvent<HTMLInputElement>) => {\r\n    const { value } = e.target;\r\n    setDiscussion(value);\r\n  };\r\n\r\n  const handleKeypress = () => {\r\n    handleDiscussSubmit();\r\n\r\n  }\r\n  const handleDiscussSubmit = () => {\r\n    props.handleDiscussSubmit(discussion);\r\n    setDiscussion(\"\");\r\n\r\n\r\n  };\r\n  return (\r\n    <Form>\r\n      <Row>\r\n        <Col sm={9} lg={10}>\r\n          <Form.Control\r\n            className='mb-2'\r\n            type=\"text\"\r\n            value={discussion}\r\n            onKeyPress={(event: React.KeyboardEvent<HTMLInputElement>) => {\r\n              if (event.key === \"Enter\") {\r\n                handleKeypress();\r\n                event.preventDefault();\r\n              }\r\n            }}\r\n            onChange={handleDiscussChange} />\r\n        </Col>\r\n        <Col sm={3} lg={2}>\r\n          <Button onClick={handleDiscussSubmit}>{t(\"submit\")}</Button>\r\n        </Col>\r\n      </Row>\r\n    </Form>\r\n  )\r\n};\r\nexport default AddDiscussion;\r\n", "//Import Library\r\nimport React, { useState, useEffect, useContext } from \"react\";\r\nimport moment from \"moment\";\r\nimport \"moment/locale/de\";\r\nimport { <PERSON><PERSON>, Row, Col, Container } from \"react-bootstrap\";\r\nimport { FontAwesomeIcon } from \"@fortawesome/react-fontawesome\";\r\nimport { faUser } from \"@fortawesome/free-solid-svg-icons\";\r\nimport _ from \"lodash\";\r\n\r\n//Import services/components\r\nimport apiService from \"../../services/apiService\";\r\nimport { UpdateContext } from \"../../context/update\";\r\nimport { canAddDiscussionUpdate } from \"./permissions\";\r\nimport AddDiscussion from \"./AddDiscussion\";\r\nimport { useTranslation } from 'next-i18next';\r\nimport { EditorComponent } from \"../../shared/quill-editor/quill-editor.component\";\r\n\r\n// Define the discussion item type\r\ninterface DiscussionItem {\r\n    _id: string;\r\n    title: string;\r\n    reply: Array<{\r\n        user: string;\r\n        msg: string;\r\n        time: Date;\r\n    }>;\r\n    user: {\r\n        username: string;\r\n    };\r\n    created_at: string;\r\n    [key: string]: any;\r\n}\r\n\r\ninterface DiscussionProps {\r\n    id: string | null;\r\n    type: 'operation' | 'event' | 'project' | 'vspace' | 'country' | 'hazard' | 'institution';\r\n}\r\n\r\nconst Discussion: React.FC<DiscussionProps> = (props) => {\r\n    const { t, i18n } = useTranslation('common');\r\n    const currentLang = i18n.language === \"fr\" ? \"en\" : i18n.language;\r\n    const { updates, setUpdates } = useContext(UpdateContext);\r\n    const [discussList, setDiscussList] = useState<DiscussionItem[]>([]);\r\n    const [replyToggle, setReply] = useState(-1);\r\n    const [description, setDescription] = useState(\"\");\r\n    const [username, setUserName] = useState(\"\");\r\n    const [updateTypeId, setUpdateTypeId] = useState(\"\");\r\n    const [_moment] = useState(currentLang);\r\n    const [nodata, setNoData] = useState(\"\");\r\n\r\n    const fetchUpdateType = async () => {\r\n        const response = await apiService.get(\"/updatetype\");\r\n        if (response && response.data && response.data.length > 0) {\r\n            const updateType = _.find(response.data, { title: \"Conversation\" });\r\n            if (updateType && updateType._id) {\r\n                setUpdateTypeId(updateType._id);\r\n                return updateType._id;\r\n            }\r\n        }\r\n    };\r\n\r\n    const fetchData = async (type: string, updateTypeIdinitial: string) => {\r\n        if (updateTypeIdinitial) {\r\n            const operationParams = {\r\n                query: { type: type, update_type: updateTypeIdinitial },\r\n                sort: { title: \"asc\" },\r\n                limit: \"~\",\r\n            };\r\n            const response = await apiService.get(\"/updates\", operationParams);\r\n            if (response && response.data) {\r\n                const key = `parent_${type}`;\r\n                const _data = _.filter(response.data, { [key]: props.id });\r\n                setDiscussList(_data);\r\n            }\r\n        }\r\n        setNoData(discussList && discussList.length > 0 ? \"\" : t(\"NoFilesFound!\"));\r\n    };\r\n\r\n    useEffect(() => {\r\n        const initialData = async () => {\r\n            const currentUser = await apiService.post(\"/users/getLoggedUser\", {});\r\n            if (currentUser && currentUser.username) {\r\n                setUserName(currentUser.username);\r\n            }\r\n            const updateTypeIdset = await fetchUpdateType();\r\n            await fetchData(props.type, updateTypeIdset);\r\n        };\r\n        initialData();\r\n    }, []);\r\n\r\n    const handleDiscussSubmit = async (discussion: string) => {\r\n        if (discussion) {\r\n            const data: any = {\r\n                title: discussion,\r\n                reply: [],\r\n                type: props.type,\r\n                update_type: updateTypeId,\r\n                show_as_announcement: false,\r\n                user: {\r\n                    username: username,\r\n                },\r\n            };\r\n            switch (props.type) {\r\n                case \"operation\":\r\n                    data[\"parent_operation\"] = props.id;\r\n                    break;\r\n                case \"event\":\r\n                    data[\"parent_event\"] = props.id;\r\n                    break;\r\n                case \"project\":\r\n                    data[\"parent_project\"] = props.id;\r\n                    break;\r\n                case \"vspace\":\r\n                    data[\"parent_vspace\"] = props.id;\r\n                    break;\r\n                case \"country\":\r\n                    data[\"parent_country\"] = props.id;\r\n                    break;\r\n                case \"hazard\":\r\n                    data[\"parent_hazard\"] = props.id;\r\n                    break;\r\n                case \"institution\":\r\n                    data[\"parent_institution\"] = props.id;\r\n                    break;\r\n                default:\r\n                    break;\r\n            }\r\n            const updateresp = await apiService.post(\"updates\", data);\r\n            setDiscussList([...discussList, updateresp]);\r\n            localStorage.setItem(\"discuss\", JSON.stringify([...discussList, data]));\r\n            setUpdates([updateresp, ...updates]);\r\n        }\r\n    };\r\n\r\n    const handleDescriptionChange = (val: string) => {\r\n        setDescription(val);\r\n    };\r\n\r\n    const showReply = async (key: number) => {\r\n        // Add the reply to the discussion item\r\n        discussList[key].reply.push({\r\n            user: username,\r\n            msg: description,\r\n            time: new Date(),\r\n        });\r\n\r\n        // Update the state with the modified discussList\r\n        setDiscussList([...discussList]);\r\n        setReply(-1);\r\n        setDescription(\"\");\r\n\r\n        // Update the backend\r\n        await apiService.patch(`/updates/${discussList[key]._id}`, discussList[key]);\r\n\r\n        // Update localStorage with the current discussList\r\n        localStorage.setItem(\"discuss\", JSON.stringify(discussList));\r\n    };\r\n\r\n    const createMarkup = (htmlContent: string) => {\r\n        return { __html: htmlContent };\r\n    };\r\n    const AddReplyComponent = (AddReplyprops: { i: number }) => {\r\n        return (\r\n            <Col sm={2}>\r\n                <Button\r\n                    variant=\"secondary\"\r\n                    size=\"sm\"\r\n                    onClick={() => {\r\n                        setReply(AddReplyprops.i);\r\n                    }}\r\n                >\r\n                    {t(\"Reply\")}\r\n                </Button>\r\n            </Col>\r\n        );\r\n    };\r\n    const CanAddDiscussion = canAddDiscussionUpdate(() => <AddDiscussion handleDiscussSubmit={handleDiscussSubmit} />);\r\n    const CanReplyToDiscussion = canAddDiscussionUpdate((Discussionprops: { i: number }) => (\r\n        <AddReplyComponent i={Discussionprops.i} />\r\n    ));\r\n    return (\r\n        <Container fluid>\r\n            <CanAddDiscussion />\r\n            {discussList && discussList.length == 0 ? (\r\n                <div>\r\n                    <p className=\"d-flex d-flex justify-content-center p-2 m-0\">{nodata}</p>\r\n                </div>\r\n            ) : (\r\n                <>\r\n                    {discussList.map((cur, i) => {\r\n                        if (cur.title) {\r\n                            return (\r\n                                <div className=\"discItem\" key={i}>\r\n                                    <Row>\r\n                                        <Col sm={10}>\r\n                                            <div className=\"discThread\">\r\n                                                <div className=\"discAvatar\">\r\n                                                    <FontAwesomeIcon icon={faUser} color=\"#fff\" size=\"lg\" />\r\n                                                </div>\r\n                                                <div className=\"discBody\">\r\n                                                    <div className=\"discUser\">\r\n                                                        <span className=\"discUserName\">\r\n                                                            {cur && cur.user && cur.user.username}\r\n                                                        </span>{\" \"}\r\n                                                        -{\" \"}\r\n                                                        <span className=\"discTime\">\r\n                                                            {moment.utc(cur.created_at).locale(_moment).fromNow()}\r\n                                                        </span>\r\n                                                    </div>\r\n                                                    <div className=\"discBodyInnner\">{cur.title}</div>\r\n                                                </div>\r\n                                            </div>\r\n                                        </Col>\r\n                                        {replyToggle !== -1 && i === replyToggle ? null : (\r\n                                            <CanReplyToDiscussion i={i} />\r\n                                        )}\r\n                                    </Row>\r\n\r\n                                    {cur.reply &&\r\n                                        cur.reply.map((replyData, index) => {\r\n                                            return cur_func(index, replyData, _moment, createMarkup);\r\n                                        })}\r\n                                    {replyToggle !== -1 && i === replyToggle ? (\r\n                                        <div>\r\n                                            <EditorComponent initContent={description} onChange={(evt: string) => handleDescriptionChange(evt)} />{\" \"}\r\n                                            <br />\r\n                                            <Button\r\n                                                disabled={!description.length}\r\n                                                size=\"sm\"\r\n                                                variant=\"info\"\r\n                                                onClick={() => showReply(i)}\r\n                                            >\r\n                                                {t(\"Send\")}\r\n                                            </Button>{\" \"}\r\n                                            &nbsp;\r\n                                            <Button\r\n                                                variant=\"secondary\"\r\n                                                size=\"sm\"\r\n                                                onClick={() => {\r\n                                                    setReply(-1);\r\n                                                }}\r\n                                            >\r\n                                                {t(\"Cancel\")}\r\n                                            </Button>\r\n                                        </div>\r\n                                    ) : null}\r\n                                </div>\r\n                            );\r\n                        } else {\r\n                            return null;\r\n                        }\r\n                    })}\r\n                </>\r\n            )}\r\n        </Container>\r\n    );\r\n};\r\n\r\nexport default Discussion;\r\nfunction cur_func(index: number, replyData: any, _moment: string, createMarkup: (htmlContent: string) => { __html: string }) {\r\n    return (\r\n        <div className=\"discReply\" key={index}>\r\n            <div style={{ marginLeft: \"55px\", marginBottom: \"10px\", marginTop: \"10px\" }}>\r\n                <div className=\"discThread\">\r\n                    <div className=\"discAvatar\">\r\n                        <FontAwesomeIcon icon={faUser} color=\"#fff\" size=\"xs\" />\r\n                    </div>\r\n                    <div className=\"discBody\">\r\n                        <div className=\"discUser\">\r\n                            <span className=\"discUserName\">{replyData && replyData.user}</span> -{\" \"}\r\n                            <span className=\"discTime\">\r\n                                {replyData.time\r\n                                    ? moment.utc(replyData.time).locale(_moment).fromNow()\r\n                                    : moment().fromNow()}\r\n                            </span>\r\n                        </div>\r\n                        <div className=\"discBodyInnner\">\r\n                            <div dangerouslySetInnerHTML={createMarkup(replyData.msg)}></div>\r\n                        </div>\r\n                    </div>\r\n                </div>\r\n            </div>\r\n        </div>\r\n    );\r\n}\r\n"], "names": ["value", "onChange", "SimpleRichTextEditor", "placeholder", "height", "disabled", "editor<PERSON><PERSON>", "useRef", "isFocused", "setIsFocused", "useState", "useEffect", "current", "innerHTML", "handleInput", "execCommand", "command", "document", "focus", "div", "className", "style", "border", "padding", "borderBottom", "background", "button", "type", "onClick", "margin", "strong", "em", "u", "url", "prompt", "ref", "contentEditable", "onInput", "onFocus", "onBlur", "minHeight", "maxHeight", "overflow", "outline", "data-placeholder", "suppressContentEditableWarning", "EditorComponent", "initContent", "props", "content", "canAddDiscussionUpdate", "connectedAuthWrapper", "authenticatedSelector", "state", "permissions", "update", "wrapperDisplayName", "discussion", "setDiscussion", "AddDiscussion", "t", "useTranslation", "handleKeypress", "handleDiscussSubmit", "Form", "Row", "Col", "sm", "lg", "Control", "onKeyPress", "event", "key", "preventDefault", "handleDiscussChange", "e", "target", "<PERSON><PERSON>", "i18n", "currentLang", "language", "updates", "setUpdates", "useContext", "UpdateContext", "discussList", "setDiscussList", "replyToggle", "setReply", "description", "setDescription", "username", "setUserName", "updateTypeId", "setUpdateTypeId", "_moment", "nodata", "setNoData", "fetchUpdateType", "response", "apiService", "get", "data", "length", "updateType", "_", "title", "_id", "fetchData", "updateTypeIdinitial", "operationParams", "update_type", "sort", "limit", "_data", "id", "initialData", "currentUser", "post", "updateTypeIdset", "reply", "show_as_announcement", "user", "updateresp", "localStorage", "setItem", "JSON", "stringify", "handleDescriptionChange", "val", "showReply", "push", "msg", "time", "Date", "patch", "createMarkup", "__html", "htmlContent", "AddReplyComponent", "variant", "size", "AddReplyprops", "i", "CanAddDiscussion", "CanReplyToDiscussion", "Discussionprops", "Container", "fluid", "p", "map", "cur", "FontAwesomeIcon", "icon", "faUser", "color", "span", "moment", "created_at", "locale", "fromNow", "replyData", "index", "cur_func", "marginLeft", "marginBottom", "marginTop", "dangerouslySetInnerHTML", "evt", "br"], "sourceRoot": "", "ignoreList": []}