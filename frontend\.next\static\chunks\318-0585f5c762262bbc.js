"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[318],{12613:(e,t,s)=>{s.d(t,{A:()=>i});var a=s(37876);s(14232);var r=s(56856);let i=e=>(0,a.jsx)(r.Ay,{...e})},30318:(e,t,s)=>{s.r(t),s.d(t,{default:()=>I});var a=s(37876),r=s(14232),i=s(49589),l=s(29335),n=s(56970),o=s(37784),c=s(29504),d=s(60282),u=s(89099),p=s.n(u),m=s(48230),h=s.n(m),x=s(10841),g=s.n(x),v=s(97685),y=s(35611),b=s(53718),f=s(12613),j=s(31753),A=s(89673),C=s(67814),w=s(2827),S=s(73278),k=s(79314);let N=e=>{let t=(0,S.Ay)(),[s,i]=(0,r.useState)([]),[l,d]=(0,r.useState)(""),[u,p]=(0,r.useState)([]),[m,h]=(0,r.useState)([]),[x,g]=(0,r.useState)(!1),[v,y]=(0,r.useState)(!1),[f,A]=(0,r.useState)(""),{t:N,i18n:_}=(0,j.Bd)("common"),E="de"===_.language?{title_de:"asc"}:{title:"asc"},I=_.language;(0,r.useEffect)(()=>{(async e=>{let t=await b.A.get("/country",e);t&&Array.isArray(t.data)&&i(t.data.map((e,t)=>({label:e.title,value:e._id})));let s=await b.A.get("/institutiontype",e);s&&Array.isArray(s.data)&&h(s.data.map((e,t)=>({label:e.title,value:e._id})))})({query:{},sort:E,limit:"~",languageCode:I})},[]),(0,r.useEffect)(()=>{u&&e.nonMember&&e.nonMember(u)},[u]);let O=async e=>{e&&(/\S+@\S+\.\S+/.test(e)?(""===(await b.A.post("/vspace/filterNonmember",{email:e})).message?p([...u||[],{label:e,value:e}]):(A(e),y(!0),setTimeout(()=>{y(!1),A("")},1200)),d("")):(g(!0),d(""),setTimeout(()=>{g(!1)},1200)))},R=async e=>{if(l)switch(e.key){case"Enter":case"Tab":await O(l),e.preventDefault()}},D=async()=>{l&&l.trim()&&await O(l.trim())},{invitesCountry:T,invitesRegion:P,invitesOrganisationType:L,invitesOrganisation:M,invitesExpertise:F,invitesNetWork:B,visibility:W,userList:q}=e,G="visibility-space";return(0,a.jsxs)("div",{children:[(0,a.jsx)(o.A,{className:"header-block",lg:12,children:(0,a.jsx)("h6",{children:(0,a.jsx)("span",{children:N("vspace.Admin")})})}),(0,a.jsxs)(n.A,{children:[(0,a.jsx)(o.A,{children:(0,a.jsxs)(c.A.Group,{children:[(0,a.jsx)(c.A.Label,{children:N("vspace.GroupVisibility")}),(0,a.jsx)(c.A.Check,{className:"check",checked:W,name:"visibility",onClick:e.handleVisibility,type:"radio",label:N("vspace.Public")})]})}),(0,a.jsx)(o.A,{style:{marginTop:"1.6rem"},children:(0,a.jsx)(c.A.Check,{type:"radio",checked:!W,name:"visibility",value:"off",onClick:e.handleVisibility,label:N("vspace.Private")})})]}),!W&&(0,a.jsxs)("div",{children:[(0,a.jsx)(o.A,{className:"header-block",lg:12,children:(0,a.jsx)("h6",{children:(0,a.jsx)("span",{children:N("vspace.Invites")})})}),(0,a.jsxs)(n.A,{children:[(0,a.jsx)(o.A,{md:4,sm:4,lg:4,children:(0,a.jsxs)(c.A.Group,{children:[(0,a.jsx)(c.A.Label,{children:N("CountryOrTerritory")}),(0,a.jsx)(C.KF,{overrideStrings:{selectSomeItems:N("SelectCountry")},options:s||[],onChange:t=>e.onChange(t,"invitesCountry"),value:T,className:G,labelledBy:N("SelectCountry")})]})}),(0,a.jsx)(o.A,{md:4,sm:4,lg:4,children:(0,a.jsxs)(c.A.Group,{children:[(0,a.jsx)(c.A.Label,{children:N("CountryRegions")}),(0,a.jsx)(C.KF,{overrideStrings:{selectSomeItems:N("SelectRegions")},options:e.multiRegionOptions||[],value:P,onChange:t=>e.onChange(t,"invitesRegion"),className:G,labelledBy:N("SelectRegions")})]})}),(0,a.jsx)(o.A,{md:4,sm:4,lg:4,children:(0,a.jsxs)(c.A.Group,{children:[(0,a.jsx)(c.A.Label,{children:N("OrganisationType")}),(0,a.jsx)(C.KF,{overrideStrings:{selectSomeItems:N("SelectOrganisationType")},options:m||[],onChange:t=>e.onChange(t,"invitesOrganisationType"),value:L,className:G,labelledBy:N("vspace.SelectOrganisationType")})]})})]}),(0,a.jsxs)(n.A,{children:[(0,a.jsx)(o.A,{md:4,sm:4,lg:4,children:(0,a.jsxs)(c.A.Group,{children:[(0,a.jsx)(c.A.Label,{children:N("Organisation")}),(0,a.jsx)(C.KF,{overrideStrings:{selectSomeItems:N("SelectOrganisation")},options:e.multiOrganisationOptions||[],onChange:t=>e.onChange(t,"invitesOrganisation"),value:M,className:G,labelledBy:N("SelectOrganisation")})]})}),(0,a.jsx)(o.A,{md:4,sm:4,lg:4,children:(0,a.jsxs)(c.A.Group,{style:{maxWidth:"450px"},children:[(0,a.jsx)(c.A.Label,{children:N("Expertise")}),(0,a.jsx)(C.KF,{overrideStrings:{selectSomeItems:N("SelectExpertise")},options:e.multiExpertsOptions||[],onChange:t=>e.onChange(t,"invitesExpertise"),value:F,className:"visibility-space",labelledBy:"Select Organisation Type"})]})}),(0,a.jsx)(o.A,{md:4,sm:4,lg:4,children:(0,a.jsxs)(c.A.Group,{children:[(0,a.jsx)(c.A.Label,{children:N("Network")}),(0,a.jsx)(C.KF,{overrideStrings:{selectSomeItems:N("SelectNetwork")},options:e.multiNetworkOptions||[],onChange:t=>e.onChange(t,"invitesNetWork"),value:B,className:"visibility-space",labelledBy:"Select Organisation Type"})]})})]}),(0,a.jsx)(o.A,{className:"header-block",lg:12,children:(0,a.jsx)("h6",{children:(0,a.jsx)("span",{children:N("vspace.PlatformMemberInvites")})})}),(0,a.jsx)(n.A,{children:(0,a.jsx)(o.A,{md:12,lg:12,sm:12,children:(0,a.jsx)(w.Ay,{closeMenuOnSelect:!1,components:t,isMulti:!0,value:q||[],placeholder:N("SelectUsers"),onChange:t=>{var s,a;let r=(null==(s=e.allOption)?void 0:s.value)||"*";return t&&t.length>0&&(null==(a=t[t.length-1])?void 0:a.value)===r?e.onChange(e.multiUserOptions||[],"userList"):e.onChange(t||[],"userList")},options:[e.allOption||{label:"All users",value:"*"},...e.multiUserOptions||[]]})})}),(0,a.jsx)(o.A,{className:"header-block",lg:12,children:(0,a.jsx)("h6",{className:"mb-1",children:(0,a.jsx)("span",{children:N("vspace.NonPlatform")})})}),(0,a.jsx)(n.A,{children:(0,a.jsxs)(o.A,{md:12,lg:12,sm:12,children:[(0,a.jsx)("small",{children:N("vspace.PressTab")}),(0,a.jsx)(k.A,{components:t,inputValue:l||"",isClearable:!0,isMulti:!0,menuIsOpen:!1,onChange:e=>p(e&&Array.isArray(e)?e:[]),onInputChange:e=>d(e||""),onKeyDown:R,onBlur:D,placeholder:N("vspace.Typeemail"),value:u||[]}),x&&(0,a.jsx)("small",{className:"text-danger",children:N("PleaseenterValidEmailid")}),v&&(0,a.jsxs)("small",{className:"text-danger",children:[" ",f,"  ",N("isalreadyexist")]})]})})]})]})};N.defaultProps={allOption:{label:"All users",value:"*"}};var _=s(5671);let E={title:"",description:"",startDate:null,endDate:null,searchData:"",visibility:!0,images:[],checked:!1,file_category:"",nonMembers:[],images_src:[],members:[],doc_src:[],document:[]},I=e=>{let t=(0,r.useRef)(null),s=(0,u.useRouter)(),{t:m}=(0,j.Bd)("common"),[x,C]=(0,r.useState)([]),[w,S]=(0,r.useState)([]),[k,I]=(0,r.useState)([]),[O,R]=(0,r.useState)([]),[D,T]=(0,r.useState)(E),[P,L]=(0,r.useState)([]),[M,F]=(0,r.useState)([]),[B,W]=(0,r.useState)([]),[q,G]=(0,r.useState)([]),[U,V]=(0,r.useState)([]),[,z]=(0,r.useState)(!1),[K,$]=(0,r.useState)(null),[H,J]=(0,r.useState)(null),[Q,X]=(0,r.useState)({invitesCountry:[],invitesRegion:[],invitesOrganisationType:[],invitesOrganisation:[],invitesExpertise:[],invitesNetWork:[],userList:[]}),Y=(0,r.useRef)(null),Z=e=>{T(t=>({...t,description:e}))},ee=(e,t)=>{T(s=>({...s,[t]:e}))},et=async s=>{let a,r;if(t.current&&t.current.setAttribute("disabled","disabled"),s.preventDefault(),!D.title||D.title.length<5){v.Ay.error(m("minimum5CharsReq")),t.current&&t.current.removeAttribute("disabled");return}z(!0);let i=Q.userList.length>0?Q.userList.map(e=>e.value):[],l={title:D.title,description:D.description,start_date:D.startDate,end_date:D.endDate,visibility:!0===D.visibility,images:D.images,images_src:D.images_src,members:i,nonMembers:D.nonMembers&&D.nonMembers.length>0?D.nonMembers.map(e=>e.value):"",document:D.document,doc_src:D.doc_src};"Operation"===H?l.operation=K:l.project=K;try{e.routes&&"edit"===e.routes[0]&&e.routes[1]?(r="vspace.virtualspaceupdatedsuccessfully",a=await b.A.patch("/vspace/".concat(e.routes[1]),l)):(r="vspace.virtualspaceaddedsuccessfully",a=await b.A.post("/vspace",l)),a&&a._id?(v.Ay.success(m(r)),p().push("/vspace/[...routes]","/vspace/show/".concat(a._id))):(v.Ay.error(m("An error occurred while saving the virtual space")),t.current&&t.current.removeAttribute("disabled"))}catch(e){console.error("Error saving virtual space:",e),v.Ay.error(m("An error occurred while saving the virtual space")),t.current&&t.current.removeAttribute("disabled")}},es=e=>{e&&e.images?C(e.images):C([]),e&&e.images_src?S(e.images_src):S([]),e&&e.document?R(e.document):R([]),e&&e.doc_src?I(e.doc_src):I([])},ea=async()=>{let t=await b.A.post("/users/getLoggedUser",{}),s=await b.A.get("/vspace/".concat(e.routes[1])),a={title:s.title,description:s.description,startDate:s.start_date?g()(s.start_date).toDate():null,endDate:s.end_date?g()(s.end_date).toDate():null,file_category:s.file_category?s.file_category:null,visibility:!!s.visibility,user:s.user?s.user._id:"",images:s.images,images_src:s.images_src,nonMembers:""!==s.nonMembers[0]?s.nonMembers.map(e=>({label:e,value:e})):[]};if(s.members.length>0){let e=[];s.members.forEach(t=>{e.push({label:t.username,value:t._id})}),X({...Q,userList:e})}return es(s),s.user&&s.user._id!==t._id&&p().push("/vspace"),T(a),s.end_date?T(e=>({...e,checked:!0})):null};(0,r.useEffect)(()=>{e.routes&&"edit"===e.routes[0]&&e.routes[1]&&ea(),$(s&&s.query&&s.query.id?s.query.id:null),J(s&&s.query&&s.query.source?s.query.source:null)},[]),(0,r.useEffect)(()=>{let e={query:{},sort:{username:"asc"},limit:"~",select:"-acceptCookiesPolicy -country -created_at -dataConsentPolicy -dial_code -enabled -firstname -image -institution -is_focal_point -password -position -region -restrictedUsePolicy -withdrawConsentPolicy -medicalConsentPolicy -fullDataProtectionConsentPolicy -email -roles -updated_at -emailActivateToken -lastname -mobile_number "};(async()=>{var t;let s=await b.A.get("/users",e);(null==s||null==(t=s.data)?void 0:t.length)&&(s.data=s.data.filter(e=>"Request Pending"!==e.vspace_status&&"Request Pending"!==e.status)),s&&L(s.data.map((e,t)=>({label:e.username,value:e._id})))})()},[Q]),r.useEffect(()=>{if(Q){let e={};Object.keys(Q).forEach((t,s)=>{let a=Q[t].length>0&&Q[t].map(e=>e.value);e[t]=a||[]}),en(e)}else console.log("No threshold reached.")},[Q]);let er=e=>{e&&Array.isArray(e.data)&&W(e.data.map(e=>({label:e.title,value:e._id})))},ei=e=>{e&&Array.isArray(e.data)&&V(e.data.map((e,t)=>({label:e.title,value:e._id})))},el=e=>{e&&Array.isArray(e.data)&&G(e.data.map((e,t)=>({label:e.title,value:e._id})))},en=async e=>{let{invitesCountry:t,invitesRegion:s,invitesOrganisationType:a,invitesOrganisation:r,invitesExpertise:i,invitesNetWork:l}=e,n=[],o=[],c=[],d=[],u=await b.A.post("vspace/filterUser",{query:{country:t,country_region:s,institution_type:a,institution:r,expertises:i,networks:l,type:"public"}});if(u&&Array.isArray(u)){if(u[0].regions&&u[0].regions.length>0&&F(u[0].regions.map((e,t)=>({label:e.title,value:e._id}))),u[1].organisation&&u[1].organisation.length>0)W(u[1].organisation.map((e,t)=>(d=e.networks.map(e=>({label:e.title,value:e._id})),c=e.expertise.map(e=>({label:e.title,value:e._id})),{label:e.title,value:e._id}))),G(d),V(c);else if(0===u[1].organisation.length){let e={query:{},sort:{title:"asc"},limit:"~"};er(await b.A.get("/institution",e)),ei(await b.A.get("/expertise",e)),el(await b.A.get("/institutionnetwork",e))}u[2].usersList&&u[2].usersList.length>0&&L(u[2].usersList.map((e,t)=>({label:e.username,value:e._id})))}},eo=e=>{let t=[],s=[];e.length>0&&e.map(e=>{e.type&&(e.type.includes("pdf")||e.type.includes("docx")||e.type.includes("xlsx")||e.type.includes("xls"))?s.push(e.serverID):t.push(e.serverID)}),T(e=>({...e,images:t})),T(e=>({...e,document:s}))},ec=e=>{T(t=>({...t,images_src:e}))},ed=e=>{T(t=>({...t,doc_src:e}))},eu=e=>{let{name:t,value:s}=e.target;T(e=>({...e,[t]:s}))};return(0,a.jsx)(i.A,{className:"formCard",fluid:!0,children:(0,a.jsx)(l.A,{children:(0,a.jsx)(y.yk,{onSubmit:et,ref:Y,onKeyPress:e=>{"Enter"===e.key&&e.preventDefault()},children:(0,a.jsxs)(l.A.Body,{children:[(0,a.jsx)(n.A,{children:(0,a.jsx)(o.A,{children:(0,a.jsx)(l.A.Title,{children:"edit"===e.routes[0]?m("vspace.editVirtualSpace"):m("vspace.addVirtualSpace")})})}),(0,a.jsx)("hr",{}),(0,a.jsx)(n.A,{className:"mb-3",children:(0,a.jsx)(o.A,{children:(0,a.jsxs)(c.A.Group,{children:[(0,a.jsx)(c.A.Label,{className:"required-field",children:m("vspace.title")}),(0,a.jsx)(c.A.Control,{minLength:5,required:!0,type:"text",name:"title",value:D.title,onChange:eu}),(0,a.jsx)(c.A.Control.Feedback,{type:"invalid",children:0===D.title.length?m("Pleaseprovideatitle"):m("minimum5CharsReq")})]})})}),(0,a.jsx)(n.A,{className:"mb-3",children:(0,a.jsx)(o.A,{children:(0,a.jsxs)(c.A.Group,{children:[(0,a.jsx)(c.A.Label,{children:m("vspace.Body")}),(0,a.jsx)(_.x,{initContent:D.description,onChange:e=>Z(e)})]})})}),(0,a.jsx)(n.A,{className:"mb-3",children:(0,a.jsx)(o.A,{lg:12,children:(0,a.jsxs)(c.A.Group,{children:[(0,a.jsx)(c.A.Label,{children:m("vspace.Image")}),(0,a.jsx)(A.A,{datas:x,srcText:w,getImgID:e=>eo(e),getImageSource:e=>ec(e)})]})})}),(0,a.jsx)(n.A,{className:"mb-3",children:(0,a.jsx)(o.A,{lg:12,children:(0,a.jsxs)(c.A.Group,{children:[(0,a.jsx)(c.A.Label,{children:m("vspace.Documents")}),(0,a.jsx)(A.A,{type:"application",datas:O,srcText:k,getImgID:e=>eo(e),getImageSource:e=>ed(e)})]})})}),(0,a.jsxs)(n.A,{className:"mb-3",children:[(0,a.jsx)(o.A,{md:!0,lg:3,sm:12,children:(0,a.jsxs)(c.A.Group,{children:[(0,a.jsx)(c.A.Label,{className:"d-block",children:m("vspace.StartDate")}),(0,a.jsx)(f.A,{selected:D.startDate,onChange:e=>ee(e,"startDate"),dateFormat:"MMMM d, yyyy",placeholderText:m("vspace.Selectadate")})]})}),(0,a.jsx)(o.A,{md:!0,lg:2,sm:12,className:"col-md",children:(0,a.jsx)(c.A.Check,{type:"checkbox",checked:D.checked,onChange:()=>{T(e=>({...e,checked:!e.checked}))},label:m("vspace.ShowEndDate")})}),D.checked&&(0,a.jsx)(o.A,{md:!0,lg:3,sm:12,children:(0,a.jsxs)(c.A.Group,{children:[(0,a.jsx)(c.A.Label,{className:"d-block",children:m("vspace.EndDate")}),(0,a.jsx)(f.A,{selected:D.endDate,minDate:D.startDate,onChange:e=>ee(e,"endDate"),dateFormat:"MMMM d, yyyy",placeholderText:m("vspace.Selectadate")})]})})]}),(0,a.jsx)(N,{...Q,...D,allOption:{label:"All users",value:"*"},multiUserOptions:P,multiRegionOptions:M,multiOrganisationOptions:B,multiExpertsOptions:U,multiNetworkOptions:q,onChange:(e,t)=>{X(s=>({...s,[t]:null==e?[]:e}))},handleVisibility:()=>{T(e=>({...e,visibility:!e.visibility}))},onHandleChange:eu,nonMember:e=>{T(t=>({...t,nonMembers:e}))}}),(0,a.jsx)(n.A,{className:"my-4",children:(0,a.jsxs)(o.A,{children:[(0,a.jsx)(d.A,{className:"me-2",type:"submit",variant:"primary",ref:t,children:m("submit")}),(0,a.jsx)(d.A,{className:"me-2",onClick:()=>{T(E),C([]),S([]),R([]),I([]),X({invitesCountry:[],invitesRegion:[],invitesOrganisationType:[],invitesOrganisation:[],invitesExpertise:[],invitesNetWork:[],userList:[]}),z(!1),window.scrollTo(0,0)},variant:"info",children:m("reset")}),(0,a.jsx)(h(),{href:"/vspace",as:"/vspace",children:(0,a.jsx)(d.A,{variant:"secondary",children:m("Cancel")})})]})})]})})})})}},35611:(e,t,s)=>{s.d(t,{sx:()=>c,s3:()=>r.s3,ks:()=>r.ks,yk:()=>a.A});var a=s(54773),r=s(59200),i=s(37876),l=s(14232),n=s(39593),o=s(29504);let c={RadioGroup:e=>{let{name:t,valueSelected:s,onChange:a,errorMessage:r,children:o}=e,{errors:c,touched:d}=(0,n.j7)(),u=d[t]&&c[t];l.useMemo(()=>({name:t}),[t]);let p=l.Children.map(o,e=>l.isValidElement(e)&&function(e){return"object"==typeof e&&null!==e}(e.props)?l.cloneElement(e,{name:t,...e.props}):e);return(0,i.jsxs)("div",{children:[(0,i.jsx)("div",{className:"radio-group",children:p}),u&&(0,i.jsx)("div",{className:"invalid-feedback d-block",children:r||("string"==typeof c[t]?c[t]:String(c[t]))})]})},RadioItem:e=>{let{id:t,label:s,value:a,name:r,disabled:l}=e,{values:c,setFieldValue:d}=(0,n.j7)(),u=r||t;return(0,i.jsx)(o.A.Check,{type:"radio",id:t,label:s,value:a,name:u,checked:c[u]===a,onChange:e=>{d(u,e.target.value)},disabled:l,inline:!0})}};a.A,r.ks,r.s3},54773:(e,t,s)=>{s.d(t,{A:()=>o});var a=s(37876),r=s(14232),i=s(39593),l=s(91408);let n=(0,r.forwardRef)((e,t)=>{let{children:s,onSubmit:r,autoComplete:n,className:o,onKeyPress:c,initialValues:d,...u}=e,p=l.Ik().shape({});return(0,a.jsx)(i.l1,{initialValues:d||{},validationSchema:p,onSubmit:(e,t)=>{let s={preventDefault:()=>{},stopPropagation:()=>{},currentTarget:null,target:null,nativeEvent:new Event("submit"),bubbles:!1,cancelable:!0,defaultPrevented:!1,eventPhase:0,isTrusted:!1,timeStamp:Date.now(),type:"submit",isDefaultPrevented:()=>!1,isPropagationStopped:()=>!1,persist:()=>{}};r&&r(s,e,t)},...u,children:e=>(0,a.jsx)(i.lV,{ref:t,onSubmit:e.handleSubmit,autoComplete:n,className:o,onKeyPress:c,children:"function"==typeof s?s(e):s})})});n.displayName="ValidationFormWrapper";let o=n},59200:(e,t,s)=>{s.d(t,{ks:()=>l,s3:()=>n});var a=s(37876);s(14232);var r=s(29504),i=s(39593);let l=e=>{let{name:t,id:s,required:l,validator:n,errorMessage:o,onChange:c,value:d,as:u,multiline:p,rows:m,pattern:h,...x}=e;return(0,a.jsx)(i.D0,{name:t,validate:e=>{let t="string"==typeof e?e:String(e||"");return l&&(!e||""===t.trim())?(null==o?void 0:o.validator)||"This field is required":n&&!n(e)?(null==o?void 0:o.validator)||"Invalid value":h&&e&&!new RegExp(h).test(e)?(null==o?void 0:o.pattern)||"Invalid format":void 0},children:e=>{let{field:t,meta:i}=e;return(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(r.A.Control,{...t,...x,id:s,as:u||"input",rows:m,isInvalid:i.touched&&!!i.error,onChange:e=>{t.onChange(e),c&&c(e)},value:void 0!==d?d:t.value}),i.touched&&i.error?(0,a.jsx)(r.A.Control.Feedback,{type:"invalid",children:i.error}):null]})}})},n=e=>{let{name:t,id:s,required:l,errorMessage:n,onChange:o,value:c,children:d,...u}=e;return(0,a.jsx)(i.D0,{name:t,validate:e=>{if(l&&(!e||""===e))return(null==n?void 0:n.validator)||"This field is required"},children:e=>{let{field:t,meta:i}=e;return(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(r.A.Control,{as:"select",...t,...u,id:s,isInvalid:i.touched&&!!i.error,onChange:e=>{t.onChange(e),o&&o(e)},value:void 0!==c?c:t.value,children:d}),i.touched&&i.error?(0,a.jsx)(r.A.Control.Feedback,{type:"invalid",children:i.error}):null]})}})}},67814:(e,t,s)=>{s.d(t,{KF:()=>C});var a=s(14232),r=s(37876);!function(e,{insertAt:t}={}){if(!e||typeof document>"u")return;let s=document.head||document.getElementsByTagName("head")[0],a=document.createElement("style");a.type="text/css","top"===t&&s.firstChild?s.insertBefore(a,s.firstChild):s.appendChild(a),a.styleSheet?a.styleSheet.cssText=e:a.appendChild(document.createTextNode(e))}(`.rmsc{--rmsc-main: #4285f4;--rmsc-hover: #f1f3f5;--rmsc-selected: #e2e6ea;--rmsc-border: #ccc;--rmsc-gray: #aaa;--rmsc-bg: #fff;--rmsc-p: 10px;--rmsc-radius: 4px;--rmsc-h: 38px}.rmsc *{box-sizing:border-box;transition:all .2s ease}.rmsc .gray{color:var(--rmsc-gray)}.rmsc .dropdown-content{position:absolute;z-index:1;top:100%;width:100%;padding-top:8px}.rmsc .dropdown-content .panel-content{overflow:hidden;border-radius:var(--rmsc-radius);background:var(--rmsc-bg);box-shadow:0 0 0 1px #0000001a,0 4px 11px #0000001a}.rmsc .dropdown-container{position:relative;outline:0;background-color:var(--rmsc-bg);border:1px solid var(--rmsc-border);border-radius:var(--rmsc-radius)}.rmsc .dropdown-container[aria-disabled=true]:focus-within{box-shadow:var(--rmsc-gray) 0 0 0 1px;border-color:var(--rmsc-gray)}.rmsc .dropdown-container:focus-within{box-shadow:var(--rmsc-main) 0 0 0 1px;border-color:var(--rmsc-main)}.rmsc .dropdown-heading{position:relative;padding:0 var(--rmsc-p);display:flex;align-items:center;width:100%;height:var(--rmsc-h);cursor:default;outline:0}.rmsc .dropdown-heading .dropdown-heading-value{overflow:hidden;text-overflow:ellipsis;white-space:nowrap;flex:1}.rmsc .clear-selected-button{cursor:pointer;background:none;border:0;padding:0;display:flex}.rmsc .options{max-height:260px;overflow-y:auto;margin:0;padding-left:0}.rmsc .options li{list-style:none;margin:0}.rmsc .select-item{box-sizing:border-box;cursor:pointer;display:block;padding:var(--rmsc-p);outline-offset:-1px;outline-color:var(--rmsc-primary)}.rmsc .select-item:hover{background:var(--rmsc-hover)}.rmsc .select-item.selected{background:var(--rmsc-selected)}.rmsc .no-options{padding:var(--rmsc-p);text-align:center;color:var(--rmsc-gray)}.rmsc .search{width:100%;position:relative;border-bottom:1px solid var(--rmsc-border)}.rmsc .search input{background:none;height:var(--rmsc-h);padding:0 var(--rmsc-p);width:100%;outline:0;border:0;font-size:1em}.rmsc .search input:focus{background:var(--rmsc-hover)}.rmsc .search-clear-button{cursor:pointer;position:absolute;top:0;right:0;bottom:0;background:none;border:0;padding:0 calc(var(--rmsc-p) / 2)}.rmsc .search-clear-button [hidden]{display:none}.rmsc .item-renderer{display:flex;align-items:baseline}.rmsc .item-renderer input{margin:0 5px 0 0}.rmsc .item-renderer.disabled{opacity:.5}.rmsc .spinner{animation:rotate 2s linear infinite}.rmsc .spinner .path{stroke:var(--rmsc-border);stroke-width:4px;stroke-linecap:round;animation:dash 1.5s ease-in-out infinite}@keyframes rotate{to{transform:rotate(360deg)}}@keyframes dash{0%{stroke-dasharray:1,150;stroke-dashoffset:0}50%{stroke-dasharray:90,150;stroke-dashoffset:-35}to{stroke-dasharray:90,150;stroke-dashoffset:-124}}
`);var i={allItemsAreSelected:"All items are selected.",clearSearch:"Clear Search",clearSelected:"Clear Selected",noOptions:"No options",search:"Search",selectAll:"Select All",selectAllFiltered:"Select All (Filtered)",selectSomeItems:"Select...",create:"Create"},l={value:[],hasSelectAll:!0,className:"multi-select",debounceDuration:200,options:[]},n=a.createContext({}),o=({props:e,children:t})=>{let[s,o]=(0,a.useState)(e.options);return(0,a.useEffect)(()=>{o(e.options)},[e.options]),(0,r.jsx)(n.Provider,{value:{t:t=>{var s;return(null==(s=e.overrideStrings)?void 0:s[t])||i[t]},...l,...e,options:s,setOptions:o},children:t})},c=()=>a.useContext(n),d={when:!0,eventTypes:["keydown"]};function u(e,t,s){let r=(0,a.useMemo)(()=>Array.isArray(e)?e:[e],[e]),i=Object.assign({},d,s),{when:l,eventTypes:n}=i,o=(0,a.useRef)(t),{target:c}=i;(0,a.useEffect)(()=>{o.current=t});let u=(0,a.useCallback)(e=>{r.some(t=>e.key===t||e.code===t)&&o.current(e)},[r]);(0,a.useEffect)(()=>{if(l&&"u">typeof window){let e=c?c.current:window;return n.forEach(t=>{e&&e.addEventListener(t,u)}),()=>{n.forEach(t=>{e&&e.removeEventListener(t,u)})}}},[l,n,r,c,t])}var p={ARROW_DOWN:"ArrowDown",ARROW_UP:"ArrowUp",ENTER:"Enter",ESCAPE:"Escape",SPACE:"Space"},m=(e,t)=>{let s;return function(...a){clearTimeout(s),s=setTimeout(()=>{e.apply(null,a)},t)}},h=()=>(0,r.jsxs)("svg",{width:"24",height:"24",fill:"none",stroke:"currentColor",strokeWidth:"2",className:"dropdown-search-clear-icon gray",children:[(0,r.jsx)("line",{x1:"18",y1:"6",x2:"6",y2:"18"}),(0,r.jsx)("line",{x1:"6",y1:"6",x2:"18",y2:"18"})]}),x=({checked:e,option:t,onClick:s,disabled:a})=>(0,r.jsxs)("div",{className:`item-renderer ${a?"disabled":""}`,children:[(0,r.jsx)("input",{type:"checkbox",onChange:s,checked:e,tabIndex:-1,disabled:a}),(0,r.jsx)("span",{children:t.label})]}),g=({itemRenderer:e=x,option:t,checked:s,tabIndex:i,disabled:l,onSelectionChanged:n,onClick:o})=>{let c=(0,a.useRef)(),d=()=>{l||n(!s)};return u([p.ENTER,p.SPACE],e=>{d(),e.preventDefault()},{target:c}),(0,r.jsx)("label",{className:`select-item ${s?"selected":""}`,role:"option","aria-selected":s,tabIndex:i,ref:c,children:(0,r.jsx)(e,{option:t,checked:s,onClick:e=>{d(),o(e)},disabled:l})})},v=({options:e,onClick:t,skipIndex:s})=>{let{disabled:a,value:i,onChange:l,ItemRenderer:n}=c(),o=(e,t)=>{a||l(t?[...i,e]:i.filter(t=>t.value!==e.value))};return(0,r.jsx)(r.Fragment,{children:e.map((e,l)=>{let c=l+s;return(0,r.jsx)("li",{children:(0,r.jsx)(g,{tabIndex:c,option:e,onSelectionChanged:t=>o(e,t),checked:!!i.find(t=>t.value===e.value),onClick:e=>t(e,c),itemRenderer:n,disabled:e.disabled||a})},(null==e?void 0:e.key)||l)})})},y=()=>{let{t:e,onChange:t,options:s,setOptions:i,value:l,filterOptions:n,ItemRenderer:o,disabled:d,disableSearch:x,hasSelectAll:y,ClearIcon:b,debounceDuration:f,isCreatable:j,onCreateOption:A}=c(),C=(0,a.useRef)(),w=(0,a.useRef)(),[S,k]=(0,a.useState)(""),[N,_]=(0,a.useState)(s),[E,I]=(0,a.useState)(""),[O,R]=(0,a.useState)(0),D=(0,a.useCallback)(m(e=>I(e),f),[]),T=(0,a.useMemo)(()=>{let e=0;return x||(e+=1),y&&(e+=1),e},[x,y]),P={label:e(S?"selectAllFiltered":"selectAll"),value:""},L=e=>{let t=N.filter(e=>!e.disabled).map(e=>e.value);if(e){let e=[...l.map(e=>e.value),...t];return(n?N:s).filter(t=>e.includes(t.value))}return l.filter(e=>!t.includes(e.value))},M=()=>{var e;I(""),k(""),null==(e=null==w?void 0:w.current)||e.focus()},F=e=>R(e);u([p.ARROW_DOWN,p.ARROW_UP],e=>{switch(e.code){case p.ARROW_UP:q(-1);break;case p.ARROW_DOWN:q(1);break;default:return}e.stopPropagation(),e.preventDefault()},{target:C});let B=async()=>{let e={label:S,value:S,__isNew__:!0};A&&(e=await A(S)),i([e,...s]),M(),t([...l,e])},W=async()=>n?await n(s,E):function(e,t){return t?e.filter(({label:e,value:s})=>null!=e&&null!=s&&e.toLowerCase().includes(t.toLowerCase())):e}(s,E),q=e=>{let t=O+e;R(t=Math.min(t=Math.max(0,t),s.length+Math.max(T-1,0)))};(0,a.useEffect)(()=>{var e,t;null==(t=null==(e=null==C?void 0:C.current)?void 0:e.querySelector(`[tabIndex='${O}']`))||t.focus()},[O]);let[G,U]=(0,a.useMemo)(()=>{let e=N.filter(e=>!e.disabled);return[e.every(e=>-1!==l.findIndex(t=>t.value===e.value)),0!==e.length]},[N,l]);(0,a.useEffect)(()=>{W().then(_)},[E,s]);let V=(0,a.useRef)();u([p.ENTER],B,{target:V});let z=j&&S&&!N.some(e=>(null==e?void 0:e.value)===S);return(0,r.jsxs)("div",{className:"select-panel",role:"listbox",ref:C,children:[!x&&(0,r.jsxs)("div",{className:"search",children:[(0,r.jsx)("input",{placeholder:e("search"),type:"text","aria-describedby":e("search"),onChange:e=>{D(e.target.value),k(e.target.value),R(0)},onFocus:()=>{R(0)},value:S,ref:w,tabIndex:0}),(0,r.jsx)("button",{type:"button",className:"search-clear-button",hidden:!S,onClick:M,"aria-label":e("clearSearch"),children:b||(0,r.jsx)(h,{})})]}),(0,r.jsxs)("ul",{className:"options",children:[y&&U&&(0,r.jsx)(g,{tabIndex:+(1!==T),checked:G,option:P,onSelectionChanged:e=>{t(L(e))},onClick:()=>F(1),itemRenderer:o,disabled:d}),N.length?(0,r.jsx)(v,{skipIndex:T,options:N,onClick:(e,t)=>F(t)}):z?(0,r.jsx)("li",{onClick:B,className:"select-item creatable",tabIndex:1,ref:V,children:`${e("create")} "${S}"`}):(0,r.jsx)("li",{className:"no-options",children:e("noOptions")})]})]})},b=({expanded:e})=>(0,r.jsx)("svg",{width:"24",height:"24",fill:"none",stroke:"currentColor",strokeWidth:"2",className:"dropdown-heading-dropdown-arrow gray",children:(0,r.jsx)("path",{d:e?"M18 15 12 9 6 15":"M6 9L12 15 18 9"})}),f=()=>{let{t:e,value:t,options:s,valueRenderer:a}=c(),i=0===t.length,l=t.length===s.length,n=a&&a(t,s);return i?(0,r.jsx)("span",{className:"gray",children:n||e("selectSomeItems")}):(0,r.jsx)("span",{children:n||(l?e("allItemsAreSelected"):t.map(e=>e.label).join(", "))})},j=({size:e=24})=>(0,r.jsx)("span",{style:{width:e,marginRight:"0.2rem"},children:(0,r.jsx)("svg",{width:e,height:e,className:"spinner",viewBox:"0 0 50 50",style:{display:"inline",verticalAlign:"middle"},children:(0,r.jsx)("circle",{cx:"25",cy:"25",r:"20",fill:"none",className:"path"})})}),A=()=>{let{t:e,onMenuToggle:t,ArrowRenderer:s,shouldToggleOnHover:i,isLoading:l,disabled:n,onChange:o,labelledBy:d,value:m,isOpen:x,defaultIsOpen:g,ClearSelectedIcon:v,closeOnChangedValue:A}=c();(0,a.useEffect)(()=>{A&&k(!1)},[m]);let[C,w]=(0,a.useState)(!0),[S,k]=(0,a.useState)(g),[N,_]=(0,a.useState)(!1),E=(0,a.useRef)();(function(e,t){let s=(0,a.useRef)(!1);(0,a.useEffect)(()=>{s.current?e():s.current=!0},t)})(()=>{t&&t(S)},[S]),(0,a.useEffect)(()=>{void 0===g&&"boolean"==typeof x&&(w(!1),k(x))},[x]),u([p.ENTER,p.ARROW_DOWN,p.SPACE,p.ESCAPE],e=>{var t;["text","button"].includes(e.target.type)&&[p.SPACE,p.ENTER].includes(e.code)||(C&&(e.code===p.ESCAPE?(k(!1),null==(t=null==E?void 0:E.current)||t.focus()):k(!0)),e.preventDefault())},{target:E});let I=e=>{C&&i&&k(e)};return(0,r.jsxs)("div",{tabIndex:0,className:"dropdown-container","aria-labelledby":d,"aria-expanded":S,"aria-readonly":!0,"aria-disabled":n,ref:E,onFocus:()=>!N&&_(!0),onBlur:e=>{!e.currentTarget.contains(e.relatedTarget)&&C&&(_(!1),k(!1))},onMouseEnter:()=>I(!0),onMouseLeave:()=>I(!1),children:[(0,r.jsxs)("div",{className:"dropdown-heading",onClick:()=>{C&&k(!l&&!n&&!S)},children:[(0,r.jsx)("div",{className:"dropdown-heading-value",children:(0,r.jsx)(f,{})}),l&&(0,r.jsx)(j,{}),m.length>0&&null!==v&&(0,r.jsx)("button",{type:"button",className:"clear-selected-button",onClick:e=>{e.stopPropagation(),o([]),C&&k(!1)},disabled:n,"aria-label":e("clearSelected"),children:v||(0,r.jsx)(h,{})}),(0,r.jsx)(s||b,{expanded:S})]}),S&&(0,r.jsx)("div",{className:"dropdown-content",children:(0,r.jsx)("div",{className:"panel-content",children:(0,r.jsx)(y,{})})})]})},C=e=>(0,r.jsx)(o,{props:e,children:(0,r.jsx)("div",{className:`rmsc ${e.className||"multi-select"}`,children:(0,r.jsx)(A,{})})})},89673:(e,t,s)=>{s.d(t,{A:()=>A});var a=s(37876),r=s(14232),i=s(17336),l=s(21772),n=s(11041),o=s(37784),c=s(29504),d=s(60282),u=s(31195),p=s(82851),m=s.n(p),h=s(97685),x=s(53718),g=s(31753);let v=[],y={flex:1,display:"flex",flexDirection:"column",alignItems:"center",justifyContent:"center",width:"100%",height:"100%",borderWidth:.1,borderColor:"#fafafa",backgroundColor:"#fafafa",color:"black",transition:"border  .24s ease-in-out",padding:"15px"},b={display:"flex",padding:"10px",width:"100%",border:"2px solid gray",flexDirection:"column",justifyContent:"flex-start",flexWrap:"wrap",marginTop:20},f={width:"150px"},j={borderColor:"#2196f3"},A=e=>{let t,{t:s}=(0,g.Bd)("common"),[p,A]=(0,r.useState)(!1),[C,w]=(0,r.useState)(),S="application"==e.type?0x1400000:"20971520",[k,N]=(0,r.useState)([]),[_,E]=(0,r.useState)(!0),[I,O]=(0,r.useState)([]),R=e&&"application"===e.type?"/files":"/image",D=async e=>{await x.A.remove("".concat(R,"/").concat(e))},T=e=>{w(e),A(!0)},P=(e,t)=>{let s=[...I];s[t]=e.target.value,O(s)},L=t=>{switch(t&&t.name.split(".").pop()){case"JPG":case"jpg":case"jpeg":case"jpg":case"png":return(0,a.jsx)("img",{src:t.preview,style:f});case"pdf":return(0,a.jsx)("img",{src:"/images/fileIcons/pdfFile.png",className:"application"===e.type?"docPreview":"imgPreview"});case"docx":default:return(0,a.jsx)("img",{src:"/images/fileIcons/wordFile.png",className:"application"===e.type?"docPreview":"imgPreview"});case"xls":case"xlsx":return(0,a.jsx)("img",{src:"/images/fileIcons/xlsFile.png",className:"application"===e.type?"docPreview":"imgPreview"})}},M=()=>A(!1),F=()=>{A(!1)},B=t=>{let s=(t=C)&&t._id?{serverID:t._id}:{file:t},a=m().findIndex(v,s),r=[...I];r.splice(a,1),O(r),D(v[a].serverID),v.splice(a,1),e.getImgID(v,e.index?e.index:0);let i=[...k];i.splice(i.indexOf(t),1),N(i),A(!1)},W=k.map((t,r)=>(0,a.jsxs)("div",{children:[(0,a.jsx)(o.A,{xs:12,children:(0,a.jsxs)("div",{className:"row",children:[(0,a.jsx)(o.A,{md:4,lg:3,className:"application text-center align-self-center"===e.type?"docImagePreview text-center align-self-center":"imgPreview text-center align-self-center",children:L(t)}),(0,a.jsx)(o.A,{md:5,lg:7,className:"align-self-center",children:(0,a.jsxs)(c.A,{children:[(0,a.jsxs)(c.A.Group,{controlId:"filename",children:[(0,a.jsx)(c.A.Label,{className:"mt-2",children:s("FileName")}),(0,a.jsx)(c.A.Control,{size:"sm",type:"text",disabled:!0,value:t.original_name?t.original_name:t.name})]}),(0,a.jsxs)(c.A.Group,{controlId:"description",children:[(0,a.jsx)(c.A.Label,{children:"application"===e.type?s("ShortDescription/(Max255Characters)"):s("Source/Description")}),(0,a.jsx)(c.A.Control,{maxLength:"application"===e.type?255:void 0,size:"sm",type:"text",placeholder:"application"===e.type?s("`Enteryourdocumentdescription`"):s("`Enteryourimagesource/description`"),value:I[r],onChange:e=>P(e,r)})]})]})}),(0,a.jsx)(o.A,{md:3,lg:2,className:"align-self-center text-center",onClick:()=>T(t),children:(0,a.jsx)(d.A,{variant:"dark",children:s("Remove")})})]})}),(0,a.jsxs)(u.A,{show:p,onHide:M,children:[(0,a.jsx)(u.A.Header,{closeButton:!0,children:(0,a.jsx)(u.A.Title,{children:s("DeleteFile")})}),(0,a.jsx)(u.A.Body,{children:s("Areyousurewanttodeletethisfile?")}),(0,a.jsxs)(u.A.Footer,{children:[(0,a.jsx)(d.A,{variant:"secondary",onClick:F,children:s("Cancel")}),(0,a.jsx)(d.A,{variant:"primary",onClick:()=>B(t),children:s("yes")})]})]})]},r));(0,r.useEffect)(()=>{k.forEach(e=>URL.revokeObjectURL(e.preview)),v=[]},[]),(0,r.useEffect)(()=>{e.getImageSource(I)},[I]),(0,r.useEffect)(()=>{O(e.srcText)},[e.srcText]),(0,r.useEffect)(()=>{e&&"true"===e.singleUpload&&E(!1),e&&e.datas&&N([...e.datas.map((t,s)=>(v.push({serverID:t._id,index:e.index?e.index:0,type:t.name.split(".")[1]}),{...t,preview:"".concat("http://localhost:3001/api/v1","/image/show/").concat(t._id)}))])},[e.datas]);let q=async(t,s)=>{if(t.length>s)try{let a=new FormData;a.append("file",t[s]);let r=await x.A.post(R,a,{"Content-Type":"multipart/form-data"});v.push({serverID:r._id,file:t[s],index:e.index?e.index:0,type:t[s].name.split(".")[1]}),q(t,s+1)}catch(e){q(t,s+1)}else e.getImgID(v,e.index?e.index:0)},G=(0,r.useCallback)(async e=>{await q(e,0);let t=e.map(e=>Object.assign(e,{preview:URL.createObjectURL(e)}));_?N(e=>[...e,...t]):N([...t])},[]),{getRootProps:U,getInputProps:V,isDragActive:z,isDragAccept:K,isDragReject:$,fileRejections:H}=(0,i.VB)({accept:e&&e.type?"application/pdf, application/vnd.openxmlformats-officedocument.wordprocessingml.document, application/vnd.oasis.opendocument.text,application/vnd.openxmlformats-officedocument.spreadsheetml.sheet,text/csv":"image/*",multiple:_,minSize:0,maxSize:S,onDrop:G,validator:function(e){if("/image"===R){if("image"!==e.type.substring(0,5))return h.Ay.error(s("toast.filetypenotsupport")),{code:"file-invalid-type",message:"File type not supported"}}else if("/files"===R&&"image"===e.type.substring(0,5))return h.Ay.error(s("toast.filetypenotsupport")),{code:"file-invalid-type",message:"File type not supported"};return null}}),J=(0,r.useMemo)(()=>({...y,...z?j:{outline:"2px dashed #bbb"},...K?{outline:"2px dashed #595959"}:{outline:"2px dashed #bbb"},...$?{outline:"2px dashed red"}:{activeStyle:j}}),[z,$]);t=e&&"application"===e.type?(0,a.jsx)("small",{style:{color:"#595959"},children:s("DocumentWeSupport")}):(0,a.jsx)("small",{style:{color:"#595959"},children:s("ImageWeSupport")});let Q=H.length>0&&H[0].file.size>S;return(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)("div",{className:" d-flex justify-content-center align-items-center mt-3",style:{width:"100%",height:"180px"},children:(0,a.jsxs)("div",{...U({style:J}),children:[(0,a.jsx)("input",{...V()}),(0,a.jsx)(l.g,{icon:n.rOd,size:"4x",color:"#999"}),(0,a.jsx)("p",{style:{color:"#595959",marginBottom:"0px"},children:s("Drag'n'dropsomefileshere,orclicktoselectfiles")}),!_&&(0,a.jsxs)("small",{style:{color:"#595959"},children:[(0,a.jsx)("b",{children:"Note:"})," One single image will be accepted"]}),t,(e.type,Q&&(0,a.jsxs)("small",{className:"text-danger mt-2",children:[(0,a.jsx)(l.g,{icon:n.tUE,size:"1x",color:"red"})," ",s("FileistoolargeItshouldbelessthan20MB")]})),$&&(0,a.jsxs)("small",{className:"text-danger",style:{color:"#595959"},children:[(0,a.jsx)(l.g,{icon:n.tUE,size:"1x",color:"red"})," ",s("Filetypenotacceptedsorr")]})]})}),k.length>0&&(0,a.jsx)("div",{style:b,children:W})]})}}}]);
//# sourceMappingURL=318-0585f5c762262bbc.js.map