"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/_app",{

/***/ "(pages-dir-browser)/./services/authService.tsx":
/*!**********************************!*\
  !*** ./services/authService.tsx ***!
  \**********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var axios__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! axios */ \"(pages-dir-browser)/./node_modules/axios/index.js\");\n//Import Library\n\nconst axios = axios__WEBPACK_IMPORTED_MODULE_0__[\"default\"];\nconst application = \"application/json\";\nclass AuthService {\n    constructor(){\n        this.auth = async (params)=>{\n            try {\n                let url = \"\".concat(\"http://localhost:3001/api/v1\", \"/auth/login\");\n                // if (isAdminLogin) {\n                //   url = `${process.env.API_SERVER}/auth/admin/login`;\n                // }\n                const response = await axios.post(url, {\n                    username: params.username,\n                    password: params.password\n                }, {\n                    headers: {\n                        \"Content-Type\": application\n                    },\n                    withCredentials: true\n                });\n                //if any one of these exist, then there is a field error\n                if (response.status === 201 && response.data) {\n                    // Set client-side authentication flag when user actually logs in\n                    if (true) {\n                        sessionStorage.setItem('userLoggedIn', 'true');\n                        sessionStorage.setItem('loginTimestamp', Date.now().toString());\n                    }\n                    return response;\n                }\n            } catch (error) {\n                const axiosError = error;\n                return axiosError.response ? axiosError.response : {};\n            }\n        };\n        this.logout = async ()=>{\n            try {\n                const _response = await axios.post(\"\".concat(\"http://localhost:3001/api/v1\", \"/auth/logout\"), {}, {\n                    headers: {\n                        \"Content-Type\": application\n                    },\n                    withCredentials: true\n                });\n                localStorage.removeItem(\"persist:root\");\n                // Clear client-side authentication flags\n                if (true) {\n                    sessionStorage.removeItem('userLoggedIn');\n                    sessionStorage.removeItem('loginTimestamp');\n                }\n            } catch (error) {\n                const axiosError = error;\n                return axiosError.response ? axiosError.response : {};\n            }\n        };\n        this.verifySession = async ()=>{\n            try {\n                const response = await axios.get(\"\".concat(\"http://localhost:3001/api/v1\", \"/auth/verify-session\"), {\n                    headers: {\n                        \"Content-Type\": application\n                    },\n                    withCredentials: true\n                });\n                if (response.status === 200 && response.data) {\n                    return response.data;\n                }\n                return {\n                    isAuthenticated: false,\n                    user: null\n                };\n            } catch (error) {\n                return {\n                    isAuthenticated: false,\n                    user: null\n                };\n            }\n        };\n        this.getAuthHeader = async ()=>{\n            return {\n                \"Content-Type\": application\n            };\n        };\n    }\n}\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (new AuthService());\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-browser)/./services/authService.tsx\n"));

/***/ })

});