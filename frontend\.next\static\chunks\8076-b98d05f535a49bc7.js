"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[8076],{8076:(e,s,t)=>{t.r(s),t.d(s,{default:()=>j});var n=t(37876),a=t(14232),l=t(31777),r=t(31195),i=t(29504),o=t(56970),d=t(37784),c=t(60282),u=t(97685),m=t(35611),h=t(54773),p=t(19957),x=t(53718),f=t(31753);let j=(0,l.Ng)()(e=>{let{t:s}=(0,f.Bd)("common"),{isOpen:t,manageClose:l,data:j}=e,v=()=>{l(!1)},[g,A]=(0,a.useState)({_id:"",username:"",firstname:"",lastname:"",position:"",institution:"",role:"",image:null,email:"",password:"",dataConsentPolicy:"",restrictedUsePolicy:"",acceptCookiesPolicy:"",withdrawConsentPolicy:"",medicalConsentPolicy:"",fullDataProtectionConsentPolicy:""}),[y,b]=(0,a.useState)([]);(0,a.useEffect)(()=>{(async()=>{let e=j&&j.institution&&j.institution._id?j.institution._id:"";A(s=>({...s,...j,institution:e,image:null}))})()},[j]),(0,a.useEffect)(()=>{let e={query:{},sort:{title:"asc"},limit:"~",select:"-contact_name -description -type -networks -expertise -hazard_types -hazards -address -focal_points -website -telephone -twitter -header -use_default_header -images -status -email -user -created_at -updated_at -primary_focal_point"};(async()=>{let s=await x.A.get("/institution",e);s&&Array.isArray(s.data)&&b(s.data)})()},[]);let I=e=>{if(e.target){let{name:s,value:t}=e.target;A(e=>({...e,[s]:t}))}},C=async t=>{t.preventDefault(),await x.A.post("/users/updateProfile",g)&&(e.dispatch((0,p.js)()),u.Ay.success(s("toast.ProfileUpdatedSuccessfully")),l(!1))};return(0,n.jsx)("div",{children:(0,n.jsx)(r.A,{show:t,onHide:v,size:"xl",className:"w-100",centered:!0,children:(0,n.jsxs)(h.A,{className:"m-4",onSubmit:C,initialValues:g,enableReinitialize:!0,children:[(0,n.jsx)(r.A.Header,{closeButton:!0,children:(0,n.jsx)(r.A.Title,{children:s("setInfo.editprofile")})}),(0,n.jsxs)(r.A.Body,{children:[(0,n.jsx)("h5",{children:s("setInfo.MyInformation")}),(0,n.jsxs)("div",{className:"p-2 w-100",children:[(0,n.jsxs)(i.A.Group,{as:o.A,controlId:"username",className:"mb-3",children:[(0,n.jsx)(i.A.Label,{column:!0,md:"4",xs:"5",lg:"2",className:"required-field",children:s("setInfo.username")}),(0,n.jsx)(d.A,{md:"8",xs:"7",lg:"10",children:(0,n.jsx)(m.ks,{name:"username",required:!0,type:"text",value:g&&g.username,placeholder:s("setInfo.EnterYourName"),onChange:I})})]}),(0,n.jsxs)(i.A.Group,{as:o.A,controlId:"username",className:"mb-3",children:[(0,n.jsx)(i.A.Label,{column:!0,md:"4",xs:"5",lg:"2",className:"required-field",children:s("setInfo.name")}),(0,n.jsx)(d.A,{md:"8",xs:"7",lg:"10",children:(0,n.jsxs)(o.A,{children:[(0,n.jsx)(d.A,{xs:"12",sm:"6",children:(0,n.jsx)(m.ks,{name:"firstname",required:!0,type:"text",value:g&&g.firstname,placeholder:s("setInfo.Enteryourfirstname"),errorMessage:s("setInfo.Pleaseenteryourfirstname"),onChange:I})}),(0,n.jsx)(d.A,{xs:"12",sm:"6",className:"pt-2 pt-sm-0",children:(0,n.jsx)(m.ks,{name:"lastname",type:"text",value:g&&g.lastname,placeholder:s("setInfo.EnterYourlastname"),onChange:I})})]})})]}),(0,n.jsxs)(i.A.Group,{as:o.A,controlId:"position",className:"mb-3",children:[(0,n.jsx)(i.A.Label,{column:!0,md:"4",xs:"5",lg:"2",children:s("setInfo.position")}),(0,n.jsx)(d.A,{md:"8",xs:"7",lg:"10",children:(0,n.jsx)(m.ks,{name:"position",type:"text",value:g&&g.position,placeholder:s("setInfo.EnterYourposition"),onChange:I})})]}),(0,n.jsxs)(i.A.Group,{as:o.A,controlId:"institution",className:"mb-3",children:[(0,n.jsx)(i.A.Label,{column:!0,md:"4",xs:"5",lg:"2",children:s("setInfo.organisation")}),(0,n.jsx)(d.A,{md:"8",xs:"7",lg:"10",children:(0,n.jsxs)(m.s3,{name:"partner_institution",id:"partner_institution",value:g.institution,onChange:e=>{let{value:s}=e.target;A(e=>({...e,institution:s}))},style:{backgroundColor:"inherit",borderRadius:"5px",color:"#495057"},children:[(0,n.jsx)("option",{value:"",children:s("setInfo.SelectOrganisation")}),y.map((e,s)=>(0,n.jsx)("option",{value:e._id,children:e.title}))]})})]}),(0,n.jsxs)(i.A.Group,{as:o.A,controlId:"Email",className:"mb-3",children:[(0,n.jsx)(i.A.Label,{column:!0,md:"4",xs:"5",lg:"2",className:"required-field",children:s("setInfo.email")}),(0,n.jsx)(d.A,{md:"8",xs:"7",lg:"10",children:(0,n.jsx)(m.ks,{required:!0,name:"email",type:"text",value:g&&g.email,placeholder:s("setInfo.EnterYourEmail"),onChange:I})})]}),(0,n.jsxs)(i.A.Group,{as:o.A,controlId:"password",className:"mb-3",children:[(0,n.jsx)(i.A.Label,{column:!0,md:"4",xs:"5",lg:"2",children:s("setInfo.password")}),(0,n.jsx)(d.A,{md:"8",xs:"7",lg:"10",children:(0,n.jsx)(m.ks,{name:"password",type:"password",value:g.password,placeholder:s("setInfo.EnterYourNewPassword"),onChange:I,pattern:"^(?=.*?[A-Z])(?=.*?[a-z])(?=.*?[0-9])(?=.*?[#?!@$%^&*-]).{8,}$",errorMessage:{pattern:s("setInfo.Passwordshouldcontainatleastcharacter")}})})]})]})]}),(0,n.jsxs)(r.A.Footer,{className:"pb-0",children:[(0,n.jsx)(c.A,{variant:"primary",type:"submit",children:s("setInfo.savechanges")}),(0,n.jsx)(c.A,{variant:"danger",onClick:v,children:s("setInfo.Cancel")})]})]})})})})},35611:(e,s,t)=>{t.d(s,{sx:()=>d,s3:()=>a.s3,ks:()=>a.ks,yk:()=>n.A});var n=t(54773),a=t(59200),l=t(37876),r=t(14232),i=t(39593),o=t(29504);let d={RadioGroup:e=>{let{name:s,valueSelected:t,onChange:n,errorMessage:a,children:o}=e,{errors:d,touched:c}=(0,i.j7)(),u=c[s]&&d[s];r.useMemo(()=>({name:s}),[s]);let m=r.Children.map(o,e=>r.isValidElement(e)&&function(e){return"object"==typeof e&&null!==e}(e.props)?r.cloneElement(e,{name:s,...e.props}):e);return(0,l.jsxs)("div",{children:[(0,l.jsx)("div",{className:"radio-group",children:m}),u&&(0,l.jsx)("div",{className:"invalid-feedback d-block",children:a||("string"==typeof d[s]?d[s]:String(d[s]))})]})},RadioItem:e=>{let{id:s,label:t,value:n,name:a,disabled:r}=e,{values:d,setFieldValue:c}=(0,i.j7)(),u=a||s;return(0,l.jsx)(o.A.Check,{type:"radio",id:s,label:t,value:n,name:u,checked:d[u]===n,onChange:e=>{c(u,e.target.value)},disabled:r,inline:!0})}};n.A,a.ks,a.s3},54773:(e,s,t)=>{t.d(s,{A:()=>o});var n=t(37876),a=t(14232),l=t(39593),r=t(91408);let i=(0,a.forwardRef)((e,s)=>{let{children:t,onSubmit:a,autoComplete:i,className:o,onKeyPress:d,initialValues:c,...u}=e,m=r.Ik().shape({});return(0,n.jsx)(l.l1,{initialValues:c||{},validationSchema:m,onSubmit:(e,s)=>{let t={preventDefault:()=>{},stopPropagation:()=>{},currentTarget:null,target:null,nativeEvent:new Event("submit"),bubbles:!1,cancelable:!0,defaultPrevented:!1,eventPhase:0,isTrusted:!1,timeStamp:Date.now(),type:"submit",isDefaultPrevented:()=>!1,isPropagationStopped:()=>!1,persist:()=>{}};a&&a(t,e,s)},...u,children:e=>(0,n.jsx)(l.lV,{ref:s,onSubmit:e.handleSubmit,autoComplete:i,className:o,onKeyPress:d,children:"function"==typeof t?t(e):t})})});i.displayName="ValidationFormWrapper";let o=i},59200:(e,s,t)=>{t.d(s,{ks:()=>r,s3:()=>i});var n=t(37876);t(14232);var a=t(29504),l=t(39593);let r=e=>{let{name:s,id:t,required:r,validator:i,errorMessage:o,onChange:d,value:c,as:u,multiline:m,rows:h,pattern:p,...x}=e;return(0,n.jsx)(l.D0,{name:s,validate:e=>{let s="string"==typeof e?e:String(e||"");return r&&(!e||""===s.trim())?(null==o?void 0:o.validator)||"This field is required":i&&!i(e)?(null==o?void 0:o.validator)||"Invalid value":p&&e&&!new RegExp(p).test(e)?(null==o?void 0:o.pattern)||"Invalid format":void 0},children:e=>{let{field:s,meta:l}=e;return(0,n.jsxs)(n.Fragment,{children:[(0,n.jsx)(a.A.Control,{...s,...x,id:t,as:u||"input",rows:h,isInvalid:l.touched&&!!l.error,onChange:e=>{s.onChange(e),d&&d(e)},value:void 0!==c?c:s.value}),l.touched&&l.error?(0,n.jsx)(a.A.Control.Feedback,{type:"invalid",children:l.error}):null]})}})},i=e=>{let{name:s,id:t,required:r,errorMessage:i,onChange:o,value:d,children:c,...u}=e;return(0,n.jsx)(l.D0,{name:s,validate:e=>{if(r&&(!e||""===e))return(null==i?void 0:i.validator)||"This field is required"},children:e=>{let{field:s,meta:l}=e;return(0,n.jsxs)(n.Fragment,{children:[(0,n.jsx)(a.A.Control,{as:"select",...s,...u,id:t,isInvalid:l.touched&&!!l.error,onChange:e=>{s.onChange(e),o&&o(e)},value:void 0!==d?d:s.value,children:c}),l.touched&&l.error?(0,n.jsx)(a.A.Control.Feedback,{type:"invalid",children:l.error}):null]})}})}}}]);
//# sourceMappingURL=8076-b98d05f535a49bc7.js.map