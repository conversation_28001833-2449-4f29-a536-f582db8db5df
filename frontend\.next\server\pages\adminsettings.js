"use strict";(()=>{var e={};e.id=3582,e.ids=[636,3220,3582],e.modules={123:e=>{e.exports=require("dom-helpers/removeEventListener")},1332:e=>{e.exports=require("react-custom-scrollbars-2")},1428:e=>{e.exports=import("axios")},1680:e=>{e.exports=require("@restart/ui/utils")},1919:e=>{e.exports=require("react-transition-group/Transition")},3892:e=>{e.exports=require("classnames")},4048:e=>{e.exports=require("react-truncate")},6009:e=>{e.exports=require("dom-helpers/ownerDocument")},6417:(e,s,r)=>{r.d(s,{A:()=>i});let a=r(82015).createContext(null);a.displayName="CardHeaderContext";let i=a},6952:e=>{e.exports=require("@restart/ui/Modal")},7374:e=>{e.exports=require("@restart/ui/Dropdown")},8732:e=>{e.exports=require("react/jsx-runtime")},9653:e=>{e.exports=require("@restart/ui/Overlay")},11242:e=>{e.exports=require("redux-persist/integration/react")},11688:e=>{e.exports=require("@restart/hooks/useWillUnmount")},12388:e=>{e.exports=require("dom-helpers/addEventListener")},13364:e=>{e.exports=require("redux-saga/effects")},14062:e=>{e.exports=import("react-redux")},14078:e=>{e.exports=import("swr")},14332:e=>{e.exports=require("uncontrollable")},16116:e=>{e.exports=require("invariant")},18597:(e,s,r)=>{r.d(s,{A:()=>w});var a=r(3892),i=r.n(a),t=r(82015),n=r(80739),d=r(8732);let o=t.forwardRef(({className:e,bsPrefix:s,as:r="div",...a},t)=>(s=(0,n.oU)(s,"card-body"),(0,d.jsx)(r,{ref:t,className:i()(e,s),...a})));o.displayName="CardBody";let c=t.forwardRef(({className:e,bsPrefix:s,as:r="div",...a},t)=>(s=(0,n.oU)(s,"card-footer"),(0,d.jsx)(r,{ref:t,className:i()(e,s),...a})));c.displayName="CardFooter";var m=r(6417);let l=t.forwardRef(({bsPrefix:e,className:s,as:r="div",...a},o)=>{let c=(0,n.oU)(e,"card-header"),l=(0,t.useMemo)(()=>({cardHeaderBsPrefix:c}),[c]);return(0,d.jsx)(m.A.Provider,{value:l,children:(0,d.jsx)(r,{ref:o,...a,className:i()(s,c)})})});l.displayName="CardHeader";let p=t.forwardRef(({bsPrefix:e,className:s,variant:r,as:a="img",...t},o)=>{let c=(0,n.oU)(e,"card-img");return(0,d.jsx)(a,{ref:o,className:i()(r?`${c}-${r}`:c,s),...t})});p.displayName="CardImg";let x=t.forwardRef(({className:e,bsPrefix:s,as:r="div",...a},t)=>(s=(0,n.oU)(s,"card-img-overlay"),(0,d.jsx)(r,{ref:t,className:i()(e,s),...a})));x.displayName="CardImgOverlay";let u=t.forwardRef(({className:e,bsPrefix:s,as:r="a",...a},t)=>(s=(0,n.oU)(s,"card-link"),(0,d.jsx)(r,{ref:t,className:i()(e,s),...a})));u.displayName="CardLink";var h=r(7783);let j=(0,h.A)("h6"),A=t.forwardRef(({className:e,bsPrefix:s,as:r=j,...a},t)=>(s=(0,n.oU)(s,"card-subtitle"),(0,d.jsx)(r,{ref:t,className:i()(e,s),...a})));A.displayName="CardSubtitle";let g=t.forwardRef(({className:e,bsPrefix:s,as:r="p",...a},t)=>(s=(0,n.oU)(s,"card-text"),(0,d.jsx)(r,{ref:t,className:i()(e,s),...a})));g.displayName="CardText";let f=(0,h.A)("h5"),y=t.forwardRef(({className:e,bsPrefix:s,as:r=f,...a},t)=>(s=(0,n.oU)(s,"card-title"),(0,d.jsx)(r,{ref:t,className:i()(e,s),...a})));y.displayName="CardTitle";let N=t.forwardRef(({bsPrefix:e,className:s,bg:r,text:a,border:t,body:c=!1,children:m,as:l="div",...p},x)=>{let u=(0,n.oU)(e,"card");return(0,d.jsx)(l,{ref:x,...p,className:i()(s,u,r&&`bg-${r}`,a&&`text-${a}`,t&&`border-${t}`),children:c?(0,d.jsx)(o,{children:m}):m})});N.displayName="Card";let w=Object.assign(N,{Img:p,Title:y,Subtitle:A,Body:o,Link:u,Text:g,Header:l,Footer:c,ImgOverlay:x})},18622:e=>{e.exports=require("yup")},19121:e=>{e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},22326:e=>{e.exports=require("react-dom")},22541:e=>{e.exports=require("redux-persist/lib/storage")},25303:e=>{e.exports=require("@restart/ui/NavItem")},26324:e=>{e.exports=require("warning")},27053:(e,s,r)=>{r.d(s,{A:()=>i});var a=r(8732);function i(e){return(0,a.jsx)("h2",{className:"page-heading",children:e.title})}},27825:e=>{e.exports=require("lodash")},27910:e=>{e.exports=require("stream")},28217:e=>{e.exports=require("@restart/ui/DropdownItem")},29021:e=>{e.exports=require("fs")},29294:e=>{e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},29628:e=>{e.exports=require("dom-helpers/addClass")},29825:e=>{e.exports=require("prop-types")},29841:e=>{e.exports=require("dom-helpers/hasClass")},30362:e=>{e.exports=require("@restart/ui/DropdownToggle")},32067:(e,s,r)=>{r.a(e,async(e,a)=>{try{r.r(s),r.d(s,{default:()=>f,getStaticProps:()=>g});var i=r(8732),t=r(49481),n=r(18597),d=r(7082),o=r(83551),c=r(19918),m=r.n(c),l=r(82053),p=r(54131),x=r(27053),u=r(88751),h=r(35576),j=r(45927),A=e([p]);async function g({locale:e}){return{props:{...await (0,h.serverSideTranslations)(e,["common"])}}}p=(A.then?(await A)():A)[0];let f=()=>{let{t:e}=(0,u.useTranslation)("common"),s=()=>(0,i.jsx)(t.A,{md:4,sm:12,className:"mb-2 mt-2",children:(0,i.jsx)("div",{className:"infoCard_admin_card text-center cursor-pointer",children:(0,i.jsx)(m(),{href:"/adminsettings/[...routes]",as:"/adminsettings/area_of_work",children:(0,i.jsx)(n.A,{className:"infoCard ",children:(0,i.jsx)(n.A.Body,{children:(0,i.jsxs)(n.A.Text,{children:[e("adminsetting.adminindex.Areaofwork"),(0,i.jsx)("span",{className:"arrowStyle",children:(0,i.jsx)(l.FontAwesomeIcon,{icon:p.faArrowRight})})]})})})})})}),r=()=>(0,i.jsx)(t.A,{md:4,sm:12,className:"mb-2 mt-2",children:(0,i.jsx)("div",{className:"infoCard_admin_card text-center cursor-pointer",children:(0,i.jsx)(m(),{href:"/adminsettings/[...routes]",as:"/adminsettings/country",children:(0,i.jsx)(n.A,{className:"infoCard",children:(0,i.jsx)(n.A.Body,{children:(0,i.jsxs)(n.A.Text,{children:[e("adminsetting.adminindex.Countries"),(0,i.jsx)("span",{className:"arrowStyle",children:(0,i.jsx)(l.FontAwesomeIcon,{icon:p.faArrowRight})})]})})})})})}),a=()=>(0,i.jsx)(t.A,{md:4,sm:12,className:"mb-2 mt-2",children:(0,i.jsx)("div",{className:" infoCard_admin_card text-center cursor-pointer",children:(0,i.jsx)(m(),{href:"/adminsettings/[...routes]",as:"/adminsettings/deploymentstatus",children:(0,i.jsx)(n.A,{className:"infoCard",children:(0,i.jsx)(n.A.Body,{children:(0,i.jsxs)(n.A.Text,{children:[e("adminsetting.adminindex.DeploymentStatus"),(0,i.jsx)("span",{className:"arrowStyle",children:(0,i.jsx)(l.FontAwesomeIcon,{icon:p.faArrowRight})})]})})})})})}),c=()=>(0,i.jsx)(t.A,{md:4,sm:12,className:"mb-2 mt-2",children:(0,i.jsx)("div",{className:"infoCard_admin_card text-center cursor-pointer",children:(0,i.jsx)(m(),{href:"/adminsettings/[...routes]",as:"/adminsettings/eventstatus",children:(0,i.jsx)(n.A,{className:"infoCard",children:(0,i.jsx)(n.A.Body,{children:(0,i.jsxs)(n.A.Text,{children:[e("adminsetting.adminindex.EventStatus"),(0,i.jsx)("span",{className:"arrowStyle",children:(0,i.jsx)(l.FontAwesomeIcon,{icon:p.faArrowRight})})]})})})})})}),h=()=>(0,i.jsx)(t.A,{md:4,sm:12,className:"mb-2 mt-2",children:(0,i.jsx)("div",{className:"infoCard_admin_card text-center cursor-pointer",children:(0,i.jsx)(m(),{href:"/adminsettings/[...routes]",as:"/adminsettings/expertise",children:(0,i.jsx)(n.A,{className:"infoCard",children:(0,i.jsx)(n.A.Body,{children:(0,i.jsxs)(n.A.Text,{children:[e("adminsetting.adminindex.Expertise"),(0,i.jsx)("span",{className:"arrowStyle",children:(0,i.jsx)(l.FontAwesomeIcon,{icon:p.faArrowRight})})]})})})})})}),A=()=>(0,i.jsx)(t.A,{md:4,sm:12,className:"mb-2 mt-2",children:(0,i.jsx)("div",{className:"infoCard_admin_card text-center cursor-pointer",children:(0,i.jsx)(m(),{href:"/adminsettings/[...routes]",as:"/adminsettings/focal_point",children:(0,i.jsx)(n.A,{className:"infoCard",children:(0,i.jsx)(n.A.Body,{children:(0,i.jsxs)(n.A.Text,{children:[e("adminsetting.adminindex.FocalPointApproval"),(0,i.jsx)("span",{className:"arrowStyle",children:(0,i.jsx)(l.FontAwesomeIcon,{icon:p.faArrowRight})})]})})})})})}),g=()=>(0,i.jsx)(t.A,{md:4,sm:12,className:"mb-2 mt-2",children:(0,i.jsx)("div",{className:"infoCard_admin_card text-center cursor-pointer",children:(0,i.jsx)(m(),{href:"/adminsettings/[...routes]",as:"/adminsettings/Vspace_point",children:(0,i.jsx)(n.A,{className:"infoCard",children:(0,i.jsx)(n.A.Body,{children:(0,i.jsxs)(n.A.Text,{children:[e("adminsetting.adminindex.VspaceApproval"),(0,i.jsx)("span",{className:"arrowStyle",children:(0,i.jsx)(l.FontAwesomeIcon,{icon:p.faArrowRight})})]})})})})})}),f=()=>(0,i.jsx)(t.A,{md:4,sm:12,className:"mb-2 mt-2",children:(0,i.jsx)("div",{className:"infoCard_admin_card text-center cursor-pointer",children:(0,i.jsx)(m(),{href:"/adminsettings/[...routes]",as:"/adminsettings/hazard",children:(0,i.jsx)(n.A,{className:"infoCard",children:(0,i.jsx)(n.A.Body,{children:(0,i.jsxs)(n.A.Text,{children:[e("adminsetting.adminindex.Hazards"),(0,i.jsx)("span",{className:"arrowStyle",children:(0,i.jsx)(l.FontAwesomeIcon,{icon:p.faArrowRight})})]})})})})})}),y=()=>(0,i.jsx)(t.A,{md:4,sm:12,className:"mb-2 mt-2",children:(0,i.jsx)("div",{className:"infoCard_admin_card text-center cursor-pointer",children:(0,i.jsx)(m(),{href:"/adminsettings/[...routes]",as:"/adminsettings/hazardTypes",children:(0,i.jsx)(n.A,{className:"infoCard",children:(0,i.jsx)(n.A.Body,{children:(0,i.jsxs)(n.A.Text,{children:[e("adminsetting.adminindex.HazardTypes"),(0,i.jsx)("span",{className:"arrowStyle",children:(0,i.jsx)(l.FontAwesomeIcon,{icon:p.faArrowRight})})]})})})})})}),N=()=>(0,i.jsx)(t.A,{md:4,sm:12,className:"mb-2 mt-2",children:(0,i.jsx)("div",{className:"infoCard_admin_card text-center cursor-pointer",children:(0,i.jsx)(m(),{href:"/adminsettings/[...routes]",as:"/adminsettings/institution_approval",children:(0,i.jsx)(n.A,{className:"infoCard",children:(0,i.jsx)(n.A.Body,{children:(0,i.jsxs)(n.A.Text,{children:[e("adminsetting.adminindex.OrganisationApproval"),(0,i.jsx)("span",{className:"arrowStyle",children:(0,i.jsx)(l.FontAwesomeIcon,{icon:p.faArrowRight})})]})})})})})}),w=()=>(0,i.jsx)(t.A,{md:4,sm:12,className:"mb-2 mt-2",children:(0,i.jsx)("div",{className:"infoCard_admin_card text-center cursor-pointer",children:(0,i.jsx)(m(),{href:"/adminsettings/[...routes]",as:"/adminsettings/institution_network",children:(0,i.jsx)(n.A,{className:"infoCard",children:(0,i.jsx)(n.A.Body,{children:(0,i.jsxs)(n.A.Text,{children:[e("adminsetting.adminindex.OrganisationNetworks"),(0,i.jsx)("span",{className:"arrowStyle",children:(0,i.jsx)(l.FontAwesomeIcon,{icon:p.faArrowRight})})]})})})})})}),_=()=>(0,i.jsx)(t.A,{md:4,sm:12,className:"mb-2 mt-2",children:(0,i.jsx)("div",{className:"infoCard_admin_card text-center cursor-pointer",children:(0,i.jsx)(m(),{href:"/adminsettings/[...routes]",as:"/adminsettings/institution_type",children:(0,i.jsx)(n.A,{className:"infoCard",children:(0,i.jsx)(n.A.Body,{children:(0,i.jsxs)(n.A.Text,{children:[e("adminsetting.adminindex.OrganisationTypes"),(0,i.jsx)("span",{className:"arrowStyle",children:(0,i.jsx)(l.FontAwesomeIcon,{icon:p.faArrowRight})})]})})})})})}),S=()=>(0,i.jsx)(t.A,{md:4,sm:12,className:"mb-2 mt-2",children:(0,i.jsx)("div",{className:"infoCard_admin_card text-center cursor-pointer",children:(0,i.jsx)(m(),{href:"/adminsettings/[...routes]",as:"/adminsettings/operationstatus",children:(0,i.jsx)(n.A,{className:"infoCard",children:(0,i.jsx)(n.A.Body,{children:(0,i.jsxs)(n.A.Text,{children:[e("adminsetting.adminindex.OperationStatus"),(0,i.jsx)("span",{className:"arrowStyle",children:(0,i.jsx)(l.FontAwesomeIcon,{icon:p.faArrowRight})})]})})})})})}),v=()=>(0,i.jsx)(t.A,{md:4,sm:12,className:"mb-2 mt-2",children:(0,i.jsx)("div",{className:"infoCard_admin_card text-center cursor-pointer",children:(0,i.jsx)(m(),{href:"/adminsettings/[...routes]",as:"/adminsettings/projectstatus",children:(0,i.jsx)(n.A,{className:"infoCard",children:(0,i.jsx)(n.A.Body,{children:(0,i.jsxs)(n.A.Text,{children:[e("adminsetting.adminindex.ProjectStatus"),(0,i.jsx)("span",{className:"arrowStyle",children:(0,i.jsx)(l.FontAwesomeIcon,{icon:p.faArrowRight})})]})})})})})}),C=()=>(0,i.jsx)(t.A,{md:4,sm:12,className:"mb-2 mt-2",children:(0,i.jsx)("div",{className:"infoCard_admin_card text-center cursor-pointer",children:(0,i.jsx)(m(),{href:"/adminsettings/[...routes]",as:"/adminsettings/region",children:(0,i.jsx)(n.A,{className:"infoCard",children:(0,i.jsx)(n.A.Body,{children:(0,i.jsxs)(n.A.Text,{children:[e("adminsetting.adminindex.Regions"),(0,i.jsx)("span",{className:"arrowStyle",children:(0,i.jsx)(l.FontAwesomeIcon,{icon:p.faArrowRight})})]})})})})})}),q=()=>(0,i.jsx)(t.A,{md:4,sm:12,className:"mb-2 mt-2",children:(0,i.jsx)("div",{className:"infoCard_admin_card text-center cursor-pointer",children:(0,i.jsx)(m(),{href:"/adminsettings/[...routes]",as:"/adminsettings/risklevel",children:(0,i.jsx)(n.A,{className:"infoCard",children:(0,i.jsx)(n.A.Body,{children:(0,i.jsxs)(n.A.Text,{children:[e("adminsetting.adminindex.RiskLevels"),(0,i.jsx)("span",{className:"arrowStyle",children:(0,i.jsx)(l.FontAwesomeIcon,{icon:p.faArrowRight})})]})})})})})}),b=()=>(0,i.jsx)(t.A,{md:4,sm:12,className:"mb-2 mt-2",children:(0,i.jsx)("div",{className:"infoCard_admin_card text-center cursor-pointer",children:(0,i.jsx)(m(),{href:"/adminsettings/[...routes]",as:"/adminsettings/syndrome",children:(0,i.jsx)(n.A,{className:"infoCard",children:(0,i.jsx)(n.A.Body,{children:(0,i.jsxs)(n.A.Text,{children:[e("adminsetting.adminindex.Syndromes"),(0,i.jsx)("span",{className:"arrowStyle",children:(0,i.jsx)(l.FontAwesomeIcon,{icon:p.faArrowRight})})]})})})})})}),P=()=>(0,i.jsx)(t.A,{md:4,sm:12,className:"mb-2 mt-2",children:(0,i.jsx)("div",{className:"infoCard_admin_card text-center cursor-pointer",children:(0,i.jsx)(m(),{href:"/adminsettings/[...routes]",as:"/adminsettings/update_type",children:(0,i.jsx)(n.A,{className:"infoCard",children:(0,i.jsx)(n.A.Body,{children:(0,i.jsxs)(n.A.Text,{children:[e("adminsetting.adminindex.UpdatesTypes"),(0,i.jsx)("span",{className:"arrowStyle",children:(0,i.jsx)(l.FontAwesomeIcon,{icon:p.faArrowRight})})]})})})})})}),R=()=>(0,i.jsx)(t.A,{md:4,sm:12,className:"mb-2 mt-2",children:(0,i.jsx)("div",{className:"infoCard_admin_card text-center cursor-pointer",children:(0,i.jsx)(m(),{href:"/adminsettings/[...routes]",as:"/adminsettings/users",children:(0,i.jsx)(n.A,{className:"infoCard",children:(0,i.jsx)(n.A.Body,{children:(0,i.jsxs)(n.A.Text,{children:[e("adminsetting.adminindex.Users"),(0,i.jsx)("span",{className:"arrowStyle",children:(0,i.jsx)(l.FontAwesomeIcon,{icon:p.faArrowRight})})]})})})})})}),T=()=>(0,i.jsx)(t.A,{md:4,sm:12,className:"mb-2 mt-2",children:(0,i.jsx)("div",{className:"infoCard_admin_card text-center cursor-pointer",children:(0,i.jsx)(m(),{href:"/adminsettings/[...routes]",as:"/adminsettings/worldregion",children:(0,i.jsx)(n.A,{className:"infoCard",children:(0,i.jsx)(n.A.Body,{children:(0,i.jsxs)(n.A.Text,{children:[e("adminsetting.adminindex.WorldRegions"),(0,i.jsx)("span",{className:"arrowStyle",children:(0,i.jsx)(l.FontAwesomeIcon,{icon:p.faArrowRight})})]})})})})})}),k=()=>(0,i.jsx)(t.A,{md:4,sm:12,className:"mb-2 mt-2",children:(0,i.jsx)("div",{className:"infoCard_admin_card text-center cursor-pointer",children:(0,i.jsx)(m(),{href:"/adminsettings/[...routes]",as:"/adminsettings/landing",children:(0,i.jsx)(n.A,{className:"infoCard",children:(0,i.jsx)(n.A.Body,{children:(0,i.jsxs)(n.A.Text,{children:[e("adminsetting.adminindex.EditableContent"),(0,i.jsx)("span",{className:"arrowStyle",children:(0,i.jsx)(l.FontAwesomeIcon,{icon:p.faArrowRight})})]})})})})})}),D=()=>(0,i.jsx)(t.A,{md:4,sm:12,className:"mb-2 mt-2",children:(0,i.jsx)("div",{className:"infoCard_admin_card text-center cursor-pointer",children:(0,i.jsx)(m(),{href:"/adminsettings/[...routes]",as:"/adminsettings/content",children:(0,i.jsx)(n.A,{className:"infoCard",children:(0,i.jsx)(n.A.Body,{children:(0,i.jsxs)(n.A.Text,{children:[e("adminsetting.adminindex.Content"),(0,i.jsx)("span",{className:"arrowStyle",children:(0,i.jsx)(l.FontAwesomeIcon,{icon:p.faArrowRight})})]})})})})})}),I=(0,j.canAddAreaOfWork)(()=>(0,i.jsx)(s,{})),F=(0,j.canAddCountry)(()=>(0,i.jsx)(r,{})),O=(0,j.canAddDeploymentStatus)(()=>(0,i.jsx)(a,{})),B=(0,j.canAddEventStatus)(()=>(0,i.jsx)(c,{})),E=(0,j.canAddExpertise)(()=>(0,i.jsx)(h,{})),M=(0,j.canAddFocalPointApproval)(()=>(0,i.jsx)(A,{})),U=(0,j.canAddVspaceApproval)(()=>(0,i.jsx)(g,{})),z=(0,j.canAddHazards)(()=>(0,i.jsx)(f,{})),H=(0,j.canAddHazardTypes)(()=>(0,i.jsx)(y,{})),L=(0,j.canAddOrganisationApproval)(()=>(0,i.jsx)(N,{})),W=(0,j.canAddOrganisationNetworks)(()=>(0,i.jsx)(w,{})),G=(0,j.canAddOrganisationTypes)(()=>(0,i.jsx)(_,{})),V=(0,j.canAddOperationStatus)(()=>(0,i.jsx)(S,{})),$=(0,j.canAddProjectStatus)(()=>(0,i.jsx)(v,{})),X=(0,j.canAddRegions)(()=>(0,i.jsx)(C,{})),J=(0,j.canAddRiskLevels)(()=>(0,i.jsx)(q,{})),K=(0,j.canAddSyndromes)(()=>(0,i.jsx)(b,{})),Q=(0,j.canAddUpdateTypes)(()=>(0,i.jsx)(P,{})),Y=(0,j.canAddUsers)(()=>(0,i.jsx)(R,{})),Z=(0,j.canAddWorldRegion)(()=>(0,i.jsx)(T,{})),ee=(0,j.canAddLandingPage)(()=>(0,i.jsx)(k,{})),es=(0,j.canAddContent)(()=>(0,i.jsx)(D,{}));return(0,i.jsxs)(d.A,{fluid:!0,style:{overflowX:"hidden"},className:"p-0",children:[(0,i.jsx)(x.A,{title:e("menu.adminSettings")}),(0,i.jsxs)(o.A,{children:[(0,i.jsx)(I,{}),(0,i.jsx)(F,{}),(0,i.jsx)(O,{}),(0,i.jsx)(B,{}),(0,i.jsx)(E,{}),(0,i.jsx)(M,{}),(0,i.jsx)(U,{}),(0,i.jsx)(z,{}),(0,i.jsx)(H,{}),(0,i.jsx)(L,{}),(0,i.jsx)(W,{}),(0,i.jsx)(G,{}),(0,i.jsx)(V,{}),(0,i.jsx)($,{}),(0,i.jsx)(X,{}),(0,i.jsx)(J,{}),(0,i.jsx)(K,{}),(0,i.jsx)(Q,{}),(0,i.jsx)(Y,{}),(0,i.jsx)(Z,{}),(0,i.jsx)(ee,{}),(0,i.jsx)(es,{})]})]})};a()}catch(e){a(e)}})},33873:e=>{e.exports=require("path")},35576:e=>{e.exports=require("next-i18next/serverSideTranslations")},36653:e=>{e.exports=require("nprogress")},36955:e=>{e.exports=require("@restart/hooks/useBreakpoint")},37766:e=>{e.exports=require("@restart/hooks/useMergedRefs")},39756:e=>{e.exports=import("redux")},40051:e=>{e.exports=require("dom-helpers/removeClass")},40361:e=>{e.exports=require("next/dist/compiled/next-server/pages.runtime.prod.js")},42893:e=>{e.exports=import("react-hot-toast")},43294:e=>{e.exports=require("formik")},45927:(e,s,r)=>{r.r(s),r.d(s,{canAddAreaOfWork:()=>n,canAddContent:()=>q,canAddCountry:()=>d,canAddDeploymentStatus:()=>o,canAddEventStatus:()=>c,canAddExpertise:()=>m,canAddFocalPointApproval:()=>l,canAddHazardTypes:()=>u,canAddHazards:()=>x,canAddLandingPage:()=>C,canAddOperationStatus:()=>g,canAddOrganisationApproval:()=>h,canAddOrganisationNetworks:()=>j,canAddOrganisationTypes:()=>A,canAddProjectStatus:()=>f,canAddRegions:()=>y,canAddRiskLevels:()=>N,canAddSyndromes:()=>w,canAddUpdateTypes:()=>_,canAddUsers:()=>S,canAddVspaceApproval:()=>p,canAddWorldRegion:()=>v,default:()=>b});var a=r(81366),i=r.n(a);let t="create:any",n=i()({authenticatedSelector:e=>!!e.permissions&&!!e.permissions.area_of_work&&!!e.permissions.area_of_work[t],wrapperDisplayName:"CanAddAreaOfWork"}),d=i()({authenticatedSelector:e=>!!e.permissions&&!!e.permissions.country&&!!e.permissions.country[t],wrapperDisplayName:"CanAddCountry"}),o=i()({authenticatedSelector:e=>!!e.permissions&&!!e.permissions.deployment_status&&!!e.permissions.deployment_status[t],wrapperDisplayName:"CanAddDeploymentStatus"}),c=i()({authenticatedSelector:e=>!!e.permissions&&!!e.permissions.event_status&&!!e.permissions.event_status[t],wrapperDisplayName:"CanAddEventStatus"}),m=i()({authenticatedSelector:e=>!!e.permissions&&!!e.permissions.expertise&&!!e.permissions.expertise[t],wrapperDisplayName:"CanAddExpertise"}),l=i()({authenticatedSelector:e=>!!e.permissions&&!!e.permissions.institution_focal_point&&!!e.permissions.institution_focal_point[t],wrapperDisplayName:"CanAddFocalPointApproval"}),p=i()({authenticatedSelector:e=>!!e.permissions&&!!e.permissions.institution_focal_point&&!!e.permissions.institution_focal_point[t],wrapperDisplayName:"CanAddVspaceApproval"}),x=i()({authenticatedSelector:e=>!!e.permissions&&!!e.permissions.hazard&&!!e.permissions.hazard[t],wrapperDisplayName:"CanAddHazards"}),u=i()({authenticatedSelector:e=>!!e.permissions&&!!e.permissions.hazard_type&&!!e.permissions.hazard_type[t],wrapperDisplayName:"CanAddHazardTypes"}),h=i()({authenticatedSelector:e=>!!e.permissions&&!!e.permissions.institution&&!!e.permissions.institution[t],wrapperDisplayName:"CanAddOrganisationApproval"}),j=i()({authenticatedSelector:e=>!!e.permissions&&!!e.permissions.institution_network&&!!e.permissions.institution_network[t],wrapperDisplayName:"CanAddOrganisationNetworks"}),A=i()({authenticatedSelector:e=>!!e.permissions&&!!e.permissions.institution_type&&!!e.permissions.institution_type[t],wrapperDisplayName:"CanAddOrganisationTypes"}),g=i()({authenticatedSelector:e=>!!e.permissions&&!!e.permissions.operation_status&&!!e.permissions.operation_status[t],wrapperDisplayName:"CanAddOperationStatus"}),f=i()({authenticatedSelector:e=>!!e.permissions&&!!e.permissions.project_status&&!!e.permissions.project_status[t],wrapperDisplayName:"CanAddProjectStatus"}),y=i()({authenticatedSelector:e=>!!e.permissions&&!!e.permissions.region&&!!e.permissions.region[t],wrapperDisplayName:"CanAddRegions"}),N=i()({authenticatedSelector:e=>!!e.permissions&&!!e.permissions.risk_level&&!!e.permissions.risk_level[t],wrapperDisplayName:"CanAddRiskLevels"}),w=i()({authenticatedSelector:e=>!!e.permissions&&!!e.permissions.syndrome&&!!e.permissions.syndrome[t],wrapperDisplayName:"CanAddSyndromes"}),_=i()({authenticatedSelector:e=>!!e.permissions&&!!e.permissions.update_type&&!!e.permissions.update_type[t],wrapperDisplayName:"CanAddUpdateTypes"}),S=i()({authenticatedSelector:e=>!!e.permissions&&!!e.permissions.users&&!!e.permissions.users[t],wrapperDisplayName:"CanAddUsers"}),v=i()({authenticatedSelector:e=>!!e.permissions&&!!e.permissions.worl_region&&!!e.permissions.worl_region[t],wrapperDisplayName:"CanAddWorldRegion"}),C=i()({authenticatedSelector:e=>!!e.permissions&&!!e.permissions.landing_page&&!!e.permissions.landing_page[t],wrapperDisplayName:"CanAddLandingPage"}),q=i()({authenticatedSelector:e=>!!e.permissions&&!!e.permissions.operation&&!!e.permissions.operation[t]&&!!e.permissions.project&&!!e.permissions.project[t]&&!!e.permissions.event&&!!e.permissions.event[t]&&!!e.permissions.vspace&&!!e.permissions.vspace[t]&&!!e.permissions.institution&&!!e.permissions.institution[t]&&!!e.permissions.update&&!!e.permissions.update[t]||!1,wrapperDisplayName:"CanAddContent"}),b=n},50009:e=>{e.exports=require("@restart/ui/DropdownMenu")},54131:e=>{e.exports=import("@fortawesome/free-solid-svg-icons")},57664:e=>{e.exports=require("dom-helpers/scrollbarSize")},58928:e=>{e.exports=require("dom-helpers/canUseDOM")},59717:e=>{e.exports=require("@restart/ui/DropdownContext")},60560:e=>{e.exports=require("dom-helpers/contains")},63033:e=>{e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},65447:e=>{e.exports=require("@restart/ui/Button")},67364:e=>{e.exports=require("@restart/hooks/useCallbackRef")},68455:e=>{e.exports=import("redux-saga")},69722:e=>{e.exports=require("es6-promise")},74075:e=>{e.exports=require("zlib")},74716:e=>{e.exports=require("moment")},74987:e=>{e.exports=require("@restart/ui/ModalManager")},78097:e=>{e.exports=require("next-redux-saga")},78634:e=>{e.exports=require("@restart/ui/Anchor")},80024:(e,s,r)=>{r.a(e,async(e,a)=>{try{r.r(s),r.d(s,{config:()=>j,default:()=>p,getServerSideProps:()=>h,getStaticPaths:()=>u,getStaticProps:()=>x,reportWebVitals:()=>A,routeModule:()=>_,unstable_getServerProps:()=>N,unstable_getServerSideProps:()=>w,unstable_getStaticParams:()=>y,unstable_getStaticPaths:()=>f,unstable_getStaticProps:()=>g});var i=r(63885),t=r(80237),n=r(81413),d=r(9616),o=r.n(d),c=r(72386),m=r(32067),l=e([c,m]);[c,m]=l.then?(await l)():l;let p=(0,n.M)(m,"default"),x=(0,n.M)(m,"getStaticProps"),u=(0,n.M)(m,"getStaticPaths"),h=(0,n.M)(m,"getServerSideProps"),j=(0,n.M)(m,"config"),A=(0,n.M)(m,"reportWebVitals"),g=(0,n.M)(m,"unstable_getStaticProps"),f=(0,n.M)(m,"unstable_getStaticPaths"),y=(0,n.M)(m,"unstable_getStaticParams"),N=(0,n.M)(m,"unstable_getServerProps"),w=(0,n.M)(m,"unstable_getServerSideProps"),_=new i.PagesRouteModule({definition:{kind:t.A.PAGES,page:"/adminsettings",pathname:"/adminsettings",bundlePath:"",filename:""},components:{App:c.default,Document:o()},userland:m});a()}catch(e){a(e)}})},80237:(e,s)=>{Object.defineProperty(s,"A",{enumerable:!0,get:function(){return r}});var r=function(e){return e.PAGES="PAGES",e.PAGES_API="PAGES_API",e.APP_PAGE="APP_PAGE",e.APP_ROUTE="APP_ROUTE",e.IMAGE="IMAGE",e}({})},81366:e=>{e.exports=require("redux-auth-wrapper/connectedAuthWrapper")},81413:(e,s)=>{Object.defineProperty(s,"M",{enumerable:!0,get:function(){return function e(s,r){return r in s?s[r]:"then"in s&&"function"==typeof s.then?s.then(s=>e(s,r)):"function"==typeof s&&"default"===r?s:void 0}}})},81521:e=>{e.exports=require("dom-helpers/querySelectorAll")},81895:e=>{e.exports=require("@restart/hooks/useEventCallback")},82015:e=>{e.exports=require("react")},82053:e=>{e.exports=require("@fortawesome/react-fontawesome")},86842:e=>{e.exports=require("@restart/ui/SelectableContext")},87571:e=>{e.exports=require("dom-helpers/transitionEnd")},88751:e=>{e.exports=require("next-i18next")},93787:e=>{e.exports=require("redux-persist")},94947:e=>{e.exports=require("@restart/hooks/useTimeout")},96196:e=>{e.exports=require("next-redux-wrapper")},98320:e=>{e.exports=require("@restart/hooks/useIsomorphicEffect")},99460:e=>{e.exports=require("dom-helpers/css")}};var s=require("../webpack-runtime.js");s.C(e);var r=e=>s(s.s=e),a=s.X(0,[6089,9216,9616,2386],()=>r(80024));module.exports=a})();