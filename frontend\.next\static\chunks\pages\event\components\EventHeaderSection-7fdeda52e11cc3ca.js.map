{"version": 3, "file": "static/chunks/pages/event/components/EventHeaderSection-7fdeda52e11cc3ca.js", "mappings": "2RAOO,IAAMA,EAAcC,CAAAA,EAAAA,EAAAA,CAAAA,CAAoBA,CAAC,CAC9CC,sBAAuB,KACjBC,EAAMC,WAAW,IAAID,EAAMC,WAAW,CAACC,KAAK,IAAIF,EAAMC,WAAW,CAACC,KAAK,CAAC,aAAa,CAK3FC,CAL6F,kBAKzE,aACtB,GAAG,EAE4BL,CAAAA,EAAAA,EAAAA,CAAAA,CAAoBA,CAAC,CAClDC,sBAAuB,KACjBC,EAAMC,WAAW,IAAID,EAAMC,WAAW,CAACC,KAAK,IAAIF,EAAMC,WAAW,CAACC,KAAK,CAAC,aAAa,CAK3FC,CAL6F,kBAKzE,kBACpBC,iBAAkB,IAAM,UAACC,EAAAA,OAAIA,CAAAA,CAAAA,EAC/B,GAAG,EAEyBP,CAAAA,EAAAA,EAAAA,CAAAA,CAAoBA,CAAC,CAC/CC,sBAAuB,CAACC,EAAOM,KAC7B,GAAIN,EAAMC,WAAW,EAAID,EAAMC,WAAW,CAACC,KAAK,EAAE,GAC5CF,EAAMC,WAAW,CAACC,KAAK,CAAC,aAAa,CACvC,CADyC,MAClC,OAEP,GAAIF,EAAMC,WAAW,CAACC,KAAK,CAAC,aAAa,EAAE,EAC/BA,KAAK,EAAII,EAAMJ,KAAK,CAACK,IAAI,EAAID,EAAMJ,KAAK,CAACK,IAAI,CAACC,GAAG,GAAKR,EAAMO,IAAI,CAACC,GAAG,CAC5E,CAD8E,MACvE,CAGb,CAEF,OAAO,CACT,EACAL,mBAAoB,cACtB,GAAG,EAE6BL,CAAAA,EAAAA,EAAAA,CAAAA,CAAoBA,CAAC,CACnDC,sBAAuB,CAACC,EAAOM,KAC7B,GAAIN,EAAMC,WAAW,EAAID,EAAMC,WAAW,CAACC,KAAK,EAAE,GAC5CF,EAAMC,WAAW,CAACC,KAAK,CAAC,aAAa,CACvC,CADyC,MAClC,OAEP,GAAIF,EAAMC,WAAW,CAACC,KAAK,CAAC,aAAa,EAAE,EAC/BA,KAAK,EAAII,EAAMJ,KAAK,CAACK,IAAI,EAAID,EAAMJ,KAAK,CAACK,IAAI,CAACC,GAAG,GAAKR,EAAMO,IAAI,CAACC,GAAG,CAC5E,CAD8E,MACvE,CAGb,CAEF,OAAO,CACT,EACAL,mBAAoB,mBACpBC,iBAAkB,IAAM,UAACC,EAAAA,OAAIA,CAAAA,CAAAA,EAC/B,GAAG,EAEoCP,CAAAA,EAAAA,EAAAA,CAAAA,CAAoBA,CAAC,CAC1DC,sBAAuB,GACjBC,IAAMC,WAAW,IAAID,EAAMC,WAAW,CAACQ,MAAM,IAAIT,EAAMC,WAAW,CAACQ,MAAM,CAAC,WAAW,CAK3FN,CAL6F,kBAKzE,yBACtB,GAAG,EAEYN,WAAWA,EAAC,0GClE3B,IAAMa,EAAiB,CACrBC,UAAW,YACXC,YAAa,cACbV,MAAO,QACPW,QAAS,UACTC,OAAQ,QACV,EAoEA,EAAeC,CAAAA,EAAAA,EAAAA,EAAAA,CAAOA,CAAC,GAAgBf,GA1DIM,IACzC,GAAM,MAAEC,CAAI,CAAES,UAAQ,YAAEC,CAAU,CAAE,CAAGX,EACjC,CAACY,EAAUC,EAAY,CAAGC,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAU,IAC5C,CAACC,EAAWC,EAAa,CAAGF,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAM,IAG1CG,EAA2B,UAC/B,GAAI,QAAChB,EAAAA,KAAAA,EAAAA,EAAMC,GAAAA,EAAK,CAAXD,MACL,IAAMiB,EAAY,MAAMC,EAAAA,CAAUA,CAACC,GAAG,CAAC,QAAS,CAACC,MAAO,CAACC,UAAWZ,EAAUT,KAAMA,EAAKC,GAAG,CAAEqB,QAASnB,CAAc,CAACO,EAAW,CAAC,GAC9HO,GAAaA,EAAUM,IAAI,EAAIN,EAAUM,IAAI,CAACC,MAAM,CAAG,GAAG,CAC5DT,EAAaE,EAAUM,IAAI,CAAC,EAAE,EAC9BX,GAAY,GAEhB,EAEMa,EAAkB,MAAOC,IAE7B,GADAA,EAAEC,cAAc,GACZ,QAAC3B,EAAAA,KAAAA,EAAAA,EAAMC,GAAAA,EAAK,CAAXD,MACL,IAAM4B,EAAQ,CAACjB,EACTkB,EAAc,CAClBC,YAAapB,EACbW,UAAWZ,EACXT,KAAMA,EAAKC,GAAG,CACdqB,QAASnB,CAAc,CAACO,EAAW,EAErC,GAAIkB,EAAM,CACR,IAAMG,EAAc,MAAMb,EAAAA,CAAUA,CAACc,IAAI,CAAC,QAASH,GAC/CE,GAAUA,EAAO9B,GAAG,EAAE,CACxBc,EAAagB,GACbnB,EAAYgB,GAEhB,KAAO,CACL,IAAMK,EAAW,MAAMf,EAAAA,CAAUA,CAACgB,MAAM,CAAC,SAAuB,OAAdpB,EAAUb,GAAG,GAC3DgC,GAAYA,EAASE,CAAC,EAAE,EACdP,EAEhB,CACF,EAKA,MAHAQ,CAAAA,EAAAA,EAAAA,SAAAA,CAASA,CAAC,KACRpB,GACF,EAAE,EAAE,EAEF,UAACqB,MAAAA,CAAIC,UAAU,0BACb,WAACC,IAAAA,CAAEC,KAAK,GAAGC,QAAShB,YAClB,UAACiB,OAAAA,CAAKJ,UAAU,iBACb3B,EACC,UAACgC,EAAAA,CAAeA,CAAAA,CAACL,UAAU,sBAAsBM,KAAMC,EAAAA,GAAaA,CAAEC,MAAM,YAE5E,UAACH,EAAAA,CAAeA,CAAAA,CAACL,UAAU,sBAAsBM,KAAMG,EAAAA,GAAYA,CAAED,MAAM,WAG/E,UAACH,EAAAA,CAAeA,CAAAA,CAACL,UAAU,WAAWM,KAAMI,EAAAA,GAAUA,CAAEF,MAAM,gBAItE,oBChFA,4CACA,uCACA,WACA,OAAe,EAAQ,KAA4D,CACnF,EACA,SAFsB,uMC+DtB,MArD4B/C,IACxB,GAAM,CAAEkD,CAAC,CAAE,CAAGC,CAAAA,EAAAA,EAAAA,EAAAA,CAAcA,CAAC,QAoDlBC,EAlDLC,EAAqB,IAEnB,UAACC,EAgDqB,EAhDjBA,CACDb,KAAK,qBACLc,GAAI,eAFHD,MAE4C,CAA1BtD,EAAMwD,SAAS,CAACC,MAAM,CAAC,EAAE,WAE5C,WAACC,EAAAA,CAAMA,CAAAA,CAACC,QAAQ,YAAYC,KAAK,eAC7B,UAAChB,EAAAA,CAAeA,CAAAA,CAACC,KAAMgB,EAAAA,GAAKA,GAAI,OACzBX,EAAE,yBAMnBY,EAAeC,CAAAA,EAAAA,EAAAA,YAAAA,CAAYA,CAAC,IAAM,UAACV,EAAAA,CAAAA,IAEzC,MACI,+BACI,WAACW,EAAAA,CAAGA,CAAAA,WACA,WAACC,EAAAA,CAAGA,CAAAA,CAACC,GAAI,aACJC,SAWRA,MAISnE,EAQqBA,EAXnC,MACI,UAACsC,MAAAA,CAAIC,UAAU,0CACX,WAAC6B,KAAAA,WACKpE,OAAAA,GAAAA,MAAAA,GAAAA,EAAOqE,IAAPrE,KAAOqE,EAAPrE,KAAAA,EAAAA,EAAkBsE,GAAlBtE,IAAyB,EACrB,GAAsCuE,MAAAA,CAAnCvE,EAAMqE,SAAS,CAACC,OAAO,CAACE,KAAK,CAAC,OAI3BxE,MAAAA,CAJgCuE,OACpCvE,EAAMqE,SAAS,CAACI,MAAM,CACfC,GAAG,CAAEC,GAAeA,GAAQA,EAAKH,KAAK,EAAIG,EAAKH,KAAK,CAACI,EAAE,CAAGD,EAAKH,KAAK,CAACI,EAAE,CAAG,IAC1EC,IAAI,CAAC,OACZ,MAA0B,OAAtB7E,EAAMqE,SAAS,CAACG,KAAK,CAAC,KAC5B,GAAG,WAEPxE,OAAAA,EAAAA,KAAAA,EAAAA,EAAO8E,OAAP9E,GAAO8E,UAAc9E,GAAAA,OAAAA,EAAAA,EAAOwD,IAAPxD,KAAOwD,EAAPxD,KAAAA,EAAAA,EAAkByD,GAAlBzD,GAAwB,CAAC,EAAE,EAAG,UAAC8D,EAAAA,CAAalE,MAAOI,EAAMqE,SAAS,GAAO,SAIhH,IA1BgB,UAACU,KAAAA,CAAAA,GACD,UAACC,EAAAA,CAAiBA,CAAAA,CAACC,YAAajF,EAAMqE,SAAS,CAACY,WAAW,MAE/D,UAAChB,EAAAA,CAAGA,CAAAA,CAACC,GAAI,WACL,UAACgB,EAAAA,CAAQA,CAAAA,CAACxE,SAAUV,EAAMwD,SAAS,CAACC,MAAM,CAAC,EAAE,CAAE9C,WAAW,gBAuB9E,sFCzBA,MA9B0B,IACxB,GAAM,CAAEuC,CAAC,CAAE,CAAGC,CAAAA,EAAAA,EAAAA,EAAAA,CAAcA,CAAC,QA6BhB6B,EA5BPG,EAAiBC,SAASC,IA4BFL,CA5B8B,CA4B7B,CA3BzB,CAACM,CADyD,CAAK,EACpC,CAAGxE,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,EAAC,GAO7C,MACE,iCAEId,EAAMiF,WAAW,CACjB,UAAC3C,MAAAA,CACCiD,wBAAyBC,CAVZ,CAACC,EAAqBC,KAElC,CAAEC,OADe,CAAED,GAAqBD,EAAYhE,MAAM,CAAG0D,EAAkBM,EAAYG,SAAS,CAAC,EAAGT,GAAkB,MAAQnF,EAAMiF,WAAW,CACzH,CACnC,EAO8CjF,EAAMiF,WAAW,CAACK,GACxD/C,UAAU,kBAEH,KAGTvC,EAAMiF,WAAW,EAAIjF,EAAMiF,WAAW,CAACxD,MAAM,CAAG0D,EAC9C,UAACU,SAAAA,CAAOC,KAAK,SAASvD,UAAU,eAAeG,QAAS,IAAMqD,EAAc,CAACT,YAChEpC,EAAboC,EAAe,WAAgB,GAAFpC,WACjB,OAItB", "sources": ["webpack://_N_E/./pages/event/permission.tsx", "webpack://_N_E/./components/common/Bookmark.tsx", "webpack://_N_E/?cb02", "webpack://_N_E/./pages/event/components/EventHeaderSection.tsx", "webpack://_N_E/./components/common/readMore/readMore.tsx"], "sourcesContent": ["//Import Library\r\nimport React from 'react';\r\nimport connectedAuthWrapper from 'redux-auth-wrapper/connectedAuthWrapper';\r\n\r\n//Import services/components\r\nimport R403 from \"../r403\";\r\n\r\nexport const canAddEvent = connectedAuthWrapper({\r\n  authenticatedSelector: (state) => {\r\n    if (state.permissions && state.permissions.event && state.permissions.event['create:any']) {\r\n      return true;\r\n    }\r\n    return false;\r\n  },\r\n  wrapperDisplayName: 'CanAddEvent',\r\n});\r\n\r\nexport const canAddEventForm = connectedAuthWrapper({\r\n  authenticatedSelector: (state) => {\r\n    if (state.permissions && state.permissions.event && state.permissions.event['create:any']) {\r\n      return true;\r\n    }\r\n    return false;\r\n  },\r\n  wrapperDisplayName: 'CanAddEventForm',\r\n  FailureComponent: () => <R403/>\r\n});\r\n\r\nexport const canEditEvent = connectedAuthWrapper({\r\n  authenticatedSelector: (state, props) => {\r\n    if (state.permissions && state.permissions.event) {\r\n      if (state.permissions.event['update:any']) {\r\n        return true;\r\n      } else { //if update:own\r\n        if (state.permissions.event['update:own']) {\r\n          if (props.event && props.event.user && props.event.user._id === state.user._id) {\r\n            return true;\r\n          }  \r\n        }\r\n      }\r\n    }\r\n    return false;\r\n  },\r\n  wrapperDisplayName: 'CanEditEvent',\r\n});\r\n\r\nexport const canEditEventForm = connectedAuthWrapper({\r\n  authenticatedSelector: (state, props) => {\r\n    if (state.permissions && state.permissions.event) {\r\n      if (state.permissions.event['update:any']) {\r\n        return true;\r\n      } else { //if update:own\r\n        if (state.permissions.event['update:own']) {\r\n          if (props.event && props.event.user && props.event.user._id === state.user._id) {\r\n            return true;\r\n          }\r\n        }\r\n      }\r\n    }\r\n    return false;\r\n  },\r\n  wrapperDisplayName: 'CanEditEventForm',\r\n  FailureComponent: () => <R403/>\r\n});\r\n\r\nexport const canViewDiscussionUpdate = connectedAuthWrapper({\r\n  authenticatedSelector: (state) => {\r\n    if (state.permissions && state.permissions.update && state.permissions.update['read:any']) {\r\n      return true;\r\n    }\r\n    return false;\r\n  },\r\n  wrapperDisplayName: 'CanViewDiscussionUpdate',\r\n});\r\n\r\nexport default canAddEvent;", "//Import Library\r\nimport { connect } from \"react-redux\";\r\nimport {faBookmark, faCheckCircle, faPlusCircle} from \"@fortawesome/free-solid-svg-icons\";\r\nimport {useEffect, useState} from \"react\";\r\nimport {FontAwesomeIcon} from \"@fortawesome/react-fontawesome\";\r\n\r\n//Import services/components\r\nimport apiService from \"../../services/apiService\";\r\n\r\nconst onModelOptions = {\r\n  operation: \"Operation\",\r\n  institution: \"Institution\",\r\n  event: \"Event\",\r\n  project: \"Project\",\r\n  vspace: \"Vspace\"\r\n} as const;\r\n\r\ninterface BookMarkProps {\r\n  user?: {\r\n    _id: string;\r\n  };\r\n  entityId: string;\r\n  entityType: keyof typeof onModelOptions;\r\n}\r\n\r\nconst BookMark: React.FC<BookMarkProps> = (props) => {\r\n  const { user, entityId, entityType } = props;\r\n  const [bookmark, setBookmark] = useState<boolean>(false);\r\n  const [subscribe, setSubscribe] = useState<any>(\"\");\r\n\r\n  //Handle bookmark feature\r\n  const fetchIfContentSubscribed = async () => {\r\n    if (!user?._id) return;\r\n    const checkFlag = await apiService.get('/flag', {query: {entity_id: entityId, user: user._id, onModel: onModelOptions[entityType]}});\r\n    if (checkFlag && checkFlag.data && checkFlag.data.length > 0) {\r\n      setSubscribe(checkFlag.data[0]);\r\n      setBookmark(true);\r\n    }\r\n  };\r\n\r\n  const bookmarkHandler = async (e: React.MouseEvent<HTMLAnchorElement>) => {\r\n    e.preventDefault();\r\n    if (!user?._id) return;\r\n    const flag  = !bookmark;\r\n    const flagPayload = {\r\n      entity_type: entityType,\r\n      entity_id: entityId,\r\n      user: user._id,\r\n      onModel: onModelOptions[entityType]\r\n    }\r\n    if (flag) {\r\n      const flagIt: any = await apiService.post('/flag', flagPayload);\r\n      if (flagIt && flagIt._id) {\r\n        setSubscribe(flagIt);\r\n        setBookmark(flag);\r\n      }\r\n    } else {\r\n      const unFlagIt = await apiService.remove(`/flag/${subscribe._id}`);\r\n      if (unFlagIt && unFlagIt.n) {\r\n        setBookmark(flag);\r\n      }\r\n    }\r\n  }\r\n  //END Bookmark handler\r\n  useEffect(() => {\r\n    fetchIfContentSubscribed();\r\n  },[]);\r\n  return (\r\n    <div className=\"subscribe-flag\">\r\n      <a href=\"\" onClick={bookmarkHandler}>\r\n        <span className=\"check\">\r\n          {bookmark ?\r\n            <FontAwesomeIcon className=\"clickable checkIcon\" icon={faCheckCircle} color=\"#00CC00\" />\r\n            :\r\n            <FontAwesomeIcon className=\"clickable minusIcon\" icon={faPlusCircle} color=\"#fff\"/>\r\n          }\r\n        </span>\r\n        <FontAwesomeIcon className=\"bookmark\" icon={faBookmark} color=\"#d4d4d4\"/>\r\n      </a>\r\n    </div>\r\n  )\r\n}\r\n\r\nexport default connect((state: any) => state)(BookMark);\r\n\r\n", "\n    (window.__NEXT_P = window.__NEXT_P || []).push([\n      \"/event/components/EventHeaderSection\",\n      function () {\n        return require(\"private-next-pages/event/components/EventHeaderSection.tsx\");\n      }\n    ]);\n    if(module.hot) {\n      module.hot.dispose(function () {\n        window.__NEXT_P.push([\"/event/components/EventHeaderSection\"])\n      });\n    }\n  ", "//Import Library\r\nimport React from 'react';\r\nimport { But<PERSON>, Col, Row } from \"react-bootstrap\";\r\nimport Link from \"next/link\";\r\nimport { FontAwesomeIcon } from \"@fortawesome/react-fontawesome\";\r\nimport { faPen } from \"@fortawesome/free-solid-svg-icons\";\r\n\r\n//Import services/components\r\nimport ReadMoreContainer from \"../../../components/common/readMore/readMore\";\r\nimport Bookmark from \"../../../components/common/Bookmark\";\r\nimport { useTranslation } from 'next-i18next';\r\nimport { canEditEvent } from \"../permission\";\r\n\r\n\r\nconst EventHeaderSection = (props: any) => {\r\n    const { t } = useTranslation('common');\r\n\r\n    const EditEventComponent = () => {\r\n        return (\r\n            <Link\r\n                href=\"/event/[...routes]\"\r\n                as={`/event/edit/${props.routeData.routes[1]}`}\r\n                >\r\n                <Button variant=\"secondary\" size=\"sm\">\r\n                    <FontAwesomeIcon icon={faPen} />\r\n                    &nbsp;{t(\"Events.show.Edit\")}\r\n                </Button>\r\n            </Link>\r\n        );\r\n    };\r\n\r\n    const CanEditEvent = canEditEvent(() => <EditEventComponent />);\r\n    \r\n    return (\r\n        <>\r\n            <Row>\r\n                <Col md={11}>\r\n                    {eventdata_func()}\r\n                    <hr />\r\n                    <ReadMoreContainer description={props.eventData.description} />\r\n                </Col>\r\n                <Col md={1}>\r\n                    <Bookmark entityId={props.routeData.routes[1]} entityType=\"event\" />\r\n                </Col>\r\n            </Row>\r\n        </>\r\n    );\r\n\r\n    function eventdata_func() {\r\n        return (\r\n            <div className=\"d-flex justify-content-between\">\r\n                <h4>\r\n                    { props?.eventData?.country\r\n                        ? `${props.eventData.country.title} | ${String(\r\n                            props.eventData.hazard\r\n                                  .map((item: any) => (item && item.title && item.title.en ? item.title.en : \"\"))\r\n                                  .join(\", \")\r\n                          )} (${props.eventData.title})`\r\n                        : \"\"}\r\n                    &nbsp;&nbsp;\r\n                    { props?.editAccess && props?.routeData?.routes[1] ? <CanEditEvent event={props.eventData} /> : null}\r\n                </h4>\r\n            </div>\r\n        );\r\n    }\r\n}\r\n\r\nexport default EventHeaderSection;", "//Import Library\r\nimport { useState } from \"react\";\r\n\r\n//Import services/components\r\nimport { useTranslation } from 'next-i18next';\r\n\r\ninterface ReadMoreContainerProps {\r\n  description: string;\r\n}\r\n\r\nconst ReadMoreContainer = (props: ReadMoreContainerProps) => {\r\n  const { t } = useTranslation('common');\r\n  const readMoreLength = parseInt(process.env.READ_MORE_LENGTH || '200');\r\n  const [isReadMore, setIsReadMore] = useState(false);\r\n\r\n  const createMarkup = (htmlContent: string, isReadMoreInitial: boolean) => {\r\n    const truncateContent = (!isReadMoreInitial && htmlContent.length > readMoreLength) ? htmlContent.substring(0, readMoreLength) + \"...\" : props.description;\r\n    return { __html: truncateContent };\r\n  };\r\n\r\n  return (\r\n    <>\r\n      {\r\n        props.description  ?\r\n        <div\r\n          dangerouslySetInnerHTML={createMarkup(props.description,isReadMore)}\r\n          className=\"operationDesc\"\r\n        >\r\n        </div> : null\r\n      }\r\n      {\r\n        props.description && props.description.length > readMoreLength ?\r\n          <button type=\"button\" className=\"readMoreText\" onClick={() => setIsReadMore(!isReadMore)}>\r\n         {isReadMore ? t(\"readLess\") : t(\"readMore\")}\r\n          </button> : null\r\n      }\r\n    </>\r\n  )\r\n}\r\n\r\nexport default ReadMoreContainer;\r\n"], "names": ["canAddEvent", "connectedAuthWrapper", "authenticatedSelector", "state", "permissions", "event", "wrapperDisplayName", "FailureComponent", "R403", "props", "user", "_id", "update", "onModelOptions", "operation", "institution", "project", "vspace", "connect", "entityId", "entityType", "bookmark", "setBookmark", "useState", "subscribe", "setSubscribe", "fetchIfContentSubscribed", "checkFlag", "apiService", "get", "query", "entity_id", "onModel", "data", "length", "bookmarkHandler", "e", "preventDefault", "flag", "flagPayload", "entity_type", "flagIt", "post", "unFlagIt", "remove", "n", "useEffect", "div", "className", "a", "href", "onClick", "span", "FontAwesomeIcon", "icon", "faCheckCircle", "color", "faPlusCircle", "faBookmark", "t", "useTranslation", "EventHeaderSection", "EditEventComponent", "Link", "as", "routeData", "routes", "<PERSON><PERSON>", "variant", "size", "faPen", "CanEditEvent", "canEditEvent", "Row", "Col", "md", "eventdata_func", "h4", "eventData", "country", "String", "title", "hazard", "map", "item", "en", "join", "editAccess", "hr", "ReadMoreContainer", "description", "Bookmark", "readMoreLength", "parseInt", "process", "isReadMore", "dangerouslySetInnerHTML", "createMarkup", "htmlContent", "isReadMoreInitial", "__html", "substring", "button", "type", "setIsReadMore"], "sourceRoot": "", "ignoreList": []}