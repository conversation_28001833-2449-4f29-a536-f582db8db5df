{"version": 3, "file": "static/chunks/pages/vspace/AcceptRequestVspace-2ac3d640b399e3e5.js", "mappings": "+EACA,4CACA,8BACA,WACA,OAAe,EAAQ,KAAmD,CAC1E,EACA,SAFsB,yLC0FtB,MAlFqB,IAiBjB,GAAM,CAACA,EAASC,EAAW,CAAGC,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAC,IACjC,CAACC,EAAYC,EAAc,CAAGF,CAAAA,CAgEbG,CAhEaH,CAgEZ,CAhEYA,QAAAA,CAAQA,CAjBlB,CACtBI,MAAO,GACPC,YAAa,GACbC,UAAW,KACXC,QAAS,KACTC,WAAY,GACZC,YAAY,EACZC,OAAQ,EAAE,CACVC,SAAS,EACTC,cAAe,GACfC,WAAY,EAAE,CACdC,WAAY,EAAE,CACdC,QAAS,EAAE,CACXC,QAAS,EAAE,CACXC,SAAU,EAAE,GAIV,CAACC,EAAMC,EAAQ,CAAGnB,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAC,IACvB,GAAEoB,CAAC,CAAE,CAAGC,CAAAA,EAAAA,EAAAA,EAAAA,CAAcA,CAAC,UAE3BC,EADSC,CAAAA,EAAAA,EAAAA,SAAAA,CAASA,GACGC,KAAK,CAACF,MAAM,EAAI,EAAE,CAE7CG,CAAAA,EAAAA,EAAAA,SAAAA,CAASA,CAAC,KACNC,GACJ,EAAG,EAAE,EACL,IAAMA,EAAY,UACd,IAAMC,EAAiB,MAAMC,EAAAA,CAAUA,CAACC,GAAG,CAAC,WAAqB,OAAVP,CAAM,CAAC,EAAE,GAC1DQ,EAAQ,MAAMF,EAAAA,CAAUA,CAACC,GAAG,CAAC,UAAoB,OAAVP,CAAM,CAAC,EAAE,GAClDK,IACA5B,EAAW4B,EAAelB,QADV,EACoB,EACpCP,EAAcyB,IAEdG,GACAX,EAAQW,EAAMC,QAAQ,CAE9B,EACMC,EAAyB,UAC3B/B,EAAWQ,UAAU,EAAG,EAExBwB,MADqBC,IAAAA,IAAM,CAACjC,EAAWkC,WAAW,CAAE,CAAEC,IAAKd,CAAM,CAAC,EAAE,IAC5CrB,EAAWkC,WAAW,CAACE,IAAI,CAACf,CAAM,CAAC,EAAE,EAC7D,IAAMgB,EAAiB,MAAMV,EAAAA,CAAUA,CAACW,IAAI,CAAC,qCAAoD,OAAftC,EAAWmC,GAAG,EAAInC,GAChGqC,GAAkBA,EAAeF,GAAG,EAAE,CACtCrC,GAAW,GACXyC,EAAAA,EAAKA,CAACC,OAAO,CAACrB,EAAE,iCAChBsB,IAAAA,IAAW,CAAC,YAEE,kBAAlBJ,GAAsCE,EAAAA,EAAKA,CAACG,KAAK,CAACvB,EAAE,uBACxD,EAQA,MACI,UAACwB,MAAAA,UACG,UAACC,EAAAA,CAAGA,CAAAA,CAACC,UAAU,gBACX,WAACC,EAAAA,CAAGA,CAAAA,WACA,UAACH,MAAAA,UAAI,sCACL,UAACI,IAAAA,UAAG9B,IAAS,uDAAoD,UAAC8B,IAAAA,UAAG/C,EAAWG,KAAK,GACrF,UAAC6C,KAAAA,CAAAA,GACD,UAACC,EAAAA,CAAMA,CAAAA,CACHC,SAAUrD,EACVgD,UAAU,OACVM,KAAK,SACLC,QAAQ,UACRC,QAAStB,WAERZ,EAAE,YAEP,UAAC8B,EAAAA,CAAMA,CAAAA,CAACC,SAAUrD,EAASgD,UAAU,OAAOO,QAAQ,OAAOC,QAtB9C,CAsBuDC,IArBhFxD,GAAW,GACXyC,EAAAA,EAAKA,CAACG,KAAK,CAACvB,EAAE,iCACdsB,IAAAA,IAAW,CAAC,UAChB,WAmBqBtB,EAAE,mBAM3B", "sources": ["webpack://_N_E/?9d7c", "webpack://_N_E/./pages/vspace/AcceptRequestVspace.tsx"], "sourcesContent": ["\n    (window.__NEXT_P = window.__NEXT_P || []).push([\n      \"/vspace/AcceptRequestVspace\",\n      function () {\n        return require(\"private-next-pages/vspace/AcceptRequestVspace.tsx\");\n      }\n    ]);\n    if(module.hot) {\n      module.hot.dispose(function () {\n        window.__NEXT_P.push([\"/vspace/AcceptRequestVspace\"])\n      });\n    }\n  ", "//Import Library\r\nimport Router, { useRouter } from \"next/router\";\r\nimport { Button, Col, Row } from \"react-bootstrap\";\r\n\r\nimport toast from 'react-hot-toast';\r\nimport _ from \"lodash\";\r\nimport { useEffect, useState } from \"react\";\r\n\r\n//Import services/components\r\nimport apiService from \"../../services/apiService\";\r\nimport { useTranslation } from 'next-i18next';\r\n\r\nconst AcceptVspace = (_props: any) => {\r\n    const initialState: any = {\r\n        title: \"\",\r\n        description: \"\",\r\n        startDate: null,\r\n        endDate: null,\r\n        searchData: \"\",\r\n        visibility: true,\r\n        images: [],\r\n        checked: false,\r\n        file_category: \"\",\r\n        nonMembers: [],\r\n        images_src: [],\r\n        members: [],\r\n        doc_src: [],\r\n        document: [],\r\n    };\r\n    const [disable, setDisable] = useState(false);\r\n    const [vspacedata, setVspaceData] = useState(initialState);\r\n    const [user, setUser] = useState(\"\");\r\n        const { t } = useTranslation('common');\r\n    const router = useRouter();\r\n    const routes: any = router.query.routes || [];\r\n\r\n    useEffect(() => {\r\n        GetvSpace();\r\n    }, []);\r\n    const GetvSpace = async () => {\r\n        const vspaceResponse = await apiService.get(`/vspace/${routes[1]}`);\r\n        const _user = await apiService.get(`/users/${routes[3]}`);\r\n        if (vspaceResponse) {\r\n            setDisable(vspaceResponse.visibility);\r\n            setVspaceData(vspaceResponse);\r\n        }\r\n        if (_user) {\r\n            setUser(_user.username);\r\n        }\r\n    };\r\n    const acceptedRequesrtVspace = async () => {\r\n        vspacedata.visibility = true;\r\n        const isSubscribed = _.find(vspacedata.subscribers, { _id: routes[3] });\r\n        isSubscribed == null && vspacedata.subscribers.push(routes[3]);\r\n        const Updateresponse = await apiService.post(`/vspace/acceptSubscriptionRequest/${vspacedata._id}`, vspacedata);\r\n        if (Updateresponse && Updateresponse._id) {\r\n            setDisable(true);\r\n            toast.success(t(\"Vspaceissuccessfullyaccepted\"));\r\n            Router.push(\"/vspace\");\r\n        }\r\n        Updateresponse == \"Not authorized\" && toast.error(t(\"Youarenotauthorized\"));\r\n    };\r\n\r\n    const declineRequestVspace = () => {\r\n        setDisable(true);\r\n        toast.error(t(\"VspaceissuccessfullyDeclined\"));\r\n        Router.push(\"/vspace\");\r\n    };\r\n\r\n    return (\r\n        <div>\r\n            <Row className=\"my-4\">\r\n                <Col>\r\n                    <div>Welcome to Robert Koch Institut !</div>\r\n                    <b>{user}</b> has requested to access your private virtual space <b>{vspacedata.title}</b>\r\n                    <br />\r\n                    <Button\r\n                        disabled={disable}\r\n                        className=\"me-2\"\r\n                        type=\"submit\"\r\n                        variant=\"primary\"\r\n                        onClick={acceptedRequesrtVspace}\r\n                    >\r\n                        {t(\"Accept\")}\r\n                    </Button>\r\n                    <Button disabled={disable} className=\"me-2\" variant=\"info\" onClick={declineRequestVspace}>\r\n                        {t(\"Decline\")}\r\n                    </Button>\r\n                </Col>\r\n            </Row>\r\n        </div>\r\n    );\r\n};\r\n\r\nexport default AcceptVspace;\r\n"], "names": ["disable", "setDisable", "useState", "vspacedata", "setVspaceData", "AcceptVspace", "title", "description", "startDate", "endDate", "searchData", "visibility", "images", "checked", "file_category", "nonMembers", "images_src", "members", "doc_src", "document", "user", "setUser", "t", "useTranslation", "routes", "useRouter", "query", "useEffect", "GetvSpace", "vspaceResponse", "apiService", "get", "_user", "username", "acceptedRequesrtVspace", "isSubscribed", "_", "subscribers", "_id", "push", "Updateresponse", "post", "toast", "success", "Router", "error", "div", "Row", "className", "Col", "b", "br", "<PERSON><PERSON>", "disabled", "type", "variant", "onClick", "declineRequestVspace"], "sourceRoot": "", "ignoreList": []}