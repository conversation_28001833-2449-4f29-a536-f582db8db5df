"use strict";(()=>{var e={};e.id=1944,e.ids=[636,1944,3220],e.modules={123:e=>{e.exports=require("dom-helpers/removeEventListener")},1332:e=>{e.exports=require("react-custom-scrollbars-2")},1428:e=>{e.exports=import("axios")},1680:e=>{e.exports=require("@restart/ui/utils")},1919:e=>{e.exports=require("react-transition-group/Transition")},2757:(e,r,t)=>{t.a(e,async(e,a)=>{try{t.r(r),t.d(r,{default:()=>P,getStaticProps:()=>S});var s=t(8732),i=t(82015),o=t(50843),n=t.n(o),l=t(19918),u=t.n(l),p=t(41803),c=t.n(p),d=t(7082),x=t(83551),h=t(49481),m=t(34191),g=t(27825),f=t.n(g),q=t(50230),y=t(63487),w=t(82427),v=t(27053),A=t(88751),j=t(35576),b=e([m,q,y]);async function S({locale:e}){return{props:{...await (0,j.serverSideTranslations)(e,["common"])}}}[m,q,y]=b.then?(await b)():b;let P=()=>{let{t:e,i18n:r}=(0,A.useTranslation)("common"),t="fr"===r.language?"en":r.language,a=t?`title.${t}`:"title.en",o="en"===t?w.E:w.M,[l]=(0,i.useState)(o),[p,g]=(0,i.useState)([]),[j,b]=(0,i.useState)([]),[S,P]=(0,i.useState)([]),[k,C]=(0,i.useState)(0),[N,_]=(0,i.useState)(1),[M]=(0,i.useState)(50),[z,E]=(0,i.useState)("All"),[L,O]=(0,i.useState)(!0),[I,T]=(0,i.useState)([]),[D,G]=(0,i.useState)(""),[R]=(0,i.useState)(a),[$,U]=(0,i.useState)(!0),[W,B]=(0,i.useState)({}),F=async e=>{await O(!0);let r=await y.A.get("/hazard",e);r&&r.data&&(await O(!1),await b(r.data),await C(r.totalCount))},V=async(r,a)=>{if(await E(l[a]),l[a]===e("All")||l[a]===e("Alle")){if(U(!0),await P([]),Array.isArray(I)&&0!==I.length){let e=async()=>{r.query={hazard_type:I},r.page=1;let e=await y.A.get("/hazard",r);await g(e.data),await C(e.totalCount)},r={query:{},limit:M,page:N,sort:{[R]:"asc"}};e()}}else{let e=[];G("");let r=l[a].toLowerCase();U(!1),j.map(async(a,s)=>{if(a.title[t].toLowerCase().split("")[0]===r&&a.enabled){let r={title:a.title,_id:a._id,coordinates:a.coordinates};e.push(r)}}),e.length>=1?(await P(e),C(e.length)):(await P([]),await g([]),await C([]),await B({}))}},H=async e=>{let r=e.selected+1;if(await _(r),Array.isArray(I)&&0!==I.length){let e=async()=>{t.query={hazard_type:I};let e=await y.A.get("/hazard",t);await g(e.data),await C(e.totalCount),await B(e)},t={query:{},limit:M,page:r,sort:{[R]:"asc"}};e()}else{let e=async()=>{let e=await y.A.get("/hazard",t);g(e.data)},t={query:{},limit:M,page:r,sort:{[R]:"asc"}};await e()}},X=async e=>{if(U(!0),Array.isArray(e)&&0===e.length){await g([]),await C(0),await E("All"),await B({}),await b([]),P([]),T([]);return}F({query:{hazard_type:e},limit:"~",sort:{[R]:"asc"}}),T(e);let r=[],t={query:{},limit:M,page:N,sort:{[R]:"asc"}};(async()=>{t.sort={[R]:"asc"},t.query={hazard_type:e},t.page=1;let a=await y.A.get("/hazard",t);r=a.data,await g(r),await C(a.totalCount),await E("All"),await B(a),P([])})()},J={query:{},sort:{[R]:"asc"}},K=async()=>{let e=await y.A.get("/hazard",J);g(e.data)},Q=(e,r)=>{e?J.query={[R]:e&&e[0].toUpperCase()+e.slice(1).toLowerCase()}:J.query={},"ACTIVE"!==z&&(J.query={...J.query,hazard_type:r}),K()},Y=(0,i.useRef)(f().debounce((e,r)=>Q(e,r),Number("500")||300)).current;return(0,s.jsxs)(d.A,{fluid:!0,className:"p-0",children:[(0,s.jsx)(x.A,{children:(0,s.jsx)(h.A,{xs:12,children:(0,s.jsx)(v.A,{title:e("menu.hazards")})})}),(0,s.jsxs)("div",{className:"hazard-image-block",children:[(0,s.jsx)("img",{className:"hazard-image-cover",src:"/images/hazard.838eccb4.jpg",alt:"Logo"}),(0,s.jsx)(q.A,{currentLang:t,filterByletter:z,filthaz:X})]}),(0,s.jsxs)("div",{className:"alphabetBlock",children:[(0,s.jsx)("div",{className:"alphabetContainer",children:l.map((e,r)=>(0,s.jsx)("span",{className:`alphabetItems ${z===e?"active":""}`,onClick:e=>V(e,r),children:e},r))}),$&&(0,s.jsx)("div",{children:(0,s.jsx)(x.A,{children:(0,s.jsx)(h.A,{className:"mt-3 mx-3",children:(0,s.jsx)(m.default,{onFilter:e=>{G(e.target.value),Y(e.target.value,I)},filterText:D})})})}),(0,s.jsx)("div",{className:"alphabetLists",children:L?(0,s.jsx)(c(),{animation:"border",variant:"primary"}):0!==S.length?S.map((e,r)=>(0,s.jsx)("li",{className:"alphaListItems clearfix",children:(0,s.jsx)(u(),{href:"/hazard/[...routes]",as:`/hazard/show/${e._id}`,children:e.title&&e.title[t]?e.title[t]:""})},r)):p.length>=1?(0,s.jsx)("ul",{children:p.map((e,r)=>e.enabled?(0,s.jsx)("li",{className:"alphaListItems clearfix",children:(0,s.jsx)(u(),{href:"/hazard/[...routes]",as:`/hazard/show/${e._id}`,children:e.title&&e.title[t]?e.title[t]:""})},r):null)}):(0,s.jsxs)("div",{className:"noresultFound",children:[" ",e("Noresultsfound")," "]})}),k>M&&!D?(0,s.jsx)("div",{className:"hazards-pagination",children:(0,s.jsx)(n(),{pageCount:Math.ceil(W.totalCount/W.limit),pageRangeDisplayed:5,marginPagesDisplayed:2,onPageChange:H,forcePage:W.page-1,containerClassName:"pagination",pageClassName:"page-item",pageLinkClassName:"page-link",previousClassName:"page-item",previousLinkClassName:"page-link",nextClassName:"page-item",nextLinkClassName:"page-link",activeClassName:"active",disabledClassName:"disabled",previousLabel:"‹",nextLabel:"›"})}):null]})]})};a()}catch(e){a(e)}})},3892:e=>{e.exports=require("classnames")},4048:e=>{e.exports=require("react-truncate")},6009:e=>{e.exports=require("dom-helpers/ownerDocument")},6952:e=>{e.exports=require("@restart/ui/Modal")},7374:e=>{e.exports=require("@restart/ui/Dropdown")},8732:e=>{e.exports=require("react/jsx-runtime")},9653:e=>{e.exports=require("@restart/ui/Overlay")},11242:e=>{e.exports=require("redux-persist/integration/react")},11688:e=>{e.exports=require("@restart/hooks/useWillUnmount")},12388:e=>{e.exports=require("dom-helpers/addEventListener")},13364:e=>{e.exports=require("redux-saga/effects")},14062:e=>{e.exports=import("react-redux")},14078:e=>{e.exports=import("swr")},14332:e=>{e.exports=require("uncontrollable")},16116:e=>{e.exports=require("invariant")},18622:e=>{e.exports=require("yup")},19121:e=>{e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},21852:(e,r,t)=>{t.d(r,{A:()=>d});var a=t(3892),s=t.n(a),i=t(82015),o=t(80739),n=t(52755),l=t(24765),u=t(8732);let p=i.forwardRef(({className:e,bsPrefix:r,as:t="span",...a},i)=>(r=(0,o.oU)(r,"input-group-text"),(0,u.jsx)(t,{ref:i,className:s()(e,r),...a})));p.displayName="InputGroupText";let c=i.forwardRef(({bsPrefix:e,size:r,hasValidation:t,className:a,as:n="div",...p},c)=>{e=(0,o.oU)(e,"input-group");let d=(0,i.useMemo)(()=>({}),[]);return(0,u.jsx)(l.A.Provider,{value:d,children:(0,u.jsx)(n,{ref:c,...p,className:s()(a,e,r&&`${e}-${r}`,t&&"has-validation")})})});c.displayName="InputGroup";let d=Object.assign(c,{Text:p,Radio:e=>(0,u.jsx)(p,{children:(0,u.jsx)(n.A,{type:"radio",...e})}),Checkbox:e=>(0,u.jsx)(p,{children:(0,u.jsx)(n.A,{type:"checkbox",...e})})})},22326:e=>{e.exports=require("react-dom")},22541:e=>{e.exports=require("redux-persist/lib/storage")},22810:(e,r,t)=>{t.a(e,async(e,a)=>{try{t.r(r),t.d(r,{config:()=>g,default:()=>d,getServerSideProps:()=>m,getStaticPaths:()=>h,getStaticProps:()=>x,reportWebVitals:()=>f,routeModule:()=>j,unstable_getServerProps:()=>v,unstable_getServerSideProps:()=>A,unstable_getStaticParams:()=>w,unstable_getStaticPaths:()=>y,unstable_getStaticProps:()=>q});var s=t(63885),i=t(80237),o=t(81413),n=t(9616),l=t.n(n),u=t(72386),p=t(2757),c=e([u,p]);[u,p]=c.then?(await c)():c;let d=(0,o.M)(p,"default"),x=(0,o.M)(p,"getStaticProps"),h=(0,o.M)(p,"getStaticPaths"),m=(0,o.M)(p,"getServerSideProps"),g=(0,o.M)(p,"config"),f=(0,o.M)(p,"reportWebVitals"),q=(0,o.M)(p,"unstable_getStaticProps"),y=(0,o.M)(p,"unstable_getStaticPaths"),w=(0,o.M)(p,"unstable_getStaticParams"),v=(0,o.M)(p,"unstable_getServerProps"),A=(0,o.M)(p,"unstable_getServerSideProps"),j=new s.PagesRouteModule({definition:{kind:i.A.PAGES,page:"/hazard",pathname:"/hazard",bundlePath:"",filename:""},components:{App:u.default,Document:l()},userland:p});a()}catch(e){a(e)}})},25303:e=>{e.exports=require("@restart/ui/NavItem")},26324:e=>{e.exports=require("warning")},27053:(e,r,t)=>{t.d(r,{A:()=>s});var a=t(8732);function s(e){return(0,a.jsx)("h2",{className:"page-heading",children:e.title})}},27825:e=>{e.exports=require("lodash")},27910:e=>{e.exports=require("stream")},28217:e=>{e.exports=require("@restart/ui/DropdownItem")},29021:e=>{e.exports=require("fs")},29294:e=>{e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},29628:e=>{e.exports=require("dom-helpers/addClass")},29825:e=>{e.exports=require("prop-types")},29841:e=>{e.exports=require("dom-helpers/hasClass")},30362:e=>{e.exports=require("@restart/ui/DropdownToggle")},33873:e=>{e.exports=require("path")},34191:(e,r,t)=>{t.a(e,async(e,a)=>{try{t.r(r),t.d(r,{default:()=>x});var s=t(8732),i=t(7082),o=t(83551),n=t(21852),l=t(59549),u=t(82053),p=t(54131),c=t(88751),d=e([p]);p=(d.then?(await d)():d)[0];let x=({filterText:e,onFilter:r})=>{let{t}=(0,c.useTranslation)("common");return(0,s.jsx)(i.A,{fluid:!0,className:"p-0",children:(0,s.jsx)(o.A,{children:(0,s.jsxs)(n.A,{children:[(0,s.jsx)(l.A.Control,{className:"rounded",type:"text",placeholder:t("SearchHazards"),value:e,onChange:r}),(0,s.jsx)("div",{className:"search-icon",children:(0,s.jsx)(u.FontAwesomeIcon,{icon:p.faSearch})})]})})})};a()}catch(e){a(e)}})},35576:e=>{e.exports=require("next-i18next/serverSideTranslations")},36653:e=>{e.exports=require("nprogress")},36955:e=>{e.exports=require("@restart/hooks/useBreakpoint")},37766:e=>{e.exports=require("@restart/hooks/useMergedRefs")},39756:e=>{e.exports=import("redux")},40051:e=>{e.exports=require("dom-helpers/removeClass")},40361:e=>{e.exports=require("next/dist/compiled/next-server/pages.runtime.prod.js")},41803:(e,r,t)=>{var a=t(92921);r.__esModule=!0,r.default=void 0;var s=a(t(3892)),i=function(e,r){if(e&&e.__esModule)return e;if(null===e||"object"!=typeof e&&"function"!=typeof e)return{default:e};var t=l(r);if(t&&t.has(e))return t.get(e);var a={__proto__:null},s=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var i in e)if("default"!==i&&Object.prototype.hasOwnProperty.call(e,i)){var o=s?Object.getOwnPropertyDescriptor(e,i):null;o&&(o.get||o.set)?Object.defineProperty(a,i,o):a[i]=e[i]}return a.default=e,t&&t.set(e,a),a}(t(82015)),o=t(11940),n=t(8732);function l(e){if("function"!=typeof WeakMap)return null;var r=new WeakMap,t=new WeakMap;return(l=function(e){return e?t:r})(e)}let u=i.forwardRef(({bsPrefix:e,variant:r,animation:t="border",size:a,as:i="div",className:l,...u},p)=>{e=(0,o.useBootstrapPrefix)(e,"spinner");let c=`${e}-${t}`;return(0,n.jsx)(i,{ref:p,...u,className:(0,s.default)(l,c,a&&`${c}-${a}`,r&&`text-${r}`)})});u.displayName="Spinner",r.default=u,e.exports=r.default},42893:e=>{e.exports=import("react-hot-toast")},43294:e=>{e.exports=require("formik")},50009:e=>{e.exports=require("@restart/ui/DropdownMenu")},50230:(e,r,t)=>{t.a(e,async(e,a)=>{try{t.d(r,{A:()=>d});var s=t(8732),i=t(82015);t(27825);var o=t(59549),n=t(91353),l=t(63487),u=t(88751),p=e([l]);function c(e){let{t:r}=(0,u.useTranslation)("common"),{filthaz:t}=e,[a,l]=(0,i.useState)(!0),[p,c]=(0,i.useState)([]),[d,x]=(0,i.useState)([]),h=e=>{let r=[...p],a=[...d];r.forEach((t,s)=>{t.code===e.target.id&&(r[s].isChecked=e.target.checked,e.target.checked?a.push(t._id):a=a.filter(e=>e!==t._id))}),x(a),t(a),l(!1),c(r)};return(0,s.jsxs)("div",{className:"hazards-multi-checkboxes",children:[(0,s.jsx)(o.A.Check,{type:"checkbox",id:"all",checked:a,label:r("Events.forms.AllHazards"),onChange:e=>{let r=p.map(r=>({...r,isChecked:e.target.checked})),a=[];e.target.checked&&(a=r.map(e=>e._id)),t(a),x(a),l(e.target.checked),c(r)}}),p.map((e,r)=>(0,s.jsx)(o.A.Check,{type:"checkbox",id:e.code,label:e.title,value:e.code,onChange:h,checked:p[r].isChecked},r)),(0,s.jsx)(n.A,{onClick:()=>{let e=p.map(e=>({...e,isChecked:!1}));x([]),l(!1),c(e),t([])},className:"btn-plain ps-2",children:r("ClearAll")})]})}l=(p.then?(await p)():p)[0],c.defaultProps={filthaz:()=>{}};let d=c;a()}catch(e){a(e)}})},50843:e=>{e.exports=require("react-paginate")},54131:e=>{e.exports=import("@fortawesome/free-solid-svg-icons")},57664:e=>{e.exports=require("dom-helpers/scrollbarSize")},58928:e=>{e.exports=require("dom-helpers/canUseDOM")},59717:e=>{e.exports=require("@restart/ui/DropdownContext")},60560:e=>{e.exports=require("dom-helpers/contains")},63033:e=>{e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},65447:e=>{e.exports=require("@restart/ui/Button")},67364:e=>{e.exports=require("@restart/hooks/useCallbackRef")},68455:e=>{e.exports=import("redux-saga")},69722:e=>{e.exports=require("es6-promise")},74075:e=>{e.exports=require("zlib")},74716:e=>{e.exports=require("moment")},74987:e=>{e.exports=require("@restart/ui/ModalManager")},78097:e=>{e.exports=require("next-redux-saga")},78634:e=>{e.exports=require("@restart/ui/Anchor")},80237:(e,r)=>{Object.defineProperty(r,"A",{enumerable:!0,get:function(){return t}});var t=function(e){return e.PAGES="PAGES",e.PAGES_API="PAGES_API",e.APP_PAGE="APP_PAGE",e.APP_ROUTE="APP_ROUTE",e.IMAGE="IMAGE",e}({})},81366:e=>{e.exports=require("redux-auth-wrapper/connectedAuthWrapper")},81413:(e,r)=>{Object.defineProperty(r,"M",{enumerable:!0,get:function(){return function e(r,t){return t in r?r[t]:"then"in r&&"function"==typeof r.then?r.then(r=>e(r,t)):"function"==typeof r&&"default"===t?r:void 0}}})},81521:e=>{e.exports=require("dom-helpers/querySelectorAll")},81895:e=>{e.exports=require("@restart/hooks/useEventCallback")},82015:e=>{e.exports=require("react")},82053:e=>{e.exports=require("@fortawesome/react-fontawesome")},82427:(e,r,t)=>{t.d(r,{E:()=>s,M:()=>a});let a=["A","B","C","D","E","F","G","H","I","J","K","L","M","N","O","P","Q","R","S","T","U","V","W","X","Y","Z","Alle"],s=["A","B","C","D","E","F","G","H","I","J","K","L","M","N","O","P","Q","R","S","T","U","V","W","X","Y","Z","All"]},86842:e=>{e.exports=require("@restart/ui/SelectableContext")},87571:e=>{e.exports=require("dom-helpers/transitionEnd")},88751:e=>{e.exports=require("next-i18next")},93787:e=>{e.exports=require("redux-persist")},94947:e=>{e.exports=require("@restart/hooks/useTimeout")},96196:e=>{e.exports=require("next-redux-wrapper")},98320:e=>{e.exports=require("@restart/hooks/useIsomorphicEffect")},99460:e=>{e.exports=require("dom-helpers/css")}};var r=require("../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),a=r.X(0,[6089,9216,9616,2386],()=>t(22810));module.exports=a})();