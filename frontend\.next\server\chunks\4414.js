"use strict";exports.id=4414,exports.ids=[4414],exports.modules={6417:(e,t,r)=>{r.d(t,{A:()=>s});let a=r(82015).createContext(null);a.displayName="CardHeaderContext";let s=a},15653:(e,t,r)=>{r.d(t,{ks:()=>l,s3:()=>n});var a=r(8732);r(82015);var s=r(59549),i=r(43294);let l=({name:e,id:t,required:r,validator:l,errorMessage:n,onChange:o,value:d,as:c,multiline:m,rows:u,pattern:p,...h})=>(0,a.jsx)(i.Field,{name:e,validate:e=>{let t="string"==typeof e?e:String(e||"");return r&&(!e||""===t.trim())?n?.validator||"This field is required":l&&!l(e)?n?.validator||"Invalid value":p&&e&&!new RegExp(p).test(e)?n?.pattern||"Invalid format":void 0},children:({field:e,meta:r})=>(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(s.A.Control,{...e,...h,id:t,as:c||"input",rows:u,isInvalid:r.touched&&!!r.error,onChange:t=>{e.onChange(t),o&&o(t)},value:void 0!==d?d:e.value}),r.touched&&r.error?(0,a.jsx)(s.A.Control.Feedback,{type:"invalid",children:r.error}):null]})}),n=({name:e,id:t,required:r,errorMessage:l,onChange:n,value:o,children:d,...c})=>(0,a.jsx)(i.Field,{name:e,validate:e=>{if(r&&(!e||""===e))return l?.validator||"This field is required"},children:({field:e,meta:r})=>(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(s.A.Control,{as:"select",...e,...c,id:t,isInvalid:r.touched&&!!r.error,onChange:t=>{e.onChange(t),n&&n(t)},value:void 0!==o?o:e.value,children:d}),r.touched&&r.error?(0,a.jsx)(s.A.Control.Feedback,{type:"invalid",children:r.error}):null]})})},18597:(e,t,r)=>{r.d(t,{A:()=>b});var a=r(3892),s=r.n(a),i=r(82015),l=r(80739),n=r(8732);let o=i.forwardRef(({className:e,bsPrefix:t,as:r="div",...a},i)=>(t=(0,l.oU)(t,"card-body"),(0,n.jsx)(r,{ref:i,className:s()(e,t),...a})));o.displayName="CardBody";let d=i.forwardRef(({className:e,bsPrefix:t,as:r="div",...a},i)=>(t=(0,l.oU)(t,"card-footer"),(0,n.jsx)(r,{ref:i,className:s()(e,t),...a})));d.displayName="CardFooter";var c=r(6417);let m=i.forwardRef(({bsPrefix:e,className:t,as:r="div",...a},o)=>{let d=(0,l.oU)(e,"card-header"),m=(0,i.useMemo)(()=>({cardHeaderBsPrefix:d}),[d]);return(0,n.jsx)(c.A.Provider,{value:m,children:(0,n.jsx)(r,{ref:o,...a,className:s()(t,d)})})});m.displayName="CardHeader";let u=i.forwardRef(({bsPrefix:e,className:t,variant:r,as:a="img",...i},o)=>{let d=(0,l.oU)(e,"card-img");return(0,n.jsx)(a,{ref:o,className:s()(r?`${d}-${r}`:d,t),...i})});u.displayName="CardImg";let p=i.forwardRef(({className:e,bsPrefix:t,as:r="div",...a},i)=>(t=(0,l.oU)(t,"card-img-overlay"),(0,n.jsx)(r,{ref:i,className:s()(e,t),...a})));p.displayName="CardImgOverlay";let h=i.forwardRef(({className:e,bsPrefix:t,as:r="a",...a},i)=>(t=(0,l.oU)(t,"card-link"),(0,n.jsx)(r,{ref:i,className:s()(e,t),...a})));h.displayName="CardLink";var x=r(7783);let f=(0,x.A)("h6"),g=i.forwardRef(({className:e,bsPrefix:t,as:r=f,...a},i)=>(t=(0,l.oU)(t,"card-subtitle"),(0,n.jsx)(r,{ref:i,className:s()(e,t),...a})));g.displayName="CardSubtitle";let y=i.forwardRef(({className:e,bsPrefix:t,as:r="p",...a},i)=>(t=(0,l.oU)(t,"card-text"),(0,n.jsx)(r,{ref:i,className:s()(e,t),...a})));y.displayName="CardText";let j=(0,x.A)("h5"),v=i.forwardRef(({className:e,bsPrefix:t,as:r=j,...a},i)=>(t=(0,l.oU)(t,"card-title"),(0,n.jsx)(r,{ref:i,className:s()(e,t),...a})));v.displayName="CardTitle";let A=i.forwardRef(({bsPrefix:e,className:t,bg:r,text:a,border:i,body:d=!1,children:c,as:m="div",...u},p)=>{let h=(0,l.oU)(e,"card");return(0,n.jsx)(m,{ref:p,...u,className:s()(t,h,r&&`bg-${r}`,a&&`text-${a}`,i&&`border-${i}`),children:d?(0,n.jsx)(o,{children:c}):c})});A.displayName="Card";let b=Object.assign(A,{Img:u,Title:v,Subtitle:g,Body:o,Link:h,Text:y,Header:m,Footer:d,ImgOverlay:p})},23579:(e,t,r)=>{r.d(t,{sx:()=>c,s3:()=>s.s3,ks:()=>s.ks,yk:()=>a.A});var a=r(66994),s=r(15653),i=r(8732),l=r(82015),n=r.n(l),o=r(43294),d=r(59549);let c={RadioGroup:({name:e,valueSelected:t,onChange:r,errorMessage:a,children:s})=>{let{errors:l,touched:d}=(0,o.useFormikContext)(),c=d[e]&&l[e];n().useMemo(()=>({name:e}),[e]);let m=n().Children.map(s,t=>n().isValidElement(t)&&function(e){return"object"==typeof e&&null!==e}(t.props)?n().cloneElement(t,{name:e,...t.props}):t);return(0,i.jsxs)("div",{children:[(0,i.jsx)("div",{className:"radio-group",children:m}),c&&(0,i.jsx)("div",{className:"invalid-feedback d-block",children:a||("string"==typeof l[e]?l[e]:String(l[e]))})]})},RadioItem:({id:e,label:t,value:r,name:a,disabled:s})=>{let{values:l,setFieldValue:n}=(0,o.useFormikContext)(),c=a||e;return(0,i.jsx)(d.A.Check,{type:"radio",id:e,label:t,value:r,name:c,checked:l[c]===r,onChange:e=>{n(c,e.target.value)},disabled:s,inline:!0})}};a.A,s.ks,s.s3},32033:(e,t,r)=>{r.a(e,async(e,a)=>{try{r.r(t),r.d(t,{default:()=>k});var s=r(8732),i=r(82015),l=r(44233),n=r.n(l),o=r(7082),d=r(18597),c=r(83551),m=r(49481),u=r(59549),p=r(91353),h=r(19918),x=r.n(h),f=r(23579),g=r(66994),y=r(11e3),j=r(74716),v=r.n(j),A=r(42893),b=r(98178),_=r(58070),C=r(63487),w=r(88751),S=r(24047),N=e([A,b,C]);[A,b,C]=N.then?(await N)():N;let k=e=>{let t={title:"",operation:null,date:null,syndrome:"",description:"",hazard_type:[],country_regions:[],hazard:[],status:[],rki_monitored:!1,country:[],world_region:null,more_info:"",laboratory_confirmed:!1,officially_validated:!1,images:[],images_src:[]},r=(0,i.useRef)(null),a=(0,i.useRef)(null),{t:l,i18n:h}=(0,w.useTranslation)("common"),j="de"===h.language?{title_de:"asc"}:{title:"asc"},N="fr"===h.language?"en":h.language,k=h.language,E=N?`title.${N}`:"title.en",[I,z]=(0,i.useState)([]),[R,D]=(0,i.useState)([]),[F,T]=(0,i.useState)(t),[L,G]=(0,i.useState)([]),[M,P]=(0,i.useState)([]),[q,U]=(0,i.useState)([]),[O,$]=(0,i.useState)([]),[H,B]=(0,i.useState)([]),[W,V]=(0,i.useState)([]),[K,J]=(0,i.useState)([]),[Q,X]=(0,i.useState)([]),[Y,Z]=(0,i.useState)([]),[ee]=(0,i.useState)(E),[et,er]=(0,i.useState)([]),ea={query:{},sort:j,limit:"~",languageCode:k},es=async e=>{let t=await C.A.get("/country",e);t&&Array.isArray(t.data)&&U(t.data)},ei=async e=>{let t=await C.A.get("/hazardtype",e);t&&Array.isArray(t.data)&&$(t.data)},el=async e=>{let t=await C.A.get("/syndrome",e);t&&Array.isArray(t.data)&&B(t.data)},en=async e=>{let t=await C.A.get("/operation",e);t&&Array.isArray(t.data)&&V(t.data)},eo=async e=>{let t=await C.A.get("/eventstatus",e);t&&Array.isArray(t.data)&&J(t.data)},ed=async e=>{let t=await C.A.get("/risklevel",e);t&&Array.isArray(t.data)&&(X(t.data),Z(t.data),er(t.data))},ec=(e,t)=>{ev(t.country),eb(t.hazard_type),z(t.images?t.images:[]),D(t.images_src?t.images_src:[]),T(e=>({...e,...t})),e&&ex(t=>({...t,riskcountry:e.country?e.country._id:"",region:e.region?e.region._id:"",international:e.international?e.international._id:"",description:e.description?e.description:""}))},em=(e,t,r,a)=>{a.country_regions=e&&e.length>0?e.map((e,t)=>({label:e.title,value:e._id})):[],a.date=r?v()(r).toDate():null,a.hazard=t&&t.length>0?t.map((e,t)=>({label:e.title[N],value:e._id})):[]},eu=(e,t,r,a,s,i)=>{e.syndrome=t&&t._id?t._id:"",e.status=i&&i._id?i._id:"",e.country=r&&r._id?r._id:"",e.hazard_type=a&&a._id?a._id:"",e.operation=s&&s._id?s._id:""};(0,i.useEffect)(()=>{e.routes&&"edit"===e.routes[0]&&e.routes[1]&&(async()=>{let t=await C.A.get(`/event/${e.routes[1]}`,ea);if(t){let{status:e,syndrome:r,country:a,hazard_type:s,risk_assessment:i,country_regions:l,hazard:n,operations:o,date:d}=t;eu(t,r,a,s,o,e),em(l,n,d,t),ec(i,t)}})(),es(ea),ei(ea),el(ea),en(ea),eo(ea),ed(ea)},[]);let ep={riskcountry:null,region:null,international:null,description:null,country:null},[eh,ex]=(0,i.useState)(ep),ef=e=>{let{name:t,value:r}=e.target;ex(e=>({...e,[t]:r}))},eg=e=>{ex(t=>({...t,description:e}))},ey=async t=>{if(null==F.date)return void a.current.focus();if(r.current&&r.current.setAttribute("disabled","disabled"),t&&t.preventDefault&&t.preventDefault(),!F.title||!F.country){r.current&&r.current.removeAttribute("disabled");return}try{let t,a;F.country_regions=F.country_regions?F.country_regions.map((e,t)=>e.value):[],F.hazard=F.hazard?F.hazard.map((e,t)=>e.value):[],F.operation=F.operation||null,F.syndrome=F.syndrome||null,eh.country=eh.riskcountry,e.routes&&"edit"===e.routes[0]&&e.routes[1]?(a="toast.Eventupdatedsuccessfully",t=await C.A.patch(`/event/${e.routes[1]}`,{...F,risk_assessment:eh})):(a="toast.Eventaddedsuccessfully",t=await C.A.post("/event",{...F,risk_assessment:eh})),t&&t._id?(A.default.success(l(a)),n().push("/event/[...routes]",`/event/show/${t._id}`)):(r.current&&r.current.removeAttribute("disabled"),"date should not be empty"===t&&(t=l("toast.dateShouldNotBeEmpty")),A.default.error(t||l("toast.errorOccurred")))}catch(e){r.current&&r.current.removeAttribute("disabled"),console.error("Error submitting event:",e),A.default.error(l("toast.errorOccurred"))}},ej=e=>{T(t=>({...t,...e}))},ev=async e=>{let t=[];if(e){let r=await C.A.get(`/country_region/${e}`,ea);r&&r.data&&(t=r.data.map((e,t)=>({label:e.title,value:e._id}))).sort((e,t)=>e.label.localeCompare(t.label))}G(t)},eA={query:{enabled:!0},sort:{[ee]:"asc"},limit:"~",select:"-description -first_letter -hazard_type -picture -picture_source  -created_at -updated_at"},eb=async e=>{let t=[];if(e){let r=await C.A.get(`/hazard_hazard_type/${e}`,eA);r&&r.data&&(t=r.data.map(e=>({label:e.title[N],value:e._id})))}P(t)},e_=(e,t)=>{T(r=>({...r,[t]:e}))},eC=e=>{let{name:t,value:r}=e.target;if(T(e=>({...e,[t]:r})),"country"===t){let t=e.target.selectedIndex;if(e.target&&t&&null!=t){let r=e.target[t].getAttribute("data-worldregion");T(e=>({...e,world_region:r}))}}"country"===t&&(ev(r),ej({country_regions:[]})),"hazard_type"===t&&(eb(r),ej({hazard:[]}))},ew=(0,i.useRef)(null),eS=e=>{T(t=>({...t,description:e}))},eN=e=>{T(t=>({...t,more_info:e}))},ek=e=>{let t=e.map(e=>e.serverID);T(e=>({...e,images:t}))},eE=e=>{T(t=>({...t,images_src:e}))};return(0,s.jsx)(o.A,{fluid:!0,children:(0,s.jsx)(d.A,{style:{marginTop:"5px",boxShadow:"0 4px 8px 0 rgba(0, 0, 0, 0.2), 0 6px 20px 0 rgba(0, 0, 0, 0.19)"},children:(0,s.jsx)(g.A,{onSubmit:ey,ref:ew,initialValues:F,enableReinitialize:!0,children:(0,s.jsxs)(d.A.Body,{children:[(0,s.jsx)(c.A,{children:(0,s.jsxs)(m.A,{children:[(0,s.jsx)(d.A.Title,{children:"edit"===e.routes[0]?l("editEvent"):l("addEvent")}),(0,s.jsx)("hr",{})]})}),(0,s.jsxs)(c.A,{className:"mb-3",children:[(0,s.jsx)(m.A,{children:(0,s.jsxs)(u.A.Group,{children:[(0,s.jsx)(u.A.Label,{className:"required-field",children:l("Events.forms.EventID")}),(0,s.jsx)(f.ks,{name:"title",id:"eventId",value:F.title,validator:e=>""!==e.trim(),errorMessage:{validator:l("PleaseAddtheEventTitle")},onChange:eC,required:!0})]})}),(0,s.jsx)(m.A,{children:(0,s.jsxs)(u.A.Group,{children:[(0,s.jsxs)("div",{className:"d-flex",children:[(0,s.jsx)(u.A.Label,{className:"d-flex me-3",children:l("Events.forms.OperationName")}),(0,s.jsx)(u.A.Check,{className:"ms-4",type:"switch",name:"rki_monitored",id:"custom-switch",onChange:e=>{T(e=>({...e,rki_monitored:!e.rki_monitored}))},label:l("Events.forms.MonitoredbyRKI"),checked:F.rki_monitored})]}),(0,s.jsxs)(f.s3,{name:"operation",id:"operation",value:null===F.operation?"":F.operation,onChange:eC,children:[(0,s.jsx)("option",{value:"",children:l("Events.forms.SelectOperationName")}),W.map((e,t)=>(0,s.jsx)("option",{value:e._id,children:e.title},t))]})]})})]}),(0,s.jsxs)(c.A,{className:"mb-3",children:[(0,s.jsx)(m.A,{children:(0,s.jsxs)(u.A.Group,{children:[(0,s.jsx)(u.A.Label,{className:"required-field",children:l("Events.forms.CountryorTerritory")}),(0,s.jsxs)(f.s3,{name:"country",id:"country",value:Array.isArray(F.country)&&0===F.country.length?"":F.country,onChange:eC,required:!0,errorMessage:l("thisfieldisrequired"),children:[(0,s.jsx)("option",{value:"",children:l("Events.forms.SelectCountry")}),q.map((e,t)=>(0,s.jsx)("option",{"data-worldregion":e.world_region._id,value:e._id,children:e.title},t))]})]})}),(0,s.jsx)(m.A,{sm:6,md:6,lg:6,children:(0,s.jsxs)(u.A.Group,{style:{maxWidth:"600px"},children:[(0,s.jsx)(u.A.Label,{children:l("CountryRegions")}),(0,s.jsx)(y.MultiSelect,{overrideStrings:{selectSomeItems:l("SelectRegions"),allItemsAreSelected:l("Events.forms.AllRegionsareSelected")},options:L,value:F.country_regions,onChange:e=>{T(t=>({...t,country_regions:e}))},className:"region",labelledBy:l("SelectRegions")})]})})]}),(0,s.jsxs)(c.A,{className:"mb-3",children:[(0,s.jsx)(m.A,{sm:6,md:6,lg:4,children:(0,s.jsxs)(u.A.Group,{children:[(0,s.jsx)(u.A.Label,{className:"required-field",children:l("Events.forms.Status")}),(0,s.jsxs)(f.s3,{name:"status",id:"status",value:Array.isArray(F.status)&&0===F.status.length?"":F.status,onChange:eC,required:!0,errorMessage:l("thisfieldisrequired"),children:[(0,s.jsx)("option",{value:"",children:l("Events.forms.SelectStatus")}),K.map((e,t)=>(0,s.jsx)("option",{value:e._id,children:e.title},t))]})]})}),(0,s.jsx)(m.A,{sm:6,md:6,lg:4,children:(0,s.jsxs)(u.A.Group,{children:[(0,s.jsx)(u.A.Label,{className:"required-field",children:l("Events.forms.HazardType")}),(0,s.jsxs)(f.s3,{name:"hazard_type",id:"hazardType",value:Array.isArray(F.hazard_type)&&0===F.hazard_type.length?"":F.hazard_type,onChange:eC,required:!0,errorMessage:l("thisfieldisrequired"),children:[(0,s.jsx)("option",{value:"",children:l("Events.forms.SelectHazardType")}),O.map((e,t)=>(0,s.jsx)("option",{value:e._id,children:e.title},t))]})]})}),(0,s.jsx)(m.A,{sm:6,md:6,lg:4,children:(0,s.jsxs)(u.A.Group,{style:{maxWidth:"600px"},children:[(0,s.jsxs)(u.A.Label,{children:[l("Events.forms.Hazard")," "]}),(0,s.jsx)(y.MultiSelect,{overrideStrings:{selectSomeItems:l("Events.forms.SelectHazard"),allItemsAreSelected:l("Events.forms.AllHazardsareSelected")},options:M,value:F.hazard,onChange:e=>{T(t=>({...t,hazard:e}))},className:"region",labelledBy:l("Events.forms.SelectHazard")})]})})]}),(0,s.jsxs)(c.A,{className:"mb-3",children:[(0,s.jsx)(m.A,{md:!0,lg:3,sm:12,children:(0,s.jsxs)(u.A.Group,{children:[(0,s.jsx)(u.A.Label,{children:l("Events.forms.Syndrome")}),(0,s.jsxs)(f.s3,{name:"syndrome",id:"syndrome",value:F.syndrome,onChange:eC,children:[(0,s.jsx)("option",{value:"",children:l("Events.forms.SelectSyndrome")}),H.map((e,t)=>(0,s.jsx)("option",{value:e._id,children:e.title},t))]})]})}),(0,s.jsx)(m.A,{children:(0,s.jsxs)(u.A.Group,{children:[(0,s.jsx)(c.A,{children:(0,s.jsx)(m.A,{children:(0,s.jsx)(u.A.Label,{className:"required-field",children:l("Events.forms.Date")})})}),(0,s.jsx)("label",{className:"date-validation w-100",ref:a,children:(0,s.jsx)(_.A,{selected:F.date,maxDate:new Date,errorMessage:l("PleaseselecttheDate"),onChange:e=>e_(e,"date"),dateFormat:"MMMM d, yyyy",placeholderText:l("Events.forms.SelectDate")})})]})}),(0,s.jsx)(m.A,{sm:6,lg:3,className:"align-self-center",children:(0,s.jsx)(u.A.Group,{children:(0,s.jsx)(u.A.Check,{style:{all:"unset"},type:"checkbox",name:l("Events.forms.LaboratoryConfirmed"),label:l("Events.forms.LaboratoryConfirmed"),checked:F.laboratory_confirmed,onChange:()=>{T(e=>({...e,laboratory_confirmed:!e.laboratory_confirmed}))},inline:!0})})}),(0,s.jsx)(m.A,{sm:6,lg:3,className:"align-self-center",children:(0,s.jsx)(u.A.Group,{controlId:"validated_by_official",children:(0,s.jsx)(u.A.Check,{name:l("Events.forms.ValidatedbyOfficial"),label:l("Events.forms.ValidatedbyOfficial"),checked:F.officially_validated,onChange:()=>{T(e=>({...e,officially_validated:!e.officially_validated}))},inline:!0})})})]}),(0,s.jsx)(c.A,{className:"mb-3",children:(0,s.jsx)(m.A,{children:(0,s.jsxs)(u.A.Group,{children:[(0,s.jsx)(u.A.Label,{children:l("Events.forms.Description")}),(0,s.jsx)(S.x,{initContent:F.description,onChange:e=>eS(e)})]})})}),(0,s.jsx)(c.A,{className:"mb-3",children:(0,s.jsxs)(m.A,{children:[(0,s.jsx)(d.A.Text,{children:(0,s.jsx)("b",{children:l("Events.forms.RiskAssessment")})}),(0,s.jsx)("hr",{})]})}),(0,s.jsxs)(c.A,{className:"mb-3",children:[(0,s.jsx)(m.A,{sm:6,md:6,lg:4,children:(0,s.jsxs)(u.A.Group,{children:[(0,s.jsx)(u.A.Label,{children:l("Events.forms.SelectCountryrisklevel")}),(0,s.jsxs)(f.s3,{name:"riskcountry",id:"riskcountry",value:null===eh.riskcountry?"":eh.riskcountry,onChange:ef,children:[(0,s.jsx)("option",{value:"",children:l("Events.forms.SelectCountryrisklevel")}),Q.map((e,t)=>(0,s.jsx)("option",{value:e._id,children:e.title},t))]})]})}),(0,s.jsx)(m.A,{sm:6,md:6,lg:4,children:(0,s.jsxs)(u.A.Group,{children:[(0,s.jsx)(u.A.Label,{children:l("Events.forms.SelectRegionrisklevel")}),(0,s.jsxs)(f.s3,{name:"region",id:"region",value:null===eh.region?"":eh.region,onChange:ef,children:[(0,s.jsx)("option",{value:"",children:l("Events.forms.SelectRegionrisklevel")}),Y.map((e,t)=>(0,s.jsx)("option",{value:e._id,children:e.title},t))]})]})}),(0,s.jsx)(m.A,{sm:6,md:6,lg:4,children:(0,s.jsxs)(u.A.Group,{children:[(0,s.jsx)(u.A.Label,{children:l("Events.forms.SelectInternationalrisklevel")}),(0,s.jsxs)(f.s3,{name:"international",id:"international",value:null===eh.international?"":eh.international,onChange:ef,children:[(0,s.jsx)("option",{value:"",children:l("Events.forms.SelectInternationalrisklevel")}),et.map((e,t)=>(0,s.jsx)("option",{value:e._id,children:e.title},t))]})]})})]}),(0,s.jsx)(c.A,{className:"mb-3",children:(0,s.jsx)(m.A,{children:(0,s.jsx)(u.A.Group,{children:(0,s.jsx)(S.x,{initContent:eh.description,onChange:e=>eg(e)})})})}),(0,s.jsx)(c.A,{className:"mb-3",children:(0,s.jsxs)(m.A,{children:[(0,s.jsx)(d.A.Text,{children:(0,s.jsx)("b",{children:l("Events.forms.MoreInformation")})}),(0,s.jsx)("hr",{})]})}),(0,s.jsx)(c.A,{className:"mb-3",children:(0,s.jsx)(m.A,{children:(0,s.jsx)(u.A.Group,{children:(0,s.jsx)(S.x,{initContent:F.more_info,onChange:e=>eN(e)})})})}),(0,s.jsx)(c.A,{className:"mb-3",children:(0,s.jsxs)(m.A,{children:[(0,s.jsx)(d.A.Text,{children:(0,s.jsx)("b",{children:l("Events.forms.MediaGallery")})}),(0,s.jsx)("hr",{})]})}),(0,s.jsx)(c.A,{className:"mb-3",children:(0,s.jsx)(m.A,{children:(0,s.jsx)(b.A,{datas:I,srcText:R,getImgID:e=>ek(e),getImageSource:e=>eE(e)})})}),(0,s.jsx)(c.A,{className:"my-4",children:(0,s.jsxs)(m.A,{children:[(0,s.jsx)(p.A,{className:"me-2",type:"submit",variant:"primary",ref:r,children:l("submit")}),(0,s.jsx)(p.A,{className:"me-2",variant:"info",onClick:()=>{T(t),z([]),D([]),X([]),Z([]),er([]),ex(ep),window.scrollTo(0,0)},children:l("reset")}),(0,s.jsx)(x(),{href:"/event",as:"/event",children:(0,s.jsx)(p.A,{variant:"secondary",children:l("Cancel")})})]})})]})})})})};a()}catch(e){a(e)}})},58070:(e,t,r)=>{r.d(t,{A:()=>l});var a=r(8732);r(82015);var s=r(29780),i=r.n(s);let l=e=>(0,a.jsx)(i(),{...e})},66994:(e,t,r)=>{r.d(t,{A:()=>o});var a=r(8732),s=r(82015),i=r(43294),l=r(18622);let n=(0,s.forwardRef)((e,t)=>{let{children:r,onSubmit:s,autoComplete:n,className:o,onKeyPress:d,initialValues:c,...m}=e,u=l.object().shape({});return(0,a.jsx)(i.Formik,{initialValues:c||{},validationSchema:u,onSubmit:(e,t)=>{let r={preventDefault:()=>{},stopPropagation:()=>{},currentTarget:null,target:null,nativeEvent:new Event("submit"),bubbles:!1,cancelable:!0,defaultPrevented:!1,eventPhase:0,isTrusted:!1,timeStamp:Date.now(),type:"submit",isDefaultPrevented:()=>!1,isPropagationStopped:()=>!1,persist:()=>{}};s&&s(r,e,t)},...m,children:e=>(0,a.jsx)(i.Form,{ref:t,onSubmit:e.handleSubmit,autoComplete:n,className:o,onKeyPress:d,children:"function"==typeof r?r(e):r})})});n.displayName="ValidationFormWrapper";let o=n},98178:(e,t,r)=>{r.a(e,async(e,a)=>{try{r.d(t,{A:()=>C});var s=r(8732),i=r(82015),l=r(16029),n=r(82053),o=r(54131),d=r(49481),c=r(59549),m=r(91353),u=r(12403),p=r(27825),h=r.n(p),x=r(42893),f=r(63487),g=r(88751),y=e([o,x,f]);[o,x,f]=y.then?(await y)():y;let j=[],v={flex:1,display:"flex",flexDirection:"column",alignItems:"center",justifyContent:"center",width:"100%",height:"100%",borderWidth:.1,borderColor:"#fafafa",backgroundColor:"#fafafa",color:"black",transition:"border  .24s ease-in-out",padding:"15px"},A={display:"flex",padding:"10px",width:"100%",border:"2px solid gray",flexDirection:"column",justifyContent:"flex-start",flexWrap:"wrap",marginTop:20},b={width:"150px"},_={borderColor:"#2196f3"},C=e=>{let t,{t:r}=(0,g.useTranslation)("common"),[a,p]=(0,i.useState)(!1),[y,C]=(0,i.useState)(),w="application"==e.type?0x1400000:"20971520",[S,N]=(0,i.useState)([]),[k,E]=(0,i.useState)(!0),[I,z]=(0,i.useState)([]),R=e&&"application"===e.type?"/files":"/image",D=async e=>{await f.A.remove(`${R}/${e}`)},F=e=>{C(e),p(!0)},T=(e,t)=>{let r=[...I];r[t]=e.target.value,z(r)},L=t=>{switch(t&&t.name.split(".").pop()){case"JPG":case"jpg":case"jpeg":case"jpg":case"png":return(0,s.jsx)("img",{src:t.preview,style:b});case"pdf":return(0,s.jsx)("img",{src:"/images/fileIcons/pdfFile.png",className:"application"===e.type?"docPreview":"imgPreview"});case"docx":default:return(0,s.jsx)("img",{src:"/images/fileIcons/wordFile.png",className:"application"===e.type?"docPreview":"imgPreview"});case"xls":case"xlsx":return(0,s.jsx)("img",{src:"/images/fileIcons/xlsFile.png",className:"application"===e.type?"docPreview":"imgPreview"})}},G=()=>p(!1),M=()=>{p(!1)},P=t=>{let r=(t=y)&&t._id?{serverID:t._id}:{file:t},a=h().findIndex(j,r),s=[...I];s.splice(a,1),z(s),D(j[a].serverID),j.splice(a,1),e.getImgID(j,e.index?e.index:0);let i=[...S];i.splice(i.indexOf(t),1),N(i),p(!1)},q=S.map((t,i)=>(0,s.jsxs)("div",{children:[(0,s.jsx)(d.A,{xs:12,children:(0,s.jsxs)("div",{className:"row",children:[(0,s.jsx)(d.A,{md:4,lg:3,className:"application text-center align-self-center"===e.type?"docImagePreview text-center align-self-center":"imgPreview text-center align-self-center",children:L(t)}),(0,s.jsx)(d.A,{md:5,lg:7,className:"align-self-center",children:(0,s.jsxs)(c.A,{children:[(0,s.jsxs)(c.A.Group,{controlId:"filename",children:[(0,s.jsx)(c.A.Label,{className:"mt-2",children:r("FileName")}),(0,s.jsx)(c.A.Control,{size:"sm",type:"text",disabled:!0,value:t.original_name?t.original_name:t.name})]}),(0,s.jsxs)(c.A.Group,{controlId:"description",children:[(0,s.jsx)(c.A.Label,{children:"application"===e.type?r("ShortDescription/(Max255Characters)"):r("Source/Description")}),(0,s.jsx)(c.A.Control,{maxLength:"application"===e.type?255:void 0,size:"sm",type:"text",placeholder:"application"===e.type?r("`Enteryourdocumentdescription`"):r("`Enteryourimagesource/description`"),value:I[i],onChange:e=>T(e,i)})]})]})}),(0,s.jsx)(d.A,{md:3,lg:2,className:"align-self-center text-center",onClick:()=>F(t),children:(0,s.jsx)(m.A,{variant:"dark",children:r("Remove")})})]})}),(0,s.jsxs)(u.A,{show:a,onHide:G,children:[(0,s.jsx)(u.A.Header,{closeButton:!0,children:(0,s.jsx)(u.A.Title,{children:r("DeleteFile")})}),(0,s.jsx)(u.A.Body,{children:r("Areyousurewanttodeletethisfile?")}),(0,s.jsxs)(u.A.Footer,{children:[(0,s.jsx)(m.A,{variant:"secondary",onClick:M,children:r("Cancel")}),(0,s.jsx)(m.A,{variant:"primary",onClick:()=>P(t),children:r("yes")})]})]})]},i));(0,i.useEffect)(()=>{S.forEach(e=>URL.revokeObjectURL(e.preview)),j=[]},[]),(0,i.useEffect)(()=>{e.getImageSource(I)},[I]),(0,i.useEffect)(()=>{z(e.srcText)},[e.srcText]),(0,i.useEffect)(()=>{if(e&&"true"===e.singleUpload&&E(!1),e&&e.datas){let t=e.datas.map((t,r)=>(j.push({serverID:t._id,index:e.index?e.index:0,type:t.name.split(".")[1]}),{...t,preview:`http://localhost:3001/api/v1/image/show/${t._id}`}));N([...t])}},[e.datas]);let U=async(t,r)=>{if(t.length>r)try{let a=new FormData;a.append("file",t[r]);let s=await f.A.post(R,a,{"Content-Type":"multipart/form-data"});j.push({serverID:s._id,file:t[r],index:e.index?e.index:0,type:t[r].name.split(".")[1]}),U(t,r+1)}catch(e){U(t,r+1)}else e.getImgID(j,e.index?e.index:0)},O=(0,i.useCallback)(async e=>{await U(e,0);let t=e.map(e=>Object.assign(e,{preview:URL.createObjectURL(e)}));k?N(e=>[...e,...t]):N([...t])},[]),{getRootProps:$,getInputProps:H,isDragActive:B,isDragAccept:W,isDragReject:V,fileRejections:K}=(0,l.useDropzone)({accept:e&&e.type?"application/pdf, application/vnd.openxmlformats-officedocument.wordprocessingml.document, application/vnd.oasis.opendocument.text,application/vnd.openxmlformats-officedocument.spreadsheetml.sheet,text/csv":"image/*",multiple:k,minSize:0,maxSize:w,onDrop:O,validator:function(e){if("/image"===R){if("image"!==e.type.substring(0,5))return x.default.error(r("toast.filetypenotsupport")),{code:"file-invalid-type",message:"File type not supported"}}else if("/files"===R&&"image"===e.type.substring(0,5))return x.default.error(r("toast.filetypenotsupport")),{code:"file-invalid-type",message:"File type not supported"};return null}}),J=(0,i.useMemo)(()=>({...v,...B?_:{outline:"2px dashed #bbb"},...W?{outline:"2px dashed #595959"}:{outline:"2px dashed #bbb"},...V?{outline:"2px dashed red"}:{activeStyle:_}}),[B,V]);t=e&&"application"===e.type?(0,s.jsx)("small",{style:{color:"#595959"},children:r("DocumentWeSupport")}):(0,s.jsx)("small",{style:{color:"#595959"},children:r("ImageWeSupport")});let Q=K.length>0&&K[0].file.size>w;return(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)("div",{className:" d-flex justify-content-center align-items-center mt-3",style:{width:"100%",height:"180px"},children:(0,s.jsxs)("div",{...$({style:J}),children:[(0,s.jsx)("input",{...H()}),(0,s.jsx)(n.FontAwesomeIcon,{icon:o.faCloudUploadAlt,size:"4x",color:"#999"}),(0,s.jsx)("p",{style:{color:"#595959",marginBottom:"0px"},children:r("Drag'n'dropsomefileshere,orclicktoselectfiles")}),!k&&(0,s.jsxs)("small",{style:{color:"#595959"},children:[(0,s.jsx)("b",{children:"Note:"})," One single image will be accepted"]}),t,(e.type,Q&&(0,s.jsxs)("small",{className:"text-danger mt-2",children:[(0,s.jsx)(n.FontAwesomeIcon,{icon:o.faExclamationCircle,size:"1x",color:"red"})," ",r("FileistoolargeItshouldbelessthan20MB")]})),V&&(0,s.jsxs)("small",{className:"text-danger",style:{color:"#595959"},children:[(0,s.jsx)(n.FontAwesomeIcon,{icon:o.faExclamationCircle,size:"1x",color:"red"})," ",r("Filetypenotacceptedsorr")]})]})}),S.length>0&&(0,s.jsx)("div",{style:A,children:q})]})};a()}catch(e){a(e)}})}};