{"version": 3, "file": "static/chunks/pages/hazard/HazardSearch-a925b94af77be387.js", "mappings": "sNAuCA,MAxBqB,OAAC,YAAEA,CAAU,QAwBnBC,EAxBqBC,CAAQ,CAAqB,GACzD,GAAEC,CAAC,CAAE,CAAGC,CAAAA,EAAAA,EAAAA,EAAAA,CAAcA,CAAC,UAE7B,MACE,UAACC,EAAAA,CAASA,CAAAA,CAACC,KAAK,IAACC,UAAU,eACzB,UAACC,EAAAA,CAAGA,CAAAA,UACF,WAACC,EAAAA,CAAUA,CAAAA,WACT,UAACC,EAAAA,CAAIA,CAACC,OAAO,EACXJ,UAAU,UACVK,KAAK,OACLC,YAAcV,EAAE,iBAChBW,MAAOd,EACPe,SAAUb,IAEZ,UAACc,MAAAA,CAAIT,UAAU,uBACb,UAACU,EAAAA,CAAeA,CAAAA,CAACC,KAAMC,EAAAA,GAAQA,WAO3C,gIC/BA,IAAMC,EAA8BC,EAAAA,UAAgB,CAAC,EAA9B,CAKpBC,QALmD,EAApB,SAChCf,CAAS,UACTgB,CAAQ,CACRC,GAAIC,EAAY,MAAM,CACtB,GAAGC,EACJ,GAEC,OADAH,EAAWI,CAAAA,EAAAA,EAAAA,EAAAA,CAAkBA,CAACJ,EAAU,oBACpBK,CAAAA,EAAAA,EAAAA,GAAAA,CAAIA,CAACH,EAAW,CAClCH,IAAKA,EACLf,UAAWsB,IAAWtB,EAAWgB,GACjC,GAAGG,CAAK,EAEZ,GACAN,EAAeU,WAAW,CAAG,iBCG7B,IAAMrB,EAA0BY,EAAAA,SAAb,CAA6B,CAAC,GAQ9CC,MAR2B,EAAoB,UAChDC,CAAQ,MACRQ,CAAI,eACJC,CAAa,WACbzB,CAAS,CAETiB,CADA,EACIC,EAAY,KAAK,CACrB,GAAGC,EACJ,GACCH,EAAWI,CAAAA,EAAAA,EAAAA,EAAAA,CAAkBA,CAACJ,EAAU,eAIxC,IAAMU,EAAeC,CAAAA,EAAAA,EAAAA,OAAAA,CAAOA,CAAC,IAAO,GAAC,EAAI,EAAE,EAC3C,MAAoBN,CAAb,EAAaA,EAAAA,GAAAA,CAAIA,CAACO,EAAAA,CAAiBA,CAACC,QAAQ,CAAE,CACnDtB,MAAOmB,EACPI,SAAuBT,CAAb,EAAaA,EAAAA,GAAAA,CAAIA,CAACH,EAAW,CACrCH,IAAKA,EACL,GAAGI,CAAK,CACRnB,KAduJ,KAc5IsB,IAAWtB,EAAWgB,EAAUQ,GAAQ,GAAeA,MAAAA,CAAZR,EAAS,KAAQ,OAALQ,GAAQC,GAAiB,iBAC7F,EACF,EACF,GACAvB,EAAWqB,WAAW,CAAG,aACzB,MAAeQ,OAAOC,MAAM,CAAC9B,EAAY,CACvC+B,MAAMpB,CACNqB,MAhCsBf,CAgCfgB,EAhCqCd,CAAAA,EAAAA,EAAb,GAAaA,CAAIA,CAACR,EAAgB,CACjEiB,GAD0C,MACnBT,CAAb,EAAaA,EAAAA,CADwCR,EACxCQ,CAAIA,CAACe,EAAAA,CAAcA,CAAE,CAC1C/B,KAAM,QACN,GAAGc,CAAK,EAEZ,GA4BEkB,SAvCyBlB,CAuCfmB,EAvCqCjB,CAAAA,EAAAA,EAAb,GAAaA,CAAIA,CAACR,EAAgB,CACpEiB,GAD6C,MACtBT,CAAb,CDSiBR,CCTJQ,CDSK,CCTLA,CAD2CR,EAC3CQ,CAAIA,CAACe,EAAP,CAAqBA,CAAE,CAC1C/B,KAAM,WACN,GAAGc,CAAK,EAEZ,EAmCA,EAAE,EAAC,gBCjDH,4CACA,uBACA,WACA,OAAe,EAAQ,KAA4C,CACnE,EACA,SAFsB", "sources": ["webpack://_N_E/./pages/hazard/HazardSearch.tsx", "webpack://_N_E/./node_modules/react-bootstrap/esm/InputGroupText.js", "webpack://_N_E/./node_modules/react-bootstrap/esm/InputGroup.js", "webpack://_N_E/?60a9"], "sourcesContent": ["//Import Library\r\nimport { Container, Row, InputGroup, Form } from \"react-bootstrap\";\r\nimport { FontAwesomeIcon } from \"@fortawesome/react-fontawesome\";\r\nimport {\r\n  faSearch\r\n} from \"@fortawesome/free-solid-svg-icons\";\r\n\r\n//Import services/components\r\nimport { useTranslation } from 'next-i18next';\r\n\r\ninterface HazardSearchProps {\r\n  filterText: string;\r\n  onFilter: (e: React.ChangeEvent<HTMLInputElement>) => void;\r\n}\r\n\r\nconst HazardSearch = ({ filterText, onFilter }: HazardSearchProps) => {\r\n  const { t } = useTranslation('common');\r\n\r\n  return (\r\n    <Container fluid className=\"p-0\">\r\n      <Row>\r\n        <InputGroup >\r\n          <Form.Control\r\n            className=\"rounded\"\r\n            type=\"text\"\r\n            placeholder= {t(\"SearchHazards\")}\r\n            value={filterText}\r\n            onChange={onFilter}\r\n          />\r\n          <div className=\"search-icon\">\r\n            <FontAwesomeIcon icon={faSearch} />\r\n          </div>\r\n        </InputGroup>\r\n\r\n      </Row>\r\n    </Container>\r\n  );\r\n};\r\n\r\nexport default HazardSearch;\r\n", "\"use client\";\n\nimport * as React from 'react';\nimport classNames from 'classnames';\nimport { useBootstrapPrefix } from './ThemeProvider';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst InputGroupText = /*#__PURE__*/React.forwardRef(({\n  className,\n  bsPrefix,\n  as: Component = 'span',\n  ...props\n}, ref) => {\n  bsPrefix = useBootstrapPrefix(bsPrefix, 'input-group-text');\n  return /*#__PURE__*/_jsx(Component, {\n    ref: ref,\n    className: classNames(className, bsPrefix),\n    ...props\n  });\n});\nInputGroupText.displayName = 'InputGroupText';\nexport default InputGroupText;", "\"use client\";\n\nimport classNames from 'classnames';\nimport * as React from 'react';\nimport { useMemo } from 'react';\nimport { useBootstrapPrefix } from './ThemeProvider';\nimport FormCheckInput from './FormCheckInput';\nimport InputGroupContext from './InputGroupContext';\nimport InputGroupText from './InputGroupText';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst InputGroupCheckbox = props => /*#__PURE__*/_jsx(InputGroupText, {\n  children: /*#__PURE__*/_jsx(FormCheckInput, {\n    type: \"checkbox\",\n    ...props\n  })\n});\nconst InputGroupRadio = props => /*#__PURE__*/_jsx(InputGroupText, {\n  children: /*#__PURE__*/_jsx(FormCheckInput, {\n    type: \"radio\",\n    ...props\n  })\n});\nconst InputGroup = /*#__PURE__*/React.forwardRef(({\n  bsPrefix,\n  size,\n  hasValidation,\n  className,\n  // Need to define the default \"as\" during prop destructuring to be compatible with styled-components github.com/react-bootstrap/react-bootstrap/issues/3595\n  as: Component = 'div',\n  ...props\n}, ref) => {\n  bsPrefix = useBootstrapPrefix(bsPrefix, 'input-group');\n\n  // Intentionally an empty object. Used in detecting if a dropdown\n  // exists under an input group.\n  const contextValue = useMemo(() => ({}), []);\n  return /*#__PURE__*/_jsx(InputGroupContext.Provider, {\n    value: contextValue,\n    children: /*#__PURE__*/_jsx(Component, {\n      ref: ref,\n      ...props,\n      className: classNames(className, bsPrefix, size && `${bsPrefix}-${size}`, hasValidation && 'has-validation')\n    })\n  });\n});\nInputGroup.displayName = 'InputGroup';\nexport default Object.assign(InputGroup, {\n  Text: InputGroupText,\n  Radio: InputGroupRadio,\n  Checkbox: InputGroupCheckbox\n});", "\n    (window.__NEXT_P = window.__NEXT_P || []).push([\n      \"/hazard/HazardSearch\",\n      function () {\n        return require(\"private-next-pages/hazard/HazardSearch.tsx\");\n      }\n    ]);\n    if(module.hot) {\n      module.hot.dispose(function () {\n        window.__NEXT_P.push([\"/hazard/HazardSearch\"])\n      });\n    }\n  "], "names": ["filterText", "HazardSearch", "onFilter", "t", "useTranslation", "Container", "fluid", "className", "Row", "InputGroup", "Form", "Control", "type", "placeholder", "value", "onChange", "div", "FontAwesomeIcon", "icon", "faSearch", "InputGroupText", "React", "ref", "bsPrefix", "as", "Component", "props", "useBootstrapPrefix", "_jsx", "classNames", "displayName", "size", "hasValidation", "contextValue", "useMemo", "InputGroupContext", "Provider", "children", "Object", "assign", "Text", "Radio", "InputGroupRadio", "FormCheckInput", "Checkbox", "InputGroupCheckbox"], "sourceRoot": "", "ignoreList": [1, 2]}