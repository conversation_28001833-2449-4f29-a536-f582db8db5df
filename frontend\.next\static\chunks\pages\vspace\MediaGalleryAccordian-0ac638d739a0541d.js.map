{"version": 3, "file": "static/chunks/pages/vspace/MediaGalleryAccordian-0ac638d739a0541d.js", "mappings": "gFACA,4CACA,gCACA,WACA,OAAe,EAAQ,KAAqD,CAC5E,EACA,SAFsB,kICmKtB,MAnIoB,IAClB,GAAM,GAAEA,CAAC,CAAE,CAAGC,CAAAA,EAAAA,EAAAA,EAAAA,CAAcA,CAAC,MAkIhBC,IAjIP,CAACC,EAAQC,EAAU,CAAGC,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAc,EAAE,EAG9CC,EAAoB,IACxB,IAAMC,EAAc,8EAA8EC,IAAI,CAACC,EAAKC,WAAW,EAEvH,MACE,WAACC,MAAAA,CAAIC,UAAU,4BACb,WAACC,IAAAA,CAAED,UAAU,iBACX,UAACE,IAAAA,UAAGd,EAAE,cAAgB,IAAES,EAAKM,YAAY,EAAI,mBAE9CN,EAAKC,WAAW,EACf,WAACC,MAAAA,CAAIC,UAAU,wBACb,UAACC,IAAAA,UAAE,UAACC,IAAAA,UAAGd,EAAE,cACRO,EACC,WAACI,MAAAA,WACC,UAACK,EAAAA,CAAeA,CAAAA,CAACC,KAAMC,EAAAA,GAAMA,CAAEC,KAAK,KAAKC,MAAM,OAAOR,UAAU,SAChE,UAACS,IAAAA,CAAET,UAAU,cAAcU,KAAMb,EAAKC,WAAW,CAAEa,OAAO,SAASC,IAAI,+BACpEf,EAAKC,WAAW,MAIrB,UAACC,MAAAA,UACC,UAACE,IAAAA,CAAED,UAAU,YAAYa,MAAO,CAAEC,UAAW,WAAY,WACtDjB,EAAKC,WAAW,QAM1BD,EAAKkB,YAAY,EAChB,WAACC,EAAAA,CAAMA,CAAAA,CAAChB,UAAU,qCAAqCU,KAAMb,EAAKkB,YAAY,WAC3E3B,EAAE,YACH,UAACgB,EAAAA,CAAeA,CAAAA,CAACC,KAAMY,EAAAA,GAAUA,CAAEV,KAAK,KAAKP,UAAU,cAKjE,EA6CA,MA3CAkB,CAAAA,EAAAA,EAAAA,SAAAA,CAASA,CAAC,KACR,IAAMC,EAA8B,EAAE,CACtCC,GAASA,EAAMC,OAAO,EAAIC,MAAMC,OAAO,CAACH,EAAMC,OAAO,GAAKD,EAAMC,OAAO,CAACG,GAAG,CAAC,CAAC3B,EAAM4B,KACjF,IACIC,EADEC,EAAW9B,GAAQA,EAAK+B,IAAI,CAACC,KAAK,CAAC,KAAKC,GAAG,GAGjD,OAAQH,GACN,IAAK,MACL,IAAK,MACL,IAAK,OACL,IAAK,MACHD,EAAS,GAAwC7B,MAAAA,CAArCkC,8BAAsB,CAAC,gBAAuB,OAATlC,EAAKmC,GAAG,EACzD,KACF,KAAK,MACHN,EAAS,gCACT,KACF,KAAK,OACHA,EAAS,iCACT,KACF,KAAK,MACL,IAAK,OACHA,EAAS,gCACT,KACF,SACEA,EAAS,iCACb,CAEA,IAAMO,EAAQ,CAAc,YAAuB,QAAbN,GAAmC,QAAbA,GAAmC,SAAbA,CAAa,CAAK,EAC/F,GAA4C9B,MAAAA,CAAzCkC,8BAAsB,CAAC,oBAA2B,OAATlC,EAAKmC,GAAG,EACnDE,EAAQ,GAAqE,OAAlErC,GAAQA,EAAKsC,aAAa,CAAGtC,EAAKsC,aAAa,CAAG,iBAC7DC,EAAehB,EAAMiB,WAAW,EAAIf,MAAMC,OAAO,CAACH,EAAMiB,WAAW,GACpEjB,EAAMiB,WAAW,CAACC,MAAM,CAAG,EAAIlB,EAAMiB,WAAW,CAACZ,EAAE,CAAG,GAE3DN,EAAeoB,IAAI,CAAC,CAClBC,IAAKd,EACL5B,YAAasC,EACbjC,aAAc+B,EACdnB,aAAckB,CAChB,EACF,GACAzC,EAAU2B,EACZ,EAAG,CAACC,EAAM,EAGR,UAACrB,MAAAA,UACER,GAA4B,IAAlBA,EAAO+C,MAAM,CACtB,UAACvC,MAAAA,CAAIC,UAAU,wCACb,UAACC,IAAAA,CAAED,UAAU,wDAAgDZ,EAAE,qBAGjE,UAACqD,EAAAA,EAAQA,CAAAA,CACPC,YAAY,EACZC,YAAY,EACZC,gBAAgB,EAChBC,cAAc,EACdC,mBAAmB,EACnBC,UAAU,EACVC,YAAa,GACbC,WAAW,EACXC,eAAe,EACfC,cAAc,EACdC,aAAc,IACZ7D,EAAOiC,GAAG,CAAC,CAAC3B,EAAMwD,IAChB,UAACC,MAAAA,CAECd,IAAK3C,EAAK2C,GAAG,CACbe,IAAK,aAAuB,OAAVF,EAAQ,GAC1BxC,MAAO,CAAE2C,MAAO,OAAQC,OAAQ,OAAQC,UAAW,OAAQ,GAHtDL,aAQV9D,EAAOiC,GAAG,CAAC,CAAC3B,EAAMwD,IACjB,WAACtD,MAAAA,WACC,UAACuD,MAAAA,CACCd,IAAK3C,EAAK2C,GAAG,CACbe,IAAK1D,EAAKM,YAAY,EAAI,gBAC1BU,MAAO,CAAE8C,UAAW,QAASD,UAAW,SAAU,IAEnDhE,EAAkBG,KANXwD,OActB,+IC7HA,MAvB8B,IAC1B,GAAM,GAAEjE,CAAC,CAAE,CAAGC,CAAAA,EAAAA,EAAAA,EAAAA,CAAcA,CAAC,MAsBlBuE,IArBL,CAACC,EAASC,EAAW,CAAGrE,CAAAA,EAAAA,EAAAA,MAqBEmE,EAAC,CArBKnE,EAAC,GAEvC,MACI,+BACI,WAACsE,EAAAA,CAASA,CAACC,IAAI,EAACC,SAAS,cACvB,WAACF,EAAAA,CAASA,CAACG,MAAM,EAACC,QAAS,IAAML,EAAW,CAACD,aAC3C,UAAC9D,MAAAA,CAAIC,UAAU,qBAAaZ,EAAE,yBAC9B,UAACW,MAAAA,CAAIC,UAAU,qBACZ6D,EAAU,UAACzD,EAAAA,CAAeA,CAAAA,CAACC,KAAM+D,EAAAA,GAAOA,CAAE5D,MAAM,SAC/C,UAACJ,EAAAA,CAAeA,CAAAA,CAACC,KAAMgE,EAAAA,GAAMA,CAAE7D,MAAM,cAG3C,UAACuD,EAAAA,CAASA,CAACO,IAAI,WACb,UAAChF,EAAAA,CAAWA,CAAAA,CAAC+B,QAASD,EAAMmD,cAAc,CAACjC,MAAM,CAAG,GAAKlB,EAAMmD,cAAc,CAAC/C,GAAG,CAAC3B,GAAQA,EAAKN,MAAM,EAAEiF,IAAI,CAAC,GAC1GnC,YAAajB,EAAMmD,cAAc,CAACjC,MAAM,CAAG,GAAKlB,EAAMmD,cAAc,CAAC/C,GAAG,CAAC3B,GAAQA,EAAK4E,UAAU,EAAED,IAAI,CAAC,WAKzH", "sources": ["webpack://_N_E/?d157", "webpack://_N_E/./components/common/ReactImages.tsx", "webpack://_N_E/./pages/vspace/MediaGalleryAccordian.tsx"], "sourcesContent": ["\n    (window.__NEXT_P = window.__NEXT_P || []).push([\n      \"/vspace/MediaGalleryAccordian\",\n      function () {\n        return require(\"private-next-pages/vspace/MediaGalleryAccordian.tsx\");\n      }\n    ]);\n    if(module.hot) {\n      module.hot.dispose(function () {\n        window.__NEXT_P.push([\"/vspace/MediaGalleryAccordian\"])\n      });\n    }\n  ", "\r\n//Import Library\r\nimport React, { useState, useEffect } from 'react';\r\nimport { Carousel } from 'react-responsive-carousel';\r\nimport { FontAwesomeIcon } from \"@fortawesome/react-fontawesome\";\r\nimport {\r\n  faLink, faDownload\r\n} from \"@fortawesome/free-solid-svg-icons\";\r\nimport { Button } from \"react-bootstrap\";\r\n\r\n//Import services/components\r\nimport { useTranslation } from 'next-i18next';\r\n\r\n// Import CSS for react-responsive-carousel\r\nimport \"react-responsive-carousel/lib/styles/carousel.min.css\";\r\n\r\n// Define types for image items\r\ninterface ImageItem {\r\n  src: string;\r\n  description: string;\r\n  originalName: string;\r\n  downloadLink: string | false;\r\n}\r\n\r\ninterface ReactImagesProps {\r\n  gallery?: Array<{\r\n    _id: string;\r\n    name: string;\r\n    original_name?: string;\r\n    src?: string;\r\n    caption?: string;\r\n    alt?: string;\r\n  }> | boolean;\r\n  imageSource?: string[] | boolean;\r\n}\r\n\r\nconst ReactImages = (props: ReactImagesProps) => {\r\n  const { t } = useTranslation('common');\r\n  const [images, setImages] = useState<ImageItem[]>([]);\r\n\r\n  // Render image description and metadata\r\n  const renderImageLegend = (item: ImageItem) => {\r\n    const isValidLink = /(http|https):\\/\\/(\\w+:{0,1}\\w*)?(\\S+)(:[0-9]+)?(\\/|\\/([\\w#!:.?+=&%!\\-\\/]))?/.test(item.description);\r\n\r\n    return (\r\n      <div className=\"carousel-legend\">\r\n        <p className=\"lead\">\r\n          <b>{t(\"Filename\")}</b> {item.originalName || \"No Name found\"}\r\n        </p>\r\n        {item.description && (\r\n          <div className=\"source_link\">\r\n            <p><b>{t(\"Source\")}</b></p>\r\n            {isValidLink ? (\r\n              <div>\r\n                <FontAwesomeIcon icon={faLink} size=\"1x\" color=\"#999\" className=\"me-1\" />\r\n                <a className=\"source_link\" href={item.description} target=\"_blank\" rel=\"noopener noreferrer\">\r\n                  {item.description}\r\n                </a>\r\n              </div>\r\n            ) : (\r\n              <div>\r\n                <p className=\"ps-0 py-0\" style={{ wordBreak: \"break-all\" }}>\r\n                  {item.description}\r\n                </p>\r\n              </div>\r\n            )}\r\n          </div>\r\n        )}\r\n        {item.downloadLink && (\r\n          <Button className=\"btn btn-success mt-2 btn--download\" href={item.downloadLink}>\r\n            {t(\"Download\")}\r\n            <FontAwesomeIcon icon={faDownload} size=\"1x\" className=\"ms-1\" />\r\n          </Button>\r\n        )}\r\n      </div>\r\n    );\r\n  };\r\n\r\n  useEffect(() => {\r\n    const carouselImages: ImageItem[] = [];\r\n    props && props.gallery && Array.isArray(props.gallery) && props.gallery.map((item, i) => {\r\n      const fileType = item && item.name.split('.').pop();\r\n      let imgSrc;\r\n\r\n      switch (fileType) {\r\n        case \"JPG\":\r\n        case \"jpg\":\r\n        case \"jpeg\":\r\n        case \"png\":\r\n          imgSrc = `${process.env.API_SERVER}/image/show/${item._id}`;\r\n          break;\r\n        case \"pdf\":\r\n          imgSrc = \"/images/fileIcons/pdfFile.png\";\r\n          break;\r\n        case \"docx\":\r\n          imgSrc = \"/images/fileIcons/wordFile.png\";\r\n          break;\r\n        case \"xls\":\r\n        case 'xlsx':\r\n          imgSrc = \"/images/fileIcons/xlsFile.png\";\r\n          break;\r\n        default:\r\n          imgSrc = \"/images/fileIcons/otherFile.png\";\r\n      }\r\n\r\n      const _link = (fileType === \"docx\" || fileType === \"pdf\" || fileType === \"xls\" || fileType === \"xlsx\")\r\n        && `${process.env.API_SERVER}/files/download/${item._id}`;\r\n      const _name = `${item && item.original_name ? item.original_name : \"No Name found\"}`;\r\n      const _description = props.imageSource && Array.isArray(props.imageSource)\r\n        && props.imageSource.length > 0 ? props.imageSource[i] : \"\";\r\n\r\n      carouselImages.push({\r\n        src: imgSrc,\r\n        description: _description,\r\n        originalName: _name,\r\n        downloadLink: _link\r\n      });\r\n    });\r\n    setImages(carouselImages);\r\n  }, [props]);\r\n\r\n  return (\r\n    <div>\r\n      {images && images.length === 0 ? (\r\n        <div className=\"border border-info my-3 mx-0\">\r\n          <p className=\"d-flex d-flex justify-content-center p-2 m-0\">{t(\"NoFilesFound!\")}</p>\r\n        </div>\r\n      ) : (\r\n        <Carousel\r\n          showThumbs={true}\r\n          showStatus={true}\r\n          showIndicators={true}\r\n          infiniteLoop={true}\r\n          useKeyboardArrows={true}\r\n          autoPlay={false}\r\n          stopOnHover={true}\r\n          swipeable={true}\r\n          dynamicHeight={false}\r\n          emulateTouch={true}\r\n          renderThumbs={() =>\r\n            images.map((item, index) => (\r\n              <img\r\n                key={index}\r\n                src={item.src}\r\n                alt={`Thumbnail ${index + 1}`}\r\n                style={{ width: '60px', height: '60px', objectFit: 'cover' }}\r\n              />\r\n            ))\r\n          }\r\n        >\r\n          {images.map((item, index) => (\r\n            <div key={index}>\r\n              <img\r\n                src={item.src}\r\n                alt={item.originalName || \"Gallery image\"}\r\n                style={{ maxHeight: '500px', objectFit: 'contain' }}\r\n              />\r\n              {renderImageLegend(item)}\r\n            </div>\r\n          ))}\r\n        </Carousel>\r\n      )}\r\n    </div>\r\n  );\r\n\r\n}\r\n\r\nexport default ReactImages;\r\n", "//Import Library\r\nimport { faPlus, faMinus } from \"@fortawesome/free-solid-svg-icons\";\r\nimport { FontAwesomeIcon } from \"@fortawesome/react-fontawesome\";\r\nimport { useState } from \"react\";\r\nimport { Card, Accordion } from \"react-bootstrap\";\r\n\r\n//Import services/components\r\nimport ReactImages from \"../../components/common/ReactImages\";\r\nimport { useTranslation } from 'next-i18next';\r\n\r\ninterface MediaGalleryAccordianProps {\r\n  calenderEvents: Array<{\r\n    images: any[];\r\n    images_src: any[];\r\n  }>;\r\n}\r\n\r\nconst MediaGalleryAccordian = (props: MediaGalleryAccordianProps) => {\r\n    const { t } = useTranslation('common');\r\n    const [section, setSection] = useState(false);\r\n\r\n    return (\r\n        <>\r\n            <Accordion.Item eventKey=\"2\">\r\n              <Accordion.Header onClick={() => setSection(!section)}>\r\n                <div className=\"cardTitle\">{t(\"vspace.MediaGallery\")}</div>\r\n                <div className=\"cardArrow\">\r\n                  {section ? <FontAwesomeIcon icon={faMinus} color=\"#fff\" /> :\r\n                    <FontAwesomeIcon icon={faPlus} color=\"#fff\" />}\r\n                </div>\r\n              </Accordion.Header>\r\n              <Accordion.Body>\r\n                <ReactImages gallery={props.calenderEvents.length > 0 && props.calenderEvents.map(item => item.images).flat(1)}\r\n                  imageSource={props.calenderEvents.length > 0 && props.calenderEvents.map(item => item.images_src).flat(1)} />\r\n              </Accordion.Body>\r\n            </Accordion.Item>\r\n        </>\r\n    )\r\n};\r\n\r\nexport default MediaGalleryAccordian;"], "names": ["t", "useTranslation", "ReactImages", "images", "setImages", "useState", "renderImageLegend", "isValidLink", "test", "item", "description", "div", "className", "p", "b", "originalName", "FontAwesomeIcon", "icon", "faLink", "size", "color", "a", "href", "target", "rel", "style", "wordBreak", "downloadLink", "<PERSON><PERSON>", "faDownload", "useEffect", "carouselImages", "props", "gallery", "Array", "isArray", "map", "i", "imgSrc", "fileType", "name", "split", "pop", "process", "_id", "_link", "_name", "original_name", "_description", "imageSource", "length", "push", "src", "Carousel", "showThumbs", "showStatus", "showIndicators", "infiniteLoop", "useKeyboardArrows", "autoPlay", "stopOnHover", "swipeable", "dynamicHeight", "emulate<PERSON><PERSON><PERSON>", "renderThumbs", "index", "img", "alt", "width", "height", "objectFit", "maxHeight", "MediaGalleryAccordian", "section", "setSection", "Accordion", "<PERSON><PERSON>", "eventKey", "Header", "onClick", "faMinus", "faPlus", "Body", "calenderEvents", "flat", "images_src"], "sourceRoot": "", "ignoreList": []}