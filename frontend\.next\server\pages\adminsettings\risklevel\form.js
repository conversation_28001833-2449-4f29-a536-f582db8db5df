"use strict";(()=>{var e={};e.id=4523,e.ids=[636,3220,4523],e.modules={123:e=>{e.exports=require("dom-helpers/removeEventListener")},1332:e=>{e.exports=require("react-custom-scrollbars-2")},1428:e=>{e.exports=import("axios")},1680:e=>{e.exports=require("@restart/ui/utils")},1919:e=>{e.exports=require("react-transition-group/Transition")},3892:e=>{e.exports=require("classnames")},4048:e=>{e.exports=require("react-truncate")},5887:(e,r,t)=>{t.a(e,async(e,s)=>{try{t.r(r),t.d(r,{config:()=>h,default:()=>c,getServerSideProps:()=>v,getStaticPaths:()=>m,getStaticProps:()=>x,reportWebVitals:()=>f,routeModule:()=>k,unstable_getServerProps:()=>b,unstable_getServerSideProps:()=>A,unstable_getStaticParams:()=>j,unstable_getStaticPaths:()=>q,unstable_getStaticProps:()=>g});var a=t(63885),i=t(80237),o=t(81413),l=t(9616),n=t.n(l),d=t(72386),u=t(83623),p=e([d,u]);[d,u]=p.then?(await p)():p;let c=(0,o.M)(u,"default"),x=(0,o.M)(u,"getStaticProps"),m=(0,o.M)(u,"getStaticPaths"),v=(0,o.M)(u,"getServerSideProps"),h=(0,o.M)(u,"config"),f=(0,o.M)(u,"reportWebVitals"),g=(0,o.M)(u,"unstable_getStaticProps"),q=(0,o.M)(u,"unstable_getStaticPaths"),j=(0,o.M)(u,"unstable_getStaticParams"),b=(0,o.M)(u,"unstable_getServerProps"),A=(0,o.M)(u,"unstable_getServerSideProps"),k=new a.PagesRouteModule({definition:{kind:i.A.PAGES,page:"/adminsettings/risklevel/form",pathname:"/adminsettings/risklevel/form",bundlePath:"",filename:""},components:{App:d.default,Document:n()},userland:u});s()}catch(e){s(e)}})},6009:e=>{e.exports=require("dom-helpers/ownerDocument")},6417:(e,r,t)=>{t.d(r,{A:()=>a});let s=t(82015).createContext(null);s.displayName="CardHeaderContext";let a=s},6952:e=>{e.exports=require("@restart/ui/Modal")},7374:e=>{e.exports=require("@restart/ui/Dropdown")},8732:e=>{e.exports=require("react/jsx-runtime")},9653:e=>{e.exports=require("@restart/ui/Overlay")},11242:e=>{e.exports=require("redux-persist/integration/react")},11688:e=>{e.exports=require("@restart/hooks/useWillUnmount")},12388:e=>{e.exports=require("dom-helpers/addEventListener")},13364:e=>{e.exports=require("redux-saga/effects")},14062:e=>{e.exports=import("react-redux")},14078:e=>{e.exports=import("swr")},14332:e=>{e.exports=require("uncontrollable")},15653:(e,r,t)=>{t.d(r,{ks:()=>o,s3:()=>l});var s=t(8732);t(82015);var a=t(59549),i=t(43294);let o=({name:e,id:r,required:t,validator:o,errorMessage:l,onChange:n,value:d,as:u,multiline:p,rows:c,pattern:x,...m})=>(0,s.jsx)(i.Field,{name:e,validate:e=>{let r="string"==typeof e?e:String(e||"");return t&&(!e||""===r.trim())?l?.validator||"This field is required":o&&!o(e)?l?.validator||"Invalid value":x&&e&&!new RegExp(x).test(e)?l?.pattern||"Invalid format":void 0},children:({field:e,meta:t})=>(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)(a.A.Control,{...e,...m,id:r,as:u||"input",rows:c,isInvalid:t.touched&&!!t.error,onChange:r=>{e.onChange(r),n&&n(r)},value:void 0!==d?d:e.value}),t.touched&&t.error?(0,s.jsx)(a.A.Control.Feedback,{type:"invalid",children:t.error}):null]})}),l=({name:e,id:r,required:t,errorMessage:o,onChange:l,value:n,children:d,...u})=>(0,s.jsx)(i.Field,{name:e,validate:e=>{if(t&&(!e||""===e))return o?.validator||"This field is required"},children:({field:e,meta:t})=>(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)(a.A.Control,{as:"select",...e,...u,id:r,isInvalid:t.touched&&!!t.error,onChange:r=>{e.onChange(r),l&&l(r)},value:void 0!==n?n:e.value,children:d}),t.touched&&t.error?(0,s.jsx)(a.A.Control.Feedback,{type:"invalid",children:t.error}):null]})})},16116:e=>{e.exports=require("invariant")},18597:(e,r,t)=>{t.d(r,{A:()=>A});var s=t(3892),a=t.n(s),i=t(82015),o=t(80739),l=t(8732);let n=i.forwardRef(({className:e,bsPrefix:r,as:t="div",...s},i)=>(r=(0,o.oU)(r,"card-body"),(0,l.jsx)(t,{ref:i,className:a()(e,r),...s})));n.displayName="CardBody";let d=i.forwardRef(({className:e,bsPrefix:r,as:t="div",...s},i)=>(r=(0,o.oU)(r,"card-footer"),(0,l.jsx)(t,{ref:i,className:a()(e,r),...s})));d.displayName="CardFooter";var u=t(6417);let p=i.forwardRef(({bsPrefix:e,className:r,as:t="div",...s},n)=>{let d=(0,o.oU)(e,"card-header"),p=(0,i.useMemo)(()=>({cardHeaderBsPrefix:d}),[d]);return(0,l.jsx)(u.A.Provider,{value:p,children:(0,l.jsx)(t,{ref:n,...s,className:a()(r,d)})})});p.displayName="CardHeader";let c=i.forwardRef(({bsPrefix:e,className:r,variant:t,as:s="img",...i},n)=>{let d=(0,o.oU)(e,"card-img");return(0,l.jsx)(s,{ref:n,className:a()(t?`${d}-${t}`:d,r),...i})});c.displayName="CardImg";let x=i.forwardRef(({className:e,bsPrefix:r,as:t="div",...s},i)=>(r=(0,o.oU)(r,"card-img-overlay"),(0,l.jsx)(t,{ref:i,className:a()(e,r),...s})));x.displayName="CardImgOverlay";let m=i.forwardRef(({className:e,bsPrefix:r,as:t="a",...s},i)=>(r=(0,o.oU)(r,"card-link"),(0,l.jsx)(t,{ref:i,className:a()(e,r),...s})));m.displayName="CardLink";var v=t(7783);let h=(0,v.A)("h6"),f=i.forwardRef(({className:e,bsPrefix:r,as:t=h,...s},i)=>(r=(0,o.oU)(r,"card-subtitle"),(0,l.jsx)(t,{ref:i,className:a()(e,r),...s})));f.displayName="CardSubtitle";let g=i.forwardRef(({className:e,bsPrefix:r,as:t="p",...s},i)=>(r=(0,o.oU)(r,"card-text"),(0,l.jsx)(t,{ref:i,className:a()(e,r),...s})));g.displayName="CardText";let q=(0,v.A)("h5"),j=i.forwardRef(({className:e,bsPrefix:r,as:t=q,...s},i)=>(r=(0,o.oU)(r,"card-title"),(0,l.jsx)(t,{ref:i,className:a()(e,r),...s})));j.displayName="CardTitle";let b=i.forwardRef(({bsPrefix:e,className:r,bg:t,text:s,border:i,body:d=!1,children:u,as:p="div",...c},x)=>{let m=(0,o.oU)(e,"card");return(0,l.jsx)(p,{ref:x,...c,className:a()(r,m,t&&`bg-${t}`,s&&`text-${s}`,i&&`border-${i}`),children:d?(0,l.jsx)(n,{children:u}):u})});b.displayName="Card";let A=Object.assign(b,{Img:c,Title:j,Subtitle:f,Body:n,Link:m,Text:g,Header:p,Footer:d,ImgOverlay:x})},18622:e=>{e.exports=require("yup")},19121:e=>{e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},22326:e=>{e.exports=require("react-dom")},22541:e=>{e.exports=require("redux-persist/lib/storage")},23579:(e,r,t)=>{t.d(r,{sx:()=>u,s3:()=>a.s3,ks:()=>a.ks,yk:()=>s.A});var s=t(66994),a=t(15653),i=t(8732),o=t(82015),l=t.n(o),n=t(43294),d=t(59549);let u={RadioGroup:({name:e,valueSelected:r,onChange:t,errorMessage:s,children:a})=>{let{errors:o,touched:d}=(0,n.useFormikContext)(),u=d[e]&&o[e];l().useMemo(()=>({name:e}),[e]);let p=l().Children.map(a,r=>l().isValidElement(r)&&function(e){return"object"==typeof e&&null!==e}(r.props)?l().cloneElement(r,{name:e,...r.props}):r);return(0,i.jsxs)("div",{children:[(0,i.jsx)("div",{className:"radio-group",children:p}),u&&(0,i.jsx)("div",{className:"invalid-feedback d-block",children:s||("string"==typeof o[e]?o[e]:String(o[e]))})]})},RadioItem:({id:e,label:r,value:t,name:s,disabled:a})=>{let{values:o,setFieldValue:l}=(0,n.useFormikContext)(),u=s||e;return(0,i.jsx)(d.A.Check,{type:"radio",id:e,label:r,value:t,name:u,checked:o[u]===t,onChange:e=>{l(u,e.target.value)},disabled:a,inline:!0})}};s.A,a.ks,a.s3},25303:e=>{e.exports=require("@restart/ui/NavItem")},26324:e=>{e.exports=require("warning")},27825:e=>{e.exports=require("lodash")},27910:e=>{e.exports=require("stream")},28217:e=>{e.exports=require("@restart/ui/DropdownItem")},29021:e=>{e.exports=require("fs")},29294:e=>{e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},29628:e=>{e.exports=require("dom-helpers/addClass")},29825:e=>{e.exports=require("prop-types")},29841:e=>{e.exports=require("dom-helpers/hasClass")},30362:e=>{e.exports=require("@restart/ui/DropdownToggle")},33873:e=>{e.exports=require("path")},36653:e=>{e.exports=require("nprogress")},36955:e=>{e.exports=require("@restart/hooks/useBreakpoint")},37766:e=>{e.exports=require("@restart/hooks/useMergedRefs")},39756:e=>{e.exports=import("redux")},40051:e=>{e.exports=require("dom-helpers/removeClass")},40361:e=>{e.exports=require("next/dist/compiled/next-server/pages.runtime.prod.js")},42893:e=>{e.exports=import("react-hot-toast")},43294:e=>{e.exports=require("formik")},50009:e=>{e.exports=require("@restart/ui/DropdownMenu")},57664:e=>{e.exports=require("dom-helpers/scrollbarSize")},58928:e=>{e.exports=require("dom-helpers/canUseDOM")},59717:e=>{e.exports=require("@restart/ui/DropdownContext")},60560:e=>{e.exports=require("dom-helpers/contains")},63033:e=>{e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},65447:e=>{e.exports=require("@restart/ui/Button")},66994:(e,r,t)=>{t.d(r,{A:()=>n});var s=t(8732),a=t(82015),i=t(43294),o=t(18622);let l=(0,a.forwardRef)((e,r)=>{let{children:t,onSubmit:a,autoComplete:l,className:n,onKeyPress:d,initialValues:u,...p}=e,c=o.object().shape({});return(0,s.jsx)(i.Formik,{initialValues:u||{},validationSchema:c,onSubmit:(e,r)=>{let t={preventDefault:()=>{},stopPropagation:()=>{},currentTarget:null,target:null,nativeEvent:new Event("submit"),bubbles:!1,cancelable:!0,defaultPrevented:!1,eventPhase:0,isTrusted:!1,timeStamp:Date.now(),type:"submit",isDefaultPrevented:()=>!1,isPropagationStopped:()=>!1,persist:()=>{}};a&&a(t,e,r)},...p,children:e=>(0,s.jsx)(i.Form,{ref:r,onSubmit:e.handleSubmit,autoComplete:l,className:n,onKeyPress:d,children:"function"==typeof t?t(e):t})})});l.displayName="ValidationFormWrapper";let n=l},67364:e=>{e.exports=require("@restart/hooks/useCallbackRef")},68455:e=>{e.exports=import("redux-saga")},69722:e=>{e.exports=require("es6-promise")},74075:e=>{e.exports=require("zlib")},74716:e=>{e.exports=require("moment")},74987:e=>{e.exports=require("@restart/ui/ModalManager")},78097:e=>{e.exports=require("next-redux-saga")},78634:e=>{e.exports=require("@restart/ui/Anchor")},80237:(e,r)=>{Object.defineProperty(r,"A",{enumerable:!0,get:function(){return t}});var t=function(e){return e.PAGES="PAGES",e.PAGES_API="PAGES_API",e.APP_PAGE="APP_PAGE",e.APP_ROUTE="APP_ROUTE",e.IMAGE="IMAGE",e}({})},81366:e=>{e.exports=require("redux-auth-wrapper/connectedAuthWrapper")},81413:(e,r)=>{Object.defineProperty(r,"M",{enumerable:!0,get:function(){return function e(r,t){return t in r?r[t]:"then"in r&&"function"==typeof r.then?r.then(r=>e(r,t)):"function"==typeof r&&"default"===t?r:void 0}}})},81521:e=>{e.exports=require("dom-helpers/querySelectorAll")},81895:e=>{e.exports=require("@restart/hooks/useEventCallback")},82015:e=>{e.exports=require("react")},83623:(e,r,t)=>{t.a(e,async(e,s)=>{try{t.r(r),t.d(r,{default:()=>A});var a=t(8732),i=t(82015),o=t(7082),l=t(18597),n=t(83551),d=t(49481),u=t(59549),p=t(91353),c=t(66994),x=t(23579),m=t(44233),v=t.n(m),h=t(42893),f=t(19918),g=t.n(f),q=t(63487),j=t(88751),b=e([h,q]);[h,q]=b.then?(await b)():b;let A=e=>{let r={title:"",level:""},[t,s]=(0,i.useState)(r),m=e.routes&&"edit_risklevel"===e.routes[0]&&e.routes[1],{t:f}=(0,j.useTranslation)("common"),b=(0,i.useRef)(null),A=e=>{if(e.target){let{name:r,value:t}=e.target;s(e=>({...e,[r]:t}))}},k=e=>(e||(e=""),e.charAt(0).toUpperCase()+e.slice(1)),y=async(r,s)=>{let a,i;r.preventDefault();let o=s||t,l={title:o.title.trim(),level:parseInt(o.level)};m?(i="adminsetting.RiskLevel.updatesuccess",a=await q.A.patch(`/risklevel/${e.routes[1]}`,l)):(i="adminsetting.RiskLevel.success",a=await q.A.post("/risklevel",l)),a&&a._id?(h.default.success(f(i)),v().push("/adminsettings/risklevel")):a?.errorCode===11e3?h.default.error(f("duplicatesNotAllowed")):h.default.error(k(a))};return(0,i.useEffect)(()=>{let r={query:{},sort:{title:"asc"},limit:"~"};m&&(async()=>{let t=await q.A.get(`/risklevel/${e.routes[1]}`,r);s(e=>({...e,...t}))})()},[]),(0,a.jsx)("div",{children:(0,a.jsx)(o.A,{className:"formCard",fluid:!0,children:(0,a.jsx)(l.A,{style:{marginTop:"5px",boxShadow:"0 4px 8px 0 rgba(0, 0, 0, 0.2), 0 6px 20px 0 rgba(0, 0, 0, 0.19)"},children:(0,a.jsx)(c.A,{onSubmit:y,ref:b,initialValues:t,enableReinitialize:!0,children:(0,a.jsxs)(l.A.Body,{children:[(0,a.jsx)(n.A,{children:(0,a.jsx)(d.A,{children:(0,a.jsx)(l.A.Title,{children:f("adminsetting.RiskLevel.Risklevel")})})}),(0,a.jsx)("hr",{}),(0,a.jsxs)(n.A,{children:[(0,a.jsx)(d.A,{md:!0,lg:6,sm:12,children:(0,a.jsxs)(u.A.Group,{children:[(0,a.jsx)(u.A.Label,{className:"required-field",children:f("adminsetting.RiskLevel.Risklevelname")}),(0,a.jsx)(x.ks,{name:"title",id:"title",required:!0,value:t.title,validator:e=>""!==e.trim(),errorMessage:{validator:f("adminsetting.RiskLevel.add")},onChange:A})]})}),(0,a.jsx)(d.A,{md:!0,lg:6,sm:12,children:(0,a.jsxs)(u.A.Group,{children:[(0,a.jsx)(u.A.Label,{className:"required-field",children:f("adminsetting.RiskLevel.Risklevelvalue")}),(0,a.jsx)(x.ks,{min:"0",type:"number",name:"level",id:"level",required:!0,value:t.level,errorMessage:{validator:f("adminsetting.RiskLevel.value"),min:f("adminsetting.RiskLevel.minValue"),required:f("adminsetting.RiskLevel.value")},onChange:A})]})})]}),(0,a.jsx)(n.A,{className:"my-4",children:(0,a.jsxs)(d.A,{children:[(0,a.jsx)(p.A,{className:"me-2",type:"submit",variant:"primary",children:f("submit")}),(0,a.jsx)(p.A,{className:"me-2",onClick:()=>{s(r),window.scrollTo(0,0)},variant:"info",children:f("reset")}),(0,a.jsx)(g(),{href:"/adminsettings/[...routes]",as:"/adminsettings/risklevel",children:(0,a.jsx)(p.A,{variant:"secondary",children:f("Cancel")})})]})})]})})})})})};s()}catch(e){s(e)}})},86842:e=>{e.exports=require("@restart/ui/SelectableContext")},87571:e=>{e.exports=require("dom-helpers/transitionEnd")},88751:e=>{e.exports=require("next-i18next")},93787:e=>{e.exports=require("redux-persist")},94947:e=>{e.exports=require("@restart/hooks/useTimeout")},96196:e=>{e.exports=require("next-redux-wrapper")},98320:e=>{e.exports=require("@restart/hooks/useIsomorphicEffect")},99460:e=>{e.exports=require("dom-helpers/css")}};var r=require("../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[6089,9216,9616,2386],()=>t(5887));module.exports=s})();