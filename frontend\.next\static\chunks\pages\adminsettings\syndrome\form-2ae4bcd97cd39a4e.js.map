{"version": 3, "file": "static/chunks/pages/adminsettings/syndrome/form-2ae4bcd97cd39a4e.js", "mappings": "uIAwHA,MA9GkE,OAAC,OACjEA,CAAK,UACLC,CAAQ,GA4GKC,UA3GbC,EAAc,QA2GmBD,EAAC,UA3GA,QAClCE,EAAS,GAAG,UACZC,GAAW,CAAK,CACjB,GACOC,EAAYC,CAAAA,EAAAA,EAAAA,MAAAA,CAAMA,CAAiB,MACnC,CAACC,EAAWC,EAAa,CAAGC,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,EAAC,GAG3CC,CAAAA,EAAAA,EAAAA,SAAAA,CAASA,CAAC,KACJL,EAAUM,OAAO,EAAI,GAEnB,CAACJ,GAAaF,EAAUM,IAFa,GAEN,CAACC,SAFkB,GAEJb,IAChDM,EAAUM,CAD6C,MACtC,CAACC,SAAS,CAAGb,GAAS,GAG7C,EAAG,CAACA,EAAOQ,EAAU,EAGrB,IAAMM,EAAc,KACdR,EAAUM,OAAO,EAAIX,GACvBA,EAASK,EAAUM,GADc,IACP,CAACC,SAAS,CAExC,EAGME,EAAc,CAACC,EAAiBhB,KACpC,GAAwB,aAApB,OAAOiB,SAA0B,KAGnCX,EAFAW,SAASF,WAAW,CAACC,GAAS,EAAOhB,GAAS,IAC9Cc,WACAR,EAAAA,EAAUM,OAAAA,GAAVN,EAAmBY,KAAK,EAC1B,CACF,CAFIZ,CAIJ,MACE,UAACa,MAAAA,CAAIC,UAAU,0BAA0BC,MAAO,CAAEC,OAAQ,gBAAiB,WAEzE,MAD8B,GAC9B,wBACE,WAACH,MAAAA,CAAIC,UAAU,UAAUC,MAAO,CAAEE,QAAS,MAAOC,aAAc,iBAAkBC,WAAY,SAAU,YACpG,UAACC,SAAAA,CACCC,KAAK,SACLC,QAAS,IAAMb,EAAY,QAC3BM,MAAO,CAAEQ,OAAQ,QAASN,QAAS,SAAU,WAE7C,UAACO,SAAAA,UAAO,QAEV,UAACJ,SAAAA,CACCC,KAAK,SACLC,QAAS,IAAMb,EAAY,UAC3BM,MAAO,CAAEQ,OAAQ,QAASN,QAAS,SAAU,WAE7C,UAACQ,KAAAA,UAAG,QAEN,UAACL,SAAAA,CACCC,KAAK,SACLC,QAAS,IAAMb,EAAY,aAC3BM,MAAO,CAAEQ,OAAQ,QAASN,QAAS,SAAU,WAE7C,UAACS,IAAAA,UAAE,QAEL,UAACN,SAAAA,CACCC,KAAK,SACLC,QAAS,IAAMb,EAAY,qBAC3BM,MAAO,CAAEQ,OAAQ,QAASN,QAAS,SAAU,WAC9C,OAGD,UAACG,SAAAA,CACCC,KAAK,SACLC,QAAS,IAAMb,EAAY,uBAC3BM,MAAO,CAAEQ,OAAQ,QAASN,QAAS,SAAU,WAC9C,OAGD,UAACG,SAAAA,CACCC,KAAK,SACLC,QAAS,KACP,IAAMK,EAAMC,OAAO,sBACfD,GAAKlB,EAAY,aAAckB,EACrC,EACAZ,MAAO,CAAEQ,OAAQ,QAASN,QAAS,SAAU,WAC9C,YAIH,UAACJ,MAAAA,CACCgB,IAAK7B,EACL8B,gBAAiB,CAAC/B,EAClBgC,QAASvB,EACTwB,QAAS,IAAM7B,GAAa,GAC5B8B,OAAQ,IAAM9B,GAAa,GAC3BY,MAAO,CACLE,QAAS,OACTiB,UAAWpC,EACXqC,UAAoB,EAATrC,EACXsC,SAAU,OACVC,QAAS,MACX,EACAC,mBAAmB5C,EAAsB,GAAdG,EAC3B0C,gCAAgC,QAO5C,EC7GaC,EAAmD,IAC9D,GAAM,aAAEC,CAAW,UAAE9C,CAAQ,CAAE,CAAG+C,EAElC,MACE,UAAC9C,EAAoBA,CACnBF,MAAO+C,GAAe,GACtB9C,SAAWgD,GAAYhD,EAASgD,IAGtC,EAAE,iQC4JF,MA3JqB,IACjB,IAAMC,EAAmB,CACrBC,MAAO,GACPC,KAAM,GAwJCC,YAvJM,EACjB,EACM,CAAEC,CAAC,CAAE,CAAGC,CAAAA,EAAAA,EAAAA,EAAAA,CAAcA,CAAC,UACvB,CAACC,EAAYC,EAAc,CAAG/C,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAMwC,GAE5CQ,EAAWV,EAAMW,MAAM,EAAwB,kBAApBX,EAAMW,MAAM,CAAC,EAAE,EAAwBX,EAAMW,MAAM,CAAC,EAAE,CAEjFC,EAAe,MAAOC,EAAYC,SAQhCC,EACAC,EARJH,EAAMI,cAAc,GACpB,IAAMC,EAAM,CACRf,MAAOK,EAAWL,KAAK,CAACgB,IAAI,GAC5Bf,KAAMI,EAAWJ,IAAI,CACrBgB,YAAaZ,EAAWY,WAAW,EAKnCV,GACAM,EAAW,KADD,iDAEVD,EAAW,MAAMM,EAAAA,CAAUA,CAACC,KAAK,CAAC,aAA6B,OAAhBtB,EAAMW,MAAM,CAAC,EAAE,EAAIO,KAElEF,EAAW,oDACXD,EAAW,MAAMM,EAAAA,CAAUA,CAACE,IAAI,CAAC,YAAaL,IAE9CH,GAAYA,EAASS,GAAG,EAAE,EAC1BC,EAAKA,CAACC,OAAO,CAACpB,EAAEU,IAChBW,IAAAA,IAAW,CAAC,4BAEZF,EAAAA,EAAKA,CAACG,KAAK,CAACb,EAEpB,EAOMc,EAAe,IACjB,GAAIC,EAAEC,MAAM,CAAE,CACV,GAAM,MAAEC,CAAI,OAAEhF,CAAK,CAAE,CAAG8E,EAAEC,MAAM,CAChCtB,EAAc,GAAqB,EAC/B,GAAGwB,CAAS,CACZ,CAACD,CAF8B,CAEzB,CAAEhF,EACZ,EACJ,CACJ,EAEMkF,EAAoB,IACtBzB,EAAc,GAAqB,EAC/B,GAAGwB,CAAS,CACZb,EAF+B,UAElBpE,EACjB,EACJ,EAEAW,CAAAA,EAAAA,EAAAA,SAAAA,CAASA,CAAC,KACN,IAAMwE,EAAiB,CACnBC,MAAO,CAAC,EACRC,KAAM,CAAElC,MAAO,KAAM,EACrBmC,MAAO,GACX,EAEI5B,GAKA6B,CAJwB,MADd,IAEN,IAAMxB,EAAqB,MAAMM,EAAAA,CAAUA,CAACmB,GAAG,CAAC,aAA6B,OAAhBxC,EAAMW,MAAM,CAAC,EAAE,EAAIwB,GAChF1B,EAAc,GAAqB,EAAE,GAAGwB,CAAS,CAAE,EAAhB,CAAmBlB,CAAQ,CAAC,EACnE,IAGR,EAAG,EAAE,EAEL,IAAM0B,EAAUlF,CAAAA,EAAAA,EAAAA,MAAAA,CAAMA,CAAC,MAEvB,MACI,UAACmF,EAAAA,CAASA,CAAAA,CAACtE,UAAU,WAAWuE,KAAK,aACjC,UAACC,EAAAA,CAAIA,CAAAA,CACDvE,MAAO,CACHwE,UAAW,MACXC,UAAW,kEACf,WAEA,UAACC,EAAAA,CAAqBA,CAAAA,CAACC,SAAUpC,EAAczB,IAAKsD,EAASQ,cAAezC,EAAY0C,oBAAoB,WACxG,WAACN,EAAAA,CAAIA,CAACO,IAAI,YACN,UAACC,EAAAA,CAAGA,CAAAA,UACA,UAACC,EAAAA,CAAGA,CAAAA,UACA,UAACT,EAAAA,CAAIA,CAACU,KAAK,WAAE5C,EAAWJ,EAAE,sCAAwCA,EAAE,2CAG5E,UAACiD,KAAAA,CAAAA,GACD,WAACH,EAAAA,CAAGA,CAAAA,WACA,UAACC,EAAAA,CAAGA,CAAAA,CAACG,EAAE,IAACC,GAAI,EAAGC,GAAI,YACf,WAACC,EAAAA,CAAIA,CAACC,KAAK,YACP,UAACD,EAAAA,CAAIA,CAACE,KAAK,EAACzF,UAAU,0BACjBkC,EAAE,wCAEP,UAACwD,EAAAA,EAASA,CAAAA,CACN9B,KAAK,QACL+B,GAAG,QACHC,QAAQ,IACRhH,MAAOwD,EAAWL,KAAK,CACvB8D,UAAW,GAAmBjH,OAAMmE,IAAI,GACxC+C,aAAc,CACVD,UAAW3D,EAAE,iDACjB,EACArD,SAAU4E,SAItB,UAACwB,EAAAA,CAAGA,CAAAA,CAACG,EAAE,IAACC,GAAI,EAAGC,GAAI,YACf,WAACC,EAAAA,CAAIA,CAACC,KAAK,YACP,UAACD,EAAAA,CAAIA,CAACE,KAAK,WAAEvD,EAAE,gCACf,UAACwD,EAAAA,EAASA,CAAAA,CACN9B,KAAK,OACL+B,GAAG,OACHC,QAAQ,IACRhH,MAAOwD,EAAWJ,IAAI,CACtB8D,aAAc,CAACD,UAAW3D,EAAE,yCAAyC,EACrErD,SAAU4E,YAK1B,UAACuB,EAAAA,CAAGA,CAAAA,UACA,UAACC,EAAAA,CAAGA,CAAAA,UACA,WAACM,EAAAA,CAAIA,CAACC,KAAK,YACP,UAACD,EAAAA,CAAIA,CAACE,KAAK,WAAEvD,EAAE,uCACf,UAACR,EAAAA,CAAeA,CAAAA,CAACC,YAAaS,EAAWY,WAAW,CAAEnE,SAAU,GAAiBiF,EAAkBiC,YAI/G,UAACf,EAAAA,CAAGA,CAAAA,CAAChF,UAAU,gBACX,WAACiF,EAAAA,CAAGA,CAAAA,WACA,UAACe,EAAAA,CAAMA,CAAAA,CAAChG,UAAU,OAAOO,KAAK,SAAS0F,QAAQ,mBAC1C/D,EAAE,kCAEP,UAAC8D,EAAAA,CAAMA,CAAAA,CAAChG,UAAU,OAAOQ,QAtGhC,CAsGyC0F,IArG1D7D,EAAcP,GAEdqE,OAAOC,QAAQ,CAAC,EAAG,EACvB,EAkG4EH,QAAQ,gBACnD/D,EAAE,iCAEP,UAACmE,IAAIA,CACDC,KAAK,6BACLC,GAAK,OAFJF,4BAID,UAACL,EAAAA,CAAMA,CAAAA,CAACC,QAAQ,qBAAa/D,EAAE,iDASnE,0GCvKA,IAAMsE,EAAwBC,EAAAA,OAAb,GAA6B,CAAC,GAK5C1F,IALyB,IAAoB,WAC9Cf,CAAS,UACT0G,CAAQ,CACRH,GAAII,EAAY,KAAK,CACrB,GAAG/E,EACJ,GAEC,OADA8E,EAAWE,CAAAA,EAAAA,EAAAA,EAAAA,CAAkBA,CACX,EADsB,aACpBC,CAAAA,EAAAA,EAAAA,GAAAA,CAAIA,CAACF,EAAW,CAClC5F,IAAKA,EACLf,UAAW8G,IAAW9G,EAAW0G,GACjC,GAAG9E,CAAK,EAEZ,GACA4E,EAASO,WAAW,CAAG,WCbvB,IAAMC,EAA0BP,EAAAA,SAAb,CAA6B,CAAC,GAK9C1F,MAL2B,EAAoB,CAChDf,WAAS,UACT0G,CAAQ,CACRH,GAAII,EAAY,KAAK,CACrB,GAAG/E,EACJ,GAEC,OADA8E,EAAWE,CAAAA,EAAAA,EAAAA,EAAAA,CAAkBA,CACX,EADsB,eACpBC,CAAAA,EAAAA,EAAAA,GAAAA,CAAIA,CAACF,EAAW,CAClC5F,IAAKA,EACLf,UAAW8G,IAAW9G,EAAW0G,GACjC,GAAG9E,CAAK,EAEZ,GACAoF,EAAWD,WAAW,CAAG,4BCXzB,IAAME,EAA0BR,EAAAA,SAAb,CAA6B,CAAC,GAM9C1F,MAN2B,EAAoB,UAChD2F,CAAQ,WACR1G,CAAS,CAETuG,CADA,EACII,EAAY,KAAK,CACrB,GAAG/E,EACJ,GACOsF,EAASN,CAAAA,EAAAA,EAAAA,EAAAA,CAAkBA,CAACF,EAAU,eACtCS,EAAeC,CAAAA,EAAAA,EAAAA,OAAAA,CAAOA,CAAC,IAAO,EAClCC,mBAAoBH,EACtB,EAAI,CAACA,EAAO,EACZ,MAAoBL,CAAb,EAAaA,EAAAA,GAAAA,CAAIA,CAACS,EAAP,CAAwBA,CAACC,QAAQ,CAAE,CACnD3I,MAAOuI,EACPK,SAAuBX,CAAb,EAAaA,EAAAA,GAAAA,CAAIA,CAACF,EAAW,CACrC5F,IAAKA,EACL,GAAGa,CAAK,CACR5B,UAAW8G,IAAW9G,EAAWkH,EACnC,EACF,EACF,GACAD,EAAWF,GAJgBD,QAIL,CAAG,aCtBzB,IAAMW,EAAuBhB,EAAAA,MAAb,IAA6B,CAC7C,CACA,EAMG1F,GARwB,KAE1B,CACC2F,UAAQ,WACR1G,CAAS,SACTiG,CAAO,CACPM,GAAII,EAAY,KAAK,CACrB,GAAG/E,EACJ,GACOsF,EAASN,CAAAA,EAAAA,EAAAA,EAAAA,CAAkBA,CAACF,EAAU,YAC5C,MAAoBG,CAAb,EAAaA,EAAAA,GAAAA,CAAIA,CAACF,EAAW,CAClC5F,IAAKA,EACLf,UAAW8G,IAAWb,EAAU,GAAaA,MAAAA,CAAViB,EAAO,EAArBJ,GAAgC,OAARb,CAX0G,EAW9FiB,EAAQlH,GACjE,GAAG4B,CAAK,EAEZ,GACA6F,EAAQV,WAAW,CAAG,UChBtB,IAAMW,EAA8BjB,EAAAA,UAAgB,CAAC,EAA9B,CAKpB1F,QALmD,CACpDf,CADgC,UACvB,CACT0G,UAAQ,CACRH,GAAII,EAAY,KAAK,CACrB,GAAG/E,EACJ,GAEC,OADA8E,EAAWE,CAAAA,EAAAA,EAAAA,EAAAA,CAAkBA,CAACF,EAAU,oBACpBG,CAAAA,EAAAA,EAAAA,GAAAA,CAAIA,CAACF,EAAW,CAClC5F,IAAKA,EACLf,UAAW8G,IAAW9G,EAAW0G,GACjC,GAAG9E,CAAK,EAEZ,GACA8F,EAJyBZ,WAIC,CAAG,iBCb7B,IAAMa,EAAwBlB,EAAAA,OAAb,GAA6B,CAAC,GAK5C1F,IALyB,IAAoB,WAC9Cf,CAAS,UACT0G,CAAQ,CACRH,GAAII,EAAY,GAAG,CACnB,GAAG/E,EACJ,GAEC,OADA8E,EAAWE,CAAAA,EAAAA,EAAAA,EAAAA,CAAkBA,CAACF,EAAU,aACpBG,CAAAA,EAAAA,EAAAA,GAAAA,CAAIA,CAACF,EAAW,CAClC5F,IAAKA,EACLf,UAAW8G,IAAW9G,EAAW0G,GACjC,GAAG9E,CAAK,EAEZ,GACA+F,EAJyBb,WAIL,CAAG,0BCZvB,IAAMc,EAAgBC,CAAAA,EAAAA,EAAAA,CAAAA,CAAgBA,CAAC,MACjCC,EAA4BrB,EAAAA,UAAgB,CAA7B,CAA8B,EAKhD1F,QAL6B,WAC9Bf,CAAS,UACT0G,CAAQ,CACRH,GAAII,EAAYiB,CAAa,CAC7B,GAAGhG,EACJ,GAEC,OADA8E,EAAWE,CAAAA,EAAAA,EAAAA,EAAAA,CAAkBA,CAACF,EAAU,iBACpBG,CAAAA,EAAAA,EAAAA,GAAAA,CAAIA,CAACF,EAAW,CAClC5F,IAAKA,EACLf,UAAW8G,IAAW9G,EAAW0G,GACjC,GAAG9E,CAAK,EAEZ,EACAkG,GAJyBhB,WAID,CAAG,eCf3B,IAAMiB,EAAwBtB,EAAAA,OAAb,GAA6B,CAAC,GAK5C1F,IALyB,IAAoB,WAC9Cf,CAAS,CACT0G,UAAQ,CACRH,GAAII,EAAY,GAAG,CACnB,GAAG/E,EACJ,GAEC,OADA8E,EAAWE,CAAAA,EAAAA,EAAAA,EAAAA,CAAkBA,CAACF,EAAU,aACpBG,CAAAA,EAAAA,EAAAA,GAAAA,CAAIA,CAACF,EAAW,CAClC5F,IAAKA,EACLf,UAAW8G,IAAW9G,EAAW0G,GACjC,GAAG9E,CAAK,EAEZ,GACAmG,EAAShB,WAAW,CAAG,WCZvB,IAAMiB,EAAgBH,CAAAA,EAAAA,EAAAA,CAAAA,CAAgBA,CAAC,MACjCI,EAAyBxB,EAAAA,QAAb,EAA6B,CAAC,GAK7C1F,KAL0B,GAAoB,WAC/Cf,CAAS,UACT0G,CAAQ,CACRH,GAAII,EAAYqB,CAAa,CAC7B,GAAGpG,EACJ,GAEC,OAAO,EADIgF,CAAAA,EAAAA,EAAAA,EAAAA,CAAkBA,CAACF,EAAU,cACpBG,CAAAA,EAAAA,EAAAA,GAAAA,CAAIA,CAACF,EAAW,CAClC5F,IAAKA,EACLf,UAAW8G,IAAW9G,EAAW0G,GACjC,GAAG9E,CAAK,EAEZ,GACAqG,EAJyBnB,WAIJ,CAAG,YCNxB,IAAMtC,EAAoBiC,EAAAA,GAAb,OAA6B,CAAC,GAWxC1F,QAXyC,UAC1C2F,CAAQ,WACR1G,CAAS,IACTkI,CAAE,MACFC,CAAI,QACJjI,CAAM,MACNkI,GAAO,CAAK,UACZZ,CAAQ,CAERjB,CADA,EACII,EAAY,KAAK,CACrB,GAAG/E,EACJ,GACOsF,EAASN,CAAAA,EAAAA,EAAAA,EAAAA,CAAkBA,CAACF,EAAU,QAC5C,MAAoBG,CAAb,EAAaA,EAAAA,GAAAA,CAAIA,CAACF,EAAW,CAClC5F,IAAKA,EACL,GAAGa,CAAK,CACR5B,UAAW8G,IAAW9G,EAAWkH,EAAQgB,GAAM,MAAS,GAAnCpB,GAAmC,CAAHoB,GAAMC,GAAQ,QAAa,OAALA,GAAQjI,GAAU,UAAiB,OAAPA,IACvGsH,IATyJ,KAS/IY,EAAoBvB,CAAAA,EAAAA,EAAb,GAAaA,CAAIA,CAACL,EAAU,CAC3CgB,GAD0B,MAAehB,CAE3C,GAAKgB,CACP,EACF,GACAhD,EAAKuC,WAAW,CAAG,OACnB,MAAesB,OAAOC,MAAM,CAAC9D,EAAM,CACjC+D,INhBad,CMgBRA,CACLvC,KNjBoBuC,CKDPQ,CLCQ,CMkBrBO,EAFYf,KDjBUQ,EAAC,CCmBbH,CACV/C,CAFgBkD,ITpBHzB,CSsBPA,CACNH,GHrByByB,EAAC,CNFLtB,CSwBrBiC,CTxBsB,GSsBRjC,CFtBDuB,CFAQJ,CIyBrBe,CJzBsB,GIuBRf,EFvBOI,CLSRd,CKTS,CE0BtB0B,EAFcZ,KRxBDf,CCSUC,COkBvB2B,CPlBwB,GOgBN3B,IRzBKD,EAAC,CGAXU,CK2BDA,CADMV,CAElB,EAAC,SL5B0BU,EAAC,GK2BFA,0ICwCrB,IAAMmB,EAAQ,CACnBC,WA1C4C,OAAC,MAC7ClF,CAAI,eACJmF,CAAa,UACblK,CAAQ,cACRiH,CAAY,CACZ0B,UAAQ,CACT,GACO,QAAEwB,CAAM,CAAEC,SAAO,CAAE,CAAGC,CAAAA,EAAAA,EAAAA,EAAAA,CAAgBA,GACtCC,EAAWF,CAAO,CAACrF,EAAK,EAAIoF,CAAM,CAACpF,EAAK,CAGzB6C,EAAAA,OAAa,CAAC,IAAO,OAAE7C,EAAK,EAAI,CAACA,EAAK,EAG3D,IAAMwF,EAAoB3C,EAAAA,QAAc,CAAC4C,GAAG,CAAC7B,EAAW8B,GACtD,EAAI7C,cAAoB,CAAC6C,IAEnBC,IAF2B,KAxC5BA,CAAmB,EAC1B,MAAwB,UAAjB,OAAO3H,GAAgC,OAAVA,CACtC,EAwCmB0H,EAAM1H,KAAK,EACf6E,CADkB,CAClBA,YAAkB,CAAC6C,EAA6C,MACrE1F,EACA,GAAG0F,EAAM1H,KAAK,GAIb0H,GAGT,MACE,WAACvJ,MAAAA,WACC,UAACA,MAAAA,CAAIC,UAAU,uBACZoJ,IAEFD,GACC,UAACpJ,MAAAA,CAAIC,UAAU,oCACZ8F,GAAiB,kBAAOkD,CAAM,CAACpF,EAAK,CAAgBoF,CAAM,CAACpF,EAAK,CAAG4F,OAAOR,CAAM,CAACpF,GAAK,MAKjG,EAIE6F,UAhE0C,OAAC,IAAE9D,CAAE,OAAE+D,CAAK,OAAE9K,CAAK,MAAEgF,CAAI,UAAE3E,CAAQ,CAAE,GACzE,QAAEyD,CAAM,eAAEiH,CAAa,CAAE,CAAGT,CAAAA,EAAAA,EAAAA,EAAAA,CAAgBA,GAC5CU,EAAYhG,GAAQ+B,EAE1B,MACE,UAACJ,EAAAA,CAAIA,CAACsE,KAAK,EACTtJ,KAAK,QACLoF,GAAIA,EACJ+D,MAAOA,EACP9K,MAAOA,EACPgF,KAAMgG,EACNE,QAASpH,CAAM,CAACkH,EAAU,GAAKhL,EAC/BC,SAAU,IACR8K,EAAcC,EAAWlG,EAAEC,MAAM,CAAC/E,KAAK,CACzC,EACAK,SAAUA,EACV8K,MAAM,KAGZ,CA8CA,EAAE,ECzEcC,CAAAA,CACLtE,EAAAA,EAAAA,CACEuE,EAAAA,EAAAA,gGCeb,IAAMtF,EAAwBuF,CAAAA,EAAAA,EAAAA,UAAAA,CAAUA,CAA8C,CAACtI,EAAOb,KAC5F,GAAM,UAAEyG,CAAQ,UAAE5C,CAAQ,cAAEuF,CAAY,WAAEnK,CAAS,YAAEoK,CAAU,eAAEvF,CAAa,CAAE,GAAGwF,EAAM,CAAGzI,EAGtF0I,EAAmBC,EAAAA,EAAU,GAAGC,KAAK,CAAC,CAAC,GAE7C,MACE,UAACC,EAAAA,EAAMA,CAAAA,CACL5F,cAAeA,GAAiB,CAAC,EACjCyF,iBAAkBA,EAClB1F,SAAU,CAAClC,EAA6BgI,KAEtC,IAAMC,EAAuB,CAC3B9H,eAAgB,KAAO,EACvB+H,gBAAiB,KAAO,EACxBC,cAAe,KACflH,OAAQ,KACRmH,YAAa,IAAIC,MAAM,UACvBC,SAAS,EACTC,YAAY,EACZC,kBAAkB,EAClBC,WAAY,EACZC,WAAW,EACXC,UAAWC,KAAKC,GAAG,GACnBhL,KAAM,SACNiL,mBAAoB,KAAM,EAC1BC,qBAAsB,KAAM,EAC5BC,QAAS,KAAO,CAClB,EAEI9G,GAEFA,EAAS+F,EAAWjI,EAAQgI,CAFhB,CAIhB,EACC,GAAGL,CAAI,UAEP,GACC,UAAC9E,EAAAA,EAAIA,CAAAA,CACHxE,IAAKA,EACL6D,SAAU+G,EAAYnJ,YAAY,CAClC2H,aAAcA,EACdnK,UAAWA,EACXoK,WAAYA,WAES,YAApB,OAAO5C,EAA0BA,EAASmE,GAAenE,KAKpE,GAEA7C,EAAsBoC,WAAW,CAAG,wBAEpC,MAAepC,qBAAqBA,EAAC,sFClF9B,IAAMe,EAAY,OAAC,MACxB9B,CAAI,IACJ+B,CAAE,UACFC,CAAQ,WACRC,CAAS,CACTC,cAAY,CACZjH,UAAQ,OACRD,CAAK,IACL2H,CAAE,WACFqF,CAAS,MACTC,CAAI,SACJC,CAAO,CACP,GAAGlK,EACC,GAuBJ,MACE,UAACmK,EAAAA,EAAKA,CAAAA,CAACnI,KAAMA,EAAMoI,SAtBJ,CAsBcA,GApB7B,IAAMC,EAA2B,UAAf,OAAOC,EAAmBA,EAAM1C,OAAO0C,GAAO,WAChE,GAAiB,EAACA,GAA4B,IAA7B,CAAQD,EAAUlJ,IAAI,EAAO,CAAC,CACtC+C,EAD0C,KAC1CA,EAAAA,KAAAA,EAAAA,EAAcD,SAAAA,GAAa,EAA3BC,uBAGLD,GAAa,CAACA,EAAUqG,GACnBpG,GADyB,IACzBA,EAAAA,KAAAA,EAAAA,EAAcD,SAAS,GAAI,EAA3BC,cAGLgG,GAAWI,GAET,CADU,CADI,GACAC,OAAOL,GACdM,IAAI,CAACF,GACPpG,GADa,IACbA,EAAAA,KAAAA,EAAAA,EAAcgG,OAAAA,GAAW,IAAzBhG,mBAKb,WAIK,OAAC,OAAEuG,CAAK,MAAEC,CAAI,CAAO,SACpB,iCACE,UAAC/G,EAAAA,CAAIA,CAACgH,OAAO,EACV,GAAGF,CAAK,CACR,GAAGzK,CAAK,CACT+D,GAAIA,EACJY,GAAIA,GAAM,QACVsF,KAAMA,EACNW,UAAWF,EAAKrD,OAAO,EAAI,CAAC,CAACqD,EAAK9I,KAAK,CACvC3E,SAAU,IACRwN,EAAMxN,QAAQ,CAAC6E,GACX7E,GAAUA,EAAS6E,EACzB,EACA9E,WAAiB6N,IAAV7N,EAAsBA,EAAQyN,EAAMzN,KAAK,GAEjD0N,EAAKrD,OAAO,EAAIqD,EAAK9I,KAAK,CACzB,UAAC+B,EAAAA,CAAIA,CAACgH,OAAO,CAACG,QAAQ,EAACnM,KAAK,mBACzB+L,EAAK9I,KAAK,GAEX,UAKd,EAIayG,EAAc,OAAC,MAC1BrG,CAAI,IACJ+B,CAAE,UACFC,CAAQ,CACRE,cAAY,CACZjH,UAAQ,OACRD,CAAK,UACL4I,CAAQ,CACR,GAAG5F,EACC,GAUJ,MACE,UAACmK,EAAAA,EAAKA,CAAAA,CAACnI,KAAMA,EAAMoI,SATJ,CAScA,GAR7B,GAAIpG,GAAa,EAACsG,GAAe,IAAhB,CAAQA,CAAQ,CAAC,CAChC,EADoC,IAC7BpG,OAAAA,EAAAA,KAAAA,EAAAA,EAAcD,SAAAA,GAAa,EAA3BC,sBAIX,WAIK,OAAC,OAAEuG,CAAK,MAAEC,CAAI,CAAO,SACpB,iCACE,UAAC/G,EAAAA,CAAIA,CAACgH,OAAO,EACXhG,GAAG,SACF,GAAG8F,CAAK,CACR,GAAGzK,CAAK,CACT+D,GAAIA,EACJ6G,UAAWF,EAAKrD,OAAO,EAAI,CAAC,CAACqD,EAAK9I,KAAK,CACvC3E,SAAU,IACRwN,EAAMxN,QAAQ,CAAC6E,GACX7E,GAAUA,EAAS6E,EACzB,EACA9E,WAAiB6N,IAAV7N,EAAsBA,EAAQyN,EAAMzN,KAAK,UAE/C4I,IAEF8E,EAAKrD,OAAO,EAAIqD,EAAK9I,KAAK,CACzB,UAAC+B,EAAAA,CAAIA,CAACgH,OAAO,CAACG,QAAQ,EAACnM,KAAK,mBACzB+L,EAAK9I,KAAK,GAEX,UAKd,EAAE,+CCnHF,IAAMmJ,EAAuBlG,QAAb,CAAaA,UAAF,GAAqB,CAAC,MACjDkG,EAAQ5F,WAAW,CAAG,oBACtB,MAAe4F,OAAOA,EAAC,UCJvB,4CACA,+BACA,WACA,OAAe,EAAQ,KAAoD,CAC3E,EACA,SAFsB", "sources": ["webpack://_N_E/./components/common/SimpleRichTextEditor.tsx", "webpack://_N_E/./shared/quill-editor/quill-editor.component.tsx", "webpack://_N_E/./pages/adminsettings/syndrome/form.tsx", "webpack://_N_E/./node_modules/react-bootstrap/esm/CardBody.js", "webpack://_N_E/./node_modules/react-bootstrap/esm/CardFooter.js", "webpack://_N_E/./node_modules/react-bootstrap/esm/CardHeader.js", "webpack://_N_E/./node_modules/react-bootstrap/esm/CardImg.js", "webpack://_N_E/./node_modules/react-bootstrap/esm/CardImgOverlay.js", "webpack://_N_E/./node_modules/react-bootstrap/esm/CardLink.js", "webpack://_N_E/./node_modules/react-bootstrap/esm/CardSubtitle.js", "webpack://_N_E/./node_modules/react-bootstrap/esm/CardText.js", "webpack://_N_E/./node_modules/react-bootstrap/esm/CardTitle.js", "webpack://_N_E/./node_modules/react-bootstrap/esm/Card.js", "webpack://_N_E/./components/common/FormikRadio.tsx", "webpack://_N_E/./components/common/FormValidation.tsx", "webpack://_N_E/./components/common/ValidationFormWrapper.tsx", "webpack://_N_E/./components/common/FormikTextInput.tsx", "webpack://_N_E/./node_modules/react-bootstrap/esm/CardHeaderContext.js", "webpack://_N_E/?0bc4"], "sourcesContent": ["import React, { useRef, useEffect, useState } from 'react';\r\n\r\ninterface SimpleRichTextEditorProps {\r\n  value: string;\r\n  onChange: (content: string) => void;\r\n  placeholder?: string;\r\n  height?: number;\r\n  disabled?: boolean;\r\n}\r\n\r\nconst SimpleRichTextEditor: React.FC<SimpleRichTextEditorProps> = ({\r\n  value,\r\n  onChange,\r\n  placeholder = 'Write something...',\r\n  height = 300,\r\n  disabled = false,\r\n}) => {\r\n  const editorRef = useRef<HTMLDivElement>(null);\r\n  const [isFocused, setIsFocused] = useState(false);\r\n\r\n  // Initialize editor with HTML content\r\n  useEffect(() => {\r\n    if (editorRef.current && typeof window !== 'undefined') {\r\n      // Only update if the editor doesn't have focus to prevent cursor jumping\r\n      if (!isFocused && editorRef.current.innerHTML !== value) {\r\n        editorRef.current.innerHTML = value || '';\r\n      }\r\n    }\r\n  }, [value, isFocused]);\r\n\r\n  // Handle content changes\r\n  const handleInput = () => {\r\n    if (editorRef.current && onChange) {\r\n      onChange(editorRef.current.innerHTML);\r\n    }\r\n  };\r\n\r\n  // Simple toolbar buttons\r\n  const execCommand = (command: string, value?: string) => {\r\n    if (typeof document !== 'undefined') {\r\n      document.execCommand(command, false, value || '');\r\n      handleInput();\r\n      editorRef.current?.focus();\r\n    }\r\n  };\r\n\r\n  return (\r\n    <div className=\"simple-rich-text-editor\" style={{ border: '1px solid #ccc' }}>\r\n      {typeof window !== 'undefined' && (\r\n      <>\r\n        <div className=\"toolbar\" style={{ padding: '8px', borderBottom: '1px solid #ccc', background: '#f5f5f5' }}>\r\n            <button\r\n              type=\"button\"\r\n              onClick={() => execCommand('bold')}\r\n              style={{ margin: '0 5px', padding: '3px 8px' }}\r\n            >\r\n              <strong>B</strong>\r\n            </button>\r\n            <button\r\n              type=\"button\"\r\n              onClick={() => execCommand('italic')}\r\n              style={{ margin: '0 5px', padding: '3px 8px' }}\r\n            >\r\n              <em>I</em>\r\n            </button>\r\n            <button\r\n              type=\"button\"\r\n              onClick={() => execCommand('underline')}\r\n              style={{ margin: '0 5px', padding: '3px 8px' }}\r\n            >\r\n              <u>U</u>\r\n            </button>\r\n            <button\r\n              type=\"button\"\r\n              onClick={() => execCommand('insertOrderedList')}\r\n              style={{ margin: '0 5px', padding: '3px 8px' }}\r\n            >\r\n              OL\r\n            </button>\r\n            <button\r\n              type=\"button\"\r\n              onClick={() => execCommand('insertUnorderedList')}\r\n              style={{ margin: '0 5px', padding: '3px 8px' }}\r\n            >\r\n              UL\r\n            </button>\r\n            <button\r\n              type=\"button\"\r\n              onClick={() => {\r\n                const url = prompt('Enter the link URL');\r\n                if (url) execCommand('createLink', url);\r\n              }}\r\n              style={{ margin: '0 5px', padding: '3px 8px' }}\r\n            >\r\n              Link\r\n            </button>\r\n          </div>\r\n          <div\r\n            ref={editorRef}\r\n            contentEditable={!disabled}\r\n            onInput={handleInput}\r\n            onFocus={() => setIsFocused(true)}\r\n            onBlur={() => setIsFocused(false)}\r\n            style={{\r\n              padding: '15px',\r\n              minHeight: height,\r\n              maxHeight: height * 2,\r\n              overflow: 'auto',\r\n              outline: 'none',\r\n            }}\r\n            data-placeholder={!value ? placeholder : ''}\r\n            suppressContentEditableWarning={true}\r\n          >\r\n          </div>\r\n      </>\r\n      )}\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default SimpleRichTextEditor;\r\n", "// React Imports\r\nimport React from \"react\";\r\nimport SimpleRichTextEditor from \"../../components/common/SimpleRichTextEditor\";\r\n\r\ninterface IEditorComponentProps {\r\n  initContent: string | undefined;\r\n  onChange: Function;\r\n}\r\n\r\nexport const EditorComponent: React.FC<IEditorComponentProps> = (props) => {\r\n  const { initContent, onChange } = props;\r\n\r\n  return (\r\n    <SimpleRichTextEditor\r\n      value={initContent || \"\"}\r\n      onChange={(content) => onChange(content)}\r\n    />\r\n  );\r\n};\r\n", "//Import Library\r\nimport { Con<PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, Button } from \"react-bootstrap\";\r\nimport { TextInput } from \"../../../components/common/FormValidation\";\r\nimport ValidationFormWrapper from \"../../../components/common/ValidationFormWrapper\";\r\nimport { useRef, useState, useEffect } from \"react\";\r\nimport toast from 'react-hot-toast';\r\nimport Router from \"next/router\";\r\nimport Link from \"next/link\";\r\n\r\n//Import services/components\r\nimport apiService from \"../../../services/apiService\";\r\nimport { Syndrome } from \"../../../types\";\r\nimport { useTranslation } from 'next-i18next';\r\nimport { EditorComponent } from \"../../../shared/quill-editor/quill-editor.component\";\r\n\r\ninterface SyndromeFormProps {\r\n    [key: string]: any;\r\n}\r\n\r\nconst SyndromeForm = (props: SyndromeFormProps) => {\r\n    const _initialSyndrome = {\r\n        title: \"\",\r\n        code: \"\",\r\n        description: \"\",\r\n    };\r\n    const { t } = useTranslation('common');\r\n    const [initialVal, setInitialVal] = useState<any>(_initialSyndrome);\r\n\r\n    const editform = props.routes && props.routes[0] === \"edit_syndrome\" && props.routes[1];\r\n\r\n    const handleSubmit = async (event: any, values?: any) => {\r\n        event.preventDefault();\r\n        const obj = {\r\n            title: initialVal.title.trim(),\r\n            code: initialVal.code,\r\n            description: initialVal.description,\r\n        };\r\n\r\n        let response;\r\n        let toastMsg;\r\n        if (editform) {\r\n            toastMsg = \"adminsetting.syndrome.Syndromeisupdatedsuccessfully\";\r\n            response = await apiService.patch(`/syndrome/${props.routes[1]}`, obj);\r\n        } else {\r\n            toastMsg = \"adminsetting.syndrome.Syndromeisaddedsuccessfully\";\r\n            response = await apiService.post(\"/syndrome\", obj);\r\n        }\r\n        if (response && response._id) {\r\n            toast.success(t(toastMsg));\r\n            Router.push(\"/adminsettings/syndrome\");\r\n        } else {\r\n            toast.error(response);\r\n        }\r\n    };\r\n\r\n    const resetHandler = () => {\r\n        setInitialVal(_initialSyndrome);\r\n        // Reset validation state (Formik handles this automatically)\r\n        window.scrollTo(0, 0);\r\n    };\r\n    const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement>) => {\r\n        if (e.target) {\r\n            const { name, value } = e.target;\r\n            setInitialVal((prevState: any) => ({\r\n                ...prevState,\r\n                [name]: value,\r\n            }));\r\n        }\r\n    };\r\n\r\n    const handleDescription = (value: string) => {\r\n        setInitialVal((prevState: any) => ({\r\n            ...prevState,\r\n            description: value,\r\n        }));\r\n    };\r\n\r\n    useEffect(() => {\r\n        const syndromeParams = {\r\n            query: {},\r\n            sort: { title: \"asc\" },\r\n            limit: \"~\",\r\n        };\r\n\r\n        if (editform) {\r\n            const getSyndromeData = async () => {\r\n                const response: Syndrome = await apiService.get(`/syndrome/${props.routes[1]}`, syndromeParams);\r\n                setInitialVal((prevState: any) => ({ ...prevState, ...response }));\r\n            };\r\n            getSyndromeData();\r\n        }\r\n    }, []);\r\n\r\n    const formRef = useRef(null);\r\n\r\n    return (\r\n        <Container className=\"formCard\" fluid>\r\n            <Card\r\n                style={{\r\n                    marginTop: \"5px\",\r\n                    boxShadow: \"0 4px 8px 0 rgba(0, 0, 0, 0.2), 0 6px 20px 0 rgba(0, 0, 0, 0.19)\",\r\n                }}\r\n            >\r\n                <ValidationFormWrapper onSubmit={handleSubmit} ref={formRef} initialValues={initialVal} enableReinitialize={true}>\r\n                    <Card.Body>\r\n                        <Row>\r\n                            <Col>\r\n                                <Card.Title>{editform ? t(\"adminsetting.syndrome.EditSyndrome\") : t(\"adminsetting.syndrome.AddSyndrome\")}</Card.Title>\r\n                            </Col>\r\n                        </Row>\r\n                        <hr />\r\n                        <Row>\r\n                            <Col md lg={6} sm={12}>\r\n                                <Form.Group>\r\n                                    <Form.Label className=\"required-field\">\r\n                                        {t(\"adminsetting.syndrome.SyndromeName\")}\r\n                                    </Form.Label>\r\n                                    <TextInput\r\n                                        name=\"title\"\r\n                                        id=\"title\"\r\n                                        required\r\n                                        value={initialVal.title}\r\n                                        validator={(value: string) => value.trim() !== \"\"}\r\n                                        errorMessage={{\r\n                                            validator: t(\"adminsetting.syndrome.PleaseAddtheSyndromeName\"),\r\n                                        }}\r\n                                        onChange={handleChange}\r\n                                    />\r\n                                </Form.Group>\r\n                            </Col>\r\n                            <Col md lg={6} sm={12}>\r\n                                <Form.Group>\r\n                                    <Form.Label>{t(\"adminsetting.syndrome.Code\")}</Form.Label>\r\n                                    <TextInput\r\n                                        name=\"code\"\r\n                                        id=\"code\"\r\n                                        required\r\n                                        value={initialVal.code}\r\n                                        errorMessage={{validator: t(\"adminsetting.syndrome.PleaseAddtheCode\")}}\r\n                                        onChange={handleChange}\r\n                                    />\r\n                                </Form.Group>\r\n                            </Col>\r\n                        </Row>\r\n                        <Row>\r\n                            <Col>\r\n                                <Form.Group>\r\n                                    <Form.Label>{t(\"adminsetting.syndrome.Description\")}</Form.Label>\r\n                                    <EditorComponent initContent={initialVal.description} onChange={(evt: string) => handleDescription(evt)} />\r\n                                </Form.Group>\r\n                            </Col>\r\n                        </Row>\r\n                        <Row className=\"my-4\">\r\n                            <Col>\r\n                                <Button className=\"me-2\" type=\"submit\" variant=\"primary\">\r\n                                    {t(\"adminsetting.syndrome.Submit\")}\r\n                                </Button>\r\n                                <Button className=\"me-2\" onClick={resetHandler} variant=\"info\">\r\n                                    {t(\"adminsetting.syndrome.Reset\")}\r\n                                </Button>\r\n                                <Link\r\n                                    href=\"/adminsettings/[...routes]\"\r\n                                    as={`/adminsettings/syndrome`}\r\n                                    >\r\n                                    <Button variant=\"secondary\">{t(\"adminsetting.syndrome.Cancel\")}</Button>\r\n                                </Link>\r\n                            </Col>\r\n                        </Row>\r\n                    </Card.Body>\r\n                </ValidationFormWrapper>\r\n            </Card>\r\n        </Container>\r\n    );\r\n};\r\nexport default SyndromeForm;\r\n", "\"use client\";\n\nimport * as React from 'react';\nimport classNames from 'classnames';\nimport { useBootstrapPrefix } from './ThemeProvider';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst CardBody = /*#__PURE__*/React.forwardRef(({\n  className,\n  bsPrefix,\n  as: Component = 'div',\n  ...props\n}, ref) => {\n  bsPrefix = useBootstrapPrefix(bsPrefix, 'card-body');\n  return /*#__PURE__*/_jsx(Component, {\n    ref: ref,\n    className: classNames(className, bsPrefix),\n    ...props\n  });\n});\nCardBody.displayName = 'CardBody';\nexport default CardBody;", "\"use client\";\n\nimport * as React from 'react';\nimport classNames from 'classnames';\nimport { useBootstrapPrefix } from './ThemeProvider';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst CardFooter = /*#__PURE__*/React.forwardRef(({\n  className,\n  bsPrefix,\n  as: Component = 'div',\n  ...props\n}, ref) => {\n  bsPrefix = useBootstrapPrefix(bsPrefix, 'card-footer');\n  return /*#__PURE__*/_jsx(Component, {\n    ref: ref,\n    className: classNames(className, bsPrefix),\n    ...props\n  });\n});\nCardFooter.displayName = 'CardFooter';\nexport default CardFooter;", "\"use client\";\n\nimport classNames from 'classnames';\nimport * as React from 'react';\nimport { useMemo } from 'react';\nimport { useBootstrapPrefix } from './ThemeProvider';\nimport CardHeaderContext from './CardHeaderContext';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst CardHeader = /*#__PURE__*/React.forwardRef(({\n  bsPrefix,\n  className,\n  // Need to define the default \"as\" during prop destructuring to be compatible with styled-components github.com/react-bootstrap/react-bootstrap/issues/3595\n  as: Component = 'div',\n  ...props\n}, ref) => {\n  const prefix = useBootstrapPrefix(bsPrefix, 'card-header');\n  const contextValue = useMemo(() => ({\n    cardHeaderBsPrefix: prefix\n  }), [prefix]);\n  return /*#__PURE__*/_jsx(CardHeaderContext.Provider, {\n    value: contextValue,\n    children: /*#__PURE__*/_jsx(Component, {\n      ref: ref,\n      ...props,\n      className: classNames(className, prefix)\n    })\n  });\n});\nCardHeader.displayName = 'CardHeader';\nexport default CardHeader;", "\"use client\";\n\nimport classNames from 'classnames';\nimport * as React from 'react';\nimport { useBootstrapPrefix } from './ThemeProvider';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst CardImg = /*#__PURE__*/React.forwardRef(\n// Need to define the default \"as\" during prop destructuring to be compatible with styled-components github.com/react-bootstrap/react-bootstrap/issues/3595\n({\n  bsPrefix,\n  className,\n  variant,\n  as: Component = 'img',\n  ...props\n}, ref) => {\n  const prefix = useBootstrapPrefix(bsPrefix, 'card-img');\n  return /*#__PURE__*/_jsx(Component, {\n    ref: ref,\n    className: classNames(variant ? `${prefix}-${variant}` : prefix, className),\n    ...props\n  });\n});\nCardImg.displayName = 'CardImg';\nexport default CardImg;", "\"use client\";\n\nimport * as React from 'react';\nimport classNames from 'classnames';\nimport { useBootstrapPrefix } from './ThemeProvider';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst CardImgOverlay = /*#__PURE__*/React.forwardRef(({\n  className,\n  bsPrefix,\n  as: Component = 'div',\n  ...props\n}, ref) => {\n  bsPrefix = useBootstrapPrefix(bsPrefix, 'card-img-overlay');\n  return /*#__PURE__*/_jsx(Component, {\n    ref: ref,\n    className: classNames(className, bsPrefix),\n    ...props\n  });\n});\nCardImgOverlay.displayName = 'CardImgOverlay';\nexport default CardImgOverlay;", "\"use client\";\n\nimport * as React from 'react';\nimport classNames from 'classnames';\nimport { useBootstrapPrefix } from './ThemeProvider';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst CardLink = /*#__PURE__*/React.forwardRef(({\n  className,\n  bsPrefix,\n  as: Component = 'a',\n  ...props\n}, ref) => {\n  bsPrefix = useBootstrapPrefix(bsPrefix, 'card-link');\n  return /*#__PURE__*/_jsx(Component, {\n    ref: ref,\n    className: classNames(className, bsPrefix),\n    ...props\n  });\n});\nCardLink.displayName = 'CardLink';\nexport default CardLink;", "\"use client\";\n\nimport * as React from 'react';\nimport classNames from 'classnames';\nimport { useBootstrapPrefix } from './ThemeProvider';\nimport divWithClassName from './divWithClassName';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst DivStyledAsH6 = divWithClassName('h6');\nconst CardSubtitle = /*#__PURE__*/React.forwardRef(({\n  className,\n  bsPrefix,\n  as: Component = DivStyledAsH6,\n  ...props\n}, ref) => {\n  bsPrefix = useBootstrapPrefix(bsPrefix, 'card-subtitle');\n  return /*#__PURE__*/_jsx(Component, {\n    ref: ref,\n    className: classNames(className, bsPrefix),\n    ...props\n  });\n});\nCardSubtitle.displayName = 'CardSubtitle';\nexport default CardSubtitle;", "\"use client\";\n\nimport * as React from 'react';\nimport classNames from 'classnames';\nimport { useBootstrapPrefix } from './ThemeProvider';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst CardText = /*#__PURE__*/React.forwardRef(({\n  className,\n  bsPrefix,\n  as: Component = 'p',\n  ...props\n}, ref) => {\n  bsPrefix = useBootstrapPrefix(bsPrefix, 'card-text');\n  return /*#__PURE__*/_jsx(Component, {\n    ref: ref,\n    className: classNames(className, bsPrefix),\n    ...props\n  });\n});\nCardText.displayName = 'CardText';\nexport default CardText;", "\"use client\";\n\nimport * as React from 'react';\nimport classNames from 'classnames';\nimport { useBootstrapPrefix } from './ThemeProvider';\nimport divWithClassName from './divWithClassName';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst DivStyledAsH5 = divWithClassName('h5');\nconst CardTitle = /*#__PURE__*/React.forwardRef(({\n  className,\n  bsPrefix,\n  as: Component = DivStyledAsH5,\n  ...props\n}, ref) => {\n  bsPrefix = useBootstrapPrefix(bsPrefix, 'card-title');\n  return /*#__PURE__*/_jsx(Component, {\n    ref: ref,\n    className: classNames(className, bsPrefix),\n    ...props\n  });\n});\nCardTitle.displayName = 'CardTitle';\nexport default CardTitle;", "\"use client\";\n\nimport classNames from 'classnames';\nimport * as React from 'react';\nimport { useBootstrapPrefix } from './ThemeProvider';\nimport CardBody from './CardBody';\nimport CardFooter from './CardFooter';\nimport CardHeader from './CardHeader';\nimport CardImg from './CardImg';\nimport CardImgOverlay from './CardImgOverlay';\nimport CardLink from './CardLink';\nimport CardSubtitle from './CardSubtitle';\nimport CardText from './CardText';\nimport CardTitle from './CardTitle';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst Card = /*#__PURE__*/React.forwardRef(({\n  bsPrefix,\n  className,\n  bg,\n  text,\n  border,\n  body = false,\n  children,\n  // Need to define the default \"as\" during prop destructuring to be compatible with styled-components github.com/react-bootstrap/react-bootstrap/issues/3595\n  as: Component = 'div',\n  ...props\n}, ref) => {\n  const prefix = useBootstrapPrefix(bsPrefix, 'card');\n  return /*#__PURE__*/_jsx(Component, {\n    ref: ref,\n    ...props,\n    className: classNames(className, prefix, bg && `bg-${bg}`, text && `text-${text}`, border && `border-${border}`),\n    children: body ? /*#__PURE__*/_jsx(CardBody, {\n      children: children\n    }) : children\n  });\n});\nCard.displayName = 'Card';\nexport default Object.assign(Card, {\n  Img: CardImg,\n  Title: CardTitle,\n  Subtitle: CardSubtitle,\n  Body: CardBody,\n  Link: CardLink,\n  Text: CardText,\n  Header: CardHeader,\n  Footer: CardFooter,\n  ImgOverlay: CardImgOverlay\n});", "import React from 'react';\r\nimport { useFormikContext, Field } from 'formik';\r\nimport { Form } from 'react-bootstrap';\r\n\r\ninterface RadioItemProps {\r\n  id: string;\r\n  label: string;\r\n  value: string;\r\n  name?: string;\r\n  disabled?: boolean;\r\n}\r\n\r\ninterface RadioGroupProps {\r\n  name: string;\r\n  valueSelected: string;\r\n  onChange: (e: React.ChangeEvent<HTMLInputElement>) => void;\r\n  errorMessage?: string;\r\n  children: React.ReactNode;\r\n}\r\n\r\n// Type Guard to ensure child.props is an object\r\nfunction isObject(props: any): props is { [key: string]: any } {\r\n  return typeof props === 'object' && props !== null;\r\n}\r\n\r\nconst RadioItem: React.FC<RadioItemProps> = ({ id, label, value, name, disabled }) => {\r\n  const { values, setFieldValue } = useFormikContext<any>();\r\n  const fieldName = name || id;\r\n\r\n  return (\r\n    <Form.Check\r\n      type=\"radio\"\r\n      id={id}\r\n      label={label}\r\n      value={value}\r\n      name={fieldName}\r\n      checked={values[fieldName] === value}\r\n      onChange={(e) => {\r\n        setFieldValue(fieldName, e.target.value);\r\n      }}\r\n      disabled={disabled}\r\n      inline\r\n    />\r\n  );\r\n};\r\n\r\nconst RadioGroup: React.FC<RadioGroupProps> = ({\r\n  name,\r\n  valueSelected,\r\n  onChange,\r\n  errorMessage,\r\n  children,\r\n}) => {\r\n  const { errors, touched } = useFormikContext<any>();\r\n  const hasError = touched[name] && errors[name];\r\n\r\n  // Create a context to pass the name to RadioItems\r\n  const radioContext = React.useMemo(() => ({ name }), [name]);\r\n\r\n  // Clone children to pass the name, ensuring props are spread safely\r\n  const childrenWithProps = React.Children.map(children, (child) => {\r\n    if (React.isValidElement(child)) {\r\n      // Ensure child.props is an object before spreading\r\n      if (isObject(child.props)) {\r\n        return React.cloneElement(child as React.ReactElement<RadioItemProps>, {\r\n          name,\r\n          ...child.props, // Safely spread child.props\r\n        });\r\n      }\r\n    }\r\n    return child;\r\n  });\r\n\r\n  return (\r\n    <div>\r\n      <div className=\"radio-group\">\r\n        {childrenWithProps}\r\n      </div>\r\n      {hasError && (\r\n        <div className=\"invalid-feedback d-block\">\r\n          {errorMessage || (typeof errors[name] === 'string' ? errors[name] : String(errors[name]))}\r\n        </div>\r\n      )}\r\n    </div>\r\n  );\r\n};\r\n\r\nexport const Radio = {\r\n  RadioGroup,\r\n  RadioItem,\r\n};\r\n\r\nexport default Radio;\r\n", "// This file exports all the components needed to replace react-bootstrap4-form-validation\r\n// It serves as a drop-in replacement for the original library\r\n\r\nimport ValidationForm from './ValidationFormWrapper';\r\nimport { TextInput, SelectGroup } from './FormikTextInput';\r\nimport { Radio } from './FormikRadio';\r\n\r\n// Export all components\r\nexport {\r\n  ValidationForm,\r\n  TextInput,\r\n  SelectGroup,\r\n  Radio\r\n};\r\n\r\n// Also export as default for convenience\r\nexport default {\r\n  ValidationForm,\r\n  TextInput,\r\n  SelectGroup,\r\n  Radio\r\n};\r\n", "import React, { forwardRef } from 'react';\r\nimport { Formik, Form, FormikProps, FormikHelpers } from 'formik';\r\nimport * as Yup from 'yup';\r\n\r\n// This is a wrapper component that replaces react-bootstrap4-form-validation with Formik\r\ninterface MockEvent {\r\n  preventDefault: () => void;\r\n  stopPropagation: () => void;\r\n  currentTarget: (EventTarget & Element) | null;\r\n  target: EventTarget | null;\r\n  nativeEvent: Event;\r\n  bubbles: boolean;\r\n  cancelable: boolean;\r\n  defaultPrevented: boolean;\r\n  eventPhase: number;\r\n  isTrusted: boolean;\r\n  timeStamp: number;\r\n  type: string;\r\n  isDefaultPrevented: () => boolean;\r\n  isPropagationStopped: () => boolean;\r\n  persist: () => void;\r\n}\r\n\r\ninterface ValidationFormWrapperProps {\r\n  children: React.ReactNode | ((formikProps: FormikProps<any>) => React.ReactNode);\r\n  onSubmit: (event: React.FormEvent | MockEvent, values?: Record<string, any>, actions?: FormikHelpers<Record<string, any>>) => void;\r\n  onErrorSubmit?: (errors: any) => void;\r\n  initialValues?: Record<string, any>;\r\n  enableReinitialize?: boolean;\r\n  autoComplete?: string;\r\n  className?: string;\r\n  onKeyPress?: (e: any) => void;\r\n}\r\n\r\nconst ValidationFormWrapper = forwardRef<HTMLFormElement, ValidationFormWrapperProps>((props, ref) => {\r\n  const { children, onSubmit, autoComplete, className, onKeyPress, initialValues, ...rest } = props;\r\n\r\n  // Create an empty validation schema by default\r\n  const validationSchema = Yup.object().shape({});\r\n\r\n  return (\r\n    <Formik\r\n      initialValues={initialValues || {}}\r\n      validationSchema={validationSchema}\r\n      onSubmit={(values: Record<string, any>, actions: FormikHelpers<Record<string, any>>) => {\r\n        // Create a mock event object with preventDefault method and currentTarget\r\n        const mockEvent: MockEvent = {\r\n          preventDefault: () => {},\r\n          stopPropagation: () => {},\r\n          currentTarget: null, // Set to null to avoid checkValidity errors\r\n          target: null,\r\n          nativeEvent: new Event('submit'),\r\n          bubbles: false,\r\n          cancelable: true,\r\n          defaultPrevented: false,\r\n          eventPhase: 0,\r\n          isTrusted: false,\r\n          timeStamp: Date.now(),\r\n          type: 'submit',\r\n          isDefaultPrevented: () => false,\r\n          isPropagationStopped: () => false,\r\n          persist: () => {}\r\n        };\r\n\r\n        if (onSubmit) {\r\n          // Pass the mock event object to maintain compatibility with the original code\r\n          onSubmit(mockEvent, values, actions);\r\n        }\r\n      }}\r\n      {...rest}\r\n    >\r\n      {(formikProps: FormikProps<any>) => (\r\n        <Form\r\n          ref={ref}\r\n          onSubmit={formikProps.handleSubmit}\r\n          autoComplete={autoComplete}\r\n          className={className}\r\n          onKeyPress={onKeyPress}\r\n        >\r\n          {typeof children === 'function' ? children(formikProps) : children}\r\n        </Form>\r\n      )}\r\n    </Formik>\r\n  );\r\n});\r\n\r\nValidationFormWrapper.displayName = 'ValidationFormWrapper';\r\n\r\nexport default ValidationFormWrapper;\r\n", "import React from 'react';\r\nimport { Form } from 'react-bootstrap';\r\nimport { Field, ErrorMessage, useField } from 'formik';\r\n\r\n// This component mimics the TextInput component from react-bootstrap4-form-validation\r\n// but uses Formik under the hood\r\nexport const TextInput = ({\r\n  name,\r\n  id,\r\n  required,\r\n  validator,\r\n  errorMessage,\r\n  onChange,\r\n  value,\r\n  as,\r\n  multiline,\r\n  rows,\r\n  pattern,\r\n  ...props\r\n}: any) => {\r\n  // Create a custom validation function that mimics the original validator\r\n  const validate = (val: any) => {\r\n    // Safely convert value to string and trim\r\n    const stringVal = typeof val === 'string' ? val : String(val || '');\r\n    if (required && (!val || stringVal.trim() === '')) {\r\n      return errorMessage?.validator || 'This field is required';\r\n    }\r\n\r\n    if (validator && !validator(val)) {\r\n      return errorMessage?.validator || 'Invalid value';\r\n    }\r\n\r\n    if (pattern && val) {\r\n      const regex = new RegExp(pattern);\r\n      if (!regex.test(val)) {\r\n        return errorMessage?.pattern || 'Invalid format';\r\n      }\r\n    }\r\n\r\n    return undefined;\r\n  };\r\n\r\n  return (\r\n    <Field name={name} validate={validate}>\r\n      {({ field, meta }: any) => (\r\n        <>\r\n          <Form.Control\r\n            {...field}\r\n            {...props}\r\n            id={id}\r\n            as={as || 'input'}\r\n            rows={rows}\r\n            isInvalid={meta.touched && !!meta.error}\r\n            onChange={(e: React.ChangeEvent<HTMLInputElement>) => {\r\n              field.onChange(e);\r\n              if (onChange) onChange(e);\r\n            }}\r\n            value={value !== undefined ? value : field.value}\r\n          />\r\n          {meta.touched && meta.error ? (\r\n            <Form.Control.Feedback type=\"invalid\">\r\n              {meta.error}\r\n            </Form.Control.Feedback>\r\n          ) : null}\r\n        </>\r\n      )}\r\n    </Field>\r\n  );\r\n};\r\n\r\n// This component mimics the SelectGroup component from react-bootstrap4-form-validation\r\n// but uses Formik under the hood\r\nexport const SelectGroup = ({\r\n  name,\r\n  id,\r\n  required,\r\n  errorMessage,\r\n  onChange,\r\n  value,\r\n  children,\r\n  ...props\r\n}: any) => {\r\n  // Create a custom validation function that mimics the original validator\r\n  const validate = (val: any) => {\r\n    if (required && (!val || val === '')) {\r\n      return errorMessage?.validator || 'This field is required';\r\n    }\r\n\r\n    return undefined;\r\n  };\r\n\r\n  return (\r\n    <Field name={name} validate={validate}>\r\n      {({ field, meta }: any) => (\r\n        <>\r\n          <Form.Control\r\n            as=\"select\"\r\n            {...field}\r\n            {...props}\r\n            id={id}\r\n            isInvalid={meta.touched && !!meta.error}\r\n            onChange={(e: React.ChangeEvent<HTMLSelectElement>) => {\r\n              field.onChange(e);\r\n              if (onChange) onChange(e);\r\n            }}\r\n            value={value !== undefined ? value : field.value}\r\n          >\r\n            {children}\r\n          </Form.Control>\r\n          {meta.touched && meta.error ? (\r\n            <Form.Control.Feedback type=\"invalid\">\r\n              {meta.error}\r\n            </Form.Control.Feedback>\r\n          ) : null}\r\n        </>\r\n      )}\r\n    </Field>\r\n  );\r\n};\r\n\r\n// Export both components as named exports and as a default object\r\nexport default {\r\n  TextInput,\r\n  SelectGroup\r\n};\r\n", "\"use client\";\n\nimport * as React from 'react';\nconst context = /*#__PURE__*/React.createContext(null);\ncontext.displayName = 'CardHeaderContext';\nexport default context;", "\n    (window.__NEXT_P = window.__NEXT_P || []).push([\n      \"/adminsettings/syndrome/form\",\n      function () {\n        return require(\"private-next-pages/adminsettings/syndrome/form.tsx\");\n      }\n    ]);\n    if(module.hot) {\n      module.hot.dispose(function () {\n        window.__NEXT_P.push([\"/adminsettings/syndrome/form\"])\n      });\n    }\n  "], "names": ["value", "onChange", "SimpleRichTextEditor", "placeholder", "height", "disabled", "editor<PERSON><PERSON>", "useRef", "isFocused", "setIsFocused", "useState", "useEffect", "current", "innerHTML", "handleInput", "execCommand", "command", "document", "focus", "div", "className", "style", "border", "padding", "borderBottom", "background", "button", "type", "onClick", "margin", "strong", "em", "u", "url", "prompt", "ref", "contentEditable", "onInput", "onFocus", "onBlur", "minHeight", "maxHeight", "overflow", "outline", "data-placeholder", "suppressContentEditableWarning", "EditorComponent", "initContent", "props", "content", "_initialSyndrome", "title", "code", "SyndromeForm", "t", "useTranslation", "initialVal", "setInitialVal", "editform", "routes", "handleSubmit", "event", "values", "response", "toastMsg", "preventDefault", "obj", "trim", "description", "apiService", "patch", "post", "_id", "toast", "success", "Router", "error", "handleChange", "e", "target", "name", "prevState", "handleDescription", "syndromeParams", "query", "sort", "limit", "getSyndromeData", "get", "formRef", "Container", "fluid", "Card", "marginTop", "boxShadow", "ValidationFormWrapper", "onSubmit", "initialValues", "enableReinitialize", "Body", "Row", "Col", "Title", "hr", "md", "lg", "sm", "Form", "Group", "Label", "TextInput", "id", "required", "validator", "errorMessage", "evt", "<PERSON><PERSON>", "variant", "re<PERSON><PERSON><PERSON><PERSON>", "window", "scrollTo", "Link", "href", "as", "CardBody", "React", "bsPrefix", "Component", "useBootstrapPrefix", "_jsx", "classNames", "displayName", "<PERSON><PERSON><PERSON>er", "<PERSON><PERSON><PERSON><PERSON>", "prefix", "contextValue", "useMemo", "cardHeaderBsPrefix", "CardHeaderContext", "Provider", "children", "CardImg", "CardImgOverlay", "CardLink", "DivStyledAsH6", "divWithClassName", "CardSubtitle", "CardText", "DivStyledAsH5", "CardTitle", "bg", "text", "body", "Object", "assign", "Img", "Subtitle", "Text", "Header", "Footer", "ImgOverlay", "Radio", "RadioGroup", "valueSelected", "errors", "touched", "useFormikContext", "<PERSON><PERSON><PERSON><PERSON>", "childrenWithProps", "map", "child", "isObject", "String", "RadioItem", "label", "setFieldValue", "fieldName", "Check", "checked", "inline", "ValidationForm", "SelectGroup", "forwardRef", "autoComplete", "onKeyPress", "rest", "validationSchema", "<PERSON><PERSON>", "shape", "<PERSON><PERSON>", "actions", "mockEvent", "stopPropagation", "currentTarget", "nativeEvent", "Event", "bubbles", "cancelable", "defaultPrevented", "eventPhase", "isTrusted", "timeStamp", "Date", "now", "isDefaultPrevented", "isPropagationStopped", "persist", "formikProps", "multiline", "rows", "pattern", "Field", "validate", "stringVal", "val", "RegExp", "test", "field", "meta", "Control", "isInvalid", "undefined", "<PERSON><PERSON><PERSON>", "context"], "sourceRoot": "", "ignoreList": [3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 17]}