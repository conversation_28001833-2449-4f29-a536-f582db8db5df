"use strict";(()=>{var e={};e.id=9243,e.ids=[636,3220,9243],e.modules={123:e=>{e.exports=require("dom-helpers/removeEventListener")},1332:e=>{e.exports=require("react-custom-scrollbars-2")},1428:e=>{e.exports=import("axios")},1680:e=>{e.exports=require("@restart/ui/utils")},1919:e=>{e.exports=require("react-transition-group/Transition")},3892:e=>{e.exports=require("classnames")},4048:e=>{e.exports=require("react-truncate")},6009:e=>{e.exports=require("dom-helpers/ownerDocument")},6952:e=>{e.exports=require("@restart/ui/Modal")},7374:e=>{e.exports=require("@restart/ui/Dropdown")},8732:e=>{e.exports=require("react/jsx-runtime")},9653:e=>{e.exports=require("@restart/ui/Overlay")},11e3:e=>{e.exports=require("react-multi-select-component")},11242:e=>{e.exports=require("redux-persist/integration/react")},11688:e=>{e.exports=require("@restart/hooks/useWillUnmount")},12388:e=>{e.exports=require("dom-helpers/addEventListener")},13364:e=>{e.exports=require("redux-saga/effects")},13408:e=>{e.exports=require("@restart/hooks/useUpdateEffect")},14062:e=>{e.exports=import("react-redux")},14078:e=>{e.exports=import("swr")},14332:e=>{e.exports=require("uncontrollable")},16029:e=>{e.exports=require("react-dropzone")},16116:e=>{e.exports=require("invariant")},16444:e=>{e.exports=require("moment/locale/fr")},18622:e=>{e.exports=require("yup")},19121:e=>{e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},20479:(e,r,t)=>{t.a(e,async(e,s)=>{try{t.r(r),t.d(r,{config:()=>h,default:()=>x,getServerSideProps:()=>q,getStaticPaths:()=>m,getStaticProps:()=>d,reportWebVitals:()=>g,routeModule:()=>w,unstable_getServerProps:()=>S,unstable_getServerSideProps:()=>y,unstable_getStaticParams:()=>f,unstable_getStaticPaths:()=>b,unstable_getStaticProps:()=>v});var o=t(63885),a=t(80237),i=t(81413),u=t(9616),p=t.n(u),n=t(72386),c=t(52295),l=e([n,c]);[n,c]=l.then?(await l)():l;let x=(0,i.M)(c,"default"),d=(0,i.M)(c,"getStaticProps"),m=(0,i.M)(c,"getStaticPaths"),q=(0,i.M)(c,"getServerSideProps"),h=(0,i.M)(c,"config"),g=(0,i.M)(c,"reportWebVitals"),v=(0,i.M)(c,"unstable_getStaticProps"),b=(0,i.M)(c,"unstable_getStaticPaths"),f=(0,i.M)(c,"unstable_getStaticParams"),S=(0,i.M)(c,"unstable_getServerProps"),y=(0,i.M)(c,"unstable_getServerSideProps"),w=new o.PagesRouteModule({definition:{kind:a.A.PAGES,page:"/vspace/[...routes]",pathname:"/vspace/[...routes]",bundlePath:"",filename:""},components:{App:n.default,Document:p()},userland:c});s()}catch(e){s(e)}})},22326:e=>{e.exports=require("react-dom")},22541:e=>{e.exports=require("redux-persist/lib/storage")},25303:e=>{e.exports=require("@restart/ui/NavItem")},26324:e=>{e.exports=require("warning")},27825:e=>{e.exports=require("lodash")},27910:e=>{e.exports=require("stream")},28217:e=>{e.exports=require("@restart/ui/DropdownItem")},29021:e=>{e.exports=require("fs")},29294:e=>{e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},29628:e=>{e.exports=require("dom-helpers/addClass")},29780:e=>{e.exports=require("react-datepicker")},29825:e=>{e.exports=require("prop-types")},29841:e=>{e.exports=require("dom-helpers/hasClass")},30362:e=>{e.exports=require("@restart/ui/DropdownToggle")},33852:(e,r,t)=>{t.a(e,async(e,s)=>{try{t.r(r),t.d(r,{default:()=>g});var o=t(8732),a=t(44233),i=t.n(a),u=t(83551),p=t(49481),n=t(91353),c=t(42893),l=t(27825),x=t.n(l),d=t(82015),m=t(63487),q=t(88751),h=e([c,m]);[c,m]=h.then?(await h)():h;let g=e=>{let[r,t]=(0,d.useState)(!1),[s,l]=(0,d.useState)({title:"",description:"",startDate:null,endDate:null,searchData:"",visibility:!0,images:[],checked:!1,file_category:"",nonMembers:[],images_src:[],members:[],doc_src:[],document:[]}),[h,g]=(0,d.useState)(""),{t:v}=(0,q.useTranslation)("common"),b=(0,a.useRouter)().query.routes||[];(0,d.useEffect)(()=>{f()},[]);let f=async()=>{let e=await m.A.get(`/vspace/${b[1]}`),r=await m.A.get(`/users/${b[3]}`);e&&(t(e.visibility),l(e)),r&&g(r.username)},S=async()=>{s.visibility=!0;let e=x().find(s.subscribers,{_id:b[3]});null==e&&s.subscribers.push(b[3]);let r=await m.A.post(`/vspace/acceptSubscriptionRequest/${s._id}`,s);r&&r._id&&(t(!0),c.default.success(v("Vspaceissuccessfullyaccepted")),i().push("/vspace")),"Not authorized"==r&&c.default.error(v("Youarenotauthorized"))};return(0,o.jsx)("div",{children:(0,o.jsx)(u.A,{className:"my-4",children:(0,o.jsxs)(p.A,{children:[(0,o.jsx)("div",{children:"Welcome to Robert Koch Institut !"}),(0,o.jsx)("b",{children:h})," has requested to access your private virtual space ",(0,o.jsx)("b",{children:s.title}),(0,o.jsx)("br",{}),(0,o.jsx)(n.A,{disabled:r,className:"me-2",type:"submit",variant:"primary",onClick:S,children:v("Accept")}),(0,o.jsx)(n.A,{disabled:r,className:"me-2",variant:"info",onClick:()=>{t(!0),c.default.error(v("VspaceissuccessfullyDeclined")),i().push("/vspace")},children:v("Decline")})]})})})};s()}catch(e){s(e)}})},33873:e=>{e.exports=require("path")},35576:e=>{e.exports=require("next-i18next/serverSideTranslations")},36653:e=>{e.exports=require("nprogress")},36955:e=>{e.exports=require("@restart/hooks/useBreakpoint")},37766:e=>{e.exports=require("@restart/hooks/useMergedRefs")},38609:e=>{e.exports=require("react-data-table-component")},39756:e=>{e.exports=import("redux")},40051:e=>{e.exports=require("dom-helpers/removeClass")},40361:e=>{e.exports=require("next/dist/compiled/next-server/pages.runtime.prod.js")},42051:e=>{e.exports=require("@restart/hooks/useCommittedRef")},42738:e=>{e.exports=import("react-select/creatable")},42893:e=>{e.exports=import("react-hot-toast")},43294:e=>{e.exports=require("formik")},50009:e=>{e.exports=require("@restart/ui/DropdownMenu")},52295:(e,r,t)=>{t.a(e,async(e,s)=>{try{t.r(r),t.d(r,{default:()=>m,getServerSideProps:()=>d});var o=t(8732),a=t(44233),i=t(51821),u=t(88706),p=t(45400),n=t(87075),c=t(33852),l=t(35576),x=e([i,u,n,c]);async function d({locale:e}){return{props:{...await (0,l.serverSideTranslations)(e,["common"])}}}[i,u,n,c]=x.then?(await x)():x;let m=()=>{let e=(0,a.useRouter)().query.routes||[],r=(0,p.canAddVspaceForm)(()=>(0,o.jsx)(i.default,{routes:e}));switch(e[0]){case"create":return(0,o.jsx)(r,{});case"edit":return(0,o.jsx)(i.default,{routes:e});case"show":return(0,o.jsx)(u.default,{routes:e});case"manage":return(0,o.jsx)(n.default,{routes:e});case"acceptvspace":return(0,o.jsx)(c.default,{routes:e});default:return null}};s()}catch(e){s(e)}})},54131:e=>{e.exports=import("@fortawesome/free-solid-svg-icons")},57664:e=>{e.exports=require("dom-helpers/scrollbarSize")},58928:e=>{e.exports=require("dom-helpers/canUseDOM")},59717:e=>{e.exports=require("@restart/ui/DropdownContext")},59984:e=>{e.exports=import("react-select/animated")},60560:e=>{e.exports=require("dom-helpers/contains")},63033:e=>{e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},64801:e=>{e.exports=require("react-big-calendar")},65447:e=>{e.exports=require("@restart/ui/Button")},67364:e=>{e.exports=require("@restart/hooks/useCallbackRef")},68455:e=>{e.exports=import("redux-saga")},69722:e=>{e.exports=require("es6-promise")},74075:e=>{e.exports=require("zlib")},74716:e=>{e.exports=require("moment")},74987:e=>{e.exports=require("@restart/ui/ModalManager")},78097:e=>{e.exports=require("next-redux-saga")},78634:e=>{e.exports=require("@restart/ui/Anchor")},81149:e=>{e.exports=require("react-responsive-carousel")},81366:e=>{e.exports=require("redux-auth-wrapper/connectedAuthWrapper")},81521:e=>{e.exports=require("dom-helpers/querySelectorAll")},81895:e=>{e.exports=require("@restart/hooks/useEventCallback")},82015:e=>{e.exports=require("react")},82053:e=>{e.exports=require("@fortawesome/react-fontawesome")},86842:e=>{e.exports=require("@restart/ui/SelectableContext")},86843:e=>{e.exports=require("moment/locale/de")},87571:e=>{e.exports=require("dom-helpers/transitionEnd")},88751:e=>{e.exports=require("next-i18next")},93787:e=>{e.exports=require("redux-persist")},94947:e=>{e.exports=require("@restart/hooks/useTimeout")},96196:e=>{e.exports=require("next-redux-wrapper")},98320:e=>{e.exports=require("@restart/hooks/useIsomorphicEffect")},99460:e=>{e.exports=require("dom-helpers/css")},99800:e=>{e.exports=import("react-select")}};var r=require("../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[6089,9216,9616,4033,2386,2491,7136,3245,1821,8706,7075],()=>t(20479));module.exports=s})();