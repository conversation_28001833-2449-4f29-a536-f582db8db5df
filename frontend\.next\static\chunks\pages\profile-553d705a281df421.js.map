{"version": 3, "file": "static/chunks/pages/profile-553d705a281df421.js", "mappings": "+YAiLA,MAtKoB,OAAC,QAAEA,CAAM,IAsKdC,UAtKgBC,CAAY,EAsKhB,KAtKkBC,CAAK,CAAM,GAChD,GAAEC,CAAC,CAAE,CAAGC,CAAAA,EAAAA,EAAAA,EAAAA,CAAcA,CAAC,UACvBC,EAAOF,EAAE,sBAET,CAACG,EAAOC,EAAS,CAAGC,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAC,GAC7B,CAACC,EAAQC,EAAU,CAAGF,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAC,GAC/B,CAACG,EAAMC,EAAQ,CAAGJ,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAC,IAC3B,CAACK,EAAKC,EAAO,CAAGN,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAgB,MACxCO,EAAYC,CAAAA,EAAAA,EAAAA,MAAAA,CAAMA,CAAM,MAExBC,EAAc,UAmBlB,IAAMC,EAAOC,CAjBS,IACtB,IAAMC,EAAMC,EAAQC,CAgBOC,IAhBF,CAAC,KACnBC,EAAYJ,CAAG,CAAC,EAAE,CAACK,KAAK,CAAC,WACzBC,EAAOF,EAAYA,CAAS,CAAC,EAAE,CAAG,GAClCG,EAAOC,KAAKR,CAAG,CAAC,EAAE,EACpBS,EAAIF,EAAKG,MAAM,CACbC,EAAQ,IAAIC,WAAWH,GAC5B,KAAOA,IAAK,CACVE,CAAK,CAACF,EAAE,CAAGF,EAAKM,UAAU,CAACJ,GAE7B,OAAO,IAAIK,KAAK,CAACH,EAAM,CAAE,CAAEI,KAAMT,CAAK,GACxC,EAGeX,EAAUqB,OAAO,CAC7BC,sBAAsB,GACtBC,SAAS,CAAC,aAAc,KAErBC,EAAK,IAAIC,SACfD,EAAGE,MAAM,CAAC,OAAQvB,EAAMP,GAExB,GAAI,CACF,IAAM+B,EAAM,MAAMC,EAAAA,CAAUA,CAACC,IAAI,CAAC,SAAUL,EAAI,CAC9C,eAAgB,qBAClB,GAEIG,GAAOA,EAAIG,GAAG,EAAE,MACKF,EAAAA,CAAUA,CAACC,IAAI,CAAC,uBAAwB,CAC7D1C,MAAOwC,EAAIG,GAAG,IAGdC,EAAAA,EAAKA,CAACC,OAAO,CAAC5C,EAAE,sCAItB,CAAE,MAAO6C,EAAgB,CACvB,MAAMA,aAAiBC,MAAQD,EAAYC,MAAM,yBACnD,CACAhD,EAAa,IACba,EAAO,MACPF,EAAQP,GACRE,EAAS,EACX,EAWA,MACE,+BACE,UAAC2C,MAAAA,UACC,WAACC,EAAAA,CAAKA,CAAAA,CACJC,KAAMrD,EACNsD,KAAK,KACLC,kBAAgB,cAChBC,OAAQ,IAAMtD,GAAa,GAC3BuD,QAAQ,cAER,UAACL,EAAAA,CAAKA,CAACM,MAAM,WACX,UAACN,EAAAA,CAAKA,CAACO,KAAK,EAACC,GAAG,yCACfxD,EAAE,6BAGL,WAACgD,EAAAA,CAAKA,CAACS,IAAI,YACT,UAACV,MAAAA,CAAIW,UAAU,wEACb,UAACC,IAAYA,CACXC,IAAKhD,EACLiD,IAFWF,SAEG,IACdxD,MAAOA,EACPG,OAAQA,EACRwD,MAAO,CAAC,EAAG,EAAG,EAAG,GAAI,CACrB/D,MAAOW,GAAY,GAANA,0BAIjB,UAACqC,MAAAA,CAAIW,UAAU,qBACb,WAACK,EAAAA,CAAGA,CAAAA,CAACL,UAAU,oCACb,UAACM,EAAAA,CAAGA,CAAAA,CAACC,GAAI,EAAGC,GAAI,EAAGC,GAAI,EAAGT,UAAU,gBACpC,UAACU,IAAAA,UAAGpE,EAAE,4BAEN,UAACgE,EAAAA,CAAGA,CAAAA,CAACC,GAAI,GAAIC,GAAI,GAAIC,GAAI,YACvB,WAACpB,MAAAA,CAAIW,UAAU,uDACb,UAACW,QAAAA,CACCrC,KAAK,OACLxB,KAAK,QACLkD,UAAU,oDACVY,OAAO,UACPd,GAAG,aACHe,SAhDCC,CAgDSC,GA/CxBD,EAAEE,MAAM,CAACC,KAAK,EAAIH,EAAEE,MAAM,CAACC,KAAK,CAAC,EAAE,EAAE,CACvClE,EAAQ+D,EAAEE,MAAM,CAACC,KAAK,CAAC,EAAE,CAACnE,IAAI,EAC9BG,EAAOiE,IAAIC,eAAe,CAACL,EAAEE,MAAM,CAACC,KAAK,CAAC,EAAE,GAEhD,IA6CkB,UAACG,QAAAA,CAAMpB,UAAU,6CAA8CqB,cAAe/E,EAAE,mBAAYA,EAAE,kCAMtG,UAAC+C,MAAAA,CAAIW,UAAU,qBACb,WAACK,EAAAA,CAAGA,CAAAA,WACF,UAACC,EAAAA,CAAGA,CAAAA,CAACC,GAAI,EAAGC,GAAI,EAAGC,GAAI,EAAGT,UAAU,gBACpC,UAACU,IAAAA,UAAGpE,EAAE,oBAEN,UAACgE,EAAAA,CAAGA,CAAAA,CAACC,GAAI,EAAGC,GAAI,EAAGC,GAAI,WACrB,UAACa,IAAWA,CACVC,MAAO9E,EACP+E,QAAQ,OACRC,IAAK,EACLC,IAAK,GACLC,GALUL,EAKJ,GACNM,QAAQ,UACRf,SAAU,GACRnE,EAASmF,OAAOC,EAAYd,MAAM,CAACO,KAAK,OAI9C,UAACjB,EAAAA,CAAGA,CAAAA,CAACC,GAAI,EAAGC,GAAI,EAAGC,GAAI,EAAGT,UAAU,gBACpC,UAACU,IAAAA,UAAGpE,EAAE,sBAEN,UAACgE,EAAAA,CAAGA,CAAAA,CAACC,GAAI,EAAGC,GAAI,EAAGC,GAAI,WACrB,UAACa,IAAWA,CACVC,MAAO3E,EACP4E,QAAQ,OACRO,aAAc,GAHJT,GAGsC,OAAbU,EAAa,QAChDP,IAAK,EACLC,IAAK,IACLE,QAAQ,UACRf,SAAU,GACRhE,EAAUgF,OAAOC,EAAYd,MAAM,CAACO,KAAK,eAOrD,WAACjC,EAAAA,CAAKA,CAAC2C,MAAM,YACb,UAACC,EAAAA,CAAMA,CAAAA,CAACC,QAAS/E,WAAcd,EAAE,yBAC/B,UAAC4F,EAAAA,CAAMA,CAAAA,CAACN,QAAQ,SAASO,QAAS,KAChC/F,GAAa,GACba,EAAO,KACP,WAEDX,EAAE,qBAOf,sBCuHA,MAAe8F,CAAAA,EAAAA,EAAAA,EAAAA,CAAOA,GAxQN,IACd,GAAM,CAuQyBC,EAvQvB/F,CAAC,CAAE,CAAGC,CAAAA,EAAAA,EAAAA,EAAAA,CAAcA,CAAC,UACvB,CAACgD,EAAM+C,EAAQ,CAAQ3F,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,EAAC,GAChC,CAAC4F,EAAaC,EAAe,CAAG7F,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,EAAC,GACzC,CAAC8F,EAAOC,EAAS,CAAG/F,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,EAAC,GAI7BgG,EAAe,IACnBL,EAAQM,GACRJ,GAAe,EACjB,EAYM,CAACK,EAAUC,EAAY,CAAQnG,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAACoG,CAT5CC,SAAU,GACVC,YAAa,GACbC,MAAO,GACP7G,MAAO,KACP8G,UAAW,GACXC,SAAU,GACVC,SAAU,EACZ,GAIAC,CAAAA,EAAAA,EAAAA,SAAAA,CAASA,CAAC,KAWRC,CAVgB,MAAOC,IACrB,IAAMC,EAAW,MAAM3E,EAAAA,CAAUA,CAACC,IAAI,CAAC,uBAAwByE,GAC3DC,IACFA,EAASpH,IADG,CACE,CACZoH,EAASpH,KAAK,EAAIoH,EAASpH,KAAK,CAAC2C,GAAG,CAChC,GAAwCyE,MAAAA,CAArCC,8BAAsB,CAAC,gBAAiC,OAAnBD,EAASpH,KAAK,CAAC2C,GAAG,EAC1D,yBACN8D,EAAY,GAAqB,EAAE,GAAGa,CAAS,CAAE,EAAhB,CAAmBF,CAAQ,CAAC,IAEjE,EACQ,CAAC,EACX,EAAG,CAAClE,EAAMkD,EAAM,EAOhB,IAAMmB,EAAa,IACjBC,EAAMC,QAAQ,CAACC,CAAAA,EAAAA,EAAAA,EAAAA,CAAoBA,IACnCrB,EAASE,EACX,EAGA,MACE,WAACoB,EAAAA,CAASA,CAAAA,CAACC,KAAK,IAACjE,UAAU,gBACzB,UAACK,EAAAA,CAAGA,CAAAA,UACF,UAACC,EAAAA,CAAGA,CAAAA,CAAC4D,GAAI,YACP,UAACC,EAAAA,CAAWA,CAAAA,CAACC,MAAO9H,EAAE,2BAG1B,UAAC+D,EAAAA,CAAGA,CAAAA,UACF,UAACC,EAAAA,CAAGA,CAAAA,CAAC4D,EAAE,IAACzD,GAAI,YACV,UAACuD,EAAAA,CAASA,CAAAA,CAACC,KAAK,aACd,UAAC5D,EAAAA,CAAGA,CAAAA,UACF,UAACC,EAAAA,CAAGA,CAAAA,UACF,WAAC+D,EAAAA,CAAIA,CAAAA,CAACrE,UAAU,8CACd,WAACX,MAAAA,CAAIW,UAAU,0BACb,UAACX,MAAAA,CAAIW,UAAU,yCACZ6C,EAASxG,KAAK,CACb,+BACE,UAACW,MAAAA,CACCgD,UAAU,WACVsE,IAuLjBzB,CAtLmB0B,EAsLP1B,EAASxG,KAAK,CAAGwG,EAASxG,KAAK,CAAG,OAjLjC,UAACmI,EAAAA,CAAOA,CAAAA,CACNxE,UAAU,kBACVyE,UAAU,OACV7C,QAAQ,WAId,UAACvC,MAAAA,CAAIW,UAAU,0BACb,UAAC0E,EAAAA,CAAIA,CAAAA,UACH,WAACrF,MAAAA,WACC,WAACqF,EAAAA,CAAIA,CAACC,KAAK,EACTC,GAAIvE,EAAAA,CAAGA,CACPwE,UAAU,WACV7E,UAAU,oCAEV,UAAC0E,EAAAA,CAAIA,CAACI,KAAK,EAACC,MAAM,IAACvE,GAAG,IAAI0D,GAAG,IAAIzD,GAAG,IAAIT,UAAU,gBAC/C1D,EAAE,sBAEL,UAACgE,EAAAA,CAAGA,CAAAA,CAAC4D,GAAG,aACN,UAACc,OAAAA,UAAK,QAER,UAAC1E,EAAAA,CAAGA,CAAAA,CAACE,GAAG,IAAI0D,GAAG,IAAIzD,GAAG,aACpB,UAACiE,EAAAA,CAAIA,CAACO,OAAO,EACXC,SAAS,IACTC,QAAQ,IACRC,aAAcvC,EAASG,QAAQ,QAKrC,WAAC0B,EAAAA,CAAIA,CAACC,KAAK,EACTC,GAAIvE,EAAAA,CAAGA,CACPwE,UAAU,OACV7E,UAAU,oCAEV,UAAC0E,EAAAA,CAAIA,CAACI,KAAK,EAACC,MAAM,IAACvE,GAAG,IAAI0D,GAAG,IAAIzD,GAAG,IAAIT,UAAU,gBAC/C1D,EAAE,kBAEL,UAACgE,EAAAA,CAAGA,CAAAA,CAAC4D,GAAG,aACN,UAACc,OAAAA,UAAK,QAER,UAAC1E,EAAAA,CAAGA,CAAAA,CAACE,GAAG,IAAI0D,GAAG,IAAIzD,GAAG,aACpB,UAACiE,EAAAA,CAAIA,CAACO,OAAO,EACXC,SAAS,IACTC,QAAQ,IACRC,aAAcvC,GAAYA,EAASM,SAAS,CAAG,GAAyBN,MAAAA,CAAtBA,EAASM,SAAS,CAAC,KAAqB,OAAlBN,EAASO,QAAQ,EAAK,UAIpG,WAACsB,EAAAA,CAAIA,CAACC,KAAK,EACTC,GAAIvE,EAAAA,CAAGA,CACPwE,UAAU,WACV7E,UAAU,oCAEV,UAAC0E,EAAAA,CAAIA,CAACI,KAAK,EAACC,MAAM,IAACvE,GAAG,IAAI0D,GAAG,IAAIzD,GAAG,IAAIT,UAAU,gBAC/C1D,EAAE,sBAEL,UAACgE,EAAAA,CAAGA,CAAAA,CAAC4D,GAAG,aACN,UAACc,OAAAA,UAAK,QAER,UAAC1E,EAAAA,CAAGA,CAAAA,CAACE,GAAG,IAAI0D,GAAG,IAAIzD,GAAG,aACpB,UAACiE,EAAAA,CAAIA,CAACO,OAAO,EACXC,SAAS,IACTC,QAAQ,IACRC,aAAcvC,GAAYA,EAASQ,QAAQ,CAAGR,EAASQ,QAAQ,CAAG,UAKxE,WAACqB,EAAAA,CAAIA,CAACC,KAAK,EACTC,GAAIvE,EAAAA,CAAGA,CACPwE,UAAU,eACV7E,UAAU,oCAEV,UAAC0E,EAAAA,CAAIA,CAACI,KAAK,EAACC,MAAM,IAACvE,GAAG,IAAI0D,GAAG,IAAIzD,GAAG,IAAIT,UAAU,gBAC/C1D,EAAE,0BAEL,UAACgE,EAAAA,CAAGA,CAAAA,CAAC4D,GAAG,aACN,UAACc,OAAAA,UAAK,QAER,UAAC1E,EAAAA,CAAGA,CAAAA,CAACE,GAAG,IAAI0D,GAAG,IAAIzD,GAAG,aACpB,UAACiE,EAAAA,CAAIA,CAACO,OAAO,EACXE,QAAQ,IACRD,SAAS,IACTE,aACEvC,EAASI,WAAW,EACpBJ,EAASI,WAAW,CAACmB,KAAK,QAKlC,UAACjI,EAAWA,CACVE,MAAOwG,EAASxG,KAAK,CACrBH,CAFUC,MAEFsG,EACRrG,aAAc,GAAcwH,EAAWhB,KAEzC,WAAC8B,EAAAA,CAAIA,CAACC,KAAK,EACTC,GAAIvE,EAAAA,CAAGA,CACPwE,UAAU,QACV7E,UAAU,oCAEV,UAAC0E,EAAAA,CAAIA,CAACI,KAAK,EAACC,MAAM,IAACvE,GAAG,IAAI0D,GAAG,IAAIzD,GAAG,IAAIT,UAAU,gBAC/C1D,EAAE,mBAEL,UAACgE,EAAAA,CAAGA,CAAAA,CAAC4D,GAAG,aACN,UAACc,OAAAA,UAAK,QAER,UAAC1E,EAAAA,CAAGA,CAAAA,CAACE,GAAG,IAAI0D,GAAG,IAAIzD,GAAG,aACpB,UAACiE,EAAAA,CAAIA,CAACO,OAAO,EACXC,SAAS,IACTC,QAAQ,IACRC,aAAcvC,EAASK,KAAK,QAIlC,WAACwB,EAAAA,CAAIA,CAACC,KAAK,EACTC,GAAIvE,EAAAA,CAAGA,CACPwE,UAAU,UACV7E,UAAU,oCAEV,UAAC0E,EAAAA,CAAIA,CAACI,KAAK,EAACC,MAAM,IAACvE,GAAG,IAAI0D,GAAG,IAAIzD,GAAG,IAAIT,UAAU,gBACjD1D,EAAE,qBAEH,UAACgE,EAAAA,CAAGA,CAAAA,CAAC4D,GAAG,aACN,UAACc,OAAAA,UAAK,QAER,UAAC1E,EAAAA,CAAGA,CAAAA,CAACE,GAAG,IAAI0D,GAAG,IAAIzD,GAAG,aACpB,UAAC4E,EAAAA,CAAeA,CAAAA,CACdrF,UAAU,YACVsF,KAAMC,EAAAA,GAAaA,CACnBnF,MAAM,UACN+B,QAAS,IAAMK,GAAe,oBAQ5C,WAACnD,MAAAA,CAAIW,UAAU,sBACb,WAACkC,EAAAA,CAAMA,CAAAA,CAACC,QA/KC,CA+KQqD,IA9KjC9C,GAAS,EACX,EA6KuDd,QAAQ,sBAC3C,UAACyD,EAAAA,CAAeA,CAAAA,CAACC,KAAMG,EAAAA,GAAMA,GAAI,OAC1BnJ,EAAE,0BAEX,WAAC4F,EAAAA,CAAMA,CAAAA,CAAClC,UAAU,cAAcmC,QArN/B,CAqNwCuD,GArNlCpD,GAAQ,GAqNsCV,QAAQ,sBAC3D,UAACyD,EAAAA,CAAeA,CAAAA,CAACC,KAAMG,EAAAA,GAAMA,GAAI,OAC1BnJ,EAAE,4CASxBiD,GACC,UAACoG,EAAAA,OAAWA,CAAAA,CACVC,KAAM/C,EACN3G,OAAQqD,EACRsG,YAAa,GAAclD,EAAaC,KAG5C,WAACtC,EAAAA,CAAGA,CAAAA,CAAC4D,GAAI,GAAIlE,UAAU,iBACrB,UAACmE,EAAAA,CAAWA,CAAAA,CAACC,MAAO9H,EAAE,iBACtB,UAACwJ,EAAAA,OAAaA,CAAAA,CAAAA,MAEfvD,GACC,UAACwD,EAAAA,OAASA,CAAAA,CACR7J,OAAQqG,EACRsD,YAAa,GAAclD,EAAaC,GACxC9C,GAAI+C,EAAS7D,GAAG,KAS1B,6JC5OA,MAxC4B,OAAC,YAC3BgH,CAAU,QAuCGC,EAtCbC,CAAQ,SACRC,CAAO,MAqCyBF,EAAC,YApCjCG,CAAkB,WAClBC,CAAS,SACTC,CAAO,CAQR,GACO,GAAEhK,CAAC,CAAE,CAAGC,CAAAA,EAAAA,EAAAA,EAAAA,CAAcA,CAAC,UAE7B,MACE,UAACyH,EAAAA,CAASA,CAAAA,CAACC,KAAK,IAACjE,UAAU,eACzB,UAACK,EAAAA,CAAGA,CAAAA,UACF,UAACC,EAAAA,CAAGA,CAAAA,CAAC4D,GAAI,EAAG1D,GAAI,EAAGC,GAAI,WACrB,UAACiE,EAAAA,CAAIA,CAACC,KAAK,EAAC4B,MAAO,CAAEC,SAAU,OAAQ,WACrC,UAACC,EAAAA,EAAWA,CAAAA,CACVC,gBAAiB,CACfC,gBAAiBrK,EAAE,eACnBsK,oBAAqB,yBACvB,EACAN,QAASA,EACT/E,MAAO8E,EACPxF,SAAUuF,EACVpG,UAAW,eACX6G,WAAY,0BAO1B,6GCTA,SAASC,EAASjD,CAAoB,EACpC,GAAM,GAAEvH,CAAC,CAAE,CAAGC,CAAAA,EAAAA,EAAAA,EAAAA,CAAcA,CAAC,UACvBwK,EAA6B,CACjCC,gBAAiB1K,EAAE,cACnB,EACI,SACJ2K,CAAO,MACPrB,CAAI,WACJsB,CAAS,uBACTC,CAAqB,WACrBC,CAAS,oBACTC,CAAkB,qBAClBC,CAAmB,kBACnBC,CAAgB,aAChBC,CAAW,oBACXC,CAAkB,gBAClBC,CAAc,SACdC,CAAO,WACPC,CAAS,sBACTC,CAAoB,CACpBC,mBAAiB,YACjBC,CAAU,CACVC,QAAM,kBACNC,CAAgB,cAChBC,CAAY,CAEZ,CADA,EACGC,EACJ,CAAGtE,EAGEuE,EAAiB,4BACrBrB,EACAsB,gBAAiB/L,EAAE,IAP0C,MAQ7DgM,UAAU,UACVrB,EACArB,KAAMA,GAAQ,EAAE,CAChB2C,OAAO,EACPC,2BAA4BrB,EAC5BsB,UAAWrB,EACXsB,gBAAiBf,qBACjBN,EACAsB,WAAY,GACZC,iBAAkBhB,EAClBiB,kBAAmBpB,GAA0C,GAC7DqB,eADwCrB,cACVD,GAA4B,CAAC,GAAI,GAAI,GAAI,CAA3BA,EAA+B,GAAG,CAC9EuB,oBAAqB7B,EACrB8B,oBAAqB1B,EACrB2B,aAAc1B,iBACdG,uBACAG,oBACAC,EACAoB,kBAAmB,UAACC,EAAAA,CAAYA,CAAAA,CAAAA,GAChCC,SAAU,UAACC,IAAAA,CAAErJ,UAAU,kCACvB+H,oBACAC,eACAE,mBACAD,EACAjI,UAAW,WACb,EACA,MACE,UAACsJ,EAAAA,EAASA,CAAAA,CAAE,GAAGlB,CAAc,EAEjC,CAEAtB,EAASyC,YAAY,CAAG,CACtBd,UAAW,GACXE,WAAY,GACZzB,UAAW,KACXU,WAAW,EACXC,qBAAsB,KACtBC,mBAAmB,EACnBC,YAAY,EACZE,kBAAkB,CACpB,EAEA,MAAenB,QAAQA,EAAC,mMC0IxB,MAAe1E,CAAAA,EAAAA,EAAAA,EAAAA,CAAOA,CAAC,GAAWoH,GA5OZ,IAClB,IAAMC,EAAQ,CACV,CACIlI,MAAO,EAyOqC,YAxO5CH,MAAO,eACX,EACA,CACIG,MAAO,YACPH,MAAO,YACX,EACA,CACIG,MAAO,UACPH,MAAO,UACX,EACA,CACIG,MAAO,QACPH,MAAO,QACX,EACA,CACIG,MAAO,SACPH,MAAO,gBACX,EACH,CACK,CAACsI,EAAWC,EAAe,CAAGhN,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAC,EAAE,EACzC,EAAGiN,EAAW,CAAGjN,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAC,IAC1B,CAACkN,EAAanH,EAAS,CAAG/F,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,EAAC,GACnC,CAACuK,EAAW4C,EAAa,CAAGnN,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAC,GACrC,CAACoN,EAASC,EAAW,CAAGrN,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAC,IACjC,CAACqJ,EAAYiE,EAAc,CAAGtN,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAC,IACvC,CAACwK,EAAuB+C,EAAyB,CAAGvN,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,EAAC,GAC7D,CAAC0J,EAAW8D,EAAa,CAAGxN,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAC8M,GACrC,CAACW,EAAeC,EAAiB,CAAG1N,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAM,CAAC,GACnD,GAAEL,CAAC,CAAE,CAAGC,CAAAA,EAAAA,EAAAA,EAAAA,CAAcA,CAAC,UAEvB+N,EAAa,MAAOC,IAClBA,GAASA,EAAMvL,GAAG,EAClBqL,EAAiBE,EAAMvL,GAAG,EAE9B0D,GAAS,EACb,EAEM8H,EAAkB,CACpBC,KAAM,CAAEC,WAAY,KAAM,EAC1BC,SAAU,CAAEC,KAAM,YAAaC,OAAQ,OAAQ,EAC/CC,MAAM,EACNC,MAAOhB,EACPiB,KAAM,EACNC,MAAO,CACHC,KAAMrH,EAAMqH,IAAI,EAAIrH,EAAMqH,IAAI,CAAClM,GAAG,CAAG6E,EAAMqH,IAAI,CAAClM,GAAG,CAAG,EAC1D,CACJ,EAEMiI,EAAU,CACZ,CACInK,KAAMR,EAAE,SACR6O,SAAU,GACVC,KAAM,GACFC,EAAEC,SAAS,EAAID,EAAEC,SAAS,CAAClH,KAAK,CAC5B,UAACmH,IAAIA,CAACC,KAAM,IAAkB,OAAdH,EAAEI,WAAW,CAAC,cAAzBF,EAAwC3G,GAAI,IAA0ByG,MAAAA,CAAtBA,EAAEI,WAAW,CAAC,UAAwB,OAAhBJ,EAAEC,SAAS,CAACtM,GAAG,WACrFqM,EAAEC,SAAS,CAAClH,KAAK,GAGtB,EAEZ,EACA,CACItH,KAAMR,EAAE,SACR6O,SAAU,QACVC,KAAM,GACKC,EAAEK,OAAO,EAAkB,gBAAdL,EAAEK,OAAO,CAAqB,eAAiBL,EAAEK,OAAO,EAGpF,CACI5O,KAAMR,EAAE,UACR6O,SAAU,GACVC,KAAOC,GACH,UAAChM,MAAAA,CAAI8C,QAAS,IAAMmI,EAAWe,GAAI9E,MAAO,CAAEoF,OAAQ,SAAU,WAC1D,UAACtC,IAAAA,CAAErJ,UAAU,2BAGzB,EACH,CAEK4L,EAAc,UAChBhC,GAAW,GACX,IAAMnG,EAAW,MAAM3E,EAAAA,CAAUA,CAAC+M,GAAG,CAAC,QAASrB,GAC3C/G,GAAYA,EAASmC,IAAI,EAAE,CAC3B+D,EAAelG,EAASmC,IAAI,EAC5BkE,EAAarG,EAASqI,UAAU,EAChClC,GAAW,GAEnB,EAkBMtC,EAAsB,MAAOyE,EAAiBf,KAChDR,EAAWO,KAAK,CAAGgB,EACnBvB,EAAWQ,IAAI,CAAGA,EAClBpB,GAAW,GACX,IAAMoC,EAAaC,IAAAA,GAAK,CAAC5F,EAAW,SAChC2F,GAAcA,EAAW/N,MAAM,CAAG,GAAG,CACrCuM,EAAWS,KAAK,CAAG,CACf,GAAGT,EAAWS,KAAK,CACnBQ,YAAaO,EACjB,EAEJ,IAAMvI,EAAW,MAAM3E,EAAAA,CAAUA,CAAC+M,GAAG,CAAC,QAASrB,GAC3C/G,GAAYA,EAASmC,IAAI,EAAInC,EAASmC,IAAI,CAAC3H,MAAM,CAAG,GAAG,CACvD0L,EAAelG,EAASmC,IAAI,EAC5BoE,EAAW+B,GACXnC,GAAW,GAEnB,EAEAtG,CAAAA,EAAAA,EAAAA,SAAAA,CAASA,CAAC,KACNkH,EAAWQ,IAAI,CAAG,EAClBY,GACJ,EAAG,EAAE,EAEL,IAAMM,EAAyBC,EAAAA,OAAa,CAAC,KA2BzC,IAAMC,EAAY,IACVC,GAAG,CACH7B,EAAWG,QAAQ,CAAC/M,KAAK,CAAG,CAAEwG,MAAO,CAAEkI,OAAQD,CAAE,CAAE,EACnDT,IAER,EAEMW,EAAoBN,IAAAA,QAAU,CAAC,GAAeG,EAAUC,GAAIxK,OAAO6B,KAAgC,GAAK,KAO9G,MACI,UAACuC,EAAAA,OAAmBA,CAAAA,CAChBC,SAPa,CAOHsG,GANdvC,EAAcnJ,EAAEE,MAAM,CAACO,KAAK,EAC5BgL,EAAkBzL,EAAEE,MAAM,CAACO,KAAK,CACpC,EAKQ4E,QA3CY,CA2CHsG,IA1CTzG,IACAkE,EAAyB,CAAC/C,GAC1B8C,EAFY,IAIpB,EAuCQjE,WAAYA,EACZI,mBAtCoBsG,CAsCAtG,GArCxB+D,EAAauC,GAEb,IAAMC,EAAaV,IAAAA,GAAK,CAACS,CAFO,CAEU,SAGhB,GAAG,CAAzBC,EAAW1O,MAAM,CAEjB0L,EAAe,EAAE,GAAI,EAGVsB,KAAK,CAAG,CACf,GAAGT,EAAWS,KAAK,CACnBQ,GALwC,SAK3BkB,CACjB,EACAf,IAER,EAsBQvF,SAxBe,CAwBJA,EACXC,QAASmD,GAGrB,EAAG,CAACzD,EAAYK,EAAWc,EAAsB,EAE3CyF,EAAY,IAAMlK,GAAS,GAE3BmK,EAAe,OAhCoD,GAqCrE,GAJAnK,GAAS,GAET,MAAM5D,EAAAA,CAAUA,CAACgO,MAAM,CAAC,SAAuB,OAAd1C,IAE7B/D,GAAa0G,MAAMC,OAAO,CAAC3G,GAAY,CACvC,IAAMsG,EAAaV,IAAAA,GAAK,CAAC5F,EAAW,SAChCsG,GAAcA,EAAW1O,MAAM,CAAG,GAAG,CACrCuM,EAAWS,KAAK,CAAG,CACf,GAAGT,EAAWS,KAAK,CACnBQ,YAAakB,EACjB,CAER,CACAf,GACJ,EAEA,MACI,WAACvM,MAAAA,CAAIW,UAAU,8BACX,WAACV,EAAAA,CAAKA,CAAAA,CAACC,KAAMsK,EAAanK,OAAQkN,YAC9B,UAACtN,EAAAA,CAAKA,CAACM,MAAM,EAACqN,WAAW,aACrB,UAAC3N,EAAAA,CAAKA,CAACO,KAAK,WAAEvD,EAAE,sBAEpB,UAACgD,EAAAA,CAAKA,CAACS,IAAI,WAAEzD,EAAE,4CACf,WAACgD,EAAAA,CAAKA,CAAC2C,MAAM,YACT,UAACC,EAAAA,CAAMA,CAAAA,CAACN,QAAQ,YAAYO,QAASyK,WAChCtQ,EAAE,YAEP,UAAC4F,EAAAA,CAAMA,CAAAA,CAACN,QAAQ,UAAUO,QAAS0K,WAC9BvQ,EAAE,6BAIf,UAACwK,EAAAA,CAAQA,CAAAA,CACLG,QAASA,EACTrB,KAAM8D,EACNxC,UAAWA,EACXE,SAAS,IACTQ,UAAW,GACXT,sBAAuBA,EACvBE,mBAAoB6E,EACpB5E,oBAAqBA,EACrBC,iBAzIcyD,CAyIIzD,GAxI1BiD,EAAWO,KAAK,CAAGhB,EACnBS,EAAWQ,IAAI,CAAGA,EACC,IAAI,CAAnBhF,GACAwE,GAAWS,KAAK,CAAG,CAAE7G,MAAO4B,EAAW,EAE3C,IAAMgG,EAAaC,IAAAA,GAAK,CAAC5F,EAAW,SAChC2F,GAAcA,EAAW/N,MAAM,CAAG,GAAG,CACrCuM,EAAWS,KAAK,CAAG,CACf,GAAGT,EAAWS,KAAK,CACnBQ,YAAaO,EACjB,EAEJJ,GACJ,MA+HJ,gJCxJA,MAxFkB,OAAC,QAAE1P,CAAM,YAwFZ6J,CAxFcF,CAAW,IAAE/F,CAAE,CAA+C,GACnF,GAAExD,CAAC,CAAE,CAAGC,CAAAA,EAAAA,EAAAA,EAAAA,CAAcA,CAAC,UACvB2Q,EAAqB,CACzBC,UAAU,EACVC,UAAU,EACVC,UAAU,EACVC,UAAU,CACZ,EACM,CAACC,EAAUC,EAAY,CAAG7Q,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAACuQ,GACnC,CAACO,EAAeC,EAAiB,CAAG/Q,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,EAAC,GAE7CgR,EAAiB,IACrB,GAAM,MAAE7Q,CAAI,SAAE8Q,CAAO,CAAE,CAAG9M,EAAEE,MAAM,CAClCwM,EAAY,GAAgB,EAAE,GAAG7J,CAAS,CAAE,CAAC7G,CAAjB,CAAsB,CAAE8Q,EAAQ,GAC5DF,EAAiB,CAACD,EACpB,EACM9K,EAAe,IACnB6K,EAAYN,GACZQ,EAAiB9K,EACnB,EAKA,MACE,+BACE,WAACtD,EAAAA,CAAKA,CAAAA,CACJC,KAAMrD,EACNwD,OAPoB,CAOZmO,IANZhI,EAAY,GACd,EAMMrG,KAAK,KACLM,GAAG,eACHE,UAAU,kBAEV,UAACV,EAAAA,CAAKA,CAACM,MAAM,EAACqN,WAAW,aACvB,UAAC3N,EAAAA,CAAKA,CAACO,KAAK,WAAEvD,EAAE,yBAElB,UAACgD,EAAAA,CAAKA,CAACS,IAAI,WACT,WAACV,MAAAA,CAAIW,UAAU,sBACb,UAAC8N,EAAAA,CAAKA,CAAAA,CAAClM,QAAQ,kBAAUtF,EAAE,sBAC3B,UAACoI,EAAAA,CAAIA,CAACqJ,KAAK,EACT/N,UAAU,OACV1B,KAAK,WACLxB,KAAK,WACL+D,SAAU8M,EACVC,QAASL,EAASJ,QAAQ,CAC1B5L,MAAM,WACNH,MAAO9E,EAAE,0BAEV,UAACoI,EAAAA,CAAIA,CAACqJ,KAAK,EACV/N,UAAU,OACVlD,KAAK,WACL+D,SAAU8M,EACVrP,KAAK,WACLsP,QAASL,EAASH,QAAQ,CAC1B7L,MAAM,WACNH,MAAO9E,EAAE,0BAEX,UAACoI,EAAAA,CAAIA,CAACqJ,KAAK,EACT/N,UAAU,OACVlD,KAAK,WACL+D,SAAU8M,EACVrP,KAAK,WACLsP,QAASL,EAASF,QAAQ,CAC1B9L,MAAM,WACNH,MAAO9E,EAAE,0BAEX,UAACoI,EAAAA,CAAIA,CAACqJ,KAAK,EACT/N,UAAU,OACV1B,KAAK,WACLxB,KAAK,WACLyE,MAAM,WACNH,MAAO9E,EAAE,wBACTsR,QAASL,EAASD,QAAQ,CAC1BzM,SAAU8M,SAIhB,UAACK,EAAAA,OAAYA,CAAAA,CACXC,SAAS,SACTC,OAAQpO,GAAU,EAALA,CACbqO,OAAQV,EACRW,aAAexL,GAAaD,EAAaC,SAKnD,gLC3BA,MArDqB,IACjB,GAAM,QAAEuL,CAAM,YAoDHH,EApDKI,CAAY,QAAEF,CAAM,EAoDZ,QApDcD,CAAQ,CAAE,CAAGpK,EAC7C,GAAEvH,CAAC,CAAE,CAAGC,CAAAA,EAAAA,EAAAA,EAAAA,CAAcA,CAAC,UAEvB8R,EAAc,KAChBD,GAAa,EACjB,EAEME,EAAwB,UAC1B,IAAI7K,GACa,UAAU,CAAvBwK,EACW,MAAMnP,EAAAA,CAAUA,CAACgO,MAAM,CAAC,GAAeoB,MAAAA,CAAZD,EAAS,KAAU,OAAPC,IAEvC,MAAMpP,EAAAA,CAAUA,CAACC,IAAI,CAAC,GAAY,OAATkP,GAAY,CAAEM,KAAML,CAAO,MAI/DE,GAAa,GACbI,IAAAA,IAAW,CAAC,SAEpB,EAEA,MACI,WAAClP,EAAAA,CAAKA,CAAAA,CAACC,KAAM4O,EAAQzO,OAAQ2O,YACzB,UAAChP,MAAAA,CAAIW,UAAU,2BACX,UAACqF,EAAAA,CAAeA,CAAAA,CACZC,KAAMmJ,EAAAA,GAAqBA,CAC3BjP,KAAK,KACLY,MAAM,YACNmG,MAAO,CACHmI,WAAY,UACZC,QAAS,OACTxO,aAAc,MACdyO,MAAO,QACPC,OAAQ,OACZ,MAGR,UAACvP,EAAAA,CAAKA,CAACS,IAAI,WAAC,UAACW,IAAAA,UAAGpE,EAAE,oDAClB,WAACgD,EAAAA,CAAKA,CAAC2C,MAAM,YACT,UAACC,EAAAA,CAAMA,CAAAA,CAACN,QAAQ,YAAYO,QAASkM,WACpC/R,EAAE,QAEH,UAAC4F,EAAAA,CAAMA,CAAAA,CAACN,QAAQ,SAASO,QAASmM,WACjChS,EAAE,sBAKnB,gEC3De,SAAS6H,EAAYN,CAAuB,EACzD,MACE,UAACiL,KAAAA,CAAG9O,UAAU,wBAAgB6D,EAAMO,KAAK,EAE7C,mBCPA,4CACA,WACA,WACA,OAAe,EAAQ,KAAsC,CAC7D,EACA,SAFsB", "sources": ["webpack://_N_E/./components/common/ImageEditor.tsx", "webpack://_N_E/./pages/profile/index.tsx", "webpack://_N_E/./pages/profile/bookmarkTableFilter.tsx", "webpack://_N_E/./components/common/RKITable.tsx", "webpack://_N_E/./pages/profile/bookmarkTable.tsx", "webpack://_N_E/./pages/profile/myConsent.tsx", "webpack://_N_E/./pages/profile/confirmation.tsx", "webpack://_N_E/./components/common/PageHeading.tsx", "webpack://_N_E/?e407"], "sourcesContent": ["//Import Library\r\nimport React, { useState, useRef } from \"react\";\r\nimport AvatarEditor from \"react-avatar-editor\";\r\nimport RangeSlider from \"react-bootstrap-range-slider\";\r\nimport { Modal, Button, Row, Col } from \"react-bootstrap\";\r\nimport toast from 'react-hot-toast';\r\n\r\n//Import services/components\r\nimport apiService from \"../../services/apiService\";\r\nimport { useTranslation } from 'next-i18next';\r\n\r\nconst ImageEditor = ({ isOpen, onModalClose, image}: any) => {\r\n  const { t } = useTranslation('common');\r\n  const file = t(\"setInfo.Choosefile\");\r\n  const error = \"Something wrong in server || your data!\";\r\n  const [scale, setScale] = useState(1);\r\n  const [rotate, setRotate] = useState(0);\r\n  const [name, setName] = useState(\"\");\r\n  const [img, setImg] = useState<string | null>(null);\r\n  const editorRef = useRef<any>(null);\r\n\r\n  const cropHandler = async () => {\r\n    /*****Helper Function to convert to blob******/\r\n    const dataURLtoBlob = (dataurl: string) => {\r\n    const arr = dataurl.split(\",\");\r\n     const mimeMatch = arr[0].match(/:(.*?);/);\r\n     const mime = mimeMatch ? mimeMatch[1] : '';\r\n     const bstr = atob(arr[1]);\r\n     let n = bstr.length;\r\n     const u8arr = new Uint8Array(n);\r\n      while (n--) {\r\n        u8arr[n] = bstr.charCodeAt(n);\r\n      }\r\n      return new Blob([u8arr], { type: mime });\r\n    };\r\n    /*****End ********/\r\n\r\n    const canvas = editorRef.current\r\n      .getImageScaledToCanvas()\r\n      .toDataURL(\"image/jpeg\", 0.8);\r\n    const blob = dataURLtoBlob(canvas);\r\n    const fd = new FormData();\r\n    fd.append(\"file\", blob, name);\r\n\r\n    try {\r\n      const res = await apiService.post(\"/image\", fd, {\r\n        \"Content-Type\": \"multipart/form-data\",\r\n      });\r\n\r\n      if (res && res._id) {\r\n        const response = await apiService.post(\"/users/updateProfile\", {\r\n          image: res._id,\r\n        });\r\n        if (response) {\r\n          toast.success(t(\"setInfo.ProfileUpdatedSuccessfully\"));\r\n\r\n        }\r\n      }\r\n    } catch (error: unknown) {\r\n      throw error instanceof Error ? error : new Error('Unknown error occurred');\r\n    }\r\n    onModalClose(false);\r\n    setImg(null);\r\n    setName(file);\r\n    setScale(1);\r\n  };\r\n\r\n  /**File Handler**/\r\n  const fileHandler = (e: React.ChangeEvent<HTMLInputElement>) => {\r\n    if (e.target.files && e.target.files[0]) {\r\n      setName(e.target.files[0].name);\r\n      setImg(URL.createObjectURL(e.target.files[0]));\r\n    }\r\n  };\r\n  /**End**/\r\n\r\n  return (\r\n    <>\r\n      <div>\r\n        <Modal\r\n          show={isOpen}\r\n          size=\"lg\"\r\n          aria-labelledby=\"ProfileEdit\"\r\n          onHide={() => onModalClose(false)}\r\n          centered\r\n        >\r\n          <Modal.Header>\r\n            <Modal.Title id=\"contained-modal-title-vcenter\">\r\n            {t(\"setInfo.EditYourImage\")}\r\n            </Modal.Title>\r\n          </Modal.Header>\r\n          <Modal.Body>\r\n            <div className=\"d-flex flex-column justify-content-center align-items-center\">\r\n              <AvatarEditor\r\n                ref={editorRef}\r\n                borderRadius={100}\r\n                scale={scale}\r\n                rotate={rotate}\r\n                color={[0, 0, 0, 0.6]}\r\n                image={img ? img : \"/images/rkiProfile.jpg\"}\r\n              />\r\n            </div>\r\n\r\n            <div className=\"my-3 mx-2\">\r\n              <Row className=\"align-items-center mb-4\">\r\n                <Col sm={2} md={2} lg={2} className=\"pe-0\">\r\n                <b>{t(\"setInfo.Uploadaimage\")}</b>\r\n                </Col>\r\n                <Col sm={10} md={10} lg={10}>\r\n                  <div className=\"form-control custom-file position-relative\">\r\n                    <input\r\n                      type=\"file\"\r\n                      name=\"files\"\r\n                      className=\"form-control-input form-control custom-file-input\"\r\n                      accept=\"image/*\"\r\n                      id=\"customFile\"\r\n                      onChange={fileHandler}\r\n                    />\r\n                    <label className=\"custom-file-label form-control-label w-100\"  data-browse = {t(\"Browse\")}>{t(\"setInfo.Choosefile\")}</label>\r\n                  </div>\r\n                </Col>\r\n              </Row>\r\n            </div>\r\n\r\n            <div className=\"my-3 mx-2\">\r\n              <Row>\r\n                <Col sm={2} md={2} lg={2} className=\"pe-0\">\r\n                <b>{t(\"setInfo.Zoom\")}</b>\r\n                </Col>\r\n                <Col sm={4} md={4} lg={4}>\r\n                  <RangeSlider\r\n                    value={scale}\r\n                    tooltip=\"auto\"\r\n                    min={1}\r\n                    max={10}\r\n                    step={0.1}\r\n                    variant=\"primary\"\r\n                    onChange={(changeEvent: React.ChangeEvent<HTMLInputElement>) =>\r\n                      setScale(Number(changeEvent.target.value))\r\n                    }\r\n                  />\r\n                </Col>\r\n                <Col sm={2} md={2} lg={2} className=\"pe-0\">\r\n                <b>{t(\"setInfo.Rotate\")}</b>\r\n                </Col>\r\n                <Col sm={4} md={4} lg={4}>\r\n                  <RangeSlider\r\n                    value={rotate}\r\n                    tooltip=\"auto\"\r\n                    tooltipLabel={(currentValue) => `${currentValue}°`}\r\n                    min={0}\r\n                    max={360}\r\n                    variant=\"primary\"\r\n                    onChange={(changeEvent: React.ChangeEvent<HTMLInputElement>) =>\r\n                      setRotate(Number(changeEvent.target.value))\r\n                    }\r\n                  />\r\n                </Col>\r\n              </Row>\r\n            </div>\r\n          </Modal.Body>\r\n          <Modal.Footer>\r\n          <Button onClick={cropHandler}>{t(\"setInfo.SaveChanges\")}</Button>\r\n            <Button variant=\"danger\" onClick={() => {\r\n              onModalClose(false)\r\n              setImg(null)\r\n              }\r\n            }>\r\n            {t(\"Cancel\")}\r\n            </Button>\r\n          </Modal.Footer>\r\n        </Modal>\r\n      </div>\r\n    </>\r\n  );\r\n};\r\n\r\nexport default ImageEditor;\r\n", "//Import Library\r\nimport React, { useState, useEffect } from \"react\";\r\nimport { connect } from \"react-redux\";\r\nimport {\r\n  Con<PERSON>er,\r\n  <PERSON>,\r\n  Button,\r\n  Form,\r\n  Row,\r\n  Col,\r\n  Spinner,\r\n} from \"react-bootstrap\";\r\nimport { FontAwesomeIcon } from \"@fortawesome/react-fontawesome\";\r\nimport {\r\n  faEdit,\r\n  faCheckSquare,\r\n} from \"@fortawesome/free-solid-svg-icons\";\r\n\r\n//Import services/components\r\nimport ProfileEdit from \"./profileEdit\";\r\nimport MyConsent from \"./myConsent\";\r\nimport BookmarkTable from \"./bookmarkTable\";\r\nimport { loadLoggedinUserData } from \"../../stores/userActions\";\r\nimport apiService from \"../../services/apiService\";\r\nimport { useTranslation } from 'next-i18next';\r\nimport { serverSideTranslations } from 'next-i18next/serverSideTranslations';\r\nimport ImageEditor from \"../../components/common/ImageEditor\";\r\nimport PageHeading from \"../../components/common/PageHeading\";\r\n\r\n\r\nconst Profile = (props: any) => {\r\n  const { t } = useTranslation('common');\r\n  const [show, setShow]: any = useState(false);\r\n  const [consentShow, setConsentShow] = useState(false);\r\n  const [modal, setModal] = useState(false);\r\n\r\n  const handleShow = () => setShow(true);\r\n\r\n  const closeHandler = (val: any) => {\r\n    setShow(val);\r\n    setConsentShow(false);\r\n  };\r\n\r\n  const intialState = {\r\n    username: \"\",\r\n    institution: \"\",\r\n    email: \"\",\r\n    image: null,\r\n    firstname: \"\",\r\n    lastname: \"\",\r\n    position: \"\"\r\n  };\r\n\r\n  const [userData, setUserData]: any = useState(intialState);\r\n\r\n  useEffect(() => {\r\n    const getUser = async (userParams: any) => {\r\n      const response = await apiService.post(\"/users/getLoggedUser\", userParams);\r\n      if (response) {\r\n        response.image =\r\n          response.image && response.image._id\r\n            ? `${process.env.API_SERVER}/image/show/${response.image._id}`\r\n            : \"/images/rkiProfile.jpg\";\r\n        setUserData((prevState: any) => ({ ...prevState, ...response }));\r\n      }\r\n    };\r\n    getUser({});\r\n  }, [show, modal]);\r\n\r\n  /***Handle Image Edit****/\r\n  const imageEditorHandler = () => {\r\n    setModal(true);\r\n  };\r\n\r\n  const modalClose = (val: any) => {\r\n    props.dispatch(loadLoggedinUserData());\r\n    setModal(val);\r\n  };\r\n  /********End *********/\r\n\r\n  return (\r\n    <Container fluid className=\"p-0\">\r\n      <Row>\r\n        <Col xs={12}>\r\n          <PageHeading title={t(\"setInfo.myprofile\")} />\r\n        </Col>\r\n      </Row>\r\n      <Row>\r\n        <Col xs lg={12}>\r\n          <Container fluid>\r\n            <Row>\r\n              <Col>\r\n                <Card className=\" mt-3 form--outline profile--card\">\r\n                  <div className=\"row mx-3 mt-4\">\r\n                    <div className=\"col-lg-3 col-md-4 text-center\">\r\n                      {userData.image ? (\r\n                        <>\r\n                          <img\r\n                            className=\"imgStyle\"\r\n                            src={\r\n                              userdata_func()\r\n                            }\r\n                          />\r\n                        </>\r\n                      ) : (\r\n                        <Spinner\r\n                          className=\"text-center m-5\"\r\n                          animation=\"grow\"\r\n                          variant=\"dark\"\r\n                      />\r\n                        )}\r\n                    </div>\r\n                    <div className=\"col-md-9 w-100\">\r\n                      <Form>\r\n                        <div>\r\n                          <Form.Group\r\n                            as={Row}\r\n                            controlId=\"username\"\r\n                            className=\"align-items-center mb-3\"\r\n                          >\r\n                            <Form.Label column md=\"3\" xs=\"5\" lg=\"2\" className=\"px-1\">\r\n                              {t(\"setInfo.username\")}\r\n                            </Form.Label>\r\n                            <Col xs=\"1\">\r\n                              <span>:</span>\r\n                            </Col>\r\n                            <Col md=\"8\" xs=\"5\" lg=\"9\">\r\n                              <Form.Control\r\n                                plaintext\r\n                                readOnly\r\n                                defaultValue={userData.username}\r\n                              />\r\n                            </Col>\r\n                          </Form.Group>\r\n\r\n                          <Form.Group\r\n                            as={Row}\r\n                            controlId=\"name\"\r\n                            className=\"align-items-center mb-3\"\r\n                          >\r\n                            <Form.Label column md=\"3\" xs=\"5\" lg=\"2\" className=\"px-1\">\r\n                              {t(\"setInfo.name\")}\r\n                            </Form.Label>\r\n                            <Col xs=\"1\">\r\n                              <span>:</span>\r\n                            </Col>\r\n                            <Col md=\"8\" xs=\"5\" lg=\"9\">\r\n                              <Form.Control\r\n                                plaintext\r\n                                readOnly\r\n                                defaultValue={userData && userData.firstname ? `${userData.firstname} ${userData.lastname}` : \"\"}\r\n                              />\r\n                            </Col>\r\n                          </Form.Group>\r\n                          <Form.Group\r\n                            as={Row}\r\n                            controlId=\"position\"\r\n                            className=\"align-items-center mb-3\"\r\n                          >\r\n                            <Form.Label column md=\"3\" xs=\"5\" lg=\"2\" className=\"px-1\">\r\n                              {t(\"setInfo.position\")}\r\n                            </Form.Label>\r\n                            <Col xs=\"1\">\r\n                              <span>:</span>\r\n                            </Col>\r\n                            <Col md=\"8\" xs=\"5\" lg=\"9\">\r\n                              <Form.Control\r\n                                plaintext\r\n                                readOnly\r\n                                defaultValue={userData && userData.position ? userData.position : \"\"}\r\n                              />\r\n                            </Col>\r\n                          </Form.Group>\r\n\r\n                          <Form.Group\r\n                            as={Row}\r\n                            controlId=\"Organization\"\r\n                            className=\"align-items-center mb-3\"\r\n                          >\r\n                            <Form.Label column md=\"3\" xs=\"5\" lg=\"2\" className=\"px-1\">\r\n                              {t(\"setInfo.organisation\")}\r\n                            </Form.Label>\r\n                            <Col xs=\"1\">\r\n                              <span>:</span>\r\n                            </Col>\r\n                            <Col md=\"8\" xs=\"5\" lg=\"9\">\r\n                              <Form.Control\r\n                                readOnly\r\n                                plaintext\r\n                                defaultValue={\r\n                                  userData.institution &&\r\n                                  userData.institution.title\r\n                                }\r\n                              />\r\n                            </Col>\r\n                          </Form.Group>\r\n                          <ImageEditor\r\n                            image={userData.image}\r\n                            isOpen={modal}\r\n                            onModalClose={(val: any) => modalClose(val)}\r\n                          />\r\n                          <Form.Group\r\n                            as={Row}\r\n                            controlId=\"Email\"\r\n                            className=\"align-items-center mb-3\"\r\n                          >\r\n                            <Form.Label column md=\"3\" xs=\"5\" lg=\"2\" className=\"px-1\">\r\n                              {t(\"setInfo.email\")}\r\n                            </Form.Label>\r\n                            <Col xs=\"1\">\r\n                              <span>:</span>\r\n                            </Col>\r\n                            <Col md=\"8\" xs=\"5\" lg=\"9\">\r\n                              <Form.Control\r\n                                plaintext\r\n                                readOnly\r\n                                defaultValue={userData.email}\r\n                              />\r\n                            </Col>\r\n                          </Form.Group>\r\n                          <Form.Group\r\n                            as={Row}\r\n                            controlId=\"consent\"\r\n                            className=\"align-items-center mb-3\"\r\n                          >\r\n                            <Form.Label column md=\"3\" xs=\"5\" lg=\"2\" className=\"px-1\">\r\n                            {t(\"setInfo.Consent\")}\r\n                            </Form.Label>\r\n                            <Col xs=\"1\">\r\n                              <span>:</span>\r\n                            </Col>\r\n                            <Col md=\"8\" xs=\"5\" lg=\"9\">\r\n                              <FontAwesomeIcon\r\n                                className=\"clickable\"\r\n                                icon={faCheckSquare}\r\n                                color=\"#293F92\"\r\n                                onClick={() => setConsentShow(true)}\r\n                              />\r\n                            </Col>\r\n                          </Form.Group>\r\n                        </div>\r\n                      </Form>\r\n                    </div>\r\n                  </div>\r\n                  <div className=\"mb-3 mx-3\">\r\n                    <Button onClick={imageEditorHandler} variant=\"secondary\">\r\n                      <FontAwesomeIcon icon={faEdit} />\r\n                      &nbsp;{t(\"setInfo.editmyimage\")}\r\n                    </Button>\r\n                    <Button className=\"float-right\" onClick={handleShow} variant=\"secondary\">\r\n                      <FontAwesomeIcon icon={faEdit} />\r\n                      &nbsp;{t(\"setInfo.editmyprofile\")}\r\n                    </Button>\r\n                  </div>\r\n                </Card>\r\n              </Col>\r\n            </Row>\r\n          </Container>\r\n        </Col>\r\n      </Row>\r\n      {show && (\r\n        <ProfileEdit\r\n          data={userData}\r\n          isOpen={show}\r\n          manageClose={(val: any) => closeHandler(val)}\r\n        />\r\n      )}\r\n      <Col xs={12} className=\"mt-3\">\r\n        <PageHeading title={t(\"MyBookmarks\")} />\r\n        <BookmarkTable />\r\n      </Col>\r\n      {consentShow && (\r\n        <MyConsent\r\n          isOpen={consentShow}\r\n          manageClose={(val: any) => closeHandler(val)}\r\n          id={userData._id}\r\n        />\r\n      )}\r\n    </Container>\r\n  );\r\n\r\n  function userdata_func(): string {\r\n    return userData && userData.image ? userData.image : \"\";\r\n  }\r\n};\r\n\r\nexport async function getStaticProps({ locale } : { locale: string}) {\r\n  return {\r\n    props: {\r\n      ...(await serverSideTranslations(locale, ['common'])),\r\n    },\r\n  }\r\n}\r\n\r\nexport default connect()(Profile);\r\n", "//Import Library\r\nimport React from \"react\";\r\nimport { Form, Col, Container, Row } from \"react-bootstrap\";\r\nimport { MultiSelect } from \"react-multi-select-component\";\r\n\r\n//Import services/components\r\nimport { useTranslation } from 'next-i18next';\r\n\r\nconst BookmarkTableFilter = ({\r\n  filterText,\r\n  onFilter,\r\n  onClear,\r\n  handleGroupHandler,\r\n  groupType,\r\n  options,\r\n}: {\r\n  filterText: any,\r\n  onFilter: any,\r\n  onClear: any,\r\n  handleGroupHandler: any,\r\n  groupType: any,\r\n  options: any,\r\n}) => {\r\n  const { t } = useTranslation('common');\r\n\r\n  return (\r\n    <Container fluid className=\"p-0\">\r\n      <Row>\r\n        <Col xs={4} md={4} lg={4}>\r\n          <Form.Group style={{ maxWidth: \"800px\" }}>\r\n            <MultiSelect\r\n              overrideStrings={{\r\n                selectSomeItems: t(\"ChooseGroup\"),\r\n                allItemsAreSelected: \"All Groups are Selected\",\r\n              }}\r\n              options={options}\r\n              value={groupType}\r\n              onChange={handleGroupHandler}\r\n              className={\"choose-group\"}\r\n              labelledBy={\"Select Network\"}\r\n            />\r\n          </Form.Group>\r\n        </Col>\r\n      </Row>\r\n    </Container>\r\n  );\r\n};\r\n\r\nexport default BookmarkTableFilter;\r\n", "//Import Library\r\nimport React from 'react'\r\nimport DataTable  from 'react-data-table-component';\r\n\r\n//Import services/components\r\nimport { useTranslation } from 'next-i18next';\r\nimport CustomLoader from './CustomLoader';\r\n\r\ninterface RKITableProps {\r\n  columns: any[];\r\n  data: any[];\r\n  totalRows?: number;\r\n  resetPaginationToggle?: boolean;\r\n  subheader?: boolean;\r\n  subHeaderComponent?: React.ReactNode;\r\n  handlePerRowsChange?: (newPerPage: number, page: number) => void;\r\n  handlePageChange?: (page: number) => void;\r\n  rowsPerPage?: number[];\r\n  defaultRowsPerPage?: number;\r\n  selectableRows?: boolean;\r\n  loading?: boolean;\r\n  pagServer?: boolean;\r\n  onSelectedRowsChange?: (selectedRows: any) => void;\r\n  clearSelectedRows?: boolean;\r\n  sortServer?: boolean;\r\n  onSort?: (column: any, sortDirection: string) => void;\r\n  persistTableHead?: boolean;\r\n  sortFunction?: (rows: any[], field: any, direction: 'asc' | 'desc') => any[];\r\n  noHeader?: boolean;\r\n  dense?: boolean;\r\n  pagination?: boolean;\r\n  paginationServer?: boolean;\r\n  paginationTotalRows?: number;\r\n  subHeaderAlign?: string;\r\n  [key: string]: any;\r\n}\r\n\r\nfunction RKITable(props: RKITableProps) {\r\n  const { t } = useTranslation('common');\r\n  const paginationComponentOptions = {\r\n    rowsPerPageText: t('Rowsperpage'),\r\n    };\r\n  const {\r\n    columns,\r\n    data,\r\n    totalRows,\r\n    resetPaginationToggle,\r\n    subheader,\r\n    subHeaderComponent,\r\n    handlePerRowsChange,\r\n    handlePageChange,\r\n    rowsPerPage,\r\n    defaultRowsPerPage,\r\n    selectableRows,\r\n    loading,\r\n    pagServer,\r\n    onSelectedRowsChange,\r\n    clearSelectedRows,\r\n    sortServer,\r\n    onSort,\r\n    persistTableHead,\r\n    sortFunction,\r\n    // Filter out any unknown props that might cause DOM warnings\r\n    ...otherProps\r\n  } = props;\r\n\r\n  // Only pass known DataTable props to avoid DOM warnings\r\n  const dataTableProps = {\r\n    paginationComponentOptions,\r\n    noDataComponent: t(\"NoData\"),\r\n    noHeader: true,\r\n    columns,\r\n    data: data || [],\r\n    dense: true,\r\n    paginationResetDefaultPage: resetPaginationToggle,\r\n    subHeader: subheader,\r\n    progressPending: loading,\r\n    subHeaderComponent,\r\n    pagination: true,\r\n    paginationServer: pagServer,\r\n    paginationPerPage: defaultRowsPerPage ? defaultRowsPerPage : 10,\r\n    paginationRowsPerPageOptions: rowsPerPage ? rowsPerPage : [10, 15, 20, 25, 30],\r\n    paginationTotalRows: totalRows,\r\n    onChangeRowsPerPage: handlePerRowsChange,\r\n    onChangePage: handlePageChange,\r\n    selectableRows,\r\n    onSelectedRowsChange,\r\n    clearSelectedRows,\r\n    progressComponent: <CustomLoader/>,\r\n    sortIcon: <i className=\"sort-icon fas fa-exchange-alt\" />,\r\n    sortServer,\r\n    onSort,\r\n    sortFunction,\r\n    persistTableHead,\r\n    className: \"rki-table\"\r\n  };\r\n  return (\r\n    <DataTable {...dataTableProps} />\r\n  )\r\n};\r\n\r\nRKITable.defaultProps = {\r\n  subHeader: false,\r\n  pagination: true,\r\n  totalRows: null,\r\n  pagServer: true,\r\n  onSelectedRowsChange: null,\r\n  clearSelectedRows: false,\r\n  sortServer: false,\r\n  persistTableHead: false\r\n}\r\n\r\nexport default RKITable;\r\n\r\n", "//Import Library\r\nimport React, { useEffect, useState } from \"react\";\r\nimport { connect } from \"react-redux\";\r\nimport _ from \"lodash\";\r\nimport Link from \"next/link\";\r\nimport { Mo<PERSON>, But<PERSON> } from \"react-bootstrap\";\r\n\r\n//Import services/components\r\nimport BookmarkTableFilter from \"./bookmarkTableFilter\";\r\nimport RKITable from \"../../components/common/RKITable\";\r\nimport apiService from \"../../services/apiService\";\r\nimport { useTranslation } from 'next-i18next';\r\n\r\n\r\nconst BookmarkTable = (props: any) => {\r\n    const GROUP = [\r\n        {\r\n            value: \"institution\",\r\n            label: \"Organisations\",\r\n        },\r\n        {\r\n            value: \"operation\",\r\n            label: \"Operations\",\r\n        },\r\n        {\r\n            value: \"project\",\r\n            label: \"Projects\",\r\n        },\r\n        {\r\n            value: \"event\",\r\n            label: \"Events\",\r\n        },\r\n        {\r\n            value: \"vspace\",\r\n            label: \"Virtual Spaces\",\r\n        },\r\n    ];\r\n    const [tabledata, setDataToTable] = useState([]);\r\n    const [, setLoading] = useState(false);\r\n    const [isModalShow, setModal] = useState(false);\r\n    const [totalRows, setTotalRows] = useState(0);\r\n    const [perPage, setPerPage] = useState(10);\r\n    const [filterText, setFilterText] = useState(\"\");\r\n    const [resetPaginationToggle, setResetPaginationToggle] = useState(false);\r\n    const [groupType, setGroupType] = useState(GROUP);\r\n    const [selectedGroup, setSelectedGroup] = useState<any>({});\r\n    const { t } = useTranslation('common');\r\n\r\n    const userAction = async (group: any) => {\r\n        if (group && group._id) {\r\n            setSelectedGroup(group._id);\r\n        }\r\n        setModal(true);\r\n    };\r\n\r\n    const flagParams: any = {\r\n        sort: { created_at: \"asc\" },\r\n        populate: { path: \"entity_id\", select: \"title\" },\r\n        lean: true,\r\n        limit: perPage,\r\n        page: 1,\r\n        query: {\r\n            user: props.user && props.user._id ? props.user._id : \"\",\r\n        },\r\n    };\r\n\r\n    const columns = [\r\n        {\r\n            name: t(\"Title\"),\r\n            selector: \"\",\r\n            cell: (d: any) =>\r\n                d.entity_id && d.entity_id.title ? (\r\n                    <Link href={`/${d.entity_type}/[...routes]`} as={`/${d.entity_type}/show/${d.entity_id._id}`}>\r\n                        {d.entity_id.title}\r\n                    </Link>\r\n                ) : (\r\n                    \"\"\r\n                ),\r\n        },\r\n        {\r\n            name: t(\"Group\"),\r\n            selector: \"group\",\r\n            cell: (d: any) => {\r\n                return d.onModel && d.onModel === \"Institution\" ? \"Organisation\" : d.onModel;\r\n            },\r\n        },\r\n        {\r\n            name: t(\"Remove\"),\r\n            selector: \"\",\r\n            cell: (d: any) => (\r\n                <div onClick={() => userAction(d)} style={{ cursor: \"pointer\" }}>\r\n                    <i className=\"icon fas fa-trash-alt\" />\r\n                </div>\r\n            ),\r\n        },\r\n    ];\r\n\r\n    const getUserData = async () => {\r\n        setLoading(true);\r\n        const response = await apiService.get(\"/flag\", flagParams);\r\n        if (response && response.data) {\r\n            setDataToTable(response.data);\r\n            setTotalRows(response.totalCount);\r\n            setLoading(false);\r\n        }\r\n    };\r\n\r\n    const handlePageChange = (page: any) => {\r\n        flagParams.limit = perPage;\r\n        flagParams.page = page;\r\n        if (filterText !== \"\") {\r\n            flagParams.query = { title: filterText };\r\n        }\r\n        const groupType1 = _.map(groupType, \"value\");\r\n        if (groupType1 && groupType1.length > 0) {\r\n            flagParams.query = {\r\n                ...flagParams.query,\r\n                entity_type: groupType1,\r\n            };\r\n        }\r\n        getUserData();\r\n    };\r\n\r\n    const handlePerRowsChange = async (newPerPage: any, page: any) => {\r\n        flagParams.limit = newPerPage;\r\n        flagParams.page = page;\r\n        setLoading(true);\r\n        const groupType1 = _.map(groupType, \"value\");\r\n        if (groupType1 && groupType1.length > 0) {\r\n            flagParams.query = {\r\n                ...flagParams.query,\r\n                entity_type: groupType1,\r\n            };\r\n        }\r\n        const response = await apiService.get(\"/flag\", flagParams);\r\n        if (response && response.data && response.data.length > 0) {\r\n            setDataToTable(response.data);\r\n            setPerPage(newPerPage);\r\n            setLoading(false);\r\n        }\r\n    };\r\n\r\n    useEffect(() => {\r\n        flagParams.page = 1;\r\n        getUserData();\r\n    }, []);\r\n\r\n    const subHeaderComponentMemo = React.useMemo(() => {\r\n        const handleClear = () => {\r\n            if (filterText) {\r\n                setResetPaginationToggle(!resetPaginationToggle);\r\n                setFilterText(\"\");\r\n            }\r\n        };\r\n\r\n        const handleGroupHandler = (selectedOptions: any) => {\r\n            setGroupType(selectedOptions);  // Update groupType with the selected values\r\n\r\n            const groupArray = _.map(selectedOptions, \"value\");\r\n\r\n            // Check if no groups are selected\r\n            if (groupArray.length === 0) {\r\n                // Clear data if no options are selected\r\n                setDataToTable([]);  // Clear the table data\r\n            } else {\r\n                // Proceed with fetching data if there are selected groups\r\n                flagParams.query = {\r\n                    ...flagParams.query,\r\n                    entity_type: groupArray,\r\n                };\r\n                getUserData(); // Fetch the data based on the selected groups\r\n            }\r\n        };\r\n\r\n        const sendQuery = (q: any) => {\r\n            if (q) {\r\n                flagParams.populate.match = { title: { $regex: q } };\r\n                getUserData();\r\n            }\r\n        };\r\n\r\n        const handleSearchTitle = _.debounce((q: string) => sendQuery(q), Number(process.env.SEARCH_DEBOUNCE_TIME) || 300);\r\n\r\n        const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {\r\n            setFilterText(e.target.value);\r\n            handleSearchTitle(e.target.value);\r\n        };\r\n\r\n        return (\r\n            <BookmarkTableFilter\r\n                onFilter={handleChange}\r\n                onClear={handleClear}\r\n                filterText={filterText}\r\n                handleGroupHandler={handleGroupHandler}\r\n                groupType={groupType}\r\n                options={GROUP}\r\n            />\r\n        );\r\n    }, [filterText, groupType, resetPaginationToggle]);\r\n\r\n    const modalHide = () => setModal(false);\r\n\r\n    const modalConfirm = async () => {\r\n        setModal(false);\r\n\r\n        await apiService.remove(`/flag/${selectedGroup}`);\r\n        //set Filter Text\r\n        if (groupType && Array.isArray(groupType)) {\r\n            const groupArray = _.map(groupType, \"value\");\r\n            if (groupArray && groupArray.length > 0) {\r\n                flagParams.query = {\r\n                    ...flagParams.query,\r\n                    entity_type: groupArray,\r\n                };\r\n            }\r\n        }\r\n        getUserData();\r\n    };\r\n\r\n    return (\r\n        <div className=\"my-bookmark-table\">\r\n            <Modal show={isModalShow} onHide={modalHide}>\r\n                <Modal.Header closeButton>\r\n                    <Modal.Title>{t(\"Removebookmark\")}</Modal.Title>\r\n                </Modal.Header>\r\n                <Modal.Body>{t(\"Areyousurewanttoremovefromyourbookmark\")}</Modal.Body>\r\n                <Modal.Footer>\r\n                    <Button variant=\"secondary\" onClick={modalHide}>\r\n                        {t(\"Cancel\")}\r\n                    </Button>\r\n                    <Button variant=\"primary\" onClick={modalConfirm}>\r\n                        {t(\"bookmarkDeleteYes\")}\r\n                    </Button>\r\n                </Modal.Footer>\r\n            </Modal>\r\n            <RKITable\r\n                columns={columns}\r\n                data={tabledata}\r\n                totalRows={totalRows}\r\n                subheader\r\n                pagServer={true}\r\n                resetPaginationToggle={resetPaginationToggle}\r\n                subHeaderComponent={subHeaderComponentMemo}\r\n                handlePerRowsChange={handlePerRowsChange}\r\n                handlePageChange={handlePageChange}\r\n            />\r\n        </div>\r\n    );\r\n};\r\n\r\nexport default connect((state) => state)(BookmarkTable);\r\n", "//Import Library\r\nimport React, { useState } from \"react\";\r\nimport { Form, Modal, Alert } from \"react-bootstrap\";\r\n\r\n//Import services/components\r\nimport Confirmation from \"./confirmation\";\r\nimport { useTranslation } from 'next-i18next';\r\n\r\nconst MyConsent = ({ isOpen, manageClose, id } : { isOpen: any, manageClose: any, id: any }) => {\r\n  const { t } = useTranslation('common');\r\n  const consentIntialState = {\r\n    consent1: true,\r\n    consent2: true,\r\n    consent3: true,\r\n    consent4: true,\r\n  };\r\n  const [consents, setConsents] = useState(consentIntialState);\r\n  const [warningDialog, setWarningDialog] = useState(false);\r\n\r\n  const consentHandler = (e: React.ChangeEvent<HTMLInputElement>) => {\r\n    const { name, checked } = e.target;\r\n    setConsents((prevState) => ({ ...prevState, [name]: checked }));\r\n    setWarningDialog(!warningDialog);\r\n  };\r\n  const closeHandler = (val: any) => {\r\n    setConsents(consentIntialState);\r\n    setWarningDialog(val);\r\n  };\r\n\r\n  const modalCloseHandler = () => {\r\n    manageClose(false);\r\n  };\r\n  return (\r\n    <>\r\n      <Modal\r\n        show={isOpen}\r\n        onHide={modalCloseHandler}\r\n        size=\"xl\"\r\n        id=\"main-content\"\r\n        className=\"w-100\"\r\n      >\r\n        <Modal.Header closeButton>\r\n          <Modal.Title>{t(\"declaration.title\")}</Modal.Title>\r\n        </Modal.Header>\r\n        <Modal.Body>\r\n          <div className=\"p-3 w-100\">\r\n            <Alert variant=\"danger\">{t(\"declaration.info\")}</Alert>\r\n            <Form.Check\r\n              className=\"pb-4\"\r\n              type=\"checkbox\"\r\n              name=\"consent1\"\r\n              onChange={consentHandler}\r\n              checked={consents.consent1}\r\n              value=\"consent1\"\r\n              label={t(\"declaration.consent1\")}\r\n            />\r\n             <Form.Check\r\n              className=\"pb-4\"\r\n              name=\"consent2\"\r\n              onChange={consentHandler}\r\n              type=\"checkbox\"\r\n              checked={consents.consent2}\r\n              value=\"consent2\"\r\n              label={t(\"declaration.consent2\")}\r\n            />\r\n            <Form.Check\r\n              className=\"pb-4\"\r\n              name=\"consent3\"\r\n              onChange={consentHandler}\r\n              type=\"checkbox\"\r\n              checked={consents.consent3}\r\n              value=\"consent3\"\r\n              label={t(\"declaration.consent3\")}\r\n            />\r\n            <Form.Check\r\n              className=\"pb-4\"\r\n              type=\"checkbox\"\r\n              name=\"consent4\"\r\n              value=\"consent4\"\r\n              label={t(\"declaration.consent4\")}\r\n              checked={consents.consent4}\r\n              onChange={consentHandler}\r\n            />       \r\n          </div>\r\n        </Modal.Body>\r\n        <Confirmation\r\n          endpoint=\"/users\"\r\n          userId={id ? id : \"\"}\r\n          isopen={warningDialog}\r\n          manageDialog={(val: any) => closeHandler(val)}\r\n        />\r\n      </Modal>\r\n    </>\r\n  );\r\n};\r\n\r\nexport default MyConsent;\r\n", "//Import Library\r\nimport React from \"react\";\r\nimport { <PERSON><PERSON>, But<PERSON> } from \"react-bootstrap\";\r\nimport { FontAwesomeIcon } from \"@fortawesome/react-fontawesome\";\r\nimport {\r\n    faExclamationTriangle\r\n} from \"@fortawesome/free-solid-svg-icons\";\r\nimport Router from 'next/router';\r\n\r\n//Import services/components\r\nimport apiService from '../../services/apiService';\r\nimport { useTranslation } from 'next-i18next';\r\n\r\n\r\nconst Confirmation = (props: any) => {\r\n    const { isopen, manageDialog, userId, endpoint } = props;\r\n    const { t } = useTranslation('common');\r\n\r\n    const handleClose = () => {\r\n        manageDialog(false)\r\n    }\r\n\r\n    const accountDeleteHandlder = async () => {\r\n        let response;\r\n        if (endpoint === \"/users\") {\r\n            response = await apiService.remove(`${endpoint}/${userId}`);\r\n        } else {\r\n            response = await apiService.post(`${endpoint}`, { code: userId })\r\n        }\r\n\r\n        if (response) {\r\n            manageDialog(false);\r\n            Router.push('/home');\r\n        }\r\n    }\r\n\r\n    return (\r\n        <Modal show={isopen} onHide={handleClose}>\r\n            <div className=\"text-center p-2\">\r\n                <FontAwesomeIcon\r\n                    icon={faExclamationTriangle}\r\n                    size=\"5x\"\r\n                    color=\"indianRed\"\r\n                    style={{\r\n                        background: \"#d6deec\",\r\n                        padding: \"19px\",\r\n                        borderRadius: \"50%\",\r\n                        width: \"100px\",\r\n                        height: \"100px\"\r\n                    }}\r\n                />\r\n            </div>\r\n            <Modal.Body><b>{t(\"AreyousureyouwishtoleavetheKnowledgePlatform\")}</b></Modal.Body>\r\n            <Modal.Footer>\r\n                <Button variant=\"secondary\" onClick={handleClose}>\r\n                {t(\"No\")}\r\n          </Button>\r\n                <Button variant=\"danger\" onClick={accountDeleteHandlder}>\r\n                {t(\"YesDeleteMe\")}\r\n          </Button>\r\n            </Modal.Footer>\r\n        </Modal>\r\n    )\r\n}\r\n\r\n\r\n\r\nexport default Confirmation;", "interface PageHeadingProps {\r\n  title: string; // Required based on actual usage\r\n}\r\n\r\nexport default function PageHeading(props: PageHeadingProps) {\r\n  return (\r\n    <h2 className=\"page-heading\">{props.title}</h2>\r\n  )\r\n}\r\n", "\n    (window.__NEXT_P = window.__NEXT_P || []).push([\n      \"/profile\",\n      function () {\n        return require(\"private-next-pages/profile/index.tsx\");\n      }\n    ]);\n    if(module.hot) {\n      module.hot.dispose(function () {\n        window.__NEXT_P.push([\"/profile\"])\n      });\n    }\n  "], "names": ["isOpen", "ImageEditor", "onModalClose", "image", "t", "useTranslation", "file", "scale", "setScale", "useState", "rotate", "setRotate", "name", "setName", "img", "setImg", "editor<PERSON><PERSON>", "useRef", "<PERSON><PERSON><PERSON><PERSON>", "blob", "dataURLtoBlob", "arr", "dataurl", "split", "canvas", "mimeMatch", "match", "mime", "bstr", "atob", "n", "length", "u8arr", "Uint8Array", "charCodeAt", "Blob", "type", "current", "getImageScaledToCanvas", "toDataURL", "fd", "FormData", "append", "res", "apiService", "post", "_id", "toast", "success", "error", "Error", "div", "Modal", "show", "size", "aria-<PERSON>by", "onHide", "centered", "Header", "Title", "id", "Body", "className", "AvatarEditor", "ref", "borderRadius", "color", "Row", "Col", "sm", "md", "lg", "b", "input", "accept", "onChange", "e", "fileHandler", "target", "files", "URL", "createObjectURL", "label", "data-browse", "RangeSlider", "value", "tooltip", "min", "max", "step", "variant", "Number", "changeEvent", "tooltipLabel", "currentValue", "Footer", "<PERSON><PERSON>", "onClick", "connect", "Profile", "setShow", "consentShow", "setConsentShow", "modal", "setModal", "<PERSON><PERSON><PERSON><PERSON>", "val", "userData", "setUserData", "intialState", "username", "institution", "email", "firstname", "lastname", "position", "useEffect", "getUser", "userParams", "response", "process", "prevState", "modalClose", "props", "dispatch", "loadLoggedinUserData", "Container", "fluid", "xs", "PageHeading", "title", "Card", "src", "userdata_func", "Spinner", "animation", "Form", "Group", "as", "controlId", "Label", "column", "span", "Control", "plaintext", "readOnly", "defaultValue", "FontAwesomeIcon", "icon", "faCheckSquare", "imageEditorHandler", "faEdit", "handleShow", "ProfileEdit", "data", "manageClose", "BookmarkTable", "MyConsent", "filterText", "BookmarkTableFilter", "onFilter", "onClear", "handleGroupHandler", "groupType", "options", "style", "max<PERSON><PERSON><PERSON>", "MultiSelect", "overrideStrings", "selectSomeItems", "allItemsAreSelected", "labelledBy", "RKITable", "paginationComponentOptions", "rowsPerPageText", "columns", "totalRows", "resetPaginationToggle", "subheader", "subHeaderComponent", "handlePerRowsChange", "handlePageChange", "rowsPerPage", "defaultRowsPerPage", "selectableRows", "loading", "pagServer", "onSelectedRowsChange", "clearSelectedRows", "sortServer", "onSort", "persistTableHead", "sortFunction", "otherProps", "dataTableProps", "noDataComponent", "<PERSON><PERSON><PERSON><PERSON>", "dense", "paginationResetDefaultPage", "subHeader", "progressPending", "pagination", "paginationServer", "paginationPerPage", "paginationRowsPerPageOptions", "paginationTotalRows", "onChangeRowsPerPage", "onChangePage", "progressComponent", "CustomLoader", "sortIcon", "i", "DataTable", "defaultProps", "state", "GROUP", "tabledata", "setDataToTable", "setLoading", "isModalShow", "setTotalRows", "perPage", "setPerPage", "setFilterText", "setResetPaginationToggle", "setGroupType", "selectedGroup", "setSelectedGroup", "userAction", "group", "flagParams", "sort", "created_at", "populate", "path", "select", "lean", "limit", "page", "query", "user", "selector", "cell", "d", "entity_id", "Link", "href", "entity_type", "onModel", "cursor", "getUserData", "get", "totalCount", "newPerPage", "groupType1", "_", "subHeaderComponentMemo", "React", "<PERSON><PERSON><PERSON><PERSON>", "q", "$regex", "handleSearchTitle", "handleChange", "handleClear", "selectedOptions", "groupArray", "modalHide", "modalConfirm", "remove", "Array", "isArray", "closeButton", "consentIntialState", "consent1", "consent2", "consent3", "consent4", "consents", "setConsents", "warningDialog", "setWarningDialog", "<PERSON><PERSON><PERSON><PERSON>", "checked", "modalCloseHandler", "<PERSON><PERSON>", "Check", "Confirmation", "endpoint", "userId", "isopen", "manageDialog", "handleClose", "accountDeleteHandlder", "code", "Router", "faExclamationTriangle", "background", "padding", "width", "height", "h2"], "sourceRoot": "", "ignoreList": []}