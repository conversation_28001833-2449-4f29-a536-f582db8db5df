"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[4192],{26188:(e,a,s)=>{s.d(a,{A:()=>v});var r=s(15039),l=s.n(r),n=s(14232),t=s(22631),i=s(76959),o=s(77346),d=s(46052),c=s(37876);let m=(0,d.A)("h4");m.displayName="DivStyledAsH4";let u=n.forwardRef((e,a)=>{let{className:s,bsPrefix:r,as:n=m,...t}=e;return r=(0,o.oU)(r,"alert-heading"),(0,c.jsx)(n,{ref:a,className:l()(s,r),...t})});u.displayName="AlertHeading";var h=s(10401);let x=n.forwardRef((e,a)=>{let{className:s,bsPrefix:r,as:n=h.A,...t}=e;return r=(0,o.oU)(r,"alert-link"),(0,c.jsx)(n,{ref:a,className:l()(s,r),...t})});x.displayName="AlertLink";var p=s(72788),j=s(12054);let f=n.forwardRef((e,a)=>{let{bsPrefix:s,show:r=!0,closeLabel:n="Close alert",closeVariant:d,className:m,children:u,variant:h="primary",onClose:x,dismissible:f,transition:v=p.A,...A}=(0,t.Zw)(e,{show:"onClose"}),y=(0,o.oU)(s,"alert"),g=(0,i.A)(e=>{x&&x(!1,e)}),b=!0===v?p.A:v,C=(0,c.jsxs)("div",{role:"alert",...!b?A:void 0,ref:a,className:l()(m,y,h&&"".concat(y,"-").concat(h),f&&"".concat(y,"-dismissible")),children:[f&&(0,c.jsx)(j.A,{onClick:g,"aria-label":n,variant:d}),u]});return b?(0,c.jsx)(b,{unmountOnExit:!0,...A,ref:void 0,in:r,children:C}):r?C:null});f.displayName="Alert";let v=Object.assign(f,{Link:x,Heading:u})},29335:(e,a,s)=>{s.d(a,{A:()=>b});var r=s(15039),l=s.n(r),n=s(14232),t=s(77346),i=s(37876);let o=n.forwardRef((e,a)=>{let{className:s,bsPrefix:r,as:n="div",...o}=e;return r=(0,t.oU)(r,"card-body"),(0,i.jsx)(n,{ref:a,className:l()(s,r),...o})});o.displayName="CardBody";let d=n.forwardRef((e,a)=>{let{className:s,bsPrefix:r,as:n="div",...o}=e;return r=(0,t.oU)(r,"card-footer"),(0,i.jsx)(n,{ref:a,className:l()(s,r),...o})});d.displayName="CardFooter";var c=s(81764);let m=n.forwardRef((e,a)=>{let{bsPrefix:s,className:r,as:o="div",...d}=e,m=(0,t.oU)(s,"card-header"),u=(0,n.useMemo)(()=>({cardHeaderBsPrefix:m}),[m]);return(0,i.jsx)(c.A.Provider,{value:u,children:(0,i.jsx)(o,{ref:a,...d,className:l()(r,m)})})});m.displayName="CardHeader";let u=n.forwardRef((e,a)=>{let{bsPrefix:s,className:r,variant:n,as:o="img",...d}=e,c=(0,t.oU)(s,"card-img");return(0,i.jsx)(o,{ref:a,className:l()(n?"".concat(c,"-").concat(n):c,r),...d})});u.displayName="CardImg";let h=n.forwardRef((e,a)=>{let{className:s,bsPrefix:r,as:n="div",...o}=e;return r=(0,t.oU)(r,"card-img-overlay"),(0,i.jsx)(n,{ref:a,className:l()(s,r),...o})});h.displayName="CardImgOverlay";let x=n.forwardRef((e,a)=>{let{className:s,bsPrefix:r,as:n="a",...o}=e;return r=(0,t.oU)(r,"card-link"),(0,i.jsx)(n,{ref:a,className:l()(s,r),...o})});x.displayName="CardLink";var p=s(46052);let j=(0,p.A)("h6"),f=n.forwardRef((e,a)=>{let{className:s,bsPrefix:r,as:n=j,...o}=e;return r=(0,t.oU)(r,"card-subtitle"),(0,i.jsx)(n,{ref:a,className:l()(s,r),...o})});f.displayName="CardSubtitle";let v=n.forwardRef((e,a)=>{let{className:s,bsPrefix:r,as:n="p",...o}=e;return r=(0,t.oU)(r,"card-text"),(0,i.jsx)(n,{ref:a,className:l()(s,r),...o})});v.displayName="CardText";let A=(0,p.A)("h5"),y=n.forwardRef((e,a)=>{let{className:s,bsPrefix:r,as:n=A,...o}=e;return r=(0,t.oU)(r,"card-title"),(0,i.jsx)(n,{ref:a,className:l()(s,r),...o})});y.displayName="CardTitle";let g=n.forwardRef((e,a)=>{let{bsPrefix:s,className:r,bg:n,text:d,border:c,body:m=!1,children:u,as:h="div",...x}=e,p=(0,t.oU)(s,"card");return(0,i.jsx)(h,{ref:a,...x,className:l()(r,p,n&&"bg-".concat(n),d&&"text-".concat(d),c&&"border-".concat(c)),children:m?(0,i.jsx)(o,{children:u}):u})});g.displayName="Card";let b=Object.assign(g,{Img:u,Title:y,Subtitle:f,Body:o,Link:x,Text:v,Header:m,Footer:d,ImgOverlay:h})},35611:(e,a,s)=>{s.d(a,{sx:()=>d,s3:()=>l.s3,ks:()=>l.ks,yk:()=>r.A});var r=s(54773),l=s(59200),n=s(37876),t=s(14232),i=s(39593),o=s(29504);let d={RadioGroup:e=>{let{name:a,valueSelected:s,onChange:r,errorMessage:l,children:o}=e,{errors:d,touched:c}=(0,i.j7)(),m=c[a]&&d[a];t.useMemo(()=>({name:a}),[a]);let u=t.Children.map(o,e=>t.isValidElement(e)&&function(e){return"object"==typeof e&&null!==e}(e.props)?t.cloneElement(e,{name:a,...e.props}):e);return(0,n.jsxs)("div",{children:[(0,n.jsx)("div",{className:"radio-group",children:u}),m&&(0,n.jsx)("div",{className:"invalid-feedback d-block",children:l||("string"==typeof d[a]?d[a]:String(d[a]))})]})},RadioItem:e=>{let{id:a,label:s,value:r,name:l,disabled:t}=e,{values:d,setFieldValue:c}=(0,i.j7)(),m=l||a;return(0,n.jsx)(o.A.Check,{type:"radio",id:a,label:s,value:r,name:m,checked:d[m]===r,onChange:e=>{c(m,e.target.value)},disabled:t,inline:!0})}};r.A,l.ks,l.s3},54192:(e,a,s)=>{s.r(a),s.d(a,{default:()=>N});var r=s(37876),l=s(14232),n=s(31777),t=s(29335),i=s(26188),o=s(29504),d=s(60282),c=s(56970),m=s(37784),u=s(21772),h=s(11041),x=s(35611),p=s(54773),j=s(89099),f=s.n(j),v=s(97685),A=s(19957),y=s(61862),g=s(53718),b=s(27794),C=s(31753);let N=(0,n.Ng)()(e=>{let{t:a,i18n:s}=(0,C.Bd)("common"),n="de"===s.language?{title_de:"asc"}:{title:"asc"},j=s.language,{authToken:N,userProfile:w}=e,[k,P]=(0,l.useState)(!1),[I,_]=(0,l.useState)({dataConsentPolicy:!1,restrictedUsePolicy:!1,acceptCookiesPolicy:!1,medicalConsentPolicy:!1}),[R,E]=(0,l.useState)(!0),[S,U]=(0,l.useState)(!1),[q,L]=(0,l.useState)({username:"",email:"",dial_code:"",firstname:"",lastname:"",position:"",mobile_number:"",password:"",confirm_password:""}),[D,F]=(0,l.useState)([]);(0,l.useEffect)(()=>{(async e=>{let a=await g.A.get("/country",e);a&&Array.isArray(a.data)&&F(a.data)})({query:{},sort:n,limit:"~",languageCode:j})},[]);let T=e=>{let{name:a,checked:s}=e.target;_(e=>({...e,[a]:s}))};(0,l.useEffect)(()=>{s.changeLanguage(w&&w.languageCode);let e=w&&w.dial_code?w.dial_code:"";w&&L(a=>({...a,...w,password:"",dial_code:e}))},[w]),(0,l.useEffect)(()=>{I.dataConsentPolicy&&I.restrictedUsePolicy&&I.acceptCookiesPolicy&&I.medicalConsentPolicy?E(!1):(U(!1),E(!0))},[I,_,S]);let B=()=>{U(!S)},G=e=>{P(e)},H=e=>{let{name:a,value:s}=e.target;L(e=>({...e,[a]:s}))},M=async s=>{s.preventDefault();let r={code:N,username:q.username.toLowerCase().trim(),firstname:q.firstname,lastname:q.lastname,email:q.email.toLowerCase().trim(),dial_code:q.dial_code,mobile_number:q.mobile_number,password:q.password,enabled:!0,...I},l=await g.A.post("/saveInviteUserDetails",r);if(l&&l._id){let{auth:s}=b.A;l.roles.includes("SUPER_ADMIN");let n=await s({username:r.username,password:r.password});if(n&&201===n.status)v.Ay.success(a("toast.AccountcreatedSuccesfully")),f().push("/"),e.dispatch((0,A.js)());else{let e=n.status+" "+n.statusText;v.Ay.error(e),f().push("/home")}}};return(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)("div",{className:"text-center mt-2",children:(0,r.jsx)("img",{src:"/images/logo.jpg",alt:"Rohert Koch Institut - Logo"})}),(0,r.jsx)("div",{className:"d-flex justify-content-center align-items-center",children:!S&&(0,r.jsxs)(t.A,{className:"px-3 declarationcard",children:[(0,r.jsx)("p",{className:"lead px-3 pt-3 mb-0",children:(0,r.jsx)("b",{children:a("declaration.header")})}),(0,r.jsx)("hr",{className:"hr--cardfront"}),(0,r.jsxs)(t.A.Body,{children:[(0,r.jsx)(i.A,{variant:"success",className:"mt-0",children:a("declaration.info")}),(0,r.jsx)(o.A.Check,{className:"pb-4",type:"checkbox",onChange:T,name:"dataConsentPolicy",checked:I.dataConsentPolicy,value:"consent1",label:a("declaration.consent1")}),(0,r.jsx)(o.A.Check,{className:"pb-4",onChange:T,type:"checkbox",name:"medicalConsentPolicy",checked:I.medicalConsentPolicy,value:"consent2",label:a("declaration.consent2")}),(0,r.jsx)(o.A.Check,{className:"pb-4",onChange:T,type:"checkbox",name:"restrictedUsePolicy",checked:I.restrictedUsePolicy,value:"consent3",label:a("declaration.consent3")}),(0,r.jsx)(o.A.Check,{className:"pb-4",type:"checkbox",name:"acceptCookiesPolicy",onChange:T,checked:I.acceptCookiesPolicy,value:"consent4",label:a("declaration.consent4")}),(0,r.jsxs)("div",{className:"d-flex",children:[R&&(0,r.jsx)(d.A,{onClick:()=>{P(!k)},variant:"danger",className:"d-grid w-100",children:a("declaration.decline")}),!R&&(0,r.jsx)(d.A,{variant:"success",className:"mt-0 d-grid w-100",onClick:B,children:a("declaration.accept")})]})]})]})}),S&&(0,r.jsx)("div",{className:"d-flex justify-content-center align-items-center w-100",id:"main-content",children:(0,r.jsx)(p.A,{onSubmit:M,initialValues:q,enableReinitialize:!0,children:(0,r.jsxs)(t.A,{className:"declarationcard",children:[(0,r.jsxs)("div",{className:"d-flex align-items-center ms-3",children:[(0,r.jsx)(u.g,{icon:h.f3N,onClick:B,size:"2x",className:"icon--arrow"}),(0,r.jsx)("p",{className:"lead px-3 pt-3 pb-0 mb-0",children:(0,r.jsx)("b",{children:a("setInfo.header")})})]}),(0,r.jsx)("hr",{className:"hr--cardback"}),(0,r.jsxs)(i.A,{className:"mx-3 mb-0 mt-3",variant:"warning",children:[(0,r.jsx)(u.g,{icon:h.zpE}),"\xa0",a("setInfo.info")]}),(0,r.jsxs)(t.A.Body,{children:[(0,r.jsx)(c.A,{children:(0,r.jsx)(m.A,{children:(0,r.jsxs)(o.A.Group,{as:c.A,controlId:"username",children:[(0,r.jsx)(o.A.Label,{className:"required-field",column:!0,sm:"3",children:a("setInfo.username")}),(0,r.jsx)(m.A,{sm:"9",children:(0,r.jsx)(x.ks,{className:"form-control",name:"username",id:"username",errorMessage:"Please enter your username",placeholder:"Enter your username",type:"text",required:!0,value:q.username,onChange:H})})]})})}),(0,r.jsx)(c.A,{children:(0,r.jsx)(m.A,{children:(0,r.jsxs)(o.A.Group,{as:c.A,controlId:"name",children:[(0,r.jsx)(o.A.Label,{className:"required-field",column:!0,sm:"3",children:a("setInfo.name")}),(0,r.jsx)(m.A,{children:(0,r.jsx)(x.ks,{className:"form-control",name:"firstname",id:"firstname",type:"text",required:!0,errorMessage:"Please enter your first name",placeholder:"Enter your first name",value:q.firstname,onChange:H})}),(0,r.jsx)(m.A,{children:(0,r.jsx)(x.ks,{className:"form-control",name:"lastname",id:"lastname",type:"text",placeholder:"Enter your last name",value:q.lastname,onChange:H})})]})})}),(0,r.jsx)(c.A,{children:(0,r.jsx)(m.A,{children:(0,r.jsxs)(o.A.Group,{as:c.A,controlId:"position",children:[(0,r.jsx)(o.A.Label,{column:!0,sm:"3",children:a("setInfo.position")}),(0,r.jsx)(m.A,{sm:"9",children:(0,r.jsx)(x.ks,{className:"form-control",name:"position",id:"position",type:"text",placeholder:"Enter the position",value:q.position,onChange:H})})]})})}),(0,r.jsx)(c.A,{children:(0,r.jsx)(m.A,{children:(0,r.jsxs)(o.A.Group,{as:c.A,controlId:"Email",children:[(0,r.jsx)(o.A.Label,{className:"required-field",column:!0,sm:"3",children:a("setInfo.email")}),(0,r.jsx)(m.A,{sm:"9",children:(0,r.jsx)(x.ks,{name:"email",id:"email",placeholder:"Enter your email",type:"text",required:!0,disabled:!0,value:q.email,onChange:H})})]})})}),(0,r.jsx)(c.A,{children:(0,r.jsx)(m.A,{children:(0,r.jsxs)(o.A.Group,{as:c.A,controlId:"mobile_number",children:[(0,r.jsx)(o.A.Label,{column:!0,sm:"3",children:a("setInfo.phno")}),(0,r.jsx)(m.A,{children:(0,r.jsxs)(x.s3,{name:"dial_code",id:"dialCode",value:q.dial_code,onChange:H,style:{backgroundColor:"inherit",borderRadius:"5px",color:"#495057"},children:[(0,r.jsx)("option",{value:"",children:"Dial Code"}),D.map((e,a)=>(0,r.jsx)("option",{value:e.dial_code,children:"(".concat(e.dial_code,") ").concat(e.title)},a))]})}),(0,r.jsx)(m.A,{children:(0,r.jsx)(x.ks,{name:"mobile_number",id:"mobile_number",type:"text",placeholder:"Enter the phone number",value:q.mobile_number,onChange:H})})]})})}),(0,r.jsx)(c.A,{children:(0,r.jsx)(m.A,{children:(0,r.jsxs)(o.A.Group,{as:c.A,controlId:"Password",children:[(0,r.jsx)(o.A.Label,{className:"required-field",column:!0,sm:"3",children:a("setInfo.password")}),(0,r.jsx)(m.A,{sm:"9",children:(0,r.jsx)(x.ks,{name:"password",id:"password",type:"password",pattern:"^(?=.*?[A-Z])(?=.*?[a-z])(?=.*?[0-9])(?=.*?[#?!@$%^&*-]).{8,}$",errorMessage:{required:"Please enter the password",pattern:"Password should contain at least 8 characters, with at least one digit, one letter in upper case & one special character"},value:q.password,onChange:H,required:!0})})]})})}),(0,r.jsx)(c.A,{children:(0,r.jsx)(m.A,{children:(0,r.jsxs)(o.A.Group,{as:c.A,controlId:"Password",children:[(0,r.jsx)(o.A.Label,{className:"required-field",column:!0,sm:"3",children:a("setInfo.confirmpassword")}),(0,r.jsx)(m.A,{sm:"9",children:(0,r.jsx)(x.ks,{name:"confirm_password",id:"confirm_password",type:"password",validator:e=>e===q.password,errorMessage:{required:"Confirm password is required",validator:"Password does not match"},required:!0,value:q.confirm_password,onChange:H})})]})})}),(0,r.jsx)("div",{className:"d-flex justify-content-end",children:(0,r.jsxs)(d.A,{className:"w-20",variant:"success",type:"submit",children:[a("setInfo.accept"),"\xa0\xa0",(0,r.jsx)(u.g,{icon:h.vh1,color:"white"})]})})]})]})})}),(0,r.jsx)(y.default,{userId:N,endpoint:"/deleteUser",isopen:k,manageDialog:e=>G(e)})]})})},54773:(e,a,s)=>{s.d(a,{A:()=>o});var r=s(37876),l=s(14232),n=s(39593),t=s(91408);let i=(0,l.forwardRef)((e,a)=>{let{children:s,onSubmit:l,autoComplete:i,className:o,onKeyPress:d,initialValues:c,...m}=e,u=t.Ik().shape({});return(0,r.jsx)(n.l1,{initialValues:c||{},validationSchema:u,onSubmit:(e,a)=>{let s={preventDefault:()=>{},stopPropagation:()=>{},currentTarget:null,target:null,nativeEvent:new Event("submit"),bubbles:!1,cancelable:!0,defaultPrevented:!1,eventPhase:0,isTrusted:!1,timeStamp:Date.now(),type:"submit",isDefaultPrevented:()=>!1,isPropagationStopped:()=>!1,persist:()=>{}};l&&l(s,e,a)},...m,children:e=>(0,r.jsx)(n.lV,{ref:a,onSubmit:e.handleSubmit,autoComplete:i,className:o,onKeyPress:d,children:"function"==typeof s?s(e):s})})});i.displayName="ValidationFormWrapper";let o=i},59200:(e,a,s)=>{s.d(a,{ks:()=>t,s3:()=>i});var r=s(37876);s(14232);var l=s(29504),n=s(39593);let t=e=>{let{name:a,id:s,required:t,validator:i,errorMessage:o,onChange:d,value:c,as:m,multiline:u,rows:h,pattern:x,...p}=e;return(0,r.jsx)(n.D0,{name:a,validate:e=>{let a="string"==typeof e?e:String(e||"");return t&&(!e||""===a.trim())?(null==o?void 0:o.validator)||"This field is required":i&&!i(e)?(null==o?void 0:o.validator)||"Invalid value":x&&e&&!new RegExp(x).test(e)?(null==o?void 0:o.pattern)||"Invalid format":void 0},children:e=>{let{field:a,meta:n}=e;return(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(l.A.Control,{...a,...p,id:s,as:m||"input",rows:h,isInvalid:n.touched&&!!n.error,onChange:e=>{a.onChange(e),d&&d(e)},value:void 0!==c?c:a.value}),n.touched&&n.error?(0,r.jsx)(l.A.Control.Feedback,{type:"invalid",children:n.error}):null]})}})},i=e=>{let{name:a,id:s,required:t,errorMessage:i,onChange:o,value:d,children:c,...m}=e;return(0,r.jsx)(n.D0,{name:a,validate:e=>{if(t&&(!e||""===e))return(null==i?void 0:i.validator)||"This field is required"},children:e=>{let{field:a,meta:n}=e;return(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(l.A.Control,{as:"select",...a,...m,id:s,isInvalid:n.touched&&!!n.error,onChange:e=>{a.onChange(e),o&&o(e)},value:void 0!==d?d:a.value,children:c}),n.touched&&n.error?(0,r.jsx)(l.A.Control.Feedback,{type:"invalid",children:n.error}):null]})}})}},61862:(e,a,s)=>{s.r(a),s.d(a,{default:()=>u});var r=s(37876);s(14232);var l=s(31195),n=s(60282),t=s(21772),i=s(11041),o=s(89099),d=s.n(o),c=s(53718),m=s(31753);let u=e=>{let{isopen:a,manageDialog:s,userId:o,endpoint:u}=e,{t:h}=(0,m.Bd)("common"),x=()=>{s(!1)},p=async()=>{let e;("/users"===u?await c.A.remove("".concat(u,"/").concat(o)):await c.A.post("".concat(u),{code:o}))&&(s(!1),d().push("/home"))};return(0,r.jsxs)(l.A,{show:a,onHide:x,children:[(0,r.jsx)("div",{className:"text-center p-2",children:(0,r.jsx)(t.g,{icon:i.zpE,size:"5x",color:"indianRed",style:{background:"#d6deec",padding:"19px",borderRadius:"50%",width:"100px",height:"100px"}})}),(0,r.jsx)(l.A.Body,{children:(0,r.jsx)("b",{children:h("AreyousureyouwishtoleavetheKnowledgePlatform")})}),(0,r.jsxs)(l.A.Footer,{children:[(0,r.jsx)(n.A,{variant:"secondary",onClick:x,children:h("No")}),(0,r.jsx)(n.A,{variant:"danger",onClick:p,children:h("YesDeleteMe")})]})]})}},81764:(e,a,s)=>{s.d(a,{A:()=>l});let r=s(14232).createContext(null);r.displayName="CardHeaderContext";let l=r}}]);
//# sourceMappingURL=4192-7932f4bf054b5d05.js.map