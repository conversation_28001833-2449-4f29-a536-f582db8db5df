"use strict";exports.id=9,exports.ids=[9],exports.modules={13822:(e,t,i)=>{i.a(e,async(e,a)=>{try{i.r(t),i.d(t,{default:()=>z});var r=i(8732),s=i(82015),l=i(16029),d=i(82053),n=i(54131),o=i(27825),c=i.n(o),p=i(63487),h=i(88751),x=e([n,p]);[n,p]=x.then?(await x)():x;let u=[],m="20971520",f={flex:1,display:"flex",flexDirection:"column",alignItems:"center",justifyContent:"center",width:"100%",height:"100%",borderWidth:.1,borderColor:"#fafafa",backgroundColor:"#fafafa",color:"black",transition:"border  .24s ease-in-out"},g={display:"inline-flex",borderRadius:2,border:"1px solid #ddd",marginBottom:8,marginRight:20,width:100,height:100,padding:2,position:"relative",boxShadow:"0 0 15px 0.25px rgba(0,0,0,0.25)",boxSizing:"border-box"},j={display:"flex",flexDirection:"row",justifyContent:"flex-start",flexWrap:"wrap",marginTop:20},b={display:"flex",minWidth:0,overflow:"hidden"},y={position:"absolute",fontSize:"22px",top:"-10px",right:"-10px",zIndex:1e3,cursor:"pointer",backgroundColor:"#fff",color:"#000",borderRadius:"50%"},A={display:"block",width:"auto",height:"100%"},v={borderColor:"#2196f3"},z=e=>{let{t}=(0,h.useTranslation)("common"),[i,a]=(0,s.useState)([]),o=async e=>{await p.A.remove(`/image/${e}`)},x=t=>{let r=t&&t._id?{serverID:t._id}:{file:t},s=c().findIndex(u,r);o(u[s].serverID),u.splice(s,1),e.getImgID(u,e.index?e.index:0);let l=[...i];l.splice(l.indexOf(t),1),a(l)},z=e=>(0,r.jsx)("img",{src:e.preview,style:A}),w=i.map((e,t)=>(0,r.jsx)("div",{children:(0,r.jsxs)("div",{style:g,children:[(0,r.jsx)("div",{style:b,children:z(e)}),(0,r.jsx)(d.FontAwesomeIcon,{icon:n.faTimesCircle,style:y,color:"black",onClick:()=>x(e)})]})},t));(0,s.useEffect)(()=>{i.forEach(e=>URL.revokeObjectURL(e.preview)),u=[]},[]),(0,s.useEffect)(()=>{if(e&&e.datas){let t=e.datas.map((t,i)=>(u.push({serverID:t._id,index:e.index?e.index:0}),{...t,preview:`http://localhost:3001/api/v1/image/show/${t._id}`}));a([...t])}},[e.datas]);let _=async(t,i)=>{if(t.length>i)try{let a=new FormData;a.append("file",t[i]);let r=await p.A.post("/image",a,{"Content-Type":"multipart/form-data"});u.push({serverID:r._id,file:t[i],index:e.index?e.index:0}),_(t,i+1)}catch(e){_(t,i+1)}else e.getImgID(u,e.index?e.index:0)},C=(0,s.useCallback)(e=>{_(e,0);let t=e.map((e,t)=>Object.assign(e,{preview:URL.createObjectURL(e)}));a([...t])},[]),{getRootProps:I,getInputProps:N,isDragActive:S,isDragAccept:k,isDragReject:D,fileRejections:T}=(0,l.useDropzone)({accept:"image/*",multiple:!1,minSize:0,maxSize:m,onDrop:C}),L=(0,s.useMemo)(()=>({...f,...S?v:{outline:"2px dashed #bbb"},...k?{outline:"2px dashed #595959"}:{outline:"2px dashed #bbb"},...D?{outline:"2px dashed red"}:{activeStyle:v}}),[S,D]),R=T.length>0&&T[0].file.size>m;return(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)("div",{className:" d-flex justify-content-center align-items-center mt-3",style:{width:"100%",height:"180px"},children:(0,r.jsxs)("div",{...I({style:L}),children:[(0,r.jsx)("input",{...N()}),(0,r.jsx)(d.FontAwesomeIcon,{icon:n.faCloudUploadAlt,size:"4x",color:"#999"}),(0,r.jsx)("p",{style:{color:"#595959",marginBottom:"0px"},children:t("Drag'n'dropsomefileshere,orclicktoselectfiles")}),(0,r.jsx)("small",{style:{color:"#595959"},children:t("ImageWeSupport")}),(0,r.jsxs)("small",{style:{color:"#595959"},children:[(0,r.jsx)("b",{children:t("Note:")})," ",t("Onesingleimagewillbeaccepted")]}),R&&(0,r.jsxs)("small",{className:"text-danger mt-2",children:[" ",(0,r.jsx)(d.FontAwesomeIcon,{icon:n.faExclamationCircle,size:"1x",color:"red"})," ",t("FileistoolargeItshouldbelessthan20MB")]}),D&&(0,r.jsxs)("small",{className:"text-danger",style:{color:"#595959"},children:[(0,r.jsx)(d.FontAwesomeIcon,{icon:n.faExclamationCircle,size:"1x",color:"red"})," ",t("Filetypenotacceptedsorr")]})]})}),(0,r.jsx)("div",{style:j,children:w})]})};a()}catch(e){a(e)}})},30009:(e,t,i)=>{i.a(e,async(e,a)=>{try{i.r(t),i.d(t,{default:()=>C});var r=i(8732),s=i(7082),l=i(18597),d=i(83551),n=i(49481),o=i(59549),c=i(25161),p=i(63899),h=i(91353),x=i(66994),u=i(23579),m=i(82015),f=i(42893),g=i(44233),j=i.n(g),b=i(19918),y=i.n(b),A=i(63487),v=i(13822),z=i(88751),w=i(24047),_=e([f,A,v]);[f,A,v]=_.then?(await _)():_;let C=e=>{let{t,i18n:i}=(0,z.useTranslation)("common"),a=i.language&&"fr"===i.language?"en":i.language,g={title:{en:"",fr:"",de:""},hazard_type:"",description:{},enabled:!0,picture:null,picture_source:"",images_src:[]},b=async()=>{let e=await A.A.get("/language",_);e&&R(e.data)},_={query:{},sort:{title:"asc"},limit:"~",select:"-_id -created_at -updated_at"},[C,I]=(0,m.useState)(g),[N,S]=(0,m.useState)([]),[,k]=(0,m.useState)([]),[D,T]=(0,m.useState)([]),[L,R]=(0,m.useState)([]),[F,U]=(0,m.useState)(i.language&&"fr"===i.language&&"en"===a?"fr":a),q=e.routes&&"edit_hazard"===e.routes[0]&&e.routes[1],E=async(i,a)=>{let r,s;i.preventDefault();let l=a||C,d={},n={};L&&L.map((e,t)=>{let i=e.abbr;d={...d,[i]:l.title[i]},n={...n,[i]:l.description[i]}}),d.en&&(d.de=d.en,d.fr=d.en),d.de&&(d.en=d.de,d.fr=d.de),d.fr&&(d.en=d.fr,d.de=d.fr);let o={title:{...d},hazard_type:l.hazard_type,description:{...n},enabled:l.enabled,picture:l.picture,picture_source:l.picture_source,images_src:l.images_src};q?(s="Hazardisupdatedsuccessfully",r=await A.A.patch(`/hazard/${e.routes[1]}`,o)):(s="Hazardisaddedsuccessfully",r=await A.A.post("/hazard",o)),r&&r._id?(f.default.success(t(s)),j().push("/adminsettings/hazard")):r?.errorCode===11e3?f.default.error(t("duplicatesNotAllowed")):f.default.error(r)},H=e=>{U(e.abbr)},O=e=>{if(e.target){let{name:t,value:i}=e.target;I(e=>({...e,[t]:i}))}},B=async e=>{let t=await A.A.get("/hazardtype",e);t&&T(t.data)},G=e=>{let t={...C.description,[F]:e};I(e=>({...e,description:t}))},M=async t=>{let i=await A.A.get(`/hazard/${e.routes[1]}`,t);if(i){let{hazard_type:e,picture:t}=i;i.hazard_type=e&&e._id?e._id:"",i.title&&"object"==typeof i.title?i.title={en:i.title.en||"",fr:i.title.fr||"",de:i.title.de||""}:i.title={en:"",fr:"",de:""},i.description&&"object"==typeof i.description?i.description={en:i.description.en||"",fr:i.description.fr||"",de:i.description.de||"",...i.description}:i.description={},null!=t&&S([t]),k(i.images_src?i.images_src:[]),I(e=>({...e,...i}))}};(0,m.useEffect)(()=>{b();let e={query:{},sort:{title:"asc"},limit:"~"};q&&M(e),B(e)},[]);let W=(0,m.useRef)(null),$=e=>{let t=e.map(e=>e.serverID);I(e=>({...e,picture:t}))};return(0,r.jsx)(s.A,{className:"formCard",fluid:!0,children:(0,r.jsx)(l.A,{style:{marginTop:"5px",boxShadow:"0 4px 8px 0 rgba(0, 0, 0, 0.2), 0 6px 20px 0 rgba(0, 0, 0, 0.19)"},children:(0,r.jsx)(x.A,{onSubmit:E,ref:W,initialValues:C,enableReinitialize:!0,children:(0,r.jsxs)(l.A.Body,{children:[(0,r.jsx)(d.A,{children:(0,r.jsx)(n.A,{children:(0,r.jsx)(l.A.Title,{children:e.routes&&"edit_hazard"===e.routes[0]?t("editHazard"):t("addHazard")})})}),(0,r.jsx)("hr",{}),(0,r.jsxs)(d.A,{className:"mb-3",children:[(0,r.jsx)(n.A,{md:!0,lg:6,sm:12,children:(0,r.jsxs)(o.A.Group,{children:[(0,r.jsx)(o.A.Label,{className:"required-field",children:t("hazardName")}),(0,r.jsx)(u.ks,{name:"title",id:"title",required:"en"===F,value:"fr"===F?C.title.en||"":C.title[F]||"",validator:e=>""!==String(C.title[F]||"").trim(),errorMessage:{validator:t("adminsetting.hazard.add")},onChange:e=>{let{name:t,value:i}=e.target,a={...C.title,[F]:i};I(e=>({...e,[t]:a}))}})]})}),(0,r.jsx)(n.A,{md:!0,lg:6,sm:12,children:(0,r.jsxs)(o.A.Group,{children:[(0,r.jsx)(o.A.Label,{className:"required-field d-flex me-3",children:t("hazardType")}),(0,r.jsxs)(u.s3,{name:"hazard_type",id:"hazard_type",required:!0,value:C.hazard_type,errorMessage:{validator:t("pleaseAddtheHazardType")},onChange:O,children:[(0,r.jsx)("option",{value:"",children:t("hazardTypeCategory.selectHazardType")}),D.length>=1?D.map((e,t)=>(0,r.jsx)("option",{value:e._id,children:e.title},e._id)):null]})]})})]}),(0,r.jsx)(d.A,{className:"mb-3",children:(0,r.jsxs)(n.A,{md:4,className:"d-flex",style:{marginTop:"10px"},children:[(0,r.jsx)(o.A.Label,{children:t("chooseLanguage")}),(0,r.jsx)(c.A,{title:F.toUpperCase(),variant:"outline-secondary",id:"basic-dropdown",className:"ms-2",children:L&&L.map((e,t)=>(0,r.jsx)("div",{children:(0,r.jsxs)(p.A.Item,{active:e.abbr===F,eventKey:e._id,onClick:()=>H(e),children:[e.abbr.toUpperCase(),"-",e.title.toUpperCase()]})},t))})]})}),(0,r.jsx)("br",{}),(0,r.jsx)(d.A,{children:(0,r.jsx)(o.A.Check,{className:" ms-4",type:"switch",name:"rki_monitored",id:"custom-switch",onChange:e=>{I(e=>({...e,enabled:!e.enabled}))},label:t("published"),checked:C.enabled})}),(0,r.jsx)("br",{}),(0,r.jsx)(d.A,{children:(0,r.jsx)(n.A,{children:(0,r.jsxs)(o.A.Group,{children:[(0,r.jsx)(o.A.Label,{children:t("description")}),(0,r.jsx)(w.x,{initContent:"fr"===F?C.description.en||"":C.description[F]||"",onChange:e=>G(e)})]})})}),(0,r.jsx)(d.A,{children:(0,r.jsx)(n.A,{children:(0,r.jsx)(v.default,{datas:N,getImgID:e=>$(e)})})}),(0,r.jsx)(d.A,{children:(0,r.jsx)(n.A,{children:(0,r.jsxs)(o.A.Group,{children:[(0,r.jsx)(o.A.Label,{children:t("imageSourceCredit")}),(0,r.jsx)(u.ks,{name:"picture_source",id:"picture_source",value:C.picture_source||"",onChange:O})]})})}),(0,r.jsx)(d.A,{className:"my-4",children:(0,r.jsxs)(n.A,{children:[(0,r.jsx)(h.A,{className:"me-2",type:"submit",variant:"primary",children:t("submit")}),(0,r.jsx)(h.A,{className:"me-2",onClick:()=>{I(g),S([]),k([]),window.scrollTo(0,0)},variant:"info",children:t("reset")}),(0,r.jsx)(y(),{href:"/adminsettings/[...routes]",as:"/adminsettings/hazard",children:(0,r.jsx)(h.A,{variant:"secondary",children:t("Cancel")})})]})})]})})})})};a()}catch(e){a(e)}})}};