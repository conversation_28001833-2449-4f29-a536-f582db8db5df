"use strict";(()=>{var e={};e.id=2808,e.ids=[636,2808,3220],e.modules={123:e=>{e.exports=require("dom-helpers/removeEventListener")},1332:e=>{e.exports=require("react-custom-scrollbars-2")},1428:e=>{e.exports=import("axios")},1680:e=>{e.exports=require("@restart/ui/utils")},1919:e=>{e.exports=require("react-transition-group/Transition")},3892:e=>{e.exports=require("classnames")},4048:e=>{e.exports=require("react-truncate")},6009:e=>{e.exports=require("dom-helpers/ownerDocument")},6417:(e,r,t)=>{t.d(r,{A:()=>a});let s=t(82015).createContext(null);s.displayName="CardHeaderContext";let a=s},6952:e=>{e.exports=require("@restart/ui/Modal")},7374:e=>{e.exports=require("@restart/ui/Dropdown")},8732:e=>{e.exports=require("react/jsx-runtime")},9653:e=>{e.exports=require("@restart/ui/Overlay")},11242:e=>{e.exports=require("redux-persist/integration/react")},11688:e=>{e.exports=require("@restart/hooks/useWillUnmount")},12388:e=>{e.exports=require("dom-helpers/addEventListener")},13364:e=>{e.exports=require("redux-saga/effects")},14062:e=>{e.exports=import("react-redux")},14078:e=>{e.exports=import("swr")},14332:e=>{e.exports=require("uncontrollable")},16116:e=>{e.exports=require("invariant")},18597:(e,r,t)=>{t.d(r,{A:()=>v});var s=t(3892),a=t.n(s),o=t(82015),i=t(80739),n=t(8732);let p=o.forwardRef(({className:e,bsPrefix:r,as:t="div",...s},o)=>(r=(0,i.oU)(r,"card-body"),(0,n.jsx)(t,{ref:o,className:a()(e,r),...s})));p.displayName="CardBody";let d=o.forwardRef(({className:e,bsPrefix:r,as:t="div",...s},o)=>(r=(0,i.oU)(r,"card-footer"),(0,n.jsx)(t,{ref:o,className:a()(e,r),...s})));d.displayName="CardFooter";var l=t(6417);let u=o.forwardRef(({bsPrefix:e,className:r,as:t="div",...s},p)=>{let d=(0,i.oU)(e,"card-header"),u=(0,o.useMemo)(()=>({cardHeaderBsPrefix:d}),[d]);return(0,n.jsx)(l.A.Provider,{value:u,children:(0,n.jsx)(t,{ref:p,...s,className:a()(r,d)})})});u.displayName="CardHeader";let c=o.forwardRef(({bsPrefix:e,className:r,variant:t,as:s="img",...o},p)=>{let d=(0,i.oU)(e,"card-img");return(0,n.jsx)(s,{ref:p,className:a()(t?`${d}-${t}`:d,r),...o})});c.displayName="CardImg";let x=o.forwardRef(({className:e,bsPrefix:r,as:t="div",...s},o)=>(r=(0,i.oU)(r,"card-img-overlay"),(0,n.jsx)(t,{ref:o,className:a()(e,r),...s})));x.displayName="CardImgOverlay";let m=o.forwardRef(({className:e,bsPrefix:r,as:t="a",...s},o)=>(r=(0,i.oU)(r,"card-link"),(0,n.jsx)(t,{ref:o,className:a()(e,r),...s})));m.displayName="CardLink";var f=t(7783);let h=(0,f.A)("h6"),q=o.forwardRef(({className:e,bsPrefix:r,as:t=h,...s},o)=>(r=(0,i.oU)(r,"card-subtitle"),(0,n.jsx)(t,{ref:o,className:a()(e,r),...s})));q.displayName="CardSubtitle";let j=o.forwardRef(({className:e,bsPrefix:r,as:t="p",...s},o)=>(r=(0,i.oU)(r,"card-text"),(0,n.jsx)(t,{ref:o,className:a()(e,r),...s})));j.displayName="CardText";let g=(0,f.A)("h5"),P=o.forwardRef(({className:e,bsPrefix:r,as:t=g,...s},o)=>(r=(0,i.oU)(r,"card-title"),(0,n.jsx)(t,{ref:o,className:a()(e,r),...s})));P.displayName="CardTitle";let b=o.forwardRef(({bsPrefix:e,className:r,bg:t,text:s,border:o,body:d=!1,children:l,as:u="div",...c},x)=>{let m=(0,i.oU)(e,"card");return(0,n.jsx)(u,{ref:x,...c,className:a()(r,m,t&&`bg-${t}`,s&&`text-${s}`,o&&`border-${o}`),children:d?(0,n.jsx)(p,{children:l}):l})});b.displayName="Card";let v=Object.assign(b,{Img:c,Title:P,Subtitle:q,Body:p,Link:m,Text:j,Header:u,Footer:d,ImgOverlay:x})},18622:e=>{e.exports=require("yup")},19121:e=>{e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},22326:e=>{e.exports=require("react-dom")},22541:e=>{e.exports=require("redux-persist/lib/storage")},25303:e=>{e.exports=require("@restart/ui/NavItem")},26324:e=>{e.exports=require("warning")},27825:e=>{e.exports=require("lodash")},27910:e=>{e.exports=require("stream")},28217:e=>{e.exports=require("@restart/ui/DropdownItem")},29021:e=>{e.exports=require("fs")},29294:e=>{e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},29628:e=>{e.exports=require("dom-helpers/addClass")},29825:e=>{e.exports=require("prop-types")},29841:e=>{e.exports=require("dom-helpers/hasClass")},30362:e=>{e.exports=require("@restart/ui/DropdownToggle")},33873:e=>{e.exports=require("path")},36653:e=>{e.exports=require("nprogress")},36955:e=>{e.exports=require("@restart/hooks/useBreakpoint")},37766:e=>{e.exports=require("@restart/hooks/useMergedRefs")},39756:e=>{e.exports=import("redux")},40051:e=>{e.exports=require("dom-helpers/removeClass")},40361:e=>{e.exports=require("next/dist/compiled/next-server/pages.runtime.prod.js")},42893:e=>{e.exports=import("react-hot-toast")},43294:e=>{e.exports=require("formik")},45127:(e,r,t)=>{t.a(e,async(e,s)=>{try{t.r(r),t.d(r,{config:()=>h,default:()=>c,getServerSideProps:()=>f,getStaticPaths:()=>m,getStaticProps:()=>x,reportWebVitals:()=>q,routeModule:()=>w,unstable_getServerProps:()=>b,unstable_getServerSideProps:()=>v,unstable_getStaticParams:()=>P,unstable_getStaticPaths:()=>g,unstable_getStaticProps:()=>j});var a=t(63885),o=t(80237),i=t(81413),n=t(9616),p=t.n(n),d=t(72386),l=t(53238),u=e([d,l]);[d,l]=u.then?(await u)():u;let c=(0,i.M)(l,"default"),x=(0,i.M)(l,"getStaticProps"),m=(0,i.M)(l,"getStaticPaths"),f=(0,i.M)(l,"getServerSideProps"),h=(0,i.M)(l,"config"),q=(0,i.M)(l,"reportWebVitals"),j=(0,i.M)(l,"unstable_getStaticProps"),g=(0,i.M)(l,"unstable_getStaticPaths"),P=(0,i.M)(l,"unstable_getStaticParams"),b=(0,i.M)(l,"unstable_getServerProps"),v=(0,i.M)(l,"unstable_getServerSideProps"),w=new a.PagesRouteModule({definition:{kind:o.A.PAGES,page:"/project/components/ProjectDetailsAccordion",pathname:"/project/components/ProjectDetailsAccordion",bundlePath:"",filename:""},components:{App:d.default,Document:p()},userland:l});s()}catch(e){s(e)}})},50009:e=>{e.exports=require("@restart/ui/DropdownMenu")},53238:(e,r,t)=>{t.a(e,async(e,s)=>{try{t.r(r),t.d(r,{default:()=>m});var a=t(8732),o=t(82015),i=t(54131),n=t(82053),p=t(74716),d=t.n(p),l=t(93024),u=t(18597),c=t(88751),x=e([i]);i=(x.then?(await x)():x)[0];let m=e=>{let r="DD-MM-YYYY HH:mm:ss",{t}=(0,c.useTranslation)("common"),[s,p]=(0,o.useState)(!0);return(0,a.jsx)(a.Fragment,{children:(0,a.jsxs)(l.A.Item,{eventKey:"0",children:[(0,a.jsxs)(l.A.Header,{onClick:()=>p(!s),children:[(0,a.jsx)("div",{className:"cardTitle",children:t("ProjectDetails")}),(0,a.jsx)("div",{className:"cardArrow",children:s?(0,a.jsx)(n.FontAwesomeIcon,{icon:i.faPlus,color:"#fff"}):(0,a.jsx)(n.FontAwesomeIcon,{icon:i.faMinus,color:"#fff"})})]}),(0,a.jsx)(l.A.Body,{children:(0,a.jsxs)(u.A.Text,{className:"projectDetails ps-0",children:[(0,a.jsxs)("p",{children:[(0,a.jsx)("b",{children:t("Status")}),":",(0,a.jsxs)("span",{children:[" ",e.project.status?e.project.status.title:""]})]}),(0,a.jsxs)("p",{children:[(0,a.jsx)("b",{children:t("Created")}),":",(0,a.jsxs)("span",{children:[" ",e.project.created_at?d()(e.project.created_at).format(r):null," "]})]}),(0,a.jsxs)("p",{children:[(0,a.jsx)("b",{children:t("EndDate")}),":",(0,a.jsxs)("span",{children:[" ",e.project.end_date?d()(e.project.end_date).format("DD-MM-YYYY"):null]})]}),(0,a.jsxs)("p",{children:[(0,a.jsx)("b",{children:t("LastModified")}),":",(0,a.jsxs)("span",{children:[" ",e.project.updated_at?d()(e.project.updated_at).format(r):null," "]})]}),(0,a.jsxs)("p",{children:[(0,a.jsx)("b",{children:t("WeblinktoProject")}),":",(0,a.jsx)("span",{children:e.project?.website&&(0,a.jsx)("a",{href:e.project.website,target:"_blank",children:e.project.website})})]})]})})]})})};s()}catch(e){s(e)}})},54131:e=>{e.exports=import("@fortawesome/free-solid-svg-icons")},57664:e=>{e.exports=require("dom-helpers/scrollbarSize")},58928:e=>{e.exports=require("dom-helpers/canUseDOM")},59717:e=>{e.exports=require("@restart/ui/DropdownContext")},60560:e=>{e.exports=require("dom-helpers/contains")},63033:e=>{e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},65447:e=>{e.exports=require("@restart/ui/Button")},67364:e=>{e.exports=require("@restart/hooks/useCallbackRef")},68455:e=>{e.exports=import("redux-saga")},69722:e=>{e.exports=require("es6-promise")},74075:e=>{e.exports=require("zlib")},74716:e=>{e.exports=require("moment")},74987:e=>{e.exports=require("@restart/ui/ModalManager")},78097:e=>{e.exports=require("next-redux-saga")},78634:e=>{e.exports=require("@restart/ui/Anchor")},80237:(e,r)=>{Object.defineProperty(r,"A",{enumerable:!0,get:function(){return t}});var t=function(e){return e.PAGES="PAGES",e.PAGES_API="PAGES_API",e.APP_PAGE="APP_PAGE",e.APP_ROUTE="APP_ROUTE",e.IMAGE="IMAGE",e}({})},81366:e=>{e.exports=require("redux-auth-wrapper/connectedAuthWrapper")},81413:(e,r)=>{Object.defineProperty(r,"M",{enumerable:!0,get:function(){return function e(r,t){return t in r?r[t]:"then"in r&&"function"==typeof r.then?r.then(r=>e(r,t)):"function"==typeof r&&"default"===t?r:void 0}}})},81521:e=>{e.exports=require("dom-helpers/querySelectorAll")},81895:e=>{e.exports=require("@restart/hooks/useEventCallback")},82015:e=>{e.exports=require("react")},82053:e=>{e.exports=require("@fortawesome/react-fontawesome")},86842:e=>{e.exports=require("@restart/ui/SelectableContext")},87571:e=>{e.exports=require("dom-helpers/transitionEnd")},88751:e=>{e.exports=require("next-i18next")},93787:e=>{e.exports=require("redux-persist")},94947:e=>{e.exports=require("@restart/hooks/useTimeout")},96196:e=>{e.exports=require("next-redux-wrapper")},98320:e=>{e.exports=require("@restart/hooks/useIsomorphicEffect")},99460:e=>{e.exports=require("dom-helpers/css")}};var r=require("../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[6089,9216,9616,2386],()=>t(45127));module.exports=s})();