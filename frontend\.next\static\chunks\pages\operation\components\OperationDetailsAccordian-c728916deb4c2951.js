(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[4006],{16341:(e,n,s)=>{"use strict";s.r(n),s.d(n,{default:()=>x});var r=s(37876),a=s(14232),t=s(11041),i=s(21772),l=s(10841),d=s.n(l),o=s(32890),c=s(56970),p=s(37784),h=s(31753);let x=e=>{let{t:n}=(0,h.Bd)("common"),s="MM-D-YYYY HH:mm:ss",l="MM-D-YYYY",[x,j]=(0,a.useState)(!1);return(0,r.jsx)(r.Fragment,{children:(0,r.jsxs)(o.A.Item,{eventKey:"0",children:[(0,r.jsxs)(o<PERSON><PERSON><PERSON>,{onClick:()=>j(!x),children:[(0,r.jsx)("div",{className:"cardTitle",children:n("OperationDetails")}),(0,r.jsx)("div",{className:"cardArrow",children:x?(0,r.jsx)(i.g,{icon:t.EZy,color:"#fff"}):(0,r.jsx)(i.g,{icon:t.QLR,color:"#fff"})})]}),(0,r.jsx)(o.A.Body,{children:(0,r.jsxs)(c.A,{className:"operationData",children:[(0,r.jsxs)(p.A,{md:!0,lg:6,sm:12,className:"ps-0",children:[(0,r.jsxs)("p",{children:[(0,r.jsx)("b",{children:n("HazardType")}),":",(0,r.jsx)("span",{children:e.operation.hazard_type?e.operation.hazard_type.title:null})]}),function(e,n){return(0,r.jsxs)("div",{className:"d-flex mb-2 pb-1",children:[(0,r.jsx)("b",{children:e("Hazard")}),":",(0,r.jsx)("span",{children:(0,r.jsx)("ul",{className:"comma-separated",children:n.hazard&&n.hazard.length>=1?n.hazard.map((e,n)=>(0,r.jsx)("li",{children:e.title.en},n)):null})})]})}(n,e.operation),(0,r.jsxs)("p",{children:[(0,r.jsx)("b",{children:n("Syndrome")}),":",(0,r.jsx)("span",{children:e.operation.syndrome?e.operation.syndrome.title:null})]}),(0,r.jsxs)("p",{children:[(0,r.jsx)("b",{children:n("Created")}),":",(0,r.jsx)("span",{children:d()(e.operation.created_at).format(s)})]}),(0,r.jsxs)("p",{children:[(0,r.jsx)("b",{children:n("LastModified")}),":",(0,r.jsx)("span",{children:d()(e.operation.updated_at).format(s)})]})]}),(0,r.jsxs)(p.A,{md:!0,lg:6,sm:12,className:"ps-0",children:[(0,r.jsxs)("p",{children:[(0,r.jsx)("b",{children:n("CountryOrTerritory")}),":",(0,r.jsx)("span",{children:e.operation.country?e.operation.country.title:null})]}),(0,r.jsxs)("p",{children:[(0,r.jsx)("b",{children:n("OperationStatus")}),":",(0,r.jsxs)("span",{children:[" ",e.operation.status.title," "]})]}),(0,r.jsxs)("p",{children:[(0,r.jsx)("b",{children:n("StartDate")}),":",(0,r.jsx)("span",{children:e.operation.start_date?d()(e.operation.start_date).format(l):null})]}),(0,r.jsxs)("p",{children:[(0,r.jsx)("b",{children:n("EndDate")}),":",(0,r.jsx)("span",{children:e.operation.end_date?d()(e.operation.end_date).format(l):null})]})]})]})})]})})}},55156:(e,n,s)=>{(window.__NEXT_P=window.__NEXT_P||[]).push(["/operation/components/OperationDetailsAccordian",function(){return s(16341)}])}},e=>{var n=n=>e(e.s=n);e.O(0,[7725,1772,636,6593,8792],()=>n(55156)),_N_E=e.O()}]);
//# sourceMappingURL=OperationDetailsAccordian-c728916deb4c2951.js.map