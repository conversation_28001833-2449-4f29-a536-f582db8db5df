"use strict";(()=>{var e={};e.id=8384,e.ids=[636,3220,8384],e.modules={123:e=>{e.exports=require("dom-helpers/removeEventListener")},1332:e=>{e.exports=require("react-custom-scrollbars-2")},1428:e=>{e.exports=import("axios")},1680:e=>{e.exports=require("@restart/ui/utils")},1919:e=>{e.exports=require("react-transition-group/Transition")},3892:e=>{e.exports=require("classnames")},4048:e=>{e.exports=require("react-truncate")},5087:(e,r,t)=>{t.a(e,async(e,s)=>{try{t.r(r),t.d(r,{config:()=>g,default:()=>c,getServerSideProps:()=>h,getStaticPaths:()=>m,getStaticProps:()=>x,reportWebVitals:()=>v,routeModule:()=>b,unstable_getServerProps:()=>P,unstable_getServerSideProps:()=>w,unstable_getStaticParams:()=>S,unstable_getStaticPaths:()=>q,unstable_getStaticProps:()=>f});var a=t(63885),o=t(80237),i=t(81413),n=t(9616),l=t.n(n),d=t(72386),u=t(70078),p=e([d,u]);[d,u]=p.then?(await p)():p;let c=(0,i.M)(u,"default"),x=(0,i.M)(u,"getStaticProps"),m=(0,i.M)(u,"getStaticPaths"),h=(0,i.M)(u,"getServerSideProps"),g=(0,i.M)(u,"config"),v=(0,i.M)(u,"reportWebVitals"),f=(0,i.M)(u,"unstable_getStaticProps"),q=(0,i.M)(u,"unstable_getStaticPaths"),S=(0,i.M)(u,"unstable_getStaticParams"),P=(0,i.M)(u,"unstable_getServerProps"),w=(0,i.M)(u,"unstable_getServerSideProps"),b=new a.PagesRouteModule({definition:{kind:o.A.PAGES,page:"/reset-password/[passwordToken]",pathname:"/reset-password/[passwordToken]",bundlePath:"",filename:""},components:{App:d.default,Document:l()},userland:u});s()}catch(e){s(e)}})},5299:(e,r,t)=>{t.d(r,{w:()=>a});let s=e=>{let r,t=localStorage.getItem(e);try{null!==t&&(r=JSON.parse(t))}catch(e){}return r},a=()=>{let e,r=s("persist:root");try{e=JSON.parse(r.user)}catch(e){}return e}},6009:e=>{e.exports=require("dom-helpers/ownerDocument")},6952:e=>{e.exports=require("@restart/ui/Modal")},7374:e=>{e.exports=require("@restart/ui/Dropdown")},8732:e=>{e.exports=require("react/jsx-runtime")},9653:e=>{e.exports=require("@restart/ui/Overlay")},11242:e=>{e.exports=require("redux-persist/integration/react")},11688:e=>{e.exports=require("@restart/hooks/useWillUnmount")},12388:e=>{e.exports=require("dom-helpers/addEventListener")},13364:e=>{e.exports=require("redux-saga/effects")},14062:e=>{e.exports=import("react-redux")},14078:e=>{e.exports=import("swr")},14332:e=>{e.exports=require("uncontrollable")},15653:(e,r,t)=>{t.d(r,{ks:()=>i,s3:()=>n});var s=t(8732);t(82015);var a=t(59549),o=t(43294);let i=({name:e,id:r,required:t,validator:i,errorMessage:n,onChange:l,value:d,as:u,multiline:p,rows:c,pattern:x,...m})=>(0,s.jsx)(o.Field,{name:e,validate:e=>{let r="string"==typeof e?e:String(e||"");return t&&(!e||""===r.trim())?n?.validator||"This field is required":i&&!i(e)?n?.validator||"Invalid value":x&&e&&!new RegExp(x).test(e)?n?.pattern||"Invalid format":void 0},children:({field:e,meta:t})=>(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)(a.A.Control,{...e,...m,id:r,as:u||"input",rows:c,isInvalid:t.touched&&!!t.error,onChange:r=>{e.onChange(r),l&&l(r)},value:void 0!==d?d:e.value}),t.touched&&t.error?(0,s.jsx)(a.A.Control.Feedback,{type:"invalid",children:t.error}):null]})}),n=({name:e,id:r,required:t,errorMessage:i,onChange:n,value:l,children:d,...u})=>(0,s.jsx)(o.Field,{name:e,validate:e=>{if(t&&(!e||""===e))return i?.validator||"This field is required"},children:({field:e,meta:t})=>(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)(a.A.Control,{as:"select",...e,...u,id:r,isInvalid:t.touched&&!!t.error,onChange:r=>{e.onChange(r),n&&n(r)},value:void 0!==l?l:e.value,children:d}),t.touched&&t.error?(0,s.jsx)(a.A.Control.Feedback,{type:"invalid",children:t.error}):null]})})},16116:e=>{e.exports=require("invariant")},18622:e=>{e.exports=require("yup")},19121:e=>{e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},22326:e=>{e.exports=require("react-dom")},22541:e=>{e.exports=require("redux-persist/lib/storage")},23579:(e,r,t)=>{t.d(r,{sx:()=>u,s3:()=>a.s3,ks:()=>a.ks,yk:()=>s.A});var s=t(66994),a=t(15653),o=t(8732),i=t(82015),n=t.n(i),l=t(43294),d=t(59549);let u={RadioGroup:({name:e,valueSelected:r,onChange:t,errorMessage:s,children:a})=>{let{errors:i,touched:d}=(0,l.useFormikContext)(),u=d[e]&&i[e];n().useMemo(()=>({name:e}),[e]);let p=n().Children.map(a,r=>n().isValidElement(r)&&function(e){return"object"==typeof e&&null!==e}(r.props)?n().cloneElement(r,{name:e,...r.props}):r);return(0,o.jsxs)("div",{children:[(0,o.jsx)("div",{className:"radio-group",children:p}),u&&(0,o.jsx)("div",{className:"invalid-feedback d-block",children:s||("string"==typeof i[e]?i[e]:String(i[e]))})]})},RadioItem:({id:e,label:r,value:t,name:s,disabled:a})=>{let{values:i,setFieldValue:n}=(0,l.useFormikContext)(),u=s||e;return(0,o.jsx)(d.A.Check,{type:"radio",id:e,label:r,value:t,name:u,checked:i[u]===t,onChange:e=>{n(u,e.target.value)},disabled:a,inline:!0})}};s.A,a.ks,a.s3},25303:e=>{e.exports=require("@restart/ui/NavItem")},26324:e=>{e.exports=require("warning")},27825:e=>{e.exports=require("lodash")},27910:e=>{e.exports=require("stream")},28217:e=>{e.exports=require("@restart/ui/DropdownItem")},29021:e=>{e.exports=require("fs")},29294:e=>{e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},29628:e=>{e.exports=require("dom-helpers/addClass")},29825:e=>{e.exports=require("prop-types")},29841:e=>{e.exports=require("dom-helpers/hasClass")},30362:e=>{e.exports=require("@restart/ui/DropdownToggle")},33873:e=>{e.exports=require("path")},36653:e=>{e.exports=require("nprogress")},36955:e=>{e.exports=require("@restart/hooks/useBreakpoint")},37766:e=>{e.exports=require("@restart/hooks/useMergedRefs")},39756:e=>{e.exports=import("redux")},40051:e=>{e.exports=require("dom-helpers/removeClass")},40361:e=>{e.exports=require("next/dist/compiled/next-server/pages.runtime.prod.js")},42893:e=>{e.exports=import("react-hot-toast")},43294:e=>{e.exports=require("formik")},50009:e=>{e.exports=require("@restart/ui/DropdownMenu")},54131:e=>{e.exports=import("@fortawesome/free-solid-svg-icons")},57664:e=>{e.exports=require("dom-helpers/scrollbarSize")},58928:e=>{e.exports=require("dom-helpers/canUseDOM")},59717:e=>{e.exports=require("@restart/ui/DropdownContext")},60560:e=>{e.exports=require("dom-helpers/contains")},63033:e=>{e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},65447:e=>{e.exports=require("@restart/ui/Button")},66994:(e,r,t)=>{t.d(r,{A:()=>l});var s=t(8732),a=t(82015),o=t(43294),i=t(18622);let n=(0,a.forwardRef)((e,r)=>{let{children:t,onSubmit:a,autoComplete:n,className:l,onKeyPress:d,initialValues:u,...p}=e,c=i.object().shape({});return(0,s.jsx)(o.Formik,{initialValues:u||{},validationSchema:c,onSubmit:(e,r)=>{let t={preventDefault:()=>{},stopPropagation:()=>{},currentTarget:null,target:null,nativeEvent:new Event("submit"),bubbles:!1,cancelable:!0,defaultPrevented:!1,eventPhase:0,isTrusted:!1,timeStamp:Date.now(),type:"submit",isDefaultPrevented:()=>!1,isPropagationStopped:()=>!1,persist:()=>{}};a&&a(t,e,r)},...p,children:e=>(0,s.jsx)(o.Form,{ref:r,onSubmit:e.handleSubmit,autoComplete:n,className:l,onKeyPress:d,children:"function"==typeof t?t(e):t})})});n.displayName="ValidationFormWrapper";let l=n},67364:e=>{e.exports=require("@restart/hooks/useCallbackRef")},68455:e=>{e.exports=import("redux-saga")},69722:e=>{e.exports=require("es6-promise")},70078:(e,r,t)=>{t.a(e,async(e,s)=>{try{t.r(r),t.d(r,{default:()=>S});var a=t(8732),o=t(44233),i=t.n(o),n=t(14062),l=t(82015),d=t(23579),u=t(66994),p=t(42893),c=t(19918),x=t.n(c),m=t(54131),h=t(82053),g=t(63487),v=t(5299),f=e([n,p,m,g]);[n,p,m,g]=f.then?(await f)():f;let q={"RESET_PASSWORD.CHANGE_PASSWORD_ERROR":"Unable to reset password. Contact Administrator","RESET_PASSWORD.WRONG_CURRENT_PASSWORD":"Wrong Current password","RESET_PASSWORD.PASSWORD_CHANGED":"Password Changed Successfully.","RESET_PASSWORD.NO_TOKEN":"Invalid Token. Please reset password and try again","RESET_PASSWORD.EXPIRED_TOKEN":"Token Expired. Please reset password again","RESET_PASSWORD.EXPIRY_RESET_HOURS_NOT_SET":"Unable to reset password. Contact Administrator"},S=(0,n.connect)(e=>e)(e=>{let{passwordToken:r}=(0,o.useRouter)().query,t=(0,l.useRef)(null),[s,n]=(0,l.useState)(""),[c,f]=(0,l.useState)(""),[S,P]=(0,l.useState)(!1),w=e=>{"password"===e.target.name&&n(e.target.value),"confirmPassword"===e.target.name&&f(e.target.value)},b=async(e,t)=>{if(e.preventDefault(),t.password===t.confirmPassword){let e=await g.A.post("/email/reset-password",{newPasswordToken:r,newPassword:c});e&&e.success?(p.default.success(q[e.message]),i().push("/login")):e.data&&e.data.message?p.default.error(q[e.data.message]):p.default.error(q[e.message])}};return(0,l.useEffect)(()=>{let e=(0,v.w)();e&&e.username&&P(!0)},[]),(0,a.jsx)("div",{className:"loginContainer ",children:(0,a.jsx)("div",{className:"section",children:(0,a.jsx)("div",{className:"container",children:(0,a.jsx)("div",{className:"columns",children:(0,a.jsx)("div",{className:"column  is-two-thirds",children:(0,a.jsxs)("div",{className:"column reset-password",children:[(0,a.jsx)("div",{className:"imgBanner",children:(0,a.jsx)("img",{src:"/images/login-banner.jpg",alt:"RKI Login Banner Image"})}),(0,a.jsxs)(u.A,{className:"formContainer",onSubmit:b,ref:t,children:[(0,a.jsx)("div",{className:"logoContainer",children:(0,a.jsx)(x(),{href:"/",children:(0,a.jsx)("img",{src:"/images/logo.jpg",alt:"Rohert Koch Institut - Logo"})})}),(0,a.jsx)("section",{className:"fieldsContainer",children:S?(0,a.jsx)("div",{children:(0,a.jsxs)("div",{className:"d-flex flex-column justify-content-center align-items-center",children:[(0,a.jsx)(h.FontAwesomeIcon,{icon:m.faExclamationCircle,color:"#e8ba0d",size:"5x",className:"error-icon"}),(0,a.jsx)("p",{children:"Logged in user cannot use reset password"}),(0,a.jsx)(x(),{href:"/",as:"/",children:(0,a.jsxs)("button",{className:"button is-primary",children:[(0,a.jsx)(h.FontAwesomeIcon,{icon:m.faArrowCircleLeft,color:"#ffff",size:"1x"})," Back to RKI Dashboard"]})})]})}):(0,a.jsx)(a.Fragment,{children:(0,a.jsxs)("div",{children:[(0,a.jsxs)("div",{className:"mb-3",children:[(0,a.jsx)("label",{htmlFor:"password",children:"Password"}),(0,a.jsx)(d.ks,{name:"password",id:"password",type:"password",required:!0,pattern:"(?=.*[A-Z]).{8,}",errorMessage:{required:"Password is required",pattern:"Password should be at least 8 characters and contains at least one upper case letter"},value:s,onChange:w})]}),(0,a.jsxs)("div",{className:"mb-3",children:[(0,a.jsx)("label",{htmlFor:"confirmPassword",children:"Confirm Password"}),(0,a.jsx)(d.ks,{name:"confirmPassword",id:"confirmPassword",type:"password",required:!0,validator:e=>e&&e===s,errorMessage:{required:"Confirm password is required",validator:"Password does not match"},value:c,onChange:w})]}),(0,a.jsx)("div",{className:"field is-grouped",children:(0,a.jsx)("div",{className:"control",children:(0,a.jsx)("button",{className:"button is-primary",type:"submit",children:"Reset Password"})})})]})})})]})]})})})})})})});s()}catch(e){s(e)}})},74075:e=>{e.exports=require("zlib")},74716:e=>{e.exports=require("moment")},74987:e=>{e.exports=require("@restart/ui/ModalManager")},78097:e=>{e.exports=require("next-redux-saga")},78634:e=>{e.exports=require("@restart/ui/Anchor")},80237:(e,r)=>{Object.defineProperty(r,"A",{enumerable:!0,get:function(){return t}});var t=function(e){return e.PAGES="PAGES",e.PAGES_API="PAGES_API",e.APP_PAGE="APP_PAGE",e.APP_ROUTE="APP_ROUTE",e.IMAGE="IMAGE",e}({})},81366:e=>{e.exports=require("redux-auth-wrapper/connectedAuthWrapper")},81413:(e,r)=>{Object.defineProperty(r,"M",{enumerable:!0,get:function(){return function e(r,t){return t in r?r[t]:"then"in r&&"function"==typeof r.then?r.then(r=>e(r,t)):"function"==typeof r&&"default"===t?r:void 0}}})},81521:e=>{e.exports=require("dom-helpers/querySelectorAll")},81895:e=>{e.exports=require("@restart/hooks/useEventCallback")},82015:e=>{e.exports=require("react")},82053:e=>{e.exports=require("@fortawesome/react-fontawesome")},86842:e=>{e.exports=require("@restart/ui/SelectableContext")},87571:e=>{e.exports=require("dom-helpers/transitionEnd")},88751:e=>{e.exports=require("next-i18next")},93787:e=>{e.exports=require("redux-persist")},94947:e=>{e.exports=require("@restart/hooks/useTimeout")},96196:e=>{e.exports=require("next-redux-wrapper")},98320:e=>{e.exports=require("@restart/hooks/useIsomorphicEffect")},99460:e=>{e.exports=require("dom-helpers/css")}};var r=require("../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[6089,9216,9616,2386],()=>t(5087));module.exports=s})();