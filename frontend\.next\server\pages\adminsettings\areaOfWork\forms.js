"use strict";(()=>{var e={};e.id=9970,e.ids=[636,3220,9970],e.modules={123:e=>{e.exports=require("dom-helpers/removeEventListener")},1332:e=>{e.exports=require("react-custom-scrollbars-2")},1428:e=>{e.exports=import("axios")},1680:e=>{e.exports=require("@restart/ui/utils")},1919:e=>{e.exports=require("react-transition-group/Transition")},3892:e=>{e.exports=require("classnames")},4048:e=>{e.exports=require("react-truncate")},6009:e=>{e.exports=require("dom-helpers/ownerDocument")},6417:(e,r,t)=>{t.d(r,{A:()=>a});let s=t(82015).createContext(null);s.displayName="CardHeaderContext";let a=s},6952:e=>{e.exports=require("@restart/ui/Modal")},7374:e=>{e.exports=require("@restart/ui/Dropdown")},8732:e=>{e.exports=require("react/jsx-runtime")},9653:e=>{e.exports=require("@restart/ui/Overlay")},11242:e=>{e.exports=require("redux-persist/integration/react")},11688:e=>{e.exports=require("@restart/hooks/useWillUnmount")},12388:e=>{e.exports=require("dom-helpers/addEventListener")},13364:e=>{e.exports=require("redux-saga/effects")},14062:e=>{e.exports=import("react-redux")},14078:e=>{e.exports=import("swr")},14332:e=>{e.exports=require("uncontrollable")},15653:(e,r,t)=>{t.d(r,{ks:()=>i,s3:()=>n});var s=t(8732);t(82015);var a=t(59549),o=t(43294);let i=({name:e,id:r,required:t,validator:i,errorMessage:n,onChange:l,value:d,as:u,multiline:p,rows:c,pattern:x,...m})=>(0,s.jsx)(o.Field,{name:e,validate:e=>{let r="string"==typeof e?e:String(e||"");return t&&(!e||""===r.trim())?n?.validator||"This field is required":i&&!i(e)?n?.validator||"Invalid value":x&&e&&!new RegExp(x).test(e)?n?.pattern||"Invalid format":void 0},children:({field:e,meta:t})=>(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)(a.A.Control,{...e,...m,id:r,as:u||"input",rows:c,isInvalid:t.touched&&!!t.error,onChange:r=>{e.onChange(r),l&&l(r)},value:void 0!==d?d:e.value}),t.touched&&t.error?(0,s.jsx)(a.A.Control.Feedback,{type:"invalid",children:t.error}):null]})}),n=({name:e,id:r,required:t,errorMessage:i,onChange:n,value:l,children:d,...u})=>(0,s.jsx)(o.Field,{name:e,validate:e=>{if(t&&(!e||""===e))return i?.validator||"This field is required"},children:({field:e,meta:t})=>(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)(a.A.Control,{as:"select",...e,...u,id:r,isInvalid:t.touched&&!!t.error,onChange:r=>{e.onChange(r),n&&n(r)},value:void 0!==l?l:e.value,children:d}),t.touched&&t.error?(0,s.jsx)(a.A.Control.Feedback,{type:"invalid",children:t.error}):null]})})},16116:e=>{e.exports=require("invariant")},16495:(e,r,t)=>{t.a(e,async(e,s)=>{try{t.r(r),t.d(r,{config:()=>h,default:()=>c,getServerSideProps:()=>f,getStaticPaths:()=>m,getStaticProps:()=>x,reportWebVitals:()=>g,routeModule:()=>y,unstable_getServerProps:()=>b,unstable_getServerSideProps:()=>j,unstable_getStaticParams:()=>A,unstable_getStaticPaths:()=>q,unstable_getStaticProps:()=>v});var a=t(63885),o=t(80237),i=t(81413),n=t(9616),l=t.n(n),d=t(72386),u=t(80760),p=e([d,u]);[d,u]=p.then?(await p)():p;let c=(0,i.M)(u,"default"),x=(0,i.M)(u,"getStaticProps"),m=(0,i.M)(u,"getStaticPaths"),f=(0,i.M)(u,"getServerSideProps"),h=(0,i.M)(u,"config"),g=(0,i.M)(u,"reportWebVitals"),v=(0,i.M)(u,"unstable_getStaticProps"),q=(0,i.M)(u,"unstable_getStaticPaths"),A=(0,i.M)(u,"unstable_getStaticParams"),b=(0,i.M)(u,"unstable_getServerProps"),j=(0,i.M)(u,"unstable_getServerSideProps"),y=new a.PagesRouteModule({definition:{kind:o.A.PAGES,page:"/adminsettings/areaOfWork/forms",pathname:"/adminsettings/areaOfWork/forms",bundlePath:"",filename:""},components:{App:d.default,Document:l()},userland:u});s()}catch(e){s(e)}})},18597:(e,r,t)=>{t.d(r,{A:()=>j});var s=t(3892),a=t.n(s),o=t(82015),i=t(80739),n=t(8732);let l=o.forwardRef(({className:e,bsPrefix:r,as:t="div",...s},o)=>(r=(0,i.oU)(r,"card-body"),(0,n.jsx)(t,{ref:o,className:a()(e,r),...s})));l.displayName="CardBody";let d=o.forwardRef(({className:e,bsPrefix:r,as:t="div",...s},o)=>(r=(0,i.oU)(r,"card-footer"),(0,n.jsx)(t,{ref:o,className:a()(e,r),...s})));d.displayName="CardFooter";var u=t(6417);let p=o.forwardRef(({bsPrefix:e,className:r,as:t="div",...s},l)=>{let d=(0,i.oU)(e,"card-header"),p=(0,o.useMemo)(()=>({cardHeaderBsPrefix:d}),[d]);return(0,n.jsx)(u.A.Provider,{value:p,children:(0,n.jsx)(t,{ref:l,...s,className:a()(r,d)})})});p.displayName="CardHeader";let c=o.forwardRef(({bsPrefix:e,className:r,variant:t,as:s="img",...o},l)=>{let d=(0,i.oU)(e,"card-img");return(0,n.jsx)(s,{ref:l,className:a()(t?`${d}-${t}`:d,r),...o})});c.displayName="CardImg";let x=o.forwardRef(({className:e,bsPrefix:r,as:t="div",...s},o)=>(r=(0,i.oU)(r,"card-img-overlay"),(0,n.jsx)(t,{ref:o,className:a()(e,r),...s})));x.displayName="CardImgOverlay";let m=o.forwardRef(({className:e,bsPrefix:r,as:t="a",...s},o)=>(r=(0,i.oU)(r,"card-link"),(0,n.jsx)(t,{ref:o,className:a()(e,r),...s})));m.displayName="CardLink";var f=t(7783);let h=(0,f.A)("h6"),g=o.forwardRef(({className:e,bsPrefix:r,as:t=h,...s},o)=>(r=(0,i.oU)(r,"card-subtitle"),(0,n.jsx)(t,{ref:o,className:a()(e,r),...s})));g.displayName="CardSubtitle";let v=o.forwardRef(({className:e,bsPrefix:r,as:t="p",...s},o)=>(r=(0,i.oU)(r,"card-text"),(0,n.jsx)(t,{ref:o,className:a()(e,r),...s})));v.displayName="CardText";let q=(0,f.A)("h5"),A=o.forwardRef(({className:e,bsPrefix:r,as:t=q,...s},o)=>(r=(0,i.oU)(r,"card-title"),(0,n.jsx)(t,{ref:o,className:a()(e,r),...s})));A.displayName="CardTitle";let b=o.forwardRef(({bsPrefix:e,className:r,bg:t,text:s,border:o,body:d=!1,children:u,as:p="div",...c},x)=>{let m=(0,i.oU)(e,"card");return(0,n.jsx)(p,{ref:x,...c,className:a()(r,m,t&&`bg-${t}`,s&&`text-${s}`,o&&`border-${o}`),children:d?(0,n.jsx)(l,{children:u}):u})});b.displayName="Card";let j=Object.assign(b,{Img:c,Title:A,Subtitle:g,Body:l,Link:m,Text:v,Header:p,Footer:d,ImgOverlay:x})},18622:e=>{e.exports=require("yup")},19121:e=>{e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},22326:e=>{e.exports=require("react-dom")},22541:e=>{e.exports=require("redux-persist/lib/storage")},23579:(e,r,t)=>{t.d(r,{sx:()=>u,s3:()=>a.s3,ks:()=>a.ks,yk:()=>s.A});var s=t(66994),a=t(15653),o=t(8732),i=t(82015),n=t.n(i),l=t(43294),d=t(59549);let u={RadioGroup:({name:e,valueSelected:r,onChange:t,errorMessage:s,children:a})=>{let{errors:i,touched:d}=(0,l.useFormikContext)(),u=d[e]&&i[e];n().useMemo(()=>({name:e}),[e]);let p=n().Children.map(a,r=>n().isValidElement(r)&&function(e){return"object"==typeof e&&null!==e}(r.props)?n().cloneElement(r,{name:e,...r.props}):r);return(0,o.jsxs)("div",{children:[(0,o.jsx)("div",{className:"radio-group",children:p}),u&&(0,o.jsx)("div",{className:"invalid-feedback d-block",children:s||("string"==typeof i[e]?i[e]:String(i[e]))})]})},RadioItem:({id:e,label:r,value:t,name:s,disabled:a})=>{let{values:i,setFieldValue:n}=(0,l.useFormikContext)(),u=s||e;return(0,o.jsx)(d.A.Check,{type:"radio",id:e,label:r,value:t,name:u,checked:i[u]===t,onChange:e=>{n(u,e.target.value)},disabled:a,inline:!0})}};s.A,a.ks,a.s3},25303:e=>{e.exports=require("@restart/ui/NavItem")},26324:e=>{e.exports=require("warning")},27825:e=>{e.exports=require("lodash")},27910:e=>{e.exports=require("stream")},28217:e=>{e.exports=require("@restart/ui/DropdownItem")},29021:e=>{e.exports=require("fs")},29294:e=>{e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},29628:e=>{e.exports=require("dom-helpers/addClass")},29825:e=>{e.exports=require("prop-types")},29841:e=>{e.exports=require("dom-helpers/hasClass")},30362:e=>{e.exports=require("@restart/ui/DropdownToggle")},33873:e=>{e.exports=require("path")},36653:e=>{e.exports=require("nprogress")},36955:e=>{e.exports=require("@restart/hooks/useBreakpoint")},37766:e=>{e.exports=require("@restart/hooks/useMergedRefs")},39756:e=>{e.exports=import("redux")},40051:e=>{e.exports=require("dom-helpers/removeClass")},40361:e=>{e.exports=require("next/dist/compiled/next-server/pages.runtime.prod.js")},42893:e=>{e.exports=import("react-hot-toast")},43294:e=>{e.exports=require("formik")},50009:e=>{e.exports=require("@restart/ui/DropdownMenu")},57664:e=>{e.exports=require("dom-helpers/scrollbarSize")},58928:e=>{e.exports=require("dom-helpers/canUseDOM")},59717:e=>{e.exports=require("@restart/ui/DropdownContext")},60560:e=>{e.exports=require("dom-helpers/contains")},63033:e=>{e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},65447:e=>{e.exports=require("@restart/ui/Button")},66994:(e,r,t)=>{t.d(r,{A:()=>l});var s=t(8732),a=t(82015),o=t(43294),i=t(18622);let n=(0,a.forwardRef)((e,r)=>{let{children:t,onSubmit:a,autoComplete:n,className:l,onKeyPress:d,initialValues:u,...p}=e,c=i.object().shape({});return(0,s.jsx)(o.Formik,{initialValues:u||{},validationSchema:c,onSubmit:(e,r)=>{let t={preventDefault:()=>{},stopPropagation:()=>{},currentTarget:null,target:null,nativeEvent:new Event("submit"),bubbles:!1,cancelable:!0,defaultPrevented:!1,eventPhase:0,isTrusted:!1,timeStamp:Date.now(),type:"submit",isDefaultPrevented:()=>!1,isPropagationStopped:()=>!1,persist:()=>{}};a&&a(t,e,r)},...p,children:e=>(0,s.jsx)(o.Form,{ref:r,onSubmit:e.handleSubmit,autoComplete:n,className:l,onKeyPress:d,children:"function"==typeof t?t(e):t})})});n.displayName="ValidationFormWrapper";let l=n},67364:e=>{e.exports=require("@restart/hooks/useCallbackRef")},68455:e=>{e.exports=import("redux-saga")},69722:e=>{e.exports=require("es6-promise")},74075:e=>{e.exports=require("zlib")},74716:e=>{e.exports=require("moment")},74987:e=>{e.exports=require("@restart/ui/ModalManager")},78097:e=>{e.exports=require("next-redux-saga")},78634:e=>{e.exports=require("@restart/ui/Anchor")},80237:(e,r)=>{Object.defineProperty(r,"A",{enumerable:!0,get:function(){return t}});var t=function(e){return e.PAGES="PAGES",e.PAGES_API="PAGES_API",e.APP_PAGE="APP_PAGE",e.APP_ROUTE="APP_ROUTE",e.IMAGE="IMAGE",e}({})},80760:(e,r,t)=>{t.a(e,async(e,s)=>{try{t.r(r),t.d(r,{default:()=>j});var a=t(8732),o=t(82015),i=t(7082),n=t(18597),l=t(83551),d=t(49481),u=t(59549),p=t(91353),c=t(66994),x=t(23579),m=t(44233),f=t.n(m),h=t(42893),g=t(19918),v=t.n(g),q=t(63487),A=t(88751),b=e([h,q]);[h,q]=b.then?(await b)():b;let j=e=>{let{t:r}=(0,A.useTranslation)("common"),t={_id:"",title:""},[s,m]=(0,o.useState)(t),g=!!(e.routes&&"edit_area_of_work"===e.routes[0]&&e.routes[1]),b=(0,o.useRef)(null),j=async t=>{let a,o;t.preventDefault();let i={title:s.title.trim()};g?(o="Areaofworkisupdatedsuccessfully",a=await q.A.patch(`/areaofwork/${e.routes[1]}`,i)):(o="Areaofworkisaddedsuccessfully",a=await q.A.post("/areaofwork",i)),a&&a._id?(h.default.success(r(o)),f().push("/adminsettings/area_of_work")):a?.errorCode===11e3?h.default.error(r("duplicatesNotAllowed")):h.default.error(a)};return(0,o.useEffect)(()=>{let r={query:{},sort:{title:"asc"},limit:"~"};g&&(async()=>{let t=await q.A.get(`/areaofwork/${e.routes[1]}`,r);m(e=>({...e,...t}))})()},[]),(0,a.jsx)("div",{children:(0,a.jsx)(i.A,{className:"formCard",fluid:!0,children:(0,a.jsx)(n.A,{style:{marginTop:"5px",boxShadow:"0 4px 8px 0 rgba(0, 0, 0, 0.2), 0 6px 20px 0 rgba(0, 0, 0, 0.19)"},children:(0,a.jsx)(c.A,{onSubmit:j,ref:b,initialValues:s,enableReinitialize:!0,children:(0,a.jsxs)(n.A.Body,{children:[(0,a.jsx)(l.A,{children:(0,a.jsx)(d.A,{children:(0,a.jsx)(n.A.Title,{children:r("adminsetting.areaofwork.Forms.AreaOfWork")})})}),(0,a.jsx)("hr",{}),(0,a.jsx)(l.A,{children:(0,a.jsx)(d.A,{md:!0,lg:6,sm:12,children:(0,a.jsxs)(u.A.Group,{children:[(0,a.jsx)(u.A.Label,{className:"required-field",children:r("adminsetting.areaofwork.Forms.AreaOfWork")}),(0,a.jsx)(x.ks,{name:"title",id:"title",required:!0,value:s.title,validator:e=>""!==String(e||"").trim(),errorMessage:{validator:r("adminsetting.areaofwork.Forms.PleaseAddtheAreaofWork")},onChange:e=>{if(e.target){let{name:r,value:t}=e.target;m(e=>({...e,[r]:t}))}}})]})})}),(0,a.jsx)(l.A,{className:"my-4",children:(0,a.jsxs)(d.A,{children:[(0,a.jsx)(p.A,{className:"me-2",type:"submit",variant:"primary",children:r("adminsetting.areaofwork.Forms.Submit")}),(0,a.jsx)(p.A,{className:"me-2",onClick:()=>{m(t),window.scrollTo(0,0)},variant:"info",children:r("adminsetting.areaofwork.Forms.Reset")}),(0,a.jsx)(v(),{href:"/adminsettings/[...routes]",as:"/adminsettings/area_of_work",children:(0,a.jsx)(p.A,{variant:"secondary",children:r("adminsetting.areaofwork.Forms.Cancel")})})]})})]})})})})})};s()}catch(e){s(e)}})},81366:e=>{e.exports=require("redux-auth-wrapper/connectedAuthWrapper")},81413:(e,r)=>{Object.defineProperty(r,"M",{enumerable:!0,get:function(){return function e(r,t){return t in r?r[t]:"then"in r&&"function"==typeof r.then?r.then(r=>e(r,t)):"function"==typeof r&&"default"===t?r:void 0}}})},81521:e=>{e.exports=require("dom-helpers/querySelectorAll")},81895:e=>{e.exports=require("@restart/hooks/useEventCallback")},82015:e=>{e.exports=require("react")},86842:e=>{e.exports=require("@restart/ui/SelectableContext")},87571:e=>{e.exports=require("dom-helpers/transitionEnd")},88751:e=>{e.exports=require("next-i18next")},93787:e=>{e.exports=require("redux-persist")},94947:e=>{e.exports=require("@restart/hooks/useTimeout")},96196:e=>{e.exports=require("next-redux-wrapper")},98320:e=>{e.exports=require("@restart/hooks/useIsomorphicEffect")},99460:e=>{e.exports=require("dom-helpers/css")}};var r=require("../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[6089,9216,9616,2386],()=>t(16495));module.exports=s})();