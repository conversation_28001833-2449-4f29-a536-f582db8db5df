(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[3261],{15214:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>p});var l=r(37876),o=r(14232),s=r(82851),n=r.n(s),a=r(66619),i=r(15641),y=r(31753);let p=e=>{let{i18n:t}=(0,y.Bd)("common"),r=t.language,{operations:s}=e,[p,c]=(0,o.useState)([]),[u,d]=(0,o.useState)({}),[m,f]=(0,o.useState)({}),[T,g]=(0,o.useState)({}),b=()=>{d(null),f(null)},h=(e,t,r)=>{b(),d(t),f({name:e.name,id:e.id,countryId:e.countryId})},v=()=>{let e=[];n().forEach(s,t=>{e.push({title:t.title,id:t._id,lat:t.country&&t.country.coordinates&&parseFloat(t.country.coordinates[0].latitude),lng:t.country&&t.country.coordinates&&parseFloat(t.country.coordinates[0].longitude),countryId:t.country&&t.country._id})}),c([...e])};return(0,o.useEffect)(()=>{v(),s&&s.length>0&&g(n().groupBy(s,"country._id"))},[s]),(0,l.jsx)(a.A,{onClose:b,points:p,language:r,activeMarker:u,markerInfo:(0,l.jsx)(e=>{let{info:t}=e;return t&&t.countryId&&T[t.countryId]?(0,l.jsx)("ul",{children:T[t.countryId].map((e,t)=>(0,l.jsx)("li",{children:(0,l.jsx)("a",{href:"".concat(r,"/operation/show/").concat(e._id),children:e.title})},t))}):null},{info:m}),children:p.length>=1?p.map((e,t)=>(0,l.jsx)(i.A,{name:e.title,id:e.id,countryId:e.countryId,icon:{url:"/images/map-marker-white.svg"},onClick:h,position:e},t)):null})}},15641:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});var l=r(37876);r(14232);var o=r(62945);let s=e=>{let{name:t="Marker",id:r="",countryId:s="",type:n,icon:a,position:i,onClick:y,title:p,draggable:c=!1}=e;return i&&"number"==typeof i.lat&&"number"==typeof i.lng?(0,l.jsx)(o.pH,{position:i,icon:a,title:p||t,draggable:c,onClick:e=>{y&&y({name:t,id:r,countryId:s,type:n,position:i},{position:i,getPosition:()=>i},e)}}):null}},62174:(e,t,r)=>{(window.__NEXT_P=window.__NEXT_P||[]).push(["/operation/ListMapContainer",function(){return r(15214)}])},66619:(e,t,r)=>{"use strict";r.d(t,{A:()=>d});var l=r(37876);r(14232);var o=r(62945);let s=e=>{let{position:t,onCloseClick:r,children:s}=e;return(0,l.jsx)(o.Fu,{position:t,onCloseClick:r,children:(0,l.jsx)("div",{children:s})})},n="labels.text.fill",a="labels.text.stroke",i="road.highway",y="geometry.stroke",p=[{elementType:"geometry",stylers:[{color:"#1d2c4d"}]},{elementType:n,stylers:[{color:"#8ec3b9"}]},{elementType:a,stylers:[{color:"#1a3646"}]},{featureType:"administrative",elementType:"geometry",stylers:[{visibility:"off"}]},{featureType:"administrative.country",elementType:y,stylers:[{color:"#4b6878"}]},{featureType:"administrative.land_parcel",elementType:"labels",stylers:[{visibility:"off"}]},{featureType:"administrative.land_parcel",elementType:n,stylers:[{color:"#64779e"}]},{featureType:"administrative.province",elementType:y,stylers:[{color:"#4b6878"}]},{featureType:"landscape.man_made",elementType:y,stylers:[{color:"#334e87"}]},{featureType:"landscape.natural",elementType:"geometry",stylers:[{color:"#023e58"}]},{featureType:"poi",stylers:[{visibility:"off"}]},{featureType:"poi",elementType:"geometry",stylers:[{color:"#283d6a"}]},{featureType:"poi",elementType:"labels.text",stylers:[{visibility:"off"}]},{featureType:"poi",elementType:n,stylers:[{color:"#6f9ba5"}]},{featureType:"poi",elementType:a,stylers:[{color:"#1d2c4d"}]},{featureType:"poi.park",elementType:"geometry.fill",stylers:[{color:"#023e58"}]},{featureType:"poi.park",elementType:n,stylers:[{color:"#3C7680"}]},{featureType:"road",stylers:[{visibility:"off"}]},{featureType:"road",elementType:"geometry",stylers:[{color:"#304a7d"}]},{featureType:"road",elementType:"labels.icon",stylers:[{visibility:"off"}]},{featureType:"road",elementType:n,stylers:[{color:"#98a5be"}]},{featureType:"road",elementType:a,stylers:[{color:"#1d2c4d"}]},{featureType:i,elementType:"geometry",stylers:[{color:"#2c6675"}]},{featureType:i,elementType:y,stylers:[{color:"#255763"}]},{featureType:i,elementType:n,stylers:[{color:"#b0d5ce"}]},{featureType:i,elementType:a,stylers:[{color:"#023e58"}]},{featureType:"road.local",elementType:"labels",stylers:[{visibility:"off"}]},{featureType:"transit",stylers:[{visibility:"off"}]},{featureType:"transit",elementType:n,stylers:[{color:"#98a5be"}]},{featureType:"transit",elementType:a,stylers:[{color:"#1d2c4d"}]},{featureType:"transit.line",elementType:"geometry.fill",stylers:[{color:"#283d6a"}]},{featureType:"transit.station",elementType:"geometry",stylers:[{color:"#3a4762"}]},{featureType:"water",elementType:"geometry",stylers:[{color:"#0e1626"}]},{featureType:"water",elementType:n,stylers:[{color:"#4e6d70"}]}];var c=r(89099),u=r(55316);let d=e=>{let{markerInfo:t,activeMarker:r,initialCenter:n,children:a,height:i=300,width:y="114%",language:d,zoom:m=1,minZoom:f=1,onClose:T}=e,{locale:g}=(0,c.useRouter)(),{isLoaded:b,loadError:h}=(0,u._)();return h?(0,l.jsx)("div",{children:"Error loading maps"}):b?(0,l.jsx)("div",{className:"map-container",children:(0,l.jsx)("div",{className:"mapprint",style:{width:y,height:i,position:"relative"},children:(0,l.jsxs)(o.u6,{mapContainerStyle:{width:y,height:"number"==typeof i?"".concat(i,"px"):i},center:n||{lat:52.520017,lng:13.404195},zoom:m,onLoad:e=>{e.setOptions({styles:p})},options:{minZoom:f,draggable:!0,keyboardShortcuts:!1,streetViewControl:!1,panControl:!1,clickableIcons:!1,mapTypeControl:!1,fullscreenControl:!0},children:[a,t&&r&&r.getPosition&&(0,l.jsx)(s,{position:r.getPosition(),onCloseClick:()=>{console.log("close click"),null==T||T()},children:t})]})})}):(0,l.jsx)("div",{children:"Loading Maps..."})}}},e=>{var t=t=>e(e.s=t);e.O(0,[9759,636,6593,8792],()=>t(62174)),_N_E=e.O()}]);
//# sourceMappingURL=ListMapContainer-a7d11e3eba3c6d51.js.map