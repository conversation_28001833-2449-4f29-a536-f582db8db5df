{"version": 3, "file": "static/chunks/pages/operation/components/OperationDetailsAccordian-c728916deb4c2951.js", "mappings": "2OA2GA,MAjGkC,IAC9B,GAAM,GAAEA,CAAC,CAAE,CAAGC,CAAAA,EAAAA,EAAAA,EAAAA,CAAcA,CAAC,MAgGlBC,IA/FLC,EAAa,mBA+FiBD,EA9F9BE,EAAwB,YACxB,CAACC,EAASC,EAAW,CAAGC,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,EAAC,GACvC,MACI,+BACI,WAACC,EAAAA,CAASA,CAACC,IAAI,EAACC,SAAS,cACrB,WAACF,EAAAA,CAASA,CAACG,MAAM,EAACC,QAAS,IAAMN,EAAW,CAACD,aACzC,UAACQ,MAAAA,CAAIC,UAAU,qBAAad,EAAE,sBAC9B,UAACa,MAAAA,CAAIC,UAAU,qBACVT,EACG,UAACU,EAAAA,CAAeA,CAAAA,CAACC,KAAMC,EAAAA,GAAOA,CAAEC,MAAM,SAEtC,UAACH,EAAAA,CAAeA,CAAAA,CAACC,KAAMG,EAAAA,GAAMA,CAAED,MAAM,cAIjD,UAACV,EAAAA,CAASA,CAACY,IAAI,WACP,WAACC,EAAAA,CAAGA,CAAAA,CAACP,UAAU,0BACX,WAACQ,EAAAA,CAAGA,CAAAA,CAACC,EAAE,IAACC,GAAI,EAAGC,GAAI,GAAIX,UAAU,iBAC7B,WAACY,IAAAA,WACG,UAACC,IAAAA,UAAG3B,EAAE,gBAAkB,IACxB,UAAC4B,OAAAA,UACIC,EAAMC,SAAS,CAACC,WAAW,CACtBF,EAAMC,SAAS,CAACC,WAAW,CAACC,KAAK,CACjC,UAGbC,SAqExBA,CACmB,CACxBC,CAkBC,EAED,MACI,WAACrB,MAAAA,CAAIC,UAAU,6BACX,UAACa,IAAAA,UAAG3B,EAAE,YAAc,IACpB,UAAC4B,OAAAA,UACG,UAACO,KAAAA,CAAGrB,UAAU,2BACToB,EAAcE,MAAM,EAAIF,EAAcE,MAAM,CAACC,MAAM,EAAI,EAClDH,EAAcE,MAAM,CAACE,GAAG,CAAC,CAACC,EAAMC,IACvB,UAACC,KAAAA,UAAYF,EAAKP,KAAK,CAACU,EAAE,EAAjBF,IAElB,WAK1B,EAzGuDxC,EAAG6B,EAAMC,SAAS,EACzC,WAACJ,IAAAA,WACG,UAACC,IAAAA,UAAG3B,EAAE,cAAgB,IACtB,UAAC4B,OAAAA,UACIC,EAAMC,SAAS,CAACa,QAAQ,CACnBd,EAAMC,SAAS,CAACa,QAAQ,CAACX,KAAK,CAC9B,UAGd,WAACN,IAAAA,WACG,UAACC,IAAAA,UAAG3B,EAAE,aAAe,IACrB,UAAC4B,OAAAA,UACIgB,IAAOf,EAAMC,SAAS,CAACe,UAAU,EAAEC,MAAM,CACtC3C,QAIZ,EALeyC,CAKf,QAAClB,IAAAA,WACG,UAACC,IAAAA,UAAG3B,EAAE,kBAAoB,IAC1B,UAAC4B,OAAAA,UACIgB,IAAOf,EAAMC,SAAS,CAACiB,UAAU,EAAED,MAAM,CACtC3C,UADGyC,CAOnB,WAACtB,EAAAA,CAAGA,CAAAA,CAACC,EAAE,IAACC,GAAI,EAAGC,GAAI,GAAIX,UAAU,iBAC7B,WAACY,IAAAA,WACG,UAACC,IAAAA,UAAG3B,EAAE,wBAA0B,IAChC,UAAC4B,OAAAA,UACIC,EAAMC,SAAS,CAACkB,OAAO,CAClBnB,EAAMC,SAAS,CAACkB,OAAO,CAAChB,KAAK,CAC7B,UAGd,WAACN,IAAAA,WACG,UAACC,IAAAA,UAAG3B,EAAE,qBAAuB,IAC7B,WAAC4B,OAAAA,WAAK,IAAEC,EAAMC,SAAS,CAACmB,MAAM,CAACjB,KAAK,CAAC,UAEzC,WAACN,IAAAA,WACG,UAACC,IAAAA,UAAG3B,EAAE,eAAiB,IACvB,UAAC4B,OAAAA,UACIC,EAAMC,SAAS,CAACoB,UAAU,CACrBN,IAAOf,EAAMC,SAAS,CAACoB,UAAU,EAAEJ,MAAM,CACvC1C,GAEF,OAHMwC,GAMpB,WAAClB,IAAAA,WACG,UAACC,IAAAA,UAAG3B,EAAE,aAAe,IACrB,UAAC4B,OAAAA,UACIC,EAAMC,SAAS,CAACqB,QAAQ,CACnBP,IAAOf,EAAMC,SAAS,CAACqB,QAAQ,EAAEL,MAAM,CACrC1C,GAEF,SAHMwC,aAYpD,mBCxGA,4CACA,kDACA,WACA,OAAe,EAAQ,KAAuE,CAC9F,EACA,SAFsB", "sources": ["webpack://_N_E/./pages/operation/components/OperationDetailsAccordian.tsx", "webpack://_N_E/?116a"], "sourcesContent": ["//Import Library\r\nimport React, { useState } from \"react\";\r\nimport { faMinus, faPlus } from \"@fortawesome/free-solid-svg-icons\";\r\nimport { FontAwesomeIcon } from \"@fortawesome/react-fontawesome\";\r\nimport moment from \"moment\";\r\nimport { Accordion, Card, Col, Row } from \"react-bootstrap\";\r\n\r\n//Import services/components\r\nimport { useTranslation } from 'next-i18next';\r\n\r\nconst OperationDetailsAccordian = (props: any) => {\r\n    const { t } = useTranslation('common');\r\n    const formatDate = \"MM-D-YYYY HH:mm:ss\";\r\n    const formatDateWithoutTime = \"MM-D-YYYY\";\r\n    const [section, setSection] = useState(false);\r\n    return (\r\n        <>\r\n            <Accordion.Item eventKey=\"0\">\r\n                <Accordion.Header onClick={() => setSection(!section)}>\r\n                    <div className=\"cardTitle\">{t(\"OperationDetails\")}</div>\r\n                    <div className=\"cardArrow\">\r\n                        {section ? (\r\n                            <FontAwesomeIcon icon={faMinus} color=\"#fff\" />\r\n                        ) : (\r\n                            <FontAwesomeIcon icon={faPlus} color=\"#fff\" />\r\n                        )}\r\n                    </div>\r\n                </Accordion.Header>\r\n                <Accordion.Body>\r\n                        <Row className=\"operationData\">\r\n                            <Col md lg={6} sm={12} className=\"ps-0\">\r\n                                <p>\r\n                                    <b>{t(\"HazardType\")}</b>:\r\n                                    <span>\r\n                                        {props.operation.hazard_type\r\n                                            ? props.operation.hazard_type.title\r\n                                            : null}\r\n                                    </span>\r\n                                </p>\r\n                                {hazard_separated_func(t, props.operation)}\r\n                                <p>\r\n                                    <b>{t(\"Syndrome\")}</b>:\r\n                                    <span>\r\n                                        {props.operation.syndrome\r\n                                            ? props.operation.syndrome.title\r\n                                            : null}\r\n                                    </span>\r\n                                </p>\r\n                                <p>\r\n                                    <b>{t(\"Created\")}</b>:\r\n                                    <span>\r\n                                        {moment(props.operation.created_at).format(\r\n                                            formatDate\r\n                                        )}\r\n                                    </span>\r\n                                </p>\r\n                                <p>\r\n                                    <b>{t(\"LastModified\")}</b>:\r\n                                    <span>\r\n                                        {moment(props.operation.updated_at).format(\r\n                                            formatDate\r\n                                        )}\r\n                                    </span>\r\n                                </p>\r\n                            </Col>\r\n\r\n                            <Col md lg={6} sm={12} className=\"ps-0\">\r\n                                <p>\r\n                                    <b>{t(\"CountryOrTerritory\")}</b>:\r\n                                    <span>\r\n                                        {props.operation.country\r\n                                            ? props.operation.country.title\r\n                                            : null}\r\n                                    </span>\r\n                                </p>\r\n                                <p>\r\n                                    <b>{t(\"OperationStatus\")}</b>:\r\n                                    <span> {props.operation.status.title} </span>\r\n                                </p>\r\n                                <p>\r\n                                    <b>{t(\"StartDate\")}</b>:\r\n                                    <span>\r\n                                        {props.operation.start_date\r\n                                            ? moment(props.operation.start_date).format(\r\n                                                formatDateWithoutTime\r\n                                            )\r\n                                            : null}\r\n                                    </span>\r\n                                </p>\r\n                                <p>\r\n                                    <b>{t(\"EndDate\")}</b>:\r\n                                    <span>\r\n                                        {props.operation.end_date\r\n                                            ? moment(props.operation.end_date).format(\r\n                                                formatDateWithoutTime\r\n                                            )\r\n                                            : null}\r\n                                    </span>\r\n                                </p>\r\n                            </Col>\r\n                        </Row>\r\n                </Accordion.Body>\r\n            </Accordion.Item>\r\n        </>\r\n    )\r\n}\r\n\r\nexport default OperationDetailsAccordian;\r\nfunction hazard_separated_func(\r\n    t: (p: string) => string,\r\n    operationData: {\r\n        title: string;\r\n        timeline: any[];\r\n        description: string;\r\n        hazard_type: { title: string };\r\n        hazard: any[];\r\n        syndrome: { title: string };\r\n        created_at: string;\r\n        updated_at: string;\r\n        country: { title: string };\r\n        status: { title: string };\r\n        start_date: string;\r\n        end_date: string;\r\n        partners: any[];\r\n        images: any[];\r\n        images_src: any[];\r\n        document: any[];\r\n        doc_src: any[];\r\n    }\r\n) {\r\n    return (\r\n        <div className=\"d-flex mb-2 pb-1\">\r\n            <b>{t(\"Hazard\")}</b>:\r\n            <span>\r\n                <ul className=\"comma-separated\">\r\n                    {operationData.hazard && operationData.hazard.length >= 1\r\n                        ? operationData.hazard.map((item, i) => {\r\n                            return <li key={i}>{item.title.en}</li>;\r\n                        })\r\n                        : null}\r\n                </ul>\r\n            </span>\r\n        </div>\r\n    );\r\n}", "\n    (window.__NEXT_P = window.__NEXT_P || []).push([\n      \"/operation/components/OperationDetailsAccordian\",\n      function () {\n        return require(\"private-next-pages/operation/components/OperationDetailsAccordian.tsx\");\n      }\n    ]);\n    if(module.hot) {\n      module.hot.dispose(function () {\n        window.__NEXT_P.push([\"/operation/components/OperationDetailsAccordian\"])\n      });\n    }\n  "], "names": ["t", "useTranslation", "OperationDetailsAccordian", "formatDate", "formatDateWithoutTime", "section", "setSection", "useState", "Accordion", "<PERSON><PERSON>", "eventKey", "Header", "onClick", "div", "className", "FontAwesomeIcon", "icon", "faMinus", "color", "faPlus", "Body", "Row", "Col", "md", "lg", "sm", "p", "b", "span", "props", "operation", "hazard_type", "title", "hazard_separated_func", "operationData", "ul", "hazard", "length", "map", "item", "i", "li", "en", "syndrome", "moment", "created_at", "format", "updated_at", "country", "status", "start_date", "end_date"], "sourceRoot": "", "ignoreList": []}