"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[658],{12613:(e,r,t)=>{t.d(r,{A:()=>l});var a=t(37876);t(14232);var s=t(56856);let l=e=>(0,a.jsx)(s.Ay,{...e})},29335:(e,r,t)=>{t.d(r,{A:()=>A});var a=t(15039),s=t.n(a),l=t(14232),n=t(77346),i=t(37876);let o=l.forwardRef((e,r)=>{let{className:t,bsPrefix:a,as:l="div",...o}=e;return a=(0,n.oU)(a,"card-body"),(0,i.jsx)(l,{ref:r,className:s()(t,a),...o})});o.displayName="CardBody";let d=l.forwardRef((e,r)=>{let{className:t,bsPrefix:a,as:l="div",...o}=e;return a=(0,n.oU)(a,"card-footer"),(0,i.jsx)(l,{ref:r,className:s()(t,a),...o})});d.displayName="CardFooter";var c=t(81764);let u=l.forwardRef((e,r)=>{let{bsPrefix:t,className:a,as:o="div",...d}=e,u=(0,n.oU)(t,"card-header"),m=(0,l.useMemo)(()=>({cardHeaderBsPrefix:u}),[u]);return(0,i.jsx)(c.A.Provider,{value:m,children:(0,i.jsx)(o,{ref:r,...d,className:s()(a,u)})})});u.displayName="CardHeader";let m=l.forwardRef((e,r)=>{let{bsPrefix:t,className:a,variant:l,as:o="img",...d}=e,c=(0,n.oU)(t,"card-img");return(0,i.jsx)(o,{ref:r,className:s()(l?"".concat(c,"-").concat(l):c,a),...d})});m.displayName="CardImg";let p=l.forwardRef((e,r)=>{let{className:t,bsPrefix:a,as:l="div",...o}=e;return a=(0,n.oU)(a,"card-img-overlay"),(0,i.jsx)(l,{ref:r,className:s()(t,a),...o})});p.displayName="CardImgOverlay";let h=l.forwardRef((e,r)=>{let{className:t,bsPrefix:a,as:l="a",...o}=e;return a=(0,n.oU)(a,"card-link"),(0,i.jsx)(l,{ref:r,className:s()(t,a),...o})});h.displayName="CardLink";var x=t(46052);let f=(0,x.A)("h6"),g=l.forwardRef((e,r)=>{let{className:t,bsPrefix:a,as:l=f,...o}=e;return a=(0,n.oU)(a,"card-subtitle"),(0,i.jsx)(l,{ref:r,className:s()(t,a),...o})});g.displayName="CardSubtitle";let v=l.forwardRef((e,r)=>{let{className:t,bsPrefix:a,as:l="p",...o}=e;return a=(0,n.oU)(a,"card-text"),(0,i.jsx)(l,{ref:r,className:s()(t,a),...o})});v.displayName="CardText";let y=(0,x.A)("h5"),j=l.forwardRef((e,r)=>{let{className:t,bsPrefix:a,as:l=y,...o}=e;return a=(0,n.oU)(a,"card-title"),(0,i.jsx)(l,{ref:r,className:s()(t,a),...o})});j.displayName="CardTitle";let b=l.forwardRef((e,r)=>{let{bsPrefix:t,className:a,bg:l,text:d,border:c,body:u=!1,children:m,as:p="div",...h}=e,x=(0,n.oU)(t,"card");return(0,i.jsx)(p,{ref:r,...h,className:s()(a,x,l&&"bg-".concat(l),d&&"text-".concat(d),c&&"border-".concat(c)),children:u?(0,i.jsx)(o,{children:m}):m})});b.displayName="Card";let A=Object.assign(b,{Img:m,Title:j,Subtitle:g,Body:o,Link:h,Text:v,Header:u,Footer:d,ImgOverlay:p})},35611:(e,r,t)=>{t.d(r,{sx:()=>d,s3:()=>s.s3,ks:()=>s.ks,yk:()=>a.A});var a=t(54773),s=t(59200),l=t(37876),n=t(14232),i=t(39593),o=t(29504);let d={RadioGroup:e=>{let{name:r,valueSelected:t,onChange:a,errorMessage:s,children:o}=e,{errors:d,touched:c}=(0,i.j7)(),u=c[r]&&d[r];n.useMemo(()=>({name:r}),[r]);let m=n.Children.map(o,e=>n.isValidElement(e)&&function(e){return"object"==typeof e&&null!==e}(e.props)?n.cloneElement(e,{name:r,...e.props}):e);return(0,l.jsxs)("div",{children:[(0,l.jsx)("div",{className:"radio-group",children:m}),u&&(0,l.jsx)("div",{className:"invalid-feedback d-block",children:s||("string"==typeof d[r]?d[r]:String(d[r]))})]})},RadioItem:e=>{let{id:r,label:t,value:a,name:s,disabled:n}=e,{values:d,setFieldValue:c}=(0,i.j7)(),u=s||r;return(0,l.jsx)(o.A.Check,{type:"radio",id:r,label:t,value:a,name:u,checked:d[u]===a,onChange:e=>{c(u,e.target.value)},disabled:n,inline:!0})}};a.A,s.ks,s.s3},54773:(e,r,t)=>{t.d(r,{A:()=>o});var a=t(37876),s=t(14232),l=t(39593),n=t(91408);let i=(0,s.forwardRef)((e,r)=>{let{children:t,onSubmit:s,autoComplete:i,className:o,onKeyPress:d,initialValues:c,...u}=e,m=n.Ik().shape({});return(0,a.jsx)(l.l1,{initialValues:c||{},validationSchema:m,onSubmit:(e,r)=>{let t={preventDefault:()=>{},stopPropagation:()=>{},currentTarget:null,target:null,nativeEvent:new Event("submit"),bubbles:!1,cancelable:!0,defaultPrevented:!1,eventPhase:0,isTrusted:!1,timeStamp:Date.now(),type:"submit",isDefaultPrevented:()=>!1,isPropagationStopped:()=>!1,persist:()=>{}};s&&s(t,e,r)},...u,children:e=>(0,a.jsx)(l.lV,{ref:r,onSubmit:e.handleSubmit,autoComplete:i,className:o,onKeyPress:d,children:"function"==typeof t?t(e):t})})});i.displayName="ValidationFormWrapper";let o=i},59200:(e,r,t)=>{t.d(r,{ks:()=>n,s3:()=>i});var a=t(37876);t(14232);var s=t(29504),l=t(39593);let n=e=>{let{name:r,id:t,required:n,validator:i,errorMessage:o,onChange:d,value:c,as:u,multiline:m,rows:p,pattern:h,...x}=e;return(0,a.jsx)(l.D0,{name:r,validate:e=>{let r="string"==typeof e?e:String(e||"");return n&&(!e||""===r.trim())?(null==o?void 0:o.validator)||"This field is required":i&&!i(e)?(null==o?void 0:o.validator)||"Invalid value":h&&e&&!new RegExp(h).test(e)?(null==o?void 0:o.pattern)||"Invalid format":void 0},children:e=>{let{field:r,meta:l}=e;return(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(s.A.Control,{...r,...x,id:t,as:u||"input",rows:p,isInvalid:l.touched&&!!l.error,onChange:e=>{r.onChange(e),d&&d(e)},value:void 0!==c?c:r.value}),l.touched&&l.error?(0,a.jsx)(s.A.Control.Feedback,{type:"invalid",children:l.error}):null]})}})},i=e=>{let{name:r,id:t,required:n,errorMessage:i,onChange:o,value:d,children:c,...u}=e;return(0,a.jsx)(l.D0,{name:r,validate:e=>{if(n&&(!e||""===e))return(null==i?void 0:i.validator)||"This field is required"},children:e=>{let{field:r,meta:l}=e;return(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(s.A.Control,{as:"select",...r,...u,id:t,isInvalid:l.touched&&!!l.error,onChange:e=>{r.onChange(e),o&&o(e)},value:void 0!==d?d:r.value,children:c}),l.touched&&l.error?(0,a.jsx)(s.A.Control.Feedback,{type:"invalid",children:l.error}):null]})}})}},67814:(e,r,t)=>{t.d(r,{KF:()=>w});var a=t(14232),s=t(37876);!function(e,{insertAt:r}={}){if(!e||typeof document>"u")return;let t=document.head||document.getElementsByTagName("head")[0],a=document.createElement("style");a.type="text/css","top"===r&&t.firstChild?t.insertBefore(a,t.firstChild):t.appendChild(a),a.styleSheet?a.styleSheet.cssText=e:a.appendChild(document.createTextNode(e))}(`.rmsc{--rmsc-main: #4285f4;--rmsc-hover: #f1f3f5;--rmsc-selected: #e2e6ea;--rmsc-border: #ccc;--rmsc-gray: #aaa;--rmsc-bg: #fff;--rmsc-p: 10px;--rmsc-radius: 4px;--rmsc-h: 38px}.rmsc *{box-sizing:border-box;transition:all .2s ease}.rmsc .gray{color:var(--rmsc-gray)}.rmsc .dropdown-content{position:absolute;z-index:1;top:100%;width:100%;padding-top:8px}.rmsc .dropdown-content .panel-content{overflow:hidden;border-radius:var(--rmsc-radius);background:var(--rmsc-bg);box-shadow:0 0 0 1px #0000001a,0 4px 11px #0000001a}.rmsc .dropdown-container{position:relative;outline:0;background-color:var(--rmsc-bg);border:1px solid var(--rmsc-border);border-radius:var(--rmsc-radius)}.rmsc .dropdown-container[aria-disabled=true]:focus-within{box-shadow:var(--rmsc-gray) 0 0 0 1px;border-color:var(--rmsc-gray)}.rmsc .dropdown-container:focus-within{box-shadow:var(--rmsc-main) 0 0 0 1px;border-color:var(--rmsc-main)}.rmsc .dropdown-heading{position:relative;padding:0 var(--rmsc-p);display:flex;align-items:center;width:100%;height:var(--rmsc-h);cursor:default;outline:0}.rmsc .dropdown-heading .dropdown-heading-value{overflow:hidden;text-overflow:ellipsis;white-space:nowrap;flex:1}.rmsc .clear-selected-button{cursor:pointer;background:none;border:0;padding:0;display:flex}.rmsc .options{max-height:260px;overflow-y:auto;margin:0;padding-left:0}.rmsc .options li{list-style:none;margin:0}.rmsc .select-item{box-sizing:border-box;cursor:pointer;display:block;padding:var(--rmsc-p);outline-offset:-1px;outline-color:var(--rmsc-primary)}.rmsc .select-item:hover{background:var(--rmsc-hover)}.rmsc .select-item.selected{background:var(--rmsc-selected)}.rmsc .no-options{padding:var(--rmsc-p);text-align:center;color:var(--rmsc-gray)}.rmsc .search{width:100%;position:relative;border-bottom:1px solid var(--rmsc-border)}.rmsc .search input{background:none;height:var(--rmsc-h);padding:0 var(--rmsc-p);width:100%;outline:0;border:0;font-size:1em}.rmsc .search input:focus{background:var(--rmsc-hover)}.rmsc .search-clear-button{cursor:pointer;position:absolute;top:0;right:0;bottom:0;background:none;border:0;padding:0 calc(var(--rmsc-p) / 2)}.rmsc .search-clear-button [hidden]{display:none}.rmsc .item-renderer{display:flex;align-items:baseline}.rmsc .item-renderer input{margin:0 5px 0 0}.rmsc .item-renderer.disabled{opacity:.5}.rmsc .spinner{animation:rotate 2s linear infinite}.rmsc .spinner .path{stroke:var(--rmsc-border);stroke-width:4px;stroke-linecap:round;animation:dash 1.5s ease-in-out infinite}@keyframes rotate{to{transform:rotate(360deg)}}@keyframes dash{0%{stroke-dasharray:1,150;stroke-dashoffset:0}50%{stroke-dasharray:90,150;stroke-dashoffset:-35}to{stroke-dasharray:90,150;stroke-dashoffset:-124}}
`);var l={allItemsAreSelected:"All items are selected.",clearSearch:"Clear Search",clearSelected:"Clear Selected",noOptions:"No options",search:"Search",selectAll:"Select All",selectAllFiltered:"Select All (Filtered)",selectSomeItems:"Select...",create:"Create"},n={value:[],hasSelectAll:!0,className:"multi-select",debounceDuration:200,options:[]},i=a.createContext({}),o=({props:e,children:r})=>{let[t,o]=(0,a.useState)(e.options);return(0,a.useEffect)(()=>{o(e.options)},[e.options]),(0,s.jsx)(i.Provider,{value:{t:r=>{var t;return(null==(t=e.overrideStrings)?void 0:t[r])||l[r]},...n,...e,options:t,setOptions:o},children:r})},d=()=>a.useContext(i),c={when:!0,eventTypes:["keydown"]};function u(e,r,t){let s=(0,a.useMemo)(()=>Array.isArray(e)?e:[e],[e]),l=Object.assign({},c,t),{when:n,eventTypes:i}=l,o=(0,a.useRef)(r),{target:d}=l;(0,a.useEffect)(()=>{o.current=r});let u=(0,a.useCallback)(e=>{s.some(r=>e.key===r||e.code===r)&&o.current(e)},[s]);(0,a.useEffect)(()=>{if(n&&"u">typeof window){let e=d?d.current:window;return i.forEach(r=>{e&&e.addEventListener(r,u)}),()=>{i.forEach(r=>{e&&e.removeEventListener(r,u)})}}},[n,i,s,d,r])}var m={ARROW_DOWN:"ArrowDown",ARROW_UP:"ArrowUp",ENTER:"Enter",ESCAPE:"Escape",SPACE:"Space"},p=(e,r)=>{let t;return function(...a){clearTimeout(t),t=setTimeout(()=>{e.apply(null,a)},r)}},h=()=>(0,s.jsxs)("svg",{width:"24",height:"24",fill:"none",stroke:"currentColor",strokeWidth:"2",className:"dropdown-search-clear-icon gray",children:[(0,s.jsx)("line",{x1:"18",y1:"6",x2:"6",y2:"18"}),(0,s.jsx)("line",{x1:"6",y1:"6",x2:"18",y2:"18"})]}),x=({checked:e,option:r,onClick:t,disabled:a})=>(0,s.jsxs)("div",{className:`item-renderer ${a?"disabled":""}`,children:[(0,s.jsx)("input",{type:"checkbox",onChange:t,checked:e,tabIndex:-1,disabled:a}),(0,s.jsx)("span",{children:r.label})]}),f=({itemRenderer:e=x,option:r,checked:t,tabIndex:l,disabled:n,onSelectionChanged:i,onClick:o})=>{let d=(0,a.useRef)(),c=()=>{n||i(!t)};return u([m.ENTER,m.SPACE],e=>{c(),e.preventDefault()},{target:d}),(0,s.jsx)("label",{className:`select-item ${t?"selected":""}`,role:"option","aria-selected":t,tabIndex:l,ref:d,children:(0,s.jsx)(e,{option:r,checked:t,onClick:e=>{c(),o(e)},disabled:n})})},g=({options:e,onClick:r,skipIndex:t})=>{let{disabled:a,value:l,onChange:n,ItemRenderer:i}=d(),o=(e,r)=>{a||n(r?[...l,e]:l.filter(r=>r.value!==e.value))};return(0,s.jsx)(s.Fragment,{children:e.map((e,n)=>{let d=n+t;return(0,s.jsx)("li",{children:(0,s.jsx)(f,{tabIndex:d,option:e,onSelectionChanged:r=>o(e,r),checked:!!l.find(r=>r.value===e.value),onClick:e=>r(e,d),itemRenderer:i,disabled:e.disabled||a})},(null==e?void 0:e.key)||n)})})},v=()=>{let{t:e,onChange:r,options:t,setOptions:l,value:n,filterOptions:i,ItemRenderer:o,disabled:c,disableSearch:x,hasSelectAll:v,ClearIcon:y,debounceDuration:j,isCreatable:b,onCreateOption:A}=d(),w=(0,a.useRef)(),C=(0,a.useRef)(),[k,S]=(0,a.useState)(""),[N,_]=(0,a.useState)(t),[E,I]=(0,a.useState)(""),[R,T]=(0,a.useState)(0),z=(0,a.useCallback)(p(e=>I(e),j),[]),D=(0,a.useMemo)(()=>{let e=0;return x||(e+=1),v&&(e+=1),e},[x,v]),O={label:e(k?"selectAllFiltered":"selectAll"),value:""},P=e=>{let r=N.filter(e=>!e.disabled).map(e=>e.value);if(e){let e=[...n.map(e=>e.value),...r];return(i?N:t).filter(r=>e.includes(r.value))}return n.filter(e=>!r.includes(e.value))},L=()=>{var e;I(""),S(""),null==(e=null==C?void 0:C.current)||e.focus()},F=e=>T(e);u([m.ARROW_DOWN,m.ARROW_UP],e=>{switch(e.code){case m.ARROW_UP:U(-1);break;case m.ARROW_DOWN:U(1);break;default:return}e.stopPropagation(),e.preventDefault()},{target:w});let M=async()=>{let e={label:k,value:k,__isNew__:!0};A&&(e=await A(k)),l([e,...t]),L(),r([...n,e])},G=async()=>i?await i(t,E):function(e,r){return r?e.filter(({label:e,value:t})=>null!=e&&null!=t&&e.toLowerCase().includes(r.toLowerCase())):e}(t,E),U=e=>{let r=R+e;T(r=Math.min(r=Math.max(0,r),t.length+Math.max(D-1,0)))};(0,a.useEffect)(()=>{var e,r;null==(r=null==(e=null==w?void 0:w.current)?void 0:e.querySelector(`[tabIndex='${R}']`))||r.focus()},[R]);let[W,B]=(0,a.useMemo)(()=>{let e=N.filter(e=>!e.disabled);return[e.every(e=>-1!==n.findIndex(r=>r.value===e.value)),0!==e.length]},[N,n]);(0,a.useEffect)(()=>{G().then(_)},[E,t]);let q=(0,a.useRef)();u([m.ENTER],M,{target:q});let H=b&&k&&!N.some(e=>(null==e?void 0:e.value)===k);return(0,s.jsxs)("div",{className:"select-panel",role:"listbox",ref:w,children:[!x&&(0,s.jsxs)("div",{className:"search",children:[(0,s.jsx)("input",{placeholder:e("search"),type:"text","aria-describedby":e("search"),onChange:e=>{z(e.target.value),S(e.target.value),T(0)},onFocus:()=>{T(0)},value:k,ref:C,tabIndex:0}),(0,s.jsx)("button",{type:"button",className:"search-clear-button",hidden:!k,onClick:L,"aria-label":e("clearSearch"),children:y||(0,s.jsx)(h,{})})]}),(0,s.jsxs)("ul",{className:"options",children:[v&&B&&(0,s.jsx)(f,{tabIndex:+(1!==D),checked:W,option:O,onSelectionChanged:e=>{r(P(e))},onClick:()=>F(1),itemRenderer:o,disabled:c}),N.length?(0,s.jsx)(g,{skipIndex:D,options:N,onClick:(e,r)=>F(r)}):H?(0,s.jsx)("li",{onClick:M,className:"select-item creatable",tabIndex:1,ref:q,children:`${e("create")} "${k}"`}):(0,s.jsx)("li",{className:"no-options",children:e("noOptions")})]})]})},y=({expanded:e})=>(0,s.jsx)("svg",{width:"24",height:"24",fill:"none",stroke:"currentColor",strokeWidth:"2",className:"dropdown-heading-dropdown-arrow gray",children:(0,s.jsx)("path",{d:e?"M18 15 12 9 6 15":"M6 9L12 15 18 9"})}),j=()=>{let{t:e,value:r,options:t,valueRenderer:a}=d(),l=0===r.length,n=r.length===t.length,i=a&&a(r,t);return l?(0,s.jsx)("span",{className:"gray",children:i||e("selectSomeItems")}):(0,s.jsx)("span",{children:i||(n?e("allItemsAreSelected"):r.map(e=>e.label).join(", "))})},b=({size:e=24})=>(0,s.jsx)("span",{style:{width:e,marginRight:"0.2rem"},children:(0,s.jsx)("svg",{width:e,height:e,className:"spinner",viewBox:"0 0 50 50",style:{display:"inline",verticalAlign:"middle"},children:(0,s.jsx)("circle",{cx:"25",cy:"25",r:"20",fill:"none",className:"path"})})}),A=()=>{let{t:e,onMenuToggle:r,ArrowRenderer:t,shouldToggleOnHover:l,isLoading:n,disabled:i,onChange:o,labelledBy:c,value:p,isOpen:x,defaultIsOpen:f,ClearSelectedIcon:g,closeOnChangedValue:A}=d();(0,a.useEffect)(()=>{A&&S(!1)},[p]);let[w,C]=(0,a.useState)(!0),[k,S]=(0,a.useState)(f),[N,_]=(0,a.useState)(!1),E=(0,a.useRef)();(function(e,r){let t=(0,a.useRef)(!1);(0,a.useEffect)(()=>{t.current?e():t.current=!0},r)})(()=>{r&&r(k)},[k]),(0,a.useEffect)(()=>{void 0===f&&"boolean"==typeof x&&(C(!1),S(x))},[x]),u([m.ENTER,m.ARROW_DOWN,m.SPACE,m.ESCAPE],e=>{var r;["text","button"].includes(e.target.type)&&[m.SPACE,m.ENTER].includes(e.code)||(w&&(e.code===m.ESCAPE?(S(!1),null==(r=null==E?void 0:E.current)||r.focus()):S(!0)),e.preventDefault())},{target:E});let I=e=>{w&&l&&S(e)};return(0,s.jsxs)("div",{tabIndex:0,className:"dropdown-container","aria-labelledby":c,"aria-expanded":k,"aria-readonly":!0,"aria-disabled":i,ref:E,onFocus:()=>!N&&_(!0),onBlur:e=>{!e.currentTarget.contains(e.relatedTarget)&&w&&(_(!1),S(!1))},onMouseEnter:()=>I(!0),onMouseLeave:()=>I(!1),children:[(0,s.jsxs)("div",{className:"dropdown-heading",onClick:()=>{w&&S(!n&&!i&&!k)},children:[(0,s.jsx)("div",{className:"dropdown-heading-value",children:(0,s.jsx)(j,{})}),n&&(0,s.jsx)(b,{}),p.length>0&&null!==g&&(0,s.jsx)("button",{type:"button",className:"clear-selected-button",onClick:e=>{e.stopPropagation(),o([]),w&&S(!1)},disabled:i,"aria-label":e("clearSelected"),children:g||(0,s.jsx)(h,{})}),(0,s.jsx)(t||y,{expanded:k})]}),k&&(0,s.jsx)("div",{className:"dropdown-content",children:(0,s.jsx)("div",{className:"panel-content",children:(0,s.jsx)(v,{})})})]})},w=e=>(0,s.jsx)(o,{props:e,children:(0,s.jsx)("div",{className:`rmsc ${e.className||"multi-select"}`,children:(0,s.jsx)(A,{})})})},80658:(e,r,t)=>{t.r(r),t.d(r,{default:()=>S});var a=t(37876),s=t(14232),l=t(89099),n=t.n(l),i=t(49589),o=t(29335),d=t(56970),c=t(37784),u=t(29504),m=t(60282),p=t(48230),h=t.n(p),x=t(35611),f=t(54773),g=t(67814),v=t(10841),y=t.n(v),j=t(97685),b=t(89673),A=t(12613),w=t(53718),C=t(31753),k=t(5671);let S=e=>{let r={title:"",operation:null,date:null,syndrome:"",description:"",hazard_type:[],country_regions:[],hazard:[],status:[],rki_monitored:!1,country:[],world_region:null,more_info:"",laboratory_confirmed:!1,officially_validated:!1,images:[],images_src:[]},t=(0,s.useRef)(null),l=(0,s.useRef)(null),{t:p,i18n:v}=(0,C.Bd)("common"),S="de"===v.language?{title_de:"asc"}:{title:"asc"},N="fr"===v.language?"en":v.language,_=v.language,[E,I]=(0,s.useState)([]),[R,T]=(0,s.useState)([]),[z,D]=(0,s.useState)(r),[O,P]=(0,s.useState)([]),[L,F]=(0,s.useState)([]),[M,G]=(0,s.useState)([]),[U,W]=(0,s.useState)([]),[B,q]=(0,s.useState)([]),[H,V]=(0,s.useState)([]),[$,K]=(0,s.useState)([]),[J,Q]=(0,s.useState)([]),[X,Y]=(0,s.useState)([]),[Z]=(0,s.useState)(N?"title.".concat(N):"title.en"),[ee,er]=(0,s.useState)([]),et={query:{},sort:S,limit:"~",languageCode:_},ea=async e=>{let r=await w.A.get("/country",e);r&&Array.isArray(r.data)&&G(r.data)},es=async e=>{let r=await w.A.get("/hazardtype",e);r&&Array.isArray(r.data)&&W(r.data)},el=async e=>{let r=await w.A.get("/syndrome",e);r&&Array.isArray(r.data)&&q(r.data)},en=async e=>{let r=await w.A.get("/operation",e);r&&Array.isArray(r.data)&&V(r.data)},ei=async e=>{let r=await w.A.get("/eventstatus",e);r&&Array.isArray(r.data)&&K(r.data)},eo=async e=>{let r=await w.A.get("/risklevel",e);r&&Array.isArray(r.data)&&(Q(r.data),Y(r.data),er(r.data))},ed=(e,r)=>{ey(r.country),eb(r.hazard_type),I(r.images?r.images:[]),T(r.images_src?r.images_src:[]),D(e=>({...e,...r})),e&&eh(r=>({...r,riskcountry:e.country?e.country._id:"",region:e.region?e.region._id:"",international:e.international?e.international._id:"",description:e.description?e.description:""}))},ec=(e,r,t,a)=>{a.country_regions=e&&e.length>0?e.map((e,r)=>({label:e.title,value:e._id})):[],a.date=t?y()(t).toDate():null,a.hazard=r&&r.length>0?r.map((e,r)=>({label:e.title[N],value:e._id})):[]},eu=(e,r,t,a,s,l)=>{e.syndrome=r&&r._id?r._id:"",e.status=l&&l._id?l._id:"",e.country=t&&t._id?t._id:"",e.hazard_type=a&&a._id?a._id:"",e.operation=s&&s._id?s._id:""};(0,s.useEffect)(()=>{e.routes&&"edit"===e.routes[0]&&e.routes[1]&&(async()=>{let r=await w.A.get("/event/".concat(e.routes[1]),et);if(r){let{status:e,syndrome:t,country:a,hazard_type:s,risk_assessment:l,country_regions:n,hazard:i,operations:o,date:d}=r;eu(r,t,a,s,o,e),ec(n,i,d,r),ed(l,r)}})(),ea(et),es(et),el(et),en(et),ei(et),eo(et)},[]);let em={riskcountry:null,region:null,international:null,description:null,country:null},[ep,eh]=(0,s.useState)(em),ex=e=>{let{name:r,value:t}=e.target;eh(e=>({...e,[r]:t}))},ef=e=>{eh(r=>({...r,description:e}))},eg=async r=>{if(null==z.date)return void l.current.focus();if(t.current&&t.current.setAttribute("disabled","disabled"),r&&r.preventDefault&&r.preventDefault(),!z.title||!z.country){t.current&&t.current.removeAttribute("disabled");return}try{let r,a;z.country_regions=z.country_regions?z.country_regions.map((e,r)=>e.value):[],z.hazard=z.hazard?z.hazard.map((e,r)=>e.value):[],z.operation=z.operation||null,z.syndrome=z.syndrome||null,ep.country=ep.riskcountry,e.routes&&"edit"===e.routes[0]&&e.routes[1]?(a="toast.Eventupdatedsuccessfully",r=await w.A.patch("/event/".concat(e.routes[1]),{...z,risk_assessment:ep})):(a="toast.Eventaddedsuccessfully",r=await w.A.post("/event",{...z,risk_assessment:ep})),r&&r._id?(j.Ay.success(p(a)),n().push("/event/[...routes]","/event/show/".concat(r._id))):(t.current&&t.current.removeAttribute("disabled"),"date should not be empty"===r&&(r=p("toast.dateShouldNotBeEmpty")),j.Ay.error(r||p("toast.errorOccurred")))}catch(e){t.current&&t.current.removeAttribute("disabled"),console.error("Error submitting event:",e),j.Ay.error(p("toast.errorOccurred"))}},ev=e=>{D(r=>({...r,...e}))},ey=async e=>{let r=[];if(e){let t=await w.A.get("/country_region/".concat(e),et);t&&t.data&&(r=t.data.map((e,r)=>({label:e.title,value:e._id}))).sort((e,r)=>e.label.localeCompare(r.label))}P(r)},ej={query:{enabled:!0},sort:{[Z]:"asc"},limit:"~",select:"-description -first_letter -hazard_type -picture -picture_source  -created_at -updated_at"},eb=async e=>{let r=[];if(e){let t=await w.A.get("/hazard_hazard_type/".concat(e),ej);t&&t.data&&(r=t.data.map(e=>({label:e.title[N],value:e._id})))}F(r)},eA=(e,r)=>{D(t=>({...t,[r]:e}))},ew=e=>{let{name:r,value:t}=e.target;if(D(e=>({...e,[r]:t})),"country"===r){let r=e.target.selectedIndex;if(e.target&&r&&null!=r){let t=e.target[r].getAttribute("data-worldregion");D(e=>({...e,world_region:t}))}}"country"===r&&(ey(t),ev({country_regions:[]})),"hazard_type"===r&&(eb(t),ev({hazard:[]}))},eC=(0,s.useRef)(null),ek=e=>{D(r=>({...r,description:e}))},eS=e=>{D(r=>({...r,more_info:e}))},eN=e=>{let r=e.map(e=>e.serverID);D(e=>({...e,images:r}))},e_=e=>{D(r=>({...r,images_src:e}))};return(0,a.jsx)(i.A,{fluid:!0,children:(0,a.jsx)(o.A,{style:{marginTop:"5px",boxShadow:"0 4px 8px 0 rgba(0, 0, 0, 0.2), 0 6px 20px 0 rgba(0, 0, 0, 0.19)"},children:(0,a.jsx)(f.A,{onSubmit:eg,ref:eC,initialValues:z,enableReinitialize:!0,children:(0,a.jsxs)(o.A.Body,{children:[(0,a.jsx)(d.A,{children:(0,a.jsxs)(c.A,{children:[(0,a.jsx)(o.A.Title,{children:"edit"===e.routes[0]?p("editEvent"):p("addEvent")}),(0,a.jsx)("hr",{})]})}),(0,a.jsxs)(d.A,{className:"mb-3",children:[(0,a.jsx)(c.A,{children:(0,a.jsxs)(u.A.Group,{children:[(0,a.jsx)(u.A.Label,{className:"required-field",children:p("Events.forms.EventID")}),(0,a.jsx)(x.ks,{name:"title",id:"eventId",value:z.title,validator:e=>""!==e.trim(),errorMessage:{validator:p("PleaseAddtheEventTitle")},onChange:ew,required:!0})]})}),(0,a.jsx)(c.A,{children:(0,a.jsxs)(u.A.Group,{children:[(0,a.jsxs)("div",{className:"d-flex",children:[(0,a.jsx)(u.A.Label,{className:"d-flex me-3",children:p("Events.forms.OperationName")}),(0,a.jsx)(u.A.Check,{className:"ms-4",type:"switch",name:"rki_monitored",id:"custom-switch",onChange:e=>{D(e=>({...e,rki_monitored:!e.rki_monitored}))},label:p("Events.forms.MonitoredbyRKI"),checked:z.rki_monitored})]}),(0,a.jsxs)(x.s3,{name:"operation",id:"operation",value:null===z.operation?"":z.operation,onChange:ew,children:[(0,a.jsx)("option",{value:"",children:p("Events.forms.SelectOperationName")}),H.map((e,r)=>(0,a.jsx)("option",{value:e._id,children:e.title},r))]})]})})]}),(0,a.jsxs)(d.A,{className:"mb-3",children:[(0,a.jsx)(c.A,{children:(0,a.jsxs)(u.A.Group,{children:[(0,a.jsx)(u.A.Label,{className:"required-field",children:p("Events.forms.CountryorTerritory")}),(0,a.jsxs)(x.s3,{name:"country",id:"country",value:Array.isArray(z.country)&&0===z.country.length?"":z.country,onChange:ew,required:!0,errorMessage:p("thisfieldisrequired"),children:[(0,a.jsx)("option",{value:"",children:p("Events.forms.SelectCountry")}),M.map((e,r)=>(0,a.jsx)("option",{"data-worldregion":e.world_region._id,value:e._id,children:e.title},r))]})]})}),(0,a.jsx)(c.A,{sm:6,md:6,lg:6,children:(0,a.jsxs)(u.A.Group,{style:{maxWidth:"600px"},children:[(0,a.jsx)(u.A.Label,{children:p("CountryRegions")}),(0,a.jsx)(g.KF,{overrideStrings:{selectSomeItems:p("SelectRegions"),allItemsAreSelected:p("Events.forms.AllRegionsareSelected")},options:O,value:z.country_regions,onChange:e=>{D(r=>({...r,country_regions:e}))},className:"region",labelledBy:p("SelectRegions")})]})})]}),(0,a.jsxs)(d.A,{className:"mb-3",children:[(0,a.jsx)(c.A,{sm:6,md:6,lg:4,children:(0,a.jsxs)(u.A.Group,{children:[(0,a.jsx)(u.A.Label,{className:"required-field",children:p("Events.forms.Status")}),(0,a.jsxs)(x.s3,{name:"status",id:"status",value:Array.isArray(z.status)&&0===z.status.length?"":z.status,onChange:ew,required:!0,errorMessage:p("thisfieldisrequired"),children:[(0,a.jsx)("option",{value:"",children:p("Events.forms.SelectStatus")}),$.map((e,r)=>(0,a.jsx)("option",{value:e._id,children:e.title},r))]})]})}),(0,a.jsx)(c.A,{sm:6,md:6,lg:4,children:(0,a.jsxs)(u.A.Group,{children:[(0,a.jsx)(u.A.Label,{className:"required-field",children:p("Events.forms.HazardType")}),(0,a.jsxs)(x.s3,{name:"hazard_type",id:"hazardType",value:Array.isArray(z.hazard_type)&&0===z.hazard_type.length?"":z.hazard_type,onChange:ew,required:!0,errorMessage:p("thisfieldisrequired"),children:[(0,a.jsx)("option",{value:"",children:p("Events.forms.SelectHazardType")}),U.map((e,r)=>(0,a.jsx)("option",{value:e._id,children:e.title},r))]})]})}),(0,a.jsx)(c.A,{sm:6,md:6,lg:4,children:(0,a.jsxs)(u.A.Group,{style:{maxWidth:"600px"},children:[(0,a.jsxs)(u.A.Label,{children:[p("Events.forms.Hazard")," "]}),(0,a.jsx)(g.KF,{overrideStrings:{selectSomeItems:p("Events.forms.SelectHazard"),allItemsAreSelected:p("Events.forms.AllHazardsareSelected")},options:L,value:z.hazard,onChange:e=>{D(r=>({...r,hazard:e}))},className:"region",labelledBy:p("Events.forms.SelectHazard")})]})})]}),(0,a.jsxs)(d.A,{className:"mb-3",children:[(0,a.jsx)(c.A,{md:!0,lg:3,sm:12,children:(0,a.jsxs)(u.A.Group,{children:[(0,a.jsx)(u.A.Label,{children:p("Events.forms.Syndrome")}),(0,a.jsxs)(x.s3,{name:"syndrome",id:"syndrome",value:z.syndrome,onChange:ew,children:[(0,a.jsx)("option",{value:"",children:p("Events.forms.SelectSyndrome")}),B.map((e,r)=>(0,a.jsx)("option",{value:e._id,children:e.title},r))]})]})}),(0,a.jsx)(c.A,{children:(0,a.jsxs)(u.A.Group,{children:[(0,a.jsx)(d.A,{children:(0,a.jsx)(c.A,{children:(0,a.jsx)(u.A.Label,{className:"required-field",children:p("Events.forms.Date")})})}),(0,a.jsx)("label",{className:"date-validation w-100",ref:l,children:(0,a.jsx)(A.A,{selected:z.date,maxDate:new Date,errorMessage:p("PleaseselecttheDate"),onChange:e=>eA(e,"date"),dateFormat:"MMMM d, yyyy",placeholderText:p("Events.forms.SelectDate")})})]})}),(0,a.jsx)(c.A,{sm:6,lg:3,className:"align-self-center",children:(0,a.jsx)(u.A.Group,{children:(0,a.jsx)(u.A.Check,{style:{all:"unset"},type:"checkbox",name:p("Events.forms.LaboratoryConfirmed"),label:p("Events.forms.LaboratoryConfirmed"),checked:z.laboratory_confirmed,onChange:()=>{D(e=>({...e,laboratory_confirmed:!e.laboratory_confirmed}))},inline:!0})})}),(0,a.jsx)(c.A,{sm:6,lg:3,className:"align-self-center",children:(0,a.jsx)(u.A.Group,{controlId:"validated_by_official",children:(0,a.jsx)(u.A.Check,{name:p("Events.forms.ValidatedbyOfficial"),label:p("Events.forms.ValidatedbyOfficial"),checked:z.officially_validated,onChange:()=>{D(e=>({...e,officially_validated:!e.officially_validated}))},inline:!0})})})]}),(0,a.jsx)(d.A,{className:"mb-3",children:(0,a.jsx)(c.A,{children:(0,a.jsxs)(u.A.Group,{children:[(0,a.jsx)(u.A.Label,{children:p("Events.forms.Description")}),(0,a.jsx)(k.x,{initContent:z.description,onChange:e=>ek(e)})]})})}),(0,a.jsx)(d.A,{className:"mb-3",children:(0,a.jsxs)(c.A,{children:[(0,a.jsx)(o.A.Text,{children:(0,a.jsx)("b",{children:p("Events.forms.RiskAssessment")})}),(0,a.jsx)("hr",{})]})}),(0,a.jsxs)(d.A,{className:"mb-3",children:[(0,a.jsx)(c.A,{sm:6,md:6,lg:4,children:(0,a.jsxs)(u.A.Group,{children:[(0,a.jsx)(u.A.Label,{children:p("Events.forms.SelectCountryrisklevel")}),(0,a.jsxs)(x.s3,{name:"riskcountry",id:"riskcountry",value:null===ep.riskcountry?"":ep.riskcountry,onChange:ex,children:[(0,a.jsx)("option",{value:"",children:p("Events.forms.SelectCountryrisklevel")}),J.map((e,r)=>(0,a.jsx)("option",{value:e._id,children:e.title},r))]})]})}),(0,a.jsx)(c.A,{sm:6,md:6,lg:4,children:(0,a.jsxs)(u.A.Group,{children:[(0,a.jsx)(u.A.Label,{children:p("Events.forms.SelectRegionrisklevel")}),(0,a.jsxs)(x.s3,{name:"region",id:"region",value:null===ep.region?"":ep.region,onChange:ex,children:[(0,a.jsx)("option",{value:"",children:p("Events.forms.SelectRegionrisklevel")}),X.map((e,r)=>(0,a.jsx)("option",{value:e._id,children:e.title},r))]})]})}),(0,a.jsx)(c.A,{sm:6,md:6,lg:4,children:(0,a.jsxs)(u.A.Group,{children:[(0,a.jsx)(u.A.Label,{children:p("Events.forms.SelectInternationalrisklevel")}),(0,a.jsxs)(x.s3,{name:"international",id:"international",value:null===ep.international?"":ep.international,onChange:ex,children:[(0,a.jsx)("option",{value:"",children:p("Events.forms.SelectInternationalrisklevel")}),ee.map((e,r)=>(0,a.jsx)("option",{value:e._id,children:e.title},r))]})]})})]}),(0,a.jsx)(d.A,{className:"mb-3",children:(0,a.jsx)(c.A,{children:(0,a.jsx)(u.A.Group,{children:(0,a.jsx)(k.x,{initContent:ep.description,onChange:e=>ef(e)})})})}),(0,a.jsx)(d.A,{className:"mb-3",children:(0,a.jsxs)(c.A,{children:[(0,a.jsx)(o.A.Text,{children:(0,a.jsx)("b",{children:p("Events.forms.MoreInformation")})}),(0,a.jsx)("hr",{})]})}),(0,a.jsx)(d.A,{className:"mb-3",children:(0,a.jsx)(c.A,{children:(0,a.jsx)(u.A.Group,{children:(0,a.jsx)(k.x,{initContent:z.more_info,onChange:e=>eS(e)})})})}),(0,a.jsx)(d.A,{className:"mb-3",children:(0,a.jsxs)(c.A,{children:[(0,a.jsx)(o.A.Text,{children:(0,a.jsx)("b",{children:p("Events.forms.MediaGallery")})}),(0,a.jsx)("hr",{})]})}),(0,a.jsx)(d.A,{className:"mb-3",children:(0,a.jsx)(c.A,{children:(0,a.jsx)(b.A,{datas:E,srcText:R,getImgID:e=>eN(e),getImageSource:e=>e_(e)})})}),(0,a.jsx)(d.A,{className:"my-4",children:(0,a.jsxs)(c.A,{children:[(0,a.jsx)(m.A,{className:"me-2",type:"submit",variant:"primary",ref:t,children:p("submit")}),(0,a.jsx)(m.A,{className:"me-2",variant:"info",onClick:()=>{D(r),I([]),T([]),Q([]),Y([]),er([]),eh(em),window.scrollTo(0,0)},children:p("reset")}),(0,a.jsx)(h(),{href:"/event",as:"/event",children:(0,a.jsx)(m.A,{variant:"secondary",children:p("Cancel")})})]})})]})})})})}},81764:(e,r,t)=>{t.d(r,{A:()=>s});let a=t(14232).createContext(null);a.displayName="CardHeaderContext";let s=a},89673:(e,r,t)=>{t.d(r,{A:()=>A});var a=t(37876),s=t(14232),l=t(17336),n=t(21772),i=t(11041),o=t(37784),d=t(29504),c=t(60282),u=t(31195),m=t(82851),p=t.n(m),h=t(97685),x=t(53718),f=t(31753);let g=[],v={flex:1,display:"flex",flexDirection:"column",alignItems:"center",justifyContent:"center",width:"100%",height:"100%",borderWidth:.1,borderColor:"#fafafa",backgroundColor:"#fafafa",color:"black",transition:"border  .24s ease-in-out",padding:"15px"},y={display:"flex",padding:"10px",width:"100%",border:"2px solid gray",flexDirection:"column",justifyContent:"flex-start",flexWrap:"wrap",marginTop:20},j={width:"150px"},b={borderColor:"#2196f3"},A=e=>{let r,{t}=(0,f.Bd)("common"),[m,A]=(0,s.useState)(!1),[w,C]=(0,s.useState)(),k="application"==e.type?0x1400000:"20971520",[S,N]=(0,s.useState)([]),[_,E]=(0,s.useState)(!0),[I,R]=(0,s.useState)([]),T=e&&"application"===e.type?"/files":"/image",z=async e=>{await x.A.remove("".concat(T,"/").concat(e))},D=e=>{C(e),A(!0)},O=(e,r)=>{let t=[...I];t[r]=e.target.value,R(t)},P=r=>{switch(r&&r.name.split(".").pop()){case"JPG":case"jpg":case"jpeg":case"jpg":case"png":return(0,a.jsx)("img",{src:r.preview,style:j});case"pdf":return(0,a.jsx)("img",{src:"/images/fileIcons/pdfFile.png",className:"application"===e.type?"docPreview":"imgPreview"});case"docx":default:return(0,a.jsx)("img",{src:"/images/fileIcons/wordFile.png",className:"application"===e.type?"docPreview":"imgPreview"});case"xls":case"xlsx":return(0,a.jsx)("img",{src:"/images/fileIcons/xlsFile.png",className:"application"===e.type?"docPreview":"imgPreview"})}},L=()=>A(!1),F=()=>{A(!1)},M=r=>{let t=(r=w)&&r._id?{serverID:r._id}:{file:r},a=p().findIndex(g,t),s=[...I];s.splice(a,1),R(s),z(g[a].serverID),g.splice(a,1),e.getImgID(g,e.index?e.index:0);let l=[...S];l.splice(l.indexOf(r),1),N(l),A(!1)},G=S.map((r,s)=>(0,a.jsxs)("div",{children:[(0,a.jsx)(o.A,{xs:12,children:(0,a.jsxs)("div",{className:"row",children:[(0,a.jsx)(o.A,{md:4,lg:3,className:"application text-center align-self-center"===e.type?"docImagePreview text-center align-self-center":"imgPreview text-center align-self-center",children:P(r)}),(0,a.jsx)(o.A,{md:5,lg:7,className:"align-self-center",children:(0,a.jsxs)(d.A,{children:[(0,a.jsxs)(d.A.Group,{controlId:"filename",children:[(0,a.jsx)(d.A.Label,{className:"mt-2",children:t("FileName")}),(0,a.jsx)(d.A.Control,{size:"sm",type:"text",disabled:!0,value:r.original_name?r.original_name:r.name})]}),(0,a.jsxs)(d.A.Group,{controlId:"description",children:[(0,a.jsx)(d.A.Label,{children:"application"===e.type?t("ShortDescription/(Max255Characters)"):t("Source/Description")}),(0,a.jsx)(d.A.Control,{maxLength:"application"===e.type?255:void 0,size:"sm",type:"text",placeholder:"application"===e.type?t("`Enteryourdocumentdescription`"):t("`Enteryourimagesource/description`"),value:I[s],onChange:e=>O(e,s)})]})]})}),(0,a.jsx)(o.A,{md:3,lg:2,className:"align-self-center text-center",onClick:()=>D(r),children:(0,a.jsx)(c.A,{variant:"dark",children:t("Remove")})})]})}),(0,a.jsxs)(u.A,{show:m,onHide:L,children:[(0,a.jsx)(u.A.Header,{closeButton:!0,children:(0,a.jsx)(u.A.Title,{children:t("DeleteFile")})}),(0,a.jsx)(u.A.Body,{children:t("Areyousurewanttodeletethisfile?")}),(0,a.jsxs)(u.A.Footer,{children:[(0,a.jsx)(c.A,{variant:"secondary",onClick:F,children:t("Cancel")}),(0,a.jsx)(c.A,{variant:"primary",onClick:()=>M(r),children:t("yes")})]})]})]},s));(0,s.useEffect)(()=>{S.forEach(e=>URL.revokeObjectURL(e.preview)),g=[]},[]),(0,s.useEffect)(()=>{e.getImageSource(I)},[I]),(0,s.useEffect)(()=>{R(e.srcText)},[e.srcText]),(0,s.useEffect)(()=>{e&&"true"===e.singleUpload&&E(!1),e&&e.datas&&N([...e.datas.map((r,t)=>(g.push({serverID:r._id,index:e.index?e.index:0,type:r.name.split(".")[1]}),{...r,preview:"".concat("http://localhost:3001/api/v1","/image/show/").concat(r._id)}))])},[e.datas]);let U=async(r,t)=>{if(r.length>t)try{let a=new FormData;a.append("file",r[t]);let s=await x.A.post(T,a,{"Content-Type":"multipart/form-data"});g.push({serverID:s._id,file:r[t],index:e.index?e.index:0,type:r[t].name.split(".")[1]}),U(r,t+1)}catch(e){U(r,t+1)}else e.getImgID(g,e.index?e.index:0)},W=(0,s.useCallback)(async e=>{await U(e,0);let r=e.map(e=>Object.assign(e,{preview:URL.createObjectURL(e)}));_?N(e=>[...e,...r]):N([...r])},[]),{getRootProps:B,getInputProps:q,isDragActive:H,isDragAccept:V,isDragReject:$,fileRejections:K}=(0,l.VB)({accept:e&&e.type?"application/pdf, application/vnd.openxmlformats-officedocument.wordprocessingml.document, application/vnd.oasis.opendocument.text,application/vnd.openxmlformats-officedocument.spreadsheetml.sheet,text/csv":"image/*",multiple:_,minSize:0,maxSize:k,onDrop:W,validator:function(e){if("/image"===T){if("image"!==e.type.substring(0,5))return h.Ay.error(t("toast.filetypenotsupport")),{code:"file-invalid-type",message:"File type not supported"}}else if("/files"===T&&"image"===e.type.substring(0,5))return h.Ay.error(t("toast.filetypenotsupport")),{code:"file-invalid-type",message:"File type not supported"};return null}}),J=(0,s.useMemo)(()=>({...v,...H?b:{outline:"2px dashed #bbb"},...V?{outline:"2px dashed #595959"}:{outline:"2px dashed #bbb"},...$?{outline:"2px dashed red"}:{activeStyle:b}}),[H,$]);r=e&&"application"===e.type?(0,a.jsx)("small",{style:{color:"#595959"},children:t("DocumentWeSupport")}):(0,a.jsx)("small",{style:{color:"#595959"},children:t("ImageWeSupport")});let Q=K.length>0&&K[0].file.size>k;return(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)("div",{className:" d-flex justify-content-center align-items-center mt-3",style:{width:"100%",height:"180px"},children:(0,a.jsxs)("div",{...B({style:J}),children:[(0,a.jsx)("input",{...q()}),(0,a.jsx)(n.g,{icon:i.rOd,size:"4x",color:"#999"}),(0,a.jsx)("p",{style:{color:"#595959",marginBottom:"0px"},children:t("Drag'n'dropsomefileshere,orclicktoselectfiles")}),!_&&(0,a.jsxs)("small",{style:{color:"#595959"},children:[(0,a.jsx)("b",{children:"Note:"})," One single image will be accepted"]}),r,(e.type,Q&&(0,a.jsxs)("small",{className:"text-danger mt-2",children:[(0,a.jsx)(n.g,{icon:i.tUE,size:"1x",color:"red"})," ",t("FileistoolargeItshouldbelessthan20MB")]})),$&&(0,a.jsxs)("small",{className:"text-danger",style:{color:"#595959"},children:[(0,a.jsx)(n.g,{icon:i.tUE,size:"1x",color:"red"})," ",t("Filetypenotacceptedsorr")]})]})}),S.length>0&&(0,a.jsx)("div",{style:y,children:G})]})}}}]);
//# sourceMappingURL=658-89d27000dd74c309.js.map