"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[9810],{9810:(i,s,e)=>{e.r(s),e.d(s,{default:()=>j});var n=e(37876);e(14232);var t=e(60282),a=e(49589),r=e(56970),l=e(37784),o=e(48230),c=e.n(o),d=e(11041),u=e(21772),p=e(57116),m=e(31647),h=e(33859),x=e(31753);let j=i=>{let{t:s}=(0,x.Bd)("common"),e=()=>(0,n.jsx)(c(),{href:"/institution/[...routes]",as:"/institution/edit/".concat(i.prop.routes[1]),children:(0,n.jsxs)(t.A,{variant:"secondary",size:"sm",children:[(0,n.jsx)(u.g,{icon:d.hpd}),"\xa0",s("Edit")]})}),o=()=>{var s;return(0,n.jsx)("li",{children:(0,n.jsx)("span",{className:"image-container",children:(0,n.jsx)(c(),{href:"/institution/[...routes]",as:"/institution/focalpoint/".concat(null==i||null==(s=i.prop)?void 0:s.routes[1]),children:(0,n.jsx)("i",{className:"fas fa-plus"})})})})},j=(0,h.canEditInstitution)(()=>(0,n.jsx)(e,{})),f=(0,h.canManageFocalPoints)(()=>(0,n.jsx)(o,{}));return(0,n.jsx)(n.Fragment,{children:(0,n.jsx)(a.A,{fluid:!0,children:(0,n.jsxs)(r.A,{children:[function(i,s,e,t){return(0,n.jsx)(l.A,{xs:12,children:(0,n.jsxs)("div",{className:"d-flex justify-content-between",children:[(0,n.jsxs)("h4",{className:"institutionTitle",children:[i.title," \xa0\xa0",t&&s.routes&&s.routes[1]?(0,n.jsx)(e,{institution:i}):null]}),(0,n.jsx)(m.A,{entityId:s.routes[1],entityType:"institution"})]})})}(i.institutionData,i.prop,j,i.editAccess),(0,n.jsxs)(l.A,{xs:6,children:[(0,n.jsx)("div",{children:(0,n.jsx)(p.default,{description:i.institutionData.description})}),function(i,s){return(0,n.jsx)("div",{className:"institutionInfo",children:(0,n.jsxs)("ul",{children:[(0,n.jsxs)("li",{children:[(0,n.jsxs)("label",{children:[i("Telephone"),":"]}),(0,n.jsx)("span",{className:"field-value",children:"".concat("undefined"!==s.dial_code&&s.dial_code," ").concat(s.telephone)})]}),(0,n.jsxs)("li",{children:[(0,n.jsxs)("label",{children:[i("Address"),":"]}),(0,n.jsxs)("span",{className:"field-value",children:[s&&s.address?s.address.line_1:null," ",s&&s.address?s.address.line_2:null," ",s&&s.address?s.address.city:null]})]}),(0,n.jsxs)("li",{children:[(0,n.jsxs)("label",{children:[i("Website"),":"]}),(0,n.jsx)("span",{className:"field-value",children:s.website})]})]})})}(s,i.institutionData)]}),(0,n.jsx)(l.A,{xs:6,children:(0,n.jsxs)(r.A,{children:[(0,n.jsxs)(l.A,{md:4,className:"p-0",children:[i.focalPoints.length>=1?i.focalPoints.map((i,e)=>{if(i.isPrimary)return(0,n.jsx)("h6",{className:"other-focal-points-header",children:s("PrimaryFocalPoint")})}):"",(0,n.jsx)("ul",{className:"focalPoints primary",children:i.focalPoints.length>=1?i.focalPoints.map((i,s)=>{if(i.isPrimary)return(0,n.jsxs)("li",{className:i.isPrimary?"isPrimary":"",children:[(0,n.jsx)("span",{className:"image-container",children:i.image&&i.image._id?(0,n.jsx)("img",{src:"".concat("http://localhost:3001/api/v1","/image/show/").concat(i.image._id)}):(0,n.jsx)("img",{src:"/images/rkiProfile.jpg"})}),(0,n.jsx)("div",{className:"focalpointDetails",children:(0,n.jsxs)("p",{className:"fpDetailsFixed",children:[(0,n.jsx)("b",{children:i.username}),(0,n.jsx)("span",{children:i.email}),(0,n.jsx)("span",{children:i.focal_points_institution}),(0,n.jsx)("span",{children:i.mobile_number})]})})]},s)}):""})]}),function(i,s,e,t,a){var r,o;return(0,n.jsxs)(l.A,{md:8,children:[(0,n.jsx)("h6",{className:"other-focal-points-header",children:i("OtherFocalPoints")}),(0,n.jsxs)("ul",{className:"focalPoints",children:[s.length>=1?s.map((i,s)=>(0,n.jsxs)("li",{className:i.isPrimary?"isPrimary":"",children:[(0,n.jsx)("span",{className:"image-container",children:i.image&&i.image._id?(0,n.jsx)("img",{src:"".concat("http://localhost:3001/api/v1","/image/show/").concat(i.image._id)}):(0,n.jsx)("img",{src:"/images/rkiProfile.jpg"})}),(0,n.jsx)("div",{className:"focalpointDetails",children:(0,n.jsxs)("p",{className:"fpDetailsFixed",children:[(0,n.jsx)("b",{children:i.username}),(0,n.jsx)("span",{children:i.email}),(0,n.jsx)("span",{children:i.focal_points_institution}),(0,n.jsx)("span",{children:i.mobile_number})]})})]},s)):"",(null==e||null==(r=e.prop)?void 0:r.routes)&&(null==e||null==(o=e.prop)?void 0:o.routes[1])?(0,n.jsx)(t,{institution:a}):null]})]})}(s,i.focalPoints,i,f,i.institutionData)]})})]})})})}},31647:(i,s,e)=>{e.d(s,{A:()=>d});var n=e(37876),t=e(31777),a=e(11041),r=e(14232),l=e(21772),o=e(53718);let c={operation:"Operation",institution:"Institution",event:"Event",project:"Project",vspace:"Vspace"},d=(0,t.Ng)(i=>i)(i=>{let{user:s,entityId:e,entityType:t}=i,[d,u]=(0,r.useState)(!1),[p,m]=(0,r.useState)(""),h=async()=>{if(!(null==s?void 0:s._id))return;let i=await o.A.get("/flag",{query:{entity_id:e,user:s._id,onModel:c[t]}});i&&i.data&&i.data.length>0&&(m(i.data[0]),u(!0))},x=async i=>{if(i.preventDefault(),!(null==s?void 0:s._id))return;let n=!d,a={entity_type:t,entity_id:e,user:s._id,onModel:c[t]};if(n){let i=await o.A.post("/flag",a);i&&i._id&&(m(i),u(n))}else{let i=await o.A.remove("/flag/".concat(p._id));i&&i.n&&u(n)}};return(0,r.useEffect)(()=>{h()},[]),(0,n.jsx)("div",{className:"subscribe-flag",children:(0,n.jsxs)("a",{href:"",onClick:x,children:[(0,n.jsx)("span",{className:"check",children:d?(0,n.jsx)(l.g,{className:"clickable checkIcon",icon:a.SGM,color:"#00CC00"}):(0,n.jsx)(l.g,{className:"clickable minusIcon",icon:a.OQW,color:"#fff"})}),(0,n.jsx)(l.g,{className:"bookmark",icon:a.G06,color:"#d4d4d4"})]})})})},33859:(i,s,e)=>{e.r(s),e.d(s,{canAddInstitution:()=>r,canAddInstitutionForm:()=>l,canEditInstitution:()=>o,canEditInstitutionForm:()=>c,canManageFocalPoints:()=>u,canViewDiscussionUpdate:()=>d,default:()=>p});var n=e(37876);e(14232);var t=e(8178),a=e(59626);let r=(0,t.A)({authenticatedSelector:i=>!!i.permissions&&!!i.permissions.institution&&!!i.permissions.institution["create:any"],wrapperDisplayName:"CanAddInstitution"}),l=(0,t.A)({authenticatedSelector:i=>!!i.permissions&&!!i.permissions.institution&&!!i.permissions.institution["create:any"],wrapperDisplayName:"CanAddInstitutionForm",FailureComponent:()=>(0,n.jsx)(a.default,{})}),o=(0,t.A)({authenticatedSelector:(i,s)=>{if(i.permissions&&i.permissions.institution){if(i.permissions.institution["update:any"])return!0;else if(i.permissions.institution["update:own"]&&s.institution&&s.institution.user&&s.institution.user===i.user._id)return!0}return!1},wrapperDisplayName:"CanEditInstitution"}),c=(0,t.A)({authenticatedSelector:(i,s)=>{if(i.permissions&&i.permissions.institution){if(i.permissions.institution["update:any"])return!0;else if(i.permissions.institution["update:own"]&&s.institution&&s.institution.user&&s.institution.user===i.user._id)return!0}return!1},wrapperDisplayName:"CanEditInstitutionForm",FailureComponent:()=>(0,n.jsx)(a.default,{})}),d=(0,t.A)({authenticatedSelector:i=>!!i.permissions&&!!i.permissions.update&&!!i.permissions.update["read:any"],wrapperDisplayName:"CanViewDiscussionUpdate"}),u=(0,t.A)({authenticatedSelector:(i,s)=>{if(i.permissions&&i.permissions.institution_focal_point){if(i.permissions.institution_focal_point["update:any"])return!0;else if(i.permissions.institution_focal_point["update:own"]&&s.institution&&s.institution.user&&s.institution.user===i.user._id)return!0}return!1},wrapperDisplayName:"canManageFocalPoints"}),p=r},57116:(i,s,e)=>{e.r(s),e.d(s,{default:()=>o});var n=e(37876),t=e(14232),a=e(31195),r=e(60282),l=e(31753);let o=i=>{let{description:s}=i,[e,o]=(0,t.useState)(!1),{t:c}=(0,l.Bd)("common");return(0,n.jsxs)(n.Fragment,{children:[(0,n.jsxs)(a.A,{show:e,onHide:()=>o(!e),backdrop:"static",keyboard:!1,children:[(0,n.jsx)(a.A.Header,{closeButton:!0,children:(0,n.jsx)(a.A.Title,{children:c("Description")})}),(0,n.jsx)(a.A.Body,{children:(0,n.jsx)("div",{className:"_readmore_d",dangerouslySetInnerHTML:{__html:void 0==s?"":s}})})]}),s&&s.length<130?(0,n.jsx)("div",{dangerouslySetInnerHTML:{__html:void 0==s?"":s}}):""!=s?(0,n.jsxs)("div",{children:[(0,n.jsx)("div",{className:"_tabelw",dangerouslySetInnerHTML:{__html:void 0==s?"":s.substring(0,130)+"".concat(s.includes("<p")?"...":"").concat("")}}),(0,n.jsx)("div",{className:"pt-3",children:(0,n.jsx)(r.A,{onClick:()=>o(!e),className:"readMoreBtn mb-3",variant:"outline-light",children:c("ReadMore")})})]}):""]})}}}]);
//# sourceMappingURL=9810-4375e8f45567821a.js.map