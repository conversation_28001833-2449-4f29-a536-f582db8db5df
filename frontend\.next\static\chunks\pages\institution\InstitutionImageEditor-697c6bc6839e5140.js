(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[7219],{40928:(e,t,n)=>{"use strict";n.r(t),n.d(t,{default:()=>w});var i=n(37876),a=n(14232),l=n(93131),r=n.n(l),s=n(7940),o=n.n(s),d=n(31195),c=n(56970),u=n(37784),h=n(60282),m=n(97685),g=n(53718),p=n(31753);let w=e=>{let{isOpen:t,onModalClose:n,image:l,getId:s,fileName:w,getBlob:x}=e,[f,j]=(0,a.useState)(1),[y,_]=(0,a.useState)(""),[v,A]=(0,a.useState)(null),b=(0,a.useRef)(null),{t:C}=(0,p.Bd)("common");(0,a.useEffect)(()=>{_(w)},[w]);let N=async()=>{let e=(e=>{var t;let n=e.split(","),i=null==(t=n[0].match(/:(.*?);/))?void 0:t[1],a=atob(n[1]),l=a.length,r=new Uint8Array(l);for(;l--;)r[l]=a.charCodeAt(l);return new Blob([r],{type:i})})(b.current.getImage().toDataURL("image/jpeg",.6));x((window.URL||window.webkitURL).createObjectURL(e));let t=new FormData;t.append("file",e,y);try{let e=await g.A.post("/image",t,{"Content-Type":"multipart/form-data"});e&&e._id&&s(e._id)}catch(e){throw"Something wrong in server || your data!"}m.Ay.success(C("toast.CroppedtheimageSuccessfully")),n(!1),A(null),_("none"),j(1)};return(0,i.jsx)(i.Fragment,{children:(0,i.jsx)("div",{children:(0,i.jsxs)(d.A,{show:t,size:"lg","aria-labelledby":"ProfileEdit",onHide:()=>n(!1),centered:!0,children:[(0,i.jsxs)(d.A.Body,{children:[(0,i.jsxs)("div",{className:"d-flex flex-column justify-content-center align-items-center imgRotate",children:[(0,i.jsx)(r(),{ref:b,width:700,height:400,borderRadius:2,scale:f,color:[0,0,0,.6],image:v||l,style:{width:"100%",height:"auto"}}),(0,i.jsx)("div",{className:"info-identifier",children:(0,i.jsx)("span",{children:C("ThisareawillcontainyourInstitutionandfocalpointinformation")})})]}),(0,i.jsx)("div",{className:"mx-2 my-3",children:(0,i.jsxs)(c.A,{children:[(0,i.jsx)(u.A,{sm:1,md:1,lg:1,className:"pe-0",children:(0,i.jsx)("b",{children:C("Zoom")})}),(0,i.jsx)(u.A,{sm:11,md:11,lg:11,children:(0,i.jsx)(o(),{value:f,tooltip:"auto",min:1,max:10,step:.01,variant:"primary",onChange:e=>j(Number(e.target.value))})})]})})]}),(0,i.jsxs)(d.A.Footer,{children:[(0,i.jsx)(h.A,{onClick:N,children:C("Crop")}),(0,i.jsx)(h.A,{variant:"danger",onClick:()=>n(!1),children:C("Cancel")})]})]})})})}},41738:(e,t,n)=>{(window.__NEXT_P=window.__NEXT_P||[]).push(["/institution/InstitutionImageEditor",function(){return n(40928)}])}},e=>{var t=t=>e(e.s=t);e.O(0,[5266,636,6593,8792],()=>t(41738)),_N_E=e.O()}]);
//# sourceMappingURL=InstitutionImageEditor-697c6bc6839e5140.js.map