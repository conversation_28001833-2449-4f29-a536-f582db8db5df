{"version": 3, "file": "static/chunks/pages/adminsettings/syndrome/syndromeTable-72fbea1d937da18c.js", "mappings": "gFACA,4CACA,wCACA,WACA,OAAe,EAAQ,KAA6D,CACpF,EACA,SAFsB,oGCiCtB,SAASA,EAASC,CAAoB,EACpC,GAAM,GAAEC,CAAC,CAAE,CAAGC,CAAAA,EAAAA,EAAAA,EAAAA,CAAcA,CAAC,UACvBC,EAA6B,CACjCC,gBAAiBH,EAAE,cACnB,EACI,SACJI,CAAO,MACPC,CAAI,WACJC,CAAS,uBACTC,CAAqB,WACrBC,CAAS,oBACTC,CAAkB,qBAClBC,CAAmB,kBACnBC,CAAgB,aAChBC,CAAW,oBACXC,CAAkB,gBAClBC,CAAc,CACdC,SAAO,CACPC,WAAS,sBACTC,CAAoB,mBACpBC,CAAiB,YACjBC,CAAU,QACVC,CAAM,kBACNC,CAAgB,cAChBC,CAAY,CAEZ,CADA,EACGC,EACJ,CAAGxB,EAGEyB,EAAiB,4BACrBtB,EACAuB,gBAAiBzB,EAAE,IAP0C,MAQ7D0B,SAAU,GACVtB,UACAC,KAAMA,GAAQ,EAAE,CAChBsB,OAAO,EACPC,2BAA4BrB,EAC5BsB,UAAWrB,EACXsB,gBAAiBf,qBACjBN,EACAsB,YAAY,EACZC,iBAAkBhB,EAClBiB,kBAAmBpB,GAA0C,GAC7DqB,eADwCrB,cACVD,GAA4B,CAAC,GAAI,GAAI,GAAI,CAA3BA,EAA+B,GAAG,CAC9EuB,oBAAqB7B,EACrB8B,oBAAqB1B,EACrB2B,aAAc1B,iBACdG,EACAG,yCACAC,EACAoB,kBAAmB,UAACC,EAAAA,CAAYA,CAAAA,CAAAA,GAChCC,SAAU,UAACC,IAAAA,CAAEC,UAAU,kCACvBvB,oBACAC,eACAE,mBACAD,EACAqB,UAAW,WACb,EACA,MACE,UAACC,EAAAA,EAASA,CAAAA,CAAE,GAAGnB,CAAc,EAEjC,CAEA1B,EAAS8C,YAAY,CAAG,CACtBf,WAAW,EACXE,YAAY,EACZzB,UAAW,KACXU,WAAW,EACXC,qBAAsB,KACtBC,mBAAmB,EACnBC,YAAY,EACZE,kBAAkB,CACpB,EAEA,MAAevB,QAAQA,EAAC,oKC4BxB,MAjIuB+C,IACnB,GAAM,GAAE7C,CAAC,CAAE,CAAGC,CAAAA,EAAAA,EAAAA,EAAAA,CAAcA,CAAC,MAgIlB6C,IA/HL,CAACC,EAAWC,EAAe,CAAGC,CAAAA,EAAAA,EA+HX,QA/HWA,CAAQA,CAAC,EAAE,EACzC,EAAGC,EAAW,CAAGD,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAC,IAC1B,CAAC3C,EAAW6C,EAAa,CAAGF,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAC,GACrC,CAACG,EAASC,EAAW,CAAGJ,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAC,IACjC,CAACK,EAAaC,EAAS,CAAGN,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,EAAC,GACnC,CAACO,EAAgBC,EAAkB,CAAGR,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAC,CAAC,GAEhD7C,EAAU,CACZ,CACIsD,KAAM1D,EAAE,mCACR2D,SAAU,OACd,EACA,CACID,KAAM1D,EAAE,8BACR2D,SAAU,OACVC,KAAM,GAAYC,EAAEC,IAAI,EAE5B,CACIJ,KAAM1D,EAAE,qCACR2D,SAAU,cACVC,KAAM,GAAYC,EAAEE,WAAW,CAACC,OAAO,CAAC,WAAY,GACxD,EACA,CACIN,KAAM1D,EAAE,gCACR2D,SAAU,GACVC,KAAOC,GACH,WAACI,MAAAA,WACG,UAACC,IAAIA,CAACC,KAAK,6BAA6BC,GAAI,OAAvCF,yBAA6E,OAANL,EAAEQ,GAAG,WAE7E,UAAC5B,IAAAA,CAAEC,UAAU,uBAEV,OAEP,UAAC4B,IAAAA,CAAEC,QAAS,IAAMC,EAAWX,YACzB,UAACpB,IAAAA,CAAEC,UAAU,4BACZ,MAGjB,EACH,CAED+B,CAAAA,EAAAA,EAAAA,SAAAA,CAASA,CAAC,KACNC,GACJ,EAAG,EAAE,EAEL,IAAMC,EAAiB,CACnBC,KAAM,CAAEC,MAAO,KAAM,EACrBC,MAAO1B,EACP2B,KAAM,EACNC,MAAO,CAAC,CACZ,EAEMN,EAAkB,UACpBxB,GAAW,GACX,IAAM+B,EAAW,MAAMC,EAAAA,CAAUA,CAACC,GAAG,CAAC,YAAaR,GAC/CM,GAAYA,EAAS5E,IAAI,EAAI4E,EAAS5E,IAAI,CAAC+E,MAAM,CAAG,GAAG,CACvDpC,EAAeiC,EAAS5E,IAAI,EAC5B8C,EAAa8B,EAASI,UAAU,EAChCnC,GAAW,GAEnB,EAQMxC,EAAsB,MAAO4E,EAAiBP,KAChDJ,EAAeG,KAAK,CAAGQ,EACvBX,EAAeI,IAAI,CAAGA,EACtB7B,GAAW,GACX,IAAM+B,EAAW,MAAMC,EAAAA,CAAUA,CAACC,GAAG,CAAC,YAAaR,GAC/CM,GAAYA,EAAS5E,IAAI,EAAI4E,EAAS5E,IAAI,CAAC+E,MAAM,CAAG,GAAG,CACvDpC,EAAeiC,EAAS5E,IAAI,EAC5BgD,EAAWiC,GACXpC,GAAW,GAEnB,EAEMsB,EAAa,MAAOe,IACtB9B,EAAkB8B,EAAIlB,GAAG,EACzBd,GAAS,EACb,EAEMiC,EAAe,UACjB,GAAI,CACA,MAAMN,EAAAA,CAAUA,CAACO,MAAM,CAAC,aAA4B,OAAfjC,IACrCkB,IACAnB,GAAS,GACTmC,EAAAA,EAAKA,CAACC,OAAO,CAAC3F,EAAE,2DACpB,CAAE,MAAO4F,EAAO,CACZF,EAAAA,EAAKA,CAACE,KAAK,CAAC5F,EAAE,qDAClB,CACJ,EAEM6F,EAAY,IAAMtC,GAAS,GAEjC,MACI,WAACU,MAAAA,WACG,WAAC6B,EAAAA,CAAKA,CAAAA,CAACC,KAAMzC,EAAa0C,OAAQH,YAC9B,UAACC,EAAAA,CAAKA,CAACG,MAAM,EAACC,WAAW,aACrB,UAACJ,EAAAA,CAAKA,CAACK,KAAK,WAAEnG,EAAE,4CAEpB,UAAC8F,EAAAA,CAAKA,CAACM,IAAI,WAAEpG,EAAE,+DACf,WAAC8F,EAAAA,CAAKA,CAACO,MAAM,YACT,UAACC,EAAAA,CAAMA,CAAAA,CAACC,QAAQ,YAAYhC,QAASsB,WAChC7F,EAAE,kCAEP,UAACsG,EAAAA,CAAMA,CAAAA,CAACC,QAAQ,UAAUhC,QAASiB,WAC9BxF,EAAE,qCAKf,UAACF,EAAAA,CAAQA,CAAAA,CACLM,QAASA,EACTC,KAAM0C,EACNzC,UAAWA,EACXU,WAAW,EACXN,oBAAqBA,EACrBC,iBA3Da,CA2DKA,GA1D1BgE,EAAeG,KAAK,CAAG1B,EACvBuB,EAAeI,IAAI,CAAGA,EACtBL,GACJ,MA2DJ", "sources": ["webpack://_N_E/?4fb2", "webpack://_N_E/./components/common/RKITable.tsx", "webpack://_N_E/./pages/adminsettings/syndrome/syndromeTable.tsx"], "sourcesContent": ["\n    (window.__NEXT_P = window.__NEXT_P || []).push([\n      \"/adminsettings/syndrome/syndromeTable\",\n      function () {\n        return require(\"private-next-pages/adminsettings/syndrome/syndromeTable.tsx\");\n      }\n    ]);\n    if(module.hot) {\n      module.hot.dispose(function () {\n        window.__NEXT_P.push([\"/adminsettings/syndrome/syndromeTable\"])\n      });\n    }\n  ", "//Import Library\r\nimport React from 'react'\r\nimport DataTable  from 'react-data-table-component';\r\n\r\n//Import services/components\r\nimport { useTranslation } from 'next-i18next';\r\nimport CustomLoader from './CustomLoader';\r\n\r\ninterface RKITableProps {\r\n  columns: any[];\r\n  data: any[];\r\n  totalRows?: number;\r\n  resetPaginationToggle?: boolean;\r\n  subheader?: boolean;\r\n  subHeaderComponent?: React.ReactNode;\r\n  handlePerRowsChange?: (newPerPage: number, page: number) => void;\r\n  handlePageChange?: (page: number) => void;\r\n  rowsPerPage?: number[];\r\n  defaultRowsPerPage?: number;\r\n  selectableRows?: boolean;\r\n  loading?: boolean;\r\n  pagServer?: boolean;\r\n  onSelectedRowsChange?: (selectedRows: any) => void;\r\n  clearSelectedRows?: boolean;\r\n  sortServer?: boolean;\r\n  onSort?: (column: any, sortDirection: string) => void;\r\n  persistTableHead?: boolean;\r\n  sortFunction?: (rows: any[], field: any, direction: 'asc' | 'desc') => any[];\r\n  noHeader?: boolean;\r\n  dense?: boolean;\r\n  pagination?: boolean;\r\n  paginationServer?: boolean;\r\n  paginationTotalRows?: number;\r\n  subHeaderAlign?: string;\r\n  [key: string]: any;\r\n}\r\n\r\nfunction RKITable(props: RKITableProps) {\r\n  const { t } = useTranslation('common');\r\n  const paginationComponentOptions = {\r\n    rowsPerPageText: t('Rowsperpage'),\r\n    };\r\n  const {\r\n    columns,\r\n    data,\r\n    totalRows,\r\n    resetPaginationToggle,\r\n    subheader,\r\n    subHeaderComponent,\r\n    handlePerRowsChange,\r\n    handlePageChange,\r\n    rowsPerPage,\r\n    defaultRowsPerPage,\r\n    selectableRows,\r\n    loading,\r\n    pagServer,\r\n    onSelectedRowsChange,\r\n    clearSelectedRows,\r\n    sortServer,\r\n    onSort,\r\n    persistTableHead,\r\n    sortFunction,\r\n    // Filter out any unknown props that might cause DOM warnings\r\n    ...otherProps\r\n  } = props;\r\n\r\n  // Only pass known DataTable props to avoid DOM warnings\r\n  const dataTableProps = {\r\n    paginationComponentOptions,\r\n    noDataComponent: t(\"NoData\"),\r\n    noHeader: true,\r\n    columns,\r\n    data: data || [],\r\n    dense: true,\r\n    paginationResetDefaultPage: resetPaginationToggle,\r\n    subHeader: subheader,\r\n    progressPending: loading,\r\n    subHeaderComponent,\r\n    pagination: true,\r\n    paginationServer: pagServer,\r\n    paginationPerPage: defaultRowsPerPage ? defaultRowsPerPage : 10,\r\n    paginationRowsPerPageOptions: rowsPerPage ? rowsPerPage : [10, 15, 20, 25, 30],\r\n    paginationTotalRows: totalRows,\r\n    onChangeRowsPerPage: handlePerRowsChange,\r\n    onChangePage: handlePageChange,\r\n    selectableRows,\r\n    onSelectedRowsChange,\r\n    clearSelectedRows,\r\n    progressComponent: <CustomLoader/>,\r\n    sortIcon: <i className=\"sort-icon fas fa-exchange-alt\" />,\r\n    sortServer,\r\n    onSort,\r\n    sortFunction,\r\n    persistTableHead,\r\n    className: \"rki-table\"\r\n  };\r\n  return (\r\n    <DataTable {...dataTableProps} />\r\n  )\r\n};\r\n\r\nRKITable.defaultProps = {\r\n  subHeader: false,\r\n  pagination: true,\r\n  totalRows: null,\r\n  pagServer: true,\r\n  onSelectedRowsChange: null,\r\n  clearSelectedRows: false,\r\n  sortServer: false,\r\n  persistTableHead: false\r\n}\r\n\r\nexport default RKITable;\r\n\r\n", "//Import Library\r\nimport Link from \"next/link\";\r\nimport { useState, useEffect } from \"react\";\r\nimport { <PERSON><PERSON>, Button } from \"react-bootstrap\";\r\n\r\nimport toast from 'react-hot-toast';\r\n\r\n//Import services/components\r\nimport RKITable from \"../../../components/common/RKITable\";\r\nimport apiService from \"../../../services/apiService\";\r\nimport { useTranslation } from 'next-i18next';\r\nconst SyndromeTable = (_props: any) => {\r\n    const { t } = useTranslation('common');\r\n    const [tabledata, setDataToTable] = useState([]);\r\n    const [, setLoading] = useState(false);\r\n    const [totalRows, setTotalRows] = useState(0);\r\n    const [perPage, setPerPage] = useState(10);\r\n    const [isModalShow, setModal] = useState(false);\r\n    const [selectSyndrome, setSelectSyndrome] = useState({});\r\n    \r\n    const columns = [\r\n        {\r\n            name: t(\"adminsetting.syndrome.Syndromes\"),\r\n            selector: \"title\",\r\n        },\r\n        {\r\n            name: t(\"adminsetting.syndrome.Code\"),\r\n            selector: \"code\",\r\n            cell: (d: any) => d.code,\r\n        },\r\n        {\r\n            name: t(\"adminsetting.syndrome.Description\"),\r\n            selector: \"description\",\r\n            cell: (d: any) => d.description.replace(/<[^>]+>/g, \"\"),\r\n        },\r\n        {\r\n            name: t(\"adminsetting.syndrome.Action\"),\r\n            selector: \"\",\r\n            cell: (d: any) => (\r\n                <div>\r\n                    <Link href=\"/adminsettings/[...routes]\" as={`/adminsettings/edit_syndrome/${d._id}`}>\r\n\r\n                        <i className=\"icon fas fa-edit\" />\r\n\r\n                    </Link>\r\n                    &nbsp;\r\n                    <a onClick={() => userAction(d)}>\r\n                        <i className=\"icon fas fa-trash-alt\" />\r\n                    </a>{\" \"}\r\n                </div>\r\n            ),\r\n        },\r\n    ];\r\n\r\n    useEffect(() => {\r\n        getSyndromeData();\r\n    }, []);\r\n\r\n    const syndromeParams = {\r\n        sort: { title: \"asc\" },\r\n        limit: perPage,\r\n        page: 1,\r\n        query: {},\r\n    };\r\n\r\n    const getSyndromeData = async () => {\r\n        setLoading(true);\r\n        const response = await apiService.get(\"/syndrome\", syndromeParams);\r\n        if (response && response.data && response.data.length > 0) {\r\n            setDataToTable(response.data);\r\n            setTotalRows(response.totalCount);\r\n            setLoading(false);\r\n        }\r\n    };\r\n\r\n    const handlePageChange = (page: any) => {\r\n        syndromeParams.limit = perPage;\r\n        syndromeParams.page = page;\r\n        getSyndromeData();\r\n    };\r\n\r\n    const handlePerRowsChange = async (newPerPage: any, page: any) => {\r\n        syndromeParams.limit = newPerPage;\r\n        syndromeParams.page = page;\r\n        setLoading(true);\r\n        const response = await apiService.get(\"/syndrome\", syndromeParams);\r\n        if (response && response.data && response.data.length > 0) {\r\n            setDataToTable(response.data);\r\n            setPerPage(newPerPage);\r\n            setLoading(false);\r\n        }\r\n    };\r\n\r\n    const userAction = async (row: any) => {\r\n        setSelectSyndrome(row._id);\r\n        setModal(true);\r\n    };\r\n\r\n    const modalConfirm = async () => {\r\n        try {\r\n            await apiService.remove(`/syndrome/${selectSyndrome}`);\r\n            getSyndromeData();\r\n            setModal(false);\r\n            toast.success(t(\"adminsetting.syndrome.Table.syndromeDeletedSuccessfully\"));\r\n        } catch (error) {\r\n            toast.error(t(\"adminsetting.syndrome.Table.errorDeletingSyndrome\"));\r\n        }\r\n    };\r\n\r\n    const modalHide = () => setModal(false);\r\n\r\n    return (\r\n        <div>\r\n            <Modal show={isModalShow} onHide={modalHide}>\r\n                <Modal.Header closeButton>\r\n                    <Modal.Title>{t(\"adminsetting.syndrome.Deletesyndrome\")}</Modal.Title>\r\n                </Modal.Header>\r\n                <Modal.Body>{t(\"adminsetting.syndrome.Areyousurewanttodeletethissyndrome?\")}</Modal.Body>\r\n                <Modal.Footer>\r\n                    <Button variant=\"secondary\" onClick={modalHide}>\r\n                        {t(\"adminsetting.syndrome.Cancel\")}\r\n                    </Button>\r\n                    <Button variant=\"primary\" onClick={modalConfirm}>\r\n                        {t(\"adminsetting.syndrome.Yes\")}\r\n                    </Button>\r\n                </Modal.Footer>\r\n            </Modal>\r\n\r\n            <RKITable\r\n                columns={columns}\r\n                data={tabledata}\r\n                totalRows={totalRows}\r\n                pagServer={true}\r\n                handlePerRowsChange={handlePerRowsChange}\r\n                handlePageChange={handlePageChange}\r\n            />\r\n        </div>\r\n    );\r\n};\r\n\r\nexport default SyndromeTable;\r\n"], "names": ["RKITable", "props", "t", "useTranslation", "paginationComponentOptions", "rowsPerPageText", "columns", "data", "totalRows", "resetPaginationToggle", "subheader", "subHeaderComponent", "handlePerRowsChange", "handlePageChange", "rowsPerPage", "defaultRowsPerPage", "selectableRows", "loading", "pagServer", "onSelectedRowsChange", "clearSelectedRows", "sortServer", "onSort", "persistTableHead", "sortFunction", "otherProps", "dataTableProps", "noDataComponent", "<PERSON><PERSON><PERSON><PERSON>", "dense", "paginationResetDefaultPage", "subHeader", "progressPending", "pagination", "paginationServer", "paginationPerPage", "paginationRowsPerPageOptions", "paginationTotalRows", "onChangeRowsPerPage", "onChangePage", "progressComponent", "CustomLoader", "sortIcon", "i", "className", "DataTable", "defaultProps", "_props", "SyndromeTable", "tabledata", "setDataToTable", "useState", "setLoading", "setTotalRows", "perPage", "setPerPage", "isModalShow", "setModal", "selectSyndrome", "setSelectSyndrome", "name", "selector", "cell", "d", "code", "description", "replace", "div", "Link", "href", "as", "_id", "a", "onClick", "userAction", "useEffect", "getSyndromeData", "syndromeParams", "sort", "title", "limit", "page", "query", "response", "apiService", "get", "length", "totalCount", "newPerPage", "row", "modalConfirm", "remove", "toast", "success", "error", "modalHide", "Modal", "show", "onHide", "Header", "closeButton", "Title", "Body", "Footer", "<PERSON><PERSON>", "variant"], "sourceRoot": "", "ignoreList": []}