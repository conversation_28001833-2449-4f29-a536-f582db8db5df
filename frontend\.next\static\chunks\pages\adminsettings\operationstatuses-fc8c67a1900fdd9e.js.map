{"version": 3, "file": "static/chunks/pages/adminsettings/operationstatuses-fc8c67a1900fdd9e.js", "mappings": "4QAuDA,MA1C8BA,QAmCtBC,EAAAA,EAlCN,GAAM,GAAEC,CAAC,CAAE,CAAGC,CAAAA,EAAAA,EAAAA,EAAAA,CAAcA,CAAC,UACvBC,EAA2B,IAE7B,EAsC6BC,CAtC7B,CAsC8B,CAtC9B,KAACC,MAAAA,UACC,WAACC,EAAAA,CAASA,CAAAA,CAACC,MAAO,CAAEC,UAAW,QAAS,EAAGC,KAAK,IAACC,UAAU,gBACzD,UAACC,EAAAA,CAAGA,CAAAA,UACF,UAACC,EAAAA,CAAGA,CAAAA,CAACC,GAAI,YACP,UAACC,EAAAA,CAAWA,CAAAA,CAACC,MAAQd,EAAE,sDAG3B,UAACU,EAAAA,CAAGA,CAAAA,UACF,UAACC,EAAAA,CAAGA,CAAAA,CAACC,GAAI,YACP,UAACG,IAAIA,CACHC,KAAK,6BACLC,GAAG,OAFAF,0CAIH,UAACG,EAAAA,CAAMA,CAAAA,CAACC,QAAQ,YAAYC,KAAK,cAChCpB,EAAE,2DAKT,UAACU,EAAAA,CAAGA,CAAAA,CAACD,UAAU,gBACb,UAACE,EAAAA,CAAGA,CAAAA,CAACC,GAAI,YACP,UAACS,EAAAA,OAAoBA,CAAAA,CAAAA,YAQ3BC,EAAyBC,CAAAA,EAAAA,EAAAA,qBAAAA,CAAqBA,CAAC,IAAM,UAACrB,EAAAA,CAAAA,IACtDH,EAAYyB,CAAAA,EAAAA,EAAAA,EAAAA,CAAWA,CAAC,GAAWzB,SACzC,IAAI,GAAEA,GAAAA,OAAAA,EAAAA,EAAO0B,IAAP1B,OAAO0B,GAAP1B,MAAAA,GAAAA,EAAAA,gBAAoB2B,EAApB3B,KAAAA,EAAAA,CAAsC,CAAC,GAAvCA,UAAoD,EAIxD,UAACuB,EAAAA,CAAAA,GAHM,UAACK,EAAAA,OAAeA,CAAAA,CAAAA,EAK3B,6mBCnDA,IAAMC,EAAS,aACFC,EAAmBC,CAAAA,EAAAA,EAAAA,CAAAA,CAAoBA,CAAC,CACnDC,sBAAuB,KACjBhC,EAAM0B,WAAW,IAAI1B,EAAM0B,WAAW,CAACO,YAAY,IAAIjC,EAAM0B,WAAW,CAACO,YAAY,CAACJ,EAAO,CAKnGK,CALqG,kBAKjF,kBACtB,GAAG,EAE0BH,CAAAA,EAAAA,EAAAA,CAAAA,CAAoBA,CAAC,CAChDC,sBAAuB,KACjBhC,EAAM0B,WAAW,IAAI1B,EAAM0B,WAAW,CAACS,OAAO,IAAInC,EAAM0B,WAAW,CAACS,OAAO,CAACN,EAAO,CAKzFK,CAL2F,kBAKvE,eACtB,GAEaE,EAAyBL,CAAAA,EAAAA,EAAAA,CAAAA,CAAoBA,CAAC,CACzDC,sBAAuB,KACjBhC,EAAM0B,WAAW,IAAI1B,EAAM0B,WAAW,CAACW,iBAAiB,IAAIrC,EAAM0B,WAAW,CAACW,iBAAiB,CAACR,EAAO,CAK7GK,CAL+G,kBAK3F,wBACtB,GAAG,EAE8BH,CAAAA,EAAAA,EAAAA,CAAAA,CAAoBA,CAAC,CACpDC,sBAAuB,KACjBhC,EAAM0B,WAAW,IAAI1B,EAAM0B,WAAW,CAACY,YAAY,IAAItC,EAAM0B,WAAW,CAACY,YAAY,CAACT,EAAO,CAKnGK,CALqG,kBAKjF,mBACtB,GAAG,EAE4BH,CAAAA,EAAAA,EAAAA,CAAAA,CAAoBA,CAAC,CAClDC,sBAAuB,KACjBhC,EAAM0B,WAAW,IAAI1B,EAAM0B,WAAW,CAACa,SAAS,IAAIvC,EAAM0B,WAAW,CAACa,SAAS,CAACV,EAAO,CAK7FK,CAL+F,kBAK3E,iBACtB,GAAG,EAEqCH,CAAAA,EAAAA,EAAAA,CAAAA,CAAoBA,CAAC,CAC3DC,sBAAuB,KACjBhC,EAAM0B,WAAW,IAAI1B,EAAM0B,WAAW,CAACc,uBAAuB,IAAIxC,EAAM0B,WAAW,CAACc,uBAAuB,CAACX,EAAO,CAKzHK,CAL2H,kBAKvG,0BACtB,GAEaO,EAAuBV,CAAAA,EAAAA,EAAAA,CAAAA,CAAoBA,CAAC,CACvDC,sBAAuB,GACjBhC,IAAM0B,WAAW,IAAI1B,EAAM0B,WAAW,CAACc,uBAAuB,IAAIxC,EAAM0B,WAAW,CAACc,uBAAuB,CAACX,EAAO,CAKzHK,CAL2H,kBAKvG,sBACtB,GAAG,EAC0BH,CAAAA,EAAAA,EAAAA,CAAAA,CAAoBA,CAAC,CAChDC,sBAAuB,GACjBhC,IAAM0B,WAAW,IAAI1B,EAAM0B,WAAW,CAACgB,MAAM,IAAI1C,EAAM0B,WAAW,CAACgB,MAAM,CAACb,EAAO,CAKvFK,CALyF,kBAKrE,eACtB,GAAG,EAE8BH,CAAAA,EAAAA,EAAAA,CAAAA,CAAoBA,CAAC,CACpDC,sBAAuB,GACjBhC,IAAM0B,WAAW,IAAI1B,EAAM0B,WAAW,CAACiB,WAAW,IAAI3C,EAAM0B,WAAW,CAACiB,WAAW,CAACd,EAAO,CAKjGK,CALmG,kBAK/E,mBACtB,GAAG,EAEuCH,CAAAA,EAAAA,EAAAA,CAAAA,CAAoBA,CAAC,CAC7DC,sBAAuB,KACjBhC,EAAM0B,WAAW,IAAI1B,EAAM0B,WAAW,CAACkB,WAAW,IAAI5C,EAAM0B,WAAW,CAACkB,WAAW,CAACf,EAAO,CAKjGK,CALmG,kBAK/E,4BACtB,GAAG,EAEuCH,CAAAA,EAAAA,EAAAA,CAAAA,CAAoBA,CAAC,CAC7DC,sBAAuB,KACjBhC,EAAM0B,WAAW,IAAI1B,EAAM0B,WAAW,CAACmB,mBAAmB,IAAI7C,EAAM0B,WAAW,CAACmB,mBAAmB,CAAChB,EAAO,CAKjHK,CALmH,kBAK/F,4BACtB,GAEaY,EAA0Bf,CAAAA,EAAAA,EAAAA,CAAAA,CAAoBA,CAAC,CAC1DC,sBAAuB,KACjBhC,EAAM0B,WAAW,IAAI1B,EAAM0B,WAAW,CAACqB,gBAAgB,IAAI/C,EAAM0B,WAAW,CAACqB,gBAAgB,CAAClB,EAAO,CAK3GK,CAL6G,kBAKzF,yBACtB,GAAG,EAEkCH,CAAAA,EAAAA,EAAAA,CAAAA,CAAoBA,CAAC,CACxDC,sBAAuB,KACjBhC,EAAM0B,WAAW,IAAI1B,EAAM0B,WAAW,CAACC,gBAAgB,IAAI3B,EAAM0B,WAAW,CAACC,gBAAgB,CAACE,EAAO,CAK3GK,CAL6G,kBAKzF,uBACtB,GAAG,EAEgCH,CAAAA,EAAAA,EAAAA,CAAAA,CAAoBA,CAAC,CACtDC,sBAAuB,KACjBhC,EAAM0B,WAAW,IAAI1B,EAAM0B,WAAW,CAACsB,cAAc,IAAIhD,EAAM0B,WAAW,CAACsB,cAAc,CAACnB,EAAO,CAKvGK,CALyG,kBAKrF,qBACtB,GAAG,EAE0BH,CAAAA,EAAAA,EAAAA,CAAAA,CAAoBA,CAAC,CAChDC,sBAAuB,KACjBhC,EAAM0B,WAAW,IAAI1B,EAAM0B,WAAW,CAACuB,MAAM,IAAIjD,EAAM0B,WAAW,CAACuB,MAAM,CAACpB,EAAO,CAKvFK,CALyF,kBAKrE,eACtB,GAAG,EAE6BH,CAAAA,EAAAA,EAAAA,CAAAA,CAAoBA,CAAC,CACnDC,sBAAuB,KACjBhC,EAAM0B,WAAW,IAAI1B,EAAM0B,WAAW,CAACwB,UAAU,IAAIlD,EAAM0B,WAAW,CAACwB,UAAU,CAACrB,EAAO,CAK/FK,CALiG,kBAK7E,kBACtB,GAAG,EAE4BH,CAAAA,EAAAA,EAAAA,CAAAA,CAAoBA,CAAC,CAClDC,sBAAuB,KACjBhC,EAAM0B,WAAW,IAAI1B,EAAM0B,WAAW,CAACyB,QAAQ,IAAInD,EAAM0B,WAAW,CAACyB,QAAQ,CAACtB,EAAO,CAK3FK,CAL6F,kBAKzE,iBACtB,GAAG,EAE8BH,CAAAA,EAAAA,EAAAA,CAAAA,CAAoBA,CAAC,CACpDC,sBAAuB,KACjBhC,EAAM0B,WAAW,IAAI1B,EAAM0B,WAAW,CAAC0B,WAAW,IAAIpD,EAAM0B,WAAW,CAAC0B,WAAW,CAACvB,EAAO,CAKjGK,CALmG,kBAK/E,mBACtB,GAAG,EAEwBH,CAAAA,EAAAA,EAAAA,CAAAA,CAAoBA,CAAC,CAC9CC,sBAAuB,KACjBhC,EAAM0B,WAAW,IAAI1B,EAAM0B,WAAW,CAAC2B,KAAK,IAAIrD,EAAM0B,WAAW,CAAC2B,KAAK,CAACxB,EAAO,CAKrFK,CALuF,kBAKnE,aACtB,GAAG,EAE8BH,CAAAA,EAAAA,EAAAA,CAAAA,CAAoBA,CAAC,CACpDC,sBAAuB,KACjBhC,EAAM0B,WAAW,IAAI1B,EAAM0B,WAAW,CAAC4B,WAAW,IAAItD,EAAM0B,WAAW,CAAC4B,WAAW,CAACzB,EAAO,CAKjGK,CALmG,kBAK/E,mBACtB,GAAG,EAE8BH,CAAAA,EAAAA,EAAAA,CAAAA,CAAoBA,CAAC,CACpDC,sBAAuB,KACjBhC,EAAM0B,WAAW,IAAI1B,EAAM0B,WAAW,CAAC6B,YAAY,IAAIvD,EAAM0B,WAAW,CAAC6B,YAAY,CAAC1B,EAAO,CAKnGK,CALqG,kBAKjF,mBACtB,GAAG,EAE0BH,CAAAA,EAAAA,EAAAA,CAAAA,CAAoBA,CAAC,CAChDC,sBAAuB,GACrB,EAAIhC,EAAM0B,WAAW,IAAI1B,EAAM0B,WAAW,CAAC8B,SAAS,IAAIxD,EAAM0B,WAAW,CAAC8B,SAAS,CAAC3B,EAAO,IAAI7B,EAAM0B,WAAW,CAAC+B,OAAO,IAAIzD,EAAM0B,WAAW,CAAC+B,OAAO,CAAC5B,EAAO,IAAG7B,EAAM0B,WAAW,CAACgC,KAAK,IAAI1D,EAAM0B,WAAW,CAACgC,KAAK,CAAC7B,EAAO,IAAG7B,EAAM0B,WAAW,CAACiC,MAAM,IAAI3D,EAAM0B,WAAW,CAACiC,MAAM,CAAC9B,EAAO,IAAG7B,EAAM0B,WAAW,CAACkB,WAAW,IAAI5C,EAAM0B,WAAW,CAACkB,WAAW,CAACf,EAAO,IAAG7B,EAAM0B,WAAW,CAACkC,MAAM,IAAI5D,EAAM0B,WAAW,CAACkC,MAAM,CAAC/B,EAAO,EAAE,CAG5Z,EAETK,mBAAoB,eACtB,GAAG,EAEYJ,gBAAgBA,EAAC,2FC1LhC,SAAS+B,EAASC,CAAoB,EACpC,GAAM,GAAE7D,CAAC,CAAE,CAAGC,CAAAA,EAAAA,EAAAA,EAAAA,CAAcA,CAAC,UACvB6D,EAA6B,CACjCC,gBAAiB/D,EAAE,cACnB,EACI,SACJgE,CAAO,MACPC,CAAI,WACJC,CAAS,uBACTC,CAAqB,WACrBC,CAAS,oBACTC,CAAkB,qBAClBC,CAAmB,kBACnBC,CAAgB,aAChBC,CAAW,oBACXC,CAAkB,gBAClBC,CAAc,SACdC,CAAO,WACPC,CAAS,sBACTC,CAAoB,mBACpBC,CAAiB,CACjBC,YAAU,QACVC,CAAM,kBACNC,CAAgB,cAChBC,CAAY,CAEZ,CADA,EACGC,EACJ,CAAGtB,EAGEuB,EAAiB,4BACrBtB,EACAuB,gBAAiBrF,EAAE,IAP0C,MAQ7DsF,UAAU,UACVtB,EACAC,KAAMA,GAAQ,EAAE,CAChBsB,OAAO,EACPC,2BAA4BrB,EAC5BsB,UAAWrB,EACXsB,gBAAiBf,qBACjBN,EACAsB,WAAY,GACZC,iBAAkBhB,EAClBiB,kBAAmBpB,GAA0C,GAC7DqB,eADwCrB,cACVD,GAA4B,CAAC,GAAI,GAAI,GAAI,CAA3BA,EAA+B,GAAG,CAC9EuB,oBAAqB7B,EACrB8B,oBAAqB1B,EACrB2B,aAAc1B,iBACdG,uBACAG,oBACAC,EACAoB,kBAAmB,UAACC,EAAAA,CAAYA,CAAAA,CAAAA,GAChCC,SAAU,UAACC,IAAAA,CAAE5F,UAAU,6CACvBsE,SACAC,eACAE,mBACAD,EACAxE,UAAW,WACb,EACA,MACE,UAAC6F,EAAAA,EAASA,CAAAA,CAAE,GAAGlB,CAAc,EAEjC,CAEAxB,EAAS2C,YAAY,CAAG,CACtBd,WAAW,EACXE,YAAY,EACZzB,UAAW,KACXU,WAAW,EACXC,qBAAsB,KACtBC,mBAAmB,EACnBC,YAAY,EACZE,kBAAkB,CACpB,EAEA,MAAerB,QAAQA,EAAC,mEChHT,SAASjC,EAAgB7B,CAAW,EAC/C,MACE,UAACM,MAAAA,CAAIK,UAAU,sDACb,UAACL,MAAAA,CAAIK,UAAU,mBAAU,yCAG/B,gECFa,SAASI,EAAYgD,CAAuB,EACzD,MACE,UAAC2C,KAAAA,CAAG/F,UAAU,wBAAgBoD,EAAM/C,KAAK,EAE7C,8KC2HA,MAvH6B,IACzB,GAAM,CAAC2F,EAAWC,EAAe,CAAGC,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAC,EAAE,EACzC,EAAGC,EAAW,CAAGD,CAAAA,EAAAA,EAAAA,MAqHQtF,EArHRsF,CAAQA,EAAC,GAC1B,CAACzC,EAAW2C,EAAa,CAAGF,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAC,GACrC,CAACG,EAASC,EAAW,CAAGJ,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAC,IACjC,CAACK,EAAaC,EAAS,CAAGN,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,EAAC,GACnC,CAACO,EAAuBC,EAAyB,CAAGR,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAC,CAAC,GAC9D,GAAE3G,CAAC,CAAE,CAAGC,CAAAA,EAAAA,EAAAA,EAAAA,CAAcA,CAAC,UAEvBmH,EAAwB,CAC1BC,KAAM,CAAEvG,MAAO,KAAM,EACrBwG,MAAOR,EACPS,KAAM,EACNC,MAAO,CAAC,CACZ,EAEMxD,EAAU,CACZ,CACIyD,KAAMzH,EAAE,SACR0H,SAAU,OACd,EACA,CACID,KAAMzH,EAAE,UACR0H,SAAU,GACVC,KAAM,GACF,WAACvH,MAAAA,WACG,UAACW,IAAIA,CAACC,KAAK,6BAA6BC,GAAI,OAAvCF,gCAAoF,OAAN6G,EAAEC,GAAG,WAEpF,UAACxB,IAAAA,CAAE5F,UAAU,uBAEV,OAEP,UAACqH,IAAAA,CAAEC,QAAS,IAAMC,EAAWJ,YACzB,UAACvB,IAAAA,CAAE5F,UAAU,4BACZ,MAGjB,EACH,CAEKwH,EAAyB,UAC3BrB,GAAW,GACX,IAAMsB,EAAW,MAAMC,EAAAA,CAAUA,CAACC,GAAG,CAAC,oBAAqBhB,GACvDc,GAAYA,EAASjE,IAAI,EAAIiE,EAASjE,IAAI,CAACoE,MAAM,CAAG,GAAG,CACvD3B,EAAewB,EAASjE,IAAI,EAC5B4C,EAAaqB,EAASI,UAAU,EAChC1B,GAAW,GAEnB,EAQMtC,EAAsB,MAAOiE,EAAiBhB,KAChDH,EAAsBE,KAAK,CAAGiB,EAC9BnB,EAAsBG,IAAI,CAAGA,EAC7BX,GAAW,GACX,IAAMsB,EAAW,MAAMC,EAAAA,CAAUA,CAACC,GAAG,CAAC,oBAAqBhB,GACvDc,GAAYA,EAASjE,IAAI,EAAIiE,EAASjE,IAAI,CAACoE,MAAM,CAAG,GAAG,CACvD3B,EAAewB,EAASjE,IAAI,EAC5B8C,EAAWwB,GACX3B,GAAW,GAEnB,EAEMoB,EAAa,MAAOQ,IACtBrB,EAAyBqB,EAAIX,GAAG,EAChCZ,GAAS,EACb,EAEMwB,EAAe,UACjB,GAAI,CACA,MAAMN,EAAAA,CAAUA,CAACO,MAAM,CAAC,qBAA2C,OAAtBxB,IAC7Ce,IACAhB,GAAS,GACT0B,EAAAA,EAAKA,CAACC,OAAO,CAAC5I,EAAE,kEACpB,CAAE,MAAO6I,EAAO,CACZF,EAAAA,EAAKA,CAACE,KAAK,CAAC7I,EAAE,4DAClB,CACJ,EAEM8I,EAAY,IAAM7B,GAAS,GAMjC,MAJA8B,CAAAA,EAAAA,EAAAA,SAAAA,CAASA,CAAC,KACNd,GACJ,EAAG,EAAE,EAGD,WAAC7H,MAAAA,WACG,WAAC4I,EAAAA,CAAKA,CAAAA,CAACC,KAAMjC,EAAakC,OAAQJ,YAC9B,UAACE,EAAAA,CAAKA,CAACG,MAAM,EAACC,WAAW,aACrB,UAACJ,EAAAA,CAAKA,CAACK,KAAK,WAAErJ,EAAE,2CAEpB,UAACgJ,EAAAA,CAAKA,CAACM,IAAI,WAAEtJ,EAAE,uCACf,WAACgJ,EAAAA,CAAKA,CAACO,MAAM,YACT,UAACrI,EAAAA,CAAMA,CAAAA,CAACC,QAAQ,YAAY4G,QAASe,WAChC9I,EAAE,YAEP,UAACkB,EAAAA,CAAMA,CAAAA,CAACC,QAAQ,UAAU4G,QAASU,WAC9BzI,EAAE,eAKf,UAAC4D,EAAAA,CAAQA,CAAAA,CACLI,QAASA,EACTC,KAAMwC,EACNvC,UAAWA,EACXU,UAAW,GACXN,oBAAqBA,EACrBC,iBA/DcgD,CA+DIhD,GA9D1B6C,EAAsBE,KAAK,CAAGR,EAC9BM,EAAsBG,IAAI,CAAGA,EAC7BU,GACJ,MA+DJ,mBChIA,4CACA,mCACA,WACA,OAAe,EAAQ,KAA8D,CACrF,EACA,SAFsB", "sources": ["webpack://_N_E/./pages/adminsettings/operationstatuses/index.tsx", "webpack://_N_E/./pages/adminsettings/permissions.tsx", "webpack://_N_E/./components/common/RKITable.tsx", "webpack://_N_E/./pages/rNoAccess.tsx", "webpack://_N_E/./components/common/PageHeading.tsx", "webpack://_N_E/./pages/adminsettings/operationstatuses/operationstatusTable.tsx", "webpack://_N_E/?e8c2"], "sourcesContent": ["//Import Library\r\nimport { Container, <PERSON>, <PERSON>, Button } from \"react-bootstrap\";\r\nimport Link from \"next/link\";\r\n\r\n//Import services/components\r\nimport PageHeading from \"../../../components/common/PageHeading\";\r\nimport OperationstatusTable from \"./operationstatusTable\";\r\nimport { useTranslation } from 'next-i18next';\r\nimport { canAddOperationStatus } from \"../permissions\";\r\nimport { useSelector } from \"react-redux\";\r\nimport NoAccessMessage from \"../../rNoAccess\";\r\n\r\n\r\nconst OperationstatusIndex = (_props: any) => {\r\n  const { t } = useTranslation('common');\r\n  const ShowOperationstatusIndex = () => {\r\n    return (\r\n      <div>\r\n        <Container style={{ overflowX: \"hidden\" }} fluid className=\"p-0\">\r\n          <Row>\r\n            <Col xs={12}>\r\n              <PageHeading title= {t(\"adminsetting.OperationStatus.Operationstatus\")} />\r\n            </Col>\r\n          </Row>\r\n          <Row>\r\n            <Col xs={12}>\r\n              <Link\r\n                href=\"/adminsettings/[...routes]\"\r\n                as=\"/adminsettings/create_operationstatus\"\r\n                >\r\n                <Button variant=\"secondary\" size=\"sm\">\r\n                {t(\"adminsetting.OperationStatus.AddOperationstatus\")}\r\n              </Button>\r\n              </Link>\r\n            </Col>\r\n          </Row>\r\n          <Row className=\"mt-3\">\r\n            <Col xs={12}>\r\n              <OperationstatusTable />\r\n            </Col>\r\n          </Row>\r\n        </Container>\r\n      </div>\r\n    );\r\n  }\r\n\r\n  const ShowAddOperationStatus = canAddOperationStatus(() => <ShowOperationstatusIndex />);\r\n  const state:any = useSelector((state) => state);\r\n  if (!(state?.permissions?.operation_status?.['create:any'])) {\r\n    return <NoAccessMessage />\r\n  }\r\n  return(\r\n    <ShowAddOperationStatus />\r\n  )  \r\n}\r\nexport default OperationstatusIndex;", "//Import services/components\r\nimport connectedAuthWrapper from 'redux-auth-wrapper/connectedAuthWrapper';\r\n\r\nconst create = \"create:any\";\r\nexport const canAddAreaOfWork = connectedAuthWrapper({\r\n  authenticatedSelector: (state) => {\r\n    if (state.permissions && state.permissions.area_of_work && state.permissions.area_of_work[create]) {\r\n      return true;\r\n    }\r\n    return false;\r\n  },\r\n  wrapperDisplayName: 'CanAddAreaOfWork',\r\n});\r\n\r\nexport const canAddCountry = connectedAuthWrapper({\r\n  authenticatedSelector: (state) => {\r\n    if (state.permissions && state.permissions.country && state.permissions.country[create]) {\r\n      return true;\r\n    }\r\n    return false;\r\n  },\r\n  wrapperDisplayName: 'CanAddCountry',\r\n});\r\n\r\nexport const canAddDeploymentStatus = connectedAuthWrapper({\r\n  authenticatedSelector: (state) => {\r\n    if (state.permissions && state.permissions.deployment_status && state.permissions.deployment_status[create]) {\r\n      return true;\r\n    }\r\n    return false;\r\n  },\r\n  wrapperDisplayName: 'CanAddDeploymentStatus',\r\n});\r\n\r\nexport const canAddEventStatus = connectedAuthWrapper({\r\n  authenticatedSelector: (state) => {\r\n    if (state.permissions && state.permissions.event_status && state.permissions.event_status[create]) {\r\n      return true;\r\n    }\r\n    return false;\r\n  },\r\n  wrapperDisplayName: 'CanAddEventStatus',\r\n});\r\n\r\nexport const canAddExpertise = connectedAuthWrapper({\r\n  authenticatedSelector: (state) => {\r\n    if (state.permissions && state.permissions.expertise && state.permissions.expertise[create]) {\r\n      return true;\r\n    }\r\n    return false;\r\n  },\r\n  wrapperDisplayName: 'CanAddExpertise',\r\n});\r\n\r\nexport const canAddFocalPointApproval = connectedAuthWrapper({\r\n  authenticatedSelector: (state) => {\r\n    if (state.permissions && state.permissions.institution_focal_point && state.permissions.institution_focal_point[create]) {\r\n      return true;\r\n    }\r\n    return false;\r\n  },\r\n  wrapperDisplayName: 'CanAddFocalPointApproval',\r\n});\r\n\r\nexport const canAddVspaceApproval = connectedAuthWrapper({\r\n  authenticatedSelector: (state) => {\r\n    if (state.permissions && state.permissions.institution_focal_point && state.permissions.institution_focal_point[create]) {\r\n      return true;\r\n    }\r\n    return false;\r\n  },\r\n  wrapperDisplayName: 'CanAddVspaceApproval',\r\n});\r\nexport const canAddHazards = connectedAuthWrapper({\r\n  authenticatedSelector: (state) => {\r\n    if (state.permissions && state.permissions.hazard && state.permissions.hazard[create]) {\r\n      return true;\r\n    }\r\n    return false;\r\n  },\r\n  wrapperDisplayName: 'CanAddHazards',\r\n});\r\n\r\nexport const canAddHazardTypes = connectedAuthWrapper({\r\n  authenticatedSelector: (state) => {\r\n    if (state.permissions && state.permissions.hazard_type && state.permissions.hazard_type[create]) {\r\n      return true;\r\n    }\r\n    return false;\r\n  },\r\n  wrapperDisplayName: 'CanAddHazardTypes',\r\n}); \r\n\r\nexport const canAddOrganisationApproval = connectedAuthWrapper({\r\n  authenticatedSelector: (state) => {\r\n    if (state.permissions && state.permissions.institution && state.permissions.institution[create]) {\r\n      return true;\r\n    }\r\n    return false;\r\n  },\r\n  wrapperDisplayName: 'CanAddOrganisationApproval',\r\n}); \r\n\r\nexport const canAddOrganisationNetworks = connectedAuthWrapper({\r\n  authenticatedSelector: (state) => {\r\n    if (state.permissions && state.permissions.institution_network && state.permissions.institution_network[create]) {\r\n      return true;\r\n    }\r\n    return false;\r\n  },\r\n  wrapperDisplayName: 'CanAddOrganisationNetworks',\r\n});\r\n\r\nexport const canAddOrganisationTypes = connectedAuthWrapper({\r\n  authenticatedSelector: (state) => {\r\n    if (state.permissions && state.permissions.institution_type && state.permissions.institution_type[create]) {\r\n      return true;\r\n    }\r\n    return false;\r\n  },\r\n  wrapperDisplayName: 'CanAddOrganisationTypes',\r\n});\r\n\r\nexport const canAddOperationStatus = connectedAuthWrapper({\r\n  authenticatedSelector: (state) => {\r\n    if (state.permissions && state.permissions.operation_status && state.permissions.operation_status[create]) {\r\n      return true;\r\n    }\r\n    return false;\r\n  },\r\n  wrapperDisplayName: 'CanAddOperationStatus',\r\n});\r\n\r\nexport const canAddProjectStatus = connectedAuthWrapper({\r\n  authenticatedSelector: (state) => {\r\n    if (state.permissions && state.permissions.project_status && state.permissions.project_status[create]) {\r\n      return true;\r\n    }\r\n    return false;\r\n  },\r\n  wrapperDisplayName: 'CanAddProjectStatus',\r\n});\r\n\r\nexport const canAddRegions = connectedAuthWrapper({\r\n  authenticatedSelector: (state) => {\r\n    if (state.permissions && state.permissions.region && state.permissions.region[create]) {\r\n      return true;\r\n    }\r\n    return false;\r\n  },\r\n  wrapperDisplayName: 'CanAddRegions',\r\n});\r\n\r\nexport const canAddRiskLevels = connectedAuthWrapper({\r\n  authenticatedSelector: (state) => {\r\n    if (state.permissions && state.permissions.risk_level && state.permissions.risk_level[create]) {\r\n      return true;\r\n    }\r\n    return false;\r\n  },\r\n  wrapperDisplayName: 'CanAddRiskLevels',\r\n});\r\n\r\nexport const canAddSyndromes = connectedAuthWrapper({\r\n  authenticatedSelector: (state) => {\r\n    if (state.permissions && state.permissions.syndrome && state.permissions.syndrome[create]) {\r\n      return true;\r\n    }\r\n    return false;\r\n  },\r\n  wrapperDisplayName: 'CanAddSyndromes',\r\n});\r\n\r\nexport const canAddUpdateTypes = connectedAuthWrapper({\r\n  authenticatedSelector: (state) => {\r\n    if (state.permissions && state.permissions.update_type && state.permissions.update_type[create]) {\r\n      return true;\r\n    }\r\n    return false;\r\n  },\r\n  wrapperDisplayName: 'CanAddUpdateTypes',\r\n});\r\n\r\nexport const canAddUsers = connectedAuthWrapper({\r\n  authenticatedSelector: (state) => {\r\n    if (state.permissions && state.permissions.users && state.permissions.users[create]) {\r\n      return true;\r\n    }\r\n    return false;\r\n  },\r\n  wrapperDisplayName: 'CanAddUsers',\r\n});\r\n\r\nexport const canAddWorldRegion = connectedAuthWrapper({\r\n  authenticatedSelector: (state) => {\r\n    if (state.permissions && state.permissions.worl_region && state.permissions.worl_region[create]) {\r\n      return true;\r\n    }\r\n    return false;\r\n  },\r\n  wrapperDisplayName: 'CanAddWorldRegion',\r\n});\r\n\r\nexport const canAddLandingPage = connectedAuthWrapper({\r\n  authenticatedSelector: (state) => {\r\n    if (state.permissions && state.permissions.landing_page && state.permissions.landing_page[create]) {\r\n      return true;\r\n    }\r\n    return false;\r\n  },\r\n  wrapperDisplayName: 'CanAddLandingPage',\r\n});\r\n\r\nexport const canAddContent = connectedAuthWrapper({\r\n  authenticatedSelector: (state) => {\r\n    if (state.permissions && state.permissions.operation && state.permissions.operation[create] && state.permissions.project && state.permissions.project[create]&& state.permissions.event && state.permissions.event[create]&& state.permissions.vspace && state.permissions.vspace[create]&& state.permissions.institution && state.permissions.institution[create]&& state.permissions.update && state.permissions.update[create]) {\r\n      return true;\r\n    }\r\n    return false;\r\n  },\r\n  wrapperDisplayName: 'CanAddContent',\r\n});\r\n\r\nexport default canAddAreaOfWork;", "//Import Library\r\nimport React from 'react'\r\nimport DataTable  from 'react-data-table-component';\r\n\r\n//Import services/components\r\nimport { useTranslation } from 'next-i18next';\r\nimport CustomLoader from './CustomLoader';\r\n\r\ninterface RKITableProps {\r\n  columns: any[];\r\n  data: any[];\r\n  totalRows?: number;\r\n  resetPaginationToggle?: boolean;\r\n  subheader?: boolean;\r\n  subHeaderComponent?: React.ReactNode;\r\n  handlePerRowsChange?: (newPerPage: number, page: number) => void;\r\n  handlePageChange?: (page: number) => void;\r\n  rowsPerPage?: number[];\r\n  defaultRowsPerPage?: number;\r\n  selectableRows?: boolean;\r\n  loading?: boolean;\r\n  pagServer?: boolean;\r\n  onSelectedRowsChange?: (selectedRows: any) => void;\r\n  clearSelectedRows?: boolean;\r\n  sortServer?: boolean;\r\n  onSort?: (column: any, sortDirection: string) => void;\r\n  persistTableHead?: boolean;\r\n  sortFunction?: (rows: any[], field: any, direction: 'asc' | 'desc') => any[];\r\n  noHeader?: boolean;\r\n  dense?: boolean;\r\n  pagination?: boolean;\r\n  paginationServer?: boolean;\r\n  paginationTotalRows?: number;\r\n  subHeaderAlign?: string;\r\n  [key: string]: any;\r\n}\r\n\r\nfunction RKITable(props: RKITableProps) {\r\n  const { t } = useTranslation('common');\r\n  const paginationComponentOptions = {\r\n    rowsPerPageText: t('Rowsperpage'),\r\n    };\r\n  const {\r\n    columns,\r\n    data,\r\n    totalRows,\r\n    resetPaginationToggle,\r\n    subheader,\r\n    subHeaderComponent,\r\n    handlePerRowsChange,\r\n    handlePageChange,\r\n    rowsPerPage,\r\n    defaultRowsPerPage,\r\n    selectableRows,\r\n    loading,\r\n    pagServer,\r\n    onSelectedRowsChange,\r\n    clearSelectedRows,\r\n    sortServer,\r\n    onSort,\r\n    persistTableHead,\r\n    sortFunction,\r\n    // Filter out any unknown props that might cause DOM warnings\r\n    ...otherProps\r\n  } = props;\r\n\r\n  // Only pass known DataTable props to avoid DOM warnings\r\n  const dataTableProps = {\r\n    paginationComponentOptions,\r\n    noDataComponent: t(\"NoData\"),\r\n    noHeader: true,\r\n    columns,\r\n    data: data || [],\r\n    dense: true,\r\n    paginationResetDefaultPage: resetPaginationToggle,\r\n    subHeader: subheader,\r\n    progressPending: loading,\r\n    subHeaderComponent,\r\n    pagination: true,\r\n    paginationServer: pagServer,\r\n    paginationPerPage: defaultRowsPerPage ? defaultRowsPerPage : 10,\r\n    paginationRowsPerPageOptions: rowsPerPage ? rowsPerPage : [10, 15, 20, 25, 30],\r\n    paginationTotalRows: totalRows,\r\n    onChangeRowsPerPage: handlePerRowsChange,\r\n    onChangePage: handlePageChange,\r\n    selectableRows,\r\n    onSelectedRowsChange,\r\n    clearSelectedRows,\r\n    progressComponent: <CustomLoader/>,\r\n    sortIcon: <i className=\"sort-icon fas fa-exchange-alt\" />,\r\n    sortServer,\r\n    onSort,\r\n    sortFunction,\r\n    persistTableHead,\r\n    className: \"rki-table\"\r\n  };\r\n  return (\r\n    <DataTable {...dataTableProps} />\r\n  )\r\n};\r\n\r\nRKITable.defaultProps = {\r\n  subHeader: false,\r\n  pagination: true,\r\n  totalRows: null,\r\n  pagServer: true,\r\n  onSelectedRowsChange: null,\r\n  clearSelectedRows: false,\r\n  sortServer: false,\r\n  persistTableHead: false\r\n}\r\n\r\nexport default RKITable;\r\n\r\n", "export default function NoAccessMessage(_props: any) {\r\n    return (\r\n      <div className=\"container-fluid p-0 response-message-block\">\r\n        <div className=\"message\">you don't have permission to access</div>\r\n      </div>\r\n    )\r\n  }", "interface PageHeadingProps {\r\n  title: string; // Required based on actual usage\r\n}\r\n\r\nexport default function PageHeading(props: PageHeadingProps) {\r\n  return (\r\n    <h2 className=\"page-heading\">{props.title}</h2>\r\n  )\r\n}\r\n", "//Import Library\r\nimport { useState, useEffect } from \"react\";\r\nimport Link from \"next/link\";\r\nimport { <PERSON><PERSON>, Button } from \"react-bootstrap\";\r\n\r\nimport toast from 'react-hot-toast';\r\n\r\n//Import services/components\r\nimport RKITable from \"../../../components/common/RKITable\";\r\nimport apiService from \"../../../services/apiService\";\r\nimport { useTranslation } from 'next-i18next';\r\n\r\nconst OperationstatusTable = (_props: any) => {\r\n    const [tabledata, setDataToTable] = useState([]);\r\n    const [, setLoading] = useState(false);\r\n    const [totalRows, setTotalRows] = useState(0);\r\n    const [perPage, setPerPage] = useState(10);\r\n    const [isModalShow, setModal] = useState(false);\r\n    const [selectOperationstatus, setSelectOperationstatus] = useState({});\r\n    const { t } = useTranslation('common');\r\n    \r\n    const operationstatusParams = {\r\n        sort: { title: \"asc\" },\r\n        limit: perPage,\r\n        page: 1,\r\n        query: {},\r\n    };\r\n\r\n    const columns = [\r\n        {\r\n            name: t(\"Title\"),\r\n            selector: \"title\",\r\n        },\r\n        {\r\n            name: t(\"action\"),\r\n            selector: \"\",\r\n            cell: (d: any) => (\r\n                <div>\r\n                    <Link href=\"/adminsettings/[...routes]\" as={`/adminsettings/edit_operationstatus/${d._id}`}>\r\n\r\n                        <i className=\"icon fas fa-edit\" />\r\n\r\n                    </Link>\r\n                    &nbsp;\r\n                    <a onClick={() => userAction(d)}>\r\n                        <i className=\"icon fas fa-trash-alt\" />\r\n                    </a>{\" \"}\r\n                </div>\r\n            ),\r\n        },\r\n    ];\r\n\r\n    const getoperationstatusData = async () => {\r\n        setLoading(true);\r\n        const response = await apiService.get(\"/operation_status\", operationstatusParams);\r\n        if (response && response.data && response.data.length > 0) {\r\n            setDataToTable(response.data);\r\n            setTotalRows(response.totalCount);\r\n            setLoading(false);\r\n        }\r\n    };\r\n\r\n    const handlePageChange = (page: any) => {\r\n        operationstatusParams.limit = perPage;\r\n        operationstatusParams.page = page;\r\n        getoperationstatusData();\r\n    };\r\n\r\n    const handlePerRowsChange = async (newPerPage: any, page: any) => {\r\n        operationstatusParams.limit = newPerPage;\r\n        operationstatusParams.page = page;\r\n        setLoading(true);\r\n        const response = await apiService.get(\"/operation_status\", operationstatusParams);\r\n        if (response && response.data && response.data.length > 0) {\r\n            setDataToTable(response.data);\r\n            setPerPage(newPerPage);\r\n            setLoading(false);\r\n        }\r\n    };\r\n\r\n    const userAction = async (row: any) => {\r\n        setSelectOperationstatus(row._id);\r\n        setModal(true);\r\n    };\r\n\r\n    const modalConfirm = async () => {\r\n        try {\r\n            await apiService.remove(`/operation_status/${selectOperationstatus}`);\r\n            getoperationstatusData();\r\n            setModal(false);\r\n            toast.success(t(\"adminsetting.OperationStatus.Table.opStatusDeletedSuccessfully\"));\r\n        } catch (error) {\r\n            toast.error(t(\"adminsetting.OperationStatus.Table.errorDeletingOpStatus\"));\r\n        }\r\n    };\r\n\r\n    const modalHide = () => setModal(false);\r\n\r\n    useEffect(() => {\r\n        getoperationstatusData();\r\n    }, []);\r\n\r\n    return (\r\n        <div>\r\n            <Modal show={isModalShow} onHide={modalHide}>\r\n                <Modal.Header closeButton>\r\n                    <Modal.Title>{t(\"adminsetting.OperationStatus.Delete\")}</Modal.Title>\r\n                </Modal.Header>\r\n                <Modal.Body>{t(\"adminsetting.OperationStatus.sure\")}</Modal.Body>\r\n                <Modal.Footer>\r\n                    <Button variant=\"secondary\" onClick={modalHide}>\r\n                        {t(\"Cancel\")}\r\n                    </Button>\r\n                    <Button variant=\"primary\" onClick={modalConfirm}>\r\n                        {t(\"yes\")}\r\n                    </Button>\r\n                </Modal.Footer>\r\n            </Modal>\r\n\r\n            <RKITable\r\n                columns={columns}\r\n                data={tabledata}\r\n                totalRows={totalRows}\r\n                pagServer={true}\r\n                handlePerRowsChange={handlePerRowsChange}\r\n                handlePageChange={handlePageChange}\r\n            />\r\n        </div>\r\n    );\r\n};\r\n\r\nexport default OperationstatusTable;\r\n", "\n    (window.__NEXT_P = window.__NEXT_P || []).push([\n      \"/adminsettings/operationstatuses\",\n      function () {\n        return require(\"private-next-pages/adminsettings/operationstatuses/index.tsx\");\n      }\n    ]);\n    if(module.hot) {\n      module.hot.dispose(function () {\n        window.__NEXT_P.push([\"/adminsettings/operationstatuses\"])\n      });\n    }\n  "], "names": ["_props", "state", "t", "useTranslation", "ShowOperationstatusIndex", "OperationstatusIndex", "div", "Container", "style", "overflowX", "fluid", "className", "Row", "Col", "xs", "PageHeading", "title", "Link", "href", "as", "<PERSON><PERSON>", "variant", "size", "OperationstatusTable", "ShowAddOperationStatus", "canAddOperationStatus", "useSelector", "permissions", "operation_status", "NoAccessMessage", "create", "canAddAreaOfWork", "connectedAuthWrapper", "authenticatedSelector", "area_of_work", "wrapperDisplayName", "country", "canAddDeploymentStatus", "deployment_status", "event_status", "expertise", "institution_focal_point", "canAddVspaceApproval", "hazard", "hazard_type", "institution", "institution_network", "canAddOrganisationTypes", "institution_type", "project_status", "region", "risk_level", "syndrome", "update_type", "users", "worl_region", "landing_page", "operation", "project", "event", "vspace", "update", "RKITable", "props", "paginationComponentOptions", "rowsPerPageText", "columns", "data", "totalRows", "resetPaginationToggle", "subheader", "subHeaderComponent", "handlePerRowsChange", "handlePageChange", "rowsPerPage", "defaultRowsPerPage", "selectableRows", "loading", "pagServer", "onSelectedRowsChange", "clearSelectedRows", "sortServer", "onSort", "persistTableHead", "sortFunction", "otherProps", "dataTableProps", "noDataComponent", "<PERSON><PERSON><PERSON><PERSON>", "dense", "paginationResetDefaultPage", "subHeader", "progressPending", "pagination", "paginationServer", "paginationPerPage", "paginationRowsPerPageOptions", "paginationTotalRows", "onChangeRowsPerPage", "onChangePage", "progressComponent", "CustomLoader", "sortIcon", "i", "DataTable", "defaultProps", "h2", "tabledata", "setDataToTable", "useState", "setLoading", "setTotalRows", "perPage", "setPerPage", "isModalShow", "setModal", "selectOperationstatus", "setSelectOperationstatus", "operationstatusParams", "sort", "limit", "page", "query", "name", "selector", "cell", "d", "_id", "a", "onClick", "userAction", "getoperationstatusData", "response", "apiService", "get", "length", "totalCount", "newPerPage", "row", "modalConfirm", "remove", "toast", "success", "error", "modalHide", "useEffect", "Modal", "show", "onHide", "Header", "closeButton", "Title", "Body", "Footer"], "sourceRoot": "", "ignoreList": []}