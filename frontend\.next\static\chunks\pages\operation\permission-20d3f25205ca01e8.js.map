{"version": 3, "file": "static/chunks/pages/operation/permission-20d3f25205ca01e8.js", "mappings": "4SAOO,IAAMA,EAAkBC,CAAAA,EAAAA,EAAAA,CAAAA,CAAoBA,CAAC,CAClDC,sBAAuB,KACjBC,EAAMC,WAAW,IAAID,EAAMC,WAAW,CAACC,SAAS,IAAIF,EAAMC,WAAW,CAACC,SAAS,CAAC,aAAa,CAKnGC,CALqG,kBAKjF,iBACtB,GAAG,EAEgCL,CAAAA,EAAAA,EAAAA,CAAAA,CAAoBA,CAAC,CACtDC,sBAAuB,KACjBC,EAAMC,WAAW,IAAID,EAAMC,WAAW,CAACC,SAAS,IAAIF,EAAMC,WAAW,CAACC,SAAS,CAAC,aAAa,CAKnGC,CALqG,kBAKjF,sBACpBC,iBAAkB,IAAM,UAACC,EAAAA,OAAIA,CAAAA,CAAAA,EAC/B,GAAG,EAE6BP,CAAAA,EAAAA,EAAAA,CAAAA,CAAoBA,CAAC,CACnDC,sBAAuB,CAACC,EAAOM,KAC7B,GAAIN,EAAMC,WAAW,EAAID,EAAMC,WAAW,CAACC,SAAS,EAAE,GAChDF,EAAMC,WAAW,CAACC,SAAS,CAAC,aAAa,CAC3C,CAD6C,KACtC,QAEP,GAAIF,EAAMC,WAAW,CAACC,SAAS,CAAC,aAAa,EAAE,EACnCA,SAAS,EAAII,EAAMJ,SAAS,CAACK,IAAI,EAAID,EAAMJ,SAAS,CAACK,IAAI,CAACC,GAAG,GAAKR,EAAMO,IAAI,CAACC,GAAG,CACxF,CAD0F,MACnF,CAGb,CAEF,OAAO,CACT,EACAL,mBAAoB,kBACtB,GAAG,EAEiCL,CAAAA,EAAAA,EAAAA,CAAAA,CAAoBA,CAAC,CACvDC,sBAAuB,CAACC,EAAOM,KAC7B,GAAIN,EAAMC,WAAW,EAAID,EAAMC,WAAW,CAACC,SAAS,EAAE,GAChDF,EAAMC,WAAW,CAACC,SAAS,CAAC,aAAa,CAC3C,CAD6C,MACtC,OAEP,GAAIF,EAAMC,WAAW,CAACC,SAAS,CAAC,aAAa,EAAE,EACnCA,SAAS,EAAII,EAAMJ,SAAS,CAACK,IAAI,EAAID,EAAMJ,SAAS,CAACK,IAAI,CAACC,GAAG,GAAKR,EAAMO,IAAI,CAACC,GAAG,CACxF,CAD0F,MACnF,CAGb,CAEF,OAAO,CACT,EACAL,mBAAoB,uBACpBC,iBAAkB,IAAM,UAACC,EAAAA,OAAIA,CAAAA,CAAAA,EAC/B,GAEaI,EAA0BX,CAAAA,EAAAA,EAAAA,CAAAA,CAAoBA,CAAC,CAC1DC,sBAAuB,KACjBC,EAAMC,WAAW,IAAID,EAAMC,WAAW,CAACS,MAAM,IAAIV,EAAMC,WAAW,CAACS,MAAM,CAAC,WAAW,CAK3FP,CAL6F,kBAKzE,yBACtB,GAAG,EAEYN,eAAeA,EAAC,EC1E/B,4CACA,wBACA,WACA,OAAe,EAAQ,KAA6C,CACpE,EACA,SAFsB", "sources": ["webpack://_N_E/./pages/operation/permission.tsx", "webpack://_N_E/?5eac"], "sourcesContent": ["//Import Library\r\nimport React from 'react';\r\nimport connectedAuthWrapper from 'redux-auth-wrapper/connectedAuthWrapper';\r\n\r\n//Import services/components\r\nimport R403 from \"../r403\";\r\n\r\nexport const canAddOperation = connectedAuthWrapper({\r\n  authenticatedSelector: (state) => {\r\n    if (state.permissions && state.permissions.operation && state.permissions.operation['create:any']) {\r\n      return true;\r\n    }\r\n    return false;\r\n  },\r\n  wrapperDisplayName: 'CanAddOperation',\r\n});\r\n\r\nexport const canAddOperationForm = connectedAuthWrapper({\r\n  authenticatedSelector: (state) => {\r\n    if (state.permissions && state.permissions.operation && state.permissions.operation['create:any']) {\r\n      return true;\r\n    }\r\n    return false;\r\n  },\r\n  wrapperDisplayName: 'CanAddOperationForm',\r\n  FailureComponent: () => <R403/>\r\n});\r\n\r\nexport const canEditOperation = connectedAuthWrapper({\r\n  authenticatedSelector: (state, props) => {\r\n    if (state.permissions && state.permissions.operation) {\r\n      if (state.permissions.operation['update:any']) {\r\n        return true;\r\n      } else { //if update:own\r\n        if (state.permissions.operation['update:own']) {\r\n          if (props.operation && props.operation.user && props.operation.user._id === state.user._id) {\r\n            return true;\r\n          }\r\n        }\r\n      }\r\n    }\r\n    return false;\r\n  },\r\n  wrapperDisplayName: 'CanEditOperation',\r\n});\r\n\r\nexport const canEditOperationForm = connectedAuthWrapper({\r\n  authenticatedSelector: (state, props) => {\r\n    if (state.permissions && state.permissions.operation) {\r\n      if (state.permissions.operation['update:any']) {\r\n        return true;\r\n      } else { //if update:own\r\n        if (state.permissions.operation['update:own']) {\r\n          if (props.operation && props.operation.user && props.operation.user._id === state.user._id) {\r\n            return true;\r\n          }\r\n        }\r\n      }\r\n    }\r\n    return false;\r\n  },\r\n  wrapperDisplayName: 'CanEditOperationForm',\r\n  FailureComponent: () => <R403/>\r\n});\r\n\r\nexport const canViewDiscussionUpdate = connectedAuthWrapper({\r\n  authenticatedSelector: (state) => {\r\n    if (state.permissions && state.permissions.update && state.permissions.update['read:any']) {\r\n      return true;\r\n    }\r\n    return false;\r\n  },\r\n  wrapperDisplayName: 'CanViewDiscussionUpdate',\r\n});\r\n\r\nexport default canAddOperation;", "\n    (window.__NEXT_P = window.__NEXT_P || []).push([\n      \"/operation/permission\",\n      function () {\n        return require(\"private-next-pages/operation/permission.tsx\");\n      }\n    ]);\n    if(module.hot) {\n      module.hot.dispose(function () {\n        window.__NEXT_P.push([\"/operation/permission\"])\n      });\n    }\n  "], "names": ["canAddOperation", "connectedAuthWrapper", "authenticatedSelector", "state", "permissions", "operation", "wrapperDisplayName", "FailureComponent", "R403", "props", "user", "_id", "canViewDiscussionUpdate", "update"], "sourceRoot": "", "ignoreList": []}