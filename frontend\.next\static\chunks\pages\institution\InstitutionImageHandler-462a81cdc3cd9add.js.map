{"version": 3, "file": "static/chunks/pages/institution/InstitutionImageHandler-462a81cdc3cd9add.js", "mappings": "yQAmIA,MAvH+B,OAAC,CAC9BA,QAAM,YAsHOC,EArHbC,CAAY,OACZC,CAAK,OACLC,CAAK,GAmH8BH,EAAC,KAlHpCI,CAAQ,SACRC,CAAO,CACH,GACE,CAACC,EAAOC,EAAS,CAAGC,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAS,GACrC,CAACC,EAAMC,EAAQ,CAAGF,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAS,IACnC,CAACG,EAAKC,EAAO,CAAGJ,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAM,MAC9BK,EAAYC,CAAAA,EAAAA,EAAAA,MAAAA,CAAMA,CAAM,MACtB,GAAEC,CAAC,CAAE,CAAGC,CAAAA,EAAAA,EAAAA,EAAAA,CAAcA,CAAC,UAE/BC,CAAAA,EAAAA,EAAAA,SAAAA,CAASA,CAAC,KACRP,EAAQN,EACV,EAAG,CAACA,EAAS,EAEb,IAAMc,EAAc,UAelB,IAAMC,EAAOC,CAbS,QAENC,EADd,GAYyBC,CAZnBD,EAAME,EAAQC,KAAK,CAAC,KACnBC,EAAAA,OAAOJ,EAAAA,CAAG,CAAC,EAAE,CAACK,KAAK,CAAC,YAAbL,KAAAA,EAAAA,CAAyB,CAAC,EAAE,CACnCM,CADON,CACAO,KAAKP,CAAG,CAAC,EAAE,EACrBQ,EAAIF,EAAKG,MAAM,CACZC,EAAQ,IAAIC,WAAWH,GAC9B,KAAOA,IAAK,CACVE,CAAK,CAACF,EAAE,CAAGF,EAAKM,UAAU,CAACJ,GAE7B,OAAO,IAAIK,KAAK,CAACH,EAAM,CAAE,CAAEI,KAAMV,CAAK,GACxC,EAEeZ,EAAUuB,OAAO,CAACC,QAAQ,GAAGC,SAAS,CAAC,aAAc,KAIpEjC,EADgBkC,CADGC,KAEXC,EAFkBC,GAAG,EAAIF,OAAOG,SAAAA,EACbC,eAAe,CAACzB,IAG3C,IAAM0B,EAAK,IAAIC,IAJsE,KAKrFD,EAAGE,MAAM,CAAC,OAAQ5B,EAAMV,GAExB,GAAI,CACF,IAAMuC,EAAM,MAAMC,EAAAA,CAAUA,CAACC,IAAI,CAAC,SAAUL,EAAI,CAC9C,eAAgB,qBAClB,GAEIG,GAAOA,EAAIG,GAAG,EAAE,EACZH,EAAIG,GAAG,CAEjB,CAAE,QAAM,CACN,KAjCa,CAiCPC,wCACR,CACAC,EAAAA,EAAKA,CAACC,OAAO,CAACvC,EAAE,sCAChBd,GAAa,GACbW,EAAO,MACPF,EAAQ,QACRH,EAAS,EACX,EAEA,MACE,+BACE,UAACgD,MAAAA,UACC,WAACC,EAAAA,CAAKA,CAAAA,CACJC,KAAM1D,EACN2D,KAAK,KACLC,kBAAgB,cAChBC,OAAQ,IAAM3D,GAAa,GAC3B4D,QAAQ,cAER,WAACL,EAAAA,CAAKA,CAACM,IAAI,YACT,WAACP,MAAAA,CAAIQ,UAAU,mFACb,UAACC,IAAYA,CACXC,IAAKpD,EACLqD,MAAO,IACPC,OAAQ,IACRC,aAAc,EACd9D,MAAOA,EACP+D,IANWL,EAMJ,CAAC,EAAG,EAAG,EAAG,GAAI,CACrB9D,MAAOS,GAAYT,EACnBoE,CADa3D,KACN,CAACuD,MAAO,OAAOC,OAAQ,MAAM,IAEtC,UAACZ,MAAAA,CAAIQ,UAAU,2BACb,UAACQ,OAAAA,UAAMxD,EAAE,qEAIb,UAACwC,MAAAA,CAAIQ,UAAU,qBACb,WAACS,EAAAA,CAAGA,CAAAA,WACF,UAACC,EAAAA,CAAGA,CAAAA,CAACC,GAAI,EAAGC,GAAI,EAAGC,GAAI,EAAGb,UAAU,gBAClC,UAACc,IAAAA,UAAG9D,EAAE,YAER,UAAC0D,EAAAA,CAAGA,CAAAA,CAACC,GAAI,GAAIC,GAAI,GAAIC,GAAI,YACvB,UAACE,IAAWA,CACVC,MAAOzE,EACP0E,QAAQ,OACRC,IAAK,EACLC,IAAK,GACLC,KAAM,IACNC,QAAQ,UANEN,SAOCO,GACT9E,EAAS+E,OAAOD,EAAYE,MAAM,CAACR,KAAK,eAOpD,WAACvB,EAAAA,CAAKA,CAACgC,MAAM,YACX,UAACC,EAAAA,CAAMA,CAAAA,CAACC,QAASxE,WAAcH,EAAE,UACjC,UAAC0E,EAAAA,CAAMA,CAAAA,CAACL,QAAQ,SAASM,QAAS,IAAMzF,GAAa,YAClDc,EAAE,qBAOjB,mBChIA,4CACA,uCACA,WACA,OAAe,EAAQ,KAA4D,CACnF,EACA,SAFsB,4JC8OtB,MApOgC,OAAC,OAAEZ,CAAK,QAAEwF,CAAM,IAoOjCC,EApOmCzD,CAAI,CAA2C,GACvF,CAAEpB,GAAC,CAAE,CAAGC,CAAAA,EAAAA,EAAAA,EAAAA,CAAcA,CAAC,CAmOK4E,EAAC,OAlO7B,CAACC,EAAOC,EAAS,CAAGtF,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,EAAU,GACtC,CAACuF,EAAOC,EAAS,CAAQxF,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAC,EAAE,EACpC,CAACyF,EAASC,EAAW,CAAG1F,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAC,IACjC,CAAC2F,EAAUC,EAAY,CAAG5F,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAgB,IAC9C6F,EAAoB,gBAATlE,EAAyB,SAAW,SAGzDlB,CAAAA,EAAAA,EAAAA,SAAAA,CAASA,CAAC,KACN0E,EAASS,EAAY,GAAwCT,MAAAA,CAArCW,8BAAsB,CAAC,gBAAqB,OAAPX,IAAYS,EAAY,KACzF,EAAG,CAACT,EAAO,EAKX,IAAMY,EAAiB,CACnBC,KAAM,EACNC,QAAS,OACTC,cAAe,SACfC,WAAY,SACZC,eAAgB,SAChB1C,MAAO,OACPC,OAAQ,OACR0C,YAAa,GACbC,YAAa,UACbC,gBAAiB,UACjB1C,MAAO,QACP2C,WAAY,0BAChB,EA6CMC,EAAmB,CACrBH,YAAa,SACjB,EAIMI,EAAa,IACfpB,EAASqB,EACb,EAkBM,cAAEC,CAAY,eAAEC,CAAa,cAAEC,CAAY,cAAEC,CAAY,cAAEC,CAAY,gBAAEC,CAAc,CAAE,CAAGC,CAAAA,EAAAA,EAAAA,EAAAA,CAAWA,CAAC,CAC1GC,QAAS,GACTC,OAAQ,UACRC,UAAU,EACVC,QAAS,EACTC,QAAS,IACTC,OApBYC,IAMZjC,EALiBiC,EAAKC,GAAG,CAAC,CAACD,EAAWE,IAClCC,OAAOC,MAAM,CAACJ,EAAM,CAChBK,QAAS5F,IAAIE,eAAe,CAACqF,EACjC,KAGAA,EAAKnG,MAAM,CAAG,GAAG,EACR,GAEjB,EAWIyG,UAiDJ,CAjDeC,QAiDNA,CAA6B,EAelC,MAdiB,UAAU,CAAvBnC,EACkC,SAAS,CAAvC4B,EAAK9F,IAAI,CAACsG,SAAS,CAAC,EAAG,IAGvBpF,EAAAA,EAAKA,CAACqF,KAAK,CAAC3H,EAAE,6BAEE,UAAU,CAAvBsF,GAC6B,OAAM,GAApC4B,EAAK9F,IAAI,CAACsG,SAAS,CAAC,EAAG,IACzBpF,EAAAA,EAAKA,CAACqF,KAAK,CAAC3H,EAAE,6BAMf,IACX,CAhEA,GAIMuD,EAAQqE,CAAAA,EAAAA,EAAAA,OAAAA,CAAOA,CACjB,IAAO,EACH,GAAGpC,CAAS,CACZ,GAAIe,EAAeL,EAAc,CAAE2B,QAAS,iBAAkB,CAAC,CAC/D,GAAIrB,EAAe,CAAEqB,QAAS,oBAAqB,EAAI,CAAEA,QAAS,iBAAkB,CAAC,CACrF,GAAIpB,EAAe,CAAEoB,QAAS,gBAAiB,EAAI,aAAE3B,CAAY,CAAC,CACtE,EACA,CAACK,EAAcE,EAAa,EAK1BqB,EAAiBpB,EAAe3F,MAAM,CAAG,GAAK2F,CAAc,CAAC,EAAE,CAACQ,IAAI,CAACvE,IAAI,CAAG,IAI5EoF,EAAe,IACjB5C,EAAW6C,GACX5I,EAAM4I,EACV,EAIMC,EAAa,UACf,IAAIhG,GAEAA,EADA2C,EACM,MADE,EACI1C,CAAUA,CAACgG,MAAM,CAAC,SAAgB,OAAPtD,IAEjC,MAAM1C,EAAAA,CAAUA,CAACgG,MAAM,CAAC,SAAiB,OAARhD,MAGhCjD,EAAIG,GAAG,EAAE,CAChBiD,EAAY,MACZ0C,EAAa,MAErB,EAIMI,EAAeC,IACjB/C,EAAY+C,EAChB,EAqBA,MACI,iCACI,UAAC5F,MAAAA,CACGQ,UAAU,yDACVO,MAAO,CAAEJ,MAAO,OAAQC,OAAQ,OAAQ,WAExC,WAACZ,MAAAA,CAAK,GAAG6D,EAAa,OAAE9C,CAAM,EAAE,WAC5B,UAAC8E,QAAAA,CAAO,GAAG/B,GAAe,GAC1B,UAACgC,EAAAA,CAAeA,CAAAA,CAACC,KAAMC,EAAAA,GAAgBA,CAAE7F,KAAK,KAAKW,MAAM,SACzD,UAACmF,IAAAA,CAAElF,MAAO,CAAED,MAAO,UAAWoF,aAAc,KAAM,WAC7C1I,EAAE,mDAEP,WAAC2I,QAAAA,CAAMpF,MAAO,CAAED,MAAO,SAAU,YAC7B,UAACQ,IAAAA,UAAG9D,EAAE,WAAa,IAAEA,EAAE,mCAE1B8H,GACG,WAACa,QAAAA,CAAM3F,UAAU,6BACb,UAACsF,EAAAA,CAAeA,CAAAA,CAACC,KAAMK,EAAAA,GAAmBA,CAAEjG,KAAK,KAAKW,MAAM,QAAQ,OAC7DtD,EAAE,0CAGhByG,GACG,WAACkC,QAAAA,CAAM3F,UAAU,cAAcO,MAAO,CAAED,MAAO,KAAM,YACjD,UAACgF,EAAAA,CAAeA,CAAAA,CAACC,KAAMK,EAAAA,GAAmBA,CAAEjG,KAAK,KAAKW,MAAM,QAC3DtD,EAAE,mCAKlBoF,GACG,+BACI,UAAC5C,MAAAA,CAAIe,MAhKQ,CAgKDsF,QA/Jf,OACTlD,cAAe,MACfE,eAAgB,aAChBiD,SAAU,OACVC,UAAW,EACf,WA2JoB,WAACvG,MAAAA,CAAIe,MA/KN,CA+KayF,QA9KnB,cACT3F,aAAc,EACd4F,OAAQ,iBACRP,aAAc,EACdQ,YAAa,GACb/F,MAAO,IACPC,OAAQ,IACR+F,QAAS,EACTC,SAAU,WACVC,UAAW,mCACXC,UAAW,YACf,YAoKwB,UAAC9G,MAAAA,CAAIe,MA1JL,CACpBmC,QAAS,MACb,WAyJ4B,UAAC9F,MAAAA,CAAI2J,IAAKnE,EAAU7B,MA3IpC,CA2I2C3D,QA1I1C,QACTwD,OAAQ,MACZ,MA0IwB,UAACkF,EAAAA,CAAeA,CAAAA,CAACC,KAAMiB,EAAAA,GAAaA,CAAEjG,MAzJ5C,CACd6F,SAAU,WACVK,SAAU,OACVC,IAAK,QACLC,MAAO,QACPC,OAAQ,IACRC,OAAQ,UACR7D,gBAAiB,OACjB1C,MAAO,OACPD,aAAc,KAClB,EA+I2EC,MAAM,QAAQqB,QAASsD,WAM1F,UAAChJ,EAAAA,OAAsBA,CAAAA,CACnBD,OAAQ8F,EACR1F,MAAO,GAAa2I,EAAaC,GACjC7I,MAAO6F,GAASA,CAAK,CAAC,EAAE,CAAGA,CAAK,CAAC,EAAE,CAACuC,OAAO,CAAG,GAC9CrI,aAAc,GAAciH,EAAWC,GACvC/G,SAAU2F,GAASA,CAAK,CAAC,EAAE,CAAGA,CAAK,CAAC,EAAE,CAACtF,IAAI,CAAG,GAC9CJ,QAAS,GAAc6I,EAAYC,OAInD", "sources": ["webpack://_N_E/./pages/institution/InstitutionImageEditor.tsx", "webpack://_N_E/?eeb6", "webpack://_N_E/./pages/institution/InstitutionImageHandler.tsx"], "sourcesContent": ["//Import Library\r\nimport React, { useState, useEffect, useRef } from \"react\";\r\nimport AvatarEditor from \"react-avatar-editor\";\r\nimport RangeSlider from \"react-bootstrap-range-slider\";\r\nimport { Modal, Button, Row, Col } from \"react-bootstrap\";\r\n\r\nimport toast from 'react-hot-toast';\r\n\r\n//Import services/components\r\nimport apiService from \"../../services/apiService\";\r\nimport { useTranslation } from 'next-i18next';\r\n\r\nconst InstitutionImageEditor = ({\r\n  isOpen,\r\n  onModalClose,\r\n  image,\r\n  getId,\r\n  fileName,\r\n  getBlob,\r\n}: any) => {\r\n  const [scale, setScale] = useState<number>(1);\r\n  const [name, setName] = useState<string>(\"\");\r\n  const [img, setImg] = useState<any>(null);\r\n  const editorRef = useRef<any>(null);\r\n    const { t } = useTranslation('common');\r\n\r\n  useEffect(() => {\r\n    setName(fileName);\r\n  }, [fileName]);\r\n  const newLocal = \"Something wrong in server || your data!\";\r\n  const cropHandler = async () => {\r\n    /*****Helper Function to convert to blob******/\r\n    const dataURLtoBlob = (dataurl: string) => {\r\n      const arr = dataurl.split(\",\");\r\n      const  mime = arr[0].match(/:(.*?);/)?.[1];\r\n      const  bstr = atob(arr[1]);\r\n      let n = bstr.length;\r\n      const  u8arr = new Uint8Array(n);\r\n      while (n--) {\r\n        u8arr[n] = bstr.charCodeAt(n);\r\n      }\r\n      return new Blob([u8arr], { type: mime });\r\n    };\r\n    /*****End ********/\r\n    const canvas = editorRef.current.getImage().toDataURL(\"image/jpeg\", 0.6);\r\n    const blob = dataURLtoBlob(canvas);\r\n    const urlCreator = window.URL || window.webkitURL; //For Creating the url for preview\r\n    const blobUrl = urlCreator.createObjectURL(blob);\r\n    getBlob(blobUrl);\r\n\r\n    const fd = new FormData();\r\n    fd.append(\"file\", blob, name);\r\n\r\n    try {\r\n      const res = await apiService.post(\"/image\", fd, {\r\n        \"Content-Type\": \"multipart/form-data\",\r\n      });\r\n\r\n      if (res && res._id) {\r\n        getId(res._id);\r\n      }\r\n    } catch {\r\n      throw newLocal;\r\n    }\r\n    toast.success(t(\"toast.CroppedtheimageSuccessfully\"));\r\n    onModalClose(false);\r\n    setImg(null);\r\n    setName(\"none\");\r\n    setScale(1);\r\n  };\r\n\r\n  return (\r\n    <>\r\n      <div>\r\n        <Modal\r\n          show={isOpen}\r\n          size=\"lg\"\r\n          aria-labelledby=\"ProfileEdit\"\r\n          onHide={() => onModalClose(false)}\r\n          centered\r\n        >\r\n          <Modal.Body>\r\n            <div className=\"d-flex flex-column justify-content-center align-items-center imgRotate\">\r\n              <AvatarEditor\r\n                ref={editorRef}\r\n                width={700}\r\n                height={400}\r\n                borderRadius={2}\r\n                scale={scale}\r\n                color={[0, 0, 0, 0.6]}\r\n                image={img ? img : image}\r\n                style={{width: \"100%\",height: \"auto\"}}\r\n              />\r\n              <div className=\"info-identifier\">\r\n                <span>{t(\"ThisareawillcontainyourInstitutionandfocalpointinformation\")}</span>\r\n              </div>\r\n            </div>\r\n\r\n            <div className=\"mx-2 my-3\">\r\n              <Row>\r\n                <Col sm={1} md={1} lg={1} className=\"pe-0\">\r\n                  <b>{t(\"Zoom\")}</b>\r\n                </Col>\r\n                <Col sm={11} md={11} lg={11}>\r\n                  <RangeSlider\r\n                    value={scale}\r\n                    tooltip=\"auto\"\r\n                    min={1}\r\n                    max={10}\r\n                    step={0.01}\r\n                    variant=\"primary\"\r\n                    onChange={(changeEvent) =>\r\n                      setScale(Number(changeEvent.target.value))\r\n                    }\r\n                  />\r\n                </Col>\r\n              </Row>\r\n            </div>\r\n          </Modal.Body>\r\n          <Modal.Footer>\r\n            <Button onClick={cropHandler}>{t(\"Crop\")}</Button>\r\n            <Button variant=\"danger\" onClick={() => onModalClose(false)}>\r\n              {t(\"Cancel\")}\r\n            </Button>\r\n          </Modal.Footer>\r\n        </Modal>\r\n      </div>\r\n    </>\r\n  );\r\n};\r\n\r\nexport default InstitutionImageEditor;\r\n", "\n    (window.__NEXT_P = window.__NEXT_P || []).push([\n      \"/institution/InstitutionImageHandler\",\n      function () {\n        return require(\"private-next-pages/institution/InstitutionImageHandler.tsx\");\n      }\n    ]);\n    if(module.hot) {\n      module.hot.dispose(function () {\n        window.__NEXT_P.push([\"/institution/InstitutionImageHandler\"])\n      });\n    }\n  ", "//Import Library\r\nimport React, { useState, useMemo, useEffect } from \"react\";\r\nimport { FileError, useDropzone } from \"react-dropzone\";\r\nimport { FontAwesomeIcon } from \"@fortawesome/react-fontawesome\";\r\nimport { faTimesCircle, faExclamationCircle, faCloudUploadAlt } from \"@fortawesome/free-solid-svg-icons\";\r\n\r\nimport toast from 'react-hot-toast';\r\n\r\n//Import services/components\r\nimport InstitutionImageEditor from \"./InstitutionImageEditor\";\r\nimport apiService from \"../../services/apiService\";\r\nimport { useTranslation } from 'next-i18next';\r\n\r\n\r\nconst InstitutionImageCropper = ({ getId, header, type } : { getId: any, header: any, type: any }) => {\r\n    const { t } = useTranslation('common');\r\n    const [modal, setModal] = useState<boolean>(false);\r\n    const [files, setFiles]: any = useState([]);\r\n    const [imageId, setImageId] = useState(\"\");\r\n    const [thumbUrl, setThumbUrl] = useState<string | null>(\"\");\r\n        const endpoint = type === \"application\" ? \"/files\" : \"/image\";\r\n\r\n    /*Display the cropped image in edit */\r\n    useEffect(() => {\r\n        header ? setThumbUrl(`${process.env.API_SERVER}/image/show/${header}`) : setThumbUrl(null);\r\n    }, [header]);\r\n\r\n    /*End*/\r\n\r\n    /*Styles For the container*/\r\n    const baseStyle: any = {\r\n        flex: 1,\r\n        display: \"flex\",\r\n        flexDirection: \"column\",\r\n        alignItems: \"center\",\r\n        justifyContent: \"center\",\r\n        width: \"100%\",\r\n        height: \"100%\",\r\n        borderWidth: 0.1,\r\n        borderColor: \"#fafafa\",\r\n        backgroundColor: \"#fafafa\",\r\n        color: \"black\",\r\n        transition: \"border  .24s ease-in-out\",\r\n    };\r\n\r\n    const thumb: any = {\r\n        display: \"inline-flex\",\r\n        borderRadius: 2,\r\n        border: \"1px solid #ddd\",\r\n        marginBottom: 8,\r\n        marginRight: 20,\r\n        width: 170,\r\n        height: 100,\r\n        padding: 2,\r\n        position: \"relative\",\r\n        boxShadow: \"0 0 15px 0.25px rgba(0,0,0,0.25)\",\r\n        boxSizing: \"border-box\",\r\n    };\r\n\r\n    const thumbsContainer: any = {\r\n        display: \"flex\",\r\n        flexDirection: \"row\",\r\n        justifyContent: \"flex-start\",\r\n        flexWrap: \"wrap\",\r\n        marginTop: 20,\r\n    };\r\n\r\n    const thumbInner: any = {\r\n        display: \"flex\",\r\n    };\r\n\r\n    const icon: any = {\r\n        position: \"absolute\",\r\n        fontSize: \"22px\",\r\n        top: \"-10px\",\r\n        right: \"-10px\",\r\n        zIndex: 1000,\r\n        cursor: \"pointer\",\r\n        backgroundColor: \"#fff\",\r\n        color: \"#000\",\r\n        borderRadius: \"50%\",\r\n    };\r\n\r\n    const img = {\r\n        display: \"block\",\r\n        height: \"100%\",\r\n    };\r\n\r\n    const activeStyle: any = {\r\n        borderColor: \"#2196f3\",\r\n    };\r\n    /*End of Styles*/\r\n\r\n    /**Handle Modal Close**/\r\n    const modalClose = (val: boolean) => {\r\n        setModal(val);\r\n    };\r\n    /*End*/\r\n\r\n    /*For Handle the dropFiles*/\r\n    const onDrop = (file: any) => {\r\n        const accFiles = file.map((file: any, i: any) =>\r\n            Object.assign(file, {\r\n                preview: URL.createObjectURL(file),\r\n            })\r\n        );\r\n        setFiles(accFiles);\r\n        if (file.length > 0) {\r\n            setModal(true);\r\n        }\r\n    };\r\n    /*End*/\r\n\r\n    /*Setting the intial accept type & size e.t.c */\r\n    const { getRootProps, getInputProps, isDragActive, isDragAccept, isDragReject, fileRejections } = useDropzone({\r\n        noClick: false,\r\n        accept: \"image/*\",\r\n        multiple: false,\r\n        minSize: 0,\r\n        maxSize: 2000000,\r\n        onDrop,\r\n        validator: nameLengthValidator,\r\n    });\r\n    /*End*/\r\n\r\n    /*Styles for drag & drop Container*/\r\n    const style = useMemo(\r\n        () => ({\r\n            ...baseStyle,\r\n            ...(isDragActive ? activeStyle : { outline: \"2px dashed #bbb\" }),\r\n            ...(isDragAccept ? { outline: \"2px dashed #595959\" } : { outline: \"2px dashed #bbb\" }),\r\n            ...(isDragReject ? { outline: \"2px dashed red\" } : { activeStyle }),\r\n        }),\r\n        [isDragActive, isDragReject]\r\n    );\r\n    /*End*/\r\n\r\n    /*Reject the file If length is greater than 2mb*/\r\n    const isFileTooLarge = fileRejections.length > 0 && fileRejections[0].file.size > 2000000;\r\n    /*End*/\r\n\r\n    /*Get Id crop using callBack Function*/\r\n    const getIdHandler = (id: any) => {\r\n        setImageId(id);\r\n        getId(id);\r\n    };\r\n    /*End*/\r\n\r\n    /*Remove File Handler*/\r\n    const removeFile = async () => {\r\n        let res;\r\n        if (header) {\r\n            res = await apiService.remove(`image/${header}`);\r\n        } else {\r\n            res = await apiService.remove(`image/${imageId}`);\r\n        }\r\n\r\n        if (res && res._id) {\r\n            setThumbUrl(null);\r\n            getIdHandler(null);\r\n        }\r\n    };\r\n    /*End*/\r\n\r\n    //***Get Blob from the react avaatr editor for preview*/\r\n    const blobHandler = (url: any) => {\r\n        setThumbUrl(url);\r\n    };\r\n    /*End*/\r\n\r\n    function nameLengthValidator(file: any) {\r\n        if (endpoint === \"/image\") {\r\n            if (file.type.substring(0, 5) === \"image\") {\r\n                return null;\r\n            } else {\r\n                toast.error(t(\"toast.filetypenotsupport\"));\r\n            }\r\n        } else if (endpoint === \"/files\") {\r\n            if (!(file.type.substring(0, 5) !== \"image\")) {\r\n                toast.error(t(\"toast.filetypenotsupport\"));\r\n            }\r\n        } else {\r\n            return null;\r\n        }\r\n        \r\n        return null;\r\n    }\r\n\r\n    return (\r\n        <>\r\n            <div\r\n                className=\" d-flex justify-content-center align-items-center mt-3\"\r\n                style={{ width: \"100%\", height: \"180px\" }}\r\n            >\r\n                <div {...getRootProps({ style })}>\r\n                    <input {...getInputProps()} />\r\n                    <FontAwesomeIcon icon={faCloudUploadAlt} size=\"4x\" color=\"#999\" />\r\n                    <p style={{ color: \"#595959\", marginBottom: \"0px\" }}>\r\n                        {t(\"Drag'n'dropsomefileshere,orclicktoselectfiles\")}\r\n                    </p>\r\n                    <small style={{ color: \"#595959\" }}>\r\n                        <b>{t(\"Note:\")}</b> {t(\"Onesingleimagewillbeaccepted\")}\r\n                    </small>\r\n                    {isFileTooLarge && (\r\n                        <small className=\"text-danger mt-2\">\r\n                            <FontAwesomeIcon icon={faExclamationCircle} size=\"1x\" color=\"red\" />\r\n                            &nbsp;{t(\"FileistoolargeItshouldbelessthan2MB\")}\r\n                        </small>\r\n                    )}\r\n                    {isDragReject && (\r\n                        <small className=\"text-danger\" style={{ color: \"red\" }}>\r\n                            <FontAwesomeIcon icon={faExclamationCircle} size=\"1x\" color=\"red\" />\r\n                            {t(\"Filetypenotacceptedsorr\")}\r\n                        </small>\r\n                    )}\r\n                </div>\r\n            </div>\r\n            {thumbUrl && (\r\n                <>\r\n                    <div style={thumbsContainer}>\r\n                        <div style={thumb}>\r\n                            <div style={thumbInner}>\r\n                                <img src={thumbUrl} style={img} />\r\n                            </div>\r\n                            <FontAwesomeIcon icon={faTimesCircle} style={icon} color=\"black\" onClick={removeFile} />\r\n                        </div>\r\n                    </div>\r\n                </>\r\n            )}\r\n\r\n            <InstitutionImageEditor\r\n                isOpen={modal}\r\n                getId={(id: any) => getIdHandler(id)}\r\n                image={files && files[0] ? files[0].preview : \"\"}\r\n                onModalClose={(val: any) => modalClose(val)}\r\n                fileName={files && files[0] ? files[0].name : \"\"}\r\n                getBlob={(url: any) => blobHandler(url)}\r\n            />\r\n        </>\r\n    );\r\n};\r\n\r\nexport default InstitutionImageCropper;\r\n"], "names": ["isOpen", "InstitutionImageEditor", "onModalClose", "image", "getId", "fileName", "getBlob", "scale", "setScale", "useState", "name", "setName", "img", "setImg", "editor<PERSON><PERSON>", "useRef", "t", "useTranslation", "useEffect", "<PERSON><PERSON><PERSON><PERSON>", "blob", "dataURLtoBlob", "arr", "canvas", "dataurl", "split", "mime", "match", "bstr", "atob", "n", "length", "u8arr", "Uint8Array", "charCodeAt", "Blob", "type", "current", "getImage", "toDataURL", "urlCreator", "window", "blobUrl", "URL", "webkitURL", "createObjectURL", "fd", "FormData", "append", "res", "apiService", "post", "_id", "newLocal", "toast", "success", "div", "Modal", "show", "size", "aria-<PERSON>by", "onHide", "centered", "Body", "className", "AvatarEditor", "ref", "width", "height", "borderRadius", "color", "style", "span", "Row", "Col", "sm", "md", "lg", "b", "RangeSlider", "value", "tooltip", "min", "max", "step", "variant", "changeEvent", "Number", "target", "Footer", "<PERSON><PERSON>", "onClick", "header", "InstitutionImageCropper", "modal", "setModal", "files", "setFiles", "imageId", "setImageId", "thumbUrl", "setThumbUrl", "endpoint", "process", "baseStyle", "flex", "display", "flexDirection", "alignItems", "justifyContent", "borderWidth", "borderColor", "backgroundColor", "transition", "activeStyle", "modalClose", "val", "getRootProps", "getInputProps", "isDragActive", "isDragAccept", "isDragReject", "fileRejections", "useDropzone", "noClick", "accept", "multiple", "minSize", "maxSize", "onDrop", "file", "map", "i", "Object", "assign", "preview", "validator", "nameLengthValidator", "substring", "error", "useMemo", "outline", "isFileTooLarge", "getIdHandler", "id", "removeFile", "remove", "b<PERSON><PERSON><PERSON><PERSON><PERSON>", "url", "input", "FontAwesomeIcon", "icon", "faCloudUploadAlt", "p", "marginBottom", "small", "faExclamationCircle", "thumbsContainer", "flexWrap", "marginTop", "thumb", "border", "marginRight", "padding", "position", "boxShadow", "boxSizing", "src", "faTimesCircle", "fontSize", "top", "right", "zIndex", "cursor"], "sourceRoot": "", "ignoreList": []}