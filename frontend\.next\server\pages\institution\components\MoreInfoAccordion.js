"use strict";(()=>{var e={};e.id=9395,e.ids=[636,3220,9395],e.modules={123:e=>{e.exports=require("dom-helpers/removeEventListener")},1332:e=>{e.exports=require("react-custom-scrollbars-2")},1428:e=>{e.exports=import("axios")},1680:e=>{e.exports=require("@restart/ui/utils")},1919:e=>{e.exports=require("react-transition-group/Transition")},3892:e=>{e.exports=require("classnames")},4048:e=>{e.exports=require("react-truncate")},6009:e=>{e.exports=require("dom-helpers/ownerDocument")},6952:e=>{e.exports=require("@restart/ui/Modal")},7374:e=>{e.exports=require("@restart/ui/Dropdown")},8732:e=>{e.exports=require("react/jsx-runtime")},9653:e=>{e.exports=require("@restart/ui/Overlay")},11242:e=>{e.exports=require("redux-persist/integration/react")},11688:e=>{e.exports=require("@restart/hooks/useWillUnmount")},12388:e=>{e.exports=require("dom-helpers/addEventListener")},13364:e=>{e.exports=require("redux-saga/effects")},14062:e=>{e.exports=import("react-redux")},14078:e=>{e.exports=import("swr")},14332:e=>{e.exports=require("uncontrollable")},16116:e=>{e.exports=require("invariant")},18622:e=>{e.exports=require("yup")},19121:e=>{e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},22326:e=>{e.exports=require("react-dom")},22541:e=>{e.exports=require("redux-persist/lib/storage")},25303:e=>{e.exports=require("@restart/ui/NavItem")},26324:e=>{e.exports=require("warning")},27825:e=>{e.exports=require("lodash")},27910:e=>{e.exports=require("stream")},28217:e=>{e.exports=require("@restart/ui/DropdownItem")},28927:(e,r,t)=>{t.a(e,async(e,s)=>{try{t.r(r),t.d(r,{config:()=>m,default:()=>d,getServerSideProps:()=>q,getStaticPaths:()=>h,getStaticProps:()=>c,reportWebVitals:()=>j,routeModule:()=>A,unstable_getServerProps:()=>b,unstable_getServerSideProps:()=>v,unstable_getStaticParams:()=>f,unstable_getStaticPaths:()=>P,unstable_getStaticProps:()=>g});var o=t(63885),i=t(80237),n=t(81413),p=t(9616),a=t.n(p),u=t(72386),x=t(78959),l=e([u]);u=(l.then?(await l)():l)[0];let d=(0,n.M)(x,"default"),c=(0,n.M)(x,"getStaticProps"),h=(0,n.M)(x,"getStaticPaths"),q=(0,n.M)(x,"getServerSideProps"),m=(0,n.M)(x,"config"),j=(0,n.M)(x,"reportWebVitals"),g=(0,n.M)(x,"unstable_getStaticProps"),P=(0,n.M)(x,"unstable_getStaticPaths"),f=(0,n.M)(x,"unstable_getStaticParams"),b=(0,n.M)(x,"unstable_getServerProps"),v=(0,n.M)(x,"unstable_getServerSideProps"),A=new o.PagesRouteModule({definition:{kind:i.A.PAGES,page:"/institution/components/MoreInfoAccordion",pathname:"/institution/components/MoreInfoAccordion",bundlePath:"",filename:""},components:{App:u.default,Document:a()},userland:x});s()}catch(e){s(e)}})},29021:e=>{e.exports=require("fs")},29294:e=>{e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},29628:e=>{e.exports=require("dom-helpers/addClass")},29825:e=>{e.exports=require("prop-types")},29841:e=>{e.exports=require("dom-helpers/hasClass")},30362:e=>{e.exports=require("@restart/ui/DropdownToggle")},33873:e=>{e.exports=require("path")},36653:e=>{e.exports=require("nprogress")},36955:e=>{e.exports=require("@restart/hooks/useBreakpoint")},37766:e=>{e.exports=require("@restart/hooks/useMergedRefs")},39756:e=>{e.exports=import("redux")},40051:e=>{e.exports=require("dom-helpers/removeClass")},40361:e=>{e.exports=require("next/dist/compiled/next-server/pages.runtime.prod.js")},42893:e=>{e.exports=import("react-hot-toast")},43294:e=>{e.exports=require("formik")},50009:e=>{e.exports=require("@restart/ui/DropdownMenu")},57664:e=>{e.exports=require("dom-helpers/scrollbarSize")},58928:e=>{e.exports=require("dom-helpers/canUseDOM")},59717:e=>{e.exports=require("@restart/ui/DropdownContext")},60560:e=>{e.exports=require("dom-helpers/contains")},63033:e=>{e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},65447:e=>{e.exports=require("@restart/ui/Button")},67364:e=>{e.exports=require("@restart/hooks/useCallbackRef")},68455:e=>{e.exports=import("redux-saga")},69722:e=>{e.exports=require("es6-promise")},74075:e=>{e.exports=require("zlib")},74716:e=>{e.exports=require("moment")},74987:e=>{e.exports=require("@restart/ui/ModalManager")},78097:e=>{e.exports=require("next-redux-saga")},78634:e=>{e.exports=require("@restart/ui/Anchor")},78959:(e,r,t)=>{t.r(r),t.d(r,{default:()=>a});var s=t(8732);t(82015);var o=t(93024),i=t(19918),n=t.n(i),p=t(88751);let a=e=>{let{t:r}=(0,p.useTranslation)("common"),{institutionData:t,activeOperations:i,activeProjects:a}=e;return(0,s.jsx)(o.A,{defaultActiveKey:"0",children:(0,s.jsxs)(o.A.Item,{eventKey:"0",children:[(0,s.jsx)(o.A.Header,{children:(0,s.jsx)("div",{className:"cardTitle",children:r("MoreInfo")})}),(0,s.jsxs)(o.A.Body,{className:"institutionDetails ps-4",children:[(0,s.jsxs)("p",{children:[(0,s.jsx)("b",{children:r("OrganisationType")}),":",(0,s.jsxs)("span",{children:[" ",t.type?t.type.title:""]})]}),(0,s.jsxs)("p",{children:[(0,s.jsx)("b",{children:r("Network")}),":",(0,s.jsx)("span",{children:t.networks?t.networks.map((e,r)=>(0,s.jsx)("span",{children:(0,s.jsx)("li",{children:e.title})},r)):""})]}),(0,s.jsxs)("p",{children:[(0,s.jsx)("b",{children:r("ActiveOperation")}),":",(0,s.jsx)("span",{children:i&&i.length>0?i.map((e,r)=>(0,s.jsx)("li",{children:(0,s.jsx)(n(),{href:"/operation/[...routes]",as:`/operation/show/${e._id}`,children:e.title})},r)):(0,s.jsx)("li",{children:r("NoActiveoperationsfound")})})]}),(0,s.jsxs)("p",{children:[(0,s.jsx)("b",{children:r("Expertise")}),":",(0,s.jsxs)("span",{children:[" ",t.expertise?t.expertise.map((e,r)=>(0,s.jsxs)("li",{children:[e.title," ",(0,s.jsx)("br",{})]},r)):""]})]}),(0,s.jsxs)("p",{children:[(0,s.jsx)("b",{children:r("ActiveProject")}),":",(0,s.jsx)("span",{children:a&&a.length>0?a.map((e,r)=>(0,s.jsx)("li",{children:(0,s.jsx)(n(),{href:"/project/[...routes]",as:`/project/show/${e._id}`,children:e.title})},r)):(0,s.jsx)("li",{children:r("NoActiveprojectsfound")})})]}),(0,s.jsxs)("p",{children:[(0,s.jsx)("b",{children:r("Department")}),":",(0,s.jsx)("span",{children:t&&t.department?t.department:r("Nodepartmentfound")})]}),(0,s.jsxs)("p",{children:[(0,s.jsx)("b",{children:r("Unit")}),":",(0,s.jsx)("span",{children:t&&t.unit?t.unit:r("Nounitfound")})]})]})]})})}},80237:(e,r)=>{Object.defineProperty(r,"A",{enumerable:!0,get:function(){return t}});var t=function(e){return e.PAGES="PAGES",e.PAGES_API="PAGES_API",e.APP_PAGE="APP_PAGE",e.APP_ROUTE="APP_ROUTE",e.IMAGE="IMAGE",e}({})},81366:e=>{e.exports=require("redux-auth-wrapper/connectedAuthWrapper")},81413:(e,r)=>{Object.defineProperty(r,"M",{enumerable:!0,get:function(){return function e(r,t){return t in r?r[t]:"then"in r&&"function"==typeof r.then?r.then(r=>e(r,t)):"function"==typeof r&&"default"===t?r:void 0}}})},81521:e=>{e.exports=require("dom-helpers/querySelectorAll")},81895:e=>{e.exports=require("@restart/hooks/useEventCallback")},82015:e=>{e.exports=require("react")},86842:e=>{e.exports=require("@restart/ui/SelectableContext")},87571:e=>{e.exports=require("dom-helpers/transitionEnd")},88751:e=>{e.exports=require("next-i18next")},93787:e=>{e.exports=require("redux-persist")},94947:e=>{e.exports=require("@restart/hooks/useTimeout")},96196:e=>{e.exports=require("next-redux-wrapper")},98320:e=>{e.exports=require("@restart/hooks/useIsomorphicEffect")},99460:e=>{e.exports=require("dom-helpers/css")}};var r=require("../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[6089,9216,9616,2386],()=>t(28927));module.exports=s})();