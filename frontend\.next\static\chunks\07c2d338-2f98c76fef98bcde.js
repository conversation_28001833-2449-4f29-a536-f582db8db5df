"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[6701],{33144:(e,t,n)=>{n.d(t,{ie:()=>C,we:()=>B});var l,r=n(14232),o=n(62757),u=n(37876),s=n(84974);n(98477);var i=n(94882);let a="active",c="selected",f="ArrowLeft",d="ArrowRight",g="ArrowUp",h="ArrowDown",m={...l||(l=n.t(r,2))},v=!1,p=0,x=()=>"floating-ui-"+Math.random().toString(36).slice(2,6)+p++,w=m.useId||function(){let[e,t]=r.useState(()=>v?x():void 0);return(0,o.OS)(()=>{null==e&&t(x())},[]),r.useEffect(()=>{v=!0},[]),e},C=r.forwardRef(function(e,t){let{context:{placement:n,elements:{floating:l},middlewareData:{arrow:i,shift:a}},width:c=14,height:f=7,tipRadius:d=0,strokeWidth:g=0,staticOffset:h,stroke:m,d:v,style:{transform:p,...x}={},...C}=e,R=w(),[S,M]=r.useState(!1);if((0,o.OS)(()=>{l&&"rtl"===(0,s.L9)(l).direction&&M(!0)},[l]),!l)return null;let[k,b]=n.split("-"),y="top"===k||"bottom"===k,E=h;(y&&null!=a&&a.x||!y&&null!=a&&a.y)&&(E=null);let j=2*g,O=j/2,q=c/2*(-(d/8)+1),A=f/2*d/4,I=!!v,_=E&&"end"===b?"bottom":"top",B=E&&"end"===b?"right":"left";E&&S&&(B="end"===b?"left":"right");let D=(null==i?void 0:i.x)!=null?E||i.x:"",L=(null==i?void 0:i.y)!=null?E||i.y:"",N=v||"M0,0 H"+c+(" L"+(c-q))+","+(f-A)+(" Q"+c/2+","+f+" "+q)+","+(f-A)+" Z",P={top:I?"rotate(180deg)":"",left:I?"rotate(90deg)":"rotate(-90deg)",bottom:I?"":"rotate(180deg)",right:I?"rotate(-90deg)":"rotate(90deg)"}[k];return(0,u.jsxs)("svg",{...C,"aria-hidden":!0,ref:t,width:I?c:c+j,height:c,viewBox:"0 0 "+c+" "+(f>c?f:c),style:{position:"absolute",pointerEvents:"none",[B]:D,[_]:L,[k]:y||I?"100%":"calc(100% - "+j/2+"px)",transform:[P,p].filter(e=>!!e).join(" "),...x},children:[j>0&&(0,u.jsx)("path",{clipPath:"url(#"+R+")",fill:"none",stroke:m,strokeWidth:j+ +!v,d:N}),(0,u.jsx)("path",{stroke:j&&!v?C.fill:"none",d:N}),(0,u.jsx)("clipPath",{id:R,children:(0,u.jsx)("rect",{x:-O,y:O*(I?-1:1),width:c+j,height:c})})]})}),R=r.createContext(null),S=r.createContext(null),M=()=>{var e;return(null==(e=r.useContext(R))?void 0:e.id)||null},k=()=>r.useContext(S),b=()=>{},y=r.createContext({delay:0,initialDelay:0,timeoutMs:0,currentId:null,setCurrentId:b,setState:b,isInstantPhase:!1});let E=0,j=new WeakMap,O=new WeakSet,q={},A=0,I=e=>e&&(e.host||I(e.parentNode)),_=(e,t)=>t.map(t=>{if(e.contains(t))return t;let n=I(t);return e.contains(n)?n:null}).filter(e=>null!=e);function B(e){void 0===e&&(e={});let{nodeId:t}=e,n=function(e){let{open:t=!1,onOpenChange:n,elements:l}=e,u=w(),s=r.useRef({}),[i]=r.useState(()=>(function(){let e=new Map;return{emit(t,n){var l;null==(l=e.get(t))||l.forEach(e=>e(n))},on(t,n){e.has(t)||e.set(t,new Set),e.get(t).add(n)},off(t,n){var l;null==(l=e.get(t))||l.delete(n)}}})()),a=null!=M(),[c,f]=r.useState(l.reference),d=(0,o.Jt)((e,t,l)=>{s.current.openEvent=e?t:void 0,i.emit("openchange",{open:e,event:t,reason:l,nested:a}),null==n||n(e,t,l)}),g=r.useMemo(()=>({setPositionReference:f}),[]),h=r.useMemo(()=>({reference:c||l.reference||null,floating:l.floating||null,domReference:l.reference}),[c,l.reference,l.floating]);return r.useMemo(()=>({dataRef:s,open:t,onOpenChange:d,elements:h,events:i,floatingId:u,refs:g}),[t,d,h,i,u,g])}({...e,elements:{reference:null,floating:null,...e.elements}}),l=e.rootContext||n,u=l.elements,[a,c]=r.useState(null),[f,d]=r.useState(null),g=(null==u?void 0:u.domReference)||a,h=r.useRef(null),m=k();(0,o.OS)(()=>{g&&(h.current=g)},[g]);let v=(0,i.we)({...e,elements:{...u,...f&&{reference:f}}}),p=r.useCallback(e=>{let t=(0,s.vq)(e)?{getBoundingClientRect:()=>e.getBoundingClientRect(),getClientRects:()=>e.getClientRects(),contextElement:e}:e;d(t),v.refs.setReference(t)},[v.refs]),x=r.useCallback(e=>{((0,s.vq)(e)||null===e)&&(h.current=e,c(e)),((0,s.vq)(v.refs.reference.current)||null===v.refs.reference.current||null!==e&&!(0,s.vq)(e))&&v.refs.setReference(e)},[v.refs]),C=r.useMemo(()=>({...v.refs,setReference:x,setPositionReference:p,domReference:h}),[v.refs,x,p]),R=r.useMemo(()=>({...v.elements,domReference:g}),[v.elements,g]),S=r.useMemo(()=>({...v,...l,refs:C,elements:R,nodeId:t}),[v,C,R,t,l]);return(0,o.OS)(()=>{l.dataRef.current.floatingContext=S;let e=null==m?void 0:m.nodesRef.current.find(e=>e.id===t);e&&(e.context=S)}),r.useMemo(()=>({...v,context:S,refs:C,elements:R}),[v,C,R,S])}function D(e,t,n){switch(e){case"vertical":return t;case"horizontal":return n;default:return t||n}}}}]);
//# sourceMappingURL=07c2d338-2f98c76fef98bcde.js.map