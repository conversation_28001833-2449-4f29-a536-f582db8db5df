"use strict";exports.id=5645,exports.ids=[5645],exports.modules={9329:(e,t,n)=>{n.a(e,async(e,a)=>{try{n.r(t),n.d(t,{default:()=>u});var s=n(8732),i=n(82015),r=n(93024),o=n(82053),l=n(54131),c=n(88751),d=n(42447),p=e([l,d]);[l,d]=p.then?(await p)():p;let u=e=>{let{t}=(0,c.useTranslation)("common"),[n,a]=(0,i.useState)(!1);return(0,s.jsx)(s.Fragment,{children:(0,s.jsxs)(r.A.Item,{eventKey:"0",children:[(0,s.jsxs)(r.<PERSON><PERSON>,{onClick:()=>a(!n),children:[(0,s.jsx)("div",{className:"cardTitle",children:t("MediaGallery")}),(0,s.jsx)("div",{className:"cardArrow",children:n?(0,s.jsx)(o.FontAwesomeIcon,{icon:l.faMinus,color:"#fff"}):(0,s.jsx)(o.FontAwesomeIcon,{icon:l.faPlus,color:"#fff"})})]}),(0,s.jsx)(r.A.Body,{children:(0,s.jsx)(d.A,{gallery:e.operation.images,imageSource:e.operation.images_src})})]})})};a()}catch(e){a(e)}})},24292:(e,t,n)=>{n.a(e,async(e,a)=>{try{n.r(t),n.d(t,{default:()=>x});var s=n(8732),i=n(82015),r=n(54131),o=n(82053),l=n(74716),c=n.n(l),d=n(93024),p=n(83551),u=n(49481),m=n(88751),h=e([r]);r=(h.then?(await h)():h)[0];let x=e=>{let{t}=(0,m.useTranslation)("common"),n="MM-D-YYYY HH:mm:ss",a="MM-D-YYYY",[l,h]=(0,i.useState)(!1);return(0,s.jsx)(s.Fragment,{children:(0,s.jsxs)(d.A.Item,{eventKey:"0",children:[(0,s.jsxs)(d.A.Header,{onClick:()=>h(!l),children:[(0,s.jsx)("div",{className:"cardTitle",children:t("OperationDetails")}),(0,s.jsx)("div",{className:"cardArrow",children:l?(0,s.jsx)(o.FontAwesomeIcon,{icon:r.faMinus,color:"#fff"}):(0,s.jsx)(o.FontAwesomeIcon,{icon:r.faPlus,color:"#fff"})})]}),(0,s.jsx)(d.A.Body,{children:(0,s.jsxs)(p.A,{className:"operationData",children:[(0,s.jsxs)(u.A,{md:!0,lg:6,sm:12,className:"ps-0",children:[(0,s.jsxs)("p",{children:[(0,s.jsx)("b",{children:t("HazardType")}),":",(0,s.jsx)("span",{children:e.operation.hazard_type?e.operation.hazard_type.title:null})]}),function(e,t){return(0,s.jsxs)("div",{className:"d-flex mb-2 pb-1",children:[(0,s.jsx)("b",{children:e("Hazard")}),":",(0,s.jsx)("span",{children:(0,s.jsx)("ul",{className:"comma-separated",children:t.hazard&&t.hazard.length>=1?t.hazard.map((e,t)=>(0,s.jsx)("li",{children:e.title.en},t)):null})})]})}(t,e.operation),(0,s.jsxs)("p",{children:[(0,s.jsx)("b",{children:t("Syndrome")}),":",(0,s.jsx)("span",{children:e.operation.syndrome?e.operation.syndrome.title:null})]}),(0,s.jsxs)("p",{children:[(0,s.jsx)("b",{children:t("Created")}),":",(0,s.jsx)("span",{children:c()(e.operation.created_at).format(n)})]}),(0,s.jsxs)("p",{children:[(0,s.jsx)("b",{children:t("LastModified")}),":",(0,s.jsx)("span",{children:c()(e.operation.updated_at).format(n)})]})]}),(0,s.jsxs)(u.A,{md:!0,lg:6,sm:12,className:"ps-0",children:[(0,s.jsxs)("p",{children:[(0,s.jsx)("b",{children:t("CountryOrTerritory")}),":",(0,s.jsx)("span",{children:e.operation.country?e.operation.country.title:null})]}),(0,s.jsxs)("p",{children:[(0,s.jsx)("b",{children:t("OperationStatus")}),":",(0,s.jsxs)("span",{children:[" ",e.operation.status.title," "]})]}),(0,s.jsxs)("p",{children:[(0,s.jsx)("b",{children:t("StartDate")}),":",(0,s.jsx)("span",{children:e.operation.start_date?c()(e.operation.start_date).format(a):null})]}),(0,s.jsxs)("p",{children:[(0,s.jsx)("b",{children:t("EndDate")}),":",(0,s.jsx)("span",{children:e.operation.end_date?c()(e.operation.end_date).format(a):null})]})]})]})})]})})};a()}catch(e){a(e)}})},28163:(e,t,n)=>{n.r(t),n.d(t,{canAddOperation:()=>o,canAddOperationForm:()=>l,canEditOperation:()=>c,canEditOperationForm:()=>d,canViewDiscussionUpdate:()=>p,default:()=>u});var a=n(8732);n(82015);var s=n(81366),i=n.n(s),r=n(61421);let o=i()({authenticatedSelector:e=>!!e.permissions&&!!e.permissions.operation&&!!e.permissions.operation["create:any"],wrapperDisplayName:"CanAddOperation"}),l=i()({authenticatedSelector:e=>!!e.permissions&&!!e.permissions.operation&&!!e.permissions.operation["create:any"],wrapperDisplayName:"CanAddOperationForm",FailureComponent:()=>(0,a.jsx)(r.default,{})}),c=i()({authenticatedSelector:(e,t)=>{if(e.permissions&&e.permissions.operation){if(e.permissions.operation["update:any"])return!0;else if(e.permissions.operation["update:own"]&&t.operation&&t.operation.user&&t.operation.user._id===e.user._id)return!0}return!1},wrapperDisplayName:"CanEditOperation"}),d=i()({authenticatedSelector:(e,t)=>{if(e.permissions&&e.permissions.operation){if(e.permissions.operation["update:any"])return!0;else if(e.permissions.operation["update:own"]&&t.operation&&t.operation.user&&t.operation.user._id===e.user._id)return!0}return!1},wrapperDisplayName:"CanEditOperationForm",FailureComponent:()=>(0,a.jsx)(r.default,{})}),p=i()({authenticatedSelector:e=>!!e.permissions&&!!e.permissions.update&&!!e.permissions.update["read:any"],wrapperDisplayName:"CanViewDiscussionUpdate"}),u=o},38058:(e,t,n)=>{n.a(e,async(e,a)=>{try{n.d(t,{A:()=>u});var s=n(8732),i=n(82015),r=n(19918),o=n.n(r),l=n(56084),c=n(63487),d=n(88751),p=e([c]);c=(p.then?(await p)():p)[0];let u=e=>{let{t}=(0,d.useTranslation)("common"),{type:n,id:a}=e,[r,p]=(0,i.useState)([]),[u,m]=(0,i.useState)(!1),[h,x]=(0,i.useState)(0),[j,f]=(0,i.useState)(10),[A]=(0,i.useState)(!1),y={sort:{created_at:"asc"},limit:j,page:1,query:{}},g=[{name:t("Title"),selector:"title",cell:e=>e&&e.title&&e._id?(0,s.jsx)(o(),{href:"/vspace/[...routes]",as:`/vspace/show/${e._id}`,children:e.title}):""},{name:t("Owner"),selector:"users",cell:e=>e&&e.user&&e.user.firstname?`${e.user.firstname} ${e.user.lastname}`:""},{name:t("PublicPrivate"),selector:"visibility",cell:e=>e&&e.visibility?"Public":"Private"},{name:t("NumberofMembers"),selector:"members",cell:e=>e&&e.members?e.members.length:"-"}],w=async e=>{m(!0);let t=await c.A.get(`stats/get${n}WithVspace/${a}`,y);t&&("Operation"===n?p(t.operation):p(t.project),x(t.totalCount),m(!1))},v=async(e,t)=>{y.limit=e,y.page=t,m(!0);let s=await c.A.get(`stats/get${n}WithVspace/${a}`,y);s&&("Operation"===n?p(s.operation):p(s.project),f(e),m(!1))};return(0,i.useEffect)(()=>{w(y)},[]),(0,s.jsx)("div",{children:(0,s.jsx)(l.A,{columns:g,data:r,totalRows:h,loading:u,resetPaginationToggle:A,handlePerRowsChange:v,handlePageChange:e=>{y.limit=j,y.page=e,w(y)}})})};a()}catch(e){a(e)}})},56084:(e,t,n)=>{n.d(t,{A:()=>c});var a=n(8732);n(82015);var s=n(38609),i=n.n(s),r=n(88751),o=n(30370);function l(e){let{t}=(0,r.useTranslation)("common"),n={rowsPerPageText:t("Rowsperpage")},{columns:s,data:l,totalRows:c,resetPaginationToggle:d,subheader:p,subHeaderComponent:u,handlePerRowsChange:m,handlePageChange:h,rowsPerPage:x,defaultRowsPerPage:j,selectableRows:f,loading:A,pagServer:y,onSelectedRowsChange:g,clearSelectedRows:w,sortServer:v,onSort:D,persistTableHead:b,sortFunction:P,...N}=e,S={paginationComponentOptions:n,noDataComponent:t("NoData"),noHeader:!0,columns:s,data:l||[],dense:!0,paginationResetDefaultPage:d,subHeader:p,progressPending:A,subHeaderComponent:u,pagination:!0,paginationServer:y,paginationPerPage:j||10,paginationRowsPerPageOptions:x||[10,15,20,25,30],paginationTotalRows:c,onChangeRowsPerPage:m,onChangePage:h,selectableRows:f,onSelectedRowsChange:g,clearSelectedRows:w,progressComponent:(0,a.jsx)(o.A,{}),sortIcon:(0,a.jsx)("i",{className:"sort-icon fas fa-exchange-alt"}),sortServer:v,onSort:D,sortFunction:P,persistTableHead:b,className:"rki-table"};return(0,a.jsx)(i(),{...S})}l.defaultProps={subHeader:!1,pagination:!0,totalRows:null,pagServer:!0,onSelectedRowsChange:null,clearSelectedRows:!1,sortServer:!1,persistTableHead:!1};let c=l},56670:(e,t,n)=>{n.a(e,async(e,a)=>{try{n.r(t),n.d(t,{default:()=>u});var s=n(8732),i=n(54131),r=n(82053),o=n(82015),l=n(93024),c=n(56678),d=n(88751),p=e([i]);i=(p.then?(await p)():p)[0];let u=e=>{let{t}=(0,d.useTranslation)("common"),[n,a]=(0,o.useState)(!1);return(0,s.jsx)(s.Fragment,{children:(0,s.jsxs)(l.A.Item,{eventKey:"0",children:[(0,s.jsxs)(l.A.Header,{onClick:()=>a(!n),children:[(0,s.jsx)("div",{className:"cardTitle",children:t("Partners")}),(0,s.jsx)("div",{className:"cardArrow",children:n?(0,s.jsx)(r.FontAwesomeIcon,{icon:i.faMinus,color:"#fff"}):(0,s.jsx)(r.FontAwesomeIcon,{icon:i.faPlus,color:"#fff"})})]}),(0,s.jsx)(l.A.Body,{children:(0,s.jsx)(c.default,{partners:e.operation.partners})})]})})};a()}catch(e){a(e)}})},56678:(e,t,n)=>{n.r(t),n.d(t,{default:()=>h});var a=n(8732),s=n(19918),i=n.n(s),r=n(27825),o=n.n(r),l=n(81181),c=n(63241),d=n(56084),p=n(88751);let u=({networks:e})=>e&&e.length>0?(0,a.jsx)("ul",{children:e.map((e,t)=>(0,a.jsx)("li",{children:e.title},t))}):null,m=(0,a.jsxs)(l.A,{id:"popover-basic",children:[(0,a.jsx)(l.A.Header,{as:"h3",className:"text-center",children:"NETWORKS"}),(0,a.jsx)(l.A.Body,{children:(0,a.jsxs)("div",{className:"m-2",children:[(0,a.jsxs)("p",{children:[(0,a.jsx)("b",{children:"EMLab"})," - European Mobile Lab"]}),(0,a.jsxs)("p",{children:[(0,a.jsx)("b",{children:"EMT"})," - Emergency Medical Teams"]}),(0,a.jsxs)("p",{children:[(0,a.jsx)("b",{children:"GHPP"})," - Global Health Protection Program"]}),(0,a.jsxs)("p",{children:[(0,a.jsx)("b",{children:"GOARN"})," - Global Outbreak Alert & Response Network"]}),(0,a.jsxs)("p",{children:[(0,a.jsx)("b",{children:"IANPHI"})," - International Association of National Public Health Institutes"]}),(0,a.jsxs)("p",{children:[(0,a.jsx)("b",{children:"STAKOB"})," - St\xe4ndiger Arbeitskreis der Kompetenz-und Behandlungszentren"]}),(0,a.jsxs)("p",{children:[(0,a.jsx)("b",{children:"WHOCC"}),"- World Health Organization Collaborating Centres"]})]})})]}),h=function(e){let{t}=(0,p.useTranslation)("common"),{partners:n}=e,s=[{name:t("Organisation"),selector:"title",cell:e=>e&&e.institution?(0,a.jsx)(i(),{href:"/institution/[...routes]",as:`/institution/show/${e.institution._id}`,children:e.institution.title}):"",sortable:!0},{name:t("Country"),selector:"country",cell:e=>e&&e.institution&&e.institution.address&&e.institution.address.country?(0,a.jsx)(i(),{href:"/country/[...routes]",as:`/country/show/${e.institution.address.country._id}`,children:e.institution.address.country.title}):"",sortable:!0},{name:t("Type"),selector:"type.title",cell:e=>e.institution&&e.institution.type&&e.institution.type.title?e.institution.type.title:"",sortable:!0},{name:(0,a.jsx)(c.A,{trigger:"click",placement:"right",overlay:m,children:(0,a.jsxs)("span",{children:[t("Network"),"\xa0\xa0\xa0",(0,a.jsx)("i",{className:"fa fa-info-circle",style:{cursor:"pointer"},"aria-hidden":"true"})]})}),selector:t("Networks"),cell:e=>e.institution&&e.institution.networks&&e.institution.networks.length>0?(0,a.jsx)(u,{networks:e.institution.networks}):""}],r=e=>{if(e.institution.address&&e.institution.address.country)return e.institution.address.country&&e.institution.address.country.title?e.institution.address.country.title.toLowerCase():e.institution.address.country.title},l=e=>{if(e.institution.type&&e.institution.type&&e.institution.type.title)return e.institution.type.title.toLowerCase()};return(0,a.jsx)(d.A,{columns:s,data:n,pagServer:!0,persistTableHead:!0,sortFunction:(e,t,n)=>o().orderBy(e,e=>{if("country"===t)r(e);else if("type.title"===t)l(e);else if(e.institution&&e.institution[t])return e.institution[t].toLowerCase()},n)})}},91058:(e,t,n)=>{n.a(e,async(e,a)=>{try{n.r(t),n.d(t,{default:()=>u});var s=n(8732),i=n(82015),r=n(93024),o=n(82053),l=n(54131),c=n(88751),d=n(38058),p=e([l,d]);[l,d]=p.then?(await p)():p;let u=e=>{let{t}=(0,c.useTranslation)("common"),[n,a]=(0,i.useState)(!1);return(0,s.jsx)(s.Fragment,{children:(0,s.jsxs)(r.A.Item,{eventKey:"0",children:[(0,s.jsxs)(r.A.Header,{onClick:()=>a(!n),children:[(0,s.jsx)("div",{className:"cardTitle",children:t("LinkedVirtualSpace")}),(0,s.jsx)("div",{className:"cardArrow",children:n?(0,s.jsx)(o.FontAwesomeIcon,{icon:l.faMinus,color:"#fff"}):(0,s.jsx)(o.FontAwesomeIcon,{icon:l.faPlus,color:"#fff"})})]}),(0,s.jsx)(r.A.Body,{children:(0,s.jsx)(d.A,{id:e.routeData?.routes?.[1]||"",type:"Operation",vspaceData:[],vspaceDataLoading:!1,vspaceDataTotalRows:0,vspaceDataPerPage:10,vspaceDataCurrentPage:1})})]})})};a()}catch(e){a(e)}})},91615:(e,t,n)=>{n.a(e,async(e,a)=>{try{n.r(t),n.d(t,{default:()=>u});var s=n(8732),i=n(82015),r=n(93024),o=n(82053),l=n(54131),c=n(97377),d=n(88751),p=e([l]);l=(p.then?(await p)():p)[0];let u=e=>{let{t}=(0,d.useTranslation)("common"),[n,a]=(0,i.useState)(!1);return(0,s.jsx)(s.Fragment,{children:(0,s.jsxs)(r.A.Item,{eventKey:"0",children:[(0,s.jsxs)(r.A.Header,{onClick:()=>a(!n),children:[(0,s.jsx)("div",{className:"cardTitle",children:t("Documents")}),(0,s.jsx)("div",{className:"cardArrow",children:n?(0,s.jsx)(o.FontAwesomeIcon,{icon:l.faMinus,color:"#fff"}):(0,s.jsx)(o.FontAwesomeIcon,{icon:l.faPlus,color:"#fff"})})]}),(0,s.jsxs)(r.A.Body,{children:[(0,s.jsx)(c.A,{loading:e.documentAccoirdianData.documentAccoirdianProps.loading,sortProps:e.documentAccoirdianData.documentAccoirdianProps.sortProps,docs:e.documentAccoirdianData.documentAccoirdianProps.Document||[],docsDescription:e.documentAccoirdianData.operationData.doc_src}),(0,s.jsx)("h6",{className:"mt-3",children:t("DocumentsfromUpdates")}),(0,s.jsx)(c.A,{loading:e.documentAccoirdianData.documentAccoirdianProps.loading,sortProps:e.documentAccoirdianData.documentAccoirdianProps.sortUpdateProps,docs:e.documentAccoirdianData.documentAccoirdianProps.updateDocument||[],docsDescription:e.documentAccoirdianData.operationData.doc_src})]})]})})};a()}catch(e){a(e)}})},95645:(e,t,n)=>{n.a(e,async(e,a)=>{try{n.r(t),n.d(t,{default:()=>g});var s=n(8732),i=n(82015),r=n(93024),o=n(83551),l=n(49481),c=n(56670),d=n(82053),p=n(54131),u=n(88751),m=n(82491),h=n(28163),x=n(91058),j=n(24292),f=n(9329),A=n(91615),y=e([c,p,m,x,j,f,A]);[c,p,m,x,j,f,A]=y.then?(await y)():y;let g=e=>{let{t}=(0,u.useTranslation)("common"),[n,a]=(0,i.useState)(!1);console.log(e,"propsdata");let y=()=>(0,s.jsxs)(r.A.Item,{eventKey:"0",children:[(0,s.jsxs)(r.A.Header,{onClick:()=>a(!n),children:[(0,s.jsx)("div",{className:"cardTitle",children:t("Discussions")}),(0,s.jsx)("div",{className:"cardArrow",children:n?(0,s.jsx)(d.FontAwesomeIcon,{icon:p.faMinus,color:"#fff"}):(0,s.jsx)(d.FontAwesomeIcon,{icon:p.faPlus,color:"#fff"})})]}),(0,s.jsx)(r.A.Body,{children:(0,s.jsx)(m.A,{type:"operation",id:e?.routeData?.routes?e.routeData.routes[1]:null})})]}),g=(0,h.canViewDiscussionUpdate)(()=>(0,s.jsx)(y,{}));return(0,s.jsx)(s.Fragment,{children:(0,s.jsx)(o.A,{children:(0,s.jsxs)(l.A,{className:"operationAccordion",xs:12,children:[(0,s.jsx)(r.A,{children:(0,s.jsx)(j.default,{operation:e.operationData})}),(0,s.jsx)(r.A,{children:(0,s.jsx)(c.default,{operation:e.operationData})}),(0,s.jsx)(r.A,{children:(0,s.jsx)(f.default,{operation:e.operationData})}),(0,s.jsx)(r.A,{children:(0,s.jsx)(A.default,{documentAccoirdianData:e})}),(0,s.jsx)(r.A,{children:(0,s.jsx)(g,{})}),(0,s.jsx)(r.A,{children:(0,s.jsx)(x.default,{routeData:e.routeData})})]})})})};a()}catch(e){a(e)}})},97377:(e,t,n)=>{n.d(t,{A:()=>l});var a=n(8732);n(82015);var s=n(74716),i=n.n(s),r=n(56084),o=n(88751);let l=({docs:e,docsDescription:t,sortProps:n,loading:s})=>{let l=async(e,t)=>{n({columnSelector:e.selector,sortDirection:t})},{t:c}=(0,o.useTranslation)("common"),d=[{name:c("FileType"),width:"15%",selector:"extension",cell:e=>e&&e.extension&&e.extension},{name:c("FileName"),width:"25%",selector:"document_title",cell:e=>e&&e.original_name&&(0,a.jsx)("a",{href:`http://localhost:3001/api/v1/files/download/${e._id}`,target:"_blank",children:e.original_name.split(".").slice(0,-1).join(".")}),sortable:!0},{name:c("Description"),selector:"description",cell:e=>e&&e.description&&e.description},{name:c("UploadedDate"),width:"25%",selector:"doc_created_at",cell:e=>e&&e.updated_at&&i()(e.updated_at).format("MM/DD/YYYY"),sortable:!0}];return(0,a.jsx)(r.A,{columns:d,data:e,pagServer:!0,onSort:l,persistTableHead:!0,loading:s})}}};