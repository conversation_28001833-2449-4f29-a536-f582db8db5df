//Import Library
import React, { useState, useEffect } from "react";
import _ from "lodash";

//Import services/components
import RKIMAP1 from "../../components/common/RKIMap1";
import RKIMapMarker from "../../components/common/RKIMapMarker";
import { useTranslation } from 'next-i18next';

interface MapLegendsProps {
  t: (key: string) => string;
}

const MapLegends = (props: MapLegendsProps) => {
  const { t } = props;
  const Event = t("Event");
  return (
    <div className="map-legends">
      <ul>
        <li className="marker-yellow-legend">
          <i className="fas fa-circle" /> {t("Projects")}
        </li>
        <li className="marker-green-legend">
          <i className="fas fa-circle" /> {t("Operations")}
        </li>
        <li className="marker-red-legend">
          <i className="fas fa-circle" /> {Event}{" "}
        </li>
      </ul>
    </div>
  );
};

interface ListMapContainerProps {
  t: (key: string) => string;
  ongoingOperations: any[];
  ongoingProjects: any[];
  currentEvents: any[];
}

const ListMapContainer = (props: ListMapContainerProps) => {
  const { i18n } = useTranslation('common');
  const currentLang = i18n.language;
  const { t, ongoingOperations, ongoingProjects, currentEvents } = props;
  const [dataCollector, setDataCollector] = useState<any>({
    events: false,
    projects: false,
    operations: false,
  });
  console.log(ongoingProjects);
  console.log(dataCollector);

  const [mapdata, setMapdata] = useState<any[]>([]);
  console.log(mapdata);
  
  const [activeMarker, setactiveMarker]: any = useState({});
  const [markerInfo, setMarkerInfo]: any = useState({});

  const MarkerInfo = (Markerprops: any) => {
    const { info } = Markerprops;
    const markersInformation = markerDetails(info);
    if (
      info &&
      Object.keys(info).length > 0 &&
      markersInformation != undefined
    ) {
      return (
        <ul>
          {markersInformation.map((item: any, index: number) => {
            return (
              <li key={index}>
                <a href={`/${currentLang}/${info?.type}/show/${info?.id}`}>{item?.title}</a>
              </li>
            );
          })}
        </ul>
      );
    } else {
      return null;
    }

    function markerDetails(infoinit: any) {
      console.log(info);
      
      switch (infoinit?.type) {
        case "operation":
          return ongoingOperations.filter(
            (x) => x.country && x.country._id == infoinit.countryId
          );
        case "project":
          return ongoingProjects.filter(
            (x) =>
              x.partner_institutions &&
              x.partner_institutions.length > 0 &&
              x.partner_institutions[0].partner_country &&
              x.partner_institutions[0].partner_country._id ==
                infoinit.countryId
          );
        case "event":
          return currentEvents.filter(
            (x) => x.country && x.country._id == infoinit.countryId
          );
      }
    }
  };

  const resetMarker = () => {
    setactiveMarker(null);
    setMarkerInfo(null);
  };

  const onMarkerClick = async (props: any, marker: any, _e: any) => {
    resetMarker();
    setactiveMarker(marker);
    setMarkerInfo({
      name: props.name,
      id: props.id,
      type: props.type,
      countryId: props.countryId,
    });
  };

  const fetchOperations = () => {
    const operations = ongoingOperations;
    const dashboardOperationFilter: any[] = [];
    _.forEach(operations, (op: any) => {
      if (op.country && op.country.coordinates && op.country.coordinates[0]) {
        dashboardOperationFilter.push({
          title: op.title,
          type: "operation",
          id: op._id,
          countryId: op.country && op.country._id,
          lat: parseFloat(op.country.coordinates[0].latitude),
          lng: parseFloat(op.country.coordinates[0].longitude),
          icon: "/images/map-marker-green.svg",
        });
      }
    });
    dataCollector.operations = true;
    Data_setfunc(setDataCollector, dataCollector);
    setMapdata(prevMapdata => [...prevMapdata, ...dashboardOperationFilter]);
  };

  const fetchProjects = () => {
    const projects = ongoingProjects;
    const dashboardProjectsFilter: any[] = [];
    _.forEach(projects, (val: any) => {
      if (val.partner_institutions && val.partner_institutions.length > 0) {
        _.forEach(val.partner_institutions, (country: any) => {
          if (country.partner_country && country.partner_country.coordinates && country.partner_country.coordinates[0]) {
            dashboardProjectsFilter.push({
              title: val.title,
              type: "project",
              id: val._id,
              countryId:
                val.partner_institutions.length > 0 &&
                val.partner_institutions[0].partner_country &&
                val.partner_institutions[0].partner_country._id,
              lat: parseFloat(country.partner_country.coordinates[0].latitude),
              lng: parseFloat(country.partner_country.coordinates[0].longitude),
              icon: "/images/map-marker-yellow.svg",
            });
          }
        });
      }
    });
    dataCollector.projects = true;
    DataCollector_func(setDataCollector, dataCollector);
    setMapdata(prevMapdata => [...prevMapdata, ...dashboardProjectsFilter]);
  };

  const fetchEvents = () => {
    console.log('fetchEvents called, currentEvents:', currentEvents);
    const dashbordEventFilter: any[] = [];
    _.forEach(currentEvents, (event: any) => {
      console.log('Processing event:', event.title, 'Country:', event.country);
      if (event.country && event.country.coordinates && event.country.coordinates[0]) {
        const eventMarker = {
          title: event.title,
          type: "event",
          id: event._id,
          countryId: event.country && event.country._id,
          lat: parseFloat(event.country.coordinates[0].latitude),
          lng: parseFloat(event.country.coordinates[0].longitude),
          icon: "/images/map-marker-red.svg",
        };
        console.log('Adding event marker:', eventMarker);
        dashbordEventFilter.push(eventMarker);
      } else {
        console.log('Event skipped - missing country/coordinates:', event.title);
      }
    });
    console.log('Total event markers created:', dashbordEventFilter.length);
    dataCollector.events = true;
    data_select(setDataCollector, dataCollector);
    setMapdata(prevMapdata => {
      console.log('Adding events to mapdata. Previous length:', prevMapdata.length, 'Adding:', dashbordEventFilter.length);
      return [...prevMapdata, ...dashbordEventFilter];
    });
  };

  useEffect(() => {
    fetchProjects();
  }, [ongoingProjects]);

  useEffect(() => {
    fetchOperations();
  }, [ongoingOperations]);

  useEffect(() => {
    fetchEvents();
  }, [currentEvents]);

  return (
    <>
      <RKIMAP1
        onClose={resetMarker}
        language={currentLang}
        points={mapdata}
        activeMarker={activeMarker}
        markerInfo={<MarkerInfo info={markerInfo} />}
      >
        {mapdata.length >= 1
          ? mapdata.map((item: any, index: number) => {
              if (item.lat && item.lng && !isNaN(item.lat) && !isNaN(item.lng)) {
                return (
                  <RKIMapMarker
                    key={index}
                    name={item.title}
                    id={item.id}
                    countryId={item.countryId}
                    type={item.type}
                    icon={{
                      url: item.icon,
                    }}
                    onClick={onMarkerClick}
                    position={item}
                  />
                );
              }
              return null;
            })
          : null}
      </RKIMAP1>
      <MapLegends t={t} />
    </>
  );
};

export default ListMapContainer;
function data_select(setDataCollector: any, dataCollector: any) {
  setDataCollector(dataCollector);
}

function DataCollector_func(setDataCollector: any, dataCollector: any) {
  setDataCollector(dataCollector);
}

function Data_setfunc(setDataCollector: any, dataCollector: any) {
  setDataCollector(dataCollector);
}
