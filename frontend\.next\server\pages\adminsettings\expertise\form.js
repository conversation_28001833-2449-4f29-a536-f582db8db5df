"use strict";(()=>{var e={};e.id=2519,e.ids=[636,2519,3220],e.modules={123:e=>{e.exports=require("dom-helpers/removeEventListener")},1332:e=>{e.exports=require("react-custom-scrollbars-2")},1428:e=>{e.exports=import("axios")},1680:e=>{e.exports=require("@restart/ui/utils")},1919:e=>{e.exports=require("react-transition-group/Transition")},3271:(e,r,t)=>{t.a(e,async(e,s)=>{try{t.r(r),t.d(r,{config:()=>f,default:()=>x,getServerSideProps:()=>h,getStaticPaths:()=>m,getStaticProps:()=>c,reportWebVitals:()=>g,routeModule:()=>y,unstable_getServerProps:()=>j,unstable_getServerSideProps:()=>A,unstable_getStaticParams:()=>b,unstable_getStaticPaths:()=>q,unstable_getStaticProps:()=>v});var a=t(63885),i=t(80237),o=t(81413),n=t(9616),l=t.n(n),d=t(72386),u=t(44971),p=e([d,u]);[d,u]=p.then?(await p)():p;let x=(0,o.M)(u,"default"),c=(0,o.M)(u,"getStaticProps"),m=(0,o.M)(u,"getStaticPaths"),h=(0,o.M)(u,"getServerSideProps"),f=(0,o.M)(u,"config"),g=(0,o.M)(u,"reportWebVitals"),v=(0,o.M)(u,"unstable_getStaticProps"),q=(0,o.M)(u,"unstable_getStaticPaths"),b=(0,o.M)(u,"unstable_getStaticParams"),j=(0,o.M)(u,"unstable_getServerProps"),A=(0,o.M)(u,"unstable_getServerSideProps"),y=new a.PagesRouteModule({definition:{kind:i.A.PAGES,page:"/adminsettings/expertise/form",pathname:"/adminsettings/expertise/form",bundlePath:"",filename:""},components:{App:d.default,Document:l()},userland:u});s()}catch(e){s(e)}})},3892:e=>{e.exports=require("classnames")},4048:e=>{e.exports=require("react-truncate")},6009:e=>{e.exports=require("dom-helpers/ownerDocument")},6417:(e,r,t)=>{t.d(r,{A:()=>a});let s=t(82015).createContext(null);s.displayName="CardHeaderContext";let a=s},6952:e=>{e.exports=require("@restart/ui/Modal")},7374:e=>{e.exports=require("@restart/ui/Dropdown")},8732:e=>{e.exports=require("react/jsx-runtime")},9653:e=>{e.exports=require("@restart/ui/Overlay")},11242:e=>{e.exports=require("redux-persist/integration/react")},11688:e=>{e.exports=require("@restart/hooks/useWillUnmount")},12388:e=>{e.exports=require("dom-helpers/addEventListener")},13364:e=>{e.exports=require("redux-saga/effects")},14062:e=>{e.exports=import("react-redux")},14078:e=>{e.exports=import("swr")},14332:e=>{e.exports=require("uncontrollable")},15653:(e,r,t)=>{t.d(r,{ks:()=>o,s3:()=>n});var s=t(8732);t(82015);var a=t(59549),i=t(43294);let o=({name:e,id:r,required:t,validator:o,errorMessage:n,onChange:l,value:d,as:u,multiline:p,rows:x,pattern:c,...m})=>(0,s.jsx)(i.Field,{name:e,validate:e=>{let r="string"==typeof e?e:String(e||"");return t&&(!e||""===r.trim())?n?.validator||"This field is required":o&&!o(e)?n?.validator||"Invalid value":c&&e&&!new RegExp(c).test(e)?n?.pattern||"Invalid format":void 0},children:({field:e,meta:t})=>(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)(a.A.Control,{...e,...m,id:r,as:u||"input",rows:x,isInvalid:t.touched&&!!t.error,onChange:r=>{e.onChange(r),l&&l(r)},value:void 0!==d?d:e.value}),t.touched&&t.error?(0,s.jsx)(a.A.Control.Feedback,{type:"invalid",children:t.error}):null]})}),n=({name:e,id:r,required:t,errorMessage:o,onChange:n,value:l,children:d,...u})=>(0,s.jsx)(i.Field,{name:e,validate:e=>{if(t&&(!e||""===e))return o?.validator||"This field is required"},children:({field:e,meta:t})=>(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)(a.A.Control,{as:"select",...e,...u,id:r,isInvalid:t.touched&&!!t.error,onChange:r=>{e.onChange(r),n&&n(r)},value:void 0!==l?l:e.value,children:d}),t.touched&&t.error?(0,s.jsx)(a.A.Control.Feedback,{type:"invalid",children:t.error}):null]})})},16116:e=>{e.exports=require("invariant")},18597:(e,r,t)=>{t.d(r,{A:()=>A});var s=t(3892),a=t.n(s),i=t(82015),o=t(80739),n=t(8732);let l=i.forwardRef(({className:e,bsPrefix:r,as:t="div",...s},i)=>(r=(0,o.oU)(r,"card-body"),(0,n.jsx)(t,{ref:i,className:a()(e,r),...s})));l.displayName="CardBody";let d=i.forwardRef(({className:e,bsPrefix:r,as:t="div",...s},i)=>(r=(0,o.oU)(r,"card-footer"),(0,n.jsx)(t,{ref:i,className:a()(e,r),...s})));d.displayName="CardFooter";var u=t(6417);let p=i.forwardRef(({bsPrefix:e,className:r,as:t="div",...s},l)=>{let d=(0,o.oU)(e,"card-header"),p=(0,i.useMemo)(()=>({cardHeaderBsPrefix:d}),[d]);return(0,n.jsx)(u.A.Provider,{value:p,children:(0,n.jsx)(t,{ref:l,...s,className:a()(r,d)})})});p.displayName="CardHeader";let x=i.forwardRef(({bsPrefix:e,className:r,variant:t,as:s="img",...i},l)=>{let d=(0,o.oU)(e,"card-img");return(0,n.jsx)(s,{ref:l,className:a()(t?`${d}-${t}`:d,r),...i})});x.displayName="CardImg";let c=i.forwardRef(({className:e,bsPrefix:r,as:t="div",...s},i)=>(r=(0,o.oU)(r,"card-img-overlay"),(0,n.jsx)(t,{ref:i,className:a()(e,r),...s})));c.displayName="CardImgOverlay";let m=i.forwardRef(({className:e,bsPrefix:r,as:t="a",...s},i)=>(r=(0,o.oU)(r,"card-link"),(0,n.jsx)(t,{ref:i,className:a()(e,r),...s})));m.displayName="CardLink";var h=t(7783);let f=(0,h.A)("h6"),g=i.forwardRef(({className:e,bsPrefix:r,as:t=f,...s},i)=>(r=(0,o.oU)(r,"card-subtitle"),(0,n.jsx)(t,{ref:i,className:a()(e,r),...s})));g.displayName="CardSubtitle";let v=i.forwardRef(({className:e,bsPrefix:r,as:t="p",...s},i)=>(r=(0,o.oU)(r,"card-text"),(0,n.jsx)(t,{ref:i,className:a()(e,r),...s})));v.displayName="CardText";let q=(0,h.A)("h5"),b=i.forwardRef(({className:e,bsPrefix:r,as:t=q,...s},i)=>(r=(0,o.oU)(r,"card-title"),(0,n.jsx)(t,{ref:i,className:a()(e,r),...s})));b.displayName="CardTitle";let j=i.forwardRef(({bsPrefix:e,className:r,bg:t,text:s,border:i,body:d=!1,children:u,as:p="div",...x},c)=>{let m=(0,o.oU)(e,"card");return(0,n.jsx)(p,{ref:c,...x,className:a()(r,m,t&&`bg-${t}`,s&&`text-${s}`,i&&`border-${i}`),children:d?(0,n.jsx)(l,{children:u}):u})});j.displayName="Card";let A=Object.assign(j,{Img:x,Title:b,Subtitle:g,Body:l,Link:m,Text:v,Header:p,Footer:d,ImgOverlay:c})},18622:e=>{e.exports=require("yup")},19121:e=>{e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},22326:e=>{e.exports=require("react-dom")},22541:e=>{e.exports=require("redux-persist/lib/storage")},23579:(e,r,t)=>{t.d(r,{sx:()=>u,s3:()=>a.s3,ks:()=>a.ks,yk:()=>s.A});var s=t(66994),a=t(15653),i=t(8732),o=t(82015),n=t.n(o),l=t(43294),d=t(59549);let u={RadioGroup:({name:e,valueSelected:r,onChange:t,errorMessage:s,children:a})=>{let{errors:o,touched:d}=(0,l.useFormikContext)(),u=d[e]&&o[e];n().useMemo(()=>({name:e}),[e]);let p=n().Children.map(a,r=>n().isValidElement(r)&&function(e){return"object"==typeof e&&null!==e}(r.props)?n().cloneElement(r,{name:e,...r.props}):r);return(0,i.jsxs)("div",{children:[(0,i.jsx)("div",{className:"radio-group",children:p}),u&&(0,i.jsx)("div",{className:"invalid-feedback d-block",children:s||("string"==typeof o[e]?o[e]:String(o[e]))})]})},RadioItem:({id:e,label:r,value:t,name:s,disabled:a})=>{let{values:o,setFieldValue:n}=(0,l.useFormikContext)(),u=s||e;return(0,i.jsx)(d.A.Check,{type:"radio",id:e,label:r,value:t,name:u,checked:o[u]===t,onChange:e=>{n(u,e.target.value)},disabled:a,inline:!0})}};s.A,a.ks,a.s3},25303:e=>{e.exports=require("@restart/ui/NavItem")},26324:e=>{e.exports=require("warning")},27825:e=>{e.exports=require("lodash")},27910:e=>{e.exports=require("stream")},28217:e=>{e.exports=require("@restart/ui/DropdownItem")},29021:e=>{e.exports=require("fs")},29294:e=>{e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},29628:e=>{e.exports=require("dom-helpers/addClass")},29825:e=>{e.exports=require("prop-types")},29841:e=>{e.exports=require("dom-helpers/hasClass")},30362:e=>{e.exports=require("@restart/ui/DropdownToggle")},33873:e=>{e.exports=require("path")},36653:e=>{e.exports=require("nprogress")},36955:e=>{e.exports=require("@restart/hooks/useBreakpoint")},37766:e=>{e.exports=require("@restart/hooks/useMergedRefs")},39756:e=>{e.exports=import("redux")},40051:e=>{e.exports=require("dom-helpers/removeClass")},40361:e=>{e.exports=require("next/dist/compiled/next-server/pages.runtime.prod.js")},42893:e=>{e.exports=import("react-hot-toast")},43294:e=>{e.exports=require("formik")},44971:(e,r,t)=>{t.a(e,async(e,s)=>{try{t.r(r),t.d(r,{default:()=>A});var a=t(8732),i=t(82015),o=t(7082),n=t(18597),l=t(83551),d=t(49481),u=t(59549),p=t(91353),x=t(23579),c=t(66994),m=t(44233),h=t.n(m),f=t(42893),g=t(19918),v=t.n(g),q=t(63487),b=t(88751),j=e([f,q]);[f,q]=j.then?(await j)():j;let A=e=>{let r={_id:"",title:""},{t}=(0,b.useTranslation)("common"),[s,m]=(0,i.useState)(r),g=e.routes&&"edit_expertise"===e.routes[0]&&e.routes[1],j=(0,i.useRef)(null),A=async r=>{let a,i;r.preventDefault();let o={title:s.title.trim()};g?(i="adminsetting.Expertise.Forms.Expertiseisupdatedsuccessfully",a=await q.A.patch(`/expertise/${e.routes[1]}`,o)):(i="adminsetting.Expertise.Forms.Expertiseisaddedsuccessfully",a=await q.A.post("/expertise",o)),a&&a._id?(f.default.success(t(i)),h().push("/adminsettings/expertise")):a?.errorCode===11e3?f.default.error(t("duplicatesNotAllowed")):f.default.error(a)};return(0,i.useEffect)(()=>{let r={query:{},sort:{title:"asc"},limit:"~"};g&&(async()=>{let t=await q.A.get(`/expertise/${e.routes[1]}`,r);m(e=>({...e,...t}))})()},[]),(0,a.jsx)("div",{children:(0,a.jsx)(o.A,{className:"formCard",fluid:!0,children:(0,a.jsx)(n.A,{style:{marginTop:"5px",boxShadow:"0 4px 8px 0 rgba(0, 0, 0, 0.2), 0 6px 20px 0 rgba(0, 0, 0, 0.19)"},children:(0,a.jsx)(c.A,{onSubmit:A,ref:j,initialValues:s,enableReinitialize:!0,children:(0,a.jsxs)(n.A.Body,{children:[(0,a.jsx)(l.A,{children:(0,a.jsx)(d.A,{children:(0,a.jsx)(n.A.Title,{children:t("adminsetting.Expertise.Forms.Expertise")})})}),(0,a.jsx)("hr",{}),(0,a.jsx)(l.A,{children:(0,a.jsx)(d.A,{md:!0,lg:6,sm:12,children:(0,a.jsxs)(u.A.Group,{children:[(0,a.jsx)(u.A.Label,{className:"required-field",children:t("adminsetting.Expertise.Forms.Expertise")}),(0,a.jsx)(x.ks,{name:"title",id:"title",required:!0,value:s.title,validator:e=>""!==String(e||"").trim(),errorMessage:{validator:t("adminsetting.Expertise.Forms.PleaseAddtheExpertise")},onChange:e=>{if(e.target){let{name:r,value:t}=e.target;m(e=>({...e,[r]:t}))}}})]})})}),(0,a.jsx)(l.A,{className:"my-4",children:(0,a.jsxs)(d.A,{children:[(0,a.jsx)(p.A,{className:"me-2",type:"submit",variant:"primary",children:t("adminsetting.Expertise.Forms.Submit")}),(0,a.jsx)(p.A,{className:"me-2",onClick:()=>{m(r),window.scrollTo(0,0)},variant:"info",children:t("adminsetting.Expertise.Forms.Reset")}),(0,a.jsx)(v(),{href:"/adminsettings/[...routes]",as:"/adminsettings/expertise",children:(0,a.jsx)(p.A,{variant:"secondary",children:t("adminsetting.Expertise.Forms.Cancel")})})]})})]})})})})})};s()}catch(e){s(e)}})},50009:e=>{e.exports=require("@restart/ui/DropdownMenu")},57664:e=>{e.exports=require("dom-helpers/scrollbarSize")},58928:e=>{e.exports=require("dom-helpers/canUseDOM")},59717:e=>{e.exports=require("@restart/ui/DropdownContext")},60560:e=>{e.exports=require("dom-helpers/contains")},63033:e=>{e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},65447:e=>{e.exports=require("@restart/ui/Button")},66994:(e,r,t)=>{t.d(r,{A:()=>l});var s=t(8732),a=t(82015),i=t(43294),o=t(18622);let n=(0,a.forwardRef)((e,r)=>{let{children:t,onSubmit:a,autoComplete:n,className:l,onKeyPress:d,initialValues:u,...p}=e,x=o.object().shape({});return(0,s.jsx)(i.Formik,{initialValues:u||{},validationSchema:x,onSubmit:(e,r)=>{let t={preventDefault:()=>{},stopPropagation:()=>{},currentTarget:null,target:null,nativeEvent:new Event("submit"),bubbles:!1,cancelable:!0,defaultPrevented:!1,eventPhase:0,isTrusted:!1,timeStamp:Date.now(),type:"submit",isDefaultPrevented:()=>!1,isPropagationStopped:()=>!1,persist:()=>{}};a&&a(t,e,r)},...p,children:e=>(0,s.jsx)(i.Form,{ref:r,onSubmit:e.handleSubmit,autoComplete:n,className:l,onKeyPress:d,children:"function"==typeof t?t(e):t})})});n.displayName="ValidationFormWrapper";let l=n},67364:e=>{e.exports=require("@restart/hooks/useCallbackRef")},68455:e=>{e.exports=import("redux-saga")},69722:e=>{e.exports=require("es6-promise")},74075:e=>{e.exports=require("zlib")},74716:e=>{e.exports=require("moment")},74987:e=>{e.exports=require("@restart/ui/ModalManager")},78097:e=>{e.exports=require("next-redux-saga")},78634:e=>{e.exports=require("@restart/ui/Anchor")},80237:(e,r)=>{Object.defineProperty(r,"A",{enumerable:!0,get:function(){return t}});var t=function(e){return e.PAGES="PAGES",e.PAGES_API="PAGES_API",e.APP_PAGE="APP_PAGE",e.APP_ROUTE="APP_ROUTE",e.IMAGE="IMAGE",e}({})},81366:e=>{e.exports=require("redux-auth-wrapper/connectedAuthWrapper")},81413:(e,r)=>{Object.defineProperty(r,"M",{enumerable:!0,get:function(){return function e(r,t){return t in r?r[t]:"then"in r&&"function"==typeof r.then?r.then(r=>e(r,t)):"function"==typeof r&&"default"===t?r:void 0}}})},81521:e=>{e.exports=require("dom-helpers/querySelectorAll")},81895:e=>{e.exports=require("@restart/hooks/useEventCallback")},82015:e=>{e.exports=require("react")},86842:e=>{e.exports=require("@restart/ui/SelectableContext")},87571:e=>{e.exports=require("dom-helpers/transitionEnd")},88751:e=>{e.exports=require("next-i18next")},93787:e=>{e.exports=require("redux-persist")},94947:e=>{e.exports=require("@restart/hooks/useTimeout")},96196:e=>{e.exports=require("next-redux-wrapper")},98320:e=>{e.exports=require("@restart/hooks/useIsomorphicEffect")},99460:e=>{e.exports=require("dom-helpers/css")}};var r=require("../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[6089,9216,9616,2386],()=>t(3271));module.exports=s})();