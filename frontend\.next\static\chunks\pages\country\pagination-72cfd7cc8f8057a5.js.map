{"version": 3, "file": "static/chunks/pages/country/pagination-72cfd7cc8f8057a5.js", "mappings": "mJAuBA,MApBmB,OAAC,cAAEA,CAAY,MAoBnBC,MApBqBC,CAAU,CAAEC,EAoBvBF,EAAC,MApB8B,CAAyD,GACzGG,EAAc,EAAE,CAEtB,IAAK,IAAIC,EAAI,EAAGA,GAAKC,KAAKC,IAAI,CAACL,EAAaF,GAAeK,IAAK,EAClDG,IAAI,CAACH,GAGnB,MACE,UAACI,KAAAA,CAAGC,UAAU,sBACXN,EAAYO,GAAG,CAACC,GACf,UAACC,KAAAA,CAAgBH,UAAU,qBACzB,UAACI,IAAAA,CAAEC,QAAS,IAAMZ,EAASS,GAAUF,UAAU,qBAC5CE,KAFIA,KAQjB,mBCpBA,4CACA,sBACA,WACA,OAAe,EAAQ,KAA2C,CAClE,EACA,SAFsB", "sources": ["webpack://_N_E/./pages/country/pagination.tsx", "webpack://_N_E/?93b5"], "sourcesContent": ["//Import Library\r\nimport React from 'react';\r\n\r\nconst Pagination = ({ postsPerPage, totalPosts, paginate }: { postsPerPage: any, totalPosts: any, paginate: any }) => {\r\n  const pageNumbers = [];\r\n\r\n  for (let i = 1; i <= Math.ceil(totalPosts / postsPerPage); i++) {\r\n    pageNumbers.push(i);\r\n  }\r\n\r\n  return (\r\n    <ul className='pagination'>\r\n      {pageNumbers.map(number => (\r\n        <li key={number} className='page-item'>\r\n          <a onClick={() => paginate(number)}  className='page-link'>\r\n            {number}\r\n          </a>\r\n        </li>\r\n      ))}\r\n    </ul>\r\n  );\r\n};\r\n\r\nexport default Pagination;", "\n    (window.__NEXT_P = window.__NEXT_P || []).push([\n      \"/country/pagination\",\n      function () {\n        return require(\"private-next-pages/country/pagination.tsx\");\n      }\n    ]);\n    if(module.hot) {\n      module.hot.dispose(function () {\n        window.__NEXT_P.push([\"/country/pagination\"])\n      });\n    }\n  "], "names": ["postsPerPage", "Pagination", "totalPosts", "paginate", "pageNumbers", "i", "Math", "ceil", "push", "ul", "className", "map", "number", "li", "a", "onClick"], "sourceRoot": "", "ignoreList": []}