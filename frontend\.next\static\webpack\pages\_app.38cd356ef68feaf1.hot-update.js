"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/_app",{

/***/ "(pages-dir-browser)/./node_modules/next-redux-wrapper/es6/index.js":
/*!******************************************************!*\
  !*** ./node_modules/next-redux-wrapper/es6/index.js ***!
  \******************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   HYDRATE: () => (/* binding */ HYDRATE),\n/* harmony export */   createWrapper: () => (/* binding */ createWrapper),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(pages-dir-browser)/./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react_redux__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react-redux */ \"(pages-dir-browser)/./node_modules/react-redux/dist/react-redux.mjs\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/router */ \"(pages-dir-browser)/./node_modules/next/router.js\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_router__WEBPACK_IMPORTED_MODULE_1__);\nvar __assign = (undefined && undefined.__assign) || function () {\n    __assign = Object.assign || function(t) {\n        for (var s, i = 1, n = arguments.length; i < n; i++) {\n            s = arguments[i];\n            for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p))\n                t[p] = s[p];\n        }\n        return t;\n    };\n    return __assign.apply(this, arguments);\n};\nvar __awaiter = (undefined && undefined.__awaiter) || function (thisArg, _arguments, P, generator) {\n    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }\n    return new (P || (P = Promise))(function (resolve, reject) {\n        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }\n        function rejected(value) { try { step(generator[\"throw\"](value)); } catch (e) { reject(e); } }\n        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }\n        step((generator = generator.apply(thisArg, _arguments || [])).next());\n    });\n};\nvar __generator = (undefined && undefined.__generator) || function (thisArg, body) {\n    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g;\n    return g = { next: verb(0), \"throw\": verb(1), \"return\": verb(2) }, typeof Symbol === \"function\" && (g[Symbol.iterator] = function() { return this; }), g;\n    function verb(n) { return function (v) { return step([n, v]); }; }\n    function step(op) {\n        if (f) throw new TypeError(\"Generator is already executing.\");\n        while (_) try {\n            if (f = 1, y && (t = op[0] & 2 ? y[\"return\"] : op[0] ? y[\"throw\"] || ((t = y[\"return\"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;\n            if (y = 0, t) op = [op[0] & 2, t.value];\n            switch (op[0]) {\n                case 0: case 1: t = op; break;\n                case 4: _.label++; return { value: op[1], done: false };\n                case 5: _.label++; y = op[1]; op = [0]; continue;\n                case 7: op = _.ops.pop(); _.trys.pop(); continue;\n                default:\n                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }\n                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }\n                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }\n                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }\n                    if (t[2]) _.ops.pop();\n                    _.trys.pop(); continue;\n            }\n            op = body.call(thisArg, _);\n        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }\n        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };\n    }\n};\nvar __rest = (undefined && undefined.__rest) || function (s, e) {\n    var t = {};\n    for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0)\n        t[p] = s[p];\n    if (s != null && typeof Object.getOwnPropertySymbols === \"function\")\n        for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {\n            if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i]))\n                t[p[i]] = s[p[i]];\n        }\n    return t;\n};\n\n\n\n/**\n * Quick note on Next.js return types:\n *\n * Page.getInitialProps https://nextjs.org/docs/api-reference/data-fetching/getInitialProps\n * as-is\n *\n * App.getInitialProps: AppInitialProps https://nextjs.org/docs/advanced-features/custom-app\n * {pageProps: any}\n *\n * getStaticProps https://nextjs.org/docs/basic-features/data-fetching#getstaticprops-static-generation\n * {props: any}\n *\n * getServerSideProps https://nextjs.org/docs/basic-features/data-fetching#getserversideprops-server-side-rendering\n * {props: any}\n */\nvar HYDRATE = '__NEXT_REDUX_WRAPPER_HYDRATE__';\nvar getIsServer = function () { return typeof window === 'undefined'; };\nvar getDeserializedState = function (initialState, _a) {\n    var _b = _a === void 0 ? {} : _a, deserializeState = _b.deserializeState;\n    return deserializeState ? deserializeState(initialState) : initialState;\n};\nvar getSerializedState = function (state, _a) {\n    var _b = _a === void 0 ? {} : _a, serializeState = _b.serializeState;\n    return serializeState ? serializeState(state) : state;\n};\nvar sharedClientStore;\nvar initStore = function (_a) {\n    var _b, _c, _d;\n    var makeStore = _a.makeStore, _e = _a.context, context = _e === void 0 ? {} : _e;\n    var createStore = function () { return makeStore(context); };\n    if (getIsServer()) {\n        var req = ((_b = context) === null || _b === void 0 ? void 0 : _b.req) || ((_d = (_c = context) === null || _c === void 0 ? void 0 : _c.ctx) === null || _d === void 0 ? void 0 : _d.req);\n        if (req) {\n            // ATTENTION! THIS IS INTERNAL, DO NOT ACCESS DIRECTLY ANYWHERE ELSE\n            // @see https://github.com/kirill-konshin/next-redux-wrapper/pull/196#issuecomment-611673546\n            if (!req.__nextReduxWrapperStore) {\n                req.__nextReduxWrapperStore = createStore(); // Used in GIP/GSSP\n            }\n            return req.__nextReduxWrapperStore;\n        }\n        return createStore();\n    }\n    // Memoize the store if we're on the client\n    if (!sharedClientStore) {\n        sharedClientStore = createStore();\n    }\n    return sharedClientStore;\n};\nvar createWrapper = function (makeStore, config) {\n    if (config === void 0) { config = {}; }\n    var makeProps = function (_a) {\n        var callback = _a.callback, context = _a.context, _b = _a.addStoreToContext, addStoreToContext = _b === void 0 ? false : _b;\n        return __awaiter(void 0, void 0, void 0, function () {\n            var store, nextCallback, initialProps, _c, state;\n            return __generator(this, function (_d) {\n                switch (_d.label) {\n                    case 0:\n                        store = initStore({ context: context, makeStore: makeStore });\n                        if (config.debug) {\n                            console.log(\"1. getProps created store with state\", store.getState());\n                        }\n                        // Legacy stuff - put store in context\n                        if (addStoreToContext) {\n                            if (context.ctx) {\n                                context.ctx.store = store;\n                            }\n                            else {\n                                context.store = store;\n                            }\n                        }\n                        nextCallback = callback && callback(store);\n                        _c = nextCallback;\n                        if (!_c) return [3 /*break*/, 2];\n                        return [4 /*yield*/, nextCallback(context)];\n                    case 1:\n                        _c = (_d.sent());\n                        _d.label = 2;\n                    case 2:\n                        initialProps = (_c) || {};\n                        if (config.debug) {\n                            console.log(\"3. getProps after dispatches has store state\", store.getState());\n                        }\n                        state = store.getState();\n                        return [2 /*return*/, {\n                                initialProps: initialProps,\n                                initialState: getIsServer() ? getSerializedState(state, config) : state,\n                            }];\n                }\n            });\n        });\n    };\n    var getInitialPageProps = function (callback) {\n        return function (context) { return __awaiter(void 0, void 0, void 0, function () {\n            return __generator(this, function (_a) {\n                switch (_a.label) {\n                    case 0:\n                        // context is store — avoid double-wrapping\n                        if ('getState' in context) {\n                            return [2 /*return*/, callback && callback(context)];\n                        }\n                        return [4 /*yield*/, makeProps({ callback: callback, context: context, addStoreToContext: true })];\n                    case 1: return [2 /*return*/, _a.sent()];\n                }\n            });\n        }); };\n    };\n    var getInitialAppProps = function (callback) {\n        return function (context) { return __awaiter(void 0, void 0, void 0, function () {\n            var _a, initialProps, initialState;\n            return __generator(this, function (_b) {\n                switch (_b.label) {\n                    case 0: return [4 /*yield*/, makeProps({ callback: callback, context: context, addStoreToContext: true })];\n                    case 1:\n                        _a = _b.sent(), initialProps = _a.initialProps, initialState = _a.initialState;\n                        return [2 /*return*/, __assign(__assign({}, initialProps), { initialState: initialState })];\n                }\n            });\n        }); };\n    };\n    var getStaticProps = function (callback) {\n        return function (context) { return __awaiter(void 0, void 0, void 0, function () {\n            var _a, initialProps, initialState;\n            return __generator(this, function (_b) {\n                switch (_b.label) {\n                    case 0: return [4 /*yield*/, makeProps({ callback: callback, context: context })];\n                    case 1:\n                        _a = _b.sent(), initialProps = _a.initialProps, initialState = _a.initialState;\n                        return [2 /*return*/, __assign(__assign({}, initialProps), { props: __assign(__assign({}, initialProps.props), { initialState: initialState }) })];\n                }\n            });\n        }); };\n    };\n    var getServerSideProps = function (callback) {\n        return function (context) { return __awaiter(void 0, void 0, void 0, function () { return __generator(this, function (_a) {\n            switch (_a.label) {\n                case 0: return [4 /*yield*/, getStaticProps(callback)(context)];\n                case 1: return [2 /*return*/, _a.sent()];\n            }\n        }); }); };\n    }; // just not to repeat myself\n    var hydrate = function (store, state) {\n        if (!state) {\n            return;\n        }\n        store.dispatch({\n            type: HYDRATE,\n            payload: getDeserializedState(state, config),\n        });\n    };\n    var hydrateOrchestrator = function (store, giapState, gspState, gsspState, gippState) {\n        var _a;\n        if (gspState) {\n            // If GSP has run, then gspState will _not_ contain the data from GIP (if it exists), because GSP is run at build time,\n            // and GIP runs at request time. So we have to hydrate the GIP data first, and then do another hydrate on the gspState.\n            hydrate(store, giapState);\n            hydrate(store, gspState);\n        }\n        else if (gsspState || gippState || giapState) {\n            // If GSSP has run, then gsspState _will_ contain the data from GIP (if there is a GIP) and the GSSP data combined\n            // (see https://github.com/kirill-konshin/next-redux-wrapper/pull/499#discussion_r1014500941).\n            // If there is no GSP or GSSP for this page, but there is a GIP on page level (not _app), then we use the gippState.\n            // If there is no GSP or GSSP and no GIP on page level for this page, but there is a GIP on _app level, then we use the giapState.\n            hydrate(store, (_a = gsspState !== null && gsspState !== void 0 ? gsspState : gippState) !== null && _a !== void 0 ? _a : giapState);\n        }\n    };\n    var useHybridHydrate = function (store, giapState, gspState, gsspState, gippState) {\n        var events = (0,next_router__WEBPACK_IMPORTED_MODULE_1__.useRouter)().events;\n        var shouldHydrate = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(true);\n        // We should only hydrate when the router has changed routes\n        (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(function () {\n            var handleStart = function () {\n                shouldHydrate.current = true;\n            };\n            events === null || events === void 0 ? void 0 : events.on('routeChangeStart', handleStart);\n            return function () {\n                events === null || events === void 0 ? void 0 : events.off('routeChangeStart', handleStart);\n            };\n        }, [events]);\n        // useMemo so that when we navigate client side, we always synchronously hydrate the state before the new page\n        // components are mounted. This means we hydrate while the previous page components are still mounted.\n        // You might think that might cause issues because the selectors on the previous page (still mounted) will suddenly\n        // contain other data, and maybe even nested properties, causing null reference exceptions.\n        // But that's not the case.\n        // Hydrating in useMemo will not trigger a rerender of the still mounted page component. So if your selectors do have\n        // some initial state values causing them to rerun after hydration, and you're accessing deeply nested values inside your\n        // components, you still wouldn't get errors, because there's no rerender.\n        // Instead, React will render the new page components straight away, which will have selectors with the correct data.\n        (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(function () {\n            if (shouldHydrate.current) {\n                hydrateOrchestrator(store, giapState, gspState, gsspState, gippState);\n                shouldHydrate.current = false;\n            }\n        }, [store, giapState, gspState, gsspState, gippState]);\n    };\n    // giapState stands for getInitialAppProps state\n    var useWrappedStore = function (_a, displayName) {\n        var _b, _c, _d, _e, _f, _g;\n        if (displayName === void 0) { displayName = 'useWrappedStore'; }\n        var giapState = _a.initialState, initialProps = _a.initialProps, props = __rest(_a, [\"initialState\", \"initialProps\"]);\n        // getStaticProps state\n        var gspState = (props === null || props === void 0 ? void 0 : props.__N_SSG) ? (_b = props === null || props === void 0 ? void 0 : props.pageProps) === null || _b === void 0 ? void 0 : _b.initialState : null;\n        // getServerSideProps state\n        var gsspState = (props === null || props === void 0 ? void 0 : props.__N_SSP) ? (_c = props === null || props === void 0 ? void 0 : props.pageProps) === null || _c === void 0 ? void 0 : _c.initialState : null;\n        // getInitialPageProps state\n        var gippState = !gspState && !gsspState ? (_e = (_d = props === null || props === void 0 ? void 0 : props.pageProps) === null || _d === void 0 ? void 0 : _d.initialState) !== null && _e !== void 0 ? _e : null : null;\n        if (config.debug) {\n            console.log('4.', displayName, 'created new store with', {\n                giapState: giapState,\n                gspState: gspState,\n                gsspState: gsspState,\n                gippState: gippState,\n            });\n        }\n        var store = (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(function () { return initStore({ makeStore: makeStore }); }, []);\n        useHybridHydrate(store, giapState, gspState, gsspState, gippState);\n        var resultProps = props;\n        // order is important! Next.js overwrites props from pages/_app with getStaticProps from page\n        // @see https://github.com/zeit/next.js/issues/11648\n        if (initialProps && initialProps.pageProps) {\n            resultProps.pageProps = __assign(__assign({}, initialProps.pageProps), props.pageProps);\n        }\n        // just some cleanup to prevent passing it as props, we need to clone props to safely delete initialState\n        if ((_f = props === null || props === void 0 ? void 0 : props.pageProps) === null || _f === void 0 ? void 0 : _f.initialState) {\n            resultProps = __assign(__assign({}, props), { pageProps: __assign({}, props.pageProps) });\n            delete resultProps.pageProps.initialState;\n        }\n        // unwrap getInitialPageProps\n        if ((_g = resultProps === null || resultProps === void 0 ? void 0 : resultProps.pageProps) === null || _g === void 0 ? void 0 : _g.initialProps) {\n            resultProps.pageProps = __assign(__assign({}, resultProps.pageProps), resultProps.pageProps.initialProps);\n            delete resultProps.pageProps.initialProps;\n        }\n        return { store: store, props: __assign(__assign({}, initialProps), resultProps) };\n    };\n    var withRedux = function (Component) {\n        console.warn('/!\\\\ You are using legacy implementation. Please update your code: use createWrapper() and wrapper.useWrappedStore().');\n        //TODO Check if pages/_app was wrapped so there's no need to wrap a page itself\n        var WrappedComponent = function (props) {\n            var _a = useWrappedStore(props, WrappedComponent.displayName), store = _a.store, combinedProps = _a.props;\n            return (react__WEBPACK_IMPORTED_MODULE_0___default().createElement(react_redux__WEBPACK_IMPORTED_MODULE_2__.Provider, { store: store },\n                react__WEBPACK_IMPORTED_MODULE_0___default().createElement(Component, __assign({}, combinedProps))));\n        };\n        WrappedComponent.displayName = \"withRedux(\".concat(Component.displayName || Component.name || 'Component', \")\");\n        if ('getInitialProps' in Component) {\n            WrappedComponent.getInitialProps = Component.getInitialProps;\n        }\n        return WrappedComponent;\n    };\n    return {\n        getServerSideProps: getServerSideProps,\n        getStaticProps: getStaticProps,\n        getInitialAppProps: getInitialAppProps,\n        getInitialPageProps: getInitialPageProps,\n        withRedux: withRedux,\n        useWrappedStore: useWrappedStore,\n    };\n};\n// Legacy\n// eslint-disable-next-line import/no-anonymous-default-export\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (function (makeStore, config) {\n    if (config === void 0) { config = {}; }\n    console.warn('/!\\\\ You are using legacy implementation. Please update your code: use createWrapper() and wrapper.withRedux().');\n    return createWrapper(makeStore, config).withRedux;\n});\n//# sourceMappingURL=index.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-browser)/./node_modules/next-redux-wrapper/es6/index.js\n"));

/***/ }),

/***/ "(pages-dir-browser)/./pages/_app.tsx":
/*!************************!*\
  !*** ./pages/_app.tsx ***!
  \************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(pages-dir-browser)/./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(pages-dir-browser)/./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react_redux__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! react-redux */ \"(pages-dir-browser)/./node_modules/react-redux/dist/react-redux.mjs\");\n/* harmony import */ var next_redux_wrapper__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next-redux-wrapper */ \"(pages-dir-browser)/./node_modules/next-redux-wrapper/es6/index.js\");\n/* harmony import */ var redux_persist_integration_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! redux-persist/integration/react */ \"(pages-dir-browser)/./node_modules/redux-persist/es/integration/react.js\");\n/* harmony import */ var react_hot_toast__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! react-hot-toast */ \"(pages-dir-browser)/./node_modules/react-hot-toast/dist/index.mjs\");\n/* harmony import */ var _barrel_optimize_names_Spinner_react_bootstrap__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=Spinner!=!react-bootstrap */ \"(pages-dir-browser)/__barrel_optimize__?names=Spinner!=!./node_modules/react-bootstrap/esm/index.js\");\n/* harmony import */ var next_app__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! next/app */ \"(pages-dir-browser)/./node_modules/next/app.js\");\n/* harmony import */ var next_app__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(next_app__WEBPACK_IMPORTED_MODULE_5__);\n/* harmony import */ var next_i18next__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! next-i18next */ \"(pages-dir-browser)/./node_modules/next-i18next/dist/esm/index.js\");\n/* harmony import */ var _components_layout_authenticated_Layout__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ../components/layout/authenticated/Layout */ \"(pages-dir-browser)/./components/layout/authenticated/Layout.tsx\");\n/* harmony import */ var _components_layout_authenticated_Footer__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ../components/layout/authenticated/Footer */ \"(pages-dir-browser)/./components/layout/authenticated/Footer.tsx\");\n/* harmony import */ var _styles_global_scss__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ../styles/global.scss */ \"(pages-dir-browser)/./styles/global.scss\");\n/* harmony import */ var _styles_global_scss__WEBPACK_IMPORTED_MODULE_9___default = /*#__PURE__*/__webpack_require__.n(_styles_global_scss__WEBPACK_IMPORTED_MODULE_9__);\n/* harmony import */ var _components_hoc_AuthSync__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ../components/hoc/AuthSync */ \"(pages-dir-browser)/./components/hoc/AuthSync.tsx\");\n/* harmony import */ var _store__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! ../store */ \"(pages-dir-browser)/./store.tsx\");\n/* harmony import */ var _routePermissions__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! ./routePermissions */ \"(pages-dir-browser)/./pages/routePermissions.tsx\");\n/* harmony import */ var _components_common_GoogleMapsProvider__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! ../components/common/GoogleMapsProvider */ \"(pages-dir-browser)/./components/common/GoogleMapsProvider.tsx\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! next/router */ \"(pages-dir-browser)/./node_modules/next/router.js\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_14___default = /*#__PURE__*/__webpack_require__.n(next_router__WEBPACK_IMPORTED_MODULE_14__);\n\nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n//Import services/components\n\n\n\n\n\n\n\n\n\n// Create the wrapper\nconst wrapper = (0,next_redux_wrapper__WEBPACK_IMPORTED_MODULE_2__.createWrapper)(_store__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n    debug: \"development\" === 'development'\n});\nif (true) {\n    module.hot.addStatusHandler((status)=>{\n        if ( true && status === 'ready') {\n            window['__webpack_reload_css__'] = true;\n        }\n    });\n}\nfunction MyApp(param) {\n    let { Component, pageProps, router, isPublicRoute, isLoading } = param;\n    _s();\n    const CanAccessRoutes = (0,_routePermissions__WEBPACK_IMPORTED_MODULE_12__.canAccessRoutes)(()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Component, {\n            ...pageProps,\n            router: router\n        }, void 0, false, {\n            fileName: \"D:\\\\rki\\\\frontend\\\\pages\\\\_app.tsx\",\n            lineNumber: 43,\n            columnNumber: 49\n        }, this));\n    const { locale } = (0,next_router__WEBPACK_IMPORTED_MODULE_14__.useRouter)();\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_common_GoogleMapsProvider__WEBPACK_IMPORTED_MODULE_13__.GoogleMapsProvider, {\n        language: locale || 'en',\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_redux__WEBPACK_IMPORTED_MODULE_15__.Provider, {\n            store: store,\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(redux_persist_integration_react__WEBPACK_IMPORTED_MODULE_3__.PersistGate, {\n                loading: null,\n                persistor: persistor,\n                children: isLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Spinner_react_bootstrap__WEBPACK_IMPORTED_MODULE_16__.Spinner, {\n                    animation: \"border\",\n                    variant: \"primary\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\rki\\\\frontend\\\\pages\\\\_app.tsx\",\n                    lineNumber: 51,\n                    columnNumber: 13\n                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                    children: [\n                        public_route_func(),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_hot_toast__WEBPACK_IMPORTED_MODULE_4__.Toaster, {\n                            position: \"top-right\",\n                            reverseOrder: false\n                        }, void 0, false, {\n                            fileName: \"D:\\\\rki\\\\frontend\\\\pages\\\\_app.tsx\",\n                            lineNumber: 55,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true)\n            }, void 0, false, {\n                fileName: \"D:\\\\rki\\\\frontend\\\\pages\\\\_app.tsx\",\n                lineNumber: 49,\n                columnNumber: 7\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\rki\\\\frontend\\\\pages\\\\_app.tsx\",\n            lineNumber: 48,\n            columnNumber: 5\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\rki\\\\frontend\\\\pages\\\\_app.tsx\",\n        lineNumber: 47,\n        columnNumber: 5\n    }, this);\n    function public_route_func() {\n        return isPublicRoute ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Component, {\n            ...pageProps,\n            router: router\n        }, void 0, false, {\n            fileName: \"D:\\\\rki\\\\frontend\\\\pages\\\\_app.tsx\",\n            lineNumber: 65,\n            columnNumber: 29\n        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_layout_authenticated_Layout__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n            router: router,\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(CanAccessRoutes, {\n                    router: router,\n                    pageProps: pageProps\n                }, void 0, false, {\n                    fileName: \"D:\\\\rki\\\\frontend\\\\pages\\\\_app.tsx\",\n                    lineNumber: 67,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_layout_authenticated_Footer__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {}, void 0, false, {\n                    fileName: \"D:\\\\rki\\\\frontend\\\\pages\\\\_app.tsx\",\n                    lineNumber: 68,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\rki\\\\frontend\\\\pages\\\\_app.tsx\",\n            lineNumber: 66,\n            columnNumber: 7\n        }, this);\n    }\n}\n_s(MyApp, \"8NqG7Oj4xICngQpoOytAK34hitI=\", false, function() {\n    return [\n        next_router__WEBPACK_IMPORTED_MODULE_14__.useRouter\n    ];\n});\n_c = MyApp;\nMyApp.getInitialProps = async (appContext)=>{\n    const appProps = await next_app__WEBPACK_IMPORTED_MODULE_5___default().getInitialProps(appContext);\n    return {\n        ...appProps\n    };\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (withRedux(_store__WEBPACK_IMPORTED_MODULE_11__[\"default\"])(withReduxSaga((0,next_i18next__WEBPACK_IMPORTED_MODULE_6__.appWithTranslation)((0,_components_hoc_AuthSync__WEBPACK_IMPORTED_MODULE_10__[\"default\"])(MyApp)))));\nvar _c;\n$RefreshReg$(_c, \"MyApp\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-browser)/./pages/_app.tsx\n"));

/***/ })

});