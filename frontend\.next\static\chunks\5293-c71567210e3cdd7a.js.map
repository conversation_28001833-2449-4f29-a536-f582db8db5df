{"version": 3, "file": "static/chunks/5293-c71567210e3cdd7a.js", "mappings": "wLA0BA,MAlB4B,IACxB,GAAM,GAAEA,CAAC,CAAE,CAAGC,CAAAA,EAAAA,EAAAA,EAAAA,CAAcA,CAAC,MAiBlBC,IAhBX,MACI,SAe0BA,CAfzBC,CAe0B,CAf1BA,CAASA,CAAAA,CAACC,iBAAiB,aACxB,WAACD,EAAAA,CAASA,CAACE,IAAI,EAACC,SAAS,cACrB,UAACH,EAAAA,CAASA,CAACI,MAAM,WACb,UAACC,MAAAA,CAAIC,UAAU,qBAAaT,EAAE,mBAElC,UAACG,EAAAA,CAASA,CAACO,IAAI,WACX,UAACC,EAAAA,CAAUA,CAAAA,CACPC,KAAK,SACLC,GAAIC,GAASA,EAAMC,MAAM,CAAGD,EAAMC,MAAM,CAAC,EAAE,CAAG,aAMtE,2HCEA,MAnB8B,IAC1B,GAAM,GAAEf,CAAC,CAAE,CAAGC,CAAAA,EAAAA,EAAAA,EAAAA,CAAcA,CAAC,MAkBlBe,IAjBX,MACI,UAACb,CAgB2Ba,CAhB3Bb,CAASA,CAAAA,CAACC,iBAAiB,aACxB,WAACD,EAAAA,CAASA,CAACE,IAAI,EAACC,SAAS,cACrB,UAACH,EAAAA,CAASA,CAACI,MAAM,WACb,UAACC,MAAAA,CAAIC,UAAU,qBAAaT,EAAE,oBAElC,UAACG,EAAAA,CAASA,CAACO,IAAI,WACX,UAACO,EAAAA,CAAWA,CAAAA,CACRC,QAASJ,EAAMK,MAAM,CACrBC,YAAaN,EAAMO,UAAU,SAMrD,oICsHA,MA1I0B,IACtB,GAAM,GAAErB,CAAC,CAAE,CAAGC,CAAAA,EAAAA,EAAAA,EAAAA,CAAcA,CAAC,MAyIlBqB,IAxIL,aAwIsBA,EAAC,EAxIrBC,CAAe,kBAAEC,CAAgB,gBAAEC,CAAc,CAAE,CAAGX,EAoH9D,MACI,UAACX,EAAAA,CAASA,CAAAA,CAACC,iBAAiB,aACxB,WAACD,EAAAA,CAASA,CAACE,IAAI,EAACC,SAAS,cACrB,UAACH,EAAAA,CAASA,CAACI,MAAM,WACb,UAACC,MAAAA,CAAIC,UAAU,qBAAaT,EAAE,gBAElC,WAACG,EAAAA,CAASA,CAACO,IAAI,EAACD,UAAU,oCAtHlC,WAACiB,IAAAA,WACG,UAACC,IAAAA,UAAG3B,EAAE,sBAAwB,IAC9B,WAAC4B,OAAAA,WACI,IACAL,EAAgBX,IAAI,CAAGW,EAAgBX,IAAI,CAACiB,KAAK,CAAG,SAO7D,WAACH,IAAAA,WACG,UAACC,IAAAA,UAAG3B,EAAE,aAAe,IACrB,UAAC4B,OAAAA,UACIL,EAAgBO,QAAQ,CACnBP,EAAgBO,QAAQ,CAACC,GAAG,CAAC,CAACC,EAAWC,IACvC,UAACL,OAAAA,UACG,UAACM,KAAAA,UAAIF,EAAKH,KAAK,IADRI,IAIb,QAOd,WAACP,IAAAA,WACG,UAACC,IAAAA,UAAG3B,EAAE,qBAAuB,IAC7B,UAAC4B,OAAAA,UACIJ,GAAoBA,EAAiBW,MAAM,CAAG,EAC3CX,EAAiBO,GAAG,CAAC,CAACC,EAAWI,IAC7B,UAACF,KAAAA,UACG,UAACG,IAAIA,CACDC,KAAM,yBACNC,GAAI,WAFHF,QAE+B,OAATL,EAAKQ,GAAG,WAE9BR,EAAKH,KAAK,IALVO,IAUb,UAACF,KAAAA,UAAIlC,EAAE,kCAQnB,WAAC0B,IAAAA,WACG,UAACC,IAAAA,UAAG3B,EAAE,eAAiB,IACvB,WAAC4B,OAAAA,WACI,IACAL,EAAgBkB,SAAS,CACpBlB,EAAgBkB,SAAS,CAACV,GAAG,CAAC,CAACC,EAAWC,IACxC,WAACC,KAAAA,WACIF,EAAKH,KAAK,CAAC,IAAC,UAACa,KAAAA,CAAAA,KADTT,IAIX,SAOd,WAACP,IAAAA,WACG,UAACC,IAAAA,UAAG3B,EAAE,mBAAqB,IAC3B,UAAC4B,OAAAA,UACIH,GAAkBA,EAAeU,MAAM,CAAG,EACvCV,EAAeM,GAAG,CAAC,CAACC,EAAWI,IAC3B,UAACF,KAAAA,UACG,UAACG,IAAIA,CACDC,KAAM,uBACNC,GAAI,aAFHF,IAE6B,OAATL,EAAKQ,GAAG,WAE5BR,EAAKH,KAAK,IALVO,IAUb,UAACF,KAAAA,UAAIlC,EAAE,gCAQnB,WAAC0B,IAAAA,WACG,UAACC,IAAAA,UAAG3B,EAAE,gBAAkB,IACxB,UAAC4B,OAAAA,UACIL,GAAmBA,EAAgBoB,UAAU,CACxCpB,EAAgBoB,UAAU,CAC1B3C,EAAE,0BAOhB,WAAC0B,IAAAA,WACG,UAACC,IAAAA,UAAG3B,EAAE,UAAY,IAClB,UAAC4B,OAAAA,UACIL,GAAmBA,EAAgBqB,IAAI,CAClCrB,EAAgBqB,IAAI,CACpB5C,EAAE,2BAuBxB,oGCjEA,MA5CgC,GAExB,yBA0CO6C,MAzCH,WAACrC,MAAAA,CAAIC,CAyCsB,SAzCZ,oCACV,CAACK,EAAMgC,YAAY,EAChBhC,EAAMS,eAAe,EACrBT,EAAMS,eAAe,CAACwB,MAAM,EAC5BjC,EAAMS,eAAe,CAACwB,MAAM,CAACP,GAAG,CAChC,UAACQ,MAAAA,CACGvC,UAAU,0BACVwC,IAAK,GAAwCnC,MAAAA,CAArCoC,8BAAsB,CAAC,gBAA+C,OAAjCpC,EAAMS,eAAe,CAACwB,MAAM,CAACP,GAAG,IAGjF,GAEH1B,EAAMgC,YAAY,CACf,UAACtC,MAAAA,CAAIC,UAAU,mCACX,UAACD,MAAAA,CAAIC,UAAU,kCAGnB,GAEH,EAAOqC,YAAY,GAAIhC,EAAMS,eAAe,EAAKT,EAAD,eAAsB,CAACiC,MAAM,CAM1E,GALA,UAACC,MAAAA,CACGvC,UAAU,0BACVwC,IAAI,uCAKZ,UAACzC,MAAAA,CAAIC,UAAU,2CACX,UAAC0C,EAAAA,OAA8BA,CAAAA,CAC3B5B,gBAAiBT,EAAMS,eAAe,CACtC6B,UAAWtC,EAAMuC,IAAI,CACrBA,KAAMvC,EAAMuC,IAAI,CAChBC,WAAYxC,EAAMwC,UAAU,CAC5BC,YAAazC,EAAMyC,WAAW,kKCyCtD,MArF+B,IAC3B,GAAM,GAAEvD,CAAC,CAAE,CAAGC,CAAAA,EAAAA,EAAAA,EAAAA,CAAcA,CAAC,MAoFlBuD,IAjFL,CAACC,EAAOC,EAAS,CAAGC,CAAAA,EAAAA,EAAAA,OAiFOH,CAjFPG,CAAQA,EAAU,GACtC,CAACC,EAAWC,EAAa,CAAGF,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAQ,EAAE,EAC9C,CAACG,EAAWC,EAAa,CAAGJ,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAS,IAG7CK,EAAqB,IAGvB,OAFAN,EAAS,CAACD,GACVM,EAAa/D,EAAEiE,IACPA,GACJ,IAAK,WAKDJ,EAHA/C,EAAMS,SAGO2C,MAHQ,EAAIpD,EAAMS,eAAe,CAAC2C,QAAQ,CAC7CpD,EAAMS,eAAe,CAAC2C,QAAQ,CAC9B,EAAE,EAEZ,KAEJ,KAAK,aAKDL,EAHI/C,EAAMqD,SAGGC,QAHc,EAAItD,EAAMqD,iBAAiB,CAACC,aAAa,CAC1DtD,EAAMqD,iBAAiB,CAACC,aAAa,CACrC,EAAE,EAEZ,KAEJ,KAAK,WAKDP,EAHA/C,EAAMqD,SAGOE,QAHU,EAAIvD,EAAMqD,iBAAiB,CAACE,WAAW,CACpDvD,EAAMqD,iBAAiB,CAACE,WAAW,CACnC,EAAE,CAGpB,CACJ,EACMC,EAAe,IACjBZ,EAASa,EACb,EAGA,MACI,+BACI,WAAC/D,MAAAA,CAAIC,UAAU,0CACX,WAAC+D,EAAAA,CAAGA,CAAAA,WACCC,SAwCZA,CACkC,CACvCzE,CAAwB,CACxBmE,CAMC,EAED,MACI,UAACO,EAAAA,CAAGA,CAAAA,UACA,WAAClE,MAAAA,CACGC,UAAU,mCACVkE,QAAS,IAAMX,EAAmB,sBAElC,UAACxD,MAAAA,CAAIC,UAAU,yBACX,UAACmE,EAAAA,CAAeA,CAAAA,CAACC,KAAMC,EAAAA,GAAOA,CAAEC,MAAM,OAAOC,KAAK,SAEtD,WAACxE,MAAAA,CAAIC,UAAU,0BACX,UAACwE,KAAAA,UAAIjF,EAAE,cACP,UAACkF,KAAAA,UACIf,GAAqBA,EAAkBD,QAAQ,CAC1CC,EAAkBD,QAAQ,CAC1B,WAM9B,EAvEkCF,EAAoBhE,EAAGc,EAAMqD,iBAAiB,EAC5D,UAACO,EAAAA,CAAGA,CAAAA,UACA,WAAClE,MAAAA,CACGC,UAAU,mCACVkE,QAAS,IAAMX,EAAmB,wBAElC,UAACxD,MAAAA,CAAIC,UAAU,yBACX,UAACmE,EAAAA,CAAeA,CAAAA,CACZC,KAAMM,EAAAA,GAAYA,CAClBJ,MAAM,OACNC,KAAK,SAGb,WAACxE,MAAAA,CAAIC,UAAU,0BACX,UAACwE,KAAAA,UAAIjF,EAAE,gBACP,UAACkF,KAAAA,UACIpE,EAAMqD,iBAAiB,EAAIrD,EAAMqD,iBAAiB,CAACiB,UAAU,CACxDtE,EAAMqD,iBAAiB,CAACiB,UAAU,CAClC,YAKtB,UAACV,EAAAA,CAAGA,CAAAA,UACCW,SAiDhBA,CACkC,CACvCrF,CAAwB,CACxBmE,CAMC,EAED,MACI,WAAC3D,MAAAA,CACGC,UAAU,mCACVkE,QAAS,IAAMX,EAAmB,sBAElC,UAACxD,MAAAA,CAAIC,UAAU,yBACV,UAACmE,EAAAA,CAAeA,CAAAA,CAACC,KAAMS,EAAAA,GAAYA,CAAEP,MAAM,OAAOC,KAAK,SAE5D,WAACxE,MAAAA,CAAIC,UAAU,0BACX,UAACwE,KAAAA,UAAIjF,EAAE,cACP,UAACkF,KAAAA,UACIf,GAAqBA,EAAkBoB,QAAQ,CAC1CpB,EAAkBoB,QAAQ,CAC1B,SAK1B,EA9EsCvB,EAAoBhE,EAAGc,EAAMqD,iBAAiB,OAGpE,UAACqB,EAAAA,OAASA,CAAAA,CACNC,OAAQhC,EACRiC,QAAUnB,GAAiBD,EAAaC,GACxCoB,KAAM/B,EACNK,KAAMH,QAK1B,oICzDA,MA1CkB,IAChB,GAAM,GAAE9D,CAAC,CAAE,CAAGC,CAAAA,EAAAA,EAAAA,EAAAA,CAAcA,CAAC,MAyChBuF,IAxCP,CAAEC,IAwCcD,EAAC,EAxCT,SAAEE,CAAO,MAAEC,CAAI,MAAE1B,CAAI,CAAE,CAAGnD,EAClC8E,EAAiB,aAAT3B,EAAsB,UAmC3BA,EAnCuC4B,aAmCjB,cAAgB,YAlCzCC,EACJH,EAAKxD,MAAM,CAAG,EACZwD,EAAK5D,GAAG,CAAC,CAACC,EAAWI,IACnB,WAACR,OAAAA,WACC,UAACS,IAAIA,CAACC,KAAM,IAAkBN,MAAAA,CAAd4D,EAAM,UAAiB,OAAT5D,EAAKQ,GAAG,IAAjCH,OACFL,EAAKH,KAAK,GAEb,UAACkE,KAAAA,CAAAA,KAJQ3D,IAQb,WAACV,IAAAA,WACE1B,EAAE,MAAM,IAAEiE,EAAK,IAAEjE,EAAE,SAAS,OAInC,MACE,WAACgG,EAAAA,CAAKA,CAAAA,CACJC,QAAQ,IACRjB,KAAK,KACLkB,KAAMT,EACNU,OAAQ,IAAMT,EAAQ,CAACD,GACvBW,kBAAgB,wBAEhB,UAACJ,EAAAA,CAAKA,CAACzF,MAAM,EAAC8F,WAAW,aACvB,UAACL,EAAAA,CAAKA,CAACM,KAAK,WAAErC,MAEhB,UAAC+B,EAAAA,CAAKA,CAACtF,IAAI,WACT,UAACF,MAAAA,UAAKsF,QAQd,iJCXA,MApBqChF,IACjC,IAAMyF,EAA0BC,CAAAA,EAAAA,EAAAA,aAmBrBC,UAnBqBD,CAAuBA,CAAC,IACpD,UAACtG,CAkBiCuG,CAlBjCvG,CAkBkC,MAlBfA,CAAAA,CAAE,GAAGY,EAAMuC,IAAI,IAGvC,MACI,iCACI,UAAClD,EAAAA,CAASA,CAAAA,CAACM,UAAU,+BACjB,UAACa,EAAAA,OAAiBA,CAAAA,CAAE,GAAGR,CAAK,KAEhC,UAACX,EAAAA,CAASA,CAAAA,CAACM,UAAU,+BAClB,UAACO,EAAAA,OAAqBA,CAAAA,CAAE,GAAGF,EAAMS,eAAe,KAEnD,UAACpB,EAAAA,CAASA,CAAAA,CAACM,UAAU,+BACjB,UAAC8F,EAAAA,CAAAA,OAIjB,yBC3BuC,CAGtC,YAA4B,aAI7B,oBACA,OACA,iCACA,iCACA,0BACA,0BACA,+BACA,8BACA,8BACA,4BACA,6BAEA,yBAGA,qBACA,qGACA,KAEA,YACA,2EACA,oBACA,SACA,oEACA,KAEA,uDACA,8CACA,sBACA,gBACA,WACA,eACA,eACA,kBACA,yBACA,+BACA,CAAS,CACT,UACA,8BACA,aACA,+BACA,8BACA,gCACA,uCACA,CAAS,CACT,cACA,eACA,cACA,sBACA,iBACA,IACA,gBACA,IACA,gBACA,IACA,KACA,IACA,eACA,IACA,KACA,IACA,IACA,CAAS,CACT,2BAAoC,IAAI,IACxC,cACA,MACA,MACA,KACA,CAAS,CACJ,EAIL,CAAC,CAhFiD,EAAQ,KAAW,YAAZ,8KCoRzD,MAAeG,CAAAA,EAAAA,EAAAA,EAAAA,CAAOA,CAAC,GAAWC,GA3QT7F,IA2BrB,GAAM,CAACS,EAAiBqF,EAAmB,CAAGjD,CAAAA,EAAAA,EAAAA,EAgPOkD,EAAC,IAhPRlD,CAAQA,CAACmD,CAzBnDjF,MAAO,GACPkF,YAAa,GACbC,UAAW,GACXC,UAAW,GACXC,QAAS,CACLC,OAAQ,GACRC,OAAQ,GACRC,KAAM,EACV,EACAC,QAAS,GACT1G,KAAM,CACFiB,MAAO,EACX,EACAC,SAAU,EAAE,CACZW,UAAW,EAAE,CACb8E,aAAc,EAAE,CAChBC,oBAAqB,GACrBrG,OAAQ,EAAE,CACV4B,OAAQ,GACRJ,WAAY,GACZC,KAAM,GACNsB,SAAU,EAAE,CACZ7C,WAAY,EAAE,GAIZ,CAAC8C,EAAmBsD,EAAqB,CAAG9D,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAC,CACvDO,SAAU,EACVkB,WAAY,EACZG,SAAU,EACVnB,cAAe,EAAE,CACjBC,YAAa,EAAE,GAEb,CAAC7C,EAAkBkG,EAAoB,CAAG/D,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAC,EAAE,EACrD,CAAClC,EAAgBkG,EAAkB,CAAGhE,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAC,EAAE,EACjD,CAACJ,EAAaqE,EAAe,CAAGjE,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAQ,EAAE,EAElD,CAACb,EAAc+E,EAAgB,CAAGlE,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,EAAC,GAC3C,CAACL,EAAYwE,EAAc,CAAGnE,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,EAAC,GAEvCoE,EAAuB,MAAOC,IAChCH,GAAgB,GAChB,IAAMI,EAAuB,MAAMC,EAAAA,CAAUA,CAACC,GAAG,CAC7C,gBAAgC,OAAhBrH,EAAMC,MAAM,CAAC,EAAE,GAEnC8G,GAAgB,GAgBpB,SAASO,CAAqC,CAAEC,CAAc,EAC1DP,GAAc,GACVO,GAAaA,EAAU,KAAQ,EAAT,CAClBA,EAAU,KAAQ,CAACC,CAAV,OAAkB,CAAC,gBAAgB,EAG3B,KAAQ,CAACA,CAAV,OAAkB,CAAC,mBAAqB/G,EAAgBgH,IAAI,EAAIF,EAAU,GAAM,CAEhGP,CAFkG,EAAT,GAIpFO,EAAU,KAAQ,CAACC,CAAV,OAAkB,CAAC,iBAAmB/G,EAAgBgH,IAAI,EAAIF,EAAU,GAAM,EAAE,EAAT,CAEvE,GAG1B,EA9BqBJ,EAAsBD,GACvCC,EAAqBjB,SAAS,CAC1BiB,GAAwBA,EAAqBjB,SAAS,CAChDiB,EAAqBjB,SAAS,CAC9B,GACVJ,EAAmBqB,EACvB,EAEMO,EAAyB,UAI3Bf,EAH+B,MAAMS,EAAAA,CAAUA,CAACC,GAAG,CAC/C,KAEiBM,iBAFqB,OAAhB3H,EAAMC,MAAM,CAAC,EAAE,GAG7C,EAmBM2H,EAAkB,UACpB,IAAM/C,EAAO,MAAMuC,EAAAA,CAAUA,CAACS,IAAI,CAAC,uBAAwB,CAAC,GAC5D,GAAIhD,GAAQA,EAAKiD,KAAK,EAAIjD,EAAKiD,KAAK,CAACzG,MAAM,CACvC,CADyC,EACrCwD,EAAKiD,KAAK,CAACN,QAAQ,CAAC,eACpBO,CADoC,CACzB,WAAYlD,OACpB,CACH,IAAImD,EAAUnD,EAAKoD,kBAAkB,CAACC,MAAM,CACxC,GAAiBC,EAAOC,aAAa,GAAKpI,EAAMC,MAAM,CAAC,EAAE,EAEzD+H,GAAWA,EAAQ3G,MAAM,CACzB0G,CAD2B,CAChBC,CAAO,CAAC,EAAE,CAACK,MAAM,CAAExD,GAE9BkD,EAAW,wBAAyBlD,EAE5C,CAER,EAEMkD,EAAa,CAACM,EAAanB,KAE7BD,EAAqBC,GACrBQ,IACAY,EAAmCtI,EAAMC,MAAM,CAAC,EAAE,EAClDsI,EAAkCvI,EAAMC,MAAM,CAAC,EAAE,CAErD,EAEAuI,CAAAA,EAAAA,EAAAA,SAAAA,CAASA,CAAC,KACFxI,EAAMC,MAAM,EAAID,EAAMC,MAAM,CAAC,EAAE,EAAE,GAGzC,EAAG,EAAE,EAELuI,CAAAA,EAAAA,EAAAA,SAAAA,CAASA,CAAC,KACF/H,GAAmBA,EAAgBgG,YAAY,EAAIhG,EAAgBgG,YAAY,CAACpF,MAAM,CAAG,GAEzFoH,EADwBC,IAAAA,GAAK,CAACjI,EAAgBgG,GAC/BkC,SAD2C,CAAE,OAGpE,EAAG,CAAClI,EAAgB,EAEpB,IAAM6H,EAAqC,MAAOF,IAC9C,IAAMQ,EAAW,MAAMC,IACvB,GAAID,GAAYA,EAASvH,MAAM,CAAG,EAAG,CACjC,IAAMyH,EAAa,MAAM1B,EAAAA,CAAUA,CAACC,GAAG,CAAC,aAAc,CAClD0B,MAAO,CACH,uBAAwB,CAACX,EAAc,CACvCC,OAAQO,CACZ,CACJ,GACIE,GAAcA,EAAWjE,IAAI,EAAIiE,EAAWjE,IAAI,CAACxD,MAAM,CAAG,GAAG,EACzCyH,EAAWjE,IAAI,CAE3C,CACJ,EAEMgE,EAAuB,UACzB,IAAMG,EAAW,MAAM5B,EAAAA,CAAUA,CAACC,GAAG,CAAC,qBACtC,GAAI2B,GAAYA,EAASnE,IAAI,EAAImE,EAASnE,IAAI,CAACxD,MAAM,CAAG,EAAG,CACvD,IAAMuH,EAAkB,EAAE,CAW1B,OAVAF,IAAAA,OAAS,CAACM,EAASnE,IAAI,CAAE,SAAU3D,CAAI,GAEhB,aAAfA,EAAKH,KAAK,EACK,eAAfG,EAAKH,KAAK,EACK,eAAfG,EAAKH,KAAK,EACVG,cAAKH,KAAK,GACZ,EACWkI,IAAI,CAAC/H,EAAKQ,GAAG,CAE9B,GACOkH,CACX,CACA,MAAO,EAAE,EAGPL,EAAoC,MAAOH,IAC7C,IAAMQ,EAAW,MAAMM,IACvB,GAAIN,GAAYA,EAASvH,MAAM,CAAG,EAAG,CACjC,IAAM8H,EAAkB,MAAM/B,EAAAA,CAAUA,CAACC,GAAG,CAAC,WAAY,CACrD0B,MAAO,CACH,2CAA4C,CAACX,EAAc,CAC3DC,OAAQO,CACZ,CACJ,GACIO,GAAmBA,EAAgBtE,IAAI,EAAIsE,EAAgBtE,IAAI,CAACxD,MAAM,CAAG,GAAG,EAC1D8H,EAAgBtE,IAAI,CAE9C,CACJ,EAEMqE,EAAqB,UACvB,IAAMF,EAAW,MAAM5B,EAAAA,CAAUA,CAACC,GAAG,CAAC,kBACtC,GAAI2B,GAAYA,EAASnE,IAAI,EAAImE,EAASnE,IAAI,CAACxD,MAAM,CAAG,EAAG,CACvD,IAAMuH,EAAkB,EAAE,CAM1B,OALAF,IAAAA,OAAS,CAACM,EAASnE,IAAI,CAAE,SAAU3D,CAAI,GAChB,YAAfA,EAAKH,KAAK,EAAiC,aAAfG,EAAKH,KAAK,GAAiB,EAC9CkI,IAAI,CAAC/H,EAAKQ,GAAG,CAE9B,GACOkH,CACX,CACA,MAAO,EAAE,EAGPH,EAAiB,MAAOW,IAQ1B,IAAMC,EAAa,MAAMjC,EAAAA,CAAUA,CAACC,GAAG,CAAC,SAPpB,CAO8BiC,MANvC,CAAE5H,IAAK0H,CAAM,EACpBG,KAAM,CAAExI,MAAO,KAAM,EACrByI,MAAO,IACPC,OACI,2SACR,GAEIC,EAAmB,EAAE,UACzBL,EAAYxE,IAAI,CAAC8E,MAAjBN,CAAwB,CAAC,UACrB5B,GAAAA,EAAMQ,YAANR,MAAwB,CAACkC,OAAO,CAAEC,IAE1BA,GAC6B,aAA7BA,EAAkBvB,MAAM,EACxBuB,EAAkBxB,aAAa,GAAKpI,EAAMC,MAAM,CAAC,EAAE,EACrD,EACYgJ,IAAI,CAAC,CACX,GAAGxB,CAAI,CACP,GAAG,CACCW,cAAewB,EAAkBxB,aAAa,CAC9CyB,gBAAiBD,EAAkBC,eAAe,CAClDxG,kBAAmBuG,EAAkBvB,MAAM,CAC9C,EAGb,EACJ,GACIqB,GAAaA,EAAUrI,MAAM,CAAG,GAAG,EACjBqI,EACbzI,GAAG,CAAC,GAAU,CACX,CACI,EAQG6I,CARA5I,CAAI,CACP6I,UAAW7I,EAAKQ,GAAG,GAAKjB,EAAgBiG,mBAAmB,EAElE,EACAsD,IAAI,GAEJT,IAAI,CAAC,CAACU,EAAGpJ,IAAMoJ,EAAEF,SAAS,CAAGlJ,EAAEkJ,SAAS,EACxCG,OAAO,GAGpB,EAqBA,MACI,UAACxK,MAAAA,UAGW,iCACI,UAACyK,EAAAA,CAASA,CAAAA,CAACC,KAAK,IAACzK,UAAU,kCACvB,UAAC0K,EAAAA,CAAWA,CAAAA,CAACpK,OAAQD,EAAMC,MAAM,KAGrC,UAAC8B,EAAAA,OAAuBA,CAAAA,CA3BxCQ,KAAMvC,EACNgC,aAAcA,EACdvB,gBAAiBA,EACjB+B,WAAYA,EACZC,YAAaA,IAyBG,UAACC,EAAAA,OAAsBA,CAAAA,CArBvCjC,gBAAiBA,EACjB4C,kBAAmBA,IAsBH,UAACsC,EAAAA,OAA2BA,CAAAA,CAlB5ClF,gBAAiBA,EACjB8B,KAAMvC,EACNW,eAAgBA,EAChBD,iBAAkBA,QAqB1B", "sources": ["webpack://_N_E/./pages/institution/components/DiscussionAccordion.tsx", "webpack://_N_E/./pages/institution/components/MediaGalleryAccordion.tsx", "webpack://_N_E/./pages/institution/components/MoreInfoAccordion.tsx", "webpack://_N_E/./pages/institution/components/InstitutionCoverSection.tsx", "webpack://_N_E/./pages/institution/components/InstitutionInfoSection.tsx", "webpack://_N_E/./pages/institution/InfoPopup.tsx", "webpack://_N_E/./pages/institution/components/InstitutionAccordionSection.tsx", "webpack://_N_E/./node_modules/moment/locale/de.js", "webpack://_N_E/./pages/institution/InstitutionShow.tsx"], "sourcesContent": ["//Import Library\r\nimport React from \"react\";\r\nimport { Accordion } from \"react-bootstrap\";\r\n\r\n//Import services/components\r\nimport Discussion from \"../../../components/common/disussion\";\r\nimport { useTranslation } from 'next-i18next';\r\n\r\nconst DiscussionAccordion = (props: any) => {\r\n    const { t } = useTranslation('common');\r\n    return (\r\n        <Accordion defaultActiveKey=\"2\">\r\n            <Accordion.Item eventKey=\"2\">\r\n                <Accordion.Header>\r\n                    <div className=\"cardTitle\">{t(\"Discussions\")}</div>\r\n                </Accordion.Header>\r\n                <Accordion.Body>\r\n                    <Discussion\r\n                        type=\"hazard\"\r\n                        id={props && props.routes ? props.routes[1] : null}\r\n                    />\r\n                </Accordion.Body>\r\n            </Accordion.Item>\r\n        </Accordion>\r\n    );\r\n};\r\nexport default DiscussionAccordion;", "//Import Library\r\nimport React from \"react\";\r\nimport { Accordion } from \"react-bootstrap\";\r\n\r\n//Import services/components\r\nimport ReactImages from \"../../../components/common/ReactImages\";\r\nimport { useTranslation } from 'next-i18next';\r\n\r\nconst MediaGalleryAccordion = (props: any) => {\r\n    const { t } = useTranslation('common');\r\n    return (\r\n        <Accordion defaultActiveKey=\"1\">\r\n            <Accordion.Item eventKey=\"1\">\r\n                <Accordion.Header>\r\n                    <div className=\"cardTitle\">{t(\"MediaGallery\")}</div>\r\n                </Accordion.Header>\r\n                <Accordion.Body>\r\n                    <ReactImages\r\n                        gallery={props.images}\r\n                        imageSource={props.images_src}\r\n                    />\r\n                </Accordion.Body>\r\n            </Accordion.Item>\r\n        </Accordion>\r\n    )\r\n}\r\n\r\nexport default MediaGalleryAccordion;", "import React from \"react\";\r\nimport { Accordion } from \"react-bootstrap\";\r\nimport Link from \"next/link\";\r\nimport { useTranslation } from 'next-i18next';\r\n\r\nconst MoreInfoAccordion = (props: any) => {\r\n    const { t } = useTranslation('common');\r\n    const { institutionData, activeOperations, activeProjects } = props;\r\n\r\n    // Render organization type\r\n    const renderOrganizationType = () => (\r\n        <p>\r\n            <b>{t(\"OrganisationType\")}</b>:\r\n            <span>\r\n                {\" \"}\r\n                {institutionData.type ? institutionData.type.title : \"\"}\r\n            </span>\r\n        </p>\r\n    );\r\n\r\n    // Render networks\r\n    const renderNetworks = () => (\r\n        <p>\r\n            <b>{t(\"Network\")}</b>:\r\n            <span>\r\n                {institutionData.networks\r\n                    ? institutionData.networks.map((item: any, index: any) => (\r\n                        <span key={index}>\r\n                            <li>{item.title}</li>\r\n                        </span>\r\n                    ))\r\n                    : \"\"}\r\n            </span>\r\n        </p>\r\n    );\r\n\r\n    // Render active operations\r\n    const renderActiveOperations = () => (\r\n        <p>\r\n            <b>{t(\"ActiveOperation\")}</b>:\r\n            <span>\r\n                {activeOperations && activeOperations.length > 0 ? (\r\n                    activeOperations.map((item: any, i: any) => (\r\n                        <li key={i}>\r\n                            <Link\r\n                                href={\"/operation/[...routes]\"}\r\n                                as={`/operation/show/${item._id}`}\r\n                            >\r\n                                {item.title}\r\n                            </Link>\r\n                        </li>\r\n                    ))\r\n                ) : (\r\n                    <li>{t(\"NoActiveoperationsfound\")}</li>\r\n                )}\r\n            </span>\r\n        </p>\r\n    );\r\n\r\n    // Render expertise\r\n    const renderExpertise = () => (\r\n        <p>\r\n            <b>{t(\"Expertise\")}</b>:\r\n            <span>\r\n                {\" \"}\r\n                {institutionData.expertise\r\n                    ? institutionData.expertise.map((item: any, index: any) => (\r\n                        <li key={index}>\r\n                            {item.title} <br />\r\n                        </li>\r\n                    ))\r\n                    : \"\"}\r\n            </span>\r\n        </p>\r\n    );\r\n\r\n    // Render active projects\r\n    const renderActiveProjects = () => (\r\n        <p>\r\n            <b>{t(\"ActiveProject\")}</b>:\r\n            <span>\r\n                {activeProjects && activeProjects.length > 0 ? (\r\n                    activeProjects.map((item: any, i: any) => (\r\n                        <li key={i}>\r\n                            <Link\r\n                                href={\"/project/[...routes]\"}\r\n                                as={`/project/show/${item._id}`}\r\n                            >\r\n                                {item.title}\r\n                            </Link>\r\n                        </li>\r\n                    ))\r\n                ) : (\r\n                    <li>{t(\"NoActiveprojectsfound\")}</li>\r\n                )}\r\n            </span>\r\n        </p>\r\n    );\r\n\r\n    // Render department\r\n    const renderDepartment = () => (\r\n        <p>\r\n            <b>{t(\"Department\")}</b>:\r\n            <span>\r\n                {institutionData && institutionData.department\r\n                    ? institutionData.department\r\n                    : t(\"Nodepartmentfound\")}\r\n            </span>\r\n        </p>\r\n    );\r\n\r\n    // Render unit\r\n    const renderUnit = () => (\r\n        <p>\r\n            <b>{t(\"Unit\")}</b>:\r\n            <span>\r\n                {institutionData && institutionData.unit\r\n                    ? institutionData.unit\r\n                    : t(\"Nounitfound\")}\r\n            </span>\r\n        </p>\r\n    );\r\n\r\n    return (\r\n        <Accordion defaultActiveKey=\"0\">\r\n            <Accordion.Item eventKey=\"0\">\r\n                <Accordion.Header>\r\n                    <div className=\"cardTitle\">{t(\"MoreInfo\")}</div>\r\n                </Accordion.Header>\r\n                <Accordion.Body className=\"institutionDetails ps-4\">\r\n                    {renderOrganizationType()}\r\n                    {renderNetworks()}\r\n                    {renderActiveOperations()}\r\n                    {renderExpertise()}\r\n                    {renderActiveProjects()}\r\n                    {renderDepartment()}\r\n                    {renderUnit()}\r\n                </Accordion.Body>\r\n            </Accordion.Item>\r\n        </Accordion>\r\n    );\r\n};\r\n\r\nexport default MoreInfoAccordion;\r\n", "//Import Library\r\nimport React from \"react\";\r\n\r\n//Import services/components\r\nimport InstitutionCoverSectionContent from \"./InstitutionCoverSectionContent\";\r\n\r\ninterface InstitutionCoverSectionProps {\r\n  prop: any;\r\n  imageLoading: boolean;\r\n  institutionData: {\r\n    header?: {\r\n      _id: string;\r\n    };\r\n    title: string;\r\n    description: string;\r\n    website?: string;\r\n    telephone?: string;\r\n    dial_code?: string;\r\n    address?: {\r\n      line_1?: string;\r\n      line_2?: string;\r\n      city?: string;\r\n      postal_code?: string;\r\n      country?: {\r\n        title: string;\r\n      };\r\n    };\r\n  };\r\n  editAccess: boolean;\r\n  focalPoints: any[];\r\n}\r\n\r\nconst InstitutionCoverSection = (props: InstitutionCoverSectionProps) => {\r\n    return (\r\n        <>\r\n            <div className=\"institution-image-block\">\r\n                {!props.imageLoading &&\r\n                    props.institutionData &&\r\n                    props.institutionData.header &&\r\n                    props.institutionData.header._id ? (\r\n                    <img\r\n                        className=\"institution-image-cover\"\r\n                        src={`${process.env.API_SERVER}/image/show/${props.institutionData.header._id}`}\r\n                    />\r\n                ) : (\r\n                    \"\"\r\n                )}\r\n                {props.imageLoading ? (\r\n                    <div className=\"institution-imageLoader\">\r\n                        <div className=\"spinner-border text-primary\" />\r\n                    </div>\r\n                ) : (\r\n                    \"\"\r\n                )}\r\n                {!props.imageLoading && props.institutionData && !props.institutionData.header ? (\r\n                    <img\r\n                        className=\"institution-image-cover\"\r\n                        src=\"/images/rki_institute.7cb751d6.jpg\"\r\n                    />\r\n                ) : (\r\n                    \"\"\r\n                )}\r\n                <div className=\"institution-image-inner-content\">\r\n                    <InstitutionCoverSectionContent\r\n                        institutionData={props.institutionData}\r\n                        routeData={props.prop}\r\n                        prop={props.prop}\r\n                        editAccess={props.editAccess}\r\n                        focalPoints={props.focalPoints}\r\n                    />\r\n                </div>\r\n            </div>\r\n        </>\r\n    )\r\n}\r\n\r\nexport default InstitutionCoverSection;", "//Import Library\r\nimport React, { useState } from \"react\";\r\nimport { faFolder<PERSON><PERSON>, faLayerGroup, faUsers } from \"@fortawesome/free-solid-svg-icons\";\r\nimport { FontAwesomeIcon } from \"@fortawesome/react-fontawesome\";\r\nimport { Col, Row } from \"react-bootstrap\";\r\n\r\n//Import services/components\r\nimport { useTranslation } from 'next-i18next';\r\nimport InfoPopup from \"../InfoPopup\";\r\n\r\ninterface InstitutionInfoSectionProps {\r\n  institutionData: {\r\n    description: string;\r\n    partners?: any[];\r\n  };\r\n  institutionStatus: {\r\n    partners: number;\r\n    operations: number;\r\n    projects: number;\r\n    operationData: any[];\r\n    projectData: any[];\r\n  };\r\n}\r\n\r\nconst InstitutionInfoSection = (props: InstitutionInfoSectionProps) => {\r\n    const { t } = useTranslation('common');\r\n\r\n    /***For Qucik Info Modal***/\r\n    const [popup, setPopup] = useState<boolean>(false);\r\n    const [quickInfo, setQuickInfo] = useState<any[]>([]);\r\n    const [fieldName, setFieldName] = useState<string>(\"\");\r\n\r\n    /***Handle Partner List***/\r\n    const partnerListHandler = (name: string) => {\r\n        setPopup(!popup);\r\n        setFieldName(t(name));\r\n        switch (name) {\r\n            case \"Partners\":\r\n                const partners =\r\n                props.institutionData && props.institutionData.partners\r\n                        ? props.institutionData.partners\r\n                        : [];\r\n                setQuickInfo(partners);\r\n                break;\r\n\r\n            case \"Operations\":\r\n                const operationData =\r\n                    props.institutionStatus && props.institutionStatus.operationData\r\n                        ? props.institutionStatus.operationData\r\n                        : [];\r\n                setQuickInfo(operationData);\r\n                break;\r\n\r\n            case \"Projects\":\r\n                const projectData =\r\n                props.institutionStatus && props.institutionStatus.projectData\r\n                        ? props.institutionStatus.projectData\r\n                        : [];\r\n                setQuickInfo(projectData);\r\n                break;\r\n        }\r\n    };\r\n    const closeHandler = (val: boolean) => {\r\n        setPopup(val);\r\n    };\r\n\r\n\r\n    return (\r\n        <>\r\n            <div className=\"institution-infographic-block\">\r\n                <Row>\r\n                    {partner_func(partnerListHandler, t, props.institutionStatus)}\r\n                    <Col>\r\n                        <div\r\n                            className=\"list-group-item d-flex clickable\"\r\n                            onClick={() => partnerListHandler(\"Operations\")}\r\n                        >\r\n                            <div className=\"quickinfo-img\">\r\n                                <FontAwesomeIcon\r\n                                    icon={faLayerGroup}\r\n                                    color=\"#fff\"\r\n                                    size=\"2x\"\r\n                                />\r\n                            </div>\r\n                            <div className=\"quickinfoDesc\">\r\n                                <h5>{t(\"Operations\")}</h5>\r\n                                <h4>\r\n                                    {props.institutionStatus && props.institutionStatus.operations\r\n                                        ? props.institutionStatus.operations\r\n                                        : 0}\r\n                                </h4>\r\n                            </div>\r\n                        </div>\r\n                    </Col>\r\n                    <Col>\r\n                        {project_func(partnerListHandler, t, props.institutionStatus)}\r\n                    </Col>\r\n                </Row>\r\n                <InfoPopup\r\n                    isShow={popup}\r\n                    isClose={(val: boolean) => closeHandler(val)}\r\n                    data={quickInfo}\r\n                    name={fieldName}\r\n                />\r\n            </div>\r\n        </>\r\n    )\r\n}\r\n\r\nexport default InstitutionInfoSection;\r\n\r\nfunction partner_func(\r\n    partnerListHandler: (name: any) => void,\r\n    t: (p: string) => string ,\r\n    institutionStatus: {\r\n        partners: number;\r\n        operations: number;\r\n        projects: number;\r\n        operationData: any[];\r\n        projectData: any[];\r\n    }\r\n) {\r\n    return (\r\n        <Col>\r\n            <div\r\n                className=\"list-group-item d-flex clickable\"\r\n                onClick={() => partnerListHandler(\"Partners\")}\r\n            >\r\n                <div className=\"quickinfo-img\">\r\n                    <FontAwesomeIcon icon={faUsers} color=\"#fff\" size=\"2x\" />\r\n                </div>\r\n                <div className=\"quickinfoDesc\">\r\n                    <h5>{t(\"Partners\")}</h5>\r\n                    <h4>\r\n                        {institutionStatus && institutionStatus.partners\r\n                            ? institutionStatus.partners\r\n                            : 0}\r\n                    </h4>\r\n                </div>\r\n            </div>\r\n        </Col>\r\n    );\r\n}\r\n\r\nfunction project_func(\r\n    partnerListHandler: (name: any) => void,\r\n    t: (p: string) => string,\r\n    institutionStatus: {\r\n        partners: number;\r\n        operations: number;\r\n        projects: number;\r\n        operationData: any[];\r\n        projectData: any[];\r\n    }\r\n) {\r\n    return (\r\n        <div\r\n            className=\"list-group-item d-flex clickable\"\r\n            onClick={() => partnerListHandler(\"Projects\")}\r\n        >\r\n            <div className=\"quickinfo-img\">\r\n                {<FontAwesomeIcon icon={faFolderOpen} color=\"#fff\" size=\"2x\" />}\r\n            </div>\r\n            <div className=\"quickinfoDesc\">\r\n                <h5>{t(\"Projects\")}</h5>\r\n                <h4>\r\n                    {institutionStatus && institutionStatus.projects\r\n                        ? institutionStatus.projects\r\n                        : 0}\r\n                </h4>\r\n            </div>\r\n        </div>\r\n    );\r\n}\r\n\r\n", "//Import Library\r\nimport React from \"react\";\r\nimport { Modal } from \"react-bootstrap\";\r\nimport Link from \"next/link\";\r\n\r\n//Import services/components\r\nimport { useTranslation } from 'next-i18next';\r\n\r\nconst InfoPopup = (props: any) => {\r\n  const { t } = useTranslation('common');\r\n  const { isShow, isClose, data, name } = props;\r\n  const route = name === \"Projects\" ? \"project\" : arrowfun();\r\n  const titles =\r\n    data.length > 0 ? (\r\n      data.map((item: any, i: any) => (\r\n        <span key={i}>\r\n          <Link href={`/${route}/show/${item._id}`}>\r\n            {item.title}\r\n          </Link>\r\n          <hr />\r\n        </span>\r\n      ))\r\n    ) : (\r\n      <p>\r\n        {t(\"No\")} {name} {t(\"Found\")}.\r\n      </p>\r\n    );\r\n\r\n  return (\r\n    <Modal\r\n      centered\r\n      size=\"sm\"\r\n      show={isShow}\r\n      onHide={() => isClose(!isShow)}\r\n      aria-labelledby=\"modal_popup\"\r\n    >\r\n      <Modal.Header closeButton>\r\n        <Modal.Title>{name}</Modal.Title>\r\n      </Modal.Header>\r\n      <Modal.Body>\r\n        <div>{titles}</div>\r\n      </Modal.Body>\r\n    </Modal>\r\n  );\r\n\r\n  function arrowfun() {\r\n    return name === \"Partners\" ? \"institution\" : \"operation\";\r\n  }\r\n};\r\n\r\nexport default InfoPopup;\r\n", "//Import Library\r\nimport React from \"react\";\r\nimport { Accordion } from \"react-bootstrap\";\r\n\r\n//Import services/components\r\nimport DiscussionAccordion from \"./DiscussionAccordion\";\r\nimport { canViewDiscussionUpdate } from \"../permission\";\r\nimport MoreInfoAccordion from \"./MoreInfoAccordion\";\r\nimport MediaGalleryAccordion from \"./MediaGalleryAccordion\";\r\n\r\ninterface InstitutionAccordionSectionProps {\r\n  institutionData: any;\r\n  prop: any;\r\n  activeProjects: any[];\r\n  activeOperations: any[];\r\n}\r\n\r\nconst InstitutionAccordionSection = (props: InstitutionAccordionSectionProps) => {\r\n    const CanViewDiscussionUpdate = canViewDiscussionUpdate(() => (\r\n        <DiscussionAccordion {...props.prop} />\r\n      ));\r\n\r\n    return (\r\n        <>\r\n            <Accordion className=\"countryAccordionNew\">\r\n                <MoreInfoAccordion {...props} />\r\n            </Accordion>\r\n            <Accordion className=\"countryAccordionNew\">\r\n               <MediaGalleryAccordion {...props.institutionData} />\r\n            </Accordion>\r\n            <Accordion className=\"countryAccordionNew\">\r\n                <CanViewDiscussionUpdate />\r\n            </Accordion>\r\n        </>\r\n    )\r\n}\r\n\r\nexport default InstitutionAccordionSection;", "//! moment.js locale configuration\n//! locale : German [de]\n//! author : lluchs : https://github.com/lluchs\n//! author: <PERSON><PERSON><PERSON>: https://github.com/Oire\n//! author : <PERSON><PERSON><PERSON><PERSON> : https://github.com/mik01aj\n\n;(function (global, factory) {\n   typeof exports === 'object' && typeof module !== 'undefined'\n       && typeof require === 'function' ? factory(require('../moment')) :\n   typeof define === 'function' && define.amd ? define(['../moment'], factory) :\n   factory(global.moment)\n}(this, (function (moment) { 'use strict';\n\n    //! moment.js locale configuration\n\n    function processRelativeTime(number, withoutSuffix, key, isFuture) {\n        var format = {\n            m: ['eine Minute', 'einer Minute'],\n            h: ['eine Stunde', 'einer Stunde'],\n            d: ['ein Tag', 'einem Tag'],\n            dd: [number + ' Tage', number + ' Tagen'],\n            w: ['eine Woche', 'einer Woche'],\n            M: ['ein <PERSON><PERSON>', 'einem Monat'],\n            MM: [number + ' <PERSON><PERSON>', number + ' Monaten'],\n            y: ['ein Jahr', 'einem Jahr'],\n            yy: [number + ' Jahre', number + ' Jahren'],\n        };\n        return withoutSuffix ? format[key][0] : format[key][1];\n    }\n\n    var de = moment.defineLocale('de', {\n        months: 'Januar_Februar_März_April_Mai_Juni_Juli_August_September_Oktober_November_Dezember'.split(\n            '_'\n        ),\n        monthsShort:\n            'Jan._Feb._März_Apr._Mai_Juni_Juli_Aug._Sep._Okt._Nov._Dez.'.split('_'),\n        monthsParseExact: true,\n        weekdays:\n            'Sonntag_Montag_Dienstag_Mittwoch_Donnerstag_Freitag_Samstag'.split(\n                '_'\n            ),\n        weekdaysShort: 'So._Mo._Di._Mi._Do._Fr._Sa.'.split('_'),\n        weekdaysMin: 'So_Mo_Di_Mi_Do_Fr_Sa'.split('_'),\n        weekdaysParseExact: true,\n        longDateFormat: {\n            LT: 'HH:mm',\n            LTS: 'HH:mm:ss',\n            L: 'DD.MM.YYYY',\n            LL: 'D. MMMM YYYY',\n            LLL: 'D. MMMM YYYY HH:mm',\n            LLLL: 'dddd, D. MMMM YYYY HH:mm',\n        },\n        calendar: {\n            sameDay: '[heute um] LT [Uhr]',\n            sameElse: 'L',\n            nextDay: '[morgen um] LT [Uhr]',\n            nextWeek: 'dddd [um] LT [Uhr]',\n            lastDay: '[gestern um] LT [Uhr]',\n            lastWeek: '[letzten] dddd [um] LT [Uhr]',\n        },\n        relativeTime: {\n            future: 'in %s',\n            past: 'vor %s',\n            s: 'ein paar Sekunden',\n            ss: '%d Sekunden',\n            m: processRelativeTime,\n            mm: '%d Minuten',\n            h: processRelativeTime,\n            hh: '%d Stunden',\n            d: processRelativeTime,\n            dd: processRelativeTime,\n            w: processRelativeTime,\n            ww: '%d Wochen',\n            M: processRelativeTime,\n            MM: processRelativeTime,\n            y: processRelativeTime,\n            yy: processRelativeTime,\n        },\n        dayOfMonthOrdinalParse: /\\d{1,2}\\./,\n        ordinal: '%d.',\n        week: {\n            dow: 1, // Monday is the first day of the week.\n            doy: 4, // The week that contains Jan 4th is the first week of the year.\n        },\n    });\n\n    return de;\n\n})));\n", "//Import Library\r\nimport React, { useState, useEffect } from \"react\";\r\nimport { connect } from \"react-redux\";\r\nimport { Container } from \"react-bootstrap\";\r\nimport _ from \"lodash\";\r\n\r\n//Import services/components\r\nimport UpdatePopup from \"../../components/updates/UpdatePopup\";\r\nimport apiService from \"../../services/apiService\";\r\nimport InstitutionCoverSection from \"./components/InstitutionCoverSection\";\r\nimport InstitutionInfoSection from \"./components/InstitutionInfoSection\";\r\nimport InstitutionAccordionSection from \"./components/InstitutionAccordionSection\";\r\n\r\ninterface InstitutionShowProps {\r\n  routes: string[];\r\n}\r\n\r\nconst InstitutionShow = (props: InstitutionShowProps) => {\r\n    const initialVal: any = {\r\n        title: \"\",\r\n        description: \"\",\r\n        dial_code: \"\",\r\n        telephone: \"\",\r\n        address: {\r\n            line_1: \"\",\r\n            line_2: \"\",\r\n            city: \"\",\r\n        },\r\n        website: \"\",\r\n        type: {\r\n            title: \"\",\r\n        },\r\n        networks: [],\r\n        expertise: [],\r\n        focal_points: [],\r\n        primary_focal_point: \"\",\r\n        images: [],\r\n        header: \"\",\r\n        department: \"\",\r\n        unit: \"\",\r\n        partners: [],\r\n        images_src: [],\r\n    };\r\n\r\n    const [institutionData, setInstitutionData] = useState(initialVal);\r\n    const [institutionStatus, setInstitutionStatus] = useState({\r\n        partners: 0,\r\n        operations: 0,\r\n        projects: 0,\r\n        operationData: [],\r\n        projectData: [],\r\n    });\r\n    const [activeOperations, setActiveOperations] = useState([]);\r\n    const [activeProjects, setActiveProjects] = useState([]);\r\n    const [focalPoints, setFocalPoints] = useState<any[]>([]);\r\n    /***End***/\r\n    const [imageLoading, setImageLoading] = useState(true);\r\n    const [editAccess, setEditAccess] = useState(false);\r\n\r\n    const fetchInstitutionData = async (loginUserData: any) => {\r\n        setImageLoading(true);\r\n        const institutionDatavalue = await apiService.get(\r\n            `/institution/${props.routes[1]}`\r\n        );\r\n        setImageLoading(false);\r\n        getOrgEditAccess(institutionDatavalue, loginUserData);\r\n        institutionDatavalue.dial_code =\r\n            institutionDatavalue && institutionDatavalue.dial_code\r\n                ? institutionDatavalue.dial_code\r\n                : \"\";\r\n        setInstitutionData(institutionDatavalue);\r\n    };\r\n\r\n    const fetchInstitutionStatus = async () => {\r\n        const institutionStatusvalue = await apiService.get(\r\n            `/stats/institution/${props.routes[1]}`\r\n        );\r\n        setInstitutionStatus(institutionStatusvalue);\r\n    };\r\n\r\n    function getOrgEditAccess(institutionData: any, loginUser: any) {\r\n        setEditAccess(false);\r\n        if (loginUser && loginUser['roles']) {\r\n            if (loginUser['roles'].includes(\"SUPER_ADMIN\")) {\r\n                //SUPER_ADMIN can Edit all organisations\r\n                setEditAccess(true);\r\n            } else if (loginUser['roles'].includes(\"PLATFORM_ADMIN\") && institutionData.user == loginUser['_id']) {\r\n                //PLATFORM_ADMIN can Edit organisations which is added by them only\r\n                setEditAccess(true);\r\n            }\r\n            else if (loginUser['roles'].includes(\"GENERAL_USER\") && institutionData.user == loginUser['_id']) {\r\n                //\"GENERAL_USER\" can Edit organisations which is added by them only\r\n                setEditAccess(true);\r\n            }\r\n        }\r\n    }\r\n\r\n    const getLoggedInUser = async () => {\r\n        const data = await apiService.post(\"/users/getLoggedUser\", {});\r\n        if (data && data.roles && data.roles.length) {\r\n            if (data.roles.includes(\"SUPER_ADMIN\")) {\r\n                fetchDatas(\"Approved\", data);\r\n            } else {\r\n                let invites = data.institutionInvites.filter(\r\n                    (invite: any) => invite.institutionId === props.routes[1]\r\n                );\r\n                if (invites && invites.length) {\r\n                    fetchDatas(invites[0].status, data);\r\n                } else {\r\n                    fetchDatas(\"You dont have access.\", data);\r\n                }\r\n            }\r\n        }\r\n    };\r\n\r\n    const fetchDatas = (status: any, loginUserData: any) => {\r\n        // if (status === \"Approved\") {\r\n        fetchInstitutionData(loginUserData);\r\n        fetchInstitutionStatus();\r\n        fetchActiveOperationsByInstitution(props.routes[1]);\r\n        fetchActiveProjectssByInstitution(props.routes[1]);\r\n        // }\r\n    };\r\n\r\n    useEffect(() => {\r\n        if (props.routes && props.routes[1]) {\r\n            getLoggedInUser();\r\n        }\r\n    }, []);\r\n\r\n    useEffect(() => {\r\n        if (institutionData && institutionData.focal_points && institutionData.focal_points.length > 0) {\r\n            const focalPointsvalu = _.map(institutionData.focal_points, \"_id\");\r\n            fetchUsersByID(focalPointsvalu);\r\n        }\r\n    }, [institutionData]);\r\n\r\n    const fetchActiveOperationsByInstitution = async (institutionId: any) => {\r\n        const statusId = await fetchOperationStatus();\r\n        if (statusId && statusId.length > 0) {\r\n            const opResponse = await apiService.get(\"/operation\", {\r\n                query: {\r\n                    \"partners.institution\": [institutionId],\r\n                    status: statusId,\r\n                },\r\n            });\r\n            if (opResponse && opResponse.data && opResponse.data.length > 0) {\r\n                setActiveOperations(opResponse.data);\r\n            }\r\n        }\r\n    };\r\n\r\n    const fetchOperationStatus = async () => {\r\n        const response = await apiService.get(\"/operation_status\");\r\n        if (response && response.data && response.data.length > 0) {\r\n            const statusId: any[] = [];\r\n            _.forEach(response.data, function (item) {\r\n                if (\r\n                    item.title === \"Deployed\" ||\r\n                    item.title === \"Mobilizing\" ||\r\n                    item.title === \"Monitoring\" ||\r\n                    item.title === \"Ongoing\"\r\n                ) {\r\n                    statusId.push(item._id);\r\n                }\r\n            });\r\n            return statusId;\r\n        }\r\n        return [];\r\n    };\r\n\r\n    const fetchActiveProjectssByInstitution = async (institutionId: any) => {\r\n        const statusId = await fetchProjectStatus();\r\n        if (statusId && statusId.length > 0) {\r\n            const projectResponse = await apiService.get(\"/project\", {\r\n                query: {\r\n                    \"partner_institutions.partner_institution\": [institutionId],\r\n                    status: statusId,\r\n                },\r\n            });\r\n            if (projectResponse && projectResponse.data && projectResponse.data.length > 0) {\r\n                setActiveProjects(projectResponse.data);\r\n            }\r\n        }\r\n    };\r\n\r\n    const fetchProjectStatus = async () => {\r\n        const response = await apiService.get(\"/projectStatus\");\r\n        if (response && response.data && response.data.length > 0) {\r\n            const statusId: any[] = [];\r\n            _.forEach(response.data, function (item) {\r\n                if (item.title === \"Ongoing\" || item.title === \"Planning\") {\r\n                    statusId.push(item._id);\r\n                }\r\n            });\r\n            return statusId;\r\n        }\r\n        return [];\r\n    };\r\n\r\n    const fetchUsersByID = async (users: any) => {\r\n        const usersParams = {\r\n            query: { _id: users },\r\n            sort: { title: \"asc\" },\r\n            limit: \"~\",\r\n            select:\r\n                \"-firstname -lastname -password -role -country -region -institution -status -is_focal_point -mobile_number -enabled -created_at -updated_at -dataConsentPolicy -restrictedUsePolicy -acceptCookiesPolicy -withdrawConsentPolicy -medicalConsentPolicy -fullDataProtectionConsentPolicy -emailActivateToken\",\r\n        };\r\n        const usersQuery = await apiService.get(\"/users\", usersParams);\r\n        let tableData: any[] = [];\r\n        usersQuery?.data.forEach((user: any) => {\r\n            user?.institutionInvites.forEach((institutionInvite: any) => {\r\n                if (\r\n                    institutionInvite &&\r\n                    institutionInvite.status === \"Approved\" &&\r\n                    institutionInvite.institutionId === props.routes[1]\r\n                ) {\r\n                    tableData.push({\r\n                        ...user,\r\n                        ...{\r\n                            institutionId: institutionInvite.institutionId,\r\n                            institutionName: institutionInvite.institutionName,\r\n                            institutionStatus: institutionInvite.status,\r\n                        },\r\n                    });\r\n                }\r\n            });\r\n        });\r\n        if (tableData && tableData.length > 0) {\r\n            const isPrimary = tableData\r\n                .map((item) => [\r\n                    {\r\n                        ...item,\r\n                        isPrimary: item._id === institutionData.primary_focal_point,\r\n                    },\r\n                ])\r\n                .flat();\r\n            const sortPrimaryArr = isPrimary\r\n                .sort((a, b) => a.isPrimary - b.isPrimary)\r\n                .reverse();\r\n            setFocalPoints(sortPrimaryArr);\r\n        }\r\n    };\r\n\r\n    let propsData = {\r\n        prop: props,\r\n        imageLoading: imageLoading,\r\n        institutionData: institutionData,\r\n        editAccess: editAccess,\r\n        focalPoints: focalPoints,\r\n    }\r\n\r\n    let infoSectionData = {\r\n        institutionData: institutionData,\r\n        institutionStatus: institutionStatus\r\n    }\r\n\r\n    let accordionSectionData = {\r\n        institutionData: institutionData,\r\n        prop: props,\r\n        activeProjects: activeProjects,\r\n        activeOperations: activeOperations\r\n    }\r\n    return (\r\n        <div>\r\n            {\r\n                (\r\n                    <>\r\n                        <Container fluid className=\"_institutionfocalpoint\">\r\n                            <UpdatePopup routes={props.routes} />\r\n                        </Container>\r\n\r\n                        <InstitutionCoverSection {...propsData} />\r\n\r\n                        <InstitutionInfoSection {...infoSectionData} />\r\n\r\n                        <InstitutionAccordionSection {...accordionSectionData} />\r\n                    </>\r\n                )\r\n            }\r\n        </div>\r\n    );\r\n};\r\n\r\nexport default connect((state) => state)(InstitutionShow);\r\n"], "names": ["t", "useTranslation", "DiscussionAccordion", "Accordion", "defaultActiveKey", "<PERSON><PERSON>", "eventKey", "Header", "div", "className", "Body", "Discussion", "type", "id", "props", "routes", "MediaGalleryAccordion", "ReactImages", "gallery", "images", "imageSource", "images_src", "MoreInfoAccordion", "institutionData", "activeOperations", "activeProjects", "p", "b", "span", "title", "networks", "map", "item", "index", "li", "length", "i", "Link", "href", "as", "_id", "expertise", "br", "department", "unit", "InstitutionCoverSection", "imageLoading", "header", "img", "src", "process", "InstitutionCoverSectionContent", "routeData", "prop", "editAccess", "focalPoints", "InstitutionInfoSection", "popup", "setPopup", "useState", "quickInfo", "setQuickInfo", "fieldName", "setFieldName", "partner<PERSON><PERSON><PERSON><PERSON><PERSON>", "name", "partners", "institutionStatus", "operationData", "projectData", "<PERSON><PERSON><PERSON><PERSON>", "val", "Row", "partner_func", "Col", "onClick", "FontAwesomeIcon", "icon", "faUsers", "color", "size", "h5", "h4", "faLayerGroup", "operations", "project_func", "faFolderOpen", "projects", "InfoPopup", "isShow", "isClose", "data", "route", "arrowfun", "titles", "hr", "Modal", "centered", "show", "onHide", "aria-<PERSON>by", "closeButton", "Title", "CanViewDiscussionUpdate", "canViewDiscussionUpdate", "InstitutionAccordionSection", "connect", "state", "setInstitutionData", "InstitutionShow", "initialVal", "description", "dial_code", "telephone", "address", "line_1", "line_2", "city", "website", "focal_points", "primary_focal_point", "setInstitutionStatus", "setActiveOperations", "setActiveProjects", "setFocalPoints", "setImageLoading", "setEditAccess", "fetchInstitutionData", "loginUserData", "institutionDatavalue", "apiService", "get", "getOrgEditAccess", "loginUser", "includes", "user", "fetchInstitutionStatus", "institutionStatusvalue", "getLoggedInUser", "post", "roles", "fetchDatas", "invites", "institutionInvites", "filter", "invite", "institutionId", "status", "fetchActiveOperationsByInstitution", "fetchActiveProjectssByInstitution", "useEffect", "fetchUsersByID", "_", "focalPointsvalu", "statusId", "fetchOperationStatus", "opResponse", "query", "response", "push", "fetchProjectStatus", "projectResponse", "users", "usersQuery", "usersParams", "sort", "limit", "select", "tableData", "for<PERSON>ach", "institutionInvite", "institutionName", "sortPrimaryArr", "isPrimary", "flat", "a", "reverse", "Container", "fluid", "UpdatePopup"], "sourceRoot": "", "ignoreList": [7]}