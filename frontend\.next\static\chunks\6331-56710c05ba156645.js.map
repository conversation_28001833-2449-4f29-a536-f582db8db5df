{"version": 3, "file": "static/chunks/6331-56710c05ba156645.js", "mappings": "qJAcA,MANuBA,GAEnB,UAACC,EAAAA,EAAUA,CAAAA,CAAE,GAAGD,CAAK,KAIVE,aAAaA,EAAC,iWCsjC7B,MA7iCsB,IAClB,GAAM,CAAEC,CAAC,MAAEC,CAAI,CAAE,CAAGC,CAAAA,EAAAA,EAAAA,EAAAA,CAAcA,CAAC,CA4iCxBC,SA3iCLC,EAAgC,EA2iCdD,EAAC,KA3iCAE,QAAQ,CAAY,KAAOJ,EAAKI,QAAQ,CAC3DC,EAAkC,OAAlBL,EAAKI,QAAQ,CAAY,CAAEE,SAAU,KAAM,EAAI,CAAEC,MAAO,KAAM,EAE9EC,EAAUC,CAAAA,EAAAA,EAAAA,MAAAA,CAAMA,CAAC,MACjBC,EAAYD,CAAAA,EAAAA,EAAAA,MAAAA,CAAMA,CAA2B,MAC7C,CAACE,EAAiBC,EAAmB,CAAGC,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAC,CACnDC,eAAgB,GAChBC,cAAe,EAAE,CACjBC,wBAAyB,GACzBC,oBAAqB,GACrBC,iBAAkB,GAClBC,eAAgB,GAChBC,YAAY,CAChB,GAWM,CAACC,EAAS,CAAGR,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAtBPV,EAAc,SAAqB,OAAZA,GAAgB,YAuBrD,CAACmB,EAAoBC,EAAsB,CAAGV,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAQ,EAAE,EAChE,CAACW,EAAeC,EAAiB,CAAGZ,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAQ,EAAE,EACtD,CAACa,EAAkBC,EAAoB,CAAGd,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAQ,EAAE,EAC5D,CAACe,EAAeC,EAAiB,CAAGhB,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAQ,EAAE,EACtD,CAACiB,EAAiBC,EAAmB,CAAGlB,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAQ,EAAE,EAC1D,CAACmB,EAAaC,EAAe,CAAGpB,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAQ,EAAE,EAClD,EAAGqB,EAAa,CAAGrB,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAQ,EAAE,EACrC,CAACsB,EAAkBC,EAAoB,CAAGvB,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAS,GAC3D,CAACwB,EAAUC,EAAY,CAAGzB,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAQ,CAlBxB,CACpB0B,YAAa,GACbC,UAAW,EAAE,CACbC,kBAAmB,EAAE,CACrBC,oBAAqB,GACrBC,iBAAkB,GAClBC,OAAQ,EACZ,EAWiE,EAC3D,CAACC,EAAaC,GAAe,CAAGjC,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAQ,EAAE,EAClD,CAACkC,GAASC,GAAU,CAAGnC,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAQ,EAAE,EACzC,CAACoC,GAASC,GAAU,CAAGrC,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAQ,EAAE,EACzC,CAACsC,GAAkBC,GAAoB,CAAGvC,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAQ,EAAE,EAC5D,CAACwC,GAAUC,GAAY,CAAGzC,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAQ,EAAE,EAC5C,CAAC0C,GAAYC,GAAc,CAAG3C,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAQ,EAAE,EAChD,CAAC4C,GAAkBC,GAAoB,CAAG7C,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAQ,EAAE,EAC5D,CAAC8C,GAAcC,GAAgB,CAAG/C,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,EAAU,GACpD,CAACgD,GAAW,CAAGhD,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAASV,GAChC,CAAC2D,GAAOC,GAAS,CAAGlD,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,EAAU,GACtC,CAACmD,GAAOC,GAAS,CAAGpD,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAM,MAClC,CAACqD,GAAMC,GAAQ,CAAGtD,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAQ,CAAC,CAAEuD,UAAW,GAAIC,UAAW,GAAIC,KAAM,IAAK,EAAE,EAChF,CAACC,GAA8BC,GAAgC,CAAG3D,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,EAAU,GACpF4D,GAAW7E,EAAM8E,MAAM,EAAwB,SAApB9E,EAAM8E,MAAM,CAAC,EAAE,EAAe9E,EAAM8E,MAAM,CAAC,EAAE,CACxEC,GAAgB,CAClBpE,MAAO,GACPqE,QAAS,GACTC,aAAc,GACdC,OAAQ,EAAE,CACVlC,OAAQ,IACRmC,YAAa,GACbC,OAAQ,EAAE,CACV3B,SAAU,KACV4B,WAAY,EACZC,WAAY,KACZC,kBAAmB,IAAIC,KACvBC,SAAU,KACVC,YAAa,GACbC,OAAQ,EAAE,CACVC,SAAU,EAAE,CACZC,SAAS,EACTC,WAAY,EAAE,CACdC,QAAS,EAAE,EAGTC,GAAkB,CACpBC,MAAO,CAAC,EACRC,KAAMzF,EACN0F,MAAO,IACPC,aAAc7F,CAClB,EACM,CAAC8F,GAAYC,GAAc,CAAGrF,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAM8D,IAE5CwB,GAAS,KACX,IAAMC,EAAQ,IAAI/D,EAAS,CAC3B+D,EAAMC,IAAI,CAAC,CACPzD,OAAQ,GACRL,YAAa,GACbC,UAAW,EAAE,CACbE,oBAAqB,GACrBC,iBAAkB,GAClBF,kBAAmB,EAAE,GAEzBH,EAAY8D,GACZhE,EAAoBgE,EAAME,MAAM,CACpC,EAEMC,GAAY,CAACC,EAASC,KACxBpE,EAASqE,MAAM,CAACD,EAAG,GACnB,IAAML,EAAQ,IAAI/D,EAAS,CAC3BC,EAAY8D,GACZhE,EAAoBgE,EAAME,MAAM,EACR,GAAG,CAAvBjE,EAASiE,MAAM,EACfH,IAER,EAEMQ,GAAY,CAACC,EAAQH,KACvB,GAAIG,EAAEC,MAAM,CAAE,CACV,GAAM,MAAEC,CAAI,OAAEC,CAAK,CAAE,CAAGH,EAAEC,MAAM,CAChCxE,CAAQ,CAACoE,EAAE,CAACK,EAAK,CAAGC,EACP,uBAAuB,CAAhCD,GACAzE,EAAQ,CAACoE,EAAE,CAAC,MAAS,CAAGM,CAAAA,CAEhC,MACI1E,CADG,CACMoE,EAAE,CAAChE,iBAAiB,CAAGmE,EAChCvE,CAAQ,CAACoE,EAAE,CAACjE,SAAS,CAAGoE,EAAEI,GAAG,CAAC,GACnBC,EAAKF,KAAK,EAGzBzE,EAAY,IAAID,EAAS,CAC7B,EAgBM6E,GAA0B,MAAOC,IACnC,IAAMC,EAAkB,MAAMC,EAAAA,CAAUA,CAACC,GAAG,CAAC,oBAAqBH,GAC9DC,GAAmBG,MAAMC,OAAO,CAACJ,EAAgBK,IAAI,GAAG,EACrCL,EAAgBK,IAAI,EAG3C,IAAM7C,EAAU,MAAMyC,EAAAA,CAAUA,CAACC,GAAG,CAAC,WAAYH,GAC7CvC,GAAW2C,MAAMC,OAAO,CAAC5C,EAAQ6C,IAAI,GAAG,EACzB7C,EAAQ6C,IAAI,EAG/B,IAAMC,EAAa,MAAML,EAAAA,CAAUA,CAACC,GAAG,CAAC,cAAeH,GACnDO,GAAcH,MAAMC,OAAO,CAACE,EAAWD,IAAI,GAAG,GAC/BC,EAAWD,IAAI,EAGlC,IAAME,EAAY,MAAMN,EAAAA,CAAUA,CAACC,GAAG,CAAC,YAAaH,GAChDQ,GAAaJ,MAAMC,OAAO,CAACG,EAAUF,IAAI,GAAG,GAChCE,EAAUF,IAAI,EAI9BG,EAAkBC,MAAM,CACpB,yOACJD,EAAkB/B,KAAK,CAACjD,MAAM,CAAG,CAAEkF,IAAK,iBAAkB,EAC1D,IAAMC,EAAe,MAAMV,EAAAA,CAAUA,CAACC,GAAG,CAAC,gBAAgBM,EACtDG,GAAgBR,MAAMC,OAAO,CAACO,EAAaN,IAAI,GAAG,GAC9BM,EAAaN,IAAI,EAGzC,IAAMjF,EAAY,MAAM6E,EAAAA,CAAUA,CAACC,GAAG,CAAC,aAAcH,GACjD3E,GAAa+E,MAAMC,OAAO,CAAChF,EAAUiF,IAAI,GAIzCjE,GAHmBhB,EAAUiF,IAAI,CAACT,GAAG,CAGvBgB,CAHyBf,EAAWgB,KACvC,CAAEC,MAAOjB,EAAK1G,KAAK,CAAEwG,MAAOE,EAAKkB,GAAG,CAAC,IAIpD,IAAMC,EAAa,MAAMf,EAAAA,CAAUA,CAACC,GAAG,CAAC,oBAAqBH,GACzDiB,GAAcb,MAAMC,OAAO,CAACY,EAAWX,IAAI,GAAG,GAC1BW,EAAWX,IAAI,CAE3C,EAEMY,GAAmB,CAACC,EAAeC,EAAuBlD,KAC5D9D,EAAsB+G,EAAS/C,MAAM,CAAG+C,EAAS/C,MAAM,CAAG,EAAE,EAC5D9D,EAAiB6G,EAAS5C,UAAU,CAAG4C,EAAS5C,UAAU,CAAG,EAAE,EAC/D7D,EAAiByG,EAAS9C,QAAQ,CAAG8C,EAAS9C,QAAQ,CAAG,EAAE,EAC3D7D,EAAoB2G,EAAS3C,OAAO,CAAG2C,EAAS3C,OAAO,CAAG,EAAE,EAC5DrD,EAAYiG,GACZC,GAAUF,EAAS1D,OAAO,EAC1B6D,CAD6B,EACnBH,EAASvD,WAAW,EAC9BmB,CADiC,EACnB,GAAqB,EAAE,GAAGwC,CAAS,CAAE,EAAhB,CAAmBJ,CAAQ,CAAC,EAFG,CAG3DjD,EAAWa,GAAewC,GAAoB,EAAE,GAAGA,CAAS,CAAEjD,EAAhB,IAFqB,GAEI,EAAK,GAAM,MAGvFkD,GAAmB,CAACC,EAAYC,EAAkBC,EAAiBP,KACrEA,EAAiBlC,IAAI,CAAC,CAClB9D,YAAaqG,EAAMrG,WAAW,EAAIqG,EAAMrG,WAAW,CAAC4F,GAAG,CACvD1F,kBAAmBoG,EACnBrG,UAAWsG,EACXpG,oBAAqBkG,GAASA,EAAMhG,MAAM,CAAGgG,EAAMhG,MAAM,CAACuF,GAAG,CAAG,GAChEvF,OAAQgG,GAASA,EAAMhG,MAAM,CAAGgG,EAAMhG,MAAM,CAACuF,GAAG,CAAG,GACnDxF,iBAAkBiG,EAAMjG,gBAAgB,EAEhD,EAEMoG,GAAa,IACf,GAAM,QAAEnG,CAAM,SAAEgC,CAAO,CAAE+C,WAAS,QAAE7C,CAAM,aAAEC,CAAW,QAAEC,CAAM,UAAEgE,CAAQ,YAAE9D,CAAU,UAAEG,CAAQ,CAAE,CAAGiD,EACpGA,EAAS1F,MAAM,CAAGA,GAAUA,EAAOuF,GAAG,CAAGvF,EAAOuF,GAAG,CAAG,IACtDG,CAD2D,CAClDpD,UAAU,CAAGA,EAAa+D,CADuC,GAChC/D,GAAYgE,MAAM,GAAK,KACjEZ,EAASjD,QAAQ,CAAGA,EAAW4D,IAAO5D,GAAU6D,IADPD,EACa,GAAK,KAC3DX,EAAS1D,OAAO,CAAGA,GAAWA,EAAQuD,GAAG,CAAGvD,EAAQuD,GADfc,CACqB,IAC1DX,EAASjF,QAAQ,CAAGsE,GAAaA,EAAUQ,GAAG,CAAGR,EAAUQ,GAAG,CAAG,KACjEG,EAASvD,WAAW,CAAGA,GAAeA,EAAYoD,GAAG,CAAGpD,EAAYoD,GAAG,CAAG,GAC1EG,EAAStD,MAAM,CAAGA,EACZA,EAAOgC,GAAG,CAAC,IACA,CAAEkB,MAAOiB,EAAM5I,KAAK,CAACsD,GAAW,CAAEkD,MAAOoC,EAAMhB,GAAG,CAAC,GAE9D,EAAE,CACRG,EAASxD,MAAM,CAAGA,EACZA,EAAOkC,GAAG,CAAC,IACA,CAAEkB,MAAOkB,EAAM7I,KAAK,CAAEwG,MAAOqC,EAAMjB,GAAG,CAAC,GAElD,EAAE,CACRa,GAAY7E,GAAQ6E,EACxB,EAEMK,GAAoB,UACtB,IAAMf,EAAW,MAAMjB,EAAAA,CAAUA,CAACC,GAAG,CAAC,cAA8B,OAAhB1H,EAAM8E,MAAM,CAAC,EAAE,EAAIkB,IACvE,GAAI0C,EAAU,CACV,IAAMC,EAA0B,EAAE,CAC5B,UAAElD,CAAQ,UAAEhD,CAAQ,CAAE,CAAGiG,EAC/BS,GAAWT,GACXjG,GACIA,EAASiH,OAAO,CAAC,IACb,IAAMT,EACFD,EAAMpG,SAAS,EACfoG,EAAMpG,SAAS,CAACwE,GAAG,CAAC,IACT,CAAEkB,MAAOqB,EAAMhJ,KAAK,CAAEwG,MAAOwC,EAAMpB,GAAG,CAAC,GAEhDW,EACFF,EAAMpG,SAAS,EACfoG,EAAMpG,SAAS,CAACwE,GAAG,CAAC,GACTwC,EAAMrB,GAAG,EAExBQ,GAAiBC,EAAOC,EAAaC,EAAYP,EACrD,GACJF,GAAiBC,EAAUC,EAAkBlD,EACjD,CACJ,EAGMoE,GAAqB,UACvB,IAAMC,EAAc,MAAMrC,EAAAA,CAAUA,CAACsC,IAAI,CAAC,uBAAwB,CAAC,GACnE,GAAID,GAAeA,EAAY,KAAQ,CAAE,GAAX,EACJA,CAClBE,CADEA,CAAAA,OAAgBF,EAAAA,EAAY,OAAZA,EAAW,GAAXA,EAAAA,EAAsBG,GAAtBH,GAA4B,CAAC,GAA2B,2BAARI,EAAQ,EAC5DxD,MAAM,CAAG,EACvB9B,CAD0B,GACM,GAEhCA,IAAgC,EAExC,CACJ,EAEAuF,CAAAA,EAAAA,EAAAA,SAAAA,CAASA,CAAC,KAEFtF,IACA4E,KAKJnC,CANc,EAMUtB,IACxB6D,IACJ,EAAG,EAAE,EAEL,IAAMO,GAAa,IACf9D,GAAewC,GAAoB,EAC/B,GAAGA,CAAS,CACZ,EAF+B,CAE5BuB,CAAG,CACV,GACArJ,EAAmB,GAAqB,EACpC,GAAG8H,CAAS,CACZ,EAFoC,CAEjCuB,CAAG,CACV,EACJ,EAEMC,GAAgB,MAAOC,IACzB,IAAIC,EAAkB,EAAE,CAExB,GAAID,EAAI,CACJ,IAAM7B,EAAW,MAAMjB,EAAAA,CAAUA,CAACC,GAAG,CAAC,mBAAsB,OAAH6C,GAAMvE,IAC3D0C,GAAYA,EAASb,IAAI,EAAE,CAC3B2C,EAAW9B,EAASb,IAAI,CAACT,GAAG,CAAC,CAACC,EAAWgB,KAC9B,CAAEC,MAAOjB,EAAK1G,KAAK,CAAEwG,MAAOE,EAAKkB,GAAG,GAC/C,EACSrC,IAAI,CAAC,CAACuE,EAAQC,IAAWD,EAAEnC,KAAK,CAACqC,aAAa,CAACD,EAAEpC,KAAK,EAEvE,CACA,OAAOkC,CACX,EAEM5B,GAAY,MAAO2B,IACrBnH,GAAU,MAAMkH,GAAcC,GAClC,EAEM1B,GAAY,MAAO0B,IAOrB,IAAIC,EAAkB,EAAE,CACxB,GAAID,EAAI,CACJ,IAAM7B,EAAW,MAAMjB,EAAAA,CAAUA,CAACC,GAAG,CAAC,uBAA0B,OAAH6C,GAR3C,CAClBtE,CAOmE2E,KAP5D,CAAEC,SAAS,CAAK,EACvB3E,KAAM,CAAE,CAACzE,EAAS,CAAE,KAAM,EAC1B0E,MAAO,IACP8B,OAAQ,2FACZ,GAIQS,GAAYA,EAASb,IAAI,EAAE,CAC3B2C,EAAW9B,EAASb,IAAI,CAACT,GAAG,CAAC,GAAgB,EACzCkB,EADyC,IAClCjB,EAAK1G,KAAK,CAACsD,GAAW,CAC7BkD,MAAOE,EAAKkB,GAAG,CACnB,GAER,CACAjF,GAAUkH,EACd,EAEMM,GAAU,KACZ,IAAML,EAAI,CAAEjG,UAAW,GAAIC,UAAW,GAAIC,KAAM,IAAK,EACrDH,GAAQ,GAAmB,IAAIwG,EAAUN,EAAE,CAC/C,EAEMO,GAAa,CAACpE,EAASC,KACzBvC,GAAKwC,MAAM,CAACD,EAAG,GACftC,GAAQ,IAAID,GAAK,EACG,GAAG,CAAnBA,GAAKoC,MAAM,EACXoE,IAER,EAEMG,GAAgBjE,IAClB,GAAIA,EAAEC,MAAM,CAAE,CACV,GAAM,MAAEC,CAAI,OAAEC,CAAK,CAAE,CAAGH,EAAEC,MAAM,CAChC,GAAa,YAATC,EAAoB,CACpB,IAAMgE,EAAgBlE,EAAEC,MAAM,CAACiE,aAAa,CAC5C,GAAIlE,EAAEC,MAAM,EAAIiE,GAAkC,MAAjBA,EAAuB,CACpD,IAAMC,EAAcnE,EAAEC,MAAM,CAACiE,EAAc,CAACE,YAAY,CAAC,oBACzD9E,GAAc,GAAqB,EAC/B,GAAGwC,CAAS,CACZ7D,EAF+B,WAEjBkG,EAClB,EACJ,CACJ,CACA7E,GAAc,GAAqB,EAAE,GAAGwC,CAAS,CAAE,CAAC5B,CAAjB,CAAsB,CAAEC,EAAM,GACpD,WAAW,CAApBD,IACA0B,GAAUzB,GACViD,GAAW,CAAElF,OAAQ,EAAE,IAEd,eAAe,CAAxBgC,IACA2B,GAAU1B,GACViD,GAAW,CAAEhF,OAAQ,EAAE,GAE/B,MACIkB,CADG,EACW,GAAqB,EAAE,GAAGwC,CAAS,CAAE5D,EAAhB,KAAwB8B,EAAE,EAErE,EAEAqE,EAAAA,SAAe,CAAC,KACZ,GAAItK,EAAiB,CACjB,IAAMuK,EAAsB,CAAC,EAC7BC,OAAOC,IAAI,CAACzK,GAAiB2I,OAAO,CAAC,CAACrC,EAAWgB,KAC7C,IAAMoD,EACF,CAAwB,CAACpE,EAAK,CAACX,MAAM,CAAG,GACxC,CAAwB,CAACW,EAAK,CAACD,GAAG,CAAC,GACxBsE,EAAEvE,KAAK,EAEtBmE,CAAc,CAACjE,EAAK,CAAGoE,GAAgB,EAC3C,GADmCA,GAE1BH,EACb,CACJ,EAAG,CAACvK,EAAgB,EAEpB,IAAM4K,GAAW,MAAOL,IACpB,GAAM,gBAAEpK,CAAc,qBAAEG,CAAmB,CAAE,CAAGiK,EAO1CM,EAAc,MAAMnE,EAAAA,CAAUA,CAACsC,IAAI,CAAC,eANtB,CAMsC8B,MAL/C,CACH7G,QAAS9D,EACTyB,YAAatB,CACjB,CACJ,GAEIuK,GAAejE,MAAMC,OAAO,CAACgE,IAI7BtJ,EAHesJ,EAAYxE,GAAG,CAAC,CAACC,CADW,CACAgB,EAG9ByD,GAFF,CAAExD,MAAOjB,EAAK0E,QAAQ,CAAE5E,MAAOE,EAAKkB,GAAG,CAAC,GAI3D,EAUMyD,GAA0B,CAAChF,EAAQH,KACrC,GAAM,MAAEK,CAAI,OAAEC,CAAK,CAAE,CAAGH,EAAEC,MAAM,CAC1BgF,EAAa,IAAI3H,GAAK,CAC5B2H,CAAU,CAACpF,EAAE,CAACK,EAAK,CAAGC,EACtB5C,GAAQ0H,EACZ,EAEMC,GAAe,CAACxH,EAAWyH,KAChB,eAAT,GAAyBzH,SAAyB,cAAc,CAAtByH,EAC1C7F,GAAc,GAAqB,EAC/B,GAAGwC,CAAS,CACZrD,EAF+B,OAErB,KACVH,WAAYZ,EAChB,GAEA4B,GAAc,GAAqB,EAC/B,GAAGwC,CAAS,CACZ,CAACqD,CAF8B,CAE1B,CAAEzH,EACX,EAER,EAEM0H,GAAuB,CAACC,EAAcxF,KACxCvC,EAAI,CAACuC,EAAE,CAACnC,IAAI,CAAG2H,EACf9H,GAAQ,IAAID,GAAK,CACrB,EAEMgI,GAAoB,IACtBhG,GAAc,GAAqB,EAC/B,GAAGwC,CAAS,CACZpD,EAF+B,UAElByB,EACjB,EACJ,EA6BMoF,GAAe,MAAOC,EAAaC,EAAcC,SAyC/ChE,EACAiE,CAzCAH,IAASA,EAAMI,cAAc,EAAE,EACzBA,cAAc,GAGpB9L,EAAU+L,OAAO,EACjB/L,EAAU+L,OAAO,CAACC,YAAY,CAAC,WAAY,YAG/C,IAAMC,EAASC,IAAAA,GAAK,CAChBvK,EACAuK,IAAAA,YAAc,CAACA,IAAAA,IAAAA,CAAM,CAAG,cAAe,YAAa,SAAU,mBAAmB,GAE/E3C,EAAM,CACR1J,MAAO0F,GAAW1F,KAAK,CAACsM,IAAI,GAC5BvH,YAAaW,GAAWX,WAAW,CACnC1C,OAAQqD,GAAWrD,MAAM,CACzBsC,WAAYe,GAAWf,UAAU,CACjCG,SAAUY,GAAWZ,QAAQ,CAC7BT,QAASqB,GAAWrB,OAAO,CAC3BC,aAAcoB,GAAWpB,YAAY,CACrCxB,SAAU4C,GAAW5C,QAAQ,CAC7B0B,YAAakB,GAAWlB,WAAW,CACnCC,OAAQiB,GAAWjB,MAAM,CACnBiB,GAAWjB,MAAM,CAACgC,GAAG,CAAC,CAACC,EAAWgB,IACvBhB,EAAKF,KAAK,EAErB,EAAE,CACRjC,OAAQmB,GAAWnB,MAAM,CACnBmB,GAAWnB,MAAM,CAACkC,GAAG,CAAC,CAACC,EAAWgB,IACvBhB,EAAKF,KAAK,EAErB,EAAE,CACRiC,SAAU9E,GAAKoC,MAAM,CAAG,GAA2B,KAAtBpC,EAAI,CAAC,EAAE,CAACE,SAAS,CAAUF,GAAO,EAAE,CACjE7B,SAAUsK,EACVpH,OAAQU,GAAWV,MAAM,CACzBG,WAAYO,GAAWP,UAAU,CACjCF,SAAUS,GAAWT,QAAQ,CAC7BG,QAASM,GAAWN,OAAO,EAK3BlB,IACA8H,EAAW,IADD,iCAEVjE,EAAW,MAAMjB,EAAAA,CAAUA,CAACyF,KAAK,CAAC,cAA8B,OAAhBlN,EAAM8E,MAAM,CAAC,EAAE,EAAIuF,KAEnEsC,EAAW,mCACXjE,EAAW,MAAMjB,EAAAA,CAAUA,CAACsC,IAAI,CAAC,aAAcM,IAE/C3B,GAAYA,EAASH,GAAG,CACpBxE,CADsB,GAEtBM,GAASqE,OADK,EACLA,KAAAA,EAAAA,EAAUH,GAAAA,GAAOG,EAASH,EAA1BG,CAA6B,EACtCvE,IAAS,KAETgJ,EAAAA,EAAKA,CAACC,OAAO,CAACjN,EAAEwM,IAChBU,IAAAA,IAAW,CAAC,yBAA0B,mBAAgC,OAAb3E,EAASH,GAAG,KAGzDoE,EAAZ9H,GAAuB,OAAb,iCACI,sCAElBsI,EAAAA,EAAKA,CAACG,KAAK,CAACnN,EAAEwM,KAId7L,EAAU+L,OAAO,EACjB/L,EAAU+L,OAAO,CAACU,eAAe,CAAC,WAE1C,EAeMC,GAAQ,IACV,IAAMC,EAAkB,EAAE,CACpBC,EAAgB,EAAE,CACpBnD,EAAG7D,MAAM,CAAG,GAAG,EACZU,GAAG,CAAEC,IAEAA,EAAKsG,IAAI,EACRtG,EAAAA,CAAKsG,IAAI,CAACC,QAAQ,CAAC,QAChBvG,EAAKsG,IAAI,CAACC,QAAQ,CAAC,SACnBvG,EAAKsG,IAAI,CAACC,QAAQ,CAAC,SACnBvG,EAAKsG,IAAI,CAACC,QAAQ,CAAC,OAAK,CAE5BF,EADF,IACa,CAACrG,EAAKwG,QAAQ,EAEzBJ,EAAShH,IAAI,CAACY,EAAKwG,QAAQ,CAEnC,GAEJvH,GAAc,GAAqB,EAAE,GAAGwC,CAAS,CAAEnD,EAAhB,KAAwB8H,CAAS,IACpEnH,GAAc,GAAqB,EAAE,GAAGwC,CAAS,CAAElD,EAAhB,OAA0B8H,EAAO,EACxE,EAEMI,GAAY,IACdxH,GAAc,GAAqB,EAAE,GAAGwC,CAAS,CAAEhD,EAAhB,SAA4BiI,EAAU,EAC7E,EAEMC,GAAe,IACjB1H,GAAewC,GAAoB,EAAE,GAAGA,CAAS,CAAE/C,EAAhB,MAAyBkI,EAAU,EAC1E,EAEA,MACI,WAACC,EAAAA,CAASA,CAAAA,CAACC,UAAU,WAAWC,KAAK,cACjC,UAACC,EAAAA,CAAIA,CAAAA,UACD,UAACC,EAAAA,CAAqBA,CAAAA,CAACC,SAAUhC,GAAciC,IAAK5N,EAAS6N,cA9I9CC,CA8I6DC,GA7IpF,IAAIC,EAAc,CAAC,EACf7B,EAAgBC,IAAAA,GAAK,CACrBvK,EACAuK,IAAAA,YAAc,CAACA,IAAAA,IAAAA,CAAM,CAAG,cAAe,YAAa,SAAU,mBAAmB,GAEjF6B,EAAe,EACnB,IAAK,IAAMhI,KAAKkG,EAAQ,CACpB,GAAuC,KAAnC,CAAO,CAAClG,EAAE,CAASlE,WAAW,EAIA,IAAI,CAAlC,CAAO,CAACkE,EAAE,CAAS7D,MAAM,CAJc,CACvC4L,EAAcC,EACd,KACJ,CAKAA,GACJ,CACA,GAAID,EAAc,CAAC,EAAG,CAClBpM,EAAoBoM,EAAc,GAClC,IAAIE,EAAQlJ,SAASmJ,cAAc,CAAC,aACpCD,UAAAA,EAAOE,MAAPF,QAAqB,EACzB,CACJ,EAuH2GG,cAAe5I,GAAY6I,oBAAoB,WAC1I,WAACb,EAAAA,CAAIA,CAACc,IAAI,YACN,UAACC,EAAAA,CAAGA,CAAAA,UACA,UAACC,EAAAA,CAAGA,CAAAA,UACA,UAAChB,EAAAA,CAAIA,CAACiB,KAAK,WAAEzK,GAAW1E,EAAE,iBAAmBA,EAAE,sBAGvD,UAACoP,KAAAA,CAAAA,GACD,WAACH,EAAAA,CAAGA,CAAAA,CAACjB,UAAU,iBACX,UAACkB,EAAAA,CAAGA,CAAAA,CAACG,EAAE,IAACC,GAAI,EAAGC,GAAI,YACf,WAACC,EAAAA,CAAIA,CAACC,KAAK,YACP,UAACD,EAAAA,CAAIA,CAACE,KAAK,EAAC1B,UAAU,0BAAkBhO,EAAE,wBAC1C,WAAC2P,EAAAA,EAAWA,CAAAA,CACR5I,KAAK,UACLqD,GAAG,UACHpD,MAAOd,GAAWrB,OAAO,CACzB+K,SAAU9E,GACV+E,QAAQ,IACRC,aAAc9P,EAAE,iCAEhB,UAAC+P,SAAAA,CAAO/I,MAAM,YAAIhH,EAAE,mBACnBiC,EAAYgF,GAAG,CAAC,CAACC,EAAMR,IAEhB,UAACqJ,SAAAA,CAEGC,mBAAkB9I,EAAKpC,YAAY,CAACsD,GAAG,CACvCpB,MAAOE,EAAKkB,GAAG,UAEdlB,EAAK1G,KAAK,EAJNkG,YAW7B,UAACwI,EAAAA,CAAGA,CAAAA,CAACG,EAAE,IAACC,GAAI,EAAGC,GAAI,YACf,WAACC,EAAAA,CAAIA,CAACC,KAAK,EAACQ,MAAO,CAAEC,SAAU,OAAQ,YACnC,UAACV,EAAAA,CAAIA,CAACE,KAAK,WAAE1P,EAAE,oBACf,UAACmQ,EAAAA,EAAWA,CAAAA,CACRC,gBAAiB,CACbC,gBAAiBrQ,EAAE,iBACnBsQ,oBAAqB,0BACzB,EACAC,QAASvN,GACTgE,MAAOd,GAAWnB,MAAM,CACxB6K,SAAU9E,GACVkD,UAAW,SACXwC,WAAY,mCAK5B,WAACvB,EAAAA,CAAGA,CAAAA,CAACjB,UAAU,iBACX,UAACkB,EAAAA,CAAGA,CAAAA,CAACG,EAAE,IAACC,GAAI,EAAGC,GAAI,YACf,WAACC,EAAAA,CAAIA,CAACC,KAAK,YACP,UAACD,EAAAA,CAAIA,CAACE,KAAK,EAAC1B,UAAU,0BAAkBhO,EAAE,gBAC1C,WAAC2P,EAAAA,EAAWA,CAAAA,CACR5I,KAAK,cACLqD,GAAG,cACHpD,MAAOd,GAAWlB,WAAW,CAC7B4K,SAAU9E,GACV+E,QAAQ,IACRC,aAAc9P,EAAE,iCAEhB,UAAC+P,SAAAA,CAAO/I,MAAM,YAAIhH,EAAE,sBACnB8C,EAAYmE,GAAG,CAAC,CAACC,EAAMR,IAEhB,UAACqJ,SAAAA,CAAe/I,MAAOE,EAAKkB,GAAG,UAC1BlB,EAAK1G,KAAK,EADFkG,YAQjC,UAACwI,EAAAA,CAAGA,CAAAA,CAACG,EAAE,IAACC,GAAI,EAAGC,GAAI,YACf,WAACC,EAAAA,CAAIA,CAACC,KAAK,YACP,UAACD,EAAAA,CAAIA,CAACE,KAAK,WAAE1P,EAAE,YACf,UAACwP,EAAAA,CAAIA,CAACC,KAAK,WACP,UAACU,EAAAA,EAAWA,CAAAA,CACRC,gBAAiB,CACbC,gBAAiBrQ,EAAE,gBACnBsQ,oBAAqB,0BACzB,EACAC,QAASrN,GACT8D,MAAOd,GAAWjB,MAAM,CACxB2K,SA/QpB/I,CA+Q8B4J,GA9Q9CtK,GAAc,GAAqB,EAC/B,GAAGwC,CAAS,CACZ1D,EAF+B,KAEvB4B,EACZ,EACJ,EA2QwCmH,UAAW,SACXwC,WAAY,0BAK5B,UAACtB,EAAAA,CAAGA,CAAAA,CAACG,EAAE,IAACC,GAAI,EAAGC,GAAI,YACf,WAACC,EAAAA,CAAIA,CAACC,KAAK,YACP,UAACD,EAAAA,CAAIA,CAACE,KAAK,WAAE1P,EAAE,cACf,WAAC2P,EAAAA,EAAWA,CAAAA,CACR5I,KAAK,WACLqD,GAAG,WACHpD,MAAOd,GAAW5C,QAAQ,CAC1BsM,SAAU9E,aAEV,UAACiF,SAAAA,CAAO/I,MAAM,YAAIhH,EAAE,oBACnBsD,GAAS2D,GAAG,CAAC,CAACC,EAAMR,IACjB,UAACqJ,SAAAA,CAAe/I,MAAOE,EAAKkB,GAAG,UAC1BlB,EAAK1G,KAAK,EADFkG,eAQjC,UAACuI,EAAAA,CAAGA,CAAAA,CAACjB,UAAU,gBACX,UAACkB,EAAAA,CAAGA,CAAAA,UACA,WAACM,EAAAA,CAAIA,CAACC,KAAK,YACP,UAACD,EAAAA,CAAIA,CAACE,KAAK,EAAC1B,UAAU,0BAAkBhO,EAAE,WAC1C,UAAC0Q,EAAAA,EAASA,CAAAA,CACN3J,KAAK,QACLqD,GAAG,QACHyF,QAAQ,IACR7I,MAAOd,GAAW1F,KAAK,CACvBmQ,UAAW,GAAiC,KAAjB3J,EAAM8F,IAAI,GACrCgD,aAAc,CACVa,UAAW3Q,EAAE,oBACjB,EACA4P,SAAU9E,YAK1B,UAACmE,EAAAA,CAAGA,CAAAA,CAACjB,UAAU,gBACX,UAACkB,EAAAA,CAAGA,CAAAA,UACA,WAACM,EAAAA,CAAIA,CAACC,KAAK,YACP,UAACD,EAAAA,CAAIA,CAACE,KAAK,WAAE1P,EAAE,iBACf,UAAC4Q,EAAAA,CAAeA,CAAAA,CAACC,YAAa3K,GAAWX,WAAW,CAAEqK,SAAU,GAA4BzD,GAAkB2E,YAI1H,WAAC7B,EAAAA,CAAGA,CAAAA,CAACjB,UAAU,iBACVjM,EAAgBwE,MAAM,CACvB,UAAC2I,EAAAA,CAAGA,CAAAA,CAACG,EAAE,IAACC,GAAI,EAAGC,GAAI,YAEX,WAACC,EAAAA,CAAIA,CAACC,KAAK,YACP,UAACD,EAAAA,CAAIA,CAACE,KAAK,EAAC1B,UAAU,0BAAkBhO,EAAE,qBAE1C,WAAC2P,EAAAA,EAAWA,CAAAA,CACR5I,KAAK,SACLqD,GAAG,SACHpD,MAAOd,GAAWrD,MAAM,CACxB+M,SAAU9E,GACV+E,QAAQ,IACRC,aAAc9P,EAAE,iCAEhB,UAAC+P,SAAAA,CAAO/I,MAAM,YAAIhH,EAAE,2BACnB+B,EAAgBkF,GAAG,CAAC,CAACC,EAAMR,IAEpB,UAACqJ,SAAAA,CAAe/I,MAAOE,EAAKkB,GAAG,UAC1BlB,EAAK1G,KAAK,EADFkG,YAO3B,KACV,UAACwI,EAAAA,CAAGA,CAAAA,CAACG,EAAE,IAACC,GAAI,EAAGC,GAAI,YACf,WAACC,EAAAA,CAAIA,CAACC,KAAK,YACP,UAACR,EAAAA,CAAGA,CAAAA,UACA,UAACC,EAAAA,CAAGA,CAAAA,UACA,UAACM,EAAAA,CAAIA,CAACE,KAAK,WAAE1P,EAAE,4BAGvB,UAACD,EAAAA,CAAaA,CAAAA,CACVgR,SAAU7K,GAAWf,UAAU,CAC/ByK,SAAU,GAAe7D,GAAaxH,EAAM,cAC5CyM,WAAW,eACXC,gBAAiBjR,EAAE,0BAI/B,UAACkP,EAAAA,CAAGA,CAAAA,CAACG,EAAE,IAACC,GAAI,EAAGC,GAAI,YACf,UAACC,EAAAA,CAAIA,CAAC0B,KAAK,EACP1D,KAAK,WACL9H,QAASQ,GAAWR,OAAO,CAC3BkK,SA3nBF,CA2nBYuB,IA1nBtChL,GAAc,GAAqB,EAC/B,GAAGwC,CAAS,CACZjD,EAF+B,MAEtB,CAACiD,EAAUjD,OAAO,CAC3BJ,SAAU,KACd,EACJ,EAsnBgC6C,MAAOnI,EAAE,mBAGhBkG,GAAWR,OAAO,EACf,UAACwJ,EAAAA,CAAGA,CAAAA,CAACG,EAAE,IAACC,GAAI,EAAGC,GAAI,YACf,WAACC,EAAAA,CAAIA,CAACC,KAAK,YACP,UAACR,EAAAA,CAAGA,CAAAA,UACA,UAACC,EAAAA,CAAGA,CAAAA,UACA,UAACM,EAAAA,CAAIA,CAACE,KAAK,WAAE1P,EAAE,0BAGvB,UAACD,EAAAA,CAAaA,CAAAA,CACVgR,SAAU7K,GAAWZ,QAAQ,CAC7B8L,SAAU,CAAClL,GAAWf,UAAU,CAChCyK,SAAU,GAAe7D,GAAaxH,EAAM,YAC5CyM,WAAW,eACXK,QAASnL,GAAWf,UAAU,CAC9B8L,gBAAiBjR,EAAE,2BAMvC,UAACkO,EAAAA,CAAIA,CAACoD,IAAI,WACN,UAAC/G,IAAAA,UAAGvK,EAAE,gBAEV,UAACoP,KAAAA,CAAAA,GACAjL,GAAK8C,GAAG,CAAC,CAACC,EAAMR,IAET,UAAC6K,MAAAA,UACG,WAACtC,EAAAA,CAAGA,CAAAA,WACA,UAACC,EAAAA,CAAGA,CAAAA,CAACG,GAAI,WACL,WAACG,EAAAA,CAAIA,CAACC,KAAK,YACP,UAACD,EAAAA,CAAIA,CAACE,KAAK,WAAE1P,EAAE,WACf,UAAC0Q,EAAAA,EAASA,CAAAA,CACN3J,KAAK,YACLqD,GAAG,YACHpD,MAAOE,EAAK7C,SAAS,CACrBuL,SAAU,GAAY/D,GAAwBhF,EAAGH,UAI7D,UAACwI,EAAAA,CAAGA,CAAAA,CAACG,GAAI,WACL,WAACG,EAAAA,CAAIA,CAACC,KAAK,YACP,UAACD,EAAAA,CAAIA,CAACE,KAAK,WAAE1P,EAAE,eACf,WAAC2P,EAAAA,EAAWA,CAAAA,CACR5I,KAAK,YACLqD,GAAG,YACHpD,MAAOE,EAAK5C,SAAS,CACrBsL,SAAU,GAAY/D,GAAwBhF,EAAGH,aAEjD,UAACqJ,SAAAA,CAAO/I,MAAM,cAAK,aACnB,UAAC+I,SAAAA,CAAO/I,MAAM,aAAI,QAClB,UAAC+I,SAAAA,CAAO/I,MAAM,aAAI,UAClB,UAAC+I,SAAAA,CAAO/I,MAAM,aAAI,uBAClB,UAAC+I,SAAAA,CAAO/I,MAAM,aAAI,mBAClB,UAAC+I,SAAAA,CAAO/I,MAAM,aAAI,cAClB,UAAC+I,SAAAA,CAAO/I,MAAM,aAAI,YAClB,UAAC+I,SAAAA,CAAO/I,MAAM,aAAI,mBAI9B,UAACkI,EAAAA,CAAGA,CAAAA,CAACG,GAAI,WACL,WAACG,EAAAA,CAAIA,CAACC,KAAK,YACP,UAACR,EAAAA,CAAGA,CAAAA,UACA,UAACC,EAAAA,CAAGA,CAAAA,UACA,UAACM,EAAAA,CAAIA,CAACE,KAAK,WAAE1P,EAAE,mBAGvB,UAACD,EAAAA,CAAaA,CAAAA,CACVgR,SAAU7J,EAAK3C,IAAI,CAAG2E,IAAOhC,EAAK3C,IAAI,EAAE4E,MAAM,GAAK,KACnDyG,SAAWrL,GAAc0H,GAAqB1H,EAAMmC,EADxBwC,CAE5B8H,WAAW,eACXC,gBAAiBjR,EAAE,0BAI/B,UAACkP,EAAAA,CAAGA,CAAAA,CAACG,GAAI,EAAGrB,UAAU,0BAClB,UAACwB,EAAAA,CAAIA,CAACC,KAAK,WACA,IAAN/I,EACG,UAAC6K,MAAAA,CAAAA,GAED,UAACC,EAAAA,CAAMA,CAAAA,CACHC,QAAQ,YACRxB,MAAO,CAAEyB,UAAW,MAAO,EAC3BC,QAAS,GAAY9G,GAAWhE,EAAGH,YAElC1G,EAAE,oBA1DjB0G,IAmElB,UAACuI,EAAAA,CAAGA,CAAAA,UACA,UAACC,EAAAA,CAAGA,CAAAA,CAACG,EAAE,IAACC,GAAG,aACP,UAACkC,EAAAA,CAAMA,CAAAA,CACHpH,GAAG,aACHqH,QAAQ,YACRxB,MAAO,CAAEyB,UAAW,OAAQE,aAAc,MAAO,EACjDD,QAAShH,YAER3K,EAAE,wBAIf,UAACiP,EAAAA,CAAGA,CAAAA,CAACjB,UAAU,gBACX,WAACkB,EAAAA,CAAGA,CAAAA,WACA,UAAChB,EAAAA,CAAIA,CAACoD,IAAI,WACN,UAAC/G,IAAAA,UAAGvK,EAAE,0BAEV,UAACoP,KAAAA,CAAAA,QAGT,UAACH,EAAAA,CAAGA,CAAAA,CAACjB,UAAU,gBACX,UAACkB,EAAAA,CAAGA,CAAAA,UACA,UAACM,EAAAA,CAAIA,CAACC,KAAK,WACP,WAACoC,EAAAA,CAAIA,CAAAA,CACDC,UAAW1P,EACX2P,SAAU,GAAY1P,EAAoB2P,GAC1C5H,GAAG,qCAEF,IACA9H,EAAS2E,GAAG,CAAC,CAACC,EAAWR,IAElB,WAACuL,EAAAA,CAAGA,CAAAA,CAASC,SAAU,GAAS,OAANxL,EAAI,GAAKlG,MAAO,gBAAsB,OAANkG,EAAI,aAC1D,WAACuI,EAAAA,CAAGA,CAAAA,CAACjB,UAAU,iBACX,UAACkB,EAAAA,CAAGA,CAAAA,CAACG,GAAI,WACL,WAACG,EAAAA,CAAIA,CAACC,KAAK,EAACQ,MAAO,CAAEkC,WAAY,MAAO,YACpC,UAAC3C,EAAAA,CAAIA,CAACE,KAAK,EAAC1B,UAAU,0BACjBhO,EAAE,kCAEP,WAAC2P,EAAAA,EAAWA,CAAAA,CACR5I,KAAK,cACLqD,GAAG,cACHpD,MAAOE,EAAK1E,WAAW,CACvBoN,SAAU,GAAYhJ,GAAUC,EAAGH,GACnCmJ,QAAQ,IACRC,aAAc9P,EAAE,iCAEhB,UAAC+P,SAAAA,CAAO/I,MAAM,YAAIhH,EAAE,wBACnB0D,GAAiBuD,GAAG,CAAC,CAACmL,EAAQlK,IAEvB,UAAC6H,SAAAA,CAAgB/I,MAAOoL,EAAOhK,GAAG,UAC7BgK,EAAO5R,KAAK,EADJ0H,YAQjC,UAACgH,EAAAA,CAAGA,CAAAA,CAACG,GAAI,WACL,WAACG,EAAAA,CAAIA,CAACC,KAAK,EACPQ,MAAO,CACHkC,WAAY,OACZjC,SAAU,OACd,YAEA,UAACV,EAAAA,CAAIA,CAACE,KAAK,WAAE1P,EAAE,sBACf,UAACmQ,EAAAA,EAAWA,CAAAA,CACRC,gBAAiB,CACbC,gBAAiBrQ,EAAE,mBACnBsQ,oBACI,4BACR,EACAV,SAAU,GAAYhJ,GAAUC,EAAGH,GACnC6J,QAAS/M,GACTwD,MAAOE,EAAKxE,iBAAiB,CAC7BsL,UAAW,cACXwC,WAAY,0BAIxB,UAACtB,EAAAA,CAAGA,CAAAA,CAACG,GAAI,WACL,WAACG,EAAAA,CAAIA,CAACC,KAAK,EAACQ,MAAO,CAAEkC,WAAY,MAAO,YACpC,UAAC3C,EAAAA,CAAIA,CAACE,KAAK,EAAC1B,UAAU,0BACjBhO,EAAE,YAEP,WAAC2P,EAAAA,EAAWA,CAAAA,CACR5I,KAAK,sBACLqD,GAAG,sBACHpD,MAAOE,EAAKrE,MAAM,CAClB+M,SAAU,GAAYhJ,GAAUC,EAAGH,GACnCmJ,QAAQ,IACRC,aAAc9P,EAAE,iCAEhB,UAAC+P,SAAAA,CAAO/I,MAAM,YAAIhH,EAAE,kBACnBoD,GAAiB6D,GAAG,CAAC,CAACoL,EAAQnK,IAC3B,UAAC6H,SAAAA,CAAgB/I,MAAOqL,EAAOjK,GAAG,UAC7BiK,EAAO7R,KAAK,EADJ0H,eAQjC,UAAC+G,EAAAA,CAAGA,CAAAA,UACA,UAACC,EAAAA,CAAGA,CAAAA,UACA,WAACM,EAAAA,CAAIA,CAACC,KAAK,YACP,UAACD,EAAAA,CAAIA,CAACE,KAAK,WAAE1P,EAAE,iBACf,UAAC4Q,EAAAA,CAAeA,CAAAA,CAACC,YAAa3J,EAAKtE,gBAAgB,CAAEgN,SAAU,GAA4B0C,CA50BvJ,SAASA,CAAuB,CAAE5L,CAAM,EACpC,IAAMoF,EAAa,IAAIxJ,EAAS,CAChCwJ,CAAU,CAACpF,EAAE,CAAC9D,gBAAgB,CAAGoE,EACjCzE,EAAYuJ,GAChB,EAw0BoKgF,EAAKpK,YAIzH,UAAC6K,MAAAA,UACU,IAAN7K,EACG,UAAC6L,OAAAA,CAAAA,GAED,UAACrD,EAAAA,CAAGA,CAAAA,CAACsD,EAAE,IAAClD,GAAG,aACP,UAACkC,EAAAA,CAAMA,CAAAA,CACHO,SAAU,GAAY1P,EAAoB2P,GAC1CP,QAAQ,YACRE,QAAU9K,GAAWL,GAAUK,EAAGH,YAEjC1G,EAAE,kBAzFb0G,IAiGlB,UAACuL,EAAAA,CAAGA,CAAAA,CACAzR,MACI,UAAC+Q,MAAAA,UACG,UAACgB,OAAAA,CAAKZ,QAASvL,YACX,UAACqM,EAAAA,CAAeA,CAAAA,CAACC,KAAMC,EAAAA,GAAMA,CAAEC,MAAM,2BASrE,WAAC1D,EAAAA,CAAGA,CAAAA,CAAClB,UAAU,iBACX,UAACE,EAAAA,CAAIA,CAACoD,IAAI,WACN,UAAC/G,IAAAA,UAAGvK,EAAE,oBAEV,UAACoP,KAAAA,CAAAA,MAEL,UAACH,EAAAA,CAAGA,CAAAA,UACA,UAACC,EAAAA,CAAGA,CAAAA,UACA,UAAC2D,EAAAA,CAAaA,CAAAA,CACVC,MAAOvR,EACPwR,QAAStR,EACTuR,SAAU,GAAa3F,GAAMjD,GAC7B6I,eAAgB,GAAoBtF,GAAUC,SAI1D,WAACsB,EAAAA,CAAGA,CAAAA,CAAClB,UAAU,sBACX,UAACE,EAAAA,CAAIA,CAACoD,IAAI,WACN,UAAC/G,IAAAA,UAAGvK,EAAE,iBAEV,UAACoP,KAAAA,CAAAA,MAEL,UAACH,EAAAA,CAAGA,CAAAA,UACA,UAACC,EAAAA,CAAGA,CAAAA,UACA,UAAC2D,EAAAA,CAAaA,CAAAA,CACVrF,KAAK,cACLsF,MAAOjR,EACPkR,QAASpR,EACTqR,SAAU,GAAa3F,GAAMjD,GAC7B6I,eAAgB,GAAoBpF,GAAaC,SAI7D,UAACmB,EAAAA,CAAGA,CAAAA,CAACjB,UAAU,gBACX,WAACkB,EAAAA,CAAGA,CAAAA,WACA,UAAChB,EAAAA,CAAIA,CAACoD,IAAI,WACN,UAAC/G,IAAAA,UAAGvK,EAAE,oBAEV,UAACoP,KAAAA,CAAAA,GACD,UAACI,EAAAA,CAAIA,CAAC0B,KAAK,EACPlD,UAAU,MACVR,KAAK,WACL4D,SAAU,CAAC5M,GACXoL,SAAU,IAAM/L,GAAgB,CAACD,IACjCmD,KAAK,UACLrB,QAAS9B,GACTuE,MAAOnI,EAAE,yCAIrB,UAACiP,EAAAA,CAAGA,CAAAA,CAACjB,UAAU,gBACX,WAACkB,EAAAA,CAAGA,CAAAA,WACA,UAACsC,EAAAA,CAAMA,CAAAA,CAACxD,UAAU,OAAOR,KAAK,SAASiE,QAAQ,UAAUpD,IAAK1N,EAAWgR,QAASvF,YAC7EpM,EAAE,YAEP,UAACwR,EAAAA,CAAMA,CAAAA,CAACxD,UAAU,OAAO2D,QA1gBhC,CA0gByCuB,IAzgB1D/M,GAAcvB,IACdpD,EAAsB,EAAE,EACxBE,EAAiB,EAAE,EACnBI,EAAiB,EAAE,EACnBF,EAAoB,EAAE,EACtB+B,GAAoB,EAAE,EACtBS,GAAQ,EAAE,EACV7B,EAAY,EAAE,EAEd4Q,OAAOC,QAAQ,CAAC,EAAG,EACvB,EA+f4E3B,QAAQ,gBACnDzR,EAAE,WAEP,UAACqT,IAAIA,CAACC,KAAK,aAAaC,GAAG,sBACvB,UAAC/B,EAAAA,CAAMA,CAAAA,CAACC,QAAQ,qBAAazR,EAAE,0BAOtD+D,IAAS,UAACyP,EAAAA,CAAWA,CAAAA,CAAChG,KAAK,YAAYpD,GAAInG,OAGxD,8IC3+BO,IAAMwP,EAAQ,CACnBC,WA1C4C,OAAC,MAC7C3M,CAAI,eACJ4M,CAAa,UACb/D,CAAQ,CACRE,cAAY,CACZ8D,UAAQ,CACT,GACO,QAAErF,CAAM,SAAEsF,CAAO,CAAE,CAAGC,CAAAA,EAAAA,EAAAA,EAAAA,CAAgBA,GACtCC,EAAWF,CAAO,CAAC9M,EAAK,EAAIwH,CAAM,CAACxH,EAAK,CAGzBmE,EAAAA,OAAa,CAAC,IAAO,OAAEnE,EAAK,EAAI,CAACA,EAAK,EAG3D,IAAMiN,EAAoB9I,EAAAA,QAAc,CAACjE,GAAG,CAAC2M,EAAU,GACrD,EAAI1I,cAAoB,CAAC+I,IAEnBC,IAF2B,KAxC5BA,CAAmB,EAC1B,MAAwB,UAAjB,OAAOrU,GAAgC,OAAVA,CACtC,EAwCmBoU,EAAMpU,KAAK,EACfqL,CADkB,CAClBA,YAAkB,CAAC+I,EAA6C,MACrElN,EACA,GAAGkN,EAAMpU,KAAK,GAIboU,GAGT,MACE,WAAC1C,MAAAA,WACC,UAACA,MAAAA,CAAIvD,UAAU,uBACZgG,IAEFD,GACC,UAACxC,MAAAA,CAAIvD,UAAU,oCACZ8B,GAAiB,kBAAOvB,CAAM,CAACxH,EAAK,CAAgBwH,CAAM,CAACxH,EAAK,CAAGoN,OAAO5F,CAAM,CAACxH,GAAK,MAKjG,EAIEqN,UAhE0C,OAAC,IAAEhK,CAAE,OAAEjC,CAAK,CAAEnB,OAAK,CAAED,MAAI,UAAEqK,CAAQ,CAAE,GACzE,QAAE9E,CAAM,eAAE+H,CAAa,CAAE,CAAGP,CAAAA,EAAAA,EAAAA,EAAAA,CAAgBA,GAC5CQ,EAAYvN,GAAQqD,EAE1B,MACE,UAACoF,EAAAA,CAAIA,CAAC0B,KAAK,EACT1D,KAAK,QACLpD,GAAIA,EACJjC,MAAOA,EACPnB,MAAOA,EACPD,KAAMuN,EACN5O,QAAS4G,CAAM,CAACgI,EAAU,GAAKtN,EAC/B4I,SAAU,IACRyE,EAAcC,EAAWzN,EAAEC,MAAM,CAACE,KAAK,CACzC,EACAoK,SAAUA,EACVmD,MAAM,KAGZ,CA8CA,EAAE,ECzEcC,CAAAA,CACL9D,EAAAA,EAAAA,CACEf,EAAAA,EAAAA,6HC8Bb,MAvCoB,OAAC,MAAEnC,CAAI,CAAEpD,IAAE,CAAO,GAC9B,CAACqK,EAAMC,EAAQ,CAAG5T,CAAAA,EAAAA,EAAAA,KAsCA6T,EAAC,CAtCD7T,CAAQA,CAAC,GAC3B,GAAEd,CAAC,CAAE,CAAGE,CAAAA,EAAAA,EAAAA,EAAAA,CAAcA,CAAC,UAY7B,MAVA8J,CAAAA,EAAAA,EAAAA,SAAAA,CAASA,CAAC,KACJyK,EAAO,EACTG,CADY,UACD,IAAMF,EAAQD,EAAO,GAAI,KAEpCvH,IAAAA,IAAW,CAAC,CACV2H,SAAU,iBACV/O,MAAO,CAAEsE,GAAIA,EAAI0K,OAAQtH,CAAK,CAChC,EAEJ,GAEE,WAACuH,EAAAA,CAAKA,CAAAA,CAACC,MAAM,YACX,UAACzD,MAAAA,CAAIvD,UAAU,6BACb,UAACuD,MAAAA,CAAIvD,UAAU,uBACb,UAACyE,EAAAA,CAAeA,CAAAA,CAACC,KAAMuC,EAAAA,GAAOA,CAAErC,MAAM,UAAUsC,KAAK,WAGzD,UAAC3D,MAAAA,CAAIvD,UAAU,4BACb,WAACmH,IAAAA,CAAEnH,UAAU,iBACVR,EAAK,IAAExN,EAAE,oCACV,UAACoV,KAAAA,CAAAA,GACD,UAACC,QAAAA,UAAOrV,EAAE,mCACV,UAACoV,KAAAA,CAAAA,GACD,UAACC,QAAAA,UACC,WAAC9K,IAAAA,WACE,IACAvK,EAAE,kBAAkB,IAAEyU,EAAK,IAAEzU,EAAE,iCAO9C,oFCbA,IAAMmO,EAAwBmH,CAAAA,EAAAA,EAAAA,UAAAA,CAAUA,CAA8C,CAACzV,EAAOwO,KAC5F,GAAM,UAAEuF,CAAQ,UAAExF,CAAQ,CAAEmH,cAAY,WAAEvH,CAAS,YAAEwH,CAAU,eAAE1G,CAAa,CAAE,GAAG2G,EAAM,CAAG5V,EAGtF6V,EAAmBC,EAAAA,EAAU,GAAGC,KAAK,CAAC,CAAC,GAE7C,MACE,UAACC,EAAAA,EAAMA,CAAAA,CACL/G,cAAeA,GAAiB,CAAC,EACjC4G,iBAAkBA,EAClBtH,SAAU,CAAC9B,EAA6BC,KAEtC,IAAMuJ,EAAuB,CAC3BrJ,eAAgB,KAAO,EACvBsJ,gBAAiB,KAAO,EACxBC,cAAe,KACflP,OAAQ,KACRmP,YAAa,IAAIC,MAAM,UACvBC,SAAS,EACTC,WAAY,GACZC,kBAAkB,EAClBC,WAAY,EACZC,WAAW,EACXC,UAAWnR,KAAKoR,GAAG,GACnBjJ,KAAM,SACNkJ,mBAAoB,KAAM,EAC1BC,qBAAsB,KAAM,EAC5BC,QAAS,KAAO,CAClB,CAEIxI,IAEFA,EAAS0H,EAAWxJ,EAFR,EAIhB,EACC,GAAGmJ,CAAI,UAEP,GACC,UAACjG,EAAAA,EAAIA,CAAAA,CACHnB,IAAKA,EACLD,SAAUyI,EAAYzK,YAAY,CAClCmJ,aAAcA,EACdvH,UAAWA,EACXwH,WAAYA,WAES,YAApB,OAAO5B,EAA0BA,EAASiD,GAAejD,KAKpE,GAEAzF,EAAsB2I,WAAW,CAAG,wBAEpC,MAAe3I,qBAAqBA,EAAC,yEClF9B,IAAMuC,EAAY,OAAC,CACxB3J,MAAI,IACJqD,CAAE,UACFyF,CAAQ,WACRc,CAAS,cACTb,CAAY,UACZF,CAAQ,OACR5I,CAAK,IACLuM,CAAE,WACFwD,CAAS,CACTC,MAAI,SACJC,CAAO,CACP,GAAGpX,EACC,GAuBJ,MACE,UAACqX,EAAAA,EAAKA,CAAAA,CAACnQ,KAAMA,EAAMoQ,SAtBJ,CAsBcA,GApB7B,IAAMC,EAA2B,UAAf,OAAOC,EAAmBA,EAAMlD,OAAOkD,GAAO,WAC5DxH,GAAa,EAACwH,GAA4B,IAA7B,CAAQD,EAAUtK,IAAI,EAAO,CAAC,CACtCgD,EAD0C,KAC1CA,EAAAA,KAAAA,EAAAA,EAAca,SAAS,GAAI,EAA3Bb,uBAGLa,GAAa,CAACA,EAAU0G,GACnBvH,GADyB,IACzBA,EAAAA,KAAAA,EAAAA,EAAca,SAAAA,GAAa,EAA3Bb,cAGLmH,GAAWI,GAET,CADU,CADI,GACAC,OAAOL,GACdM,IAAI,CAACF,GACPvH,GADa,IACbA,EAAAA,KAAAA,EAAAA,EAAcmH,OAAO,GAAI,IAAzBnH,mBAKb,WAIK,OAAC,OAAE0H,CAAK,MAAEC,CAAI,CAAO,SACpB,iCACE,UAACjI,EAAAA,CAAIA,CAACkI,OAAO,EACV,GAAGF,CAAK,CACR,GAAG3X,CAAK,CACTuK,GAAIA,EACJmJ,GAAIA,GAAM,QACVyD,KAAMA,EACNW,UAAWF,EAAK5D,OAAO,EAAI,CAAC,CAAC4D,EAAKtK,KAAK,CACvCyC,SAAW/I,IACT2Q,EAAM5H,QAAQ,CAAC/I,GACX+I,GAAUA,EAAS/I,EACzB,EACAG,MAAOA,KAAU4Q,MAAY5Q,EAAQwQ,EAAMxQ,KAAK,GAEjDyQ,EAAK5D,OAAO,EAAI4D,EAAKtK,KAAK,CACzB,UAACqC,EAAAA,CAAIA,CAACkI,OAAO,CAACG,QAAQ,EAACrK,KAAK,mBACzBiK,EAAKtK,KAAK,GAEX,UAKd,EAAE,EAIyB,OAAC,MAC1BpG,CAAI,IACJqD,CAAE,UACFyF,CAAQ,cACRC,CAAY,UACZF,CAAQ,OACR5I,CAAK,UACL4M,CAAQ,CACR,GAAG/T,EACC,GAUJ,MACE,UAACqX,EAAAA,EAAKA,CAAAA,CAACnQ,KAAMA,EAAMoQ,SATJ,CAScA,GAR7B,GAAItH,GAAa,EAACwH,GAAe,IAAhB,CAAQA,CAAQ,CAAC,CAChC,EADoC,IAC7BvH,OAAAA,EAAAA,KAAAA,EAAAA,EAAca,SAAAA,GAAa,EAA3Bb,sBAIX,WAIK,OAAC,OAAE0H,CAAK,CAAEC,MAAI,CAAO,SACpB,iCACE,UAACjI,EAAAA,CAAIA,CAACkI,OAAO,EACXnE,GAAG,SACF,GAAGiE,CAAK,CACR,GAAG3X,CAAK,CACTuK,GAAIA,EACJuN,UAAWF,EAAK5D,OAAO,EAAI,CAAC,CAAC4D,EAAKtK,KAAK,CACvCyC,SAAU,IACR4H,EAAM5H,QAAQ,CAAC/I,GACX+I,GAAUA,EAAS/I,EACzB,EACAG,WAAiB4Q,IAAV5Q,EAAsBA,EAAQwQ,EAAMxQ,KAAK,UAE/C4M,IAEF6D,EAAK5D,OAAO,EAAI4D,EAAKtK,KAAK,CACzB,UAACqC,EAAAA,CAAIA,CAACkI,OAAO,CAACG,QAAQ,EAACrK,KAAK,mBACzBiK,EAAKtK,KAAK,GAEX,UAKd,EAAE,8LC7FF,IAAI2K,EAAmB,EAAE,CAEnBC,EAAiB,CACrBC,KAAM,EACNC,QAAS,OACTC,cAAe,SACfC,WAAY,SACZC,eAAgB,SAChBC,MAAO,OACPC,OAAQ,OACRC,YAAa,GACbC,YAAa,UACbC,gBAAiB,UACjB7F,MAAO,QACP8F,WAAY,2BACZC,QAAS,MACX,EAYMC,EAAuB,CAC3BX,QAAS,OACTU,QAAS,OACTN,MAAO,OACPQ,OAAQ,iBACRX,cAAe,SACfE,eAAgB,aAChBU,SAAU,OACVpH,UAAW,EACb,EAcMqH,EAAM,CACVV,MAAO,OACT,EAEMW,EAAmB,CACvBR,YAAa,SACf,EA4WA,EA1WsB,IACpB,IAmSIS,EAnSE,GAAEjZ,CAAC,CAAE,CAAGE,CAAAA,EAAAA,EAAAA,EAAAA,CAAcA,CAAC,GAyWhB2S,OAxWP,CAACqG,EAAWC,EAAa,CAAGrY,CAAAA,CAwWP,CAxWOA,EAAAA,QAAAA,CAAQA,CAAC,IACrC,CAACsY,EAAYC,EAAc,CAAGvY,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,GACtCkF,EACU,eAAdnG,EAAM2N,IAAI,CAAoB,UAAW8L,UAAwB,CAC7D,CAACC,EAAOC,EAAS,CAAG1Y,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAQ,EAAE,EACtC,CAAC2Y,EAAOC,EAAS,CAAG5Y,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,EAAC,GAC7B,CAAC6Y,EAAaC,EAAe,CAAG9Y,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAW,EAAE,EAErD+Y,EAAWha,GAAwB,gBAAfA,EAAM2N,IAAI,CAAqB,SAAW,SAC9DsM,EAAc,MAAO1P,IACZ,MAAM9C,EAAAA,CAAUA,CAACyS,MAAM,CAAC,GAAe3P,MAAAA,CAAZyP,EAAS,KAAM,OAAHzP,GACtD,EAEM4P,EAAa,IACjBX,EAAcY,GACdd,GAAa,EACf,EAEMe,EAAe,CAACrT,EAA8DsT,KAClF,IAAMC,EAAQ,IAAIT,EAAY,CAC9BS,CAAK,CAACD,EAAM,CAAGtT,EAAEC,MAAM,CAACE,KAAK,CAC7B4S,EAAeQ,EACjB,EAEMC,EAAe,IAEnB,OAAQC,GADiBL,EAAKlT,IAAI,CAACwT,KAAK,CAAC,KAAKC,GAAG,IAE/C,IAAK,MACL,IAAK,MACL,IAAK,OACL,IAAK,MACL,IAAK,MACH,MAAO,UAACzB,MAAAA,CAAI0B,IAAKR,EAAKS,OAAO,CAAEzK,MAAO8I,GACxC,KAAK,MACH,MACE,UAACA,MAAAA,CACC0B,IAAI,gCACJzM,UACiB,gBAAfnO,EAAM2N,IAAI,CAAqB,aAAe,cAItD,KAAK,OAmBL,QAlBE,MACE,UAACuL,MAAAA,CACC0B,IAAI,iCACJzM,UACiB,gBAAfnO,EAAM2N,IAAI,CAAqB,aAAe,cAItD,KAAK,MACL,IAAK,OACH,MACE,UAACuL,MAAAA,CACC0B,IAAI,gCACJzM,UACiB,gBAAfnO,EAAM2N,IAAI,CAAqB,aAAe,cAaxD,CACF,EAEMmN,EAAY,IAAMxB,GAAa,GAE/ByB,EAAgB,KACpBzB,GAAa,EACf,EAEM0B,EAAgB,IAEpB,IAAM3Q,EACJ4Q,GAFa1B,CAAAA,GAEG0B,EAAa1S,GAAG,CAC5B,CAAEsF,SAAUoN,EAAa1S,GAAG,EAC5B,CAAE6R,KAAMa,CAAa,EACrBC,EAASlO,IAAAA,SAAW,CAACiL,EAAM5N,GAE3B8Q,EAAY,IAAIrB,EAAY,CAClCqB,EAAUrU,MAAM,CAACoU,EAAQ,GACzBnB,EAAeoB,GAEflB,EAAYhC,CAAI,CAACiD,EAAO,CAACrN,QAAQ,EACjCoK,EAAKnR,MAAM,CAACoU,EAAQ,GACpBlb,EAAMmT,QAAQ,CAAC8E,EAAMjY,EAAMsa,KAAK,CAAGta,EAAMsa,KAAK,CAAG,GACjD,IAAMc,EAAW,IAAI1B,EAAM,CAC3B0B,EAAStU,MAAM,CAACsU,EAASC,OAAO,CAACJ,GAAe,GAChDtB,EAASyB,GACT9B,GAAa,EACf,EAEMgC,EAAc5B,EAAMtS,GAAG,CAAC,CAACgT,EAAWvT,IAEtC,WAAC6K,MAAAA,WACC,UAACrC,EAAAA,CAAGA,CAAAA,CAACsD,GAAI,YACP,WAACjB,MAAAA,CAAIvD,UAAU,gBACb,UAACkB,EAAAA,CAAGA,CAAAA,CACFG,GAAI,EACJC,GAAI,EACJtB,UACiB,8CAAfnO,EAAM2N,IAAI,CACN,gDACA,oDAGL6M,EAAaJ,KAEhB,UAAC/K,EAAAA,CAAGA,CAAAA,CAACG,GAAI,EAAGC,GAAI,EAAGtB,UAAU,6BAC3B,WAACwB,EAAAA,CAAIA,CAAAA,WACH,WAACA,EAAAA,CAAIA,CAACC,KAAK,EAAC2L,UAAU,qBACpB,UAAC5L,EAAAA,CAAIA,CAACE,KAAK,EAAC1B,UAAU,gBAAQhO,EAAE,cAChC,UAACwP,EAAAA,CAAIA,CAACkI,OAAO,EACXxC,KAAK,KACL1H,KAAK,OACL4D,QAAQ,IACRpK,MAAOiT,EAAKoB,aAAa,CAAGpB,EAAKoB,aAAa,CAAGpB,EAAKlT,IAAI,MAG9D,WAACyI,EAAAA,CAAIA,CAACC,KAAK,EAAC2L,UAAU,wBACpB,UAAC5L,EAAAA,CAAIA,CAACE,KAAK,WACR7P,kBAAM2N,IAAI,CACPxN,EAAE,uCACFA,EAAE,wBAER,UAACwP,EAAAA,CAAIA,CAACkI,OAAO,EACX4D,UAA0B,kBAAT9N,IAAI,CAAqB,SAAMoK,EAChD1C,KAAK,KACL1H,KAAK,OACL+N,YACiB,gBAAf1b,EAAM2N,IAAI,CACNxN,EAAE,kCACFA,EAAE,sCAERgH,MAAO2S,CAAW,CAACjT,EAAE,CACrBkJ,SAAU,GAAOsK,EAAarT,EAAGH,aAKzC,UAACwI,EAAAA,CAAGA,CAAAA,CACFG,GAAI,EACJC,GAAI,EACJtB,UAAU,gCACV2D,QAAS,IAAMqI,EAAWC,YAE1B,UAACzI,EAAAA,CAAMA,CAAAA,CAACC,QAAQ,gBAAQzR,EAAE,mBAIhC,WAAC+U,EAAAA,CAAKA,CAAAA,CAACC,KAAMkE,EAAWsC,OAAQb,YAC9B,UAAC5F,EAAAA,CAAKA,CAAC0G,MAAM,EAACC,WAAW,aACvB,UAAC3G,EAAAA,CAAKA,CAAC5F,KAAK,WAAEnP,EAAE,kBAElB,UAAC+U,EAAAA,CAAKA,CAAC/F,IAAI,WAAEhP,EAAE,qCACf,WAAC+U,EAAAA,CAAKA,CAAC4G,MAAM,YACX,UAACnK,EAAAA,CAAMA,CAAAA,CAACC,QAAQ,YAAYE,QAASiJ,WAClC5a,EAAE,YAEL,UAACwR,EAAAA,CAAMA,CAAAA,CAACC,QAAQ,UAAUE,QAAS,IAAMkJ,EAAcZ,YACpDja,EAAE,iBAlED0G,IA0EdsD,CAAAA,EAAAA,EAAAA,SAAAA,CAASA,CAAC,KACRuP,EAAMhQ,OAAO,CAAC,GAAUqS,IAAIC,eAAe,CAAC5B,EAAKS,OAAO,GACxD5C,EAAO,EAAE,EACR,EAAE,EAEL9N,CAAAA,EAAAA,EAAAA,SAAAA,CAASA,CAAC,KACRnK,EAAMoT,cAAc,CAAC0G,EACvB,EAAG,CAACA,EAAY,EAEhB3P,CAAAA,EAAAA,EAAAA,SAAAA,CAASA,CAAC,KACR4P,EAAe/Z,EAAMkT,OAAO,CAC9B,EAAG,CAAClT,EAAMkT,OAAO,CAAC,EAElB/I,CAAAA,EAAAA,EAAAA,SAAAA,CAASA,CAAC,KACRnK,GAAgC,SAAvBA,EAAMic,YAAY,EAAepC,GAAS,GAC/C7Z,GAASA,EAAMiT,KAAK,EAAE,EAaf,IAZMjT,EAAMiT,KAAK,CAAC7L,GAAG,CAAC,CAACC,EAAWgB,KACzC4P,EAAKxR,IAAI,CAAC,CACRoH,SAAUxG,EAAKkB,GAAG,CAClB+R,MAAOta,EAAMsa,KAAK,CAAGta,EAAMsa,KAAK,CAAG,EACnC3M,KAAMtG,EAAKH,IAAI,CAACwT,KAAK,CAAC,IAAI,CAAC,EAAE,GAEV,CACnB,GAAGrT,CAAI,CACPwT,QAAS,GAAwCxT,MAAAA,CAArCoS,8BAAsB,CAAC,gBAAuB,OAATpS,EAAKkB,GAAG,CAC3D,IAGkB,CAExB,EAAG,CAACvI,EAAMiT,KAAK,CAAC,EAEhB,IAAMiJ,EAAc,MAAOC,EAAqB7B,KAC9C,GAAI6B,EAAazV,MAAM,CAAG4T,EACxB,GAAI,CACF,CAF6B,GAEvBhW,EAAY,IAAI8X,SACtB9X,EAAK+X,MAAM,CAAC,OAAQF,CAAY,CAAC7B,EAAM,EACvC,IAAMgC,EAAM,MAAM7U,EAAAA,CAAUA,CAACsC,IAAI,CAACiQ,EAAU1V,EAAM,CAChD,eAAgB,qBAClB,GACA2T,EAAKxR,IAAI,CAAC,CACRoH,SAAUyO,EAAI/T,GAAG,CACjB6R,KAAM+B,CAAY,CAAC7B,EAAM,CACzBA,MAAOta,EAAMsa,KAAK,CAAGta,EAAMsa,KAAK,CAAG,EACnC3M,KAAMwO,CAAY,CAAC7B,EAAM,CAACpT,IAAI,CAACwT,KAAK,CAAC,IAAI,CAAC,EAAE,GAE9CwB,EAAYC,EAAc7B,EAAQ,EACpC,CAAE,MAAOhN,EAAO,CACd4O,EAAYC,EAAc7B,EAAQ,EACpC,MAEAta,EAAMmT,QAAQ,CAAC8E,EAAMjY,EAAMsa,KAAK,CAAGta,EAAMsa,KAAK,CAAG,EAErD,EAEMiC,EAASC,CAAAA,EAAAA,EAAAA,WAAAA,CAAWA,CAAC,MAAOC,IAChC,MAAMP,EAAYO,EAAc,GAChC,IAAMC,EAAWD,EAAarV,GAAG,CAAC,GAChCmE,OAAOoR,MAAM,CAACvC,EAAM,CAClBS,QAASkB,IAAIa,eAAe,CAACxC,EAC/B,IAEFR,EACID,EAAS,GAAe,IAAI7Q,KAAc4T,EAAS,EACnD/C,EAAS,IAAI+C,EAAS,CAC5B,EAAG,EAAE,EAkBC,CACJG,cAAY,eACZC,CAAa,cACbC,CAAY,cACZC,CAAY,cACZC,CAAY,gBACZC,CAAc,CACf,CAAGC,CAAAA,EAAAA,EAAAA,EAAAA,CAAWA,CAAC,CACdC,OACEpd,GAASA,EAAM2N,IAAI,CACf,+MACA,UACN0P,SAAUzD,EACV0D,QAAS,EACTC,QAASpX,SACToW,EACAzL,UAhCF,CAgCa0M,QAhCJA,CAA8B,EACrC,GAAiB,UAAU,CAAvBxD,GACF,GAAkC,SAAS,CAAvCI,EAAKzM,IAAI,CAAC8P,SAAS,CAAC,EAAG,GAIzB,OADAtQ,EAAAA,EAAKA,CAACG,KAAK,CAACnN,EAAE,6BACP,CAAEud,KAAM,oBAAqBC,QAAS,yBAA0B,CACzE,MACK,GAAiB,UAAU,CAAvB3D,GAC2B,OAAM,GAApCI,EAAKzM,IAAI,CAAC8P,SAAS,CAAC,EAAG,GAE3B,OADAtQ,EAAAA,EAAKA,CAACG,KAAK,CAACnN,EAAE,6BACP,CAAEud,KAAM,oBAAqBC,QAAS,yBAA0B,EAG3E,OAAO,IACT,CAkBA,GAEMvN,EAAQwN,CAAAA,EAAAA,EAAAA,OAAAA,CAAOA,CACnB,IAAO,EACL,GAAG1F,CAAS,CACZ,GAAI6E,EAAe5D,EAAc,CAAE0E,QAAS,iBAAkB,CAAC,CAC/D,GAAIb,EACA,CAAEa,QAAS,oBAAqB,EAChC,CAAEA,QAAS,iBAAkB,CAAC,CAClC,GAAIZ,EAAe,CAAEY,QAAS,gBAAiB,EAAI,aAAE1E,CAAY,CAAC,CACpE,EACA,CAAC4D,EAAcE,EAAa,EAK5B7D,EADEpZ,GAAwB,eAAe,CAA9BA,EAAM2N,IAAI,CAEnB,UAAC6H,QAAAA,CAAMpF,MAAO,CAAE2C,MAAO,SAAU,WAAI5S,EAAE,uBAIvC,UAACqV,QAAAA,CAAMpF,MAAO,CAAE2C,MAAO,SAAU,WAAI5S,EAAE,oBAI3C,IAAM2d,EACJZ,EAAexW,MAAM,CAAG,GAAKwW,CAAc,CAAC,EAAE,CAAC9C,IAAI,CAAC/E,IAAI,CAAGlP,EAC7D,MACE,iCACE,UAACuL,MAAAA,CACCvD,UAAU,yDACViC,MAAO,CAAEoI,MAAO,OAAQC,OAAQ,OAAQ,WAExC,WAAC/G,MAAAA,CAAK,GAAGmL,EAAa,OAAEzM,CAAM,EAAE,WAC9B,UAAC2N,QAAAA,CAAO,GAAGjB,GAAe,GAC1B,UAAClK,EAAAA,CAAeA,CAAAA,CAACC,KAAMmL,EAAAA,GAAgBA,CAAE3I,KAAK,KAAKtC,MAAM,SACzD,UAACuC,IAAAA,CAAElF,MAAO,CAAE2C,MAAO,UAAWhB,aAAc,KAAM,WAC/C5R,EAAE,mDAGJ,CAACyZ,GACA,WAACpE,QAAAA,CAAMpF,MAAO,CAAE2C,MAAO,SAAU,YAC/B,UAACrI,IAAAA,UAAE,UAAS,wCAGf0O,GACApZ,EAAM2N,IAAI,CACPmQ,GACE,CAFU,EAEV,QAACtI,QAAAA,CAAMrH,UAAU,6BACf,UAACyE,EAAAA,CAAeA,CAAAA,CACdC,KAAMoL,EAAAA,GAAmBA,CACzB5I,KAAK,KACLtC,MAAM,QACL,IACF5S,EAAE,4CAaV8c,CAVGa,EAWF,WAACtI,QAAAA,CAAMrH,UAAU,cAAciC,MAAO,CAAE2C,MAAO,SAAU,YACvD,UAACH,EAAAA,CAAeA,CAAAA,CACdC,KAAMoL,EAAAA,GAAmBA,CACzB5I,KAAK,KACLtC,MAAM,QACL,IACF5S,EAAE,mCAKVuZ,EAAMhT,MAAM,CAAG,GAAK,UAACgL,MAAAA,CAAItB,MAAO2I,WAAkBuC,MAGzD", "sources": ["webpack://_N_E/./components/common/RKIDatePicker.tsx", "webpack://_N_E/./pages/operation/Form.tsx", "webpack://_N_E/./components/common/FormikRadio.tsx", "webpack://_N_E/./components/common/FormValidation.tsx", "webpack://_N_E/./components/common/VspaceModal.tsx", "webpack://_N_E/./components/common/ValidationFormWrapper.tsx", "webpack://_N_E/./components/common/FormikTextInput.tsx", "webpack://_N_E/./components/common/ReactDropZone.tsx"], "sourcesContent": ["//Import Library\r\nimport React from 'react'\r\nimport DatePicker from \"react-datepicker\";\r\n\r\ninterface RKIDatePickerProps {\r\n  [key: string]: any;\r\n}\r\n\r\nconst RKIDatePicker = (props: RKIDatePickerProps) => {\r\n  return (\r\n    <DatePicker {...props}  />\r\n  )\r\n};\r\n\r\nexport default RKIDatePicker;\r\n", "//Import Library\r\nimport React, { useState, useRef, useEffect } from \"react\";\r\nimport { <PERSON><PERSON>, Card, Form, Container, Row, Col, Tab, Tabs } from \"react-bootstrap\";\r\nimport { FontAwesomeIcon } from \"@fortawesome/react-fontawesome\";\r\nimport { faPlus } from \"@fortawesome/free-solid-svg-icons\";\r\nimport { MultiSelect } from \"react-multi-select-component\";\r\nimport _ from \"lodash\";\r\nimport moment from \"moment\";\r\nimport Link from \"next/link\";\r\nimport { TextInput, SelectGroup } from \"../../components/common/FormValidation\";\r\nimport ValidationFormWrapper from \"../../components/common/ValidationFormWrapper\";\r\n\r\nimport toast from 'react-hot-toast';\r\nimport Router from \"next/router\";\r\n\r\n//Import services/components\r\nimport ReactDropZone from \"../../components/common/ReactDropZone\";\r\nimport apiService from \"../../services/apiService\";\r\nimport RKIDatePicker from \"../../components/common/RKIDatePicker\";\r\nimport VspaceModal from \"../../components/common/VspaceModal\";\r\nimport { useTranslation } from 'next-i18next';\r\nimport { EditorComponent } from \"../../shared/quill-editor/quill-editor.component\";\r\n\r\nconst OperationForm = (props: any) => {\r\n    const { t, i18n } = useTranslation('common');\r\n    const currentLang = i18n.language === \"fr\" ? \"en\" : i18n.language;\r\n    const countrySearch = i18n.language === \"de\" ? { title_de: \"asc\" } : { title: \"asc\" };\r\n    const titleSearch = currentLang ? `title.${currentLang}` : \"title.en\";\r\n    const formRef = useRef(null);\r\n    const buttonRef = useRef<HTMLButtonElement | null>(null);\r\n    const [groupVisibility, setGroupVisibility] = useState({\r\n        invitesCountry: \"\",\r\n        invitesRegion: [],\r\n        invitesOrganisationType: \"\",\r\n        invitesOrganisation: \"\",\r\n        invitesExpertise: \"\",\r\n        invitesNetWork: \"\",\r\n        visibility: true,\r\n    });\r\n\r\n    const initialTabState = {\r\n        institution: \"\",\r\n        expertise: [],\r\n        partner_expertise: [],\r\n        organisation_status: \"\",\r\n        work_description: \"\",\r\n        status: \"\",\r\n    };\r\n\r\n    const [currLang] = useState<string>(titleSearch);\r\n    const [dropZoneCollection, setDropZoneCollection] = useState<any[]>([]);\r\n    const [srcCollection, setSrcCollection] = useState<any[]>([]);\r\n    const [docSrcCollection, setDocSrcCollection] = useState<any[]>([]);\r\n    const [docCollection, setDocCollection] = useState<any[]>([]);\r\n    const [statusOperation, setOperationStatus] = useState<any[]>([]);\r\n    const [countryList, setcountryList] = useState<any[]>([]);\r\n    const [, setUsersList] = useState<any[]>([]);\r\n    const [defaultActiveKey, setdefaultActiveKey] = useState<number>(1);\r\n    const [partners, setPartners] = useState<any[]>([initialTabState]);\r\n    const [hazardTypes, sethazardTypes] = useState<any[]>([]);\r\n    const [regions, setRegion] = useState<any[]>([]);\r\n    const [hazards, setHazard] = useState<any[]>([]);\r\n    const [deploymentStatus, setDeploymentStatus] = useState<any[]>([]);\r\n    const [syndrome, setSyndrome] = useState<any[]>([]);\r\n    const [expertises, setExpertises] = useState<any[]>([]);\r\n    const [organizationList, setOrganizationList] = useState<any[]>([]);\r\n    const [virtualSpace, setVirtualSpace] = useState<boolean>(false);\r\n    const [localeLang] = useState<string>(currentLang);\r\n    const [modal, setModal] = useState<boolean>(false);\r\n    const [resId, setResId] = useState<any>(null);\r\n    const [form, setForm] = useState<any[]>([{ timetitle: \"\", iconclass: \"\", date: null }]);\r\n    const [virtualSpaceAccessPermission, setVirtualSpaceAccessPermission] = useState<boolean>(true);\r\n    const editform = props.routes && props.routes[0] === \"edit\" && props.routes[1];\r\n    const _initialState = {\r\n        title: \"\",\r\n        country: \"\",\r\n        world_region: \"\",\r\n        region: [],\r\n        status: \" \",\r\n        hazard_type: \"\",\r\n        hazard: [],\r\n        syndrome: null,\r\n        active_tab: 1,\r\n        start_date: null,\r\n        TimeLineStartDate: new Date(),\r\n        end_date: null,\r\n        description: \"\",\r\n        images: [],\r\n        document: [],\r\n        checked: false,\r\n        images_src: [],\r\n        doc_src: [],\r\n    };\r\n\r\n    const operationParams = {\r\n        query: {},\r\n        sort: countrySearch,\r\n        limit: \"~\",\r\n        languageCode: currentLang,\r\n    };\r\n    const [initialVal, setinitialVal] = useState<any>(_initialState);\r\n\r\n    const tabAdd = () => {\r\n        const _temp = [...partners];\r\n        _temp.push({\r\n            status: \"\",\r\n            institution: \"\",\r\n            expertise: [],\r\n            organisation_status: \"\",\r\n            work_description: \"\",\r\n            partner_expertise: [],\r\n        });\r\n        setPartners(_temp);\r\n        setdefaultActiveKey(_temp.length);\r\n    };\r\n\r\n    const removeTab = (_e: any, i: any) => {\r\n        partners.splice(i, 1);\r\n        const _temp = [...partners];\r\n        setPartners(_temp);\r\n        setdefaultActiveKey(_temp.length);\r\n        if (partners.length === 0) {\r\n            tabAdd();\r\n        }\r\n    };\r\n\r\n    const handleTab = (e: any, i: any) => {\r\n        if (e.target) {\r\n            const { name, value } = e.target;\r\n            partners[i][name] = value;\r\n            if (name === \"organisation_status\") {\r\n                partners[i][\"status\"] = value;\r\n            }\r\n        } else {\r\n            partners[i].partner_expertise = e;\r\n            partners[i].expertise = e.map((item: any) => {\r\n                return item.value;\r\n            });\r\n        }\r\n        setPartners([...partners]);\r\n    };\r\n\r\n    function handleDesTab(value: any, i: any) {\r\n        const _tempCosts = [...partners];\r\n        _tempCosts[i].work_description = value;\r\n        setPartners(_tempCosts);\r\n    }\r\n\r\n    const handleEndDateCheckBox = () => {\r\n        setinitialVal((prevState: any) => ({\r\n            ...prevState,\r\n            checked: !prevState.checked,\r\n            end_date: null,\r\n        }));\r\n    };\r\n\r\n    const getOperationInitialData = async (operationParamsinit: any) => {\r\n        const operationStatus = await apiService.get(\"/operation_status\", operationParamsinit);\r\n        if (operationStatus && Array.isArray(operationStatus.data)) {\r\n            setOperationStatus(operationStatus.data);\r\n        } //operation_status\r\n\r\n        const country = await apiService.get(\"/country\", operationParamsinit);\r\n        if (country && Array.isArray(country.data)) {\r\n            setcountryList(country.data);\r\n        } //country list\r\n\r\n        const hazardtype = await apiService.get(\"/hazardtype\", operationParamsinit);\r\n        if (hazardtype && Array.isArray(hazardtype.data)) {\r\n            sethazardTypes(hazardtype.data);\r\n        } // hazardtype\r\n\r\n        const syndromes = await apiService.get(\"/syndrome\", operationParamsinit);\r\n        if (syndromes && Array.isArray(syndromes.data)) {\r\n            setSyndrome(syndromes.data);\r\n        } //syndrome\r\n\r\n        const institutionParams = operationParamsinit;\r\n        institutionParams.select =\r\n            \"-contact_name -description -type -networks -expertise -hazard_types -hazards -address -focal_points -website -telephone -twitter -header -use_default_header -images -status -email -user -created_at -updated_at -primary_focal_point\";\r\n        institutionParams.query.status = { $ne: \"Request Pending\" };\r\n        const institutions = await apiService.get(\"/institution\", institutionParams);\r\n        if (institutions && Array.isArray(institutions.data)) {\r\n            setOrganizationList(institutions.data);\r\n        } // institution\r\n\r\n        const expertise = await apiService.get(\"/expertise\", operationParamsinit);\r\n        if (expertise && Array.isArray(expertise.data)) {\r\n            const _expertise = expertise.data.map((item: any, _i: any) => {\r\n                return { label: item.title, value: item._id };\r\n            });\r\n            setExpertises(_expertise);\r\n        }\r\n        const deployment = await apiService.get(\"/deploymentstatus\", operationParamsinit);\r\n        if (deployment && Array.isArray(deployment.data)) {\r\n            setDeploymentStatus(deployment.data);\r\n        }\r\n    };\r\n\r\n    const getdoccollection = (response: any, normalizePartner: any, end_date: any) => {\r\n        setDropZoneCollection(response.images ? response.images : []);\r\n        setSrcCollection(response.images_src ? response.images_src : []);\r\n        setDocCollection(response.document ? response.document : []);\r\n        setDocSrcCollection(response.doc_src ? response.doc_src : []);\r\n        setPartners(normalizePartner);\r\n        getRegion(response.country); // update region based on the country\r\n        getHazard(response.hazard_type); // update hazard based on the hazard type\r\n        setinitialVal((prevState: any) => ({ ...prevState, ...response }));\r\n        return end_date ? setinitialVal((prevState: any) => ({ ...prevState, checked: true })) : null;\r\n    };\r\n\r\n    const getnormalization = (item5: any, initRegions: any, _regionsId: any, normalizePartner: any) => {\r\n        normalizePartner.push({\r\n            institution: item5.institution && item5.institution._id,\r\n            partner_expertise: initRegions,\r\n            expertise: _regionsId,\r\n            organisation_status: item5 && item5.status ? item5.status._id : \"\",\r\n            status: item5 && item5.status ? item5.status._id : \"\",\r\n            work_description: item5.work_description,\r\n        });\r\n    };\r\n\r\n    const getresonse = (response: any) => {\r\n        const { status, country, syndromes, region, hazard_type, hazard, timeline, start_date, end_date } = response;\r\n        response.status = status && status._id ? status._id : \" \"; // status value\r\n        response.start_date = start_date ? moment(start_date).toDate() : null;\r\n        response.end_date = end_date ? moment(end_date).toDate() : null;\r\n        response.country = country && country._id ? country._id : \" \";\r\n        response.syndrome = syndromes && syndromes._id ? syndromes._id : null;\r\n        response.hazard_type = hazard_type && hazard_type._id ? hazard_type._id : \"\";\r\n        response.hazard = hazard\r\n            ? hazard.map((item1: any) => {\r\n                  return { label: item1.title[localeLang], value: item1._id };\r\n              })\r\n            : [];\r\n        response.region = region\r\n            ? region.map((item2: any) => {\r\n                  return { label: item2.title, value: item2._id };\r\n              })\r\n            : [];\r\n        timeline && setForm(timeline);\r\n    };\r\n\r\n    const getOperationsData = async () => {\r\n        const response = await apiService.get(`/operation/${props.routes[1]}`, operationParams);\r\n        if (response) {\r\n            const normalizePartner: any[] = [];\r\n            const { end_date, partners } = response;\r\n            getresonse(response);\r\n            partners &&\r\n                partners.forEach((item5: any) => {\r\n                    const initRegions =\r\n                        item5.expertise &&\r\n                        item5.expertise.map((item3: any) => {\r\n                            return { label: item3.title, value: item3._id };\r\n                        });\r\n                    const _regionsId =\r\n                        item5.expertise &&\r\n                        item5.expertise.map((item4: any) => {\r\n                            return item4._id;\r\n                        });\r\n                    getnormalization(item5, initRegions, _regionsId, normalizePartner);\r\n                });\r\n            getdoccollection(response, normalizePartner, end_date);\r\n        }\r\n    };\r\n\r\n\r\n    const getUserInformation = async () => {\r\n        const currentUser = await apiService.post(\"/users/getLoggedUser\", {});\r\n        if (currentUser && currentUser['roles']) {\r\n            const filteredRoles = currentUser['roles']?.filter((role: string) => (role == \"EMT_NATIONAL_FOCALPOINT\"));\r\n            if (filteredRoles.length > 0) {\r\n                setVirtualSpaceAccessPermission(false);\r\n            } else {\r\n                setVirtualSpaceAccessPermission(true);\r\n            }\r\n        }\r\n    }\r\n\r\n    useEffect(() => {\r\n        /* Start Update data for form */\r\n        if (editform) {\r\n            getOperationsData();\r\n        } /* End */\r\n\r\n        // get the data from operation APi\r\n\r\n        getOperationInitialData(operationParams);\r\n        getUserInformation();\r\n    }, []);\r\n\r\n    const clearValue = (obj: any) => {\r\n        setinitialVal((prevState: any) => ({\r\n            ...prevState,\r\n            ...obj,\r\n        }));\r\n        setGroupVisibility((prevState: any) => ({\r\n            ...prevState,\r\n            ...obj,\r\n        }));\r\n    };\r\n\r\n    const countryRegion = async (id: any) => {\r\n        let _regions: any[] = [];\r\n\r\n        if (id) {\r\n            const response = await apiService.get(`/country_region/${id}`, operationParams);\r\n            if (response && response.data) {\r\n                _regions = response.data.map((item: any, _i: any) => {\r\n                    return { label: item.title, value: item._id };\r\n                });\r\n                _regions.sort((a: any, b: any) => a.label.localeCompare(b.label));\r\n            }\r\n        }\r\n        return _regions;\r\n    };\r\n\r\n    const getRegion = async (id: any) => {\r\n        setRegion(await countryRegion(id));\r\n    };\r\n\r\n    const getHazard = async (id: any) => {\r\n        const regionsParams = {\r\n            query: { enabled: true },\r\n            sort: { [currLang]: \"asc\" },\r\n            limit: \"~\",\r\n            select: \"-description -first_letter -hazard_type -picture -picture_source  -created_at -updated_at\",\r\n        };\r\n        let _regions: any[] = [];\r\n        if (id) {\r\n            const response = await apiService.get(`/hazard_hazard_type/${id}`, regionsParams);\r\n            if (response && response.data) {\r\n                _regions = response.data.map((item: any) => ({\r\n                    label: item.title[localeLang],\r\n                    value: item._id,\r\n                }));\r\n            }\r\n        }\r\n        setHazard(_regions);\r\n    };\r\n\r\n    const addform = () => {\r\n        const a = { timetitle: \"\", iconclass: \"\", date: null };\r\n        setForm((oldArray: any) => [...oldArray, a]);\r\n    };\r\n\r\n    const removeForm = (_e: any, i: any) => {\r\n        form.splice(i, 1);\r\n        setForm([...form]);\r\n        if (form.length === 0) {\r\n            addform();\r\n        }\r\n    };\r\n\r\n    const handleChange = (e: any) => {\r\n        if (e.target) {\r\n            const { name, value } = e.target;\r\n            if (name === \"country\") {\r\n                const selectedIndex = e.target.selectedIndex;\r\n                if (e.target && selectedIndex && selectedIndex != null) {\r\n                    const worldRegion = e.target[selectedIndex].getAttribute(\"data-worldregion\");\r\n                    setinitialVal((prevState: any) => ({\r\n                        ...prevState,\r\n                        world_region: worldRegion,\r\n                    }));\r\n                }\r\n            }\r\n            setinitialVal((prevState: any) => ({ ...prevState, [name]: value }));\r\n            if (name === \"country\") {\r\n                getRegion(value);\r\n                clearValue({ region: [] });\r\n            }\r\n            if (name === \"hazard_type\") {\r\n                getHazard(value);\r\n                clearValue({ hazard: [] });\r\n            }\r\n        } else {\r\n            setinitialVal((prevState: any) => ({ ...prevState, region: e }));\r\n        }\r\n    };\r\n\r\n    React.useEffect(() => {\r\n        if (groupVisibility) {\r\n            const normalizeGroup: any = {};\r\n            Object.keys(groupVisibility).forEach((item: any, _i: any) => {\r\n                const _data: any[] =\r\n                    (groupVisibility as any)[item].length > 0 &&\r\n                    (groupVisibility as any)[item].map((d: any) => {\r\n                        return d.value;\r\n                    });\r\n                normalizeGroup[item] = _data ? _data : [];\r\n            });\r\n            getUsers(normalizeGroup);\r\n        }\r\n    }, [groupVisibility]);\r\n\r\n    const getUsers = async (normalizeGroup: any) => {\r\n        const { invitesCountry, invitesOrganisation } = normalizeGroup;\r\n        const groupParams = {\r\n            query: {\r\n                country: invitesCountry,\r\n                institution: invitesOrganisation,\r\n            },\r\n        };\r\n        const userInvites = await apiService.post(\"/user-invite\", groupParams);\r\n        if (userInvites && Array.isArray(userInvites)) {\r\n            const _users = userInvites.map((item: any, _i: any) => {\r\n                return { label: item.username, value: item._id };\r\n            });\r\n            setUsersList(_users);\r\n        }\r\n    };\r\n\r\n    //******To Handle Group Visibility******//\r\n    const bindHazard = (e: any) => {\r\n        setinitialVal((prevState: any) => ({\r\n            ...prevState,\r\n            hazard: e,\r\n        }));\r\n    };\r\n\r\n    const handleChangeforTimeline = (e: any, i: any) => {\r\n        const { name, value } = e.target;\r\n        const _tempCosts = [...form];\r\n        _tempCosts[i][name] = value;\r\n        setForm(_tempCosts);\r\n    };\r\n\r\n    const onChangeDate = (date: any, key: any) => {\r\n        if ((key === \"start_date\" && date == null) || key === \"start_date\") {\r\n            setinitialVal((prevState: any) => ({\r\n                ...prevState,\r\n                end_date: null,\r\n                start_date: date,\r\n            }));\r\n        } else {\r\n            setinitialVal((prevState: any) => ({\r\n                ...prevState,\r\n                [key]: date,\r\n            }));\r\n        }\r\n    };\r\n\r\n    const onChangeDateTimeline = (dateval: any, i: any) => {\r\n        form[i].date = dateval;\r\n        setForm([...form]);\r\n    };\r\n\r\n    const handleDescription = (value: any) => {\r\n        setinitialVal((prevState: any) => ({\r\n            ...prevState,\r\n            description: value,\r\n        }));\r\n    };\r\n\r\n\r\n\r\n    const handleErrorSubmit = (errors: any) => {\r\n        let searchIndex = -1;\r\n        let mapped: any[] = _.map(\r\n            partners,\r\n            _.partialRight(_.pick, [\"institution\", \"expertise\", \"status\", \"work_description\"])\r\n        );\r\n        let currentIndex = 0;\r\n        for (const i in mapped) {\r\n            if ((mapped[i] as any).institution === \"\") {\r\n                searchIndex = currentIndex;\r\n                break;\r\n            }\r\n            if ((mapped[i] as any).status === \"\") {\r\n                searchIndex = currentIndex;\r\n                break;\r\n            }\r\n            currentIndex++;\r\n        }\r\n        if (searchIndex > -1) {\r\n            setdefaultActiveKey(searchIndex + 1);\r\n            let elmnt = document.getElementById(\"btnAddForm\");\r\n            elmnt?.scrollIntoView();\r\n        }\r\n    };\r\n\r\n    const handleSubmit = async (event?: any, values?: any, actions?: any) => {\r\n        if (event && event.preventDefault) {\r\n            event.preventDefault();\r\n        }\r\n\r\n        if (buttonRef.current) {\r\n            buttonRef.current.setAttribute(\"disabled\", \"disabled\");\r\n        }\r\n\r\n        const mapped = _.map(\r\n            partners,\r\n            _.partialRight(_.pick, [\"institution\", \"expertise\", \"status\", \"work_description\"])\r\n        );\r\n        const obj = {\r\n            title: initialVal.title.trim(),\r\n            description: initialVal.description,\r\n            status: initialVal.status,\r\n            start_date: initialVal.start_date,\r\n            end_date: initialVal.end_date,\r\n            country: initialVal.country,\r\n            world_region: initialVal.world_region,\r\n            syndrome: initialVal.syndrome,\r\n            hazard_type: initialVal.hazard_type,\r\n            hazard: initialVal.hazard\r\n                ? initialVal.hazard.map((item: any, _i: any) => {\r\n                      return item.value;\r\n                  })\r\n                : [],\r\n            region: initialVal.region\r\n                ? initialVal.region.map((item: any, _i: any) => {\r\n                      return item.value;\r\n                  })\r\n                : [],\r\n            timeline: form.length > 0 && form[0].timetitle !== \"\" ? form : [],\r\n            partners: mapped,\r\n            images: initialVal.images,\r\n            images_src: initialVal.images_src,\r\n            document: initialVal.document,\r\n            doc_src: initialVal.doc_src,\r\n        };\r\n\r\n        let response;\r\n        let toastMsg;\r\n        if (editform) {\r\n            toastMsg = \"toast.Operationupdatedsuccessfully\";\r\n            response = await apiService.patch(`/operation/${props.routes[1]}`, obj);\r\n        } else {\r\n            toastMsg = \"toast.Operationaddedsuccessfully\";\r\n            response = await apiService.post(\"/operation\", obj);\r\n        }\r\n        if (response && response._id) {\r\n            if (virtualSpace) {\r\n                setResId(response?._id && response._id);\r\n                setModal(true);\r\n            } else {\r\n                toast.success(t(toastMsg));\r\n                Router.push(\"/operation/[...routes]\", `/operation/show/${response._id}`);\r\n            }\r\n        } else {\r\n            if (editform) { toastMsg = \"toast.OperationNotupdatedsuccessfully\";}\r\n            else { toastMsg = \"toast.OperationNotaddedsuccessfully\"; }\r\n\r\n            toast.error(t(toastMsg));\r\n        }\r\n\r\n        // Re-enable the submit button\r\n        if (buttonRef.current) {\r\n            buttonRef.current.removeAttribute(\"disabled\");\r\n        }\r\n    };\r\n\r\n    const resetHandler = () => {\r\n        setinitialVal(_initialState);\r\n        setDropZoneCollection([]);\r\n        setSrcCollection([]);\r\n        setDocCollection([]);\r\n        setDocSrcCollection([]);\r\n        setOrganizationList([]);\r\n        setForm([]);\r\n        setPartners([]);\r\n        // Reset validation state (Formik handles this automatically)\r\n        window.scrollTo(0, 0);\r\n    };\r\n\r\n    const getID = (id: any) => {\r\n        const imageIds: any[] = [];\r\n        const docIds: any[] = [];\r\n        if (id.length > 0) {\r\n            id.map((item: any) => {\r\n                if (\r\n                    item.type &&\r\n                    (item.type.includes(\"pdf\") ||\r\n                        item.type.includes(\"docx\") ||\r\n                        item.type.includes(\"xlsx\") ||\r\n                        item.type.includes(\"xls\"))\r\n                ) {\r\n                    docIds.push(item.serverID);\r\n                } else {\r\n                    imageIds.push(item.serverID);\r\n                }\r\n            });\r\n        }\r\n        setinitialVal((prevState: any) => ({ ...prevState, images: imageIds }));\r\n        setinitialVal((prevState: any) => ({ ...prevState, document: docIds }));\r\n    };\r\n\r\n    const getSource = (imgSrcArr: any) => {\r\n        setinitialVal((prevState: any) => ({ ...prevState, images_src: imgSrcArr }));\r\n    };\r\n\r\n    const getDocSource = (docSrcArr: any) => {\r\n        setinitialVal((prevState: any) => ({ ...prevState, doc_src: docSrcArr }));\r\n    };\r\n\r\n    return (\r\n        <Container className=\"formCard\" fluid>\r\n            <Card>\r\n                <ValidationFormWrapper onSubmit={handleSubmit} ref={formRef} onErrorSubmit={handleErrorSubmit} initialValues={initialVal} enableReinitialize={true}>\r\n                    <Card.Body>\r\n                        <Row>\r\n                            <Col>\r\n                                <Card.Title>{editform ? t(\"editOperation\") : t(\"addOperation\")}</Card.Title>\r\n                            </Col>\r\n                        </Row>\r\n                        <hr />\r\n                        <Row className=\"mb-3\">\r\n                            <Col md lg={6} sm={12}>\r\n                                <Form.Group>\r\n                                    <Form.Label className=\"required-field\">{t(\"CountryOrTerritory\")}</Form.Label>\r\n                                    <SelectGroup\r\n                                        name=\"country\"\r\n                                        id=\"country\"\r\n                                        value={initialVal.country}\r\n                                        onChange={handleChange}\r\n                                        required\r\n                                        errorMessage={t(\"thisfieldisrequired\")}\r\n                                    >\r\n                                        <option value=\"\">{t(\"SelectCountry\")}</option>\r\n                                        {countryList.map((item, i) => {\r\n                                            return (\r\n                                                <option\r\n                                                    key={i}\r\n                                                    data-worldregion={item.world_region._id}\r\n                                                    value={item._id}\r\n                                                >\r\n                                                    {item.title}\r\n                                                </option>\r\n                                            );\r\n                                        })}\r\n                                    </SelectGroup>\r\n                                </Form.Group>\r\n                            </Col>\r\n                            <Col md lg={6} sm={12}>\r\n                                <Form.Group style={{ maxWidth: \"600px\" }}>\r\n                                    <Form.Label>{t(\"CountryRegions\")}</Form.Label>\r\n                                    <MultiSelect\r\n                                        overrideStrings={{\r\n                                            selectSomeItems: t(\"SelectRegions\"),\r\n                                            allItemsAreSelected: \"All Regions are Selected\",\r\n                                        }}\r\n                                        options={regions}\r\n                                        value={initialVal.region}\r\n                                        onChange={handleChange}\r\n                                        className={\"region\"}\r\n                                        labelledBy={\"Select Country Regions\"}\r\n                                    />\r\n                                </Form.Group>\r\n                            </Col>\r\n                        </Row>\r\n                        <Row className=\"mb-3\">\r\n                            <Col md lg={4} sm={12}>\r\n                                <Form.Group>\r\n                                    <Form.Label className=\"required-field\">{t(\"HazardType\")}</Form.Label>\r\n                                    <SelectGroup\r\n                                        name=\"hazard_type\"\r\n                                        id=\"hazard_type\"\r\n                                        value={initialVal.hazard_type}\r\n                                        onChange={handleChange}\r\n                                        required\r\n                                        errorMessage={t(\"thisfieldisrequired\")}\r\n                                    >\r\n                                        <option value=\"\">{t(\"SelectHazardType\")}</option>\r\n                                        {hazardTypes.map((item, i) => {\r\n                                            return (\r\n                                                <option key={i} value={item._id}>\r\n                                                    {item.title}\r\n                                                </option>\r\n                                            );\r\n                                        })}\r\n                                    </SelectGroup>\r\n                                </Form.Group>\r\n                            </Col>\r\n                            <Col md lg={4} sm={12}>\r\n                                <Form.Group>\r\n                                    <Form.Label>{t(\"Hazard\")}</Form.Label>\r\n                                    <Form.Group>\r\n                                        <MultiSelect\r\n                                            overrideStrings={{\r\n                                                selectSomeItems: t(\"SelectHazard\"),\r\n                                                allItemsAreSelected: \"All Hazards are Selected\",\r\n                                            }}\r\n                                            options={hazards}\r\n                                            value={initialVal.hazard}\r\n                                            onChange={bindHazard}\r\n                                            className={\"hazard\"}\r\n                                            labelledBy={\"Select Hazards\"}\r\n                                        />\r\n                                    </Form.Group>\r\n                                </Form.Group>\r\n                            </Col>\r\n                            <Col md lg={4} sm={12}>\r\n                                <Form.Group>\r\n                                    <Form.Label>{t(\"Syndrome\")}</Form.Label>\r\n                                    <SelectGroup\r\n                                        name=\"syndrome\"\r\n                                        id=\"syndrome\"\r\n                                        value={initialVal.syndrome}\r\n                                        onChange={handleChange}\r\n                                    >\r\n                                        <option value=\"\">{t(\"SelectSyndrome\")}</option>\r\n                                        {syndrome.map((item, i) => (\r\n                                            <option key={i} value={item._id}>\r\n                                                {item.title}\r\n                                            </option>\r\n                                        ))}\r\n                                    </SelectGroup>\r\n                                </Form.Group>\r\n                            </Col>\r\n                        </Row>\r\n                        <Row className=\"mb-3\">\r\n                            <Col>\r\n                                <Form.Group>\r\n                                    <Form.Label className=\"required-field\">{t(\"Title\")}</Form.Label>\r\n                                    <TextInput\r\n                                        name=\"title\"\r\n                                        id=\"title\"\r\n                                        required\r\n                                        value={initialVal.title}\r\n                                        validator={(value: any) => value.trim() !== \"\"}\r\n                                        errorMessage={{\r\n                                            validator: t(\"PleaseAddtheTitle\"),\r\n                                        }}\r\n                                        onChange={handleChange}\r\n                                    />\r\n                                </Form.Group>\r\n                            </Col>\r\n                        </Row>\r\n                        <Row className=\"mb-3\">\r\n                            <Col>\r\n                                <Form.Group>\r\n                                    <Form.Label>{t(\"Description\")}</Form.Label>\r\n                                    <EditorComponent initContent={initialVal.description} onChange={(evt: React.ChangeEvent) => handleDescription(evt)} />\r\n                                </Form.Group>\r\n                            </Col>\r\n                        </Row>\r\n                        <Row className=\"mb-3\">\r\n                            {statusOperation.length ? (\r\n                            <Col md lg={3} sm={12}>\r\n                                \r\n                                    <Form.Group>\r\n                                        <Form.Label className=\"required-field\">{t(\"OperationStatus\")}</Form.Label>\r\n\r\n                                        <SelectGroup\r\n                                            name=\"status\"\r\n                                            id=\"status\"\r\n                                            value={initialVal.status}\r\n                                            onChange={handleChange}\r\n                                            required\r\n                                            errorMessage={t(\"thisfieldisrequired\")}\r\n                                        >\r\n                                            <option value=\"\">{t(\"SelectOperationStatus\")}</option>\r\n                                            {statusOperation.map((item, i) => {\r\n                                                return (\r\n                                                    <option key={i} value={item._id}>\r\n                                                        {item.title}\r\n                                                    </option>\r\n                                                );\r\n                                            })}\r\n                                        </SelectGroup>\r\n                                    </Form.Group>\r\n                            </Col>) : null}\r\n                            <Col md lg={3} sm={12}>\r\n                                <Form.Group>\r\n                                    <Row>\r\n                                        <Col>\r\n                                            <Form.Label>{t(\"OperationStartDate\")}</Form.Label>\r\n                                        </Col>\r\n                                    </Row>\r\n                                    <RKIDatePicker\r\n                                        selected={initialVal.start_date}\r\n                                        onChange={(date: any) => onChangeDate(date, \"start_date\")}\r\n                                        dateFormat=\"MMMM d, yyyy\"\r\n                                        placeholderText={t(\"SelectStartDate\")}\r\n                                    />\r\n                                </Form.Group>\r\n                            </Col>\r\n                            <Col md lg={2} sm={12}>\r\n                                <Form.Check\r\n                                    type=\"checkbox\"\r\n                                    checked={initialVal.checked}\r\n                                    onChange={handleEndDateCheckBox}\r\n                                    label={t(\"ShowEndDate\")}\r\n                                />\r\n                            </Col>\r\n                            {initialVal.checked && (\r\n                                <Col md lg={3} sm={12}>\r\n                                    <Form.Group>\r\n                                        <Row>\r\n                                            <Col>\r\n                                                <Form.Label>{t(\"OperationEndDate\")}</Form.Label>\r\n                                            </Col>\r\n                                        </Row>\r\n                                        <RKIDatePicker\r\n                                            selected={initialVal.end_date}\r\n                                            disabled={!initialVal.start_date}\r\n                                            onChange={(date: any) => onChangeDate(date, \"end_date\")}\r\n                                            dateFormat=\"MMMM d, yyyy\"\r\n                                            minDate={initialVal.start_date}\r\n                                            placeholderText={t(\"SelectEndDate\")}\r\n                                        />\r\n                                    </Form.Group>\r\n                                </Col>\r\n                            )}\r\n                        </Row>\r\n                        <Card.Text>\r\n                            <b>{t(\"Timeline\")}</b>\r\n                        </Card.Text>\r\n                        <hr />\r\n                        {form.map((item, i) => {\r\n                            return (\r\n                                <div key={i}>\r\n                                    <Row>\r\n                                        <Col md={4}>\r\n                                            <Form.Group>\r\n                                                <Form.Label>{t(\"Title\")}</Form.Label>\r\n                                                <TextInput\r\n                                                    name=\"timetitle\"\r\n                                                    id=\"timetitle\"\r\n                                                    value={item.timetitle}\r\n                                                    onChange={(e: any) => handleChangeforTimeline(e, i)}\r\n                                                />\r\n                                            </Form.Group>\r\n                                        </Col>\r\n                                        <Col md={3}>\r\n                                            <Form.Group>\r\n                                                <Form.Label>{t(\"IconClass\")}</Form.Label>\r\n                                                <SelectGroup\r\n                                                    name=\"iconclass\"\r\n                                                    id=\"iconclass\"\r\n                                                    value={item.iconclass}\r\n                                                    onChange={(e: any) => handleChangeforTimeline(e, i)}\r\n                                                >\r\n                                                    <option value=\"-1\">-Select-</option>\r\n                                                    <option value=\"1\">RFA</option>\r\n                                                    <option value=\"2\">Alert</option>\r\n                                                    <option value=\"3\">Mission to Country</option>\r\n                                                    <option value=\"4\">Calendar Event</option>\r\n                                                    <option value=\"5\">Documents</option>\r\n                                                    <option value=\"6\">Meeting</option>\r\n                                                    <option value=\"7\">Others</option>\r\n                                                </SelectGroup>\r\n                                            </Form.Group>\r\n                                        </Col>\r\n                                        <Col md={3}>\r\n                                            <Form.Group>\r\n                                                <Row>\r\n                                                    <Col>\r\n                                                        <Form.Label>{t(\"StartDate\")}</Form.Label>\r\n                                                    </Col>\r\n                                                </Row>\r\n                                                <RKIDatePicker\r\n                                                    selected={item.date ? moment(item.date).toDate() : null}\r\n                                                    onChange={(date: any) => onChangeDateTimeline(date, i)}\r\n                                                    dateFormat=\"MMMM d, yyyy\"\r\n                                                    placeholderText={t(\"SelectStartDate\")}\r\n                                                />\r\n                                            </Form.Group>\r\n                                        </Col>\r\n                                        <Col md={2} className=\"text-md-center\">\r\n                                            <Form.Group>\r\n                                                {i === 0 ? (\r\n                                                    <div></div>\r\n                                                ) : (\r\n                                                    <Button\r\n                                                        variant=\"secondary\"\r\n                                                        style={{ marginTop: \"30px\" }}\r\n                                                        onClick={(e: any) => removeForm(e, i)}\r\n                                                    >\r\n                                                        {t(\"Remove\")}\r\n                                                    </Button>\r\n                                                )}\r\n                                            </Form.Group>\r\n                                        </Col>\r\n                                    </Row>\r\n                                </div>\r\n                            );\r\n                        })}\r\n                        <Row>\r\n                            <Col md lg=\"4\">\r\n                                <Button\r\n                                    id=\"btnAddForm\"\r\n                                    variant=\"secondary\"\r\n                                    style={{ marginTop: \"27px\", marginBottom: \"20px\" }}\r\n                                    onClick={addform}\r\n                                >\r\n                                    {t(\"ADDANOTHERITEM\")}\r\n                                </Button>\r\n                            </Col>\r\n                        </Row>\r\n                        <Row className=\"mt-1\">\r\n                            <Col>\r\n                                <Card.Text>\r\n                                    <b>{t(\"AddOrganisation(s)\")}</b>\r\n                                </Card.Text>\r\n                                <hr />\r\n                            </Col>\r\n                        </Row>\r\n                        <Row className=\"mb-3\">\r\n                            <Col>\r\n                                <Form.Group>\r\n                                    <Tabs\r\n                                        activeKey={defaultActiveKey}\r\n                                        onSelect={(k: any) => setdefaultActiveKey(k)}\r\n                                        id=\"uncontrolled-tab-example\"\r\n                                    >\r\n                                        {\" \"}\r\n                                        {partners.map((item: any, i: number) => {\r\n                                            return (\r\n                                                <Tab key={i} eventKey={`${i + 1}`} title={`Organisation ${i + 1}`}>\r\n                                                    <Row className=\"mb-3\">\r\n                                                        <Col md={4}>\r\n                                                            <Form.Group style={{ paddingTop: \"20px\" }}>\r\n                                                                <Form.Label className=\"required-field\">\r\n                                                                    {t(\"NameofAssociatedOrganisation\")}\r\n                                                                </Form.Label>\r\n                                                                <SelectGroup\r\n                                                                    name=\"institution\"\r\n                                                                    id=\"institution\"\r\n                                                                    value={item.institution}\r\n                                                                    onChange={(e: any) => handleTab(e, i)}\r\n                                                                    required\r\n                                                                    errorMessage={t(\"thisfieldisrequired\")}\r\n                                                                >\r\n                                                                    <option value=\"\">{t(\"SelectOrganisation\")}</option>\r\n                                                                    {organizationList.map((item_o, _i) => {\r\n                                                                        return (\r\n                                                                            <option key={_i} value={item_o._id}>\r\n                                                                                {item_o.title}\r\n                                                                            </option>\r\n                                                                        );\r\n                                                                    })}\r\n                                                                </SelectGroup>\r\n                                                            </Form.Group>\r\n                                                        </Col>\r\n                                                        <Col md={4}>\r\n                                                            <Form.Group\r\n                                                                style={{\r\n                                                                    paddingTop: \"20px\",\r\n                                                                    maxWidth: \"400px\",\r\n                                                                }}\r\n                                                            >\r\n                                                                <Form.Label>{t(\"WorkingExpertise\")}</Form.Label>\r\n                                                                <MultiSelect\r\n                                                                    overrideStrings={{\r\n                                                                        selectSomeItems: t(\"ChooseExpertise\"),\r\n                                                                        allItemsAreSelected:\r\n                                                                            \"All Expertise are Selected\",\r\n                                                                    }}\r\n                                                                    onChange={(e: any) => handleTab(e, i)}\r\n                                                                    options={expertises}\r\n                                                                    value={item.partner_expertise}\r\n                                                                    className={\"work-expert\"}\r\n                                                                    labelledBy={\"Select Expertise\"}\r\n                                                                />\r\n                                                            </Form.Group>\r\n                                                        </Col>\r\n                                                        <Col md={4}>\r\n                                                            <Form.Group style={{ paddingTop: \"20px\" }}>\r\n                                                                <Form.Label className=\"required-field\">\r\n                                                                    {t(\"Status\")}\r\n                                                                </Form.Label>\r\n                                                                <SelectGroup\r\n                                                                    name=\"organisation_status\"\r\n                                                                    id=\"organisation_status\"\r\n                                                                    value={item.status}\r\n                                                                    onChange={(e: any) => handleTab(e, i)}\r\n                                                                    required\r\n                                                                    errorMessage={t(\"thisfieldisrequired\")}\r\n                                                                >\r\n                                                                    <option value=\"\">{t(\"SelectStatus\")}</option>\r\n                                                                    {deploymentStatus.map((item_d, _i) => (\r\n                                                                        <option key={_i} value={item_d._id}>\r\n                                                                            {item_d.title}\r\n                                                                        </option>\r\n                                                                    ))}\r\n                                                                </SelectGroup>\r\n                                                            </Form.Group>\r\n                                                        </Col>\r\n                                                    </Row>\r\n                                                    <Row>\r\n                                                        <Col>\r\n                                                            <Form.Group>\r\n                                                                <Form.Label>{t(\"Description\")}</Form.Label>\r\n                                                                <EditorComponent initContent={item.work_description} onChange={(evt: React.ChangeEvent) => handleDesTab(evt, i)} />\r\n                                                            </Form.Group>\r\n                                                        </Col>\r\n                                                    </Row>\r\n                                                    <div>\r\n                                                        {i === 0 ? (\r\n                                                            <span></span>\r\n                                                        ) : (\r\n                                                            <Col xs lg=\"4\">\r\n                                                                <Button\r\n                                                                    onSelect={(k: any) => setdefaultActiveKey(k)}\r\n                                                                    variant=\"secondary\"\r\n                                                                    onClick={(e: any) => removeTab(e, i)}\r\n                                                                >\r\n                                                                    {t(\"Remove\")}\r\n                                                                </Button>\r\n                                                            </Col>\r\n                                                        )}\r\n                                                    </div>\r\n                                                </Tab>\r\n                                            );\r\n                                        })}\r\n                                        <Tab\r\n                                            title={\r\n                                                <div>\r\n                                                    <span onClick={tabAdd}>\r\n                                                        <FontAwesomeIcon icon={faPlus} color=\"#808080\" />\r\n                                                    </span>\r\n                                                </div>\r\n                                            }\r\n                                        ></Tab>\r\n                                    </Tabs>\r\n                                </Form.Group>\r\n                            </Col>\r\n                        </Row>\r\n                        <Col className=\"px-0\">\r\n                            <Card.Text>\r\n                                <b>{t(\"MediaGallery\")}</b>\r\n                            </Card.Text>\r\n                            <hr />\r\n                        </Col>\r\n                        <Row>\r\n                            <Col>\r\n                                <ReactDropZone\r\n                                    datas={dropZoneCollection}\r\n                                    srcText={srcCollection}\r\n                                    getImgID={(id: any) => getID(id)}\r\n                                    getImageSource={(imgSrcArr: any) => getSource(imgSrcArr)}\r\n                                />\r\n                            </Col>\r\n                        </Row>\r\n                        <Col className=\"px-0 mt-4\">\r\n                            <Card.Text>\r\n                                <b>{t(\"Documents\")}</b>\r\n                            </Card.Text>\r\n                            <hr />\r\n                        </Col>\r\n                        <Row>\r\n                            <Col>\r\n                                <ReactDropZone\r\n                                    type=\"application\"\r\n                                    datas={docCollection}\r\n                                    srcText={docSrcCollection}\r\n                                    getImgID={(id: any) => getID(id)}\r\n                                    getImageSource={(docSrcArr: any) => getDocSource(docSrcArr)}\r\n                                />\r\n                            </Col>\r\n                        </Row>\r\n                        <Row className=\"mt-4\">\r\n                            <Col>\r\n                                <Card.Text>\r\n                                    <b>{t(\"VirtualSpace\")}</b>\r\n                                </Card.Text>\r\n                                <hr />\r\n                                <Form.Check\r\n                                    className=\"p-0\"\r\n                                    type=\"checkbox\"\r\n                                    disabled={!virtualSpaceAccessPermission}\r\n                                    onChange={() => setVirtualSpace(!virtualSpace)}\r\n                                    name=\"virtula\"\r\n                                    checked={virtualSpace}\r\n                                    label={t(\"WouldliketocreateaVirtualSpace\")}\r\n                                />\r\n                            </Col>\r\n                        </Row>\r\n                        <Row className=\"my-4\">\r\n                            <Col>\r\n                                <Button className=\"me-2\" type=\"submit\" variant=\"primary\" ref={buttonRef} onClick={handleSubmit}>\r\n                                    {t(\"submit\")}\r\n                                </Button>\r\n                                <Button className=\"me-2\" onClick={resetHandler} variant=\"info\">\r\n                                    {t(\"reset\")}\r\n                                </Button>\r\n                                <Link href=\"/operation\" as=\"/operation\" >\r\n                                    <Button variant=\"secondary\">{t(\"Cancel\")}</Button>\r\n                                </Link>\r\n                            </Col>\r\n                        </Row>\r\n                    </Card.Body>\r\n                </ValidationFormWrapper>\r\n            </Card>\r\n            {modal && <VspaceModal type=\"Operation\" id={resId} />}\r\n        </Container>\r\n    );\r\n};\r\n\r\nexport default OperationForm;\r\n", "import React from 'react';\r\nimport { useFormikContext, Field } from 'formik';\r\nimport { Form } from 'react-bootstrap';\r\n\r\ninterface RadioItemProps {\r\n  id: string;\r\n  label: string;\r\n  value: string;\r\n  name?: string;\r\n  disabled?: boolean;\r\n}\r\n\r\ninterface RadioGroupProps {\r\n  name: string;\r\n  valueSelected: string;\r\n  onChange: (e: React.ChangeEvent<HTMLInputElement>) => void;\r\n  errorMessage?: string;\r\n  children: React.ReactNode;\r\n}\r\n\r\n// Type Guard to ensure child.props is an object\r\nfunction isObject(props: any): props is { [key: string]: any } {\r\n  return typeof props === 'object' && props !== null;\r\n}\r\n\r\nconst RadioItem: React.FC<RadioItemProps> = ({ id, label, value, name, disabled }) => {\r\n  const { values, setFieldValue } = useFormikContext<any>();\r\n  const fieldName = name || id;\r\n\r\n  return (\r\n    <Form.Check\r\n      type=\"radio\"\r\n      id={id}\r\n      label={label}\r\n      value={value}\r\n      name={fieldName}\r\n      checked={values[fieldName] === value}\r\n      onChange={(e) => {\r\n        setFieldValue(fieldName, e.target.value);\r\n      }}\r\n      disabled={disabled}\r\n      inline\r\n    />\r\n  );\r\n};\r\n\r\nconst RadioGroup: React.FC<RadioGroupProps> = ({\r\n  name,\r\n  valueSelected,\r\n  onChange,\r\n  errorMessage,\r\n  children,\r\n}) => {\r\n  const { errors, touched } = useFormikContext<any>();\r\n  const hasError = touched[name] && errors[name];\r\n\r\n  // Create a context to pass the name to RadioItems\r\n  const radioContext = React.useMemo(() => ({ name }), [name]);\r\n\r\n  // Clone children to pass the name, ensuring props are spread safely\r\n  const childrenWithProps = React.Children.map(children, (child) => {\r\n    if (React.isValidElement(child)) {\r\n      // Ensure child.props is an object before spreading\r\n      if (isObject(child.props)) {\r\n        return React.cloneElement(child as React.ReactElement<RadioItemProps>, {\r\n          name,\r\n          ...child.props, // Safely spread child.props\r\n        });\r\n      }\r\n    }\r\n    return child;\r\n  });\r\n\r\n  return (\r\n    <div>\r\n      <div className=\"radio-group\">\r\n        {childrenWithProps}\r\n      </div>\r\n      {hasError && (\r\n        <div className=\"invalid-feedback d-block\">\r\n          {errorMessage || (typeof errors[name] === 'string' ? errors[name] : String(errors[name]))}\r\n        </div>\r\n      )}\r\n    </div>\r\n  );\r\n};\r\n\r\nexport const Radio = {\r\n  RadioGroup,\r\n  RadioItem,\r\n};\r\n\r\nexport default Radio;\r\n", "// This file exports all the components needed to replace react-bootstrap4-form-validation\r\n// It serves as a drop-in replacement for the original library\r\n\r\nimport ValidationForm from './ValidationFormWrapper';\r\nimport { TextInput, SelectGroup } from './FormikTextInput';\r\nimport { Radio } from './FormikRadio';\r\n\r\n// Export all components\r\nexport {\r\n  ValidationForm,\r\n  TextInput,\r\n  SelectGroup,\r\n  Radio\r\n};\r\n\r\n// Also export as default for convenience\r\nexport default {\r\n  ValidationForm,\r\n  TextInput,\r\n  SelectGroup,\r\n  Radio\r\n};\r\n", "//Import Library\r\nimport React, { useState, useEffect } from \"react\";\r\nimport { Modal } from \"react-bootstrap\";\r\nimport { FontAwesomeIcon } from \"@fortawesome/react-fontawesome\";\r\nimport { faCheck } from \"@fortawesome/free-solid-svg-icons\";\r\nimport Router from \"next/router\";\r\n\r\n//Import services/components\r\nimport { useTranslation } from 'next-i18next';\r\n\r\nconst VSpaceModal = ({ type, id }: any) => {\r\n  const [time, setTime] = useState(5);\r\n  const { t } = useTranslation('common');\r\n\r\n  useEffect(() => {\r\n    if (time > 0) {\r\n      setTimeout(() => setTime(time - 1), 1000);\r\n    } else {\r\n      Router.push({\r\n        pathname: \"/vspace/create\",\r\n        query: { id: id, source: type },\r\n      });\r\n    }\r\n  });\r\n  return (\r\n    <Modal show={true}>\r\n      <div className=\"modal--align mt-2\">\r\n        <div className=\"modal--icon\">\r\n          <FontAwesomeIcon icon={faCheck} color=\"#4dc724\" size=\"4x\" />\r\n        </div>\r\n      </div>\r\n      <div className=\"text-center mt-4\">\r\n        <p className=\"lead\">\r\n          {type} {t(\"vspace.formSubmittedSuccessfully\")}\r\n          <br />\r\n          <small>{t(\"vspace.vspaceCreationRedirect\")}</small>\r\n          <br />\r\n          <small>\r\n            <b>\r\n              {\" \"}\r\n              {t(\"vspace.waitFor\")} {time} {t(\"vspace.waitForSec\")}\r\n            </b>\r\n          </small>\r\n        </p>\r\n      </div>\r\n    </Modal>\r\n  );\r\n};\r\n\r\nexport default VSpaceModal;\r\n", "import React, { forwardRef } from 'react';\r\nimport { Formik, Form, FormikProps, FormikHelpers } from 'formik';\r\nimport * as Yup from 'yup';\r\n\r\n// This is a wrapper component that replaces react-bootstrap4-form-validation with Formik\r\ninterface MockEvent {\r\n  preventDefault: () => void;\r\n  stopPropagation: () => void;\r\n  currentTarget: (EventTarget & Element) | null;\r\n  target: EventTarget | null;\r\n  nativeEvent: Event;\r\n  bubbles: boolean;\r\n  cancelable: boolean;\r\n  defaultPrevented: boolean;\r\n  eventPhase: number;\r\n  isTrusted: boolean;\r\n  timeStamp: number;\r\n  type: string;\r\n  isDefaultPrevented: () => boolean;\r\n  isPropagationStopped: () => boolean;\r\n  persist: () => void;\r\n}\r\n\r\ninterface ValidationFormWrapperProps {\r\n  children: React.ReactNode | ((formikProps: FormikProps<any>) => React.ReactNode);\r\n  onSubmit: (event: React.FormEvent | MockEvent, values?: Record<string, any>, actions?: FormikHelpers<Record<string, any>>) => void;\r\n  onErrorSubmit?: (errors: any) => void;\r\n  initialValues?: Record<string, any>;\r\n  enableReinitialize?: boolean;\r\n  autoComplete?: string;\r\n  className?: string;\r\n  onKeyPress?: (e: any) => void;\r\n}\r\n\r\nconst ValidationFormWrapper = forwardRef<HTMLFormElement, ValidationFormWrapperProps>((props, ref) => {\r\n  const { children, onSubmit, autoComplete, className, onKeyPress, initialValues, ...rest } = props;\r\n\r\n  // Create an empty validation schema by default\r\n  const validationSchema = Yup.object().shape({});\r\n\r\n  return (\r\n    <Formik\r\n      initialValues={initialValues || {}}\r\n      validationSchema={validationSchema}\r\n      onSubmit={(values: Record<string, any>, actions: FormikHelpers<Record<string, any>>) => {\r\n        // Create a mock event object with preventDefault method and currentTarget\r\n        const mockEvent: MockEvent = {\r\n          preventDefault: () => {},\r\n          stopPropagation: () => {},\r\n          currentTarget: null, // Set to null to avoid checkValidity errors\r\n          target: null,\r\n          nativeEvent: new Event('submit'),\r\n          bubbles: false,\r\n          cancelable: true,\r\n          defaultPrevented: false,\r\n          eventPhase: 0,\r\n          isTrusted: false,\r\n          timeStamp: Date.now(),\r\n          type: 'submit',\r\n          isDefaultPrevented: () => false,\r\n          isPropagationStopped: () => false,\r\n          persist: () => {}\r\n        };\r\n\r\n        if (onSubmit) {\r\n          // Pass the mock event object to maintain compatibility with the original code\r\n          onSubmit(mockEvent, values, actions);\r\n        }\r\n      }}\r\n      {...rest}\r\n    >\r\n      {(formikProps: FormikProps<any>) => (\r\n        <Form\r\n          ref={ref}\r\n          onSubmit={formikProps.handleSubmit}\r\n          autoComplete={autoComplete}\r\n          className={className}\r\n          onKeyPress={onKeyPress}\r\n        >\r\n          {typeof children === 'function' ? children(formikProps) : children}\r\n        </Form>\r\n      )}\r\n    </Formik>\r\n  );\r\n});\r\n\r\nValidationFormWrapper.displayName = 'ValidationFormWrapper';\r\n\r\nexport default ValidationFormWrapper;\r\n", "import React from 'react';\r\nimport { Form } from 'react-bootstrap';\r\nimport { Field, ErrorMessage, useField } from 'formik';\r\n\r\n// This component mimics the TextInput component from react-bootstrap4-form-validation\r\n// but uses Formik under the hood\r\nexport const TextInput = ({\r\n  name,\r\n  id,\r\n  required,\r\n  validator,\r\n  errorMessage,\r\n  onChange,\r\n  value,\r\n  as,\r\n  multiline,\r\n  rows,\r\n  pattern,\r\n  ...props\r\n}: any) => {\r\n  // Create a custom validation function that mimics the original validator\r\n  const validate = (val: any) => {\r\n    // Safely convert value to string and trim\r\n    const stringVal = typeof val === 'string' ? val : String(val || '');\r\n    if (required && (!val || stringVal.trim() === '')) {\r\n      return errorMessage?.validator || 'This field is required';\r\n    }\r\n\r\n    if (validator && !validator(val)) {\r\n      return errorMessage?.validator || 'Invalid value';\r\n    }\r\n\r\n    if (pattern && val) {\r\n      const regex = new RegExp(pattern);\r\n      if (!regex.test(val)) {\r\n        return errorMessage?.pattern || 'Invalid format';\r\n      }\r\n    }\r\n\r\n    return undefined;\r\n  };\r\n\r\n  return (\r\n    <Field name={name} validate={validate}>\r\n      {({ field, meta }: any) => (\r\n        <>\r\n          <Form.Control\r\n            {...field}\r\n            {...props}\r\n            id={id}\r\n            as={as || 'input'}\r\n            rows={rows}\r\n            isInvalid={meta.touched && !!meta.error}\r\n            onChange={(e: React.ChangeEvent<HTMLInputElement>) => {\r\n              field.onChange(e);\r\n              if (onChange) onChange(e);\r\n            }}\r\n            value={value !== undefined ? value : field.value}\r\n          />\r\n          {meta.touched && meta.error ? (\r\n            <Form.Control.Feedback type=\"invalid\">\r\n              {meta.error}\r\n            </Form.Control.Feedback>\r\n          ) : null}\r\n        </>\r\n      )}\r\n    </Field>\r\n  );\r\n};\r\n\r\n// This component mimics the SelectGroup component from react-bootstrap4-form-validation\r\n// but uses Formik under the hood\r\nexport const SelectGroup = ({\r\n  name,\r\n  id,\r\n  required,\r\n  errorMessage,\r\n  onChange,\r\n  value,\r\n  children,\r\n  ...props\r\n}: any) => {\r\n  // Create a custom validation function that mimics the original validator\r\n  const validate = (val: any) => {\r\n    if (required && (!val || val === '')) {\r\n      return errorMessage?.validator || 'This field is required';\r\n    }\r\n\r\n    return undefined;\r\n  };\r\n\r\n  return (\r\n    <Field name={name} validate={validate}>\r\n      {({ field, meta }: any) => (\r\n        <>\r\n          <Form.Control\r\n            as=\"select\"\r\n            {...field}\r\n            {...props}\r\n            id={id}\r\n            isInvalid={meta.touched && !!meta.error}\r\n            onChange={(e: React.ChangeEvent<HTMLSelectElement>) => {\r\n              field.onChange(e);\r\n              if (onChange) onChange(e);\r\n            }}\r\n            value={value !== undefined ? value : field.value}\r\n          >\r\n            {children}\r\n          </Form.Control>\r\n          {meta.touched && meta.error ? (\r\n            <Form.Control.Feedback type=\"invalid\">\r\n              {meta.error}\r\n            </Form.Control.Feedback>\r\n          ) : null}\r\n        </>\r\n      )}\r\n    </Field>\r\n  );\r\n};\r\n\r\n// Export both components as named exports and as a default object\r\nexport default {\r\n  TextInput,\r\n  SelectGroup\r\n};\r\n", "//Import Library\r\nimport React, { useMemo, useEffect, useState, useCallback } from \"react\";\r\nimport { useDropzone } from \"react-dropzone\";\r\nimport { FontAwesomeIcon } from \"@fortawesome/react-fontawesome\";\r\nimport {\r\n  faExclamationCircle,\r\n  faCloudUploadAlt,\r\n} from \"@fortawesome/free-solid-svg-icons\";\r\nimport { Form, Button, Modal, Col } from \"react-bootstrap\";\r\nimport _ from \"lodash\";\r\n\r\nimport toast from 'react-hot-toast';\r\n\r\n//Import services/components\r\nimport apiService from \"../../services/apiService\";\r\nimport { useTranslation } from 'next-i18next';\r\n\r\n// Define type for temp array items\r\ninterface TempItem {\r\n  serverID: string;\r\n  file?: any;\r\n  index: number;\r\n  type: string;\r\n}\r\n\r\nlet temp: TempItem[] = [];\r\n\r\nconst baseStyle: any = {\r\n  flex: 1,\r\n  display: \"flex\",\r\n  flexDirection: \"column\",\r\n  alignItems: \"center\",\r\n  justifyContent: \"center\",\r\n  width: \"100%\",\r\n  height: \"100%\",\r\n  borderWidth: 0.1,\r\n  borderColor: \"#fafafa\",\r\n  backgroundColor: \"#fafafa\",\r\n  color: \"black\",\r\n  transition: \"border  .24s ease-in-out\",\r\n  padding: \"15px\",\r\n};\r\n\r\nconst thumb: any = {\r\n  display: \"flex\",\r\n  borderRadius: 2,\r\n  border: \"1px solid #ddd\",\r\n  margin: 8,\r\n  height: 175,\r\n  boxShadow: \"0 0 15px 0.25px rgba(0,0,0,0.15)\",\r\n  boxSizing: \"border-box\",\r\n};\r\n\r\nconst thumbsContainer: any = {\r\n  display: \"flex\",\r\n  padding: \"10px\",\r\n  width: \"100%\",\r\n  border: \"2px solid gray\",\r\n  flexDirection: \"column\",\r\n  justifyContent: \"flex-start\",\r\n  flexWrap: \"wrap\",\r\n  marginTop: 20,\r\n};\r\n\r\nconst thumbInner: any = {\r\n  display: \"flex\",\r\n  minWidth: 0,\r\n};\r\n\r\nconst deleteIcon: any = {\r\n  display: \"flex\",\r\n  justifyContent: \"center\",\r\n  alignItems: \"center\",\r\n  marginLeft: 30,\r\n};\r\n\r\nconst img = {\r\n  width: \"150px\",\r\n};\r\n\r\nconst activeStyle: any = {\r\n  borderColor: \"#2196f3\",\r\n};\r\n\r\nconst ReactDropZone = (props: any) => {\r\n  const { t } = useTranslation('common');\r\n  const [modalShow, setModalShow] = useState(false);\r\n  const [deleteFile, setDeleteFile] = useState();\r\n  const limit: any =\r\n    props.type == \"application\" ? 20971520 : process.env.UPLOAD_LIMIT;\r\n  const [files, setFiles] = useState<any[]>([]);\r\n  const [multi, setMulti] = useState(true);\r\n  const [imageSource, setImageSource] = useState<string[]>([]);\r\n\r\n  const endpoint = props && props.type === \"application\" ? \"/files\" : \"/image\";\r\n  const imageDelete = async (id: string) => {\r\n    const _res = await apiService.remove(`${endpoint}/${id}`);\r\n  };\r\n\r\n  const removeFile = (file: any) => {\r\n    setDeleteFile(file);\r\n    setModalShow(true);\r\n  };\r\n\r\n  const handleSource = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>, index: number) => {\r\n    const items = [...imageSource];\r\n    items[index] = e.target.value;\r\n    setImageSource(items);\r\n  };\r\n\r\n  const getComponent = (file: any) => {\r\n    const fileType = file && file.name.split(\".\").pop();\r\n    switch (fileType) {\r\n      case \"JPG\":\r\n      case \"jpg\":\r\n      case \"jpeg\":\r\n      case \"jpg\":\r\n      case \"png\":\r\n        return <img src={file.preview} style={img} />;\r\n      case \"pdf\":\r\n        return (\r\n          <img\r\n            src=\"/images/fileIcons/pdfFile.png\"\r\n            className={\r\n              props.type === \"application\" ? \"docPreview\" : \"imgPreview\"\r\n            }\r\n          />\r\n        );\r\n      case \"docx\":\r\n        return (\r\n          <img\r\n            src=\"/images/fileIcons/wordFile.png\"\r\n            className={\r\n              props.type === \"application\" ? \"docPreview\" : \"imgPreview\"\r\n            }\r\n          />\r\n        );\r\n      case \"xls\":\r\n      case \"xlsx\":\r\n        return (\r\n          <img\r\n            src=\"/images/fileIcons/xlsFile.png\"\r\n            className={\r\n              props.type === \"application\" ? \"docPreview\" : \"imgPreview\"\r\n            }\r\n          />\r\n        );\r\n      default:\r\n        return (\r\n          <img\r\n            src=\"/images/fileIcons/wordFile.png\"\r\n            className={\r\n              props.type === \"application\" ? \"docPreview\" : \"imgPreview\"\r\n            }\r\n          />\r\n        );\r\n    }\r\n  };\r\n\r\n  const modalHide = () => setModalShow(false);\r\n\r\n  const cancelHandler = () => {\r\n    setModalShow(false);\r\n  };\r\n\r\n  const submitHandler = (fileselector: any) => {\r\n    fileselector = deleteFile;\r\n    const obj =\r\n      fileselector && fileselector._id\r\n        ? { serverID: fileselector._id }\r\n        : { file: fileselector };\r\n    const _index = _.findIndex(temp, obj);\r\n    //**Delete the source Field**//\r\n    const removeSrc = [...imageSource];\r\n    removeSrc.splice(_index, 1);\r\n    setImageSource(removeSrc);\r\n    //**End**/\r\n    imageDelete(temp[_index].serverID);\r\n    temp.splice(_index, 1);\r\n    props.getImgID(temp, props.index ? props.index : 0);\r\n    const newFiles = [...files];\r\n    newFiles.splice(newFiles.indexOf(fileselector), 1);\r\n    setFiles(newFiles);\r\n    setModalShow(false);\r\n  };\r\n\r\n  const thumbs: any = files.map((file: any, i) => {\r\n    return (\r\n      <div key={i}>\r\n        <Col xs={12}>\r\n          <div className=\"row\">\r\n            <Col\r\n              md={4}\r\n              lg={3}\r\n              className={\r\n                props.type === \"application text-center align-self-center\"\r\n                  ? \"docImagePreview text-center align-self-center\"\r\n                  : \"imgPreview text-center align-self-center\"\r\n              }\r\n            >\r\n              {getComponent(file)}\r\n            </Col>\r\n            <Col md={5} lg={7} className=\"align-self-center\">\r\n              <Form>\r\n                <Form.Group controlId=\"filename\">\r\n                  <Form.Label className=\"mt-2\">{t(\"FileName\")}</Form.Label>\r\n                  <Form.Control\r\n                    size=\"sm\"\r\n                    type=\"text\"\r\n                    disabled\r\n                    value={file.original_name ? file.original_name : file.name}\r\n                  />\r\n                </Form.Group>\r\n                <Form.Group controlId=\"description\">\r\n                  <Form.Label>\r\n                    {props.type === \"application\"\r\n                      ? t(\"ShortDescription/(Max255Characters)\")\r\n                      : t(\"Source/Description\")}\r\n                  </Form.Label>\r\n                  <Form.Control\r\n                    maxLength={props.type === \"application\" ? 255 : undefined}\r\n                    size=\"sm\"\r\n                    type=\"text\"\r\n                    placeholder={\r\n                      props.type === \"application\"\r\n                        ? t(\"`Enteryourdocumentdescription`\")\r\n                        : t(\"`Enteryourimagesource/description`\")\r\n                    }\r\n                    value={imageSource[i]}\r\n                    onChange={(e) => handleSource(e, i)}\r\n                  />\r\n                </Form.Group>\r\n              </Form>\r\n            </Col>\r\n            <Col\r\n              md={3}\r\n              lg={2}\r\n              className=\"align-self-center text-center\"\r\n              onClick={() => removeFile(file)}\r\n            >\r\n              <Button variant=\"dark\">{t(\"Remove\")}</Button>\r\n            </Col>\r\n          </div>\r\n        </Col>\r\n        <Modal show={modalShow} onHide={modalHide}>\r\n          <Modal.Header closeButton>\r\n            <Modal.Title>{t(\"DeleteFile\")}</Modal.Title>\r\n          </Modal.Header>\r\n          <Modal.Body>{t(\"Areyousurewanttodeletethisfile?\")}</Modal.Body>\r\n          <Modal.Footer>\r\n            <Button variant=\"secondary\" onClick={cancelHandler}>\r\n              {t(\"Cancel\")}\r\n            </Button>\r\n            <Button variant=\"primary\" onClick={() => submitHandler(file)}>\r\n              {t(\"yes\")}\r\n            </Button>\r\n          </Modal.Footer>\r\n        </Modal>\r\n      </div>\r\n    );\r\n  });\r\n\r\n  useEffect(() => {\r\n    files.forEach((file) => URL.revokeObjectURL(file.preview));\r\n    temp = [];\r\n  }, []);\r\n\r\n  useEffect(() => {\r\n    props.getImageSource(imageSource);\r\n  }, [imageSource]);\r\n\r\n  useEffect(() => {\r\n    setImageSource(props.srcText);\r\n  }, [props.srcText]);\r\n\r\n  useEffect(() => {\r\n    props && props.singleUpload === \"true\" && setMulti(false);\r\n    if (props && props.datas) {\r\n      const newObj = props.datas.map((item: any, _i: number) => {\r\n        temp.push({\r\n          serverID: item._id,\r\n          index: props.index ? props.index : 0,\r\n          type: item.name.split(\".\")[1],\r\n        });\r\n        const previewState = {\r\n          ...item,\r\n          preview: `${process.env.API_SERVER}/image/show/${item._id}`,\r\n        };\r\n        return previewState;\r\n      });\r\n      setFiles([...newObj]);\r\n    }\r\n  }, [props.datas]);\r\n\r\n  const filesUpload = async (filesinitial: any[], index: number) => {\r\n    if (filesinitial.length > index) {\r\n      try {\r\n        const form: any = new FormData();\r\n        form.append(\"file\", filesinitial[index]);\r\n        const res = await apiService.post(endpoint, form, {\r\n          \"Content-Type\": \"multipart/form-data\",\r\n        });\r\n        temp.push({\r\n          serverID: res._id,\r\n          file: filesinitial[index],\r\n          index: props.index ? props.index : 0,\r\n          type: filesinitial[index].name.split(\".\")[1],\r\n        });\r\n        filesUpload(filesinitial, index + 1);\r\n      } catch (error) {\r\n        filesUpload(filesinitial, index + 1);\r\n      }\r\n    } else {\r\n      props.getImgID(temp, props.index ? props.index : 0);\r\n    }\r\n  };\r\n\r\n  const onDrop = useCallback(async (ondrop_files: any[]) => {\r\n    await filesUpload(ondrop_files, 0);\r\n    const accFiles = ondrop_files.map((file: any) =>\r\n      Object.assign(file, {\r\n        preview: URL.createObjectURL(file),\r\n      })\r\n    );\r\n    multi\r\n      ? setFiles((prevState) => [...prevState, ...accFiles])\r\n      : setFiles([...accFiles]);\r\n  }, []);\r\n\r\n  function nameLengthValidator(file: File) {\r\n    if (endpoint === \"/image\") {\r\n      if (file.type.substring(0, 5) === \"image\") {\r\n        return null;\r\n      } else {\r\n        toast.error(t(\"toast.filetypenotsupport\"));\r\n        return { code: \"file-invalid-type\", message: \"File type not supported\" };\r\n      }\r\n    } else if (endpoint === \"/files\") {\r\n      if (!(file.type.substring(0, 5) !== \"image\")) {\r\n        toast.error(t(\"toast.filetypenotsupport\"));\r\n        return { code: \"file-invalid-type\", message: \"File type not supported\" };\r\n      }\r\n    }\r\n    return null;\r\n  }\r\n  const {\r\n    getRootProps,\r\n    getInputProps,\r\n    isDragActive,\r\n    isDragAccept,\r\n    isDragReject,\r\n    fileRejections,\r\n  } = useDropzone({\r\n    accept:\r\n      props && props.type\r\n        ? \"application/pdf, application/vnd.openxmlformats-officedocument.wordprocessingml.document, application/vnd.oasis.opendocument.text,application/vnd.openxmlformats-officedocument.spreadsheetml.sheet,text/csv\"\r\n        : \"image/*\",\r\n    multiple: multi,\r\n    minSize: 0,\r\n    maxSize: limit,\r\n    onDrop,\r\n    validator: nameLengthValidator,\r\n  });\r\n\r\n  const style = useMemo(\r\n    () => ({\r\n      ...baseStyle,\r\n      ...(isDragActive ? activeStyle : { outline: \"2px dashed #bbb\" }),\r\n      ...(isDragAccept\r\n        ? { outline: \"2px dashed #595959\" }\r\n        : { outline: \"2px dashed #bbb\" }),\r\n      ...(isDragReject ? { outline: \"2px dashed red\" } : { activeStyle }),\r\n    }),\r\n    [isDragActive, isDragReject]\r\n  );\r\n\r\n  let dropZoneMsg;\r\n  if (props && props.type === \"application\") {\r\n    dropZoneMsg = (\r\n      <small style={{ color: \"#595959\" }}>{t(\"DocumentWeSupport\")}</small>\r\n    );\r\n  } else {\r\n    dropZoneMsg = (\r\n      <small style={{ color: \"#595959\" }}>{t(\"ImageWeSupport\")}</small>\r\n    );\r\n  }\r\n\r\n  const isFileTooLarge =\r\n    fileRejections.length > 0 && fileRejections[0].file.size > limit;\r\n  return (\r\n    <>\r\n      <div\r\n        className=\" d-flex justify-content-center align-items-center mt-3\"\r\n        style={{ width: \"100%\", height: \"180px\" }}\r\n      >\r\n        <div {...getRootProps({ style })}>\r\n          <input {...getInputProps()} />\r\n          <FontAwesomeIcon icon={faCloudUploadAlt} size=\"4x\" color=\"#999\" />\r\n          <p style={{ color: \"#595959\", marginBottom: \"0px\" }}>\r\n            {t(\"Drag'n'dropsomefileshere,orclicktoselectfiles\")}\r\n          </p>\r\n\r\n          {!multi && (\r\n            <small style={{ color: \"#595959\" }}>\r\n              <b>Note:</b> One single image will be accepted\r\n            </small>\r\n          )}\r\n          {dropZoneMsg}\r\n          {props.type === \"application\"\r\n            ? isFileTooLarge && (\r\n                <small className=\"text-danger mt-2\">\r\n                  <FontAwesomeIcon\r\n                    icon={faExclamationCircle}\r\n                    size=\"1x\"\r\n                    color=\"red\"\r\n                  />{\" \"}\r\n                  {t(\"FileistoolargeItshouldbelessthan20MB\")}\r\n                </small>\r\n              )\r\n            : isFileTooLarge && (\r\n                <small className=\"text-danger mt-2\">\r\n                  <FontAwesomeIcon\r\n                    icon={faExclamationCircle}\r\n                    size=\"1x\"\r\n                    color=\"red\"\r\n                  />{\" \"}\r\n                  {t(\"FileistoolargeItshouldbelessthan20MB\")}\r\n                </small>\r\n              )}\r\n          {isDragReject && (\r\n            <small className=\"text-danger\" style={{ color: \"#595959\" }}>\r\n              <FontAwesomeIcon\r\n                icon={faExclamationCircle}\r\n                size=\"1x\"\r\n                color=\"red\"\r\n              />{\" \"}\r\n              {t(\"Filetypenotacceptedsorr\")}\r\n            </small>\r\n          )}\r\n        </div>\r\n      </div>\r\n      {files.length > 0 && <div style={thumbsContainer}>{thumbs}</div>}\r\n    </>\r\n  );\r\n};\r\n\r\nexport default ReactDropZone;\r\n"], "names": ["props", "DatePicker", "RKIDatePicker", "t", "i18n", "useTranslation", "OperationForm", "currentLang", "language", "countrySearch", "title_de", "title", "formRef", "useRef", "buttonRef", "groupVisibility", "setGroupVisibility", "useState", "invitesCountry", "invitesRegion", "invitesOrganisationType", "invitesOrganisation", "invitesExpertise", "invitesNetWork", "visibility", "currLang", "dropZoneCollection", "setDropZoneCollection", "srcCollection", "setSrcCollection", "docSrcCollection", "setDocSrcCollection", "docCollection", "setDocCollection", "statusOperation", "setOperationStatus", "countryList", "setcountryList", "setUsersList", "defaultActiveKey", "setdefaultActiveKey", "partners", "setPartners", "institution", "expertise", "partner_expertise", "organisation_status", "work_description", "status", "hazardTypes", "sethazardTypes", "regions", "setRegion", "hazards", "<PERSON><PERSON><PERSON><PERSON>", "deploymentStatus", "setDeploymentStatus", "syndrome", "setSyndrome", "expertises", "setExpertises", "organizationList", "setOrganizationList", "virtualSpace", "setVirtualSpace", "localeLang", "modal", "setModal", "resId", "setResId", "form", "setForm", "timetitle", "iconclass", "date", "virtualSpaceAccessPermission", "setVirtualSpaceAccessPermission", "editform", "routes", "_initialState", "country", "world_region", "region", "hazard_type", "hazard", "active_tab", "start_date", "TimeLineStartDate", "Date", "end_date", "description", "images", "document", "checked", "images_src", "doc_src", "operationParams", "query", "sort", "limit", "languageCode", "initialVal", "setinitialVal", "tabAdd", "_temp", "push", "length", "removeTab", "_e", "i", "splice", "handleTab", "e", "target", "name", "value", "map", "item", "getOperationInitialData", "operationParamsinit", "operationStatus", "apiService", "get", "Array", "isArray", "data", "hazardtype", "syndromes", "institutionParams", "select", "$ne", "institutions", "_expertise", "_i", "label", "_id", "deployment", "getdoccollection", "response", "normalize<PERSON><PERSON>ner", "getRegion", "<PERSON><PERSON><PERSON><PERSON>", "prevState", "getnormalization", "item5", "initRegions", "_regionsId", "getresonse", "timeline", "moment", "toDate", "item1", "item2", "getOperationsData", "for<PERSON>ach", "item3", "item4", "getUserInformation", "currentUser", "post", "filteredRoles", "filter", "role", "useEffect", "clearValue", "obj", "countryRegion", "id", "_regions", "a", "b", "localeCompare", "regionsParams", "enabled", "addform", "oldArray", "removeForm", "handleChange", "selectedIndex", "worldRegion", "getAttribute", "React", "normalizeGroup", "Object", "keys", "_data", "d", "getUsers", "userInvites", "groupParams", "_users", "username", "handleChangeforTimeline", "_tempCosts", "onChangeDate", "key", "onChangeDateTimeline", "dateval", "handleDescription", "handleSubmit", "event", "values", "actions", "toastMsg", "preventDefault", "current", "setAttribute", "mapped", "_", "trim", "patch", "toast", "success", "Router", "error", "removeAttribute", "getID", "imageIds", "docIds", "type", "includes", "serverID", "getSource", "imgSrcArr", "getDocSource", "docSrcArr", "Container", "className", "fluid", "Card", "ValidationFormWrapper", "onSubmit", "ref", "onErrorSubmit", "errors", "handleErrorSubmit", "searchIndex", "currentIndex", "elmnt", "getElementById", "scrollIntoView", "initialValues", "enableReinitialize", "Body", "Row", "Col", "Title", "hr", "md", "lg", "sm", "Form", "Group", "Label", "SelectGroup", "onChange", "required", "errorMessage", "option", "data-worldregion", "style", "max<PERSON><PERSON><PERSON>", "MultiSelect", "overrideStrings", "selectSomeItems", "allItemsAreSelected", "options", "labelledBy", "<PERSON><PERSON><PERSON><PERSON>", "TextInput", "validator", "EditorComponent", "initContent", "evt", "selected", "dateFormat", "placeholderText", "Check", "handleEndDateCheckBox", "disabled", "minDate", "Text", "div", "<PERSON><PERSON>", "variant", "marginTop", "onClick", "marginBottom", "Tabs", "active<PERSON><PERSON>", "onSelect", "k", "Tab", "eventKey", "paddingTop", "item_o", "item_d", "handleDesTab", "span", "xs", "FontAwesomeIcon", "icon", "faPlus", "color", "ReactDropZone", "datas", "srcText", "getImgID", "getImageSource", "re<PERSON><PERSON><PERSON><PERSON>", "window", "scrollTo", "Link", "href", "as", "VspaceModal", "Radio", "RadioGroup", "valueSelected", "children", "touched", "useFormikContext", "<PERSON><PERSON><PERSON><PERSON>", "childrenWithProps", "child", "isObject", "String", "RadioItem", "setFieldValue", "fieldName", "inline", "ValidationForm", "time", "setTime", "VSpaceModal", "setTimeout", "pathname", "source", "Modal", "show", "faCheck", "size", "p", "br", "small", "forwardRef", "autoComplete", "onKeyPress", "rest", "validationSchema", "<PERSON><PERSON>", "shape", "<PERSON><PERSON>", "mockEvent", "stopPropagation", "currentTarget", "nativeEvent", "Event", "bubbles", "cancelable", "defaultPrevented", "eventPhase", "isTrusted", "timeStamp", "now", "isDefaultPrevented", "isPropagationStopped", "persist", "formikProps", "displayName", "multiline", "rows", "pattern", "Field", "validate", "stringVal", "val", "RegExp", "test", "field", "meta", "Control", "isInvalid", "undefined", "<PERSON><PERSON><PERSON>", "temp", "baseStyle", "flex", "display", "flexDirection", "alignItems", "justifyContent", "width", "height", "borderWidth", "borderColor", "backgroundColor", "transition", "padding", "thumbsContainer", "border", "flexWrap", "img", "activeStyle", "dropZoneMsg", "modalShow", "setModalShow", "deleteFile", "setDeleteFile", "process", "files", "setFiles", "multi", "set<PERSON><PERSON><PERSON>", "imageSource", "setImageSource", "endpoint", "imageDelete", "remove", "removeFile", "file", "handleSource", "index", "items", "getComponent", "fileType", "split", "pop", "src", "preview", "modalHide", "cancelHandler", "<PERSON><PERSON><PERSON><PERSON>", "fileselector", "_index", "removeSrc", "newFiles", "indexOf", "thumbs", "controlId", "original_name", "max<PERSON><PERSON><PERSON>", "placeholder", "onHide", "Header", "closeButton", "Footer", "URL", "revokeObjectURL", "singleUpload", "filesUpload", "filesinitial", "FormData", "append", "res", "onDrop", "useCallback", "ondrop_files", "accFiles", "assign", "createObjectURL", "getRootProps", "getInputProps", "isDragActive", "isDragAccept", "isDragReject", "fileRejections", "useDropzone", "accept", "multiple", "minSize", "maxSize", "nameLengthValidator", "substring", "code", "message", "useMemo", "outline", "isFileTooLarge", "input", "faCloudUploadAlt", "faExclamationCircle"], "sourceRoot": "", "ignoreList": []}