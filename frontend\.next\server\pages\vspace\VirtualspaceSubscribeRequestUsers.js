"use strict";(()=>{var e={};e.id=1895,e.ids=[636,1895,3220],e.modules={123:e=>{e.exports=require("dom-helpers/removeEventListener")},1332:e=>{e.exports=require("react-custom-scrollbars-2")},1428:e=>{e.exports=import("axios")},1680:e=>{e.exports=require("@restart/ui/utils")},1919:e=>{e.exports=require("react-transition-group/Transition")},3892:e=>{e.exports=require("classnames")},4048:e=>{e.exports=require("react-truncate")},6009:e=>{e.exports=require("dom-helpers/ownerDocument")},6952:e=>{e.exports=require("@restart/ui/Modal")},7374:e=>{e.exports=require("@restart/ui/Dropdown")},8732:e=>{e.exports=require("react/jsx-runtime")},9653:e=>{e.exports=require("@restart/ui/Overlay")},11242:e=>{e.exports=require("redux-persist/integration/react")},11688:e=>{e.exports=require("@restart/hooks/useWillUnmount")},12388:e=>{e.exports=require("dom-helpers/addEventListener")},13364:e=>{e.exports=require("redux-saga/effects")},14062:e=>{e.exports=import("react-redux")},14078:e=>{e.exports=import("swr")},14332:e=>{e.exports=require("uncontrollable")},16116:e=>{e.exports=require("invariant")},18622:e=>{e.exports=require("yup")},19121:e=>{e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},22326:e=>{e.exports=require("react-dom")},22541:e=>{e.exports=require("redux-persist/lib/storage")},25303:e=>{e.exports=require("@restart/ui/NavItem")},26324:e=>{e.exports=require("warning")},27825:e=>{e.exports=require("lodash")},27910:e=>{e.exports=require("stream")},28217:e=>{e.exports=require("@restart/ui/DropdownItem")},29021:e=>{e.exports=require("fs")},29294:e=>{e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},29628:e=>{e.exports=require("dom-helpers/addClass")},29825:e=>{e.exports=require("prop-types")},29841:e=>{e.exports=require("dom-helpers/hasClass")},30362:e=>{e.exports=require("@restart/ui/DropdownToggle")},33873:e=>{e.exports=require("path")},36653:e=>{e.exports=require("nprogress")},36955:e=>{e.exports=require("@restart/hooks/useBreakpoint")},37766:e=>{e.exports=require("@restart/hooks/useMergedRefs")},38609:e=>{e.exports=require("react-data-table-component")},39756:e=>{e.exports=import("redux")},40051:e=>{e.exports=require("dom-helpers/removeClass")},40361:e=>{e.exports=require("next/dist/compiled/next-server/pages.runtime.prod.js")},42893:e=>{e.exports=import("react-hot-toast")},43294:e=>{e.exports=require("formik")},48639:(e,r,t)=>{t.a(e,async(e,s)=>{try{t.r(r),t.d(r,{config:()=>m,default:()=>d,getServerSideProps:()=>g,getStaticPaths:()=>q,getStaticProps:()=>c,reportWebVitals:()=>P,routeModule:()=>w,unstable_getServerProps:()=>v,unstable_getServerSideProps:()=>f,unstable_getStaticParams:()=>S,unstable_getStaticPaths:()=>b,unstable_getStaticProps:()=>h});var o=t(63885),a=t(80237),i=t(81413),n=t(9616),u=t.n(n),p=t(72386),x=t(99435),l=e([p]);p=(l.then?(await l)():l)[0];let d=(0,i.M)(x,"default"),c=(0,i.M)(x,"getStaticProps"),q=(0,i.M)(x,"getStaticPaths"),g=(0,i.M)(x,"getServerSideProps"),m=(0,i.M)(x,"config"),P=(0,i.M)(x,"reportWebVitals"),h=(0,i.M)(x,"unstable_getStaticProps"),b=(0,i.M)(x,"unstable_getStaticPaths"),S=(0,i.M)(x,"unstable_getStaticParams"),v=(0,i.M)(x,"unstable_getServerProps"),f=(0,i.M)(x,"unstable_getServerSideProps"),w=new o.PagesRouteModule({definition:{kind:a.A.PAGES,page:"/vspace/VirtualspaceSubscribeRequestUsers",pathname:"/vspace/VirtualspaceSubscribeRequestUsers",bundlePath:"",filename:""},components:{App:p.default,Document:u()},userland:x});s()}catch(e){s(e)}})},50009:e=>{e.exports=require("@restart/ui/DropdownMenu")},56084:(e,r,t)=>{t.d(r,{A:()=>p});var s=t(8732);t(82015);var o=t(38609),a=t.n(o),i=t(88751),n=t(30370);function u(e){let{t:r}=(0,i.useTranslation)("common"),t={rowsPerPageText:r("Rowsperpage")},{columns:o,data:u,totalRows:p,resetPaginationToggle:x,subheader:l,subHeaderComponent:d,handlePerRowsChange:c,handlePageChange:q,rowsPerPage:g,defaultRowsPerPage:m,selectableRows:P,loading:h,pagServer:b,onSelectedRowsChange:S,clearSelectedRows:v,sortServer:f,onSort:w,persistTableHead:A,sortFunction:M,...R}=e,k={paginationComponentOptions:t,noDataComponent:r("NoData"),noHeader:!0,columns:o,data:u||[],dense:!0,paginationResetDefaultPage:x,subHeader:l,progressPending:h,subHeaderComponent:d,pagination:!0,paginationServer:b,paginationPerPage:m||10,paginationRowsPerPageOptions:g||[10,15,20,25,30],paginationTotalRows:p,onChangeRowsPerPage:c,onChangePage:q,selectableRows:P,onSelectedRowsChange:S,clearSelectedRows:v,progressComponent:(0,s.jsx)(n.A,{}),sortIcon:(0,s.jsx)("i",{className:"sort-icon fas fa-exchange-alt"}),sortServer:f,onSort:w,sortFunction:M,persistTableHead:A,className:"rki-table"};return(0,s.jsx)(a(),{...k})}u.defaultProps={subHeader:!1,pagination:!0,totalRows:null,pagServer:!0,onSelectedRowsChange:null,clearSelectedRows:!1,sortServer:!1,persistTableHead:!1};let p=u},57664:e=>{e.exports=require("dom-helpers/scrollbarSize")},58928:e=>{e.exports=require("dom-helpers/canUseDOM")},59717:e=>{e.exports=require("@restart/ui/DropdownContext")},60560:e=>{e.exports=require("dom-helpers/contains")},63033:e=>{e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},65447:e=>{e.exports=require("@restart/ui/Button")},67364:e=>{e.exports=require("@restart/hooks/useCallbackRef")},68455:e=>{e.exports=import("redux-saga")},69722:e=>{e.exports=require("es6-promise")},74075:e=>{e.exports=require("zlib")},74716:e=>{e.exports=require("moment")},74987:e=>{e.exports=require("@restart/ui/ModalManager")},78097:e=>{e.exports=require("next-redux-saga")},78634:e=>{e.exports=require("@restart/ui/Anchor")},80237:(e,r)=>{Object.defineProperty(r,"A",{enumerable:!0,get:function(){return t}});var t=function(e){return e.PAGES="PAGES",e.PAGES_API="PAGES_API",e.APP_PAGE="APP_PAGE",e.APP_ROUTE="APP_ROUTE",e.IMAGE="IMAGE",e}({})},81366:e=>{e.exports=require("redux-auth-wrapper/connectedAuthWrapper")},81413:(e,r)=>{Object.defineProperty(r,"M",{enumerable:!0,get:function(){return function e(r,t){return t in r?r[t]:"then"in r&&"function"==typeof r.then?r.then(r=>e(r,t)):"function"==typeof r&&"default"===t?r:void 0}}})},81521:e=>{e.exports=require("dom-helpers/querySelectorAll")},81895:e=>{e.exports=require("@restart/hooks/useEventCallback")},82015:e=>{e.exports=require("react")},86842:e=>{e.exports=require("@restart/ui/SelectableContext")},87571:e=>{e.exports=require("dom-helpers/transitionEnd")},88751:e=>{e.exports=require("next-i18next")},93787:e=>{e.exports=require("redux-persist")},94947:e=>{e.exports=require("@restart/hooks/useTimeout")},96196:e=>{e.exports=require("next-redux-wrapper")},98320:e=>{e.exports=require("@restart/hooks/useIsomorphicEffect")},99435:(e,r,t)=>{t.r(r),t.d(r,{default:()=>n});var s=t(8732),o=t(56084),a=t(27825),i=t.n(a);let n=e=>(0,s.jsx)(s.Fragment,{children:(0,s.jsx)(o.A,{noHeader:!0,columns:e.SubscribeRequestUsers.requestedColumns,data:e.SubscribeRequestUsers.vspaceRequest,dense:!0,paginationServer:!0,pagServer:!0,paginationTotalRows:0,subHeader:!0,subHeaderAlign:"left",pagination:!0,persistTableHead:!0,sortFunction:(e,r,t)=>i().orderBy(e,e=>e[r]?e[r].toLowerCase():e[r],t)})})},99460:e=>{e.exports=require("dom-helpers/css")}};var r=require("../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[6089,9216,9616,2386],()=>t(48639));module.exports=s})();