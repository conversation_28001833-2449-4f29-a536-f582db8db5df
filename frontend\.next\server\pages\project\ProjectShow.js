"use strict";(()=>{var e={};e.id=1766,e.ids=[636,1766,3220],e.modules={123:e=>{e.exports=require("dom-helpers/removeEventListener")},1332:e=>{e.exports=require("react-custom-scrollbars-2")},1428:e=>{e.exports=import("axios")},1680:e=>{e.exports=require("@restart/ui/utils")},1919:e=>{e.exports=require("react-transition-group/Transition")},3892:e=>{e.exports=require("classnames")},4048:e=>{e.exports=require("react-truncate")},6009:e=>{e.exports=require("dom-helpers/ownerDocument")},6417:(e,r,t)=>{t.d(r,{A:()=>o});let s=t(82015).createContext(null);s.displayName="CardHeaderContext";let o=s},6952:e=>{e.exports=require("@restart/ui/Modal")},7374:e=>{e.exports=require("@restart/ui/Dropdown")},8732:e=>{e.exports=require("react/jsx-runtime")},9653:e=>{e.exports=require("@restart/ui/Overlay")},11242:e=>{e.exports=require("redux-persist/integration/react")},11688:e=>{e.exports=require("@restart/hooks/useWillUnmount")},12388:e=>{e.exports=require("dom-helpers/addEventListener")},13364:e=>{e.exports=require("redux-saga/effects")},14062:e=>{e.exports=import("react-redux")},14078:e=>{e.exports=import("swr")},14332:e=>{e.exports=require("uncontrollable")},16116:e=>{e.exports=require("invariant")},18597:(e,r,t)=>{t.d(r,{A:()=>S});var s=t(3892),o=t.n(s),a=t(82015),i=t(80739),p=t(8732);let u=a.forwardRef(({className:e,bsPrefix:r,as:t="div",...s},a)=>(r=(0,i.oU)(r,"card-body"),(0,p.jsx)(t,{ref:a,className:o()(e,r),...s})));u.displayName="CardBody";let n=a.forwardRef(({className:e,bsPrefix:r,as:t="div",...s},a)=>(r=(0,i.oU)(r,"card-footer"),(0,p.jsx)(t,{ref:a,className:o()(e,r),...s})));n.displayName="CardFooter";var d=t(6417);let l=a.forwardRef(({bsPrefix:e,className:r,as:t="div",...s},u)=>{let n=(0,i.oU)(e,"card-header"),l=(0,a.useMemo)(()=>({cardHeaderBsPrefix:n}),[n]);return(0,p.jsx)(d.A.Provider,{value:l,children:(0,p.jsx)(t,{ref:u,...s,className:o()(r,n)})})});l.displayName="CardHeader";let x=a.forwardRef(({bsPrefix:e,className:r,variant:t,as:s="img",...a},u)=>{let n=(0,i.oU)(e,"card-img");return(0,p.jsx)(s,{ref:u,className:o()(t?`${n}-${t}`:n,r),...a})});x.displayName="CardImg";let c=a.forwardRef(({className:e,bsPrefix:r,as:t="div",...s},a)=>(r=(0,i.oU)(r,"card-img-overlay"),(0,p.jsx)(t,{ref:a,className:o()(e,r),...s})));c.displayName="CardImgOverlay";let m=a.forwardRef(({className:e,bsPrefix:r,as:t="a",...s},a)=>(r=(0,i.oU)(r,"card-link"),(0,p.jsx)(t,{ref:a,className:o()(e,r),...s})));m.displayName="CardLink";var q=t(7783);let f=(0,q.A)("h6"),g=a.forwardRef(({className:e,bsPrefix:r,as:t=f,...s},a)=>(r=(0,i.oU)(r,"card-subtitle"),(0,p.jsx)(t,{ref:a,className:o()(e,r),...s})));g.displayName="CardSubtitle";let h=a.forwardRef(({className:e,bsPrefix:r,as:t="p",...s},a)=>(r=(0,i.oU)(r,"card-text"),(0,p.jsx)(t,{ref:a,className:o()(e,r),...s})));h.displayName="CardText";let P=(0,q.A)("h5"),v=a.forwardRef(({className:e,bsPrefix:r,as:t=P,...s},a)=>(r=(0,i.oU)(r,"card-title"),(0,p.jsx)(t,{ref:a,className:o()(e,r),...s})));v.displayName="CardTitle";let b=a.forwardRef(({bsPrefix:e,className:r,bg:t,text:s,border:a,body:n=!1,children:d,as:l="div",...x},c)=>{let m=(0,i.oU)(e,"card");return(0,p.jsx)(l,{ref:c,...x,className:o()(r,m,t&&`bg-${t}`,s&&`text-${s}`,a&&`border-${a}`),children:n?(0,p.jsx)(u,{children:d}):d})});b.displayName="Card";let S=Object.assign(b,{Img:x,Title:v,Subtitle:g,Body:u,Link:m,Text:h,Header:l,Footer:n,ImgOverlay:c})},18622:e=>{e.exports=require("yup")},19121:e=>{e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},22326:e=>{e.exports=require("react-dom")},22541:e=>{e.exports=require("redux-persist/lib/storage")},25303:e=>{e.exports=require("@restart/ui/NavItem")},26324:e=>{e.exports=require("warning")},27825:e=>{e.exports=require("lodash")},27910:e=>{e.exports=require("stream")},28217:e=>{e.exports=require("@restart/ui/DropdownItem")},29021:e=>{e.exports=require("fs")},29294:e=>{e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},29628:e=>{e.exports=require("dom-helpers/addClass")},29825:e=>{e.exports=require("prop-types")},29841:e=>{e.exports=require("dom-helpers/hasClass")},30362:e=>{e.exports=require("@restart/ui/DropdownToggle")},33873:e=>{e.exports=require("path")},36653:e=>{e.exports=require("nprogress")},36955:e=>{e.exports=require("@restart/hooks/useBreakpoint")},37766:e=>{e.exports=require("@restart/hooks/useMergedRefs")},38609:e=>{e.exports=require("react-data-table-component")},39756:e=>{e.exports=import("redux")},40051:e=>{e.exports=require("dom-helpers/removeClass")},40361:e=>{e.exports=require("next/dist/compiled/next-server/pages.runtime.prod.js")},42893:e=>{e.exports=import("react-hot-toast")},43294:e=>{e.exports=require("formik")},50009:e=>{e.exports=require("@restart/ui/DropdownMenu")},54131:e=>{e.exports=import("@fortawesome/free-solid-svg-icons")},57664:e=>{e.exports=require("dom-helpers/scrollbarSize")},58928:e=>{e.exports=require("dom-helpers/canUseDOM")},59717:e=>{e.exports=require("@restart/ui/DropdownContext")},60560:e=>{e.exports=require("dom-helpers/contains")},63033:e=>{e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},65447:e=>{e.exports=require("@restart/ui/Button")},67009:(e,r,t)=>{t.a(e,async(e,s)=>{try{t.r(r),t.d(r,{config:()=>f,default:()=>x,getServerSideProps:()=>q,getStaticPaths:()=>m,getStaticProps:()=>c,reportWebVitals:()=>g,routeModule:()=>w,unstable_getServerProps:()=>b,unstable_getServerSideProps:()=>S,unstable_getStaticParams:()=>v,unstable_getStaticPaths:()=>P,unstable_getStaticProps:()=>h});var o=t(63885),a=t(80237),i=t(81413),p=t(9616),u=t.n(p),n=t(72386),d=t(74756),l=e([n,d]);[n,d]=l.then?(await l)():l;let x=(0,i.M)(d,"default"),c=(0,i.M)(d,"getStaticProps"),m=(0,i.M)(d,"getStaticPaths"),q=(0,i.M)(d,"getServerSideProps"),f=(0,i.M)(d,"config"),g=(0,i.M)(d,"reportWebVitals"),h=(0,i.M)(d,"unstable_getStaticProps"),P=(0,i.M)(d,"unstable_getStaticPaths"),v=(0,i.M)(d,"unstable_getStaticParams"),b=(0,i.M)(d,"unstable_getServerProps"),S=(0,i.M)(d,"unstable_getServerSideProps"),w=new o.PagesRouteModule({definition:{kind:a.A.PAGES,page:"/project/ProjectShow",pathname:"/project/ProjectShow",bundlePath:"",filename:""},components:{App:n.default,Document:u()},userland:d});s()}catch(e){s(e)}})},67364:e=>{e.exports=require("@restart/hooks/useCallbackRef")},68455:e=>{e.exports=import("redux-saga")},69722:e=>{e.exports=require("es6-promise")},74075:e=>{e.exports=require("zlib")},74716:e=>{e.exports=require("moment")},74987:e=>{e.exports=require("@restart/ui/ModalManager")},78097:e=>{e.exports=require("next-redux-saga")},78634:e=>{e.exports=require("@restart/ui/Anchor")},80237:(e,r)=>{Object.defineProperty(r,"A",{enumerable:!0,get:function(){return t}});var t=function(e){return e.PAGES="PAGES",e.PAGES_API="PAGES_API",e.APP_PAGE="APP_PAGE",e.APP_ROUTE="APP_ROUTE",e.IMAGE="IMAGE",e}({})},81149:e=>{e.exports=require("react-responsive-carousel")},81366:e=>{e.exports=require("redux-auth-wrapper/connectedAuthWrapper")},81413:(e,r)=>{Object.defineProperty(r,"M",{enumerable:!0,get:function(){return function e(r,t){return t in r?r[t]:"then"in r&&"function"==typeof r.then?r.then(r=>e(r,t)):"function"==typeof r&&"default"===t?r:void 0}}})},81521:e=>{e.exports=require("dom-helpers/querySelectorAll")},81895:e=>{e.exports=require("@restart/hooks/useEventCallback")},82015:e=>{e.exports=require("react")},82053:e=>{e.exports=require("@fortawesome/react-fontawesome")},86842:e=>{e.exports=require("@restart/ui/SelectableContext")},86843:e=>{e.exports=require("moment/locale/de")},87571:e=>{e.exports=require("dom-helpers/transitionEnd")},88751:e=>{e.exports=require("next-i18next")},93787:e=>{e.exports=require("redux-persist")},94947:e=>{e.exports=require("@restart/hooks/useTimeout")},96196:e=>{e.exports=require("next-redux-wrapper")},98320:e=>{e.exports=require("@restart/hooks/useIsomorphicEffect")},99460:e=>{e.exports=require("dom-helpers/css")}};var r=require("../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[6089,9216,9616,2386,2491,7136,4756],()=>t(67009));module.exports=s})();