"use strict";exports.id=9958,exports.ids=[9958],exports.modules={6417:(e,t,r)=>{r.d(t,{A:()=>o});let a=r(82015).createContext(null);a.displayName="CardHeaderContext";let o=a},6858:(e,t,r)=>{r.d(t,{A:()=>f});var a=r(8732),o=r(82015),l=r.n(o),n=r(18597),s=r(78219),i=r.n(s);function d(e){let{list:t,dialogClassName:r}=e;return(0,a.jsxs)(i(),{...e,dialogClassName:r,"aria-labelledby":"contained-modal-title-vcenter",centered:!0,children:[(0,a.jsx)(i().Header,{closeButton:!0,children:(0,a.jsx)(i().Title,{id:"contained-modal-title-vcenter",children:t.heading})}),(0,a.jsx)(i().Body,{children:t.body})]})}function c(e){let{list:t}=e,[r,o]=l().useState(!1);return t&&t.body?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)("button",{type:"button",onClick:()=>o(!0),style:{border:"none",background:"none",padding:0},children:(0,a.jsx)(n.A.Footer,{children:(0,a.jsx)("i",{className:"fas fa-chevron-down"})})}),e.list&&(0,a.jsx)(d,{list:e.list,show:r,onHide:()=>o(!1),dialogClassName:e.dialogClassName})]}):null}let f=function(e){let{header:t,body:r}=e;return(0,a.jsxs)(n.A,{className:"text-center infoCard",children:[(0,a.jsx)(n.A.Header,{children:t}),(0,a.jsx)(n.A.Body,{children:(0,a.jsx)(n.A.Text,{children:r})}),(0,a.jsx)(c,{...e})]})}},18597:(e,t,r)=>{r.d(t,{A:()=>N});var a=r(3892),o=r.n(a),l=r(82015),n=r(80739),s=r(8732);let i=l.forwardRef(({className:e,bsPrefix:t,as:r="div",...a},l)=>(t=(0,n.oU)(t,"card-body"),(0,s.jsx)(r,{ref:l,className:o()(e,t),...a})));i.displayName="CardBody";let d=l.forwardRef(({className:e,bsPrefix:t,as:r="div",...a},l)=>(t=(0,n.oU)(t,"card-footer"),(0,s.jsx)(r,{ref:l,className:o()(e,t),...a})));d.displayName="CardFooter";var c=r(6417);let f=l.forwardRef(({bsPrefix:e,className:t,as:r="div",...a},i)=>{let d=(0,n.oU)(e,"card-header"),f=(0,l.useMemo)(()=>({cardHeaderBsPrefix:d}),[d]);return(0,s.jsx)(c.A.Provider,{value:f,children:(0,s.jsx)(r,{ref:i,...a,className:o()(t,d)})})});f.displayName="CardHeader";let u=l.forwardRef(({bsPrefix:e,className:t,variant:r,as:a="img",...l},i)=>{let d=(0,n.oU)(e,"card-img");return(0,s.jsx)(a,{ref:i,className:o()(r?`${d}-${r}`:d,t),...l})});u.displayName="CardImg";let p=l.forwardRef(({className:e,bsPrefix:t,as:r="div",...a},l)=>(t=(0,n.oU)(t,"card-img-overlay"),(0,s.jsx)(r,{ref:l,className:o()(e,t),...a})));p.displayName="CardImgOverlay";let x=l.forwardRef(({className:e,bsPrefix:t,as:r="a",...a},l)=>(t=(0,n.oU)(t,"card-link"),(0,s.jsx)(r,{ref:l,className:o()(e,t),...a})));x.displayName="CardLink";var y=r(7783);let j=(0,y.A)("h6"),m=l.forwardRef(({className:e,bsPrefix:t,as:r=j,...a},l)=>(t=(0,n.oU)(t,"card-subtitle"),(0,s.jsx)(r,{ref:l,className:o()(e,t),...a})));m.displayName="CardSubtitle";let h=l.forwardRef(({className:e,bsPrefix:t,as:r="p",...a},l)=>(t=(0,n.oU)(t,"card-text"),(0,s.jsx)(r,{ref:l,className:o()(e,t),...a})));h.displayName="CardText";let v=(0,y.A)("h5"),b=l.forwardRef(({className:e,bsPrefix:t,as:r=v,...a},l)=>(t=(0,n.oU)(t,"card-title"),(0,s.jsx)(r,{ref:l,className:o()(e,t),...a})));b.displayName="CardTitle";let g=l.forwardRef(({bsPrefix:e,className:t,bg:r,text:a,border:l,body:d=!1,children:c,as:f="div",...u},p)=>{let x=(0,n.oU)(e,"card");return(0,s.jsx)(f,{ref:p,...u,className:o()(t,x,r&&`bg-${r}`,a&&`text-${a}`,l&&`border-${l}`),children:d?(0,s.jsx)(i,{children:c}):c})});g.displayName="Card";let N=Object.assign(g,{Img:u,Title:b,Subtitle:m,Body:i,Link:x,Text:h,Header:f,Footer:d,ImgOverlay:p})},20956:(e,t,r)=>{var a=r(92921);t.__esModule=!0,t.default=void 0;var o=a(r(3892)),l=function(e,t){if(e&&e.__esModule)return e;if(null===e||"object"!=typeof e&&"function"!=typeof e)return{default:e};var r=f(t);if(r&&r.has(e))return r.get(e);var a={__proto__:null},o=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var l in e)if("default"!==l&&Object.prototype.hasOwnProperty.call(e,l)){var n=o?Object.getOwnPropertyDescriptor(e,l):null;n&&(n.get||n.set)?Object.defineProperty(a,l,n):a[l]=e[l]}return a.default=e,r&&r.set(e,a),a}(r(82015));a(r(26324));var n=a(r(81895)),s=r(25303),i=r(86842),d=r(11940),c=r(8732);function f(e){if("function"!=typeof WeakMap)return null;var t=new WeakMap,r=new WeakMap;return(f=function(e){return e?r:t})(e)}let u=l.forwardRef(({bsPrefix:e,active:t,disabled:r,eventKey:a,className:l,variant:f,action:u,as:p,...x},y)=>{e=(0,d.useBootstrapPrefix)(e,"list-group-item");let[j,m]=(0,s.useNavItem)({key:(0,i.makeEventKey)(a,x.href),active:t,...x}),h=(0,n.default)(e=>{if(r){e.preventDefault(),e.stopPropagation();return}j.onClick(e)});r&&void 0===x.tabIndex&&(x.tabIndex=-1,x["aria-disabled"]=!0);let v=p||(u?x.href?"a":"button":"div");return(0,c.jsx)(v,{ref:y,...x,...j,onClick:h,className:(0,o.default)(l,e,m.isActive&&"active",r&&"disabled",f&&`${e}-${f}`,u&&`${e}-action`)})});u.displayName="ListGroupItem",t.default=u,e.exports=t.default},32412:(e,t,r)=>{r.d(t,{A:()=>n});var a=r(8732),o=r(52449),l=r.n(o);function n(){return(0,a.jsxs)(l(),{viewBox:"0 0 380 70",height:50,width:317,speed:2,title:"Loading",foregroundColor:"#f7f7f7",backgroundColor:"#ecebeb",uniqueKey:"operation",children:[(0,a.jsx)("rect",{x:"10",y:"0",rx:"4",ry:"4",width:"320",height:"25"}),(0,a.jsx)("rect",{x:"40",y:"40",rx:"3",ry:"3",width:"250",height:"20"})]})}},69958:(e,t,r)=>{r.a(e,async(e,a)=>{try{r.r(t),r.d(t,{default:()=>j});var o=r(8732),l=r(82015);r(27825);var n=r(19918),s=r.n(n),i=r(78373),d=r.n(i),c=r(6858),f=r(63487),u=r(32412),p=e([f]);function x(e){let{list:t}=e;return t.length>0?(0,o.jsx)(d(),{children:t.map((e,t)=>(0,o.jsx)(d().Item,{children:(0,o.jsx)(s(),{href:"/operation/[...routes]",as:`/operation/show/${e._id}`,children:e.title})},t))}):null}function y(e){let{operation:t}=e;return(0,o.jsx)(s(),{href:"/operation/[...routes]",as:`/operation/show/${t.id}`,className:"active-op-project",children:(0,o.jsx)("span",{className:"project-title link",children:t.body})})}f=(p.then?(await p)():p)[0];let j=function(e){let{t,fetchOngoingOperations:r}=e,a=t("OngoingOperations"),[n,s]=(0,l.useState)({body:"",id:"",list:[]}),[i,d]=(0,l.useState)(!0),f={heading:a,body:(0,o.jsx)(x,{list:n.list})};return(0,o.jsx)(c.A,{dialogClassName:"ongoing-project-list",list:f,header:a,body:i?(0,o.jsx)(u.A,{}):(0,o.jsx)(y,{operation:n})})};a()}catch(e){a(e)}})},78373:(e,t,r)=>{var a=r(92921);t.__esModule=!0,t.default=void 0;var o=a(r(3892)),l=function(e,t){if(e&&e.__esModule)return e;if(null===e||"object"!=typeof e&&"function"!=typeof e)return{default:e};var r=f(t);if(r&&r.has(e))return r.get(e);var a={__proto__:null},o=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var l in e)if("default"!==l&&Object.prototype.hasOwnProperty.call(e,l)){var n=o?Object.getOwnPropertyDescriptor(e,l):null;n&&(n.get||n.set)?Object.defineProperty(a,l,n):a[l]=e[l]}return a.default=e,r&&r.set(e,a),a}(r(82015));a(r(26324));var n=r(14332),s=a(r(9532)),i=r(11940),d=a(r(20956)),c=r(8732);function f(e){if("function"!=typeof WeakMap)return null;var t=new WeakMap,r=new WeakMap;return(f=function(e){return e?r:t})(e)}let u=l.forwardRef((e,t)=>{let r,{className:a,bsPrefix:l,variant:d,horizontal:f,numbered:u,as:p="div",...x}=(0,n.useUncontrolled)(e,{activeKey:"onSelect"}),y=(0,i.useBootstrapPrefix)(l,"list-group");return f&&(r=!0===f?"horizontal":`horizontal-${f}`),(0,c.jsx)(s.default,{ref:t,...x,as:p,className:(0,o.default)(a,y,d&&`${y}-${d}`,r&&`${y}-${r}`,u&&`${y}-numbered`)})});u.displayName="ListGroup",t.default=Object.assign(u,{Item:d.default}),e.exports=t.default}};