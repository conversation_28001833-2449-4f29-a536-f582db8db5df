{"c": ["webpack"], "r": ["pages/project"], "m": ["(pages-dir-browser)/./components/common/RegionsMultiCheckboxes.tsx", "(pages-dir-browser)/./node_modules/next/dist/build/webpack/loaders/next-client-pages-loader.js?absolutePagePath=D%3A%5Crki%5Cfrontend%5Cpages%5Cproject%5Cindex.tsx&page=%2Fproject!", "(pages-dir-browser)/./pages/project/ListMapContainer.tsx", "(pages-dir-browser)/./pages/project/ProjectsTable.tsx", "(pages-dir-browser)/./pages/project/ProjectsTableFilter.tsx", "(pages-dir-browser)/./pages/project/index.tsx", "(pages-dir-browser)/./pages/project/permission.tsx", "(pages-dir-browser)/__barrel_optimize__?names=Button,Form!=!./node_modules/react-bootstrap/esm/index.js", "(pages-dir-browser)/__barrel_optimize__?names=Col,Container,Form,FormControl,Row!=!./node_modules/react-bootstrap/esm/index.js"]}