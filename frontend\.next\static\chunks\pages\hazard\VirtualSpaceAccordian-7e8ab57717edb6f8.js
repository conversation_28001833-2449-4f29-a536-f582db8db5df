(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[114],{34381:(e,a,t)=>{"use strict";t.d(a,{A:()=>d});var s=t(37876),r=t(14232),n=t(48230),o=t.n(n),i=t(50749),c=t(53718),l=t(31753);let d=e=>{let{t:a}=(0,l.Bd)("common"),{type:t,id:n}=e,[d,u]=(0,r.useState)([]),[g,p]=(0,r.useState)(!1),[m,h]=(0,r.useState)(0),[v,P]=(0,r.useState)(10),[w]=(0,r.useState)(!1),f={sort:{created_at:"asc"},limit:v,page:1,query:{}},j=[{name:a("Title"),selector:"title",cell:e=>e&&e.title&&e._id?(0,s.jsx)(o(),{href:"/vspace/[...routes]",as:"/vspace/show/".concat(e._id),children:e.title}):""},{name:a("Owner"),selector:"users",cell:e=>e&&e.user&&e.user.firstname?"".concat(e.user.firstname," ").concat(e.user.lastname):""},{name:a("PublicPrivate"),selector:"visibility",cell:e=>e&&e.visibility?"Public":"Private"},{name:a("NumberofMembers"),selector:"members",cell:e=>e&&e.members?e.members.length:"-"}],x=async e=>{p(!0);let a=await c.A.get("stats/get".concat(t,"WithVspace/").concat(n),f);a&&("Operation"===t?u(a.operation):u(a.project),h(a.totalCount),p(!1))},b=async(e,a)=>{f.limit=e,f.page=a,p(!0);let s=await c.A.get("stats/get".concat(t,"WithVspace/").concat(n),f);s&&("Operation"===t?u(s.operation):u(s.project),P(e),p(!1))};return(0,r.useEffect)(()=>{x(f)},[]),(0,s.jsx)("div",{children:(0,s.jsx)(i.A,{columns:j,data:d,totalRows:m,loading:g,resetPaginationToggle:w,handlePerRowsChange:b,handlePageChange:e=>{f.limit=v,f.page=e,x(f)}})})}},50749:(e,a,t)=>{"use strict";t.d(a,{A:()=>c});var s=t(37876);t(14232);var r=t(89773),n=t(31753),o=t(5507);function i(e){let{t:a}=(0,n.Bd)("common"),t={rowsPerPageText:a("Rowsperpage")},{columns:i,data:c,totalRows:l,resetPaginationToggle:d,subheader:u,subHeaderComponent:g,handlePerRowsChange:p,handlePageChange:m,rowsPerPage:h,defaultRowsPerPage:v,selectableRows:P,loading:w,pagServer:f,onSelectedRowsChange:j,clearSelectedRows:x,sortServer:b,onSort:_,persistTableHead:A,sortFunction:S,...C}=e,R={paginationComponentOptions:t,noDataComponent:a("NoData"),noHeader:!0,columns:i,data:c||[],dense:!0,paginationResetDefaultPage:d,subHeader:u,progressPending:w,subHeaderComponent:g,pagination:!0,paginationServer:f,paginationPerPage:v||10,paginationRowsPerPageOptions:h||[10,15,20,25,30],paginationTotalRows:l,onChangeRowsPerPage:p,onChangePage:m,selectableRows:P,onSelectedRowsChange:j,clearSelectedRows:x,progressComponent:(0,s.jsx)(o.A,{}),sortIcon:(0,s.jsx)("i",{className:"sort-icon fas fa-exchange-alt"}),sortServer:b,onSort:_,sortFunction:S,persistTableHead:A,className:"rki-table"};return(0,s.jsx)(r.Ay,{...R})}i.defaultProps={subHeader:!1,pagination:!0,totalRows:null,pagServer:!0,onSelectedRowsChange:null,clearSelectedRows:!1,sortServer:!1,persistTableHead:!1};let c=i},52476:(e,a,t)=>{(window.__NEXT_P=window.__NEXT_P||[]).push(["/hazard/VirtualSpaceAccordian",function(){return t(75657)}])},75657:(e,a,t)=>{"use strict";t.r(a),t.d(a,{default:()=>d});var s=t(37876),r=t(11041),n=t(21772),o=t(32890),i=t(34381),c=t(31753),l=t(14232);let d=e=>{var a,t;let{t:d}=(0,c.Bd)("common"),[u,g]=(0,l.useState)(!1);return(0,s.jsx)(s.Fragment,{children:(0,s.jsxs)(o.A.Item,{eventKey:"0",children:[(0,s.jsxs)(o.A.Header,{onClick:()=>g(!u),children:[(0,s.jsx)("div",{className:"cardTitle",children:d("LinkedVirtualSpace")}),(0,s.jsx)("div",{className:"cardArrow",children:u?(0,s.jsx)(n.g,{icon:r.EZy,color:"#fff"}):(0,s.jsx)(n.g,{icon:r.QLR,color:"#fff"})})]}),(0,s.jsx)(o.A.Body,{children:(0,s.jsx)(i.A,{id:(null==(t=e.routeData)||null==(a=t.routes)?void 0:a[1])||"",type:"Project",vspaceData:[],vspaceDataLoading:!1,vspaceDataTotalRows:0,vspaceDataPerPage:10,vspaceDataCurrentPage:1})})]})})}}},e=>{var a=a=>e(e.s=a);e.O(0,[7725,9773,1772,636,6593,8792],()=>a(52476)),_N_E=e.O()}]);
//# sourceMappingURL=VirtualSpaceAccordian-7e8ab57717edb6f8.js.map