"use strict";exports.id=4395,exports.ids=[4395],exports.modules={15653:(e,t,i)=>{i.d(t,{ks:()=>n,s3:()=>r});var a=i(8732);i(82015);var s=i(59549),l=i(43294);let n=({name:e,id:t,required:i,validator:n,errorMessage:r,onChange:o,value:d,as:c,multiline:u,rows:p,pattern:m,...h})=>(0,a.jsx)(l.<PERSON>,{name:e,validate:e=>{let t="string"==typeof e?e:String(e||"");return i&&(!e||""===t.trim())?r?.validator||"This field is required":n&&!n(e)?r?.validator||"Invalid value":m&&e&&!new RegExp(m).test(e)?r?.pattern||"Invalid format":void 0},children:({field:e,meta:i})=>(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(s.A.Control,{...e,...h,id:t,as:c||"input",rows:p,isInvalid:i.touched&&!!i.error,onChange:t=>{e.onChange(t),o&&o(t)},value:void 0!==d?d:e.value}),i.touched&&i.error?(0,a.jsx)(s.A.Control.Feedback,{type:"invalid",children:i.error}):null]})}),r=({name:e,id:t,required:i,errorMessage:n,onChange:r,value:o,children:d,...c})=>(0,a.jsx)(l.Field,{name:e,validate:e=>{if(i&&(!e||""===e))return n?.validator||"This field is required"},children:({field:e,meta:i})=>(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(s.A.Control,{as:"select",...e,...c,id:t,isInvalid:i.touched&&!!i.error,onChange:t=>{e.onChange(t),r&&r(t)},value:void 0!==o?o:e.value,children:d}),i.touched&&i.error?(0,a.jsx)(s.A.Control.Feedback,{type:"invalid",children:i.error}):null]})})},18511:(e,t,i)=>{i.a(e,async(e,a)=>{try{i.r(t),i.d(t,{default:()=>f});var s=i(8732),l=i(82015),n=i(12103),r=i.n(n),o=i(19442),d=i.n(o),c=i(12403),u=i(83551),p=i(49481),m=i(91353),h=i(42893),x=i(63487),g=i(88751),j=e([h,x]);[h,x]=j.then?(await j)():j;let f=({isOpen:e,onModalClose:t,image:i,getId:a,fileName:n,getBlob:o})=>{let[j,f]=(0,l.useState)(1),[y,A]=(0,l.useState)(""),[v,b]=(0,l.useState)(null),w=(0,l.useRef)(null),{t:_}=(0,g.useTranslation)("common");(0,l.useEffect)(()=>{A(n)},[n]);let S=async()=>{let e=w.current.getImage().toDataURL("image/jpeg",.6),i=(e=>{let t=e.split(","),i=t[0].match(/:(.*?);/)?.[1],a=atob(t[1]),s=a.length,l=new Uint8Array(s);for(;s--;)l[s]=a.charCodeAt(s);return new Blob([l],{type:i})})(e),s=(window.URL||window.webkitURL).createObjectURL(i);o(s);let l=new FormData;l.append("file",i,y);try{let e=await x.A.post("/image",l,{"Content-Type":"multipart/form-data"});e&&e._id&&a(e._id)}catch{throw"Something wrong in server || your data!"}h.default.success(_("toast.CroppedtheimageSuccessfully")),t(!1),b(null),A("none"),f(1)};return(0,s.jsx)(s.Fragment,{children:(0,s.jsx)("div",{children:(0,s.jsxs)(c.A,{show:e,size:"lg","aria-labelledby":"ProfileEdit",onHide:()=>t(!1),centered:!0,children:[(0,s.jsxs)(c.A.Body,{children:[(0,s.jsxs)("div",{className:"d-flex flex-column justify-content-center align-items-center imgRotate",children:[(0,s.jsx)(r(),{ref:w,width:700,height:400,borderRadius:2,scale:j,color:[0,0,0,.6],image:v||i,style:{width:"100%",height:"auto"}}),(0,s.jsx)("div",{className:"info-identifier",children:(0,s.jsx)("span",{children:_("ThisareawillcontainyourInstitutionandfocalpointinformation")})})]}),(0,s.jsx)("div",{className:"mx-2 my-3",children:(0,s.jsxs)(u.A,{children:[(0,s.jsx)(p.A,{sm:1,md:1,lg:1,className:"pe-0",children:(0,s.jsx)("b",{children:_("Zoom")})}),(0,s.jsx)(p.A,{sm:11,md:11,lg:11,children:(0,s.jsx)(d(),{value:j,tooltip:"auto",min:1,max:10,step:.01,variant:"primary",onChange:e=>f(Number(e.target.value))})})]})})]}),(0,s.jsxs)(c.A.Footer,{children:[(0,s.jsx)(m.A,{onClick:S,children:_("Crop")}),(0,s.jsx)(m.A,{variant:"danger",onClick:()=>t(!1),children:_("Cancel")})]})]})})})};a()}catch(e){a(e)}})},55122:(e,t,i)=>{i.a(e,async(e,a)=>{try{i.r(t),i.d(t,{default:()=>h});var s=i(8732),l=i(82015),n=i(16029),r=i(82053),o=i(54131),d=i(42893),c=i(18511),u=i(63487),p=i(88751),m=e([o,d,c,u]);[o,d,c,u]=m.then?(await m)():m;let h=({getId:e,header:t,type:i})=>{let{t:a}=(0,p.useTranslation)("common"),[m,h]=(0,l.useState)(!1),[x,g]=(0,l.useState)([]),[j,f]=(0,l.useState)(""),[y,A]=(0,l.useState)(""),v="application"===i?"/files":"/image";(0,l.useEffect)(()=>{t?A(`http://localhost:3001/api/v1/image/show/${t}`):A(null)},[t]);let b={flex:1,display:"flex",flexDirection:"column",alignItems:"center",justifyContent:"center",width:"100%",height:"100%",borderWidth:.1,borderColor:"#fafafa",backgroundColor:"#fafafa",color:"black",transition:"border  .24s ease-in-out"},w={borderColor:"#2196f3"},_=e=>{h(e)},{getRootProps:S,getInputProps:C,isDragActive:I,isDragAccept:N,isDragReject:k,fileRejections:L}=(0,n.useDropzone)({noClick:!1,accept:"image/*",multiple:!1,minSize:0,maxSize:2e6,onDrop:e=>{let t=e.map((e,t)=>Object.assign(e,{preview:URL.createObjectURL(e)}));g(t),e.length>0&&h(!0)},validator:function(e){return"/image"===v?"image"===e.type.substring(0,5)||d.default.error(a("toast.filetypenotsupport")):"/files"===v&&"image"===e.type.substring(0,5)&&d.default.error(a("toast.filetypenotsupport")),null}}),T=(0,l.useMemo)(()=>({...b,...I?w:{outline:"2px dashed #bbb"},...N?{outline:"2px dashed #595959"}:{outline:"2px dashed #bbb"},...k?{outline:"2px dashed red"}:{activeStyle:w}}),[I,k]),D=L.length>0&&L[0].file.size>2e6,E=t=>{f(t),e(t)},P=async()=>{let e;(e=t?await u.A.remove(`image/${t}`):await u.A.remove(`image/${j}`))&&e._id&&(A(null),E(null))},F=e=>{A(e)};return(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)("div",{className:" d-flex justify-content-center align-items-center mt-3",style:{width:"100%",height:"180px"},children:(0,s.jsxs)("div",{...S({style:T}),children:[(0,s.jsx)("input",{...C()}),(0,s.jsx)(r.FontAwesomeIcon,{icon:o.faCloudUploadAlt,size:"4x",color:"#999"}),(0,s.jsx)("p",{style:{color:"#595959",marginBottom:"0px"},children:a("Drag'n'dropsomefileshere,orclicktoselectfiles")}),(0,s.jsxs)("small",{style:{color:"#595959"},children:[(0,s.jsx)("b",{children:a("Note:")})," ",a("Onesingleimagewillbeaccepted")]}),D&&(0,s.jsxs)("small",{className:"text-danger mt-2",children:[(0,s.jsx)(r.FontAwesomeIcon,{icon:o.faExclamationCircle,size:"1x",color:"red"}),"\xa0",a("FileistoolargeItshouldbelessthan2MB")]}),k&&(0,s.jsxs)("small",{className:"text-danger",style:{color:"red"},children:[(0,s.jsx)(r.FontAwesomeIcon,{icon:o.faExclamationCircle,size:"1x",color:"red"}),a("Filetypenotacceptedsorr")]})]})}),y&&(0,s.jsx)(s.Fragment,{children:(0,s.jsx)("div",{style:{display:"flex",flexDirection:"row",justifyContent:"flex-start",flexWrap:"wrap",marginTop:20},children:(0,s.jsxs)("div",{style:{display:"inline-flex",borderRadius:2,border:"1px solid #ddd",marginBottom:8,marginRight:20,width:170,height:100,padding:2,position:"relative",boxShadow:"0 0 15px 0.25px rgba(0,0,0,0.25)",boxSizing:"border-box"},children:[(0,s.jsx)("div",{style:{display:"flex"},children:(0,s.jsx)("img",{src:y,style:{display:"block",height:"100%"}})}),(0,s.jsx)(r.FontAwesomeIcon,{icon:o.faTimesCircle,style:{position:"absolute",fontSize:"22px",top:"-10px",right:"-10px",zIndex:1e3,cursor:"pointer",backgroundColor:"#fff",color:"#000",borderRadius:"50%"},color:"black",onClick:P})]})})}),(0,s.jsx)(c.default,{isOpen:m,getId:e=>E(e),image:x&&x[0]?x[0].preview:"",onModalClose:e=>_(e),fileName:x&&x[0]?x[0].name:"",getBlob:e=>F(e)})]})};a()}catch(e){a(e)}})},55476:(e,t,i)=>{i.a(e,async(e,a)=>{try{i.d(t,{A:()=>r});var s=i(63487),l=e([s]);s=(l.then?(await l)():l)[0];class n{arrayDifference(e,t){let i=[];return e.forEach(e=>{0===t.filter(t=>t._id===e.value).length&&i.push(e.value)}),i}async deleteInvitations(e,t){e.forEach(async e=>{let i=await s.A.get(`/users/${e}`);i.institutionInvites.length&&(i.institutionInvites=i.institutionInvites.filter(e=>e.institutionId!==t),await s.A.patch(`/users/${e}`,i))})}isDuplicateInvite(e,t){return!!e.filter(e=>e.institutionId===t).length}getNewInviteMeta(e,t){return{institutionName:t,institutionId:e,status:"Request Pending"}}async getUserData(e,t,i){let a={};return t&&i?await s.A.get("/users",{query:{email:t,username:i}}):await s.A.get(`/users/${e}`)}async sendInvitations(e,t,i){e.forEach(async e=>{let a=e._id;if(a){let e=await this.getUserData(a);e&&(e.institutionInvites||(e.institutionInvites=[]),e.institutionInvites=e.institutionInvites.map(e=>(e.institutionId===t&&"Rejected"===e.status&&(e.status="Request Pending"),e)),this.isDuplicateInvite(e.institutionInvites||[],t)||e.institutionInvites.push(this.getNewInviteMeta(t,i)),await s.A.patch(`/users/${a}`,e))}else this.inviteNewUserWithEmail(e.email,e.username,t,i)})}async inviteNewUserWithEmail(e,t,i,a){let l=await s.A.get("/users",{query:{email:e,username:t}});l&&l.data[0]&&((l=l.data[0]).institutionInvites.push(this.getNewInviteMeta(i,a)),await s.A.patch(`/users/${l._id}`,l))}}let r=new n;a()}catch(e){a(e)}})},64395:(e,t,i)=>{i.a(e,async(e,a)=>{try{i.r(t),i.d(t,{default:()=>D});var s=i(8732),l=i(82015),n=i(44233),r=i.n(n),o=i(27825),d=i.n(o),c=i(19918),u=i.n(c),p=i(11e3),m=i(7082),h=i(18597),x=i(83551),g=i(49481),j=i(59549),f=i(21852),y=i(96158),A=i(72521),v=i(91353),b=i(15653),w=i(66994),_=i(42893),S=i(98178),C=i(63487),I=i(55476),N=i(88751),k=i(55122),L=i(24047),T=e([_,S,C,I,k]);[_,S,C,I,k]=T.then?(await T)():T;let D=({routes:e,institution:t})=>{let i=(0,l.useRef)(null),a=(0,l.useRef)(null),{t:o,i18n:c}=(0,N.useTranslation)("common"),T={organisationName:"",title:"",country:"",world_region:"",website:"",organisationType:null,telephone:"",dial_code:o("DialCode"),contact_name:"",twitter:"",addressL1:"",addressL2:"",city:"",region:[],users:[],institution:"",expertise:[],network:[],hazards:[],description:"",images:[],header:null,department:"",unit:"",use_default_header:!0,images_src:[]},{query:D}=(0,n.useRouter)(),E=c.language,P="de"===c.language?{title_de:"asc"}:{title:"asc"},[F,R]=(0,l.useState)([]),[q,z]=(0,l.useState)([]),[G,M]=(0,l.useState)([]),[U,$]=(0,l.useState)(null),[O,B]=(0,l.useState)([]),[W,H]=(0,l.useState)([]),[K,V]=(0,l.useState)([]),[J,Z]=(0,l.useState)([]),[Q,X]=(0,l.useState)([]),[Y,ee]=(0,l.useState)(1),[,et]=(0,l.useState)([]),[ei,ea]=(0,l.useState)([{username:"",email:"",institution:"",focal_dial_code:o("DialCode"),mobile_number:"",_id:null}]),[es,el]=(0,l.useState)([]),[en,er]=(0,l.useState)([]),[eo,ed]=(0,l.useState)([]),[ec,eu]=(0,l.useState)([]),ep=e&&"edit"==e[0]&&!!e[1],[em,eh]=(0,l.useState)(T),[ex,eg]=(0,l.useState)([]),[ej,ef]=(0,l.useState)([]),ey={query:{},sort:{title:"asc"},limit:"~"},eA={query:{},sort:{title:"asc"},select:"-networks -expertise -focal_points -hazard_types -description -twitter -telephone -images -header -partners -type -user -website -hazards -contact_name -updated_at -created_at -__v",limit:"~",lean:!0};(0,l.useEffect)(()=>{if("edit"==e[0]&&t&&t){!function(e){e.organisationType=e&&e.type?e.type._id:"",e.addressL1=e.address.line_1,e.addressL2=e.address.line_2,e.city=e.address.city,e.country=e.address.country&&e.address.country._id?e.address.country._id:" ",e.world_region=e.address&&e.address.world_region?e.address.world_region:"",e.region=e.address.region?e.address.region.map((e,t)=>({label:e.title,value:e._id})):[]}(t);let e="fr"===c.language?"en":c.language,{_expertise:i,_network:a,_partners:s,_hazards:l}=function(e,t){let i=e.expertise?e.expertise.map((e,t)=>({label:e.title,value:e._id})):[],a=e.networks?e.networks.map((e,t)=>({label:e.title,value:e._id})):[],s=e.partners?e.partners.map((e,t)=>({label:e.title,value:e._id})):[],l=e.hazards?e.hazards.map(e=>({label:e.title[t],value:e._id})):[];return e.users=e.focal_points?e.focal_points.map((e,t)=>({label:e.username,value:e._id})):[],{_expertise:i,_network:a,_partners:s,_hazards:l}}(t,e);eT(t.country,ey),H(i),V(a),eG(s),B(l),et(l),z(t.images?t.images:[]),M(t.images_src?t.images_src:[]),$(t.header&&t.header._id?t.header._id:null),eh(e=>({...e,...t}))}eL(),eS(),ev(),eC(ey),eN(ey),ek(ey),eI(eA)},[]);let ev=async()=>{let e=await C.A.get("/country",{sort:P,limit:"~",select:"-code -code3 -coordinates -created_at -first_letter -health_profile -security_advice -updated_at",lean:!0,languageCode:E});e&&Array.isArray(e.data)&&R(e.data)},eb=t=>{if(t&&t.data&&t.data.length>0){let i=[];t.data.forEach(t=>{t.institutionInvites.filter(t=>t.institutionId===e[1]&&"Approved"===t.status).length&&i.push({label:t.username,value:t._id,email:t.email})}),eg(i),eh(e=>({...e,users:i}))}},ew=(t,i)=>{let a=!1;return t.forEach(t=>{t.institutionInvites.forEach(s=>{if(s.institutionId===e[1]&&"Request Pending"===s.status&&i._id===t._id){a=!0;return}})}),a},e_=e=>{let t=[];return(e=e.filter(t=>!ew(e,t))).forEach(e=>{t.push({label:e.username,value:e._id})}),t},eS=async()=>{let e=await C.A.get("/users",{query:{},sort:{title:"asc"},limit:"~",select:"-firstname -lastname -email -password -role -country -region -institution -is_focal_point -image -mobile_number -enabled -created_at -updated_at -dataConsentPolicy -restrictedUsePolicy -acceptCookiesPolicy -withdrawConsentPolicy -medicalConsentPolicy -fullDataProtectionConsentPolicy -emailActivateToken"});if(ep&&eb(e),e&&Array.isArray(e.data)){let t=[];if(ep){let i=e_(e.data);e.data.forEach(e=>{t.push({label:e.username,value:e._id})}),X(i)}else e.data.forEach(e=>{t.push({label:e.username,value:e._id})}),X(t)}},eC=async e=>{let t=await C.A.get("/InstitutionType",e);t&&Array.isArray(t.data)&&el(t.data)},eI=async e=>{let t=await C.A.get("/institution",e);if(t&&Array.isArray(t.data)){let e=t.data.filter(e=>e._id!=D.routes[1]).map((e,t)=>({label:e.title,value:e._id}));ef(e)}},eN=async e=>{let t=await C.A.get("/Expertise",e);if(t&&Array.isArray(t.data)){let e=t.data.map((e,t)=>({label:e.title,value:e._id}));er(e)}},ek=async e=>{let t=await C.A.get("/InstitutionNetwork",e);if(t&&Array.isArray(t.data)){let e=t.data.map((e,t)=>({label:e.title,value:e._id}));ed(e)}},eL=async()=>{let e=[],t=await C.A.get("/hazardtype",{query:{},limit:"~",sort:{title:"asc"}});t&&Array.isArray(t.data)&&d().each(t.data,(t,i)=>{e.push(t._id)});let i=await C.A.get("/hazard",{query:{hazard_type:e},limit:"~",sort:{title:"asc"}});if(i&&Array.isArray(i.data)){let e=i.data.map((e,t)=>({label:e.title[E],value:e._id}));eu(e)}},eT=async(e,t)=>{let i=[];if(e){let a=await C.A.get(`/country_region/${e}`,t);a&&a.data&&(i=a.data.map((e,t)=>({label:e.title,value:e._id}))).sort((e,t)=>e.label.localeCompare(t.label))}Z(i)},eD=e=>{eh(t=>({...t,...e}))},eE=e=>{let{name:t,value:i}=e.target;if(eh(e=>({...e,[t]:i})),"country"==t){let t=e.target.selectedIndex;if(e.target&&t&&null!=t){let i=e.target[t].getAttribute("data-worldregion");eh(e=>({...e,world_region:i}))}}"country"==t&&(eT(i,ey),eD({region:[]}))},eP=(e,t)=>{eh(i=>({...i,[t]:e}))},eF=e=>{eh(t=>({...t,description:e}))},[eR]=(0,l.useState)(""),[eq]=(0,l.useState)(""),[ez,eG]=(0,l.useState)([]),eM=()=>{let e=[...ei];e.push({username:"",email:"",focal_dial_code:o("DialCode"),mobile_number:"",institution:"",_id:null}),ea(e),ee(e.length)},eU=(e,t)=>{ei.splice(t,1);let i=[...ei];ea(i),ee(i.length),0===ei.length&&eM()},e$=(e,t)=>{let i=[...ei];i[t][e.target.name]=e.target.value,ea(i)},eO=(t,i)=>{if(ep){let a=I.A.arrayDifference(ex,t);I.A.deleteInvitations(a,e[1]),I.A.sendInvitations(t,e[1],i.title)}else t.forEach(async e=>{let t,a=e._id;if(a?t=await C.A.get(`/users/${a}`):e.email&&e.username&&(t=await C.A.get("/users",{query:{email:e.email,username:e.username}}),a=(t=t?.data?.length?t.data[0]:{})._id),t&&t._id){let e=[];t?.institutionInvites?.length?(e=[...t.institutionInvites]).filter(e=>e.institutionId===i._id).length<=0?e.push({institutionName:i.title,institutionId:i._id,status:"Request Pending"}):e=e.map(e=>(e.institutionId===i._id&&"Rejected"===e.status&&(e.status="Request Pending"),e)):e=[{institutionName:i.title,institutionId:i._id,status:"Request Pending"}],t.institutionInvites=e,await C.A.patch(`/users/${a}`,t)}})},eB=async t=>{let i,a;t.preventDefault();let s=em.users.map((e,t)=>({_id:e.value}));for(let e=0;e<ei.length;e++)!function(e,t){e[t].username=e[t].username.toLowerCase(),e[t].email=e[t].email.toLowerCase()}(ei,e);let l=d().uniqBy(ei,"username"),n=d().uniqBy(ei,"email");if(l.length!=ei.length||n.length!=ei.length)return void _.default.error(o("toast.UniqueFocalpointIssue"));let c={title:em.title.trim(),contact_name:em.contact_name,networks:K?K.map((e,t)=>e.value):[],partners:ez?ez.map((e,t)=>e.value):[],expertise:W?W.map((e,t)=>e.value):[],hazards:em.hazards,address:{country:em.country,world_region:em.world_region,region:em.region?em.region.map((e,t)=>e.value):[],line_1:em.addressL1,line_2:em.addressL2,city:em.city},focal_points:[...ei,...s],website:em.website,telephone:em.telephone,dial_code:em.dial_code,twitter:em.twitter,type:em.organisationType,description:em.description,images:em.images,header:em.header,department:em.department,unit:em.unit,use_default_header:null==em.header,images_src:em.images_src,primary_focal_point:em.primary_focal_point};O&&O.length&&(c.hazards=O.map(e=>e.value)),ep?(c?.focal_points?.length>0&&!c.focal_points.find(e=>e._id===em.primary_focal_point)&&(c.primary_focal_point=""),a="toast.Organisationupdatedsuccessfully",i=await C.A.patch(`/institution/${e[1]}`,c)):(a="toast.Organisationaddedsuccessfully",i=await C.A.post("/institution",c)),i&&i._id?(c.focal_points?.length&&eO(c.focal_points,i),o("addOrganisation"),_.default.success(o(a)),r().push("/institution/[...routes]",`/institution/show/${i._id}`)):(a=ep?"toast.OrganisationNotupdatedsuccessfully":"toast.OrganisationNotaddedsuccessfully","toast.usernameoremailusedalready"==i&&(a="toast.OrganisationNameShouldUnique"),_.default.error(o(a)))},eW=(e,t)=>{let i={},a=e.filter(e=>e.index==t).map(e=>e.serverID);switch(t){case 0:i={header:String(a)};break;case 1:break;case 2:i={images:a}}eh(e=>({...e,...i}))},eH=e=>{eh(t=>({...t,images_src:e}))},eK=e=>{eh(t=>({...t,header:e,use_default_header:null==e}))};return(0,s.jsx)(w.A,{onSubmit:eB,ref:a,initialValues:em,enableReinitialize:!0,children:(0,s.jsx)(m.A,{className:"formCard",fluid:!0,children:(0,s.jsx)(h.A,{children:(0,s.jsxs)(h.A.Body,{children:[(0,s.jsx)(x.A,{children:(0,s.jsx)(g.A,{children:(0,s.jsx)(h.A.Title,{children:"edit"===e[0]?o("editOrganisation"):o("addOrganisation")})})}),(0,s.jsx)(x.A,{className:"mb-3",children:(0,s.jsx)(g.A,{children:(0,s.jsxs)(j.A.Group,{children:[(0,s.jsx)(j.A.Label,{className:"required-field",children:o("OrganisationName")}),(0,s.jsx)(b.ks,{name:"title",id:"title",required:!0,value:em.title,onChange:eE,validator:e=>""!=e.trim(),errorMessage:{validator:o("PleaseAddtheOrganisationName")},as:"input",multiline:!1,rows:1})]})})}),(0,s.jsx)(x.A,{className:"mb-3",children:(0,s.jsx)(g.A,{children:(0,s.jsxs)(j.A.Group,{children:[(0,s.jsxs)(j.A.Label,{children:[" ",o("Description")]}),(0,s.jsx)(L.x,{initContent:em.description,onChange:e=>eF(e)})]})})}),(0,s.jsx)(x.A,{className:"mb-3",children:(0,s.jsx)(g.A,{children:(0,s.jsxs)(j.A.Group,{children:[(0,s.jsxs)(j.A.Label,{className:"d-inline switch-right",children:[o("HeaderImage"),(0,s.jsx)(j.A.Check,{className:"ms-2 d-inline institution_switch",type:"switch",checked:em.use_default_header,id:"custom-switch",label:o("UseDefaultHeader")})]}),(0,s.jsx)(k.default,{getId:e=>eK(e),header:U,type:"image"})]})})}),(0,s.jsxs)(x.A,{className:"mb-3",children:[(0,s.jsx)(g.A,{lg:4,sm:6,children:(0,s.jsxs)(j.A.Group,{children:[(0,s.jsx)(j.A.Label,{children:o("Website")}),(0,s.jsx)(b.ks,{name:"website",id:"website",value:em.website,pattern:"^(?:http(s)?:\\/\\/)?[\\w.-]+(?:\\.[\\w\\.-]+)+[\\w\\-\\._~:/?#[\\]@!\\$&'\\(\\)\\*\\+,;=.]+$",errorMessage:{pattern:o("Pleaseentervalidwebsite")},onChange:eE,required:!1,as:"input",multiline:!1,rows:1})]})}),(0,s.jsx)(g.A,{lg:4,sm:6,children:(0,s.jsxs)(f.A,{className:"row ms-0 me-0",children:[(0,s.jsxs)(j.A.Group,{className:"institutionDialCode col-5 col-lg-4 ps-0 pe-0",children:[(0,s.jsx)(j.A.Label,{children:o("Telephone")}),(0,s.jsxs)(b.s3,{type:"numbers",name:"dial_code",id:"dialCode",value:em.dial_code,onChange:eE,required:!1,errorMessage:"",children:[(0,s.jsx)("option",{value:em.dial_code,children:em.dial_code}),F.map((e,t)=>(0,s.jsx)("option",{value:e.dial_code,children:`(${e.dial_code}) ${e.title}`},t))]})]}),(0,s.jsxs)(j.A.Group,{className:"institutionTelephone col-7 col-lg-8 pe-0",children:[(0,s.jsx)(j.A.Label,{children:"\xa0"}),(0,s.jsx)(b.ks,{name:"telephone",id:"telephone",value:em.telephone,onChange:eE,required:!1,as:"input",multiline:!1,rows:1,errorMessage:{}})]})]})}),(0,s.jsx)(g.A,{lg:4,sm:6,children:(0,s.jsxs)(j.A.Group,{children:[(0,s.jsx)(j.A.Label,{children:o("Twitter")}),(0,s.jsx)(b.ks,{name:"twitter",id:"twitter",value:em.twitter,onChange:eE,required:!1,as:"input",multiline:!1,rows:1,errorMessage:{},validator:()=>!0,pattern:""})]})})]}),(0,s.jsx)(h.A.Text,{children:(0,s.jsx)("b",{children:o("Address")})}),(0,s.jsxs)(x.A,{className:"mb-3",children:[(0,s.jsx)(g.A,{lg:!0,md:2,children:(0,s.jsxs)(j.A.Group,{children:[(0,s.jsx)(j.A.Label,{children:o("Address1")}),(0,s.jsx)(b.ks,{name:"addressL1",id:"addressL1",value:em.addressL1,onChange:eE,required:!1,as:"input",multiline:!1,rows:1,errorMessage:{},validator:()=>!0,pattern:""})]})}),(0,s.jsx)(g.A,{lg:!0,md:2,children:(0,s.jsxs)(j.A.Group,{children:[(0,s.jsx)(j.A.Label,{children:o("Address2")}),(0,s.jsx)(b.ks,{name:"addressL2",id:"addressL2",value:em.addressL2,onChange:eE,required:!1,as:"input",multiline:!1,rows:1,errorMessage:{},validator:()=>!0,pattern:""})]})}),(0,s.jsx)(g.A,{lg:!0,md:2,children:(0,s.jsxs)(j.A.Group,{children:[(0,s.jsx)(j.A.Label,{children:o("City")}),(0,s.jsx)(b.ks,{name:"city",id:"city",value:em.city,onChange:eE,required:!1,as:"input",multiline:!1,rows:1,errorMessage:{},validator:()=>!0,pattern:""})]})}),(0,s.jsx)(g.A,{lg:!0,md:3,children:(0,s.jsxs)(j.A.Group,{children:[(0,s.jsx)(j.A.Label,{className:"required-field",children:o("Country")}),(0,s.jsxs)(b.s3,{name:"country",id:"country",value:em.country,onChange:eE,errorMessage:o("thisfieldisrequired"),required:!0,children:[(0,s.jsx)("option",{value:"",children:o("SelectCountry")}),F.map((e,t)=>(0,s.jsx)("option",{"data-worldregion":e.world_region,value:e._id,children:e.title},t))]})]})}),(0,s.jsx)(g.A,{lg:!0,md:3,children:(0,s.jsxs)(j.A.Group,{style:{maxWidth:"250px"},children:[(0,s.jsx)(j.A.Label,{children:o("CountryRegions")}),(0,s.jsx)(p.MultiSelect,{overrideStrings:{selectSomeItems:o("SelectRegions"),allItemsAreSelected:"All Regions are Selected"},options:J,value:em.region,className:"region",onChange:e=>eP(e,"region"),labelledBy:o("SelectRegions")})]})})]}),(0,s.jsx)(h.A.Text,{children:(0,s.jsx)("b",{children:o("FocalPoints")})}),(0,s.jsx)("hr",{}),(0,s.jsx)(x.A,{className:"mb-3",children:(0,s.jsx)(g.A,{children:(0,s.jsx)(j.A.Group,{children:(0,s.jsxs)(y.A,{activeKey:Y,onSelect:e=>ee(e),id:"uncontrolled-tab-example",children:[(0,s.jsx)(A.A,{eventKey:"",title:(0,s.jsx)("div",{children:(0,s.jsx)("p",{children:o("ExistingUser")})}),children:(0,s.jsx)(x.A,{children:(0,s.jsx)(g.A,{md:!0,lg:4,children:(0,s.jsxs)(j.A.Group,{style:{paddingTop:"20px"},children:[(0,s.jsx)(j.A.Label,{children:o("ExistingUser")}),(0,s.jsx)(p.MultiSelect,{overrideStrings:{selectSomeItems:"Select Users",allItemsAreSelected:"All Users are Selected"},options:Q,onChange:e=>eP(e,"users"),className:"user",value:em.users,labelledBy:o("SelectUsers")})]})})})}),ei.map((e,t)=>(0,s.jsxs)(A.A,{eventKey:`${t+1}`,title:`${o("FocalPoints")} ${t+1}`,children:[(0,s.jsxs)(x.A,{className:"mb-3",children:[(0,s.jsx)(g.A,{lg:4,sm:6,children:(0,s.jsxs)(j.A.Group,{style:{paddingTop:"20px"},children:[(0,s.jsx)(g.A,{className:"p-0",children:(0,s.jsx)(j.A.Label,{children:o("Name")})}),(0,s.jsx)("label",{className:"w-100",children:(0,s.jsx)(b.ks,{name:"username",id:"username",value:e.username,onChange:e=>e$(e,t)})})]})}),(0,s.jsx)(g.A,{lg:4,sm:6,children:(0,s.jsxs)(j.A.Group,{style:{paddingTop:"20px"},children:[(0,s.jsx)(g.A,{className:"p-0",children:(0,s.jsx)(j.A.Label,{children:o("Email")})}),(0,s.jsx)("label",{className:"w-100",children:(0,s.jsx)(b.ks,{name:"email",id:"email",value:e.email,onChange:e=>e$(e,t),pattern:"^[^@]+@[^@]+\\.[^@]+$",errorMessage:{pattern:"Please enter a valid email"}})})]})}),(0,s.jsx)(g.A,{lg:4,sm:6,children:(0,s.jsxs)(f.A,{className:"row ms-0 me-0",children:[(0,s.jsxs)(j.A.Group,{style:{paddingTop:"20px"},className:"institutionDialCode col-5 col-lg-4 ps-0 pe-0",children:[(0,s.jsx)(j.A.Label,{children:o("Telephone")}),(0,s.jsxs)(b.s3,{type:"numbers",name:"focal_dial_code",id:"focal_dial_Code",value:e.focal_dial_code,onChange:e=>e$(e,t),children:[(0,s.jsx)("option",{value:e.focal_dial_code,children:e.focal_dial_code}),F.map((e,t)=>(0,s.jsx)("option",{value:e.dial_code,children:`(${e.dial_code}) ${e.title}`},t))]})]}),(0,s.jsxs)(j.A.Group,{style:{paddingTop:"20px"},className:"institutionTelephone col-7 col-lg-8 pe-0",children:[(0,s.jsx)(j.A.Label,{children:"\xa0"}),(0,s.jsx)(b.ks,{name:"mobile_number",id:"mobile_number",value:e.mobile_number,onChange:e=>e$(e,t)})]})]})}),(0,s.jsx)(g.A,{children:(0,s.jsx)(j.A.Group,{className:"m-0",children:(0,s.jsx)("input",{name:"institution",id:"institution",value:em.title,type:"hidden"})})})]}),(0,s.jsx)(x.A,{children:0===t?(0,s.jsx)("span",{}):(0,s.jsx)(g.A,{xs:!0,lg:"4",children:(0,s.jsx)(v.A,{onSelect:e=>ee(e),variant:"secondary",onClick:e=>eU(e,t),children:o("Remove")})})})]})),(0,s.jsx)(A.A,{title:(0,s.jsx)("div",{children:(0,s.jsxs)("span",{onClick:eM,children:[" ",(0,s.jsx)("p",{children:o("AddnewFocalPoint")})]})})})]})})})}),(0,s.jsx)(h.A.Text,{children:(0,s.jsx)("b",{children:o("MoreInfo")})}),(0,s.jsxs)(x.A,{className:"mb-3",children:[(0,s.jsx)(g.A,{lg:!0,md:4,children:(0,s.jsxs)(j.A.Group,{children:[(0,s.jsx)(j.A.Label,{children:o("OrganisationType")}),(0,s.jsxs)(b.s3,{name:"organisationType",id:"organisationType",value:null===em.organisationType?"":em.organisationType,onChange:eE,required:!1,errorMessage:"",children:[(0,s.jsx)("option",{value:"",children:o("SelectOrganisationType")}),es.map((e,t)=>(0,s.jsx)("option",{value:e._id,children:e.title},t))]})]})}),(0,s.jsx)(g.A,{lg:!0,md:4,children:(0,s.jsxs)(j.A.Group,{children:[(0,s.jsx)(j.A.Label,{children:o("Network")}),(0,s.jsx)(p.MultiSelect,{overrideStrings:{selectSomeItems:o("SelectNetwork"),allItemsAreSelected:"All Networks are Selected"},options:eo,value:K,onChange:e=>{V(e)},className:"net-work",labelledBy:o("SelectNetwork")}),(0,s.jsx)("span",{children:eq})]})}),(0,s.jsx)(g.A,{lg:!0,md:4,children:(0,s.jsxs)(j.A.Group,{children:[(0,s.jsx)(j.A.Label,{children:o("Expertise")}),(0,s.jsx)(p.MultiSelect,{overrideStrings:{selectSomeItems:o("SelectExpertise"),allItemsAreSelected:"All Expertise are Selected"},options:en,value:W,onChange:e=>{H(e)},className:"expertise",labelledBy:o("SelectExpertise")}),eR]})})]}),(0,s.jsxs)(x.A,{className:"mb-3",children:[(0,s.jsx)(g.A,{md:!0,lg:6,children:(0,s.jsxs)(j.A.Group,{style:{maxWidth:"600px"},children:[(0,s.jsx)(j.A.Label,{children:o("ExpertisewithHazards")}),(0,s.jsx)(p.MultiSelect,{overrideStrings:{selectSomeItems:o("ExpertisewithHazards"),allItemsAreSelected:"All Hazards are Selected"},options:ec,filterOptions:(e,t)=>{if(!t)return e;let i=RegExp(`^${t}`,"gi");return e.filter(({label:e})=>e&&e.match(i))},onChange:e=>{B(e)},value:O,className:"partners",labelledBy:o("SelectPartners")})]})}),(0,s.jsx)(g.A,{md:!0,lg:6,children:(0,s.jsxs)(j.A.Group,{style:{maxWidth:"600px"},children:[(0,s.jsx)(j.A.Label,{children:o("Partners")}),(0,s.jsx)(p.MultiSelect,{overrideStrings:{selectSomeItems:o("SelectPartners"),allItemsAreSelected:"All Partners are Selected"},options:ej,onChange:e=>{eG(e)},value:ez,className:"partners",labelledBy:o("SelectPartners")})]})})]}),(0,s.jsxs)(x.A,{className:"mb-3",children:[(0,s.jsx)(g.A,{sm:12,md:6,lg:6,children:(0,s.jsxs)(j.A.Group,{children:[(0,s.jsx)(j.A.Label,{children:o("Department")}),(0,s.jsx)(b.ks,{name:"department",id:"department",value:em.department,onChange:eE,required:!1,as:"input",multiline:!1,rows:1,errorMessage:{},validator:()=>!0,pattern:""})]})}),(0,s.jsx)(g.A,{sm:12,md:6,lg:6,children:(0,s.jsxs)(j.A.Group,{children:[(0,s.jsx)(j.A.Label,{children:o("Unit")}),(0,s.jsx)(b.ks,{name:"unit",id:"unit",value:em.unit,onChange:eE,required:!1,as:"input",multiline:!1,rows:1,errorMessage:{},validator:()=>!0,pattern:""})]})})]}),(0,s.jsx)(h.A.Text,{children:(0,s.jsx)("b",{children:o("MediaGallery")})}),(0,s.jsx)(x.A,{children:(0,s.jsx)(g.A,{children:(0,s.jsx)(S.A,{index:2,datas:q,srcText:G,getImgID:(e,t)=>eW(e,t),getImageSource:e=>eH(e)})})}),(0,s.jsx)(x.A,{className:"my-4",children:(0,s.jsxs)(g.A,{children:[(0,s.jsx)(v.A,{className:"me-2",type:"submit",variant:"primary",ref:i,children:o("submit")}),(0,s.jsx)(v.A,{className:"me-2",onClick:()=>{eh(T),z([]),M([]),$(null),ea([]),H([]),V([]),el([]),eu([]),et([]),B([]),eG([]),window.scrollTo(0,0)},variant:"info",children:o("reset")}),(0,s.jsx)(u(),{href:"/institution",as:"/institution",children:(0,s.jsx)(v.A,{variant:"secondary",children:o("Cancel")})})]})})]})})})})};a()}catch(e){a(e)}})},66994:(e,t,i)=>{i.d(t,{A:()=>o});var a=i(8732),s=i(82015),l=i(43294),n=i(18622);let r=(0,s.forwardRef)((e,t)=>{let{children:i,onSubmit:s,autoComplete:r,className:o,onKeyPress:d,initialValues:c,...u}=e,p=n.object().shape({});return(0,a.jsx)(l.Formik,{initialValues:c||{},validationSchema:p,onSubmit:(e,t)=>{let i={preventDefault:()=>{},stopPropagation:()=>{},currentTarget:null,target:null,nativeEvent:new Event("submit"),bubbles:!1,cancelable:!0,defaultPrevented:!1,eventPhase:0,isTrusted:!1,timeStamp:Date.now(),type:"submit",isDefaultPrevented:()=>!1,isPropagationStopped:()=>!1,persist:()=>{}};s&&s(i,e,t)},...u,children:e=>(0,a.jsx)(l.Form,{ref:t,onSubmit:e.handleSubmit,autoComplete:r,className:o,onKeyPress:d,children:"function"==typeof i?i(e):i})})});r.displayName="ValidationFormWrapper";let o=r},98178:(e,t,i)=>{i.a(e,async(e,a)=>{try{i.d(t,{A:()=>_});var s=i(8732),l=i(82015),n=i(16029),r=i(82053),o=i(54131),d=i(49481),c=i(59549),u=i(91353),p=i(12403),m=i(27825),h=i.n(m),x=i(42893),g=i(63487),j=i(88751),f=e([o,x,g]);[o,x,g]=f.then?(await f)():f;let y=[],A={flex:1,display:"flex",flexDirection:"column",alignItems:"center",justifyContent:"center",width:"100%",height:"100%",borderWidth:.1,borderColor:"#fafafa",backgroundColor:"#fafafa",color:"black",transition:"border  .24s ease-in-out",padding:"15px"},v={display:"flex",padding:"10px",width:"100%",border:"2px solid gray",flexDirection:"column",justifyContent:"flex-start",flexWrap:"wrap",marginTop:20},b={width:"150px"},w={borderColor:"#2196f3"},_=e=>{let t,{t:i}=(0,j.useTranslation)("common"),[a,m]=(0,l.useState)(!1),[f,_]=(0,l.useState)(),S="application"==e.type?0x1400000:"20971520",[C,I]=(0,l.useState)([]),[N,k]=(0,l.useState)(!0),[L,T]=(0,l.useState)([]),D=e&&"application"===e.type?"/files":"/image",E=async e=>{await g.A.remove(`${D}/${e}`)},P=e=>{_(e),m(!0)},F=(e,t)=>{let i=[...L];i[t]=e.target.value,T(i)},R=t=>{switch(t&&t.name.split(".").pop()){case"JPG":case"jpg":case"jpeg":case"jpg":case"png":return(0,s.jsx)("img",{src:t.preview,style:b});case"pdf":return(0,s.jsx)("img",{src:"/images/fileIcons/pdfFile.png",className:"application"===e.type?"docPreview":"imgPreview"});case"docx":default:return(0,s.jsx)("img",{src:"/images/fileIcons/wordFile.png",className:"application"===e.type?"docPreview":"imgPreview"});case"xls":case"xlsx":return(0,s.jsx)("img",{src:"/images/fileIcons/xlsFile.png",className:"application"===e.type?"docPreview":"imgPreview"})}},q=()=>m(!1),z=()=>{m(!1)},G=t=>{let i=(t=f)&&t._id?{serverID:t._id}:{file:t},a=h().findIndex(y,i),s=[...L];s.splice(a,1),T(s),E(y[a].serverID),y.splice(a,1),e.getImgID(y,e.index?e.index:0);let l=[...C];l.splice(l.indexOf(t),1),I(l),m(!1)},M=C.map((t,l)=>(0,s.jsxs)("div",{children:[(0,s.jsx)(d.A,{xs:12,children:(0,s.jsxs)("div",{className:"row",children:[(0,s.jsx)(d.A,{md:4,lg:3,className:"application text-center align-self-center"===e.type?"docImagePreview text-center align-self-center":"imgPreview text-center align-self-center",children:R(t)}),(0,s.jsx)(d.A,{md:5,lg:7,className:"align-self-center",children:(0,s.jsxs)(c.A,{children:[(0,s.jsxs)(c.A.Group,{controlId:"filename",children:[(0,s.jsx)(c.A.Label,{className:"mt-2",children:i("FileName")}),(0,s.jsx)(c.A.Control,{size:"sm",type:"text",disabled:!0,value:t.original_name?t.original_name:t.name})]}),(0,s.jsxs)(c.A.Group,{controlId:"description",children:[(0,s.jsx)(c.A.Label,{children:"application"===e.type?i("ShortDescription/(Max255Characters)"):i("Source/Description")}),(0,s.jsx)(c.A.Control,{maxLength:"application"===e.type?255:void 0,size:"sm",type:"text",placeholder:"application"===e.type?i("`Enteryourdocumentdescription`"):i("`Enteryourimagesource/description`"),value:L[l],onChange:e=>F(e,l)})]})]})}),(0,s.jsx)(d.A,{md:3,lg:2,className:"align-self-center text-center",onClick:()=>P(t),children:(0,s.jsx)(u.A,{variant:"dark",children:i("Remove")})})]})}),(0,s.jsxs)(p.A,{show:a,onHide:q,children:[(0,s.jsx)(p.A.Header,{closeButton:!0,children:(0,s.jsx)(p.A.Title,{children:i("DeleteFile")})}),(0,s.jsx)(p.A.Body,{children:i("Areyousurewanttodeletethisfile?")}),(0,s.jsxs)(p.A.Footer,{children:[(0,s.jsx)(u.A,{variant:"secondary",onClick:z,children:i("Cancel")}),(0,s.jsx)(u.A,{variant:"primary",onClick:()=>G(t),children:i("yes")})]})]})]},l));(0,l.useEffect)(()=>{C.forEach(e=>URL.revokeObjectURL(e.preview)),y=[]},[]),(0,l.useEffect)(()=>{e.getImageSource(L)},[L]),(0,l.useEffect)(()=>{T(e.srcText)},[e.srcText]),(0,l.useEffect)(()=>{if(e&&"true"===e.singleUpload&&k(!1),e&&e.datas){let t=e.datas.map((t,i)=>(y.push({serverID:t._id,index:e.index?e.index:0,type:t.name.split(".")[1]}),{...t,preview:`http://localhost:3001/api/v1/image/show/${t._id}`}));I([...t])}},[e.datas]);let U=async(t,i)=>{if(t.length>i)try{let a=new FormData;a.append("file",t[i]);let s=await g.A.post(D,a,{"Content-Type":"multipart/form-data"});y.push({serverID:s._id,file:t[i],index:e.index?e.index:0,type:t[i].name.split(".")[1]}),U(t,i+1)}catch(e){U(t,i+1)}else e.getImgID(y,e.index?e.index:0)},$=(0,l.useCallback)(async e=>{await U(e,0);let t=e.map(e=>Object.assign(e,{preview:URL.createObjectURL(e)}));N?I(e=>[...e,...t]):I([...t])},[]),{getRootProps:O,getInputProps:B,isDragActive:W,isDragAccept:H,isDragReject:K,fileRejections:V}=(0,n.useDropzone)({accept:e&&e.type?"application/pdf, application/vnd.openxmlformats-officedocument.wordprocessingml.document, application/vnd.oasis.opendocument.text,application/vnd.openxmlformats-officedocument.spreadsheetml.sheet,text/csv":"image/*",multiple:N,minSize:0,maxSize:S,onDrop:$,validator:function(e){if("/image"===D){if("image"!==e.type.substring(0,5))return x.default.error(i("toast.filetypenotsupport")),{code:"file-invalid-type",message:"File type not supported"}}else if("/files"===D&&"image"===e.type.substring(0,5))return x.default.error(i("toast.filetypenotsupport")),{code:"file-invalid-type",message:"File type not supported"};return null}}),J=(0,l.useMemo)(()=>({...A,...W?w:{outline:"2px dashed #bbb"},...H?{outline:"2px dashed #595959"}:{outline:"2px dashed #bbb"},...K?{outline:"2px dashed red"}:{activeStyle:w}}),[W,K]);t=e&&"application"===e.type?(0,s.jsx)("small",{style:{color:"#595959"},children:i("DocumentWeSupport")}):(0,s.jsx)("small",{style:{color:"#595959"},children:i("ImageWeSupport")});let Z=V.length>0&&V[0].file.size>S;return(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)("div",{className:" d-flex justify-content-center align-items-center mt-3",style:{width:"100%",height:"180px"},children:(0,s.jsxs)("div",{...O({style:J}),children:[(0,s.jsx)("input",{...B()}),(0,s.jsx)(r.FontAwesomeIcon,{icon:o.faCloudUploadAlt,size:"4x",color:"#999"}),(0,s.jsx)("p",{style:{color:"#595959",marginBottom:"0px"},children:i("Drag'n'dropsomefileshere,orclicktoselectfiles")}),!N&&(0,s.jsxs)("small",{style:{color:"#595959"},children:[(0,s.jsx)("b",{children:"Note:"})," One single image will be accepted"]}),t,(e.type,Z&&(0,s.jsxs)("small",{className:"text-danger mt-2",children:[(0,s.jsx)(r.FontAwesomeIcon,{icon:o.faExclamationCircle,size:"1x",color:"red"})," ",i("FileistoolargeItshouldbelessthan20MB")]})),K&&(0,s.jsxs)("small",{className:"text-danger",style:{color:"#595959"},children:[(0,s.jsx)(r.FontAwesomeIcon,{icon:o.faExclamationCircle,size:"1x",color:"red"})," ",i("Filetypenotacceptedsorr")]})]})}),C.length>0&&(0,s.jsx)("div",{style:v,children:M})]})};a()}catch(e){a(e)}})}};