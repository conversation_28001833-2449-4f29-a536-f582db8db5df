"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/_app",{

/***/ "(pages-dir-browser)/./components/navigation/NavMenuList.tsx":
/*!***********************************************!*\
  !*** ./components/navigation/NavMenuList.tsx ***!
  \***********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   canAccessPages: () => (/* binding */ canAccessPages),\n/* harmony export */   \"default\": () => (/* binding */ SideBar)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(pages-dir-browser)/./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _NavMenuItem__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./NavMenuItem */ \"(pages-dir-browser)/./components/navigation/NavMenuItem.tsx\");\n/* harmony import */ var redux_auth_wrapper_connectedAuthWrapper__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! redux-auth-wrapper/connectedAuthWrapper */ \"(pages-dir-browser)/./node_modules/redux-auth-wrapper/connectedAuthWrapper.js\");\n//Import services/components\n\n\n\nconst canAccessPages = (0,redux_auth_wrapper_connectedAuthWrapper__WEBPACK_IMPORTED_MODULE_2__[\"default\"])({\n    authenticatedSelector: (state, props)=>{\n        if (state.permissions && state.permissions[props.item.id] && state.permissions[props.item.id]['read:any']) {\n            if (state.user && state.user.is_focal_point) {\n                return state.user.status == \"Approved\" ? true : false;\n            }\n            if (state.user && state.user.is_vspace) {\n                return state.user.vspace_status == \"Approved\" ? true : false;\n            }\n            return true;\n        }\n        return false;\n    },\n    wrapperDisplayName: 'CanAccessPages'\n});\nfunction SideBar(props) {\n    const { items } = props;\n    const CanAccessPages = canAccessPages((PageProps)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_NavMenuItem__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n            item: PageProps.item\n        }, void 0, false, {\n            fileName: \"D:\\\\rki\\\\frontend\\\\components\\\\navigation\\\\NavMenuList.tsx\",\n            lineNumber: 28,\n            columnNumber: 61\n        }, this));\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n        children: [\n            \"bla\",\n            items.map((item, index)=>{\n                console.log(items);\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(CanAccessPages, {\n                    item: item\n                }, index, false, {\n                    fileName: \"D:\\\\rki\\\\frontend\\\\components\\\\navigation\\\\NavMenuList.tsx\",\n                    lineNumber: 35,\n                    columnNumber: 16\n                }, this);\n            })\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\rki\\\\frontend\\\\components\\\\navigation\\\\NavMenuList.tsx\",\n        lineNumber: 30,\n        columnNumber: 5\n    }, this);\n}\n_c = SideBar;\nvar _c;\n$RefreshReg$(_c, \"SideBar\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-browser)/./components/navigation/NavMenuList.tsx\n"));

/***/ })

});