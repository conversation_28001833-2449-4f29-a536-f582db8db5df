{"version": 3, "file": "static/chunks/46dafc95-65c8a19c211ddb65.js", "mappings": "mpBAkDA,eACA,iBACA,uEAEA,QACA,gBACA,YACA,cACA,WACA,EACA,IACA,cACA,YACA,sBACA,UACA,eACA,EAEA,mCACA,aACC,EACD,aAAmB,EAAE,WAAgB,EAAE,OAAc,EACrD,QAAa,IACb,KAAc,IAoBd,SAAmB,EAAE,WAAiB,CAAC,SAAe,MAAgB,YAAkB,eAExF,GADA,2CAEA,YAEA,kDAA2F,IAAa,IACxG,oBAEA,OAAW,eAAqB,OAAO,IAAS,gBAEhD,CAAC,IACD,aAAmB,EAAE,SAAe,2BAA6B,SAAc,EAG/E,QACA,YACA,YACA,WACA,SACA,EACA,iBACA,MAAqB,SAAa,YAClC,OAAS,SAAa,0BACtB,CACA,iBACA,MAAmB,OAAW,YAC9B,OAAS,OAAW,0BACpB,CACA,iBAIA,IAHA,cACA,UACA,KACS,KAAS,aAClB,UACA,EAAc,KAAS,YAEvB,QACA,CACA,iBACA,MAAc,SAAa,MAC3B,OAAS,IAAQ,QAAwB,KAAS,OAClD,CACA,iBAIA,IAHA,mEACA,IACA,KACS,KAAS,SAClB,UACA,EAAc,KAAS,QAEvB,QACA,CACA,wBACA,uBACA,sBACA,sBACA,EAAS,SAAa,UACtB,EAAS,OAAW,GAAO,OAAW,KACtC,EAAS,SAAa,GAAO,SAAa,KAC1C,EAAS,SAAa,GAAO,SAAa,KACjC,cAAkB,GAAO,cAAkB,KACpD,CACA,eACA,OAAS,WAAW,KAA6B,IAAb,SAAa,KAA6B,IAAb,SAAa,KAAkC,IAAlB,cAAkB,GAChH,CASA,0BACA,sBAKA,sBAA8B,OAAa,cAA+B,OAAa,cALvF,aAMA,CAEA,OAAqB,aAAmB,EAAE,WAAgB,EAAE,OAAc,EAC1E,uBACA,oDAEA,OADE,IAAS,+FACX,CACA,CAUA,mBACA,mEACA,CACA,iBACA,kDACA,CAIA,iBACA,gCACA,CACA,eACA,MAAiB,aAAO,UACxB,gCACA,CAGA,iBACA,MAAS,QAAE,WACX,CACA,mBAEA,MAD0B,QAAE,gBACC,SAAG,gBAAyB,QAAE,eAC3D,CACA,qBAzDA,IA0DA,MAzDA,QADA,EA0DA,QAzDA,WACA,SAEE,CAAK,IAsDP,EAtDO,OAJP,QAME,CAAK,IAoDP,EApDO,UAqDP,CAGA,eACA,aACA,UACA,QACA,WACA,SACA,UACA,QACA,WACA,GAAmB,YAAO,UAAmB,aAAO,UACpD,UACA,UACA,UAEA,KAEA,SAEA,KAEA,GAEA,CACA,eACA,cACA,UACA,QACA,UACA,UACA,QACA,EAAe,aAAO,UACtB,EAAwB,SAAG,YAG3B,EADgB,SAAG,gBACc,QAAE,gBAA+B,SAAG,gBACrE,WACA,CAKA,iBACA,MAAS,QAAE,WACX,CACA,iBACA,mBACA,CACA,OAAiC,OAAY,eAC7C,WACE,OAAe,SACf,IAAS,8EACT,IAAS,wFACX,6BACA,uBACA,uBACA,0CAAwE,IAAa,IACrF,kBAEA,8CACA,EAEA,+BACA,uBACA,wBAAiC,SAAS,CAC1C,cAAuB,IAAE,CACzB,gBAAyB,KAAG,CAC5B,cAAuB,IAAE,CACzB,gBAAyB,KAAG,CAC5B,cAAuB,IAAE,CACzB,gBAAyB,KAAG,CAC5B,wBAAiC,SAAO,CACxC,oBAA6B,OAAK,CAClC,gBAAyB,KAAG,CAC5B,uBACA,qBACA,qBACA,gBAAyB,KAAG,CAC5B,gBAAyB,KAAG,CAC5B,wBAAiC,SAAO,CACxC,2BACA,2CACA,yCACA,mCACA,mCACA,wDACA,4BACA,EACA,qCACA,mCACA,yDACA,yCACA,yCACA,iCACA,qCACA,iCACA,yDACA,0DACA,CAAC,EAcD,eACA,aACA,MACI,OAAe,SACnB,0CAAwE,IAAa,IACrF,kBASA,MANA,CADA,EAAY,OAAU,uBACtB,qBACA,qBACA,EACA,mBACA,iBACA,EACA,CACA,CAEA,MADE,OAAS,MACF,OAAY,KACrB,aACA,iBACA,iBACA,uBACA,UACA,OAA0B,eAAmB,QAC7C,uBACA,CAAO,CAAe,eAAmB,SACzC,yBACA,CAAO,CAAe,eAAmB,WACzC,cACA,yCACA,CAAO,UAAgC,eAAmB,WAC1D,cACA,4CACA,CAAO,aAAmC,eAAmB,WAC7D,cACA,wCACA,CAAO,UAAgC,eAAmB,SAC1D,6BACA,CAAO,IAAuB,eAAmB,SACjD,yBACA,CAAO,yBACP,CACA,CAAG,EACH,qBACA,kBACA,WACA,mBACA,kBACA,cACA,yBACA,OAA8B,eAAmB,WACjD,cACA,MACA,UAAuB,OAAI,EAC3B,kBACA,CAAa,EACb,2BACA,CAAW,MACX,CAAS,CAET,CACA,CAAG,EACH,CAAC,CAAC,WAAe,EAEjB,iBACA,6BACA,CAEA,QACA,YACA,YACA,cACA,iBACA,YACA,sBACA,UACA,cACA,gBACA,YACA,sBACA,oBACA,cACA,gBACA,qDACA,qBACA,2BACA,CACA,EAqBA,iNACA,eACA,aAEA,MADI,OAAe,SACR,OAAU,kBACrB,CAEA,MADE,OAAS,MACF,OAAY,KACrB,aACA,iBACA,iBACA,UACA,cACA,UACA,aACA,aACA,aACA,kBACA,eACA,cACA,mBACA,mBACA,cACA,YACA,aACA,eACA,UACA,iBACA,cACA,YACA,EAAgB,OAAwB,MACxC,oBACA,iBACA,eACA,WACA,aACA,cACA,0CACA,uBACA,EAAiC,eAAmB,QACpD,8BACA,eACA,CAAO,GAAuB,eAAmB,IACjD,QACA,iBACA,iBACA,QACA,WACA,YACA,YACA,SACA,CAAO,KACP,OAA0B,eAAmB,mBAA+B,aAC5E,WACA,CAAO,EAAgB,eAAmB,uBAAwB,IAClE,MAAe,OAAa,CAAC,OAAa,GAAG,aAC7C,UAAmB,OAAI,4BACvB,iBACA,qBACA,8BACA,6BACA,CAAS,EACT,oBACA,gBACA,CAAS,CACT,0BACA,gBACA,CAAS,CACT,sBACA,gBACA,CACA,CAAO,+BACP,CACA,CAAG,EACH,CAAC,CAAC,WAAe,EAEjB,uBACA,cACS,IAAS,IAClB,CACA,iBAGA,MAFA,iBACA,CAEA,CACA,EAPkB,OAOlB,YACA,cACA,8DACA,CA+FA,eACA,IAxMA,EACA,EACA,EAsMA,iBACA,cACA,YACA,aACA,eACA,cACA,aACA,SACA,WACA,cACA,YACA,aACA,kBACA,eACA,oBACA,cACA,WACA,WAxNA,GADA,EA0NA,CACA,MACA,UACA,CAAG,EA5NH,IACA,aACE,eAAS,YACX,kBACA,0CACA,GAEA,EAEA,OADA,yCACA,WACA,2CACA,CACA,CAAG,QAiND,qBAAe,YACjB,IAvDA,EACA,EACA,EACA,EACA,EACA,EACA,EACA,EACA,EACA,EACA,EACA,EACA,EACA,EACA,EACA,EACA,EACA,EAKA,EACA,EAgCA,GAtDA,GADA,EAuDA,CACA,SACA,SACA,oBACA,cACO,EA3DP,OACA,WACA,cACA,QAEA,GADA,EAAmB,OAAS,KAC5B,IACA,SACA,UACA,WAEA,GADA,EAAoB,OAAS,KAC7B,IACA,SACA,UACA,WAEA,GADA,EAAoB,OAAS,KAC7B,MACA,WAKA,MACA,MAGA,CACA,UAHA,EAJA,EAFA,IAMA,YAIA,WAPA,IAFA,IAMA,WAIA,GAgCA,cACA,eACA,sCACA,sCAEA,CAAG,cACH,cAIA,OAAsB,eAAmB,QACzC,MAJA,CACA,cACA,EAGA,wBACA,KACA,CAAG,CAAe,eAAmB,QACrC,8BACA,CAAG,oDACH,OAAwB,eAAmB,KAC3C,MACA,aACA,YACA,QACA,UACA,WACA,YACA,aACA,gBACA,aACA,sCACA,yCACA,YACA,UACA,iBACA,aACA,uBACA,WACA,CAAO,CACP,qBACA,UACA,CACA,CAAK,CACL,CAAG,EACH,CACA,OAAyB,YAAgB,eACzC,OAAsB,eAAmB,oBAAsB,IAC/D,WACA,CAAG,EACH,CAAC,EAuBD,eACA,qBACA,gBACA,iBACA,YACA,cACA,cACA,eACA,YACA,aACA,sBACA,2BACA,wBACA,oBACA,WACA,mBACA,EAAkB,YAAM,OACxB,2BACA,OACA,WACA,IACA,IACA,GACA,GAEA,iBACA,WACA,SACA,QACA,OAAsB,eAAmB,CAAC,GAAO,EACjD,aACA,QACA,QACA,mBACA,SACA,gBACG,aACH,cACA,OAAwB,eAAmB,oBAAwB,IACnE,eACA,MACA,gBACA,SACA,YACA,UACA,WACA,aACA,YACA,WACA,OACA,SACA,YACA,UACA,WACA,gBACA,aACA,iBACA,CAAK,EACL,CAAG,CACH,CAjFA,cACA,UAAa,WAAgB,WAC7B,QAAW,WAAgB,WAC3B,SAAY,WACZ,WAAc,WAAgB,WAC9B,UAAa,WAAgB,WAC7B,SAAY,WAAgB,WAC5B,KAAQ,SAAc,WACtB,OAAU,UAAe,WACzB,UAAa,cAAoB,kBACjC,QAAW,cAAoB,OAC/B,SAAY,SAAc,cACT,SAAc,WACjB,SACd,gBAAmB,SAAc,MACxB,WAAgB,OACf,SAAe,EACzB,EAAO,WACP,EAAO,WAAgB,CAEvB,EA8DA,OAA8B,YAAgB,eAC9C,OAAsB,eAAmB,oBAA6B,IACtE,cACA,CAAG,EACH,CAAC,EAyBD,iBACA,sEACA,MAAS,OAAM,QACf,UACA,CAAG,CACH,CAgBA,qBAXA,EACA,EACA,EAUA,OAZA,YACA,YACA,mCACS,OAAO,gBAShB,EACA,CAIA,eACA,QAIA,OAHA,6BACA,iBAEA,CACA,kBACA,kBACA,cACA,cAEA,CA9DA,cACA,YAAe,aAAmB,EAAE,WAAgB,IAAE,KAAe,EACrE,EAAO,WACP,EAAO,WAAgB,EACpB,EACH,QAAW,SAAe,EAC1B,SAAc,WAAgB,OAClB,UAAe,KACjB,cAAoB,OAC9B,IAAS,cAAoB,MAC7B,CAAG,EACH,UAAa,WAAgB,WAC7B,UAAa,WAAgB,WAC7B,WAAc,WAAgB,WAC9B,QAAW,WAAgB,WAC3B,SAAY,WAAgB,kBACP,SAAc,uBACT,SAAc,oBACjB,SAAc,gBAClB,SACnB,OAAU,SAAc,eACN,QAClB,CADgC,CA4ChC,OAkCS,OAAY,CAjCrB,cACA,+DAAsF,CACtF,WAEA,uBAEA,oBAEI,OAAe,SACnB,wBACA,kBACA,mBACA,iBACA,qBAVA,cAWA,wBATA,iBAUA,qBARA,gBASA,oCACA,6DACA,uDACA,qEACA,+CACA,uEACA,+EAIA,+DAAsF,SACtF,4DACA,wDACA,6EACA,yFACA,+BACA,EACqB,EACrB,SACA,oBACA,kDAEA,OADA,UACA,CACA,kBACA,kBACA,sBACA,CACA,CACA,CACA,CAAG,EACH,WACA,kBACA,QAGA,EAHA,0CAA6F,IAAa,IAC1G,oBAOA,MAJA,yBACA,oBACA,iCACA,CAAO,EACP,CACA,CACA,CAAG,EACH,eACA,iBACA,wBACA,4BACA,sBACA,kBACA,yBACA,mBACA,oCACA,2EACA,qEACA,mDACA,2CACA,qDACA,uDACA,2DACA,2EACA,kFACA,CACA,CAAG,EACH,iBACA,kBACA,6BACA,uBACA,WACA,CACA,CAAG,EACH,aACA,yBACA,kBAGA,eACA,+BADA,EAEA,CAIA,CAAG,EACH,4BACA,oBACA,WACA,OACA,OACA,OACA,cACA,wBACA,IACA,IACA,CAAS,uBACT,4BACA,UACA,CAAS,EACT,2BACA,UACA,CAAS,CACT,EACA,qBACA,aACA,GACA,gBAEA,GACA,IAEA,GACA,IAEA,OACA,OACA,MACA,EAIA,OAHA,GACA,KAEA,WACA,IACA,GACA,CACA,CAIA,CAAG,EACH,+BACA,iBACA,WACA,6BACA,gCACA,yBACA,mEACA,CAAO,EACP,8BACA,gCACA,8EACA,CAAO,CACP,6CACA,IACA,GACA,CACA,CACA,CAAG,EACH,+BACA,kBACA,YACA,UACA,UACA,YACA,YACA,6BACA,IACA,IACA,UACA,SACA,CAAO,EACP,kBACA,CACA,CAAG,EACH,mCACA,kBACA,YACA,UACA,UACA,YACA,YACA,iCACA,IACA,IACA,UACA,SACA,CAAO,EACP,kBACA,CACA,CAAG,EACH,0BACA,kBAEA,GADA,sBACA,iBAGA,IAOA,EAPA,QACA,YACA,YACA,UACA,UACA,mBAKA,8BApPA,EAoPA,GApPuB,OAAQ,CAoP/B,EApP+B,0BAoP/B,KApP+B,GAqP/B,0BAAwC,OAAQ,cAChD,iBA8KA,+DAOA,MANM,kBAAO,SACb,MACA,OACA,QACA,QACA,GACA,CACA,EAtLA,GACA,QACA,SACA,WACA,UAWA,IATA,IACA,IAFA,UAEA,MACA,cACA,kBACA,eACA,CAAS,EACT,MACA,MACA,CAAS,EACT,MACA,CAQA,QAPA,iDACA,8BACA,IACA,IACA,UACA,SACA,CAAO,EAEP,eACA,gBACA,mEACA,+DACA,+DACA,KACA,kBACA,yBACA,oEACA,8DAEA,GACA,CAIA,CAAG,EACH,8BACA,kBACA,eACA,6BACA,mBAGA,mBACA,oBACA,CAAO,CACP,CACA,CAAG,EACH,8BACA,kBACA,qBACA,mBAWA,GATA,4BACA,uBAEA,kBACA,mDACA,qDACA,sBACA,wBACA,4BACA,GACA,uBAAsC,OAAQ,4BAC9C,wCACA,oBAGA,MACA,0BAIA,wBACA,mBARA,mBASA,CACA,CAAG,EACH,wBACA,kBACA,YACA,UACA,UACA,YACA,YACA,8BACA,qDAhTA,KAkTA,yBACA,yBACA,IACA,IACA,UACA,SACA,CAAS,IAIT,qBACA,WACA,EACA,mBACA,IACA,IACA,UACA,SACA,CAAO,EACP,CACA,CAAG,EACH,uBACA,kBACA,oDAGA,6BACA,MACA,MACA,QACA,UACA,UACA,gBACA,gBACA,gBACA,gBACA,iBACA,oBAGA,gBAGA,MACA,gDAEA,IACA,kBACA,kBACA,MACA,OACA,IACA,IACA,UACA,UACA,EACA,yCAEA,oBACA,CACA,CAAG,EACH,mBACA,kBACA,8BACA,CACA,CAAG,EACH,cACA,oBACA,6BACA,MACA,MAEA,OADA,WA1XA,GA2XA,+BACA,CACA,CAAG,GAwBH,iBACA,+DACA,QACA,QACA,SACA,UAEA,WAEA,QACA,QACA,SACA,UAEA,WAEA,OAGA,CAXA,iBAWA,KAEA,IANA,kBATA,iBAiBA,KAEA,IAZA,gBAYA,CACA,CAOA,eACA,qCACA,gCACA,oBACA,kBACA,OACA,MACA,OACA,2BACA,4BACA,CACA,CACA,qBACA,2DACA,+DACA,CAEA,mBACA,gBACA,MAOA,MANI,OAAe,SACnB,GAAY,OAAU,gBACtB,OACA,YACA,EACA,eAAsC,eAAS,GAC/C,CACA,CAEA,MADE,OAAS,MACF,OAAY,KACrB,wBACA,iBACA,yCACA,CACA,CAAG,EACH,2BACA,iBACA,0BACA,CACA,CAAG,EACH,yBACA,kBACA,yDACA,gEACA,CACA,CAAG,EACH,aACA,iBACA,iBACA,UACA,WACA,YACA,SACA,+BACA,cACA,aACA,cACA,aACA,WACA,MACA,OAA0B,eAAmB,QAC7C,uBACA,sBACO,qBAEP,mBACA,cACA,UACA,OAA4B,eAAmB,IAC/C,MACA,QACA,OACA,CAAS,CAAe,eAAmB,QAC3C,QACA,UAAqB,OAAI,gBAVzB,eAUyB,6FACzB,CAAS,EACT,CAAO,EACP,CACA,CAAG,EACH,kBACA,iBACA,WACA,4BACA,8CACA,gDACA,CAAO,EACP,gBACA,cApiBA,YACA,EAmiBA,EAniBA,QACA,kCACS,OAAO,oBAiiBhB,KACA,IAriBA,EACA,EACA,EA5SA,EACA,EA80BA,QACA,UACA,UACA,QACA,GAn1BA,MAEA,CADA,QACA,kCAi1BA,EAj1BA,MAi1BA,CACA,2BACA,eACA,WACA,SACA,SACA,KACA,CAAa,CACb,CACA,CACA,cACA,YACA,YACA,CAAS,CACT,EACA,6BACA,cACA,UACA,QACA,KACA,KAQA,GAPA,oBACA,8BACA,YACA,MACA,KACA,GAEA,iBACA,IA52BA,IACA,EACA,EACA,EACA,EAGA,EAIA,EACA,EAGA,EACA,EA41BA,QACA,GA72BA,EA62BA,WA72BA,EA62BA,SA52BA,KACA,KACA,MACA,KAy2BA,EAz2BA,GAGA,KAs2BA,EAt2BA,IAs2BA,EAt2BA,GAIA,EAk2BA,EAl2BA,sBACA,0BAGA,eACA,YA61BA,EA51BA,WA41BA,EA51BA,kBAIA,IACA,KAEA,IACA,GACA,IACA,KACM,IACN,IACA,MAGA,IAEA,mDA00BA,EA10BA,SACA,EACA,QAAyD,IACnD,IAu0BN,EAv0BM,EAGN,GAJoF,CAOpF,KAGA,CACA,WACA,QACA,GA4zBA,aACA,WAEA,YACA,aACA,WACA,QACA,CAAS,CACT,CAAO,EACP,gCACA,uCACA,mCACA,CAAO,EACP,yBACA,mBACA,CAAO,EACP,+BACA,yBACA,CAAO,EACP,0BACA,cAA2B,OAAa,CAAC,OAAa,GAAG,YAAmB,EAC5E,gBACA,QACA,CAAS,GACT,cACA,YACA,YACA,CAAS,EACT,iCACA,CAAO,CACP,CACA,CAAG,EACH,0BACA,iBACA,iBACA,0BACA,oBACA,CACA,CAAG,EACH,kBACA,kBACA,eACA,aACA,WACA,WACA,QACA,kEACA,QACA,MACA,SACA,SACA,MACA,iCACO,CACP,CACA,CAAG,EACH,CAAC,CAAC,WAAe,EAGjB,IACA,WACA,YAAiB,WAAgB,WACjC,SAAc,WAAgB,SAChB,SAAc,UACb,WAAgB,WAC/B,UAAe,WAAgB,WAC/B,WAAgB,WAAgB,WAChC,QAAa,WAAgB,WAC7B,SAAc,SAAc,cACT,SACnB,WAAgB,SAAc,CAE9B,cACA,YACA,WACA,CAAG,CACH,0BACA,iBACA,WACA,kBACA,YACA,aACA,kBACA,eACA,cACA,gBACA,eACA,cACA,sBACA,sBACA,OAAwB,eAAmB,KAC3C,QACA,UACA,YACA,YACA,aACA,WACA,gBACA,aACA,iBACA,iBACA,kBACA,eACA,iBACA,WACA,CAAK,CACL,CAAG,CACH,2BACA,iEACA,wBACA,OAAwB,eAAmB,QAC3C,MACA,4BAGA,OACA,kBACA,YACA,UACA,CACA,CAAK,GACL,CACA,EAEA,eACA,aAEA,MADI,OAAe,SACR,OAAU,kBACrB,CAEA,MADE,OAAS,MACF,OAAY,KACrB,aACA,iBACA,WACA,aACA,aACA,sBACA,cACA,IACA,OAA0B,eAAmB,QAC7C,UAAmB,OAAI,aACvB,CAAO,0BACP,cACA,SACA,UACA,SACA,YACA,MACA,4BAIA,OAHA,kDACA,+BACA,MACA,CACA,CAAO,KACP,CACA,CAAG,EACH,CAAC,CAAC,WAAe,EAGjB,eACA,kBACA,SAEA,cACA,OACA,WACA,2BAJA,mBAKA,CACA,CA6BA,eACA,IACA,EACA,EACA,EAHA,6DAIA,KACA,KACA,QAAc,WAAwB,KAEtC,QADA,OACgB,YAAmB,SA+BnC,KACA,0BACA,wCACG,CACH,EAnCmC,aACnC,KACA,UAEA,yBAEA,CACA,QAAc,WAAmB,IACjC,wBACA,qBACK,EAEL,CAFQ,KAER,CACA,SACA,OACA,CACA,CACA,uBACA,OACA,iBACA,YACA,EAKA,uBACA,QACA,MANA,CACA,QACA,KACA,CAIA,CAAG,CACH,CA2BA,qBACA,OACA,iBACA,aACA,kBACA,EACA,GACA,iBACA,aACA,kBACA,EACA,qBACA,OACA,MACA,CAAG,CACH,CAzHA,gBAAwB,OAAa,GAAG,kBA4HxC,qBACA,4BACA,EACA,iBACA,4BACA,cACA,CAAG,kBACH,eACG,CACH,EACA,eACA,aAEA,MADI,OAAe,SACR,OAAU,kBACrB,CAEA,MADE,OAAS,MACF,OAAY,KACrB,aACA,iBAQA,IAPA,iBACA,aACA,sBACA,kBACA,IACA,IACA,KACA,OACA,gBAGA,uBACA,cACA,CAAW,QAAU,CACrB,UACA,SACA,UACA,SACA,OAIA,GADA,iCACA,UACA,SACA,IACA,oCAEA,sDACA,OACA,QACA,CACA,IACA,QACA,CACA,sBACA,iCACA,kCACA,IACA,oCAEA,+BACA,OACA,EAAU,IACV,GACA,oCAEA,sDACA,MAEA,CACA,OAA0B,eAAmB,QAC7C,mBACA,CAAO,GACP,CAGA,CAAG,EACH,6BACA,oBAEA,cAIA,EADA,gBACA,mBACA,cACA,CAAO,kBACP,cACA,CAAO,EAGP,4BACA,0BACA,YACA,CAAS,CACT,CAAO,CACP,CACA,CAAG,EACH,yBACA,oBACA,0BACA,OAAa,IAAO,yBAEpB,WADA,YADoB,EACpB,CAEO,CACP,CACA,CAAG,EACH,qBACA,oBACA,WACA,aACA,cACA,gBACA,eACA,wBACA,UACA,WACA,wBACA,iBAEA,wBACA,WAAoC,eAAmB,IACvD,YACA,WACA,OACA,QACA,SACA,iBACA,CAAS,CACT,CACA,WAAkC,eAAmB,WACrD,cACA,YACA,UAAmB,OAAI,oCACvB,oBACA,sBACA,CACA,CAAO,4BACP,CACA,CAAG,EACH,eACA,oBACA,mBACA,oBACA,iCACA,CACA,CAAG,EACH,CAAC,CAAC,WAAe,EACjB,gBAA8B,OAAa,GAAG,kBAE9C,mBACA,iBACA,OAAsB,eAAmB,QACzC,4CACA,CAAG,GACH,EAKA,iBACA,2DAgEA,eACA,aACA,MACI,OAAe,SACnB,0CAAwE,IAAa,IACrF,kBAgEA,MA9DA,GAAY,OAAU,uBACtB,6BACA,cACA,UACA,mBACA,2BACA,EACA,+BACA,IAKA,EALA,UACA,UACA,eACA,yBACA,EAAgB,OAAG,yCAEnB,wBAEA,EADA,sBACA,aACA,EACA,0BACA,wBACA,qCAEA,kCACA,cACA,iBACA,WACA,cACA,UACA,OACA,wBACA,UAAmB,OAAI,gDACvB,CAAO,CACP,EACA,yBACA,cACA,cACA,UACA,iBACA,kBACA,OAA0B,eAAmB,QAC7C,YACA,mBACO,CAAe,eAAmB,QACzC,UAAmB,OAAI,mDACvB,CAAO,IAA+B,eAAmB,QACzD,oBACA,oBACO,6BAAoD,eAAmB,QAC9E,oBACA,kBACO,CAAe,eAAmB,QACzC,2BACA,CAAO,CAAe,eAAmB,QACzC,qBACA,CAAO,CAAe,eAAmB,QACzC,6BACA,CAAO,aACP,EACA,eAAsC,eAAS,GAC/C,gBAAuC,eAAS,GAChD,cAAqC,eAAS,GAC9C,uBAlIA,IACA,MAAS,OAAO,aAsBhB,IArBA,cACA,WACA,YACA,YACA,cACA,cACA,MACA,YACA,WACA,CAAO,EACP,UACA,SACA,wBA5RA,EAIA,EACA,EACA,EACA,EACA,EACA,EAGA,EAiRA,OAzRA,GAJA,MACA,UA4RA,EA3RA,UA2RA,CA1RA,CAAK,GACL,MACA,SACA,EAuRA,EAvRA,gBACA,QAsRA,EAtRA,gBAsRA,GAtRA,UACA,EAqRA,EArRA,IAqRA,EArRA,WAqRA,GArRA,UACA,EAAgB,IAoRhB,EApRyB,YACzB,wBACA,CAAG,EAMH,CACA,MA2QA,EA1QA,EAVyB,GAOzB,YAHA,WADA,oBACA,IAGA,mBAIA,SACA,qBACA,CAwQA,CAAK,EACL,wBACA,WACA,UAGA,mBACA,uBACA,OACA,QACA,OACA,SACA,QACA,QACA,eACA,kBAEA,OADA,IACuB,OAAa,CAAC,OAAa,GAAG,OACrD,CAAO,CACP,2BACA,YACO,CACP,2BACA,0BACA,wBACA,CAAS,CACT,CAAO,CACP,6BACA,4BACA,OAlDA,QAkDA,GAlDA,SAkDA,CACA,CAAS,kBACT,eACS,CACT,CAAO,CACP,2BACA,qCACA,CAAO,CACP,2BACA,iBACA,WACA,8BACA,CACA,CACA,CAAG,IACH,IAuEA,CACA,CAEA,MADE,OAAS,MACF,OAAY,KACrB,kBACA,iBAGA,IAFA,EAEA,EAAwB,OAAS,2BACjC,0CAAiJ,OAAS,+BAE1J,4BADuB,OAAS,+BAChC,KACA,CACA,CAAG,EACH,aACA,iBACA,iBACA,SACA,QACA,UACA,cACA,aACA,eACA,qBACA,cACA,YACA,eACA,WACA,iBACA,aACA,cACA,kBACA,gBACA,kBACA,eACA,eACA,uBACA,aACA,cACA,kBACA,+BACA,mCACA,WACA,UACA,UACA,gBACA,GACA,WACA,YACA,UACA,YACA,aACA,WACA,gBACA,aACA,aACA,cACA,WACA,EACA,OAA0B,eAAmB,QAC7C,YACA,gBACA,sBACO,CAAe,eAAmB,KACzC,YACA,OACA,SACA,MACA,QACA,aACA,4BACA,UACA,gBACA,cACA,mCACA,aACA,qBACA,YACA,CAAO,EAAgB,eAAmB,QAC1C,UAAmB,OAAI,oDACvB,UACA,CAAO,IAA+B,eAAmB,QACzD,qBACA,uBACO,gCAAmD,eAAmB,QAA6C,eAAmB,kBAC7I,UACA,CAAO,IACP,mBACO,sBACP,OAA4B,eAAmB,mBAC/C,MACA,UACA,CAAS,IACT,CAAO,cAAkC,eAAmB,mBAC5D,WACA,+BACO,QACP,CACA,CAAG,EACH,CAAC,CAAC,WAAe,EACjB,iBACA,UACA,WACA,EAEA,mBACA,cACA,OAAsB,eAAmB,SACzC,oBACA,kBACA,CAAG,GACH,EAEA,eACA,cACA,kBACA,uBACA,EAGsB,eAAmB,WACzC,cACA,4BACA,SACA,CAAG,IANqB,eAAmB,eAO3C,EAEA,wBAMA,eACA,aACA,MACI,OAAe,SACnB,0CAAyE,IAAa,IACtF,kBAwJA,MAtJA,GAAY,OAAU,uBACtB,wBACA,+BAEA,2BACA,IAjBA,MA5cA,EACA,EACA,EACA,EAUA,EAGA,EA6cA,UACA,WACA,eACA,eACA,WACA,aACA,SACA,cACA,uBACA,cACA,YACA,kBACA,UACA,qBACA,aAIA,GAnCA,EAkCsC,OAAkB,IAlCxD,EAkCwD,KAlCxD,EAkCwD,cA9exD,EA6cA,qBACA,gBAgCwD,IA/BxD,CAAG,EA9cH,EAAa,OAAkB,IAC/B,KACA,KACA,sBACA,MA0eA,EA1eA,SACA,UACA,CAweA,EAxeA,eACA,UAEA,SAEA,CAAG,EACH,uBACA,cAieA,IAheA,CAAG,EACH,uBACA,cA8dA,IA7dA,CAAG,EACH,UAAmB,OAAkB,IAAe,OAAkB,MA6dtE,OAA0B,eAAmB,KAC7C,MACA,8BACA,yBACA,0BACA,SACA,OACA,QACA,SACA,gBACA,WACA,aACA,aACA,YACA,UACA,YACA,iCACA,mBACA,4BACA,6BACA,uCACA,iCACA,gCACA,qBACA,gBACA,4BACA,eACA,CAAO,CACP,EACA,gCACA,aACA,cACA,EAAgB,OAAwB,OACxC,UACA,SACA,qBACA,cACA,qBACA,oBACA,OACA,2BACA,oCACA,OAA0B,eAAmB,uBAAwB,IACrE,UAAmB,OAAI,wCACvB,WACA,CAAO,EAAgB,eAAmB,IAC1C,QACA,OACA,gBACA,aACA,wBACA,kCACA,CACA,CAAO,EACP,EACA,iCACA,kDACA,6BACA,qCACA,uBACA,CAAO,CACP,EACA,qCACA,mBACA,mBACA,6BACA,EACA,+BACA,mBACA,0CAA6E,IAAe,IAC5F,kBAEA,2BACA,EACA,oCACA,mBACA,0CAA6E,IAAe,IAC5F,kBAEA,gCACA,EACA,iCACA,mBACA,0CAA6E,IAAe,IAC5F,kBAEA,6BACA,EACA,qCACA,cACA,UACA,gBACA,eACA,qBACA,wBAGA,GADA,mBACA,GACA,MAAuB,OAAa,2BACpC,YACA,SACA,OACA,SACA,WACA,QACA,CACA,CAAS,CACT,EAAQ,OACR,uBAEA,aACA,EACA,4BACA,YACA,YACA,CAAO,CACP,EACA,SACA,WACA,oBACA,SACA,EACA,eAAsC,eAAS,GAC/C,aAAoC,eAAS,GAC7C,aACA,uBACA,CACA,CAEA,MADE,OAAS,MACF,OAAY,KACrB,wBACA,iBACA,IACA,EADA,OAEA,8DACA,iEACA,GACU,GAAsB,YAChC,KACA,YACA,mBACA,CAAa,CACb,CAAW,CAEX,CAAO,IACP,CACA,CAAG,EACH,yBACA,iBACA,6DACA,CACA,CAAG,EACH,2BACA,iBACA,4DACA,CACA,CAAG,EACH,aACA,iBACA,iBACA,SACA,cACA,cACA,qBACA,EAAgB,IAAK,KAErB,OADA,yBAC0B,UAFL,KAEwB,QAC7C,UAAmB,OAAI,qBACvB,aACA,0BACA,sBACO,CAAe,eAAmB,QACzC,qCACA,UACA,CAAO,yFACP,CACA,CAAG,EACH,oBACA,kBACA,iBACA,cACA,eACA,OACA,gBACA,eACA,4CACA,OAA4B,eAAmB,QAC/C,gBACA,sBACA,CAAS,CAAe,eAAmB,IAC3C,OACA,YACA,iCACA,CAAS,EACT,CAAO,CACP,CACA,CAAG,EACH,oBACA,iBACA,MACA,EACA,OACA,uDACA,aACA,cACA,cACA,eACA,YACA,aACA,gBACA,oBAMA,OAA0B,eAAmB,KAC7C,UACA,YACA,YACA,aACA,UACA,WACA,cACA,sBACA,6CACA,yCACA,mDACA,kBACA,kBACA,mCACA,OApBA,WACA,mBACA,YACA,CAAS,CACT,CAiBA,CAAO,CAgCP,CACA,CAAG,EACH,sBACA,iBACA,eACA,oBACA,8CACA,CAAO,CACP,CACA,CAAG,EACH,kBACA,kBACA,oCACA,2BACA,qBACA,UACA,CAAO,EACP,qBACA,0BACA,qCACA,4BACA,QACA,QACA,MACA,gBACA,gBACA,UACO,CACP,CACA,CAAG,EACH,qBACA,iBACA,gCACA,0BAEA,CAAG,IACH,+BACA,oBACA,aAEA,OACA,OACA,mBAHA,UAGA,qBACA,CACA,CACA,CAAG,EACH,CAAC,CAAC,WAAe,CACjB,wBACA,kBAGA,OACA,MAHA,uBAIA,IAHA,qBAIA,CACA,EACA,4BACA,kBACA,UACA,iBACA,0BACA,cACA,yBACA,SACA,QACA,CACA,EACA,uBAEA,SADA,UACA,6BACA,EAEA,mBACA,YACA,QACA,SACA,UACA,cACA,sGACA,EACA,eAuBA,QAtBA,QACA,QACA,SACA,cACA,cACA,MACA,QACA,MACA,OACA,YACA,WACA,CAAG,EAGH,uBACA,8BACA,yBACA,MACA,WACA,WAGA,IAAoB,IAAiB,KACrC,cACA,YAAuB,IAAkB,KACzC,YACA,KAEA,kCACA,CACA,CA9BA,IAiCA,aAEA,cAEA,gBADA,0CACA,EACA,CACA,OALA,6BAKA,CACA,SACA,0BACA,gBACA,KACK,CACL,4BACA,aACA,8CACA,CAAK,CACL,qBAGA,yCACA,uBACA,CAAO,iBAGP,OADA,oCACA,CACA,CAAK,CACL,kCACA,uDACA,YACK,CACL,mCACA,+BACA,gDACA,CAAK,CACL,gCACA,+DACA,mCACA,4CACA,4BACA,sBACK,CACL,4BACA,sBACA,CAAK,CACL,2BACA,sBACA,CAAK,CACL,yBACA,qCACA,CAAK,CACL,wBACA,qCACA,CAAK,CACL,2BACA,2BACA,2BACA,WACA,OACA,gDACA,OACA,MACA,qBACA,WACA,YACA,SACA,SACA,CACA,CAAK,CACL,mCAGA,OADA,EADA,GACA,SAEA,CACA,CACA,CAEA,OAwBS,OAAY,CAvBrB,gBACA,kBACA,gBACI,OAAe,SACnB,sCACA,UACA,cACA,QACA,YACA,QACA,WACA,aACA,WACA,gBACA,cACA,WACA,cACA,WACA,EAKqB,EACrB,aACA,sBAGA,UAOA,IANA,gCACA,oCACA,CAAS,CAET,MAOA,CAPgB,GAOhB,QAEA,CADA,2BACA,uBAIA,gBAOA,CAAG,EACH,YACA,eACA,kBACA,gCAGA,aACA,SAIA,eACA,gCAIA,sBAEA,OADA,kBACA,cACA,CACA,CAAG,EACH,cACA,eAEA,sBAGA,4CAGA,eACA,WACA,YACA,WAEA,SADA,oBACA,CACA,CACA,CAAG,GAyCH,eAuDA,QAtDA,WACA,2BACA,gBACA,cASA,EAzCA,YAKA,IAJA,MAAqB,IAAM,yBAC3B,eACG,GACH,KACA,CAJ2B,CAI3B,WACA,gBACA,UACA,YAAoB,WAAyB,KAC7C,WAGA,yBAKA,QACA,uBACA,SACA,CAGA,MACA,CACA,CACA,QACA,EAQA,kBACA,iBACA,cACA,WACA,CAAK,CACL,CAAG,GAMH,KAqCA,IAAkB,WAAgC,IAClD,GArCA,WACA,IAxDA,EAwDA,OAGA,qBACA,iDACA,CAAK,EAGL,MAGA,OAFA,UACA,UACA,CAIA,EAJgB,CAIhB,YAKA,QADA,OACA,kBAA4C,SAAgB,IA5E5D,EA6EA,UA1EA,2BA0EA,KAxEA,eAwEA,EAxEA,cAyEA,cAGA,GAEA,iBACA,UAGA,YACA,eAEA,IAEA,SAIA,yBACA,OACA,aACA,OACA,UACA,gBACA,cACA,6BACA,CACA,CACA,CAAG,CACH,CA8FA,QACA,WACA,aAnFA,YAKA,UACA,OALA,SAMA,uBALA,yBAMA,YALA,cAMA,UALA,WAMA,CAAG,EACH,2BAGA,CAFA,UACA,UACA,gCAAuD,uDAAoF,CAC3I,CAAG,EACH,YAAkB,WAAyB,IAC3C,gBACA,uBACA,uBACA,gBACA,iBAEA,YAAoB,aAA+B,IAInD,QAHA,OACA,cACA,6BACA,MAA0B,WAAyB,KACnD,WACA,cACA,6BACA,qCAEA,kBACA,kBAEA,CAEA,YAAoB,WAA2B,KAG/C,QAFA,OACA,KACA,IAAsB,MAAW,cAEjC,CAFwD,GAExD,QAAsB,mBAAyB,sDAE/C,EAF8H,CAE9H,gBACA,CACA,YAAoB,WAA2B,KAC/C,QACA,eACA,SAEA,gBA/DA,SACA,YAAkB,mBAAyB,IAC3C,kCACA,wCAEA,qBACA,0BACA,UAEA,QACA,EAoDA,UACA,GACA,YACA,YAAsB,WAAyB,gBAC/C,CACA,YAAoB,WAA2B,KAC/C,UACA,2BAIA,QADA,IACA,IAAsB,mBAAwB,KAC9C,uBACA,SACA,CACA,oCAKA,mBACA,6DACA,yDACA,mEACA,CACA,QACA,CAOA,EAqBA,eACA,aAEA,MADI,OAAe,SACR,OAAU,kBACrB,CAEA,MADE,OAAS,MACF,OAAY,KACrB,aACA,iBACA,iBACA,eACA,aACA,UACA,YACA,eAEA,EADA,eAAuE,GACvE,gBACA,kBACA,0BACA,OAA0B,eAAmB,sBAC7C,8BACA,CAAO,wBACP,2BACA,OAA4B,eAAmB,IAC/C,MACA,QACA,UACA,CAAS,CAAe,eAAmB,uBAAwB,IACnE,UAAqB,OAAI,6BACzB,CAAS,aACT,CAAO,EACP,CACA,CAAG,EACH,CAAC,CAAC,WAAS,EAEX,eACA,gCACA,CAGA,eACA,cACA,cACA,UACA,cACA,QACA,aACA,UACA,mBACA,mBACA,YACA,YACA,kBACA,sBACA,eACA,eACA,UACA,iBACA,aACA,eACA,WACA,aACA,uBACA,GAA4B,eAAmB,QAC/C,QACA,2BACA,CAAG,IAAuB,eAAmB,QAC7C,QACA,6BACA,CAAG,GAAuB,eAAmB,IAC7C,QACA,OACA,CAAG,MACH,WACA,QACA,UACA,YACA,EAAmB,OAAa,CAAC,OAAa,GAAG,YAAsB,CAAE,OAAe,EACxF,UACA,aACA,WACA,CAAG,0BACH,OAAsB,eAAmB,kBACzC,WACA,CAAG,IAAuB,eAAmB,QAC7C,cACA,WACA,UACA,gBACA,QACA,YACA,gDACA,UAAe,OAAI,qDACnB,iBACA,gCACA,6BACA,CAAK,CACL,CAAG,IACH,CAEA,mBACA,iBACA,cACA,UACA,aACA,OAAsB,eAAmB,QACzC,YACA,QACA,KACA,CAAG,GACH,EACA,GAAsC,YAAgB,eACtD,OAAsB,eAAmB,oBAAmC,IAC5E,UACA,CAAG,EACH,CAAC,EAED,eACA,oDACA,eACA,aACA,MACI,OAAe,SACnB,0CAAyE,IAAa,IACtF,kBA4NA,MA1NA,GAAY,OAAU,uBACtB,OACA,aACA,0BACA,EACA,uBACA,2BACA,eACA,sBACA,UACA,QACA,aACA,cACA,cACA,YACA,eACA,SACA,cACA,uBACA,cAEA,EADA,EACA,YACA,aAQA,OA3KA,YACA,SACA,yBACA,cACA,YACA,IAVA,EAUA,uBACA,UAEA,CADA,mBAXA,CADA,EAaA,IAZA,gCAgBA,wBAFA,GAGA,EAuJA,CACA,SACA,YACA,cACA,wCACA,oBACA,CAAO,EACP,kBAEA,IADA,EAOA,EANA,UACA,UACA,WACA,aACA,oCACA,yBAEA,uBACA,sBACA,8BAAgE,mCAChE,gBAAuE,UACvE,QACA,KACA,CAAS,IACT,2BACA,sBACA,OAA4B,eAAmB,KAC/C,QACA,QACA,QACA,MACA,UACA,MACA,aACA,iBACA,iBACA,YACA,0BACA,iBACA,oBACA,iBAAiC,OAAa,CAAC,OAAa,CAAC,OAAa,GAAG,uBAC7E,gCACa,MACb,oBACA,CAAa,IACb,CAAW,CACX,0BACA,0BACA,CAAW,CACX,oBACA,uBACA,uBACA,CAAW,CACX,WACA,CAAS,CACT,CAAO,CACP,EACA,yBACA,6BACA,UACA,uBACA,cACA,gCACA,QACA,CAAO,EACP,oBACA,CAAO,EACP,cACA,0BACA,cACA,OACA,cACA,YACA,GACA,8DAIW,IAJX,GACA,QACA,MACA,4BACW,IAEX,iFACA,aAEA,EACA,cACA,iDACA,oBACA,mBAEA,qBACA,WACA,4BACU,WACV,8BAEA,oDACA,MAAe,OAAa,CAAC,OAAa,GAAG,MAAkB,EAC/D,aACA,yBACA,8BACA,CAAS,CACT,EACA,gBACA,kCACA,WACA,cACA,YACA,eACA,YACA,UACA,SACA,KACA,CAAW,CACX,CACA,YACA,YACA,CAAS,CACT,EACA,oBACA,sBACA,gCACA,uCACA,mCACA,CAAO,EACP,yBACA,mBACA,CAAO,EACP,+BACA,yBACA,CAAO,EACP,0BACA,oBACA,cAA4B,OAAa,CAAC,OAAa,GAAG,YAAkB,EAC5E,gBACA,QACA,CAAW,GACX,YACA,YACA,CAAW,EAEX,CAAO,EACP,wBACA,mBACA,YACA,YACA,CAAW,CAEX,CAAO,CACP,EACA,iCACA,cACA,uBACA,iBACA,EACA,0BAQA,IAPA,kBACA,YACA,WACA,WACA,QACA,IACA,KACA,4BACA,UACA,mCAEA,EAFqE,CAErE,sBACA,QACA,QACA,MACA,4BACA,SACA,SACA,KACA,CAAO,CACP,EACA,qBACA,0CAA6E,IAAe,IAC5F,kBAEA,2BACA,EACA,0BACA,0CAA6E,IAAe,IAC5F,kBAEA,gCACA,EACA,uBACA,0CAA6E,IAAe,IAC5F,kBAEA,6BACA,EACA,0BACA,eAAsC,eAAS,GAC/C,CACA,CAEA,MADE,OAAS,MACF,OAAY,KACrB,wBACA,iBACA,0CACA,kBACA,6CAEA,CACA,CAAG,EACH,2BACA,iBACA,2BACA,iCACA,CACA,CAAG,EACH,yBACA,oBACA,yDACA,iEACA,iBACA,WACA,UACA,cACA,SACA,QACA,QACA,kCACA,kBAEA,IADA,kCACA,GACA,+FACA,8CACA,OACQ,yDACR,4BAEA,CAMA,CAAG,EACH,6CACA,iBACA,WACA,0DACA,2BACA,6BAEA,wDACA,uBACA,0BACA,0CACA,CAAO,KACP,CACA,CAAG,EACH,iCACA,iBACA,0BACA,+CACA,CACA,CAAG,EACH,4BACA,iBACA,iBACA,QACA,QAEA,EADA,eAEA,eACA,gDACA,2BACA,eACA,uBACA,CAAS,CACT,EAAQ,IACR,iCAEA,CACA,CAAG,EACH,aACA,iBACA,iBACA,SACA,QACA,QACA,UACA,aACA,cACA,cACA,YACA,YACA,EAAkB,OAAwB,OAC1C,eACA,0BACA,yBACA,EAAqB,OAAwB,MAC7C,sDACA,uBACA,aACA,cACA,QACA,WACA,cACA,YAKA,SACA,cACA,UACA,GACA,uCACA,OACA,mDACA,CACA,EACA,yBACA,OAA0B,eAAmB,IAC7C,sBACA,OACA,QACA,UAAmB,OAAI,gEAEvB,yBACA,cACA,UACA,CAAO,4BACP,OAA4B,eAAmB,KAC/C,MACA,QACA,WACA,UACA,YACA,CAAS,CACT,CAAO,EAAgB,eAAmB,IAC1C,YACA,WACA,YACA,UACA,aACA,aACA,CAAO,CAAe,eAAmB,QACzC,UAAmB,OAAI,iCACvB,CAAO,oBACP,mCACA,oBACA,CAAO,qBACP,yBACO,OAA+B,eAAmB,QACzD,+BACA,OACA,MACA,QACA,CACA,CAAO,CAAe,eAAmB,sBAnDzC,CACA,QACA,KACA,EAgDyC,kDAAqH,eAAmB,KAAwD,eAAmB,WAC5P,CACA,CAAG,EACH,CAAC,CAAC,WAAe,EACjB,iBACA,qBACA,WACA,EAEA,mBACA,cACA,OAAsB,eAAmB,CAAC,UAAc,QACxD,EAEA,eACA,aACA,MACI,OAAe,SACnB,0CAAwE,IAAa,IACrF,kBAkDA,MAhDA,GAAY,OAAU,uBACtB,kCACA,mBACA,6BACA,EACA,wBACA,cACA,WACA,QACA,eACA,WACA,UACA,YACA,cACA,cACA,eACA,cACA,kBACA,yBACA,wBACA,CAAO,IACP,OAA0B,eAAmB,KAC7C,YACA,MACA,SACA,UAGA,gCACA,QACA,SACA,aACA,4BACA,aACA,0BACA,aACA,YACA,UACA,YACA,+BACA,8BACA,yCACA,mCACA,kCACA,8CACA,WACA,CAAO,CACP,EACA,CACA,CAEA,MADE,OAAS,MACF,OAAY,KACrB,wBACA,kBACA,WACA,aACA,cACA,qBACA,WACA,oBACA,sBACA,kBACA,MACA,2BACA,WACA,0BACA,OACA,cACA,UACA,EAAkC,eAAmB,IACrD,OACA,QACA,WACA,CAAS,EACT,OAA4B,eAAmB,QAC/C,MACA,QACA,UAAqB,OAAI,+CACzB,CAAS,GAA+B,eAAmB,WAC3D,cACA,4BACA,oBACA,iCACA,CACA,CAAS,IAAyB,eAAmB,gBACrD,CAAO,CACP,CACA,CAAG,EACH,aACA,iBACA,WACA,aACA,UACA,QACA,cACA,UACA,WACA,WACA,cACA,eACA,eACA,YACA,cACA,cACA,kBACA,eACA,qBACA,mBACA,kBACA,cACA,IACA,IACA,2CAA8D,OAAa,YAE3E,uBACA,OAA0B,eAAmB,QAC7C,QACA,MACA,UAAmB,OAAI,wCACvB,CAAO,CAAe,eAAmB,QACzC,6CACA,OACA,QACA,WACA,UACA,CACA,CAAO,IAAmC,eAAmB,8BAC7D,MAAoB,OAAc,MAClC,OACA,OACA,OAA4B,eAAmB,QAC/C,oCACA,QACA,CAAS,IAA2B,eAAmB,QACvD,qCACA,yBACA,CAAS,CAAe,eAAmB,QAC3C,sBACA,CAAS,CAAe,eAAmB,IAC3C,QACA,yBACA,UACA,CAAS,IAAkB,eAAmB,QAC9C,kGACA,CAAS,yBAAiD,eAAmB,KAC7E,YACA,MACA,SACA,UAGA,gCACA,QACA,oBACA,gBACA,4BACA,aACA,0BACA,aACA,YACA,UACA,YACA,+BACA,8BACA,yCACA,kCACA,kCACA,8CACA,WACA,CAAS,EACT,CAAO,EACP,CACA,CAAG,EACH,CAAC,CAAC,WAAe,EAEjB,eACA,aACA,MACI,OAAe,SACnB,0CAAwE,IAAa,IACrF,kBAOA,MAJA,CADA,EAAY,OAAU,uBACtB,kCACA,mBACA,6BACA,EACA,CACA,CAEA,MADE,OAAS,MACF,OAAY,KACrB,wBACA,kBACA,WACA,aACA,cACA,qBACA,WACA,oBACA,eACA,WACA,kBACA,mBACA,kBACA,cACA,cACA,WACA,QACA,eACA,eACA,YACA,cACA,MACA,mBACA,2BACA,WACA,0BACA,OACA,cACA,UACA,EAAkC,eAAmB,IACrD,OACA,QACA,WACA,CAAS,EACT,OAA4B,eAAmB,QAC/C,MACA,yDACA,CAAS,CAAe,eAAmB,QAC3C,kGACA,CAAS,CAAe,eAAmB,QAC3C,QACA,UAAqB,OAAI,+CACzB,CAAS,GAA+B,eAAmB,WAC3D,cACA,4BACA,oBACA,iCACA,CACA,CAAS,IAAyB,eAAmB,kBAAuC,eAAmB,QAC/G,mBACA,CAAS,qBACT,MAAsB,OAAc,MACpC,OACA,OACA,OAA8B,eAAmB,QACjD,wCACA,UAAuB,OAAI,+CAC3B,CAAW,CAAe,eAAmB,IAC7C,QACA,yBACA,UACA,CAAW,EACX,CAAS,GAAiB,eAAmB,QAC7C,iDACA,CAAS,qBACT,MAAsB,OAAc,MACpC,OACA,OAEA,oCACA,qDACA,CAAW,EACX,OAA8B,eAAmB,KACjD,wCACA,YACA,MACA,SACA,UACA,gCACA,UAEA,SAEA,gBACA,4BACA,aACA,0BACA,aACA,YACA,UACA,YACA,+BACA,8BACA,yCACA,kCACA,kCACA,8CACA,WACA,CAAW,CACX,CAAS,GACT,CAAO,CACP,CACA,CAAG,EACH,aACA,iBACA,iBACA,UACA,QACA,UACA,cACA,kBACA,gCACA,KAIA,OAHA,GACA,2CAA8D,OAAa,YAEjD,eAAmB,QAC7C,QACA,MACA,UAAmB,OAAI,wCACvB,CAAO,CAAe,eAAmB,QACzC,6CACA,OACA,QACA,WACA,UACA,CACA,CAAO,IAAmC,eAAmB,oCAC7D,CACA,CAAG,EACH,CAAC,CAAC,WAAe,EAuBjB,eACA,YACA,QACA,cACA,SACA,cACA,WACA,aACA,eACA,YACA,cACA,sBACA,EAAiB,aAAO,gBA3BxB,EACA,EACA,EACA,EAyBA,OA3BA,GADA,EA4BA,CACA,MACA,MACA,WACA,CAAO,EA/BP,IACA,QAEA,CADA,eACA,8CACA,CACA,wBACA,qBACA,EAEA,CACA,QACA,KACA,CAoBA,CAAK,CAEL,mEACA,UACA,QACA,EAAkB,cAAQ,KAC1B,MACA,MACA,YACA,OACA,WACA,CAAK,GACL,EAAiB,OAAc,MAC/B,OACA,OACE,eAAS,YACX,GACA,YACA,MACA,MACA,YACA,OACA,WACA,CAAO,EAMP,CAAG,sEACH,MAAmB,iBAAW,eAC9B,iBAEA,CAF0B,GAE1B,yBACA,OAAwB,eAAmB,SAC3C,UAAiB,OAAI,0BACrB,CAAK,gCACL,CAAG,UACH,OAAsB,eAAmB,IACzC,aACA,CAAG,CAAe,eAAmB,QACrC,4CACA,KACA,CAAG,4BACH,OAAwB,eAAmB,KAC3C,MACA,QACA,WACA,aACA,aACA,SACA,CAAK,CACL,CAAG,GACH,EACA,GAAgC,YAAgB,eAChD,OAAsB,eAAmB,mBACzC,WACA,CAAG,IACH,CAAC,EAED,MAmCA,eACA,cACA,MAuGA,MAtGI,OAAe,SACnB,GAAY,OAAU,cACtB,yBACA,qBACA,oDAEA,EACA,0BACM,GAAqB,cAC3B,YAAwB,GAAsB,iBAC9C,EACA,iCACA,mBACA,0CAA0E,IAAa,IACvF,kBAEA,6BACA,EACA,+BAEA,mBACA,0CAA6E,IAAe,IAC5F,kBAEA,2BACA,EACA,oCACA,mBACA,0CAA6E,IAAe,IAC5F,kBAEA,gCACA,EACA,qCACA,cACA,UACA,gBACA,eACA,qBACA,wBAEA,GADA,mBACA,GACA,MAAuB,OAAa,2BACpC,YACA,SACA,OACA,SACA,SAAsB,OAAa,CAAC,OAAa,GAAG,MAAe,EACnE,aACA,CAAa,EACb,QACA,CACA,CAAS,CACT,EAAQ,OACR,uBAEA,aACA,EACA,uCACA,2BACA,iBACA,0BACA,qCACA,MACA,QACA,QACA,MACA,gBACA,wBACO,CACP,EACA,4BACA,YACA,YACA,CAAO,CACP,EACA,2BACA,yBACA,2BACA,4BACA,oCACA,4BACA,uBACA,YACA,eACA,CAAS,YACT,sBACA,CAAS,IAET,EACA,oBAA8B,OAAO,eACrC,MA/HA,CACA,uBACA,EACA,oBACA,UA2HA,EA3HA,mBACA,CAAO,EAHP,iBAIK,CACL,wBACA,qBAuHA,EAjHA,sBACA,wBACA,oBACA,sBACA,mBACA,UACA,UACA,CAAW,MACD,CACV,mBACA,UACA,UACA,CACA,CAAO,EAhBP,YACA,CAiBA,CACA,CAkGA,CAAK,EACL,SACA,mBACA,kBACA,EACA,YAAmC,WAAe,GAClD,aAAoC,WAAe,GACnD,eAAsC,WAAe,GACrD,oBACA,YAAmC,eAAS,GAC5C,CACA,CAEA,MADE,OAAS,MACF,OAAY,KACrB,8BACA,iBAEA,OADA,qBACA,IACA,CACA,CAAG,EACH,wBACA,iBACA,wBACA,qBAEA,uBACA,mBACA,mDACA,CACA,CAAG,EACH,2BACA,iBACA,uDACM,GAAqB,iBAC3B,yCACA,oEAEA,CACA,CAAG,EACH,yBACA,iBACA,kBACA,CACA,CAAG,EACH,sBACA,oCACA,iBACA,QACA,QACA,oCACA,6CACA,CAAO,EACP,oCACA,6CACA,CAAO,EACP,OAA0B,eAAmB,oBAA4B,aACzE,YACA,iBACA,iBACA,cACA,aACA,wBACA,+BACA,OACA,SACA,mBACA,oBACA,CAAO,EACP,CACA,CAAG,EACH,2BACA,kCACA,WACA,yBACA,MAAoB,OAAc,MAClC,OACA,OACA,yBACA,6CACA,CAAS,CACT,CAAO,CACP,CACA,CAAG,EACH,uBACA,kCACA,WACA,yBACA,OAA4B,eAAmB,QAC/C,OACA,eACA,iBACA,MACA,CAAW,CACX,KACA,CAAS,mBACT,MAAsB,OAAc,MACpC,OACA,OACA,OAA8B,eAAmB,QACjD,OACA,MACA,CAAa,CACb,mBACA,CAAW,wCACX,CAAS,EACT,CAAO,CACP,CACA,CAAG,EACH,mBACA,wBACA,iBACA,cACA,cACA,2BACA,eACA,uBACA,iDACA,mBACA,0BACA,EAGA,yCAFA,4CAIA,CACA,CAAG,EACH,aACA,iBAEA,IADA,EACA,aACA,WACA,qBACA,UACA,UACA,QACA,aACA,WACA,cACA,eACA,cACA,YACA,cACA,QACA,QACA,sBACA,uBACA,cACA,2BACA,4BACA,WACA,eACA,qBACA,SACA,KACA,KACA,sBACA,kBACA,iBACA,UACA,oEACA,UAEA,SAEA,CACA,CAAO,EACP,sBACA,eACA,SAEA,CAAO,EACP,qBACA,kBACA,CAAO,EACP,OACA,QACA,SACA,QACA,MACA,SACA,YACA,WACA,oFACA,sCACA,iCACA,YACA,UACA,aACA,yBACA,uCACA,qBACA,yCACA,qCACA,+BACA,iDACA,2CACA,mCACA,6CACA,WACA,EACA,OAA0B,eAAmB,QAC7C,UAAmB,OAAI,+CACvB,sBACO,kBAA6E,eAAmB,OAAsD,eAAmB,8CAAsF,eAAmB,QACzR,oBACA,6BACA,2BACO,CAAe,eAAmB,KACzC,OACA,mBACA,YACA,iBACA,iBACA,qBACA,yBACA,+BACA,aACA,4BACA,SACA,CAAO,gCACP,CACA,CAAG,EACH,oBACA,iBACA,MACA,EACA,OACA,uDACA,aACA,cACA,cACA,eACA,YACA,aACA,gBACA,oBAMA,OAA0B,eAAmB,KAC7C,UACA,YACA,YACA,aACA,UACA,WACA,cACA,sBACA,6CACA,yCACA,mDACA,kBACA,kBACA,mCACA,OApBA,WACA,mBACA,YACA,CAAS,CACT,CAiBA,CAAO,CACP,CACA,CAAG,EACH,qBACA,iBACA,gCACA,0BAEA,CAAG,EACH,oBACA,iBACA,WACA,yCACA,qEAEA,gFAEA,IADA,EACA,mCAAgI,OAAQ,4BACxI,6BACA,YACA,aACA,CAAW,CAEX,CAAO,CACP,CACA,CAAG,EACH,kBACA,iBAEA,8DACA,8BACA,6CAEA,sBACA,CACA,CACA,CAAG,EACH,sBACA,iBACA,wEACA,QACA,QACA,iBACA,cACA,wCACA,4BACA,sBACA,CACA,CAAG,EACH,CAAC,CAAC,WAAS,EACX,iBACA,QACA,YAEA,yBACA,EAEA,0EACA,eACA,aAEA,MADI,OAAe,SACR,OAAU,kBACrB,CAEA,MADE,OAAS,MACF,OAAY,KACrB,aACA,iBAMA,iBACA,SACA,cACA,QACA,yCACA,QACA,uCACA,iBACA,yCACA,qBAEA,EAAgB,OAAwB,OACxC,aACA,WACA,CAAO,EACP,OAA0B,eAAmB,oBAA2B,IACxE,QACA,eACA,YACA,MACA,MACA,eACA,iBAZA,aAaA,CAAO,EACP,CACA,CAAG,EACH,CAAC,CAAC,WAAe,EACjB,uBAEA,OADA,YACA,mBAEA,4BACA,kBACA,UACA,iBACA,wBACA,cACA,uBACA,SACA,QACA,CACA,EACA,uBAEA,OADA,YACA,2BACA,EAEA,0EACA,eACA,aAEA,MADI,OAAe,SACR,OAAU,kBACrB,CAEA,MADE,OAAS,MACF,OAAY,KACrB,aACA,iBAMA,iBACA,SACA,cACA,QACA,yCACA,QACA,uCACA,iBACA,yCACA,qBAEA,EAAgB,OAAwB,OACxC,wBACA,OAA0B,eAAmB,oBAA2B,IACxE,QACA,eACA,YACA,MACA,MACA,eACA,iBAVA,aAWA,CAAO,EACP,CACA,CAAG,EACH,CAAC,CAAC,WAAe,EACjB,gCACA,4BACA,kBACA,UACA,iBACA,yBACA,cACA,wBACA,SACA,QACA,CACA,EACA,uBACA,kBACA,kBACA,wBACA,sBACA,mBACA,EACA,uBACA,kBACA,cACA,WACA,CAAK,EACL,EAAmB,OAAQ,IAC3B,OACA,aACA,iBACA,QACA,WACA,CAAG,wBACH,EAEA,0EACA,iBACA,wCACA,qCACA,CAAG,CACH,CACA,mBACA,aAEA,MADI,OAAe,SACR,OAAU,kBACrB,CAEA,MADE,OAAS,MACF,OAAY,KACrB,aACA,iBAMA,iBACA,SACA,cACA,QACA,yCACA,QACA,uCACA,iBACA,yCACA,qBAEA,EAAgB,OAAwB,OACxC,mBACA,OAA0B,eAAmB,oBAA2B,IACxE,QACA,eACA,YACA,MACA,MACA,eACA,iBAVA,aAWA,CAAO,EACP,CACA,CAAG,EACH,CAAC,CAAC,WAAe,EAmBjB,eACA,kBACA,eACA,SACA,WACA,YACA,WAEA,cACA,uBACA,kBACA,aACA,EAAkB,YAAM,OACxB,EAAmB,YAAM,OACzB,EAAmB,YAAM,OACzB,EAAmB,YAAM,OACzB,EAAiB,YAAM,OACrB,eAAS,YACX,GACA,CAAG,EACH,sBACA,cACA,SAIA,MAHA,wBACA,oDACA,EAAK,EACL,kBACA,iBACA,WACA,aACA,6BACA,wCACA,SAA2C,eAAmB,OAC9D,iBACA,gCACA,CAAO,GAA4B,eAAmB,IACtD,MACA,OACA,CAAO,KACP,OAA0B,eAAmB,OAC7C,YACA,sBACA,cACO,GAAsB,eAAmB,OAChD,gCACA,CAAO,SAA4C,eAAmB,OACtE,kCACA,oBACA,gBACA,CAAS,CACT,0BACA,gBACA,CACA,CAAO,GAAuB,eAAmB,IACjD,QACA,OACA,CAAO,KACP,CAAK,IACL,EACA,gBACA,SACA,SACA,oBACA,WACA,aAiBA,MAhBA,eACA,UACA,iCACQ,kBACR,YACA,QACA,KACA,CAAS,0BACD,kBACR,iCACQ,mBACR,oCAGA,2CACA,6CACwB,eAAmB,SAC3C,kBACA,CAAK,GAA+B,eAAmB,IACvD,QACA,MACA,OACA,CAAK,IACL,EACA,aACA,cACA,gBACA,uBACA,MACA,oDACA,KACA,IACA,GAAe,OAAQ,gBAAwB,OAAQ,iBACvD,6BACA,gCACA,iCAEA,GACM,OAAQ,6BACd,oBAAiC,OAAa,SAExC,OAAW,8BAEjB,EACA,aACA,UAvGA,WARA,GAQA,EAuGA,OACA,qBAOA,MANA,wBACA,oDACA,EAAG,EACH,mBACA,4BACA,CAAG,EACmB,eAAmB,QACzC,2BACA,CAAG,cAAqC,eAAmB,CAAC,UAAc,MAAqB,eAAmB,UAClH,MACA,4BACA,CAAG,CAAe,eAAmB,cAA6B,eAAmB,WAA0B,eAAmB,OAClI,uBACA,KACA,CAAG,SAA+B,eAAmB,OACrD,uBACA,KACA,CAAG,SAA+B,eAAmB,OACrD,sBACA,CAAG,aAAmC,eAAmB,QACzD,+BACA,KACA,CAAG,CAAe,eAAmB,UACrC,4BACA,CAAG,CAAe,eAAmB,UACrC,KACA,CAAG,qBACH,eACA,CAAG,MAAqB,eAAmB,SAC3C,4BACA,CAAG,oBACH,CAjKA,gCACA,YACA,wBACA,uBACA,kBACA,QACA,WACA,CAAK,EACL,EAAsB,OAAQ,IAC9B,OACA,aACA,iBACA,QACA,WACA,CAAG,wBACH,EAmJA,uBACA,eAGA,EADA,YACA,MAFA,WAnJA,GAmJA,EAEA,OACA,OACA,QACA,KACA,CACA,EACA,4BACA,eACA,aA7JA,GA6JA,EACA,cACA,UACA,iBACA,wBACA,cACA,uBACA,SACA,QACA,CACA,EACA,uBACA,eAEA,cACA,UAFA,WA1KA,GA0KA,EAEA,OACA,iBACA,QACA,KACA,CAAG,sBACH,EAEA,OAAY,OAAe,CAAC,OAAe,CAAC,OAAe,CAAC,OAAe,CAAC,OAAe,GAAG,oEAE9F,6BAgCA,eACA,uBALA,EAMA,OANA,OACA,mBAKA,EALA,IAKA,GALuD,iBAKvD,GALoG,WAAP,OAAO,CAKpG,IALoG,MAKpG,GALoG,KAKpG,GALoG,KAKpG,EALoG,EACpG,CAKA,CACA,EAEA,yCACA,0OACA,eACA,oBACA,SAGA,QADA,KACA,wBAA6D,WAA6B,KAC1F,MAA6B,OAAc,SAC3C,OACA,MAEA,SAEA,CACA,QACA,CAMA,mBACA,aACA,MACI,OAAe,SACnB,0CAAyE,IAAa,IACtF,kBAkHA,MAhHA,GAAY,OAAU,uBACtB,oBACA,2BACA,iBACe,IAAS,gBACxB,kBACS,GAAI,EAEI,WAAP,EAJc,CAId,IAAO,IACF,IAAS,sBACxB,OACA,MAEA,CACA,CAAS,EAET,EACA,EACA,qBAEA,OADA,YACA,gBAEA,+BACA,cACA,SACA,kBACA,4BACA,EACA,iCADA,CAEA,EAUA,oCACA,cACA,kBACA,cACA,GACA,SACA,aACA,WACA,CAAW,IAOX,EACA,+BACA,cACA,SACA,SACA,WACA,eACA,EAAgB,OAAwB,OACxC,cACA,MAMA,EALA,EAlIA,cACA,eACA,SACA,UACA,EAAY,OAAwB,OAEpC,OADA,6BACA,GACA,cACA,cACA,KACA,cACA,KACA,SACM,IAAS,wHACf,mBACA,CACA,QACA,EAiHA,EAAqC,OAAa,CAAC,OAAa,GAAG,MAAY,EAC/E,SACA,aACA,OACA,CAAO,GACP,KACA,wBACA,EACA,+BACA,kBAlFA,KADA,GADA,EAoFA,MApFA,OAEA,QAkFA,IACA,kBAEA,mBACA,0DACA,EACA,+BACA,0CAA6E,IAAe,IAC5F,kBAEA,2BACA,EACA,oCACA,0CAA6E,IAAe,IAC5F,kBAEA,gCACA,EACA,iCACA,0CAA6E,IAAe,IAC5F,kBAEA,6BACA,EACA,+BACA,0BACA,EACA,gCACA,0BACA,iBACA,sBAGA,0BACA,2BACA,EACA,SACA,6BACA,EACA,CACA,CAEA,MADE,OAAS,MACF,OAAY,KACrB,aACA,iBACA,iBACA,SACA,YACA,WACA,qBACA,2BACA,UACA,cACA,iBACA,SACA,WACA,WACA,sBACA,eACA,wBACA,aACA,UACA,WACA,UACA,MAAoB,OAAwB,OAC5C,SACA,qBACA,qBACA,cACA,eACA,YACA,cACA,cACA,gBACA,aACA,YACA,QACA,CAAO,EACP,OAA0B,eAAmB,uBAAwB,IACrE,UAAmB,OAAI,oCACvB,OACA,CAAO,KAA2B,eAAmB,IACrD,OACA,OACA,QACA,QACA,6BACA,+BACA,WACA,CAAO,EAAgB,eAAmB,mBAAuB,IACjE,SACA,mBACA,OACA,SACA,SACA,YACA,UACA,aACA,YACA,oBACA,uCACA,+BACA,iCACA,qCACA,+CACA,yCACA,mCACA,aACA,sBACA,wBACA,CAAO,GACP,CACA,CAAG,IACH,+BACA,kBACA,OACA,uBACA,CACA,CACA,CAAG,EACH,iBACA,kBACA,IAz7IA,EArGA,EACA,EA6hJA,kBACA,gBACA,mBACA,oBACA,kBACA,qBACA,uBACA,0BACA,oBACA,oBACA,8BACA,mBACA,wBACA,kBACA,SACA,UACA,cACA,YACA,aAEA,eACA,gBAAsD,EACtD,YAEA,QACA,GAl9IA,EA48IA,cAAoD,EA38I3C,OAAa,CAAC,OAAa,GAAG,SAk9IvC,OACA,YACA,WA1jJA,EAqjJA,cAAgD,EApjJhD,EAAgB,OAAa,CAAC,OAAa,GAAG,eACrC,OAAa,CAAC,OAAa,GAAG,CAwjJvC,GAxjJuC,EAAgB,EACvD,SAujJA,EAtjJA,uBACA,qBAqjJA,EApjJA,CAAK,CACL,qBACA,OAkjJA,EAljJA,iBAkjJA,EAjjJA,CACA,CAAG,GAijJH,SACA,qBACA,uCACA,CAAW,CACX,+BACA,uCACA,CAAW,CACX,oBACA,uCACA,CAAW,CACX,yBACA,uCACA,CAAW,CACX,mBACA,uCACA,CACA,CAAS,CACT,WAAoB,IAAQ,QAAuB,CAAE,IAAI,MACzD,gBACA,gBAF4B,OAE5B,EAFyD,CAGzD,yBACA,mBACA,eACA,mBACA,qBACA,uBACA,CAAS,EACT,WACA,YACA,UACA,aACA,cACA,YACA,eACA,iBACA,oBACA,aACA,CACA,CACA,CACA,CAAG,EACH,CAAC,CAAC,WAAe,EACjB,iBACA,UACA,oBACA,eAAkB,CAClB,SACA,WACA,cACA,0CACA,QACA,UACA,kBACA,uBACA,qBACA,sBACA,wBACA,wBACA,sBACA,kBACA,8BACA,wBACA,8BACA,qBACA,uBACA,kBACA,eACA,CAAG,CACH,4BACA,EACA,OAAiB,QAAc,KAC/B,cACA,kBACA,wBACA,CAAC,EAcD,mBACA,cACA,QACA,kDACA,EASA,IACA,gBACA,mBACA,oBACA,qBACA,wBACA,0BAdA,gBACA,cACA,+BACA,EAYA,wBAXA,gBACA,YACA,8BACA,EASA,sBACA,8BACA,8BACA,qBApCA,gBACA,cACA,QACA,qCAEA,8CACA,EA+BA,mBA9BA,gBACA,cACA,QACA,gDACA,EA2BA,8BACA,sBACA,wBACA,EACA,eACA,0BAMA,MALA,eACA,SACI,GACJ,WAEA,CACA,CACA,eAQA,gBAKA,IAJA,IAIA,eACA,eAEA,SACA,qEAOA,qEAGA,OAFA,2BACA,0BAEA,CAOA,kBACA,YAGA,OAFA,uBACA,uBACA,GAEA,aACA,kEACA,yCACA,eACA,EACA,yBAEA,aACA,CAaA,kBACA,eACA,EAA4B,OAAc,MAC1C,OACA,OACA,OACA,oBACA,CA4BA,kBACA,eACA,EAA6B,OAAc,MAC3C,OACA,OACA,OACA,4BACA,CA4BA,kBACA,YACA,6BACA,CAkBA,gBACA,mEACA,QAEA,OAEA,OADA,KACA,SACA,CASA,cACA,qDACA,CACA,cACA,iDACA,CA8CA,gBACA,WACA,OAEA,OADA,sBACA,MACA,CAqEA,eACA,WACA,YAnIA,YACA,uCACA,6BACA,EAiIA,kBACA,iBACA,YA5HA,YAIA,IAHA,WACA,OACA,KACA,QACA,UACA,aAEA,QACA,EAoHA,2BA3SA,EA4SA,OA5SA,EA4SA,KA3SA,WA2SA,GA3SA,GA2SA,SACA,CAAK,CACL,GA9NA,gBACA,eACA,EAA4B,OAAc,MAC1C,OACA,OACA,OACA,sBACA,EAwNA,MACA,GAxOA,gBACA,eACA,EAA4B,OAAc,MAC1C,OACA,OACA,OACA,qBACA,EAkOA,IAzNA,gBACA,eACA,EAA4B,OAAc,MAC1C,OACA,OACA,OACA,4BACA,EAmNA,KACA,IA9OA,gBACA,eACA,EA6OA,MAxLA,cACA,sBACA,8BACA,2CAEA,mEACA,EAmLA,QA7MA,gBACA,mEACA,QACA,OACA,OACA,OACA,8BACA,EAuMA,UACA,MAtQA,WACA,kEACA,yCACA,eACA,EACA,uBAEA,aACA,EA+PA,MAjLA,cAMA,IALA,mEACA,QAEA,gBACA,KACA,QACA,UACA,WAEA,QACA,EAuKA,MACA,OACA,KAxKA,cACA,YACA,SACA,wBACA,EAqKA,IA5MA,cACA,WACA,OAEA,SADA,SACA,QACA,EAwMA,IAvMA,cACA,WACA,OAEA,OADA,WACA,QACA,EAmMA,QA9JA,YAEA,SADA,GACA,SACA,EA4JA,YA7HA,gBACA,+CACA,EA4HA,kBA7TA,YAEA,wCACA,EA2TA,eACA,YA3HA,cACA,uBACA,EA0HA,uBAzHA,YACA,0BAEA,SADA,GACA,kBA/KA,EADA,EAgLA,GAhLA,eAgLA,EACA,EAsHA,eAnHA,cACA,WACA,OACA,0BACA,EAgHA,eA/GA,gBACA,WACA,OACA,mCACA,EA4GA,WAnGA,YACA,aACA,UACA,QACA,WACA,SACA,UACA,QACA,WACA,wBACA,SACA,SACA,UAEA,KAEA,SAEA,KAEA,GAEA,EA8EA,aA7EA,YACA,cACA,UACA,QACA,UACA,UACA,QACA,sBACA,OACA,OACA,OACA,4BAGA,EADA,sBACA,oDACA,WACA,EA8DA,WA7DA,cACA,WACA,OACA,wBACA,EA0DA,UACA,gBAnDA,WAMA,eACA,gCACA,wBACA,2CAGA,QADA,gBACA,EACA,CAuCA,CAAG,CACH,CAiBA,uBACA,cACA,QACA,gDACA,EAgZA,mBACA,cACA,QACA,gDACA,EA2DA,mBACA,cACA,QACA,mBACA,YACA,CAAG,sBACH,YACA,CAAG,GACH,EAuFA,mBACA,cACA,QACA,kEACA,EA+DA,mBACA,cACA,QACA,kDACA", "sources": ["webpack://_N_E/./node_modules/react-big-calendar/dist/react-big-calendar.esm.js"], "sourcesContent": ["import _objectSpread from '@babel/runtime/helpers/esm/objectSpread2';\nimport _objectWithoutProperties from '@babel/runtime/helpers/esm/objectWithoutProperties';\nimport _typeof from '@babel/runtime/helpers/esm/typeof';\nimport _classCallCheck from '@babel/runtime/helpers/esm/classCallCheck';\nimport _createClass from '@babel/runtime/helpers/esm/createClass';\nimport _callSuper from '@babel/runtime/helpers/esm/callSuper';\nimport _inherits from '@babel/runtime/helpers/esm/inherits';\nimport _slicedToArray from '@babel/runtime/helpers/esm/slicedToArray';\nimport clsx from 'clsx';\nimport React, { useEffect, useLayoutEffect, useRef, createRef, Component, useMemo, useState, useCallback } from 'react';\nimport { uncontrollable } from 'uncontrollable';\nimport PropTypes from 'prop-types';\nimport invariant from 'invariant';\nimport * as dates from 'date-arithmetic';\nimport { inRange as inRange$1, lt, lte, gt, gte, eq, neq, startOf, endOf, add, min, max, minutes } from 'date-arithmetic';\nimport _defineProperty from '@babel/runtime/helpers/esm/defineProperty';\nimport _toConsumableArray from '@babel/runtime/helpers/esm/toConsumableArray';\nimport chunk from 'lodash/chunk';\nimport getPosition$1 from 'dom-helpers/position';\nimport * as animationFrame from 'dom-helpers/animationFrame';\nimport { Overlay } from 'react-overlays';\nimport getOffset from 'dom-helpers/offset';\nimport isEqual$1 from 'lodash/isEqual';\nimport getHeight from 'dom-helpers/height';\nimport qsa from 'dom-helpers/querySelectorAll';\nimport contains from 'dom-helpers/contains';\nimport closest from 'dom-helpers/closest';\nimport listen from 'dom-helpers/listen';\nimport findIndex from 'lodash/findIndex';\nimport range$1 from 'lodash/range';\nimport memoize from 'memoize-one';\nimport getWidth from 'dom-helpers/width';\nimport sortBy from 'lodash/sortBy';\nimport scrollbarSize from 'dom-helpers/scrollbarSize';\nimport _toArray from '@babel/runtime/helpers/esm/toArray';\nimport addClass from 'dom-helpers/addClass';\nimport removeClass from 'dom-helpers/removeClass';\nimport defaults from 'lodash/defaults';\nimport mapValues from 'lodash/mapValues';\nimport omit from 'lodash/omit';\nimport transform from 'lodash/transform';\nimport isBetween from 'dayjs/plugin/isBetween';\nimport isSameOrAfter from 'dayjs/plugin/isSameOrAfter';\nimport isSameOrBefore from 'dayjs/plugin/isSameOrBefore';\nimport localeData from 'dayjs/plugin/localeData';\nimport localizedFormat from 'dayjs/plugin/localizedFormat';\nimport minMax from 'dayjs/plugin/minMax';\nimport utc from 'dayjs/plugin/utc';\nimport isLeapYear from 'dayjs/plugin/isLeapYear';\n\nfunction NoopWrapper(props) {\n  return props.children;\n}\n\nvar navigate = {\n  PREVIOUS: 'PREV',\n  NEXT: 'NEXT',\n  TODAY: 'TODAY',\n  DATE: 'DATE'\n};\nvar views = {\n  MONTH: 'month',\n  WEEK: 'week',\n  WORK_WEEK: 'work_week',\n  DAY: 'day',\n  AGENDA: 'agenda'\n};\n\nvar viewNames$1 = Object.keys(views).map(function (k) {\n  return views[k];\n});\nPropTypes.oneOfType([PropTypes.string, PropTypes.func]);\nPropTypes.any;\nPropTypes.func;\n\n/**\n * accepts either an array of builtin view names:\n *\n * ```\n * views={['month', 'day', 'agenda']}\n * ```\n *\n * or an object hash of the view name and the component (or boolean for builtin)\n *\n * ```\n * views={{\n *   month: true,\n *   week: false,\n *   workweek: WorkWeekViewComponent,\n * }}\n * ```\n */\n\nPropTypes.oneOfType([PropTypes.arrayOf(PropTypes.oneOf(viewNames$1)), PropTypes.objectOf(function (prop, key) {\n  var isBuiltinView = viewNames$1.indexOf(key) !== -1 && typeof prop[key] === 'boolean';\n  if (isBuiltinView) {\n    return null;\n  } else {\n    for (var _len = arguments.length, args = new Array(_len > 2 ? _len - 2 : 0), _key = 2; _key < _len; _key++) {\n      args[_key - 2] = arguments[_key];\n    }\n    return PropTypes.elementType.apply(PropTypes, [prop, key].concat(args));\n  }\n})]);\nPropTypes.oneOfType([PropTypes.oneOf(['overlap', 'no-overlap']), PropTypes.func]);\n\n/* eslint no-fallthrough: off */\nvar MILLI = {\n  seconds: 1000,\n  minutes: 1000 * 60,\n  hours: 1000 * 60 * 60,\n  day: 1000 * 60 * 60 * 24\n};\nfunction firstVisibleDay(date, localizer) {\n  var firstOfMonth = dates.startOf(date, 'month');\n  return dates.startOf(firstOfMonth, 'week', localizer.startOfWeek());\n}\nfunction lastVisibleDay(date, localizer) {\n  var endOfMonth = dates.endOf(date, 'month');\n  return dates.endOf(endOfMonth, 'week', localizer.startOfWeek());\n}\nfunction visibleDays(date, localizer) {\n  var current = firstVisibleDay(date, localizer),\n    last = lastVisibleDay(date, localizer),\n    days = [];\n  while (dates.lte(current, last, 'day')) {\n    days.push(current);\n    current = dates.add(current, 1, 'day');\n  }\n  return days;\n}\nfunction ceil(date, unit) {\n  var floor = dates.startOf(date, unit);\n  return dates.eq(floor, date) ? floor : dates.add(floor, 1, unit);\n}\nfunction range(start, end) {\n  var unit = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : 'day';\n  var current = start,\n    days = [];\n  while (dates.lte(current, end, unit)) {\n    days.push(current);\n    current = dates.add(current, 1, unit);\n  }\n  return days;\n}\nfunction merge(date, time) {\n  if (time == null && date == null) return null;\n  if (time == null) time = new Date();\n  if (date == null) date = new Date();\n  date = dates.startOf(date, 'day');\n  date = dates.hours(date, dates.hours(time));\n  date = dates.minutes(date, dates.minutes(time));\n  date = dates.seconds(date, dates.seconds(time));\n  return dates.milliseconds(date, dates.milliseconds(time));\n}\nfunction isJustDate(date) {\n  return dates.hours(date) === 0 && dates.minutes(date) === 0 && dates.seconds(date) === 0 && dates.milliseconds(date) === 0;\n}\nfunction duration(start, end, unit, firstOfWeek) {\n  if (unit === 'day') unit = 'date';\n  return Math.abs(\n  // eslint-disable-next-line import/namespace\n  dates[unit](start, undefined, firstOfWeek) -\n  // eslint-disable-next-line import/namespace\n  dates[unit](end, undefined, firstOfWeek));\n}\nfunction diff(dateA, dateB, unit) {\n  if (!unit || unit === 'milliseconds') return Math.abs(+dateA - +dateB);\n\n  // the .round() handles an edge case\n  // with DST where the total won't be exact\n  // since one day in the range may be shorter/longer by an hour\n  return Math.round(Math.abs(+dates.startOf(dateA, unit) / MILLI[unit] - +dates.startOf(dateB, unit) / MILLI[unit]));\n}\n\nvar localePropType = PropTypes.oneOfType([PropTypes.string, PropTypes.func]);\nfunction _format(localizer, formatter, value, format, culture) {\n  var result = typeof format === 'function' ? format(value, culture, localizer) : formatter.call(localizer, value, format, culture);\n  invariant(result == null || typeof result === 'string', '`localizer format(..)` must return a string, null, or undefined');\n  return result;\n}\n\n/**\n * This date conversion was moved out of TimeSlots.js, to\n * allow for localizer override\n * @param {Date} dt - The date to start from\n * @param {Number} minutesFromMidnight\n * @param {Number} offset\n * @returns {Date}\n */\nfunction getSlotDate(dt, minutesFromMidnight, offset) {\n  return new Date(dt.getFullYear(), dt.getMonth(), dt.getDate(), 0, minutesFromMidnight + offset, 0, 0);\n}\nfunction getDstOffset(start, end) {\n  return start.getTimezoneOffset() - end.getTimezoneOffset();\n}\n\n// if the start is on a DST-changing day but *after* the moment of DST\n// transition we need to add those extra minutes to our minutesFromMidnight\nfunction getTotalMin(start, end) {\n  return diff(start, end, 'minutes') + getDstOffset(start, end);\n}\nfunction getMinutesFromMidnight(start) {\n  var daystart = startOf(start, 'day');\n  return diff(daystart, start, 'minutes') + getDstOffset(daystart, start);\n}\n\n// These two are used by DateSlotMetrics\nfunction continuesPrior(start, first) {\n  return lt(start, first, 'day');\n}\nfunction continuesAfter(start, end, last) {\n  var singleDayDuration = eq(start, end, 'minutes');\n  return singleDayDuration ? gte(end, last, 'minutes') : gt(end, last, 'minutes');\n}\nfunction daySpan(start, end) {\n  return duration(start, end, 'day');\n}\n\n// These two are used by eventLevels\nfunction sortEvents$1(_ref) {\n  var _ref$evtA = _ref.evtA,\n    aStart = _ref$evtA.start,\n    aEnd = _ref$evtA.end,\n    aAllDay = _ref$evtA.allDay,\n    _ref$evtB = _ref.evtB,\n    bStart = _ref$evtB.start,\n    bEnd = _ref$evtB.end,\n    bAllDay = _ref$evtB.allDay;\n  var startSort = +startOf(aStart, 'day') - +startOf(bStart, 'day');\n  var durA = daySpan(aStart, aEnd);\n  var durB = daySpan(bStart, bEnd);\n  return startSort ||\n  // sort by start Day first\n  durB - durA ||\n  // events spanning multiple days go first\n  !!bAllDay - !!aAllDay ||\n  // then allDay single day events\n  +aStart - +bStart ||\n  // then sort by start time\n  +aEnd - +bEnd // then sort by end time\n  ;\n}\nfunction inEventRange(_ref2) {\n  var _ref2$event = _ref2.event,\n    start = _ref2$event.start,\n    end = _ref2$event.end,\n    _ref2$range = _ref2.range,\n    rangeStart = _ref2$range.start,\n    rangeEnd = _ref2$range.end;\n  var eStart = startOf(start, 'day');\n  var startsBeforeEnd = lte(eStart, rangeEnd, 'day');\n  // when the event is zero duration we need to handle a bit differently\n  var sameMin = neq(eStart, end, 'minutes');\n  var endsAfterStart = sameMin ? gt(end, rangeStart, 'minutes') : gte(end, rangeStart, 'minutes');\n  return startsBeforeEnd && endsAfterStart;\n}\n\n// other localizers treats 'day' and 'date' equality very differently, so we\n// abstract the change the 'localizer.eq(date1, date2, 'day') into this\n// new method, where they can be treated correctly by the localizer overrides\nfunction isSameDate(date1, date2) {\n  return eq(date1, date2, 'day');\n}\nfunction startAndEndAreDateOnly(start, end) {\n  return isJustDate(start) && isJustDate(end);\n}\nvar DateLocalizer = /*#__PURE__*/_createClass(function DateLocalizer(spec) {\n  var _this = this;\n  _classCallCheck(this, DateLocalizer);\n  invariant(typeof spec.format === 'function', 'date localizer `format(..)` must be a function');\n  invariant(typeof spec.firstOfWeek === 'function', 'date localizer `firstOfWeek(..)` must be a function');\n  this.propType = spec.propType || localePropType;\n  this.formats = spec.formats;\n  this.format = function () {\n    for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {\n      args[_key] = arguments[_key];\n    }\n    return _format.apply(void 0, [_this, spec.format].concat(args));\n  };\n  // These date arithmetic methods can be overriden by the localizer\n  this.startOfWeek = spec.firstOfWeek;\n  this.merge = spec.merge || merge;\n  this.inRange = spec.inRange || inRange$1;\n  this.lt = spec.lt || lt;\n  this.lte = spec.lte || lte;\n  this.gt = spec.gt || gt;\n  this.gte = spec.gte || gte;\n  this.eq = spec.eq || eq;\n  this.neq = spec.neq || neq;\n  this.startOf = spec.startOf || startOf;\n  this.endOf = spec.endOf || endOf;\n  this.add = spec.add || add;\n  this.range = spec.range || range;\n  this.diff = spec.diff || diff;\n  this.ceil = spec.ceil || ceil;\n  this.min = spec.min || min;\n  this.max = spec.max || max;\n  this.minutes = spec.minutes || minutes;\n  this.daySpan = spec.daySpan || daySpan;\n  this.firstVisibleDay = spec.firstVisibleDay || firstVisibleDay;\n  this.lastVisibleDay = spec.lastVisibleDay || lastVisibleDay;\n  this.visibleDays = spec.visibleDays || visibleDays;\n  this.getSlotDate = spec.getSlotDate || getSlotDate;\n  this.getTimezoneOffset = spec.getTimezoneOffset || function (value) {\n    return value.getTimezoneOffset();\n  };\n  this.getDstOffset = spec.getDstOffset || getDstOffset;\n  this.getTotalMin = spec.getTotalMin || getTotalMin;\n  this.getMinutesFromMidnight = spec.getMinutesFromMidnight || getMinutesFromMidnight;\n  this.continuesPrior = spec.continuesPrior || continuesPrior;\n  this.continuesAfter = spec.continuesAfter || continuesAfter;\n  this.sortEvents = spec.sortEvents || sortEvents$1;\n  this.inEventRange = spec.inEventRange || inEventRange;\n  this.isSameDate = spec.isSameDate || isSameDate;\n  this.startAndEndAreDateOnly = spec.startAndEndAreDateOnly || startAndEndAreDateOnly;\n  this.segmentOffset = spec.browserTZOffset ? spec.browserTZOffset() : 0;\n});\nfunction mergeWithDefaults(localizer, culture, formatOverrides, messages) {\n  var formats = _objectSpread(_objectSpread({}, localizer.formats), formatOverrides);\n  return _objectSpread(_objectSpread({}, localizer), {}, {\n    messages: messages,\n    startOfWeek: function startOfWeek() {\n      return localizer.startOfWeek(culture);\n    },\n    format: function format(value, _format2) {\n      return localizer.format(value, formats[_format2] || _format2, culture);\n    }\n  });\n}\n\nvar Toolbar = /*#__PURE__*/function (_React$Component) {\n  function Toolbar() {\n    var _this;\n    _classCallCheck(this, Toolbar);\n    for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {\n      args[_key] = arguments[_key];\n    }\n    _this = _callSuper(this, Toolbar, [].concat(args));\n    _this.navigate = function (action) {\n      _this.props.onNavigate(action);\n    };\n    _this.view = function (view) {\n      _this.props.onView(view);\n    };\n    return _this;\n  }\n  _inherits(Toolbar, _React$Component);\n  return _createClass(Toolbar, [{\n    key: \"render\",\n    value: function render() {\n      var _this$props = this.props,\n        messages = _this$props.localizer.messages,\n        label = _this$props.label;\n      return /*#__PURE__*/React.createElement(\"div\", {\n        className: \"rbc-toolbar\"\n      }, /*#__PURE__*/React.createElement(\"span\", {\n        className: \"rbc-btn-group\"\n      }, /*#__PURE__*/React.createElement(\"button\", {\n        type: \"button\",\n        onClick: this.navigate.bind(null, navigate.TODAY)\n      }, messages.today), /*#__PURE__*/React.createElement(\"button\", {\n        type: \"button\",\n        onClick: this.navigate.bind(null, navigate.PREVIOUS)\n      }, messages.previous), /*#__PURE__*/React.createElement(\"button\", {\n        type: \"button\",\n        onClick: this.navigate.bind(null, navigate.NEXT)\n      }, messages.next)), /*#__PURE__*/React.createElement(\"span\", {\n        className: \"rbc-toolbar-label\"\n      }, label), /*#__PURE__*/React.createElement(\"span\", {\n        className: \"rbc-btn-group\"\n      }, this.viewNamesGroup(messages)));\n    }\n  }, {\n    key: \"viewNamesGroup\",\n    value: function viewNamesGroup(messages) {\n      var _this2 = this;\n      var viewNames = this.props.views;\n      var view = this.props.view;\n      if (viewNames.length > 1) {\n        return viewNames.map(function (name) {\n          return /*#__PURE__*/React.createElement(\"button\", {\n            type: \"button\",\n            key: name,\n            className: clsx({\n              'rbc-active': view === name\n            }),\n            onClick: _this2.view.bind(null, name)\n          }, messages[name]);\n        });\n      }\n    }\n  }]);\n}(React.Component);\n\nfunction notify(handler, args) {\n  handler && handler.apply(null, [].concat(args));\n}\n\nvar defaultMessages = {\n  date: 'Date',\n  time: 'Time',\n  event: 'Event',\n  allDay: 'All Day',\n  week: 'Week',\n  work_week: 'Work Week',\n  day: 'Day',\n  month: 'Month',\n  previous: 'Back',\n  next: 'Next',\n  yesterday: 'Yesterday',\n  tomorrow: 'Tomorrow',\n  today: 'Today',\n  agenda: 'Agenda',\n  noEventsInRange: 'There are no events in this range.',\n  showMore: function showMore(total) {\n    return \"+\".concat(total, \" more\");\n  }\n};\nfunction messages(msgs) {\n  return _objectSpread(_objectSpread({}, defaultMessages), msgs);\n}\n\nfunction useClickOutside(_ref) {\n  var ref = _ref.ref,\n    callback = _ref.callback;\n  useEffect(function () {\n    var handleClickOutside = function handleClickOutside(e) {\n      if (ref.current && !ref.current.contains(e.target)) {\n        callback();\n      }\n    };\n    document.addEventListener('mousedown', handleClickOutside);\n    return function () {\n      document.removeEventListener('mousedown', handleClickOutside);\n    };\n  }, [ref, callback]);\n}\n\nvar _excluded$7 = [\"style\", \"className\", \"event\", \"selected\", \"isAllDay\", \"onSelect\", \"onDoubleClick\", \"onKeyPress\", \"localizer\", \"continuesPrior\", \"continuesAfter\", \"accessors\", \"getters\", \"children\", \"components\", \"slotStart\", \"slotEnd\"];\nvar EventCell = /*#__PURE__*/function (_React$Component) {\n  function EventCell() {\n    _classCallCheck(this, EventCell);\n    return _callSuper(this, EventCell, arguments);\n  }\n  _inherits(EventCell, _React$Component);\n  return _createClass(EventCell, [{\n    key: \"render\",\n    value: function render() {\n      var _this$props = this.props,\n        style = _this$props.style,\n        className = _this$props.className,\n        event = _this$props.event,\n        selected = _this$props.selected,\n        isAllDay = _this$props.isAllDay,\n        onSelect = _this$props.onSelect,\n        _onDoubleClick = _this$props.onDoubleClick,\n        onKeyPress = _this$props.onKeyPress,\n        localizer = _this$props.localizer,\n        continuesPrior = _this$props.continuesPrior,\n        continuesAfter = _this$props.continuesAfter,\n        accessors = _this$props.accessors,\n        getters = _this$props.getters,\n        children = _this$props.children,\n        _this$props$component = _this$props.components,\n        Event = _this$props$component.event,\n        EventWrapper = _this$props$component.eventWrapper,\n        slotStart = _this$props.slotStart,\n        slotEnd = _this$props.slotEnd,\n        props = _objectWithoutProperties(_this$props, _excluded$7);\n      delete props.resizable;\n      var title = accessors.title(event);\n      var tooltip = accessors.tooltip(event);\n      var end = accessors.end(event);\n      var start = accessors.start(event);\n      var allDay = accessors.allDay(event);\n      var showAsAllDay = isAllDay || allDay || localizer.diff(start, localizer.ceil(end, 'day'), 'day') > 1;\n      var userProps = getters.eventProp(event, start, end, selected);\n      var content = /*#__PURE__*/React.createElement(\"div\", {\n        className: \"rbc-event-content\",\n        title: tooltip || undefined\n      }, Event ? /*#__PURE__*/React.createElement(Event, {\n        event: event,\n        continuesPrior: continuesPrior,\n        continuesAfter: continuesAfter,\n        title: title,\n        isAllDay: allDay,\n        localizer: localizer,\n        slotStart: slotStart,\n        slotEnd: slotEnd\n      }) : title);\n      return /*#__PURE__*/React.createElement(EventWrapper, Object.assign({}, this.props, {\n        type: \"date\"\n      }), /*#__PURE__*/React.createElement(\"div\", Object.assign({}, props, {\n        style: _objectSpread(_objectSpread({}, userProps.style), style),\n        className: clsx('rbc-event', className, userProps.className, {\n          'rbc-selected': selected,\n          'rbc-event-allday': showAsAllDay,\n          'rbc-event-continues-prior': continuesPrior,\n          'rbc-event-continues-after': continuesAfter\n        }),\n        onClick: function onClick(e) {\n          return onSelect && onSelect(event, e);\n        },\n        onDoubleClick: function onDoubleClick(e) {\n          return _onDoubleClick && _onDoubleClick(event, e);\n        },\n        onKeyDown: function onKeyDown(e) {\n          return onKeyPress && onKeyPress(event, e);\n        }\n      }), typeof children === 'function' ? children(content) : content));\n    }\n  }]);\n}(React.Component);\n\nfunction isSelected(event, selected) {\n  if (!event || selected == null) return false;\n  return isEqual$1(event, selected);\n}\nfunction slotWidth(rowBox, slots) {\n  var rowWidth = rowBox.right - rowBox.left;\n  var cellWidth = rowWidth / slots;\n  return cellWidth;\n}\nfunction getSlotAtX(rowBox, x, rtl, slots) {\n  var cellWidth = slotWidth(rowBox, slots);\n  return rtl ? slots - 1 - Math.floor((x - rowBox.left) / cellWidth) : Math.floor((x - rowBox.left) / cellWidth);\n}\nfunction pointInBox(box, _ref) {\n  var x = _ref.x,\n    y = _ref.y;\n  return y >= box.top && y <= box.bottom && x >= box.left && x <= box.right;\n}\nfunction dateCellSelection(start, rowBox, box, slots, rtl) {\n  var startIdx = -1;\n  var endIdx = -1;\n  var lastSlotIdx = slots - 1;\n  var cellWidth = slotWidth(rowBox, slots);\n\n  // cell under the mouse\n  var currentSlot = getSlotAtX(rowBox, box.x, rtl, slots);\n\n  // Identify row as either the initial row\n  // or the row under the current mouse point\n  var isCurrentRow = rowBox.top < box.y && rowBox.bottom > box.y;\n  var isStartRow = rowBox.top < start.y && rowBox.bottom > start.y;\n\n  // this row's position relative to the start point\n  var isAboveStart = start.y > rowBox.bottom;\n  var isBelowStart = rowBox.top > start.y;\n  var isBetween = box.top < rowBox.top && box.bottom > rowBox.bottom;\n\n  // this row is between the current and start rows, so entirely selected\n  if (isBetween) {\n    startIdx = 0;\n    endIdx = lastSlotIdx;\n  }\n  if (isCurrentRow) {\n    if (isBelowStart) {\n      startIdx = 0;\n      endIdx = currentSlot;\n    } else if (isAboveStart) {\n      startIdx = currentSlot;\n      endIdx = lastSlotIdx;\n    }\n  }\n  if (isStartRow) {\n    // select the cell under the initial point\n    startIdx = endIdx = rtl ? lastSlotIdx - Math.floor((start.x - rowBox.left) / cellWidth) : Math.floor((start.x - rowBox.left) / cellWidth);\n    if (isCurrentRow) {\n      if (currentSlot < startIdx) startIdx = currentSlot;else endIdx = currentSlot; //select current range\n    } else if (start.y < box.y) {\n      // the current row is below start row\n      // select cells to the right of the start cell\n      endIdx = lastSlotIdx;\n    } else {\n      // select cells to the left of the start cell\n      startIdx = 0;\n    }\n  }\n  return {\n    startIdx: startIdx,\n    endIdx: endIdx\n  };\n}\n\n/**\n * Changes to react-overlays cause issue with auto positioning,\n * so we need to manually calculate the position of the popper,\n * and constrain it to the Month container.\n */\nfunction getPosition(_ref) {\n  var target = _ref.target,\n    offset = _ref.offset,\n    container = _ref.container,\n    box = _ref.box;\n  var _getOffset = getOffset(target),\n    top = _getOffset.top,\n    left = _getOffset.left,\n    width = _getOffset.width,\n    height = _getOffset.height;\n  var _getOffset2 = getOffset(container),\n    cTop = _getOffset2.top,\n    cLeft = _getOffset2.left,\n    cWidth = _getOffset2.width,\n    cHeight = _getOffset2.height;\n  var _getOffset3 = getOffset(box),\n    bWidth = _getOffset3.width,\n    bHeight = _getOffset3.height;\n  var viewBottom = cTop + cHeight;\n  var viewRight = cLeft + cWidth;\n  var bottom = top + bHeight;\n  var right = left + bWidth;\n  var x = offset.x,\n    y = offset.y;\n  var topOffset = bottom > viewBottom ? top - bHeight - y : top + y + height;\n  var leftOffset = right > viewRight ? left + x - bWidth + width : left + x;\n  return {\n    topOffset: topOffset,\n    leftOffset: leftOffset\n  };\n}\nfunction Pop(_ref2) {\n  var containerRef = _ref2.containerRef,\n    accessors = _ref2.accessors,\n    getters = _ref2.getters,\n    selected = _ref2.selected,\n    components = _ref2.components,\n    localizer = _ref2.localizer,\n    position = _ref2.position,\n    show = _ref2.show,\n    events = _ref2.events,\n    slotStart = _ref2.slotStart,\n    slotEnd = _ref2.slotEnd,\n    onSelect = _ref2.onSelect,\n    onDoubleClick = _ref2.onDoubleClick,\n    onKeyPress = _ref2.onKeyPress,\n    handleDragStart = _ref2.handleDragStart,\n    popperRef = _ref2.popperRef,\n    target = _ref2.target,\n    offset = _ref2.offset;\n  useClickOutside({\n    ref: popperRef,\n    callback: show\n  });\n  useLayoutEffect(function () {\n    var _getPosition = getPosition({\n        target: target,\n        offset: offset,\n        container: containerRef.current,\n        box: popperRef.current\n      }),\n      topOffset = _getPosition.topOffset,\n      leftOffset = _getPosition.leftOffset;\n    popperRef.current.style.top = \"\".concat(topOffset, \"px\");\n    popperRef.current.style.left = \"\".concat(leftOffset, \"px\");\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n  }, [offset.x, offset.y, target]);\n  var width = position.width;\n  var style = {\n    minWidth: width + width / 2\n  };\n  return /*#__PURE__*/React.createElement(\"div\", {\n    style: style,\n    className: \"rbc-overlay\",\n    ref: popperRef\n  }, /*#__PURE__*/React.createElement(\"div\", {\n    className: \"rbc-overlay-header\"\n  }, localizer.format(slotStart, 'dayHeaderFormat')), events.map(function (event, idx) {\n    return /*#__PURE__*/React.createElement(EventCell, {\n      key: idx,\n      type: \"popup\",\n      localizer: localizer,\n      event: event,\n      getters: getters,\n      onSelect: onSelect,\n      accessors: accessors,\n      components: components,\n      onDoubleClick: onDoubleClick,\n      onKeyPress: onKeyPress,\n      continuesPrior: localizer.lt(accessors.end(event), slotStart, 'day'),\n      continuesAfter: localizer.gte(accessors.start(event), slotEnd, 'day'),\n      slotStart: slotStart,\n      slotEnd: slotEnd,\n      selected: isSelected(event, selected),\n      draggable: true,\n      onDragStart: function onDragStart() {\n        return handleDragStart(event);\n      },\n      onDragEnd: function onDragEnd() {\n        return show();\n      }\n    });\n  }));\n}\nvar Popup = /*#__PURE__*/React.forwardRef(function (props, ref) {\n  return /*#__PURE__*/React.createElement(Pop, Object.assign({}, props, {\n    popperRef: ref\n  }));\n});\nPopup.propTypes = {\n  accessors: PropTypes.object.isRequired,\n  getters: PropTypes.object.isRequired,\n  selected: PropTypes.object,\n  components: PropTypes.object.isRequired,\n  localizer: PropTypes.object.isRequired,\n  position: PropTypes.object.isRequired,\n  show: PropTypes.func.isRequired,\n  events: PropTypes.array.isRequired,\n  slotStart: PropTypes.instanceOf(Date).isRequired,\n  slotEnd: PropTypes.instanceOf(Date),\n  onSelect: PropTypes.func,\n  onDoubleClick: PropTypes.func,\n  onKeyPress: PropTypes.func,\n  handleDragStart: PropTypes.func,\n  style: PropTypes.object,\n  offset: PropTypes.shape({\n    x: PropTypes.number,\n    y: PropTypes.number\n  })\n};\n\nfunction CalOverlay(_ref) {\n  var containerRef = _ref.containerRef,\n    _ref$popupOffset = _ref.popupOffset,\n    popupOffset = _ref$popupOffset === void 0 ? 5 : _ref$popupOffset,\n    overlay = _ref.overlay,\n    accessors = _ref.accessors,\n    localizer = _ref.localizer,\n    components = _ref.components,\n    getters = _ref.getters,\n    selected = _ref.selected,\n    handleSelectEvent = _ref.handleSelectEvent,\n    handleDoubleClickEvent = _ref.handleDoubleClickEvent,\n    handleKeyPressEvent = _ref.handleKeyPressEvent,\n    handleDragStart = _ref.handleDragStart,\n    onHide = _ref.onHide,\n    overlayDisplay = _ref.overlayDisplay;\n  var popperRef = useRef(null);\n  if (!overlay.position) return null;\n  var offset = popupOffset;\n  if (!isNaN(popupOffset)) {\n    offset = {\n      x: popupOffset,\n      y: popupOffset\n    };\n  }\n  var position = overlay.position,\n    events = overlay.events,\n    date = overlay.date,\n    end = overlay.end;\n  return /*#__PURE__*/React.createElement(Overlay, {\n    rootClose: true,\n    flip: true,\n    show: true,\n    placement: \"bottom\",\n    onHide: onHide,\n    target: overlay.target\n  }, function (_ref2) {\n    var props = _ref2.props;\n    return /*#__PURE__*/React.createElement(Popup, Object.assign({}, props, {\n      containerRef: containerRef,\n      ref: popperRef,\n      target: overlay.target,\n      offset: offset,\n      accessors: accessors,\n      getters: getters,\n      selected: selected,\n      components: components,\n      localizer: localizer,\n      position: position,\n      show: overlayDisplay,\n      events: events,\n      slotStart: date,\n      slotEnd: end,\n      onSelect: handleSelectEvent,\n      onDoubleClick: handleDoubleClickEvent,\n      onKeyPress: handleKeyPressEvent,\n      handleDragStart: handleDragStart\n    }));\n  });\n}\nvar PopOverlay = /*#__PURE__*/React.forwardRef(function (props, ref) {\n  return /*#__PURE__*/React.createElement(CalOverlay, Object.assign({}, props, {\n    containerRef: ref\n  }));\n});\nPopOverlay.propTypes = {\n  popupOffset: PropTypes.oneOfType([PropTypes.number, PropTypes.shape({\n    x: PropTypes.number,\n    y: PropTypes.number\n  })]),\n  overlay: PropTypes.shape({\n    position: PropTypes.object,\n    events: PropTypes.array,\n    date: PropTypes.instanceOf(Date),\n    end: PropTypes.instanceOf(Date)\n  }),\n  accessors: PropTypes.object.isRequired,\n  localizer: PropTypes.object.isRequired,\n  components: PropTypes.object.isRequired,\n  getters: PropTypes.object.isRequired,\n  selected: PropTypes.object,\n  handleSelectEvent: PropTypes.func,\n  handleDoubleClickEvent: PropTypes.func,\n  handleKeyPressEvent: PropTypes.func,\n  handleDragStart: PropTypes.func,\n  onHide: PropTypes.func,\n  overlayDisplay: PropTypes.func\n};\n\nfunction addEventListener(type, handler) {\n  var target = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : document;\n  return listen(target, type, handler, {\n    passive: false\n  });\n}\nfunction isOverContainer(container, x, y) {\n  return !container || contains(container, document.elementFromPoint(x, y));\n}\nfunction getEventNodeFromPoint(node, _ref) {\n  var clientX = _ref.clientX,\n    clientY = _ref.clientY;\n  var target = document.elementFromPoint(clientX, clientY);\n  return closest(target, '.rbc-event', node);\n}\nfunction getShowMoreNodeFromPoint(node, _ref2) {\n  var clientX = _ref2.clientX,\n    clientY = _ref2.clientY;\n  var target = document.elementFromPoint(clientX, clientY);\n  return closest(target, '.rbc-show-more', node);\n}\nfunction isEvent(node, bounds) {\n  return !!getEventNodeFromPoint(node, bounds);\n}\nfunction isShowMore(node, bounds) {\n  return !!getShowMoreNodeFromPoint(node, bounds);\n}\nfunction getEventCoordinates(e) {\n  var target = e;\n  if (e.touches && e.touches.length) {\n    target = e.touches[0];\n  }\n  return {\n    clientX: target.clientX,\n    clientY: target.clientY,\n    pageX: target.pageX,\n    pageY: target.pageY\n  };\n}\nvar clickTolerance = 5;\nvar clickInterval = 250;\nvar Selection = /*#__PURE__*/function () {\n  function Selection(node) {\n    var _ref3 = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {},\n      _ref3$global = _ref3.global,\n      global = _ref3$global === void 0 ? false : _ref3$global,\n      _ref3$longPressThresh = _ref3.longPressThreshold,\n      longPressThreshold = _ref3$longPressThresh === void 0 ? 250 : _ref3$longPressThresh,\n      _ref3$validContainers = _ref3.validContainers,\n      validContainers = _ref3$validContainers === void 0 ? [] : _ref3$validContainers;\n    _classCallCheck(this, Selection);\n    this._initialEvent = null;\n    this.selecting = false;\n    this.isDetached = false;\n    this.container = node;\n    this.globalMouse = !node || global;\n    this.longPressThreshold = longPressThreshold;\n    this.validContainers = validContainers;\n    this._listeners = Object.create(null);\n    this._handleInitialEvent = this._handleInitialEvent.bind(this);\n    this._handleMoveEvent = this._handleMoveEvent.bind(this);\n    this._handleTerminatingEvent = this._handleTerminatingEvent.bind(this);\n    this._keyListener = this._keyListener.bind(this);\n    this._dropFromOutsideListener = this._dropFromOutsideListener.bind(this);\n    this._dragOverFromOutsideListener = this._dragOverFromOutsideListener.bind(this);\n\n    // Fixes an iOS 10 bug where scrolling could not be prevented on the window.\n    // https://github.com/metafizzy/flickity/issues/457#issuecomment-254501356\n    this._removeTouchMoveWindowListener = addEventListener('touchmove', function () {}, window);\n    this._removeKeyDownListener = addEventListener('keydown', this._keyListener);\n    this._removeKeyUpListener = addEventListener('keyup', this._keyListener);\n    this._removeDropFromOutsideListener = addEventListener('drop', this._dropFromOutsideListener);\n    this._removeDragOverFromOutsideListener = addEventListener('dragover', this._dragOverFromOutsideListener);\n    this._addInitialEventListener();\n  }\n  return _createClass(Selection, [{\n    key: \"on\",\n    value: function on(type, handler) {\n      var handlers = this._listeners[type] || (this._listeners[type] = []);\n      handlers.push(handler);\n      return {\n        remove: function remove() {\n          var idx = handlers.indexOf(handler);\n          if (idx !== -1) handlers.splice(idx, 1);\n        }\n      };\n    }\n  }, {\n    key: \"emit\",\n    value: function emit(type) {\n      for (var _len = arguments.length, args = new Array(_len > 1 ? _len - 1 : 0), _key = 1; _key < _len; _key++) {\n        args[_key - 1] = arguments[_key];\n      }\n      var result;\n      var handlers = this._listeners[type] || [];\n      handlers.forEach(function (fn) {\n        if (result === undefined) result = fn.apply(void 0, args);\n      });\n      return result;\n    }\n  }, {\n    key: \"teardown\",\n    value: function teardown() {\n      this._initialEvent = null;\n      this._initialEventData = null;\n      this._selectRect = null;\n      this.selecting = false;\n      this._lastClickData = null;\n      this.isDetached = true;\n      this._listeners = Object.create(null);\n      this._removeTouchMoveWindowListener && this._removeTouchMoveWindowListener();\n      this._removeInitialEventListener && this._removeInitialEventListener();\n      this._removeEndListener && this._removeEndListener();\n      this._onEscListener && this._onEscListener();\n      this._removeMoveListener && this._removeMoveListener();\n      this._removeKeyUpListener && this._removeKeyUpListener();\n      this._removeKeyDownListener && this._removeKeyDownListener();\n      this._removeDropFromOutsideListener && this._removeDropFromOutsideListener();\n      this._removeDragOverFromOutsideListener && this._removeDragOverFromOutsideListener();\n    }\n  }, {\n    key: \"isSelected\",\n    value: function isSelected(node) {\n      var box = this._selectRect;\n      if (!box || !this.selecting) return false;\n      return objectsCollide(box, getBoundsForNode(node));\n    }\n  }, {\n    key: \"filter\",\n    value: function filter(items) {\n      var box = this._selectRect;\n\n      //not selecting\n      if (!box || !this.selecting) return [];\n      return items.filter(this.isSelected, this);\n    }\n\n    // Adds a listener that will call the handler only after the user has pressed on the screen\n    // without moving their finger for 250ms.\n  }, {\n    key: \"_addLongPressListener\",\n    value: function _addLongPressListener(handler, initialEvent) {\n      var _this = this;\n      var timer = null;\n      var removeTouchMoveListener = null;\n      var removeTouchEndListener = null;\n      var handleTouchStart = function handleTouchStart(initialEvent) {\n        timer = setTimeout(function () {\n          cleanup();\n          handler(initialEvent);\n        }, _this.longPressThreshold);\n        removeTouchMoveListener = addEventListener('touchmove', function () {\n          return cleanup();\n        });\n        removeTouchEndListener = addEventListener('touchend', function () {\n          return cleanup();\n        });\n      };\n      var removeTouchStartListener = addEventListener('touchstart', handleTouchStart);\n      var cleanup = function cleanup() {\n        if (timer) {\n          clearTimeout(timer);\n        }\n        if (removeTouchMoveListener) {\n          removeTouchMoveListener();\n        }\n        if (removeTouchEndListener) {\n          removeTouchEndListener();\n        }\n        timer = null;\n        removeTouchMoveListener = null;\n        removeTouchEndListener = null;\n      };\n      if (initialEvent) {\n        handleTouchStart(initialEvent);\n      }\n      return function () {\n        cleanup();\n        removeTouchStartListener();\n      };\n    }\n\n    // Listen for mousedown and touchstart events. When one is received, disable the other and setup\n    // future event handling based on the type of event.\n  }, {\n    key: \"_addInitialEventListener\",\n    value: function _addInitialEventListener() {\n      var _this2 = this;\n      var removeMouseDownListener = addEventListener('mousedown', function (e) {\n        _this2._removeInitialEventListener();\n        _this2._handleInitialEvent(e);\n        _this2._removeInitialEventListener = addEventListener('mousedown', _this2._handleInitialEvent);\n      });\n      var removeTouchStartListener = addEventListener('touchstart', function (e) {\n        _this2._removeInitialEventListener();\n        _this2._removeInitialEventListener = _this2._addLongPressListener(_this2._handleInitialEvent, e);\n      });\n      this._removeInitialEventListener = function () {\n        removeMouseDownListener();\n        removeTouchStartListener();\n      };\n    }\n  }, {\n    key: \"_dropFromOutsideListener\",\n    value: function _dropFromOutsideListener(e) {\n      var _getEventCoordinates = getEventCoordinates(e),\n        pageX = _getEventCoordinates.pageX,\n        pageY = _getEventCoordinates.pageY,\n        clientX = _getEventCoordinates.clientX,\n        clientY = _getEventCoordinates.clientY;\n      this.emit('dropFromOutside', {\n        x: pageX,\n        y: pageY,\n        clientX: clientX,\n        clientY: clientY\n      });\n      e.preventDefault();\n    }\n  }, {\n    key: \"_dragOverFromOutsideListener\",\n    value: function _dragOverFromOutsideListener(e) {\n      var _getEventCoordinates2 = getEventCoordinates(e),\n        pageX = _getEventCoordinates2.pageX,\n        pageY = _getEventCoordinates2.pageY,\n        clientX = _getEventCoordinates2.clientX,\n        clientY = _getEventCoordinates2.clientY;\n      this.emit('dragOverFromOutside', {\n        x: pageX,\n        y: pageY,\n        clientX: clientX,\n        clientY: clientY\n      });\n      e.preventDefault();\n    }\n  }, {\n    key: \"_handleInitialEvent\",\n    value: function _handleInitialEvent(e) {\n      this._initialEvent = e;\n      if (this.isDetached) {\n        return;\n      }\n      var _getEventCoordinates3 = getEventCoordinates(e),\n        clientX = _getEventCoordinates3.clientX,\n        clientY = _getEventCoordinates3.clientY,\n        pageX = _getEventCoordinates3.pageX,\n        pageY = _getEventCoordinates3.pageY;\n      var node = this.container(),\n        collides,\n        offsetData;\n\n      // Right clicks\n      if (e.which === 3 || e.button === 2 || !isOverContainer(node, clientX, clientY)) return;\n      if (!this.globalMouse && node && !contains(node, e.target)) {\n        var _normalizeDistance = normalizeDistance(0),\n          top = _normalizeDistance.top,\n          left = _normalizeDistance.left,\n          bottom = _normalizeDistance.bottom,\n          right = _normalizeDistance.right;\n        offsetData = getBoundsForNode(node);\n        collides = objectsCollide({\n          top: offsetData.top - top,\n          left: offsetData.left - left,\n          bottom: offsetData.bottom + bottom,\n          right: offsetData.right + right\n        }, {\n          top: pageY,\n          left: pageX\n        });\n        if (!collides) return;\n      }\n      var result = this.emit('beforeSelect', this._initialEventData = {\n        isTouch: /^touch/.test(e.type),\n        x: pageX,\n        y: pageY,\n        clientX: clientX,\n        clientY: clientY\n      });\n      if (result === false) return;\n      switch (e.type) {\n        case 'mousedown':\n          this._removeEndListener = addEventListener('mouseup', this._handleTerminatingEvent);\n          this._onEscListener = addEventListener('keydown', this._handleTerminatingEvent);\n          this._removeMoveListener = addEventListener('mousemove', this._handleMoveEvent);\n          break;\n        case 'touchstart':\n          this._handleMoveEvent(e);\n          this._removeEndListener = addEventListener('touchend', this._handleTerminatingEvent);\n          this._removeMoveListener = addEventListener('touchmove', this._handleMoveEvent);\n          break;\n      }\n    }\n\n    // Check whether provided event target element\n    // - is contained within a valid container\n  }, {\n    key: \"_isWithinValidContainer\",\n    value: function _isWithinValidContainer(e) {\n      var eventTarget = e.target;\n      var containers = this.validContainers;\n      if (!containers || !containers.length || !eventTarget) {\n        return true;\n      }\n      return containers.some(function (target) {\n        return !!eventTarget.closest(target);\n      });\n    }\n  }, {\n    key: \"_handleTerminatingEvent\",\n    value: function _handleTerminatingEvent(e) {\n      var selecting = this.selecting;\n      var bounds = this._selectRect;\n      // If it's not in selecting state, it's a click event\n      if (!selecting && e.type.includes('key')) {\n        e = this._initialEvent;\n      }\n      this.selecting = false;\n      this._removeEndListener && this._removeEndListener();\n      this._removeMoveListener && this._removeMoveListener();\n      this._selectRect = null;\n      this._initialEvent = null;\n      this._initialEventData = null;\n      if (!e) return;\n      var inRoot = !this.container || contains(this.container(), e.target);\n      var isWithinValidContainer = this._isWithinValidContainer(e);\n      if (e.key === 'Escape' || !isWithinValidContainer) {\n        return this.emit('reset');\n      }\n      if (!selecting && inRoot) {\n        return this._handleClickEvent(e);\n      }\n\n      // User drag-clicked in the Selectable area\n      if (selecting) return this.emit('select', bounds);\n      return this.emit('reset');\n    }\n  }, {\n    key: \"_handleClickEvent\",\n    value: function _handleClickEvent(e) {\n      var _getEventCoordinates4 = getEventCoordinates(e),\n        pageX = _getEventCoordinates4.pageX,\n        pageY = _getEventCoordinates4.pageY,\n        clientX = _getEventCoordinates4.clientX,\n        clientY = _getEventCoordinates4.clientY;\n      var now = new Date().getTime();\n      if (this._lastClickData && now - this._lastClickData.timestamp < clickInterval) {\n        // Double click event\n        this._lastClickData = null;\n        return this.emit('doubleClick', {\n          x: pageX,\n          y: pageY,\n          clientX: clientX,\n          clientY: clientY\n        });\n      }\n\n      // Click event\n      this._lastClickData = {\n        timestamp: now\n      };\n      return this.emit('click', {\n        x: pageX,\n        y: pageY,\n        clientX: clientX,\n        clientY: clientY\n      });\n    }\n  }, {\n    key: \"_handleMoveEvent\",\n    value: function _handleMoveEvent(e) {\n      if (this._initialEventData === null || this.isDetached) {\n        return;\n      }\n      var _this$_initialEventDa = this._initialEventData,\n        x = _this$_initialEventDa.x,\n        y = _this$_initialEventDa.y;\n      var _getEventCoordinates5 = getEventCoordinates(e),\n        pageX = _getEventCoordinates5.pageX,\n        pageY = _getEventCoordinates5.pageY;\n      var w = Math.abs(x - pageX);\n      var h = Math.abs(y - pageY);\n      var left = Math.min(pageX, x),\n        top = Math.min(pageY, y),\n        old = this.selecting;\n      var click = this.isClick(pageX, pageY);\n      // Prevent emitting selectStart event until mouse is moved.\n      // in Chrome on Windows, mouseMove event may be fired just after mouseDown event.\n      if (click && !old && !(w || h)) {\n        return;\n      }\n      if (!old && !click) {\n        this.emit('selectStart', this._initialEventData);\n      }\n      if (!click) {\n        this.selecting = true;\n        this._selectRect = {\n          top: top,\n          left: left,\n          x: pageX,\n          y: pageY,\n          right: left + w,\n          bottom: top + h\n        };\n        this.emit('selecting', this._selectRect);\n      }\n      e.preventDefault();\n    }\n  }, {\n    key: \"_keyListener\",\n    value: function _keyListener(e) {\n      this.ctrl = e.metaKey || e.ctrlKey;\n    }\n  }, {\n    key: \"isClick\",\n    value: function isClick(pageX, pageY) {\n      var _this$_initialEventDa2 = this._initialEventData,\n        x = _this$_initialEventDa2.x,\n        y = _this$_initialEventDa2.y,\n        isTouch = _this$_initialEventDa2.isTouch;\n      return !isTouch && Math.abs(pageX - x) <= clickTolerance && Math.abs(pageY - y) <= clickTolerance;\n    }\n  }]);\n}();\n/**\n * Resolve the disance prop from either an Int or an Object\n * @return {Object}\n */\nfunction normalizeDistance() {\n  var distance = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : 0;\n  if (_typeof(distance) !== 'object') distance = {\n    top: distance,\n    left: distance,\n    right: distance,\n    bottom: distance\n  };\n  return distance;\n}\n\n/**\n * Given two objects containing \"top\", \"left\", \"offsetWidth\" and \"offsetHeight\"\n * properties, determine if they collide.\n * @param  {Object|HTMLElement} a\n * @param  {Object|HTMLElement} b\n * @return {bool}\n */\nfunction objectsCollide(nodeA, nodeB) {\n  var tolerance = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : 0;\n  var _getBoundsForNode = getBoundsForNode(nodeA),\n    aTop = _getBoundsForNode.top,\n    aLeft = _getBoundsForNode.left,\n    _getBoundsForNode$rig = _getBoundsForNode.right,\n    aRight = _getBoundsForNode$rig === void 0 ? aLeft : _getBoundsForNode$rig,\n    _getBoundsForNode$bot = _getBoundsForNode.bottom,\n    aBottom = _getBoundsForNode$bot === void 0 ? aTop : _getBoundsForNode$bot;\n  var _getBoundsForNode2 = getBoundsForNode(nodeB),\n    bTop = _getBoundsForNode2.top,\n    bLeft = _getBoundsForNode2.left,\n    _getBoundsForNode2$ri = _getBoundsForNode2.right,\n    bRight = _getBoundsForNode2$ri === void 0 ? bLeft : _getBoundsForNode2$ri,\n    _getBoundsForNode2$bo = _getBoundsForNode2.bottom,\n    bBottom = _getBoundsForNode2$bo === void 0 ? bTop : _getBoundsForNode2$bo;\n  return !(\n  // 'a' bottom doesn't touch 'b' top\n\n  aBottom - tolerance < bTop ||\n  // 'a' top doesn't touch 'b' bottom\n  aTop + tolerance > bBottom ||\n  // 'a' right doesn't touch 'b' left\n  aRight - tolerance < bLeft ||\n  // 'a' left doesn't touch 'b' right\n  aLeft + tolerance > bRight);\n}\n\n/**\n * Given a node, get everything needed to calculate its boundaries\n * @param  {HTMLElement} node\n * @return {Object}\n */\nfunction getBoundsForNode(node) {\n  if (!node.getBoundingClientRect) return node;\n  var rect = node.getBoundingClientRect(),\n    left = rect.left + pageOffset('left'),\n    top = rect.top + pageOffset('top');\n  return {\n    top: top,\n    left: left,\n    right: (node.offsetWidth || 0) + left,\n    bottom: (node.offsetHeight || 0) + top\n  };\n}\nfunction pageOffset(dir) {\n  if (dir === 'left') return window.pageXOffset || document.body.scrollLeft || 0;\n  if (dir === 'top') return window.pageYOffset || document.body.scrollTop || 0;\n}\n\nvar BackgroundCells = /*#__PURE__*/function (_React$Component) {\n  function BackgroundCells(props, context) {\n    var _this;\n    _classCallCheck(this, BackgroundCells);\n    _this = _callSuper(this, BackgroundCells, [props, context]);\n    _this.state = {\n      selecting: false\n    };\n    _this.containerRef = /*#__PURE__*/createRef();\n    return _this;\n  }\n  _inherits(BackgroundCells, _React$Component);\n  return _createClass(BackgroundCells, [{\n    key: \"componentDidMount\",\n    value: function componentDidMount() {\n      this.props.selectable && this._selectable();\n    }\n  }, {\n    key: \"componentWillUnmount\",\n    value: function componentWillUnmount() {\n      this._teardownSelectable();\n    }\n  }, {\n    key: \"componentDidUpdate\",\n    value: function componentDidUpdate(prevProps) {\n      if (!prevProps.selectable && this.props.selectable) this._selectable();\n      if (prevProps.selectable && !this.props.selectable) this._teardownSelectable();\n    }\n  }, {\n    key: \"render\",\n    value: function render() {\n      var _this$props = this.props,\n        range = _this$props.range,\n        getNow = _this$props.getNow,\n        getters = _this$props.getters,\n        currentDate = _this$props.date,\n        Wrapper = _this$props.components.dateCellWrapper,\n        localizer = _this$props.localizer;\n      var _this$state = this.state,\n        selecting = _this$state.selecting,\n        startIdx = _this$state.startIdx,\n        endIdx = _this$state.endIdx;\n      var current = getNow();\n      return /*#__PURE__*/React.createElement(\"div\", {\n        className: \"rbc-row-bg\",\n        ref: this.containerRef\n      }, range.map(function (date, index) {\n        var selected = selecting && index >= startIdx && index <= endIdx;\n        var _getters$dayProp = getters.dayProp(date),\n          className = _getters$dayProp.className,\n          style = _getters$dayProp.style;\n        return /*#__PURE__*/React.createElement(Wrapper, {\n          key: index,\n          value: date,\n          range: range\n        }, /*#__PURE__*/React.createElement(\"div\", {\n          style: style,\n          className: clsx('rbc-day-bg', className, selected && 'rbc-selected-cell', localizer.isSameDate(date, current) && 'rbc-today', currentDate && localizer.neq(currentDate, date, 'month') && 'rbc-off-range-bg')\n        }));\n      }));\n    }\n  }, {\n    key: \"_selectable\",\n    value: function _selectable() {\n      var _this2 = this;\n      var node = this.containerRef.current;\n      var selector = this._selector = new Selection(this.props.container, {\n        longPressThreshold: this.props.longPressThreshold\n      });\n      var selectorClicksHandler = function selectorClicksHandler(point, actionType) {\n        if (!isEvent(node, point) && !isShowMore(node, point)) {\n          var rowBox = getBoundsForNode(node);\n          var _this2$props = _this2.props,\n            range = _this2$props.range,\n            rtl = _this2$props.rtl;\n          if (pointInBox(rowBox, point)) {\n            var currentCell = getSlotAtX(rowBox, point.x, rtl, range.length);\n            _this2._selectSlot({\n              startIdx: currentCell,\n              endIdx: currentCell,\n              action: actionType,\n              box: point\n            });\n          }\n        }\n        _this2._initial = {};\n        _this2.setState({\n          selecting: false\n        });\n      };\n      selector.on('selecting', function (box) {\n        var _this2$props2 = _this2.props,\n          range = _this2$props2.range,\n          rtl = _this2$props2.rtl;\n        var startIdx = -1;\n        var endIdx = -1;\n        if (!_this2.state.selecting) {\n          notify(_this2.props.onSelectStart, [box]);\n          _this2._initial = {\n            x: box.x,\n            y: box.y\n          };\n        }\n        if (selector.isSelected(node)) {\n          var nodeBox = getBoundsForNode(node);\n          var _dateCellSelection = dateCellSelection(_this2._initial, nodeBox, box, range.length, rtl);\n          startIdx = _dateCellSelection.startIdx;\n          endIdx = _dateCellSelection.endIdx;\n        }\n        _this2.setState({\n          selecting: true,\n          startIdx: startIdx,\n          endIdx: endIdx\n        });\n      });\n      selector.on('beforeSelect', function (box) {\n        if (_this2.props.selectable !== 'ignoreEvents') return;\n        return !isEvent(_this2.containerRef.current, box);\n      });\n      selector.on('click', function (point) {\n        return selectorClicksHandler(point, 'click');\n      });\n      selector.on('doubleClick', function (point) {\n        return selectorClicksHandler(point, 'doubleClick');\n      });\n      selector.on('select', function (bounds) {\n        _this2._selectSlot(_objectSpread(_objectSpread({}, _this2.state), {}, {\n          action: 'select',\n          bounds: bounds\n        }));\n        _this2._initial = {};\n        _this2.setState({\n          selecting: false\n        });\n        notify(_this2.props.onSelectEnd, [_this2.state]);\n      });\n    }\n  }, {\n    key: \"_teardownSelectable\",\n    value: function _teardownSelectable() {\n      if (!this._selector) return;\n      this._selector.teardown();\n      this._selector = null;\n    }\n  }, {\n    key: \"_selectSlot\",\n    value: function _selectSlot(_ref) {\n      var endIdx = _ref.endIdx,\n        startIdx = _ref.startIdx,\n        action = _ref.action,\n        bounds = _ref.bounds,\n        box = _ref.box;\n      if (endIdx !== -1 && startIdx !== -1) this.props.onSelectSlot && this.props.onSelectSlot({\n        start: startIdx,\n        end: endIdx,\n        action: action,\n        bounds: bounds,\n        box: box,\n        resourceId: this.props.resourceId\n      });\n    }\n  }]);\n}(React.Component);\n\n/* eslint-disable react/prop-types */\nvar EventRowMixin = {\n  propTypes: {\n    slotMetrics: PropTypes.object.isRequired,\n    selected: PropTypes.object,\n    isAllDay: PropTypes.bool,\n    accessors: PropTypes.object.isRequired,\n    localizer: PropTypes.object.isRequired,\n    components: PropTypes.object.isRequired,\n    getters: PropTypes.object.isRequired,\n    onSelect: PropTypes.func,\n    onDoubleClick: PropTypes.func,\n    onKeyPress: PropTypes.func\n  },\n  defaultProps: {\n    segments: [],\n    selected: {}\n  },\n  renderEvent: function renderEvent(props, event) {\n    var selected = props.selected;\n      props.isAllDay;\n      var accessors = props.accessors,\n      getters = props.getters,\n      onSelect = props.onSelect,\n      onDoubleClick = props.onDoubleClick,\n      onKeyPress = props.onKeyPress,\n      localizer = props.localizer,\n      slotMetrics = props.slotMetrics,\n      components = props.components,\n      resizable = props.resizable;\n    var continuesPrior = slotMetrics.continuesPrior(event);\n    var continuesAfter = slotMetrics.continuesAfter(event);\n    return /*#__PURE__*/React.createElement(EventCell, {\n      event: event,\n      getters: getters,\n      localizer: localizer,\n      accessors: accessors,\n      components: components,\n      onSelect: onSelect,\n      onDoubleClick: onDoubleClick,\n      onKeyPress: onKeyPress,\n      continuesPrior: continuesPrior,\n      continuesAfter: continuesAfter,\n      slotStart: slotMetrics.first,\n      slotEnd: slotMetrics.last,\n      selected: isSelected(event, selected),\n      resizable: resizable\n    });\n  },\n  renderSpan: function renderSpan(slots, len, key) {\n    var content = arguments.length > 3 && arguments[3] !== undefined ? arguments[3] : ' ';\n    var per = Math.abs(len) / slots * 100 + '%';\n    return /*#__PURE__*/React.createElement(\"div\", {\n      key: key,\n      className: \"rbc-row-segment\"\n      // IE10/11 need max-width. flex-basis doesn't respect box-sizing\n      ,\n      style: {\n        WebkitFlexBasis: per,\n        flexBasis: per,\n        maxWidth: per\n      }\n    }, content);\n  }\n};\n\nvar EventRow = /*#__PURE__*/function (_React$Component) {\n  function EventRow() {\n    _classCallCheck(this, EventRow);\n    return _callSuper(this, EventRow, arguments);\n  }\n  _inherits(EventRow, _React$Component);\n  return _createClass(EventRow, [{\n    key: \"render\",\n    value: function render() {\n      var _this = this;\n      var _this$props = this.props,\n        segments = _this$props.segments,\n        slots = _this$props.slotMetrics.slots,\n        className = _this$props.className;\n      var lastEnd = 1;\n      return /*#__PURE__*/React.createElement(\"div\", {\n        className: clsx(className, 'rbc-row')\n      }, segments.reduce(function (row, _ref, li) {\n        var event = _ref.event,\n          left = _ref.left,\n          right = _ref.right,\n          span = _ref.span;\n        var key = '_lvl_' + li;\n        var gap = left - lastEnd;\n        var content = EventRowMixin.renderEvent(_this.props, event);\n        if (gap) row.push(EventRowMixin.renderSpan(slots, gap, \"\".concat(key, \"_gap\")));\n        row.push(EventRowMixin.renderSpan(slots, span, key, content));\n        lastEnd = right + 1;\n        return row;\n      }, []));\n    }\n  }]);\n}(React.Component);\nEventRow.defaultProps = _objectSpread({}, EventRowMixin.defaultProps);\n\nfunction endOfRange(_ref) {\n  var dateRange = _ref.dateRange,\n    _ref$unit = _ref.unit,\n    unit = _ref$unit === void 0 ? 'day' : _ref$unit,\n    localizer = _ref.localizer;\n  return {\n    first: dateRange[0],\n    last: localizer.add(dateRange[dateRange.length - 1], 1, unit)\n  };\n}\n\n// properly calculating segments requires working with dates in\n// the timezone we're working with, so we use the localizer\nfunction eventSegments(event, range, accessors, localizer) {\n  var _endOfRange = endOfRange({\n      dateRange: range,\n      localizer: localizer\n    }),\n    first = _endOfRange.first,\n    last = _endOfRange.last;\n  var slots = localizer.diff(first, last, 'day');\n  var start = localizer.max(localizer.startOf(accessors.start(event), 'day'), first);\n  var end = localizer.min(localizer.ceil(accessors.end(event), 'day'), last);\n  var padding = findIndex(range, function (x) {\n    return localizer.isSameDate(x, start);\n  });\n  var span = localizer.diff(start, end, 'day');\n  span = Math.min(span, slots);\n  // The segmentOffset is necessary when adjusting for timezones\n  // ahead of the browser timezone\n  span = Math.max(span - localizer.segmentOffset, 1);\n  return {\n    event: event,\n    span: span,\n    left: padding + 1,\n    right: Math.max(padding + span, 1)\n  };\n}\nfunction eventLevels(rowSegments) {\n  var limit = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : Infinity;\n  var i,\n    j,\n    seg,\n    levels = [],\n    extra = [];\n  for (i = 0; i < rowSegments.length; i++) {\n    seg = rowSegments[i];\n    for (j = 0; j < levels.length; j++) if (!segsOverlap(seg, levels[j])) break;\n    if (j >= limit) {\n      extra.push(seg);\n    } else {\n      (levels[j] || (levels[j] = [])).push(seg);\n    }\n  }\n  for (i = 0; i < levels.length; i++) {\n    levels[i].sort(function (a, b) {\n      return a.left - b.left;\n    }); //eslint-disable-line\n  }\n  return {\n    levels: levels,\n    extra: extra\n  };\n}\nfunction inRange(e, start, end, accessors, localizer) {\n  var event = {\n    start: accessors.start(e),\n    end: accessors.end(e)\n  };\n  var range = {\n    start: start,\n    end: end\n  };\n  return localizer.inEventRange({\n    event: event,\n    range: range\n  });\n}\nfunction segsOverlap(seg, otherSegs) {\n  return otherSegs.some(function (otherSeg) {\n    return otherSeg.left <= seg.right && otherSeg.right >= seg.left;\n  });\n}\nfunction sortWeekEvents(events, accessors, localizer) {\n  var base = _toConsumableArray(events);\n  var multiDayEvents = [];\n  var standardEvents = [];\n  base.forEach(function (event) {\n    var startCheck = accessors.start(event);\n    var endCheck = accessors.end(event);\n    if (localizer.daySpan(startCheck, endCheck) > 1) {\n      multiDayEvents.push(event);\n    } else {\n      standardEvents.push(event);\n    }\n  });\n  var multiSorted = multiDayEvents.sort(function (a, b) {\n    return sortEvents(a, b, accessors, localizer);\n  });\n  var standardSorted = standardEvents.sort(function (a, b) {\n    return sortEvents(a, b, accessors, localizer);\n  });\n  return [].concat(_toConsumableArray(multiSorted), _toConsumableArray(standardSorted));\n}\nfunction sortEvents(eventA, eventB, accessors, localizer) {\n  var evtA = {\n    start: accessors.start(eventA),\n    end: accessors.end(eventA),\n    allDay: accessors.allDay(eventA)\n  };\n  var evtB = {\n    start: accessors.start(eventB),\n    end: accessors.end(eventB),\n    allDay: accessors.allDay(eventB)\n  };\n  return localizer.sortEvents({\n    evtA: evtA,\n    evtB: evtB\n  });\n}\n\n// Modified: Check if a segment spans through this slot (including events that started earlier)\nvar isSegmentInSlot$1 = function isSegmentInSlot(seg, slot) {\n  return seg.left <= slot && seg.right >= slot;\n};\nvar eventsInSlot = function eventsInSlot(segments, slot) {\n  return segments.filter(function (seg) {\n    return isSegmentInSlot$1(seg, slot);\n  }).map(function (seg) {\n    return seg.event;\n  });\n};\nvar EventEndingRow = /*#__PURE__*/function (_React$Component) {\n  function EventEndingRow() {\n    _classCallCheck(this, EventEndingRow);\n    return _callSuper(this, EventEndingRow, arguments);\n  }\n  _inherits(EventEndingRow, _React$Component);\n  return _createClass(EventEndingRow, [{\n    key: \"render\",\n    value: function render() {\n      var _this$props = this.props,\n        segments = _this$props.segments,\n        slots = _this$props.slotMetrics.slots;\n      var rowSegments = eventLevels(segments).levels[0];\n      var current = 1,\n        lastEnd = 1,\n        row = [];\n      while (current <= slots) {\n        var key = '_lvl_' + current;\n\n        // Find segment that starts at or spans through current slot\n        var _ref = rowSegments.filter(function (seg) {\n            return isSegmentInSlot$1(seg, current);\n          })[0] || {},\n          event = _ref.event,\n          left = _ref.left,\n          right = _ref.right,\n          span = _ref.span;\n        if (!event) {\n          // No visible event starts at this slot, but check if we need a \"more\" button\n          // for hidden events that span this slot\n          var hiddenEvents = this.getHiddenEventsForSlot(segments, current);\n          if (hiddenEvents.length > 0) {\n            var _gap = current - lastEnd;\n            if (_gap) {\n              row.push(EventRowMixin.renderSpan(slots, _gap, key + '_gap'));\n            }\n            row.push(EventRowMixin.renderSpan(slots, 1, key, this.renderShowMore(segments, current)));\n            lastEnd = current = current + 1;\n            continue;\n          }\n          current++;\n          continue;\n        }\n        var gap = Math.max(0, left - lastEnd);\n        if (this.canRenderSlotEvent(left, span)) {\n          var content = EventRowMixin.renderEvent(this.props, event);\n          if (gap) {\n            row.push(EventRowMixin.renderSpan(slots, gap, key + '_gap'));\n          }\n          row.push(EventRowMixin.renderSpan(slots, span, key, content));\n          lastEnd = current = right + 1;\n        } else {\n          if (gap) {\n            row.push(EventRowMixin.renderSpan(slots, gap, key + '_gap'));\n          }\n          row.push(EventRowMixin.renderSpan(slots, 1, key, this.renderShowMore(segments, current)));\n          lastEnd = current = current + 1;\n        }\n      }\n      return /*#__PURE__*/React.createElement(\"div\", {\n        className: \"rbc-row\"\n      }, row);\n    }\n\n    // New helper method to find hidden events for a slot\n  }, {\n    key: \"getHiddenEventsForSlot\",\n    value: function getHiddenEventsForSlot(segments, slot) {\n      // Get all events (visible and hidden) for this slot\n      var allEventsInSlot = eventsInSlot(segments, slot);\n\n      // Get visible events for this slot from the first level\n      var rowSegments = eventLevels(segments).levels[0];\n      var visibleEventsInSlot = rowSegments.filter(function (seg) {\n        return isSegmentInSlot$1(seg, slot);\n      }).map(function (seg) {\n        return seg.event;\n      });\n\n      // Return events that are in allEventsInSlot but not in visibleEventsInSlot\n      return allEventsInSlot.filter(function (event) {\n        return !visibleEventsInSlot.some(function (visEvent) {\n          return visEvent === event;\n        });\n      });\n    }\n  }, {\n    key: \"canRenderSlotEvent\",\n    value: function canRenderSlotEvent(slot, span) {\n      var segments = this.props.segments;\n      return range$1(slot, slot + span).every(function (s) {\n        var count = eventsInSlot(segments, s).length;\n        return count === 1;\n      });\n    }\n  }, {\n    key: \"renderShowMore\",\n    value: function renderShowMore(segments, slot) {\n      var _this = this;\n      var _this$props2 = this.props,\n        localizer = _this$props2.localizer,\n        slotMetrics = _this$props2.slotMetrics,\n        components = _this$props2.components;\n      var events = slotMetrics.getEventsForSlot(slot);\n      var remainingEvents = eventsInSlot(segments, slot);\n      var count = remainingEvents.length;\n      if (components !== null && components !== void 0 && components.showMore) {\n        var ShowMore = components.showMore;\n        // The received slot seems to be 1-based, but the range we use to pull the date is 0-based\n        var slotDate = slotMetrics.getDateForSlot(slot - 1);\n        return count ? /*#__PURE__*/React.createElement(ShowMore, {\n          localizer: localizer,\n          slotDate: slotDate,\n          slot: slot,\n          count: count,\n          events: events,\n          remainingEvents: remainingEvents\n        }) : false;\n      }\n      return count ? /*#__PURE__*/React.createElement(\"button\", {\n        type: \"button\",\n        key: 'sm_' + slot,\n        className: clsx('rbc-button-link', 'rbc-show-more'),\n        onClick: function onClick(e) {\n          return _this.showMore(slot, e);\n        }\n      }, localizer.messages.showMore(count, remainingEvents, events)) : false;\n    }\n  }, {\n    key: \"showMore\",\n    value: function showMore(slot, e) {\n      e.preventDefault();\n      e.stopPropagation();\n      this.props.onShowMore(slot, e.target);\n    }\n  }]);\n}(React.Component);\nEventEndingRow.defaultProps = _objectSpread({}, EventRowMixin.defaultProps);\n\nvar ScrollableWeekWrapper = function ScrollableWeekWrapper(_ref) {\n  var children = _ref.children;\n  return /*#__PURE__*/React.createElement(\"div\", {\n    className: \"rbc-row-content-scroll-container\"\n  }, children);\n};\n\nvar isSegmentInSlot = function isSegmentInSlot(seg, slot) {\n  return seg.left <= slot && seg.right >= slot;\n};\nvar isEqual = function isEqual(a, b) {\n  return a[0].range === b[0].range && a[0].events === b[0].events;\n};\nfunction getSlotMetrics$1() {\n  return memoize(function (options) {\n    var range = options.range,\n      events = options.events,\n      maxRows = options.maxRows,\n      minRows = options.minRows,\n      accessors = options.accessors,\n      localizer = options.localizer;\n    var _endOfRange = endOfRange({\n        dateRange: range,\n        localizer: localizer\n      }),\n      first = _endOfRange.first,\n      last = _endOfRange.last;\n    var segments = events.map(function (evt) {\n      return eventSegments(evt, range, accessors, localizer);\n    });\n    var _eventLevels = eventLevels(segments, Math.max(maxRows - 1, 1)),\n      levels = _eventLevels.levels,\n      extra = _eventLevels.extra;\n    // Subtract 1 from minRows to not include showMore button row when\n    // it would be rendered\n    var minEventRows = extra.length > 0 ? minRows - 1 : minRows;\n    while (levels.length < minEventRows) levels.push([]);\n    return {\n      first: first,\n      last: last,\n      levels: levels,\n      extra: extra,\n      range: range,\n      slots: range.length,\n      clone: function clone(args) {\n        var metrics = getSlotMetrics$1();\n        return metrics(_objectSpread(_objectSpread({}, options), args));\n      },\n      getDateForSlot: function getDateForSlot(slotNumber) {\n        return range[slotNumber];\n      },\n      getSlotForDate: function getSlotForDate(date) {\n        return range.find(function (r) {\n          return localizer.isSameDate(r, date);\n        });\n      },\n      getEventsForSlot: function getEventsForSlot(slot) {\n        return segments.filter(function (seg) {\n          return isSegmentInSlot(seg, slot);\n        }).map(function (seg) {\n          return seg.event;\n        });\n      },\n      continuesPrior: function continuesPrior(event) {\n        return localizer.continuesPrior(accessors.start(event), first);\n      },\n      continuesAfter: function continuesAfter(event) {\n        var start = accessors.start(event);\n        var end = accessors.end(event);\n        return localizer.continuesAfter(start, end, last);\n      }\n    };\n  }, isEqual);\n}\n\nvar DateContentRow = /*#__PURE__*/function (_React$Component) {\n  function DateContentRow() {\n    var _this;\n    _classCallCheck(this, DateContentRow);\n    for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {\n      args[_key] = arguments[_key];\n    }\n    _this = _callSuper(this, DateContentRow, [].concat(args));\n    _this.handleSelectSlot = function (slot) {\n      var _this$props = _this.props,\n        range = _this$props.range,\n        onSelectSlot = _this$props.onSelectSlot;\n      onSelectSlot(range.slice(slot.start, slot.end + 1), slot);\n    };\n    _this.handleShowMore = function (slot, target) {\n      var _this$props2 = _this.props,\n        range = _this$props2.range,\n        onShowMore = _this$props2.onShowMore;\n      var metrics = _this.slotMetrics(_this.props);\n      var row = qsa(_this.containerRef.current, '.rbc-row-bg')[0];\n      var cell;\n      if (row) cell = row.children[slot - 1];\n      var events = metrics.getEventsForSlot(slot);\n      onShowMore(events, range[slot - 1], cell, slot, target);\n    };\n    _this.getContainer = function () {\n      var container = _this.props.container;\n      return container ? container() : _this.containerRef.current;\n    };\n    _this.renderHeadingCell = function (date, index) {\n      var _this$props3 = _this.props,\n        renderHeader = _this$props3.renderHeader,\n        getNow = _this$props3.getNow,\n        localizer = _this$props3.localizer;\n      return renderHeader({\n        date: date,\n        key: \"header_\".concat(index),\n        className: clsx('rbc-date-cell', localizer.isSameDate(date, getNow()) && 'rbc-now')\n      });\n    };\n    _this.renderDummy = function () {\n      var _this$props4 = _this.props,\n        className = _this$props4.className,\n        range = _this$props4.range,\n        renderHeader = _this$props4.renderHeader,\n        showAllEvents = _this$props4.showAllEvents;\n      return /*#__PURE__*/React.createElement(\"div\", {\n        className: className,\n        ref: _this.containerRef\n      }, /*#__PURE__*/React.createElement(\"div\", {\n        className: clsx('rbc-row-content', showAllEvents && 'rbc-row-content-scrollable')\n      }, renderHeader && /*#__PURE__*/React.createElement(\"div\", {\n        className: \"rbc-row\",\n        ref: _this.headingRowRef\n      }, range.map(_this.renderHeadingCell)), /*#__PURE__*/React.createElement(\"div\", {\n        className: \"rbc-row\",\n        ref: _this.eventRowRef\n      }, /*#__PURE__*/React.createElement(\"div\", {\n        className: \"rbc-row-segment\"\n      }, /*#__PURE__*/React.createElement(\"div\", {\n        className: \"rbc-event\"\n      }, /*#__PURE__*/React.createElement(\"div\", {\n        className: \"rbc-event-content\"\n      }, \"\\xA0\"))))));\n    };\n    _this.containerRef = /*#__PURE__*/createRef();\n    _this.headingRowRef = /*#__PURE__*/createRef();\n    _this.eventRowRef = /*#__PURE__*/createRef();\n    _this.slotMetrics = getSlotMetrics$1();\n    return _this;\n  }\n  _inherits(DateContentRow, _React$Component);\n  return _createClass(DateContentRow, [{\n    key: \"getRowLimit\",\n    value: function getRowLimit() {\n      var _this$headingRowRef;\n      /* Guessing this only gets called on the dummyRow */\n      var eventHeight = getHeight(this.eventRowRef.current);\n      var headingHeight = (_this$headingRowRef = this.headingRowRef) !== null && _this$headingRowRef !== void 0 && _this$headingRowRef.current ? getHeight(this.headingRowRef.current) : 0;\n      var eventSpace = getHeight(this.containerRef.current) - headingHeight;\n      return Math.max(Math.floor(eventSpace / eventHeight), 1);\n    }\n  }, {\n    key: \"render\",\n    value: function render() {\n      var _this$props5 = this.props,\n        date = _this$props5.date,\n        rtl = _this$props5.rtl,\n        range = _this$props5.range,\n        className = _this$props5.className,\n        selected = _this$props5.selected,\n        selectable = _this$props5.selectable,\n        renderForMeasure = _this$props5.renderForMeasure,\n        accessors = _this$props5.accessors,\n        getters = _this$props5.getters,\n        components = _this$props5.components,\n        getNow = _this$props5.getNow,\n        renderHeader = _this$props5.renderHeader,\n        onSelect = _this$props5.onSelect,\n        localizer = _this$props5.localizer,\n        onSelectStart = _this$props5.onSelectStart,\n        onSelectEnd = _this$props5.onSelectEnd,\n        onDoubleClick = _this$props5.onDoubleClick,\n        onKeyPress = _this$props5.onKeyPress,\n        resourceId = _this$props5.resourceId,\n        longPressThreshold = _this$props5.longPressThreshold,\n        isAllDay = _this$props5.isAllDay,\n        resizable = _this$props5.resizable,\n        showAllEvents = _this$props5.showAllEvents;\n      if (renderForMeasure) return this.renderDummy();\n      var metrics = this.slotMetrics(this.props);\n      var levels = metrics.levels,\n        extra = metrics.extra;\n      var ScrollableWeekComponent = showAllEvents ? ScrollableWeekWrapper : NoopWrapper;\n      var WeekWrapper = components.weekWrapper;\n      var eventRowProps = {\n        selected: selected,\n        accessors: accessors,\n        getters: getters,\n        localizer: localizer,\n        components: components,\n        onSelect: onSelect,\n        onDoubleClick: onDoubleClick,\n        onKeyPress: onKeyPress,\n        resourceId: resourceId,\n        slotMetrics: metrics,\n        resizable: resizable\n      };\n      return /*#__PURE__*/React.createElement(\"div\", {\n        className: className,\n        role: \"rowgroup\",\n        ref: this.containerRef\n      }, /*#__PURE__*/React.createElement(BackgroundCells, {\n        localizer: localizer,\n        date: date,\n        getNow: getNow,\n        rtl: rtl,\n        range: range,\n        selectable: selectable,\n        container: this.getContainer,\n        getters: getters,\n        onSelectStart: onSelectStart,\n        onSelectEnd: onSelectEnd,\n        onSelectSlot: this.handleSelectSlot,\n        components: components,\n        longPressThreshold: longPressThreshold,\n        resourceId: resourceId\n      }), /*#__PURE__*/React.createElement(\"div\", {\n        className: clsx('rbc-row-content', showAllEvents && 'rbc-row-content-scrollable'),\n        role: \"row\"\n      }, renderHeader && /*#__PURE__*/React.createElement(\"div\", {\n        className: \"rbc-row \",\n        ref: this.headingRowRef\n      }, range.map(this.renderHeadingCell)), /*#__PURE__*/React.createElement(ScrollableWeekComponent, null, /*#__PURE__*/React.createElement(WeekWrapper, Object.assign({\n        isAllDay: isAllDay\n      }, eventRowProps, {\n        rtl: this.props.rtl\n      }), levels.map(function (segs, idx) {\n        return /*#__PURE__*/React.createElement(EventRow, Object.assign({\n          key: idx,\n          segments: segs\n        }, eventRowProps));\n      }), !!extra.length && /*#__PURE__*/React.createElement(EventEndingRow, Object.assign({\n        segments: extra,\n        onShowMore: this.handleShowMore\n      }, eventRowProps))))));\n    }\n  }]);\n}(React.Component);\nDateContentRow.defaultProps = {\n  minRows: 0,\n  maxRows: Infinity\n};\n\nvar Header = function Header(_ref) {\n  var label = _ref.label;\n  return /*#__PURE__*/React.createElement(\"span\", {\n    role: \"columnheader\",\n    \"aria-sort\": \"none\"\n  }, label);\n};\n\nvar DateHeader = function DateHeader(_ref) {\n  var label = _ref.label,\n    drilldownView = _ref.drilldownView,\n    onDrillDown = _ref.onDrillDown;\n  if (!drilldownView) {\n    return /*#__PURE__*/React.createElement(\"span\", null, label);\n  }\n  return /*#__PURE__*/React.createElement(\"button\", {\n    type: \"button\",\n    className: \"rbc-button-link\",\n    onClick: onDrillDown\n  }, label);\n};\n\nvar _excluded$6 = [\"date\", \"className\"];\nvar eventsForWeek = function eventsForWeek(evts, start, end, accessors, localizer) {\n  return evts.filter(function (e) {\n    return inRange(e, start, end, accessors, localizer);\n  });\n};\nvar MonthView = /*#__PURE__*/function (_React$Component) {\n  function MonthView() {\n    var _this;\n    _classCallCheck(this, MonthView);\n    for (var _len = arguments.length, _args = new Array(_len), _key = 0; _key < _len; _key++) {\n      _args[_key] = arguments[_key];\n    }\n    _this = _callSuper(this, MonthView, [].concat(_args));\n    _this.getContainer = function () {\n      return _this.containerRef.current;\n    };\n    _this.renderWeek = function (week, weekIdx) {\n      var _this$props = _this.props,\n        events = _this$props.events,\n        components = _this$props.components,\n        selectable = _this$props.selectable,\n        getNow = _this$props.getNow,\n        selected = _this$props.selected,\n        date = _this$props.date,\n        localizer = _this$props.localizer,\n        longPressThreshold = _this$props.longPressThreshold,\n        accessors = _this$props.accessors,\n        getters = _this$props.getters,\n        showAllEvents = _this$props.showAllEvents;\n      var _this$state = _this.state,\n        needLimitMeasure = _this$state.needLimitMeasure,\n        rowLimit = _this$state.rowLimit;\n\n      // let's not mutate props\n      var weeksEvents = eventsForWeek(_toConsumableArray(events), week[0], week[week.length - 1], accessors, localizer);\n      var sorted = sortWeekEvents(weeksEvents, accessors, localizer);\n      return /*#__PURE__*/React.createElement(DateContentRow, {\n        key: weekIdx,\n        ref: weekIdx === 0 ? _this.slotRowRef : undefined,\n        container: _this.getContainer,\n        className: \"rbc-month-row\",\n        getNow: getNow,\n        date: date,\n        range: week,\n        events: sorted,\n        maxRows: showAllEvents ? Infinity : rowLimit,\n        selected: selected,\n        selectable: selectable,\n        components: components,\n        accessors: accessors,\n        getters: getters,\n        localizer: localizer,\n        renderHeader: _this.readerDateHeading,\n        renderForMeasure: needLimitMeasure,\n        onShowMore: _this.handleShowMore,\n        onSelect: _this.handleSelectEvent,\n        onDoubleClick: _this.handleDoubleClickEvent,\n        onKeyPress: _this.handleKeyPressEvent,\n        onSelectSlot: _this.handleSelectSlot,\n        longPressThreshold: longPressThreshold,\n        rtl: _this.props.rtl,\n        resizable: _this.props.resizable,\n        showAllEvents: showAllEvents\n      });\n    };\n    _this.readerDateHeading = function (_ref) {\n      var date = _ref.date,\n        className = _ref.className,\n        props = _objectWithoutProperties(_ref, _excluded$6);\n      var _this$props2 = _this.props,\n        currentDate = _this$props2.date,\n        getDrilldownView = _this$props2.getDrilldownView,\n        localizer = _this$props2.localizer;\n      var isOffRange = localizer.neq(currentDate, date, 'month');\n      var isCurrent = localizer.isSameDate(date, currentDate);\n      var drilldownView = getDrilldownView(date);\n      var label = localizer.format(date, 'dateFormat');\n      var DateHeaderComponent = _this.props.components.dateHeader || DateHeader;\n      return /*#__PURE__*/React.createElement(\"div\", Object.assign({}, props, {\n        className: clsx(className, isOffRange && 'rbc-off-range', isCurrent && 'rbc-current'),\n        role: \"cell\"\n      }), /*#__PURE__*/React.createElement(DateHeaderComponent, {\n        label: label,\n        date: date,\n        drilldownView: drilldownView,\n        isOffRange: isOffRange,\n        onDrillDown: function onDrillDown(e) {\n          return _this.handleHeadingClick(date, drilldownView, e);\n        }\n      }));\n    };\n    _this.handleSelectSlot = function (range, slotInfo) {\n      _this._pendingSelection = _this._pendingSelection.concat(range);\n      clearTimeout(_this._selectTimer);\n      _this._selectTimer = setTimeout(function () {\n        return _this.selectDates(slotInfo);\n      });\n    };\n    _this.handleHeadingClick = function (date, view, e) {\n      e.preventDefault();\n      _this.clearSelection();\n      notify(_this.props.onDrillDown, [date, view]);\n    };\n    _this.handleSelectEvent = function () {\n      _this.clearSelection();\n      for (var _len2 = arguments.length, args = new Array(_len2), _key2 = 0; _key2 < _len2; _key2++) {\n        args[_key2] = arguments[_key2];\n      }\n      notify(_this.props.onSelectEvent, args);\n    };\n    _this.handleDoubleClickEvent = function () {\n      _this.clearSelection();\n      for (var _len3 = arguments.length, args = new Array(_len3), _key3 = 0; _key3 < _len3; _key3++) {\n        args[_key3] = arguments[_key3];\n      }\n      notify(_this.props.onDoubleClickEvent, args);\n    };\n    _this.handleKeyPressEvent = function () {\n      _this.clearSelection();\n      for (var _len4 = arguments.length, args = new Array(_len4), _key4 = 0; _key4 < _len4; _key4++) {\n        args[_key4] = arguments[_key4];\n      }\n      notify(_this.props.onKeyPressEvent, args);\n    };\n    _this.handleShowMore = function (events, date, cell, slot, target) {\n      var _this$props3 = _this.props,\n        popup = _this$props3.popup,\n        onDrillDown = _this$props3.onDrillDown,\n        onShowMore = _this$props3.onShowMore,\n        getDrilldownView = _this$props3.getDrilldownView,\n        doShowMoreDrillDown = _this$props3.doShowMoreDrillDown;\n      //cancel any pending selections so only the event click goes through.\n      _this.clearSelection();\n      if (popup) {\n        var position = getPosition$1(cell, _this.containerRef.current);\n        _this.setState({\n          overlay: {\n            date: date,\n            events: events,\n            position: position,\n            target: target\n          }\n        });\n      } else if (doShowMoreDrillDown) {\n        notify(onDrillDown, [date, getDrilldownView(date) || views.DAY]);\n      }\n      notify(onShowMore, [events, date, slot]);\n    };\n    _this.overlayDisplay = function () {\n      _this.setState({\n        overlay: null\n      });\n    };\n    _this.state = {\n      rowLimit: 5,\n      needLimitMeasure: true,\n      date: null\n    };\n    _this.containerRef = /*#__PURE__*/createRef();\n    _this.slotRowRef = /*#__PURE__*/createRef();\n    _this._bgRows = [];\n    _this._pendingSelection = [];\n    return _this;\n  }\n  _inherits(MonthView, _React$Component);\n  return _createClass(MonthView, [{\n    key: \"componentDidMount\",\n    value: function componentDidMount() {\n      var _this2 = this;\n      var running;\n      if (this.state.needLimitMeasure) this.measureRowLimit(this.props);\n      window.addEventListener('resize', this._resizeListener = function () {\n        if (!running) {\n          animationFrame.request(function () {\n            running = false;\n            _this2.setState({\n              needLimitMeasure: true\n            }); //eslint-disable-line\n          });\n        }\n      }, false);\n    }\n  }, {\n    key: \"componentDidUpdate\",\n    value: function componentDidUpdate() {\n      if (this.state.needLimitMeasure) this.measureRowLimit(this.props);\n    }\n  }, {\n    key: \"componentWillUnmount\",\n    value: function componentWillUnmount() {\n      window.removeEventListener('resize', this._resizeListener, false);\n    }\n  }, {\n    key: \"render\",\n    value: function render() {\n      var _this$props4 = this.props,\n        date = _this$props4.date,\n        localizer = _this$props4.localizer,\n        className = _this$props4.className,\n        month = localizer.visibleDays(date, localizer),\n        weeks = chunk(month, 7);\n      this._weekCount = weeks.length;\n      return /*#__PURE__*/React.createElement(\"div\", {\n        className: clsx('rbc-month-view', className),\n        role: \"table\",\n        \"aria-label\": \"Month View\",\n        ref: this.containerRef\n      }, /*#__PURE__*/React.createElement(\"div\", {\n        className: \"rbc-row rbc-month-header\",\n        role: \"row\"\n      }, this.renderHeaders(weeks[0])), weeks.map(this.renderWeek), this.props.popup && this.renderOverlay());\n    }\n  }, {\n    key: \"renderHeaders\",\n    value: function renderHeaders(row) {\n      var _this$props5 = this.props,\n        localizer = _this$props5.localizer,\n        components = _this$props5.components;\n      var first = row[0];\n      var last = row[row.length - 1];\n      var HeaderComponent = components.header || Header;\n      return localizer.range(first, last, 'day').map(function (day, idx) {\n        return /*#__PURE__*/React.createElement(\"div\", {\n          key: 'header_' + idx,\n          className: \"rbc-header\"\n        }, /*#__PURE__*/React.createElement(HeaderComponent, {\n          date: day,\n          localizer: localizer,\n          label: localizer.format(day, 'weekdayFormat')\n        }));\n      });\n    }\n  }, {\n    key: \"renderOverlay\",\n    value: function renderOverlay() {\n      var _this$state$overlay,\n        _this$state2,\n        _this3 = this;\n      var overlay = (_this$state$overlay = (_this$state2 = this.state) === null || _this$state2 === void 0 ? void 0 : _this$state2.overlay) !== null && _this$state$overlay !== void 0 ? _this$state$overlay : {};\n      var _this$props6 = this.props,\n        accessors = _this$props6.accessors,\n        localizer = _this$props6.localizer,\n        components = _this$props6.components,\n        getters = _this$props6.getters,\n        selected = _this$props6.selected,\n        popupOffset = _this$props6.popupOffset,\n        handleDragStart = _this$props6.handleDragStart;\n      var onHide = function onHide() {\n        return _this3.setState({\n          overlay: null\n        });\n      };\n      return /*#__PURE__*/React.createElement(PopOverlay, {\n        overlay: overlay,\n        accessors: accessors,\n        localizer: localizer,\n        components: components,\n        getters: getters,\n        selected: selected,\n        popupOffset: popupOffset,\n        ref: this.containerRef,\n        handleKeyPressEvent: this.handleKeyPressEvent,\n        handleSelectEvent: this.handleSelectEvent,\n        handleDoubleClickEvent: this.handleDoubleClickEvent,\n        handleDragStart: handleDragStart,\n        show: !!overlay.position,\n        overlayDisplay: this.overlayDisplay,\n        onHide: onHide\n      });\n\n      /* return (\n        <Overlay\n          rootClose\n          placement=\"bottom\"\n          show={!!overlay.position}\n          onHide={() => this.setState({ overlay: null })}\n          target={() => overlay.target}\n        >\n          {({ props }) => (\n            <Popup\n              {...props}\n              popupOffset={popupOffset}\n              accessors={accessors}\n              getters={getters}\n              selected={selected}\n              components={components}\n              localizer={localizer}\n              position={overlay.position}\n              show={this.overlayDisplay}\n              events={overlay.events}\n              slotStart={overlay.date}\n              slotEnd={overlay.end}\n              onSelect={this.handleSelectEvent}\n              onDoubleClick={this.handleDoubleClickEvent}\n              onKeyPress={this.handleKeyPressEvent}\n              handleDragStart={this.props.handleDragStart}\n            />\n          )}\n        </Overlay>\n      ) */\n    }\n  }, {\n    key: \"measureRowLimit\",\n    value: function measureRowLimit() {\n      this.setState({\n        needLimitMeasure: false,\n        rowLimit: this.slotRowRef.current.getRowLimit()\n      });\n    }\n  }, {\n    key: \"selectDates\",\n    value: function selectDates(slotInfo) {\n      var slots = this._pendingSelection.slice();\n      this._pendingSelection = [];\n      slots.sort(function (a, b) {\n        return +a - +b;\n      });\n      var start = new Date(slots[0]);\n      var end = new Date(slots[slots.length - 1]);\n      end.setDate(slots[slots.length - 1].getDate() + 1);\n      notify(this.props.onSelectSlot, {\n        slots: slots,\n        start: start,\n        end: end,\n        action: slotInfo.action,\n        bounds: slotInfo.bounds,\n        box: slotInfo.box\n      });\n    }\n  }, {\n    key: \"clearSelection\",\n    value: function clearSelection() {\n      clearTimeout(this._selectTimer);\n      this._pendingSelection = [];\n    }\n  }], [{\n    key: \"getDerivedStateFromProps\",\n    value: function getDerivedStateFromProps(_ref2, state) {\n      var date = _ref2.date,\n        localizer = _ref2.localizer;\n      return {\n        date: date,\n        needLimitMeasure: localizer.neq(date, state.date, 'month')\n      };\n    }\n  }]);\n}(React.Component);\nMonthView.range = function (date, _ref3) {\n  var localizer = _ref3.localizer;\n  var start = localizer.firstVisibleDay(date, localizer);\n  var end = localizer.lastVisibleDay(date, localizer);\n  return {\n    start: start,\n    end: end\n  };\n};\nMonthView.navigate = function (date, action, _ref4) {\n  var localizer = _ref4.localizer;\n  switch (action) {\n    case navigate.PREVIOUS:\n      return localizer.add(date, -1, 'month');\n    case navigate.NEXT:\n      return localizer.add(date, 1, 'month');\n    default:\n      return date;\n  }\n};\nMonthView.title = function (date, _ref5) {\n  var localizer = _ref5.localizer;\n  return localizer.format(date, 'monthHeaderFormat');\n};\n\nvar getKey = function getKey(_ref) {\n  var min = _ref.min,\n    max = _ref.max,\n    step = _ref.step,\n    slots = _ref.slots,\n    localizer = _ref.localizer;\n  return \"\".concat(+localizer.startOf(min, 'minutes')) + \"\".concat(+localizer.startOf(max, 'minutes')) + \"\".concat(step, \"-\").concat(slots);\n};\nfunction getSlotMetrics(_ref2) {\n  var start = _ref2.min,\n    end = _ref2.max,\n    step = _ref2.step,\n    timeslots = _ref2.timeslots,\n    localizer = _ref2.localizer;\n  var key = getKey({\n    start: start,\n    end: end,\n    step: step,\n    timeslots: timeslots,\n    localizer: localizer\n  });\n\n  // DST differences are handled inside the localizer\n  var totalMin = 1 + localizer.getTotalMin(start, end);\n  var minutesFromMidnight = localizer.getMinutesFromMidnight(start);\n  var numGroups = Math.ceil((totalMin - 1) / (step * timeslots));\n  var numSlots = numGroups * timeslots;\n  var groups = new Array(numGroups);\n  var slots = new Array(numSlots);\n  // Each slot date is created from \"zero\", instead of adding `step` to\n  // the previous one, in order to avoid DST oddities\n  for (var grp = 0; grp < numGroups; grp++) {\n    groups[grp] = new Array(timeslots);\n    for (var slot = 0; slot < timeslots; slot++) {\n      var slotIdx = grp * timeslots + slot;\n      var minFromStart = slotIdx * step;\n      // A date with total minutes calculated from the start of the day\n      slots[slotIdx] = groups[grp][slot] = localizer.getSlotDate(start, minutesFromMidnight, minFromStart);\n    }\n  }\n\n  // Necessary to be able to select up until the last timeslot in a day\n  var lastSlotMinFromStart = slots.length * step;\n  slots.push(localizer.getSlotDate(start, minutesFromMidnight, lastSlotMinFromStart));\n  function positionFromDate(date) {\n    var diff = localizer.diff(start, date, 'minutes') + localizer.getDstOffset(start, date);\n    return Math.min(diff, totalMin);\n  }\n  return {\n    groups: groups,\n    update: function update(args) {\n      if (getKey(args) !== key) return getSlotMetrics(args);\n      return this;\n    },\n    dateIsInGroup: function dateIsInGroup(date, groupIndex) {\n      var nextGroup = groups[groupIndex + 1];\n      return localizer.inRange(date, groups[groupIndex][0], nextGroup ? nextGroup[0] : end, 'minutes');\n    },\n    nextSlot: function nextSlot(slot) {\n      // We cannot guarantee that the slot object must be in slots,\n      // because after each update, a new slots array will be created.\n      var next = slots[Math.min(slots.findIndex(function (s) {\n        return s === slot || localizer.eq(s, slot);\n      }) + 1, slots.length - 1)];\n      // in the case of the last slot we won't a long enough range so manually get it\n      if (localizer.eq(next, slot)) next = localizer.add(slot, step, 'minutes');\n      return next;\n    },\n    closestSlotToPosition: function closestSlotToPosition(percent) {\n      var slot = Math.min(slots.length - 1, Math.max(0, Math.floor(percent * numSlots)));\n      return slots[slot];\n    },\n    closestSlotFromPoint: function closestSlotFromPoint(point, boundaryRect) {\n      var range = Math.abs(boundaryRect.top - boundaryRect.bottom);\n      return this.closestSlotToPosition((point.y - boundaryRect.top) / range);\n    },\n    closestSlotFromDate: function closestSlotFromDate(date) {\n      var offset = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : 0;\n      if (localizer.lt(date, start, 'minutes')) return slots[0];\n      if (localizer.gt(date, end, 'minutes')) return slots[slots.length - 1];\n      var diffMins = localizer.diff(start, date, 'minutes');\n      return slots[(diffMins - diffMins % step) / step + offset];\n    },\n    startsBeforeDay: function startsBeforeDay(date) {\n      return localizer.lt(date, start, 'day');\n    },\n    startsAfterDay: function startsAfterDay(date) {\n      return localizer.gt(date, end, 'day');\n    },\n    startsBefore: function startsBefore(date) {\n      return localizer.lt(localizer.merge(start, date), start, 'minutes');\n    },\n    startsAfter: function startsAfter(date) {\n      return localizer.gt(localizer.merge(end, date), end, 'minutes');\n    },\n    getRange: function getRange(rangeStart, rangeEnd, ignoreMin, ignoreMax) {\n      if (!ignoreMin) rangeStart = localizer.min(end, localizer.max(start, rangeStart));\n      if (!ignoreMax) rangeEnd = localizer.min(end, localizer.max(start, rangeEnd));\n      var rangeStartMin = positionFromDate(rangeStart);\n      var rangeEndMin = positionFromDate(rangeEnd);\n      var top = rangeEndMin > step * numSlots && !localizer.eq(end, rangeEnd) ? (rangeStartMin - step) / (step * numSlots) * 100 : rangeStartMin / (step * numSlots) * 100;\n      return {\n        top: top,\n        height: rangeEndMin / (step * numSlots) * 100 - top,\n        start: positionFromDate(rangeStart),\n        startDate: rangeStart,\n        end: positionFromDate(rangeEnd),\n        endDate: rangeEnd\n      };\n    },\n    getCurrentTimePosition: function getCurrentTimePosition(rangeStart) {\n      var rangeStartMin = positionFromDate(rangeStart);\n      var top = rangeStartMin / (step * numSlots) * 100;\n      return top;\n    }\n  };\n}\n\nvar Event = /*#__PURE__*/function () {\n  function Event(data, _ref) {\n    var accessors = _ref.accessors,\n      slotMetrics = _ref.slotMetrics;\n    _classCallCheck(this, Event);\n    var _slotMetrics$getRange = slotMetrics.getRange(accessors.start(data), accessors.end(data)),\n      start = _slotMetrics$getRange.start,\n      startDate = _slotMetrics$getRange.startDate,\n      end = _slotMetrics$getRange.end,\n      endDate = _slotMetrics$getRange.endDate,\n      top = _slotMetrics$getRange.top,\n      height = _slotMetrics$getRange.height;\n    this.start = start;\n    this.end = end;\n    this.startMs = +startDate;\n    this.endMs = +endDate;\n    this.top = top;\n    this.height = height;\n    this.data = data;\n  }\n\n  /**\n   * The event's width without any overlap.\n   */\n  return _createClass(Event, [{\n    key: \"_width\",\n    get: function get() {\n      // The container event's width is determined by the maximum number of\n      // events in any of its rows.\n      if (this.rows) {\n        var columns = this.rows.reduce(function (max, row) {\n          return Math.max(max, row.leaves.length + 1);\n        },\n        // add itself\n        0) + 1; // add the container\n\n        return 100 / columns;\n      }\n\n      // The row event's width is the space left by the container, divided\n      // among itself and its leaves.\n      if (this.leaves) {\n        var availableWidth = 100 - this.container._width;\n        return availableWidth / (this.leaves.length + 1);\n      }\n\n      // The leaf event's width is determined by its row's width\n      return this.row._width;\n    }\n\n    /**\n     * The event's calculated width, possibly with extra width added for\n     * overlapping effect.\n     */\n  }, {\n    key: \"width\",\n    get: function get() {\n      var noOverlap = this._width;\n      var overlap = Math.min(100, this._width * 1.7);\n\n      // Containers can always grow.\n      if (this.rows) {\n        return overlap;\n      }\n\n      // Rows can grow if they have leaves.\n      if (this.leaves) {\n        return this.leaves.length > 0 ? overlap : noOverlap;\n      }\n\n      // Leaves can grow unless they're the last item in a row.\n      var leaves = this.row.leaves;\n      var index = leaves.indexOf(this);\n      return index === leaves.length - 1 ? noOverlap : overlap;\n    }\n  }, {\n    key: \"xOffset\",\n    get: function get() {\n      // Containers have no offset.\n      if (this.rows) return 0;\n\n      // Rows always start where their container ends.\n      if (this.leaves) return this.container._width;\n\n      // Leaves are spread out evenly on the space left by its row.\n      var _this$row = this.row,\n        leaves = _this$row.leaves,\n        xOffset = _this$row.xOffset,\n        _width = _this$row._width;\n      var index = leaves.indexOf(this) + 1;\n      return xOffset + index * _width;\n    }\n  }]);\n}();\n/**\n * Return true if event a and b is considered to be on the same row.\n */\nfunction onSameRow(a, b, minimumStartDifference) {\n  return (\n    // Occupies the same start slot.\n    Math.abs(b.start - a.start) < minimumStartDifference ||\n    // A's start slot overlaps with b's end slot.\n    b.start > a.start && b.start < a.end\n  );\n}\nfunction sortByRender(events) {\n  var sortedByTime = sortBy(events, ['startMs', function (e) {\n    return -e.endMs;\n  }]);\n  var sorted = [];\n  while (sortedByTime.length > 0) {\n    var event = sortedByTime.shift();\n    sorted.push(event);\n    for (var i = 0; i < sortedByTime.length; i++) {\n      var test = sortedByTime[i];\n\n      // Still inside this event, look for next.\n      if (event.endMs > test.startMs) continue;\n\n      // We've found the first event of the next event group.\n      // If that event is not right next to our current event, we have to\n      // move it here.\n      if (i > 0) {\n        var _event = sortedByTime.splice(i, 1)[0];\n        sorted.push(_event);\n      }\n\n      // We've already found the next event group, so stop looking.\n      break;\n    }\n  }\n  return sorted;\n}\nfunction getStyledEvents$1(_ref2) {\n  var events = _ref2.events,\n    minimumStartDifference = _ref2.minimumStartDifference,\n    slotMetrics = _ref2.slotMetrics,\n    accessors = _ref2.accessors;\n  // Create proxy events and order them so that we don't have\n  // to fiddle with z-indexes.\n  var proxies = events.map(function (event) {\n    return new Event(event, {\n      slotMetrics: slotMetrics,\n      accessors: accessors\n    });\n  });\n  var eventsInRenderOrder = sortByRender(proxies);\n\n  // Group overlapping events, while keeping order.\n  // Every event is always one of: container, row or leaf.\n  // Containers can contain rows, and rows can contain leaves.\n  var containerEvents = [];\n  var _loop = function _loop() {\n    var event = eventsInRenderOrder[i];\n\n    // Check if this event can go into a container event.\n    var container = containerEvents.find(function (c) {\n      return c.end > event.start || Math.abs(event.start - c.start) < minimumStartDifference;\n    });\n\n    // Couldn't find a container — that means this event is a container.\n    if (!container) {\n      event.rows = [];\n      containerEvents.push(event);\n      return 1; // continue\n    }\n\n    // Found a container for the event.\n    event.container = container;\n\n    // Check if the event can be placed in an existing row.\n    // Start looking from behind.\n    var row = null;\n    for (var j = container.rows.length - 1; !row && j >= 0; j--) {\n      if (onSameRow(container.rows[j], event, minimumStartDifference)) {\n        row = container.rows[j];\n      }\n    }\n    if (row) {\n      // Found a row, so add it.\n      row.leaves.push(event);\n      event.row = row;\n    } else {\n      // Couldn't find a row – that means this event is a row.\n      event.leaves = [];\n      container.rows.push(event);\n    }\n  };\n  for (var i = 0; i < eventsInRenderOrder.length; i++) {\n    if (_loop()) continue;\n  }\n\n  // Return the original events, along with their styles.\n  return eventsInRenderOrder.map(function (event) {\n    return {\n      event: event.data,\n      style: {\n        top: event.top,\n        height: event.height,\n        width: event.width,\n        xOffset: Math.max(0, event.xOffset)\n      }\n    };\n  });\n}\n\nfunction getMaxIdxDFS(node, maxIdx, visited) {\n  for (var i = 0; i < node.friends.length; ++i) {\n    if (visited.indexOf(node.friends[i]) > -1) continue;\n    maxIdx = maxIdx > node.friends[i].idx ? maxIdx : node.friends[i].idx;\n    // TODO : trace it by not object but kinda index or something for performance\n    visited.push(node.friends[i]);\n    var newIdx = getMaxIdxDFS(node.friends[i], maxIdx, visited);\n    maxIdx = maxIdx > newIdx ? maxIdx : newIdx;\n  }\n  return maxIdx;\n}\nfunction noOverlap (_ref) {\n  var events = _ref.events,\n    minimumStartDifference = _ref.minimumStartDifference,\n    slotMetrics = _ref.slotMetrics,\n    accessors = _ref.accessors;\n  var styledEvents = getStyledEvents$1({\n    events: events,\n    minimumStartDifference: minimumStartDifference,\n    slotMetrics: slotMetrics,\n    accessors: accessors\n  });\n  styledEvents.sort(function (a, b) {\n    a = a.style;\n    b = b.style;\n    if (a.top !== b.top) return a.top > b.top ? 1 : -1;else if (a.height !== b.height) return a.top + a.height < b.top + b.height ? 1 : -1;else return 0;\n  });\n  for (var i = 0; i < styledEvents.length; ++i) {\n    styledEvents[i].friends = [];\n    delete styledEvents[i].style.left;\n    delete styledEvents[i].style.left;\n    delete styledEvents[i].idx;\n    delete styledEvents[i].size;\n  }\n  for (var _i2 = 0; _i2 < styledEvents.length - 1; ++_i2) {\n    var se1 = styledEvents[_i2];\n    var y1 = se1.style.top;\n    var y2 = se1.style.top + se1.style.height;\n    for (var j = _i2 + 1; j < styledEvents.length; ++j) {\n      var se2 = styledEvents[j];\n      var y3 = se2.style.top;\n      var y4 = se2.style.top + se2.style.height;\n      if (y3 >= y1 && y4 <= y2 || y4 > y1 && y4 <= y2 || y3 >= y1 && y3 < y2) {\n        // TODO : hashmap would be effective for performance\n        se1.friends.push(se2);\n        se2.friends.push(se1);\n      }\n    }\n  }\n  for (var _i4 = 0; _i4 < styledEvents.length; ++_i4) {\n    var se = styledEvents[_i4];\n    var bitmap = [];\n    for (var _j2 = 0; _j2 < 100; ++_j2) bitmap.push(1); // 1 means available\n\n    for (var _j4 = 0; _j4 < se.friends.length; ++_j4) if (se.friends[_j4].idx !== undefined) bitmap[se.friends[_j4].idx] = 0; // 0 means reserved\n\n    se.idx = bitmap.indexOf(1);\n  }\n  for (var _i6 = 0; _i6 < styledEvents.length; ++_i6) {\n    var size = 0;\n    if (styledEvents[_i6].size) continue;\n    var allFriends = [];\n    var maxIdx = getMaxIdxDFS(styledEvents[_i6], 0, allFriends);\n    size = 100 / (maxIdx + 1);\n    styledEvents[_i6].size = size;\n    for (var _j6 = 0; _j6 < allFriends.length; ++_j6) allFriends[_j6].size = size;\n  }\n  for (var _i8 = 0; _i8 < styledEvents.length; ++_i8) {\n    var e = styledEvents[_i8];\n    e.style.left = e.idx * e.size;\n\n    // stretch to maximum\n    var _maxIdx = 0;\n    for (var _j8 = 0; _j8 < e.friends.length; ++_j8) {\n      var idx = e.friends[_j8].idx;\n      _maxIdx = _maxIdx > idx ? _maxIdx : idx;\n    }\n    if (_maxIdx <= e.idx) e.size = 100 - e.idx * e.size;\n\n    // padding between events\n    // for this feature, `width` is not percentage based unit anymore\n    // it will be used with calc()\n    var padding = e.idx === 0 ? 0 : 3;\n    e.style.width = \"calc(\".concat(e.size, \"% - \").concat(padding, \"px)\");\n    e.style.height = \"calc(\".concat(e.style.height, \"% - 2px)\");\n    e.style.xOffset = \"calc(\".concat(e.style.left, \"% + \").concat(padding, \"px)\");\n  }\n  return styledEvents;\n}\n\n/*eslint no-unused-vars: \"off\"*/\n\nvar DefaultAlgorithms = {\n  overlap: getStyledEvents$1,\n  'no-overlap': noOverlap\n};\nfunction isFunction(a) {\n  return !!(a && a.constructor && a.call && a.apply);\n}\n\n//\nfunction getStyledEvents(_ref) {\n  _ref.events;\n    _ref.minimumStartDifference;\n    _ref.slotMetrics;\n    _ref.accessors;\n    var dayLayoutAlgorithm = _ref.dayLayoutAlgorithm;\n  var algorithm = dayLayoutAlgorithm;\n  if (dayLayoutAlgorithm in DefaultAlgorithms) algorithm = DefaultAlgorithms[dayLayoutAlgorithm];\n  if (!isFunction(algorithm)) {\n    // invalid algorithm\n    return [];\n  }\n  return algorithm.apply(this, arguments);\n}\n\nvar TimeSlotGroup = /*#__PURE__*/function (_Component) {\n  function TimeSlotGroup() {\n    _classCallCheck(this, TimeSlotGroup);\n    return _callSuper(this, TimeSlotGroup, arguments);\n  }\n  _inherits(TimeSlotGroup, _Component);\n  return _createClass(TimeSlotGroup, [{\n    key: \"render\",\n    value: function render() {\n      var _this$props = this.props,\n        renderSlot = _this$props.renderSlot,\n        resource = _this$props.resource,\n        group = _this$props.group,\n        getters = _this$props.getters,\n        _this$props$component = _this$props.components,\n        _this$props$component2 = _this$props$component === void 0 ? {} : _this$props$component,\n        _this$props$component3 = _this$props$component2.timeSlotWrapper,\n        Wrapper = _this$props$component3 === void 0 ? NoopWrapper : _this$props$component3;\n      var groupProps = getters ? getters.slotGroupProp(group) : {};\n      return /*#__PURE__*/React.createElement(\"div\", Object.assign({\n        className: \"rbc-timeslot-group\"\n      }, groupProps), group.map(function (value, idx) {\n        var slotProps = getters ? getters.slotProp(value, resource) : {};\n        return /*#__PURE__*/React.createElement(Wrapper, {\n          key: idx,\n          value: value,\n          resource: resource\n        }, /*#__PURE__*/React.createElement(\"div\", Object.assign({}, slotProps, {\n          className: clsx('rbc-time-slot', slotProps.className)\n        }), renderSlot && renderSlot(value, idx)));\n      }));\n    }\n  }]);\n}(Component);\n\nfunction stringifyPercent(v) {\n  return typeof v === 'string' ? v : v + '%';\n}\n\n/* eslint-disable react/prop-types */\nfunction TimeGridEvent(props) {\n  var style = props.style,\n    className = props.className,\n    event = props.event,\n    accessors = props.accessors,\n    rtl = props.rtl,\n    selected = props.selected,\n    label = props.label,\n    continuesPrior = props.continuesPrior,\n    continuesAfter = props.continuesAfter,\n    getters = props.getters,\n    onClick = props.onClick,\n    onDoubleClick = props.onDoubleClick,\n    isBackgroundEvent = props.isBackgroundEvent,\n    onKeyPress = props.onKeyPress,\n    _props$components = props.components,\n    Event = _props$components.event,\n    EventWrapper = _props$components.eventWrapper;\n  var title = accessors.title(event);\n  var tooltip = accessors.tooltip(event);\n  var end = accessors.end(event);\n  var start = accessors.start(event);\n  var userProps = getters.eventProp(event, start, end, selected);\n  var inner = [/*#__PURE__*/React.createElement(\"div\", {\n    key: \"1\",\n    className: \"rbc-event-label\"\n  }, label), /*#__PURE__*/React.createElement(\"div\", {\n    key: \"2\",\n    className: \"rbc-event-content\"\n  }, Event ? /*#__PURE__*/React.createElement(Event, {\n    event: event,\n    title: title\n  }) : title)];\n  var height = style.height,\n    top = style.top,\n    width = style.width,\n    xOffset = style.xOffset;\n  var eventStyle = _objectSpread(_objectSpread({}, userProps.style), {}, _defineProperty({\n    top: stringifyPercent(top),\n    height: stringifyPercent(height),\n    width: stringifyPercent(width)\n  }, rtl ? 'right' : 'left', stringifyPercent(xOffset)));\n  return /*#__PURE__*/React.createElement(EventWrapper, Object.assign({\n    type: \"time\"\n  }, props), /*#__PURE__*/React.createElement(\"div\", {\n    role: \"button\",\n    tabIndex: 0,\n    onClick: onClick,\n    onDoubleClick: onDoubleClick,\n    style: eventStyle,\n    onKeyDown: onKeyPress,\n    title: tooltip ? (typeof label === 'string' ? label + ': ' : '') + tooltip : undefined,\n    className: clsx(isBackgroundEvent ? 'rbc-background-event' : 'rbc-event', className, userProps.className, {\n      'rbc-selected': selected,\n      'rbc-event-continues-earlier': continuesPrior,\n      'rbc-event-continues-later': continuesAfter\n    })\n  }, inner));\n}\n\nvar DayColumnWrapper = function DayColumnWrapper(_ref) {\n  var children = _ref.children,\n    className = _ref.className,\n    style = _ref.style,\n    innerRef = _ref.innerRef;\n  return /*#__PURE__*/React.createElement(\"div\", {\n    className: className,\n    style: style,\n    ref: innerRef\n  }, children);\n};\nvar DayColumnWrapper$1 = /*#__PURE__*/React.forwardRef(function (props, ref) {\n  return /*#__PURE__*/React.createElement(DayColumnWrapper, Object.assign({}, props, {\n    innerRef: ref\n  }));\n});\n\nvar _excluded$5 = [\"dayProp\"],\n  _excluded2$1 = [\"eventContainerWrapper\", \"timeIndicatorWrapper\"];\nvar DayColumn = /*#__PURE__*/function (_React$Component) {\n  function DayColumn() {\n    var _this;\n    _classCallCheck(this, DayColumn);\n    for (var _len = arguments.length, _args = new Array(_len), _key = 0; _key < _len; _key++) {\n      _args[_key] = arguments[_key];\n    }\n    _this = _callSuper(this, DayColumn, [].concat(_args));\n    _this.state = {\n      selecting: false,\n      timeIndicatorPosition: null\n    };\n    _this.intervalTriggered = false;\n    _this.renderEvents = function (_ref) {\n      var events = _ref.events,\n        isBackgroundEvent = _ref.isBackgroundEvent;\n      var _this$props = _this.props,\n        rtl = _this$props.rtl,\n        selected = _this$props.selected,\n        accessors = _this$props.accessors,\n        localizer = _this$props.localizer,\n        getters = _this$props.getters,\n        components = _this$props.components,\n        step = _this$props.step,\n        timeslots = _this$props.timeslots,\n        dayLayoutAlgorithm = _this$props.dayLayoutAlgorithm,\n        resizable = _this$props.resizable;\n      var _this2 = _this,\n        slotMetrics = _this2.slotMetrics;\n      var messages = localizer.messages;\n      var styledEvents = getStyledEvents({\n        events: events,\n        accessors: accessors,\n        slotMetrics: slotMetrics,\n        minimumStartDifference: Math.ceil(step * timeslots / 2),\n        dayLayoutAlgorithm: dayLayoutAlgorithm\n      });\n      return styledEvents.map(function (_ref2, idx) {\n        var _accessors$eventId;\n        var event = _ref2.event,\n          style = _ref2.style;\n        var end = accessors.end(event);\n        var start = accessors.start(event);\n        var key = (_accessors$eventId = accessors.eventId(event)) !== null && _accessors$eventId !== void 0 ? _accessors$eventId : 'evt_' + idx;\n        var format = 'eventTimeRangeFormat';\n        var label;\n        var startsBeforeDay = slotMetrics.startsBeforeDay(start);\n        var startsAfterDay = slotMetrics.startsAfterDay(end);\n        if (startsBeforeDay) format = 'eventTimeRangeEndFormat';else if (startsAfterDay) format = 'eventTimeRangeStartFormat';\n        if (startsBeforeDay && startsAfterDay) label = messages.allDay;else label = localizer.format({\n          start: start,\n          end: end\n        }, format);\n        var continuesPrior = startsBeforeDay || slotMetrics.startsBefore(start);\n        var continuesAfter = startsAfterDay || slotMetrics.startsAfter(end);\n        return /*#__PURE__*/React.createElement(TimeGridEvent, {\n          style: style,\n          event: event,\n          label: label,\n          key: key,\n          getters: getters,\n          rtl: rtl,\n          components: components,\n          continuesPrior: continuesPrior,\n          continuesAfter: continuesAfter,\n          accessors: accessors,\n          resource: _this.props.resource,\n          selected: isSelected(event, selected),\n          onClick: function onClick(e) {\n            return _this._select(_objectSpread(_objectSpread(_objectSpread({}, event), _this.props.resource && {\n              sourceResource: _this.props.resource\n            }), isBackgroundEvent && {\n              isBackgroundEvent: true\n            }), e);\n          },\n          onDoubleClick: function onDoubleClick(e) {\n            return _this._doubleClick(event, e);\n          },\n          isBackgroundEvent: isBackgroundEvent,\n          onKeyPress: function onKeyPress(e) {\n            return _this._keyPress(event, e);\n          },\n          resizable: resizable\n        });\n      });\n    };\n    _this._selectable = function () {\n      var node = _this.containerRef.current;\n      var _this$props2 = _this.props,\n        longPressThreshold = _this$props2.longPressThreshold,\n        localizer = _this$props2.localizer;\n      var selector = _this._selector = new Selection(function () {\n        return node;\n      }, {\n        longPressThreshold: longPressThreshold\n      });\n      var maybeSelect = function maybeSelect(box) {\n        var onSelecting = _this.props.onSelecting;\n        var current = _this.state || {};\n        var state = selectionState(box);\n        var start = state.startDate,\n          end = state.endDate;\n        if (onSelecting) {\n          if (localizer.eq(current.startDate, start, 'minutes') && localizer.eq(current.endDate, end, 'minutes') || onSelecting({\n            start: start,\n            end: end,\n            resourceId: _this.props.resource\n          }) === false) return;\n        }\n        if (_this.state.start !== state.start || _this.state.end !== state.end || _this.state.selecting !== state.selecting) {\n          _this.setState(state);\n        }\n      };\n      var selectionState = function selectionState(point) {\n        var currentSlot = _this.slotMetrics.closestSlotFromPoint(point, getBoundsForNode(node));\n        if (!_this.state.selecting) {\n          _this._initialSlot = currentSlot;\n        }\n        var initialSlot = _this._initialSlot;\n        if (localizer.lte(initialSlot, currentSlot)) {\n          currentSlot = _this.slotMetrics.nextSlot(currentSlot);\n        } else if (localizer.gt(initialSlot, currentSlot)) {\n          initialSlot = _this.slotMetrics.nextSlot(initialSlot);\n        }\n        var selectRange = _this.slotMetrics.getRange(localizer.min(initialSlot, currentSlot), localizer.max(initialSlot, currentSlot));\n        return _objectSpread(_objectSpread({}, selectRange), {}, {\n          selecting: true,\n          top: \"\".concat(selectRange.top, \"%\"),\n          height: \"\".concat(selectRange.height, \"%\")\n        });\n      };\n      var selectorClicksHandler = function selectorClicksHandler(box, actionType) {\n        if (!isEvent(_this.containerRef.current, box)) {\n          var _selectionState = selectionState(box),\n            startDate = _selectionState.startDate,\n            endDate = _selectionState.endDate;\n          _this._selectSlot({\n            startDate: startDate,\n            endDate: endDate,\n            action: actionType,\n            box: box\n          });\n        }\n        _this.setState({\n          selecting: false\n        });\n      };\n      selector.on('selecting', maybeSelect);\n      selector.on('selectStart', maybeSelect);\n      selector.on('beforeSelect', function (box) {\n        if (_this.props.selectable !== 'ignoreEvents') return;\n        return !isEvent(_this.containerRef.current, box);\n      });\n      selector.on('click', function (box) {\n        return selectorClicksHandler(box, 'click');\n      });\n      selector.on('doubleClick', function (box) {\n        return selectorClicksHandler(box, 'doubleClick');\n      });\n      selector.on('select', function (bounds) {\n        if (_this.state.selecting) {\n          _this._selectSlot(_objectSpread(_objectSpread({}, _this.state), {}, {\n            action: 'select',\n            bounds: bounds\n          }));\n          _this.setState({\n            selecting: false\n          });\n        }\n      });\n      selector.on('reset', function () {\n        if (_this.state.selecting) {\n          _this.setState({\n            selecting: false\n          });\n        }\n      });\n    };\n    _this._teardownSelectable = function () {\n      if (!_this._selector) return;\n      _this._selector.teardown();\n      _this._selector = null;\n    };\n    _this._selectSlot = function (_ref3) {\n      var startDate = _ref3.startDate,\n        endDate = _ref3.endDate,\n        action = _ref3.action,\n        bounds = _ref3.bounds,\n        box = _ref3.box;\n      var current = startDate,\n        slots = [];\n      while (_this.props.localizer.lte(current, endDate)) {\n        slots.push(current);\n        current = new Date(+current + _this.props.step * 60 * 1000); // using Date ensures not to create an endless loop the day DST begins\n      }\n      notify(_this.props.onSelectSlot, {\n        slots: slots,\n        start: startDate,\n        end: endDate,\n        resourceId: _this.props.resource,\n        action: action,\n        bounds: bounds,\n        box: box\n      });\n    };\n    _this._select = function () {\n      for (var _len2 = arguments.length, args = new Array(_len2), _key2 = 0; _key2 < _len2; _key2++) {\n        args[_key2] = arguments[_key2];\n      }\n      notify(_this.props.onSelectEvent, args);\n    };\n    _this._doubleClick = function () {\n      for (var _len3 = arguments.length, args = new Array(_len3), _key3 = 0; _key3 < _len3; _key3++) {\n        args[_key3] = arguments[_key3];\n      }\n      notify(_this.props.onDoubleClickEvent, args);\n    };\n    _this._keyPress = function () {\n      for (var _len4 = arguments.length, args = new Array(_len4), _key4 = 0; _key4 < _len4; _key4++) {\n        args[_key4] = arguments[_key4];\n      }\n      notify(_this.props.onKeyPressEvent, args);\n    };\n    _this.slotMetrics = getSlotMetrics(_this.props);\n    _this.containerRef = /*#__PURE__*/createRef();\n    return _this;\n  }\n  _inherits(DayColumn, _React$Component);\n  return _createClass(DayColumn, [{\n    key: \"componentDidMount\",\n    value: function componentDidMount() {\n      this.props.selectable && this._selectable();\n      if (this.props.isNow) {\n        this.setTimeIndicatorPositionUpdateInterval();\n      }\n    }\n  }, {\n    key: \"componentWillUnmount\",\n    value: function componentWillUnmount() {\n      this._teardownSelectable();\n      this.clearTimeIndicatorInterval();\n    }\n  }, {\n    key: \"componentDidUpdate\",\n    value: function componentDidUpdate(prevProps, prevState) {\n      if (this.props.selectable && !prevProps.selectable) this._selectable();\n      if (!this.props.selectable && prevProps.selectable) this._teardownSelectable();\n      var _this$props3 = this.props,\n        getNow = _this$props3.getNow,\n        isNow = _this$props3.isNow,\n        localizer = _this$props3.localizer,\n        date = _this$props3.date,\n        min = _this$props3.min,\n        max = _this$props3.max;\n      var getNowChanged = localizer.neq(prevProps.getNow(), getNow(), 'minutes');\n      if (prevProps.isNow !== isNow || getNowChanged) {\n        this.clearTimeIndicatorInterval();\n        if (isNow) {\n          var tail = !getNowChanged && localizer.eq(prevProps.date, date, 'minutes') && prevState.timeIndicatorPosition === this.state.timeIndicatorPosition;\n          this.setTimeIndicatorPositionUpdateInterval(tail);\n        }\n      } else if (isNow && (localizer.neq(prevProps.min, min, 'minutes') || localizer.neq(prevProps.max, max, 'minutes'))) {\n        this.positionTimeIndicator();\n      }\n    }\n\n    /**\n     * @param tail {Boolean} - whether `positionTimeIndicator` call should be\n     *   deferred or called upon setting interval (`true` - if deferred);\n     */\n  }, {\n    key: \"setTimeIndicatorPositionUpdateInterval\",\n    value: function setTimeIndicatorPositionUpdateInterval() {\n      var _this3 = this;\n      var tail = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : false;\n      if (!this.intervalTriggered && !tail) {\n        this.positionTimeIndicator();\n      }\n      this._timeIndicatorTimeout = window.setTimeout(function () {\n        _this3.intervalTriggered = true;\n        _this3.positionTimeIndicator();\n        _this3.setTimeIndicatorPositionUpdateInterval();\n      }, 60000);\n    }\n  }, {\n    key: \"clearTimeIndicatorInterval\",\n    value: function clearTimeIndicatorInterval() {\n      this.intervalTriggered = false;\n      window.clearTimeout(this._timeIndicatorTimeout);\n    }\n  }, {\n    key: \"positionTimeIndicator\",\n    value: function positionTimeIndicator() {\n      var _this$props4 = this.props,\n        min = _this$props4.min,\n        max = _this$props4.max,\n        getNow = _this$props4.getNow;\n      var current = getNow();\n      if (current >= min && current <= max) {\n        var top = this.slotMetrics.getCurrentTimePosition(current);\n        this.intervalTriggered = true;\n        this.setState({\n          timeIndicatorPosition: top\n        });\n      } else {\n        this.clearTimeIndicatorInterval();\n      }\n    }\n  }, {\n    key: \"render\",\n    value: function render() {\n      var _this$props5 = this.props,\n        date = _this$props5.date,\n        max = _this$props5.max,\n        rtl = _this$props5.rtl,\n        isNow = _this$props5.isNow,\n        resource = _this$props5.resource,\n        accessors = _this$props5.accessors,\n        localizer = _this$props5.localizer,\n        _this$props5$getters = _this$props5.getters,\n        dayProp = _this$props5$getters.dayProp,\n        getters = _objectWithoutProperties(_this$props5$getters, _excluded$5),\n        _this$props5$componen = _this$props5.components,\n        EventContainer = _this$props5$componen.eventContainerWrapper,\n        TimeIndicatorWrapper = _this$props5$componen.timeIndicatorWrapper,\n        components = _objectWithoutProperties(_this$props5$componen, _excluded2$1);\n      this.slotMetrics = this.slotMetrics.update(this.props);\n      var slotMetrics = this.slotMetrics;\n      var _this$state = this.state,\n        selecting = _this$state.selecting,\n        top = _this$state.top,\n        height = _this$state.height,\n        startDate = _this$state.startDate,\n        endDate = _this$state.endDate;\n      var selectDates = {\n        start: startDate,\n        end: endDate\n      };\n      var _dayProp = dayProp(max, resource),\n        className = _dayProp.className,\n        style = _dayProp.style;\n      var timeIndicatorProps = {\n        className: 'rbc-current-time-indicator',\n        style: {\n          top: \"\".concat(this.state.timeIndicatorPosition, \"%\")\n        }\n      };\n      var DayColumnWrapperComponent = components.dayColumnWrapper || DayColumnWrapper$1;\n      return /*#__PURE__*/React.createElement(DayColumnWrapperComponent, {\n        ref: this.containerRef,\n        date: date,\n        style: style,\n        className: clsx(className, 'rbc-day-slot', 'rbc-time-column', isNow && 'rbc-now', isNow && 'rbc-today',\n        // WHY\n        selecting && 'rbc-slot-selecting'),\n        slotMetrics: slotMetrics,\n        resource: resource\n      }, slotMetrics.groups.map(function (grp, idx) {\n        return /*#__PURE__*/React.createElement(TimeSlotGroup, {\n          key: idx,\n          group: grp,\n          resource: resource,\n          getters: getters,\n          components: components\n        });\n      }), /*#__PURE__*/React.createElement(EventContainer, {\n        localizer: localizer,\n        resource: resource,\n        accessors: accessors,\n        getters: getters,\n        components: components,\n        slotMetrics: slotMetrics\n      }, /*#__PURE__*/React.createElement(\"div\", {\n        className: clsx('rbc-events-container', rtl && 'rtl')\n      }, this.renderEvents({\n        events: this.props.backgroundEvents,\n        isBackgroundEvent: true\n      }), this.renderEvents({\n        events: this.props.events\n      }))), selecting && /*#__PURE__*/React.createElement(\"div\", {\n        className: \"rbc-slot-selection\",\n        style: {\n          top: top,\n          height: height\n        }\n      }, /*#__PURE__*/React.createElement(\"span\", null, localizer.format(selectDates, 'selectRangeFormat'))), isNow && this.intervalTriggered && /*#__PURE__*/React.createElement(TimeIndicatorWrapper, timeIndicatorProps, /*#__PURE__*/React.createElement(\"div\", timeIndicatorProps)));\n    }\n  }]);\n}(React.Component);\nDayColumn.defaultProps = {\n  dragThroughEvents: true,\n  timeslots: 2\n};\n\nvar ResourceHeader = function ResourceHeader(_ref) {\n  var label = _ref.label;\n  return /*#__PURE__*/React.createElement(React.Fragment, null, label);\n};\n\nvar TimeGridHeader = /*#__PURE__*/function (_React$Component) {\n  function TimeGridHeader() {\n    var _this;\n    _classCallCheck(this, TimeGridHeader);\n    for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {\n      args[_key] = arguments[_key];\n    }\n    _this = _callSuper(this, TimeGridHeader, [].concat(args));\n    _this.handleHeaderClick = function (date, view, e) {\n      e.preventDefault();\n      notify(_this.props.onDrillDown, [date, view]);\n    };\n    _this.renderRow = function (resource) {\n      var _this$props = _this.props,\n        events = _this$props.events,\n        rtl = _this$props.rtl,\n        selectable = _this$props.selectable,\n        getNow = _this$props.getNow,\n        range = _this$props.range,\n        getters = _this$props.getters,\n        localizer = _this$props.localizer,\n        accessors = _this$props.accessors,\n        components = _this$props.components,\n        resizable = _this$props.resizable;\n      var resourceId = accessors.resourceId(resource);\n      var eventsToDisplay = resource ? events.filter(function (event) {\n        return accessors.resource(event) === resourceId;\n      }) : events;\n      return /*#__PURE__*/React.createElement(DateContentRow, {\n        isAllDay: true,\n        rtl: rtl,\n        getNow: getNow,\n        minRows: 2\n        // Add +1 to include showMore button row in the row limit\n        ,\n        maxRows: _this.props.allDayMaxRows + 1,\n        range: range,\n        events: eventsToDisplay,\n        resourceId: resourceId,\n        className: \"rbc-allday-cell\",\n        selectable: selectable,\n        selected: _this.props.selected,\n        components: components,\n        accessors: accessors,\n        getters: getters,\n        localizer: localizer,\n        onSelect: _this.props.onSelectEvent,\n        onShowMore: _this.props.onShowMore,\n        onDoubleClick: _this.props.onDoubleClickEvent,\n        onKeyPress: _this.props.onKeyPressEvent,\n        onSelectSlot: _this.props.onSelectSlot,\n        longPressThreshold: _this.props.longPressThreshold,\n        resizable: resizable\n      });\n    };\n    return _this;\n  }\n  _inherits(TimeGridHeader, _React$Component);\n  return _createClass(TimeGridHeader, [{\n    key: \"renderHeaderCells\",\n    value: function renderHeaderCells(range) {\n      var _this2 = this;\n      var _this$props2 = this.props,\n        localizer = _this$props2.localizer,\n        getDrilldownView = _this$props2.getDrilldownView,\n        getNow = _this$props2.getNow,\n        dayProp = _this$props2.getters.dayProp,\n        _this$props2$componen = _this$props2.components.header,\n        HeaderComponent = _this$props2$componen === void 0 ? Header : _this$props2$componen;\n      var today = getNow();\n      return range.map(function (date, i) {\n        var drilldownView = getDrilldownView(date);\n        var label = localizer.format(date, 'dayFormat');\n        var _dayProp = dayProp(date),\n          className = _dayProp.className,\n          style = _dayProp.style;\n        var header = /*#__PURE__*/React.createElement(HeaderComponent, {\n          date: date,\n          label: label,\n          localizer: localizer\n        });\n        return /*#__PURE__*/React.createElement(\"div\", {\n          key: i,\n          style: style,\n          className: clsx('rbc-header', className, localizer.isSameDate(date, today) && 'rbc-today')\n        }, drilldownView ? /*#__PURE__*/React.createElement(\"button\", {\n          type: \"button\",\n          className: \"rbc-button-link\",\n          onClick: function onClick(e) {\n            return _this2.handleHeaderClick(date, drilldownView, e);\n          }\n        }, header) : /*#__PURE__*/React.createElement(\"span\", null, header));\n      });\n    }\n  }, {\n    key: \"render\",\n    value: function render() {\n      var _this3 = this;\n      var _this$props3 = this.props,\n        width = _this$props3.width,\n        rtl = _this$props3.rtl,\n        resources = _this$props3.resources,\n        range = _this$props3.range,\n        events = _this$props3.events,\n        getNow = _this$props3.getNow,\n        accessors = _this$props3.accessors,\n        selectable = _this$props3.selectable,\n        components = _this$props3.components,\n        getters = _this$props3.getters,\n        scrollRef = _this$props3.scrollRef,\n        localizer = _this$props3.localizer,\n        isOverflowing = _this$props3.isOverflowing,\n        _this$props3$componen = _this$props3.components,\n        TimeGutterHeader = _this$props3$componen.timeGutterHeader,\n        _this$props3$componen2 = _this$props3$componen.resourceHeader,\n        ResourceHeaderComponent = _this$props3$componen2 === void 0 ? ResourceHeader : _this$props3$componen2,\n        resizable = _this$props3.resizable;\n      var style = {};\n      if (isOverflowing) {\n        style[rtl ? 'marginLeft' : 'marginRight'] = \"\".concat(scrollbarSize() - 1, \"px\");\n      }\n      var groupedEvents = resources.groupEvents(events);\n      return /*#__PURE__*/React.createElement(\"div\", {\n        style: style,\n        ref: scrollRef,\n        className: clsx('rbc-time-header', isOverflowing && 'rbc-overflowing')\n      }, /*#__PURE__*/React.createElement(\"div\", {\n        className: \"rbc-label rbc-time-header-gutter\",\n        style: {\n          width: width,\n          minWidth: width,\n          maxWidth: width\n        }\n      }, TimeGutterHeader && /*#__PURE__*/React.createElement(TimeGutterHeader, null)), resources.map(function (_ref, idx) {\n        var _ref2 = _slicedToArray(_ref, 2),\n          id = _ref2[0],\n          resource = _ref2[1];\n        return /*#__PURE__*/React.createElement(\"div\", {\n          className: \"rbc-time-header-content\",\n          key: id || idx\n        }, resource && /*#__PURE__*/React.createElement(\"div\", {\n          className: \"rbc-row rbc-row-resource\",\n          key: \"resource_\".concat(idx)\n        }, /*#__PURE__*/React.createElement(\"div\", {\n          className: \"rbc-header\"\n        }, /*#__PURE__*/React.createElement(ResourceHeaderComponent, {\n          index: idx,\n          label: accessors.resourceTitle(resource),\n          resource: resource\n        }))), /*#__PURE__*/React.createElement(\"div\", {\n          className: \"rbc-row rbc-time-header-cell\".concat(range.length <= 1 ? ' rbc-time-header-cell-single-day' : '')\n        }, _this3.renderHeaderCells(range)), /*#__PURE__*/React.createElement(DateContentRow, {\n          isAllDay: true,\n          rtl: rtl,\n          getNow: getNow,\n          minRows: 2\n          // Add +1 to include showMore button row in the row limit\n          ,\n          maxRows: _this3.props.allDayMaxRows + 1,\n          range: range,\n          events: groupedEvents.get(id) || [],\n          resourceId: resource && id,\n          className: \"rbc-allday-cell\",\n          selectable: selectable,\n          selected: _this3.props.selected,\n          components: components,\n          accessors: accessors,\n          getters: getters,\n          localizer: localizer,\n          onSelect: _this3.props.onSelectEvent,\n          onShowMore: _this3.props.onShowMore,\n          onDoubleClick: _this3.props.onDoubleClickEvent,\n          onKeyDown: _this3.props.onKeyPressEvent,\n          onSelectSlot: _this3.props.onSelectSlot,\n          longPressThreshold: _this3.props.longPressThreshold,\n          resizable: resizable\n        }));\n      }));\n    }\n  }]);\n}(React.Component);\n\nvar TimeGridHeaderResources = /*#__PURE__*/function (_React$Component) {\n  function TimeGridHeaderResources() {\n    var _this;\n    _classCallCheck(this, TimeGridHeaderResources);\n    for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {\n      args[_key] = arguments[_key];\n    }\n    _this = _callSuper(this, TimeGridHeaderResources, [].concat(args));\n    _this.handleHeaderClick = function (date, view, e) {\n      e.preventDefault();\n      notify(_this.props.onDrillDown, [date, view]);\n    };\n    return _this;\n  }\n  _inherits(TimeGridHeaderResources, _React$Component);\n  return _createClass(TimeGridHeaderResources, [{\n    key: \"renderHeaderCells\",\n    value: function renderHeaderCells(range) {\n      var _this2 = this;\n      var _this$props = this.props,\n        localizer = _this$props.localizer,\n        getDrilldownView = _this$props.getDrilldownView,\n        getNow = _this$props.getNow,\n        dayProp = _this$props.getters.dayProp,\n        _this$props$component = _this$props.components,\n        _this$props$component2 = _this$props$component.header,\n        HeaderComponent = _this$props$component2 === void 0 ? Header : _this$props$component2,\n        _this$props$component3 = _this$props$component.resourceHeader,\n        ResourceHeaderComponent = _this$props$component3 === void 0 ? ResourceHeader : _this$props$component3,\n        resources = _this$props.resources,\n        accessors = _this$props.accessors,\n        events = _this$props.events,\n        rtl = _this$props.rtl,\n        selectable = _this$props.selectable,\n        components = _this$props.components,\n        getters = _this$props.getters,\n        resizable = _this$props.resizable;\n      var today = getNow();\n      var groupedEvents = resources.groupEvents(events);\n      return range.map(function (date, idx) {\n        var drilldownView = getDrilldownView(date);\n        var label = localizer.format(date, 'dayFormat');\n        var _dayProp = dayProp(date),\n          className = _dayProp.className,\n          style = _dayProp.style;\n        var header = /*#__PURE__*/React.createElement(HeaderComponent, {\n          date: date,\n          label: label,\n          localizer: localizer\n        });\n        return /*#__PURE__*/React.createElement(\"div\", {\n          key: idx,\n          className: \"rbc-time-header-content rbc-resource-grouping\"\n        }, /*#__PURE__*/React.createElement(\"div\", {\n          className: \"rbc-row rbc-time-header-cell\".concat(range.length <= 1 ? ' rbc-time-header-cell-single-day' : '')\n        }, /*#__PURE__*/React.createElement(\"div\", {\n          style: style,\n          className: clsx('rbc-header', className, localizer.isSameDate(date, today) && 'rbc-today')\n        }, drilldownView ? /*#__PURE__*/React.createElement(\"button\", {\n          type: \"button\",\n          className: \"rbc-button-link\",\n          onClick: function onClick(e) {\n            return _this2.handleHeaderClick(date, drilldownView, e);\n          }\n        }, header) : /*#__PURE__*/React.createElement(\"span\", null, header))), /*#__PURE__*/React.createElement(\"div\", {\n          className: \"rbc-row\"\n        }, resources.map(function (_ref, idx) {\n          var _ref2 = _slicedToArray(_ref, 2),\n            id = _ref2[0],\n            resource = _ref2[1];\n          return /*#__PURE__*/React.createElement(\"div\", {\n            key: \"resource_\".concat(id, \"_\").concat(idx),\n            className: clsx('rbc-header', className, localizer.isSameDate(date, today) && 'rbc-today')\n          }, /*#__PURE__*/React.createElement(ResourceHeaderComponent, {\n            index: idx,\n            label: accessors.resourceTitle(resource),\n            resource: resource\n          }));\n        })), /*#__PURE__*/React.createElement(\"div\", {\n          className: \"rbc-row rbc-m-b-negative-3 rbc-h-full\"\n        }, resources.map(function (_ref3, idx) {\n          var _ref4 = _slicedToArray(_ref3, 2),\n            id = _ref4[0],\n            resource = _ref4[1];\n          // Filter the grouped events by the current date.\n          var filteredEvents = (groupedEvents.get(id) || []).filter(function (event) {\n            return localizer.isSameDate(event.start, date) || localizer.isSameDate(event.end, date);\n          });\n          return /*#__PURE__*/React.createElement(DateContentRow, {\n            key: \"resource_\".concat(id, \"_\").concat(idx),\n            isAllDay: true,\n            rtl: rtl,\n            getNow: getNow,\n            minRows: 2,\n            maxRows: _this2.props.allDayMaxRows + 1,\n            range: [date] // This ensures that only the single day is rendered\n            ,\n            events: filteredEvents // Only show filtered events for this day.\n            ,\n            resourceId: resource && id,\n            className: \"rbc-allday-cell\",\n            selectable: selectable,\n            selected: _this2.props.selected,\n            components: components,\n            accessors: accessors,\n            getters: getters,\n            localizer: localizer,\n            onSelect: _this2.props.onSelectEvent,\n            onShowMore: _this2.props.onShowMore,\n            onDoubleClick: _this2.props.onDoubleClickEvent,\n            onKeyDown: _this2.props.onKeyPressEvent,\n            onSelectSlot: _this2.props.onSelectSlot,\n            longPressThreshold: _this2.props.longPressThreshold,\n            resizable: resizable\n          });\n        })));\n      });\n    }\n  }, {\n    key: \"render\",\n    value: function render() {\n      var _this$props2 = this.props,\n        width = _this$props2.width,\n        rtl = _this$props2.rtl,\n        range = _this$props2.range,\n        scrollRef = _this$props2.scrollRef,\n        isOverflowing = _this$props2.isOverflowing,\n        TimeGutterHeader = _this$props2.components.timeGutterHeader;\n      var style = {};\n      if (isOverflowing) {\n        style[rtl ? 'marginLeft' : 'marginRight'] = \"\".concat(scrollbarSize() - 1, \"px\");\n      }\n      return /*#__PURE__*/React.createElement(\"div\", {\n        style: style,\n        ref: scrollRef,\n        className: clsx('rbc-time-header', isOverflowing && 'rbc-overflowing')\n      }, /*#__PURE__*/React.createElement(\"div\", {\n        className: \"rbc-label rbc-time-header-gutter\",\n        style: {\n          width: width,\n          minWidth: width,\n          maxWidth: width\n        }\n      }, TimeGutterHeader && /*#__PURE__*/React.createElement(TimeGutterHeader, null)), this.renderHeaderCells(range));\n    }\n  }]);\n}(React.Component);\n\n/**\n * Since the TimeGutter only displays the 'times' of slots in a day, and is separate\n * from the Day Columns themselves, we check to see if the range contains an offset difference\n * and, if so, change the beginning and end 'date' by a day to properly display the slots times\n * used.\n */\nfunction adjustForDST(_ref) {\n  var min = _ref.min,\n    max = _ref.max,\n    localizer = _ref.localizer;\n  if (localizer.getTimezoneOffset(min) !== localizer.getTimezoneOffset(max)) {\n    return {\n      start: localizer.add(min, -1, 'day'),\n      end: localizer.add(max, -1, 'day')\n    };\n  }\n  return {\n    start: min,\n    end: max\n  };\n}\nvar TimeGutter = function TimeGutter(_ref2) {\n  var min = _ref2.min,\n    max = _ref2.max,\n    timeslots = _ref2.timeslots,\n    step = _ref2.step,\n    localizer = _ref2.localizer,\n    getNow = _ref2.getNow,\n    resource = _ref2.resource,\n    components = _ref2.components,\n    getters = _ref2.getters,\n    gutterRef = _ref2.gutterRef;\n  var TimeGutterWrapper = components.timeGutterWrapper;\n  var _useMemo = useMemo(function () {\n      return adjustForDST({\n        min: min,\n        max: max,\n        localizer: localizer\n      });\n    },\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n    [min === null || min === void 0 ? void 0 : min.toISOString(), max === null || max === void 0 ? void 0 : max.toISOString(), localizer]),\n    start = _useMemo.start,\n    end = _useMemo.end;\n  var _useState = useState(getSlotMetrics({\n      min: start,\n      max: end,\n      timeslots: timeslots,\n      step: step,\n      localizer: localizer\n    })),\n    _useState2 = _slicedToArray(_useState, 2),\n    slotMetrics = _useState2[0],\n    setSlotMetrics = _useState2[1];\n  useEffect(function () {\n    if (slotMetrics) {\n      setSlotMetrics(slotMetrics.update({\n        min: start,\n        max: end,\n        timeslots: timeslots,\n        step: step,\n        localizer: localizer\n      }));\n    }\n    /**\n     * We don't want this to fire when slotMetrics is updated as it would recursively bomb\n     */\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n  }, [start === null || start === void 0 ? void 0 : start.toISOString(), end === null || end === void 0 ? void 0 : end.toISOString(), timeslots, step]);\n  var renderSlot = useCallback(function (value, idx) {\n    if (idx) return null; // don't return the first (0) idx\n\n    var isNow = slotMetrics.dateIsInGroup(getNow(), idx);\n    return /*#__PURE__*/React.createElement(\"span\", {\n      className: clsx('rbc-label', isNow && 'rbc-now')\n    }, localizer.format(value, 'timeGutterFormat'));\n  }, [slotMetrics, localizer, getNow]);\n  return /*#__PURE__*/React.createElement(TimeGutterWrapper, {\n    slotMetrics: slotMetrics\n  }, /*#__PURE__*/React.createElement(\"div\", {\n    className: \"rbc-time-gutter rbc-time-column\",\n    ref: gutterRef\n  }, slotMetrics.groups.map(function (grp, idx) {\n    return /*#__PURE__*/React.createElement(TimeSlotGroup, {\n      key: idx,\n      group: grp,\n      resource: resource,\n      components: components,\n      renderSlot: renderSlot,\n      getters: getters\n    });\n  })));\n};\nvar TimeGutter$1 = /*#__PURE__*/React.forwardRef(function (props, ref) {\n  return /*#__PURE__*/React.createElement(TimeGutter, Object.assign({\n    gutterRef: ref\n  }, props));\n});\n\nvar NONE = {};\nfunction Resources(resources, accessors) {\n  return {\n    map: function map(fn) {\n      if (!resources) return [fn([NONE, null], 0)];\n      return resources.map(function (resource, idx) {\n        return fn([accessors.resourceId(resource), resource], idx);\n      });\n    },\n    groupEvents: function groupEvents(events) {\n      var eventsByResource = new Map();\n      if (!resources) {\n        // Return all events if resources are not provided\n        eventsByResource.set(NONE, events);\n        return eventsByResource;\n      }\n      events.forEach(function (event) {\n        var id = accessors.resource(event) || NONE;\n        if (Array.isArray(id)) {\n          id.forEach(function (item) {\n            var resourceEvents = eventsByResource.get(item) || [];\n            resourceEvents.push(event);\n            eventsByResource.set(item, resourceEvents);\n          });\n        } else {\n          var resourceEvents = eventsByResource.get(id) || [];\n          resourceEvents.push(event);\n          eventsByResource.set(id, resourceEvents);\n        }\n      });\n      return eventsByResource;\n    }\n  };\n}\n\nvar TimeGrid = /*#__PURE__*/function (_Component) {\n  function TimeGrid(props) {\n    var _this;\n    _classCallCheck(this, TimeGrid);\n    _this = _callSuper(this, TimeGrid, [props]);\n    _this.handleScroll = function (e) {\n      if (_this.scrollRef.current) {\n        _this.scrollRef.current.scrollLeft = e.target.scrollLeft;\n      }\n    };\n    _this.handleResize = function () {\n      animationFrame.cancel(_this.rafHandle);\n      _this.rafHandle = animationFrame.request(_this.checkOverflow);\n    };\n    _this.handleKeyPressEvent = function () {\n      _this.clearSelection();\n      for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {\n        args[_key] = arguments[_key];\n      }\n      notify(_this.props.onKeyPressEvent, args);\n    };\n    _this.handleSelectEvent = function () {\n      //cancel any pending selections so only the event click goes through.\n      _this.clearSelection();\n      for (var _len2 = arguments.length, args = new Array(_len2), _key2 = 0; _key2 < _len2; _key2++) {\n        args[_key2] = arguments[_key2];\n      }\n      notify(_this.props.onSelectEvent, args);\n    };\n    _this.handleDoubleClickEvent = function () {\n      _this.clearSelection();\n      for (var _len3 = arguments.length, args = new Array(_len3), _key3 = 0; _key3 < _len3; _key3++) {\n        args[_key3] = arguments[_key3];\n      }\n      notify(_this.props.onDoubleClickEvent, args);\n    };\n    _this.handleShowMore = function (events, date, cell, slot, target) {\n      var _this$props = _this.props,\n        popup = _this$props.popup,\n        onDrillDown = _this$props.onDrillDown,\n        onShowMore = _this$props.onShowMore,\n        getDrilldownView = _this$props.getDrilldownView,\n        doShowMoreDrillDown = _this$props.doShowMoreDrillDown;\n      _this.clearSelection();\n      if (popup) {\n        var position = getPosition$1(cell, _this.containerRef.current);\n        _this.setState({\n          overlay: {\n            date: date,\n            events: events,\n            position: _objectSpread(_objectSpread({}, position), {}, {\n              width: '200px'\n            }),\n            target: target\n          }\n        });\n      } else if (doShowMoreDrillDown) {\n        notify(onDrillDown, [date, getDrilldownView(date) || views.DAY]);\n      }\n      notify(onShowMore, [events, date, slot]);\n    };\n    _this.handleSelectAllDaySlot = function (slots, slotInfo) {\n      var onSelectSlot = _this.props.onSelectSlot;\n      var start = new Date(slots[0]);\n      var end = new Date(slots[slots.length - 1]);\n      end.setDate(slots[slots.length - 1].getDate() + 1);\n      notify(onSelectSlot, {\n        slots: slots,\n        start: start,\n        end: end,\n        action: slotInfo.action,\n        resourceId: slotInfo.resourceId\n      });\n    };\n    _this.overlayDisplay = function () {\n      _this.setState({\n        overlay: null\n      });\n    };\n    _this.checkOverflow = function () {\n      if (_this._updatingOverflow) return;\n      var content = _this.contentRef.current;\n      if (!(content !== null && content !== void 0 && content.scrollHeight)) return;\n      var isOverflowing = content.scrollHeight > content.clientHeight;\n      if (_this.state.isOverflowing !== isOverflowing) {\n        _this._updatingOverflow = true;\n        _this.setState({\n          isOverflowing: isOverflowing\n        }, function () {\n          _this._updatingOverflow = false;\n        });\n      }\n    };\n    _this.memoizedResources = memoize(function (resources, accessors) {\n      return Resources(resources, accessors);\n    });\n    _this.state = {\n      gutterWidth: undefined,\n      isOverflowing: null\n    };\n    _this.scrollRef = /*#__PURE__*/React.createRef();\n    _this.contentRef = /*#__PURE__*/React.createRef();\n    _this.containerRef = /*#__PURE__*/React.createRef();\n    _this._scrollRatio = null;\n    _this.gutterRef = /*#__PURE__*/createRef();\n    return _this;\n  }\n  _inherits(TimeGrid, _Component);\n  return _createClass(TimeGrid, [{\n    key: \"getSnapshotBeforeUpdate\",\n    value: function getSnapshotBeforeUpdate() {\n      this.checkOverflow();\n      return null;\n    }\n  }, {\n    key: \"componentDidMount\",\n    value: function componentDidMount() {\n      if (this.props.width == null) {\n        this.measureGutter();\n      }\n      this.calculateScroll();\n      this.applyScroll();\n      window.addEventListener('resize', this.handleResize);\n    }\n  }, {\n    key: \"componentWillUnmount\",\n    value: function componentWillUnmount() {\n      window.removeEventListener('resize', this.handleResize);\n      animationFrame.cancel(this.rafHandle);\n      if (this.measureGutterAnimationFrameRequest) {\n        window.cancelAnimationFrame(this.measureGutterAnimationFrameRequest);\n      }\n    }\n  }, {\n    key: \"componentDidUpdate\",\n    value: function componentDidUpdate() {\n      this.applyScroll();\n    }\n  }, {\n    key: \"renderDayColumn\",\n    value: function renderDayColumn(date, id, resource, groupedEvents, groupedBackgroundEvents, localizer, accessors, components, dayLayoutAlgorithm, now) {\n      var _this$props2 = this.props,\n        min = _this$props2.min,\n        max = _this$props2.max;\n      var daysEvents = (groupedEvents.get(id) || []).filter(function (event) {\n        return localizer.inRange(date, accessors.start(event), accessors.end(event), 'day');\n      });\n      var daysBackgroundEvents = (groupedBackgroundEvents.get(id) || []).filter(function (event) {\n        return localizer.inRange(date, accessors.start(event), accessors.end(event), 'day');\n      });\n      return /*#__PURE__*/React.createElement(DayColumn, Object.assign({}, this.props, {\n        localizer: localizer,\n        min: localizer.merge(date, min),\n        max: localizer.merge(date, max),\n        resource: resource && id,\n        components: components,\n        isNow: localizer.isSameDate(date, now),\n        key: \"\".concat(id, \"-\").concat(date),\n        date: date,\n        events: daysEvents,\n        backgroundEvents: daysBackgroundEvents,\n        dayLayoutAlgorithm: dayLayoutAlgorithm\n      }));\n    }\n  }, {\n    key: \"renderResourcesFirst\",\n    value: function renderResourcesFirst(range, resources, groupedEvents, groupedBackgroundEvents, localizer, accessors, now, components, dayLayoutAlgorithm) {\n      var _this2 = this;\n      return resources.map(function (_ref) {\n        var _ref2 = _slicedToArray(_ref, 2),\n          id = _ref2[0],\n          resource = _ref2[1];\n        return range.map(function (date) {\n          return _this2.renderDayColumn(date, id, resource, groupedEvents, groupedBackgroundEvents, localizer, accessors, components, dayLayoutAlgorithm, now);\n        });\n      });\n    }\n  }, {\n    key: \"renderRangeFirst\",\n    value: function renderRangeFirst(range, resources, groupedEvents, groupedBackgroundEvents, localizer, accessors, now, components, dayLayoutAlgorithm) {\n      var _this3 = this;\n      return range.map(function (date) {\n        return /*#__PURE__*/React.createElement(\"div\", {\n          style: {\n            display: 'flex',\n            minHeight: '100%',\n            flex: 1\n          },\n          key: date\n        }, resources.map(function (_ref3) {\n          var _ref4 = _slicedToArray(_ref3, 2),\n            id = _ref4[0],\n            resource = _ref4[1];\n          return /*#__PURE__*/React.createElement(\"div\", {\n            style: {\n              flex: 1\n            },\n            key: accessors.resourceId(resource)\n          }, _this3.renderDayColumn(date, id, resource, groupedEvents, groupedBackgroundEvents, localizer, accessors, components, dayLayoutAlgorithm, now));\n        }));\n      });\n    }\n  }, {\n    key: \"renderEvents\",\n    value: function renderEvents(range, events, backgroundEvents, now) {\n      var _this$props3 = this.props,\n        accessors = _this$props3.accessors,\n        localizer = _this$props3.localizer,\n        resourceGroupingLayout = _this$props3.resourceGroupingLayout,\n        components = _this$props3.components,\n        dayLayoutAlgorithm = _this$props3.dayLayoutAlgorithm;\n      var resources = this.memoizedResources(this.props.resources, accessors);\n      var groupedEvents = resources.groupEvents(events);\n      var groupedBackgroundEvents = resources.groupEvents(backgroundEvents);\n      if (!resourceGroupingLayout) {\n        return this.renderResourcesFirst(range, resources, groupedEvents, groupedBackgroundEvents, localizer, accessors, now, components, dayLayoutAlgorithm);\n      } else {\n        return this.renderRangeFirst(range, resources, groupedEvents, groupedBackgroundEvents, localizer, accessors, now, components, dayLayoutAlgorithm);\n      }\n    }\n  }, {\n    key: \"render\",\n    value: function render() {\n      var _this$props$allDayMax;\n      var _this$props4 = this.props,\n        events = _this$props4.events,\n        backgroundEvents = _this$props4.backgroundEvents,\n        range = _this$props4.range,\n        width = _this$props4.width,\n        rtl = _this$props4.rtl,\n        selected = _this$props4.selected,\n        getNow = _this$props4.getNow,\n        resources = _this$props4.resources,\n        components = _this$props4.components,\n        accessors = _this$props4.accessors,\n        getters = _this$props4.getters,\n        localizer = _this$props4.localizer,\n        min = _this$props4.min,\n        max = _this$props4.max,\n        showMultiDayTimes = _this$props4.showMultiDayTimes,\n        longPressThreshold = _this$props4.longPressThreshold,\n        resizable = _this$props4.resizable,\n        resourceGroupingLayout = _this$props4.resourceGroupingLayout;\n      width = width || this.state.gutterWidth;\n      var start = range[0],\n        end = range[range.length - 1];\n      this.slots = range.length;\n      var allDayEvents = [],\n        rangeEvents = [],\n        rangeBackgroundEvents = [];\n      events.forEach(function (event) {\n        if (inRange(event, start, end, accessors, localizer)) {\n          var eStart = accessors.start(event),\n            eEnd = accessors.end(event);\n          if (accessors.allDay(event) || localizer.startAndEndAreDateOnly(eStart, eEnd) || !showMultiDayTimes && !localizer.isSameDate(eStart, eEnd)) {\n            allDayEvents.push(event);\n          } else {\n            rangeEvents.push(event);\n          }\n        }\n      });\n      backgroundEvents.forEach(function (event) {\n        if (inRange(event, start, end, accessors, localizer)) {\n          rangeBackgroundEvents.push(event);\n        }\n      });\n      allDayEvents.sort(function (a, b) {\n        return sortEvents(a, b, accessors, localizer);\n      });\n      var headerProps = {\n        range: range,\n        events: allDayEvents,\n        width: width,\n        rtl: rtl,\n        getNow: getNow,\n        localizer: localizer,\n        selected: selected,\n        allDayMaxRows: this.props.showAllEvents ? Infinity : (_this$props$allDayMax = this.props.allDayMaxRows) !== null && _this$props$allDayMax !== void 0 ? _this$props$allDayMax : Infinity,\n        resources: this.memoizedResources(resources, accessors),\n        selectable: this.props.selectable,\n        accessors: accessors,\n        getters: getters,\n        components: components,\n        scrollRef: this.scrollRef,\n        isOverflowing: this.state.isOverflowing,\n        longPressThreshold: longPressThreshold,\n        onSelectSlot: this.handleSelectAllDaySlot,\n        onSelectEvent: this.handleSelectEvent,\n        onShowMore: this.handleShowMore,\n        onDoubleClickEvent: this.props.onDoubleClickEvent,\n        onKeyPressEvent: this.props.onKeyPressEvent,\n        onDrillDown: this.props.onDrillDown,\n        getDrilldownView: this.props.getDrilldownView,\n        resizable: resizable\n      };\n      return /*#__PURE__*/React.createElement(\"div\", {\n        className: clsx('rbc-time-view', resources && 'rbc-time-view-resources'),\n        ref: this.containerRef\n      }, resources && resources.length > 1 && resourceGroupingLayout ? /*#__PURE__*/React.createElement(TimeGridHeaderResources, headerProps) : /*#__PURE__*/React.createElement(TimeGridHeader, headerProps), this.props.popup && this.renderOverlay(), /*#__PURE__*/React.createElement(\"div\", {\n        ref: this.contentRef,\n        className: \"rbc-time-content\",\n        onScroll: this.handleScroll\n      }, /*#__PURE__*/React.createElement(TimeGutter$1, {\n        date: start,\n        ref: this.gutterRef,\n        localizer: localizer,\n        min: localizer.merge(start, min),\n        max: localizer.merge(start, max),\n        step: this.props.step,\n        getNow: this.props.getNow,\n        timeslots: this.props.timeslots,\n        components: components,\n        className: \"rbc-time-gutter\",\n        getters: getters\n      }), this.renderEvents(range, rangeEvents, rangeBackgroundEvents, getNow())));\n    }\n  }, {\n    key: \"renderOverlay\",\n    value: function renderOverlay() {\n      var _this$state$overlay,\n        _this$state,\n        _this4 = this;\n      var overlay = (_this$state$overlay = (_this$state = this.state) === null || _this$state === void 0 ? void 0 : _this$state.overlay) !== null && _this$state$overlay !== void 0 ? _this$state$overlay : {};\n      var _this$props5 = this.props,\n        accessors = _this$props5.accessors,\n        localizer = _this$props5.localizer,\n        components = _this$props5.components,\n        getters = _this$props5.getters,\n        selected = _this$props5.selected,\n        popupOffset = _this$props5.popupOffset,\n        handleDragStart = _this$props5.handleDragStart;\n      var onHide = function onHide() {\n        return _this4.setState({\n          overlay: null\n        });\n      };\n      return /*#__PURE__*/React.createElement(PopOverlay, {\n        overlay: overlay,\n        accessors: accessors,\n        localizer: localizer,\n        components: components,\n        getters: getters,\n        selected: selected,\n        popupOffset: popupOffset,\n        ref: this.containerRef,\n        handleKeyPressEvent: this.handleKeyPressEvent,\n        handleSelectEvent: this.handleSelectEvent,\n        handleDoubleClickEvent: this.handleDoubleClickEvent,\n        handleDragStart: handleDragStart,\n        show: !!overlay.position,\n        overlayDisplay: this.overlayDisplay,\n        onHide: onHide\n      });\n    }\n  }, {\n    key: \"clearSelection\",\n    value: function clearSelection() {\n      clearTimeout(this._selectTimer);\n      this._pendingSelection = [];\n    }\n  }, {\n    key: \"measureGutter\",\n    value: function measureGutter() {\n      var _this5 = this;\n      if (this.measureGutterAnimationFrameRequest) {\n        window.cancelAnimationFrame(this.measureGutterAnimationFrameRequest);\n      }\n      this.measureGutterAnimationFrameRequest = window.requestAnimationFrame(function () {\n        var _this5$gutterRef;\n        var width = (_this5$gutterRef = _this5.gutterRef) !== null && _this5$gutterRef !== void 0 && _this5$gutterRef.current ? getWidth(_this5.gutterRef.current) : undefined;\n        if (width && _this5.state.gutterWidth !== width) {\n          _this5.setState({\n            gutterWidth: width\n          });\n        }\n      });\n    }\n  }, {\n    key: \"applyScroll\",\n    value: function applyScroll() {\n      // If auto-scroll is disabled, we don't actually apply the scroll\n      if (this._scrollRatio != null && this.props.enableAutoScroll === true) {\n        var content = this.contentRef.current;\n        content.scrollTop = content.scrollHeight * this._scrollRatio;\n        // Only do this once\n        this._scrollRatio = null;\n      }\n    }\n  }, {\n    key: \"calculateScroll\",\n    value: function calculateScroll() {\n      var props = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : this.props;\n      var min = props.min,\n        max = props.max,\n        scrollToTime = props.scrollToTime,\n        localizer = props.localizer;\n      var diffMillis = localizer.diff(localizer.merge(scrollToTime, min), scrollToTime, 'milliseconds');\n      var totalMillis = localizer.diff(min, max, 'milliseconds');\n      this._scrollRatio = diffMillis / totalMillis;\n    }\n  }]);\n}(Component);\nTimeGrid.defaultProps = {\n  step: 30,\n  timeslots: 2,\n  // To be compatible with old versions, default as `false`.\n  resourceGroupingLayout: false\n};\n\nvar _excluded$4 = [\"date\", \"localizer\", \"min\", \"max\", \"scrollToTime\", \"enableAutoScroll\"];\nvar Day = /*#__PURE__*/function (_React$Component) {\n  function Day() {\n    _classCallCheck(this, Day);\n    return _callSuper(this, Day, arguments);\n  }\n  _inherits(Day, _React$Component);\n  return _createClass(Day, [{\n    key: \"render\",\n    value: function render() {\n      /**\n       * This allows us to default min, max, and scrollToTime\n       * using our localizer. This is necessary until such time\n       * as TODO: TimeGrid is converted to a functional component.\n       */\n      var _this$props = this.props,\n        date = _this$props.date,\n        localizer = _this$props.localizer,\n        _this$props$min = _this$props.min,\n        min = _this$props$min === void 0 ? localizer.startOf(new Date(), 'day') : _this$props$min,\n        _this$props$max = _this$props.max,\n        max = _this$props$max === void 0 ? localizer.endOf(new Date(), 'day') : _this$props$max,\n        _this$props$scrollToT = _this$props.scrollToTime,\n        scrollToTime = _this$props$scrollToT === void 0 ? localizer.startOf(new Date(), 'day') : _this$props$scrollToT,\n        _this$props$enableAut = _this$props.enableAutoScroll,\n        enableAutoScroll = _this$props$enableAut === void 0 ? true : _this$props$enableAut,\n        props = _objectWithoutProperties(_this$props, _excluded$4);\n      var range = Day.range(date, {\n        localizer: localizer\n      });\n      return /*#__PURE__*/React.createElement(TimeGrid, Object.assign({}, props, {\n        range: range,\n        eventOffset: 10,\n        localizer: localizer,\n        min: min,\n        max: max,\n        scrollToTime: scrollToTime,\n        enableAutoScroll: enableAutoScroll\n      }));\n    }\n  }]);\n}(React.Component);\nDay.range = function (date, _ref) {\n  var localizer = _ref.localizer;\n  return [localizer.startOf(date, 'day')];\n};\nDay.navigate = function (date, action, _ref2) {\n  var localizer = _ref2.localizer;\n  switch (action) {\n    case navigate.PREVIOUS:\n      return localizer.add(date, -1, 'day');\n    case navigate.NEXT:\n      return localizer.add(date, 1, 'day');\n    default:\n      return date;\n  }\n};\nDay.title = function (date, _ref3) {\n  var localizer = _ref3.localizer;\n  return localizer.format(date, 'dayHeaderFormat');\n};\n\nvar _excluded$3 = [\"date\", \"localizer\", \"min\", \"max\", \"scrollToTime\", \"enableAutoScroll\"];\nvar Week = /*#__PURE__*/function (_React$Component) {\n  function Week() {\n    _classCallCheck(this, Week);\n    return _callSuper(this, Week, arguments);\n  }\n  _inherits(Week, _React$Component);\n  return _createClass(Week, [{\n    key: \"render\",\n    value: function render() {\n      /**\n       * This allows us to default min, max, and scrollToTime\n       * using our localizer. This is necessary until such time\n       * as TimeGrid is converted to a functional component.\n       */\n      var _this$props = this.props,\n        date = _this$props.date,\n        localizer = _this$props.localizer,\n        _this$props$min = _this$props.min,\n        min = _this$props$min === void 0 ? localizer.startOf(new Date(), 'day') : _this$props$min,\n        _this$props$max = _this$props.max,\n        max = _this$props$max === void 0 ? localizer.endOf(new Date(), 'day') : _this$props$max,\n        _this$props$scrollToT = _this$props.scrollToTime,\n        scrollToTime = _this$props$scrollToT === void 0 ? localizer.startOf(new Date(), 'day') : _this$props$scrollToT,\n        _this$props$enableAut = _this$props.enableAutoScroll,\n        enableAutoScroll = _this$props$enableAut === void 0 ? true : _this$props$enableAut,\n        props = _objectWithoutProperties(_this$props, _excluded$3);\n      var range = Week.range(date, this.props);\n      return /*#__PURE__*/React.createElement(TimeGrid, Object.assign({}, props, {\n        range: range,\n        eventOffset: 15,\n        localizer: localizer,\n        min: min,\n        max: max,\n        scrollToTime: scrollToTime,\n        enableAutoScroll: enableAutoScroll\n      }));\n    }\n  }]);\n}(React.Component);\nWeek.defaultProps = TimeGrid.defaultProps;\nWeek.navigate = function (date, action, _ref) {\n  var localizer = _ref.localizer;\n  switch (action) {\n    case navigate.PREVIOUS:\n      return localizer.add(date, -1, 'week');\n    case navigate.NEXT:\n      return localizer.add(date, 1, 'week');\n    default:\n      return date;\n  }\n};\nWeek.range = function (date, _ref2) {\n  var localizer = _ref2.localizer;\n  var firstOfWeek = localizer.startOfWeek();\n  var start = localizer.startOf(date, 'week', firstOfWeek);\n  var end = localizer.endOf(date, 'week', firstOfWeek);\n  return localizer.range(start, end);\n};\nWeek.title = function (date, _ref3) {\n  var localizer = _ref3.localizer;\n  var _Week$range = Week.range(date, {\n      localizer: localizer\n    }),\n    _Week$range2 = _toArray(_Week$range),\n    start = _Week$range2[0],\n    rest = _Week$range2.slice(1);\n  return localizer.format({\n    start: start,\n    end: rest.pop()\n  }, 'dayRangeHeaderFormat');\n};\n\nvar _excluded$2 = [\"date\", \"localizer\", \"min\", \"max\", \"scrollToTime\", \"enableAutoScroll\"];\nfunction workWeekRange(date, options) {\n  return Week.range(date, options).filter(function (d) {\n    return [6, 0].indexOf(d.getDay()) === -1;\n  });\n}\nvar WorkWeek = /*#__PURE__*/function (_React$Component) {\n  function WorkWeek() {\n    _classCallCheck(this, WorkWeek);\n    return _callSuper(this, WorkWeek, arguments);\n  }\n  _inherits(WorkWeek, _React$Component);\n  return _createClass(WorkWeek, [{\n    key: \"render\",\n    value: function render() {\n      /**\n       * This allows us to default min, max, and scrollToTime\n       * using our localizer. This is necessary until such time\n       * as TimeGrid is converted to a functional component.\n       */\n      var _this$props = this.props,\n        date = _this$props.date,\n        localizer = _this$props.localizer,\n        _this$props$min = _this$props.min,\n        min = _this$props$min === void 0 ? localizer.startOf(new Date(), 'day') : _this$props$min,\n        _this$props$max = _this$props.max,\n        max = _this$props$max === void 0 ? localizer.endOf(new Date(), 'day') : _this$props$max,\n        _this$props$scrollToT = _this$props.scrollToTime,\n        scrollToTime = _this$props$scrollToT === void 0 ? localizer.startOf(new Date(), 'day') : _this$props$scrollToT,\n        _this$props$enableAut = _this$props.enableAutoScroll,\n        enableAutoScroll = _this$props$enableAut === void 0 ? true : _this$props$enableAut,\n        props = _objectWithoutProperties(_this$props, _excluded$2);\n      var range = workWeekRange(date, this.props);\n      return /*#__PURE__*/React.createElement(TimeGrid, Object.assign({}, props, {\n        range: range,\n        eventOffset: 15,\n        localizer: localizer,\n        min: min,\n        max: max,\n        scrollToTime: scrollToTime,\n        enableAutoScroll: enableAutoScroll\n      }));\n    }\n  }]);\n}(React.Component);\nWorkWeek.defaultProps = TimeGrid.defaultProps;\nWorkWeek.range = workWeekRange;\nWorkWeek.navigate = Week.navigate;\nWorkWeek.title = function (date, _ref) {\n  var localizer = _ref.localizer;\n  var _workWeekRange = workWeekRange(date, {\n      localizer: localizer\n    }),\n    _workWeekRange2 = _toArray(_workWeekRange),\n    start = _workWeekRange2[0],\n    rest = _workWeekRange2.slice(1);\n  return localizer.format({\n    start: start,\n    end: rest.pop()\n  }, 'dayRangeHeaderFormat');\n};\n\nvar DEFAULT_LENGTH = 30;\nfunction Agenda(_ref) {\n  var accessors = _ref.accessors,\n    components = _ref.components,\n    date = _ref.date,\n    events = _ref.events,\n    getters = _ref.getters,\n    _ref$length = _ref.length,\n    length = _ref$length === void 0 ? DEFAULT_LENGTH : _ref$length,\n    localizer = _ref.localizer,\n    onDoubleClickEvent = _ref.onDoubleClickEvent,\n    onSelectEvent = _ref.onSelectEvent,\n    selected = _ref.selected;\n  var headerRef = useRef(null);\n  var dateColRef = useRef(null);\n  var timeColRef = useRef(null);\n  var contentRef = useRef(null);\n  var tbodyRef = useRef(null);\n  useEffect(function () {\n    _adjustHeader();\n  });\n  var renderDay = function renderDay(day, events, dayKey) {\n    var Event = components.event,\n      AgendaDate = components.date;\n    events = events.filter(function (e) {\n      return inRange(e, localizer.startOf(day, 'day'), localizer.endOf(day, 'day'), accessors, localizer);\n    });\n    return events.map(function (event, idx) {\n      var title = accessors.title(event);\n      var end = accessors.end(event);\n      var start = accessors.start(event);\n      var userProps = getters.eventProp(event, start, end, isSelected(event, selected));\n      var dateLabel = idx === 0 && localizer.format(day, 'agendaDateFormat');\n      var first = idx === 0 ? /*#__PURE__*/React.createElement(\"td\", {\n        rowSpan: events.length,\n        className: \"rbc-agenda-date-cell\"\n      }, AgendaDate ? /*#__PURE__*/React.createElement(AgendaDate, {\n        day: day,\n        label: dateLabel\n      }) : dateLabel) : false;\n      return /*#__PURE__*/React.createElement(\"tr\", {\n        key: dayKey + '_' + idx,\n        className: userProps.className,\n        style: userProps.style\n      }, first, /*#__PURE__*/React.createElement(\"td\", {\n        className: \"rbc-agenda-time-cell\"\n      }, timeRangeLabel(day, event)), /*#__PURE__*/React.createElement(\"td\", {\n        className: \"rbc-agenda-event-cell\",\n        onClick: function onClick(e) {\n          return onSelectEvent && onSelectEvent(event, e);\n        },\n        onDoubleClick: function onDoubleClick(e) {\n          return onDoubleClickEvent && onDoubleClickEvent(event, e);\n        }\n      }, Event ? /*#__PURE__*/React.createElement(Event, {\n        event: event,\n        title: title\n      }) : title));\n    }, []);\n  };\n  var timeRangeLabel = function timeRangeLabel(day, event) {\n    var labelClass = '',\n      TimeComponent = components.time,\n      label = localizer.messages.allDay;\n    var end = accessors.end(event);\n    var start = accessors.start(event);\n    if (!accessors.allDay(event)) {\n      if (localizer.eq(start, end)) {\n        label = localizer.format(start, 'agendaTimeFormat');\n      } else if (localizer.isSameDate(start, end)) {\n        label = localizer.format({\n          start: start,\n          end: end\n        }, 'agendaTimeRangeFormat');\n      } else if (localizer.isSameDate(day, start)) {\n        label = localizer.format(start, 'agendaTimeFormat');\n      } else if (localizer.isSameDate(day, end)) {\n        label = localizer.format(end, 'agendaTimeFormat');\n      }\n    }\n    if (localizer.gt(day, start, 'day')) labelClass = 'rbc-continues-prior';\n    if (localizer.lt(day, end, 'day')) labelClass += ' rbc-continues-after';\n    return /*#__PURE__*/React.createElement(\"span\", {\n      className: labelClass.trim()\n    }, TimeComponent ? /*#__PURE__*/React.createElement(TimeComponent, {\n      event: event,\n      day: day,\n      label: label\n    }) : label);\n  };\n  var _adjustHeader = function _adjustHeader() {\n    if (!tbodyRef.current) return;\n    var header = headerRef.current;\n    var firstRow = tbodyRef.current.firstChild;\n    if (!firstRow) return;\n    var isOverflowing = contentRef.current.scrollHeight > contentRef.current.clientHeight;\n    var _widths = [];\n    var widths = _widths;\n    _widths = [getWidth(firstRow.children[0]), getWidth(firstRow.children[1])];\n    if (widths[0] !== _widths[0] || widths[1] !== _widths[1]) {\n      dateColRef.current.style.width = _widths[0] + 'px';\n      timeColRef.current.style.width = _widths[1] + 'px';\n    }\n    if (isOverflowing) {\n      addClass(header, 'rbc-header-overflowing');\n      header.style.marginRight = scrollbarSize() + 'px';\n    } else {\n      removeClass(header, 'rbc-header-overflowing');\n    }\n  };\n  var messages = localizer.messages;\n  var end = localizer.add(date, length, 'day');\n  var range = localizer.range(date, end, 'day');\n  events = events.filter(function (event) {\n    return inRange(event, localizer.startOf(date, 'day'), localizer.endOf(end, 'day'), accessors, localizer);\n  });\n  events.sort(function (a, b) {\n    return +accessors.start(a) - +accessors.start(b);\n  });\n  return /*#__PURE__*/React.createElement(\"div\", {\n    className: \"rbc-agenda-view\"\n  }, events.length !== 0 ? /*#__PURE__*/React.createElement(React.Fragment, null, /*#__PURE__*/React.createElement(\"table\", {\n    ref: headerRef,\n    className: \"rbc-agenda-table\"\n  }, /*#__PURE__*/React.createElement(\"thead\", null, /*#__PURE__*/React.createElement(\"tr\", null, /*#__PURE__*/React.createElement(\"th\", {\n    className: \"rbc-header\",\n    ref: dateColRef\n  }, messages.date), /*#__PURE__*/React.createElement(\"th\", {\n    className: \"rbc-header\",\n    ref: timeColRef\n  }, messages.time), /*#__PURE__*/React.createElement(\"th\", {\n    className: \"rbc-header\"\n  }, messages.event)))), /*#__PURE__*/React.createElement(\"div\", {\n    className: \"rbc-agenda-content\",\n    ref: contentRef\n  }, /*#__PURE__*/React.createElement(\"table\", {\n    className: \"rbc-agenda-table\"\n  }, /*#__PURE__*/React.createElement(\"tbody\", {\n    ref: tbodyRef\n  }, range.map(function (day, idx) {\n    return renderDay(day, events, idx);\n  }))))) : /*#__PURE__*/React.createElement(\"span\", {\n    className: \"rbc-agenda-empty\"\n  }, messages.noEventsInRange));\n}\nAgenda.range = function (start, _ref2) {\n  var _ref2$length = _ref2.length,\n    length = _ref2$length === void 0 ? DEFAULT_LENGTH : _ref2$length,\n    localizer = _ref2.localizer;\n  var end = localizer.add(start, length, 'day');\n  return {\n    start: start,\n    end: end\n  };\n};\nAgenda.navigate = function (date, action, _ref3) {\n  var _ref3$length = _ref3.length,\n    length = _ref3$length === void 0 ? DEFAULT_LENGTH : _ref3$length,\n    localizer = _ref3.localizer;\n  switch (action) {\n    case navigate.PREVIOUS:\n      return localizer.add(date, -length, 'day');\n    case navigate.NEXT:\n      return localizer.add(date, length, 'day');\n    default:\n      return date;\n  }\n};\nAgenda.title = function (start, _ref4) {\n  var _ref4$length = _ref4.length,\n    length = _ref4$length === void 0 ? DEFAULT_LENGTH : _ref4$length,\n    localizer = _ref4.localizer;\n  var end = localizer.add(start, length, 'day');\n  return localizer.format({\n    start: start,\n    end: end\n  }, 'agendaHeaderFormat');\n};\n\nvar VIEWS = _defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty({}, views.MONTH, MonthView), views.WEEK, Week), views.WORK_WEEK, WorkWeek), views.DAY, Day), views.AGENDA, Agenda);\n\nvar _excluded$1 = [\"action\", \"date\", \"today\"];\nfunction moveDate(View, _ref) {\n  var action = _ref.action,\n    date = _ref.date,\n    today = _ref.today,\n    props = _objectWithoutProperties(_ref, _excluded$1);\n  View = typeof View === 'string' ? VIEWS[View] : View;\n  switch (action) {\n    case navigate.TODAY:\n      date = today || new Date();\n      break;\n    case navigate.DATE:\n      break;\n    default:\n      invariant(View && typeof View.navigate === 'function', 'Calendar View components must implement a static `.navigate(date, action)` method.s');\n      date = View.navigate(date, action, props);\n  }\n  return date;\n}\n\n/**\n * Retrieve via an accessor-like property\n *\n *    accessor(obj, 'name')   // => retrieves obj['name']\n *    accessor(data, func)    // => retrieves func(data)\n *    ... otherwise null\n */\nfunction accessor(data, field) {\n  var value = null;\n  if (typeof field === 'function') value = field(data);else if (typeof field === 'string' && _typeof(data) === 'object' && data != null && field in data) value = data[field];\n  return value;\n}\nvar wrapAccessor = function wrapAccessor(acc) {\n  return function (data) {\n    return accessor(data, acc);\n  };\n};\n\nvar _excluded = [\"view\", \"date\", \"getNow\", \"onNavigate\"],\n  _excluded2 = [\"view\", \"toolbar\", \"events\", \"backgroundEvents\", \"resourceGroupingLayout\", \"style\", \"className\", \"elementProps\", \"date\", \"getNow\", \"length\", \"showMultiDayTimes\", \"onShowMore\", \"doShowMoreDrillDown\", \"components\", \"formats\", \"messages\", \"culture\"];\nfunction viewNames(_views) {\n  if (Array.isArray(_views)) {\n    return _views;\n  }\n  var views = [];\n  for (var _i = 0, _Object$entries = Object.entries(_views); _i < _Object$entries.length; _i++) {\n    var _Object$entries$_i = _slicedToArray(_Object$entries[_i], 2),\n      key = _Object$entries$_i[0],\n      value = _Object$entries$_i[1];\n    if (value) {\n      views.push(key);\n    }\n  }\n  return views;\n}\nfunction isValidView(view, _ref) {\n  var _views = _ref.views;\n  var names = viewNames(_views);\n  return names.indexOf(view) !== -1;\n}\nvar Calendar = /*#__PURE__*/function (_React$Component) {\n  function Calendar() {\n    var _this;\n    _classCallCheck(this, Calendar);\n    for (var _len = arguments.length, _args = new Array(_len), _key = 0; _key < _len; _key++) {\n      _args[_key] = arguments[_key];\n    }\n    _this = _callSuper(this, Calendar, [].concat(_args));\n    _this.getViews = function () {\n      var views = _this.props.views;\n      if (Array.isArray(views)) {\n        return transform(views, function (obj, name) {\n          return obj[name] = VIEWS[name];\n        }, {});\n      }\n      if (_typeof(views) === 'object') {\n        return mapValues(views, function (value, key) {\n          if (value === true) {\n            return VIEWS[key];\n          }\n          return value;\n        });\n      }\n      return VIEWS;\n    };\n    _this.getView = function () {\n      var views = _this.getViews();\n      return views[_this.props.view];\n    };\n    _this.getDrilldownView = function (date) {\n      var _this$props = _this.props,\n        view = _this$props.view,\n        drilldownView = _this$props.drilldownView,\n        getDrilldownView = _this$props.getDrilldownView;\n      if (!getDrilldownView) return drilldownView;\n      return getDrilldownView(date, view, Object.keys(_this.getViews()));\n    };\n    /**\n     *\n     * @param date\n     * @param viewComponent\n     * @param {'month'|'week'|'work_week'|'day'|'agenda'} [view] - optional\n     * parameter. It appears when range change on view changing. It could be handy\n     * when you need to have both: range and view type at once, i.e. for manage rbc\n     * state via url\n     */\n    _this.handleRangeChange = function (date, viewComponent, view) {\n      var _this$props2 = _this.props,\n        onRangeChange = _this$props2.onRangeChange,\n        localizer = _this$props2.localizer;\n      if (onRangeChange) {\n        if (viewComponent.range) {\n          onRangeChange(viewComponent.range(date, {\n            localizer: localizer\n          }), view);\n        } else {\n          if (process.env.NODE_ENV !== 'production') {\n            console.error('onRangeChange prop not supported for this view');\n          }\n        }\n      }\n    };\n    _this.handleNavigate = function (action, newDate) {\n      var _this$props3 = _this.props,\n        view = _this$props3.view,\n        date = _this$props3.date,\n        getNow = _this$props3.getNow,\n        onNavigate = _this$props3.onNavigate,\n        props = _objectWithoutProperties(_this$props3, _excluded);\n      var ViewComponent = _this.getView();\n      var today = getNow();\n      date = moveDate(ViewComponent, _objectSpread(_objectSpread({}, props), {}, {\n        action: action,\n        date: newDate || date || today,\n        today: today\n      }));\n      onNavigate(date, view, action);\n      _this.handleRangeChange(date, ViewComponent);\n    };\n    _this.handleViewChange = function (view) {\n      if (view !== _this.props.view && isValidView(view, _this.props)) {\n        _this.props.onView(view);\n      }\n      var views = _this.getViews();\n      _this.handleRangeChange(_this.props.date || _this.props.getNow(), views[view], view);\n    };\n    _this.handleSelectEvent = function () {\n      for (var _len2 = arguments.length, args = new Array(_len2), _key2 = 0; _key2 < _len2; _key2++) {\n        args[_key2] = arguments[_key2];\n      }\n      notify(_this.props.onSelectEvent, args);\n    };\n    _this.handleDoubleClickEvent = function () {\n      for (var _len3 = arguments.length, args = new Array(_len3), _key3 = 0; _key3 < _len3; _key3++) {\n        args[_key3] = arguments[_key3];\n      }\n      notify(_this.props.onDoubleClickEvent, args);\n    };\n    _this.handleKeyPressEvent = function () {\n      for (var _len4 = arguments.length, args = new Array(_len4), _key4 = 0; _key4 < _len4; _key4++) {\n        args[_key4] = arguments[_key4];\n      }\n      notify(_this.props.onKeyPressEvent, args);\n    };\n    _this.handleSelectSlot = function (slotInfo) {\n      notify(_this.props.onSelectSlot, slotInfo);\n    };\n    _this.handleDrillDown = function (date, view) {\n      var onDrillDown = _this.props.onDrillDown;\n      if (onDrillDown) {\n        onDrillDown(date, view, _this.drilldownView);\n        return;\n      }\n      if (view) _this.handleViewChange(view);\n      _this.handleNavigate(navigate.DATE, date);\n    };\n    _this.state = {\n      context: Calendar.getContext(_this.props)\n    };\n    return _this;\n  }\n  _inherits(Calendar, _React$Component);\n  return _createClass(Calendar, [{\n    key: \"render\",\n    value: function render() {\n      var _this$props4 = this.props,\n        view = _this$props4.view,\n        toolbar = _this$props4.toolbar,\n        events = _this$props4.events,\n        backgroundEvents = _this$props4.backgroundEvents,\n        resourceGroupingLayout = _this$props4.resourceGroupingLayout,\n        style = _this$props4.style,\n        className = _this$props4.className,\n        elementProps = _this$props4.elementProps,\n        current = _this$props4.date,\n        getNow = _this$props4.getNow,\n        length = _this$props4.length,\n        showMultiDayTimes = _this$props4.showMultiDayTimes,\n        onShowMore = _this$props4.onShowMore,\n        doShowMoreDrillDown = _this$props4.doShowMoreDrillDown;\n        _this$props4.components;\n        _this$props4.formats;\n        _this$props4.messages;\n        _this$props4.culture;\n        var props = _objectWithoutProperties(_this$props4, _excluded2);\n      current = current || getNow();\n      var View = this.getView();\n      var _this$state$context = this.state.context,\n        accessors = _this$state$context.accessors,\n        components = _this$state$context.components,\n        getters = _this$state$context.getters,\n        localizer = _this$state$context.localizer,\n        viewNames = _this$state$context.viewNames;\n      var CalToolbar = components.toolbar || Toolbar;\n      var label = View.title(current, {\n        localizer: localizer,\n        length: length\n      });\n      return /*#__PURE__*/React.createElement(\"div\", Object.assign({}, elementProps, {\n        className: clsx(className, 'rbc-calendar', props.rtl && 'rbc-rtl'),\n        style: style\n      }), toolbar && /*#__PURE__*/React.createElement(CalToolbar, {\n        date: current,\n        view: view,\n        views: viewNames,\n        label: label,\n        onView: this.handleViewChange,\n        onNavigate: this.handleNavigate,\n        localizer: localizer\n      }), /*#__PURE__*/React.createElement(View, Object.assign({}, props, {\n        events: events,\n        backgroundEvents: backgroundEvents,\n        date: current,\n        getNow: getNow,\n        length: length,\n        localizer: localizer,\n        getters: getters,\n        components: components,\n        accessors: accessors,\n        showMultiDayTimes: showMultiDayTimes,\n        getDrilldownView: this.getDrilldownView,\n        onNavigate: this.handleNavigate,\n        onDrillDown: this.handleDrillDown,\n        onSelectEvent: this.handleSelectEvent,\n        onDoubleClickEvent: this.handleDoubleClickEvent,\n        onKeyPressEvent: this.handleKeyPressEvent,\n        onSelectSlot: this.handleSelectSlot,\n        onShowMore: onShowMore,\n        doShowMoreDrillDown: doShowMoreDrillDown,\n        resourceGroupingLayout: resourceGroupingLayout\n      })));\n    }\n  }], [{\n    key: \"getDerivedStateFromProps\",\n    value: function getDerivedStateFromProps(nextProps) {\n      return {\n        context: Calendar.getContext(nextProps)\n      };\n    }\n  }, {\n    key: \"getContext\",\n    value: function getContext(_ref2) {\n      var startAccessor = _ref2.startAccessor,\n        endAccessor = _ref2.endAccessor,\n        allDayAccessor = _ref2.allDayAccessor,\n        tooltipAccessor = _ref2.tooltipAccessor,\n        titleAccessor = _ref2.titleAccessor,\n        resourceAccessor = _ref2.resourceAccessor,\n        resourceIdAccessor = _ref2.resourceIdAccessor,\n        resourceTitleAccessor = _ref2.resourceTitleAccessor,\n        eventIdAccessor = _ref2.eventIdAccessor,\n        eventPropGetter = _ref2.eventPropGetter,\n        backgroundEventPropGetter = _ref2.backgroundEventPropGetter,\n        slotPropGetter = _ref2.slotPropGetter,\n        slotGroupPropGetter = _ref2.slotGroupPropGetter,\n        dayPropGetter = _ref2.dayPropGetter,\n        view = _ref2.view,\n        views = _ref2.views,\n        localizer = _ref2.localizer,\n        culture = _ref2.culture,\n        _ref2$messages = _ref2.messages,\n        messages$1 = _ref2$messages === void 0 ? {} : _ref2$messages,\n        _ref2$components = _ref2.components,\n        components = _ref2$components === void 0 ? {} : _ref2$components,\n        _ref2$formats = _ref2.formats,\n        formats = _ref2$formats === void 0 ? {} : _ref2$formats;\n      var names = viewNames(views);\n      var msgs = messages(messages$1);\n      return {\n        viewNames: names,\n        localizer: mergeWithDefaults(localizer, culture, formats, msgs),\n        getters: {\n          eventProp: function eventProp() {\n            return eventPropGetter && eventPropGetter.apply(void 0, arguments) || {};\n          },\n          backgroundEventProp: function backgroundEventProp() {\n            return backgroundEventPropGetter && backgroundEventPropGetter.apply(void 0, arguments) || {};\n          },\n          slotProp: function slotProp() {\n            return slotPropGetter && slotPropGetter.apply(void 0, arguments) || {};\n          },\n          slotGroupProp: function slotGroupProp() {\n            return slotGroupPropGetter && slotGroupPropGetter.apply(void 0, arguments) || {};\n          },\n          dayProp: function dayProp() {\n            return dayPropGetter && dayPropGetter.apply(void 0, arguments) || {};\n          }\n        },\n        components: defaults(components[view] || {}, omit(components, names), {\n          eventWrapper: NoopWrapper,\n          backgroundEventWrapper: NoopWrapper,\n          eventContainerWrapper: NoopWrapper,\n          dateCellWrapper: NoopWrapper,\n          weekWrapper: NoopWrapper,\n          timeSlotWrapper: NoopWrapper,\n          timeGutterWrapper: NoopWrapper,\n          timeIndicatorWrapper: NoopWrapper\n        }),\n        accessors: {\n          start: wrapAccessor(startAccessor),\n          end: wrapAccessor(endAccessor),\n          allDay: wrapAccessor(allDayAccessor),\n          tooltip: wrapAccessor(tooltipAccessor),\n          title: wrapAccessor(titleAccessor),\n          resource: wrapAccessor(resourceAccessor),\n          resourceId: wrapAccessor(resourceIdAccessor),\n          resourceTitle: wrapAccessor(resourceTitleAccessor),\n          eventId: wrapAccessor(eventIdAccessor)\n        }\n      };\n    }\n  }]);\n}(React.Component);\nCalendar.defaultProps = {\n  events: [],\n  backgroundEvents: [],\n  elementProps: {},\n  popup: false,\n  toolbar: true,\n  view: views.MONTH,\n  views: [views.MONTH, views.WEEK, views.DAY, views.AGENDA],\n  step: 30,\n  length: 30,\n  allDayMaxRows: Infinity,\n  doShowMoreDrillDown: true,\n  drilldownView: views.DAY,\n  titleAccessor: 'title',\n  tooltipAccessor: 'title',\n  allDayAccessor: 'allDay',\n  startAccessor: 'start',\n  endAccessor: 'end',\n  resourceAccessor: 'resourceId',\n  resourceIdAccessor: 'id',\n  resourceTitleAccessor: 'title',\n  eventIdAccessor: 'id',\n  longPressThreshold: 250,\n  getNow: function getNow() {\n    return new Date();\n  },\n  dayLayoutAlgorithm: 'overlap'\n};\nvar Calendar$1 = uncontrollable(Calendar, {\n  view: 'onView',\n  date: 'onNavigate',\n  selected: 'onSelectEvent'\n});\n\nvar weekRangeFormat$5 = function weekRangeFormat(_ref, culture, local) {\n  var start = _ref.start,\n    end = _ref.end;\n  return local.format(start, 'MMMM DD', culture) + ' – ' +\n  // updated to use this localizer 'eq()' method\n  local.format(end, local.eq(start, end, 'month') ? 'DD' : 'MMMM DD', culture);\n};\nvar dateRangeFormat$5 = function dateRangeFormat(_ref2, culture, local) {\n  var start = _ref2.start,\n    end = _ref2.end;\n  return local.format(start, 'L', culture) + ' – ' + local.format(end, 'L', culture);\n};\nvar timeRangeFormat$5 = function timeRangeFormat(_ref3, culture, local) {\n  var start = _ref3.start,\n    end = _ref3.end;\n  return local.format(start, 'LT', culture) + ' – ' + local.format(end, 'LT', culture);\n};\nvar timeRangeStartFormat$5 = function timeRangeStartFormat(_ref4, culture, local) {\n  var start = _ref4.start;\n  return local.format(start, 'LT', culture) + ' – ';\n};\nvar timeRangeEndFormat$5 = function timeRangeEndFormat(_ref5, culture, local) {\n  var end = _ref5.end;\n  return ' – ' + local.format(end, 'LT', culture);\n};\nvar formats$5 = {\n  dateFormat: 'DD',\n  dayFormat: 'DD ddd',\n  weekdayFormat: 'ddd',\n  selectRangeFormat: timeRangeFormat$5,\n  eventTimeRangeFormat: timeRangeFormat$5,\n  eventTimeRangeStartFormat: timeRangeStartFormat$5,\n  eventTimeRangeEndFormat: timeRangeEndFormat$5,\n  timeGutterFormat: 'LT',\n  monthHeaderFormat: 'MMMM YYYY',\n  dayHeaderFormat: 'dddd MMM DD',\n  dayRangeHeaderFormat: weekRangeFormat$5,\n  agendaHeaderFormat: dateRangeFormat$5,\n  agendaDateFormat: 'ddd MMM DD',\n  agendaTimeFormat: 'LT',\n  agendaTimeRangeFormat: timeRangeFormat$5\n};\nfunction fixUnit$2(unit) {\n  var datePart = unit ? unit.toLowerCase() : unit;\n  if (datePart === 'FullYear') {\n    datePart = 'year';\n  } else if (!datePart) {\n    datePart = undefined;\n  }\n  return datePart;\n}\nfunction moment (moment) {\n  var locale = function locale(m, c) {\n    return c ? m.locale(c) : m;\n  };\n  function getTimezoneOffset(date) {\n    // ensures this gets cast to timezone\n    return moment(date).toDate().getTimezoneOffset();\n  }\n  function getDstOffset(start, end) {\n    var _st$_z$name, _st$_z;\n    // convert to moment, in case\n    // Calculate the offset in the timezone of the Events (local)\n    // not in the timezone of the calendar (moment.tz)\n    var st = moment(start).local();\n    var ed = moment(end).local();\n    // if not using moment timezone\n    if (!moment.tz) {\n      return st.toDate().getTimezoneOffset() - ed.toDate().getTimezoneOffset();\n    }\n    /**\n     * If using moment-timezone, and a timezone has been applied, then\n     * use this to get the proper timezone offset, otherwise default\n     * the timezone to the browser local\n     */\n    var tzName = (_st$_z$name = st === null || st === void 0 ? void 0 : (_st$_z = st._z) === null || _st$_z === void 0 ? void 0 : _st$_z.name) !== null && _st$_z$name !== void 0 ? _st$_z$name : moment.tz.guess();\n    var startOffset = moment.tz.zone(tzName).utcOffset(+st);\n    var endOffset = moment.tz.zone(tzName).utcOffset(+ed);\n    return startOffset - endOffset;\n  }\n  function getDayStartDstOffset(start) {\n    var dayStart = moment(start).startOf('day');\n    return getDstOffset(dayStart, start);\n  }\n\n  /*** BEGIN localized date arithmetic methods with moment ***/\n  function defineComparators(a, b, unit) {\n    var datePart = fixUnit$2(unit);\n    var dtA = datePart ? moment(a).startOf(datePart) : moment(a);\n    var dtB = datePart ? moment(b).startOf(datePart) : moment(b);\n    return [dtA, dtB, datePart];\n  }\n  function startOf() {\n    var date = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : null;\n    var unit = arguments.length > 1 ? arguments[1] : undefined;\n    var datePart = fixUnit$2(unit);\n    if (datePart) {\n      return moment(date).startOf(datePart).toDate();\n    }\n    return moment(date).toDate();\n  }\n  function endOf() {\n    var date = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : null;\n    var unit = arguments.length > 1 ? arguments[1] : undefined;\n    var datePart = fixUnit$2(unit);\n    if (datePart) {\n      return moment(date).endOf(datePart).toDate();\n    }\n    return moment(date).toDate();\n  }\n\n  // moment comparison operations *always* convert both sides to moment objects\n  // prior to running the comparisons\n  function eq(a, b, unit) {\n    var _defineComparators = defineComparators(a, b, unit),\n      _defineComparators2 = _slicedToArray(_defineComparators, 3),\n      dtA = _defineComparators2[0],\n      dtB = _defineComparators2[1],\n      datePart = _defineComparators2[2];\n    return dtA.isSame(dtB, datePart);\n  }\n  function neq(a, b, unit) {\n    return !eq(a, b, unit);\n  }\n  function gt(a, b, unit) {\n    var _defineComparators3 = defineComparators(a, b, unit),\n      _defineComparators4 = _slicedToArray(_defineComparators3, 3),\n      dtA = _defineComparators4[0],\n      dtB = _defineComparators4[1],\n      datePart = _defineComparators4[2];\n    return dtA.isAfter(dtB, datePart);\n  }\n  function lt(a, b, unit) {\n    var _defineComparators5 = defineComparators(a, b, unit),\n      _defineComparators6 = _slicedToArray(_defineComparators5, 3),\n      dtA = _defineComparators6[0],\n      dtB = _defineComparators6[1],\n      datePart = _defineComparators6[2];\n    return dtA.isBefore(dtB, datePart);\n  }\n  function gte(a, b, unit) {\n    var _defineComparators7 = defineComparators(a, b, unit),\n      _defineComparators8 = _slicedToArray(_defineComparators7, 3),\n      dtA = _defineComparators8[0],\n      dtB = _defineComparators8[1],\n      datePart = _defineComparators8[2];\n    return dtA.isSameOrBefore(dtB, datePart);\n  }\n  function lte(a, b, unit) {\n    var _defineComparators9 = defineComparators(a, b, unit),\n      _defineComparators10 = _slicedToArray(_defineComparators9, 3),\n      dtA = _defineComparators10[0],\n      dtB = _defineComparators10[1],\n      datePart = _defineComparators10[2];\n    return dtA.isSameOrBefore(dtB, datePart);\n  }\n  function inRange(day, min, max) {\n    var unit = arguments.length > 3 && arguments[3] !== undefined ? arguments[3] : 'day';\n    var datePart = fixUnit$2(unit);\n    var mDay = moment(day);\n    var mMin = moment(min);\n    var mMax = moment(max);\n    return mDay.isBetween(mMin, mMax, datePart, '[]');\n  }\n  function min(dateA, dateB) {\n    var dtA = moment(dateA);\n    var dtB = moment(dateB);\n    var minDt = moment.min(dtA, dtB);\n    return minDt.toDate();\n  }\n  function max(dateA, dateB) {\n    var dtA = moment(dateA);\n    var dtB = moment(dateB);\n    var maxDt = moment.max(dtA, dtB);\n    return maxDt.toDate();\n  }\n  function merge(date, time) {\n    if (!date && !time) return null;\n    var tm = moment(time).format('HH:mm:ss');\n    var dt = moment(date).startOf('day').format('MM/DD/YYYY');\n    // We do it this way to avoid issues when timezone switching\n    return moment(\"\".concat(dt, \" \").concat(tm), 'MM/DD/YYYY HH:mm:ss').toDate();\n  }\n  function add(date, adder, unit) {\n    var datePart = fixUnit$2(unit);\n    return moment(date).add(adder, datePart).toDate();\n  }\n  function range(start, end) {\n    var unit = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : 'day';\n    var datePart = fixUnit$2(unit);\n    // because the add method will put these in tz, we have to start that way\n    var current = moment(start).toDate();\n    var days = [];\n    while (lte(current, end)) {\n      days.push(current);\n      current = add(current, 1, datePart);\n    }\n    return days;\n  }\n  function ceil(date, unit) {\n    var datePart = fixUnit$2(unit);\n    var floor = startOf(date, datePart);\n    return eq(floor, date) ? floor : add(floor, 1, datePart);\n  }\n  function diff(a, b) {\n    var unit = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : 'day';\n    var datePart = fixUnit$2(unit);\n    // don't use 'defineComparators' here, as we don't want to mutate the values\n    var dtA = moment(a);\n    var dtB = moment(b);\n    return dtB.diff(dtA, datePart);\n  }\n  function minutes(date) {\n    var dt = moment(date);\n    return dt.minutes();\n  }\n  function firstOfWeek(culture) {\n    var data = culture ? moment.localeData(culture) : moment.localeData();\n    return data ? data.firstDayOfWeek() : 0;\n  }\n  function firstVisibleDay(date) {\n    return moment(date).startOf('month').startOf('week').toDate();\n  }\n  function lastVisibleDay(date) {\n    return moment(date).endOf('month').endOf('week').toDate();\n  }\n  function visibleDays(date) {\n    var current = firstVisibleDay(date);\n    var last = lastVisibleDay(date);\n    var days = [];\n    while (lte(current, last)) {\n      days.push(current);\n      current = add(current, 1, 'd');\n    }\n    return days;\n  }\n  /*** END localized date arithmetic methods with moment ***/\n\n  /**\n   * Moved from TimeSlots.js, this method overrides the method of the same name\n   * in the localizer.js, using moment to construct the js Date\n   * @param {Date} dt - date to start with\n   * @param {Number} minutesFromMidnight\n   * @param {Number} offset\n   * @returns {Date}\n   */\n  function getSlotDate(dt, minutesFromMidnight, offset) {\n    return moment(dt).startOf('day').minute(minutesFromMidnight + offset).toDate();\n  }\n\n  // moment will automatically handle DST differences in it's calculations\n  function getTotalMin(start, end) {\n    return diff(start, end, 'minutes');\n  }\n  function getMinutesFromMidnight(start) {\n    var dayStart = moment(start).startOf('day');\n    var day = moment(start);\n    return day.diff(dayStart, 'minutes') + getDayStartDstOffset(start);\n  }\n\n  // These two are used by DateSlotMetrics\n  function continuesPrior(start, first) {\n    var mStart = moment(start);\n    var mFirst = moment(first);\n    return mStart.isBefore(mFirst, 'day');\n  }\n  function continuesAfter(start, end, last) {\n    var mEnd = moment(end);\n    var mLast = moment(last);\n    return mEnd.isSameOrAfter(mLast, 'minutes');\n  }\n  function daySpan(start, end) {\n    var mStart = moment(start);\n    var mEnd = moment(end);\n    var dur = moment.duration(mEnd.diff(mStart));\n    return dur.days();\n  }\n\n  // These two are used by eventLevels\n  function sortEvents(_ref6) {\n    var _ref6$evtA = _ref6.evtA,\n      aStart = _ref6$evtA.start,\n      aEnd = _ref6$evtA.end,\n      aAllDay = _ref6$evtA.allDay,\n      _ref6$evtB = _ref6.evtB,\n      bStart = _ref6$evtB.start,\n      bEnd = _ref6$evtB.end,\n      bAllDay = _ref6$evtB.allDay;\n    var startSort = +startOf(aStart, 'day') - +startOf(bStart, 'day');\n    var durA = daySpan(aStart, aEnd);\n    var durB = daySpan(bStart, bEnd);\n    return startSort ||\n    // sort by start Day first\n    durB - durA ||\n    // events spanning multiple days go first\n    !!bAllDay - !!aAllDay ||\n    // then allDay single day events\n    +aStart - +bStart ||\n    // then sort by start time *don't need moment conversion here\n    +aEnd - +bEnd // then sort by end time *don't need moment conversion here either\n    ;\n  }\n  function inEventRange(_ref7) {\n    var _ref7$event = _ref7.event,\n      start = _ref7$event.start,\n      end = _ref7$event.end,\n      _ref7$range = _ref7.range,\n      rangeStart = _ref7$range.start,\n      rangeEnd = _ref7$range.end;\n    var startOfDay = moment(start).startOf('day');\n    var eEnd = moment(end);\n    var rStart = moment(rangeStart);\n    var rEnd = moment(rangeEnd);\n    var startsBeforeEnd = startOfDay.isSameOrBefore(rEnd, 'day');\n    // when the event is zero duration we need to handle a bit differently\n    var sameMin = !startOfDay.isSame(eEnd, 'minutes');\n    var endsAfterStart = sameMin ? eEnd.isAfter(rStart, 'minutes') : eEnd.isSameOrAfter(rStart, 'minutes');\n    return startsBeforeEnd && endsAfterStart;\n  }\n  function isSameDate(date1, date2) {\n    var dt = moment(date1);\n    var dt2 = moment(date2);\n    return dt.isSame(dt2, 'day');\n  }\n\n  /**\n   * This method, called once in the localizer constructor, is used by eventLevels\n   * 'eventSegments()' to assist in determining the 'span' of the event in the display,\n   * specifically when using a timezone that is greater than the browser native timezone.\n   * @returns number\n   */\n  function browserTZOffset() {\n    /**\n     * Date.prototype.getTimezoneOffset horrifically flips the positive/negative from\n     * what you see in it's string, so we have to jump through some hoops to get a value\n     * we can actually compare.\n     */\n    var dt = new Date();\n    var neg = /-/.test(dt.toString()) ? '-' : '';\n    var dtOffset = dt.getTimezoneOffset();\n    var comparator = Number(\"\".concat(neg).concat(Math.abs(dtOffset)));\n    // moment correctly provides positive/negative offset, as expected\n    var mtOffset = moment().utcOffset();\n    return mtOffset > comparator ? 1 : 0;\n  }\n  return new DateLocalizer({\n    formats: formats$5,\n    firstOfWeek: firstOfWeek,\n    firstVisibleDay: firstVisibleDay,\n    lastVisibleDay: lastVisibleDay,\n    visibleDays: visibleDays,\n    format: function format(value, _format, culture) {\n      return locale(moment(value), culture).format(_format);\n    },\n    lt: lt,\n    lte: lte,\n    gt: gt,\n    gte: gte,\n    eq: eq,\n    neq: neq,\n    merge: merge,\n    inRange: inRange,\n    startOf: startOf,\n    endOf: endOf,\n    range: range,\n    add: add,\n    diff: diff,\n    ceil: ceil,\n    min: min,\n    max: max,\n    minutes: minutes,\n    getSlotDate: getSlotDate,\n    getTimezoneOffset: getTimezoneOffset,\n    getDstOffset: getDstOffset,\n    getTotalMin: getTotalMin,\n    getMinutesFromMidnight: getMinutesFromMidnight,\n    continuesPrior: continuesPrior,\n    continuesAfter: continuesAfter,\n    sortEvents: sortEvents,\n    inEventRange: inEventRange,\n    isSameDate: isSameDate,\n    daySpan: daySpan,\n    browserTZOffset: browserTZOffset\n  });\n}\n\nfunction pluralizeUnit(unit) {\n  return /s$/.test(unit) ? unit : unit + 's';\n}\nvar weekRangeFormat$4 = function weekRangeFormat(_ref, culture, local) {\n  var start = _ref.start,\n    end = _ref.end;\n  return local.format(start, 'MMMM dd', culture) + ' – ' +\n  // updated to use this localizer 'eq()' method\n  local.format(end, local.eq(start, end, 'month') ? 'dd' : 'MMMM dd', culture);\n};\nvar dateRangeFormat$4 = function dateRangeFormat(_ref2, culture, local) {\n  var start = _ref2.start,\n    end = _ref2.end;\n  return local.format(start, 'D', culture) + ' – ' + local.format(end, 'D', culture);\n};\nvar timeRangeFormat$4 = function timeRangeFormat(_ref3, culture, local) {\n  var start = _ref3.start,\n    end = _ref3.end;\n  return local.format(start, 't', culture) + ' – ' + local.format(end, 't', culture);\n};\nvar timeRangeStartFormat$4 = function timeRangeStartFormat(_ref4, culture, local) {\n  var start = _ref4.start;\n  return local.format(start, 't', culture) + ' – ';\n};\nvar timeRangeEndFormat$4 = function timeRangeEndFormat(_ref5, culture, local) {\n  var end = _ref5.end;\n  return ' – ' + local.format(end, 't', culture);\n};\nvar formats$4 = {\n  dateFormat: 'dd',\n  dayFormat: 'dd EEE',\n  weekdayFormat: 'EEE',\n  selectRangeFormat: timeRangeFormat$4,\n  eventTimeRangeFormat: timeRangeFormat$4,\n  eventTimeRangeStartFormat: timeRangeStartFormat$4,\n  eventTimeRangeEndFormat: timeRangeEndFormat$4,\n  timeGutterFormat: 't',\n  monthHeaderFormat: 'MMMM yyyy',\n  dayHeaderFormat: 'EEEE MMM dd',\n  dayRangeHeaderFormat: weekRangeFormat$4,\n  agendaHeaderFormat: dateRangeFormat$4,\n  agendaDateFormat: 'EEE MMM dd',\n  agendaTimeFormat: 't',\n  agendaTimeRangeFormat: timeRangeFormat$4\n};\nfunction fixUnit$1(unit) {\n  var datePart = unit ? pluralizeUnit(unit.toLowerCase()) : unit;\n  if (datePart === 'FullYear') {\n    datePart = 'year';\n  } else if (!datePart) {\n    datePart = undefined;\n  }\n  return datePart;\n}\n\n// Luxon does not currently have weekInfo by culture\n// Luxon uses 1 based values for month and weekday\n// So we default to Sunday (7)\nfunction luxon (DateTime) {\n  var _ref6 = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {},\n    _ref6$firstDayOfWeek = _ref6.firstDayOfWeek,\n    firstDayOfWeek = _ref6$firstDayOfWeek === void 0 ? 7 : _ref6$firstDayOfWeek;\n  function formatDate(value, format) {\n    return DateTime.fromJSDate(value).toFormat(format);\n  }\n  function formatDateWithCulture(value, culture, format) {\n    return DateTime.fromJSDate(value).setLocale(culture).toFormat(format);\n  }\n\n  /*** BEGIN localized date arithmetic methods with Luxon ***/\n  function defineComparators(a, b, unit) {\n    var datePart = fixUnit$1(unit);\n    var dtA = datePart ? DateTime.fromJSDate(a).startOf(datePart) : DateTime.fromJSDate(a);\n    var dtB = datePart ? DateTime.fromJSDate(b).startOf(datePart) : DateTime.fromJSDate(b);\n    return [dtA, dtB, datePart];\n  }\n\n  // Since Luxon (and current Intl API) has no support\n  // for culture based weekInfo, we need to handle\n  // the start of the week differently\n  // depending on locale, the firstDayOfWeek could also be Saturday, Sunday or Monday\n  function startOfDTWeek(dtObj) {\n    var weekday = dtObj.weekday;\n    if (weekday === firstDayOfWeek) {\n      return dtObj.startOf('day'); // already beginning of week\n    } else if (firstDayOfWeek === 1) {\n      return dtObj.startOf('week'); // fow is Monday, which is Luxon default\n    }\n    var diff = firstDayOfWeek === 7 ? weekday : weekday + (7 - firstDayOfWeek);\n    return dtObj.minus({\n      day: diff\n    }).startOf('day');\n  }\n  function endOfDTWeek(dtObj) {\n    var weekday = dtObj.weekday;\n    var eow = firstDayOfWeek === 1 ? 7 : firstDayOfWeek - 1;\n    if (weekday === eow) {\n      return dtObj.endOf('day'); // already last day of the week\n    } else if (firstDayOfWeek === 1) {\n      return dtObj.endOf('week'); // use Luxon default (Sunday)\n    }\n    var fromDate = firstDayOfWeek > eow ? dtObj.plus({\n      day: firstDayOfWeek - eow\n    }) : dtObj;\n    return fromDate.set({\n      weekday: eow\n    }).endOf('day');\n  }\n\n  // This returns a DateTime instance\n  function startOfDT() {\n    var date = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : new Date();\n    var unit = arguments.length > 1 ? arguments[1] : undefined;\n    var datePart = fixUnit$1(unit);\n    if (datePart) {\n      var dt = DateTime.fromJSDate(date);\n      return datePart.includes('week') ? startOfDTWeek(dt) : dt.startOf(datePart);\n    }\n    return DateTime.fromJSDate(date);\n  }\n  function firstOfWeek() {\n    return firstDayOfWeek;\n  }\n\n  // This returns a JS Date from a DateTime instance\n  function startOf() {\n    var date = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : new Date();\n    var unit = arguments.length > 1 ? arguments[1] : undefined;\n    return startOfDT(date, unit).toJSDate();\n  }\n\n  // This returns a DateTime instance\n  function endOfDT() {\n    var date = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : new Date();\n    var unit = arguments.length > 1 ? arguments[1] : undefined;\n    var datePart = fixUnit$1(unit);\n    if (datePart) {\n      var dt = DateTime.fromJSDate(date);\n      return datePart.includes('week') ? endOfDTWeek(dt) : dt.endOf(datePart);\n    }\n    return DateTime.fromJSDate(date);\n  }\n  function endOf() {\n    var date = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : new Date();\n    var unit = arguments.length > 1 ? arguments[1] : undefined;\n    return endOfDT(date, unit).toJSDate();\n  }\n  function eq(a, b, unit) {\n    var _defineComparators = defineComparators(a, b, unit),\n      _defineComparators2 = _slicedToArray(_defineComparators, 2),\n      dtA = _defineComparators2[0],\n      dtB = _defineComparators2[1];\n    return +dtA == +dtB;\n  }\n  function neq(a, b, unit) {\n    return !eq(a, b, unit);\n  }\n  function gt(a, b, unit) {\n    var _defineComparators3 = defineComparators(a, b, unit),\n      _defineComparators4 = _slicedToArray(_defineComparators3, 2),\n      dtA = _defineComparators4[0],\n      dtB = _defineComparators4[1];\n    return +dtA > +dtB;\n  }\n  function lt(a, b, unit) {\n    var _defineComparators5 = defineComparators(a, b, unit),\n      _defineComparators6 = _slicedToArray(_defineComparators5, 2),\n      dtA = _defineComparators6[0],\n      dtB = _defineComparators6[1];\n    return +dtA < +dtB;\n  }\n  function gte(a, b, unit) {\n    var _defineComparators7 = defineComparators(a, b, unit),\n      _defineComparators8 = _slicedToArray(_defineComparators7, 2),\n      dtA = _defineComparators8[0],\n      dtB = _defineComparators8[1];\n    return +dtA >= +dtB;\n  }\n  function lte(a, b, unit) {\n    var _defineComparators9 = defineComparators(a, b, unit),\n      _defineComparators10 = _slicedToArray(_defineComparators9, 2),\n      dtA = _defineComparators10[0],\n      dtB = _defineComparators10[1];\n    return +dtA <= +dtB;\n  }\n  function inRange(day, min, max) {\n    var unit = arguments.length > 3 && arguments[3] !== undefined ? arguments[3] : 'day';\n    var datePart = fixUnit$1(unit);\n    var mDay = startOfDT(day, datePart);\n    var mMin = startOfDT(min, datePart);\n    var mMax = startOfDT(max, datePart);\n    return +mDay >= +mMin && +mDay <= +mMax;\n  }\n  function min(dateA, dateB) {\n    var dtA = DateTime.fromJSDate(dateA);\n    var dtB = DateTime.fromJSDate(dateB);\n    var minDt = DateTime.min(dtA, dtB);\n    return minDt.toJSDate();\n  }\n  function max(dateA, dateB) {\n    var dtA = DateTime.fromJSDate(dateA);\n    var dtB = DateTime.fromJSDate(dateB);\n    var maxDt = DateTime.max(dtA, dtB);\n    return maxDt.toJSDate();\n  }\n  function merge(date, time) {\n    if (!date && !time) return null;\n    var tm = DateTime.fromJSDate(time);\n    var dt = startOfDT(date, 'day');\n    return dt.set({\n      hour: tm.hour,\n      minute: tm.minute,\n      second: tm.second,\n      millisecond: tm.millisecond\n    }).toJSDate();\n  }\n  function add(date, adder, unit) {\n    var datePart = fixUnit$1(unit);\n    return DateTime.fromJSDate(date).plus(_defineProperty({}, datePart, adder)).toJSDate();\n  }\n  function range(start, end) {\n    var unit = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : 'day';\n    var datePart = fixUnit$1(unit);\n    var current = DateTime.fromJSDate(start).toJSDate(); // this is to get it to tz\n    var days = [];\n    while (lte(current, end)) {\n      days.push(current);\n      current = add(current, 1, datePart);\n    }\n    return days;\n  }\n  function ceil(date, unit) {\n    var datePart = fixUnit$1(unit);\n    var floor = startOf(date, datePart);\n    return eq(floor, date) ? floor : add(floor, 1, datePart);\n  }\n  function diff(a, b) {\n    var unit = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : 'day';\n    var datePart = fixUnit$1(unit);\n    // don't use 'defineComparators' here, as we don't want to mutate the values\n    var dtA = DateTime.fromJSDate(a);\n    var dtB = DateTime.fromJSDate(b);\n    return Math.floor(dtB.diff(dtA, datePart, {\n      conversionAccuracy: 'longterm'\n    }).toObject()[datePart]);\n  }\n  function firstVisibleDay(date) {\n    var startOfMonth = startOfDT(date, 'month');\n    return startOfDTWeek(startOfMonth).toJSDate();\n  }\n  function lastVisibleDay(date) {\n    var endOfMonth = endOfDT(date, 'month');\n    return endOfDTWeek(endOfMonth).toJSDate();\n  }\n  function visibleDays(date) {\n    var current = firstVisibleDay(date);\n    var last = lastVisibleDay(date);\n    var days = [];\n    while (lte(current, last)) {\n      days.push(current);\n      current = add(current, 1, 'day');\n    }\n    return days;\n  }\n  /*** END localized date arithmetic methods with moment ***/\n\n  /**\n   * Moved from TimeSlots.js, this method overrides the method of the same name\n   * in the localizer.js, using moment to construct the js Date\n   * @param {Date} dt - date to start with\n   * @param {Number} minutesFromMidnight\n   * @param {Number} offset\n   * @returns {Date}\n   */\n  function getSlotDate(dt, minutesFromMidnight, offset) {\n    return startOfDT(dt, 'day').set({\n      minutes: minutesFromMidnight + offset\n    }).toJSDate();\n  }\n\n  // Luxon will automatically handle DST differences in it's calculations\n  function getTotalMin(start, end) {\n    return diff(start, end, 'minutes');\n  }\n  function getMinutesFromMidnight(start) {\n    var dayStart = startOfDT(start, 'day');\n    var day = DateTime.fromJSDate(start);\n    return Math.round(day.diff(dayStart, 'minutes', {\n      conversionAccuracy: 'longterm'\n    }).toObject().minutes);\n  }\n\n  // These two are used by DateSlotMetrics\n  function continuesPrior(start, first) {\n    return lt(start, first);\n  }\n  function continuesAfter(start, end, last) {\n    return gte(end, last);\n  }\n  function daySpan(start, end) {\n    var dtStart = DateTime.fromJSDate(start);\n    var dtEnd = DateTime.fromJSDate(end);\n    return dtEnd.diff(dtStart).as('days');\n  }\n\n  // These two are used by eventLevels\n  function sortEvents(_ref7) {\n    var _ref7$evtA = _ref7.evtA,\n      aStart = _ref7$evtA.start,\n      aEnd = _ref7$evtA.end,\n      aAllDay = _ref7$evtA.allDay,\n      _ref7$evtB = _ref7.evtB,\n      bStart = _ref7$evtB.start,\n      bEnd = _ref7$evtB.end,\n      bAllDay = _ref7$evtB.allDay;\n    var startSort = +startOf(aStart, 'day') - +startOf(bStart, 'day');\n    var durA = daySpan(aStart, aEnd);\n    var durB = daySpan(bStart, bEnd);\n    return startSort ||\n    // sort by start Day first\n    durB - durA ||\n    // events spanning multiple days go first\n    !!bAllDay - !!aAllDay ||\n    // then allDay single day events\n    +aStart - +bStart ||\n    // then sort by start time *don't need moment conversion here\n    +aEnd - +bEnd // then sort by end time *don't need moment conversion here either\n    ;\n  }\n  function inEventRange(_ref8) {\n    var _ref8$event = _ref8.event,\n      start = _ref8$event.start,\n      end = _ref8$event.end,\n      _ref8$range = _ref8.range,\n      rangeStart = _ref8$range.start,\n      rangeEnd = _ref8$range.end;\n    var eStart = startOf(start, 'day');\n    var startsBeforeEnd = lte(eStart, rangeEnd, 'day');\n    // when the event is zero duration we need to handle a bit differently\n    var sameMin = neq(eStart, end, 'minutes');\n    var endsAfterStart = sameMin ? gt(end, rangeStart, 'minutes') : gte(end, rangeStart, 'minutes');\n    return startsBeforeEnd && endsAfterStart;\n  }\n\n  // moment treats 'day' and 'date' equality very different\n  // moment(date1).isSame(date2, 'day') would test that they were both the same day of the week\n  // moment(date1).isSame(date2, 'date') would test that they were both the same date of the month of the year\n  function isSameDate(date1, date2) {\n    var dt = DateTime.fromJSDate(date1);\n    var dt2 = DateTime.fromJSDate(date2);\n    return dt.hasSame(dt2, 'day');\n  }\n\n  /**\n   * This method, called once in the localizer constructor, is used by eventLevels\n   * 'eventSegments()' to assist in determining the 'span' of the event in the display,\n   * specifically when using a timezone that is greater than the browser native timezone.\n   * @returns number\n   */\n  function browserTZOffset() {\n    /**\n     * Date.prototype.getTimezoneOffset horrifically flips the positive/negative from\n     * what you see in it's string, so we have to jump through some hoops to get a value\n     * we can actually compare.\n     */\n    var dt = new Date();\n    var neg = /-/.test(dt.toString()) ? '-' : '';\n    var dtOffset = dt.getTimezoneOffset();\n    var comparator = Number(\"\".concat(neg).concat(Math.abs(dtOffset)));\n    // moment correctly provides positive/negative offset, as expected\n    var mtOffset = DateTime.local().offset;\n    return mtOffset > comparator ? 1 : 0;\n  }\n  return new DateLocalizer({\n    format: function format(value, _format, culture) {\n      if (culture) {\n        return formatDateWithCulture(value, culture, _format);\n      }\n      return formatDate(value, _format);\n    },\n    formats: formats$4,\n    firstOfWeek: firstOfWeek,\n    firstVisibleDay: firstVisibleDay,\n    lastVisibleDay: lastVisibleDay,\n    visibleDays: visibleDays,\n    lt: lt,\n    lte: lte,\n    gt: gt,\n    gte: gte,\n    eq: eq,\n    neq: neq,\n    merge: merge,\n    inRange: inRange,\n    startOf: startOf,\n    endOf: endOf,\n    range: range,\n    add: add,\n    diff: diff,\n    ceil: ceil,\n    min: min,\n    max: max,\n    getSlotDate: getSlotDate,\n    getTotalMin: getTotalMin,\n    getMinutesFromMidnight: getMinutesFromMidnight,\n    continuesPrior: continuesPrior,\n    continuesAfter: continuesAfter,\n    sortEvents: sortEvents,\n    inEventRange: inEventRange,\n    isSameDate: isSameDate,\n    daySpan: daySpan,\n    browserTZOffset: browserTZOffset\n  });\n}\n\nvar dateRangeFormat$3 = function dateRangeFormat(_ref, culture, local) {\n  var start = _ref.start,\n    end = _ref.end;\n  return local.format(start, 'd', culture) + ' – ' + local.format(end, 'd', culture);\n};\nvar timeRangeFormat$3 = function timeRangeFormat(_ref2, culture, local) {\n  var start = _ref2.start,\n    end = _ref2.end;\n  return local.format(start, 't', culture) + ' – ' + local.format(end, 't', culture);\n};\nvar timeRangeStartFormat$3 = function timeRangeStartFormat(_ref3, culture, local) {\n  var start = _ref3.start;\n  return local.format(start, 't', culture) + ' – ';\n};\nvar timeRangeEndFormat$3 = function timeRangeEndFormat(_ref4, culture, local) {\n  var end = _ref4.end;\n  return ' – ' + local.format(end, 't', culture);\n};\nvar weekRangeFormat$3 = function weekRangeFormat(_ref5, culture, local) {\n  var start = _ref5.start,\n    end = _ref5.end;\n  return local.format(start, 'MMM dd', culture) + ' – ' + local.format(end, eq(start, end, 'month') ? 'dd' : 'MMM dd', culture);\n};\nvar formats$3 = {\n  dateFormat: 'dd',\n  dayFormat: 'ddd dd/MM',\n  weekdayFormat: 'ddd',\n  selectRangeFormat: timeRangeFormat$3,\n  eventTimeRangeFormat: timeRangeFormat$3,\n  eventTimeRangeStartFormat: timeRangeStartFormat$3,\n  eventTimeRangeEndFormat: timeRangeEndFormat$3,\n  timeGutterFormat: 't',\n  monthHeaderFormat: 'Y',\n  dayHeaderFormat: 'dddd MMM dd',\n  dayRangeHeaderFormat: weekRangeFormat$3,\n  agendaHeaderFormat: dateRangeFormat$3,\n  agendaDateFormat: 'ddd MMM dd',\n  agendaTimeFormat: 't',\n  agendaTimeRangeFormat: timeRangeFormat$3\n};\nfunction oldGlobalize (globalize) {\n  function getCulture(culture) {\n    return culture ? globalize.findClosestCulture(culture) : globalize.culture();\n  }\n  function firstOfWeek(culture) {\n    culture = getCulture(culture);\n    return culture && culture.calendar.firstDay || 0;\n  }\n  return new DateLocalizer({\n    firstOfWeek: firstOfWeek,\n    formats: formats$3,\n    format: function format(value, _format, culture) {\n      return globalize.format(value, _format, culture);\n    }\n  });\n}\n\n// TODO: fix the globalizeLocalizer to work with globalize 1.x\n\nvar dateRangeFormat$2 = function dateRangeFormat(_ref, culture, local) {\n  var start = _ref.start,\n    end = _ref.end;\n  return local.format(start, {\n    date: 'short'\n  }, culture) + ' – ' + local.format(end, {\n    date: 'short'\n  }, culture);\n};\nvar timeRangeFormat$2 = function timeRangeFormat(_ref2, culture, local) {\n  var start = _ref2.start,\n    end = _ref2.end;\n  return local.format(start, {\n    time: 'short'\n  }, culture) + ' – ' + local.format(end, {\n    time: 'short'\n  }, culture);\n};\nvar timeRangeStartFormat$2 = function timeRangeStartFormat(_ref3, culture, local) {\n  var start = _ref3.start;\n  return local.format(start, {\n    time: 'short'\n  }, culture) + ' – ';\n};\nvar timeRangeEndFormat$2 = function timeRangeEndFormat(_ref4, culture, local) {\n  var end = _ref4.end;\n  return ' – ' + local.format(end, {\n    time: 'short'\n  }, culture);\n};\nvar weekRangeFormat$2 = function weekRangeFormat(_ref5, culture, local) {\n  var start = _ref5.start,\n    end = _ref5.end;\n  return local.format(start, 'MMM dd', culture) + ' – ' + local.format(end, eq(start, end, 'month') ? 'dd' : 'MMM dd', culture);\n};\nvar formats$2 = {\n  dateFormat: 'dd',\n  dayFormat: 'eee dd/MM',\n  weekdayFormat: 'eee',\n  selectRangeFormat: timeRangeFormat$2,\n  eventTimeRangeFormat: timeRangeFormat$2,\n  eventTimeRangeStartFormat: timeRangeStartFormat$2,\n  eventTimeRangeEndFormat: timeRangeEndFormat$2,\n  timeGutterFormat: {\n    time: 'short'\n  },\n  monthHeaderFormat: 'MMMM yyyy',\n  dayHeaderFormat: 'eeee MMM dd',\n  dayRangeHeaderFormat: weekRangeFormat$2,\n  agendaHeaderFormat: dateRangeFormat$2,\n  agendaDateFormat: 'eee MMM dd',\n  agendaTimeFormat: {\n    time: 'short'\n  },\n  agendaTimeRangeFormat: timeRangeFormat$2\n};\nfunction globalize (globalize) {\n  var locale = function locale(culture) {\n    return culture ? globalize(culture) : globalize;\n  };\n\n  // return the first day of the week from the locale data. Defaults to 'world'\n  // territory if no territory is derivable from CLDR.\n  // Failing to use CLDR supplemental (not loaded?), revert to the original\n  // method of getting first day of week.\n  function firstOfWeek(culture) {\n    try {\n      var days = ['sun', 'mon', 'tue', 'wed', 'thu', 'fri', 'sat'];\n      var cldr = locale(culture).cldr;\n      var territory = cldr.attributes.territory;\n      var weekData = cldr.get('supplemental').weekData;\n      var firstDay = weekData.firstDay[territory || '001'];\n      return days.indexOf(firstDay);\n    } catch (e) {\n      if (process.env.NODE_ENV !== 'production') {\n        console.error('Failed to accurately determine first day of the week.' + ' Is supplemental data loaded into CLDR?');\n      }\n      // maybe cldr supplemental is not loaded? revert to original method\n      var date = new Date();\n      //cldr-data doesn't seem to be zero based\n      var localeDay = Math.max(parseInt(locale(culture).formatDate(date, {\n        raw: 'e'\n      }), 10) - 1, 0);\n      return Math.abs(date.getDay() - localeDay);\n    }\n  }\n  if (!globalize.load) return oldGlobalize(globalize);\n  return new DateLocalizer({\n    firstOfWeek: firstOfWeek,\n    formats: formats$2,\n    format: function format(value, _format, culture) {\n      _format = typeof _format === 'string' ? {\n        raw: _format\n      } : _format;\n      return locale(culture).formatDate(value, _format);\n    }\n  });\n}\n\nvar dateRangeFormat$1 = function dateRangeFormat(_ref, culture, local) {\n  var start = _ref.start,\n    end = _ref.end;\n  return \"\".concat(local.format(start, 'P', culture), \" \\u2013 \").concat(local.format(end, 'P', culture));\n};\nvar timeRangeFormat$1 = function timeRangeFormat(_ref2, culture, local) {\n  var start = _ref2.start,\n    end = _ref2.end;\n  return \"\".concat(local.format(start, 'p', culture), \" \\u2013 \").concat(local.format(end, 'p', culture));\n};\nvar timeRangeStartFormat$1 = function timeRangeStartFormat(_ref3, culture, local) {\n  var start = _ref3.start;\n  return \"\".concat(local.format(start, 'h:mma', culture), \" \\u2013 \");\n};\nvar timeRangeEndFormat$1 = function timeRangeEndFormat(_ref4, culture, local) {\n  var end = _ref4.end;\n  return \" \\u2013 \".concat(local.format(end, 'h:mma', culture));\n};\nvar weekRangeFormat$1 = function weekRangeFormat(_ref5, culture, local) {\n  var start = _ref5.start,\n    end = _ref5.end;\n  return \"\".concat(local.format(start, 'MMMM dd', culture), \" \\u2013 \").concat(local.format(end, eq(start, end, 'month') ? 'dd' : 'MMMM dd', culture));\n};\nvar formats$1 = {\n  dateFormat: 'dd',\n  dayFormat: 'dd eee',\n  weekdayFormat: 'ccc',\n  selectRangeFormat: timeRangeFormat$1,\n  eventTimeRangeFormat: timeRangeFormat$1,\n  eventTimeRangeStartFormat: timeRangeStartFormat$1,\n  eventTimeRangeEndFormat: timeRangeEndFormat$1,\n  timeGutterFormat: 'p',\n  monthHeaderFormat: 'MMMM yyyy',\n  dayHeaderFormat: 'cccc MMM dd',\n  dayRangeHeaderFormat: weekRangeFormat$1,\n  agendaHeaderFormat: dateRangeFormat$1,\n  agendaDateFormat: 'ccc MMM dd',\n  agendaTimeFormat: 'p',\n  agendaTimeRangeFormat: timeRangeFormat$1\n};\nvar dateFnsLocalizer = function dateFnsLocalizer(_ref6) {\n  var startOfWeek = _ref6.startOfWeek,\n    getDay = _ref6.getDay,\n    _format = _ref6.format,\n    locales = _ref6.locales;\n  return new DateLocalizer({\n    formats: formats$1,\n    firstOfWeek: function firstOfWeek(culture) {\n      return getDay(startOfWeek(new Date(), {\n        locale: locales[culture]\n      }));\n    },\n    format: function format(value, formatString, culture) {\n      return _format(new Date(value), formatString, {\n        locale: locales[culture]\n      });\n    }\n  });\n};\n\nvar weekRangeFormat = function weekRangeFormat(_ref, culture, local) {\n  var start = _ref.start,\n    end = _ref.end;\n  return local.format(start, 'MMMM DD', culture) + ' – ' +\n  // updated to use this localizer 'eq()' method\n  local.format(end, local.eq(start, end, 'month') ? 'DD' : 'MMMM DD', culture);\n};\nvar dateRangeFormat = function dateRangeFormat(_ref2, culture, local) {\n  var start = _ref2.start,\n    end = _ref2.end;\n  return local.format(start, 'L', culture) + ' – ' + local.format(end, 'L', culture);\n};\nvar timeRangeFormat = function timeRangeFormat(_ref3, culture, local) {\n  var start = _ref3.start,\n    end = _ref3.end;\n  return local.format(start, 'LT', culture) + ' – ' + local.format(end, 'LT', culture);\n};\nvar timeRangeStartFormat = function timeRangeStartFormat(_ref4, culture, local) {\n  var start = _ref4.start;\n  return local.format(start, 'LT', culture) + ' – ';\n};\nvar timeRangeEndFormat = function timeRangeEndFormat(_ref5, culture, local) {\n  var end = _ref5.end;\n  return ' – ' + local.format(end, 'LT', culture);\n};\nvar formats = {\n  dateFormat: 'DD',\n  dayFormat: 'DD ddd',\n  weekdayFormat: 'ddd',\n  selectRangeFormat: timeRangeFormat,\n  eventTimeRangeFormat: timeRangeFormat,\n  eventTimeRangeStartFormat: timeRangeStartFormat,\n  eventTimeRangeEndFormat: timeRangeEndFormat,\n  timeGutterFormat: 'LT',\n  monthHeaderFormat: 'MMMM YYYY',\n  dayHeaderFormat: 'dddd MMM DD',\n  dayRangeHeaderFormat: weekRangeFormat,\n  agendaHeaderFormat: dateRangeFormat,\n  agendaDateFormat: 'ddd MMM DD',\n  agendaTimeFormat: 'LT',\n  agendaTimeRangeFormat: timeRangeFormat\n};\nfunction fixUnit(unit) {\n  var datePart = unit ? unit.toLowerCase() : unit;\n  if (datePart === 'FullYear') {\n    datePart = 'year';\n  } else if (!datePart) {\n    datePart = undefined;\n  }\n  return datePart;\n}\nfunction dayjs (dayjsLib) {\n  // load dayjs plugins\n  dayjsLib.extend(isBetween);\n  dayjsLib.extend(isSameOrAfter);\n  dayjsLib.extend(isSameOrBefore);\n  dayjsLib.extend(localeData);\n  dayjsLib.extend(localizedFormat);\n  dayjsLib.extend(minMax);\n  dayjsLib.extend(utc);\n  dayjsLib.extend(isLeapYear);\n  var locale = function locale(dj, c) {\n    return c ? dj.locale(c) : dj;\n  };\n\n  // if the timezone plugin is loaded,\n  // then use the timezone aware version\n  var dayjs = dayjsLib.tz ? dayjsLib.tz : dayjsLib;\n  function getTimezoneOffset(date) {\n    // ensures this gets cast to timezone\n    return dayjs(date).toDate().getTimezoneOffset();\n  }\n  function getDstOffset(start, end) {\n    var _st$tz$$x$$timezone;\n    // convert to dayjs, in case\n    var st = dayjs(start);\n    var ed = dayjs(end);\n    // if not using the dayjs timezone plugin\n    if (!dayjs.tz) {\n      return st.toDate().getTimezoneOffset() - ed.toDate().getTimezoneOffset();\n    }\n    /**\n     * If a default timezone has been applied, then\n     * use this to get the proper timezone offset, otherwise default\n     * the timezone to the browser local\n     */\n    var tzName = (_st$tz$$x$$timezone = st.tz().$x.$timezone) !== null && _st$tz$$x$$timezone !== void 0 ? _st$tz$$x$$timezone : dayjsLib.tz.guess();\n    // invert offsets to be inline with moment.js\n    var startOffset = -dayjs.tz(+st, tzName).utcOffset();\n    var endOffset = -dayjs.tz(+ed, tzName).utcOffset();\n    return startOffset - endOffset;\n  }\n  function getDayStartDstOffset(start) {\n    var dayStart = dayjs(start).startOf('day');\n    return getDstOffset(dayStart, start);\n  }\n\n  /*** BEGIN localized date arithmetic methods with dayjs ***/\n  function defineComparators(a, b, unit) {\n    var datePart = fixUnit(unit);\n    var dtA = datePart ? dayjs(a).startOf(datePart) : dayjs(a);\n    var dtB = datePart ? dayjs(b).startOf(datePart) : dayjs(b);\n    return [dtA, dtB, datePart];\n  }\n  function startOf() {\n    var date = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : null;\n    var unit = arguments.length > 1 ? arguments[1] : undefined;\n    var datePart = fixUnit(unit);\n    if (datePart) {\n      return dayjs(date).startOf(datePart).toDate();\n    }\n    return dayjs(date).toDate();\n  }\n  function endOf() {\n    var date = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : null;\n    var unit = arguments.length > 1 ? arguments[1] : undefined;\n    var datePart = fixUnit(unit);\n    if (datePart) {\n      return dayjs(date).endOf(datePart).toDate();\n    }\n    return dayjs(date).toDate();\n  }\n\n  // dayjs comparison operations *always* convert both sides to dayjs objects\n  // prior to running the comparisons\n  function eq(a, b, unit) {\n    var _defineComparators = defineComparators(a, b, unit),\n      _defineComparators2 = _slicedToArray(_defineComparators, 3),\n      dtA = _defineComparators2[0],\n      dtB = _defineComparators2[1],\n      datePart = _defineComparators2[2];\n    return dtA.isSame(dtB, datePart);\n  }\n  function neq(a, b, unit) {\n    return !eq(a, b, unit);\n  }\n  function gt(a, b, unit) {\n    var _defineComparators3 = defineComparators(a, b, unit),\n      _defineComparators4 = _slicedToArray(_defineComparators3, 3),\n      dtA = _defineComparators4[0],\n      dtB = _defineComparators4[1],\n      datePart = _defineComparators4[2];\n    return dtA.isAfter(dtB, datePart);\n  }\n  function lt(a, b, unit) {\n    var _defineComparators5 = defineComparators(a, b, unit),\n      _defineComparators6 = _slicedToArray(_defineComparators5, 3),\n      dtA = _defineComparators6[0],\n      dtB = _defineComparators6[1],\n      datePart = _defineComparators6[2];\n    return dtA.isBefore(dtB, datePart);\n  }\n  function gte(a, b, unit) {\n    var _defineComparators7 = defineComparators(a, b, unit),\n      _defineComparators8 = _slicedToArray(_defineComparators7, 3),\n      dtA = _defineComparators8[0],\n      dtB = _defineComparators8[1],\n      datePart = _defineComparators8[2];\n    return dtA.isSameOrBefore(dtB, datePart);\n  }\n  function lte(a, b, unit) {\n    var _defineComparators9 = defineComparators(a, b, unit),\n      _defineComparators10 = _slicedToArray(_defineComparators9, 3),\n      dtA = _defineComparators10[0],\n      dtB = _defineComparators10[1],\n      datePart = _defineComparators10[2];\n    return dtA.isSameOrBefore(dtB, datePart);\n  }\n  function inRange(day, min, max) {\n    var unit = arguments.length > 3 && arguments[3] !== undefined ? arguments[3] : 'day';\n    var datePart = fixUnit(unit);\n    var djDay = dayjs(day);\n    var djMin = dayjs(min);\n    var djMax = dayjs(max);\n    return djDay.isBetween(djMin, djMax, datePart, '[]');\n  }\n  function min(dateA, dateB) {\n    var dtA = dayjs(dateA);\n    var dtB = dayjs(dateB);\n    var minDt = dayjsLib.min(dtA, dtB);\n    return minDt.toDate();\n  }\n  function max(dateA, dateB) {\n    var dtA = dayjs(dateA);\n    var dtB = dayjs(dateB);\n    var maxDt = dayjsLib.max(dtA, dtB);\n    return maxDt.toDate();\n  }\n  function merge(date, time) {\n    if (!date && !time) return null;\n    var tm = dayjs(time).format('HH:mm:ss');\n    var dt = dayjs(date).startOf('day').format('MM/DD/YYYY');\n    // We do it this way to avoid issues when timezone switching\n    var mergedDateTime = dayjs(\"\".concat(dt, \" \").concat(tm)).toDate();\n    return dayjsLib(mergedDateTime).utc(true).toDate();\n  }\n  function add(date, adder, unit) {\n    var datePart = fixUnit(unit);\n    return dayjs(date).add(adder, datePart).toDate();\n  }\n  function range(start, end) {\n    var unit = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : 'day';\n    var datePart = fixUnit(unit);\n    // because the add method will put these in tz, we have to start that way\n    var current = dayjs(start).toDate();\n    var days = [];\n    while (lte(current, end)) {\n      days.push(current);\n      current = add(current, 1, datePart);\n    }\n    return days;\n  }\n  function ceil(date, unit) {\n    var datePart = fixUnit(unit);\n    var floor = startOf(date, datePart);\n    return eq(floor, date) ? floor : add(floor, 1, datePart);\n  }\n  function diff(a, b) {\n    var unit = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : 'day';\n    var datePart = fixUnit(unit);\n    // don't use 'defineComparators' here, as we don't want to mutate the values\n    var dtA = dayjs(a);\n    var dtB = dayjs(b);\n    return dtB.diff(dtA, datePart);\n  }\n  function minutes(date) {\n    var dt = dayjs(date);\n    return dt.minutes();\n  }\n  function firstOfWeek(culture) {\n    var data = culture ? dayjsLib.localeData(culture) : dayjsLib.localeData();\n    return data ? data.firstDayOfWeek() : 0;\n  }\n  function firstVisibleDay(date) {\n    var firstDayOfMonth = dayjs(date).startOf('month');\n    var firstDayOfWeek = dayjs(firstDayOfMonth).startOf('week');\n    // special handling for leapyears until Dayjs patches it\n    if (dayjs(firstDayOfMonth).isLeapYear()) {\n      var day = firstDayOfMonth.toDate().getDay(),\n        _diff = firstDayOfMonth.toDate().getDate() - day + (day == 0 ? -6 : 1);\n      firstDayOfWeek.date(_diff);\n    }\n    return firstDayOfWeek.toDate();\n  }\n  function lastVisibleDay(date) {\n    return dayjs(date).endOf('month').endOf('week').toDate();\n  }\n  function visibleDays(date) {\n    var current = firstVisibleDay(date);\n    var last = lastVisibleDay(date);\n    var days = [];\n    while (lte(current, last)) {\n      days.push(current);\n      current = add(current, 1, 'd');\n    }\n    return days;\n  }\n  /*** END localized date arithmetic methods with dayjs ***/\n\n  /**\n   * Moved from TimeSlots.js, this method overrides the method of the same name\n   * in the localizer.js, using dayjs to construct the js Date\n   * @param {Date} dt - date to start with\n   * @param {Number} minutesFromMidnight\n   * @param {Number} offset\n   * @returns {Date}\n   */\n  function getSlotDate(dt, minutesFromMidnight, offset) {\n    return dayjs(dt).startOf('day').minute(minutesFromMidnight + offset).toDate();\n  }\n\n  // dayjs will automatically handle DST differences in it's calculations\n  function getTotalMin(start, end) {\n    return diff(start, end, 'minutes');\n  }\n  function getMinutesFromMidnight(start) {\n    var dayStart = dayjs(start).startOf('day');\n    var day = dayjs(start);\n    return day.diff(dayStart, 'minutes') + getDayStartDstOffset(start);\n  }\n\n  // These two are used by DateSlotMetrics\n  function continuesPrior(start, first) {\n    var djStart = dayjs(start);\n    var djFirst = dayjs(first);\n    return djStart.isBefore(djFirst, 'day');\n  }\n  function continuesAfter(start, end, last) {\n    var djEnd = dayjs(end);\n    var djLast = dayjs(last);\n    return djEnd.isSameOrAfter(djLast, 'minutes');\n  }\n  function daySpan(start, end) {\n    var startDay = dayjs(start);\n    var endDay = dayjs(end);\n    return endDay.diff(startDay, 'day');\n  }\n\n  // These two are used by eventLevels\n  function sortEvents(_ref6) {\n    var _ref6$evtA = _ref6.evtA,\n      aStart = _ref6$evtA.start,\n      aEnd = _ref6$evtA.end,\n      aAllDay = _ref6$evtA.allDay,\n      _ref6$evtB = _ref6.evtB,\n      bStart = _ref6$evtB.start,\n      bEnd = _ref6$evtB.end,\n      bAllDay = _ref6$evtB.allDay;\n    var startSort = +startOf(aStart, 'day') - +startOf(bStart, 'day');\n    var durA = daySpan(aStart, aEnd);\n    var durB = daySpan(bStart, bEnd);\n    return startSort ||\n    // sort by start Day first\n    durB - durA ||\n    // events spanning multiple days go first\n    !!bAllDay - !!aAllDay ||\n    // then allDay single day events\n    +aStart - +bStart ||\n    // then sort by start time *don't need dayjs conversion here\n    +aEnd - +bEnd // then sort by end time *don't need dayjs conversion here either\n    ;\n  }\n  function inEventRange(_ref7) {\n    var _ref7$event = _ref7.event,\n      start = _ref7$event.start,\n      end = _ref7$event.end,\n      _ref7$range = _ref7.range,\n      rangeStart = _ref7$range.start,\n      rangeEnd = _ref7$range.end;\n    var startOfDay = dayjs(start).startOf('day');\n    var eEnd = dayjs(end);\n    var rStart = dayjs(rangeStart);\n    var rEnd = dayjs(rangeEnd);\n    var startsBeforeEnd = startOfDay.isSameOrBefore(rEnd, 'day');\n    // when the event is zero duration we need to handle a bit differently\n    var sameMin = !startOfDay.isSame(eEnd, 'minutes');\n    var endsAfterStart = sameMin ? eEnd.isAfter(rStart, 'minutes') : eEnd.isSameOrAfter(rStart, 'minutes');\n    return startsBeforeEnd && endsAfterStart;\n  }\n  function isSameDate(date1, date2) {\n    var dt = dayjs(date1);\n    var dt2 = dayjs(date2);\n    return dt.isSame(dt2, 'day');\n  }\n\n  /**\n   * This method, called once in the localizer constructor, is used by eventLevels\n   * 'eventSegments()' to assist in determining the 'span' of the event in the display,\n   * specifically when using a timezone that is greater than the browser native timezone.\n   * @returns number\n   */\n  function browserTZOffset() {\n    /**\n     * Date.prototype.getTimezoneOffset horrifically flips the positive/negative from\n     * what you see in it's string, so we have to jump through some hoops to get a value\n     * we can actually compare.\n     */\n    var dt = new Date();\n    var neg = /-/.test(dt.toString()) ? '-' : '';\n    var dtOffset = dt.getTimezoneOffset();\n    var comparator = Number(\"\".concat(neg).concat(Math.abs(dtOffset)));\n    // dayjs correctly provides positive/negative offset, as expected\n    var mtOffset = dayjs().utcOffset();\n    return mtOffset > comparator ? 1 : 0;\n  }\n  return new DateLocalizer({\n    formats: formats,\n    firstOfWeek: firstOfWeek,\n    firstVisibleDay: firstVisibleDay,\n    lastVisibleDay: lastVisibleDay,\n    visibleDays: visibleDays,\n    format: function format(value, _format, culture) {\n      return locale(dayjs(value), culture).format(_format);\n    },\n    lt: lt,\n    lte: lte,\n    gt: gt,\n    gte: gte,\n    eq: eq,\n    neq: neq,\n    merge: merge,\n    inRange: inRange,\n    startOf: startOf,\n    endOf: endOf,\n    range: range,\n    add: add,\n    diff: diff,\n    ceil: ceil,\n    min: min,\n    max: max,\n    minutes: minutes,\n    getSlotDate: getSlotDate,\n    getTimezoneOffset: getTimezoneOffset,\n    getDstOffset: getDstOffset,\n    getTotalMin: getTotalMin,\n    getMinutesFromMidnight: getMinutesFromMidnight,\n    continuesPrior: continuesPrior,\n    continuesAfter: continuesAfter,\n    sortEvents: sortEvents,\n    inEventRange: inEventRange,\n    isSameDate: isSameDate,\n    browserTZOffset: browserTZOffset\n  });\n}\n\nvar components = {\n  eventWrapper: NoopWrapper,\n  timeSlotWrapper: NoopWrapper,\n  dateCellWrapper: NoopWrapper\n};\n\nexport { Calendar$1 as Calendar, DateLocalizer, navigate as Navigate, views as Views, components, dateFnsLocalizer, dayjs as dayjsLocalizer, globalize as globalizeLocalizer, luxon as luxonLocalizer, moment as momentLocalizer, moveDate as move };\n"], "names": [], "sourceRoot": "", "ignoreList": [0]}