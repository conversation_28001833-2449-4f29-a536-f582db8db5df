"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[4140],{74140:(e,s,a)=>{a.r(s),a.d(s,{default:()=>k});var t=a(37876),r=a(14232),l=a(60282),c=a(49589),n=a(56970),i=a(37784),d=a(33939),o=a(31195),p=a(82851),u=a.n(p),m=a(48230),v=a.n(m),h=a(21772),b=a(11041),A=a(2827),j=a(73278),x=a(79314),y=a(97685),f=a(89099),g=a.n(f),w=a(50749),S=a(53718),N=a(31753);let M=e=>{let s,a,p=(0,f.useRouter)(),{t:m}=(0,N.Bd)("common"),M=(0,j.Ay)(),[k,_]=(0,r.useState)(null),[C,q]=(0,r.useState)([]),[V,U]=(0,r.useState)([]),[E,H]=(0,r.useState)(""),[P,R]=(0,r.useState)([]),[T,D]=(0,r.useState)([]),[I,O]=(0,r.useState)(!1),[B,F]=(0,r.useState)(null),[z,K]=(0,r.useState)(null),[L,Y]=(0,r.useState)(!1),G=p&&p.query&&p.query.id,J={query:{},sort:{username:"asc"},limit:"~"},Q=async()=>{let e=await S.A.get("/vspace/".concat(G));e&&_(e)},W=async()=>{var e;let s=await S.A.get("/users",J);(null==s||null==(e=s.data)?void 0:e.length)&&(s.data=s.data.filter(e=>"Request Pending"!==e.vspace_status&&"Request Pending"!==e.status));let a=await S.A.get("/vspace/".concat(G));if(s&&a&&a.members){let e=a.members&&a.members.map(e=>e._id);q(s.data.map((e,s)=>({label:e.username,value:e._id})).filter(s=>-1===e.indexOf(s.value)))}},X={query:{vspace:G},select:"-vspace -requested_to -created_at -updated_at",limit:"~"},Z=async()=>{let e=await S.A.get("/vspace-request-subscribers/getRequestedToMe",X);e&&e.data&&e.data.length>0?D(e.data.map(e=>(e.requested_by._id=e._id,e.requested_by))):D(e&&e.data)};(0,r.useEffect)(()=>{Q(),W(),Z()},[]),(0,r.useEffect)(()=>{Q(),W(),Z()},[L]);let $=async e=>{if(E)switch(e.key){case"Enter":case"Tab":/\S+@\S+\.\S+/.test(E)?(""===(await S.A.post("/vspace/filterNonmember",{email:E})).message?R([...P,{label:E,value:E}]):y.Ay.error("".concat(E,"  is already exist")),H(""),e.preventDefault()):(e.preventDefault(),y.Ay.error(m("vspace.Pleaseentervalidemailaddress")),H(""))}},ee=async()=>{let e={_id:B._id,status:z},s=await S.A.post("/vspace/requestStatus",e);s&&200===s.status&&Y(!L)},es=(e,s)=>{O(!0),K(s),F(e)},ea=async()=>{await S.A.remove("/vspace/".concat(G)),y.Ay.success(m("vspace.DeletedtheVirtualSpacesuccessfully")),g().push("/vspace")},et=[{name:m("vspace.UserName"),selector:"username",sortable:!0},{name:m("vspace.Email"),selector:"email",sortable:!0},{name:m("vspace.Action"),cell:e=>e?(0,t.jsx)("a",{onClick:()=>es(e._id,"platformMember"),style:{cursor:"pointer"},children:(0,t.jsx)("i",{className:"icon fas fa-trash-alt"})}):""}],er=[{name:m("vspace.UserName"),selector:"username",sortable:!0},{name:m("vspace.Email"),selector:"email",sortable:!0},{name:m("vspace.Action"),cell:e=>(0,t.jsxs)("div",{children:[(0,t.jsx)(l.A,{variant:"primary",size:"sm",onClick:()=>es(e,"approved"),children:m("vspace.Approve")}),"\xa0",(0,t.jsx)(l.A,{variant:"secondary",size:"sm",onClick:()=>es(e,"declined"),children:m("vspace.Reject")})]})}];switch(z){case"platformMember":s=m("vspace.Deleteuserfromvirtualspace"),a=m("vspace.AreyousurewanttoremovetheuserfromtheVirtualSpace?");break;case"approved":s=m("vspace.ApproveUser"),a=m("vspace.AreyousurewanttoaddtheusertoVirtualSpace?");break;case"declined":s=m("vspace.RejectUser"),a=m("vspace.AreyousurewanttorejecttheuserfromVirtualSpace?");break;case"deleteVspace":s=m("vspace.DeleteVirtualSpace"),a=m("vspace.AreyousurewanttodeletetheVirtualSpace?")}let el=(e,s,a)=>u().orderBy(e,e=>e[s]?e[s].toLowerCase():e[s],a),ec=async()=>{let e=k&&k.members.filter(e=>e._id!==B).map(e=>e._id),s=k&&k.subscribers.filter(e=>e._id!==B);await S.A.patch("/vspace/".concat(G),{...k,members:e,nonMembers:""===k.nonMembers[0]?"":k.nonMembers,subscribers:s.map(e=>e._id)}),Y(!L)},en=()=>{O(!1),F(null),K(null)},ei=async e=>{let s=P&&P.map(e=>e.value),a=V&&V.map(e=>e.value),t=k&&k.members.map(e=>e._id);"platformMembers"===e&&V.length>0?(await S.A.patch("/vspace/".concat(G),{...k,members:[...t,...a],nonMembers:""===k.nonMembers[0]?"":k.nonMembers}),y.Ay.success(m("vspace.UserInvitedsuccessfully")),U([]),Y(!L)):"nonPlatformMembers"===e&&P.length>0?(await S.A.patch("/vspace/".concat(G),{...k,nonMembers:s,members:t}),y.Ay.success(m("vspace.UserInvitedsuccessfully")),R([]),Y(!L)):y.Ay.error(m("vspace.Pleasechoosemembersoraddemailtoinvite"))};return(0,t.jsx)("div",{className:"pe-2",children:(0,t.jsxs)(c.A,{children:[k?(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)(n.A,{className:"mt-3",children:(0,t.jsx)(i.A,{className:"p-0",children:(0,t.jsxs)("div",{className:"d-flex justify-content-between",children:[(0,t.jsx)("div",{children:(0,t.jsxs)("h4",{children:[(0,t.jsx)(v(),{href:"/vspace/[...routes]",as:"/vspace/show/".concat(G),className:"h5 p-0 m-0",children:k.title}),(0,t.jsx)(v(),{href:"/vspace/[...routes]",as:"/vspace/edit/".concat(G),children:(0,t.jsxs)(l.A,{variant:"secondary",size:"sm",className:"ms-2",children:[(0,t.jsx)(h.g,{icon:b.hpd}),"\xa0",m("vspace.Edit")]})})]})}),(0,t.jsxs)(l.A,{variant:"dark",onClick:()=>{O(!0),K("deleteVspace"),F(G)},children:[" ",(0,t.jsx)(h.g,{icon:b.BeE,color:"#fff",className:"me-2"}),m("vspace.DeleteVirtualSpace")]})]})})}),(0,t.jsx)(n.A,{children:(0,t.jsxs)(i.A,{className:"header-block",lg:12,children:[(0,t.jsx)("h6",{children:(0,t.jsx)("span",{children:m("vspace.MonitoringandEvaluationMembers")})}),(0,t.jsx)(w.A,{noHeader:!0,columns:et,data:k.members,dense:!0,paginationServer:!0,pagServer:!0,paginationTotalRows:0,subHeader:!0,subHeaderAlign:"left",pagination:!0,persistTableHead:!0,sortFunction:el})]})}),(0,t.jsx)(n.A,{children:(0,t.jsxs)(i.A,{className:"header-block",lg:12,children:[(0,t.jsx)("h6",{children:(0,t.jsx)("span",{children:m("vspace.SubscribeRequestUsers")})}),T&&T.length>0?(0,t.jsx)(w.A,{noHeader:!0,columns:er,data:T,dense:!0,paginationServer:!0,pagServer:!0,paginationTotalRows:0,subHeader:!0,subHeaderAlign:"left",pagination:!0,persistTableHead:!0,sortFunction:el}):(0,t.jsx)("div",{className:"nodataFound",children:m("vspace.Nodataavailable")})]})}),(0,t.jsx)(n.A,{children:(0,t.jsxs)(i.A,{className:"header-block",children:[(0,t.jsx)("h6",{children:(0,t.jsx)("span",{children:m("vspace.PlatformMembersInvites")})}),(0,t.jsxs)("div",{children:[(0,t.jsx)(A.Ay,{closeMenuOnSelect:!1,components:M,isMulti:!0,value:V||[],placeholder:m("vspace.SelectUsers"),onChange:s=>{let a=e.allOption||{label:"All users",value:"*"};s&&s.length>0&&s[s.length-1].value===a.value?U(C):U(s)},options:[e.allOption||{label:"All users",value:"*"},...C||[]]}),(0,t.jsx)(l.A,{className:"mt-3",variant:"primary",onClick:()=>ei("platformMembers"),children:m("vspace.AddMembers")})]})]})}),(0,t.jsx)(n.A,{children:(0,t.jsxs)(i.A,{className:"header-block",children:[(0,t.jsx)("h6",{children:(0,t.jsx)("span",{children:m("vspace.Non-PlatformMemberInvites(by email)")})}),(0,t.jsx)("small",{children:m("vspace.PressTabtoseparatemultipleemailid(s)")}),(0,t.jsxs)("div",{children:[(0,t.jsx)(x.A,{components:M,inputValue:E,isClearable:!0,isMulti:!0,menuIsOpen:!1,onChange:()=>R(P||[]),onInputChange:e=>H(e),onKeyDown:$,placeholder:m("vspace.Typeemailid(s),pressingenterbetweeneachone"),value:P||[]}),(0,t.jsx)(l.A,{className:"mt-3",variant:"primary",onClick:()=>ei("nonPlatformMembers"),children:m("vspace.AddMembers")})]})]})})]}):(0,t.jsx)("div",{className:"d-flex justify-content-center align-items-center ",children:(0,t.jsx)(d.A,{animation:"border",variant:"primary"})}),(0,t.jsxs)(o.A,{show:I,onHide:()=>O(!1),children:[(0,t.jsx)(o.A.Header,{closeButton:!0,children:(0,t.jsx)(o.A.Title,{children:s})}),(0,t.jsx)(o.A.Body,{children:a}),(0,t.jsxs)("div",{className:"d-flex justify-content-end m-2",children:[(0,t.jsx)(l.A,{variant:"secondary",onClick:()=>{switch(z){case"platformMember":case"declined":case"approved":case"deleteVspace":en()}},className:"me-2",children:m("vspace.No")}),(0,t.jsx)(l.A,{variant:"primary",onClick:()=>{switch(z){case"platformMember":y.Ay.success(m("vspace.Userremovedsuccessfully")),en(),ec();break;case"approved":y.Ay.success(m("vspace.Userapprovedsuccessfully")),en(),ee();break;case"declined":y.Ay.success(m("vspace.Userrejectedsuccessfully")),en(),ee();break;case"deleteVspace":O(!1),K(null),ea()}},children:m("vspace.Yes")})]})]})]})})};M.defaultProps={allOption:{label:"All users",value:"*"}};let k=M}}]);
//# sourceMappingURL=4140-a29a92f2ddb12370.js.map