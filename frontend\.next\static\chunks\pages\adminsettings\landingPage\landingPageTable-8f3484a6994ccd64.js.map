{"version": 3, "file": "static/chunks/pages/adminsettings/landingPage/landingPageTable-8f3484a6994ccd64.js", "mappings": "0KAqCA,SAASA,EAASC,CAAoB,EACpC,GAAM,GAAEC,CAAC,CAAE,CAAGC,CAAAA,EAAAA,EAAAA,EAAAA,CAAcA,CAAC,UACvBC,EAA6B,CACjCC,gBAAiBH,EAAE,cACnB,EACI,CACJI,SAAO,MACPC,CAAI,WACJC,CAAS,uBACTC,CAAqB,WACrBC,CAAS,oBACTC,CAAkB,CAClBC,qBAAmB,kBACnBC,CAAgB,aAChBC,CAAW,oBACXC,CAAkB,gBAClBC,CAAc,SACdC,CAAO,WACPC,CAAS,CACTC,sBAAoB,mBACpBC,CAAiB,YACjBC,CAAU,QACVC,CAAM,kBACNC,CAAgB,cAChBC,CAAY,CAEZ,CADA,EACGC,EACJ,CAAGxB,EAGEyB,EAAiB,4BACrBtB,EACAuB,gBAAiBzB,EAAE,IAP0C,MAQ7D0B,UAAU,UACVtB,EACAC,KAAMA,GAAQ,EAAE,CAChBsB,OAAO,EACPC,2BAA4BrB,EAC5BsB,UAAWrB,EACXsB,gBAAiBf,EACjBN,qBACAsB,YAAY,EACZC,iBAAkBhB,EAClBiB,kBAAmBpB,GAA0C,GAC7DqB,eADwCrB,cACVD,GAA4B,CAAC,GAAI,GAAI,GAAI,CAA3BA,EAA+B,GAAG,CAC9EuB,oBAAqB7B,EACrB8B,oBAAqB1B,EACrB2B,aAAc1B,iBACdG,uBACAG,oBACAC,EACAoB,kBAAmB,UAACC,EAAAA,CAAYA,CAAAA,CAAAA,GAChCC,SAAU,UAACC,IAAAA,CAAEC,UAAU,6CACvBvB,SACAC,EACAE,eACAD,mBACAqB,UAAW,WACb,EACA,MACE,UAACC,EAAAA,EAASA,CAAAA,CAAE,GAAGnB,CAAc,EAEjC,CAEA1B,EAAS8C,YAAY,CAAG,CACtBf,WAAW,EACXE,YAAY,EACZzB,UAAW,KACXU,WAAW,EACXC,qBAAsB,KACtBC,mBAAmB,EACnBC,YAAY,EACZE,kBAAkB,CACpB,EAEA,MAAevB,QAAQA,EAAC,SC/GxB,4CACA,8CACA,WACA,OAAe,EAAQ,KAAmE,CAC1F,EACA,SAFsB,qKCkItB,MA1HyB,IACrB,GAAM,GAAEE,CAAC,MAAE6C,CAAI,CAAE,CAAG5C,CAAAA,EAAAA,EAAAA,EAAAA,CAAcA,CAAC,UACf4C,EAAKC,GAwHEC,EAAC,GAxHK,EAAGF,EAAKC,QAAQ,CAEjD,EAFoD,CAE9C,CAACE,EAAWC,EAAe,CAAGC,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAC,EAAE,EACzC,EAAGC,EAAW,CAAGD,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,EAAC,GAC1B,CAAC5C,EAAW8C,EAAa,CAAGF,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAC,GACrC,CAACG,EAASC,EAAW,CAAGJ,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAC,IACjC,CAACK,EAAaC,EAAS,CAAGN,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,EAAC,GACnC,CAACO,EAAmBC,EAAqB,CAAGR,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAC,CAAC,GACtDS,EAAY,IAAMH,GAAS,GAE3BpD,EAAU,CACZ,CACIwD,KAAM5D,EAAE,oCACR6D,SAAU,QACVC,KAAM,GAAYC,EAAEC,KAAK,EAE7B,CACIJ,KAAM5D,EAAE,sCACR6D,SAAU,YACVC,KAAM,GAAaC,EAAEE,SAAS,CAAG,MAAQ,IAC7C,EACA,CACIL,KAAM5D,EAAE,qCACR6D,SAAU,GACVC,KAAM,GACF,WAACI,MAAAA,WACG,UAACC,IAAIA,CAACC,KAAK,6BAA6BC,GAAI,OAAvCF,wBAA4E,OAANJ,EAAEO,GAAG,WAE5E,UAAC7B,IAAAA,CAAEC,UAAU,uBAEV,OAEN,MAGb,EACH,CACK6B,EAAoB,CACtBC,KAAM,CAAER,MAAO,KAAM,EACrBS,MAAOpB,EACPqB,KAAM,CACV,EAEAC,CAAAA,EAAAA,EAAAA,SAAAA,CAASA,CAAC,KACNC,GACJ,EAAG,EAAE,EAEL,IAAMA,EAAqB,UACvBzB,GAAW,GACX,IAAM0B,EAAW,MAAMC,EAAAA,CAAUA,CAACC,GAAG,CAAC,eAAgBR,GAClDM,IACA5B,EAAe4B,EAASxE,EADd,EACkB,EAC5B+C,EAAayB,EAASG,UAAU,EAChC7B,EAAW,IAEnB,EAQMzC,EAAsB,MAAOuE,EAAiBP,KAChDH,EAAkBE,KAAK,CAAGQ,EAC1BV,EAAkBG,IAAI,CAAGA,EACzBvB,GAAW,GACX,IAAM0B,EAAW,MAAMC,EAAAA,CAAUA,CAACC,GAAG,CAAC,eAAgBR,GAClDM,GAAYA,EAASxE,IAAI,EAAIwE,EAASxE,IAAI,CAAC6E,MAAM,CAAG,GAAG,CACvDjC,EAAe4B,EAASxE,IAAI,EAC5BiD,EAAW2B,GACX9B,GAAW,GAEnB,EAEMgC,EAAe,UACjB,GAAI,CACA,MAAML,EAAAA,CAAUA,CAACM,MAAM,CAAC,gBAAkC,OAAlB3B,IACxCmB,IACApB,GAAS,GACT6B,EAAAA,EAAKA,CAACC,OAAO,CAACtF,EAAE,yDACpB,CAAE,MAAOuF,EAAO,CACZF,EAAAA,EAAKA,CAACE,KAAK,CAACvF,EAAE,mDAClB,CACJ,EAOA,MACI,WAACkE,MAAAA,WACG,WAACsB,EAAAA,CAAKA,CAAAA,CAACC,KAAMlC,EAAamC,OAAQ/B,YAC9B,UAAC6B,EAAAA,CAAKA,CAACG,MAAM,EAACC,WAAW,aACrB,UAACJ,EAAAA,CAAKA,CAACK,KAAK,WAAE7F,EAAE,wDAEpB,UAACwF,EAAAA,CAAKA,CAACM,IAAI,WAAE9F,EAAE,2EACf,WAACwF,EAAAA,CAAKA,CAACO,MAAM,YACT,UAACC,EAAAA,CAAMA,CAAAA,CAACC,QAAQ,YAAYC,QAASvC,WAChC3D,EAAE,uCAEP,UAACgG,EAAAA,CAAMA,CAAAA,CAACC,QAAQ,UAAUC,QAASf,WAC9BnF,EAAE,0CAKf,UAACF,EAAAA,CAAQA,CAAAA,CACLM,QAASA,EACTC,KAAM2C,EACN1C,UAAWA,EACXU,WAAW,EACXN,oBAAqBA,EACrBC,iBAzDc+D,CAyDI/D,GAxD1B4D,EAAkBE,KAAK,CAAGpB,EAC1BkB,EAAkBG,IAAI,CAAGA,EACzBE,GACJ,MAyDJ", "sources": ["webpack://_N_E/./components/common/RKITable.tsx", "webpack://_N_E/?443c", "webpack://_N_E/./pages/adminsettings/landingPage/landingPageTable.tsx"], "sourcesContent": ["//Import Library\r\nimport React from 'react'\r\nimport DataTable  from 'react-data-table-component';\r\n\r\n//Import services/components\r\nimport { useTranslation } from 'next-i18next';\r\nimport CustomLoader from './CustomLoader';\r\n\r\ninterface RKITableProps {\r\n  columns: any[];\r\n  data: any[];\r\n  totalRows?: number;\r\n  resetPaginationToggle?: boolean;\r\n  subheader?: boolean;\r\n  subHeaderComponent?: React.ReactNode;\r\n  handlePerRowsChange?: (newPerPage: number, page: number) => void;\r\n  handlePageChange?: (page: number) => void;\r\n  rowsPerPage?: number[];\r\n  defaultRowsPerPage?: number;\r\n  selectableRows?: boolean;\r\n  loading?: boolean;\r\n  pagServer?: boolean;\r\n  onSelectedRowsChange?: (selectedRows: any) => void;\r\n  clearSelectedRows?: boolean;\r\n  sortServer?: boolean;\r\n  onSort?: (column: any, sortDirection: string) => void;\r\n  persistTableHead?: boolean;\r\n  sortFunction?: (rows: any[], field: any, direction: 'asc' | 'desc') => any[];\r\n  noHeader?: boolean;\r\n  dense?: boolean;\r\n  pagination?: boolean;\r\n  paginationServer?: boolean;\r\n  paginationTotalRows?: number;\r\n  subHeaderAlign?: string;\r\n  [key: string]: any;\r\n}\r\n\r\nfunction RKITable(props: RKITableProps) {\r\n  const { t } = useTranslation('common');\r\n  const paginationComponentOptions = {\r\n    rowsPerPageText: t('Rowsperpage'),\r\n    };\r\n  const {\r\n    columns,\r\n    data,\r\n    totalRows,\r\n    resetPaginationToggle,\r\n    subheader,\r\n    subHeaderComponent,\r\n    handlePerRowsChange,\r\n    handlePageChange,\r\n    rowsPerPage,\r\n    defaultRowsPerPage,\r\n    selectableRows,\r\n    loading,\r\n    pagServer,\r\n    onSelectedRowsChange,\r\n    clearSelectedRows,\r\n    sortServer,\r\n    onSort,\r\n    persistTableHead,\r\n    sortFunction,\r\n    // Filter out any unknown props that might cause DOM warnings\r\n    ...otherProps\r\n  } = props;\r\n\r\n  // Only pass known DataTable props to avoid DOM warnings\r\n  const dataTableProps = {\r\n    paginationComponentOptions,\r\n    noDataComponent: t(\"NoData\"),\r\n    noHeader: true,\r\n    columns,\r\n    data: data || [],\r\n    dense: true,\r\n    paginationResetDefaultPage: resetPaginationToggle,\r\n    subHeader: subheader,\r\n    progressPending: loading,\r\n    subHeaderComponent,\r\n    pagination: true,\r\n    paginationServer: pagServer,\r\n    paginationPerPage: defaultRowsPerPage ? defaultRowsPerPage : 10,\r\n    paginationRowsPerPageOptions: rowsPerPage ? rowsPerPage : [10, 15, 20, 25, 30],\r\n    paginationTotalRows: totalRows,\r\n    onChangeRowsPerPage: handlePerRowsChange,\r\n    onChangePage: handlePageChange,\r\n    selectableRows,\r\n    onSelectedRowsChange,\r\n    clearSelectedRows,\r\n    progressComponent: <CustomLoader/>,\r\n    sortIcon: <i className=\"sort-icon fas fa-exchange-alt\" />,\r\n    sortServer,\r\n    onSort,\r\n    sortFunction,\r\n    persistTableHead,\r\n    className: \"rki-table\"\r\n  };\r\n  return (\r\n    <DataTable {...dataTableProps} />\r\n  )\r\n};\r\n\r\nRKITable.defaultProps = {\r\n  subHeader: false,\r\n  pagination: true,\r\n  totalRows: null,\r\n  pagServer: true,\r\n  onSelectedRowsChange: null,\r\n  clearSelectedRows: false,\r\n  sortServer: false,\r\n  persistTableHead: false\r\n}\r\n\r\nexport default RKITable;\r\n\r\n", "\n    (window.__NEXT_P = window.__NEXT_P || []).push([\n      \"/adminsettings/landingPage/landingPageTable\",\n      function () {\n        return require(\"private-next-pages/adminsettings/landingPage/landingPageTable.tsx\");\n      }\n    ]);\n    if(module.hot) {\n      module.hot.dispose(function () {\n        window.__NEXT_P.push([\"/adminsettings/landingPage/landingPageTable\"])\n      });\n    }\n  ", "//Import Library\r\nimport Link from \"next/link\";\r\nimport { useState, useEffect } from \"react\";\r\nimport { <PERSON><PERSON>, But<PERSON> } from \"react-bootstrap\";\r\n\r\nimport toast from 'react-hot-toast';\r\n\r\n//Import services/components\r\nimport RKITable from \"../../../components/common/RKITable\";\r\nimport apiService from \"../../../services/apiService\";\r\nimport { useTranslation } from 'next-i18next';\r\n\r\nconst LandingPageTable = (_props: any) => {\r\n    const { t, i18n } = useTranslation('common');\r\n    const currentLang = i18n.language ? i18n.language : \"en\";\r\n\r\n    const [tabledata, setDataToTable] = useState([]);\r\n    const [, setLoading] = useState(false);\r\n    const [totalRows, setTotalRows] = useState(0);\r\n    const [perPage, setPerPage] = useState(10);\r\n    const [isModalShow, setModal] = useState(false);\r\n    const [selectLandingPage, setSelectLandingPage] = useState({});\r\n    const modalHide = () => setModal(false);\r\n    \r\n    const columns = [\r\n        {\r\n            name: t(\"adminsetting.landing.table.Title\"),\r\n            selector: \"title\",\r\n            cell: (d: any) => d.title,\r\n        },\r\n        {\r\n            name: t(\"adminsetting.landing.table.Enabled\"),\r\n            selector: \"isEnabled\",\r\n            cell: (d: any) => (d.isEnabled ? \"Yes\" : \"No\"),\r\n        },\r\n        {\r\n            name: t(\"adminsetting.landing.table.Action\"),\r\n            selector: \"\",\r\n            cell: (d: any) => (\r\n                <div>\r\n                    <Link href=\"/adminsettings/[...routes]\" as={`/adminsettings/edit_landing/${d._id}`}>\r\n\r\n                        <i className=\"icon fas fa-edit\" />\r\n\r\n                    </Link>\r\n                    &nbsp;\r\n                    {\" \"}\r\n                </div>\r\n            ),\r\n        },\r\n    ];\r\n    const landingPageParams = {\r\n        sort: { title: \"asc\" },\r\n        limit: perPage,\r\n        page: 1,\r\n    };\r\n\r\n    useEffect(() => {\r\n        getlandingPageData();\r\n    }, []);\r\n\r\n    const getlandingPageData = async () => {\r\n        setLoading(true);\r\n        const response = await apiService.get(\"/landingPage\", landingPageParams);\r\n        if (response) {\r\n            setDataToTable(response.data);\r\n            setTotalRows(response.totalCount);\r\n            setLoading(false);\r\n        }\r\n    };\r\n\r\n    const handlePageChange = (page: any) => {\r\n        landingPageParams.limit = perPage;\r\n        landingPageParams.page = page;\r\n        getlandingPageData();\r\n    };\r\n\r\n    const handlePerRowsChange = async (newPerPage: any, page: any) => {\r\n        landingPageParams.limit = newPerPage;\r\n        landingPageParams.page = page;\r\n        setLoading(true);\r\n        const response = await apiService.get(\"/landingPage\", landingPageParams);\r\n        if (response && response.data && response.data.length > 0) {\r\n            setDataToTable(response.data);\r\n            setPerPage(newPerPage);\r\n            setLoading(false);\r\n        }\r\n    };\r\n\r\n    const modalConfirm = async () => {\r\n        try {\r\n            await apiService.remove(`/landingPage/${selectLandingPage}`);\r\n            getlandingPageData();\r\n            setModal(false);\r\n            toast.success(t(\"adminsetting.landing.table.landingDeletedSuccessfully\"));\r\n        } catch (error) {\r\n            toast.error(t(\"adminsetting.landing.table.errorDeletingLanding\"));\r\n        }\r\n    };\r\n\r\n    const userAction = async (row: any) => {\r\n        setSelectLandingPage(row._id);\r\n        setModal(true);\r\n    };\r\n\r\n    return (\r\n        <div>\r\n            <Modal show={isModalShow} onHide={modalHide}>\r\n                <Modal.Header closeButton>\r\n                    <Modal.Title>{t(\"adminsetting.landing.table.DeleteEditableContent\")}</Modal.Title>\r\n                </Modal.Header>\r\n                <Modal.Body>{t(\"adminsetting.landing.table.AreyousurewanttodeletethisEditableContent?\")}</Modal.Body>\r\n                <Modal.Footer>\r\n                    <Button variant=\"secondary\" onClick={modalHide}>\r\n                        {t(\"adminsetting.landing.table.Cancel\")}\r\n                    </Button>\r\n                    <Button variant=\"primary\" onClick={modalConfirm}>\r\n                        {t(\"adminsetting.landing.table.Yes\")}\r\n                    </Button>\r\n                </Modal.Footer>\r\n            </Modal>\r\n\r\n            <RKITable\r\n                columns={columns}\r\n                data={tabledata}\r\n                totalRows={totalRows}\r\n                pagServer={true}\r\n                handlePerRowsChange={handlePerRowsChange}\r\n                handlePageChange={handlePageChange}\r\n            />\r\n        </div>\r\n    );\r\n};\r\n\r\nexport default LandingPageTable;\r\n"], "names": ["RKITable", "props", "t", "useTranslation", "paginationComponentOptions", "rowsPerPageText", "columns", "data", "totalRows", "resetPaginationToggle", "subheader", "subHeaderComponent", "handlePerRowsChange", "handlePageChange", "rowsPerPage", "defaultRowsPerPage", "selectableRows", "loading", "pagServer", "onSelectedRowsChange", "clearSelectedRows", "sortServer", "onSort", "persistTableHead", "sortFunction", "otherProps", "dataTableProps", "noDataComponent", "<PERSON><PERSON><PERSON><PERSON>", "dense", "paginationResetDefaultPage", "subHeader", "progressPending", "pagination", "paginationServer", "paginationPerPage", "paginationRowsPerPageOptions", "paginationTotalRows", "onChangeRowsPerPage", "onChangePage", "progressComponent", "CustomLoader", "sortIcon", "i", "className", "DataTable", "defaultProps", "i18n", "language", "LandingPageTable", "tabledata", "setDataToTable", "useState", "setLoading", "setTotalRows", "perPage", "setPerPage", "isModalShow", "setModal", "selectLandingPage", "setSelectLandingPage", "modalHide", "name", "selector", "cell", "d", "title", "isEnabled", "div", "Link", "href", "as", "_id", "landingPageParams", "sort", "limit", "page", "useEffect", "getlandingPageData", "response", "apiService", "get", "totalCount", "newPerPage", "length", "modalConfirm", "remove", "toast", "success", "error", "Modal", "show", "onHide", "Header", "closeButton", "Title", "Body", "Footer", "<PERSON><PERSON>", "variant", "onClick"], "sourceRoot": "", "ignoreList": []}