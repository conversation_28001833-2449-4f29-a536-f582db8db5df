{"version": 3, "file": "static/chunks/pages/home-6b80f918bc5c0464.js", "mappings": "gFACA,4CACA,QACA,WACA,OAAe,EAAQ,KAAmC,CAC1D,EACA,SAFsB,wMCWP,SAASA,EAAgBC,CAA2B,EASjE,GAAM,CAACC,EAAeC,EAAa,CAAGC,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,EAAC,GAEzC,EAAGC,EAAQ,CAAGD,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAACE,CAR3BC,MAAO,GACPC,YAAa,GACbC,aAAc,GACdC,WAAW,CACb,GAKMC,EAAe,IAAkC,CAAEC,OAAQC,EAAY,EACvE,GAAEC,CAAC,CAAE,CAAGC,CAAAA,EAAAA,EAAAA,EAAAA,CAAcA,CAAC,UAGvBC,EAAiB,UAErB,IAAMC,EAAa,CACjBC,MAAO,CAAET,aAAc,EAAG,EAC1BU,KAAM,CAAEZ,MAAO,KAAM,EACrBa,MAAO,GACT,EAEMC,EAAsB,MAAMC,IAClC,GAAID,EAAeE,MAAM,CAAG,EAAG,CAC7BN,EAAWC,KAAK,CAACT,YAAY,CAAGY,EAChC,IAAMG,EAAW,MAAMC,EAAAA,CAAUA,CAACC,GAAG,CAAC,eAAeT,GACjDU,MAAMC,OAAO,CAACJ,EAASK,IAAI,GAAKL,EAASK,IAAI,CAACN,MAAM,CAAG,GAAG,CAE5DC,EAASK,IAAI,CAAC,EAAE,CAACtB,KAAK,CAAGiB,GAAYA,EAASK,IAAI,CAAC,EAAE,CAACtB,KAAK,CAACgB,MAAM,CAAG,IAAoC,IAA/BC,EAASK,IAAI,CAAC,EAAE,CAACnB,SAAS,CAAYc,EAASK,IAAI,CAAC,EAAE,CAACtB,KAAK,CAAGuB,EAEzIN,EAASK,IAAI,CAAC,EAAE,CAACrB,WAAW,CAAGgB,GAAYA,EAASK,IAAI,CAAC,EAAE,CAACrB,WAAW,CAACe,MAAM,CAAG,GAAKC,CAA+B,MAAtBK,IAAI,CAAC,EAAE,CAACnB,SAAS,CAAYc,EAASK,IAAI,CAAC,EAAE,CAACrB,WAAW,CAAGuB,EAE3J1B,EAAQmB,EAASK,IAAI,CAAC,EAAE,EACxBP,IAGJ,CACF,EAEAU,CAAAA,EAAAA,EAAAA,SAAAA,CAASA,CAAC,KACRhB,GACF,EAAG,EAAE,EAEL,IAAMM,EAAoB,UACxB,IAAME,EAAW,MAAMC,EAAAA,CAAUA,CAACC,GAAG,CAAC,gBAAgB,CAACR,MAAM,CAACX,MAAQ,MAAM,CAAC,SAC7E,EAAIiB,KAAYA,EAASK,IAAI,EAAIL,EAASK,IAAI,CAACN,MAAM,CAAG,GAAG,EACzBM,IAAI,CAAC,EAAE,CAACI,GAAG,EAMzCF,EAAe,q5BAEfD,EAAgB,oCAEtB,MACE,WAACI,MAAAA,WACC,UAACC,IAAAA,CAAEC,QAAS,IAAMjC,GAAa,YAC7B,UAACkC,EAAAA,CAAcA,CAAAA,CACbC,UAAU,SACVC,MAAO,CAAEC,KAAM,IAAKC,KAAM,GAAI,EAC9BC,QAAS,UAACC,EAAAA,CAAOA,CAAAA,CAACC,GAAG,yBAAkB9B,EAAE,mBACzC,UAAC+B,IAAAA,CAAEC,UAAU,+BACjB,WAACC,EAAAA,CAAKA,CAAAA,CAACC,KAAK,KAAKR,KAAMtC,EAAe+C,OAxDtB,CAwD8BC,GAxDxB/C,GAAa,aAyDjC,UAAC4C,EAAAA,CAAKA,CAACI,MAAM,EAACC,WAAW,aACvB,UAACL,EAAAA,CAAKA,CAACM,KAAK,EAACT,GAAG,sBACf9B,EAAE,YAGL,UAACiC,EAAAA,CAAKA,CAACO,IAAI,WACT,UAACC,KAAAA,UACC,UAACC,EAAAA,CAASA,CAAAA,UACR,WAACA,EAAAA,CAASA,CAACC,IAAI,EAACC,SAAS,cACvB,UAACC,KAAAA,CAAGb,UAAU,iCACZ,UAACU,EAAAA,CAASA,CAACL,MAAM,WACf,UAACjB,MAAAA,CACC0B,wBAAyBjD,EAAamB,SAI5C,UAAC0B,EAAAA,CAASA,CAACF,IAAI,EAACR,UAAU,mCACxB,UAACZ,MAAAA,CACC0B,wBAAyBjD,EAAaoB,qBAU1D,CC9DA,MApCiD,OAAC,SAoCnCoB,CApCqCU,CAAQ,CAAE,GAoCzCV,EAAC,IAnCpB,UAACjB,MAAAA,CAAIY,UAAU,wBACb,UAACZ,MAAAA,CAAIY,UAAU,mBAAmBgB,MAAO,CAAC,WACxC,WAACC,EAAAA,CAAGA,CAAAA,WACF,UAACC,EAAAA,CAAGA,CAAAA,CAACC,GAAI,EAAGnB,UAAU,sBACpB,UAACoB,MAAAA,CAAIC,IAAI,wBAAwBC,OAAO,WAE1C,UAACJ,EAAAA,CAAGA,CAAAA,CAACC,GAAI,WACP,WAACV,KAAAA,CAAGT,UAAU,uBACZ,UAACa,KAAAA,UACC,UAACxB,IAAAA,CAAEC,QAAS,IAAMyB,EAAS,YAAI,WAEjC,UAACF,KAAAA,UACC,UAACxB,IAAAA,CAAEC,QAAS,IAAMyB,EAAS,YAAI,kBAEjC,UAACF,KAAAA,UACC,UAACxB,IAAAA,CAAEC,QAAS,IAAMyB,EAAS,YAAI,eAEjC,UAACF,KAAAA,UACC,UAACxB,IAAAA,CAAEC,QAAS,IAAMyB,EAAS,YAAI,iBAEjC,UAACF,KAAAA,UACC,UAAC3D,EAAeA,CAACwC,MAAM,EAAOS,IAAdjD,GAAsB,KAAO,MAE/C,UAAC2D,KAAAA,CAAGb,UAAU,iBACZ,UAACuB,IAAIA,CAACC,KAAK,KAAND,aAAe,yBCrCrBE,EAA2C,CACtD,EAAG,4BACH,EAAG,4BACH,EAAG,4BACH,EAAG,4BACH,EAAG,4BACH,EAAG,4BACH,EAAG,4BACH,EAAG,4BACH,EAAG,2BACL,EAAE,EAEmD,CACnD,EAAG,yBACH,EAAG,yBACH,EAAG,yBACH,EAAG,yBACH,EAAG,wBACL,EAAE,EAEwB,CACxB,CACEhE,MAAO,wBACPiE,QAAS,kZACTF,KAAM,+JACNG,MAAO,0BACT,EACD,CAAC,EAEyB,CACzB,CACElE,MAAO,+EACPiE,QAAS,uUACTF,KAAM,0FACR,EACA,CACE/D,MAAO,+CACPiE,QAAS,oVACTF,KAAM,qBACR,EACA,CACE/D,MAAO,uBACPiE,QAAS,+RACTF,KAAM,gFACR,EACA,CACE/D,MAAO,+BACPiE,QAAS,0ZACTF,KAAM,sGACR,EACA,CACE/D,MAAO,8BACPiE,QAAS,yTACTF,KAAM,uBACR,EACA,CACE/D,MAAO,0BACPiE,QAAS,4VACTF,KAAM,+BACR,EACA,CACE/D,MAAO,uEACPiE,QAAS,4SACTF,KAAM,sEACR,EACA,CACE/D,MAAO,sDACPiE,QAAS,wPACTF,KAAM,gCACR,EACA,CACE/D,MAAO,iEACPiE,QAAS,4PACX,EACD,CAAC,EAEsB,CACtB,CACEjE,MAAO,cACPmE,IAAK,eACLF,QAAS,mJACX,EACA,CACEjE,MAAO,WACPmE,IAAK,YACLF,QAAS,yIACX,EACA,CACEjE,MAAO,WACPmE,IAAK,YACLF,QAAS,gJACX,EACA,CACEjE,MAAO,YACPmE,IAAK,aACLF,QAAS,6IACX,EACA,CACEjE,MAAO,gBACPmE,IAAK,iBACLF,QAAS,iJACX,EACD,CAAC,EC9F0C,IAC1C,UAACtC,EAoBYyC,IApBZzC,CAAIY,CAoBc6B,EAAC,OApBL,kBACb,UAACZ,EAAAA,CAAGA,CAAAA,UACF,UAACC,EAAAA,CAAGA,CAAAA,CAACY,GAAI,YACNC,EAAWC,GAAG,CAAC,CAACC,EAAMC,CAAZH,GACT,WAAC3C,MAAAA,CAAIY,UAAU,qBACb,UAACoB,MAAAA,CAAIC,IAAI,yBAAyBc,MAAM,SACxC,WAAC/C,MAAAA,CAAIY,UAAU,0BACb,UAACZ,MAAAA,CAAIY,UAAU,uBAAeiC,EAAKxE,KAAK,GACxC,WAAC2B,MAAAA,CAAIY,UAAU,uBAAciC,EAAKP,OAAO,CACzC,UAACH,IAAIA,CAACC,KAAMS,EAAKT,GAAZD,CAAgB,CAAEa,OAAO,kBAAS,6BALZF,UCKzC,EAf6C,IAC3C,UAAC9C,GAcYiD,GAdZjD,CAAIY,GAceqC,EAAC,KAdN,mBACb,WAACjD,MAAAA,CAAIY,UAAU,0BACb,UAACZ,MAAAA,CAAIY,UAAU,iBAAQ,8EACvB,UAACsC,IAAAA,UAAE,20BCoBT,EAnB8C,IAC5C,WAACpB,EAAAA,CAAGA,CAAAA,CAACC,GAAI,GAkBYoB,EAAC,OAjBpB,UAACnD,MAAAA,CAAIY,UAAU,+BAAsB,iBACpCwC,EAAYR,GAAG,CAAC,CAACC,EAAMC,EAAZM,EACV,WAACpD,MAAAA,CAAIY,UAAU,yBACb,UAACoB,MAAAA,CAAIC,IAAKI,CAAa,CAACS,EAAI,GAC5B,WAAC9C,MAAAA,CAAIY,UAAU,wBACb,UAACZ,MAAAA,CAAIY,UAAU,qBAAaiC,EAAKxE,KAAK,GACtC,WAAC2B,MAAAA,CAAIY,UAAU,qBAAYiC,EAAKP,OAAO,CACtCO,EAAKT,IAAI,CAAG,UAACD,IAAIA,CAACC,KAAMS,EAAKT,GAAZD,CAAgB,CAAEa,OAAO,kBAAS,6BAE5C,aAPuBF,OCUzC,EAf8C,IAC5C,WAAChB,EAAAA,CAAGA,CAAAA,CAACC,GAAI,GAcYsB,EAAC,OAbpB,UAACrD,MAAAA,CAAIY,UAAU,kCAAyB,eACvC0C,EAASV,GAAG,CAAC,CAACC,CAANS,CAAYR,IACnB,WAAC9C,MAAAA,CAAIY,UAAU,sBACb,UAACoB,MAAAA,CAAIC,IAAKsB,CAAU,CAACT,EAAI,GACzB,WAAC9C,MAAAA,CAAIY,UAAU,wBACb,WAACZ,MAAAA,CAAIY,UAAU,sBAAaiC,EAAKxE,KAAK,CAAC,IAAC,UAACmF,OAAAA,UAAMX,EAAKL,GAAG,MACvD,UAACxC,MAAAA,CAAIY,UAAU,oBAAYiC,EAAKP,OAAO,QAJXQ,OCctC,EArB4D,OAAC,UAAEW,CAAQ,CAAE,KAqBnDC,EAAC,EApBrB,UAAC1D,MAAAA,CAAIY,UAAU,WAAW+C,IAAKF,WAC7B,UAACzD,MAAAA,CAAIY,UAAU,wBACb,WAACiB,EAAAA,CAAGA,CAAAA,CAACa,GAAI,aACP,UAACZ,EAAAA,CAAGA,CAAAA,CAACC,GAAI,WACT,UAACC,MAAAA,CAAIC,IAAI,4BAA4B2B,IAAI,QAAQb,MAAM,WAEvD,UAACjB,EAAAA,CAAGA,CAAAA,CAACC,GAAI,WACP,WAAC/B,MAAAA,CAAIY,UAAU,oBACb,UAACZ,MAAAA,CAAIY,UAAU,iBAAQ,aACvB,UAACZ,MAAAA,CAAIY,UAAU,gBAAO,oYC0DlC,EApE2D,OAAC,SAoE7CiD,CAnEbJ,CAAQ,CACT,GAkEoBI,EAAC,IAjEpB,UAAC7D,MAAAA,CAAIY,UAAU,iBAAiB+C,IAAKF,WACnC,UAACzD,MAAAA,CAAIY,UAAU,wBACb,UAACkB,EAAAA,CAAGA,CAAAA,CAACY,GAAI,YACP,WAACb,EAAAA,CAAGA,CAAAA,WACF,UAACC,EAAAA,CAAGA,CAAAA,CACFC,GAAI,EACJH,MAAO,CACLkC,UAAW,MACb,WAEA,UAAC9B,MAAAA,CAAIC,IAAI,wBAAwBrB,UAAU,gBAE7C,UAACkB,EAAAA,CAAGA,CAAAA,CACFC,GAAI,EACJH,MAAO,CACLkC,UAAW,MACb,WAEA,UAAC9D,MAAAA,CAAIY,UAAU,sBACb,WAACZ,MAAAA,CAAI4B,MAAO,CAAEmC,WAAY,MAAO,YAC/B,UAACb,IAAAA,UAAE,2GAIH,WAACA,IAAAA,WACC,UAACc,KAAAA,CAAAA,GACD,UAACR,OAAAA,CAAK5B,MAAO,CAAEqC,WAAY,SAAU,WAAG,SAAQ,IAAE,IAAIC,OAAOC,WAAW,GAAG,4BAG7E,UAACjB,IAAAA,UAAE,0DAIT,UAACpB,EAAAA,CAAGA,CAAAA,CAACC,GAAI,WACP,WAAC/B,MAAAA,CAAIY,UAAU,cAAcgB,MAAO,CAAEkC,UAAW,KAAM,YACrD,UAACZ,IAAAA,CAAEtB,MAAO,CAAEwC,SAAU,MAAO,WAAG,yBAChC,WAAClB,IAAAA,WAAE,+CAED,UAACc,KAAAA,CAAAA,GAAK,8BAGR,UAACd,IAAAA,UAAE,gBACH,UAACA,IAAAA,UAAE,kBACH,UAACA,IAAAA,UAAE,YACH,UAACc,KAAAA,CAAAA,GACD,UAACA,KAAAA,CAAAA,QAGL,UAAClC,EAAAA,CAAGA,CAAAA,CACFC,GAAI,EACJH,MAAO,CACLkC,UAAW,MACb,WAEA,WAAC9D,MAAAA,CAAIY,UAAU,uBACb,UAACsC,IAAAA,UAAE,2BACH,UAACA,IAAAA,UAAE,sCCLjB,EA7C4C,KAC1C,IAAMmB,EAAUC,CAAAA,EAAAA,CA4CGC,CA5CHD,CA4CI,KA5CJA,CAAMA,CAAwB,MACxCE,EAAaF,CAAAA,EAAAA,EAAAA,MAAAA,CAAMA,CAAwB,MAC3CG,EAAaH,CAAAA,EAAAA,EAAAA,MAAAA,CAAMA,CAAwB,MAC3CI,EAAYJ,CAAAA,EAAAA,EAAAA,MAAAA,CAAMA,CAAwB,MAqBhD,MACE,WAACtE,MAAAA,CAAIY,UAAU,oBACb,UAACK,EAAMA,CAACU,SAjBc,CAiBJgD,CAAX1D,EAhBT,IAAM2D,EAAO,CACXP,EACAG,EACAC,EACAC,EACD,CAED,GAAIE,CAAI,CAACC,EAAM,EAAID,CAAI,CAACC,EAAM,CAACC,OAAO,CAAE,CAEtC,IAAMC,EAAgBC,OAAOC,WAAW,CAAGL,CAAI,CAACC,EAAM,CAACC,OAAO,CAACI,qBAAqB,GAAGC,GAAG,CADrE,EACwEC,EAC7FJ,OAAOK,QAAQ,CAAC,CAAEC,SAAU,SAAUH,IAAKJ,CAAa,EAC1D,CACF,IAKI,UAAC/E,MAAAA,CAAIY,UAAU,oBACf,UAACZ,MAAAA,CAAIY,UAAU,OAAO+C,IAAKU,WACzB,UAAC5B,EAAMA,CAAAA,KAET,MAFSA,EAET,EAACQ,EAAOA,CAAAA,GACR,SADQA,CACPjD,MAAAA,CAAI2D,IAAKa,WACR,WAAC3C,EAAAA,CAAGA,CAAAA,CAACjB,UAAU,0BACb,UAACuC,EAAQA,CAAAA,GACT,UADSA,EACAE,CAAAA,QAGb,UAACK,EAAOA,CAACD,SAAUgB,GAAXf,CACR,UAACG,EAAMA,CAACJ,SAAUiB,EAAXb,IAGb,WCpCA,MAZiB,IACR,EAWM0B,CAXN,OAACC,EAAOA,CAAAA,IAAAA", "sources": ["webpack://_N_E/?675e", "webpack://_N_E/./components/layout/modals/layout-help.tsx", "webpack://_N_E/./components/layout/landing/Header.tsx", "webpack://_N_E/./data/landing.ts", "webpack://_N_E/./components/layout/landing/Slider.tsx", "webpack://_N_E/./components/layout/landing/Welcome.tsx", "webpack://_N_E/./components/layout/landing/Networks.tsx", "webpack://_N_E/./components/layout/landing/NewsFeed.tsx", "webpack://_N_E/./components/layout/landing/AboutUs.tsx", "webpack://_N_E/./components/layout/landing/Footer.tsx", "webpack://_N_E/./components/layout/landing/index.tsx", "webpack://_N_E/./pages/home/<USER>"], "sourcesContent": ["\n    (window.__NEXT_P = window.__NEXT_P || []).push([\n      \"/home\",\n      function () {\n        return require(\"private-next-pages/home/<USER>\");\n      }\n    ]);\n    if(module.hot) {\n      module.hot.dispose(function () {\n        window.__NEXT_P.push([\"/home\"])\n      });\n    }\n  ", "//Import Library\r\nimport { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>ton, Tooltip, OverlayTrigger } from \"react-bootstrap\";\r\nimport { useState, useEffect } from \"react\";\r\n\r\n//Import services/components\r\nimport apiService from \"../../../services/apiService\";\r\nimport { useTranslation } from 'next-i18next';\r\n\r\n\r\ninterface LayoutHelpModalProps {\r\n  show: boolean;\r\n  onHide: () => void;\r\n  [key: string]: any;\r\n}\r\n\r\nexport default function LayoutHelpModal(props: LayoutHelpModalProps) {\r\n\r\n  const _initialVal = {\r\n    title: '',\r\n    description: '',\r\n    pageCategory: '',\r\n    isEnabled: true,\r\n  }\r\n\r\n  const [showHelpModal, setHelpModal] = useState(false);\r\n  const handleClose = () => setHelpModal(false);\r\n  const [, setHelp] = useState(_initialVal);\r\n  const createMarkup = (htmlContent: string) => { return { __html: htmlContent } };\r\n  const { t } = useTranslation('common');\r\n\r\n\r\n  const fetchModalData = async () => {\r\n\r\n    const helpParams = {\r\n      query: { pageCategory: '' },\r\n      sort: { title: \"asc\" },\r\n      limit: \"~\",\r\n    };\r\n\r\n    const pageCategoryId: any = await fetchPageCategory();\r\n    if (pageCategoryId.length > 0) {\r\n      helpParams.query.pageCategory = pageCategoryId;\r\n      const response = await apiService.get('/landingPage',helpParams);\r\n      if (Array.isArray(response.data) && response.data.length > 0) {\r\n\r\n        response.data[0].title = response && response.data[0].title.length > 0 && response.data[0].isEnabled === true ? response.data[0].title : defaultTitle;\r\n\r\n        response.data[0].description = response && response.data[0].description.length > 0 && response.data[0].isEnabled === true ? response.data[0].description : defaultDesc;\r\n\r\n        setHelp(response.data[0])\r\n        fetchPageCategory();\r\n\r\n      }\r\n    }\r\n  };\r\n\r\n  useEffect(() => {\r\n    fetchModalData();\r\n  }, []);\r\n\r\n  const fetchPageCategory = async () => {\r\n    const response = await apiService.get('/pagecategory',{query:{title : \"Help\"}});\r\n    if (response && response.data && response.data.length > 0) {\r\n      const pageCategoryId = response.data[0]._id;\r\n      return pageCategoryId\r\n    }\r\n    return false;\r\n  };\r\n\r\n  const defaultDesc = `The RKI Platform is accessible by invitation only to cooperation partner organisations in the field of Health Protection and their staff, through their organisations focal points. If your organisation is already part of the platform and you would like to join please check with your lead focal point as they will be able to add you. <br /><br />  If your organisation is not registered, but you would like to add your organization to our platform, you can email the team at  <EMAIL>.<br/><br/>  We ask that all users of the platform allow their name, title and email to be shared to facilitate communications across the network. We have data protection rules in place to protect against the misuse of data. For further information please click <a href='https://www.rki.de/DE/Service/Datenschutz/datenschutzerklaerung_node.html' target='_blank'>here</a><br /><br />Thank you for your interest.<br/>Kind regards,`;\r\n\r\n  const defaultTitle = `How do I access the RKI Platform?`;\r\n\r\n  return (\r\n    <div>\r\n      <a onClick={() => setHelpModal(true)} >\r\n        <OverlayTrigger\r\n          placement=\"bottom\"\r\n          delay={{ show: 250, hide: 400 }}\r\n          overlay={<Tooltip id=\"print-tooltip\" >{t(\"Help\")}</Tooltip>} >\r\n          <i className=\"fas fa-question-circle\" /></OverlayTrigger></a>\r\n      <Modal size=\"lg\" show={showHelpModal} onHide={handleClose}>\r\n        <Modal.Header closeButton>\r\n          <Modal.Title id=\"help-modal\">\r\n          {t(\"Help\")}\r\n        </Modal.Title>\r\n        </Modal.Header>\r\n        <Modal.Body>\r\n          <ul>\r\n            <Accordion >\r\n              <Accordion.Item eventKey=\"0\">\r\n                <li className=\"help-content-li-title\">\r\n                  <Accordion.Header>\r\n                    <div\r\n                      dangerouslySetInnerHTML={createMarkup(defaultTitle)}>\r\n                    </div>\r\n                  </Accordion.Header>\r\n                </li>\r\n                <Accordion.Body className=\"help-content-li-content\">\r\n                  <div\r\n                    dangerouslySetInnerHTML={createMarkup(defaultDesc)}>\r\n                  </div>\r\n                </Accordion.Body>\r\n              </Accordion.Item>\r\n            </Accordion>\r\n          </ul>\r\n        </Modal.Body>\r\n      </Modal>\r\n    </div>\r\n  );\r\n}", "//Import Library\r\nimport React from \"react\";\r\nimport { Row, Col } from \"react-bootstrap\";\r\nimport <PERSON> from \"next/link\";\r\n\r\n//Import services/components\r\nimport LayoutHelpModal from \"./../modals/layout-help\";\r\n\r\ntype Header1 = {\r\n  onScroll: (index: number) => void;\r\n};\r\n\r\nconst Header: React.FunctionComponent<Header1> = ({ onScroll }): React.ReactElement => (\r\n  <div className=\"header-block\">\r\n    <div className=\"header-container\" style={{}}>\r\n      <Row>\r\n        <Col sm={3} className=\"logo-image\">\r\n          <img src=\"/images/home/<USER>\" height=\"60px\" />\r\n        </Col>\r\n        <Col sm={9}>\r\n          <ul className=\"quick-menu\">\r\n            <li>\r\n              <a onClick={() => onScroll(0)}>Home</a>\r\n            </li>\r\n            <li>\r\n              <a onClick={() => onScroll(1)}>Our network</a>\r\n            </li>\r\n            <li>\r\n              <a onClick={() => onScroll(2)}>About us</a>\r\n            </li>\r\n            <li>\r\n              <a onClick={() => onScroll(3)}>Contact us</a>\r\n            </li>\r\n            <li>\r\n              <LayoutHelpModal show={false} onHide={() => {}} />\r\n            </li>\r\n            <li className=\"login\">\r\n              <Link href=\"/login\">\r\n                Login\r\n              </Link>\r\n            </li>\r\n          </ul>\r\n        </Col>\r\n      </Row>\r\n    </div>\r\n  </div>\r\n);\r\n\r\nexport default Header;\r\n", "export const networkImages: { [key: number]: string } = {\r\n  0: \"images/home/<USER>\",\r\n  1: \"images/home/<USER>\",\r\n  2: \"images/home/<USER>\",\r\n  3: \"images/home/<USER>\",\r\n  4: \"images/home/<USER>\",\r\n  5: \"images/home/<USER>\",\r\n  6: \"images/home/<USER>\",\r\n  7: \"images/home/<USER>\",\r\n  8: \"images/home/<USER>\"\r\n};\r\n\r\nexport const newsImages: { [key: number]: string } = {\r\n  0: \"images/home/<USER>\",\r\n  1: \"images/home/<USER>\",\r\n  2: \"images/home/<USER>\",\r\n  3: \"images/home/<USER>\",\r\n  4: \"images/home/<USER>\"\r\n};\r\n\r\nexport const sliderData = [\r\n  {\r\n    title: \"SARS-CoV-2 in Germany\",\r\n    content: \"After a temporary stabilisation of case numbers at a higher level in late August and early September 2020, a steep increase in case numbers ensued in October in all federal states. Due to measures implemented at the beginning of November the rise in cases could be stopped, albeit no considerable reduction in case numbers ensued. Since 04/12/2020 case numbers have been sharply increasing again... \",\r\n    href: \"https://www.rki.de/EN/Content/infections/epidemiology/outbreaks/COVID-19/Situationsberichte_Tab.html;jsessionid=0D6329A535C4B9D303C461108C27A545.internet051\",\r\n    image: 'images/home/<USER>'\r\n  }\r\n];\r\n\r\nexport const networkData = [\r\n  {\r\n    title: \"The Robert Koch Institut as international hub for health protection networks\",\r\n    content: \"When there are health emergencies across the world, such as disease outbreaks, the Robert Koch Institut’s expertise is in ever greater demand. RKI staff are involved in various international research projects and programmes. Thus, the RKI helps to tackle urgent public health problems and improve people’s health worldwide. \",\r\n    href: \"https://www.rki.de/EN/Content/Institute/International/international_activities_node.html\"\r\n  },\r\n  {\r\n    title: \"About the Global Health Protection Programme\",\r\n    content: \"As part of its international commitments, Germany is providing  increasing support to partner countries for the management of outbreaks and the development of reliable heafthcare systems. To this end, the German Federal Ministry of Health has launched a Global Health​ Protection Programme to improve and promote health at global scale. \",\r\n    href: \"https://ghpp.de/en/\"\r\n  },\r\n  {\r\n    title: \"Global Health Policy\",\r\n    content: \"Global health issues are closely related to several other policy areas such as development, security, trade and travel, the economy, human rights, nutrition, agriculture, research, employment, education, migration, the environment and climate protection, as well as humanitarian aid. \",\r\n    href: \"https://www.bundesgesundheitsministerium.de/en/international-co-operation.html\"\r\n  },\r\n  {\r\n    title: \"German Biosecurity Programme\",\r\n    content: \"The German Biosecurity, founded by the Federal Foreign Office, is intended to help partner countries tackle biosecurity threats, such as the intentional misuse of biological pathogens and toxins or outbreaks of highly pathogenic diseases and pandemics. The aim is to strengthen the health services of our partner countries in Africa, Central Asia and Eastern Europe, thus enhancing their national security. \",\r\n    href: \"https://www.auswaertiges-amt.de/en/aussenpolitik/themen/abruestung/uebersicht-bcwaffen-node/-/239362\"\r\n  },\r\n  {\r\n    title: \"Emergency Mobile Laboratory\",\r\n    content: \"The European Mobile Laboratory (EMLab) Consortium deploy state of the art boxed field laboratories as well as trained scientists and technicians to epidemics and outbreaks of infectious diseases caused by pathogens up to risk group 4 to perform diagnostics and supporting clinical laboratory analysis on site. \",\r\n    href: \"https://www.emlab.eu/\"\r\n  },\r\n  {\r\n    title: \"Emergency Medical Teams\",\r\n    content: \"The purpose of the Emergency Medical Teams initiative is to improve the timeliness and quality of health services provided by national and international EMTs and enhance the capacity of national health systems in leading the activation and coordination of this response in the immediate aftermath of a disaster, outbreak and/or other emergency. \",\r\n    href: \"https://extranet.who.int/emt/\"\r\n  },\r\n  {\r\n    title: \"Over 800 institutions in over 80 countries supporting WHO programmes\",\r\n    content: \"WHO collaborating centres are institutions such as research institutes, parts of universities or academies, which are designated by the Director-General to carry out activities in support of the Organization's programmes. Currently there are 24 from over 800 WHO collaborating centres in Germany. \",\r\n    href: \"https://www.who.int/about/who-we-are/structure/collaborating-centres\"\r\n  },\r\n  {\r\n    title: \"Global Outbreak Alert and Response Network (GOARN) \",\r\n    content: \"GOARN is a collaboration of existing institutions and networks, constantly alert and ready to respond. The network pools human and technical resources for rapid identification, confirmation and response to outbreaks of international importance. \",\r\n    href: \"https://extranet.who.int/goarn\"\r\n  },\r\n  {\r\n    title: \"International Association of National Public Health Institutes\",\r\n    content: \"The International Association of National Public Health Institutes is a member organization of government agencies working to improve national disease prevention and response. IANPHI is made up of 100+ members, located in approximately 90 countries. \",\r\n  }\r\n];\r\n\r\nexport const newsData = [\r\n  {\r\n    title: \"PEI_Germany\",\r\n    tag: \"@PEI_Germany\",\r\n    content: \"Exten­sively drug-resis­tant Kleb­siella pneu­moniae out­break, north-eastern Ger­many, 2019 (Euro­surveillance, 12.12.2019)\"\r\n  },\r\n  {\r\n    title: \"BMG_Bund\",\r\n    tag: \"@BMG_Bund\",\r\n    content: \"Angesichts der Entwicklung in Italien rechnet Bundesgesundheitsminister Jens Spahn damit, dass sich das Coronavirus auch in Deutschland\"\r\n  },\r\n  {\r\n    title: \"Bfarm_de\",\r\n    tag: \"@bfarm_de\",\r\n    content: \"Was genau macht eigentlich das BfArM? Knapp vier Minuten Film geben Einblicke in unsere Aufgaben, unseren Arbeitsalltag und unsere Motivation.\"\r\n  },\r\n  {\r\n    title: \"FZBorstel\",\r\n    tag: \"@FZBorstel\",\r\n    content: \"The Research Center Borstel, Germany is hosting the next #lipidomics forum! Please check it out, share, spread the word, retweet, register!\"\r\n  },\r\n  {\r\n    title: \"Loeffler_News\",\r\n    tag: \"@Loeffler_News\",\r\n    content: \"With the current cases in Poland, #AfricanSwineFever made a westerly leap of about 250 km and has now moved about 80 km from the German border.\"\r\n  }\r\n];\r\n", "//Import Library\r\nimport React from \"react\";\r\nimport { Row, Col } from \"react-bootstrap\";\r\nimport Link from 'next/link';\r\n\r\n//Import services/components\r\nimport { sliderData } from \"../../../data/landing\";\r\n\r\nconst Slider: React.FunctionComponent<{}> = (): React.ReactElement => (\r\n  <div className='slider'>\r\n    <Row>\r\n      <Col xs={12}>\r\n        {sliderData.map((item, idx) => (\r\n          <div className=\"myslider\" key={idx}>\r\n            <img src=\"images/home/<USER>\" width=\"100%\" />\r\n            <div className=\"sliderContent\">\r\n              <div className=\"sliderTitle\">{item.title}</div>\r\n              <div className=\"sliderDesc\">{item.content}\r\n              <Link href={item.href} target=\"_blank\">\r\n          More Information\r\n        </Link></div>\r\n            </div>\r\n          </div>\r\n        ))}\r\n      </Col>\r\n    </Row>\r\n  </div>\r\n);\r\n\r\nexport default Slider;\r\n", "//Import Library\r\nimport React from \"react\";\r\n\r\nconst Welcome: React.FunctionComponent<{}> = (): React.ReactElement => (\r\n  <div className='welcome'>\r\n    <div className=\"about-section\">\r\n      <div className=\"title\">Welcome to the new Knowledge Platform for international health protection</div>\r\n      <p>The purpose of the new Knowledge Platform is to enable German Institutions and partners, who work in the field of international health protection, \r\n        to share information in order to strengthen our all’s capacity to respond and engage all partners effectively within this network. \r\n        Non-commercial entities and institutions with an ability to support outbreak control and public health emergencies are welcome to the network.\r\n      The site disseminates information concerning events, projects, operations, partner institutions and activities of the network.\r\n      In addition, the platform also creates a forum for partners to collaborate through virtual spaces, including spaces created \r\n      for specific purpose such as WHO Collaboration Centres or Emergency Medical Teams.\r\nThe development of the platform was financed by the German Ministry of Health.</p>\r\n    </div>\r\n  </div>\r\n);\r\n\r\nexport default Welcome;\r\n", "//Import Library\r\nimport React from \"react\";\r\nimport { Col } from \"react-bootstrap\";\r\nimport Link from 'next/link';\r\n\r\n//Import services/components\r\nimport { networkData, networkImages } from \"../../../data/landing\";\r\n\r\nconst Networks: React.FunctionComponent<{}> = (): React.ReactElement => (\r\n  <Col sm={8}>\r\n    <div className=\"block-title network\">Our Networks</div>\r\n    {networkData.map((item, idx) => (\r\n      <div className=\"network-news\" key={idx}>\r\n        <img src={networkImages[idx]} />\r\n        <div className=\"newsContent\">\r\n          <div className=\"newsTitle\">{item.title}</div>          \r\n          <div className=\"newsDesc\">{item.content}\r\n          {item.href ? <Link href={item.href} target=\"_blank\">\r\n          Find more information...\r\n        </Link> : null}\r\n        </div>\r\n        </div>\r\n      </div>\r\n    ))}\r\n  </Col>\r\n);\r\n\r\nexport default Networks;\r\n", "//Import Library\r\nimport React from \"react\";\r\nimport { Col } from \"react-bootstrap\";\r\n\r\n//Import services/components\r\nimport { newsData, newsImages } from \"../../../data/landing\";\r\n\r\nconst NewsFeed: React.FunctionComponent<{}> = (): React.ReactElement => (\r\n  <Col sm={4}>\r\n    <div className=\"block-title news-feeds\">News Feeds</div>\r\n    {newsData.map((item, idx) => (\r\n      <div className=\"feed-news\" key={idx}>\r\n        <img src={newsImages[idx]} />\r\n        <div className=\"newsContent\">\r\n          <div className=\"newsTitle\">{item.title} <span>{item.tag}</span></div>\r\n          <div className=\"newsDesc\">{item.content}</div>\r\n        </div>\r\n      </div>\r\n    ))}\r\n  </Col>\r\n);\r\n\r\nexport default NewsFeed;\r\n", "//Import Library\r\nimport React from \"react\";\r\nimport { Row, Col } from \"react-bootstrap\";\r\n\r\nconst AboutUs: React.FunctionComponent<{ innerRef: any }> = ({ innerRef }): React.ReactElement => (\r\n  <div className=\"about-us\" ref={innerRef}>\r\n    <div className='aboutus-wrap' >\r\n      <Row xs={12}>\r\n        <Col sm={6}>\r\n        <img src=\"/images/home/<USER>\" alt=\"About\" width=\"100%\" />\r\n        </Col>\r\n        <Col sm={6}>\r\n          <div className=\"content\">\r\n            <div className=\"title\">About us</div>\r\n            <div className=\"desc\">The German Ministry of Health has commissioned the Robert Koch Institut (RKI) to develop this platform.\r\n            Under the coordination of the Centre for International Health Protection, based at RKI in Berlin,\r\n            we want to support our partners in exchanging information in order to better coordinate\r\n            the activities of all of them and thus strengthen the response capacity of all. </div>\r\n          </div>\r\n        </Col>\r\n      </Row>\r\n    </div>\r\n  </div>\r\n);\r\n\r\nexport default AboutUs;\r\n", "//Import Library\r\nimport React from \"react\";\r\nimport { Row, Col } from \"react-bootstrap\";\r\n\r\nconst Footer: React.FunctionComponent<{ innerRef: any }> = ({\r\n  innerRef,\r\n}): React.ReactElement => (\r\n  <div className=\"landing-footer\" ref={innerRef}>\r\n    <div className=\"footer-block\">\r\n      <Col xs={12}>\r\n        <Row>\r\n          <Col\r\n            sm={3}\r\n            style={{\r\n              marginTop: \"10px\",\r\n            }}\r\n          >\r\n            <img src=\"/images/home/<USER>\" className=\"img-fluid\" />\r\n          </Col>\r\n          <Col\r\n            sm={3}\r\n            style={{\r\n              marginTop: \"10px\",\r\n            }}\r\n          >\r\n            <div className=\"footerLeft\">\r\n              <div style={{ lineHeight: \"14px\" }}>\r\n                <p>\r\n                  The Robert Koch Institut is a Federal Institute within the\r\n                  portfolio of the Federal Ministry of Health\r\n                </p>\r\n                <p>\r\n                  <br />\r\n                  <span style={{ fontFamily: \"verdana\" }}>©</span> {new Date().getFullYear()} Robert\r\n                  Koch Institute\r\n                </p>\r\n                <p>All rights reserved unless explicitly granted.</p>\r\n              </div>\r\n            </div>\r\n          </Col>\r\n          <Col sm={3}>\r\n            <div className=\"contactInfo\" style={{ marginTop: \"3px\" }}>\r\n              <p style={{ fontSize: \"16px\" }}>Robert Koch Institut</p>\r\n              <p>\r\n                Federal Information Centre for International\r\n                <br />\r\n                Health Protection (ZIG1)\r\n              </p>\r\n              <p>Nordufer 20</p>\r\n              <p>13353 Berlin </p>\r\n              <p>Germany</p>\r\n              <br />\r\n              <br />\r\n            </div>\r\n          </Col>\r\n          <Col\r\n            sm={3}\r\n            style={{\r\n              marginTop: \"10px\",\r\n            }}\r\n          >\r\n            <div className=\"contactNum\">\r\n              <p>Tel: +49 (0) 3018 7540</p>\r\n              <p>Email: <EMAIL></p>\r\n            </div>\r\n          </Col>\r\n        </Row>\r\n      </Col>\r\n    </div>\r\n  </div>\r\n);\r\n\r\nexport default Footer;\r\n", "//Import Library\r\nimport React, { useRef } from \"react\";\r\nimport { Container, Row, Col, Button } from \"react-bootstrap\";\r\n\r\n//Import services/components\r\nimport Header from './Header';\r\nimport Slider from \"./Slider\";\r\nimport Welcome from \"./Welcome\";\r\nimport Networks from \"./Networks\";\r\nimport NewsFeed from \"./NewsFeed\";\r\nimport AboutUs from \"./AboutUs\";\r\nimport Footer from \"./Footer\";\r\n\r\nconst Layout: React.FunctionComponent<{}> = (): React.ReactElement => {\r\n  const homeRef = useRef<HTMLDivElement | null>(null);\r\n  const networkRef = useRef<HTMLDivElement | null>(null);\r\n  const aboutUsRef = useRef<HTMLDivElement | null>(null);\r\n  const footerRef = useRef<HTMLDivElement | null>(null);\r\n\r\n  /**\r\n   * Scrolls to the respective ref element\r\n   * @param index: number\r\n   */\r\n  const onScrollToElement = (index: number): void => {\r\n    const refs = [\r\n      homeRef,\r\n      networkRef,\r\n      aboutUsRef,\r\n      footerRef\r\n    ];\r\n\r\n    if (refs[index] && refs[index].current) {\r\n      const headerHeight = 100;\r\n      const topOfElement = (window.pageYOffset + refs[index].current.getBoundingClientRect().top - headerHeight);\r\n      window.scrollTo({ behavior: \"smooth\", top: topOfElement });\r\n    }\r\n  }\r\n\r\n  return (\r\n    <div className=\"landing\">\r\n      <Header onScroll={onScrollToElement} />\r\n      <div className='horizontal-line' />\r\n      <div className=\"home\" ref={homeRef}>\r\n        <Slider />\r\n      </div>\r\n      <Welcome />\r\n      <div ref={networkRef} >\r\n        <Row className='feeds-section'>\r\n          <Networks />\r\n          <NewsFeed />\r\n        </Row>\r\n      </div>\r\n      <AboutUs innerRef={aboutUsRef} />\r\n      <Footer innerRef={footerRef} />\r\n    </div>\r\n  );\r\n}\r\n\r\nexport default Layout;\r\n", "//Import Library\r\nimport React from \"react\";\r\n\r\n//Import services/components\r\nimport Landing from \"../../components/layout/landing\";\r\nimport { serverSideTranslations } from 'next-i18next/serverSideTranslations';\r\nimport { GetStaticPropsContext, GetStaticProps } from 'next'\r\n\r\nconst Landings = () => {\r\n  return <Landing />;\r\n};\r\nexport const getStaticProps: GetStaticProps = async (context: GetStaticPropsContext) => {\r\n  const locale = context.locale || 'en'\r\n\r\n  return {\r\n    props: {\r\n      ...(await serverSideTranslations(locale, ['common'])),\r\n    },\r\n  }\r\n}\r\nexport default Landings;\r\n"], "names": ["LayoutHelpModal", "props", "showHelpModal", "setHelpModal", "useState", "setHelp", "_initialVal", "title", "description", "pageCategory", "isEnabled", "createMarkup", "__html", "htmlContent", "t", "useTranslation", "fetchModalData", "helpParams", "query", "sort", "limit", "pageCategoryId", "fetchPageCategory", "length", "response", "apiService", "get", "Array", "isArray", "data", "defaultTitle", "defaultDesc", "useEffect", "_id", "div", "a", "onClick", "OverlayTrigger", "placement", "delay", "show", "hide", "overlay", "<PERSON><PERSON><PERSON>", "id", "i", "className", "Modal", "size", "onHide", "handleClose", "Header", "closeButton", "Title", "Body", "ul", "Accordion", "<PERSON><PERSON>", "eventKey", "li", "dangerouslySetInnerHTML", "onScroll", "style", "Row", "Col", "sm", "img", "src", "height", "Link", "href", "networkImages", "content", "image", "tag", "Slide<PERSON>", "xs", "sliderData", "map", "item", "idx", "width", "target", "Welcome", "p", "Networks", "networkData", "NewsFeed", "newsData", "newsImages", "span", "innerRef", "AboutUs", "ref", "alt", "Footer", "marginTop", "lineHeight", "br", "fontFamily", "Date", "getFullYear", "fontSize", "homeRef", "useRef", "Layout", "networkRef", "aboutUsRef", "footerRef", "onScrollToElement", "refs", "index", "current", "topOfElement", "window", "pageYOffset", "getBoundingClientRect", "top", "headerHeight", "scrollTo", "behavior", "Landings", "Landing"], "sourceRoot": "", "ignoreList": []}