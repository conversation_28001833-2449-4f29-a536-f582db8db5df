(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[9382],{45047:(e,s,a)=>{"use strict";a.r(s),a.d(s,{default:()=>h});var l=a(37876),t=a(14232),r=a(49589),n=a(56970),i=a(37784),c=a(12697),u=a(29504),d=a(53718),o=a(31753);let h=e=>{let{filterText:s,onFilter:a,onFilterStatusChange:h,onClear:p,filterStatus:x}=e,[A,_]=(0,t.useState)([]),{t:j}=(0,o.Bd)("common"),m=async e=>{let s=await d.A.get("/operation_status",e);s&&Array.isArray(s.data)&&_(s.data)};return(0,t.useEffect)(()=>{m({query:{},sort:{title:"asc"}})},[]),(0,l.jsx)(r.A,{fluid:!0,className:"p-0",children:(0,l.jsxs)(n.A,{children:[(0,l.jsx)(i.A,{xs:6,className:"ps-0 align-self-end mb-3",children:(0,l.jsx)(c.A,{type:"text",className:"searchInput",placeholder:j("search"),"aria-label":"Search",value:s,onChange:a})}),(0,l.jsx)(i.A,{xs:6,children:(0,l.jsx)(u.A,{children:(0,l.jsxs)(u.A.Group,{as:n.A,controlId:"statusFilter",children:[(0,l.jsx)(u.A.Label,{column:!0,sm:"3",lg:"2",children:j("Status")}),(0,l.jsx)(i.A,{className:"ps-0 pe-1",children:(0,l.jsxs)(c.A,{as:"select","aria-label":"Status",onChange:h,value:x,children:[(0,l.jsx)("option",{value:"",children:"All"}),A.map((e,s)=>(0,l.jsx)("option",{value:e._id,children:e.title},s))]})})]})})})]})})}},78780:(e,s,a)=>{(window.__NEXT_P=window.__NEXT_P||[]).push(["/operation/OperationsTableFilter",function(){return a(45047)}])}},e=>{var s=s=>e(e.s=s);e.O(0,[636,6593,8792],()=>s(78780)),_N_E=e.O()}]);
//# sourceMappingURL=OperationsTableFilter-4088ec68da18605f.js.map