"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[1772],{21772:(t,e,n)=>{function a(t,e){var n=Object.keys(t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(t);e&&(a=a.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),n.push.apply(n,a)}return n}function r(t){for(var e=1;e<arguments.length;e++){var n=null!=arguments[e]?arguments[e]:{};e%2?a(Object(n),!0).forEach(function(e){var a,r;a=e,r=n[e],(a=function(t){var e=function(t,e){if("object"!=typeof t||!t)return t;var n=t[Symbol.toPrimitive];if(void 0!==n){var a=n.call(t,e||"default");if("object"!=typeof a)return a;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==typeof e?e:e+""}(a))in t?Object.defineProperty(t,a,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[a]=r}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(n)):a(Object(n)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(n,e))})}return t}n.d(e,{g:()=>e1});let i=()=>{},o={},l={},s=null,f={mark:i,measure:i};try{"undefined"!=typeof window&&(o=window),"undefined"!=typeof document&&(l=document),"undefined"!=typeof MutationObserver&&(s=MutationObserver),"undefined"!=typeof performance&&(f=performance)}catch(t){}let{userAgent:c=""}=o.navigator||{},u=o,d=l,m=s,p=f;u.document;let g=!!d.documentElement&&!!d.head&&"function"==typeof d.addEventListener&&"function"==typeof d.createElement,h=~c.indexOf("MSIE")||~c.indexOf("Trident/");var b={classic:{fa:"solid",fas:"solid","fa-solid":"solid",far:"regular","fa-regular":"regular",fal:"light","fa-light":"light",fat:"thin","fa-thin":"thin",fab:"brands","fa-brands":"brands"},duotone:{fa:"solid",fad:"solid","fa-solid":"solid","fa-duotone":"solid",fadr:"regular","fa-regular":"regular",fadl:"light","fa-light":"light",fadt:"thin","fa-thin":"thin"},sharp:{fa:"solid",fass:"solid","fa-solid":"solid",fasr:"regular","fa-regular":"regular",fasl:"light","fa-light":"light",fast:"thin","fa-thin":"thin"},"sharp-duotone":{fa:"solid",fasds:"solid","fa-solid":"solid",fasdr:"regular","fa-regular":"regular",fasdl:"light","fa-light":"light",fasdt:"thin","fa-thin":"thin"}},y=["fa-classic","fa-duotone","fa-sharp","fa-sharp-duotone"],v="classic",x="duotone",k=[v,x,"sharp","sharp-duotone"],w=new Map([["classic",{defaultShortPrefixId:"fas",defaultStyleId:"solid",styleIds:["solid","regular","light","thin","brands"],futureStyleIds:[],defaultFontWeight:900}],["sharp",{defaultShortPrefixId:"fass",defaultStyleId:"solid",styleIds:["solid","regular","light","thin"],futureStyleIds:[],defaultFontWeight:900}],["duotone",{defaultShortPrefixId:"fad",defaultStyleId:"solid",styleIds:["solid","regular","light","thin"],futureStyleIds:[],defaultFontWeight:900}],["sharp-duotone",{defaultShortPrefixId:"fasds",defaultStyleId:"solid",styleIds:["solid","regular","light","thin"],futureStyleIds:[],defaultFontWeight:900}]]),A=["fak","fa-kit","fakd","fa-kit-duotone"],O={kit:{fak:"kit","fa-kit":"kit"},"kit-duotone":{fakd:"kit-duotone","fa-kit-duotone":"kit-duotone"}},P=["fak","fakd"],S={kit:{kit:"fak"},"kit-duotone":{"kit-duotone":"fakd"}},j={GROUP:"duotone-group",SWAP_OPACITY:"swap-opacity",PRIMARY:"primary",SECONDARY:"secondary"},N=["fak","fa-kit","fakd","fa-kit-duotone"],C={classic:{fab:"fa-brands",fad:"fa-duotone",fal:"fa-light",far:"fa-regular",fas:"fa-solid",fat:"fa-thin"},duotone:{fadr:"fa-regular",fadl:"fa-light",fadt:"fa-thin"},sharp:{fass:"fa-solid",fasr:"fa-regular",fasl:"fa-light",fast:"fa-thin"},"sharp-duotone":{fasds:"fa-solid",fasdr:"fa-regular",fasdl:"fa-light",fasdt:"fa-thin"}},E=["fa","fas","far","fal","fat","fad","fadr","fadl","fadt","fab","fass","fasr","fasl","fast","fasds","fasdr","fasdl","fasdt","fa-classic","fa-duotone","fa-sharp","fa-sharp-duotone","fa-solid","fa-regular","fa-light","fa-thin","fa-duotone","fa-brands"],z=[1,2,3,4,5,6,7,8,9,10],I=z.concat([11,12,13,14,15,16,17,18,19,20]),M=["classic","duotone","sharp","sharp-duotone","solid","regular","light","thin","duotone","brands","2xs","xs","sm","lg","xl","2xl","beat","border","fade","beat-fade","bounce","flip-both","flip-horizontal","flip-vertical","flip","fw","inverse","layers-counter","layers-text","layers","li","pull-left","pull-right","pulse","rotate-180","rotate-270","rotate-90","rotate-by","shake","spin-pulse","spin-reverse","spin","stack-1x","stack-2x","stack","ul",j.GROUP,j.SWAP_OPACITY,j.PRIMARY,j.SECONDARY].concat(z.map(t=>"".concat(t,"x"))).concat(I.map(t=>"w-".concat(t)));let F="___FONT_AWESOME___",D="svg-inline--fa",L="data-fa-i2svg",R="data-fa-pseudo-element",T="data-prefix",Y="data-icon",W="fontawesome-i2svg",_=["HTML","HEAD","STYLE","SCRIPT"],U=(()=>{try{return!0}catch(t){return!1}})();function H(t){return new Proxy(t,{get:(t,e)=>e in t?t[e]:t[v]})}let B=r({},b);B[v]=r(r(r(r({},{"fa-duotone":"duotone"}),b[v]),O.kit),O["kit-duotone"]);let X=H(B),q=r({},{classic:{solid:"fas",regular:"far",light:"fal",thin:"fat",brands:"fab"},duotone:{solid:"fad",regular:"fadr",light:"fadl",thin:"fadt"},sharp:{solid:"fass",regular:"fasr",light:"fasl",thin:"fast"},"sharp-duotone":{solid:"fasds",regular:"fasdr",light:"fasdl",thin:"fasdt"}});q[v]=r(r(r(r({},{duotone:"fad"}),q[v]),S.kit),S["kit-duotone"]);let V=H(q),G=r({},C);G[v]=r(r({},G[v]),{fak:"fa-kit"});let K=H(G),$=r({},{classic:{"fa-brands":"fab","fa-duotone":"fad","fa-light":"fal","fa-regular":"far","fa-solid":"fas","fa-thin":"fat"},duotone:{"fa-regular":"fadr","fa-light":"fadl","fa-thin":"fadt"},sharp:{"fa-solid":"fass","fa-regular":"fasr","fa-light":"fasl","fa-thin":"fast"},"sharp-duotone":{"fa-solid":"fasds","fa-regular":"fasdr","fa-light":"fasdl","fa-thin":"fasdt"}});$[v]=r(r({},$[v]),{"fa-kit":"fak"}),H($);let J=/fa(s|r|l|t|d|dr|dl|dt|b|k|kd|ss|sr|sl|st|sds|sdr|sdl|sdt)?[\-\ ]/,Q="fa-layers-text",Z=/Font ?Awesome ?([56 ]*)(Solid|Regular|Light|Thin|Duotone|Brands|Free|Pro|Sharp Duotone|Sharp|Kit)?.*/i;H(r({},{classic:{900:"fas",400:"far",normal:"far",300:"fal",100:"fat"},duotone:{900:"fad",400:"fadr",300:"fadl",100:"fadt"},sharp:{900:"fass",400:"fasr",300:"fasl",100:"fast"},"sharp-duotone":{900:"fasds",400:"fasdr",300:"fasdl",100:"fasdt"}}));let tt=["class","data-prefix","data-icon","data-fa-transform","data-fa-mask"],te={GROUP:"duotone-group",PRIMARY:"primary",SECONDARY:"secondary"},tn=["kit",...M],ta=u.FontAwesomeConfig||{};d&&"function"==typeof d.querySelector&&[["data-family-prefix","familyPrefix"],["data-css-prefix","cssPrefix"],["data-family-default","familyDefault"],["data-style-default","styleDefault"],["data-replacement-class","replacementClass"],["data-auto-replace-svg","autoReplaceSvg"],["data-auto-add-css","autoAddCss"],["data-auto-a11y","autoA11y"],["data-search-pseudo-elements","searchPseudoElements"],["data-observe-mutations","observeMutations"],["data-mutate-approach","mutateApproach"],["data-keep-original-source","keepOriginalSource"],["data-measure-performance","measurePerformance"],["data-show-missing-icons","showMissingIcons"]].forEach(t=>{let[e,n]=t,a=function(t){return""===t||"false"!==t&&("true"===t||t)}(function(t){var e=d.querySelector("script["+t+"]");if(e)return e.getAttribute(t)}(e));null!=a&&(ta[n]=a)});let tr={styleDefault:"solid",familyDefault:v,cssPrefix:"fa",replacementClass:D,autoReplaceSvg:!0,autoAddCss:!0,autoA11y:!0,searchPseudoElements:!1,observeMutations:!0,mutateApproach:"async",keepOriginalSource:!0,measurePerformance:!1,showMissingIcons:!0};ta.familyPrefix&&(ta.cssPrefix=ta.familyPrefix);let ti=r(r({},tr),ta);ti.autoReplaceSvg||(ti.observeMutations=!1);let to={};Object.keys(tr).forEach(t=>{Object.defineProperty(to,t,{enumerable:!0,set:function(e){ti[t]=e,tl.forEach(t=>t(to))},get:function(){return ti[t]}})}),Object.defineProperty(to,"familyPrefix",{enumerable:!0,set:function(t){ti.cssPrefix=t,tl.forEach(t=>t(to))},get:function(){return ti.cssPrefix}}),u.FontAwesomeConfig=to;let tl=[],ts={size:16,x:0,y:0,rotate:0,flipX:!1,flipY:!1};function tf(){let t=12,e="";for(;t-- >0;)e+="0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ"[62*Math.random()|0];return e}function tc(t){let e=[];for(let n=(t||[]).length>>>0;n--;)e[n]=t[n];return e}function tu(t){return t.classList?tc(t.classList):(t.getAttribute("class")||"").split(" ").filter(t=>t)}function td(t){return"".concat(t).replace(/&/g,"&amp;").replace(/"/g,"&quot;").replace(/'/g,"&#39;").replace(/</g,"&lt;").replace(/>/g,"&gt;")}function tm(t){return Object.keys(t||{}).reduce((e,n)=>e+"".concat(n,": ").concat(t[n].trim(),";"),"")}function tp(t){return t.size!==ts.size||t.x!==ts.x||t.y!==ts.y||t.rotate!==ts.rotate||t.flipX||t.flipY}function tg(){let t=to.cssPrefix,e=to.replacementClass,n=':root, :host {\n  --fa-font-solid: normal 900 1em/1 "Font Awesome 6 Free";\n  --fa-font-regular: normal 400 1em/1 "Font Awesome 6 Free";\n  --fa-font-light: normal 300 1em/1 "Font Awesome 6 Pro";\n  --fa-font-thin: normal 100 1em/1 "Font Awesome 6 Pro";\n  --fa-font-duotone: normal 900 1em/1 "Font Awesome 6 Duotone";\n  --fa-font-duotone-regular: normal 400 1em/1 "Font Awesome 6 Duotone";\n  --fa-font-duotone-light: normal 300 1em/1 "Font Awesome 6 Duotone";\n  --fa-font-duotone-thin: normal 100 1em/1 "Font Awesome 6 Duotone";\n  --fa-font-brands: normal 400 1em/1 "Font Awesome 6 Brands";\n  --fa-font-sharp-solid: normal 900 1em/1 "Font Awesome 6 Sharp";\n  --fa-font-sharp-regular: normal 400 1em/1 "Font Awesome 6 Sharp";\n  --fa-font-sharp-light: normal 300 1em/1 "Font Awesome 6 Sharp";\n  --fa-font-sharp-thin: normal 100 1em/1 "Font Awesome 6 Sharp";\n  --fa-font-sharp-duotone-solid: normal 900 1em/1 "Font Awesome 6 Sharp Duotone";\n  --fa-font-sharp-duotone-regular: normal 400 1em/1 "Font Awesome 6 Sharp Duotone";\n  --fa-font-sharp-duotone-light: normal 300 1em/1 "Font Awesome 6 Sharp Duotone";\n  --fa-font-sharp-duotone-thin: normal 100 1em/1 "Font Awesome 6 Sharp Duotone";\n}\n\nsvg:not(:root).svg-inline--fa, svg:not(:host).svg-inline--fa {\n  overflow: visible;\n  box-sizing: content-box;\n}\n\n.svg-inline--fa {\n  display: var(--fa-display, inline-block);\n  height: 1em;\n  overflow: visible;\n  vertical-align: -0.125em;\n}\n.svg-inline--fa.fa-2xs {\n  vertical-align: 0.1em;\n}\n.svg-inline--fa.fa-xs {\n  vertical-align: 0em;\n}\n.svg-inline--fa.fa-sm {\n  vertical-align: -0.0714285705em;\n}\n.svg-inline--fa.fa-lg {\n  vertical-align: -0.2em;\n}\n.svg-inline--fa.fa-xl {\n  vertical-align: -0.25em;\n}\n.svg-inline--fa.fa-2xl {\n  vertical-align: -0.3125em;\n}\n.svg-inline--fa.fa-pull-left {\n  margin-right: var(--fa-pull-margin, 0.3em);\n  width: auto;\n}\n.svg-inline--fa.fa-pull-right {\n  margin-left: var(--fa-pull-margin, 0.3em);\n  width: auto;\n}\n.svg-inline--fa.fa-li {\n  width: var(--fa-li-width, 2em);\n  top: 0.25em;\n}\n.svg-inline--fa.fa-fw {\n  width: var(--fa-fw-width, 1.25em);\n}\n\n.fa-layers svg.svg-inline--fa {\n  bottom: 0;\n  left: 0;\n  margin: auto;\n  position: absolute;\n  right: 0;\n  top: 0;\n}\n\n.fa-layers-counter, .fa-layers-text {\n  display: inline-block;\n  position: absolute;\n  text-align: center;\n}\n\n.fa-layers {\n  display: inline-block;\n  height: 1em;\n  position: relative;\n  text-align: center;\n  vertical-align: -0.125em;\n  width: 1em;\n}\n.fa-layers svg.svg-inline--fa {\n  transform-origin: center center;\n}\n\n.fa-layers-text {\n  left: 50%;\n  top: 50%;\n  transform: translate(-50%, -50%);\n  transform-origin: center center;\n}\n\n.fa-layers-counter {\n  background-color: var(--fa-counter-background-color, #ff253a);\n  border-radius: var(--fa-counter-border-radius, 1em);\n  box-sizing: border-box;\n  color: var(--fa-inverse, #fff);\n  line-height: var(--fa-counter-line-height, 1);\n  max-width: var(--fa-counter-max-width, 5em);\n  min-width: var(--fa-counter-min-width, 1.5em);\n  overflow: hidden;\n  padding: var(--fa-counter-padding, 0.25em 0.5em);\n  right: var(--fa-right, 0);\n  text-overflow: ellipsis;\n  top: var(--fa-top, 0);\n  transform: scale(var(--fa-counter-scale, 0.25));\n  transform-origin: top right;\n}\n\n.fa-layers-bottom-right {\n  bottom: var(--fa-bottom, 0);\n  right: var(--fa-right, 0);\n  top: auto;\n  transform: scale(var(--fa-layers-scale, 0.25));\n  transform-origin: bottom right;\n}\n\n.fa-layers-bottom-left {\n  bottom: var(--fa-bottom, 0);\n  left: var(--fa-left, 0);\n  right: auto;\n  top: auto;\n  transform: scale(var(--fa-layers-scale, 0.25));\n  transform-origin: bottom left;\n}\n\n.fa-layers-top-right {\n  top: var(--fa-top, 0);\n  right: var(--fa-right, 0);\n  transform: scale(var(--fa-layers-scale, 0.25));\n  transform-origin: top right;\n}\n\n.fa-layers-top-left {\n  left: var(--fa-left, 0);\n  right: auto;\n  top: var(--fa-top, 0);\n  transform: scale(var(--fa-layers-scale, 0.25));\n  transform-origin: top left;\n}\n\n.fa-1x {\n  font-size: 1em;\n}\n\n.fa-2x {\n  font-size: 2em;\n}\n\n.fa-3x {\n  font-size: 3em;\n}\n\n.fa-4x {\n  font-size: 4em;\n}\n\n.fa-5x {\n  font-size: 5em;\n}\n\n.fa-6x {\n  font-size: 6em;\n}\n\n.fa-7x {\n  font-size: 7em;\n}\n\n.fa-8x {\n  font-size: 8em;\n}\n\n.fa-9x {\n  font-size: 9em;\n}\n\n.fa-10x {\n  font-size: 10em;\n}\n\n.fa-2xs {\n  font-size: 0.625em;\n  line-height: 0.1em;\n  vertical-align: 0.225em;\n}\n\n.fa-xs {\n  font-size: 0.75em;\n  line-height: 0.0833333337em;\n  vertical-align: 0.125em;\n}\n\n.fa-sm {\n  font-size: 0.875em;\n  line-height: 0.0714285718em;\n  vertical-align: 0.0535714295em;\n}\n\n.fa-lg {\n  font-size: 1.25em;\n  line-height: 0.05em;\n  vertical-align: -0.075em;\n}\n\n.fa-xl {\n  font-size: 1.5em;\n  line-height: 0.0416666682em;\n  vertical-align: -0.125em;\n}\n\n.fa-2xl {\n  font-size: 2em;\n  line-height: 0.03125em;\n  vertical-align: -0.1875em;\n}\n\n.fa-fw {\n  text-align: center;\n  width: 1.25em;\n}\n\n.fa-ul {\n  list-style-type: none;\n  margin-left: var(--fa-li-margin, 2.5em);\n  padding-left: 0;\n}\n.fa-ul > li {\n  position: relative;\n}\n\n.fa-li {\n  left: calc(-1 * var(--fa-li-width, 2em));\n  position: absolute;\n  text-align: center;\n  width: var(--fa-li-width, 2em);\n  line-height: inherit;\n}\n\n.fa-border {\n  border-color: var(--fa-border-color, #eee);\n  border-radius: var(--fa-border-radius, 0.1em);\n  border-style: var(--fa-border-style, solid);\n  border-width: var(--fa-border-width, 0.08em);\n  padding: var(--fa-border-padding, 0.2em 0.25em 0.15em);\n}\n\n.fa-pull-left {\n  float: left;\n  margin-right: var(--fa-pull-margin, 0.3em);\n}\n\n.fa-pull-right {\n  float: right;\n  margin-left: var(--fa-pull-margin, 0.3em);\n}\n\n.fa-beat {\n  animation-name: fa-beat;\n  animation-delay: var(--fa-animation-delay, 0s);\n  animation-direction: var(--fa-animation-direction, normal);\n  animation-duration: var(--fa-animation-duration, 1s);\n  animation-iteration-count: var(--fa-animation-iteration-count, infinite);\n  animation-timing-function: var(--fa-animation-timing, ease-in-out);\n}\n\n.fa-bounce {\n  animation-name: fa-bounce;\n  animation-delay: var(--fa-animation-delay, 0s);\n  animation-direction: var(--fa-animation-direction, normal);\n  animation-duration: var(--fa-animation-duration, 1s);\n  animation-iteration-count: var(--fa-animation-iteration-count, infinite);\n  animation-timing-function: var(--fa-animation-timing, cubic-bezier(0.28, 0.84, 0.42, 1));\n}\n\n.fa-fade {\n  animation-name: fa-fade;\n  animation-delay: var(--fa-animation-delay, 0s);\n  animation-direction: var(--fa-animation-direction, normal);\n  animation-duration: var(--fa-animation-duration, 1s);\n  animation-iteration-count: var(--fa-animation-iteration-count, infinite);\n  animation-timing-function: var(--fa-animation-timing, cubic-bezier(0.4, 0, 0.6, 1));\n}\n\n.fa-beat-fade {\n  animation-name: fa-beat-fade;\n  animation-delay: var(--fa-animation-delay, 0s);\n  animation-direction: var(--fa-animation-direction, normal);\n  animation-duration: var(--fa-animation-duration, 1s);\n  animation-iteration-count: var(--fa-animation-iteration-count, infinite);\n  animation-timing-function: var(--fa-animation-timing, cubic-bezier(0.4, 0, 0.6, 1));\n}\n\n.fa-flip {\n  animation-name: fa-flip;\n  animation-delay: var(--fa-animation-delay, 0s);\n  animation-direction: var(--fa-animation-direction, normal);\n  animation-duration: var(--fa-animation-duration, 1s);\n  animation-iteration-count: var(--fa-animation-iteration-count, infinite);\n  animation-timing-function: var(--fa-animation-timing, ease-in-out);\n}\n\n.fa-shake {\n  animation-name: fa-shake;\n  animation-delay: var(--fa-animation-delay, 0s);\n  animation-direction: var(--fa-animation-direction, normal);\n  animation-duration: var(--fa-animation-duration, 1s);\n  animation-iteration-count: var(--fa-animation-iteration-count, infinite);\n  animation-timing-function: var(--fa-animation-timing, linear);\n}\n\n.fa-spin {\n  animation-name: fa-spin;\n  animation-delay: var(--fa-animation-delay, 0s);\n  animation-direction: var(--fa-animation-direction, normal);\n  animation-duration: var(--fa-animation-duration, 2s);\n  animation-iteration-count: var(--fa-animation-iteration-count, infinite);\n  animation-timing-function: var(--fa-animation-timing, linear);\n}\n\n.fa-spin-reverse {\n  --fa-animation-direction: reverse;\n}\n\n.fa-pulse,\n.fa-spin-pulse {\n  animation-name: fa-spin;\n  animation-direction: var(--fa-animation-direction, normal);\n  animation-duration: var(--fa-animation-duration, 1s);\n  animation-iteration-count: var(--fa-animation-iteration-count, infinite);\n  animation-timing-function: var(--fa-animation-timing, steps(8));\n}\n\n@media (prefers-reduced-motion: reduce) {\n  .fa-beat,\n.fa-bounce,\n.fa-fade,\n.fa-beat-fade,\n.fa-flip,\n.fa-pulse,\n.fa-shake,\n.fa-spin,\n.fa-spin-pulse {\n    animation-delay: -1ms;\n    animation-duration: 1ms;\n    animation-iteration-count: 1;\n    transition-delay: 0s;\n    transition-duration: 0s;\n  }\n}\n@keyframes fa-beat {\n  0%, 90% {\n    transform: scale(1);\n  }\n  45% {\n    transform: scale(var(--fa-beat-scale, 1.25));\n  }\n}\n@keyframes fa-bounce {\n  0% {\n    transform: scale(1, 1) translateY(0);\n  }\n  10% {\n    transform: scale(var(--fa-bounce-start-scale-x, 1.1), var(--fa-bounce-start-scale-y, 0.9)) translateY(0);\n  }\n  30% {\n    transform: scale(var(--fa-bounce-jump-scale-x, 0.9), var(--fa-bounce-jump-scale-y, 1.1)) translateY(var(--fa-bounce-height, -0.5em));\n  }\n  50% {\n    transform: scale(var(--fa-bounce-land-scale-x, 1.05), var(--fa-bounce-land-scale-y, 0.95)) translateY(0);\n  }\n  57% {\n    transform: scale(1, 1) translateY(var(--fa-bounce-rebound, -0.125em));\n  }\n  64% {\n    transform: scale(1, 1) translateY(0);\n  }\n  100% {\n    transform: scale(1, 1) translateY(0);\n  }\n}\n@keyframes fa-fade {\n  50% {\n    opacity: var(--fa-fade-opacity, 0.4);\n  }\n}\n@keyframes fa-beat-fade {\n  0%, 100% {\n    opacity: var(--fa-beat-fade-opacity, 0.4);\n    transform: scale(1);\n  }\n  50% {\n    opacity: 1;\n    transform: scale(var(--fa-beat-fade-scale, 1.125));\n  }\n}\n@keyframes fa-flip {\n  50% {\n    transform: rotate3d(var(--fa-flip-x, 0), var(--fa-flip-y, 1), var(--fa-flip-z, 0), var(--fa-flip-angle, -180deg));\n  }\n}\n@keyframes fa-shake {\n  0% {\n    transform: rotate(-15deg);\n  }\n  4% {\n    transform: rotate(15deg);\n  }\n  8%, 24% {\n    transform: rotate(-18deg);\n  }\n  12%, 28% {\n    transform: rotate(18deg);\n  }\n  16% {\n    transform: rotate(-22deg);\n  }\n  20% {\n    transform: rotate(22deg);\n  }\n  32% {\n    transform: rotate(-12deg);\n  }\n  36% {\n    transform: rotate(12deg);\n  }\n  40%, 100% {\n    transform: rotate(0deg);\n  }\n}\n@keyframes fa-spin {\n  0% {\n    transform: rotate(0deg);\n  }\n  100% {\n    transform: rotate(360deg);\n  }\n}\n.fa-rotate-90 {\n  transform: rotate(90deg);\n}\n\n.fa-rotate-180 {\n  transform: rotate(180deg);\n}\n\n.fa-rotate-270 {\n  transform: rotate(270deg);\n}\n\n.fa-flip-horizontal {\n  transform: scale(-1, 1);\n}\n\n.fa-flip-vertical {\n  transform: scale(1, -1);\n}\n\n.fa-flip-both,\n.fa-flip-horizontal.fa-flip-vertical {\n  transform: scale(-1, -1);\n}\n\n.fa-rotate-by {\n  transform: rotate(var(--fa-rotate-angle, 0));\n}\n\n.fa-stack {\n  display: inline-block;\n  vertical-align: middle;\n  height: 2em;\n  position: relative;\n  width: 2.5em;\n}\n\n.fa-stack-1x,\n.fa-stack-2x {\n  bottom: 0;\n  left: 0;\n  margin: auto;\n  position: absolute;\n  right: 0;\n  top: 0;\n  z-index: var(--fa-stack-z-index, auto);\n}\n\n.svg-inline--fa.fa-stack-1x {\n  height: 1em;\n  width: 1.25em;\n}\n.svg-inline--fa.fa-stack-2x {\n  height: 2em;\n  width: 2.5em;\n}\n\n.fa-inverse {\n  color: var(--fa-inverse, #fff);\n}\n\n.sr-only,\n.fa-sr-only {\n  position: absolute;\n  width: 1px;\n  height: 1px;\n  padding: 0;\n  margin: -1px;\n  overflow: hidden;\n  clip: rect(0, 0, 0, 0);\n  white-space: nowrap;\n  border-width: 0;\n}\n\n.sr-only-focusable:not(:focus),\n.fa-sr-only-focusable:not(:focus) {\n  position: absolute;\n  width: 1px;\n  height: 1px;\n  padding: 0;\n  margin: -1px;\n  overflow: hidden;\n  clip: rect(0, 0, 0, 0);\n  white-space: nowrap;\n  border-width: 0;\n}\n\n.svg-inline--fa .fa-primary {\n  fill: var(--fa-primary-color, currentColor);\n  opacity: var(--fa-primary-opacity, 1);\n}\n\n.svg-inline--fa .fa-secondary {\n  fill: var(--fa-secondary-color, currentColor);\n  opacity: var(--fa-secondary-opacity, 0.4);\n}\n\n.svg-inline--fa.fa-swap-opacity .fa-primary {\n  opacity: var(--fa-secondary-opacity, 0.4);\n}\n\n.svg-inline--fa.fa-swap-opacity .fa-secondary {\n  opacity: var(--fa-primary-opacity, 1);\n}\n\n.svg-inline--fa mask .fa-primary,\n.svg-inline--fa mask .fa-secondary {\n  fill: black;\n}';if("fa"!==t||e!==D){let a=RegExp("\\.".concat("fa","\\-"),"g"),r=RegExp("\\--".concat("fa","\\-"),"g"),i=RegExp("\\.".concat(D),"g");n=n.replace(a,".".concat(t,"-")).replace(r,"--".concat(t,"-")).replace(i,".".concat(e))}return n}let th=!1;function tb(){to.autoAddCss&&!th&&(!function(t){if(!t||!g)return;let e=d.createElement("style");e.setAttribute("type","text/css"),e.innerHTML=t;let n=d.head.childNodes,a=null;for(let t=n.length-1;t>-1;t--){let e=n[t];["STYLE","LINK"].indexOf((e.tagName||"").toUpperCase())>-1&&(a=e)}d.head.insertBefore(e,a)}(tg()),th=!0)}let ty=u||{};ty[F]||(ty[F]={}),ty[F].styles||(ty[F].styles={}),ty[F].hooks||(ty[F].hooks={}),ty[F].shims||(ty[F].shims=[]);var tv=ty[F];let tx=[],tk=function(){d.removeEventListener("DOMContentLoaded",tk),tw=1,tx.map(t=>t())},tw=!1;function tA(t){let{tag:e,attributes:n={},children:a=[]}=t;return"string"==typeof t?td(t):"<".concat(e," ").concat(Object.keys(n||{}).reduce((t,e)=>t+"".concat(e,'="').concat(td(n[e]),'" '),"").trim(),">").concat(a.map(tA).join(""),"</").concat(e,">")}function tO(t,e,n){if(t&&t[e]&&t[e][n])return{prefix:e,iconName:n,icon:t[e][n]}}g&&((tw=(d.documentElement.doScroll?/^loaded|^c/:/^loaded|^i|^c/).test(d.readyState))||d.addEventListener("DOMContentLoaded",tk));var tP=function(t,e,n,a){var r,i,o,l=Object.keys(t),s=l.length,f=void 0!==a?function(t,n,r,i){return e.call(a,t,n,r,i)}:e;for(void 0===n?(r=1,o=t[l[0]]):(r=0,o=n);r<s;r++)o=f(o,t[i=l[r]],i,t);return o};function tS(t){let e=function(t){let e=[],n=0,a=t.length;for(;n<a;){let r=t.charCodeAt(n++);if(r>=55296&&r<=56319&&n<a){let a=t.charCodeAt(n++);(64512&a)==56320?e.push(((1023&r)<<10)+(1023&a)+65536):(e.push(r),n--)}else e.push(r)}return e}(t);return 1===e.length?e[0].toString(16):null}function tj(t){return Object.keys(t).reduce((e,n)=>{let a=t[n];return a.icon?e[a.iconName]=a.icon:e[n]=a,e},{})}function tN(t,e){let n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{},{skipHooks:a=!1}=n,i=tj(e);"function"!=typeof tv.hooks.addPack||a?tv.styles[t]=r(r({},tv.styles[t]||{}),i):tv.hooks.addPack(t,tj(e)),"fas"===t&&tN("fa",e)}let{styles:tC,shims:tE}=tv,tz=Object.keys(K),tI=tz.reduce((t,e)=>(t[e]=Object.keys(K[e]),t),{}),tM=null,tF={},tD={},tL={},tR={},tT={},tY=()=>{let t=t=>tP(tC,(e,n,a)=>(e[a]=tP(n,t,{}),e),{});tF=t((t,e,n)=>(e[3]&&(t[e[3]]=n),e[2]&&e[2].filter(t=>"number"==typeof t).forEach(e=>{t[e.toString(16)]=n}),t)),tD=t((t,e,n)=>(t[n]=n,e[2]&&e[2].filter(t=>"string"==typeof t).forEach(e=>{t[e]=n}),t)),tT=t((t,e,n)=>{let a=e[2];return t[n]=n,a.forEach(e=>{t[e]=n}),t});let e="far"in tC||to.autoFetchSvg,n=tP(tE,(t,n)=>{let a=n[0],r=n[1],i=n[2];return"far"!==r||e||(r="fas"),"string"==typeof a&&(t.names[a]={prefix:r,iconName:i}),"number"==typeof a&&(t.unicodes[a.toString(16)]={prefix:r,iconName:i}),t},{names:{},unicodes:{}});tL=n.names,tR=n.unicodes,tM=tB(to.styleDefault,{family:to.familyDefault})};function tW(t,e){return(tF[t]||{})[e]}function t_(t,e){return(tT[t]||{})[e]}function tU(t){return tL[t]||{prefix:null,iconName:null}}!function(t){tl.push(t),()=>{tl.splice(tl.indexOf(t),1)}}(t=>{tM=tB(t.styleDefault,{family:to.familyDefault})}),tY();let tH=()=>({prefix:null,iconName:null,rest:[]});function tB(t){let e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},{family:n=v}=e,a=X[n][t];if(n===x&&!t)return"fad";let r=V[n][t]||V[n][a],i=t in tv.styles?t:null;return r||i||null}function tX(t){return t.sort().filter((t,e,n)=>n.indexOf(t)===e)}function tq(t){let e,n,a=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},{skipLookups:i=!1}=a,o=null,l=E.concat(N),s=tX(t.filter(t=>l.includes(t))),f=tX(t.filter(t=>!E.includes(t))),[c=null]=s.filter(t=>(o=t,!y.includes(t))),u=function(t){let e=v,n=tz.reduce((t,e)=>(t[e]="".concat(to.cssPrefix,"-").concat(e),t),{});return k.forEach(a=>{(t.includes(n[a])||t.some(t=>tI[a].includes(t)))&&(e=a)}),e}(s),d=r(r({},(e=[],n=null,f.forEach(t=>{let a=function(t,e){let n=e.split("-"),a=n[0],r=n.slice(1).join("-");return a!==t||""===r||~tn.indexOf(r)?null:r}(to.cssPrefix,t);a?n=a:t&&e.push(t)}),{iconName:n,rest:e})),{},{prefix:tB(c,{family:u})});return r(r(r({},d),function(t){let{values:e,family:n,canonical:a,givenPrefix:r="",styles:i={},config:o={}}=t,l=n===x,s=e.includes("fa-duotone")||e.includes("fad"),f="duotone"===o.familyDefault,c="fad"===a.prefix||"fa-duotone"===a.prefix;return!l&&(s||f||c)&&(a.prefix="fad"),(e.includes("fa-brands")||e.includes("fab"))&&(a.prefix="fab"),!a.prefix&&tV.includes(n)&&(Object.keys(i).find(t=>tG.includes(t))||o.autoFetchSvg)&&(a.prefix=w.get(n).defaultShortPrefixId,a.iconName=t_(a.prefix,a.iconName)||a.iconName),("fa"===a.prefix||"fa"===r)&&(a.prefix=tM||"fas"),a}({values:t,family:u,styles:tC,config:to,canonical:d,givenPrefix:o})),function(t,e,n){let{prefix:a,iconName:r}=n;if(t||!a||!r)return{prefix:a,iconName:r};let i="fa"===e?tU(r):{},o=t_(a,r);return r=i.iconName||o||r,"far"!==(a=i.prefix||a)||tC.far||!tC.fas||to.autoFetchSvg||(a="fas"),{prefix:a,iconName:r}}(i,o,d))}let tV=k.filter(t=>t!==v||t!==x),tG=Object.keys(C).filter(t=>t!==v).map(t=>Object.keys(C[t])).flat();class tK{constructor(){this.definitions={}}add(){for(var t=arguments.length,e=Array(t),n=0;n<t;n++)e[n]=arguments[n];let a=e.reduce(this._pullDefinitions,{});Object.keys(a).forEach(t=>{this.definitions[t]=r(r({},this.definitions[t]||{}),a[t]),tN(t,a[t]);let e=K[v][t];e&&tN(e,a[t]),tY()})}reset(){this.definitions={}}_pullDefinitions(t,e){let n=e.prefix&&e.iconName&&e.icon?{0:e}:e;return Object.keys(n).map(e=>{let{prefix:a,iconName:r,icon:i}=n[e],o=i[2];t[a]||(t[a]={}),o.length>0&&o.forEach(e=>{"string"==typeof e&&(t[a][e]=i)}),t[a][r]=i}),t}}let t$=[],tJ={},tQ={},tZ=Object.keys(tQ);function t1(t,e){for(var n=arguments.length,a=Array(n>2?n-2:0),r=2;r<n;r++)a[r-2]=arguments[r];return(tJ[t]||[]).forEach(t=>{e=t.apply(null,[e,...a])}),e}function t0(t){for(var e=arguments.length,n=Array(e>1?e-1:0),a=1;a<e;a++)n[a-1]=arguments[a];(tJ[t]||[]).forEach(t=>{t.apply(null,n)})}function t2(){let t=arguments[0],e=Array.prototype.slice.call(arguments,1);return tQ[t]?tQ[t].apply(null,e):void 0}function t5(t){"fa"===t.prefix&&(t.prefix="fas");let{iconName:e}=t,n=t.prefix||tM;if(e)return e=t_(n,e)||e,tO(t4.definitions,n,e)||tO(tv.styles,n,e)}let t4=new tK,t6={noAuto:()=>{to.autoReplaceSvg=!1,to.observeMutations=!1,t0("noAuto")},config:to,dom:{i2svg:function(){let t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};return g?(t0("beforeI2svg",t),t2("pseudoElements2svg",t),t2("i2svg",t)):Promise.reject(Error("Operation requires a DOM of some kind."))},watch:function(){var t;let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},{autoReplaceSvgRoot:n}=e;!1===to.autoReplaceSvg&&(to.autoReplaceSvg=!0),to.observeMutations=!0,t=()=>{t3({autoReplaceSvgRoot:n}),t0("watch",e)},g&&(tw?setTimeout(t,0):tx.push(t))}},parse:{icon:t=>{if(null===t)return null;if("object"==typeof t&&t.prefix&&t.iconName)return{prefix:t.prefix,iconName:t_(t.prefix,t.iconName)||t.iconName};if(Array.isArray(t)&&2===t.length){let e=0===t[1].indexOf("fa-")?t[1].slice(3):t[1],n=tB(t[0]);return{prefix:n,iconName:t_(n,e)||e}}if("string"==typeof t&&(t.indexOf("".concat(to.cssPrefix,"-"))>-1||t.match(J))){let e=tq(t.split(" "),{skipLookups:!0});return{prefix:e.prefix||tM,iconName:t_(e.prefix,e.iconName)||e.iconName}}if("string"==typeof t){let e=tM;return{prefix:e,iconName:t_(e,t)||t}}}},library:t4,findIconDefinition:t5,toHtml:tA},t3=function(){let t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},{autoReplaceSvgRoot:e=d}=t;(Object.keys(tv.styles).length>0||to.autoFetchSvg)&&g&&to.autoReplaceSvg&&t6.dom.i2svg({node:e})};function t9(t,e){return Object.defineProperty(t,"abstract",{get:e}),Object.defineProperty(t,"html",{get:function(){return t.abstract.map(t=>tA(t))}}),Object.defineProperty(t,"node",{get:function(){if(!g)return;let e=d.createElement("div");return e.innerHTML=t.html,e.children}}),t}function t7(t){let{icons:{main:e,mask:n},prefix:a,iconName:i,transform:o,symbol:l,title:s,maskId:f,titleId:c,extra:u,watchable:d=!1}=t,{width:m,height:p}=n.found?n:e,g=P.includes(a),h=[to.replacementClass,i?"".concat(to.cssPrefix,"-").concat(i):""].filter(t=>-1===u.classes.indexOf(t)).filter(t=>""!==t||!!t).concat(u.classes).join(" "),b={children:[],attributes:r(r({},u.attributes),{},{"data-prefix":a,"data-icon":i,class:h,role:u.attributes.role||"img",xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 ".concat(m," ").concat(p)})},y=g&&!~u.classes.indexOf("fa-fw")?{width:"".concat(m/p*1,"em")}:{};d&&(b.attributes[L]=""),s&&(b.children.push({tag:"title",attributes:{id:b.attributes["aria-labelledby"]||"title-".concat(c||tf())},children:[s]}),delete b.attributes.title);let v=r(r({},b),{},{prefix:a,iconName:i,main:e,mask:n,maskId:f,transform:o,symbol:l,styles:r(r({},y),u.styles)}),{children:x,attributes:k}=n.found&&e.found?t2("generateAbstractMask",v)||{children:[],attributes:{}}:t2("generateAbstractIcon",v)||{children:[],attributes:{}};return(v.children=x,v.attributes=k,l)?function(t){let{prefix:e,iconName:n,children:a,attributes:i,symbol:o}=t,l=!0===o?"".concat(e,"-").concat(to.cssPrefix,"-").concat(n):o;return[{tag:"svg",attributes:{style:"display: none;"},children:[{tag:"symbol",attributes:r(r({},i),{},{id:l}),children:a}]}]}(v):function(t){let{children:e,main:n,mask:a,attributes:i,styles:o,transform:l}=t;if(tp(l)&&n.found&&!a.found){let{width:t,height:e}=n,a={x:t/e/2,y:.5};i.style=tm(r(r({},o),{},{"transform-origin":"".concat(a.x+l.x/16,"em ").concat(a.y+l.y/16,"em")}))}return[{tag:"svg",attributes:i,children:e}]}(v)}function t8(t){let{content:e,width:n,height:a,transform:i,title:o,extra:l,watchable:s=!1}=t,f=r(r(r({},l.attributes),o?{title:o}:{}),{},{class:l.classes.join(" ")});s&&(f[L]="");let c=r({},l.styles);tp(i)&&(c.transform=function(t){let{transform:e,width:n=16,height:a=16,startCentered:r=!1}=t,i="";return r&&h?i+="translate(".concat(e.x/16-n/2,"em, ").concat(e.y/16-a/2,"em) "):r?i+="translate(calc(-50% + ".concat(e.x/16,"em), calc(-50% + ").concat(e.y/16,"em)) "):i+="translate(".concat(e.x/16,"em, ").concat(e.y/16,"em) "),i+="scale(".concat(e.size/16*(e.flipX?-1:1),", ").concat(e.size/16*(e.flipY?-1:1),") "),i+="rotate(".concat(e.rotate,"deg) ")}({transform:i,startCentered:!0,width:n,height:a}),c["-webkit-transform"]=c.transform);let u=tm(c);u.length>0&&(f.style=u);let d=[];return d.push({tag:"span",attributes:f,children:[e]}),o&&d.push({tag:"span",attributes:{class:"sr-only"},children:[o]}),d}let{styles:et}=tv;function ee(t){let e=t[0],n=t[1],[a]=t.slice(4),r=null;return{found:!0,width:e,height:n,icon:Array.isArray(a)?{tag:"g",attributes:{class:"".concat(to.cssPrefix,"-").concat(te.GROUP)},children:[{tag:"path",attributes:{class:"".concat(to.cssPrefix,"-").concat(te.SECONDARY),fill:"currentColor",d:a[0]}},{tag:"path",attributes:{class:"".concat(to.cssPrefix,"-").concat(te.PRIMARY),fill:"currentColor",d:a[1]}}]}:{tag:"path",attributes:{fill:"currentColor",d:a}}}}let en={found:!1,width:512,height:512};function ea(t,e){let n=e;return"fa"===e&&null!==to.styleDefault&&(e=tM),new Promise((a,i)=>{var o,l;if("fa"===n){let n=tU(t)||{};t=n.iconName||t,e=n.prefix||e}if(t&&e&&et[e]&&et[e][t])return a(ee(et[e][t]));o=t,l=e,U||to.showMissingIcons||!o||console.error('Icon with name "'.concat(o,'" and prefix "').concat(l,'" is missing.')),a(r(r({},en),{},{icon:to.showMissingIcons&&t&&t2("missingIconAbstract")||{}}))})}let er=()=>{},ei=to.measurePerformance&&p&&p.mark&&p.measure?p:{mark:er,measure:er},eo='FA "6.7.2"',el=t=>{ei.mark("".concat(eo," ").concat(t," ends")),ei.measure("".concat(eo," ").concat(t),"".concat(eo," ").concat(t," begins"),"".concat(eo," ").concat(t," ends"))};var es={begin:t=>(ei.mark("".concat(eo," ").concat(t," begins")),()=>el(t))};let ef=()=>{};function ec(t){return"string"==typeof(t.getAttribute?t.getAttribute(L):null)}function eu(t){return d.createElementNS("http://www.w3.org/2000/svg",t)}function ed(t){return d.createElement(t)}let em={replace:function(t){let e=t[0];if(e.parentNode)if(t[1].forEach(t=>{e.parentNode.insertBefore(function t(e){let n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},{ceFn:a="svg"===e.tag?eu:ed}=n;if("string"==typeof e)return d.createTextNode(e);let r=a(e.tag);return Object.keys(e.attributes||[]).forEach(function(t){r.setAttribute(t,e.attributes[t])}),(e.children||[]).forEach(function(e){r.appendChild(t(e,{ceFn:a}))}),r}(t),e)}),null===e.getAttribute(L)&&to.keepOriginalSource){let t,n=d.createComment((t=" ".concat(e.outerHTML," "),t="".concat(t,"Font Awesome fontawesome.com ")));e.parentNode.replaceChild(n,e)}else e.remove()},nest:function(t){let e=t[0],n=t[1];if(~tu(e).indexOf(to.replacementClass))return em.replace(t);let a=new RegExp("".concat(to.cssPrefix,"-.*"));if(delete n[0].attributes.id,n[0].attributes.class){let t=n[0].attributes.class.split(" ").reduce((t,e)=>(e===to.replacementClass||e.match(a)?t.toSvg.push(e):t.toNode.push(e),t),{toNode:[],toSvg:[]});n[0].attributes.class=t.toSvg.join(" "),0===t.toNode.length?e.removeAttribute("class"):e.setAttribute("class",t.toNode.join(" "))}let r=n.map(t=>tA(t)).join("\n");e.setAttribute(L,""),e.innerHTML=r}};function ep(t){t()}function eg(t,e){let n="function"==typeof e?e:ef;if(0===t.length)n();else{let e=ep;"async"===to.mutateApproach&&(e=u.requestAnimationFrame||ep),e(()=>{let e=!0===to.autoReplaceSvg?em.replace:em[to.autoReplaceSvg]||em.replace,a=es.begin("mutate");t.map(e),a(),n()})}}let eh=!1,eb=null;function ey(t){if(!m||!to.observeMutations)return;let{treeCallback:e=ef,nodeCallback:n=ef,pseudoElementsCallback:a=ef,observeMutationsRoot:r=d}=t;eb=new m(t=>{if(eh)return;let r=tM;tc(t).forEach(t=>{if("childList"===t.type&&t.addedNodes.length>0&&!ec(t.addedNodes[0])&&(to.searchPseudoElements&&a(t.target),e(t.target)),"attributes"===t.type&&t.target.parentNode&&to.searchPseudoElements&&a(t.target.parentNode),"attributes"===t.type&&ec(t.target)&&~tt.indexOf(t.attributeName))if("class"===t.attributeName&&function(t){let e=t.getAttribute?t.getAttribute(T):null,n=t.getAttribute?t.getAttribute(Y):null;return e&&n}(t.target)){let{prefix:e,iconName:n}=tq(tu(t.target));t.target.setAttribute(T,e||r),n&&t.target.setAttribute(Y,n)}else{var i;(i=t.target)&&i.classList&&i.classList.contains&&i.classList.contains(to.replacementClass)&&n(t.target)}})}),g&&eb.observe(r,{childList:!0,attributes:!0,characterData:!0,subtree:!0})}function ev(t){let e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{styleParser:!0},{iconName:n,prefix:a,rest:i}=function(t){var e,n;let a=t.getAttribute("data-prefix"),r=t.getAttribute("data-icon"),i=void 0!==t.innerText?t.innerText.trim():"",o=tq(tu(t));return(o.prefix||(o.prefix=tM),a&&r&&(o.prefix=a,o.iconName=r),o.iconName&&o.prefix)?o:(o.prefix&&i.length>0&&(e=o.prefix,n=t.innerText,o.iconName=(tD[e]||{})[n]||tW(o.prefix,tS(t.innerText))),!o.iconName&&to.autoFetchSvg&&t.firstChild&&t.firstChild.nodeType===Node.TEXT_NODE&&(o.iconName=t.firstChild.data),o)}(t),o=function(t){let e=tc(t.attributes).reduce((t,e)=>("class"!==t.name&&"style"!==t.name&&(t[e.name]=e.value),t),{}),n=t.getAttribute("title"),a=t.getAttribute("data-fa-title-id");return to.autoA11y&&(n?e["aria-labelledby"]="".concat(to.replacementClass,"-title-").concat(a||tf()):(e["aria-hidden"]="true",e.focusable="false")),e}(t),l=t1("parseNodeAttributes",{},t),s=e.styleParser?function(t){let e=t.getAttribute("style"),n=[];return e&&(n=e.split(";").reduce((t,e)=>{let n=e.split(":"),a=n[0],r=n.slice(1);return a&&r.length>0&&(t[a]=r.join(":").trim()),t},{})),n}(t):[];return r({iconName:n,title:t.getAttribute("title"),titleId:t.getAttribute("data-fa-title-id"),prefix:a,transform:ts,mask:{iconName:null,prefix:null,rest:[]},maskId:null,symbol:!1,extra:{classes:i,styles:s,attributes:o}},l)}let{styles:ex}=tv;function ek(t){let e="nest"===to.autoReplaceSvg?ev(t,{styleParser:!1}):ev(t);return~e.extra.classes.indexOf(Q)?t2("generateLayersText",t,e):t2("generateSvgReplacementMutation",t,e)}function ew(t){let e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null;if(!g)return Promise.resolve();let n=d.documentElement.classList,a=t=>n.add("".concat(W,"-").concat(t)),r=t=>n.remove("".concat(W,"-").concat(t)),i=to.autoFetchSvg?[...A,...E]:y.concat(Object.keys(ex));i.includes("fa")||i.push("fa");let o=[".".concat(Q,":not([").concat(L,"])")].concat(i.map(t=>".".concat(t,":not([").concat(L,"])"))).join(", ");if(0===o.length)return Promise.resolve();let l=[];try{l=tc(t.querySelectorAll(o))}catch(t){}if(!(l.length>0))return Promise.resolve();a("pending"),r("complete");let s=es.begin("onTree"),f=l.reduce((t,e)=>{try{let n=ek(e);n&&t.push(n)}catch(t){U||"MissingIcon"!==t.name||console.error(t)}return t},[]);return new Promise((t,n)=>{Promise.all(f).then(n=>{eg(n,()=>{a("active"),a("complete"),r("pending"),"function"==typeof e&&e(),s(),t()})}).catch(t=>{s(),n(t)})})}function eA(t){let e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null;ek(t).then(t=>{t&&eg([t],e)})}let eO=function(t){let e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},{transform:n=ts,symbol:a=!1,mask:i=null,maskId:o=null,title:l=null,titleId:s=null,classes:f=[],attributes:c={},styles:u={}}=e;if(!t)return;let{prefix:d,iconName:m,icon:p}=t;return t9(r({type:"icon"},t),()=>(t0("beforeDOMElementCreation",{iconDefinition:t,params:e}),to.autoA11y&&(l?c["aria-labelledby"]="".concat(to.replacementClass,"-title-").concat(s||tf()):(c["aria-hidden"]="true",c.focusable="false")),t7({icons:{main:ee(p),mask:i?ee(i.icon):{found:!1,width:null,height:null,icon:{}}},prefix:d,iconName:m,transform:r(r({},ts),n),symbol:a,title:l,maskId:o,titleId:s,extra:{attributes:c,styles:u,classes:f}})))},eP=RegExp('"',"ug"),eS=r(r(r(r({},{FontAwesome:{normal:"fas",400:"fas"}}),{"Font Awesome 6 Free":{900:"fas",400:"far"},"Font Awesome 6 Pro":{900:"fas",400:"far",normal:"far",300:"fal",100:"fat"},"Font Awesome 6 Brands":{400:"fab",normal:"fab"},"Font Awesome 6 Duotone":{900:"fad",400:"fadr",normal:"fadr",300:"fadl",100:"fadt"},"Font Awesome 6 Sharp":{900:"fass",400:"fasr",normal:"fasr",300:"fasl",100:"fast"},"Font Awesome 6 Sharp Duotone":{900:"fasds",400:"fasdr",normal:"fasdr",300:"fasdl",100:"fasdt"}}),{"Font Awesome 5 Free":{900:"fas",400:"far"},"Font Awesome 5 Pro":{900:"fas",400:"far",normal:"far",300:"fal"},"Font Awesome 5 Brands":{400:"fab",normal:"fab"},"Font Awesome 5 Duotone":{900:"fad"}}),{"Font Awesome Kit":{400:"fak",normal:"fak"},"Font Awesome Kit Duotone":{400:"fakd",normal:"fakd"}}),ej=Object.keys(eS).reduce((t,e)=>(t[e.toLowerCase()]=eS[e],t),{}),eN=Object.keys(ej).reduce((t,e)=>{let n=ej[e];return t[e]=n[900]||[...Object.entries(n)][0][1],t},{});function eC(t,e){let n="".concat("data-fa-pseudo-element-pending").concat(e.replace(":","-"));return new Promise((a,i)=>{if(null!==t.getAttribute(n))return a();let o=tc(t.children).filter(t=>t.getAttribute(R)===e)[0],l=u.getComputedStyle(t,e),s=l.getPropertyValue("font-family"),f=s.match(Z),c=l.getPropertyValue("font-weight"),m=l.getPropertyValue("content");if(o&&!f)return t.removeChild(o),a();if(f&&"none"!==m&&""!==m){let u=l.getPropertyValue("content"),m=function(t,e){let n=t.replace(/^['"]|['"]$/g,"").toLowerCase(),a=parseInt(e),r=isNaN(a)?"normal":a;return(ej[n]||{})[r]||eN[n]}(s,c),{value:p,isSecondary:g}=function(t){let e=t.replace(eP,""),n=function(t,e){let n,a=t.length,r=t.charCodeAt(0);return r>=55296&&r<=56319&&a>1&&(n=t.charCodeAt(e+1))>=56320&&n<=57343?(r-55296)*1024+n-56320+65536:r}(e,0),a=2===e.length&&e[0]===e[1];return{value:a?tS(e[0]):tS(e),isSecondary:n>=1105920&&n<=1112319||a}}(u),h=f[0].startsWith("FontAwesome"),b=tW(m,p),y=b;if(h){let t=function(t){let e=tR[t],n=tW("fas",t);return e||(n?{prefix:"fas",iconName:n}:null)||{prefix:null,iconName:null}}(p);t.iconName&&t.prefix&&(b=t.iconName,m=t.prefix)}if(!b||g||o&&o.getAttribute(T)===m&&o.getAttribute(Y)===y)a();else{t.setAttribute(n,y),o&&t.removeChild(o);let l={iconName:null,title:null,titleId:null,prefix:null,transform:ts,symbol:!1,mask:{iconName:null,prefix:null,rest:[]},maskId:null,extra:{classes:[],styles:{},attributes:{}}},{extra:s}=l;s.attributes[R]=e,ea(b,m).then(i=>{let o=t7(r(r({},l),{},{icons:{main:i,mask:tH()},prefix:m,iconName:y,extra:s,watchable:!0})),f=d.createElementNS("http://www.w3.org/2000/svg","svg");"::before"===e?t.insertBefore(f,t.firstChild):t.appendChild(f),f.outerHTML=o.map(t=>tA(t)).join("\n"),t.removeAttribute(n),a()}).catch(i)}}else a()})}function eE(t){return Promise.all([eC(t,"::before"),eC(t,"::after")])}function ez(t){return t.parentNode!==document.head&&!~_.indexOf(t.tagName.toUpperCase())&&!t.getAttribute(R)&&(!t.parentNode||"svg"!==t.parentNode.tagName)}function eI(t){if(g)return new Promise((e,n)=>{let a=tc(t.querySelectorAll("*")).filter(ez).map(eE),r=es.begin("searchPseudoElements");eh=!0,Promise.all(a).then(()=>{r(),eh=!1,e()}).catch(()=>{r(),eh=!1,n()})})}let eM=!1,eF=t=>t.toLowerCase().split(" ").reduce((t,e)=>{let n=e.toLowerCase().split("-"),a=n[0],r=n.slice(1).join("-");if(a&&"h"===r)return t.flipX=!0,t;if(a&&"v"===r)return t.flipY=!0,t;if(isNaN(r=parseFloat(r)))return t;switch(a){case"grow":t.size=t.size+r;break;case"shrink":t.size=t.size-r;break;case"left":t.x=t.x-r;break;case"right":t.x=t.x+r;break;case"up":t.y=t.y-r;break;case"down":t.y=t.y+r;break;case"rotate":t.rotate=t.rotate+r}return t},{size:16,x:0,y:0,flipX:!1,flipY:!1,rotate:0}),eD={x:0,y:0,width:"100%",height:"100%"};function eL(t){let e=!(arguments.length>1)||void 0===arguments[1]||arguments[1];return t.attributes&&(t.attributes.fill||e)&&(t.attributes.fill="black"),t}!function(t,e){let{mixoutsTo:n}=e;t$=t,tJ={},Object.keys(tQ).forEach(t=>{-1===tZ.indexOf(t)&&delete tQ[t]}),t$.forEach(t=>{let e=t.mixout?t.mixout():{};if(Object.keys(e).forEach(t=>{"function"==typeof e[t]&&(n[t]=e[t]),"object"==typeof e[t]&&Object.keys(e[t]).forEach(a=>{n[t]||(n[t]={}),n[t][a]=e[t][a]})}),t.hooks){let e=t.hooks();Object.keys(e).forEach(t=>{tJ[t]||(tJ[t]=[]),tJ[t].push(e[t])})}t.provides&&t.provides(tQ)})}([{mixout:()=>({dom:{css:tg,insertCss:tb}}),hooks:()=>({beforeDOMElementCreation(){tb()},beforeI2svg(){tb()}})},{mixout:()=>({icon:function(t){let e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=(t||{}).icon?t:t5(t||{}),{mask:a}=e;return a&&(a=(a||{}).icon?a:t5(a||{})),eO(n,r(r({},e),{},{mask:a}))}}),hooks:()=>({mutationObserverCallbacks:t=>(t.treeCallback=ew,t.nodeCallback=eA,t)}),provides(t){t.i2svg=function(t){let{node:e=d,callback:n=()=>{}}=t;return ew(e,n)},t.generateSvgReplacementMutation=function(t,e){let{iconName:n,title:a,titleId:r,prefix:i,transform:o,symbol:l,mask:s,maskId:f,extra:c}=e;return new Promise((e,u)=>{Promise.all([ea(n,i),s.iconName?ea(s.iconName,s.prefix):Promise.resolve({found:!1,width:512,height:512,icon:{}})]).then(s=>{let[u,d]=s;e([t,t7({icons:{main:u,mask:d},prefix:i,iconName:n,transform:o,symbol:l,maskId:f,title:a,titleId:r,extra:c,watchable:!0})])}).catch(u)})},t.generateAbstractIcon=function(t){let e,{children:n,attributes:a,main:r,transform:i,styles:o}=t,l=tm(o);return l.length>0&&(a.style=l),tp(i)&&(e=t2("generateAbstractTransformGrouping",{main:r,transform:i,containerWidth:r.width,iconWidth:r.width})),n.push(e||r.icon),{children:n,attributes:a}}}},{mixout:()=>({layer(t){let e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},{classes:n=[]}=e;return t9({type:"layer"},()=>{t0("beforeDOMElementCreation",{assembler:t,params:e});let a=[];return t(t=>{Array.isArray(t)?t.map(t=>{a=a.concat(t.abstract)}):a=a.concat(t.abstract)}),[{tag:"span",attributes:{class:["".concat(to.cssPrefix,"-layers"),...n].join(" ")},children:a}]})}})},{mixout:()=>({counter(t){let e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},{title:n=null,classes:a=[],attributes:i={},styles:o={}}=e;return t9({type:"counter",content:t},()=>(t0("beforeDOMElementCreation",{content:t,params:e}),function(t){let{content:e,title:n,extra:a}=t,i=r(r(r({},a.attributes),n?{title:n}:{}),{},{class:a.classes.join(" ")}),o=tm(a.styles);o.length>0&&(i.style=o);let l=[];return l.push({tag:"span",attributes:i,children:[e]}),n&&l.push({tag:"span",attributes:{class:"sr-only"},children:[n]}),l}({content:t.toString(),title:n,extra:{attributes:i,styles:o,classes:["".concat(to.cssPrefix,"-layers-counter"),...a]}})))}})},{mixout:()=>({text(t){let e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},{transform:n=ts,title:a=null,classes:i=[],attributes:o={},styles:l={}}=e;return t9({type:"text",content:t},()=>(t0("beforeDOMElementCreation",{content:t,params:e}),t8({content:t,transform:r(r({},ts),n),title:a,extra:{attributes:o,styles:l,classes:["".concat(to.cssPrefix,"-layers-text"),...i]}})))}}),provides(t){t.generateLayersText=function(t,e){let{title:n,transform:a,extra:r}=e,i=null,o=null;if(h){let e=parseInt(getComputedStyle(t).fontSize,10),n=t.getBoundingClientRect();i=n.width/e,o=n.height/e}return to.autoA11y&&!n&&(r.attributes["aria-hidden"]="true"),Promise.resolve([t,t8({content:t.innerHTML,width:i,height:o,transform:a,title:n,extra:r,watchable:!0})])}}},{hooks:()=>({mutationObserverCallbacks:t=>(t.pseudoElementsCallback=eI,t)}),provides(t){t.pseudoElements2svg=function(t){let{node:e=d}=t;to.searchPseudoElements&&eI(e)}}},{mixout:()=>({dom:{unwatch(){eh=!0,eM=!0}}}),hooks:()=>({bootstrap(){ey(t1("mutationObserverCallbacks",{}))},noAuto(){eb&&eb.disconnect()},watch(t){let{observeMutationsRoot:e}=t;eM?eh=!1:ey(t1("mutationObserverCallbacks",{observeMutationsRoot:e}))}})},{mixout:()=>({parse:{transform:t=>eF(t)}}),hooks:()=>({parseNodeAttributes(t,e){let n=e.getAttribute("data-fa-transform");return n&&(t.transform=eF(n)),t}}),provides(t){t.generateAbstractTransformGrouping=function(t){let{main:e,transform:n,containerWidth:a,iconWidth:i}=t,o="translate(".concat(32*n.x,", ").concat(32*n.y,") "),l="scale(".concat(n.size/16*(n.flipX?-1:1),", ").concat(n.size/16*(n.flipY?-1:1),") "),s="rotate(".concat(n.rotate," 0 0)"),f={transform:"".concat(o," ").concat(l," ").concat(s)},c={outer:{transform:"translate(".concat(a/2," 256)")},inner:f,path:{transform:"translate(".concat(-(i/2*1)," -256)")}};return{tag:"g",attributes:r({},c.outer),children:[{tag:"g",attributes:r({},c.inner),children:[{tag:e.icon.tag,children:e.icon.children,attributes:r(r({},e.icon.attributes),c.path)}]}]}}}},{hooks:()=>({parseNodeAttributes(t,e){let n=e.getAttribute("data-fa-mask"),a=n?tq(n.split(" ").map(t=>t.trim())):tH();return a.prefix||(a.prefix=tM),t.mask=a,t.maskId=e.getAttribute("data-fa-mask-id"),t}}),provides(t){t.generateAbstractMask=function(t){let{children:e,attributes:n,main:a,mask:i,maskId:o,transform:l}=t,{width:s,icon:f}=a,{width:c,icon:u}=i,d=function(t){let{transform:e,containerWidth:n,iconWidth:a}=t,r="translate(".concat(32*e.x,", ").concat(32*e.y,") "),i="scale(".concat(e.size/16*(e.flipX?-1:1),", ").concat(e.size/16*(e.flipY?-1:1),") "),o="rotate(".concat(e.rotate," 0 0)"),l={transform:"".concat(r," ").concat(i," ").concat(o)};return{outer:{transform:"translate(".concat(n/2," 256)")},inner:l,path:{transform:"translate(".concat(-(a/2*1)," -256)")}}}({transform:l,containerWidth:c,iconWidth:s}),m={tag:"rect",attributes:r(r({},eD),{},{fill:"white"})},p=f.children?{children:f.children.map(eL)}:{},g={tag:"g",attributes:r({},d.inner),children:[eL(r({tag:f.tag,attributes:r(r({},f.attributes),d.path)},p))]},h={tag:"g",attributes:r({},d.outer),children:[g]},b="mask-".concat(o||tf()),y="clip-".concat(o||tf()),v={tag:"mask",attributes:r(r({},eD),{},{id:b,maskUnits:"userSpaceOnUse",maskContentUnits:"userSpaceOnUse"}),children:[m,h]},x={tag:"defs",children:[{tag:"clipPath",attributes:{id:y},children:"g"===u.tag?u.children:[u]},v]};return e.push(x,{tag:"rect",attributes:r({fill:"currentColor","clip-path":"url(#".concat(y,")"),mask:"url(#".concat(b,")")},eD)}),{children:e,attributes:n}}}},{provides(t){let e=!1;u.matchMedia&&(e=u.matchMedia("(prefers-reduced-motion: reduce)").matches),t.missingIconAbstract=function(){let t=[],n={fill:"currentColor"},a={attributeType:"XML",repeatCount:"indefinite",dur:"2s"};t.push({tag:"path",attributes:r(r({},n),{},{d:"M156.5,447.7l-12.6,29.5c-18.7-9.5-35.9-21.2-51.5-34.9l22.7-22.7C127.6,430.5,141.5,440,156.5,447.7z M40.6,272H8.5 c1.4,21.2,5.4,41.7,11.7,61.1L50,321.2C45.1,305.5,41.8,289,40.6,272z M40.6,240c1.4-18.8,5.2-37,11.1-54.1l-29.5-12.6 C14.7,194.3,10,216.7,8.5,240H40.6z M64.3,156.5c7.8-14.9,17.2-28.8,28.1-41.5L69.7,92.3c-13.7,15.6-25.5,32.8-34.9,51.5 L64.3,156.5z M397,419.6c-13.9,12-29.4,22.3-46.1,30.4l11.9,29.8c20.7-9.9,39.8-22.6,56.9-37.6L397,419.6z M115,92.4 c13.9-12,29.4-22.3,46.1-30.4l-11.9-29.8c-20.7,9.9-39.8,22.6-56.8,37.6L115,92.4z M447.7,355.5c-7.8,14.9-17.2,28.8-28.1,41.5 l22.7,22.7c13.7-15.6,25.5-32.9,34.9-51.5L447.7,355.5z M471.4,272c-1.4,18.8-5.2,37-11.1,54.1l29.5,12.6 c7.5-21.1,12.2-43.5,13.6-66.8H471.4z M321.2,462c-15.7,5-32.2,8.2-49.2,9.4v32.1c21.2-1.4,41.7-5.4,61.1-11.7L321.2,462z M240,471.4c-18.8-1.4-37-5.2-54.1-11.1l-12.6,29.5c21.1,7.5,43.5,12.2,66.8,13.6V471.4z M462,190.8c5,15.7,8.2,32.2,9.4,49.2h32.1 c-1.4-21.2-5.4-41.7-11.7-61.1L462,190.8z M92.4,397c-12-13.9-22.3-29.4-30.4-46.1l-29.8,11.9c9.9,20.7,22.6,39.8,37.6,56.9 L92.4,397z M272,40.6c18.8,1.4,36.9,5.2,54.1,11.1l12.6-29.5C317.7,14.7,295.3,10,272,8.5V40.6z M190.8,50 c15.7-5,32.2-8.2,49.2-9.4V8.5c-21.2,1.4-41.7,5.4-61.1,11.7L190.8,50z M442.3,92.3L419.6,115c12,13.9,22.3,29.4,30.5,46.1 l29.8-11.9C470,128.5,457.3,109.4,442.3,92.3z M397,92.4l22.7-22.7c-15.6-13.7-32.8-25.5-51.5-34.9l-12.6,29.5 C370.4,72.1,384.4,81.5,397,92.4z"})});let i=r(r({},a),{},{attributeName:"opacity"}),o={tag:"circle",attributes:r(r({},n),{},{cx:"256",cy:"364",r:"28"}),children:[]};return e||o.children.push({tag:"animate",attributes:r(r({},a),{},{attributeName:"r",values:"28;14;28;28;14;28;"})},{tag:"animate",attributes:r(r({},i),{},{values:"1;0;1;1;0;1;"})}),t.push(o),t.push({tag:"path",attributes:r(r({},n),{},{opacity:"1",d:"M263.7,312h-16c-6.6,0-12-5.4-12-12c0-71,77.4-63.9,77.4-107.8c0-20-17.8-40.2-57.4-40.2c-29.1,0-44.3,9.6-59.2,28.7 c-3.9,5-11.1,6-16.2,2.4l-13.1-9.2c-5.6-3.9-6.9-11.8-2.6-17.2c21.2-27.2,46.4-44.7,91.2-44.7c52.3,0,97.4,29.8,97.4,80.2 c0,67.6-77.4,63.5-77.4,107.8C275.7,306.6,270.3,312,263.7,312z"}),children:e?[]:[{tag:"animate",attributes:r(r({},i),{},{values:"1;0;0;0;0;1;"})}]}),e||t.push({tag:"path",attributes:r(r({},n),{},{opacity:"0",d:"M232.5,134.5l7,168c0.3,6.4,5.6,11.5,12,11.5h9c6.4,0,11.7-5.1,12-11.5l7-168c0.3-6.8-5.2-12.5-12-12.5h-23 C237.7,122,232.2,127.7,232.5,134.5z"}),children:[{tag:"animate",attributes:r(r({},i),{},{values:"0;0;1;1;0;0;"})}]}),{tag:"g",attributes:{class:"missing"},children:t}}}},{hooks:()=>({parseNodeAttributes(t,e){let n=e.getAttribute("data-fa-symbol");return t.symbol=null!==n&&(""===n||n),t}})}],{mixoutsTo:t6}),t6.noAuto,t6.config,t6.library,t6.dom;let eR=t6.parse;t6.findIconDefinition,t6.toHtml;let eT=t6.icon;t6.layer,t6.text,t6.counter;var eY=n(95062),eW=n.n(eY),e_=n(14232);function eU(t,e){var n=Object.keys(t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(t);e&&(a=a.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),n.push.apply(n,a)}return n}function eH(t){for(var e=1;e<arguments.length;e++){var n=null!=arguments[e]?arguments[e]:{};e%2?eU(Object(n),!0).forEach(function(e){eX(t,e,n[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(n)):eU(Object(n)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(n,e))})}return t}function eB(t){return(eB="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function eX(t,e,n){return e in t?Object.defineProperty(t,e,{value:n,enumerable:!0,configurable:!0,writable:!0}):t[e]=n,t}function eq(t){return function(t){if(Array.isArray(t))return eV(t)}(t)||function(t){if("undefined"!=typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)}(t)||function(t,e){if(t){if("string"==typeof t)return eV(t,void 0);var n=Object.prototype.toString.call(t).slice(8,-1);if("Object"===n&&t.constructor&&(n=t.constructor.name),"Map"===n||"Set"===n)return Array.from(t);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return eV(t,e)}}(t)||function(){throw TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function eV(t,e){(null==e||e>t.length)&&(e=t.length);for(var n=0,a=Array(e);n<e;n++)a[n]=t[n];return a}function eG(t){var e;return(e=t-0)==e?t:(t=t.replace(/[\-_\s]+(.)?/g,function(t,e){return e?e.toUpperCase():""})).substr(0,1).toLowerCase()+t.substr(1)}var eK=["style"],e$=!1;try{e$=!0}catch(t){}function eJ(t){return t&&"object"===eB(t)&&t.prefix&&t.iconName&&t.icon?t:eR.icon?eR.icon(t):null===t?null:t&&"object"===eB(t)&&t.prefix&&t.iconName?t:Array.isArray(t)&&2===t.length?{prefix:t[0],iconName:t[1]}:"string"==typeof t?{prefix:"fas",iconName:t}:void 0}function eQ(t,e){return Array.isArray(e)&&e.length>0||!Array.isArray(e)&&e?eX({},t,e):{}}var eZ={border:!1,className:"",mask:null,maskId:null,fixedWidth:!1,inverse:!1,flip:!1,icon:null,listItem:!1,pull:null,pulse:!1,rotation:null,size:null,spin:!1,spinPulse:!1,spinReverse:!1,beat:!1,fade:!1,beatFade:!1,bounce:!1,shake:!1,symbol:!1,title:"",titleId:null,transform:null,swapOpacity:!1},e1=e_.forwardRef(function(t,e){var n,a,r,i,o,l,s,f,c,u,d,m,p,g,h,b,y,v,x,k,w=eH(eH({},eZ),t),A=w.icon,O=w.mask,P=w.symbol,S=w.className,j=w.title,N=w.titleId,C=w.maskId,E=eJ(A),z=eQ("classes",[].concat(eq((a=w.beat,r=w.fade,i=w.beatFade,o=w.bounce,l=w.shake,s=w.flash,f=w.spin,c=w.spinPulse,u=w.spinReverse,d=w.pulse,m=w.fixedWidth,p=w.inverse,g=w.border,h=w.listItem,b=w.flip,y=w.size,v=w.rotation,x=w.pull,Object.keys((eX(n={"fa-beat":a,"fa-fade":r,"fa-beat-fade":i,"fa-bounce":o,"fa-shake":l,"fa-flash":s,"fa-spin":f,"fa-spin-reverse":u,"fa-spin-pulse":c,"fa-pulse":d,"fa-fw":m,"fa-inverse":p,"fa-border":g,"fa-li":h,"fa-flip":!0===b,"fa-flip-horizontal":"horizontal"===b||"both"===b,"fa-flip-vertical":"vertical"===b||"both"===b},"fa-".concat(y),null!=y),eX(n,"fa-rotate-".concat(v),null!=v&&0!==v),eX(n,"fa-pull-".concat(x),null!=x),eX(n,"fa-swap-opacity",w.swapOpacity),k=n)).map(function(t){return k[t]?t:null}).filter(function(t){return t}))),eq((S||"").split(" ")))),I=eQ("transform","string"==typeof w.transform?eR.transform(w.transform):w.transform),M=eQ("mask",eJ(O)),F=eT(E,eH(eH(eH(eH({},z),I),M),{},{symbol:P,title:j,titleId:N,maskId:C}));if(!F)return!function(){if(!e$&&console&&"function"==typeof console.error){var t;(t=console).error.apply(t,arguments)}}("Could not find icon",E),null;var D=F.abstract,L={ref:e};return Object.keys(w).forEach(function(t){eZ.hasOwnProperty(t)||(L[t]=w[t])}),e0(D[0],L)});e1.displayName="FontAwesomeIcon",e1.propTypes={beat:eW().bool,border:eW().bool,beatFade:eW().bool,bounce:eW().bool,className:eW().string,fade:eW().bool,flash:eW().bool,mask:eW().oneOfType([eW().object,eW().array,eW().string]),maskId:eW().string,fixedWidth:eW().bool,inverse:eW().bool,flip:eW().oneOf([!0,!1,"horizontal","vertical","both"]),icon:eW().oneOfType([eW().object,eW().array,eW().string]),listItem:eW().bool,pull:eW().oneOf(["right","left"]),pulse:eW().bool,rotation:eW().oneOf([0,90,180,270]),shake:eW().bool,size:eW().oneOf(["2xs","xs","sm","lg","xl","2xl","1x","2x","3x","4x","5x","6x","7x","8x","9x","10x"]),spin:eW().bool,spinPulse:eW().bool,spinReverse:eW().bool,symbol:eW().oneOfType([eW().bool,eW().string]),title:eW().string,titleId:eW().string,transform:eW().oneOfType([eW().string,eW().object]),swapOpacity:eW().bool};var e0=(function t(e,n){var a=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};if("string"==typeof n)return n;var r=(n.children||[]).map(function(n){return t(e,n)}),i=Object.keys(n.attributes||{}).reduce(function(t,e){var a=n.attributes[e];switch(e){case"class":t.attrs.className=a,delete n.attributes.class;break;case"style":t.attrs.style=a.split(";").map(function(t){return t.trim()}).filter(function(t){return t}).reduce(function(t,e){var n=e.indexOf(":"),a=eG(e.slice(0,n)),r=e.slice(n+1).trim();return a.startsWith("webkit")?t[a.charAt(0).toUpperCase()+a.slice(1)]=r:t[a]=r,t},{});break;default:0===e.indexOf("aria-")||0===e.indexOf("data-")?t.attrs[e.toLowerCase()]=a:t.attrs[eG(e)]=a}return t},{attrs:{}}),o=a.style,l=function(t,e){if(null==t)return{};var n,a,r=function(t,e){if(null==t)return{};var n,a,r={},i=Object.keys(t);for(a=0;a<i.length;a++)n=i[a],e.indexOf(n)>=0||(r[n]=t[n]);return r}(t,e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);for(a=0;a<i.length;a++)n=i[a],!(e.indexOf(n)>=0)&&Object.prototype.propertyIsEnumerable.call(t,n)&&(r[n]=t[n])}return r}(a,eK);return i.attrs.style=eH(eH({},i.attrs.style),void 0===o?{}:o),e.apply(void 0,[n.tag,eH(eH({},i.attrs),l)].concat(eq(r)))}).bind(null,e_.createElement)}}]);
//# sourceMappingURL=1772-2b96854433199e89.js.map