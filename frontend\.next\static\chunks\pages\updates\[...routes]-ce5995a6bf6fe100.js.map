{"version": 3, "file": "static/chunks/pages/updates/[...routes]-ce5995a6bf6fe100.js", "mappings": "mLA4BA,MArBe,KACb,IAAMA,EAASC,CAAAA,EAAAA,EAAAA,SAAAA,CAASA,EAoBXC,CAjBb,KAiBmBA,EAjBXC,CAFaH,EAAOI,KAAK,CAACD,MAAM,EAAI,GAE9B,CAAC,EAAE,EACf,IAAK,MACL,IAAK,OACH,MAAO,UAACE,EAAAA,OAAIA,CAAAA,CAACL,OAAQA,GACvB,SACE,OAAO,IACX,CACF,mBCjBA,4CACA,uBACA,WACA,OAAe,EAAQ,KAA4C,CACnE,EACA,SAFsB", "sources": ["webpack://_N_E/./pages/updates/[...routes].tsx", "webpack://_N_E/?a237"], "sourcesContent": ["//Import Library\r\nimport { useRouter } from 'next/router';\r\n\r\n//Import services/components\r\nimport Form from '../updates';\r\nimport { serverSideTranslations } from 'next-i18next/serverSideTranslations';\r\n\r\nconst Router = () => {\r\n  const router = useRouter()\r\n  const routes: any = (router.query.routes || []);\r\n\r\n  switch (routes[0]) {\r\n    case 'add':\r\n    case 'edit':\r\n      return <Form router={router} />\r\n    default:\r\n      return null;\r\n  }\r\n}\r\n\r\nexport async function getServerSideProps({ locale }: { locale: string }) {\r\n  return {\r\n    props: {\r\n      ...(await serverSideTranslations(locale, ['common'])),\r\n    },\r\n  }\r\n}\r\n\r\nexport default Router;\r\n", "\n    (window.__NEXT_P = window.__NEXT_P || []).push([\n      \"/updates/[...routes]\",\n      function () {\n        return require(\"private-next-pages/updates/[...routes].tsx\");\n      }\n    ]);\n    if(module.hot) {\n      module.hot.dispose(function () {\n        window.__NEXT_P.push([\"/updates/[...routes]\"])\n      });\n    }\n  "], "names": ["router", "useRouter", "Router", "routes", "query", "Form"], "sourceRoot": "", "ignoreList": []}