{"version": 3, "file": "static/chunks/1656-97f00f4fbdb37d6c.js", "mappings": "0MASA,IAAMA,EAAa,CACfC,IAAK,QACLC,OAAQ,QACRC,KAAM,QACN,YAAa,OACjB,EA4CA,EAjCgC,IAC5B,GAAM,CAAEC,GAAC,CAAE,CAAGC,CAAAA,EAAAA,EAAAA,EAAAA,CAAcA,CAAC,MAgClBC,IA/BL,CAACC,EAASC,EAAW,CAAGC,CAAAA,EAAAA,EAAAA,QA+BIH,CA/BIG,CA+BH,CA/BI,GAEjC,CAAEC,iBAAe,CAAE,CAAGC,EAE5B,MACI,+BACI,WAACC,EAAAA,CAASA,CAACC,IAAI,EAACC,SAAS,cACrB,WAACF,EAAAA,CAASA,CAACG,MAAM,EAACC,QAAS,IAAMR,EAAW,CAACD,aACzC,UAACU,MAAAA,CAAIC,UAAU,qBAAad,EAAE,gCAC9B,UAACa,MAAAA,CAAIC,UAAU,qBACVX,EACG,UAACY,EAAAA,CAAeA,CAAAA,CAACC,KAAMC,EAAAA,GAAOA,CAAEC,MAAM,SAEtC,UAACH,EAAAA,CAAeA,CAAAA,CAACC,KAAMG,EAAAA,GAAMA,CAAED,MAAM,cAIjD,UAACV,EAAAA,CAASA,CAACY,IAAI,WACVd,EAAgBe,OAAO,CACpB,WAACR,MAAAA,WACIS,SAyBpBA,CAAyC,CAAEtB,CAA0B,EAC1E,MACI,WAACa,MAAAA,CAAIC,UAAU,wBACVR,EAAgBe,OAAO,CACpB,WAACR,MAAAA,CAAIC,UAAU,sBACX,UAACD,MAAAA,CAAIC,UAAW,YAAiD,OAArClB,CAAK,CAACU,EAAgBe,OAAO,CAACE,KAAK,CAAC,WAC5D,UAACC,MAAAA,CAAIC,IAAI,4BAA4BC,MAAM,KAAKC,OAAO,KAAKC,IAAI,2BAEpE,WAACf,MAAAA,CAAIC,UAAU,qBACX,UAACe,KAAAA,UAAI7B,EAAE,yBACP,UAAC8B,KAAAA,UAAIxB,EAAgBe,OAAO,CAACE,KAAK,SAI1C,yBAEHjB,EAAgByB,MAAM,CACnB,WAAClB,MAAAA,CAAIC,UAAU,sBACX,UAACD,MAAAA,CACGC,UAAW,YAEV,OADGR,GAAmBA,EAAgByB,MAAM,CAAGnC,CAAK,CAACU,EAAgByB,MAAM,CAACR,KAAK,CAAC,CAAG,aAGtF,UAACC,MAAAA,CAAIC,IAAI,2BAA2BC,MAAM,KAAKC,OAAO,KAAKC,IAAI,2BAEnE,WAACf,MAAAA,CAAIC,UAAU,qBACX,UAACe,KAAAA,UAAI7B,EAAE,wBACP,UAAC8B,KAAAA,UAAIxB,GAAmBA,EAAgByB,MAAM,CAAGzB,EAAgByB,MAAM,CAACR,KAAK,CAAG,WAIxF,yBAEHjB,EAAgB0B,aAAa,CAC1B,WAACnB,MAAAA,CAAIC,UAAU,sBACX,UAACD,MAAAA,CACGC,UAAW,YAIV,OAHGR,GAAmBA,EAAgB0B,aAAa,CAC1CpC,CAAK,CAACU,EAAgB0B,aAAa,CAACT,KAAK,CAAC,CAC1C,aAGV,UAACC,MAAAA,CAAIC,IAAI,kCAAkCC,MAAM,KAAKC,OAAO,KAAKC,IAAI,2BAE1E,WAACf,MAAAA,CAAIC,UAAU,qBACX,UAACe,KAAAA,UAAI7B,EAAE,+BACP,UAAC8B,KAAAA,UACIxB,GAAmBA,EAAgB0B,aAAa,CAC3C1B,EAAgB0B,aAAa,CAACT,KAAK,CACnC,WAKlB,2BAIhB,EAnFkDjB,EAAiBN,GAanE,SAASiC,CAAmC,EACxC,MACI,UAACpB,MAAAA,CAAIC,UAAU,gBACX,UAACoB,EAAAA,CAAiBA,CAAAA,CACdC,YACIC,GAAaA,EAAU9B,eAAe,CAAC6B,WAAW,CAAGC,EAAU9B,eAAe,CAAC6B,WAAW,CAAG,MAKjH,EArBkD5B,MAE1B,WAKxB,8NCjDO,IAAM8B,EAAcC,CAAAA,EAAAA,EAAAA,CAAAA,CAAoBA,CAAC,CAC9CC,sBAAuB,KACjBC,EAAMC,WAAW,IAAID,EAAMC,WAAW,CAACC,KAAK,IAAIF,EAAMC,WAAW,CAACC,KAAK,CAAC,aAAa,CAK3FC,CAL6F,kBAKzE,aACtB,GAEaC,EAAkBN,CAAAA,EAAAA,EAAAA,CAAAA,CAAoBA,CAAC,CAClDC,sBAAuB,KACjBC,EAAMC,WAAW,IAAID,EAAMC,WAAW,CAACC,KAAK,IAAIF,EAAMC,WAAW,CAACC,KAAK,CAAC,aAAa,CAK3FC,CAL6F,kBAKzE,kBACpBE,iBAAkB,IAAM,UAACC,EAAAA,OAAIA,CAAAA,CAAAA,EAC/B,GAAG,EAEyBR,CAAAA,EAAAA,EAAAA,CAAAA,CAAoBA,CAAC,CAC/CC,sBAAuB,CAACC,EAAOjC,KAC7B,GAAIiC,EAAMC,WAAW,EAAID,EAAMC,WAAW,CAACC,KAAK,EAAE,GAC5CF,EAAMC,WAAW,CAACC,KAAK,CAAC,aAAa,CACvC,CADyC,MAClC,OAEP,GAAIF,EAAMC,WAAW,CAACC,KAAK,CAAC,aAAa,EAAE,EAC/BA,KAAK,EAAInC,EAAMmC,KAAK,CAACK,IAAI,EAAIxC,EAAMmC,KAAK,CAACK,IAAI,CAACC,GAAG,GAAKR,EAAMO,IAAI,CAACC,GAAG,CAC5E,CAD8E,MACvE,CAGb,CAEF,MAAO,EACT,EACAL,mBAAoB,cACtB,GAAG,EAE6BL,CAAAA,EAAAA,EAAAA,CAAAA,CAAoBA,CAAC,CACnDC,sBAAuB,CAACC,EAAOjC,KAC7B,GAAIiC,EAAMC,WAAW,EAAID,EAAMC,WAAW,CAACC,KAAK,EAAE,GAC5CF,EAAMC,WAAW,CAACC,KAAK,CAAC,aAAa,CACvC,CADyC,KAClC,QAEP,GAAIF,EAAMC,WAAW,CAACC,KAAK,CAAC,aAAa,EAAE,EAC/BA,KAAK,EAAInC,EAAMmC,KAAK,CAACK,IAAI,EAAIxC,EAAMmC,KAAK,CAACK,IAAI,CAACC,GAAG,GAAKR,EAAMO,IAAI,CAACC,GAAG,CAC5E,CAD8E,KACvE,EAGb,CAEF,OAAO,CACT,EACAL,mBAAoB,mBACpBE,iBAAkB,IAAM,UAACC,EAAAA,OAAIA,CAAAA,CAAAA,EAC/B,GAAG,EAEoCR,CAAAA,EAAAA,EAAAA,CAAAA,CAAoBA,CAAC,CAC1DC,sBAAuB,KACjBC,EAAMC,WAAW,IAAID,EAAMC,WAAW,CAACQ,MAAM,IAAIT,EAAMC,WAAW,CAACQ,MAAM,CAAC,WAAW,CAK3FN,CAL6F,kBAKzE,yBACtB,GAAG,EAEYN,WAAWA,EAAC,gKCtC3B,MA1B+B9B,IAE3B,IAAM2C,EAA0BC,CAAAA,EAAAA,EAAAA,aAwBrBC,UAxBqBD,CAAuBA,CAAC,IAAM,OAwB7B,CAxB6B,EAACE,EAAAA,OAAmBA,CAAAA,CAAG,GAAG9C,EAAM+C,SAAS,IAEvG,MACI,+BACI,UAACC,EAAAA,CAAGA,CAAAA,UACA,WAACC,EAAAA,CAAGA,CAAAA,CAAC1C,UAAU,iBAAiB2C,GAAI,aAChC,UAACjD,EAAAA,CAASA,CAAAA,UACN,UAACN,EAAAA,OAAuBA,CAAAA,CAAG,GAAGK,EAAM6B,SAAS,KAEjD,UAAC5B,EAAAA,CAASA,CAAAA,UACN,UAACkD,EAAAA,OAAwBA,CAAAA,CAAG,GAAGnD,EAAM6B,SAAS,KAElD,UAAC5B,EAAAA,CAASA,CAAAA,UACN,UAACmD,EAAAA,OAAqBA,CAAAA,CAAG,GAAGpD,EAAM6B,SAAS,KAE/C,UAAC5B,EAAAA,CAASA,CAAAA,UACN,UAAC0C,EAAAA,CAAAA,WAMzB,8ICDA,MAxBiC,IAC7B,GAAM,GAAElD,CAAC,CAAE,CAAGC,CAAAA,EAAAA,EAAAA,EAAAA,CAAcA,CAAC,MAuBlByD,IAtBL,CAACvD,EAASC,EAAW,CAAGC,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,EAAC,GACvC,MACI,+BACI,WAACG,EAAAA,CAASA,CAACC,IAAI,EAACC,SAAS,cACrB,WAACF,EAAAA,CAASA,CAACG,MAAM,EAACC,QAAS,IAAMR,EAAW,CAACD,aACzC,UAACU,MAAAA,CAAIC,UAAU,qBAAad,EAAE,iCAC9B,UAACa,MAAAA,CAAIC,UAAU,qBACVX,EACG,UAACY,EAAAA,CAAeA,CAAAA,CAACC,KAAMC,EAAAA,GAAOA,CAAEC,MAAM,SAEtC,UAACH,EAAAA,CAAeA,CAAAA,CAACC,KAAMG,EAAAA,GAAMA,CAAED,MAAM,cAIjD,UAACV,EAAAA,CAASA,CAACY,IAAI,WACX,UAACc,EAAAA,CAAiBA,CAAAA,CAACC,YAAa5B,EAAMqD,SAAS,SAKnE,+ICGA,MAzB8B,IAC1B,GAAM,GAAE5D,CAAC,CAAE,CAAGC,CAAAA,EAAAA,EAAAA,EAAAA,CAAcA,CAAC,MAwBlB0D,IAvBL,CAACxD,EAASC,EAAW,CAAGC,CAAAA,EAAAA,EAAAA,MAuBEsD,EAvBFtD,CAAQA,EAAC,GAEvC,MACI,+BACI,WAACG,EAAAA,CAASA,CAACC,IAAI,EAACC,SAAS,cACrB,WAACF,EAAAA,CAASA,CAACG,MAAM,EAACC,QAAS,IAAMR,EAAW,CAACD,aACzC,UAACU,MAAAA,CAAIC,UAAU,qBAAad,EAAE,8BAC9B,UAACa,MAAAA,CAAIC,UAAU,qBACVX,EACG,UAACY,EAAAA,CAAeA,CAAAA,CAACC,KAAMC,EAAAA,GAAOA,CAAEC,MAAM,SAEtC,UAACH,EAAAA,CAAeA,CAAAA,CAACC,KAAMG,EAAAA,GAAMA,CAAED,MAAM,cAIjD,UAACV,EAAAA,CAASA,CAACY,IAAI,WACX,UAACyC,EAAAA,CAAWA,CAAAA,CAACC,QAASvD,EAAMwD,MAAM,CAAEC,YAAazD,EAAM0D,UAAU,SAKrF,+ICCA,MAxB4B,IACxB,GAAM,CAAEjE,GAAC,CAAE,CAAGC,CAAAA,EAAAA,EAAAA,EAAAA,CAAcA,CAAC,MAuBlBoD,IAtBL,CAAClD,EAASC,EAAW,CAAGC,CAAAA,EAAAA,EAAAA,IAsBAgD,EAAC,EAtBDhD,CAAQA,EAAC,GACvC,MACI,+BACI,WAACG,EAAAA,CAASA,CAACC,IAAI,EAACC,SAAS,cACrB,WAACF,EAAAA,CAASA,CAACG,MAAM,EAACC,QAAS,IAAMR,EAAW,CAACD,aACzC,UAACU,MAAAA,CAAIC,UAAU,qBAAad,EAAE,6BAC9B,UAACa,MAAAA,CAAIC,UAAU,qBACVX,EACG,UAACY,EAAAA,CAAeA,CAAAA,CAACC,KAAMC,EAAAA,GAAOA,CAAEC,MAAM,SAEtC,UAACH,EAAAA,CAAeA,CAAAA,CAACC,KAAMG,EAAAA,GAAMA,CAAED,MAAM,cAIjD,UAACV,EAAAA,CAASA,CAACY,IAAI,WACX,UAAC8C,EAAAA,CAAUA,CAAAA,CAACC,KAAK,QAAQC,GAAI7D,OAAAA,EAAAA,KAAAA,EAAAA,EAAO8D,MAAAA,CAAP9D,CAAgBA,EAAM8D,MAAM,CAAC,EAAE,CAAG,aAKnF,yBCxBuC,CAGtC,YAA4B,aAI7B,oBACA,OACA,iCACA,iCACA,0BACA,0BACA,+BACA,8BACA,8BACA,4BACA,6BAEA,yBAGA,qBACA,qGACA,KAEA,YACA,2EACA,oBACA,SACA,oEACA,KAEA,uDACA,8CACA,sBACA,gBACA,WACA,eACA,eACA,kBACA,yBACA,+BACA,CAAS,CACT,UACA,8BACA,aACA,+BACA,8BACA,gCACA,uCACA,CAAS,CACT,cACA,eACA,cACA,sBACA,iBACA,IACA,gBACA,IACA,gBACA,IACA,KACA,IACA,eACA,IACA,KACA,IACA,IACA,CAAS,CACT,2BAAoC,IAAI,IACxC,cACA,MACA,MACA,KACA,CAAS,CACJ,EAIL,CAAC,CAhFiD,EAAQ,KAAW", "sources": ["webpack://_N_E/./pages/event/components/RiskAssessmentAccordion.tsx", "webpack://_N_E/./pages/event/permission.tsx", "webpack://_N_E/./pages/event/components/EventAccordionSection.tsx", "webpack://_N_E/./pages/event/components/MoreInformationAccordion.tsx", "webpack://_N_E/./pages/event/components/MediaGalleryAccordion.tsx", "webpack://_N_E/./pages/event/components/DiscussionAccordion.tsx", "webpack://_N_E/./node_modules/moment/locale/de.js"], "sourcesContent": ["//Import Library\r\nimport React, { useEffect, useState } from \"react\";\r\nimport { faMinus, faPlus } from \"@fortawesome/free-solid-svg-icons\";\r\nimport { FontAwesomeIcon } from \"@fortawesome/react-fontawesome\";\r\nimport { Accordion, Card } from \"react-bootstrap\";\r\n\r\n//Import services/components\r\nimport { useTranslation } from 'next-i18next';\r\nimport ReadMoreContainer from \"../../../components/common/readMore/readMore\";\r\nconst icons: any = {\r\n    Low: \"risk0\",\r\n    Medium: \"risk1\",\r\n    High: \"risk2\",\r\n    \"Very High\": \"risk3\",\r\n};\r\n\r\ninterface RiskAssessmentAccordionProps {\r\n  risk_assessment: {\r\n    country?: {\r\n      title: string;\r\n    };\r\n    description?: string;\r\n  };\r\n}\r\n\r\nconst RiskAssessmentAccordion = (props: RiskAssessmentAccordionProps) => {\r\n    const { t } = useTranslation('common');\r\n    const [section, setSection] = useState(false);\r\n\r\n    const { risk_assessment } = props;\r\n\r\n    return (\r\n        <>\r\n            <Accordion.Item eventKey=\"0\">\r\n                <Accordion.Header onClick={() => setSection(!section)}>\r\n                    <div className=\"cardTitle\">{t(\"Events.show.RiskAssessment\")}</div>\r\n                    <div className=\"cardArrow\">\r\n                        {section ? (\r\n                            <FontAwesomeIcon icon={faMinus} color=\"#fff\" />\r\n                        ) : (\r\n                            <FontAwesomeIcon icon={faPlus} color=\"#fff\" />\r\n                        )}\r\n                    </div>\r\n                </Accordion.Header>\r\n                <Accordion.Body>\r\n                    {risk_assessment.country ? (\r\n                        <div>\r\n                            {risk_assessment_Func(risk_assessment, t)}\r\n\r\n                            {risk_assessment_func(props)}\r\n                        </div>\r\n                    ) : null}\r\n                </Accordion.Body>\r\n            </Accordion.Item>\r\n        </>\r\n    )\r\n};\r\n\r\nexport default RiskAssessmentAccordion;\r\n\r\nfunction risk_assessment_func(eventData: any) {\r\n    return (\r\n        <div className=\"mt-4\">\r\n            <ReadMoreContainer\r\n                description={\r\n                    eventData && eventData.risk_assessment.description ? eventData.risk_assessment.description : \"\"\r\n                }\r\n            />\r\n        </div>\r\n    );\r\n}\r\n\r\nfunction risk_assessment_Func(risk_assessment: any, t: (key: string) => string) {\r\n    return (\r\n        <div className=\"riskDetails\">\r\n            {risk_assessment.country ? (\r\n                <div className=\"riskItems\">\r\n                    <div className={`riskIcon ${icons[risk_assessment.country.title]}`}>\r\n                        <img src=\"/images/event_country.png\" width=\"30\" height=\"30\" alt=\"Risk Assessment Info\" />\r\n                    </div>\r\n                    <div className=\"riskInfo\">\r\n                        <h5>{t(\"Events.show.Country\")}</h5>\r\n                        <h4>{risk_assessment.country.title}</h4>\r\n                    </div>\r\n                </div>\r\n            ) : (\r\n                <></>\r\n            )}\r\n            {risk_assessment.region ? (\r\n                <div className=\"riskItems\">\r\n                    <div\r\n                        className={`riskIcon ${\r\n                            risk_assessment && risk_assessment.region ? icons[risk_assessment.region.title] : \"\"\r\n                        }`}\r\n                    >\r\n                        <img src=\"/images/event_region.png\" width=\"35\" height=\"26\" alt=\"Risk Assessment Info\" />\r\n                    </div>\r\n                    <div className=\"riskInfo\">\r\n                        <h5>{t(\"Events.show.Region\")}</h5>\r\n                        <h4>{risk_assessment && risk_assessment.region ? risk_assessment.region.title : \"\"}</h4>\r\n                    </div>\r\n                </div>\r\n            ) : (\r\n                <></>\r\n            )}\r\n            {risk_assessment.international ? (\r\n                <div className=\"riskItems\">\r\n                    <div\r\n                        className={`riskIcon ${\r\n                            risk_assessment && risk_assessment.international\r\n                                ? icons[risk_assessment.international.title]\r\n                                : \"\"\r\n                        }`}\r\n                    >\r\n                        <img src=\"/images/event_international.png\" width=\"38\" height=\"38\" alt=\"Risk Assessment Info\" />\r\n                    </div>\r\n                    <div className=\"riskInfo\">\r\n                        <h5>{t(\"Events.show.International\")}</h5>\r\n                        <h4>\r\n                            {risk_assessment && risk_assessment.international\r\n                                ? risk_assessment.international.title\r\n                                : \"\"}\r\n                        </h4>\r\n                    </div>\r\n                </div>\r\n            ) : (\r\n                <></>\r\n            )}\r\n        </div>\r\n    );\r\n}\r\n", "//Import Library\r\nimport React from 'react';\r\nimport connectedAuthWrapper from 'redux-auth-wrapper/connectedAuthWrapper';\r\n\r\n//Import services/components\r\nimport R403 from \"../r403\";\r\n\r\nexport const canAddEvent = connectedAuthWrapper({\r\n  authenticatedSelector: (state) => {\r\n    if (state.permissions && state.permissions.event && state.permissions.event['create:any']) {\r\n      return true;\r\n    }\r\n    return false;\r\n  },\r\n  wrapperDisplayName: 'CanAddEvent',\r\n});\r\n\r\nexport const canAddEventForm = connectedAuthWrapper({\r\n  authenticatedSelector: (state) => {\r\n    if (state.permissions && state.permissions.event && state.permissions.event['create:any']) {\r\n      return true;\r\n    }\r\n    return false;\r\n  },\r\n  wrapperDisplayName: 'CanAddEventForm',\r\n  FailureComponent: () => <R403/>\r\n});\r\n\r\nexport const canEditEvent = connectedAuthWrapper({\r\n  authenticatedSelector: (state, props) => {\r\n    if (state.permissions && state.permissions.event) {\r\n      if (state.permissions.event['update:any']) {\r\n        return true;\r\n      } else { //if update:own\r\n        if (state.permissions.event['update:own']) {\r\n          if (props.event && props.event.user && props.event.user._id === state.user._id) {\r\n            return true;\r\n          }  \r\n        }\r\n      }\r\n    }\r\n    return false;\r\n  },\r\n  wrapperDisplayName: 'CanEditEvent',\r\n});\r\n\r\nexport const canEditEventForm = connectedAuthWrapper({\r\n  authenticatedSelector: (state, props) => {\r\n    if (state.permissions && state.permissions.event) {\r\n      if (state.permissions.event['update:any']) {\r\n        return true;\r\n      } else { //if update:own\r\n        if (state.permissions.event['update:own']) {\r\n          if (props.event && props.event.user && props.event.user._id === state.user._id) {\r\n            return true;\r\n          }\r\n        }\r\n      }\r\n    }\r\n    return false;\r\n  },\r\n  wrapperDisplayName: 'CanEditEventForm',\r\n  FailureComponent: () => <R403/>\r\n});\r\n\r\nexport const canViewDiscussionUpdate = connectedAuthWrapper({\r\n  authenticatedSelector: (state) => {\r\n    if (state.permissions && state.permissions.update && state.permissions.update['read:any']) {\r\n      return true;\r\n    }\r\n    return false;\r\n  },\r\n  wrapperDisplayName: 'CanViewDiscussionUpdate',\r\n});\r\n\r\nexport default canAddEvent;", "//Import Library\r\nimport React from \"react\";\r\nimport { Accordion, Col, Row } from \"react-bootstrap\";\r\n\r\n//Import services/components\r\nimport RiskAssessmentAccordion from \"./RiskAssessmentAccordion\";\r\nimport { canViewDiscussionUpdate } from \"../permission\";\r\nimport DiscussionAccordion from \"./DiscussionAccordion\";\r\nimport MoreInformationAccordion from \"./MoreInformationAccordion\";\r\nimport MediaGalleryAccordion from \"./MediaGalleryAccordion\";\r\n\r\nconst EventAccordionSection = (props: any) => {\r\n\r\n    const CanViewDiscussionUpdate = canViewDiscussionUpdate(() => <DiscussionAccordion { ...props.routeData } />);\r\n\r\n    return (\r\n        <>\r\n            <Row>\r\n                <Col className=\"eventAccordion\" xs={12}>\r\n                    <Accordion>\r\n                        <RiskAssessmentAccordion { ...props.eventData } />\r\n                    </Accordion>\r\n                    <Accordion>\r\n                        <MoreInformationAccordion { ...props.eventData } />\r\n                    </Accordion>\r\n                    <Accordion>\r\n                        <MediaGalleryAccordion { ...props.eventData } />\r\n                    </Accordion>\r\n                    <Accordion>\r\n                        <CanViewDiscussionUpdate />\r\n                    </Accordion>\r\n                </Col>\r\n            </Row>\r\n        </>\r\n    )\r\n};\r\n\r\nexport default EventAccordionSection;", "//Import Library\r\nimport React, { useEffect, useState } from \"react\";\r\nimport { Accordion, Card } from \"react-bootstrap\";\r\nimport { FontAwesomeIcon } from \"@fortawesome/react-fontawesome\";\r\nimport { faMinus, faPlus } from \"@fortawesome/free-solid-svg-icons\";\r\n\r\n//Import services/components\r\nimport { useTranslation } from 'next-i18next';\r\nimport ReadMoreContainer from \"../../../components/common/readMore/readMore\";\r\n\r\nconst MoreInformationAccordion = (props: any) => {\r\n    const { t } = useTranslation('common');\r\n    const [section, setSection] = useState(false);\r\n    return (\r\n        <>\r\n            <Accordion.Item eventKey=\"0\">\r\n                <Accordion.Header onClick={() => setSection(!section)}>\r\n                    <div className=\"cardTitle\">{t(\"Events.show.MoreInformation\")}</div>\r\n                    <div className=\"cardArrow\">\r\n                        {section ? (\r\n                            <FontAwesomeIcon icon={faMinus} color=\"#fff\" />\r\n                        ) : (\r\n                            <FontAwesomeIcon icon={faPlus} color=\"#fff\" />\r\n                        )}\r\n                    </div>\r\n                </Accordion.Header>\r\n                <Accordion.Body>\r\n                    <ReadMoreContainer description={props.more_info} />\r\n                </Accordion.Body>\r\n            </Accordion.Item>\r\n        </>\r\n    )\r\n};\r\n\r\nexport default MoreInformationAccordion;", "//Import Library\r\nimport React, { useEffect, useState } from \"react\";\r\nimport { faMinus, faPlus } from \"@fortawesome/free-solid-svg-icons\";\r\nimport { FontAwesomeIcon } from \"@fortawesome/react-fontawesome\";\r\nimport { Accordion, Card } from \"react-bootstrap\";\r\n\r\n//Import services/components\r\nimport ReactImages from \"../../../components/common/ReactImages\";\r\nimport { useTranslation } from 'next-i18next';\r\n\r\nconst MediaGalleryAccordion = (props: any) => {\r\n    const { t } = useTranslation('common');\r\n    const [section, setSection] = useState(false);\r\n\r\n    return (\r\n        <>\r\n            <Accordion.Item eventKey=\"0\">\r\n                <Accordion.Header onClick={() => setSection(!section)}>\r\n                    <div className=\"cardTitle\">{t(\"Events.show.MediaGallery\")}</div>\r\n                    <div className=\"cardArrow\">\r\n                        {section ? (\r\n                            <FontAwesomeIcon icon={faMinus} color=\"#fff\" />\r\n                        ) : (\r\n                            <FontAwesomeIcon icon={faPlus} color=\"#fff\" />\r\n                        )}\r\n                    </div>\r\n                </Accordion.Header>\r\n                <Accordion.Body>\r\n                    <ReactImages gallery={props.images} imageSource={props.images_src} />\r\n                </Accordion.Body>\r\n            </Accordion.Item>\r\n        </>\r\n    )\r\n};\r\n\r\nexport default MediaGalleryAccordion;", "//Import Library\r\nimport React, { useState } from \"react\";\r\nimport { Accordion, Card } from \"react-bootstrap\";\r\nimport { FontAwesomeIcon } from \"@fortawesome/react-fontawesome\";\r\nimport { faMinus, faPlus } from \"@fortawesome/free-solid-svg-icons\";\r\n\r\n//Import services/components\r\nimport { useTranslation } from 'next-i18next';\r\nimport Discussion from \"../../../components/common/disussion\";\r\n\r\nconst DiscussionAccordion = (props: any) => {\r\n    const { t } = useTranslation('common');\r\n    const [section, setSection] = useState(false);\r\n    return (\r\n        <>\r\n            <Accordion.Item eventKey=\"0\">\r\n                <Accordion.Header onClick={() => setSection(!section)}>\r\n                    <div className=\"cardTitle\">{t(\"Events.show.Discussions\")}</div>\r\n                    <div className=\"cardArrow\">\r\n                        {section ? (\r\n                            <FontAwesomeIcon icon={faMinus} color=\"#fff\" />\r\n                        ) : (\r\n                            <FontAwesomeIcon icon={faPlus} color=\"#fff\" />\r\n                        )}\r\n                    </div>\r\n                </Accordion.Header>\r\n                <Accordion.Body>\r\n                    <Discussion type=\"event\" id={props?.routes ? props.routes[1] : null} />\r\n                </Accordion.Body>\r\n            </Accordion.Item>\r\n        </>\r\n    )\r\n};\r\n\r\nexport default DiscussionAccordion;", "//! moment.js locale configuration\n//! locale : German [de]\n//! author : lluchs : https://github.com/lluchs\n//! author: <PERSON><PERSON><PERSON>: https://github.com/Oire\n//! author : <PERSON><PERSON><PERSON><PERSON> : https://github.com/mik01aj\n\n;(function (global, factory) {\n   typeof exports === 'object' && typeof module !== 'undefined'\n       && typeof require === 'function' ? factory(require('../moment')) :\n   typeof define === 'function' && define.amd ? define(['../moment'], factory) :\n   factory(global.moment)\n}(this, (function (moment) { 'use strict';\n\n    //! moment.js locale configuration\n\n    function processRelativeTime(number, withoutSuffix, key, isFuture) {\n        var format = {\n            m: ['eine Minute', 'einer Minute'],\n            h: ['eine Stunde', 'einer Stunde'],\n            d: ['ein Tag', 'einem Tag'],\n            dd: [number + ' Tage', number + ' Tagen'],\n            w: ['eine Woche', 'einer Woche'],\n            M: ['ein <PERSON><PERSON>', 'einem Monat'],\n            MM: [number + ' <PERSON><PERSON>', number + ' Monaten'],\n            y: ['ein Jahr', 'einem Jahr'],\n            yy: [number + ' Jahre', number + ' Jahren'],\n        };\n        return withoutSuffix ? format[key][0] : format[key][1];\n    }\n\n    var de = moment.defineLocale('de', {\n        months: 'Januar_Februar_März_April_Mai_Juni_Juli_August_September_Oktober_November_Dezember'.split(\n            '_'\n        ),\n        monthsShort:\n            'Jan._Feb._März_Apr._Mai_Juni_Juli_Aug._Sep._Okt._Nov._Dez.'.split('_'),\n        monthsParseExact: true,\n        weekdays:\n            'Sonntag_Montag_Dienstag_Mittwoch_Donnerstag_Freitag_Samstag'.split(\n                '_'\n            ),\n        weekdaysShort: 'So._Mo._Di._Mi._Do._Fr._Sa.'.split('_'),\n        weekdaysMin: 'So_Mo_Di_Mi_Do_Fr_Sa'.split('_'),\n        weekdaysParseExact: true,\n        longDateFormat: {\n            LT: 'HH:mm',\n            LTS: 'HH:mm:ss',\n            L: 'DD.MM.YYYY',\n            LL: 'D. MMMM YYYY',\n            LLL: 'D. MMMM YYYY HH:mm',\n            LLLL: 'dddd, D. MMMM YYYY HH:mm',\n        },\n        calendar: {\n            sameDay: '[heute um] LT [Uhr]',\n            sameElse: 'L',\n            nextDay: '[morgen um] LT [Uhr]',\n            nextWeek: 'dddd [um] LT [Uhr]',\n            lastDay: '[gestern um] LT [Uhr]',\n            lastWeek: '[letzten] dddd [um] LT [Uhr]',\n        },\n        relativeTime: {\n            future: 'in %s',\n            past: 'vor %s',\n            s: 'ein paar Sekunden',\n            ss: '%d Sekunden',\n            m: processRelativeTime,\n            mm: '%d Minuten',\n            h: processRelativeTime,\n            hh: '%d Stunden',\n            d: processRelativeTime,\n            dd: processRelativeTime,\n            w: processRelativeTime,\n            ww: '%d Wochen',\n            M: processRelativeTime,\n            MM: processRelativeTime,\n            y: processRelativeTime,\n            yy: processRelativeTime,\n        },\n        dayOfMonthOrdinalParse: /\\d{1,2}\\./,\n        ordinal: '%d.',\n        week: {\n            dow: 1, // Monday is the first day of the week.\n            doy: 4, // The week that contains Jan 4th is the first week of the year.\n        },\n    });\n\n    return de;\n\n})));\n"], "names": ["icons", "Low", "Medium", "High", "t", "useTranslation", "RiskAssessmentAccordion", "section", "setSection", "useState", "risk_assessment", "props", "Accordion", "<PERSON><PERSON>", "eventKey", "Header", "onClick", "div", "className", "FontAwesomeIcon", "icon", "faMinus", "color", "faPlus", "Body", "country", "risk_assessment_Func", "title", "img", "src", "width", "height", "alt", "h5", "h4", "region", "international", "risk_assessment_func", "ReadMoreContainer", "description", "eventData", "canAddEvent", "connectedAuthWrapper", "authenticatedSelector", "state", "permissions", "event", "wrapperDisplayName", "canAddEventForm", "FailureComponent", "R403", "user", "_id", "update", "CanViewDiscussionUpdate", "canViewDiscussionUpdate", "EventAccordionSection", "DiscussionAccordion", "routeData", "Row", "Col", "xs", "MoreInformationAccordion", "MediaGalleryAccordion", "more_info", "ReactImages", "gallery", "images", "imageSource", "images_src", "Discussion", "type", "id", "routes"], "sourceRoot": "", "ignoreList": [6]}