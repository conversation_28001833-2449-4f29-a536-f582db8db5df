//Import Library
import Link from "next/link";
import { Navbar, NavDropdown, Dropdown, Tooltip, OverlayTrigger } from "react-bootstrap";
import { connect } from "react-redux";
import Router from "next/router";
import { useEffect, useState } from "react";
import NProgress from "nprogress";

//Import services/components
import authService from "../../../services/authService";
import AboutUsModal from "../modals/about-us";
import ContactUsModal from "../modals/contact-us";
import HelpModal from "../modals/help";
import apiService from "../../../services/apiService";
import printContainer from "../../common/printContent/printContainer";
import { useTranslation } from 'next-i18next';
import { useRouter } from 'next/router';

NProgress.configure({ showSpinner: false });
import _ from "lodash";

// Define types for language items
interface LanguageItem {
  _id: string;
  abbr: string;
  title: string;
}

interface HeaderProps {
  user: any;
  [key: string]: any;
}

function Header(props: HeaderProps) {
  const { t, i18n } = useTranslation('common');
  const [getUserName, setUserName] = useState("");
  const [userPicture, setUserPicture] = useState("/images/rkiProfile.jpg");
  const [showAboutUsModal, setAboutUsModal] = useState(false);
  const [showContactUsModal, setContactUsModal] = useState(false);
  const [showHelpModal, setHelpModal] = useState(false);
  const [lang, setLang] = useState<LanguageItem[]>([]);
  const router = useRouter();
  const { pathname, asPath, query } = router;
  const [locale, setLocale] = useState(router.locale);

  /***NProgress*****/
  Router.events.on('routeChangeStart', () => NProgress.start())
  Router.events.on('routeChangeComplete', () => NProgress.done())
  Router.events.on('routeChangeError', () => NProgress.done())
  /*****End***************/

  const logoutFunction = async () => {
    const { logout } = authService;
    await logout();
    Router.push("/home");
  };

  const getLanguage = () => {
    return i18n.language || (typeof window !== 'undefined' && window.localStorage.i18nextLng) || 'en';
  };

  const onChangeLocale = (item: any): void => {
    if (item.abbr === "de") {
      i18n.changeLanguage(item.abbr);
    }
    else {
      i18n.changeLanguage("en");
    }
    setLocale(item.abbr);
    router.push({ pathname, query }, asPath, { locale: item.abbr });
  };

  const getUserPicture = async (userParams: any) => {
    const response = (userParams && userParams.image) ? `${process.env.API_SERVER}/image/show/${userParams.image._id}` : "/images/rkiProfile.jpg";
    setUserPicture(response);
  }

  useEffect(() => {
    const _username = (props.user && props.user.username) ? props.user.username : "";
    setUserName(_username);

    const _locale = getLanguage();
    setLocale(_locale);

    const getLang = async () => {
      const response = await apiService.get("/language", langParams);
      if (response) {
        _.remove(response.data, { abbr: "fr" });
        setLang(response.data);
      }
    };

    const langParams = {
      query: {},
      // sort: { title: "dec" },
      limit: "~",
      select: '-_id -created_at -updated_at'
    };
    getLang();
    getUserPicture(props.user);
  }, [props.user]);


  return (
    <Navbar sticky="top" expand="lg" variant="light" bg="light">
      <Navbar.Brand href="#">
        <img src="/images/logo.jpg" alt="Rohert Koch Institut - Logo" />
      </Navbar.Brand>
      <div className="me-auto" />
      <div className="headerMenu">
        <ul>
          <li>
            <a style={{ cursor: "pointer" }} onClick={() => setAboutUsModal(true)} id="about">{t('about')}</a>
          </li>
          <li>
            <a style={{ cursor: "pointer" }} onClick={() => setContactUsModal(true)} id="contact">{t('contact')}</a>
          </li>
        </ul>
      </div>
      <div className="headerIcons">
        <OverlayTrigger
          placement="left"
          delay={{ show: 250, hide: 400 }}
          overlay={<Tooltip id="language-tooltip" >{t("Languages")}</Tooltip>} >
          <NavDropdown title={(locale || 'en')} className="language" id="basic-nav-dropdown">
            {lang && lang.map((item, i) => (
              <div key={i}>
                <NavDropdown.Item active={(item.abbr === locale)} eventKey={item._id} onClick={() => onChangeLocale(item)} >
                  {item.abbr}-{item.title}
                </NavDropdown.Item>
                <Dropdown.Divider />
              </div>
            ))}

          </NavDropdown>
        </OverlayTrigger>

        <a onClick={() => printContainer('main-content')} className="topiconLinks">
          <OverlayTrigger
            placement="bottom"
            delay={{ show: 250, hide: 400 }}
            overlay={<Tooltip id="print-tooltip" >{t("Print")}</Tooltip>} >
            <i className="fas fa-print" />
          </OverlayTrigger>
        </a>
        <Link href="#" onClick={() => setHelpModal(true)} className="topiconLinks">

          <OverlayTrigger
            placement="bottom"
            delay={{ show: 250, hide: 400 }}
            overlay={<Tooltip id="print-tooltip" >{t("Help")}</Tooltip>} >
            <i className="fas fa-question-circle" />
          </OverlayTrigger>

        </Link>
      </div>
      <div className="headerUser">
        <div className="my-profile-icon">
          <Link href="/profile">

            <OverlayTrigger
              placement="bottom"
              delay={{ show: 250, hide: 400 }}
              overlay={<Tooltip id="profile-tooltip" >{t("Profile")}</Tooltip>} >
              <img src={userPicture} style={{
                background: "rgb(245, 245, 245)",
                borderRadius: "50%",
                width: "35px",
                height: "35px"
              }} />
            </OverlayTrigger>

          </Link>
        </div>
        <NavDropdown title={getUserName} id="basic-nav-dropdown">
          <NavDropdown.Item>
            <Link href="/profile">
              {t("Profile")}
            </Link>
          </NavDropdown.Item>
          <NavDropdown.Item onClick={logoutFunction}>{t("Logout")}</NavDropdown.Item>
        </NavDropdown>
      </div>
      {/* About us */}
      <AboutUsModal
        {...props}
        show={showAboutUsModal}
        onHide={() => setAboutUsModal(false)}
      />
      {/* Contact us */}
      <ContactUsModal
        {...props}
        show={showContactUsModal}
        onHide={() => setContactUsModal(false)}
      />
      {/* Help */}
      <HelpModal
        {...props}
        show={showHelpModal}
        onHide={() => setHelpModal(false)}
      />
    </Navbar>
  );
}

const mapStateToProps = (state: any) => state;
export default connect(mapStateToProps)(Header);
