"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[9946],{61887:(e,t,i)=>{i.r(t),i.d(t,{default:()=>A});var a=i(37876),r=i(14232),s=i(17336),l=i(21772),d=i(11041),n=i(82851),c=i.n(n),o=i(53718),p=i(31753);let h=[],x="20971520",u={flex:1,display:"flex",flexDirection:"column",alignItems:"center",justifyContent:"center",width:"100%",height:"100%",borderWidth:.1,borderColor:"#fafafa",backgroundColor:"#fafafa",color:"black",transition:"border  .24s ease-in-out"},m={display:"inline-flex",borderRadius:2,border:"1px solid #ddd",marginBottom:8,marginRight:20,width:100,height:100,padding:2,position:"relative",boxShadow:"0 0 15px 0.25px rgba(0,0,0,0.25)",boxSizing:"border-box"},g={display:"flex",flexDirection:"row",justifyContent:"flex-start",flexWrap:"wrap",marginTop:20},f={display:"flex",minWidth:0,overflow:"hidden"},j={position:"absolute",fontSize:"22px",top:"-10px",right:"-10px",zIndex:1e3,cursor:"pointer",backgroundColor:"#fff",color:"#000",borderRadius:"50%"},b={display:"block",width:"auto",height:"100%"},y={borderColor:"#2196f3"},A=e=>{let{t}=(0,p.Bd)("common"),[i,n]=(0,r.useState)([]),A=async e=>{await o.A.remove("/image/".concat(e))},v=t=>{let a=t&&t._id?{serverID:t._id}:{file:t},r=c().findIndex(h,a);A(h[r].serverID),h.splice(r,1),e.getImgID(h,e.index?e.index:0);let s=[...i];s.splice(s.indexOf(t),1),n(s)},_=e=>(0,a.jsx)("img",{src:e.preview,style:b}),z=i.map((e,t)=>(0,a.jsx)("div",{children:(0,a.jsxs)("div",{style:m,children:[(0,a.jsx)("div",{style:f,children:_(e)}),(0,a.jsx)(l.g,{icon:d.s0Q,style:j,color:"black",onClick:()=>v(e)})]})},t));(0,r.useEffect)(()=>{i.forEach(e=>URL.revokeObjectURL(e.preview)),h=[]},[]),(0,r.useEffect)(()=>{e&&e.datas&&n([...e.datas.map((t,i)=>(h.push({serverID:t._id,index:e.index?e.index:0}),{...t,preview:"".concat("http://localhost:3001/api/v1","/image/show/").concat(t._id)}))])},[e.datas]);let w=async(t,i)=>{if(t.length>i)try{let a=new FormData;a.append("file",t[i]);let r=await o.A.post("/image",a,{"Content-Type":"multipart/form-data"});h.push({serverID:r._id,file:t[i],index:e.index?e.index:0}),w(t,i+1)}catch(e){w(t,i+1)}else e.getImgID(h,e.index?e.index:0)},C=(0,r.useCallback)(e=>{w(e,0),n([...e.map((e,t)=>Object.assign(e,{preview:URL.createObjectURL(e)}))])},[]),{getRootProps:k,getInputProps:N,isDragActive:I,isDragAccept:S,isDragReject:D,fileRejections:L}=(0,s.VB)({accept:"image/*",multiple:!1,minSize:0,maxSize:x,onDrop:C}),T=(0,r.useMemo)(()=>({...u,...I?y:{outline:"2px dashed #bbb"},...S?{outline:"2px dashed #595959"}:{outline:"2px dashed #bbb"},...D?{outline:"2px dashed red"}:{activeStyle:y}}),[I,D]),R=L.length>0&&L[0].file.size>x;return(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)("div",{className:" d-flex justify-content-center align-items-center mt-3",style:{width:"100%",height:"180px"},children:(0,a.jsxs)("div",{...k({style:T}),children:[(0,a.jsx)("input",{...N()}),(0,a.jsx)(l.g,{icon:d.rOd,size:"4x",color:"#999"}),(0,a.jsx)("p",{style:{color:"#595959",marginBottom:"0px"},children:t("Drag'n'dropsomefileshere,orclicktoselectfiles")}),(0,a.jsx)("small",{style:{color:"#595959"},children:t("ImageWeSupport")}),(0,a.jsxs)("small",{style:{color:"#595959"},children:[(0,a.jsx)("b",{children:t("Note:")})," ",t("Onesingleimagewillbeaccepted")]}),R&&(0,a.jsxs)("small",{className:"text-danger mt-2",children:[" ",(0,a.jsx)(l.g,{icon:d.tUE,size:"1x",color:"red"})," ",t("FileistoolargeItshouldbelessthan20MB")]}),D&&(0,a.jsxs)("small",{className:"text-danger",style:{color:"#595959"},children:[(0,a.jsx)(l.g,{icon:d.tUE,size:"1x",color:"red"})," ",t("Filetypenotacceptedsorr")]})]})}),(0,a.jsx)("div",{style:g,children:z})]})}},89946:(e,t,i)=>{i.r(t),i.d(t,{default:()=>z});var a=i(37876),r=i(49589),s=i(29335),l=i(56970),d=i(37784),n=i(29504),c=i(97257),o=i(34192),p=i(60282),h=i(54773),x=i(35611),u=i(14232),m=i(97685),g=i(89099),f=i.n(g),j=i(48230),b=i.n(j),y=i(53718),A=i(61887),v=i(31753),_=i(5671);let z=e=>{let{t,i18n:i}=(0,v.Bd)("common"),g=i.language&&"fr"===i.language?"en":i.language,j={title:{en:"",fr:"",de:""},hazard_type:"",description:{},enabled:!0,picture:null,picture_source:"",images_src:[]},z=async()=>{let e=await y.A.get("/language",w);e&&R(e.data)},w={query:{},sort:{title:"asc"},limit:"~",select:"-_id -created_at -updated_at"},[C,k]=(0,u.useState)(j),[N,I]=(0,u.useState)([]),[,S]=(0,u.useState)([]),[D,L]=(0,u.useState)([]),[T,R]=(0,u.useState)([]),[U,E]=(0,u.useState)(i.language&&"fr"===i.language&&"en"===g?"fr":g),B=e.routes&&"edit_hazard"===e.routes[0]&&e.routes[1],q=async(i,a)=>{let r,s;i.preventDefault();let l=a||C,d={},n={};T&&T.map((e,t)=>{let i=e.abbr;d={...d,[i]:l.title[i]},n={...n,[i]:l.description[i]}}),d.en&&(d.de=d.en,d.fr=d.en),d.de&&(d.en=d.de,d.fr=d.de),d.fr&&(d.en=d.fr,d.de=d.fr);let c={title:{...d},hazard_type:l.hazard_type,description:{...n},enabled:l.enabled,picture:l.picture,picture_source:l.picture_source,images_src:l.images_src};B?(s="Hazardisupdatedsuccessfully",r=await y.A.patch("/hazard/".concat(e.routes[1]),c)):(s="Hazardisaddedsuccessfully",r=await y.A.post("/hazard",c)),r&&r._id?(m.Ay.success(t(s)),f().push("/adminsettings/hazard")):(null==r?void 0:r.errorCode)===11e3?m.Ay.error(t("duplicatesNotAllowed")):m.Ay.error(r)},H=e=>{E(e.abbr)},O=e=>{if(e.target){let{name:t,value:i}=e.target;k(e=>({...e,[t]:i}))}},F=async e=>{let t=await y.A.get("/hazardtype",e);t&&L(t.data)},G=e=>{let t={...C.description,[U]:e};k(e=>({...e,description:t}))},M=async t=>{let i=await y.A.get("/hazard/".concat(e.routes[1]),t);if(i){let{hazard_type:e,picture:t}=i;i.hazard_type=e&&e._id?e._id:"",i.title&&"object"==typeof i.title?i.title={en:i.title.en||"",fr:i.title.fr||"",de:i.title.de||""}:i.title={en:"",fr:"",de:""},i.description&&"object"==typeof i.description?i.description={en:i.description.en||"",fr:i.description.fr||"",de:i.description.de||"",...i.description}:i.description={},null!=t&&I([t]),S(i.images_src?i.images_src:[]),k(e=>({...e,...i}))}};(0,u.useEffect)(()=>{z();let e={query:{},sort:{title:"asc"},limit:"~"};B&&M(e),F(e)},[]);let W=(0,u.useRef)(null),V=e=>{let t=e.map(e=>e.serverID);k(e=>({...e,picture:t}))};return(0,a.jsx)(r.A,{className:"formCard",fluid:!0,children:(0,a.jsx)(s.A,{style:{marginTop:"5px",boxShadow:"0 4px 8px 0 rgba(0, 0, 0, 0.2), 0 6px 20px 0 rgba(0, 0, 0, 0.19)"},children:(0,a.jsx)(h.A,{onSubmit:q,ref:W,initialValues:C,enableReinitialize:!0,children:(0,a.jsxs)(s.A.Body,{children:[(0,a.jsx)(l.A,{children:(0,a.jsx)(d.A,{children:(0,a.jsx)(s.A.Title,{children:e.routes&&"edit_hazard"===e.routes[0]?t("editHazard"):t("addHazard")})})}),(0,a.jsx)("hr",{}),(0,a.jsxs)(l.A,{className:"mb-3",children:[(0,a.jsx)(d.A,{md:!0,lg:6,sm:12,children:(0,a.jsxs)(n.A.Group,{children:[(0,a.jsx)(n.A.Label,{className:"required-field",children:t("hazardName")}),(0,a.jsx)(x.ks,{name:"title",id:"title",required:"en"===U,value:"fr"===U?C.title.en||"":C.title[U]||"",validator:e=>""!==String(C.title[U]||"").trim(),errorMessage:{validator:t("adminsetting.hazard.add")},onChange:e=>{let{name:t,value:i}=e.target,a={...C.title,[U]:i};k(e=>({...e,[t]:a}))}})]})}),(0,a.jsx)(d.A,{md:!0,lg:6,sm:12,children:(0,a.jsxs)(n.A.Group,{children:[(0,a.jsx)(n.A.Label,{className:"required-field d-flex me-3",children:t("hazardType")}),(0,a.jsxs)(x.s3,{name:"hazard_type",id:"hazard_type",required:!0,value:C.hazard_type,errorMessage:{validator:t("pleaseAddtheHazardType")},onChange:O,children:[(0,a.jsx)("option",{value:"",children:t("hazardTypeCategory.selectHazardType")}),D.length>=1?D.map((e,t)=>(0,a.jsx)("option",{value:e._id,children:e.title},e._id)):null]})]})})]}),(0,a.jsx)(l.A,{className:"mb-3",children:(0,a.jsxs)(d.A,{md:4,className:"d-flex",style:{marginTop:"10px"},children:[(0,a.jsx)(n.A.Label,{children:t("chooseLanguage")}),(0,a.jsx)(c.A,{title:U.toUpperCase(),variant:"outline-secondary",id:"basic-dropdown",className:"ms-2",children:T&&T.map((e,t)=>(0,a.jsx)("div",{children:(0,a.jsxs)(o.A.Item,{active:e.abbr===U,eventKey:e._id,onClick:()=>H(e),children:[e.abbr.toUpperCase(),"-",e.title.toUpperCase()]})},t))})]})}),(0,a.jsx)("br",{}),(0,a.jsx)(l.A,{children:(0,a.jsx)(n.A.Check,{className:" ms-4",type:"switch",name:"rki_monitored",id:"custom-switch",onChange:e=>{k(e=>({...e,enabled:!e.enabled}))},label:t("published"),checked:C.enabled})}),(0,a.jsx)("br",{}),(0,a.jsx)(l.A,{children:(0,a.jsx)(d.A,{children:(0,a.jsxs)(n.A.Group,{children:[(0,a.jsx)(n.A.Label,{children:t("description")}),(0,a.jsx)(_.x,{initContent:"fr"===U?C.description.en||"":C.description[U]||"",onChange:e=>G(e)})]})})}),(0,a.jsx)(l.A,{children:(0,a.jsx)(d.A,{children:(0,a.jsx)(A.default,{datas:N,getImgID:e=>V(e)})})}),(0,a.jsx)(l.A,{children:(0,a.jsx)(d.A,{children:(0,a.jsxs)(n.A.Group,{children:[(0,a.jsx)(n.A.Label,{children:t("imageSourceCredit")}),(0,a.jsx)(x.ks,{name:"picture_source",id:"picture_source",value:C.picture_source||"",onChange:O})]})})}),(0,a.jsx)(l.A,{className:"my-4",children:(0,a.jsxs)(d.A,{children:[(0,a.jsx)(p.A,{className:"me-2",type:"submit",variant:"primary",children:t("submit")}),(0,a.jsx)(p.A,{className:"me-2",onClick:()=>{k(j),I([]),S([]),window.scrollTo(0,0)},variant:"info",children:t("reset")}),(0,a.jsx)(b(),{href:"/adminsettings/[...routes]",as:"/adminsettings/hazard",children:(0,a.jsx)(p.A,{variant:"secondary",children:t("Cancel")})})]})})]})})})})}}}]);
//# sourceMappingURL=9946-1d72664cd0cae2b3.js.map