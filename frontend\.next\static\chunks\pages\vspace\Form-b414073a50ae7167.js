(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[4809],{5671:(e,r,t)=>{"use strict";t.d(r,{x:()=>l});var a=t(37876),n=t(14232);let d=e=>{let{value:r,onChange:t,placeholder:d="Write something...",height:l=300,disabled:s=!1}=e,i=(0,n.useRef)(null),[o,c]=(0,n.useState)(!1);(0,n.useEffect)(()=>{i.current&&1&&!o&&i.current.innerHTML!==r&&(i.current.innerHTML=r||"")},[r,o]);let u=()=>{i.current&&t&&t(i.current.innerHTML)},p=(e,r)=>{if("undefined"!=typeof document){var t;document.execCommand(e,!1,r||""),u(),null==(t=i.current)||t.focus()}};return(0,a.jsx)("div",{className:"simple-rich-text-editor",style:{border:"1px solid #ccc"},children:(0,a.jsxs)(a.Fragment,{children:[(0,a.jsxs)("div",{className:"toolbar",style:{padding:"8px",borderBottom:"1px solid #ccc",background:"#f5f5f5"},children:[(0,a.jsx)("button",{type:"button",onClick:()=>p("bold"),style:{margin:"0 5px",padding:"3px 8px"},children:(0,a.jsx)("strong",{children:"B"})}),(0,a.jsx)("button",{type:"button",onClick:()=>p("italic"),style:{margin:"0 5px",padding:"3px 8px"},children:(0,a.jsx)("em",{children:"I"})}),(0,a.jsx)("button",{type:"button",onClick:()=>p("underline"),style:{margin:"0 5px",padding:"3px 8px"},children:(0,a.jsx)("u",{children:"U"})}),(0,a.jsx)("button",{type:"button",onClick:()=>p("insertOrderedList"),style:{margin:"0 5px",padding:"3px 8px"},children:"OL"}),(0,a.jsx)("button",{type:"button",onClick:()=>p("insertUnorderedList"),style:{margin:"0 5px",padding:"3px 8px"},children:"UL"}),(0,a.jsx)("button",{type:"button",onClick:()=>{let e=prompt("Enter the link URL");e&&p("createLink",e)},style:{margin:"0 5px",padding:"3px 8px"},children:"Link"})]}),(0,a.jsx)("div",{ref:i,contentEditable:!s,onInput:u,onFocus:()=>c(!0),onBlur:()=>c(!1),style:{padding:"15px",minHeight:l,maxHeight:2*l,overflow:"auto",outline:"none"},"data-placeholder":r?"":d,suppressContentEditableWarning:!0})]})})},l=e=>{let{initContent:r,onChange:t}=e;return(0,a.jsx)(d,{value:r||"",onChange:e=>t(e)})}},29335:(e,r,t)=>{"use strict";t.d(r,{A:()=>C});var a=t(15039),n=t.n(a),d=t(14232),l=t(77346),s=t(37876);let i=d.forwardRef((e,r)=>{let{className:t,bsPrefix:a,as:d="div",...i}=e;return a=(0,l.oU)(a,"card-body"),(0,s.jsx)(d,{ref:r,className:n()(t,a),...i})});i.displayName="CardBody";let o=d.forwardRef((e,r)=>{let{className:t,bsPrefix:a,as:d="div",...i}=e;return a=(0,l.oU)(a,"card-footer"),(0,s.jsx)(d,{ref:r,className:n()(t,a),...i})});o.displayName="CardFooter";var c=t(81764);let u=d.forwardRef((e,r)=>{let{bsPrefix:t,className:a,as:i="div",...o}=e,u=(0,l.oU)(t,"card-header"),p=(0,d.useMemo)(()=>({cardHeaderBsPrefix:u}),[u]);return(0,s.jsx)(c.A.Provider,{value:p,children:(0,s.jsx)(i,{ref:r,...o,className:n()(a,u)})})});u.displayName="CardHeader";let p=d.forwardRef((e,r)=>{let{bsPrefix:t,className:a,variant:d,as:i="img",...o}=e,c=(0,l.oU)(t,"card-img");return(0,s.jsx)(i,{ref:r,className:n()(d?"".concat(c,"-").concat(d):c,a),...o})});p.displayName="CardImg";let x=d.forwardRef((e,r)=>{let{className:t,bsPrefix:a,as:d="div",...i}=e;return a=(0,l.oU)(a,"card-img-overlay"),(0,s.jsx)(d,{ref:r,className:n()(t,a),...i})});x.displayName="CardImgOverlay";let m=d.forwardRef((e,r)=>{let{className:t,bsPrefix:a,as:d="a",...i}=e;return a=(0,l.oU)(a,"card-link"),(0,s.jsx)(d,{ref:r,className:n()(t,a),...i})});m.displayName="CardLink";var f=t(46052);let y=(0,f.A)("h6"),g=d.forwardRef((e,r)=>{let{className:t,bsPrefix:a,as:d=y,...i}=e;return a=(0,l.oU)(a,"card-subtitle"),(0,s.jsx)(d,{ref:r,className:n()(t,a),...i})});g.displayName="CardSubtitle";let h=d.forwardRef((e,r)=>{let{className:t,bsPrefix:a,as:d="p",...i}=e;return a=(0,l.oU)(a,"card-text"),(0,s.jsx)(d,{ref:r,className:n()(t,a),...i})});h.displayName="CardText";let b=(0,f.A)("h5"),N=d.forwardRef((e,r)=>{let{className:t,bsPrefix:a,as:d=b,...i}=e;return a=(0,l.oU)(a,"card-title"),(0,s.jsx)(d,{ref:r,className:n()(t,a),...i})});N.displayName="CardTitle";let j=d.forwardRef((e,r)=>{let{bsPrefix:t,className:a,bg:d,text:o,border:c,body:u=!1,children:p,as:x="div",...m}=e,f=(0,l.oU)(t,"card");return(0,s.jsx)(x,{ref:r,...m,className:n()(a,f,d&&"bg-".concat(d),o&&"text-".concat(o),c&&"border-".concat(c)),children:u?(0,s.jsx)(i,{children:p}):p})});j.displayName="Card";let C=Object.assign(j,{Img:p,Title:N,Subtitle:g,Body:i,Link:m,Text:h,Header:u,Footer:o,ImgOverlay:x})},64628:(e,r,t)=>{(window.__NEXT_P=window.__NEXT_P||[]).push(["/vspace/Form",function(){return t(30318)}])},81764:(e,r,t)=>{"use strict";t.d(r,{A:()=>n});let a=t(14232).createContext(null);a.displayName="CardHeaderContext";let n=a}},e=>{var r=r=>e(e.s=r);e.O(0,[7725,1121,6701,1772,698,7336,2827,8220,2897,318,636,6593,8792],()=>r(64628)),_N_E=e.O()}]);
//# sourceMappingURL=Form-b414073a50ae7167.js.map