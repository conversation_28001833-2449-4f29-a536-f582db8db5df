"use strict";(()=>{var e={};e.id=620,e.ids=[620,636,3220],e.modules={123:e=>{e.exports=require("dom-helpers/removeEventListener")},1332:e=>{e.exports=require("react-custom-scrollbars-2")},1428:e=>{e.exports=import("axios")},1680:e=>{e.exports=require("@restart/ui/utils")},1919:e=>{e.exports=require("react-transition-group/Transition")},3892:e=>{e.exports=require("classnames")},4048:e=>{e.exports=require("react-truncate")},6009:e=>{e.exports=require("dom-helpers/ownerDocument")},6952:e=>{e.exports=require("@restart/ui/Modal")},7374:e=>{e.exports=require("@restart/ui/Dropdown")},8732:e=>{e.exports=require("react/jsx-runtime")},9653:e=>{e.exports=require("@restart/ui/Overlay")},11242:e=>{e.exports=require("redux-persist/integration/react")},11688:e=>{e.exports=require("@restart/hooks/useWillUnmount")},12388:e=>{e.exports=require("dom-helpers/addEventListener")},13364:e=>{e.exports=require("redux-saga/effects")},14062:e=>{e.exports=import("react-redux")},14078:e=>{e.exports=import("swr")},14332:e=>{e.exports=require("uncontrollable")},16116:e=>{e.exports=require("invariant")},18622:e=>{e.exports=require("yup")},19121:e=>{e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},21851:(e,r,t)=>{t.a(e,async(e,s)=>{try{t.r(r),t.d(r,{config:()=>q,default:()=>x,getServerSideProps:()=>g,getStaticPaths:()=>m,getStaticProps:()=>d,reportWebVitals:()=>h,routeModule:()=>w,unstable_getServerProps:()=>S,unstable_getServerSideProps:()=>b,unstable_getStaticParams:()=>v,unstable_getStaticPaths:()=>f,unstable_getStaticProps:()=>P});var a=t(63885),o=t(80237),i=t(81413),n=t(9616),p=t.n(n),u=t(72386),l=t(91058),c=e([u,l]);[u,l]=c.then?(await c)():c;let x=(0,i.M)(l,"default"),d=(0,i.M)(l,"getStaticProps"),m=(0,i.M)(l,"getStaticPaths"),g=(0,i.M)(l,"getServerSideProps"),q=(0,i.M)(l,"config"),h=(0,i.M)(l,"reportWebVitals"),P=(0,i.M)(l,"unstable_getStaticProps"),f=(0,i.M)(l,"unstable_getStaticPaths"),v=(0,i.M)(l,"unstable_getStaticParams"),S=(0,i.M)(l,"unstable_getServerProps"),b=(0,i.M)(l,"unstable_getServerSideProps"),w=new a.PagesRouteModule({definition:{kind:o.A.PAGES,page:"/operation/components/VirtualSpaceAccordian",pathname:"/operation/components/VirtualSpaceAccordian",bundlePath:"",filename:""},components:{App:u.default,Document:p()},userland:l});s()}catch(e){s(e)}})},22326:e=>{e.exports=require("react-dom")},22541:e=>{e.exports=require("redux-persist/lib/storage")},25303:e=>{e.exports=require("@restart/ui/NavItem")},26324:e=>{e.exports=require("warning")},27825:e=>{e.exports=require("lodash")},27910:e=>{e.exports=require("stream")},28217:e=>{e.exports=require("@restart/ui/DropdownItem")},29021:e=>{e.exports=require("fs")},29294:e=>{e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},29628:e=>{e.exports=require("dom-helpers/addClass")},29825:e=>{e.exports=require("prop-types")},29841:e=>{e.exports=require("dom-helpers/hasClass")},30362:e=>{e.exports=require("@restart/ui/DropdownToggle")},33873:e=>{e.exports=require("path")},36653:e=>{e.exports=require("nprogress")},36955:e=>{e.exports=require("@restart/hooks/useBreakpoint")},37766:e=>{e.exports=require("@restart/hooks/useMergedRefs")},38058:(e,r,t)=>{t.a(e,async(e,s)=>{try{t.d(r,{A:()=>x});var a=t(8732),o=t(82015),i=t(19918),n=t.n(i),p=t(56084),u=t(63487),l=t(88751),c=e([u]);u=(c.then?(await c)():c)[0];let x=e=>{let{t:r}=(0,l.useTranslation)("common"),{type:t,id:s}=e,[i,c]=(0,o.useState)([]),[x,d]=(0,o.useState)(!1),[m,g]=(0,o.useState)(0),[q,h]=(0,o.useState)(10),[P]=(0,o.useState)(!1),f={sort:{created_at:"asc"},limit:q,page:1,query:{}},v=[{name:r("Title"),selector:"title",cell:e=>e&&e.title&&e._id?(0,a.jsx)(n(),{href:"/vspace/[...routes]",as:`/vspace/show/${e._id}`,children:e.title}):""},{name:r("Owner"),selector:"users",cell:e=>e&&e.user&&e.user.firstname?`${e.user.firstname} ${e.user.lastname}`:""},{name:r("PublicPrivate"),selector:"visibility",cell:e=>e&&e.visibility?"Public":"Private"},{name:r("NumberofMembers"),selector:"members",cell:e=>e&&e.members?e.members.length:"-"}],S=async e=>{d(!0);let r=await u.A.get(`stats/get${t}WithVspace/${s}`,f);r&&("Operation"===t?c(r.operation):c(r.project),g(r.totalCount),d(!1))},b=async(e,r)=>{f.limit=e,f.page=r,d(!0);let a=await u.A.get(`stats/get${t}WithVspace/${s}`,f);a&&("Operation"===t?c(a.operation):c(a.project),h(e),d(!1))};return(0,o.useEffect)(()=>{S(f)},[]),(0,a.jsx)("div",{children:(0,a.jsx)(p.A,{columns:v,data:i,totalRows:m,loading:x,resetPaginationToggle:P,handlePerRowsChange:b,handlePageChange:e=>{f.limit=q,f.page=e,S(f)}})})};s()}catch(e){s(e)}})},38609:e=>{e.exports=require("react-data-table-component")},39756:e=>{e.exports=import("redux")},40051:e=>{e.exports=require("dom-helpers/removeClass")},40361:e=>{e.exports=require("next/dist/compiled/next-server/pages.runtime.prod.js")},42893:e=>{e.exports=import("react-hot-toast")},43294:e=>{e.exports=require("formik")},50009:e=>{e.exports=require("@restart/ui/DropdownMenu")},54131:e=>{e.exports=import("@fortawesome/free-solid-svg-icons")},56084:(e,r,t)=>{t.d(r,{A:()=>u});var s=t(8732);t(82015);var a=t(38609),o=t.n(a),i=t(88751),n=t(30370);function p(e){let{t:r}=(0,i.useTranslation)("common"),t={rowsPerPageText:r("Rowsperpage")},{columns:a,data:p,totalRows:u,resetPaginationToggle:l,subheader:c,subHeaderComponent:x,handlePerRowsChange:d,handlePageChange:m,rowsPerPage:g,defaultRowsPerPage:q,selectableRows:h,loading:P,pagServer:f,onSelectedRowsChange:v,clearSelectedRows:S,sortServer:b,onSort:w,persistTableHead:A,sortFunction:j,...y}=e,M={paginationComponentOptions:t,noDataComponent:r("NoData"),noHeader:!0,columns:a,data:p||[],dense:!0,paginationResetDefaultPage:l,subHeader:c,progressPending:P,subHeaderComponent:x,pagination:!0,paginationServer:f,paginationPerPage:q||10,paginationRowsPerPageOptions:g||[10,15,20,25,30],paginationTotalRows:u,onChangeRowsPerPage:d,onChangePage:m,selectableRows:h,onSelectedRowsChange:v,clearSelectedRows:S,progressComponent:(0,s.jsx)(n.A,{}),sortIcon:(0,s.jsx)("i",{className:"sort-icon fas fa-exchange-alt"}),sortServer:b,onSort:w,sortFunction:j,persistTableHead:A,className:"rki-table"};return(0,s.jsx)(o(),{...M})}p.defaultProps={subHeader:!1,pagination:!0,totalRows:null,pagServer:!0,onSelectedRowsChange:null,clearSelectedRows:!1,sortServer:!1,persistTableHead:!1};let u=p},57664:e=>{e.exports=require("dom-helpers/scrollbarSize")},58928:e=>{e.exports=require("dom-helpers/canUseDOM")},59717:e=>{e.exports=require("@restart/ui/DropdownContext")},60560:e=>{e.exports=require("dom-helpers/contains")},63033:e=>{e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},65447:e=>{e.exports=require("@restart/ui/Button")},67364:e=>{e.exports=require("@restart/hooks/useCallbackRef")},68455:e=>{e.exports=import("redux-saga")},69722:e=>{e.exports=require("es6-promise")},74075:e=>{e.exports=require("zlib")},74716:e=>{e.exports=require("moment")},74987:e=>{e.exports=require("@restart/ui/ModalManager")},78097:e=>{e.exports=require("next-redux-saga")},78634:e=>{e.exports=require("@restart/ui/Anchor")},80237:(e,r)=>{Object.defineProperty(r,"A",{enumerable:!0,get:function(){return t}});var t=function(e){return e.PAGES="PAGES",e.PAGES_API="PAGES_API",e.APP_PAGE="APP_PAGE",e.APP_ROUTE="APP_ROUTE",e.IMAGE="IMAGE",e}({})},81366:e=>{e.exports=require("redux-auth-wrapper/connectedAuthWrapper")},81413:(e,r)=>{Object.defineProperty(r,"M",{enumerable:!0,get:function(){return function e(r,t){return t in r?r[t]:"then"in r&&"function"==typeof r.then?r.then(r=>e(r,t)):"function"==typeof r&&"default"===t?r:void 0}}})},81521:e=>{e.exports=require("dom-helpers/querySelectorAll")},81895:e=>{e.exports=require("@restart/hooks/useEventCallback")},82015:e=>{e.exports=require("react")},82053:e=>{e.exports=require("@fortawesome/react-fontawesome")},86842:e=>{e.exports=require("@restart/ui/SelectableContext")},87571:e=>{e.exports=require("dom-helpers/transitionEnd")},88751:e=>{e.exports=require("next-i18next")},91058:(e,r,t)=>{t.a(e,async(e,s)=>{try{t.r(r),t.d(r,{default:()=>x});var a=t(8732),o=t(82015),i=t(93024),n=t(82053),p=t(54131),u=t(88751),l=t(38058),c=e([p,l]);[p,l]=c.then?(await c)():c;let x=e=>{let{t:r}=(0,u.useTranslation)("common"),[t,s]=(0,o.useState)(!1);return(0,a.jsx)(a.Fragment,{children:(0,a.jsxs)(i.A.Item,{eventKey:"0",children:[(0,a.jsxs)(i.A.Header,{onClick:()=>s(!t),children:[(0,a.jsx)("div",{className:"cardTitle",children:r("LinkedVirtualSpace")}),(0,a.jsx)("div",{className:"cardArrow",children:t?(0,a.jsx)(n.FontAwesomeIcon,{icon:p.faMinus,color:"#fff"}):(0,a.jsx)(n.FontAwesomeIcon,{icon:p.faPlus,color:"#fff"})})]}),(0,a.jsx)(i.A.Body,{children:(0,a.jsx)(l.A,{id:e.routeData?.routes?.[1]||"",type:"Operation",vspaceData:[],vspaceDataLoading:!1,vspaceDataTotalRows:0,vspaceDataPerPage:10,vspaceDataCurrentPage:1})})]})})};s()}catch(e){s(e)}})},93787:e=>{e.exports=require("redux-persist")},94947:e=>{e.exports=require("@restart/hooks/useTimeout")},96196:e=>{e.exports=require("next-redux-wrapper")},98320:e=>{e.exports=require("@restart/hooks/useIsomorphicEffect")},99460:e=>{e.exports=require("dom-helpers/css")}};var r=require("../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[6089,9216,9616,2386],()=>t(21851));module.exports=s})();