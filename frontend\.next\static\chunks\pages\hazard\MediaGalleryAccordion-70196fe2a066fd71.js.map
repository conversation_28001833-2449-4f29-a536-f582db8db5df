{"version": 3, "file": "static/chunks/pages/hazard/MediaGalleryAccordion-70196fe2a066fd71.js", "mappings": "wMAuKA,MAnIqBA,IACnB,GAAM,GAAEC,CAAC,CAAE,CAAGC,CAAAA,EAAAA,EAAAA,EAAAA,CAAcA,CAAC,MAkIhBC,IAjIP,CAACC,EAAQC,EAAU,CAAGC,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAc,EAAE,EAG9CC,EAAoB,IACxB,IAAMC,EAAc,8EAA8EC,IAAI,CAACC,EAAKC,WAAW,EAEvH,MACE,WAACC,MAAAA,CAAIC,UAAU,4BACb,WAACC,IAAAA,CAAED,UAAU,iBACX,UAACE,IAAAA,UAAGd,EAAE,cAAgB,IAAES,EAAKM,YAAY,EAAI,mBAE9CN,EAAKC,WAAW,EACf,WAACC,MAAAA,CAAIC,UAAU,wBACb,UAACC,IAAAA,UAAE,UAACC,IAAAA,UAAGd,EAAE,cACRO,EACC,WAACI,MAAAA,WACC,UAACK,EAAAA,CAAeA,CAAAA,CAACC,KAAMC,EAAAA,GAAMA,CAAEC,KAAK,KAAKC,MAAM,OAAOR,UAAU,SAChE,UAACS,IAAAA,CAAET,UAAU,cAAcU,KAAMb,EAAKC,WAAW,CAAEa,OAAO,SAASC,IAAI,+BACpEf,EAAKC,WAAW,MAIrB,UAACC,MAAAA,UACC,UAACE,IAAAA,CAAED,UAAU,YAAYa,MAAO,CAAEC,UAAW,WAAY,WACtDjB,EAAKC,WAAW,QAM1BD,EAAKkB,YAAY,EAChB,WAACC,EAAAA,CAAMA,CAAAA,CAAChB,UAAU,qCAAqCU,KAAMb,EAAKkB,YAAY,WAC3E3B,EAAE,YACH,UAACgB,EAAAA,CAAeA,CAAAA,CAACC,KAAMY,EAAAA,GAAUA,CAAEV,KAAK,KAAKP,UAAU,cAKjE,EA6CA,MA3CAkB,CAAAA,EAAAA,EAAAA,SAAAA,CAASA,CAAC,KACR,IAAMC,EAA8B,EAAE,CACtChC,GAASA,EAAMiC,OAAO,EAAIC,MAAMC,OAAO,CAACnC,EAAMiC,OAAO,GAAKjC,EAAMiC,OAAO,CAACG,GAAG,CAAC,CAAC1B,EAAM2B,KACjF,IACIC,EADEC,EAAW7B,GAAQA,EAAK8B,IAAI,CAACC,KAAK,CAAC,KAAKC,GAAG,GAGjD,OAAQH,GACN,IAAK,MACL,IAAK,MACL,IAAK,OACL,IAAK,MACHD,EAAS,GAAwC5B,MAAAA,CAArCiC,8BAAsB,CAAC,gBAAuB,OAATjC,EAAKkC,GAAG,EACzD,KACF,KAAK,MACHN,EAAS,gCACT,KACF,KAAK,OACHA,EAAS,iCACT,KACF,KAAK,MACL,IAAK,OACHA,EAAS,gCACT,KACF,SACEA,EAAS,iCACb,CAEA,IAAMO,EAAQ,CAAc,SAAbN,GAAoC,QAAbA,GAAmC,QAAbA,GAAmC,SAAbA,CAAa,CAAK,EAC/F,GAA4C7B,MAAAA,CAAzCiC,8BAAsB,CAAC,oBAA2B,OAATjC,EAAKkC,GAAG,EACnDE,EAAQ,GAAqE,OAAlEpC,GAAQA,EAAKqC,aAAa,CAAGrC,EAAKqC,aAAa,CAAG,iBAC7DC,EAAehD,EAAMiD,WAAW,EAAIf,MAAMC,OAAO,CAACnC,EAAMiD,WAAW,GACpEjD,EAAMiD,WAAW,CAACC,MAAM,CAAG,EAAIlD,EAAMiD,WAAW,CAACZ,EAAE,CAAG,GAE3DL,EAAemB,IAAI,CAAC,CAClBC,IAAKd,EACL3B,YAAaqC,EACbhC,aAAc8B,EACdlB,aAAciB,CAChB,EACF,GACAxC,EAAU2B,EACZ,EAAG,CAAChC,EAAM,EAGR,UAACY,MAAAA,UACER,GAA4B,IAAlBA,EAAO8C,MAAM,CACtB,UAACtC,MAAAA,CAAIC,UAAU,wCACb,UAACC,IAAAA,CAAED,UAAU,wDAAgDZ,EAAE,qBAGjE,UAACoD,EAAAA,EAAQA,CAAAA,CACPC,YAAY,EACZC,YAAY,EACZC,gBAAgB,EAChBC,cAAc,EACdC,mBAAmB,EACnBC,UAAU,EACVC,aAAa,EACbC,WAAW,EACXC,eAAe,EACfC,cAAc,EACdC,aAAc,IACZ5D,EAAOgC,GAAG,CAAC,CAAC1B,EAAMuD,IAChB,UAACC,MAAAA,CAECd,IAAK1C,EAAK0C,GAAG,CACbe,IAAK,aAAuB,OAAVF,EAAQ,GAC1BvC,MAAO,CAAE0C,MAAO,OAAQC,OAAQ,OAAQC,UAAW,OAAQ,GAHtDL,aAQV7D,EAAOgC,GAAG,CAAC,CAAC1B,EAAMuD,IACjB,WAACrD,MAAAA,WACC,UAACsD,MAAAA,CACCd,IAAK1C,EAAK0C,GAAG,CACbe,IAAKzD,EAAKM,YAAY,EAAI,gBAC1BU,MAAO,CAAE6C,UAAW,QAASD,UAAW,SAAU,IAEnD/D,EAAkBG,KANXuD,OActB,mBCpKA,4CACA,gCACA,WACA,OAAe,EAAQ,KAAqD,CAC5E,EACA,SAFsB,sICmCtB,MAxB8B,IAC1B,GAAM,GAAEhE,CAAC,CAAE,CAAGC,CAAAA,EAAAA,EAAAA,EAAAA,CAAcA,CAAC,MAuBlBsE,IAtBL,CAACC,EAASC,EAAW,CAAGpE,CAAAA,EAAAA,EAAAA,MAsBEkE,EAtBFlE,CAAQA,EAAC,GACvC,MACI,+BACI,WAACqE,EAAAA,CAASA,CAACC,IAAI,EAACC,SAAS,cACvB,WAACF,EAAAA,CAASA,CAACG,MAAM,EAACC,QAAS,IAAML,EAAW,CAACD,aAC3C,UAAC7D,MAAAA,CAAIC,UAAU,qBAAaZ,EAAE,kBAC9B,UAACW,MAAAA,CAAIC,UAAU,qBACZ4D,EACC,UAACxD,EAAAA,CAAeA,CAAAA,CAACC,KAAM8D,EAAAA,GAAOA,CAAE3D,MAAM,SAEtC,UAACJ,EAAAA,CAAeA,CAAAA,CAACC,KAAM+D,EAAAA,GAAMA,CAAE5D,MAAM,cAI3C,UAACsD,EAAAA,CAASA,CAACO,IAAI,WACb,UAAC/E,EAAAA,CAAWA,CAAAA,CAAC8B,QAASjC,EAAMI,MAAM,CAAE6C,YAAajD,EAAMsC,MAAM,SAK7E", "sources": ["webpack://_N_E/./components/common/ReactImages.tsx", "webpack://_N_E/?41a2", "webpack://_N_E/./pages/hazard/MediaGalleryAccordion.tsx"], "sourcesContent": ["\r\n//Import Library\r\nimport React, { useState, useEffect } from 'react';\r\nimport { Carousel } from 'react-responsive-carousel';\r\nimport { FontAwesomeIcon } from \"@fortawesome/react-fontawesome\";\r\nimport {\r\n  faLink, faDownload\r\n} from \"@fortawesome/free-solid-svg-icons\";\r\nimport { Button } from \"react-bootstrap\";\r\n\r\n//Import services/components\r\nimport { useTranslation } from 'next-i18next';\r\n\r\n// Import CSS for react-responsive-carousel\r\nimport \"react-responsive-carousel/lib/styles/carousel.min.css\";\r\n\r\n// Define types for image items\r\ninterface ImageItem {\r\n  src: string;\r\n  description: string;\r\n  originalName: string;\r\n  downloadLink: string | false;\r\n}\r\n\r\ninterface ReactImagesProps {\r\n  gallery?: Array<{\r\n    _id: string;\r\n    name: string;\r\n    original_name?: string;\r\n    src?: string;\r\n    caption?: string;\r\n    alt?: string;\r\n  }> | boolean;\r\n  imageSource?: string[] | boolean;\r\n}\r\n\r\nconst ReactImages = (props: ReactImagesProps) => {\r\n  const { t } = useTranslation('common');\r\n  const [images, setImages] = useState<ImageItem[]>([]);\r\n\r\n  // Render image description and metadata\r\n  const renderImageLegend = (item: ImageItem) => {\r\n    const isValidLink = /(http|https):\\/\\/(\\w+:{0,1}\\w*)?(\\S+)(:[0-9]+)?(\\/|\\/([\\w#!:.?+=&%!\\-\\/]))?/.test(item.description);\r\n\r\n    return (\r\n      <div className=\"carousel-legend\">\r\n        <p className=\"lead\">\r\n          <b>{t(\"Filename\")}</b> {item.originalName || \"No Name found\"}\r\n        </p>\r\n        {item.description && (\r\n          <div className=\"source_link\">\r\n            <p><b>{t(\"Source\")}</b></p>\r\n            {isValidLink ? (\r\n              <div>\r\n                <FontAwesomeIcon icon={faLink} size=\"1x\" color=\"#999\" className=\"me-1\" />\r\n                <a className=\"source_link\" href={item.description} target=\"_blank\" rel=\"noopener noreferrer\">\r\n                  {item.description}\r\n                </a>\r\n              </div>\r\n            ) : (\r\n              <div>\r\n                <p className=\"ps-0 py-0\" style={{ wordBreak: \"break-all\" }}>\r\n                  {item.description}\r\n                </p>\r\n              </div>\r\n            )}\r\n          </div>\r\n        )}\r\n        {item.downloadLink && (\r\n          <Button className=\"btn btn-success mt-2 btn--download\" href={item.downloadLink}>\r\n            {t(\"Download\")}\r\n            <FontAwesomeIcon icon={faDownload} size=\"1x\" className=\"ms-1\" />\r\n          </Button>\r\n        )}\r\n      </div>\r\n    );\r\n  };\r\n\r\n  useEffect(() => {\r\n    const carouselImages: ImageItem[] = [];\r\n    props && props.gallery && Array.isArray(props.gallery) && props.gallery.map((item, i) => {\r\n      const fileType = item && item.name.split('.').pop();\r\n      let imgSrc;\r\n\r\n      switch (fileType) {\r\n        case \"JPG\":\r\n        case \"jpg\":\r\n        case \"jpeg\":\r\n        case \"png\":\r\n          imgSrc = `${process.env.API_SERVER}/image/show/${item._id}`;\r\n          break;\r\n        case \"pdf\":\r\n          imgSrc = \"/images/fileIcons/pdfFile.png\";\r\n          break;\r\n        case \"docx\":\r\n          imgSrc = \"/images/fileIcons/wordFile.png\";\r\n          break;\r\n        case \"xls\":\r\n        case 'xlsx':\r\n          imgSrc = \"/images/fileIcons/xlsFile.png\";\r\n          break;\r\n        default:\r\n          imgSrc = \"/images/fileIcons/otherFile.png\";\r\n      }\r\n\r\n      const _link = (fileType === \"docx\" || fileType === \"pdf\" || fileType === \"xls\" || fileType === \"xlsx\")\r\n        && `${process.env.API_SERVER}/files/download/${item._id}`;\r\n      const _name = `${item && item.original_name ? item.original_name : \"No Name found\"}`;\r\n      const _description = props.imageSource && Array.isArray(props.imageSource)\r\n        && props.imageSource.length > 0 ? props.imageSource[i] : \"\";\r\n\r\n      carouselImages.push({\r\n        src: imgSrc,\r\n        description: _description,\r\n        originalName: _name,\r\n        downloadLink: _link\r\n      });\r\n    });\r\n    setImages(carouselImages);\r\n  }, [props]);\r\n\r\n  return (\r\n    <div>\r\n      {images && images.length === 0 ? (\r\n        <div className=\"border border-info my-3 mx-0\">\r\n          <p className=\"d-flex d-flex justify-content-center p-2 m-0\">{t(\"NoFilesFound!\")}</p>\r\n        </div>\r\n      ) : (\r\n        <Carousel\r\n          showThumbs={true}\r\n          showStatus={true}\r\n          showIndicators={true}\r\n          infiniteLoop={true}\r\n          useKeyboardArrows={true}\r\n          autoPlay={false}\r\n          stopOnHover={true}\r\n          swipeable={true}\r\n          dynamicHeight={false}\r\n          emulateTouch={true}\r\n          renderThumbs={() =>\r\n            images.map((item, index) => (\r\n              <img\r\n                key={index}\r\n                src={item.src}\r\n                alt={`Thumbnail ${index + 1}`}\r\n                style={{ width: '60px', height: '60px', objectFit: 'cover' }}\r\n              />\r\n            ))\r\n          }\r\n        >\r\n          {images.map((item, index) => (\r\n            <div key={index}>\r\n              <img\r\n                src={item.src}\r\n                alt={item.originalName || \"Gallery image\"}\r\n                style={{ maxHeight: '500px', objectFit: 'contain' }}\r\n              />\r\n              {renderImageLegend(item)}\r\n            </div>\r\n          ))}\r\n        </Carousel>\r\n      )}\r\n    </div>\r\n  );\r\n\r\n}\r\n\r\nexport default ReactImages;\r\n", "\n    (window.__NEXT_P = window.__NEXT_P || []).push([\n      \"/hazard/MediaGalleryAccordion\",\n      function () {\n        return require(\"private-next-pages/hazard/MediaGalleryAccordion.tsx\");\n      }\n    ]);\n    if(module.hot) {\n      module.hot.dispose(function () {\n        window.__NEXT_P.push([\"/hazard/MediaGalleryAccordion\"])\n      });\n    }\n  ", "//Import Library\r\nimport { faMinus, faPlus } from \"@fortawesome/free-solid-svg-icons\";\r\nimport { FontAwesomeIcon } from \"@fortawesome/react-fontawesome\";\r\nimport { Card, Accordion } from \"react-bootstrap\";\r\nimport { useState } from \"react\";\r\n\r\n//Import services/components\r\nimport ReactImages from \"../../components/common/ReactImages\";\r\nimport { useTranslation } from 'next-i18next';\r\n\r\ninterface MediaGalleryAccordionProps {\r\n  images: any[];\r\n  imgSrc: string[];\r\n}\r\n\r\nconst MediaGalleryAccordion = (props: MediaGalleryAccordionProps) => {\r\n    const { t } = useTranslation('common');\r\n    const [section, setSection] = useState(false);\r\n    return(\r\n        <>\r\n            <Accordion.Item eventKey=\"0\">\r\n              <Accordion.Header onClick={() => setSection(!section)}>\r\n                <div className=\"cardTitle\">{t(\"mediaGallery\")}</div>\r\n                <div className=\"cardArrow\">\r\n                  {section ? (\r\n                    <FontAwesomeIcon icon={faMinus} color=\"#fff\" />\r\n                  ) : (\r\n                    <FontAwesomeIcon icon={faPlus} color=\"#fff\" />\r\n                  )}\r\n                </div>\r\n              </Accordion.Header>\r\n              <Accordion.Body>\r\n                <ReactImages gallery={props.images} imageSource={props.imgSrc} />\r\n              </Accordion.Body>\r\n            </Accordion.Item>\r\n        </>\r\n    );\r\n};\r\n\r\nexport default MediaGalleryAccordion;"], "names": ["props", "t", "useTranslation", "ReactImages", "images", "setImages", "useState", "renderImageLegend", "isValidLink", "test", "item", "description", "div", "className", "p", "b", "originalName", "FontAwesomeIcon", "icon", "faLink", "size", "color", "a", "href", "target", "rel", "style", "wordBreak", "downloadLink", "<PERSON><PERSON>", "faDownload", "useEffect", "carouselImages", "gallery", "Array", "isArray", "map", "i", "imgSrc", "fileType", "name", "split", "pop", "process", "_id", "_link", "_name", "original_name", "_description", "imageSource", "length", "push", "src", "Carousel", "showThumbs", "showStatus", "showIndicators", "infiniteLoop", "useKeyboardArrows", "autoPlay", "stopOnHover", "swipeable", "dynamicHeight", "emulate<PERSON><PERSON><PERSON>", "renderThumbs", "index", "img", "alt", "width", "height", "objectFit", "maxHeight", "MediaGalleryAccordion", "section", "setSection", "Accordion", "<PERSON><PERSON>", "eventKey", "Header", "onClick", "faMinus", "faPlus", "Body"], "sourceRoot": "", "ignoreList": []}