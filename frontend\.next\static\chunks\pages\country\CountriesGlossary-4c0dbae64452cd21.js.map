{"version": 3, "file": "static/chunks/pages/country/CountriesGlossary-4c0dbae64452cd21.js", "mappings": "qHAAO,IAAMA,EAAwB,CACnC,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,OACD,CAAC,EAEmC,CACnC,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,MACD,CAAC,gGC5BF,MArB0B,IACxB,GAAM,eAAEC,CAAa,KAoBRC,aApBUC,CAAgB,CAAE,CAAGC,CAoBdF,CAnBxB,CAmByB,KAnBvBG,CAAI,CAAE,CAAGC,CAAAA,EAAAA,EAAAA,EAAAA,CAAcA,CAAC,UAC1BC,EAA6B,MAAjBF,EAAKG,QAAQ,CAAWC,EAAAA,CAAqBA,CAAGT,EAAAA,CAAqBA,CACvF,MACE,UAACU,MAAAA,CAAIC,UAAU,6BACb,UAACC,KAAAA,UACAL,EAAUM,GAAG,CAAC,CAACC,EAAMC,IAElB,UAACC,KAAAA,UACC,UAACC,IAAAA,CAAEC,QAAS,IAAMf,EAAiBW,GAAOH,UAAW,GAA4C,OAAzC,GAAkBG,EAAQ,SAAU,eACzFA,KAFIC,OAUnB,mBC3BA,4CACA,6BACA,WACA,OAAe,EAAQ,KAAkD,CACzE,EACA,SAFsB", "sources": ["webpack://_N_E/./data/alphabet.tsx", "webpack://_N_E/./pages/country/CountriesGlossary.tsx", "webpack://_N_E/?64ce"], "sourcesContent": ["export const ALPHABETIC_FILTERS_DE = [\r\n  \"A\",\r\n  \"B\",\r\n  \"C\",\r\n  \"D\",\r\n  \"E\",\r\n  \"F\",\r\n  \"G\",\r\n  \"H\",\r\n  \"I\",\r\n  \"J\",\r\n  \"K\",\r\n  \"L\",\r\n  \"M\",\r\n  \"N\",\r\n  \"O\",\r\n  \"P\",\r\n  \"Q\",\r\n  \"R\",\r\n  \"S\",\r\n  \"T\",\r\n  \"U\",\r\n  \"V\",\r\n  \"W\",\r\n  \"X\",\r\n  \"Y\",\r\n  \"Z\",\r\n  \"Alle\"\r\n];\r\n\r\nexport const ALPHABETIC_FILTERS_EN = [\r\n  \"A\",\r\n  \"B\",\r\n  \"C\",\r\n  \"D\",\r\n  \"E\",\r\n  \"F\",\r\n  \"G\",\r\n  \"H\",\r\n  \"I\",\r\n  \"J\",\r\n  \"K\",\r\n  \"L\",\r\n  \"M\",\r\n  \"N\",\r\n  \"O\",\r\n  \"P\",\r\n  \"Q\",\r\n  \"R\",\r\n  \"S\",\r\n  \"T\",\r\n  \"U\",\r\n  \"V\",\r\n  \"W\",\r\n  \"X\",\r\n  \"Y\",\r\n  \"Z\",\r\n  \"All\"\r\n];\r\n\r\n", "//Import services/components\r\nimport { ALPHABETIC_FILTERS_EN, ALPHABETIC_FILTERS_DE } from '../../data/alphabet';\r\nimport { useTranslation } from 'next-i18next';\r\n\r\ninterface CountriesGlossaryProps {\r\n  selectedAlpha: string;\r\n  setselectedAlpha: (alpha: string) => void;\r\n}\r\n\r\nconst CountriesGlossary = (props: CountriesGlossaryProps) => {\r\n  const { selectedAlpha, setselectedAlpha } = props;\r\n  const { i18n } = useTranslation('common');\r\n  const alphabets = i18n.language == 'en' ? ALPHABETIC_FILTERS_EN : ALPHABETIC_FILTERS_DE;\r\n  return (\r\n    <div className=\"alphabetContainer\">\r\n      <ul>\r\n      {alphabets.map((item, i) => {\r\n        return (\r\n          <li key={i}>\r\n            <a onClick={() => setselectedAlpha(item)} className={`${(selectedAlpha == item) ? 'active': null}`}>\r\n              {item}\r\n            </a>\r\n          </li>\r\n        )\r\n      })}\r\n      </ul>\r\n    </div>\r\n  )\r\n}\r\n\r\nexport default CountriesGlossary;", "\n    (window.__NEXT_P = window.__NEXT_P || []).push([\n      \"/country/CountriesGlossary\",\n      function () {\n        return require(\"private-next-pages/country/CountriesGlossary.tsx\");\n      }\n    ]);\n    if(module.hot) {\n      module.hot.dispose(function () {\n        window.__NEXT_P.push([\"/country/CountriesGlossary\"])\n      });\n    }\n  "], "names": ["ALPHABETIC_FILTERS_DE", "<PERSON><PERSON><PERSON><PERSON>", "CountriesGlossary", "setselectedAlpha", "props", "i18n", "useTranslation", "alphabets", "language", "ALPHABETIC_FILTERS_EN", "div", "className", "ul", "map", "item", "i", "li", "a", "onClick"], "sourceRoot": "", "ignoreList": []}