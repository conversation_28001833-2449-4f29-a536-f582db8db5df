(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[4063],{11778:(e,a,s)=>{"use strict";s.r(a),s.d(a,{__N_SSG:()=>I,default:()=>P});var t=s(37876),n=s(14232),l=s(31777),i=s(49589),r=s(56970),o=s(37784),c=s(29335),d=s(33939),m=s(29504),u=s(60282),x=s(21772),h=s(11041),g=s(8076),p=s(52579),j=s(52027),A=s(19957),f=s(53718),y=s(31753),v=s(93131),b=s.n(v),N=s(7940),C=s.n(N),w=s(31195),k=s(97685);let S=e=>{let{isOpen:a,onModalClose:s,image:l}=e,{t:i}=(0,y.Bd)("common"),c=i("setInfo.Choosefile"),[d,m]=(0,n.useState)(1),[x,h]=(0,n.useState)(0),[g,p]=(0,n.useState)(""),[j,A]=(0,n.useState)(null),v=(0,n.useRef)(null),N=async()=>{let e=(e=>{let a=e.split(","),s=a[0].match(/:(.*?);/),t=s?s[1]:"",n=atob(a[1]),l=n.length,i=new Uint8Array(l);for(;l--;)i[l]=n.charCodeAt(l);return new Blob([i],{type:t})})(v.current.getImageScaledToCanvas().toDataURL("image/jpeg",.8)),a=new FormData;a.append("file",e,g);try{let e=await f.A.post("/image",a,{"Content-Type":"multipart/form-data"});e&&e._id&&await f.A.post("/users/updateProfile",{image:e._id})&&k.Ay.success(i("setInfo.ProfileUpdatedSuccessfully"))}catch(e){throw e instanceof Error?e:Error("Unknown error occurred")}s(!1),A(null),p(c),m(1)};return(0,t.jsx)(t.Fragment,{children:(0,t.jsx)("div",{children:(0,t.jsxs)(w.A,{show:a,size:"lg","aria-labelledby":"ProfileEdit",onHide:()=>s(!1),centered:!0,children:[(0,t.jsx)(w.A.Header,{children:(0,t.jsx)(w.A.Title,{id:"contained-modal-title-vcenter",children:i("setInfo.EditYourImage")})}),(0,t.jsxs)(w.A.Body,{children:[(0,t.jsx)("div",{className:"d-flex flex-column justify-content-center align-items-center",children:(0,t.jsx)(b(),{ref:v,borderRadius:100,scale:d,rotate:x,color:[0,0,0,.6],image:j||"/images/rkiProfile.jpg"})}),(0,t.jsx)("div",{className:"my-3 mx-2",children:(0,t.jsxs)(r.A,{className:"align-items-center mb-4",children:[(0,t.jsx)(o.A,{sm:2,md:2,lg:2,className:"pe-0",children:(0,t.jsx)("b",{children:i("setInfo.Uploadaimage")})}),(0,t.jsx)(o.A,{sm:10,md:10,lg:10,children:(0,t.jsxs)("div",{className:"form-control custom-file position-relative",children:[(0,t.jsx)("input",{type:"file",name:"files",className:"form-control-input form-control custom-file-input",accept:"image/*",id:"customFile",onChange:e=>{e.target.files&&e.target.files[0]&&(p(e.target.files[0].name),A(URL.createObjectURL(e.target.files[0])))}}),(0,t.jsx)("label",{className:"custom-file-label form-control-label w-100","data-browse":i("Browse"),children:i("setInfo.Choosefile")})]})})]})}),(0,t.jsx)("div",{className:"my-3 mx-2",children:(0,t.jsxs)(r.A,{children:[(0,t.jsx)(o.A,{sm:2,md:2,lg:2,className:"pe-0",children:(0,t.jsx)("b",{children:i("setInfo.Zoom")})}),(0,t.jsx)(o.A,{sm:4,md:4,lg:4,children:(0,t.jsx)(C(),{value:d,tooltip:"auto",min:1,max:10,step:.1,variant:"primary",onChange:e=>m(Number(e.target.value))})}),(0,t.jsx)(o.A,{sm:2,md:2,lg:2,className:"pe-0",children:(0,t.jsx)("b",{children:i("setInfo.Rotate")})}),(0,t.jsx)(o.A,{sm:4,md:4,lg:4,children:(0,t.jsx)(C(),{value:x,tooltip:"auto",tooltipLabel:e=>"".concat(e,"\xb0"),min:0,max:360,variant:"primary",onChange:e=>h(Number(e.target.value))})})]})})]}),(0,t.jsxs)(w.A.Footer,{children:[(0,t.jsx)(u.A,{onClick:N,children:i("setInfo.SaveChanges")}),(0,t.jsx)(u.A,{variant:"danger",onClick:()=>{s(!1),A(null)},children:i("Cancel")})]})]})})})};var _=s(69600),I=!0;let P=(0,l.Ng)()(e=>{let{t:a}=(0,y.Bd)("common"),[s,l]=(0,n.useState)(!1),[v,b]=(0,n.useState)(!1),[N,C]=(0,n.useState)(!1),w=e=>{l(e),b(!1)},[k,I]=(0,n.useState)({username:"",institution:"",email:"",image:null,firstname:"",lastname:"",position:""});(0,n.useEffect)(()=>{(async e=>{let a=await f.A.post("/users/getLoggedUser",e);a&&(a.image=a.image&&a.image._id?"".concat("http://localhost:3001/api/v1","/image/show/").concat(a.image._id):"/images/rkiProfile.jpg",I(e=>({...e,...a})))})({})},[s,N]);let P=a=>{e.dispatch((0,A.js)()),C(a)};return(0,t.jsxs)(i.A,{fluid:!0,className:"p-0",children:[(0,t.jsx)(r.A,{children:(0,t.jsx)(o.A,{xs:12,children:(0,t.jsx)(_.A,{title:a("setInfo.myprofile")})})}),(0,t.jsx)(r.A,{children:(0,t.jsx)(o.A,{xs:!0,lg:12,children:(0,t.jsx)(i.A,{fluid:!0,children:(0,t.jsx)(r.A,{children:(0,t.jsx)(o.A,{children:(0,t.jsxs)(c.A,{className:" mt-3 form--outline profile--card",children:[(0,t.jsxs)("div",{className:"row mx-3 mt-4",children:[(0,t.jsx)("div",{className:"col-lg-3 col-md-4 text-center",children:k.image?(0,t.jsx)(t.Fragment,{children:(0,t.jsx)("img",{className:"imgStyle",src:k&&k.image?k.image:""})}):(0,t.jsx)(d.A,{className:"text-center m-5",animation:"grow",variant:"dark"})}),(0,t.jsx)("div",{className:"col-md-9 w-100",children:(0,t.jsx)(m.A,{children:(0,t.jsxs)("div",{children:[(0,t.jsxs)(m.A.Group,{as:r.A,controlId:"username",className:"align-items-center mb-3",children:[(0,t.jsx)(m.A.Label,{column:!0,md:"3",xs:"5",lg:"2",className:"px-1",children:a("setInfo.username")}),(0,t.jsx)(o.A,{xs:"1",children:(0,t.jsx)("span",{children:":"})}),(0,t.jsx)(o.A,{md:"8",xs:"5",lg:"9",children:(0,t.jsx)(m.A.Control,{plaintext:!0,readOnly:!0,defaultValue:k.username})})]}),(0,t.jsxs)(m.A.Group,{as:r.A,controlId:"name",className:"align-items-center mb-3",children:[(0,t.jsx)(m.A.Label,{column:!0,md:"3",xs:"5",lg:"2",className:"px-1",children:a("setInfo.name")}),(0,t.jsx)(o.A,{xs:"1",children:(0,t.jsx)("span",{children:":"})}),(0,t.jsx)(o.A,{md:"8",xs:"5",lg:"9",children:(0,t.jsx)(m.A.Control,{plaintext:!0,readOnly:!0,defaultValue:k&&k.firstname?"".concat(k.firstname," ").concat(k.lastname):""})})]}),(0,t.jsxs)(m.A.Group,{as:r.A,controlId:"position",className:"align-items-center mb-3",children:[(0,t.jsx)(m.A.Label,{column:!0,md:"3",xs:"5",lg:"2",className:"px-1",children:a("setInfo.position")}),(0,t.jsx)(o.A,{xs:"1",children:(0,t.jsx)("span",{children:":"})}),(0,t.jsx)(o.A,{md:"8",xs:"5",lg:"9",children:(0,t.jsx)(m.A.Control,{plaintext:!0,readOnly:!0,defaultValue:k&&k.position?k.position:""})})]}),(0,t.jsxs)(m.A.Group,{as:r.A,controlId:"Organization",className:"align-items-center mb-3",children:[(0,t.jsx)(m.A.Label,{column:!0,md:"3",xs:"5",lg:"2",className:"px-1",children:a("setInfo.organisation")}),(0,t.jsx)(o.A,{xs:"1",children:(0,t.jsx)("span",{children:":"})}),(0,t.jsx)(o.A,{md:"8",xs:"5",lg:"9",children:(0,t.jsx)(m.A.Control,{readOnly:!0,plaintext:!0,defaultValue:k.institution&&k.institution.title})})]}),(0,t.jsx)(S,{image:k.image,isOpen:N,onModalClose:e=>P(e)}),(0,t.jsxs)(m.A.Group,{as:r.A,controlId:"Email",className:"align-items-center mb-3",children:[(0,t.jsx)(m.A.Label,{column:!0,md:"3",xs:"5",lg:"2",className:"px-1",children:a("setInfo.email")}),(0,t.jsx)(o.A,{xs:"1",children:(0,t.jsx)("span",{children:":"})}),(0,t.jsx)(o.A,{md:"8",xs:"5",lg:"9",children:(0,t.jsx)(m.A.Control,{plaintext:!0,readOnly:!0,defaultValue:k.email})})]}),(0,t.jsxs)(m.A.Group,{as:r.A,controlId:"consent",className:"align-items-center mb-3",children:[(0,t.jsx)(m.A.Label,{column:!0,md:"3",xs:"5",lg:"2",className:"px-1",children:a("setInfo.Consent")}),(0,t.jsx)(o.A,{xs:"1",children:(0,t.jsx)("span",{children:":"})}),(0,t.jsx)(o.A,{md:"8",xs:"5",lg:"9",children:(0,t.jsx)(x.g,{className:"clickable",icon:h.rQb,color:"#293F92",onClick:()=>b(!0)})})]})]})})})]}),(0,t.jsxs)("div",{className:"mb-3 mx-3",children:[(0,t.jsxs)(u.A,{onClick:()=>{C(!0)},variant:"secondary",children:[(0,t.jsx)(x.g,{icon:h.MT7}),"\xa0",a("setInfo.editmyimage")]}),(0,t.jsxs)(u.A,{className:"float-right",onClick:()=>l(!0),variant:"secondary",children:[(0,t.jsx)(x.g,{icon:h.MT7}),"\xa0",a("setInfo.editmyprofile")]})]})]})})})})})}),s&&(0,t.jsx)(g.default,{data:k,isOpen:s,manageClose:e=>w(e)}),(0,t.jsxs)(o.A,{xs:12,className:"mt-3",children:[(0,t.jsx)(_.A,{title:a("MyBookmarks")}),(0,t.jsx)(j.default,{})]}),v&&(0,t.jsx)(p.default,{isOpen:v,manageClose:e=>w(e),id:k._id})]})})},22797:(e,a,s)=>{"use strict";s.r(a),s.d(a,{default:()=>d});var t=s(37876);s(14232);var n=s(49589),l=s(56970),i=s(37784),r=s(29504),o=s(67814),c=s(31753);let d=e=>{let{filterText:a,onFilter:s,onClear:d,handleGroupHandler:m,groupType:u,options:x}=e,{t:h}=(0,c.Bd)("common");return(0,t.jsx)(n.A,{fluid:!0,className:"p-0",children:(0,t.jsx)(l.A,{children:(0,t.jsx)(i.A,{xs:4,md:4,lg:4,children:(0,t.jsx)(r.A.Group,{style:{maxWidth:"800px"},children:(0,t.jsx)(o.KF,{overrideStrings:{selectSomeItems:h("ChooseGroup"),allItemsAreSelected:"All Groups are Selected"},options:x,value:u,onChange:m,className:"choose-group",labelledBy:"Select Network"})})})})})}},50749:(e,a,s)=>{"use strict";s.d(a,{A:()=>o});var t=s(37876);s(14232);var n=s(89773),l=s(31753),i=s(5507);function r(e){let{t:a}=(0,l.Bd)("common"),s={rowsPerPageText:a("Rowsperpage")},{columns:r,data:o,totalRows:c,resetPaginationToggle:d,subheader:m,subHeaderComponent:u,handlePerRowsChange:x,handlePageChange:h,rowsPerPage:g,defaultRowsPerPage:p,selectableRows:j,loading:A,pagServer:f,onSelectedRowsChange:y,clearSelectedRows:v,sortServer:b,onSort:N,persistTableHead:C,sortFunction:w,...k}=e,S={paginationComponentOptions:s,noDataComponent:a("NoData"),noHeader:!0,columns:r,data:o||[],dense:!0,paginationResetDefaultPage:d,subHeader:m,progressPending:A,subHeaderComponent:u,pagination:!0,paginationServer:f,paginationPerPage:p||10,paginationRowsPerPageOptions:g||[10,15,20,25,30],paginationTotalRows:c,onChangeRowsPerPage:x,onChangePage:h,selectableRows:j,onSelectedRowsChange:y,clearSelectedRows:v,progressComponent:(0,t.jsx)(i.A,{}),sortIcon:(0,t.jsx)("i",{className:"sort-icon fas fa-exchange-alt"}),sortServer:b,onSort:N,sortFunction:w,persistTableHead:C,className:"rki-table"};return(0,t.jsx)(n.Ay,{...S})}r.defaultProps={subHeader:!1,pagination:!0,totalRows:null,pagServer:!0,onSelectedRowsChange:null,clearSelectedRows:!1,sortServer:!1,persistTableHead:!1};let o=r},52027:(e,a,s)=>{"use strict";s.r(a),s.d(a,{default:()=>p});var t=s(37876),n=s(14232),l=s(31777),i=s(82851),r=s.n(i),o=s(48230),c=s.n(o),d=s(31195),m=s(60282),u=s(22797),x=s(50749),h=s(53718),g=s(31753);let p=(0,l.Ng)(e=>e)(e=>{let a=[{value:"institution",label:"Organisations"},{value:"operation",label:"Operations"},{value:"project",label:"Projects"},{value:"event",label:"Events"},{value:"vspace",label:"Virtual Spaces"}],[s,l]=(0,n.useState)([]),[,i]=(0,n.useState)(!1),[o,p]=(0,n.useState)(!1),[j,A]=(0,n.useState)(0),[f,y]=(0,n.useState)(10),[v,b]=(0,n.useState)(""),[N,C]=(0,n.useState)(!1),[w,k]=(0,n.useState)(a),[S,_]=(0,n.useState)({}),{t:I}=(0,g.Bd)("common"),P=async e=>{e&&e._id&&_(e._id),p(!0)},R={sort:{created_at:"asc"},populate:{path:"entity_id",select:"title"},lean:!0,limit:f,page:1,query:{user:e.user&&e.user._id?e.user._id:""}},B=[{name:I("Title"),selector:"",cell:e=>e.entity_id&&e.entity_id.title?(0,t.jsx)(c(),{href:"/".concat(e.entity_type,"/[...routes]"),as:"/".concat(e.entity_type,"/show/").concat(e.entity_id._id),children:e.entity_id.title}):""},{name:I("Group"),selector:"group",cell:e=>e.onModel&&"Institution"===e.onModel?"Organisation":e.onModel},{name:I("Remove"),selector:"",cell:e=>(0,t.jsx)("div",{onClick:()=>P(e),style:{cursor:"pointer"},children:(0,t.jsx)("i",{className:"icon fas fa-trash-alt"})})}],O=async()=>{i(!0);let e=await h.A.get("/flag",R);e&&e.data&&(l(e.data),A(e.totalCount),i(!1))},T=async(e,a)=>{R.limit=e,R.page=a,i(!0);let s=r().map(w,"value");s&&s.length>0&&(R.query={...R.query,entity_type:s});let t=await h.A.get("/flag",R);t&&t.data&&t.data.length>0&&(l(t.data),y(e),i(!1))};(0,n.useEffect)(()=>{R.page=1,O()},[]);let E=n.useMemo(()=>{let e=e=>{e&&(R.populate.match={title:{$regex:e}},O())},s=r().debounce(a=>e(a),Number("500")||300);return(0,t.jsx)(u.default,{onFilter:e=>{b(e.target.value),s(e.target.value)},onClear:()=>{v&&(C(!N),b(""))},filterText:v,handleGroupHandler:e=>{k(e);let a=r().map(e,"value");0===a.length?l([]):(R.query={...R.query,entity_type:a},O())},groupType:w,options:a})},[v,w,N]),H=()=>p(!1),G=async()=>{if(p(!1),await h.A.remove("/flag/".concat(S)),w&&Array.isArray(w)){let e=r().map(w,"value");e&&e.length>0&&(R.query={...R.query,entity_type:e})}O()};return(0,t.jsxs)("div",{className:"my-bookmark-table",children:[(0,t.jsxs)(d.A,{show:o,onHide:H,children:[(0,t.jsx)(d.A.Header,{closeButton:!0,children:(0,t.jsx)(d.A.Title,{children:I("Removebookmark")})}),(0,t.jsx)(d.A.Body,{children:I("Areyousurewanttoremovefromyourbookmark")}),(0,t.jsxs)(d.A.Footer,{children:[(0,t.jsx)(m.A,{variant:"secondary",onClick:H,children:I("Cancel")}),(0,t.jsx)(m.A,{variant:"primary",onClick:G,children:I("bookmarkDeleteYes")})]})]}),(0,t.jsx)(x.A,{columns:B,data:s,totalRows:j,subheader:!0,pagServer:!0,resetPaginationToggle:N,subHeaderComponent:E,handlePerRowsChange:T,handlePageChange:e=>{R.limit=f,R.page=e,""!==v&&(R.query={title:v});let a=r().map(w,"value");a&&a.length>0&&(R.query={...R.query,entity_type:a}),O()}})]})})},52579:(e,a,s)=>{"use strict";s.r(a),s.d(a,{default:()=>d});var t=s(37876),n=s(14232),l=s(31195),i=s(26188),r=s(29504),o=s(61862),c=s(31753);let d=e=>{let{isOpen:a,manageClose:s,id:d}=e,{t:m}=(0,c.Bd)("common"),u={consent1:!0,consent2:!0,consent3:!0,consent4:!0},[x,h]=(0,n.useState)(u),[g,p]=(0,n.useState)(!1),j=e=>{let{name:a,checked:s}=e.target;h(e=>({...e,[a]:s})),p(!g)},A=e=>{h(u),p(e)};return(0,t.jsx)(t.Fragment,{children:(0,t.jsxs)(l.A,{show:a,onHide:()=>{s(!1)},size:"xl",id:"main-content",className:"w-100",children:[(0,t.jsx)(l.A.Header,{closeButton:!0,children:(0,t.jsx)(l.A.Title,{children:m("declaration.title")})}),(0,t.jsx)(l.A.Body,{children:(0,t.jsxs)("div",{className:"p-3 w-100",children:[(0,t.jsx)(i.A,{variant:"danger",children:m("declaration.info")}),(0,t.jsx)(r.A.Check,{className:"pb-4",type:"checkbox",name:"consent1",onChange:j,checked:x.consent1,value:"consent1",label:m("declaration.consent1")}),(0,t.jsx)(r.A.Check,{className:"pb-4",name:"consent2",onChange:j,type:"checkbox",checked:x.consent2,value:"consent2",label:m("declaration.consent2")}),(0,t.jsx)(r.A.Check,{className:"pb-4",name:"consent3",onChange:j,type:"checkbox",checked:x.consent3,value:"consent3",label:m("declaration.consent3")}),(0,t.jsx)(r.A.Check,{className:"pb-4",type:"checkbox",name:"consent4",value:"consent4",label:m("declaration.consent4"),checked:x.consent4,onChange:j})]})}),(0,t.jsx)(o.default,{endpoint:"/users",userId:d||"",isopen:g,manageDialog:e=>A(e)})]})})}},61862:(e,a,s)=>{"use strict";s.r(a),s.d(a,{default:()=>u});var t=s(37876);s(14232);var n=s(31195),l=s(60282),i=s(21772),r=s(11041),o=s(89099),c=s.n(o),d=s(53718),m=s(31753);let u=e=>{let{isopen:a,manageDialog:s,userId:o,endpoint:u}=e,{t:x}=(0,m.Bd)("common"),h=()=>{s(!1)},g=async()=>{let e;("/users"===u?await d.A.remove("".concat(u,"/").concat(o)):await d.A.post("".concat(u),{code:o}))&&(s(!1),c().push("/home"))};return(0,t.jsxs)(n.A,{show:a,onHide:h,children:[(0,t.jsx)("div",{className:"text-center p-2",children:(0,t.jsx)(i.g,{icon:r.zpE,size:"5x",color:"indianRed",style:{background:"#d6deec",padding:"19px",borderRadius:"50%",width:"100px",height:"100px"}})}),(0,t.jsx)(n.A.Body,{children:(0,t.jsx)("b",{children:x("AreyousureyouwishtoleavetheKnowledgePlatform")})}),(0,t.jsxs)(n.A.Footer,{children:[(0,t.jsx)(l.A,{variant:"secondary",onClick:h,children:x("No")}),(0,t.jsx)(l.A,{variant:"danger",onClick:g,children:x("YesDeleteMe")})]})]})}},69600:(e,a,s)=>{"use strict";s.d(a,{A:()=>n});var t=s(37876);function n(e){return(0,t.jsx)("h2",{className:"page-heading",children:e.title})}},93649:(e,a,s)=>{(window.__NEXT_P=window.__NEXT_P||[]).push(["/profile",function(){return s(11778)}])}},e=>{var a=a=>e(e.s=a);e.O(0,[7725,9773,1772,5266,9875,8076,636,6593,8792],()=>a(93649)),_N_E=e.O()}]);
//# sourceMappingURL=profile-553d705a281df421.js.map