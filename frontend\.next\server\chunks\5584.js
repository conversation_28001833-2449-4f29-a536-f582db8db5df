"use strict";exports.id=5584,exports.ids=[5584],exports.modules={5584:(e,t,r)=>{r.a(e,async(e,l)=>{try{r.r(t),r.d(t,{default:()=>y});var n=r(8732),o=r(82015),s=r(36254),i=r(63487),a=e([i]);function y(e){let{t,ongoingOperations:r,ongoingProjects:l}=e,[i,a]=(0,o.useState)([]);return(0,n.jsxs)("div",{className:"active-projects-announcements",children:[(0,n.jsx)("h4",{children:t("allActivity")}),(0,n.jsx)(s.default,{t:t,currentEvents:i,ongoingProjects:l,ongoingOperations:r})]})}i=(a.then?(await a)():a)[0],l()}catch(e){l(e)}})},36254:(e,t,r)=>{r.r(t),r.d(t,{default:()=>p});var l=r(8732),n=r(82015),o=r(27825),s=r.n(o),i=r(72953),a=r(89364),y=r(88751);let c=e=>{let{t}=e,r=t("Event");return(0,l.jsx)("div",{className:"map-legends",children:(0,l.jsxs)("ul",{children:[(0,l.jsxs)("li",{className:"marker-yellow-legend",children:[(0,l.jsx)("i",{className:"fas fa-circle"})," ",t("Projects")]}),(0,l.jsxs)("li",{className:"marker-green-legend",children:[(0,l.jsx)("i",{className:"fas fa-circle"})," ",t("Operations")]}),(0,l.jsxs)("li",{className:"marker-red-legend",children:[(0,l.jsx)("i",{className:"fas fa-circle"})," ",r," "]})]})})},p=e=>{let{i18n:t}=(0,y.useTranslation)("common"),r=t.language,{t:o,ongoingOperations:p,ongoingProjects:u,currentEvents:d}=e,[m,f]=(0,n.useState)({events:!1,projects:!1,operations:!1}),[T,g]=(0,n.useState)([]),[h,v]=(0,n.useState)({}),[j,x]=(0,n.useState)({}),_=()=>{v(null),x(null)},b=async(e,t,r)=>{_(),v(t),x({name:e.name,id:e.id,type:e.type,countryId:e.countryId})},k=()=>{let e=[];s().forEach(p,t=>{t.country&&e.push({title:t.title,type:"operation",id:t._id,countryId:t.country&&t.country._id,lat:t.country.coordinates[0].latitude,lng:t.country.coordinates[0].longitude,icon:"/images/map-marker-green.svg"})}),m.operations=!0,f(m),g([...T,...e])},C=()=>{let e=[];s().forEach(u,t=>{t.partner_institutions&&t.partner_institutions.length>0&&s().forEach(t.partner_institutions,r=>{r.partner_country&&e.push({title:t.title,type:"project",id:t._id,countryId:t.partner_institutions.length>0&&t.partner_institutions[0].partner_country&&t.partner_institutions[0].partner_country._id,lat:r.partner_country.coordinates[0].latitude,lng:r.partner_country.coordinates[0].longitude,icon:"/images/map-marker-yellow.svg"})})}),m.projects=!0,f(m),g([...T,...e])},I=()=>{let e=[];s().forEach(d,t=>{t.country&&e.push({title:t.title,type:"event",id:t._id,countryId:t.country&&t.country._id,lat:t.country.coordinates[0].latitude,lng:t.country.coordinates[0].longitude,icon:"/images/map-marker-red.svg"})}),m.events=!0,f(m),g([...T,...e])};return(0,n.useEffect)(()=>{C()},[u]),(0,n.useEffect)(()=>{k()},[p]),(0,n.useEffect)(()=>{I()},[d]),(0,l.jsxs)(l.Fragment,{children:[(0,l.jsx)(i.A,{onClose:_,language:r,points:T,activeMarker:h,markerInfo:(0,l.jsx)(e=>{let{info:t}=e,n=function(e){switch(e?.type){case"operation":return p.filter(t=>t.country&&t.country._id==e.countryId);case"project":return u.filter(t=>t.partner_institutions&&t.partner_institutions.length>0&&t.partner_institutions[0].partner_country&&t.partner_institutions[0].partner_country._id==e.countryId);case"event":return d.filter(t=>t.country&&t.country._id==e.countryId)}}(t);return t&&Object.keys(t).length>0&&void 0!=n?(0,l.jsx)("ul",{children:n.map((e,n)=>(0,l.jsx)("li",{children:(0,l.jsx)("a",{href:`/${r}/${t?.type}/show/${t?.id}`,children:e?.title})},n))}):null},{info:j}),children:m.projects&&m.operations&&m.events&&T.length>=1?T.map((e,t)=>{if(e.lat&&e.lng)return(0,l.jsx)(a.A,{name:e.title,id:e.id,countryId:e.countryId,type:e.type,icon:{url:e.icon},onClick:b,position:e},t)}):null}),(0,l.jsx)(c,{t:o})]})}},72953:(e,t,r)=>{r.d(t,{A:()=>d});var l=r(8732);r(82015);var n=r(94696);let o=({position:e,onCloseClick:t,children:r})=>(0,l.jsx)(n.InfoWindow,{position:e,onCloseClick:t,children:(0,l.jsx)("div",{children:r})}),s="labels.text.fill",i="labels.text.stroke",a="road.highway",y="geometry.stroke",c=[{elementType:"geometry",stylers:[{color:"#1d2c4d"}]},{elementType:s,stylers:[{color:"#8ec3b9"}]},{elementType:i,stylers:[{color:"#1a3646"}]},{featureType:"administrative",elementType:"geometry",stylers:[{visibility:"off"}]},{featureType:"administrative.country",elementType:y,stylers:[{color:"#4b6878"}]},{featureType:"administrative.land_parcel",elementType:"labels",stylers:[{visibility:"off"}]},{featureType:"administrative.land_parcel",elementType:s,stylers:[{color:"#64779e"}]},{featureType:"administrative.province",elementType:y,stylers:[{color:"#4b6878"}]},{featureType:"landscape.man_made",elementType:y,stylers:[{color:"#334e87"}]},{featureType:"landscape.natural",elementType:"geometry",stylers:[{color:"#023e58"}]},{featureType:"poi",stylers:[{visibility:"off"}]},{featureType:"poi",elementType:"geometry",stylers:[{color:"#283d6a"}]},{featureType:"poi",elementType:"labels.text",stylers:[{visibility:"off"}]},{featureType:"poi",elementType:s,stylers:[{color:"#6f9ba5"}]},{featureType:"poi",elementType:i,stylers:[{color:"#1d2c4d"}]},{featureType:"poi.park",elementType:"geometry.fill",stylers:[{color:"#023e58"}]},{featureType:"poi.park",elementType:s,stylers:[{color:"#3C7680"}]},{featureType:"road",stylers:[{visibility:"off"}]},{featureType:"road",elementType:"geometry",stylers:[{color:"#304a7d"}]},{featureType:"road",elementType:"labels.icon",stylers:[{visibility:"off"}]},{featureType:"road",elementType:s,stylers:[{color:"#98a5be"}]},{featureType:"road",elementType:i,stylers:[{color:"#1d2c4d"}]},{featureType:a,elementType:"geometry",stylers:[{color:"#2c6675"}]},{featureType:a,elementType:y,stylers:[{color:"#255763"}]},{featureType:a,elementType:s,stylers:[{color:"#b0d5ce"}]},{featureType:a,elementType:i,stylers:[{color:"#023e58"}]},{featureType:"road.local",elementType:"labels",stylers:[{visibility:"off"}]},{featureType:"transit",stylers:[{visibility:"off"}]},{featureType:"transit",elementType:s,stylers:[{color:"#98a5be"}]},{featureType:"transit",elementType:i,stylers:[{color:"#1d2c4d"}]},{featureType:"transit.line",elementType:"geometry.fill",stylers:[{color:"#283d6a"}]},{featureType:"transit.station",elementType:"geometry",stylers:[{color:"#3a4762"}]},{featureType:"water",elementType:"geometry",stylers:[{color:"#0e1626"}]},{featureType:"water",elementType:s,stylers:[{color:"#4e6d70"}]}];var p=r(44233),u=r(40691);let d=({markerInfo:e,activeMarker:t,initialCenter:r,children:s,height:i=300,width:a="114%",language:y,zoom:d=1,minZoom:m=1,onClose:f})=>{let{locale:T}=(0,p.useRouter)(),{isLoaded:g,loadError:h}=(0,u._)(),v={width:a,height:"number"==typeof i?`${i}px`:i};return h?(0,l.jsx)("div",{children:"Error loading maps"}):g?(0,l.jsx)("div",{className:"map-container",children:(0,l.jsx)("div",{className:"mapprint",style:{width:a,height:i,position:"relative"},children:(0,l.jsxs)(n.GoogleMap,{mapContainerStyle:v,center:r||{lat:52.520017,lng:13.404195},zoom:d,onLoad:e=>{e.setOptions({styles:c})},options:{minZoom:m,draggable:!0,keyboardShortcuts:!1,streetViewControl:!1,panControl:!1,clickableIcons:!1,mapTypeControl:!1,fullscreenControl:!0},children:[s,e&&t&&t.getPosition&&(0,l.jsx)(o,{position:t.getPosition(),onCloseClick:()=>{console.log("close click"),f?.()},children:e})]})})}):(0,l.jsx)("div",{children:"Loading Maps..."})}},89364:(e,t,r)=>{r.d(t,{A:()=>o});var l=r(8732);r(82015);var n=r(94696);let o=({name:e="Marker",id:t="",countryId:r="",type:o,icon:s,position:i,onClick:a,title:y,draggable:c=!1})=>i&&"number"==typeof i.lat&&"number"==typeof i.lng?(0,l.jsx)(n.Marker,{position:i,icon:s,title:y||e,draggable:c,onClick:l=>{a&&a({name:e,id:t,countryId:r,type:o,position:i},{position:i,getPosition:()=>i},l)}}):null}};