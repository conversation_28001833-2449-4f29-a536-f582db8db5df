{"version": 3, "file": "static/chunks/pages/project/components/ProjectCoverSection-a101314ece07c1f8.js", "mappings": "8EACA,4CACA,0CACA,WACA,OAAe,EAAQ,KAA+D,CACtF,EACA,SAFsB,sMCqEtB,MArC4B,IACxB,GAAM,CAAEA,CAAC,CAAE,CAAGC,CAAAA,EAAAA,EAAAA,EAAAA,CAAcA,CAAC,QAoClBC,EAjCLC,EAAuB,IAEvB,WA+BwBD,CA/BxB,CA+ByB,OA/BzB,WAEEE,EAAMC,UAAU,CACd,UAACC,IAAIA,CACHC,KAAK,uBACLC,GAAI,aAFDF,IAE4C,OAA1BF,EAAMK,SAAS,CAACC,MAAM,CAAC,EAAE,WAE9C,WAACC,EAAAA,CAAMA,CAAAA,CAACC,QAAQ,YAAYC,KAAK,eAC/B,UAACC,EAAAA,CAAeA,CAAAA,CAACC,KAAMC,EAAAA,GAAKA,GAAI,OAAOhB,EAAE,aAEnC,KAMdiB,EAAiBC,CAAAA,EAAAA,EAAAA,cAAAA,CAAcA,CAAC,IAAM,UAACf,EAAAA,CAAAA,IAC7C,MACI,+BACI,UAACgB,EAAAA,CAAGA,CAAAA,CAACC,UAAU,sBACX,WAACC,MAAAA,CAAID,UAAU,0BACX,UAACC,MAAAA,CAAID,UAAU,sBACX,UAACE,MAAAA,CAAIC,IAAI,6BAA6BC,IAAI,qBAE7CC,SAQZA,CAAgS,CAAErB,CAAU,CAAEa,CAAmB,CAAEjB,CAA0B,CAAE0B,CAA2B,EAC/X,MAAO,WAACL,MAAAA,CAAID,UAAU,8BACpB,WAACO,KAAAA,CAAGP,UAAU,yBACXQ,EAAYC,KAAK,CAAC,WAClBzB,EAAMM,MAAM,EAAIN,EAAMM,MAAM,CAAC,EAAE,CAAG,UAACO,EAAAA,CAAea,QAASF,IAAkB,QAEhF,WAACP,MAAAA,CAAID,UAAU,wBACb,WAACC,MAAAA,CAAID,UAAU,yBACb,UAACW,IAAAA,CAAEX,UAAU,wBACb,WAACC,MAAAA,WACC,WAACW,KAAAA,CAAGC,MAAO,CAAEC,MAAO,OAAQ,YAAIlC,EAAE,aAAa,OAC/C,UAACmC,KAAAA,UAAIP,EAAYQ,UAAU,CAAIC,IAAOT,EAAYQ,UAAU,EAAEE,MAAM,CAACZ,GAAwB,aAGjG,IAHyCW,CAGzC,MAAChB,MAAAA,CAAID,UAAU,+BACb,UAACW,IAAAA,CAAEX,UAAU,0BACb,WAACC,MAAAA,WACC,WAACW,KAAAA,CAAGC,MAAO,CAAEC,MAAO,OAAQ,YAAIlC,EAAE,UAAU,OAC5C,UAACmC,KAAAA,UAAIP,EAAYW,MAAM,EAAIX,EAAYW,MAAM,CAAC,KAAQ,SAG1D,UAACC,EAAAA,CAAQA,CAAAA,CAACC,SAAUrC,EAAMM,MAAM,CAAC,EAAE,CAAEgC,WAAW,iBAGtD,EAhCsCtC,EAAMwB,WAAW,CAAExB,EAAMK,SAAS,CAAEQ,EAAgBjB,EA5B5D,CA4B+D0B,mBAK/F,uHC9DA,IAAMiB,EAAiB,CACrBC,UAAW,YACXC,YAAa,cACbC,MAAO,QACPhB,QAAS,UACTiB,OAAQ,QACV,EAoEA,EAAeC,CAAAA,EAAAA,EAAAA,EAAAA,CAAOA,CAAC,GAAgBC,GA1DG,IACxC,GAAM,MAAEC,CAAI,CAyD0C,SAzDxCT,CAAQ,YAAEC,CAAU,CAAE,CAAGtC,EACjC,CAAC+C,EAAUC,EAAY,CAAGC,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,EAAU,GAC5C,CAACC,EAAWC,EAAa,CAAGF,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAM,IAG1CG,EAA2B,UAC/B,GAAI,QAACN,EAAAA,KAAAA,EAAAA,EAAMO,GAAG,EAAE,CAAXP,MACL,IAAMQ,EAAY,MAAMC,EAAAA,CAAUA,CAACC,GAAG,CAAC,QAAS,CAACC,MAAO,CAACC,UAAWrB,EAAUS,KAAMA,EAAKO,GAAG,CAAEM,QAASpB,CAAc,CAACD,EAAW,CAAC,GAC9HgB,GAAaA,EAAUM,IAAI,EAAIN,EAAUM,IAAI,CAACC,MAAM,CAAG,GAAG,CAC5DV,EAAaG,EAAUM,IAAI,CAAC,EAAE,EAC9BZ,GAAY,GAEhB,EAEMc,EAAkB,MAAOC,IAE7B,GADAA,EAAEC,cAAc,GACZ,QAAClB,EAAAA,KAAAA,EAAAA,EAAMO,GAAAA,EAAK,CAAXP,MACL,IAAMmB,EAAQ,CAAClB,EACTmB,EAAc,CAClBC,YAAa7B,EACboB,UAAWrB,EACXS,KAAMA,EAAKO,GAAG,CACdM,QAASpB,CAAc,CAACD,EAAW,EAErC,GAAI2B,EAAM,CACR,IAAMG,EAAc,MAAMb,EAAAA,CAAUA,CAACc,IAAI,CAAC,QAASH,GAC/CE,GAAUA,EAAOf,GAAG,EAAE,CACxBF,EAAaiB,GACbpB,EAAYiB,GAEhB,KAAO,CACL,IAAMK,EAAW,MAAMf,EAAAA,CAAUA,CAACgB,MAAM,CAAC,SAAuB,OAAdrB,EAAUG,GAAG,GAC3DiB,GAAYA,EAASE,CAAC,EAAE,EACdP,EAEhB,CACF,EAKA,MAHAQ,CAAAA,EAAAA,EAAAA,SAAAA,CAASA,CAAC,KACRrB,GACF,EAAE,EAAE,EAEF,UAACnC,MAAAA,CAAID,UAAU,0BACb,WAAC0D,IAAAA,CAAEvE,KAAK,GAAGwE,QAASb,YAClB,UAACc,OAAAA,CAAK5D,UAAU,iBACb+B,EACC,UAACrC,EAAAA,CAAeA,CAAAA,CAACM,UAAU,sBAAsBL,KAAMkE,EAAAA,GAAaA,CAAE/C,MAAM,YAE5E,UAACpB,EAAAA,CAAeA,CAAAA,CAACM,UAAU,sBAAsBL,KAAMmE,EAAAA,GAAYA,CAAEhD,MAAM,WAG/E,UAACpB,EAAAA,CAAeA,CAAAA,CAACM,UAAU,WAAWL,KAAMoE,EAAAA,GAAUA,CAAEjD,MAAM,gBAItE,wOC1EO,IAAMkD,EAAgBC,CAAAA,EAAAA,EAAAA,CAAAA,CAAoBA,CAAC,CAChDC,sBAAuB,KACjBrC,EAAMsC,WAAW,IAAItC,EAAMsC,WAAW,CAACzD,OAAO,IAAImB,EAAMsC,WAAW,CAACzD,OAAO,CAAC,aAAa,CAK/F0D,CALiG,kBAK7E,eACtB,GAAG,EAE8BH,CAAAA,EAAAA,EAAAA,CAAAA,CAAoBA,CAAC,CACpDC,sBAAuB,KACjBrC,EAAMsC,WAAW,IAAItC,EAAMsC,WAAW,CAACzD,OAAO,IAAImB,EAAMsC,WAAW,CAACzD,OAAO,CAAC,aAAa,CAK/F0D,CALiG,kBAK7E,oBACpBC,iBAAkB,IAAM,UAACC,EAAAA,OAAIA,CAAAA,CAAAA,EAC/B,GAAG,EAE2BL,CAAAA,EAAAA,EAAAA,CAAAA,CAAoBA,CAAC,CACjDC,sBAAuB,CAACrC,EAAO7C,KAC7B,GAAI6C,EAAMsC,WAAW,EAAItC,EAAMsC,WAAW,CAACzD,OAAO,CAChD,CADkD,GAC9CmB,EAAMsC,WAAW,CAACzD,OAAO,CAAC,aAAa,CACzC,CAD2C,MACpC,OAEP,GAAImB,EAAMsC,WAAW,CAACzD,OAAO,CAAC,aAAa,EAAE,EACjCA,OAAO,EAAI1B,EAAM0B,OAAO,CAACoB,IAAI,EAAI9C,EAAM0B,OAAO,CAACoB,IAAI,CAACO,GAAG,GAAKR,EAAMC,IAAI,CAACO,GAAG,CAClF,CADoF,MAC7E,CAGb,CAEF,OAAO,CACT,EACA+B,mBAAoB,gBACtB,GAAG,EAE+BH,CAAAA,EAAAA,EAAAA,CAAAA,CAAoBA,CAAC,CACrDC,sBAAuB,CAACrC,EAAO7C,KAC7B,GAAI6C,EAAMsC,WAAW,EAAItC,EAAMsC,WAAW,CAACzD,OAAO,EAAE,GAC9CmB,EAAMsC,WAAW,CAACzD,OAAO,CAAC,aAAa,CACzC,CAD2C,MACpC,OAEP,GAAImB,EAAMsC,WAAW,CAACzD,OAAO,CAAC,aAAa,EAAE,EACjCA,OAAO,EAAI1B,EAAM0B,OAAO,CAACoB,IAAI,EAAI9C,EAAM0B,OAAO,CAACoB,IAAI,CAACO,GAAG,GAAKR,EAAMC,IAAI,CAACO,GAAG,CAClF,CADoF,MAC7E,CAGb,CAEF,OAAO,CACT,EACA+B,mBAAoB,qBACpBC,iBAAkB,IAAM,UAACC,EAAAA,OAAIA,CAAAA,CAAAA,EAC/B,GAAG,EAEoCL,CAAAA,EAAAA,EAAAA,CAAAA,CAAoBA,CAAC,CAC1DC,sBAAuB,KACjBrC,EAAMsC,WAAW,IAAItC,EAAMsC,WAAW,CAACI,MAAM,IAAI1C,EAAMsC,WAAW,CAACI,MAAM,CAAC,WAAW,CAK3FH,CAL6F,kBAKzE,yBACtB,GAAG,EAEYJ,aAAaA,EAAC", "sources": ["webpack://_N_E/?5bfb", "webpack://_N_E/./pages/project/components/ProjectCoverSection.tsx", "webpack://_N_E/./components/common/Bookmark.tsx", "webpack://_N_E/./pages/project/permission.tsx"], "sourcesContent": ["\n    (window.__NEXT_P = window.__NEXT_P || []).push([\n      \"/project/components/ProjectCoverSection\",\n      function () {\n        return require(\"private-next-pages/project/components/ProjectCoverSection.tsx\");\n      }\n    ]);\n    if(module.hot) {\n      module.hot.dispose(function () {\n        window.__NEXT_P.push([\"/project/components/ProjectCoverSection\"])\n      });\n    }\n  ", "//Import Library\r\nimport React from \"react\";\r\nimport { <PERSON><PERSON>, <PERSON> } from \"react-bootstrap\";\r\nimport Link from 'next/link';\r\nimport { FontAwesomeIcon } from \"@fortawesome/react-fontawesome\";\r\nimport { faPen } from \"@fortawesome/free-solid-svg-icons\";\r\nimport moment from 'moment';\r\n\r\n//Import services/components\r\nimport { useTranslation } from 'next-i18next';\r\nimport { canEditProject } from \"../permission\";\r\nimport Bookmark from \"../../../components/common/Bookmark\";\r\n\r\n\r\ninterface ProjectCoverSectionProps {\r\n  projectData: {\r\n    title: string;\r\n    website: string;\r\n    area_of_work: any[];\r\n    status: any;\r\n    funded_by: string;\r\n    country: any;\r\n    description: string;\r\n    end_date: string;\r\n    start_date: string;\r\n    partner_institutions: any[];\r\n    partner_institution: any;\r\n    created_at: string;\r\n    updated_at: string;\r\n  };\r\n  routeData: {\r\n    routes: string[];\r\n  };\r\n  editAccess: boolean;\r\n}\r\n\r\nconst ProjectCoverSection = (props: ProjectCoverSectionProps) => {\r\n    const { t } = useTranslation('common');\r\n    const formatDateStartDate = \"DD-MM-YYYY\";\r\n\r\n    const EditProjectComponent = () => {\r\n        return (\r\n          <>\r\n            {\r\n            props.editAccess ?\r\n              <Link\r\n                href=\"/project/[...routes]\"\r\n                as={`/project/edit/${props.routeData.routes[1]}`}\r\n                >\r\n                <Button variant=\"secondary\" size=\"sm\">\r\n                  <FontAwesomeIcon icon={faPen} />&nbsp;{t(\"Edit\")}\r\n                </Button>\r\n              </Link> : \"\"\r\n            }\r\n          </>\r\n        );\r\n      }\r\n\r\n    const CanEditProject = canEditProject(() => <EditProjectComponent />);\r\n    return (\r\n        <>\r\n            <Row className=\"projectRow\">\r\n                <div className=\"projectBanner\">\r\n                    <div className=\"projectImg\">\r\n                        <img src=\"/images/project-banner.jpg\" alt=\"Project Detail\" />\r\n                    </div>\r\n                    {project_start_func(props.projectData, props.routeData, CanEditProject, t, formatDateStartDate)}\r\n                </div>\r\n            </Row>\r\n        </>\r\n    )\r\n}\r\n\r\nexport default ProjectCoverSection;\r\nfunction project_start_func(projectData: { title: string; website: string; area_of_work: any[]; status: any; funded_by: string; country: any; description: string; end_date: string; start_date: string; partner_institutions: any[]; partner_institution: any; created_at: string; updated_at: string; }, props: any, CanEditProject: any, t: (key: string) => string, formatDateStartDate: string) {\r\n    return <div className=\"projectTitleBlock\">\r\n      <h4 className=\"projectTitle\">\r\n        {projectData.title}&nbsp;&nbsp;\r\n        {props.routes && props.routes[1] ? <CanEditProject project={projectData} /> : null}\r\n      </h4>\r\n      <div className=\"projectDate\">\r\n        <div className=\"projectStart\">\r\n          <i className=\"fas fa-calendar-alt\" />\r\n          <div>\r\n            <h6 style={{ color: \"white\" }}>{t(\"StartDate\")}:</h6>\r\n            <h5>{projectData.start_date ? (moment(projectData.start_date).format(formatDateStartDate)) : null}</h5>\r\n          </div>\r\n        </div>\r\n        <div className=\"projectStatus me-2\">\r\n          <i className=\"fas fa-hourglass-half\" />\r\n          <div>\r\n            <h6 style={{ color: \"white\" }}>{t(\"Status\")}:</h6>\r\n            <h5>{projectData.status && projectData.status[\"title\"]}</h5>\r\n          </div>\r\n        </div>\r\n        <Bookmark entityId={props.routes[1]} entityType=\"project\" />\r\n      </div>\r\n    </div>;\r\n  }", "//Import Library\r\nimport { connect } from \"react-redux\";\r\nimport {faBookmark, faCheckCircle, faPlusCircle} from \"@fortawesome/free-solid-svg-icons\";\r\nimport {useEffect, useState} from \"react\";\r\nimport {FontAwesomeIcon} from \"@fortawesome/react-fontawesome\";\r\n\r\n//Import services/components\r\nimport apiService from \"../../services/apiService\";\r\n\r\nconst onModelOptions = {\r\n  operation: \"Operation\",\r\n  institution: \"Institution\",\r\n  event: \"Event\",\r\n  project: \"Project\",\r\n  vspace: \"Vspace\"\r\n} as const;\r\n\r\ninterface BookMarkProps {\r\n  user?: {\r\n    _id: string;\r\n  };\r\n  entityId: string;\r\n  entityType: keyof typeof onModelOptions;\r\n}\r\n\r\nconst BookMark: React.FC<BookMarkProps> = (props) => {\r\n  const { user, entityId, entityType } = props;\r\n  const [bookmark, setBookmark] = useState<boolean>(false);\r\n  const [subscribe, setSubscribe] = useState<any>(\"\");\r\n\r\n  //Handle bookmark feature\r\n  const fetchIfContentSubscribed = async () => {\r\n    if (!user?._id) return;\r\n    const checkFlag = await apiService.get('/flag', {query: {entity_id: entityId, user: user._id, onModel: onModelOptions[entityType]}});\r\n    if (checkFlag && checkFlag.data && checkFlag.data.length > 0) {\r\n      setSubscribe(checkFlag.data[0]);\r\n      setBookmark(true);\r\n    }\r\n  };\r\n\r\n  const bookmarkHandler = async (e: React.MouseEvent<HTMLAnchorElement>) => {\r\n    e.preventDefault();\r\n    if (!user?._id) return;\r\n    const flag  = !bookmark;\r\n    const flagPayload = {\r\n      entity_type: entityType,\r\n      entity_id: entityId,\r\n      user: user._id,\r\n      onModel: onModelOptions[entityType]\r\n    }\r\n    if (flag) {\r\n      const flagIt: any = await apiService.post('/flag', flagPayload);\r\n      if (flagIt && flagIt._id) {\r\n        setSubscribe(flagIt);\r\n        setBookmark(flag);\r\n      }\r\n    } else {\r\n      const unFlagIt = await apiService.remove(`/flag/${subscribe._id}`);\r\n      if (unFlagIt && unFlagIt.n) {\r\n        setBookmark(flag);\r\n      }\r\n    }\r\n  }\r\n  //END Bookmark handler\r\n  useEffect(() => {\r\n    fetchIfContentSubscribed();\r\n  },[]);\r\n  return (\r\n    <div className=\"subscribe-flag\">\r\n      <a href=\"\" onClick={bookmarkHandler}>\r\n        <span className=\"check\">\r\n          {bookmark ?\r\n            <FontAwesomeIcon className=\"clickable checkIcon\" icon={faCheckCircle} color=\"#00CC00\" />\r\n            :\r\n            <FontAwesomeIcon className=\"clickable minusIcon\" icon={faPlusCircle} color=\"#fff\"/>\r\n          }\r\n        </span>\r\n        <FontAwesomeIcon className=\"bookmark\" icon={faBookmark} color=\"#d4d4d4\"/>\r\n      </a>\r\n    </div>\r\n  )\r\n}\r\n\r\nexport default connect((state: any) => state)(BookMark);\r\n\r\n", "//Import Library\r\nimport React from 'react';\r\nimport connectedAuthWrapper from 'redux-auth-wrapper/connectedAuthWrapper';\r\n\r\n//Import services/components\r\nimport R403 from \"../r403\";\r\n\r\nexport const canAddProject = connectedAuthWrapper({\r\n  authenticatedSelector: (state) => {\r\n    if (state.permissions && state.permissions.project && state.permissions.project['create:any']) {\r\n      return true;\r\n    }\r\n    return false;\r\n  },\r\n  wrapperDisplayName: 'CanAddProject',\r\n});\r\n\r\nexport const canAddProjectForm = connectedAuthWrapper({\r\n  authenticatedSelector: (state) => {\r\n    if (state.permissions && state.permissions.project && state.permissions.project['create:any']) {\r\n      return true;\r\n    }\r\n    return false;\r\n  },\r\n  wrapperDisplayName: 'CanAddProjectForm',\r\n  FailureComponent: () => <R403/>\r\n});\r\n\r\nexport const canEditProject = connectedAuthWrapper({\r\n  authenticatedSelector: (state, props) => {\r\n    if (state.permissions && state.permissions.project) {\r\n      if (state.permissions.project['update:any']) {\r\n        return true;\r\n      } else { //if update:own\r\n        if (state.permissions.project['update:own']) {\r\n          if (props.project && props.project.user && props.project.user._id === state.user._id) {\r\n            return true;\r\n          }  \r\n        }\r\n      }\r\n    }\r\n    return false;\r\n  },\r\n  wrapperDisplayName: 'CanEditProject',\r\n});\r\n\r\nexport const canEditProjectForm = connectedAuthWrapper({\r\n  authenticatedSelector: (state, props) => {\r\n    if (state.permissions && state.permissions.project) {\r\n      if (state.permissions.project['update:any']) {\r\n        return true;\r\n      } else { //if update:own\r\n        if (state.permissions.project['update:own']) {\r\n          if (props.project && props.project.user && props.project.user._id === state.user._id) {\r\n            return true;\r\n          }\r\n        }\r\n      }\r\n    }\r\n    return false;\r\n  },\r\n  wrapperDisplayName: 'CanEditProjectForm',\r\n  FailureComponent: () => <R403/>\r\n});\r\n\r\nexport const canViewDiscussionUpdate = connectedAuthWrapper({\r\n  authenticatedSelector: (state) => {\r\n    if (state.permissions && state.permissions.update && state.permissions.update['read:any']) {\r\n      return true;\r\n    }\r\n    return false;\r\n  },\r\n  wrapperDisplayName: 'CanViewDiscussionUpdate',\r\n});\r\n\r\nexport default canAddProject;"], "names": ["t", "useTranslation", "ProjectCoverSection", "EditProjectComponent", "props", "editAccess", "Link", "href", "as", "routeData", "routes", "<PERSON><PERSON>", "variant", "size", "FontAwesomeIcon", "icon", "faPen", "CanEditProject", "canEditProject", "Row", "className", "div", "img", "src", "alt", "project_start_func", "formatDateStartDate", "h4", "projectData", "title", "project", "i", "h6", "style", "color", "h5", "start_date", "moment", "format", "status", "Bookmark", "entityId", "entityType", "onModelOptions", "operation", "institution", "event", "vspace", "connect", "state", "user", "bookmark", "setBookmark", "useState", "subscribe", "setSubscribe", "fetchIfContentSubscribed", "_id", "checkFlag", "apiService", "get", "query", "entity_id", "onModel", "data", "length", "bookmarkHandler", "e", "preventDefault", "flag", "flagPayload", "entity_type", "flagIt", "post", "unFlagIt", "remove", "n", "useEffect", "a", "onClick", "span", "faCheckCircle", "faPlusCircle", "faBookmark", "canAddProject", "connectedAuthWrapper", "authenticatedSelector", "permissions", "wrapperDisplayName", "FailureComponent", "R403", "update"], "sourceRoot": "", "ignoreList": []}