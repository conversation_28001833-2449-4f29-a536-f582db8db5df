{"version": 3, "file": "static/chunks/pages/country/CountryShow-20c6c74404681949.js", "mappings": "gFACA,4CACA,uBACA,WACA,OAAe,EAAQ,IAA4C,CACnE,EACA,UAFsB", "sources": ["webpack://_N_E/?e717"], "sourcesContent": ["\n    (window.__NEXT_P = window.__NEXT_P || []).push([\n      \"/country/CountryShow\",\n      function () {\n        return require(\"private-next-pages/country/CountryShow.tsx\");\n      }\n    ]);\n    if(module.hot) {\n      module.hot.dispose(function () {\n        window.__NEXT_P.push([\"/country/CountryShow\"])\n      });\n    }\n  "], "names": [], "sourceRoot": "", "ignoreList": []}