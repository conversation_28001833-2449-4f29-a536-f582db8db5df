(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[9395],{39896:(e,n,s)=>{"use strict";s.r(n),s.d(n,{default:()=>d});var i=s(37876);s(14232);var r=s(32890),t=s(48230),c=s.n(t),l=s(31753);let d=e=>{let{t:n}=(0,l.Bd)("common"),{institutionData:s,activeOperations:t,activeProjects:d}=e;return(0,i.jsx)(r.A,{defaultActiveKey:"0",children:(0,i.jsxs)(r.A.Item,{eventKey:"0",children:[(0,i.jsx)(r.<PERSON><PERSON>,{children:(0,i.jsx)("div",{className:"cardTitle",children:n("MoreInfo")})}),(0,i.jsxs)(r.<PERSON>.Body,{className:"institutionDetails ps-4",children:[(0,i.jsxs)("p",{children:[(0,i.jsx)("b",{children:n("OrganisationType")}),":",(0,i.jsxs)("span",{children:[" ",s.type?s.type.title:""]})]}),(0,i.jsxs)("p",{children:[(0,i.jsx)("b",{children:n("Network")}),":",(0,i.jsx)("span",{children:s.networks?s.networks.map((e,n)=>(0,i.jsx)("span",{children:(0,i.jsx)("li",{children:e.title})},n)):""})]}),(0,i.jsxs)("p",{children:[(0,i.jsx)("b",{children:n("ActiveOperation")}),":",(0,i.jsx)("span",{children:t&&t.length>0?t.map((e,n)=>(0,i.jsx)("li",{children:(0,i.jsx)(c(),{href:"/operation/[...routes]",as:"/operation/show/".concat(e._id),children:e.title})},n)):(0,i.jsx)("li",{children:n("NoActiveoperationsfound")})})]}),(0,i.jsxs)("p",{children:[(0,i.jsx)("b",{children:n("Expertise")}),":",(0,i.jsxs)("span",{children:[" ",s.expertise?s.expertise.map((e,n)=>(0,i.jsxs)("li",{children:[e.title," ",(0,i.jsx)("br",{})]},n)):""]})]}),(0,i.jsxs)("p",{children:[(0,i.jsx)("b",{children:n("ActiveProject")}),":",(0,i.jsx)("span",{children:d&&d.length>0?d.map((e,n)=>(0,i.jsx)("li",{children:(0,i.jsx)(c(),{href:"/project/[...routes]",as:"/project/show/".concat(e._id),children:e.title})},n)):(0,i.jsx)("li",{children:n("NoActiveprojectsfound")})})]}),(0,i.jsxs)("p",{children:[(0,i.jsx)("b",{children:n("Department")}),":",(0,i.jsx)("span",{children:s&&s.department?s.department:n("Nodepartmentfound")})]}),(0,i.jsxs)("p",{children:[(0,i.jsx)("b",{children:n("Unit")}),":",(0,i.jsx)("span",{children:s&&s.unit?s.unit:n("Nounitfound")})]})]})]})})}},50814:(e,n,s)=>{(window.__NEXT_P=window.__NEXT_P||[]).push(["/institution/components/MoreInfoAccordion",function(){return s(39896)}])}},e=>{var n=n=>e(e.s=n);e.O(0,[636,6593,8792],()=>n(50814)),_N_E=e.O()}]);
//# sourceMappingURL=MoreInfoAccordion-5011ec22da831fa8.js.map