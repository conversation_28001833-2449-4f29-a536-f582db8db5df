{"version": 3, "file": "static/chunks/pages/users/[...routes]-459b9f4f775d540a.js", "mappings": "+EACA,4CACA,qBACA,WACA,OAAe,EAAQ,KAA0C,CACjE,EACA,SAFsB,6GC0BtB,MAvBe,KAEb,IAAMA,EADSC,CAAAA,EAAAA,EAAAA,SAAAA,CAASA,EAsBXC,CArBcC,KAAK,CAACH,CAqBd,KArBoB,EAAI,EAAE,CAE7C,OAAQA,CAAM,CAAC,EAAE,EACf,IAAK,SACL,IAAK,OACH,MAAO,UAACI,EAAAA,OAAIA,CAAAA,CAACJ,OAAQA,GACvB,KAAK,IACH,OAAO,IAGX,CACF", "sources": ["webpack://_N_E/?3ea7", "webpack://_N_E/./pages/users/[...routes].tsx"], "sourcesContent": ["\n    (window.__NEXT_P = window.__NEXT_P || []).push([\n      \"/users/[...routes]\",\n      function () {\n        return require(\"private-next-pages/users/[...routes].tsx\");\n      }\n    ]);\n    if(module.hot) {\n      module.hot.dispose(function () {\n        window.__NEXT_P.push([\"/users/[...routes]\"])\n      });\n    }\n  ", "//Import Library\r\nimport { useRouter } from 'next/router';\r\n\r\n//Import services/components\r\nimport Form from './Form';\r\nimport { serverSideTranslations } from 'next-i18next/serverSideTranslations';\r\n\r\nconst Router = () => {\r\n  const router = useRouter()\r\n  const routes: any = router.query.routes || []\r\n\r\n  switch (routes[0]) {\r\n    case 'create':\r\n    case 'edit':\r\n      return <Form routes={routes} />\r\n    case 'show':\r\n      return null;\r\n    default:\r\n      return null;\r\n  }\r\n}\r\n\r\nexport async function getServerSideProps({ locale }: { locale: string }) {\r\n  return {\r\n    props: {\r\n      ...(await serverSideTranslations(locale, ['common'])),\r\n    },\r\n  }\r\n}\r\n\r\nexport default Router\r\n"], "names": ["routes", "useRouter", "Router", "query", "Form"], "sourceRoot": "", "ignoreList": []}