(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[6256],{45047:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>p});var r=a(37876),s=a(14232),n=a(49589),l=a(56970),o=a(37784),u=a(12697),i=a(29504),c=a(53718),d=a(31753);let p=e=>{let{filterText:t,onFilter:a,onFilterStatusChange:p,onClear:g,filterStatus:y}=e,[h,_]=(0,s.useState)([]),{t:m}=(0,d.Bd)("common"),q=async e=>{let t=await c.A.get("/operation_status",e);t&&Array.isArray(t.data)&&_(t.data)};return(0,s.useEffect)(()=>{q({query:{},sort:{title:"asc"}})},[]),(0,r.jsx)(n.A,{fluid:!0,className:"p-0",children:(0,r.jsxs)(l.A,{children:[(0,r.jsx)(o.A,{xs:6,className:"ps-0 align-self-end mb-3",children:(0,r.jsx)(u.A,{type:"text",className:"searchInput",placeholder:m("search"),"aria-label":"Search",value:t,onChange:a})}),(0,r.jsx)(o.A,{xs:6,children:(0,r.jsx)(i.A,{children:(0,r.jsxs)(i.A.Group,{as:l.A,controlId:"statusFilter",children:[(0,r.jsx)(i.A.Label,{column:!0,sm:"3",lg:"2",children:m("Status")}),(0,r.jsx)(o.A,{className:"ps-0 pe-1",children:(0,r.jsxs)(u.A,{as:"select","aria-label":"Status",onChange:p,value:y,children:[(0,r.jsx)("option",{value:"",children:"All"}),h.map((e,t)=>(0,r.jsx)("option",{value:e._id,children:e.title},t))]})})]})})})]})})}},50749:(e,t,a)=>{"use strict";a.d(t,{A:()=>u});var r=a(37876);a(14232);var s=a(89773),n=a(31753),l=a(5507);function o(e){let{t}=(0,n.Bd)("common"),a={rowsPerPageText:t("Rowsperpage")},{columns:o,data:u,totalRows:i,resetPaginationToggle:c,subheader:d,subHeaderComponent:p,handlePerRowsChange:g,handlePageChange:y,rowsPerPage:h,defaultRowsPerPage:_,selectableRows:m,loading:q,pagServer:w,onSelectedRowsChange:x,clearSelectedRows:f,sortServer:A,onSort:S,persistTableHead:j,sortFunction:b,...P}=e,v={paginationComponentOptions:a,noDataComponent:t("NoData"),noHeader:!0,columns:o,data:u||[],dense:!0,paginationResetDefaultPage:c,subHeader:d,progressPending:q,subHeaderComponent:p,pagination:!0,paginationServer:w,paginationPerPage:_||10,paginationRowsPerPageOptions:h||[10,15,20,25,30],paginationTotalRows:i,onChangeRowsPerPage:g,onChangePage:y,selectableRows:m,onSelectedRowsChange:x,clearSelectedRows:f,progressComponent:(0,r.jsx)(l.A,{}),sortIcon:(0,r.jsx)("i",{className:"sort-icon fas fa-exchange-alt"}),sortServer:A,onSort:S,sortFunction:b,persistTableHead:j,className:"rki-table"};return(0,r.jsx)(s.Ay,{...v})}o.defaultProps={subHeader:!1,pagination:!0,totalRows:null,pagServer:!0,onSelectedRowsChange:null,clearSelectedRows:!1,sortServer:!1,persistTableHead:!1};let u=o},54848:(e,t,a)=>{(window.__NEXT_P=window.__NEXT_P||[]).push(["/operation/OperationsTable",function(){return a(99293)}])},99293:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>m});var r=a(37876),s=a(14232),n=a(48230),l=a.n(n),o=a(82851),u=a.n(o),i=a(89099),c=a(10841),d=a.n(c),p=a(50749),g=a(53718),y=a(45047),h=a(31753);let _=e=>{let{partners:t}=e;return t&&t.length>0?(0,r.jsx)("ul",{children:t.map((e,t)=>{if(e.institution)return(0,r.jsx)("li",{children:(0,r.jsx)(l(),{href:"/institution/[...routes]",as:"/institution/show/".concat(e.institution._id),children:e.institution.title})},t)})}):null},m=function(e){let{t}=(0,h.Bd)("common"),a=(0,i.useRouter)(),{setOperations:n,selectedRegions:o}=e,[c,m]=(0,s.useState)(""),[q,w]=(0,s.useState)(""),[x,f]=(0,s.useState)(!1),[A,S]=(0,s.useState)([]),[j,b]=(0,s.useState)(!1),[P,v]=(0,s.useState)(0),[C,N]=(0,s.useState)(10),[R,T]=(0,s.useState)(1),[E,H]=(0,s.useState)(null),O={sort:{created_at:"desc"},lean:!0,populate:[{path:"partners.status",select:"title"},{path:"partners.institution",select:"title"},{path:"status",select:"title"},{path:"country",select:"coordinates"}],limit:C,page:1,query:{},select:"-timeline -region -hazard -description -end_date -syndrome -hazard_type -created_at -updated_at"},[k,D]=(0,s.useState)(O),M=[{name:t("Operations"),selector:"title",sortable:!0,cell:e=>(0,r.jsx)(l(),{href:"/operation/[...routes]",as:"/operation/show/".concat(e._id),children:e.title})},{name:t("Status"),selector:"status",sortable:!0,cell:e=>e.status&&e.status.title?e.status.title:""},{name:t("StartDate"),selector:"start_date",sortable:!0,cell:e=>e&&e.start_date?d()(e.start_date).format("M/D/Y"):""},{name:t("Partners"),selector:"partners",cell:e=>(0,r.jsx)(_,{partners:e.partners})}],B=async e=>{b(!0),a.query&&a.query.country&&(e.query.country=a.query.country),null===o?delete e.query.world_region:0===o.length?e.query.world_region=["__NO_MATCH__"]:e.query.world_region=o;let t=await g.A.get("/operation",e);t&&Array.isArray(t.data)&&(S(t.data),n(t.data),v(t.totalCount),b(!1))},F=async(e,t)=>{O.limit=e,O.page=t,b(!0),a.query&&a.query.country&&(O.query.country=a.query.country),null===o?delete O.query.world_region:0===o.length?O.query.world_region=["__NO_MATCH__"]:O.query.world_region=o,q&&(O.query={...O.query,status:q}),E&&(O.sort=E.sort);let r=await g.A.get("/operation",O);r&&Array.isArray(r.data)&&(S(r.data),n(r.data),N(e),b(!1)),T(t)};(0,s.useEffect)(()=>{k.page=1,B(k)},[o,a]),(0,s.useEffect)(()=>{B(k)},[k]);let I=async(e,t)=>{b(!0),O.sort={[e.selector]:t},q&&(O.query={...O.query,status:q}),""!==c&&(O.query={...O.query,title:c}),await B(O),H(O),b(!1)},z=(e,t)=>{e?(k.query.title=e,k.page=t):delete k.query.title,D({...k})},X=(0,s.useRef)(u().debounce((e,t)=>z(e,t),Number("500")||300)).current,G=(0,s.useMemo)(()=>{let e=e=>{w(e),e?(k.query.status=e,k.page=R):delete k.query.status,D({...k})};return(0,r.jsx)(y.default,{onFilter:e=>{m(e.target.value),X(e.target.value,R)},onFilterStatusChange:t=>e(t.target.value),onClear:()=>{c&&(f(!x),m(""))},filterText:c,filterStatus:q})},[c,q,x,o]);return(0,r.jsx)(p.A,{columns:M,data:A,totalRows:P,loading:j,subheader:!0,persistTableHead:!0,onSort:I,sortServer:!0,pagServer:!0,resetPaginationToggle:x,subHeaderComponent:G,handlePerRowsChange:F,handlePageChange:e=>{O.limit=C,O.page=e,""!==c&&(O.query={title:c}),q&&(O.query={...O.query,status:q}),E&&(O.sort=E.sort),B(O),T(e)}})}}},e=>{var t=t=>e(e.s=t);e.O(0,[9773,636,6593,8792],()=>t(54848)),_N_E=e.O()}]);
//# sourceMappingURL=OperationsTable-8e2738b68eded330.js.map