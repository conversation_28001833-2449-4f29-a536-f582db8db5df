{"version": 3, "file": "static/chunks/pages/vspace-37813acea11fdeca.js", "mappings": "iSAcA,IAAMA,EAAa,CACjB,IAAK,CAAEC,MAAO,CAAE,EAChB,KAAM,CAAEA,MAAO,CAAE,CACnB,EACMC,EAAe,CACnBC,YAAa,EACbC,aAAc,CAEhB,EA8DA,EA5DoB,OAAC,OAAEH,CAAK,KA4DbI,QA5DeC,CAAW,CAAE,CA4DjBD,EA5DoBE,EAAyB,GAC/DC,EAAcC,CAAAA,EAAAA,EAAAA,MAAAA,CAAMA,CAAM,MAC1B,CAACC,EAAcC,EAAgB,CAAGC,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAC,GAC3C,CAACC,EAAGC,EAAS,CAAGF,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAQ,EAAE,EAClC,GAAEG,CAAC,CAAE,CAAGC,CAAAA,EAAAA,EAAAA,EAAAA,CAAcA,CAAC,UAGvBC,EAAgB,IACpB,GAAM,MAAEC,CAAI,CAAE,CAAGC,EACjBR,EAAgBO,EAClB,EAgBA,MALAE,CAAAA,EAAAA,EAAAA,SAAAA,CAASA,CAAC,KAERN,EADab,EAAMoB,GAAG,CAAC,CAACH,EAAMI,IAAQhB,EAAYY,EAAMI,IAE1D,EAAG,CAACrB,EAAM,EAGR,WAACsB,MAAAA,CAAIC,MAAO,CAACC,SAAU,UAAU,YAC/B,UAACC,EAAAA,CAAGA,CAAAA,CAACC,GAAI,GAAIH,MAAO,CAACrB,YAAa,MAAM,WACtC,UAACyB,IAAaA,CACZ3B,MAAOY,EACPgB,YAAanB,EACbV,IAHY4B,OAGA5B,EACZ8B,UAAU,EACVC,uBAAwB,GACxB5B,YAAaD,EAAaC,WAAW,CACrCC,aAAcF,EAAaE,YAAY,CACvC4B,qBAAqB,EACrBC,cAAehB,EACfiB,eAAgBjB,EAChBkB,UAAWlB,EACXmB,IAAK5B,MAGRP,GAAUA,EAAMoC,MAAM,CAAG,EACxB,WAACC,EAAAA,QAAQA,CAAAA,WACP,UAACf,MAAAA,CAAIgB,UAAU,gCAAgCC,QAnClC,CAmC2CC,IAlC1DjC,EAAYkC,OAAO,EAAE,EACXA,OAAO,CAACC,SAAS,EAEjC,EA+B8EC,aAAW,0BAAiB,UAAC/B,IAAAA,CAAE0B,UAAU,gCAC/G,UAAChB,MAAAA,CAAIgB,UAAU,gCAAgCC,QA/BlC,CA+B2CK,IA9B1DrC,EAAYkC,OAAO,EAAE,EACXA,OAAO,CAACI,SAAS,EAEjC,EA2B8EF,aAAW,sBAAa,UAAC/B,IAAAA,CAAE0B,UAAU,oCAG3G,UAACQ,EAAAA,CAAGA,CAAAA,UACF,UAACrB,EAAAA,CAAGA,CAAAA,CAACsB,GAAI,GAAIT,UAAU,eACrB,UAAChB,MAAAA,CAAIgB,UAAU,uBAAexB,EAAE,mBAM9C,uDC8HA,MA9Le,KACb,GAAM,GAAEA,CAAC,CAAE,CAAGC,CAAAA,CA6LM,CA7LNA,EAAAA,EAAAA,CAAcA,CAAC,UACvB,CAACiC,EAAmBC,EAAqB,CAAGtC,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAQ,EAAE,EAC9D,CAACuC,EAAYC,EAAc,CAAGxC,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAS,IAC/C,CAACyC,EAAqBC,EAAsB,CAAG1C,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAQ,EAAE,EACjE,CAAC2C,EAAyBC,EAA0B,CAAG5C,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAQ,EAAE,EACzE,CAAC6C,EAAeC,EAAiB,CAAG9C,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAQ,EAAE,EAEtD+C,EAAe,CACnBC,KAAM,CAAEC,WAAY,MAAO,EAC3BC,MAAM,EACNC,MAAO,CAAC,EACRC,SAAU,CACR,CAAEC,KAAM,QAASC,OAAQ,OAAQ,EACjC,CAAED,KAAM,cAAeC,OAAQ,KAAM,EACtC,CACDA,OAAQ,0GACV,EAEMC,EAAY,IACZC,GAAG,CAACT,EAAaI,KAAK,CAAG,CAAE,MAASK,EAAE,EAC1CC,GACF,EAEMC,EAAoBC,IAAAA,QAAU,CAAC,GAAeJ,EAAUC,GAAII,OAAOC,KAAgC,GAAK,KAOxGJ,EAAc,UAClB,IAAMK,EAAe,MAAMC,EAAAA,CAAUA,CAACC,IAAI,CAAC,uBAAwB,CAAC,GACpE,GAAGF,GAAgBA,EAAa,GAAM,CACpC,CADsC,EAClC,CACF,EAF2B,CAErB,MAAEG,CAAI,CAAE,CAAG,MAAMF,EAAAA,CAAUA,CAACG,GAAG,CAAC,UAAWnB,EACjD,OAAMoB,EAAcF,EAAMH,EAAcxB,EAAsBI,EAAuBI,EAAkBF,EACzG,CAAE,MAAOwB,EAAK,CACZ9B,EAAqB,EAAE,CACzB,CAGJ,EAEA9B,CAAAA,EAAAA,EAAAA,SAAAA,CAASA,CAAC,KACRiD,GACF,EAAG,EAAE,EAEL,IAAMY,EAAmB,IACvB,IAAM1D,EAAM2D,SAASC,aAAa,CAAC,MACnC5D,GAAI6D,SAAS,CAAGC,EAChB,IAAMC,EAAS/D,EAAIgE,WAAW,EAAIhE,EAAIiE,SAAS,EAAI,GACnD,OAAQF,EAAOjD,MAAM,CAtDF,EAsDKoD,EAAiB,GAA2C,OAAxCH,EAAOI,SAAS,CAAC,EAAGD,KAAoB,OAAOH,CAC7F,EAiBMK,EAlB6E,MAkBpDzE,IACzBA,GAAQA,EAAK0E,GAAG,EAAE,CACpB,MAAMjB,EAAAA,CAAUA,CAACC,IAAI,CAAC,2BAA4B,CAChD,aAAe,EACf,IAAO1D,EAAK0E,GAAG,GAGjBtC,EADsBD,EAAoBwC,MAAM,CAAC,GAAOC,EAAEF,GAAG,GAAK1E,EAAK0E,GAAG,GAE1E1C,EAAqB6C,GAAU,CAAE7E,KAAS6E,EAAO,EACjDC,EAAAA,EAAKA,CAACC,OAAO,CAAClF,EAAE,8BAGpB,EA6BMmF,EAAiB,MAAOhF,IACxBA,GAAQA,EAAK0E,GAAG,EAAE,MACDjB,EAAAA,CAAUA,CAACC,IAAI,CAAC,2BAA4B,CAC7D,IAAO1D,EAAK0E,GAAG,KAGjBlC,EAAiByC,GAAQ,IAAIA,EAAMjF,EAAK0E,GAAG,CAAC,EAC5CpC,EAA0B2C,GAAQ,IAAIA,EAAK,EAC3CH,EAAAA,EAAKA,CAACC,OAAO,CAAClF,EAAE,8BAGpB,EAEMqF,EAAwB,IAE1B,UAACC,IAAIA,CAACC,KAAK,KAAND,iBAA4BE,GAAG,0BAClC,UAACC,EAAAA,CAAMA,CAAAA,UAAEzF,EAAE,2BAKX0F,EAAeC,CAAAA,EAAAA,EAAAA,YAAAA,CAAYA,CAAC,IAAM,UAACN,EAAAA,CAAAA,IAEzC,MACE,WAACO,EAAAA,CAASA,CAAAA,CAACpE,UAAU,mBACnB,UAACQ,EAAAA,CAAGA,CAAAA,CAACR,UAAU,uBACb,UAACb,EAAAA,CAAGA,CAAAA,CAACC,GAAI,YACP,UAACiF,EAAAA,CAAWA,CAAAA,CAACC,MAAO9F,EAAE,8BAG1B,WAACgC,EAAAA,CAAGA,CAAAA,WACF,UAACrB,EAAAA,CAAGA,CAAAA,CAACa,UAAU,OAAOuE,GAAI,WACxB,WAACvF,MAAAA,CAAIgB,UAAU,sBACb,UAACwE,QAAAA,CAAMxE,UAAU,2BAA2ByE,SAtHhCC,CAsH0CC,GArH9D9D,EAAc6D,EAAEE,MAAM,CAACC,KAAK,EAC5B9C,EAAkB2C,EAAEE,MAAM,CAACC,KAAK,CAClC,EAmH8EA,MAAOjE,EAAYkE,KAAK,OAAOC,KAAK,GAAGC,YAAcxG,EAAE,mBAC3H,UAACyG,IAAAA,CAAElB,KAAK,IAAI/D,UAAU,uBAAc,UAAC1B,IAAAA,CAAE0B,UAAU,yBAGrD,UAACb,EAAAA,CAAGA,CAAAA,CAACoF,GAAI,IAET,UAACpF,EAAAA,CAAGA,CAAAA,CAACoF,GAAI,EAAGvE,UAAU,wBACpB,UAACkE,EAAAA,CAAAA,QAGL,WAAC1D,EAAAA,CAAGA,CAAAA,CAACR,UAAU,8BACb,UAACb,EAAAA,CAAGA,CAAAA,CAACa,UAAU,eAAeZ,GAAI,YAChC,UAAC8F,KAAAA,UAAG,UAACC,OAAAA,UAAM3G,EAAE,iCAEf,UAACW,EAAAA,CAAGA,CAAAA,CAACC,GAAI,YACP,UAACtB,EAAWA,CAACsH,SAAS,MAAVtH,EAAkBJ,MAAOgD,EAAmB3C,YAxGhC,CAwG6CsH,EAxGjCtG,KAC1C,IAAMuG,EAAgB5C,EAAiB/D,EAAK4G,WAAW,EAEvD,MACE,UAACzB,IAAIA,CAEHC,KAAO,KAFJD,gBAGHE,GAAI,gBAAyB,OAATrF,EAAK0E,GAAG,WAE5B,UAACrE,MAAAA,CAAIgB,UAAU,6BACb,UAACwF,EAAAA,CAAOA,CAAAA,CAACC,OAAQ9G,EAAK2F,KAAK,CAAEoB,KAAMJ,OALhC3G,EAAK0E,GAAG,CASnB,SA6FI,WAAC7C,EAAAA,CAAGA,CAAAA,CAACR,UAAU,qDACb,UAACb,EAAAA,CAAGA,CAAAA,CAACa,UAAU,eAAeZ,GAAI,YAChC,UAAC8F,KAAAA,UAAG,UAACC,OAAAA,UAAM3G,EAAE,yCAEf,UAACW,EAAAA,CAAGA,CAAAA,CAACC,GAAI,YACP,UAACtB,EAAWA,CAACJ,MAAOoD,EAAqB/C,OAA7BD,KAnFQ,CAACa,EAAWI,KACtC,IAAMuG,EAAgB5C,EAAiB/D,EAAK4G,WAAW,EAEvD,MACE,WAACvG,MAAAA,CAAIgB,UAAU,8BACb,UAACwF,EAAAA,CAAOA,CAAAA,CAACC,OAAQ9G,EAAK2F,KAAK,CAAEoB,KAAMJ,IACnC,UAACtG,MAAAA,CAAIgB,UAAY,wBAAwBC,QAAS,IAAMmD,EAAgBzE,YAAQH,EAAE,wBAF5CG,EAAK0E,GAAG,CAKpD,SA6EI,WAAC7C,EAAAA,CAAGA,CAAAA,CAACR,UAAU,wCACb,UAACb,EAAAA,CAAGA,CAAAA,CAACa,UAAU,eAAeZ,GAAI,YAChC,UAAC8F,KAAAA,UAAG,UAACC,OAAAA,UAAM3G,EAAE,kCAEf,UAACW,EAAAA,CAAGA,CAAAA,CAACC,GAAI,YACP,UAACtB,EAAWA,CAACJ,MAAOsD,EAAyBjD,OAAjCD,KAhFY,CAgFkC6H,EAhFtB5G,KAC1C,IAAMuG,EAAgB5C,EAAiB/D,EAAK4G,WAAW,EAEvD,MACE,WAACvG,MAAAA,CAAIgB,UAAU,8BACb,UAACwF,EAAAA,CAAOA,CAAAA,CAACC,OAAQ9G,EAAK2F,KAAK,CAAEoB,KAAMJ,IAEG,CAAC,IAArCpE,EAAc0E,OAAO,CAACjH,EAAK0E,GAAG,EAC5B,UAACrE,MAAAA,CAAIgB,UAAY,wBAAwBC,QAAS,IAAM0D,EAAehF,YAAQH,EAAE,oBAEjF,UAACQ,MAAAA,CAAIgB,UAAY,iCAAyBxB,EAAE,wBANVG,EAAK0E,GAAG,CAUpD,WAuEF,EAWA,eAAeb,EAAcF,CAAS,CAAEH,CAAiB,CAAExB,CAAiE,CAAEI,CAAkE,CAAEI,CAA6D,CAAEF,CAAsE,EACrU,GAAI4E,MAAMC,OAAO,CAACxD,IAASA,EAAKxC,MAAM,CAAG,EAAG,CAC1C,IAAMiG,EAAoB/D,IAAAA,MAAQ,CAACM,EAAM,IACvC,IAAI0D,GAAe,EAInB,OAHIH,MAAMC,OAAO,CAACxH,EAAE2H,WAAW,GAAK3H,EAAE2H,WAAW,CAACnG,MAAM,CAAG,GAAG,CAC5DkG,EAAehE,IAAAA,MAAQ,CAAC1D,EAAE2H,WAAW,CAAE,GAAO1C,EAAEF,GAAG,GAAKlB,EAAa,GAAM,EAAErC,KAAT,CAAe,EAAG,EAEjFkG,CACT,GACME,EAAYH,EAAkBjH,GAAG,CAAC,GAAOyE,EAAEF,GAAG,EAC9C8C,EAAgBnE,IAAAA,MAAQ,CAACM,EAAOhE,IAAwB,IAAjBA,EAAE8H,UAAU,EAA0C,CAAC,IAA9BF,EAAUN,OAAO,CAACtH,EAAE+E,GAAG,GACvFgD,EAAgBrE,IAAAA,MAAQ,CAACM,EAAM,IAAyB,IAAjBhE,EAAE8H,UAAU,EAA2C,CAAC,MAApBR,OAAO,CAACtH,EAAE+E,GAAG,GAC9F1C,EAAqBoF,GACrBhF,EAAsBoF,GAEtB,IAAMG,EAAc,MAAMlE,EAAAA,CAAUA,CAACG,GAAG,CAAC,+CAAgD,CAAEf,MAAO,CAAE+E,aAAcpE,EAAa,GAAM,CAAG,GACpImE,GAD0H,EAC/FhE,IAAI,EAEjCnB,EADmBmF,EAAYhE,IAAI,CAACxD,GAAG,CAAC,GAAYyE,CACnCiD,CADqCC,MAAM,CAACpD,GAAG,GAGlEpC,EAA0BoF,EAC5B,CACF,mOChOO,IAAMlC,EAAeuC,CAAAA,EAAAA,EAAAA,CAAAA,CAAoBA,CAAC,CAC/CC,sBAAuB,KACjBC,EAAMC,WAAW,IAAID,EAAMC,WAAW,CAACJ,MAAM,IAAIG,EAAMC,WAAW,CAACJ,MAAM,CAAC,aAAa,CAK7FK,CAL+F,kBAK3E,cACtB,GAAG,EAE6BJ,CAAAA,EAAAA,EAAAA,CAAAA,CAAoBA,CAAC,CACnDC,sBAAwBC,KAClBA,EAAMC,WAAW,IAAID,EAAMC,WAAW,CAACJ,MAAM,IAAIG,EAAMC,WAAW,CAACJ,MAAM,CAAC,aAAa,CAK7FK,CAL+F,kBAK3E,mBACpBC,iBAAkB,IAAM,UAACC,EAAAA,OAAIA,CAAAA,CAAAA,EAC/B,GAAG,EAE0BN,CAAAA,EAAAA,EAAAA,CAAAA,CAAoBA,CAAC,CAChDC,sBAAuB,CAACC,EAAO5I,KAC7B,GAAI4I,EAAMC,WAAW,EAAID,EAAMC,WAAW,CAACJ,MAAM,EAAE,GAC7CG,EAAMC,WAAW,CAACJ,MAAM,CAAC,aAAa,CACxC,CAD0C,MACnC,OAEP,GAAIG,EAAMC,WAAW,CAACJ,MAAM,CAAC,aAAa,EAAE,EAChCA,MAAM,EAAIzI,EAAMyI,MAAM,CAACQ,IAAI,EAAIjJ,EAAMyI,MAAM,CAACQ,IAAI,CAAC5D,GAAG,GAAKuD,EAAMK,IAAI,CAAC5D,GAAG,CAC/E,CADiF,MAC1E,CAGb,CAEF,OAAO,CACT,EACAyD,mBAAoB,eACtB,GAAG,EAE8BJ,CAAAA,EAAAA,EAAAA,CAAAA,CAAoBA,CAAC,CACpDC,sBAAuB,CAACC,EAAO5I,KAC7B,GAAI4I,EAAMC,WAAW,EAAID,EAAMC,WAAW,CAACJ,MAAM,EAC/C,GAAIG,EAAMC,WAAW,CAACJ,MAAM,CAAC,aAAa,CACxC,CAD0C,MACnC,OAEP,GAAIG,EAAMC,WAAW,CAACJ,MAAM,CAAC,aAAa,EAAE,EAChCA,MAAM,EAAIzI,EAAMyI,MAAM,CAACQ,IAAI,EAAIjJ,EAAMyI,MAAM,CAACQ,IAAI,CAAC5D,GAAG,GAAKuD,EAAMK,IAAI,CAAC5D,GAAG,CAC/E,CADiF,MAC1E,CAGb,CAEF,OAAO,CACT,EACAyD,mBAAoB,oBACpBC,iBAAkB,IAAM,UAACC,EAAAA,OAAIA,CAAAA,CAAAA,EAC/B,GAAG,EAEoCN,CAAAA,EAAAA,EAAAA,CAAAA,CAAoBA,CAAC,CAC1DC,sBAAuB,KACjBC,EAAMC,WAAW,IAAID,EAAMC,WAAW,CAACK,MAAM,IAAIN,EAAMC,WAAW,CAACK,MAAM,CAAC,WAAW,CAK3FJ,CAL6F,kBAKzE,yBACtB,GAAG,EAEY3C,YAAYA,EAAC,kDCvEb,SAASE,EAAYrG,CAAuB,EACzD,MACE,UAACmJ,KAAAA,CAAGnH,UAAU,wBAAgBhC,EAAMsG,KAAK,EAE7C,mBCPA,4CACA,UACA,WACA,OAAe,EAAQ,KAAqC,CAC5D,EACA,SAFsB,wFCWtB,SAAS8C,EAAUpJ,CAAqB,EACtC,GAAM,MAAEqJ,CAAI,iBAAEC,CAAe,CAAE,CAAGtJ,EAClC,MACE,WAACuJ,EAAAA,CAAKA,CAAAA,CACH,GAAGvJ,CAAK,CACTsJ,gBAAiBA,EACjBE,kBAAgB,gCAChBC,QAAQ,cAER,UAACF,EAAAA,CAAKA,CAACG,MAAM,EAACC,WAAW,aACvB,UAACJ,EAAAA,CAAKA,CAACK,KAAK,EAACC,GAAG,yCACbR,EAAKS,OAAO,KAGjB,UAACP,EAAAA,CAAKA,CAACQ,IAAI,WACRV,EAAK3B,IAAI,KAIlB,CAUA,SAASsC,EAAWhK,CAAsB,EACxC,GAAM,MAAEqJ,CAAI,CAAE,CAAGrJ,EACX,CAACiK,EAAWC,EAAa,CAAGC,EAAAA,QAAc,EAAC,UACjD,GAAYd,EAAK3B,IAAI,CAEjB,CAFmB,EAEnB,8BACE,UAAC0C,SAAAA,CAAOtD,KAAK,SAAS7E,QAAS,IAAMiI,GAAa,GAAOjJ,MAAO,CAAEoJ,OAAQ,OAAQC,WAAY,OAAQC,QAAS,CAAE,WAC/G,UAACC,EAAAA,CAAIA,CAACC,MAAM,WACV,UAACnK,IAAAA,CAAE0B,UAAU,4BAGhBhC,EAAMqJ,IAAI,EAAI,UAACD,EAAAA,CAAUC,KAAMrJ,EAAMqJ,IAAI,CAAEqB,KAAMT,EAAWU,OAAQ,IAAMT,GAAa,GAAQZ,gBAAiBtJ,EAAMsJ,eAAe,MAIrI,IACT,CA4BA,MAhBA,SAAS9B,CAA2B,EAClC,GAAM,QAAEC,CAAM,IAeDD,EAfGE,CAAI,CAAE,CAAG1H,EAEzB,EAaqB,IAZnB,WAACwK,EAAAA,CAAIA,CAAAA,CAACxI,UAAU,iCACd,UAACwI,EAAAA,CAAIA,CAACd,MAAM,WAAEjC,IACd,UAAC+C,EAAAA,CAAIA,CAACT,IAAI,WACR,UAACS,EAAAA,CAAIA,CAACI,IAAI,WACPlD,MAGL,UAACsC,EAAAA,CAAY,GAAGhK,CAAK,KAG3B", "sources": ["webpack://_N_E/./components/common/RKICarousel.tsx", "webpack://_N_E/./pages/vspace/index.tsx", "webpack://_N_E/./pages/vspace/permission.tsx", "webpack://_N_E/./components/common/PageHeading.tsx", "webpack://_N_E/?8ebf", "webpack://_N_E/./components/common/RKICard.tsx"], "sourcesContent": ["//Import Library\r\nimport React, { useState, useRef, Fragment, useEffect } from \"react\";\r\nimport { Col, Row } from 'react-bootstrap';\r\nimport AliceCarousel from \"react-alice-carousel\";\r\n\r\n//Import services/components\r\nimport { useTranslation } from 'next-i18next';\r\n\r\ninterface RKICarouselProps {\r\n  items: any[];\r\n  renderItems: (item: any, index: number) => React.ReactNode;\r\n  selector?: string;\r\n}\r\n\r\nconst responsive = {\r\n  576: { items: 1 },\r\n  1024: { items: 4 }\r\n};\r\nconst stagePadding = {\r\n  paddingLeft: 0,\r\n  paddingRight: 0,\r\n  width: 250\r\n};\r\n\r\nconst RKICarousel = ({ items, renderItems, ...props }: RKICarouselProps) => {\r\n  const CarouselRef = useRef<any>(null);\r\n  const [currentIndex, setCurrentIndex] = useState(0);\r\n  const [i, setItems] = useState<any[]>([]);\r\n  const { t } = useTranslation('common');\r\n\r\n\r\n  const onSlideChange = (event: { item: number }) => {\r\n    const { item } = event;\r\n    setCurrentIndex(item);\r\n  };\r\n  const onChangePrev = () => {\r\n    if (CarouselRef.current) {\r\n      CarouselRef.current.slidePrev();\r\n    }\r\n  };\r\n  const onChangeNext = () => {\r\n    if (CarouselRef.current) {\r\n      CarouselRef.current.slideNext();\r\n    }\r\n  };\r\n  useEffect(() => {\r\n    const data = items.map((item, idx) => renderItems(item, idx));\r\n    setItems(data);\r\n  }, [items])\r\n\r\n  return (\r\n    <div style={{position: 'relative'}}>\r\n      <Col lg={12} style={{paddingLeft: \"13px\"}}>\r\n        <AliceCarousel\r\n          items={i}\r\n          activeIndex={currentIndex}\r\n          responsive={responsive}\r\n          infinite={true}\r\n          disableButtonsControls={true}\r\n          paddingLeft={stagePadding.paddingLeft}\r\n          paddingRight={stagePadding.paddingRight}\r\n          disableDotsControls={true}\r\n          onInitialized={onSlideChange}\r\n          onSlideChanged={onSlideChange}\r\n          onResized={onSlideChange}\r\n          ref={CarouselRef}\r\n        />\r\n      </Col>\r\n      {items && (items.length > 0) ? (\r\n        <Fragment>\r\n          <div className=\"alice-carousel__prev-btn-item\" onClick={onChangePrev} aria-label=\"Previous Slide\"><i className=\"fa fa-chevron-circle-left\" /></div>\r\n          <div className=\"alice-carousel__next-btn-item\" onClick={onChangeNext} aria-label=\"Next Slide\"><i className=\"fa fa-chevron-circle-right\" /></div>\r\n        </Fragment>\r\n      ) : (\r\n          <Row>\r\n            <Col xs={12} className=\"p-0\">\r\n              <div className=\"text-center\">{t(\"Content\")}</div>\r\n            </Col>\r\n          </Row>\r\n        )}\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default RKICarousel;\r\n", "//Import Library\r\nimport React, { useEffect, useState } from \"react\";\r\nimport Link from 'next/link';\r\nimport _ from \"lodash\";\r\nimport { Container, Row, Col, Button } from 'react-bootstrap';\r\nimport toast from 'react-hot-toast';\r\n\r\n\r\n//Import services/components\r\nimport RKICarousel from \"../../components/common/RKICarousel\";\r\nimport apiService from \"../../services/apiService\";\r\nimport RK<PERSON><PERSON> from \"../../components/common/RKICard\";\r\nimport { useTranslation } from 'next-i18next';\r\nimport PageHeading from \"../../components/common/PageHeading\";\r\nimport { canAddVspace } from \"./permission\";\r\nimport { serverSideTranslations } from 'next-i18next/serverSideTranslations';\r\nconst truncateLength = 180;\r\n\r\nconst VSpace = (): React.ReactElement => {\r\n  const { t } = useTranslation('common');\r\n  const [subscribedContent, setSubscribedContent] = useState<any[]>([]);\r\n  const [filterText, setFilterText] = useState<string>('');\r\n  const [publicVirtualspaces, setPublicVirtualspace] = useState<any[]>([]);\r\n  const [restrictedVirtualSpaces, setRestrictedVirtualSpace] = useState<any[]>([]);\r\n  const [vspaceRequest, setVspaceRequest] = useState<any[]>([]);\r\n\r\n  const vspaceParams = {\r\n    sort: { created_at: \"desc\" },\r\n    lean: true,\r\n    query: {},\r\n    populate: [\r\n      { path: \"topic\", select: \"title\" },\r\n      { path: \"subscribers\", select: \"_id\" }\r\n    ],\r\n    select: \"-start_date -end_date -members -images -user -created_at -updated_at -file_category -vspace_email_invite\"\r\n  };\r\n\r\n  const sendQuery = (q: string) => {\r\n    if (q) {vspaceParams.query = { \"title\": q }}\r\n    fetchVspace()\r\n  }\r\n\r\n  const handleSearchTitle = _.debounce((q: string) => sendQuery(q), Number(process.env.SEARCH_DEBOUNCE_TIME) || 300);\r\n\r\n  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {\r\n    setFilterText(e.target.value);\r\n    handleSearchTitle(e.target.value)\r\n  }\r\n\r\n  const fetchVspace = async () => {\r\n    const responseUser = await apiService.post(\"/users/getLoggedUser\", {});\r\n    if(responseUser && responseUser['_id']) {\r\n      try {\r\n        const { data } = await apiService.get('/vspace', vspaceParams);\r\n        await response_func(data, responseUser, setSubscribedContent, setPublicVirtualspace, setVspaceRequest, setRestrictedVirtualSpace);\r\n      } catch (err) {\r\n        setSubscribedContent([]);\r\n      }\r\n    }\r\n\r\n  };\r\n\r\n  useEffect(() => {\r\n    fetchVspace();\r\n  }, []);\r\n\r\n  const getTrimmedString = (html: string) => {\r\n    const div = document.createElement(\"div\");\r\n    div.innerHTML = html;\r\n    const string = div.textContent || div.innerText || \"\";\r\n    return (string.length > truncateLength ? `${string.substring(0, truncateLength - 3)}...` : string);\r\n  }\r\n\r\n  const onRenderSubscribedItems = (item: any, idx: number) => {\r\n    const trimmedString = getTrimmedString(item.description);\r\n\r\n    return (\r\n      <Link\r\n        key={item._id}\r\n        href={`vspace/[...routes]`}\r\n        as={`/vspace/show/${item._id}`}\r\n        >\r\n        <div className='rki-carousel-card'>\r\n          <RKICard header={item.title} body={trimmedString} />\r\n        </div>\r\n      </Link>\r\n    );\r\n  }\r\n  const updateSubscribe = async (item: any) => {\r\n    if (item && item._id) {\r\n      await apiService.post('/vspace/updateSubscriber', {\r\n        \"isSubscribe\": true,\r\n        \"_id\": item._id\r\n      });\r\n      const notSubscribed = publicVirtualspaces.filter((d) => d._id !== item._id);\r\n      setPublicVirtualspace(notSubscribed);\r\n      setSubscribedContent(oldArr => [ item ,...oldArr]);\r\n      toast.success(t('vspace.subscribedForToast'));\r\n\r\n    }\r\n  }\r\n\r\n  const onRenderPublicItems = (item: any, idx: number) => {\r\n    const trimmedString = getTrimmedString(item.description);\r\n\r\n    return (\r\n      <div className='rki-carousel-card' key={item._id}>\r\n        <RKICard header={item.title} body={trimmedString} />\r\n        <div className={`hover-btn text-center`} onClick={() => updateSubscribe(item)}>{t(\"vspace.Subscribe\")}</div>\r\n      </div>\r\n    );\r\n  }\r\n\r\n  const onRenderRestrictedItems = (item: any, idx: number) => {\r\n    const trimmedString = getTrimmedString(item.description);\r\n\r\n    return (\r\n      <div className='rki-carousel-card' key={item._id}>\r\n        <RKICard header={item.title} body={trimmedString} />\r\n        {\r\n          vspaceRequest.indexOf(item._id) === -1 ?\r\n            <div className={`hover-btn text-center`} onClick={() => requestConfirm(item)}>{t(\"vspace.Request\")}</div>\r\n            :\r\n            <div className={`hover-btn text-center`}>{t(\"vspace.Requested\")}</div>\r\n        }\r\n      </div>\r\n    );\r\n  };\r\n\r\n  const requestConfirm = async (item: any) => {\r\n    if (item && item._id) {\r\n      const resp = await apiService.post('/vspace/subscribeRequest', {\r\n        \"_id\": item._id\r\n      });\r\n      if(resp){\r\n      setVspaceRequest(prev => [...prev, item._id]);\r\n      setRestrictedVirtualSpace(prev => [...prev]);\r\n      toast.success(t(\"vspace.vspacehasrequested\"));\r\n      }\r\n    }\r\n  }\r\n\r\n  const AddNewVspaceComponent = () => {\r\n    return (\r\n      <Link href=\"/vspace/[...routes]\" as=\"/vspace/create\" >\r\n        <Button>{t('createvirtualSpaces')}</Button>\r\n      </Link>\r\n    );\r\n  };\r\n\r\n  const CanAddVspace = canAddVspace(() => <AddNewVspaceComponent />)\r\n\r\n  return (\r\n    <Container className='vspace'>\r\n      <Row className='page-header'>\r\n        <Col lg={12}>\r\n          <PageHeading title={t('vspace.virtualSpaces')} />\r\n        </Col>\r\n      </Row>\r\n      <Row>\r\n        <Col className=\"ps-2\" md={6}>\r\n          <div className=\"searchbar\">\r\n            <input className=\"searchInput form-control\" onChange={handleChange} value={filterText} type=\"text\" name=\"\" placeholder= {t(\"vspace.Search\")} />\r\n            <a href=\"#\" className=\"search_icon\"><i className=\"fas fa-search\"></i></a>\r\n          </div>\r\n        </Col>\r\n        <Col md={3}>\r\n        </Col>\r\n        <Col md={3} className=\"createVSpace\">\r\n          <CanAddVspace />\r\n        </Col>\r\n      </Row>\r\n      <Row className='subscriptionBlock'>\r\n        <Col className='header-block' lg={12}>\r\n          <h6><span>{t('vspace.subscribedVspace')}</span></h6>\r\n        </Col>\r\n        <Col lg={12}>\r\n          <RKICarousel selector=\"title\" items={subscribedContent} renderItems={onRenderSubscribedItems} />\r\n        </Col>\r\n      </Row>\r\n      <Row className='subscriptionBlock publicVSpace popButton'>\r\n        <Col className='header-block' lg={12}>\r\n          <h6><span>{t('vspace.publicVspaceUnSubscribed')}</span></h6>\r\n        </Col>\r\n        <Col lg={12}>\r\n          <RKICarousel items={publicVirtualspaces} renderItems={onRenderPublicItems} />\r\n        </Col>\r\n      </Row>\r\n      <Row className='subscriptionBlock popButton'>\r\n        <Col className='header-block' lg={12}>\r\n          <h6><span>{t('vspace.restrictedVSpaces')}</span></h6>\r\n        </Col>\r\n        <Col lg={12}>\r\n          <RKICarousel items={restrictedVirtualSpaces} renderItems={onRenderRestrictedItems} />\r\n        </Col>\r\n      </Row>\r\n    </Container>\r\n  );\r\n}\r\n\r\nexport async function getStaticProps({ locale } : { locale: string}) {\r\n  return {\r\n    props: {\r\n      ...(await serverSideTranslations(locale, ['common'])),\r\n    },\r\n  }\r\n}\r\n\r\nexport default VSpace;\r\nasync function response_func(data: any, responseUser: any, setSubscribedContent: React.Dispatch<React.SetStateAction<any[]>>, setPublicVirtualspace: React.Dispatch<React.SetStateAction<any[]>>, setVspaceRequest: React.Dispatch<React.SetStateAction<any[]>>, setRestrictedVirtualSpace: React.Dispatch<React.SetStateAction<any[]>>) {\r\n  if (Array.isArray(data) && data.length > 0) {\r\n    const subscribedVspaces = _.filter(data, (i) => {\r\n      let isSubscribed = false;\r\n      if (Array.isArray(i.subscribers) && i.subscribers.length > 0) {\r\n        isSubscribed = _.filter(i.subscribers, (d) => d._id === responseUser['_id']).length > 0;\r\n      }\r\n      return isSubscribed;\r\n    });\r\n    const vSpaceIds = subscribedVspaces.map((d) => d._id);\r\n    const publicVspaces = _.filter(data, (i) => (i.visibility === true && vSpaceIds.indexOf(i._id) === -1));\r\n    const privateVspace = _.filter(data, (i) => (i.visibility === false && vSpaceIds.indexOf(i._id) === -1));\r\n    setSubscribedContent(subscribedVspaces);\r\n    setPublicVirtualspace(publicVspaces);\r\n\r\n    const requestData = await apiService.get('/vspace-request-subscribers/getRequestedByMe', { query: { requested_by: responseUser['_id'] } });\r\n    if (requestData && requestData.data) {\r\n      const updateData = requestData.data.map((d: any) => d.vspace._id);\r\n      setVspaceRequest(updateData);\r\n    }\r\n    setRestrictedVirtualSpace(privateVspace);\r\n  }\r\n}\r\n\r\n", "//Import Library\r\nimport React from 'react';\r\nimport connectedAuthWrapper from 'redux-auth-wrapper/connectedAuthWrapper';\r\n\r\n//Import services/components\r\nimport R403 from \"../r403\";\r\n\r\nexport const canAddVspace = connectedAuthWrapper({\r\n  authenticatedSelector: (state) => {\r\n    if (state.permissions && state.permissions.vspace && state.permissions.vspace['create:any']) {\r\n      return true;\r\n    }\r\n    return false;\r\n  },\r\n  wrapperDisplayName: 'CanAddVspace',\r\n});\r\n\r\nexport const canAddVspaceForm = connectedAuthWrapper({\r\n  authenticatedSelector: (state) => {\r\n    if (state.permissions && state.permissions.vspace && state.permissions.vspace['create:any']) {\r\n      return true;\r\n    }\r\n    return false;\r\n  },\r\n  wrapperDisplayName: 'CanAddVspaceForm',\r\n  FailureComponent: () => <R403/>\r\n});\r\n\r\nexport const canEditVspace = connectedAuthWrapper({\r\n  authenticatedSelector: (state, props) => {\r\n    if (state.permissions && state.permissions.vspace) {\r\n      if (state.permissions.vspace['update:any']) {\r\n        return true;\r\n      } else { //if update:own\r\n        if (state.permissions.vspace['update:own']) {\r\n          if (props.vspace && props.vspace.user && props.vspace.user._id === state.user._id) {\r\n            return true;\r\n          }  \r\n        }\r\n      }\r\n    }\r\n    return false;\r\n  },\r\n  wrapperDisplayName: 'CanEditVspace',\r\n});\r\n\r\nexport const canEditVspaceForm = connectedAuthWrapper({\r\n  authenticatedSelector: (state, props) => {\r\n    if (state.permissions && state.permissions.vspace) {\r\n      if (state.permissions.vspace['update:any']) {\r\n        return true;\r\n      } else { //if update:own\r\n        if (state.permissions.vspace['update:own']) {\r\n          if (props.vspace && props.vspace.user && props.vspace.user._id === state.user._id) {\r\n            return true;\r\n          }\r\n        }\r\n      }\r\n    }\r\n    return false;\r\n  },\r\n  wrapperDisplayName: 'CanEditVspaceForm',\r\n  FailureComponent: () => <R403/>\r\n});\r\n\r\nexport const canViewDiscussionUpdate = connectedAuthWrapper({\r\n  authenticatedSelector: (state) => {\r\n    if (state.permissions && state.permissions.update && state.permissions.update['read:any']) {\r\n      return true;\r\n    }\r\n    return false;\r\n  },\r\n  wrapperDisplayName: 'CanViewDiscussionUpdate',\r\n});\r\n\r\nexport default canAddVspace;", "interface PageHeadingProps {\r\n  title: string; // Required based on actual usage\r\n}\r\n\r\nexport default function PageHeading(props: PageHeadingProps) {\r\n  return (\r\n    <h2 className=\"page-heading\">{props.title}</h2>\r\n  )\r\n}\r\n", "\n    (window.__NEXT_P = window.__NEXT_P || []).push([\n      \"/vspace\",\n      function () {\n        return require(\"private-next-pages/vspace/index.tsx\");\n      }\n    ]);\n    if(module.hot) {\n      module.hot.dispose(function () {\n        window.__NEXT_P.push([\"/vspace\"])\n      });\n    }\n  ", "//Import Library\r\nimport React from \"react\";\r\nimport { Card } from \"react-bootstrap\";\r\nimport Modal from \"react-bootstrap/Modal\";\r\n\r\ninterface ListModalProps {\r\n  list: {\r\n    heading: string;\r\n    body: React.ReactNode;\r\n  };\r\n  dialogClassName?: string;\r\n  show: boolean;\r\n  onHide: () => void;\r\n}\r\n\r\nfunction ListModal(props: ListModalProps) {\r\n  const { list, dialogClassName } = props;\r\n  return (\r\n    <Modal\r\n      {...props}\r\n      dialogClassName={dialogClassName}\r\n      aria-labelledby=\"contained-modal-title-vcenter\"\r\n      centered\r\n    >\r\n      <Modal.Header closeButton>\r\n        <Modal.Title id=\"contained-modal-title-vcenter\">\r\n          {list.heading}\r\n        </Modal.Title>\r\n      </Modal.Header>\r\n      <Modal.Body>\r\n        {list.body}\r\n      </Modal.Body>\r\n    </Modal>\r\n  )\r\n}\r\n\r\ninterface CardFooterProps {\r\n  list?: {\r\n    body: React.ReactNode;\r\n    heading: string;\r\n  };\r\n  dialogClassName?: string;\r\n}\r\n\r\nfunction CardFooter(props: CardFooterProps) {\r\n  const { list } = props;\r\n  const [modalShow, setModalShow] = React.useState(false);\r\n  if (list && list.body) {\r\n    return (\r\n      <>\r\n        <button type=\"button\" onClick={() => setModalShow(true)} style={{ border: 'none', background: 'none', padding: 0 }}>\r\n          <Card.Footer>\r\n            <i className=\"fas fa-chevron-down\" />\r\n          </Card.Footer>\r\n        </button>\r\n        {props.list && <ListModal list={props.list} show={modalShow} onHide={() => setModalShow(false)} dialogClassName={props.dialogClassName} />}\r\n      </>\r\n    )\r\n  }\r\n  return null;\r\n}\r\n\r\ninterface RKICardProps {\r\n  header: string;\r\n  body: React.ReactNode;\r\n  list?: {\r\n    body: React.ReactNode;\r\n    heading: string;\r\n  };\r\n  dialogClassName?: string;\r\n}\r\n\r\nfunction RKICard(props: RKICardProps) {\r\n  const { header, body } = props\r\n\r\n  return (\r\n    <Card className=\"text-center infoCard\">\r\n      <Card.Header>{header}</Card.Header>\r\n      <Card.Body>\r\n        <Card.Text>\r\n          {body}\r\n        </Card.Text>\r\n      </Card.Body>\r\n      <CardFooter {...props} />\r\n    </Card>\r\n  )\r\n}\r\n\r\nexport default RKICard;\r\n"], "names": ["responsive", "items", "stagePadding", "paddingLeft", "paddingRight", "RKICarousel", "renderItems", "props", "CarouselRef", "useRef", "currentIndex", "setCurrentIndex", "useState", "i", "setItems", "t", "useTranslation", "onSlideChange", "item", "event", "useEffect", "map", "idx", "div", "style", "position", "Col", "lg", "AliceCarousel", "activeIndex", "infinite", "disableButtonsControls", "disableDotsControls", "onInitialized", "onSlideChanged", "onResized", "ref", "length", "Fragment", "className", "onClick", "onChangePrev", "current", "slidePrev", "aria-label", "onChangeNext", "slideNext", "Row", "xs", "subscribedContent", "setSubscribedContent", "filterText", "setFilterText", "publicVirtualspaces", "setPublicVirtualspace", "restrictedVirtualSpaces", "setRestrictedVirtualSpace", "vspaceRequest", "setVspaceRequest", "vspaceParams", "sort", "created_at", "lean", "query", "populate", "path", "select", "<PERSON><PERSON><PERSON><PERSON>", "q", "fetchVspace", "handleSearchTitle", "_", "Number", "process", "responseUser", "apiService", "post", "data", "get", "response_func", "err", "getTrimmedString", "document", "createElement", "innerHTML", "html", "string", "textContent", "innerText", "truncate<PERSON><PERSON>th", "substring", "updateSubscribe", "_id", "filter", "d", "oldArr", "toast", "success", "requestConfirm", "prev", "AddNewVspaceComponent", "Link", "href", "as", "<PERSON><PERSON>", "CanAddVspace", "canAddVspace", "Container", "PageHeading", "title", "md", "input", "onChange", "e", "handleChange", "target", "value", "type", "name", "placeholder", "a", "h6", "span", "selector", "onRenderSubscribedItems", "trimmedString", "description", "RKICard", "header", "body", "onRenderRestrictedItems", "indexOf", "Array", "isArray", "subscribedVspaces", "isSubscribed", "subscribers", "vSpaceIds", "publicVspaces", "visibility", "privateVspace", "requestData", "requested_by", "updateData", "vspace", "connectedAuthWrapper", "authenticatedSelector", "state", "permissions", "wrapperDisplayName", "FailureComponent", "R403", "user", "update", "h2", "ListModal", "list", "dialogClassName", "Modal", "aria-<PERSON>by", "centered", "Header", "closeButton", "Title", "id", "heading", "Body", "<PERSON><PERSON><PERSON>er", "modalShow", "setModalShow", "React", "button", "border", "background", "padding", "Card", "Footer", "show", "onHide", "Text"], "sourceRoot": "", "ignoreList": []}