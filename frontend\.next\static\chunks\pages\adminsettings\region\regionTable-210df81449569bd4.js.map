{"version": 3, "file": "static/chunks/pages/adminsettings/region/regionTable-210df81449569bd4.js", "mappings": "0KAqCA,SAASA,EAASC,CAAoB,EACpC,GAAM,CAAEC,CAAC,CAAE,CAAGC,CAAAA,EAAAA,EAAAA,EAAAA,CAAcA,CAAC,UACvBC,EAA6B,CACjCC,gBAAiBH,EAAE,cACnB,EACI,SACJI,CAAO,CACPC,MAAI,WACJC,CAAS,uBACTC,CAAqB,WACrBC,CAAS,oBACTC,CAAkB,qBAClBC,CAAmB,kBACnBC,CAAgB,aAChBC,CAAW,oBACXC,CAAkB,gBAClBC,CAAc,SACdC,CAAO,CACPC,WAAS,sBACTC,CAAoB,mBACpBC,CAAiB,YACjBC,CAAU,QACVC,CAAM,kBACNC,CAAgB,cAChBC,CAAY,CAEZ,CADA,EACGC,EACJ,CAAGxB,EAGEyB,EAAiB,CACrBtB,6BACAuB,gBAAiBzB,EAAE,IAP0C,MAQ7D0B,UAAU,UACVtB,EACAC,KAAMA,GAAQ,EAAE,CAChBsB,OAAO,EACPC,2BAA4BrB,EAC5BsB,UAAWrB,EACXsB,gBAAiBf,qBACjBN,EACAsB,YAAY,EACZC,iBAAkBhB,EAClBiB,kBAAmBpB,GAA0C,GAC7DqB,eADwCrB,cACVD,GAA4B,CAAC,GAAI,GAAI,GAAI,CAA3BA,EAA+B,GAAG,CAC9EuB,oBAAqB7B,EACrB8B,oBAAqB1B,EACrB2B,aAAc1B,iBACdG,uBACAG,oBACAC,EACAoB,kBAAmB,UAACC,EAAAA,CAAYA,CAAAA,CAAAA,GAChCC,SAAU,UAACC,IAAAA,CAAEC,UAAU,6CACvBvB,SACAC,eACAE,mBACAD,EACAqB,UAAW,WACb,EACA,MACE,UAACC,EAAAA,EAASA,CAAAA,CAAE,GAAGnB,CAAc,EAEjC,CAEA1B,EAAS8C,YAAY,CAAG,CACtBf,WAAW,EACXE,YAAY,EACZzB,UAAW,KACXU,WAAW,EACXC,qBAAsB,KACtBC,mBAAmB,EACnBC,YAAY,EACZE,kBAAkB,CACpB,EAEA,MAAevB,QAAQA,EAAC,SC/GxB,4CACA,oCACA,WACA,OAAe,EAAQ,KAAyD,CAChF,EACA,SAFsB,gLCqJtB,MA5IoB,IAChB,GAAM,CAAEE,CAAC,CAAE,CAAGC,CAAAA,EAAAA,EAAAA,EAAAA,CAAcA,CAAC,QA2IlB4C,EA1IL,CAACC,EAAWC,EAAe,CAAGC,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAC,EAAE,EACzC,EAAGC,EAAW,CAAGD,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,EAAC,GAC1B,CAAC1C,EAAW4C,EAAa,CAAGF,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAC,GACrC,CAACG,EAASC,EAAW,CAAGJ,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAC,IACjC,CAACK,EAAaC,EAAS,CAAGN,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAC,IACnC,CAACO,EAAqBC,EAAgB,CAAGR,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAC,CAAC,GACnD,CAACS,EAAWC,EAAa,CAAQV,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAC,IAE1CW,EAAe,CACjBC,KAAM,CAAEC,MAAO,KAAM,EACrBC,MAAOX,EACPY,KAAM,EACNC,MAAO,CAAC,CACZ,EAEM5D,EAAU,CACZ,CACI6D,KAAMjE,EAAE,+BACRkE,SAAU,GAAcC,EAAIN,KAAK,CACjCO,UAAU,CACd,EACA,CACIH,KAAMjE,EAAE,gCACRkE,SAAWC,QAAaA,QAAAA,CAAAA,OAAAA,EAAAA,EAAIE,OAAAA,EAAJF,KAAAA,EAAAA,EAAaN,GAAbM,EAAaN,GAAS,IAC9CO,UAAU,EACVE,KAAM,GAAaC,EAAEF,OAAO,EAAIE,EAAEF,OAAO,CAACR,KAAK,CAAGU,EAAEF,OAAO,CAACR,KAAK,CAAG,EACxE,EACA,CACII,KAAMjE,EAAE,+BACRkE,SAAWC,GAAaA,EAAIK,GAAG,CAC/BJ,UAAU,EACVE,KAAM,GACF,WAACG,MAAAA,WACG,UAACC,IAAIA,CAACC,KAAK,6BAA6BC,GAAI,OAAvCF,uBAA2E,OAANH,EAAEC,GAAG,WAE3E,UAAC/B,IAAAA,CAAEC,UAAU,uBAEV,OAEP,UAACmC,IAAAA,CAAEC,QAAS,IAAMC,EAAWR,YACzB,UAAC9B,IAAAA,CAAEC,UAAU,4BACZ,MAGjB,EACH,CAEKsC,EAAgB,UAClB/B,GAAW,GACX,IAAMgC,EAAW,MAAMC,EAAAA,CAAUA,CAACC,GAAG,CAAC,UAAWxB,GAC7CsB,GAAYA,EAAS5E,IAAI,EAAE,CAC3B0C,EAAekC,EAAS5E,IAAI,EAC5B6C,EAAa+B,EAASG,UAAU,EAChCnC,GAAW,GAEnB,EASMvC,EAAsB,MAAO2E,EAAiBtB,KAChDJ,EAAaG,KAAK,CAAGuB,EACrB1B,EAAaI,IAAI,CAAGA,EACpBN,IAAcE,EAAaK,KAAK,CAAG,CAAEK,QAASZ,EAAU6B,KAAK,CAAC,EAC9DrC,GAAW,GACX,IAAMgC,EAAW,MAAMC,EAAAA,CAAUA,CAACC,GAAG,CAAC,UAAWxB,GAC7CsB,GAAYA,EAAS5E,IAAI,EAAI4E,EAAS5E,IAAI,CAACkF,MAAM,CAAG,GAAG,CACvDxC,EAAekC,EAAS5E,IAAI,EAC5B+C,EAAWiC,GACXpC,GAAW,GAEnB,EAEM8B,EAAa,MAAOZ,IACtBX,EAAgBW,EAAIK,GAAG,EACvBlB,GAAS,EACb,EAEMkC,EAAe,UACjB,GAAI,CACA,MAAMN,EAAAA,CAAUA,CAACO,MAAM,CAAC,WAA+B,OAApBlC,IACnCyB,IACA1B,GAAS,GACToC,EAAAA,EAAKA,CAACC,OAAO,CAAC3F,EAAE,wDACpB,CAAE,MAAO4F,EAAO,CACZF,EAAAA,EAAKA,CAACE,KAAK,CAAC5F,EAAE,kDAClB,CACJ,EAEM6F,EAAY,IAAMvC,GAAS,GAgBjC,MATAwC,CAAAA,EAAAA,EAAAA,SAAAA,CAASA,CAAC,KACNd,GACJ,EAAG,EAAE,EAELc,CAAAA,EAAAA,EAAAA,SAAAA,CAASA,CAAC,KACNrC,GAAcE,GAAaK,KAAK,CAAG,CAAEK,QAASZ,EAAU6B,KAAK,CAAC,EAC9DN,GACJ,EAAG,CAACvB,EAAU,EAGV,WAACgB,MAAAA,CAAI/B,UAAU,0BACX,WAACqD,EAAAA,CAAKA,CAAAA,CAACC,KAAM3C,EAAa4C,OAAQJ,YAC9B,UAACE,EAAAA,CAAKA,CAACG,MAAM,EAACC,WAAW,aACrB,UAACJ,EAAAA,CAAKA,CAACK,KAAK,WAAEpG,EAAE,yCAEpB,UAAC+F,EAAAA,CAAKA,CAACM,IAAI,WAAErG,EAAE,4DACf,WAAC+F,EAAAA,CAAKA,CAACO,MAAM,YACT,UAACC,EAAAA,CAAMA,CAAAA,CAACC,QAAQ,YAAY1B,QAASe,WAChC7F,EAAE,iCAEP,UAACuG,EAAAA,CAAMA,CAAAA,CAACC,QAAQ,UAAU1B,QAASU,WAC9BxF,EAAE,oCAIf,UAACyG,EAAAA,OAAiBA,CAAAA,CAACC,eA7BJrC,CA6BoBsC,GA5BvCjD,EAAaW,EACjB,EA2B0DiB,MAAO7B,IACzD,UAAC3D,EAAAA,CAAQA,CAAAA,CACLM,QAASA,EACTC,KAAMyC,EACNxC,UAAWA,EACXU,WAAW,EACXN,oBAAqBA,EACrBC,iBA3Ea,CA2EKA,GA1E1BgD,EAAaG,KAAK,CAAGX,EACrBQ,EAAaI,IAAI,CAAGA,EACpBN,IAAcE,EAAaK,KAAK,CAAG,CAArBL,QAAgCF,EAAU6B,KAAK,CAAC,EAC9DN,GACJ,MA0EJ,yJCxGA,MArC0B,OAAC,gBAAE0B,CAAc,CAAEpB,GAqC9BmB,IArCmC,CAAO,GACjD,GAAEzG,CAAC,KAoCqByG,CApCpBG,CAoCqB,CApCf,CAAG3G,CAAAA,EAAAA,EAAAA,EAAAA,CAAcA,CAAC,UAC5B4G,EAAgC,OAAlBD,EAAKE,QAAQ,CAAW,CAACC,SAAU,KAAK,EAAI,CAAClD,MAAO,KAAK,EACvEmD,EAAcJ,EAAKE,QAAQ,CAC3B,CAACG,EAAWC,EAAa,CAAGlE,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAQ,EAAE,EAC9CmE,EAAgB,CACpBvD,KAAMiD,EACN/C,MAAO,IACPsD,aAAaJ,CACf,EAWA,MATAlB,CAAAA,EAAAA,EAAAA,SAAAA,CAASA,CAAC,KAORuB,CANuB,UACrB,IAAMpC,EAAW,MAAMC,EAAAA,CAAUA,CAACC,GAAG,CAAC,WAAYgC,GAC9ClC,GAAYA,EAAS5E,IAAI,EAAI4E,EAAS5E,IAAI,CAACkF,MAAM,CAAG,GAAG,EAC5CN,EAAS5E,IAAI,EAE9B,GAEF,EAAG,EAAE,EAEH,UAACiH,EAAAA,CAASA,CAAAA,CAACC,KAAK,aACd,UAACC,EAAAA,CAAGA,CAAAA,UACF,UAACC,EAAAA,CAAGA,CAAAA,CAACC,GAAI,EAAGhF,UAAU,gBACpB,UAACiF,EAAAA,EAAMA,CAAAA,CACLrC,MAAO,CAACA,EAAM,CACdsC,YAAa5H,EAAE,sCACf6H,aAAa,EACbC,SAAUpB,EACVqB,QAASd,EAAU1B,MAAM,CAAG,EAAI0B,EAAUe,GAAG,CAAC,CAACC,EAAMC,IAAQ,EAAE5C,MAAO2C,EAAKzD,GAAG,CAAE2D,MAAOF,EAAKpE,KAAK,CAAC,GAAM,EAAE,QAMtH", "sources": ["webpack://_N_E/./components/common/RKITable.tsx", "webpack://_N_E/?ba17", "webpack://_N_E/./pages/adminsettings/region/regionTable.tsx", "webpack://_N_E/./pages/adminsettings/region/regionTableFilter.tsx"], "sourcesContent": ["//Import Library\r\nimport React from 'react'\r\nimport DataTable  from 'react-data-table-component';\r\n\r\n//Import services/components\r\nimport { useTranslation } from 'next-i18next';\r\nimport CustomLoader from './CustomLoader';\r\n\r\ninterface RKITableProps {\r\n  columns: any[];\r\n  data: any[];\r\n  totalRows?: number;\r\n  resetPaginationToggle?: boolean;\r\n  subheader?: boolean;\r\n  subHeaderComponent?: React.ReactNode;\r\n  handlePerRowsChange?: (newPerPage: number, page: number) => void;\r\n  handlePageChange?: (page: number) => void;\r\n  rowsPerPage?: number[];\r\n  defaultRowsPerPage?: number;\r\n  selectableRows?: boolean;\r\n  loading?: boolean;\r\n  pagServer?: boolean;\r\n  onSelectedRowsChange?: (selectedRows: any) => void;\r\n  clearSelectedRows?: boolean;\r\n  sortServer?: boolean;\r\n  onSort?: (column: any, sortDirection: string) => void;\r\n  persistTableHead?: boolean;\r\n  sortFunction?: (rows: any[], field: any, direction: 'asc' | 'desc') => any[];\r\n  noHeader?: boolean;\r\n  dense?: boolean;\r\n  pagination?: boolean;\r\n  paginationServer?: boolean;\r\n  paginationTotalRows?: number;\r\n  subHeaderAlign?: string;\r\n  [key: string]: any;\r\n}\r\n\r\nfunction RKITable(props: RKITableProps) {\r\n  const { t } = useTranslation('common');\r\n  const paginationComponentOptions = {\r\n    rowsPerPageText: t('Rowsperpage'),\r\n    };\r\n  const {\r\n    columns,\r\n    data,\r\n    totalRows,\r\n    resetPaginationToggle,\r\n    subheader,\r\n    subHeaderComponent,\r\n    handlePerRowsChange,\r\n    handlePageChange,\r\n    rowsPerPage,\r\n    defaultRowsPerPage,\r\n    selectableRows,\r\n    loading,\r\n    pagServer,\r\n    onSelectedRowsChange,\r\n    clearSelectedRows,\r\n    sortServer,\r\n    onSort,\r\n    persistTableHead,\r\n    sortFunction,\r\n    // Filter out any unknown props that might cause DOM warnings\r\n    ...otherProps\r\n  } = props;\r\n\r\n  // Only pass known DataTable props to avoid DOM warnings\r\n  const dataTableProps = {\r\n    paginationComponentOptions,\r\n    noDataComponent: t(\"NoData\"),\r\n    noHeader: true,\r\n    columns,\r\n    data: data || [],\r\n    dense: true,\r\n    paginationResetDefaultPage: resetPaginationToggle,\r\n    subHeader: subheader,\r\n    progressPending: loading,\r\n    subHeaderComponent,\r\n    pagination: true,\r\n    paginationServer: pagServer,\r\n    paginationPerPage: defaultRowsPerPage ? defaultRowsPerPage : 10,\r\n    paginationRowsPerPageOptions: rowsPerPage ? rowsPerPage : [10, 15, 20, 25, 30],\r\n    paginationTotalRows: totalRows,\r\n    onChangeRowsPerPage: handlePerRowsChange,\r\n    onChangePage: handlePageChange,\r\n    selectableRows,\r\n    onSelectedRowsChange,\r\n    clearSelectedRows,\r\n    progressComponent: <CustomLoader/>,\r\n    sortIcon: <i className=\"sort-icon fas fa-exchange-alt\" />,\r\n    sortServer,\r\n    onSort,\r\n    sortFunction,\r\n    persistTableHead,\r\n    className: \"rki-table\"\r\n  };\r\n  return (\r\n    <DataTable {...dataTableProps} />\r\n  )\r\n};\r\n\r\nRKITable.defaultProps = {\r\n  subHeader: false,\r\n  pagination: true,\r\n  totalRows: null,\r\n  pagServer: true,\r\n  onSelectedRowsChange: null,\r\n  clearSelectedRows: false,\r\n  sortServer: false,\r\n  persistTableHead: false\r\n}\r\n\r\nexport default RKITable;\r\n\r\n", "\n    (window.__NEXT_P = window.__NEXT_P || []).push([\n      \"/adminsettings/region/regionTable\",\n      function () {\n        return require(\"private-next-pages/adminsettings/region/regionTable.tsx\");\n      }\n    ]);\n    if(module.hot) {\n      module.hot.dispose(function () {\n        window.__NEXT_P.push([\"/adminsettings/region/regionTable\"])\n      });\n    }\n  ", "//Import Library\r\nimport { useState, useEffect } from \"react\";\r\n\r\nimport toast from 'react-hot-toast';\r\nimport Link from \"next/link\";\r\nimport { Mo<PERSON>, Button } from \"react-bootstrap\";\r\n\r\n//Import services/components\r\nimport apiService from \"../../../services/apiService\";\r\nimport RKITable from \"../../../components/common/RKITable\";\r\nimport RegionTableFilter from \"./regionTableFilter\";\r\nimport { useTranslation } from 'next-i18next';\r\n\r\nconst RegionTable = (_props: any) => {\r\n    const { t } = useTranslation('common');\r\n    const [tabledata, setDataToTable] = useState([]);\r\n    const [, setLoading] = useState(false);\r\n    const [totalRows, setTotalRows] = useState(0);\r\n    const [perPage, setPerPage] = useState(10);\r\n    const [isModalShow, setModal] = useState(false);\r\n    const [selectRegionDetails, setSelectRegion] = useState({});\r\n    const [countryId, setCountryId]: any = useState(\"\");\r\n\r\n    const regionParams = {\r\n        sort: { title: \"asc\" },\r\n        limit: perPage,\r\n        page: 1,\r\n        query: {},\r\n    };\r\n\r\n    const columns = [\r\n        {\r\n            name: t(\"adminsetting.Regions.Region\"),\r\n            selector: (row: any) => row.title,\r\n            sortable: true,\r\n        },\r\n        {\r\n            name: t(\"adminsetting.Regions.Country\"),\r\n            selector: (row: any) => row.country?.title || '',\r\n            sortable: true,\r\n            cell: (d: any) => (d.country && d.country.title ? d.country.title : \"\"),\r\n        },\r\n        {\r\n            name: t(\"adminsetting.Regions.Action\"),\r\n            selector: (row: any) => row._id,\r\n            sortable: false,\r\n            cell: (d: any) => (\r\n                <div>\r\n                    <Link href=\"/adminsettings/[...routes]\" as={`/adminsettings/edit_region/${d._id}`}>\r\n\r\n                        <i className=\"icon fas fa-edit\" />\r\n\r\n                    </Link>\r\n                    &nbsp;\r\n                    <a onClick={() => userAction(d)}>\r\n                        <i className=\"icon fas fa-trash-alt\" />\r\n                    </a>{\" \"}\r\n                </div>\r\n            ),\r\n        },\r\n    ];\r\n\r\n    const getRegionData = async () => {\r\n        setLoading(true);\r\n        const response = await apiService.get(\"/region\", regionParams);\r\n        if (response && response.data) {\r\n            setDataToTable(response.data);\r\n            setTotalRows(response.totalCount);\r\n            setLoading(false);\r\n        }\r\n    };\r\n\r\n    const handlePageChange = (page: any) => {\r\n        regionParams.limit = perPage;\r\n        regionParams.page = page;\r\n        countryId && (regionParams.query = { country: countryId.value });\r\n        getRegionData();\r\n    };\r\n\r\n    const handlePerRowsChange = async (newPerPage: any, page: any) => {\r\n        regionParams.limit = newPerPage;\r\n        regionParams.page = page;\r\n        countryId && (regionParams.query = { country: countryId.value });\r\n        setLoading(true);\r\n        const response = await apiService.get(\"/region\", regionParams);\r\n        if (response && response.data && response.data.length > 0) {\r\n            setDataToTable(response.data);\r\n            setPerPage(newPerPage);\r\n            setLoading(false);\r\n        }\r\n    };\r\n\r\n    const userAction = async (row: any) => {\r\n        setSelectRegion(row._id);\r\n        setModal(true);\r\n    };\r\n\r\n    const modalConfirm = async () => {\r\n        try {\r\n            await apiService.remove(`/region/${selectRegionDetails}`);\r\n            getRegionData();\r\n            setModal(false);\r\n            toast.success(t(\"adminsetting.Regions.Table.regionDeletedSuccessfully\"));\r\n        } catch (error) {\r\n            toast.error(t(\"adminsetting.Regions.Table.errorDeletingRegion\"));\r\n        }\r\n    };\r\n\r\n    const modalHide = () => setModal(false);\r\n\r\n    //Handle Country Search\r\n    const handleCountry = (country: any) => {\r\n        setCountryId(country);\r\n    };\r\n\r\n    useEffect(() => {\r\n        getRegionData();\r\n    }, []);\r\n\r\n    useEffect(() => {\r\n        countryId && (regionParams.query = { country: countryId.value });\r\n        getRegionData();\r\n    }, [countryId]);\r\n\r\n    return (\r\n        <div className=\"region__table\">\r\n            <Modal show={isModalShow} onHide={modalHide}>\r\n                <Modal.Header closeButton>\r\n                    <Modal.Title>{t(\"adminsetting.Regions.DeleteRegion\")}</Modal.Title>\r\n                </Modal.Header>\r\n                <Modal.Body>{t(\"adminsetting.Regions.Areyousurewanttodeletethisregion?\")}</Modal.Body>\r\n                <Modal.Footer>\r\n                    <Button variant=\"secondary\" onClick={modalHide}>\r\n                        {t(\"adminsetting.Regions.Cancel\")}\r\n                    </Button>\r\n                    <Button variant=\"primary\" onClick={modalConfirm}>\r\n                        {t(\"adminsetting.Regions.Yes\")}\r\n                    </Button>\r\n                </Modal.Footer>\r\n            </Modal>\r\n            <RegionTableFilter countryHandler={handleCountry} value={countryId} />\r\n            <RKITable\r\n                columns={columns}\r\n                data={tabledata}\r\n                totalRows={totalRows}\r\n                pagServer={true}\r\n                handlePerRowsChange={handlePerRowsChange}\r\n                handlePageChange={handlePageChange}\r\n            />\r\n        </div>\r\n    );\r\n};\r\n\r\nexport default RegionTable;\r\n", "//Import Library\r\nimport { Col, Container, Row } from \"react-bootstrap\";\r\nimport Select from 'react-select';\r\nimport React, { useEffect, useState } from \"react\";\r\n\r\n//Import services/components\r\nimport apiService from \"../../../services/apiService\";\r\nimport { useTranslation } from 'next-i18next';\r\n\r\n\r\nconst RegionTableFilter = ({ countryHandler, value }: any) => {\r\n  const { t,i18n } = useTranslation('common');\r\n  const titleSearch = i18n.language === 'de'? {title_de: \"asc\"} : {title: \"asc\"};\r\n  const currentLang = i18n.language;\r\n  const [countries, setCountreis] = useState<any[]>([]);\r\n  const countryParams = {\r\n    sort: titleSearch,\r\n    limit: \"~\",\r\n    languageCode:currentLang\r\n  };\r\n\r\n  useEffect(() => {\r\n    const fetchCountries = async () => {\r\n      const response = await apiService.get(\"/country\", countryParams);\r\n      if (response && response.data && response.data.length > 0) {\r\n        setCountreis(response.data);\r\n      }\r\n    }\r\n    fetchCountries();\r\n  }, [])\r\n  return (\r\n    <Container fluid>\r\n      <Row>\r\n        <Col md={4} className=\"ps-1\">\r\n          <Select\r\n            value={[value]}\r\n            placeholder={t(\"adminsetting.Regions.SelectCountry\")}\r\n            isClearable={true}\r\n            onChange={countryHandler}\r\n            options={countries.length > 0 ? countries.map((item, _i) => ({ value: item._id, label: item.title })) : []}\r\n          />\r\n        </Col>\r\n      </Row>\r\n    </Container>\r\n  )\r\n};\r\n\r\nexport default RegionTableFilter;"], "names": ["RKITable", "props", "t", "useTranslation", "paginationComponentOptions", "rowsPerPageText", "columns", "data", "totalRows", "resetPaginationToggle", "subheader", "subHeaderComponent", "handlePerRowsChange", "handlePageChange", "rowsPerPage", "defaultRowsPerPage", "selectableRows", "loading", "pagServer", "onSelectedRowsChange", "clearSelectedRows", "sortServer", "onSort", "persistTableHead", "sortFunction", "otherProps", "dataTableProps", "noDataComponent", "<PERSON><PERSON><PERSON><PERSON>", "dense", "paginationResetDefaultPage", "subHeader", "progressPending", "pagination", "paginationServer", "paginationPerPage", "paginationRowsPerPageOptions", "paginationTotalRows", "onChangeRowsPerPage", "onChangePage", "progressComponent", "CustomLoader", "sortIcon", "i", "className", "DataTable", "defaultProps", "RegionTable", "tabledata", "setDataToTable", "useState", "setLoading", "setTotalRows", "perPage", "setPerPage", "isModalShow", "setModal", "selectRegionDetails", "setSelectRegion", "countryId", "setCountryId", "regionParams", "sort", "title", "limit", "page", "query", "name", "selector", "row", "sortable", "country", "cell", "d", "_id", "div", "Link", "href", "as", "a", "onClick", "userAction", "getRegionData", "response", "apiService", "get", "totalCount", "newPerPage", "value", "length", "modalConfirm", "remove", "toast", "success", "error", "modalHide", "useEffect", "Modal", "show", "onHide", "Header", "closeButton", "Title", "Body", "Footer", "<PERSON><PERSON>", "variant", "RegionTableFilter", "<PERSON><PERSON><PERSON><PERSON>", "handleCountry", "i18n", "titleSearch", "language", "title_de", "currentLang", "countries", "setCountreis", "countryParams", "languageCode", "fetchCountries", "Container", "fluid", "Row", "Col", "md", "Select", "placeholder", "isClearable", "onChange", "options", "map", "item", "_i", "label"], "sourceRoot": "", "ignoreList": []}