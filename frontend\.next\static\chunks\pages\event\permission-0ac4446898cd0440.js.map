{"version": 3, "file": "static/chunks/pages/event/permission-0ac4446898cd0440.js", "mappings": "2RAOO,IAAMA,EAAcC,CAAAA,EAAAA,EAAAA,CAAAA,CAAoBA,CAAC,CAC9CC,sBAAuB,KACjBC,EAAMC,WAAW,IAAID,EAAMC,WAAW,CAACC,KAAK,IAAIF,EAAMC,WAAW,CAACC,KAAK,CAAC,aAAa,CAK3FC,CAL6F,kBAKzE,aACtB,GAAG,EAE4BL,CAAAA,EAAAA,EAAAA,CAAAA,CAAoBA,CAAC,CAClDC,sBAAuB,KACjBC,EAAMC,WAAW,IAAID,EAAMC,WAAW,CAACC,KAAK,IAAIF,EAAMC,WAAW,CAACC,KAAK,CAAC,aAAa,CAK3FC,CAL6F,kBAKzE,kBACpBC,iBAAkB,IAAM,UAACC,EAAAA,OAAIA,CAAAA,CAAAA,EAC/B,GAAG,EAEyBP,CAAAA,EAAAA,EAAAA,CAAAA,CAAoBA,CAAC,CAC/CC,sBAAuB,CAACC,EAAOM,KAC7B,GAAIN,EAAMC,WAAW,EAAID,EAAMC,WAAW,CAACC,KAAK,EAAE,GAC5CF,EAAMC,WAAW,CAACC,KAAK,CAAC,aAAa,CACvC,CADyC,KAClC,QAEP,GAAIF,EAAMC,WAAW,CAACC,KAAK,CAAC,aAAa,EAAE,EAC/BA,KAAK,EAAII,EAAMJ,KAAK,CAACK,IAAI,EAAID,EAAMJ,KAAK,CAACK,IAAI,CAACC,GAAG,GAAKR,EAAMO,IAAI,CAACC,GAAG,CAC5E,CAD8E,MACvE,CAGb,CAEF,OAAO,CACT,EACAL,mBAAoB,cACtB,GAAG,EAE6BL,CAAAA,EAAAA,EAAAA,CAAAA,CAAoBA,CAAC,CACnDC,sBAAuB,CAACC,EAAOM,KAC7B,GAAIN,EAAMC,WAAW,EAAID,EAAMC,WAAW,CAACC,KAAK,EAAE,GAC5CF,EAAMC,WAAW,CAACC,KAAK,CAAC,aAAa,CACvC,CADyC,MAClC,OAEP,GAAIF,EAAMC,WAAW,CAACC,KAAK,CAAC,aAAa,EAAE,EAC/BA,KAAK,EAAII,EAAMJ,KAAK,CAACK,IAAI,EAAID,EAAMJ,KAAK,CAACK,IAAI,CAACC,GAAG,GAAKR,EAAMO,IAAI,CAACC,GAAG,CAC5E,CAD8E,MACvE,CAGb,CAEF,OAAO,CACT,EACAL,mBAAoB,mBACpBC,iBAAkB,IAAM,UAACC,EAAAA,OAAIA,CAAAA,CAAAA,EAC/B,GAEaI,EAA0BX,CAAAA,EAAAA,EAAAA,CAAAA,CAAoBA,CAAC,CAC1DC,sBAAuB,KACjBC,EAAMC,WAAW,IAAID,EAAMC,WAAW,CAACS,MAAM,IAAIV,EAAMC,WAAW,CAACS,MAAM,CAAC,WAAW,CAK3FP,CAL6F,kBAKzE,yBACtB,GAAG,EAEYN,WAAWA,EAAC,MC1E3B,4CACA,oBACA,WACA,OAAe,EAAQ,IAAyC,CAChE,EACA,UAFsB", "sources": ["webpack://_N_E/./pages/event/permission.tsx", "webpack://_N_E/?aa63"], "sourcesContent": ["//Import Library\r\nimport React from 'react';\r\nimport connectedAuthWrapper from 'redux-auth-wrapper/connectedAuthWrapper';\r\n\r\n//Import services/components\r\nimport R403 from \"../r403\";\r\n\r\nexport const canAddEvent = connectedAuthWrapper({\r\n  authenticatedSelector: (state) => {\r\n    if (state.permissions && state.permissions.event && state.permissions.event['create:any']) {\r\n      return true;\r\n    }\r\n    return false;\r\n  },\r\n  wrapperDisplayName: 'CanAddEvent',\r\n});\r\n\r\nexport const canAddEventForm = connectedAuthWrapper({\r\n  authenticatedSelector: (state) => {\r\n    if (state.permissions && state.permissions.event && state.permissions.event['create:any']) {\r\n      return true;\r\n    }\r\n    return false;\r\n  },\r\n  wrapperDisplayName: 'CanAddEventForm',\r\n  FailureComponent: () => <R403/>\r\n});\r\n\r\nexport const canEditEvent = connectedAuthWrapper({\r\n  authenticatedSelector: (state, props) => {\r\n    if (state.permissions && state.permissions.event) {\r\n      if (state.permissions.event['update:any']) {\r\n        return true;\r\n      } else { //if update:own\r\n        if (state.permissions.event['update:own']) {\r\n          if (props.event && props.event.user && props.event.user._id === state.user._id) {\r\n            return true;\r\n          }  \r\n        }\r\n      }\r\n    }\r\n    return false;\r\n  },\r\n  wrapperDisplayName: 'CanEditEvent',\r\n});\r\n\r\nexport const canEditEventForm = connectedAuthWrapper({\r\n  authenticatedSelector: (state, props) => {\r\n    if (state.permissions && state.permissions.event) {\r\n      if (state.permissions.event['update:any']) {\r\n        return true;\r\n      } else { //if update:own\r\n        if (state.permissions.event['update:own']) {\r\n          if (props.event && props.event.user && props.event.user._id === state.user._id) {\r\n            return true;\r\n          }\r\n        }\r\n      }\r\n    }\r\n    return false;\r\n  },\r\n  wrapperDisplayName: 'CanEditEventForm',\r\n  FailureComponent: () => <R403/>\r\n});\r\n\r\nexport const canViewDiscussionUpdate = connectedAuthWrapper({\r\n  authenticatedSelector: (state) => {\r\n    if (state.permissions && state.permissions.update && state.permissions.update['read:any']) {\r\n      return true;\r\n    }\r\n    return false;\r\n  },\r\n  wrapperDisplayName: 'CanViewDiscussionUpdate',\r\n});\r\n\r\nexport default canAddEvent;", "\n    (window.__NEXT_P = window.__NEXT_P || []).push([\n      \"/event/permission\",\n      function () {\n        return require(\"private-next-pages/event/permission.tsx\");\n      }\n    ]);\n    if(module.hot) {\n      module.hot.dispose(function () {\n        window.__NEXT_P.push([\"/event/permission\"])\n      });\n    }\n  "], "names": ["canAddEvent", "connectedAuthWrapper", "authenticatedSelector", "state", "permissions", "event", "wrapperDisplayName", "FailureComponent", "R403", "props", "user", "_id", "canViewDiscussionUpdate", "update"], "sourceRoot": "", "ignoreList": []}