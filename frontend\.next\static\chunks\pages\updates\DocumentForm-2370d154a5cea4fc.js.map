{"version": 3, "file": "static/chunks/pages/updates/DocumentForm-2370d154a5cea4fc.js", "mappings": "gFACA,4CACA,wBACA,WACA,OAAe,EAAQ,KAA6C,CACpE,EACA,SAFsB", "sources": ["webpack://_N_E/?748d"], "sourcesContent": ["\n    (window.__NEXT_P = window.__NEXT_P || []).push([\n      \"/updates/DocumentForm\",\n      function () {\n        return require(\"private-next-pages/updates/DocumentForm.tsx\");\n      }\n    ]);\n    if(module.hot) {\n      module.hot.dispose(function () {\n        window.__NEXT_P.push([\"/updates/DocumentForm\"])\n      });\n    }\n  "], "names": [], "sourceRoot": "", "ignoreList": []}