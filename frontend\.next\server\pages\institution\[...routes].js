"use strict";(()=>{var e={};e.id=8237,e.ids=[636,3220,8237],e.modules={123:e=>{e.exports=require("dom-helpers/removeEventListener")},1332:e=>{e.exports=require("react-custom-scrollbars-2")},1428:e=>{e.exports=import("axios")},1680:e=>{e.exports=require("@restart/ui/utils")},1919:e=>{e.exports=require("react-transition-group/Transition")},3892:e=>{e.exports=require("classnames")},4048:e=>{e.exports=require("react-truncate")},6009:e=>{e.exports=require("dom-helpers/ownerDocument")},6695:(e,r,t)=>{t.a(e,async(e,s)=>{try{t.r(r),t.d(r,{config:()=>g,default:()=>c,getServerSideProps:()=>m,getStaticPaths:()=>q,getStaticProps:()=>d,reportWebVitals:()=>h,routeModule:()=>P,unstable_getServerProps:()=>b,unstable_getServerSideProps:()=>w,unstable_getStaticParams:()=>S,unstable_getStaticPaths:()=>v,unstable_getStaticProps:()=>f});var o=t(63885),i=t(80237),a=t(81413),u=t(9616),n=t.n(u),p=t(72386),x=t(8137),l=e([p,x]);[p,x]=l.then?(await l)():l;let c=(0,a.M)(x,"default"),d=(0,a.M)(x,"getStaticProps"),q=(0,a.M)(x,"getStaticPaths"),m=(0,a.M)(x,"getServerSideProps"),g=(0,a.M)(x,"config"),h=(0,a.M)(x,"reportWebVitals"),f=(0,a.M)(x,"unstable_getStaticProps"),v=(0,a.M)(x,"unstable_getStaticPaths"),S=(0,a.M)(x,"unstable_getStaticParams"),b=(0,a.M)(x,"unstable_getServerProps"),w=(0,a.M)(x,"unstable_getServerSideProps"),P=new o.PagesRouteModule({definition:{kind:i.A.PAGES,page:"/institution/[...routes]",pathname:"/institution/[...routes]",bundlePath:"",filename:""},components:{App:p.default,Document:n()},userland:x});s()}catch(e){s(e)}})},6952:e=>{e.exports=require("@restart/ui/Modal")},7374:e=>{e.exports=require("@restart/ui/Dropdown")},8137:(e,r,t)=>{t.a(e,async(e,s)=>{try{t.r(r),t.d(r,{default:()=>m,getServerSideProps:()=>q});var o=t(8732),i=t(82015),a=t(64395),u=t(15272),n=t(10836),p=t(63487),x=t(28966),l=t(35557),c=t(35576),d=e([a,u,n,p]);async function q({locale:e}){return{props:{...await (0,c.serverSideTranslations)(e,["common"])}}}[a,u,n,p]=d.then?(await d)():d;let m=({router:e})=>{let r=e.query.routes||[],[t,s]=(0,i.useState)(""),[c,d]=(0,i.useState)(!1),[q,m]=(0,i.useState)(""),g=async()=>{let e=await p.A.get(`/institution/${r[1]}`);s(e),h(e)};(0,i.useEffect)(()=>{g()},[]);let h=async e=>{let r=await p.A.post("/users/getLoggedUser",{});r&&r.roles&&r.roles.length&&(m(r),d(!1),r&&r.roles&&(r.roles.includes("SUPER_ADMIN")||r.roles.includes("PLATFORM_ADMIN")&&e.user==r._id?d(!0):r.roles.includes("GENERAL_USER")&&e.user==r._id&&d(!0)))},f=(0,x.canAddInstitutionForm)(()=>(0,o.jsx)(a.default,{institution:"",routes:r})),v=(0,x.canEditInstitutionForm)(()=>(0,o.jsx)(a.default,{institution:t,routes:r})),S=(0,x.canManageFocalPoints)(()=>(0,o.jsx)(n.default,{institution:t,routes:r}));switch(r[0]){case"create":return(0,o.jsx)(f,{});case"edit":if(c)return(0,o.jsx)(v,{institution:t,routes:r});if(""!=q)return(0,o.jsx)(l.default,{});case"show":return(0,o.jsx)(u.default,{routes:r});case"focalpoint":if(null!==t)return(0,o.jsx)(S,{institution:t});break;default:return null}};s()}catch(e){s(e)}})},8732:e=>{e.exports=require("react/jsx-runtime")},9532:e=>{e.exports=require("@restart/ui/Nav")},9653:e=>{e.exports=require("@restart/ui/Overlay")},11e3:e=>{e.exports=require("react-multi-select-component")},11242:e=>{e.exports=require("redux-persist/integration/react")},11688:e=>{e.exports=require("@restart/hooks/useWillUnmount")},12103:e=>{e.exports=require("react-avatar-editor")},12388:e=>{e.exports=require("dom-helpers/addEventListener")},13364:e=>{e.exports=require("redux-saga/effects")},14062:e=>{e.exports=import("react-redux")},14078:e=>{e.exports=import("swr")},14332:e=>{e.exports=require("uncontrollable")},16029:e=>{e.exports=require("react-dropzone")},16116:e=>{e.exports=require("invariant")},18622:e=>{e.exports=require("yup")},19121:e=>{e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},19442:e=>{e.exports=require("react-bootstrap-range-slider")},21964:e=>{e.exports=require("@restart/ui/TabPanel")},22313:e=>{e.exports=require("react-confirm-alert")},22326:e=>{e.exports=require("react-dom")},22541:e=>{e.exports=require("redux-persist/lib/storage")},25303:e=>{e.exports=require("@restart/ui/NavItem")},26324:e=>{e.exports=require("warning")},27825:e=>{e.exports=require("lodash")},27910:e=>{e.exports=require("stream")},28217:e=>{e.exports=require("@restart/ui/DropdownItem")},29021:e=>{e.exports=require("fs")},29294:e=>{e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},29628:e=>{e.exports=require("dom-helpers/addClass")},29825:e=>{e.exports=require("prop-types")},29841:e=>{e.exports=require("dom-helpers/hasClass")},30362:e=>{e.exports=require("@restart/ui/DropdownToggle")},33873:e=>{e.exports=require("path")},35557:(e,r,t)=>{t.r(r),t.d(r,{default:()=>o});var s=t(8732);function o(e){return(0,s.jsx)("div",{className:"container-fluid p-0 response-message-block",children:(0,s.jsx)("div",{className:"message",children:"you don't have permission to access"})})}},35576:e=>{e.exports=require("next-i18next/serverSideTranslations")},36653:e=>{e.exports=require("nprogress")},36955:e=>{e.exports=require("@restart/hooks/useBreakpoint")},37766:e=>{e.exports=require("@restart/hooks/useMergedRefs")},38609:e=>{e.exports=require("react-data-table-component")},39756:e=>{e.exports=import("redux")},40051:e=>{e.exports=require("dom-helpers/removeClass")},40361:e=>{e.exports=require("next/dist/compiled/next-server/pages.runtime.prod.js")},42893:e=>{e.exports=import("react-hot-toast")},43294:e=>{e.exports=require("formik")},50009:e=>{e.exports=require("@restart/ui/DropdownMenu")},54131:e=>{e.exports=import("@fortawesome/free-solid-svg-icons")},57664:e=>{e.exports=require("dom-helpers/scrollbarSize")},58928:e=>{e.exports=require("dom-helpers/canUseDOM")},59717:e=>{e.exports=require("@restart/ui/DropdownContext")},60560:e=>{e.exports=require("dom-helpers/contains")},63033:e=>{e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},65209:e=>{e.exports=require("@restart/ui/TabContext")},65447:e=>{e.exports=require("@restart/ui/Button")},67364:e=>{e.exports=require("@restart/hooks/useCallbackRef")},68455:e=>{e.exports=import("redux-saga")},69722:e=>{e.exports=require("es6-promise")},70947:e=>{e.exports=require("@restart/ui/Tabs")},74075:e=>{e.exports=require("zlib")},74716:e=>{e.exports=require("moment")},74987:e=>{e.exports=require("@restart/ui/ModalManager")},78097:e=>{e.exports=require("next-redux-saga")},78634:e=>{e.exports=require("@restart/ui/Anchor")},80860:e=>{e.exports=require("@restart/ui/NoopTransition")},81149:e=>{e.exports=require("react-responsive-carousel")},81366:e=>{e.exports=require("redux-auth-wrapper/connectedAuthWrapper")},81521:e=>{e.exports=require("dom-helpers/querySelectorAll")},81895:e=>{e.exports=require("@restart/hooks/useEventCallback")},82015:e=>{e.exports=require("react")},82053:e=>{e.exports=require("@fortawesome/react-fontawesome")},86842:e=>{e.exports=require("@restart/ui/SelectableContext")},86843:e=>{e.exports=require("moment/locale/de")},87571:e=>{e.exports=require("dom-helpers/transitionEnd")},88751:e=>{e.exports=require("next-i18next")},93787:e=>{e.exports=require("redux-persist")},94947:e=>{e.exports=require("@restart/hooks/useTimeout")},96196:e=>{e.exports=require("next-redux-wrapper")},98320:e=>{e.exports=require("@restart/hooks/useIsomorphicEffect")},99460:e=>{e.exports=require("dom-helpers/css")}};var r=require("../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[6089,9216,9616,6761,2386,2491,7136,4535,4395,5272,836],()=>t(6695));module.exports=s})();