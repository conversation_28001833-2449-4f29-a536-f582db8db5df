{"version": 3, "file": "static/chunks/pages/search-0b5977bf751d30d1.js", "mappings": "iMAqCA,MA5Be,IAEP,WAACA,MAAAA,OA0BMC,IAzBH,EAyBSA,CAzBT,CAyBU,CAzBV,KAACC,KAAAA,UAAG,UAACC,IAAAA,UAAE,iBACP,UAACC,KAAAA,CAAAA,GACD,UAACJ,MAAAA,CAAIK,MAAO,CAAEC,gBAAiB,UAAWC,MAAO,OAAQC,OAAQ,OAAQ,WACrE,UAACC,EAAAA,CAAIA,CAAAA,CAACC,UAAU,gBACZ,WAACD,EAAAA,CAAIA,CAACE,KAAK,EAACC,UAAU,sCAClB,UAACH,EAAAA,CAAIA,CAACI,KAAK,EAACH,UAAU,gBAAO,wBAC7B,WAACV,MAAAA,CAAIU,UAAU,mBACX,UAACD,EAAAA,CAAIA,CAACK,OAAO,EAACC,KAAK,QAAQC,YAAY,kBACvC,UAACC,EAAAA,CAAMA,CAAAA,CAACP,UAAU,OAAOQ,QAAQ,mBAAU,qBAK3D,WAAClB,MAAAA,CAAIU,UAAU,cAAcL,MAAO,CAAEc,UAAW,OAAQ,YACrD,UAACC,EAAAA,CAAeA,CAAAA,CAACV,UAAU,OAAOW,KAAMC,EAAAA,GAAqBA,CAAEC,MAAM,UAAUC,KAAK,OACpF,UAACC,KAAAA,UAAG,uBACJ,UAACC,IAAAA,CAAEhB,UAAU,gBAAO,kDC3BpC,4CACA,UACA,WACA,OAAe,EAAQ,IAAqC,CAC5D,EACA,UAFsB", "sources": ["webpack://_N_E/./pages/search/index.tsx", "webpack://_N_E/?1a82"], "sourcesContent": ["//Import Library\r\nimport React from \"react\";\r\nimport { Form, Button, Row, Col } from 'react-bootstrap';\r\nimport { FontAwesomeIcon } from \"@fortawesome/react-fontawesome\";\r\nimport {\r\n    faExclamationTriangle\r\n} from \"@fortawesome/free-solid-svg-icons\";\r\n\r\n\r\nconst Search = () => {\r\n    return (\r\n        <div>\r\n            <h3><b>RKI Search</b></h3>\r\n            <hr />\r\n            <div style={{ backgroundColor: \"#f5f5f5\", width: \"100%\", height: \"100px\" }}>\r\n                <Form className=\"mx-3\" >\r\n                    <Form.Group controlId=\"exampleForm.ControlInput1\">\r\n                        <Form.Label className=\"pt-2\">Enter Your Keywords</Form.Label>\r\n                        <div className=\"d-flex\">\r\n                            <Form.Control type=\"email\" placeholder=\"Ex:- Covid-19\" />\r\n                            <Button className=\"ms-3\" variant=\"primary\">Search</Button>\r\n                        </div>\r\n                    </Form.Group>\r\n                </Form>\r\n            </div>\r\n            <div className=\"text-center\" style={{ marginTop: \"150px\" }}>\r\n                <FontAwesomeIcon className=\"mb-1\" icon={faExclamationTriangle} color=\"#232c3d\" size=\"6x\" />\r\n                <h1>Under Construction</h1>\r\n                <p className=\"lead\">Please Visit us Later!</p>\r\n            </div>\r\n        </div>\r\n\r\n    )\r\n}\r\n\r\n\r\n\r\nexport default Search;\r\n\r\n", "\n    (window.__NEXT_P = window.__NEXT_P || []).push([\n      \"/search\",\n      function () {\n        return require(\"private-next-pages/search/index.tsx\");\n      }\n    ]);\n    if(module.hot) {\n      module.hot.dispose(function () {\n        window.__NEXT_P.push([\"/search\"])\n      });\n    }\n  "], "names": ["div", "Search", "h3", "b", "hr", "style", "backgroundColor", "width", "height", "Form", "className", "Group", "controlId", "Label", "Control", "type", "placeholder", "<PERSON><PERSON>", "variant", "marginTop", "FontAwesomeIcon", "icon", "faExclamationTriangle", "color", "size", "h1", "p"], "sourceRoot": "", "ignoreList": []}