{"version": 3, "file": "static/chunks/pages/project/ListMapContainer-47bcaecaa44aa2e1.js", "mappings": "qJAoEA,MA/CkD,OAAC,MACjDA,EAAO,QAAQ,IACfC,CA6CaC,CA7CR,EAAE,SA6CkBA,EA5CzBC,EAAY,EAAE,MACdC,CAAI,MACJC,CAAI,UACJC,CAAQ,SACRC,CAAO,OACPC,CAAK,WACLC,GAAY,CAAK,CAClB,UAsBC,GAAiB,iBAAOH,EAASI,GAAG,EAAyC,UAAU,OAA3BJ,EAASK,GAAG,CAKtE,UAACC,EAAAA,EAAMA,CAAAA,CACLN,SAAUA,EACVD,KAAMA,EACNG,MAAOA,GAASR,EAChBS,UAAWA,EACXF,QA/BiBM,CA+BRC,GA9BPP,GAeFA,EAdoB,IADT,EAETP,KACAC,QAYmBc,IAXnBZ,OACAC,WACAE,CACF,EAGe,UACbA,EACAU,YAAa,IAAMV,CACrB,EAE6BO,EAEjC,IAIS,IAYX,mBCjEA,4CACA,4BACA,WACA,OAAe,EAAQ,KAAiD,CACxE,EACA,SAFsB,oICwItB,MAnI0BI,IACxB,GAAM,MAAEC,CAAI,CAAE,CAAGC,CAAAA,EAAAA,EAAAA,EAAAA,CAAcA,CAAC,GAkInBC,OAjIPC,EAAcH,EAAKI,KAiIIF,EAAC,CAjIG,CAC3B,UAAEG,CAAQ,iBAAEC,CAAe,CAAE,CAAGP,EAChC,CAACQ,EAAQC,EAAU,CAAGC,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAQ,EAAE,EACxC,CAACC,EAAcC,EAAgB,CAAQF,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAC,CAAC,GACjD,CAACG,EAAYC,EAAc,CAAQJ,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAC,CAAC,GAC7C,CAACK,EAAiBC,EAAmB,CAAQN,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAC,CAAC,GAoBvDO,EAAc,KAClBL,EAAgB,MAChBE,EAAc,KAChB,EAEMI,EAAgB,CAACC,EAAmBrB,EAAaF,KACrDqB,IACAL,EAAgBd,GAChBgB,EAAc,CACZ/B,KAAMoC,EAAapC,IAAI,CACvBC,GAAImC,EAAanC,EAAE,CACnBE,UAAWiC,EAAajC,SAC1B,EACF,EAEMkC,EAAO,GACXC,EAAQC,eAAe,EAAID,EAAQC,eAAe,CAACC,WAAW,CAC1DC,EAASH,GACbA,EAAQC,eAAe,EAAID,EAAQC,eAAe,CAACC,WAAW,CAC1DE,EAAa,IACjB,IAAMC,EAA0B,EAAE,CAiBlC,OAhBAC,IAAAA,OAAS,CAACC,EAAQC,oBAAoB,CAAE,IACtCC,QAAQC,GAAG,CAAC,UAAWV,GAEvBK,EAAiBM,IAAI,CAAC,CACpBzC,MAAOqC,GAAWA,EAAQrC,KAAK,CAAGqC,EAAQrC,KAAK,CAAG,GAClDP,GAAI4C,GAAWA,EAAQK,GAAG,CAAGL,EAAQK,GAAG,CAAG,GAC3CxC,IACE2B,EAAKC,IACLa,WAAWb,EAAQC,eAAe,CAACC,WAAW,CAAC,EAAE,CAACY,QAAQ,EAC5DzC,IACE8B,EAAMH,IACNa,WAAWb,EAAQC,eAAe,CAACC,WAAW,CAAC,EAAE,CAACa,SAAS,EAC7DC,aAAchB,EAAQgB,YAAY,CAClCnD,UAAWmC,EAAQC,eAAe,EAAID,EAAQC,eAAe,CAACW,GAAG,EAErE,GACOP,CAAgB,CAAC,EAAE,EAGtBY,EAAwB,KAC5B,IAAMC,EAA6B,EAAE,CACrCZ,IAAAA,OAAS,CAACrB,EAAU,IAClB,IAAMkC,EAAUf,EAAWG,GAC3BW,EAAoBP,IAAI,CAACQ,EAC3B,GAMA/B,EALuBkB,IAAAA,IAKbc,EALqB,CAACF,EAAqB,SAAUG,CAAK,EAClE,GAAInC,EAAgBoC,MAAM,CAAG,EAC3B,CAD8B,MACvBpC,EAAgBqC,QAAQ,CAACF,EAAML,YAAY,CAEtD,GAEF,EAEMQ,EAA2B,KAC/B,IAAMC,EAAuB,EAAE,CAC/BnB,IAAAA,OAAS,CAACrB,EAAU,IAEhBsB,EAAQC,oBAAoB,EAC5BD,EAAQC,oBAAoB,CAACc,MAAM,CAAG,GACtC,IACAhB,OAAS,CAACC,EAAQC,oBAAoB,CAAE,IACtCkB,EAAGxD,KAAK,CAAGqC,EAAQrC,KAAK,CACxBwD,EAAG/D,EAAE,CAAG4C,EAAQK,GAAG,CACnBa,EAAcd,IAAI,CAACe,EACrB,EAEJ,GACA/B,EAAmBW,IAAAA,OAAS,CAACmB,EAAe,uBAC9C,EAOA,MALAE,CAAAA,EAAAA,EAAAA,SAAAA,CAASA,CAAC,KACRV,IACAO,GACF,EAAG,CAACvC,EAAS,EAGX,UAAC2C,EAAAA,CAAOA,CAAAA,CACNC,QAASjC,EACTZ,SAAUD,EACVO,aAAcA,EACdE,WAAY,UAACsC,IAlGf,GAAM,MAAEC,CAAI,CAAE,CAAGC,SACjB,GAAYD,EAAKlE,SAAS,EAAI6B,CAAe,CAACqC,EAAKlE,SAAS,CAAC,CAEzD,CAF2D,EAE3D,OAACoE,KAAAA,UACEvC,CAAe,CAACqC,EAAKlE,SAAS,CAAC,CAACqE,GAAG,CAAC,CAACC,EAAWC,IAE7C,UAACC,KAAAA,UACC,UAACC,IAAAA,CAAEC,KAAM,IAAgCJ,MAAAA,CAA5BpD,EAAY,kBAAwB,OAARoD,EAAKxE,EAAE,WAAKwE,EAAKjE,KAAK,IADxDkE,MAQZ,IACT,EAmFiBN,CAAWC,KAAMvC,aAE7BL,EAAOmC,MAAM,EAAI,EACdnC,EAAO+C,GAAG,CAAC,CAACC,EAAMC,IAEd,UAACxE,EAAAA,CAAYA,CAAAA,CAEXF,KAAMyE,EAAKjE,KAAK,CAChBP,GAAIwE,EAAKxE,EAAE,CACXE,UAAWsE,EAAKtE,SAAS,CACzBE,KAAM,CACJyE,IAAK,8BACP,EACAvE,QAAS4B,EACT7B,SAAUmE,GARLC,IAYX,MAGV,wFC1HA,MARyB,OAAC,UAAEpE,CAAQ,OAQrByE,OARuBC,CAAY,CAAEC,OAQrBF,EAAC,CAR4B,CAAS,GACnE,MACE,UAACG,EAAAA,EAAUA,CAAAA,CAAC5E,SAAUA,EAAU0E,aAAcA,WAC5C,UAACG,MAAAA,UAAKF,KAGZ,ECdMG,EAAO,mBACPC,EAAQ,qBACRC,EAAO,eACPC,EAAW,kBAgTjB,EA/SkB,CAChB,CACE,YAAe,IA6SJC,OA5SX,EA4SoBA,EAAC,IA5SV,CACT,CACE,MAAS,SACX,EACD,EAEH,CACE,YAAeJ,EACf,QAAW,CACT,CACE,MAAS,SACX,EACD,EAEH,CACE,YAAeC,EACf,QAAW,CACT,CACE,MAAS,SACX,EACD,EAEH,CACE,YAAe,iBACf,YAAe,WACf,QAAW,CACT,CACE,WAAc,KAChB,EACD,EAEH,CACE,YAAe,yBACf,YAAeE,EACf,QAAW,CACT,CACE,MAAS,SACX,EAEJ,EACA,CACE,YAAe,6BACf,YAAe,SACf,QAAW,CACT,CACE,WAAc,KAChB,EACD,EAEH,CACE,YAAe,6BACf,YAAeH,EACf,QAAW,CACT,CACE,MAAS,SACX,EAEJ,EACA,CACE,YAAe,0BACf,YAAeG,EACf,QAAW,CACT,CACE,MAAS,SACX,EACD,EAEH,CACE,YAAe,qBACf,YAAeA,EACf,QAAW,CACT,CACE,MAAS,SACX,EACD,EAEH,CACE,YAAe,oBACf,YAAe,WACf,QAAW,CACT,CACE,MAAS,SACX,EACD,EAEH,CACE,YAAe,MACf,QAAW,CACT,CACE,WAAc,KAChB,EACD,EAEH,CACE,YAAe,MACf,YAAe,WACf,QAAW,CACT,CACE,MAAS,SACX,EACD,EAEH,CACE,YAAe,MACf,YAAe,cACf,QAAW,CACT,CACE,WAAc,KAChB,EACD,EAEH,CACE,YAAe,MACf,YAAeH,EACf,QAAW,CACT,CACE,MAAS,SACX,EACD,EAEH,CACE,YAAe,MACf,YAAeC,EACf,QAAW,CACT,CACE,MAAS,SACX,EACD,EAEH,CACE,YAAe,WACf,YAAe,gBACf,QAAW,CACT,CACE,MAAS,SACX,EACD,EAEH,CACE,YAAe,WACf,YAAeD,EACf,QAAW,CACT,CACE,MAAS,SACX,EACD,EAEH,CACE,YAAe,OACf,QAAW,CACT,CACE,WAAc,KAChB,EAEJ,EACA,CACE,YAAe,OACf,YAAe,WACf,QAAW,CACT,CACE,MAAS,SACX,EACD,EAEH,CACE,YAAe,OACf,YAAe,cACf,QAAW,CACT,CACE,WAAc,KAChB,EACD,EAEH,CACE,YAAe,OACf,YAAeA,EACf,QAAW,CACT,CACE,MAAS,SACX,EACD,EAEH,CACE,YAAe,OACf,YAAeC,EACf,QAAW,CACT,CACE,MAAS,SACX,EAEJ,EACA,CACE,YAAeC,EACf,YAAe,WACf,QAAW,CACT,CACE,MAAS,SACX,EACD,EAEH,CACE,YAAeA,EACf,YAAeC,EACf,QAAW,CACT,CACE,MAAS,SACX,EACD,EAEH,CACE,YAAeD,EACf,YAAeF,EACf,QAAW,CACT,CACE,MAAS,SACX,EACD,EAEH,CACE,YAAeE,EACf,YAAeD,EACf,QAAW,CACT,CACE,MAAS,SACX,EACD,EAEH,CACE,YAAe,aACf,YAAe,SACf,QAAW,CACT,CACE,WAAc,KAChB,EACD,EAEH,CACE,YAAe,UACf,QAAW,CACT,CACE,WAAc,KAChB,EACD,EAEH,CACE,YAAe,UACf,YAAeD,EACf,QAAW,CACT,CACE,MAAS,SACX,EACD,EAEH,CACE,YAAe,UACf,YAAeC,EACf,QAAW,CACT,CACE,MAAS,SACX,EACD,EAEH,CACE,YAAe,eACf,YAAe,gBACf,QAAW,CACT,CACE,MAAS,SACX,EACD,EAEH,CACE,YAAe,kBACf,YAAe,WACf,QAAW,CACT,CACE,MAAS,SACX,EACD,EAEH,CACE,YAAe,QACf,YAAe,WACf,QAAW,CACT,CACE,MAAS,SACX,EACD,EAEH,CACE,YAAe,QACf,YAAeD,EACf,QAAW,CACT,CACE,MAAS,SACX,EACD,EAEJ,2BCnND,MAzEwC,OAAC,SAyE1BK,GAxEb3D,CAAU,GAwEU2D,EAAC,SAvErB7D,CAAY,eACZ8D,CAAa,UACbT,CAAQ,CACRU,SAAS,GAAG,OACZC,EAAQ,MAAM,UACdtE,CAAQ,MACRuE,EAAO,CAAC,SACRC,EAAU,CAAC,SACX3B,CAAO,CACR,GACO,QAAE4B,CAAM,CAAE,CAAGC,CAAAA,EAAAA,EAAAA,SAAAA,CAASA,GACtB,UAAEC,CAAQ,WAAEC,CAAS,CAAE,CAAIC,CAAAA,EAAAA,EAAAA,CAAAA,CAAaA,UAmB9C,EAAsB,SAAP,CAAQhB,MAAAA,UAAI,uBACtBc,EAGH,QAHa,EAGZd,MAAAA,CAAIiB,UAAU,yBACb,UAACjB,MAAAA,CAAIiB,UAAU,WAAWC,MAAO,OAAET,SAAOD,EAAQrF,SAAU,UAAW,WACrE,WAACgG,EAAAA,EAASA,CAAAA,CACRC,kBAzBe,CACrBX,MAAOA,EACPD,OAA0B,UAAlB,OAAOA,EAAsB,GAAU,OAAPA,EAAO,MAAMA,CACvD,EAuBQa,OAhBOd,CAgBCc,EArBM,CACpB9F,IAAK,SAIyB+F,CAH9B9F,IAAK,SACP,EAmBQkF,KAAMA,EACNa,OAhBWlC,CAgBHmC,GAfdnC,EAAIoC,UAAU,CAAC,CACbC,OAAQrB,CACV,EACF,EAaQsB,QAAS,CACPhB,EAhBWN,MAgBFM,EACTrF,UAAW,GACXsG,kBAAmB,GACnBC,mBAAmB,EACnBC,YAAY,EACZC,gBAAgB,EAChBC,gBAAgB,EAChBC,mBAAmB,CACrB,YAECnC,EACAnD,GAAcF,GAAgBA,EAAaZ,WAAW,EACrD,UAAC+D,EAAgBA,CACfzE,SAAUsB,EAAaZ,SADR+D,EACmB,GAClCC,aAAc,KAEZjC,QAAQC,GAAG,CAAC,qBACZmB,GAAAA,GACF,WAECrC,GAHCqC,QA5BQ,UAACgB,MAAAA,UAAI,mBAsC7B", "sources": ["webpack://_N_E/./components/common/RKIMapMarker.tsx", "webpack://_N_E/?25e7", "webpack://_N_E/./pages/project/ListMapContainer.tsx", "webpack://_N_E/./components/common/RKIMapInfowindow.tsx", "webpack://_N_E/./components/common/mapStyles.tsx", "webpack://_N_E/./components/common/RKIMap1.tsx"], "sourcesContent": ["import React from 'react';\r\nimport { Marker } from '@react-google-maps/api';\r\n\r\ninterface RKIMapMarkerProps {\r\n  name?: string;\r\n  id?: string;\r\n  countryId?: string;\r\n  type?: string;\r\n  icon?: {\r\n    url: string;\r\n    scaledSize?: google.maps.Size;\r\n  };\r\n  position: {\r\n    lat: number;\r\n    lng: number;\r\n  };\r\n  onClick?: (props: any, marker: any, e: any) => void;\r\n  title?: string;\r\n  draggable?: boolean;\r\n}\r\n\r\nconst RKIMapMarker: React.FC<RKIMapMarkerProps> = ({\r\n  name = 'Marker',\r\n  id = '',\r\n  countryId = '',\r\n  type,\r\n  icon,\r\n  position,\r\n  onClick,\r\n  title,\r\n  draggable = false,\r\n}) => {\r\n  const handleClick = (e: google.maps.MapMouseEvent) => {\r\n    if (onClick) {\r\n      const markerProps = {\r\n        name,\r\n        id,\r\n        countryId,\r\n        type,\r\n        position,\r\n      };\r\n\r\n      // Create a marker-like object for compatibility with old onClick signature\r\n      const marker = {\r\n        position,\r\n        getPosition: () => position,\r\n      };\r\n\r\n      onClick(markerProps, marker, e);\r\n    }\r\n  };\r\n\r\n  // Ensure position is valid\r\n  if (!position || typeof position.lat !== 'number' || typeof position.lng !== 'number') {\r\n    return null;\r\n  }\r\n\r\n  return (\r\n    <Marker\r\n      position={position}\r\n      icon={icon}\r\n      title={title || name}\r\n      draggable={draggable}\r\n      onClick={handleClick}\r\n    />\r\n  );\r\n};\r\n\r\nexport default RKIMapMarker;\r\n", "\n    (window.__NEXT_P = window.__NEXT_P || []).push([\n      \"/project/ListMapContainer\",\n      function () {\n        return require(\"private-next-pages/project/ListMapContainer.tsx\");\n      }\n    ]);\n    if(module.hot) {\n      module.hot.dispose(function () {\n        window.__NEXT_P.push([\"/project/ListMapContainer\"])\n      });\n    }\n  ", "//Import Library\r\nimport React, { useState, useEffect } from \"react\";\r\nimport _ from \"lodash\";\r\n\r\n//Import services/components\r\nimport { useTranslation } from 'next-i18next';\r\nimport RKIMAP1 from \"../../components/common/RKIMap1\";\r\nimport RKIMapMarker from \"../../components/common/RKIMapMarker\";\r\n\r\nconst ListMapContainer = (props: any) => {\r\n  const { i18n } = useTranslation('common');\r\n  const currentLang = i18n.language;\r\n  const { projects, selectedRegions } = props;\r\n  const [points, setPoints] = useState<any[]>([]);\r\n  const [activeMarker, setactiveMarker]: any = useState({});\r\n  const [markerInfo, setMarkerInfo]: any = useState({});\r\n  const [groupedProjects, setGroupedProjects]: any = useState({});\r\n\r\n  const MarkerInfo = (Markerprops: any) => {\r\n    const { info } = Markerprops;\r\n    if (info && info.countryId && groupedProjects[info.countryId]) {\r\n      return (\r\n        <ul>\r\n          {groupedProjects[info.countryId].map((item: any, index: any) => {\r\n            return (\r\n              <li key={index}>\r\n                <a href={`/${currentLang}/project/show/${item.id}`}>{item.title}</a>\r\n              </li>\r\n            );\r\n          })}\r\n        </ul>\r\n      );\r\n    }\r\n    return null;\r\n  };\r\n\r\n  const resetMarker = () => {\r\n    setactiveMarker(null);\r\n    setMarkerInfo(null);\r\n  };\r\n\r\n  const onMarkerClick = (propsinitial: any, marker: any, e: any) => {\r\n    resetMarker();\r\n    setactiveMarker(marker);\r\n    setMarkerInfo({\r\n      name: propsinitial.name,\r\n      id: propsinitial.id,\r\n      countryId: propsinitial.countryId,\r\n    });\r\n  };\r\n\r\n  const part = (pointer: any) =>\r\n    pointer.partner_country && pointer.partner_country.coordinates;\r\n  const part1 = (pointer: any) =>\r\n    pointer.partner_country && pointer.partner_country.coordinates;\r\n  const getpointer = (project: any) => {\r\n    const projectParterner: any[] = [];\r\n    _.forEach(project.partner_institutions, (pointer: any) => {\r\n      console.log(\"pointer\", pointer);\r\n\r\n      projectParterner.push({\r\n        title: project && project.title ? project.title : \"\",\r\n        id: project && project._id ? project._id : \"\",\r\n        lat:\r\n          part(pointer) &&\r\n          parseFloat(pointer.partner_country.coordinates[0].latitude),\r\n        lng:\r\n          part1(pointer) &&\r\n          parseFloat(pointer.partner_country.coordinates[0].longitude),\r\n        world_region: pointer.world_region,\r\n        countryId: pointer.partner_country && pointer.partner_country._id,\r\n      });\r\n    });\r\n    return projectParterner[0];\r\n  };\r\n\r\n  const setPointsFromProjects = () => {\r\n    const filterProjectpoints: any[] = [];\r\n    _.forEach(projects, (project) => {\r\n      const partner = getpointer(project);\r\n      filterProjectpoints.push(partner);\r\n    });\r\n    const filteredPoints = _.filter(filterProjectpoints, function (point) {\r\n      if (selectedRegions.length > 0) {\r\n        return selectedRegions.includes(point.world_region);\r\n      }\r\n    });\r\n    setPoints(filteredPoints);\r\n  };\r\n\r\n  const getProjectsGroupBCountry = () => {\r\n    const countriesList: any[] = [];\r\n    _.forEach(projects, (project) => {\r\n      if (\r\n        project.partner_institutions &&\r\n        project.partner_institutions.length > 0\r\n      ) {\r\n        _.forEach(project.partner_institutions, (pi) => {\r\n          pi.title = project.title;\r\n          pi.id = project._id;\r\n          countriesList.push(pi);\r\n        });\r\n      }\r\n    });\r\n    setGroupedProjects(_.groupBy(countriesList, \"partner_country._id\"));\r\n  };\r\n\r\n  useEffect(() => {\r\n    setPointsFromProjects();\r\n    getProjectsGroupBCountry();\r\n  }, [projects]);\r\n\r\n  return (\r\n    <RKIMAP1\r\n      onClose={resetMarker}\r\n      language={currentLang}\r\n      activeMarker={activeMarker}\r\n      markerInfo={<MarkerInfo info={markerInfo} />}\r\n    >\r\n      {points.length >= 1\r\n        ? points.map((item, index) => {\r\n            return (\r\n              <RKIMapMarker\r\n                key={index}\r\n                name={item.title}\r\n                id={item.id}\r\n                countryId={item.countryId}\r\n                icon={{\r\n                  url: \"/images/map-marker-white.svg\",\r\n                }}\r\n                onClick={onMarkerClick}\r\n                position={item}\r\n              />\r\n            );\r\n          })\r\n        : null}\r\n    </RKIMAP1>\r\n  );\r\n};\r\n\r\nexport default ListMapContainer;\r\n", "import { InfoWindow } from '@react-google-maps/api';\r\n\r\ninterface Props {\r\n  position: google.maps.LatLngLiteral;\r\n  onCloseClick?: () => void;\r\n  children?: React.ReactNode;\r\n}\r\n\r\nconst RKIMapInfowindow = ({ position, onCloseClick, children }: Props) => {\r\n  return (\r\n    <InfoWindow position={position} onCloseClick={onCloseClick}>\r\n      <div>{children}</div>\r\n    </InfoWindow>\r\n  );\r\n};\r\n\r\nexport default RKIMapInfowindow;\r\n", "const fill = \"labels.text.fill\"\r\nconst stoke = \"labels.text.stroke\"\r\nconst road = \"road.highway\"\r\nconst geometry = \"geometry.stroke\"\r\nconst mapStyles = [\r\n  {\r\n    \"elementType\": \"geometry\",\r\n    \"stylers\": [\r\n      {\r\n        \"color\": \"#1d2c4d\"\r\n      }\r\n    ]\r\n  },\r\n  {\r\n    \"elementType\": fill,\r\n    \"stylers\": [\r\n      {\r\n        \"color\": \"#8ec3b9\"\r\n      }\r\n    ]\r\n  },\r\n  {\r\n    \"elementType\": stoke,\r\n    \"stylers\": [\r\n      {\r\n        \"color\": \"#1a3646\"\r\n      }\r\n    ]\r\n  },\r\n  {\r\n    \"featureType\": \"administrative\",\r\n    \"elementType\": \"geometry\",\r\n    \"stylers\": [\r\n      {\r\n        \"visibility\": \"off\"\r\n      }\r\n    ]\r\n  },\r\n  {\r\n    \"featureType\": \"administrative.country\",\r\n    \"elementType\": geometry,\r\n    \"stylers\": [\r\n      {\r\n        \"color\": \"#4b6878\"\r\n      }\r\n    ]\r\n  },\r\n  {\r\n    \"featureType\": \"administrative.land_parcel\",\r\n    \"elementType\": \"labels\",\r\n    \"stylers\": [\r\n      {\r\n        \"visibility\": \"off\"\r\n      }\r\n    ]\r\n  },\r\n  {\r\n    \"featureType\": \"administrative.land_parcel\",\r\n    \"elementType\": fill,\r\n    \"stylers\": [\r\n      {\r\n        \"color\": \"#64779e\"\r\n      }\r\n    ]\r\n  },\r\n  {\r\n    \"featureType\": \"administrative.province\",\r\n    \"elementType\": geometry,\r\n    \"stylers\": [\r\n      {\r\n        \"color\": \"#4b6878\"\r\n      }\r\n    ]\r\n  },\r\n  {\r\n    \"featureType\": \"landscape.man_made\",\r\n    \"elementType\": geometry,\r\n    \"stylers\": [\r\n      {\r\n        \"color\": \"#334e87\"\r\n      }\r\n    ]\r\n  },\r\n  {\r\n    \"featureType\": \"landscape.natural\",\r\n    \"elementType\": \"geometry\",\r\n    \"stylers\": [\r\n      {\r\n        \"color\": \"#023e58\"\r\n      }\r\n    ]\r\n  },\r\n  {\r\n    \"featureType\": \"poi\",\r\n    \"stylers\": [\r\n      {\r\n        \"visibility\": \"off\"\r\n      }\r\n    ]\r\n  },\r\n  {\r\n    \"featureType\": \"poi\",\r\n    \"elementType\": \"geometry\",\r\n    \"stylers\": [\r\n      {\r\n        \"color\": \"#283d6a\"\r\n      }\r\n    ]\r\n  },\r\n  {\r\n    \"featureType\": \"poi\",\r\n    \"elementType\": \"labels.text\",\r\n    \"stylers\": [\r\n      {\r\n        \"visibility\": \"off\"\r\n      }\r\n    ]\r\n  },\r\n  {\r\n    \"featureType\": \"poi\",\r\n    \"elementType\": fill,\r\n    \"stylers\": [\r\n      {\r\n        \"color\": \"#6f9ba5\"\r\n      }\r\n    ]\r\n  },\r\n  {\r\n    \"featureType\": \"poi\",\r\n    \"elementType\": stoke,\r\n    \"stylers\": [\r\n      {\r\n        \"color\": \"#1d2c4d\"\r\n      }\r\n    ]\r\n  },\r\n  {\r\n    \"featureType\": \"poi.park\",\r\n    \"elementType\": \"geometry.fill\",\r\n    \"stylers\": [\r\n      {\r\n        \"color\": \"#023e58\"\r\n      }\r\n    ]\r\n  },\r\n  {\r\n    \"featureType\": \"poi.park\",\r\n    \"elementType\": fill,\r\n    \"stylers\": [\r\n      {\r\n        \"color\": \"#3C7680\"\r\n      }\r\n    ]\r\n  },\r\n  {\r\n    \"featureType\": \"road\",\r\n    \"stylers\": [\r\n      {\r\n        \"visibility\": \"off\"\r\n      }\r\n    ]\r\n  },\r\n  {\r\n    \"featureType\": \"road\",\r\n    \"elementType\": \"geometry\",\r\n    \"stylers\": [\r\n      {\r\n        \"color\": \"#304a7d\"\r\n      }\r\n    ]\r\n  },\r\n  {\r\n    \"featureType\": \"road\",\r\n    \"elementType\": \"labels.icon\",\r\n    \"stylers\": [\r\n      {\r\n        \"visibility\": \"off\"\r\n      }\r\n    ]\r\n  },\r\n  {\r\n    \"featureType\": \"road\",\r\n    \"elementType\": fill,\r\n    \"stylers\": [\r\n      {\r\n        \"color\": \"#98a5be\"\r\n      }\r\n    ]\r\n  },\r\n  {\r\n    \"featureType\": \"road\",\r\n    \"elementType\": stoke,\r\n    \"stylers\": [\r\n      {\r\n        \"color\": \"#1d2c4d\"\r\n      }\r\n    ]\r\n  },\r\n  {\r\n    \"featureType\": road,\r\n    \"elementType\": \"geometry\",\r\n    \"stylers\": [\r\n      {\r\n        \"color\": \"#2c6675\"\r\n      }\r\n    ]\r\n  },\r\n  {\r\n    \"featureType\": road,\r\n    \"elementType\": geometry,\r\n    \"stylers\": [\r\n      {\r\n        \"color\": \"#255763\"\r\n      }\r\n    ]\r\n  },\r\n  {\r\n    \"featureType\": road,\r\n    \"elementType\": fill,\r\n    \"stylers\": [\r\n      {\r\n        \"color\": \"#b0d5ce\"\r\n      }\r\n    ]\r\n  },\r\n  {\r\n    \"featureType\": road,\r\n    \"elementType\": stoke,\r\n    \"stylers\": [\r\n      {\r\n        \"color\": \"#023e58\"\r\n      }\r\n    ]\r\n  },\r\n  {\r\n    \"featureType\": \"road.local\",\r\n    \"elementType\": \"labels\",\r\n    \"stylers\": [\r\n      {\r\n        \"visibility\": \"off\"\r\n      }\r\n    ]\r\n  },\r\n  {\r\n    \"featureType\": \"transit\",\r\n    \"stylers\": [\r\n      {\r\n        \"visibility\": \"off\"\r\n      }\r\n    ]\r\n  },\r\n  {\r\n    \"featureType\": \"transit\",\r\n    \"elementType\": fill,\r\n    \"stylers\": [\r\n      {\r\n        \"color\": \"#98a5be\"\r\n      }\r\n    ]\r\n  },\r\n  {\r\n    \"featureType\": \"transit\",\r\n    \"elementType\": stoke,\r\n    \"stylers\": [\r\n      {\r\n        \"color\": \"#1d2c4d\"\r\n      }\r\n    ]\r\n  },\r\n  {\r\n    \"featureType\": \"transit.line\",\r\n    \"elementType\": \"geometry.fill\",\r\n    \"stylers\": [\r\n      {\r\n        \"color\": \"#283d6a\"\r\n      }\r\n    ]\r\n  },\r\n  {\r\n    \"featureType\": \"transit.station\",\r\n    \"elementType\": \"geometry\",\r\n    \"stylers\": [\r\n      {\r\n        \"color\": \"#3a4762\"\r\n      }\r\n    ]\r\n  },\r\n  {\r\n    \"featureType\": \"water\",\r\n    \"elementType\": \"geometry\",\r\n    \"stylers\": [\r\n      {\r\n        \"color\": \"#0e1626\"\r\n      }\r\n    ]\r\n  },\r\n  {\r\n    \"featureType\": \"water\",\r\n    \"elementType\": fill,\r\n    \"stylers\": [\r\n      {\r\n        \"color\": \"#4e6d70\"\r\n      }\r\n    ]\r\n  }\r\n];\r\n\r\nexport default mapStyles;", "import React from 'react';\r\nimport { GoogleMap, useJsApiLoader } from '@react-google-maps/api';\r\nimport RKIMapInfowindow from './RKIMapInfowindow';\r\nimport mapStyles from './mapStyles';\r\nimport { useRouter } from \"next/router\";\r\nimport { useGoogleMaps } from './GoogleMapsProvider';\r\n\r\ninterface RKIMap1Props {\r\n  markerInfo?: React.ReactNode;\r\n  activeMarker?: any;\r\n  initialCenter?: { lat: number; lng: number };\r\n  children?: React.ReactNode;\r\n  height?: number | string;\r\n  width?: string;\r\n  language?: string;\r\n  points?: any[];\r\n  zoom?: number;\r\n  minZoom?: number;\r\n  onClose?: () => void\r\n}\r\n\r\nconst RKIMap1: React.FC<RKIMap1Props> = ({\r\n  markerInfo,\r\n  activeMarker,\r\n  initialCenter,\r\n  children,\r\n  height = 300,\r\n  width = \"114%\",\r\n  language,\r\n  zoom = 1,\r\n  minZoom = 1,\r\n  onClose\r\n}) => {\r\n  const { locale } = useRouter();\r\n  const { isLoaded, loadError } =  useGoogleMaps();\r\n  const containerStyle = {\r\n    width: width,\r\n    height: typeof height === 'number' ? `${height}px` : height,\r\n  };\r\n\r\n  const defaultCenter = {\r\n    lat: 52.520017,\r\n    lng: 13.404195,\r\n  };\r\n\r\n  const center = initialCenter || defaultCenter;\r\n\r\n  const onMapLoad = (map: google.maps.Map) => {\r\n    map.setOptions({\r\n      styles: mapStyles,\r\n    });\r\n  };\r\n\r\n  if (loadError) return <div>Error loading maps</div>;\r\n  if (!isLoaded) return <div>Loading Maps...</div>;\r\n\r\n  return (\r\n    <div className=\"map-container\">\r\n      <div className=\"mapprint\" style={{ width, height, position: 'relative' }}>\r\n        <GoogleMap\r\n          mapContainerStyle={containerStyle}\r\n          center={center}\r\n          zoom={zoom}\r\n          onLoad={onMapLoad}\r\n          options={{\r\n            minZoom: minZoom,\r\n            draggable: true,\r\n            keyboardShortcuts: false,\r\n            streetViewControl: false,\r\n            panControl: false,\r\n            clickableIcons: false,\r\n            mapTypeControl: false,\r\n            fullscreenControl: true,\r\n          }}\r\n        >\r\n          {children}\r\n          {markerInfo && activeMarker && activeMarker.getPosition && (\r\n            <RKIMapInfowindow\r\n              position={activeMarker.getPosition()}\r\n              onCloseClick={() => {\r\n                // Handle close if needed\r\n                console.log('close click');\r\n                onClose?.()\r\n              }}\r\n            >\r\n              {markerInfo}\r\n            </RKIMapInfowindow>\r\n          )}\r\n        </GoogleMap>\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default RKIMap1;\r\n"], "names": ["name", "id", "R<PERSON>IMapMarker", "countryId", "type", "icon", "position", "onClick", "title", "draggable", "lat", "lng", "<PERSON><PERSON>", "e", "handleClick", "marker", "getPosition", "props", "i18n", "useTranslation", "ListMapContainer", "currentLang", "language", "projects", "selectedRegions", "points", "setPoints", "useState", "activeMarker", "set<PERSON><PERSON><PERSON><PERSON>", "markerInfo", "setMarkerInfo", "groupedProjects", "setGroupedProjects", "reset<PERSON><PERSON><PERSON>", "onMarkerClick", "propsinitial", "part", "pointer", "partner_country", "coordinates", "part1", "getpointer", "projectParterner", "_", "project", "partner_institutions", "console", "log", "push", "_id", "parseFloat", "latitude", "longitude", "world_region", "setPointsFromProjects", "filterProjectpoints", "partner", "filteredPoints", "point", "length", "includes", "getProjectsGroupBCountry", "countriesList", "pi", "useEffect", "RKIMAP1", "onClose", "MarkerInfo", "info", "Markerprops", "ul", "map", "item", "index", "li", "a", "href", "url", "RKIMapInfowindow", "onCloseClick", "children", "InfoWindow", "div", "fill", "stoke", "road", "geometry", "mapStyles", "RKIMap1", "initialCenter", "height", "width", "zoom", "minZoom", "locale", "useRouter", "isLoaded", "loadError", "useGoogleMaps", "className", "style", "GoogleMap", "mapContainerStyle", "center", "defaultCenter", "onLoad", "onMapLoad", "setOptions", "styles", "options", "keyboardShortcuts", "streetViewControl", "panControl", "clickableIcons", "mapTypeControl", "fullscreenControl"], "sourceRoot": "", "ignoreList": []}