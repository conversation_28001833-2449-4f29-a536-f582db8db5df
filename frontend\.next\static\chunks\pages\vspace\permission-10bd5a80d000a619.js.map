{"version": 3, "file": "static/chunks/pages/vspace/permission-10bd5a80d000a619.js", "mappings": "gFACA,4CACA,qBACA,WACA,OAAe,EAAQ,KAA0C,CACjE,EACA,SAFsB,0NCGf,IAAMA,EAAeC,CAAAA,EAAAA,EAAAA,CAAAA,CAAoBA,CAAC,CAC/CC,sBAAuB,KACjBC,EAAMC,WAAW,IAAID,EAAMC,WAAW,CAACC,MAAM,IAAIF,EAAMC,WAAW,CAACC,MAAM,CAAC,aAAa,CAK7FC,CAL+F,kBAK3E,cACtB,GAAG,EAE6BL,CAAAA,EAAAA,EAAAA,CAAAA,CAAoBA,CAAC,CACnDC,sBAAuB,KACjBC,EAAMC,WAAW,IAAID,EAAMC,WAAW,CAACC,MAAM,IAAIF,EAAMC,WAAW,CAACC,MAAM,CAAC,aAAa,CAK7FC,CAL+F,kBAK3E,mBACpBC,iBAAkB,IAAM,UAACC,EAAAA,OAAIA,CAAAA,CAAAA,EAC/B,GAAG,EAE0BP,CAAAA,EAAAA,EAAAA,CAAAA,CAAoBA,CAAC,CAChDC,sBAAuB,CAACC,EAAOM,KAC7B,GAAIN,EAAMC,WAAW,EAAID,EAAMC,WAAW,CAACC,MAAM,EAAE,GAC7CF,EAAMC,WAAW,CAACC,MAAM,CAAC,aAAa,CACxC,CAD0C,MACnC,OAEP,GAAIF,EAAMC,WAAW,CAACC,MAAM,CAAC,aAAa,EAAE,EAChCA,MAAM,EAAII,EAAMJ,MAAM,CAACK,IAAI,EAAID,EAAMJ,MAAM,CAACK,IAAI,CAACC,GAAG,GAAKR,EAAMO,IAAI,CAACC,GAAG,CAC/E,CADiF,MAC1E,CAGb,CAEF,OAAO,CACT,EACAL,mBAAoB,eACtB,GAAG,EAE8BL,CAAAA,EAAAA,EAAAA,CAAAA,CAAoBA,CAAC,CACpDC,sBAAuB,CAACC,EAAOM,KAC7B,GAAIN,EAAMC,WAAW,EAAID,EAAMC,WAAW,CAACC,MAAM,EAAE,GAC7CF,EAAMC,WAAW,CAACC,MAAM,CAAC,aAAa,CACxC,CAD0C,MACnC,OAEP,GAAIF,EAAMC,WAAW,CAACC,MAAM,CAAC,aAAa,EAAE,EAChCA,MAAM,EAAII,EAAMJ,MAAM,CAACK,IAAI,EAAID,EAAMJ,MAAM,CAACK,IAAI,CAACC,GAAG,GAAKR,EAAMO,IAAI,CAACC,GAAG,CAC/E,CADiF,MAC1E,CAGb,CAEF,MAAO,EACT,EACAL,mBAAoB,oBACpBC,iBAAkB,IAAM,UAACC,EAAAA,OAAIA,CAAAA,CAAAA,EAC/B,GAAG,EAEoCP,CAAAA,EAAAA,EAAAA,CAAAA,CAAoBA,CAAC,CAC1DC,sBAAuB,KACjBC,EAAMC,WAAW,IAAID,EAAMC,WAAW,CAACQ,MAAM,IAAIT,EAAMC,WAAW,CAACQ,MAAM,CAAC,WAAW,CAK3FN,CAL6F,kBAKzE,yBACtB,GAAG,EAEYN,YAAYA,EAAC", "sources": ["webpack://_N_E/?ffa3", "webpack://_N_E/./pages/vspace/permission.tsx"], "sourcesContent": ["\n    (window.__NEXT_P = window.__NEXT_P || []).push([\n      \"/vspace/permission\",\n      function () {\n        return require(\"private-next-pages/vspace/permission.tsx\");\n      }\n    ]);\n    if(module.hot) {\n      module.hot.dispose(function () {\n        window.__NEXT_P.push([\"/vspace/permission\"])\n      });\n    }\n  ", "//Import Library\r\nimport React from 'react';\r\nimport connectedAuthWrapper from 'redux-auth-wrapper/connectedAuthWrapper';\r\n\r\n//Import services/components\r\nimport R403 from \"../r403\";\r\n\r\nexport const canAddVspace = connectedAuthWrapper({\r\n  authenticatedSelector: (state) => {\r\n    if (state.permissions && state.permissions.vspace && state.permissions.vspace['create:any']) {\r\n      return true;\r\n    }\r\n    return false;\r\n  },\r\n  wrapperDisplayName: 'CanAddVspace',\r\n});\r\n\r\nexport const canAddVspaceForm = connectedAuthWrapper({\r\n  authenticatedSelector: (state) => {\r\n    if (state.permissions && state.permissions.vspace && state.permissions.vspace['create:any']) {\r\n      return true;\r\n    }\r\n    return false;\r\n  },\r\n  wrapperDisplayName: 'CanAddVspaceForm',\r\n  FailureComponent: () => <R403/>\r\n});\r\n\r\nexport const canEditVspace = connectedAuthWrapper({\r\n  authenticatedSelector: (state, props) => {\r\n    if (state.permissions && state.permissions.vspace) {\r\n      if (state.permissions.vspace['update:any']) {\r\n        return true;\r\n      } else { //if update:own\r\n        if (state.permissions.vspace['update:own']) {\r\n          if (props.vspace && props.vspace.user && props.vspace.user._id === state.user._id) {\r\n            return true;\r\n          }  \r\n        }\r\n      }\r\n    }\r\n    return false;\r\n  },\r\n  wrapperDisplayName: 'CanEditVspace',\r\n});\r\n\r\nexport const canEditVspaceForm = connectedAuthWrapper({\r\n  authenticatedSelector: (state, props) => {\r\n    if (state.permissions && state.permissions.vspace) {\r\n      if (state.permissions.vspace['update:any']) {\r\n        return true;\r\n      } else { //if update:own\r\n        if (state.permissions.vspace['update:own']) {\r\n          if (props.vspace && props.vspace.user && props.vspace.user._id === state.user._id) {\r\n            return true;\r\n          }\r\n        }\r\n      }\r\n    }\r\n    return false;\r\n  },\r\n  wrapperDisplayName: 'CanEditVspaceForm',\r\n  FailureComponent: () => <R403/>\r\n});\r\n\r\nexport const canViewDiscussionUpdate = connectedAuthWrapper({\r\n  authenticatedSelector: (state) => {\r\n    if (state.permissions && state.permissions.update && state.permissions.update['read:any']) {\r\n      return true;\r\n    }\r\n    return false;\r\n  },\r\n  wrapperDisplayName: 'CanViewDiscussionUpdate',\r\n});\r\n\r\nexport default canAddVspace;"], "names": ["canAddVspace", "connectedAuthWrapper", "authenticatedSelector", "state", "permissions", "vspace", "wrapperDisplayName", "FailureComponent", "R403", "props", "user", "_id", "update"], "sourceRoot": "", "ignoreList": []}