{"version": 3, "file": "static/chunks/5377-7a5e3a4896889050.js", "mappings": "4UAgWA,MA5UqBA,QAyNkCC,EAAAA,EAYAA,EAAAA,EApOnD,GAAM,GAAEC,CAAC,CAAE,CAAGC,CAAAA,EA2UHC,EA3UGD,EAAAA,CAAcA,CAAC,KA2UPC,EAAC,GA1UjBC,EAAkB,CACpBC,IAAK,GACLC,MAAO,GACPC,KAAM,GACNC,MAAO,GACPC,UAAW,GACXC,YAAa,CACT,CACIC,SAAU,GACVC,UAAW,EACf,EACH,CACDC,aAAc,GACdC,eAAgB,GAChBC,gBAAiB,EACrB,EAEM,CAACf,EAAYgB,EAAc,CAAGC,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAUb,GAChD,CAACc,EAAaC,EAAe,CAAGF,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAgB,EAAE,EAC1D,CAACP,EAAaU,EAAgB,CAAGH,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,EAAU,GAEnDI,EACFtB,EAAMuB,MAAM,EAAKvB,EAAAA,CAAMuB,MAAM,CAAC,EAAE,GAAKrB,EAAE,8CACvC,cAAgBA,EAAE,6CAA2C,EAAMF,EAAMuB,MAAM,CAAC,EAAE,CAEhFC,EAAUC,CAAAA,EAAAA,EAAAA,MAAAA,CAAMA,CAAC,MAQjBC,EAAe,MAAOC,EAAYC,SAQ1BC,MAcNC,EACAC,EAtBJJ,EAAMK,cAAc,GAEpB,IAAMH,EAAaD,GAAU3B,EACvBgC,EAAWJ,EAAWtB,KAAK,CAAC2B,MAAM,CAAC,GAAGC,WAAW,GAAKN,EAAWtB,KAAK,CAAC6B,KAAK,CAAC,GAC7EC,EAAM,CACR9B,KAAK,OAAE0B,EAAAA,KAAAA,EAAAA,EAAUK,IAAI,GACrBC,GADON,KACC,OAAEA,EAAAA,KAAAA,EAAAA,EAAUK,IAAI,GACxB9B,GADUyB,CACN,QAAEJ,EAAAA,EAAWrB,IAAAA,EAAXqB,KAAAA,EAAAA,EAAiBS,GAAjBT,CAAqB,GAC3BpB,MAAOoB,EAAWpB,KAAK,CACvBC,UAAWmB,EAAWnB,SAAS,CAC/B8B,aAAc,CACVC,GAAIZ,EAAWtB,KAAK,CAAC2B,MAAM,CAAC,GAAGC,WAAW,GAC1CO,GAAIb,EAAWtB,KAAK,CAAC2B,MAAM,CAAC,GAAGC,WAAW,GAC1CQ,GAAId,EAAWtB,KAAK,CAAC2B,MAAM,CAAC,GAAGC,WAAW,EAC9C,EACAxB,YAAakB,EAAWlB,WAAW,CACnCG,aAAce,EAAWf,YAAY,CACrCC,eAAgBc,EAAWd,cAAc,CACzCC,gBAAiBa,EAAWb,eAAe,EAK3CM,GACAS,EAAW,KADD,uDAEVD,EAAW,MAAMc,EAAAA,CAAUA,CAACC,KAAK,CAAC,YAA4B,OAAhB7C,EAAMuB,MAAM,CAAC,EAAE,EAAIc,KAEjEN,EAAW,0DACXD,EAAW,MAAMc,EAAAA,CAAUA,CAACE,IAAI,CAAC,WAAYT,IAE7CP,GAAYA,EAASxB,GAAG,EACxByC,EAAAA,EAAKA,CAACC,OAAO,CAAC9C,EAAE6B,IAChBkB,IAAAA,IAAW,CAAC,2BAERnB,OAAAA,EAAAA,KAAAA,EAAAA,EAAUoB,SAAAA,CAAVpB,GAAwB,KACxBiB,EAD+B,EAC1BA,CAACI,KAAK,CAACjD,EAAE,yBAEd6C,EAAAA,EAAKA,CAACI,KAAK,CAACrB,EAGxB,EAEMsB,EAAe,IACjB,GAAIC,EAAEC,MAAM,CAAE,CACV,GAAM,CAAEC,MAAI,OAAEC,CAAK,CAAE,CAAGH,EAAEC,MAAM,eAC5BC,GAAwBA,YAAqB,GAC7CtC,EAAc,QAEShB,QAFO,CAC1B,GAAGwD,CAAS,CACZ9C,YAAa,CAAC,WAAKV,EAAAA,EAAWU,WAAAA,EAAXV,KAAAA,EAAAA,CAAwB,CAAC,EAAE,CAAE,CAACsD,EAAK,CAAEC,CAAM,EAAE,IAGpEvC,EAAewC,GAAe,EAC1B,GAAGA,CAAS,CACZ,CAACF,CAFyB,CAEpB,CAAEC,CACZ,GAER,CACJ,EAEME,EAAiB,MAAOC,IAC1B,IAAM7B,EAAuC,MAAMc,EAAAA,CAAUA,CAACgB,GAAG,CAAC,eAAgBD,GAC9E7B,GACAV,EAAeU,EAAS+B,GADd,CACkB,CAEpC,EAwBA,MAtBAC,CAAAA,EAAAA,EAAAA,SAAAA,CAASA,CAAC,KACN,IAAMH,EAAkB,CACpBI,MAAO,CAAC,EACRC,KAAM,CAAEzD,MAAO,KAAM,EACrB0D,MAAO,GACX,EAEI3C,GASA4C,CARuB,MADb,IAEN,IAAMpC,EAAoB,MAAMc,EAAAA,CAAUA,CAACgB,GAAG,CAAC,YAA4B,OAAhB5D,EAAMuB,MAAM,CAAC,EAAE,EAAI4C,GAC9E,GAAIrC,EAAU,CACV,GAAM,cAAEhB,CAAY,CAAE,CAAGgB,EACzBA,EAAShB,YAAY,CAAGA,GAAgB,EAAsBR,GAAG,CAAG,EAAsBA,GAAG,CAAG,GAChGW,EAAc,GAAgB,EAAE,GAAGwC,CAAS,CAAE,EAAhB,CAAmB3B,CAAQ,CAAC,EAC9D,EACJ,EACe6B,GAGnBD,EAAeC,EACnB,EAAG,EAAE,EAGD,UAACS,MAAAA,UACG,UAACC,EAAAA,CAASA,CAAAA,CAACC,UAAU,WAAWC,KAAK,aACjC,UAACC,EAAAA,CAAIA,CAAAA,CACDC,MAAO,CACHC,UAAW,MACXC,UAAW,kEACf,WAEA,UAACC,EAAAA,CAAqBA,CAAAA,CAACC,SAAUnD,EAAcoD,IAAKtD,EAASuD,cAAe9E,EAAY+E,oBAAoB,WACxG,WAACR,EAAAA,CAAIA,CAACS,IAAI,YACN,UAACC,EAAAA,CAAGA,CAAAA,UACA,UAACC,EAAAA,CAAGA,CAAAA,UACA,UAACX,EAAAA,CAAIA,CAACY,KAAK,WAAE9D,EAAWpB,EAAE,4CAA8CA,EAAE,iDAGlF,UAACmF,KAAAA,CAAAA,GACD,UAACH,EAAAA,CAAGA,CAAAA,CAACZ,UAAU,gBACX,UAACa,EAAAA,CAAGA,CAAAA,CAACG,EAAE,IAACC,GAAI,GAAIC,GAAI,YAChB,WAACC,EAAAA,CAAIA,CAACC,KAAK,YACP,UAACD,EAAAA,CAAIA,CAACE,KAAK,EAACrB,UAAU,0BACjBpE,EAAE,8CAEP,UAAC0F,EAAAA,EAASA,CAAAA,CACNrC,KAAK,QACLsC,GAAG,QACHC,QAAQ,IACRtC,MAAOvD,EAAWM,KAAK,CACvBwF,UAAW,GAAgBC,YAAOxC,GAAS,IAAIlB,IAAI,GACnD2D,aAAc,CACVF,UAAW7F,EAAE,uDACjB,EACAgG,SAAU9C,WAKtB,WAAC8B,EAAAA,CAAGA,CAAAA,CAACZ,UAAU,iBACf,UAACa,EAAAA,CAAGA,CAAAA,CAACG,EAAE,IAACC,GAAI,EAAGC,GAAI,YACf,WAACC,EAAAA,CAAIA,CAACC,KAAK,YACP,UAACD,EAAAA,CAAIA,CAACE,KAAK,EAACrB,UAAU,0BACjBpE,EAAE,8CAEP,UAAC0F,EAAAA,EAASA,CAAAA,CACNrC,KAAK,OACLsC,GAAG,OACHC,QAAQ,IACRtC,MAAOvD,EAAWO,IAAI,CACtByF,aAAc,CAACF,UAAW7F,EAAE,uDAAuD,EACnFgG,SAAU9C,SAItB,UAAC+B,EAAAA,CAAGA,CAAAA,CAACG,EAAE,IAACC,GAAI,EAAGC,GAAI,YACf,WAACC,EAAAA,CAAIA,CAACC,KAAK,YACP,UAACD,EAAAA,CAAIA,CAACE,KAAK,WAAEzF,EAAE,+CACf,UAAC0F,EAAAA,EAASA,CAAAA,CACNrC,KAAK,QACLsC,GAAG,QACHrC,MAAOvD,EAAWQ,KAAK,CACvBwF,aAAc/F,EAAE,yDAChBgG,SAAU9C,SAKtB,UAAC+B,EAAAA,CAAGA,CAAAA,CAACG,EAAE,IAACC,GAAI,EAAGC,GAAI,YACf,WAACC,EAAAA,CAAIA,CAACC,KAAK,YACP,WAACD,EAAAA,CAAIA,CAACE,KAAK,YAAEzF,EAAE,yCAAyC,OACxD,UAAC0F,EAAAA,EAASA,CAAAA,CACNrC,KAAK,YACLsC,GAAG,YACHrC,MAAOvD,EAAWS,SAAS,CAC3BuF,aAAc/F,EAAE,4DAChBgG,SAAU9C,YAK1B,UAAC8B,EAAAA,CAAGA,CAAAA,CAACZ,UAAU,gBACX,UAACa,EAAAA,CAAGA,CAAAA,UACA,UAACX,EAAAA,CAAIA,CAACY,KAAK,WAAElF,EAAE,mDAItBS,EACG,WAACuE,EAAAA,CAAGA,CAAAA,CAACZ,UAAU,iBACX,UAACa,EAAAA,CAAGA,CAAAA,CAACG,EAAE,IAACC,GAAI,EAAGC,GAAI,YACf,WAACC,EAAAA,CAAIA,CAACC,KAAK,YACP,UAACD,EAAAA,CAAIA,CAACE,KAAK,WAAEzF,EAAE,2CACf,UAAC0F,EAAAA,EAASA,CAAAA,CACNrC,KAAK,WACLsC,GAAG,WACHrC,MAAOvD,QAAAA,EAAAA,EAAWU,WAAAA,GAAXV,OAAAA,EAAAA,CAAwB,CAAC,IAAzBA,KAAAA,EAAAA,EAA6BW,GAA7BX,KAAqC,GAAI,GAChDgG,aAAc/F,EAAE,qDAChBgG,SAAU9C,SAItB,UAAC+B,EAAAA,CAAGA,CAAAA,CAACG,EAAE,IAACC,GAAI,EAAGC,GAAI,YACf,WAACC,EAAAA,CAAIA,CAACC,KAAK,YACP,UAACD,EAAAA,CAAIA,CAACE,KAAK,WAAEzF,EAAE,4CACf,UAAC0F,EAAAA,EAASA,CAAAA,CACNrC,KAAK,YACLsC,GAAG,YACHrC,MAAOvD,QAAAA,EAAAA,EAAWU,WAAAA,GAAXV,OAAAA,EAAAA,CAAwB,CAAxBA,EAAyB,EAAzBA,KAAAA,EAAAA,EAA6BY,GAA7BZ,MAAsC,GAAI,GACjDgG,aAAc/F,EAAE,sDAChBgG,SAAU9C,SAItB,UAAC+B,EAAAA,CAAGA,CAAAA,CAACG,EAAE,IAACC,GAAI,EAAGC,GAAI,YACf,UAACpB,MAAAA,CAAIK,MAAO,CAAEC,UAAW,MAAO,WAC5B,WAACyB,EAAAA,CAAMA,CAAAA,CAACC,QAAQ,YAAYC,QAAS,IAAMhF,EAAgB,CAACV,aACvD,IACD,UAAC2F,EAAAA,CAAeA,CAAAA,CAACC,KAAMC,EAAAA,GAAcA,CAAElC,UAAU,SAChDpE,EAAE,0DAMnB,UAACkE,MAAAA,CAAIE,UAAU,gBACX,WAAC6B,EAAAA,CAAMA,CAAAA,CAACC,QAAQ,YAAYC,QAAS,IAAMhF,EAAgB,CAACV,aACvD,IACD,UAAC2F,EAAAA,CAAeA,CAAAA,CAACC,KAAMC,EAAAA,GAAcA,CAAElC,UAAU,SAChDpE,EAAE,wDAIf,WAACgF,EAAAA,CAAGA,CAAAA,CAACZ,UAAU,iBACX,UAACa,EAAAA,CAAGA,CAAAA,CAACG,EAAE,IAACC,GAAI,EAAGC,GAAI,YACf,WAACC,EAAAA,CAAIA,CAACC,KAAK,YACP,UAACD,EAAAA,CAAIA,CAACE,KAAK,EAACrB,UAAU,0BACjBpE,EAAE,8CAEP,WAACuG,EAAAA,EAAWA,CAAAA,CACRlD,KAAK,eACLsC,GAAG,eACHrC,MAAOvD,EAAWa,YAAY,CAC9BmF,aAAc,CAAEF,UAAW7F,EAAE,uDAAuD,EACpF4F,QAAQ,IACRI,SAAU9C,YAEV,UAACsD,SAAAA,CAAOlD,MAAM,YACTtD,EAAE,oDAENiB,EAAYwF,MAAM,EAAI,EACjBxF,EAAYyF,GAAG,CAAC,CAACC,EAAMC,IAEf,UAACJ,SAAAA,CAAsBlD,MAAOqD,EAAKvG,GAAG,UACjCuG,EAAKtG,KAAK,EADFsG,EAAKvG,GAAG,GAK7B,aAIlB,UAAC6E,EAAAA,CAAGA,CAAAA,CAACG,EAAE,IAACC,GAAI,EAAGC,GAAI,YACf,WAACC,EAAAA,CAAIA,CAACC,KAAK,YACP,UAACD,EAAAA,CAAIA,CAACE,KAAK,WAAEzF,EAAE,gDACf,UAAC0F,EAAAA,EAASA,CAAAA,CACNrC,KAAK,iBACLsC,GAAG,iBACHrC,MAAOvD,EAAWc,cAAc,CAChCkF,aAAc/F,EAAE,0DAChBgG,SAAU9C,SAItB,UAAC+B,EAAAA,CAAGA,CAAAA,CAACG,EAAE,IAACC,GAAI,EAAGC,GAAI,YACf,WAACC,EAAAA,CAAIA,CAACC,KAAK,YACP,UAACD,EAAAA,CAAIA,CAACE,KAAK,WAAEzF,EAAE,iDACf,UAAC0F,EAAAA,EAASA,CAAAA,CACNrC,KAAK,kBACLsC,GAAG,kBACHrC,MAAOvD,EAAWe,eAAe,CACjCiF,aAAc/F,EAAE,2DAChBgG,SAAU9C,YAK1B,UAAC8B,EAAAA,CAAGA,CAAAA,CAACZ,UAAU,gBACX,WAACa,EAAAA,CAAGA,CAAAA,WACA,UAACgB,EAAAA,CAAMA,CAAAA,CAAC7B,UAAU,OAAOyC,KAAK,SAASX,QAAQ,mBAC1ClG,EAAE,yCAEP,UAACiG,EAAAA,CAAMA,CAAAA,CAAC7B,UAAU,OAAO+B,QA5RpC,CA4R6CW,IA3R9D/F,EAAcZ,GAEd4G,OAAOC,QAAQ,CAAC,EAAG,EACvB,EAwRgFd,QAAQ,gBACnDlG,EAAE,wCAEP,UAACiH,IAAIA,CACDC,KAAK,6BACLC,GAAK,OAFJF,2BAID,UAAChB,EAAAA,CAAMA,CAAAA,CAACC,QAAQ,qBAAalG,EAAE,0DAUvE", "sources": ["webpack://_N_E/./pages/adminsettings/country/form.tsx"], "sourcesContent": ["//Import Library\r\nimport { useRef, useState, useEffect } from \"react\";\r\nimport { Container, Card, Row, Col, Form, Button } from \"react-bootstrap\";\r\nimport ValidationFormWrapper from \"../../../components/common/ValidationFormWrapper\";\r\nimport { TextInput, SelectGroup } from \"../../../components/common/FormValidation\";\r\nimport toast from 'react-hot-toast';\r\nimport Router from \"next/router\";\r\nimport Link from \"next/link\";\r\nimport { FontAwesomeIcon } from \"@fortawesome/react-fontawesome\";\r\nimport { faMapMarkerAlt } from \"@fortawesome/free-solid-svg-icons\";\r\n\r\n//Import services/components\r\nimport { useTranslation } from 'next-i18next';\r\nimport apiService from \"../../../services/apiService\";\r\nimport { Country, WorldRegion, Region, ApiResponse } from \"../../../types\";\r\n\r\ninterface CountryFormProps {\r\n    [key: string]: any;\r\n}\r\n\r\nconst CountryForm = (props: CountryFormProps) => {\r\n    const { t } = useTranslation('common');\r\n    const _initialCountry = {\r\n        _id: \"\",\r\n        title: \"\",\r\n        code: \"\",\r\n        code3: \"\",\r\n        dial_code: \"\",\r\n        coordinates: [\r\n            {\r\n                latitude: \"\",\r\n                longitude: \"\",\r\n            },\r\n        ],\r\n        world_region: \"\",\r\n        health_profile: \"\",\r\n        security_advice: \"\",\r\n    };\r\n\r\n    const [initialVal, setInitialVal] = useState<Country>(_initialCountry);\r\n    const [worldregion, setworldRegion] = useState<WorldRegion[]>([]);\r\n    const [coordinates, showCoordinates] = useState<boolean>(false);\r\n\r\n    const editform =\r\n        props.routes && (props.routes[0] === t(\"adminsetting.Countries.Forms.edit_country\") ||\r\n        \"edit_land\" === t(\"adminsetting.Countries.Forms.edit_country\")) && props.routes[1];\r\n\r\n    const formRef = useRef(null);\r\n\r\n    const resetHandler = () => {\r\n        setInitialVal(_initialCountry);\r\n        // Reset validation state (Formik handles this automatically)\r\n        window.scrollTo(0, 0);\r\n    };\r\n\r\n    const handleSubmit = async (event: any, values?: Country | any) => {\r\n        event.preventDefault();\r\n        // Use Formik values if available, otherwise fall back to initialVal\r\n        const formValues = values || initialVal;\r\n        const newTitle = formValues.title.charAt(0).toUpperCase() + formValues.title.slice(1);\r\n        const obj = {\r\n            title: newTitle?.trim(),\r\n            title_de: newTitle?.trim(),\r\n            code: formValues.code?.trim(),\r\n            code3: formValues.code3,\r\n            dial_code: formValues.dial_code,\r\n            first_letter: {\r\n                en: formValues.title.charAt(0).toUpperCase(),\r\n                fr: formValues.title.charAt(0).toUpperCase(),\r\n                de: formValues.title.charAt(0).toUpperCase(),\r\n            },\r\n            coordinates: formValues.coordinates,\r\n            world_region: formValues.world_region,\r\n            health_profile: formValues.health_profile,\r\n            security_advice: formValues.security_advice,\r\n        };\r\n\r\n        let response;\r\n        let toastMsg;\r\n        if (editform) {\r\n            toastMsg = \"adminsetting.Countries.Forms.Countryisupdatedsuccessfully\";\r\n            response = await apiService.patch(`/country/${props.routes[1]}`, obj);\r\n        } else {\r\n            toastMsg = \"adminsetting.Countries.Forms.Countryisaddedsuccessfully\";\r\n            response = await apiService.post(\"/country\", obj);\r\n        }\r\n        if (response && response._id) {\r\n            toast.success(t(toastMsg));\r\n            Router.push(\"/adminsettings/country\");\r\n        } else {\r\n            if (response?.errorCode === 11000) {\r\n                toast.error(t(\"duplicatesNotAllowed\"));\r\n            } else {\r\n                toast.error(response);\r\n            }\r\n        }\r\n    };\r\n\r\n    const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement>) => {\r\n        if (e.target) {\r\n            const { name, value } = e.target;\r\n            if (name === \"longitude\" || name === \"latitude\") {\r\n                setInitialVal((prevState) => ({\r\n                    ...prevState,\r\n                    coordinates: [{ ...initialVal.coordinates?.[0], [name]: value }],\r\n                }));\r\n            } else {\r\n                setInitialVal((prevState) => ({\r\n                    ...prevState,\r\n                    [name]: value,\r\n                }));\r\n            }\r\n        }\r\n    };\r\n\r\n    const getworldregion = async (countriesParams: any) => {\r\n        const response: ApiResponse<WorldRegion[]> = await apiService.get(\"/worldregion\", countriesParams);\r\n        if (response) {\r\n            setworldRegion(response.data);\r\n        }\r\n    };\r\n\r\n    useEffect(() => {\r\n        const countriesParams = {\r\n            query: {},\r\n            sort: { title: \"asc\" },\r\n            limit: \"~\",\r\n        };\r\n\r\n        if (editform) {\r\n            const getCountryData = async (countriesParams_initial: any) => {\r\n                const response: Country = await apiService.get(`/country/${props.routes[1]}`, countriesParams_initial);\r\n                if (response) {\r\n                    const { world_region } = response;\r\n                    response.world_region = world_region && (world_region as any)._id ? (world_region as any)._id : \"\";\r\n                    setInitialVal((prevState) => ({ ...prevState, ...response }));\r\n                }\r\n            };\r\n            getCountryData(countriesParams);\r\n        }\r\n\r\n        getworldregion(countriesParams);\r\n    }, []);\r\n\r\n    return (\r\n        <div>\r\n            <Container className=\"formCard\" fluid>\r\n                <Card\r\n                    style={{\r\n                        marginTop: \"5px\",\r\n                        boxShadow: \"0 4px 8px 0 rgba(0, 0, 0, 0.2), 0 6px 20px 0 rgba(0, 0, 0, 0.19)\",\r\n                    }}\r\n                >\r\n                    <ValidationFormWrapper onSubmit={handleSubmit} ref={formRef} initialValues={initialVal} enableReinitialize={true}>\r\n                        <Card.Body>\r\n                            <Row>\r\n                                <Col>\r\n                                    <Card.Title>{editform ? t(\"adminsetting.Countries.Forms.EditCountry\") : t(\"adminsetting.Countries.Forms.AddCountry\")}</Card.Title>\r\n                                </Col>\r\n                            </Row>\r\n                            <hr />\r\n                            <Row className=\"mb-3\">\r\n                                <Col md lg={12} sm={12}>\r\n                                    <Form.Group>\r\n                                        <Form.Label className=\"required-field\">\r\n                                            {t(\"adminsetting.Countries.Forms.CountryName\")}\r\n                                        </Form.Label>\r\n                                        <TextInput\r\n                                            name=\"title\"\r\n                                            id=\"title\"\r\n                                            required\r\n                                            value={initialVal.title}\r\n                                            validator={(value: any) => String(value || '').trim() !== \"\"}\r\n                                            errorMessage={{\r\n                                                validator: t(\"adminsetting.Countries.Forms.PleaseAddtheCountryName\"),\r\n                                            }}\r\n                                            onChange={handleChange}\r\n                                        />\r\n                                    </Form.Group>\r\n                                </Col>\r\n                                </Row>\r\n                                <Row className=\"mb-3\">\r\n                                <Col md lg={4} sm={12}>\r\n                                    <Form.Group>\r\n                                        <Form.Label className=\"required-field\">\r\n                                            {t(\"adminsetting.Countries.Forms.CountryCode\")}\r\n                                        </Form.Label>\r\n                                        <TextInput\r\n                                            name=\"code\"\r\n                                            id=\"code\"\r\n                                            required\r\n                                            value={initialVal.code}\r\n                                            errorMessage={{validator: t(\"adminsetting.Countries.Forms.PleaseAddtheCountryCode\")}}\r\n                                            onChange={handleChange}\r\n                                        />\r\n                                    </Form.Group>\r\n                                </Col>\r\n                                <Col md lg={4} sm={12}>\r\n                                    <Form.Group>\r\n                                        <Form.Label>{t(\"adminsetting.Countries.Forms.CountryCode3\")}</Form.Label>\r\n                                        <TextInput\r\n                                            name=\"code3\"\r\n                                            id=\"code3\"\r\n                                            value={initialVal.code3}\r\n                                            errorMessage={t(\"adminsetting.Countries.Forms.PleaseAddtheCountryCode3\")}\r\n                                            onChange={handleChange}\r\n                                        />\r\n                                    </Form.Group>\r\n                                </Col>\r\n\r\n                                <Col md lg={4} sm={12}>\r\n                                    <Form.Group>\r\n                                        <Form.Label>{t(\"adminsetting.Countries.Forms.DialCode\")} </Form.Label>\r\n                                        <TextInput\r\n                                            name=\"dial_code\"\r\n                                            id=\"dial code\"\r\n                                            value={initialVal.dial_code}\r\n                                            errorMessage={t(\"adminsetting.Countries.Forms.PleaseAddtheCountryDialCode\")}\r\n                                            onChange={handleChange}\r\n                                        />\r\n                                    </Form.Group>\r\n                                </Col>\r\n                            </Row>\r\n                            <Row className=\"mb-3\">\r\n                                <Col>\r\n                                    <Card.Title>{t(\"adminsetting.Countries.Forms.Co-ordinates\")}</Card.Title>\r\n                                </Col>\r\n                            </Row>\r\n\r\n                            {coordinates ? (\r\n                                <Row className=\"mb-3\">\r\n                                    <Col md lg={4} sm={12}>\r\n                                        <Form.Group>\r\n                                            <Form.Label>{t(\"adminsetting.Countries.Forms.Latitude\")}</Form.Label>\r\n                                            <TextInput\r\n                                                name=\"latitude\"\r\n                                                id=\"latitude\"\r\n                                                value={initialVal.coordinates?.[0]?.latitude || \"\"}\r\n                                                errorMessage={t(\"adminsetting.Countries.Forms.PleaseAddtheLatitude\")}\r\n                                                onChange={handleChange}\r\n                                            />\r\n                                        </Form.Group>\r\n                                    </Col>\r\n                                    <Col md lg={4} sm={12}>\r\n                                        <Form.Group>\r\n                                            <Form.Label>{t(\"adminsetting.Countries.Forms.Longitude\")}</Form.Label>\r\n                                            <TextInput\r\n                                                name=\"longitude\"\r\n                                                id=\"longitude\"\r\n                                                value={initialVal.coordinates?.[0]?.longitude || \"\"}\r\n                                                errorMessage={t(\"adminsetting.Countries.Forms.PleaseAddtheLongitude\")}\r\n                                                onChange={handleChange}\r\n                                            />\r\n                                        </Form.Group>\r\n                                    </Col>\r\n                                    <Col md lg={4} sm={12}>\r\n                                        <div style={{ marginTop: \"30px\" }}>\r\n                                            <Button variant=\"secondary\" onClick={() => showCoordinates(!coordinates)}>\r\n                                                {\" \"}\r\n                                                <FontAwesomeIcon icon={faMapMarkerAlt} className=\"me-2\" />\r\n                                                {t(\"adminsetting.Countries.Forms.HideCoordinates\")}\r\n                                            </Button>\r\n                                        </div>\r\n                                    </Col>\r\n                                </Row>\r\n                            ) : (\r\n                                <div className=\"mb-3\">\r\n                                    <Button variant=\"secondary\" onClick={() => showCoordinates(!coordinates)}>\r\n                                        {\" \"}\r\n                                        <FontAwesomeIcon icon={faMapMarkerAlt} className=\"me-2\" />\r\n                                        {t(\"adminsetting.Countries.Forms.coordinatesBtnText\")}\r\n                                    </Button>\r\n                                </div>\r\n                            )}\r\n                            <Row className=\"mb-3\">\r\n                                <Col md lg={4} sm={12}>\r\n                                    <Form.Group>\r\n                                        <Form.Label className=\"required-field\">\r\n                                            {t(\"adminsetting.Countries.Forms.WorldRegion\")}\r\n                                        </Form.Label>\r\n                                        <SelectGroup\r\n                                            name=\"world_region\"\r\n                                            id=\"world_region\"\r\n                                            value={initialVal.world_region}\r\n                                            errorMessage={{ validator: t(\"adminsetting.Countries.Forms.PleaseAddtheWorldRegion\")}}\r\n                                            required\r\n                                            onChange={handleChange}\r\n                                        >\r\n                                            <option value=\"\">\r\n                                                {t(\"adminsetting.Countries.Forms.SelectWorldRegion\")}\r\n                                            </option>\r\n                                            {worldregion.length >= 1\r\n                                                ? worldregion.map((item, _i) => {\r\n                                                      return (\r\n                                                          <option key={item._id} value={item._id}>\r\n                                                              {item.title}\r\n                                                          </option>\r\n                                                      );\r\n                                                  })\r\n                                                : null}\r\n                                        </SelectGroup>\r\n                                    </Form.Group>\r\n                                </Col>\r\n                                <Col md lg={4} sm={12}>\r\n                                    <Form.Group>\r\n                                        <Form.Label>{t(\"adminsetting.Countries.Forms.Healthprofile\")}</Form.Label>\r\n                                        <TextInput\r\n                                            name=\"health_profile\"\r\n                                            id=\"health_profile\"\r\n                                            value={initialVal.health_profile}\r\n                                            errorMessage={t(\"adminsetting.Countries.Forms.PleaseAddtheHealthProfile\")}\r\n                                            onChange={handleChange}\r\n                                        />\r\n                                    </Form.Group>\r\n                                </Col>\r\n                                <Col md lg={4} sm={12}>\r\n                                    <Form.Group>\r\n                                        <Form.Label>{t(\"adminsetting.Countries.Forms.SecurityAdvice\")}</Form.Label>\r\n                                        <TextInput\r\n                                            name=\"security_advice\"\r\n                                            id=\"security_advice\"\r\n                                            value={initialVal.security_advice}\r\n                                            errorMessage={t(\"adminsetting.Countries.Forms.PleaseAddtheSecurityAdvice\")}\r\n                                            onChange={handleChange}\r\n                                        />\r\n                                    </Form.Group>\r\n                                </Col>\r\n                            </Row>\r\n                            <Row className=\"my-4\">\r\n                                <Col>\r\n                                    <Button className=\"me-2\" type=\"submit\" variant=\"primary\">\r\n                                        {t(\"adminsetting.Countries.Forms.Submit\")}\r\n                                    </Button>\r\n                                    <Button className=\"me-2\" onClick={resetHandler} variant=\"info\">\r\n                                        {t(\"adminsetting.Countries.Forms.Reset\")}\r\n                                    </Button>\r\n                                    <Link\r\n                                        href=\"/adminsettings/[...routes]\"\r\n                                        as={`/adminsettings/country`}\r\n                                        >\r\n                                        <Button variant=\"secondary\">{t(\"adminsetting.Countries.Forms.Cancel\")}</Button>\r\n                                    </Link>\r\n                                </Col>\r\n                            </Row>\r\n                        </Card.Body>\r\n                    </ValidationFormWrapper>\r\n                </Card>\r\n            </Container>\r\n        </div>\r\n    );\r\n};\r\n\r\nexport default CountryForm;\r\n"], "names": ["props", "initialVal", "t", "useTranslation", "CountryForm", "_initialCountry", "_id", "title", "code", "code3", "dial_code", "coordinates", "latitude", "longitude", "world_region", "health_profile", "security_advice", "setInitialVal", "useState", "worldregion", "setworldRegion", "showCoordinates", "editform", "routes", "formRef", "useRef", "handleSubmit", "event", "values", "formValues", "response", "toastMsg", "preventDefault", "newTitle", "char<PERSON>t", "toUpperCase", "slice", "obj", "trim", "title_de", "first_letter", "en", "fr", "de", "apiService", "patch", "post", "toast", "success", "Router", "errorCode", "error", "handleChange", "e", "target", "name", "value", "prevState", "getworldregion", "countriesParams", "get", "data", "useEffect", "query", "sort", "limit", "getCountryData", "countriesParams_initial", "div", "Container", "className", "fluid", "Card", "style", "marginTop", "boxShadow", "ValidationFormWrapper", "onSubmit", "ref", "initialValues", "enableReinitialize", "Body", "Row", "Col", "Title", "hr", "md", "lg", "sm", "Form", "Group", "Label", "TextInput", "id", "required", "validator", "String", "errorMessage", "onChange", "<PERSON><PERSON>", "variant", "onClick", "FontAwesomeIcon", "icon", "faMapMarkerAlt", "SelectGroup", "option", "length", "map", "item", "_i", "type", "re<PERSON><PERSON><PERSON><PERSON>", "window", "scrollTo", "Link", "href", "as"], "sourceRoot": "", "ignoreList": []}