{"version": 3, "file": "static/chunks/pages/event/components/RiskAssessmentAccordion-e7dbf1546fef56d4.js", "mappings": "0MASA,IAAMA,EAAa,CACfC,IAAK,QACLC,OAAQ,QACRC,KAAM,QACN,YAAa,OACjB,EA4CA,EAjCgC,IAC5B,GAAM,GAAEC,CAAC,CAAE,CAAGC,CAAAA,EAAAA,EAAAA,EAAAA,CAAcA,CAAC,MAgClBC,IA/BL,CAACC,EAASC,EAAW,CAAGC,CAAAA,EAAAA,EAAAA,QA+BIH,CA/BIG,CA+BH,CA/BI,GAEjC,iBAAEC,CAAe,CAAE,CAAGC,EAE5B,MACI,+BACI,WAACC,EAAAA,CAASA,CAACC,IAAI,EAACC,SAAS,cACrB,WAACF,EAAAA,CAASA,CAACG,MAAM,EAACC,QAAS,IAAMR,EAAW,CAACD,aACzC,UAACU,MAAAA,CAAIC,UAAU,qBAAad,EAAE,gCAC9B,UAACa,MAAAA,CAAIC,UAAU,qBACVX,EACG,UAACY,EAAAA,CAAeA,CAAAA,CAACC,KAAMC,EAAAA,GAAOA,CAAEC,MAAM,SAEtC,UAACH,EAAAA,CAAeA,CAAAA,CAACC,KAAMG,EAAAA,GAAMA,CAAED,MAAM,cAIjD,UAACV,EAAAA,CAASA,CAACY,IAAI,WACVd,EAAgBe,OAAO,CACpB,WAACR,MAAAA,WACIS,SAyBpBA,CAAyC,CAAEtB,CAA0B,EAC1E,MACI,WAACa,MAAAA,CAAIC,UAAU,wBACVR,EAAgBe,OAAO,CACpB,WAACR,MAAAA,CAAIC,UAAU,sBACX,UAACD,MAAAA,CAAIC,UAAW,YAAiD,OAArClB,CAAK,CAACU,EAAgBe,OAAO,CAACE,KAAK,CAAC,WAC5D,UAACC,MAAAA,CAAIC,IAAI,4BAA4BC,MAAM,KAAKC,OAAO,KAAKC,IAAI,2BAEpE,WAACf,MAAAA,CAAIC,UAAU,qBACX,UAACe,KAAAA,UAAI7B,EAAE,yBACP,UAAC8B,KAAAA,UAAIxB,EAAgBe,OAAO,CAACE,KAAK,SAI1C,yBAEHjB,EAAgByB,MAAM,CACnB,WAAClB,MAAAA,CAAIC,UAAU,sBACX,UAACD,MAAAA,CACGC,UAAW,YAEV,OADGR,GAAmBA,EAAgByB,MAAM,CAAGnC,CAAK,CAACU,EAAgByB,MAAM,CAACR,KAAK,CAAC,CAAG,aAGtF,UAACC,MAAAA,CAAIC,IAAI,2BAA2BC,MAAM,KAAKC,OAAO,KAAKC,IAAI,2BAEnE,WAACf,MAAAA,CAAIC,UAAU,qBACX,UAACe,KAAAA,UAAI7B,EAAE,wBACP,UAAC8B,KAAAA,UAAIxB,GAAmBA,EAAgByB,MAAM,CAAGzB,EAAgByB,MAAM,CAACR,KAAK,CAAG,WAIxF,yBAEHjB,EAAgB0B,aAAa,CAC1B,WAACnB,MAAAA,CAAIC,UAAU,sBACX,UAACD,MAAAA,CACGC,UAAW,YAIV,OAHGR,GAAmBA,EAAgB0B,aAAa,CAC1CpC,CAAK,CAACU,EAAgB0B,aAAa,CAACT,KAAK,CAAC,CAC1C,aAGV,UAACC,MAAAA,CAAIC,IAAI,kCAAkCC,MAAM,KAAKC,OAAO,KAAKC,IAAI,2BAE1E,WAACf,MAAAA,CAAIC,UAAU,qBACX,UAACe,KAAAA,UAAI7B,EAAE,+BACP,UAAC8B,KAAAA,UACIxB,GAAmBA,EAAgB0B,aAAa,CAC3C1B,EAAgB0B,aAAa,CAACT,KAAK,CACnC,WAKlB,2BAIhB,EAnFkDjB,EAAiBN,GAEtCiC,SAWpBA,CAAmC,EACxC,MACI,UAACpB,MAAAA,CAAIC,UAAU,gBACX,UAACoB,EAAAA,CAAiBA,CAAAA,CACdC,YACIC,GAAaA,EAAU9B,eAAe,CAAC6B,WAAW,CAAGC,EAAU9B,eAAe,CAAC6B,WAAW,CAAG,MAKjH,EArBkD5B,MAE1B,WAKxB,sFChBA,MA9B0B,IACxB,GAAM,GAAEP,CAAC,CAAE,CAAGC,CAAAA,EAAAA,EAAAA,EAAAA,CAAcA,CAAC,MA6BhBiC,IA5BPG,EAAiBC,SAASC,EA4BFL,EAAC,CA5B6B,EACtD,CAACM,CADyD,CAAK,EACpC,CAAGnC,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,EAAC,GAO7C,MACE,iCAEIE,EAAM4B,WAAW,CACjB,UAACtB,MAAAA,CACC4B,wBAAyBC,CAVZ,CAACC,EAAqBC,KAElC,CAAEC,OADe,CAAED,GAAqBD,EAAYG,MAAM,CAAGT,EAAkBM,EAAYI,SAAS,CAAC,EAAGV,GAAkB,MAAQ9B,EAAM4B,WAAW,CACzH,CACnC,EAO8C5B,EAAM4B,WAAW,CAACK,GACxD1B,UAAU,kBAEH,KAGTP,EAAM4B,WAAW,EAAI5B,EAAM4B,WAAW,CAACW,MAAM,CAAGT,EAC9C,UAACW,SAAAA,CAAOC,KAAK,SAASnC,UAAU,eAAeF,QAAS,IAAMsC,EAAc,CAACV,YAChExC,EAAbwC,EAAe,WAAgB,GAAFxC,WACjB,OAItB,mBCrCA,4CACA,4CACA,WACA,OAAe,EAAQ,GAAiE,CACxF,EACA,WAFsB", "sources": ["webpack://_N_E/./pages/event/components/RiskAssessmentAccordion.tsx", "webpack://_N_E/./components/common/readMore/readMore.tsx", "webpack://_N_E/?69a0"], "sourcesContent": ["//Import Library\r\nimport React, { useEffect, useState } from \"react\";\r\nimport { faMinus, faPlus } from \"@fortawesome/free-solid-svg-icons\";\r\nimport { FontAwesomeIcon } from \"@fortawesome/react-fontawesome\";\r\nimport { Accordion, Card } from \"react-bootstrap\";\r\n\r\n//Import services/components\r\nimport { useTranslation } from 'next-i18next';\r\nimport ReadMoreContainer from \"../../../components/common/readMore/readMore\";\r\nconst icons: any = {\r\n    Low: \"risk0\",\r\n    Medium: \"risk1\",\r\n    High: \"risk2\",\r\n    \"Very High\": \"risk3\",\r\n};\r\n\r\ninterface RiskAssessmentAccordionProps {\r\n  risk_assessment: {\r\n    country?: {\r\n      title: string;\r\n    };\r\n    description?: string;\r\n  };\r\n}\r\n\r\nconst RiskAssessmentAccordion = (props: RiskAssessmentAccordionProps) => {\r\n    const { t } = useTranslation('common');\r\n    const [section, setSection] = useState(false);\r\n\r\n    const { risk_assessment } = props;\r\n\r\n    return (\r\n        <>\r\n            <Accordion.Item eventKey=\"0\">\r\n                <Accordion.Header onClick={() => setSection(!section)}>\r\n                    <div className=\"cardTitle\">{t(\"Events.show.RiskAssessment\")}</div>\r\n                    <div className=\"cardArrow\">\r\n                        {section ? (\r\n                            <FontAwesomeIcon icon={faMinus} color=\"#fff\" />\r\n                        ) : (\r\n                            <FontAwesomeIcon icon={faPlus} color=\"#fff\" />\r\n                        )}\r\n                    </div>\r\n                </Accordion.Header>\r\n                <Accordion.Body>\r\n                    {risk_assessment.country ? (\r\n                        <div>\r\n                            {risk_assessment_Func(risk_assessment, t)}\r\n\r\n                            {risk_assessment_func(props)}\r\n                        </div>\r\n                    ) : null}\r\n                </Accordion.Body>\r\n            </Accordion.Item>\r\n        </>\r\n    )\r\n};\r\n\r\nexport default RiskAssessmentAccordion;\r\n\r\nfunction risk_assessment_func(eventData: any) {\r\n    return (\r\n        <div className=\"mt-4\">\r\n            <ReadMoreContainer\r\n                description={\r\n                    eventData && eventData.risk_assessment.description ? eventData.risk_assessment.description : \"\"\r\n                }\r\n            />\r\n        </div>\r\n    );\r\n}\r\n\r\nfunction risk_assessment_Func(risk_assessment: any, t: (key: string) => string) {\r\n    return (\r\n        <div className=\"riskDetails\">\r\n            {risk_assessment.country ? (\r\n                <div className=\"riskItems\">\r\n                    <div className={`riskIcon ${icons[risk_assessment.country.title]}`}>\r\n                        <img src=\"/images/event_country.png\" width=\"30\" height=\"30\" alt=\"Risk Assessment Info\" />\r\n                    </div>\r\n                    <div className=\"riskInfo\">\r\n                        <h5>{t(\"Events.show.Country\")}</h5>\r\n                        <h4>{risk_assessment.country.title}</h4>\r\n                    </div>\r\n                </div>\r\n            ) : (\r\n                <></>\r\n            )}\r\n            {risk_assessment.region ? (\r\n                <div className=\"riskItems\">\r\n                    <div\r\n                        className={`riskIcon ${\r\n                            risk_assessment && risk_assessment.region ? icons[risk_assessment.region.title] : \"\"\r\n                        }`}\r\n                    >\r\n                        <img src=\"/images/event_region.png\" width=\"35\" height=\"26\" alt=\"Risk Assessment Info\" />\r\n                    </div>\r\n                    <div className=\"riskInfo\">\r\n                        <h5>{t(\"Events.show.Region\")}</h5>\r\n                        <h4>{risk_assessment && risk_assessment.region ? risk_assessment.region.title : \"\"}</h4>\r\n                    </div>\r\n                </div>\r\n            ) : (\r\n                <></>\r\n            )}\r\n            {risk_assessment.international ? (\r\n                <div className=\"riskItems\">\r\n                    <div\r\n                        className={`riskIcon ${\r\n                            risk_assessment && risk_assessment.international\r\n                                ? icons[risk_assessment.international.title]\r\n                                : \"\"\r\n                        }`}\r\n                    >\r\n                        <img src=\"/images/event_international.png\" width=\"38\" height=\"38\" alt=\"Risk Assessment Info\" />\r\n                    </div>\r\n                    <div className=\"riskInfo\">\r\n                        <h5>{t(\"Events.show.International\")}</h5>\r\n                        <h4>\r\n                            {risk_assessment && risk_assessment.international\r\n                                ? risk_assessment.international.title\r\n                                : \"\"}\r\n                        </h4>\r\n                    </div>\r\n                </div>\r\n            ) : (\r\n                <></>\r\n            )}\r\n        </div>\r\n    );\r\n}\r\n", "//Import Library\r\nimport { useState } from \"react\";\r\n\r\n//Import services/components\r\nimport { useTranslation } from 'next-i18next';\r\n\r\ninterface ReadMoreContainerProps {\r\n  description: string;\r\n}\r\n\r\nconst ReadMoreContainer = (props: ReadMoreContainerProps) => {\r\n  const { t } = useTranslation('common');\r\n  const readMoreLength = parseInt(process.env.READ_MORE_LENGTH || '200');\r\n  const [isReadMore, setIsReadMore] = useState(false);\r\n\r\n  const createMarkup = (htmlContent: string, isReadMoreInitial: boolean) => {\r\n    const truncateContent = (!isReadMoreInitial && htmlContent.length > readMoreLength) ? htmlContent.substring(0, readMoreLength) + \"...\" : props.description;\r\n    return { __html: truncateContent };\r\n  };\r\n\r\n  return (\r\n    <>\r\n      {\r\n        props.description  ?\r\n        <div\r\n          dangerouslySetInnerHTML={createMarkup(props.description,isReadMore)}\r\n          className=\"operationDesc\"\r\n        >\r\n        </div> : null\r\n      }\r\n      {\r\n        props.description && props.description.length > readMoreLength ?\r\n          <button type=\"button\" className=\"readMoreText\" onClick={() => setIsReadMore(!isReadMore)}>\r\n         {isReadMore ? t(\"readLess\") : t(\"readMore\")}\r\n          </button> : null\r\n      }\r\n    </>\r\n  )\r\n}\r\n\r\nexport default ReadMoreContainer;\r\n", "\n    (window.__NEXT_P = window.__NEXT_P || []).push([\n      \"/event/components/RiskAssessmentAccordion\",\n      function () {\n        return require(\"private-next-pages/event/components/RiskAssessmentAccordion.tsx\");\n      }\n    ]);\n    if(module.hot) {\n      module.hot.dispose(function () {\n        window.__NEXT_P.push([\"/event/components/RiskAssessmentAccordion\"])\n      });\n    }\n  "], "names": ["icons", "Low", "Medium", "High", "t", "useTranslation", "RiskAssessmentAccordion", "section", "setSection", "useState", "risk_assessment", "props", "Accordion", "<PERSON><PERSON>", "eventKey", "Header", "onClick", "div", "className", "FontAwesomeIcon", "icon", "faMinus", "color", "faPlus", "Body", "country", "risk_assessment_Func", "title", "img", "src", "width", "height", "alt", "h5", "h4", "region", "international", "risk_assessment_func", "ReadMoreContainer", "description", "eventData", "readMoreLength", "parseInt", "process", "isReadMore", "dangerouslySetInnerHTML", "createMarkup", "htmlContent", "isReadMoreInitial", "__html", "length", "substring", "button", "type", "setIsReadMore"], "sourceRoot": "", "ignoreList": []}