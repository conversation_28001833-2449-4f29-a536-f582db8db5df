# Authentication Session Fix - README

## Problem Description

### What Was Happening?
Your Next.js application was experiencing authentication issues where users would be logged out after refreshing the page in **production mode** (`npm run build` + `npm start`), but authentication worked fine in **development mode** (`npm run dev`).

### Root Cause
The issue was caused by different server configurations between development and production:

- **Development mode (`npm run dev`)**: Uses your custom Express server (`server.ts`) with ts-node
- **Production mode (`npm start`)**: Uses Next.js's built-in server

The authentication system relied on **client-side cookie parsing** in the `AuthSync` component's `getInitialProps` method. In production, the Next.js built-in server doesn't parse cookies the same way, causing `ctx.req.cookies` to be empty, which made the authentication fail.

## Solution Overview

Instead of relying on client-side cookie parsing, we moved the session verification to the **backend** where the session management already exists and works correctly.

### Architecture Change
```
BEFORE (Client-side cookie parsing):
Frontend → Parse cookies → Check session → Authenticate

AFTER (Backend session verification):
Frontend → Call backend API → Backend checks session → Return auth status
```

## Changes Made

### 1. Backend Changes (Essential)

**File: `backend/src/app.controller.ts`**

Added a new endpoint for session verification:

```typescript
@Get('auth/verify-session')
async verifySession(@Request() req) {
  try {
    if (req.session && req.session.passport && req.session.passport.user) {
      const user = req.session.passport.user;
      return {
        isAuthenticated: true,
        user: {
          username: user.username,
          sub: user._id,
          roles: user.roles,
          isEnabled: user.enabled,
        }
      };
    } else {
      return {
        isAuthenticated: false,
        user: null
      };
    }
  } catch (error) {
    return {
      isAuthenticated: false,
      user: null
    };
  }
}
```

**Why this works**: The backend already has proper session management with Passport.js and express-session. The session data is stored in MongoDB and properly managed.

### 2. Frontend Changes (Minimal)

**File: `frontend/services/authService.tsx`**

Added a method to call the backend session verification:

```typescript
verifySession = async () => {
  try {
    const response = await axios.get(
      `${process.env.API_SERVER}/auth/verify-session`,
      { 
        headers: { "Content-Type": application }, 
        withCredentials: true 
      }
    );
    if (response.status === 200 && response.data) {
      return response.data;
    }
    return { isAuthenticated: false, user: null };
  } catch (error: unknown) {
    return { isAuthenticated: false, user: null };
  }
};
```

**File: `frontend/components/hoc/AuthSync.tsx`**

Modified the `componentDidMount` method to use backend verification instead of cookie parsing:

```typescript
async componentDidMount() {
  // ... existing code ...

  // Use backend session verification instead of cookie parsing
  try {
    const sessionData = await authService.verifySession();
    
    if (!sessionData.isAuthenticated && publicRoutes.indexOf(route) === -1) {
      this.props.router.push("/home");
      return;
    }
    
    this.setState({ 
      isLoading: false,
      cookie: sessionData.isAuthenticated ? "authenticated" : null
    });
  } catch (error) {
    // Handle error and redirect if needed
  }
}
```

## For React Developers New to NestJS

### Understanding the Backend (NestJS)

**NestJS** is a Node.js framework similar to Express but with TypeScript and decorators (like Angular). Here's what you need to know:

1. **Controllers** (`@Controller()`): Handle HTTP requests (like React components handle UI)
2. **Routes** (`@Get()`, `@Post()`): Define API endpoints (like React Router routes)
3. **Guards** (`@UseGuards()`): Middleware for authentication (like React route guards)
4. **Services** (`@Injectable()`): Business logic (like React custom hooks or services)

### Session Management in NestJS

Your backend uses:
- **Passport.js**: Authentication middleware
- **express-session**: Session storage
- **MongoDB**: Session persistence
- **Guards**: Route protection

```typescript
// This is like a React route guard
@UseGuards(SessionGuard)
@Get('protected-route')
getProtectedData() {
  // Only authenticated users can access this
}
```

### Why Backend Verification is Better

1. **Security**: Session validation happens server-side
2. **Consistency**: Same session management for all environments
3. **Reliability**: No dependency on client-side cookie parsing
4. **Scalability**: Centralized authentication logic

## Testing the Fix

1. **Build and start the application**:
   ```bash
   npm run build
   npm start
   ```

2. **Test the authentication flow**:
   - Login to the application
   - Refresh the page
   - You should remain logged in (no redirect to login)

3. **Verify in different scenarios**:
   - Fresh browser session
   - After browser restart
   - In incognito mode

## Benefits of This Solution

1. **Minimal Changes**: Only essential modifications were made
2. **Backward Compatible**: Existing functionality remains unchanged
3. **Environment Agnostic**: Works in both development and production
4. **Secure**: Authentication logic stays on the server
5. **Maintainable**: Centralized session management

## Future Considerations

- Consider migrating to JWT tokens for stateless authentication
- Implement refresh token mechanism for better security
- Add session timeout handling
- Consider using Next.js middleware for authentication

## Troubleshooting

If you still experience issues:

1. **Check backend logs**: Ensure the session verification endpoint is working
2. **Verify CORS settings**: Make sure `withCredentials: true` is working
3. **Check network tab**: Verify the API call is being made
4. **Session storage**: Ensure MongoDB connection is stable

This solution maintains your existing architecture while fixing the production authentication issue with minimal code changes.
