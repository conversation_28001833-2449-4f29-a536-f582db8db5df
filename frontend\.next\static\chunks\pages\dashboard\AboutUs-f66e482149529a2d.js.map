{"version": 3, "file": "static/chunks/pages/dashboard/AboutUs-f66e482149529a2d.js", "mappings": "qLA8FA,MAtFgB,KACd,GAAM,MAAEA,CAAI,CAAE,CAAGC,CAAAA,EAAAA,EAAAA,EAAAA,CAAcA,CAAC,EAqFnBC,OAAOA,CApFAF,CAoFC,CApFIG,QAAQ,EAAGH,EAAKG,QAAQ,CASjD,EAToD,CAS9C,CAACC,EAASC,EAAW,CAAGC,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAMC,CAP1CC,MAAO,GACPC,YAAa,GACbC,aAAc,GACdC,OAAQ,GACRC,WAAW,CACb,GAGM,CAACC,EAASC,EAAW,CAAGR,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,EAAC,GAKjCS,EAAoB,CACxBC,MAAO,CAAEN,aAAc,EAAG,EAC1BO,KAAM,CAAET,MAAO,KAAM,EACrBU,MAAO,GACT,EAEMC,EAAe,UAInB,IAAMC,EAAsB,MAAMC,IAElC,GADAP,GAAW,GACPM,EAAeE,MAAM,CAAG,EAAG,CAC7BP,EAAkBC,KAAK,CAACN,YAAY,CAAGU,EACvC,IAAMG,EAAW,MAAMC,EAAAA,CAAUA,CAACC,GAAG,CAAC,eAAgBV,GACtD,GAAIW,MAAMC,OAAO,CAACJ,EAASK,IAAI,GAAKL,EAASK,IAAI,CAACN,MAAM,CAAG,EAAG,CAC5D,IAAMO,EAAkBN,EAASK,IAAI,CAACL,EAASK,IAAI,CAACN,MAAM,CAAG,EAAE,CAC/DO,EAAgBlB,MAAM,CACpBY,GACAM,EAAgBlB,MAAM,CAACW,MAAM,CAAG,IACF,IAA9BO,EAAgBjB,SAAS,CACrBiB,EAAgBlB,MAAM,CAACmB,GAAG,CAAC,CAACC,EAAWC,IACrCC,OAAO,GAAwCF,MAAAA,CAArCG,8BAAsB,CAAC,gBAAuB,OAATH,EAAKI,GAAG,IAEzD,mBACFN,EAAgBpB,WAAW,CAC7Bc,GACAM,EAAgBpB,WAAW,CAACa,MAAM,CAAG,IACP,IAA9BO,EAAgBjB,SAAS,CACrBiB,EAAgBpB,WAAW,CArBnC,EAsBQ2B,waACN/B,EAAWwB,GACXR,IACAP,GAAW,EACb,CACF,CACF,EAEMO,EAAoB,UACxB,IAAME,EAAW,MAAMC,EAAAA,CAAUA,CAACC,GAAG,CAAC,gBAAiB,CACrDT,MAAO,CAAER,MAAO,SAAU,CAC5B,SACA,EAAIe,KAAYA,EAASK,IAAI,EAAIL,EAASK,IAAI,CAACN,MAAM,CAAG,GAAG,EACzBM,IAAI,CAAC,EAAE,CAACO,GAI5C,EAEAE,CAAAA,EAAAA,EAAAA,SAAAA,CAASA,CAAC,KACRlB,GACF,EAAG,EAAE,EAEL,IAAMmB,EAAOlC,EAAQK,WAAW,CAAC8B,OAAO,CAAC,WAAY,KAErD,MACE,UAACC,MAAAA,CAAIC,UAAU,oBACA,IAAZ5B,EACC,UAAC6B,EAAAA,CAAeA,CAAAA,CAAAA,GAEhB,WAACF,MAAAA,WACC,UAACG,MAAAA,CAAIF,UAAU,UAAUG,IAAKxC,EAAQO,MAAM,CAAEkC,IAAI,KAClD,UAACL,MAAAA,CAAIM,wBAjEJ,CAAEC,OAiEwCT,CAjEhCU,IAiE8C,QAKnE,kBC3FA,4CACA,qBACA,WACA,OAAe,EAAQ,IAA0C,CACjE,EACA,UAFsB,uDCatB,aAQA,MAPA,8BACA,iCAAiD,IAAO,IAExD,aADA,eACA,uDAEA,QACA,GACA,qBACA,EAoBA,cACA,0UAAwzB,2EAnBxzB,cACA,SACA,0EACA,YACA,4DACA,8CAA6D,WAAc,IAC3E,uEACA,kBAEA,QACA,EASwzB,qOACxzB,KAPA,cACA,aACA,aAMA,YACA,qBACA,YAEA,MAAuB,EANvB,mBAMuB,GAAkB,GACzC,EAPA,mBAOA,IAEA,MAAY,mBAAa,UAAmB,2CAAmE,CAT/G,cAAwzB,GAKxzB,KALA,SAKA,CAA2B,wBAA0B,KAI0D,CAAqB,IACpI,EAAgB,mBAAa,UAAY,KAAY,SACrD,GAAsB,oBAAc,WAC5B,mBAAa,SAAW,8FAA+H,yBAAmD,EAC1M,mBAAa,aACT,mBAAa,aAAe,KAAY,IACxC,mBAAa,mBAAqB,uBAP9C,eARA,4BAQA,mBAO8C,CAAsD,CACpF,mBAAa,SAAW,sCAA0E,IAAc,mBAAa,YAAc,oCAAqD,QAAwB,qDAAkF,GAC1S,mBAAa,SAAW,uBAjBxC,oBAiBwC,YAjBxC,cAiBwC,CAA2E,IAAc,mBAAa,YAAc,sCAAyD,UAA4B,KACjP,uDAA0H,GAC1G,mBAAa,SAAW,wCAA4E,IAAc,mBAAa,YAAc,kCAAqC,GAAG,0DAAuG,KAC5S,EAEA,cACA,kBAA4B,mBAAa,OAAiB,KAAY,mBAAa,OAAwC,IAC3H,EAEA,cAAoD,MAAQ,mBAAa,MAA2B,sBAAwB,IACxH,mBAAa,SAAW,0CAAoD,EAC5E,mBAAa,SAAW,2CAAqD,EAC7E,mBAAa,SAAW,2CAAqD,EAC7E,mBAAa,SAAW,2CAAqD,EAC7E,mBAAa,SAAW,2CAAqD,EAC7E,mBAAa,WAAa,uBAA6B,IAoC3D,MAAe,aAAa,EAAC,4DC/Gd,SAASN,IACtB,MACE,WAACO,EAAAA,EAAaA,CAAAA,CACZC,QAAQ,aACRC,OAAQ,GACRC,MAAO,IACPC,MAAO,EACP7C,MAAO,UACP8C,gBAAgB,UAChBC,gBAAgB,UAChBC,UAAW,sBAEX,UAACC,OAAAA,CAAKC,EAAE,KAAKC,EAAE,IAAIC,GAAG,IAAIC,GAAG,IAAIT,MAAM,MAAMD,OAAO,OACpD,UAACM,OAAAA,CAAKC,EAAE,KAAKC,EAAE,KAAKC,GAAG,IAAIC,GAAG,IAAIT,MAAM,MAAMD,OAAO,SAG3D", "sources": ["webpack://_N_E/./pages/dashboard/AboutUs.tsx", "webpack://_N_E/?cee2", "webpack://_N_E/./node_modules/react-content-loader/dist/react-content-loader.es.js", "webpack://_N_E/./components/common/placeholders/CardPlaceholder.tsx"], "sourcesContent": ["//Import Library\nimport React, { useEffect, useState } from \"react\";\n\n//Import services/components\nimport apiService from \"../../services/apiService\";\nimport CardPlaceholder from \"./../../components/common/placeholders/CardPlaceholder\";\nimport { useTranslation } from 'next-i18next';\n\nconst AboutUs = () => {\n  const { i18n } = useTranslation('common');\n  const currentLang = i18n.language ? i18n.language : \"en\";\n  const _initialVal = {\n    title: \"\",\n    description: \"\",\n    pageCategory: \"\",\n    images: \"\",\n    isEnabled: true,\n  };\n\n  const [aboutUs, setAboutUs] = useState<any>(_initialVal);\n  const [loading, setLoading] = useState(false);\n  const createMarkup = (htmlContent: string) => {\n    return { __html: htmlContent };\n  };\n\n  const landingPageParams = {\n    query: { pageCategory: \"\" },\n    sort: { title: \"asc\" },\n    limit: \"~\",\n  };\n\n  const fetchAboutUs = async () => {\n    const defautDesc =\n      \"The Robert Koch Institut is taking over the coordination of the “WHO AMR Surveillance and Quality Assessment Collaborating Centres Network” this autumn 2019. The network supports the World Health Organization (WHO) to reduce drug-resistant infections globally. It focuses on further developing the global antimicrobial resistance (AMR) surveillance system (GLASS), and promoting exchange and peer support between countries.\";\n\n    const pageCategoryId: any = await fetchPageCategory();\n    setLoading(true);\n    if (pageCategoryId.length > 0) {\n      landingPageParams.query.pageCategory = pageCategoryId;\n      const response = await apiService.get(\"/landingPage\", landingPageParams);\n      if (Array.isArray(response.data) && response.data.length > 0) {\n        const landingPageData = response.data[response.data.length - 1];\n        landingPageData.images =\n          response &&\n          landingPageData.images.length > 0 &&\n          landingPageData.isEnabled === true\n            ? landingPageData.images.map((item: any, _i: any) =>\n                String(`${process.env.API_SERVER}/image/show/${item._id}`)\n              )\n            : \"/images/logo.jpg\";\n            landingPageData.description =\n          response &&\n          landingPageData.description.length > 0 &&\n          landingPageData.isEnabled === true\n            ? landingPageData.description\n            : defautDesc;\n        setAboutUs(landingPageData);\n        fetchPageCategory();\n        setLoading(false);\n      }\n    }\n  };\n\n  const fetchPageCategory = async () => {\n    const response = await apiService.get(\"/pagecategory\", {\n      query: { title: \"AboutUs\" },\n    });\n    if (response && response.data && response.data.length > 0) {\n      const pageCategoryId = response.data[0]._id;\n      return pageCategoryId;\n    }\n    return false;\n  };\n\n  useEffect(() => {\n    fetchAboutUs();\n  }, []);\n\n  const desc = aboutUs.description.replace(/\\&nbsp;/g, \" \");\n\n  return (\n    <div className=\"aboutUs\">\n      {loading === true ? (\n        <CardPlaceholder />\n      ) : (\n        <div>\n          <img className=\"logoImg\" src={aboutUs.images} alt=\"\" />\n          <div dangerouslySetInnerHTML={createMarkup(desc)}></div>{\" \"}\n        </div>\n      )}\n    </div>\n  );\n};\n\nexport default AboutUs;\n", "\n    (window.__NEXT_P = window.__NEXT_P || []).push([\n      \"/dashboard/AboutUs\",\n      function () {\n        return require(\"private-next-pages/dashboard/AboutUs.tsx\");\n      }\n    ]);\n    if(module.hot) {\n      module.hot.dispose(function () {\n        window.__NEXT_P.push([\"/dashboard/AboutUs\"])\n      });\n    }\n  ", "import { createElement, isValidElement } from 'react';\n\n/*! *****************************************************************************\r\nCopyright (c) Microsoft Corporation. All rights reserved.\r\nLicensed under the Apache License, Version 2.0 (the \"License\"); you may not use\r\nthis file except in compliance with the License. You may obtain a copy of the\r\nLicense at http://www.apache.org/licenses/LICENSE-2.0\r\n\r\nTHIS CODE IS PROVIDED ON AN *AS IS* BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\nKIND, EITHER EXPRESS OR IMPLIED, INCLUDING WITHOUT LIMITATION ANY IMPLIED\r\nWARRANTIES OR CONDITIONS OF TITLE, FITNESS FOR A PARTICULAR PURPOSE,\r\nMERCHANTABLITY OR NON-INFRINGEMENT.\r\n\r\nSee the Apache Version 2.0 License for specific language governing permissions\r\nand limitations under the License.\r\n***************************************************************************** */\r\n\r\nvar __assign = function() {\r\n    __assign = Object.assign || function __assign(t) {\r\n        for (var s, i = 1, n = arguments.length; i < n; i++) {\r\n            s = arguments[i];\r\n            for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p)) t[p] = s[p];\r\n        }\r\n        return t;\r\n    };\r\n    return __assign.apply(this, arguments);\r\n};\r\n\r\nfunction __rest(s, e) {\r\n    var t = {};\r\n    for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0)\r\n        t[p] = s[p];\r\n    if (s != null && typeof Object.getOwnPropertySymbols === \"function\")\r\n        for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {\r\n            if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i]))\r\n                t[p[i]] = s[p[i]];\r\n        }\r\n    return t;\r\n}\n\nvar uid = (function () {\r\n    return Math.random()\r\n        .toString(36)\r\n        .substring(6);\r\n});\n\nvar SVG = function (_a) {\r\n    var _b = _a.animate, animate = _b === void 0 ? true : _b, animateBegin = _a.animateBegin, _c = _a.backgroundColor, backgroundColor = _c === void 0 ? '#f5f6f7' : _c, _d = _a.backgroundOpacity, backgroundOpacity = _d === void 0 ? 1 : _d, _e = _a.baseUrl, baseUrl = _e === void 0 ? '' : _e, children = _a.children, _f = _a.foregroundColor, foregroundColor = _f === void 0 ? '#eee' : _f, _g = _a.foregroundOpacity, foregroundOpacity = _g === void 0 ? 1 : _g, _h = _a.gradientRatio, gradientRatio = _h === void 0 ? 2 : _h, _j = _a.gradientDirection, gradientDirection = _j === void 0 ? 'left-right' : _j, uniqueKey = _a.uniqueKey, _k = _a.interval, interval = _k === void 0 ? 0.25 : _k, _l = _a.rtl, rtl = _l === void 0 ? false : _l, _m = _a.speed, speed = _m === void 0 ? 1.2 : _m, _o = _a.style, style = _o === void 0 ? {} : _o, _p = _a.title, title = _p === void 0 ? 'Loading...' : _p, _q = _a.beforeMask, beforeMask = _q === void 0 ? null : _q, props = __rest(_a, [\"animate\", \"animateBegin\", \"backgroundColor\", \"backgroundOpacity\", \"baseUrl\", \"children\", \"foregroundColor\", \"foregroundOpacity\", \"gradientRatio\", \"gradientDirection\", \"uniqueKey\", \"interval\", \"rtl\", \"speed\", \"style\", \"title\", \"beforeMask\"]);\r\n    var fixedId = uniqueKey || uid();\r\n    var idClip = fixedId + \"-diff\";\r\n    var idGradient = fixedId + \"-animated-diff\";\r\n    var idAria = fixedId + \"-aria\";\r\n    var rtlStyle = rtl ? { transform: 'scaleX(-1)' } : null;\r\n    var keyTimes = \"0; \" + interval + \"; 1\";\r\n    var dur = speed + \"s\";\r\n    var gradientTransform = gradientDirection === 'top-bottom' ? 'rotate(90)' : undefined;\r\n    return (createElement(\"svg\", __assign({ \"aria-labelledby\": idAria, role: \"img\", style: __assign(__assign({}, style), rtlStyle) }, props),\r\n        title ? createElement(\"title\", { id: idAria }, title) : null,\r\n        beforeMask && isValidElement(beforeMask) ? beforeMask : null,\r\n        createElement(\"rect\", { role: \"presentation\", x: \"0\", y: \"0\", width: \"100%\", height: \"100%\", clipPath: \"url(\" + baseUrl + \"#\" + idClip + \")\", style: { fill: \"url(\" + baseUrl + \"#\" + idGradient + \")\" } }),\r\n        createElement(\"defs\", null,\r\n            createElement(\"clipPath\", { id: idClip }, children),\r\n            createElement(\"linearGradient\", { id: idGradient, gradientTransform: gradientTransform },\r\n                createElement(\"stop\", { offset: \"0%\", stopColor: backgroundColor, stopOpacity: backgroundOpacity }, animate && (createElement(\"animate\", { attributeName: \"offset\", values: -gradientRatio + \"; \" + -gradientRatio + \"; 1\", keyTimes: keyTimes, dur: dur, repeatCount: \"indefinite\", begin: animateBegin }))),\r\n                createElement(\"stop\", { offset: \"50%\", stopColor: foregroundColor, stopOpacity: foregroundOpacity }, animate && (createElement(\"animate\", { attributeName: \"offset\", values: -gradientRatio / 2 + \"; \" + -gradientRatio / 2 + \"; \" + (1 +\r\n                        gradientRatio / 2), keyTimes: keyTimes, dur: dur, repeatCount: \"indefinite\", begin: animateBegin }))),\r\n                createElement(\"stop\", { offset: \"100%\", stopColor: backgroundColor, stopOpacity: backgroundOpacity }, animate && (createElement(\"animate\", { attributeName: \"offset\", values: \"0; 0; \" + (1 + gradientRatio), keyTimes: keyTimes, dur: dur, repeatCount: \"indefinite\", begin: animateBegin })))))));\r\n};\n\nvar ContentLoader = function (props) {\r\n    return props.children ? createElement(SVG, __assign({}, props)) : createElement(ReactContentLoaderFacebook, __assign({}, props));\r\n};\n\nvar ReactContentLoaderFacebook = function (props) { return (createElement(ContentLoader, __assign({ viewBox: \"0 0 476 124\" }, props),\r\n    createElement(\"rect\", { x: \"48\", y: \"8\", width: \"88\", height: \"6\", rx: \"3\" }),\r\n    createElement(\"rect\", { x: \"48\", y: \"26\", width: \"52\", height: \"6\", rx: \"3\" }),\r\n    createElement(\"rect\", { x: \"0\", y: \"56\", width: \"410\", height: \"6\", rx: \"3\" }),\r\n    createElement(\"rect\", { x: \"0\", y: \"72\", width: \"380\", height: \"6\", rx: \"3\" }),\r\n    createElement(\"rect\", { x: \"0\", y: \"88\", width: \"178\", height: \"6\", rx: \"3\" }),\r\n    createElement(\"circle\", { cx: \"20\", cy: \"20\", r: \"20\" }))); };\n\nvar ReactContentLoaderInstagram = function (props) { return (createElement(ContentLoader, __assign({ viewBox: \"0 0 400 460\" }, props),\r\n    createElement(\"circle\", { cx: \"31\", cy: \"31\", r: \"15\" }),\r\n    createElement(\"rect\", { x: \"58\", y: \"18\", rx: \"2\", ry: \"2\", width: \"140\", height: \"10\" }),\r\n    createElement(\"rect\", { x: \"58\", y: \"34\", rx: \"2\", ry: \"2\", width: \"140\", height: \"10\" }),\r\n    createElement(\"rect\", { x: \"0\", y: \"60\", rx: \"2\", ry: \"2\", width: \"400\", height: \"400\" }))); };\n\nvar ReactContentLoaderCode = function (props) { return (createElement(ContentLoader, __assign({ viewBox: \"0 0 340 84\" }, props),\r\n    createElement(\"rect\", { x: \"0\", y: \"0\", width: \"67\", height: \"11\", rx: \"3\" }),\r\n    createElement(\"rect\", { x: \"76\", y: \"0\", width: \"140\", height: \"11\", rx: \"3\" }),\r\n    createElement(\"rect\", { x: \"127\", y: \"48\", width: \"53\", height: \"11\", rx: \"3\" }),\r\n    createElement(\"rect\", { x: \"187\", y: \"48\", width: \"72\", height: \"11\", rx: \"3\" }),\r\n    createElement(\"rect\", { x: \"18\", y: \"48\", width: \"100\", height: \"11\", rx: \"3\" }),\r\n    createElement(\"rect\", { x: \"0\", y: \"71\", width: \"37\", height: \"11\", rx: \"3\" }),\r\n    createElement(\"rect\", { x: \"18\", y: \"23\", width: \"140\", height: \"11\", rx: \"3\" }),\r\n    createElement(\"rect\", { x: \"166\", y: \"23\", width: \"173\", height: \"11\", rx: \"3\" }))); };\n\nvar ReactContentLoaderListStyle = function (props) { return (createElement(ContentLoader, __assign({ viewBox: \"0 0 400 110\" }, props),\r\n    createElement(\"rect\", { x: \"0\", y: \"0\", rx: \"3\", ry: \"3\", width: \"250\", height: \"10\" }),\r\n    createElement(\"rect\", { x: \"20\", y: \"20\", rx: \"3\", ry: \"3\", width: \"220\", height: \"10\" }),\r\n    createElement(\"rect\", { x: \"20\", y: \"40\", rx: \"3\", ry: \"3\", width: \"170\", height: \"10\" }),\r\n    createElement(\"rect\", { x: \"0\", y: \"60\", rx: \"3\", ry: \"3\", width: \"250\", height: \"10\" }),\r\n    createElement(\"rect\", { x: \"20\", y: \"80\", rx: \"3\", ry: \"3\", width: \"200\", height: \"10\" }),\r\n    createElement(\"rect\", { x: \"20\", y: \"100\", rx: \"3\", ry: \"3\", width: \"80\", height: \"10\" }))); };\n\nvar ReactContentLoaderBulletList = function (props) { return (createElement(ContentLoader, __assign({ viewBox: \"0 0 245 125\" }, props),\r\n    createElement(\"circle\", { cx: \"10\", cy: \"20\", r: \"8\" }),\r\n    createElement(\"rect\", { x: \"25\", y: \"15\", rx: \"5\", ry: \"5\", width: \"220\", height: \"10\" }),\r\n    createElement(\"circle\", { cx: \"10\", cy: \"50\", r: \"8\" }),\r\n    createElement(\"rect\", { x: \"25\", y: \"45\", rx: \"5\", ry: \"5\", width: \"220\", height: \"10\" }),\r\n    createElement(\"circle\", { cx: \"10\", cy: \"80\", r: \"8\" }),\r\n    createElement(\"rect\", { x: \"25\", y: \"75\", rx: \"5\", ry: \"5\", width: \"220\", height: \"10\" }),\r\n    createElement(\"circle\", { cx: \"10\", cy: \"110\", r: \"8\" }),\r\n    createElement(\"rect\", { x: \"25\", y: \"105\", rx: \"5\", ry: \"5\", width: \"220\", height: \"10\" }))); };\n\nexport default ContentLoader;\nexport { ReactContentLoaderBulletList as BulletList, ReactContentLoaderCode as Code, ReactContentLoaderFacebook as Facebook, ReactContentLoaderInstagram as Instagram, ReactContentLoaderListStyle as List };\n//# sourceMappingURL=react-content-loader.es.js.map\n", "//Import Library\r\nimport ContentLoader from 'react-content-loader';\r\n\r\n// No props needed - component uses hardcoded values\r\nexport default function CardPlaceholder() {\r\n  return(\r\n    <ContentLoader\r\n      viewBox=\"0 0 380 70\"\r\n      height={50}\r\n      width={317}\r\n      speed={2}\r\n      title={'Loading'}\r\n      foregroundColor=\"#f7f7f7\"\r\n      backgroundColor=\"#ecebeb\"\r\n      uniqueKey={\"operation\"}\r\n    >\r\n      <rect x=\"10\" y=\"0\" rx=\"4\" ry=\"4\" width=\"320\" height=\"25\" />\r\n      <rect x=\"40\" y=\"40\" rx=\"3\" ry=\"3\" width=\"250\" height=\"20\" />\r\n    </ContentLoader>\r\n  )\r\n}\r\n"], "names": ["i18n", "useTranslation", "AboutUs", "language", "aboutUs", "setAboutUs", "useState", "_initialVal", "title", "description", "pageCategory", "images", "isEnabled", "loading", "setLoading", "landingPageParams", "query", "sort", "limit", "fetchAboutUs", "pageCategoryId", "fetchPageCategory", "length", "response", "apiService", "get", "Array", "isArray", "data", "landingPageData", "map", "item", "_i", "String", "process", "_id", "defautDesc", "useEffect", "desc", "replace", "div", "className", "CardPlaceholder", "img", "src", "alt", "dangerouslySetInnerHTML", "__html", "htmlContent", "ContentLoader", "viewBox", "height", "width", "speed", "foregroundColor", "backgroundColor", "<PERSON><PERSON><PERSON>", "rect", "x", "y", "rx", "ry"], "sourceRoot": "", "ignoreList": [2]}