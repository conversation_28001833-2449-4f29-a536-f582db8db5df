//Import Library
import React, { Component } from "react";
import { connect } from "react-redux";
import Router from "next/router";
import CustomLoader from "../common/CustomLoader";
import authService from "../../services/authService";
import { loadLoggedinUserData } from "../../stores/userActions";

type IHocState = {
  isLoading: boolean;
  cookie: string | null;
};

interface AuthSyncProps {
  objCookies?: { [key: string]: string };
  [key: string]: any;
}

// Public routes used to handle layouts
const publicRoutes: string[] = [
  "/home",
  "/login",
  // "/admin/login",
  "/forgot-password",
  "/reset-password/[passwordToken]",
  "/declarationform/[...routes]",
];

// Gets the display name of a JSX component for dev tools
const getDisplayName = (Component1: any) =>
  Component1.displayName || Component1.name || "Component";

function withAuthSync(WrappedComponent: any) {
  class MainComponent extends Component<AuthSyncProps, IHocState> {
    static displayName = `withAuthSync(${getDisplayName(WrappedComponent)})`;
    static async getInitialProps(ctx: any) {
      const componentProps =
        WrappedComponent.getInitialProps &&
        (await WrappedComponent.getInitialProps(ctx));
      if (ctx.ctx && ctx.ctx.req && ctx.ctx.req.cookies) {
        const objCookies =
          ctx.ctx && ctx.ctx.req && ctx.ctx.req.cookies
            ? ctx.ctx.req.cookies
            : {};
        componentProps.objCookies = objCookies;
        return { ...componentProps };
      } else {
        return { ...componentProps };
      }
    }

    constructor(props: any) {
      super(props);
      this.state = {
        isLoading: true,
        cookie: null, // Will be set based on active login check
      };
    }

    async componentDidMount() {
      const { route } = this.props.router;

      Router.events.on("routeChangeComplete", (url) => {
        if (url === "/home") {
          this.setState({ isLoading: false });
        }
      });

      // Check if user has actively logged in (not just copied cookie)
      const hasActiveLogin = authService.hasActiveLogin();

      if (!hasActiveLogin && publicRoutes.indexOf(route) === -1) {
        console.log("User has valid session but no active login - redirecting to login");
        // Clear any existing authentication flags to be safe
        if (typeof window !== 'undefined') {
          localStorage.removeItem('userLoggedIn');
          localStorage.removeItem('loginTimestamp');
        }
        this.props.router.push("/home");
        return;
      }

      // Use backend session verification instead of cookie parsing
      try {
        const sessionData = await authService.verifySession();

        if (!sessionData.isAuthenticated && publicRoutes.indexOf(route) === -1) {
          this.props.router.push("/home");
          return;
        }

        // If user is authenticated, load user data and permissions into Redux
        if (sessionData.isAuthenticated && hasActiveLogin) {
          (this.props as any).dispatch(loadLoggedinUserData());
        }

        this.setState({
          isLoading: false,
          cookie: sessionData.isAuthenticated && hasActiveLogin ? "authenticated" : null
        });
      } catch (error) {
        console.error("Session verification failed:", error);
        if (publicRoutes.indexOf(route) === -1) {
          this.props.router.push("/home");
          return;
        }
        this.setState({ isLoading: false });
      }
    }

    componentWillUnmount() {
      Router.events.off("routeChangeComplete", () => null);
    }

    render() {
      const { router } = this.props;
      const isPublicRoute = publicRoutes.indexOf(router.route) > -1;
      return this.state.isLoading ? <CustomLoader /> : (
        <WrappedComponent
          isLoading={this.state.isLoading}
          isPublicRoute={isPublicRoute}
          {...this.props}
        />
      );
    }
  }

  const mapStateToProps = (state: any) => state;
  return connect(mapStateToProps)(MainComponent);
}

export default withAuthSync;
