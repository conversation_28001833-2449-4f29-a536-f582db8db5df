(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[3582],{3249:(s,e,a)=>{(window.__NEXT_P=window.__NEXT_P||[]).push(["/adminsettings",function(){return a(72938)}])},21546:(s,e,a)=>{"use strict";a.r(e),a.d(e,{canAddAreaOfWork:()=>r,canAddContent:()=>v,canAddCountry:()=>d,canAddDeploymentStatus:()=>t,canAddEventStatus:()=>c,canAddExpertise:()=>o,canAddFocalPointApproval:()=>m,canAddHazardTypes:()=>p,canAddHazards:()=>x,canAddLandingPage:()=>w,canAddOperationStatus:()=>u,canAddOrganisationApproval:()=>j,canAddOrganisationNetworks:()=>h,canAddOrganisationTypes:()=>A,canAddProjectStatus:()=>N,canAddRegions:()=>g,canAddRiskLevels:()=>y,canAddSyndromes:()=>f,canAddUpdateTypes:()=>_,canAddUsers:()=>S,canAddVspaceApproval:()=>l,canAddWorldRegion:()=>C,default:()=>T});var n=a(8178);let i="create:any",r=(0,n.A)({authenticatedSelector:s=>!!s.permissions&&!!s.permissions.area_of_work&&!!s.permissions.area_of_work[i],wrapperDisplayName:"CanAddAreaOfWork"}),d=(0,n.A)({authenticatedSelector:s=>!!s.permissions&&!!s.permissions.country&&!!s.permissions.country[i],wrapperDisplayName:"CanAddCountry"}),t=(0,n.A)({authenticatedSelector:s=>!!s.permissions&&!!s.permissions.deployment_status&&!!s.permissions.deployment_status[i],wrapperDisplayName:"CanAddDeploymentStatus"}),c=(0,n.A)({authenticatedSelector:s=>!!s.permissions&&!!s.permissions.event_status&&!!s.permissions.event_status[i],wrapperDisplayName:"CanAddEventStatus"}),o=(0,n.A)({authenticatedSelector:s=>!!s.permissions&&!!s.permissions.expertise&&!!s.permissions.expertise[i],wrapperDisplayName:"CanAddExpertise"}),m=(0,n.A)({authenticatedSelector:s=>!!s.permissions&&!!s.permissions.institution_focal_point&&!!s.permissions.institution_focal_point[i],wrapperDisplayName:"CanAddFocalPointApproval"}),l=(0,n.A)({authenticatedSelector:s=>!!s.permissions&&!!s.permissions.institution_focal_point&&!!s.permissions.institution_focal_point[i],wrapperDisplayName:"CanAddVspaceApproval"}),x=(0,n.A)({authenticatedSelector:s=>!!s.permissions&&!!s.permissions.hazard&&!!s.permissions.hazard[i],wrapperDisplayName:"CanAddHazards"}),p=(0,n.A)({authenticatedSelector:s=>!!s.permissions&&!!s.permissions.hazard_type&&!!s.permissions.hazard_type[i],wrapperDisplayName:"CanAddHazardTypes"}),j=(0,n.A)({authenticatedSelector:s=>!!s.permissions&&!!s.permissions.institution&&!!s.permissions.institution[i],wrapperDisplayName:"CanAddOrganisationApproval"}),h=(0,n.A)({authenticatedSelector:s=>!!s.permissions&&!!s.permissions.institution_network&&!!s.permissions.institution_network[i],wrapperDisplayName:"CanAddOrganisationNetworks"}),A=(0,n.A)({authenticatedSelector:s=>!!s.permissions&&!!s.permissions.institution_type&&!!s.permissions.institution_type[i],wrapperDisplayName:"CanAddOrganisationTypes"}),u=(0,n.A)({authenticatedSelector:s=>!!s.permissions&&!!s.permissions.operation_status&&!!s.permissions.operation_status[i],wrapperDisplayName:"CanAddOperationStatus"}),N=(0,n.A)({authenticatedSelector:s=>!!s.permissions&&!!s.permissions.project_status&&!!s.permissions.project_status[i],wrapperDisplayName:"CanAddProjectStatus"}),g=(0,n.A)({authenticatedSelector:s=>!!s.permissions&&!!s.permissions.region&&!!s.permissions.region[i],wrapperDisplayName:"CanAddRegions"}),y=(0,n.A)({authenticatedSelector:s=>!!s.permissions&&!!s.permissions.risk_level&&!!s.permissions.risk_level[i],wrapperDisplayName:"CanAddRiskLevels"}),f=(0,n.A)({authenticatedSelector:s=>!!s.permissions&&!!s.permissions.syndrome&&!!s.permissions.syndrome[i],wrapperDisplayName:"CanAddSyndromes"}),_=(0,n.A)({authenticatedSelector:s=>!!s.permissions&&!!s.permissions.update_type&&!!s.permissions.update_type[i],wrapperDisplayName:"CanAddUpdateTypes"}),S=(0,n.A)({authenticatedSelector:s=>!!s.permissions&&!!s.permissions.users&&!!s.permissions.users[i],wrapperDisplayName:"CanAddUsers"}),C=(0,n.A)({authenticatedSelector:s=>!!s.permissions&&!!s.permissions.worl_region&&!!s.permissions.worl_region[i],wrapperDisplayName:"CanAddWorldRegion"}),w=(0,n.A)({authenticatedSelector:s=>!!s.permissions&&!!s.permissions.landing_page&&!!s.permissions.landing_page[i],wrapperDisplayName:"CanAddLandingPage"}),v=(0,n.A)({authenticatedSelector:s=>!!s.permissions&&!!s.permissions.operation&&!!s.permissions.operation[i]&&!!s.permissions.project&&!!s.permissions.project[i]&&!!s.permissions.event&&!!s.permissions.event[i]&&!!s.permissions.vspace&&!!s.permissions.vspace[i]&&!!s.permissions.institution&&!!s.permissions.institution[i]&&!!s.permissions.update&&!!s.permissions.update[i]||!1,wrapperDisplayName:"CanAddContent"}),T=r},29335:(s,e,a)=>{"use strict";a.d(e,{A:()=>_});var n=a(15039),i=a.n(n),r=a(14232),d=a(77346),t=a(37876);let c=r.forwardRef((s,e)=>{let{className:a,bsPrefix:n,as:r="div",...c}=s;return n=(0,d.oU)(n,"card-body"),(0,t.jsx)(r,{ref:e,className:i()(a,n),...c})});c.displayName="CardBody";let o=r.forwardRef((s,e)=>{let{className:a,bsPrefix:n,as:r="div",...c}=s;return n=(0,d.oU)(n,"card-footer"),(0,t.jsx)(r,{ref:e,className:i()(a,n),...c})});o.displayName="CardFooter";var m=a(81764);let l=r.forwardRef((s,e)=>{let{bsPrefix:a,className:n,as:c="div",...o}=s,l=(0,d.oU)(a,"card-header"),x=(0,r.useMemo)(()=>({cardHeaderBsPrefix:l}),[l]);return(0,t.jsx)(m.A.Provider,{value:x,children:(0,t.jsx)(c,{ref:e,...o,className:i()(n,l)})})});l.displayName="CardHeader";let x=r.forwardRef((s,e)=>{let{bsPrefix:a,className:n,variant:r,as:c="img",...o}=s,m=(0,d.oU)(a,"card-img");return(0,t.jsx)(c,{ref:e,className:i()(r?"".concat(m,"-").concat(r):m,n),...o})});x.displayName="CardImg";let p=r.forwardRef((s,e)=>{let{className:a,bsPrefix:n,as:r="div",...c}=s;return n=(0,d.oU)(n,"card-img-overlay"),(0,t.jsx)(r,{ref:e,className:i()(a,n),...c})});p.displayName="CardImgOverlay";let j=r.forwardRef((s,e)=>{let{className:a,bsPrefix:n,as:r="a",...c}=s;return n=(0,d.oU)(n,"card-link"),(0,t.jsx)(r,{ref:e,className:i()(a,n),...c})});j.displayName="CardLink";var h=a(46052);let A=(0,h.A)("h6"),u=r.forwardRef((s,e)=>{let{className:a,bsPrefix:n,as:r=A,...c}=s;return n=(0,d.oU)(n,"card-subtitle"),(0,t.jsx)(r,{ref:e,className:i()(a,n),...c})});u.displayName="CardSubtitle";let N=r.forwardRef((s,e)=>{let{className:a,bsPrefix:n,as:r="p",...c}=s;return n=(0,d.oU)(n,"card-text"),(0,t.jsx)(r,{ref:e,className:i()(a,n),...c})});N.displayName="CardText";let g=(0,h.A)("h5"),y=r.forwardRef((s,e)=>{let{className:a,bsPrefix:n,as:r=g,...c}=s;return n=(0,d.oU)(n,"card-title"),(0,t.jsx)(r,{ref:e,className:i()(a,n),...c})});y.displayName="CardTitle";let f=r.forwardRef((s,e)=>{let{bsPrefix:a,className:n,bg:r,text:o,border:m,body:l=!1,children:x,as:p="div",...j}=s,h=(0,d.oU)(a,"card");return(0,t.jsx)(p,{ref:e,...j,className:i()(n,h,r&&"bg-".concat(r),o&&"text-".concat(o),m&&"border-".concat(m)),children:l?(0,t.jsx)(c,{children:x}):x})});f.displayName="Card";let _=Object.assign(f,{Img:x,Title:y,Subtitle:u,Body:c,Link:j,Text:N,Header:l,Footer:o,ImgOverlay:p})},69600:(s,e,a)=>{"use strict";a.d(e,{A:()=>i});var n=a(37876);function i(s){return(0,n.jsx)("h2",{className:"page-heading",children:s.title})}},72938:(s,e,a)=>{"use strict";a.r(e),a.d(e,{__N_SSG:()=>h,default:()=>A});var n=a(37876),i=a(37784),r=a(29335),d=a(49589),t=a(56970),c=a(48230),o=a.n(c),m=a(21772),l=a(11041),x=a(69600),p=a(31753),j=a(21546),h=!0;let A=()=>{let{t:s}=(0,p.Bd)("common"),e=()=>(0,n.jsx)(i.A,{md:4,sm:12,className:"mb-2 mt-2",children:(0,n.jsx)("div",{className:"infoCard_admin_card text-center cursor-pointer",children:(0,n.jsx)(o(),{href:"/adminsettings/[...routes]",as:"/adminsettings/area_of_work",children:(0,n.jsx)(r.A,{className:"infoCard ",children:(0,n.jsx)(r.A.Body,{children:(0,n.jsxs)(r.A.Text,{children:[s("adminsetting.adminindex.Areaofwork"),(0,n.jsx)("span",{className:"arrowStyle",children:(0,n.jsx)(m.g,{icon:l.dmS})})]})})})})})}),a=()=>(0,n.jsx)(i.A,{md:4,sm:12,className:"mb-2 mt-2",children:(0,n.jsx)("div",{className:"infoCard_admin_card text-center cursor-pointer",children:(0,n.jsx)(o(),{href:"/adminsettings/[...routes]",as:"/adminsettings/country",children:(0,n.jsx)(r.A,{className:"infoCard",children:(0,n.jsx)(r.A.Body,{children:(0,n.jsxs)(r.A.Text,{children:[s("adminsetting.adminindex.Countries"),(0,n.jsx)("span",{className:"arrowStyle",children:(0,n.jsx)(m.g,{icon:l.dmS})})]})})})})})}),c=()=>(0,n.jsx)(i.A,{md:4,sm:12,className:"mb-2 mt-2",children:(0,n.jsx)("div",{className:" infoCard_admin_card text-center cursor-pointer",children:(0,n.jsx)(o(),{href:"/adminsettings/[...routes]",as:"/adminsettings/deploymentstatus",children:(0,n.jsx)(r.A,{className:"infoCard",children:(0,n.jsx)(r.A.Body,{children:(0,n.jsxs)(r.A.Text,{children:[s("adminsetting.adminindex.DeploymentStatus"),(0,n.jsx)("span",{className:"arrowStyle",children:(0,n.jsx)(m.g,{icon:l.dmS})})]})})})})})}),h=()=>(0,n.jsx)(i.A,{md:4,sm:12,className:"mb-2 mt-2",children:(0,n.jsx)("div",{className:"infoCard_admin_card text-center cursor-pointer",children:(0,n.jsx)(o(),{href:"/adminsettings/[...routes]",as:"/adminsettings/eventstatus",children:(0,n.jsx)(r.A,{className:"infoCard",children:(0,n.jsx)(r.A.Body,{children:(0,n.jsxs)(r.A.Text,{children:[s("adminsetting.adminindex.EventStatus"),(0,n.jsx)("span",{className:"arrowStyle",children:(0,n.jsx)(m.g,{icon:l.dmS})})]})})})})})}),A=()=>(0,n.jsx)(i.A,{md:4,sm:12,className:"mb-2 mt-2",children:(0,n.jsx)("div",{className:"infoCard_admin_card text-center cursor-pointer",children:(0,n.jsx)(o(),{href:"/adminsettings/[...routes]",as:"/adminsettings/expertise",children:(0,n.jsx)(r.A,{className:"infoCard",children:(0,n.jsx)(r.A.Body,{children:(0,n.jsxs)(r.A.Text,{children:[s("adminsetting.adminindex.Expertise"),(0,n.jsx)("span",{className:"arrowStyle",children:(0,n.jsx)(m.g,{icon:l.dmS})})]})})})})})}),u=()=>(0,n.jsx)(i.A,{md:4,sm:12,className:"mb-2 mt-2",children:(0,n.jsx)("div",{className:"infoCard_admin_card text-center cursor-pointer",children:(0,n.jsx)(o(),{href:"/adminsettings/[...routes]",as:"/adminsettings/focal_point",children:(0,n.jsx)(r.A,{className:"infoCard",children:(0,n.jsx)(r.A.Body,{children:(0,n.jsxs)(r.A.Text,{children:[s("adminsetting.adminindex.FocalPointApproval"),(0,n.jsx)("span",{className:"arrowStyle",children:(0,n.jsx)(m.g,{icon:l.dmS})})]})})})})})}),N=()=>(0,n.jsx)(i.A,{md:4,sm:12,className:"mb-2 mt-2",children:(0,n.jsx)("div",{className:"infoCard_admin_card text-center cursor-pointer",children:(0,n.jsx)(o(),{href:"/adminsettings/[...routes]",as:"/adminsettings/Vspace_point",children:(0,n.jsx)(r.A,{className:"infoCard",children:(0,n.jsx)(r.A.Body,{children:(0,n.jsxs)(r.A.Text,{children:[s("adminsetting.adminindex.VspaceApproval"),(0,n.jsx)("span",{className:"arrowStyle",children:(0,n.jsx)(m.g,{icon:l.dmS})})]})})})})})}),g=()=>(0,n.jsx)(i.A,{md:4,sm:12,className:"mb-2 mt-2",children:(0,n.jsx)("div",{className:"infoCard_admin_card text-center cursor-pointer",children:(0,n.jsx)(o(),{href:"/adminsettings/[...routes]",as:"/adminsettings/hazard",children:(0,n.jsx)(r.A,{className:"infoCard",children:(0,n.jsx)(r.A.Body,{children:(0,n.jsxs)(r.A.Text,{children:[s("adminsetting.adminindex.Hazards"),(0,n.jsx)("span",{className:"arrowStyle",children:(0,n.jsx)(m.g,{icon:l.dmS})})]})})})})})}),y=()=>(0,n.jsx)(i.A,{md:4,sm:12,className:"mb-2 mt-2",children:(0,n.jsx)("div",{className:"infoCard_admin_card text-center cursor-pointer",children:(0,n.jsx)(o(),{href:"/adminsettings/[...routes]",as:"/adminsettings/hazardTypes",children:(0,n.jsx)(r.A,{className:"infoCard",children:(0,n.jsx)(r.A.Body,{children:(0,n.jsxs)(r.A.Text,{children:[s("adminsetting.adminindex.HazardTypes"),(0,n.jsx)("span",{className:"arrowStyle",children:(0,n.jsx)(m.g,{icon:l.dmS})})]})})})})})}),f=()=>(0,n.jsx)(i.A,{md:4,sm:12,className:"mb-2 mt-2",children:(0,n.jsx)("div",{className:"infoCard_admin_card text-center cursor-pointer",children:(0,n.jsx)(o(),{href:"/adminsettings/[...routes]",as:"/adminsettings/institution_approval",children:(0,n.jsx)(r.A,{className:"infoCard",children:(0,n.jsx)(r.A.Body,{children:(0,n.jsxs)(r.A.Text,{children:[s("adminsetting.adminindex.OrganisationApproval"),(0,n.jsx)("span",{className:"arrowStyle",children:(0,n.jsx)(m.g,{icon:l.dmS})})]})})})})})}),_=()=>(0,n.jsx)(i.A,{md:4,sm:12,className:"mb-2 mt-2",children:(0,n.jsx)("div",{className:"infoCard_admin_card text-center cursor-pointer",children:(0,n.jsx)(o(),{href:"/adminsettings/[...routes]",as:"/adminsettings/institution_network",children:(0,n.jsx)(r.A,{className:"infoCard",children:(0,n.jsx)(r.A.Body,{children:(0,n.jsxs)(r.A.Text,{children:[s("adminsetting.adminindex.OrganisationNetworks"),(0,n.jsx)("span",{className:"arrowStyle",children:(0,n.jsx)(m.g,{icon:l.dmS})})]})})})})})}),S=()=>(0,n.jsx)(i.A,{md:4,sm:12,className:"mb-2 mt-2",children:(0,n.jsx)("div",{className:"infoCard_admin_card text-center cursor-pointer",children:(0,n.jsx)(o(),{href:"/adminsettings/[...routes]",as:"/adminsettings/institution_type",children:(0,n.jsx)(r.A,{className:"infoCard",children:(0,n.jsx)(r.A.Body,{children:(0,n.jsxs)(r.A.Text,{children:[s("adminsetting.adminindex.OrganisationTypes"),(0,n.jsx)("span",{className:"arrowStyle",children:(0,n.jsx)(m.g,{icon:l.dmS})})]})})})})})}),C=()=>(0,n.jsx)(i.A,{md:4,sm:12,className:"mb-2 mt-2",children:(0,n.jsx)("div",{className:"infoCard_admin_card text-center cursor-pointer",children:(0,n.jsx)(o(),{href:"/adminsettings/[...routes]",as:"/adminsettings/operationstatus",children:(0,n.jsx)(r.A,{className:"infoCard",children:(0,n.jsx)(r.A.Body,{children:(0,n.jsxs)(r.A.Text,{children:[s("adminsetting.adminindex.OperationStatus"),(0,n.jsx)("span",{className:"arrowStyle",children:(0,n.jsx)(m.g,{icon:l.dmS})})]})})})})})}),w=()=>(0,n.jsx)(i.A,{md:4,sm:12,className:"mb-2 mt-2",children:(0,n.jsx)("div",{className:"infoCard_admin_card text-center cursor-pointer",children:(0,n.jsx)(o(),{href:"/adminsettings/[...routes]",as:"/adminsettings/projectstatus",children:(0,n.jsx)(r.A,{className:"infoCard",children:(0,n.jsx)(r.A.Body,{children:(0,n.jsxs)(r.A.Text,{children:[s("adminsetting.adminindex.ProjectStatus"),(0,n.jsx)("span",{className:"arrowStyle",children:(0,n.jsx)(m.g,{icon:l.dmS})})]})})})})})}),v=()=>(0,n.jsx)(i.A,{md:4,sm:12,className:"mb-2 mt-2",children:(0,n.jsx)("div",{className:"infoCard_admin_card text-center cursor-pointer",children:(0,n.jsx)(o(),{href:"/adminsettings/[...routes]",as:"/adminsettings/region",children:(0,n.jsx)(r.A,{className:"infoCard",children:(0,n.jsx)(r.A.Body,{children:(0,n.jsxs)(r.A.Text,{children:[s("adminsetting.adminindex.Regions"),(0,n.jsx)("span",{className:"arrowStyle",children:(0,n.jsx)(m.g,{icon:l.dmS})})]})})})})})}),T=()=>(0,n.jsx)(i.A,{md:4,sm:12,className:"mb-2 mt-2",children:(0,n.jsx)("div",{className:"infoCard_admin_card text-center cursor-pointer",children:(0,n.jsx)(o(),{href:"/adminsettings/[...routes]",as:"/adminsettings/risklevel",children:(0,n.jsx)(r.A,{className:"infoCard",children:(0,n.jsx)(r.A.Body,{children:(0,n.jsxs)(r.A.Text,{children:[s("adminsetting.adminindex.RiskLevels"),(0,n.jsx)("span",{className:"arrowStyle",children:(0,n.jsx)(m.g,{icon:l.dmS})})]})})})})})}),b=()=>(0,n.jsx)(i.A,{md:4,sm:12,className:"mb-2 mt-2",children:(0,n.jsx)("div",{className:"infoCard_admin_card text-center cursor-pointer",children:(0,n.jsx)(o(),{href:"/adminsettings/[...routes]",as:"/adminsettings/syndrome",children:(0,n.jsx)(r.A,{className:"infoCard",children:(0,n.jsx)(r.A.Body,{children:(0,n.jsxs)(r.A.Text,{children:[s("adminsetting.adminindex.Syndromes"),(0,n.jsx)("span",{className:"arrowStyle",children:(0,n.jsx)(m.g,{icon:l.dmS})})]})})})})})}),k=()=>(0,n.jsx)(i.A,{md:4,sm:12,className:"mb-2 mt-2",children:(0,n.jsx)("div",{className:"infoCard_admin_card text-center cursor-pointer",children:(0,n.jsx)(o(),{href:"/adminsettings/[...routes]",as:"/adminsettings/update_type",children:(0,n.jsx)(r.A,{className:"infoCard",children:(0,n.jsx)(r.A.Body,{children:(0,n.jsxs)(r.A.Text,{children:[s("adminsetting.adminindex.UpdatesTypes"),(0,n.jsx)("span",{className:"arrowStyle",children:(0,n.jsx)(m.g,{icon:l.dmS})})]})})})})})}),B=()=>(0,n.jsx)(i.A,{md:4,sm:12,className:"mb-2 mt-2",children:(0,n.jsx)("div",{className:"infoCard_admin_card text-center cursor-pointer",children:(0,n.jsx)(o(),{href:"/adminsettings/[...routes]",as:"/adminsettings/users",children:(0,n.jsx)(r.A,{className:"infoCard",children:(0,n.jsx)(r.A.Body,{children:(0,n.jsxs)(r.A.Text,{children:[s("adminsetting.adminindex.Users"),(0,n.jsx)("span",{className:"arrowStyle",children:(0,n.jsx)(m.g,{icon:l.dmS})})]})})})})})}),D=()=>(0,n.jsx)(i.A,{md:4,sm:12,className:"mb-2 mt-2",children:(0,n.jsx)("div",{className:"infoCard_admin_card text-center cursor-pointer",children:(0,n.jsx)(o(),{href:"/adminsettings/[...routes]",as:"/adminsettings/worldregion",children:(0,n.jsx)(r.A,{className:"infoCard",children:(0,n.jsx)(r.A.Body,{children:(0,n.jsxs)(r.A.Text,{children:[s("adminsetting.adminindex.WorldRegions"),(0,n.jsx)("span",{className:"arrowStyle",children:(0,n.jsx)(m.g,{icon:l.dmS})})]})})})})})}),O=()=>(0,n.jsx)(i.A,{md:4,sm:12,className:"mb-2 mt-2",children:(0,n.jsx)("div",{className:"infoCard_admin_card text-center cursor-pointer",children:(0,n.jsx)(o(),{href:"/adminsettings/[...routes]",as:"/adminsettings/landing",children:(0,n.jsx)(r.A,{className:"infoCard",children:(0,n.jsx)(r.A.Body,{children:(0,n.jsxs)(r.A.Text,{children:[s("adminsetting.adminindex.EditableContent"),(0,n.jsx)("span",{className:"arrowStyle",children:(0,n.jsx)(m.g,{icon:l.dmS})})]})})})})})}),R=()=>(0,n.jsx)(i.A,{md:4,sm:12,className:"mb-2 mt-2",children:(0,n.jsx)("div",{className:"infoCard_admin_card text-center cursor-pointer",children:(0,n.jsx)(o(),{href:"/adminsettings/[...routes]",as:"/adminsettings/content",children:(0,n.jsx)(r.A,{className:"infoCard",children:(0,n.jsx)(r.A.Body,{children:(0,n.jsxs)(r.A.Text,{children:[s("adminsetting.adminindex.Content"),(0,n.jsx)("span",{className:"arrowStyle",children:(0,n.jsx)(m.g,{icon:l.dmS})})]})})})})})}),U=(0,j.canAddAreaOfWork)(()=>(0,n.jsx)(e,{})),P=(0,j.canAddCountry)(()=>(0,n.jsx)(a,{})),z=(0,j.canAddDeploymentStatus)(()=>(0,n.jsx)(c,{})),E=(0,j.canAddEventStatus)(()=>(0,n.jsx)(h,{})),H=(0,j.canAddExpertise)(()=>(0,n.jsx)(A,{})),L=(0,j.canAddFocalPointApproval)(()=>(0,n.jsx)(u,{})),W=(0,j.canAddVspaceApproval)(()=>(0,n.jsx)(N,{})),F=(0,j.canAddHazards)(()=>(0,n.jsx)(g,{})),V=(0,j.canAddHazardTypes)(()=>(0,n.jsx)(y,{})),I=(0,j.canAddOrganisationApproval)(()=>(0,n.jsx)(f,{})),X=(0,j.canAddOrganisationNetworks)(()=>(0,n.jsx)(_,{})),G=(0,j.canAddOrganisationTypes)(()=>(0,n.jsx)(S,{})),M=(0,j.canAddOperationStatus)(()=>(0,n.jsx)(C,{})),q=(0,j.canAddProjectStatus)(()=>(0,n.jsx)(w,{})),J=(0,j.canAddRegions)(()=>(0,n.jsx)(v,{})),K=(0,j.canAddRiskLevels)(()=>(0,n.jsx)(T,{})),Q=(0,j.canAddSyndromes)(()=>(0,n.jsx)(b,{})),Y=(0,j.canAddUpdateTypes)(()=>(0,n.jsx)(k,{})),Z=(0,j.canAddUsers)(()=>(0,n.jsx)(B,{})),$=(0,j.canAddWorldRegion)(()=>(0,n.jsx)(D,{})),ss=(0,j.canAddLandingPage)(()=>(0,n.jsx)(O,{})),se=(0,j.canAddContent)(()=>(0,n.jsx)(R,{}));return(0,n.jsxs)(d.A,{fluid:!0,style:{overflowX:"hidden"},className:"p-0",children:[(0,n.jsx)(x.A,{title:s("menu.adminSettings")}),(0,n.jsxs)(t.A,{children:[(0,n.jsx)(U,{}),(0,n.jsx)(P,{}),(0,n.jsx)(z,{}),(0,n.jsx)(E,{}),(0,n.jsx)(H,{}),(0,n.jsx)(L,{}),(0,n.jsx)(W,{}),(0,n.jsx)(F,{}),(0,n.jsx)(V,{}),(0,n.jsx)(I,{}),(0,n.jsx)(X,{}),(0,n.jsx)(G,{}),(0,n.jsx)(M,{}),(0,n.jsx)(q,{}),(0,n.jsx)(J,{}),(0,n.jsx)(K,{}),(0,n.jsx)(Q,{}),(0,n.jsx)(Y,{}),(0,n.jsx)(Z,{}),(0,n.jsx)($,{}),(0,n.jsx)(ss,{}),(0,n.jsx)(se,{})]})]})}},81764:(s,e,a)=>{"use strict";a.d(e,{A:()=>i});let n=a(14232).createContext(null);n.displayName="CardHeaderContext";let i=n}},s=>{var e=e=>s(s.s=e);s.O(0,[7725,1772,636,6593,8792],()=>e(3249)),_N_E=s.O()}]);
//# sourceMappingURL=adminsettings-4b1e40472462c2ca.js.map