{"version": 3, "file": "static/chunks/7126-7e3df21f44832880.js", "mappings": "yFAAA,QAYC,cACD,iBAQA,EANA,sCACA,QACA,CAAG,EAUH,WALA,CADA,EAFA,IAGA,gBACA,SACA,GAGA,QACC,CA1BG,EAAO,CAAC,EAAW,KAAF,GAAiB,CAAC,CAAS,YAAP,EAAO,4BAAC,4BCAjD,qCAA6C,CAC7C,QACA,CAAC,EAAC,EACF,OAAe,QAEf,eAEA,GAAuC,0BAAuC,YAFrC,EAAQ,KAAY,GA4D7D,SAAe,CAtDf,CACA,qBAHA,MAIA,qBAJA,EAIA,CACA,kBACA,CAAK,CANL,EAMK,MANL,EAMK,IANuC,OAAkB,2BAAkC,kDAAoE,EAAY,GAAP,CAAO,GAAoB,GAOpM,CAAG,CACH,EARoM,OAQpM,YACA,qBACA,YACA,mBACA,CAAK,CACL,CAAG,CACH,sBACA,qBACA,oBACA,mBACA,mCACA,gCACA,CAAK,CACL,CAAG,CACH,qBACA,qBACA,UACA,SACA,WACA,CAAK,CACL,CAAG,CACH,qBACA,qBACA,SACA,QACA,WACA,UACA,CAAK,CACL,CAAG,CACH,uBACA,qBACA,gCACA,oBACA,CAAK,CACL,CAAG,CACH,uBACA,qBACA,gCACA,oBACA,CAAK,CACL,CAAG,CACH,gBACA,qBACA,OACA,UACA,CAAK,CACL,CACA,gCChEA,qCAA6C,CAC7C,QACA,CAAC,EAAC,EACF,OAAe,QAEf,eAgBA,GAAwC,mBAA6B,SAAc,mDAA8E,OAAS,WAAkB,UAAwC,eAA+B,gBAAyB,SAAiB,yDAAsF,eAAuB,8CAAsD,iDAAqF,mBAAsC,6BAAmD,MAAP,CAAO,GAA2F,OAA7D,YAAsB,KAAa,SAA0B,GAhBprB,EAAQ,KAAO,GAEpD,IAAyC,EAAQ,GAFL,CAEoB,GAEhE,EAAkB,EAAQ,KAAe,CAFO,CAIhD,IAA2C,EAAQ,IAF1B,CAE2C,GAEpE,IAA6C,EAAQ,GAFH,CAEqB,GAEvE,IAAqC,EAAQ,IAFO,CAEU,GAE9D,SAF4C,EAE5C,GAAuC,0BAAuC,WAE9E,aAAsC,0CAAgD,kBAA6G,OAAlF,aAAiE,UAAiB,EAInM,cAAoW,EAA5U,IAA4U,CAAtO,EAA3E,4DAA2E,YAAkC,iBAA+B,GAAP,MAAO,GAAkC,sGAAmI,GAEpW,aAAoR,OAA9P,6BAAgD,YAAgB,mBAAsB,KAAO,mBAA2B,eAA0B,2CAAyD,YAAiC,WAAkB,sBAUpR,gBAA2I,OAA1G,uCAA6F,OAAjB,cAAiB,IAAa,KAM3I,cAAwC,cAAuB,kFAAyF,SAIxJ,cAAiL,OAAnJ,0DAAgG,+CAAmD,GAEjL,kBAAoM,EAAxJ,YAAkB,2BAAkC,kDAAoE,EAAY,GAAP,CAAO,GAAoB,EAMpM,kBApB2C,sBAqB3C,GArB2C,OAqB3C,EArB0G,qEAA6E,CAqBvL,EArBuL,wCAAyE,aAAe,MAqB/Q,EArB+Q,6BAAuD,EAAG,KAqBzU,KAEA,IAnBiC,EANjC,EAyBA,GAnBiC,EAMjC,WAAuC,kBANuD,KAMvD,6BAAwE,uBAAxE,SAAkH,KAA1C,UAA0C,sBAA8C,IAAiF,OAA3E,mEAAuE,GAAI,GAAe,SAAY,aANrN,WAAyC,IAEvI,IAFuI,MAmBvI,EAnBqL,CAA+L,OAA3G,EAApF,EAAoF,2BAAnD,kBAA8G,IAA9G,EAAqH,wBAE3U,EAFoX,KAElU,CAAlD,EAFoX,IAElU,wCAA0E,EAAe,IAFyO,EAEzO,CAmB3I,kBACA,EAhCkD,KAkClD,iBAlCkD,EAA0C,qDAyK5F,OAnIA,IAFA,kBAEA,0BAEA,8BAEA,2BAEA,wCACA,mBACA,CAAK,EAEL,qCACA,gBACA,CAAK,EAEL,oCACA,aACA,iBAGA,gBACA,CAAK,EAEL,gCACA,qDAIA,yCAEA,gCACA,yEACA,kBACA,MACA,UAEA,yBACA,OACA,WACA,eACA,6CACA,eACA,YACA,CACA,CAAO,EACP,CAAK,EAEL,yCACA,MA9DA,uBA8DA,iBACA,2BAEA,sBACA,MAEA,CACA,CAAK,EAEL,iCACA,YACA,UACA,CAAO,CACP,CAAK,EAEL,+BACA,YACA,UACA,CAAO,CACP,CAAK,EAEL,iCACA,UAEA,gEACA,SAKA,yCAEA,gDAJA,IAQA,QACA,MAIA,IAPA,MADA,kCACA,wBAA6E,EAO7E,GACA,MAIA,eADA,4BACA,GAQA,EARqE,KAErE,gBACA,4GACA,yDACA,CAAS,EAGT,EACA,CAAK,EAEL,gCACA,oDACA,CAAK,EAEL,+BACA,oDACA,CAAK,EAEL,4BAIA,EAFA,aAEA,4CAEA,YACA,WACA,CAAO,CACP,CAAK,EAEL,SACA,4BACA,WACA,cACA,YACA,eACA,cACA,EACA,CACA,CAwKA,OA9UA,EAwKA,EACA,uBAzK8D,CA0K9D,iBACA,kBACA,CACA,CAAG,EACH,yBACA,kBACA,mDACA,eACA,qCACA,oDACA,CAAS,EAGT,kCAMA,kBACA,CACA,CAAG,EACH,2BACA,iBACA,oBACA,CACA,CAAG,EACH,kBACA,iBAGA,4DAEA,CAF2E,EAE3E,mEAGA,CAHqF,GAGrF,cACA,CACA,CAAG,EACH,oBACA,iBAEA,+DACA,wEACA,CACA,CAAG,EACH,mBACA,kBACA,QAcA,OAZA,4BACA,4BAGA,gDACA,yBAGA,wBACA,MAGA,CACA,CACA,CAAG,EACH,kBACA,iBACA,WAEA,6CACA,kDAEA,GACA,MACA,gBACA,0BACA,CAAW,CACX,YACA,wDACA,0DACA,4DACA,OACA,yBAEA,EACA,wCAA0E,IAC1E,cACA,UACA,CAAS,IACT,CAAO,CACP,CACA,CAAG,EACH,aACA,iBACA,WAEA,wBACA,YAGA,8CAEA,CAFwE,CAExE,8CAEA,CAFuE,CAEvE,oEAEA,CAF6F,CAE7F,GACA,iDACA,wCACA,iCAeA,OAdA,GACA,kBACA,eACA,cACA,aACA,YACA,cACA,2BACA,wBACA,uBACA,sBACA,qBACA,sBACA,EACA,+BACA,gCACA,CAAO,gCACP,gCACA,4BACO,mCACP,cACA,mCACA,mBACA,qBACA,CAAS,CACT,yCACO,uCACP,aACA,kDACA,2BACA,6BACA,6BACA,+BACA,2BACA,QACA,8BACA,yCACO,mDACP,kDACA,gBACA,2BACA,CAAS,CACT,OACA,CAAO,uDACP,cACA,mCACA,mBACA,oBACA,CAAS,CACT,yCACA,CAAO,GACP,CACA,CAAG,EA9UH,cAA4C,YAAgB,WAAkB,KAAO,aAA2B,4BAAwD,kBAAgC,6BAAuD,mCA0K/P,EAxK8D,aA8U9D,CACA,CAAC,QA/UmI,IA+UnI,EAED,SAAe,GAEf,4BAEA,gBArVkM,GAqVlM,CACA,kBACA,QACA,kCACA,+BACA,iBACA,CAAG,CACH,eACA,cACA,kBACA,CAAC,0DG9XD,qCAA6C,CAC7C,QACA,CAAC,EAAC,EACF,WAAmB,CAAG,aAAmB,CAAG,iBAAuB,CAAG,wBAA8B,CAAG,MAAY,QAEnH,MAAa,EAAQ,KAAO,EAE5B,EAEA,QAJoB,CAIpB,GAAuC,0BAAuC,YAFnC,EAAQ,KAAoB,EAMvE,OAAY,CAFZ,EAJkD,SAIlD,EAQA,wBAA8B,CAJ9B,cACA,mCACA,EAaA,iBAAuB,CATvB,YACA,kCACA,EA2CA,aAAmB,CAlCnB,cAMA,GALA,gBAEA,IAGA,MACA,SAGA,mCAEA,wCACA,iCACA,MAQA,OANA,2BACA,mCACM,OACN,iCAGA,CACA,CAEA,cACA,EAmBA,aAAmB,CATnB,cACA,SAKA,MAJA,4GAEA,2BACA,CAAG,EACH,CACA,gCCxEA,6BAA2C,CAC3C,cACA,eACA,iBAEA,CAAC,EAAC,IAcF,IAAuC,EAAQ,KAAuB,GAMtE,cAAuC,0BAAuC,WAJjE,EAAQ,KAA6B,EAElD,EAAqC,EAAQ,KAAqB,CAF9C,WAEwB,kBC5B5C,QAYC,gBACD,aAEA,sCACA,QACA,CAAG,EACH,+BAuFA,YACA,GACA,EAvFA,WAEA,OAEA,cACA,0BACA,SACA,CACA,CAEA,iCACA,YAAoB,mBAAsB,KAC1C,mBAEA,eACA,2CACA,WAGA,CAEA,QACA,EAoBA,aACA,gBACA,YAAsB,WAAkB,KACxC,WACA,8BACA,kBACA,6BACA,gCACA,CACA,CAEA,uBAGA,OAFA,oBACA,UACA,CACA,CACA,CAAG,GA0BH,KAKA,IACA,qDAA2D,YAAe,eAJ1E,EAKA,EACA,EAAS,EACT,CAAI,UAEJ,CAFiB,QAEjB,IACA,8DAAwF,YAExF,qBAQA,cACA,kBACA,mBAIA,OAAe,EAHf,QAGe,EAFf,QAGA,CAKA,OAAa,EAHb,UAGa,EAFb,UAGA,CAEA,kBApDA,sBAqDA,GArDA,OAqDA,EApDA,kFAoDA,GAEA,aAvFA,KA0FA,iBA1FA,EACA,qDA2FA,QAJA,EAIA,kCAAsE,IAAa,IACnF,kBAGA,MAzEA,cACA,MACA,kFAGA,wDACA,EAmEA,+EAWA,OATA,gDACA,8CACA,4CAEA,sCACA,sCACA,kCAEA,wCACA,CACA,CA+JA,OAtLA,EAjDA,wBAiDA,KAjDA,WACA,aACA,MA+CA,EA9CA,cACA,YACA,eACA,CACA,CAAK,EACL,gDAyCA,OAzCA,UAyCA,CAzCA,EAkEA,MACA,wBACA,iBACA,aACA,kEACA,WACA,UACA,CAAW,EAEX,CACA,CAAK,EACL,2BACA,iBACA,aACA,qEACA,WACA,UACA,CAAW,EAEX,CACA,CAAK,EACL,mBACA,kBACA,8BAIA,kBAEA,qDACA,yDAEA,0BACA,CACA,CAAK,EACL,mBACA,kBACA,gBAIA,wBACA,CACA,CAAK,EACL,iBACA,kBACA,kBAEA,wDACA,4DAEA,uBACA,CACA,CAAK,EACL,wBACA,kBACA,WACA,MACA,MAEA,gBAA2B,SAC3B,0BACA,CACA,CAAK,EACL,uBACA,kBACA,mBAIA,WACA,MACA,MAEA,qBACA,qBACA,eAIA,wBACA,IACA,GACA,CAAS,KAET,cACA,mBAGA,mBAA8B,mBAC9B,CACA,CAAK,EACL,sBACA,kBACA,yBAEA,2BAGA,iCACA,4BACA,4BACY,4BACZ,6BAEA,4BACA,0BACY,4BACZ,6BAIA,oBACA,eACA,sBACA,CACA,CAAK,EACL,oBACA,kBACA,cACA,sBACA,CACA,CAAK,EACL,aACA,iBACA,iBAEA,GADA,UACA,aACA,UACA,aAWA,GAVA,mBACA,YACA,cACA,cACA,eACA,eACA,cACA,aACA,WACA,YACA,SAvQA,KACA,SAEA,gBACA,mBACA,2CACA,YAGA,QACA,EA6PA,0LAEA,+BACA,mBACA,GACA,uBACA,8BACA,oCACA,gCACA,YACA,OACA,CAAW,IACX,EAEA,CACA,CAAK,GAEL,CACA,CAAG,aAEH,4BACA,aACA,yBACA,2BACA,uBACA,wBACA,gCACA,yBACA,2BACA,2BACA,4BACA,4BACA,2BACA,0BACA,wBACA,uCAEA,gBACA,cACA,oBACA,sBAAsC,CACtC,wBAA0C,CAC1C,wBAA0C,CAC1C,yBAA4C,CAC5C,yBAA4C,CAC5C,wBAA0C,CAC1C,uBAAwC,CACxC,qBAAoC,CAEpC,WACA,EACA,WACA,CAAC,CArWG,EAAO,CAAC,EAAW,KAAF,GAAS,CAAE,QAAY,CAAC,CAAS,0CAAC,6BCAvD,qCAA6C,CAC7C,QACA,CAAC,EAAC,EACF,OAAe,QAEf,eAoBA,GAAwC,mBAA6B,SAAc,mDAA8E,OAAS,WAAkB,UAAwC,eAA+B,gBAAyB,SAAiB,yDAAsF,eAAuB,8CAAsD,iDAAqF,mBAAsC,6BAAmD,MAAP,CAAO,GAA2F,OAA7D,YAAsB,KAAa,SAA0B,GApBprB,EAAQ,KAAO,GAEpD,IAA6C,EAAQ,GAFT,CAE2B,GAEvE,IAAyC,EAAQ,IAAkB,GAEnE,IAAqC,EAAQ,IAFG,CAEQ,GAExD,IAAuC,EAAQ,GAFH,EAEyB,GAErE,IAAqC,EAAQ,GAFC,EAEmB,GAEjE,EAAa,EAAQ,KAFuB,EAI5C,EAAkB,EAAQ,KAAc,CAFpB,CAIpB,UAFyB,CAEzB,GAAuC,0BAAuC,WAE9E,aAAsC,0CAAgD,kBAA6G,OAAlF,aAAiE,UAAiB,EAInM,cAAoW,EAA5U,IAA4U,CAAtO,EAA3E,4DAA2E,YAAkC,iBAA+B,GAAP,MAAO,GAAkC,sGAAmI,GAEpW,aAAoR,OAA9P,6BAAgD,YAAgB,mBAAsB,KAAO,mBAA2B,eAA0B,4CAAyD,WAAiC,WAAkB,sBAEpR,gBAA2C,qBAAgC,iCAAoC,qCAAoD,4BAA8D,wDAAiE,EAAG,kBAAkC,SAEvU,cAAiC,YAAgB,mBAAsB,KAAO,wCAAuD,KAAa,oCAAuD,YAA4C,EAAK,iCAA6C,+DAAoF,QAAP,CAAO,wBAAiD,gEAAmF,EAAO,SAUtgB,gBAA2I,MAA1G,wCAA6F,OAAjB,cAAiB,IAAa,KAM3I,cAAwC,cAAuB,kFAAyF,SAIxJ,cAAiL,MAAnJ,2DAAgG,+CAAmD,GAEjL,kBAAoM,EAAxJ,YAAkB,2BAAkC,kDAAoE,EAAY,GAAP,CAAO,GAAoB,EAEpM,kBAhB2C,sBAiB3C,GAjB2C,OAiB3C,EAjB0G,qEAA6E,CAiBvL,EAjBuL,wBAiBvL,GAjBuL,aAAyE,aAAe,MAiB/Q,EAjB+Q,6BAAuD,EAAG,KAiBzU,KAEA,IAfiC,EANjC,EAqBA,GAfiC,EAMjC,WAAuC,kBANuD,KAMvD,6BAAwE,uBAAxE,SAAkH,KAA1C,UAA0C,sBAA8C,IAAiF,OAA3E,mEAAuE,GAAI,GAAe,SAAY,aANrN,WAAyC,IAEvI,IAFuI,MAevI,EAfqL,CAA+L,OAA3G,EAApF,EAAoF,2BAAnD,kBAA8G,IAA9G,EAAqH,wBAE3U,EAFoX,KAElU,CAAlD,EAFoX,IAElU,wCAA0E,EAAe,IAFyO,EAEzO,CAgB3I,cA5BkD,SA+BlD,aA/BkD,EAA0C,qDAmC5F,IAFA,kBAEA,oBAEA,oCAEA,yBAEA,0BAEA,uBAEA,kCAEA,kCACA,aACA,CAAK,EAEL,2CACA,sBACA,CAAK,EAEL,gCACA,WACA,CAAK,EAEL,mCACA,YACA,gBAGA,eACA,CAAK,EAEL,8BACA,0CAIA,kBAEA,kBAIA,+BACA,aACA,CAAO,oBACP,CAAK,EAEL,kCACA,8BACA,CAAK,EAEL,kCACA,kBAEA,YACA,CAAK,EAEL,gCACA,YACA,iBACA,CAAO,iBACP,CAAK,EAEL,iCACA,YACA,iBACA,CAAO,YACP,CAAK,EAEL,mDACA,0BAIA,oHAKA,CAAK,EAEL,0CACA,iCAKA,qBADA,aAEA,GACA,WACA,cACA,aACA,YACA,EACA,6BACA,0BAEA,cACA,cACQ,eACR,cAEA,CAAK,EAEL,gCACA,2DAIA,kCACA,gBAEA,MAIA,qCAEA,YACA,UACA,CAAO,EAEP,aACA,2BAEA,CAAK,EAEL,kCACA,YACA,WACA,CAAO,EAEP,eACA,CAAK,EAEL,uCACA,2CAIA,mCACA,YACA,cACA,CAAS,EAKT,yBAEA,0BACA,YACA,cACA,CAAS,EAET,CAAK,EAEL,sCACA,uCAIA,qBACA,CAAK,EAEL,wCACA,0BAEA,WACA,CAAK,EAEL,kCACA,YACA,UACA,CAAO,EAEP,uBACA,CAAK,EAEL,gCACA,YACA,WACA,eACA,uBACA,CAAO,EAEP,sBAEA,kBAEA,kBACA,YAEA,CAAK,EAEL,mCACA,uBAEA,6EAMA,OAJA,eAAqC,KAIrC,wBACK,EAEL,8BACA,+DAEA,uDACA,CAAK,EAEL,8BACA,+DAEA,uDACA,CAAK,EAEL,4BACA,uBAIA,0CAEA,MACA,6BAGA,KACA,6BAGA,cAEA,cACA,CAAO,EAIP,CAJU,CAIV,6CACA,kBAEA,CAAK,EAEL,gCACA,cACA,CAAK,EAEL,gCACA,cACA,CAAK,EAEL,mCACA,eAEA,sBACA,YACA,cACA,CAAS,CAET,CAAK,EAEL,qCACA,eAEA,sBACA,YACA,cACA,CAAS,CAET,CAAK,EAEL,gCACA,mBACA,2CACA,WAEA,CACA,CAAK,EAEL,gCAEA,cACA,iCACA,CAAO,eAEP,+CACA,CAAO,EAEP,qFACA,CAAK,EAEL,oCACA,2BACA,4BAEA,MADA,sCACA,IACK,EAEL,2CACA,gCAEA,2CACA,oDAEA,eACA,WAEA,YAQA,0BANA,aACA,gBAEA,+BACA,EAIA,CAIA,CAJU,GAIV,EADA,sBACA,aACA,iBACA,CAEA,WACA,CAAK,EAEL,IAlVA,EAkVA,GACA,eACA,4BACA,4BACA,YACA,kBACA,oBACA,WACA,wBACA,eACA,WACA,gBAAuB,CACvB,aAAoB,CACpB,gBAAuB,CACvB,YACA,EAGA,OAFA,2JACA,cAAgD,6BAChD,CACA,CAoTA,OAlrBA,EAgYA,EACA,uBAjY8D,CAkY9D,iBACA,qBAIA,oBACA,CACA,CAAG,EACH,yBACA,oBACA,0DACA,qBAGA,oCACA,kBAGA,gCAEA,kBAAsC,wDAGtC,mFACA,mBACA,sCAGA,mCACA,oBACA,qBAEA,uBAGA,eACA,6BACS,EAET,CACA,CAAG,EACH,2BACA,iBACA,sBACA,CACA,CAAG,EACH,oBACA,iBACA,WAEA,kBAEA,8DACA,qBAGA,sBACA,kBAGA,eACA,cACA,CAAO,YACP,yBAEA,gBAEA,2CAEA,iBAEA,CAAO,CACP,CACA,CAAG,EACH,sBACA,iBACA,yBACA,oBACA,uBAEA,CACA,CAAG,EACH,oBACA,iBACA,gBACA,8BAEA,4BACA,kDACA,mDAEA,CACA,CAAG,EACH,sBACA,iBACA,qBACA,8BAEA,4BACA,qDACA,sDAEA,CACA,CAAG,EACH,iBACA,iBAGA,4DAEA,CAF2E,EAE3E,mEAEA,8BACA,qEAEA,CACA,CAAG,EACH,mBACA,iBAEA,+DACA,yEACA,4BAEA,IACA,iDAGA,8BACA,wEAEA,CACA,CAAG,EACH,iBACA,iBACA,KAEA,6CACA,CACA,CAAG,EACH,kBACA,kBACA,kBAEA,oBAIA,iDACA,+BACA,2BACA,wEAEA,kDACA,SAAgD,MAAY,EAC5D,0CACA,EAAW,EAGX,+CACA,SAAgD,MAAY,EAC5D,oBACA,EAAW,EAGX,OACA,gBACA,yBACA,CAAW,CACX,+BACA,+EACA,sCACA,OACA,EACA,4DACA,oCACA,oCACS,EACT,CAAO,EAjCP,GAmCA,CAAG,EACH,qBACA,iBACA,WAEA,aACA,mBACA,WACA,oBACA,oBAEA,EAIA,8BACA,wBACA,CAAO,gCACP,8DACA,CAAO,GAPP,IAQA,CACA,CAAG,EACH,mBACA,wBACA,sBAIA,6BACA,2BACA,CAAO,8FALP,IAMA,CACA,CAAG,EACH,mBACA,wBACA,sFAIA,mCACA,sBACA,mCACA,qCACA,yCACA,iCACA,yBACA,qCACO,+CAXP,IAYA,CACA,CAAG,EACH,aACA,iBACA,WAEA,mEACA,YAGA,oEACA,iCACA,iEAEA,CAFmG,CAEnG,4DAEA,KAFwG,CAExG,8FACA,uBACA,YACA,UACA,GACA,kDACA,6BACA,+BACA,2BACA,+BACA,2CAEA,KAoBA,OAlBA,GACA,kCACA,qCAEA,0BAGA,UAFA,qDAAgF,MAEhF,IAGA,2FACA,6FACA,cAA0D,YAAwB,EAClF,2BACS,EACT,8BAGA,+BACA,kCACA,+CACA,+BACA,8CACA,CAAO,gCACP,iCACA,OACA,sBACA,CACA,CAAO,iIACP,gDACA,OACA,CAAO,wCACP,aACA,yBACO,IACP,yCACO,0GACP,kDACA,gBACA,sBACA,CAAS,CACT,kCACA,CAAO,iMACP,CACA,CAAG,EAhrB2D,SAF9D,KAA4C,YAAgB,WAAkB,KAAO,WAA2B,8BAAwD,kBAAgC,6BAAuD,mCAkY/P,EAhY8D,aAkrB9D,CACA,CAAC,QAnrBmI,EAmrBnI,YAED,SAAe,GAEf,8BAEA,MAzrBkM,aAyrBlM,CACA,iBACA,kBACA,yBACA,aACA,QACA,kCACA,+BACA,iBACA,CAAG,CACH,mBACA,oBACA,gBACA,yBAA0C,CAC1C,uBAAsC,CACtC,uBACA,QACA,CAAG,CACH,4CACA,gCACA,yCACA,cACA,eACA,mCACA,SACA,CAAK,CACL,CAAG,CACH,gCACA,yCACA,cACA,eACA,mCACA,SACA,CAAK,CACL,CAAG,CACH,kCACA,qCACA,2BACA,UACA,YACA,QACA,MACA,cACA,WACA,yCACA,CAAK,CACL,CAAG,CACH,uBACA,QACA,CAAG,CACH,yBACA,mCACA,QAQA,GANA,CAFsB,OAEtB,QACA,yDACA,sBACS,EAGT,EAIA,QACA,CAAK,SAEL,yBACA,QACA,CAAK,UACL,oHAA0H,OAAO,mMACjI,IAGA,CACA,CAAG,CACH,yCACA,eACA,cACA,kBACA,cACA,cACA,eACA,uBACA,aACA,mBACA,yBACA,aACA,yBACA,mDACA,6CACC,8BC3zBD,qCAA6C,CAC7C,QACA,CAAC,EAAC,EACF,UAAkB,QASlB,YAAkB,CAPlB,YACA,oBACA,sBAEA,OADA,kDAEA,8BCVA,qCAA6C,CAC7C,QACA,CAAC,EAAC,EACF,OAAe,QAMf,SAAe,CAJf,WACA,eACA,8BCPA,qCAA6C,CAC7C,QACA,CAAC,EAAC,EACF,OAAe,QAMf,SAAe,CAJf,WACA,aACA,8BCPA,qCAA6C,CAC7C,QACA,CAAC,EAAC,EACF,OAAe,QAUf,SAAe,CARf,gBACA,kBAIA,qBADA,CAFA,kCAEA,aAEA,gCCXA,qCAA6C,CAC7C,QACA,CAAC,EAAC,EACF,oBAA4B,CAAG,yBAA+B,CAAG,4BAAkC,CAAG,uBAA6B,QAEnI,MAAa,EAAQ,KAAO,EAE5B,UAFoB,CAMpB,GAAuC,0BAAuC,YAJnC,EAAQ,KAAoB,GAEvE,EAAa,EAAQ,KAF6B,EAMlD,UAJoB,CAIpB,KAA2C,qBAAgC,iCAAoC,qCAAoD,4BAA8D,wDAAiE,EAAG,kBAAkC,SAEvU,cAAiC,YAAgB,mBAAsB,KAAO,wCAAuD,KAAa,sCAAuD,EAEzM,QAFyM,EAEzM,EAFyM,CAE7J,CAA5C,EAFyM,KAE7J,OAAkB,2BAAkC,kDAAoE,EAAY,GAAP,CAAO,EAFyB,CAA4C,EAAK,KAEtD,EAFsD,0BAA6C,+DAAoF,QAAP,CAAO,wBAAiD,gEAAmF,EAAO,SA8DtgB,uBAA6B,CArD7B,cACA,SACA,iBAEA,iCAGA,GAFA,2BAaA,EAbiG,KAGjG,IACA,6DACA,yGAEA,uDATA,EAWM,GACN,8CAGA,EAGA,6BAEA,KAFsE,EAEtE,uBACA,wBAkBA,OAjBA,iBACA,kBACA,cACA,aACA,WACA,EAEA,WACA,uBAA+D,oBAAiC,EAChG,2BACA,wBACA,sBACA,qBACA,sBACA,EAAK,EAGL,CACA,EAwEA,4BAAkC,CA5DlC,kBACA,SACA,wBAEA,+BAGA,sCACA,uEACA,YACA,GAJA,KAMA,QACA,MAIA,YACA,MAGA,2BACA,qCA6BA,OA3BA,oBAGA,2BACA,SACM,kCACN,YAIA,2EACA,wBACA,GACA,uBACA,CAAO,EAGP,6CAIA,mBACA,GACA,cACA,CAAK,EAGL,CACA,EAwBA,yBAA+B,CAd/B,cACA,0CAEA,OACA,cAFA,2BAGA,CACA,EAkDA,sBAA4B,CAxC5B,cACA,4BACA,gBACA,GACA,oBACA,gBACA,UACA,iBACA,UACA,MACA,QACA,OACA,SACA,2BACA,6BACA,8BACA,iCACA,2BACA,EAYA,OAVA,WACA,SAA+C,MAAiB,EAChE,2BACA,wBACA,sBACA,qBACA,sBACA,EAAK,EAGL,CACA,aACA,oBAAiD,MAAiB,EAClE,UACA,mBACA,CAAK,EACL,cAA+B,GAC/B,CACA", "sources": ["webpack://_N_E/./node_modules/react-easy-swipe/lib/index.js", "webpack://_N_E/./node_modules/react-responsive-carousel/lib/js/cssClasses.js", "webpack://_N_E/./node_modules/react-responsive-carousel/lib/js/components/Thumbs.js", "webpack://_N_E/./node_modules/react-responsive-carousel/lib/js/components/Carousel/types.js", "webpack://_N_E/./node_modules/react-responsive-carousel/lib/styles/carousel.min.css", "webpack://_N_E/./node_modules/react-responsive-carousel/lib/js/components/Carousel/utils.js", "webpack://_N_E/./node_modules/react-responsive-carousel/lib/js/index.js", "webpack://_N_E/./node_modules/react-easy-swipe/lib/react-swipe.js", "webpack://_N_E/./node_modules/react-responsive-carousel/lib/js/components/Carousel/index.js", "webpack://_N_E/./node_modules/react-responsive-carousel/lib/js/dimensions.js", "webpack://_N_E/./node_modules/react-responsive-carousel/lib/js/shims/document.js", "webpack://_N_E/./node_modules/react-responsive-carousel/lib/js/shims/window.js", "webpack://_N_E/./node_modules/react-responsive-carousel/lib/js/CSSTranslate.js", "webpack://_N_E/./node_modules/react-responsive-carousel/lib/js/components/Carousel/animations.js"], "sourcesContent": ["(function (global, factory) {\n  if (typeof define === \"function\" && define.amd) {\n    define(['exports', './react-swipe'], factory);\n  } else if (typeof exports !== \"undefined\") {\n    factory(exports, require('./react-swipe'));\n  } else {\n    var mod = {\n      exports: {}\n    };\n    factory(mod.exports, global.reactSwipe);\n    global.index = mod.exports;\n  }\n})(this, function (exports, _reactSwipe) {\n  'use strict';\n\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n\n  var _reactSwipe2 = _interopRequireDefault(_reactSwipe);\n\n  function _interopRequireDefault(obj) {\n    return obj && obj.__esModule ? obj : {\n      default: obj\n    };\n  }\n\n  exports.default = _reactSwipe2.default;\n});", "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = void 0;\n\nvar _classnames = _interopRequireDefault(require(\"classnames\"));\n\nfunction _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }\n\nfunction _defineProperty(obj, key, value) { if (key in obj) { Object.defineProperty(obj, key, { value: value, enumerable: true, configurable: true, writable: true }); } else { obj[key] = value; } return obj; }\n\nvar _default = {\n  ROOT: function ROOT(customClassName) {\n    return (0, _classnames.default)(_defineProperty({\n      'carousel-root': true\n    }, customClassName || '', !!customClassName));\n  },\n  CAROUSEL: function CAROUSEL(isSlider) {\n    return (0, _classnames.default)({\n      carousel: true,\n      'carousel-slider': isSlider\n    });\n  },\n  WRAPPER: function WRAPPER(isSlider, axis) {\n    return (0, _classnames.default)({\n      'thumbs-wrapper': !isSlider,\n      'slider-wrapper': isSlider,\n      'axis-horizontal': axis === 'horizontal',\n      'axis-vertical': axis !== 'horizontal'\n    });\n  },\n  SLIDER: function SLIDER(isSlider, isSwiping) {\n    return (0, _classnames.default)({\n      thumbs: !isSlider,\n      slider: isSlider,\n      animated: !isSwiping\n    });\n  },\n  ITEM: function ITEM(isSlider, selected, previous) {\n    return (0, _classnames.default)({\n      thumb: !isSlider,\n      slide: isSlider,\n      selected: selected,\n      previous: previous\n    });\n  },\n  ARROW_PREV: function ARROW_PREV(disabled) {\n    return (0, _classnames.default)({\n      'control-arrow control-prev': true,\n      'control-disabled': disabled\n    });\n  },\n  ARROW_NEXT: function ARROW_NEXT(disabled) {\n    return (0, _classnames.default)({\n      'control-arrow control-next': true,\n      'control-disabled': disabled\n    });\n  },\n  DOT: function DOT(selected) {\n    return (0, _classnames.default)({\n      dot: true,\n      selected: selected\n    });\n  }\n};\nexports.default = _default;", "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = void 0;\n\nvar _react = _interopRequireWildcard(require(\"react\"));\n\nvar _cssClasses = _interopRequireDefault(require(\"../cssClasses\"));\n\nvar _dimensions = require(\"../dimensions\");\n\nvar _CSSTranslate = _interopRequireDefault(require(\"../CSSTranslate\"));\n\nvar _reactEasySwipe = _interopRequireDefault(require(\"react-easy-swipe\"));\n\nvar _window = _interopRequireDefault(require(\"../shims/window\"));\n\nfunction _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }\n\nfunction _getRequireWildcardCache() { if (typeof WeakMap !== \"function\") return null; var cache = new WeakMap(); _getRequireWildcardCache = function _getRequireWildcardCache() { return cache; }; return cache; }\n\nfunction _interopRequireWildcard(obj) { if (obj && obj.__esModule) { return obj; } if (obj === null || _typeof(obj) !== \"object\" && typeof obj !== \"function\") { return { default: obj }; } var cache = _getRequireWildcardCache(); if (cache && cache.has(obj)) { return cache.get(obj); } var newObj = {}; var hasPropertyDescriptor = Object.defineProperty && Object.getOwnPropertyDescriptor; for (var key in obj) { if (Object.prototype.hasOwnProperty.call(obj, key)) { var desc = hasPropertyDescriptor ? Object.getOwnPropertyDescriptor(obj, key) : null; if (desc && (desc.get || desc.set)) { Object.defineProperty(newObj, key, desc); } else { newObj[key] = obj[key]; } } } newObj.default = obj; if (cache) { cache.set(obj, newObj); } return newObj; }\n\nfunction _typeof(obj) { \"@babel/helpers - typeof\"; if (typeof Symbol === \"function\" && typeof Symbol.iterator === \"symbol\") { _typeof = function _typeof(obj) { return typeof obj; }; } else { _typeof = function _typeof(obj) { return obj && typeof Symbol === \"function\" && obj.constructor === Symbol && obj !== Symbol.prototype ? \"symbol\" : typeof obj; }; } return _typeof(obj); }\n\nfunction _extends() { _extends = Object.assign || function (target) { for (var i = 1; i < arguments.length; i++) { var source = arguments[i]; for (var key in source) { if (Object.prototype.hasOwnProperty.call(source, key)) { target[key] = source[key]; } } } return target; }; return _extends.apply(this, arguments); }\n\nfunction _classCallCheck(instance, Constructor) { if (!(instance instanceof Constructor)) { throw new TypeError(\"Cannot call a class as a function\"); } }\n\nfunction _defineProperties(target, props) { for (var i = 0; i < props.length; i++) { var descriptor = props[i]; descriptor.enumerable = descriptor.enumerable || false; descriptor.configurable = true; if (\"value\" in descriptor) descriptor.writable = true; Object.defineProperty(target, descriptor.key, descriptor); } }\n\nfunction _createClass(Constructor, protoProps, staticProps) { if (protoProps) _defineProperties(Constructor.prototype, protoProps); if (staticProps) _defineProperties(Constructor, staticProps); return Constructor; }\n\nfunction _inherits(subClass, superClass) { if (typeof superClass !== \"function\" && superClass !== null) { throw new TypeError(\"Super expression must either be null or a function\"); } subClass.prototype = Object.create(superClass && superClass.prototype, { constructor: { value: subClass, writable: true, configurable: true } }); if (superClass) _setPrototypeOf(subClass, superClass); }\n\nfunction _setPrototypeOf(o, p) { _setPrototypeOf = Object.setPrototypeOf || function _setPrototypeOf(o, p) { o.__proto__ = p; return o; }; return _setPrototypeOf(o, p); }\n\nfunction _createSuper(Derived) { var hasNativeReflectConstruct = _isNativeReflectConstruct(); return function _createSuperInternal() { var Super = _getPrototypeOf(Derived), result; if (hasNativeReflectConstruct) { var NewTarget = _getPrototypeOf(this).constructor; result = Reflect.construct(Super, arguments, NewTarget); } else { result = Super.apply(this, arguments); } return _possibleConstructorReturn(this, result); }; }\n\nfunction _possibleConstructorReturn(self, call) { if (call && (_typeof(call) === \"object\" || typeof call === \"function\")) { return call; } return _assertThisInitialized(self); }\n\nfunction _assertThisInitialized(self) { if (self === void 0) { throw new ReferenceError(\"this hasn't been initialised - super() hasn't been called\"); } return self; }\n\nfunction _isNativeReflectConstruct() { if (typeof Reflect === \"undefined\" || !Reflect.construct) return false; if (Reflect.construct.sham) return false; if (typeof Proxy === \"function\") return true; try { Date.prototype.toString.call(Reflect.construct(Date, [], function () {})); return true; } catch (e) { return false; } }\n\nfunction _getPrototypeOf(o) { _getPrototypeOf = Object.setPrototypeOf ? Object.getPrototypeOf : function _getPrototypeOf(o) { return o.__proto__ || Object.getPrototypeOf(o); }; return _getPrototypeOf(o); }\n\nfunction _defineProperty(obj, key, value) { if (key in obj) { Object.defineProperty(obj, key, { value: value, enumerable: true, configurable: true, writable: true }); } else { obj[key] = value; } return obj; }\n\nvar isKeyboardEvent = function isKeyboardEvent(e) {\n  return e.hasOwnProperty('key');\n};\n\nvar Thumbs = /*#__PURE__*/function (_Component) {\n  _inherits(Thumbs, _Component);\n\n  var _super = _createSuper(Thumbs);\n\n  function Thumbs(_props) {\n    var _this;\n\n    _classCallCheck(this, Thumbs);\n\n    _this = _super.call(this, _props);\n\n    _defineProperty(_assertThisInitialized(_this), \"itemsWrapperRef\", void 0);\n\n    _defineProperty(_assertThisInitialized(_this), \"itemsListRef\", void 0);\n\n    _defineProperty(_assertThisInitialized(_this), \"thumbsRef\", void 0);\n\n    _defineProperty(_assertThisInitialized(_this), \"setItemsWrapperRef\", function (node) {\n      _this.itemsWrapperRef = node;\n    });\n\n    _defineProperty(_assertThisInitialized(_this), \"setItemsListRef\", function (node) {\n      _this.itemsListRef = node;\n    });\n\n    _defineProperty(_assertThisInitialized(_this), \"setThumbsRef\", function (node, index) {\n      if (!_this.thumbsRef) {\n        _this.thumbsRef = [];\n      }\n\n      _this.thumbsRef[index] = node;\n    });\n\n    _defineProperty(_assertThisInitialized(_this), \"updateSizes\", function () {\n      if (!_this.props.children || !_this.itemsWrapperRef || !_this.thumbsRef) {\n        return;\n      }\n\n      var total = _react.Children.count(_this.props.children);\n\n      var wrapperSize = _this.itemsWrapperRef.clientWidth;\n      var itemSize = _this.props.thumbWidth ? _this.props.thumbWidth : (0, _dimensions.outerWidth)(_this.thumbsRef[0]);\n      var visibleItems = Math.floor(wrapperSize / itemSize);\n      var showArrows = visibleItems < total;\n      var lastPosition = showArrows ? total - visibleItems : 0;\n\n      _this.setState(function (_state, props) {\n        return {\n          itemSize: itemSize,\n          visibleItems: visibleItems,\n          firstItem: showArrows ? _this.getFirstItem(props.selectedItem) : 0,\n          lastPosition: lastPosition,\n          showArrows: showArrows\n        };\n      });\n    });\n\n    _defineProperty(_assertThisInitialized(_this), \"handleClickItem\", function (index, item, e) {\n      if (!isKeyboardEvent(e) || e.key === 'Enter') {\n        var handler = _this.props.onSelectItem;\n\n        if (typeof handler === 'function') {\n          handler(index, item);\n        }\n      }\n    });\n\n    _defineProperty(_assertThisInitialized(_this), \"onSwipeStart\", function () {\n      _this.setState({\n        swiping: true\n      });\n    });\n\n    _defineProperty(_assertThisInitialized(_this), \"onSwipeEnd\", function () {\n      _this.setState({\n        swiping: false\n      });\n    });\n\n    _defineProperty(_assertThisInitialized(_this), \"onSwipeMove\", function (delta) {\n      var deltaX = delta.x;\n\n      if (!_this.state.itemSize || !_this.itemsWrapperRef || !_this.state.visibleItems) {\n        return false;\n      }\n\n      var leftBoundary = 0;\n\n      var childrenLength = _react.Children.count(_this.props.children);\n\n      var currentPosition = -(_this.state.firstItem * 100) / _this.state.visibleItems;\n      var lastLeftItem = Math.max(childrenLength - _this.state.visibleItems, 0);\n      var lastLeftBoundary = -lastLeftItem * 100 / _this.state.visibleItems; // prevent user from swiping left out of boundaries\n\n      if (currentPosition === leftBoundary && deltaX > 0) {\n        deltaX = 0;\n      } // prevent user from swiping right out of boundaries\n\n\n      if (currentPosition === lastLeftBoundary && deltaX < 0) {\n        deltaX = 0;\n      }\n\n      var wrapperSize = _this.itemsWrapperRef.clientWidth;\n      var position = currentPosition + 100 / (wrapperSize / deltaX); // if 3d isn't available we will use left to move\n\n      if (_this.itemsListRef) {\n        ['WebkitTransform', 'MozTransform', 'MsTransform', 'OTransform', 'transform', 'msTransform'].forEach(function (prop) {\n          _this.itemsListRef.style[prop] = (0, _CSSTranslate.default)(position, '%', _this.props.axis);\n        });\n      }\n\n      return true;\n    });\n\n    _defineProperty(_assertThisInitialized(_this), \"slideRight\", function (positions) {\n      _this.moveTo(_this.state.firstItem - (typeof positions === 'number' ? positions : 1));\n    });\n\n    _defineProperty(_assertThisInitialized(_this), \"slideLeft\", function (positions) {\n      _this.moveTo(_this.state.firstItem + (typeof positions === 'number' ? positions : 1));\n    });\n\n    _defineProperty(_assertThisInitialized(_this), \"moveTo\", function (position) {\n      // position can't be lower than 0\n      position = position < 0 ? 0 : position; // position can't be higher than last postion\n\n      position = position >= _this.state.lastPosition ? _this.state.lastPosition : position;\n\n      _this.setState({\n        firstItem: position\n      });\n    });\n\n    _this.state = {\n      selectedItem: _props.selectedItem,\n      swiping: false,\n      showArrows: false,\n      firstItem: 0,\n      visibleItems: 0,\n      lastPosition: 0\n    };\n    return _this;\n  }\n\n  _createClass(Thumbs, [{\n    key: \"componentDidMount\",\n    value: function componentDidMount() {\n      this.setupThumbs();\n    }\n  }, {\n    key: \"componentDidUpdate\",\n    value: function componentDidUpdate(prevProps) {\n      if (this.props.selectedItem !== this.state.selectedItem) {\n        this.setState({\n          selectedItem: this.props.selectedItem,\n          firstItem: this.getFirstItem(this.props.selectedItem)\n        });\n      }\n\n      if (this.props.children === prevProps.children) {\n        return;\n      } // This will capture any size changes for arrow adjustments etc.\n      // usually in the same render cycle so we don't see any flickers\n\n\n      this.updateSizes();\n    }\n  }, {\n    key: \"componentWillUnmount\",\n    value: function componentWillUnmount() {\n      this.destroyThumbs();\n    }\n  }, {\n    key: \"setupThumbs\",\n    value: function setupThumbs() {\n      // as the widths are calculated, we need to resize\n      // the carousel when the window is resized\n      (0, _window.default)().addEventListener('resize', this.updateSizes); // issue #2 - image loading smaller\n\n      (0, _window.default)().addEventListener('DOMContentLoaded', this.updateSizes); // when the component is rendered we need to calculate\n      // the container size to adjust the responsive behaviour\n\n      this.updateSizes();\n    }\n  }, {\n    key: \"destroyThumbs\",\n    value: function destroyThumbs() {\n      // removing listeners\n      (0, _window.default)().removeEventListener('resize', this.updateSizes);\n      (0, _window.default)().removeEventListener('DOMContentLoaded', this.updateSizes);\n    }\n  }, {\n    key: \"getFirstItem\",\n    value: function getFirstItem(selectedItem) {\n      var firstItem = selectedItem;\n\n      if (selectedItem >= this.state.lastPosition) {\n        firstItem = this.state.lastPosition;\n      }\n\n      if (selectedItem < this.state.firstItem + this.state.visibleItems) {\n        firstItem = this.state.firstItem;\n      }\n\n      if (selectedItem < this.state.firstItem) {\n        firstItem = selectedItem;\n      }\n\n      return firstItem;\n    }\n  }, {\n    key: \"renderItems\",\n    value: function renderItems() {\n      var _this2 = this;\n\n      return this.props.children.map(function (img, index) {\n        var itemClass = _cssClasses.default.ITEM(false, index === _this2.state.selectedItem);\n\n        var thumbProps = {\n          key: index,\n          ref: function ref(e) {\n            return _this2.setThumbsRef(e, index);\n          },\n          className: itemClass,\n          onClick: _this2.handleClickItem.bind(_this2, index, _this2.props.children[index]),\n          onKeyDown: _this2.handleClickItem.bind(_this2, index, _this2.props.children[index]),\n          'aria-label': \"\".concat(_this2.props.labels.item, \" \").concat(index + 1),\n          style: {\n            width: _this2.props.thumbWidth\n          }\n        };\n        return /*#__PURE__*/_react.default.createElement(\"li\", _extends({}, thumbProps, {\n          role: \"button\",\n          tabIndex: 0\n        }), img);\n      });\n    }\n  }, {\n    key: \"render\",\n    value: function render() {\n      var _this3 = this;\n\n      if (!this.props.children) {\n        return null;\n      }\n\n      var isSwipeable = _react.Children.count(this.props.children) > 1; // show left arrow?\n\n      var hasPrev = this.state.showArrows && this.state.firstItem > 0; // show right arrow\n\n      var hasNext = this.state.showArrows && this.state.firstItem < this.state.lastPosition; // obj to hold the transformations and styles\n\n      var itemListStyles = {};\n      var currentPosition = -this.state.firstItem * (this.state.itemSize || 0);\n      var transformProp = (0, _CSSTranslate.default)(currentPosition, 'px', this.props.axis);\n      var transitionTime = this.props.transitionTime + 'ms';\n      itemListStyles = {\n        WebkitTransform: transformProp,\n        MozTransform: transformProp,\n        MsTransform: transformProp,\n        OTransform: transformProp,\n        transform: transformProp,\n        msTransform: transformProp,\n        WebkitTransitionDuration: transitionTime,\n        MozTransitionDuration: transitionTime,\n        MsTransitionDuration: transitionTime,\n        OTransitionDuration: transitionTime,\n        transitionDuration: transitionTime,\n        msTransitionDuration: transitionTime\n      };\n      return /*#__PURE__*/_react.default.createElement(\"div\", {\n        className: _cssClasses.default.CAROUSEL(false)\n      }, /*#__PURE__*/_react.default.createElement(\"div\", {\n        className: _cssClasses.default.WRAPPER(false),\n        ref: this.setItemsWrapperRef\n      }, /*#__PURE__*/_react.default.createElement(\"button\", {\n        type: \"button\",\n        className: _cssClasses.default.ARROW_PREV(!hasPrev),\n        onClick: function onClick() {\n          return _this3.slideRight();\n        },\n        \"aria-label\": this.props.labels.leftArrow\n      }), isSwipeable ? /*#__PURE__*/_react.default.createElement(_reactEasySwipe.default, {\n        tagName: \"ul\",\n        className: _cssClasses.default.SLIDER(false, this.state.swiping),\n        onSwipeLeft: this.slideLeft,\n        onSwipeRight: this.slideRight,\n        onSwipeMove: this.onSwipeMove,\n        onSwipeStart: this.onSwipeStart,\n        onSwipeEnd: this.onSwipeEnd,\n        style: itemListStyles,\n        innerRef: this.setItemsListRef,\n        allowMouseEvents: this.props.emulateTouch\n      }, this.renderItems()) : /*#__PURE__*/_react.default.createElement(\"ul\", {\n        className: _cssClasses.default.SLIDER(false, this.state.swiping),\n        ref: function ref(node) {\n          return _this3.setItemsListRef(node);\n        },\n        style: itemListStyles\n      }, this.renderItems()), /*#__PURE__*/_react.default.createElement(\"button\", {\n        type: \"button\",\n        className: _cssClasses.default.ARROW_NEXT(!hasNext),\n        onClick: function onClick() {\n          return _this3.slideLeft();\n        },\n        \"aria-label\": this.props.labels.rightArrow\n      })));\n    }\n  }]);\n\n  return Thumbs;\n}(_react.Component);\n\nexports.default = Thumbs;\n\n_defineProperty(Thumbs, \"displayName\", 'Thumbs');\n\n_defineProperty(Thumbs, \"defaultProps\", {\n  axis: 'horizontal',\n  labels: {\n    leftArrow: 'previous slide / item',\n    rightArrow: 'next slide / item',\n    item: 'slide item'\n  },\n  selectedItem: 0,\n  thumbWidth: 80,\n  transitionTime: 350\n});", "\"use strict\";", "// extracted by mini-css-extract-plugin", "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.setPosition = exports.getPosition = exports.isKeyboardEvent = exports.defaultStatusFormatter = exports.noop = void 0;\n\nvar _react = require(\"react\");\n\nvar _CSSTranslate = _interopRequireDefault(require(\"../../CSSTranslate\"));\n\nfunction _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }\n\nvar noop = function noop() {};\n\nexports.noop = noop;\n\nvar defaultStatusFormatter = function defaultStatusFormatter(current, total) {\n  return \"\".concat(current, \" of \").concat(total);\n};\n\nexports.defaultStatusFormatter = defaultStatusFormatter;\n\nvar isKeyboardEvent = function isKeyboardEvent(e) {\n  return e ? e.hasOwnProperty('key') : false;\n};\n/**\n * Gets the list 'position' relative to a current index\n * @param index\n */\n\n\nexports.isKeyboardEvent = isKeyboardEvent;\n\nvar getPosition = function getPosition(index, props) {\n  if (props.infiniteLoop) {\n    // index has to be added by 1 because of the first cloned slide\n    ++index;\n  }\n\n  if (index === 0) {\n    return 0;\n  }\n\n  var childrenLength = _react.Children.count(props.children);\n\n  if (props.centerMode && props.axis === 'horizontal') {\n    var currentPosition = -index * props.centerSlidePercentage;\n    var lastPosition = childrenLength - 1;\n\n    if (index && (index !== lastPosition || props.infiniteLoop)) {\n      currentPosition += (100 - props.centerSlidePercentage) / 2;\n    } else if (index === lastPosition) {\n      currentPosition += 100 - props.centerSlidePercentage;\n    }\n\n    return currentPosition;\n  }\n\n  return -index * 100;\n};\n/**\n * Sets the 'position' transform for sliding animations\n * @param position\n * @param forceReflow\n */\n\n\nexports.getPosition = getPosition;\n\nvar setPosition = function setPosition(position, axis) {\n  var style = {};\n  ['WebkitTransform', 'MozTransform', 'MsTransform', 'OTransform', 'transform', 'msTransform'].forEach(function (prop) {\n    // @ts-ignore\n    style[prop] = (0, _CSSTranslate.default)(position, '%', axis);\n  });\n  return style;\n};\n\nexports.setPosition = setPosition;", "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nObject.defineProperty(exports, \"Carousel\", {\n  enumerable: true,\n  get: function get() {\n    return _Carousel.default;\n  }\n});\nObject.defineProperty(exports, \"CarouselProps\", {\n  enumerable: true,\n  get: function get() {\n    return _types.CarouselProps;\n  }\n});\nObject.defineProperty(exports, \"Thumbs\", {\n  enumerable: true,\n  get: function get() {\n    return _Thumbs.default;\n  }\n});\n\nvar _Carousel = _interopRequireDefault(require(\"./components/Carousel\"));\n\nvar _types = require(\"./components/Carousel/types\");\n\nvar _Thumbs = _interopRequireDefault(require(\"./components/Thumbs\"));\n\nfunction _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }", "(function (global, factory) {\n  if (typeof define === \"function\" && define.amd) {\n    define(['exports', 'react', 'prop-types'], factory);\n  } else if (typeof exports !== \"undefined\") {\n    factory(exports, require('react'), require('prop-types'));\n  } else {\n    var mod = {\n      exports: {}\n    };\n    factory(mod.exports, global.react, global.propTypes);\n    global.reactSwipe = mod.exports;\n  }\n})(this, function (exports, _react, _propTypes) {\n  'use strict';\n\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.setHasSupportToCaptureOption = setHasSupportToCaptureOption;\n\n  var _react2 = _interopRequireDefault(_react);\n\n  var _propTypes2 = _interopRequireDefault(_propTypes);\n\n  function _interopRequireDefault(obj) {\n    return obj && obj.__esModule ? obj : {\n      default: obj\n    };\n  }\n\n  var _extends = Object.assign || function (target) {\n    for (var i = 1; i < arguments.length; i++) {\n      var source = arguments[i];\n\n      for (var key in source) {\n        if (Object.prototype.hasOwnProperty.call(source, key)) {\n          target[key] = source[key];\n        }\n      }\n    }\n\n    return target;\n  };\n\n  function _objectWithoutProperties(obj, keys) {\n    var target = {};\n\n    for (var i in obj) {\n      if (keys.indexOf(i) >= 0) continue;\n      if (!Object.prototype.hasOwnProperty.call(obj, i)) continue;\n      target[i] = obj[i];\n    }\n\n    return target;\n  }\n\n  function _classCallCheck(instance, Constructor) {\n    if (!(instance instanceof Constructor)) {\n      throw new TypeError(\"Cannot call a class as a function\");\n    }\n  }\n\n  var _createClass = function () {\n    function defineProperties(target, props) {\n      for (var i = 0; i < props.length; i++) {\n        var descriptor = props[i];\n        descriptor.enumerable = descriptor.enumerable || false;\n        descriptor.configurable = true;\n        if (\"value\" in descriptor) descriptor.writable = true;\n        Object.defineProperty(target, descriptor.key, descriptor);\n      }\n    }\n\n    return function (Constructor, protoProps, staticProps) {\n      if (protoProps) defineProperties(Constructor.prototype, protoProps);\n      if (staticProps) defineProperties(Constructor, staticProps);\n      return Constructor;\n    };\n  }();\n\n  function _possibleConstructorReturn(self, call) {\n    if (!self) {\n      throw new ReferenceError(\"this hasn't been initialised - super() hasn't been called\");\n    }\n\n    return call && (typeof call === \"object\" || typeof call === \"function\") ? call : self;\n  }\n\n  function _inherits(subClass, superClass) {\n    if (typeof superClass !== \"function\" && superClass !== null) {\n      throw new TypeError(\"Super expression must either be null or a function, not \" + typeof superClass);\n    }\n\n    subClass.prototype = Object.create(superClass && superClass.prototype, {\n      constructor: {\n        value: subClass,\n        enumerable: false,\n        writable: true,\n        configurable: true\n      }\n    });\n    if (superClass) Object.setPrototypeOf ? Object.setPrototypeOf(subClass, superClass) : subClass.__proto__ = superClass;\n  }\n\n  var supportsCaptureOption = false;\n  function setHasSupportToCaptureOption(hasSupport) {\n    supportsCaptureOption = hasSupport;\n  }\n\n  try {\n    addEventListener('test', null, Object.defineProperty({}, 'capture', { get: function get() {\n        setHasSupportToCaptureOption(true);\n      } }));\n  } catch (e) {} // eslint-disable-line no-empty\n\n  function getSafeEventHandlerOpts() {\n    var options = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : { capture: true };\n\n    return supportsCaptureOption ? options : options.capture;\n  }\n\n  /**\n   * [getPosition returns a position element that works for mouse or touch events]\n   * @param  {[Event]} event [the received event]\n   * @return {[Object]}      [x and y coords]\n   */\n  function getPosition(event) {\n    if ('touches' in event) {\n      var _event$touches$ = event.touches[0],\n          pageX = _event$touches$.pageX,\n          pageY = _event$touches$.pageY;\n\n      return { x: pageX, y: pageY };\n    }\n\n    var screenX = event.screenX,\n        screenY = event.screenY;\n\n    return { x: screenX, y: screenY };\n  }\n\n  var ReactSwipe = function (_Component) {\n    _inherits(ReactSwipe, _Component);\n\n    function ReactSwipe() {\n      var _ref;\n\n      _classCallCheck(this, ReactSwipe);\n\n      for (var _len = arguments.length, args = Array(_len), _key = 0; _key < _len; _key++) {\n        args[_key] = arguments[_key];\n      }\n\n      var _this = _possibleConstructorReturn(this, (_ref = ReactSwipe.__proto__ || Object.getPrototypeOf(ReactSwipe)).call.apply(_ref, [this].concat(args)));\n\n      _this._handleSwipeStart = _this._handleSwipeStart.bind(_this);\n      _this._handleSwipeMove = _this._handleSwipeMove.bind(_this);\n      _this._handleSwipeEnd = _this._handleSwipeEnd.bind(_this);\n\n      _this._onMouseDown = _this._onMouseDown.bind(_this);\n      _this._onMouseMove = _this._onMouseMove.bind(_this);\n      _this._onMouseUp = _this._onMouseUp.bind(_this);\n\n      _this._setSwiperRef = _this._setSwiperRef.bind(_this);\n      return _this;\n    }\n\n    _createClass(ReactSwipe, [{\n      key: 'componentDidMount',\n      value: function componentDidMount() {\n        if (this.swiper) {\n          this.swiper.addEventListener('touchmove', this._handleSwipeMove, getSafeEventHandlerOpts({\n            capture: true,\n            passive: false\n          }));\n        }\n      }\n    }, {\n      key: 'componentWillUnmount',\n      value: function componentWillUnmount() {\n        if (this.swiper) {\n          this.swiper.removeEventListener('touchmove', this._handleSwipeMove, getSafeEventHandlerOpts({\n            capture: true,\n            passive: false\n          }));\n        }\n      }\n    }, {\n      key: '_onMouseDown',\n      value: function _onMouseDown(event) {\n        if (!this.props.allowMouseEvents) {\n          return;\n        }\n\n        this.mouseDown = true;\n\n        document.addEventListener('mouseup', this._onMouseUp);\n        document.addEventListener('mousemove', this._onMouseMove);\n\n        this._handleSwipeStart(event);\n      }\n    }, {\n      key: '_onMouseMove',\n      value: function _onMouseMove(event) {\n        if (!this.mouseDown) {\n          return;\n        }\n\n        this._handleSwipeMove(event);\n      }\n    }, {\n      key: '_onMouseUp',\n      value: function _onMouseUp(event) {\n        this.mouseDown = false;\n\n        document.removeEventListener('mouseup', this._onMouseUp);\n        document.removeEventListener('mousemove', this._onMouseMove);\n\n        this._handleSwipeEnd(event);\n      }\n    }, {\n      key: '_handleSwipeStart',\n      value: function _handleSwipeStart(event) {\n        var _getPosition = getPosition(event),\n            x = _getPosition.x,\n            y = _getPosition.y;\n\n        this.moveStart = { x: x, y: y };\n        this.props.onSwipeStart(event);\n      }\n    }, {\n      key: '_handleSwipeMove',\n      value: function _handleSwipeMove(event) {\n        if (!this.moveStart) {\n          return;\n        }\n\n        var _getPosition2 = getPosition(event),\n            x = _getPosition2.x,\n            y = _getPosition2.y;\n\n        var deltaX = x - this.moveStart.x;\n        var deltaY = y - this.moveStart.y;\n        this.moving = true;\n\n        // handling the responsability of cancelling the scroll to\n        // the component handling the event\n        var shouldPreventDefault = this.props.onSwipeMove({\n          x: deltaX,\n          y: deltaY\n        }, event);\n\n        if (shouldPreventDefault && event.cancelable) {\n          event.preventDefault();\n        }\n\n        this.movePosition = { deltaX: deltaX, deltaY: deltaY };\n      }\n    }, {\n      key: '_handleSwipeEnd',\n      value: function _handleSwipeEnd(event) {\n        this.props.onSwipeEnd(event);\n\n        var tolerance = this.props.tolerance;\n\n\n        if (this.moving && this.movePosition) {\n          if (this.movePosition.deltaX < -tolerance) {\n            this.props.onSwipeLeft(1, event);\n          } else if (this.movePosition.deltaX > tolerance) {\n            this.props.onSwipeRight(1, event);\n          }\n          if (this.movePosition.deltaY < -tolerance) {\n            this.props.onSwipeUp(1, event);\n          } else if (this.movePosition.deltaY > tolerance) {\n            this.props.onSwipeDown(1, event);\n          }\n        }\n\n        this.moveStart = null;\n        this.moving = false;\n        this.movePosition = null;\n      }\n    }, {\n      key: '_setSwiperRef',\n      value: function _setSwiperRef(node) {\n        this.swiper = node;\n        this.props.innerRef(node);\n      }\n    }, {\n      key: 'render',\n      value: function render() {\n        var _props = this.props,\n            tagName = _props.tagName,\n            className = _props.className,\n            style = _props.style,\n            children = _props.children,\n            allowMouseEvents = _props.allowMouseEvents,\n            onSwipeUp = _props.onSwipeUp,\n            onSwipeDown = _props.onSwipeDown,\n            onSwipeLeft = _props.onSwipeLeft,\n            onSwipeRight = _props.onSwipeRight,\n            onSwipeStart = _props.onSwipeStart,\n            onSwipeMove = _props.onSwipeMove,\n            onSwipeEnd = _props.onSwipeEnd,\n            innerRef = _props.innerRef,\n            tolerance = _props.tolerance,\n            props = _objectWithoutProperties(_props, ['tagName', 'className', 'style', 'children', 'allowMouseEvents', 'onSwipeUp', 'onSwipeDown', 'onSwipeLeft', 'onSwipeRight', 'onSwipeStart', 'onSwipeMove', 'onSwipeEnd', 'innerRef', 'tolerance']);\n\n        return _react2.default.createElement(\n          this.props.tagName,\n          _extends({\n            ref: this._setSwiperRef,\n            onMouseDown: this._onMouseDown,\n            onTouchStart: this._handleSwipeStart,\n            onTouchEnd: this._handleSwipeEnd,\n            className: className,\n            style: style\n          }, props),\n          children\n        );\n      }\n    }]);\n\n    return ReactSwipe;\n  }(_react.Component);\n\n  ReactSwipe.displayName = 'ReactSwipe';\n  ReactSwipe.propTypes = {\n    tagName: _propTypes2.default.string,\n    className: _propTypes2.default.string,\n    style: _propTypes2.default.object,\n    children: _propTypes2.default.node,\n    allowMouseEvents: _propTypes2.default.bool,\n    onSwipeUp: _propTypes2.default.func,\n    onSwipeDown: _propTypes2.default.func,\n    onSwipeLeft: _propTypes2.default.func,\n    onSwipeRight: _propTypes2.default.func,\n    onSwipeStart: _propTypes2.default.func,\n    onSwipeMove: _propTypes2.default.func,\n    onSwipeEnd: _propTypes2.default.func,\n    innerRef: _propTypes2.default.func,\n    tolerance: _propTypes2.default.number.isRequired\n  };\n  ReactSwipe.defaultProps = {\n    tagName: 'div',\n    allowMouseEvents: false,\n    onSwipeUp: function onSwipeUp() {},\n    onSwipeDown: function onSwipeDown() {},\n    onSwipeLeft: function onSwipeLeft() {},\n    onSwipeRight: function onSwipeRight() {},\n    onSwipeStart: function onSwipeStart() {},\n    onSwipeMove: function onSwipeMove() {},\n    onSwipeEnd: function onSwipeEnd() {},\n    innerRef: function innerRef() {},\n\n    tolerance: 0\n  };\n  exports.default = ReactSwipe;\n});", "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = void 0;\n\nvar _react = _interopRequireWildcard(require(\"react\"));\n\nvar _reactEasySwipe = _interopRequireDefault(require(\"react-easy-swipe\"));\n\nvar _cssClasses = _interopRequireDefault(require(\"../../cssClasses\"));\n\nvar _Thumbs = _interopRequireDefault(require(\"../Thumbs\"));\n\nvar _document = _interopRequireDefault(require(\"../../shims/document\"));\n\nvar _window = _interopRequireDefault(require(\"../../shims/window\"));\n\nvar _utils = require(\"./utils\");\n\nvar _animations = require(\"./animations\");\n\nfunction _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }\n\nfunction _getRequireWildcardCache() { if (typeof WeakMap !== \"function\") return null; var cache = new WeakMap(); _getRequireWildcardCache = function _getRequireWildcardCache() { return cache; }; return cache; }\n\nfunction _interopRequireWildcard(obj) { if (obj && obj.__esModule) { return obj; } if (obj === null || _typeof(obj) !== \"object\" && typeof obj !== \"function\") { return { default: obj }; } var cache = _getRequireWildcardCache(); if (cache && cache.has(obj)) { return cache.get(obj); } var newObj = {}; var hasPropertyDescriptor = Object.defineProperty && Object.getOwnPropertyDescriptor; for (var key in obj) { if (Object.prototype.hasOwnProperty.call(obj, key)) { var desc = hasPropertyDescriptor ? Object.getOwnPropertyDescriptor(obj, key) : null; if (desc && (desc.get || desc.set)) { Object.defineProperty(newObj, key, desc); } else { newObj[key] = obj[key]; } } } newObj.default = obj; if (cache) { cache.set(obj, newObj); } return newObj; }\n\nfunction _typeof(obj) { \"@babel/helpers - typeof\"; if (typeof Symbol === \"function\" && typeof Symbol.iterator === \"symbol\") { _typeof = function _typeof(obj) { return typeof obj; }; } else { _typeof = function _typeof(obj) { return obj && typeof Symbol === \"function\" && obj.constructor === Symbol && obj !== Symbol.prototype ? \"symbol\" : typeof obj; }; } return _typeof(obj); }\n\nfunction _extends() { _extends = Object.assign || function (target) { for (var i = 1; i < arguments.length; i++) { var source = arguments[i]; for (var key in source) { if (Object.prototype.hasOwnProperty.call(source, key)) { target[key] = source[key]; } } } return target; }; return _extends.apply(this, arguments); }\n\nfunction ownKeys(object, enumerableOnly) { var keys = Object.keys(object); if (Object.getOwnPropertySymbols) { var symbols = Object.getOwnPropertySymbols(object); if (enumerableOnly) symbols = symbols.filter(function (sym) { return Object.getOwnPropertyDescriptor(object, sym).enumerable; }); keys.push.apply(keys, symbols); } return keys; }\n\nfunction _objectSpread(target) { for (var i = 1; i < arguments.length; i++) { var source = arguments[i] != null ? arguments[i] : {}; if (i % 2) { ownKeys(Object(source), true).forEach(function (key) { _defineProperty(target, key, source[key]); }); } else if (Object.getOwnPropertyDescriptors) { Object.defineProperties(target, Object.getOwnPropertyDescriptors(source)); } else { ownKeys(Object(source)).forEach(function (key) { Object.defineProperty(target, key, Object.getOwnPropertyDescriptor(source, key)); }); } } return target; }\n\nfunction _classCallCheck(instance, Constructor) { if (!(instance instanceof Constructor)) { throw new TypeError(\"Cannot call a class as a function\"); } }\n\nfunction _defineProperties(target, props) { for (var i = 0; i < props.length; i++) { var descriptor = props[i]; descriptor.enumerable = descriptor.enumerable || false; descriptor.configurable = true; if (\"value\" in descriptor) descriptor.writable = true; Object.defineProperty(target, descriptor.key, descriptor); } }\n\nfunction _createClass(Constructor, protoProps, staticProps) { if (protoProps) _defineProperties(Constructor.prototype, protoProps); if (staticProps) _defineProperties(Constructor, staticProps); return Constructor; }\n\nfunction _inherits(subClass, superClass) { if (typeof superClass !== \"function\" && superClass !== null) { throw new TypeError(\"Super expression must either be null or a function\"); } subClass.prototype = Object.create(superClass && superClass.prototype, { constructor: { value: subClass, writable: true, configurable: true } }); if (superClass) _setPrototypeOf(subClass, superClass); }\n\nfunction _setPrototypeOf(o, p) { _setPrototypeOf = Object.setPrototypeOf || function _setPrototypeOf(o, p) { o.__proto__ = p; return o; }; return _setPrototypeOf(o, p); }\n\nfunction _createSuper(Derived) { var hasNativeReflectConstruct = _isNativeReflectConstruct(); return function _createSuperInternal() { var Super = _getPrototypeOf(Derived), result; if (hasNativeReflectConstruct) { var NewTarget = _getPrototypeOf(this).constructor; result = Reflect.construct(Super, arguments, NewTarget); } else { result = Super.apply(this, arguments); } return _possibleConstructorReturn(this, result); }; }\n\nfunction _possibleConstructorReturn(self, call) { if (call && (_typeof(call) === \"object\" || typeof call === \"function\")) { return call; } return _assertThisInitialized(self); }\n\nfunction _assertThisInitialized(self) { if (self === void 0) { throw new ReferenceError(\"this hasn't been initialised - super() hasn't been called\"); } return self; }\n\nfunction _isNativeReflectConstruct() { if (typeof Reflect === \"undefined\" || !Reflect.construct) return false; if (Reflect.construct.sham) return false; if (typeof Proxy === \"function\") return true; try { Date.prototype.toString.call(Reflect.construct(Date, [], function () {})); return true; } catch (e) { return false; } }\n\nfunction _getPrototypeOf(o) { _getPrototypeOf = Object.setPrototypeOf ? Object.getPrototypeOf : function _getPrototypeOf(o) { return o.__proto__ || Object.getPrototypeOf(o); }; return _getPrototypeOf(o); }\n\nfunction _defineProperty(obj, key, value) { if (key in obj) { Object.defineProperty(obj, key, { value: value, enumerable: true, configurable: true, writable: true }); } else { obj[key] = value; } return obj; }\n\nvar Carousel = /*#__PURE__*/function (_React$Component) {\n  _inherits(Carousel, _React$Component);\n\n  var _super = _createSuper(Carousel);\n\n  // @ts-ignore\n  function Carousel(props) {\n    var _this;\n\n    _classCallCheck(this, Carousel);\n\n    _this = _super.call(this, props);\n\n    _defineProperty(_assertThisInitialized(_this), \"thumbsRef\", void 0);\n\n    _defineProperty(_assertThisInitialized(_this), \"carouselWrapperRef\", void 0);\n\n    _defineProperty(_assertThisInitialized(_this), \"listRef\", void 0);\n\n    _defineProperty(_assertThisInitialized(_this), \"itemsRef\", void 0);\n\n    _defineProperty(_assertThisInitialized(_this), \"timer\", void 0);\n\n    _defineProperty(_assertThisInitialized(_this), \"animationHandler\", void 0);\n\n    _defineProperty(_assertThisInitialized(_this), \"setThumbsRef\", function (node) {\n      _this.thumbsRef = node;\n    });\n\n    _defineProperty(_assertThisInitialized(_this), \"setCarouselWrapperRef\", function (node) {\n      _this.carouselWrapperRef = node;\n    });\n\n    _defineProperty(_assertThisInitialized(_this), \"setListRef\", function (node) {\n      _this.listRef = node;\n    });\n\n    _defineProperty(_assertThisInitialized(_this), \"setItemsRef\", function (node, index) {\n      if (!_this.itemsRef) {\n        _this.itemsRef = [];\n      }\n\n      _this.itemsRef[index] = node;\n    });\n\n    _defineProperty(_assertThisInitialized(_this), \"autoPlay\", function () {\n      if (_react.Children.count(_this.props.children) <= 1) {\n        return;\n      }\n\n      _this.clearAutoPlay();\n\n      if (!_this.props.autoPlay) {\n        return;\n      }\n\n      _this.timer = setTimeout(function () {\n        _this.increment();\n      }, _this.props.interval);\n    });\n\n    _defineProperty(_assertThisInitialized(_this), \"clearAutoPlay\", function () {\n      if (_this.timer) clearTimeout(_this.timer);\n    });\n\n    _defineProperty(_assertThisInitialized(_this), \"resetAutoPlay\", function () {\n      _this.clearAutoPlay();\n\n      _this.autoPlay();\n    });\n\n    _defineProperty(_assertThisInitialized(_this), \"stopOnHover\", function () {\n      _this.setState({\n        isMouseEntered: true\n      }, _this.clearAutoPlay);\n    });\n\n    _defineProperty(_assertThisInitialized(_this), \"startOnLeave\", function () {\n      _this.setState({\n        isMouseEntered: false\n      }, _this.autoPlay);\n    });\n\n    _defineProperty(_assertThisInitialized(_this), \"isFocusWithinTheCarousel\", function () {\n      if (!_this.carouselWrapperRef) {\n        return false;\n      }\n\n      if ((0, _document.default)().activeElement === _this.carouselWrapperRef || _this.carouselWrapperRef.contains((0, _document.default)().activeElement)) {\n        return true;\n      }\n\n      return false;\n    });\n\n    _defineProperty(_assertThisInitialized(_this), \"navigateWithKeyboard\", function (e) {\n      if (!_this.isFocusWithinTheCarousel()) {\n        return;\n      }\n\n      var axis = _this.props.axis;\n      var isHorizontal = axis === 'horizontal';\n      var keyNames = {\n        ArrowUp: 38,\n        ArrowRight: 39,\n        ArrowDown: 40,\n        ArrowLeft: 37\n      };\n      var nextKey = isHorizontal ? keyNames.ArrowRight : keyNames.ArrowDown;\n      var prevKey = isHorizontal ? keyNames.ArrowLeft : keyNames.ArrowUp;\n\n      if (nextKey === e.keyCode) {\n        _this.increment();\n      } else if (prevKey === e.keyCode) {\n        _this.decrement();\n      }\n    });\n\n    _defineProperty(_assertThisInitialized(_this), \"updateSizes\", function () {\n      if (!_this.state.initialized || !_this.itemsRef || _this.itemsRef.length === 0) {\n        return;\n      }\n\n      var isHorizontal = _this.props.axis === 'horizontal';\n      var firstItem = _this.itemsRef[0];\n\n      if (!firstItem) {\n        return;\n      }\n\n      var itemSize = isHorizontal ? firstItem.clientWidth : firstItem.clientHeight;\n\n      _this.setState({\n        itemSize: itemSize\n      });\n\n      if (_this.thumbsRef) {\n        _this.thumbsRef.updateSizes();\n      }\n    });\n\n    _defineProperty(_assertThisInitialized(_this), \"setMountState\", function () {\n      _this.setState({\n        hasMount: true\n      });\n\n      _this.updateSizes();\n    });\n\n    _defineProperty(_assertThisInitialized(_this), \"handleClickItem\", function (index, item) {\n      if (_react.Children.count(_this.props.children) === 0) {\n        return;\n      }\n\n      if (_this.state.cancelClick) {\n        _this.setState({\n          cancelClick: false\n        });\n\n        return;\n      }\n\n      _this.props.onClickItem(index, item);\n\n      if (index !== _this.state.selectedItem) {\n        _this.setState({\n          selectedItem: index\n        });\n      }\n    });\n\n    _defineProperty(_assertThisInitialized(_this), \"handleOnChange\", function (index, item) {\n      if (_react.Children.count(_this.props.children) <= 1) {\n        return;\n      }\n\n      _this.props.onChange(index, item);\n    });\n\n    _defineProperty(_assertThisInitialized(_this), \"handleClickThumb\", function (index, item) {\n      _this.props.onClickThumb(index, item);\n\n      _this.moveTo(index);\n    });\n\n    _defineProperty(_assertThisInitialized(_this), \"onSwipeStart\", function (event) {\n      _this.setState({\n        swiping: true\n      });\n\n      _this.props.onSwipeStart(event);\n    });\n\n    _defineProperty(_assertThisInitialized(_this), \"onSwipeEnd\", function (event) {\n      _this.setState({\n        swiping: false,\n        cancelClick: false,\n        swipeMovementStarted: false\n      });\n\n      _this.props.onSwipeEnd(event);\n\n      _this.clearAutoPlay();\n\n      if (_this.state.autoPlay) {\n        _this.autoPlay();\n      }\n    });\n\n    _defineProperty(_assertThisInitialized(_this), \"onSwipeMove\", function (delta, event) {\n      _this.props.onSwipeMove(event);\n\n      var animationHandlerResponse = _this.props.swipeAnimationHandler(delta, _this.props, _this.state, _this.setState.bind(_assertThisInitialized(_this)));\n\n      _this.setState(_objectSpread({}, animationHandlerResponse)); // If we have not moved, we should have an empty object returned\n      // Return false to allow scrolling when not swiping\n\n\n      return !!Object.keys(animationHandlerResponse).length;\n    });\n\n    _defineProperty(_assertThisInitialized(_this), \"decrement\", function () {\n      var positions = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : 1;\n\n      _this.moveTo(_this.state.selectedItem - (typeof positions === 'number' ? positions : 1));\n    });\n\n    _defineProperty(_assertThisInitialized(_this), \"increment\", function () {\n      var positions = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : 1;\n\n      _this.moveTo(_this.state.selectedItem + (typeof positions === 'number' ? positions : 1));\n    });\n\n    _defineProperty(_assertThisInitialized(_this), \"moveTo\", function (position) {\n      if (typeof position !== 'number') {\n        return;\n      }\n\n      var lastPosition = _react.Children.count(_this.props.children) - 1;\n\n      if (position < 0) {\n        position = _this.props.infiniteLoop ? lastPosition : 0;\n      }\n\n      if (position > lastPosition) {\n        position = _this.props.infiniteLoop ? 0 : lastPosition;\n      }\n\n      _this.selectItem({\n        // if it's not a slider, we don't need to set position here\n        selectedItem: position\n      }); // don't reset auto play when stop on hover is enabled, doing so will trigger a call to auto play more than once\n      // and will result in the interval function not being cleared correctly.\n\n\n      if (_this.state.autoPlay && _this.state.isMouseEntered === false) {\n        _this.resetAutoPlay();\n      }\n    });\n\n    _defineProperty(_assertThisInitialized(_this), \"onClickNext\", function () {\n      _this.increment(1);\n    });\n\n    _defineProperty(_assertThisInitialized(_this), \"onClickPrev\", function () {\n      _this.decrement(1);\n    });\n\n    _defineProperty(_assertThisInitialized(_this), \"onSwipeForward\", function () {\n      _this.increment(1);\n\n      if (_this.props.emulateTouch) {\n        _this.setState({\n          cancelClick: true\n        });\n      }\n    });\n\n    _defineProperty(_assertThisInitialized(_this), \"onSwipeBackwards\", function () {\n      _this.decrement(1);\n\n      if (_this.props.emulateTouch) {\n        _this.setState({\n          cancelClick: true\n        });\n      }\n    });\n\n    _defineProperty(_assertThisInitialized(_this), \"changeItem\", function (newIndex) {\n      return function (e) {\n        if (!(0, _utils.isKeyboardEvent)(e) || e.key === 'Enter') {\n          _this.moveTo(newIndex);\n        }\n      };\n    });\n\n    _defineProperty(_assertThisInitialized(_this), \"selectItem\", function (state) {\n      // Merge in the new state while updating updating previous item\n      _this.setState(_objectSpread({\n        previousItem: _this.state.selectedItem\n      }, state), function () {\n        // Run animation handler and update styles based on it\n        _this.setState(_this.animationHandler(_this.props, _this.state));\n      });\n\n      _this.handleOnChange(state.selectedItem, _react.Children.toArray(_this.props.children)[state.selectedItem]);\n    });\n\n    _defineProperty(_assertThisInitialized(_this), \"getInitialImage\", function () {\n      var selectedItem = _this.props.selectedItem;\n      var item = _this.itemsRef && _this.itemsRef[selectedItem];\n      var images = item && item.getElementsByTagName('img') || [];\n      return images[0];\n    });\n\n    _defineProperty(_assertThisInitialized(_this), \"getVariableItemHeight\", function (position) {\n      var item = _this.itemsRef && _this.itemsRef[position];\n\n      if (_this.state.hasMount && item && item.children.length) {\n        var slideImages = item.children[0].getElementsByTagName('img') || [];\n\n        if (slideImages.length > 0) {\n          var image = slideImages[0];\n\n          if (!image.complete) {\n            // if the image is still loading, the size won't be available so we trigger a new render after it's done\n            var onImageLoad = function onImageLoad() {\n              _this.forceUpdate();\n\n              image.removeEventListener('load', onImageLoad);\n            };\n\n            image.addEventListener('load', onImageLoad);\n          }\n        } // try to get img first, if img not there find first display tag\n\n\n        var displayItem = slideImages[0] || item.children[0];\n        var height = displayItem.clientHeight;\n        return height > 0 ? height : null;\n      }\n\n      return null;\n    });\n\n    var initState = {\n      initialized: false,\n      previousItem: props.selectedItem,\n      selectedItem: props.selectedItem,\n      hasMount: false,\n      isMouseEntered: false,\n      autoPlay: props.autoPlay,\n      swiping: false,\n      swipeMovementStarted: false,\n      cancelClick: false,\n      itemSize: 1,\n      itemListStyle: {},\n      slideStyle: {},\n      selectedStyle: {},\n      prevStyle: {}\n    };\n    _this.animationHandler = typeof props.animationHandler === 'function' && props.animationHandler || props.animationHandler === 'fade' && _animations.fadeAnimationHandler || _animations.slideAnimationHandler;\n    _this.state = _objectSpread(_objectSpread({}, initState), _this.animationHandler(props, initState));\n    return _this;\n  }\n\n  _createClass(Carousel, [{\n    key: \"componentDidMount\",\n    value: function componentDidMount() {\n      if (!this.props.children) {\n        return;\n      }\n\n      this.setupCarousel();\n    }\n  }, {\n    key: \"componentDidUpdate\",\n    value: function componentDidUpdate(prevProps, prevState) {\n      if (!prevProps.children && this.props.children && !this.state.initialized) {\n        this.setupCarousel();\n      }\n\n      if (!prevProps.autoFocus && this.props.autoFocus) {\n        this.forceFocus();\n      }\n\n      if (prevState.swiping && !this.state.swiping) {\n        // We stopped swiping, ensure we are heading to the new/current slide and not stuck\n        this.setState(_objectSpread({}, this.props.stopSwipingHandler(this.props, this.state)));\n      }\n\n      if (prevProps.selectedItem !== this.props.selectedItem || prevProps.centerMode !== this.props.centerMode) {\n        this.updateSizes();\n        this.moveTo(this.props.selectedItem);\n      }\n\n      if (prevProps.autoPlay !== this.props.autoPlay) {\n        if (this.props.autoPlay) {\n          this.setupAutoPlay();\n        } else {\n          this.destroyAutoPlay();\n        }\n\n        this.setState({\n          autoPlay: this.props.autoPlay\n        });\n      }\n    }\n  }, {\n    key: \"componentWillUnmount\",\n    value: function componentWillUnmount() {\n      this.destroyCarousel();\n    }\n  }, {\n    key: \"setupCarousel\",\n    value: function setupCarousel() {\n      var _this2 = this;\n\n      this.bindEvents();\n\n      if (this.state.autoPlay && _react.Children.count(this.props.children) > 1) {\n        this.setupAutoPlay();\n      }\n\n      if (this.props.autoFocus) {\n        this.forceFocus();\n      }\n\n      this.setState({\n        initialized: true\n      }, function () {\n        var initialImage = _this2.getInitialImage();\n\n        if (initialImage && !initialImage.complete) {\n          // if it's a carousel of images, we set the mount state after the first image is loaded\n          initialImage.addEventListener('load', _this2.setMountState);\n        } else {\n          _this2.setMountState();\n        }\n      });\n    }\n  }, {\n    key: \"destroyCarousel\",\n    value: function destroyCarousel() {\n      if (this.state.initialized) {\n        this.unbindEvents();\n        this.destroyAutoPlay();\n      }\n    }\n  }, {\n    key: \"setupAutoPlay\",\n    value: function setupAutoPlay() {\n      this.autoPlay();\n      var carouselWrapper = this.carouselWrapperRef;\n\n      if (this.props.stopOnHover && carouselWrapper) {\n        carouselWrapper.addEventListener('mouseenter', this.stopOnHover);\n        carouselWrapper.addEventListener('mouseleave', this.startOnLeave);\n      }\n    }\n  }, {\n    key: \"destroyAutoPlay\",\n    value: function destroyAutoPlay() {\n      this.clearAutoPlay();\n      var carouselWrapper = this.carouselWrapperRef;\n\n      if (this.props.stopOnHover && carouselWrapper) {\n        carouselWrapper.removeEventListener('mouseenter', this.stopOnHover);\n        carouselWrapper.removeEventListener('mouseleave', this.startOnLeave);\n      }\n    }\n  }, {\n    key: \"bindEvents\",\n    value: function bindEvents() {\n      // as the widths are calculated, we need to resize\n      // the carousel when the window is resized\n      (0, _window.default)().addEventListener('resize', this.updateSizes); // issue #2 - image loading smaller\n\n      (0, _window.default)().addEventListener('DOMContentLoaded', this.updateSizes);\n\n      if (this.props.useKeyboardArrows) {\n        (0, _document.default)().addEventListener('keydown', this.navigateWithKeyboard);\n      }\n    }\n  }, {\n    key: \"unbindEvents\",\n    value: function unbindEvents() {\n      // removing listeners\n      (0, _window.default)().removeEventListener('resize', this.updateSizes);\n      (0, _window.default)().removeEventListener('DOMContentLoaded', this.updateSizes);\n      var initialImage = this.getInitialImage();\n\n      if (initialImage) {\n        initialImage.removeEventListener('load', this.setMountState);\n      }\n\n      if (this.props.useKeyboardArrows) {\n        (0, _document.default)().removeEventListener('keydown', this.navigateWithKeyboard);\n      }\n    }\n  }, {\n    key: \"forceFocus\",\n    value: function forceFocus() {\n      var _this$carouselWrapper;\n\n      (_this$carouselWrapper = this.carouselWrapperRef) === null || _this$carouselWrapper === void 0 ? void 0 : _this$carouselWrapper.focus();\n    }\n  }, {\n    key: \"renderItems\",\n    value: function renderItems(isClone) {\n      var _this3 = this;\n\n      if (!this.props.children) {\n        return [];\n      }\n\n      return _react.Children.map(this.props.children, function (item, index) {\n        var isSelected = index === _this3.state.selectedItem;\n        var isPrevious = index === _this3.state.previousItem;\n        var style = isSelected && _this3.state.selectedStyle || isPrevious && _this3.state.prevStyle || _this3.state.slideStyle || {};\n\n        if (_this3.props.centerMode && _this3.props.axis === 'horizontal') {\n          style = _objectSpread(_objectSpread({}, style), {}, {\n            minWidth: _this3.props.centerSlidePercentage + '%'\n          });\n        }\n\n        if (_this3.state.swiping && _this3.state.swipeMovementStarted) {\n          style = _objectSpread(_objectSpread({}, style), {}, {\n            pointerEvents: 'none'\n          });\n        }\n\n        var slideProps = {\n          ref: function ref(e) {\n            return _this3.setItemsRef(e, index);\n          },\n          key: 'itemKey' + index + (isClone ? 'clone' : ''),\n          className: _cssClasses.default.ITEM(true, index === _this3.state.selectedItem, index === _this3.state.previousItem),\n          onClick: _this3.handleClickItem.bind(_this3, index, item),\n          style: style\n        };\n        return /*#__PURE__*/_react.default.createElement(\"li\", slideProps, _this3.props.renderItem(item, {\n          isSelected: index === _this3.state.selectedItem,\n          isPrevious: index === _this3.state.previousItem\n        }));\n      });\n    }\n  }, {\n    key: \"renderControls\",\n    value: function renderControls() {\n      var _this4 = this;\n\n      var _this$props = this.props,\n          showIndicators = _this$props.showIndicators,\n          labels = _this$props.labels,\n          renderIndicator = _this$props.renderIndicator,\n          children = _this$props.children;\n\n      if (!showIndicators) {\n        return null;\n      }\n\n      return /*#__PURE__*/_react.default.createElement(\"ul\", {\n        className: \"control-dots\"\n      }, _react.Children.map(children, function (_, index) {\n        return renderIndicator && renderIndicator(_this4.changeItem(index), index === _this4.state.selectedItem, index, labels.item);\n      }));\n    }\n  }, {\n    key: \"renderStatus\",\n    value: function renderStatus() {\n      if (!this.props.showStatus) {\n        return null;\n      }\n\n      return /*#__PURE__*/_react.default.createElement(\"p\", {\n        className: \"carousel-status\"\n      }, this.props.statusFormatter(this.state.selectedItem + 1, _react.Children.count(this.props.children)));\n    }\n  }, {\n    key: \"renderThumbs\",\n    value: function renderThumbs() {\n      if (!this.props.showThumbs || !this.props.children || _react.Children.count(this.props.children) === 0) {\n        return null;\n      }\n\n      return /*#__PURE__*/_react.default.createElement(_Thumbs.default, {\n        ref: this.setThumbsRef,\n        onSelectItem: this.handleClickThumb,\n        selectedItem: this.state.selectedItem,\n        transitionTime: this.props.transitionTime,\n        thumbWidth: this.props.thumbWidth,\n        labels: this.props.labels,\n        emulateTouch: this.props.emulateTouch\n      }, this.props.renderThumbs(this.props.children));\n    }\n  }, {\n    key: \"render\",\n    value: function render() {\n      var _this5 = this;\n\n      if (!this.props.children || _react.Children.count(this.props.children) === 0) {\n        return null;\n      }\n\n      var isSwipeable = this.props.swipeable && _react.Children.count(this.props.children) > 1;\n      var isHorizontal = this.props.axis === 'horizontal';\n      var canShowArrows = this.props.showArrows && _react.Children.count(this.props.children) > 1; // show left arrow?\n\n      var hasPrev = canShowArrows && (this.state.selectedItem > 0 || this.props.infiniteLoop) || false; // show right arrow\n\n      var hasNext = canShowArrows && (this.state.selectedItem < _react.Children.count(this.props.children) - 1 || this.props.infiniteLoop) || false;\n      var itemsClone = this.renderItems(true);\n      var firstClone = itemsClone.shift();\n      var lastClone = itemsClone.pop();\n      var swiperProps = {\n        className: _cssClasses.default.SLIDER(true, this.state.swiping),\n        onSwipeMove: this.onSwipeMove,\n        onSwipeStart: this.onSwipeStart,\n        onSwipeEnd: this.onSwipeEnd,\n        style: this.state.itemListStyle,\n        tolerance: this.props.swipeScrollTolerance\n      };\n      var containerStyles = {};\n\n      if (isHorizontal) {\n        swiperProps.onSwipeLeft = this.onSwipeForward;\n        swiperProps.onSwipeRight = this.onSwipeBackwards;\n\n        if (this.props.dynamicHeight) {\n          var itemHeight = this.getVariableItemHeight(this.state.selectedItem); // swiperProps.style.height = itemHeight || 'auto';\n\n          containerStyles.height = itemHeight || 'auto';\n        }\n      } else {\n        swiperProps.onSwipeUp = this.props.verticalSwipe === 'natural' ? this.onSwipeBackwards : this.onSwipeForward;\n        swiperProps.onSwipeDown = this.props.verticalSwipe === 'natural' ? this.onSwipeForward : this.onSwipeBackwards;\n        swiperProps.style = _objectSpread(_objectSpread({}, swiperProps.style), {}, {\n          height: this.state.itemSize\n        });\n        containerStyles.height = this.state.itemSize;\n      }\n\n      return /*#__PURE__*/_react.default.createElement(\"div\", {\n        \"aria-label\": this.props.ariaLabel,\n        className: _cssClasses.default.ROOT(this.props.className),\n        ref: this.setCarouselWrapperRef,\n        tabIndex: this.props.useKeyboardArrows ? 0 : undefined\n      }, /*#__PURE__*/_react.default.createElement(\"div\", {\n        className: _cssClasses.default.CAROUSEL(true),\n        style: {\n          width: this.props.width\n        }\n      }, this.renderControls(), this.props.renderArrowPrev(this.onClickPrev, hasPrev, this.props.labels.leftArrow), /*#__PURE__*/_react.default.createElement(\"div\", {\n        className: _cssClasses.default.WRAPPER(true, this.props.axis),\n        style: containerStyles\n      }, isSwipeable ? /*#__PURE__*/_react.default.createElement(_reactEasySwipe.default, _extends({\n        tagName: \"ul\",\n        innerRef: this.setListRef\n      }, swiperProps, {\n        allowMouseEvents: this.props.emulateTouch\n      }), this.props.infiniteLoop && lastClone, this.renderItems(), this.props.infiniteLoop && firstClone) : /*#__PURE__*/_react.default.createElement(\"ul\", {\n        className: _cssClasses.default.SLIDER(true, this.state.swiping),\n        ref: function ref(node) {\n          return _this5.setListRef(node);\n        },\n        style: this.state.itemListStyle || {}\n      }, this.props.infiniteLoop && lastClone, this.renderItems(), this.props.infiniteLoop && firstClone)), this.props.renderArrowNext(this.onClickNext, hasNext, this.props.labels.rightArrow), this.renderStatus()), this.renderThumbs());\n    }\n  }]);\n\n  return Carousel;\n}(_react.default.Component);\n\nexports.default = Carousel;\n\n_defineProperty(Carousel, \"displayName\", 'Carousel');\n\n_defineProperty(Carousel, \"defaultProps\", {\n  ariaLabel: undefined,\n  axis: 'horizontal',\n  centerSlidePercentage: 80,\n  interval: 3000,\n  labels: {\n    leftArrow: 'previous slide / item',\n    rightArrow: 'next slide / item',\n    item: 'slide item'\n  },\n  onClickItem: _utils.noop,\n  onClickThumb: _utils.noop,\n  onChange: _utils.noop,\n  onSwipeStart: function onSwipeStart() {},\n  onSwipeEnd: function onSwipeEnd() {},\n  onSwipeMove: function onSwipeMove() {\n    return false;\n  },\n  preventMovementUntilSwipeScrollTolerance: false,\n  renderArrowPrev: function renderArrowPrev(onClickHandler, hasPrev, label) {\n    return /*#__PURE__*/_react.default.createElement(\"button\", {\n      type: \"button\",\n      \"aria-label\": label,\n      className: _cssClasses.default.ARROW_PREV(!hasPrev),\n      onClick: onClickHandler\n    });\n  },\n  renderArrowNext: function renderArrowNext(onClickHandler, hasNext, label) {\n    return /*#__PURE__*/_react.default.createElement(\"button\", {\n      type: \"button\",\n      \"aria-label\": label,\n      className: _cssClasses.default.ARROW_NEXT(!hasNext),\n      onClick: onClickHandler\n    });\n  },\n  renderIndicator: function renderIndicator(onClickHandler, isSelected, index, label) {\n    return /*#__PURE__*/_react.default.createElement(\"li\", {\n      className: _cssClasses.default.DOT(isSelected),\n      onClick: onClickHandler,\n      onKeyDown: onClickHandler,\n      value: index,\n      key: index,\n      role: \"button\",\n      tabIndex: 0,\n      \"aria-label\": \"\".concat(label, \" \").concat(index + 1)\n    });\n  },\n  renderItem: function renderItem(item) {\n    return item;\n  },\n  renderThumbs: function renderThumbs(children) {\n    var images = _react.Children.map(children, function (item) {\n      var img = item; // if the item is not an image, try to find the first image in the item's children.\n\n      if (item.type !== 'img') {\n        img = _react.Children.toArray(item.props.children).find(function (children) {\n          return children.type === 'img';\n        });\n      }\n\n      if (!img) {\n        return undefined;\n      }\n\n      return img;\n    });\n\n    if (images.filter(function (image) {\n      return image;\n    }).length === 0) {\n      console.warn(\"No images found! Can't build the thumb list without images. If you don't need thumbs, set showThumbs={false} in the Carousel. Note that it's not possible to get images rendered inside custom components. More info at https://github.com/leandrowd/react-responsive-carousel/blob/master/TROUBLESHOOTING.md\");\n      return [];\n    }\n\n    return images;\n  },\n  statusFormatter: _utils.defaultStatusFormatter,\n  selectedItem: 0,\n  showArrows: true,\n  showIndicators: true,\n  showStatus: true,\n  showThumbs: true,\n  stopOnHover: true,\n  swipeScrollTolerance: 5,\n  swipeable: true,\n  transitionTime: 350,\n  verticalSwipe: 'standard',\n  width: '100%',\n  animationHandler: 'slide',\n  swipeAnimationHandler: _animations.slideSwipeAnimationHandler,\n  stopSwipingHandler: _animations.slideStopSwipingHandler\n});", "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.outerWidth = void 0;\n\nvar outerWidth = function outerWidth(el) {\n  var width = el.offsetWidth;\n  var style = getComputedStyle(el);\n  width += parseInt(style.marginLeft) + parseInt(style.marginRight);\n  return width;\n};\n\nexports.outerWidth = outerWidth;", "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = void 0;\n\nvar _default = function _default() {\n  return document;\n};\n\nexports.default = _default;", "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = void 0;\n\nvar _default = function _default() {\n  return window;\n};\n\nexports.default = _default;", "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = void 0;\n\nvar _default = function _default(position, metric, axis) {\n  var positionPercent = position === 0 ? position : position + metric;\n  var positionCss = axis === 'horizontal' ? [positionPercent, 0, 0] : [0, positionPercent, 0];\n  var transitionProp = 'translate3d';\n  var translatedPosition = '(' + positionCss.join(',') + ')';\n  return transitionProp + translatedPosition;\n};\n\nexports.default = _default;", "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.fadeAnimationHandler = exports.slideStopSwipingHandler = exports.slideSwipeAnimationHandler = exports.slideAnimationHandler = void 0;\n\nvar _react = require(\"react\");\n\nvar _CSSTranslate = _interopRequireDefault(require(\"../../CSSTranslate\"));\n\nvar _utils = require(\"./utils\");\n\nfunction _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }\n\nfunction ownKeys(object, enumerableOnly) { var keys = Object.keys(object); if (Object.getOwnPropertySymbols) { var symbols = Object.getOwnPropertySymbols(object); if (enumerableOnly) symbols = symbols.filter(function (sym) { return Object.getOwnPropertyDescriptor(object, sym).enumerable; }); keys.push.apply(keys, symbols); } return keys; }\n\nfunction _objectSpread(target) { for (var i = 1; i < arguments.length; i++) { var source = arguments[i] != null ? arguments[i] : {}; if (i % 2) { ownKeys(Object(source), true).forEach(function (key) { _defineProperty(target, key, source[key]); }); } else if (Object.getOwnPropertyDescriptors) { Object.defineProperties(target, Object.getOwnPropertyDescriptors(source)); } else { ownKeys(Object(source)).forEach(function (key) { Object.defineProperty(target, key, Object.getOwnPropertyDescriptor(source, key)); }); } } return target; }\n\nfunction _defineProperty(obj, key, value) { if (key in obj) { Object.defineProperty(obj, key, { value: value, enumerable: true, configurable: true, writable: true }); } else { obj[key] = value; } return obj; }\n\n/**\n * Main animation handler for the default 'sliding' style animation\n * @param props\n * @param state\n */\nvar slideAnimationHandler = function slideAnimationHandler(props, state) {\n  var returnStyles = {};\n  var selectedItem = state.selectedItem;\n  var previousItem = selectedItem;\n  var lastPosition = _react.Children.count(props.children) - 1;\n  var needClonedSlide = props.infiniteLoop && (selectedItem < 0 || selectedItem > lastPosition); // Handle list position if it needs a clone\n\n  if (needClonedSlide) {\n    if (previousItem < 0) {\n      if (props.centerMode && props.centerSlidePercentage && props.axis === 'horizontal') {\n        returnStyles.itemListStyle = (0, _utils.setPosition)(-(lastPosition + 2) * props.centerSlidePercentage - (100 - props.centerSlidePercentage) / 2, props.axis);\n      } else {\n        returnStyles.itemListStyle = (0, _utils.setPosition)(-(lastPosition + 2) * 100, props.axis);\n      }\n    } else if (previousItem > lastPosition) {\n      returnStyles.itemListStyle = (0, _utils.setPosition)(0, props.axis);\n    }\n\n    return returnStyles;\n  }\n\n  var currentPosition = (0, _utils.getPosition)(selectedItem, props); // if 3d is available, let's take advantage of the performance of transform\n\n  var transformProp = (0, _CSSTranslate.default)(currentPosition, '%', props.axis);\n  var transitionTime = props.transitionTime + 'ms';\n  returnStyles.itemListStyle = {\n    WebkitTransform: transformProp,\n    msTransform: transformProp,\n    OTransform: transformProp,\n    transform: transformProp\n  };\n\n  if (!state.swiping) {\n    returnStyles.itemListStyle = _objectSpread(_objectSpread({}, returnStyles.itemListStyle), {}, {\n      WebkitTransitionDuration: transitionTime,\n      MozTransitionDuration: transitionTime,\n      OTransitionDuration: transitionTime,\n      transitionDuration: transitionTime,\n      msTransitionDuration: transitionTime\n    });\n  }\n\n  return returnStyles;\n};\n/**\n * Swiping animation handler for the default 'sliding' style animation\n * @param delta\n * @param props\n * @param state\n * @param setState\n */\n\n\nexports.slideAnimationHandler = slideAnimationHandler;\n\nvar slideSwipeAnimationHandler = function slideSwipeAnimationHandler(delta, props, state, setState) {\n  var returnStyles = {};\n  var isHorizontal = props.axis === 'horizontal';\n\n  var childrenLength = _react.Children.count(props.children);\n\n  var initialBoundry = 0;\n  var currentPosition = (0, _utils.getPosition)(state.selectedItem, props);\n  var finalBoundry = props.infiniteLoop ? (0, _utils.getPosition)(childrenLength - 1, props) - 100 : (0, _utils.getPosition)(childrenLength - 1, props);\n  var axisDelta = isHorizontal ? delta.x : delta.y;\n  var handledDelta = axisDelta; // prevent user from swiping left out of boundaries\n\n  if (currentPosition === initialBoundry && axisDelta > 0) {\n    handledDelta = 0;\n  } // prevent user from swiping right out of boundaries\n\n\n  if (currentPosition === finalBoundry && axisDelta < 0) {\n    handledDelta = 0;\n  }\n\n  var position = currentPosition + 100 / (state.itemSize / handledDelta);\n  var hasMoved = Math.abs(axisDelta) > props.swipeScrollTolerance;\n\n  if (props.infiniteLoop && hasMoved) {\n    // When allowing infinite loop, if we slide left from position 0 we reveal the cloned last slide that appears before it\n    // if we slide even further we need to jump to other side so it can continue - and vice versa for the last slide\n    if (state.selectedItem === 0 && position > -100) {\n      position -= childrenLength * 100;\n    } else if (state.selectedItem === childrenLength - 1 && position < -childrenLength * 100) {\n      position += childrenLength * 100;\n    }\n  }\n\n  if (!props.preventMovementUntilSwipeScrollTolerance || hasMoved || state.swipeMovementStarted) {\n    if (!state.swipeMovementStarted) {\n      setState({\n        swipeMovementStarted: true\n      });\n    }\n\n    returnStyles.itemListStyle = (0, _utils.setPosition)(position, props.axis);\n  } //allows scroll if the swipe was within the tolerance\n\n\n  if (hasMoved && !state.cancelClick) {\n    setState({\n      cancelClick: true\n    });\n  }\n\n  return returnStyles;\n};\n/**\n * Default 'sliding' style animination handler for when a swipe action stops.\n * @param props\n * @param state\n */\n\n\nexports.slideSwipeAnimationHandler = slideSwipeAnimationHandler;\n\nvar slideStopSwipingHandler = function slideStopSwipingHandler(props, state) {\n  var currentPosition = (0, _utils.getPosition)(state.selectedItem, props);\n  var itemListStyle = (0, _utils.setPosition)(currentPosition, props.axis);\n  return {\n    itemListStyle: itemListStyle\n  };\n};\n/**\n * Main animation handler for the default 'fade' style animation\n * @param props\n * @param state\n */\n\n\nexports.slideStopSwipingHandler = slideStopSwipingHandler;\n\nvar fadeAnimationHandler = function fadeAnimationHandler(props, state) {\n  var transitionTime = props.transitionTime + 'ms';\n  var transitionTimingFunction = 'ease-in-out';\n  var slideStyle = {\n    position: 'absolute',\n    display: 'block',\n    zIndex: -2,\n    minHeight: '100%',\n    opacity: 0,\n    top: 0,\n    right: 0,\n    left: 0,\n    bottom: 0,\n    transitionTimingFunction: transitionTimingFunction,\n    msTransitionTimingFunction: transitionTimingFunction,\n    MozTransitionTimingFunction: transitionTimingFunction,\n    WebkitTransitionTimingFunction: transitionTimingFunction,\n    OTransitionTimingFunction: transitionTimingFunction\n  };\n\n  if (!state.swiping) {\n    slideStyle = _objectSpread(_objectSpread({}, slideStyle), {}, {\n      WebkitTransitionDuration: transitionTime,\n      MozTransitionDuration: transitionTime,\n      OTransitionDuration: transitionTime,\n      transitionDuration: transitionTime,\n      msTransitionDuration: transitionTime\n    });\n  }\n\n  return {\n    slideStyle: slideStyle,\n    selectedStyle: _objectSpread(_objectSpread({}, slideStyle), {}, {\n      opacity: 1,\n      position: 'relative'\n    }),\n    prevStyle: _objectSpread({}, slideStyle)\n  };\n};\n\nexports.fadeAnimationHandler = fadeAnimationHandler;"], "names": [], "sourceRoot": "", "ignoreList": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13]}