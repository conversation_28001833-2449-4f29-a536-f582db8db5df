(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[9461],{10448:(e,s,a)=>{(window.__NEXT_P=window.__NEXT_P||[]).push(["/forgot-password",function(){return a(55428)}])},48268:(e,s,a)=>{"use strict";a.d(s,{w:()=>t});let r=e=>{let s,a=localStorage.getItem(e);try{null!==a&&(s=JSON.parse(a))}catch(e){}return s},t=()=>{let e,s=r("persist:root");try{e=JSON.parse(s.user)}catch(e){}return e}},55428:(e,s,a)=>{"use strict";a.r(s),a.d(s,{default:()=>x});var r=a(37876),t=a(14232),i=a(21772),l=a(11041),n=a(97685),o=a(48230),c=a.n(o),d=a(31777),m=a(53718),u=a(27794),h=a(48268);let N={"LOGIN.EMAIL_RESENT":"Password reset successfully. Please check email","REGISTRATION.ERROR.MAIL_NOT_SENT":"Error resetting password. Unable to find account","LOGIN.ERROR.SEND_EMAIL":"You have entered a invalid e-mail","LOGIN.USER_NOT_FOUND":"Error Resetting Password. Please enter validate E-mail","REGISTER.USER_NOT_REGISTERED":"Unable to reset password. Contact Administrator","LOGIN.ERROR.GENERIC_ERROR":"Unable to reset password. Contact Administrator"},x=(0,d.Ng)(e=>e)(e=>{let[s,a]=(0,t.useState)({email:"",username:""}),[o,d]=(0,t.useState)(!0),[x,g]=(0,t.useState)(""),[j,E]=(0,t.useState)(!1),p=e=>{g(e.target.value)},R=async e=>{let a=await m.A.get("/email/forgot-password/".concat(e));if(a&&a.success){if(n.Ay.success(N[a.message]),d(!1),s&&s.email){let{logout:e}=u.A;await e(),E(!1)}}else a.data&&a.data.message?n.Ay.error(N[a.message]):n.Ay.error("Error Resetting Password. Please enter validate E-mail")},f=async e=>{e.preventDefault();let a=x;s&&s.email&&(a=s.email),a?R(a):n.Ay.error("Error reseting password. Contact administrator")};return(0,t.useEffect)(()=>{let e=(0,h.w)();e&&e.username&&(a(e),E(!0))},[]),(0,r.jsx)("div",{className:"loginContainer ",children:(0,r.jsx)("div",{className:"section",children:(0,r.jsx)("div",{className:"container",children:(0,r.jsx)("div",{className:"columns",children:(0,r.jsx)("div",{className:"column  is-two-thirds",children:(0,r.jsxs)("div",{className:"column loginForm",children:[(0,r.jsx)("div",{className:"imgBanner",children:(0,r.jsx)("img",{src:"/images/login-banner.jpg",alt:"RKI Login Banner Image"})}),(0,r.jsxs)("form",{className:"formContainer",onSubmit:f,children:[(0,r.jsx)("div",{className:"logoContainer",children:(0,r.jsx)(c(),{href:"/",children:(0,r.jsx)("img",{src:"/images/logo.jpg",alt:"Rohert Koch Institut - Logo"})})}),j?(0,r.jsx)("div",{children:(0,r.jsxs)("section",{children:[(0,r.jsxs)("p",{children:["Password reset instructions will be mailed to ",s.email,". You must log out to use the password reset link in the email."]}),(0,r.jsx)("div",{className:"field is-grouped",children:(0,r.jsx)("div",{className:"control",children:(0,r.jsx)("button",{className:"button is-primary",type:"submit",children:"Reset Password"})})})]})}):(0,r.jsx)(r.Fragment,{children:o?(0,r.jsxs)("div",{children:[(0,r.jsxs)("div",{className:"mb-3",children:[(0,r.jsx)("label",{className:"label",children:"Enter your email id"}),(0,r.jsx)("input",{className:"form-control",type:"text",name:"username",onChange:p,required:!0})]}),(0,r.jsx)("div",{className:"field is-grouped",children:(0,r.jsx)("div",{className:"control",children:(0,r.jsx)("button",{className:"button is-primary",type:"submit",children:"Reset Password"})})})]}):(0,r.jsxs)("div",{className:"d-flex flex-column justify-content-center align-items-center",children:[(0,r.jsx)("div",{children:(0,r.jsx)(i.g,{icon:l.SGM,color:"#1a273a",size:"5x",className:"success-icon"})}),(0,r.jsx)("p",{className:"text-center lead mt-3 infotext",children:"Your one time password reset link is sent to your email. Please use that and reset your password."}),(0,r.jsx)(c(),{href:"/home",as:"/home",children:(0,r.jsxs)("button",{className:"button is-primary",children:[(0,r.jsx)(i.g,{icon:l._sz,color:"#ffff",size:"1x"})," Back to RKI Home"]})})]})})]})]})})})})})})})}},e=>{var s=s=>e(e.s=s);e.O(0,[7725,1772,636,6593,8792],()=>s(10448)),_N_E=e.O()}]);
//# sourceMappingURL=forgot-password-b6a74af5ccf5af47.js.map