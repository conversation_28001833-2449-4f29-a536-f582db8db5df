{"version": 3, "file": "static/chunks/pages/operation/components/OperationCoverSection-8d8405a4ab791cd6.js", "mappings": "4SAOO,IAAMA,EAAkBC,CAAAA,EAAAA,EAAAA,CAAAA,CAAoBA,CAAC,CAClDC,sBAAuB,KACjBC,EAAMC,WAAW,IAAID,EAAMC,WAAW,CAACC,SAAS,IAAIF,EAAMC,WAAW,CAACC,SAAS,CAAC,aAAa,CAKnGC,CALqG,kBAKjF,iBACtB,GAAG,EAEgCL,CAAAA,EAAAA,EAAAA,CAAAA,CAAoBA,CAAC,CACtDC,sBAAuB,KACjBC,EAAMC,WAAW,IAAID,EAAMC,WAAW,CAACC,SAAS,IAAIF,EAAMC,WAAW,CAACC,SAAS,CAAC,aAAa,CAKnGC,CALqG,kBAKjF,sBACpBC,iBAAkB,IAAM,UAACC,EAAAA,OAAIA,CAAAA,CAAAA,EAC/B,GAAG,EAE6BP,CAAAA,EAAAA,EAAAA,CAAAA,CAAoBA,CAAC,CACnDC,sBAAuB,CAACC,EAAOM,KAC7B,GAAIN,EAAMC,WAAW,EAAID,EAAMC,WAAW,CAACC,SAAS,EAClD,GAAIF,EAAMC,WAAW,CAACC,SAAS,CAAC,aAAa,CAC3C,CAD6C,MACtC,OAEP,GAAIF,EAAMC,WAAW,CAACC,SAAS,CAAC,aAAa,EAAE,EACnCA,SAAS,EAAII,EAAMJ,SAAS,CAACK,IAAI,EAAID,EAAMJ,SAAS,CAACK,IAAI,CAACC,GAAG,GAAKR,EAAMO,IAAI,CAACC,GAAG,CACxF,CAD0F,MACnF,CAGb,CAEF,OAAO,CACT,EACAL,mBAAoB,kBACtB,GAAG,EAEiCL,CAAAA,EAAAA,EAAAA,CAAAA,CAAoBA,CAAC,CACvDC,sBAAuB,CAACC,EAAOM,KAC7B,GAAIN,EAAMC,WAAW,EAAID,EAAMC,WAAW,CAACC,SAAS,EAAE,GAChDF,EAAMC,WAAW,CAACC,SAAS,CAAC,aAAa,CAC3C,CAD6C,MACtC,OAEP,GAAIF,EAAMC,WAAW,CAACC,SAAS,CAAC,aAAa,EAAE,EACnCA,SAAS,EAAII,EAAMJ,SAAS,CAACK,IAAI,EAAID,EAAMJ,SAAS,CAACK,IAAI,CAACC,GAAG,GAAKR,EAAMO,IAAI,CAACC,GAAG,CACxF,CAD0F,MACnF,CAGb,CAEF,OAAO,CACT,EACAL,mBAAoB,uBACpBC,iBAAkB,IAAM,UAACC,EAAAA,OAAIA,CAAAA,CAAAA,EAC/B,GAEaI,EAA0BX,CAAAA,EAAAA,EAAAA,CAAAA,CAAoBA,CAAC,CAC1DC,sBAAuB,GACjBC,IAAMC,WAAW,IAAID,EAAMC,WAAW,CAACS,MAAM,IAAIV,EAAMC,WAAW,CAACS,MAAM,CAAC,WAAW,CAK3FP,CAL6F,kBAKzE,yBACtB,GAAG,EAEYN,eAAeA,EAAC,EC1E/B,4CACA,8CACA,WACA,OAAe,EAAQ,KAAmE,CAC1F,EACA,SAFsB,6ECoCtB,MA9B0B,IACxB,GAAM,GAAEc,CAAC,CAAE,CAAGC,CAAAA,EAAAA,EAAAA,EAAAA,CAAcA,CAAC,MA6BhBC,IA5BPC,EAAiBC,SAASC,EA4BFH,EAAC,CA5B6B,EACtD,CAACI,CADyD,CAAK,EACpC,CAAGC,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,EAAC,GAO7C,MACE,iCAEIZ,EAAMa,WAAW,CACjB,UAACC,MAAAA,CACCC,wBAAyBC,CAVZ,CAACC,EAAqBC,KAElC,CAAEC,OADe,CACPC,GAD8BH,EAAYI,MAAM,CAAGb,EAAkBS,EAAYK,SAAS,CAAC,EAAGd,GAAkB,MAAQR,EAAMa,WAAW,CACzH,CACnC,EAO8Cb,EAAMa,WAAW,CAACF,GACxDY,UAAU,kBAEH,KAGTvB,EAAMa,WAAW,EAAIb,EAAMa,WAAW,CAACQ,MAAM,CAAGb,EAC9C,UAACgB,SAAAA,CAAOC,KAAK,SAASF,UAAU,eAAeG,QAAS,IAAMC,EAAc,CAAChB,YAChEN,EAAbM,EAAe,WAAgB,GAAFN,WACjB,OAItB", "sources": ["webpack://_N_E/./pages/operation/permission.tsx", "webpack://_N_E/?0096", "webpack://_N_E/./components/common/readMore/readMore.tsx"], "sourcesContent": ["//Import Library\r\nimport React from 'react';\r\nimport connectedAuthWrapper from 'redux-auth-wrapper/connectedAuthWrapper';\r\n\r\n//Import services/components\r\nimport R403 from \"../r403\";\r\n\r\nexport const canAddOperation = connectedAuthWrapper({\r\n  authenticatedSelector: (state) => {\r\n    if (state.permissions && state.permissions.operation && state.permissions.operation['create:any']) {\r\n      return true;\r\n    }\r\n    return false;\r\n  },\r\n  wrapperDisplayName: 'CanAddOperation',\r\n});\r\n\r\nexport const canAddOperationForm = connectedAuthWrapper({\r\n  authenticatedSelector: (state) => {\r\n    if (state.permissions && state.permissions.operation && state.permissions.operation['create:any']) {\r\n      return true;\r\n    }\r\n    return false;\r\n  },\r\n  wrapperDisplayName: 'CanAddOperationForm',\r\n  FailureComponent: () => <R403/>\r\n});\r\n\r\nexport const canEditOperation = connectedAuthWrapper({\r\n  authenticatedSelector: (state, props) => {\r\n    if (state.permissions && state.permissions.operation) {\r\n      if (state.permissions.operation['update:any']) {\r\n        return true;\r\n      } else { //if update:own\r\n        if (state.permissions.operation['update:own']) {\r\n          if (props.operation && props.operation.user && props.operation.user._id === state.user._id) {\r\n            return true;\r\n          }\r\n        }\r\n      }\r\n    }\r\n    return false;\r\n  },\r\n  wrapperDisplayName: 'CanEditOperation',\r\n});\r\n\r\nexport const canEditOperationForm = connectedAuthWrapper({\r\n  authenticatedSelector: (state, props) => {\r\n    if (state.permissions && state.permissions.operation) {\r\n      if (state.permissions.operation['update:any']) {\r\n        return true;\r\n      } else { //if update:own\r\n        if (state.permissions.operation['update:own']) {\r\n          if (props.operation && props.operation.user && props.operation.user._id === state.user._id) {\r\n            return true;\r\n          }\r\n        }\r\n      }\r\n    }\r\n    return false;\r\n  },\r\n  wrapperDisplayName: 'CanEditOperationForm',\r\n  FailureComponent: () => <R403/>\r\n});\r\n\r\nexport const canViewDiscussionUpdate = connectedAuthWrapper({\r\n  authenticatedSelector: (state) => {\r\n    if (state.permissions && state.permissions.update && state.permissions.update['read:any']) {\r\n      return true;\r\n    }\r\n    return false;\r\n  },\r\n  wrapperDisplayName: 'CanViewDiscussionUpdate',\r\n});\r\n\r\nexport default canAddOperation;", "\n    (window.__NEXT_P = window.__NEXT_P || []).push([\n      \"/operation/components/OperationCoverSection\",\n      function () {\n        return require(\"private-next-pages/operation/components/OperationCoverSection.tsx\");\n      }\n    ]);\n    if(module.hot) {\n      module.hot.dispose(function () {\n        window.__NEXT_P.push([\"/operation/components/OperationCoverSection\"])\n      });\n    }\n  ", "//Import Library\r\nimport { useState } from \"react\";\r\n\r\n//Import services/components\r\nimport { useTranslation } from 'next-i18next';\r\n\r\ninterface ReadMoreContainerProps {\r\n  description: string;\r\n}\r\n\r\nconst ReadMoreContainer = (props: ReadMoreContainerProps) => {\r\n  const { t } = useTranslation('common');\r\n  const readMoreLength = parseInt(process.env.READ_MORE_LENGTH || '200');\r\n  const [isReadMore, setIsReadMore] = useState(false);\r\n\r\n  const createMarkup = (htmlContent: string, isReadMoreInitial: boolean) => {\r\n    const truncateContent = (!isReadMoreInitial && htmlContent.length > readMoreLength) ? htmlContent.substring(0, readMoreLength) + \"...\" : props.description;\r\n    return { __html: truncateContent };\r\n  };\r\n\r\n  return (\r\n    <>\r\n      {\r\n        props.description  ?\r\n        <div\r\n          dangerouslySetInnerHTML={createMarkup(props.description,isReadMore)}\r\n          className=\"operationDesc\"\r\n        >\r\n        </div> : null\r\n      }\r\n      {\r\n        props.description && props.description.length > readMoreLength ?\r\n          <button type=\"button\" className=\"readMoreText\" onClick={() => setIsReadMore(!isReadMore)}>\r\n         {isReadMore ? t(\"readLess\") : t(\"readMore\")}\r\n          </button> : null\r\n      }\r\n    </>\r\n  )\r\n}\r\n\r\nexport default ReadMoreContainer;\r\n"], "names": ["canAddOperation", "connectedAuthWrapper", "authenticatedSelector", "state", "permissions", "operation", "wrapperDisplayName", "FailureComponent", "R403", "props", "user", "_id", "canViewDiscussionUpdate", "update", "t", "useTranslation", "ReadMoreContainer", "readMoreLength", "parseInt", "process", "isReadMore", "useState", "description", "div", "dangerouslySetInnerHTML", "createMarkup", "htmlContent", "isReadMoreInitial", "__html", "truncate<PERSON><PERSON><PERSON>", "length", "substring", "className", "button", "type", "onClick", "setIsReadMore"], "sourceRoot": "", "ignoreList": []}