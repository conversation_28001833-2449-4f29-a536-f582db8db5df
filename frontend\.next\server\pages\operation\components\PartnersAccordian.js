"use strict";(()=>{var e={};e.id=5056,e.ids=[636,3220,5056],e.modules={123:e=>{e.exports=require("dom-helpers/removeEventListener")},1332:e=>{e.exports=require("react-custom-scrollbars-2")},1428:e=>{e.exports=import("axios")},1680:e=>{e.exports=require("@restart/ui/utils")},1919:e=>{e.exports=require("react-transition-group/Transition")},3892:e=>{e.exports=require("classnames")},4048:e=>{e.exports=require("react-truncate")},6009:e=>{e.exports=require("dom-helpers/ownerDocument")},6952:e=>{e.exports=require("@restart/ui/Modal")},7374:e=>{e.exports=require("@restart/ui/Dropdown")},8732:e=>{e.exports=require("react/jsx-runtime")},9653:e=>{e.exports=require("@restart/ui/Overlay")},11242:e=>{e.exports=require("redux-persist/integration/react")},11688:e=>{e.exports=require("@restart/hooks/useWillUnmount")},12388:e=>{e.exports=require("dom-helpers/addEventListener")},13364:e=>{e.exports=require("redux-saga/effects")},14062:e=>{e.exports=import("react-redux")},14078:e=>{e.exports=import("swr")},14332:e=>{e.exports=require("uncontrollable")},16116:e=>{e.exports=require("invariant")},18622:e=>{e.exports=require("yup")},19121:e=>{e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},22326:e=>{e.exports=require("react-dom")},22541:e=>{e.exports=require("redux-persist/lib/storage")},25303:e=>{e.exports=require("@restart/ui/NavItem")},26324:e=>{e.exports=require("warning")},27825:e=>{e.exports=require("lodash")},27910:e=>{e.exports=require("stream")},28217:e=>{e.exports=require("@restart/ui/DropdownItem")},29021:e=>{e.exports=require("fs")},29294:e=>{e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},29628:e=>{e.exports=require("dom-helpers/addClass")},29825:e=>{e.exports=require("prop-types")},29841:e=>{e.exports=require("dom-helpers/hasClass")},30362:e=>{e.exports=require("@restart/ui/DropdownToggle")},33873:e=>{e.exports=require("path")},36653:e=>{e.exports=require("nprogress")},36955:e=>{e.exports=require("@restart/hooks/useBreakpoint")},37766:e=>{e.exports=require("@restart/hooks/useMergedRefs")},38609:e=>{e.exports=require("react-data-table-component")},39756:e=>{e.exports=import("redux")},40051:e=>{e.exports=require("dom-helpers/removeClass")},40361:e=>{e.exports=require("next/dist/compiled/next-server/pages.runtime.prod.js")},42893:e=>{e.exports=import("react-hot-toast")},43294:e=>{e.exports=require("formik")},50009:e=>{e.exports=require("@restart/ui/DropdownMenu")},54131:e=>{e.exports=import("@fortawesome/free-solid-svg-icons")},56084:(e,r,t)=>{t.d(r,{A:()=>l});var s=t(8732);t(82015);var o=t(38609),i=t.n(o),n=t(88751),a=t(30370);function u(e){let{t:r}=(0,n.useTranslation)("common"),t={rowsPerPageText:r("Rowsperpage")},{columns:o,data:u,totalRows:l,resetPaginationToggle:p,subheader:c,subHeaderComponent:d,handlePerRowsChange:x,handlePageChange:h,rowsPerPage:m,defaultRowsPerPage:g,selectableRows:q,loading:P,pagServer:f,onSelectedRowsChange:b,clearSelectedRows:j,sortServer:y,onSort:w,persistTableHead:A,sortFunction:v,...S}=e,k={paginationComponentOptions:t,noDataComponent:r("NoData"),noHeader:!0,columns:o,data:u||[],dense:!0,paginationResetDefaultPage:p,subHeader:c,progressPending:P,subHeaderComponent:d,pagination:!0,paginationServer:f,paginationPerPage:g||10,paginationRowsPerPageOptions:m||[10,15,20,25,30],paginationTotalRows:l,onChangeRowsPerPage:x,onChangePage:h,selectableRows:q,onSelectedRowsChange:b,clearSelectedRows:j,progressComponent:(0,s.jsx)(a.A,{}),sortIcon:(0,s.jsx)("i",{className:"sort-icon fas fa-exchange-alt"}),sortServer:y,onSort:w,sortFunction:v,persistTableHead:A,className:"rki-table"};return(0,s.jsx)(i(),{...k})}u.defaultProps={subHeader:!1,pagination:!0,totalRows:null,pagServer:!0,onSelectedRowsChange:null,clearSelectedRows:!1,sortServer:!1,persistTableHead:!1};let l=u},56670:(e,r,t)=>{t.a(e,async(e,s)=>{try{t.r(r),t.d(r,{default:()=>d});var o=t(8732),i=t(54131),n=t(82053),a=t(82015),u=t(93024),l=t(56678),p=t(88751),c=e([i]);i=(c.then?(await c)():c)[0];let d=e=>{let{t:r}=(0,p.useTranslation)("common"),[t,s]=(0,a.useState)(!1);return(0,o.jsx)(o.Fragment,{children:(0,o.jsxs)(u.A.Item,{eventKey:"0",children:[(0,o.jsxs)(u.A.Header,{onClick:()=>s(!t),children:[(0,o.jsx)("div",{className:"cardTitle",children:r("Partners")}),(0,o.jsx)("div",{className:"cardArrow",children:t?(0,o.jsx)(n.FontAwesomeIcon,{icon:i.faMinus,color:"#fff"}):(0,o.jsx)(n.FontAwesomeIcon,{icon:i.faPlus,color:"#fff"})})]}),(0,o.jsx)(u.A.Body,{children:(0,o.jsx)(l.default,{partners:e.operation.partners})})]})})};s()}catch(e){s(e)}})},56678:(e,r,t)=>{t.r(r),t.d(r,{default:()=>h});var s=t(8732),o=t(19918),i=t.n(o),n=t(27825),a=t.n(n),u=t(81181),l=t(63241),p=t(56084),c=t(88751);let d=({networks:e})=>e&&e.length>0?(0,s.jsx)("ul",{children:e.map((e,r)=>(0,s.jsx)("li",{children:e.title},r))}):null,x=(0,s.jsxs)(u.A,{id:"popover-basic",children:[(0,s.jsx)(u.A.Header,{as:"h3",className:"text-center",children:"NETWORKS"}),(0,s.jsx)(u.A.Body,{children:(0,s.jsxs)("div",{className:"m-2",children:[(0,s.jsxs)("p",{children:[(0,s.jsx)("b",{children:"EMLab"})," - European Mobile Lab"]}),(0,s.jsxs)("p",{children:[(0,s.jsx)("b",{children:"EMT"})," - Emergency Medical Teams"]}),(0,s.jsxs)("p",{children:[(0,s.jsx)("b",{children:"GHPP"})," - Global Health Protection Program"]}),(0,s.jsxs)("p",{children:[(0,s.jsx)("b",{children:"GOARN"})," - Global Outbreak Alert & Response Network"]}),(0,s.jsxs)("p",{children:[(0,s.jsx)("b",{children:"IANPHI"})," - International Association of National Public Health Institutes"]}),(0,s.jsxs)("p",{children:[(0,s.jsx)("b",{children:"STAKOB"})," - St\xe4ndiger Arbeitskreis der Kompetenz-und Behandlungszentren"]}),(0,s.jsxs)("p",{children:[(0,s.jsx)("b",{children:"WHOCC"}),"- World Health Organization Collaborating Centres"]})]})})]}),h=function(e){let{t:r}=(0,c.useTranslation)("common"),{partners:t}=e,o=[{name:r("Organisation"),selector:"title",cell:e=>e&&e.institution?(0,s.jsx)(i(),{href:"/institution/[...routes]",as:`/institution/show/${e.institution._id}`,children:e.institution.title}):"",sortable:!0},{name:r("Country"),selector:"country",cell:e=>e&&e.institution&&e.institution.address&&e.institution.address.country?(0,s.jsx)(i(),{href:"/country/[...routes]",as:`/country/show/${e.institution.address.country._id}`,children:e.institution.address.country.title}):"",sortable:!0},{name:r("Type"),selector:"type.title",cell:e=>e.institution&&e.institution.type&&e.institution.type.title?e.institution.type.title:"",sortable:!0},{name:(0,s.jsx)(l.A,{trigger:"click",placement:"right",overlay:x,children:(0,s.jsxs)("span",{children:[r("Network"),"\xa0\xa0\xa0",(0,s.jsx)("i",{className:"fa fa-info-circle",style:{cursor:"pointer"},"aria-hidden":"true"})]})}),selector:r("Networks"),cell:e=>e.institution&&e.institution.networks&&e.institution.networks.length>0?(0,s.jsx)(d,{networks:e.institution.networks}):""}],n=e=>{if(e.institution.address&&e.institution.address.country)return e.institution.address.country&&e.institution.address.country.title?e.institution.address.country.title.toLowerCase():e.institution.address.country.title},u=e=>{if(e.institution.type&&e.institution.type&&e.institution.type.title)return e.institution.type.title.toLowerCase()};return(0,s.jsx)(p.A,{columns:o,data:t,pagServer:!0,persistTableHead:!0,sortFunction:(e,r,t)=>a().orderBy(e,e=>{if("country"===r)n(e);else if("type.title"===r)u(e);else if(e.institution&&e.institution[r])return e.institution[r].toLowerCase()},t)})}},57664:e=>{e.exports=require("dom-helpers/scrollbarSize")},58163:(e,r,t)=>{t.a(e,async(e,s)=>{try{t.r(r),t.d(r,{config:()=>g,default:()=>d,getServerSideProps:()=>m,getStaticPaths:()=>h,getStaticProps:()=>x,reportWebVitals:()=>q,routeModule:()=>w,unstable_getServerProps:()=>j,unstable_getServerSideProps:()=>y,unstable_getStaticParams:()=>b,unstable_getStaticPaths:()=>f,unstable_getStaticProps:()=>P});var o=t(63885),i=t(80237),n=t(81413),a=t(9616),u=t.n(a),l=t(72386),p=t(56670),c=e([l,p]);[l,p]=c.then?(await c)():c;let d=(0,n.M)(p,"default"),x=(0,n.M)(p,"getStaticProps"),h=(0,n.M)(p,"getStaticPaths"),m=(0,n.M)(p,"getServerSideProps"),g=(0,n.M)(p,"config"),q=(0,n.M)(p,"reportWebVitals"),P=(0,n.M)(p,"unstable_getStaticProps"),f=(0,n.M)(p,"unstable_getStaticPaths"),b=(0,n.M)(p,"unstable_getStaticParams"),j=(0,n.M)(p,"unstable_getServerProps"),y=(0,n.M)(p,"unstable_getServerSideProps"),w=new o.PagesRouteModule({definition:{kind:i.A.PAGES,page:"/operation/components/PartnersAccordian",pathname:"/operation/components/PartnersAccordian",bundlePath:"",filename:""},components:{App:l.default,Document:u()},userland:p});s()}catch(e){s(e)}})},58928:e=>{e.exports=require("dom-helpers/canUseDOM")},59717:e=>{e.exports=require("@restart/ui/DropdownContext")},60560:e=>{e.exports=require("dom-helpers/contains")},63033:e=>{e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},65447:e=>{e.exports=require("@restart/ui/Button")},67364:e=>{e.exports=require("@restart/hooks/useCallbackRef")},68455:e=>{e.exports=import("redux-saga")},69722:e=>{e.exports=require("es6-promise")},74075:e=>{e.exports=require("zlib")},74716:e=>{e.exports=require("moment")},74987:e=>{e.exports=require("@restart/ui/ModalManager")},78097:e=>{e.exports=require("next-redux-saga")},78634:e=>{e.exports=require("@restart/ui/Anchor")},80237:(e,r)=>{Object.defineProperty(r,"A",{enumerable:!0,get:function(){return t}});var t=function(e){return e.PAGES="PAGES",e.PAGES_API="PAGES_API",e.APP_PAGE="APP_PAGE",e.APP_ROUTE="APP_ROUTE",e.IMAGE="IMAGE",e}({})},81366:e=>{e.exports=require("redux-auth-wrapper/connectedAuthWrapper")},81413:(e,r)=>{Object.defineProperty(r,"M",{enumerable:!0,get:function(){return function e(r,t){return t in r?r[t]:"then"in r&&"function"==typeof r.then?r.then(r=>e(r,t)):"function"==typeof r&&"default"===t?r:void 0}}})},81521:e=>{e.exports=require("dom-helpers/querySelectorAll")},81895:e=>{e.exports=require("@restart/hooks/useEventCallback")},82015:e=>{e.exports=require("react")},82053:e=>{e.exports=require("@fortawesome/react-fontawesome")},86842:e=>{e.exports=require("@restart/ui/SelectableContext")},87571:e=>{e.exports=require("dom-helpers/transitionEnd")},88751:e=>{e.exports=require("next-i18next")},93787:e=>{e.exports=require("redux-persist")},94947:e=>{e.exports=require("@restart/hooks/useTimeout")},96196:e=>{e.exports=require("next-redux-wrapper")},98320:e=>{e.exports=require("@restart/hooks/useIsomorphicEffect")},99460:e=>{e.exports=require("dom-helpers/css")}};var r=require("../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[6089,9216,9616,2386],()=>t(58163));module.exports=s})();