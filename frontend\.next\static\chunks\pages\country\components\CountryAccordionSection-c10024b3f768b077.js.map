{"version": 3, "file": "static/chunks/pages/country/components/CountryAccordionSection-c10024b3f768b077.js", "mappings": "sLA2BA,MAnB6BA,IACzB,GAAM,CAAEC,CAAC,CAAE,CAAGC,CAAAA,EAAAA,EAAAA,EAAAA,CAAcA,CAAC,QAkBlBC,EAjBX,MACI,UAACC,CAgByBD,CAhBzBC,CAgB0B,CAhBjBA,CAACC,iBAAiB,aACxB,WAACD,EAAAA,CAASA,CAACE,IAAI,EAACC,SAAS,cACrB,UAACH,EAAAA,CAASA,CAACI,MAAM,WACb,UAACC,MAAAA,CAAIC,UAAU,qBAAaT,EAAE,kBAElC,UAACG,EAAAA,CAASA,CAACO,IAAI,WACX,UAACC,EAAAA,CAAUA,CAAAA,CACPC,KAAK,UACLC,GAAId,OAAAA,EAAAA,KAAAA,EAAAA,EAAOe,MAAAA,CAAPf,CAAgBA,EAAMe,MAAM,CAAC,EAAE,CAAG,aAM9D,2JCsBA,MAxBiCf,IAE7B,IAAMgB,EAA0BC,CAAAA,EAAAA,EAAAA,OAAAA,CAAuBA,CAAC,IACpD,UAACd,EAAAA,OAAmBA,CAAAA,CAAE,EAqBQe,CArBLlB,CAqBM,CArBAmB,IAAI,IAGvC,MACI,iCACI,UAACf,EAAAA,CAASA,CAAAA,CAACC,iBAAiB,IAAIK,UAAU,+BACtC,UAACU,EAAAA,OAA4BA,CAAAA,CAAE,GAAGpB,CAAK,KAE3C,UAACI,EAAAA,CAASA,CAAAA,CAACM,UAAU,+BACjB,UAACW,EAAAA,OAA4BA,CAAAA,CAAE,GAAGrB,CAAK,KAE3C,UAACI,EAAAA,CAASA,CAAAA,CAACM,UAAU,+BACjB,UAACY,EAAAA,OAAwBA,CAAAA,CAAE,GAAGtB,CAAK,KAEvC,UAACI,EAAAA,CAASA,CAAAA,CAACM,UAAU,+BACjB,UAACM,EAAAA,CAAyB,GAAGhB,CAAK,OAIlD,mBC5CA,4CACA,8CACA,WACA,OAAe,EAAQ,KAAmE,CAC1F,EACA,SAFsB,kICmKtB,MAnIoB,IAClB,GAAM,CAAEC,CAAC,CAAE,CAAGC,CAAAA,EAAAA,EAAAA,EAAAA,CAAcA,CAAC,QAkIhBqB,EAjIP,CAACC,EAAQC,EAAU,CAAGC,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAc,EAAE,EAG9CC,EAAoB,IACxB,IAAMC,EAAc,8EAA8EC,IAAI,CAACC,EAAKC,WAAW,EAEvH,MACE,WAACtB,MAAAA,CAAIC,UAAU,4BACb,WAACsB,IAAAA,CAAEtB,UAAU,iBACX,UAACuB,IAAAA,UAAGhC,EAAE,cAAgB,IAAE6B,EAAKI,YAAY,EAAI,mBAE9CJ,EAAKC,WAAW,EACf,WAACtB,MAAAA,CAAIC,UAAU,wBACb,UAACsB,IAAAA,UAAE,UAACC,IAAAA,UAAGhC,EAAE,cACR2B,EACC,WAACnB,MAAAA,WACC,UAAC0B,EAAAA,CAAeA,CAAAA,CAACC,KAAMC,EAAAA,GAAMA,CAAEC,KAAK,KAAKC,MAAM,OAAO7B,UAAU,SAChE,UAAC8B,IAAAA,CAAE9B,UAAU,cAAc+B,KAAMX,EAAKC,WAAW,CAAEW,OAAO,SAASC,IAAI,+BACpEb,EAAKC,WAAW,MAIrB,UAACtB,MAAAA,UACC,UAACuB,IAAAA,CAAEtB,UAAU,YAAYkC,MAAO,CAAEC,UAAW,WAAY,WACtDf,EAAKC,WAAW,QAM1BD,EAAKgB,YAAY,EAChB,WAACC,EAAAA,CAAMA,CAAAA,CAACrC,UAAU,qCAAqC+B,KAAMX,EAAKgB,YAAY,WAC3E7C,EAAE,YACH,UAACkC,EAAAA,CAAeA,CAAAA,CAACC,KAAMY,EAAAA,GAAUA,CAAEV,KAAK,KAAK5B,UAAU,cAKjE,EA6CA,MA3CAuC,CAAAA,EAAAA,EAAAA,SAAAA,CAASA,CAAC,KACR,IAAMC,EAA8B,EAAE,CACtClD,GAASA,EAAMmD,OAAO,EAAIC,MAAMC,OAAO,CAACrD,EAAMmD,OAAO,GAAKnD,EAAMmD,OAAO,CAACG,GAAG,CAAC,CAACxB,EAAMyB,KACjF,IACIC,EADEC,EAAW3B,GAAQA,EAAK4B,IAAI,CAACC,KAAK,CAAC,KAAKC,GAAG,GAGjD,OAAQH,GACN,IAAK,MACL,IAAK,MACL,IAAK,OACL,IAAK,MACHD,EAAS,GAAwC1B,MAAAA,CAArC+B,8BAAsB,CAAC,gBAAuB,OAAT/B,EAAKgC,GAAG,EACzD,KACF,KAAK,MACHN,EAAS,gCACT,KACF,KAAK,OACHA,EAAS,iCACT,KACF,KAAK,MACL,IAAK,OACHA,EAAS,gCACT,KACF,SACEA,EAAS,iCACb,CAEA,IAAMO,EAAQ,CAAc,YAAuB,QAAbN,GAAmC,QAAbA,GAAmC,SAAbA,CAAa,CAAK,EAC/F,GAA4C3B,MAAAA,CAAzC+B,8BAAsB,CAAC,oBAA2B,OAAT/B,EAAKgC,GAAG,EACnDE,EAAQ,GAAqE,OAAlElC,GAAQA,EAAKmC,aAAa,CAAGnC,EAAKmC,aAAa,CAAG,iBAC7DC,EAAelE,EAAMmE,WAAW,EAAIf,MAAMC,OAAO,CAACrD,EAAMmE,WAAW,GACpEnE,EAAMmE,WAAW,CAACC,MAAM,CAAG,EAAIpE,EAAMmE,WAAW,CAACZ,EAAE,CAAG,GAE3DL,EAAemB,IAAI,CAAC,CAClBC,IAAKd,EACLzB,YAAamC,EACbhC,aAAc8B,EACdlB,aAAciB,CAChB,EACF,GACAtC,EAAUyB,EACZ,EAAG,CAAClD,EAAM,EAGR,UAACS,MAAAA,UACEe,GAAUA,MAAO4C,MAAM,CACtB,UAAC3D,MAAAA,CAAIC,UAAU,wCACb,UAACsB,IAAAA,CAAEtB,UAAU,wDAAgDT,EAAE,qBAGjE,UAACsE,EAAAA,EAAQA,CAAAA,CACPC,YAAY,EACZC,YAAY,EACZC,eAAgB,GAChBC,cAAc,EACdC,mBAAmB,EACnBC,UAAU,EACVC,aAAa,EACbC,WAAW,EACXC,eAAe,EACfC,cAAc,EACdC,aAAc,IACZ1D,EAAO8B,GAAG,CAAC,CAACxB,EAAMqD,IAChB,UAACC,MAAAA,CAECd,IAAKxC,EAAKwC,GAAG,CACbe,IAAK,aAAuB,OAAVF,EAAQ,GAC1BvC,MAAO,CAAE0C,MAAO,OAAQC,OAAQ,OAAQC,UAAW,OAAQ,GAHtDL,aAQV3D,EAAO8B,GAAG,CAAC,CAACxB,EAAMqD,IACjB,WAAC1E,MAAAA,WACC,UAAC2E,MAAAA,CACCd,IAAKxC,EAAKwC,GAAG,CACbe,IAAKvD,EAAKI,YAAY,EAAI,gBAC1BU,MAAO,CAAE6C,UAAW,QAASD,UAAW,SAAU,IAEnD7D,EAAkBG,KANXqD,OActB,2HCjIA,MA5BiC,IAC7B,GAAM,CAAElF,CAAC,CAAE,CAAGC,CAAAA,EAAAA,EAAAA,EAAAA,CAAcA,CAAC,QA2BlBoB,EA1BX,MACI,UAAClB,EAAAA,CAASA,CAAAA,CAACC,CAyBoBiB,EAAC,cAzBJ,aACxB,WAAClB,EAAAA,CAASA,CAACE,IAAI,EAACC,SAAS,cACrB,UAACH,EAAAA,CAASA,CAACI,MAAM,WACb,UAACC,MAAAA,CAAIC,UAAU,qBAAaT,EAAE,iBAElC,WAACG,EAAAA,CAASA,CAACO,IAAI,YACX,UAAC+E,EAAAA,CAAaA,CAAAA,CACVC,QAAS3F,EAAM2F,OAAO,CACtBC,UAAW5F,EAAM6F,UAAU,CAC3BC,KAAM9F,EAAM+F,QAAQ,CACpBC,gBAAiBhG,EAAMiG,MAAM,GAEjC,UAACC,KAAAA,CAAGxF,UAAU,gBAAQT,EAAE,0BACxB,UAACyF,EAAAA,CAAaA,CAAAA,CACVC,QAAS3F,EAAM2F,OAAO,CACtBC,UAAW5F,EAAMmG,kBAAkB,CACnCL,KAAM9F,EAAMoG,cAAc,CAC1BJ,gBAAiBhG,EAAMiG,MAAM,UAMrD,qGC9BO,IAAMhF,EAA0BoF,CAAAA,EAAAA,QAAAA,CAAAA,CAAoBA,CAAC,CAC1DC,sBAAwBC,KAClBA,EAAMC,WAAW,IAAID,EAAMC,WAAW,CAACC,MAAM,IAAIF,EAAMC,WAAW,CAACC,MAAM,CAAC,WAAW,CAK3FC,CAL6F,kBAKzE,yBACtB,GAAG,EAEYzF,uBAAuBA,EAAC,kGCUvC,MAhBqC,IACjC,GAAM,CAAEhB,CAAC,CAAE,CAAGC,CAAAA,EAAAA,EAAAA,EAAAA,CAAcA,CAAC,QAelBmB,EAdX,MACI,UAACjB,EAAAA,CAASA,CAAAA,CAACC,KAawBgB,EAAC,UAbR,aACxB,WAACjB,EAAAA,CAASA,CAACE,IAAI,EAACC,SAAS,cACrB,UAACH,EAAAA,CAASA,CAACI,MAAM,WACb,UAACC,MAAAA,CAAIC,UAAU,qBAAaT,EAAE,oBAElC,UAACG,EAAAA,CAASA,CAACO,IAAI,WACX,UAACY,EAAAA,CAAWA,CAAAA,CAAC4B,QAASnD,EAAMwB,MAAM,CAAE2C,YAAanE,EAAMwD,MAAM,SAKjF,6GCeA,SAASmD,EAAS3G,CAAoB,EACpC,GAAM,CAAEC,CAAC,CAAE,CAAGC,CAAAA,EAAAA,EAAAA,EAAAA,CAAcA,CAAC,UACvB0G,EAA6B,CACjCC,gBAAiB5G,EAAE,cACnB,EACI,SACJ6G,CAAO,MACPC,CAAI,WACJC,CAAS,CACTC,uBAAqB,CACrBC,WAAS,oBACTC,CAAkB,qBAClBC,CAAmB,kBACnBC,CAAgB,aAChBC,CAAW,CACXC,oBAAkB,gBAClBC,CAAc,SACd7B,CAAO,WACP8B,CAAS,sBACTC,CAAoB,mBACpBC,CAAiB,YACjBC,CAAU,QACVC,CAAM,kBACNC,CAAgB,cAChBC,CAAY,CAEZ,CADA,EACGC,EACJ,CAAGhI,EAGEiI,EAAiB,4BACrBrB,EACAsB,gBAAiBjI,EAAE,IAP0C,MAQ7DkI,UAAU,UACVrB,EACAC,KAAMA,GAAQ,EAAE,CAChBqB,OAAO,EACPC,2BAA4BpB,EAC5BqB,UAAWpB,EACXqB,gBAAiB5C,qBACjBwB,EACAqB,YAAY,EACZC,iBAAkBhB,EAClBiB,kBAAmBnB,GAA0C,GAC7DoB,eADwCpB,cACVD,GAA4B,CAAC,GAAI,GAAI,GAAI,CAA3BA,EAA+B,GAAG,CAC9EsB,oBAAqB5B,EACrB6B,oBAAqBzB,EACrB0B,aAAczB,EACdG,iBACAE,yCACAC,EACAoB,kBAAmB,UAACC,EAAAA,CAAYA,CAAAA,CAAAA,GAChCC,SAAU,UAAC1F,IAAAA,CAAE7C,UAAU,6CACvBkH,SACAC,eACAE,EACAD,mBACApH,UAAW,WACb,EACA,MACE,UAACwI,EAAAA,EAASA,CAAAA,CAAE,GAAGjB,CAAc,EAEjC,CAEAtB,EAASwC,YAAY,CAAG,CACtBb,UAAW,GACXE,YAAY,EACZxB,UAAW,KACXS,WAAW,EACXC,qBAAsB,KACtBC,mBAAmB,EACnBC,YAAY,EACZE,kBAAkB,CACpB,EAEA,MAAenB,QAAQA,EAAC,6GC3CxB,MAtDoD,OAAC,MAAEb,CAAI,cAsD5CJ,GAtD8CM,CAAe,SAsDhDN,EAtDkDE,CAAS,SAAED,CAAO,CAAE,GAE1FyD,EAAa,MAAOC,EAAaC,KAKrC1D,EAJiB,CACf2D,OAGQC,QAHQH,EAAOI,QAAQ,CAC/BH,cAAeA,CACjB,EAEF,EAEM,GAAErJ,CAAC,CAAE,CAAGC,CAAAA,EAAAA,EAAAA,EAAAA,CAAcA,CAAC,UAGvB4G,EAAU,CACd,CACEpD,KAAMzD,EAAE,YACRqF,MAAO,MACPmE,SAAU,YACVC,KAAM,GAAYC,GAAKA,EAAEC,SAAS,EAAID,EAAEC,SAAS,EAEnD,CACElG,KAAMzD,EAAE,YACRqF,MAAO,MACPmE,SAAU,iBACVC,KAAM,GAAYC,GAAKA,EAAE1F,aAAa,EAAI,UAACzB,IAAAA,CAAEC,KAAM,GAA4CkH,MAAAA,CAAzC9F,8BAAsB,CAAC,oBAAwB,OAAN8F,EAAE7F,GAAG,EAAIpB,OAAO,kBAAUiH,EAAE1F,aAAa,CAACN,KAAK,CAAC,KAAKkG,KAAK,CAAC,EAAG,CAAC,GAAGC,IAAI,CAAC,OACtKC,UAAU,CACZ,EACA,CACErG,KAAMzD,EAAE,eACRwJ,SAAU,cACVC,KAAM,GAAYC,GAAKA,EAAE5H,WAAW,EAAI4H,EAAE5H,WAAW,EAEvD,CACE2B,KAAMzD,EAAE,gBACRqF,MAAO,MACPmE,SAAU,iBACVC,KAAM,GAAYC,GAAKA,EAAEK,UAAU,EAAIC,IAAON,EAAEK,UAAU,EAAEE,MAAM,CAAC,cACnEH,MAD6CE,IACnC,CACZ,EACD,CAED,MACE,UAACtD,EAAAA,CAAQA,CAAAA,CACPG,QAASA,EACTC,KAAMjB,EACN2B,WAAW,EACXI,OAAQuB,EACRtB,gBAAgB,IAChBnC,QAASA,GAIf,6ICyDA,MAlHA,SAASwE,CAA4B,EACnC,GAAM,CAACC,EAAWC,EAAe,CAAG3I,CAAAA,EAAAA,EAAAA,EAiHvByI,MAjHuBzI,CAAQA,CAAC,EAAE,EACzC,EAAG4I,EAAW,CAAG5I,CAAAA,CAgHQ,CAhHRA,EAAAA,QAAAA,CAAQA,EAAC,GAC1B,CAACsF,EAAWuD,EAAa,CAAG7I,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAC,GACrC,CAAC8I,EAAQ,CAAG9I,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAC,IACrB+I,EAAazK,GAASA,EAAMe,MAAM,CAAGf,EAAMe,MAAM,CAAC,EAAE,CAAG,KACxD,CAAC2J,EAAUC,EAAY,CAAGjJ,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAM,MACvC,GAAEzB,CAAC,MAAC2K,CAAI,CAAE,CAAG1K,CAAAA,EAAAA,EAAAA,EAAAA,CAAcA,CAAC,UAG5B2K,EAAa,CACjBC,KAAM,CAAC,EACPC,MAAOP,EACPQ,KAAM,EACNC,YAAY,EACZC,MAAO,CAAE,EACTC,aARkBP,CAQLQ,CARUC,QASzB,EAEMvE,EAAU,CACd,CACEpD,KAAMzD,EAAE,gBACRwJ,SAAU,QACVC,KAAM,GACJ,UAAC4B,IAAIA,CAAC7I,KAAK,2BAA2B8I,GAAI,SAArCD,YAAgE,OAAN3B,EAAE7F,GAAG,WACjE6F,EAAE6B,KAAK,GAGZzB,UAAU,EACV0B,SAAU,OACZ,EACA,CACE/H,KAAMzD,EAAE,eACRwJ,SAAU,eACVC,KAAM,GACJC,EAAE+B,IAAI,CAAG/B,EAAE+B,IAAI,CAACC,QAAQ,CAAG,GAE7BF,SAAU,OACZ,EACA,CACE/H,KAAMzD,EAAE,aACRwJ,SAAU,YACVgC,SAAU,OACZ,EACA,CACE/H,KAAMzD,EAAE,UACRwJ,SAAU,iBACVgC,SAAU,OACZ,EACD,CAEKrC,EAAa,MAAOC,EAAaC,KACrCgB,GAAW,GACXO,EAAWC,IAAI,CAAG,CAAC,CAACzB,EAAOI,QAAQ,CAAC,CAAEH,CAAa,EACnD,IAAMsC,EAAiB,CACrBd,KAAM,CAAE,CAACzB,EAAOI,QAAQ,CAAC,CAAEH,CAAc,EACzCyB,MAAOP,EACPQ,KAAM,EACNC,YAAW,EACXC,MAAO,CAAC,CACV,EACAP,EAAYiB,GACZC,EAAYD,EACd,EAEMC,EAAc,MAAOC,IACzBxB,GAAW,GAERyB,GAAgD,OAAzCC,IAAI,CAACF,EAAe,IAAO,EAAE1H,MAAV,GAC3B0H,EAAehB,IAAI,CAAG,CAACmB,WAAa,OAAM,EAG5C,IAAMC,EAAW,MAAMC,EAAAA,CAAUA,CAACC,GAAG,CAAC,YAAuB,OAAX3B,EAAW,gBAAeqB,GAC1ExB,GAAW,GACP4B,GAAYA,EAASnF,IAAI,EAAImF,EAASnF,IAAI,CAAC3C,MAAM,CAAG,GAAG,CAC3D8H,EAASnF,IAAI,CAACsF,OAAO,CAAC,CAACC,EAAcnH,KACnC+G,EAASnF,IAAI,CAAC5B,EAAM,CAACoH,SAAS,CAAGD,EAAQC,SAAS,CAACjJ,GAAG,CAAC,GAAYkJ,EAAEhB,KAAK,EAAE1B,IAAI,CAAC,MACjFoC,EAASnF,IAAI,CAAC5B,EAAM,CAACsH,OAAO,CAACC,MAAM,CAAGJ,EAAQG,OAAO,CAACC,MAAM,CAACpJ,GAAG,CAAC,GAAYkJ,EAAEhB,KAAK,EAAE1B,IAAI,CAAC,KAC7F,GACAO,EAAe6B,EAASnF,IAAI,EAC5BwD,EAAa2B,EAASS,UAAU,GAElCrC,GAAW,EACb,EAQMlD,EAAsB,MAAOwF,EAAiB5B,KAClDH,EAAWE,KAAK,CAAG6B,EACnB/B,EAAWG,IAAI,CAAGA,EAClBN,GAAaG,GAAWC,IAAI,CAAGJ,CAAlBG,CAA2BC,IAAAA,EACxCe,EAAYhB,EACd,EAKA,MAHA5H,CAAAA,EAAAA,EAAAA,SAAAA,CAASA,CAAC,KACR4I,EAAYhB,EACd,EAAG,EAAE,EAEH,UAAClE,EAAAA,CAAQA,CAAAA,CACPG,QAASA,EACTC,KAAMqD,EACNpD,UAAWA,EACXI,oBAAqBA,EACrBC,iBAvBqB,CAuBHA,GAtBpBwD,EAAWE,KAAK,CAAGP,EACnBK,EAAWG,IAAI,CAAGA,EAClBN,GAAaG,GAAWC,IAAI,CAAGJ,CAAlBG,CAA2BC,IAAI,EAC5Ce,EAAYhB,EACd,EAmBI/C,gBAAgB,IAChBD,OAAQuB,GAGd,yBClHuC,CAGtC,YAA4B,aAI7B,oBACA,OACA,iCACA,iCACA,0BACA,0BACA,+BACA,8BACA,8BACA,4BACA,6BAEA,yBAGA,qBACA,qGACA,KAEA,YACA,2EACA,oBACA,SACA,oEACA,KAEA,uDACA,8CACA,sBACA,gBACA,WACA,eACA,eACA,kBACA,yBACA,+BACA,CAAS,CACT,UACA,8BACA,aACA,+BACA,8BACA,gCACA,uCACA,CAAS,CACT,cACA,eACA,cACA,sBACA,iBACA,IACA,gBACA,IACA,gBACA,IACA,KACA,IACA,eACA,IACA,KACA,IACA,IACA,CAAS,CACT,2BAAoC,IAAI,IACxC,cACA,MACA,MACA,KACA,CAAS,CACJ,CAIL,CAAC,EAhFiD,EAAQ,KAAW,YAAZ,iJCsBzD,MAtBqC,IACjC,GAAM,CAAEnJ,CAAC,CAAE,CAAGC,CAAAA,EAAAA,EAAAA,EAAAA,CAAcA,CAAC,QAqBlBkB,EApBX,MACI,UAAChB,EAAAA,CAASA,CAAAA,CAACC,KAmBwBe,EAAC,UAnBR,aACxB,WAAChB,EAAAA,CAASA,CAACE,IAAI,EAACC,SAAS,cACrB,UAACH,EAAAA,CAASA,CAACI,MAAM,WACb,UAACC,MAAAA,CAAIC,UAAU,qBAAaT,EAAE,oBAElC,UAACG,EAAAA,CAASA,CAACO,IAAI,WACX,UAACkM,EAAAA,CAASA,CAAAA,CAACC,KAAK,aACZ,UAACC,EAAAA,CAAGA,CAAAA,UACA,UAACC,EAAAA,CAAGA,CAAAA,UACA,UAAC7C,EAAAA,OAAiBA,CAAAA,CAAE,GAAGnK,EAAMmB,IAAI,eAQjE", "sources": ["webpack://_N_E/./pages/country/components/DiscussionAccordion.tsx", "webpack://_N_E/./pages/country/components/CountryAccordionSection.tsx", "webpack://_N_E/?c752", "webpack://_N_E/./components/common/ReactImages.tsx", "webpack://_N_E/./pages/country/components/CountryDocumentAccordion.tsx", "webpack://_N_E/./pages/country/permission.tsx", "webpack://_N_E/./pages/country/components/CountryMediaGalleryAccordion.tsx", "webpack://_N_E/./components/common/RKITable.tsx", "webpack://_N_E/./components/common/DocumentTable.tsx", "webpack://_N_E/./pages/country/OrganizationTable.tsx", "webpack://_N_E/./node_modules/moment/locale/de.js", "webpack://_N_E/./pages/country/components/CountryOrganisationAccordion.tsx"], "sourcesContent": ["//Import Library\r\nimport React from \"react\";\r\nimport { Accordion } from \"react-bootstrap\";\r\n\r\n//Import services/components\r\nimport Discussion from \"../../../components/common/disussion\";\r\nimport { useTranslation } from 'next-i18next';\r\n\r\nconst DiscussionAccordion = (props: any) => {\r\n    const { t } = useTranslation('common');\r\n    return (\r\n        <Accordion defaultActiveKey=\"1\">\r\n            <Accordion.Item eventKey=\"1\">\r\n                <Accordion.Header>\r\n                    <div className=\"cardTitle\">{t(\"Discussion\")}</div>\r\n                </Accordion.Header>\r\n                <Accordion.Body>\r\n                    <Discussion\r\n                        type=\"country\"\r\n                        id={props?.routes ? props.routes[1] : null}\r\n                    />\r\n                </Accordion.Body>\r\n            </Accordion.Item>\r\n        </Accordion>\r\n    );\r\n};\r\n\r\nexport default DiscussionAccordion;\r\n", "//Import Library\r\nimport React from \"react\";\r\nimport { Accordion } from \"react-bootstrap\";\r\n\r\n//Import services/components\r\nimport CountryOrganisationAccordion from \"./CountryOrganisationAccordion\";\r\nimport CountryMediaGalleryAccordion from \"./CountryMediaGalleryAccordion\";\r\nimport CountryDocumentAccordion from \"./CountryDocumentAccordion\";\r\nimport DiscussionAccordion from \"./DiscussionAccordion\";\r\nimport canViewDiscussionUpdate from \"../permission\";\r\n\r\ninterface CountryAccordionSectionProps {\r\n  prop: any;\r\n  images: any[];\r\n  imgSrc: string[];\r\n  loading: boolean;\r\n  updateSort: any;\r\n  document: any[];\r\n  docSrc: string[];\r\n  updateDocumentSort: any;\r\n  updateDocument: any[];\r\n}\r\n\r\nconst CountryAccordionSection = (props: CountryAccordionSectionProps) => {\r\n\r\n    const CanViewDiscussionUpdate = canViewDiscussionUpdate(() => (\r\n        <DiscussionAccordion {...props.prop} />\r\n      ));\r\n\r\n    return (\r\n        <>\r\n            <Accordion defaultActiveKey=\"0\" className=\"countryAccordionNew\">\r\n                <CountryOrganisationAccordion {...props} />\r\n            </Accordion>\r\n            <Accordion className=\"countryAccordionNew\">\r\n                <CountryMediaGalleryAccordion {...props} />\r\n            </Accordion>\r\n            <Accordion className=\"countryAccordionNew\">\r\n                <CountryDocumentAccordion {...props} />\r\n            </Accordion>\r\n            <Accordion className=\"countryAccordionNew\">\r\n                <CanViewDiscussionUpdate {...props} />\r\n            </Accordion>\r\n        </>\r\n    )\r\n}\r\n\r\nexport default CountryAccordionSection;", "\n    (window.__NEXT_P = window.__NEXT_P || []).push([\n      \"/country/components/CountryAccordionSection\",\n      function () {\n        return require(\"private-next-pages/country/components/CountryAccordionSection.tsx\");\n      }\n    ]);\n    if(module.hot) {\n      module.hot.dispose(function () {\n        window.__NEXT_P.push([\"/country/components/CountryAccordionSection\"])\n      });\n    }\n  ", "\r\n//Import Library\r\nimport React, { useState, useEffect } from 'react';\r\nimport { Carousel } from 'react-responsive-carousel';\r\nimport { FontAwesomeIcon } from \"@fortawesome/react-fontawesome\";\r\nimport {\r\n  faLink, faDownload\r\n} from \"@fortawesome/free-solid-svg-icons\";\r\nimport { Button } from \"react-bootstrap\";\r\n\r\n//Import services/components\r\nimport { useTranslation } from 'next-i18next';\r\n\r\n// Import CSS for react-responsive-carousel\r\nimport \"react-responsive-carousel/lib/styles/carousel.min.css\";\r\n\r\n// Define types for image items\r\ninterface ImageItem {\r\n  src: string;\r\n  description: string;\r\n  originalName: string;\r\n  downloadLink: string | false;\r\n}\r\n\r\ninterface ReactImagesProps {\r\n  gallery?: Array<{\r\n    _id: string;\r\n    name: string;\r\n    original_name?: string;\r\n    src?: string;\r\n    caption?: string;\r\n    alt?: string;\r\n  }> | boolean;\r\n  imageSource?: string[] | boolean;\r\n}\r\n\r\nconst ReactImages = (props: ReactImagesProps) => {\r\n  const { t } = useTranslation('common');\r\n  const [images, setImages] = useState<ImageItem[]>([]);\r\n\r\n  // Render image description and metadata\r\n  const renderImageLegend = (item: ImageItem) => {\r\n    const isValidLink = /(http|https):\\/\\/(\\w+:{0,1}\\w*)?(\\S+)(:[0-9]+)?(\\/|\\/([\\w#!:.?+=&%!\\-\\/]))?/.test(item.description);\r\n\r\n    return (\r\n      <div className=\"carousel-legend\">\r\n        <p className=\"lead\">\r\n          <b>{t(\"Filename\")}</b> {item.originalName || \"No Name found\"}\r\n        </p>\r\n        {item.description && (\r\n          <div className=\"source_link\">\r\n            <p><b>{t(\"Source\")}</b></p>\r\n            {isValidLink ? (\r\n              <div>\r\n                <FontAwesomeIcon icon={faLink} size=\"1x\" color=\"#999\" className=\"me-1\" />\r\n                <a className=\"source_link\" href={item.description} target=\"_blank\" rel=\"noopener noreferrer\">\r\n                  {item.description}\r\n                </a>\r\n              </div>\r\n            ) : (\r\n              <div>\r\n                <p className=\"ps-0 py-0\" style={{ wordBreak: \"break-all\" }}>\r\n                  {item.description}\r\n                </p>\r\n              </div>\r\n            )}\r\n          </div>\r\n        )}\r\n        {item.downloadLink && (\r\n          <Button className=\"btn btn-success mt-2 btn--download\" href={item.downloadLink}>\r\n            {t(\"Download\")}\r\n            <FontAwesomeIcon icon={faDownload} size=\"1x\" className=\"ms-1\" />\r\n          </Button>\r\n        )}\r\n      </div>\r\n    );\r\n  };\r\n\r\n  useEffect(() => {\r\n    const carouselImages: ImageItem[] = [];\r\n    props && props.gallery && Array.isArray(props.gallery) && props.gallery.map((item, i) => {\r\n      const fileType = item && item.name.split('.').pop();\r\n      let imgSrc;\r\n\r\n      switch (fileType) {\r\n        case \"JPG\":\r\n        case \"jpg\":\r\n        case \"jpeg\":\r\n        case \"png\":\r\n          imgSrc = `${process.env.API_SERVER}/image/show/${item._id}`;\r\n          break;\r\n        case \"pdf\":\r\n          imgSrc = \"/images/fileIcons/pdfFile.png\";\r\n          break;\r\n        case \"docx\":\r\n          imgSrc = \"/images/fileIcons/wordFile.png\";\r\n          break;\r\n        case \"xls\":\r\n        case 'xlsx':\r\n          imgSrc = \"/images/fileIcons/xlsFile.png\";\r\n          break;\r\n        default:\r\n          imgSrc = \"/images/fileIcons/otherFile.png\";\r\n      }\r\n\r\n      const _link = (fileType === \"docx\" || fileType === \"pdf\" || fileType === \"xls\" || fileType === \"xlsx\")\r\n        && `${process.env.API_SERVER}/files/download/${item._id}`;\r\n      const _name = `${item && item.original_name ? item.original_name : \"No Name found\"}`;\r\n      const _description = props.imageSource && Array.isArray(props.imageSource)\r\n        && props.imageSource.length > 0 ? props.imageSource[i] : \"\";\r\n\r\n      carouselImages.push({\r\n        src: imgSrc,\r\n        description: _description,\r\n        originalName: _name,\r\n        downloadLink: _link\r\n      });\r\n    });\r\n    setImages(carouselImages);\r\n  }, [props]);\r\n\r\n  return (\r\n    <div>\r\n      {images && images.length === 0 ? (\r\n        <div className=\"border border-info my-3 mx-0\">\r\n          <p className=\"d-flex d-flex justify-content-center p-2 m-0\">{t(\"NoFilesFound!\")}</p>\r\n        </div>\r\n      ) : (\r\n        <Carousel\r\n          showThumbs={true}\r\n          showStatus={true}\r\n          showIndicators={true}\r\n          infiniteLoop={true}\r\n          useKeyboardArrows={true}\r\n          autoPlay={false}\r\n          stopOnHover={true}\r\n          swipeable={true}\r\n          dynamicHeight={false}\r\n          emulateTouch={true}\r\n          renderThumbs={() =>\r\n            images.map((item, index) => (\r\n              <img\r\n                key={index}\r\n                src={item.src}\r\n                alt={`Thumbnail ${index + 1}`}\r\n                style={{ width: '60px', height: '60px', objectFit: 'cover' }}\r\n              />\r\n            ))\r\n          }\r\n        >\r\n          {images.map((item, index) => (\r\n            <div key={index}>\r\n              <img\r\n                src={item.src}\r\n                alt={item.originalName || \"Gallery image\"}\r\n                style={{ maxHeight: '500px', objectFit: 'contain' }}\r\n              />\r\n              {renderImageLegend(item)}\r\n            </div>\r\n          ))}\r\n        </Carousel>\r\n      )}\r\n    </div>\r\n  );\r\n\r\n}\r\n\r\nexport default ReactImages;\r\n", "//Import Library\r\nimport React from \"react\";\r\nimport { Accordion } from \"react-bootstrap\";\r\n\r\n//Import services/components\r\nimport { useTranslation } from 'next-i18next';\r\nimport DocumentTable from \"../../../components/common/DocumentTable\";\r\n\r\nconst CountryDocumentAccordion = (props: any) => {\r\n    const { t } = useTranslation('common');\r\n    return (\r\n        <Accordion defaultActiveKey=\"0\">\r\n            <Accordion.Item eventKey=\"0\">\r\n                <Accordion.Header>\r\n                    <div className=\"cardTitle\">{t(\"Documents\")}</div>\r\n                </Accordion.Header>\r\n                <Accordion.Body>\r\n                    <DocumentTable\r\n                        loading={props.loading}\r\n                        sortProps={props.updateSort}\r\n                        docs={props.document}\r\n                        docsDescription={props.docSrc}\r\n                    />\r\n                    <h6 className=\"mt-3\">{t(\"DocumentsfromUpdates\")}</h6>\r\n                    <DocumentTable\r\n                        loading={props.loading}\r\n                        sortProps={props.updateDocumentSort}\r\n                        docs={props.updateDocument}\r\n                        docsDescription={props.docSrc}\r\n                    />\r\n                </Accordion.Body>\r\n            </Accordion.Item>\r\n        </Accordion>\r\n    )\r\n}\r\n\r\nexport default CountryDocumentAccordion;", "//Import Library\r\nimport React from 'react';\r\nimport connectedAuthWrapper from 'redux-auth-wrapper/connectedAuthWrapper';\r\n\r\nexport const canViewDiscussionUpdate = connectedAuthWrapper({\r\n  authenticatedSelector: (state) => {\r\n    if (state.permissions && state.permissions.update && state.permissions.update['read:any']) {\r\n      return true;\r\n    }\r\n    return false;\r\n  },\r\n  wrapperDisplayName: 'CanViewDiscussionUpdate',\r\n});\r\n\r\nexport default canViewDiscussionUpdate;", "//Import Library\r\nimport React from \"react\";\r\nimport { Accordion } from \"react-bootstrap\";\r\n\r\n//Import services/components\r\nimport ReactImages from \"../../../components/common/ReactImages\";\r\nimport { useTranslation } from 'next-i18next';\r\n\r\nconst CountryMediaGalleryAccordion = (props: any) => {\r\n    const { t } = useTranslation('common');\r\n    return (\r\n        <Accordion defaultActiveKey=\"0\">\r\n            <Accordion.Item eventKey=\"0\">\r\n                <Accordion.Header>\r\n                    <div className=\"cardTitle\">{t(\"MediaGallery\")}</div>\r\n                </Accordion.Header>\r\n                <Accordion.Body>\r\n                    <ReactImages gallery={props.images} imageSource={props.imgSrc} />\r\n                </Accordion.Body>\r\n            </Accordion.Item>\r\n        </Accordion>\r\n    )\r\n}\r\n\r\nexport default CountryMediaGalleryAccordion;", "//Import Library\r\nimport React from 'react'\r\nimport DataTable  from 'react-data-table-component';\r\n\r\n//Import services/components\r\nimport { useTranslation } from 'next-i18next';\r\nimport CustomLoader from './CustomLoader';\r\n\r\ninterface RKITableProps {\r\n  columns: any[];\r\n  data: any[];\r\n  totalRows?: number;\r\n  resetPaginationToggle?: boolean;\r\n  subheader?: boolean;\r\n  subHeaderComponent?: React.ReactNode;\r\n  handlePerRowsChange?: (newPerPage: number, page: number) => void;\r\n  handlePageChange?: (page: number) => void;\r\n  rowsPerPage?: number[];\r\n  defaultRowsPerPage?: number;\r\n  selectableRows?: boolean;\r\n  loading?: boolean;\r\n  pagServer?: boolean;\r\n  onSelectedRowsChange?: (selectedRows: any) => void;\r\n  clearSelectedRows?: boolean;\r\n  sortServer?: boolean;\r\n  onSort?: (column: any, sortDirection: string) => void;\r\n  persistTableHead?: boolean;\r\n  sortFunction?: (rows: any[], field: any, direction: 'asc' | 'desc') => any[];\r\n  noHeader?: boolean;\r\n  dense?: boolean;\r\n  pagination?: boolean;\r\n  paginationServer?: boolean;\r\n  paginationTotalRows?: number;\r\n  subHeaderAlign?: string;\r\n  [key: string]: any;\r\n}\r\n\r\nfunction RKITable(props: RKITableProps) {\r\n  const { t } = useTranslation('common');\r\n  const paginationComponentOptions = {\r\n    rowsPerPageText: t('Rowsperpage'),\r\n    };\r\n  const {\r\n    columns,\r\n    data,\r\n    totalRows,\r\n    resetPaginationToggle,\r\n    subheader,\r\n    subHeaderComponent,\r\n    handlePerRowsChange,\r\n    handlePageChange,\r\n    rowsPerPage,\r\n    defaultRowsPerPage,\r\n    selectableRows,\r\n    loading,\r\n    pagServer,\r\n    onSelectedRowsChange,\r\n    clearSelectedRows,\r\n    sortServer,\r\n    onSort,\r\n    persistTableHead,\r\n    sortFunction,\r\n    // Filter out any unknown props that might cause DOM warnings\r\n    ...otherProps\r\n  } = props;\r\n\r\n  // Only pass known DataTable props to avoid DOM warnings\r\n  const dataTableProps = {\r\n    paginationComponentOptions,\r\n    noDataComponent: t(\"NoData\"),\r\n    noHeader: true,\r\n    columns,\r\n    data: data || [],\r\n    dense: true,\r\n    paginationResetDefaultPage: resetPaginationToggle,\r\n    subHeader: subheader,\r\n    progressPending: loading,\r\n    subHeaderComponent,\r\n    pagination: true,\r\n    paginationServer: pagServer,\r\n    paginationPerPage: defaultRowsPerPage ? defaultRowsPerPage : 10,\r\n    paginationRowsPerPageOptions: rowsPerPage ? rowsPerPage : [10, 15, 20, 25, 30],\r\n    paginationTotalRows: totalRows,\r\n    onChangeRowsPerPage: handlePerRowsChange,\r\n    onChangePage: handlePageChange,\r\n    selectableRows,\r\n    onSelectedRowsChange,\r\n    clearSelectedRows,\r\n    progressComponent: <CustomLoader/>,\r\n    sortIcon: <i className=\"sort-icon fas fa-exchange-alt\" />,\r\n    sortServer,\r\n    onSort,\r\n    sortFunction,\r\n    persistTableHead,\r\n    className: \"rki-table\"\r\n  };\r\n  return (\r\n    <DataTable {...dataTableProps} />\r\n  )\r\n};\r\n\r\nRKITable.defaultProps = {\r\n  subHeader: false,\r\n  pagination: true,\r\n  totalRows: null,\r\n  pagServer: true,\r\n  onSelectedRowsChange: null,\r\n  clearSelectedRows: false,\r\n  sortServer: false,\r\n  persistTableHead: false\r\n}\r\n\r\nexport default RKITable;\r\n\r\n", "//Import Library\r\nimport React from \"react\";\r\nimport moment from \"moment\";\r\n\r\n//Import services/components\r\nimport RKITable from '../../components/common/RKITable';\r\nimport { useTranslation } from 'next-i18next';\r\n\r\ninterface DocumentTableProps {\r\n  docs: any[];\r\n  docsDescription: string;\r\n  sortProps: (sortObj: { columnSelector: string; sortDirection: string }) => void;\r\n  loading: boolean;\r\n}\r\n\r\nconst DocumentTable: React.FC<DocumentTableProps> = ({ docs, docsDescription, sortProps, loading }) => {\r\n\r\n  const handleSort = async (column: any, sortDirection: string) => {\r\n    const objSlect = {\r\n      columnSelector: column.selector,\r\n      sortDirection: sortDirection\r\n    }\r\n    sortProps(objSlect);\r\n  };\r\n\r\n  const { t } = useTranslation('common');\r\n\r\n\r\n  const columns = [\r\n    {\r\n      name: t(\"FileType\"),\r\n      width: \"15%\",\r\n      selector: 'extension',\r\n      cell: (d: any) => d && d.extension && d.extension,\r\n    },\r\n    {\r\n      name: t(\"FileName\"),\r\n      width: \"25%\",\r\n      selector: \"document_title\",\r\n      cell: (d: any) => d && d.original_name && <a href={`${process.env.API_SERVER}/files/download/${d._id}`} target=\"_blank\">{d.original_name.split('.').slice(0, -1).join('.')}</a>,\r\n      sortable: true\r\n    },\r\n    {\r\n      name: t(\"Description\"),\r\n      selector: 'description',\r\n      cell: (d: any) => d && d.description && d.description,\r\n    },\r\n    {\r\n      name: t(\"UploadedDate\"),\r\n      width: \"25%\",\r\n      selector: 'doc_created_at',\r\n      cell: (d: any) => d && d.updated_at && moment(d.updated_at).format('MM/DD/YYYY'),\r\n      sortable: true\r\n    }\r\n  ];\r\n\r\n  return (\r\n    <RKITable\r\n      columns={columns}\r\n      data={docs}\r\n      pagServer={true}\r\n      onSort={handleSort}\r\n      persistTableHead\r\n      loading={loading}\r\n    />\r\n\r\n  )\r\n}\r\n\r\nexport default DocumentTable;\r\n", "//Import Library\r\nimport { useEffect, useState } from \"react\";\r\nimport Link from \"next/link\";\r\n\r\n//Import services/components\r\nimport RKITable from \"../../components/common/RKITable\";\r\nimport apiService from \"../../services/apiService\";\r\nimport { useTranslation } from 'next-i18next';\r\n\r\n\r\nfunction OrganizationTable(props: any) {\r\n  const [tabledata, setDataToTable] = useState([]);\r\n  const [, setLoading] = useState(false);\r\n  const [totalRows, setTotalRows] = useState(0);\r\n  const [perPage] = useState(10);\r\n  const _countryId = props && props.routes ? props.routes[1] : null;\r\n  const[pageSort, setPageSort] = useState<any>(null);\r\n  const { t,i18n } = useTranslation('common');\r\n  const currentLang = i18n.language;\r\n\r\n  const instParams = {\r\n    sort: {},\r\n    limit: perPage,\r\n    page: 1,\r\n    instiTable: true,\r\n    query: { },\r\n    languageCode:currentLang\r\n  };\r\n\r\n  const columns = [\r\n    {\r\n      name: t(\"Organisation\"),\r\n      selector: \"title\",\r\n      cell: (d: any) => (\r\n        <Link href=\"/institution/[...routes]\" as={`/institution/show/${d._id}`}>\r\n          {d.title}\r\n        </Link>\r\n      ),\r\n      sortable: true,\r\n      maxWidth: \"200px\"\r\n    },\r\n    {\r\n      name: t(\"ContactName\"),\r\n      selector: \"contact_name\",\r\n      cell: (d: any) => (\r\n        d.user ? d.user.username : ''\r\n      ),\r\n      maxWidth: \"200px\"\r\n    },\r\n    {\r\n      name: t(\"Expertise\"),\r\n      selector: \"expertise\",\r\n      maxWidth: \"200px\"\r\n    },\r\n    {\r\n      name: t(\"Region\"),\r\n      selector: \"address.region\",\r\n      maxWidth: \"200px\"\r\n    }\r\n  ];\r\n\r\n  const handleSort = async (column: any, sortDirection: string) => {\r\n    setLoading(true);\r\n    instParams.sort = {[column.selector]: sortDirection};\r\n    const instSortParams = {\r\n      sort: { [column.selector]: sortDirection },\r\n      limit: perPage,\r\n      page: 1,\r\n      instiTable:true,\r\n      query: {}\r\n    };\r\n    setPageSort(instSortParams)\r\n    getInstData(instSortParams);\r\n  };\r\n\r\n  const getInstData = async (instParamsinit: any) => {\r\n    setLoading(true);\r\n    \r\n    if(Object.keys(instParamsinit['sort']).length == 0){\r\n      instParamsinit.sort = {created_at : 'desc'}\r\n    }\r\n    \r\n    const response = await apiService.get(`/country/${_countryId}/institution`, instParamsinit);\r\n      setLoading(true);\r\n      if (response && response.data && response.data.length > 0) {\r\n      response.data.forEach((element: any, index: number) => {\r\n        response.data[index].expertise = element.expertise.map((e: any) => e.title).join(', ');\r\n        response.data[index].address.region = element.address.region.map((e: any) => e.title).join(', ');\r\n      });\r\n      setDataToTable(response.data);\r\n      setTotalRows(response.totalCount);\r\n    }\r\n    setLoading(false);\r\n  };\r\n  const handlePageChange = (page: any) => {\r\n    instParams.limit = perPage;\r\n    instParams.page = page;\r\n    pageSort && (instParams.sort = pageSort.sort);\r\n    getInstData(instParams);\r\n  };\r\n\r\n  const handlePerRowsChange = async (newPerPage: any, page: any) => {\r\n    instParams.limit = newPerPage;\r\n    instParams.page = page;\r\n    pageSort && (instParams.sort = pageSort.sort);\r\n    getInstData(instParams);\r\n  };\r\n\r\n  useEffect(() => {\r\n    getInstData(instParams);\r\n  }, []);\r\n  return (\r\n    <RKITable\r\n      columns={columns}\r\n      data={tabledata}\r\n      totalRows={totalRows}\r\n      handlePerRowsChange={handlePerRowsChange}\r\n      handlePageChange={handlePageChange}\r\n      persistTableHead\r\n      onSort={handleSort}\r\n    />\r\n  );\r\n}\r\n\r\nexport default OrganizationTable;\r\n", "//! moment.js locale configuration\n//! locale : German [de]\n//! author : lluchs : https://github.com/lluchs\n//! author: <PERSON><PERSON><PERSON>: https://github.com/Oire\n//! author : <PERSON><PERSON><PERSON><PERSON> : https://github.com/mik01aj\n\n;(function (global, factory) {\n   typeof exports === 'object' && typeof module !== 'undefined'\n       && typeof require === 'function' ? factory(require('../moment')) :\n   typeof define === 'function' && define.amd ? define(['../moment'], factory) :\n   factory(global.moment)\n}(this, (function (moment) { 'use strict';\n\n    //! moment.js locale configuration\n\n    function processRelativeTime(number, withoutSuffix, key, isFuture) {\n        var format = {\n            m: ['eine Minute', 'einer Minute'],\n            h: ['eine Stunde', 'einer Stunde'],\n            d: ['ein Tag', 'einem Tag'],\n            dd: [number + ' Tage', number + ' Tagen'],\n            w: ['eine Woche', 'einer Woche'],\n            M: ['ein <PERSON><PERSON>', 'einem Monat'],\n            MM: [number + ' <PERSON><PERSON>', number + ' Monaten'],\n            y: ['ein Jahr', 'einem Jahr'],\n            yy: [number + ' Jahre', number + ' Jahren'],\n        };\n        return withoutSuffix ? format[key][0] : format[key][1];\n    }\n\n    var de = moment.defineLocale('de', {\n        months: 'Januar_Februar_März_April_Mai_Juni_Juli_August_September_Oktober_November_Dezember'.split(\n            '_'\n        ),\n        monthsShort:\n            'Jan._Feb._März_Apr._Mai_Juni_Juli_Aug._Sep._Okt._Nov._Dez.'.split('_'),\n        monthsParseExact: true,\n        weekdays:\n            'Sonntag_Montag_Dienstag_Mittwoch_Donnerstag_Freitag_Samstag'.split(\n                '_'\n            ),\n        weekdaysShort: 'So._Mo._Di._Mi._Do._Fr._Sa.'.split('_'),\n        weekdaysMin: 'So_Mo_Di_Mi_Do_Fr_Sa'.split('_'),\n        weekdaysParseExact: true,\n        longDateFormat: {\n            LT: 'HH:mm',\n            LTS: 'HH:mm:ss',\n            L: 'DD.MM.YYYY',\n            LL: 'D. MMMM YYYY',\n            LLL: 'D. MMMM YYYY HH:mm',\n            LLLL: 'dddd, D. MMMM YYYY HH:mm',\n        },\n        calendar: {\n            sameDay: '[heute um] LT [Uhr]',\n            sameElse: 'L',\n            nextDay: '[morgen um] LT [Uhr]',\n            nextWeek: 'dddd [um] LT [Uhr]',\n            lastDay: '[gestern um] LT [Uhr]',\n            lastWeek: '[letzten] dddd [um] LT [Uhr]',\n        },\n        relativeTime: {\n            future: 'in %s',\n            past: 'vor %s',\n            s: 'ein paar Sekunden',\n            ss: '%d Sekunden',\n            m: processRelativeTime,\n            mm: '%d Minuten',\n            h: processRelativeTime,\n            hh: '%d Stunden',\n            d: processRelativeTime,\n            dd: processRelativeTime,\n            w: processRelativeTime,\n            ww: '%d Wochen',\n            M: processRelativeTime,\n            MM: processRelativeTime,\n            y: processRelativeTime,\n            yy: processRelativeTime,\n        },\n        dayOfMonthOrdinalParse: /\\d{1,2}\\./,\n        ordinal: '%d.',\n        week: {\n            dow: 1, // Monday is the first day of the week.\n            doy: 4, // The week that contains Jan 4th is the first week of the year.\n        },\n    });\n\n    return de;\n\n})));\n", "//Import Library\r\nimport React from \"react\";\r\nimport { Accordion, Col, Container, Row } from \"react-bootstrap\";\r\n\r\n//Import services/components\r\nimport OrganizationTable from \"../OrganizationTable\";\r\nimport { useTranslation } from 'next-i18next';\r\n\r\nconst CountryOrganisationAccordion = (props: any) => {\r\n    const { t } = useTranslation('common');\r\n    return (\r\n        <Accordion defaultActiveKey=\"0\">\r\n            <Accordion.Item eventKey=\"0\">\r\n                <Accordion.Header>\r\n                    <div className=\"cardTitle\">{t(\"Organisation\")}</div>\r\n                </Accordion.Header>\r\n                <Accordion.Body>\r\n                    <Container fluid>\r\n                        <Row>\r\n                            <Col>\r\n                                <OrganizationTable {...props.prop} />\r\n                            </Col>\r\n                        </Row>\r\n                    </Container>\r\n                </Accordion.Body>\r\n            </Accordion.Item>\r\n        </Accordion>\r\n    )\r\n}\r\n\r\nexport default CountryOrganisationAccordion;"], "names": ["props", "t", "useTranslation", "DiscussionAccordion", "Accordion", "defaultActiveKey", "<PERSON><PERSON>", "eventKey", "Header", "div", "className", "Body", "Discussion", "type", "id", "routes", "CanViewDiscussionUpdate", "canViewDiscussionUpdate", "CountryAccordionSection", "prop", "CountryOrganisationAccordion", "CountryMediaGalleryAccordion", "CountryDocumentAccordion", "ReactImages", "images", "setImages", "useState", "renderImageLegend", "isValidLink", "test", "item", "description", "p", "b", "originalName", "FontAwesomeIcon", "icon", "faLink", "size", "color", "a", "href", "target", "rel", "style", "wordBreak", "downloadLink", "<PERSON><PERSON>", "faDownload", "useEffect", "carouselImages", "gallery", "Array", "isArray", "map", "i", "imgSrc", "fileType", "name", "split", "pop", "process", "_id", "_link", "_name", "original_name", "_description", "imageSource", "length", "push", "src", "Carousel", "showThumbs", "showStatus", "showIndicators", "infiniteLoop", "useKeyboardArrows", "autoPlay", "stopOnHover", "swipeable", "dynamicHeight", "emulate<PERSON><PERSON><PERSON>", "renderThumbs", "index", "img", "alt", "width", "height", "objectFit", "maxHeight", "DocumentTable", "loading", "sortProps", "updateSort", "docs", "document", "docsDescription", "docSrc", "h6", "updateDocumentSort", "updateDocument", "connectedAuthWrapper", "authenticatedSelector", "state", "permissions", "update", "wrapperDisplayName", "RKITable", "paginationComponentOptions", "rowsPerPageText", "columns", "data", "totalRows", "resetPaginationToggle", "subheader", "subHeaderComponent", "handlePerRowsChange", "handlePageChange", "rowsPerPage", "defaultRowsPerPage", "selectableRows", "pagServer", "onSelectedRowsChange", "clearSelectedRows", "sortServer", "onSort", "persistTableHead", "sortFunction", "otherProps", "dataTableProps", "noDataComponent", "<PERSON><PERSON><PERSON><PERSON>", "dense", "paginationResetDefaultPage", "subHeader", "progressPending", "pagination", "paginationServer", "paginationPerPage", "paginationRowsPerPageOptions", "paginationTotalRows", "onChangeRowsPerPage", "onChangePage", "progressComponent", "CustomLoader", "sortIcon", "DataTable", "defaultProps", "handleSort", "column", "sortDirection", "columnSelector", "objSlect", "selector", "cell", "d", "extension", "slice", "join", "sortable", "updated_at", "moment", "format", "OrganizationTable", "tabledata", "setDataToTable", "setLoading", "setTotalRows", "perPage", "_countryId", "pageSort", "setPageSort", "i18n", "instParams", "sort", "limit", "page", "instiTable", "query", "languageCode", "currentLang", "language", "Link", "as", "title", "max<PERSON><PERSON><PERSON>", "user", "username", "instSortParams", "getInstData", "instParamsinit", "Object", "keys", "created_at", "response", "apiService", "get", "for<PERSON>ach", "element", "expertise", "e", "address", "region", "totalCount", "newPerPage", "Container", "fluid", "Row", "Col"], "sourceRoot": "", "ignoreList": [10]}