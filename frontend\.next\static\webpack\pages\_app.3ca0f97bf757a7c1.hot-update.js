"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/_app",{

/***/ "(pages-dir-browser)/./pages/_app.tsx":
/*!************************!*\
  !*** ./pages/_app.tsx ***!
  \************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(pages-dir-browser)/./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(pages-dir-browser)/./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react_redux__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! react-redux */ \"(pages-dir-browser)/./node_modules/react-redux/dist/react-redux.mjs\");\n/* harmony import */ var next_redux_wrapper__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next-redux-wrapper */ \"(pages-dir-browser)/./node_modules/next-redux-wrapper/es6/index.js\");\n/* harmony import */ var redux_persist_integration_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! redux-persist/integration/react */ \"(pages-dir-browser)/./node_modules/redux-persist/es/integration/react.js\");\n/* harmony import */ var redux_persist__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! redux-persist */ \"(pages-dir-browser)/./node_modules/redux-persist/es/index.js\");\n/* harmony import */ var react_hot_toast__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! react-hot-toast */ \"(pages-dir-browser)/./node_modules/react-hot-toast/dist/index.mjs\");\n/* harmony import */ var _barrel_optimize_names_Spinner_react_bootstrap__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=Spinner!=!react-bootstrap */ \"(pages-dir-browser)/__barrel_optimize__?names=Spinner!=!./node_modules/react-bootstrap/esm/index.js\");\n/* harmony import */ var next_app__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! next/app */ \"(pages-dir-browser)/./node_modules/next/app.js\");\n/* harmony import */ var next_app__WEBPACK_IMPORTED_MODULE_6___default = /*#__PURE__*/__webpack_require__.n(next_app__WEBPACK_IMPORTED_MODULE_6__);\n/* harmony import */ var next_i18next__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! next-i18next */ \"(pages-dir-browser)/./node_modules/next-i18next/dist/esm/index.js\");\n/* harmony import */ var _components_layout_authenticated_Layout__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ../components/layout/authenticated/Layout */ \"(pages-dir-browser)/./components/layout/authenticated/Layout.tsx\");\n/* harmony import */ var _components_layout_authenticated_Footer__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ../components/layout/authenticated/Footer */ \"(pages-dir-browser)/./components/layout/authenticated/Footer.tsx\");\n/* harmony import */ var _styles_global_scss__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ../styles/global.scss */ \"(pages-dir-browser)/./styles/global.scss\");\n/* harmony import */ var _styles_global_scss__WEBPACK_IMPORTED_MODULE_10___default = /*#__PURE__*/__webpack_require__.n(_styles_global_scss__WEBPACK_IMPORTED_MODULE_10__);\n/* harmony import */ var _components_hoc_AuthSync__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! ../components/hoc/AuthSync */ \"(pages-dir-browser)/./components/hoc/AuthSync.tsx\");\n/* harmony import */ var _store__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! ../store */ \"(pages-dir-browser)/./store.tsx\");\n/* harmony import */ var _routePermissions__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! ./routePermissions */ \"(pages-dir-browser)/./pages/routePermissions.tsx\");\n/* harmony import */ var _components_common_GoogleMapsProvider__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! ../components/common/GoogleMapsProvider */ \"(pages-dir-browser)/./components/common/GoogleMapsProvider.tsx\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! next/router */ \"(pages-dir-browser)/./node_modules/next/router.js\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_15___default = /*#__PURE__*/__webpack_require__.n(next_router__WEBPACK_IMPORTED_MODULE_15__);\n\nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n//Import services/components\n\n\n\n\n\n\n\n\n\n// Create the wrapper\nconst wrapper = (0,next_redux_wrapper__WEBPACK_IMPORTED_MODULE_2__.createWrapper)(_store__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n    debug: \"development\" === 'development'\n});\nif (true) {\n    module.hot.addStatusHandler((status)=>{\n        if ( true && status === 'ready') {\n            window['__webpack_reload_css__'] = true;\n        }\n    });\n}\nfunction MyApp(param) {\n    let { Component, pageProps, router, isPublicRoute, isLoading, ...rest } = param;\n    _s();\n    const CanAccessRoutes = (0,_routePermissions__WEBPACK_IMPORTED_MODULE_13__.canAccessRoutes)(()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Component, {\n            ...pageProps,\n            router: router\n        }, void 0, false, {\n            fileName: \"D:\\\\rki\\\\frontend\\\\pages\\\\_app.tsx\",\n            lineNumber: 43,\n            columnNumber: 49\n        }, this));\n    const { locale } = (0,next_router__WEBPACK_IMPORTED_MODULE_15__.useRouter)();\n    // Get store from wrapper\n    const store = wrapper.useWrappedStore(rest).store;\n    const persistor = (0,redux_persist__WEBPACK_IMPORTED_MODULE_4__.persistStore)(store);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_common_GoogleMapsProvider__WEBPACK_IMPORTED_MODULE_14__.GoogleMapsProvider, {\n        language: locale || 'en',\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_redux__WEBPACK_IMPORTED_MODULE_16__.Provider, {\n            store: store,\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(redux_persist_integration_react__WEBPACK_IMPORTED_MODULE_3__.PersistGate, {\n                loading: null,\n                persistor: persistor,\n                children: isLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Spinner_react_bootstrap__WEBPACK_IMPORTED_MODULE_17__.Spinner, {\n                    animation: \"border\",\n                    variant: \"primary\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\rki\\\\frontend\\\\pages\\\\_app.tsx\",\n                    lineNumber: 55,\n                    columnNumber: 13\n                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                    children: [\n                        public_route_func(),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_hot_toast__WEBPACK_IMPORTED_MODULE_5__.Toaster, {\n                            position: \"top-right\",\n                            reverseOrder: false\n                        }, void 0, false, {\n                            fileName: \"D:\\\\rki\\\\frontend\\\\pages\\\\_app.tsx\",\n                            lineNumber: 59,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true)\n            }, void 0, false, {\n                fileName: \"D:\\\\rki\\\\frontend\\\\pages\\\\_app.tsx\",\n                lineNumber: 53,\n                columnNumber: 7\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\rki\\\\frontend\\\\pages\\\\_app.tsx\",\n            lineNumber: 52,\n            columnNumber: 5\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\rki\\\\frontend\\\\pages\\\\_app.tsx\",\n        lineNumber: 51,\n        columnNumber: 5\n    }, this);\n    function public_route_func() {\n        return isPublicRoute ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Component, {\n            ...pageProps,\n            router: router\n        }, void 0, false, {\n            fileName: \"D:\\\\rki\\\\frontend\\\\pages\\\\_app.tsx\",\n            lineNumber: 69,\n            columnNumber: 29\n        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_layout_authenticated_Layout__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n            router: router,\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(CanAccessRoutes, {\n                    router: router,\n                    pageProps: pageProps\n                }, void 0, false, {\n                    fileName: \"D:\\\\rki\\\\frontend\\\\pages\\\\_app.tsx\",\n                    lineNumber: 71,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_layout_authenticated_Footer__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {}, void 0, false, {\n                    fileName: \"D:\\\\rki\\\\frontend\\\\pages\\\\_app.tsx\",\n                    lineNumber: 72,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\rki\\\\frontend\\\\pages\\\\_app.tsx\",\n            lineNumber: 70,\n            columnNumber: 7\n        }, this);\n    }\n}\n_s(MyApp, \"HlaErzM7676/oDmoHF+nyogq+mM=\", false, function() {\n    return [\n        next_router__WEBPACK_IMPORTED_MODULE_15__.useRouter,\n        wrapper.useWrappedStore\n    ];\n});\n_c = MyApp;\nMyApp.getInitialProps = async (appContext)=>{\n    const appProps = await next_app__WEBPACK_IMPORTED_MODULE_6___default().getInitialProps(appContext);\n    return {\n        ...appProps\n    };\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (_c3 = wrapper.withRedux(_c2 = (0,next_i18next__WEBPACK_IMPORTED_MODULE_7__.appWithTranslation)(_c1 = (0,_components_hoc_AuthSync__WEBPACK_IMPORTED_MODULE_11__[\"default\"])(MyApp))));\nvar _c, _c1, _c2, _c3;\n$RefreshReg$(_c, \"MyApp\");\n$RefreshReg$(_c1, \"%default%$wrapper.withRedux$appWithTranslation\");\n$RefreshReg$(_c2, \"%default%$wrapper.withRedux\");\n$RefreshReg$(_c3, \"%default%\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-browser)/./pages/_app.tsx\n"));

/***/ })

});