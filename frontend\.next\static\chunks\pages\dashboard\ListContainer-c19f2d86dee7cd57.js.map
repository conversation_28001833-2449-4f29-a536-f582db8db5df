{"version": 3, "file": "static/chunks/pages/dashboard/ListContainer-c19f2d86dee7cd57.js", "mappings": "qJAoEA,MA/CkD,OAAC,MACjDA,EAAO,QAAQ,CACfC,IA6CaC,CA7CR,EAAE,SA6CkBA,EA5CzBC,EAAY,EAAE,MACdC,CAAI,MACJC,CAAI,UACJC,CAAQ,SACRC,CAAO,OACPC,CAAK,CACLC,aAAY,CAAK,CAClB,UAsBC,GAAiB,iBAAOH,EAASI,GAAG,EAAyC,UAAU,OAA3BJ,EAASK,GAAG,CAKtE,UAACC,EAAAA,EAAMA,CAAAA,CACLN,SAAUA,EACVD,KAAMA,EACNG,MAAOA,GAASR,EAChBS,UAAWA,EACXF,QA/BgB,CA+BPM,GA9BPN,GAeFA,EAdoB,IADT,EAETP,KACAC,QAYmBa,IAXnBX,OACAC,WACAE,CACF,EAGe,CACbA,WACAS,YAAa,IAAMT,CACrB,EAE6BU,EAEjC,IAIS,IAYX,6ICrDA,IAAMC,EAAa,IACjB,GAAM,CAAEC,CAAC,CAAE,CAAGC,EACRC,EAAQF,EAAE,SAChB,MACE,UAACG,MAAAA,CAAIC,UAAU,uBACb,WAACC,KAAAA,WACC,WAACC,KAAAA,CAAGF,UAAU,iCACZ,UAACG,IAAAA,CAAEH,UAAU,kBAAkB,IAAEJ,EAAE,eAErC,WAACM,KAAAA,CAAGF,UAAU,gCACZ,UAACG,IAAAA,CAAEH,UAAU,kBAAkB,IAAEJ,EAAE,iBAErC,WAACM,KAAAA,CAAGF,UAAU,8BACZ,UAACG,IAAAA,CAAEH,UAAU,kBAAkB,IAAEF,EAAO,WAKlD,EA2MA,EAlMyB,IACvB,GAAM,MAAEM,CAAI,CAAE,CAAGC,CAAAA,EAAAA,EAAAA,EAAAA,CAAcA,CAAC,GAiMnBC,OAhMPC,EAAcH,EAAKI,KAgMIF,EAAC,CAhMG,CAC3B,GAAEV,CAAC,mBAAEa,CAAiB,iBAAEC,CAAe,CAAEC,eAAa,CAAE,CAAGd,EAC3D,CAACe,EAAeC,EAAiB,CAAGC,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAM,CACtDC,QAAQ,EACRC,UAAU,EACVC,YAAY,CACd,GACM,CAACC,EAASC,EAAW,CAAGL,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAQ,EAAE,EAC1C,CAACM,EAAcC,EAAgB,CAAQP,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAC,CAAC,GACjD,CAACQ,EAAYC,EAAc,CAAQT,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAC,CAAC,GAgD7CU,EAAc,KAClBH,EAAgB,MAChBE,EAAc,KAChB,EAEME,EAAgB,MAAO5B,EAAYL,EAAaE,KACpD8B,IACAH,EAAgB7B,GAChB+B,EAAc,CACZ7C,KAAMmB,EAAMnB,IAAI,CAChBC,GAAIkB,EAAMlB,EAAE,CACZG,KAAMe,EAAMf,IAAI,CAChBD,UAAWgB,EAAMhB,SAAS,EAE9B,EAEM6C,EAAkB,KAEtB,IAAMC,EAAkC,EAAE,CAC1CC,IAAAA,OAAS,CAACX,EAAY,IAChBY,EAAGC,OAAO,EAAE,EACWC,IAAI,CAAC,CAC5B7C,MAAO2C,EAAG3C,KAAK,CACfJ,KAAM,YACNH,GAAIkD,EAAGG,GAAG,CACVnD,UAAWgD,EAAGC,OAAO,EAAID,EAAGC,OAAO,CAACE,GAAG,CACvC5C,IAAKyC,EAAGC,OAAO,CAACG,WAAW,CAAC,EAAE,CAACC,QAAQ,CACvC7C,IAAKwC,EAAGC,OAAO,CAACG,WAAW,CAAC,EAAE,CAACE,SAAS,CACxCpD,KAAM,8BACR,EAEJ,GACA6B,EAAcK,UAAU,EAAG,EACdJ,EAAkBD,GAC/BO,EAAW,IAAID,KAAYS,CA+GZf,CA/GqC,CACtD,EAEMwB,EAAgB,KAEpB,IAAMC,EAAiC,EAAE,CACzCT,IAAAA,OAAS,CAACZ,EAAU,IACdsB,EAAIC,oBAAoB,EAAID,EAAIC,oBAAoB,CAACC,MAAM,CAAG,GAAG,IACnEZ,OAAS,CAACU,EAAIC,oBAAoB,CAAE,IAC9BT,EAAQW,eAAe,EAAE,EACHV,IAAI,CAAC,CAC3B7C,MAAOoD,EAAIpD,KAAK,CAChBJ,KAAM,UACNH,GAAI2D,EAAIN,GAAG,CACXnD,UACEyD,EAAIC,oBAAoB,CAACC,MAAM,CAAG,GAClCF,EAAIC,oBAAoB,CAAC,EAAE,CAACE,eAAe,EAC3CH,EAAIC,oBAAoB,CAAC,EAAE,CAACE,eAAe,CAACT,GAAG,CACjD5C,IAAK0C,EAAQW,eAAe,CAACR,WAAW,CAAC,EAAE,CAACC,QAAQ,CACpD7C,IAAKyC,EAAQW,eAAe,CAACR,WAAW,CAAC,EAAE,CAACE,SAAS,CACrDpD,KAAM,+BACR,EAEJ,EAEJ,GACA6B,EAAcI,QAAQ,EAAG,EACNH,EAAkBD,GACrCO,EAAW,IAAID,KAAYmB,CA+EZzB,CA/EoC,CACrD,EAEM8B,EAAc,KAClB,IAAMC,EAA6B,EAAE,CACrCf,IAAAA,OAAS,CAACjB,EAAe,IACnBiC,EAAMd,OAAO,EAAE,EACGC,IAAI,CAAC,CACvB7C,MAAO0D,EAAM1D,KAAK,CAClBJ,KAAM,QACNH,GAAIiE,EAAMZ,GAAG,CACbnD,UAAW+D,EAAMd,OAAO,EAAIc,EAAMd,OAAO,CAACE,GAAG,CAC7C5C,IAAKwD,EAAMd,OAAO,CAACG,WAAW,CAAC,EAAE,CAACC,QAAQ,CAC1C7C,IAAKuD,EAAMd,OAAO,CAACG,WAAW,CAAC,EAAE,CAACE,SAAS,CAC3CpD,KAAM,4BACR,EAEJ,GACA6B,EAAcG,MAAM,EAAG,EACXF,EAAkBD,GAC9BO,EAAW,IAAID,KAAYyB,CAuDZ/B,CAvDgC,CACjD,EAcA,MAZAiC,CAAAA,EAAAA,EAAAA,SAAAA,CAASA,CAAC,KACRT,GACF,EAAG,CAAC1B,EAAgB,EAEpBmC,CAAAA,EAAAA,EAAAA,SAAAA,CAASA,CAAC,KACRnB,GACF,EAAG,CAACjB,EAAkB,EAEtBoC,CAAAA,EAAAA,EAAAA,SAAAA,CAASA,CAAC,KACRH,GACF,EAAG,CAAC/B,EAAc,EAGhB,iCACE,UAACmC,EAAAA,CAAOA,CAAAA,CACNC,QAASvB,EACThB,SAAUD,EACVyC,OAAQ9B,EACRE,aAAcA,EACdE,WAAY,UAAC2B,IArJjB,GAAM,MAAEC,CAAI,CAAE,CAAGC,EACXC,EAqBN,SAASC,CAA2B,EAClC,aAAQC,EAAAA,KAAAA,EAAAA,EAAUxE,IAAI,EACpB,IADMwE,YAEJ,OAAO7C,EAAkB8C,MAAM,CAC7B,GAAOC,EAAE1B,OAAO,EAAI0B,EAAE1B,OAAO,CAACE,GAAG,EAAIsB,EAASzE,SAAS,CAE3D,KAAK,UACH,OAAO6B,EAAgB6C,MAAM,CAC3B,GACEC,EAAEjB,oBAAoB,EACtBiB,EAAEjB,oBAAoB,CAACC,MAAM,CAAG,GAChCgB,EAAEjB,oBAAoB,CAAC,EAAE,CAACE,eAAe,EACzCe,EAAEjB,oBAAoB,CAAC,EAAE,CAACE,eAAe,CAACT,GAAG,EAC3CsB,EAASzE,SAAS,CAE1B,KAAK,QACH,OAAO8B,EAAc4C,MAAM,CACzB,GAAOC,EAAE1B,OAAO,EAAI0B,EAAE1B,OAAO,CAACE,GAAG,EAAIsB,EAASzE,SAAS,CAE7D,CACF,EAzCyCqE,UACzC,GAEEO,OAAOC,IAAI,CAACR,GAAMV,MAAM,CAAG,GAC3BY,KAAsBO,KAGpB,MAFF,EAEE,EAAC1D,KAAAA,UACEmD,EAAmBQ,GAAG,CAAC,CAACC,EAAWC,IAEhC,UAAC5D,KAAAA,UACC,UAAC6D,IAAAA,CAAEC,KAAM,WAAIzD,EAAY,kBAAG2C,EAAAA,KAAAA,EAAAA,EAAMpE,IAAI,CAAC,CAAXoE,SAA4B,aAATA,EAAAA,KAAAA,EAAAA,EAAMvE,EAAE,IAARuE,aAAaW,EAAAA,KAAAA,EAAAA,EAAM3E,KAAK,CAAX2E,GADrDC,MAQV,IAwBX,EA0GmBb,CAAWC,KAAM5B,aAE7BV,EAAcI,QAAQ,EACvBJ,EAAcK,UAAU,EACxBL,EAAcG,MAAM,EACpBG,EAAQsB,MAAM,EAAI,EACdtB,EAAQ0C,GAAG,CAAC,CAACC,EAAWC,KACtB,GAAID,EAAKzE,GAAG,EAAIyE,EAAKxE,GAAG,CACtB,CADwB,KAEtB,UAACT,EAAAA,CAAYA,CAAAA,CAEXF,KAAMmF,EAAK3E,KAAK,CAChBP,GAAIkF,EAAKlF,EAAE,CACXE,UAAWgF,EAAKhF,SAAS,CACzBC,KAAM+E,EAAK/E,IAAI,CACfC,KAAM,CACJkF,IAAKJ,EAAK9E,IAAI,EAEhBE,QAASwC,EACTzC,SAAU6E,GATLC,EAab,GACA,OAEN,UAACnE,EAAAA,CAAWC,EAAGA,MAGrB,wFCxNA,MARyB,OAAC,UAAEZ,CAAQ,OAQrBkF,OARuBC,CAAY,QAQnBD,EARqBE,CAAQ,CAAS,GACnE,MACE,UAACC,EAAAA,EAAUA,CAAAA,CAACrF,SAAUA,EAAUmF,aAAcA,WAC5C,UAACpE,MAAAA,UAAKqE,KAGZ,ECdME,EAAO,mBACPC,EAAQ,qBACRC,EAAO,eACPC,EAAW,kBAgTjB,EA/SkB,CAChB,CACE,YAAe,IA6SJC,OA5SX,EA4SoBA,EAAC,IA5SV,CACT,CACE,MAAS,SACX,EACD,EAEH,CACE,YAAeJ,EACf,QAAW,CACT,CACE,MAAS,SACX,EACD,EAEH,CACE,YAAeC,EACf,QAAW,CACT,CACE,MAAS,SACX,EACD,EAEH,CACE,YAAe,iBACf,YAAe,WACf,QAAW,CACT,CACE,WAAc,KAChB,EACD,EAEH,CACE,YAAe,yBACf,YAAeE,EACf,QAAW,CACT,CACE,MAAS,SACX,EACD,EAEH,CACE,YAAe,6BACf,YAAe,SACf,QAAW,CACT,CACE,WAAc,KAChB,EAEJ,EACA,CACE,YAAe,6BACf,YAAeH,EACf,QAAW,CACT,CACE,MAAS,SACX,EACD,EAEH,CACE,YAAe,0BACf,YAAeG,EACf,QAAW,CACT,CACE,MAAS,SACX,EACD,EAEH,CACE,YAAe,qBACf,YAAeA,EACf,QAAW,CACT,CACE,MAAS,SACX,EACD,EAEH,CACE,YAAe,oBACf,YAAe,WACf,QAAW,CACT,CACE,MAAS,SACX,EACD,EAEH,CACE,YAAe,MACf,QAAW,CACT,CACE,WAAc,KAChB,EACD,EAEH,CACE,YAAe,MACf,YAAe,WACf,QAAW,CACT,CACE,MAAS,SACX,EACD,EAEH,CACE,YAAe,MACf,YAAe,cACf,QAAW,CACT,CACE,WAAc,KAChB,EACD,EAEH,CACE,YAAe,MACf,YAAeH,EACf,QAAW,CACT,CACE,MAAS,SACX,EACD,EAEH,CACE,YAAe,MACf,YAAeC,EACf,QAAW,CACT,CACE,MAAS,SACX,EACD,EAEH,CACE,YAAe,WACf,YAAe,gBACf,QAAW,CACT,CACE,MAAS,SACX,EACD,EAEH,CACE,YAAe,WACf,YAAeD,EACf,QAAW,CACT,CACE,MAAS,SACX,EACD,EAEH,CACE,YAAe,OACf,QAAW,CACT,CACE,WAAc,KAChB,EACD,EAEH,CACE,YAAe,OACf,YAAe,WACf,QAAW,CACT,CACE,MAAS,SACX,EACD,EAEH,CACE,YAAe,OACf,YAAe,cACf,QAAW,CACT,CACE,WAAc,KAChB,EACD,EAEH,CACE,YAAe,OACf,YAAeA,EACf,QAAW,CACT,CACE,MAAS,SACX,EACD,EAEH,CACE,YAAe,OACf,YAAeC,EACf,QAAW,CACT,CACE,MAAS,SACX,EACD,EAEH,CACE,YAAeC,EACf,YAAe,WACf,QAAW,CACT,CACE,MAAS,SACX,EACD,EAEH,CACE,YAAeA,EACf,YAAeC,EACf,QAAW,CACT,CACE,MAAS,SACX,EACD,EAEH,CACE,YAAeD,EACf,YAAeF,EACf,QAAW,CACT,CACE,MAAS,SACX,EAEJ,EACA,CACE,YAAeE,EACf,YAAeD,EACf,QAAW,CACT,CACE,MAAS,SACX,EACD,EAEH,CACE,YAAe,aACf,YAAe,SACf,QAAW,CACT,CACE,WAAc,KAChB,EACD,EAEH,CACE,YAAe,UACf,QAAW,CACT,CACE,WAAc,KAChB,EACD,EAEH,CACE,YAAe,UACf,YAAeD,EACf,QAAW,CACT,CACE,MAAS,SACX,EAEJ,EACA,CACE,YAAe,UACf,YAAeC,EACf,QAAW,CACT,CACE,MAAS,SACX,EACD,EAEH,CACE,YAAe,eACf,YAAe,gBACf,QAAW,CACT,CACE,MAAS,SACX,EACD,EAEH,CACE,YAAe,kBACf,YAAe,WACf,QAAW,CACT,CACE,MAAS,SACX,EAEJ,EACA,CACE,YAAe,QACf,YAAe,WACf,QAAW,CACT,CACE,MAAS,SACX,EACD,EAEH,CACE,YAAe,QACf,YAAeD,EACf,QAAW,CACT,CACE,MAAS,SACX,EACD,EAEJ,2BCnND,MAzEwC,OAAC,SAyE1BK,GAxEbrD,CAAU,CACVF,EAuEoBuD,EAAC,UAvET,CACZC,eAAa,UACbR,CAAQ,QACRS,EAAS,GAAG,OACZC,EAAQ,MAAM,UACdtE,CAAQ,MACRuE,EAAO,CAAC,SACRC,EAAU,CAAC,SACXjC,CAAO,CACR,GACO,QAAEkC,CAAM,CAAE,CAAGC,CAAAA,EAAAA,EAAAA,SAAAA,CAASA,GACtB,UAAEC,CAAQ,WAAEC,CAAS,CAAE,CAAIC,CAAAA,EAAAA,EAAAA,CAAAA,CAAaA,UAmB9C,EAAsB,SAAP,CAAQtF,MAAAA,UAAI,uBACtBoF,EAGH,QAHa,EAGZpF,MAAAA,CAAIC,UAAU,yBACb,UAACD,MAAAA,CAAIC,UAAU,WAAWsF,MAAO,OAAER,SAAOD,EAAQ7F,SAAU,UAAW,WACrE,WAACuG,EAAAA,EAASA,CAAAA,CACRC,kBAzBe,CACrBV,MAAOA,EACPD,OAA0B,UAAlB,OAAOA,EAAsB,GAAU,OAAPA,EAAO,MAAMA,CACvD,EAuBQY,OAhBOb,CAgBCa,EArBM,CACpBrG,IAAK,SAIyBsG,CAH9BrG,IAAK,SACP,EAmBQ0F,KAAMA,EACNY,OAhBU,CAgBFC,GAfdhC,EAAIiC,UAAU,CAAC,CACbC,OAAQpB,CACV,EACF,EAaQqB,QAAS,CACPf,EAhBWN,MAgBFM,EACT7F,WAAW,EACX6G,mBAAmB,EACnBC,kBAAmB,GACnBC,YAAY,EACZC,gBAAgB,EAChBC,gBAAgB,EAChBC,mBAAmB,CACrB,YAECjC,EACA9C,GAAcF,GAAgBA,EAAa3B,WAAW,EACrD,UAACyE,EAAgBA,CACflF,SAAUoC,EAAa3B,SADRyE,EACmB,GAClCC,aAAc,KAEZmC,QAAQC,GAAG,CAAC,qBACZxD,GAAAA,GACF,WAECzB,GAHCyB,QA5BQ,UAAChD,MAAAA,UAAI,mBAsC7B,mBC3FA,4CACA,2BACA,WACA,OAAe,EAAQ,KAAgD,CACvE,EACA,SAFsB", "sources": ["webpack://_N_E/./components/common/RKIMapMarker.tsx", "webpack://_N_E/./pages/dashboard/ListContainer.tsx", "webpack://_N_E/./components/common/RKIMapInfowindow.tsx", "webpack://_N_E/./components/common/mapStyles.tsx", "webpack://_N_E/./components/common/RKIMap1.tsx", "webpack://_N_E/?fe68"], "sourcesContent": ["import React from 'react';\r\nimport { Marker } from '@react-google-maps/api';\r\n\r\ninterface RKIMapMarkerProps {\r\n  name?: string;\r\n  id?: string;\r\n  countryId?: string;\r\n  type?: string;\r\n  icon?: {\r\n    url: string;\r\n    scaledSize?: google.maps.Size;\r\n  };\r\n  position: {\r\n    lat: number;\r\n    lng: number;\r\n  };\r\n  onClick?: (props: any, marker: any, e: any) => void;\r\n  title?: string;\r\n  draggable?: boolean;\r\n}\r\n\r\nconst RKIMapMarker: React.FC<RKIMapMarkerProps> = ({\r\n  name = 'Marker',\r\n  id = '',\r\n  countryId = '',\r\n  type,\r\n  icon,\r\n  position,\r\n  onClick,\r\n  title,\r\n  draggable = false,\r\n}) => {\r\n  const handleClick = (e: google.maps.MapMouseEvent) => {\r\n    if (onClick) {\r\n      const markerProps = {\r\n        name,\r\n        id,\r\n        countryId,\r\n        type,\r\n        position,\r\n      };\r\n\r\n      // Create a marker-like object for compatibility with old onClick signature\r\n      const marker = {\r\n        position,\r\n        getPosition: () => position,\r\n      };\r\n\r\n      onClick(markerProps, marker, e);\r\n    }\r\n  };\r\n\r\n  // Ensure position is valid\r\n  if (!position || typeof position.lat !== 'number' || typeof position.lng !== 'number') {\r\n    return null;\r\n  }\r\n\r\n  return (\r\n    <Marker\r\n      position={position}\r\n      icon={icon}\r\n      title={title || name}\r\n      draggable={draggable}\r\n      onClick={handleClick}\r\n    />\r\n  );\r\n};\r\n\r\nexport default RKIMapMarker;\r\n", "//Import Library\r\nimport React, { useState, useEffect } from \"react\";\r\nimport _ from \"lodash\";\r\n\r\n//Import services/components\r\nimport RKIMAP1 from \"../../components/common/RKIMap1\";\r\nimport RKIMapMarker from \"../../components/common/RKIMapMarker\";\r\nimport { useTranslation } from 'next-i18next';\r\n\r\ninterface MapLegendsProps {\r\n  t: (key: string) => string;\r\n}\r\n\r\nconst MapLegends = (props: MapLegendsProps) => {\r\n  const { t } = props;\r\n  const Event = t(\"Event\");\r\n  return (\r\n    <div className=\"map-legends\">\r\n      <ul>\r\n        <li className=\"marker-yellow-legend\">\r\n          <i className=\"fas fa-circle\" /> {t(\"Projects\")}\r\n        </li>\r\n        <li className=\"marker-green-legend\">\r\n          <i className=\"fas fa-circle\" /> {t(\"Operations\")}\r\n        </li>\r\n        <li className=\"marker-red-legend\">\r\n          <i className=\"fas fa-circle\" /> {Event}{\" \"}\r\n        </li>\r\n      </ul>\r\n    </div>\r\n  );\r\n};\r\n\r\ninterface ListMapContainerProps {\r\n  t: (key: string) => string;\r\n  ongoingOperations: any[];\r\n  ongoingProjects: any[];\r\n  currentEvents: any[];\r\n}\r\n\r\nconst ListMapContainer = (props: ListMapContainerProps) => {\r\n  const { i18n } = useTranslation('common');\r\n  const currentLang = i18n.language;\r\n  const { t, ongoingOperations, ongoingProjects, currentEvents } = props;\r\n  const [dataCollector, setDataCollector] = useState<any>({\r\n    events: false,\r\n    projects: false,\r\n    operations: false,\r\n  });\r\n  const [mapdata, setMapdata] = useState<any[]>([]);\r\n  const [activeMarker, setactiveMarker]: any = useState({});\r\n  const [markerInfo, setMarkerInfo]: any = useState({});\r\n\r\n  const MarkerInfo = (Markerprops: any) => {\r\n    const { info } = Markerprops;\r\n    const markersInformation = markerDetails(info);\r\n    if (\r\n      info &&\r\n      Object.keys(info).length > 0 &&\r\n      markersInformation != undefined\r\n    ) {\r\n      return (\r\n        <ul>\r\n          {markersInformation.map((item: any, index: number) => {\r\n            return (\r\n              <li key={index}>\r\n                <a href={`/${currentLang}/${info?.type}/show/${info?.id}`}>{item?.title}</a>\r\n              </li>\r\n            );\r\n          })}\r\n        </ul>\r\n      );\r\n    } else {\r\n      return null;\r\n    }\r\n\r\n    function markerDetails(infoinit: any) {\r\n      switch (infoinit?.type) {\r\n        case \"operation\":\r\n          return ongoingOperations.filter(\r\n            (x) => x.country && x.country._id == infoinit.countryId\r\n          );\r\n        case \"project\":\r\n          return ongoingProjects.filter(\r\n            (x) =>\r\n              x.partner_institutions &&\r\n              x.partner_institutions.length > 0 &&\r\n              x.partner_institutions[0].partner_country &&\r\n              x.partner_institutions[0].partner_country._id ==\r\n                infoinit.countryId\r\n          );\r\n        case \"event\":\r\n          return currentEvents.filter(\r\n            (x) => x.country && x.country._id == infoinit.countryId\r\n          );\r\n      }\r\n    }\r\n  };\r\n\r\n  const resetMarker = () => {\r\n    setactiveMarker(null);\r\n    setMarkerInfo(null);\r\n  };\r\n\r\n  const onMarkerClick = async (props: any, marker: any, e: any) => {\r\n    resetMarker();\r\n    setactiveMarker(marker);\r\n    setMarkerInfo({\r\n      name: props.name,\r\n      id: props.id,\r\n      type: props.type,\r\n      countryId: props.countryId,\r\n    });\r\n  };\r\n\r\n  const fetchOperations = () => {\r\n    const operations = ongoingOperations;\r\n    const dashboardOperationFilter: any[] = [];\r\n    _.forEach(operations, (op: any) => {\r\n      if (op.country) {\r\n        dashboardOperationFilter.push({\r\n          title: op.title,\r\n          type: \"operation\",\r\n          id: op._id,\r\n          countryId: op.country && op.country._id,\r\n          lat: op.country.coordinates[0].latitude,\r\n          lng: op.country.coordinates[0].longitude,\r\n          icon: \"/images/map-marker-green.svg\",\r\n        });\r\n      }\r\n    });\r\n    dataCollector.operations = true;\r\n    Data_setfunc(setDataCollector, dataCollector);\r\n    setMapdata([...mapdata, ...dashboardOperationFilter]);\r\n  };\r\n\r\n  const fetchProjects = () => {\r\n    const projects = ongoingProjects;\r\n    const dashboardProjectsFilter: any[] = [];\r\n    _.forEach(projects, (val: any) => {\r\n      if (val.partner_institutions && val.partner_institutions.length > 0) {\r\n        _.forEach(val.partner_institutions, (country: any) => {\r\n          if (country.partner_country) {\r\n            dashboardProjectsFilter.push({\r\n              title: val.title,\r\n              type: \"project\",\r\n              id: val._id,\r\n              countryId:\r\n                val.partner_institutions.length > 0 &&\r\n                val.partner_institutions[0].partner_country &&\r\n                val.partner_institutions[0].partner_country._id,\r\n              lat: country.partner_country.coordinates[0].latitude,\r\n              lng: country.partner_country.coordinates[0].longitude,\r\n              icon: \"/images/map-marker-yellow.svg\",\r\n            });\r\n          }\r\n        });\r\n      }\r\n    });\r\n    dataCollector.projects = true;\r\n    DataCollector_func(setDataCollector, dataCollector);\r\n    setMapdata([...mapdata, ...dashboardProjectsFilter]);\r\n  };\r\n\r\n  const fetchEvents = () => {\r\n    const dashbordEventFilter: any[] = [];\r\n    _.forEach(currentEvents, (event: any) => {\r\n      if (event.country) {\r\n        dashbordEventFilter.push({\r\n          title: event.title,\r\n          type: \"event\",\r\n          id: event._id,\r\n          countryId: event.country && event.country._id,\r\n          lat: event.country.coordinates[0].latitude,\r\n          lng: event.country.coordinates[0].longitude,\r\n          icon: \"/images/map-marker-red.svg\",\r\n        });\r\n      }\r\n    });\r\n    dataCollector.events = true;\r\n    data_select(setDataCollector, dataCollector);\r\n    setMapdata([...mapdata, ...dashbordEventFilter]);\r\n  };\r\n\r\n  useEffect(() => {\r\n    fetchProjects();\r\n  }, [ongoingProjects]);\r\n\r\n  useEffect(() => {\r\n    fetchOperations();\r\n  }, [ongoingOperations]);\r\n\r\n  useEffect(() => {\r\n    fetchEvents();\r\n  }, [currentEvents]);\r\n\r\n  return (\r\n    <>\r\n      <RKIMAP1\r\n        onClose={resetMarker}\r\n        language={currentLang}\r\n        points={mapdata}\r\n        activeMarker={activeMarker}\r\n        markerInfo={<MarkerInfo info={markerInfo} />}\r\n      >\r\n        {dataCollector.projects &&\r\n        dataCollector.operations &&\r\n        dataCollector.events &&\r\n        mapdata.length >= 1\r\n          ? mapdata.map((item: any, index: number) => {\r\n              if (item.lat && item.lng) {\r\n                return (\r\n                  <RKIMapMarker\r\n                    key={index}\r\n                    name={item.title}\r\n                    id={item.id}\r\n                    countryId={item.countryId}\r\n                    type={item.type}\r\n                    icon={{\r\n                      url: item.icon,\r\n                    }}\r\n                    onClick={onMarkerClick}\r\n                    position={item}\r\n                  />\r\n                );\r\n              }\r\n            })\r\n          : null}\r\n      </RKIMAP1>\r\n      <MapLegends t={t} />\r\n    </>\r\n  );\r\n};\r\n\r\nexport default ListMapContainer;\r\nfunction data_select(setDataCollector: any, dataCollector: any) {\r\n  setDataCollector(dataCollector);\r\n}\r\n\r\nfunction DataCollector_func(setDataCollector: any, dataCollector: any) {\r\n  setDataCollector(dataCollector);\r\n}\r\n\r\nfunction Data_setfunc(setDataCollector: any, dataCollector: any) {\r\n  setDataCollector(dataCollector);\r\n}\r\n", "import { InfoWindow } from '@react-google-maps/api';\r\n\r\ninterface Props {\r\n  position: google.maps.LatLngLiteral;\r\n  onCloseClick?: () => void;\r\n  children?: React.ReactNode;\r\n}\r\n\r\nconst RKIMapInfowindow = ({ position, onCloseClick, children }: Props) => {\r\n  return (\r\n    <InfoWindow position={position} onCloseClick={onCloseClick}>\r\n      <div>{children}</div>\r\n    </InfoWindow>\r\n  );\r\n};\r\n\r\nexport default RKIMapInfowindow;\r\n", "const fill = \"labels.text.fill\"\r\nconst stoke = \"labels.text.stroke\"\r\nconst road = \"road.highway\"\r\nconst geometry = \"geometry.stroke\"\r\nconst mapStyles = [\r\n  {\r\n    \"elementType\": \"geometry\",\r\n    \"stylers\": [\r\n      {\r\n        \"color\": \"#1d2c4d\"\r\n      }\r\n    ]\r\n  },\r\n  {\r\n    \"elementType\": fill,\r\n    \"stylers\": [\r\n      {\r\n        \"color\": \"#8ec3b9\"\r\n      }\r\n    ]\r\n  },\r\n  {\r\n    \"elementType\": stoke,\r\n    \"stylers\": [\r\n      {\r\n        \"color\": \"#1a3646\"\r\n      }\r\n    ]\r\n  },\r\n  {\r\n    \"featureType\": \"administrative\",\r\n    \"elementType\": \"geometry\",\r\n    \"stylers\": [\r\n      {\r\n        \"visibility\": \"off\"\r\n      }\r\n    ]\r\n  },\r\n  {\r\n    \"featureType\": \"administrative.country\",\r\n    \"elementType\": geometry,\r\n    \"stylers\": [\r\n      {\r\n        \"color\": \"#4b6878\"\r\n      }\r\n    ]\r\n  },\r\n  {\r\n    \"featureType\": \"administrative.land_parcel\",\r\n    \"elementType\": \"labels\",\r\n    \"stylers\": [\r\n      {\r\n        \"visibility\": \"off\"\r\n      }\r\n    ]\r\n  },\r\n  {\r\n    \"featureType\": \"administrative.land_parcel\",\r\n    \"elementType\": fill,\r\n    \"stylers\": [\r\n      {\r\n        \"color\": \"#64779e\"\r\n      }\r\n    ]\r\n  },\r\n  {\r\n    \"featureType\": \"administrative.province\",\r\n    \"elementType\": geometry,\r\n    \"stylers\": [\r\n      {\r\n        \"color\": \"#4b6878\"\r\n      }\r\n    ]\r\n  },\r\n  {\r\n    \"featureType\": \"landscape.man_made\",\r\n    \"elementType\": geometry,\r\n    \"stylers\": [\r\n      {\r\n        \"color\": \"#334e87\"\r\n      }\r\n    ]\r\n  },\r\n  {\r\n    \"featureType\": \"landscape.natural\",\r\n    \"elementType\": \"geometry\",\r\n    \"stylers\": [\r\n      {\r\n        \"color\": \"#023e58\"\r\n      }\r\n    ]\r\n  },\r\n  {\r\n    \"featureType\": \"poi\",\r\n    \"stylers\": [\r\n      {\r\n        \"visibility\": \"off\"\r\n      }\r\n    ]\r\n  },\r\n  {\r\n    \"featureType\": \"poi\",\r\n    \"elementType\": \"geometry\",\r\n    \"stylers\": [\r\n      {\r\n        \"color\": \"#283d6a\"\r\n      }\r\n    ]\r\n  },\r\n  {\r\n    \"featureType\": \"poi\",\r\n    \"elementType\": \"labels.text\",\r\n    \"stylers\": [\r\n      {\r\n        \"visibility\": \"off\"\r\n      }\r\n    ]\r\n  },\r\n  {\r\n    \"featureType\": \"poi\",\r\n    \"elementType\": fill,\r\n    \"stylers\": [\r\n      {\r\n        \"color\": \"#6f9ba5\"\r\n      }\r\n    ]\r\n  },\r\n  {\r\n    \"featureType\": \"poi\",\r\n    \"elementType\": stoke,\r\n    \"stylers\": [\r\n      {\r\n        \"color\": \"#1d2c4d\"\r\n      }\r\n    ]\r\n  },\r\n  {\r\n    \"featureType\": \"poi.park\",\r\n    \"elementType\": \"geometry.fill\",\r\n    \"stylers\": [\r\n      {\r\n        \"color\": \"#023e58\"\r\n      }\r\n    ]\r\n  },\r\n  {\r\n    \"featureType\": \"poi.park\",\r\n    \"elementType\": fill,\r\n    \"stylers\": [\r\n      {\r\n        \"color\": \"#3C7680\"\r\n      }\r\n    ]\r\n  },\r\n  {\r\n    \"featureType\": \"road\",\r\n    \"stylers\": [\r\n      {\r\n        \"visibility\": \"off\"\r\n      }\r\n    ]\r\n  },\r\n  {\r\n    \"featureType\": \"road\",\r\n    \"elementType\": \"geometry\",\r\n    \"stylers\": [\r\n      {\r\n        \"color\": \"#304a7d\"\r\n      }\r\n    ]\r\n  },\r\n  {\r\n    \"featureType\": \"road\",\r\n    \"elementType\": \"labels.icon\",\r\n    \"stylers\": [\r\n      {\r\n        \"visibility\": \"off\"\r\n      }\r\n    ]\r\n  },\r\n  {\r\n    \"featureType\": \"road\",\r\n    \"elementType\": fill,\r\n    \"stylers\": [\r\n      {\r\n        \"color\": \"#98a5be\"\r\n      }\r\n    ]\r\n  },\r\n  {\r\n    \"featureType\": \"road\",\r\n    \"elementType\": stoke,\r\n    \"stylers\": [\r\n      {\r\n        \"color\": \"#1d2c4d\"\r\n      }\r\n    ]\r\n  },\r\n  {\r\n    \"featureType\": road,\r\n    \"elementType\": \"geometry\",\r\n    \"stylers\": [\r\n      {\r\n        \"color\": \"#2c6675\"\r\n      }\r\n    ]\r\n  },\r\n  {\r\n    \"featureType\": road,\r\n    \"elementType\": geometry,\r\n    \"stylers\": [\r\n      {\r\n        \"color\": \"#255763\"\r\n      }\r\n    ]\r\n  },\r\n  {\r\n    \"featureType\": road,\r\n    \"elementType\": fill,\r\n    \"stylers\": [\r\n      {\r\n        \"color\": \"#b0d5ce\"\r\n      }\r\n    ]\r\n  },\r\n  {\r\n    \"featureType\": road,\r\n    \"elementType\": stoke,\r\n    \"stylers\": [\r\n      {\r\n        \"color\": \"#023e58\"\r\n      }\r\n    ]\r\n  },\r\n  {\r\n    \"featureType\": \"road.local\",\r\n    \"elementType\": \"labels\",\r\n    \"stylers\": [\r\n      {\r\n        \"visibility\": \"off\"\r\n      }\r\n    ]\r\n  },\r\n  {\r\n    \"featureType\": \"transit\",\r\n    \"stylers\": [\r\n      {\r\n        \"visibility\": \"off\"\r\n      }\r\n    ]\r\n  },\r\n  {\r\n    \"featureType\": \"transit\",\r\n    \"elementType\": fill,\r\n    \"stylers\": [\r\n      {\r\n        \"color\": \"#98a5be\"\r\n      }\r\n    ]\r\n  },\r\n  {\r\n    \"featureType\": \"transit\",\r\n    \"elementType\": stoke,\r\n    \"stylers\": [\r\n      {\r\n        \"color\": \"#1d2c4d\"\r\n      }\r\n    ]\r\n  },\r\n  {\r\n    \"featureType\": \"transit.line\",\r\n    \"elementType\": \"geometry.fill\",\r\n    \"stylers\": [\r\n      {\r\n        \"color\": \"#283d6a\"\r\n      }\r\n    ]\r\n  },\r\n  {\r\n    \"featureType\": \"transit.station\",\r\n    \"elementType\": \"geometry\",\r\n    \"stylers\": [\r\n      {\r\n        \"color\": \"#3a4762\"\r\n      }\r\n    ]\r\n  },\r\n  {\r\n    \"featureType\": \"water\",\r\n    \"elementType\": \"geometry\",\r\n    \"stylers\": [\r\n      {\r\n        \"color\": \"#0e1626\"\r\n      }\r\n    ]\r\n  },\r\n  {\r\n    \"featureType\": \"water\",\r\n    \"elementType\": fill,\r\n    \"stylers\": [\r\n      {\r\n        \"color\": \"#4e6d70\"\r\n      }\r\n    ]\r\n  }\r\n];\r\n\r\nexport default mapStyles;", "import React from 'react';\r\nimport { GoogleMap, useJsApiLoader } from '@react-google-maps/api';\r\nimport RKIMapInfowindow from './RKIMapInfowindow';\r\nimport mapStyles from './mapStyles';\r\nimport { useRouter } from \"next/router\";\r\nimport { useGoogleMaps } from './GoogleMapsProvider';\r\n\r\ninterface RKIMap1Props {\r\n  markerInfo?: React.ReactNode;\r\n  activeMarker?: any;\r\n  initialCenter?: { lat: number; lng: number };\r\n  children?: React.ReactNode;\r\n  height?: number | string;\r\n  width?: string;\r\n  language?: string;\r\n  points?: any[];\r\n  zoom?: number;\r\n  minZoom?: number;\r\n  onClose?: () => void\r\n}\r\n\r\nconst RKIMap1: React.FC<RKIMap1Props> = ({\r\n  markerInfo,\r\n  activeMarker,\r\n  initialCenter,\r\n  children,\r\n  height = 300,\r\n  width = \"114%\",\r\n  language,\r\n  zoom = 1,\r\n  minZoom = 1,\r\n  onClose\r\n}) => {\r\n  const { locale } = useRouter();\r\n  const { isLoaded, loadError } =  useGoogleMaps();\r\n  const containerStyle = {\r\n    width: width,\r\n    height: typeof height === 'number' ? `${height}px` : height,\r\n  };\r\n\r\n  const defaultCenter = {\r\n    lat: 52.520017,\r\n    lng: 13.404195,\r\n  };\r\n\r\n  const center = initialCenter || defaultCenter;\r\n\r\n  const onMapLoad = (map: google.maps.Map) => {\r\n    map.setOptions({\r\n      styles: mapStyles,\r\n    });\r\n  };\r\n\r\n  if (loadError) return <div>Error loading maps</div>;\r\n  if (!isLoaded) return <div>Loading Maps...</div>;\r\n\r\n  return (\r\n    <div className=\"map-container\">\r\n      <div className=\"mapprint\" style={{ width, height, position: 'relative' }}>\r\n        <GoogleMap\r\n          mapContainerStyle={containerStyle}\r\n          center={center}\r\n          zoom={zoom}\r\n          onLoad={onMapLoad}\r\n          options={{\r\n            minZoom: minZoom,\r\n            draggable: true,\r\n            keyboardShortcuts: false,\r\n            streetViewControl: false,\r\n            panControl: false,\r\n            clickableIcons: false,\r\n            mapTypeControl: false,\r\n            fullscreenControl: true,\r\n          }}\r\n        >\r\n          {children}\r\n          {markerInfo && activeMarker && activeMarker.getPosition && (\r\n            <RKIMapInfowindow\r\n              position={activeMarker.getPosition()}\r\n              onCloseClick={() => {\r\n                // Handle close if needed\r\n                console.log('close click');\r\n                onClose?.()\r\n              }}\r\n            >\r\n              {markerInfo}\r\n            </RKIMapInfowindow>\r\n          )}\r\n        </GoogleMap>\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default RKIMap1;\r\n", "\n    (window.__NEXT_P = window.__NEXT_P || []).push([\n      \"/dashboard/ListContainer\",\n      function () {\n        return require(\"private-next-pages/dashboard/ListContainer.tsx\");\n      }\n    ]);\n    if(module.hot) {\n      module.hot.dispose(function () {\n        window.__NEXT_P.push([\"/dashboard/ListContainer\"])\n      });\n    }\n  "], "names": ["name", "id", "R<PERSON>IMapMarker", "countryId", "type", "icon", "position", "onClick", "title", "draggable", "lat", "lng", "<PERSON><PERSON>", "handleClick", "marker", "getPosition", "e", "MapLegends", "t", "props", "Event", "div", "className", "ul", "li", "i", "i18n", "useTranslation", "ListMapContainer", "currentLang", "language", "ongoingOperations", "ongoingProjects", "currentEvents", "dataCollector", "setDataCollector", "useState", "events", "projects", "operations", "mapdata", "setMapdata", "activeMarker", "set<PERSON><PERSON><PERSON><PERSON>", "markerInfo", "setMarkerInfo", "reset<PERSON><PERSON><PERSON>", "onMarkerClick", "fetchOperations", "dashboardOperationFilter", "_", "op", "country", "push", "_id", "coordinates", "latitude", "longitude", "fetchProjects", "dashboardProjectsFilter", "val", "partner_institutions", "length", "partner_country", "fetchEvents", "dashbordEventFilter", "event", "useEffect", "RKIMAP1", "onClose", "points", "MarkerInfo", "info", "Markerprops", "markersInformation", "markerDetails", "infoinit", "filter", "x", "Object", "keys", "undefined", "map", "item", "index", "a", "href", "url", "RKIMapInfowindow", "onCloseClick", "children", "InfoWindow", "fill", "stoke", "road", "geometry", "mapStyles", "RKIMap1", "initialCenter", "height", "width", "zoom", "minZoom", "locale", "useRouter", "isLoaded", "loadError", "useGoogleMaps", "style", "GoogleMap", "mapContainerStyle", "center", "defaultCenter", "onLoad", "onMapLoad", "setOptions", "styles", "options", "keyboardShortcuts", "streetViewControl", "panControl", "clickableIcons", "mapTypeControl", "fullscreenControl", "console", "log"], "sourceRoot": "", "ignoreList": []}