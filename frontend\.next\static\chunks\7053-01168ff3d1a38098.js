"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[7053],{5377:(e,t,r)=>{r.d(t,{A:()=>i});var l=r(37876),a=r(14232),o=r(66619),s=r(15641),n=r(31753);let i=e=>{let{i18n:t}=(0,n.Bd)("common"),r=t.language,{mapdata:i}=e,[c,d]=(0,a.useState)({}),[p,y]=(0,a.useState)({}),[m,u]=(0,a.useState)({}),f=()=>{y(null),u(null)},T=()=>{d({title:i.title,id:i._id,lat:i.country&&i.country.coordinates?parseFloat(i.country.coordinates[0].latitude):null,lng:i.country&&i.country.coordinates?parseFloat(i.country.coordinates[0].longitude):null})};return(0,a.useEffect)(()=>{T()},[i]),(0,l.jsxs)(l.<PERSON>,{children:[" ",c&&c.id?(0,l.jsx)(o.A,{onClose:f,language:r,initialCenter:{lat:c.lat,lng:c.lng},activeMarker:p,markerInfo:(0,l.jsx)(e=>{let{info:t}=e;return(0,l.jsx)("a",{children:null==t?void 0:t.name})},{info:m}),children:(0,l.jsx)(s.A,{name:c.title,icon:{url:"/images/map-marker-white.svg"},onClick:(e,t,r)=>{f(),y(t),u({name:e.name})},position:c})}):null]})}},10248:(e,t,r)=>{r.r(t),r.d(t,{default:()=>c});var l=r(37876);r(14232);var a=r(11041),o=r(21772),s=r(56970),n=r(37784),i=r(31753);let c=e=>{let{t}=(0,i.Bd)("common");return(0,l.jsx)(l.Fragment,{children:(0,l.jsx)("div",{className:"operationStats",children:(0,l.jsxs)(s.A,{children:[(0,l.jsxs)(n.A,{className:"operationInfo-Items",md:6,children:[(0,l.jsx)("div",{className:"operationIcon",children:(0,l.jsx)(o.g,{icon:a.gdJ,color:"#fff",size:"2x"})}),(0,l.jsxs)("div",{className:"operationInfo",children:[(0,l.jsx)("h5",{children:t("Partners")}),(0,l.jsx)("h4",{children:e.operation.partners.length})]})]}),(0,l.jsxs)(n.A,{className:"operationInfo-Items",md:6,children:[(0,l.jsx)("div",{className:"operationIcon",children:(0,l.jsx)(o.g,{icon:a.z$e,color:"#fff",size:"2x"})}),(0,l.jsxs)("div",{className:"operationInfo",children:[(0,l.jsx)("h5",{children:t("ActivitiesinField")}),(0,l.jsx)("h4",{children:e.operation.timeline.length})]})]})]})})})}},15641:(e,t,r)=>{r.d(t,{A:()=>o});var l=r(37876);r(14232);var a=r(62945);let o=e=>{let{name:t="Marker",id:r="",countryId:o="",type:s,icon:n,position:i,onClick:c,title:d,draggable:p=!1}=e;return i&&"number"==typeof i.lat&&"number"==typeof i.lng?(0,l.jsx)(a.pH,{position:i,icon:n,title:d||t,draggable:p,onClick:e=>{c&&c({name:t,id:r,countryId:o,type:s,position:i},{position:i,getPosition:()=>i},e)}}):null}},31647:(e,t,r)=>{r.d(t,{A:()=>d});var l=r(37876),a=r(31777),o=r(11041),s=r(14232),n=r(21772),i=r(53718);let c={operation:"Operation",institution:"Institution",event:"Event",project:"Project",vspace:"Vspace"},d=(0,a.Ng)(e=>e)(e=>{let{user:t,entityId:r,entityType:a}=e,[d,p]=(0,s.useState)(!1),[y,m]=(0,s.useState)(""),u=async()=>{if(!(null==t?void 0:t._id))return;let e=await i.A.get("/flag",{query:{entity_id:r,user:t._id,onModel:c[a]}});e&&e.data&&e.data.length>0&&(m(e.data[0]),p(!0))},f=async e=>{if(e.preventDefault(),!(null==t?void 0:t._id))return;let l=!d,o={entity_type:a,entity_id:r,user:t._id,onModel:c[a]};if(l){let e=await i.A.post("/flag",o);e&&e._id&&(m(e),p(l))}else{let e=await i.A.remove("/flag/".concat(y._id));e&&e.n&&p(l)}};return(0,s.useEffect)(()=>{u()},[]),(0,l.jsx)("div",{className:"subscribe-flag",children:(0,l.jsxs)("a",{href:"",onClick:f,children:[(0,l.jsx)("span",{className:"check",children:d?(0,l.jsx)(n.g,{className:"clickable checkIcon",icon:o.SGM,color:"#00CC00"}):(0,l.jsx)(n.g,{className:"clickable minusIcon",icon:o.OQW,color:"#fff"})}),(0,l.jsx)(n.g,{className:"bookmark",icon:o.G06,color:"#d4d4d4"})]})})})},50903:(e,t,r)=>{r.r(t),r.d(t,{default:()=>y});var l=r(37876);r(14232);var a=r(60282),o=r(21772),s=r(48230),n=r.n(s),i=r(11041),c=r(31647),d=r(22352),p=r(31753);let y=e=>{let{t}=(0,p.Bd)("common"),r=()=>(0,l.jsx)(l.Fragment,{children:e.editData?(0,l.jsx)(n(),{href:"/operation/[...routes]",as:"/operation/edit/".concat(e.routeData.routes[1]),children:(0,l.jsxs)(a.A,{variant:"secondary",size:"sm",children:[(0,l.jsx)(o.g,{icon:i.hpd}),"\xa0",t("Edit")]})}):""}),s=(0,d.canEditOperation)(()=>(0,l.jsx)(r,{}));return(0,l.jsx)(l.Fragment,{children:(0,l.jsxs)("section",{className:"d-flex justify-content-between",children:[(0,l.jsxs)("h4",{className:"operationTitle",children:[e.operation.title,"\xa0\xa0",e.routeData.routes&&e.routeData.routes[1]?(0,l.jsx)(s,{operation:e.operation}):null]}),(0,l.jsx)(c.A,{entityId:e.routeData.routes[1],entityType:"operation"})]})})}},66619:(e,t,r)=>{r.d(t,{A:()=>m});var l=r(37876);r(14232);var a=r(62945);let o=e=>{let{position:t,onCloseClick:r,children:o}=e;return(0,l.jsx)(a.Fu,{position:t,onCloseClick:r,children:(0,l.jsx)("div",{children:o})})},s="labels.text.fill",n="labels.text.stroke",i="road.highway",c="geometry.stroke",d=[{elementType:"geometry",stylers:[{color:"#1d2c4d"}]},{elementType:s,stylers:[{color:"#8ec3b9"}]},{elementType:n,stylers:[{color:"#1a3646"}]},{featureType:"administrative",elementType:"geometry",stylers:[{visibility:"off"}]},{featureType:"administrative.country",elementType:c,stylers:[{color:"#4b6878"}]},{featureType:"administrative.land_parcel",elementType:"labels",stylers:[{visibility:"off"}]},{featureType:"administrative.land_parcel",elementType:s,stylers:[{color:"#64779e"}]},{featureType:"administrative.province",elementType:c,stylers:[{color:"#4b6878"}]},{featureType:"landscape.man_made",elementType:c,stylers:[{color:"#334e87"}]},{featureType:"landscape.natural",elementType:"geometry",stylers:[{color:"#023e58"}]},{featureType:"poi",stylers:[{visibility:"off"}]},{featureType:"poi",elementType:"geometry",stylers:[{color:"#283d6a"}]},{featureType:"poi",elementType:"labels.text",stylers:[{visibility:"off"}]},{featureType:"poi",elementType:s,stylers:[{color:"#6f9ba5"}]},{featureType:"poi",elementType:n,stylers:[{color:"#1d2c4d"}]},{featureType:"poi.park",elementType:"geometry.fill",stylers:[{color:"#023e58"}]},{featureType:"poi.park",elementType:s,stylers:[{color:"#3C7680"}]},{featureType:"road",stylers:[{visibility:"off"}]},{featureType:"road",elementType:"geometry",stylers:[{color:"#304a7d"}]},{featureType:"road",elementType:"labels.icon",stylers:[{visibility:"off"}]},{featureType:"road",elementType:s,stylers:[{color:"#98a5be"}]},{featureType:"road",elementType:n,stylers:[{color:"#1d2c4d"}]},{featureType:i,elementType:"geometry",stylers:[{color:"#2c6675"}]},{featureType:i,elementType:c,stylers:[{color:"#255763"}]},{featureType:i,elementType:s,stylers:[{color:"#b0d5ce"}]},{featureType:i,elementType:n,stylers:[{color:"#023e58"}]},{featureType:"road.local",elementType:"labels",stylers:[{visibility:"off"}]},{featureType:"transit",stylers:[{visibility:"off"}]},{featureType:"transit",elementType:s,stylers:[{color:"#98a5be"}]},{featureType:"transit",elementType:n,stylers:[{color:"#1d2c4d"}]},{featureType:"transit.line",elementType:"geometry.fill",stylers:[{color:"#283d6a"}]},{featureType:"transit.station",elementType:"geometry",stylers:[{color:"#3a4762"}]},{featureType:"water",elementType:"geometry",stylers:[{color:"#0e1626"}]},{featureType:"water",elementType:s,stylers:[{color:"#4e6d70"}]}];var p=r(89099),y=r(55316);let m=e=>{let{markerInfo:t,activeMarker:r,initialCenter:s,children:n,height:i=300,width:c="114%",language:m,zoom:u=1,minZoom:f=1,onClose:T}=e,{locale:h}=(0,p.useRouter)(),{isLoaded:x,loadError:g}=(0,y._)();return g?(0,l.jsx)("div",{children:"Error loading maps"}):x?(0,l.jsx)("div",{className:"map-container",children:(0,l.jsx)("div",{className:"mapprint",style:{width:c,height:i,position:"relative"},children:(0,l.jsxs)(a.u6,{mapContainerStyle:{width:c,height:"number"==typeof i?"".concat(i,"px"):i},center:s||{lat:52.520017,lng:13.404195},zoom:u,onLoad:e=>{e.setOptions({styles:d})},options:{minZoom:f,draggable:!0,keyboardShortcuts:!1,streetViewControl:!1,panControl:!1,clickableIcons:!1,mapTypeControl:!1,fullscreenControl:!0},children:[n,t&&r&&r.getPosition&&(0,l.jsx)(o,{position:r.getPosition(),onCloseClick:()=>{console.log("close click"),null==T||T()},children:t})]})})}):(0,l.jsx)("div",{children:"Loading Maps..."})}},77053:(e,t,r)=>{r.r(t),r.d(t,{default:()=>d});var l=r(37876);r(14232);var a=r(56970),o=r(37784),s=r(50903),n=r(72800),i=r(5377),c=r(10248);let d=e=>(0,l.jsx)(l.Fragment,{children:(0,l.jsxs)(a.A,{children:[(0,l.jsxs)(o.A,{className:"ps-md-0",md:7,children:[(0,l.jsx)(s.default,{operation:e.operationData,routeData:e.routeData,editData:e.editAccess}),(0,l.jsx)(n.A,{description:e.operationData.description}),(0,l.jsx)(c.default,{operation:e.operationData})]}),(0,l.jsx)(o.A,{className:"pe-md-0",md:5,children:(0,l.jsx)(i.A,{mapdata:e.operationData})})]})})}}]);
//# sourceMappingURL=7053-01168ff3d1a38098.js.map