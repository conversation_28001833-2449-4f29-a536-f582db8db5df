{"version": 3, "file": "static/chunks/pages/adminsettings/country/countryTable-fccc924341ab9468.js", "mappings": "0QAuLA,MA1KsBA,IAClB,GAAM,CAAEC,CAAC,MAAEC,CAAI,CAAE,CAAGC,CAAAA,EAAAA,EAAAA,EAAAA,CAAcA,CAAC,CAyKxBC,SAxKLC,EAAcH,CAwKGE,EAAC,MAxKCE,QAAQ,CAAY,CAAEC,SAAU,KAAM,EAAI,CAAEC,MAAO,KAAM,EAC5EC,EAAcP,EAAKI,QAAQ,CAC3B,CAACI,EAAWC,EAAe,CAAGC,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAC,EAAE,EACzC,EAAGC,EAAW,CAAGD,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,EAAC,GAC1B,CAACE,EAAWC,EAAa,CAAGH,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAC,GACrC,CAACI,EAASC,EAAW,CAAGL,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAC,IACjC,CAACM,EAAaC,EAAS,CAAGP,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,EAAC,GACnC,CAACQ,EAAsBC,EAAiB,CAAGT,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAC,CAAC,GACrD,CAACU,EAAYC,EAAc,CAAGX,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAC,IACvC,CAACY,EAAuBC,EAAyB,CAAGb,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,EAAC,GAG7Dc,EAAgB,CAClBC,KAAMtB,EACNuB,MAAOZ,EACPa,KAAM,EACNC,MAAO,CAAC,EACRC,aAActB,GAA4B,IAC9C,EAEMuB,EAAU,CACZ,CACIC,CALwBxB,IAKlBR,EAAE,wCACRiC,SAAU,GAAcC,EAAI3B,KAAK,CACjC4B,UAAU,CACd,EACA,CACIH,KAAMhC,EAAE,qCACRiC,SAAU,GAAcC,EAAIE,IAAI,CAChCD,SAAU,EACd,EACA,CACIH,KAAMhC,EAAE,yCACRiC,SAAU,GAAcC,EAAIG,SAAS,CACrCF,UAAU,CACd,EACA,CACIH,KAAMhC,EAAE,4CACRiC,SAAWC,QAAaA,QAAAA,CAAAA,OAAAA,EAAAA,EAAII,YAAAA,EAAJJ,KAAAA,EAAAA,EAAkB3B,GAAlB2B,EAAkB3B,GAAS,IACnD4B,UAAU,CACd,EACA,CACIH,KAAMhC,EAAE,uCACRiC,SAAU,GAAcC,EAAIK,GAAG,CAC/BJ,UAAU,EACVK,KAAM,GACF,WAACC,MAAAA,WACG,UAACC,IAAIA,CAACC,KAAK,6BAA6BC,GAAI,OAAvCF,wBAA4E,OAANG,EAAEN,GAAG,WAE5E,UAACO,IAAAA,CAAEC,UAAU,uBAEV,OAEP,UAACC,IAAAA,CAAEC,QAAS,IAAMC,EAAWL,YACzB,UAACC,IAAAA,CAAEC,UAAU,8BAI7B,EACH,CAEKI,EAAmB,MAAOC,IAC5BxC,GAAW,GACX,IAAMyC,EAAW,MAAMC,EAAAA,CAAUA,CAACC,GAAG,CAAC,WAAYH,GAC9CC,IACA3C,EAAe2C,EAASG,EADd,EACkB,EAC5B1C,EAAauC,EAASI,UAAU,EAChC7C,GAAW,GAEnB,EAQM8C,EAAsB,MAAOC,EAAiB/B,KAChDH,EAAcE,KAAK,CAAGgC,EACtBlC,EAAcG,IAAI,CAAGA,EACrBhB,GAAW,GACX,IAAMyC,EAAW,MAAMC,EAAAA,CAAUA,CAACC,GAAG,CAAC,WAAY9B,GAC9C4B,GAAYA,EAASG,IAAI,EAAIH,EAASG,IAAI,CAACI,MAAM,CAAG,GAAG,CACvDlD,EAAe2C,EAASG,IAAI,EAC5BxC,EAAW2C,GACX/C,GAAW,GAEnB,EAEMsC,EAAa,MAAOhB,IACtBd,EAAiBc,EAAIK,GAAG,EACxBrB,GAAS,EACb,EAEM2C,EAAe,UACjB,GAAI,CACA,MAAMP,EAAAA,CAAUA,CAACQ,MAAM,CAAC,YAAiC,OAArB3C,IACpCgC,EAAiB1B,GACjBP,GAAS,GACT6C,EAAAA,EAAKA,CAACC,OAAO,CAAChE,EAAE,2DACpB,CAAE,MAAOiE,EAAO,CACZF,EAAAA,EAAKA,CAACE,KAAK,CAACjE,EAAE,qDAClB,CACJ,EAEMkE,EAAY,IAAMhD,EAAS,IAE3BiD,EAAyBC,CAAAA,EAAAA,EAAAA,OAAAA,CAAOA,CAAC,KAQnC,IAAMC,EAAY,IACVC,GAAG,CACH7C,EAAcI,KAAK,CAAG,CAAEtB,MAAO+D,EAAE,EAErCnB,EAAiB1B,EACrB,EAEM8C,EAAoBC,IAAAA,QAAU,CAAC,GAAeH,EAAUC,GAAIG,OAAOC,KAAgC,GAAK,KAO9G,MAAO,UAACC,EAAAA,OAAkBA,CAAAA,CAACC,SALN,CAKgBC,GAJjCvD,EAAcwD,EAAEC,MAAM,CAACC,KAAK,EAC5BT,EAAkBO,EAAEC,MAAM,CAACC,KAAK,CACpC,EAEmDC,QArB/B,CAqBwCC,IApBpD7D,IACAG,EAAyB,CAACD,GAC1BD,EAAc,IAEtB,EAgByED,WAAYA,GACzF,EAAG,CAACA,EAAW,EAMf,MAJA8D,CAAAA,EAAAA,EAAAA,SAAAA,CAASA,CAAC,KACNhC,EAAiB1B,EACrB,EAAG,EAAE,EAGD,WAACgB,MAAAA,WACG,WAAC2C,EAAAA,CAAKA,CAAAA,CAACC,KAAMpE,EAAaqE,OAAQpB,YAC9B,UAACkB,EAAAA,CAAKA,CAACG,MAAM,EAACC,WAAW,aACrB,UAACJ,EAAAA,CAAKA,CAACK,KAAK,WAAEzF,EAAE,kDAEpB,UAACoF,EAAAA,CAAKA,CAACM,IAAI,WAAE1F,EAAE,qEACf,WAACoF,EAAAA,CAAKA,CAACO,MAAM,YACT,UAACC,EAAAA,CAAMA,CAAAA,CAACC,QAAQ,YAAY5C,QAASiB,WAChClE,EAAE,yCAEP,UAAC4F,EAAAA,CAAMA,CAAAA,CAACC,QAAQ,UAAU5C,QAASY,WAC9B7D,EAAE,4CAKf,UAAC8F,EAAAA,CAAQA,CAAAA,CACL/D,QAASA,EACTyB,KAAM/C,EACNI,UAAWA,EACXkF,SAAS,IACTC,WAAW,EACXzE,sBAAuBA,EACvB0E,mBAAoB9B,EACpBT,oBAAqBA,EACrBwC,iBA3Fa,CA2FKA,GA1F1BzE,EAAcE,KAAK,CAAGZ,EACtBU,EAAcG,IAAI,CAAGA,EACrBuB,EAAiB1B,EACrB,MA2FJ,6GChJA,SAASqE,EAASK,CAAoB,EACpC,GAAM,CAAEnG,CAAC,CAAE,CAAGE,CAAAA,EAAAA,EAAAA,EAAAA,CAAcA,CAAC,UACvBkG,EAA6B,CACjCC,gBAAiBrG,EAAE,cACnB,EACI,SACJ+B,CAAO,MACPyB,CAAI,WACJ3C,CAAS,uBACTU,CAAqB,WACrBwE,CAAS,oBACTE,CAAkB,qBAClBvC,CAAmB,kBACnBwC,CAAgB,aAChBI,CAAW,oBACXC,CAAkB,gBAClBC,CAAc,CACdC,SAAO,CACPT,WAAS,sBACTU,CAAoB,mBACpBC,CAAiB,YACjBC,CAAU,QACVC,CAAM,kBACNC,CAAgB,cAChBC,CAAY,CAEZ,CADA,EACGC,EACJ,CAAGb,EAGEc,EAAiB,4BACrBb,EACAc,gBAAiBlH,EAAE,IAP0C,MAQ7DmH,UAAU,UACVpF,EACAyB,KAAMA,GAAQ,EAAE,CAChB4D,MAAO,GACPC,2BAA4B9F,EAC5B+F,UAAWvB,EACXwB,gBAAiBd,qBACjBR,EACAuB,YAAY,EACZC,iBAAkBzB,EAClB0B,kBAAmBnB,GAA0C,GAC7DoB,eADwCpB,cACVD,GAA4B,CAAC,GAAI,GAAI,GAAI,CAA3BA,EAA+B,GAAG,CAC9EsB,oBAAqB/G,EACrBgH,oBAAqBnE,EACrBoE,aAAc5B,iBACdM,uBACAE,oBACAC,EACAoB,kBAAmB,UAACC,EAAAA,CAAYA,CAAAA,CAAAA,GAChCC,SAAU,UAACnF,IAAAA,CAAEC,UAAU,6CACvB6D,SACAC,eACAE,mBACAD,EACA/D,UAAW,WACb,EACA,MACE,UAACmF,EAAAA,EAASA,CAAAA,CAAE,GAAGjB,CAAc,EAEjC,CAEAnB,EAASqC,YAAY,CAAG,CACtBb,WAAW,EACXE,YAAY,EACZ3G,UAAW,KACXmF,WAAW,EACXU,qBAAsB,KACtBC,mBAAmB,EACnBC,WAAY,GACZE,kBAAkB,CACpB,EAEA,MAAehB,QAAQA,EAAC,SC/GxB,4CACA,sCACA,WACA,OAAe,EAAQ,KAA2D,CAClF,EACA,SAFsB,2HCsBtB,MApB2B,OAAC,YAAEzE,CAAU,QAoBzBsD,EApB2BC,CAAQ,SAACK,CAAO,CAAO,GACzD,CAmByBN,EAnBvB3E,CAAC,CAAE,CAAGE,CAAAA,EAAAA,EAAAA,EAAAA,CAAcA,CAAC,UAC7B,MACE,UAACkI,EAAAA,CAASA,CAAAA,CAACC,KAAK,IAACtF,UAAU,eACzB,UAACuF,EAAAA,CAAGA,CAAAA,UACF,UAACC,EAAAA,CAAGA,CAAAA,CAACC,GAAI,EAAGzF,UAAU,eACpB,UAAC0F,EAAAA,CAAWA,CAAAA,CACVC,KAAK,OACL3F,UAAU,cACV4F,YAAa3I,EAAE,uCACf4I,aAAW,SACX5D,MAAO3D,EACPwH,SAAUjE,SAMtB", "sources": ["webpack://_N_E/./pages/adminsettings/country/countryTable.tsx", "webpack://_N_E/./components/common/RKITable.tsx", "webpack://_N_E/?2622", "webpack://_N_E/./pages/adminsettings/country/countryTableFilter.tsx"], "sourcesContent": ["//Import Library\r\nimport { useState, useEffect, useMemo } from \"react\";\r\nimport Link from \"next/link\";\r\nimport { <PERSON><PERSON>, But<PERSON> } from \"react-bootstrap\";\r\nimport _ from \"lodash\";\r\nimport toast from 'react-hot-toast';\r\n\r\n//Import services/components\r\nimport RKITable from \"../../../components/common/RKITable\";\r\nimport apiService from \"../../../services/apiService\";\r\nimport CountryTableFilter from \"./countryTableFilter\";\r\nimport { useTranslation } from 'next-i18next';\r\n\r\nconst CountryTable = (_props: any) => {\r\n    const { t, i18n } = useTranslation('common');\r\n    const titleSearch = i18n.language === \"de\" ? { title_de: \"asc\" } : { title: \"asc\" };\r\n    const currentLang = i18n.language;\r\n    const [tabledata, setDataToTable] = useState([]);\r\n    const [, setLoading] = useState(false);\r\n    const [totalRows, setTotalRows] = useState(0);\r\n    const [perPage, setPerPage] = useState(10);\r\n    const [isModalShow, setModal] = useState(false);\r\n    const [selectCountryDetails, setSelectCountry] = useState({});\r\n    const [filterText, setFilterText] = useState(\"\");\r\n    const [resetPaginationToggle, setResetPaginationToggle] = useState(false);\r\n\r\n\r\n    const countryParams = {\r\n        sort: titleSearch,\r\n        limit: perPage,\r\n        page: 1,\r\n        query: {},\r\n        languageCode: currentLang ? currentLang : \"en\",\r\n    };\r\n\r\n    const columns = [\r\n        {\r\n            name: t(\"adminsetting.Countries.Table.Country\"),\r\n            selector: (row: any) => row.title,\r\n            sortable: true,\r\n        },\r\n        {\r\n            name: t(\"adminsetting.Countries.Table.Code\"),\r\n            selector: (row: any) => row.code,\r\n            sortable: true,\r\n        },\r\n        {\r\n            name: t(\"adminsetting.Countries.Table.DialCode\"),\r\n            selector: (row: any) => row.dial_code,\r\n            sortable: true,\r\n        },\r\n        {\r\n            name: t(\"adminsetting.Countries.Table.WorldRegion\"),\r\n            selector: (row: any) => row.world_region?.title || '',\r\n            sortable: true,\r\n        },\r\n        {\r\n            name: t(\"adminsetting.Countries.Table.Action\"),\r\n            selector: (row: any) => row._id,\r\n            sortable: false,\r\n            cell: (d: any) => (\r\n                <div>\r\n                    <Link href=\"/adminsettings/[...routes]\" as={`/adminsettings/edit_country/${d._id}`}>\r\n\r\n                        <i className=\"icon fas fa-edit\" />\r\n\r\n                    </Link>\r\n                    &nbsp;\r\n                    <a onClick={() => userAction(d)}>\r\n                        <i className=\"icon fas fa-trash-alt\" />\r\n                    </a>\r\n                </div>\r\n            ),\r\n        },\r\n    ];\r\n\r\n    const getCountriesData = async (countryParams_initial: any) => {\r\n        setLoading(true);\r\n        const response = await apiService.get(\"/country\", countryParams_initial);\r\n        if (response) {\r\n            setDataToTable(response.data);\r\n            setTotalRows(response.totalCount);\r\n            setLoading(false);\r\n        }\r\n    };\r\n\r\n    const handlePageChange = (page: any) => {\r\n        countryParams.limit = perPage;\r\n        countryParams.page = page;\r\n        getCountriesData(countryParams);\r\n    };\r\n\r\n    const handlePerRowsChange = async (newPerPage: any, page: any) => {\r\n        countryParams.limit = newPerPage;\r\n        countryParams.page = page;\r\n        setLoading(true);\r\n        const response = await apiService.get(\"/country\", countryParams);\r\n        if (response && response.data && response.data.length > 0) {\r\n            setDataToTable(response.data);\r\n            setPerPage(newPerPage);\r\n            setLoading(false);\r\n        }\r\n    };\r\n\r\n    const userAction = async (row: any) => {\r\n        setSelectCountry(row._id);\r\n        setModal(true);\r\n    };\r\n\r\n    const modalConfirm = async () => {\r\n        try {\r\n            await apiService.remove(`/country/${selectCountryDetails}`);\r\n            getCountriesData(countryParams);\r\n            setModal(false);\r\n            toast.success(t(\"adminsetting.Countries.Table.countryDeletedSuccessfully\"));\r\n        } catch (error) {\r\n            toast.error(t(\"adminsetting.Countries.Table.errorDeletingcountry\"));\r\n        }\r\n    };\r\n\r\n    const modalHide = () => setModal(false);\r\n\r\n    const subHeaderComponentMemo = useMemo(() => {\r\n        const handleClear = () => {\r\n            if (filterText) {\r\n                setResetPaginationToggle(!resetPaginationToggle);\r\n                setFilterText(\"\");\r\n            }\r\n        };\r\n\r\n        const sendQuery = (q: any) => {\r\n            if (q) {\r\n                countryParams.query = { title: q };\r\n            }\r\n            getCountriesData(countryParams);\r\n        };\r\n\r\n        const handleSearchTitle = _.debounce((q: string) => sendQuery(q), Number(process.env.SEARCH_DEBOUNCE_TIME) || 300);\r\n\r\n        const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {\r\n            setFilterText(e.target.value);\r\n            handleSearchTitle(e.target.value);\r\n        };\r\n\r\n        return <CountryTableFilter onFilter={handleChange} onClear={handleClear} filterText={filterText} />;\r\n    }, [filterText]);\r\n\r\n    useEffect(() => {\r\n        getCountriesData(countryParams);\r\n    }, []);\r\n\r\n    return (\r\n        <div>\r\n            <Modal show={isModalShow} onHide={modalHide}>\r\n                <Modal.Header closeButton>\r\n                    <Modal.Title>{t(\"adminsetting.Countries.Table.DeleteCountry\")}</Modal.Title>\r\n                </Modal.Header>\r\n                <Modal.Body>{t(\"adminsetting.Countries.Table.Areyousurewanttodeletethiscountry?\")}</Modal.Body>\r\n                <Modal.Footer>\r\n                    <Button variant=\"secondary\" onClick={modalHide}>\r\n                        {t(\"adminsetting.Countries.Table.Cancel\")}\r\n                    </Button>\r\n                    <Button variant=\"primary\" onClick={modalConfirm}>\r\n                        {t(\"adminsetting.Countries.Table.Yes\")}\r\n                    </Button>\r\n                </Modal.Footer>\r\n            </Modal>\r\n\r\n            <RKITable\r\n                columns={columns}\r\n                data={tabledata}\r\n                totalRows={totalRows}\r\n                subheader\r\n                pagServer={true}\r\n                resetPaginationToggle={resetPaginationToggle}\r\n                subHeaderComponent={subHeaderComponentMemo}\r\n                handlePerRowsChange={handlePerRowsChange}\r\n                handlePageChange={handlePageChange}\r\n            />\r\n        </div>\r\n    );\r\n};\r\n\r\nexport default CountryTable;\r\n", "//Import Library\r\nimport React from 'react'\r\nimport DataTable  from 'react-data-table-component';\r\n\r\n//Import services/components\r\nimport { useTranslation } from 'next-i18next';\r\nimport CustomLoader from './CustomLoader';\r\n\r\ninterface RKITableProps {\r\n  columns: any[];\r\n  data: any[];\r\n  totalRows?: number;\r\n  resetPaginationToggle?: boolean;\r\n  subheader?: boolean;\r\n  subHeaderComponent?: React.ReactNode;\r\n  handlePerRowsChange?: (newPerPage: number, page: number) => void;\r\n  handlePageChange?: (page: number) => void;\r\n  rowsPerPage?: number[];\r\n  defaultRowsPerPage?: number;\r\n  selectableRows?: boolean;\r\n  loading?: boolean;\r\n  pagServer?: boolean;\r\n  onSelectedRowsChange?: (selectedRows: any) => void;\r\n  clearSelectedRows?: boolean;\r\n  sortServer?: boolean;\r\n  onSort?: (column: any, sortDirection: string) => void;\r\n  persistTableHead?: boolean;\r\n  sortFunction?: (rows: any[], field: any, direction: 'asc' | 'desc') => any[];\r\n  noHeader?: boolean;\r\n  dense?: boolean;\r\n  pagination?: boolean;\r\n  paginationServer?: boolean;\r\n  paginationTotalRows?: number;\r\n  subHeaderAlign?: string;\r\n  [key: string]: any;\r\n}\r\n\r\nfunction RKITable(props: RKITableProps) {\r\n  const { t } = useTranslation('common');\r\n  const paginationComponentOptions = {\r\n    rowsPerPageText: t('Rowsperpage'),\r\n    };\r\n  const {\r\n    columns,\r\n    data,\r\n    totalRows,\r\n    resetPaginationToggle,\r\n    subheader,\r\n    subHeaderComponent,\r\n    handlePerRowsChange,\r\n    handlePageChange,\r\n    rowsPerPage,\r\n    defaultRowsPerPage,\r\n    selectableRows,\r\n    loading,\r\n    pagServer,\r\n    onSelectedRowsChange,\r\n    clearSelectedRows,\r\n    sortServer,\r\n    onSort,\r\n    persistTableHead,\r\n    sortFunction,\r\n    // Filter out any unknown props that might cause DOM warnings\r\n    ...otherProps\r\n  } = props;\r\n\r\n  // Only pass known DataTable props to avoid DOM warnings\r\n  const dataTableProps = {\r\n    paginationComponentOptions,\r\n    noDataComponent: t(\"NoData\"),\r\n    noHeader: true,\r\n    columns,\r\n    data: data || [],\r\n    dense: true,\r\n    paginationResetDefaultPage: resetPaginationToggle,\r\n    subHeader: subheader,\r\n    progressPending: loading,\r\n    subHeaderComponent,\r\n    pagination: true,\r\n    paginationServer: pagServer,\r\n    paginationPerPage: defaultRowsPerPage ? defaultRowsPerPage : 10,\r\n    paginationRowsPerPageOptions: rowsPerPage ? rowsPerPage : [10, 15, 20, 25, 30],\r\n    paginationTotalRows: totalRows,\r\n    onChangeRowsPerPage: handlePerRowsChange,\r\n    onChangePage: handlePageChange,\r\n    selectableRows,\r\n    onSelectedRowsChange,\r\n    clearSelectedRows,\r\n    progressComponent: <CustomLoader/>,\r\n    sortIcon: <i className=\"sort-icon fas fa-exchange-alt\" />,\r\n    sortServer,\r\n    onSort,\r\n    sortFunction,\r\n    persistTableHead,\r\n    className: \"rki-table\"\r\n  };\r\n  return (\r\n    <DataTable {...dataTableProps} />\r\n  )\r\n};\r\n\r\nRKITable.defaultProps = {\r\n  subHeader: false,\r\n  pagination: true,\r\n  totalRows: null,\r\n  pagServer: true,\r\n  onSelectedRowsChange: null,\r\n  clearSelectedRows: false,\r\n  sortServer: false,\r\n  persistTableHead: false\r\n}\r\n\r\nexport default RKITable;\r\n\r\n", "\n    (window.__NEXT_P = window.__NEXT_P || []).push([\n      \"/adminsettings/country/countryTable\",\n      function () {\n        return require(\"private-next-pages/adminsettings/country/countryTable.tsx\");\n      }\n    ]);\n    if(module.hot) {\n      module.hot.dispose(function () {\n        window.__NEXT_P.push([\"/adminsettings/country/countryTable\"])\n      });\n    }\n  ", "//Import Library\r\nimport {Col, Container, FormControl, Row} from \"react-bootstrap\";\r\n\r\n//Import services/components\r\nimport { useTranslation } from 'next-i18next';\r\n\r\nconst CountryTableFilter = ({ filterText, onFilter,onClear }: any) => {\r\n  const { t } = useTranslation('common');\r\n  return (\r\n    <Container fluid className=\"p-0\">\r\n      <Row>\r\n        <Col md={4} className=\"p-0\">\r\n          <FormControl\r\n            type=\"text\"\r\n            className=\"searchInput\"\r\n            placeholder={t(\"adminsetting.Countries.Forms.Search\")}\r\n            aria-label=\"Search\"\r\n            value={filterText}\r\n            onChange={onFilter}\r\n          />\r\n        </Col>\r\n      </Row>\r\n    </Container>\r\n  )\r\n};\r\n\r\nexport default CountryTableFilter;"], "names": ["_props", "t", "i18n", "useTranslation", "CountryTable", "titleSearch", "language", "title_de", "title", "currentLang", "tabledata", "setDataToTable", "useState", "setLoading", "totalRows", "setTotalRows", "perPage", "setPerPage", "isModalShow", "setModal", "selectCountryDetails", "setSelectCountry", "filterText", "setFilterText", "resetPaginationToggle", "setResetPaginationToggle", "countryParams", "sort", "limit", "page", "query", "languageCode", "columns", "name", "selector", "row", "sortable", "code", "dial_code", "world_region", "_id", "cell", "div", "Link", "href", "as", "d", "i", "className", "a", "onClick", "userAction", "getCountriesData", "countryParams_initial", "response", "apiService", "get", "data", "totalCount", "handlePerRowsChange", "newPerPage", "length", "modalConfirm", "remove", "toast", "success", "error", "modalHide", "subHeaderComponentMemo", "useMemo", "<PERSON><PERSON><PERSON><PERSON>", "q", "handleSearchTitle", "_", "Number", "process", "CountryTableFilter", "onFilter", "handleChange", "e", "target", "value", "onClear", "handleClear", "useEffect", "Modal", "show", "onHide", "Header", "closeButton", "Title", "Body", "Footer", "<PERSON><PERSON>", "variant", "RKITable", "subheader", "pagServer", "subHeaderComponent", "handlePageChange", "props", "paginationComponentOptions", "rowsPerPageText", "rowsPerPage", "defaultRowsPerPage", "selectableRows", "loading", "onSelectedRowsChange", "clearSelectedRows", "sortServer", "onSort", "persistTableHead", "sortFunction", "otherProps", "dataTableProps", "noDataComponent", "<PERSON><PERSON><PERSON><PERSON>", "dense", "paginationResetDefaultPage", "subHeader", "progressPending", "pagination", "paginationServer", "paginationPerPage", "paginationRowsPerPageOptions", "paginationTotalRows", "onChangeRowsPerPage", "onChangePage", "progressComponent", "CustomLoader", "sortIcon", "DataTable", "defaultProps", "Container", "fluid", "Row", "Col", "md", "FormControl", "type", "placeholder", "aria-label", "onChange"], "sourceRoot": "", "ignoreList": []}