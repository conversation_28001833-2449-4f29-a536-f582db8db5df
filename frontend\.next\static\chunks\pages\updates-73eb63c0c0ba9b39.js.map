{"version": 3, "file": "static/chunks/pages/updates-73eb63c0c0ba9b39.js", "mappings": "gFACA,4CACA,WACA,WACA,OAAe,EAAQ,KAAsC,CAC7D,EACA,SAFsB", "sources": ["webpack://_N_E/?5fae"], "sourcesContent": ["\n    (window.__NEXT_P = window.__NEXT_P || []).push([\n      \"/updates\",\n      function () {\n        return require(\"private-next-pages/updates/index.tsx\");\n      }\n    ]);\n    if(module.hot) {\n      module.hot.dispose(function () {\n        window.__NEXT_P.push([\"/updates\"])\n      });\n    }\n  "], "names": [], "sourceRoot": "", "ignoreList": []}