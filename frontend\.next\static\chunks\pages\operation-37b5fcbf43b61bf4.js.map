{"version": 3, "file": "static/chunks/pages/operation-37b5fcbf43b61bf4.js", "mappings": "0MA4HA,MAnG0BA,IACxB,GAAM,MAAEC,CAAI,CAAE,CAAGC,CAAAA,EAAAA,EAAAA,EAAAA,CAAcA,CAAC,GAkGnBC,OAjGPC,EAAcH,EAAKI,KAiGIF,EAAC,CAjGG,CAC3B,YAAEG,CAAU,CAAE,CAAGN,EACjB,CAACO,EAAQC,EAAU,CAAGC,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAQ,EAAE,EACxC,CAACC,EAAcC,EAAgB,CAAQF,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAC,CAAC,GACjD,CAACG,EAAYC,EAAc,CAAQJ,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAC,CAAC,GAC7C,CAACK,EAAmBC,EAAqB,CAAQN,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAC,CAAC,GAoB3DO,EAAc,KAClBL,EAAgB,MAChBE,EAAc,KAChB,EAEMI,EAAgB,CAACC,EAA4DC,EAAaC,KAC9FJ,IACAL,EAAgBQ,GAChBN,EAAc,CACZQ,KAAMH,EAAUG,IAAI,CACpBC,GAAIJ,EAAUI,EAAE,CAChBC,UAAWL,EAAUK,SAAS,EAElC,EAEMC,EAA4B,KAChC,IAAMC,EAA+B,EAAE,CACvCC,IAAAA,OAAS,CAACpB,EAAY,IACpBmB,EAAsBE,IAAI,CAAC,CACzBC,MAAOC,EAAUD,KAAK,CACtBN,GAAIO,EAAUC,GAAG,CACjBC,IACEF,EAAUG,OAAO,EACjBH,EAAUG,OAAO,CAACC,WAAW,EAC7BC,WAAWL,EAAUG,OAAO,CAACC,WAAW,CAAC,EAAE,CAACE,QAAQ,EACtDC,IACEP,EAAUG,OAAO,EACjBH,EAAUG,OAAO,CAACC,WAAW,EAC7BC,WAAWL,EAAUG,OAAO,CAACC,WAAW,CAAC,EAAE,CAACI,SAAS,EACvDd,UAAWM,EAAUG,OAAO,EAAIH,EAAUG,OAAO,CAACF,GAAG,EAEzD,GACAtB,EAAU,IAAIiB,EAAsB,CACtC,EAUA,MARAa,CAAAA,EAAAA,EAAAA,SAAAA,CAASA,CAAC,KACRd,IACIlB,GAAcA,EAAWiC,MAAM,CAAG,GAAG,EACLb,IAAAA,OAAS,CAACpB,EAAY,KACnCkC,UAEzB,EAAG,CAAClC,EAAW,EAGb,UAACmC,EAAAA,CAAOA,CAAAA,CACNC,QAAS1B,EACTT,OAAQA,EACRF,SAAUD,EACVM,aAAcA,EACdE,WAAY,UAnEG,IACjB,GAAM,MAAE+B,CAAI,CAAE,CAAGC,SACjB,GAAYD,EAAKpB,SAAS,EAAIT,CAAiB,CAAC6B,EAAKpB,SAAS,CAAC,CAE3D,CAF6D,EAE7D,OAACsB,KAAAA,UACE/B,CAAiB,CAAC6B,EAAKpB,SAAS,CAAC,CAACuB,GAAG,CAAC,CAACC,EAAWC,IAE/C,UAACC,KAAAA,UACC,UAACC,IAAAA,CAAEC,KAAM,GAAiCJ,MAAAA,CAA9B3C,EAAY,oBAA2B,OAAT2C,EAAKjB,GAAG,WAAKiB,EAAKnB,KAAK,IAD1DoB,MAQZ,IACT,EAmDiBI,CAAWT,KAAM/B,aAE7BL,EAAOgC,MAAM,EAAI,EACdhC,EAAOuC,GAAG,CAAC,CAACC,EAAMC,IAEd,UAACK,EAAAA,CAAYA,CAAAA,CAEXhC,KAAM0B,EAAKnB,KAAK,CAChBN,GAAIyB,EAAKzB,EAAE,CACXC,UAAWwB,EAAKxB,SAAS,CACzB+B,KAAM,CACJC,IAAK,8BACP,EACAC,QAASvC,EACTwC,SAAUV,GARLC,IAYX,MAGV,wFCtDA,MA/CkD,OAAC,MACjD3B,EAAO,QAAQ,IACfC,CA6Ca+B,CA7CR,EAAE,SA6CkBA,EA5CzB9B,EAAY,EAAE,MACdmC,CAAI,MACJJ,CAAI,UACJG,CAAQ,SACRD,CAAO,OACP5B,CAAK,WACL+B,GAAY,CAAK,CAClB,UAsBC,GAAyC,UAAxB,OAAOF,EAAS1B,GAAG,EAAyC,UAAxB,OAAO0B,EAASrB,GAAG,CAKtE,UAACwB,EAAAA,EAAMA,CAAAA,CACLH,SAAUA,EACVH,KAAMA,EACN1B,MAAOA,GAASP,EAChBsC,UAAWA,EACXH,QA/BgB,CA+BPK,GA9BPL,GAeFA,EAdoB,IADT,EAETnC,KACAC,QAYmBH,IAXnBI,OACAmC,WACAD,CACF,EAGe,UACbA,EACAK,YAAa,IAAML,CACrB,EAE6BrC,EAEjC,IAIS,IAYX,+OC3DO,IAAM2C,EAAkBC,CAAAA,EAAAA,EAAAA,CAAAA,CAAoBA,CAAC,CAClDC,sBAAuB,KACjBC,EAAMC,WAAW,IAAID,EAAMC,WAAW,CAACtC,SAAS,IAAIqC,EAAMC,WAAW,CAACtC,SAAS,CAAC,aAAa,CAKnGuC,CALqG,kBAKjF,iBACtB,GAAG,EAEgCJ,CAAAA,EAAAA,EAAAA,CAAAA,CAAoBA,CAAC,CACtDC,sBAAwBC,KAClBA,EAAMC,WAAW,IAAID,EAAMC,WAAW,CAACtC,SAAS,IAAIqC,EAAMC,WAAW,CAACtC,SAAS,CAAC,aAAa,CAKnGuC,CALqG,kBAKjF,sBACpBC,iBAAkB,IAAM,UAACC,EAAAA,OAAIA,CAAAA,CAAAA,EAC/B,GAAG,EAE6BN,CAAAA,EAAAA,EAAAA,CAAAA,CAAoBA,CAAC,CACnDC,sBAAuB,CAACC,EAAOlE,KAC7B,GAAIkE,EAAMC,WAAW,EAAID,EAAMC,WAAW,CAACtC,SAAS,EAAE,GAChDqC,EAAMC,WAAW,CAACtC,SAAS,CAAC,aAAa,CAC3C,CAD6C,MACtC,OAEP,GAAIqC,EAAMC,WAAW,CAACtC,SAAS,CAAC,aAAa,EAAE,EACnCA,SAAS,EAAI7B,EAAM6B,SAAS,CAAC0C,IAAI,EAAIvE,EAAM6B,SAAS,CAAC0C,IAAI,CAACzC,GAAG,GAAKoC,EAAMK,IAAI,CAACzC,GAAG,CACxF,CAD0F,MACnF,CAGb,CAEF,OAAO,CACT,EACAsC,mBAAoB,kBACtB,GAAG,EAEiCJ,CAAAA,EAAAA,EAAAA,CAAAA,CAAoBA,CAAC,CACvDC,sBAAuB,CAACC,EAAOlE,KAC7B,GAAIkE,EAAMC,WAAW,EAAID,EAAMC,WAAW,CAACtC,SAAS,EAAE,GAChDqC,EAAMC,WAAW,CAACtC,SAAS,CAAC,aAAa,CAC3C,CAD6C,MACtC,OAEP,GAAIqC,EAAMC,WAAW,CAACtC,SAAS,CAAC,aAAa,EAAE,EACnCA,SAAS,EAAI7B,EAAM6B,SAAS,CAAC0C,IAAI,EAAIvE,EAAM6B,SAAS,CAAC0C,IAAI,CAACzC,GAAG,GAAKoC,EAAMK,IAAI,CAACzC,GAAG,CACxF,CAD0F,MACnF,CAGb,CAEF,OAAO,CACT,EACAsC,mBAAoB,uBACpBC,iBAAkB,IAAM,UAACC,EAAAA,OAAIA,CAAAA,CAAAA,EAC/B,GAAG,EAEoCN,CAAAA,EAAAA,EAAAA,CAAAA,CAAoBA,CAAC,CAC1DC,sBAAuB,KACjBC,EAAMC,WAAW,IAAID,EAAMC,WAAW,CAACK,MAAM,IAAIN,EAAMC,WAAW,CAACK,MAAM,CAAC,WAAW,CAK3FJ,CAL6F,kBAKzE,yBACtB,GAEA,EAAeL,eAAeA,EAAC,oJCQ/B,MA1E8B,OAAC,YAC7BU,CAAU,QAyEGC,EAxEbC,CAAQ,kBAwE0BD,EAAC,EAvEnCE,CAAoB,SACpBC,CAAO,cACPC,CAAY,CAOb,GACO,CAACC,EAAQC,EAAU,CAAGvE,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAC,EAAE,EACjC,GAAEwE,CAAC,CAAE,CAAG/E,CAAAA,EAAAA,EAAAA,EAAAA,CAAcA,CAAC,UAEvBgF,EAAsB,MAAOC,IACjC,IAAMC,EAAW,MAAMC,EAAAA,CAAUA,CAACC,GAAG,CAAC,oBAAqBH,GACvDC,GAAYG,MAAMC,OAAO,CAACJ,EAASK,IAAI,GAAG,EAClCL,EAASK,IAAI,CAE3B,EASA,MAPAnD,CAAAA,EAAAA,EAAAA,SAAAA,CAASA,CAAC,KACR4C,EAAoB,CAClBQ,MAAO,CAAC,EACRC,KAAM,CAAE/D,MAAO,KAAM,CACvB,EACF,EAAG,EAAE,EAGH,UAACgE,EAAAA,CAASA,CAAAA,CAACC,KAAK,IAACC,UAAU,eACzB,WAACC,EAAAA,CAAGA,CAAAA,WACF,UAACC,EAAAA,CAAGA,CAAAA,CAACC,GAAI,EAAGH,UAAU,oCACpB,UAACI,EAAAA,CAAWA,CAAAA,CACVxC,KAAK,OACLoC,UAAU,cACVK,YAAalB,EAAE,UACfmB,aAAW,SACXC,MAAO5B,EACP6B,SAAU3B,MAGd,UAACqB,EAAAA,CAAGA,CAAAA,CAACC,GAAI,WACP,UAACM,EAAAA,CAAIA,CAAAA,UACH,WAACA,EAAAA,CAAIA,CAACC,KAAK,EAACC,GAAIV,EAAAA,CAAGA,CAAEW,UAAU,yBAC7B,UAACH,EAAAA,CAAIA,CAACI,KAAK,EAACC,MAAM,IAACC,GAAG,IAAIC,GAAG,aAC5B7B,EAAE,YAEH,UAACe,EAAAA,CAAGA,CAAAA,CAACF,UAAU,qBACb,WAACI,EAAAA,CAAWA,CAAAA,CACVO,GAAG,SACHL,aAAW,SACXE,SAAU1B,EACVyB,MAAOvB,YAEP,UAACiC,SAAAA,CAAOV,MAAO,YAAI,QAClBtB,EAAOjC,GAAG,CAAC,CAACC,EAAWC,IAEpB,UAAC+D,SAAAA,CAAmBV,MAAOtD,EAAKjB,GAAG,UAChCiB,EAAKnB,KAAK,EADAoB,oBAanC,6GC5CA,SAASgE,EAAShH,CAAoB,EACpC,GAAM,CAAEiF,CAAC,CAAE,CAAG/E,CAAAA,EAAAA,EAAAA,EAAAA,CAAcA,CAAC,UACvB+G,EAA6B,CACjCC,gBAAiBjC,EAAE,cACnB,EACI,SACJkC,CAAO,MACP1B,CAAI,WACJ2B,CAAS,uBACTC,CAAqB,WACrBC,CAAS,oBACTC,CAAkB,qBAClBC,CAAmB,kBACnBC,CAAgB,CAChBC,aAAW,oBACXC,CAAkB,gBAClBC,CAAc,SACdC,CAAO,WACPC,CAAS,sBACTC,CAAoB,mBACpBC,CAAiB,YACjBC,CAAU,QACVC,CAAM,kBACNC,CAAgB,cAChBC,CAAY,CAEZ,CADA,EACGC,EACJ,CAAGrI,EAGEsI,EAAiB,4BACrBrB,EACAsB,gBAAiBtD,EAAE,IAP0C,MAQ7DuD,UAAU,UACVrB,EACA1B,KAAMA,GAAQ,EAAE,CAChBgD,MAAO,GACPC,2BAA4BrB,EAC5BsB,UAAWrB,EACXsB,gBAAiBf,qBACjBN,EACAsB,WAAY,GACZC,iBAAkBhB,EAClBiB,kBAAmBpB,GAA0C,GAC7DqB,eADwCrB,cACVD,GAA4B,CAAC,GAAI,GAAI,GAAI,CAA3BA,EAA+B,GAAG,CAC9EuB,oBAAqB7B,EACrB8B,oBAAqB1B,EACrB2B,aAAc1B,iBACdG,uBACAG,oBACAC,EACAoB,kBAAmB,UAACC,EAAAA,CAAYA,CAAAA,CAAAA,GAChCC,SAAU,UAACC,IAAAA,CAAEzD,UAAU,6CACvBmC,SACAC,eACAE,mBACAD,EACArC,UAAW,WACb,EACA,MACE,UAAC0D,EAAAA,EAASA,CAAAA,CAAE,GAAGlB,CAAc,EAEjC,CAEAtB,EAASyC,YAAY,CAAG,CACtBd,WAAW,EACXE,YAAY,EACZzB,UAAW,KACXU,UAAW,GACXC,qBAAsB,KACtBC,mBAAmB,EACnBC,YAAY,EACZE,kBAAkB,CACpB,EAEA,MAAenB,QAAQA,EAAC,8EChGxB,MARyB,OAAC,CAAEvD,UAAQ,OAQrBiG,OARuBC,CAAY,QAQnBD,EAAC,CAR4B,CAAS,GACnE,MACE,UAACE,EAAAA,EAAUA,CAAAA,CAACnG,SAAUA,EAAUkG,aAAcA,WAC5C,UAACE,MAAAA,UAAKC,KAGZ,ECdMC,EAAO,mBACPC,EAAQ,qBACRC,EAAO,eACPC,EAAW,kBAgTjB,EA/SkB,CAChB,CACE,YAAe,IA6SJC,OA5SX,EA4SoBA,EAAC,IA5SV,CACT,CACE,MAAS,SACX,EACD,EAEH,CACE,YAAeJ,EACf,QAAW,CACT,CACE,MAAS,SACX,EACD,EAEH,CACE,YAAeC,EACf,QAAW,CACT,CACE,MAAS,SACX,EACD,EAEH,CACE,YAAe,iBACf,YAAe,WACf,QAAW,CACT,CACE,WAAc,KAChB,EACD,EAEH,CACE,YAAe,yBACf,YAAeE,EACf,QAAW,CACT,CACE,MAAS,SACX,EACD,EAEH,CACE,YAAe,6BACf,YAAe,SACf,QAAW,CACT,CACE,WAAc,KAChB,EACD,EAEH,CACE,YAAe,6BACf,YAAeH,EACf,QAAW,CACT,CACE,MAAS,SACX,EACD,EAEH,CACE,YAAe,0BACf,YAAeG,EACf,QAAW,CACT,CACE,MAAS,SACX,EACD,EAEH,CACE,YAAe,qBACf,YAAeA,EACf,QAAW,CACT,CACE,MAAS,SACX,EACD,EAEH,CACE,YAAe,oBACf,YAAe,WACf,QAAW,CACT,CACE,MAAS,SACX,EACD,EAEH,CACE,YAAe,MACf,QAAW,CACT,CACE,WAAc,KAChB,EACD,EAEH,CACE,YAAe,MACf,YAAe,WACf,QAAW,CACT,CACE,MAAS,SACX,EACD,EAEH,CACE,YAAe,MACf,YAAe,cACf,QAAW,CACT,CACE,WAAc,KAChB,EACD,EAEH,CACE,YAAe,MACf,YAAeH,EACf,QAAW,CACT,CACE,MAAS,SACX,EACD,EAEH,CACE,YAAe,MACf,YAAeC,EACf,QAAW,CACT,CACE,MAAS,SACX,EACD,EAEH,CACE,YAAe,WACf,YAAe,gBACf,QAAW,CACT,CACE,MAAS,SACX,EACD,EAEH,CACE,YAAe,WACf,YAAeD,EACf,QAAW,CACT,CACE,MAAS,SACX,EACD,EAEH,CACE,YAAe,OACf,QAAW,CACT,CACE,WAAc,KAChB,EACD,EAEH,CACE,YAAe,OACf,YAAe,WACf,QAAW,CACT,CACE,MAAS,SACX,EACD,EAEH,CACE,YAAe,OACf,YAAe,cACf,QAAW,CACT,CACE,WAAc,KAChB,EAEJ,EACA,CACE,YAAe,OACf,YAAeA,EACf,QAAW,CACT,CACE,MAAS,SACX,EACD,EAEH,CACE,YAAe,OACf,YAAeC,EACf,QAAW,CACT,CACE,MAAS,SACX,EAEJ,EACA,CACE,YAAeC,EACf,YAAe,WACf,QAAW,CACT,CACE,MAAS,SACX,EACD,EAEH,CACE,YAAeA,EACf,YAAeC,EACf,QAAW,CACT,CACE,MAAS,SACX,EACD,EAEH,CACE,YAAeD,EACf,YAAeF,EACf,QAAW,CACT,CACE,MAAS,SACX,EACD,EAEH,CACE,YAAeE,EACf,YAAeD,EACf,QAAW,CACT,CACE,MAAS,SACX,EACD,EAEH,CACE,YAAe,aACf,YAAe,SACf,QAAW,CACT,CACE,WAAc,KAChB,EACD,EAEH,CACE,YAAe,UACf,QAAW,CACT,CACE,WAAc,KAChB,EACD,EAEH,CACE,YAAe,UACf,YAAeD,EACf,QAAW,CACT,CACE,MAAS,SACX,EACD,EAEH,CACE,YAAe,UACf,YAAeC,EACf,QAAW,CACT,CACE,MAAS,SACX,EAEJ,EACA,CACE,YAAe,eACf,YAAe,gBACf,QAAW,CACT,CACE,MAAS,SACX,EACD,EAEH,CACE,YAAe,kBACf,YAAe,WACf,QAAW,CACT,CACE,MAAS,SACX,EACD,EAEH,CACE,YAAe,QACf,YAAe,WACf,QAAW,CACT,CACE,MAAS,SACX,EACD,EAEH,CACE,YAAe,QACf,YAAeD,EACf,QAAW,CACT,CACE,MAAS,SACX,EACD,EAEJ,2BCnND,MAzEwC,OAAC,SAyE1BK,GAxEbxJ,CAAU,GAwEUwJ,EAAC,SAvErB1J,CAAY,eACZ2J,CAAa,UACbP,CAAQ,QACRQ,EAAS,GAAG,OACZC,EAAQ,MAAM,UACdlK,CAAQ,MACRmK,EAAO,CAAC,SACRC,EAAU,CAAC,SACX/H,CAAO,CACR,GACO,QAAEgI,CAAM,CAAE,CAAGC,CAAAA,EAAAA,EAAAA,SAAAA,CAASA,GACtB,CAAEC,UAAQ,WAAEC,CAAS,CAAE,CAAIC,CAAAA,EAAAA,EAAAA,CAAAA,CAAaA,UAmB9C,EAAsB,SAAP,CAAQjB,MAAAA,UAAI,uBACtBe,EAGH,QAHa,EAGZf,MAAAA,CAAI/D,UAAU,yBACb,UAAC+D,MAAAA,CAAI/D,UAAU,WAAWiF,MAAO,CAAER,QAAOD,SAAQ7G,SAAU,UAAW,WACrE,WAACuH,EAAAA,EAASA,CAAAA,CACRC,kBAzBe,CACrBV,MAAOA,EACPD,OAA0B,UAAlB,OAAOA,EAAsB,GAAU,OAAPA,EAAO,MAAMA,CACvD,EAuBQY,OAhBOb,CAgBCa,EArBM,CACpBnJ,IAAK,SAIyBoJ,CAH9B/I,IAAK,SACP,EAmBQoI,KAAMA,EACNY,OAhBU,CAgBFC,GAfdvI,EAAIwI,UAAU,CAAC,CACbC,OAAQpB,CACV,EACF,EAaQqB,QAAS,CACPf,EAhBWN,MAgBFM,EACT9G,WAAW,EACX8H,mBAAmB,EACnBC,mBAAmB,EACnBC,YAAY,EACZC,gBAAgB,EAChBC,gBAAgB,EAChBC,mBAAmB,CACrB,YAEChC,EACAlJ,GAAcF,GAAgBA,EAAaoD,WAAW,EACrD,UAAC4F,EAAgBA,CACfjG,SAAU/C,EAAaoD,SADR4F,EACmB,GAClCC,aAAc,KAEZoC,QAAQC,GAAG,CAAC,qBACZtJ,GAAAA,GACF,WAEC9B,GAHC8B,QA5BQ,UAACmH,MAAAA,UAAI,mBAsC7B,2ICrEA,SAASoC,EAAuBjM,CAAkC,EAChE,GAAM,SAACkM,CAAO,CAAC,CAAGlM,EACZ,CAACmM,EAAYC,EAAc,CAAG3L,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,EAAC,GACvC,CAAC4L,EAAQC,EAAU,CAAG7L,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAe,EAAE,EAC/C,CAAC8L,EAAiBC,EAAmB,CAAG/L,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAW,EAAE,EAC7D,GAAEwE,CAAC,CAAE,CAAG/E,CAAAA,EAAAA,EAAAA,EAAAA,CAAcA,CAAC,UACvBuM,EAAe,CACnB,MAAS,CAAC,EACV,MAAS,IACT,KAAQ,CAAE,MAAS,KAAM,CAC3B,EAEMC,EAAiB,MAAOC,IAC5B,IAAMvH,EAAW,MAAMC,EAAAA,CAAUA,CAACC,GAAG,CAAC,eAAgBqH,GACtD,GAAIvH,GAAYG,MAAMC,OAAO,CAACJ,EAASK,IAAI,EAAG,CAC5C,IAAMmH,EAA6B,EAAE,CAC/BC,EAAwB,EAAE,CAEhCnL,IAAAA,IAAM,CAAC0D,EAASK,IAAI,CAAE,CAAC1C,EAAMrB,KAC3B,IAAMoL,EAAyB,CAC7B,GAAG/J,CAAI,CACPgK,UAAW,EACb,EACAH,EAAajL,IAAI,CAACmL,GAClBD,EAAYlL,IAAI,CAACoB,EAAKjB,GAAG,CAC3B,GAEAoK,EAAQW,GACRL,EAAmBK,GACnBP,EAAUM,EACZ,CACF,EAEAtK,CAAAA,EAAAA,EAAAA,SAAAA,CAASA,CAAC,KACRoK,EAAeD,EACjB,EAAG,EAAE,EAmBL,IAAMO,EAA+B,IACnC,IAAMC,EAAiB,IAAIZ,EAAO,CAC9Ba,EAAyB,IAAIX,EAAgB,CAEjDU,EAAeE,OAAO,CAAC,CAACpK,EAAMC,KACxBD,EAAKqK,IAAI,GAAKhM,EAAEiM,MAAM,CAAC/L,EAAE,EAAE,CAC7B2L,CAAc,CAACjK,EAAM,CAAC+J,SAAS,CAAG3L,EAAEiM,MAAM,CAACC,OAAO,CAC7ClM,EAAEiM,MAAM,CAACC,OAAO,CAGnBJ,CAHqB,CAGEvL,IAAI,CAACoB,EAAKjB,GAAG,EAFpCoL,EAAyBA,EAAuBK,MAAM,CAACC,GAAKA,IAAMzK,EAAKjB,GAAG,EAKhF,GAEA0K,EAAmBU,GACnBhB,EAAQgB,GACRd,EAAc,IACdE,EAAUW,EACZ,EAcA,MACE,WAACpD,MAAAA,CAAI/D,UAAU,qCACb,UAACS,EAAAA,CAAIA,CAACkH,KAAK,EACT/J,KAAK,WACLpC,GAAK,MACLoM,MAAOzI,EAAE,cACTqI,QAASnB,EACT7F,SAzDmB,CAyDTqH,GAxDd,IAAMV,EAAiBZ,EAAOvJ,GAAG,CAACC,GAAS,EACzC,EADyC,CACtCA,CAAI,CACPgK,UAAWa,EAAMP,MAAM,CAACC,OAAO,CACjC,GAEIO,EAA6B,EAAE,GACzBR,MAAM,CAACC,OAAO,EAAE,CACxBO,EAAmBZ,EAAenK,GAAG,CAACC,GAAQA,EAAKjB,GAAG,GAGxDoK,EAAQ2B,GACRrB,EAAmBqB,GACnBzB,EAAcwB,EAAMP,MAAM,CAACC,OAAO,EAClChB,EAAUW,EACZ,IA4CKZ,EAAOvJ,GAAG,CAAC,CAACC,EAAMC,IAEf,UAACuD,EAAAA,CAAIA,CAACkH,KAAK,EAET/J,KAAK,WACLpC,GAAIyB,EAAKqK,IAAI,CACbM,MAAO3K,EAAKnB,KAAK,CACjByE,MAAOtD,EAAKqK,IAAI,CAChB9G,SAAU0G,EACVM,QAASjB,CAAM,CAACrJ,EAAM,CAAC+J,SAAS,EAN3B/J,IAUX,UAAC8K,EAAAA,CAAMA,CAAAA,CAACtK,QAlCW,CAkCFuK,IAjCnB,IAAMd,EAAiBZ,EAAOvJ,GAAG,CAACC,GAAS,EACzC,EADyC,CACtCA,CAAI,CACPgK,WAAW,EACb,GAEAP,EAAmB,EAAE,EACrBJ,GAAc,GACdE,EAAUW,GACVf,EAAQ,EAAE,CACZ,EAwBqCpG,UAAU,0BAAkBb,EAAE,gBAGrE,CAEAgH,EAAuBxC,YAAY,CAAG,CACpCyC,QAAS,KAAS,CACpB,EAEA,MAAeD,sBAAsBA,EAAC,wCCzIvB,SAAS+B,EAAYhO,CAAuB,EACzD,MACE,UAACiO,KAAAA,CAAGnI,UAAU,wBAAgB9F,EAAM4B,KAAK,EAE7C,mBCPA,4CACA,aACA,WACA,OAAe,EAAQ,KAAwC,CAC/D,EACA,SAFsB,oOC8EtB,MAnEkB,IAChB,GAAM,CAAEqD,CAAC,CAAE,CAAG/E,CAAAA,EAAAA,EAAAA,EAAAA,CAAcA,CAAC,QAkEhBgO,EAjEP,CAAC5N,EAAY6N,EAAc,CAAG1N,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAC,EAAE,EACzC,CAAC8L,EAAiBC,EAAmB,CAAG/L,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAC,MAEjD2N,EAAwB,IAE1B,UAACC,IAAIA,CAAClL,KAAK,yBAAyBsD,GAAG,WAAlC4H,kBACH,UAACP,EAAAA,CAAMA,CAAAA,CAACQ,QAAQ,YAAYC,KAAK,cAC9BtJ,EAAE,oBAMLuJ,EAAkBzK,CAAAA,EAAAA,EAAAA,eAAAA,CAAeA,CAAC,IAAO,UAACqK,EAAAA,CAAAA,IAE1CK,EAAgB,IAEpBjC,EAAmBkC,EACnB,EAIF,MACE,WAAC9I,EAAAA,CAASA,CAAAA,CAACC,KAAK,IAACC,UAAU,gBACzB,UAACC,EAAAA,CAAGA,CAAAA,UACF,UAACC,EAAAA,CAAGA,CAAAA,CAACC,GAAI,YACP,UAAC+H,EAAAA,CAAWA,CAAAA,CAACpM,MAAOqD,EAAE,yBAG1B,UAACc,EAAAA,CAAGA,CAAAA,UACF,UAACC,EAAAA,CAAGA,CAAAA,CAACC,EAAE,IAACa,GAAI,YACV,UAAC3G,EAAAA,OAAgBA,CAAAA,CAACG,WAAYA,QAGlC,UAACyF,EAAAA,CAAGA,CAAAA,UACF,UAACC,EAAAA,CAAGA,CAAAA,CAACC,EAAE,IAACa,GAAI,YACV,UAACmF,EAAAA,CAAsBA,CAAAA,CACrBC,QAAS,GAAQuC,EAAcC,GAC/BnC,gBAAiB,EAAE,CACnBkC,cAAeA,QAIrB,UAAC1I,EAAAA,CAAGA,CAAAA,UACF,UAACC,EAAAA,CAAGA,CAAAA,CAACC,GAAI,GAAIH,UAAU,gBACrB,UAAC0I,EAAAA,CAAAA,OAGL,UAACzI,EAAAA,CAAGA,CAAAA,CAACD,UAAU,gBACb,UAACE,EAAAA,CAAGA,CAAAA,CAACC,GAAI,YACP,UAAC0I,EAAAA,OAAeA,CAAAA,CAACpC,gBAAiBA,EAAiB4B,cAAeA,UAK5E,2MC3DA,IAAMS,EAAe,OAAC,UAAEC,CAAQ,CAAO,UACrC,GAAgBA,EAAStM,MAAM,CAAG,EAE9B,UAACM,KAAAA,UACEgM,EAAS/L,GAAG,CAAC,CAACC,EAAWC,KACxB,GAAID,EAAK+L,WAAW,CAClB,CADoB,KAElB,UAAC7L,KAAAA,UACC,UAACoL,IAAIA,CACHlL,KAAK,2BACLsD,GAAI,SAFD4H,YAE2C,OAArBtL,EAAK+L,WAAW,CAAChN,GAAG,WAE5CiB,EAAK+L,WAAW,CAAClN,KAAK,IALlBoB,EAUf,KAIC,IACT,EA0PA,EAxPA,SAAS2L,CAA0B,EACjC,GAAM,CAAE1J,CAAC,CAAE,CAAG/E,CAAAA,EAAAA,EAAAA,EAAAA,CAAcA,CAAC,UACvB6O,EAASpE,CAAAA,EAAAA,EAAAA,SAAAA,CAASA,GAClB,eAAEwD,CAAa,iBAAE5B,CAAe,CAAE,CAAGvM,EACrC,CAACyE,EAAYuK,EAAc,CAAGvO,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAC,IACvC,CAACqE,EAAcmK,EAAgB,CAAGxO,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAC,IAC3C,CAAC4G,EAAuB6H,EAAyB,CAAGzO,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,EAAC,GAC7D,CAAC0O,EAAWC,EAAe,CAAG3O,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAC,EAAE,EACzC,CAACoH,EAASwH,EAAW,CAAG5O,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAC,IACjC,CAAC2G,EAAWkI,EAAa,CAAG7O,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAC,GACrC,CAAC8O,EAASC,EAAW,CAAG/O,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAC,IACjC,CAACgP,EAASC,EAAW,CAAGjP,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAC,GACjC,CAACkP,EAAUC,EAAY,CAAGnP,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAM,MAGxC0E,EAAuB,CAC3BQ,KAAM,CAAEkK,WAAY,MAAO,EAC3BC,MAAM,EACNC,SAAU,CACR,CAAEC,KAAM,kBAAmBC,OAAQ,OAAQ,EAC3C,CAAED,KAAM,uBAAwBC,OAAQ,OAAQ,EAChD,CAAED,KAAM,SAAUC,OAAQ,OAAQ,EAClC,CAAED,KAAM,UAAWC,OAAQ,aAAc,EAC1C,CACDC,MAAOX,EACPY,KAAM,EACNzK,MAAO,CAAC,EACRuK,OACE,iGACJ,EACM,CAACG,EAAUC,EAAY,CAAG5P,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAC0E,GAEnCgC,EAAU,CACd,CACE9F,KAAM4D,EAAE,cACRqL,SAAU,QACVC,UAAU,EACVC,KAAM,GACJ,UAACnC,IAAIA,CAAClL,KAAK,yBAAyBsD,GAAI,WAAnC4H,QAA4D,OAANoC,EAAE3O,GAAG,WAC7D2O,EAAE7O,KAAK,EAGd,EACA,CACEP,KAAM4D,EAAE,UACRqL,SAAU,SACVC,SAAU,GACVC,KAAM,GAAYC,EAAE1L,MAAM,EAAI0L,EAAE1L,MAAM,CAACnD,KAAK,CAAG6O,EAAE1L,MAAM,CAACnD,KAAK,CAAG,EAClE,EACA,CACEP,KAAM4D,EAAE,aACRqL,SAAU,aACVC,UAAU,EACVC,KAAM,GACJC,GAAKA,EAAEC,UAAU,CAAGC,IAAOF,EAAEC,UAAU,EAAEE,MAAM,CAAC,SAAW,EAC/D,EACA,CACEvP,KAAM4D,CAHsB0L,CAGpB,YACRL,SAAU,WACVE,KAAM,GAAY,UAAC5B,EAAAA,CAAaC,SAAU4B,EAAE5B,QAAQ,EACtD,EACD,CAIKgC,EAAoB,MAAOC,IAC/BzB,GAAW,GAEPN,EAAOrJ,KAAK,EAAIqJ,EAAOrJ,KAAK,CAAC1D,OAAO,EAAE,CACxC8O,EAAoBpL,KAAK,CAAC,OAAU,CAAGqJ,EAAOrJ,KAAK,CAAC1D,OAAAA,EAI9B,MAAM,CAA1BuK,EAEF,OAAOuE,EAAoBpL,KAAK,CAAC,YAAe,CACZ,GAAG,CAA9B6G,EAAgBhK,MAAM,CAE/BuO,EAAoBpL,KAAK,CAAC,YAAe,CAAG,CAAC,eAAe,CAG5DoL,CAH8D,CAG1CpL,KAAK,CAAC,OAHkD,KAGnC,CAAG6G,EAG9C,IAAMnH,EAAW,MAAMC,EAAAA,CAAUA,CAACC,GAAG,CAAC,aAAcwL,GAChD1L,GAAYG,MAAMC,OAAO,CAACJ,EAASK,IAAI,GAAG,CAC5C2J,EAAehK,EAASK,IAAI,EAC5B0I,EAAc/I,EAASK,IAAI,EAC3B6J,EAAalK,EAAS2L,UAAU,EAChC1B,GAAW,GAEf,EA4BM7H,EAAsB,MAAOwJ,EAAiBb,KAClDhL,EAAgB+K,KAAK,CAAGc,EACxB7L,EAAgBgL,IAAI,CAAGA,EACvBd,GAAW,GAEPN,EAAOrJ,KAAK,EAAIqJ,EAAOrJ,KAAK,CAAC1D,OAAO,EAAE,CACxCmD,EAAgBO,KAAK,CAAC,OAAU,CAAGqJ,EAAOrJ,KAAK,CAAC1D,OAAAA,EAI9CuK,MAA0B,GAC5B,OAAOpH,EAAgBO,KAAK,CAAC,YAAe,CACR,GAAG,CAA9B6G,EAAgBhK,MAAM,CAC/B4C,EAAgBO,KAAK,CAAC,YAAe,CAAG,CAAC,eAAe,CAExDP,EAAgBO,KAAK,CAAC,YAAe,CAAG6G,EAG1CzH,IACGK,EAAgBO,KAAK,CAAG,CACvB,GADDP,EACoBO,KAAK,CACxBX,OAAQD,CACV,GAEF6K,IAAaxK,EAAgBQ,IAAI,CAAGgK,CAAvBxK,CAAgCQ,IAAAA,EAE7C,IAAMP,EAAW,MAAMC,EAAAA,CAAUA,CAACC,GAAG,CAAC,aAAcH,GAChDC,GAAYG,MAAMC,OAAO,CAACJ,EAASK,IAAI,GAAG,CAC5C2J,EAAehK,EAASK,IAAI,EAC5B0I,EAAc/I,EAASK,IAAI,EAC3B+J,EAAWwB,GACX3B,GAAW,IAEbK,EAAWS,EACb,EAGA7N,CAAAA,EAAAA,EAAAA,SAAAA,CAASA,CAAC,KACR8N,EAASD,IAAI,CAAG,EAChBU,EAAkBT,EACpB,EAAG,CAAC7D,EAAiBwC,EAAO,EAE5BzM,CAAAA,EAAAA,EAAAA,SAAAA,CAASA,CAAC,KACRuO,EAAkBT,EACpB,EAAG,CAACA,EAAS,EAGb,CAHiB,GAGXa,EAAa,MAAOrK,EAAasK,KACrC7B,GAAW,GACXlK,EAAgBQ,IAAI,CAAG,CACrB,CAACiB,EAAO0J,QAAQ,CAAC,CAAEY,CACrB,EACApM,GAAiBK,GAAgBO,KAAK,CAAG,CAAE,GAA1BP,EAA6CO,KAAK,CAAEX,OAAQD,EAAa,EAC1FL,MAAsBU,EAAAA,GAAgBO,KAAK,CAAG,CAAE,GAAGP,EAAgBO,KAAK,CAAE9D,MAAO6C,EAAW,EAE5F,MAAMoM,EAAkB1L,GACxByK,EAAYzK,GACZkK,GAAW,EACb,EAEM8B,EAAY,CAACC,EAAQjB,KACrBiB,GAAG,EACI1L,KAAK,CAAC,KAAQ,CAAG0L,EAC1BhB,EAASD,IAAI,CAAGA,GAGhB,OAAOC,EAAS1K,KAAK,CAAC9D,KAAK,CAC3ByO,EAAY,CAAE,GAAGD,CAAQ,EAE7B,EAEMiB,EAAoBC,CAAAA,EAAAA,EAAAA,MAAAA,CAAMA,CAC9B5P,IAAAA,QAAU,CAAC,CAAC0P,EAAGjB,IAASgB,EAAUC,EAAGjB,GAAOoB,OAAOC,KAAgC,GAAK,MACxFC,OAAO,CAEHC,EAAyBC,CAAAA,EAAAA,EAAAA,OAAAA,CAAOA,CAAC,KAQrC,IAAMC,EAA2B,IAC/B3C,EAAgBlK,GACZA,GACFqL,EAAS1K,GADC,EACI,CAAC,MAAS,CAAGX,EAC3BqL,EAASD,IAAI,CAAGV,GAGhB,OAAOW,EAAS1K,KAAK,CAACX,MAAM,CAC5BsL,EAAY,CAAE,GAAGD,CAAQ,EAE7B,EAOA,MACE,UAAC1L,EAAAA,OAAqBA,CAAAA,CACpBC,SAPiB,CAOPkN,GANZ7C,EAAc5N,EAAEiM,MAAM,CAAChH,KAAK,EAC5BgL,EAAkBjQ,EAAEiM,MAAM,CAAChH,KAAK,CAAEoJ,EACpC,EAKI7K,qBAAsB,GAAYgN,EAAyBxQ,EAAEiM,MAAM,CAAChH,KAAK,EACzExB,QA5BgB,CA4BPiN,IA3BPrN,IACFyK,EAAyB,CAAC7H,GAC1B2H,EAFc,IAIlB,EAwBIvK,WAAYA,EACZK,aAAcA,GAGpB,EAAG,CAACL,EAAYK,EAAcuC,EAAuBkF,EAAgB,EACrE,MACE,UAACvF,EAAAA,CAAQA,CAAAA,CACPG,QAASA,EACT1B,KAAM0J,EACN/H,UAAWA,EACXS,QAASA,EACTP,SAAS,IACTa,gBAAgB,IAChBD,OAAQ+I,EACRhJ,UAAU,IACVH,WAAW,EACXT,sBAAuBA,EACvBE,mBAAoBmK,EACpBlK,oBAAqBA,EACrBC,iBArJsB0I,CAqJJ1I,GApJpBtC,EAAgB+K,KAAK,CAAGX,EACxBpK,EAAgBgL,IAAI,CAAGA,EAGJ,IAAI,CAAnB1L,IACFU,EAAgBO,KAAK,CAAG,CAAE9D,MAAO6C,EAAW,EAI9CK,IACGK,EAAgBO,KAAK,CAAG,CACvB,GADDP,EACoBO,KAAK,CACxBX,OAAQD,EACV,EAGF6K,IAAaxK,EAAgBQ,IAAI,CAAGgK,CAAvBxK,CAAgCQ,IAAAA,EAG7CkL,EAAkB1L,GAClBuK,EAAWS,EACb,GAkIF", "sources": ["webpack://_N_E/./pages/operation/ListMapContainer.tsx", "webpack://_N_E/./components/common/RKIMapMarker.tsx", "webpack://_N_E/./pages/operation/permission.tsx", "webpack://_N_E/./pages/operation/OperationsTableFilter.tsx", "webpack://_N_E/./components/common/RKITable.tsx", "webpack://_N_E/./components/common/RKIMapInfowindow.tsx", "webpack://_N_E/./components/common/mapStyles.tsx", "webpack://_N_E/./components/common/RKIMap1.tsx", "webpack://_N_E/./components/common/RegionsMultiCheckboxes.tsx", "webpack://_N_E/./components/common/PageHeading.tsx", "webpack://_N_E/?a7fd", "webpack://_N_E/./pages/operation/index.tsx", "webpack://_N_E/./pages/operation/OperationsTable.tsx"], "sourcesContent": ["//Import Library\r\nimport React, { useState, useEffect } from \"react\";\r\nimport _ from \"lodash\";\r\n\r\n//Import services/components\r\nimport RKIMAP1 from \"../../components/common/RKIMap1\";\r\nimport RKIMapMarker from \"../../components/common/RKIMapMarker\";\r\nimport { useTranslation } from 'next-i18next';\r\n\r\ninterface Operation {\r\n  _id: string;\r\n  title: string;\r\n  country?: {\r\n    _id: string;\r\n    coordinates?: Array<{\r\n      latitude: string;\r\n      longitude: string;\r\n    }>;\r\n  };\r\n}\r\n\r\ninterface ListMapContainerProps {\r\n  operations: Operation[];\r\n}\r\n\r\nconst ListMapContainer = (props: ListMapContainerProps) => {\r\n  const { i18n } = useTranslation('common');\r\n  const currentLang = i18n.language;\r\n  const { operations } = props;\r\n  const [points, setPoints] = useState<any[]>([]);\r\n  const [activeMarker, setactiveMarker]: any = useState({});\r\n  const [markerInfo, setMarkerInfo]: any = useState({});\r\n  const [groupedOperations, setGroupedOperations]: any = useState({});\r\n\r\n  const MarkerInfo = (Markerprops: { info: { countryId?: string } }) => {\r\n    const { info } = Markerprops;\r\n    if (info && info.countryId && groupedOperations[info.countryId]) {\r\n      return (\r\n        <ul>\r\n          {groupedOperations[info.countryId].map((item: any, index: any) => {\r\n            return (\r\n              <li key={index}>\r\n                <a href={`${currentLang}/operation/show/${item._id}`}>{item.title}</a>\r\n              </li>\r\n            );\r\n          })}\r\n        </ul>\r\n      );\r\n    }\r\n    return null;\r\n  };\r\n\r\n  const resetMarker = () => {\r\n    setactiveMarker(null);\r\n    setMarkerInfo(null);\r\n  };\r\n\r\n  const onMarkerClick = (propsinit: { name: string; id: string; countryId: string }, marker: any, e: any) => {\r\n    resetMarker();\r\n    setactiveMarker(marker);\r\n    setMarkerInfo({\r\n      name: propsinit.name,\r\n      id: propsinit.id,\r\n      countryId: propsinit.countryId,\r\n    });\r\n  };\r\n\r\n  const setPointersFromOperations = () => {\r\n    const filteroperationPoints: any[] = [];\r\n    _.forEach(operations, (operation) => {\r\n      filteroperationPoints.push({\r\n        title: operation.title,\r\n        id: operation._id,\r\n        lat:\r\n          operation.country &&\r\n          operation.country.coordinates &&\r\n          parseFloat(operation.country.coordinates[0].latitude),\r\n        lng:\r\n          operation.country &&\r\n          operation.country.coordinates &&\r\n          parseFloat(operation.country.coordinates[0].longitude),\r\n        countryId: operation.country && operation.country._id,\r\n      });\r\n    });\r\n    setPoints([...filteroperationPoints]);\r\n  };\r\n\r\n  useEffect(() => {\r\n    setPointersFromOperations();\r\n    if (operations && operations.length > 0) {\r\n      const groupByCountriesOperation = _.groupBy(operations, \"country._id\");\r\n      setGroupedOperations(groupByCountriesOperation);\r\n    }\r\n  }, [operations]);\r\n\r\n  return (\r\n    <RKIMAP1\r\n      onClose={resetMarker}\r\n      points={points}\r\n      language={currentLang}\r\n      activeMarker={activeMarker}\r\n      markerInfo={<MarkerInfo info={markerInfo} />}\r\n    >\r\n      {points.length >= 1\r\n        ? points.map((item, index) => {\r\n            return (\r\n              <RKIMapMarker\r\n                key={index}\r\n                name={item.title}\r\n                id={item.id}\r\n                countryId={item.countryId}\r\n                icon={{\r\n                  url: \"/images/map-marker-white.svg\",\r\n                }}\r\n                onClick={onMarkerClick}\r\n                position={item}\r\n              />\r\n            );\r\n          })\r\n        : null}\r\n    </RKIMAP1>\r\n  );\r\n};\r\n\r\nexport default ListMapContainer;\r\n", "import React from 'react';\r\nimport { Marker } from '@react-google-maps/api';\r\n\r\ninterface RKIMapMarkerProps {\r\n  name?: string;\r\n  id?: string;\r\n  countryId?: string;\r\n  type?: string;\r\n  icon?: {\r\n    url: string;\r\n    scaledSize?: google.maps.Size;\r\n  };\r\n  position: {\r\n    lat: number;\r\n    lng: number;\r\n  };\r\n  onClick?: (props: any, marker: any, e: any) => void;\r\n  title?: string;\r\n  draggable?: boolean;\r\n}\r\n\r\nconst RKIMapMarker: React.FC<RKIMapMarkerProps> = ({\r\n  name = 'Marker',\r\n  id = '',\r\n  countryId = '',\r\n  type,\r\n  icon,\r\n  position,\r\n  onClick,\r\n  title,\r\n  draggable = false,\r\n}) => {\r\n  const handleClick = (e: google.maps.MapMouseEvent) => {\r\n    if (onClick) {\r\n      const markerProps = {\r\n        name,\r\n        id,\r\n        countryId,\r\n        type,\r\n        position,\r\n      };\r\n\r\n      // Create a marker-like object for compatibility with old onClick signature\r\n      const marker = {\r\n        position,\r\n        getPosition: () => position,\r\n      };\r\n\r\n      onClick(markerProps, marker, e);\r\n    }\r\n  };\r\n\r\n  // Ensure position is valid\r\n  if (!position || typeof position.lat !== 'number' || typeof position.lng !== 'number') {\r\n    return null;\r\n  }\r\n\r\n  return (\r\n    <Marker\r\n      position={position}\r\n      icon={icon}\r\n      title={title || name}\r\n      draggable={draggable}\r\n      onClick={handleClick}\r\n    />\r\n  );\r\n};\r\n\r\nexport default RKIMapMarker;\r\n", "//Import Library\r\nimport React from 'react';\r\nimport connectedAuthWrapper from 'redux-auth-wrapper/connectedAuthWrapper';\r\n\r\n//Import services/components\r\nimport R403 from \"../r403\";\r\n\r\nexport const canAddOperation = connectedAuthWrapper({\r\n  authenticatedSelector: (state) => {\r\n    if (state.permissions && state.permissions.operation && state.permissions.operation['create:any']) {\r\n      return true;\r\n    }\r\n    return false;\r\n  },\r\n  wrapperDisplayName: 'CanAddOperation',\r\n});\r\n\r\nexport const canAddOperationForm = connectedAuthWrapper({\r\n  authenticatedSelector: (state) => {\r\n    if (state.permissions && state.permissions.operation && state.permissions.operation['create:any']) {\r\n      return true;\r\n    }\r\n    return false;\r\n  },\r\n  wrapperDisplayName: 'CanAddOperationForm',\r\n  FailureComponent: () => <R403/>\r\n});\r\n\r\nexport const canEditOperation = connectedAuthWrapper({\r\n  authenticatedSelector: (state, props) => {\r\n    if (state.permissions && state.permissions.operation) {\r\n      if (state.permissions.operation['update:any']) {\r\n        return true;\r\n      } else { //if update:own\r\n        if (state.permissions.operation['update:own']) {\r\n          if (props.operation && props.operation.user && props.operation.user._id === state.user._id) {\r\n            return true;\r\n          }\r\n        }\r\n      }\r\n    }\r\n    return false;\r\n  },\r\n  wrapperDisplayName: 'CanEditOperation',\r\n});\r\n\r\nexport const canEditOperationForm = connectedAuthWrapper({\r\n  authenticatedSelector: (state, props) => {\r\n    if (state.permissions && state.permissions.operation) {\r\n      if (state.permissions.operation['update:any']) {\r\n        return true;\r\n      } else { //if update:own\r\n        if (state.permissions.operation['update:own']) {\r\n          if (props.operation && props.operation.user && props.operation.user._id === state.user._id) {\r\n            return true;\r\n          }\r\n        }\r\n      }\r\n    }\r\n    return false;\r\n  },\r\n  wrapperDisplayName: 'CanEditOperationForm',\r\n  FailureComponent: () => <R403/>\r\n});\r\n\r\nexport const canViewDiscussionUpdate = connectedAuthWrapper({\r\n  authenticatedSelector: (state) => {\r\n    if (state.permissions && state.permissions.update && state.permissions.update['read:any']) {\r\n      return true;\r\n    }\r\n    return false;\r\n  },\r\n  wrapperDisplayName: 'CanViewDiscussionUpdate',\r\n});\r\n\r\nexport default canAddOperation;", "//Import Library\r\nimport { useEffect, useState } from \"react\";\r\nimport { Col, Container, FormControl, Form, Row } from \"react-bootstrap\";\r\n\r\n//Import services/components\r\nimport apiService from \"../../services/apiService\";\r\nimport { useTranslation } from 'next-i18next';\r\n\r\n\r\nconst OperationsTableFilter = ({\r\n  filterText,\r\n  onFilter,\r\n  onFilterStatusChange,\r\n  onClear,\r\n  filterStatus,\r\n}: {\r\n  filterText: any,\r\n  onFilter: any,\r\n  onFilterStatusChange: any,\r\n  onClear: any,\r\n  filterStatus: any,\r\n}) => {\r\n  const [status, setStatus] = useState([]);\r\n  const { t } = useTranslation('common');\r\n\r\n  const getOperationsStatus = async (operationParams: any) => {\r\n    const response = await apiService.get(\"/operation_status\", operationParams);\r\n    if (response && Array.isArray(response.data)) { \r\n      setStatus(response.data);\r\n    }\r\n  };\r\n\r\n  useEffect(() => {\r\n    getOperationsStatus({\r\n      query: {},\r\n      sort: { title: \"asc\" },\r\n    });\r\n  }, []);\r\n\r\n  return (\r\n    <Container fluid className=\"p-0\">\r\n      <Row>\r\n        <Col xs={6} className=\"ps-0 align-self-end mb-3\">\r\n          <FormControl\r\n            type=\"text\"\r\n            className=\"searchInput\"\r\n            placeholder={t(\"search\")}\r\n            aria-label=\"Search\"\r\n            value={filterText}\r\n            onChange={onFilter}\r\n          />\r\n        </Col>\r\n        <Col xs={6}>\r\n          <Form>\r\n            <Form.Group as={Row} controlId=\"statusFilter\">\r\n              <Form.Label column sm=\"3\" lg=\"2\">\r\n              {t(\"Status\")}\r\n              </Form.Label>\r\n              <Col className=\"ps-0 pe-1\">\r\n                <FormControl\r\n                  as=\"select\"\r\n                  aria-label=\"Status\"\r\n                  onChange={onFilterStatusChange}\r\n                  value={filterStatus}\r\n                >\r\n                  <option value={\"\"}>All</option>\r\n                  {status.map((item: any, index) => {\r\n                    return (\r\n                      <option key={index} value={item._id}>\r\n                        {item.title}\r\n                      </option>\r\n                    );\r\n                  })}\r\n                </FormControl>\r\n              </Col>\r\n            </Form.Group>\r\n          </Form>\r\n        </Col>\r\n      </Row>\r\n    </Container>\r\n  );\r\n};\r\n\r\nexport default OperationsTableFilter;\r\n", "//Import Library\r\nimport React from 'react'\r\nimport DataTable  from 'react-data-table-component';\r\n\r\n//Import services/components\r\nimport { useTranslation } from 'next-i18next';\r\nimport CustomLoader from './CustomLoader';\r\n\r\ninterface RKITableProps {\r\n  columns: any[];\r\n  data: any[];\r\n  totalRows?: number;\r\n  resetPaginationToggle?: boolean;\r\n  subheader?: boolean;\r\n  subHeaderComponent?: React.ReactNode;\r\n  handlePerRowsChange?: (newPerPage: number, page: number) => void;\r\n  handlePageChange?: (page: number) => void;\r\n  rowsPerPage?: number[];\r\n  defaultRowsPerPage?: number;\r\n  selectableRows?: boolean;\r\n  loading?: boolean;\r\n  pagServer?: boolean;\r\n  onSelectedRowsChange?: (selectedRows: any) => void;\r\n  clearSelectedRows?: boolean;\r\n  sortServer?: boolean;\r\n  onSort?: (column: any, sortDirection: string) => void;\r\n  persistTableHead?: boolean;\r\n  sortFunction?: (rows: any[], field: any, direction: 'asc' | 'desc') => any[];\r\n  noHeader?: boolean;\r\n  dense?: boolean;\r\n  pagination?: boolean;\r\n  paginationServer?: boolean;\r\n  paginationTotalRows?: number;\r\n  subHeaderAlign?: string;\r\n  [key: string]: any;\r\n}\r\n\r\nfunction RKITable(props: RKITableProps) {\r\n  const { t } = useTranslation('common');\r\n  const paginationComponentOptions = {\r\n    rowsPerPageText: t('Rowsperpage'),\r\n    };\r\n  const {\r\n    columns,\r\n    data,\r\n    totalRows,\r\n    resetPaginationToggle,\r\n    subheader,\r\n    subHeaderComponent,\r\n    handlePerRowsChange,\r\n    handlePageChange,\r\n    rowsPerPage,\r\n    defaultRowsPerPage,\r\n    selectableRows,\r\n    loading,\r\n    pagServer,\r\n    onSelectedRowsChange,\r\n    clearSelectedRows,\r\n    sortServer,\r\n    onSort,\r\n    persistTableHead,\r\n    sortFunction,\r\n    // Filter out any unknown props that might cause DOM warnings\r\n    ...otherProps\r\n  } = props;\r\n\r\n  // Only pass known DataTable props to avoid DOM warnings\r\n  const dataTableProps = {\r\n    paginationComponentOptions,\r\n    noDataComponent: t(\"NoData\"),\r\n    noHeader: true,\r\n    columns,\r\n    data: data || [],\r\n    dense: true,\r\n    paginationResetDefaultPage: resetPaginationToggle,\r\n    subHeader: subheader,\r\n    progressPending: loading,\r\n    subHeaderComponent,\r\n    pagination: true,\r\n    paginationServer: pagServer,\r\n    paginationPerPage: defaultRowsPerPage ? defaultRowsPerPage : 10,\r\n    paginationRowsPerPageOptions: rowsPerPage ? rowsPerPage : [10, 15, 20, 25, 30],\r\n    paginationTotalRows: totalRows,\r\n    onChangeRowsPerPage: handlePerRowsChange,\r\n    onChangePage: handlePageChange,\r\n    selectableRows,\r\n    onSelectedRowsChange,\r\n    clearSelectedRows,\r\n    progressComponent: <CustomLoader/>,\r\n    sortIcon: <i className=\"sort-icon fas fa-exchange-alt\" />,\r\n    sortServer,\r\n    onSort,\r\n    sortFunction,\r\n    persistTableHead,\r\n    className: \"rki-table\"\r\n  };\r\n  return (\r\n    <DataTable {...dataTableProps} />\r\n  )\r\n};\r\n\r\nRKITable.defaultProps = {\r\n  subHeader: false,\r\n  pagination: true,\r\n  totalRows: null,\r\n  pagServer: true,\r\n  onSelectedRowsChange: null,\r\n  clearSelectedRows: false,\r\n  sortServer: false,\r\n  persistTableHead: false\r\n}\r\n\r\nexport default RKITable;\r\n\r\n", "import { InfoWindow } from '@react-google-maps/api';\r\n\r\ninterface Props {\r\n  position: google.maps.LatLngLiteral;\r\n  onCloseClick?: () => void;\r\n  children?: React.ReactNode;\r\n}\r\n\r\nconst RKIMapInfowindow = ({ position, onCloseClick, children }: Props) => {\r\n  return (\r\n    <InfoWindow position={position} onCloseClick={onCloseClick}>\r\n      <div>{children}</div>\r\n    </InfoWindow>\r\n  );\r\n};\r\n\r\nexport default RKIMapInfowindow;\r\n", "const fill = \"labels.text.fill\"\r\nconst stoke = \"labels.text.stroke\"\r\nconst road = \"road.highway\"\r\nconst geometry = \"geometry.stroke\"\r\nconst mapStyles = [\r\n  {\r\n    \"elementType\": \"geometry\",\r\n    \"stylers\": [\r\n      {\r\n        \"color\": \"#1d2c4d\"\r\n      }\r\n    ]\r\n  },\r\n  {\r\n    \"elementType\": fill,\r\n    \"stylers\": [\r\n      {\r\n        \"color\": \"#8ec3b9\"\r\n      }\r\n    ]\r\n  },\r\n  {\r\n    \"elementType\": stoke,\r\n    \"stylers\": [\r\n      {\r\n        \"color\": \"#1a3646\"\r\n      }\r\n    ]\r\n  },\r\n  {\r\n    \"featureType\": \"administrative\",\r\n    \"elementType\": \"geometry\",\r\n    \"stylers\": [\r\n      {\r\n        \"visibility\": \"off\"\r\n      }\r\n    ]\r\n  },\r\n  {\r\n    \"featureType\": \"administrative.country\",\r\n    \"elementType\": geometry,\r\n    \"stylers\": [\r\n      {\r\n        \"color\": \"#4b6878\"\r\n      }\r\n    ]\r\n  },\r\n  {\r\n    \"featureType\": \"administrative.land_parcel\",\r\n    \"elementType\": \"labels\",\r\n    \"stylers\": [\r\n      {\r\n        \"visibility\": \"off\"\r\n      }\r\n    ]\r\n  },\r\n  {\r\n    \"featureType\": \"administrative.land_parcel\",\r\n    \"elementType\": fill,\r\n    \"stylers\": [\r\n      {\r\n        \"color\": \"#64779e\"\r\n      }\r\n    ]\r\n  },\r\n  {\r\n    \"featureType\": \"administrative.province\",\r\n    \"elementType\": geometry,\r\n    \"stylers\": [\r\n      {\r\n        \"color\": \"#4b6878\"\r\n      }\r\n    ]\r\n  },\r\n  {\r\n    \"featureType\": \"landscape.man_made\",\r\n    \"elementType\": geometry,\r\n    \"stylers\": [\r\n      {\r\n        \"color\": \"#334e87\"\r\n      }\r\n    ]\r\n  },\r\n  {\r\n    \"featureType\": \"landscape.natural\",\r\n    \"elementType\": \"geometry\",\r\n    \"stylers\": [\r\n      {\r\n        \"color\": \"#023e58\"\r\n      }\r\n    ]\r\n  },\r\n  {\r\n    \"featureType\": \"poi\",\r\n    \"stylers\": [\r\n      {\r\n        \"visibility\": \"off\"\r\n      }\r\n    ]\r\n  },\r\n  {\r\n    \"featureType\": \"poi\",\r\n    \"elementType\": \"geometry\",\r\n    \"stylers\": [\r\n      {\r\n        \"color\": \"#283d6a\"\r\n      }\r\n    ]\r\n  },\r\n  {\r\n    \"featureType\": \"poi\",\r\n    \"elementType\": \"labels.text\",\r\n    \"stylers\": [\r\n      {\r\n        \"visibility\": \"off\"\r\n      }\r\n    ]\r\n  },\r\n  {\r\n    \"featureType\": \"poi\",\r\n    \"elementType\": fill,\r\n    \"stylers\": [\r\n      {\r\n        \"color\": \"#6f9ba5\"\r\n      }\r\n    ]\r\n  },\r\n  {\r\n    \"featureType\": \"poi\",\r\n    \"elementType\": stoke,\r\n    \"stylers\": [\r\n      {\r\n        \"color\": \"#1d2c4d\"\r\n      }\r\n    ]\r\n  },\r\n  {\r\n    \"featureType\": \"poi.park\",\r\n    \"elementType\": \"geometry.fill\",\r\n    \"stylers\": [\r\n      {\r\n        \"color\": \"#023e58\"\r\n      }\r\n    ]\r\n  },\r\n  {\r\n    \"featureType\": \"poi.park\",\r\n    \"elementType\": fill,\r\n    \"stylers\": [\r\n      {\r\n        \"color\": \"#3C7680\"\r\n      }\r\n    ]\r\n  },\r\n  {\r\n    \"featureType\": \"road\",\r\n    \"stylers\": [\r\n      {\r\n        \"visibility\": \"off\"\r\n      }\r\n    ]\r\n  },\r\n  {\r\n    \"featureType\": \"road\",\r\n    \"elementType\": \"geometry\",\r\n    \"stylers\": [\r\n      {\r\n        \"color\": \"#304a7d\"\r\n      }\r\n    ]\r\n  },\r\n  {\r\n    \"featureType\": \"road\",\r\n    \"elementType\": \"labels.icon\",\r\n    \"stylers\": [\r\n      {\r\n        \"visibility\": \"off\"\r\n      }\r\n    ]\r\n  },\r\n  {\r\n    \"featureType\": \"road\",\r\n    \"elementType\": fill,\r\n    \"stylers\": [\r\n      {\r\n        \"color\": \"#98a5be\"\r\n      }\r\n    ]\r\n  },\r\n  {\r\n    \"featureType\": \"road\",\r\n    \"elementType\": stoke,\r\n    \"stylers\": [\r\n      {\r\n        \"color\": \"#1d2c4d\"\r\n      }\r\n    ]\r\n  },\r\n  {\r\n    \"featureType\": road,\r\n    \"elementType\": \"geometry\",\r\n    \"stylers\": [\r\n      {\r\n        \"color\": \"#2c6675\"\r\n      }\r\n    ]\r\n  },\r\n  {\r\n    \"featureType\": road,\r\n    \"elementType\": geometry,\r\n    \"stylers\": [\r\n      {\r\n        \"color\": \"#255763\"\r\n      }\r\n    ]\r\n  },\r\n  {\r\n    \"featureType\": road,\r\n    \"elementType\": fill,\r\n    \"stylers\": [\r\n      {\r\n        \"color\": \"#b0d5ce\"\r\n      }\r\n    ]\r\n  },\r\n  {\r\n    \"featureType\": road,\r\n    \"elementType\": stoke,\r\n    \"stylers\": [\r\n      {\r\n        \"color\": \"#023e58\"\r\n      }\r\n    ]\r\n  },\r\n  {\r\n    \"featureType\": \"road.local\",\r\n    \"elementType\": \"labels\",\r\n    \"stylers\": [\r\n      {\r\n        \"visibility\": \"off\"\r\n      }\r\n    ]\r\n  },\r\n  {\r\n    \"featureType\": \"transit\",\r\n    \"stylers\": [\r\n      {\r\n        \"visibility\": \"off\"\r\n      }\r\n    ]\r\n  },\r\n  {\r\n    \"featureType\": \"transit\",\r\n    \"elementType\": fill,\r\n    \"stylers\": [\r\n      {\r\n        \"color\": \"#98a5be\"\r\n      }\r\n    ]\r\n  },\r\n  {\r\n    \"featureType\": \"transit\",\r\n    \"elementType\": stoke,\r\n    \"stylers\": [\r\n      {\r\n        \"color\": \"#1d2c4d\"\r\n      }\r\n    ]\r\n  },\r\n  {\r\n    \"featureType\": \"transit.line\",\r\n    \"elementType\": \"geometry.fill\",\r\n    \"stylers\": [\r\n      {\r\n        \"color\": \"#283d6a\"\r\n      }\r\n    ]\r\n  },\r\n  {\r\n    \"featureType\": \"transit.station\",\r\n    \"elementType\": \"geometry\",\r\n    \"stylers\": [\r\n      {\r\n        \"color\": \"#3a4762\"\r\n      }\r\n    ]\r\n  },\r\n  {\r\n    \"featureType\": \"water\",\r\n    \"elementType\": \"geometry\",\r\n    \"stylers\": [\r\n      {\r\n        \"color\": \"#0e1626\"\r\n      }\r\n    ]\r\n  },\r\n  {\r\n    \"featureType\": \"water\",\r\n    \"elementType\": fill,\r\n    \"stylers\": [\r\n      {\r\n        \"color\": \"#4e6d70\"\r\n      }\r\n    ]\r\n  }\r\n];\r\n\r\nexport default mapStyles;", "import React from 'react';\r\nimport { GoogleMap, useJsApiLoader } from '@react-google-maps/api';\r\nimport RKIMapInfowindow from './RKIMapInfowindow';\r\nimport mapStyles from './mapStyles';\r\nimport { useRouter } from \"next/router\";\r\nimport { useGoogleMaps } from './GoogleMapsProvider';\r\n\r\ninterface RKIMap1Props {\r\n  markerInfo?: React.ReactNode;\r\n  activeMarker?: any;\r\n  initialCenter?: { lat: number; lng: number };\r\n  children?: React.ReactNode;\r\n  height?: number | string;\r\n  width?: string;\r\n  language?: string;\r\n  points?: any[];\r\n  zoom?: number;\r\n  minZoom?: number;\r\n  onClose?: () => void\r\n}\r\n\r\nconst RKIMap1: React.FC<RKIMap1Props> = ({\r\n  markerInfo,\r\n  activeMarker,\r\n  initialCenter,\r\n  children,\r\n  height = 300,\r\n  width = \"114%\",\r\n  language,\r\n  zoom = 1,\r\n  minZoom = 1,\r\n  onClose\r\n}) => {\r\n  const { locale } = useRouter();\r\n  const { isLoaded, loadError } =  useGoogleMaps();\r\n  const containerStyle = {\r\n    width: width,\r\n    height: typeof height === 'number' ? `${height}px` : height,\r\n  };\r\n\r\n  const defaultCenter = {\r\n    lat: 52.520017,\r\n    lng: 13.404195,\r\n  };\r\n\r\n  const center = initialCenter || defaultCenter;\r\n\r\n  const onMapLoad = (map: google.maps.Map) => {\r\n    map.setOptions({\r\n      styles: mapStyles,\r\n    });\r\n  };\r\n\r\n  if (loadError) return <div>Error loading maps</div>;\r\n  if (!isLoaded) return <div>Loading Maps...</div>;\r\n\r\n  return (\r\n    <div className=\"map-container\">\r\n      <div className=\"mapprint\" style={{ width, height, position: 'relative' }}>\r\n        <GoogleMap\r\n          mapContainerStyle={containerStyle}\r\n          center={center}\r\n          zoom={zoom}\r\n          onLoad={onMapLoad}\r\n          options={{\r\n            minZoom: minZoom,\r\n            draggable: true,\r\n            keyboardShortcuts: false,\r\n            streetViewControl: false,\r\n            panControl: false,\r\n            clickableIcons: false,\r\n            mapTypeControl: false,\r\n            fullscreenControl: true,\r\n          }}\r\n        >\r\n          {children}\r\n          {markerInfo && activeMarker && activeMarker.getPosition && (\r\n            <RKIMapInfowindow\r\n              position={activeMarker.getPosition()}\r\n              onCloseClick={() => {\r\n                // Handle close if needed\r\n                console.log('close click');\r\n                onClose?.()\r\n              }}\r\n            >\r\n              {markerInfo}\r\n            </RKIMapInfowindow>\r\n          )}\r\n        </GoogleMap>\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default RKIMap1;\r\n", "//Import Library\r\nimport React, { useState, useEffect } from 'react';\r\nimport _ from 'lodash';\r\nimport { Form, Button } from \"react-bootstrap\";\r\n\r\n//Import services/components\r\nimport apiService from '../../services/apiService';\r\nimport { useTranslation } from 'next-i18next';\r\n\r\n// Define types for region items\r\ninterface RegionItem {\r\n  _id: string;\r\n  code: string;\r\n  title: string;\r\n  isChecked: boolean;\r\n}\r\n\r\ninterface RegionsMultiCheckboxesProps {\r\n  regionHandler: (regions: string[]) => void;\r\n  selectedRegions: string[];\r\n  filtreg: (regions: string[]) => void;\r\n}\r\n\r\nfunction RegionsMultiCheckboxes(props: RegionsMultiCheckboxesProps) {\r\n  const {filtreg} = props;\r\n  const [allregions, setAllregions] = useState(true);\r\n  const [region, setRegion] = useState<RegionItem[]>([]);\r\n  const [selectedRegions, setSelectedRegions] = useState<string[]>([]);\r\n  const { t } = useTranslation('common');\r\n  const RegionParams = {\r\n    \"query\": {},\r\n    \"limit\": \"~\",\r\n    \"sort\": { \"title\": \"asc\" }\r\n  };\r\n\r\n  const getworldregion = async (RegionParams_initial: typeof RegionParams) => {\r\n    const response = await apiService.get('/worldregion', RegionParams_initial);\r\n    if (response && Array.isArray(response.data)) {\r\n      const finalRegions: RegionItem[] = [];\r\n      const selectedIds: string[] = [];\r\n\r\n      _.each(response.data, (item, _) => {\r\n        const regionItem: RegionItem = {\r\n          ...item,\r\n          isChecked: true\r\n        };\r\n        finalRegions.push(regionItem);\r\n        selectedIds.push(item._id);\r\n      });\r\n\r\n      filtreg(selectedIds);\r\n      setSelectedRegions(selectedIds);\r\n      setRegion(finalRegions);\r\n    }\r\n  };\r\n\r\n  useEffect(() => {\r\n    getworldregion(RegionParams);\r\n  }, [])\r\n\r\n  const handleAllChecked = (event: React.ChangeEvent<HTMLInputElement>) => {\r\n    const updatedRegions = region.map(item => ({\r\n      ...item,\r\n      isChecked: event.target.checked\r\n    }));\r\n\r\n    let selected_Regions: string[] = [];\r\n    if (event.target.checked) {\r\n      selected_Regions = updatedRegions.map(item => item._id);\r\n    }\r\n\r\n    filtreg(selected_Regions);\r\n    setSelectedRegions(selected_Regions);\r\n    setAllregions(event.target.checked);\r\n    setRegion(updatedRegions);\r\n  };\r\n\r\n  const handleIndividualRegionChange = (e: React.ChangeEvent<HTMLInputElement>) => {\r\n    const updatedRegions = [...region];\r\n    let updatedSelectedRegions = [...selectedRegions];\r\n\r\n    updatedRegions.forEach((item, index) => {\r\n      if (item.code === e.target.id) {\r\n        updatedRegions[index].isChecked = e.target.checked;\r\n        if (!e.target.checked) {\r\n          updatedSelectedRegions = updatedSelectedRegions.filter(n => n !== item._id);\r\n        } else {\r\n          updatedSelectedRegions.push(item._id);\r\n        }\r\n      }\r\n    });\r\n\r\n    setSelectedRegions(updatedSelectedRegions);\r\n    filtreg(updatedSelectedRegions);\r\n    setAllregions(false);\r\n    setRegion(updatedRegions);\r\n  };\r\n\r\n  const resetAllRegion = () => {\r\n    const updatedRegions = region.map(item => ({\r\n      ...item,\r\n      isChecked: false\r\n    }));\r\n\r\n    setSelectedRegions([]);\r\n    setAllregions(false);\r\n    setRegion(updatedRegions);\r\n    filtreg([]);\r\n  };\r\n\r\n  return (\r\n    <div className=\"regions-multi-checkboxes\">\r\n      <Form.Check\r\n        type=\"checkbox\"\r\n        id={`all`}\r\n        label={t(\"AllRegions\")}\r\n        checked={allregions}\r\n        onChange={handleAllChecked}\r\n      />\r\n      {region.map((item, index) => {\r\n        return (\r\n          <Form.Check\r\n            key={index}\r\n            type=\"checkbox\"\r\n            id={item.code}\r\n            label={item.title}\r\n            value={item.code}\r\n            onChange={handleIndividualRegionChange}\r\n            checked={region[index].isChecked}\r\n          />\r\n        )\r\n      })}\r\n      <Button onClick={resetAllRegion} className=\"btn-plain ps-2\">{t(\"ClearAll\")}</Button>\r\n    </div>\r\n  )\r\n}\r\n\r\nRegionsMultiCheckboxes.defaultProps = {\r\n  filtreg: () => {\"\"}\r\n}\r\n\r\nexport default RegionsMultiCheckboxes;\r\n", "interface PageHeadingProps {\r\n  title: string; // Required based on actual usage\r\n}\r\n\r\nexport default function PageHeading(props: PageHeadingProps) {\r\n  return (\r\n    <h2 className=\"page-heading\">{props.title}</h2>\r\n  )\r\n}\r\n", "\n    (window.__NEXT_P = window.__NEXT_P || []).push([\n      \"/operation\",\n      function () {\n        return require(\"private-next-pages/operation/index.tsx\");\n      }\n    ]);\n    if(module.hot) {\n      module.hot.dispose(function () {\n        window.__NEXT_P.push([\"/operation\"])\n      });\n    }\n  ", "//Import Library\r\nimport { Container, Row, Col } from \"react-bootstrap\";\r\nimport Link from 'next/link';\r\nimport Button from 'react-bootstrap/Button';\r\nimport React, {useState} from \"react\";\r\n\r\n//Import services/components\r\nimport RegionsMultiCheckboxes from \"../../components/common/RegionsMultiCheckboxes\";\r\nimport PageHeading from \"../../components/common/PageHeading\";\r\nimport OperationsTable from \"./OperationsTable\";\r\nimport ListMapContainer from \"./ListMapContainer\";\r\nimport { useTranslation } from 'next-i18next';\r\nimport { serverSideTranslations } from 'next-i18next/serverSideTranslations';\r\nimport { canAddOperation } from \"./permission\";\r\n\r\nconst Operation = (_props: any) => {\r\n  const { t } = useTranslation('common');\r\n  const [operations, setOperations] = useState([]);\r\n  const [selectedRegions, setSelectedRegions] = useState(null);\r\n\r\n  const AddOperationComponent = () => {\r\n    return (\r\n      <Link href=\"/operation/[...routes]\" as=\"/operation/create\" >\r\n        <Button variant=\"secondary\" size=\"sm\">\r\n          {t('addOperation')}\r\n        </Button>\r\n      </Link>\r\n    );\r\n  };\r\n\r\n  const CanAddOperation = canAddOperation(() =>  <AddOperationComponent />);\r\n\r\n  const regionHandler = (val: any) => {\r\n\r\n    setSelectedRegions(val);\r\n    }\r\n\r\n\r\n\r\n  return (\r\n    <Container fluid className=\"p-0\">\r\n      <Row>\r\n        <Col xs={12}>\r\n          <PageHeading title={t('menu.operations')} />\r\n        </Col>\r\n      </Row>\r\n      <Row>\r\n        <Col xs lg={12} >\r\n          <ListMapContainer operations={operations} />\r\n        </Col>\r\n      </Row>\r\n      <Row>\r\n        <Col xs lg={12}>\r\n          <RegionsMultiCheckboxes\r\n            filtreg={(val)=> regionHandler(val)}\r\n            selectedRegions={[]}\r\n            regionHandler={regionHandler}\r\n          />\r\n        </Col>\r\n      </Row>\r\n      <Row>\r\n        <Col xs={12} className=\"ps-4\">\r\n          <CanAddOperation />\r\n        </Col>\r\n      </Row>\r\n      <Row className=\"mt-3\">\r\n        <Col xs={12}>\r\n          <OperationsTable selectedRegions={selectedRegions} setOperations={setOperations} />\r\n        </Col>\r\n      </Row>\r\n    </Container>\r\n  )\r\n};\r\n\r\nexport async function getStaticProps({ locale }: { locale: string }) {\r\n  return {\r\n    props: {\r\n      ...(await serverSideTranslations(locale, ['common'])),\r\n    },\r\n  }\r\n}\r\n\r\nexport default Operation;", "//Import Library\r\nimport React, { useRef, useMemo, useEffect, useState } from \"react\";\r\nimport Link from \"next/link\";\r\nimport _ from \"lodash\";\r\nimport { useRouter } from \"next/router\";\r\nimport moment from \"moment\";\r\n\r\n//Import services/components\r\nimport RKITable from \"../../components/common/RKITable\";\r\nimport apiService from \"../../services/apiService\";\r\nimport OperationsTableFilter from \"./OperationsTableFilter\";\r\nimport { useTranslation } from 'next-i18next';\r\n\r\nconst PartnersLink = ({ partners }: any) => {\r\n  if (partners && partners.length > 0) {\r\n    return (\r\n      <ul>\r\n        {partners.map((item: any, index: any) => {\r\n          if (item.institution) {\r\n            return (\r\n              <li key={index}>\r\n                <Link\r\n                  href=\"/institution/[...routes]\"\r\n                  as={`/institution/show/${item.institution._id}`}\r\n                >\r\n                  {item.institution.title}\r\n                </Link>\r\n              </li>\r\n            );\r\n          }\r\n        })}\r\n      </ul>\r\n    );\r\n  }\r\n  return null;\r\n};\r\n\r\nfunction OperationsTable(props: any) {\r\n  const { t } = useTranslation('common');\r\n  const router = useRouter();\r\n  const { setOperations, selectedRegions } = props;\r\n  const [filterText, setFilterText] = useState(\"\");\r\n  const [filterStatus, setFilterStatus] = useState(\"\");\r\n  const [resetPaginationToggle, setResetPaginationToggle] = useState(false);\r\n  const [tabledata, setDataToTable] = useState([]);\r\n  const [loading, setLoading] = useState(false);\r\n  const [totalRows, setTotalRows] = useState(0);\r\n  const [perPage, setPerPage] = useState(10);\r\n  const [pageNum, setPageNum] = useState(1);\r\n  const [pageSort, setPageSort] = useState<any>(null);\r\n\r\n\r\n  const operationParams: any = {\r\n    sort: { created_at: \"desc\" },\r\n    lean: true,\r\n    populate: [\r\n      { path: \"partners.status\", select: \"title\" },\r\n      { path: \"partners.institution\", select: \"title\" },\r\n      { path: \"status\", select: \"title\" },\r\n      { path: \"country\", select: \"coordinates\" },\r\n    ],\r\n    limit: perPage,\r\n    page: 1,\r\n    query: {},\r\n    select:\r\n      \"-timeline -region -hazard -description -end_date -syndrome -hazard_type -created_at -updated_at\",\r\n  };\r\n  const [opParams, setOpParams] = useState(operationParams);\r\n\r\n  const columns = [\r\n    {\r\n      name: t(\"Operations\"),\r\n      selector: \"title\",\r\n      sortable: true,\r\n      cell: (d: any) => (\r\n        <Link href=\"/operation/[...routes]\" as={`/operation/show/${d._id}`}>\r\n          {d.title}\r\n        </Link>\r\n      ),\r\n    },\r\n    {\r\n      name: t(\"Status\"),\r\n      selector: \"status\",\r\n      sortable: true,\r\n      cell: (d: any) => d.status && d.status.title ? d.status.title : \"\"\r\n    },\r\n    {\r\n      name: t(\"StartDate\"),\r\n      selector: \"start_date\",\r\n      sortable: true,\r\n      cell: (d: any) =>\r\n        d && d.start_date ? moment(d.start_date).format(\"M/D/Y\") : \"\",\r\n    },\r\n    {\r\n      name: t(\"Partners\"),\r\n      selector: \"partners\",\r\n      cell: (d: any) => <PartnersLink partners={d.partners} />,\r\n    },\r\n  ];\r\n\r\n\r\n\r\n  const getOperationsData = async (operationParamsinit: any) => {\r\n    setLoading(true);\r\n\r\n    if (router.query && router.query.country) {\r\n      operationParamsinit.query[\"country\"] = router.query.country;\r\n    }\r\n\r\n    // Handle selectedRegions with proper condition\r\n    if (selectedRegions === null) {\r\n      // First load: don't apply region filter\r\n      delete operationParamsinit.query[\"world_region\"];\r\n    } else if (selectedRegions.length === 0) {\r\n      // No regions selected: force zero results\r\n      operationParamsinit.query[\"world_region\"] = [\"__NO_MATCH__\"]; // dummy value\r\n    } else {\r\n      // Normal filtering\r\n      operationParamsinit.query[\"world_region\"] = selectedRegions;\r\n    }\r\n\r\n    const response = await apiService.get(\"/operation\", operationParamsinit);\r\n    if (response && Array.isArray(response.data)) {\r\n      setDataToTable(response.data);\r\n      setOperations(response.data);\r\n      setTotalRows(response.totalCount);\r\n      setLoading(false);\r\n    }\r\n  };\r\n\r\n\r\n  const handlePageChange = (page: any) => {\r\n    operationParams.limit = perPage;\r\n    operationParams.page = page;\r\n\r\n    // Apply the filter for title if present\r\n    if (filterText !== \"\") {\r\n      operationParams.query = { title: filterText };\r\n    }\r\n\r\n    // Apply filter for status if present\r\n    filterStatus &&\r\n      (operationParams.query = {\r\n        ...operationParams.query,\r\n        status: filterStatus,\r\n      });\r\n\r\n    // Handle sorting\r\n    pageSort && (operationParams.sort = pageSort.sort);\r\n\r\n    // Get the data\r\n    getOperationsData(operationParams);\r\n    setPageNum(page);\r\n  };\r\n\r\n\r\n  const handlePerRowsChange = async (newPerPage: any, page: any) => {\r\n    operationParams.limit = newPerPage;\r\n    operationParams.page = page;\r\n    setLoading(true);\r\n\r\n    if (router.query && router.query.country) {\r\n      operationParams.query[\"country\"] = router.query.country;\r\n    }\r\n\r\n    // Handle selected regions similarly as in `getOperationsData()`\r\n    if (selectedRegions === null) {\r\n      delete operationParams.query[\"world_region\"];\r\n    } else if (selectedRegions.length === 0) {\r\n      operationParams.query[\"world_region\"] = [\"__NO_MATCH__\"];\r\n    } else {\r\n      operationParams.query[\"world_region\"] = selectedRegions;\r\n    }\r\n\r\n    filterStatus &&\r\n      (operationParams.query = {\r\n        ...operationParams.query,\r\n        status: filterStatus,\r\n      });\r\n\r\n    pageSort && (operationParams.sort = pageSort.sort);\r\n\r\n    const response = await apiService.get(\"/operation\", operationParams);\r\n    if (response && Array.isArray(response.data)) {\r\n      setDataToTable(response.data);\r\n      setOperations(response.data);\r\n      setPerPage(newPerPage);\r\n      setLoading(false);\r\n    }\r\n    setPageNum(page);\r\n  };\r\n\r\n\r\n  useEffect(() => {\r\n    opParams.page = 1;\r\n    getOperationsData(opParams);\r\n  }, [selectedRegions, router]);\r\n\r\n  useEffect(() => {\r\n    getOperationsData(opParams);\r\n  }, [opParams]);  // Make sure to watch `opParams` for changes\r\n\r\n\r\n  const handleSort = async (column: any, sortDirection: any) => {\r\n    setLoading(true);\r\n    operationParams.sort = {\r\n      [column.selector]: sortDirection,\r\n    };\r\n    filterStatus && (operationParams.query = { ...operationParams.query, status: filterStatus });\r\n    filterText !== \"\" && (operationParams.query = { ...operationParams.query, title: filterText });\r\n\r\n    await getOperationsData(operationParams);\r\n    setPageSort(operationParams);\r\n    setLoading(false);\r\n  };\r\n\r\n  const sendQuery = (q: any, page: any) => {\r\n    if (q) {\r\n      opParams.query[\"title\"] = q;\r\n      opParams.page = page;\r\n      setOpParams({ ...opParams });\r\n    } else {\r\n      delete opParams.query.title;\r\n      setOpParams({ ...opParams });\r\n    }\r\n  };\r\n\r\n  const handleSearchTitle = useRef(\r\n    _.debounce((q, page) => sendQuery(q, page), Number(process.env.SEARCH_DEBOUNCE_TIME) || 300)\r\n  ).current;\r\n\r\n  const subHeaderComponentMemo = useMemo(() => {\r\n    const handleClear = () => {\r\n      if (filterText) {\r\n        setResetPaginationToggle(!resetPaginationToggle);\r\n        setFilterText(\"\");\r\n      }\r\n    };\r\n\r\n    const handleFilterStatusChange = (status: any) => {\r\n      setFilterStatus(status);\r\n      if (status) {\r\n        opParams.query[\"status\"] = status;\r\n        opParams.page = pageNum;\r\n        setOpParams({ ...opParams });\r\n      } else {\r\n        delete opParams.query.status;\r\n        setOpParams({ ...opParams });\r\n      }\r\n    };\r\n\r\n    const handleChange = (e: any) => {\r\n      setFilterText(e.target.value);\r\n      handleSearchTitle(e.target.value, pageNum);\r\n    };\r\n\r\n    return (\r\n      <OperationsTableFilter\r\n        onFilter={handleChange}\r\n        onFilterStatusChange={(e: any) => handleFilterStatusChange(e.target.value)}\r\n        onClear={handleClear}\r\n        filterText={filterText}\r\n        filterStatus={filterStatus}\r\n      />\r\n    );\r\n  }, [filterText, filterStatus, resetPaginationToggle, selectedRegions]);\r\n  return (\r\n    <RKITable\r\n      columns={columns}\r\n      data={tabledata}\r\n      totalRows={totalRows}\r\n      loading={loading}\r\n      subheader\r\n      persistTableHead\r\n      onSort={handleSort}\r\n      sortServer\r\n      pagServer={true}\r\n      resetPaginationToggle={resetPaginationToggle}\r\n      subHeaderComponent={subHeaderComponentMemo}\r\n      handlePerRowsChange={handlePerRowsChange}\r\n      handlePageChange={handlePageChange}\r\n    />\r\n  );\r\n}\r\n\r\nexport default OperationsTable;\r\n"], "names": ["props", "i18n", "useTranslation", "ListMapContainer", "currentLang", "language", "operations", "points", "setPoints", "useState", "activeMarker", "set<PERSON><PERSON><PERSON><PERSON>", "markerInfo", "setMarkerInfo", "groupedOperations", "setGroupedOperations", "reset<PERSON><PERSON><PERSON>", "onMarkerClick", "propsinit", "marker", "e", "name", "id", "countryId", "setPointersFromOperations", "filteroperationPoints", "_", "push", "title", "operation", "_id", "lat", "country", "coordinates", "parseFloat", "latitude", "lng", "longitude", "useEffect", "length", "groupByCountriesOperation", "RKIMAP1", "onClose", "info", "Markerprops", "ul", "map", "item", "index", "li", "a", "href", "MarkerInfo", "R<PERSON>IMapMarker", "icon", "url", "onClick", "position", "type", "draggable", "<PERSON><PERSON>", "handleClick", "getPosition", "canAddOperation", "connectedAuthWrapper", "authenticatedSelector", "state", "permissions", "wrapperDisplayName", "FailureComponent", "R403", "user", "update", "filterText", "OperationsTableFilter", "onFilter", "onFilterStatusChange", "onClear", "filterStatus", "status", "setStatus", "t", "getOperationsStatus", "operationParams", "response", "apiService", "get", "Array", "isArray", "data", "query", "sort", "Container", "fluid", "className", "Row", "Col", "xs", "FormControl", "placeholder", "aria-label", "value", "onChange", "Form", "Group", "as", "controlId", "Label", "column", "sm", "lg", "option", "RKITable", "paginationComponentOptions", "rowsPerPageText", "columns", "totalRows", "resetPaginationToggle", "subheader", "subHeaderComponent", "handlePerRowsChange", "handlePageChange", "rowsPerPage", "defaultRowsPerPage", "selectableRows", "loading", "pagServer", "onSelectedRowsChange", "clearSelectedRows", "sortServer", "onSort", "persistTableHead", "sortFunction", "otherProps", "dataTableProps", "noDataComponent", "<PERSON><PERSON><PERSON><PERSON>", "dense", "paginationResetDefaultPage", "subHeader", "progressPending", "pagination", "paginationServer", "paginationPerPage", "paginationRowsPerPageOptions", "paginationTotalRows", "onChangeRowsPerPage", "onChangePage", "progressComponent", "CustomLoader", "sortIcon", "i", "DataTable", "defaultProps", "RKIMapInfowindow", "onCloseClick", "InfoWindow", "div", "children", "fill", "stoke", "road", "geometry", "mapStyles", "RKIMap1", "initialCenter", "height", "width", "zoom", "minZoom", "locale", "useRouter", "isLoaded", "loadError", "useGoogleMaps", "style", "GoogleMap", "mapContainerStyle", "center", "defaultCenter", "onLoad", "onMapLoad", "setOptions", "styles", "options", "keyboardShortcuts", "streetViewControl", "panControl", "clickableIcons", "mapTypeControl", "fullscreenControl", "console", "log", "RegionsMultiCheckboxes", "filtreg", "allregions", "setAllregions", "region", "setRegion", "selectedRegions", "setSelectedRegions", "RegionParams", "getworldregion", "RegionParams_initial", "finalRegions", "selectedIds", "regionItem", "isChecked", "handleIndividualRegionChange", "updatedRegions", "updatedSelectedRegions", "for<PERSON>ach", "code", "target", "checked", "filter", "n", "Check", "label", "handleAllChecked", "event", "selected_Regions", "<PERSON><PERSON>", "resetAllRegion", "PageHeading", "h2", "Operation", "setOperations", "AddOperationComponent", "Link", "variant", "size", "CanAddOperation", "regionHandler", "val", "OperationsTable", "PartnersLink", "partners", "institution", "router", "setFilterText", "setFilterStatus", "setResetPaginationToggle", "tabledata", "setDataToTable", "setLoading", "setTotalRows", "perPage", "setPerPage", "pageNum", "setPageNum", "pageSort", "setPageSort", "created_at", "lean", "populate", "path", "select", "limit", "page", "opParams", "setOpParams", "selector", "sortable", "cell", "d", "start_date", "moment", "format", "getOperationsData", "operationParamsinit", "totalCount", "newPerPage", "handleSort", "sortDirection", "<PERSON><PERSON><PERSON><PERSON>", "q", "handleSearchTitle", "useRef", "Number", "process", "current", "subHeaderComponentMemo", "useMemo", "handleFilterStatusChange", "handleChange", "handleClear"], "sourceRoot": "", "ignoreList": []}