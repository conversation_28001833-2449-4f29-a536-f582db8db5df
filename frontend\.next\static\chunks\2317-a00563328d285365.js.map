{"version": 3, "file": "static/chunks/2317-a00563328d285365.js", "mappings": "uPAwFA,MA3DgCA,IAC9B,GAAM,GAAEC,CAAC,CAAE,CAAGC,CAAAA,EAAAA,EAAAA,EAAAA,CAAcA,CAAC,MA0DhBC,IAzDP,CAACC,EAASC,EAAW,CAAGC,CAAAA,EAAAA,EAAAA,OAyDKH,CAzDLG,CAAQA,EAAC,GACjCC,EAAsB,SAgBMP,EAfhC,MACE,WAACQ,EAAAA,CAASA,CAACC,IAAI,EAACC,SAAS,cACvB,WAACF,EAAAA,CAASA,CAACG,MAAM,EAACC,QAAS,IAAMP,EAAW,CAACD,aAC3C,UAACS,MAAAA,CAAIC,UAAU,qBAAab,EAAE,iBAC9B,UAACY,MAAAA,CAAIC,UAAU,qBACZV,EACC,UAACW,EAAAA,CAAeA,CAAAA,CAACC,KAAMC,EAAAA,GAAOA,CAAEC,MAAM,SAEtC,UAACH,EAAAA,CAAeA,CAAAA,CAACC,KAAMG,EAAAA,GAAMA,CAAED,MAAM,cAI3C,UAACV,EAAAA,CAASA,CAACY,IAAI,WACb,UAACC,EAAAA,CAAUA,CAAAA,CACTC,KAAK,SACLC,GAAIvB,CAAAA,QAAAA,KAAAA,EAAAA,EAAOwB,CAAPxB,QAAOwB,UAAaxB,GAAAA,OAAAA,EAAAA,EAAOwB,IAAPxB,KAAOwB,EAAPxB,KAAAA,EAAAA,EAAkByB,GAAlBzB,GAAwB,EAAGA,EAAMwB,SAAS,CAACC,MAAM,CAAC,EAAE,CAAG,WAKzF,EAEMC,EAA0BC,CAAAA,EAAAA,EAAAA,OAAAA,CAAuBA,CAAC,IACtD,UAACpB,EAAAA,CAAAA,IAGD,MACI,iCACE,UAACC,EAAAA,CAASA,CAAAA,CAACM,UAAU,+BACnB,UAACc,EAAAA,OAAqBA,CAAAA,CAAE,GAAG5B,CAAK,KAGlC,UAACQ,EAAAA,CAASA,CAAAA,CAACM,UAAU,+BACnB,UAACe,EAAAA,OAAiBA,CAAAA,CAAE,GAAG7B,CAAK,KAG9B,UAACQ,EAAAA,CAASA,CAAAA,CAACM,UAAU,+BACnB,UAACY,EAAAA,CAAAA,KAGH,UAAClB,EAAAA,CAASA,CAAAA,CAACM,UAAU,4BACnB,UAACgB,EAAAA,OAAqBA,CAAAA,CACpBC,QAAS/B,EAAMgC,uBAAuB,CAACD,OAAO,CAC9CE,SAAUjC,EAAMgC,uBAAuB,CAACC,QAAQ,CAChDC,eAAgBlC,EAAMgC,uBAAuB,CAACE,cAAc,CAC5DC,cAAenC,EAAMgC,uBAAuB,CAACG,aAAa,CAC1DC,oBAAqBpC,EAAMgC,uBAAuB,CAACI,mBAAmB,CACtEC,OAAQrC,EAAMgC,uBAAuB,CAACK,MAAM,CAC5Cb,UAAWxB,EAAMwB,SAAS,OAKxC,gICsBA,MAzFqBxB,IACnB,GAAM,GAAEC,CAAC,CAAE,CAAGC,CAAAA,EAAAA,EAAAA,EAAAA,CAAcA,CAAC,MAwFhBoC,IAvFP,MAAEhB,CAAI,EAuFa,EAvFXC,CAAE,CAAE,CAAGvB,EACf,CAACuC,EAAWC,EAAe,CAAGlC,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAC,EAAE,EACzC,CAACyB,EAASU,EAAW,CAAGnC,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAC,IACjC,CAACoC,EAAWC,EAAa,CAAGrC,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAC,GACrC,CAACsC,EAASC,EAAW,CAAGvC,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAC,IACjC,CAACwC,EAAsB,CAAGxC,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,EAAC,GAEnCyC,EAAe,CACnBC,KAAM,CAAEC,WAAY,KAAM,EAC1BC,MAAON,EACPO,KAAM,EACNC,MAAO,CAAC,CACV,EAEMC,EAAU,CACd,CACEC,KAAMrD,EAAE,SACRsD,SAAU,QACVC,KAAM,GAAYC,GAAKA,EAAEC,KAAK,EAAID,EAAEE,GAAG,CAAG,UAACC,IAAIA,CAACC,KAAK,sBAAsBC,GAAI,cAAhCF,EAAsD,OAANH,EAAEE,GAAG,WAAMF,EAAEC,KAAK,GAAW,EAC9H,EACA,CACEJ,KAAMrD,EAAE,SACRsD,SAAU,QACVC,KAAOC,GAAWA,GAAKA,EAAEM,IAAI,EAAIN,EAAEM,IAAI,CAACC,SAAS,CAAG,GAAuBP,MAAAA,CAApBA,EAAEM,IAAI,CAACC,SAAS,CAAC,KAAmB,OAAhBP,EAAEM,IAAI,CAACE,QAAQ,EAAK,EACjG,EACA,CACEX,KAAMrD,EAAE,iBACRsD,SAAU,aACVC,KAAM,GAAYC,GAAKA,EAAES,UAAU,CAAG,SAAW,SAEnD,EACA,CACEZ,KAAMrD,EAAE,mBACRsD,SAAU,UACVC,KAAM,GAAYC,GAAKA,EAAEU,OAAO,CAAGV,EAAEU,OAAO,CAACC,MAAM,CAAG,GACxD,EACD,CAEKC,EAAkB,MAAOC,IAC7B7B,GAAW,GACX,IAAM8B,EAAW,MAAMC,EAAAA,CAAUA,CAACC,GAAG,CAAC,YAA8BlD,MAAAA,CAAlBD,EAAK,eAAgB,OAAHC,GAAMwB,GACtEwB,IACO,MADG,QACZjD,EAAuBkB,EAAe+B,EAASG,SAAS,EAAIlC,EAAe+B,EAASI,OAAO,EAC3FhC,EAAa4B,EAASK,UAAU,EAChCnC,GAAW,GAEf,EAQMoC,EAAsB,MAAOC,EAAoB3B,KACrDJ,EAAaG,KAAK,CAAG4B,EACrB/B,EAAaI,IAAI,CAAGA,EACpBV,GAAW,GACX,IAAM8B,EAAW,MAAMC,EAAAA,CAAUA,CAACC,GAAG,CAAC,YAA8BlD,MAAAA,CAAlBD,EAAK,eAAgB,OAAHC,GAAMwB,GACtEwB,IACFjD,MADY,UACWkB,EAAe+B,EAASG,SAAS,EAAIlC,EAAe+B,EAASI,OAAO,EAC3F9B,EAAWiC,GACXrC,GAAW,GAEf,EAQA,MANAsC,CAAAA,EAAAA,EAAAA,SAAAA,CAASA,CAAC,KACRV,EAAgBtB,EAClB,EAAG,EAAE,EAKH,UAAClC,MAAAA,UACC,UAACmE,EAAAA,CAAQA,CAAAA,CACP3B,QAASA,EACT4B,KAAM1C,EACNG,UAAWA,EACXX,QAASA,EACTe,sBAAuBA,EACvB+B,oBAAqBA,EACrBK,iBAjCmB,CAiCDA,GAhCtBnC,EAAaG,KAAK,CAAGN,EACrBG,EAAaI,IAAI,CAAGA,EACpBkB,EAAgBtB,EAClB,KAiCF,6GCrEA,SAASiC,EAAShF,CAAoB,EACpC,GAAM,GAAEC,CAAC,CAAE,CAAGC,CAAAA,EAAAA,EAAAA,EAAAA,CAAcA,CAAC,UACvBiF,EAA6B,CACjCC,gBAAiBnF,EAAE,cACnB,EACI,SACJoD,CAAO,MACP4B,CAAI,WACJvC,CAAS,uBACTI,CAAqB,CACrBuC,WAAS,oBACTC,CAAkB,qBAClBT,CAAmB,kBACnBK,CAAgB,aAChBK,CAAW,CACXC,oBAAkB,CAClBC,gBAAc,SACd1D,CAAO,WACP2D,CAAS,sBACTC,CAAoB,CACpBC,mBAAiB,CACjBC,YAAU,QACVC,CAAM,kBACNC,CAAgB,cAChBC,CAAY,CAEZ,CADA,EACGC,EACJ,CAAGjG,EAGEkG,EAAiB,4BACrBf,EACAgB,gBAAiBlG,EAAE,IAP0C,MAQ7DmG,UAAU,EACV/C,UACA4B,KAAMA,GAAQ,EAAE,CAChBoB,OAAO,EACPC,2BAA4BxD,EAC5ByD,UAAWlB,EACXmB,gBAAiBzE,qBACjBuD,EACAmB,WAAY,GACZC,iBAAkBhB,EAClBiB,kBAAmBnB,GAA0C,GAC7DoB,eADwCpB,cACVD,GAA4B,CAAC,GAAI,GAAI,GAAI,CAA3BA,EAA+B,GAAG,CAC9EsB,oBAAqBnE,EACrBoE,oBAAqBjC,EACrBkC,aAAc7B,iBACdO,uBACAE,oBACAC,EACAoB,kBAAmB,UAACC,EAAAA,CAAYA,CAAAA,CAAAA,GAChCC,SAAU,UAACC,IAAAA,CAAErG,UAAU,6CACvB+E,SACAC,eACAE,mBACAD,EACAjF,UAAW,WACb,EACA,MACE,UAACsG,EAAAA,EAASA,CAAAA,CAAE,GAAGlB,CAAc,EAEjC,CAEAlB,EAASqC,YAAY,CAAG,CACtBd,WAAW,EACXE,YAAY,EACZ/D,UAAW,KACXgD,WAAW,EACXC,qBAAsB,KACtBC,kBAAmB,GACnBC,WAAY,GACZE,kBAAkB,CACpB,EAEA,MAAef,QAAQA,EAAC,qIClExB,MApC0B,IACtB,GAAM,GAAE/E,CAAC,CAAE,CAAGC,CAAAA,EAAAA,EAAAA,EAAAA,CAAcA,CAAC,MAmClB2B,IAlCL,CAACzB,EAASC,EAAW,CAAGC,CAAAA,EAAAA,EAAAA,EAkCFuB,EAAC,IAlCCvB,CAAQA,EAAC,GACvC,MACI,+BACI,WAACE,EAAAA,CAASA,CAACC,IAAI,EAACC,SAAS,cACvB,WAACF,EAAAA,CAASA,CAACG,MAAM,EAACC,QAAS,IAAMP,EAAW,CAACD,aAC3C,UAACS,MAAAA,CAAIC,UAAU,qBAAab,EAAE,eAC9B,UAACY,MAAAA,CAAIC,UAAU,qBACZV,EACC,UAACW,EAAAA,CAAeA,CAAAA,CAACC,KAAMC,EAAAA,GAAOA,CAAEC,MAAM,SAEtC,UAACH,EAAAA,CAAeA,CAAAA,CAACC,KAAMG,EAAAA,GAAMA,CAAED,MAAM,cAI3C,WAACV,EAAAA,CAASA,CAACY,IAAI,YACb,UAACkG,EAAAA,CAAaA,CAAAA,CACZvF,QAAS/B,EAAMgC,uBAAuB,CAACD,OAAO,CAC9CwF,UAAWvH,EAAMgC,uBAAuB,CAACG,aAAa,CACtDqF,KAAMxH,EAAMgC,uBAAuB,CAACC,QAAQ,EAAI,EAAE,CAClDwF,gBAAiBzH,EAAMgC,uBAAuB,CAACK,MAAM,GAEvD,UAACqF,KAAAA,CAAG5G,UAAU,gBAAQb,EAAE,0BACxB,UAACqH,EAAAA,CAAaA,CAAAA,CACZvF,QAAS/B,EAAMgC,uBAAuB,CAACD,OAAO,CAC9CwF,UAAWvH,EAAMgC,uBAAuB,CAACI,mBAAmB,CAC5DoF,KAAMxH,EAAMgC,uBAAuB,CAACE,cAAc,EAAI,EAAE,CACxDuF,gBAAiBzH,EAAMgC,uBAAuB,CAACK,MAAM,UAMvE,uHCyBA,MAtDoD,OAAC,MAAEmF,CAAI,cAsD5CF,GAtD8CG,CAAe,SAsDhDH,EAtDkDC,CAAS,CAAExF,SAAO,CAAE,GAE1F4F,EAAa,MAAOC,EAAaC,KAKrCN,EAJiB,CACfO,OAGQC,QAHQH,EAAOrE,QAAQ,CAC/BsE,cAAeA,CACjB,EAEF,EAEM,GAAE5H,CAAC,CAAE,CAAGC,CAAAA,EAAAA,EAAAA,EAAAA,CAAcA,CAAC,UAGvBmD,EAAU,CACd,CACEC,KAAMrD,EAAE,YACR+H,MAAO,MACPzE,SAAU,YACVC,KAAM,GAAYC,GAAKA,EAAEwE,SAAS,EAAIxE,EAAEwE,SAC1C,EACA,CACE3E,KAAMrD,EAAE,YACR+H,MAAO,MACPzE,SAAU,iBACVC,KAAM,GAAYC,GAAKA,EAAEyE,aAAa,EAAI,UAACC,IAAAA,CAAEtE,KAAM,GAA4CJ,MAAAA,CAAzC2E,8BAAsB,CAAC,oBAAwB,OAAN3E,EAAEE,GAAG,EAAI0E,OAAO,kBAAU5E,EAAEyE,aAAa,CAACI,KAAK,CAAC,KAAKC,KAAK,CAAC,EAAG,CAAC,GAAGC,IAAI,CAAC,OACtKC,UAAU,CACZ,EACA,CACEnF,KAAMrD,EAAE,eACRsD,SAAU,cACVC,KAAOC,GAAWA,GAAKA,EAAEiF,WAAW,EAAIjF,EAAEiF,WAAW,EAEvD,CACEpF,KAAMrD,EAAE,gBACR+H,MAAO,MACPzE,SAAU,iBACVC,KAAM,GAAYC,GAAKA,EAAEkF,UAAU,EAAIC,IAAOnF,EAAEkF,UAAU,EAAEE,MAAM,CAAC,cACnEJ,MAD6CG,IACnC,CACZ,EACD,CAED,MACE,UAAC5D,EAAAA,CAAQA,CAAAA,CACP3B,QAASA,EACT4B,KAAMuC,EACN9B,WAAW,EACXI,OAAQ6B,EACR5B,gBAAgB,IAChBhE,QAASA,GAIf,+ICbA,MAhC8B,QAkBR/B,EAAAA,EAjBlB,GAAM,CAAEC,GAAC,CAAE,CAAGC,CAAAA,EAAAA,EAAAA,EAAAA,CAAcA,CAAC,UACvB,CAACE,EAASC,EAAW,CAAGC,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,EAAC,GACvC,MACI,+BACG,WAACE,EAAAA,CAASA,CAACC,IAAI,EAACC,SAAS,cACtB,WAACF,EAAAA,CAASA,CAACG,MAAM,EAACC,QAAS,IAAMP,EAAW,CAACD,aAC3C,UAACS,MAAAA,CAAIC,UAAU,qBAAab,EAAE,wBAC9B,UAACY,MAAAA,CAAIC,UAAU,qBACZV,EACC,UAACW,EAAAA,CAAeA,CAAAA,CAACC,KAAMC,EAAAA,GAAOA,CAAEC,MAAM,SAEtC,UAACH,EAAAA,CAAeA,CAAAA,CAACC,KAAMG,EAAAA,GAAMA,CAAED,MAAM,cAI3C,UAACV,EAAAA,CAASA,CAACY,IAAI,WACb,UAACkB,EAAAA,CAAWA,CAAAA,CACVf,GAAIvB,OAAAA,GAAAA,EAAMwB,SAAAA,GAANxB,OAAAA,EAAAA,EAAiByB,MAAAA,EAAjBzB,KAAAA,EAAAA,CAAyB,CAAC,EAAE,CAA5BA,EAAgC,GACpCsB,KAAK,UACLwH,WAAY,EAAE,CACdC,mBAAmB,EACnBC,oBAAqB,EACrBC,kBAAmB,GACnBC,sBAAuB,UAMzC,+ICbA,MAxB8B,IAC1B,GAAM,CAAEjJ,GAAC,CAAE,CAAGC,CAAAA,EAAAA,EAAAA,EAAAA,CAAcA,CAAC,MAuBlB0B,IAtBL,CAACxB,EAASC,EAAW,CAAGC,CAAAA,EAAAA,EAAAA,MAsBEsB,EAtBFtB,CAAQA,CAAC,IACvC,MACI,+BACI,WAACE,EAAAA,CAASA,CAACC,IAAI,EAACC,SAAS,cACvB,WAACF,EAAAA,CAASA,CAACG,MAAM,EAACC,QAAS,IAAMP,EAAW,CAACD,aAC3C,UAACS,MAAAA,CAAIC,UAAU,qBAAab,EAAE,kBAC9B,UAACY,MAAAA,CAAIC,UAAU,qBACZV,EACC,UAACW,EAAAA,CAAeA,CAAAA,CAACC,KAAMC,EAAAA,GAAOA,CAAEC,MAAM,SAEtC,UAACH,EAAAA,CAAeA,CAAAA,CAACC,KAAMG,EAAAA,GAAMA,CAAED,MAAM,cAI3C,UAACV,EAAAA,CAASA,CAACY,IAAI,WACb,UAAC+H,EAAAA,CAAWA,CAAAA,CAACC,QAASpJ,EAAMqJ,MAAM,CAAEC,YAAatJ,EAAMuJ,MAAM,SAK7E,yBC1BC,aAA4B,aAI7B,oBACA,OACA,iCACA,iCACA,0BACA,0BACA,+BACA,8BACA,8BACA,4BACA,6BAEA,yBAGA,qBACA,qGACA,KAEA,YACA,2EACA,oBACA,SACA,oEACA,KAEA,uDACA,8CACA,sBACA,gBACA,WACA,eACA,eACA,kBACA,yBACA,+BACA,CAAS,CACT,UACA,8BACA,aACA,+BACA,8BACA,gCACA,uCACA,CAAS,CACT,cACA,eACA,cACA,sBACA,iBACA,IACA,gBACA,IACA,gBACA,IACA,KACA,IACA,eACA,IACA,KACA,IACA,IACA,CAAS,CACT,2BAAoC,IAAI,IACxC,cACA,MACA,MACA,KACA,CAAS,CACJ,EAIL,CAAC,CAhFiD,EAAQ,KAAW,YAAZ,iFCLlD,IAAM5H,EAA0B6H,CAAAA,EAAAA,QAAAA,CAAAA,CAAoBA,CAAC,CAC1DC,sBAAuB,KACjBC,EAAMC,WAAW,IAAID,EAAMC,WAAW,CAACC,MAAM,IAAIF,EAAMC,WAAW,CAACC,MAAM,CAAC,WAAW,CAK3FC,CAL6F,kBAKzE,yBACtB,GAAG,EAEYlI", "sources": ["webpack://_N_E/./pages/hazard/HazardAccordianSection.tsx", "webpack://_N_E/./components/common/VspaceTable.tsx", "webpack://_N_E/./components/common/RKITable.tsx", "webpack://_N_E/./pages/hazard/DocumentAccordian.tsx", "webpack://_N_E/./components/common/DocumentTable.tsx", "webpack://_N_E/./pages/hazard/VirtualSpaceAccordian.tsx", "webpack://_N_E/./pages/hazard/MediaGalleryAccordion.tsx", "webpack://_N_E/./node_modules/moment/locale/de.js", "webpack://_N_E/./pages/hazard/permission.tsx"], "sourcesContent": ["//Import Library\r\nimport { Accordion, Card } from \"react-bootstrap\";\r\nimport DocumentAccordian from \"./DocumentAccordian\";\r\nimport { faPlus, faMinus } from \"@fortawesome/free-solid-svg-icons\";\r\nimport { FontAwesomeIcon } from \"@fortawesome/react-fontawesome\";\r\nimport { useState } from \"react\";\r\n\r\n//Import services/components\r\nimport Discussion from \"../../components/common/disussion\";\r\nimport { useTranslation } from 'next-i18next';\r\nimport canViewDiscussionUpdate from \"./permission\";\r\nimport VirtualSpaceAccordian from \"./VirtualSpaceAccordian\";\r\nimport MediaGalleryAccordion from \"./MediaGalleryAccordion\";\r\n\r\ninterface HazardAccordianSectionProps {\r\n  t: (key: string) => string;\r\n  images: any[];\r\n  imgSrc: string[];\r\n  routeData: any;\r\n  documentAccoirdianProps: {\r\n    loading: boolean;\r\n    Document: any[];\r\n    updateDocument: any[];\r\n    hazardDocSort: (data: { columnSelector: string; sortDirection: string }) => void;\r\n    hazardDocUpdateSort: (data: { columnSelector: string; sortDirection: string }) => void;\r\n    docSrc: string[];\r\n  };\r\n}\r\n\r\nconst HazardAccordianSection = (props: HazardAccordianSectionProps) => {\r\n  const { t } = useTranslation('common');\r\n  const [section, setSection] = useState(false);\r\n  const DiscussionComponent = () => {\r\n    return (\r\n      <Accordion.Item eventKey=\"1\">\r\n        <Accordion.Header onClick={() => setSection(!section)}>\r\n          <div className=\"cardTitle\">{t(\"discussions\")}</div>\r\n          <div className=\"cardArrow\">\r\n            {section ? (\r\n              <FontAwesomeIcon icon={faMinus} color=\"#fff\" />\r\n            ) : (\r\n              <FontAwesomeIcon icon={faPlus} color=\"#fff\" />\r\n            )}\r\n          </div>\r\n        </Accordion.Header>\r\n        <Accordion.Body>\r\n          <Discussion\r\n            type=\"hazard\"\r\n            id={props?.routeData && props?.routeData?.routes ? props.routeData.routes[1] : null}\r\n          />\r\n        </Accordion.Body>\r\n      </Accordion.Item>\r\n    );\r\n  };\r\n\r\n  const CanViewDiscussionUpdate = canViewDiscussionUpdate(() => (\r\n    <DiscussionComponent />\r\n  ));\r\n\r\n    return (\r\n        <>\r\n          <Accordion className=\"countryAccordionNew\">\r\n            <MediaGalleryAccordion {...props} />\r\n          </Accordion>\r\n\r\n          <Accordion className=\"countryAccordionNew\">\r\n            <DocumentAccordian {...props} />\r\n          </Accordion>\r\n\r\n          <Accordion className=\"countryAccordionNew\">\r\n            <CanViewDiscussionUpdate />\r\n          </Accordion>\r\n\r\n          <Accordion className=\"countryAccordion\">\r\n            <VirtualSpaceAccordian\r\n              loading={props.documentAccoirdianProps.loading}\r\n              Document={props.documentAccoirdianProps.Document}\r\n              updateDocument={props.documentAccoirdianProps.updateDocument}\r\n              hazardDocSort={props.documentAccoirdianProps.hazardDocSort}\r\n              hazardDocUpdateSort={props.documentAccoirdianProps.hazardDocUpdateSort}\r\n              docSrc={props.documentAccoirdianProps.docSrc}\r\n              routeData={props.routeData}\r\n            />\r\n          </Accordion>\r\n        </>\r\n    )\r\n}\r\n\r\nexport default HazardAccordianSection;", "//Import Library\r\nimport { useEffect, useState } from \"react\";\r\nimport Link from 'next/link';\r\n\r\n//Import services/components\r\nimport RKITable from \"../../components/common/RKITable\";\r\nimport apiService from \"../../services/apiService\";\r\nimport { useTranslation } from 'next-i18next';\r\n\r\ninterface VspaceTableProps {\r\n  vspaceData: any;\r\n  vspaceDataLoading: boolean;\r\n  vspaceDataTotalRows: number;\r\n  vspaceDataPerPage: number;\r\n  vspaceDataCurrentPage: number;\r\n  type: string;\r\n  id: string;\r\n}\r\n\r\nconst VspaceTable = (props: VspaceTableProps) => {\r\n  const { t } = useTranslation('common');\r\n  const { type, id } = props;\r\n  const [tabledata, setDataToTable] = useState([]);\r\n  const [loading, setLoading] = useState(false);\r\n  const [totalRows, setTotalRows] = useState(0);\r\n  const [perPage, setPerPage] = useState(10);\r\n  const [resetPaginationToggle] = useState(false);\r\n\r\n  const vSpaceParams = {\r\n    sort: { created_at: \"asc\" },\r\n    limit: perPage,\r\n    page: 1,\r\n    query: {},\r\n  };\r\n\r\n  const columns = [\r\n    {\r\n      name: t(\"Title\"),\r\n      selector: \"title\",\r\n      cell: (d: any) => d && d.title && d._id ? <Link href=\"/vspace/[...routes]\" as={`/vspace/show/${d._id}`} >{d.title}</Link> : \"\",\r\n    },\r\n    {\r\n      name: t(\"Owner\"),\r\n      selector: \"users\",\r\n      cell: (d: any) => d && d.user && d.user.firstname ? `${d.user.firstname} ${d.user.lastname}` : \"\"\r\n    },\r\n    {\r\n      name: t(\"PublicPrivate\"),\r\n      selector: \"visibility\",\r\n      cell: (d: any) => d && d.visibility ? \"Public\" : \"Private\",\r\n\r\n    },\r\n    {\r\n      name: t(\"NumberofMembers\"),\r\n      selector: \"members\",\r\n      cell: (d: any) => d && d.members ? d.members.length : \"-\",\r\n    }\r\n  ];\r\n\r\n  const getLinkedVspace = async (vSpaceParams1: any) => {\r\n    setLoading(true);\r\n    const response = await apiService.get(`stats/get${type}WithVspace/${id}`, vSpaceParams);\r\n    if (response) {\r\n      type === \"Operation\" ? setDataToTable(response.operation) : setDataToTable(response.project);\r\n      setTotalRows(response.totalCount);\r\n      setLoading(false);\r\n    }\r\n  };\r\n\r\n  const handlePageChange = (page: number) => {\r\n    vSpaceParams.limit = perPage;\r\n    vSpaceParams.page = page;\r\n    getLinkedVspace(vSpaceParams);\r\n  };\r\n\r\n  const handlePerRowsChange = async (newPerPage: number, page: number) => {\r\n    vSpaceParams.limit = newPerPage;\r\n    vSpaceParams.page = page;\r\n    setLoading(true);\r\n    const response = await apiService.get(`stats/get${type}WithVspace/${id}`, vSpaceParams);\r\n    if (response) {\r\n      type === \"Operation\" ? setDataToTable(response.operation) : setDataToTable(response.project);\r\n      setPerPage(newPerPage);\r\n      setLoading(false);\r\n    }\r\n  };\r\n\r\n  useEffect(() => {\r\n    getLinkedVspace(vSpaceParams);\r\n  }, []);\r\n\r\n\r\n\r\n  return (\r\n    <div>\r\n      <RKITable\r\n        columns={columns}\r\n        data={tabledata}\r\n        totalRows={totalRows}\r\n        loading={loading}\r\n        resetPaginationToggle={resetPaginationToggle}\r\n        handlePerRowsChange={handlePerRowsChange}\r\n        handlePageChange={handlePageChange}\r\n      />\r\n    </div>\r\n  );\r\n}\r\n\r\nexport default VspaceTable;\r\n", "//Import Library\r\nimport React from 'react'\r\nimport DataTable  from 'react-data-table-component';\r\n\r\n//Import services/components\r\nimport { useTranslation } from 'next-i18next';\r\nimport CustomLoader from './CustomLoader';\r\n\r\ninterface RKITableProps {\r\n  columns: any[];\r\n  data: any[];\r\n  totalRows?: number;\r\n  resetPaginationToggle?: boolean;\r\n  subheader?: boolean;\r\n  subHeaderComponent?: React.ReactNode;\r\n  handlePerRowsChange?: (newPerPage: number, page: number) => void;\r\n  handlePageChange?: (page: number) => void;\r\n  rowsPerPage?: number[];\r\n  defaultRowsPerPage?: number;\r\n  selectableRows?: boolean;\r\n  loading?: boolean;\r\n  pagServer?: boolean;\r\n  onSelectedRowsChange?: (selectedRows: any) => void;\r\n  clearSelectedRows?: boolean;\r\n  sortServer?: boolean;\r\n  onSort?: (column: any, sortDirection: string) => void;\r\n  persistTableHead?: boolean;\r\n  sortFunction?: (rows: any[], field: any, direction: 'asc' | 'desc') => any[];\r\n  noHeader?: boolean;\r\n  dense?: boolean;\r\n  pagination?: boolean;\r\n  paginationServer?: boolean;\r\n  paginationTotalRows?: number;\r\n  subHeaderAlign?: string;\r\n  [key: string]: any;\r\n}\r\n\r\nfunction RKITable(props: RKITableProps) {\r\n  const { t } = useTranslation('common');\r\n  const paginationComponentOptions = {\r\n    rowsPerPageText: t('Rowsperpage'),\r\n    };\r\n  const {\r\n    columns,\r\n    data,\r\n    totalRows,\r\n    resetPaginationToggle,\r\n    subheader,\r\n    subHeaderComponent,\r\n    handlePerRowsChange,\r\n    handlePageChange,\r\n    rowsPerPage,\r\n    defaultRowsPerPage,\r\n    selectableRows,\r\n    loading,\r\n    pagServer,\r\n    onSelectedRowsChange,\r\n    clearSelectedRows,\r\n    sortServer,\r\n    onSort,\r\n    persistTableHead,\r\n    sortFunction,\r\n    // Filter out any unknown props that might cause DOM warnings\r\n    ...otherProps\r\n  } = props;\r\n\r\n  // Only pass known DataTable props to avoid DOM warnings\r\n  const dataTableProps = {\r\n    paginationComponentOptions,\r\n    noDataComponent: t(\"NoData\"),\r\n    noHeader: true,\r\n    columns,\r\n    data: data || [],\r\n    dense: true,\r\n    paginationResetDefaultPage: resetPaginationToggle,\r\n    subHeader: subheader,\r\n    progressPending: loading,\r\n    subHeaderComponent,\r\n    pagination: true,\r\n    paginationServer: pagServer,\r\n    paginationPerPage: defaultRowsPerPage ? defaultRowsPerPage : 10,\r\n    paginationRowsPerPageOptions: rowsPerPage ? rowsPerPage : [10, 15, 20, 25, 30],\r\n    paginationTotalRows: totalRows,\r\n    onChangeRowsPerPage: handlePerRowsChange,\r\n    onChangePage: handlePageChange,\r\n    selectableRows,\r\n    onSelectedRowsChange,\r\n    clearSelectedRows,\r\n    progressComponent: <CustomLoader/>,\r\n    sortIcon: <i className=\"sort-icon fas fa-exchange-alt\" />,\r\n    sortServer,\r\n    onSort,\r\n    sortFunction,\r\n    persistTableHead,\r\n    className: \"rki-table\"\r\n  };\r\n  return (\r\n    <DataTable {...dataTableProps} />\r\n  )\r\n};\r\n\r\nRKITable.defaultProps = {\r\n  subHeader: false,\r\n  pagination: true,\r\n  totalRows: null,\r\n  pagServer: true,\r\n  onSelectedRowsChange: null,\r\n  clearSelectedRows: false,\r\n  sortServer: false,\r\n  persistTableHead: false\r\n}\r\n\r\nexport default RKITable;\r\n\r\n", "//Import Library\r\nimport { faMinus, faPlus } from \"@fortawesome/free-solid-svg-icons\";\r\nimport { FontAwesomeIcon } from \"@fortawesome/react-fontawesome\";\r\nimport { Card, Accordion } from \"react-bootstrap\";\r\nimport { useState } from \"react\";\r\n\r\n//Import services/components\r\nimport DocumentTable from \"../../components/common/DocumentTable\";\r\nimport { useTranslation } from 'next-i18next';\r\n\r\nconst DocumentAccordian = (props: any) => {\r\n    const { t } = useTranslation('common');\r\n    const [section, setSection] = useState(false);\r\n    return (\r\n        <>\r\n            <Accordion.Item eventKey=\"0\">\r\n              <Accordion.Header onClick={() => setSection(!section)}>\r\n                <div className=\"cardTitle\">{t(\"documents\")}</div>\r\n                <div className=\"cardArrow\">\r\n                  {section ? (\r\n                    <FontAwesomeIcon icon={faMinus} color=\"#fff\" />\r\n                  ) : (\r\n                    <FontAwesomeIcon icon={faPlus} color=\"#fff\" />\r\n                  )}\r\n                </div>\r\n              </Accordion.Header>\r\n              <Accordion.Body>\r\n                <DocumentTable\r\n                  loading={props.documentAccoirdianProps.loading}\r\n                  sortProps={props.documentAccoirdianProps.hazardDocSort}\r\n                  docs={props.documentAccoirdianProps.Document || []}\r\n                  docsDescription={props.documentAccoirdianProps.docSrc}\r\n                />\r\n                <h6 className=\"mt-3\">{t(\"DocumentsfromUpdates\")}</h6>\r\n                <DocumentTable\r\n                  loading={props.documentAccoirdianProps.loading}\r\n                  sortProps={props.documentAccoirdianProps.hazardDocUpdateSort}\r\n                  docs={props.documentAccoirdianProps.updateDocument || []}\r\n                  docsDescription={props.documentAccoirdianProps.docSrc}\r\n                />\r\n              </Accordion.Body>\r\n            </Accordion.Item>\r\n        </>\r\n    )\r\n};\r\n\r\nexport default DocumentAccordian;", "//Import Library\r\nimport React from \"react\";\r\nimport moment from \"moment\";\r\n\r\n//Import services/components\r\nimport RKITable from '../../components/common/RKITable';\r\nimport { useTranslation } from 'next-i18next';\r\n\r\ninterface DocumentTableProps {\r\n  docs: any[];\r\n  docsDescription: string;\r\n  sortProps: (sortObj: { columnSelector: string; sortDirection: string }) => void;\r\n  loading: boolean;\r\n}\r\n\r\nconst DocumentTable: React.FC<DocumentTableProps> = ({ docs, docsDescription, sortProps, loading }) => {\r\n\r\n  const handleSort = async (column: any, sortDirection: string) => {\r\n    const objSlect = {\r\n      columnSelector: column.selector,\r\n      sortDirection: sortDirection\r\n    }\r\n    sortProps(objSlect);\r\n  };\r\n\r\n  const { t } = useTranslation('common');\r\n\r\n\r\n  const columns = [\r\n    {\r\n      name: t(\"FileType\"),\r\n      width: \"15%\",\r\n      selector: 'extension',\r\n      cell: (d: any) => d && d.extension && d.extension,\r\n    },\r\n    {\r\n      name: t(\"FileName\"),\r\n      width: \"25%\",\r\n      selector: \"document_title\",\r\n      cell: (d: any) => d && d.original_name && <a href={`${process.env.API_SERVER}/files/download/${d._id}`} target=\"_blank\">{d.original_name.split('.').slice(0, -1).join('.')}</a>,\r\n      sortable: true\r\n    },\r\n    {\r\n      name: t(\"Description\"),\r\n      selector: 'description',\r\n      cell: (d: any) => d && d.description && d.description,\r\n    },\r\n    {\r\n      name: t(\"UploadedDate\"),\r\n      width: \"25%\",\r\n      selector: 'doc_created_at',\r\n      cell: (d: any) => d && d.updated_at && moment(d.updated_at).format('MM/DD/YYYY'),\r\n      sortable: true\r\n    }\r\n  ];\r\n\r\n  return (\r\n    <RKITable\r\n      columns={columns}\r\n      data={docs}\r\n      pagServer={true}\r\n      onSort={handleSort}\r\n      persistTableHead\r\n      loading={loading}\r\n    />\r\n\r\n  )\r\n}\r\n\r\nexport default DocumentTable;\r\n", "//Import Library\r\nimport { faMinus, faPlus } from \"@fortawesome/free-solid-svg-icons\";\r\nimport { FontAwesomeIcon } from \"@fortawesome/react-fontawesome\";\r\nimport { Card, Accordion } from \"react-bootstrap\";\r\n\r\n//Import services/components\r\nimport VspaceTable from \"../../components/common/VspaceTable\";\r\nimport { useTranslation } from 'next-i18next';\r\nimport { useState } from \"react\";\r\n\r\ninterface VirtualSpaceAccordianProps {\r\n  loading: boolean;\r\n  Document: any[];\r\n  updateDocument: any[];\r\n  hazardDocSort: (data: { columnSelector: string; sortDirection: string }) => void;\r\n  hazardDocUpdateSort: (data: { columnSelector: string; sortDirection: string }) => void;\r\n  docSrc: string[];\r\n  routeData?: {\r\n    routes?: string[];\r\n  };\r\n}\r\n\r\nconst VirtualSpaceAccordian = (props: VirtualSpaceAccordianProps) => {\r\n    const { t } = useTranslation('common');\r\n    const [section, setSection] = useState(false);\r\n    return(\r\n        <>\r\n           <Accordion.Item eventKey=\"0\">\r\n              <Accordion.Header onClick={() => setSection(!section)}>\r\n                <div className=\"cardTitle\">{t(\"LinkedVirtualSpace\")}</div>\r\n                <div className=\"cardArrow\">\r\n                  {section ? (\r\n                    <FontAwesomeIcon icon={faMinus} color=\"#fff\" />\r\n                  ) : (\r\n                    <FontAwesomeIcon icon={faPlus} color=\"#fff\" />\r\n                  )}\r\n                </div>\r\n              </Accordion.Header>\r\n              <Accordion.Body>\r\n                <VspaceTable\r\n                  id={props.routeData?.routes?.[1] || ''}\r\n                  type=\"Project\"\r\n                  vspaceData={[]}\r\n                  vspaceDataLoading={false}\r\n                  vspaceDataTotalRows={0}\r\n                  vspaceDataPerPage={10}\r\n                  vspaceDataCurrentPage={1}\r\n                />\r\n              </Accordion.Body>\r\n            </Accordion.Item>\r\n        </>\r\n    )\r\n};\r\n\r\nexport default VirtualSpaceAccordian;", "//Import Library\r\nimport { faMinus, faPlus } from \"@fortawesome/free-solid-svg-icons\";\r\nimport { FontAwesomeIcon } from \"@fortawesome/react-fontawesome\";\r\nimport { Card, Accordion } from \"react-bootstrap\";\r\nimport { useState } from \"react\";\r\n\r\n//Import services/components\r\nimport ReactImages from \"../../components/common/ReactImages\";\r\nimport { useTranslation } from 'next-i18next';\r\n\r\ninterface MediaGalleryAccordionProps {\r\n  images: any[];\r\n  imgSrc: string[];\r\n}\r\n\r\nconst MediaGalleryAccordion = (props: MediaGalleryAccordionProps) => {\r\n    const { t } = useTranslation('common');\r\n    const [section, setSection] = useState(false);\r\n    return(\r\n        <>\r\n            <Accordion.Item eventKey=\"0\">\r\n              <Accordion.Header onClick={() => setSection(!section)}>\r\n                <div className=\"cardTitle\">{t(\"mediaGallery\")}</div>\r\n                <div className=\"cardArrow\">\r\n                  {section ? (\r\n                    <FontAwesomeIcon icon={faMinus} color=\"#fff\" />\r\n                  ) : (\r\n                    <FontAwesomeIcon icon={faPlus} color=\"#fff\" />\r\n                  )}\r\n                </div>\r\n              </Accordion.Header>\r\n              <Accordion.Body>\r\n                <ReactImages gallery={props.images} imageSource={props.imgSrc} />\r\n              </Accordion.Body>\r\n            </Accordion.Item>\r\n        </>\r\n    );\r\n};\r\n\r\nexport default MediaGalleryAccordion;", "//! moment.js locale configuration\n//! locale : German [de]\n//! author : lluchs : https://github.com/lluchs\n//! author: <PERSON><PERSON><PERSON>: https://github.com/Oire\n//! author : <PERSON><PERSON><PERSON><PERSON> : https://github.com/mik01aj\n\n;(function (global, factory) {\n   typeof exports === 'object' && typeof module !== 'undefined'\n       && typeof require === 'function' ? factory(require('../moment')) :\n   typeof define === 'function' && define.amd ? define(['../moment'], factory) :\n   factory(global.moment)\n}(this, (function (moment) { 'use strict';\n\n    //! moment.js locale configuration\n\n    function processRelativeTime(number, withoutSuffix, key, isFuture) {\n        var format = {\n            m: ['eine Minute', 'einer Minute'],\n            h: ['eine Stunde', 'einer Stunde'],\n            d: ['ein Tag', 'einem Tag'],\n            dd: [number + ' Tage', number + ' Tagen'],\n            w: ['eine Woche', 'einer Woche'],\n            M: ['ein <PERSON><PERSON>', 'einem Monat'],\n            MM: [number + ' <PERSON><PERSON>', number + ' Monaten'],\n            y: ['ein Jahr', 'einem Jahr'],\n            yy: [number + ' Jahre', number + ' Jahren'],\n        };\n        return withoutSuffix ? format[key][0] : format[key][1];\n    }\n\n    var de = moment.defineLocale('de', {\n        months: 'Januar_Februar_März_April_Mai_Juni_Juli_August_September_Oktober_November_Dezember'.split(\n            '_'\n        ),\n        monthsShort:\n            'Jan._Feb._März_Apr._Mai_Juni_Juli_Aug._Sep._Okt._Nov._Dez.'.split('_'),\n        monthsParseExact: true,\n        weekdays:\n            'Sonntag_Montag_Dienstag_Mittwoch_Donnerstag_Freitag_Samstag'.split(\n                '_'\n            ),\n        weekdaysShort: 'So._Mo._Di._Mi._Do._Fr._Sa.'.split('_'),\n        weekdaysMin: 'So_Mo_Di_Mi_Do_Fr_Sa'.split('_'),\n        weekdaysParseExact: true,\n        longDateFormat: {\n            LT: 'HH:mm',\n            LTS: 'HH:mm:ss',\n            L: 'DD.MM.YYYY',\n            LL: 'D. MMMM YYYY',\n            LLL: 'D. MMMM YYYY HH:mm',\n            LLLL: 'dddd, D. MMMM YYYY HH:mm',\n        },\n        calendar: {\n            sameDay: '[heute um] LT [Uhr]',\n            sameElse: 'L',\n            nextDay: '[morgen um] LT [Uhr]',\n            nextWeek: 'dddd [um] LT [Uhr]',\n            lastDay: '[gestern um] LT [Uhr]',\n            lastWeek: '[letzten] dddd [um] LT [Uhr]',\n        },\n        relativeTime: {\n            future: 'in %s',\n            past: 'vor %s',\n            s: 'ein paar Sekunden',\n            ss: '%d Sekunden',\n            m: processRelativeTime,\n            mm: '%d Minuten',\n            h: processRelativeTime,\n            hh: '%d Stunden',\n            d: processRelativeTime,\n            dd: processRelativeTime,\n            w: processRelativeTime,\n            ww: '%d Wochen',\n            M: processRelativeTime,\n            MM: processRelativeTime,\n            y: processRelativeTime,\n            yy: processRelativeTime,\n        },\n        dayOfMonthOrdinalParse: /\\d{1,2}\\./,\n        ordinal: '%d.',\n        week: {\n            dow: 1, // Monday is the first day of the week.\n            doy: 4, // The week that contains Jan 4th is the first week of the year.\n        },\n    });\n\n    return de;\n\n})));\n", "//Import Library\r\nimport connectedAuthWrapper from 'redux-auth-wrapper/connectedAuthWrapper';\r\n\r\nexport const canViewDiscussionUpdate = connectedAuthWrapper({\r\n  authenticatedSelector: (state) => {\r\n    if (state.permissions && state.permissions.update && state.permissions.update['read:any']) {\r\n      return true;\r\n    }\r\n    return false;\r\n  },\r\n  wrapperDisplayName: 'CanViewDiscussionUpdate',\r\n});\r\n\r\nexport default canViewDiscussionUpdate;"], "names": ["props", "t", "useTranslation", "HazardAccordianSection", "section", "setSection", "useState", "DiscussionComponent", "Accordion", "<PERSON><PERSON>", "eventKey", "Header", "onClick", "div", "className", "FontAwesomeIcon", "icon", "faMinus", "color", "faPlus", "Body", "Discussion", "type", "id", "routeData", "routes", "CanViewDiscussionUpdate", "canViewDiscussionUpdate", "MediaGalleryAccordion", "DocumentAccordian", "VirtualSpaceAccordian", "loading", "documentAccoirdianProps", "Document", "updateDocument", "hazardDocSort", "hazardDocUpdateSort", "docSrc", "VspaceTable", "tabledata", "setDataToTable", "setLoading", "totalRows", "setTotalRows", "perPage", "setPerPage", "resetPaginationToggle", "vSpaceParams", "sort", "created_at", "limit", "page", "query", "columns", "name", "selector", "cell", "d", "title", "_id", "Link", "href", "as", "user", "firstname", "lastname", "visibility", "members", "length", "getLinkedVspace", "vSpaceParams1", "response", "apiService", "get", "operation", "project", "totalCount", "handlePerRowsChange", "newPerPage", "useEffect", "RKITable", "data", "handlePageChange", "paginationComponentOptions", "rowsPerPageText", "subheader", "subHeaderComponent", "rowsPerPage", "defaultRowsPerPage", "selectableRows", "pagServer", "onSelectedRowsChange", "clearSelectedRows", "sortServer", "onSort", "persistTableHead", "sortFunction", "otherProps", "dataTableProps", "noDataComponent", "<PERSON><PERSON><PERSON><PERSON>", "dense", "paginationResetDefaultPage", "subHeader", "progressPending", "pagination", "paginationServer", "paginationPerPage", "paginationRowsPerPageOptions", "paginationTotalRows", "onChangeRowsPerPage", "onChangePage", "progressComponent", "CustomLoader", "sortIcon", "i", "DataTable", "defaultProps", "DocumentTable", "sortProps", "docs", "docsDescription", "h6", "handleSort", "column", "sortDirection", "columnSelector", "objSlect", "width", "extension", "original_name", "a", "process", "target", "split", "slice", "join", "sortable", "description", "updated_at", "moment", "format", "vspaceData", "vspaceDataLoading", "vspaceDataTotalRows", "vspaceDataPerPage", "vspaceDataCurrentPage", "ReactImages", "gallery", "images", "imageSource", "imgSrc", "connectedAuthWrapper", "authenticatedSelector", "state", "permissions", "update", "wrapperDisplayName"], "sourceRoot": "", "ignoreList": [7]}