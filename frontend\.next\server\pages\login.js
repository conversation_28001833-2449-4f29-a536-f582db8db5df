"use strict";(()=>{var e={};e.id=8295,e.ids=[636,3220,8295],e.modules={123:e=>{e.exports=require("dom-helpers/removeEventListener")},1332:e=>{e.exports=require("react-custom-scrollbars-2")},1428:e=>{e.exports=import("axios")},1680:e=>{e.exports=require("@restart/ui/utils")},1919:e=>{e.exports=require("react-transition-group/Transition")},3892:e=>{e.exports=require("classnames")},4048:e=>{e.exports=require("react-truncate")},6009:e=>{e.exports=require("dom-helpers/ownerDocument")},6952:e=>{e.exports=require("@restart/ui/Modal")},7374:e=>{e.exports=require("@restart/ui/Dropdown")},8732:e=>{e.exports=require("react/jsx-runtime")},9653:e=>{e.exports=require("@restart/ui/Overlay")},11242:e=>{e.exports=require("redux-persist/integration/react")},11688:e=>{e.exports=require("@restart/hooks/useWillUnmount")},12388:e=>{e.exports=require("dom-helpers/addEventListener")},13364:e=>{e.exports=require("redux-saga/effects")},14062:e=>{e.exports=import("react-redux")},14078:e=>{e.exports=import("swr")},14332:e=>{e.exports=require("uncontrollable")},16116:e=>{e.exports=require("invariant")},18622:e=>{e.exports=require("yup")},19121:e=>{e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},22326:e=>{e.exports=require("react-dom")},22541:e=>{e.exports=require("redux-persist/lib/storage")},25169:(e,r,s)=>{s.d(r,{A:()=>f});var t=s(3892),a=s.n(t),o=s(82015),i=s(14332),n=s(81895),l=s.n(n),u=s(80739),p=s(7783),d=s(8732);let c=(0,p.A)("h4");c.displayName="DivStyledAsH4";let m=o.forwardRef(({className:e,bsPrefix:r,as:s=c,...t},o)=>(r=(0,u.oU)(r,"alert-heading"),(0,d.jsx)(s,{ref:o,className:a()(e,r),...t})));m.displayName="AlertHeading";var x=s(78634),g=s.n(x);let h=o.forwardRef(({className:e,bsPrefix:r,as:s=g(),...t},o)=>(r=(0,u.oU)(r,"alert-link"),(0,d.jsx)(s,{ref:o,className:a()(e,r),...t})));h.displayName="AlertLink";var q=s(19799),b=s(73087);let v=o.forwardRef((e,r)=>{let{bsPrefix:s,show:t=!0,closeLabel:o="Close alert",closeVariant:n,className:p,children:c,variant:m="primary",onClose:x,dismissible:g,transition:h=q.A,...v}=(0,i.useUncontrolled)(e,{show:"onClose"}),f=(0,u.oU)(s,"alert"),j=l()(e=>{x&&x(!1,e)}),P=!0===h?q.A:h,S=(0,d.jsxs)("div",{role:"alert",...!P?v:void 0,ref:r,className:a()(p,f,m&&`${f}-${m}`,g&&`${f}-dismissible`),children:[g&&(0,d.jsx)(b.A,{onClick:j,"aria-label":o,variant:n}),c]});return P?(0,d.jsx)(P,{unmountOnExit:!0,...v,ref:void 0,in:t,children:S}):t?S:null});v.displayName="Alert";let f=Object.assign(v,{Link:h,Heading:m})},25303:e=>{e.exports=require("@restart/ui/NavItem")},26324:e=>{e.exports=require("warning")},27825:e=>{e.exports=require("lodash")},27910:e=>{e.exports=require("stream")},28217:e=>{e.exports=require("@restart/ui/DropdownItem")},29021:e=>{e.exports=require("fs")},29294:e=>{e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},29628:e=>{e.exports=require("dom-helpers/addClass")},29825:e=>{e.exports=require("prop-types")},29841:e=>{e.exports=require("dom-helpers/hasClass")},30362:e=>{e.exports=require("@restart/ui/DropdownToggle")},33873:e=>{e.exports=require("path")},35576:e=>{e.exports=require("next-i18next/serverSideTranslations")},36653:e=>{e.exports=require("nprogress")},36955:e=>{e.exports=require("@restart/hooks/useBreakpoint")},37766:e=>{e.exports=require("@restart/hooks/useMergedRefs")},39756:e=>{e.exports=import("redux")},40051:e=>{e.exports=require("dom-helpers/removeClass")},40361:e=>{e.exports=require("next/dist/compiled/next-server/pages.runtime.prod.js")},42893:e=>{e.exports=import("react-hot-toast")},43294:e=>{e.exports=require("formik")},50009:e=>{e.exports=require("@restart/ui/DropdownMenu")},57664:e=>{e.exports=require("dom-helpers/scrollbarSize")},58928:e=>{e.exports=require("dom-helpers/canUseDOM")},59717:e=>{e.exports=require("@restart/ui/DropdownContext")},60560:e=>{e.exports=require("dom-helpers/contains")},63033:e=>{e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},65447:e=>{e.exports=require("@restart/ui/Button")},67364:e=>{e.exports=require("@restart/hooks/useCallbackRef")},68455:e=>{e.exports=import("redux-saga")},69722:e=>{e.exports=require("es6-promise")},74075:e=>{e.exports=require("zlib")},74716:e=>{e.exports=require("moment")},74987:e=>{e.exports=require("@restart/ui/ModalManager")},75851:(e,r,s)=>{s.a(e,async(e,t)=>{try{s.r(r),s.d(r,{default:()=>v,getStaticProps:()=>b});var a=s(8732),o=s(82015),i=s(14062),n=s(44233),l=s.n(n),u=s(42893),p=s(19918),d=s.n(p),c=s(25169),m=s(49856),x=s(84337),g=s(88751),h=s(35576),q=e([i,u,x]);async function b({locale:e}){return{props:{...await (0,h.serverSideTranslations)(e,["common"])}}}[i,u,x]=q.then?(await q)():q;let v=(0,i.connect)()(e=>{let{t:r}=(0,g.useTranslation)("common"),[s,t]=(0,o.useState)({username:"",password:"",rememberMe:!1}),[,i]=(0,o.useState)(!1),n=e=>{13===e.keyCode&&b(e)},[p,h]=(0,o.useState)({message:"",display:!1});(0,o.useEffect)(()=>{let e="true"===localStorage.getItem("rememberMe"),r=e?localStorage.getItem("user"):"",s=e?localStorage.getItem("password"):"";t({username:r,password:s,rememberMe:e})},[]);let q=e=>{h({message:"",display:!1}),t({...s,[e.target.name]:e.target.value})},b=async a=>{a.preventDefault();let{auth:o,logout:n}=x.A;s.username=s.username.trim();let p=await o(s);p&&201===p.status&&p.data&&p.data.isEnabled?(u.default.success(r("login.success")),l().push("/"),e.dispatch((0,m.js)())):403===p.status?(await n(),h({display:!0,message:r("login.unauthAccess")})):(i(!0),t(e=>({...e,username:"",password:""})),401===p.status&&h({display:!0,message:r("login.invalidUserPass")})),localStorage.setItem("rememberMe",s.rememberMe.toString()),localStorage.setItem("user",s.rememberMe?s.username:""),localStorage.setItem("password",s.rememberMe?s.password:"")};return(0,a.jsxs)("div",{className:"loginContainer",children:[p.display?(0,a.jsx)(c.A,{variant:"danger",children:p.message},"danger"):(0,a.jsx)(a.Fragment,{}),(0,a.jsx)("div",{className:"section",children:(0,a.jsx)("div",{className:"container",children:(0,a.jsxs)("div",{className:"columns",children:[(0,a.jsx)("div",{className:"column  is-two-thirds",children:(0,a.jsxs)("div",{className:"column loginForm",children:[(0,a.jsx)("div",{className:"imgBanner",children:(0,a.jsx)("img",{src:"/images/login-banner.jpg",alt:"RKI Login Banner Image"})}),(0,a.jsxs)("form",{className:"formContainer",onSubmit:b,children:[(0,a.jsx)("div",{className:"logoContainer",children:(0,a.jsx)(d(),{href:"/",children:(0,a.jsx)("img",{src:"/images/logo.jpg",alt:"Rohert Koch Institut - Logo"})})}),(0,a.jsxs)("div",{children:[(0,a.jsxs)("div",{className:"mb-3",children:[(0,a.jsx)("label",{className:"label",children:"Username or Email"}),(0,a.jsx)("input",{className:"form-control",type:"text",placeholder:"Enter the Username or Email",name:"username",value:s.username,onChange:q,onKeyPress:n,required:!0})]}),(0,a.jsxs)("div",{className:"mb-3",children:[(0,a.jsx)("label",{className:"label",children:"Password"}),(0,a.jsx)("input",{className:"form-control",type:"password",value:s.password,placeholder:"Password",name:"password",onChange:q,onKeyPress:n,required:!0})]}),(0,a.jsxs)("div",{className:"mb-3 form-check d-flex justify-content-between",children:[(0,a.jsxs)("label",{children:[(0,a.jsx)("input",{name:"rememberMe",checked:s.rememberMe,onChange:e=>{let r=e.target,a="checkbox"===r.type?r.checked:r.value;t({...s,[r.name]:a}),h({message:"",display:!1})},type:"checkbox"})," ","Remember me"]}),(0,a.jsx)(d(),{href:"/forgot-password",as:"/forgot-password",children:(0,a.jsx)("p",{className:"text-primary btn p-0",children:"Forgot Password?"})})]}),(0,a.jsx)("div",{className:"field is-grouped",children:(0,a.jsx)("div",{className:"control",children:(0,a.jsx)("button",{className:"button is-primary",type:"submit",children:"Sign in"})})})]})]})]})}),(0,a.jsx)("div",{className:"column"})]})})})]})});t()}catch(e){t(e)}})},77143:(e,r,s)=>{s.a(e,async(e,t)=>{try{s.r(r),s.d(r,{config:()=>h,default:()=>c,getServerSideProps:()=>g,getStaticPaths:()=>x,getStaticProps:()=>m,reportWebVitals:()=>q,routeModule:()=>S,unstable_getServerProps:()=>j,unstable_getServerSideProps:()=>P,unstable_getStaticParams:()=>f,unstable_getStaticPaths:()=>v,unstable_getStaticProps:()=>b});var a=s(63885),o=s(80237),i=s(81413),n=s(9616),l=s.n(n),u=s(72386),p=s(75851),d=e([u,p]);[u,p]=d.then?(await d)():d;let c=(0,i.M)(p,"default"),m=(0,i.M)(p,"getStaticProps"),x=(0,i.M)(p,"getStaticPaths"),g=(0,i.M)(p,"getServerSideProps"),h=(0,i.M)(p,"config"),q=(0,i.M)(p,"reportWebVitals"),b=(0,i.M)(p,"unstable_getStaticProps"),v=(0,i.M)(p,"unstable_getStaticPaths"),f=(0,i.M)(p,"unstable_getStaticParams"),j=(0,i.M)(p,"unstable_getServerProps"),P=(0,i.M)(p,"unstable_getServerSideProps"),S=new a.PagesRouteModule({definition:{kind:o.A.PAGES,page:"/login",pathname:"/login",bundlePath:"",filename:""},components:{App:u.default,Document:l()},userland:p});t()}catch(e){t(e)}})},78097:e=>{e.exports=require("next-redux-saga")},78634:e=>{e.exports=require("@restart/ui/Anchor")},80237:(e,r)=>{Object.defineProperty(r,"A",{enumerable:!0,get:function(){return s}});var s=function(e){return e.PAGES="PAGES",e.PAGES_API="PAGES_API",e.APP_PAGE="APP_PAGE",e.APP_ROUTE="APP_ROUTE",e.IMAGE="IMAGE",e}({})},81366:e=>{e.exports=require("redux-auth-wrapper/connectedAuthWrapper")},81413:(e,r)=>{Object.defineProperty(r,"M",{enumerable:!0,get:function(){return function e(r,s){return s in r?r[s]:"then"in r&&"function"==typeof r.then?r.then(r=>e(r,s)):"function"==typeof r&&"default"===s?r:void 0}}})},81521:e=>{e.exports=require("dom-helpers/querySelectorAll")},81895:e=>{e.exports=require("@restart/hooks/useEventCallback")},82015:e=>{e.exports=require("react")},86842:e=>{e.exports=require("@restart/ui/SelectableContext")},87571:e=>{e.exports=require("dom-helpers/transitionEnd")},88751:e=>{e.exports=require("next-i18next")},93787:e=>{e.exports=require("redux-persist")},94947:e=>{e.exports=require("@restart/hooks/useTimeout")},96196:e=>{e.exports=require("next-redux-wrapper")},98320:e=>{e.exports=require("@restart/hooks/useIsomorphicEffect")},99460:e=>{e.exports=require("dom-helpers/css")}};var r=require("../webpack-runtime.js");r.C(e);var s=e=>r(r.s=e),t=r.X(0,[6089,9216,9616,2386],()=>s(77143));module.exports=t})();