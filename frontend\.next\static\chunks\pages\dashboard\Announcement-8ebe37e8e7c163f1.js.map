{"version": 3, "file": "static/chunks/pages/dashboard/Announcement-8ebe37e8e7c163f1.js", "mappings": "yKAiBe,SAASA,EAAiBC,CAA4B,EAEnE,GAAM,MAACC,CAAI,CAAC,CAAGD,EASTE,EAAYD,EAAKE,MAAM,CAACC,MAAM,CAAE,EAChCC,EAAS,UAAoB,OAAVJ,EAAKK,IAAI,EAElC,MACE,WAACC,EAAAA,CAAGA,CAAAA,CAACC,UAAU,MAAMC,GAAI,aACvB,UAACC,IAAIA,CAACC,KAAM,IAAc,OAAVV,EAAKK,IAAI,CAAC,gBAAeM,GAAI,EAAxCF,EAA8DT,MAAAA,CAAlBA,EAAKK,IAAI,CAAC,UAA+BL,MAAAA,CAAvBA,CAAI,CAACI,EAAO,CAAC,YAAmB,OAATJ,EAAKY,GAAG,WAE/F,EAAMV,MAAM,EAAIF,EAAKE,MAAM,CAACD,EAAS,CACpC,UAACY,MAAAA,CAAIC,IAAK,GAAwCd,MAAAA,CAArCe,8BAAsB,CAAC,gBAAwC,OAA1Bf,EAAKE,MAAM,CAACD,EAAS,CAACW,GAAG,EAAII,IAAI,eAC9ET,UAAU,gBACb,UAACU,IAAAA,CAAEV,UAAU,iCAGnB,WAACW,MAAAA,CAAIX,UAAU,yBACb,UAACE,IAAIA,CAACC,KAAM,IAAc,OAAVV,EAAKK,IAAI,CAAC,gBAAeM,GAAI,EAAxCF,EAA8DT,MAAAA,CAAlBA,EAAKK,IAAI,CAAC,UAA+BL,MAAAA,CAAvBA,CAAI,CAACI,EAAO,CAAC,YAAmB,OAATJ,EAAKY,GAAG,WAC/FZ,GAAQA,EAAKmB,KAAK,CAAGnB,EAAKmB,KAAK,CAAG,KAErC,UAACC,IAAAA,UACEpB,GAAQA,EAAKqB,WAAW,CAAGC,CAzBX,IACvB,IAAMJ,EAAMK,SAASC,aAAa,CAAC,OACnCN,EAAIO,SAAS,CAAGC,EAChB,IAAMC,EAAST,EAAIU,WAAW,EAAIV,EAAIW,SAAS,EAAI,GACnD,OAAQF,EAAOxB,MAAM,CArBF,EAqBK2B,EAAiB,GAA2C,OAAxCH,EAAOI,SAAS,CAAC,EAAGD,KAAoB,OAAOH,CAC7F,GAoBqD3B,CArB8B,CAqBzBqB,WAAW,EAAI,YAK3E,mBClDA,4CACA,0BACA,WACA,OAAe,EAAQ,KAA+C,CACtE,EACA,SAFsB,kEC4BtB,MAVA,cACA,MAAkB,YAAM,IASM,CAR5B,eAAS,MACX,cACA,aACA,MACA,CACA,UACA,CAAG,GACH,4FCzBA,IAAMW,EAA+BC,EAAAA,UAAgB,CAAC,GAKnDC,QALoD,GAApB,QACjC3B,CAAS,UACT4B,CAAQ,CACRxB,GAAIyB,EAAY,KAAK,CACrB,GAAGrC,EACJ,GAEC,OADAoC,EAAWE,CAAAA,EAAAA,EAAAA,EAAAA,CAAkBA,CAACF,EAAU,oBACpBG,CAAAA,EAAAA,EAAAA,GAAAA,CAAIA,CAACF,EAAW,CAClCF,IAAKA,EACL3B,UAAWgC,IAAWhC,EAAW4B,GACjC,GAAGpC,CAAK,EAEZ,EACAiC,GAJyBO,WAIE,CAAG,kBCb9B,IAAMC,EAA4BP,EAAAA,UAAgB,CAA7B,CAA8B,EAMhDC,QANiD,CAElDvB,CADA,EACIyB,EAAY,KAAK,UACrBD,CAAQ,WACR5B,CAAS,CACT,GAAGR,EACJ,GACO0C,EAAiBF,IAAWhC,EAAW8B,CAAAA,EAAAA,EAAAA,EAAAA,CAAkBA,CAACF,EAAU,GAAzCI,eACjC,MAAoBD,CAAb,EAAaA,EAAAA,GAAAA,CAAIA,CAACF,EAAW,CAClCF,IAAKA,EACL,GAAGnC,CAAK,CACRQ,UAAWkC,CACb,EACF,GACAD,EAAaE,WAAW,CAAG,iBAbkI,8CCsB7J,IAAMC,EAGNV,EAAAA,OAFA,GAEgB,CAAC,GAGdC,IALQ,GACX,EA2EMU,EA1EY,oBAChBC,EAAqB,CAAC,CACtB,GAAGC,EACJ,GACO,CAEJnC,CADA,EACIyB,EAAY,IAP0B,CAOrB,UACrBD,CAAQ,OACRY,GAAQ,CAAI,MACZC,GAAO,CAAK,UACZC,GAAW,CAAI,CACfC,cAAa,CAAI,iBACjBC,EAAkB,EAAE,aACpBC,CAAW,CACXC,UAAQ,SACRC,CAAO,QACPC,CAAM,CACNC,WAAW,GAAI,IAZ4I,MAa3JC,GAAW,CAAI,WACfC,CAAS,OACTC,EAAQ,OAAO,aACfC,CAAW,YACXC,CAAU,MACVC,GAAO,CAAI,CACXC,SAAQ,CAAI,cACZC,CAAY,aACZC,CAAW,YACXC,CAAU,UACVC,EAAwB7B,CAAAA,EAAAA,EAAAA,GAAAA,CAAIA,CAAC,OAAQ,CACnC,EADoB,YACL,OACf/B,UAAW,4BACb,EAAE,WACF6D,EAAY,UAAU,UACtBC,EAAwB/B,CAAAA,EAAAA,EAAAA,GAAAA,CAAb,CAAkB,OAAQ,CACnC,EADoB,YACL,OACf/B,UAAW,4BACb,EAAE,WACF+D,EAAY,MAAM,SAClBC,CAAO,WACPhE,CAAS,UACTiE,CAAQ,CACR,GAAGzE,EACJ,CAAG0E,CAAAA,EAAAA,EAAAA,EAAAA,CAAeA,CAAC,oBAClB5B,EACA,GAAGC,CAAiB,EACnB,CACDM,YAAa,UACf,GACMsB,EAASrC,CAAAA,EAAAA,EAAAA,EAAAA,CAAkBA,CAACF,EAAU,YACtCwC,EAAQC,CAAAA,EAAAA,EAAAA,EAAAA,CAAQA,GAChBC,EAAmBC,CAAAA,EAAAA,EAAAA,MAAAA,CAAMA,CAAC,MAC1B,CAACC,EAAWC,EAAa,CAAGC,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAC,QACrC,CAACC,GAAQC,GAAU,CAAGF,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,EAAC,GAC/B,CAACG,GAAWC,GAAa,CAAGJ,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,EAAC,GACrC,CAACK,GAAqBC,GAAuB,CAAGN,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAC7B,GAAe,GAC9EoC,CAAAA,EAAAA,EAAAA,SAAAA,CAASA,CAAC,KACHJ,IAAahC,IAAgBkC,KAC5BT,EAAiBY,OAAO,CAC1BT,CAD4B,CACfH,EAAiBY,EAFqB,KAEd,EAErCT,EAAa,CAAC5B,IAAe,EAAKkC,GAAsB,OAAS,QAE/DvC,GACFsC,IADS,GAGXE,GAAuBnC,GAAe,GAE1C,EAAG,CAACA,EAAagC,GAAWE,GAAqBvC,EAAM,EACvDyC,CAAAA,EAAAA,EAAAA,SAAAA,CAASA,CAAC,KACJX,EAAiBY,OAAO,EAAE,CAC5BZ,EAAiBY,OAAO,CAAG,KAE/B,GACA,IAAIC,GAAc,EAKlBC,CAAAA,EAAAA,EAAAA,EAAAA,CAAOA,CAACnB,EAAU,CAACoB,EAAOC,KACxB,EAAEH,GACEG,IAAUzC,IACZR,EAAsBgD,EAAM7F,KADH,CACSyD,QAAAA,CAEtC,GACA,IAAMsC,GAAyBC,CAAAA,EAAAA,EAAAA,CAAAA,CAAeA,CAACnD,GACzCoD,GAAOC,CAAAA,EAAAA,EAAAA,WAAAA,CAAWA,CAACC,IACvB,GAAId,GACF,OAEF,CAHe,GAGXe,EAAkBb,GAAsB,EAC5C,GAAIa,EAAkB,EAAG,CACvB,GAAI,CAACrC,EACH,IADS,GAGXqC,EAAkBT,GAAc,CAClC,CACAb,EAAiBY,OAAO,CAAG,OACf,MAAZpC,GAAoBA,EAAS8C,EAAiBD,EAChD,EAAG,CAACd,GAAWE,GAAqBjC,EAAUS,EAAM4B,GAAY,EAG1DU,GAAOC,CAAAA,EAAAA,EAAAA,CAAAA,CAAgBA,CAACH,IAC5B,GAAId,GACF,OAEF,CAHe,GAGXe,EAAkBb,GAAsB,EAC5C,GAAIa,GAAmBT,GAAa,CAClC,GAAI,CAAC5B,EACH,IADS,GAGXqC,EAAkB,CACpB,CACAtB,EAAiBY,OAAO,CAAG,OACf,MAAZpC,GAAoBA,EAAS8C,EAAiBD,EAChD,GACMI,GAAaxB,CAAAA,EAAAA,EAAAA,MAAAA,CAAMA,GACzByB,CAAAA,EAAAA,EAAAA,mBAAAA,CAAmBA,CAACrE,EAAK,IAAO,EAC9BsE,QAASF,GAAWb,OAAO,MAC3BO,GACAI,QACF,GAGA,IAAMK,GAAkBJ,CAAAA,EAAAA,EAAAA,CAAAA,CAAgBA,CAAC,KACnC,CAAC9E,SAASmF,MAAM,EAtIxB,SAASC,CAAiB,EACxB,GAAI,CAACH,GAAW,CAACA,EAAQI,KAAK,EAAI,CAACJ,EAAQK,UAAU,EAAI,CAACL,EAAQK,UAAU,CAACD,KAAK,CAChF,CADkF,MAC3E,EAET,IAAME,EAAeC,iBAAiBP,GACtC,MAAgC,SAAzBM,EAAaE,OAAO,EAA2C,WAA5BF,EAAaG,UAAU,EAAkE,0BAAhCT,EAAQK,UAAU,EAAEG,OACzH,EAgIsCV,GAAWb,OAAO,GAAG,CACjDd,EACFqB,KADS,KAMf,GACMkB,GAA+B,SAAdnC,EAAuB,QAAU,MACxDoC,EAAgB,KACVpE,IAIO,GAJA,GAIXO,EALa6D,CAKM7D,EAAQgC,GAAqB4B,IACtC,MAAV3D,GAAkBA,EAAO+B,GAAqB4B,IAChD,EAAG,CAAC5B,GAAoB,EACxB,IAAM8B,GAAiB,GAAkBrC,MAAAA,CAAfL,EAAO,UAAkB,OAAVK,GACnCsC,GAAuB,GAAkBH,MAAAA,CAAfxC,EAAO,UAAuB,OAAfwC,IACzCI,GAAcrB,CAAAA,EAAAA,EAAAA,WAAAA,CAAWA,CAACsB,IAC9BC,CAAAA,EAAAA,EAAAA,CAAAA,CAAoBA,CAACD,GACV,MAAXjE,GAAmBA,EAAQgC,GAAqB4B,GAClD,EAAG,CAAC5D,EAASgC,GAAqB4B,GAAe,EAC3CO,GAAgBxB,CAAAA,EAAAA,EAAAA,WAAAA,CAAWA,CAAC,KAChCZ,GAAa,IACH,MAAV9B,GAAkBA,EAAO+B,GAAqB4B,GAChD,EAAG,CAAC3D,EAAQ+B,GAAqB4B,GAAe,EAC1CQ,GAAgBzB,CAAAA,EAAAA,EAAAA,WAAAA,CAAWA,CAACC,IAChC,GAAIzC,GAAY,CAAC,kBAAkBkE,IAAI,CAACzB,EAAM0B,MAAM,CAACC,OAAO,EAC1D,CAD6D,MACrD3B,EAAM4B,GAAG,EACf,IAAK,YACH5B,EAAM6B,cAAc,GAChBpD,EACFyB,GAAKF,EADI,CAGTF,GAAKE,GAEP,MACF,KAAK,aACHA,EAAM6B,cAAc,GAChBpD,EACFqB,GAAKE,EADI,CAGTE,GAAKF,GAEP,MAEJ,CAEW,MAAbxC,GAAqBA,EAAUwC,EACjC,EAAG,CAACzC,EAAUC,EAAWsC,GAAMI,GAAMzB,EAAM,EACrCqD,GAAkB/B,CAAAA,EAAAA,EAAAA,WAAAA,CAAWA,CAACC,IACpB,SAAS,CAAnBvC,GACFwB,IAAU,GAEG,MAAfvB,GAAuBA,EAAYsC,EACrC,EAAG,CAACvC,EAAOC,EAAY,EACjBqE,GAAiBhC,CAAAA,EAAAA,EAAAA,WAAAA,CAAWA,CAACC,IACjCf,IAAU,GACI,MAAdtB,GAAsBA,EAAWqC,EACnC,EAAG,CAACrC,EAAW,EACTqE,GAAiBpD,CAAAA,EAAAA,EAAAA,MAAAA,CAAMA,CAAC,GACxBqD,GAAiBrD,CAAAA,EAAAA,EAAAA,MAAAA,CAAMA,CAAC,GACxBsD,GAAsBC,CAAAA,EAAAA,EAAAA,CAAAA,CAAUA,GAChCC,GAAmBrC,CAAAA,EAAAA,EAAAA,WAAAA,CAAWA,CAACC,IACnCgC,GAAezC,OAAO,CAAGS,EAAMqC,OAAO,CAAC,EAAE,CAACC,OAAO,CACjDL,GAAe1C,OAAO,CAAG,EACX,SAAS,CAAnB9B,GACFwB,IAAU,GAEZnB,SAAwBA,EAAakC,EACvC,EAAG,CAACvC,EAAOK,EAAa,EAClByE,GAAkBxC,CAAAA,EAAAA,EAAAA,WAAAA,CAAWA,CAACC,IAC9BA,EAAMqC,OAAO,EAAIrC,EAAMqC,OAAO,CAACpI,MAAM,CAAG,EAC1CgI,CAD6C,EAC9B1C,OAAO,CAAG,EAEzB0C,GAAe1C,OAAO,CAAGS,EAAMqC,OAAO,CAAC,EAAE,CAACC,OAAO,CAAGN,GAAezC,OAAO,CAE7D,MAAfxB,GAAuBA,EAAYiC,EACrC,EAAG,CAACjC,EAAY,EACVyE,GAAiBzC,CAAAA,EAAAA,EAAAA,WAAAA,CAAWA,CAACC,IACjC,GAAInC,EAAO,CACT,IAAM4E,EAAcR,GAAe1C,OAAO,CACtCmD,KAAKC,GAAG,CAACF,GA1NK,KA2NZA,EAAc,EAChB3C,CADmB,EACdE,GAELE,GAAKF,GAGX,CACc,OARiC,EAQxB,CAAnBvC,GACFyE,GAAoBU,GAAG,CAAC,KACtB3D,IAAU,EACZ,EAAG3B,QAAYuF,GAEH,MAAd7E,GAAsBA,EAAWgC,EACnC,EAAG,CAACnC,EAAOJ,EAAOqC,GAAMI,GAAMgC,GAAqB5E,EAAUU,EAAW,EAClE8E,GAAyB,MAAZxF,GAAoB,CAAC0B,IAAU,CAACE,GAC7C6D,GAAoBnE,CAAAA,EAAAA,EAAAA,MAAAA,CAAMA,GAChCU,CAAAA,EAAAA,EAAAA,SAAAA,CAASA,CAAC,KACR,IAAI0D,EAAMC,EACV,GAAI,CAACH,GACH,OAAOD,EADQ,EAGXK,EAAWzE,EAAQqB,GAAOI,GAEhC,OADA6C,GAAkBxD,OAAO,CAAG4D,OAAOC,WAAW,CAAC/H,SAASgI,eAAe,CAAG9C,GAAkB2C,EAAU,OAACF,EAAO,OAACC,EAAwBrD,GAAuBL,OAAAA,EAAmB0D,EAAwB3F,CAAAA,CAAO,CAAa0F,EAAOH,QAC7N,KACDE,MAAoC,IAAlBxD,OAAO,EAC3B+D,cAAcP,GAAkBxD,OAAO,CAE3C,CACF,EAAG,CAACuD,GAAYhD,GAAMI,GAAMN,GAAwBtC,EAAUiD,GAAiB9B,EAAM,EACrF,IAAM8E,GAAoBC,CAAAA,EAAAA,EAAAA,OAAAA,CAAOA,CAAC,IAAMxG,GAAcyG,MAAMC,IAAI,CAAC,CAC/DzJ,OAAQuF,EACV,EAAG,CAACmE,EAAGhE,IAAUK,IACH,MAAZ7C,GAAoBA,EAASwC,EAAOK,EACtC,GAAI,CAAChD,EAAYwC,GAAarC,EAAS,EACvC,MAAoByG,CAAb,EAAaA,EAAAA,IAAAA,CAAKA,CAAC1H,CAAR,CAAmB,CACnCF,IAAKoE,GACL,GAAGvG,CAAK,CACR2D,UAAWgE,GACX9D,YAAaoE,GACbnE,WAAYoE,GACZjE,aAAcsE,GACdrE,YAAawE,GACbvE,WAAYwE,GACZnI,UAAWgC,IAAWhC,EAAWmE,EAAQ3B,GAAS,QAASC,CAAtCT,EAA8C,GAAU,OAAPmC,EAAO,SAAQH,GAAW,GAAaA,MAAAA,CAAVG,EAAO,KAAW,OAARH,IAC7GC,SAAU,CAACtB,GAA2BZ,CAAAA,EAAAA,EAAAA,GAAAA,CAAIA,CAAC,CAAlB,KAAyB,CAChD/B,KADkC,KACvB,GAAU,OAAPmE,EAAO,eACrBF,SAAUuF,CAAAA,EAAAA,EAAAA,EAAAA,CAAGA,CAACvF,EAAU,CAACqF,EAAGhE,IAAuBvD,CAAAA,EAAAA,CAAb,CAAaA,GAAAA,CAAIA,CAAC,KAAP,IAAiB,CAChEjC,KAAM,SACN,iBAAkB,GAAG,aAEP8C,SAA2BA,EAAgBhD,MAAM,CAAGgD,CAAe,CAAC0C,EAAM,CAAG,IAF9B,KAEiD,OAAVA,EAAQ,GAC5GtF,UAAWsF,IAAUP,GAAsB,cAAWyD,EACtDiB,QAASP,GAAoBA,EAAiB,CAAC5D,EAAM,CAAGkD,OACxD,eAAgBlD,IAAUP,EAC5B,EAAGO,GACL,GAAiBvD,CAAAA,EAAAA,EAAAA,GAAAA,CAAIA,CAAC,MAAO,CAC3B/B,UAAW,GAAU,OAAPmE,EAAO,UACrBF,SAAUuF,CAAAA,EAAAA,EAAAA,EAAAA,CAAGA,CAACvF,EAAU,CAACoB,EAAOC,KAC9B,IAAMoE,EAAWpE,IAAUP,GAC3B,OAAOvC,EAAqBT,CAAAA,EAAAA,EAAAA,CAAb,EAAaA,CAAIA,CAAC4H,EAAAA,CAAiBA,CAAE,CAClDC,EADwB,CACpBF,EACJG,QAASH,EAAW3C,QAAcyB,EAClCsB,UAAWJ,EAAWxC,QAAgBsB,EACtCuB,eAAgBC,EAAAA,CAAqBA,CACrC/F,SAAU,CAACgG,EAAQC,IAA4BxI,EAAAA,OAAb,KAA+B,CAAC2D,EAAO,CACvE,EAD2C,CACxC6E,CAAU,CACblK,UAAWgC,IAAWqD,EAAM7F,KAAK,CAACQ,QAAbgC,CAAsB,CAAE0H,GAAuB,YAAXO,GAAwBpD,GAAgB,CAAY,YAAXoD,GAAmC,YAAXA,CAAW,CAAQ,EAAM,SAAU,CAAY,aAAXA,GAAoC,YAAXA,CAAW,CAAQ,EAAMnD,GAClN,EACF,GAAoBpF,EAAb,WAAW,CAAoB,CAAC2D,EAAO,CAC5CrF,UAAWgC,IAAWqD,EAAM7F,KAAK,CAACQ,QAAbgC,CAAsB,CAAE0H,GAAY,SAC3D,EACF,EACF,GAAIhH,GAAyB6G,CAAAA,EAAAA,EAAAA,IAAAA,CAAKA,CAACY,EAAAA,OAAR,CAAiBA,CAAE,CAC5ClG,SAAU,CAAEV,CAAAA,GAAQV,KAAgB,GAAmB0G,CAAAA,EAAb,EAAaA,IAAAA,CAAKA,CAACa,EAAAA,CAAMA,CAAE,CACnEpK,UAAW,GAAU,OAAPmE,EAAO,iBACrBsF,QAAShE,GACTxB,SAAU,CAACL,EAAUC,GAA0B9B,CAAAA,EAAAA,EAAAA,GAAAA,CAAIA,CAAjB,OAA0B,CAC1D/B,GAD2C,OAChC,kBACXiE,SAAUJ,CACZ,GAAG,GACAN,CAAAA,GAAQV,IAAgBsC,IAAc,GAAmBoE,CAAAA,EAAb,EAAaA,IAAAA,CAAKA,CAACa,EAAAA,CAAR,CAAgB,CAC1EpK,UAAW,GAAU,OAAPmE,EAAO,iBACrBsF,QAAS5D,GACT5B,SAAU,CAACH,EAAUC,GAA0BhC,CAAAA,EAAAA,EAAAA,GAAAA,CAAIA,CAAjB,OAA0B,CAC1D/B,GAD2C,OAChC,kBACXiE,SAAUF,CACZ,GAAG,GACF,GACF,EAEP,GACA3B,EAASD,WAAW,CAAG,WACvB,MAAekI,OAAOC,MAAM,CAAClI,EAAU,CACrCmI,QFzTa9I,CEyTJA,CACT+I,KDzTavI,CCyTPA,EACN,EAAC,GF3T2BR,EAAC,ECCJQ,CCwTDR,CDxTE,GCyTRQ,+JC7TpB,SAASwI,EAAuBjL,CAAkC,EAChE,GAAM,eAAEkL,CAAa,CAAE,CAAGlL,EAC1B,MACE,UAACmB,MAAAA,UACE+J,EAAclB,GAAG,CAAC,CAAC/J,EAAW6F,IAE3B,UAACqF,EAAAA,CAAGA,CAAAA,CAAC3K,UAAU,4BACb,UAACT,EAAAA,OAAgBA,CAAAA,CAACE,KAAMA,KADa6F,KAOjD,CAmHA,MA7GA,SAASsF,CAAqC,KAAxB,CAAEC,CAAC,CAAqB,CAAxB,EACd,CAACH,EAAeI,EAAiB,CAAGpG,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAU,EAAE,EAExD,CAACqG,EAAQC,EAAU,CAAGtG,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAC,GAE/B,CAACuG,EAAkB,CAAGvG,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAC,GAE/BwG,EAAiB,KACrBJ,EAAiB,EAAE,CACrB,EAEMK,EAAgB,CACpBC,MAAO,CAAEC,sBAAsB,CAAK,EACpCC,KAAM,CAAEC,WAAY,MAAO,EAC3BC,MAAO,EACPC,OAAQ,iHACV,EAEMC,EAAqB,qBAAOC,EAAAA,UAAAA,MAAAA,CAAAA,GAAAA,KAAAA,IAAAA,SAAAA,CAAAA,EAAAA,CAAAA,SAAAA,CAAAA,EAAAA,CAASR,EACnCS,EAAW,MAAMC,EAAAA,CAAUA,CAACC,GAAG,CAAC,WAAYH,GAC9CC,GAAYA,EAASG,IAAI,EAAIH,EAASG,IAAI,CAACnM,MAAM,CAAG,EAEtDkL,CAFyD,CACvCxB,IAAAA,KAAO,CAACsC,EAASG,GAClBC,CADsB,CAAE,IAGzCd,GAEJ,EAEAjG,CAAAA,EAAAA,EAAAA,SAAAA,CAASA,CAAC,KACRyG,GACF,EAAG,EAAE,EAEL,IAAMO,EAAiB,IACrB,IAAI3G,EAAQyF,EACN,CAACmB,EAAMC,EAAI,CAAG,CAAC,EAAGlB,EAAoB,EAAE,CAE5B,SAAdzG,GAAwBc,EAAQ6G,EAClC7G,GADuC,CAGlB,SAAdd,GAAwBc,EAAQ,GAAI,IAKzC,EAAe1F,MAAM,CAAG0F,GAAW,GAAG,CACxCA,EAAQoF,EAAc9K,MAAM,EAAG,EAG7B,EAAeA,MAAM,CAAG0F,GAAW,GAAG,CACxCA,GAAQ,EAEV0F,EAAU1F,EACZ,EAGA,MACE,UAAC3E,MAAAA,CAAIX,UAAU,yBACZ0K,GAAiBA,EAAc9K,MAAM,CAAG,EACvC,iCACE,UAACwM,EAAAA,CAASA,CAAAA,CAACC,KAAK,aACd,WAAC1B,EAAAA,CAAGA,CAAAA,WACF,UAAC5K,EAAAA,CAAGA,CAAAA,CAACE,GAAI,GAAID,UAAU,eACrB,UAACsM,KAAAA,UAAIzB,EAAE,qBAERH,GAAiBA,EAAc9K,MAAM,CAAG,EACvC,UAACG,EAAAA,CAAGA,CAAAA,CAACE,GAAI,EAAGD,UAAU,yCACpB,WAACW,MAAAA,CAAIX,UAAU,gCACb,UAACuM,IAAAA,CAAEvM,UAAU,wBAAwByJ,QAAS,IAAMwC,EAAe,iBACjE,UAACvL,IAAAA,CAAEV,UAAU,yBAEf,UAACuM,IAAAA,CAAEvM,UAAU,yBAAyByJ,QAAS,IAAMwC,EAAe,iBAClE,UAACvL,IAAAA,CAAEV,UAAU,+BAIjB,UAGR,UAACoM,EAAAA,CAASA,CAAAA,CAACC,KAAK,aACd,UAAC1B,EAAAA,CAAGA,CAAAA,UACF,UAAC5K,EAAAA,CAAGA,CAAAA,CAACE,GAAI,GAAID,UAAU,eACrB,UAACoC,EAAAA,CAAQA,CAAAA,CAACO,WAAY,GAAOD,UAAU,EAAOO,SAAU,KAAMJ,YAAakI,WACxEL,EAAclB,GAAG,CAAC,CAAC/J,EAAW6F,IAE3B,UAAClD,EAAAA,CAAQA,CAACoI,IAAI,WACZ,UAACC,EAAAA,CAAuBC,cAAejL,KADrB6F,eAWhC,UAAC8G,EAAAA,CAASA,CAAAA,CAACC,OAAO,WAChB,WAAC1B,EAAAA,CAAGA,CAAAA,WACF,UAAC5K,EAAAA,CAAGA,CAAAA,CAACE,GAAI,GAAID,UAAU,eACrB,UAACsM,KAAAA,UAAIzB,EAAE,qBAET,WAAC9K,EAAAA,CAAGA,CAAAA,CAACE,GAAI,GAAID,UAAU,gBACrB,UAACW,MAAAA,CAAIX,UAAU,sBAAc6K,EAAE,uCAA2C,UAAC2B,KAAAA,CAAAA,YAM3F", "sources": ["webpack://_N_E/./pages/dashboard/AnnouncementItem.tsx", "webpack://_N_E/?84e9", "webpack://_N_E/./node_modules/@restart/hooks/esm/useUpdateEffect.js", "webpack://_N_E/./node_modules/react-bootstrap/esm/CarouselCaption.js", "webpack://_N_E/./node_modules/react-bootstrap/esm/CarouselItem.js", "webpack://_N_E/./node_modules/react-bootstrap/esm/Carousel.js", "webpack://_N_E/./pages/dashboard/Announcement.tsx"], "sourcesContent": ["//Import Library\r\nimport {Col} from \"react-bootstrap\";\r\nimport <PERSON> from \"next/link\";\r\n\r\nconst truncateLength = 260;\r\n\r\n//TODO: Remove the maths random number for image after updates completed with image upload\r\ninterface AnnouncementItemProps {\r\n  item: {\r\n    _id: string;\r\n    title: string;\r\n    description: string;\r\n    type: string;\r\n    [key: string]: any;\r\n  };\r\n}\r\n\r\nexport default function AnnouncementItem(props: AnnouncementItemProps) {\r\n\r\n  const {item} = props;\r\n\r\n  const getTrimmedString = (html: string) => {\r\n    const div = document.createElement(\"div\");\r\n    div.innerHTML = html;\r\n    const string = div.textContent || div.innerText || \"\";\r\n    return (string.length > truncateLength ? `${string.substring(0, truncateLength - 3)}...` : string);\r\n  }\r\n\r\n  const indexNew =  item.images.length -1;\r\n  const parent = `parent_${item.type}`;\r\n\r\n  return (\r\n    <Col className=\"p-0\" xs={12}>\r\n      <Link href={`/${item.type}/[...routes]`} as={`/${item.type}/show/${item[parent]}/update/${item._id}`}>\r\n\r\n        {(item.images && item.images[indexNew]) ?\r\n          <img src={`${process.env.API_SERVER}/image/show/${item.images[indexNew]._id}`} alt=\"announcement\"\r\n               className=\"announceImg\"/>\r\n          : <i className=\"fa fa-bullhorn announceImg\"/>}\r\n\r\n      </Link>\r\n      <div className=\"announceDesc\">\r\n        <Link href={`/${item.type}/[...routes]`} as={`/${item.type}/show/${item[parent]}/update/${item._id}`}>\r\n          {item && item.title ? item.title : ''}\r\n        </Link>\r\n        <p>\r\n          {item && item.description ? getTrimmedString(item.description) : null}\r\n        </p>\r\n      </div>\r\n    </Col>\r\n  );\r\n}", "\n    (window.__NEXT_P = window.__NEXT_P || []).push([\n      \"/dashboard/Announcement\",\n      function () {\n        return require(\"private-next-pages/dashboard/Announcement.tsx\");\n      }\n    ]);\n    if(module.hot) {\n      module.hot.dispose(function () {\n        window.__NEXT_P.push([\"/dashboard/Announcement\"])\n      });\n    }\n  ", "import { useEffect, useRef } from 'react';\n\n/**\n * Runs an effect only when the dependencies have changed, skipping the\n * initial \"on mount\" run. Caution, if the dependency list never changes,\n * the effect is **never run**\n *\n * ```ts\n *  const ref = useRef<HTMLInput>(null);\n *\n *  // focuses an element only if the focus changes, and not on mount\n *  useUpdateEffect(() => {\n *    const element = ref.current?.children[focusedIdx] as HTMLElement\n *\n *    element?.focus()\n *\n *  }, [focusedIndex])\n * ```\n * @param effect An effect to run on mount\n *\n * @category effects\n */\nfunction useUpdateEffect(fn, deps) {\n  const isFirst = useRef(true);\n  useEffect(() => {\n    if (isFirst.current) {\n      isFirst.current = false;\n      return;\n    }\n    return fn();\n  }, deps);\n}\nexport default useUpdateEffect;", "\"use client\";\n\nimport * as React from 'react';\nimport classNames from 'classnames';\nimport { useBootstrapPrefix } from './ThemeProvider';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst CarouselCaption = /*#__PURE__*/React.forwardRef(({\n  className,\n  bsPrefix,\n  as: Component = 'div',\n  ...props\n}, ref) => {\n  bsPrefix = useBootstrapPrefix(bsPrefix, 'carousel-caption');\n  return /*#__PURE__*/_jsx(Component, {\n    ref: ref,\n    className: classNames(className, bsPrefix),\n    ...props\n  });\n});\nCarouselCaption.displayName = 'CarouselCaption';\nexport default CarouselCaption;", "\"use client\";\n\nimport classNames from 'classnames';\nimport * as React from 'react';\nimport { useBootstrapPrefix } from './ThemeProvider';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst CarouselItem = /*#__PURE__*/React.forwardRef(({\n  // Need to define the default \"as\" during prop destructuring to be compatible with styled-components github.com/react-bootstrap/react-bootstrap/issues/3595\n  as: Component = 'div',\n  bsPrefix,\n  className,\n  ...props\n}, ref) => {\n  const finalClassName = classNames(className, useBootstrapPrefix(bsPrefix, 'carousel-item'));\n  return /*#__PURE__*/_jsx(Component, {\n    ref: ref,\n    ...props,\n    className: finalClassName\n  });\n});\nCarouselItem.displayName = 'CarouselItem';\nexport default CarouselItem;", "\"use client\";\n\nimport useEventCallback from '@restart/hooks/useEventCallback';\nimport useUpdateEffect from '@restart/hooks/useUpdateEffect';\nimport useCommittedRef from '@restart/hooks/useCommittedRef';\nimport useTimeout from '@restart/hooks/useTimeout';\nimport Anchor from '@restart/ui/Anchor';\nimport classNames from 'classnames';\nimport * as React from 'react';\nimport { useCallback, useEffect, useImperativeHandle, useMemo, useRef, useState } from 'react';\nimport { useUncontrolled } from 'uncontrollable';\nimport CarouselCaption from './CarouselCaption';\nimport CarouselItem from './CarouselItem';\nimport { map, forEach } from './ElementChildren';\nimport { useBootstrapPrefix, useIsRTL } from './ThemeProvider';\nimport transitionEndListener from './transitionEndListener';\nimport triggerBrowserReflow from './triggerBrowserReflow';\nimport TransitionWrapper from './TransitionWrapper';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nimport { jsxs as _jsxs } from \"react/jsx-runtime\";\nimport { Fragment as _Fragment } from \"react/jsx-runtime\";\nconst SWIPE_THRESHOLD = 40;\nfunction isVisible(element) {\n  if (!element || !element.style || !element.parentNode || !element.parentNode.style) {\n    return false;\n  }\n  const elementStyle = getComputedStyle(element);\n  return elementStyle.display !== 'none' && elementStyle.visibility !== 'hidden' && getComputedStyle(element.parentNode).display !== 'none';\n}\nconst Carousel =\n/*#__PURE__*/\n// eslint-disable-next-line react/display-name\nReact.forwardRef(({\n  defaultActiveIndex = 0,\n  ...uncontrolledProps\n}, ref) => {\n  const {\n    // Need to define the default \"as\" during prop destructuring to be compatible with styled-components github.com/react-bootstrap/react-bootstrap/issues/3595\n    as: Component = 'div',\n    bsPrefix,\n    slide = true,\n    fade = false,\n    controls = true,\n    indicators = true,\n    indicatorLabels = [],\n    activeIndex,\n    onSelect,\n    onSlide,\n    onSlid,\n    interval = 5000,\n    keyboard = true,\n    onKeyDown,\n    pause = 'hover',\n    onMouseOver,\n    onMouseOut,\n    wrap = true,\n    touch = true,\n    onTouchStart,\n    onTouchMove,\n    onTouchEnd,\n    prevIcon = /*#__PURE__*/_jsx(\"span\", {\n      \"aria-hidden\": \"true\",\n      className: \"carousel-control-prev-icon\"\n    }),\n    prevLabel = 'Previous',\n    nextIcon = /*#__PURE__*/_jsx(\"span\", {\n      \"aria-hidden\": \"true\",\n      className: \"carousel-control-next-icon\"\n    }),\n    nextLabel = 'Next',\n    variant,\n    className,\n    children,\n    ...props\n  } = useUncontrolled({\n    defaultActiveIndex,\n    ...uncontrolledProps\n  }, {\n    activeIndex: 'onSelect'\n  });\n  const prefix = useBootstrapPrefix(bsPrefix, 'carousel');\n  const isRTL = useIsRTL();\n  const nextDirectionRef = useRef(null);\n  const [direction, setDirection] = useState('next');\n  const [paused, setPaused] = useState(false);\n  const [isSliding, setIsSliding] = useState(false);\n  const [renderedActiveIndex, setRenderedActiveIndex] = useState(activeIndex || 0);\n  useEffect(() => {\n    if (!isSliding && activeIndex !== renderedActiveIndex) {\n      if (nextDirectionRef.current) {\n        setDirection(nextDirectionRef.current);\n      } else {\n        setDirection((activeIndex || 0) > renderedActiveIndex ? 'next' : 'prev');\n      }\n      if (slide) {\n        setIsSliding(true);\n      }\n      setRenderedActiveIndex(activeIndex || 0);\n    }\n  }, [activeIndex, isSliding, renderedActiveIndex, slide]);\n  useEffect(() => {\n    if (nextDirectionRef.current) {\n      nextDirectionRef.current = null;\n    }\n  });\n  let numChildren = 0;\n  let activeChildInterval;\n\n  // Iterate to grab all of the children's interval values\n  // (and count them, too)\n  forEach(children, (child, index) => {\n    ++numChildren;\n    if (index === activeIndex) {\n      activeChildInterval = child.props.interval;\n    }\n  });\n  const activeChildIntervalRef = useCommittedRef(activeChildInterval);\n  const prev = useCallback(event => {\n    if (isSliding) {\n      return;\n    }\n    let nextActiveIndex = renderedActiveIndex - 1;\n    if (nextActiveIndex < 0) {\n      if (!wrap) {\n        return;\n      }\n      nextActiveIndex = numChildren - 1;\n    }\n    nextDirectionRef.current = 'prev';\n    onSelect == null || onSelect(nextActiveIndex, event);\n  }, [isSliding, renderedActiveIndex, onSelect, wrap, numChildren]);\n\n  // This is used in the setInterval, so it should not invalidate.\n  const next = useEventCallback(event => {\n    if (isSliding) {\n      return;\n    }\n    let nextActiveIndex = renderedActiveIndex + 1;\n    if (nextActiveIndex >= numChildren) {\n      if (!wrap) {\n        return;\n      }\n      nextActiveIndex = 0;\n    }\n    nextDirectionRef.current = 'next';\n    onSelect == null || onSelect(nextActiveIndex, event);\n  });\n  const elementRef = useRef();\n  useImperativeHandle(ref, () => ({\n    element: elementRef.current,\n    prev,\n    next\n  }));\n\n  // This is used in the setInterval, so it should not invalidate.\n  const nextWhenVisible = useEventCallback(() => {\n    if (!document.hidden && isVisible(elementRef.current)) {\n      if (isRTL) {\n        prev();\n      } else {\n        next();\n      }\n    }\n  });\n  const slideDirection = direction === 'next' ? 'start' : 'end';\n  useUpdateEffect(() => {\n    if (slide) {\n      // These callbacks will be handled by the <Transition> callbacks.\n      return;\n    }\n    onSlide == null || onSlide(renderedActiveIndex, slideDirection);\n    onSlid == null || onSlid(renderedActiveIndex, slideDirection);\n  }, [renderedActiveIndex]);\n  const orderClassName = `${prefix}-item-${direction}`;\n  const directionalClassName = `${prefix}-item-${slideDirection}`;\n  const handleEnter = useCallback(node => {\n    triggerBrowserReflow(node);\n    onSlide == null || onSlide(renderedActiveIndex, slideDirection);\n  }, [onSlide, renderedActiveIndex, slideDirection]);\n  const handleEntered = useCallback(() => {\n    setIsSliding(false);\n    onSlid == null || onSlid(renderedActiveIndex, slideDirection);\n  }, [onSlid, renderedActiveIndex, slideDirection]);\n  const handleKeyDown = useCallback(event => {\n    if (keyboard && !/input|textarea/i.test(event.target.tagName)) {\n      switch (event.key) {\n        case 'ArrowLeft':\n          event.preventDefault();\n          if (isRTL) {\n            next(event);\n          } else {\n            prev(event);\n          }\n          return;\n        case 'ArrowRight':\n          event.preventDefault();\n          if (isRTL) {\n            prev(event);\n          } else {\n            next(event);\n          }\n          return;\n        default:\n      }\n    }\n    onKeyDown == null || onKeyDown(event);\n  }, [keyboard, onKeyDown, prev, next, isRTL]);\n  const handleMouseOver = useCallback(event => {\n    if (pause === 'hover') {\n      setPaused(true);\n    }\n    onMouseOver == null || onMouseOver(event);\n  }, [pause, onMouseOver]);\n  const handleMouseOut = useCallback(event => {\n    setPaused(false);\n    onMouseOut == null || onMouseOut(event);\n  }, [onMouseOut]);\n  const touchStartXRef = useRef(0);\n  const touchDeltaXRef = useRef(0);\n  const touchUnpauseTimeout = useTimeout();\n  const handleTouchStart = useCallback(event => {\n    touchStartXRef.current = event.touches[0].clientX;\n    touchDeltaXRef.current = 0;\n    if (pause === 'hover') {\n      setPaused(true);\n    }\n    onTouchStart == null || onTouchStart(event);\n  }, [pause, onTouchStart]);\n  const handleTouchMove = useCallback(event => {\n    if (event.touches && event.touches.length > 1) {\n      touchDeltaXRef.current = 0;\n    } else {\n      touchDeltaXRef.current = event.touches[0].clientX - touchStartXRef.current;\n    }\n    onTouchMove == null || onTouchMove(event);\n  }, [onTouchMove]);\n  const handleTouchEnd = useCallback(event => {\n    if (touch) {\n      const touchDeltaX = touchDeltaXRef.current;\n      if (Math.abs(touchDeltaX) > SWIPE_THRESHOLD) {\n        if (touchDeltaX > 0) {\n          prev(event);\n        } else {\n          next(event);\n        }\n      }\n    }\n    if (pause === 'hover') {\n      touchUnpauseTimeout.set(() => {\n        setPaused(false);\n      }, interval || undefined);\n    }\n    onTouchEnd == null || onTouchEnd(event);\n  }, [touch, pause, prev, next, touchUnpauseTimeout, interval, onTouchEnd]);\n  const shouldPlay = interval != null && !paused && !isSliding;\n  const intervalHandleRef = useRef();\n  useEffect(() => {\n    var _ref, _activeChildIntervalR;\n    if (!shouldPlay) {\n      return undefined;\n    }\n    const nextFunc = isRTL ? prev : next;\n    intervalHandleRef.current = window.setInterval(document.visibilityState ? nextWhenVisible : nextFunc, (_ref = (_activeChildIntervalR = activeChildIntervalRef.current) != null ? _activeChildIntervalR : interval) != null ? _ref : undefined);\n    return () => {\n      if (intervalHandleRef.current !== null) {\n        clearInterval(intervalHandleRef.current);\n      }\n    };\n  }, [shouldPlay, prev, next, activeChildIntervalRef, interval, nextWhenVisible, isRTL]);\n  const indicatorOnClicks = useMemo(() => indicators && Array.from({\n    length: numChildren\n  }, (_, index) => event => {\n    onSelect == null || onSelect(index, event);\n  }), [indicators, numChildren, onSelect]);\n  return /*#__PURE__*/_jsxs(Component, {\n    ref: elementRef,\n    ...props,\n    onKeyDown: handleKeyDown,\n    onMouseOver: handleMouseOver,\n    onMouseOut: handleMouseOut,\n    onTouchStart: handleTouchStart,\n    onTouchMove: handleTouchMove,\n    onTouchEnd: handleTouchEnd,\n    className: classNames(className, prefix, slide && 'slide', fade && `${prefix}-fade`, variant && `${prefix}-${variant}`),\n    children: [indicators && /*#__PURE__*/_jsx(\"div\", {\n      className: `${prefix}-indicators`,\n      children: map(children, (_, index) => /*#__PURE__*/_jsx(\"button\", {\n        type: \"button\",\n        \"data-bs-target\": \"\" // Bootstrap requires this in their css.\n        ,\n        \"aria-label\": indicatorLabels != null && indicatorLabels.length ? indicatorLabels[index] : `Slide ${index + 1}`,\n        className: index === renderedActiveIndex ? 'active' : undefined,\n        onClick: indicatorOnClicks ? indicatorOnClicks[index] : undefined,\n        \"aria-current\": index === renderedActiveIndex\n      }, index))\n    }), /*#__PURE__*/_jsx(\"div\", {\n      className: `${prefix}-inner`,\n      children: map(children, (child, index) => {\n        const isActive = index === renderedActiveIndex;\n        return slide ? /*#__PURE__*/_jsx(TransitionWrapper, {\n          in: isActive,\n          onEnter: isActive ? handleEnter : undefined,\n          onEntered: isActive ? handleEntered : undefined,\n          addEndListener: transitionEndListener,\n          children: (status, innerProps) => /*#__PURE__*/React.cloneElement(child, {\n            ...innerProps,\n            className: classNames(child.props.className, isActive && status !== 'entered' && orderClassName, (status === 'entered' || status === 'exiting') && 'active', (status === 'entering' || status === 'exiting') && directionalClassName)\n          })\n        }) : ( /*#__PURE__*/React.cloneElement(child, {\n          className: classNames(child.props.className, isActive && 'active')\n        }));\n      })\n    }), controls && /*#__PURE__*/_jsxs(_Fragment, {\n      children: [(wrap || activeIndex !== 0) && /*#__PURE__*/_jsxs(Anchor, {\n        className: `${prefix}-control-prev`,\n        onClick: prev,\n        children: [prevIcon, prevLabel && /*#__PURE__*/_jsx(\"span\", {\n          className: \"visually-hidden\",\n          children: prevLabel\n        })]\n      }), (wrap || activeIndex !== numChildren - 1) && /*#__PURE__*/_jsxs(Anchor, {\n        className: `${prefix}-control-next`,\n        onClick: next,\n        children: [nextIcon, nextLabel && /*#__PURE__*/_jsx(\"span\", {\n          className: \"visually-hidden\",\n          children: nextLabel\n        })]\n      })]\n    })]\n  });\n});\nCarousel.displayName = 'Carousel';\nexport default Object.assign(Carousel, {\n  Caption: CarouselCaption,\n  Item: CarouselItem\n});", "//Import Library\r\nimport React, { useEffect, useState } from 'react';\r\nimport { Container, Row } from \"react-bootstrap\";\r\nimport _ from 'lodash';\r\nimport Carousel from 'react-bootstrap/Carousel'\r\nimport Col from \"react-bootstrap/Col\";\r\n\r\n//Import services/components\r\nimport AnnouncementItem from \"./AnnouncementItem\";\r\nimport apiService from \"../../services/apiService\";\r\n\r\n\r\n//TODO: Need to use RKISingleItemCarousel component to reuse our exisiting component\r\ninterface ListOfAnnouncementItemProps {\r\n  announcements: any[][];\r\n}\r\n\r\nfunction ListOfAnnouncementItem(props: ListOfAnnouncementItemProps) {\r\n  const { announcements } = props;\r\n  return (\r\n    <div>\r\n      {announcements.map((item: any, index: number) => {\r\n        return (\r\n          <Row className=\"announcementItem\" key={index}>\r\n            <AnnouncementItem item={item} />\r\n          </Row>\r\n        )\r\n      })}\r\n    </div>\r\n  )\r\n}\r\n\r\ninterface AnnouncementProps {\r\n  t: (key: string) => string;\r\n}\r\n\r\nfunction Announcement({ t }: AnnouncementProps) {\r\n  const [announcements, setAnnouncements] = useState<any[][]>([]);\r\n\r\n  const [cindex, setCindex] = useState(0);\r\n\r\n  const [carouselItemCount] = useState(3);\r\n\r\n  const setEmptyNotice = () => {\r\n    setAnnouncements([]);\r\n  };\r\n\r\n  const updatesParams = {\r\n    query: { show_as_announcement: true },\r\n    sort: { created_at: \"desc\" },\r\n    limit: 9,\r\n    select: \"-created_at -update_type -contact_details -document -end_date -link -media -reply -start_date -updated_at -user\"\r\n  };\r\n\r\n  const fetchAnnouncements = async (params = updatesParams) => {\r\n    const response = await apiService.get('/updates', params);\r\n    if (response && response.data && response.data.length > 0) {\r\n      const partition = _.chunk(response.data, 3);\r\n      setAnnouncements(partition)\r\n    } else {\r\n      setEmptyNotice()\r\n    }\r\n  };\r\n\r\n  useEffect(() => {\r\n    fetchAnnouncements();\r\n  }, [])\r\n\r\n  const toggleCarousel = (direction: 'next' | 'prev') => {\r\n    let index = cindex;\r\n    const [_min, max] = [0, carouselItemCount - 1];\r\n\r\n    if (direction === 'next' && index < max) {\r\n      index++\r\n    }\r\n    else if (direction === 'prev' && index > 0 ) {\r\n      index--\r\n    }\r\n\r\n\r\n    if ((announcements.length - index) === 1) {\r\n      index = announcements.length - 1;\r\n    }\r\n\r\n    if ((announcements.length - index) === 0) {\r\n      index = 1;\r\n    }\r\n    setCindex(index);\r\n  };\r\n\r\n\r\n  return (\r\n    <div className=\"announcements\">\r\n      {announcements && announcements.length > 0 ? (\r\n        <>\r\n          <Container fluid>\r\n            <Row>\r\n              <Col xs={10} className=\"p-0\">\r\n                <h4>{t('announcements')}</h4>\r\n              </Col>\r\n              {announcements && announcements.length > 1 ?\r\n                <Col xs={2} className=\"text-end carousel-control p-0\">\r\n                  <div className=\"carousel-navigation\">\r\n                    <a className=\"left carousel-control\" onClick={() => toggleCarousel('prev')}>\r\n                      <i className=\"fa fa-chevron-left\" />\r\n                    </a>\r\n                    <a className=\"right carousel-control\" onClick={() => toggleCarousel('next')}>\r\n                      <i className=\"fa fa-chevron-right\" />\r\n                    </a>\r\n                  </div>\r\n                </Col>\r\n                : null}\r\n            </Row>\r\n          </Container>\r\n          <Container fluid>\r\n            <Row>\r\n              <Col xs={12} className=\"p-0\">\r\n                <Carousel indicators={false} controls={false} interval={null} activeIndex={cindex}>\r\n                  {announcements.map((item: any, index: number) => {\r\n                    return (\r\n                      <Carousel.Item key={index}>\r\n                        <ListOfAnnouncementItem announcements={item} />\r\n                      </Carousel.Item>\r\n                    )\r\n                  })}\r\n                </Carousel>\r\n              </Col>\r\n            </Row>\r\n          </Container>\r\n        </>\r\n      ) : (\r\n          <Container fluid={true}>\r\n            <Row>\r\n              <Col xs={10} className=\"p-0\">\r\n                <h4>{t('announcements')}</h4>\r\n              </Col>\r\n              <Col xs={12} className=\"p-0\">\r\n                <div className=\"border p-3\">{t(\"NoAnnouncementsavailabletodisplay\")}</div><br /></Col>\r\n            </Row>\r\n          </Container>\r\n        )}\r\n    </div>\r\n  )\r\n}\r\n\r\nexport default Announcement;"], "names": ["AnnouncementItem", "props", "item", "indexNew", "images", "length", "parent", "type", "Col", "className", "xs", "Link", "href", "as", "_id", "img", "src", "process", "alt", "i", "div", "title", "p", "description", "getTrimmedString", "document", "createElement", "innerHTML", "html", "string", "textContent", "innerText", "truncate<PERSON><PERSON>th", "substring", "CarouselCaption", "React", "ref", "bsPrefix", "Component", "useBootstrapPrefix", "_jsx", "classNames", "CarouselItem", "finalClassName", "displayName", "Carousel", "activeChildInterval", "defaultActiveIndex", "uncontrolledProps", "slide", "fade", "controls", "indicators", "indicatorLabels", "activeIndex", "onSelect", "onSlide", "onSlid", "interval", "keyboard", "onKeyDown", "pause", "onMouseOver", "onMouseOut", "wrap", "touch", "onTouchStart", "onTouchMove", "onTouchEnd", "prevIcon", "prevLabel", "nextIcon", "next<PERSON><PERSON><PERSON>", "variant", "children", "useUncontrolled", "prefix", "isRTL", "useIsRTL", "nextDirectionRef", "useRef", "direction", "setDirection", "useState", "paused", "setPaused", "isSliding", "setIsSliding", "renderedActiveIndex", "setRenderedActiveIndex", "useEffect", "current", "numC<PERSON><PERSON>n", "for<PERSON>ach", "child", "index", "activeChildIntervalRef", "useCommittedRef", "prev", "useCallback", "event", "nextActiveIndex", "next", "useEventCallback", "elementRef", "useImperativeHandle", "element", "nextWhenVisible", "hidden", "isVisible", "style", "parentNode", "elementStyle", "getComputedStyle", "display", "visibility", "slideDirection", "useUpdateEffect", "orderClassName", "directionalClassName", "handleEnter", "node", "triggerBrowserReflow", "handleEntered", "handleKeyDown", "test", "target", "tagName", "key", "preventDefault", "handleMouseOver", "handleMouseOut", "touchStartXRef", "touchDeltaXRef", "touchUnpauseTimeout", "useTimeout", "handleTouchStart", "touches", "clientX", "handleTouchMove", "handleTouchEnd", "touchDeltaX", "Math", "abs", "set", "undefined", "shouldPlay", "intervalHandleRef", "_ref", "_activeChildIntervalR", "nextFunc", "window", "setInterval", "visibilityState", "clearInterval", "indicatorOnClicks", "useMemo", "Array", "from", "_", "_jsxs", "map", "onClick", "isActive", "TransitionWrapper", "in", "onEnter", "onEntered", "addEndListener", "transitionEndListener", "status", "innerProps", "_Fragment", "<PERSON><PERSON>", "Object", "assign", "Caption", "<PERSON><PERSON>", "ListOfAnnouncementItem", "announcements", "Row", "Announcement", "t", "setAnnouncements", "cindex", "setCindex", "carouselItemCount", "setEmptyNotice", "updatesParams", "query", "show_as_announcement", "sort", "created_at", "limit", "select", "fetchAnnouncements", "params", "response", "apiService", "get", "data", "partition", "toggleCarousel", "_min", "max", "Container", "fluid", "h4", "a", "br"], "sourceRoot": "", "ignoreList": [2, 3, 4, 5]}