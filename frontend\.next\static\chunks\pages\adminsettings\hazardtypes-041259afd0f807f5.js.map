{"version": 3, "file": "static/chunks/pages/adminsettings/hazardtypes-041259afd0f807f5.js", "mappings": "0qBAGA,IAAMA,EAAS,aACFC,EAAmBC,CAAAA,EAAAA,EAAAA,CAAAA,CAAoBA,CAAC,CACnDC,sBAAuB,KACjBC,EAAMC,WAAW,IAAID,EAAMC,WAAW,CAACC,YAAY,IAAIF,EAAMC,WAAW,CAACC,YAAY,CAACN,EAAO,CAKnGO,CALqG,kBAKjF,kBACtB,GAAG,EAE0BL,CAAAA,EAAAA,EAAAA,CAAAA,CAAoBA,CAAC,CAChDC,sBAAuB,KACjBC,EAAMC,WAAW,IAAID,EAAMC,WAAW,CAACG,OAAO,IAAIJ,EAAMC,WAAW,CAACG,OAAO,CAACR,EAAO,CAKzFO,CAL2F,kBAKvE,eACtB,GAAG,EAEmCL,CAAAA,EAAAA,EAAAA,CAAAA,CAAoBA,CAAC,CACzDC,sBAAuB,KACjBC,EAAMC,WAAW,IAAID,EAAMC,WAAW,CAACI,iBAAiB,IAAIL,EAAMC,WAAW,CAACI,iBAAiB,CAACT,EAAO,CAK7GO,CAL+G,kBAK3F,wBACtB,GAAG,EAE8BL,CAAAA,EAAAA,EAAAA,CAAAA,CAAoBA,CAAC,CACpDC,sBAAuB,GACjBC,IAAMC,WAAW,IAAID,EAAMC,WAAW,CAACK,YAAY,IAAIN,EAAMC,WAAW,CAACK,YAAY,CAACV,EAAO,CAKnGO,CALqG,kBAKjF,mBACtB,GAAG,EAE4BL,CAAAA,EAAAA,EAAAA,CAAAA,CAAoBA,CAAC,CAClDC,sBAAuB,KACjBC,EAAMC,WAAW,IAAID,EAAMC,WAAW,CAACM,SAAS,IAAIP,EAAMC,WAAW,CAACM,SAAS,CAACX,EAAO,CAK7FO,CAL+F,kBAK3E,iBACtB,GAAG,EAEqCL,CAAAA,EAAAA,EAAAA,CAAAA,CAAoBA,CAAC,CAC3DC,sBAAuB,KACjBC,EAAMC,WAAW,IAAID,EAAMC,WAAW,CAACO,uBAAuB,IAAIR,EAAMC,WAAW,CAACO,uBAAuB,CAACZ,EAAO,CAKzHO,CAL2H,kBAKvG,0BACtB,GAAG,EAEiCL,CAAAA,EAAAA,EAAAA,CAAAA,CAAoBA,CAAC,CACvDC,sBAAuB,KACjBC,EAAMC,WAAW,IAAID,EAAMC,WAAW,CAACO,uBAAuB,IAAIR,EAAMC,WAAW,CAACO,uBAAuB,CAACZ,EAAO,CAKzHO,CAL2H,kBAKvG,sBACtB,GAAG,EAC0BL,CAAAA,EAAAA,EAAAA,CAAAA,CAAoBA,CAAC,CAChDC,sBAAuB,KACjBC,EAAMC,WAAW,IAAID,EAAMC,WAAW,CAACQ,MAAM,IAAIT,EAAMC,WAAW,CAACQ,MAAM,CAACb,EAAO,CAKvFO,CALyF,kBAKrE,eACtB,GAAG,EAE8BL,CAAAA,EAAAA,EAAAA,CAAAA,CAAoBA,CAAC,CACpDC,sBAAwBC,KAClBA,EAAMC,WAAW,IAAID,EAAMC,WAAW,CAACS,WAAW,IAAIV,EAAMC,WAAW,CAACS,WAAW,CAACd,EAAO,CAKjGO,CALmG,kBAK/E,mBACtB,GAEaQ,EAA6Bb,CAAAA,EAAAA,EAAAA,CAAAA,CAAoBA,CAAC,CAC7DC,sBAAuB,KACjBC,EAAMC,WAAW,IAAID,EAAMC,WAAW,CAACW,WAAW,IAAIZ,EAAMC,WAAW,CAACW,WAAW,CAAChB,EAAO,CAKjGO,CALmG,kBAK/E,4BACtB,GAAG,EAEuCL,CAAAA,EAAAA,EAAAA,CAAAA,CAAoBA,CAAC,CAC7DC,sBAAwBC,KAClBA,EAAMC,WAAW,IAAID,EAAMC,WAAW,CAACY,mBAAmB,IAAIb,EAAMC,WAAW,CAACY,mBAAmB,CAACjB,EAAO,CAKjHO,CALmH,kBAK/F,4BACtB,GAAG,EAEoCL,CAAAA,EAAAA,EAAAA,CAAAA,CAAoBA,CAAC,CAC1DC,sBAAwBC,KAClBA,EAAMC,WAAW,IAAID,EAAMC,WAAW,CAACa,gBAAgB,IAAId,EAAMC,WAAW,CAACa,gBAAgB,CAAClB,EAAO,CAK3GO,CAL6G,kBAKzF,yBACtB,GAAG,EAEkCL,CAAAA,EAAAA,EAAAA,CAAAA,CAAoBA,CAAC,CACxDC,sBAAuB,KACjBC,EAAMC,WAAW,IAAID,EAAMC,WAAW,CAACc,gBAAgB,IAAIf,EAAMC,WAAW,CAACc,gBAAgB,CAACnB,EAAO,CAK3GO,CAL6G,kBAKzF,uBACtB,GAAG,EAEgCL,CAAAA,EAAAA,EAAAA,CAAAA,CAAoBA,CAAC,CACtDC,sBAAuB,KACjBC,EAAMC,WAAW,IAAID,EAAMC,WAAW,CAACe,cAAc,IAAIhB,EAAMC,WAAW,CAACe,cAAc,CAACpB,EAAO,CAKvGO,CALyG,kBAKrF,qBACtB,GAAG,EAE0BL,CAAAA,EAAAA,EAAAA,CAAAA,CAAoBA,CAAC,CAChDC,sBAAuB,KACjBC,EAAMC,WAAW,IAAID,EAAMC,WAAW,CAACgB,MAAM,IAAIjB,EAAMC,WAAW,CAACgB,MAAM,CAACrB,EAAO,CAKvFO,CALyF,kBAKrE,eACtB,GAAG,EAE6BL,CAAAA,EAAAA,EAAAA,CAAAA,CAAoBA,CAAC,CACnDC,sBAAuB,KACjBC,EAAMC,WAAW,IAAID,EAAMC,WAAW,CAACiB,UAAU,IAAIlB,EAAMC,WAAW,CAACiB,UAAU,CAACtB,EAAO,CAK/FO,CALiG,kBAK7E,kBACtB,GAAG,EAE4BL,CAAAA,EAAAA,EAAAA,CAAAA,CAAoBA,CAAC,CAClDC,sBAAuB,KACjBC,EAAMC,WAAW,IAAID,EAAMC,WAAW,CAACkB,QAAQ,IAAInB,EAAMC,WAAW,CAACkB,QAAQ,CAACvB,EAAO,CAK3FO,CAL6F,kBAKzE,iBACtB,GAAG,EAE8BL,CAAAA,EAAAA,EAAAA,CAAAA,CAAoBA,CAAC,CACpDC,sBAAuB,KACjBC,EAAMC,WAAW,IAAID,EAAMC,WAAW,CAACmB,WAAW,IAAIpB,EAAMC,WAAW,CAACmB,WAAW,CAACxB,EAAO,CAKjGO,CALmG,kBAK/E,mBACtB,GAAG,EAEwBL,CAAAA,EAAAA,EAAAA,CAAAA,CAAoBA,CAAC,CAC9CC,sBAAuB,KACjBC,EAAMC,WAAW,IAAID,EAAMC,WAAW,CAACoB,KAAK,IAAIrB,EAAMC,WAAW,CAACoB,KAAK,CAACzB,EAAO,CAKrFO,CALuF,kBAKnE,aACtB,GAAG,EAE8BL,CAAAA,EAAAA,EAAAA,CAAAA,CAAoBA,CAAC,CACpDC,sBAAuB,KACjBC,EAAMC,WAAW,IAAID,EAAMC,WAAW,CAACqB,WAAW,IAAItB,EAAMC,WAAW,CAACqB,WAAW,CAAC1B,EAAO,CAKjGO,CALmG,kBAK/E,mBACtB,GAAG,EAE8BL,CAAAA,EAAAA,EAAAA,CAAAA,CAAoBA,CAAC,CACpDC,sBAAuB,KACjBC,EAAMC,WAAW,IAAID,EAAMC,WAAW,CAACsB,YAAY,IAAIvB,EAAMC,WAAW,CAACsB,YAAY,CAAC3B,EAAO,CAKnGO,CALqG,kBAKjF,mBACtB,GAAG,EAE0BL,CAAAA,EAAAA,EAAAA,CAAAA,CAAoBA,CAAC,CAChDC,sBAAuB,GACrB,EAAIC,EAAMC,WAAW,IAAID,EAAMC,WAAW,CAACuB,SAAS,IAAIxB,EAAMC,WAAW,CAACuB,SAAS,CAAC5B,EAAO,IAAII,EAAMC,WAAW,CAACwB,OAAO,IAAIzB,EAAMC,WAAW,CAACwB,OAAO,CAAC7B,EAAO,IAAGI,EAAMC,WAAW,CAACyB,KAAK,IAAI1B,EAAMC,WAAW,CAACyB,KAAK,CAAC9B,EAAO,IAAGI,EAAMC,WAAW,CAAC0B,MAAM,IAAI3B,EAAMC,WAAW,CAAC0B,MAAM,CAAC/B,EAAO,IAAGI,EAAMC,WAAW,CAACW,WAAW,IAAIZ,EAAMC,WAAW,CAACW,WAAW,CAAChB,EAAO,IAAGI,EAAMC,WAAW,CAAC2B,MAAM,IAAI5B,EAAMC,WAAW,CAAC2B,MAAM,CAAChC,EAAO,EAAE,CAG5Z,EAETO,mBAAoB,eACtB,GAAG,EAEYN,gBAAgBA,EAAC,4JClFhC,MAlIwB,IACpB,GAAM,CAACgC,EAAWC,EAAe,CAAGC,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAC,EAAE,EACzC,EAAGC,EAAW,CAAGD,CAAAA,EAAAA,EAAAA,CAgIGE,EAAC,KAhIJF,CAAQA,EAAC,GAC1B,CAACG,EAAWC,EAAa,CAAGJ,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAC,GACrC,CAACK,EAASC,EAAW,CAAGN,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAC,IACjC,CAACO,EAAaC,EAAS,CAAGR,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,EAAC,GACnC,CAACS,EAAkBC,EAAoB,CAAGV,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAC,CAAC,GACpD,GAAEW,CAAC,CAAE,CAAGC,CAAAA,EAAAA,EAAAA,EAAAA,CAAcA,CAAC,UAGvBC,EAAU,CACZ,CACIC,KAAMH,EAAE,wCACRI,SAAU,OACd,EACA,CACID,KAAMH,EAAE,iCACRI,SAAU,OACVC,KAAM,GAAYC,EAAEC,IAAI,EAE5B,CACIJ,KAAMH,EAAE,eACRI,SAAU,cACVC,KAAM,GAAYC,EAAEE,WAAW,CAACC,OAAO,CAAC,WAAY,GACxD,EACA,CACIN,KAAMH,EAAE,UACRI,SAAU,GACVC,KAAM,GACF,WAACK,MAAAA,WACG,UAACC,IAAIA,CAACC,KAAK,6BAA6BC,GAAI,OAAvCF,6BAAiF,OAANL,EAAEQ,GAAG,WAEjF,UAACC,IAAAA,CAAEC,UAAU,uBAEV,OAEP,UAACC,IAAAA,CAAEC,QAAS,IAAMC,EAAWb,YACzB,UAACS,IAAAA,CAAEC,UAAU,8BAI7B,EACH,CAEDI,CAAAA,EAAAA,EAAAA,SAAAA,CAASA,CAAC,KACNC,GACJ,EAAG,EAAE,EAEL,IAAMC,EAAmB,CACrBC,KAAM,CAAEC,MAAO,KAAM,EACrBC,MAAO/B,EACPgC,KAAM,EACNC,MAAO,CAAC,CACZ,EAEMN,EAAqB,UACvB/B,GAAW,GACX,IAAMsC,EAAW,MAAMC,EAAAA,CAAUA,CAACC,GAAG,CAAC,cAAeR,GACjDM,GAAYA,EAASG,IAAI,EAAIH,EAASG,IAAI,CAACC,MAAM,CAAG,GAAG,CACvD5C,EAAewC,EAASG,IAAI,EAC5BtC,EAAamC,EAASK,UAAU,EAChC3C,GAAW,GAEnB,EAQM4C,EAAsB,MAAOC,EAAiBT,KAChDJ,EAAiBG,KAAK,CAAGU,EACzBb,EAAiBI,IAAI,CAAGA,EACxBpC,GAAW,GACX,IAAMsC,EAAW,MAAMC,EAAAA,CAAUA,CAACC,GAAG,CAAC,cAAeR,GACjDM,GAAYA,EAASG,IAAI,EAAIH,EAASG,IAAI,CAACC,MAAM,CAAG,GAAG,CACvD5C,EAAewC,EAASG,IAAI,EAC5BpC,EAAWwC,GACX7C,EAAW,IAEnB,EAEM6B,EAAa,MAAOiB,IACtBrC,EAAoBqC,EAAItB,GAAG,EAC3BjB,GAAS,EACb,EAEMwC,EAAe,UACjB,GAAI,CACA,MAAMR,EAAAA,CAAUA,CAACS,MAAM,CAAC,eAAgC,OAAjBxC,IACvCuB,IACAxB,GAAS,GACT0C,EAAAA,EAAKA,CAACC,OAAO,CAACxC,EAAE,gEACpB,CAAE,MAAOyC,EAAO,CACZF,EAAAA,EAAKA,CAACE,KAAK,CAACzC,EAAE,0DAClB,CACJ,EAEM0C,EAAY,IAAM7C,GAAS,GAEjC,MACI,WAACa,MAAAA,WACG,WAACiC,EAAAA,CAAKA,CAAAA,CAACC,KAAMhD,EAAaiD,OAAQH,YAC9B,UAACC,EAAAA,CAAKA,CAACG,MAAM,EAACC,WAAW,aACrB,UAACJ,EAAAA,CAAKA,CAACK,KAAK,WAAEhD,EAAE,iDAEpB,UAAC2C,EAAAA,CAAKA,CAACM,IAAI,WAAEjD,EAAE,mEACf,WAAC2C,EAAAA,CAAKA,CAACO,MAAM,YACT,UAACC,EAAAA,CAAMA,CAAAA,CAACC,QAAQ,YAAYlC,QAASwB,WAChC1C,EAAE,YAEP,UAACmD,EAAAA,CAAMA,CAAAA,CAACC,QAAQ,UAAUlC,QAASmB,WAC9BrC,EAAE,eAKf,UAACqD,EAAAA,CAAQA,CAAAA,CACLnD,QAASA,EACT6B,KAAM5C,EACNK,UAAWA,EACX8D,UAAW,GACXpB,oBAAqBA,EACrBqB,iBA3Dc7B,CA2DI6B,GA1D1BjC,EAAiBG,KAAK,CAAG/B,EACzB4B,EAAiBI,IAAI,CAAGA,EACxBL,GACJ,MA2DJ,6GCtGA,SAASgC,EAASG,CAAoB,EACpC,GAAM,GAAExD,CAAC,CAAE,CAAGC,CAAAA,EAAAA,EAAAA,EAAAA,CAAcA,CAAC,UACvBwD,EAA6B,CACjCC,gBAAiB1D,EAAE,cACnB,EACI,SACJE,CAAO,MACP6B,CAAI,WACJvC,CAAS,uBACTmE,CAAqB,WACrBC,CAAS,oBACTC,CAAkB,CAClB3B,qBAAmB,kBACnBqB,CAAgB,aAChBO,CAAW,oBACXC,CAAkB,gBAClBC,CAAc,SACdC,CAAO,WACPX,CAAS,sBACTY,CAAoB,mBACpBC,CAAiB,CACjBC,YAAU,CACVC,QAAM,kBACNC,CAAgB,cAChBC,CAAY,CAEZ,CADA,EACGC,EACJ,CAAGhB,EAGEiB,EAAiB,4BACrBhB,EACAiB,gBAAiB1E,EAAE,IAP0C,MAQ7D2E,UAAU,UACVzE,EACA6B,KAAMA,GAAQ,EAAE,CAChB6C,OAAO,EACPC,2BAA4BlB,EAC5BmB,UAAWlB,EACXmB,gBAAiBd,qBACjBJ,EACAmB,YAAY,EACZC,iBAAkB3B,EAClB4B,kBAAmBnB,GAA0C,GAC7DoB,eADwCpB,cACVD,GAA4B,CAAC,GAAI,GAAI,GAAI,CAA3BA,EAA+B,GAAG,CAC9EsB,oBAAqB5F,EACrB6F,oBAAqBnD,EACrBoD,aAAc/B,EACdS,iBACAE,yCACAC,EACAoB,kBAAmB,UAACC,EAAAA,CAAYA,CAAAA,CAAAA,GAChCC,SAAU,UAAC1E,IAAAA,CAAEC,UAAU,6CACvBoD,SACAC,eACAE,mBACAD,EACAtD,UAAW,WACb,EACA,MACE,UAAC0E,EAAAA,EAASA,CAAAA,CAAE,GAAGjB,CAAc,EAEjC,CAEApB,EAASsC,YAAY,CAAG,CACtBb,WAAW,EACXE,YAAY,EACZxF,UAAW,KACX8D,WAAW,EACXY,qBAAsB,KACtBC,mBAAmB,EACnBC,YAAY,EACZE,kBAAkB,CACpB,EAEA,MAAejB,QAAQA,EAAC,qMC1DxB,MAzCyBuC,QAiCjBtI,EAAAA,EAhCN,GAAM,GAAE0C,CAAC,CAAE,CAAGC,CAAAA,EAAAA,EAAAA,EAAAA,CAAcA,CAAC,UACvB4F,EAAsB,CAuCAC,EAAC,CArCzB,WAACC,EAAAA,CAASA,CAAAA,CAACC,MAAO,CAAEC,UAAW,QAAS,EAAGC,KAAK,IAAClF,UAAU,gBACzD,UAACmF,EAAAA,CAAGA,CAAAA,UACF,UAACC,EAAAA,CAAGA,CAAAA,CAACC,GAAI,YACP,UAACC,EAAAA,CAAWA,CAAAA,CAAC9E,MAAQxB,EAAE,8CAG3B,UAACmG,EAAAA,CAAGA,CAAAA,UACF,UAACC,EAAAA,CAAGA,CAAAA,CAACC,GAAI,YACP,UAAC1F,IAAIA,CACHC,KAAK,6BACLC,GAAG,OAFAF,uCAIH,UAACwC,EAAAA,CAAMA,CAAAA,CAACC,QAAQ,YAAYmD,KAAK,cAC9BvG,EAAE,yCAKX,UAACmG,EAAAA,CAAGA,CAAAA,CAACnF,UAAU,gBACb,UAACoF,EAAAA,CAAGA,CAAAA,CAACC,GAAI,YACP,UAAC9G,EAAAA,OAAeA,CAAAA,CAAAA,UAOpBiH,EAAqBC,CAAAA,EAAAA,EAAAA,iBAAAA,CAAiBA,CAAC,IAAM,UAACZ,EAAAA,CAAAA,IAC9CvI,EAAYoJ,CAAAA,EAAAA,EAAAA,EAAAA,CAAWA,CAAC,GAAWpJ,SACzC,IAAI,GAAEA,GAAAA,OAAAA,EAAAA,EAAOC,IAAPD,OAAOC,GAAPD,OAAAA,EAAAA,EAAoBU,WAAAA,EAApBV,KAAAA,EAAAA,CAAiC,CAAC,GAAlCA,UAA+C,EAInD,UAACkJ,EAAAA,CAAAA,GAHM,UAACG,EAAAA,OAAeA,CAAAA,CAAAA,EAK3B,6ECpDe,SAASA,EAAgBf,CAAW,EAC/C,MACE,UAAClF,MAAAA,CAAIM,UAAU,sDACb,UAACN,MAAAA,CAAIM,UAAU,mBAAU,yCAG/B,gECFa,SAASsF,EAAY9C,CAAuB,EACzD,MACE,UAACoD,KAAAA,CAAG5F,UAAU,wBAAgBwC,EAAMhC,KAAK,EAE7C,mBCPA,4CACA,6BACA,WACA,OAAe,EAAQ,KAAwD,CAC/E,EACA,SAFsB", "sources": ["webpack://_N_E/./pages/adminsettings/permissions.tsx", "webpack://_N_E/./pages/adminsettings/hazardtypes/hazardTypeTable.tsx", "webpack://_N_E/./components/common/RKITable.tsx", "webpack://_N_E/./pages/adminsettings/hazardtypes/index.tsx", "webpack://_N_E/./pages/rNoAccess.tsx", "webpack://_N_E/./components/common/PageHeading.tsx", "webpack://_N_E/?c010"], "sourcesContent": ["//Import services/components\r\nimport connectedAuthWrapper from 'redux-auth-wrapper/connectedAuthWrapper';\r\n\r\nconst create = \"create:any\";\r\nexport const canAddAreaOfWork = connectedAuthWrapper({\r\n  authenticatedSelector: (state) => {\r\n    if (state.permissions && state.permissions.area_of_work && state.permissions.area_of_work[create]) {\r\n      return true;\r\n    }\r\n    return false;\r\n  },\r\n  wrapperDisplayName: 'CanAddAreaOfWork',\r\n});\r\n\r\nexport const canAddCountry = connectedAuthWrapper({\r\n  authenticatedSelector: (state) => {\r\n    if (state.permissions && state.permissions.country && state.permissions.country[create]) {\r\n      return true;\r\n    }\r\n    return false;\r\n  },\r\n  wrapperDisplayName: 'CanAddCountry',\r\n});\r\n\r\nexport const canAddDeploymentStatus = connectedAuthWrapper({\r\n  authenticatedSelector: (state) => {\r\n    if (state.permissions && state.permissions.deployment_status && state.permissions.deployment_status[create]) {\r\n      return true;\r\n    }\r\n    return false;\r\n  },\r\n  wrapperDisplayName: 'CanAddDeploymentStatus',\r\n});\r\n\r\nexport const canAddEventStatus = connectedAuthWrapper({\r\n  authenticatedSelector: (state) => {\r\n    if (state.permissions && state.permissions.event_status && state.permissions.event_status[create]) {\r\n      return true;\r\n    }\r\n    return false;\r\n  },\r\n  wrapperDisplayName: 'CanAddEventStatus',\r\n});\r\n\r\nexport const canAddExpertise = connectedAuthWrapper({\r\n  authenticatedSelector: (state) => {\r\n    if (state.permissions && state.permissions.expertise && state.permissions.expertise[create]) {\r\n      return true;\r\n    }\r\n    return false;\r\n  },\r\n  wrapperDisplayName: 'CanAddExpertise',\r\n});\r\n\r\nexport const canAddFocalPointApproval = connectedAuthWrapper({\r\n  authenticatedSelector: (state) => {\r\n    if (state.permissions && state.permissions.institution_focal_point && state.permissions.institution_focal_point[create]) {\r\n      return true;\r\n    }\r\n    return false;\r\n  },\r\n  wrapperDisplayName: 'CanAddFocalPointApproval',\r\n});\r\n\r\nexport const canAddVspaceApproval = connectedAuthWrapper({\r\n  authenticatedSelector: (state) => {\r\n    if (state.permissions && state.permissions.institution_focal_point && state.permissions.institution_focal_point[create]) {\r\n      return true;\r\n    }\r\n    return false;\r\n  },\r\n  wrapperDisplayName: 'CanAddVspaceApproval',\r\n});\r\nexport const canAddHazards = connectedAuthWrapper({\r\n  authenticatedSelector: (state) => {\r\n    if (state.permissions && state.permissions.hazard && state.permissions.hazard[create]) {\r\n      return true;\r\n    }\r\n    return false;\r\n  },\r\n  wrapperDisplayName: 'CanAddHazards',\r\n});\r\n\r\nexport const canAddHazardTypes = connectedAuthWrapper({\r\n  authenticatedSelector: (state) => {\r\n    if (state.permissions && state.permissions.hazard_type && state.permissions.hazard_type[create]) {\r\n      return true;\r\n    }\r\n    return false;\r\n  },\r\n  wrapperDisplayName: 'CanAddHazardTypes',\r\n}); \r\n\r\nexport const canAddOrganisationApproval = connectedAuthWrapper({\r\n  authenticatedSelector: (state) => {\r\n    if (state.permissions && state.permissions.institution && state.permissions.institution[create]) {\r\n      return true;\r\n    }\r\n    return false;\r\n  },\r\n  wrapperDisplayName: 'CanAddOrganisationApproval',\r\n}); \r\n\r\nexport const canAddOrganisationNetworks = connectedAuthWrapper({\r\n  authenticatedSelector: (state) => {\r\n    if (state.permissions && state.permissions.institution_network && state.permissions.institution_network[create]) {\r\n      return true;\r\n    }\r\n    return false;\r\n  },\r\n  wrapperDisplayName: 'CanAddOrganisationNetworks',\r\n});\r\n\r\nexport const canAddOrganisationTypes = connectedAuthWrapper({\r\n  authenticatedSelector: (state) => {\r\n    if (state.permissions && state.permissions.institution_type && state.permissions.institution_type[create]) {\r\n      return true;\r\n    }\r\n    return false;\r\n  },\r\n  wrapperDisplayName: 'CanAddOrganisationTypes',\r\n});\r\n\r\nexport const canAddOperationStatus = connectedAuthWrapper({\r\n  authenticatedSelector: (state) => {\r\n    if (state.permissions && state.permissions.operation_status && state.permissions.operation_status[create]) {\r\n      return true;\r\n    }\r\n    return false;\r\n  },\r\n  wrapperDisplayName: 'CanAddOperationStatus',\r\n});\r\n\r\nexport const canAddProjectStatus = connectedAuthWrapper({\r\n  authenticatedSelector: (state) => {\r\n    if (state.permissions && state.permissions.project_status && state.permissions.project_status[create]) {\r\n      return true;\r\n    }\r\n    return false;\r\n  },\r\n  wrapperDisplayName: 'CanAddProjectStatus',\r\n});\r\n\r\nexport const canAddRegions = connectedAuthWrapper({\r\n  authenticatedSelector: (state) => {\r\n    if (state.permissions && state.permissions.region && state.permissions.region[create]) {\r\n      return true;\r\n    }\r\n    return false;\r\n  },\r\n  wrapperDisplayName: 'CanAddRegions',\r\n});\r\n\r\nexport const canAddRiskLevels = connectedAuthWrapper({\r\n  authenticatedSelector: (state) => {\r\n    if (state.permissions && state.permissions.risk_level && state.permissions.risk_level[create]) {\r\n      return true;\r\n    }\r\n    return false;\r\n  },\r\n  wrapperDisplayName: 'CanAddRiskLevels',\r\n});\r\n\r\nexport const canAddSyndromes = connectedAuthWrapper({\r\n  authenticatedSelector: (state) => {\r\n    if (state.permissions && state.permissions.syndrome && state.permissions.syndrome[create]) {\r\n      return true;\r\n    }\r\n    return false;\r\n  },\r\n  wrapperDisplayName: 'CanAddSyndromes',\r\n});\r\n\r\nexport const canAddUpdateTypes = connectedAuthWrapper({\r\n  authenticatedSelector: (state) => {\r\n    if (state.permissions && state.permissions.update_type && state.permissions.update_type[create]) {\r\n      return true;\r\n    }\r\n    return false;\r\n  },\r\n  wrapperDisplayName: 'CanAddUpdateTypes',\r\n});\r\n\r\nexport const canAddUsers = connectedAuthWrapper({\r\n  authenticatedSelector: (state) => {\r\n    if (state.permissions && state.permissions.users && state.permissions.users[create]) {\r\n      return true;\r\n    }\r\n    return false;\r\n  },\r\n  wrapperDisplayName: 'CanAddUsers',\r\n});\r\n\r\nexport const canAddWorldRegion = connectedAuthWrapper({\r\n  authenticatedSelector: (state) => {\r\n    if (state.permissions && state.permissions.worl_region && state.permissions.worl_region[create]) {\r\n      return true;\r\n    }\r\n    return false;\r\n  },\r\n  wrapperDisplayName: 'CanAddWorldRegion',\r\n});\r\n\r\nexport const canAddLandingPage = connectedAuthWrapper({\r\n  authenticatedSelector: (state) => {\r\n    if (state.permissions && state.permissions.landing_page && state.permissions.landing_page[create]) {\r\n      return true;\r\n    }\r\n    return false;\r\n  },\r\n  wrapperDisplayName: 'CanAddLandingPage',\r\n});\r\n\r\nexport const canAddContent = connectedAuthWrapper({\r\n  authenticatedSelector: (state) => {\r\n    if (state.permissions && state.permissions.operation && state.permissions.operation[create] && state.permissions.project && state.permissions.project[create]&& state.permissions.event && state.permissions.event[create]&& state.permissions.vspace && state.permissions.vspace[create]&& state.permissions.institution && state.permissions.institution[create]&& state.permissions.update && state.permissions.update[create]) {\r\n      return true;\r\n    }\r\n    return false;\r\n  },\r\n  wrapperDisplayName: 'CanAddContent',\r\n});\r\n\r\nexport default canAddAreaOfWork;", "//Import Library\r\nimport Link from \"next/link\";\r\nimport { useState, useEffect } from \"react\";\r\nimport { <PERSON><PERSON>, Button } from \"react-bootstrap\";\r\nimport toast from 'react-hot-toast';\r\n\r\n//Import services/components\r\nimport RKITable from \"../../../components/common/RKITable\";\r\nimport apiService from \"../../../services/apiService\";\r\nimport { useTranslation } from 'next-i18next';\r\n\r\nconst HazardTypeTable = (_props: any) => {\r\n    const [tabledata, setDataToTable] = useState([]);\r\n    const [, setLoading] = useState(false);\r\n    const [totalRows, setTotalRows] = useState(0);\r\n    const [perPage, setPerPage] = useState(10);\r\n    const [isModalShow, setModal] = useState(false);\r\n    const [selectHazardType, setSelectHazardType] = useState({});\r\n    const { t } = useTranslation('common');\r\n\r\n\r\n    const columns = [\r\n        {\r\n            name: t(\"adminsetting.hazardtypes.HarzardType\"),\r\n            selector: \"title\",\r\n        },\r\n        {\r\n            name: t(\"adminsetting.hazardtypes.Code\"),\r\n            selector: \"code\",\r\n            cell: (d: any) => d.code,\r\n        },\r\n        {\r\n            name: t(\"Description\"),\r\n            selector: \"description\",\r\n            cell: (d: any) => d.description.replace(/<[^>]+>/g, \"\"),\r\n        },\r\n        {\r\n            name: t(\"action\"),\r\n            selector: \"\",\r\n            cell: (d: any) => (\r\n                <div>\r\n                    <Link href=\"/adminsettings/[...routes]\" as={`/adminsettings/edit_hazard_types/${d._id}`}>\r\n\r\n                        <i className=\"icon fas fa-edit\" />\r\n\r\n                    </Link>\r\n                    &nbsp;\r\n                    <a onClick={() => userAction(d)}>\r\n                        <i className=\"icon fas fa-trash-alt\" />\r\n                    </a>\r\n                </div>\r\n            ),\r\n        },\r\n    ];\r\n\r\n    useEffect(() => {\r\n        getHazardsTypeData();\r\n    }, []);\r\n\r\n    const hazardTypeParams = {\r\n        sort: { title: \"asc\" },\r\n        limit: perPage,\r\n        page: 1,\r\n        query: {},\r\n    };\r\n\r\n    const getHazardsTypeData = async () => {\r\n        setLoading(true);\r\n        const response = await apiService.get(\"/hazardtype\", hazardTypeParams);\r\n        if (response && response.data && response.data.length > 0) {\r\n            setDataToTable(response.data);\r\n            setTotalRows(response.totalCount);\r\n            setLoading(false);\r\n        }\r\n    };\r\n\r\n    const handlePageChange = (page: any) => {\r\n        hazardTypeParams.limit = perPage;\r\n        hazardTypeParams.page = page;\r\n        getHazardsTypeData();\r\n    };\r\n\r\n    const handlePerRowsChange = async (newPerPage: any, page: any) => {\r\n        hazardTypeParams.limit = newPerPage;\r\n        hazardTypeParams.page = page;\r\n        setLoading(true);\r\n        const response = await apiService.get(\"/hazardtype\", hazardTypeParams);\r\n        if (response && response.data && response.data.length > 0) {\r\n            setDataToTable(response.data);\r\n            setPerPage(newPerPage);\r\n            setLoading(false);\r\n        }\r\n    };\r\n\r\n    const userAction = async (row: any) => {\r\n        setSelectHazardType(row._id);\r\n        setModal(true);\r\n    };\r\n\r\n    const modalConfirm = async () => {\r\n        try {\r\n            await apiService.remove(`/hazardtype/${selectHazardType}`);\r\n            getHazardsTypeData();\r\n            setModal(false);\r\n            toast.success(t(\"adminsetting.hazardtypes.Table.hazardTypeDeletedSuccessfully\"));\r\n        } catch (error) {\r\n            toast.error(t(\"adminsetting.hazardtypes.Table.errorDeletingHazardType\"));\r\n        }\r\n    };\r\n\r\n    const modalHide = () => setModal(false);\r\n\r\n    return (\r\n        <div>\r\n            <Modal show={isModalShow} onHide={modalHide}>\r\n                <Modal.Header closeButton>\r\n                    <Modal.Title>{t(\"adminsetting.hazardtypes.Deletehazardtype\")}</Modal.Title>\r\n                </Modal.Header>\r\n                <Modal.Body>{t(\"adminsetting.hazardtypes.Areyousurewanttodeletethishazardtype\")}</Modal.Body>\r\n                <Modal.Footer>\r\n                    <Button variant=\"secondary\" onClick={modalHide}>\r\n                        {t(\"cancel\")}\r\n                    </Button>\r\n                    <Button variant=\"primary\" onClick={modalConfirm}>\r\n                        {t(\"yes\")}\r\n                    </Button>\r\n                </Modal.Footer>\r\n            </Modal>\r\n\r\n            <RKITable\r\n                columns={columns}\r\n                data={tabledata}\r\n                totalRows={totalRows}\r\n                pagServer={true}\r\n                handlePerRowsChange={handlePerRowsChange}\r\n                handlePageChange={handlePageChange}\r\n            />\r\n        </div>\r\n    );\r\n};\r\n\r\nexport default HazardTypeTable;\r\n", "//Import Library\r\nimport React from 'react'\r\nimport DataTable  from 'react-data-table-component';\r\n\r\n//Import services/components\r\nimport { useTranslation } from 'next-i18next';\r\nimport CustomLoader from './CustomLoader';\r\n\r\ninterface RKITableProps {\r\n  columns: any[];\r\n  data: any[];\r\n  totalRows?: number;\r\n  resetPaginationToggle?: boolean;\r\n  subheader?: boolean;\r\n  subHeaderComponent?: React.ReactNode;\r\n  handlePerRowsChange?: (newPerPage: number, page: number) => void;\r\n  handlePageChange?: (page: number) => void;\r\n  rowsPerPage?: number[];\r\n  defaultRowsPerPage?: number;\r\n  selectableRows?: boolean;\r\n  loading?: boolean;\r\n  pagServer?: boolean;\r\n  onSelectedRowsChange?: (selectedRows: any) => void;\r\n  clearSelectedRows?: boolean;\r\n  sortServer?: boolean;\r\n  onSort?: (column: any, sortDirection: string) => void;\r\n  persistTableHead?: boolean;\r\n  sortFunction?: (rows: any[], field: any, direction: 'asc' | 'desc') => any[];\r\n  noHeader?: boolean;\r\n  dense?: boolean;\r\n  pagination?: boolean;\r\n  paginationServer?: boolean;\r\n  paginationTotalRows?: number;\r\n  subHeaderAlign?: string;\r\n  [key: string]: any;\r\n}\r\n\r\nfunction RKITable(props: RKITableProps) {\r\n  const { t } = useTranslation('common');\r\n  const paginationComponentOptions = {\r\n    rowsPerPageText: t('Rowsperpage'),\r\n    };\r\n  const {\r\n    columns,\r\n    data,\r\n    totalRows,\r\n    resetPaginationToggle,\r\n    subheader,\r\n    subHeaderComponent,\r\n    handlePerRowsChange,\r\n    handlePageChange,\r\n    rowsPerPage,\r\n    defaultRowsPerPage,\r\n    selectableRows,\r\n    loading,\r\n    pagServer,\r\n    onSelectedRowsChange,\r\n    clearSelectedRows,\r\n    sortServer,\r\n    onSort,\r\n    persistTableHead,\r\n    sortFunction,\r\n    // Filter out any unknown props that might cause DOM warnings\r\n    ...otherProps\r\n  } = props;\r\n\r\n  // Only pass known DataTable props to avoid DOM warnings\r\n  const dataTableProps = {\r\n    paginationComponentOptions,\r\n    noDataComponent: t(\"NoData\"),\r\n    noHeader: true,\r\n    columns,\r\n    data: data || [],\r\n    dense: true,\r\n    paginationResetDefaultPage: resetPaginationToggle,\r\n    subHeader: subheader,\r\n    progressPending: loading,\r\n    subHeaderComponent,\r\n    pagination: true,\r\n    paginationServer: pagServer,\r\n    paginationPerPage: defaultRowsPerPage ? defaultRowsPerPage : 10,\r\n    paginationRowsPerPageOptions: rowsPerPage ? rowsPerPage : [10, 15, 20, 25, 30],\r\n    paginationTotalRows: totalRows,\r\n    onChangeRowsPerPage: handlePerRowsChange,\r\n    onChangePage: handlePageChange,\r\n    selectableRows,\r\n    onSelectedRowsChange,\r\n    clearSelectedRows,\r\n    progressComponent: <CustomLoader/>,\r\n    sortIcon: <i className=\"sort-icon fas fa-exchange-alt\" />,\r\n    sortServer,\r\n    onSort,\r\n    sortFunction,\r\n    persistTableHead,\r\n    className: \"rki-table\"\r\n  };\r\n  return (\r\n    <DataTable {...dataTableProps} />\r\n  )\r\n};\r\n\r\nRKITable.defaultProps = {\r\n  subHeader: false,\r\n  pagination: true,\r\n  totalRows: null,\r\n  pagServer: true,\r\n  onSelectedRowsChange: null,\r\n  clearSelectedRows: false,\r\n  sortServer: false,\r\n  persistTableHead: false\r\n}\r\n\r\nexport default RKITable;\r\n\r\n", "//Import Library\r\nimport { Container, <PERSON>, <PERSON>, Button } from \"react-bootstrap\";\r\nimport Link from \"next/link\";\r\nimport HazardTypeTable from \"./hazardTypeTable\";\r\n\r\n//Import services/components\r\nimport PageHeading from \"../../../components/common/PageHeading\";\r\nimport { useTranslation } from 'next-i18next';\r\nimport { canAddHazardTypes } from \"../permissions\";\r\nimport NoAccessMessage from \"../../rNoAccess\";\r\nimport { useSelector } from \"react-redux\";\r\n\r\n\r\nconst HazardTypeIndex = (_props: any) => {\r\n  const { t } = useTranslation('common');\r\n  const ShowHazardTypeIndex = () => {\r\n    return (\r\n      <Container style={{ overflowX: \"hidden\" }} fluid className=\"p-0\">\r\n        <Row>\r\n          <Col xs={12}>\r\n            <PageHeading title= {t(\"adminsetting.hazardtypes.HarzardType\")} />\r\n          </Col>\r\n        </Row>\r\n        <Row>\r\n          <Col xs={12}>\r\n            <Link\r\n              href=\"/adminsettings/[...routes]\"\r\n              as=\"/adminsettings/create_hazard_types\"\r\n              >\r\n              <Button variant=\"secondary\" size=\"sm\">\r\n                {t(\"adminsetting.hazardtypes.type\")}\r\n              </Button>\r\n            </Link>\r\n          </Col>\r\n        </Row>\r\n        <Row className=\"mt-3\">\r\n          <Col xs={12}>\r\n            <HazardTypeTable />\r\n          </Col>\r\n        </Row>\r\n      </Container>\r\n    );\r\n  };\r\n  \r\n  const ShowAddHazardTypes = canAddHazardTypes(() => <ShowHazardTypeIndex />);\r\n  const state:any = useSelector((state) => state);\r\n  if (!(state?.permissions?.hazard_type?.['create:any'])) {\r\n    return <NoAccessMessage />\r\n  }\r\n  return(\r\n    <ShowAddHazardTypes />\r\n  );\r\n};\r\n\r\nexport default HazardTypeIndex;\r\n", "export default function NoAccessMessage(_props: any) {\r\n    return (\r\n      <div className=\"container-fluid p-0 response-message-block\">\r\n        <div className=\"message\">you don't have permission to access</div>\r\n      </div>\r\n    )\r\n  }", "interface PageHeadingProps {\r\n  title: string; // Required based on actual usage\r\n}\r\n\r\nexport default function PageHeading(props: PageHeadingProps) {\r\n  return (\r\n    <h2 className=\"page-heading\">{props.title}</h2>\r\n  )\r\n}\r\n", "\n    (window.__NEXT_P = window.__NEXT_P || []).push([\n      \"/adminsettings/hazardtypes\",\n      function () {\n        return require(\"private-next-pages/adminsettings/hazardtypes/index.tsx\");\n      }\n    ]);\n    if(module.hot) {\n      module.hot.dispose(function () {\n        window.__NEXT_P.push([\"/adminsettings/hazardtypes\"])\n      });\n    }\n  "], "names": ["create", "canAddAreaOfWork", "connectedAuthWrapper", "authenticatedSelector", "state", "permissions", "area_of_work", "wrapperDisplayName", "country", "deployment_status", "event_status", "expertise", "institution_focal_point", "hazard", "hazard_type", "canAddOrganisationApproval", "institution", "institution_network", "institution_type", "operation_status", "project_status", "region", "risk_level", "syndrome", "update_type", "users", "worl_region", "landing_page", "operation", "project", "event", "vspace", "update", "tabledata", "setDataToTable", "useState", "setLoading", "HazardTypeTable", "totalRows", "setTotalRows", "perPage", "setPerPage", "isModalShow", "setModal", "selectHazardType", "setSelectHazardType", "t", "useTranslation", "columns", "name", "selector", "cell", "d", "code", "description", "replace", "div", "Link", "href", "as", "_id", "i", "className", "a", "onClick", "userAction", "useEffect", "getHazardsTypeData", "hazardTypeParams", "sort", "title", "limit", "page", "query", "response", "apiService", "get", "data", "length", "totalCount", "handlePerRowsChange", "newPerPage", "row", "modalConfirm", "remove", "toast", "success", "error", "modalHide", "Modal", "show", "onHide", "Header", "closeButton", "Title", "Body", "Footer", "<PERSON><PERSON>", "variant", "RKITable", "pagServer", "handlePageChange", "props", "paginationComponentOptions", "rowsPerPageText", "resetPaginationToggle", "subheader", "subHeaderComponent", "rowsPerPage", "defaultRowsPerPage", "selectableRows", "loading", "onSelectedRowsChange", "clearSelectedRows", "sortServer", "onSort", "persistTableHead", "sortFunction", "otherProps", "dataTableProps", "noDataComponent", "<PERSON><PERSON><PERSON><PERSON>", "dense", "paginationResetDefaultPage", "subHeader", "progressPending", "pagination", "paginationServer", "paginationPerPage", "paginationRowsPerPageOptions", "paginationTotalRows", "onChangeRowsPerPage", "onChangePage", "progressComponent", "CustomLoader", "sortIcon", "DataTable", "defaultProps", "_props", "ShowHazardTypeIndex", "HazardTypeIndex", "Container", "style", "overflowX", "fluid", "Row", "Col", "xs", "PageHeading", "size", "ShowAddHazardTypes", "canAddHazardTypes", "useSelector", "NoAccessMessage", "h2"], "sourceRoot": "", "ignoreList": []}