{"version": 3, "file": "static/chunks/pages/country-bc02441622382414.js", "mappings": "oHAAO,IAAMA,EAAwB,CACnC,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,OACD,CAAC,EAEmC,CACnC,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,MACD,CAAC,sFCUF,MA/CkD,OAAC,CACjDC,OAAO,QAAQ,IACfC,CA6CaC,CA7CR,EAAE,SA6CkBA,EA5CzBC,EAAY,EAAE,MACdC,CAAI,MACJC,CAAI,UACJC,CAAQ,SACRC,CAAO,OACPC,CAAK,CACLC,aAAY,CAAK,CAClB,UAsBC,GAAyC,UAAxB,OAAOH,EAASI,GAAG,EAAiB,UAAkC,OAA3BJ,EAASK,GAAG,CAKtE,UAACC,EAAAA,EAAMA,CAAAA,CACLN,SAAUA,EACVD,KAAMA,EACNG,MAAOA,GAASR,EAChBS,UAAWA,EACXF,QA/BgB,CA+BPM,GA9BPN,GAeFA,EAdoB,IADT,EAETP,KACAC,QAYmBa,IAXnBX,OACAC,WACAE,CACF,EAGe,UACbA,EACAS,YAAa,IAAMT,CACrB,EAE6BU,EAEjC,IAIS,IAYX,kGCpCA,MArB0B,IACxB,GAAM,eAAEC,CAAa,KAoBRC,aApBUC,CAAgB,CAAE,CAAGC,CAoBdF,CAnBxB,CAmByB,KAnBvBG,CAAI,CAAE,CAAGC,CAAAA,EAAAA,EAAAA,EAAAA,CAAcA,CAAC,UAC1BC,EAA6B,MAAjBF,EAAKG,QAAQ,CAAWC,EAAAA,CAAqBA,CAAG1B,EAAAA,CAAqBA,CACvF,MACE,UAAC2B,MAAAA,CAAIC,UAAU,6BACb,UAACC,KAAAA,UACAL,EAAUM,GAAG,CAAC,CAACC,EAAMC,IAElB,UAACC,KAAAA,UACC,UAACC,IAAAA,CAAE1B,QAAS,IAAMY,EAAiBW,GAAOH,UAAW,GAA4C,OAAzC,GAAkBG,EAAQ,SAAU,eACzFA,KAFIC,OAUnB,gICPA,IAAMG,EAAwB,IAC5B,GAAM,CAAEC,CAAC,CAAE,CAAGb,CAAAA,EAAAA,EAAAA,EAAAA,CAAcA,CAAC,UACvB,WAAEc,CAAS,eAAEC,CAAa,CAAE,CAAGjB,EAMrC,MACE,WAACM,MAAAA,WACC,UAACA,MAAAA,CAAIC,UAAU,yBACZ,GAAcS,EAAUE,IAAI,EAAKF,EAAUE,IAAI,CAACC,MAAM,CAAG,EACxD,UAACX,KAAAA,UACEQ,EAAUE,IAAI,CAACT,GAAG,CAAC,CAACC,EAAMC,IAEvB,UAACC,KAAAA,CAAGL,UAAU,oBACZ,UAACa,IAAIA,CAACC,KAAK,uBAAuBC,GAAI,aAAjCF,IAA2D,OAATV,EAAKa,GAAG,WAC7D,UAACjB,MAAAA,UAAKI,EAAKtB,KAAK,MAFUuB,MAQpCI,EAAE,sBAEJC,GAAaA,EAAUE,IAAI,CAC7B,UAACZ,MAAAA,CAAIC,UAAU,gCACb,UAACiB,IAAaA,CACZC,UAAWC,KAAKC,IAAI,CAACX,EAAUY,UAAU,CAAIZ,EAAUa,KAAK,EAC5DC,OAFYN,YAEQ,EACpBO,qBAAsB,EACtBC,aA3BiB,CA2BHC,GAzBpBhB,EADmBiB,EAAaC,QAAQ,CAAG,CAC7BC,CAChB,CAFgD,CA2BxCC,UAAWrB,EAAUsB,IAAI,CAAI,EAC7BC,mBAAmB,aACnBC,cAAc,YACdC,kBAAkB,YAClBC,kBAAkB,YAClBC,sBAAsB,YACtBC,cAAc,YACdC,kBAAkB,YAClBC,gBAAgB,SAChBC,kBAAkB,WAClBC,cAAc,IACdC,UAAU,QAEP,OAGb,EAEAnC,EAAsBoC,YAAY,CAAG,CACnClC,UAAW,CACTsB,KAAM,EACNT,MAAO,GACPD,WAAY,GACZV,KAAM,EAAE,CAEZ,EAEA,MAAeJ,qBAAqBA,EAAC,6MCqBrC,MAAeqC,CAAAA,EAAAA,EAAAA,EAAAA,CAAOA,CAAC,GAAWC,GAtFlB,IACd,GAAM,CAAErC,CAAC,GAqFsCsC,EAAC,CArFrCpD,CAAI,CAAE,CAAGC,CAAAA,EAAAA,EAAAA,EAAAA,CAAcA,CAAC,UAC7BoD,EAAgC,OAAlBrD,EAAKG,QAAQ,CAAW,CAACmD,SAAU,KAAK,EAAI,CAACnE,MAAO,KAAK,EACvEoE,EAASzC,EAAE,OACX,CAAClB,EAAeE,EAAiB,CAAG0D,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAASD,GACrD,CAACE,EAAiBC,EAAmB,CAAGF,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAQ,EAAE,EAC1D,CAACzC,EAAW4C,EAAa,CAAGH,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAM,CAAC,GAC3C,CAACI,EAAY5C,EAAc,CAAGwC,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAS,GAE/CK,EAAkB,CACtBC,KAAMT,EACNzB,MAAO,GACPS,KAAMuB,EACNG,MAAO,CAAC,EACRC,OAAQ,0DACV,EACMC,EAAiB,eAAOC,CAAAA,MAAWC,EAAAA,UAAAA,MAAAA,CAAAA,GAAAA,KAAAA,IAAAA,SAAAA,CAAAA,EAAAA,CAAAA,SAAAA,CAAAA,EAAAA,CAAcN,EAErD,GAA+B,IAA3BJ,EAAgBvC,MAAM,CAAQ,YAChCyC,EAAa,CAAE1C,KAAM,EAAE,CAAEU,WAAY,EAAGyC,WAAY,EAAG/B,KAAM,EAAGT,MAAO,EAAG,GAK1EuC,EADEvE,WAA6C,QAAQ,CAA1BA,EACpB,CAAE,GAAGuE,CAAM,CAAEJ,MAAO,CAAE,aAAgBN,CAAgB,CAAE,EAExD,CAAE,GAAGU,CAAM,CAAE9B,KAAK,EAAG0B,MAAO,CAAE,aAAgBG,EAAM,aAAgBtE,EAAe,aAAgB6D,CAAgB,CAAE,EAEhI,IAAMY,EAAW,MAAMC,EAAAA,CAAUA,CAACC,GAAG,CAAC,WAAY,CAAE,GAAGJ,CAAM,CAAEK,aAAcN,CAAK,GAC9EG,GAAYA,EAASpD,IAAI,EAAE,CAE7BwD,QAAQC,GAAG,CAAC,gBAAgBL,EAASpD,IAAI,EACzCwD,QAAQC,GAAG,CAAC,gBAAgBL,GAE5BV,EAAaU,GAGjB,EAMA,MAJAM,CAAAA,EAAAA,EAAAA,SAAAA,CAASA,CAAC,KACRV,EAAenD,EAAE,YAAY+C,EAC/B,EAAG,CAACjE,EAAe6D,EAAiBG,EAAY9C,EAAE,YAAY,EAG5D,WAAC8D,EAAAA,CAASA,CAAAA,CAACC,KAAK,IAACvE,UAAU,gBACzB,UAACwE,EAAAA,CAAGA,CAAAA,UACF,UAACC,EAAAA,CAAGA,CAAAA,CAACC,GAAI,YACP,UAACC,EAAAA,CAAWA,CAAAA,CAAC9F,MAAO2B,EAAE,wBAG1B,UAACgE,EAAAA,CAAGA,CAAAA,UACF,UAACC,EAAAA,CAAGA,CAAAA,CAACC,GAAI,YACP,UAACE,EAAAA,OAAYA,CAAAA,CAACnE,UAAWA,QAG7B,UAAC+D,EAAAA,CAAGA,CAAAA,UACF,UAACC,EAAAA,CAAGA,CAAAA,CAACC,GAAI,YACP,UAACG,EAAAA,CAAsBA,CAAAA,CACrBC,QAAS1B,EACTD,gBAAiBA,EACjB4B,cAAe3B,QAIrB,UAACoB,EAAAA,CAAGA,CAAAA,UACF,UAACC,EAAAA,CAAGA,CAAAA,CAACC,GAAI,YACP,UAACnF,EAAAA,OAAiBA,CAAAA,CAACD,cAAeA,EAAeE,iBAAkBA,QAGvE,UAACgF,EAAAA,CAAGA,CAAAA,UACF,UAACC,EAAAA,CAAGA,CAAAA,CAACC,GAAI,YACP,UAACnE,EAAAA,OAAqBA,CAAAA,CAACG,cAAeA,EAAeD,UAAWA,UAK1E,yFC3EA,MARyB,OAAC,UAAE9B,CAAQ,OAQrBqG,OARuBC,CAAY,QAQnBD,EARqBE,CAAQ,CAAS,GACnE,MACE,UAACC,EAAAA,EAAUA,CAAAA,CAACxG,SAAUA,EAAUsG,aAAcA,WAC5C,UAAClF,MAAAA,UAAKmF,KAGZ,ECdME,EAAO,mBACPC,EAAQ,qBACRC,EAAO,eACPC,EAAW,kBAgTjB,EA/SkB,CAChB,CACE,YAAe,IA6SJC,OA5SX,EA4SoBA,EAAC,IA5SV,CACT,CACE,MAAS,SACX,EACD,EAEH,CACE,YAAeJ,EACf,QAAW,CACT,CACE,MAAS,SACX,EACD,EAEH,CACE,YAAeC,EACf,QAAW,CACT,CACE,MAAS,SACX,EAEJ,EACA,CACE,YAAe,iBACf,YAAe,WACf,QAAW,CACT,CACE,WAAc,KAChB,EACD,EAEH,CACE,YAAe,yBACf,YAAeE,EACf,QAAW,CACT,CACE,MAAS,SACX,EACD,EAEH,CACE,YAAe,6BACf,YAAe,SACf,QAAW,CACT,CACE,WAAc,KAChB,EACD,EAEH,CACE,YAAe,6BACf,YAAeH,EACf,QAAW,CACT,CACE,MAAS,SACX,EACD,EAEH,CACE,YAAe,0BACf,YAAeG,EACf,QAAW,CACT,CACE,MAAS,SACX,EACD,EAEH,CACE,YAAe,qBACf,YAAeA,EACf,QAAW,CACT,CACE,MAAS,SACX,EAEJ,EACA,CACE,YAAe,oBACf,YAAe,WACf,QAAW,CACT,CACE,MAAS,SACX,EACD,EAEH,CACE,YAAe,MACf,QAAW,CACT,CACE,WAAc,KAChB,EACD,EAEH,CACE,YAAe,MACf,YAAe,WACf,QAAW,CACT,CACE,MAAS,SACX,EACD,EAEH,CACE,YAAe,MACf,YAAe,cACf,QAAW,CACT,CACE,WAAc,KAChB,EACD,EAEH,CACE,YAAe,MACf,YAAeH,EACf,QAAW,CACT,CACE,MAAS,SACX,EACD,EAEH,CACE,YAAe,MACf,YAAeC,EACf,QAAW,CACT,CACE,MAAS,SACX,EACD,EAEH,CACE,YAAe,WACf,YAAe,gBACf,QAAW,CACT,CACE,MAAS,SACX,EAEJ,EACA,CACE,YAAe,WACf,YAAeD,EACf,QAAW,CACT,CACE,MAAS,SACX,EACD,EAEH,CACE,YAAe,OACf,QAAW,CACT,CACE,WAAc,KAChB,EACD,EAEH,CACE,YAAe,OACf,YAAe,WACf,QAAW,CACT,CACE,MAAS,SACX,EACD,EAEH,CACE,YAAe,OACf,YAAe,cACf,QAAW,CACT,CACE,WAAc,KAChB,EAEJ,EACA,CACE,YAAe,OACf,YAAeA,EACf,QAAW,CACT,CACE,MAAS,SACX,EACD,EAEH,CACE,YAAe,OACf,YAAeC,EACf,QAAW,CACT,CACE,MAAS,SACX,EACD,EAEH,CACE,YAAeC,EACf,YAAe,WACf,QAAW,CACT,CACE,MAAS,SACX,EACD,EAEH,CACE,YAAeA,EACf,YAAeC,EACf,QAAW,CACT,CACE,MAAS,SACX,EACD,EAEH,CACE,YAAeD,EACf,YAAeF,EACf,QAAW,CACT,CACE,MAAS,SACX,EACD,EAEH,CACE,YAAeE,EACf,YAAeD,EACf,QAAW,CACT,CACE,MAAS,SACX,EACD,EAEH,CACE,YAAe,aACf,YAAe,SACf,QAAW,CACT,CACE,WAAc,KAChB,EACD,EAEH,CACE,YAAe,UACf,QAAW,CACT,CACE,WAAc,KAChB,EAEJ,EACA,CACE,YAAe,UACf,YAAeD,EACf,QAAW,CACT,CACE,MAAS,SACX,EACD,EAEH,CACE,YAAe,UACf,YAAeC,EACf,QAAW,CACT,CACE,MAAS,SACX,EACD,EAEH,CACE,YAAe,eACf,YAAe,gBACf,QAAW,CACT,CACE,MAAS,SACX,EAEJ,EACA,CACE,YAAe,kBACf,YAAe,WACf,QAAW,CACT,CACE,MAAS,SACX,EACD,EAEH,CACE,YAAe,QACf,YAAe,WACf,QAAW,CACT,CACE,MAAS,SACX,EACD,EAEH,CACE,YAAe,QACf,YAAeD,EACf,QAAW,CACT,CACE,MAAS,SACX,EACD,EAEJ,2BCnND,MAzEwC,OAAC,CACvCK,QAwEaC,IAxEH,GAwEUA,EAAC,SAvErBC,CAAY,eACZC,CAAa,UACbV,CAAQ,QACRW,EAAS,GAAG,OACZC,EAAQ,MAAM,UACdjG,CAAQ,MACRkG,EAAO,CAAC,SACRC,EAAU,CAAC,SACXC,CAAO,CACR,GACO,QAAEC,CAAM,CAAE,CAAGC,CAAAA,EAAAA,EAAAA,SAAAA,CAASA,GACtB,UAAEC,CAAQ,WAAEC,CAAS,CAAE,CAAIC,CAAAA,EAAAA,EAAAA,CAAAA,CAAaA,UAmB9C,EAAsB,SAAP,CAAQvG,MAAAA,UAAI,uBACtBqG,EAGH,UAACrG,MAAAA,CAAIC,UAAU,yBACb,UAACD,MAAAA,CAAIC,UAAU,WAAWuG,MAAO,OAAET,SAAOD,EAAQlH,SAAU,UAAW,WACrE,WAAC6H,EAAAA,EAASA,CAAAA,CACRC,kBAzBe,CACrBX,MAAOA,EACPD,OAA0B,UAAlB,OAAOA,EAAsB,GAAU,OAAPA,EAAO,MAAMA,CACvD,EAuBQa,OAhBOd,CAgBCc,EArBM,CACpB3H,IAAK,SAIyB4H,CAH9B3H,IAAK,SACP,EAmBQ+G,KAAMA,EACNa,OAhBU,CAgBFC,GAfd3G,EAAI4G,UAAU,CAAC,CACbC,OAAQvB,CACV,EACF,EAaQwB,QAAS,CACPhB,EAhBWR,MAgBFQ,EACTlH,WAAW,EACXmI,mBAAmB,EACnBC,mBAAmB,EACnBC,YAAY,EACZC,gBAAgB,EAChBC,gBAAgB,EAChBC,mBAAmB,CACrB,YAECpC,EACAO,GAAcE,GAAgBA,EAAavG,WAAW,EACrD,UAAC4F,EAAgBA,CACfrG,SAAUgH,EAAavG,SADR4F,EACmB,GAClCC,aAAc,KAEZd,QAAQC,GAAG,CAAC,qBACZ6B,GAAAA,GACF,WAECR,GAHCQ,QA5BQ,UAAClG,MAAAA,UAAI,mBAsC7B,2ICrEA,SAAS8E,EAAuBpF,CAAkC,EAChE,GAAM,SAACqF,CAAO,CAAC,CAAGrF,EACZ,CAAC8H,EAAYC,EAAc,CAAGtE,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,EAAC,GACvC,CAACuE,EAAQC,EAAU,CAAGxE,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAe,EAAE,EAC/C,CAACC,EAAiBC,EAAmB,CAAGF,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAW,EAAE,EAC7D,GAAE1C,CAAC,CAAE,CAAGb,CAAAA,EAAAA,EAAAA,EAAAA,CAAcA,CAAC,UACvBgI,EAAe,CACnB,MAAS,CAAC,EACV,MAAS,IACT,KAAQ,CAAE,MAAS,KAAM,CAC3B,EAEMC,EAAiB,MAAOC,IAC5B,IAAM9D,EAAW,MAAMC,EAAAA,CAAUA,CAACC,GAAG,CAAC,eAAgB4D,GACtD,GAAI9D,GAAY+D,MAAMC,OAAO,CAAChE,EAASpD,IAAI,EAAG,CAC5C,IAAMqH,EAA6B,EAAE,CAC/BC,EAAwB,EAAE,CAEhCC,IAAAA,IAAM,CAACnE,EAASpD,IAAI,CAAE,CAACR,EAAM+H,KAC3B,IAAMC,EAAyB,CAC7B,GAAGhI,CAAI,CACPiI,WAAW,CACb,EACAJ,EAAaK,IAAI,CAACF,GAClBF,EAAYI,IAAI,CAAClI,EAAKa,GAAG,CAC3B,GAEA8D,EAAQmD,GACR7E,EAAmB6E,GACnBP,EAAUM,EACZ,CACF,EAEA3D,CAAAA,EAAAA,EAAAA,SAAAA,CAASA,CAAC,KACRuD,EAAeD,EACjB,EAAG,EAAE,EAmBL,IAAMW,EAA+B,IACnC,IAAMC,EAAiB,IAAId,EAAO,CAC9Be,EAAyB,IAAIrF,EAAgB,CAEjDoF,EAAeE,OAAO,CAAC,CAACtI,EAAMuI,KACxBvI,EAAKwI,IAAI,GAAKtJ,EAAEuJ,MAAM,CAACtK,EAAE,EAAE,CAC7BiK,CAAc,CAACG,EAAM,CAACN,SAAS,CAAG/I,EAAEuJ,MAAM,CAACC,OAAO,CAC7CxJ,EAAEuJ,MAAM,CAACC,OAAO,CAGnBL,CAHqB,CAGEH,IAAI,CAAClI,EAAKa,GAAG,EAFpCwH,EAAyBA,EAAuBM,MAAM,CAACC,GAAKA,IAAM5I,EAAKa,GAAG,EAKhF,GAEAoC,EAAmBoF,GACnB1D,EAAQ0D,GACRhB,GAAc,GACdE,EAAUa,EACZ,EAcA,MACE,WAACxI,MAAAA,CAAIC,UAAU,qCACb,UAACgJ,EAAAA,CAAIA,CAACC,KAAK,EACTxK,KAAK,WACLH,GAAK,MACL4K,MAAO1I,EAAE,cACTqI,QAAStB,EACT4B,SAzDoBC,CAyDVC,GAxDd,IAAMd,EAAiBd,EAAOvH,GAAG,CAACC,GAAS,EACzC,EADyC,CACtCA,CAAI,CACPiI,UAAWgB,EAAMR,MAAM,CAACC,OAAO,IAG7BS,EAA6B,EAAE,CAC/BF,EAAMR,MAAM,CAACC,OAAO,EAAE,CACxBS,EAAmBf,EAAerI,GAAG,CAACC,GAAQA,EAAKa,IAAG,EAGxD8D,EAAQwE,GACRlG,EAAmBkG,GACnB9B,EAAc4B,EAAMR,MAAM,CAACC,OAAO,EAClCnB,EAAUa,EACZ,IA4CKd,EAAOvH,GAAG,CAAC,CAACC,EAAMuI,IAEf,UAACM,EAAAA,CAAIA,CAACC,KAAK,EAETxK,KAAK,WACLH,GAAI6B,EAAKwI,IAAI,CACbO,MAAO/I,EAAKtB,KAAK,CACjB0K,MAAOpJ,EAAKwI,IAAI,CAChBQ,SAAUb,EACVO,QAASpB,CAAM,CAACiB,EAAM,CAACN,SAAS,EAN3BM,IAUX,UAACc,EAAAA,CAAMA,CAAAA,CAAC5K,QAlCW,CAkCF6K,IAjCnB,IAAMlB,EAAiBd,EAAOvH,GAAG,CAACC,GAAS,EACzC,EADyC,CACtCA,CAAI,CACPiI,WAAW,EACb,GAEAhF,EAAmB,EAAE,EACrBoE,GAAc,GACdE,EAAUa,GACVzD,EAAQ,EAAE,CACZ,EAwBqC9E,UAAU,0BAAkBQ,EAAE,gBAGrE,CAEAqE,EAAuBlC,YAAY,CAAG,CACpCmC,QAAS,KAAS,CACpB,EAEA,MAAeD,sBAAsBA,EAAC,wCCzIvB,SAASF,EAAYlF,CAAuB,EACzD,MACE,UAACiK,KAAAA,CAAG1J,UAAU,wBAAgBP,EAAMZ,KAAK,EAE7C,mBCPA,4CACA,WACA,WACA,OAAe,EAAQ,KAAsC,CAC7D,EACA,SAFsB,oBCJsO,IAA1K,EAAQ,KAAO,EAAjC,CAAoC,CAAuJ,CAAC,MAA5L,CAAyB,CAAmK,KAAgB,OAAO,cAAc,aAAa,aAAa,cAAc,cAAc,2CAA2C,wBAAwB,UAAU,8LAAmM,uCAAsC,aAAa,SAAS,eAAe,OAAO,oNAAoN,wBAAwB,eAAe,mBAAmB,SAAS,aAAa,yDAAyD,QAAQ,YAAa,cAAa,MAAM,cAAc,WAAW,+BAA+B,YAAY,YAAY,qCAAqC,QAAQ,0CAA0C,gBAAc,EAAE,IAAI,aAAa,+DAA+D,uBAAuB,EAAE,8DAA8D,4FAA4F,eAAe,wCAAwC,SAAS,GAAG,SAAS,YAAY,aAAa,cAAc,cAAc,EAAE,uCAAuC,aAAa,wDAAwD,YAAY,mBAAmB,KAAK,mBAAmB,sEAAsE,UAAS,uBAAyB,kBAAkB,mQAAmQ,4JAA4J,YAAY,0BAA0B,iHAAiH,eAAiZ,aAAa,wDAAwD,YAAY,mBAAmB,KAAK,mBAAmB,sEAAsE,UAAS,uBAA7kB,aAAa,2WAA2W,IAA8O,cAAkB,sHAAmI,+BAA+B,UAAlK,UAAkK,CAAY,0BAA0B,mEAAmE,YAA4O,cAAc,gEAAgE,mBAAmB,cAAc,iFAAiF,gBAAgB,aAAa,qGAAoG,IAAM,aAAa,wDAAwD,YAAY,mBAAmB,KAAK,mBAAmB,sEAAsE,WAAS,sBAA+L,gBAAgB,0EAA0E,wBAAuB,MAAQ,SAAiL,KAAc,gGAAoG,SAAS,cAAc,wEAAwE,8CAA6C,IAAM,kBAAkB,yCAAyC,kDAAkD,WAA5kD,aAAa,yMAA0kD,kBAAkB,GAAe,mBAAuQ,GAAvQ,OAAuQ,EAAvQ,qEAA4G,CAA2J,EAA3J,wBAA2J,KAA3J,WAA0C,aAAa,MAAoG,EAApG,6BAAqC,wBAA+D,EAA/D,aAAuC,YAAY,EAAY,GAAZ,EAAY,SAAM,oBAAgC,oDAA4D,uBAA5D,SAA+F,qCAAqC,IAAI,+EAAgF,MAAO,SAAS,UAAU,cAAc,UAAhS,GAA30B,GAAwpC,EAAhC,EAAgC,CAA1B,OAA0B,sBAA1B,GAA6D,GAA7D,eAA6D,wBAA3rC,iBAA0tC,IAA1tC,mBAA0tC,CAA1tC,SAA0tC,EAAnqC,YAAmqC,EAAnqC,4EAA8F,SAAqkC,OAAiB,CAAE,cAAc,MAAQ,qBAAqB,0EAA8E,gEAAiE,uBAAuB,qCAAqC,cAAc,EAAE,sCAAwC,2CAA2C,uCAAuC,UAAU,EAAE,4CAA8C,yFAAyF,YAAY,EAAE,wBAAwB,wCAA0C,mCAAmC,WAAW,qBAAqB,wCAA0C,WAAW,0BAA0B,uCAAyC,+DAA+D,sDAAoH,mDAAqD,6CAA6C,MAAM,SAAS,yDAArO,cAAqO,OAArO,cAAqO,QAArO,cAAqO,SAArO,aAAqO,CAAyF,EAAE,iBAAiB,2BAA2B,kCAAkC,0CAA4C,uBAAuB,8DAA8D,WAAW,EAAE,oCAAsC,8FAA8F,WAAW,EAAE,0CAA4C,8FAA8F,WAAW,EAAE,yCAA2C,qFAAqF,0CAA0C,iCAAmC,iLAAiL,oBAAoB,IAAI,gCAAgC,KAAK,gBAAgB,uCAAuC,sBAAsB,2BAA2B,MAAM,QAAQ,IAAI,KAAK,UAAU,gBAAgB,iCAAiC,EAAE,sBAAsB,iCAAiC,EAAE,gDAAgD,iCAAiC,EAAE,8DAA8D,+BAA+B,oBAAllJ,EAAklJ,CAAuB,2JAA2J,UAAU,+BAA+B,GAAG,wBAAyB,QAAQ,gHAAgH,6CAA6C,oBAAoB,EAAG,SAAS,uVAAgZ,SAAhZ,qDAAgZ,CAAW,GAAG,WAAgB,yCAAyC,4GAA6G,0qBAAyqB,EAAE,2CAA2C,kUAAkU,+BAA8B,mOAAoO,EAAE,sCAAsC,8EAA8E,mBAAmB,EAAE,uCAAuC,wDAAwD,gBAAgB,EAAE,uCAAuC,mEAAmE,8DAA8D,EAAE,yCAAyC,8BAA8B,8DAA8D,yCAAyC,kFAAkF,EAAE,uCAAuC,qKAAqK,yBAA78Q,EAA68Q,CAA4B,6UAA6U,GAAG,EAAE,8BAA8B,uCAAuC,iEAAiE,ohBAA0jB,+BAA+B,2DAA2D,yBAAyB,YAAY,0BAA0B,+HAAntB,iBAAmtB,qBAAsJ,+FAA+F,YAAY,0BAA0B,2HAA9+B,iBAA8+B,qBAAkJ,mDAAmD,EAAl1R,cAAgB,YAAY,WAAW,KAAK,WAAW,gHAA2xR,qCAA39H,EAA29H,aAA0D,YAAY,IAAI,cAAc,iBAAiB,6VAA6V,uCAAuC,yuBAAyuB,sBAAsB,qUAAqU,gDAAgD,qFAAqF,SAAS,qGAAqG,EAAE,SAAU,MAAM,8ICsB/vd,IAAM+F,EAAe,IACnB,GAAM,MAAElF,CAAI,CAAE,CAAGC,CAAAA,EAAAA,EAAAA,EAAAA,CAAcA,CAAC,UAC1BgK,EAAcjK,EAAKG,QAAQ,CAC3B,CAAC8F,EAAciE,EAAgB,CAAG1G,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAM,CAAC,GACjD,CAACuC,EAAYoE,EAAc,CAAG3G,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAM,CAAC,GAC7C,CAAC4G,EAAQC,EAAU,CAAG7G,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAQ,EAAE,EACxC,WAAEzC,CAAS,CAAE,CAAGhB,EAOhBuK,EAAc,KAClBJ,EAAgB,MAChBC,EAAc,KAChB,EAEMI,EAAgB,CAACC,EAAyC/K,EAAagL,KAC3EH,IACAJ,EAAgBzK,GAChB0K,EAAc,CACZxL,KAAM6L,EAAU7L,IAAI,CACpBC,GAAI4L,EAAU5L,EAAE,EAEpB,EAEM8L,EAAyB,KAC7B,IAAMC,EAAqB,EAAE,CACzB5J,GAAaA,EAAUE,IAAI,EAAE,IAC/BuH,OAAS,CAACzH,EAAUE,IAAI,CAAE,IACxB0J,EAAYhC,IAAI,CAAC,CACfxJ,MAAOyL,EAAQzL,KAAK,CACpBP,GAAIgM,EAAQtJ,GAAG,CACfjC,IACEuL,EAAQC,WAAW,EAAID,EAAQC,WAAW,CAAC,EAAE,CACzCD,EAAQC,WAAW,CAAC,EAAE,CAACC,QAAQ,CAC/B,KACNxL,IACEsL,EAAQC,WAAW,EAAID,EAAQC,WAAW,CAAC,EAAE,CACzCD,EAAQC,WAAW,CAAC,EAAE,CAACE,SAAS,CAChC,IACR,EACF,GAEFV,EAAU,IAAIM,EAAY,CAC5B,EAMA,MAJAhG,CAAAA,EAAAA,EAAAA,SAAAA,CAASA,CAAC,KACR+F,GACF,EAAG,CAAC3J,EAAU,EAGZ,UAACiK,EAAAA,CAAOA,CAAAA,CACN7K,SAAU8J,EACVG,OAAQA,EACRjE,OAAQ,IACRF,aAAcA,EACdF,WAAY,UAlDIkF,IAClB,GAAM,MAAEC,CAAI,CAAE,CAAGD,EACjB,MAAO,UAACrK,IAAAA,CAAEQ,KAAM,WAAI6I,EAAY,kBAAyB,aAATiB,EAAAA,KAAAA,EAAAA,EAAMtM,EAAE,IAARsM,aAAaA,EAAAA,KAAAA,EAAAA,EAAMvM,IAAI,EAAVuM,EAgD9CC,CAAWD,KAAMnF,IAC9BQ,QAAS+D,WAERF,GAAUA,EAAOlJ,MAAM,EAAI,EACxBkJ,EAAO5J,GAAG,CAAC,CAACC,EAAMuI,KAChB,GAAIvI,GAAQA,EAAKpB,GAAG,CAClB,CADoB,KAElB,UAACR,EAAAA,CAAYA,CAAAA,CAEXF,KAAM8B,EAAKtB,KAAK,CAChBP,GAAI6B,EAAK7B,EAAE,CACXI,KAAM,CACJoM,IAAK,8BACP,EACAlM,QAASqL,EACTtL,SAAUwB,GAPLuI,EAWb,GACA,MAGV,EAEA9D,EAAajC,YAAY,CAAG,CAC1BlC,UAAW,CAAEE,KAAM,EAAE,CACvB,EAEA,MAAeiE,YAAYA,EAAC", "sources": ["webpack://_N_E/./data/alphabet.tsx", "webpack://_N_E/./components/common/RKIMapMarker.tsx", "webpack://_N_E/./pages/country/CountriesGlossary.tsx", "webpack://_N_E/./pages/country/CountriesNamesListing.tsx", "webpack://_N_E/./pages/country/index.tsx", "webpack://_N_E/./components/common/RKIMapInfowindow.tsx", "webpack://_N_E/./components/common/mapStyles.tsx", "webpack://_N_E/./components/common/RKIMap1.tsx", "webpack://_N_E/./components/common/RegionsMultiCheckboxes.tsx", "webpack://_N_E/./components/common/PageHeading.tsx", "webpack://_N_E/?a932", "webpack://_N_E/./node_modules/react-paginate/dist/react-paginate.js", "webpack://_N_E/./pages/country/CountriesMap.tsx"], "sourcesContent": ["export const ALPHABETIC_FILTERS_DE = [\r\n  \"A\",\r\n  \"B\",\r\n  \"C\",\r\n  \"D\",\r\n  \"E\",\r\n  \"F\",\r\n  \"G\",\r\n  \"H\",\r\n  \"I\",\r\n  \"J\",\r\n  \"K\",\r\n  \"L\",\r\n  \"M\",\r\n  \"N\",\r\n  \"O\",\r\n  \"P\",\r\n  \"Q\",\r\n  \"R\",\r\n  \"S\",\r\n  \"T\",\r\n  \"U\",\r\n  \"V\",\r\n  \"W\",\r\n  \"X\",\r\n  \"Y\",\r\n  \"Z\",\r\n  \"Alle\"\r\n];\r\n\r\nexport const ALPHABETIC_FILTERS_EN = [\r\n  \"A\",\r\n  \"B\",\r\n  \"C\",\r\n  \"D\",\r\n  \"E\",\r\n  \"F\",\r\n  \"G\",\r\n  \"H\",\r\n  \"I\",\r\n  \"J\",\r\n  \"K\",\r\n  \"L\",\r\n  \"M\",\r\n  \"N\",\r\n  \"O\",\r\n  \"P\",\r\n  \"Q\",\r\n  \"R\",\r\n  \"S\",\r\n  \"T\",\r\n  \"U\",\r\n  \"V\",\r\n  \"W\",\r\n  \"X\",\r\n  \"Y\",\r\n  \"Z\",\r\n  \"All\"\r\n];\r\n\r\n", "import React from 'react';\r\nimport { Marker } from '@react-google-maps/api';\r\n\r\ninterface RKIMapMarkerProps {\r\n  name?: string;\r\n  id?: string;\r\n  countryId?: string;\r\n  type?: string;\r\n  icon?: {\r\n    url: string;\r\n    scaledSize?: google.maps.Size;\r\n  };\r\n  position: {\r\n    lat: number;\r\n    lng: number;\r\n  };\r\n  onClick?: (props: any, marker: any, e: any) => void;\r\n  title?: string;\r\n  draggable?: boolean;\r\n}\r\n\r\nconst RKIMapMarker: React.FC<RKIMapMarkerProps> = ({\r\n  name = 'Marker',\r\n  id = '',\r\n  countryId = '',\r\n  type,\r\n  icon,\r\n  position,\r\n  onClick,\r\n  title,\r\n  draggable = false,\r\n}) => {\r\n  const handleClick = (e: google.maps.MapMouseEvent) => {\r\n    if (onClick) {\r\n      const markerProps = {\r\n        name,\r\n        id,\r\n        countryId,\r\n        type,\r\n        position,\r\n      };\r\n\r\n      // Create a marker-like object for compatibility with old onClick signature\r\n      const marker = {\r\n        position,\r\n        getPosition: () => position,\r\n      };\r\n\r\n      onClick(markerProps, marker, e);\r\n    }\r\n  };\r\n\r\n  // Ensure position is valid\r\n  if (!position || typeof position.lat !== 'number' || typeof position.lng !== 'number') {\r\n    return null;\r\n  }\r\n\r\n  return (\r\n    <Marker\r\n      position={position}\r\n      icon={icon}\r\n      title={title || name}\r\n      draggable={draggable}\r\n      onClick={handleClick}\r\n    />\r\n  );\r\n};\r\n\r\nexport default RKIMapMarker;\r\n", "//Import services/components\r\nimport { ALPHABETIC_FILTERS_EN, ALPHABETIC_FILTERS_DE } from '../../data/alphabet';\r\nimport { useTranslation } from 'next-i18next';\r\n\r\ninterface CountriesGlossaryProps {\r\n  selectedAlpha: string;\r\n  setselectedAlpha: (alpha: string) => void;\r\n}\r\n\r\nconst CountriesGlossary = (props: CountriesGlossaryProps) => {\r\n  const { selectedAlpha, setselectedAlpha } = props;\r\n  const { i18n } = useTranslation('common');\r\n  const alphabets = i18n.language == 'en' ? ALPHABETIC_FILTERS_EN : ALPHABETIC_FILTERS_DE;\r\n  return (\r\n    <div className=\"alphabetContainer\">\r\n      <ul>\r\n      {alphabets.map((item, i) => {\r\n        return (\r\n          <li key={i}>\r\n            <a onClick={() => setselectedAlpha(item)} className={`${(selectedAlpha == item) ? 'active': null}`}>\r\n              {item}\r\n            </a>\r\n          </li>\r\n        )\r\n      })}\r\n      </ul>\r\n    </div>\r\n  )\r\n}\r\n\r\nexport default CountriesGlossary;", "//Import Library\r\nimport Link from 'next/link';\r\nimport ReactPaginate from \"react-paginate\";\r\n\r\n//Import services/components\r\nimport { useTranslation } from 'next-i18next';\r\n\r\ninterface CountriesNamesListingProps {\r\n  countries: {\r\n    data: Array<{\r\n      _id: string;\r\n      title: string;\r\n    }>;\r\n    totalPages?: number;\r\n    page?: number;\r\n    totalCount?: number;\r\n    limit?: number;\r\n  };\r\n  setActivePage: (page: number) => void;\r\n}\r\n\r\nconst CountriesNamesListing = (props: CountriesNamesListingProps) => {\r\n  const { t } = useTranslation('common');\r\n  const { countries, setActivePage } = props;\r\n\r\n  const handlePageChange = (selectedItem: { selected: number }) => {\r\n    const pageNumber = selectedItem.selected + 1; // react-paginate uses 0-based indexing\r\n    setActivePage(pageNumber);\r\n  };\r\n  return (\r\n    <div>\r\n      <div className=\"alphabetLists\">\r\n        {(countries && countries.data && (countries.data.length > 0)) ?\r\n          <ul>\r\n            {countries.data.map((item, i) => {\r\n              return (\r\n                <li className='clearfix' key={i}>\r\n                  <Link href='/country/[...routes]' as={`/country/show/${item._id}`}>\r\n                    <div>{item.title}</div>\r\n                  </Link>\r\n                </li>\r\n              );\r\n            })}\r\n          </ul>\r\n        : t(\"NoCountriesfound\")}\r\n      </div>\r\n      {(countries && countries.data)?\r\n      <div className=\"countries-pagination\">\r\n        <ReactPaginate\r\n          pageCount={Math.ceil(countries.totalCount! / countries.limit!)}\r\n          pageRangeDisplayed={5}\r\n          marginPagesDisplayed={2}\r\n          onPageChange={handlePageChange}\r\n          forcePage={countries.page! - 1} // react-paginate uses 0-based indexing\r\n          containerClassName=\"pagination\"\r\n          pageClassName=\"page-item\"\r\n          pageLinkClassName=\"page-link\"\r\n          previousClassName=\"page-item\"\r\n          previousLinkClassName=\"page-link\"\r\n          nextClassName=\"page-item\"\r\n          nextLinkClassName=\"page-link\"\r\n          activeClassName=\"active\"\r\n          disabledClassName=\"disabled\"\r\n          previousLabel=\"‹\"\r\n          nextLabel=\"›\"\r\n        />\r\n      </div>:null}\r\n    </div>\r\n  );\r\n}\r\n\r\nCountriesNamesListing.defaultProps = {\r\n  countries: {\r\n    page: 1,\r\n    limit: 10,\r\n    totalCount: 10,\r\n    data: []\r\n  }\r\n}\r\n\r\nexport default CountriesNamesListing;", "//Import Library\r\nimport { useEffect, useState } from \"react\";\r\nimport { Col, Container, Row } from \"react-bootstrap\";\r\nimport { connect } from \"react-redux\";\r\n\r\n//Import services/components\r\nimport CountriesMap from \"./CountriesMap\";\r\nimport CountriesGlossary from \"./CountriesGlossary\";\r\nimport CountriesNamesListing from \"./CountriesNamesListing\";\r\nimport PageHeading from \"../../components/common/PageHeading\";\r\nimport RegionsMultiCheckboxes from \"../../components/common/RegionsMultiCheckboxes\";\r\nimport { useTranslation } from 'next-i18next';\r\nimport apiService from \"../../services/apiService\";\r\nimport { serverSideTranslations } from 'next-i18next/serverSideTranslations';\r\n\r\nconst Country = (props: any) => {\r\n  const { t, i18n } = useTranslation('common');\r\n  const titleSearch = i18n.language === 'de'? {title_de: \"asc\"} : {title: \"asc\"};\r\n  const letter = t(\"All\");\r\n  const [selectedAlpha, setselectedAlpha] = useState<string>(letter);\r\n  const [selectedRegions, setSelectedRegions] = useState<any[]>([]);\r\n  const [countries, setCountries] = useState<any>({});\r\n  const [activePage, setActivePage] = useState<number>(1);\r\n\r\n  const countriesParams = {\r\n    sort: titleSearch ,\r\n    limit: 48,\r\n    page: activePage,\r\n    query: {},\r\n    select: \"-health_profile -security_advice -created_at -updated_at\",\r\n  }\r\n  const fetchCountries = async (lang: any, params: any = countriesParams) => {\r\n    // If no regions are selected, show no countries\r\n    if (selectedRegions.length === 0) {\r\n      setCountries({ data: [], totalCount: 0, totalPages: 0, page: 1, limit: 48 });\r\n      return;\r\n    }\r\n\r\n    if (selectedAlpha === 'All' || selectedAlpha === 'Alle') {\r\n      params = { ...params, query: { \"world_region\": selectedRegions } };\r\n    } else {\r\n      params = { ...params, page:1, query: { \"languageCode\": lang, \"first_letter\": selectedAlpha, \"world_region\": selectedRegions } };\r\n    }\r\n    const response = await apiService.get('/country', { ...params, languageCode: lang });\r\n    if (response && response.data) {\r\n\r\n      console.log(\"response.data\",response.data)\r\n      console.log(\"response.data\",response)\r\n\r\n      setCountries(response);\r\n\r\n    }\r\n  };\r\n\r\n  useEffect(() => {\r\n    fetchCountries(t(\"language\"),countriesParams);\r\n  }, [selectedAlpha, selectedRegions, activePage, t(\"language\")]);\r\n\r\n  return (\r\n    <Container fluid className=\"p-0\">\r\n      <Row>\r\n        <Col md={12}>\r\n          <PageHeading title={t(\"menu.countries\")} />\r\n        </Col>\r\n      </Row>\r\n      <Row>\r\n        <Col md={12}>\r\n          <CountriesMap countries={countries} />\r\n        </Col>\r\n      </Row>\r\n      <Row>\r\n        <Col md={12}>\r\n          <RegionsMultiCheckboxes\r\n            filtreg={setSelectedRegions}\r\n            selectedRegions={selectedRegions}\r\n            regionHandler={setSelectedRegions}\r\n          />\r\n        </Col>\r\n      </Row>\r\n      <Row>\r\n        <Col md={12}>\r\n          <CountriesGlossary selectedAlpha={selectedAlpha} setselectedAlpha={setselectedAlpha} />\r\n        </Col>\r\n      </Row>\r\n      <Row>\r\n        <Col md={12}>\r\n          <CountriesNamesListing setActivePage={setActivePage} countries={countries} />\r\n        </Col>\r\n      </Row>\r\n    </Container>\r\n  )\r\n}\r\n\r\nexport async function getStaticProps({ locale }: { locale: string }) {\r\n  return {\r\n    props: {\r\n      ...(await serverSideTranslations(locale, ['common'])),\r\n    },\r\n  }\r\n}\r\n\r\nexport default connect((state) => state)(Country);", "import { InfoWindow } from '@react-google-maps/api';\r\n\r\ninterface Props {\r\n  position: google.maps.LatLngLiteral;\r\n  onCloseClick?: () => void;\r\n  children?: React.ReactNode;\r\n}\r\n\r\nconst RKIMapInfowindow = ({ position, onCloseClick, children }: Props) => {\r\n  return (\r\n    <InfoWindow position={position} onCloseClick={onCloseClick}>\r\n      <div>{children}</div>\r\n    </InfoWindow>\r\n  );\r\n};\r\n\r\nexport default RKIMapInfowindow;\r\n", "const fill = \"labels.text.fill\"\r\nconst stoke = \"labels.text.stroke\"\r\nconst road = \"road.highway\"\r\nconst geometry = \"geometry.stroke\"\r\nconst mapStyles = [\r\n  {\r\n    \"elementType\": \"geometry\",\r\n    \"stylers\": [\r\n      {\r\n        \"color\": \"#1d2c4d\"\r\n      }\r\n    ]\r\n  },\r\n  {\r\n    \"elementType\": fill,\r\n    \"stylers\": [\r\n      {\r\n        \"color\": \"#8ec3b9\"\r\n      }\r\n    ]\r\n  },\r\n  {\r\n    \"elementType\": stoke,\r\n    \"stylers\": [\r\n      {\r\n        \"color\": \"#1a3646\"\r\n      }\r\n    ]\r\n  },\r\n  {\r\n    \"featureType\": \"administrative\",\r\n    \"elementType\": \"geometry\",\r\n    \"stylers\": [\r\n      {\r\n        \"visibility\": \"off\"\r\n      }\r\n    ]\r\n  },\r\n  {\r\n    \"featureType\": \"administrative.country\",\r\n    \"elementType\": geometry,\r\n    \"stylers\": [\r\n      {\r\n        \"color\": \"#4b6878\"\r\n      }\r\n    ]\r\n  },\r\n  {\r\n    \"featureType\": \"administrative.land_parcel\",\r\n    \"elementType\": \"labels\",\r\n    \"stylers\": [\r\n      {\r\n        \"visibility\": \"off\"\r\n      }\r\n    ]\r\n  },\r\n  {\r\n    \"featureType\": \"administrative.land_parcel\",\r\n    \"elementType\": fill,\r\n    \"stylers\": [\r\n      {\r\n        \"color\": \"#64779e\"\r\n      }\r\n    ]\r\n  },\r\n  {\r\n    \"featureType\": \"administrative.province\",\r\n    \"elementType\": geometry,\r\n    \"stylers\": [\r\n      {\r\n        \"color\": \"#4b6878\"\r\n      }\r\n    ]\r\n  },\r\n  {\r\n    \"featureType\": \"landscape.man_made\",\r\n    \"elementType\": geometry,\r\n    \"stylers\": [\r\n      {\r\n        \"color\": \"#334e87\"\r\n      }\r\n    ]\r\n  },\r\n  {\r\n    \"featureType\": \"landscape.natural\",\r\n    \"elementType\": \"geometry\",\r\n    \"stylers\": [\r\n      {\r\n        \"color\": \"#023e58\"\r\n      }\r\n    ]\r\n  },\r\n  {\r\n    \"featureType\": \"poi\",\r\n    \"stylers\": [\r\n      {\r\n        \"visibility\": \"off\"\r\n      }\r\n    ]\r\n  },\r\n  {\r\n    \"featureType\": \"poi\",\r\n    \"elementType\": \"geometry\",\r\n    \"stylers\": [\r\n      {\r\n        \"color\": \"#283d6a\"\r\n      }\r\n    ]\r\n  },\r\n  {\r\n    \"featureType\": \"poi\",\r\n    \"elementType\": \"labels.text\",\r\n    \"stylers\": [\r\n      {\r\n        \"visibility\": \"off\"\r\n      }\r\n    ]\r\n  },\r\n  {\r\n    \"featureType\": \"poi\",\r\n    \"elementType\": fill,\r\n    \"stylers\": [\r\n      {\r\n        \"color\": \"#6f9ba5\"\r\n      }\r\n    ]\r\n  },\r\n  {\r\n    \"featureType\": \"poi\",\r\n    \"elementType\": stoke,\r\n    \"stylers\": [\r\n      {\r\n        \"color\": \"#1d2c4d\"\r\n      }\r\n    ]\r\n  },\r\n  {\r\n    \"featureType\": \"poi.park\",\r\n    \"elementType\": \"geometry.fill\",\r\n    \"stylers\": [\r\n      {\r\n        \"color\": \"#023e58\"\r\n      }\r\n    ]\r\n  },\r\n  {\r\n    \"featureType\": \"poi.park\",\r\n    \"elementType\": fill,\r\n    \"stylers\": [\r\n      {\r\n        \"color\": \"#3C7680\"\r\n      }\r\n    ]\r\n  },\r\n  {\r\n    \"featureType\": \"road\",\r\n    \"stylers\": [\r\n      {\r\n        \"visibility\": \"off\"\r\n      }\r\n    ]\r\n  },\r\n  {\r\n    \"featureType\": \"road\",\r\n    \"elementType\": \"geometry\",\r\n    \"stylers\": [\r\n      {\r\n        \"color\": \"#304a7d\"\r\n      }\r\n    ]\r\n  },\r\n  {\r\n    \"featureType\": \"road\",\r\n    \"elementType\": \"labels.icon\",\r\n    \"stylers\": [\r\n      {\r\n        \"visibility\": \"off\"\r\n      }\r\n    ]\r\n  },\r\n  {\r\n    \"featureType\": \"road\",\r\n    \"elementType\": fill,\r\n    \"stylers\": [\r\n      {\r\n        \"color\": \"#98a5be\"\r\n      }\r\n    ]\r\n  },\r\n  {\r\n    \"featureType\": \"road\",\r\n    \"elementType\": stoke,\r\n    \"stylers\": [\r\n      {\r\n        \"color\": \"#1d2c4d\"\r\n      }\r\n    ]\r\n  },\r\n  {\r\n    \"featureType\": road,\r\n    \"elementType\": \"geometry\",\r\n    \"stylers\": [\r\n      {\r\n        \"color\": \"#2c6675\"\r\n      }\r\n    ]\r\n  },\r\n  {\r\n    \"featureType\": road,\r\n    \"elementType\": geometry,\r\n    \"stylers\": [\r\n      {\r\n        \"color\": \"#255763\"\r\n      }\r\n    ]\r\n  },\r\n  {\r\n    \"featureType\": road,\r\n    \"elementType\": fill,\r\n    \"stylers\": [\r\n      {\r\n        \"color\": \"#b0d5ce\"\r\n      }\r\n    ]\r\n  },\r\n  {\r\n    \"featureType\": road,\r\n    \"elementType\": stoke,\r\n    \"stylers\": [\r\n      {\r\n        \"color\": \"#023e58\"\r\n      }\r\n    ]\r\n  },\r\n  {\r\n    \"featureType\": \"road.local\",\r\n    \"elementType\": \"labels\",\r\n    \"stylers\": [\r\n      {\r\n        \"visibility\": \"off\"\r\n      }\r\n    ]\r\n  },\r\n  {\r\n    \"featureType\": \"transit\",\r\n    \"stylers\": [\r\n      {\r\n        \"visibility\": \"off\"\r\n      }\r\n    ]\r\n  },\r\n  {\r\n    \"featureType\": \"transit\",\r\n    \"elementType\": fill,\r\n    \"stylers\": [\r\n      {\r\n        \"color\": \"#98a5be\"\r\n      }\r\n    ]\r\n  },\r\n  {\r\n    \"featureType\": \"transit\",\r\n    \"elementType\": stoke,\r\n    \"stylers\": [\r\n      {\r\n        \"color\": \"#1d2c4d\"\r\n      }\r\n    ]\r\n  },\r\n  {\r\n    \"featureType\": \"transit.line\",\r\n    \"elementType\": \"geometry.fill\",\r\n    \"stylers\": [\r\n      {\r\n        \"color\": \"#283d6a\"\r\n      }\r\n    ]\r\n  },\r\n  {\r\n    \"featureType\": \"transit.station\",\r\n    \"elementType\": \"geometry\",\r\n    \"stylers\": [\r\n      {\r\n        \"color\": \"#3a4762\"\r\n      }\r\n    ]\r\n  },\r\n  {\r\n    \"featureType\": \"water\",\r\n    \"elementType\": \"geometry\",\r\n    \"stylers\": [\r\n      {\r\n        \"color\": \"#0e1626\"\r\n      }\r\n    ]\r\n  },\r\n  {\r\n    \"featureType\": \"water\",\r\n    \"elementType\": fill,\r\n    \"stylers\": [\r\n      {\r\n        \"color\": \"#4e6d70\"\r\n      }\r\n    ]\r\n  }\r\n];\r\n\r\nexport default mapStyles;", "import React from 'react';\r\nimport { GoogleMap, useJsApiLoader } from '@react-google-maps/api';\r\nimport RKIMapInfowindow from './RKIMapInfowindow';\r\nimport mapStyles from './mapStyles';\r\nimport { useRouter } from \"next/router\";\r\nimport { useGoogleMaps } from './GoogleMapsProvider';\r\n\r\ninterface RKIMap1Props {\r\n  markerInfo?: React.ReactNode;\r\n  activeMarker?: any;\r\n  initialCenter?: { lat: number; lng: number };\r\n  children?: React.ReactNode;\r\n  height?: number | string;\r\n  width?: string;\r\n  language?: string;\r\n  points?: any[];\r\n  zoom?: number;\r\n  minZoom?: number;\r\n  onClose?: () => void\r\n}\r\n\r\nconst RKIMap1: React.FC<RKIMap1Props> = ({\r\n  markerInfo,\r\n  activeMarker,\r\n  initialCenter,\r\n  children,\r\n  height = 300,\r\n  width = \"114%\",\r\n  language,\r\n  zoom = 1,\r\n  minZoom = 1,\r\n  onClose\r\n}) => {\r\n  const { locale } = useRouter();\r\n  const { isLoaded, loadError } =  useGoogleMaps();\r\n  const containerStyle = {\r\n    width: width,\r\n    height: typeof height === 'number' ? `${height}px` : height,\r\n  };\r\n\r\n  const defaultCenter = {\r\n    lat: 52.520017,\r\n    lng: 13.404195,\r\n  };\r\n\r\n  const center = initialCenter || defaultCenter;\r\n\r\n  const onMapLoad = (map: google.maps.Map) => {\r\n    map.setOptions({\r\n      styles: mapStyles,\r\n    });\r\n  };\r\n\r\n  if (loadError) return <div>Error loading maps</div>;\r\n  if (!isLoaded) return <div>Loading Maps...</div>;\r\n\r\n  return (\r\n    <div className=\"map-container\">\r\n      <div className=\"mapprint\" style={{ width, height, position: 'relative' }}>\r\n        <GoogleMap\r\n          mapContainerStyle={containerStyle}\r\n          center={center}\r\n          zoom={zoom}\r\n          onLoad={onMapLoad}\r\n          options={{\r\n            minZoom: minZoom,\r\n            draggable: true,\r\n            keyboardShortcuts: false,\r\n            streetViewControl: false,\r\n            panControl: false,\r\n            clickableIcons: false,\r\n            mapTypeControl: false,\r\n            fullscreenControl: true,\r\n          }}\r\n        >\r\n          {children}\r\n          {markerInfo && activeMarker && activeMarker.getPosition && (\r\n            <RKIMapInfowindow\r\n              position={activeMarker.getPosition()}\r\n              onCloseClick={() => {\r\n                // Handle close if needed\r\n                console.log('close click');\r\n                onClose?.()\r\n              }}\r\n            >\r\n              {markerInfo}\r\n            </RKIMapInfowindow>\r\n          )}\r\n        </GoogleMap>\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default RKIMap1;\r\n", "//Import Library\r\nimport React, { useState, useEffect } from 'react';\r\nimport _ from 'lodash';\r\nimport { Form, Button } from \"react-bootstrap\";\r\n\r\n//Import services/components\r\nimport apiService from '../../services/apiService';\r\nimport { useTranslation } from 'next-i18next';\r\n\r\n// Define types for region items\r\ninterface RegionItem {\r\n  _id: string;\r\n  code: string;\r\n  title: string;\r\n  isChecked: boolean;\r\n}\r\n\r\ninterface RegionsMultiCheckboxesProps {\r\n  regionHandler: (regions: string[]) => void;\r\n  selectedRegions: string[];\r\n  filtreg: (regions: string[]) => void;\r\n}\r\n\r\nfunction RegionsMultiCheckboxes(props: RegionsMultiCheckboxesProps) {\r\n  const {filtreg} = props;\r\n  const [allregions, setAllregions] = useState(true);\r\n  const [region, setRegion] = useState<RegionItem[]>([]);\r\n  const [selectedRegions, setSelectedRegions] = useState<string[]>([]);\r\n  const { t } = useTranslation('common');\r\n  const RegionParams = {\r\n    \"query\": {},\r\n    \"limit\": \"~\",\r\n    \"sort\": { \"title\": \"asc\" }\r\n  };\r\n\r\n  const getworldregion = async (RegionParams_initial: typeof RegionParams) => {\r\n    const response = await apiService.get('/worldregion', RegionParams_initial);\r\n    if (response && Array.isArray(response.data)) {\r\n      const finalRegions: RegionItem[] = [];\r\n      const selectedIds: string[] = [];\r\n\r\n      _.each(response.data, (item, _) => {\r\n        const regionItem: RegionItem = {\r\n          ...item,\r\n          isChecked: true\r\n        };\r\n        finalRegions.push(regionItem);\r\n        selectedIds.push(item._id);\r\n      });\r\n\r\n      filtreg(selectedIds);\r\n      setSelectedRegions(selectedIds);\r\n      setRegion(finalRegions);\r\n    }\r\n  };\r\n\r\n  useEffect(() => {\r\n    getworldregion(RegionParams);\r\n  }, [])\r\n\r\n  const handleAllChecked = (event: React.ChangeEvent<HTMLInputElement>) => {\r\n    const updatedRegions = region.map(item => ({\r\n      ...item,\r\n      isChecked: event.target.checked\r\n    }));\r\n\r\n    let selected_Regions: string[] = [];\r\n    if (event.target.checked) {\r\n      selected_Regions = updatedRegions.map(item => item._id);\r\n    }\r\n\r\n    filtreg(selected_Regions);\r\n    setSelectedRegions(selected_Regions);\r\n    setAllregions(event.target.checked);\r\n    setRegion(updatedRegions);\r\n  };\r\n\r\n  const handleIndividualRegionChange = (e: React.ChangeEvent<HTMLInputElement>) => {\r\n    const updatedRegions = [...region];\r\n    let updatedSelectedRegions = [...selectedRegions];\r\n\r\n    updatedRegions.forEach((item, index) => {\r\n      if (item.code === e.target.id) {\r\n        updatedRegions[index].isChecked = e.target.checked;\r\n        if (!e.target.checked) {\r\n          updatedSelectedRegions = updatedSelectedRegions.filter(n => n !== item._id);\r\n        } else {\r\n          updatedSelectedRegions.push(item._id);\r\n        }\r\n      }\r\n    });\r\n\r\n    setSelectedRegions(updatedSelectedRegions);\r\n    filtreg(updatedSelectedRegions);\r\n    setAllregions(false);\r\n    setRegion(updatedRegions);\r\n  };\r\n\r\n  const resetAllRegion = () => {\r\n    const updatedRegions = region.map(item => ({\r\n      ...item,\r\n      isChecked: false\r\n    }));\r\n\r\n    setSelectedRegions([]);\r\n    setAllregions(false);\r\n    setRegion(updatedRegions);\r\n    filtreg([]);\r\n  };\r\n\r\n  return (\r\n    <div className=\"regions-multi-checkboxes\">\r\n      <Form.Check\r\n        type=\"checkbox\"\r\n        id={`all`}\r\n        label={t(\"AllRegions\")}\r\n        checked={allregions}\r\n        onChange={handleAllChecked}\r\n      />\r\n      {region.map((item, index) => {\r\n        return (\r\n          <Form.Check\r\n            key={index}\r\n            type=\"checkbox\"\r\n            id={item.code}\r\n            label={item.title}\r\n            value={item.code}\r\n            onChange={handleIndividualRegionChange}\r\n            checked={region[index].isChecked}\r\n          />\r\n        )\r\n      })}\r\n      <Button onClick={resetAllRegion} className=\"btn-plain ps-2\">{t(\"ClearAll\")}</Button>\r\n    </div>\r\n  )\r\n}\r\n\r\nRegionsMultiCheckboxes.defaultProps = {\r\n  filtreg: () => {\"\"}\r\n}\r\n\r\nexport default RegionsMultiCheckboxes;\r\n", "interface PageHeadingProps {\r\n  title: string; // Required based on actual usage\r\n}\r\n\r\nexport default function PageHeading(props: PageHeadingProps) {\r\n  return (\r\n    <h2 className=\"page-heading\">{props.title}</h2>\r\n  )\r\n}\r\n", "\n    (window.__NEXT_P = window.__NEXT_P || []).push([\n      \"/country\",\n      function () {\n        return require(\"private-next-pages/country/index.tsx\");\n      }\n    ]);\n    if(module.hot) {\n      module.hot.dispose(function () {\n        window.__NEXT_P.push([\"/country\"])\n      });\n    }\n  ", "!function(e,a){\"object\"==typeof exports&&\"object\"==typeof module?module.exports=a(require(\"react\")):\"function\"==typeof define&&define.amd?define([\"react\"],a):\"object\"==typeof exports?exports.ReactPaginate=a(require(\"react\")):e.ReactPaginate=a(e.React)}(this,(e=>(()=>{var a={703:(e,a,t)=>{\"use strict\";var r=t(414);function n(){}function i(){}i.resetWarningCache=n,e.exports=function(){function e(e,a,t,n,i,s){if(s!==r){var o=new Error(\"Calling PropTypes validators directly is not supported by the `prop-types` package. Use PropTypes.checkPropTypes() to call them. Read more at http://fb.me/use-check-prop-types\");throw o.name=\"Invariant Violation\",o}}function a(){return e}e.isRequired=e;var t={array:e,bigint:e,bool:e,func:e,number:e,object:e,string:e,symbol:e,any:e,arrayOf:a,element:e,elementType:e,instanceOf:a,node:e,objectOf:a,oneOf:a,oneOfType:a,shape:a,exact:a,checkPropTypes:i,resetWarningCache:n};return t.PropTypes=t,t}},697:(e,a,t)=>{e.exports=t(703)()},414:e=>{\"use strict\";e.exports=\"SECRET_DO_NOT_PASS_THIS_OR_YOU_WILL_BE_FIRED\"},98:a=>{\"use strict\";a.exports=e}},t={};function r(e){var n=t[e];if(void 0!==n)return n.exports;var i=t[e]={exports:{}};return a[e](i,i.exports,r),i.exports}r.n=e=>{var a=e&&e.__esModule?()=>e.default:()=>e;return r.d(a,{a}),a},r.d=(e,a)=>{for(var t in a)r.o(a,t)&&!r.o(e,t)&&Object.defineProperty(e,t,{enumerable:!0,get:a[t]})},r.o=(e,a)=>Object.prototype.hasOwnProperty.call(e,a),r.r=e=>{\"undefined\"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:\"Module\"}),Object.defineProperty(e,\"__esModule\",{value:!0})};var n={};return(()=>{\"use strict\";r.r(n),r.d(n,{default:()=>k});var e=r(98),a=r.n(e),t=r(697),i=r.n(t);function s(){return s=Object.assign?Object.assign.bind():function(e){for(var a=1;a<arguments.length;a++){var t=arguments[a];for(var r in t)Object.prototype.hasOwnProperty.call(t,r)&&(e[r]=t[r])}return e},s.apply(this,arguments)}var o=function(e){var t=e.pageClassName,r=e.pageLinkClassName,n=e.page,i=e.selected,o=e.activeClassName,l=e.activeLinkClassName,c=e.getEventListener,p=e.pageSelectedHandler,u=e.href,g=e.extraAriaContext,d=e.pageLabelBuilder,f=e.rel,b=e.ariaLabel||\"Page \"+n+(g?\" \"+g:\"\"),v=null;return i&&(v=\"page\",b=e.ariaLabel||\"Page \"+n+\" is your current page\",t=void 0!==t?t+\" \"+o:o,void 0!==r?void 0!==l&&(r=r+\" \"+l):r=l),a().createElement(\"li\",{className:t},a().createElement(\"a\",s({rel:f,role:u?void 0:\"button\",className:r,href:u,tabIndex:i?\"-1\":\"0\",\"aria-label\":b,\"aria-current\":v,onKeyPress:p},c(p)),d(n)))};o.propTypes={pageSelectedHandler:i().func.isRequired,selected:i().bool.isRequired,pageClassName:i().string,pageLinkClassName:i().string,activeClassName:i().string,activeLinkClassName:i().string,extraAriaContext:i().string,href:i().string,ariaLabel:i().string,page:i().number.isRequired,getEventListener:i().func.isRequired,pageLabelBuilder:i().func.isRequired,rel:i().string};const l=o;function c(){return c=Object.assign?Object.assign.bind():function(e){for(var a=1;a<arguments.length;a++){var t=arguments[a];for(var r in t)Object.prototype.hasOwnProperty.call(t,r)&&(e[r]=t[r])}return e},c.apply(this,arguments)}var p=function(e){var t=e.breakLabel,r=e.breakAriaLabel,n=e.breakClassName,i=e.breakLinkClassName,s=e.breakHandler,o=e.getEventListener,l=n||\"break\";return a().createElement(\"li\",{className:l},a().createElement(\"a\",c({className:i,role:\"button\",tabIndex:\"0\",\"aria-label\":r,onKeyPress:s},o(s)),t))};p.propTypes={breakLabel:i().oneOfType([i().string,i().node]),breakAriaLabel:i().string,breakClassName:i().string,breakLinkClassName:i().string,breakHandler:i().func.isRequired,getEventListener:i().func.isRequired};const u=p;function g(e){var a=arguments.length>1&&void 0!==arguments[1]?arguments[1]:\"\";return null!=e?e:a}function d(e){return d=\"function\"==typeof Symbol&&\"symbol\"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&\"function\"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?\"symbol\":typeof e},d(e)}function f(){return f=Object.assign?Object.assign.bind():function(e){for(var a=1;a<arguments.length;a++){var t=arguments[a];for(var r in t)Object.prototype.hasOwnProperty.call(t,r)&&(e[r]=t[r])}return e},f.apply(this,arguments)}function b(e,a){for(var t=0;t<a.length;t++){var r=a[t];r.enumerable=r.enumerable||!1,r.configurable=!0,\"value\"in r&&(r.writable=!0),Object.defineProperty(e,r.key,r)}}function v(e,a){return v=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,a){return e.__proto__=a,e},v(e,a)}function h(e,a){if(a&&(\"object\"===d(a)||\"function\"==typeof a))return a;if(void 0!==a)throw new TypeError(\"Derived constructors may only return object or undefined\");return m(e)}function m(e){if(void 0===e)throw new ReferenceError(\"this hasn't been initialised - super() hasn't been called\");return e}function y(e){return y=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)},y(e)}function C(e,a,t){return a in e?Object.defineProperty(e,a,{value:t,enumerable:!0,configurable:!0,writable:!0}):e[a]=t,e}var P=function(e){!function(e,a){if(\"function\"!=typeof a&&null!==a)throw new TypeError(\"Super expression must either be null or a function\");e.prototype=Object.create(a&&a.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,\"prototype\",{writable:!1}),a&&v(e,a)}(o,e);var t,r,n,i,s=(n=o,i=function(){if(\"undefined\"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if(\"function\"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(e){return!1}}(),function(){var e,a=y(n);if(i){var t=y(this).constructor;e=Reflect.construct(a,arguments,t)}else e=a.apply(this,arguments);return h(this,e)});function o(e){var t,r;return function(e,a){if(!(e instanceof a))throw new TypeError(\"Cannot call a class as a function\")}(this,o),C(m(t=s.call(this,e)),\"handlePreviousPage\",(function(e){var a=t.state.selected;t.handleClick(e,null,a>0?a-1:void 0,{isPrevious:!0})})),C(m(t),\"handleNextPage\",(function(e){var a=t.state.selected,r=t.props.pageCount;t.handleClick(e,null,a<r-1?a+1:void 0,{isNext:!0})})),C(m(t),\"handlePageSelected\",(function(e,a){if(t.state.selected===e)return t.callActiveCallback(e),void t.handleClick(a,null,void 0,{isActive:!0});t.handleClick(a,null,e)})),C(m(t),\"handlePageChange\",(function(e){t.state.selected!==e&&(t.setState({selected:e}),t.callCallback(e))})),C(m(t),\"getEventListener\",(function(e){return C({},t.props.eventListener,e)})),C(m(t),\"handleClick\",(function(e,a,r){var n=arguments.length>3&&void 0!==arguments[3]?arguments[3]:{},i=n.isPrevious,s=void 0!==i&&i,o=n.isNext,l=void 0!==o&&o,c=n.isBreak,p=void 0!==c&&c,u=n.isActive,g=void 0!==u&&u;e.preventDefault?e.preventDefault():e.returnValue=!1;var d=t.state.selected,f=t.props.onClick,b=r;if(f){var v=f({index:a,selected:d,nextSelectedPage:r,event:e,isPrevious:s,isNext:l,isBreak:p,isActive:g});if(!1===v)return;Number.isInteger(v)&&(b=v)}void 0!==b&&t.handlePageChange(b)})),C(m(t),\"handleBreakClick\",(function(e,a){var r=t.state.selected;t.handleClick(a,e,r<e?t.getForwardJump():t.getBackwardJump(),{isBreak:!0})})),C(m(t),\"callCallback\",(function(e){void 0!==t.props.onPageChange&&\"function\"==typeof t.props.onPageChange&&t.props.onPageChange({selected:e})})),C(m(t),\"callActiveCallback\",(function(e){void 0!==t.props.onPageActive&&\"function\"==typeof t.props.onPageActive&&t.props.onPageActive({selected:e})})),C(m(t),\"getElementPageRel\",(function(e){var a=t.state.selected,r=t.props,n=r.nextPageRel,i=r.prevPageRel,s=r.selectedPageRel;return a-1===e?i:a===e?s:a+1===e?n:void 0})),C(m(t),\"pagination\",(function(){var e=[],r=t.props,n=r.pageRangeDisplayed,i=r.pageCount,s=r.marginPagesDisplayed,o=r.breakLabel,l=r.breakClassName,c=r.breakLinkClassName,p=r.breakAriaLabels,g=t.state.selected;if(i<=n)for(var d=0;d<i;d++)e.push(t.getPageElement(d));else{var f=n/2,b=n-f;g>i-n/2?f=n-(b=i-g):g<n/2&&(b=n-(f=g));var v,h,m=function(e){return t.getPageElement(e)},y=[];for(v=0;v<i;v++){var C=v+1;if(C<=s)y.push({type:\"page\",index:v,display:m(v)});else if(C>i-s)y.push({type:\"page\",index:v,display:m(v)});else if(v>=g-f&&v<=g+(0===g&&n>1?b-1:b))y.push({type:\"page\",index:v,display:m(v)});else if(o&&y.length>0&&y[y.length-1].display!==h&&(n>0||s>0)){var P=v<g?p.backward:p.forward;h=a().createElement(u,{key:v,breakAriaLabel:P,breakLabel:o,breakClassName:l,breakLinkClassName:c,breakHandler:t.handleBreakClick.bind(null,v),getEventListener:t.getEventListener}),y.push({type:\"break\",index:v,display:h})}}y.forEach((function(a,t){var r=a;\"break\"===a.type&&y[t-1]&&\"page\"===y[t-1].type&&y[t+1]&&\"page\"===y[t+1].type&&y[t+1].index-y[t-1].index<=2&&(r={type:\"page\",index:a.index,display:m(a.index)}),e.push(r.display)}))}return e})),void 0!==e.initialPage&&void 0!==e.forcePage&&console.warn(\"(react-paginate): Both initialPage (\".concat(e.initialPage,\") and forcePage (\").concat(e.forcePage,\") props are provided, which is discouraged.\")+\" Use exclusively forcePage prop for a controlled component.\\nSee https://reactjs.org/docs/forms.html#controlled-components\"),r=e.initialPage?e.initialPage:e.forcePage?e.forcePage:0,t.state={selected:r},t}return t=o,(r=[{key:\"componentDidMount\",value:function(){var e=this.props,a=e.initialPage,t=e.disableInitialCallback,r=e.extraAriaContext,n=e.pageCount,i=e.forcePage;void 0===a||t||this.callCallback(a),r&&console.warn(\"DEPRECATED (react-paginate): The extraAriaContext prop is deprecated. You should now use the ariaLabelBuilder instead.\"),Number.isInteger(n)||console.warn(\"(react-paginate): The pageCount prop value provided is not an integer (\".concat(n,\"). Did you forget a Math.ceil()?\")),void 0!==a&&a>n-1&&console.warn(\"(react-paginate): The initialPage prop provided is greater than the maximum page index from pageCount prop (\".concat(a,\" > \").concat(n-1,\").\")),void 0!==i&&i>n-1&&console.warn(\"(react-paginate): The forcePage prop provided is greater than the maximum page index from pageCount prop (\".concat(i,\" > \").concat(n-1,\").\"))}},{key:\"componentDidUpdate\",value:function(e){void 0!==this.props.forcePage&&this.props.forcePage!==e.forcePage&&(this.props.forcePage>this.props.pageCount-1&&console.warn(\"(react-paginate): The forcePage prop provided is greater than the maximum page index from pageCount prop (\".concat(this.props.forcePage,\" > \").concat(this.props.pageCount-1,\").\")),this.setState({selected:this.props.forcePage})),Number.isInteger(e.pageCount)&&!Number.isInteger(this.props.pageCount)&&console.warn(\"(react-paginate): The pageCount prop value provided is not an integer (\".concat(this.props.pageCount,\"). Did you forget a Math.ceil()?\"))}},{key:\"getForwardJump\",value:function(){var e=this.state.selected,a=this.props,t=a.pageCount,r=e+a.pageRangeDisplayed;return r>=t?t-1:r}},{key:\"getBackwardJump\",value:function(){var e=this.state.selected-this.props.pageRangeDisplayed;return e<0?0:e}},{key:\"getElementHref\",value:function(e){var a=this.props,t=a.hrefBuilder,r=a.pageCount,n=a.hrefAllControls;if(t)return n||e>=0&&e<r?t(e+1,r,this.state.selected):void 0}},{key:\"ariaLabelBuilder\",value:function(e){var a=e===this.state.selected;if(this.props.ariaLabelBuilder&&e>=0&&e<this.props.pageCount){var t=this.props.ariaLabelBuilder(e+1,a);return this.props.extraAriaContext&&!a&&(t=t+\" \"+this.props.extraAriaContext),t}}},{key:\"getPageElement\",value:function(e){var t=this.state.selected,r=this.props,n=r.pageClassName,i=r.pageLinkClassName,s=r.activeClassName,o=r.activeLinkClassName,c=r.extraAriaContext,p=r.pageLabelBuilder;return a().createElement(l,{key:e,pageSelectedHandler:this.handlePageSelected.bind(null,e),selected:t===e,rel:this.getElementPageRel(e),pageClassName:n,pageLinkClassName:i,activeClassName:s,activeLinkClassName:o,extraAriaContext:c,href:this.getElementHref(e),ariaLabel:this.ariaLabelBuilder(e),page:e+1,pageLabelBuilder:p,getEventListener:this.getEventListener})}},{key:\"render\",value:function(){var e=this.props.renderOnZeroPageCount;if(0===this.props.pageCount&&void 0!==e)return e?e(this.props):e;var t=this.props,r=t.disabledClassName,n=t.disabledLinkClassName,i=t.pageCount,s=t.className,o=t.containerClassName,l=t.previousLabel,c=t.previousClassName,p=t.previousLinkClassName,u=t.previousAriaLabel,d=t.prevRel,b=t.nextLabel,v=t.nextClassName,h=t.nextLinkClassName,m=t.nextAriaLabel,y=t.nextRel,C=this.state.selected,P=0===C,k=C===i-1,x=\"\".concat(g(c)).concat(P?\" \".concat(g(r)):\"\"),L=\"\".concat(g(v)).concat(k?\" \".concat(g(r)):\"\"),N=\"\".concat(g(p)).concat(P?\" \".concat(g(n)):\"\"),O=\"\".concat(g(h)).concat(k?\" \".concat(g(n)):\"\"),R=P?\"true\":\"false\",E=k?\"true\":\"false\";return a().createElement(\"ul\",{className:s||o,role:\"navigation\",\"aria-label\":\"Pagination\"},a().createElement(\"li\",{className:x},a().createElement(\"a\",f({className:N,href:this.getElementHref(C-1),tabIndex:P?\"-1\":\"0\",role:\"button\",onKeyPress:this.handlePreviousPage,\"aria-disabled\":R,\"aria-label\":u,rel:d},this.getEventListener(this.handlePreviousPage)),l)),this.pagination(),a().createElement(\"li\",{className:L},a().createElement(\"a\",f({className:O,href:this.getElementHref(C+1),tabIndex:k?\"-1\":\"0\",role:\"button\",onKeyPress:this.handleNextPage,\"aria-disabled\":E,\"aria-label\":m,rel:y},this.getEventListener(this.handleNextPage)),b)))}}])&&b(t.prototype,r),Object.defineProperty(t,\"prototype\",{writable:!1}),o}(e.Component);C(P,\"propTypes\",{pageCount:i().number.isRequired,pageRangeDisplayed:i().number,marginPagesDisplayed:i().number,previousLabel:i().node,previousAriaLabel:i().string,prevPageRel:i().string,prevRel:i().string,nextLabel:i().node,nextAriaLabel:i().string,nextPageRel:i().string,nextRel:i().string,breakLabel:i().oneOfType([i().string,i().node]),breakAriaLabels:i().shape({forward:i().string,backward:i().string}),hrefBuilder:i().func,hrefAllControls:i().bool,onPageChange:i().func,onPageActive:i().func,onClick:i().func,initialPage:i().number,forcePage:i().number,disableInitialCallback:i().bool,containerClassName:i().string,className:i().string,pageClassName:i().string,pageLinkClassName:i().string,pageLabelBuilder:i().func,activeClassName:i().string,activeLinkClassName:i().string,previousClassName:i().string,nextClassName:i().string,previousLinkClassName:i().string,nextLinkClassName:i().string,disabledClassName:i().string,disabledLinkClassName:i().string,breakClassName:i().string,breakLinkClassName:i().string,extraAriaContext:i().string,ariaLabelBuilder:i().func,eventListener:i().string,renderOnZeroPageCount:i().func,selectedPageRel:i().string}),C(P,\"defaultProps\",{pageRangeDisplayed:2,marginPagesDisplayed:3,activeClassName:\"selected\",previousLabel:\"Previous\",previousClassName:\"previous\",previousAriaLabel:\"Previous page\",prevPageRel:\"prev\",prevRel:\"prev\",nextLabel:\"Next\",nextClassName:\"next\",nextAriaLabel:\"Next page\",nextPageRel:\"next\",nextRel:\"next\",breakLabel:\"...\",breakAriaLabels:{forward:\"Jump forward\",backward:\"Jump backward\"},disabledClassName:\"disabled\",disableInitialCallback:!1,pageLabelBuilder:function(e){return e},eventListener:\"onClick\",renderOnZeroPageCount:void 0,selectedPageRel:\"canonical\",hrefAllControls:!1});const k=P})(),n})()));\n//# sourceMappingURL=react-paginate.js.map", "//Import Library\r\nimport { useEffect, useState } from \"react\";\r\nimport _ from \"lodash\";\r\n\r\n//Import services/components\r\nimport RKIMapMarker from \"../../components/common/RKIMapMarker\";\r\nimport RKIMAP1 from \"../../components/common/RKIMap1\";\r\nimport { useTranslation } from 'next-i18next';\r\n\r\ninterface CountriesMapProps {\r\n  countries: {\r\n    data: Array<{\r\n      _id: string;\r\n      title: string;\r\n      coordinates?: Array<{\r\n        latitude: string;\r\n        longitude: string;\r\n      }>;\r\n    }>;\r\n  };\r\n}\r\n\r\nconst CountriesMap = (props: CountriesMapProps) => {\r\n  const { i18n } = useTranslation('common');\r\n  const currentLang = i18n.language;\r\n  const [activeMarker, setactiveMarker] = useState<any>({});\r\n  const [markerInfo, setMarkerInfo] = useState<any>({});\r\n  const [points, setPoints] = useState<any[]>([]);\r\n  const { countries } = props;\r\n\r\n  const MarkerInfo = (Props: { info: { id?: string; name?: string } }) => {\r\n    const { info } = Props;\r\n    return <a href={`/${currentLang}/country/show/${info?.id}`}>{info?.name}</a>;\r\n  };\r\n\r\n  const resetMarker = () => {\r\n    setactiveMarker(null);\r\n    setMarkerInfo(null);\r\n  };\r\n\r\n  const onMarkerClick = (propsinit: { name: string; id: string }, marker: any, _e: any) => {\r\n    resetMarker();\r\n    setactiveMarker(marker);\r\n    setMarkerInfo({\r\n      name: propsinit.name,\r\n      id: propsinit.id,\r\n    });\r\n  };\r\n\r\n  const setPointsFromCountries = () => {\r\n    const pointsvalue: any[] = [];\r\n    if (countries && countries.data) {\r\n      _.forEach(countries.data, (country) => {\r\n        pointsvalue.push({\r\n          title: country.title,\r\n          id: country._id,\r\n          lat:\r\n            country.coordinates && country.coordinates[0]\r\n              ? country.coordinates[0].latitude\r\n              : null,\r\n          lng:\r\n            country.coordinates && country.coordinates[0]\r\n              ? country.coordinates[0].longitude\r\n              : null,\r\n        });\r\n      });\r\n    }\r\n    setPoints([...pointsvalue]);\r\n  };\r\n\r\n  useEffect(() => {\r\n    setPointsFromCountries();\r\n  }, [countries]);\r\n\r\n  return (\r\n    <RKIMAP1\r\n      language={currentLang}\r\n      points={points}\r\n      height={300}\r\n      activeMarker={activeMarker}\r\n      markerInfo={<MarkerInfo info={markerInfo} />}\r\n      onClose={resetMarker}\r\n    >\r\n      {points && points.length >= 1\r\n        ? points.map((item, index) => {\r\n            if (item && item.lat) {\r\n              return (\r\n                <RKIMapMarker\r\n                  key={index}\r\n                  name={item.title}\r\n                  id={item.id}\r\n                  icon={{\r\n                    url: \"/images/map-marker-white.svg\",\r\n                  }}\r\n                  onClick={onMarkerClick}\r\n                  position={item}\r\n                />\r\n              );\r\n            }\r\n          })\r\n        : null}\r\n    </RKIMAP1>\r\n  );\r\n};\r\n\r\nCountriesMap.defaultProps = {\r\n  countries: { data: [] },\r\n};\r\n\r\nexport default CountriesMap;\r\n"], "names": ["ALPHABETIC_FILTERS_DE", "name", "id", "R<PERSON>IMapMarker", "countryId", "type", "icon", "position", "onClick", "title", "draggable", "lat", "lng", "<PERSON><PERSON>", "handleClick", "marker", "getPosition", "e", "<PERSON><PERSON><PERSON><PERSON>", "CountriesGlossary", "setselectedAlpha", "props", "i18n", "useTranslation", "alphabets", "language", "ALPHABETIC_FILTERS_EN", "div", "className", "ul", "map", "item", "i", "li", "a", "CountriesNamesListing", "t", "countries", "setActivePage", "data", "length", "Link", "href", "as", "_id", "ReactPaginate", "pageCount", "Math", "ceil", "totalCount", "limit", "pageRangeDisplayed", "marginPagesDisplayed", "onPageChange", "handlePageChange", "selectedItem", "selected", "pageNumber", "forcePage", "page", "containerClassName", "pageClassName", "pageLinkClassName", "previousClassName", "previousLinkClassName", "nextClassName", "nextLinkClassName", "activeClassName", "disabledClassName", "previousLabel", "next<PERSON><PERSON><PERSON>", "defaultProps", "connect", "state", "Country", "titleSearch", "title_de", "letter", "useState", "selectedRegions", "setSelectedRegions", "setCountries", "activePage", "countriesParams", "sort", "query", "select", "fetchCountries", "lang", "params", "totalPages", "response", "apiService", "get", "languageCode", "console", "log", "useEffect", "Container", "fluid", "Row", "Col", "md", "PageHeading", "CountriesMap", "RegionsMultiCheckboxes", "filtreg", "regionHandler", "RKIMapInfowindow", "onCloseClick", "children", "InfoWindow", "fill", "stoke", "road", "geometry", "mapStyles", "markerInfo", "RKIMap1", "activeMarker", "initialCenter", "height", "width", "zoom", "minZoom", "onClose", "locale", "useRouter", "isLoaded", "loadError", "useGoogleMaps", "style", "GoogleMap", "mapContainerStyle", "center", "defaultCenter", "onLoad", "onMapLoad", "setOptions", "styles", "options", "keyboardShortcuts", "streetViewControl", "panControl", "clickableIcons", "mapTypeControl", "fullscreenControl", "allregions", "setAllregions", "region", "setRegion", "RegionParams", "getworldregion", "RegionParams_initial", "Array", "isArray", "finalRegions", "selectedIds", "_", "regionItem", "isChecked", "push", "handleIndividualRegionChange", "updatedRegions", "updatedSelectedRegions", "for<PERSON>ach", "index", "code", "target", "checked", "filter", "n", "Form", "Check", "label", "onChange", "event", "handleAllChecked", "selected_Regions", "value", "<PERSON><PERSON>", "resetAllRegion", "h2", "currentLang", "set<PERSON><PERSON><PERSON><PERSON>", "setMarkerInfo", "points", "setPoints", "reset<PERSON><PERSON><PERSON>", "onMarkerClick", "propsinit", "_e", "setPointsFromCountries", "pointsvalue", "country", "coordinates", "latitude", "longitude", "RKIMAP1", "Props", "info", "MarkerInfo", "url"], "sourceRoot": "", "ignoreList": [11]}