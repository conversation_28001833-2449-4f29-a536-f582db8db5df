{"version": 3, "file": "static/chunks/pages/institution/InstitutionImageEditor-697c6bc6839e5140.js", "mappings": "yQAmIA,MAvH+B,OAAC,QAC9BA,CAAM,YAsHOC,EArHbC,CAAY,OACZC,CAAK,OACLC,CAAK,GAmH8BH,EAAC,KAlHpCI,CAAQ,SACRC,CAAO,CACH,GACE,CAACC,EAAOC,EAAS,CAAGC,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAS,GACrC,CAACC,EAAMC,EAAQ,CAAGF,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAS,IACnC,CAACG,EAAKC,EAAO,CAAGJ,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAM,MAC9BK,EAAYC,CAAAA,EAAAA,EAAAA,MAAAA,CAAMA,CAAM,MACtB,GAAEC,CAAC,CAAE,CAAGC,CAAAA,EAAAA,EAAAA,EAAAA,CAAcA,CAAC,UAE/BC,CAAAA,EAAAA,EAAAA,SAAAA,CAASA,CAAC,KACRP,EAAQN,EACV,EAAG,CAACA,EAAS,EAEb,IAAMc,EAAc,UAelB,IAAMC,EAbgB,CAACC,QAEPC,EADd,GAYyBC,CAZnBD,EAAMD,EAAQG,KAAK,CAAC,KACnBC,EAAAA,OAAOH,EAAAA,CAAG,CAAC,EAAE,CAACI,KAAK,CAAC,YAAbJ,KAAAA,EAAAA,CAAyB,CAAC,EAAE,CACnCK,CADOL,CACAM,KAAKN,CAAG,CAAC,EAAE,EACrBO,EAAIF,EAAKG,MAAM,CACZC,EAAQ,IAAIC,WAAWH,GAC9B,KAAOA,IAAK,CACVE,CAAK,CAACF,EAAE,CAAGF,EAAKM,UAAU,CAACJ,GAE7B,OAAO,IAAIK,KAAK,CAACH,EAAM,CAAE,CAAEI,KAAMV,CAAK,GACxC,EAEeX,EAAUsB,OAAO,CAACC,QAAQ,GAAGC,SAAS,CAAC,aAAc,KAIpEhC,EADgBiC,CADGC,KAEXC,EAFkBC,GAAG,EAAIF,OAAOG,SAAAA,EAAW,eACT,CAACvB,IAG3C,IAAMwB,EAAK,IAAIC,IAJsE,KAKrFD,EAAGE,MAAM,CAAC,OAAQ1B,EAAMV,GAExB,GAAI,CACF,IAAMqC,EAAM,MAAMC,EAAAA,CAAUA,CAACC,IAAI,CAAC,SAAUL,EAAI,CAC9C,eAAgB,qBAClB,GAEIG,GAAOA,EAAIG,GAAG,EAChB9C,EAAM2C,EAAIG,GAAG,CAEjB,CAAE,QAAM,CACN,KAjCa,CAiCPC,wCACR,CACAC,EAAAA,EAAKA,CAACC,OAAO,CAACrC,EAAE,sCAChBd,EAAa,IACbW,EAAO,MACPF,EAAQ,QACRH,EAAS,EACX,EAEA,MACE,+BACE,UAAC8C,MAAAA,UACC,WAACC,EAAAA,CAAKA,CAAAA,CACJC,KAAMxD,EACNyD,KAAK,KACLC,kBAAgB,cAChBC,OAAQ,IAAMzD,GAAa,GAC3B0D,QAAQ,cAER,WAACL,EAAAA,CAAKA,CAACM,IAAI,YACT,WAACP,MAAAA,CAAIQ,UAAU,mFACb,UAACC,IAAYA,CACXC,IAAKlD,EACLmD,MAAO,IACPC,OAAQ,IACRC,aAAc,EACd5D,MAAOA,EACP6D,IANWL,EAMJ,CAAC,EAAG,EAAG,EAAG,GAAI,CACrB5D,MAAOS,GAAYT,EACnBkE,CADazD,KACN,CAACqD,MAAO,OAAOC,OAAQ,MAAM,IAEtC,UAACZ,MAAAA,CAAIQ,UAAU,2BACb,UAACQ,OAAAA,UAAMtD,EAAE,qEAIb,UAACsC,MAAAA,CAAIQ,UAAU,qBACb,WAACS,EAAAA,CAAGA,CAAAA,WACF,UAACC,EAAAA,CAAGA,CAAAA,CAACC,GAAI,EAAGC,GAAI,EAAGC,GAAI,EAAGb,UAAU,gBAClC,UAACc,IAAAA,UAAG5D,EAAE,YAER,UAACwD,EAAAA,CAAGA,CAAAA,CAACC,GAAI,GAAIC,GAAI,GAAIC,GAAI,YACvB,UAACE,IAAWA,CACVC,MAAOvE,EACPwE,QAAQ,OACRC,IAAK,EACLC,IAAK,GACLC,KAAM,IACNC,QAAQ,UACRC,SAAU,GACR5E,EAAS6E,OAAOC,EAAYC,MAAM,CAACT,KAAK,eAOpD,WAACvB,EAAAA,CAAKA,CAACiC,MAAM,YACX,UAACC,EAAAA,CAAMA,CAAAA,CAACC,QAASvE,WAAcH,EAAE,UACjC,UAACyE,EAAAA,CAAMA,CAAAA,CAACN,QAAQ,SAASO,QAAS,IAAMxF,EAAa,aAClDc,EAAE,qBAOjB,mBChIA,4CACA,sCACA,WACA,OAAe,EAAQ,KAA2D,CAClF,EACA,SAFsB", "sources": ["webpack://_N_E/./pages/institution/InstitutionImageEditor.tsx", "webpack://_N_E/?8b94"], "sourcesContent": ["//Import Library\r\nimport React, { useState, useEffect, useRef } from \"react\";\r\nimport AvatarEditor from \"react-avatar-editor\";\r\nimport RangeSlider from \"react-bootstrap-range-slider\";\r\nimport { Modal, Button, Row, Col } from \"react-bootstrap\";\r\n\r\nimport toast from 'react-hot-toast';\r\n\r\n//Import services/components\r\nimport apiService from \"../../services/apiService\";\r\nimport { useTranslation } from 'next-i18next';\r\n\r\nconst InstitutionImageEditor = ({\r\n  isOpen,\r\n  onModalClose,\r\n  image,\r\n  getId,\r\n  fileName,\r\n  getBlob,\r\n}: any) => {\r\n  const [scale, setScale] = useState<number>(1);\r\n  const [name, setName] = useState<string>(\"\");\r\n  const [img, setImg] = useState<any>(null);\r\n  const editorRef = useRef<any>(null);\r\n    const { t } = useTranslation('common');\r\n\r\n  useEffect(() => {\r\n    setName(fileName);\r\n  }, [fileName]);\r\n  const newLocal = \"Something wrong in server || your data!\";\r\n  const cropHandler = async () => {\r\n    /*****Helper Function to convert to blob******/\r\n    const dataURLtoBlob = (dataurl: string) => {\r\n      const arr = dataurl.split(\",\");\r\n      const  mime = arr[0].match(/:(.*?);/)?.[1];\r\n      const  bstr = atob(arr[1]);\r\n      let n = bstr.length;\r\n      const  u8arr = new Uint8Array(n);\r\n      while (n--) {\r\n        u8arr[n] = bstr.charCodeAt(n);\r\n      }\r\n      return new Blob([u8arr], { type: mime });\r\n    };\r\n    /*****End ********/\r\n    const canvas = editorRef.current.getImage().toDataURL(\"image/jpeg\", 0.6);\r\n    const blob = dataURLtoBlob(canvas);\r\n    const urlCreator = window.URL || window.webkitURL; //For Creating the url for preview\r\n    const blobUrl = urlCreator.createObjectURL(blob);\r\n    getBlob(blobUrl);\r\n\r\n    const fd = new FormData();\r\n    fd.append(\"file\", blob, name);\r\n\r\n    try {\r\n      const res = await apiService.post(\"/image\", fd, {\r\n        \"Content-Type\": \"multipart/form-data\",\r\n      });\r\n\r\n      if (res && res._id) {\r\n        getId(res._id);\r\n      }\r\n    } catch {\r\n      throw newLocal;\r\n    }\r\n    toast.success(t(\"toast.CroppedtheimageSuccessfully\"));\r\n    onModalClose(false);\r\n    setImg(null);\r\n    setName(\"none\");\r\n    setScale(1);\r\n  };\r\n\r\n  return (\r\n    <>\r\n      <div>\r\n        <Modal\r\n          show={isOpen}\r\n          size=\"lg\"\r\n          aria-labelledby=\"ProfileEdit\"\r\n          onHide={() => onModalClose(false)}\r\n          centered\r\n        >\r\n          <Modal.Body>\r\n            <div className=\"d-flex flex-column justify-content-center align-items-center imgRotate\">\r\n              <AvatarEditor\r\n                ref={editorRef}\r\n                width={700}\r\n                height={400}\r\n                borderRadius={2}\r\n                scale={scale}\r\n                color={[0, 0, 0, 0.6]}\r\n                image={img ? img : image}\r\n                style={{width: \"100%\",height: \"auto\"}}\r\n              />\r\n              <div className=\"info-identifier\">\r\n                <span>{t(\"ThisareawillcontainyourInstitutionandfocalpointinformation\")}</span>\r\n              </div>\r\n            </div>\r\n\r\n            <div className=\"mx-2 my-3\">\r\n              <Row>\r\n                <Col sm={1} md={1} lg={1} className=\"pe-0\">\r\n                  <b>{t(\"Zoom\")}</b>\r\n                </Col>\r\n                <Col sm={11} md={11} lg={11}>\r\n                  <RangeSlider\r\n                    value={scale}\r\n                    tooltip=\"auto\"\r\n                    min={1}\r\n                    max={10}\r\n                    step={0.01}\r\n                    variant=\"primary\"\r\n                    onChange={(changeEvent) =>\r\n                      setScale(Number(changeEvent.target.value))\r\n                    }\r\n                  />\r\n                </Col>\r\n              </Row>\r\n            </div>\r\n          </Modal.Body>\r\n          <Modal.Footer>\r\n            <Button onClick={cropHandler}>{t(\"Crop\")}</Button>\r\n            <Button variant=\"danger\" onClick={() => onModalClose(false)}>\r\n              {t(\"Cancel\")}\r\n            </Button>\r\n          </Modal.Footer>\r\n        </Modal>\r\n      </div>\r\n    </>\r\n  );\r\n};\r\n\r\nexport default InstitutionImageEditor;\r\n", "\n    (window.__NEXT_P = window.__NEXT_P || []).push([\n      \"/institution/InstitutionImageEditor\",\n      function () {\n        return require(\"private-next-pages/institution/InstitutionImageEditor.tsx\");\n      }\n    ]);\n    if(module.hot) {\n      module.hot.dispose(function () {\n        window.__NEXT_P.push([\"/institution/InstitutionImageEditor\"])\n      });\n    }\n  "], "names": ["isOpen", "InstitutionImageEditor", "onModalClose", "image", "getId", "fileName", "getBlob", "scale", "setScale", "useState", "name", "setName", "img", "setImg", "editor<PERSON><PERSON>", "useRef", "t", "useTranslation", "useEffect", "<PERSON><PERSON><PERSON><PERSON>", "blob", "dataurl", "arr", "canvas", "split", "mime", "match", "bstr", "atob", "n", "length", "u8arr", "Uint8Array", "charCodeAt", "Blob", "type", "current", "getImage", "toDataURL", "urlCreator", "window", "blobUrl", "URL", "webkitURL", "fd", "FormData", "append", "res", "apiService", "post", "_id", "newLocal", "toast", "success", "div", "Modal", "show", "size", "aria-<PERSON>by", "onHide", "centered", "Body", "className", "AvatarEditor", "ref", "width", "height", "borderRadius", "color", "style", "span", "Row", "Col", "sm", "md", "lg", "b", "RangeSlider", "value", "tooltip", "min", "max", "step", "variant", "onChange", "Number", "changeEvent", "target", "Footer", "<PERSON><PERSON>", "onClick"], "sourceRoot": "", "ignoreList": []}