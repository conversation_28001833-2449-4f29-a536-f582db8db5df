{"version": 3, "file": "static/chunks/pages/event/components/EventCoverSection-0f3e2b6561e17138.js", "mappings": "gFACA,4CACA,sCACA,WACA,OAAe,EAAQ,KAA2D,CAClF,EACA,SAFsB", "sources": ["webpack://_N_E/?66eb"], "sourcesContent": ["\n    (window.__NEXT_P = window.__NEXT_P || []).push([\n      \"/event/components/EventCoverSection\",\n      function () {\n        return require(\"private-next-pages/event/components/EventCoverSection.tsx\");\n      }\n    ]);\n    if(module.hot) {\n      module.hot.dispose(function () {\n        window.__NEXT_P.push([\"/event/components/EventCoverSection\"])\n      });\n    }\n  "], "names": [], "sourceRoot": "", "ignoreList": []}