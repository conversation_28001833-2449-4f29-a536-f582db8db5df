"use strict";(()=>{var e={};e.id=4084,e.ids=[636,3220,4084],e.modules={123:e=>{e.exports=require("dom-helpers/removeEventListener")},1332:e=>{e.exports=require("react-custom-scrollbars-2")},1428:e=>{e.exports=import("axios")},1680:e=>{e.exports=require("@restart/ui/utils")},1919:e=>{e.exports=require("react-transition-group/Transition")},3892:e=>{e.exports=require("classnames")},4048:e=>{e.exports=require("react-truncate")},6009:e=>{e.exports=require("dom-helpers/ownerDocument")},6952:e=>{e.exports=require("@restart/ui/Modal")},7374:e=>{e.exports=require("@restart/ui/Dropdown")},8732:e=>{e.exports=require("react/jsx-runtime")},9653:e=>{e.exports=require("@restart/ui/Overlay")},11242:e=>{e.exports=require("redux-persist/integration/react")},11688:e=>{e.exports=require("@restart/hooks/useWillUnmount")},12388:e=>{e.exports=require("dom-helpers/addEventListener")},13364:e=>{e.exports=require("redux-saga/effects")},14062:e=>{e.exports=import("react-redux")},14078:e=>{e.exports=import("swr")},14332:e=>{e.exports=require("uncontrollable")},16116:e=>{e.exports=require("invariant")},17098:(e,r,t)=>{t.a(e,async(e,a)=>{try{t.r(r),t.d(r,{default:()=>S});var s=t(8732),o=t(19918),i=t.n(o),n=t(82015),u=t.n(n),l=t(59549),p=t(12403),d=t(91353),c=t(27825),x=t.n(c),h=t(42893),g=t(56084),m=t(63487),q=t(94544),f=t(88751),P=e([h,m]);[h,m]=P.then?(await P)():P;let S=()=>{let{t:e,i18n:r}=(0,f.useTranslation)("common"),t="fr"===r.language?"en":r.language,a=t?`title.${t}`:"title.en",[o,c]=(0,n.useState)([]),[,P]=(0,n.useState)(!1),[S,b]=(0,n.useState)(0),[v,A]=(0,n.useState)(10),[y,j]=u().useState(""),[w,_]=u().useState(!1),[z,C]=(0,n.useState)(!1),[M,k]=(0,n.useState)({}),[T]=(0,n.useState)(a),E=async r=>{let a=x().findIndex(o,{_id:r.target.name});if(a>-1){o[a].enabled=!o[a].enabled,c([...o]);let s=await m.A.patch(`/hazard/${r.target.name}`,o[a]);s&&s._id?h.default.success(`${s.title[t]} ${e("updatedSuccessfully")}`):h.default.error(s)}else h.default.error(e("indexNotFound"))},R=({_id:e,enabled:r})=>(0,s.jsx)(l.A.Check,{className:"ms-4",type:"switch",name:e,id:e,label:"",checked:r,onChange:e=>E(e)}),D=[{name:e("menu.hazards"),selector:T,cell:e=>e&&e.title&&e.title[t]?e.title[t]:""},{name:e("hazardType"),selector:"hazard_type",cell:e=>e&&e.hazard_type&&e.hazard_type.title?e.hazard_type.title:""},{name:e("published"),selector:"enabled",cell:e=>(0,s.jsx)(R,{...e})},{name:e("action"),selector:"",cell:e=>(0,s.jsxs)("div",{children:[(0,s.jsx)(i(),{href:"/adminsettings/[...routes]",as:`/adminsettings/edit_hazard/${e._id}`,children:(0,s.jsx)("i",{className:"icon fas fa-edit"})}),"\xa0",(0,s.jsx)("span",{onClick:()=>G(e),style:{cursor:"pointer"},children:(0,s.jsx)("i",{className:"icon fas fa-trash-alt"})})]})}];(0,n.useEffect)(()=>{H()},[]);let N={sort:{[T]:"asc"},limit:v,page:1,query:{}},H=async()=>{P(!0);let e=await m.A.get("/hazard",N);e&&e.data&&e.data.length>0&&(c(e.data),b(e.totalCount),P(!1))},I=async(e,r)=>{N.limit=e,N.page=r,P(!0);let t=await m.A.get("/hazard",N);t&&t.data&&t.data.length>0&&(c(t.data),A(e),P(!1))},G=async e=>{k(e._id),C(!0)},O=async()=>{try{await m.A.remove(`/hazard/${M}`),H(),C(!1),h.default.success(e("adminsetting.hazard.Table.hazardDeletedSuccessfully"))}catch(r){h.default.error(e("adminsetting.hazard.Table.errorDeletingHazard"))}},$=()=>C(!1),W=u().useMemo(()=>{let e=e=>{e&&(N.query={[T]:e}),H()},r=x().debounce(r=>e(r),Number("500")||300);return(0,s.jsx)(q.default,{onFilter:e=>{j(e.target.value),r(e.target.value)},onClear:()=>{y&&(_(!w),j(""))},filterText:y})},[y]);return(0,s.jsxs)("div",{children:[(0,s.jsxs)(p.A,{show:z,onHide:$,children:[(0,s.jsx)(p.A.Header,{closeButton:!0,children:(0,s.jsx)(p.A.Title,{children:e("deleteHazard")})}),(0,s.jsx)(p.A.Body,{children:e("areYouSureWantToDeleteThisHazard")}),(0,s.jsxs)(p.A.Footer,{children:[(0,s.jsx)(d.A,{variant:"secondary",onClick:$,children:e("cancel")}),(0,s.jsx)(d.A,{variant:"primary",onClick:O,children:e("yes")})]})]}),(0,s.jsx)(g.A,{columns:D,data:o,totalRows:S,pagServer:!0,subheader:!0,resetPaginationToggle:w,subHeaderComponent:W,handlePerRowsChange:I,handlePageChange:e=>{N.limit=v,N.page=e,H()}})]})};a()}catch(e){a(e)}})},18622:e=>{e.exports=require("yup")},19121:e=>{e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},22326:e=>{e.exports=require("react-dom")},22541:e=>{e.exports=require("redux-persist/lib/storage")},25303:e=>{e.exports=require("@restart/ui/NavItem")},26324:e=>{e.exports=require("warning")},27825:e=>{e.exports=require("lodash")},27910:e=>{e.exports=require("stream")},28217:e=>{e.exports=require("@restart/ui/DropdownItem")},29021:e=>{e.exports=require("fs")},29294:e=>{e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},29628:e=>{e.exports=require("dom-helpers/addClass")},29825:e=>{e.exports=require("prop-types")},29841:e=>{e.exports=require("dom-helpers/hasClass")},30362:e=>{e.exports=require("@restart/ui/DropdownToggle")},33873:e=>{e.exports=require("path")},36653:e=>{e.exports=require("nprogress")},36955:e=>{e.exports=require("@restart/hooks/useBreakpoint")},37766:e=>{e.exports=require("@restart/hooks/useMergedRefs")},38609:e=>{e.exports=require("react-data-table-component")},39756:e=>{e.exports=import("redux")},40051:e=>{e.exports=require("dom-helpers/removeClass")},40361:e=>{e.exports=require("next/dist/compiled/next-server/pages.runtime.prod.js")},42893:e=>{e.exports=import("react-hot-toast")},43294:e=>{e.exports=require("formik")},50009:e=>{e.exports=require("@restart/ui/DropdownMenu")},56084:(e,r,t)=>{t.d(r,{A:()=>l});var a=t(8732);t(82015);var s=t(38609),o=t.n(s),i=t(88751),n=t(30370);function u(e){let{t:r}=(0,i.useTranslation)("common"),t={rowsPerPageText:r("Rowsperpage")},{columns:s,data:u,totalRows:l,resetPaginationToggle:p,subheader:d,subHeaderComponent:c,handlePerRowsChange:x,handlePageChange:h,rowsPerPage:g,defaultRowsPerPage:m,selectableRows:q,loading:f,pagServer:P,onSelectedRowsChange:S,clearSelectedRows:b,sortServer:v,onSort:A,persistTableHead:y,sortFunction:j,...w}=e,_={paginationComponentOptions:t,noDataComponent:r("NoData"),noHeader:!0,columns:s,data:u||[],dense:!0,paginationResetDefaultPage:p,subHeader:d,progressPending:f,subHeaderComponent:c,pagination:!0,paginationServer:P,paginationPerPage:m||10,paginationRowsPerPageOptions:g||[10,15,20,25,30],paginationTotalRows:l,onChangeRowsPerPage:x,onChangePage:h,selectableRows:q,onSelectedRowsChange:S,clearSelectedRows:b,progressComponent:(0,a.jsx)(n.A,{}),sortIcon:(0,a.jsx)("i",{className:"sort-icon fas fa-exchange-alt"}),sortServer:v,onSort:A,sortFunction:j,persistTableHead:y,className:"rki-table"};return(0,a.jsx)(o(),{..._})}u.defaultProps={subHeader:!1,pagination:!0,totalRows:null,pagServer:!0,onSelectedRowsChange:null,clearSelectedRows:!1,sortServer:!1,persistTableHead:!1};let l=u},57664:e=>{e.exports=require("dom-helpers/scrollbarSize")},58928:e=>{e.exports=require("dom-helpers/canUseDOM")},59717:e=>{e.exports=require("@restart/ui/DropdownContext")},60560:e=>{e.exports=require("dom-helpers/contains")},63033:e=>{e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},65447:e=>{e.exports=require("@restart/ui/Button")},67364:e=>{e.exports=require("@restart/hooks/useCallbackRef")},68455:e=>{e.exports=import("redux-saga")},69722:e=>{e.exports=require("es6-promise")},74075:e=>{e.exports=require("zlib")},74716:e=>{e.exports=require("moment")},74987:e=>{e.exports=require("@restart/ui/ModalManager")},78097:e=>{e.exports=require("next-redux-saga")},78634:e=>{e.exports=require("@restart/ui/Anchor")},80237:(e,r)=>{Object.defineProperty(r,"A",{enumerable:!0,get:function(){return t}});var t=function(e){return e.PAGES="PAGES",e.PAGES_API="PAGES_API",e.APP_PAGE="APP_PAGE",e.APP_ROUTE="APP_ROUTE",e.IMAGE="IMAGE",e}({})},81366:e=>{e.exports=require("redux-auth-wrapper/connectedAuthWrapper")},81413:(e,r)=>{Object.defineProperty(r,"M",{enumerable:!0,get:function(){return function e(r,t){return t in r?r[t]:"then"in r&&"function"==typeof r.then?r.then(r=>e(r,t)):"function"==typeof r&&"default"===t?r:void 0}}})},81521:e=>{e.exports=require("dom-helpers/querySelectorAll")},81895:e=>{e.exports=require("@restart/hooks/useEventCallback")},82015:e=>{e.exports=require("react")},84227:(e,r,t)=>{t.a(e,async(e,a)=>{try{t.r(r),t.d(r,{config:()=>m,default:()=>c,getServerSideProps:()=>g,getStaticPaths:()=>h,getStaticProps:()=>x,reportWebVitals:()=>q,routeModule:()=>A,unstable_getServerProps:()=>b,unstable_getServerSideProps:()=>v,unstable_getStaticParams:()=>S,unstable_getStaticPaths:()=>P,unstable_getStaticProps:()=>f});var s=t(63885),o=t(80237),i=t(81413),n=t(9616),u=t.n(n),l=t(72386),p=t(17098),d=e([l,p]);[l,p]=d.then?(await d)():d;let c=(0,i.M)(p,"default"),x=(0,i.M)(p,"getStaticProps"),h=(0,i.M)(p,"getStaticPaths"),g=(0,i.M)(p,"getServerSideProps"),m=(0,i.M)(p,"config"),q=(0,i.M)(p,"reportWebVitals"),f=(0,i.M)(p,"unstable_getStaticProps"),P=(0,i.M)(p,"unstable_getStaticPaths"),S=(0,i.M)(p,"unstable_getStaticParams"),b=(0,i.M)(p,"unstable_getServerProps"),v=(0,i.M)(p,"unstable_getServerSideProps"),A=new s.PagesRouteModule({definition:{kind:o.A.PAGES,page:"/adminsettings/hazard/hazardTable",pathname:"/adminsettings/hazard/hazardTable",bundlePath:"",filename:""},components:{App:l.default,Document:u()},userland:p});a()}catch(e){a(e)}})},86842:e=>{e.exports=require("@restart/ui/SelectableContext")},87571:e=>{e.exports=require("dom-helpers/transitionEnd")},88751:e=>{e.exports=require("next-i18next")},93787:e=>{e.exports=require("redux-persist")},94544:(e,r,t)=>{t.r(r),t.d(r,{default:()=>l});var a=t(8732),s=t(7082),o=t(83551),i=t(49481),n=t(84517),u=t(88751);let l=({filterText:e,onFilter:r,onClear:t})=>{let{t:l}=(0,u.useTranslation)("common");return(0,a.jsx)(s.A,{fluid:!0,className:"p-0",children:(0,a.jsx)(o.A,{children:(0,a.jsx)(i.A,{md:4,className:"p-0",children:(0,a.jsx)(n.A,{type:"text",className:"searchInput",placeholder:l("adminsetting.hazard.Search"),"aria-label":"Search",value:e,onChange:r})})})})}},94947:e=>{e.exports=require("@restart/hooks/useTimeout")},96196:e=>{e.exports=require("next-redux-wrapper")},98320:e=>{e.exports=require("@restart/hooks/useIsomorphicEffect")},99460:e=>{e.exports=require("dom-helpers/css")}};var r=require("../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),a=r.X(0,[6089,9216,9616,2386],()=>t(84227));module.exports=a})();