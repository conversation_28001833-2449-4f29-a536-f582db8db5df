{"version": 3, "file": "static/chunks/2697-aaaced30b342592a.js", "mappings": "8LAgDA,MAxCA,SAA8BA,CAAU,EACtC,GAAM,CAACC,EAAQC,EAAU,CAAGC,CAAAA,EAAAA,EAAAA,EAuCfC,MAvCeD,CAAQA,CAAQ,EAAE,EAExCE,EAAkB,MAqCSD,EAAC,EApChC,IAAME,EAAW,MAAMC,EAAAA,CAAUA,CAACC,GAAG,CAAC,cAAe,CAAC,MAAS,CAAC,MAAS,gBAAgB,CAAC,GACtFF,GAAYA,EAASG,IAAI,EAAE,EACTH,EAASG,IAAI,CAAC,EAAE,CAACC,GAAG,CAE5C,EAEMC,EAAsB,MAAOC,IAMjC,IAAMN,EAAW,MAAMC,EAAAA,CAAUA,CAACC,GAAG,CAAC,WALT,CAKqBK,MAJvC,CAAC,KAAQ,SAAU,YAAeD,CAAY,EACvD,KAAQ,CAAE,WAAc,MAAO,EAC/B,MAAS,EACX,GAEA,GAAIN,GAAYA,EAASG,IAAI,EAAE,EAEnBK,EAAE,CAAC,CACX,IAAMC,EAAQC,IAAAA,MAAQ,CAACV,EAASG,IAAI,CAAE,CAF1B,cAEmCT,EAAMc,EAAE,GACvDE,IAAAA,OAAS,CAACD,EAAO,SAASE,CAAG,CAAEC,CAAC,EAC9BH,CAAK,CAACG,EAAE,CAACC,MAAM,CAAG,EACpB,GACAjB,EAAUa,EACX,CAEL,EAQA,MANAK,CAAAA,EAAAA,EAAAA,SAAAA,CAASA,CAAC,KACHpB,EAAMc,EAAE,EAAC,GAGhB,EAAE,CAACd,EAAMc,EAAE,CAAC,EAGV,UAACO,EAAAA,CAAWA,CAAAA,CAACC,WAAYrB,EAAQsB,YAAY,IAACC,eAAe,KAEjE,0GCrCA,IAAMC,EAAiB,CACrBC,UAAW,YACXC,YAAa,cACbC,MAAO,QACPC,QAAS,UACTC,OAAQ,QACV,EAoEA,EAAeC,CAAAA,EAAAA,EAAAA,EAAAA,CAAOA,CAAC,GAAgBC,GA1DG,IACxC,GAAM,MAAEC,CAAI,CAAEC,UAAQ,YAAEC,CAAU,CAAE,CAAGnC,EACjC,CAACoC,EAAUC,EAAY,CAAGlC,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,EAAU,GAC5C,CAACmC,EAAWC,EAAa,CAAGpC,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAM,IAG1CqC,EAA2B,UAC/B,GAAI,QAACP,EAAAA,KAAAA,EAAAA,EAAMvB,GAAAA,EAAK,CAAXuB,MACL,IAAMQ,EAAY,MAAMlC,EAAAA,CAAUA,CAACC,GAAG,CAAC,QAAS,CAACkC,MAAO,CAACC,UAAWT,EAAUD,KAAMA,EAAKvB,GAAG,CAAEkC,QAASnB,CAAc,CAACU,EAAW,CAAC,GAC9HM,GAAaA,EAAUhC,IAAI,EAAIgC,EAAUhC,IAAI,CAACoC,MAAM,CAAG,GAAG,CAC5DN,EAAaE,EAAUhC,IAAI,CAAC,EAAE,EAC9B4B,EAAY,IAEhB,EAEMS,EAAkB,MAAOC,IAE7B,GADAA,EAAEC,cAAc,GACZ,QAACf,EAAAA,KAAAA,EAAAA,EAAMvB,GAAAA,EAAK,CAAXuB,MACL,IAAMgB,EAAQ,CAACb,EACTc,EAAc,CAClBC,YAAahB,EACbQ,UAAWT,EACXD,KAAMA,EAAKvB,GAAG,CACdkC,QAASnB,CAAc,CAACU,EAAW,EAErC,GAAIc,EAAM,CACR,IAAMG,EAAc,MAAM7C,EAAAA,CAAUA,CAAC8C,IAAI,CAAC,QAASH,GAC/CE,GAAUA,EAAO1C,GAAG,EAAE,CACxB6B,EAAaa,GACbf,EAAYY,GAEhB,KAAO,CACL,IAAMK,EAAW,MAAM/C,EAAAA,CAAUA,CAACgD,MAAM,CAAC,SAAuB,OAAdjB,EAAU5B,GAAG,GAC3D4C,GAAYA,EAASE,CAAC,EAAE,EACdP,EAEhB,CACF,EAKA,MAHA7B,CAAAA,EAAAA,EAAAA,SAAAA,CAASA,CAAC,KACRoB,GACF,EAAE,EAAE,EAEF,UAACiB,MAAAA,CAAIC,UAAU,0BACb,WAACC,IAAAA,CAAEC,KAAK,GAAGC,QAASf,YAClB,UAACgB,OAAAA,CAAKJ,UAAU,iBACbtB,EACC,UAAC2B,EAAAA,CAAeA,CAAAA,CAACL,UAAU,sBAAsBM,KAAMC,EAAAA,GAAaA,CAAEC,MAAM,YAE5E,UAACH,EAAAA,CAAeA,CAAAA,CAACL,UAAU,sBAAsBM,KAAMG,EAAAA,GAAYA,CAAED,MAAM,WAG/E,UAACH,EAAAA,CAAeA,CAAAA,CAACL,UAAU,WAAWM,KAAMI,EAAAA,GAAUA,CAAEF,MAAM,gBAItE,iVC1DA,IAAMG,EAAkE,CACtEC,KAAM,wCACNC,MAAO,4CACT,EACMC,EAAU,CACd,CACEC,KAAM,YACNC,SAAU,WACVC,UAAU,CACZ,EACA,CACEF,KAAM,QACNC,SAAU,QACVC,UAAU,CACZ,EACD,CA+QD,EA5QmB,IACjB,GAAM,GAAEC,CAAC,MAAEC,CAAI,CAAE,CAAGC,CAAAA,EAAAA,EAAAA,EAAAA,CAAcA,CAAC,SA2QZC,CA1QjB,CAACC,EAAeC,EAAU,CAAG9E,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAM,CAC/C+E,YAAa,GACbC,MAAO,EACT,GACMC,EAAgC,OAAlBP,EAAKQ,QAAQ,CAAY,KAAOR,EAAKQ,QAAQ,CAC3D,CAACC,EAAeC,EAAiB,CAAGpF,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAC,EAAE,EAC/C,CAACqF,EAAaC,EAAe,CAAGtF,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAC,EAAE,EAC3C,CAACuF,EAAUC,EAAY,CAAGxF,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAQ,EAAE,EAC5C,CAACyF,EAAO,CAAGzF,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAC,EAAE,EACtB,CAAE0F,EAAQ,CAAI1F,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAACiF,GACvB,CAACU,EAAgB5F,EAAU,CAAQC,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAC,EAAE,EAC9C,CAAC4F,EAAYC,EAAc,CAAQ7F,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAC,EAAE,EAC9C,CAAC8F,EAAeC,EAAiB,CAAG/F,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,EAAC,GAC7C,CAACgG,EAAeC,EAAiB,CAAGjG,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,EAAC,GAE7CkG,EADSC,CAAAA,EAAAA,EAAAA,SAAAA,CAASA,GACG5D,KAAK,CAAC2D,MAAM,EAAI,EAAE,CAgBvCE,EAAoB,CACxBC,KAAM,CAAEC,eAAgB,KAAM,EAC9BC,UAAU,CACZ,EAEMC,EAAuB,CAC3BH,KAAM,CAAEC,eAAgB,KAAM,EAC9BG,UAAW,KACXC,gBAAgB,CAClB,EAGMC,EAAiB,UACrB,IAAMC,EAAoB,EAAE,CAC5Bb,GAAiB,GACjB,IAAMc,EAAa,MAAMzG,EAAAA,CAAUA,CAACC,GAAG,CAAC,WAAqB,OAAV6F,CAAM,CAAC,EAAE,EAAIM,GAC3DK,GAAcA,EAAWvG,IAAI,EAAIwG,MAAMC,OAAO,CAACF,EAAWvG,IAAI,GAAKuG,EAAWvG,IAAI,CAACoC,MAAM,EAAI,GAAE,CAClGmE,EAAWvG,IAAI,CAAC0G,OAAO,CAAC,CAACC,EAAcC,KACrCD,EAAQ1B,QAAQ,EAAI0B,EAAQ1B,QAAQ,CAAC7C,MAAM,CAAG,GAAKuE,EAAQ1B,QAAQ,CAAC4B,GAAG,CAAC,CAACC,EAAUrG,KAEjFqG,EAAIrC,WAAW,CADKkC,EACFlC,QADkB,CAAChE,EAAE,CAACsG,MAAM,CAE9CT,EAAWU,IAAI,CAACF,EAClB,EACF,GACF5B,EAAYoB,IAEZb,GAAiB,EACnB,EAEOwB,EAAmB,UACxB,IAAMX,EAAoB,EAAE,CAC5BX,GAAiB,GACjB,IAAMuB,EAAc,MAAMpH,EAAAA,CAAUA,CAACC,GAAG,CAAC,WAAqB,OAAV6F,CAAM,CAAC,EAAE,EAAIE,GAC9DoB,GAAeA,EAAYlH,IAAI,EAAIwG,MAAMC,OAAO,CAACS,EAAYlH,IAAI,GAAKkH,EAAYlH,IAAI,CAACoC,MAAM,EAAG,GAAG,CACtG8E,EAAYlH,IAAI,CAAC0G,OAAO,CAAC,CAACC,EAAcC,KACtCD,EAAQ1B,QAAQ,EAAI0B,EAAQ1B,QAAQ,CAAC7C,MAAM,CAAG,GAAKuE,EAAQ1B,QAAQ,CAAC4B,GAAG,CAAC,CAACC,EAAUrG,KAE/EqG,EAAIrC,WAAW,CADKkC,EACFlC,QADkB,CAAChE,EAAE,CAACsG,MAAM,CAE9CT,EAAWU,IAAI,CAACF,EAClB,EACF,GACAvB,EAAce,IAEhBX,GAAiB,EACnB,EAEMwB,EAAc,UAClB,GAAI,CACF,IAAMC,EAAkB,MAAMtH,EAAAA,CAAUA,CAACC,GAAG,CAAC,WAAqB,OAAV6F,CAAM,CAAC,EAAE,GAC3DyB,EAAkB,MAAMvH,EAAAA,CAAUA,CAACC,GAAG,CAAE,+CAA+C,CAAEkC,MAAO,CAAEZ,OAAQuE,CAAM,CAAC,EAAE,EAAI0B,OAAQ,+CAAgD,GACrL,GAAID,GAAmBA,EAAgBrH,IAAI,EAAIqH,EAAgBrH,IAAI,CAACoC,MAAM,CAAG,EAAG,CAC9E,IAAMmF,EAAiBF,EAAgBrH,IAAI,CAAC6G,GAAG,CAAEW,IAC/CA,EAAEC,YAAY,CAACxH,GAAG,CAAGuH,EAAEvH,GAAG,CACnBuH,EAAEC,YAAY,GAEvB3C,EAAiByC,EACnB,CACA,IAAIG,QAAqBN,EAAAA,KAAAA,EAAAA,EAAiBO,UAAU,CAACC,MAA5BR,CAAmC,GAAY,QAACA,EAAAA,KAAAA,EAAAA,EAAiBS,OAAO,CAAChB,GAAG,CAAEiB,GAAWA,EAAEC,KAAK,EAAEC,QAAQ,CAACF,EAAAA,GACpI,GAAIJ,EAAmBtF,MAAM,CAAG,EAAG,CACjC,IAAM6F,EAAW,MAAMnI,EAAAA,CAAUA,CAACC,GAAG,CAAE,SAAS,CAC9CkC,MAAO,CAACiG,cAAe,WAAYC,WAAW,CAAI,EAClDpC,KAAM,CAAEqC,SAAU,KAAM,EACxBC,MAAO,GACT,GACIC,EAAuBZ,EAAmBb,GAAG,CAAC,GAAYoB,EAASjI,IAAI,CAAC4H,MAAM,CAAC,GAAYW,EAAER,KAAK,EAAID,GAAG1F,MAAM,CAAG,EAAI6F,EAASjI,IAAI,CAAC4H,MAAM,CAAC,GAAYW,EAAER,KAAK,EAAID,EAAE,CAAC,EAAE,CAAC7H,GAAG,CAAG,IAElL,GAAIqI,CADJA,EAAuBA,EAAqBV,MAAM,CAAC,GAAiB,IAALE,EAAK,EAC3C1F,MAAM,CAAG,EAAG,CACnC,IAAMoG,EAAiBF,EACjBG,EAAarB,GAAmBA,EAAgBS,OAAO,CAAChB,GAAG,CAAC,GAAe6B,EAAKzI,GAAG,CACvE,OAAMH,EAAAA,CAAUA,CAAC6I,KAAK,CAAC,WAA+B,OAApBvB,EAAgBnH,GAAG,EAAI,CACzE,GAAGmH,CAAe,CAClBS,QAAS,IAAIY,KAAeD,EAAe,CAC3Cb,WAA8C,KAAlCP,EAAgBO,UAAU,CAAC,EAAE,CAAU,GAAKP,EAAgBO,UAAU,GAEpF,IAAIiB,QAAmBJ,EAAAA,KAAAA,EAAAA,EAAgB3B,GAAG,CAAC,GAAYoB,OAAAA,EAAhCO,KAAgCP,EAAAA,EAAUjI,IAAI,CAAC4H,KAAfK,CAAqB,CAAC,GAAYM,EAAEtI,GAAG,EAAI6H,GAAG1F,MAAAA,EAAS,EAAI6F,EAASjI,IAAI,CAAC4H,MAAM,CAAC,GAAYW,EAAEtI,GAAG,EAAI6H,EAAE,CAAC,EAAE,CAAG,EAAE,EACtKc,EAAmBA,EAAiBhB,MAAM,CAAC,GAAiB,IAALE,GACvDV,EAAgBS,OAAO,CAAGT,EAAgBS,OAAO,CAACgB,MAAM,CAACD,EAC3D,CACF,CACA5D,EAAeoC,EAAgBS,OAAO,EACtCrD,EAAU4C,GACV3H,EAAU,CAAC,CACTqJ,MAAO,SACPC,MAAO3B,EAAgB4B,UAAU,CACjCC,IAAK7B,EAAgB8B,QAAQ,CAC7BxI,QAAQ,EACRyI,OAAQ/B,EAAgB+B,MAAM,CAC9BC,WAAYhC,EAAgBgC,UAAU,CACtCnE,SAAUmC,EAAgBnC,QAAQ,CAClCoE,QAASjC,EAAgBiC,OAAO,EAEhC,CACJ,CAAE,MAAO/G,EAAG,CACVgH,QAAQC,GAAG,CAACjH,EACd,CACF,EAEA3B,CAAAA,EAAAA,EAAAA,SAAAA,CAASA,CAAC,KAERwG,IACAd,IACAY,GACF,EAAG,EAAE,EAEL,IAAMuC,EAAmB,CACvB,CACExF,KAAMG,EAAE,mBACRF,SAAU,WACVC,SAAU,EACZ,EACA,CACEF,KAAMG,EAAE,gBACRF,SAAU,QACVC,SAAU,EACZ,EACD,CAEK,CAACuF,GAAYC,GAAc,CAAGhK,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,EAAC,GACvC,CAACiK,GAAYC,GAAc,CAAGlK,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,EAAC,GACvC,CAACmK,GAAcC,GAAgB,CAAGpK,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,EAAC,GAC3C,CAACqK,GAAaC,GAAe,CAAGtK,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,EAAC,GAEzCuK,GAAsB,IAExB,UAACC,IAAIA,CACH/G,KAAK,sBACLgH,GAAI,cAFDD,EAEsC,OAArB3F,EAAc,GAAM,QAAP,GAEjC,WAAC6F,EAAAA,CAAMA,CAAAA,CAACC,QAAQ,YAAYC,KAAK,eAC/B,UAAChH,EAAAA,CAAeA,CAAAA,CAACC,KAAMgH,EAAAA,GAAKA,GAAI,OAAOpG,EAAE,oBAM3CqG,GAA8B,IAEhC,UAACN,IAAIA,CACH/G,KAAM,CAAEsH,SAAU,sBAAuBxI,MAAO,CAAE5B,GAAId,EAAMqG,MAAM,CAAC,EAAE,CAAG,EACxEuE,GAAI,qBAAqC,OAAhB5K,EAAMqG,MAAM,CAAC,EAAE,WAExC,UAACvC,OAAAA,UAAK,UAACC,EAAAA,CAAeA,CAAAA,CAACC,KAAMmH,EAAAA,GAASA,CAAEJ,KAAK,KAAK7G,MAAM,UAAUR,UAAU,kBAK5E0H,GAAsB,IAExB,WAACC,EAAAA,CAASA,CAACC,IAAI,EAACC,SAAS,cACvB,WAACF,EAAAA,CAASA,CAACG,MAAM,EAAC3H,QAAS,IAAM4G,GAAe,CAACD,cAC/C,UAAC/G,MAAAA,CAAIC,UAAU,qBAAakB,EAAE,wBAC9B,UAACnB,MAAAA,CAAIC,UAAU,qBACZ8G,GAAc,UAACzG,EAAAA,CAAeA,CAAAA,CAACC,KAAMyH,EAAAA,GAAMA,CAAEvH,MAAM,SAClD,UAACH,EAAAA,CAAeA,CAAAA,CAACC,KAAM0H,EAAAA,GAAOA,CAAExH,MAAM,cAG5C,UAACmH,EAAAA,CAASA,CAACM,IAAI,WACb,UAACC,EAAAA,CAAUA,CAAAA,CACTC,KAAK,SACL/K,GAAId,GAASA,EAAMqG,MAAM,CAAGrG,EAAMqG,MAAM,CAAC,EAAE,CAAG,YAOxByF,CAAAA,EAAAA,EAAAA,uBAAAA,CAAuBA,CAAC,IAAM,UAACV,GAAAA,CAAAA,IAC/D,IAAMW,GAAgBC,CAAAA,EAAAA,EAAAA,aAAAA,CAAaA,CAAC,IAAM,UAACtB,GAAAA,CAAAA,IACrCuB,GAAqBD,CAAAA,EAAAA,EAAAA,aAAAA,CAAaA,CAAC,IAAM,UAACf,GAAAA,CAAAA,IAE1CiB,GAAW,CACfC,kBAAmB,CACjB3H,QAASA,EACTgB,YAAaA,CACf,EACA4G,sBAAuB,CACrBnC,iBAAkBA,EAClB3E,cAAeA,CACjB,EACAa,cAAeA,EACfkG,WAlMkB5L,CAkMN4L,GAjMZ9F,EAAaC,IAAI,CAAG,CAClB,CAAC/F,EAAK6L,cAAc,CAAC,CAAG7L,EAAK8L,aAC/B,EACA7E,GACF,EA8LE8E,wBAAyB,CACvBzG,WAAYA,EACZE,cAAeA,EACfwG,WA/Le,CA+LHA,GA9Ld9F,EAAgBH,IAAI,CAAG,CACrB,CAAC/F,EAAK6L,cAAc,CAAC,CAAG7L,EAAK8L,aAAa,EAE5CzF,GACF,EA2LIlB,OAAQA,CACV,EACAE,eAAgBA,EAChBJ,SAAUA,CACZ,EAEA,MACE,WAACgH,EAAAA,CAASA,CAAAA,CAACC,KAAK,IAACjJ,UAAU,0BACzB,UAACkJ,EAAAA,CAAWA,CAAAA,CAACvG,OAAQrG,EAAMqG,MAAM,GACjC,WAACwG,EAAAA,CAAGA,CAAAA,CAACC,MAAO,CAAEC,UAAW,OAAQC,aAAc,MAAO,YACpD,UAACC,EAAAA,CAAGA,CAAAA,CAACvJ,UAAU,kBAAkBwJ,GAAI,WAClCC,SAwCFA,CAA8B,CAAEnN,CAAU,CAAE+L,CAAkB,CAAEnH,CAAM,CAAEiB,CAAe,EAC9F,MAAO,WAACpC,MAAAA,CAAIC,UAAU,uBACpB,WAACD,MAAAA,CAAIC,UAAU,wBACb,WAAC0J,KAAAA,WACEpI,EAAc,KAAQ,CAAC,KAAV,MACbhF,EAAMqG,MAAM,EAAIrG,EAAMqG,MAAM,CAAC,EAAE,CAAI,UAAC0F,EAAAA,CAAcjK,OAAQkD,IAAqB,QAElF,UAACqI,UAAAA,CAAQ3J,UAAU,0CACjB,UAAC4J,EAAAA,CAAQA,CAAAA,CAACpL,SAAUlC,EAAMqG,MAAM,CAAC,EAAE,CAAElE,WAAW,gBAIpD,UAACsB,MAAAA,CAAIC,UAAU,sBACb,UAAC6J,EAAAA,CAAiBA,CAAAA,CAACrI,YAAaF,EAAcE,WAAW,KAE3D,WAACzB,MAAAA,CAAIC,UAAU,uBACb,WAAC8J,IAAAA,WAAE,UAACC,IAAAA,UAAG7I,EAAE,oBAAsB,KAAGI,EAAc,IAAO,EAAIA,EAAc,GAA1B,CAAiC,CAAC6D,MAAT,EAAiB,CAAG,GAAkC,OAA/B7D,EAAc,IAAO,CAAC6D,MAAT,EAAiB,EAAK,GAAG,OACrI,WAAC2E,IAAAA,WAAE,UAACC,IAAAA,UAAG7I,EAAE,4BAA8B,KAAGP,CAAU,CAACW,EAAc,UAAa,CAAd,IAClE,WAACwI,IAAAA,WAAE,UAACC,IAAAA,UAAG7I,EAAE,oBAAsB,KAAG8I,IAAO1I,EAAc,UAAa,CAAd,CAAgB2I,MAAM,CAAC9H,GAAS+H,MAAM,CAAC,UAArDF,aACxC,WAACF,IAAAA,WAAE,UAACC,IAAAA,UAAG7I,EAAE,yBAA2B,KAAG8I,IAAO1I,EAAc,UAAa,CAAd,CAAgB2I,MAAM,CAAC9H,GAAS+H,MAAM,CAAC,UAArDF,kBAGnD,EA9DuB1I,EAAehF,EAAO+L,GAAenH,EAAGiB,KAEvD,UAACoH,EAAAA,CAAGA,CAAAA,CAACvJ,UAAU,UAAUwJ,GAAI,WAC3B,WAACzJ,MAAAA,CAAIC,UAAU,sCACb,UAACD,MAAAA,CAAIC,UAAU,uBACb,UAAC0J,KAAAA,UAAIxI,EAAE,gBAET,UAACxE,EAAAA,OAAoBA,CAAAA,CAACyL,KAAK,SAAS/K,GAAId,GAASA,EAAMqG,MAAM,CAAGrG,EAAMqG,MAAM,CAAC,EAAE,CAAG,eAIxF,UAACwG,EAAAA,CAAGA,CAAAA,UACF,WAACI,EAAAA,CAAGA,CAAAA,CAACvJ,UAAU,sBACb,WAACD,MAAAA,CAAIC,UAAU,2CACb,UAAC0J,KAAAA,UAAIxI,EAAE,2CACN5E,EAAMqG,MAAM,EAAIrG,EAAMqG,MAAM,CAAC,EAAE,CAAI,UAAC4F,GAAAA,CAAoBnK,OAAQkD,IAAqB,QAExF,UAAC6I,EAAAA,OAAuBA,CAAAA,CAAE,GAAG3B,EAAQ,QAIzC,UAACW,EAAAA,CAAGA,CAAAA,UACF,WAACI,EAAAA,CAAGA,CAAAA,CAACvJ,UAAU,sBACb,UAAC0J,KAAAA,UAAIxI,EAAE,kCACNU,GAAiBA,EAAczC,MAAM,CAAG,EACvC,UAACiL,EAAAA,OAA2BA,CAAAA,CAAE,GAAG5B,EAAQ,GACvC,UAACzI,MAAAA,CAAIC,UAAU,uBAAekB,EAAE,iCAIxC,UAACiI,EAAAA,CAAGA,CAAAA,UACF,UAACI,EAAAA,CAAGA,CAAAA,CAACvJ,UAAU,4BAA4BqK,GAAI,YAC7C,UAACC,EAAAA,OAAsBA,CAAAA,CAAE,GAAG9B,EAAQ,SAK9C,yMC3SA,IAAM+B,EAAYC,CAAAA,EAAAA,EAAAA,EAAAA,CAAeA,CAACR,KA0BlC,SAASrM,EAAYrB,CAAuB,EAC1C,GAAM,GAAE4E,CAAC,MAAEC,CAAI,CAAE,CAAGC,CAAAA,EAAAA,EAAAA,EAAAA,CAAcA,CAAC,EA3BG4I,QA4BhCtI,EAAcP,EAAKQ,QAAQ,CAC3B,YAAE/D,CAAU,OAAEwL,CAAK,cAAEvL,CAAY,OAAE4M,CAAK,CAAE,CAAGnO,EAC/CoO,EAAyB,CAAC,EAc9B,OAbIpO,EAAMwB,eAAe,EAAE,CACzB4M,EAAoB,CAClBC,aAAcC,EACdC,MAAO,CACLC,WAAY,GACT,UAACC,EAAAA,CAAWnN,WAAYA,EAAa,GAAGoN,CAAW,EACxD,EACF,EAEEnN,IACF6M,EAAkBO,OAAO,CADT,CACYC,EAI5B,UAACC,EAAAA,EAAQA,CAAAA,CACPC,QAAS1J,EACT6I,UAAWA,EACXhO,OAAQqB,EACR6M,MAAOA,EACPY,cAAc,aACdC,YAAY,WACZlC,MAAOA,EACPmC,WAAYb,EACZc,SAAU,CAACC,MAAMvK,EAAE,SAASwK,SAASxK,EAAE,QAAQyK,KAAKzK,EAAE,QAAQ2J,MAAM3J,EAAE,SAAS0K,KAAK1K,EAAE,QAAQ2K,IAAI3K,EAAE,MAAM,EAC1G4K,cAAe,IACb,IAAMC,EAAaC,OAAOC,IAAI,CAAC/N,GAC5ByG,MAAM,CAAEc,GAASA,EAAKV,QAAQ,CAAC,WAC/BmH,QAAQ,GACLC,EAAYJ,EAAWK,KAAK,CAAC,IAAI,CAAC,EAAE,CAC1CC,IAAAA,IAAW,CACT,IAAsBnO,MAAAA,CAAlBiO,EAAU,UAAoCjO,MAAAA,CAA5BA,CAAK,CAAC6N,EAAW,CAAC,YAAoB,OAAV7N,EAAMlB,GAAG,EAE/D,GAGN,CAOA,SAASkO,EAAe5O,CAAmB,EACzC,MACE,UAAC0M,EAAAA,CAASA,CAAAA,CAAChJ,UAAU,gBACnB,WAACmJ,EAAAA,CAAGA,CAAAA,WACF,UAACI,EAAAA,CAAGA,CAAAA,CAACvJ,UAAU,MAAMwJ,GAAI,WACvB,UAAChM,IAAAA,CACC4L,MAAO,CAAEkD,OAAQ,SAAU,EAC3BnM,QAAS,IAAM7D,EAAMiQ,UAAU,CAAC,QAChCvM,UAAY,0BAGhB,UAACuJ,EAAAA,CAAGA,CAAAA,CAACvJ,UAAU,cAAcwJ,GAAI,YAC/B,UAACpJ,OAAAA,CAAKJ,UAAU,6BAAqB1D,EAAMkQ,KAAK,KAElD,UAACjD,EAAAA,CAAGA,CAAAA,CAACvJ,UAAU,eAAewJ,GAAI,WAChC,UAAChM,IAAAA,CACC4L,MAAO,CAAEkD,OAAQ,SAAU,EAC3BnM,QAAS,IAAM7D,EAAMiQ,UAAU,CAAC,QAChCvM,UAAY,+BAMxB,CA7BArC,EAAY8O,YAAY,CAAG,CACzB5O,cAAc,EACd4M,MAAO,CAAC,QACV,EA6BA,IAAMiC,EAAiB,CAAC9O,EAA6B+O,KACnD,IAAIC,EAAc,EAoBlB,OAnBAtP,IAAAA,OAAS,CAACM,EAAY,IACpB,IAAMiP,EAAY7C,IAAO3K,EAAE0G,UAAU,EAAE+G,GAAG,CAAC,CACzCC,KAAM,EACNC,OAAQ,EACRC,MAHsBjD,CAGd,EACRkD,YAAa,CACf,GACMC,EAAUnD,IAAO3K,EAAE4G,QAAQ,EAAE6G,GAAG,CAAC,CACrCC,KAAM,EACNC,OAAQ,EACRC,OAAQ,CAHYjD,CAIpBkD,YAAa,CACf,EAGIE,CADYpD,IAAO2C,GAAMU,CAChB,QADyB,CAACR,EAAWM,EAAS,KAAM,QAE/DP,IAAe,CAEnB,GACOA,CACT,EAEM7B,EAAa,OAAC,MAAE4B,CAAI,OAAEH,CAAK,CAAE5O,YAAU,CAA8D,GACnGgP,EAAcF,EAAe9O,EAAY+O,GACzCW,EAAmBtD,IAAO2C,GAAMY,QAAQ,CAAC,IAAIC,KAAQ,OAE3D,MACE,OAH6BxD,EAG7B,EAACjK,MAAAA,CACCC,UAAU,gBACVG,QAAS,IAAMkM,IAAAA,IAAW,CAAC,8BAE3B,UAACpM,IAAAA,CAAEC,KAAK,aAAKsM,IACZI,EAAc,GACb,WAACxM,OAAAA,CAAKJ,UAAU,qEACd,UAACK,EAAAA,CAAeA,CAAAA,CACdC,KAAMmN,EAAAA,EAAMA,CACZjN,MAAO8M,EAAmB,OAAS,UACnCjG,KAAK,OAEP,UAACjH,OAAAA,CAAKJ,UAAU,sBAAc4M,SAKxC,EAEMhC,EAAe,GACZ,UAAC7K,MAAAA,CAAI2N,SAAUpR,EAAMoR,QAAQ,GAGtC,EAAe/P,WAAWA,EAAC,kFC7H3B,MA/BoC,GAY5B,yBAmBOyM,MAlBH,UAACuD,EAAAA,CAAQA,CAAAA,CACPC,MAiB4BxD,EAAC,EAjBnB,EACVtJ,QAAS0H,EAASE,qBAAqB,CAACnC,gBAAgB,CACxDxJ,KAAMyL,EAASE,qBAAqB,CAAC9G,aAAa,CAClDiM,OAAO,EACPC,gBAAgB,IAChBC,UAAW,GACXC,oBAAqB,EACrBC,SAAS,IACTC,eAAe,OACfC,YAAY,EACZC,gBAAgB,IAChBC,aAxB0B,CAACC,EAAWC,EAAYC,IAOjDlR,IAAAA,OAAS,CAACgR,EANG,GAClB,CAKqBG,CALbF,EAAM,CACLG,CAAG,CAACH,EAAM,CAACI,WAAW,GAExBD,CAAG,CAACH,EAAM,CAEiBC,oGC0B5C,MAhCgC,GAa5B,yBAmBWrE,MAlBT,UAACwD,EAAAA,CAAQA,CAAAA,CACPC,EAiB8BzD,EAAC,MAjBrB,EACVrJ,QAAS0H,EAASC,iBAAiB,CAAC3H,OAAO,CAC3C/D,KAAMyL,EAASC,iBAAiB,CAAC3G,WAAW,CAC5C+L,MAAO,GACPC,gBAAgB,IAChBC,UAAW,GACXC,oBAAqB,EACrBC,SAAS,IACTC,eAAe,OACfC,YAAY,EACZC,gBAAgB,IAChBC,aAxBqC,CAACC,EAAWC,EAAYC,IAO1DlR,IAAAA,OAAS,CAACgR,EANG,GAClB,CAKqBG,CALbF,EAAM,CACLG,CAAG,CAACH,EAAM,CAACI,WAAW,GAExBD,CAAG,CAACH,EAAM,CAEiBC", "sources": ["webpack://_N_E/./pages/vspace/VirtualspaceCalendarEvents.tsx", "webpack://_N_E/./components/common/Bookmark.tsx", "webpack://_N_E/./pages/vspace/View.tsx", "webpack://_N_E/./components/common/RKICalendar.tsx", "webpack://_N_E/./pages/vspace/VirtualspaceSubscribeRequestUsers.tsx", "webpack://_N_E/./pages/vspace/VirtualspaceMonitoringMembers.tsx"], "sourcesContent": ["//Import Library\r\nimport {useEffect, useState} from \"react\";\r\nimport _ from \"lodash\";\r\n\r\n//Import services/components\r\nimport RKICalendar from \"../../components/common/RKICalendar\";\r\nimport apiService from \"../../services/apiService\";\r\n\r\nfunction VspaceCalendarEvents(props: any) {\r\n  const [events, setEvents] = useState<any[]>([]);\r\n  \r\n  const fetchUpdateType = async () => {\r\n    const response = await apiService.get('/updateType', {\"query\": {\"title\": \"Calendar Event\"}});\r\n    if (response && response.data) {\r\n      fetchCalendarEvents(response.data[0]._id);\r\n    }\r\n  };\r\n  \r\n  const fetchCalendarEvents = async (updateTypeId: any) => {\r\n    const calendarEventsParams = {\r\n      \"query\": {\"type\": \"vspace\", \"update_type\": updateTypeId},\r\n      \"sort\": { \"created_at\": \"desc\" },\r\n      \"limit\": 20,\r\n    }\r\n    const response = await apiService.get('/updates', calendarEventsParams);\r\n    if (response && response.data) {\r\n        const key = \"parent_vspace\";\r\n       if(props.id){\r\n        const _data = _.filter(response.data, { [key]: props.id });\r\n        _.forEach(_data, function(val, i) {\r\n          _data[i].allDay = false;\r\n        });\r\n        setEvents(_data);  \r\n       }\r\n    }\r\n  };\r\n  \r\n  useEffect(() => {\r\n      if(props.id){\r\n        fetchUpdateType();\r\n      }\r\n  },[props.id])\r\n  \r\n  return (\r\n    <RKICalendar eventsList={events} minicalendar showEventCounts />\r\n  )\r\n}\r\n\r\nexport default VspaceCalendarEvents;", "//Import Library\r\nimport { connect } from \"react-redux\";\r\nimport {faBookmark, faCheckCircle, faPlusCircle} from \"@fortawesome/free-solid-svg-icons\";\r\nimport {useEffect, useState} from \"react\";\r\nimport {FontAwesomeIcon} from \"@fortawesome/react-fontawesome\";\r\n\r\n//Import services/components\r\nimport apiService from \"../../services/apiService\";\r\n\r\nconst onModelOptions = {\r\n  operation: \"Operation\",\r\n  institution: \"Institution\",\r\n  event: \"Event\",\r\n  project: \"Project\",\r\n  vspace: \"Vspace\"\r\n} as const;\r\n\r\ninterface BookMarkProps {\r\n  user?: {\r\n    _id: string;\r\n  };\r\n  entityId: string;\r\n  entityType: keyof typeof onModelOptions;\r\n}\r\n\r\nconst BookMark: React.FC<BookMarkProps> = (props) => {\r\n  const { user, entityId, entityType } = props;\r\n  const [bookmark, setBookmark] = useState<boolean>(false);\r\n  const [subscribe, setSubscribe] = useState<any>(\"\");\r\n\r\n  //Handle bookmark feature\r\n  const fetchIfContentSubscribed = async () => {\r\n    if (!user?._id) return;\r\n    const checkFlag = await apiService.get('/flag', {query: {entity_id: entityId, user: user._id, onModel: onModelOptions[entityType]}});\r\n    if (checkFlag && checkFlag.data && checkFlag.data.length > 0) {\r\n      setSubscribe(checkFlag.data[0]);\r\n      setBookmark(true);\r\n    }\r\n  };\r\n\r\n  const bookmarkHandler = async (e: React.MouseEvent<HTMLAnchorElement>) => {\r\n    e.preventDefault();\r\n    if (!user?._id) return;\r\n    const flag  = !bookmark;\r\n    const flagPayload = {\r\n      entity_type: entityType,\r\n      entity_id: entityId,\r\n      user: user._id,\r\n      onModel: onModelOptions[entityType]\r\n    }\r\n    if (flag) {\r\n      const flagIt: any = await apiService.post('/flag', flagPayload);\r\n      if (flagIt && flagIt._id) {\r\n        setSubscribe(flagIt);\r\n        setBookmark(flag);\r\n      }\r\n    } else {\r\n      const unFlagIt = await apiService.remove(`/flag/${subscribe._id}`);\r\n      if (unFlagIt && unFlagIt.n) {\r\n        setBookmark(flag);\r\n      }\r\n    }\r\n  }\r\n  //END Bookmark handler\r\n  useEffect(() => {\r\n    fetchIfContentSubscribed();\r\n  },[]);\r\n  return (\r\n    <div className=\"subscribe-flag\">\r\n      <a href=\"\" onClick={bookmarkHandler}>\r\n        <span className=\"check\">\r\n          {bookmark ?\r\n            <FontAwesomeIcon className=\"clickable checkIcon\" icon={faCheckCircle} color=\"#00CC00\" />\r\n            :\r\n            <FontAwesomeIcon className=\"clickable minusIcon\" icon={faPlusCircle} color=\"#fff\"/>\r\n          }\r\n        </span>\r\n        <FontAwesomeIcon className=\"bookmark\" icon={faBookmark} color=\"#d4d4d4\"/>\r\n      </a>\r\n    </div>\r\n  )\r\n}\r\n\r\nexport default connect((state: any) => state)(BookMark);\r\n\r\n", "//Import Library\r\nimport React, { useEffect, useState } from \"react\";\r\nimport { Container, <PERSON>, Col, Card, Button, Accordion } from 'react-bootstrap';\r\nimport { useRouter } from 'next/router';\r\nimport moment from 'moment';\r\nimport 'moment/locale/de';\r\nimport { FontAwesomeIcon } from \"@fortawesome/react-fontawesome\";\r\nimport { faPlus, faMinus, faPen, faUserCog } from \"@fortawesome/free-solid-svg-icons\";\r\nimport Link from \"next/link\";\r\nimport _ from \"lodash\";\r\n\r\n//Import services/components\r\nimport apiService from \"../../services/apiService\";\r\nimport Discussion from \"../../components/common/disussion\";\r\nimport ReadMoreContainer from \"../../components/common/readMore/readMore\";\r\nimport { useTranslation } from 'next-i18next';\r\nimport UpdatePopup from \"../../components/updates/UpdatePopup\";\r\nimport VspaceCalendarEvents from './VirtualspaceCalendarEvents';\r\nimport Bookmark from \"../../components/common/Bookmark\";\r\nimport { canEditVspace, canViewDiscussionUpdate} from \"./permission\";\r\nimport VspaceMonitoringMembers from \"./VirtualspaceMonitoringMembers\";\r\nimport VspaceSubscribeRequestUsers from \"./VirtualspaceSubscribeRequestUsers\";\r\nimport VspaceAccordianSection from \"./VirtualSpaceAccordionSection\";\r\nconst visibility: { true: string, false: string,  [key: string]: any} = {\r\n  true: 'Public - Accessible to all site users',\r\n  false: 'Private - Accessible only to group members'\r\n};\r\nconst columns = [\r\n  {\r\n    name: 'User Name',\r\n    selector: \"username\",\r\n    sortable: true\r\n  },\r\n  {\r\n    name: 'Email',\r\n    selector: \"email\",\r\n    sortable: true\r\n  }\r\n];\r\n\r\n\r\nconst ViewVSpace = (props: any) => {\r\n  const { t ,i18n } = useTranslation('common');\r\n  const [vspaceDetails, setVspace] = useState<any>({\r\n    description: \"\",\r\n    owner: \"\"\r\n  });\r\n  const currentLang = i18n.language === 'fr' ? 'en' : i18n.language;\r\n  const [vspaceRequest, setVspaceRequest] = useState([]);\r\n  const [subscribers, setSubscribers] = useState([]);\r\n  const [document, setDocument] = useState<any[]>([]);\r\n  const [docSrc] = useState([]);\r\n  const [ _moment] =  useState(currentLang);\r\n  const [calenderEvents, setEvents]: any = useState([]);\r\n  const [vSpaceDocs, setVspaceDocs]: any = useState([]);\r\n  const [updateLoading, setUpdateLoading] = useState(false);\r\n  const [vSpaceLoading, setVspaceLoading] = useState(false);\r\n  const router = useRouter();\r\n  const routes: any = router.query.routes || [];\r\n\r\n  const vSpaceSort = (data: any) => {\r\n    vSpaceParams.sort = {\r\n      [data.columnSelector] : data.sortDirection\r\n    }\r\n    fetchVspaceFiles();\r\n  }\r\n\r\n  const updateSort = (data: any) => {\r\n    updateDocParams.sort = {\r\n      [data.columnSelector] : data.sortDirection\r\n    }\r\n    fetchUpdateDoc();\r\n  }\r\n\r\n  const vSpaceParams: any = {\r\n    sort: { doc_created_at: \"dsc\" },\r\n    Doctable: true\r\n  };\r\n\r\n  const updateDocParams: any = {\r\n    sort: { doc_created_at: \"dsc\" },\r\n    collation: \"en\",\r\n    updateDoctable: true\r\n  };\r\n\r\n\r\n  const fetchUpdateDoc = async() => {\r\n    const _documents: any[] = [];\r\n    setUpdateLoading(true);\r\n    const _updateDoc = await apiService.get(`/vspace/${routes[1]}`, updateDocParams);\r\n      if(_updateDoc && _updateDoc.data && Array.isArray(_updateDoc.data) && _updateDoc.data.length >= 1){\r\n      _updateDoc.data.forEach((element: any, index: any) => {\r\n        element.document && element.document.length > 0 && element.document.map((ele: any, i: any) => {\r\n          const description = element.document[i].docsrc;\r\n          ele.description = description;\r\n          _documents.push(ele);\r\n        });\r\n      });\r\n    setDocument(_documents);\r\n    }\r\n    setUpdateLoading(false);\r\n  }\r\n\r\n  const  fetchVspaceFiles = async() => {\r\n    const _documents: any[] = [];\r\n    setVspaceLoading(true);\r\n    const vSpaceFiles = await apiService.get(`/vspace/${routes[1]}`, vSpaceParams);\r\n    if(vSpaceFiles && vSpaceFiles.data && Array.isArray(vSpaceFiles.data) && vSpaceFiles.data.length >=1) {\r\n    vSpaceFiles.data.forEach((element: any, index: any) => {\r\n      element.document && element.document.length > 0 && element.document.map((ele: any, i: any) => {\r\n          const description = element.document[i].docsrc;\r\n          ele.description = description;\r\n          _documents.push(ele);\r\n        });\r\n      });\r\n      setVspaceDocs(_documents);\r\n    }\r\n    setVspaceLoading(false);\r\n  }\r\n\r\n  const fetchVspace = async () => {\r\n    try {\r\n      const dbvspaceDetails = await apiService.get(`/vspace/${routes[1]}`);\r\n      const dbrequestVspace = await apiService.get(`/vspace-request-subscribers/getRequestedToMe`, { query: { vspace: routes[1] }, select: \"-vspace -requested_to -created_at -updated_at\" });\r\n      if (dbrequestVspace && dbrequestVspace.data && dbrequestVspace.data.length > 0) {\r\n        const requestedUsers = dbrequestVspace.data.map((d: any) => {\r\n          d.requested_by._id = d._id;\r\n          return d.requested_by;\r\n        });\r\n        setVspaceRequest(requestedUsers);\r\n      }\r\n      let notAddedNonMembers = dbvspaceDetails?.nonMembers.filter((x: any) => !dbvspaceDetails?.members.map((x: any) => x.email).includes(x));\r\n      if (notAddedNonMembers.length > 0) {\r\n        const userList = await apiService.get(`/users`, {\r\n          query: {vspace_status: \"Approved\", is_vspace: true},\r\n          sort: { username: \"asc\" },\r\n          limit: \"~\",\r\n        });\r\n        let notAddedNonMembersID = notAddedNonMembers.map((x: any) => userList.data.filter((y: any) => y.email == x).length > 0 ? userList.data.filter((y: any) => y.email == x)[0]._id : \"\");\r\n        notAddedNonMembersID = notAddedNonMembersID.filter((x: any) => x != \"\");\r\n        if (notAddedNonMembersID.length > 0) {\r\n          const choosenMembers = notAddedNonMembersID;\r\n          const oldMembers = dbvspaceDetails && dbvspaceDetails.members.map((item: any) => item._id);\r\n          const vspaceRes = await apiService.patch(`/vspace/${dbvspaceDetails._id}`, {\r\n            ...dbvspaceDetails,\r\n            members: [...oldMembers, ...choosenMembers],\r\n            nonMembers: dbvspaceDetails.nonMembers[0] === \"\" ? \"\" : dbvspaceDetails.nonMembers,\r\n          });\r\n          let newPlatformUsers = choosenMembers?.map((x: any) => userList?.data.filter((y: any) => y._id == x).length > 0 ? userList.data.filter((y: any) => y._id == x)[0] : [] );\r\n          newPlatformUsers = newPlatformUsers.filter((x: any) => x != '');\r\n          dbvspaceDetails.members = dbvspaceDetails.members.concat(newPlatformUsers);\r\n        }\r\n      }\r\n      setSubscribers(dbvspaceDetails.members);\r\n      setVspace(dbvspaceDetails);\r\n      setEvents([{\r\n        title: \"VSpace\",\r\n        start: dbvspaceDetails.start_date,\r\n        end: dbvspaceDetails.end_date,\r\n        allDay: true,\r\n        images: dbvspaceDetails.images,\r\n        images_src: dbvspaceDetails.images_src,\r\n        document: dbvspaceDetails.document,\r\n        doc_src: dbvspaceDetails.doc_src\r\n\r\n      }]);\r\n    } catch (e) {\r\n      console.log(e);\r\n    }\r\n  }\r\n\r\n  useEffect(() => {\r\n\r\n    fetchVspace();\r\n    fetchUpdateDoc();\r\n    fetchVspaceFiles();\r\n  }, []);\r\n\r\n  const requestedColumns = [\r\n    {\r\n      name: t('vspace.UserName'),\r\n      selector: \"username\",\r\n      sortable: true\r\n    },\r\n    {\r\n      name: t('vspace.Email'),\r\n      selector: \"email\",\r\n      sortable: true\r\n    }\r\n  ];\r\n\r\n  const [sectionOne, setSectionOne] = useState(true);\r\n  const [sectionTwo, setSectionTwo] = useState(true);\r\n  const [sectionThree, setSectionThree] = useState(true);\r\n  const [sectionFour, setSectionFour] = useState(true);\r\n\r\n  const EditVspaceComponent = () => {\r\n    return (\r\n      <Link\r\n        href=\"/vspace/[...routes]\"\r\n        as={`/vspace/edit/${vspaceDetails['_id']}`}\r\n        >\r\n        <Button variant=\"secondary\" size=\"sm\">\r\n          <FontAwesomeIcon icon={faPen} />&nbsp;{t(\"vspace.Edit\")}\r\n        </Button>\r\n      </Link>\r\n    );\r\n  };\r\n\r\n  const CaniconEditVSpacesComponent = () => {\r\n    return (\r\n      <Link\r\n        href={{ pathname: \"/vspace/[...routes]\", query: { id: props.routes[1] } }}\r\n        as={`/vspace/manage?id=${props.routes[1]}`}\r\n        >\r\n        <span><FontAwesomeIcon icon={faUserCog} size=\"2x\" color=\"#232c3d\" className=\"clickable\" /></span>\r\n      </Link>\r\n    );\r\n  }\r\n\r\n  const DiscussionComponent = () => {\r\n    return (\r\n      <Accordion.Item eventKey=\"3\">\r\n        <Accordion.Header onClick={() => setSectionFour(!sectionFour)}>\r\n          <div className=\"cardTitle\">{t(\"vspace.Discussions\")}</div>\r\n          <div className=\"cardArrow\">\r\n            {sectionFour ? <FontAwesomeIcon icon={faPlus} color=\"#fff\" /> :\r\n              <FontAwesomeIcon icon={faMinus} color=\"#fff\" />}\r\n          </div>\r\n        </Accordion.Header>\r\n        <Accordion.Body>\r\n          <Discussion\r\n            type=\"vspace\"\r\n            id={props && props.routes ? props.routes[1] : null}\r\n          />\r\n        </Accordion.Body>\r\n      </Accordion.Item>\r\n    )\r\n  };\r\n\r\n  const CanViewDiscussionUpdate = canViewDiscussionUpdate(() => <DiscussionComponent />)\r\n  const CanEditVSpace = canEditVspace(() => <EditVspaceComponent />)\r\n  const CanIconEditVSpaces = canEditVspace(() => <CaniconEditVSpacesComponent />)\r\n\r\n  const propData = {\r\n    Monitoringmembers: {\r\n      columns: columns,\r\n      subscribers: subscribers\r\n    },\r\n    SubscribeRequestUsers: {\r\n      requestedColumns: requestedColumns,\r\n      vspaceRequest: vspaceRequest\r\n    },\r\n    vSpaceLoading: vSpaceLoading,\r\n    vSpaceSort: vSpaceSort,\r\n    documentAccoirdianProps: {\r\n      vSpaceDocs: vSpaceDocs,\r\n      updateLoading: updateLoading,\r\n      updateSort: updateSort,\r\n      docSrc: docSrc,\r\n    },\r\n    calenderEvents: calenderEvents,\r\n    document: document\r\n  }\r\n\r\n  return (\r\n    <Container fluid className=\"vspaceDetails\">\r\n      <UpdatePopup routes={props.routes} />\r\n      <Row style={{ marginTop: \"10px\", marginBottom: \"25px\" }}>\r\n        <Col className=\"ps-md-1 pe-md-1\" md={8}>\r\n          {Vspace_func(vspaceDetails, props, CanEditVSpace, t, _moment)}\r\n        </Col>\r\n        <Col className=\"pe-md-1\" md={4}>\r\n          <div className=\"vspaceCard vspaceCalendar\">\r\n            <div className=\"vspaceTitle\">\r\n              <h4>{t('calendar')}</h4>\r\n            </div>\r\n            <VspaceCalendarEvents type='vspace' id={props && props.routes ? props.routes[1] : null} />\r\n          </div>\r\n        </Col>\r\n      </Row>\r\n      <Row>\r\n        <Col className=\"ps-1 pe-1\">\r\n          <div className=\"d-flex justify-content-between\">\r\n            <h4>{t(\"vspace.Monitoringandevaluationmembers\")}</h4>\r\n            {props.routes && props.routes[1] ? (<CanIconEditVSpaces  vspace={vspaceDetails} />) : null}\r\n          </div>\r\n          <VspaceMonitoringMembers {...propData} />\r\n        </Col>\r\n      </Row >\r\n\r\n      <Row>\r\n        <Col className=\"ps-1 pe-1\">\r\n          <h4>{t(\"vspace.SubscribeRequestUsers\")}</h4>\r\n          {vspaceRequest && vspaceRequest.length > 0 ?\r\n            <VspaceSubscribeRequestUsers {...propData} />\r\n            : <div className=\"nodataFound\">{t(\"vspace.Nodataavailable\")}</div>}\r\n        </Col>\r\n      </Row>\r\n\r\n      <Row>\r\n        <Col className=\"vspaceAccordion ps-1 pe-1\" xs={12}>\r\n          <VspaceAccordianSection {...propData} />\r\n        </Col>\r\n      </Row>\r\n    </Container >\r\n  );\r\n}\r\n\r\nexport default ViewVSpace;\r\nfunction Vspace_func(vspaceDetails: any, props: any, CanEditVSpace: any, t: any, _moment: string) {\r\n  return <div className=\"vspaceCard\">\r\n    <div className=\"vspaceTitle\">\r\n      <h4>\r\n        {vspaceDetails['title']}&nbsp;&nbsp;\r\n        {props.routes && props.routes[1] ? (<CanEditVSpace vspace={vspaceDetails} />) : null}\r\n      </h4>\r\n      <section className=\"d-flex justify-content-between\">\r\n        <Bookmark entityId={props.routes[1]} entityType=\"vspace\" />\r\n      </section>\r\n    </div>\r\n\r\n    <div className=\"vspaceDesc\">\r\n      <ReadMoreContainer description={vspaceDetails.description} />\r\n    </div>\r\n    <div className=\"vspaceInfo\">\r\n      <p><b>{t(\"vspace.Ownedby\")}</b>: {vspaceDetails[\"user\"] && vspaceDetails[\"user\"].username ? `${vspaceDetails[\"user\"].username}` : \"\"} </p>\r\n      <p><b>{t(\"vspace.Groupvisibility\")}</b>: {visibility[vspaceDetails['visibility']]}</p>\r\n      <p><b>{t(\"vspace.Created\")}</b>: {moment(vspaceDetails['created_at']).locale(_moment).format('ddd, D MMMM YYYY')}</p>\r\n      <p><b>{t(\"vspace.LastModified\")}</b>: {moment(vspaceDetails['updated_at']).locale(_moment).format('ddd, D MMMM YYYY')}</p>\r\n    </div>\r\n  </div>;\r\n}\r\n\r\n", "//Import Library\r\nimport { Calendar, momentLocalizer, View } from \"react-big-calendar\";\r\nimport moment from \"moment\";\r\nimport 'moment/locale/fr';\r\nimport Router from \"next/router\";\r\nimport { Col, Container, Row } from \"react-bootstrap\";\r\nimport { FontAwesomeIcon } from \"@fortawesome/react-fontawesome\";\r\nimport { faStar } from \"@fortawesome/free-solid-svg-icons\";\r\nconst localizer = momentLocalizer(moment);\r\nimport _ from \"lodash\";\r\n\r\n//Import services/components\r\nimport { useTranslation } from 'next-i18next';\r\n\r\ninterface CalendarEvent {\r\n  _id: string;\r\n  start_date: string | Date;\r\n  end_date: string | Date;\r\n  [key: string]: any;\r\n}\r\n\r\ninterface ToolbarProps {\r\n  label: string;\r\n  onNavigate: (action: string) => void;\r\n}\r\n\r\ninterface RKICalendarProps {\r\n  eventsList: CalendarEvent[];\r\n  style?: React.CSSProperties;\r\n  minicalendar?: boolean;\r\n  views?: View[];\r\n  showEventCounts?: boolean;\r\n}\r\n\r\nfunction RKICalendar(props: RKICalendarProps) {\r\n  const { t, i18n } = useTranslation('common');\r\n  const currentLang = i18n.language\r\n  const { eventsList, style, minicalendar, views } = props;\r\n  let calendarComponent: any = {};\r\n  if (props.showEventCounts) {\r\n    calendarComponent = {\r\n      eventWrapper: EventWrapper,\r\n      month: {\r\n        dateHeader: (headerProps: any) =>\r\n           <DateHeader eventsList={eventsList} {...headerProps} />\r\n      },\r\n    };\r\n  }\r\n  if (minicalendar) {\r\n    calendarComponent.toolbar = MinimalToolbar;\r\n  }\r\n\r\n  return (\r\n    <Calendar\r\n      culture={currentLang}\r\n      localizer={localizer}\r\n      events={eventsList}\r\n      views={views}\r\n      startAccessor=\"start_date\"\r\n      endAccessor=\"end_date\"\r\n      style={style}\r\n      components={calendarComponent}\r\n      messages={{today:t(\"today\"),previous:t(\"back\"),next:t(\"Next\"),month:t(\"Month\"),week:t(\"Week\"),day:t(\"Day\")}}\r\n      onSelectEvent={(event: CalendarEvent) => {\r\n        const findOption = Object.keys(event)\r\n          .filter((item) => item.includes(\"parent\"))\r\n          .toString();\r\n        const urlAppend = findOption.split(\"_\")[1];\r\n        Router.push(\r\n          `/${urlAppend}/show/${event[findOption]}/update/${event._id}`\r\n        );\r\n      }}\r\n    />\r\n  );\r\n}\r\n\r\nRKICalendar.defaultProps = {\r\n  minicalendar: false,\r\n  views: [\"month\"],\r\n};\r\n\r\nfunction MinimalToolbar(props: ToolbarProps) {\r\n  return (\r\n    <Container className=\"mb-1\">\r\n      <Row>\r\n        <Col className=\"p-0\" md={1}>\r\n          <i\r\n            style={{ cursor: \"pointer\" }}\r\n            onClick={() => props.onNavigate(\"PREV\")}\r\n            className={`fas fa-chevron-left`}\r\n          />\r\n        </Col>\r\n        <Col className=\"text-center\" md={10}>\r\n          <span className=\"rbc-toolbar-label\">{props.label}</span>\r\n        </Col>\r\n        <Col className=\"p-0 text-end\" md={1}>\r\n          <i\r\n            style={{ cursor: \"pointer\" }}\r\n            onClick={() => props.onNavigate(\"NEXT\")}\r\n            className={`fas fa-chevron-right`}\r\n          />\r\n        </Col>\r\n      </Row>\r\n    </Container>\r\n  );\r\n}\r\n\r\n// Generates event counts based on the given date\r\nconst getEventsCount = (eventsList: CalendarEvent[], date: Date): number => {\r\n  let eventsCount = 0;\r\n  _.forEach(eventsList, (e) => {\r\n    const startDate = moment(e.start_date).set({\r\n      hour: 0,\r\n      minute: 0,\r\n      second: 0,\r\n      millisecond: 0,\r\n    });\r\n    const endDate = moment(e.end_date).set({\r\n      hour: 0,\r\n      minute: 0,\r\n      second: 0,\r\n      millisecond: 0,\r\n    });\r\n\r\n    const isEvent = moment(date).isBetween(startDate, endDate, null, \"[]\");\r\n    if (isEvent) {\r\n      eventsCount += 1;\r\n    }\r\n  });\r\n  return eventsCount;\r\n};\r\n\r\nconst DateHeader = ({ date, label, eventsList }: { date: Date; label: string; eventsList: CalendarEvent[] }) => {\r\n  const eventsCount = getEventsCount(eventsList, date);\r\n  const isEventCompleted = moment(date).isBefore(new Date(), \"day\");\r\n\r\n  return (\r\n    <div\r\n      className=\"rbc-date-cell\"\r\n      onClick={() => Router.push(\"/events-calendar\")}\r\n    >\r\n      <a href=\"#\">{label}</a>\r\n      {eventsCount > 0 && (\r\n        <span className=\"d-flex justify-content-start align-items-center fa-stack\">\r\n          <FontAwesomeIcon\r\n            icon={faStar}\r\n            color={isEventCompleted ? \"grey\" : \"#04A6FB\"}\r\n            size=\"lg\"\r\n          />\r\n          <span className=\"eventCount\">{eventsCount}</span>\r\n        </span>\r\n      )}\r\n    </div>\r\n  );\r\n};\r\n\r\nconst EventWrapper = (props: { onSelect?: () => void }) => {\r\n  return <div onSelect={props.onSelect} />;\r\n};\r\n\r\nexport default RKICalendar;\r\n", "//Import services/components\r\nimport RKITable from \"../../components/common/RKITable\";\r\nimport _ from \"lodash\";\r\n\r\nconst VspaceSubscribeRequestUsers = (propData: any) => {\r\n    const sortSubscribeRequestTousers = (rows: any, field: any, direction: any) => {\r\n        const handleField = (row: any) => {\r\n          if (row[field]) {\r\n            return row[field].toLowerCase();\r\n          }\r\n          return row[field];\r\n        };\r\n        return _.orderBy(rows, handleField, direction);\r\n    };\r\n\r\n    return(\r\n        <>\r\n            <RKITable\r\n              noHeader={true}\r\n              columns={propData.SubscribeRequestUsers.requestedColumns}\r\n              data={propData.SubscribeRequestUsers.vspaceRequest}\r\n              dense={true}\r\n              paginationServer\r\n              pagServer={true}\r\n              paginationTotalRows={0}\r\n              subHeader\r\n              subHeaderAlign=\"left\"\r\n              pagination={true}\r\n              persistTableHead\r\n              sortFunction={sortSubscribeRequestTousers}\r\n            />\r\n        </>\r\n    )\r\n};\r\n\r\nexport default VspaceSubscribeRequestUsers;", "//Import Library\r\nimport _ from \"lodash\";\r\n\r\n//Import services/components\r\nimport RKITable from \"../../components/common/RKITable\";\r\n\r\nconst VspaceMonitoringMembers = (propData: any) => {\r\n \r\n  const sortMonitoringAndEvaluationMembers = (rows: any, field: any, direction: any) => {\r\n    const handleField = (row: any) => {\r\n      if (row[field]) {\r\n        return row[field].toLowerCase();\r\n      }\r\n      return row[field];\r\n    };\r\n    return _.orderBy(rows, handleField, direction);\r\n  };\r\n\r\n  return (\r\n    <>\r\n      <RKITable\r\n        noHeader={true}\r\n        columns={propData.Monitoringmembers.columns}\r\n        data={propData.Monitoringmembers.subscribers}\r\n        dense={true}\r\n        paginationServer\r\n        pagServer={true}\r\n        paginationTotalRows={0}\r\n        subHeader\r\n        subHeaderAlign=\"left\"\r\n        pagination={true}\r\n        persistTableHead\r\n        sortFunction={sortMonitoringAndEvaluationMembers}\r\n        />\r\n    </>\r\n  )\r\n}\r\n\r\nexport default VspaceMonitoringMembers;"], "names": ["props", "events", "setEvents", "useState", "VspaceCalendarEvents", "fetchUpdateType", "response", "apiService", "get", "data", "_id", "fetchCalendarEvents", "updateTypeId", "calendarEventsParams", "id", "_data", "_", "val", "i", "allDay", "useEffect", "RKICalendar", "eventsList", "minicalendar", "showEventCounts", "onModelOptions", "operation", "institution", "event", "project", "vspace", "connect", "state", "user", "entityId", "entityType", "bookmark", "setBookmark", "subscribe", "setSubscribe", "fetchIfContentSubscribed", "checkFlag", "query", "entity_id", "onModel", "length", "bookmarkHandler", "e", "preventDefault", "flag", "flagPayload", "entity_type", "flagIt", "post", "unFlagIt", "remove", "n", "div", "className", "a", "href", "onClick", "span", "FontAwesomeIcon", "icon", "faCheckCircle", "color", "faPlusCircle", "faBookmark", "visibility", "true", "false", "columns", "name", "selector", "sortable", "t", "i18n", "useTranslation", "ViewVSpace", "vspaceDetails", "setVspace", "description", "owner", "currentLang", "language", "vspaceRequest", "setVspaceRequest", "subscribers", "setSubscribers", "document", "setDocument", "docSrc", "_moment", "calenderEvents", "vSpaceDocs", "setVspaceDocs", "updateLoading", "setUpdateLoading", "vSpaceLoading", "setVspaceLoading", "routes", "useRouter", "vSpaceParams", "sort", "doc_created_at", "Doctable", "updateDocParams", "collation", "updateDoctable", "fetchUpdateDoc", "_documents", "_updateDoc", "Array", "isArray", "for<PERSON>ach", "element", "index", "map", "ele", "docsrc", "push", "fetchVspaceFiles", "vSpaceFiles", "fetchVspace", "dbvspaceDetails", "dbrequestVspace", "select", "requestedUsers", "d", "requested_by", "notAddedNonMembers", "nonMembers", "filter", "members", "x", "email", "includes", "userList", "vspace_status", "is_vspace", "username", "limit", "notAddedNonMembersID", "y", "choosenMembers", "old<PERSON><PERSON><PERSON>", "item", "patch", "newPlatformUsers", "concat", "title", "start", "start_date", "end", "end_date", "images", "images_src", "doc_src", "console", "log", "requestedColumns", "sectionOne", "setSectionOne", "sectionTwo", "setSectionTwo", "sectionThree", "setSectionThree", "sectionFour", "setSectionFour", "EditVspaceComponent", "Link", "as", "<PERSON><PERSON>", "variant", "size", "faPen", "CaniconEditVSpacesComponent", "pathname", "faUserCog", "DiscussionComponent", "Accordion", "<PERSON><PERSON>", "eventKey", "Header", "faPlus", "faMinus", "Body", "Discussion", "type", "canViewDiscussionUpdate", "CanEditVSpace", "canEditVspace", "CanIconEditVSpaces", "propData", "Monitoringmembers", "SubscribeRequestUsers", "vSpaceSort", "columnSelector", "sortDirection", "documentAccoirdianProps", "updateSort", "Container", "fluid", "UpdatePopup", "Row", "style", "marginTop", "marginBottom", "Col", "md", "Vspace_func", "h4", "section", "Bookmark", "ReadMoreContainer", "p", "b", "moment", "locale", "format", "VspaceMonitoringMembers", "VspaceSubscribeRequestUsers", "xs", "VspaceAccordianSection", "localizer", "momentLocalizer", "views", "calendarComponent", "eventWrapper", "EventWrapper", "month", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "headerProps", "toolbar", "MinimalToolbar", "Calendar", "culture", "startAccessor", "endAccessor", "components", "messages", "today", "previous", "next", "week", "day", "onSelectEvent", "findOption", "Object", "keys", "toString", "urlAppend", "split", "Router", "cursor", "onNavigate", "label", "defaultProps", "getEventsCount", "date", "eventsCount", "startDate", "set", "hour", "minute", "second", "millisecond", "endDate", "isEvent", "isBetween", "isEventCompleted", "isBefore", "Date", "faStar", "onSelect", "RKITable", "<PERSON><PERSON><PERSON><PERSON>", "dense", "paginationServer", "pagServer", "paginationTotalRows", "subHeader", "subHeaderAlign", "pagination", "persistTableHead", "sortFunction", "rows", "field", "direction", "handleField", "row", "toLowerCase"], "sourceRoot": "", "ignoreList": []}