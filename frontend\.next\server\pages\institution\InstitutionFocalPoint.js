"use strict";(()=>{var e={};e.id=918,e.ids=[636,918,3220],e.modules={123:e=>{e.exports=require("dom-helpers/removeEventListener")},1332:e=>{e.exports=require("react-custom-scrollbars-2")},1428:e=>{e.exports=import("axios")},1680:e=>{e.exports=require("@restart/ui/utils")},1919:e=>{e.exports=require("react-transition-group/Transition")},3892:e=>{e.exports=require("classnames")},4048:e=>{e.exports=require("react-truncate")},6009:e=>{e.exports=require("dom-helpers/ownerDocument")},6952:e=>{e.exports=require("@restart/ui/Modal")},7374:e=>{e.exports=require("@restart/ui/Dropdown")},8732:e=>{e.exports=require("react/jsx-runtime")},9653:e=>{e.exports=require("@restart/ui/Overlay")},11e3:e=>{e.exports=require("react-multi-select-component")},11242:e=>{e.exports=require("redux-persist/integration/react")},11688:e=>{e.exports=require("@restart/hooks/useWillUnmount")},12388:e=>{e.exports=require("dom-helpers/addEventListener")},13364:e=>{e.exports=require("redux-saga/effects")},14062:e=>{e.exports=import("react-redux")},14078:e=>{e.exports=import("swr")},14332:e=>{e.exports=require("uncontrollable")},16116:e=>{e.exports=require("invariant")},18622:e=>{e.exports=require("yup")},19121:e=>{e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},22313:e=>{e.exports=require("react-confirm-alert")},22326:e=>{e.exports=require("react-dom")},22541:e=>{e.exports=require("redux-persist/lib/storage")},25303:e=>{e.exports=require("@restart/ui/NavItem")},26324:e=>{e.exports=require("warning")},27825:e=>{e.exports=require("lodash")},27910:e=>{e.exports=require("stream")},28217:e=>{e.exports=require("@restart/ui/DropdownItem")},28966:(e,t,r)=>{r.r(t),r.d(t,{canAddInstitution:()=>a,canAddInstitutionForm:()=>u,canEditInstitution:()=>p,canEditInstitutionForm:()=>l,canManageFocalPoints:()=>c,canViewDiscussionUpdate:()=>d,default:()=>x});var i=r(8732);r(82015);var s=r(81366),n=r.n(s),o=r(61421);let a=n()({authenticatedSelector:e=>!!e.permissions&&!!e.permissions.institution&&!!e.permissions.institution["create:any"],wrapperDisplayName:"CanAddInstitution"}),u=n()({authenticatedSelector:e=>!!e.permissions&&!!e.permissions.institution&&!!e.permissions.institution["create:any"],wrapperDisplayName:"CanAddInstitutionForm",FailureComponent:()=>(0,i.jsx)(o.default,{})}),p=n()({authenticatedSelector:(e,t)=>{if(e.permissions&&e.permissions.institution){if(e.permissions.institution["update:any"])return!0;else if(e.permissions.institution["update:own"]&&t.institution&&t.institution.user&&t.institution.user===e.user._id)return!0}return!1},wrapperDisplayName:"CanEditInstitution"}),l=n()({authenticatedSelector:(e,t)=>{if(e.permissions&&e.permissions.institution){if(e.permissions.institution["update:any"])return!0;else if(e.permissions.institution["update:own"]&&t.institution&&t.institution.user&&t.institution.user===e.user._id)return!0}return!1},wrapperDisplayName:"CanEditInstitutionForm",FailureComponent:()=>(0,i.jsx)(o.default,{})}),d=n()({authenticatedSelector:e=>!!e.permissions&&!!e.permissions.update&&!!e.permissions.update["read:any"],wrapperDisplayName:"CanViewDiscussionUpdate"}),c=n()({authenticatedSelector:(e,t)=>{if(e.permissions&&e.permissions.institution_focal_point){if(e.permissions.institution_focal_point["update:any"])return!0;else if(e.permissions.institution_focal_point["update:own"]&&t.institution&&t.institution.user&&t.institution.user===e.user._id)return!0}return!1},wrapperDisplayName:"canManageFocalPoints"}),x=a},29021:e=>{e.exports=require("fs")},29294:e=>{e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},29628:e=>{e.exports=require("dom-helpers/addClass")},29825:e=>{e.exports=require("prop-types")},29841:e=>{e.exports=require("dom-helpers/hasClass")},30362:e=>{e.exports=require("@restart/ui/DropdownToggle")},33873:e=>{e.exports=require("path")},36653:e=>{e.exports=require("nprogress")},36955:e=>{e.exports=require("@restart/hooks/useBreakpoint")},37766:e=>{e.exports=require("@restart/hooks/useMergedRefs")},38609:e=>{e.exports=require("react-data-table-component")},39756:e=>{e.exports=import("redux")},40051:e=>{e.exports=require("dom-helpers/removeClass")},40361:e=>{e.exports=require("next/dist/compiled/next-server/pages.runtime.prod.js")},42893:e=>{e.exports=import("react-hot-toast")},43294:e=>{e.exports=require("formik")},50009:e=>{e.exports=require("@restart/ui/DropdownMenu")},55476:(e,t,r)=>{r.a(e,async(e,i)=>{try{r.d(t,{A:()=>a});var s=r(63487),n=e([s]);s=(n.then?(await n)():n)[0];class o{arrayDifference(e,t){let r=[];return e.forEach(e=>{0===t.filter(t=>t._id===e.value).length&&r.push(e.value)}),r}async deleteInvitations(e,t){e.forEach(async e=>{let r=await s.A.get(`/users/${e}`);r.institutionInvites.length&&(r.institutionInvites=r.institutionInvites.filter(e=>e.institutionId!==t),await s.A.patch(`/users/${e}`,r))})}isDuplicateInvite(e,t){return!!e.filter(e=>e.institutionId===t).length}getNewInviteMeta(e,t){return{institutionName:t,institutionId:e,status:"Request Pending"}}async getUserData(e,t,r){let i={};return t&&r?await s.A.get("/users",{query:{email:t,username:r}}):await s.A.get(`/users/${e}`)}async sendInvitations(e,t,r){e.forEach(async e=>{let i=e._id;if(i){let e=await this.getUserData(i);e&&(e.institutionInvites||(e.institutionInvites=[]),e.institutionInvites=e.institutionInvites.map(e=>(e.institutionId===t&&"Rejected"===e.status&&(e.status="Request Pending"),e)),this.isDuplicateInvite(e.institutionInvites||[],t)||e.institutionInvites.push(this.getNewInviteMeta(t,r)),await s.A.patch(`/users/${i}`,e))}else this.inviteNewUserWithEmail(e.email,e.username,t,r)})}async inviteNewUserWithEmail(e,t,r,i){let n=await s.A.get("/users",{query:{email:e,username:t}});n&&n.data[0]&&((n=n.data[0]).institutionInvites.push(this.getNewInviteMeta(r,i)),await s.A.patch(`/users/${n._id}`,n))}}let a=new o;i()}catch(e){i(e)}})},57664:e=>{e.exports=require("dom-helpers/scrollbarSize")},58928:e=>{e.exports=require("dom-helpers/canUseDOM")},59717:e=>{e.exports=require("@restart/ui/DropdownContext")},60560:e=>{e.exports=require("dom-helpers/contains")},63033:e=>{e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},65447:e=>{e.exports=require("@restart/ui/Button")},67364:e=>{e.exports=require("@restart/hooks/useCallbackRef")},68455:e=>{e.exports=import("redux-saga")},69722:e=>{e.exports=require("es6-promise")},74075:e=>{e.exports=require("zlib")},74716:e=>{e.exports=require("moment")},74987:e=>{e.exports=require("@restart/ui/ModalManager")},77025:(e,t,r)=>{r.a(e,async(e,i)=>{try{r.r(t),r.d(t,{config:()=>h,default:()=>c,getServerSideProps:()=>q,getStaticPaths:()=>m,getStaticProps:()=>x,reportWebVitals:()=>g,routeModule:()=>y,unstable_getServerProps:()=>w,unstable_getServerSideProps:()=>I,unstable_getStaticParams:()=>P,unstable_getStaticPaths:()=>v,unstable_getStaticProps:()=>f});var s=r(63885),n=r(80237),o=r(81413),a=r(9616),u=r.n(a),p=r(72386),l=r(10836),d=e([p,l]);[p,l]=d.then?(await d)():d;let c=(0,o.M)(l,"default"),x=(0,o.M)(l,"getStaticProps"),m=(0,o.M)(l,"getStaticPaths"),q=(0,o.M)(l,"getServerSideProps"),h=(0,o.M)(l,"config"),g=(0,o.M)(l,"reportWebVitals"),f=(0,o.M)(l,"unstable_getStaticProps"),v=(0,o.M)(l,"unstable_getStaticPaths"),P=(0,o.M)(l,"unstable_getStaticParams"),w=(0,o.M)(l,"unstable_getServerProps"),I=(0,o.M)(l,"unstable_getServerSideProps"),y=new s.PagesRouteModule({definition:{kind:n.A.PAGES,page:"/institution/InstitutionFocalPoint",pathname:"/institution/InstitutionFocalPoint",bundlePath:"",filename:""},components:{App:p.default,Document:u()},userland:l});i()}catch(e){i(e)}})},78097:e=>{e.exports=require("next-redux-saga")},78634:e=>{e.exports=require("@restart/ui/Anchor")},80237:(e,t)=>{Object.defineProperty(t,"A",{enumerable:!0,get:function(){return r}});var r=function(e){return e.PAGES="PAGES",e.PAGES_API="PAGES_API",e.APP_PAGE="APP_PAGE",e.APP_ROUTE="APP_ROUTE",e.IMAGE="IMAGE",e}({})},81366:e=>{e.exports=require("redux-auth-wrapper/connectedAuthWrapper")},81413:(e,t)=>{Object.defineProperty(t,"M",{enumerable:!0,get:function(){return function e(t,r){return r in t?t[r]:"then"in t&&"function"==typeof t.then?t.then(t=>e(t,r)):"function"==typeof t&&"default"===r?t:void 0}}})},81521:e=>{e.exports=require("dom-helpers/querySelectorAll")},81895:e=>{e.exports=require("@restart/hooks/useEventCallback")},82015:e=>{e.exports=require("react")},86842:e=>{e.exports=require("@restart/ui/SelectableContext")},87571:e=>{e.exports=require("dom-helpers/transitionEnd")},88751:e=>{e.exports=require("next-i18next")},93787:e=>{e.exports=require("redux-persist")},94947:e=>{e.exports=require("@restart/hooks/useTimeout")},96196:e=>{e.exports=require("next-redux-wrapper")},98320:e=>{e.exports=require("@restart/hooks/useIsomorphicEffect")},99460:e=>{e.exports=require("dom-helpers/css")}};var t=require("../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),i=t.X(0,[6089,9216,9616,2386,836],()=>r(77025));module.exports=i})();