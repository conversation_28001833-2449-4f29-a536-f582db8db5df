(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[950],{2492:(e,t,s)=>{(window.__NEXT_P=window.__NEXT_P||[]).push(["/project/components/ProjectCoverSection",function(){return s(14145)}])},14145:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>h});var r=s(37876);s(14232);var a=s(60282),i=s(56970),c=s(48230),n=s.n(c),o=s(21772),l=s(11041),d=s(10841),p=s.n(d),j=s(31753),u=s(46864),m=s(31647);let h=e=>{let{t}=(0,j.Bd)("common"),s=()=>(0,r.jsx)(r.Fragment,{children:e.editAccess?(0,r.jsx)(n(),{href:"/project/[...routes]",as:"/project/edit/".concat(e.routeData.routes[1]),children:(0,r.jsxs)(a.A,{variant:"secondary",size:"sm",children:[(0,r.jsx)(o.g,{icon:l.hpd}),"\xa0",t("Edit")]})}):""}),c=(0,u.canEditProject)(()=>(0,r.jsx)(s,{}));return(0,r.jsx)(r.Fragment,{children:(0,r.jsx)(i.A,{className:"projectRow",children:(0,r.jsxs)("div",{className:"projectBanner",children:[(0,r.jsx)("div",{className:"projectImg",children:(0,r.jsx)("img",{src:"/images/project-banner.jpg",alt:"Project Detail"})}),function(e,t,s,a,i){return(0,r.jsxs)("div",{className:"projectTitleBlock",children:[(0,r.jsxs)("h4",{className:"projectTitle",children:[e.title,"\xa0\xa0",t.routes&&t.routes[1]?(0,r.jsx)(s,{project:e}):null]}),(0,r.jsxs)("div",{className:"projectDate",children:[(0,r.jsxs)("div",{className:"projectStart",children:[(0,r.jsx)("i",{className:"fas fa-calendar-alt"}),(0,r.jsxs)("div",{children:[(0,r.jsxs)("h6",{style:{color:"white"},children:[a("StartDate"),":"]}),(0,r.jsx)("h5",{children:e.start_date?p()(e.start_date).format(i):null})]})]}),(0,r.jsxs)("div",{className:"projectStatus me-2",children:[(0,r.jsx)("i",{className:"fas fa-hourglass-half"}),(0,r.jsxs)("div",{children:[(0,r.jsxs)("h6",{style:{color:"white"},children:[a("Status"),":"]}),(0,r.jsx)("h5",{children:e.status&&e.status.title})]})]}),(0,r.jsx)(m.A,{entityId:t.routes[1],entityType:"project"})]})]})}(e.projectData,e.routeData,c,t,"DD-MM-YYYY")]})})})}},31647:(e,t,s)=>{"use strict";s.d(t,{A:()=>d});var r=s(37876),a=s(31777),i=s(11041),c=s(14232),n=s(21772),o=s(53718);let l={operation:"Operation",institution:"Institution",event:"Event",project:"Project",vspace:"Vspace"},d=(0,a.Ng)(e=>e)(e=>{let{user:t,entityId:s,entityType:a}=e,[d,p]=(0,c.useState)(!1),[j,u]=(0,c.useState)(""),m=async()=>{if(!(null==t?void 0:t._id))return;let e=await o.A.get("/flag",{query:{entity_id:s,user:t._id,onModel:l[a]}});e&&e.data&&e.data.length>0&&(u(e.data[0]),p(!0))},h=async e=>{if(e.preventDefault(),!(null==t?void 0:t._id))return;let r=!d,i={entity_type:a,entity_id:s,user:t._id,onModel:l[a]};if(r){let e=await o.A.post("/flag",i);e&&e._id&&(u(e),p(r))}else{let e=await o.A.remove("/flag/".concat(j._id));e&&e.n&&p(r)}};return(0,c.useEffect)(()=>{m()},[]),(0,r.jsx)("div",{className:"subscribe-flag",children:(0,r.jsxs)("a",{href:"",onClick:h,children:[(0,r.jsx)("span",{className:"check",children:d?(0,r.jsx)(n.g,{className:"clickable checkIcon",icon:i.SGM,color:"#00CC00"}):(0,r.jsx)(n.g,{className:"clickable minusIcon",icon:i.OQW,color:"#fff"})}),(0,r.jsx)(n.g,{className:"bookmark",icon:i.G06,color:"#d4d4d4"})]})})})},46864:(e,t,s)=>{"use strict";s.r(t),s.d(t,{canAddProject:()=>c,canAddProjectForm:()=>n,canEditProject:()=>o,canEditProjectForm:()=>l,canViewDiscussionUpdate:()=>d,default:()=>p});var r=s(37876);s(14232);var a=s(8178),i=s(59626);let c=(0,a.A)({authenticatedSelector:e=>!!e.permissions&&!!e.permissions.project&&!!e.permissions.project["create:any"],wrapperDisplayName:"CanAddProject"}),n=(0,a.A)({authenticatedSelector:e=>!!e.permissions&&!!e.permissions.project&&!!e.permissions.project["create:any"],wrapperDisplayName:"CanAddProjectForm",FailureComponent:()=>(0,r.jsx)(i.default,{})}),o=(0,a.A)({authenticatedSelector:(e,t)=>{if(e.permissions&&e.permissions.project){if(e.permissions.project["update:any"])return!0;else if(e.permissions.project["update:own"]&&t.project&&t.project.user&&t.project.user._id===e.user._id)return!0}return!1},wrapperDisplayName:"CanEditProject"}),l=(0,a.A)({authenticatedSelector:(e,t)=>{if(e.permissions&&e.permissions.project){if(e.permissions.project["update:any"])return!0;else if(e.permissions.project["update:own"]&&t.project&&t.project.user&&t.project.user._id===e.user._id)return!0}return!1},wrapperDisplayName:"CanEditProjectForm",FailureComponent:()=>(0,r.jsx)(i.default,{})}),d=(0,a.A)({authenticatedSelector:e=>!!e.permissions&&!!e.permissions.update&&!!e.permissions.update["read:any"],wrapperDisplayName:"CanViewDiscussionUpdate"}),p=c}},e=>{var t=t=>e(e.s=t);e.O(0,[7725,1772,636,6593,8792],()=>t(2492)),_N_E=e.O()}]);
//# sourceMappingURL=ProjectCoverSection-a101314ece07c1f8.js.map