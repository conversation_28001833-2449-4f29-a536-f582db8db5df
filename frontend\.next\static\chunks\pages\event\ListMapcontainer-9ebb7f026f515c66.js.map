{"version": 3, "file": "static/chunks/pages/event/ListMapcontainer-9ebb7f026f515c66.js", "mappings": "qJAoEA,MA/CkD,OAAC,CACjDA,OAAO,QAAQ,IACfC,CA6CaC,CA7CR,EAAE,SA6CkBA,EA5CzBC,EAAY,EAAE,MACdC,CAAI,MACJC,CAAI,UACJC,CAAQ,CACRC,SAAO,CACPC,OAAK,CACLC,aAAY,CAAK,CAClB,UAsBMH,GAAoC,UAAxB,OAAOA,EAASI,GAAG,EAAyC,UAAxB,OAAOJ,EAASK,GAAG,CAKtE,UAACC,EAAAA,EAAMA,CAAAA,CACLN,SAAUA,EACVD,KAAMA,EACNG,MAAOA,GAASR,EAChBS,UAAWA,EACXF,QA/BiBM,CA+BRC,GA9BPP,GAeFA,EAdoB,IADT,EAETP,KACAC,QAYmBc,IAXnBZ,OACAC,WACAE,CACF,EAGe,CACbA,WACAU,YAAa,IAAMV,CACrB,EAE6BO,EAEjC,IAIS,IAYX,iKC2CA,MAnGyB,IACvB,GAAM,MAAEI,CAAI,CAAE,CAAGC,CAAAA,EAAAA,EAAAA,EAAAA,CAAcA,CAAC,GAkGnBC,OAjGPC,EAAcH,EAAKI,KAiGIF,EAAC,CAjGG,CAC3B,QAAEG,CAAM,CAAE,CAAGC,EACb,CAACC,EAAeC,EAAiB,CAAGC,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAyB,CAAC,GACtE,CAACC,EAAQC,EAAU,CAAGF,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAQ,EAAE,EACxC,CAACG,EAAcC,EAAgB,CAAGJ,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAM,CAAC,GACjD,CAACK,EAAYC,EAAc,CAAGN,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAM,CAAC,GAsB7CO,EAAc,KAClBH,EAAgB,MAChBE,EAAc,KAChB,EAEME,EAAgB,CAACC,EAAgBpB,EAAaqB,KAClDH,IACAH,EAAgBf,GAChBiB,EAAc,CACZhC,KAAMmC,EAAUnC,IAAI,CACpBC,GAAIkC,EAAUlC,EAAE,CAChBE,UAAWgC,EAAUhC,SAAS,EAElC,EAEMkC,EAAsB,KAC1B,IAAMC,EAA2B,EAAE,CACnCC,IAAAA,OAAS,CAACjB,EAAQ,IAChBgB,EAAkBE,IAAI,CAAC,CACrBhC,MAAOiC,EAAMjC,KAAK,CAClBP,GAAIwC,EAAMC,GAAG,CACbhC,IACE+B,EAAME,OAAO,EACbF,EAAME,OAAO,CAACC,WAAW,EACzBH,EAAME,OAAO,CAACC,WAAW,CAAC,EAAE,CAACC,QAAQ,CACvClC,IACE8B,EAAME,OAAO,EACbF,EAAME,OAAO,CAACC,WAAW,EACzBH,EAAME,OAAO,CAACC,WAAW,CAAC,EAAE,CAACE,SAAS,CACxC3C,UAAWsC,EAAME,OAAO,EAAIF,EAAME,OAAO,CAACD,GAAG,EAEjD,GACAd,EAAU,IAAIU,EAAkB,CAClC,EAOA,MALAS,CAAAA,EAAAA,EAAAA,SAAAA,CAASA,CAAC,KACRV,IACAZ,EAAiBc,IAAAA,OAAS,CAACjB,EAAQ,eACrC,EAAG,CAACA,EAAO,EAGT,UAAC0B,EAAAA,CAAOA,CAAAA,CACNC,QAAShB,EACTZ,SAAUD,EACVS,aAAcA,EACdE,WAAY,UAACmB,IAhEf,GAAM,MAAEC,CAAI,CAAE,CAAGC,SACjB,GAAYD,EAAKhD,SAAS,EAAIqB,CAAa,CAAC2B,EAAKhD,SAAS,CAAC,CAEvD,UAACkD,KAAAA,UACE7B,CAAa,CAAC2B,EAAKhD,SAAS,CAAC,CAACmD,GAAG,CAAC,CAACC,EAAMC,IAEtC,UAACC,KAAAA,UACC,UAACC,IAAIA,CAACC,KAAK,qBAAqBC,GAAI,IAA8BL,MAAAA,CAA1BnC,EAAY,EAA/CsC,cAAsE,OAATH,EAAKb,GAAG,WACvEa,EAAK/C,KAAK,IAFNgD,MAUZ,IACT,EA+CiBN,CAAWC,KAAMpB,aAE7BJ,EAAOkC,MAAM,EAAI,EACdlC,EAAO2B,GAAG,CAAC,CAACC,EAAMC,KAChB,GAAID,EAAK7C,GAAG,CACV,CADY,KAEV,UAACR,EAAAA,CAAYA,CAAAA,CAEXF,KAAMuD,EAAK/C,KAAK,CAChBP,GAAIsD,EAAKtD,EAAE,CACXE,UAAWoD,EAAKpD,SAAS,CACzBE,KAAM,CACJyD,IAAK,8BACP,EACAvD,QAAS2B,EACT5B,SAAUiD,GARLC,EAYb,GACA,MAGV,wFC3FA,MARyB,OAAC,UAAElD,CAAQ,OAQrByD,OARuBC,CAAY,QAQnBD,EARqBE,CAAQ,CAAS,GACnE,MACE,UAACC,EAAAA,EAAUA,CAAAA,CAAC5D,SAAUA,EAAU0D,aAAcA,WAC5C,UAACG,MAAAA,UAAKF,KAGZ,ECdMG,EAAO,mBACPC,EAAQ,qBACRC,EAAO,eACPC,EAAW,kBAgTjB,EA/SkB,CAChB,CACE,YAAe,IA6SJC,OA5SX,EA4SoBA,EAAC,IA5SV,CACT,CACE,MAAS,SACX,EACD,EAEH,CACE,YAAeJ,EACf,QAAW,CACT,CACE,MAAS,SACX,EACD,EAEH,CACE,YAAeC,EACf,QAAW,CACT,CACE,MAAS,SACX,EAEJ,EACA,CACE,YAAe,iBACf,YAAe,WACf,QAAW,CACT,CACE,WAAc,KAChB,EACD,EAEH,CACE,YAAe,yBACf,YAAeE,EACf,QAAW,CACT,CACE,MAAS,SACX,EACD,EAEH,CACE,YAAe,6BACf,YAAe,SACf,QAAW,CACT,CACE,WAAc,KAChB,EACD,EAEH,CACE,YAAe,6BACf,YAAeH,EACf,QAAW,CACT,CACE,MAAS,SACX,EACD,EAEH,CACE,YAAe,0BACf,YAAeG,EACf,QAAW,CACT,CACE,MAAS,SACX,EACD,EAEH,CACE,YAAe,qBACf,YAAeA,EACf,QAAW,CACT,CACE,MAAS,SACX,EACD,EAEH,CACE,YAAe,oBACf,YAAe,WACf,QAAW,CACT,CACE,MAAS,SACX,EACD,EAEH,CACE,YAAe,MACf,QAAW,CACT,CACE,WAAc,KAChB,EAEJ,EACA,CACE,YAAe,MACf,YAAe,WACf,QAAW,CACT,CACE,MAAS,SACX,EACD,EAEH,CACE,YAAe,MACf,YAAe,cACf,QAAW,CACT,CACE,WAAc,KAChB,EACD,EAEH,CACE,YAAe,MACf,YAAeH,EACf,QAAW,CACT,CACE,MAAS,SACX,EACD,EAEH,CACE,YAAe,MACf,YAAeC,EACf,QAAW,CACT,CACE,MAAS,SACX,EACD,EAEH,CACE,YAAe,WACf,YAAe,gBACf,QAAW,CACT,CACE,MAAS,SACX,EACD,EAEH,CACE,YAAe,WACf,YAAeD,EACf,QAAW,CACT,CACE,MAAS,SACX,EACD,EAEH,CACE,YAAe,OACf,QAAW,CACT,CACE,WAAc,KAChB,EACD,EAEH,CACE,YAAe,OACf,YAAe,WACf,QAAW,CACT,CACE,MAAS,SACX,EACD,EAEH,CACE,YAAe,OACf,YAAe,cACf,QAAW,CACT,CACE,WAAc,KAChB,EACD,EAEH,CACE,YAAe,OACf,YAAeA,EACf,QAAW,CACT,CACE,MAAS,SACX,EACD,EAEH,CACE,YAAe,OACf,YAAeC,EACf,QAAW,CACT,CACE,MAAS,SACX,EAEJ,EACA,CACE,YAAeC,EACf,YAAe,WACf,QAAW,CACT,CACE,MAAS,SACX,EACD,EAEH,CACE,YAAeA,EACf,YAAeC,EACf,QAAW,CACT,CACE,MAAS,SACX,EACD,EAEH,CACE,YAAeD,EACf,YAAeF,EACf,QAAW,CACT,CACE,MAAS,SACX,EACD,EAEH,CACE,YAAeE,EACf,YAAeD,EACf,QAAW,CACT,CACE,MAAS,SACX,EACD,EAEH,CACE,YAAe,aACf,YAAe,SACf,QAAW,CACT,CACE,WAAc,KAChB,EACD,EAEH,CACE,YAAe,UACf,QAAW,CACT,CACE,WAAc,KAChB,EACD,EAEH,CACE,YAAe,UACf,YAAeD,EACf,QAAW,CACT,CACE,MAAS,SACX,EACD,EAEH,CACE,YAAe,UACf,YAAeC,EACf,QAAW,CACT,CACE,MAAS,SACX,EACD,EAEH,CACE,YAAe,eACf,YAAe,gBACf,QAAW,CACT,CACE,MAAS,SACX,EACD,EAEH,CACE,YAAe,kBACf,YAAe,WACf,QAAW,CACT,CACE,MAAS,SACX,EAEJ,EACA,CACE,YAAe,QACf,YAAe,WACf,QAAW,CACT,CACE,MAAS,SACX,EACD,EAEH,CACE,YAAe,QACf,YAAeD,EACf,QAAW,CACT,CACE,MAAS,SACX,EACD,EAEJ,2BCnND,MAzEwC,OAAC,SAyE1BK,GAxEb1C,CAAU,GAwEU0C,EAAC,SAvErB5C,CAAY,eACZ6C,CAAa,UACbT,CAAQ,QACRU,EAAS,GAAG,OACZC,EAAQ,MAAM,UACdvD,CAAQ,MACRwD,EAAO,CAAC,SACRC,EAAU,CAAC,CACX7B,SAAO,CACR,GACO,QAAE8B,CAAM,CAAE,CAAGC,CAAAA,EAAAA,EAAAA,SAAAA,CAASA,GACtB,UAAEC,CAAQ,WAAEC,CAAS,CAAE,CAAIC,CAAAA,EAAAA,EAAAA,CAAAA,CAAaA,UAmB9C,EAAsB,SAAP,CAAQhB,MAAAA,UAAI,uBACtBc,EAGH,QAHa,EAGZd,MAAAA,CAAIiB,UAAU,yBACb,UAACjB,MAAAA,CAAIiB,UAAU,WAAWC,MAAO,OAAET,SAAOD,EAAQrE,SAAU,UAAW,WACrE,WAACgF,EAAAA,EAASA,CAAAA,CACRC,kBAzBe,CACrBX,MAAOA,EACPD,OAA0B,UAAlB,OAAOA,EAAsB,GAAU,OAAPA,EAAO,MAAMA,CACvD,EAuBQa,OAhBOd,CAgBCc,EArBM,CACpB9E,IAAK,SAIyB+E,CAH9B9E,IAAK,SACP,EAmBQkE,KAAMA,EACNa,OAhBU,CAgBFC,GAfdrC,EAAIsC,UAAU,CAAC,CACbC,OAAQrB,CACV,EACF,EAaQsB,QAAS,CACPhB,EAhBWN,MAgBFM,EACTrE,WAAW,EACXsF,mBAAmB,EACnBC,mBAAmB,EACnBC,YAAY,EACZC,gBAAgB,EAChBC,gBAAgB,EAChBC,mBAAmB,CACrB,YAECnC,EACAlC,GAAcF,GAAgBA,EAAab,WAAW,EACrD,UAAC+C,EAAgBA,CACfzD,SAAUuB,EAAab,SADR+C,EACmB,GAClCC,aAAc,KAEZqC,QAAQC,GAAG,CAAC,qBACZrD,GAAAA,GACF,WAEClB,GAHCkB,QA5BQ,UAACkB,MAAAA,UAAI,mBAsC7B,mBC3FA,4CACA,0BACA,WACA,OAAe,EAAQ,KAA+C,CACtE,EACA,SAFsB", "sources": ["webpack://_N_E/./components/common/RKIMapMarker.tsx", "webpack://_N_E/./pages/event/ListMapcontainer.tsx", "webpack://_N_E/./components/common/RKIMapInfowindow.tsx", "webpack://_N_E/./components/common/mapStyles.tsx", "webpack://_N_E/./components/common/RKIMap1.tsx", "webpack://_N_E/?3cba"], "sourcesContent": ["import React from 'react';\r\nimport { Marker } from '@react-google-maps/api';\r\n\r\ninterface RKIMapMarkerProps {\r\n  name?: string;\r\n  id?: string;\r\n  countryId?: string;\r\n  type?: string;\r\n  icon?: {\r\n    url: string;\r\n    scaledSize?: google.maps.Size;\r\n  };\r\n  position: {\r\n    lat: number;\r\n    lng: number;\r\n  };\r\n  onClick?: (props: any, marker: any, e: any) => void;\r\n  title?: string;\r\n  draggable?: boolean;\r\n}\r\n\r\nconst RKIMapMarker: React.FC<RKIMapMarkerProps> = ({\r\n  name = 'Marker',\r\n  id = '',\r\n  countryId = '',\r\n  type,\r\n  icon,\r\n  position,\r\n  onClick,\r\n  title,\r\n  draggable = false,\r\n}) => {\r\n  const handleClick = (e: google.maps.MapMouseEvent) => {\r\n    if (onClick) {\r\n      const markerProps = {\r\n        name,\r\n        id,\r\n        countryId,\r\n        type,\r\n        position,\r\n      };\r\n\r\n      // Create a marker-like object for compatibility with old onClick signature\r\n      const marker = {\r\n        position,\r\n        getPosition: () => position,\r\n      };\r\n\r\n      onClick(markerProps, marker, e);\r\n    }\r\n  };\r\n\r\n  // Ensure position is valid\r\n  if (!position || typeof position.lat !== 'number' || typeof position.lng !== 'number') {\r\n    return null;\r\n  }\r\n\r\n  return (\r\n    <Marker\r\n      position={position}\r\n      icon={icon}\r\n      title={title || name}\r\n      draggable={draggable}\r\n      onClick={handleClick}\r\n    />\r\n  );\r\n};\r\n\r\nexport default RKIMapMarker;\r\n", "//Import Library\r\nimport _ from \"lodash\";\r\nimport Link from \"next/link\";\r\nimport React, { useState, useEffect } from \"react\";\r\n\r\n//Import services/components\r\nimport { useTranslation } from 'next-i18next';\r\nimport RKIMAP1 from \"../../components/common/RKIMap1\";\r\nimport RKIMapMarker from \"../../components/common/RKIMapMarker\";\r\n\r\nconst ListMapContainer = (props: any) => {\r\n  const { i18n } = useTranslation('common');\r\n  const currentLang = i18n.language;\r\n  const { events } = props;\r\n  const [groupedEvents, setGroupedEvents] = useState<{[key: string]: any[]}>({});\r\n  const [points, setPoints] = useState<any[]>([]);\r\n  const [activeMarker, setactiveMarker] = useState<any>({});\r\n  const [markerInfo, setMarkerInfo] = useState<any>({});\r\n\r\n  const MarkerInfo = (Markerprops: any) => {\r\n    const { info } = Markerprops;\r\n    if (info && info.countryId && groupedEvents[info.countryId]) {\r\n      return (\r\n        <ul>\r\n          {groupedEvents[info.countryId].map((item, index) => {\r\n            return (\r\n              <li key={index}>\r\n                <Link href=\"/event/[...routes]\" as={`/${currentLang}/event/show/${item._id}`}>\r\n                  {item.title}\r\n                </Link>\r\n              </li>\r\n            );\r\n          })}\r\n        </ul>\r\n      );\r\n    }\r\n    return null;\r\n  };\r\n\r\n  const resetMarker = () => {\r\n    setactiveMarker(null);\r\n    setMarkerInfo(null);\r\n  };\r\n\r\n  const onMarkerClick = (propsinit: any, marker: any, _e: any) => {\r\n    resetMarker();\r\n    setactiveMarker(marker);\r\n    setMarkerInfo({\r\n      name: propsinit.name,\r\n      id: propsinit.id,\r\n      countryId: propsinit.countryId,\r\n    });\r\n  };\r\n\r\n  const setPointsFromEvents = () => {\r\n    const eventFilterpoints: any[] = [];\r\n    _.forEach(events, (event) => {\r\n      eventFilterpoints.push({\r\n        title: event.title,\r\n        id: event._id,\r\n        lat:\r\n          event.country &&\r\n          event.country.coordinates &&\r\n          event.country.coordinates[0].latitude,\r\n        lng:\r\n          event.country &&\r\n          event.country.coordinates &&\r\n          event.country.coordinates[0].longitude,\r\n        countryId: event.country && event.country._id,\r\n      });\r\n    });\r\n    setPoints([...eventFilterpoints]);\r\n  };\r\n\r\n  useEffect(() => {\r\n    setPointsFromEvents();\r\n    setGroupedEvents(_.groupBy(events, \"country._id\"));\r\n  }, [events]);\r\n\r\n  return (\r\n    <RKIMAP1\r\n      onClose={resetMarker}\r\n      language={currentLang}\r\n      activeMarker={activeMarker}\r\n      markerInfo={<MarkerInfo info={markerInfo} />}\r\n    >\r\n      {points.length >= 1\r\n        ? points.map((item, index) => {\r\n            if (item.lat) {\r\n              return (\r\n                <RKIMapMarker\r\n                  key={index}\r\n                  name={item.title}\r\n                  id={item.id}\r\n                  countryId={item.countryId}\r\n                  icon={{\r\n                    url: \"/images/map-marker-white.svg\",\r\n                  }}\r\n                  onClick={onMarkerClick}\r\n                  position={item}\r\n                />\r\n              );\r\n            }\r\n          })\r\n        : null}\r\n    </RKIMAP1>\r\n  );\r\n};\r\n\r\nexport default ListMapContainer;\r\n", "import { InfoWindow } from '@react-google-maps/api';\r\n\r\ninterface Props {\r\n  position: google.maps.LatLngLiteral;\r\n  onCloseClick?: () => void;\r\n  children?: React.ReactNode;\r\n}\r\n\r\nconst RKIMapInfowindow = ({ position, onCloseClick, children }: Props) => {\r\n  return (\r\n    <InfoWindow position={position} onCloseClick={onCloseClick}>\r\n      <div>{children}</div>\r\n    </InfoWindow>\r\n  );\r\n};\r\n\r\nexport default RKIMapInfowindow;\r\n", "const fill = \"labels.text.fill\"\r\nconst stoke = \"labels.text.stroke\"\r\nconst road = \"road.highway\"\r\nconst geometry = \"geometry.stroke\"\r\nconst mapStyles = [\r\n  {\r\n    \"elementType\": \"geometry\",\r\n    \"stylers\": [\r\n      {\r\n        \"color\": \"#1d2c4d\"\r\n      }\r\n    ]\r\n  },\r\n  {\r\n    \"elementType\": fill,\r\n    \"stylers\": [\r\n      {\r\n        \"color\": \"#8ec3b9\"\r\n      }\r\n    ]\r\n  },\r\n  {\r\n    \"elementType\": stoke,\r\n    \"stylers\": [\r\n      {\r\n        \"color\": \"#1a3646\"\r\n      }\r\n    ]\r\n  },\r\n  {\r\n    \"featureType\": \"administrative\",\r\n    \"elementType\": \"geometry\",\r\n    \"stylers\": [\r\n      {\r\n        \"visibility\": \"off\"\r\n      }\r\n    ]\r\n  },\r\n  {\r\n    \"featureType\": \"administrative.country\",\r\n    \"elementType\": geometry,\r\n    \"stylers\": [\r\n      {\r\n        \"color\": \"#4b6878\"\r\n      }\r\n    ]\r\n  },\r\n  {\r\n    \"featureType\": \"administrative.land_parcel\",\r\n    \"elementType\": \"labels\",\r\n    \"stylers\": [\r\n      {\r\n        \"visibility\": \"off\"\r\n      }\r\n    ]\r\n  },\r\n  {\r\n    \"featureType\": \"administrative.land_parcel\",\r\n    \"elementType\": fill,\r\n    \"stylers\": [\r\n      {\r\n        \"color\": \"#64779e\"\r\n      }\r\n    ]\r\n  },\r\n  {\r\n    \"featureType\": \"administrative.province\",\r\n    \"elementType\": geometry,\r\n    \"stylers\": [\r\n      {\r\n        \"color\": \"#4b6878\"\r\n      }\r\n    ]\r\n  },\r\n  {\r\n    \"featureType\": \"landscape.man_made\",\r\n    \"elementType\": geometry,\r\n    \"stylers\": [\r\n      {\r\n        \"color\": \"#334e87\"\r\n      }\r\n    ]\r\n  },\r\n  {\r\n    \"featureType\": \"landscape.natural\",\r\n    \"elementType\": \"geometry\",\r\n    \"stylers\": [\r\n      {\r\n        \"color\": \"#023e58\"\r\n      }\r\n    ]\r\n  },\r\n  {\r\n    \"featureType\": \"poi\",\r\n    \"stylers\": [\r\n      {\r\n        \"visibility\": \"off\"\r\n      }\r\n    ]\r\n  },\r\n  {\r\n    \"featureType\": \"poi\",\r\n    \"elementType\": \"geometry\",\r\n    \"stylers\": [\r\n      {\r\n        \"color\": \"#283d6a\"\r\n      }\r\n    ]\r\n  },\r\n  {\r\n    \"featureType\": \"poi\",\r\n    \"elementType\": \"labels.text\",\r\n    \"stylers\": [\r\n      {\r\n        \"visibility\": \"off\"\r\n      }\r\n    ]\r\n  },\r\n  {\r\n    \"featureType\": \"poi\",\r\n    \"elementType\": fill,\r\n    \"stylers\": [\r\n      {\r\n        \"color\": \"#6f9ba5\"\r\n      }\r\n    ]\r\n  },\r\n  {\r\n    \"featureType\": \"poi\",\r\n    \"elementType\": stoke,\r\n    \"stylers\": [\r\n      {\r\n        \"color\": \"#1d2c4d\"\r\n      }\r\n    ]\r\n  },\r\n  {\r\n    \"featureType\": \"poi.park\",\r\n    \"elementType\": \"geometry.fill\",\r\n    \"stylers\": [\r\n      {\r\n        \"color\": \"#023e58\"\r\n      }\r\n    ]\r\n  },\r\n  {\r\n    \"featureType\": \"poi.park\",\r\n    \"elementType\": fill,\r\n    \"stylers\": [\r\n      {\r\n        \"color\": \"#3C7680\"\r\n      }\r\n    ]\r\n  },\r\n  {\r\n    \"featureType\": \"road\",\r\n    \"stylers\": [\r\n      {\r\n        \"visibility\": \"off\"\r\n      }\r\n    ]\r\n  },\r\n  {\r\n    \"featureType\": \"road\",\r\n    \"elementType\": \"geometry\",\r\n    \"stylers\": [\r\n      {\r\n        \"color\": \"#304a7d\"\r\n      }\r\n    ]\r\n  },\r\n  {\r\n    \"featureType\": \"road\",\r\n    \"elementType\": \"labels.icon\",\r\n    \"stylers\": [\r\n      {\r\n        \"visibility\": \"off\"\r\n      }\r\n    ]\r\n  },\r\n  {\r\n    \"featureType\": \"road\",\r\n    \"elementType\": fill,\r\n    \"stylers\": [\r\n      {\r\n        \"color\": \"#98a5be\"\r\n      }\r\n    ]\r\n  },\r\n  {\r\n    \"featureType\": \"road\",\r\n    \"elementType\": stoke,\r\n    \"stylers\": [\r\n      {\r\n        \"color\": \"#1d2c4d\"\r\n      }\r\n    ]\r\n  },\r\n  {\r\n    \"featureType\": road,\r\n    \"elementType\": \"geometry\",\r\n    \"stylers\": [\r\n      {\r\n        \"color\": \"#2c6675\"\r\n      }\r\n    ]\r\n  },\r\n  {\r\n    \"featureType\": road,\r\n    \"elementType\": geometry,\r\n    \"stylers\": [\r\n      {\r\n        \"color\": \"#255763\"\r\n      }\r\n    ]\r\n  },\r\n  {\r\n    \"featureType\": road,\r\n    \"elementType\": fill,\r\n    \"stylers\": [\r\n      {\r\n        \"color\": \"#b0d5ce\"\r\n      }\r\n    ]\r\n  },\r\n  {\r\n    \"featureType\": road,\r\n    \"elementType\": stoke,\r\n    \"stylers\": [\r\n      {\r\n        \"color\": \"#023e58\"\r\n      }\r\n    ]\r\n  },\r\n  {\r\n    \"featureType\": \"road.local\",\r\n    \"elementType\": \"labels\",\r\n    \"stylers\": [\r\n      {\r\n        \"visibility\": \"off\"\r\n      }\r\n    ]\r\n  },\r\n  {\r\n    \"featureType\": \"transit\",\r\n    \"stylers\": [\r\n      {\r\n        \"visibility\": \"off\"\r\n      }\r\n    ]\r\n  },\r\n  {\r\n    \"featureType\": \"transit\",\r\n    \"elementType\": fill,\r\n    \"stylers\": [\r\n      {\r\n        \"color\": \"#98a5be\"\r\n      }\r\n    ]\r\n  },\r\n  {\r\n    \"featureType\": \"transit\",\r\n    \"elementType\": stoke,\r\n    \"stylers\": [\r\n      {\r\n        \"color\": \"#1d2c4d\"\r\n      }\r\n    ]\r\n  },\r\n  {\r\n    \"featureType\": \"transit.line\",\r\n    \"elementType\": \"geometry.fill\",\r\n    \"stylers\": [\r\n      {\r\n        \"color\": \"#283d6a\"\r\n      }\r\n    ]\r\n  },\r\n  {\r\n    \"featureType\": \"transit.station\",\r\n    \"elementType\": \"geometry\",\r\n    \"stylers\": [\r\n      {\r\n        \"color\": \"#3a4762\"\r\n      }\r\n    ]\r\n  },\r\n  {\r\n    \"featureType\": \"water\",\r\n    \"elementType\": \"geometry\",\r\n    \"stylers\": [\r\n      {\r\n        \"color\": \"#0e1626\"\r\n      }\r\n    ]\r\n  },\r\n  {\r\n    \"featureType\": \"water\",\r\n    \"elementType\": fill,\r\n    \"stylers\": [\r\n      {\r\n        \"color\": \"#4e6d70\"\r\n      }\r\n    ]\r\n  }\r\n];\r\n\r\nexport default mapStyles;", "import React from 'react';\r\nimport { GoogleMap, useJsApiLoader } from '@react-google-maps/api';\r\nimport RKIMapInfowindow from './RKIMapInfowindow';\r\nimport mapStyles from './mapStyles';\r\nimport { useRouter } from \"next/router\";\r\nimport { useGoogleMaps } from './GoogleMapsProvider';\r\n\r\ninterface RKIMap1Props {\r\n  markerInfo?: React.ReactNode;\r\n  activeMarker?: any;\r\n  initialCenter?: { lat: number; lng: number };\r\n  children?: React.ReactNode;\r\n  height?: number | string;\r\n  width?: string;\r\n  language?: string;\r\n  points?: any[];\r\n  zoom?: number;\r\n  minZoom?: number;\r\n  onClose?: () => void\r\n}\r\n\r\nconst RKIMap1: React.FC<RKIMap1Props> = ({\r\n  markerInfo,\r\n  activeMarker,\r\n  initialCenter,\r\n  children,\r\n  height = 300,\r\n  width = \"114%\",\r\n  language,\r\n  zoom = 1,\r\n  minZoom = 1,\r\n  onClose\r\n}) => {\r\n  const { locale } = useRouter();\r\n  const { isLoaded, loadError } =  useGoogleMaps();\r\n  const containerStyle = {\r\n    width: width,\r\n    height: typeof height === 'number' ? `${height}px` : height,\r\n  };\r\n\r\n  const defaultCenter = {\r\n    lat: 52.520017,\r\n    lng: 13.404195,\r\n  };\r\n\r\n  const center = initialCenter || defaultCenter;\r\n\r\n  const onMapLoad = (map: google.maps.Map) => {\r\n    map.setOptions({\r\n      styles: mapStyles,\r\n    });\r\n  };\r\n\r\n  if (loadError) return <div>Error loading maps</div>;\r\n  if (!isLoaded) return <div>Loading Maps...</div>;\r\n\r\n  return (\r\n    <div className=\"map-container\">\r\n      <div className=\"mapprint\" style={{ width, height, position: 'relative' }}>\r\n        <GoogleMap\r\n          mapContainerStyle={containerStyle}\r\n          center={center}\r\n          zoom={zoom}\r\n          onLoad={onMapLoad}\r\n          options={{\r\n            minZoom: minZoom,\r\n            draggable: true,\r\n            keyboardShortcuts: false,\r\n            streetViewControl: false,\r\n            panControl: false,\r\n            clickableIcons: false,\r\n            mapTypeControl: false,\r\n            fullscreenControl: true,\r\n          }}\r\n        >\r\n          {children}\r\n          {markerInfo && activeMarker && activeMarker.getPosition && (\r\n            <RKIMapInfowindow\r\n              position={activeMarker.getPosition()}\r\n              onCloseClick={() => {\r\n                // Handle close if needed\r\n                console.log('close click');\r\n                onClose?.()\r\n              }}\r\n            >\r\n              {markerInfo}\r\n            </RKIMapInfowindow>\r\n          )}\r\n        </GoogleMap>\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default RKIMap1;\r\n", "\n    (window.__NEXT_P = window.__NEXT_P || []).push([\n      \"/event/ListMapcontainer\",\n      function () {\n        return require(\"private-next-pages/event/ListMapcontainer.tsx\");\n      }\n    ]);\n    if(module.hot) {\n      module.hot.dispose(function () {\n        window.__NEXT_P.push([\"/event/ListMapcontainer\"])\n      });\n    }\n  "], "names": ["name", "id", "R<PERSON>IMapMarker", "countryId", "type", "icon", "position", "onClick", "title", "draggable", "lat", "lng", "<PERSON><PERSON>", "e", "handleClick", "marker", "getPosition", "i18n", "useTranslation", "ListMapContainer", "currentLang", "language", "events", "props", "groupedEvents", "setGroupedEvents", "useState", "points", "setPoints", "activeMarker", "set<PERSON><PERSON><PERSON><PERSON>", "markerInfo", "setMarkerInfo", "reset<PERSON><PERSON><PERSON>", "onMarkerClick", "propsinit", "_e", "setPointsFromEvents", "eventFilterpoints", "_", "push", "event", "_id", "country", "coordinates", "latitude", "longitude", "useEffect", "RKIMAP1", "onClose", "MarkerInfo", "info", "Markerprops", "ul", "map", "item", "index", "li", "Link", "href", "as", "length", "url", "RKIMapInfowindow", "onCloseClick", "children", "InfoWindow", "div", "fill", "stoke", "road", "geometry", "mapStyles", "RKIMap1", "initialCenter", "height", "width", "zoom", "minZoom", "locale", "useRouter", "isLoaded", "loadError", "useGoogleMaps", "className", "style", "GoogleMap", "mapContainerStyle", "center", "defaultCenter", "onLoad", "onMapLoad", "setOptions", "styles", "options", "keyboardShortcuts", "streetViewControl", "panControl", "clickableIcons", "mapTypeControl", "fullscreenControl", "console", "log"], "sourceRoot": "", "ignoreList": []}