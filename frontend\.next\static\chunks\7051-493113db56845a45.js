"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[7051],{12613:(e,t,r)=>{r.d(t,{A:()=>s});var a=r(37876);r(14232);var n=r(56856);let s=e=>(0,a.jsx)(n.Ay,{...e})},35611:(e,t,r)=>{r.d(t,{sx:()=>d,s3:()=>n.s3,ks:()=>n.ks,yk:()=>a.A});var a=r(54773),n=r(59200),s=r(37876),i=r(14232),l=r(39593),o=r(29504);let d={RadioGroup:e=>{let{name:t,valueSelected:r,onChange:a,errorMessage:n,children:o}=e,{errors:d,touched:c}=(0,l.j7)(),u=c[t]&&d[t];i.useMemo(()=>({name:t}),[t]);let h=i.Children.map(o,e=>i.isValidElement(e)&&function(e){return"object"==typeof e&&null!==e}(e.props)?i.cloneElement(e,{name:t,...e.props}):e);return(0,s.jsxs)("div",{children:[(0,s.jsx)("div",{className:"radio-group",children:h}),u&&(0,s.jsx)("div",{className:"invalid-feedback d-block",children:n||("string"==typeof d[t]?d[t]:String(d[t]))})]})},RadioItem:e=>{let{id:t,label:r,value:a,name:n,disabled:i}=e,{values:d,setFieldValue:c}=(0,l.j7)(),u=n||t;return(0,s.jsx)(o.A.Check,{type:"radio",id:t,label:r,value:a,name:u,checked:d[u]===a,onChange:e=>{c(u,e.target.value)},disabled:i,inline:!0})}};a.A,n.ks,n.s3},43206:(e,t,r)=>{r.d(t,{A:()=>u});var a=r(37876),n=r(14232),s=r(31195),i=r(21772),l=r(11041),o=r(89099),d=r.n(o),c=r(31753);let u=e=>{let{type:t,id:r}=e,[o,u]=(0,n.useState)(5),{t:h}=(0,c.Bd)("common");return(0,n.useEffect)(()=>{o>0?setTimeout(()=>u(o-1),1e3):d().push({pathname:"/vspace/create",query:{id:r,source:t}})}),(0,a.jsxs)(s.A,{show:!0,children:[(0,a.jsx)("div",{className:"modal--align mt-2",children:(0,a.jsx)("div",{className:"modal--icon",children:(0,a.jsx)(i.g,{icon:l.e68,color:"#4dc724",size:"4x"})})}),(0,a.jsx)("div",{className:"text-center mt-4",children:(0,a.jsxs)("p",{className:"lead",children:[t," ",h("vspace.formSubmittedSuccessfully"),(0,a.jsx)("br",{}),(0,a.jsx)("small",{children:h("vspace.vspaceCreationRedirect")}),(0,a.jsx)("br",{}),(0,a.jsx)("small",{children:(0,a.jsxs)("b",{children:[" ",h("vspace.waitFor")," ",o," ",h("vspace.waitForSec")]})})]})})]})}},54773:(e,t,r)=>{r.d(t,{A:()=>o});var a=r(37876),n=r(14232),s=r(39593),i=r(91408);let l=(0,n.forwardRef)((e,t)=>{let{children:r,onSubmit:n,autoComplete:l,className:o,onKeyPress:d,initialValues:c,...u}=e,h=i.Ik().shape({});return(0,a.jsx)(s.l1,{initialValues:c||{},validationSchema:h,onSubmit:(e,t)=>{let r={preventDefault:()=>{},stopPropagation:()=>{},currentTarget:null,target:null,nativeEvent:new Event("submit"),bubbles:!1,cancelable:!0,defaultPrevented:!1,eventPhase:0,isTrusted:!1,timeStamp:Date.now(),type:"submit",isDefaultPrevented:()=>!1,isPropagationStopped:()=>!1,persist:()=>{}};n&&n(r,e,t)},...u,children:e=>(0,a.jsx)(s.lV,{ref:t,onSubmit:e.handleSubmit,autoComplete:l,className:o,onKeyPress:d,children:"function"==typeof r?r(e):r})})});l.displayName="ValidationFormWrapper";let o=l},59200:(e,t,r)=>{r.d(t,{ks:()=>i,s3:()=>l});var a=r(37876);r(14232);var n=r(29504),s=r(39593);let i=e=>{let{name:t,id:r,required:i,validator:l,errorMessage:o,onChange:d,value:c,as:u,multiline:h,rows:p,pattern:m,...j}=e;return(0,a.jsx)(s.D0,{name:t,validate:e=>{let t="string"==typeof e?e:String(e||"");return i&&(!e||""===t.trim())?(null==o?void 0:o.validator)||"This field is required":l&&!l(e)?(null==o?void 0:o.validator)||"Invalid value":m&&e&&!new RegExp(m).test(e)?(null==o?void 0:o.pattern)||"Invalid format":void 0},children:e=>{let{field:t,meta:s}=e;return(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(n.A.Control,{...t,...j,id:r,as:u||"input",rows:p,isInvalid:s.touched&&!!s.error,onChange:e=>{t.onChange(e),d&&d(e)},value:void 0!==c?c:t.value}),s.touched&&s.error?(0,a.jsx)(n.A.Control.Feedback,{type:"invalid",children:s.error}):null]})}})},l=e=>{let{name:t,id:r,required:i,errorMessage:l,onChange:o,value:d,children:c,...u}=e;return(0,a.jsx)(s.D0,{name:t,validate:e=>{if(i&&(!e||""===e))return(null==l?void 0:l.validator)||"This field is required"},children:e=>{let{field:t,meta:s}=e;return(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(n.A.Control,{as:"select",...t,...u,id:r,isInvalid:s.touched&&!!s.error,onChange:e=>{t.onChange(e),o&&o(e)},value:void 0!==d?d:t.value,children:c}),s.touched&&s.error?(0,a.jsx)(n.A.Control.Feedback,{type:"invalid",children:s.error}):null]})}})}},67051:(e,t,r)=>{r.r(t),r.d(t,{default:()=>E});var a=r(37876),n=r(14232),s=r(49589),i=r(29335),l=r(56970),o=r(37784),d=r(29504),c=r(60282),u=r(888),h=r(54975),p=r(67814),m=r(89099),j=r.n(m),x=r(48230),g=r.n(x),A=r(10841),v=r.n(A),y=r(82851),_=r.n(y),f=r(21772),b=r(11041),w=r(97685),k=r(35611),S=r(54773),C=r(12613),N=r(53718),P=r(31753),I=r(43206),L=r(5671);let T={title:"",website:"",funded_by:"",status:null,countryTerritory:"",start_date:null,end_date:null,area_of_work:[],description:"",institution_invites:[],partner_institutions:[],checked:!1},E=e=>{let t=(0,n.useRef)(null),r=(0,n.useRef)(null),{t:m,i18n:x}=(0,P.Bd)("common"),A="de"===x.language?{title_de:"asc"}:{title:"asc"},y=x.language,[E,G]=(0,n.useState)(T),[D]=(0,n.useState)({invitesCountry:[],invitesRegion:[],invitesOrganisationType:[],invitesOrganisation:[],invitesExpertise:[],invitesNetWork:[],userList:[],visibility:!0}),[M,q]=(0,n.useState)([]),[F,O]=(0,n.useState)([]),[,W]=(0,n.useState)([]),[,B]=(0,n.useState)([]),[K,V]=(0,n.useState)([]),[,$]=(0,n.useState)([]),[,z]=(0,n.useState)([]),[Q,U]=(0,n.useState)(!1),[H,J]=(0,n.useState)(!1),[X,Y]=(0,n.useState)([]),[Z,ee]=(0,n.useState)(null),et=e.routes&&"edit"==e.routes[0]&&e.routes[1],[er,ea]=(0,n.useState)(!0),[en,es]=(0,n.useState)([{partner_country:"",regions:[],partner_region:[],institutions:[],partner_institution:[],countryregions:[],world_region:""}]),[ei,el]=(0,n.useState)([{title:"",contact_name:"",email:"",_id:null}]),[eo,ed]=(0,n.useState)(1),[ec,eu]=(0,n.useState)(""),[eh,ep]=(0,n.useState)(""),em={query:{},sort:A,limit:"~",languageCode:y},ej=async e=>{let t=await N.A.get("/projectstatus",e);t&&Array.isArray(t.data)&&q(t.data),await R(e,O,B);let r=await N.A.get("/institution",e);if(r&&Array.isArray(r.data)){let e=r.data.map(e=>{if("Request Pending"!==e.status)return{label:e.title,value:e._id}});V(_().compact(e))}let a=await N.A.get("/institutiontype",e);a&&Array.isArray(a.data)&&$(a.data);let n=await N.A.get("/institutionnetwork",e);n&&Array.isArray(n.data)&&z(n.data);let s=await N.A.get("/areaofwork",e),i=[];s&&Array.isArray(s.data)&&Y(s.data.map((e,t)=>({label:e.title,value:e._id})))};(0,n.useEffect)(()=>{et&&(async t=>{let r=await N.A.get("/project/".concat(e.routes[1]),t);if(r){let e=[],{status:a,country:n,area_of_work:s,partner_institutions:i,start_date:l,end_date:o}=r;return function(e,t,r,a,n,s){e.start_date=t?v()(t).toDate():null,e.end_date=r?v()(r).toDate():null,e.status=a&&a._id?a._id:null,e.country=n&&n._id?n._id:null,e.area_of_work=s&&s.length>0?s.map((e,t)=>({label:e.title,value:e._id})):[]}(r,l,o,a,n,s),function(e,t,r,a){e&&e.forEach(async(e,n)=>{let s=e.partner_region&&e.partner_region.map((e,t)=>({label:e.title,value:e._id})),i=e.partner_region&&e.partner_region.map((e,t)=>e._id),l=e.partner_institution&&e.partner_institution.map(e=>({label:e.title,value:e._id})),o=e.partner_institution&&e.partner_institution.map(e=>e._id);t.push({partner_country:e.partner_country._id,regions:s,partner_region:i,institutions:l,partner_institution:o,world_region:e.partner_country.world_region._id,countryregions:await r(e.partner_country._id,a)})})}(i,e,eg,t),es(e),G(e=>({...e,...r})),o&&G(e=>({...e,checked:!0}))}})(em),ej(em),ex()},[]);let ex=async()=>{let e=await N.A.post("/users/getLoggedUser",{});if(e&&e.roles){var t;(null==(t=e.roles)?void 0:t.filter(e=>"EMT_NATIONAL_FOCALPOINT"==e||"NGOS"===e)).length>0?ea(!1):ea(!0)}},eg=async(e,t)=>{let r=[];if(e){let a=await N.A.get("/country_region/".concat(e),t);a&&a.data&&(r=a.data.map((e,t)=>({label:e.title,value:e._id}))).sort((e,t)=>e.label.localeCompare(t.label))}return r},eA=(e,t)=>{"start_date"==t&&null==e&&G(t=>({...t,end_date:e,start_date:e})),G(r=>({...r,[t]:e}))},ev=(0,n.useRef)(null),ey=()=>{let e=[...ei];e.push({title:"",contact_name:"",email:"",_id:null}),el(e),ed(e.length)},e_=(e,t)=>{ei.splice(t,1);let r=[...ei];el(r),ed(r.length),0===ei.length&&ey()},ef=()=>{let e={partner_country:"",world_region:"",regions:[],partner_region:[],institutions:[],partner_institution:[],countryregions:[]};es(t=>[...t,e])},eb=(e,t)=>{en.splice(t,1),es([...en]),0===en.length&&ef()},ew=e=>{G(t=>({...t,description:e}))};n.useEffect(()=>{if(D){let e={};Object.keys(D).forEach((t,r)=>{let a=D[t].length>0&&D[t].map((e,t)=>e.value);e[t]=a||[]}),ek(e)}else console.log("No threshold reached.")},[D]);let ek=async e=>{let{invitesCountry:t,invitesOrganisation:r}=e,a=await N.A.post("/user-invite",{query:{country:t,institution:r}});a&&Array.isArray(a)&&W(a.map((e,t)=>({label:e.username,value:e._id})))},eS=async(e,t,r)=>{if(e.target){let{name:r,value:a}=e.target;en[t][r]=a,"partner_country"==r&&(en[t].world_region=e.target[e.target.selectedIndex].getAttribute("data-worldregion"),en[t].countryregions=await eg(a,em),en[t].regions=[])}else"countries_regions"==r&&(en[t].regions=e,en[t].partner_region=e.map((e,t)=>e.value)),"partner_institutions"==r&&(en[t].institutions=e,en[t].partner_institution=e.map((e,t)=>e.value),console.log(en[t].institutions),en[t].institutions.length?eu(""):eu(m("toast.PartnerInstitutionshouldnotbeempty")));es([...en])},eC=e=>{let{name:t,value:r}=e.target;e.target&&G(e=>({...e,[t]:r}))},eN=async a=>{if(a.preventDefault(),0==E.area_of_work.length&&0==en[0].partner_institution.length){w.Ay.error(m("toast.AreaofWorkandPartnerInstitutionshouldnotbeempty")),window.scrollTo(0,0),eu(m("toast.PartnerInstitutionshouldnotbeempty")),ep(m("toast.AreaofWorkshouldnotbeempty"));return}if(0==E.area_of_work.length){w.Ay.error(m("toast.AreaofWorkshouldnotbeempty")),window.scrollTo(0,0),ep(m("toast.AreaofWorkshouldnotbeempty"));return}if(0==en[0].partner_institution.length){w.Ay.error(m("toast.PartnerInstitutionshouldnotbeempty")),window.scrollTo(0,0),ep(m("toast.PartnerInstitutionshouldnotbeempty"));return}if(null==E.start_date){var n;null==(n=r.current)||n.focus()}else{let r,n;t.current&&t.current.setAttribute("disabled","disabled");let s=_().map(en,_().partialRight(_().pick,["partner_country","partner_region","partner_institution","world_region"]));E.area_of_work=E.area_of_work?E.area_of_work.map((e,t)=>e.value):[],E.institution_invites=ei,E.partner_institutions=s,a.preventDefault(),et?(n="toast.Projectupdatedsuccessfully",r=await N.A.patch("/project/".concat(e.routes[1]),E)):(n="toast.Projectaddedsuccessfully",r=await N.A.post("/project",E)),function(e,t,r,a,n,s){e&&e._id?t?(r((null==e?void 0:e._id)&&e._id),a(!0)):(w.Ay.success(n(s)),j().push("/project/[...routes]","/project/show/".concat(e._id))):w.Ay.error(e)}(r,Q,ee,J,m,n)}};return(0,a.jsxs)(s.A,{className:"formCard",fluid:!0,children:[(0,a.jsx)(i.A,{children:(0,a.jsx)(S.A,{onSubmit:eN,ref:ev,children:(0,a.jsxs)(i.A.Body,{children:[(0,a.jsx)(l.A,{children:(0,a.jsx)(o.A,{children:(0,a.jsx)(i.A.Title,{children:et?m("editProject"):m("addProject")})})}),(0,a.jsx)("hr",{}),(0,a.jsxs)(l.A,{className:"mb-3",children:[(0,a.jsx)(o.A,{md:6,lg:6,sm:12,children:(0,a.jsxs)(d.A.Group,{children:[(0,a.jsx)(d.A.Label,{className:"required-field",children:m("Title")}),(0,a.jsx)(k.ks,{name:"title",id:"title",required:!0,value:E.title,validator:e=>""!=e.trim(),errorMessage:{validator:m("PleaseAddtheTitle")},onChange:eC})]})}),(0,a.jsx)(o.A,{md:6,lg:6,sm:12,children:(0,a.jsxs)(d.A.Group,{children:[(0,a.jsx)(d.A.Label,{children:m("Website")}),(0,a.jsx)(k.ks,{name:"website",id:"website",pattern:"^(?:http(s)?:\\/\\/)?[\\w.-]+(?:\\.[\\w\\.-]+)+[\\w\\-\\._~:/?#[\\]@!\\$&'\\(\\)\\*\\+,;=.]+$",errorMessage:{pattern:m("Pleaseentervalidwebsite")},value:E.website,onChange:eC})]})})]}),(0,a.jsx)(l.A,{className:"mb-3",children:(0,a.jsx)(o.A,{children:(0,a.jsxs)(d.A.Group,{children:[(0,a.jsx)(d.A.Label,{children:m("Description")}),(0,a.jsx)(L.x,{initContent:E.description,onChange:e=>ew(e)})]})})}),(0,a.jsxs)(l.A,{className:"d-flex align-items-center mb-3",children:[(0,a.jsx)(o.A,{md:6,lg:6,sm:12,children:(0,a.jsxs)(d.A.Group,{style:{maxWidth:"500px"},children:[(0,a.jsx)(d.A.Label,{className:"required-field",children:m("AreaofWorkthisprojectcovers")}),(0,a.jsx)(p.KF,{overrideStrings:{selectSomeItems:m("SelectAreaofwork"),allItemsAreSelected:m("AllAreaofwork'sareSelected")},options:X,value:E.area_of_work,onChange:(e,t)=>{G(t=>({...t,area_of_work:e})),e.length?ep(""):ep(m("toast.AreaofWorkshouldnotbeempty"))},className:"project-covers",labelledBy:m("Selectareaofwork")}),eh&&(0,a.jsx)("p",{style:{color:"red"},children:eh})]})}),(0,a.jsx)(o.A,{md:6,lg:6,sm:12,children:(0,a.jsxs)(d.A.Group,{children:[(0,a.jsx)(d.A.Label,{children:m("FundedBy")}),(0,a.jsx)(k.ks,{name:"funded_by",id:"funded_by",value:E.funded_by,onChange:eC})]})})]}),(0,a.jsxs)(l.A,{className:"mb-3",children:[(0,a.jsx)(o.A,{lg:3,sm:12,children:(0,a.jsxs)(d.A.Group,{children:[(0,a.jsx)(d.A.Label,{children:m("ProjectStatus")}),(0,a.jsxs)(k.s3,{name:"status",id:"status",value:null===E.status?"":E.status,onChange:eC,children:[(0,a.jsx)("option",{value:"",children:m("SelectProjectStatus")}),M.map((e,t)=>(0,a.jsx)("option",{value:e._id,children:e.title},t))]})]})}),(0,a.jsx)(o.A,{lg:3,sm:4,className:"align-self-center",children:(0,a.jsxs)(d.A.Group,{children:[(0,a.jsx)(l.A,{children:(0,a.jsx)(o.A,{children:(0,a.jsx)(d.A.Label,{className:"required-field",children:m("StartDate")})})}),(0,a.jsx)("label",{className:"date-validation w-100",ref:r,children:(0,a.jsx)(C.A,{selected:E.start_date,onChange:e=>eA(e,"start_date"),dateFormat:"MMMM d, yyyy",placeholderText:m("SelectStartDate")})})]})}),(0,a.jsx)(o.A,{lg:2,sm:4,children:(0,a.jsx)(d.A.Check,{type:"checkbox",checked:E.checked,onChange:()=>{G(e=>({...e,checked:!e.checked,end_date:null}))},label:m("ShowEndDate")})}),E.checked&&(0,a.jsx)(o.A,{lg:3,sm:4,className:"align-self-center",children:(0,a.jsxs)(d.A.Group,{children:[(0,a.jsx)(l.A,{children:(0,a.jsx)(o.A,{children:(0,a.jsx)(d.A.Label,{children:m("EndDate")})})}),(0,a.jsx)(C.A,{selected:E.end_date,disabled:!E.start_date,onChange:e=>eA(e,"end_date"),dateFormat:"MMMM d, yyyy",minDate:E.start_date,placeholderText:m("SelectEndDate")})]})})]}),en.map((e,t)=>(0,a.jsxs)("div",{children:[(0,a.jsx)(o.A,{className:"header-block pb-1 pt-2",lg:12,children:(0,a.jsx)("h6",{children:(0,a.jsxs)("span",{children:[m("Country")," ",t+1]})})}),(0,a.jsxs)(l.A,{className:"mb-3",children:[(0,a.jsx)(o.A,{lg:4,sm:6,children:(0,a.jsxs)(d.A.Group,{children:[(0,a.jsx)(d.A.Label,{className:"required-field",children:m("CountryWheretheProjectistakingplace")}),(0,a.jsxs)(k.s3,{name:"partner_country",id:"partner_country",value:e.partner_country,onChange:e=>eS(e,t,"countries"),required:!0,errorMessage:m("thisfieldisrequired"),children:[(0,a.jsx)("option",{value:"",children:m("SelectCountry")}),F.map((e,t)=>(0,a.jsx)("option",{"data-worldregion":e.world_region._id,value:e._id,children:e.title},t))]})]})}),(0,a.jsx)(o.A,{lg:4,sm:6,children:(0,a.jsxs)(d.A.Group,{className:"mw-100",children:[(0,a.jsx)(d.A.Label,{children:m("CountryRegions")}),(0,a.jsx)(p.KF,{overrideStrings:{selectSomeItems:m("SelectRegions"),allItemsAreSelected:m("AllRegionsareSelected")},options:e.countryregions,value:e.regions,onChange:e=>eS(e,t,"countries_regions"),className:"region",labelledBy:m("SelectRegions")})]})}),(0,a.jsx)(o.A,{lg:4,sm:6,children:(0,a.jsxs)(d.A.Group,{style:{maxWidth:"400px"},children:[(0,a.jsx)(d.A.Label,{className:"required-field",children:m("PartnerOrganisations(onplatform)")}),(0,a.jsx)(p.KF,{overrideStrings:{selectSomeItems:m("SelectOrganisations"),allItemsAreSelected:m("AllOrganisationsareselected")},options:K,value:e.institutions,onChange:e=>eS(e,t,"partner_institutions"),className:"organisation",labelledBy:m("SelectOrganisations")}),ec&&(0,a.jsx)("p",{style:{color:"red"},children:ec})]})})]}),(0,a.jsx)("div",{children:0===t?(0,a.jsx)("span",{}):(0,a.jsx)(l.A,{className:"mb-4",children:(0,a.jsx)(o.A,{xs:!0,lg:"4",children:(0,a.jsx)(c.A,{variant:"secondary",onClick:e=>eb(e,t),children:m("Remove")})})})})]},t)),(0,a.jsx)(l.A,{children:(0,a.jsx)(o.A,{xs:!0,lg:"4",children:(0,a.jsx)(c.A,{variant:"secondary",onClick:ef,children:m("AddAnotherCountry")})})}),(0,a.jsx)("hr",{}),(0,a.jsx)(l.A,{children:(0,a.jsx)(o.A,{children:(0,a.jsxs)(d.A.Group,{children:[(0,a.jsx)(d.A.Label,{children:m("PartnerOrganisationnotlisted?Createnewandinvite")}),(0,a.jsxs)(u.A,{activeKey:eo,onSelect:e=>ed(e),id:"uncontrolled-tab-example",children:[" ",ei.map((e,t)=>(0,a.jsxs)(h.A,{eventKey:"".concat(t+1),title:"Organisation ".concat(t+1),children:[(0,a.jsxs)(l.A,{children:[(0,a.jsx)(o.A,{lg:4,sm:6,children:(0,a.jsxs)(d.A.Group,{className:"pt-4",children:[(0,a.jsx)(d.A.Label,{children:m("OrganisationName")}),(0,a.jsx)(k.ks,{name:"input".concat(t+1,"-title"),id:"input".concat(t+1),value:e.title,onChange:e=>(function(e,t){let r=[...ei];r[t].title=e.target.value,el(r)})(e,t)})]})}),(0,a.jsx)(o.A,{lg:4,sm:6,children:(0,a.jsxs)(d.A.Group,{className:"pt-4",children:[(0,a.jsx)(d.A.Label,{children:m("ContactName")}),(0,a.jsx)(k.ks,{name:"input".concat(t+1,"-contact_name"),id:"input".concat(t+1),value:e.contact_name,onChange:e=>(function(e,t){let r=[...ei];r[t].contact_name=e.target.value,el(r)})(e,t)})]})}),(0,a.jsx)(o.A,{lg:4,sm:6,children:(0,a.jsxs)(d.A.Group,{className:"pt-4",children:[(0,a.jsx)(d.A.Label,{children:m("E-MailAddress")}),(0,a.jsx)(k.ks,{name:"input".concat(t+1),id:"input".concat(t+1),value:e.email,onChange:e=>(function(e,t){let r=[...ei];r[t].email=e.target.value,el(r)})(e,t),pattern:"^[^@]+@[^@]+\\.[^@]+$",errorMessage:{pattern:m("Pleaseenteravalidemail")}})]})})]}),(0,a.jsx)("div",{children:0===t?(0,a.jsx)("span",{}):(0,a.jsx)(o.A,{xs:!0,lg:"4",className:"p-0",children:(0,a.jsx)(c.A,{onSelect:e=>ed(e),variant:"secondary",onClick:e=>e_(e,t),children:m("Remove")})})})]},t)),(0,a.jsx)(h.A,{eventKey:"add",title:(0,a.jsx)("div",{children:(0,a.jsxs)("span",{onClick:ey,children:[" ",(0,a.jsx)(f.g,{icon:b.QLR,color:"#808080"})]})})})]})]})})}),(0,a.jsx)(l.A,{className:"mt-4",children:(0,a.jsxs)(o.A,{children:[(0,a.jsx)(i.A.Text,{children:(0,a.jsx)("b",{children:m("VirtualSpace")})}),(0,a.jsx)("hr",{}),(0,a.jsx)(d.A.Check,{className:"pb-4",disabled:!er,type:"checkbox",onChange:()=>U(!Q),name:"virtula",checked:Q,label:m("WouldliketocreateaVirtualSpace")})]})}),(0,a.jsx)(l.A,{className:"my-4",children:(0,a.jsxs)(o.A,{children:[(0,a.jsx)(c.A,{className:"me-2",type:"submit",variant:"primary",ref:t,onClick:eN,children:m("submit")}),(0,a.jsx)(c.A,{className:"me-2",onClick:()=>{G(T),es([]),el([]),window.scrollTo(0,0)},variant:"info",children:m("reset")}),(0,a.jsx)(g(),{href:"/project",as:"/project",children:(0,a.jsx)(c.A,{variant:"secondary",children:m("Cancel")})})]})})]})})}),H&&(0,a.jsx)(I.A,{type:"Project",id:Z})]})};async function R(e,t,r){let a=await N.A.get("/country",e);a&&Array.isArray(a.data)&&t(a.data);let n=await N.A.get("/expertise",e);n&&Array.isArray(n.data)&&r(n.data)}}}]);
//# sourceMappingURL=7051-493113db56845a45.js.map