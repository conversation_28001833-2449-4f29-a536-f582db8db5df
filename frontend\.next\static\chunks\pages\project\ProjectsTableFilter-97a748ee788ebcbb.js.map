{"version": 3, "file": "static/chunks/pages/project/ProjectsTableFilter-97a748ee788ebcbb.js", "mappings": "kOAgFA,MAxE4B,OAAC,YAC3BA,CAAU,QAuEGC,EAtEbC,CAAQ,gBAsEwBD,EAAC,IArEjCE,CAAoB,CACpBC,SAAO,cACPC,CAAY,CAOb,GACO,CAACC,EAAQC,EAAU,CAAGC,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAC,EAAE,EACjC,GAAEC,CAAC,CAAE,CAAGC,CAAAA,EAAAA,EAAAA,EAAAA,CAAcA,CAAC,UAEvBC,EAAmB,MAAOC,IAC9B,IAAMC,EAAW,MAAMC,EAAAA,CAAUA,CAACC,GAAG,CAAC,iBAAkBH,GACpDC,GAAYG,MAAMC,OAAO,CAACJ,EAASK,IAAI,GAAG,EAAYL,EAASK,IAAI,CACzE,EASA,MAPAC,CAAAA,EAAAA,EAAAA,SAAAA,CAASA,CAAC,KACRR,EAAiB,CACfS,MAAO,CAAC,EACRC,KAAM,CAAEC,MAAO,KAAM,CACvB,EACF,EAAG,EAAE,EAGH,UAACC,EAAAA,CAASA,CAAAA,CAACC,KAAK,IAACC,UAAU,eACzB,WAACC,EAAAA,CAAGA,CAAAA,WACF,UAACC,EAAAA,CAAGA,CAAAA,CAACC,GAAI,EAAGH,UAAU,oCACpB,UAACI,EAAAA,CAAWA,CAAAA,CACVC,KAAK,OACLL,UAAU,cACVM,YAAatB,EAAE,iBACfuB,aAAW,SACXC,MAAOjC,EACPkC,SAAUhC,MAGd,UAACyB,EAAAA,CAAGA,CAAAA,UACF,UAACQ,EAAAA,CAAIA,CAAAA,UACH,WAACA,EAAAA,CAAIA,CAACC,KAAK,EAACC,GAAIX,EAAAA,CAAGA,CAAEY,UAAU,yBAC7B,UAACH,EAAAA,CAAIA,CAACI,KAAK,EAACC,MAAM,IAACC,GAAG,IAAIC,GAAG,aAAI,WAGjC,UAACf,EAAAA,CAAGA,CAAAA,CAACF,UAAU,qBACb,WAACI,EAAAA,CAAWA,CAAAA,CACVQ,GAAG,SACHL,aAAW,SACXE,SAAU/B,EACV8B,MAAO5B,YAEP,UAACsC,SAAAA,CAAOV,MAAO,YAAI,QAClB3B,EAAOsC,GAAG,CAAC,CAACC,EAAWC,IAEpB,UAACH,SAAAA,CAAmBV,MAAOY,EAAKE,GAAG,UAChCF,EAAKvB,KAAK,EADAwB,oBAanC,mBC7EA,4CACA,+BACA,WACA,OAAe,EAAQ,KAAoD,CAC3E,EACA,SAFsB", "sources": ["webpack://_N_E/./pages/project/ProjectsTableFilter.tsx", "webpack://_N_E/?08e7"], "sourcesContent": ["// Import Library\r\nimport { useEffect, useState } from \"react\";\r\nimport { Col, Container, FormControl, Form, Row } from \"react-bootstrap\";\r\n\r\n//Import services/components\r\nimport { useTranslation } from 'next-i18next';\r\nimport apiService from \"../../services/apiService\";\r\n\r\nconst ProjectsTableFilter = ({\r\n  filterText,\r\n  onFilter,\r\n  onFilterStatusChange,\r\n  onClear,\r\n  filterStatus,\r\n}: {\r\n  filterText: any,\r\n  onFilter: any,\r\n  onFilterStatusChange: any,\r\n  onClear: any,\r\n  filterStatus: any,\r\n}) => {\r\n  const [status, setStatus] = useState([]);\r\n  const { t } = useTranslation('common');\r\n\r\n  const getProjectStatus = async (projectParams: any) => {\r\n    const response = await apiService.get(\"/projectstatus\", projectParams);\r\n    if (response && Array.isArray(response.data)) { setStatus(response.data) }\r\n  };\r\n\r\n  useEffect(() => {\r\n    getProjectStatus({\r\n      query: {},\r\n      sort: { title: \"asc\" },\r\n    });\r\n  }, []);\r\n\r\n  return (\r\n    <Container fluid className=\"p-0\">\r\n      <Row>\r\n        <Col xs={6} className=\"ps-0 align-self-end mb-3\">\r\n          <FormControl\r\n            type=\"text\"\r\n            className=\"searchInput\"\r\n            placeholder={t(\"vspace.Search\")}\r\n            aria-label=\"Search\"\r\n            value={filterText}\r\n            onChange={onFilter}\r\n          />\r\n        </Col>\r\n        <Col>\r\n          <Form>\r\n            <Form.Group as={Row} controlId=\"statusFilter\">\r\n              <Form.Label column sm=\"3\" lg=\"2\">\r\n                Status\r\n              </Form.Label>\r\n              <Col className=\"ps-0 pe-1\">\r\n                <FormControl\r\n                  as=\"select\"\r\n                  aria-label=\"Status\"\r\n                  onChange={onFilterStatusChange}\r\n                  value={filterStatus}\r\n                >\r\n                  <option value={\"\"}>All</option>\r\n                  {status.map((item: any, index) => {\r\n                    return (\r\n                      <option key={index} value={item._id}>\r\n                        {item.title}\r\n                      </option>\r\n                    );\r\n                  })}\r\n                </FormControl>\r\n              </Col>\r\n            </Form.Group>\r\n          </Form>\r\n        </Col>\r\n      </Row>\r\n    </Container>\r\n  );\r\n};\r\n\r\nexport default ProjectsTableFilter;\r\n", "\n    (window.__NEXT_P = window.__NEXT_P || []).push([\n      \"/project/ProjectsTableFilter\",\n      function () {\n        return require(\"private-next-pages/project/ProjectsTableFilter.tsx\");\n      }\n    ]);\n    if(module.hot) {\n      module.hot.dispose(function () {\n        window.__NEXT_P.push([\"/project/ProjectsTableFilter\"])\n      });\n    }\n  "], "names": ["filterText", "ProjectsTableFilter", "onFilter", "onFilterStatusChange", "onClear", "filterStatus", "status", "setStatus", "useState", "t", "useTranslation", "getProjectStatus", "projectParams", "response", "apiService", "get", "Array", "isArray", "data", "useEffect", "query", "sort", "title", "Container", "fluid", "className", "Row", "Col", "xs", "FormControl", "type", "placeholder", "aria-label", "value", "onChange", "Form", "Group", "as", "controlId", "Label", "column", "sm", "lg", "option", "map", "item", "index", "_id"], "sourceRoot": "", "ignoreList": []}