"use strict";(()=>{var e={};e.id=6599,e.ids=[636,3220,6599],e.modules={123:e=>{e.exports=require("dom-helpers/removeEventListener")},283:(e,r,t)=>{t.a(e,async(e,s)=>{try{t.r(r),t.d(r,{default:()=>c});var a=t(8732),o=t(19918),i=t.n(o),n=t(82015),u=t(12403),p=t(91353),l=t(56084),d=t(63487),x=e([d]);d=(x.then?(await x)():x)[0];let c=function(e){let[r,t]=(0,n.useState)([]),[,s]=(0,n.useState)(!1),[o,x]=(0,n.useState)(0),[c,m]=(0,n.useState)(10),[g,q]=(0,n.useState)(!1),[h,P]=(0,n.useState)({}),S={sort:{created_at:"desc"},limit:c,page:1,query:{}},v=[{name:"Username",selector:"username",cell:e=>e.username},{name:"Email",selector:"email",cell:e=>e.email},{name:"Role",selector:"role",cell:e=>e.role?e.role.title:""},{name:"Institution",selector:"institution",cell:e=>e.institution?e.institution.title:""},{name:"Action",selector:"",cell:e=>(0,a.jsxs)("div",{children:[(0,a.jsx)(i(),{href:"/users/[...routes]",as:`/users/edit/${e._id}`,children:"Edit"}),"\xa0",(0,a.jsx)("a",{onClick:()=>b(e),children:"Delete"})]})}],A=async()=>{s(!0);let e=await d.A.get("/users",S);e&&e.data&&e.data.length>0&&(t(e.data),x(e.totalCount),s(!1))},f=async(e,r)=>{S.limit=e,S.page=r,s(!0);let a=await d.A.get("/users",S);a&&a.data&&a.data.length>0&&(t(a.data),m(e),s(!1))},b=async e=>{P(e),q(!0)},w=async()=>{await d.A.remove(`/users/${h._id}`),A(),P({}),q(!1)},y=()=>q(!1);return(0,a.jsxs)("div",{children:[(0,a.jsxs)(u.A,{show:g,onHide:y,children:[(0,a.jsx)(u.A.Header,{closeButton:!0,children:(0,a.jsx)(u.A.Title,{children:"Delete User"})}),(0,a.jsx)(u.A.Body,{children:"Are you sure want to delete this user ?"}),(0,a.jsxs)(u.A.Footer,{children:[(0,a.jsx)(p.A,{variant:"secondary",onClick:y,children:"Cancel"}),(0,a.jsx)(p.A,{variant:"primary",onClick:w,children:"Yes"})]})]}),(0,a.jsx)(l.A,{columns:v,data:r,totalRows:o,pagServer:!0,handlePerRowsChange:f,handlePageChange:e=>{S.limit=c,S.page=e,A()}})]})};s()}catch(e){s(e)}})},1332:e=>{e.exports=require("react-custom-scrollbars-2")},1428:e=>{e.exports=import("axios")},1680:e=>{e.exports=require("@restart/ui/utils")},1919:e=>{e.exports=require("react-transition-group/Transition")},3892:e=>{e.exports=require("classnames")},4048:e=>{e.exports=require("react-truncate")},6009:e=>{e.exports=require("dom-helpers/ownerDocument")},6952:e=>{e.exports=require("@restart/ui/Modal")},7374:e=>{e.exports=require("@restart/ui/Dropdown")},8732:e=>{e.exports=require("react/jsx-runtime")},9653:e=>{e.exports=require("@restart/ui/Overlay")},11242:e=>{e.exports=require("redux-persist/integration/react")},11688:e=>{e.exports=require("@restart/hooks/useWillUnmount")},12388:e=>{e.exports=require("dom-helpers/addEventListener")},13364:e=>{e.exports=require("redux-saga/effects")},14062:e=>{e.exports=import("react-redux")},14078:e=>{e.exports=import("swr")},14332:e=>{e.exports=require("uncontrollable")},16116:e=>{e.exports=require("invariant")},18622:e=>{e.exports=require("yup")},19121:e=>{e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},22326:e=>{e.exports=require("react-dom")},22541:e=>{e.exports=require("redux-persist/lib/storage")},25303:e=>{e.exports=require("@restart/ui/NavItem")},26324:e=>{e.exports=require("warning")},26659:(e,r,t)=>{t.a(e,async(e,s)=>{try{t.r(r),t.d(r,{config:()=>q,default:()=>x,getServerSideProps:()=>g,getStaticPaths:()=>m,getStaticProps:()=>c,reportWebVitals:()=>h,routeModule:()=>b,unstable_getServerProps:()=>A,unstable_getServerSideProps:()=>f,unstable_getStaticParams:()=>v,unstable_getStaticPaths:()=>S,unstable_getStaticProps:()=>P});var a=t(63885),o=t(80237),i=t(81413),n=t(9616),u=t.n(n),p=t(72386),l=t(283),d=e([p,l]);[p,l]=d.then?(await d)():d;let x=(0,i.M)(l,"default"),c=(0,i.M)(l,"getStaticProps"),m=(0,i.M)(l,"getStaticPaths"),g=(0,i.M)(l,"getServerSideProps"),q=(0,i.M)(l,"config"),h=(0,i.M)(l,"reportWebVitals"),P=(0,i.M)(l,"unstable_getStaticProps"),S=(0,i.M)(l,"unstable_getStaticPaths"),v=(0,i.M)(l,"unstable_getStaticParams"),A=(0,i.M)(l,"unstable_getServerProps"),f=(0,i.M)(l,"unstable_getServerSideProps"),b=new a.PagesRouteModule({definition:{kind:o.A.PAGES,page:"/users/UsersTable",pathname:"/users/UsersTable",bundlePath:"",filename:""},components:{App:p.default,Document:u()},userland:l});s()}catch(e){s(e)}})},27825:e=>{e.exports=require("lodash")},27910:e=>{e.exports=require("stream")},28217:e=>{e.exports=require("@restart/ui/DropdownItem")},29021:e=>{e.exports=require("fs")},29294:e=>{e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},29628:e=>{e.exports=require("dom-helpers/addClass")},29825:e=>{e.exports=require("prop-types")},29841:e=>{e.exports=require("dom-helpers/hasClass")},30362:e=>{e.exports=require("@restart/ui/DropdownToggle")},33873:e=>{e.exports=require("path")},36653:e=>{e.exports=require("nprogress")},36955:e=>{e.exports=require("@restart/hooks/useBreakpoint")},37766:e=>{e.exports=require("@restart/hooks/useMergedRefs")},38609:e=>{e.exports=require("react-data-table-component")},39756:e=>{e.exports=import("redux")},40051:e=>{e.exports=require("dom-helpers/removeClass")},40361:e=>{e.exports=require("next/dist/compiled/next-server/pages.runtime.prod.js")},42893:e=>{e.exports=import("react-hot-toast")},43294:e=>{e.exports=require("formik")},50009:e=>{e.exports=require("@restart/ui/DropdownMenu")},56084:(e,r,t)=>{t.d(r,{A:()=>p});var s=t(8732);t(82015);var a=t(38609),o=t.n(a),i=t(88751),n=t(30370);function u(e){let{t:r}=(0,i.useTranslation)("common"),t={rowsPerPageText:r("Rowsperpage")},{columns:a,data:u,totalRows:p,resetPaginationToggle:l,subheader:d,subHeaderComponent:x,handlePerRowsChange:c,handlePageChange:m,rowsPerPage:g,defaultRowsPerPage:q,selectableRows:h,loading:P,pagServer:S,onSelectedRowsChange:v,clearSelectedRows:A,sortServer:f,onSort:b,persistTableHead:w,sortFunction:y,...j}=e,M={paginationComponentOptions:t,noDataComponent:r("NoData"),noHeader:!0,columns:a,data:u||[],dense:!0,paginationResetDefaultPage:l,subHeader:d,progressPending:P,subHeaderComponent:x,pagination:!0,paginationServer:S,paginationPerPage:q||10,paginationRowsPerPageOptions:g||[10,15,20,25,30],paginationTotalRows:p,onChangeRowsPerPage:c,onChangePage:m,selectableRows:h,onSelectedRowsChange:v,clearSelectedRows:A,progressComponent:(0,s.jsx)(n.A,{}),sortIcon:(0,s.jsx)("i",{className:"sort-icon fas fa-exchange-alt"}),sortServer:f,onSort:b,sortFunction:y,persistTableHead:w,className:"rki-table"};return(0,s.jsx)(o(),{...M})}u.defaultProps={subHeader:!1,pagination:!0,totalRows:null,pagServer:!0,onSelectedRowsChange:null,clearSelectedRows:!1,sortServer:!1,persistTableHead:!1};let p=u},57664:e=>{e.exports=require("dom-helpers/scrollbarSize")},58928:e=>{e.exports=require("dom-helpers/canUseDOM")},59717:e=>{e.exports=require("@restart/ui/DropdownContext")},60560:e=>{e.exports=require("dom-helpers/contains")},63033:e=>{e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},65447:e=>{e.exports=require("@restart/ui/Button")},67364:e=>{e.exports=require("@restart/hooks/useCallbackRef")},68455:e=>{e.exports=import("redux-saga")},69722:e=>{e.exports=require("es6-promise")},74075:e=>{e.exports=require("zlib")},74716:e=>{e.exports=require("moment")},74987:e=>{e.exports=require("@restart/ui/ModalManager")},78097:e=>{e.exports=require("next-redux-saga")},78634:e=>{e.exports=require("@restart/ui/Anchor")},80237:(e,r)=>{Object.defineProperty(r,"A",{enumerable:!0,get:function(){return t}});var t=function(e){return e.PAGES="PAGES",e.PAGES_API="PAGES_API",e.APP_PAGE="APP_PAGE",e.APP_ROUTE="APP_ROUTE",e.IMAGE="IMAGE",e}({})},81366:e=>{e.exports=require("redux-auth-wrapper/connectedAuthWrapper")},81413:(e,r)=>{Object.defineProperty(r,"M",{enumerable:!0,get:function(){return function e(r,t){return t in r?r[t]:"then"in r&&"function"==typeof r.then?r.then(r=>e(r,t)):"function"==typeof r&&"default"===t?r:void 0}}})},81521:e=>{e.exports=require("dom-helpers/querySelectorAll")},81895:e=>{e.exports=require("@restart/hooks/useEventCallback")},82015:e=>{e.exports=require("react")},86842:e=>{e.exports=require("@restart/ui/SelectableContext")},87571:e=>{e.exports=require("dom-helpers/transitionEnd")},88751:e=>{e.exports=require("next-i18next")},93787:e=>{e.exports=require("redux-persist")},94947:e=>{e.exports=require("@restart/hooks/useTimeout")},96196:e=>{e.exports=require("next-redux-wrapper")},98320:e=>{e.exports=require("@restart/hooks/useIsomorphicEffect")},99460:e=>{e.exports=require("dom-helpers/css")}};var r=require("../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[6089,9216,9616,2386],()=>t(26659));module.exports=s})();