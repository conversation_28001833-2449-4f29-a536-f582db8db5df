"use strict";(()=>{var e={};e.id=2142,e.ids=[636,2142,3220],e.modules={123:e=>{e.exports=require("dom-helpers/removeEventListener")},1332:e=>{e.exports=require("react-custom-scrollbars-2")},1428:e=>{e.exports=import("axios")},1680:e=>{e.exports=require("@restart/ui/utils")},1919:e=>{e.exports=require("react-transition-group/Transition")},3892:e=>{e.exports=require("classnames")},4048:e=>{e.exports=require("react-truncate")},6009:e=>{e.exports=require("dom-helpers/ownerDocument")},6952:e=>{e.exports=require("@restart/ui/Modal")},7374:e=>{e.exports=require("@restart/ui/Dropdown")},8732:e=>{e.exports=require("react/jsx-runtime")},9653:e=>{e.exports=require("@restart/ui/Overlay")},11242:e=>{e.exports=require("redux-persist/integration/react")},11688:e=>{e.exports=require("@restart/hooks/useWillUnmount")},12388:e=>{e.exports=require("dom-helpers/addEventListener")},13364:e=>{e.exports=require("redux-saga/effects")},14062:e=>{e.exports=import("react-redux")},14078:e=>{e.exports=import("swr")},14332:e=>{e.exports=require("uncontrollable")},16116:e=>{e.exports=require("invariant")},18622:e=>{e.exports=require("yup")},19121:e=>{e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},22326:e=>{e.exports=require("react-dom")},22541:e=>{e.exports=require("redux-persist/lib/storage")},25169:(e,r,t)=>{t.d(r,{A:()=>v});var s=t(3892),o=t.n(s),a=t(82015),n=t(14332),i=t(81895),u=t.n(i),l=t(80739),p=t(7783),c=t(8732);let d=(0,p.A)("h4");d.displayName="DivStyledAsH4";let x=a.forwardRef(({className:e,bsPrefix:r,as:t=d,...s},a)=>(r=(0,l.oU)(r,"alert-heading"),(0,c.jsx)(t,{ref:a,className:o()(e,r),...s})));x.displayName="AlertHeading";var m=t(78634),h=t.n(m);let q=a.forwardRef(({className:e,bsPrefix:r,as:t=h(),...s},a)=>(r=(0,l.oU)(r,"alert-link"),(0,c.jsx)(t,{ref:a,className:o()(e,r),...s})));q.displayName="AlertLink";var g=t(19799),f=t(73087);let A=a.forwardRef((e,r)=>{let{bsPrefix:t,show:s=!0,closeLabel:a="Close alert",closeVariant:i,className:p,children:d,variant:x="primary",onClose:m,dismissible:h,transition:q=g.A,...A}=(0,n.useUncontrolled)(e,{show:"onClose"}),v=(0,l.oU)(t,"alert"),b=u()(e=>{m&&m(!1,e)}),P=!0===q?g.A:q,y=(0,c.jsxs)("div",{role:"alert",...!P?A:void 0,ref:r,className:o()(p,v,x&&`${v}-${x}`,h&&`${v}-dismissible`),children:[h&&(0,c.jsx)(f.A,{onClick:b,"aria-label":a,variant:i}),d]});return P?(0,c.jsx)(P,{unmountOnExit:!0,...A,ref:void 0,in:s,children:y}):s?y:null});A.displayName="Alert";let v=Object.assign(A,{Link:q,Heading:x})},25303:e=>{e.exports=require("@restart/ui/NavItem")},26324:e=>{e.exports=require("warning")},27825:e=>{e.exports=require("lodash")},27910:e=>{e.exports=require("stream")},28217:e=>{e.exports=require("@restart/ui/DropdownItem")},29021:e=>{e.exports=require("fs")},29294:e=>{e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},29628:e=>{e.exports=require("dom-helpers/addClass")},29825:e=>{e.exports=require("prop-types")},29841:e=>{e.exports=require("dom-helpers/hasClass")},30362:e=>{e.exports=require("@restart/ui/DropdownToggle")},33873:e=>{e.exports=require("path")},36653:e=>{e.exports=require("nprogress")},36955:e=>{e.exports=require("@restart/hooks/useBreakpoint")},37766:e=>{e.exports=require("@restart/hooks/useMergedRefs")},39756:e=>{e.exports=import("redux")},40051:e=>{e.exports=require("dom-helpers/removeClass")},40361:e=>{e.exports=require("next/dist/compiled/next-server/pages.runtime.prod.js")},42893:e=>{e.exports=import("react-hot-toast")},43294:e=>{e.exports=require("formik")},50009:e=>{e.exports=require("@restart/ui/DropdownMenu")},54131:e=>{e.exports=import("@fortawesome/free-solid-svg-icons")},57664:e=>{e.exports=require("dom-helpers/scrollbarSize")},58928:e=>{e.exports=require("dom-helpers/canUseDOM")},59717:e=>{e.exports=require("@restart/ui/DropdownContext")},60560:e=>{e.exports=require("dom-helpers/contains")},63033:e=>{e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},65447:e=>{e.exports=require("@restart/ui/Button")},67364:e=>{e.exports=require("@restart/hooks/useCallbackRef")},68455:e=>{e.exports=import("redux-saga")},69722:e=>{e.exports=require("es6-promise")},74075:e=>{e.exports=require("zlib")},74716:e=>{e.exports=require("moment")},74987:e=>{e.exports=require("@restart/ui/ModalManager")},76965:(e,r,t)=>{t.a(e,async(e,s)=>{try{t.r(r),t.d(r,{config:()=>q,default:()=>d,getServerSideProps:()=>h,getStaticPaths:()=>m,getStaticProps:()=>x,reportWebVitals:()=>g,routeModule:()=>y,unstable_getServerProps:()=>b,unstable_getServerSideProps:()=>P,unstable_getStaticParams:()=>v,unstable_getStaticPaths:()=>A,unstable_getStaticProps:()=>f});var o=t(63885),a=t(80237),n=t(81413),i=t(9616),u=t.n(i),l=t(72386),p=t(88396),c=e([l,p]);[l,p]=c.then?(await c)():c;let d=(0,n.M)(p,"default"),x=(0,n.M)(p,"getStaticProps"),m=(0,n.M)(p,"getStaticPaths"),h=(0,n.M)(p,"getServerSideProps"),q=(0,n.M)(p,"config"),g=(0,n.M)(p,"reportWebVitals"),f=(0,n.M)(p,"unstable_getStaticProps"),A=(0,n.M)(p,"unstable_getStaticPaths"),v=(0,n.M)(p,"unstable_getStaticParams"),b=(0,n.M)(p,"unstable_getServerProps"),P=(0,n.M)(p,"unstable_getServerSideProps"),y=new o.PagesRouteModule({definition:{kind:a.A.PAGES,page:"/profile/myConsent",pathname:"/profile/myConsent",bundlePath:"",filename:""},components:{App:l.default,Document:u()},userland:p});s()}catch(e){s(e)}})},78097:e=>{e.exports=require("next-redux-saga")},78634:e=>{e.exports=require("@restart/ui/Anchor")},80237:(e,r)=>{Object.defineProperty(r,"A",{enumerable:!0,get:function(){return t}});var t=function(e){return e.PAGES="PAGES",e.PAGES_API="PAGES_API",e.APP_PAGE="APP_PAGE",e.APP_ROUTE="APP_ROUTE",e.IMAGE="IMAGE",e}({})},81366:e=>{e.exports=require("redux-auth-wrapper/connectedAuthWrapper")},81413:(e,r)=>{Object.defineProperty(r,"M",{enumerable:!0,get:function(){return function e(r,t){return t in r?r[t]:"then"in r&&"function"==typeof r.then?r.then(r=>e(r,t)):"function"==typeof r&&"default"===t?r:void 0}}})},81521:e=>{e.exports=require("dom-helpers/querySelectorAll")},81895:e=>{e.exports=require("@restart/hooks/useEventCallback")},82015:e=>{e.exports=require("react")},82053:e=>{e.exports=require("@fortawesome/react-fontawesome")},82601:(e,r,t)=>{t.a(e,async(e,s)=>{try{t.r(r),t.d(r,{default:()=>m});var o=t(8732);t(82015);var a=t(12403),n=t(91353),i=t(82053),u=t(54131),l=t(44233),p=t.n(l),c=t(63487),d=t(88751),x=e([u,c]);[u,c]=x.then?(await x)():x;let m=e=>{let{isopen:r,manageDialog:t,userId:s,endpoint:l}=e,{t:x}=(0,d.useTranslation)("common"),m=()=>{t(!1)},h=async()=>{let e;("/users"===l?await c.A.remove(`${l}/${s}`):await c.A.post(`${l}`,{code:s}))&&(t(!1),p().push("/home"))};return(0,o.jsxs)(a.A,{show:r,onHide:m,children:[(0,o.jsx)("div",{className:"text-center p-2",children:(0,o.jsx)(i.FontAwesomeIcon,{icon:u.faExclamationTriangle,size:"5x",color:"indianRed",style:{background:"#d6deec",padding:"19px",borderRadius:"50%",width:"100px",height:"100px"}})}),(0,o.jsx)(a.A.Body,{children:(0,o.jsx)("b",{children:x("AreyousureyouwishtoleavetheKnowledgePlatform")})}),(0,o.jsxs)(a.A.Footer,{children:[(0,o.jsx)(n.A,{variant:"secondary",onClick:m,children:x("No")}),(0,o.jsx)(n.A,{variant:"danger",onClick:h,children:x("YesDeleteMe")})]})]})};s()}catch(e){s(e)}})},86842:e=>{e.exports=require("@restart/ui/SelectableContext")},87571:e=>{e.exports=require("dom-helpers/transitionEnd")},88396:(e,r,t)=>{t.a(e,async(e,s)=>{try{t.r(r),t.d(r,{default:()=>d});var o=t(8732),a=t(82015),n=t(12403),i=t(25169),u=t(59549),l=t(82601),p=t(88751),c=e([l]);l=(c.then?(await c)():c)[0];let d=({isOpen:e,manageClose:r,id:t})=>{let{t:s}=(0,p.useTranslation)("common"),c={consent1:!0,consent2:!0,consent3:!0,consent4:!0},[d,x]=(0,a.useState)(c),[m,h]=(0,a.useState)(!1),q=e=>{let{name:r,checked:t}=e.target;x(e=>({...e,[r]:t})),h(!m)},g=e=>{x(c),h(e)};return(0,o.jsx)(o.Fragment,{children:(0,o.jsxs)(n.A,{show:e,onHide:()=>{r(!1)},size:"xl",id:"main-content",className:"w-100",children:[(0,o.jsx)(n.A.Header,{closeButton:!0,children:(0,o.jsx)(n.A.Title,{children:s("declaration.title")})}),(0,o.jsx)(n.A.Body,{children:(0,o.jsxs)("div",{className:"p-3 w-100",children:[(0,o.jsx)(i.A,{variant:"danger",children:s("declaration.info")}),(0,o.jsx)(u.A.Check,{className:"pb-4",type:"checkbox",name:"consent1",onChange:q,checked:d.consent1,value:"consent1",label:s("declaration.consent1")}),(0,o.jsx)(u.A.Check,{className:"pb-4",name:"consent2",onChange:q,type:"checkbox",checked:d.consent2,value:"consent2",label:s("declaration.consent2")}),(0,o.jsx)(u.A.Check,{className:"pb-4",name:"consent3",onChange:q,type:"checkbox",checked:d.consent3,value:"consent3",label:s("declaration.consent3")}),(0,o.jsx)(u.A.Check,{className:"pb-4",type:"checkbox",name:"consent4",value:"consent4",label:s("declaration.consent4"),checked:d.consent4,onChange:q})]})}),(0,o.jsx)(l.default,{endpoint:"/users",userId:t||"",isopen:m,manageDialog:e=>g(e)})]})})};s()}catch(e){s(e)}})},88751:e=>{e.exports=require("next-i18next")},93787:e=>{e.exports=require("redux-persist")},94947:e=>{e.exports=require("@restart/hooks/useTimeout")},96196:e=>{e.exports=require("next-redux-wrapper")},98320:e=>{e.exports=require("@restart/hooks/useIsomorphicEffect")},99460:e=>{e.exports=require("dom-helpers/css")}};var r=require("../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[6089,9216,9616,2386],()=>t(76965));module.exports=s})();