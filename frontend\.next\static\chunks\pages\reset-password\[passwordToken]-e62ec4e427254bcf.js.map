{"version": 3, "file": "static/chunks/pages/reset-password/[passwordToken]-e62ec4e427254bcf.js", "mappings": "wNAuFO,IAAMA,EAAQ,CACnBC,WA1C4C,OAAC,MAC7CC,CAAI,CACJC,eAAa,UACbC,CAAQ,cACRC,CAAY,UACZC,CAAQ,CACT,GACO,QAAEC,CAAM,SAAEC,CAAO,CAAE,CAAGC,CAAAA,EAAAA,EAAAA,EAAAA,CAAgBA,GACtCC,EAAWF,CAAO,CAACN,EAAK,EAAIK,CAAM,CAACL,EAAK,CAGzBS,EAAAA,OAAa,CAAC,IAAO,OAAET,EAAK,EAAI,CAACA,EAAK,EAG3D,IAAMU,EAAoBD,EAAAA,QAAc,CAACE,GAAG,CAACP,EAAU,GACrD,EAAIK,cAAoB,CAACG,IAEnBC,IAF2B,KAxCnBC,CAAU,EAC1B,MAAwB,UAAjB,OAAOA,GAAgC,OAAVA,CACtC,EAwCmBF,EAAME,KAAK,EACfL,CADkB,CAClBA,YAAkB,CAACG,EAA6C,MACrEZ,EACA,GAAGY,EAAME,KAAK,GAIbF,GAGT,MACE,WAACG,MAAAA,WACC,UAACA,MAAAA,CAAIC,UAAU,uBACZN,IAEFF,GACC,UAACO,MAAAA,CAAIC,UAAU,oCACZb,GAAiB,kBAAOE,CAAM,CAACL,EAAK,CAAgBK,CAAM,CAACL,EAAK,CAAGiB,OAAOZ,CAAM,CAACL,GAAK,MAKjG,EAIEkB,UAhE0C,OAAC,IAAEC,CAAE,OAAEC,CAAK,CAAEC,OAAK,MAAErB,CAAI,UAAEsB,CAAQ,CAAE,GACzE,QAAEC,CAAM,eAAEC,CAAa,CAAE,CAAGjB,CAAAA,EAAAA,EAAAA,EAAAA,CAAgBA,GAC5CkB,EAAYzB,GAAQmB,EAE1B,MACE,UAACO,EAAAA,CAAIA,CAACC,KAAK,EACTC,KAAK,QACLT,GAAIA,EACJC,MAAOA,EACPC,MAAOA,EACPrB,KAAMyB,EACNI,QAASN,CAAM,CAACE,EAAU,GAAKJ,EAC/BnB,SAAU,IACRsB,EAAcC,EAAWK,EAAEC,MAAM,CAACV,KAAK,CACzC,EACAC,SAAUA,EACVU,MAAM,KAGZ,CA8CA,EAAE,ECzEcC,CAAAA,CACLC,EAAAA,EAAAA,CACEC,EAAAA,EAAAA,gDCnBN,IAAMC,EAAU,IAErB,IADIf,EACAgB,EAAcC,aAAaF,OAAO,CAACG,GACvC,GAAI,CACkB,MAAM,CAAtBF,IACFhB,EAAQmB,KAAKC,KAAK,CAACJ,EAAAA,CAEvB,CAAE,MAAOK,EAAO,CAAC,CACjB,OAAOrB,CACT,EAAE,EAEqB,KAErB,IADIsB,EACEC,EAAOR,EAAQ,gBACrB,GAAI,CACFO,EAAOH,KAAKC,KAAK,CAACG,EAAKD,IAAI,CAC7B,CAAE,MAAOD,EAAO,CAAC,CACjB,OAAOC,CACT,EAAE,iBCjBF,4CACA,kCACA,WACA,OAAe,EAAQ,KAAuD,CAC9E,EACA,SAFsB,wFC8BtB,IAAME,EAAwBC,CAAAA,EAAAA,EAAAA,UAAAA,CAAUA,CAA8C,CAAChC,EAAOiC,KAC5F,GAAM,UAAE3C,CAAQ,UAAE4C,CAAQ,cAAEC,CAAY,WAAEjC,CAAS,YAAEkC,CAAU,eAAEC,CAAa,CAAE,GAAGC,EAAM,CAAGtC,EAGtFuC,EAAmBC,EAAAA,EAAU,GAAGC,KAAK,CAAC,CAAC,GAE7C,MACE,UAACC,EAAAA,EAAMA,CAAAA,CACLL,cAAeA,GAAiB,CAAC,EACjCE,iBAAkBA,EAClBL,SAAU,CAACzB,EAA6BkC,KAEtC,IAAMC,EAAuB,CAC3BC,eAAgB,KAAO,EACvBC,gBAAiB,KAAO,EACxBC,cAAe,KACf9B,OAAQ,KACR+B,YAAa,IAAIC,MAAM,UACvBC,SAAS,EACTC,YAAY,EACZC,kBAAkB,EAClBC,WAAY,EACZC,WAAW,EACXC,UAAWC,KAAKC,GAAG,GACnB3C,KAAM,SACN4C,mBAAoB,KAAM,EAC1BC,qBAAsB,KAAM,EAC5BC,QAAS,KAAO,CAClB,EAEI1B,GAEFA,EAASU,EAAWnC,EAAQkC,CAFhB,CAIhB,EACC,GAAGL,CAAI,UAEP,GACC,UAAC1B,EAAAA,EAAIA,CAAAA,CACHqB,IAAKA,EACLC,SAAU2B,EAAYC,YAAY,CAClC3B,aAAcA,EACdjC,UAAWA,EACXkC,WAAYA,WAES,YAApB,OAAO9C,EAA0BA,EAASuE,GAAevE,KAKpE,GAEAyC,EAAsBgC,WAAW,CAAG,wBAEpC,MAAehC,qBAAqBA,EAAC,sFClF9B,IAAMX,EAAY,OAAC,MACxBlC,CAAI,IACJmB,CAAE,UACF2D,CAAQ,WACRC,CAAS,cACT5E,CAAY,UACZD,CAAQ,OACRmB,CAAK,IACL2D,CAAE,WACFC,CAAS,CACTC,MAAI,SACJC,CAAO,CACP,GAAGrE,EACC,GAuBJ,MACE,UAACsE,EAAAA,EAAKA,CAAAA,CAACpF,KAAMA,EAAMqF,SAtBHC,CAsBaD,GApB7B,IAAME,EAA2B,UAAf,OAAOD,EAAmBA,EAAMrE,OAAOqE,GAAO,WAChE,GAAiB,EAACA,GAA4B,IAA7B,GAAkBE,IAAI,EAAO,CAAC,CACtCrF,EAD0C,KAC1CA,EAAAA,KAAAA,EAAAA,EAAc4E,SAAAA,GAAa,EAA3B5E,uBAGL4E,GAAa,CAACA,EAAUO,GACnBnF,GADyB,IACzBA,EAAAA,KAAAA,EAAAA,EAAc4E,SAAAA,GAAa,EAA3B5E,cAGLgF,GAAWG,GAET,CAACG,CAFa,GACAC,OAAOP,GACdQ,IAAI,CAACL,GACPnF,GADa,IACbA,EAAAA,KAAAA,EAAAA,EAAcgF,OAAAA,GAAW,IAAzBhF,mBAKb,WAIK,OAAC,OAAEyF,CAAK,MAAEC,CAAI,CAAO,SACpB,iCACE,UAACnE,EAAAA,CAAIA,CAACoE,OAAO,EACV,GAAGF,CAAK,CACR,GAAG9E,CAAK,CACTK,GAAIA,EACJ6D,GAAIA,GAAM,QACVE,KAAMA,EACNa,UAAWF,EAAKvF,OAAO,EAAI,CAAC,CAACuF,EAAKnD,KAAK,CACvCxC,SAAU,IACR0F,EAAM1F,QAAQ,CAAC4B,GACX5B,GAAUA,EAAS4B,EACzB,EACAT,WAAiB2E,IAAV3E,EAAsBA,EAAQuE,EAAMvE,KAAK,GAEjDwE,EAAKvF,OAAO,EAAIuF,EAAKnD,KAAK,CACzB,UAAChB,EAAAA,CAAIA,CAACoE,OAAO,CAACG,QAAQ,EAACrE,KAAK,mBACzBiE,EAAKnD,KAAK,GAEX,UAKd,EAAE,EAIyB,OAAC,MAC1B1C,CAAI,IACJmB,CAAE,UACF2D,CAAQ,cACR3E,CAAY,UACZD,CAAQ,CACRmB,OAAK,UACLjB,CAAQ,CACR,GAAGU,EACC,GAUJ,MACE,UAACsE,EAAAA,EAAKA,CAAAA,CAACpF,KAAMA,EAAMqF,SATJ,CAScA,GAR7B,GAAIP,GAAa,EAACQ,GAAe,IAAhB,CAAQA,CAAQ,CAAC,CAChC,EADoC,IAC7BnF,OAAAA,EAAAA,KAAAA,EAAAA,EAAc4E,SAAS,GAAI,EAA3B5E,sBAIX,WAIK,OAAC,OAAEyF,CAAK,MAAEC,CAAI,CAAO,SACpB,iCACE,UAACnE,EAAAA,CAAIA,CAACoE,OAAO,EACXd,GAAG,SACF,GAAGY,CAAK,CACR,GAAG9E,CAAK,CACTK,GAAIA,EACJ4E,UAAWF,EAAKvF,OAAO,EAAI,CAAC,CAACuF,EAAKnD,KAAK,CACvCxC,SAAU,IACR0F,EAAM1F,QAAQ,CAAC4B,GACX5B,GAAUA,EAAS4B,EACzB,EACAT,WAAiB2E,IAAV3E,EAAsBA,EAAQuE,EAAMvE,KAAK,UAE/CjB,IAEFyF,EAAKvF,OAAO,EAAIuF,EAAKnD,KAAK,CACzB,UAAChB,EAAAA,CAAIA,CAACoE,OAAO,CAACG,QAAQ,EAACrE,KAAK,mBACzBiE,EAAKnD,KAAK,GAEX,UAKd,EAAE,sNCvGF,IAAMwD,EAAuB,CAC3B,uCAAwC,kDACxC,wCAAyC,yBACzC,kCAAmC,iCACnC,0BAA2B,qDAC3B,+BAAgC,6CAChC,4CAA6C,iDAC/C,EAoIA,EAAeC,CAAAA,EAAAA,EAAAA,EAAAA,CAAOA,CAAC,GAAWC,GAlIZ,IAGpB,GAAM,WA+H+CC,EAAC,EA/H9CC,CAAa,CAAE,CADRC,CAAAA,EAAAA,EAAAA,SAAAA,CAASA,GACSC,KAAK,CAChCC,EAAUC,CAAAA,EAAAA,EAAAA,MAAAA,CAAMA,CAAC,MACjB,CAACC,EAAaC,EAAe,CAAGC,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAC,IACzC,CAACC,EAAiBC,EAAmB,CAAGF,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAC,IACjD,CAACG,EAAYC,EAAc,CAAGJ,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,EAAC,GAEvCK,EAAe,IACG,YAAY,CAA9BpF,EAAEC,MAAM,CAAC/B,IAAI,EACf4G,EAAe9E,EAAEC,MAAM,CAACV,KAAK,EAGT,mBAAmB,CAArCS,EAAEC,MAAM,CAAC/B,IAAI,EACf+G,EAAmBjF,EAAEC,MAAM,CAACV,KAAK,CAErC,EAEM8F,EAAgB,MAAOrF,EAAQsF,KAEnC,GADAtF,EAAE6B,cAAc,GACZyD,EAASC,QAAQ,GAAKD,EAASN,eAAe,CAAE,CAClD,IAAMQ,EAAW,MAAMC,EAAAA,CAAUA,CAACC,IAAI,CAAC,wBAAyB,CAACC,iBAAkBnB,EAAeK,YAAaG,CAAe,GAC1HQ,GAAYA,EAASI,OAAO,EAAE,EAChCC,EAAKA,CAACD,OAAO,CAACxB,CAAe,CAACoB,EAASM,OAAO,CAAC,EAC/CC,IAAAA,IAAW,CAAC,WAERP,EAASQ,IAAI,EAAIR,EAASQ,IAAI,CAACF,OAAO,CACxCD,CAD0C,CAC1CA,EAAKA,CAACjF,KAAK,CAACwD,CAAe,CAACoB,EAASQ,IAAI,CAACF,OAAO,CAAC,EAElDD,EAAAA,EAAKA,CAACjF,KAAK,CAACwD,CAAe,CAACoB,EAASM,OAAO,CAAC,CAGnD,CACF,EAaA,MAPAG,CAAAA,EAAAA,EAAAA,SAAAA,CAASA,CAAC,KACR,IAAMpF,EAAOqF,CAAAA,EAAAA,EAAAA,CAAAA,CAAOA,GAChBrF,GAAQA,EAAKsF,QAAQ,EAAE,GACX,EAElB,EAAE,EAAE,EAGF,UAAClH,MAAAA,CAAIC,UAAU,2BACb,UAACD,MAAAA,CAAIC,UAAU,mBACb,UAACD,MAAAA,CAAIC,UAAU,qBACb,UAACD,MAAAA,CAAIC,UAAU,mBACb,UAACD,MAAAA,CAAIC,UAAU,iCACb,WAACD,MAAAA,CAAIC,UAAU,kCACb,UAACD,MAAAA,CAAIC,UAAU,qBACb,UAACkH,MAAAA,CAAIC,IAAI,2BAA2BC,IAAI,6BAE1C,WAACvF,EAAAA,CAAqBA,CAAAA,CAAC7B,UAAU,gBAAgBgC,SAAUmE,EAAepE,IAAK0D,YAC7E,UAAC1F,MAAAA,CAAIC,UAAU,yBACb,UAACqH,IAAIA,CAACC,KAAM,aAEV,UAACJ,MAAAA,CAAIC,IAAI,KAFNE,cAEyBD,IAAI,oCAIpC,UAACG,UAAAA,CAAQvH,UAAU,2BACfgG,EA0CI,UAACjG,MAAAA,UACL,WAACA,MAAAA,CAAIC,UAAU,yEACb,UAACwH,EAAAA,CAAeA,CAAAA,CAACC,KAAMC,EAAAA,GAAmBA,CAAEC,MAAM,UAAUC,KAAK,KAAK5H,UAAU,eAChF,UAAC6H,IAAAA,UAAE,6CACH,UAACR,IAAIA,CAACC,KAAK,IAAItD,GAAG,aAChB,WAAC8D,QADET,CACFS,CAAO9H,UAAU,8BAChB,UAACwH,EAAAA,CAAeA,CAAAA,CAACC,KAAMM,EAAAA,GAAiBA,CAAEJ,MAAM,QAAQC,KAAK,OAAM,mCAhD3D,+BAChB,WAAC7H,MAAAA,WACC,WAACA,MAAAA,CAAIC,UAAU,iBACb,UAACI,QAAAA,CAAM4H,QAAQ,oBAAW,aAC1B,UAAC9G,EAAAA,EAASA,CAAAA,CACRlC,KAAK,WACLmB,GAAG,WACHS,KAAK,WACLkD,QAAQ,IACRK,QAAQ,mBACRhF,aAAc,CACZ2E,SAAU,uBACVK,QAAS,sFACX,EACA9D,MAAOsF,EACPzG,SAAUgH,OAGd,WAACnG,MAAAA,CAAIC,UAAU,iBACb,UAACI,QAAAA,CAAM4H,QAAQ,2BAAkB,qBACjC,UAAC9G,EAAAA,EAASA,CAAAA,CACRlC,KAAK,kBACLmB,GAAG,kBACHS,KAAK,WACLkD,QAAQ,IACRC,UAvDF,CAuDakE,EAtD1B5H,GAASA,IAAUsF,EAuDJxG,aAAc,CACZ2E,SAAS,+BACTC,UAAW,yBACb,EACA1D,MAAOyF,EACP5G,SAAUgH,OAGd,UAACnG,MAAAA,CAAIC,UAAU,4BACb,UAACD,MAAAA,CAAIC,UAAU,mBACb,UAAC8H,SAAAA,CAAO9H,UAAU,oBAAoBY,KAAK,kBAAS,6CA0B9E", "sources": ["webpack://_N_E/./components/common/FormikRadio.tsx", "webpack://_N_E/./components/common/FormValidation.tsx", "webpack://_N_E/./shared/services/local-storage.ts", "webpack://_N_E/?9ce7", "webpack://_N_E/./components/common/ValidationFormWrapper.tsx", "webpack://_N_E/./components/common/FormikTextInput.tsx", "webpack://_N_E/./pages/reset-password/[passwordToken].tsx"], "sourcesContent": ["import React from 'react';\r\nimport { useFormikContext, Field } from 'formik';\r\nimport { Form } from 'react-bootstrap';\r\n\r\ninterface RadioItemProps {\r\n  id: string;\r\n  label: string;\r\n  value: string;\r\n  name?: string;\r\n  disabled?: boolean;\r\n}\r\n\r\ninterface RadioGroupProps {\r\n  name: string;\r\n  valueSelected: string;\r\n  onChange: (e: React.ChangeEvent<HTMLInputElement>) => void;\r\n  errorMessage?: string;\r\n  children: React.ReactNode;\r\n}\r\n\r\n// Type Guard to ensure child.props is an object\r\nfunction isObject(props: any): props is { [key: string]: any } {\r\n  return typeof props === 'object' && props !== null;\r\n}\r\n\r\nconst RadioItem: React.FC<RadioItemProps> = ({ id, label, value, name, disabled }) => {\r\n  const { values, setFieldValue } = useFormikContext<any>();\r\n  const fieldName = name || id;\r\n\r\n  return (\r\n    <Form.Check\r\n      type=\"radio\"\r\n      id={id}\r\n      label={label}\r\n      value={value}\r\n      name={fieldName}\r\n      checked={values[fieldName] === value}\r\n      onChange={(e) => {\r\n        setFieldValue(fieldName, e.target.value);\r\n      }}\r\n      disabled={disabled}\r\n      inline\r\n    />\r\n  );\r\n};\r\n\r\nconst RadioGroup: React.FC<RadioGroupProps> = ({\r\n  name,\r\n  valueSelected,\r\n  onChange,\r\n  errorMessage,\r\n  children,\r\n}) => {\r\n  const { errors, touched } = useFormikContext<any>();\r\n  const hasError = touched[name] && errors[name];\r\n\r\n  // Create a context to pass the name to RadioItems\r\n  const radioContext = React.useMemo(() => ({ name }), [name]);\r\n\r\n  // Clone children to pass the name, ensuring props are spread safely\r\n  const childrenWithProps = React.Children.map(children, (child) => {\r\n    if (React.isValidElement(child)) {\r\n      // Ensure child.props is an object before spreading\r\n      if (isObject(child.props)) {\r\n        return React.cloneElement(child as React.ReactElement<RadioItemProps>, {\r\n          name,\r\n          ...child.props, // Safely spread child.props\r\n        });\r\n      }\r\n    }\r\n    return child;\r\n  });\r\n\r\n  return (\r\n    <div>\r\n      <div className=\"radio-group\">\r\n        {childrenWithProps}\r\n      </div>\r\n      {hasError && (\r\n        <div className=\"invalid-feedback d-block\">\r\n          {errorMessage || (typeof errors[name] === 'string' ? errors[name] : String(errors[name]))}\r\n        </div>\r\n      )}\r\n    </div>\r\n  );\r\n};\r\n\r\nexport const Radio = {\r\n  RadioGroup,\r\n  RadioItem,\r\n};\r\n\r\nexport default Radio;\r\n", "// This file exports all the components needed to replace react-bootstrap4-form-validation\r\n// It serves as a drop-in replacement for the original library\r\n\r\nimport ValidationForm from './ValidationFormWrapper';\r\nimport { TextInput, SelectGroup } from './FormikTextInput';\r\nimport { Radio } from './FormikRadio';\r\n\r\n// Export all components\r\nexport {\r\n  ValidationForm,\r\n  TextInput,\r\n  SelectGroup,\r\n  Radio\r\n};\r\n\r\n// Also export as default for convenience\r\nexport default {\r\n  ValidationForm,\r\n  TextInput,\r\n  SelectGroup,\r\n  Radio\r\n};\r\n", "export const getItem = (key: string) => {\r\n  let value;\r\n  let stringValue = localStorage.getItem(key);\r\n  try {\r\n    if (stringValue !== null) {\r\n      value = JSON.parse(stringValue);\r\n    }\r\n  } catch (error) {}\r\n  return value;\r\n};\r\n\r\nexport const getUser = () => {\r\n  let user;\r\n  const root = getItem(\"persist:root\");\r\n  try {\r\n    user = JSON.parse(root.user);\r\n  } catch (error) {}\r\n  return user;\r\n};\r\n", "\n    (window.__NEXT_P = window.__NEXT_P || []).push([\n      \"/reset-password/[passwordToken]\",\n      function () {\n        return require(\"private-next-pages/reset-password/[passwordToken].tsx\");\n      }\n    ]);\n    if(module.hot) {\n      module.hot.dispose(function () {\n        window.__NEXT_P.push([\"/reset-password/[passwordToken]\"])\n      });\n    }\n  ", "import React, { forwardRef } from 'react';\r\nimport { Formik, Form, FormikProps, FormikHelpers } from 'formik';\r\nimport * as Yup from 'yup';\r\n\r\n// This is a wrapper component that replaces react-bootstrap4-form-validation with Formik\r\ninterface MockEvent {\r\n  preventDefault: () => void;\r\n  stopPropagation: () => void;\r\n  currentTarget: (EventTarget & Element) | null;\r\n  target: EventTarget | null;\r\n  nativeEvent: Event;\r\n  bubbles: boolean;\r\n  cancelable: boolean;\r\n  defaultPrevented: boolean;\r\n  eventPhase: number;\r\n  isTrusted: boolean;\r\n  timeStamp: number;\r\n  type: string;\r\n  isDefaultPrevented: () => boolean;\r\n  isPropagationStopped: () => boolean;\r\n  persist: () => void;\r\n}\r\n\r\ninterface ValidationFormWrapperProps {\r\n  children: React.ReactNode | ((formikProps: FormikProps<any>) => React.ReactNode);\r\n  onSubmit: (event: React.FormEvent | MockEvent, values?: Record<string, any>, actions?: FormikHelpers<Record<string, any>>) => void;\r\n  onErrorSubmit?: (errors: any) => void;\r\n  initialValues?: Record<string, any>;\r\n  enableReinitialize?: boolean;\r\n  autoComplete?: string;\r\n  className?: string;\r\n  onKeyPress?: (e: any) => void;\r\n}\r\n\r\nconst ValidationFormWrapper = forwardRef<HTMLFormElement, ValidationFormWrapperProps>((props, ref) => {\r\n  const { children, onSubmit, autoComplete, className, onKeyPress, initialValues, ...rest } = props;\r\n\r\n  // Create an empty validation schema by default\r\n  const validationSchema = Yup.object().shape({});\r\n\r\n  return (\r\n    <Formik\r\n      initialValues={initialValues || {}}\r\n      validationSchema={validationSchema}\r\n      onSubmit={(values: Record<string, any>, actions: FormikHelpers<Record<string, any>>) => {\r\n        // Create a mock event object with preventDefault method and currentTarget\r\n        const mockEvent: MockEvent = {\r\n          preventDefault: () => {},\r\n          stopPropagation: () => {},\r\n          currentTarget: null, // Set to null to avoid checkValidity errors\r\n          target: null,\r\n          nativeEvent: new Event('submit'),\r\n          bubbles: false,\r\n          cancelable: true,\r\n          defaultPrevented: false,\r\n          eventPhase: 0,\r\n          isTrusted: false,\r\n          timeStamp: Date.now(),\r\n          type: 'submit',\r\n          isDefaultPrevented: () => false,\r\n          isPropagationStopped: () => false,\r\n          persist: () => {}\r\n        };\r\n\r\n        if (onSubmit) {\r\n          // Pass the mock event object to maintain compatibility with the original code\r\n          onSubmit(mockEvent, values, actions);\r\n        }\r\n      }}\r\n      {...rest}\r\n    >\r\n      {(formikProps: FormikProps<any>) => (\r\n        <Form\r\n          ref={ref}\r\n          onSubmit={formikProps.handleSubmit}\r\n          autoComplete={autoComplete}\r\n          className={className}\r\n          onKeyPress={onKeyPress}\r\n        >\r\n          {typeof children === 'function' ? children(formikProps) : children}\r\n        </Form>\r\n      )}\r\n    </Formik>\r\n  );\r\n});\r\n\r\nValidationFormWrapper.displayName = 'ValidationFormWrapper';\r\n\r\nexport default ValidationFormWrapper;\r\n", "import React from 'react';\r\nimport { Form } from 'react-bootstrap';\r\nimport { Field, ErrorMessage, useField } from 'formik';\r\n\r\n// This component mimics the TextInput component from react-bootstrap4-form-validation\r\n// but uses Formik under the hood\r\nexport const TextInput = ({\r\n  name,\r\n  id,\r\n  required,\r\n  validator,\r\n  errorMessage,\r\n  onChange,\r\n  value,\r\n  as,\r\n  multiline,\r\n  rows,\r\n  pattern,\r\n  ...props\r\n}: any) => {\r\n  // Create a custom validation function that mimics the original validator\r\n  const validate = (val: any) => {\r\n    // Safely convert value to string and trim\r\n    const stringVal = typeof val === 'string' ? val : String(val || '');\r\n    if (required && (!val || stringVal.trim() === '')) {\r\n      return errorMessage?.validator || 'This field is required';\r\n    }\r\n\r\n    if (validator && !validator(val)) {\r\n      return errorMessage?.validator || 'Invalid value';\r\n    }\r\n\r\n    if (pattern && val) {\r\n      const regex = new RegExp(pattern);\r\n      if (!regex.test(val)) {\r\n        return errorMessage?.pattern || 'Invalid format';\r\n      }\r\n    }\r\n\r\n    return undefined;\r\n  };\r\n\r\n  return (\r\n    <Field name={name} validate={validate}>\r\n      {({ field, meta }: any) => (\r\n        <>\r\n          <Form.Control\r\n            {...field}\r\n            {...props}\r\n            id={id}\r\n            as={as || 'input'}\r\n            rows={rows}\r\n            isInvalid={meta.touched && !!meta.error}\r\n            onChange={(e: React.ChangeEvent<HTMLInputElement>) => {\r\n              field.onChange(e);\r\n              if (onChange) onChange(e);\r\n            }}\r\n            value={value !== undefined ? value : field.value}\r\n          />\r\n          {meta.touched && meta.error ? (\r\n            <Form.Control.Feedback type=\"invalid\">\r\n              {meta.error}\r\n            </Form.Control.Feedback>\r\n          ) : null}\r\n        </>\r\n      )}\r\n    </Field>\r\n  );\r\n};\r\n\r\n// This component mimics the SelectGroup component from react-bootstrap4-form-validation\r\n// but uses Formik under the hood\r\nexport const SelectGroup = ({\r\n  name,\r\n  id,\r\n  required,\r\n  errorMessage,\r\n  onChange,\r\n  value,\r\n  children,\r\n  ...props\r\n}: any) => {\r\n  // Create a custom validation function that mimics the original validator\r\n  const validate = (val: any) => {\r\n    if (required && (!val || val === '')) {\r\n      return errorMessage?.validator || 'This field is required';\r\n    }\r\n\r\n    return undefined;\r\n  };\r\n\r\n  return (\r\n    <Field name={name} validate={validate}>\r\n      {({ field, meta }: any) => (\r\n        <>\r\n          <Form.Control\r\n            as=\"select\"\r\n            {...field}\r\n            {...props}\r\n            id={id}\r\n            isInvalid={meta.touched && !!meta.error}\r\n            onChange={(e: React.ChangeEvent<HTMLSelectElement>) => {\r\n              field.onChange(e);\r\n              if (onChange) onChange(e);\r\n            }}\r\n            value={value !== undefined ? value : field.value}\r\n          >\r\n            {children}\r\n          </Form.Control>\r\n          {meta.touched && meta.error ? (\r\n            <Form.Control.Feedback type=\"invalid\">\r\n              {meta.error}\r\n            </Form.Control.Feedback>\r\n          ) : null}\r\n        </>\r\n      )}\r\n    </Field>\r\n  );\r\n};\r\n\r\n// Export both components as named exports and as a default object\r\nexport default {\r\n  TextInput,\r\n  SelectGroup\r\n};\r\n", "//Import Library\r\nimport Router, { useRouter } from 'next/router'\r\nimport { connect } from \"react-redux\";\r\nimport React, {ChangeEvent, useEffect, useRef, useState} from 'react';\r\nimport { TextInput } from \"../../components/common/FormValidation\";\r\nimport ValidationFormWrapper from '../../components/common/ValidationFormWrapper';\r\nimport toast from 'react-hot-toast';\r\nimport Link from \"next/link\";\r\nimport {faArrowCircleLeft, faExclamationCircle} from \"@fortawesome/free-solid-svg-icons\";\r\nimport {FontAwesomeIcon} from \"@fortawesome/react-fontawesome\";\r\n\r\n//Import services/components\r\nimport apiService from \"../../services/apiService\";\r\nimport { getUser } from '../../shared/services/local-storage';\r\n\r\nconst responseMessage: any = {\r\n  \"RESET_PASSWORD.CHANGE_PASSWORD_ERROR\": \"Unable to reset password. Contact Administrator\",\r\n  \"RESET_PASSWORD.WRONG_CURRENT_PASSWORD\": \"Wrong Current password\",\r\n  \"RESET_PASSWORD.PASSWORD_CHANGED\": \"Password Changed Successfully.\",\r\n  \"RESET_PASSWORD.NO_TOKEN\": \"Invalid Token. Please reset password and try again\",\r\n  \"RESET_PASSWORD.EXPIRED_TOKEN\": \"Token Expired. Please reset password again\",\r\n  \"RESET_PASSWORD.EXPIRY_RESET_HOURS_NOT_SET\": \"Unable to reset password. Contact Administrator\"\r\n}\r\n\r\nconst ResetPassword = (props: any) => {\r\n\r\n  const router = useRouter();\r\n  const { passwordToken } = router.query;\r\n  const formRef = useRef(null);\r\n  const [newPassword, setNewPassword] = useState(\"\");\r\n  const [confirmPassword, setConfirmPassword] = useState(\"\");\r\n  const [isLoggedIn, setIsLoggedIn] = useState(false);\r\n\r\n  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {\r\n    if (e.target.name === \"password\") {\r\n      setNewPassword(e.target.value);\r\n    }\r\n\r\n    if (e.target.name === \"confirmPassword\") {\r\n      setConfirmPassword(e.target.value);\r\n    }\r\n  }\r\n\r\n  const submitHandler = async (e: any, formData: any) => {\r\n    e.preventDefault();\r\n    if (formData.password === formData.confirmPassword) {\r\n      const response = await apiService.post('/email/reset-password', {newPasswordToken: passwordToken, newPassword: confirmPassword});\r\n      if (response && response.success) {\r\n        toast.success(responseMessage[response.message]);\r\n        Router.push('/login');\r\n      } else {\r\n        if (response.data && response.data.message) {\r\n          toast.error(responseMessage[response.data.message]);\r\n        } else {\r\n          toast.error(responseMessage[response.message]);\r\n        }\r\n      }\r\n    }\r\n  }\r\n\r\n  const matchPassword = (value: string) => {\r\n    return value && value === newPassword;\r\n  }\r\n\r\n  useEffect(() => {\r\n    const user = getUser();\r\n    if (user && user.username) {\r\n      setIsLoggedIn(true);\r\n    }\r\n  },[])\r\n\r\n  return (\r\n    <div className=\"loginContainer \">\r\n      <div className='section'>\r\n        <div className='container'>\r\n          <div className='columns'>\r\n            <div className='column  is-two-thirds'>\r\n              <div className='column reset-password'>\r\n                <div className=\"imgBanner\">\r\n                  <img src=\"/images/login-banner.jpg\" alt=\"RKI Login Banner Image\"/>\r\n                </div>\r\n                <ValidationFormWrapper className=\"formContainer\" onSubmit={submitHandler} ref={formRef}>\r\n                  <div className=\"logoContainer\">\r\n                    <Link href={'/'}>\r\n\r\n                      <img src=\"/images/logo.jpg\" alt=\"Rohert Koch Institut - Logo\"/>\r\n\r\n                    </Link>\r\n                  </div>\r\n                  <section className=\"fieldsContainer\">\r\n                    {!isLoggedIn ? (<>\r\n                    <div>\r\n                      <div className=\"mb-3\">\r\n                        <label htmlFor=\"password\">Password</label>\r\n                        <TextInput\r\n                          name=\"password\"\r\n                          id=\"password\"\r\n                          type=\"password\"\r\n                          required\r\n                          pattern=\"(?=.*[A-Z]).{8,}\"\r\n                          errorMessage={{\r\n                            required: \"Password is required\",\r\n                            pattern: \"Password should be at least 8 characters and contains at least one upper case letter\"\r\n                          }}\r\n                          value={newPassword}\r\n                          onChange={handleChange}\r\n                        />\r\n                      </div>\r\n                      <div className=\"mb-3\">\r\n                        <label htmlFor=\"confirmPassword\">Confirm Password</label>\r\n                        <TextInput\r\n                          name=\"confirmPassword\"\r\n                          id=\"confirmPassword\"\r\n                          type=\"password\"\r\n                          required\r\n                          validator={matchPassword}\r\n                          errorMessage={{\r\n                            required:\"Confirm password is required\",\r\n                            validator: \"Password does not match\"\r\n                          }}\r\n                          value={confirmPassword}\r\n                          onChange={handleChange}\r\n                        />\r\n                      </div>\r\n                      <div className='field is-grouped'>\r\n                        <div className='control'>\r\n                          <button className='button is-primary' type='submit'>\r\n                            Reset Password\r\n                          </button>\r\n                        </div>\r\n                      </div>\r\n                    </div>\r\n                    </>):(<div>\r\n                      <div className=\"d-flex flex-column justify-content-center align-items-center\">\r\n                        <FontAwesomeIcon icon={faExclamationCircle} color=\"#e8ba0d\" size=\"5x\" className=\"error-icon\"/>\r\n                        <p>Logged in user cannot use reset password</p>\r\n                        <Link href=\"/\" as=\"/\" >\r\n                          <button className=\"button is-primary\">\r\n                            <FontAwesomeIcon icon={faArrowCircleLeft} color=\"#ffff\" size=\"1x\"/> Back to RKI Dashboard\r\n                          </button>\r\n                        </Link>\r\n                      </div>\r\n                    </div>)}\r\n                  </section>\r\n                </ValidationFormWrapper>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n}\r\n\r\nexport default connect((state) => state)(ResetPassword);"], "names": ["Radio", "RadioGroup", "name", "valueSelected", "onChange", "errorMessage", "children", "errors", "touched", "useFormikContext", "<PERSON><PERSON><PERSON><PERSON>", "React", "childrenWithProps", "map", "child", "isObject", "props", "div", "className", "String", "RadioItem", "id", "label", "value", "disabled", "values", "setFieldValue", "fieldName", "Form", "Check", "type", "checked", "e", "target", "inline", "ValidationForm", "TextInput", "SelectGroup", "getItem", "stringValue", "localStorage", "key", "JSON", "parse", "error", "user", "root", "ValidationFormWrapper", "forwardRef", "ref", "onSubmit", "autoComplete", "onKeyPress", "initialValues", "rest", "validationSchema", "<PERSON><PERSON>", "shape", "<PERSON><PERSON>", "actions", "mockEvent", "preventDefault", "stopPropagation", "currentTarget", "nativeEvent", "Event", "bubbles", "cancelable", "defaultPrevented", "eventPhase", "isTrusted", "timeStamp", "Date", "now", "isDefaultPrevented", "isPropagationStopped", "persist", "formikProps", "handleSubmit", "displayName", "required", "validator", "as", "multiline", "rows", "pattern", "Field", "validate", "val", "stringVal", "trim", "regex", "RegExp", "test", "field", "meta", "Control", "isInvalid", "undefined", "<PERSON><PERSON><PERSON>", "responseMessage", "connect", "state", "ResetPassword", "passwordToken", "useRouter", "query", "formRef", "useRef", "newPassword", "setNewPassword", "useState", "confirmPassword", "setConfirmPassword", "isLoggedIn", "setIsLoggedIn", "handleChange", "<PERSON><PERSON><PERSON><PERSON>", "formData", "password", "response", "apiService", "post", "newPasswordToken", "success", "toast", "message", "Router", "data", "useEffect", "getUser", "username", "img", "src", "alt", "Link", "href", "section", "FontAwesomeIcon", "icon", "faExclamationCircle", "color", "size", "p", "button", "faArrowCircleLeft", "htmlFor", "matchPassword"], "sourceRoot": "", "ignoreList": []}