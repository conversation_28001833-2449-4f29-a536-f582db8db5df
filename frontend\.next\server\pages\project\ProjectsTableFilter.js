"use strict";(()=>{var e={};e.id=7214,e.ids=[636,3220,7214],e.modules={123:e=>{e.exports=require("dom-helpers/removeEventListener")},1332:e=>{e.exports=require("react-custom-scrollbars-2")},1428:e=>{e.exports=import("axios")},1680:e=>{e.exports=require("@restart/ui/utils")},1919:e=>{e.exports=require("react-transition-group/Transition")},3892:e=>{e.exports=require("classnames")},4048:e=>{e.exports=require("react-truncate")},6009:e=>{e.exports=require("dom-helpers/ownerDocument")},6952:e=>{e.exports=require("@restart/ui/Modal")},7374:e=>{e.exports=require("@restart/ui/Dropdown")},8732:e=>{e.exports=require("react/jsx-runtime")},9653:e=>{e.exports=require("@restart/ui/Overlay")},11242:e=>{e.exports=require("redux-persist/integration/react")},11688:e=>{e.exports=require("@restart/hooks/useWillUnmount")},12388:e=>{e.exports=require("dom-helpers/addEventListener")},13364:e=>{e.exports=require("redux-saga/effects")},14062:e=>{e.exports=import("react-redux")},14078:e=>{e.exports=import("swr")},14332:e=>{e.exports=require("uncontrollable")},16116:e=>{e.exports=require("invariant")},18622:e=>{e.exports=require("yup")},19121:e=>{e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},22326:e=>{e.exports=require("react-dom")},22541:e=>{e.exports=require("redux-persist/lib/storage")},25303:e=>{e.exports=require("@restart/ui/NavItem")},26324:e=>{e.exports=require("warning")},27825:e=>{e.exports=require("lodash")},27910:e=>{e.exports=require("stream")},28217:e=>{e.exports=require("@restart/ui/DropdownItem")},29021:e=>{e.exports=require("fs")},29294:e=>{e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},29628:e=>{e.exports=require("dom-helpers/addClass")},29825:e=>{e.exports=require("prop-types")},29841:e=>{e.exports=require("dom-helpers/hasClass")},30362:e=>{e.exports=require("@restart/ui/DropdownToggle")},33873:e=>{e.exports=require("path")},36653:e=>{e.exports=require("nprogress")},36955:e=>{e.exports=require("@restart/hooks/useBreakpoint")},37766:e=>{e.exports=require("@restart/hooks/useMergedRefs")},39756:e=>{e.exports=import("redux")},40051:e=>{e.exports=require("dom-helpers/removeClass")},40361:e=>{e.exports=require("next/dist/compiled/next-server/pages.runtime.prod.js")},42893:e=>{e.exports=import("react-hot-toast")},43294:e=>{e.exports=require("formik")},50009:e=>{e.exports=require("@restart/ui/DropdownMenu")},57664:e=>{e.exports=require("dom-helpers/scrollbarSize")},58928:e=>{e.exports=require("dom-helpers/canUseDOM")},59717:e=>{e.exports=require("@restart/ui/DropdownContext")},60560:e=>{e.exports=require("dom-helpers/contains")},63033:e=>{e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},63276:(e,r,t)=>{t.a(e,async(e,s)=>{try{t.r(r),t.d(r,{default:()=>q});var o=t(8732),a=t(82015),i=t(7082),u=t(83551),p=t(49481),n=t(84517),l=t(59549),x=t(88751),c=t(63487),d=e([c]);c=(d.then?(await d)():d)[0];let q=({filterText:e,onFilter:r,onFilterStatusChange:t,onClear:s,filterStatus:d})=>{let[q,m]=(0,a.useState)([]),{t:h}=(0,x.useTranslation)("common"),g=async e=>{let r=await c.A.get("/projectstatus",e);r&&Array.isArray(r.data)&&m(r.data)};return(0,a.useEffect)(()=>{g({query:{},sort:{title:"asc"}})},[]),(0,o.jsx)(i.A,{fluid:!0,className:"p-0",children:(0,o.jsxs)(u.A,{children:[(0,o.jsx)(p.A,{xs:6,className:"ps-0 align-self-end mb-3",children:(0,o.jsx)(n.A,{type:"text",className:"searchInput",placeholder:h("vspace.Search"),"aria-label":"Search",value:e,onChange:r})}),(0,o.jsx)(p.A,{children:(0,o.jsx)(l.A,{children:(0,o.jsxs)(l.A.Group,{as:u.A,controlId:"statusFilter",children:[(0,o.jsx)(l.A.Label,{column:!0,sm:"3",lg:"2",children:"Status"}),(0,o.jsx)(p.A,{className:"ps-0 pe-1",children:(0,o.jsxs)(n.A,{as:"select","aria-label":"Status",onChange:t,value:d,children:[(0,o.jsx)("option",{value:"",children:"All"}),q.map((e,r)=>(0,o.jsx)("option",{value:e._id,children:e.title},r))]})})]})})})]})})};s()}catch(e){s(e)}})},65447:e=>{e.exports=require("@restart/ui/Button")},67364:e=>{e.exports=require("@restart/hooks/useCallbackRef")},68455:e=>{e.exports=import("redux-saga")},69722:e=>{e.exports=require("es6-promise")},74075:e=>{e.exports=require("zlib")},74716:e=>{e.exports=require("moment")},74987:e=>{e.exports=require("@restart/ui/ModalManager")},78097:e=>{e.exports=require("next-redux-saga")},78634:e=>{e.exports=require("@restart/ui/Anchor")},80237:(e,r)=>{Object.defineProperty(r,"A",{enumerable:!0,get:function(){return t}});var t=function(e){return e.PAGES="PAGES",e.PAGES_API="PAGES_API",e.APP_PAGE="APP_PAGE",e.APP_ROUTE="APP_ROUTE",e.IMAGE="IMAGE",e}({})},81366:e=>{e.exports=require("redux-auth-wrapper/connectedAuthWrapper")},81413:(e,r)=>{Object.defineProperty(r,"M",{enumerable:!0,get:function(){return function e(r,t){return t in r?r[t]:"then"in r&&"function"==typeof r.then?r.then(r=>e(r,t)):"function"==typeof r&&"default"===t?r:void 0}}})},81521:e=>{e.exports=require("dom-helpers/querySelectorAll")},81895:e=>{e.exports=require("@restart/hooks/useEventCallback")},82015:e=>{e.exports=require("react")},86842:e=>{e.exports=require("@restart/ui/SelectableContext")},87571:e=>{e.exports=require("dom-helpers/transitionEnd")},88751:e=>{e.exports=require("next-i18next")},93787:e=>{e.exports=require("redux-persist")},94947:e=>{e.exports=require("@restart/hooks/useTimeout")},96196:e=>{e.exports=require("next-redux-wrapper")},98320:e=>{e.exports=require("@restart/hooks/useIsomorphicEffect")},99257:(e,r,t)=>{t.a(e,async(e,s)=>{try{t.r(r),t.d(r,{config:()=>h,default:()=>c,getServerSideProps:()=>m,getStaticPaths:()=>q,getStaticProps:()=>d,reportWebVitals:()=>g,routeModule:()=>v,unstable_getServerProps:()=>b,unstable_getServerSideProps:()=>f,unstable_getStaticParams:()=>S,unstable_getStaticPaths:()=>A,unstable_getStaticProps:()=>P});var o=t(63885),a=t(80237),i=t(81413),u=t(9616),p=t.n(u),n=t(72386),l=t(63276),x=e([n,l]);[n,l]=x.then?(await x)():x;let c=(0,i.M)(l,"default"),d=(0,i.M)(l,"getStaticProps"),q=(0,i.M)(l,"getStaticPaths"),m=(0,i.M)(l,"getServerSideProps"),h=(0,i.M)(l,"config"),g=(0,i.M)(l,"reportWebVitals"),P=(0,i.M)(l,"unstable_getStaticProps"),A=(0,i.M)(l,"unstable_getStaticPaths"),S=(0,i.M)(l,"unstable_getStaticParams"),b=(0,i.M)(l,"unstable_getServerProps"),f=(0,i.M)(l,"unstable_getServerSideProps"),v=new o.PagesRouteModule({definition:{kind:a.A.PAGES,page:"/project/ProjectsTableFilter",pathname:"/project/ProjectsTableFilter",bundlePath:"",filename:""},components:{App:n.default,Document:p()},userland:l});s()}catch(e){s(e)}})},99460:e=>{e.exports=require("dom-helpers/css")}};var r=require("../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[6089,9216,9616,2386],()=>t(99257));module.exports=s})();