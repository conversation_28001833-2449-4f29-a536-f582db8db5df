{"version": 3, "file": "static/chunks/pages/adminsettings/user/forms-5d58bbff4fb2da1f.js", "mappings": "wNAuFO,IAAMA,EAAQ,CACnBC,WA1C4C,OAAC,MAC7CC,CAAI,eACJC,CAAa,UACbC,CAAQ,CACRC,cAAY,UACZC,CAAQ,CACT,GACO,QAAEC,CAAM,SAAEC,CAAO,CAAE,CAAGC,CAAAA,EAAAA,EAAAA,EAAAA,CAAgBA,GACtCC,EAAWF,CAAO,CAACN,EAAK,EAAIK,CAAM,CAACL,EAAK,CAGzBS,EAAAA,OAAa,CAAC,IAAO,OAAET,EAAK,EAAI,CAACA,EAAK,EAG3D,IAAMU,EAAoBD,EAAAA,QAAc,CAACE,GAAG,CAACP,EAAU,GACrD,EAAIK,cAAoB,CAACG,IAEnBC,IAF2B,KAxCnBC,CAAU,EAC1B,MAAwB,UAAjB,OAAOA,GAAgC,OAAVA,CACtC,EAwCmBF,EAAME,KAAK,EACfL,CADkB,CAClBA,YAAkB,CAACG,EAA6C,MACrEZ,EACA,GAAGY,EAAME,KAAK,GAIbF,GAGT,MACE,WAACG,MAAAA,WACC,UAACA,MAAAA,CAAIC,UAAU,uBACZN,IAEFF,GACC,UAACO,MAAAA,CAAIC,UAAU,oCACZb,GAAiB,kBAAOE,CAAM,CAACL,EAAK,CAAgBK,CAAM,CAACL,EAAK,CAAGiB,OAAOZ,CAAM,CAACL,GAAK,MAKjG,EAIEkB,UAhE0C,OAAC,IAAEC,CAAE,OAAEC,CAAK,OAAEC,CAAK,CAAErB,MAAI,UAAEsB,CAAQ,CAAE,GACzE,QAAEC,CAAM,eAAEC,CAAa,CAAE,CAAGjB,CAAAA,EAAAA,EAAAA,EAAAA,CAAgBA,GAC5CkB,EAAYzB,GAAQmB,EAE1B,MACE,UAACO,EAAAA,CAAIA,CAACC,KAAK,EACTC,KAAK,QACLT,GAAIA,EACJC,MAAOA,EACPC,MAAOA,EACPrB,KAAMyB,EACNI,QAASN,CAAM,CAACE,EAAU,GAAKJ,EAC/BnB,SAAW4B,IACTN,EAAcC,EAAWK,EAAEC,MAAM,CAACV,KAAK,CACzC,EACAC,SAAUA,EACVU,MAAM,KAGZ,CA8CA,ECzEgBC,EAAAA,CAAAA,CACLC,EAAAA,EAAAA,CACEC,EAAAA,EAAAA,gGCeb,IAAMC,EAAwBC,CAAAA,EAAAA,EAAAA,UAAAA,CAAUA,CAA8C,CAACvB,EAAOwB,KAC5F,GAAM,UAAElC,CAAQ,UAAEmC,CAAQ,cAAEC,CAAY,WAAExB,CAAS,CAAEyB,YAAU,eAAEC,CAAa,CAAE,GAAGC,EAAM,CAAG7B,EAGtF8B,EAAmBC,EAAAA,EAAU,GAAGC,KAAK,CAAC,CAAC,GAE7C,MACE,UAACC,EAAAA,EAAMA,CAAAA,CACLL,cAAeA,GAAiB,CAAC,EACjCE,iBAAkBA,EAClBL,SAAU,CAAChB,EAA6ByB,KAEtC,IAAMC,EAAuB,CAC3BC,eAAgB,KAAO,EACvBC,gBAAiB,KAAO,EACxBC,cAAe,KACfrB,OAAQ,KACRsB,YAAa,IAAIC,MAAM,UACvBC,SAAS,EACTC,YAAY,EACZC,kBAAkB,EAClBC,WAAY,EACZC,WAAW,EACXC,UAAWC,KAAKC,GAAG,GACnBlC,KAAM,SACNmC,mBAAoB,KAAM,EAC1BC,qBAAsB,KAAM,EAC5BC,QAAS,KAAO,CAClB,EAEI1B,GAEFA,EAASU,EAAW1B,EAAQyB,CAFhB,CAIhB,EACC,GAAGL,CAAI,UAEP,GACC,UAACjB,EAAAA,EAAIA,CAAAA,CACHY,IAAKA,EACLC,SAAU2B,EAAYC,YAAY,CAClC3B,aAAcA,EACdxB,UAAWA,EACXyB,WAAYA,WAES,YAApB,OAAOrC,EAA0BA,EAAS8D,GAAe9D,KAKpE,GAEAgC,EAAsBgC,WAAW,CAAG,wBAEpC,MAAehC,mBCvFf,EDuFoCA,EAAC,ICvFrC,oCACA,4BACA,WACA,OAAe,EAAQ,KAAiD,CACxE,EACA,SAFsB,oGCEf,IAAMF,EAAY,OAAC,CACxBlC,MAAI,CACJmB,IAAE,UACFkD,CAAQ,WACRC,CAAS,cACTnE,CAAY,UACZD,CAAQ,OACRmB,CAAK,IACLkD,CAAE,CACFC,WAAS,MACTC,CAAI,SACJC,CAAO,CACP,GAAG5D,EACC,GAuBJ,MACE,UAAC6D,EAAAA,EAAKA,CAAAA,CAAC3E,KAAMA,EAAM4E,SAtBHC,CAsBaD,GApB7B,IAAME,EAAY,iBAAOD,EAAmBA,EAAM5D,OAAO4D,GAAO,WAChE,GAAiB,EAACA,GAA4B,IAA7B,CAAQC,EAAUC,IAAI,EAAO,CAAC,CACtC5E,EAD0C,KAC1CA,EAAAA,KAAAA,EAAAA,EAAcmE,SAAAA,GAAa,EAA3BnE,uBAGLmE,GAAa,CAACA,EAAUO,GACnB1E,GADyB,IACzBA,EAAAA,KAAAA,EAAAA,EAAcmE,SAAS,GAAI,EAA3BnE,cAGLuE,GAAWG,GAET,CADU,CADI,GACAG,OAAON,GACdO,IAAI,CAACJ,GACP1E,CAAAA,EADa,MACbA,KAAAA,EAAAA,EAAcuE,OAAAA,CAAdvE,EAAyB,uBAKtC,WAIK,OAAC,OAAE+E,CAAK,MAAEC,CAAI,CAAO,SACpB,iCACE,UAACzD,EAAAA,CAAIA,CAAC0D,OAAO,EACV,GAAGF,CAAK,CACR,GAAGpE,CAAK,CACTK,GAAIA,EACJoD,GAAIA,GAAM,QACVE,KAAMA,EACNY,UAAWF,EAAK7E,OAAO,EAAI,CAAC,CAAC6E,EAAKG,KAAK,CACvCpF,SAAU,IACRgF,EAAMhF,QAAQ,CAAC4B,GACX5B,GAAUA,EAAS4B,EACzB,EACAT,WAAiBkE,IAAVlE,EAAsBA,EAAQ6D,EAAM7D,KAAK,GAEjD8D,EAAK7E,OAAO,EAAI6E,EAAKG,KAAK,CACzB,UAAC5D,EAAAA,CAAIA,CAAC0D,OAAO,CAACI,QAAQ,EAAC5D,KAAK,mBACzBuD,EAAKG,KAAK,GAEX,UAKd,EAAE,EAIyB,OAAC,MAC1BtF,CAAI,IACJmB,CAAE,UACFkD,CAAQ,cACRlE,CAAY,CACZD,UAAQ,OACRmB,CAAK,UACLjB,CAAQ,CACR,GAAGU,EACC,GAUJ,MACE,UAAC6D,EAAAA,EAAKA,CAAAA,CAAC3E,KAAMA,EAAM4E,SATJ,CAScA,GAR7B,GAAIP,GAAa,EAACQ,GAAe,IAAhB,CAAQA,CAAQ,CAAC,CAChC,EADoC,IAC7B1E,OAAAA,EAAAA,KAAAA,EAAAA,EAAcmE,SAAAA,GAAa,EAA3BnE,sBAIX,WAIK,OAAC,OAAE+E,CAAK,CAAEC,MAAI,CAAO,SACpB,iCACE,UAACzD,EAAAA,CAAIA,CAAC0D,OAAO,EACXb,GAAG,SACF,GAAGW,CAAK,CACR,GAAGpE,CAAK,CACTK,GAAIA,EACJkE,UAAWF,EAAK7E,OAAO,EAAI,CAAC,CAAC6E,EAAKG,KAAK,CACvCpF,SAAU,IACRgF,EAAMhF,QAAQ,CAAC4B,GACX5B,GAAUA,EAAS4B,EACzB,EACAT,WAAiBkE,IAAVlE,EAAsBA,EAAQ6D,EAAM7D,KAAK,UAE/CjB,IAEF+E,EAAK7E,OAAO,EAAI6E,EAAKG,KAAK,CACzB,UAAC5D,EAAAA,CAAIA,CAAC0D,OAAO,CAACI,QAAQ,EAAC5D,KAAK,mBACzBuD,EAAKG,KAAK,GAEX,UAKd,EAAE", "sources": ["webpack://_N_E/./components/common/FormikRadio.tsx", "webpack://_N_E/./components/common/FormValidation.tsx", "webpack://_N_E/./components/common/ValidationFormWrapper.tsx", "webpack://_N_E/?f0ae", "webpack://_N_E/./components/common/FormikTextInput.tsx"], "sourcesContent": ["import React from 'react';\r\nimport { useFormikContext, Field } from 'formik';\r\nimport { Form } from 'react-bootstrap';\r\n\r\ninterface RadioItemProps {\r\n  id: string;\r\n  label: string;\r\n  value: string;\r\n  name?: string;\r\n  disabled?: boolean;\r\n}\r\n\r\ninterface RadioGroupProps {\r\n  name: string;\r\n  valueSelected: string;\r\n  onChange: (e: React.ChangeEvent<HTMLInputElement>) => void;\r\n  errorMessage?: string;\r\n  children: React.ReactNode;\r\n}\r\n\r\n// Type Guard to ensure child.props is an object\r\nfunction isObject(props: any): props is { [key: string]: any } {\r\n  return typeof props === 'object' && props !== null;\r\n}\r\n\r\nconst RadioItem: React.FC<RadioItemProps> = ({ id, label, value, name, disabled }) => {\r\n  const { values, setFieldValue } = useFormikContext<any>();\r\n  const fieldName = name || id;\r\n\r\n  return (\r\n    <Form.Check\r\n      type=\"radio\"\r\n      id={id}\r\n      label={label}\r\n      value={value}\r\n      name={fieldName}\r\n      checked={values[fieldName] === value}\r\n      onChange={(e) => {\r\n        setFieldValue(fieldName, e.target.value);\r\n      }}\r\n      disabled={disabled}\r\n      inline\r\n    />\r\n  );\r\n};\r\n\r\nconst RadioGroup: React.FC<RadioGroupProps> = ({\r\n  name,\r\n  valueSelected,\r\n  onChange,\r\n  errorMessage,\r\n  children,\r\n}) => {\r\n  const { errors, touched } = useFormikContext<any>();\r\n  const hasError = touched[name] && errors[name];\r\n\r\n  // Create a context to pass the name to RadioItems\r\n  const radioContext = React.useMemo(() => ({ name }), [name]);\r\n\r\n  // Clone children to pass the name, ensuring props are spread safely\r\n  const childrenWithProps = React.Children.map(children, (child) => {\r\n    if (React.isValidElement(child)) {\r\n      // Ensure child.props is an object before spreading\r\n      if (isObject(child.props)) {\r\n        return React.cloneElement(child as React.ReactElement<RadioItemProps>, {\r\n          name,\r\n          ...child.props, // Safely spread child.props\r\n        });\r\n      }\r\n    }\r\n    return child;\r\n  });\r\n\r\n  return (\r\n    <div>\r\n      <div className=\"radio-group\">\r\n        {childrenWithProps}\r\n      </div>\r\n      {hasError && (\r\n        <div className=\"invalid-feedback d-block\">\r\n          {errorMessage || (typeof errors[name] === 'string' ? errors[name] : String(errors[name]))}\r\n        </div>\r\n      )}\r\n    </div>\r\n  );\r\n};\r\n\r\nexport const Radio = {\r\n  RadioGroup,\r\n  RadioItem,\r\n};\r\n\r\nexport default Radio;\r\n", "// This file exports all the components needed to replace react-bootstrap4-form-validation\r\n// It serves as a drop-in replacement for the original library\r\n\r\nimport ValidationForm from './ValidationFormWrapper';\r\nimport { TextInput, SelectGroup } from './FormikTextInput';\r\nimport { Radio } from './FormikRadio';\r\n\r\n// Export all components\r\nexport {\r\n  ValidationForm,\r\n  TextInput,\r\n  SelectGroup,\r\n  Radio\r\n};\r\n\r\n// Also export as default for convenience\r\nexport default {\r\n  ValidationForm,\r\n  TextInput,\r\n  SelectGroup,\r\n  Radio\r\n};\r\n", "import React, { forwardRef } from 'react';\r\nimport { Formik, Form, FormikProps, FormikHelpers } from 'formik';\r\nimport * as Yup from 'yup';\r\n\r\n// This is a wrapper component that replaces react-bootstrap4-form-validation with Formik\r\ninterface MockEvent {\r\n  preventDefault: () => void;\r\n  stopPropagation: () => void;\r\n  currentTarget: (EventTarget & Element) | null;\r\n  target: EventTarget | null;\r\n  nativeEvent: Event;\r\n  bubbles: boolean;\r\n  cancelable: boolean;\r\n  defaultPrevented: boolean;\r\n  eventPhase: number;\r\n  isTrusted: boolean;\r\n  timeStamp: number;\r\n  type: string;\r\n  isDefaultPrevented: () => boolean;\r\n  isPropagationStopped: () => boolean;\r\n  persist: () => void;\r\n}\r\n\r\ninterface ValidationFormWrapperProps {\r\n  children: React.ReactNode | ((formikProps: FormikProps<any>) => React.ReactNode);\r\n  onSubmit: (event: React.FormEvent | MockEvent, values?: Record<string, any>, actions?: FormikHelpers<Record<string, any>>) => void;\r\n  onErrorSubmit?: (errors: any) => void;\r\n  initialValues?: Record<string, any>;\r\n  enableReinitialize?: boolean;\r\n  autoComplete?: string;\r\n  className?: string;\r\n  onKeyPress?: (e: any) => void;\r\n}\r\n\r\nconst ValidationFormWrapper = forwardRef<HTMLFormElement, ValidationFormWrapperProps>((props, ref) => {\r\n  const { children, onSubmit, autoComplete, className, onKeyPress, initialValues, ...rest } = props;\r\n\r\n  // Create an empty validation schema by default\r\n  const validationSchema = Yup.object().shape({});\r\n\r\n  return (\r\n    <Formik\r\n      initialValues={initialValues || {}}\r\n      validationSchema={validationSchema}\r\n      onSubmit={(values: Record<string, any>, actions: FormikHelpers<Record<string, any>>) => {\r\n        // Create a mock event object with preventDefault method and currentTarget\r\n        const mockEvent: MockEvent = {\r\n          preventDefault: () => {},\r\n          stopPropagation: () => {},\r\n          currentTarget: null, // Set to null to avoid checkValidity errors\r\n          target: null,\r\n          nativeEvent: new Event('submit'),\r\n          bubbles: false,\r\n          cancelable: true,\r\n          defaultPrevented: false,\r\n          eventPhase: 0,\r\n          isTrusted: false,\r\n          timeStamp: Date.now(),\r\n          type: 'submit',\r\n          isDefaultPrevented: () => false,\r\n          isPropagationStopped: () => false,\r\n          persist: () => {}\r\n        };\r\n\r\n        if (onSubmit) {\r\n          // Pass the mock event object to maintain compatibility with the original code\r\n          onSubmit(mockEvent, values, actions);\r\n        }\r\n      }}\r\n      {...rest}\r\n    >\r\n      {(formikProps: FormikProps<any>) => (\r\n        <Form\r\n          ref={ref}\r\n          onSubmit={formikProps.handleSubmit}\r\n          autoComplete={autoComplete}\r\n          className={className}\r\n          onKeyPress={onKeyPress}\r\n        >\r\n          {typeof children === 'function' ? children(formikProps) : children}\r\n        </Form>\r\n      )}\r\n    </Formik>\r\n  );\r\n});\r\n\r\nValidationFormWrapper.displayName = 'ValidationFormWrapper';\r\n\r\nexport default ValidationFormWrapper;\r\n", "\n    (window.__NEXT_P = window.__NEXT_P || []).push([\n      \"/adminsettings/user/forms\",\n      function () {\n        return require(\"private-next-pages/adminsettings/user/forms.tsx\");\n      }\n    ]);\n    if(module.hot) {\n      module.hot.dispose(function () {\n        window.__NEXT_P.push([\"/adminsettings/user/forms\"])\n      });\n    }\n  ", "import React from 'react';\r\nimport { Form } from 'react-bootstrap';\r\nimport { Field, ErrorMessage, useField } from 'formik';\r\n\r\n// This component mimics the TextInput component from react-bootstrap4-form-validation\r\n// but uses Formik under the hood\r\nexport const TextInput = ({\r\n  name,\r\n  id,\r\n  required,\r\n  validator,\r\n  errorMessage,\r\n  onChange,\r\n  value,\r\n  as,\r\n  multiline,\r\n  rows,\r\n  pattern,\r\n  ...props\r\n}: any) => {\r\n  // Create a custom validation function that mimics the original validator\r\n  const validate = (val: any) => {\r\n    // Safely convert value to string and trim\r\n    const stringVal = typeof val === 'string' ? val : String(val || '');\r\n    if (required && (!val || stringVal.trim() === '')) {\r\n      return errorMessage?.validator || 'This field is required';\r\n    }\r\n\r\n    if (validator && !validator(val)) {\r\n      return errorMessage?.validator || 'Invalid value';\r\n    }\r\n\r\n    if (pattern && val) {\r\n      const regex = new RegExp(pattern);\r\n      if (!regex.test(val)) {\r\n        return errorMessage?.pattern || 'Invalid format';\r\n      }\r\n    }\r\n\r\n    return undefined;\r\n  };\r\n\r\n  return (\r\n    <Field name={name} validate={validate}>\r\n      {({ field, meta }: any) => (\r\n        <>\r\n          <Form.Control\r\n            {...field}\r\n            {...props}\r\n            id={id}\r\n            as={as || 'input'}\r\n            rows={rows}\r\n            isInvalid={meta.touched && !!meta.error}\r\n            onChange={(e: React.ChangeEvent<HTMLInputElement>) => {\r\n              field.onChange(e);\r\n              if (onChange) onChange(e);\r\n            }}\r\n            value={value !== undefined ? value : field.value}\r\n          />\r\n          {meta.touched && meta.error ? (\r\n            <Form.Control.Feedback type=\"invalid\">\r\n              {meta.error}\r\n            </Form.Control.Feedback>\r\n          ) : null}\r\n        </>\r\n      )}\r\n    </Field>\r\n  );\r\n};\r\n\r\n// This component mimics the SelectGroup component from react-bootstrap4-form-validation\r\n// but uses Formik under the hood\r\nexport const SelectGroup = ({\r\n  name,\r\n  id,\r\n  required,\r\n  errorMessage,\r\n  onChange,\r\n  value,\r\n  children,\r\n  ...props\r\n}: any) => {\r\n  // Create a custom validation function that mimics the original validator\r\n  const validate = (val: any) => {\r\n    if (required && (!val || val === '')) {\r\n      return errorMessage?.validator || 'This field is required';\r\n    }\r\n\r\n    return undefined;\r\n  };\r\n\r\n  return (\r\n    <Field name={name} validate={validate}>\r\n      {({ field, meta }: any) => (\r\n        <>\r\n          <Form.Control\r\n            as=\"select\"\r\n            {...field}\r\n            {...props}\r\n            id={id}\r\n            isInvalid={meta.touched && !!meta.error}\r\n            onChange={(e: React.ChangeEvent<HTMLSelectElement>) => {\r\n              field.onChange(e);\r\n              if (onChange) onChange(e);\r\n            }}\r\n            value={value !== undefined ? value : field.value}\r\n          >\r\n            {children}\r\n          </Form.Control>\r\n          {meta.touched && meta.error ? (\r\n            <Form.Control.Feedback type=\"invalid\">\r\n              {meta.error}\r\n            </Form.Control.Feedback>\r\n          ) : null}\r\n        </>\r\n      )}\r\n    </Field>\r\n  );\r\n};\r\n\r\n// Export both components as named exports and as a default object\r\nexport default {\r\n  TextInput,\r\n  SelectGroup\r\n};\r\n"], "names": ["Radio", "RadioGroup", "name", "valueSelected", "onChange", "errorMessage", "children", "errors", "touched", "useFormikContext", "<PERSON><PERSON><PERSON><PERSON>", "React", "childrenWithProps", "map", "child", "isObject", "props", "div", "className", "String", "RadioItem", "id", "label", "value", "disabled", "values", "setFieldValue", "fieldName", "Form", "Check", "type", "checked", "e", "target", "inline", "ValidationForm", "TextInput", "SelectGroup", "ValidationFormWrapper", "forwardRef", "ref", "onSubmit", "autoComplete", "onKeyPress", "initialValues", "rest", "validationSchema", "<PERSON><PERSON>", "shape", "<PERSON><PERSON>", "actions", "mockEvent", "preventDefault", "stopPropagation", "currentTarget", "nativeEvent", "Event", "bubbles", "cancelable", "defaultPrevented", "eventPhase", "isTrusted", "timeStamp", "Date", "now", "isDefaultPrevented", "isPropagationStopped", "persist", "formikProps", "handleSubmit", "displayName", "required", "validator", "as", "multiline", "rows", "pattern", "Field", "validate", "val", "stringVal", "trim", "RegExp", "test", "field", "meta", "Control", "isInvalid", "error", "undefined", "<PERSON><PERSON><PERSON>"], "sourceRoot": "", "ignoreList": []}