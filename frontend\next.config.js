const path = require('path');
const { i18n } = require('./next-i18next.config');

/** @type {import('next').NextConfig} */
const nextConfig = {
  reactStrictMode: false, // Set to true for development to catch issues
  // swcMinify is now default and deprecated option removed
  // experimental.appDir is deprecated - Pages Router is default when no app directory exists
  transpilePackages: ['react-tweet'], // Fix for react-tweet CSS import issue
  i18n,
  sassOptions: {
    includePaths: [path.join(__dirname, 'styles')]
  },
  env: {
    API_SERVER: process.env.API_SERVER || 'http://localhost:3000/api',
    MAP_KEY: process.env.MAP_KEY || '',
    READ_MORE_LENGTH: '255',
    SEARCH_DEBOUNCE_TIME: process.env.SEARCH_DEBOUNCE_TIME || '300',
    UPLOAD_LIMIT: "20971520",     //In Bytes
    INFINITE_SCROLL_TIME: '300',
    HOME_PAGE: process.env.HOME_PAGE || '/',
    ADAPPT_HOME_PAGE: process.env.ADAPPT_HOME_PAGE || '/'
  },
  // Ensure images from external domains can be displayed
  images: {
    domains: [],
  },
  // Webpack configuration for better performance
  webpack: (config, { dev, isServer }) => {
    // Optimize for development
    if (dev) {
      config.watchOptions = {
        poll: 1000,
        aggregateTimeout: 300,
        ignored: ['**/node_modules', '**/.git', '**/.next']
      };

      // Reduce bundle size in development
      config.optimization = {
        ...config.optimization,
        removeAvailableModules: false,
        removeEmptyChunks: false,
        splitChunks: false,
      };
    }

    return config;
  },
  // Only generate source maps in development
  productionBrowserSourceMaps: false,

  // Optimize for development performance
  turbopack: {
    rules: {
      '*.svg': {
        loaders: ['@svgr/webpack'],
        as: '*.js',
      },
    },
  },
};

module.exports = nextConfig;
