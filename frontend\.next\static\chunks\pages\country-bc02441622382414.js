(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[286],{5422:(e,t,a)=>{"use strict";a.d(t,{E:()=>n,M:()=>r});let r=["A","B","C","D","E","F","G","H","I","J","K","L","M","N","O","P","Q","R","S","T","U","V","W","X","Y","Z","Alle"],n=["A","B","C","D","E","F","G","H","I","J","K","L","M","N","O","P","Q","R","S","T","U","V","W","X","Y","Z","All"]},15641:(e,t,a)=>{"use strict";a.d(t,{A:()=>i});var r=a(37876);a(14232);var n=a(62945);let i=e=>{let{name:t="Marker",id:a="",countryId:i="",type:l,icon:s,position:o,onClick:c,title:p,draggable:u=!1}=e;return o&&"number"==typeof o.lat&&"number"==typeof o.lng?(0,r.jsx)(n.pH,{position:o,icon:s,title:p||t,draggable:u,onClick:e=>{c&&c({name:t,id:a,countryId:i,type:l,position:o},{position:o,getPosition:()=>o},e)}}):null}},19376:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>l});var r=a(37876),n=a(5422),i=a(31753);let l=e=>{let{selectedAlpha:t,setselectedAlpha:a}=e,{i18n:l}=(0,i.Bd)("common"),s="en"==l.language?n.E:n.M;return(0,r.jsx)("div",{className:"alphabetContainer",children:(0,r.jsx)("ul",{children:s.map((e,n)=>(0,r.jsx)("li",{children:(0,r.jsx)("a",{onClick:()=>a(e),className:"".concat(t==e?"active":null),children:e})},n))})})}},26872:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>p});var r=a(37876),n=a(48230),i=a.n(n),l=a(91238),s=a.n(l),o=a(31753);let c=e=>{let{t}=(0,o.Bd)("common"),{countries:a,setActivePage:n}=e;return(0,r.jsxs)("div",{children:[(0,r.jsx)("div",{className:"alphabetLists",children:a&&a.data&&a.data.length>0?(0,r.jsx)("ul",{children:a.data.map((e,t)=>(0,r.jsx)("li",{className:"clearfix",children:(0,r.jsx)(i(),{href:"/country/[...routes]",as:"/country/show/".concat(e._id),children:(0,r.jsx)("div",{children:e.title})})},t))}):t("NoCountriesfound")}),a&&a.data?(0,r.jsx)("div",{className:"countries-pagination",children:(0,r.jsx)(s(),{pageCount:Math.ceil(a.totalCount/a.limit),pageRangeDisplayed:5,marginPagesDisplayed:2,onPageChange:e=>{n(e.selected+1)},forcePage:a.page-1,containerClassName:"pagination",pageClassName:"page-item",pageLinkClassName:"page-link",previousClassName:"page-item",previousLinkClassName:"page-link",nextClassName:"page-item",nextLinkClassName:"page-link",activeClassName:"active",disabledClassName:"disabled",previousLabel:"‹",nextLabel:"›"})}):null]})};c.defaultProps={countries:{page:1,limit:10,totalCount:10,data:[]}};let p=c},38158:(e,t,a)=>{"use strict";a.r(t),a.d(t,{__N_SSG:()=>m,default:()=>h});var r=a(37876),n=a(14232),i=a(49589),l=a(56970),s=a(37784),o=a(31777),c=a(98854),p=a(19376),u=a(26872),d=a(69600),g=a(69438),f=a(31753),y=a(53718),m=!0;let h=(0,o.Ng)(e=>e)(e=>{let{t,i18n:a}=(0,f.Bd)("common"),o="de"===a.language?{title_de:"asc"}:{title:"asc"},m=t("All"),[h,v]=(0,n.useState)(m),[b,C]=(0,n.useState)([]),[k,x]=(0,n.useState)({}),[P,N]=(0,n.useState)(1),T={sort:o,limit:48,page:P,query:{},select:"-health_profile -security_advice -created_at -updated_at"},L=async function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:T;if(0===b.length)return void x({data:[],totalCount:0,totalPages:0,page:1,limit:48});t="All"===h||"Alle"===h?{...t,query:{world_region:b}}:{...t,page:1,query:{languageCode:e,first_letter:h,world_region:b}};let a=await y.A.get("/country",{...t,languageCode:e});a&&a.data&&(console.log("response.data",a.data),console.log("response.data",a),x(a))};return(0,n.useEffect)(()=>{L(t("language"),T)},[h,b,P,t("language")]),(0,r.jsxs)(i.A,{fluid:!0,className:"p-0",children:[(0,r.jsx)(l.A,{children:(0,r.jsx)(s.A,{md:12,children:(0,r.jsx)(d.A,{title:t("menu.countries")})})}),(0,r.jsx)(l.A,{children:(0,r.jsx)(s.A,{md:12,children:(0,r.jsx)(c.default,{countries:k})})}),(0,r.jsx)(l.A,{children:(0,r.jsx)(s.A,{md:12,children:(0,r.jsx)(g.A,{filtreg:C,selectedRegions:b,regionHandler:C})})}),(0,r.jsx)(l.A,{children:(0,r.jsx)(s.A,{md:12,children:(0,r.jsx)(p.default,{selectedAlpha:h,setselectedAlpha:v})})}),(0,r.jsx)(l.A,{children:(0,r.jsx)(s.A,{md:12,children:(0,r.jsx)(u.default,{setActivePage:N,countries:k})})})]})})},66619:(e,t,a)=>{"use strict";a.d(t,{A:()=>g});var r=a(37876);a(14232);var n=a(62945);let i=e=>{let{position:t,onCloseClick:a,children:i}=e;return(0,r.jsx)(n.Fu,{position:t,onCloseClick:a,children:(0,r.jsx)("div",{children:i})})},l="labels.text.fill",s="labels.text.stroke",o="road.highway",c="geometry.stroke",p=[{elementType:"geometry",stylers:[{color:"#1d2c4d"}]},{elementType:l,stylers:[{color:"#8ec3b9"}]},{elementType:s,stylers:[{color:"#1a3646"}]},{featureType:"administrative",elementType:"geometry",stylers:[{visibility:"off"}]},{featureType:"administrative.country",elementType:c,stylers:[{color:"#4b6878"}]},{featureType:"administrative.land_parcel",elementType:"labels",stylers:[{visibility:"off"}]},{featureType:"administrative.land_parcel",elementType:l,stylers:[{color:"#64779e"}]},{featureType:"administrative.province",elementType:c,stylers:[{color:"#4b6878"}]},{featureType:"landscape.man_made",elementType:c,stylers:[{color:"#334e87"}]},{featureType:"landscape.natural",elementType:"geometry",stylers:[{color:"#023e58"}]},{featureType:"poi",stylers:[{visibility:"off"}]},{featureType:"poi",elementType:"geometry",stylers:[{color:"#283d6a"}]},{featureType:"poi",elementType:"labels.text",stylers:[{visibility:"off"}]},{featureType:"poi",elementType:l,stylers:[{color:"#6f9ba5"}]},{featureType:"poi",elementType:s,stylers:[{color:"#1d2c4d"}]},{featureType:"poi.park",elementType:"geometry.fill",stylers:[{color:"#023e58"}]},{featureType:"poi.park",elementType:l,stylers:[{color:"#3C7680"}]},{featureType:"road",stylers:[{visibility:"off"}]},{featureType:"road",elementType:"geometry",stylers:[{color:"#304a7d"}]},{featureType:"road",elementType:"labels.icon",stylers:[{visibility:"off"}]},{featureType:"road",elementType:l,stylers:[{color:"#98a5be"}]},{featureType:"road",elementType:s,stylers:[{color:"#1d2c4d"}]},{featureType:o,elementType:"geometry",stylers:[{color:"#2c6675"}]},{featureType:o,elementType:c,stylers:[{color:"#255763"}]},{featureType:o,elementType:l,stylers:[{color:"#b0d5ce"}]},{featureType:o,elementType:s,stylers:[{color:"#023e58"}]},{featureType:"road.local",elementType:"labels",stylers:[{visibility:"off"}]},{featureType:"transit",stylers:[{visibility:"off"}]},{featureType:"transit",elementType:l,stylers:[{color:"#98a5be"}]},{featureType:"transit",elementType:s,stylers:[{color:"#1d2c4d"}]},{featureType:"transit.line",elementType:"geometry.fill",stylers:[{color:"#283d6a"}]},{featureType:"transit.station",elementType:"geometry",stylers:[{color:"#3a4762"}]},{featureType:"water",elementType:"geometry",stylers:[{color:"#0e1626"}]},{featureType:"water",elementType:l,stylers:[{color:"#4e6d70"}]}];var u=a(89099),d=a(55316);let g=e=>{let{markerInfo:t,activeMarker:a,initialCenter:l,children:s,height:o=300,width:c="114%",language:g,zoom:f=1,minZoom:y=1,onClose:m}=e,{locale:h}=(0,u.useRouter)(),{isLoaded:v,loadError:b}=(0,d._)();return b?(0,r.jsx)("div",{children:"Error loading maps"}):v?(0,r.jsx)("div",{className:"map-container",children:(0,r.jsx)("div",{className:"mapprint",style:{width:c,height:o,position:"relative"},children:(0,r.jsxs)(n.u6,{mapContainerStyle:{width:c,height:"number"==typeof o?"".concat(o,"px"):o},center:l||{lat:52.520017,lng:13.404195},zoom:f,onLoad:e=>{e.setOptions({styles:p})},options:{minZoom:y,draggable:!0,keyboardShortcuts:!1,streetViewControl:!1,panControl:!1,clickableIcons:!1,mapTypeControl:!1,fullscreenControl:!0},children:[s,t&&a&&a.getPosition&&(0,r.jsx)(i,{position:a.getPosition(),onCloseClick:()=>{console.log("close click"),null==m||m()},children:t})]})})}):(0,r.jsx)("div",{children:"Loading Maps..."})}},69438:(e,t,a)=>{"use strict";a.d(t,{A:()=>d});var r=a(37876),n=a(14232),i=a(82851),l=a.n(i),s=a(29504),o=a(60282),c=a(53718),p=a(31753);function u(e){let{filtreg:t}=e,[a,i]=(0,n.useState)(!0),[u,d]=(0,n.useState)([]),[g,f]=(0,n.useState)([]),{t:y}=(0,p.Bd)("common"),m={query:{},limit:"~",sort:{title:"asc"}},h=async e=>{let a=await c.A.get("/worldregion",e);if(a&&Array.isArray(a.data)){let e=[],r=[];l().each(a.data,(t,a)=>{let n={...t,isChecked:!0};e.push(n),r.push(t._id)}),t(r),f(r),d(e)}};(0,n.useEffect)(()=>{h(m)},[]);let v=e=>{let a=[...u],r=[...g];a.forEach((t,n)=>{t.code===e.target.id&&(a[n].isChecked=e.target.checked,e.target.checked?r.push(t._id):r=r.filter(e=>e!==t._id))}),f(r),t(r),i(!1),d(a)};return(0,r.jsxs)("div",{className:"regions-multi-checkboxes",children:[(0,r.jsx)(s.A.Check,{type:"checkbox",id:"all",label:y("AllRegions"),checked:a,onChange:e=>{let a=u.map(t=>({...t,isChecked:e.target.checked})),r=[];e.target.checked&&(r=a.map(e=>e._id)),t(r),f(r),i(e.target.checked),d(a)}}),u.map((e,t)=>(0,r.jsx)(s.A.Check,{type:"checkbox",id:e.code,label:e.title,value:e.code,onChange:v,checked:u[t].isChecked},t)),(0,r.jsx)(o.A,{onClick:()=>{let e=u.map(e=>({...e,isChecked:!1}));f([]),i(!1),d(e),t([])},className:"btn-plain ps-2",children:y("ClearAll")})]})}u.defaultProps={filtreg:()=>{}};let d=u},69600:(e,t,a)=>{"use strict";a.d(t,{A:()=>n});var r=a(37876);function n(e){return(0,r.jsx)("h2",{className:"page-heading",children:e.title})}},81081:(e,t,a)=>{(window.__NEXT_P=window.__NEXT_P||[]).push(["/country",function(){return a(38158)}])},91238:function(e,t,a){let r;r=a(14232),e.exports=(()=>{var e={703:(e,t,a)=>{"use strict";var r=a(414);function n(){}function i(){}i.resetWarningCache=n,e.exports=function(){function e(e,t,a,n,i,l){if(l!==r){var s=Error("Calling PropTypes validators directly is not supported by the `prop-types` package. Use PropTypes.checkPropTypes() to call them. Read more at http://fb.me/use-check-prop-types");throw s.name="Invariant Violation",s}}function t(){return e}e.isRequired=e;var a={array:e,bigint:e,bool:e,func:e,number:e,object:e,string:e,symbol:e,any:e,arrayOf:t,element:e,elementType:e,instanceOf:t,node:e,objectOf:t,oneOf:t,oneOfType:t,shape:t,exact:t,checkPropTypes:i,resetWarningCache:n};return a.PropTypes=a,a}},697:(e,t,a)=>{e.exports=a(703)()},414:e=>{"use strict";e.exports="SECRET_DO_NOT_PASS_THIS_OR_YOU_WILL_BE_FIRED"},98:e=>{"use strict";e.exports=r}},t={};function a(r){var n=t[r];if(void 0!==n)return n.exports;var i=t[r]={exports:{}};return e[r](i,i.exports,a),i.exports}a.n=e=>{var t=e&&e.__esModule?()=>e.default:()=>e;return a.d(t,{a:t}),t},a.d=(e,t)=>{for(var r in t)a.o(t,r)&&!a.o(e,r)&&Object.defineProperty(e,r,{enumerable:!0,get:t[r]})},a.o=(e,t)=>Object.prototype.hasOwnProperty.call(e,t),a.r=e=>{"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})};var n={};return(()=>{"use strict";a.r(n),a.d(n,{default:()=>v});var e=a(98),t=a.n(e),r=a(697),i=a.n(r);function l(){return(l=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var a=arguments[t];for(var r in a)Object.prototype.hasOwnProperty.call(a,r)&&(e[r]=a[r])}return e}).apply(this,arguments)}var s=function(e){var a=e.pageClassName,r=e.pageLinkClassName,n=e.page,i=e.selected,s=e.activeClassName,o=e.activeLinkClassName,c=e.getEventListener,p=e.pageSelectedHandler,u=e.href,d=e.extraAriaContext,g=e.pageLabelBuilder,f=e.rel,y=e.ariaLabel||"Page "+n+(d?" "+d:""),m=null;return i&&(m="page",y=e.ariaLabel||"Page "+n+" is your current page",a=void 0!==a?a+" "+s:s,void 0!==r?void 0!==o&&(r=r+" "+o):r=o),t().createElement("li",{className:a},t().createElement("a",l({rel:f,role:u?void 0:"button",className:r,href:u,tabIndex:i?"-1":"0","aria-label":y,"aria-current":m,onKeyPress:p},c(p)),g(n)))};function o(){return(o=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var a=arguments[t];for(var r in a)Object.prototype.hasOwnProperty.call(a,r)&&(e[r]=a[r])}return e}).apply(this,arguments)}s.propTypes={pageSelectedHandler:i().func.isRequired,selected:i().bool.isRequired,pageClassName:i().string,pageLinkClassName:i().string,activeClassName:i().string,activeLinkClassName:i().string,extraAriaContext:i().string,href:i().string,ariaLabel:i().string,page:i().number.isRequired,getEventListener:i().func.isRequired,pageLabelBuilder:i().func.isRequired,rel:i().string};var c=function(e){var a=e.breakLabel,r=e.breakAriaLabel,n=e.breakClassName,i=e.breakLinkClassName,l=e.breakHandler,s=e.getEventListener;return t().createElement("li",{className:n||"break"},t().createElement("a",o({className:i,role:"button",tabIndex:"0","aria-label":r,onKeyPress:l},s(l)),a))};function p(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"";return null!=e?e:t}function u(e){return(u="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function d(){return(d=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var a=arguments[t];for(var r in a)Object.prototype.hasOwnProperty.call(a,r)&&(e[r]=a[r])}return e}).apply(this,arguments)}function g(e,t){return(g=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e})(e,t)}function f(e){if(void 0===e)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function y(e){return(y=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)})(e)}function m(e,t,a){return t in e?Object.defineProperty(e,t,{value:a,enumerable:!0,configurable:!0,writable:!0}):e[t]=a,e}c.propTypes={breakLabel:i().oneOfType([i().string,i().node]),breakAriaLabel:i().string,breakClassName:i().string,breakLinkClassName:i().string,breakHandler:i().func.isRequired,getEventListener:i().func.isRequired};var h=function(e){if("function"!=typeof e&&null!==e)throw TypeError("Super expression must either be null or a function");i.prototype=Object.create(e&&e.prototype,{constructor:{value:i,writable:!0,configurable:!0}}),Object.defineProperty(i,"prototype",{writable:!1}),e&&g(i,e);var a,r,n=(r=function(){if("undefined"==typeof Reflect||!Reflect.construct||Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){})),!0}catch(e){return!1}}(),function(){var e,t=y(i);if(e=r?Reflect.construct(t,arguments,y(this).constructor):t.apply(this,arguments),e&&("object"===u(e)||"function"==typeof e))return e;if(void 0!==e)throw TypeError("Derived constructors may only return object or undefined");return f(this)});function i(e){var a;return function(e,t){if(!(e instanceof t))throw TypeError("Cannot call a class as a function")}(this,i),m(f(a=n.call(this,e)),"handlePreviousPage",function(e){var t=a.state.selected;a.handleClick(e,null,t>0?t-1:void 0,{isPrevious:!0})}),m(f(a),"handleNextPage",function(e){var t=a.state.selected,r=a.props.pageCount;a.handleClick(e,null,t<r-1?t+1:void 0,{isNext:!0})}),m(f(a),"handlePageSelected",function(e,t){if(a.state.selected===e)return a.callActiveCallback(e),void a.handleClick(t,null,void 0,{isActive:!0});a.handleClick(t,null,e)}),m(f(a),"handlePageChange",function(e){a.state.selected!==e&&(a.setState({selected:e}),a.callCallback(e))}),m(f(a),"getEventListener",function(e){return m({},a.props.eventListener,e)}),m(f(a),"handleClick",function(e,t,r){var n=arguments.length>3&&void 0!==arguments[3]?arguments[3]:{},i=n.isPrevious,l=n.isNext,s=n.isBreak,o=n.isActive;e.preventDefault?e.preventDefault():e.returnValue=!1;var c=a.state.selected,p=a.props.onClick,u=r;if(p){var d=p({index:t,selected:c,nextSelectedPage:r,event:e,isPrevious:void 0!==i&&i,isNext:void 0!==l&&l,isBreak:void 0!==s&&s,isActive:void 0!==o&&o});if(!1===d)return;Number.isInteger(d)&&(u=d)}void 0!==u&&a.handlePageChange(u)}),m(f(a),"handleBreakClick",function(e,t){var r=a.state.selected;a.handleClick(t,e,r<e?a.getForwardJump():a.getBackwardJump(),{isBreak:!0})}),m(f(a),"callCallback",function(e){void 0!==a.props.onPageChange&&"function"==typeof a.props.onPageChange&&a.props.onPageChange({selected:e})}),m(f(a),"callActiveCallback",function(e){void 0!==a.props.onPageActive&&"function"==typeof a.props.onPageActive&&a.props.onPageActive({selected:e})}),m(f(a),"getElementPageRel",function(e){var t=a.state.selected,r=a.props,n=r.nextPageRel,i=r.prevPageRel,l=r.selectedPageRel;return t-1===e?i:t===e?l:t+1===e?n:void 0}),m(f(a),"pagination",function(){var e=[],r=a.props,n=r.pageRangeDisplayed,i=r.pageCount,l=r.marginPagesDisplayed,s=r.breakLabel,o=r.breakClassName,p=r.breakLinkClassName,u=r.breakAriaLabels,d=a.state.selected;if(i<=n)for(var g=0;g<i;g++)e.push(a.getPageElement(g));else{var f=n/2,y=n-f;d>i-n/2?f=n-(y=i-d):d<n/2&&(y=n-(f=d));var m,h,v=function(e){return a.getPageElement(e)},b=[];for(m=0;m<i;m++){var C=m+1;if(C<=l)b.push({type:"page",index:m,display:v(m)});else if(C>i-l)b.push({type:"page",index:m,display:v(m)});else if(m>=d-f&&m<=d+(0===d&&n>1?y-1:y))b.push({type:"page",index:m,display:v(m)});else if(s&&b.length>0&&b[b.length-1].display!==h&&(n>0||l>0)){var k=m<d?u.backward:u.forward;h=t().createElement(c,{key:m,breakAriaLabel:k,breakLabel:s,breakClassName:o,breakLinkClassName:p,breakHandler:a.handleBreakClick.bind(null,m),getEventListener:a.getEventListener}),b.push({type:"break",index:m,display:h})}}b.forEach(function(t,a){var r=t;"break"===t.type&&b[a-1]&&"page"===b[a-1].type&&b[a+1]&&"page"===b[a+1].type&&b[a+1].index-b[a-1].index<=2&&(r={type:"page",index:t.index,display:v(t.index)}),e.push(r.display)})}return e}),void 0!==e.initialPage&&void 0!==e.forcePage&&console.warn("(react-paginate): Both initialPage (".concat(e.initialPage,") and forcePage (").concat(e.forcePage,") props are provided, which is discouraged.")+" Use exclusively forcePage prop for a controlled component.\nSee https://reactjs.org/docs/forms.html#controlled-components"),a.state={selected:e.initialPage?e.initialPage:e.forcePage?e.forcePage:0},a}return a=[{key:"componentDidMount",value:function(){var e=this.props,t=e.initialPage,a=e.disableInitialCallback,r=e.extraAriaContext,n=e.pageCount,i=e.forcePage;void 0===t||a||this.callCallback(t),r&&console.warn("DEPRECATED (react-paginate): The extraAriaContext prop is deprecated. You should now use the ariaLabelBuilder instead."),Number.isInteger(n)||console.warn("(react-paginate): The pageCount prop value provided is not an integer (".concat(n,"). Did you forget a Math.ceil()?")),void 0!==t&&t>n-1&&console.warn("(react-paginate): The initialPage prop provided is greater than the maximum page index from pageCount prop (".concat(t," > ").concat(n-1,").")),void 0!==i&&i>n-1&&console.warn("(react-paginate): The forcePage prop provided is greater than the maximum page index from pageCount prop (".concat(i," > ").concat(n-1,")."))}},{key:"componentDidUpdate",value:function(e){void 0!==this.props.forcePage&&this.props.forcePage!==e.forcePage&&(this.props.forcePage>this.props.pageCount-1&&console.warn("(react-paginate): The forcePage prop provided is greater than the maximum page index from pageCount prop (".concat(this.props.forcePage," > ").concat(this.props.pageCount-1,").")),this.setState({selected:this.props.forcePage})),Number.isInteger(e.pageCount)&&!Number.isInteger(this.props.pageCount)&&console.warn("(react-paginate): The pageCount prop value provided is not an integer (".concat(this.props.pageCount,"). Did you forget a Math.ceil()?"))}},{key:"getForwardJump",value:function(){var e=this.state.selected,t=this.props,a=t.pageCount,r=e+t.pageRangeDisplayed;return r>=a?a-1:r}},{key:"getBackwardJump",value:function(){var e=this.state.selected-this.props.pageRangeDisplayed;return e<0?0:e}},{key:"getElementHref",value:function(e){var t=this.props,a=t.hrefBuilder,r=t.pageCount,n=t.hrefAllControls;if(a)return n||e>=0&&e<r?a(e+1,r,this.state.selected):void 0}},{key:"ariaLabelBuilder",value:function(e){var t=e===this.state.selected;if(this.props.ariaLabelBuilder&&e>=0&&e<this.props.pageCount){var a=this.props.ariaLabelBuilder(e+1,t);return this.props.extraAriaContext&&!t&&(a=a+" "+this.props.extraAriaContext),a}}},{key:"getPageElement",value:function(e){var a=this.state.selected,r=this.props,n=r.pageClassName,i=r.pageLinkClassName,l=r.activeClassName,o=r.activeLinkClassName,c=r.extraAriaContext,p=r.pageLabelBuilder;return t().createElement(s,{key:e,pageSelectedHandler:this.handlePageSelected.bind(null,e),selected:a===e,rel:this.getElementPageRel(e),pageClassName:n,pageLinkClassName:i,activeClassName:l,activeLinkClassName:o,extraAriaContext:c,href:this.getElementHref(e),ariaLabel:this.ariaLabelBuilder(e),page:e+1,pageLabelBuilder:p,getEventListener:this.getEventListener})}},{key:"render",value:function(){var e=this.props.renderOnZeroPageCount;if(0===this.props.pageCount&&void 0!==e)return e?e(this.props):e;var a=this.props,r=a.disabledClassName,n=a.disabledLinkClassName,i=a.pageCount,l=a.className,s=a.containerClassName,o=a.previousLabel,c=a.previousClassName,u=a.previousLinkClassName,g=a.previousAriaLabel,f=a.prevRel,y=a.nextLabel,m=a.nextClassName,h=a.nextLinkClassName,v=a.nextAriaLabel,b=a.nextRel,C=this.state.selected,k=0===C,x=C===i-1,P="".concat(p(c)).concat(k?" ".concat(p(r)):""),N="".concat(p(m)).concat(x?" ".concat(p(r)):""),T="".concat(p(u)).concat(k?" ".concat(p(n)):""),L="".concat(p(h)).concat(x?" ".concat(p(n)):"");return t().createElement("ul",{className:l||s,role:"navigation","aria-label":"Pagination"},t().createElement("li",{className:P},t().createElement("a",d({className:T,href:this.getElementHref(C-1),tabIndex:k?"-1":"0",role:"button",onKeyPress:this.handlePreviousPage,"aria-disabled":k?"true":"false","aria-label":g,rel:f},this.getEventListener(this.handlePreviousPage)),o)),this.pagination(),t().createElement("li",{className:N},t().createElement("a",d({className:L,href:this.getElementHref(C+1),tabIndex:x?"-1":"0",role:"button",onKeyPress:this.handleNextPage,"aria-disabled":x?"true":"false","aria-label":v,rel:b},this.getEventListener(this.handleNextPage)),y)))}}],function(e,t){for(var a=0;a<t.length;a++){var r=t[a];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,r.key,r)}}(i.prototype,a),Object.defineProperty(i,"prototype",{writable:!1}),i}(e.Component);m(h,"propTypes",{pageCount:i().number.isRequired,pageRangeDisplayed:i().number,marginPagesDisplayed:i().number,previousLabel:i().node,previousAriaLabel:i().string,prevPageRel:i().string,prevRel:i().string,nextLabel:i().node,nextAriaLabel:i().string,nextPageRel:i().string,nextRel:i().string,breakLabel:i().oneOfType([i().string,i().node]),breakAriaLabels:i().shape({forward:i().string,backward:i().string}),hrefBuilder:i().func,hrefAllControls:i().bool,onPageChange:i().func,onPageActive:i().func,onClick:i().func,initialPage:i().number,forcePage:i().number,disableInitialCallback:i().bool,containerClassName:i().string,className:i().string,pageClassName:i().string,pageLinkClassName:i().string,pageLabelBuilder:i().func,activeClassName:i().string,activeLinkClassName:i().string,previousClassName:i().string,nextClassName:i().string,previousLinkClassName:i().string,nextLinkClassName:i().string,disabledClassName:i().string,disabledLinkClassName:i().string,breakClassName:i().string,breakLinkClassName:i().string,extraAriaContext:i().string,ariaLabelBuilder:i().func,eventListener:i().string,renderOnZeroPageCount:i().func,selectedPageRel:i().string}),m(h,"defaultProps",{pageRangeDisplayed:2,marginPagesDisplayed:3,activeClassName:"selected",previousLabel:"Previous",previousClassName:"previous",previousAriaLabel:"Previous page",prevPageRel:"prev",prevRel:"prev",nextLabel:"Next",nextClassName:"next",nextAriaLabel:"Next page",nextPageRel:"next",nextRel:"next",breakLabel:"...",breakAriaLabels:{forward:"Jump forward",backward:"Jump backward"},disabledClassName:"disabled",disableInitialCallback:!1,pageLabelBuilder:function(e){return e},eventListener:"onClick",renderOnZeroPageCount:void 0,selectedPageRel:"canonical",hrefAllControls:!1});let v=h})(),n})()},98854:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>u});var r=a(37876),n=a(14232),i=a(82851),l=a.n(i),s=a(15641),o=a(66619),c=a(31753);let p=e=>{let{i18n:t}=(0,c.Bd)("common"),a=t.language,[i,p]=(0,n.useState)({}),[u,d]=(0,n.useState)({}),[g,f]=(0,n.useState)([]),{countries:y}=e,m=()=>{p(null),d(null)},h=(e,t,a)=>{m(),p(t),d({name:e.name,id:e.id})},v=()=>{let e=[];y&&y.data&&l().forEach(y.data,t=>{e.push({title:t.title,id:t._id,lat:t.coordinates&&t.coordinates[0]?t.coordinates[0].latitude:null,lng:t.coordinates&&t.coordinates[0]?t.coordinates[0].longitude:null})}),f([...e])};return(0,n.useEffect)(()=>{v()},[y]),(0,r.jsx)(o.A,{language:a,points:g,height:300,activeMarker:i,markerInfo:(0,r.jsx)(e=>{let{info:t}=e;return(0,r.jsx)("a",{href:"/".concat(a,"/country/show/").concat(null==t?void 0:t.id),children:null==t?void 0:t.name})},{info:u}),onClose:m,children:g&&g.length>=1?g.map((e,t)=>{if(e&&e.lat)return(0,r.jsx)(s.A,{name:e.title,id:e.id,icon:{url:"/images/map-marker-white.svg"},onClick:h,position:e},t)}):null})};p.defaultProps={countries:{data:[]}};let u=p}},e=>{var t=t=>e(e.s=t);e.O(0,[9759,636,6593,8792],()=>t(81081)),_N_E=e.O()}]);
//# sourceMappingURL=country-bc02441622382414.js.map