(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[6415],{7263:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>m});var r=a(37876),s=a(14232),l=a(82851),n=a.n(l),i=a(96151),o=a(52994),d=a(15898),c=a(37308),u=a(53718),p=a(31753);let m=e=>{var t,a,l;let m,{t:y}=(0,p.Bd)("common"),[h,f]=(0,s.useState)({title:"",health_profile:"",security_advice:"",_id:""}),[g,v]=(0,s.useState)({operation_count:0,project_count:0,event_count:0}),[x,_]=(0,s.useState)({}),[j,T]=(0,s.useState)(),[A,b]=(0,s.useState)(),[S]=(0,s.useState)(!1),[w,N]=(0,s.useState)(),[D,M]=(0,s.useState)([]),[k,C]=(0,s.useState)([]),[I,P]=(0,s.useState)([]),[L]=(0,s.useState)([]),[H,R]=(0,s.useState)([]),U={sort:{created_at:"asc"},query:{country:e.routes[1]},limit:"~",select:"-contact_details -end_date -start_date -description -link -media -reply -parent_vspace -user -type -updated_at -update_type -created_at"},Y={sort:{doc_created_at:"asc"},query:{},limit:"~",DocTable:!0,select:"-contact_details -end_date -images -start_date -description -images -link -media -reply -parent_vspace -user -type -updated_at -update_type -created_at"},B={sort:{doc_created_at:"asc"},query:{},limit:"~",DocUpdateTable:!0,select:"-contact_details -end_date -images -start_date -description -images -link -media -reply -parent_vspace -user -type -updated_at -update_type -created_at"},O=async t=>{var a;let r=await u.A.get("/country/".concat(e.routes[1]),t),s=[];null==r||null==(a=r.data)||a.map(e=>{var t;(null==e||null==(t=e.document)?void 0:t.length)>0&&e.document.map((t,a)=>{t.description=e.document[a].docsrc,s.push(t)})}),M(s.flat())},F=async()=>{var t;let a=await u.A.get("/country/".concat(e.routes[1]),B),r=[];null==a||null==(t=a.data)||t.map(e=>{var t;(null==e||null==(t=e.document)?void 0:t.length)>0&&e.document.map((t,a)=>{t.description=e.document[a].docsrc,r.push(t)})}),C(r.flat())},E=async()=>{let e=[],t=[],a=[],r=[],s=await u.A.get("/operation",U);s&&(null==s||s.data.map((s,l)=>{var n,i;(null==s||null==(n=s.document)?void 0:n.length)>0&&(a.push(s.document),t.push(s.doc_src)),(null==s||null==(i=s.images)?void 0:i.length)>0&&(r.push(s.images),e.push(s.images_src))}),P(r.flat(1/0)),R(e.flat(1/0)))},q=async e=>{var t;let a=await u.A.get("/projectStatus");if((null==a||null==(t=a.data)?void 0:t.length)>0){let e=[];n().forEach(a.data,function(t){("Ongoing"==t.title||"Planning"==t.title)&&e.push(t._id)}),b(e)}return!1},z=async t=>{v(await u.A.get("/stats/country/".concat(e.routes[1]),t))},J=async e=>{var t;let a=await u.A.get("/operation_status");if((null==a||null==(t=a.data)?void 0:t.length)>0){let e=[];n().forEach(a.data,function(t){("Deployed"==t.title||"Mobilizing"==t.title||"Monitoring"==t.title)&&e.push(t._id)}),T(e)}return!1};(0,s.useEffect)(()=>{if(null==e?void 0:e.routes[1]){let t=async e=>{var t;let a=await u.A.get("/eventStatus",{query:{title:"Current"}});return(null==a||null==(t=a.data)?void 0:t.length)>0&&N(a.data[0]._id),!1};(async t=>{let a=await u.A.get("/country/".concat(e.routes[1]),t);_({lat:parseFloat(a&&Array.isArray(a.coordinates)&&a.coordinates[0]&&a.coordinates[0].latitude?a.coordinates[0].latitude:20.593684),lng:parseFloat(a&&Array.isArray(a.coordinates)&&a.coordinates[0]&&a.coordinates[0].longitude?a.coordinates[0].longitude:78.96288)}),f(a)})({}),z({}),J({}),q({}),t({}),O(Y),F(),E()}},[]);let K=null==h||null==(t=h.health_profile)?void 0:t.replace("en",y("healthProfileLang"));switch(y("securityAdviceLang")){case"en":case"fr":m=null==h||null==(a=h.security_advice)?void 0:a.replace("/de/aussenpolitik/laender/","/en/aussenpolitik/laenderinformationen/");break;case"de":m=null==h||null==(l=h.security_advice)?void 0:l.replace("/de","/".concat(y("securityAdviceLang")))}let W={health_profile:K,security_advice:m};return(0,r.jsxs)("div",{children:[(0,r.jsx)(c.A,{routes:e.routes}),(0,r.jsx)(i.default,{latlng:x,countryData:h,operationStatusId:j,countryStatsData:g,eventStatusId:w,projectStatusId:A}),(0,r.jsx)("br",{}),(0,r.jsx)(o.default,{...W}),(0,r.jsx)(d.default,{prop:e,images:I,imgSrc:H,loading:S,updateSort:t=>{let a={sort:{},query:{parent_country:e.routes[1]},limit:"~",DocTable:!0,select:"-contact_details -end_date -images -start_date -description -images -link -media -reply -parent_vspace -user -type -updated_at -update_type -created_at"};a.sort={[t.columnSelector]:t.sortDirection},O(a)},document:D,docSrc:L,updateDocumentSort:t=>{({sort:{},query:{parent_country:e.routes[1]},limit:"~",DocUpdateTable:!0,select:"-contact_details -end_date -images -start_date -description -images -link -media -reply -parent_vspace -user -type -updated_at -update_type -created_at"}).sort={[t.columnSelector]:t.sortDirection},F()},updateDocument:k})]})}},9649:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>i});var r=a(37876);a(14232);var s=a(32890),l=a(48477),n=a(31753);let i=e=>{let{t}=(0,n.Bd)("common");return(0,r.jsx)(s.A,{defaultActiveKey:"1",children:(0,r.jsxs)(s.A.Item,{eventKey:"1",children:[(0,r.jsx)(s.A.Header,{children:(0,r.jsx)("div",{className:"cardTitle",children:t("Discussion")})}),(0,r.jsx)(s.A.Body,{children:(0,r.jsx)(l.A,{type:"country",id:(null==e?void 0:e.routes)?e.routes[1]:null})})]})})}},15641:(e,t,a)=>{"use strict";a.d(t,{A:()=>l});var r=a(37876);a(14232);var s=a(62945);let l=e=>{let{name:t="Marker",id:a="",countryId:l="",type:n,icon:i,position:o,onClick:d,title:c,draggable:u=!1}=e;return o&&"number"==typeof o.lat&&"number"==typeof o.lng?(0,r.jsx)(s.pH,{position:o,icon:i,title:c||t,draggable:u,onClick:e=>{d&&d({name:t,id:a,countryId:l,type:n,position:o},{position:o,getPosition:()=>o},e)}}):null}},15898:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>c});var r=a(37876);a(14232);var s=a(32890),l=a(86063),n=a(50463),i=a(43208),o=a(9649),d=a(49471);let c=e=>{let t=(0,d.default)(()=>(0,r.jsx)(o.default,{...e.prop}));return(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(s.A,{defaultActiveKey:"0",className:"countryAccordionNew",children:(0,r.jsx)(l.default,{...e})}),(0,r.jsx)(s.A,{className:"countryAccordionNew",children:(0,r.jsx)(n.default,{...e})}),(0,r.jsx)(s.A,{className:"countryAccordionNew",children:(0,r.jsx)(i.default,{...e})}),(0,r.jsx)(s.A,{className:"countryAccordionNew",children:(0,r.jsx)(t,{...e})})]})}},29335:(e,t,a)=>{"use strict";a.d(t,{A:()=>T});var r=a(15039),s=a.n(r),l=a(14232),n=a(77346),i=a(37876);let o=l.forwardRef((e,t)=>{let{className:a,bsPrefix:r,as:l="div",...o}=e;return r=(0,n.oU)(r,"card-body"),(0,i.jsx)(l,{ref:t,className:s()(a,r),...o})});o.displayName="CardBody";let d=l.forwardRef((e,t)=>{let{className:a,bsPrefix:r,as:l="div",...o}=e;return r=(0,n.oU)(r,"card-footer"),(0,i.jsx)(l,{ref:t,className:s()(a,r),...o})});d.displayName="CardFooter";var c=a(81764);let u=l.forwardRef((e,t)=>{let{bsPrefix:a,className:r,as:o="div",...d}=e,u=(0,n.oU)(a,"card-header"),p=(0,l.useMemo)(()=>({cardHeaderBsPrefix:u}),[u]);return(0,i.jsx)(c.A.Provider,{value:p,children:(0,i.jsx)(o,{ref:t,...d,className:s()(r,u)})})});u.displayName="CardHeader";let p=l.forwardRef((e,t)=>{let{bsPrefix:a,className:r,variant:l,as:o="img",...d}=e,c=(0,n.oU)(a,"card-img");return(0,i.jsx)(o,{ref:t,className:s()(l?"".concat(c,"-").concat(l):c,r),...d})});p.displayName="CardImg";let m=l.forwardRef((e,t)=>{let{className:a,bsPrefix:r,as:l="div",...o}=e;return r=(0,n.oU)(r,"card-img-overlay"),(0,i.jsx)(l,{ref:t,className:s()(a,r),...o})});m.displayName="CardImgOverlay";let y=l.forwardRef((e,t)=>{let{className:a,bsPrefix:r,as:l="a",...o}=e;return r=(0,n.oU)(r,"card-link"),(0,i.jsx)(l,{ref:t,className:s()(a,r),...o})});y.displayName="CardLink";var h=a(46052);let f=(0,h.A)("h6"),g=l.forwardRef((e,t)=>{let{className:a,bsPrefix:r,as:l=f,...o}=e;return r=(0,n.oU)(r,"card-subtitle"),(0,i.jsx)(l,{ref:t,className:s()(a,r),...o})});g.displayName="CardSubtitle";let v=l.forwardRef((e,t)=>{let{className:a,bsPrefix:r,as:l="p",...o}=e;return r=(0,n.oU)(r,"card-text"),(0,i.jsx)(l,{ref:t,className:s()(a,r),...o})});v.displayName="CardText";let x=(0,h.A)("h5"),_=l.forwardRef((e,t)=>{let{className:a,bsPrefix:r,as:l=x,...o}=e;return r=(0,n.oU)(r,"card-title"),(0,i.jsx)(l,{ref:t,className:s()(a,r),...o})});_.displayName="CardTitle";let j=l.forwardRef((e,t)=>{let{bsPrefix:a,className:r,bg:l,text:d,border:c,body:u=!1,children:p,as:m="div",...y}=e,h=(0,n.oU)(a,"card");return(0,i.jsx)(m,{ref:t,...y,className:s()(r,h,l&&"bg-".concat(l),d&&"text-".concat(d),c&&"border-".concat(c)),children:u?(0,i.jsx)(o,{children:p}):p})});j.displayName="Card";let T=Object.assign(j,{Img:p,Title:_,Subtitle:g,Body:o,Link:y,Text:v,Header:u,Footer:d,ImgOverlay:m})},43208:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>i});var r=a(37876);a(14232);var s=a(32890),l=a(31753),n=a(66404);let i=e=>{let{t}=(0,l.Bd)("common");return(0,r.jsx)(s.A,{defaultActiveKey:"0",children:(0,r.jsxs)(s.A.Item,{eventKey:"0",children:[(0,r.jsx)(s.A.Header,{children:(0,r.jsx)("div",{className:"cardTitle",children:t("Documents")})}),(0,r.jsxs)(s.A.Body,{children:[(0,r.jsx)(n.A,{loading:e.loading,sortProps:e.updateSort,docs:e.document,docsDescription:e.docSrc}),(0,r.jsx)("h6",{className:"mt-3",children:t("DocumentsfromUpdates")}),(0,r.jsx)(n.A,{loading:e.loading,sortProps:e.updateDocumentSort,docs:e.updateDocument,docsDescription:e.docSrc})]})]})})}},49471:(e,t,a)=>{"use strict";a.r(t),a.d(t,{canViewDiscussionUpdate:()=>r,default:()=>s}),a(14232);let r=(0,a(8178).A)({authenticatedSelector:e=>!!e.permissions&&!!e.permissions.update&&!!e.permissions.update["read:any"],wrapperDisplayName:"CanViewDiscussionUpdate"}),s=r},50463:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>i});var r=a(37876);a(14232);var s=a(32890),l=a(33458),n=a(31753);let i=e=>{let{t}=(0,n.Bd)("common");return(0,r.jsx)(s.A,{defaultActiveKey:"0",children:(0,r.jsxs)(s.A.Item,{eventKey:"0",children:[(0,r.jsx)(s.A.Header,{children:(0,r.jsx)("div",{className:"cardTitle",children:t("MediaGallery")})}),(0,r.jsx)(s.A.Body,{children:(0,r.jsx)(l.A,{gallery:e.images,imageSource:e.imgSrc})})]})})}},50749:(e,t,a)=>{"use strict";a.d(t,{A:()=>o});var r=a(37876);a(14232);var s=a(89773),l=a(31753),n=a(5507);function i(e){let{t}=(0,l.Bd)("common"),a={rowsPerPageText:t("Rowsperpage")},{columns:i,data:o,totalRows:d,resetPaginationToggle:c,subheader:u,subHeaderComponent:p,handlePerRowsChange:m,handlePageChange:y,rowsPerPage:h,defaultRowsPerPage:f,selectableRows:g,loading:v,pagServer:x,onSelectedRowsChange:_,clearSelectedRows:j,sortServer:T,onSort:A,persistTableHead:b,sortFunction:S,...w}=e,N={paginationComponentOptions:a,noDataComponent:t("NoData"),noHeader:!0,columns:i,data:o||[],dense:!0,paginationResetDefaultPage:c,subHeader:u,progressPending:v,subHeaderComponent:p,pagination:!0,paginationServer:x,paginationPerPage:f||10,paginationRowsPerPageOptions:h||[10,15,20,25,30],paginationTotalRows:d,onChangeRowsPerPage:m,onChangePage:y,selectableRows:g,onSelectedRowsChange:_,clearSelectedRows:j,progressComponent:(0,r.jsx)(n.A,{}),sortIcon:(0,r.jsx)("i",{className:"sort-icon fas fa-exchange-alt"}),sortServer:T,onSort:A,sortFunction:S,persistTableHead:b,className:"rki-table"};return(0,r.jsx)(s.Ay,{...N})}i.defaultProps={subHeader:!1,pagination:!0,totalRows:null,pagServer:!0,onSelectedRowsChange:null,clearSelectedRows:!1,sortServer:!1,persistTableHead:!1};let o=i},52994:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>o});var r=a(37876);a(14232);var s=a(56970),l=a(37784),n=a(60282),i=a(31753);let o=e=>{let{t}=(0,i.Bd)("common");return(0,r.jsx)(r.Fragment,{children:(0,r.jsxs)(s.A,{children:[(0,r.jsx)(l.A,{children:e.health_profile?(0,r.jsx)("a",{href:e.health_profile,target:"_blank",children:(0,r.jsx)(n.A,{className:"countryBtn d-grid",variant:"primary",size:"lg",children:t("healthprofile")})}):""}),(0,r.jsx)(l.A,{children:e.security_advice?(0,r.jsx)("a",{href:e.security_advice,target:"_blank",children:(0,r.jsx)(n.A,{className:"countryBtn d-grid",variant:"primary",size:"lg",children:t("securityadvice")})}):""})]})})}},66404:(e,t,a)=>{"use strict";a.d(t,{A:()=>o});var r=a(37876);a(14232);var s=a(10841),l=a.n(s),n=a(50749),i=a(31753);let o=e=>{let{docs:t,docsDescription:a,sortProps:s,loading:o}=e,d=async(e,t)=>{s({columnSelector:e.selector,sortDirection:t})},{t:c}=(0,i.Bd)("common"),u=[{name:c("FileType"),width:"15%",selector:"extension",cell:e=>e&&e.extension&&e.extension},{name:c("FileName"),width:"25%",selector:"document_title",cell:e=>e&&e.original_name&&(0,r.jsx)("a",{href:"".concat("http://localhost:3001/api/v1","/files/download/").concat(e._id),target:"_blank",children:e.original_name.split(".").slice(0,-1).join(".")}),sortable:!0},{name:c("Description"),selector:"description",cell:e=>e&&e.description&&e.description},{name:c("UploadedDate"),width:"25%",selector:"doc_created_at",cell:e=>e&&e.updated_at&&l()(e.updated_at).format("MM/DD/YYYY"),sortable:!0}];return(0,r.jsx)(n.A,{columns:u,data:t,pagServer:!0,onSort:d,persistTableHead:!0,loading:o})}},66619:(e,t,a)=>{"use strict";a.d(t,{A:()=>m});var r=a(37876);a(14232);var s=a(62945);let l=e=>{let{position:t,onCloseClick:a,children:l}=e;return(0,r.jsx)(s.Fu,{position:t,onCloseClick:a,children:(0,r.jsx)("div",{children:l})})},n="labels.text.fill",i="labels.text.stroke",o="road.highway",d="geometry.stroke",c=[{elementType:"geometry",stylers:[{color:"#1d2c4d"}]},{elementType:n,stylers:[{color:"#8ec3b9"}]},{elementType:i,stylers:[{color:"#1a3646"}]},{featureType:"administrative",elementType:"geometry",stylers:[{visibility:"off"}]},{featureType:"administrative.country",elementType:d,stylers:[{color:"#4b6878"}]},{featureType:"administrative.land_parcel",elementType:"labels",stylers:[{visibility:"off"}]},{featureType:"administrative.land_parcel",elementType:n,stylers:[{color:"#64779e"}]},{featureType:"administrative.province",elementType:d,stylers:[{color:"#4b6878"}]},{featureType:"landscape.man_made",elementType:d,stylers:[{color:"#334e87"}]},{featureType:"landscape.natural",elementType:"geometry",stylers:[{color:"#023e58"}]},{featureType:"poi",stylers:[{visibility:"off"}]},{featureType:"poi",elementType:"geometry",stylers:[{color:"#283d6a"}]},{featureType:"poi",elementType:"labels.text",stylers:[{visibility:"off"}]},{featureType:"poi",elementType:n,stylers:[{color:"#6f9ba5"}]},{featureType:"poi",elementType:i,stylers:[{color:"#1d2c4d"}]},{featureType:"poi.park",elementType:"geometry.fill",stylers:[{color:"#023e58"}]},{featureType:"poi.park",elementType:n,stylers:[{color:"#3C7680"}]},{featureType:"road",stylers:[{visibility:"off"}]},{featureType:"road",elementType:"geometry",stylers:[{color:"#304a7d"}]},{featureType:"road",elementType:"labels.icon",stylers:[{visibility:"off"}]},{featureType:"road",elementType:n,stylers:[{color:"#98a5be"}]},{featureType:"road",elementType:i,stylers:[{color:"#1d2c4d"}]},{featureType:o,elementType:"geometry",stylers:[{color:"#2c6675"}]},{featureType:o,elementType:d,stylers:[{color:"#255763"}]},{featureType:o,elementType:n,stylers:[{color:"#b0d5ce"}]},{featureType:o,elementType:i,stylers:[{color:"#023e58"}]},{featureType:"road.local",elementType:"labels",stylers:[{visibility:"off"}]},{featureType:"transit",stylers:[{visibility:"off"}]},{featureType:"transit",elementType:n,stylers:[{color:"#98a5be"}]},{featureType:"transit",elementType:i,stylers:[{color:"#1d2c4d"}]},{featureType:"transit.line",elementType:"geometry.fill",stylers:[{color:"#283d6a"}]},{featureType:"transit.station",elementType:"geometry",stylers:[{color:"#3a4762"}]},{featureType:"water",elementType:"geometry",stylers:[{color:"#0e1626"}]},{featureType:"water",elementType:n,stylers:[{color:"#4e6d70"}]}];var u=a(89099),p=a(55316);let m=e=>{let{markerInfo:t,activeMarker:a,initialCenter:n,children:i,height:o=300,width:d="114%",language:m,zoom:y=1,minZoom:h=1,onClose:f}=e,{locale:g}=(0,u.useRouter)(),{isLoaded:v,loadError:x}=(0,p._)();return x?(0,r.jsx)("div",{children:"Error loading maps"}):v?(0,r.jsx)("div",{className:"map-container",children:(0,r.jsx)("div",{className:"mapprint",style:{width:d,height:o,position:"relative"},children:(0,r.jsxs)(s.u6,{mapContainerStyle:{width:d,height:"number"==typeof o?"".concat(o,"px"):o},center:n||{lat:52.520017,lng:13.404195},zoom:y,onLoad:e=>{e.setOptions({styles:c})},options:{minZoom:h,draggable:!0,keyboardShortcuts:!1,streetViewControl:!1,panControl:!1,clickableIcons:!1,mapTypeControl:!1,fullscreenControl:!0},children:[i,t&&a&&a.getPosition&&(0,r.jsx)(l,{position:a.getPosition(),onCloseClick:()=>{console.log("close click"),null==f||f()},children:t})]})})}):(0,r.jsx)("div",{children:"Loading Maps..."})}},81009:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>c});var r=a(37876),s=a(14232),l=a(48230),n=a.n(l),i=a(50749),o=a(53718),d=a(31753);let c=function(e){let[t,a]=(0,s.useState)([]),[,l]=(0,s.useState)(!1),[c,u]=(0,s.useState)(0),[p]=(0,s.useState)(10),m=e&&e.routes?e.routes[1]:null,[y,h]=(0,s.useState)(null),{t:f,i18n:g}=(0,d.Bd)("common"),v={sort:{},limit:p,page:1,instiTable:!0,query:{},languageCode:g.language},x=[{name:f("Organisation"),selector:"title",cell:e=>(0,r.jsx)(n(),{href:"/institution/[...routes]",as:"/institution/show/".concat(e._id),children:e.title}),sortable:!0,maxWidth:"200px"},{name:f("ContactName"),selector:"contact_name",cell:e=>e.user?e.user.username:"",maxWidth:"200px"},{name:f("Expertise"),selector:"expertise",maxWidth:"200px"},{name:f("Region"),selector:"address.region",maxWidth:"200px"}],_=async(e,t)=>{l(!0),v.sort={[e.selector]:t};let a={sort:{[e.selector]:t},limit:p,page:1,instiTable:!0,query:{}};h(a),j(a)},j=async e=>{l(!0),0==Object.keys(e.sort).length&&(e.sort={created_at:"desc"});let t=await o.A.get("/country/".concat(m,"/institution"),e);l(!0),t&&t.data&&t.data.length>0&&(t.data.forEach((e,a)=>{t.data[a].expertise=e.expertise.map(e=>e.title).join(", "),t.data[a].address.region=e.address.region.map(e=>e.title).join(", ")}),a(t.data),u(t.totalCount)),l(!1)},T=async(e,t)=>{v.limit=e,v.page=t,y&&(v.sort=y.sort),j(v)};return(0,s.useEffect)(()=>{j(v)},[]),(0,r.jsx)(i.A,{columns:x,data:t,totalRows:c,handlePerRowsChange:T,handlePageChange:e=>{v.limit=p,v.page=e,y&&(v.sort=y.sort),j(v)},persistTableHead:!0,onSort:_})}},81764:(e,t,a)=>{"use strict";a.d(t,{A:()=>s});let r=a(14232).createContext(null);r.displayName="CardHeaderContext";let s=r},84135:function(e,t,a){(function(e){"use strict";function t(e,t,a,r){var s={m:["eine Minute","einer Minute"],h:["eine Stunde","einer Stunde"],d:["ein Tag","einem Tag"],dd:[e+" Tage",e+" Tagen"],w:["eine Woche","einer Woche"],M:["ein Monat","einem Monat"],MM:[e+" Monate",e+" Monaten"],y:["ein Jahr","einem Jahr"],yy:[e+" Jahre",e+" Jahren"]};return t?s[a][0]:s[a][1]}e.defineLocale("de",{months:"Januar_Februar_M\xe4rz_April_Mai_Juni_Juli_August_September_Oktober_November_Dezember".split("_"),monthsShort:"Jan._Feb._M\xe4rz_Apr._Mai_Juni_Juli_Aug._Sep._Okt._Nov._Dez.".split("_"),monthsParseExact:!0,weekdays:"Sonntag_Montag_Dienstag_Mittwoch_Donnerstag_Freitag_Samstag".split("_"),weekdaysShort:"So._Mo._Di._Mi._Do._Fr._Sa.".split("_"),weekdaysMin:"So_Mo_Di_Mi_Do_Fr_Sa".split("_"),weekdaysParseExact:!0,longDateFormat:{LT:"HH:mm",LTS:"HH:mm:ss",L:"DD.MM.YYYY",LL:"D. MMMM YYYY",LLL:"D. MMMM YYYY HH:mm",LLLL:"dddd, D. MMMM YYYY HH:mm"},calendar:{sameDay:"[heute um] LT [Uhr]",sameElse:"L",nextDay:"[morgen um] LT [Uhr]",nextWeek:"dddd [um] LT [Uhr]",lastDay:"[gestern um] LT [Uhr]",lastWeek:"[letzten] dddd [um] LT [Uhr]"},relativeTime:{future:"in %s",past:"vor %s",s:"ein paar Sekunden",ss:"%d Sekunden",m:t,mm:"%d Minuten",h:t,hh:"%d Stunden",d:t,dd:t,w:t,ww:"%d Wochen",M:t,MM:t,y:t,yy:t},dayOfMonthOrdinalParse:/\d{1,2}\./,ordinal:"%d.",week:{dow:1,doy:4}})})(a(10841))},86063:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>c});var r=a(37876);a(14232);var s=a(32890),l=a(49589),n=a(56970),i=a(37784),o=a(81009),d=a(31753);let c=e=>{let{t}=(0,d.Bd)("common");return(0,r.jsx)(s.A,{defaultActiveKey:"0",children:(0,r.jsxs)(s.A.Item,{eventKey:"0",children:[(0,r.jsx)(s.A.Header,{children:(0,r.jsx)("div",{className:"cardTitle",children:t("Organisation")})}),(0,r.jsx)(s.A.Body,{children:(0,r.jsx)(l.A,{fluid:!0,children:(0,r.jsx)(n.A,{children:(0,r.jsx)(i.A,{children:(0,r.jsx)(o.default,{...e.prop})})})})})]})})}},96151:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>u});var r=a(37876);a(14232);var s=a(56970),l=a(37784),n=a(48230),i=a.n(n),o=a(66619),d=a(15641),c=a(31753);let u=e=>{var t,a;let{t:n}=(0,c.Bd)("common");return(0,r.jsx)(r.Fragment,{children:(0,r.jsx)(s.A,{children:(0,r.jsxs)(l.A,{xs:12,style:{display:"flex"},children:[(0,r.jsx)("div",{className:"countryMap",children:(null==e||null==(t=e.latlng)?void 0:t.lat)?(0,r.jsx)(o.A,{initialCenter:e.latlng,children:(0,r.jsx)(d.A,{icon:{url:"/images/map-marker-blue.svg"},position:e.latlng})}):null}),(0,r.jsxs)("div",{className:"countryInfo",children:[(0,r.jsxs)("h4",{children:[" ",null==e||null==(a=e.countryData)?void 0:a.title," "]}),function(e,t,a,s,l,n){return(0,r.jsxs)("div",{className:"countryInfoDetails",children:[(0,r.jsx)(i(),{href:{pathname:"/operation",query:{country:null==e?void 0:e._id,status:t}},children:(0,r.jsxs)("div",{className:"countryInfo-Item",children:[(0,r.jsx)("div",{className:"countryInfo-img",children:(0,r.jsx)("img",{src:"/images/countryinfo1.png",width:"25",height:"25",alt:"Organization Quick Info"})}),(0,r.jsxs)("span",{children:[a("CurrentOperation"),(0,r.jsx)("br",{}),(0,r.jsx)("b",{children:(null==s?void 0:s.operation_count)?s.operation_count:0})]})]})}),(0,r.jsx)(i(),{href:{pathname:"/event",query:{country:null==e?void 0:e._id,status:l}},children:(0,r.jsxs)("div",{className:"countryInfo-Item",children:[(0,r.jsx)("div",{className:"countryInfo-img",children:(0,r.jsx)("img",{src:"/images/countryinfo2.png",width:"30",height:"22",alt:"Organization Quick Info"})}),(0,r.jsxs)("span",{children:[a("CurrentEvent"),(0,r.jsx)("br",{}),(0,r.jsx)("b",{children:(null==s?void 0:s.event_count)?s.event_count:0})]})]})}),(0,r.jsx)(i(),{href:{pathname:"/project",query:{country:e._id,status:n}},children:(0,r.jsxs)("div",{className:"countryInfo-Item",children:[(0,r.jsx)("div",{className:"countryInfo-img",children:(0,r.jsx)("img",{src:"/images/quickinfo3.png",width:"24",height:"21",alt:"Organization Quick Info"})}),(0,r.jsxs)("span",{children:[a("CurrentProject"),(0,r.jsx)("br",{}),(0,r.jsx)("b",{children:(null==s?void 0:s.project_count)?s.project_count:0})]})]})})]})}(e.countryData,e.operationStatusId,n,e.countryStatsData,e.eventStatusId,e.projectStatusId)]})]})})})}}}]);
//# sourceMappingURL=6415-9455d2453fead20f.js.map