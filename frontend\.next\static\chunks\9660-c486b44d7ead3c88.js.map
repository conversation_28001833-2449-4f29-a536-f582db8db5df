{"version": 3, "file": "static/chunks/9660-c486b44d7ead3c88.js", "mappings": "oOA0CA,MAvBiCA,IAE7B,IAAMC,EAA0BC,CAAAA,EAAAA,EAAAA,aAqBrBC,UArBqBD,CAAuBA,CAAC,IAAM,OAqB5BC,CArB4B,CAqB3B,CArB4BC,EAAAA,OAAmBA,CAAAA,CAACC,UAAaL,EAAMK,SAAS,IAE/G,MACI,+BACI,UAACC,EAAAA,CAAGA,CAAAA,UACA,WAACC,EAAAA,CAAGA,CAAAA,CAACC,UAAU,mBAAmBC,GAAI,aAClC,UAACC,EAAAA,CAASA,CAAAA,UACN,UAACC,EAAAA,OAAuBA,CAAAA,CAACC,QAAWZ,EAAMa,WAAW,KAEzD,UAACH,EAAAA,CAASA,CAAAA,UACN,UAACT,EAAAA,CAAAA,KAEL,UAACS,EAAAA,CAASA,CAAAA,UACN,UAACI,EAAAA,OAAqBA,CAAAA,CAACT,UAAaL,EAAMK,SAAS,WAM3E,mKCQA,MArCgC,QAuBtBL,EAtBN,IAAMe,EAAa,YAoCRJ,UAlCL,GAAEK,CAAC,CAAE,CAAGC,CAAAA,EAAAA,EAAAA,EAAAA,CAAcA,CAAC,UACvB,CAACC,EAASC,EAAW,CAAGC,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,EAAC,GACvC,MACI,+BACQ,WAACV,EAAAA,CAASA,CAACW,IAAI,EAACC,SAAS,cACrB,WAACZ,EAAAA,CAASA,CAACa,MAAM,EAACC,QAAS,IAAML,EAAW,CAACD,aACzC,UAACO,MAAAA,CAAIjB,UAAU,qBAAaQ,EAAE,oBAC9B,UAACS,MAAAA,CAAIjB,UAAU,qBACVU,EAAU,UAACQ,EAAAA,CAAeA,CAAAA,CAACC,KAAMC,EAAAA,GAAMA,CAAEC,MAAM,SAC5C,UAACH,EAAAA,CAAeA,CAAAA,CAACC,KAAMG,EAAAA,GAAOA,CAAED,MAAM,cAGlD,UAACnB,EAAAA,CAASA,CAACqB,IAAI,WACP,WAACC,EAAAA,CAAIA,CAACC,IAAI,EAACzB,UAAU,gCACjB,WAAC0B,IAAAA,WAAE,UAACC,IAAAA,UAAGnB,EAAE,YAAc,IAAC,WAACoB,OAAAA,WAAK,IAAEpC,EAAMY,OAAO,CAACyB,MAAM,CAAGrC,EAAMY,OAAO,CAACyB,MAAM,CAAC,KAAQ,CAAG,SACvF,WAACH,IAAAA,WAAE,UAACC,IAAAA,UAAGnB,EAAE,aAAe,IAAC,WAACoB,OAAAA,WAAK,IAAEpC,EAAMY,OAAO,CAAC0B,UAAU,CAAIC,IAAOvC,EAAMY,OAAO,CAAC0B,UAAU,EAAEE,MAAM,CAACzB,GAAe,KAAK,IAAtDwB,MACnE,WAACL,IAAAA,WAAE,UAACC,IAAAA,UAAGnB,EAAE,aAAe,IAAC,WAACoB,OAAAA,WAAK,IAAEpC,EAAMY,OAAO,CAAC6B,QAAQ,CAAIF,IAAOvC,EAAMY,OAAO,CAAC6B,QAAQ,EAAED,MAAM,CAACE,cAAhCH,WACjE,WAACL,IAAAA,WAAE,UAACC,IAAAA,UAAGnB,EAAE,kBAAoB,IAAC,WAACoB,OAAAA,WAAK,IAAEpC,EAAMY,OAAO,CAAC+B,UAAU,CAAIJ,IAAOvC,EAAMY,OAAO,CAAC+B,UAAU,EAAEH,MAAM,CAACzB,GAAe,KAAK,IAAtDwB,MACxE,WAACL,IAAAA,WAAE,UAACC,IAAAA,UAAGnB,EAAE,sBAAwB,IACzD,UAACoB,OAAAA,UACCpC,CAAAA,OAAAA,EAAAA,EAAMY,OAAAA,EAANZ,KAAAA,EAAAA,EAAe4C,GAAf5C,IAAe4C,GACf,UAACC,IAAAA,CAAEC,KAAM9C,EAAMY,OAAO,CAACgC,OAAO,CAAEG,OAAQ,kBACvC/C,EAAMY,OAAO,CAACgC,OAAO,iBAUhC,gIC8DA,MAzFoB,IAClB,GAAM,CAAE5B,CAAC,CAAE,CAAGC,CAAAA,EAAAA,EAAAA,EAAAA,CAAcA,CAAC,QAwFhB+B,EAvFP,MAAEC,CAAI,CAAEC,CAuFUF,EAAC,CAvFT,CAAE,CAAGhD,EACf,CAACmD,EAAWC,EAAe,CAAGhC,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAC,EAAE,EACzC,CAACiC,EAASC,EAAW,CAAGlC,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,EAAC,GACjC,CAACmC,EAAWC,EAAa,CAAGpC,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAC,GACrC,CAACqC,EAASC,EAAW,CAAGtC,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAC,IACjC,CAACuC,EAAsB,CAAGvC,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,EAAC,GAEnCwC,EAAe,CACnBC,KAAM,CAAEvB,WAAY,KAAM,EAC1BwB,MAAOL,EACPM,KAAM,EACNC,MAAO,CAAC,CACV,EAEMC,EAAU,CACd,CACEC,KAAMlD,EAAE,SACRmD,SAAU,QACVC,KAAM,GAAYC,GAAKA,EAAEC,KAAK,EAAID,EAAEE,GAAG,CAAG,UAACC,IAAIA,CAAC1B,KAAK,sBAAsB2B,GAAI,cAAhCD,EAAsD,OAANH,EAAEE,GAAG,WAAMF,EAAEC,KAAK,GAAW,EAC9H,EACA,CACEJ,KAAMlD,EAAE,SACRmD,SAAU,QACVC,KAAM,GAAYC,GAAKA,EAAEK,IAAI,EAAIL,EAAEK,IAAI,CAACC,SAAS,CAAG,GAAuBN,MAAAA,CAApBA,EAAEK,IAAI,CAACC,SAAS,CAAC,KAAmB,OAAhBN,EAAEK,IAAI,CAACE,QAAQ,EAAK,EACjG,EACA,CACEV,KAAMlD,EAAE,iBACRmD,SAAU,aACVC,KAAM,GAAYC,GAAKA,EAAEQ,UAAU,CAAG,SAAW,SAEnD,EACA,CACEX,KAAMlD,EAAE,mBACRmD,SAAU,UACVC,KAAM,GAAYC,GAAKA,EAAES,OAAO,CAAGT,EAAES,OAAO,CAACC,MAAM,CAAG,GACxD,EACD,CAEKC,EAAkB,MAAOC,IAC7B3B,GAAW,GACX,IAAM4B,EAAW,MAAMC,EAAAA,CAAUA,CAACC,GAAG,CAAC,YAA8BlC,MAAAA,CAAlBD,EAAK,eAAgB,OAAHC,GAAMU,GACtEsB,IACO,MADG,QACZjC,EAAuBG,EAAe8B,EAASG,SAAS,EAAIjC,EAAe8B,EAAStE,OAAO,EAC3F4C,EAAa0B,EAASI,UAAU,EAChChC,GAAW,GAEf,EAQMiC,EAAsB,MAAOC,EAAoBzB,KACrDH,EAAaE,KAAK,CAAG0B,EACrB5B,EAAaG,IAAI,CAAGA,EACpBT,GAAW,GACX,IAAM4B,EAAW,MAAMC,EAAAA,CAAUA,CAACC,GAAG,CAAC,YAA8BlC,MAAAA,CAAlBD,EAAK,eAAgB,OAAHC,GAAMU,GACtEsB,IACO,MADG,QACZjC,EAAuBG,EAAe8B,EAASG,SAAS,EAAIjC,EAAe8B,EAAStE,OAAO,EAC3F8C,EAAW8B,GACXlC,GAAW,GAEf,EAQA,MANAmC,CAAAA,EAAAA,EAAAA,SAAAA,CAASA,CAAC,KACRT,EAAgBpB,EAClB,EAAG,EAAE,EAKH,UAACnC,MAAAA,UACC,UAACiE,EAAAA,CAAQA,CAAAA,CACPzB,QAASA,EACT0B,KAAMxC,EACNI,UAAWA,EACXF,QAASA,EACTM,sBAAuBA,EACvB4B,oBAAqBA,EACrBK,iBAjCmB,CAiCDA,GAhCtBhC,EAAaE,KAAK,CAAGL,EACrBG,EAAaG,IAAI,CAAGA,EACpBiB,EAAgBpB,EAClB,KAiCF,uOCnGO,IAAMiC,EAAgBC,CAAAA,EAAAA,EAAAA,CAAAA,CAAoBA,CAAC,CAChDC,sBAAuB,KACjBC,EAAMC,WAAW,IAAID,EAAMC,WAAW,CAACrF,OAAO,IAAIoF,EAAMC,WAAW,CAACrF,OAAO,CAAC,aAAa,CAK/FsF,CALiG,kBAK7E,eACtB,GAAG,EAE8BJ,CAAAA,EAAAA,EAAAA,CAAAA,CAAoBA,CAAC,CACpDC,sBAAuB,KACjBC,EAAMC,WAAW,IAAID,EAAMC,WAAW,CAACrF,OAAO,IAAIoF,EAAMC,WAAW,CAACrF,OAAO,CAAC,aAAa,CAK/FsF,CALiG,kBAK7E,oBACpBC,iBAAkB,IAAM,UAACC,EAAAA,OAAIA,CAAAA,CAAAA,EAC/B,GAEaC,EAAiBP,CAAAA,EAAAA,EAAAA,CAAAA,CAAoBA,CAAC,CACjDC,sBAAuB,CAACC,EAAOhG,KAC7B,GAAIgG,EAAMC,WAAW,EAAID,EAAMC,WAAW,CAACrF,OAAO,EAAE,GAC9CoF,EAAMC,WAAW,CAACrF,OAAO,CAAC,aAAa,CACzC,CAD2C,MACpC,OAEP,GAAIoF,EAAMC,WAAW,CAACrF,OAAO,CAAC,aAAa,EAAE,EACjCA,OAAO,EAAIZ,EAAMY,OAAO,CAAC8D,IAAI,EAAI1E,EAAMY,OAAO,CAAC8D,IAAI,CAACH,GAAG,GAAKyB,EAAMtB,IAAI,CAACH,GAAG,CAClF,CADoF,MAC7E,CAGb,CAEF,OAAO,CACT,EACA2B,mBAAoB,gBACtB,GAAG,EAE+BJ,CAAAA,EAAAA,EAAAA,CAAAA,CAAoBA,CAAC,CACrDC,sBAAuB,CAACC,EAAOhG,KAC7B,GAAIgG,EAAMC,WAAW,EAAID,EAAMC,WAAW,CAACrF,OAAO,CAChD,CADkD,GAC9CoF,EAAMC,WAAW,CAACrF,OAAO,CAAC,aAAa,CACzC,CAD2C,MACpC,OAEP,GAAIoF,EAAMC,WAAW,CAACrF,OAAO,CAAC,aAAa,EAAE,EACjCA,OAAO,EAAIZ,EAAMY,OAAO,CAAC8D,IAAI,EAAI1E,EAAMY,OAAO,CAAC8D,IAAI,CAACH,GAAG,GAAKyB,EAAMtB,IAAI,CAACH,GAAG,CAClF,CADoF,MAC7E,CAGb,CAEF,OAAO,CACT,EACA2B,mBAAoB,qBACpBC,iBAAkB,IAAM,UAACC,EAAAA,OAAIA,CAAAA,CAAAA,EAC/B,GAEalG,EAA0B4F,CAAAA,EAAAA,EAAAA,CAAAA,CAAoBA,CAAC,CAC1DC,sBAAuB,KACjBC,EAAMC,WAAW,IAAID,EAAMC,WAAW,CAACK,MAAM,IAAIN,EAAMC,WAAW,CAACK,MAAM,CAAC,WAAW,CAK3FJ,CAL6F,kBAKzE,yBACtB,GAEA,EAAeL,aAAaA,EAAC,8FCtC7B,SAASH,EAAS1F,CAAoB,EACpC,GAAM,CAAEgB,CAAC,CAAE,CAAGC,CAAAA,EAAAA,EAAAA,EAAAA,CAAcA,CAAC,UACvBsF,EAA6B,CACjCC,gBAAiBxF,EAAE,cACnB,EACI,SACJiD,CAAO,MACP0B,CAAI,WACJpC,CAAS,uBACTI,CAAqB,WACrB8C,CAAS,oBACTC,CAAkB,qBAClBnB,CAAmB,CACnBK,kBAAgB,aAChBe,CAAW,oBACXC,CAAkB,gBAClBC,CAAc,SACdxD,CAAO,CACPyD,WAAS,CACTC,sBAAoB,mBACpBC,CAAiB,YACjBC,CAAU,QACVC,CAAM,kBACNC,CAAgB,cAChBC,CAAY,CAEZ,CADA,EACGC,EACJ,CAAGrH,EAGEsH,EAAiB,4BACrBf,EACAgB,gBAAiBvG,EAAE,IAP0C,MAQ7DwG,UAAU,UACVvD,EACA0B,KAAMA,GAAQ,EAAE,CAChB8B,OAAO,EACPC,2BAA4B/D,EAC5BgE,UAAWlB,EACXmB,gBAAiBvE,qBACjBqD,EACAmB,YAAY,EACZC,iBAAkBhB,EAClBiB,kBAAmBnB,GAA0C,GAC7DoB,eADwCpB,cACVD,GAA4B,CAAC,GAAI,GAAI,GAAI,CAA3BA,EAA+B,GAAG,CAC9EsB,oBAAqB1E,EACrB2E,oBAAqB3C,EACrB4C,aAAcvC,iBACdiB,uBACAE,oBACAC,EACAoB,kBAAmB,UAACC,EAAAA,CAAYA,CAAAA,CAAAA,GAChCC,SAAU,UAACC,IAAAA,CAAE/H,UAAU,6CACvByG,SACAC,eACAE,mBACAD,EACA3G,UAAW,WACb,EACA,MACE,UAACgI,EAAAA,EAASA,CAAAA,CAAE,GAAGlB,CAAc,EAEjC,CAEA5B,EAAS+C,YAAY,CAAG,CACtBd,WAAW,EACXE,YAAY,EACZtE,UAAW,KACXuD,WAAW,EACXC,qBAAsB,KACtBC,kBAAmB,GACnBC,WAAY,GACZE,kBAAkB,CACpB,EAEA,MAAezB,QAAQA,EAAC,qIChExB,MA/B+B1F,QAkBLA,EAAAA,EAjBtB,GAAM,GAAEgB,CAAC,CAAE,CAAGC,CAAAA,EAAAA,EAAAA,EAAAA,CAAcA,CAAC,UACvB,CAACC,EAASC,EAAW,CAAGC,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,EAAC,GACvC,MACI,+BACI,WAACV,EAAAA,CAASA,CAACW,IAAI,EAACC,SAAS,cACrB,WAACZ,EAAAA,CAASA,CAACa,MAAM,EAACC,QAAS,IAAML,EAAW,CAACD,aACzC,UAACO,MAAAA,CAAIjB,UAAU,qBAAaQ,EAAE,wBAC9B,UAACS,MAAAA,CAAIjB,UAAU,qBACVU,EACG,UAACQ,EAAAA,CAAeA,CAAAA,CAACC,KAAMG,EAAAA,GAAOA,CAAED,MAAM,SAEtC,UAACH,EAAAA,CAAeA,CAAAA,CAACC,KAAMC,EAAAA,GAAMA,CAAEC,MAAM,cAIjD,UAACnB,EAAAA,CAASA,CAACqB,IAAI,WACX,UAACiB,EAAAA,CAAWA,CAAAA,CACVE,GAAIlD,QAAAA,EAAAA,EAAMK,SAAS,GAAfL,OAAAA,EAAAA,EAAiB0I,MAAAA,EAAjB1I,KAAAA,EAAAA,CAAyB,CAAC,EAAE,CAA5BA,EAAgC,GACpCiD,KAAK,UACL0F,WAAY,EAAE,CACdC,mBAAmB,EACnBC,oBAAqB,EACrBC,kBAAmB,GACnBC,sBAAuB,UAM7C,+ICZA,MAnB4B,QAaoB/I,EAZ5C,GAAM,GAAEgB,CAAC,CAAE,CAAGC,CAAAA,EAAAA,EAAAA,EAAAA,CAAcA,CAAC,UACvB,CAACC,EAASC,EAAW,CAAGC,CAAAA,EAAAA,EAiBC,QAjBDA,CAAQA,EAAC,GACvC,MACI,WAACV,EAAAA,CAASA,CAACW,IAAI,EAACC,SAAS,cACrB,WAACZ,EAAAA,CAASA,CAACa,MAAM,EAACC,QAAS,IAAML,EAAW,CAACD,aACzC,UAACO,MAAAA,CAAIjB,UAAU,qBAAaQ,EAAE,iBAC9B,UAACS,MAAAA,CAAIjB,UAAU,qBACVU,EAAU,UAACQ,EAAAA,CAAeA,CAAAA,CAACC,KAAMC,EAAAA,GAAMA,CAAEC,MAAM,SAC5C,UAACH,EAAAA,CAAeA,CAAAA,CAACC,KAAMG,EAAAA,GAAOA,CAAED,MAAM,cAGlD,UAACnB,EAAAA,CAASA,CAACqB,IAAI,WACX,UAACiH,EAAAA,CAAUA,CAAAA,CAAC/F,KAAK,UAAUC,GAAKlD,OAAAA,GAAAA,OAAAA,EAAAA,EAAOK,IAAPL,KAAgB,EAAhBA,KAAAA,EAAAA,EAAkB0I,GAAlB1I,GAAwB,EAAGA,EAAMK,SAAS,CAACqI,MAAM,CAAC,EAAE,CAAG,SAIvG,yBCzBuC,CAGtC,YAA4B,aAI7B,oBACA,OACA,iCACA,iCACA,0BACA,0BACA,+BACA,8BACA,8BACA,4BACA,6BAEA,yBAGA,qBACA,qGACA,KAEA,YACA,2EACA,oBACA,SACA,oEACA,KAEA,uDACA,8CACA,sBACA,gBACA,WACA,eACA,eACA,kBACA,yBACA,+BACA,CAAS,CACT,UACA,8BACA,aACA,+BACA,8BACA,gCACA,uCACA,CAAS,CACT,cACA,eACA,cACA,sBACA,iBACA,IACA,gBACA,IACA,gBACA,IACA,KACA,IACA,eACA,IACA,KACA,IACA,IACA,CAAS,CACT,2BAAoC,IAAI,IACxC,cACA,MACA,MACA,KACA,CAAS,CACJ,EAIL,CAAC,CAhFiD,EAAQ,KAAW", "sources": ["webpack://_N_E/./pages/project/components/ProjectAccordianSection.tsx", "webpack://_N_E/./pages/project/components/ProjectDetailsAccordion.tsx", "webpack://_N_E/./components/common/VspaceTable.tsx", "webpack://_N_E/./pages/project/permission.tsx", "webpack://_N_E/./components/common/RKITable.tsx", "webpack://_N_E/./pages/project/components/VirtualSpaceAccordion.tsx", "webpack://_N_E/./pages/project/components/DiscussionAccordion.tsx", "webpack://_N_E/./node_modules/moment/locale/de.js"], "sourcesContent": ["//Import Library\r\nimport React from \"react\";\r\nimport { Accordion, Col, Row } from \"react-bootstrap\";\r\n\r\n//Import services/components\r\nimport { canViewDiscussionUpdate } from \"../permission\";\r\nimport ProjectDetailsAccordion from \"./ProjectDetailsAccordion\";\r\nimport VirtualSpaceAccordion from \"./VirtualSpaceAccordion\";\r\nimport DiscussionAccordion from \"./DiscussionAccordion\";\r\n\r\n\r\ninterface ProjectAccordianSectionProps {\r\n  projectData: any;\r\n  routeData: {\r\n    routes: string[];\r\n  };\r\n  editAccess: boolean;\r\n}\r\n\r\nconst ProjectAccordianSection = (props: ProjectAccordianSectionProps) => {\r\n\r\n    const CanViewDiscussionUpdate = canViewDiscussionUpdate(() => <DiscussionAccordion routeData = {props.routeData} />)\r\n\r\n    return (\r\n        <>\r\n            <Row>\r\n                <Col className=\"projectAccordion\" xs={12}>\r\n                    <Accordion>\r\n                        <ProjectDetailsAccordion project = {props.projectData} />\r\n                    </Accordion>\r\n                    <Accordion>\r\n                        <CanViewDiscussionUpdate />\r\n                    </Accordion>\r\n                    <Accordion>\r\n                        <VirtualSpaceAccordion routeData = {props.routeData} />\r\n                    </Accordion>\r\n                </Col>\r\n            </Row>\r\n        </>\r\n    )\r\n}\r\n\r\nexport default ProjectAccordianSection;", "//Import Library\r\nimport React, { useState } from \"react\";\r\nimport { faMinus, faPlus } from \"@fortawesome/free-solid-svg-icons\";\r\nimport { FontAwesomeIcon } from \"@fortawesome/react-fontawesome\";\r\nimport moment from \"moment\";\r\nimport { Accordion, Card } from \"react-bootstrap\";\r\n\r\n//Import services/components\r\nimport { useTranslation } from 'next-i18next';\r\n\r\n\r\nconst ProjectDetailsAccordion = (props: any) => {\r\n    const formatDate = \"DD-MM-YYYY HH:mm:ss\";\r\n    const formatDateStartDate = \"DD-MM-YYYY\";\r\n    const { t } = useTranslation('common');\r\n    const [section, setSection] = useState(true);\r\n    return (\r\n        <>\r\n                <Accordion.Item eventKey=\"0\">\r\n                    <Accordion.Header onClick={() => setSection(!section)}>\r\n                        <div className=\"cardTitle\">{t(\"ProjectDetails\")}</div>\r\n                        <div className=\"cardArrow\">\r\n                            {section ? <FontAwesomeIcon icon={faPlus} color=\"#fff\" /> :\r\n                                <FontAwesomeIcon icon={faMinus} color=\"#fff\" />}\r\n                        </div>\r\n                    </Accordion.Header>\r\n                    <Accordion.Body>\r\n                            <Card.Text className=\"projectDetails ps-0\">\r\n                                <p><b>{t(\"Status\")}</b>:<span> {props.project.status ? props.project.status[\"title\"] : \"\"}</span></p>\r\n                                <p><b>{t(\"Created\")}</b>:<span> {props.project.created_at ? (moment(props.project.created_at).format(formatDate)) : null} </span></p>\r\n                                <p><b>{t(\"EndDate\")}</b>:<span> {props.project.end_date ? (moment(props.project.end_date).format(formatDateStartDate)) : null}</span></p>\r\n                                <p><b>{t(\"LastModified\")}</b>:<span> {props.project.updated_at ? (moment(props.project.updated_at).format(formatDate)) : null} </span></p>\r\n                                <p><b>{t(\"WeblinktoProject\")}</b>:\r\n\t\t\t\t\t\t\t\t<span>\r\n\t\t\t\t\t\t\t\t\t{props.project?.website && (\r\n\t\t\t\t\t\t\t\t\t\t<a href={props.project.website} target={\"_blank\"}>\r\n\t\t\t\t\t\t\t\t\t\t{props.project.website}\r\n\t\t\t\t\t\t\t\t\t\t</a>\r\n\t\t\t\t\t\t\t\t\t)}\r\n\t\t\t\t\t\t\t\t</span>\r\n                                </p>\r\n                            </Card.Text>\r\n                    </Accordion.Body>\r\n                </Accordion.Item>\r\n        </>\r\n    )\r\n}\r\n\r\nexport default ProjectDetailsAccordion;", "//Import Library\r\nimport { useEffect, useState } from \"react\";\r\nimport Link from 'next/link';\r\n\r\n//Import services/components\r\nimport RKITable from \"../../components/common/RKITable\";\r\nimport apiService from \"../../services/apiService\";\r\nimport { useTranslation } from 'next-i18next';\r\n\r\ninterface VspaceTableProps {\r\n  vspaceData: any;\r\n  vspaceDataLoading: boolean;\r\n  vspaceDataTotalRows: number;\r\n  vspaceDataPerPage: number;\r\n  vspaceDataCurrentPage: number;\r\n  type: string;\r\n  id: string;\r\n}\r\n\r\nconst VspaceTable = (props: VspaceTableProps) => {\r\n  const { t } = useTranslation('common');\r\n  const { type, id } = props;\r\n  const [tabledata, setDataToTable] = useState([]);\r\n  const [loading, setLoading] = useState(false);\r\n  const [totalRows, setTotalRows] = useState(0);\r\n  const [perPage, setPerPage] = useState(10);\r\n  const [resetPaginationToggle] = useState(false);\r\n\r\n  const vSpaceParams = {\r\n    sort: { created_at: \"asc\" },\r\n    limit: perPage,\r\n    page: 1,\r\n    query: {},\r\n  };\r\n\r\n  const columns = [\r\n    {\r\n      name: t(\"Title\"),\r\n      selector: \"title\",\r\n      cell: (d: any) => d && d.title && d._id ? <Link href=\"/vspace/[...routes]\" as={`/vspace/show/${d._id}`} >{d.title}</Link> : \"\",\r\n    },\r\n    {\r\n      name: t(\"Owner\"),\r\n      selector: \"users\",\r\n      cell: (d: any) => d && d.user && d.user.firstname ? `${d.user.firstname} ${d.user.lastname}` : \"\"\r\n    },\r\n    {\r\n      name: t(\"PublicPrivate\"),\r\n      selector: \"visibility\",\r\n      cell: (d: any) => d && d.visibility ? \"Public\" : \"Private\",\r\n\r\n    },\r\n    {\r\n      name: t(\"NumberofMembers\"),\r\n      selector: \"members\",\r\n      cell: (d: any) => d && d.members ? d.members.length : \"-\",\r\n    }\r\n  ];\r\n\r\n  const getLinkedVspace = async (vSpaceParams1: any) => {\r\n    setLoading(true);\r\n    const response = await apiService.get(`stats/get${type}WithVspace/${id}`, vSpaceParams);\r\n    if (response) {\r\n      type === \"Operation\" ? setDataToTable(response.operation) : setDataToTable(response.project);\r\n      setTotalRows(response.totalCount);\r\n      setLoading(false);\r\n    }\r\n  };\r\n\r\n  const handlePageChange = (page: number) => {\r\n    vSpaceParams.limit = perPage;\r\n    vSpaceParams.page = page;\r\n    getLinkedVspace(vSpaceParams);\r\n  };\r\n\r\n  const handlePerRowsChange = async (newPerPage: number, page: number) => {\r\n    vSpaceParams.limit = newPerPage;\r\n    vSpaceParams.page = page;\r\n    setLoading(true);\r\n    const response = await apiService.get(`stats/get${type}WithVspace/${id}`, vSpaceParams);\r\n    if (response) {\r\n      type === \"Operation\" ? setDataToTable(response.operation) : setDataToTable(response.project);\r\n      setPerPage(newPerPage);\r\n      setLoading(false);\r\n    }\r\n  };\r\n\r\n  useEffect(() => {\r\n    getLinkedVspace(vSpaceParams);\r\n  }, []);\r\n\r\n\r\n\r\n  return (\r\n    <div>\r\n      <RKITable\r\n        columns={columns}\r\n        data={tabledata}\r\n        totalRows={totalRows}\r\n        loading={loading}\r\n        resetPaginationToggle={resetPaginationToggle}\r\n        handlePerRowsChange={handlePerRowsChange}\r\n        handlePageChange={handlePageChange}\r\n      />\r\n    </div>\r\n  );\r\n}\r\n\r\nexport default VspaceTable;\r\n", "//Import Library\r\nimport React from 'react';\r\nimport connectedAuthWrapper from 'redux-auth-wrapper/connectedAuthWrapper';\r\n\r\n//Import services/components\r\nimport R403 from \"../r403\";\r\n\r\nexport const canAddProject = connectedAuthWrapper({\r\n  authenticatedSelector: (state) => {\r\n    if (state.permissions && state.permissions.project && state.permissions.project['create:any']) {\r\n      return true;\r\n    }\r\n    return false;\r\n  },\r\n  wrapperDisplayName: 'CanAddProject',\r\n});\r\n\r\nexport const canAddProjectForm = connectedAuthWrapper({\r\n  authenticatedSelector: (state) => {\r\n    if (state.permissions && state.permissions.project && state.permissions.project['create:any']) {\r\n      return true;\r\n    }\r\n    return false;\r\n  },\r\n  wrapperDisplayName: 'CanAddProjectForm',\r\n  FailureComponent: () => <R403/>\r\n});\r\n\r\nexport const canEditProject = connectedAuthWrapper({\r\n  authenticatedSelector: (state, props) => {\r\n    if (state.permissions && state.permissions.project) {\r\n      if (state.permissions.project['update:any']) {\r\n        return true;\r\n      } else { //if update:own\r\n        if (state.permissions.project['update:own']) {\r\n          if (props.project && props.project.user && props.project.user._id === state.user._id) {\r\n            return true;\r\n          }  \r\n        }\r\n      }\r\n    }\r\n    return false;\r\n  },\r\n  wrapperDisplayName: 'CanEditProject',\r\n});\r\n\r\nexport const canEditProjectForm = connectedAuthWrapper({\r\n  authenticatedSelector: (state, props) => {\r\n    if (state.permissions && state.permissions.project) {\r\n      if (state.permissions.project['update:any']) {\r\n        return true;\r\n      } else { //if update:own\r\n        if (state.permissions.project['update:own']) {\r\n          if (props.project && props.project.user && props.project.user._id === state.user._id) {\r\n            return true;\r\n          }\r\n        }\r\n      }\r\n    }\r\n    return false;\r\n  },\r\n  wrapperDisplayName: 'CanEditProjectForm',\r\n  FailureComponent: () => <R403/>\r\n});\r\n\r\nexport const canViewDiscussionUpdate = connectedAuthWrapper({\r\n  authenticatedSelector: (state) => {\r\n    if (state.permissions && state.permissions.update && state.permissions.update['read:any']) {\r\n      return true;\r\n    }\r\n    return false;\r\n  },\r\n  wrapperDisplayName: 'CanViewDiscussionUpdate',\r\n});\r\n\r\nexport default canAddProject;", "//Import Library\r\nimport React from 'react'\r\nimport DataTable  from 'react-data-table-component';\r\n\r\n//Import services/components\r\nimport { useTranslation } from 'next-i18next';\r\nimport CustomLoader from './CustomLoader';\r\n\r\ninterface RKITableProps {\r\n  columns: any[];\r\n  data: any[];\r\n  totalRows?: number;\r\n  resetPaginationToggle?: boolean;\r\n  subheader?: boolean;\r\n  subHeaderComponent?: React.ReactNode;\r\n  handlePerRowsChange?: (newPerPage: number, page: number) => void;\r\n  handlePageChange?: (page: number) => void;\r\n  rowsPerPage?: number[];\r\n  defaultRowsPerPage?: number;\r\n  selectableRows?: boolean;\r\n  loading?: boolean;\r\n  pagServer?: boolean;\r\n  onSelectedRowsChange?: (selectedRows: any) => void;\r\n  clearSelectedRows?: boolean;\r\n  sortServer?: boolean;\r\n  onSort?: (column: any, sortDirection: string) => void;\r\n  persistTableHead?: boolean;\r\n  sortFunction?: (rows: any[], field: any, direction: 'asc' | 'desc') => any[];\r\n  noHeader?: boolean;\r\n  dense?: boolean;\r\n  pagination?: boolean;\r\n  paginationServer?: boolean;\r\n  paginationTotalRows?: number;\r\n  subHeaderAlign?: string;\r\n  [key: string]: any;\r\n}\r\n\r\nfunction RKITable(props: RKITableProps) {\r\n  const { t } = useTranslation('common');\r\n  const paginationComponentOptions = {\r\n    rowsPerPageText: t('Rowsperpage'),\r\n    };\r\n  const {\r\n    columns,\r\n    data,\r\n    totalRows,\r\n    resetPaginationToggle,\r\n    subheader,\r\n    subHeaderComponent,\r\n    handlePerRowsChange,\r\n    handlePageChange,\r\n    rowsPerPage,\r\n    defaultRowsPerPage,\r\n    selectableRows,\r\n    loading,\r\n    pagServer,\r\n    onSelectedRowsChange,\r\n    clearSelectedRows,\r\n    sortServer,\r\n    onSort,\r\n    persistTableHead,\r\n    sortFunction,\r\n    // Filter out any unknown props that might cause DOM warnings\r\n    ...otherProps\r\n  } = props;\r\n\r\n  // Only pass known DataTable props to avoid DOM warnings\r\n  const dataTableProps = {\r\n    paginationComponentOptions,\r\n    noDataComponent: t(\"NoData\"),\r\n    noHeader: true,\r\n    columns,\r\n    data: data || [],\r\n    dense: true,\r\n    paginationResetDefaultPage: resetPaginationToggle,\r\n    subHeader: subheader,\r\n    progressPending: loading,\r\n    subHeaderComponent,\r\n    pagination: true,\r\n    paginationServer: pagServer,\r\n    paginationPerPage: defaultRowsPerPage ? defaultRowsPerPage : 10,\r\n    paginationRowsPerPageOptions: rowsPerPage ? rowsPerPage : [10, 15, 20, 25, 30],\r\n    paginationTotalRows: totalRows,\r\n    onChangeRowsPerPage: handlePerRowsChange,\r\n    onChangePage: handlePageChange,\r\n    selectableRows,\r\n    onSelectedRowsChange,\r\n    clearSelectedRows,\r\n    progressComponent: <CustomLoader/>,\r\n    sortIcon: <i className=\"sort-icon fas fa-exchange-alt\" />,\r\n    sortServer,\r\n    onSort,\r\n    sortFunction,\r\n    persistTableHead,\r\n    className: \"rki-table\"\r\n  };\r\n  return (\r\n    <DataTable {...dataTableProps} />\r\n  )\r\n};\r\n\r\nRKITable.defaultProps = {\r\n  subHeader: false,\r\n  pagination: true,\r\n  totalRows: null,\r\n  pagServer: true,\r\n  onSelectedRowsChange: null,\r\n  clearSelectedRows: false,\r\n  sortServer: false,\r\n  persistTableHead: false\r\n}\r\n\r\nexport default RKITable;\r\n\r\n", "//Import Library\r\nimport React, { useState } from \"react\";\r\nimport { faMinus, faPlus } from \"@fortawesome/free-solid-svg-icons\";\r\nimport { FontAwesomeIcon } from \"@fortawesome/react-fontawesome\";\r\nimport { Accordion, Card } from \"react-bootstrap\";\r\n\r\n//Import services/components\r\nimport VspaceTable from \"../../../components/common/VspaceTable\";\r\nimport { useTranslation } from 'next-i18next';\r\n\r\n\r\ninterface VirtualSpaceAccordionProps {\r\n  routeData: {\r\n    routes: string[];\r\n  };\r\n}\r\n\r\nconst VirtualSpaceAccordion = (props: VirtualSpaceAccordionProps) => {\r\n    const { t } = useTranslation('common');\r\n    const [section, setSection] = useState(false);\r\n    return (\r\n        <>\r\n            <Accordion.Item eventKey=\"0\">\r\n                <Accordion.Header onClick={() => setSection(!section)}>\r\n                    <div className=\"cardTitle\">{t(\"LinkedVirtualSpace\")}</div>\r\n                    <div className=\"cardArrow\">\r\n                        {section ? (\r\n                            <FontAwesomeIcon icon={faMinus} color=\"#fff\" />\r\n                        ) : (\r\n                            <FontAwesomeIcon icon={faPlus} color=\"#fff\" />\r\n                        )}\r\n                    </div>\r\n                </Accordion.Header>\r\n                <Accordion.Body>\r\n                    <VspaceTable\r\n                      id={props.routeData?.routes?.[1] || ''}\r\n                      type=\"Project\"\r\n                      vspaceData={[]}\r\n                      vspaceDataLoading={false}\r\n                      vspaceDataTotalRows={0}\r\n                      vspaceDataPerPage={10}\r\n                      vspaceDataCurrentPage={1}\r\n                    />\r\n                </Accordion.Body>\r\n            </Accordion.Item>\r\n        </>\r\n    )\r\n}\r\nexport default VirtualSpaceAccordion;", "//Import Library\r\nimport React, { useState } from \"react\";\r\nimport { Accordion, Card } from \"react-bootstrap\";\r\nimport { FontAwesomeIcon } from \"@fortawesome/react-fontawesome\";\r\nimport { faMinus, faPlus } from \"@fortawesome/free-solid-svg-icons\";\r\n\r\n//Import services/components\r\nimport { useTranslation } from 'next-i18next';\r\nimport Discussion from \"../../../components/common/disussion\";\r\n\r\ninterface DiscussionAccordionProps {\r\n  routeData: {\r\n    routes: string[];\r\n  };\r\n}\r\n\r\nconst DiscussionAccordion = (props: DiscussionAccordionProps) => {\r\n    const { t } = useTranslation('common');\r\n    const [section, setSection] = useState(true);\r\n    return (\r\n        <Accordion.Item eventKey=\"2\">\r\n            <Accordion.Header onClick={() => setSection(!section)}>\r\n                <div className=\"cardTitle\">{t(\"Discussions\")}</div>\r\n                <div className=\"cardArrow\">\r\n                    {section ? <FontAwesomeIcon icon={faPlus} color=\"#fff\" /> :\r\n                        <FontAwesomeIcon icon={faMinus} color=\"#fff\" />}\r\n                </div>\r\n            </Accordion.Header>\r\n            <Accordion.Body>\r\n                <Discussion type='project' id={ props?.routeData?.routes ? props.routeData.routes[1] : ''} />\r\n            </Accordion.Body>\r\n        </Accordion.Item>\r\n    )\r\n};\r\n\r\nexport default DiscussionAccordion;", "//! moment.js locale configuration\n//! locale : German [de]\n//! author : lluchs : https://github.com/lluchs\n//! author: <PERSON><PERSON><PERSON>: https://github.com/Oire\n//! author : <PERSON><PERSON><PERSON><PERSON> : https://github.com/mik01aj\n\n;(function (global, factory) {\n   typeof exports === 'object' && typeof module !== 'undefined'\n       && typeof require === 'function' ? factory(require('../moment')) :\n   typeof define === 'function' && define.amd ? define(['../moment'], factory) :\n   factory(global.moment)\n}(this, (function (moment) { 'use strict';\n\n    //! moment.js locale configuration\n\n    function processRelativeTime(number, withoutSuffix, key, isFuture) {\n        var format = {\n            m: ['eine Minute', 'einer Minute'],\n            h: ['eine Stunde', 'einer Stunde'],\n            d: ['ein Tag', 'einem Tag'],\n            dd: [number + ' Tage', number + ' Tagen'],\n            w: ['eine Woche', 'einer Woche'],\n            M: ['ein <PERSON><PERSON>', 'einem Monat'],\n            MM: [number + ' <PERSON><PERSON>', number + ' Monaten'],\n            y: ['ein Jahr', 'einem Jahr'],\n            yy: [number + ' Jahre', number + ' Jahren'],\n        };\n        return withoutSuffix ? format[key][0] : format[key][1];\n    }\n\n    var de = moment.defineLocale('de', {\n        months: 'Januar_Februar_März_April_Mai_Juni_Juli_August_September_Oktober_November_Dezember'.split(\n            '_'\n        ),\n        monthsShort:\n            'Jan._Feb._März_Apr._Mai_Juni_Juli_Aug._Sep._Okt._Nov._Dez.'.split('_'),\n        monthsParseExact: true,\n        weekdays:\n            'Sonntag_Montag_Dienstag_Mittwoch_Donnerstag_Freitag_Samstag'.split(\n                '_'\n            ),\n        weekdaysShort: 'So._Mo._Di._Mi._Do._Fr._Sa.'.split('_'),\n        weekdaysMin: 'So_Mo_Di_Mi_Do_Fr_Sa'.split('_'),\n        weekdaysParseExact: true,\n        longDateFormat: {\n            LT: 'HH:mm',\n            LTS: 'HH:mm:ss',\n            L: 'DD.MM.YYYY',\n            LL: 'D. MMMM YYYY',\n            LLL: 'D. MMMM YYYY HH:mm',\n            LLLL: 'dddd, D. MMMM YYYY HH:mm',\n        },\n        calendar: {\n            sameDay: '[heute um] LT [Uhr]',\n            sameElse: 'L',\n            nextDay: '[morgen um] LT [Uhr]',\n            nextWeek: 'dddd [um] LT [Uhr]',\n            lastDay: '[gestern um] LT [Uhr]',\n            lastWeek: '[letzten] dddd [um] LT [Uhr]',\n        },\n        relativeTime: {\n            future: 'in %s',\n            past: 'vor %s',\n            s: 'ein paar Sekunden',\n            ss: '%d Sekunden',\n            m: processRelativeTime,\n            mm: '%d Minuten',\n            h: processRelativeTime,\n            hh: '%d Stunden',\n            d: processRelativeTime,\n            dd: processRelativeTime,\n            w: processRelativeTime,\n            ww: '%d Wochen',\n            M: processRelativeTime,\n            MM: processRelativeTime,\n            y: processRelativeTime,\n            yy: processRelativeTime,\n        },\n        dayOfMonthOrdinalParse: /\\d{1,2}\\./,\n        ordinal: '%d.',\n        week: {\n            dow: 1, // Monday is the first day of the week.\n            doy: 4, // The week that contains Jan 4th is the first week of the year.\n        },\n    });\n\n    return de;\n\n})));\n"], "names": ["props", "CanViewDiscussionUpdate", "canViewDiscussionUpdate", "ProjectAccordianSection", "DiscussionAccordion", "routeData", "Row", "Col", "className", "xs", "Accordion", "ProjectDetailsAccordion", "project", "projectData", "VirtualSpaceAccordion", "formatDate", "t", "useTranslation", "section", "setSection", "useState", "<PERSON><PERSON>", "eventKey", "Header", "onClick", "div", "FontAwesomeIcon", "icon", "faPlus", "color", "faMinus", "Body", "Card", "Text", "p", "b", "span", "status", "created_at", "moment", "format", "end_date", "formatDateStartDate", "updated_at", "website", "a", "href", "target", "VspaceTable", "type", "id", "tabledata", "setDataToTable", "loading", "setLoading", "totalRows", "setTotalRows", "perPage", "setPerPage", "resetPaginationToggle", "vSpaceParams", "sort", "limit", "page", "query", "columns", "name", "selector", "cell", "d", "title", "_id", "Link", "as", "user", "firstname", "lastname", "visibility", "members", "length", "getLinkedVspace", "vSpaceParams1", "response", "apiService", "get", "operation", "totalCount", "handlePerRowsChange", "newPerPage", "useEffect", "RKITable", "data", "handlePageChange", "canAddProject", "connectedAuthWrapper", "authenticatedSelector", "state", "permissions", "wrapperDisplayName", "FailureComponent", "R403", "canEditProject", "update", "paginationComponentOptions", "rowsPerPageText", "subheader", "subHeaderComponent", "rowsPerPage", "defaultRowsPerPage", "selectableRows", "pagServer", "onSelectedRowsChange", "clearSelectedRows", "sortServer", "onSort", "persistTableHead", "sortFunction", "otherProps", "dataTableProps", "noDataComponent", "<PERSON><PERSON><PERSON><PERSON>", "dense", "paginationResetDefaultPage", "subHeader", "progressPending", "pagination", "paginationServer", "paginationPerPage", "paginationRowsPerPageOptions", "paginationTotalRows", "onChangeRowsPerPage", "onChangePage", "progressComponent", "CustomLoader", "sortIcon", "i", "DataTable", "defaultProps", "routes", "vspaceData", "vspaceDataLoading", "vspaceDataTotalRows", "vspaceDataPerPage", "vspaceDataCurrentPage", "Discussion"], "sourceRoot": "", "ignoreList": [7]}