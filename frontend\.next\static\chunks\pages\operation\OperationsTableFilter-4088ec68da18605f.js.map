{"version": 3, "file": "static/chunks/pages/operation/OperationsTableFilter-4088ec68da18605f.js", "mappings": "kOAmFA,MA1E8B,OAAC,YAC7BA,CAAU,QAyEGC,EAxEbC,CAAQ,kBAwE0BD,EAAC,EAvEnCE,CAAoB,CACpBC,SAAO,cACPC,CAAY,CAOb,GACO,CAACC,EAAQC,EAAU,CAAGC,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAC,EAAE,EACjC,GAAEC,CAAC,CAAE,CAAGC,CAAAA,EAAAA,EAAAA,EAAAA,CAAcA,CAAC,UAEvBC,EAAsB,MAAOC,IACjC,IAAMC,EAAW,MAAMC,EAAAA,CAAUA,CAACC,GAAG,CAAC,oBAAqBH,GACvDC,GAAYG,MAAMC,OAAO,CAACJ,EAASK,IAAI,GAAG,EAClCL,EAASK,IAAI,CAE3B,EASA,MAPAC,CAAAA,EAAAA,EAAAA,SAAAA,CAASA,CAAC,KACRR,EAAoB,CAClBS,MAAO,CAAC,EACRC,KAAM,CAAEC,MAAO,KAAM,CACvB,EACF,EAAG,EAAE,EAGH,UAACC,EAAAA,CAASA,CAAAA,CAACC,KAAK,IAACC,UAAU,eACzB,WAACC,EAAAA,CAAGA,CAAAA,WACF,UAACC,EAAAA,CAAGA,CAAAA,CAACC,GAAI,EAAGH,UAAU,oCACpB,UAACI,EAAAA,CAAWA,CAAAA,CACVC,KAAK,OACLL,UAAU,cACVM,YAAatB,EAAE,UACfuB,aAAW,SACXC,MAAOjC,EACPkC,SAAUhC,MAGd,UAACyB,EAAAA,CAAGA,CAAAA,CAACC,GAAI,WACP,UAACO,EAAAA,CAAIA,CAAAA,UACH,WAACA,EAAAA,CAAIA,CAACC,KAAK,EAACC,GAAIX,EAAAA,CAAGA,CAAEY,UAAU,yBAC7B,UAACH,EAAAA,CAAIA,CAACI,KAAK,EAACC,MAAM,IAACC,GAAG,IAAIC,GAAG,aAC5BjC,EAAE,YAEH,UAACkB,EAAAA,CAAGA,CAAAA,CAACF,UAAU,qBACb,WAACI,EAAAA,CAAWA,CAAAA,CACVQ,GAAG,SACHL,aAAW,SACXE,SAAU/B,EACV8B,MAAO5B,YAEP,UAACsC,SAAAA,CAAOV,MAAO,YAAI,QAClB3B,EAAOsC,GAAG,CAAC,CAACC,EAAWC,IAEpB,UAACH,SAAAA,CAAmBV,MAAOY,EAAKE,GAAG,UAChCF,EAAKvB,KAAK,EADAwB,oBAanC,mBChFA,4CACA,mCACA,WACA,OAAe,EAAQ,KAAwD,CAC/E,EACA,SAFsB", "sources": ["webpack://_N_E/./pages/operation/OperationsTableFilter.tsx", "webpack://_N_E/?b852"], "sourcesContent": ["//Import Library\r\nimport { useEffect, useState } from \"react\";\r\nimport { Col, Container, FormControl, Form, Row } from \"react-bootstrap\";\r\n\r\n//Import services/components\r\nimport apiService from \"../../services/apiService\";\r\nimport { useTranslation } from 'next-i18next';\r\n\r\n\r\nconst OperationsTableFilter = ({\r\n  filterText,\r\n  onFilter,\r\n  onFilterStatusChange,\r\n  onClear,\r\n  filterStatus,\r\n}: {\r\n  filterText: any,\r\n  onFilter: any,\r\n  onFilterStatusChange: any,\r\n  onClear: any,\r\n  filterStatus: any,\r\n}) => {\r\n  const [status, setStatus] = useState([]);\r\n  const { t } = useTranslation('common');\r\n\r\n  const getOperationsStatus = async (operationParams: any) => {\r\n    const response = await apiService.get(\"/operation_status\", operationParams);\r\n    if (response && Array.isArray(response.data)) { \r\n      setStatus(response.data);\r\n    }\r\n  };\r\n\r\n  useEffect(() => {\r\n    getOperationsStatus({\r\n      query: {},\r\n      sort: { title: \"asc\" },\r\n    });\r\n  }, []);\r\n\r\n  return (\r\n    <Container fluid className=\"p-0\">\r\n      <Row>\r\n        <Col xs={6} className=\"ps-0 align-self-end mb-3\">\r\n          <FormControl\r\n            type=\"text\"\r\n            className=\"searchInput\"\r\n            placeholder={t(\"search\")}\r\n            aria-label=\"Search\"\r\n            value={filterText}\r\n            onChange={onFilter}\r\n          />\r\n        </Col>\r\n        <Col xs={6}>\r\n          <Form>\r\n            <Form.Group as={Row} controlId=\"statusFilter\">\r\n              <Form.Label column sm=\"3\" lg=\"2\">\r\n              {t(\"Status\")}\r\n              </Form.Label>\r\n              <Col className=\"ps-0 pe-1\">\r\n                <FormControl\r\n                  as=\"select\"\r\n                  aria-label=\"Status\"\r\n                  onChange={onFilterStatusChange}\r\n                  value={filterStatus}\r\n                >\r\n                  <option value={\"\"}>All</option>\r\n                  {status.map((item: any, index) => {\r\n                    return (\r\n                      <option key={index} value={item._id}>\r\n                        {item.title}\r\n                      </option>\r\n                    );\r\n                  })}\r\n                </FormControl>\r\n              </Col>\r\n            </Form.Group>\r\n          </Form>\r\n        </Col>\r\n      </Row>\r\n    </Container>\r\n  );\r\n};\r\n\r\nexport default OperationsTableFilter;\r\n", "\n    (window.__NEXT_P = window.__NEXT_P || []).push([\n      \"/operation/OperationsTableFilter\",\n      function () {\n        return require(\"private-next-pages/operation/OperationsTableFilter.tsx\");\n      }\n    ]);\n    if(module.hot) {\n      module.hot.dispose(function () {\n        window.__NEXT_P.push([\"/operation/OperationsTableFilter\"])\n      });\n    }\n  "], "names": ["filterText", "OperationsTableFilter", "onFilter", "onFilterStatusChange", "onClear", "filterStatus", "status", "setStatus", "useState", "t", "useTranslation", "getOperationsStatus", "operationParams", "response", "apiService", "get", "Array", "isArray", "data", "useEffect", "query", "sort", "title", "Container", "fluid", "className", "Row", "Col", "xs", "FormControl", "type", "placeholder", "aria-label", "value", "onChange", "Form", "Group", "as", "controlId", "Label", "column", "sm", "lg", "option", "map", "item", "index", "_id"], "sourceRoot": "", "ignoreList": []}