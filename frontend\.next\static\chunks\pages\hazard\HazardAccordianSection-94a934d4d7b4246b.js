(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[414],{33458:(e,s,a)=>{"use strict";a.d(s,{A:()=>d});var i=a(37876),c=a(14232),l=a(65418),n=a(21772),r=a(11041),o=a(60282),t=a(31753);a(57637);let d=e=>{let{t:s}=(0,t.Bd)("common"),[a,d]=(0,c.useState)([]),m=e=>{let a=/(http|https):\/\/(\w+:{0,1}\w*)?(\S+)(:[0-9]+)?(\/|\/([\w#!:.?+=&%!\-\/]))?/.test(e.description);return(0,i.jsxs)("div",{className:"carousel-legend",children:[(0,i.jsxs)("p",{className:"lead",children:[(0,i.jsx)("b",{children:s("Filename")})," ",e.originalName||"No Name found"]}),e.description&&(0,i.jsxs)("div",{className:"source_link",children:[(0,i.jsx)("p",{children:(0,i.jsx)("b",{children:s("Source")})}),a?(0,i.jsxs)("div",{children:[(0,i.jsx)(n.g,{icon:r.CQO,size:"1x",color:"#999",className:"me-1"}),(0,i.jsx)("a",{className:"source_link",href:e.description,target:"_blank",rel:"noopener noreferrer",children:e.description})]}):(0,i.jsx)("div",{children:(0,i.jsx)("p",{className:"ps-0 py-0",style:{wordBreak:"break-all"},children:e.description})})]}),e.downloadLink&&(0,i.jsxs)(o.A,{className:"btn btn-success mt-2 btn--download",href:e.downloadLink,children:[s("Download"),(0,i.jsx)(n.g,{icon:r.cbP,size:"1x",className:"ms-1"})]})]})};return(0,c.useEffect)(()=>{let s=[];e&&e.gallery&&Array.isArray(e.gallery)&&e.gallery.map((a,i)=>{let c,l=a&&a.name.split(".").pop();switch(l){case"JPG":case"jpg":case"jpeg":case"png":c="".concat("http://localhost:3001/api/v1","/image/show/").concat(a._id);break;case"pdf":c="/images/fileIcons/pdfFile.png";break;case"docx":c="/images/fileIcons/wordFile.png";break;case"xls":case"xlsx":c="/images/fileIcons/xlsFile.png";break;default:c="/images/fileIcons/otherFile.png"}let n=("docx"===l||"pdf"===l||"xls"===l||"xlsx"===l)&&"".concat("http://localhost:3001/api/v1","/files/download/").concat(a._id),r="".concat(a&&a.original_name?a.original_name:"No Name found"),o=e.imageSource&&Array.isArray(e.imageSource)&&e.imageSource.length>0?e.imageSource[i]:"";s.push({src:c,description:o,originalName:r,downloadLink:n})}),d(s)},[e]),(0,i.jsx)("div",{children:a&&0===a.length?(0,i.jsx)("div",{className:"border border-info my-3 mx-0",children:(0,i.jsx)("p",{className:"d-flex d-flex justify-content-center p-2 m-0",children:s("NoFilesFound!")})}):(0,i.jsx)(l.FN,{showThumbs:!0,showStatus:!0,showIndicators:!0,infiniteLoop:!0,useKeyboardArrows:!0,autoPlay:!1,stopOnHover:!0,swipeable:!0,dynamicHeight:!1,emulateTouch:!0,renderThumbs:()=>a.map((e,s)=>(0,i.jsx)("img",{src:e.src,alt:"Thumbnail ".concat(s+1),style:{width:"60px",height:"60px",objectFit:"cover"}},s)),children:a.map((e,s)=>(0,i.jsxs)("div",{children:[(0,i.jsx)("img",{src:e.src,alt:e.originalName||"Gallery image",style:{maxHeight:"500px",objectFit:"contain"}}),m(e)]},s))})})}},91676:(e,s,a)=>{(window.__NEXT_P=window.__NEXT_P||[]).push(["/hazard/HazardAccordianSection",function(){return a(9227)}])}},e=>{var s=s=>e(e.s=s);e.O(0,[7725,9773,1772,7126,8477,2317,636,6593,8792],()=>s(91676)),_N_E=e.O()}]);
//# sourceMappingURL=HazardAccordianSection-94a934d4d7b4246b.js.map