{"version": 3, "file": "static/chunks/pages/institution/InfoPopup-6e90be5a07d36626.js", "mappings": "gFACA,4CACA,yBACA,WACA,OAAe,EAAQ,KAA8C,CACrE,EACA,SAFsB,2HC8CtB,MA1CkB,IAChB,GAAM,GAAEA,CAAC,CAAE,CAAGC,CAAAA,EAAAA,EAAAA,EAAAA,CAAcA,CAAC,MAyChBC,IAxCP,CAAEC,IAwCcD,EAAC,EAxCT,SAAEE,CAAO,MAAEC,CAAI,MAAEC,CAAI,CAAE,CAAGC,EAClCC,EAAiB,aAATF,EAAsB,UAmC3BA,EAnCuCG,aAmCjB,cAAgB,YAlCzCC,EACJL,EAAKM,MAAM,CAAG,EACZN,EAAKO,GAAG,CAAC,CAACC,EAAWC,IACnB,WAACC,OAAAA,WACC,UAACC,IAAIA,CAACC,KAAM,IAAkBJ,MAAAA,CAAdL,EAAM,UAAiB,OAATK,EAAKK,GAAG,IAAjCF,OACFH,EAAKM,KAAK,GAEb,UAACC,KAAAA,CAAAA,KAJQN,IAQb,WAACO,IAAAA,WACErB,EAAE,MAAM,IAAEM,EAAK,IAAEN,EAAE,SAAS,OAInC,MACE,WAACsB,EAAAA,CAAKA,CAAAA,CACJC,QAAQ,IACRC,KAAK,KACLC,KAAMtB,EACNuB,OAAQ,IAAMtB,EAAQ,CAACD,GACvBwB,kBAAgB,wBAEhB,UAACL,EAAAA,CAAKA,CAACM,MAAM,EAACC,WAAW,aACvB,UAACP,EAAAA,CAAKA,CAACQ,KAAK,WAAExB,MAEhB,UAACgB,EAAAA,CAAKA,CAACS,IAAI,WACT,UAACC,MAAAA,UAAKtB,QAQd", "sources": ["webpack://_N_E/?ee25", "webpack://_N_E/./pages/institution/InfoPopup.tsx"], "sourcesContent": ["\n    (window.__NEXT_P = window.__NEXT_P || []).push([\n      \"/institution/InfoPopup\",\n      function () {\n        return require(\"private-next-pages/institution/InfoPopup.tsx\");\n      }\n    ]);\n    if(module.hot) {\n      module.hot.dispose(function () {\n        window.__NEXT_P.push([\"/institution/InfoPopup\"])\n      });\n    }\n  ", "//Import Library\r\nimport React from \"react\";\r\nimport { Modal } from \"react-bootstrap\";\r\nimport Link from \"next/link\";\r\n\r\n//Import services/components\r\nimport { useTranslation } from 'next-i18next';\r\n\r\nconst InfoPopup = (props: any) => {\r\n  const { t } = useTranslation('common');\r\n  const { isShow, isClose, data, name } = props;\r\n  const route = name === \"Projects\" ? \"project\" : arrowfun();\r\n  const titles =\r\n    data.length > 0 ? (\r\n      data.map((item: any, i: any) => (\r\n        <span key={i}>\r\n          <Link href={`/${route}/show/${item._id}`}>\r\n            {item.title}\r\n          </Link>\r\n          <hr />\r\n        </span>\r\n      ))\r\n    ) : (\r\n      <p>\r\n        {t(\"No\")} {name} {t(\"Found\")}.\r\n      </p>\r\n    );\r\n\r\n  return (\r\n    <Modal\r\n      centered\r\n      size=\"sm\"\r\n      show={isShow}\r\n      onHide={() => isClose(!isShow)}\r\n      aria-labelledby=\"modal_popup\"\r\n    >\r\n      <Modal.Header closeButton>\r\n        <Modal.Title>{name}</Modal.Title>\r\n      </Modal.Header>\r\n      <Modal.Body>\r\n        <div>{titles}</div>\r\n      </Modal.Body>\r\n    </Modal>\r\n  );\r\n\r\n  function arrowfun() {\r\n    return name === \"Partners\" ? \"institution\" : \"operation\";\r\n  }\r\n};\r\n\r\nexport default InfoPopup;\r\n"], "names": ["t", "useTranslation", "InfoPopup", "isShow", "isClose", "data", "name", "props", "route", "arrowfun", "titles", "length", "map", "item", "i", "span", "Link", "href", "_id", "title", "hr", "p", "Modal", "centered", "size", "show", "onHide", "aria-<PERSON>by", "Header", "closeButton", "Title", "Body", "div"], "sourceRoot": "", "ignoreList": []}