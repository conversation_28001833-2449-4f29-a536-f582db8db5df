"use strict";exports.id=4710,exports.ids=[4710],exports.modules={4710:(e,i,t)=>{t.a(e,async(e,a)=>{try{t.r(i),t.d(i,{default:()=>N});var s=t(8732),n=t(82015),l=t(7082),r=t(18597),c=t(83551),d=t(49481),o=t(59549),m=t(25161),p=t(63899),g=t(91353),x=t(44233),u=t.n(x),h=t(23579),f=t(66994),j=t(42893),y=t(19918),A=t.n(y),b=t(63487),v=t(98178),w=t(88751),C=t(24047),I=e([j,b,v]);[j,b,v]=I.then?(await I)():I;let N=e=>{let{t:i,i18n:t}=(0,w.useTranslation)("common"),a=e.routes&&"edit_landing"===e.routes[0]&&e.routes[1],x={title:"",description:"",pageCategory:"",isEnabled:!0,images:[],images_src:[],language:t.language},[y,I]=(0,n.useState)(x),[N,S]=(0,n.useState)([]),[_,E]=(0,n.useState)([]),[D,k]=(0,n.useState)([]),[F,P]=(0,n.useState)([]),U=e=>{I(i=>({...i,language:e.abbr}))},L={query:{},sort:{title:"asc"},limit:"~",select:"-_id -created_at -updated_at"},T=async()=>{let e=await b.A.get("/language",L);e&&k(e.data)},z=e=>{I(i=>({...i,description:e}))},G=e=>{let i=e.map(e=>e.serverID);I(e=>({...e,images:i}))},R=e=>{I(i=>({...i,images_src:e}))},B=async(t,s)=>{let n,l;t.preventDefault();let r={title:y.title.trim(),description:y.description,isEnabled:y.isEnabled,images:y.images,images_src:y.images_src,language:y.language};a?(l="adminsetting.landing.form.EditableContentisupdatedsuccessfully",n=await b.A.patch(`/landingPage/${e.routes[1]}`,r)):(l="adminsetting.landing.form.EditableContentisaddedsuccessfully",n=await b.A.post("/landingPage",r)),n&&n._id?(j.default.success(i(l)),u().push("/adminsettings/landing")):j.default.error(n)},H=async()=>{let e=await b.A.get("/pagecategory");if(e&&Array.isArray(e.data)){let i=e.data.filter(e=>"AboutUs"===e.title);i.unshift({_id:"",title:"Select"}),P(i)}},O=async()=>{let i=await b.A.get(`/landingPage/${e.routes[1]}`);if(i){let{pageCategory:e}=i;i.pageCategory=e&&e._id?e._id:"",S(i.images?i.images:[]),E(i.images_src?i.images_src:[]),I(e=>({...e,...i}))}};(0,n.useEffect)(()=>{T(),a&&O(),H()},[]);let $=(0,n.useRef)(null);return(0,s.jsx)(l.A,{className:"formCard",fluid:!0,children:(0,s.jsx)(r.A,{children:(0,s.jsx)(f.A,{onSubmit:B,ref:$,initialValues:y,enableReinitialize:!0,children:(0,s.jsxs)(r.A.Body,{children:[(0,s.jsx)(c.A,{children:(0,s.jsx)(d.A,{children:(0,s.jsx)(r.A.Title,{children:i("adminsetting.landing.form.EditableContent")})})}),(0,s.jsx)("hr",{}),(0,s.jsx)(c.A,{className:"mb-3",children:(0,s.jsx)(d.A,{md:!0,lg:12,sm:12,children:(0,s.jsxs)(o.A.Group,{children:[(0,s.jsx)(o.A.Label,{children:i("adminsetting.landing.form.Title")}),(0,s.jsxs)(h.s3,{name:"title",id:"title",value:y.title,validator:e=>""!==e.trim(),required:!0,errorMessage:{validator:i("adminsetting.landing.form.PleaseAddtheTitle")},onChange:e=>{if(e.target){let{name:i,value:t}=e.target;I(e=>({...e,[i]:t}))}},children:[(0,s.jsx)("option",{value:"",children:"Select"},""),(0,s.jsx)("option",{value:"Header",children:"Header"},"Header"),(0,s.jsx)("option",{value:"About Us",children:"About Us"},"About Us")]})]})})}),(0,s.jsx)(c.A,{className:"mb-3",children:(0,s.jsx)(d.A,{md:5,children:(0,s.jsxs)(o.A.Group,{children:[(0,s.jsx)(o.A.Label,{className:"pe-3",children:i("adminsetting.landing.form.chooseLanguage")}),(0,s.jsx)(m.A,{title:y.language.toUpperCase(),variant:"outline-secondary",id:"basic-dropdown",className:"d-inline",children:D&&D.map((e,i)=>(0,s.jsx)("div",{children:(0,s.jsxs)(p.A.Item,{active:e.abbr===y.language,eventKey:e._id,onClick:()=>U(e),children:[e.abbr.toUpperCase(),"-",e.title.toUpperCase()]})},i))})]})})}),(0,s.jsx)(c.A,{className:"mb-3",children:(0,s.jsx)(d.A,{children:(0,s.jsxs)(o.A.Group,{children:[(0,s.jsx)(o.A.Label,{children:i("adminsetting.landing.form.Description")}),(0,s.jsx)(C.x,{initContent:y.description,onChange:e=>z(e)})]})})}),(0,s.jsx)(c.A,{className:"mb-3",children:(0,s.jsx)(d.A,{children:(0,s.jsxs)(o.A.Group,{children:[(0,s.jsx)(o.A.Label,{children:i("adminsetting.landing.form.Images")}),(0,s.jsx)(v.A,{datas:N,srcText:_,getImgID:e=>G(e),getImageSource:e=>R(e)})]})})}),(0,s.jsx)(c.A,{className:"mb-3",children:(0,s.jsx)(d.A,{children:(0,s.jsx)(o.A.Group,{children:(0,s.jsx)(o.A.Check,{checked:y.isEnabled,name:"isEnabled",onClick:()=>{I(e=>({...e,isEnabled:!e.isEnabled}))},label:i("adminsetting.landing.form.Published"),type:"checkbox"})})})}),(0,s.jsx)(c.A,{className:"my-4",children:(0,s.jsxs)(d.A,{children:[(0,s.jsx)(g.A,{className:"me-2",type:"submit",variant:"primary",children:i("adminsetting.landing.form.Submit")}),(0,s.jsx)(g.A,{className:"me-2",onClick:()=>{I(x),S([]),E([]),window.scrollTo(0,0)},variant:"info",children:i("adminsetting.landing.form.Reset")}),(0,s.jsx)(A(),{href:"/adminsettings/[...routes]",as:"/adminsettings/landing",children:(0,s.jsx)(g.A,{variant:"secondary",children:i("adminsetting.landing.form.Cancel")})})]})})]})})})})};a()}catch(e){a(e)}})},98178:(e,i,t)=>{t.a(e,async(e,a)=>{try{t.d(i,{A:()=>C});var s=t(8732),n=t(82015),l=t(16029),r=t(82053),c=t(54131),d=t(49481),o=t(59549),m=t(91353),p=t(12403),g=t(27825),x=t.n(g),u=t(42893),h=t(63487),f=t(88751),j=e([c,u,h]);[c,u,h]=j.then?(await j)():j;let y=[],A={flex:1,display:"flex",flexDirection:"column",alignItems:"center",justifyContent:"center",width:"100%",height:"100%",borderWidth:.1,borderColor:"#fafafa",backgroundColor:"#fafafa",color:"black",transition:"border  .24s ease-in-out",padding:"15px"},b={display:"flex",padding:"10px",width:"100%",border:"2px solid gray",flexDirection:"column",justifyContent:"flex-start",flexWrap:"wrap",marginTop:20},v={width:"150px"},w={borderColor:"#2196f3"},C=e=>{let i,{t}=(0,f.useTranslation)("common"),[a,g]=(0,n.useState)(!1),[j,C]=(0,n.useState)(),I="application"==e.type?0x1400000:"20971520",[N,S]=(0,n.useState)([]),[_,E]=(0,n.useState)(!0),[D,k]=(0,n.useState)([]),F=e&&"application"===e.type?"/files":"/image",P=async e=>{await h.A.remove(`${F}/${e}`)},U=e=>{C(e),g(!0)},L=(e,i)=>{let t=[...D];t[i]=e.target.value,k(t)},T=i=>{switch(i&&i.name.split(".").pop()){case"JPG":case"jpg":case"jpeg":case"jpg":case"png":return(0,s.jsx)("img",{src:i.preview,style:v});case"pdf":return(0,s.jsx)("img",{src:"/images/fileIcons/pdfFile.png",className:"application"===e.type?"docPreview":"imgPreview"});case"docx":default:return(0,s.jsx)("img",{src:"/images/fileIcons/wordFile.png",className:"application"===e.type?"docPreview":"imgPreview"});case"xls":case"xlsx":return(0,s.jsx)("img",{src:"/images/fileIcons/xlsFile.png",className:"application"===e.type?"docPreview":"imgPreview"})}},z=()=>g(!1),G=()=>{g(!1)},R=i=>{let t=(i=j)&&i._id?{serverID:i._id}:{file:i},a=x().findIndex(y,t),s=[...D];s.splice(a,1),k(s),P(y[a].serverID),y.splice(a,1),e.getImgID(y,e.index?e.index:0);let n=[...N];n.splice(n.indexOf(i),1),S(n),g(!1)},B=N.map((i,n)=>(0,s.jsxs)("div",{children:[(0,s.jsx)(d.A,{xs:12,children:(0,s.jsxs)("div",{className:"row",children:[(0,s.jsx)(d.A,{md:4,lg:3,className:"application text-center align-self-center"===e.type?"docImagePreview text-center align-self-center":"imgPreview text-center align-self-center",children:T(i)}),(0,s.jsx)(d.A,{md:5,lg:7,className:"align-self-center",children:(0,s.jsxs)(o.A,{children:[(0,s.jsxs)(o.A.Group,{controlId:"filename",children:[(0,s.jsx)(o.A.Label,{className:"mt-2",children:t("FileName")}),(0,s.jsx)(o.A.Control,{size:"sm",type:"text",disabled:!0,value:i.original_name?i.original_name:i.name})]}),(0,s.jsxs)(o.A.Group,{controlId:"description",children:[(0,s.jsx)(o.A.Label,{children:"application"===e.type?t("ShortDescription/(Max255Characters)"):t("Source/Description")}),(0,s.jsx)(o.A.Control,{maxLength:"application"===e.type?255:void 0,size:"sm",type:"text",placeholder:"application"===e.type?t("`Enteryourdocumentdescription`"):t("`Enteryourimagesource/description`"),value:D[n],onChange:e=>L(e,n)})]})]})}),(0,s.jsx)(d.A,{md:3,lg:2,className:"align-self-center text-center",onClick:()=>U(i),children:(0,s.jsx)(m.A,{variant:"dark",children:t("Remove")})})]})}),(0,s.jsxs)(p.A,{show:a,onHide:z,children:[(0,s.jsx)(p.A.Header,{closeButton:!0,children:(0,s.jsx)(p.A.Title,{children:t("DeleteFile")})}),(0,s.jsx)(p.A.Body,{children:t("Areyousurewanttodeletethisfile?")}),(0,s.jsxs)(p.A.Footer,{children:[(0,s.jsx)(m.A,{variant:"secondary",onClick:G,children:t("Cancel")}),(0,s.jsx)(m.A,{variant:"primary",onClick:()=>R(i),children:t("yes")})]})]})]},n));(0,n.useEffect)(()=>{N.forEach(e=>URL.revokeObjectURL(e.preview)),y=[]},[]),(0,n.useEffect)(()=>{e.getImageSource(D)},[D]),(0,n.useEffect)(()=>{k(e.srcText)},[e.srcText]),(0,n.useEffect)(()=>{if(e&&"true"===e.singleUpload&&E(!1),e&&e.datas){let i=e.datas.map((i,t)=>(y.push({serverID:i._id,index:e.index?e.index:0,type:i.name.split(".")[1]}),{...i,preview:`http://localhost:3001/api/v1/image/show/${i._id}`}));S([...i])}},[e.datas]);let H=async(i,t)=>{if(i.length>t)try{let a=new FormData;a.append("file",i[t]);let s=await h.A.post(F,a,{"Content-Type":"multipart/form-data"});y.push({serverID:s._id,file:i[t],index:e.index?e.index:0,type:i[t].name.split(".")[1]}),H(i,t+1)}catch(e){H(i,t+1)}else e.getImgID(y,e.index?e.index:0)},O=(0,n.useCallback)(async e=>{await H(e,0);let i=e.map(e=>Object.assign(e,{preview:URL.createObjectURL(e)}));_?S(e=>[...e,...i]):S([...i])},[]),{getRootProps:$,getInputProps:M,isDragActive:W,isDragAccept:q,isDragReject:J,fileRejections:K}=(0,l.useDropzone)({accept:e&&e.type?"application/pdf, application/vnd.openxmlformats-officedocument.wordprocessingml.document, application/vnd.oasis.opendocument.text,application/vnd.openxmlformats-officedocument.spreadsheetml.sheet,text/csv":"image/*",multiple:_,minSize:0,maxSize:I,onDrop:O,validator:function(e){if("/image"===F){if("image"!==e.type.substring(0,5))return u.default.error(t("toast.filetypenotsupport")),{code:"file-invalid-type",message:"File type not supported"}}else if("/files"===F&&"image"===e.type.substring(0,5))return u.default.error(t("toast.filetypenotsupport")),{code:"file-invalid-type",message:"File type not supported"};return null}}),V=(0,n.useMemo)(()=>({...A,...W?w:{outline:"2px dashed #bbb"},...q?{outline:"2px dashed #595959"}:{outline:"2px dashed #bbb"},...J?{outline:"2px dashed red"}:{activeStyle:w}}),[W,J]);i=e&&"application"===e.type?(0,s.jsx)("small",{style:{color:"#595959"},children:t("DocumentWeSupport")}):(0,s.jsx)("small",{style:{color:"#595959"},children:t("ImageWeSupport")});let Q=K.length>0&&K[0].file.size>I;return(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)("div",{className:" d-flex justify-content-center align-items-center mt-3",style:{width:"100%",height:"180px"},children:(0,s.jsxs)("div",{...$({style:V}),children:[(0,s.jsx)("input",{...M()}),(0,s.jsx)(r.FontAwesomeIcon,{icon:c.faCloudUploadAlt,size:"4x",color:"#999"}),(0,s.jsx)("p",{style:{color:"#595959",marginBottom:"0px"},children:t("Drag'n'dropsomefileshere,orclicktoselectfiles")}),!_&&(0,s.jsxs)("small",{style:{color:"#595959"},children:[(0,s.jsx)("b",{children:"Note:"})," One single image will be accepted"]}),i,(e.type,Q&&(0,s.jsxs)("small",{className:"text-danger mt-2",children:[(0,s.jsx)(r.FontAwesomeIcon,{icon:c.faExclamationCircle,size:"1x",color:"red"})," ",t("FileistoolargeItshouldbelessthan20MB")]})),J&&(0,s.jsxs)("small",{className:"text-danger",style:{color:"#595959"},children:[(0,s.jsx)(r.FontAwesomeIcon,{icon:c.faExclamationCircle,size:"1x",color:"red"})," ",t("Filetypenotacceptedsorr")]})]})}),N.length>0&&(0,s.jsx)("div",{style:b,children:B})]})};a()}catch(e){a(e)}})}};