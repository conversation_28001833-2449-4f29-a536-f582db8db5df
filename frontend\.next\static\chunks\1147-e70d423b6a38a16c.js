"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[1147],{1147:(e,t,r)=>{r.r(t),r.d(t,{default:()=>a});var l=r(37876),n=r(14232),s=r(40949),o=r(53718);function a(e){let{t,ongoingOperations:r,ongoingProjects:a}=e,[i,c]=(0,n.useState)([]),y=async()=>{let e={query:{status:[]},sort:{created_at:"desc"},limit:10,select:"-description -operation -world_region -country_regions -hazard_type -hazard -syndrome -status -laboratory_confirmed -officially_validated -rki_monitored -risk_assessment -date -more_info -images -user -created_at -updated_at"},t=await p();t&&e.query.status.push(t);let r=await o.A.get("event",e);r&&Array.isArray(r.data)&&r.data.length>0&&c(r.data)},p=async()=>{let e=await o.A.get("/eventStatus",{query:{title:"Current"}});return!!e&&!!e.data&&e.data.length>0&&e.data[0]._id};return(0,n.useEffect)(()=>{y()},[]),(0,l.jsxs)("div",{className:"active-projects-announcements",children:[(0,l.jsx)("h4",{children:t("allActivity")}),(0,l.jsx)(s.default,{t:t,currentEvents:i,ongoingProjects:a,ongoingOperations:r})]})}},15641:(e,t,r)=>{r.d(t,{A:()=>s});var l=r(37876);r(14232);var n=r(62945);let s=e=>{let{name:t="Marker",id:r="",countryId:s="",type:o,icon:a,position:i,onClick:c,title:y,draggable:p=!1}=e;return i&&"number"==typeof i.lat&&"number"==typeof i.lng?(0,l.jsx)(n.pH,{position:i,icon:a,title:y||t,draggable:p,onClick:e=>{c&&c({name:t,id:r,countryId:s,type:o,position:i},{position:i,getPosition:()=>i},e)}}):null}},40949:(e,t,r)=>{r.r(t),r.d(t,{default:()=>p});var l=r(37876),n=r(14232),s=r(82851),o=r.n(s),a=r(66619),i=r(15641),c=r(31753);let y=e=>{let{t}=e,r=t("Event");return(0,l.jsx)("div",{className:"map-legends",children:(0,l.jsxs)("ul",{children:[(0,l.jsxs)("li",{className:"marker-yellow-legend",children:[(0,l.jsx)("i",{className:"fas fa-circle"})," ",t("Projects")]}),(0,l.jsxs)("li",{className:"marker-green-legend",children:[(0,l.jsx)("i",{className:"fas fa-circle"})," ",t("Operations")]}),(0,l.jsxs)("li",{className:"marker-red-legend",children:[(0,l.jsx)("i",{className:"fas fa-circle"})," ",r," "]})]})})},p=e=>{let{i18n:t}=(0,c.Bd)("common"),r=t.language,{t:s,ongoingOperations:p,ongoingProjects:u,currentEvents:d}=e,[m,f]=(0,n.useState)({events:!1,projects:!1,operations:!1}),[g,T]=(0,n.useState)([]),[h,v]=(0,n.useState)({}),[_,j]=(0,n.useState)({}),b=()=>{v(null),j(null)},x=async(e,t,r)=>{b(),v(t),j({name:e.name,id:e.id,type:e.type,countryId:e.countryId})},k=()=>{let e=[];o().forEach(p,t=>{t.country&&e.push({title:t.title,type:"operation",id:t._id,countryId:t.country&&t.country._id,lat:t.country.coordinates[0].latitude,lng:t.country.coordinates[0].longitude,icon:"/images/map-marker-green.svg"})}),m.operations=!0,f(m),T([...g,...e])},C=()=>{let e=[];o().forEach(u,t=>{t.partner_institutions&&t.partner_institutions.length>0&&o().forEach(t.partner_institutions,r=>{r.partner_country&&e.push({title:t.title,type:"project",id:t._id,countryId:t.partner_institutions.length>0&&t.partner_institutions[0].partner_country&&t.partner_institutions[0].partner_country._id,lat:r.partner_country.coordinates[0].latitude,lng:r.partner_country.coordinates[0].longitude,icon:"/images/map-marker-yellow.svg"})})}),m.projects=!0,f(m),T([...g,...e])},w=()=>{let e=[];o().forEach(d,t=>{t.country&&e.push({title:t.title,type:"event",id:t._id,countryId:t.country&&t.country._id,lat:t.country.coordinates[0].latitude,lng:t.country.coordinates[0].longitude,icon:"/images/map-marker-red.svg"})}),m.events=!0,f(m),T([...g,...e])};return(0,n.useEffect)(()=>{C()},[u]),(0,n.useEffect)(()=>{k()},[p]),(0,n.useEffect)(()=>{w()},[d]),(0,l.jsxs)(l.Fragment,{children:[(0,l.jsx)(a.A,{onClose:b,language:r,points:g,activeMarker:h,markerInfo:(0,l.jsx)(e=>{let{info:t}=e,n=function(e){switch(null==e?void 0:e.type){case"operation":return p.filter(t=>t.country&&t.country._id==e.countryId);case"project":return u.filter(t=>t.partner_institutions&&t.partner_institutions.length>0&&t.partner_institutions[0].partner_country&&t.partner_institutions[0].partner_country._id==e.countryId);case"event":return d.filter(t=>t.country&&t.country._id==e.countryId)}}(t);return t&&Object.keys(t).length>0&&void 0!=n?(0,l.jsx)("ul",{children:n.map((e,n)=>(0,l.jsx)("li",{children:(0,l.jsx)("a",{href:"/".concat(r,"/").concat(null==t?void 0:t.type,"/show/").concat(null==t?void 0:t.id),children:null==e?void 0:e.title})},n))}):null},{info:_}),children:m.projects&&m.operations&&m.events&&g.length>=1?g.map((e,t)=>{if(e.lat&&e.lng)return(0,l.jsx)(i.A,{name:e.title,id:e.id,countryId:e.countryId,type:e.type,icon:{url:e.icon},onClick:x,position:e},t)}):null}),(0,l.jsx)(y,{t:s})]})}},66619:(e,t,r)=>{r.d(t,{A:()=>d});var l=r(37876);r(14232);var n=r(62945);let s=e=>{let{position:t,onCloseClick:r,children:s}=e;return(0,l.jsx)(n.Fu,{position:t,onCloseClick:r,children:(0,l.jsx)("div",{children:s})})},o="labels.text.fill",a="labels.text.stroke",i="road.highway",c="geometry.stroke",y=[{elementType:"geometry",stylers:[{color:"#1d2c4d"}]},{elementType:o,stylers:[{color:"#8ec3b9"}]},{elementType:a,stylers:[{color:"#1a3646"}]},{featureType:"administrative",elementType:"geometry",stylers:[{visibility:"off"}]},{featureType:"administrative.country",elementType:c,stylers:[{color:"#4b6878"}]},{featureType:"administrative.land_parcel",elementType:"labels",stylers:[{visibility:"off"}]},{featureType:"administrative.land_parcel",elementType:o,stylers:[{color:"#64779e"}]},{featureType:"administrative.province",elementType:c,stylers:[{color:"#4b6878"}]},{featureType:"landscape.man_made",elementType:c,stylers:[{color:"#334e87"}]},{featureType:"landscape.natural",elementType:"geometry",stylers:[{color:"#023e58"}]},{featureType:"poi",stylers:[{visibility:"off"}]},{featureType:"poi",elementType:"geometry",stylers:[{color:"#283d6a"}]},{featureType:"poi",elementType:"labels.text",stylers:[{visibility:"off"}]},{featureType:"poi",elementType:o,stylers:[{color:"#6f9ba5"}]},{featureType:"poi",elementType:a,stylers:[{color:"#1d2c4d"}]},{featureType:"poi.park",elementType:"geometry.fill",stylers:[{color:"#023e58"}]},{featureType:"poi.park",elementType:o,stylers:[{color:"#3C7680"}]},{featureType:"road",stylers:[{visibility:"off"}]},{featureType:"road",elementType:"geometry",stylers:[{color:"#304a7d"}]},{featureType:"road",elementType:"labels.icon",stylers:[{visibility:"off"}]},{featureType:"road",elementType:o,stylers:[{color:"#98a5be"}]},{featureType:"road",elementType:a,stylers:[{color:"#1d2c4d"}]},{featureType:i,elementType:"geometry",stylers:[{color:"#2c6675"}]},{featureType:i,elementType:c,stylers:[{color:"#255763"}]},{featureType:i,elementType:o,stylers:[{color:"#b0d5ce"}]},{featureType:i,elementType:a,stylers:[{color:"#023e58"}]},{featureType:"road.local",elementType:"labels",stylers:[{visibility:"off"}]},{featureType:"transit",stylers:[{visibility:"off"}]},{featureType:"transit",elementType:o,stylers:[{color:"#98a5be"}]},{featureType:"transit",elementType:a,stylers:[{color:"#1d2c4d"}]},{featureType:"transit.line",elementType:"geometry.fill",stylers:[{color:"#283d6a"}]},{featureType:"transit.station",elementType:"geometry",stylers:[{color:"#3a4762"}]},{featureType:"water",elementType:"geometry",stylers:[{color:"#0e1626"}]},{featureType:"water",elementType:o,stylers:[{color:"#4e6d70"}]}];var p=r(89099),u=r(55316);let d=e=>{let{markerInfo:t,activeMarker:r,initialCenter:o,children:a,height:i=300,width:c="114%",language:d,zoom:m=1,minZoom:f=1,onClose:g}=e,{locale:T}=(0,p.useRouter)(),{isLoaded:h,loadError:v}=(0,u._)();return v?(0,l.jsx)("div",{children:"Error loading maps"}):h?(0,l.jsx)("div",{className:"map-container",children:(0,l.jsx)("div",{className:"mapprint",style:{width:c,height:i,position:"relative"},children:(0,l.jsxs)(n.u6,{mapContainerStyle:{width:c,height:"number"==typeof i?"".concat(i,"px"):i},center:o||{lat:52.520017,lng:13.404195},zoom:m,onLoad:e=>{e.setOptions({styles:y})},options:{minZoom:f,draggable:!0,keyboardShortcuts:!1,streetViewControl:!1,panControl:!1,clickableIcons:!1,mapTypeControl:!1,fullscreenControl:!0},children:[a,t&&r&&r.getPosition&&(0,l.jsx)(s,{position:r.getPosition(),onCloseClick:()=>{console.log("close click"),null==g||g()},children:t})]})})}):(0,l.jsx)("div",{children:"Loading Maps..."})}}}]);
//# sourceMappingURL=1147-e70d423b6a38a16c.js.map