"use strict";(()=>{var e={};e.id=1618,e.ids=[636,1618,3220],e.modules={123:e=>{e.exports=require("dom-helpers/removeEventListener")},1332:e=>{e.exports=require("react-custom-scrollbars-2")},1428:e=>{e.exports=import("axios")},1680:e=>{e.exports=require("@restart/ui/utils")},1919:e=>{e.exports=require("react-transition-group/Transition")},3892:e=>{e.exports=require("classnames")},4048:e=>{e.exports=require("react-truncate")},6009:e=>{e.exports=require("dom-helpers/ownerDocument")},6952:e=>{e.exports=require("@restart/ui/Modal")},7374:e=>{e.exports=require("@restart/ui/Dropdown")},8732:e=>{e.exports=require("react/jsx-runtime")},9653:e=>{e.exports=require("@restart/ui/Overlay")},11242:e=>{e.exports=require("redux-persist/integration/react")},11688:e=>{e.exports=require("@restart/hooks/useWillUnmount")},12388:e=>{e.exports=require("dom-helpers/addEventListener")},13364:e=>{e.exports=require("redux-saga/effects")},14062:e=>{e.exports=import("react-redux")},14078:e=>{e.exports=import("swr")},14332:e=>{e.exports=require("uncontrollable")},16029:e=>{e.exports=require("react-dropzone")},16116:e=>{e.exports=require("invariant")},18622:e=>{e.exports=require("yup")},19121:e=>{e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},22326:e=>{e.exports=require("react-dom")},22541:e=>{e.exports=require("redux-persist/lib/storage")},25303:e=>{e.exports=require("@restart/ui/NavItem")},26324:e=>{e.exports=require("warning")},27825:e=>{e.exports=require("lodash")},27910:e=>{e.exports=require("stream")},28217:e=>{e.exports=require("@restart/ui/DropdownItem")},29021:e=>{e.exports=require("fs")},29294:e=>{e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},29628:e=>{e.exports=require("dom-helpers/addClass")},29825:e=>{e.exports=require("prop-types")},29841:e=>{e.exports=require("dom-helpers/hasClass")},30362:e=>{e.exports=require("@restart/ui/DropdownToggle")},32472:(e,r,t)=>{t.a(e,async(e,s)=>{try{t.r(r),t.d(r,{default:()=>d});var i=t(8732);t(82015);var o=t(7082),a=t(49481),n=t(83551),l=t(98178),p=t(88751),c=e([l]);l=(c.then?(await c)():c)[0];let d=e=>{let{t:r}=(0,p.useTranslation)("common"),t=r=>{e.getId(r)},s=r=>{e.getSourceCollection(r)};return(0,i.jsx)(o.A,{className:"formCard",fluid:!0,children:(0,i.jsxs)(a.A,{children:[(0,i.jsx)(n.A,{className:"header-block",lg:12,children:(0,i.jsx)("h6",{children:(0,i.jsx)("span",{children:r("update.Image")})})}),(0,i.jsx)(n.A,{children:(0,i.jsx)(l.A,{datas:e.data,srcText:e.imgSrc,getImgID:e=>t(e),getImageSource:e=>s(e)})})]})})};s()}catch(e){s(e)}})},33873:e=>{e.exports=require("path")},36653:e=>{e.exports=require("nprogress")},36955:e=>{e.exports=require("@restart/hooks/useBreakpoint")},37766:e=>{e.exports=require("@restart/hooks/useMergedRefs")},39756:e=>{e.exports=import("redux")},40051:e=>{e.exports=require("dom-helpers/removeClass")},40261:(e,r,t)=>{t.a(e,async(e,s)=>{try{t.r(r),t.d(r,{config:()=>h,default:()=>u,getServerSideProps:()=>g,getStaticPaths:()=>m,getStaticProps:()=>x,reportWebVitals:()=>f,routeModule:()=>b,unstable_getServerProps:()=>j,unstable_getServerSideProps:()=>A,unstable_getStaticParams:()=>v,unstable_getStaticPaths:()=>y,unstable_getStaticProps:()=>q});var i=t(63885),o=t(80237),a=t(81413),n=t(9616),l=t.n(n),p=t(72386),c=t(32472),d=e([p,c]);[p,c]=d.then?(await d)():d;let u=(0,a.M)(c,"default"),x=(0,a.M)(c,"getStaticProps"),m=(0,a.M)(c,"getStaticPaths"),g=(0,a.M)(c,"getServerSideProps"),h=(0,a.M)(c,"config"),f=(0,a.M)(c,"reportWebVitals"),q=(0,a.M)(c,"unstable_getStaticProps"),y=(0,a.M)(c,"unstable_getStaticPaths"),v=(0,a.M)(c,"unstable_getStaticParams"),j=(0,a.M)(c,"unstable_getServerProps"),A=(0,a.M)(c,"unstable_getServerSideProps"),b=new i.PagesRouteModule({definition:{kind:o.A.PAGES,page:"/updates/ImageForm",pathname:"/updates/ImageForm",bundlePath:"",filename:""},components:{App:p.default,Document:l()},userland:c});s()}catch(e){s(e)}})},40361:e=>{e.exports=require("next/dist/compiled/next-server/pages.runtime.prod.js")},42893:e=>{e.exports=import("react-hot-toast")},43294:e=>{e.exports=require("formik")},50009:e=>{e.exports=require("@restart/ui/DropdownMenu")},54131:e=>{e.exports=import("@fortawesome/free-solid-svg-icons")},57664:e=>{e.exports=require("dom-helpers/scrollbarSize")},58928:e=>{e.exports=require("dom-helpers/canUseDOM")},59717:e=>{e.exports=require("@restart/ui/DropdownContext")},60560:e=>{e.exports=require("dom-helpers/contains")},63033:e=>{e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},65447:e=>{e.exports=require("@restart/ui/Button")},67364:e=>{e.exports=require("@restart/hooks/useCallbackRef")},68455:e=>{e.exports=import("redux-saga")},69722:e=>{e.exports=require("es6-promise")},74075:e=>{e.exports=require("zlib")},74716:e=>{e.exports=require("moment")},74987:e=>{e.exports=require("@restart/ui/ModalManager")},78097:e=>{e.exports=require("next-redux-saga")},78634:e=>{e.exports=require("@restart/ui/Anchor")},80237:(e,r)=>{Object.defineProperty(r,"A",{enumerable:!0,get:function(){return t}});var t=function(e){return e.PAGES="PAGES",e.PAGES_API="PAGES_API",e.APP_PAGE="APP_PAGE",e.APP_ROUTE="APP_ROUTE",e.IMAGE="IMAGE",e}({})},81366:e=>{e.exports=require("redux-auth-wrapper/connectedAuthWrapper")},81413:(e,r)=>{Object.defineProperty(r,"M",{enumerable:!0,get:function(){return function e(r,t){return t in r?r[t]:"then"in r&&"function"==typeof r.then?r.then(r=>e(r,t)):"function"==typeof r&&"default"===t?r:void 0}}})},81521:e=>{e.exports=require("dom-helpers/querySelectorAll")},81895:e=>{e.exports=require("@restart/hooks/useEventCallback")},82015:e=>{e.exports=require("react")},82053:e=>{e.exports=require("@fortawesome/react-fontawesome")},86842:e=>{e.exports=require("@restart/ui/SelectableContext")},87571:e=>{e.exports=require("dom-helpers/transitionEnd")},88751:e=>{e.exports=require("next-i18next")},93787:e=>{e.exports=require("redux-persist")},94947:e=>{e.exports=require("@restart/hooks/useTimeout")},96196:e=>{e.exports=require("next-redux-wrapper")},98178:(e,r,t)=>{t.a(e,async(e,s)=>{try{t.d(r,{A:()=>w});var i=t(8732),o=t(82015),a=t(16029),n=t(82053),l=t(54131),p=t(49481),c=t(59549),d=t(91353),u=t(12403),x=t(27825),m=t.n(x),g=t(42893),h=t(63487),f=t(88751),q=e([l,g,h]);[l,g,h]=q.then?(await q)():q;let y=[],v={flex:1,display:"flex",flexDirection:"column",alignItems:"center",justifyContent:"center",width:"100%",height:"100%",borderWidth:.1,borderColor:"#fafafa",backgroundColor:"#fafafa",color:"black",transition:"border  .24s ease-in-out",padding:"15px"},j={display:"flex",padding:"10px",width:"100%",border:"2px solid gray",flexDirection:"column",justifyContent:"flex-start",flexWrap:"wrap",marginTop:20},A={width:"150px"},b={borderColor:"#2196f3"},w=e=>{let r,{t}=(0,f.useTranslation)("common"),[s,x]=(0,o.useState)(!1),[q,w]=(0,o.useState)(),P="application"==e.type?0x1400000:"20971520",[S,I]=(0,o.useState)([]),[C,k]=(0,o.useState)(!0),[E,M]=(0,o.useState)([]),D=e&&"application"===e.type?"/files":"/image",_=async e=>{await h.A.remove(`${D}/${e}`)},F=e=>{w(e),x(!0)},N=(e,r)=>{let t=[...E];t[r]=e.target.value,M(t)},T=r=>{switch(r&&r.name.split(".").pop()){case"JPG":case"jpg":case"jpeg":case"jpg":case"png":return(0,i.jsx)("img",{src:r.preview,style:A});case"pdf":return(0,i.jsx)("img",{src:"/images/fileIcons/pdfFile.png",className:"application"===e.type?"docPreview":"imgPreview"});case"docx":default:return(0,i.jsx)("img",{src:"/images/fileIcons/wordFile.png",className:"application"===e.type?"docPreview":"imgPreview"});case"xls":case"xlsx":return(0,i.jsx)("img",{src:"/images/fileIcons/xlsFile.png",className:"application"===e.type?"docPreview":"imgPreview"})}},z=()=>x(!1),G=()=>{x(!1)},O=r=>{let t=(r=q)&&r._id?{serverID:r._id}:{file:r},s=m().findIndex(y,t),i=[...E];i.splice(s,1),M(i),_(y[s].serverID),y.splice(s,1),e.getImgID(y,e.index?e.index:0);let o=[...S];o.splice(o.indexOf(r),1),I(o),x(!1)},R=S.map((r,o)=>(0,i.jsxs)("div",{children:[(0,i.jsx)(p.A,{xs:12,children:(0,i.jsxs)("div",{className:"row",children:[(0,i.jsx)(p.A,{md:4,lg:3,className:"application text-center align-self-center"===e.type?"docImagePreview text-center align-self-center":"imgPreview text-center align-self-center",children:T(r)}),(0,i.jsx)(p.A,{md:5,lg:7,className:"align-self-center",children:(0,i.jsxs)(c.A,{children:[(0,i.jsxs)(c.A.Group,{controlId:"filename",children:[(0,i.jsx)(c.A.Label,{className:"mt-2",children:t("FileName")}),(0,i.jsx)(c.A.Control,{size:"sm",type:"text",disabled:!0,value:r.original_name?r.original_name:r.name})]}),(0,i.jsxs)(c.A.Group,{controlId:"description",children:[(0,i.jsx)(c.A.Label,{children:"application"===e.type?t("ShortDescription/(Max255Characters)"):t("Source/Description")}),(0,i.jsx)(c.A.Control,{maxLength:"application"===e.type?255:void 0,size:"sm",type:"text",placeholder:"application"===e.type?t("`Enteryourdocumentdescription`"):t("`Enteryourimagesource/description`"),value:E[o],onChange:e=>N(e,o)})]})]})}),(0,i.jsx)(p.A,{md:3,lg:2,className:"align-self-center text-center",onClick:()=>F(r),children:(0,i.jsx)(d.A,{variant:"dark",children:t("Remove")})})]})}),(0,i.jsxs)(u.A,{show:s,onHide:z,children:[(0,i.jsx)(u.A.Header,{closeButton:!0,children:(0,i.jsx)(u.A.Title,{children:t("DeleteFile")})}),(0,i.jsx)(u.A.Body,{children:t("Areyousurewanttodeletethisfile?")}),(0,i.jsxs)(u.A.Footer,{children:[(0,i.jsx)(d.A,{variant:"secondary",onClick:G,children:t("Cancel")}),(0,i.jsx)(d.A,{variant:"primary",onClick:()=>O(r),children:t("yes")})]})]})]},o));(0,o.useEffect)(()=>{S.forEach(e=>URL.revokeObjectURL(e.preview)),y=[]},[]),(0,o.useEffect)(()=>{e.getImageSource(E)},[E]),(0,o.useEffect)(()=>{M(e.srcText)},[e.srcText]),(0,o.useEffect)(()=>{if(e&&"true"===e.singleUpload&&k(!1),e&&e.datas){let r=e.datas.map((r,t)=>(y.push({serverID:r._id,index:e.index?e.index:0,type:r.name.split(".")[1]}),{...r,preview:`http://localhost:3001/api/v1/image/show/${r._id}`}));I([...r])}},[e.datas]);let U=async(r,t)=>{if(r.length>t)try{let s=new FormData;s.append("file",r[t]);let i=await h.A.post(D,s,{"Content-Type":"multipart/form-data"});y.push({serverID:i._id,file:r[t],index:e.index?e.index:0,type:r[t].name.split(".")[1]}),U(r,t+1)}catch(e){U(r,t+1)}else e.getImgID(y,e.index?e.index:0)},L=(0,o.useCallback)(async e=>{await U(e,0);let r=e.map(e=>Object.assign(e,{preview:URL.createObjectURL(e)}));C?I(e=>[...e,...r]):I([...r])},[]),{getRootProps:W,getInputProps:B,isDragActive:$,isDragAccept:H,isDragReject:V,fileRejections:J}=(0,a.useDropzone)({accept:e&&e.type?"application/pdf, application/vnd.openxmlformats-officedocument.wordprocessingml.document, application/vnd.oasis.opendocument.text,application/vnd.openxmlformats-officedocument.spreadsheetml.sheet,text/csv":"image/*",multiple:C,minSize:0,maxSize:P,onDrop:L,validator:function(e){if("/image"===D){if("image"!==e.type.substring(0,5))return g.default.error(t("toast.filetypenotsupport")),{code:"file-invalid-type",message:"File type not supported"}}else if("/files"===D&&"image"===e.type.substring(0,5))return g.default.error(t("toast.filetypenotsupport")),{code:"file-invalid-type",message:"File type not supported"};return null}}),X=(0,o.useMemo)(()=>({...v,...$?b:{outline:"2px dashed #bbb"},...H?{outline:"2px dashed #595959"}:{outline:"2px dashed #bbb"},...V?{outline:"2px dashed red"}:{activeStyle:b}}),[$,V]);r=e&&"application"===e.type?(0,i.jsx)("small",{style:{color:"#595959"},children:t("DocumentWeSupport")}):(0,i.jsx)("small",{style:{color:"#595959"},children:t("ImageWeSupport")});let K=J.length>0&&J[0].file.size>P;return(0,i.jsxs)(i.Fragment,{children:[(0,i.jsx)("div",{className:" d-flex justify-content-center align-items-center mt-3",style:{width:"100%",height:"180px"},children:(0,i.jsxs)("div",{...W({style:X}),children:[(0,i.jsx)("input",{...B()}),(0,i.jsx)(n.FontAwesomeIcon,{icon:l.faCloudUploadAlt,size:"4x",color:"#999"}),(0,i.jsx)("p",{style:{color:"#595959",marginBottom:"0px"},children:t("Drag'n'dropsomefileshere,orclicktoselectfiles")}),!C&&(0,i.jsxs)("small",{style:{color:"#595959"},children:[(0,i.jsx)("b",{children:"Note:"})," One single image will be accepted"]}),r,(e.type,K&&(0,i.jsxs)("small",{className:"text-danger mt-2",children:[(0,i.jsx)(n.FontAwesomeIcon,{icon:l.faExclamationCircle,size:"1x",color:"red"})," ",t("FileistoolargeItshouldbelessthan20MB")]})),V&&(0,i.jsxs)("small",{className:"text-danger",style:{color:"#595959"},children:[(0,i.jsx)(n.FontAwesomeIcon,{icon:l.faExclamationCircle,size:"1x",color:"red"})," ",t("Filetypenotacceptedsorr")]})]})}),S.length>0&&(0,i.jsx)("div",{style:j,children:R})]})};s()}catch(e){s(e)}})},98320:e=>{e.exports=require("@restart/hooks/useIsomorphicEffect")},99460:e=>{e.exports=require("dom-helpers/css")}};var r=require("../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[6089,9216,9616,2386],()=>t(40261));module.exports=s})();