exports.id=2386,exports.ids=[2386],exports.modules={214:(e,t,s)=>{"use strict";s.d(t,{M:()=>r,bM:()=>n});var a=s(82015),i=s.n(a);let n=i().createContext({updates:[],setUpdates:()=>""}),r=()=>{let[e,t]=i().useState([]);return{updates:e,setUpdates:i().useCallback(e=>{t(e)},[])}}},3318:()=>{},16053:(e,t,s)=>{"use strict";s.r(t),s.d(t,{canAccessRoutes:()=>o,default:()=>c});var a=s(8732);s(82015);var i=s(81366),n=s.n(i),r=s(61421);let l=["data-privacy-policy","declarationform","profile","reset-password","search","forgot-password"],o=n()({authenticatedSelector:(e,t)=>{let s=e.permissions,i=t.router.route.split("/")[1];return""===i&&(i="dashboard"),"people"===i&&(i="users"),"adminsettings"===i&&(i="admin"),"events-calendar"===i&&(i="event"),"updates"===i&&(i="update"),!!s&&!!s[i]&&!!s[i]["read:any"]||!!l.includes(i)||(0,a.jsx)(r.default,{})},wrapperDisplayName:"CanAccessRoutes"}),c=o},16553:(e,t,s)=>{"use strict";s.a(e,async(e,a)=>{try{s.d(t,{A:()=>A});var i=s(8732),n=s(12403),r=s(59549),l=s(83551),o=s(49481),c=s(91353),d=s(82015),u=s(43294),p=s(18622),h=s(42893),m=s(88751),x=s(63487),j=e([h,x]);[h,x]=j.then?(await j)():j;let g=p.object().shape({name:p.string().required("Name is required"),position:p.string().required("Position is required"),organisation:p.string().required("Organisation is required"),subject:p.string().required("Subject is required"),message:p.string().required("Message is required")}),y={name:"",position:"",organisation:"",subject:"",message:""},A=e=>{let{t}=(0,m.useTranslation)("common"),s=(0,d.useRef)(null),a=async(s,{resetForm:a,setSubmitting:i})=>{try{await x.A.post("/users/contactUs",s),h.default.success(t("toast.Contactusformsubmittedsuccessfully")),a(),e.onHide()}catch(e){h.default.error(e?.message||"An error occurred")}finally{i(!1)}};return(0,i.jsxs)(n.A,{...e,size:"lg","aria-labelledby":"contact-us-modal",className:"contacts-us-modal",children:[(0,i.jsx)(n.A.Header,{closeButton:!0,children:(0,i.jsx)(n.A.Title,{className:"text-capitalize",id:"contact-us-modal",children:t("contactUs.tab").toLocaleLowerCase()})}),(0,i.jsx)(u.Formik,{initialValues:y,validationSchema:g,onSubmit:a,children:({isSubmitting:e,touched:a,errors:d})=>(0,i.jsx)(u.Form,{ref:s,children:(0,i.jsxs)(n.A.Body,{id:"main-content",style:{width:"100%"},children:[(0,i.jsx)("p",{children:t("contactUs.tab1")}),(0,i.jsxs)(r.A.Group,{as:l.A,controlId:"formHorizontalName",children:[(0,i.jsx)(r.A.Label,{column:!0,md:"4",xs:"5",lg:"2",className:"required-field",children:t("contactUs.tab2")}),(0,i.jsxs)(o.A,{md:"8",xs:"7",lg:"10",children:[(0,i.jsx)(u.Field,{name:"name",as:r.A.Control,isInvalid:a.name&&!!d.name}),(0,i.jsx)(u.ErrorMessage,{name:"name",render:e=>(0,i.jsx)(r.A.Control.Feedback,{type:"invalid",children:t("thisfieldisrequired")})})]})]}),(0,i.jsxs)(r.A.Group,{as:l.A,controlId:"formHorizontalPosition",children:[(0,i.jsx)(r.A.Label,{column:!0,md:"4",xs:"5",lg:"2",className:"required-field",children:t("contactUs.tab3")}),(0,i.jsxs)(o.A,{md:"8",xs:"7",lg:"10",children:[(0,i.jsx)(u.Field,{name:"position",as:r.A.Control,isInvalid:a.position&&!!d.position}),(0,i.jsx)(u.ErrorMessage,{name:"position",render:e=>(0,i.jsx)(r.A.Control.Feedback,{type:"invalid",children:t("thisfieldisrequired")})})]})]}),(0,i.jsxs)(r.A.Group,{as:l.A,controlId:"formHorizontalOrganization",children:[(0,i.jsx)(r.A.Label,{column:!0,md:"4",xs:"5",lg:"2",className:"required-field",children:t("contactUs.tab4")}),(0,i.jsxs)(o.A,{md:"8",xs:"7",lg:"10",children:[(0,i.jsx)(u.Field,{name:"organisation",as:r.A.Control,isInvalid:a.organisation&&!!d.organisation}),(0,i.jsx)(u.ErrorMessage,{name:"organisation",render:e=>(0,i.jsx)(r.A.Control.Feedback,{type:"invalid",children:t("thisfieldisrequired")})})]})]}),(0,i.jsxs)(r.A.Group,{as:l.A,controlId:"formHorizontalReasonofContact",children:[(0,i.jsx)(r.A.Label,{column:!0,md:"4",xs:"5",lg:"2",className:"required-field",children:t("contactUs.tab5")}),(0,i.jsxs)(o.A,{md:"8",xs:"7",lg:"10",children:[(0,i.jsxs)(u.Field,{name:"subject",as:"select",className:`form-control ${a.subject&&d.subject?"is-invalid":""}`,children:[(0,i.jsx)("option",{value:"",children:t("subject.tab")}),(0,i.jsx)("option",{value:"Membership",children:t("subject.tab1")}),(0,i.jsx)("option",{value:"Questions about Public Health Events",children:t("subject.tab2")}),(0,i.jsx)("option",{value:"Technical Support",children:t("subject.tab3")}),(0,i.jsx)("option",{value:"Others",children:t("subject.tab4")}),(0,i.jsx)("option",{value:"Remove consent",children:t("subject.tab5")})]}),(0,i.jsx)(u.ErrorMessage,{name:"subject",render:e=>(0,i.jsx)(r.A.Control.Feedback,{type:"invalid",children:t("thisfieldisrequired")})})]})]}),(0,i.jsx)(r.A.Group,{as:l.A,controlId:"formHorizontalMessage",children:(0,i.jsxs)(o.A,{md:"12",xs:"12",lg:"12",children:[(0,i.jsx)(r.A.Label,{className:"required-field",children:t("contactUs.tab6")}),(0,i.jsx)(u.Field,{name:"message",as:"textarea",rows:3,className:`form-control ${a.message&&d.message?"is-invalid":""}`,style:{backgroundImage:"none",borderColor:"#ced4da"}}),(0,i.jsx)(u.ErrorMessage,{name:"message",render:e=>(0,i.jsx)(r.A.Control.Feedback,{type:"invalid",children:t("thisfieldisrequired")})})]})}),(0,i.jsx)(r.A.Group,{as:l.A,children:(0,i.jsx)(o.A,{className:"text-end",sm:{span:3,offset:9},children:(0,i.jsx)(c.A,{type:"submit",disabled:e,children:t("submit")})})})]})})})]})};a()}catch(e){a(e)}})},16875:(e,t,s)=>{"use strict";s.d(t,{A:()=>n});var a=s(49856);let i={_id:"",username:"",email:"",status:"",enabled:!1,vspace_status:"",is_vspace:!1,is_focal_point:!1,dataConsentPolicy:!1,restrictedUsePolicy:!1,acceptCookiesPolicy:!1,withdrawConsentPolicy:!1,medicalConsentPolicy:!1,fullDataProtectionConsentPolicy:!1,image:""},n=function(e=i,t){return t.type===a.QN.LOAD_DATA_SUCCESS?{...e,...t.data}:e}},17663:(e,t,s)=>{"use strict";s.a(e,async(e,a)=>{try{s.d(t,{A:()=>d});var i=s(8732),n=s(65527),r=s(82015),l=s(63487),o=s(88751),c=e([l]);function d({updates:e,onRemoveUpdate:t,loadMore:s,loadMoreButton:a,...l}){let{t:c}=(0,o.useTranslation)("common"),[d,u]=(0,r.useState)([]);return(0,i.jsx)("div",{className:"updates-block",id:"update-content",children:(0,i.jsxs)("ul",{className:"updatesList",children:[e&&Array.isArray(e)&&d&&d.length>0&&e.map((e,s)=>(0,i.jsx)(n.A,{updateTypes:d,item:e,onRemoveUpdate:t},s)),a&&e.length>0?(0,i.jsx)("li",{className:"updates-load-more",children:(0,i.jsx)("a",{onClick:s,children:c("LoadMore")})}):null]})})}l=(c.then?(await c)():c)[0],a()}catch(e){a(e)}})},21502:(e,t,s)=>{"use strict";s.d(t,{A:()=>n});var a=s(49856);let i={},n=function(e=i,t){return t.type===a.QN.LOAD_USER_PERMISSIONS?t.data:e}},27989:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=e=>{if(e){let t=document.getElementsByClassName("updates-load-more");t.length>0&&t[0]&&t[0].remove();let s=document.getElementById(e);s&&(s.innerHTML,document.body.innerHTML,window.print())}}},28249:(e,t,s)=>{"use strict";s.a(e,async(e,a)=>{try{s.d(t,{A:()=>o});var i=s(13364),n=s(49856),r=s(63487),l=e([r]);r=(l.then?(await l)():l)[0];let o=(0,i.takeEvery)(n.QN.LOAD_DATA,function*(){try{let e=yield r.A.post("/users/getLoggedUser",{});yield(0,i.put)((0,n.aE)(e))}catch(e){console.log(e)}});a()}catch(e){a(e)}})},28286:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});var a=s(8732),i=s(12403),n=s(88751);function r(e){let{t}=(0,n.useTranslation)("common");return(0,a.jsxs)(i.A,{...e,size:"lg","aria-labelledby":"about-us-modal",children:[(0,a.jsx)(i.A.Header,{closeButton:!0,children:(0,a.jsx)(i.A.Title,{id:"about-us-modal",children:t("about")})}),(0,a.jsx)(i.A.Body,{children:(0,a.jsx)("p",{children:t("abouttext")})})]})}},30370:(e,t,s)=>{"use strict";s.d(t,{A:()=>o});var a=s(8732),i=s(7082),n=s(83551),r=s(49481),l=s(13524);let o=()=>(0,a.jsx)(i.A,{className:"pb-5 m-5",children:(0,a.jsx)(n.A,{children:(0,a.jsx)(r.A,{children:(0,a.jsx)(l.A,{animation:"border",variant:"primary"})})})})},35032:(e,t,s)=>{"use strict";s.a(e,async(e,a)=>{try{s.d(t,{A:()=>d});var i=s(8732),n=s(82015),r=s(94145),l=s(1428),o=s(84337),c=e([l,o,r]);[l,o,r]=c.then?(await c)():c;let d=()=>{let[e,t]=(0,n.useState)([]),[s,a]=(0,n.useState)(!0),[c,d]=(0,n.useState)(null);return(0,n.useEffect)(()=>{(async()=>{try{a(!0);let e=await o.A.getAuthHeader(),s=await l.default.get("http://localhost:3001/api/v1/tweets?count=2",{headers:e});s.data?.data?t(s.data.data):t([]),d(null)}catch(e){console.error("Error fetching tweets:",e),d("Failed to load tweets"),t([])}finally{a(!1)}})()},[]),(0,i.jsxs)("div",{className:"twitter__timeline",children:[(0,i.jsx)("div",{className:"timeline-Header timeline-InformationCircle-widgetParent","data-scribe":"section:header",children:(0,i.jsxs)("h1",{className:"timeline-Header-title u-inlineBlock","data-scribe":"element:title",children:[e.length>1?"Latest Tweets":"Latest Tweet",(0,i.jsxs)("span",{className:"timeline-Header-byline","data-scribe":"element:byline",children:["\xa0by\xa0",(0,i.jsx)("a",{className:"customisable-highlight",href:"https://twitter.com/rki_de",target:"_blank",rel:"noopener noreferrer",title:"‎@rki_de on Twitter",children:"‎@rki_de"})]})]})}),(0,i.jsx)("div",{style:{padding:"10px"},children:s?(0,i.jsx)("div",{style:{textAlign:"center",padding:"20px"},children:"Loading tweets..."}):c?(0,i.jsxs)("div",{style:{textAlign:"center",padding:"20px",color:"#666"},children:[c,". ",(0,i.jsx)("a",{href:"https://x.com/rki_de",target:"_blank",rel:"noopener noreferrer",children:"Visit @rki_de on X"})]}):(0,i.jsx)(i.Fragment,{children:e.length>0?(0,i.jsx)("div",{children:e.map((t,s)=>(0,i.jsx)("div",{style:{marginBottom:s<e.length-1?"20px":"0"},children:(0,i.jsx)(r.Y,{id:t.tweetId})},t.tweetId))}):(0,i.jsxs)("div",{style:{padding:"20px",textAlign:"center",color:"#666"},children:["No tweets to display. ",(0,i.jsx)("a",{href:"https://x.com/rki_de",target:"_blank",rel:"noopener noreferrer",children:"Visit @rki_de on X"})]})})})]})};a()}catch(e){a(e)}})},40691:(e,t,s)=>{"use strict";s.d(t,{C:()=>l,_:()=>r});var a=s(8732),i=s(82015);let n=(0,i.createContext)({isLoaded:!1,loadError:!1}),r=()=>(0,i.useContext)(n),l=({language:e,children:t})=>{let[s,r]=(0,i.useState)(!1),[l,o]=(0,i.useState)(!1);return(0,i.useEffect)(()=>{let t=document.getElementById("google-maps-script");t&&t.remove();let s=document.createElement("script"),a=process.env.NEXT_PUBLIC_MAP_KEY||"AIzaSyBPIOfjzl_N220i0sBU3zHNLMc8bOftd-E";return s.id="google-maps-script",s.src=`https://maps.googleapis.com/maps/api/js?key=${a}&language=${e}`,s.async=!0,s.defer=!0,s.onload=()=>r(!0),s.onerror=()=>o(!0),document.head.appendChild(s),()=>{r(!1)}},[e]),(0,a.jsx)(n.Provider,{value:{isLoaded:s,loadError:l},children:t})}},45196:(e,t,s)=>{"use strict";s.d(t,{a:()=>n});var a=s(82015),i=s.n(a);let n=()=>{let e=i().useRef(!1);return i().useEffect(()=>(e.current=!0,()=>{e.current=!1}),[]),e}},47213:(e,t,s)=>{"use strict";s.d(t,{A:()=>h});var a=s(8732),i=s(19918),n=s.n(i),r=s(16220),l=s(88751);function o(e){let{t}=(0,l.useTranslation)("common");(0,r.useRouter)();let{item:s}=e,i="",o=RegExp(`/${s.route}`);return 0!==s.route.length&&(i=o.test("")?"active":""),s.route.length,(0,a.jsx)("li",{children:(0,a.jsxs)(n(),{href:`/${s.route}`,className:i,children:[(0,a.jsx)("div",{className:"leftmenuIcon",style:{width:30,height:30,backgroundImage:`url(${s.icon})`}}),(0,a.jsx)("span",{children:t(s.title)})]})})}var c=s(81366);let d=s.n(c)()({authenticatedSelector:(e,t)=>!!e.permissions&&!!e.permissions[t.item.id]&&!!e.permissions[t.item.id]["read:any"]&&(e.user&&e.user.is_focal_point?"Approved"==e.user.status:!e.user||!e.user.is_vspace||"Approved"==e.user.vspace_status),wrapperDisplayName:"CanAccessPages"});function u(e){let{items:t}=e,s=d(e=>(0,a.jsx)(o,{item:e.item}));return(0,a.jsx)("ul",{children:t.map((e,t)=>(0,a.jsx)(s,{item:e},t))})}let p=s(48743);function h(e){return(0,a.jsx)("div",{className:"sideMenu",children:(0,a.jsx)(u,{items:p})})}},48393:(e,t,s)=>{"use strict";s.d(t,{A:()=>l});var a=s(8732),i=s(12403),n=s(93024),r=s(88751);function l(e){let{t}=(0,r.useTranslation)("common");return(0,a.jsxs)(i.A,{...e,size:"lg","aria-labelledby":"help-modal",className:"helps-modal",children:[(0,a.jsx)(i.A.Header,{closeButton:!0,children:(0,a.jsx)(i.A.Title,{id:"help-modal",children:t("help.tab")})}),(0,a.jsx)(i.A.Body,{children:(0,a.jsx)("ul",{children:(0,a.jsxs)(n.A,{children:[(0,a.jsxs)(n.A.Item,{eventKey:"0",children:[(0,a.jsx)("li",{className:"help-content-li-title",children:(0,a.jsx)(n.A.Header,{children:t("help.tab1")})}),(0,a.jsx)(n.A.Body,{className:"help-content-li-content",children:(0,a.jsxs)("p",{children:[" ",t("helpDesc.tab1")]})})]}),(0,a.jsxs)(n.A.Item,{eventKey:"1",children:[(0,a.jsx)("li",{className:"help-content-li-title",children:(0,a.jsx)(n.A.Header,{children:t("help.tab2")})}),(0,a.jsx)(n.A.Body,{className:"help-content-li-content",children:(0,a.jsx)("p",{children:t("helpDesc.tab2")})})]}),(0,a.jsxs)(n.A.Item,{eventKey:"2",children:[(0,a.jsx)("li",{className:"help-content-li-title",children:(0,a.jsx)(n.A.Header,{children:t("help.tab3")})}),(0,a.jsx)(n.A.Body,{className:"help-content-li-content",children:(0,a.jsx)("p",{children:t("helpDesc.tab3")})})]}),(0,a.jsxs)(n.A.Item,{eventKey:"3",children:[(0,a.jsx)("li",{className:"help-content-li-title",children:(0,a.jsx)(n.A.Header,{children:t("help.tab4")})}),(0,a.jsx)(n.A.Body,{className:"help-content-li-content",children:(0,a.jsx)("p",{children:t("helpDesc.tab4")})})]})]})})})]})}},48743:e=>{"use strict";e.exports=JSON.parse('[{"id":"dashboard","title":"menu.dashboard","route":"","icon":"/images/menu/icon01.png"},{"id":"country","title":"menu.countries","route":"country","icon":"/images/menu/icon02.png"},{"id":"institution","title":"menu.organisations","route":"institution","icon":"/images/menu/icon03.png"},{"id":"operation","title":"menu.operations","route":"operation","icon":"/images/menu/icon04.png"},{"id":"project","title":"menu.projects","route":"project","icon":"/images/menu/icon05.png"},{"id":"event","title":"menu.events","route":"event","icon":"/images/menu/icon06.png"},{"id":"hazard","title":"menu.hazards","route":"hazard","icon":"/images/menu/icon07.png"},{"id":"vspace","title":"menu.virtualSpaces","route":"vspace","icon":"/images/menu/icon08.png"},{"id":"users","title":"menu.people","route":"people","icon":"/images/menu/icon10.png"},{"id":"admin","title":"menu.adminSettings","route":"adminsettings","icon":"/images/menu/icon09.png"}]')},49856:(e,t,s)=>{"use strict";s.d(t,{QN:()=>a,aE:()=>n,js:()=>i,y7:()=>r});let a={LOAD_DATA:"LOAD_DATA",LOAD_DATA_SUCCESS:"LOAD_DATA_SUCCESS",LOAD_USER_PERMISSIONS:"LOAD_USER_PERMISSIONS"};function i(){return{type:a.LOAD_DATA}}function n(e){return{type:a.LOAD_DATA_SUCCESS,data:e}}function r(e){return{type:a.LOAD_USER_PERMISSIONS,data:e}}},54368:(e,t,s)=>{"use strict";s.a(e,async(e,a)=>{try{s.d(t,{A:()=>g});var i=s(39756),n=s(93787),r=s(22541),l=s.n(r),o=s(68455),c=s(16875),d=s(21502),u=s(61775),p=e([i,o,u]);[i,o,u]=p.then?(await p)():p;let h=(0,i.combineReducers)({user:c.A,permissions:d.A}),m={key:"root",storage:l()},x=(0,n.persistReducer)(m,h),j=e=>(0,i.applyMiddleware)(...e),g=function(e){let t=(0,o.default)(),s=(0,i.createStore)(x,e,j([t]));return s.sagaTask=t.run(u.A),s};a()}catch(e){a(e)}})},58338:(e,t,s)=>{"use strict";s.a(e,async(e,a)=>{try{s.d(t,{A:()=>_});var i=s(8732),n=s(19918),r=s.n(n),l=s(91923),o=s(63241),c=s(23778),d=s(35623),u=s(63899),p=s(14062),h=s(44233),m=s.n(h),x=s(82015),j=s(36653),g=s.n(j),y=s(84337),A=s(28286),b=s(16553),v=s(48393),f=s(63487),C=s(27989),w=s(88751);s(27825);var N=e([p,y,b,f]);[p,y,b,f]=N.then?(await N)():N,g().configure({showSpinner:!1});let _=(0,p.connect)(e=>e)(function(e){let{t,i18n:s}=(0,w.useTranslation)("common"),[a,n]=(0,x.useState)(""),[p,j]=(0,x.useState)("/images/rkiProfile.jpg"),[f,N]=(0,x.useState)(!1),[_,S]=(0,x.useState)(!1),[k,L]=(0,x.useState)(!1),[I,T]=(0,x.useState)([]),D=(0,h.useRouter)(),{pathname:H,asPath:P,query:E}=D,[q,U]=(0,x.useState)(D.locale);m().events.on("routeChangeStart",()=>g().start()),m().events.on("routeChangeComplete",()=>g().done()),m().events.on("routeChangeError",()=>g().done());let M=async()=>{let{logout:e}=y.A;await e(),m().push("/home")},O=e=>{"de"===e.abbr?s.changeLanguage(e.abbr):s.changeLanguage("en"),U(e.abbr),D.push({pathname:H,query:E},P,{locale:e.abbr})};return(0,i.jsxs)(l.A,{sticky:"top",expand:"lg",variant:"light",bg:"light",children:[(0,i.jsx)(l.A.Brand,{href:"#",children:(0,i.jsx)("img",{src:"/images/logo.jpg",alt:"Rohert Koch Institut - Logo"})}),(0,i.jsx)("div",{className:"me-auto"}),(0,i.jsx)("div",{className:"headerMenu",children:(0,i.jsxs)("ul",{children:[(0,i.jsx)("li",{children:(0,i.jsx)("a",{style:{cursor:"pointer"},onClick:()=>N(!0),id:"about",children:t("about")})}),(0,i.jsx)("li",{children:(0,i.jsx)("a",{style:{cursor:"pointer"},onClick:()=>S(!0),id:"contact",children:t("contact")})})]})}),(0,i.jsxs)("div",{className:"headerIcons",children:[(0,i.jsx)(o.A,{placement:"left",delay:{show:250,hide:400},overlay:(0,i.jsx)(c.A,{id:"language-tooltip",children:t("Languages")}),children:(0,i.jsx)(d.A,{title:q||"en",className:"language",id:"basic-nav-dropdown",children:I&&I.map((e,t)=>(0,i.jsxs)("div",{children:[(0,i.jsxs)(d.A.Item,{active:e.abbr===q,eventKey:e._id,onClick:()=>O(e),children:[e.abbr,"-",e.title]}),(0,i.jsx)(u.A.Divider,{})]},t))})}),(0,i.jsx)("a",{onClick:()=>(0,C.A)("main-content"),className:"topiconLinks",children:(0,i.jsx)(o.A,{placement:"bottom",delay:{show:250,hide:400},overlay:(0,i.jsx)(c.A,{id:"print-tooltip",children:t("Print")}),children:(0,i.jsx)("i",{className:"fas fa-print"})})}),(0,i.jsx)(r(),{href:"#",onClick:()=>L(!0),className:"topiconLinks",children:(0,i.jsx)(o.A,{placement:"bottom",delay:{show:250,hide:400},overlay:(0,i.jsx)(c.A,{id:"print-tooltip",children:t("Help")}),children:(0,i.jsx)("i",{className:"fas fa-question-circle"})})})]}),(0,i.jsxs)("div",{className:"headerUser",children:[(0,i.jsx)("div",{className:"my-profile-icon",children:(0,i.jsx)(r(),{href:"/profile",children:(0,i.jsx)(o.A,{placement:"bottom",delay:{show:250,hide:400},overlay:(0,i.jsx)(c.A,{id:"profile-tooltip",children:t("Profile")}),children:(0,i.jsx)("img",{src:p,style:{background:"rgb(245, 245, 245)",borderRadius:"50%",width:"35px",height:"35px"}})})})}),(0,i.jsxs)(d.A,{title:a,id:"basic-nav-dropdown",children:[(0,i.jsx)(d.A.Item,{children:(0,i.jsx)(r(),{href:"/profile",children:t("Profile")})}),(0,i.jsx)(d.A.Item,{onClick:M,children:t("Logout")})]})]}),(0,i.jsx)(A.A,{...e,show:f,onHide:()=>N(!1)}),(0,i.jsx)(b.A,{...e,show:_,onHide:()=>S(!1)}),(0,i.jsx)(v.A,{...e,show:k,onHide:()=>L(!1)})]})});a()}catch(e){a(e)}})},61421:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>i});var a=s(8732);function i(e){return(0,a.jsx)("div",{className:"container-fluid p-0 response-message-block",children:(0,a.jsx)("div",{className:"message",children:"403 | Forbidden"})})}},61775:(e,t,s)=>{"use strict";s.a(e,async(e,a)=>{try{s.d(t,{A:()=>d});var i=s(13364),n=s(69722),r=s.n(n),l=s(28249),o=s(73253),c=e([l,o]);[l,o]=c.then?(await c)():c,r().polyfill();let d=function*(){yield(0,i.all)([l.A,o.A])};a()}catch(e){a(e)}})},62642:(e,t,s)=>{"use strict";s.d(t,{A:()=>n});var a=s(44233),i=s.n(a);function n(e){return(e?.status===401&&i().push("/home"),e?.status===400||e?.status===403)?e?.data&&e?.data.message?e.data.message[0]:[]:{}}},63198:(e,t,s)=>{"use strict";s.a(e,async(e,a)=>{try{s.d(t,{A:()=>h});var i=s(8732),n=s(82015),r=s(14062),l=s(44233),o=s.n(l),c=s(30370),d=e([r]);r=(d.then?(await d)():d)[0];let u=["/home","/login","/forgot-password","/reset-password/[passwordToken]","/declarationform/[...routes]"],p=e=>e.displayName||e.name||"Component",h=function(e){class t extends n.Component{static{this.displayName=`withAuthSync(${p(e)})`}static async getInitialProps(t){let s=e.getInitialProps&&await e.getInitialProps(t);return t.ctx&&t.ctx.req&&t.ctx.req.cookies&&(s.objCookies=t.ctx&&t.ctx.req&&t.ctx.req.cookies?t.ctx.req.cookies:{}),{...s}}constructor(e){super(e),this.state={isLoading:!0,cookie:this.props&&this.props.objCookies&&this.props.objCookies["connect.sid"]?this.props.objCookies["connect.sid"]:null}}componentDidMount(){let{route:e}=this.props.router;if(o().events.on("routeChangeComplete",e=>{"/home"===e&&this.setState({isLoading:!1})}),!this.state.cookie&&-1===u.indexOf(e))return void this.props.router.push("/home");this.setState({isLoading:!1})}componentWillUnmount(){o().events.off("routeChangeComplete",()=>null)}render(){let{router:t}=this.props,s=u.indexOf(t.route)>-1;return this.state.isLoading?(0,i.jsx)(c.A,{}):(0,i.jsx)(e,{isLoading:this.state.isLoading,isPublicRoute:s,...this.props})}}return(0,r.connect)(e=>e)(t)};a()}catch(e){a(e)}})},63487:(e,t,s)=>{"use strict";s.a(e,async(e,a)=>{try{s.d(t,{A:()=>d});var i=s(1428),n=s(84337),r=s(62642),l=e([i,n]);[i,n]=l.then?(await l)():l;let o=i.default;class c{constructor(){this.getLanguage=()=>"en",this.post=async(e,t={},s={})=>{let a=await n.A.getAuthHeader();t.language=t.language?t.language:this.getLanguage();try{let i=await o.post(e,t,{headers:{...a,...s},withCredentials:!0});if((201===i.status||200===i.status)&&i.data)return i.data}catch(e){return(0,r.A)(e?.response?e.response:{})}},this.get=async(e,t)=>{let s={params:{...t}};try{let t=await o.get(e,{...s,withCredentials:!0});if(200===t.status&&t.data)return console.log(t),t.data}catch(e){return(0,r.A)(e?.response?e.response:{})}},this.update=async(e,t={})=>{let s=await n.A.getAuthHeader();try{let a=await o.put(e,t,{headers:s,withCredentials:!0});if(200===a.status&&a.data)return a.data}catch(e){return(0,r.A)(e?.response?e.response:{})}},this.patch=async(e,t={})=>{let s=await n.A.getAuthHeader();t.language=t.language?t.language:this.getLanguage();try{let a=await o.patch(e,t,{headers:s,withCredentials:!0});if(200===a.status&&a.data)return a.data}catch(e){return(0,r.A)(e?.response?e.response:{})}},this.remove=async e=>{let t=await n.A.getAuthHeader();try{let s=await o.delete(e,{headers:t,withCredentials:!0});if(200===s.status&&s.data)return s.data}catch(e){return(0,r.A)(e?.response?e.response:{})}},o.defaults.baseURL="http://localhost:3001/api/v1",o.defaults.withCredentials=!0,this.axios=o}}let d=new c;a()}catch(e){a(e)}})},65527:(e,t,s)=>{"use strict";s.d(t,{A:()=>b});var a=s(8732),i=s(82015);s(27825);var n=s(19918),r=s.n(n),l=s(91353),o=s(4048),c=s.n(o),d=s(44233),u=s.n(d),p=s(16220),h=s(78219),m=s.n(h),x=s(74716),j=s.n(x);function g(e){let t={Announcement:"fa-bullhorn","Calendar Event":"fa-calendar",Link:"fa-link",Contact:"fa-phone-square",Document:"fa-file","General / Notice":"fa-bullhorn",Conversation:"fa-comment",Image:"fa-image"}[e.type];return(0,a.jsx)("i",{className:`fas ${t} ${e.isActive&&"isIconActive"}`})}var y=s(99789),A=s(88751);function b(e){let{item:t,updateTypes:s}=e,n=(0,p.usePathname)();(0,p.useSearchParams)();let o=n.split("/").slice(1),d=o.length>=4&&"update"===o[2]&&o[3]===t._id,h=n.substr(1).split("/"),[x,b]=(0,i.useState)(!1),[v,f]=(0,i.useState)(""),C=()=>b(!1),{t:w}=(0,A.useTranslation)("common"),N=()=>{e.onRemoveUpdate(t._id),b(!1);let s=`parent_${t.type}`;u().push(`/${t.type}/show/${t[s]}`)},_=()=>{if(t&&t.type&&t._id){let e=`parent_${t.type}`;u().push(`/${t.type}/[...routes]`,`/${t.type}/show/${t[e]}/update/${t._id}`)}},S=()=>(0,a.jsx)(r(),{href:{pathname:"/updates/[...routes]",query:{parent_type:h[0],update_type:t.update_type}},as:`/updates/edit/${t._id}?parent_type=${h[0]}&update_type=${t.update_type}`,children:(0,a.jsx)("i",{className:"icon fas fa-edit"})}),k=()=>(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)("i",{className:"icon fas fa-trash-alt",onClick:()=>b(!0)}),(0,a.jsxs)(m(),{size:"sm",show:x,onHide:()=>b(!1),"aria-labelledby":"example-modal-sizes-title-sm",children:[(0,a.jsx)(m().Header,{closeButton:!0,children:(0,a.jsx)(m().Title,{id:"example-modal-sizes-title-sm",children:w("updates")})}),(0,a.jsx)(m().Body,{children:w("Areyousureyouwanttodeletetheupdate")}),(0,a.jsxs)(m().Footer,{children:[(0,a.jsx)(l.A,{variant:"secondary",onClick:C,children:w("Cancel")}),(0,a.jsx)(l.A,{variant:"primary",onClick:N,children:w("Ok")})]})]})]}),L=(0,y.iK)(()=>(0,a.jsx)(S,{})),I=(0,y.Lg)(()=>(0,a.jsx)(k,{}));return(0,a.jsxs)("li",{children:[(0,a.jsx)("div",{className:`timeline-badge ${d&&"timeline-isActive"} `,onClick:_,children:(0,a.jsx)(g,{type:v,getupdateData:_,isActive:!!d})}),(0,a.jsxs)("div",{className:"timeline-content",children:[(0,a.jsx)("span",{className:"title",onClick:_,children:t.title}),(0,a.jsxs)("span",{className:"date",children:[j()(t.updated_at).format("MM-D-YYYY")," "]}),(0,a.jsx)("p",{className:"description",children:(0,a.jsx)(c(),{lines:1,width:1400,children:t.description?(e=>{let t=document.createElement("div");t.innerHTML=e;let s=t.textContent||t.innerText||"";return s.length>120?`${s.substring(0,117)}...`:s})(t.description):null})}),(0,a.jsxs)("div",{className:"updatesAction",children:[(0,a.jsx)("div",{children:(0,a.jsx)(L,{update:t})}),(0,a.jsx)("div",{children:(0,a.jsx)(I,{update:t})})]})]})]})}},67019:(e,t,s)=>{"use strict";s.a(e,async(e,a)=>{try{s.d(t,{A:()=>C});var i=s(8732),n=s(82015),r=s(16220),l=s(44233),o=s(19918),c=s.n(o);s(27825);var d=s(63241),u=s(23778),p=s(63899),h=s(1332),m=s(17663),x=s(63487),j=s(214),g=s(88751),y=s(35032),A=s(27989),b=s(99789),v=s(45196),f=e([m,x,y]);function C(){let{t:e}=(0,g.useTranslation)("common"),t=(0,l.useRouter)(),s=(0,r.usePathname)();(0,v.a)();let a=s.substring(1).split("/"),{updates:o,setUpdates:p}=(0,n.useContext)(j.bM),[f,C]=(0,n.useState)(!1),[w,_]=(0,n.useState)([]),[S,k]=(0,n.useState)(!1),[L,I]=(0,n.useState)(""),T={sort:{updated_at:"desc"},limit:15,select:"-contact_details -document -end_date -images -link -media -reply -show_as_announcement -start_date"},D=async e=>{await x.A.remove(`/updates/${e}`);let t=o.filter(t=>t._id!==e);p(t)},H=e=>{t.push({pathname:"/updates/[...routes]",query:{parent_id:t.query.routes?.[1],parent_type:a[0],update_type:e._id}},`/updates/add?parent_id=${t.query.routes?.[1]}&parent_type=${a[0]}&update_type=${e._id}`)},P=async(e=!1)=>{try{var t,s;let a,i=await x.A.get("/updates",T);i&&Array.isArray(i.data)&&(!i.hasNextPage&&i.data.length<=15?C(!1):C(!0),a=i.data,o&&o.length>0&&(t=o,s=a,t[0].parent,s[0].parent),e?p(a):p([...o,...a]))}catch(e){p([])}},E=()=>{L&&(T.query={update_type:L}),P()},q=()=>(0,i.jsxs)("div",{children:[(0,i.jsx)("div",{className:"updatesBlock",style:S?{height:"calc(56vh - 70px)"}:void 0,children:(0,i.jsxs)(h.Scrollbars,{className:S?"dashboardUpdateScroll":"updatesScrollbar",children:[(0,i.jsxs)("div",{className:"rightTitle",children:[(0,i.jsx)("h3",{children:e("updates")}),(0,i.jsx)(c(),{href:"#",onClick:()=>(0,A.A)("update-content"),className:"topiconLinks",style:o&&0===o.length?{pointerEvents:"none"}:{pointerEvents:"auto"},children:(0,i.jsx)(d.A,{placement:"bottom",delay:{show:250,hide:400},overlay:(0,i.jsx)(u.A,{id:"print-update-tooltip",children:e("Print")}),children:(0,i.jsx)("i",{className:"fas fa-print"})})}),t.query&&t.query.hasOwnProperty("routes")&&t.query.routes?.[0]==="show"&&(0,i.jsx)(N,{onClickDropdown:H,updateTypes:w,t:e})]}),o&&o.length>0&&w&&w.length>0&&(0,i.jsx)(m.A,{loadMoreButton:f,loadMore:E,updates:o,updateTypes:w.map(({_id:e,title:t})=>({_id:e,title:t})),onRemoveUpdate:D})]})}),S&&(0,i.jsx)(y.A,{})]}),U=(0,b.le)(()=>(0,i.jsx)(q,{}));return(0,i.jsx)(U,{})}[m,x,y]=f.then?(await f)():f;let w=({items:e,onClick:t,t:s})=>(0,i.jsxs)(p.A,{children:[(0,i.jsx)(p.A.Toggle,{style:{backgroundColor:"#ccc",borderColor:"#ddd",color:"#000"},id:"dropdown-basic",children:s("addUpdate")}),(0,i.jsx)(p.A.Menu,{children:e.map((e,s)=>(0,i.jsx)(p.A.Item,{onClick:()=>t(e,s),children:e.title},s))})]}),N=(0,b.Wo)(e=>(0,i.jsx)(w,{onClick:e.onClickDropdown,items:e.updateTypes,t:e.t}));a()}catch(e){a(e)}})},72386:(e,t,s)=>{"use strict";s.a(e,async(e,a)=>{try{s.r(t),s.d(t,{default:()=>k});var i=s(8732);s(82015);var n=s(14062),r=s(96196),l=s.n(r),o=s(78097),c=s.n(o),d=s(11242),u=s(93787),p=s(42893),h=s(13524),m=s(5939),x=s.n(m),j=s(88751),g=s(92437),y=s(72674);s(3318);var A=s(63198),b=s(54368),v=s(16053),f=s(40691),C=s(44233),w=e([n,p,g,A,b]);[n,p,g,A,b]=w.then?(await w)():w;let _=(0,b.A)({}),S=(0,u.persistStore)(_);function N({Component:e,pageProps:t,router:s,isPublicRoute:a,isLoading:r}){let l=(0,v.canAccessRoutes)(()=>(0,i.jsx)(e,{...t,router:s})),{locale:o}=(0,C.useRouter)();return(0,i.jsx)(f.C,{language:o||"en",children:(0,i.jsx)(n.Provider,{store:_,children:(0,i.jsx)(d.PersistGate,{loading:null,persistor:S,children:r?(0,i.jsx)(h.A,{animation:"border",variant:"primary"}):(0,i.jsxs)(i.Fragment,{children:[a?(0,i.jsx)(e,{...t,router:s}):(0,i.jsxs)(g.A,{router:s,children:[(0,i.jsx)(l,{router:s,pageProps:t}),(0,i.jsx)(y.A,{})]}),(0,i.jsx)(p.Toaster,{position:"top-right",reverseOrder:!1})]})})})})}N.getInitialProps=async e=>({...await x().getInitialProps(e)});let k=l()(b.A)(c()((0,j.appWithTranslation)((0,A.A)(N))));a()}catch(e){a(e)}})},72674:(e,t,s)=>{"use strict";s.d(t,{A:()=>x});var a=s(8732),i=s(7082),n=s(83551),r=s(49481),l=s(78219),o=s.n(l),c=s(19918),d=s.n(c),u=s(82015),p=s(88751);let h=e=>{let{t}=(0,p.useTranslation)("common");return(0,a.jsxs)("div",{className:"imprintDetails",children:[(0,a.jsxs)("div",{className:"imprintBlock",children:[(0,a.jsx)("h5",{children:t("imprintHeader.tab1")}),(0,a.jsxs)("p",{children:[(0,a.jsx)("span",{style:{fontWeight:"bold"},children:t("imprintHeader.tab2")}),(0,a.jsx)("br",{}),t("imprintContent.tab"),(0,a.jsx)("br",{}),t("imprintContent.tab1"),(0,a.jsx)("br",{}),t("imprintContent.tab2")]}),(0,a.jsxs)("h6",{children:[t("imprintContent.tab3"),(0,a.jsx)("br",{}),t("imprintContent.tab4")]}),(0,a.jsxs)("p",{children:[(0,a.jsxs)("span",{style:{fontWeight:"bold"},children:[" ",t("imprintContent.tab5")]}),(0,a.jsx)("br",{}),t("imprintContent.tab6")]}),(0,a.jsx)("h6",{children:t("imprintHeader.tab3")}),(0,a.jsxs)("p",{children:[" ",t("imprintContent.tab7")]}),(0,a.jsx)("h6",{children:t("imprintHeader.tab4")}),(0,a.jsxs)("p",{children:[t("imprintContent.tab8"),(0,a.jsx)("br",{}),t("imprintContent.tab9")]}),(0,a.jsx)("h6",{children:t("imprintHeader.tab5")}),(0,a.jsxs)("p",{children:[" ",t("imprintContent.tab10")]}),(0,a.jsx)("h6",{children:t("imprintHeader.tab6")}),(0,a.jsxs)("p",{children:[" ",t("imprintContent.tab11")]}),(0,a.jsx)("h6",{children:t("imprintHeader.tab7")}),(0,a.jsxs)("p",{children:[" ",t("imprintContent.tab12"),(0,a.jsx)("br",{}),(0,a.jsx)("a",{href:"https://adappt.co.uk/",children:t("imprintContent.tab12a")})]})]}),(0,a.jsxs)("div",{className:"imprintBlock",children:[(0,a.jsx)("h5",{children:t("imprintHeader.tab8")}),(0,a.jsx)("p",{children:t("imprintContent.tab13")}),(0,a.jsx)("p",{children:t("imprintContent.tab14")}),(0,a.jsx)("p",{children:t("imprintContent.tab15")}),(0,a.jsx)("p",{children:t("imprintContent.tab16")}),(0,a.jsx)("p",{children:t("imprintContent.tab17")}),(0,a.jsx)("p",{children:t("imprintContent.tab18")}),(0,a.jsx)("p",{children:t("imprintContent.tab19")})]}),(0,a.jsxs)("div",{className:"imprintBlock",children:[(0,a.jsx)("h5",{children:t("imprintHeader.tab9")}),(0,a.jsx)("p",{children:t("imprintContent.tab20")})]})]})};function m(e){let{dialogClassName:t}=e,{t:s}=(0,p.useTranslation)("common");return(0,a.jsxs)(o(),{...e,size:"lg",dialogClassName:t,"aria-labelledby":"contained-modal-title-vcenter",centered:!0,children:[(0,a.jsx)(o().Header,{closeButton:!0,children:(0,a.jsx)(o().Title,{id:"contained-modal-title-vcenter",children:s("imprintHeader.tab")})}),(0,a.jsx)(o().Body,{children:(0,a.jsx)(h,{})})]})}let x=function(e){let[t,s]=(0,u.useState)(!1),{t:l}=(0,p.useTranslation)("common");return(0,a.jsx)(i.A,{fluid:!0,className:"footer",children:(0,a.jsxs)(n.A,{children:[(0,a.jsxs)(r.A,{md:"5",children:[(0,a.jsx)("span",{style:{fontFamily:"verdana"},children:"\xa9"})," ",new Date().getFullYear()," ",l("RobertKochInstitute")]}),(0,a.jsxs)(r.A,{md:"7",style:{textAlign:"right"},children:[(0,a.jsxs)("a",{href:"#",onClick:()=>s(!0),children:[" ",l("Imprint")," "]})," ","|"," ",(0,a.jsx)(d(),{href:"/data-privacy-policy",as:"/data-privacy-policy",children:l("DataPrivacyPolicy")}),(0,a.jsxs)("span",{children:[" | ",l("Allrightsreservedunlessexplicitlygranted")]})]}),(0,a.jsx)(m,{...e,show:t,onHide:()=>s(!1)})]})})}},73253:(e,t,s)=>{"use strict";s.a(e,async(e,a)=>{try{s.d(t,{A:()=>o});var i=s(13364),n=s(49856),r=s(63487),l=e([r]);r=(l.then?(await l)():l)[0];let o=(0,i.takeEvery)(n.QN.LOAD_DATA,function*(){try{let e=yield r.A.get("/permissions");yield(0,i.put)((0,n.y7)(e))}catch(e){console.log(e)}});a()}catch(e){a(e)}})},84337:(e,t,s)=>{"use strict";s.a(e,async(e,a)=>{try{s.d(t,{A:()=>c});var i=s(1428),n=e([i]);let r=(i=(n.then?(await n)():n)[0]).default,l="application/json";class o{constructor(){this.auth=async e=>{try{let t=await r.post("http://localhost:3001/api/v1/auth/login",{username:e.username,password:e.password},{headers:{"Content-Type":l},withCredentials:!0});if(201===t.status&&t.data)return t}catch(e){return e.response?e.response:{}}},this.logout=async()=>{try{await r.post("http://localhost:3001/api/v1/auth/logout",{},{headers:{"Content-Type":l},withCredentials:!0}),localStorage.removeItem("persist:root")}catch(e){return e.response?e.response:{}}},this.getAuthHeader=async()=>({"Content-Type":l})}}let c=new o;a()}catch(e){a(e)}})},92437:(e,t,s)=>{"use strict";s.a(e,async(e,a)=>{try{s.d(t,{A:()=>m});var i=s(8732),n=s(7082),r=s(83551),l=s(49481),o=s(82015),c=s(58338),d=s(47213),u=s(67019),p=s(214),h=e([c,u]);[c,u]=h.then?(await h)():h;let x=e=>{let t=e.router.query.routes||[],s=t&&t[0]?t[0]:"",a=e.router.route;return!!("create"===s||"edit"===s||"/vspace"===a||"/people"===a||"/profile"===a||a.includes("/updates")&&"add"===s||a.indexOf("adminsettings")>-1||a.indexOf("users")>-1||a.indexOf("search")>-1||a.indexOf("data-privacy-policy")>-1)};function m(e){let t=(0,p.M)(),[s,a]=(0,o.useState)(!1),[h,m]=(0,o.useState)(!1),j=()=>{m(!h),a(!1)},g=()=>{m(!1),a(!s)};return(0,i.jsx)(n.A,{fluid:!0,className:"main-container",children:(0,i.jsx)(r.A,{children:(0,i.jsxs)(l.A,{className:"p-0",children:[(0,i.jsx)(c.A,{}),(0,i.jsx)(n.A,{fluid:!0,className:"px-2",children:(0,i.jsxs)(r.A,{children:[(0,i.jsxs)("div",{className:`p-0 sidebar-region ${s&&"show-leftmenu"}`,children:[(0,i.jsx)("div",{className:"mobile-leftmenu",onClick:()=>g(),children:">>"}),(0,i.jsx)(d.A,{})]}),(0,i.jsx)(l.A,{className:"content-region",id:"main-content",children:(0,i.jsx)(p.bM.Provider,{value:t,children:(0,i.jsxs)(r.A,{children:[(0,i.jsx)(l.A,{xs:x(e)?"12":"auto",className:x(e)?"position-relative px-3 py-2 ":"px-3 py-2 content-block",children:(0,i.jsx)("div",{id:"main",children:e.children})}),x(e)?null:(0,i.jsxs)("div",{className:`sidebar-rightregion ${h&&"show-rightmenu"}`,children:[(0,i.jsx)("div",{className:"mobile-rightmenu",onClick:()=>j(),children:"<<"}),(0,i.jsx)("div",{className:"sidebar-right",children:(0,i.jsx)(u.A,{})})]})]})})})]})})]})})})}a()}catch(e){a(e)}})},99789:(e,t,s)=>{"use strict";s.d(t,{Lg:()=>o,Wo:()=>n,iK:()=>l,le:()=>r});var a=s(81366),i=s.n(a);let n=i()({authenticatedSelector:e=>!!e.permissions&&!!e.permissions.update&&!!e.permissions.update["create:any"],wrapperDisplayName:"CanViewUpdateDropDown"}),r=i()({authenticatedSelector:e=>!!e.permissions&&!!e.permissions.update&&!!e.permissions.update["read:any"]&&(e.user&&e.user.is_focal_point?"Approved"==e.user.status:!e.user||!e.user.is_vspace||"Approved"==e.user.vspace_status),wrapperDisplayName:"CanViewUpdateRegion"}),l=i()({authenticatedSelector:(e,t)=>{if(e.permissions&&e.permissions.update){if(e.permissions.update["update:any"])return!0;else if(e.permissions.update["update:own"]&&t.update&&t.update.user&&t.update.user._id===e.user._id)return!0}return!1},wrapperDisplayName:"CanEditUpdate"}),o=i()({authenticatedSelector:(e,t)=>{if(e.permissions&&e.permissions.update){if(e.permissions.update["delete:any"])return!0;else if(t.update&&t.update.user&&t.update.user._id===e.user._id)return!0}return!1},wrapperDisplayName:"CanDeleteUpdate"})}};