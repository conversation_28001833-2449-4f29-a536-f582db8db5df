"use strict";exports.id=5272,exports.ids=[5272],exports.modules={5850:(t,e,i)=>{i.a(t,async(t,s)=>{try{i.r(e),i.d(e,{default:()=>c});var a=i(8732);i(82015);var n=i(93024),r=i(42447),o=i(88751),l=t([r]);r=(l.then?(await l)():l)[0];let c=t=>{let{t:e}=(0,o.useTranslation)("common");return(0,a.jsx)(n.A,{defaultActiveKey:"1",children:(0,a.jsxs)(n.A.Item,{eventKey:"1",children:[(0,a.jsx)(n.A.<PERSON>,{children:(0,a.jsx)("div",{className:"cardTitle",children:e("MediaGallery")})}),(0,a.jsx)(n.A.Body,{children:(0,a.jsx)(r.A,{gallery:t.images,imageSource:t.images_src})})]})})};s()}catch(t){s(t)}})},15272:(t,e,i)=>{i.a(t,async(t,s)=>{try{i.r(e),i.d(e,{default:()=>m});var a=i(8732),n=i(82015),r=i(14062),o=i(7082),l=i(27825),c=i.n(l),d=i(77136),u=i(63487),h=i(36954),p=i(16567),j=i(97125),x=t([r,d,u,h,p,j]);[r,d,u,h,p,j]=x.then?(await x)():x;let m=(0,r.connect)(t=>t)(t=>{let[e,i]=(0,n.useState)({title:"",description:"",dial_code:"",telephone:"",address:{line_1:"",line_2:"",city:""},website:"",type:{title:""},networks:[],expertise:[],focal_points:[],primary_focal_point:"",images:[],header:"",department:"",unit:"",partners:[],images_src:[]}),[s,r]=(0,n.useState)({partners:0,operations:0,projects:0,operationData:[],projectData:[]}),[l,x]=(0,n.useState)([]),[m,f]=(0,n.useState)([]),[g,y]=(0,n.useState)([]),[A,v]=(0,n.useState)(!0),[_,N]=(0,n.useState)(!1),w=async e=>{v(!0);let s=await u.A.get(`/institution/${t.routes[1]}`);v(!1),function(t,e){N(!1),e&&e.roles&&(e.roles.includes("SUPER_ADMIN")||e.roles.includes("PLATFORM_ADMIN")&&t.user==e._id?N(!0):e.roles.includes("GENERAL_USER")&&t.user==e._id&&N(!0))}(s,e),s.dial_code=s&&s.dial_code?s.dial_code:"",i(s)},D=async()=>{let e=await u.A.get(`/stats/institution/${t.routes[1]}`);r(e)},S=async()=>{let e=await u.A.post("/users/getLoggedUser",{});if(e&&e.roles&&e.roles.length)if(e.roles.includes("SUPER_ADMIN"))P("Approved",e);else{let i=e.institutionInvites.filter(e=>e.institutionId===t.routes[1]);i&&i.length?P(i[0].status,e):P("You dont have access.",e)}},P=(e,i)=>{w(i),D(),b(t.routes[1]),I(t.routes[1])};(0,n.useEffect)(()=>{t.routes&&t.routes[1]&&S()},[]),(0,n.useEffect)(()=>{if(e&&e.focal_points&&e.focal_points.length>0){let t=c().map(e.focal_points,"_id");T(t)}},[e]);let b=async t=>{let e=await k();if(e&&e.length>0){let i=await u.A.get("/operation",{query:{"partners.institution":[t],status:e}});i&&i.data&&i.data.length>0&&x(i.data)}},k=async()=>{let t=await u.A.get("/operation_status");if(t&&t.data&&t.data.length>0){let e=[];return c().forEach(t.data,function(t){("Deployed"===t.title||"Mobilizing"===t.title||"Monitoring"===t.title||"Ongoing"===t.title)&&e.push(t._id)}),e}return[]},I=async t=>{let e=await E();if(e&&e.length>0){let i=await u.A.get("/project",{query:{"partner_institutions.partner_institution":[t],status:e}});i&&i.data&&i.data.length>0&&f(i.data)}},E=async()=>{let t=await u.A.get("/projectStatus");if(t&&t.data&&t.data.length>0){let e=[];return c().forEach(t.data,function(t){("Ongoing"===t.title||"Planning"===t.title)&&e.push(t._id)}),e}return[]},T=async i=>{let s=await u.A.get("/users",{query:{_id:i},sort:{title:"asc"},limit:"~",select:"-firstname -lastname -password -role -country -region -institution -status -is_focal_point -mobile_number -enabled -created_at -updated_at -dataConsentPolicy -restrictedUsePolicy -acceptCookiesPolicy -withdrawConsentPolicy -medicalConsentPolicy -fullDataProtectionConsentPolicy -emailActivateToken"}),a=[];if(s?.data.forEach(e=>{e?.institutionInvites.forEach(i=>{i&&"Approved"===i.status&&i.institutionId===t.routes[1]&&a.push({...e,...{institutionId:i.institutionId,institutionName:i.institutionName,institutionStatus:i.status}})})}),a&&a.length>0){let t=a.map(t=>[{...t,isPrimary:t._id===e.primary_focal_point}]).flat().sort((t,e)=>t.isPrimary-e.isPrimary).reverse();y(t)}};return(0,a.jsx)("div",{children:(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(o.A,{fluid:!0,className:"_institutionfocalpoint",children:(0,a.jsx)(d.A,{routes:t.routes})}),(0,a.jsx)(h.default,{prop:t,imageLoading:A,institutionData:e,editAccess:_,focalPoints:g}),(0,a.jsx)(p.default,{institutionData:e,institutionStatus:s}),(0,a.jsx)(j.default,{institutionData:e,prop:t,activeProjects:m,activeOperations:l})]})})});s()}catch(t){s(t)}})},16567:(t,e,i)=>{i.a(t,async(t,s)=>{try{i.r(e),i.d(e,{default:()=>p});var a=i(8732),n=i(82015),r=i(54131),o=i(82053),l=i(83551),c=i(49481),d=i(88751),u=i(76357),h=t([r]);r=(h.then?(await h)():h)[0];let p=t=>{let{t:e}=(0,d.useTranslation)("common"),[i,s]=(0,n.useState)(!1),[h,p]=(0,n.useState)([]),[j,x]=(0,n.useState)(""),m=a=>{switch(s(!i),x(e(a)),a){case"Partners":let n=t.institutionData&&t.institutionData.partners?t.institutionData.partners:[];p(n);break;case"Operations":let r=t.institutionStatus&&t.institutionStatus.operationData?t.institutionStatus.operationData:[];p(r);break;case"Projects":let o=t.institutionStatus&&t.institutionStatus.projectData?t.institutionStatus.projectData:[];p(o)}},f=t=>{s(t)};return(0,a.jsx)(a.Fragment,{children:(0,a.jsxs)("div",{className:"institution-infographic-block",children:[(0,a.jsxs)(l.A,{children:[function(t,e,i){return(0,a.jsx)(c.A,{children:(0,a.jsxs)("div",{className:"list-group-item d-flex clickable",onClick:()=>t("Partners"),children:[(0,a.jsx)("div",{className:"quickinfo-img",children:(0,a.jsx)(o.FontAwesomeIcon,{icon:r.faUsers,color:"#fff",size:"2x"})}),(0,a.jsxs)("div",{className:"quickinfoDesc",children:[(0,a.jsx)("h5",{children:e("Partners")}),(0,a.jsx)("h4",{children:i&&i.partners?i.partners:0})]})]})})}(m,e,t.institutionStatus),(0,a.jsx)(c.A,{children:(0,a.jsxs)("div",{className:"list-group-item d-flex clickable",onClick:()=>m("Operations"),children:[(0,a.jsx)("div",{className:"quickinfo-img",children:(0,a.jsx)(o.FontAwesomeIcon,{icon:r.faLayerGroup,color:"#fff",size:"2x"})}),(0,a.jsxs)("div",{className:"quickinfoDesc",children:[(0,a.jsx)("h5",{children:e("Operations")}),(0,a.jsx)("h4",{children:t.institutionStatus&&t.institutionStatus.operations?t.institutionStatus.operations:0})]})]})}),(0,a.jsx)(c.A,{children:function(t,e,i){return(0,a.jsxs)("div",{className:"list-group-item d-flex clickable",onClick:()=>t("Projects"),children:[(0,a.jsx)("div",{className:"quickinfo-img",children:(0,a.jsx)(o.FontAwesomeIcon,{icon:r.faFolderOpen,color:"#fff",size:"2x"})}),(0,a.jsxs)("div",{className:"quickinfoDesc",children:[(0,a.jsx)("h5",{children:e("Projects")}),(0,a.jsx)("h4",{children:i&&i.projects?i.projects:0})]})]})}(m,e,t.institutionStatus)})]}),(0,a.jsx)(u.default,{isShow:i,isClose:t=>f(t),data:h,name:j})]})})};s()}catch(t){s(t)}})},36954:(t,e,i)=>{i.a(t,async(t,s)=>{try{i.r(e),i.d(e,{default:()=>o});var a=i(8732);i(82015);var n=i(34535),r=t([n]);n=(r.then?(await r)():r)[0];let o=t=>(0,a.jsx)(a.Fragment,{children:(0,a.jsxs)("div",{className:"institution-image-block",children:[!t.imageLoading&&t.institutionData&&t.institutionData.header&&t.institutionData.header._id?(0,a.jsx)("img",{className:"institution-image-cover",src:`http://localhost:3001/api/v1/image/show/${t.institutionData.header._id}`}):"",t.imageLoading?(0,a.jsx)("div",{className:"institution-imageLoader",children:(0,a.jsx)("div",{className:"spinner-border text-primary"})}):"",t.imageLoading||!t.institutionData||t.institutionData.header?"":(0,a.jsx)("img",{className:"institution-image-cover",src:"/images/rki_institute.7cb751d6.jpg"}),(0,a.jsx)("div",{className:"institution-image-inner-content",children:(0,a.jsx)(n.default,{institutionData:t.institutionData,routeData:t.prop,prop:t.prop,editAccess:t.editAccess,focalPoints:t.focalPoints})})]})});s()}catch(t){s(t)}})},76357:(t,e,i)=>{i.r(e),i.d(e,{default:()=>l});var s=i(8732);i(82015);var a=i(12403),n=i(19918),r=i.n(n),o=i(88751);let l=t=>{let{t:e}=(0,o.useTranslation)("common"),{isShow:i,isClose:n,data:l,name:c}=t,d="Projects"===c?"project":"Partners"===c?"institution":"operation",u=l.length>0?l.map((t,e)=>(0,s.jsxs)("span",{children:[(0,s.jsx)(r(),{href:`/${d}/show/${t._id}`,children:t.title}),(0,s.jsx)("hr",{})]},e)):(0,s.jsxs)("p",{children:[e("No")," ",c," ",e("Found"),"."]});return(0,s.jsxs)(a.A,{centered:!0,size:"sm",show:i,onHide:()=>n(!i),"aria-labelledby":"modal_popup",children:[(0,s.jsx)(a.A.Header,{closeButton:!0,children:(0,s.jsx)(a.A.Title,{children:c})}),(0,s.jsx)(a.A.Body,{children:(0,s.jsx)("div",{children:u})})]})}},78959:(t,e,i)=>{i.r(e),i.d(e,{default:()=>l});var s=i(8732);i(82015);var a=i(93024),n=i(19918),r=i.n(n),o=i(88751);let l=t=>{let{t:e}=(0,o.useTranslation)("common"),{institutionData:i,activeOperations:n,activeProjects:l}=t;return(0,s.jsx)(a.A,{defaultActiveKey:"0",children:(0,s.jsxs)(a.A.Item,{eventKey:"0",children:[(0,s.jsx)(a.A.Header,{children:(0,s.jsx)("div",{className:"cardTitle",children:e("MoreInfo")})}),(0,s.jsxs)(a.A.Body,{className:"institutionDetails ps-4",children:[(0,s.jsxs)("p",{children:[(0,s.jsx)("b",{children:e("OrganisationType")}),":",(0,s.jsxs)("span",{children:[" ",i.type?i.type.title:""]})]}),(0,s.jsxs)("p",{children:[(0,s.jsx)("b",{children:e("Network")}),":",(0,s.jsx)("span",{children:i.networks?i.networks.map((t,e)=>(0,s.jsx)("span",{children:(0,s.jsx)("li",{children:t.title})},e)):""})]}),(0,s.jsxs)("p",{children:[(0,s.jsx)("b",{children:e("ActiveOperation")}),":",(0,s.jsx)("span",{children:n&&n.length>0?n.map((t,e)=>(0,s.jsx)("li",{children:(0,s.jsx)(r(),{href:"/operation/[...routes]",as:`/operation/show/${t._id}`,children:t.title})},e)):(0,s.jsx)("li",{children:e("NoActiveoperationsfound")})})]}),(0,s.jsxs)("p",{children:[(0,s.jsx)("b",{children:e("Expertise")}),":",(0,s.jsxs)("span",{children:[" ",i.expertise?i.expertise.map((t,e)=>(0,s.jsxs)("li",{children:[t.title," ",(0,s.jsx)("br",{})]},e)):""]})]}),(0,s.jsxs)("p",{children:[(0,s.jsx)("b",{children:e("ActiveProject")}),":",(0,s.jsx)("span",{children:l&&l.length>0?l.map((t,e)=>(0,s.jsx)("li",{children:(0,s.jsx)(r(),{href:"/project/[...routes]",as:`/project/show/${t._id}`,children:t.title})},e)):(0,s.jsx)("li",{children:e("NoActiveprojectsfound")})})]}),(0,s.jsxs)("p",{children:[(0,s.jsx)("b",{children:e("Department")}),":",(0,s.jsx)("span",{children:i&&i.department?i.department:e("Nodepartmentfound")})]}),(0,s.jsxs)("p",{children:[(0,s.jsx)("b",{children:e("Unit")}),":",(0,s.jsx)("span",{children:i&&i.unit?i.unit:e("Nounitfound")})]})]})]})})}},97125:(t,e,i)=>{i.a(t,async(t,s)=>{try{i.r(e),i.d(e,{default:()=>u});var a=i(8732);i(82015);var n=i(93024),r=i(99368),o=i(28966),l=i(78959),c=i(5850),d=t([r,c]);[r,c]=d.then?(await d)():d;let u=t=>{let e=(0,o.canViewDiscussionUpdate)(()=>(0,a.jsx)(r.default,{...t.prop}));return(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(n.A,{className:"countryAccordionNew",children:(0,a.jsx)(l.default,{...t})}),(0,a.jsx)(n.A,{className:"countryAccordionNew",children:(0,a.jsx)(c.default,{...t.institutionData})}),(0,a.jsx)(n.A,{className:"countryAccordionNew",children:(0,a.jsx)(e,{})})]})};s()}catch(t){s(t)}})},99368:(t,e,i)=>{i.a(t,async(t,s)=>{try{i.r(e),i.d(e,{default:()=>c});var a=i(8732);i(82015);var n=i(93024),r=i(82491),o=i(88751),l=t([r]);r=(l.then?(await l)():l)[0];let c=t=>{let{t:e}=(0,o.useTranslation)("common");return(0,a.jsx)(n.A,{defaultActiveKey:"2",children:(0,a.jsxs)(n.A.Item,{eventKey:"2",children:[(0,a.jsx)(n.A.Header,{children:(0,a.jsx)("div",{className:"cardTitle",children:e("Discussions")})}),(0,a.jsx)(n.A.Body,{children:(0,a.jsx)(r.A,{type:"hazard",id:t&&t.routes?t.routes[1]:null})})]})})};s()}catch(t){s(t)}})}};