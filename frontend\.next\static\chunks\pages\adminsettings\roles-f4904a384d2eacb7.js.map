{"version": 3, "file": "static/chunks/pages/adminsettings/roles-f4904a384d2eacb7.js", "mappings": "gFACA,4CACA,uBACA,WACA,OAAe,EAAQ,KAAkD,CACzE,EACA,SAFsB,qKCqCtB,MA/BkB,IAChB,GAAM,GAAEA,CAAC,CAAE,CAAGC,CAAAA,EAAAA,EAAAA,EAAAA,CAAcA,CAAC,MA8BhBC,IA7Bb,KA6BsBA,CA5BpB,UAACC,MAAAA,UACC,WAACC,EAAAA,CAASA,CAAAA,CAACC,MAAO,CAAEC,UAAW,QAAS,EAAGC,KAAK,IAACC,UAAU,gBACzD,UAACC,EAAAA,CAAGA,CAAAA,UACF,UAACC,EAAAA,CAAGA,CAAAA,CAACC,GAAI,YACP,UAACC,EAAAA,CAAWA,CAAAA,CAACC,MAAM,cAGvB,UAACJ,EAAAA,CAAGA,CAAAA,UACF,UAACC,EAAAA,CAAGA,CAAAA,CAACC,GAAI,YACP,UAACG,IAAIA,CACHC,KAAK,6BACLC,GAAG,OAFAF,+BAIH,UAACG,EAAAA,CAAMA,CAAAA,CAACC,QAAQ,YAAYC,KAAK,cAChCnB,EAAE,mBAKT,UAACS,EAAAA,CAAGA,CAAAA,CAACD,UAAU,gBACb,UAACE,EAAAA,CAAGA,CAAAA,CAACC,GAAI,YACP,UAACS,EAAAA,OAASA,CAAAA,CAAAA,WAMtB,6GCHA,SAASC,EAASC,CAAoB,EACpC,GAAM,GAAEtB,CAAC,CAAE,CAAGC,CAAAA,EAAAA,EAAAA,EAAAA,CAAcA,CAAC,UACvBsB,EAA6B,CACjCC,gBAAiBxB,EAAE,cACnB,EACI,SACJyB,CAAO,MACPC,CAAI,WACJC,CAAS,uBACTC,CAAqB,WACrBC,CAAS,oBACTC,CAAkB,qBAClBC,CAAmB,CACnBC,kBAAgB,CAChBC,aAAW,oBACXC,CAAkB,gBAClBC,CAAc,SACdC,CAAO,WACPC,CAAS,sBACTC,CAAoB,mBACpBC,CAAiB,YACjBC,CAAU,QACVC,CAAM,kBACNC,CAAgB,CAChBC,cAAY,CAEZ,CADA,EACGC,EACJ,CAAGtB,EAGEuB,EAAiB,4BACrBtB,EACAuB,gBAAiB9C,EAAE,IAP0C,MAQ7D+C,UAAU,UACVtB,EACAC,KAAMA,GAAQ,EAAE,CAChBsB,OAAO,EACPC,2BAA4BrB,EAC5BsB,UAAWrB,EACXsB,gBAAiBf,qBACjBN,EACAsB,YAAY,EACZC,iBAAkBhB,EAClBiB,kBAAmBpB,GAA0C,GAC7DqB,eADwCrB,cACVD,GAA4B,CAAC,GAAI,GAAI,GAAI,CAA3BA,EAA+B,GAAG,CAC9EuB,oBAAqB7B,EACrB8B,oBAAqB1B,EACrB2B,aAAc1B,EACdG,iBACAG,uBACAC,oBACAoB,kBAAmB,UAACC,EAAAA,CAAYA,CAAAA,CAAAA,GAChCC,SAAU,UAACC,IAAAA,CAAEtD,UAAU,6CACvBgC,SACAC,eACAE,EACAD,mBACAlC,UAAW,WACb,EACA,MACE,UAACuD,EAAAA,EAASA,CAAAA,CAAE,GAAGlB,CAAc,EAEjC,CAEAxB,EAAS2C,YAAY,CAAG,CACtBd,WAAW,EACXE,YAAY,EACZzB,UAAW,KACXU,UAAW,GACXC,qBAAsB,KACtBC,mBAAmB,EACnBC,YAAY,EACZE,kBAAkB,CACpB,EAEA,MAAerB,QAAQA,EAAC,sDC5GT,SAAST,EAAYU,CAAuB,EACzD,MACE,UAAC2C,KAAAA,CAAGzD,UAAU,wBAAgBc,EAAMT,KAAK,EAE7C,mKC0GA,MAvGkB,IAChB,GAAM,CAACqD,EAAWC,EAAe,CAAGC,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAC,EAAE,EACzC,EAAGC,EAAW,CAAGD,CAAAA,CAqGA,CArGAA,EAAAA,QAAAA,CAAQA,EAAC,GAC1B,CAACzC,EAAW2C,EAAa,CAAGF,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAC,GACrC,CAACG,EAASC,EAAW,CAAGJ,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAC,IACjC,CAACK,EAAaC,EAAS,CAAGN,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,EAAC,GACnC,CAACO,EAAYC,EAAc,CAAGR,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAC,CAAC,GACxC,GAAEpE,CAAC,CAAE,CAAGC,CAAAA,EAAAA,EAAAA,EAAAA,CAAcA,CAAC,UAGvB4E,EAAa,CACjB,KAAQ,CAAE,MAAS,KAAM,EACzB,MAASN,EACT,KAAQ,EACR,MAAS,CAAC,CACZ,EAEM9C,EAAU,CACd,CACEqD,KAAM,QACNC,SAAU,OACZ,EACA,CACED,KAAM,SACNC,SAAU,GACVC,KAAM,GAAY,WAAC7E,MAAAA,WAAI,UAACW,IAAIA,CAACC,KAAK,6BAA6BC,GAAI,OAAvCF,qBAAyE,OAANmE,EAAEC,GAAG,WAAK,UAACpB,IAAAA,CAAEtD,UAAU,uBAA4B,OAAM,UAAC2E,IAAAA,CAAEC,QAAS,IAAMC,EAAWJ,YAAI,UAACnB,IAAAA,CAAEtD,UAAU,4BAA8B,MACtO,EACD,CAEK8E,EAAc,UAClBjB,GAAW,GACX,IAAMkB,EAAW,MAAMC,EAAAA,CAAUA,CAACC,GAAG,CAAC,SAAUZ,GAC5CU,GAAYA,EAAS7D,IAAI,EAAI6D,EAAS7D,IAAI,CAACgE,MAAM,CAAG,GAAG,CACzDvB,EAAeoB,EAAS7D,IAAI,EAC5B4C,EAAaiB,EAASI,UAAU,EAChCtB,GAAW,GAEf,EAQMtC,EAAsB,MAAO6D,EAAiBC,KAClDhB,EAAWiB,KAAK,CAAGF,EACnBf,EAAWgB,IAAI,CAAGA,EAClBxB,GAAW,GACX,IAAMkB,EAAW,MAAMC,EAAAA,CAAUA,CAACC,GAAG,CAAC,SAAUZ,GAC5CU,GAAYA,EAAS7D,IAAI,EAAI6D,EAAS7D,IAAI,CAACgE,MAAM,CAAG,GAAG,CACzDvB,EAAeoB,EAAS7D,IAAI,EAC5B8C,EAAWoB,GACXvB,GAAW,GAEf,EAEMgB,EAAa,MAAOU,IACxBnB,EAAcmB,EAAIb,GAAG,EACrBR,GAAS,EACX,EAEMsB,EAAe,UACnB,MAAMR,EAAAA,CAAUA,CAACS,MAAM,CAAC,UAAqB,OAAXtB,IAClCW,IACAZ,GAAS,EACX,EAEMwB,EAAY,IAAMxB,GAAS,GAMjC,MAJAyB,CAAAA,EAAAA,EAAAA,SAAAA,CAASA,CAAC,KACRb,GACF,EAAG,EAAE,EAGH,WAACnF,MAAAA,WACC,WAACiG,EAAAA,CAAKA,CAAAA,CAACC,KAAM5B,EAAa6B,OAAQJ,YAChC,UAACE,EAAAA,CAAKA,CAACG,MAAM,EAACC,WAAW,aACvB,UAACJ,EAAAA,CAAKA,CAACK,KAAK,WAAEzG,EAAE,kBAElB,WAACoG,EAAAA,CAAKA,CAACM,IAAI,YAAE1G,EAAE,kCAAkC,OACjD,WAACoG,EAAAA,CAAKA,CAACO,MAAM,YACX,UAAC1F,EAAAA,CAAMA,CAAAA,CAACC,QAAQ,YAAYkE,QAASc,WACpClG,EAAE,YAEH,UAACiB,EAAAA,CAAMA,CAAAA,CAACC,QAAQ,UAAUkE,QAASY,WAClChG,EAAE,eAKP,UAACqB,EAAAA,CAAQA,CAAAA,CACPI,QAASA,EACTC,KAAMwC,EACNvC,UAAWA,EACXU,WAAW,EACXN,oBAAqBA,EACrBC,iBA1DmB,CA0DDA,GAzDtB6C,EAAWiB,KAAK,CAAGvB,EACnBM,EAAWgB,IAAI,CAAGA,EAClBP,GACF,MA0DF", "sources": ["webpack://_N_E/?bb4a", "webpack://_N_E/./pages/adminsettings/roles/index.tsx", "webpack://_N_E/./components/common/RKITable.tsx", "webpack://_N_E/./components/common/PageHeading.tsx", "webpack://_N_E/./pages/adminsettings/roles/roleTable.tsx"], "sourcesContent": ["\n    (window.__NEXT_P = window.__NEXT_P || []).push([\n      \"/adminsettings/roles\",\n      function () {\n        return require(\"private-next-pages/adminsettings/roles/index.tsx\");\n      }\n    ]);\n    if(module.hot) {\n      module.hot.dispose(function () {\n        window.__NEXT_P.push([\"/adminsettings/roles\"])\n      });\n    }\n  ", "//Import Library\r\nimport { Contain<PERSON>, <PERSON>, <PERSON>, Button } from \"react-bootstrap\";\r\nimport Link from \"next/link\";\r\n\r\n//Import services/components\r\nimport PageHeading from \"../../../components/common/PageHeading\";\r\nimport RoleTable from \"./roleTable\";\r\nimport { useTranslation } from 'next-i18next';\r\n\r\n\r\nconst RoleIndex = (_props: any) => {\r\n  const { t } = useTranslation('common');\r\n  return (\r\n    <div>\r\n      <Container style={{ overflowX: \"hidden\" }} fluid className=\"p-0\">\r\n        <Row>\r\n          <Col xs={12}>\r\n            <PageHeading title=\"Roles\" />\r\n          </Col>\r\n        </Row>\r\n        <Row>\r\n          <Col xs={12}>\r\n            <Link\r\n              href=\"/adminsettings/[...routes]\"\r\n              as=\"/adminsettings/create_role\"\r\n              >\r\n              <Button variant=\"secondary\" size=\"sm\">\r\n              {t(\"AddRole\")}\r\n            </Button>\r\n            </Link>\r\n          </Col>\r\n        </Row>\r\n        <Row className=\"mt-3\">\r\n          <Col xs={12}>\r\n            <RoleTable />\r\n          </Col>\r\n        </Row>\r\n      </Container>\r\n    </div>\r\n  );\r\n}\r\nexport default RoleIndex;", "//Import Library\r\nimport React from 'react'\r\nimport DataTable  from 'react-data-table-component';\r\n\r\n//Import services/components\r\nimport { useTranslation } from 'next-i18next';\r\nimport CustomLoader from './CustomLoader';\r\n\r\ninterface RKITableProps {\r\n  columns: any[];\r\n  data: any[];\r\n  totalRows?: number;\r\n  resetPaginationToggle?: boolean;\r\n  subheader?: boolean;\r\n  subHeaderComponent?: React.ReactNode;\r\n  handlePerRowsChange?: (newPerPage: number, page: number) => void;\r\n  handlePageChange?: (page: number) => void;\r\n  rowsPerPage?: number[];\r\n  defaultRowsPerPage?: number;\r\n  selectableRows?: boolean;\r\n  loading?: boolean;\r\n  pagServer?: boolean;\r\n  onSelectedRowsChange?: (selectedRows: any) => void;\r\n  clearSelectedRows?: boolean;\r\n  sortServer?: boolean;\r\n  onSort?: (column: any, sortDirection: string) => void;\r\n  persistTableHead?: boolean;\r\n  sortFunction?: (rows: any[], field: any, direction: 'asc' | 'desc') => any[];\r\n  noHeader?: boolean;\r\n  dense?: boolean;\r\n  pagination?: boolean;\r\n  paginationServer?: boolean;\r\n  paginationTotalRows?: number;\r\n  subHeaderAlign?: string;\r\n  [key: string]: any;\r\n}\r\n\r\nfunction RKITable(props: RKITableProps) {\r\n  const { t } = useTranslation('common');\r\n  const paginationComponentOptions = {\r\n    rowsPerPageText: t('Rowsperpage'),\r\n    };\r\n  const {\r\n    columns,\r\n    data,\r\n    totalRows,\r\n    resetPaginationToggle,\r\n    subheader,\r\n    subHeaderComponent,\r\n    handlePerRowsChange,\r\n    handlePageChange,\r\n    rowsPerPage,\r\n    defaultRowsPerPage,\r\n    selectableRows,\r\n    loading,\r\n    pagServer,\r\n    onSelectedRowsChange,\r\n    clearSelectedRows,\r\n    sortServer,\r\n    onSort,\r\n    persistTableHead,\r\n    sortFunction,\r\n    // Filter out any unknown props that might cause DOM warnings\r\n    ...otherProps\r\n  } = props;\r\n\r\n  // Only pass known DataTable props to avoid DOM warnings\r\n  const dataTableProps = {\r\n    paginationComponentOptions,\r\n    noDataComponent: t(\"NoData\"),\r\n    noHeader: true,\r\n    columns,\r\n    data: data || [],\r\n    dense: true,\r\n    paginationResetDefaultPage: resetPaginationToggle,\r\n    subHeader: subheader,\r\n    progressPending: loading,\r\n    subHeaderComponent,\r\n    pagination: true,\r\n    paginationServer: pagServer,\r\n    paginationPerPage: defaultRowsPerPage ? defaultRowsPerPage : 10,\r\n    paginationRowsPerPageOptions: rowsPerPage ? rowsPerPage : [10, 15, 20, 25, 30],\r\n    paginationTotalRows: totalRows,\r\n    onChangeRowsPerPage: handlePerRowsChange,\r\n    onChangePage: handlePageChange,\r\n    selectableRows,\r\n    onSelectedRowsChange,\r\n    clearSelectedRows,\r\n    progressComponent: <CustomLoader/>,\r\n    sortIcon: <i className=\"sort-icon fas fa-exchange-alt\" />,\r\n    sortServer,\r\n    onSort,\r\n    sortFunction,\r\n    persistTableHead,\r\n    className: \"rki-table\"\r\n  };\r\n  return (\r\n    <DataTable {...dataTableProps} />\r\n  )\r\n};\r\n\r\nRKITable.defaultProps = {\r\n  subHeader: false,\r\n  pagination: true,\r\n  totalRows: null,\r\n  pagServer: true,\r\n  onSelectedRowsChange: null,\r\n  clearSelectedRows: false,\r\n  sortServer: false,\r\n  persistTableHead: false\r\n}\r\n\r\nexport default RKITable;\r\n\r\n", "interface PageHeadingProps {\r\n  title: string; // Required based on actual usage\r\n}\r\n\r\nexport default function PageHeading(props: PageHeadingProps) {\r\n  return (\r\n    <h2 className=\"page-heading\">{props.title}</h2>\r\n  )\r\n}\r\n", "//Import Library\r\nimport { useState, useEffect } from \"react\";\r\nimport Link from \"next/link\";\r\nimport { Mo<PERSON>, Button } from \"react-bootstrap\";\r\n\r\n//Import services/components\r\nimport RKITable from \"../../../components/common/RKITable\";\r\nimport apiService from \"../../../services/apiService\";\r\nimport { useTranslation } from 'next-i18next';\r\n\r\n\r\nconst RoleTable = (_props: any) => {\r\n  const [tabledata, setDataToTable] = useState([]);\r\n  const [, setLoading] = useState(false);\r\n  const [totalRows, setTotalRows] = useState(0);\r\n  const [perPage, setPerPage] = useState(10);\r\n  const [isModalShow, setModal] = useState(false);\r\n  const [selectRole, setSelectRole] = useState({});\r\n  const { t } = useTranslation('common');\r\n\r\n\r\n  const roleParams = {\r\n    \"sort\": { \"title\": \"asc\" },\r\n    \"limit\": perPage,\r\n    \"page\": 1,\r\n    \"query\": {}\r\n  };\r\n\r\n  const columns = [\r\n    {\r\n      name: 'Title',\r\n      selector: 'title',\r\n    },\r\n    {\r\n      name: 'Action',\r\n      selector: \"\",\r\n      cell: (d: any) => <div><Link href=\"/adminsettings/[...routes]\" as={`/adminsettings/edit_role/${d._id}`} ><i className=\"icon fas fa-edit\" /></Link>&nbsp;<a onClick={() => userAction(d)}><i className=\"icon fas fa-trash-alt\" /></a> </div>\r\n    }\r\n  ];\r\n\r\n  const getRoleData = async () => {\r\n    setLoading(true);\r\n    const response = await apiService.get('/roles', roleParams);\r\n    if (response && response.data && response.data.length > 0) {\r\n      setDataToTable(response.data);\r\n      setTotalRows(response.totalCount);\r\n      setLoading(false);\r\n    }\r\n  };\r\n\r\n  const handlePageChange = (page: any) => {\r\n    roleParams.limit = perPage;\r\n    roleParams.page = page;\r\n    getRoleData();\r\n  };\r\n\r\n  const handlePerRowsChange = async (newPerPage: any, page: any) => {\r\n    roleParams.limit = newPerPage;\r\n    roleParams.page = page;\r\n    setLoading(true);\r\n    const response = await apiService.get('/roles', roleParams);\r\n    if (response && response.data && response.data.length > 0) {\r\n      setDataToTable(response.data);\r\n      setPerPage(newPerPage);\r\n      setLoading(false);\r\n    }\r\n  };\r\n\r\n  const userAction = async (row: any) => {\r\n    setSelectRole(row._id);\r\n    setModal(true);\r\n  }\r\n\r\n  const modalConfirm = async () => {\r\n    await apiService.remove(`/roles/${selectRole}`);\r\n    getRoleData();\r\n    setModal(false);\r\n  }\r\n\r\n  const modalHide = () => setModal(false);\r\n\r\n  useEffect(() => {\r\n    getRoleData();\r\n  }, []);\r\n\r\n  return (\r\n    <div>\r\n      <Modal show={isModalShow} onHide={modalHide}>\r\n        <Modal.Header closeButton>\r\n          <Modal.Title>{t(\"DeleteRole\")}</Modal.Title>\r\n        </Modal.Header>\r\n        <Modal.Body>{t(\"Areyousurewanttodeletethisrole\")} </Modal.Body>\r\n        <Modal.Footer>\r\n          <Button variant=\"secondary\" onClick={modalHide}>\r\n          {t(\"cancel\")}\r\n        </Button>\r\n          <Button variant=\"primary\" onClick={modalConfirm}>\r\n          {t(\"yes\")}\r\n        </Button>\r\n        </Modal.Footer>\r\n      </Modal>\r\n\r\n      <RKITable\r\n        columns={columns}\r\n        data={tabledata}\r\n        totalRows={totalRows}\r\n        pagServer={true}\r\n        handlePerRowsChange={handlePerRowsChange}\r\n        handlePageChange={handlePageChange}\r\n      />\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default RoleTable;"], "names": ["t", "useTranslation", "RoleIndex", "div", "Container", "style", "overflowX", "fluid", "className", "Row", "Col", "xs", "PageHeading", "title", "Link", "href", "as", "<PERSON><PERSON>", "variant", "size", "RoleTable", "RKITable", "props", "paginationComponentOptions", "rowsPerPageText", "columns", "data", "totalRows", "resetPaginationToggle", "subheader", "subHeaderComponent", "handlePerRowsChange", "handlePageChange", "rowsPerPage", "defaultRowsPerPage", "selectableRows", "loading", "pagServer", "onSelectedRowsChange", "clearSelectedRows", "sortServer", "onSort", "persistTableHead", "sortFunction", "otherProps", "dataTableProps", "noDataComponent", "<PERSON><PERSON><PERSON><PERSON>", "dense", "paginationResetDefaultPage", "subHeader", "progressPending", "pagination", "paginationServer", "paginationPerPage", "paginationRowsPerPageOptions", "paginationTotalRows", "onChangeRowsPerPage", "onChangePage", "progressComponent", "CustomLoader", "sortIcon", "i", "DataTable", "defaultProps", "h2", "tabledata", "setDataToTable", "useState", "setLoading", "setTotalRows", "perPage", "setPerPage", "isModalShow", "setModal", "selectRole", "setSelectRole", "roleParams", "name", "selector", "cell", "d", "_id", "a", "onClick", "userAction", "getRoleData", "response", "apiService", "get", "length", "totalCount", "newPerPage", "page", "limit", "row", "modalConfirm", "remove", "modalHide", "useEffect", "Modal", "show", "onHide", "Header", "closeButton", "Title", "Body", "Footer"], "sourceRoot": "", "ignoreList": []}