"use strict";exports.id=5461,exports.ids=[5461],exports.modules={6417:(e,a,s)=>{s.d(a,{A:()=>n});let r=s(82015).createContext(null);r.displayName="CardHeaderContext";let n=r},15653:(e,a,s)=>{s.d(a,{ks:()=>t,s3:()=>i});var r=s(8732);s(82015);var n=s(59549),l=s(43294);let t=({name:e,id:a,required:s,validator:t,errorMessage:i,onChange:o,value:d,as:c,multiline:m,rows:u,pattern:h,...x})=>(0,r.jsx)(l.Field,{name:e,validate:e=>{let a="string"==typeof e?e:String(e||"");return s&&(!e||""===a.trim())?i?.validator||"This field is required":t&&!t(e)?i?.validator||"Invalid value":h&&e&&!new RegExp(h).test(e)?i?.pattern||"Invalid format":void 0},children:({field:e,meta:s})=>(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(n.A.Control,{...e,...x,id:a,as:c||"input",rows:u,isInvalid:s.touched&&!!s.error,onChange:a=>{e.onChange(a),o&&o(a)},value:void 0!==d?d:e.value}),s.touched&&s.error?(0,r.jsx)(n.A.Control.Feedback,{type:"invalid",children:s.error}):null]})}),i=({name:e,id:a,required:s,errorMessage:t,onChange:i,value:o,children:d,...c})=>(0,r.jsx)(l.Field,{name:e,validate:e=>{if(s&&(!e||""===e))return t?.validator||"This field is required"},children:({field:e,meta:s})=>(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(n.A.Control,{as:"select",...e,...c,id:a,isInvalid:s.touched&&!!s.error,onChange:a=>{e.onChange(a),i&&i(a)},value:void 0!==o?o:e.value,children:d}),s.touched&&s.error?(0,r.jsx)(n.A.Control.Feedback,{type:"invalid",children:s.error}):null]})})},18597:(e,a,s)=>{s.d(a,{A:()=>b});var r=s(3892),n=s.n(r),l=s(82015),t=s(80739),i=s(8732);let o=l.forwardRef(({className:e,bsPrefix:a,as:s="div",...r},l)=>(a=(0,t.oU)(a,"card-body"),(0,i.jsx)(s,{ref:l,className:n()(e,a),...r})));o.displayName="CardBody";let d=l.forwardRef(({className:e,bsPrefix:a,as:s="div",...r},l)=>(a=(0,t.oU)(a,"card-footer"),(0,i.jsx)(s,{ref:l,className:n()(e,a),...r})));d.displayName="CardFooter";var c=s(6417);let m=l.forwardRef(({bsPrefix:e,className:a,as:s="div",...r},o)=>{let d=(0,t.oU)(e,"card-header"),m=(0,l.useMemo)(()=>({cardHeaderBsPrefix:d}),[d]);return(0,i.jsx)(c.A.Provider,{value:m,children:(0,i.jsx)(s,{ref:o,...r,className:n()(a,d)})})});m.displayName="CardHeader";let u=l.forwardRef(({bsPrefix:e,className:a,variant:s,as:r="img",...l},o)=>{let d=(0,t.oU)(e,"card-img");return(0,i.jsx)(r,{ref:o,className:n()(s?`${d}-${s}`:d,a),...l})});u.displayName="CardImg";let h=l.forwardRef(({className:e,bsPrefix:a,as:s="div",...r},l)=>(a=(0,t.oU)(a,"card-img-overlay"),(0,i.jsx)(s,{ref:l,className:n()(e,a),...r})));h.displayName="CardImgOverlay";let x=l.forwardRef(({className:e,bsPrefix:a,as:s="a",...r},l)=>(a=(0,t.oU)(a,"card-link"),(0,i.jsx)(s,{ref:l,className:n()(e,a),...r})));x.displayName="CardLink";var p=s(7783);let f=(0,p.A)("h6"),j=l.forwardRef(({className:e,bsPrefix:a,as:s=f,...r},l)=>(a=(0,t.oU)(a,"card-subtitle"),(0,i.jsx)(s,{ref:l,className:n()(e,a),...r})));j.displayName="CardSubtitle";let A=l.forwardRef(({className:e,bsPrefix:a,as:s="p",...r},l)=>(a=(0,t.oU)(a,"card-text"),(0,i.jsx)(s,{ref:l,className:n()(e,a),...r})));A.displayName="CardText";let v=(0,p.A)("h5"),y=l.forwardRef(({className:e,bsPrefix:a,as:s=v,...r},l)=>(a=(0,t.oU)(a,"card-title"),(0,i.jsx)(s,{ref:l,className:n()(e,a),...r})));y.displayName="CardTitle";let g=l.forwardRef(({bsPrefix:e,className:a,bg:s,text:r,border:l,body:d=!1,children:c,as:m="div",...u},h)=>{let x=(0,t.oU)(e,"card");return(0,i.jsx)(m,{ref:h,...u,className:n()(a,x,s&&`bg-${s}`,r&&`text-${r}`,l&&`border-${l}`),children:d?(0,i.jsx)(o,{children:c}):c})});g.displayName="Card";let b=Object.assign(g,{Img:u,Title:y,Subtitle:j,Body:o,Link:x,Text:A,Header:m,Footer:d,ImgOverlay:h})},23579:(e,a,s)=>{s.d(a,{sx:()=>c,s3:()=>n.s3,ks:()=>n.ks,yk:()=>r.A});var r=s(66994),n=s(15653),l=s(8732),t=s(82015),i=s.n(t),o=s(43294),d=s(59549);let c={RadioGroup:({name:e,valueSelected:a,onChange:s,errorMessage:r,children:n})=>{let{errors:t,touched:d}=(0,o.useFormikContext)(),c=d[e]&&t[e];i().useMemo(()=>({name:e}),[e]);let m=i().Children.map(n,a=>i().isValidElement(a)&&function(e){return"object"==typeof e&&null!==e}(a.props)?i().cloneElement(a,{name:e,...a.props}):a);return(0,l.jsxs)("div",{children:[(0,l.jsx)("div",{className:"radio-group",children:m}),c&&(0,l.jsx)("div",{className:"invalid-feedback d-block",children:r||("string"==typeof t[e]?t[e]:String(t[e]))})]})},RadioItem:({id:e,label:a,value:s,name:r,disabled:n})=>{let{values:t,setFieldValue:i}=(0,o.useFormikContext)(),c=r||e;return(0,l.jsx)(d.A.Check,{type:"radio",id:e,label:a,value:s,name:c,checked:t[c]===s,onChange:e=>{i(c,e.target.value)},disabled:n,inline:!0})}};r.A,n.ks,n.s3},25169:(e,a,s)=>{s.d(a,{A:()=>y});var r=s(3892),n=s.n(r),l=s(82015),t=s(14332),i=s(81895),o=s.n(i),d=s(80739),c=s(7783),m=s(8732);let u=(0,c.A)("h4");u.displayName="DivStyledAsH4";let h=l.forwardRef(({className:e,bsPrefix:a,as:s=u,...r},l)=>(a=(0,d.oU)(a,"alert-heading"),(0,m.jsx)(s,{ref:l,className:n()(e,a),...r})));h.displayName="AlertHeading";var x=s(78634),p=s.n(x);let f=l.forwardRef(({className:e,bsPrefix:a,as:s=p(),...r},l)=>(a=(0,d.oU)(a,"alert-link"),(0,m.jsx)(s,{ref:l,className:n()(e,a),...r})));f.displayName="AlertLink";var j=s(19799),A=s(73087);let v=l.forwardRef((e,a)=>{let{bsPrefix:s,show:r=!0,closeLabel:l="Close alert",closeVariant:i,className:c,children:u,variant:h="primary",onClose:x,dismissible:p,transition:f=j.A,...v}=(0,t.useUncontrolled)(e,{show:"onClose"}),y=(0,d.oU)(s,"alert"),g=o()(e=>{x&&x(!1,e)}),b=!0===f?j.A:f,C=(0,m.jsxs)("div",{role:"alert",...!b?v:void 0,ref:a,className:n()(c,y,h&&`${y}-${h}`,p&&`${y}-dismissible`),children:[p&&(0,m.jsx)(A.A,{onClick:g,"aria-label":l,variant:i}),u]});return b?(0,m.jsx)(b,{unmountOnExit:!0,...v,ref:void 0,in:r,children:C}):r?C:null});v.displayName="Alert";let y=Object.assign(v,{Link:f,Heading:h})},66994:(e,a,s)=>{s.d(a,{A:()=>o});var r=s(8732),n=s(82015),l=s(43294),t=s(18622);let i=(0,n.forwardRef)((e,a)=>{let{children:s,onSubmit:n,autoComplete:i,className:o,onKeyPress:d,initialValues:c,...m}=e,u=t.object().shape({});return(0,r.jsx)(l.Formik,{initialValues:c||{},validationSchema:u,onSubmit:(e,a)=>{let s={preventDefault:()=>{},stopPropagation:()=>{},currentTarget:null,target:null,nativeEvent:new Event("submit"),bubbles:!1,cancelable:!0,defaultPrevented:!1,eventPhase:0,isTrusted:!1,timeStamp:Date.now(),type:"submit",isDefaultPrevented:()=>!1,isPropagationStopped:()=>!1,persist:()=>{}};n&&n(s,e,a)},...m,children:e=>(0,r.jsx)(l.Form,{ref:a,onSubmit:e.handleSubmit,autoComplete:i,className:o,onKeyPress:d,children:"function"==typeof s?s(e):s})})});i.displayName="ValidationFormWrapper";let o=i},80237:(e,a)=>{Object.defineProperty(a,"A",{enumerable:!0,get:function(){return s}});var s=function(e){return e.PAGES="PAGES",e.PAGES_API="PAGES_API",e.APP_PAGE="APP_PAGE",e.APP_ROUTE="APP_ROUTE",e.IMAGE="IMAGE",e}({})},81413:(e,a)=>{Object.defineProperty(a,"M",{enumerable:!0,get:function(){return function e(a,s){return s in a?a[s]:"then"in a&&"function"==typeof a.then?a.then(a=>e(a,s)):"function"==typeof a&&"default"===s?a:void 0}}})},81493:(e,a,s)=>{s.a(e,async(e,r)=>{try{s.r(a),s.d(a,{default:()=>k});var n=s(8732),l=s(82015),t=s(14062),i=s(18597),o=s(25169),d=s(59549),c=s(91353),m=s(83551),u=s(49481),h=s(82053),x=s(54131),p=s(23579),f=s(66994),j=s(44233),A=s.n(j),v=s(42893),y=s(49856),g=s(82601),b=s(63487),C=s(84337),w=s(88751),N=e([t,x,v,g,b,C]);[t,x,v,g,b,C]=N.then?(await N)():N;let k=(0,t.connect)()(e=>{let{t:a,i18n:s}=(0,w.useTranslation)("common"),r="de"===s.language?{title_de:"asc"}:{title:"asc"},t=s.language,{authToken:j,userProfile:N}=e,[k,P]=(0,l.useState)(!1),[I,_]=(0,l.useState)({dataConsentPolicy:!1,restrictedUsePolicy:!1,acceptCookiesPolicy:!1,medicalConsentPolicy:!1}),[E,R]=(0,l.useState)(!0),[S,U]=(0,l.useState)(!1),[F,q]=(0,l.useState)({username:"",email:"",dial_code:"",firstname:"",lastname:"",position:"",mobile_number:"",password:"",confirm_password:""}),[G,L]=(0,l.useState)([]);(0,l.useEffect)(()=>{(async e=>{let a=await b.A.get("/country",e);a&&Array.isArray(a.data)&&L(a.data)})({query:{},sort:r,limit:"~",languageCode:t})},[]);let T=e=>{let{name:a,checked:s}=e.target;_(e=>({...e,[a]:s}))};(0,l.useEffect)(()=>{s.changeLanguage(N&&N.languageCode);let e=N&&N.dial_code?N.dial_code:"";N&&q(a=>({...a,...N,password:"",dial_code:e}))},[N]),(0,l.useEffect)(()=>{I.dataConsentPolicy&&I.restrictedUsePolicy&&I.acceptCookiesPolicy&&I.medicalConsentPolicy?R(!1):(U(!1),R(!0))},[I,_,S]);let $=()=>{U(!S)},M=e=>{P(e)},D=e=>{let{name:a,value:s}=e.target;q(e=>({...e,[a]:s}))},O=async s=>{s.preventDefault();let r={code:j,username:F.username.toLowerCase().trim(),firstname:F.firstname,lastname:F.lastname,email:F.email.toLowerCase().trim(),dial_code:F.dial_code,mobile_number:F.mobile_number,password:F.password,enabled:!0,...I},n=await b.A.post("/saveInviteUserDetails",r);if(n&&n._id){let{auth:s}=C.A;n.roles.includes("SUPER_ADMIN");let l=await s({username:r.username,password:r.password});if(l&&201===l.status)v.default.success(a("toast.AccountcreatedSuccesfully")),A().push("/"),e.dispatch((0,y.js)());else{let e=l.status+" "+l.statusText;v.default.error(e),A().push("/home")}}};return(0,n.jsxs)(n.Fragment,{children:[(0,n.jsx)("div",{className:"text-center mt-2",children:(0,n.jsx)("img",{src:"/images/logo.jpg",alt:"Rohert Koch Institut - Logo"})}),(0,n.jsx)("div",{className:"d-flex justify-content-center align-items-center",children:!S&&(0,n.jsxs)(i.A,{className:"px-3 declarationcard",children:[(0,n.jsx)("p",{className:"lead px-3 pt-3 mb-0",children:(0,n.jsx)("b",{children:a("declaration.header")})}),(0,n.jsx)("hr",{className:"hr--cardfront"}),(0,n.jsxs)(i.A.Body,{children:[(0,n.jsx)(o.A,{variant:"success",className:"mt-0",children:a("declaration.info")}),(0,n.jsx)(d.A.Check,{className:"pb-4",type:"checkbox",onChange:T,name:"dataConsentPolicy",checked:I.dataConsentPolicy,value:"consent1",label:a("declaration.consent1")}),(0,n.jsx)(d.A.Check,{className:"pb-4",onChange:T,type:"checkbox",name:"medicalConsentPolicy",checked:I.medicalConsentPolicy,value:"consent2",label:a("declaration.consent2")}),(0,n.jsx)(d.A.Check,{className:"pb-4",onChange:T,type:"checkbox",name:"restrictedUsePolicy",checked:I.restrictedUsePolicy,value:"consent3",label:a("declaration.consent3")}),(0,n.jsx)(d.A.Check,{className:"pb-4",type:"checkbox",name:"acceptCookiesPolicy",onChange:T,checked:I.acceptCookiesPolicy,value:"consent4",label:a("declaration.consent4")}),(0,n.jsxs)("div",{className:"d-flex",children:[E&&(0,n.jsx)(c.A,{onClick:()=>{P(!k)},variant:"danger",className:"d-grid w-100",children:a("declaration.decline")}),!E&&(0,n.jsx)(c.A,{variant:"success",className:"mt-0 d-grid w-100",onClick:$,children:a("declaration.accept")})]})]})]})}),S&&(0,n.jsx)("div",{className:"d-flex justify-content-center align-items-center w-100",id:"main-content",children:(0,n.jsx)(f.A,{onSubmit:O,initialValues:F,enableReinitialize:!0,children:(0,n.jsxs)(i.A,{className:"declarationcard",children:[(0,n.jsxs)("div",{className:"d-flex align-items-center ms-3",children:[(0,n.jsx)(h.FontAwesomeIcon,{icon:x.faArrowAltCircleLeft,onClick:$,size:"2x",className:"icon--arrow"}),(0,n.jsx)("p",{className:"lead px-3 pt-3 pb-0 mb-0",children:(0,n.jsx)("b",{children:a("setInfo.header")})})]}),(0,n.jsx)("hr",{className:"hr--cardback"}),(0,n.jsxs)(o.A,{className:"mx-3 mb-0 mt-3",variant:"warning",children:[(0,n.jsx)(h.FontAwesomeIcon,{icon:x.faExclamationTriangle}),"\xa0",a("setInfo.info")]}),(0,n.jsxs)(i.A.Body,{children:[(0,n.jsx)(m.A,{children:(0,n.jsx)(u.A,{children:(0,n.jsxs)(d.A.Group,{as:m.A,controlId:"username",children:[(0,n.jsx)(d.A.Label,{className:"required-field",column:!0,sm:"3",children:a("setInfo.username")}),(0,n.jsx)(u.A,{sm:"9",children:(0,n.jsx)(p.ks,{className:"form-control",name:"username",id:"username",errorMessage:"Please enter your username",placeholder:"Enter your username",type:"text",required:!0,value:F.username,onChange:D})})]})})}),(0,n.jsx)(m.A,{children:(0,n.jsx)(u.A,{children:(0,n.jsxs)(d.A.Group,{as:m.A,controlId:"name",children:[(0,n.jsx)(d.A.Label,{className:"required-field",column:!0,sm:"3",children:a("setInfo.name")}),(0,n.jsx)(u.A,{children:(0,n.jsx)(p.ks,{className:"form-control",name:"firstname",id:"firstname",type:"text",required:!0,errorMessage:"Please enter your first name",placeholder:"Enter your first name",value:F.firstname,onChange:D})}),(0,n.jsx)(u.A,{children:(0,n.jsx)(p.ks,{className:"form-control",name:"lastname",id:"lastname",type:"text",placeholder:"Enter your last name",value:F.lastname,onChange:D})})]})})}),(0,n.jsx)(m.A,{children:(0,n.jsx)(u.A,{children:(0,n.jsxs)(d.A.Group,{as:m.A,controlId:"position",children:[(0,n.jsx)(d.A.Label,{column:!0,sm:"3",children:a("setInfo.position")}),(0,n.jsx)(u.A,{sm:"9",children:(0,n.jsx)(p.ks,{className:"form-control",name:"position",id:"position",type:"text",placeholder:"Enter the position",value:F.position,onChange:D})})]})})}),(0,n.jsx)(m.A,{children:(0,n.jsx)(u.A,{children:(0,n.jsxs)(d.A.Group,{as:m.A,controlId:"Email",children:[(0,n.jsx)(d.A.Label,{className:"required-field",column:!0,sm:"3",children:a("setInfo.email")}),(0,n.jsx)(u.A,{sm:"9",children:(0,n.jsx)(p.ks,{name:"email",id:"email",placeholder:"Enter your email",type:"text",required:!0,disabled:!0,value:F.email,onChange:D})})]})})}),(0,n.jsx)(m.A,{children:(0,n.jsx)(u.A,{children:(0,n.jsxs)(d.A.Group,{as:m.A,controlId:"mobile_number",children:[(0,n.jsx)(d.A.Label,{column:!0,sm:"3",children:a("setInfo.phno")}),(0,n.jsx)(u.A,{children:(0,n.jsxs)(p.s3,{name:"dial_code",id:"dialCode",value:F.dial_code,onChange:D,style:{backgroundColor:"inherit",borderRadius:"5px",color:"#495057"},children:[(0,n.jsx)("option",{value:"",children:"Dial Code"}),G.map((e,a)=>(0,n.jsx)("option",{value:e.dial_code,children:`(${e.dial_code}) ${e.title}`},a))]})}),(0,n.jsx)(u.A,{children:(0,n.jsx)(p.ks,{name:"mobile_number",id:"mobile_number",type:"text",placeholder:"Enter the phone number",value:F.mobile_number,onChange:D})})]})})}),(0,n.jsx)(m.A,{children:(0,n.jsx)(u.A,{children:(0,n.jsxs)(d.A.Group,{as:m.A,controlId:"Password",children:[(0,n.jsx)(d.A.Label,{className:"required-field",column:!0,sm:"3",children:a("setInfo.password")}),(0,n.jsx)(u.A,{sm:"9",children:(0,n.jsx)(p.ks,{name:"password",id:"password",type:"password",pattern:"^(?=.*?[A-Z])(?=.*?[a-z])(?=.*?[0-9])(?=.*?[#?!@$%^&*-]).{8,}$",errorMessage:{required:"Please enter the password",pattern:"Password should contain at least 8 characters, with at least one digit, one letter in upper case & one special character"},value:F.password,onChange:D,required:!0})})]})})}),(0,n.jsx)(m.A,{children:(0,n.jsx)(u.A,{children:(0,n.jsxs)(d.A.Group,{as:m.A,controlId:"Password",children:[(0,n.jsx)(d.A.Label,{className:"required-field",column:!0,sm:"3",children:a("setInfo.confirmpassword")}),(0,n.jsx)(u.A,{sm:"9",children:(0,n.jsx)(p.ks,{name:"confirm_password",id:"confirm_password",type:"password",validator:e=>e===F.password,errorMessage:{required:"Confirm password is required",validator:"Password does not match"},required:!0,value:F.confirm_password,onChange:D})})]})})}),(0,n.jsx)("div",{className:"d-flex justify-content-end",children:(0,n.jsxs)(c.A,{className:"w-20",variant:"success",type:"submit",children:[a("setInfo.accept"),"\xa0\xa0",(0,n.jsx)(h.FontAwesomeIcon,{icon:x.faArrowCircleRight,color:"white"})]})})]})]})})}),(0,n.jsx)(g.default,{userId:j,endpoint:"/deleteUser",isopen:k,manageDialog:e=>M(e)})]})});r()}catch(e){r(e)}})},82601:(e,a,s)=>{s.a(e,async(e,r)=>{try{s.r(a),s.d(a,{default:()=>x});var n=s(8732);s(82015);var l=s(12403),t=s(91353),i=s(82053),o=s(54131),d=s(44233),c=s.n(d),m=s(63487),u=s(88751),h=e([o,m]);[o,m]=h.then?(await h)():h;let x=e=>{let{isopen:a,manageDialog:s,userId:r,endpoint:d}=e,{t:h}=(0,u.useTranslation)("common"),x=()=>{s(!1)},p=async()=>{let e;("/users"===d?await m.A.remove(`${d}/${r}`):await m.A.post(`${d}`,{code:r}))&&(s(!1),c().push("/home"))};return(0,n.jsxs)(l.A,{show:a,onHide:x,children:[(0,n.jsx)("div",{className:"text-center p-2",children:(0,n.jsx)(i.FontAwesomeIcon,{icon:o.faExclamationTriangle,size:"5x",color:"indianRed",style:{background:"#d6deec",padding:"19px",borderRadius:"50%",width:"100px",height:"100px"}})}),(0,n.jsx)(l.A.Body,{children:(0,n.jsx)("b",{children:h("AreyousureyouwishtoleavetheKnowledgePlatform")})}),(0,n.jsxs)(l.A.Footer,{children:[(0,n.jsx)(t.A,{variant:"secondary",onClick:x,children:h("No")}),(0,n.jsx)(t.A,{variant:"danger",onClick:p,children:h("YesDeleteMe")})]})]})};r()}catch(e){r(e)}})}};