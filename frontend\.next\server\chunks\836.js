"use strict";exports.id=836,exports.ids=[836],exports.modules={10836:(e,t,i)=>{i.a(e,async(e,a)=>{try{i.r(t),i.d(t,{default:()=>P});var s=i(8732),n=i(82015),l=i(91353),o=i(7082),r=i(83551),c=i(49481),d=i(59549),u=i(27825),m=i.n(u),p=i(22313),f=i(11e3),h=i(42893),_=i(19918),g=i.n(_),y=i(70275),x=i(56084),A=i(63487),j=i(88751),v=i(55476),b=i(28966),w=e([h,y,A,v]);[h,y,A,v]=w.then?(await w)():w;let P=({institution:e,routes:t})=>{let[i,a]=(0,n.useState)([]),[u,_]=(0,n.useState)([]),[w,P]=(0,n.useState)([]),[C,S]=(0,n.useState)(!1),[I,N]=(0,n.useState)(e),[R,E]=(0,n.useState)([]),[$,F]=(0,n.useState)([]),[k,T]=(0,n.useState)(!1),[D,q]=(0,n.useState)([]),{t:O}=(0,j.useTranslation)("common"),[U,H]=(0,n.useState)({username:"",email:"",_id:null}),B={query:{},sort:{title:"asc"},limit:"~",select:"-firstname -lastname -password -role -country -region -status -is_focal_point -image   -enabled -created_at -updated_at -dataConsentPolicy -restrictedUsePolicy -acceptCookiesPolicy -withdrawConsentPolicy -medicalConsentPolicy -fullDataProtectionConsentPolicy -emailActivateToken"},z={query:{},sort:{username:"asc"},limit:"~",select:"-firstname -lastname -password -role -country -region  -is_focal_point -image  -institution -enabled -created_at -updated_at -dataConsentPolicy -restrictedUsePolicy -acceptCookiesPolicy -withdrawConsentPolicy -medicalConsentPolicy -fullDataProtectionConsentPolicy -emailActivateToken"};(0,n.useEffect)(()=>{L(z)},[]);let G=e=>e=(e=e.data?[...e.data]:[]).filter(e=>{let i=e.institutionInvites.filter(e=>e.institutionId===t[1]&&"Rejected"!==e.status);if(0===i.length)return e}),L=async e=>{let t=await A.A.get("/users",e);if(t.data=G(t),t&&Array.isArray(t.data)){let e=t.data.map((e,t)=>({label:e.username,value:e._id,email:e.email}));E(e)}let i=await A.A.get("/users",e);if(i&&Array.isArray(i.data)){let e=i.data.map((e,t)=>({label:e.username,value:e._id,email:e.email}));q(e)}};(0,n.useEffect)(()=>{if(I.focal_points&&I.focal_points.length>0){let e=m().map(I.focal_points,"_id");Y(e),L(z)}I.primary_focal_point&&K()},[I]);let Y=async e=>{T(!0);let i=await A.A.get("/users",{...B,query:{_id:e}});if(i.data&&i.data.length>0){let e=[];i?.data.forEach(i=>{i?.institutionInvites.forEach(a=>{a&&a.institutionId===t[1]&&"Approved"===a.status&&e.push({...i,...{institutionId:a.institutionId,institutionName:a.institutionName,institutionStatus:a.status}})})}),a(e),T(!1)}},M=[{name:O("Name"),selector:"focal_points_name",cell:e=>(0,s.jsx)("div",{children:e.username})},{name:O("Email"),selector:"focal_points_email",cell:e=>(0,s.jsx)("div",{children:e.email})},{name:O("Organisation"),selector:"focal_points_institution",cell:e=>(0,s.jsx)("div",{children:e.institution&&e.institution.title})},{name:O("Telephone"),selector:"mobile_number",cell:e=>(0,s.jsx)("div",{children:`${e.dial_code?e.dial_code:""} ${e.mobile_number?e.mobile_number:""}`})}],W=[{name:O("Name"),selector:"username",cell:e=>(0,s.jsx)("div",{children:e.username}),sortable:!0},{name:O("Email"),selector:"email",cell:e=>(0,s.jsx)("div",{children:e.email}),sortable:!0},{name:O("Organisation"),selector:"focal_points_institution",cell:e=>(0,s.jsx)("div",{children:e.institutionName}),sortable:!0},{name:O("Telephone"),selector:"mobile_number",cell:e=>(0,s.jsx)("div",{children:`${e.dial_code?e.dial_code:""} ${e.mobile_number?e.mobile_number:""}`})}],J=(0,n.useCallback)(e=>{_(e.selectedRows),S(!1)},[]),K=async()=>{T(!0);let e=I.primary_focal_point,t=await A.A.get("/users",{...B,query:{_id:e}});t.data&&t.data.length>0&&(P(t.data),T(!1))},Q=async()=>{if(u.length<=0||u.length>1)h.default.error(O("toast.YoucannotaddmultipleFocalPointasPrimary"));else{let e=u[0]._id,a=await A.A.patch(`/institution/${t[1]}`,{primary_focal_point:e,title:I.title});if(a&&a._id){let e=m().filter(i,["_id",a.primary_focal_point]);N({...I,...{primary_focal_point:a.primary_focal_point}}),P(e),h.default.success(O("toast.Primaryfocalpointschangedsuccessfully")),S(!0)}}},V=async()=>{u.length>0?(0,p.confirmAlert)({title:O("toast.deleteFocalPtFromOrg"),message:O("toast.deleteFocalPtFromOrgConfirm"),buttons:[{label:O("yes"),onClick:()=>Z(u)},{label:O("No"),onClick:()=>!1}]}):h.default.error(O("toast.Pleaseselectanyfocalpointstodelete"))},X=async e=>{if(e?.length&&e.find(e=>e._id===I.primary_focal_point)){let e=await A.A.patch(`/institution/${t[1]}`,{primary_focal_point:"",title:I.title});e&&e._id&&(P([]),S(!0))}},Z=async e=>{X(e);let s=[],n=m().map(e,"_id");if(0>m().indexOf(n,I.primary_focal_point)){let n=m().difference(i,e);s=n,n=m().map(n,e=>({_id:e._id}));let l={title:I.title,focal_points:e};e.forEach(async e=>{e.institutionInvites=e.institutionInvites.filter(e=>e.institutionId!=t[1]),await A.A.patch(`/users/${e._id}`,e)});let o=await A.A.patch(`/institution/${t[1]}/updateFocalPoints`,l);o&&o._id&&(h.default.success(O("toast.Focalpointsremovedfromorganisationsuccessfuly")),a(s),S(!0),L(z))}else h.default.error(O("toast.Youcannotdeleteaprimaryfocalpoint"))},ee=async()=>{if(""==U.email)h.default.error(O("Emailidisempty"));else{let e=U.email.match(/^([^@]*)@/)[1],a=D.find(e=>e.email==U.email);if(void 0!==a)return void h.default.error(O("Givenexistinguserinnewuserfield"));Object.assign(U,{username:e}),[].push(U);let s=i.map((e,t)=>({_id:e._id})),n={title:I.title,focal_points:[U,...s]},l=await A.A.patch(`/institution/${t[1]}`,n);if(l&&l._id){h.default.success(O("toast.Invitationsentsuccessfully"));let e=await y.A.getApiData(`/institution/${t[1]}`,!1,{});e&&e._id&&N(e),v.A.inviteNewUserWithEmail(U.email,U.username,l._id,l.title),L(z)}else h.default.error(l);H({email:""})}},et=async()=>{let e=$.map((e,t)=>({_id:e.value}));if(0===e.length)h.default.error(O("ExistingUsersisempty"));else{let a={title:I.title,focal_points:[...i,...e]},s=await A.A.patch(`/institution/${t[1]}`,a);if(s&&s._id){$.forEach(async e=>{let t=e.value,i=await A.A.get(`/users/${t}`),a=[];i?.institutionInvites?.length?(a=[...i.institutionInvites]).filter(e=>e.institutionId===s._id).length<=0?a.push({institutionName:s.title,institutionId:s._id,status:"Request Pending"}):a=a.map(e=>(e.institutionId===s._id&&"Rejected"===e.status&&(e.status="Request Pending"),e)):a=[{institutionName:s.title,institutionId:s._id,status:"Request Pending"}],i.institutionInvites=a,await A.A.patch(`/users/${t}`,i),L(z)}),h.default.success(O("toast.Invitationsentsuccessfully"));let e=await y.A.getApiData(`/institution/${t[1]}`,!1,{});e&&e._id&&N(e)}F([])}},ei=()=>(0,s.jsx)(s.Fragment,{children:I&&I._id?(0,s.jsx)(g(),{href:"/institution/[...routes]",as:`/institution/edit/${t[1]}`,children:(0,s.jsxs)(l.A,{variant:"secondary",size:"sm",children:[(0,s.jsx)("i",{className:"fas fa-pen"}),"\xa0",O("Edit")]})}):null}),ea=(0,b.canEditInstitution)(()=>(0,s.jsx)(ei,{}));return(0,s.jsxs)(o.A,{children:[(0,s.jsx)(r.A,{className:"mb-4",children:(0,s.jsx)(c.A,{children:(0,s.jsxs)("h4",{className:"institutionTitle",children:[(0,s.jsx)(g(),{href:"/institution/[...routes]",as:`/institution/show/${t[1]}`,children:I.title}),(0,s.jsx)(ea,{})]})})}),(0,s.jsx)(r.A,{children:(0,s.jsx)(c.A,{className:"medium",children:(0,s.jsx)("b",{children:O("PrimaryFocalPoint")})})}),(0,s.jsx)(r.A,{className:"primary-focal-point-table mb-3",children:(0,s.jsx)(x.A,{columns:M,data:w,loading:k,subheader:!1,pagination:!1,pagServer:!0,clearSelectedRows:C})}),(0,s.jsx)(r.A,{children:(0,s.jsx)(c.A,{className:"medium",children:(0,s.jsx)("b",{children:O("FocalPoints")})})}),(0,s.jsx)(r.A,{className:"mb-3",children:(0,s.jsx)(x.A,{columns:W,data:i,loading:k,pagServer:!0,selectableRows:!0,subheader:!1,onSelectedRowsChange:J,clearSelectedRows:C,persistTableHead:!0,sortFunction:(e,t,i)=>m().orderBy(e,e=>e[t]?e[t].toLowerCase():e[t],i)})}),(0,s.jsx)(r.A,{children:(0,s.jsxs)(c.A,{children:[(0,s.jsx)(l.A,{className:"me-1",onClick:Q,variant:"primary",size:"sm",children:O("Promote")}),(0,s.jsx)(l.A,{onClick:V,variant:"primary",size:"sm",children:O("DeleteFocal")})]})}),(0,s.jsx)("br",{}),(0,s.jsxs)(r.A,{children:[(0,s.jsxs)(c.A,{md:6,children:[(0,s.jsxs)(d.A.Group,{controlId:"formBasicEmail",children:[(0,s.jsx)(d.A.Label,{children:O("external")}),(0,s.jsx)(d.A.Control,{name:"email",type:"email",placeholder:O("Enteremail"),value:U.email,onChange:e=>{if(e.target){let{name:t,value:i}=e.target;H(e=>({...e,[t]:i}))}}})]}),(0,s.jsx)(l.A,{onClick:ee,variant:"primary",type:"submit",children:O("InviteFocalPoint")})]}),(0,s.jsxs)(c.A,{md:6,children:[(0,s.jsxs)(d.A.Group,{controlId:"formBasicEmail",children:[(0,s.jsx)(d.A.Label,{children:O("existing")}),(0,s.jsx)(f.MultiSelect,{overrideStrings:{selectSomeItems:O("SelectUsers")},options:R,value:$,onChange:e=>{F(e)},className:"focal-users",labelledBy:"Select Users"})]}),(0,s.jsx)(l.A,{onClick:et,variant:"primary",type:"submit",children:O("Adduser")})]})]})]})};a()}catch(e){a(e)}})},56084:(e,t,i)=>{i.d(t,{A:()=>c});var a=i(8732);i(82015);var s=i(38609),n=i.n(s),l=i(88751),o=i(30370);function r(e){let{t}=(0,l.useTranslation)("common"),i={rowsPerPageText:t("Rowsperpage")},{columns:s,data:r,totalRows:c,resetPaginationToggle:d,subheader:u,subHeaderComponent:m,handlePerRowsChange:p,handlePageChange:f,rowsPerPage:h,defaultRowsPerPage:_,selectableRows:g,loading:y,pagServer:x,onSelectedRowsChange:A,clearSelectedRows:j,sortServer:v,onSort:b,persistTableHead:w,sortFunction:P,...C}=e,S={paginationComponentOptions:i,noDataComponent:t("NoData"),noHeader:!0,columns:s,data:r||[],dense:!0,paginationResetDefaultPage:d,subHeader:u,progressPending:y,subHeaderComponent:m,pagination:!0,paginationServer:x,paginationPerPage:_||10,paginationRowsPerPageOptions:h||[10,15,20,25,30],paginationTotalRows:c,onChangeRowsPerPage:p,onChangePage:f,selectableRows:g,onSelectedRowsChange:A,clearSelectedRows:j,progressComponent:(0,a.jsx)(o.A,{}),sortIcon:(0,a.jsx)("i",{className:"sort-icon fas fa-exchange-alt"}),sortServer:v,onSort:b,sortFunction:P,persistTableHead:w,className:"rki-table"};return(0,a.jsx)(n(),{...S})}r.defaultProps={subHeader:!1,pagination:!0,totalRows:null,pagServer:!0,onSelectedRowsChange:null,clearSelectedRows:!1,sortServer:!1,persistTableHead:!1};let c=r},70275:(e,t,i)=>{i.a(e,async(e,a)=>{try{i.d(t,{A:()=>o});var s=i(63487),n=e([s]);s=(n.then?(await n)():n)[0];class l{constructor(){this.getApiData=async(e,t,i={})=>{try{let a=await s.A.get(e,i);return t?a.data:a}catch(e){return e.response?e.response:{}}}}}let o=new l;a()}catch(e){a(e)}})}};