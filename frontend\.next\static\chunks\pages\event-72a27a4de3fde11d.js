(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[5416],{335:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>u});var a=r(37876),s=r(14232),l=r(49589),n=r(56970),o=r(37784),i=r(12697),c=r(53718),d=r(31753);let u=e=>{let{filterText:t,onFilter:r,onClear:u,onFilterHazardChange:y,filterHazard:p}=e,[m,h]=(0,s.useState)([]),{t:f}=(0,d.Bd)("common"),g=async e=>{let t=await c.A.get("/hazardtype",e);t&&Array.isArray(t.data)&&h(t.data)};return(0,s.useEffect)(()=>{g({query:{},sort:{title:"asc"}})},[]),(0,a.jsx)(l.A,{fluid:!0,className:"p-0",children:(0,a.jsxs)(n.A,{children:[(0,a.jsx)(o.A,{xs:6,className:"p-0",children:(0,a.jsx)(i.A,{type:"text",className:"searchInput",placeholder:f("Events.table.Search"),"aria-label":"Search",value:t,onChange:r})}),(0,a.jsx)(o.A,{xs:6,children:(0,a.jsxs)(i.A,{as:"select","aria-label":"HazardType","aria-placeholder":"Hazard Type",onChange:y,value:p,children:[(0,a.jsx)("option",{value:"",children:f("Events.forms.SelectHazardType")}),m.map((e,t)=>(0,a.jsx)("option",{value:e._id,children:e.title},t))]})})]})})}},2481:(e,t,r)=>{"use strict";r.r(t),r.d(t,{canAddEvent:()=>n,canAddEventForm:()=>o,canEditEvent:()=>i,canEditEventForm:()=>c,canViewDiscussionUpdate:()=>d,default:()=>u});var a=r(37876);r(14232);var s=r(8178),l=r(59626);let n=(0,s.A)({authenticatedSelector:e=>!!e.permissions&&!!e.permissions.event&&!!e.permissions.event["create:any"],wrapperDisplayName:"CanAddEvent"}),o=(0,s.A)({authenticatedSelector:e=>!!e.permissions&&!!e.permissions.event&&!!e.permissions.event["create:any"],wrapperDisplayName:"CanAddEventForm",FailureComponent:()=>(0,a.jsx)(l.default,{})}),i=(0,s.A)({authenticatedSelector:(e,t)=>{if(e.permissions&&e.permissions.event){if(e.permissions.event["update:any"])return!0;else if(e.permissions.event["update:own"]&&t.event&&t.event.user&&t.event.user._id===e.user._id)return!0}return!1},wrapperDisplayName:"CanEditEvent"}),c=(0,s.A)({authenticatedSelector:(e,t)=>{if(e.permissions&&e.permissions.event){if(e.permissions.event["update:any"])return!0;else if(e.permissions.event["update:own"]&&t.event&&t.event.user&&t.event.user._id===e.user._id)return!0}return!1},wrapperDisplayName:"CanEditEventForm",FailureComponent:()=>(0,a.jsx)(l.default,{})}),d=(0,s.A)({authenticatedSelector:e=>!!e.permissions&&!!e.permissions.update&&!!e.permissions.update["read:any"],wrapperDisplayName:"CanViewDiscussionUpdate"}),u=n},6052:(e,t,r)=>{"use strict";r.r(t),r.d(t,{__N_SSG:()=>v,default:()=>x});var a=r(37876),s=r(14232),l=r(48230),n=r.n(l),o=r(60282),i=r(49589),c=r(56970),d=r(37784),u=r(69600),y=r(75797),p=r(54787),m=r(31753),h=r(47272),f=r(2481),g=r(69438),v=!0;let x=()=>{let{t:e}=(0,m.Bd)("common"),[t,r]=(0,s.useState)(!1),[l,v]=(0,s.useState)(null),[x,T]=(0,s.useState)([]),_=()=>r(!t),j=()=>(0,a.jsx)(n(),{href:"/event/[...routes]",as:"/event/create",children:(0,a.jsx)(o.A,{variant:"secondary",size:"sm",children:e("addEvent")})}),b=(0,f.canAddEvent)(()=>(0,a.jsx)(j,{})),A=e=>{v(e)};return(0,a.jsxs)(i.A,{fluid:!0,className:"p-0",children:[(0,a.jsx)(c.A,{children:(0,a.jsx)(d.A,{xs:12,children:(0,a.jsx)(u.A,{title:e("menu.events")})})}),(0,a.jsx)(c.A,{children:(0,a.jsx)(d.A,{xs:12,children:(0,a.jsx)(p.default,{events:x})})}),(0,a.jsx)(c.A,{children:(0,a.jsx)(d.A,{xs:12,children:(0,a.jsx)(g.A,{filtreg:e=>A(e),selectedRegions:[],regionHandler:A})})}),(0,a.jsx)(c.A,{children:(0,a.jsx)(d.A,{xs:12,className:"ps-4",children:(0,a.jsxs)(c.A,{children:[(0,a.jsx)(d.A,{children:(0,a.jsx)(b,{})}),(0,a.jsx)(d.A,{children:(0,a.jsx)("p",{className:"m-0",children:(0,a.jsxs)("small",{children:[e("Events.table.Doyouknowofaneventthatneedstobeadded")," ",(0,a.jsx)(o.A,{variant:"link",size:"sm",className:"p-0 outlineButton",onClick:_,children:e("Events.table.Clickhere")}),(0,a.jsx)("span",{children:"\xa0"}),e("Events.table.toinformthePublicHealthIntelligenceTeam")]})})})]})})}),(0,a.jsx)(c.A,{className:"mt-1",children:(0,a.jsx)(d.A,{xs:12,children:(0,a.jsx)(y.default,{selectedRegions:l,setEvents:T})})}),(0,a.jsx)(h.A,{show:t,onHide:_})]})}},15641:(e,t,r)=>{"use strict";r.d(t,{A:()=>l});var a=r(37876);r(14232);var s=r(62945);let l=e=>{let{name:t="Marker",id:r="",countryId:l="",type:n,icon:o,position:i,onClick:c,title:d,draggable:u=!1}=e;return i&&"number"==typeof i.lat&&"number"==typeof i.lng?(0,a.jsx)(s.pH,{position:i,icon:o,title:d||t,draggable:u,onClick:e=>{c&&c({name:t,id:r,countryId:l,type:n,position:i},{position:i,getPosition:()=>i},e)}}):null}},50749:(e,t,r)=>{"use strict";r.d(t,{A:()=>i});var a=r(37876);r(14232);var s=r(89773),l=r(31753),n=r(5507);function o(e){let{t}=(0,l.Bd)("common"),r={rowsPerPageText:t("Rowsperpage")},{columns:o,data:i,totalRows:c,resetPaginationToggle:d,subheader:u,subHeaderComponent:y,handlePerRowsChange:p,handlePageChange:m,rowsPerPage:h,defaultRowsPerPage:f,selectableRows:g,loading:v,pagServer:x,onSelectedRowsChange:T,clearSelectedRows:_,sortServer:j,onSort:b,persistTableHead:A,sortFunction:w,...C}=e,k={paginationComponentOptions:r,noDataComponent:t("NoData"),noHeader:!0,columns:o,data:i||[],dense:!0,paginationResetDefaultPage:d,subHeader:u,progressPending:v,subHeaderComponent:y,pagination:!0,paginationServer:x,paginationPerPage:f||10,paginationRowsPerPageOptions:h||[10,15,20,25,30],paginationTotalRows:c,onChangeRowsPerPage:p,onChangePage:m,selectableRows:g,onSelectedRowsChange:T,clearSelectedRows:_,progressComponent:(0,a.jsx)(n.A,{}),sortIcon:(0,a.jsx)("i",{className:"sort-icon fas fa-exchange-alt"}),sortServer:j,onSort:b,sortFunction:w,persistTableHead:A,className:"rki-table"};return(0,a.jsx)(s.Ay,{...k})}o.defaultProps={subHeader:!1,pagination:!0,totalRows:null,pagServer:!0,onSelectedRowsChange:null,clearSelectedRows:!1,sortServer:!1,persistTableHead:!1};let i=o},54787:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>y});var a=r(37876),s=r(82851),l=r.n(s),n=r(48230),o=r.n(n),i=r(14232),c=r(31753),d=r(66619),u=r(15641);let y=e=>{let{i18n:t}=(0,c.Bd)("common"),r=t.language,{events:s}=e,[n,y]=(0,i.useState)({}),[p,m]=(0,i.useState)([]),[h,f]=(0,i.useState)({}),[g,v]=(0,i.useState)({}),x=()=>{f(null),v(null)},T=(e,t,r)=>{x(),f(t),v({name:e.name,id:e.id,countryId:e.countryId})},_=()=>{let e=[];l().forEach(s,t=>{e.push({title:t.title,id:t._id,lat:t.country&&t.country.coordinates&&t.country.coordinates[0].latitude,lng:t.country&&t.country.coordinates&&t.country.coordinates[0].longitude,countryId:t.country&&t.country._id})}),m([...e])};return(0,i.useEffect)(()=>{_(),y(l().groupBy(s,"country._id"))},[s]),(0,a.jsx)(d.A,{onClose:x,language:r,activeMarker:h,markerInfo:(0,a.jsx)(e=>{let{info:t}=e;return t&&t.countryId&&n[t.countryId]?(0,a.jsx)("ul",{children:n[t.countryId].map((e,t)=>(0,a.jsx)("li",{children:(0,a.jsx)(o(),{href:"/event/[...routes]",as:"/".concat(r,"/event/show/").concat(e._id),children:e.title})},t))}):null},{info:g}),children:p.length>=1?p.map((e,t)=>{if(e.lat)return(0,a.jsx)(u.A,{name:e.title,id:e.id,countryId:e.countryId,icon:{url:"/images/map-marker-white.svg"},onClick:T,position:e},t)}):null})}},66619:(e,t,r)=>{"use strict";r.d(t,{A:()=>p});var a=r(37876);r(14232);var s=r(62945);let l=e=>{let{position:t,onCloseClick:r,children:l}=e;return(0,a.jsx)(s.Fu,{position:t,onCloseClick:r,children:(0,a.jsx)("div",{children:l})})},n="labels.text.fill",o="labels.text.stroke",i="road.highway",c="geometry.stroke",d=[{elementType:"geometry",stylers:[{color:"#1d2c4d"}]},{elementType:n,stylers:[{color:"#8ec3b9"}]},{elementType:o,stylers:[{color:"#1a3646"}]},{featureType:"administrative",elementType:"geometry",stylers:[{visibility:"off"}]},{featureType:"administrative.country",elementType:c,stylers:[{color:"#4b6878"}]},{featureType:"administrative.land_parcel",elementType:"labels",stylers:[{visibility:"off"}]},{featureType:"administrative.land_parcel",elementType:n,stylers:[{color:"#64779e"}]},{featureType:"administrative.province",elementType:c,stylers:[{color:"#4b6878"}]},{featureType:"landscape.man_made",elementType:c,stylers:[{color:"#334e87"}]},{featureType:"landscape.natural",elementType:"geometry",stylers:[{color:"#023e58"}]},{featureType:"poi",stylers:[{visibility:"off"}]},{featureType:"poi",elementType:"geometry",stylers:[{color:"#283d6a"}]},{featureType:"poi",elementType:"labels.text",stylers:[{visibility:"off"}]},{featureType:"poi",elementType:n,stylers:[{color:"#6f9ba5"}]},{featureType:"poi",elementType:o,stylers:[{color:"#1d2c4d"}]},{featureType:"poi.park",elementType:"geometry.fill",stylers:[{color:"#023e58"}]},{featureType:"poi.park",elementType:n,stylers:[{color:"#3C7680"}]},{featureType:"road",stylers:[{visibility:"off"}]},{featureType:"road",elementType:"geometry",stylers:[{color:"#304a7d"}]},{featureType:"road",elementType:"labels.icon",stylers:[{visibility:"off"}]},{featureType:"road",elementType:n,stylers:[{color:"#98a5be"}]},{featureType:"road",elementType:o,stylers:[{color:"#1d2c4d"}]},{featureType:i,elementType:"geometry",stylers:[{color:"#2c6675"}]},{featureType:i,elementType:c,stylers:[{color:"#255763"}]},{featureType:i,elementType:n,stylers:[{color:"#b0d5ce"}]},{featureType:i,elementType:o,stylers:[{color:"#023e58"}]},{featureType:"road.local",elementType:"labels",stylers:[{visibility:"off"}]},{featureType:"transit",stylers:[{visibility:"off"}]},{featureType:"transit",elementType:n,stylers:[{color:"#98a5be"}]},{featureType:"transit",elementType:o,stylers:[{color:"#1d2c4d"}]},{featureType:"transit.line",elementType:"geometry.fill",stylers:[{color:"#283d6a"}]},{featureType:"transit.station",elementType:"geometry",stylers:[{color:"#3a4762"}]},{featureType:"water",elementType:"geometry",stylers:[{color:"#0e1626"}]},{featureType:"water",elementType:n,stylers:[{color:"#4e6d70"}]}];var u=r(89099),y=r(55316);let p=e=>{let{markerInfo:t,activeMarker:r,initialCenter:n,children:o,height:i=300,width:c="114%",language:p,zoom:m=1,minZoom:h=1,onClose:f}=e,{locale:g}=(0,u.useRouter)(),{isLoaded:v,loadError:x}=(0,y._)();return x?(0,a.jsx)("div",{children:"Error loading maps"}):v?(0,a.jsx)("div",{className:"map-container",children:(0,a.jsx)("div",{className:"mapprint",style:{width:c,height:i,position:"relative"},children:(0,a.jsxs)(s.u6,{mapContainerStyle:{width:c,height:"number"==typeof i?"".concat(i,"px"):i},center:n||{lat:52.520017,lng:13.404195},zoom:m,onLoad:e=>{e.setOptions({styles:d})},options:{minZoom:h,draggable:!0,keyboardShortcuts:!1,streetViewControl:!1,panControl:!1,clickableIcons:!1,mapTypeControl:!1,fullscreenControl:!0},children:[o,t&&r&&r.getPosition&&(0,a.jsx)(l,{position:r.getPosition(),onCloseClick:()=>{console.log("close click"),null==f||f()},children:t})]})})}):(0,a.jsx)("div",{children:"Loading Maps..."})}},69438:(e,t,r)=>{"use strict";r.d(t,{A:()=>y});var a=r(37876),s=r(14232),l=r(82851),n=r.n(l),o=r(29504),i=r(60282),c=r(53718),d=r(31753);function u(e){let{filtreg:t}=e,[r,l]=(0,s.useState)(!0),[u,y]=(0,s.useState)([]),[p,m]=(0,s.useState)([]),{t:h}=(0,d.Bd)("common"),f={query:{},limit:"~",sort:{title:"asc"}},g=async e=>{let r=await c.A.get("/worldregion",e);if(r&&Array.isArray(r.data)){let e=[],a=[];n().each(r.data,(t,r)=>{let s={...t,isChecked:!0};e.push(s),a.push(t._id)}),t(a),m(a),y(e)}};(0,s.useEffect)(()=>{g(f)},[]);let v=e=>{let r=[...u],a=[...p];r.forEach((t,s)=>{t.code===e.target.id&&(r[s].isChecked=e.target.checked,e.target.checked?a.push(t._id):a=a.filter(e=>e!==t._id))}),m(a),t(a),l(!1),y(r)};return(0,a.jsxs)("div",{className:"regions-multi-checkboxes",children:[(0,a.jsx)(o.A.Check,{type:"checkbox",id:"all",label:h("AllRegions"),checked:r,onChange:e=>{let r=u.map(t=>({...t,isChecked:e.target.checked})),a=[];e.target.checked&&(a=r.map(e=>e._id)),t(a),m(a),l(e.target.checked),y(r)}}),u.map((e,t)=>(0,a.jsx)(o.A.Check,{type:"checkbox",id:e.code,label:e.title,value:e.code,onChange:v,checked:u[t].isChecked},t)),(0,a.jsx)(i.A,{onClick:()=>{let e=u.map(e=>({...e,isChecked:!1}));m([]),l(!1),y(e),t([])},className:"btn-plain ps-2",children:h("ClearAll")})]})}u.defaultProps={filtreg:()=>{}};let y=u},69600:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});var a=r(37876);function s(e){return(0,a.jsx)("h2",{className:"page-heading",children:e.title})}},75797:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>g});var a=r(37876),s=r(48230),l=r.n(s),n=r(10841),o=r.n(n),i=r(82851),c=r.n(i),d=r(14232),u=r(89099),y=r(50749),p=r(53718),m=r(335),h=r(31753);let f=e=>{let{i18n:t}=(0,h.Bd)("common"),r="fr"===t.language?"en":t.language,{hazards:s}=e;return(0,a.jsx)("ul",{children:s.map((e,t)=>e&&e._id&&e.title&&e.title[r]?(0,a.jsx)("li",{children:(0,a.jsx)(l(),{href:"/hazard/[...routes]",as:"/hazard/show/".concat(e._id),children:e.title[r].toString()})},t):"")})},g=function(e){let t=(0,u.useRouter)(),{t:r}=(0,h.Bd)("common"),{setEvents:s,selectedRegions:n}=e,[i,g]=d.useState(""),[v,x]=d.useState(""),[T,_]=d.useState(!1),[j,b]=(0,d.useState)([]),[A,w]=(0,d.useState)(!1),[C,k]=(0,d.useState)(0),[S,E]=(0,d.useState)(10),[N,q]=(0,d.useState)(1),[z,P]=(0,d.useState)(null),H={sort:{created_at:"desc"},lean:!0,populate:[{path:"country",select:"coordinates title"},{path:"hazard_type",select:"title"},{path:"hazard",select:"title"}],limit:S,page:1,query:{},select:"-description -operation -world_region -country_regions -syndrome -status -laboratory_confirmed -officially_validated -rki_monitored -risk_assessment -date -more_info -images -user -created_at -updated_at"},[R,I]=(0,d.useState)(H),D=[{name:r("Events.table.EventId"),selector:"title",sortable:!0,width:"20%",cell:e=>(0,a.jsx)(l(),{href:"/event/[...routes]",as:"/event/show/".concat(e._id),children:e.title})},{name:r("Events.table.Country"),selector:"country",sortable:!0,cell:e=>e.country&&e.country.title?(0,a.jsx)(l(),{href:"/country/[...routes]",as:"/country/show/".concat(e.country._id),children:e.country.title}):""},{name:r("Events.table.HazardType"),selector:"hazard_type",sortable:!0,cell:e=>e.hazard_type&&e.hazard_type.title?e.hazard_type.title:""},{name:r("Events.table.Hazard"),selector:"hazard",cell:e=>(0,a.jsx)(f,{hazards:e.hazard})},{name:r("Events.table.InfoReceivedon"),selector:"created_at",sortable:!0,cell:e=>o()(e.start_date).format("M/D/Y")},{name:r("Events.table.Lastupdated"),selector:"updated_at",sortable:!0,cell:e=>o()(e.updated_at).format("M/D/Y")}],B=async e=>{w(!0),t.query&&t.query.country&&(e.query.country=t.query.country),null===n?delete e.query.world_region:0===n.length?e.query.world_region=["__NO_MATCH__"]:e.query.world_region=n;let r=await p.A.get("/event",e);r&&Array.isArray(r.data)&&(b(r.data),s(r.data),k(r.totalCount)),w(!1)},F=async(e,r)=>{H.limit=e,H.page=r,w(!0),t.query&&t.query.country&&(H.query.country=t.query.country),null===n?delete H.query.world_region:0===n.length?H.query.world_region=["__NO_MATCH__"]:H.query.world_region=n,v&&(H.query={...H.query,hazard_type:v}),z&&(H.sort=z.sort);let a=await p.A.get("/event",H);a&&Array.isArray(a.data)&&(b(a.data),s(a.data),E(e),w(!1)),q(r)};(0,d.useEffect)(()=>{R.page=1,B(R)},[n,t]),(0,d.useEffect)(()=>{B(R)},[R]);let M=async(e,t)=>{w(!0),H.sort={[e.selector]:t},v&&(H.query={...H.query,hazard_type:v}),""!==i&&(H.query={...H.query,title:i}),await B(H),P(H),w(!1)},O=(e,t)=>{e?(R.query.title=e,R.page=t):delete R.query.title,I({...R})},L=(0,d.useRef)(c().debounce((e,t)=>O(e,t),Number("500")||300)).current,V=d.useMemo(()=>{let e=e=>{x(e),e?R.query.hazard_type=e:delete R.query.hazard_type,I({...R})};return(0,a.jsx)(m.default,{onFilter:e=>{g(e.target.value),L(e.target.value,N)},onFilterHazardChange:t=>e(t.target.value),onClear:()=>{i&&(_(!T),g(""))},filterText:i,filterHazard:v})},[i,T,v,n]);return(0,a.jsx)(y.A,{columns:D,data:j,totalRows:C,loading:A,subheader:!0,persistTableHead:!0,onSort:M,sortServer:!0,pagServer:!0,subHeaderComponent:V,handlePerRowsChange:F,handlePageChange:e=>{H.limit=S,H.page=e,v&&(H.query={...H.query,hazard_type:v}),z&&(H.sort=z.sort),B(H),q(e)}})}},97793:(e,t,r)=>{(window.__NEXT_P=window.__NEXT_P||[]).push(["/event",function(){return r(6052)}])}},e=>{var t=t=>e(e.s=t);e.O(0,[9759,9773,636,6593,8792],()=>t(97793)),_N_E=e.O()}]);
//# sourceMappingURL=event-72a27a4de3fde11d.js.map