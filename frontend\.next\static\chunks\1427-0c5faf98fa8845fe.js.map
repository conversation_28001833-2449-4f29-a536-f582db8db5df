{"version": 3, "file": "static/chunks/1427-0c5faf98fa8845fe.js", "mappings": "8MAwCA,MAxBsBA,IACpB,GAAM,CAAEC,CAAC,CAAE,CAAGC,CAAAA,EAAAA,EAAAA,EAAAA,CAAcA,CAAC,QAuBhBC,EAtBPC,EAAQ,IACZJ,EAAMK,EAqBiBF,EAAC,CArBb,CAACG,EACd,EAEMC,EAAgB,IACpBP,EAAMQ,mBAAmB,CAACC,EAC5B,EAEA,MACE,UAACC,EAAAA,CAASA,CAAAA,CAACC,UAAU,oBAAoBC,KAAK,aAC5C,WAACC,EAAAA,CAAGA,CAAAA,WACF,UAACC,EAAAA,CAAGA,CAAAA,CAACH,UAAU,eAAeI,GAAI,YAChC,UAACC,KAAAA,UAAG,UAACC,OAAAA,UAAMhB,EAAE,0BAEf,UAACa,EAAAA,CAAGA,CAAAA,UACF,UAACI,EAAAA,CAAaA,CAAAA,CAACC,MAAOnB,EAAMoB,IAAI,CAAEC,KAAK,cAAcC,QAAStB,EAAMsB,OAAO,CAAEC,SAAU,GAAenB,EAAME,GAAKkB,eAAgB,GAAsBjB,EAAcE,WAK/K,gMCbA,IAAIgB,EAAmB,EAAE,CAEnBC,EAAiB,CACrBC,KAAM,EACNC,QAAS,OACTC,cAAe,SACfC,WAAY,SACZC,eAAgB,SAChBC,MAAO,OACPC,OAAQ,OACRC,YAAa,GACbC,YAAa,UACbC,gBAAiB,UACjBC,MAAO,QACPC,WAAY,2BACZC,QAAS,MACX,EAYMC,EAAuB,CAC3BZ,QAAS,OACTW,QAAS,OACTP,MAAO,OACPS,OAAQ,iBACRZ,cAAe,SACfE,eAAgB,aAChBW,SAAU,OACVC,UAAW,EACb,EAcMC,EAAM,CACVZ,MAAO,OACT,EAEMa,EAAmB,CACvBV,YAAa,SACf,EA4WA,EA1WsB,IACpB,IAmSIW,EAnSE,GAAE7C,CAAC,CAAE,CAAGC,CAAAA,EAAAA,EAAAA,EAAAA,CAAcA,CAAC,GAyWhBgB,OAxWP,CAAC6B,EAAWC,EAAa,CAAGC,CAAAA,CAwWP,CAxWOA,EAAAA,QAAAA,CAAQA,EAAC,GACrC,CAACC,EAAYC,EAAc,CAAGF,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,GACtCG,EACJpD,iBAAMqB,IAAI,CAAoB,UAAWgC,UAAwB,CAC7D,CAACC,EAAOC,EAAS,CAAGN,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAQ,EAAE,EACtC,CAACO,EAAOC,EAAS,CAAGR,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,EAAC,GAC7B,CAACS,EAAaC,EAAe,CAAGV,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAW,EAAE,EAErDW,EAAW5D,GAASA,kBAAMqB,IAAI,CAAqB,SAAW,SAC9DwC,EAAc,MAAOvD,IACZ,MAAMwD,EAAAA,CAAUA,CAACC,MAAM,CAAC,GAAezD,MAAAA,CAAZsD,EAAS,KAAM,OAAHtD,GACtD,EAEM0D,EAAa,IACjBb,EAAcc,GACdjB,EAAa,GACf,EAEMkB,EAAe,CAACC,EAA8DC,KAClF,IAAMC,EAAQ,IAAIX,EAAY,CAC9BW,CAAK,CAACD,EAAM,CAAGD,EAAEG,MAAM,CAACC,KAAK,CAC7BZ,EAAeU,EACjB,EAEMG,EAAe,IAEnB,OAAQC,GADiBR,EAAKS,IAAI,CAACC,KAAK,CAAC,KAAKC,GAAG,IAE/C,IAAK,MACL,IAAK,MACL,IAAK,OACL,IAAK,MACL,IAAK,MACH,MAAO,UAAChC,MAAAA,CAAIiC,IAAKZ,EAAKa,OAAO,CAAEC,MAAOnC,GACxC,KAAK,MACH,MACE,UAACA,MAAAA,CACCiC,IAAI,gCACJlE,UACiB,gBAAfX,EAAMqB,IAAI,CAAqB,aAAe,cAItD,KAAK,OAmBL,QAlBE,MACE,UAACuB,MAAAA,CACCiC,IAAI,iCACJlE,UACiB,gBAAfX,EAAMqB,IAAI,CAAqB,aAAe,cAItD,KAAK,MACL,IAAK,OACH,MACE,UAACuB,MAAAA,CACCiC,IAAI,gCACJlE,UACiB,gBAAfX,EAAMqB,IAAI,CAAqB,aAAe,cAaxD,CACF,EAEM2D,EAAY,IAAMhC,GAAa,GAE/BiC,EAAgB,KACpBjC,GAAa,EACf,EAEMkC,EAAgB,IAEpB,IAAMC,EADNC,GAAelC,CAAAA,GAEGkC,EAAaC,GAAG,CAC5B,CAAEC,SAAUF,EAAaC,GAAG,EAC5B,CAAEpB,KAAMmB,CAAa,EACrBG,EAASC,IAAAA,SAAW,CAAC/D,EAAM0D,GAE3BM,EAAY,IAAI/B,EAAY,CAClC+B,EAAUC,MAAM,CAACH,EAAQ,GACzB5B,EAAe8B,GAEf5B,EAAYpC,CAAI,CAAC8D,EAAO,CAACD,QAAQ,EACjC7D,EAAKiE,MAAM,CAACH,EAAQ,GACpBvF,EAAMuB,QAAQ,CAACE,EAAMzB,EAAMoE,KAAK,CAAGpE,EAAMoE,KAAK,CAAG,GACjD,IAAMuB,EAAW,IAAIrC,EAAM,CAC3BqC,EAASD,MAAM,CAACC,EAASC,OAAO,CAACR,GAAe,GAChD7B,EAASoC,GACT3C,GAAa,EACf,EAEM6C,EAAcvC,EAAMwC,GAAG,CAAC,CAAC7B,EAAW8B,IAEtC,WAACC,MAAAA,WACC,UAACnF,EAAAA,CAAGA,CAAAA,CAACoF,GAAI,YACP,WAACD,MAAAA,CAAIrF,UAAU,gBACb,UAACE,EAAAA,CAAGA,CAAAA,CACFqF,GAAI,EACJnF,GAAI,EACJJ,UACiB,8CAAfX,EAAMqB,IAAI,CACN,gDACA,oDAGLmD,EAAaP,KAEhB,UAACpD,EAAAA,CAAGA,CAAAA,CAACqF,GAAI,EAAGnF,GAAI,EAAGJ,UAAU,6BAC3B,WAACwF,EAAAA,CAAIA,CAAAA,WACH,WAACA,EAAAA,CAAIA,CAACC,KAAK,EAACC,UAAU,qBACpB,UAACF,EAAAA,CAAIA,CAACG,KAAK,EAAC3F,UAAU,gBAAQV,EAAE,cAChC,UAACkG,EAAAA,CAAIA,CAACI,OAAO,EACXC,KAAK,KACLnF,KAAK,OACLoF,QAAQ,IACRlC,MAAON,EAAKyC,aAAa,CAAGzC,EAAKyC,aAAa,CAAGzC,EAAKS,IAAI,MAG9D,WAACyB,EAAAA,CAAIA,CAACC,KAAK,EAACC,UAAU,wBACpB,UAACF,EAAAA,CAAIA,CAACG,KAAK,WACO,gBAAftG,EAAMqB,IAAI,CACPpB,EAAE,uCACFA,EAAE,wBAER,UAACkG,EAAAA,CAAIA,CAACI,OAAO,EACXI,UAA0B,gBAAf3G,EAAMqB,IAAI,CAAqB,SAAMuF,EAChDJ,KAAK,KACLnF,KAAK,OACLwF,YACE7G,kBAAMqB,IAAI,CACNpB,EAAE,kCACFA,EAAE,sCAERsE,MAAOb,CAAW,CAACqC,EAAE,CACrBe,SAAW3C,GAAMD,EAAaC,EAAG4B,aAKzC,UAAClF,EAAAA,CAAGA,CAAAA,CACFqF,GAAI,EACJnF,GAAI,EACJJ,UAAU,gCACVoG,QAAS,IAAM/C,EAAWC,YAE1B,UAAC+C,EAAAA,CAAMA,CAAAA,CAACC,QAAQ,gBAAQhH,EAAE,mBAIhC,WAACiH,EAAAA,CAAKA,CAAAA,CAACC,KAAMpE,EAAWqE,OAAQpC,YAC9B,UAACkC,EAAAA,CAAKA,CAACG,MAAM,EAACC,WAAW,aACvB,UAACJ,EAAAA,CAAKA,CAACK,KAAK,WAAEtH,EAAE,kBAElB,UAACiH,EAAAA,CAAKA,CAACM,IAAI,WAAEvH,EAAE,qCACf,WAACiH,EAAAA,CAAKA,CAACO,MAAM,YACX,UAACT,EAAAA,CAAMA,CAAAA,CAACC,QAAQ,YAAYF,QAAS9B,WAClChF,EAAE,YAEL,UAAC+G,EAAAA,CAAMA,CAAAA,CAACC,QAAQ,UAAUF,QAAS,IAAM7B,EAAcjB,YACpDhE,EAAE,iBAlED8F,IA0Ed2B,CAAAA,EAAAA,EAAAA,SAAAA,CAASA,CAAC,KACRpE,EAAMqE,OAAO,CAAC,GAAUC,IAAIC,eAAe,CAAC5D,EAAKa,OAAO,GACxDrD,EAAO,EACT,EAAG,EAAE,EAELiG,CAAAA,EAAAA,EAAAA,SAAAA,CAASA,CAAC,KACR1H,EAAMwB,cAAc,CAACkC,EACvB,EAAG,CAACA,EAAY,EAEhBgE,CAAAA,EAAAA,EAAAA,SAAAA,CAASA,CAAC,KACR/D,EAAe3D,EAAMsB,OAAO,CAC9B,EAAG,CAACtB,EAAMsB,OAAO,CAAC,EAElBoG,CAAAA,EAAAA,EAAAA,SAAAA,CAASA,CAAC,KACR1H,GAAgC,SAAvBA,EAAM8H,YAAY,EAAerE,GAAS,GAC/CzD,GAASA,EAAMmB,KAAK,EAAE,EAaf,IAZMnB,EAAMmB,KAAK,CAAC2E,GAAG,CAAC,CAACiC,EAAWC,KACzCvG,EAAKwG,IAAI,CAAC,CACR3C,SAAUyC,EAAK1C,GAAG,CAClBjB,MAAOpE,EAAMoE,KAAK,CAAGpE,EAAMoE,KAAK,CAAG,EACnC/C,KAAM0G,EAAKrD,IAAI,CAACC,KAAK,CAAC,IAAI,CAAC,EAAE,GAEV,CACnB,GAAGoD,CAAI,CACPjD,QAAS,GAAwCiD,MAAAA,CAArC1E,8BAAsB,CAAC,gBAAuB,OAAT0E,EAAK1C,GAAG,CAC3D,IAGkB,CAExB,EAAG,CAACrF,EAAMmB,KAAK,CAAC,EAEhB,IAAM+G,EAAc,MAAOC,EAAqB/D,KAC9C,GAAI+D,EAAaC,MAAM,CAAGhE,EACxB,GAAI,CACF,CAF6B,GAEvBiE,EAAY,IAAIC,SACtBD,EAAKE,MAAM,CAAC,OAAQJ,CAAY,CAAC/D,EAAM,EACvC,IAAMoE,EAAM,MAAM1E,EAAAA,CAAUA,CAAC2E,IAAI,CAAC7E,EAAUyE,EAAM,CAChD,eAAgB,qBAClB,GACA5G,EAAKwG,IAAI,CAAC,CACR3C,SAAUkD,EAAInD,GAAG,CACjBpB,KAAMkE,CAAY,CAAC/D,EAAM,CACzBA,MAAOpE,EAAMoE,KAAK,CAAGpE,EAAMoE,KAAK,CAAG,EACnC/C,KAAM8G,CAAY,CAAC/D,EAAM,CAACM,IAAI,CAACC,KAAK,CAAC,IAAI,CAAC,EAAE,GAE9CuD,EAAYC,EAAc/D,EAAQ,EACpC,CAAE,MAAOsE,EAAO,CACdR,EAAYC,EAAc/D,EAAQ,EACpC,MAEApE,EAAMuB,QAAQ,CAACE,EAAMzB,EAAMoE,KAAK,CAAGpE,EAAMoE,KAAK,CAAG,EAErD,EAEMuE,EAASC,CAAAA,EAAAA,EAAAA,WAAAA,CAAWA,CAAC,MAAOC,IAChC,MAAMX,EAAYW,EAAc,GAChC,IAAMC,EAAWD,EAAa/C,GAAG,CAAC,GAChCiD,OAAOC,MAAM,CAAC/E,EAAM,CAClBa,QAAS8C,IAAIqB,eAAe,CAAChF,EAC/B,GAEFT,GACID,EAAS,GAAe,IAAI2F,KAAcJ,EAAS,EACnDvF,EAAS,IAAIuF,EAAS,CAC5B,EAAG,EAAE,EAkBC,cACJK,CAAY,eACZC,CAAa,cACbC,CAAY,cACZC,CAAY,cACZC,CAAY,CACZC,gBAAc,CACf,CAAGC,CAAAA,EAAAA,EAAAA,EAAAA,CAAWA,CAAC,CACdC,OACE1J,GAASA,EAAMqB,IAAI,CACf,+MACA,UACNsI,SAAUnG,EACVoG,QAAS,EACTC,QAASzG,SACTuF,EACAmB,UAhCF,CAgCaC,QAhCJA,CAA8B,EACrC,GAAiB,UAAU,CAAvBnG,GACF,GAAkC,SAAS,CAAvCK,EAAK5C,IAAI,CAAC2I,SAAS,CAAC,EAAG,GAIzB,OADAC,EAAAA,EAAKA,CAACvB,KAAK,CAACzI,EAAE,6BACP,CAAEiK,KAAM,oBAAqBC,QAAS,yBAA0B,CACzE,MACK,GAAiB,UAAU,CAAvBvG,GAC2B,OAAM,GAAI,EAAnCvC,IAAI,CAAC2I,SAAS,CAAC,EAAG,GAE3B,OADAC,EAAAA,EAAKA,CAACvB,KAAK,CAACzI,EAAE,6BACP,CAAEiK,KAAM,oBAAqBC,QAAS,yBAA0B,EAG3E,OAAO,IACT,CAkBA,GAEMpF,EAAQqF,CAAAA,EAAAA,EAAAA,OAAAA,CAAOA,CACnB,IAAO,EACL,GAAG1I,CAAS,CACZ,GAAI2H,EAAexG,EAAc,CAAEwH,QAAS,iBAAkB,CAAC,CAC/D,GAAIf,EACA,CAAEe,QAAS,oBAAqB,EAChC,CAAEA,QAAS,iBAAkB,CAAC,CAClC,GAAId,EAAe,CAAEc,QAAS,gBAAiB,EAAI,aAAExH,CAAY,CAAC,CACpE,EACA,CAACwG,EAAcE,EAAa,EAK5BzG,EADE9C,GAAwB,eAAe,CAA9BA,EAAMqB,IAAI,CAEnB,UAACiJ,QAAAA,CAAMvF,MAAO,CAAE1C,MAAO,SAAU,WAAIpC,EAAE,uBAIvC,UAACqK,QAAAA,CAAMvF,MAAO,CAAE1C,MAAO,SAAU,WAAIpC,EAAE,oBAI3C,IAAMsK,EACJf,EAAepB,MAAM,CAAG,GAAKoB,CAAc,CAAC,EAAE,CAACvF,IAAI,CAACuC,IAAI,CAAGpD,EAC7D,MACE,iCACE,UAAC4C,MAAAA,CACCrF,UAAU,yDACVoE,MAAO,CAAE/C,MAAO,OAAQC,OAAQ,OAAQ,WAExC,WAAC+D,MAAAA,CAAK,GAAGmD,EAAa,OAAEpE,CAAM,EAAE,WAC9B,UAACyF,QAAAA,CAAO,GAAGpB,GAAe,GAC1B,UAACqB,EAAAA,CAAeA,CAAAA,CAACC,KAAMC,EAAAA,GAAgBA,CAAEnE,KAAK,KAAKnE,MAAM,SACzD,UAACuI,IAAAA,CAAE7F,MAAO,CAAE1C,MAAO,UAAWwI,aAAc,KAAM,WAC/C5K,EAAE,mDAGJ,CAACuD,GACA,WAAC8G,QAAAA,CAAMvF,MAAO,CAAE1C,MAAO,SAAU,YAC/B,UAACyI,IAAAA,UAAE,UAAS,wCAGfhI,GACA9C,EAAMqB,IAAI,CACPkJ,GACE,CAFU,EAEV,QAACD,QAAAA,CAAM3J,UAAU,6BACf,UAAC8J,EAAAA,CAAeA,CAAAA,CACdC,KAAMK,EAAAA,GAAmBA,CACzBvE,KAAK,KACLnE,MAAM,QACL,IACFpC,EAAE,4CAaVsJ,CAVGgB,EAWF,WAACD,QAAAA,CAAM3J,UAAU,cAAcoE,MAAO,CAAE1C,MAAO,SAAU,YACvD,UAACoI,EAAAA,CAAeA,CAAAA,CACdC,KAAMK,EAAAA,GAAmBA,CACzBvE,KAAK,KACLnE,MAAM,QACL,IACFpC,EAAE,mCAKVqD,EAAM8E,MAAM,CAAG,GAAK,UAACpC,MAAAA,CAAIjB,MAAOvC,WAAkBqD,MAGzD", "sources": ["webpack://_N_E/./pages/updates/DocumentForm.tsx", "webpack://_N_E/./components/common/ReactDropZone.tsx"], "sourcesContent": ["//Import Library\r\nimport React from \"react\";\r\nimport {  Container, Row, Col, } from \"react-bootstrap\";\r\n\r\n//Import services/components\r\nimport ReactDropZone from \"../../components/common/ReactDropZone\";\r\nimport { useTranslation } from 'next-i18next';\r\n\r\n//TOTO refactor\r\ninterface DocumentFormProps {\r\n  data: any[];\r\n  getId: (id: any[]) => void;\r\n  srcText: any[];\r\n  getSourceCollection: (docSrcArr: any[]) => void;\r\n}\r\n\r\nconst DocumentForm = (props: DocumentFormProps): React.ReactElement => {\r\n  const { t } = useTranslation('common');\r\n  const getID = (id: any[]) => {\r\n    props.getId(id)\r\n  }\r\n\r\n  const getSourceText = (imgSrcArr: any[]) => {\r\n    props.getSourceCollection(imgSrcArr);\r\n  }\r\n\r\n  return (\r\n    <Container className=\"formCard mt-0 p-0\" fluid>\r\n      <Col>\r\n        <Row className='header-block' lg={12}>\r\n          <h6><span>{t(\"update.Documents\")}</span></h6>\r\n        </Row>\r\n        <Row>\r\n          <ReactDropZone datas={props.data} type=\"application\" srcText={props.srcText} getImgID={(id: any[]) => getID(id)} getImageSource={(imgSrcArr: any[]) => getSourceText(imgSrcArr)} />\r\n        </Row>\r\n      </Col>\r\n    </Container>\r\n  );\r\n}\r\n\r\nexport default DocumentForm;", "//Import Library\r\nimport React, { useMemo, useEffect, useState, useCallback } from \"react\";\r\nimport { useDropzone } from \"react-dropzone\";\r\nimport { FontAwesomeIcon } from \"@fortawesome/react-fontawesome\";\r\nimport {\r\n  faExclamationCircle,\r\n  faCloudUploadAlt,\r\n} from \"@fortawesome/free-solid-svg-icons\";\r\nimport { Form, Button, Modal, Col } from \"react-bootstrap\";\r\nimport _ from \"lodash\";\r\n\r\nimport toast from 'react-hot-toast';\r\n\r\n//Import services/components\r\nimport apiService from \"../../services/apiService\";\r\nimport { useTranslation } from 'next-i18next';\r\n\r\n// Define type for temp array items\r\ninterface TempItem {\r\n  serverID: string;\r\n  file?: any;\r\n  index: number;\r\n  type: string;\r\n}\r\n\r\nlet temp: TempItem[] = [];\r\n\r\nconst baseStyle: any = {\r\n  flex: 1,\r\n  display: \"flex\",\r\n  flexDirection: \"column\",\r\n  alignItems: \"center\",\r\n  justifyContent: \"center\",\r\n  width: \"100%\",\r\n  height: \"100%\",\r\n  borderWidth: 0.1,\r\n  borderColor: \"#fafafa\",\r\n  backgroundColor: \"#fafafa\",\r\n  color: \"black\",\r\n  transition: \"border  .24s ease-in-out\",\r\n  padding: \"15px\",\r\n};\r\n\r\nconst thumb: any = {\r\n  display: \"flex\",\r\n  borderRadius: 2,\r\n  border: \"1px solid #ddd\",\r\n  margin: 8,\r\n  height: 175,\r\n  boxShadow: \"0 0 15px 0.25px rgba(0,0,0,0.15)\",\r\n  boxSizing: \"border-box\",\r\n};\r\n\r\nconst thumbsContainer: any = {\r\n  display: \"flex\",\r\n  padding: \"10px\",\r\n  width: \"100%\",\r\n  border: \"2px solid gray\",\r\n  flexDirection: \"column\",\r\n  justifyContent: \"flex-start\",\r\n  flexWrap: \"wrap\",\r\n  marginTop: 20,\r\n};\r\n\r\nconst thumbInner: any = {\r\n  display: \"flex\",\r\n  minWidth: 0,\r\n};\r\n\r\nconst deleteIcon: any = {\r\n  display: \"flex\",\r\n  justifyContent: \"center\",\r\n  alignItems: \"center\",\r\n  marginLeft: 30,\r\n};\r\n\r\nconst img = {\r\n  width: \"150px\",\r\n};\r\n\r\nconst activeStyle: any = {\r\n  borderColor: \"#2196f3\",\r\n};\r\n\r\nconst ReactDropZone = (props: any) => {\r\n  const { t } = useTranslation('common');\r\n  const [modalShow, setModalShow] = useState(false);\r\n  const [deleteFile, setDeleteFile] = useState();\r\n  const limit: any =\r\n    props.type == \"application\" ? 20971520 : process.env.UPLOAD_LIMIT;\r\n  const [files, setFiles] = useState<any[]>([]);\r\n  const [multi, setMulti] = useState(true);\r\n  const [imageSource, setImageSource] = useState<string[]>([]);\r\n\r\n  const endpoint = props && props.type === \"application\" ? \"/files\" : \"/image\";\r\n  const imageDelete = async (id: string) => {\r\n    const _res = await apiService.remove(`${endpoint}/${id}`);\r\n  };\r\n\r\n  const removeFile = (file: any) => {\r\n    setDeleteFile(file);\r\n    setModalShow(true);\r\n  };\r\n\r\n  const handleSource = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>, index: number) => {\r\n    const items = [...imageSource];\r\n    items[index] = e.target.value;\r\n    setImageSource(items);\r\n  };\r\n\r\n  const getComponent = (file: any) => {\r\n    const fileType = file && file.name.split(\".\").pop();\r\n    switch (fileType) {\r\n      case \"JPG\":\r\n      case \"jpg\":\r\n      case \"jpeg\":\r\n      case \"jpg\":\r\n      case \"png\":\r\n        return <img src={file.preview} style={img} />;\r\n      case \"pdf\":\r\n        return (\r\n          <img\r\n            src=\"/images/fileIcons/pdfFile.png\"\r\n            className={\r\n              props.type === \"application\" ? \"docPreview\" : \"imgPreview\"\r\n            }\r\n          />\r\n        );\r\n      case \"docx\":\r\n        return (\r\n          <img\r\n            src=\"/images/fileIcons/wordFile.png\"\r\n            className={\r\n              props.type === \"application\" ? \"docPreview\" : \"imgPreview\"\r\n            }\r\n          />\r\n        );\r\n      case \"xls\":\r\n      case \"xlsx\":\r\n        return (\r\n          <img\r\n            src=\"/images/fileIcons/xlsFile.png\"\r\n            className={\r\n              props.type === \"application\" ? \"docPreview\" : \"imgPreview\"\r\n            }\r\n          />\r\n        );\r\n      default:\r\n        return (\r\n          <img\r\n            src=\"/images/fileIcons/wordFile.png\"\r\n            className={\r\n              props.type === \"application\" ? \"docPreview\" : \"imgPreview\"\r\n            }\r\n          />\r\n        );\r\n    }\r\n  };\r\n\r\n  const modalHide = () => setModalShow(false);\r\n\r\n  const cancelHandler = () => {\r\n    setModalShow(false);\r\n  };\r\n\r\n  const submitHandler = (fileselector: any) => {\r\n    fileselector = deleteFile;\r\n    const obj =\r\n      fileselector && fileselector._id\r\n        ? { serverID: fileselector._id }\r\n        : { file: fileselector };\r\n    const _index = _.findIndex(temp, obj);\r\n    //**Delete the source Field**//\r\n    const removeSrc = [...imageSource];\r\n    removeSrc.splice(_index, 1);\r\n    setImageSource(removeSrc);\r\n    //**End**/\r\n    imageDelete(temp[_index].serverID);\r\n    temp.splice(_index, 1);\r\n    props.getImgID(temp, props.index ? props.index : 0);\r\n    const newFiles = [...files];\r\n    newFiles.splice(newFiles.indexOf(fileselector), 1);\r\n    setFiles(newFiles);\r\n    setModalShow(false);\r\n  };\r\n\r\n  const thumbs: any = files.map((file: any, i) => {\r\n    return (\r\n      <div key={i}>\r\n        <Col xs={12}>\r\n          <div className=\"row\">\r\n            <Col\r\n              md={4}\r\n              lg={3}\r\n              className={\r\n                props.type === \"application text-center align-self-center\"\r\n                  ? \"docImagePreview text-center align-self-center\"\r\n                  : \"imgPreview text-center align-self-center\"\r\n              }\r\n            >\r\n              {getComponent(file)}\r\n            </Col>\r\n            <Col md={5} lg={7} className=\"align-self-center\">\r\n              <Form>\r\n                <Form.Group controlId=\"filename\">\r\n                  <Form.Label className=\"mt-2\">{t(\"FileName\")}</Form.Label>\r\n                  <Form.Control\r\n                    size=\"sm\"\r\n                    type=\"text\"\r\n                    disabled\r\n                    value={file.original_name ? file.original_name : file.name}\r\n                  />\r\n                </Form.Group>\r\n                <Form.Group controlId=\"description\">\r\n                  <Form.Label>\r\n                    {props.type === \"application\"\r\n                      ? t(\"ShortDescription/(Max255Characters)\")\r\n                      : t(\"Source/Description\")}\r\n                  </Form.Label>\r\n                  <Form.Control\r\n                    maxLength={props.type === \"application\" ? 255 : undefined}\r\n                    size=\"sm\"\r\n                    type=\"text\"\r\n                    placeholder={\r\n                      props.type === \"application\"\r\n                        ? t(\"`Enteryourdocumentdescription`\")\r\n                        : t(\"`Enteryourimagesource/description`\")\r\n                    }\r\n                    value={imageSource[i]}\r\n                    onChange={(e) => handleSource(e, i)}\r\n                  />\r\n                </Form.Group>\r\n              </Form>\r\n            </Col>\r\n            <Col\r\n              md={3}\r\n              lg={2}\r\n              className=\"align-self-center text-center\"\r\n              onClick={() => removeFile(file)}\r\n            >\r\n              <Button variant=\"dark\">{t(\"Remove\")}</Button>\r\n            </Col>\r\n          </div>\r\n        </Col>\r\n        <Modal show={modalShow} onHide={modalHide}>\r\n          <Modal.Header closeButton>\r\n            <Modal.Title>{t(\"DeleteFile\")}</Modal.Title>\r\n          </Modal.Header>\r\n          <Modal.Body>{t(\"Areyousurewanttodeletethisfile?\")}</Modal.Body>\r\n          <Modal.Footer>\r\n            <Button variant=\"secondary\" onClick={cancelHandler}>\r\n              {t(\"Cancel\")}\r\n            </Button>\r\n            <Button variant=\"primary\" onClick={() => submitHandler(file)}>\r\n              {t(\"yes\")}\r\n            </Button>\r\n          </Modal.Footer>\r\n        </Modal>\r\n      </div>\r\n    );\r\n  });\r\n\r\n  useEffect(() => {\r\n    files.forEach((file) => URL.revokeObjectURL(file.preview));\r\n    temp = [];\r\n  }, []);\r\n\r\n  useEffect(() => {\r\n    props.getImageSource(imageSource);\r\n  }, [imageSource]);\r\n\r\n  useEffect(() => {\r\n    setImageSource(props.srcText);\r\n  }, [props.srcText]);\r\n\r\n  useEffect(() => {\r\n    props && props.singleUpload === \"true\" && setMulti(false);\r\n    if (props && props.datas) {\r\n      const newObj = props.datas.map((item: any, _i: number) => {\r\n        temp.push({\r\n          serverID: item._id,\r\n          index: props.index ? props.index : 0,\r\n          type: item.name.split(\".\")[1],\r\n        });\r\n        const previewState = {\r\n          ...item,\r\n          preview: `${process.env.API_SERVER}/image/show/${item._id}`,\r\n        };\r\n        return previewState;\r\n      });\r\n      setFiles([...newObj]);\r\n    }\r\n  }, [props.datas]);\r\n\r\n  const filesUpload = async (filesinitial: any[], index: number) => {\r\n    if (filesinitial.length > index) {\r\n      try {\r\n        const form: any = new FormData();\r\n        form.append(\"file\", filesinitial[index]);\r\n        const res = await apiService.post(endpoint, form, {\r\n          \"Content-Type\": \"multipart/form-data\",\r\n        });\r\n        temp.push({\r\n          serverID: res._id,\r\n          file: filesinitial[index],\r\n          index: props.index ? props.index : 0,\r\n          type: filesinitial[index].name.split(\".\")[1],\r\n        });\r\n        filesUpload(filesinitial, index + 1);\r\n      } catch (error) {\r\n        filesUpload(filesinitial, index + 1);\r\n      }\r\n    } else {\r\n      props.getImgID(temp, props.index ? props.index : 0);\r\n    }\r\n  };\r\n\r\n  const onDrop = useCallback(async (ondrop_files: any[]) => {\r\n    await filesUpload(ondrop_files, 0);\r\n    const accFiles = ondrop_files.map((file: any) =>\r\n      Object.assign(file, {\r\n        preview: URL.createObjectURL(file),\r\n      })\r\n    );\r\n    multi\r\n      ? setFiles((prevState) => [...prevState, ...accFiles])\r\n      : setFiles([...accFiles]);\r\n  }, []);\r\n\r\n  function nameLengthValidator(file: File) {\r\n    if (endpoint === \"/image\") {\r\n      if (file.type.substring(0, 5) === \"image\") {\r\n        return null;\r\n      } else {\r\n        toast.error(t(\"toast.filetypenotsupport\"));\r\n        return { code: \"file-invalid-type\", message: \"File type not supported\" };\r\n      }\r\n    } else if (endpoint === \"/files\") {\r\n      if (!(file.type.substring(0, 5) !== \"image\")) {\r\n        toast.error(t(\"toast.filetypenotsupport\"));\r\n        return { code: \"file-invalid-type\", message: \"File type not supported\" };\r\n      }\r\n    }\r\n    return null;\r\n  }\r\n  const {\r\n    getRootProps,\r\n    getInputProps,\r\n    isDragActive,\r\n    isDragAccept,\r\n    isDragReject,\r\n    fileRejections,\r\n  } = useDropzone({\r\n    accept:\r\n      props && props.type\r\n        ? \"application/pdf, application/vnd.openxmlformats-officedocument.wordprocessingml.document, application/vnd.oasis.opendocument.text,application/vnd.openxmlformats-officedocument.spreadsheetml.sheet,text/csv\"\r\n        : \"image/*\",\r\n    multiple: multi,\r\n    minSize: 0,\r\n    maxSize: limit,\r\n    onDrop,\r\n    validator: nameLengthValidator,\r\n  });\r\n\r\n  const style = useMemo(\r\n    () => ({\r\n      ...baseStyle,\r\n      ...(isDragActive ? activeStyle : { outline: \"2px dashed #bbb\" }),\r\n      ...(isDragAccept\r\n        ? { outline: \"2px dashed #595959\" }\r\n        : { outline: \"2px dashed #bbb\" }),\r\n      ...(isDragReject ? { outline: \"2px dashed red\" } : { activeStyle }),\r\n    }),\r\n    [isDragActive, isDragReject]\r\n  );\r\n\r\n  let dropZoneMsg;\r\n  if (props && props.type === \"application\") {\r\n    dropZoneMsg = (\r\n      <small style={{ color: \"#595959\" }}>{t(\"DocumentWeSupport\")}</small>\r\n    );\r\n  } else {\r\n    dropZoneMsg = (\r\n      <small style={{ color: \"#595959\" }}>{t(\"ImageWeSupport\")}</small>\r\n    );\r\n  }\r\n\r\n  const isFileTooLarge =\r\n    fileRejections.length > 0 && fileRejections[0].file.size > limit;\r\n  return (\r\n    <>\r\n      <div\r\n        className=\" d-flex justify-content-center align-items-center mt-3\"\r\n        style={{ width: \"100%\", height: \"180px\" }}\r\n      >\r\n        <div {...getRootProps({ style })}>\r\n          <input {...getInputProps()} />\r\n          <FontAwesomeIcon icon={faCloudUploadAlt} size=\"4x\" color=\"#999\" />\r\n          <p style={{ color: \"#595959\", marginBottom: \"0px\" }}>\r\n            {t(\"Drag'n'dropsomefileshere,orclicktoselectfiles\")}\r\n          </p>\r\n\r\n          {!multi && (\r\n            <small style={{ color: \"#595959\" }}>\r\n              <b>Note:</b> One single image will be accepted\r\n            </small>\r\n          )}\r\n          {dropZoneMsg}\r\n          {props.type === \"application\"\r\n            ? isFileTooLarge && (\r\n                <small className=\"text-danger mt-2\">\r\n                  <FontAwesomeIcon\r\n                    icon={faExclamationCircle}\r\n                    size=\"1x\"\r\n                    color=\"red\"\r\n                  />{\" \"}\r\n                  {t(\"FileistoolargeItshouldbelessthan20MB\")}\r\n                </small>\r\n              )\r\n            : isFileTooLarge && (\r\n                <small className=\"text-danger mt-2\">\r\n                  <FontAwesomeIcon\r\n                    icon={faExclamationCircle}\r\n                    size=\"1x\"\r\n                    color=\"red\"\r\n                  />{\" \"}\r\n                  {t(\"FileistoolargeItshouldbelessthan20MB\")}\r\n                </small>\r\n              )}\r\n          {isDragReject && (\r\n            <small className=\"text-danger\" style={{ color: \"#595959\" }}>\r\n              <FontAwesomeIcon\r\n                icon={faExclamationCircle}\r\n                size=\"1x\"\r\n                color=\"red\"\r\n              />{\" \"}\r\n              {t(\"Filetypenotacceptedsorr\")}\r\n            </small>\r\n          )}\r\n        </div>\r\n      </div>\r\n      {files.length > 0 && <div style={thumbsContainer}>{thumbs}</div>}\r\n    </>\r\n  );\r\n};\r\n\r\nexport default ReactDropZone;\r\n"], "names": ["props", "t", "useTranslation", "DocumentForm", "getID", "getId", "id", "getSourceText", "getSourceCollection", "imgSrcArr", "Container", "className", "fluid", "Col", "Row", "lg", "h6", "span", "ReactDropZone", "datas", "data", "type", "srcText", "getImgID", "getImageSource", "temp", "baseStyle", "flex", "display", "flexDirection", "alignItems", "justifyContent", "width", "height", "borderWidth", "borderColor", "backgroundColor", "color", "transition", "padding", "thumbsContainer", "border", "flexWrap", "marginTop", "img", "activeStyle", "dropZoneMsg", "modalShow", "setModalShow", "useState", "deleteFile", "setDeleteFile", "limit", "process", "files", "setFiles", "multi", "set<PERSON><PERSON><PERSON>", "imageSource", "setImageSource", "endpoint", "imageDelete", "apiService", "remove", "removeFile", "file", "handleSource", "e", "index", "items", "target", "value", "getComponent", "fileType", "name", "split", "pop", "src", "preview", "style", "modalHide", "cancelHandler", "<PERSON><PERSON><PERSON><PERSON>", "obj", "fileselector", "_id", "serverID", "_index", "_", "removeSrc", "splice", "newFiles", "indexOf", "thumbs", "map", "i", "div", "xs", "md", "Form", "Group", "controlId", "Label", "Control", "size", "disabled", "original_name", "max<PERSON><PERSON><PERSON>", "undefined", "placeholder", "onChange", "onClick", "<PERSON><PERSON>", "variant", "Modal", "show", "onHide", "Header", "closeButton", "Title", "Body", "Footer", "useEffect", "for<PERSON>ach", "URL", "revokeObjectURL", "singleUpload", "item", "_i", "push", "filesUpload", "filesinitial", "length", "form", "FormData", "append", "res", "post", "error", "onDrop", "useCallback", "ondrop_files", "accFiles", "Object", "assign", "createObjectURL", "prevState", "getRootProps", "getInputProps", "isDragActive", "isDragAccept", "isDragReject", "fileRejections", "useDropzone", "accept", "multiple", "minSize", "maxSize", "validator", "nameLengthValidator", "substring", "toast", "code", "message", "useMemo", "outline", "small", "isFileTooLarge", "input", "FontAwesomeIcon", "icon", "faCloudUploadAlt", "p", "marginBottom", "b", "faExclamationCircle"], "sourceRoot": "", "ignoreList": []}