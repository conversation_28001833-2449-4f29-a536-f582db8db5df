"use strict";(()=>{var e={};e.id=6312,e.ids=[636,3220,6312],e.modules={123:e=>{e.exports=require("dom-helpers/removeEventListener")},1332:e=>{e.exports=require("react-custom-scrollbars-2")},1428:e=>{e.exports=import("axios")},1680:e=>{e.exports=require("@restart/ui/utils")},1919:e=>{e.exports=require("react-transition-group/Transition")},3892:e=>{e.exports=require("classnames")},4048:e=>{e.exports=require("react-truncate")},6009:e=>{e.exports=require("dom-helpers/ownerDocument")},6952:e=>{e.exports=require("@restart/ui/Modal")},7374:e=>{e.exports=require("@restart/ui/Dropdown")},8732:e=>{e.exports=require("react/jsx-runtime")},9653:e=>{e.exports=require("@restart/ui/Overlay")},10557:(e,r,t)=>{t.a(e,async(e,s)=>{try{t.r(r),t.d(r,{config:()=>m,default:()=>c,getServerSideProps:()=>g,getStaticPaths:()=>h,getStaticProps:()=>x,reportWebVitals:()=>q,routeModule:()=>S,unstable_getServerProps:()=>y,unstable_getServerSideProps:()=>j,unstable_getStaticParams:()=>f,unstable_getStaticPaths:()=>b,unstable_getStaticProps:()=>P});var i=t(63885),o=t(80237),n=t(81413),a=t(9616),u=t.n(a),p=t(72386),l=t(56678),d=e([p]);p=(d.then?(await d)():d)[0];let c=(0,n.M)(l,"default"),x=(0,n.M)(l,"getStaticProps"),h=(0,n.M)(l,"getStaticPaths"),g=(0,n.M)(l,"getServerSideProps"),m=(0,n.M)(l,"config"),q=(0,n.M)(l,"reportWebVitals"),P=(0,n.M)(l,"unstable_getStaticProps"),b=(0,n.M)(l,"unstable_getStaticPaths"),f=(0,n.M)(l,"unstable_getStaticParams"),y=(0,n.M)(l,"unstable_getServerProps"),j=(0,n.M)(l,"unstable_getServerSideProps"),S=new i.PagesRouteModule({definition:{kind:o.A.PAGES,page:"/operation/OperationPartners",pathname:"/operation/OperationPartners",bundlePath:"",filename:""},components:{App:p.default,Document:u()},userland:l});s()}catch(e){s(e)}})},11242:e=>{e.exports=require("redux-persist/integration/react")},11688:e=>{e.exports=require("@restart/hooks/useWillUnmount")},12388:e=>{e.exports=require("dom-helpers/addEventListener")},13364:e=>{e.exports=require("redux-saga/effects")},14062:e=>{e.exports=import("react-redux")},14078:e=>{e.exports=import("swr")},14332:e=>{e.exports=require("uncontrollable")},16116:e=>{e.exports=require("invariant")},18622:e=>{e.exports=require("yup")},19121:e=>{e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},22326:e=>{e.exports=require("react-dom")},22541:e=>{e.exports=require("redux-persist/lib/storage")},25303:e=>{e.exports=require("@restart/ui/NavItem")},26324:e=>{e.exports=require("warning")},27825:e=>{e.exports=require("lodash")},27910:e=>{e.exports=require("stream")},28217:e=>{e.exports=require("@restart/ui/DropdownItem")},29021:e=>{e.exports=require("fs")},29294:e=>{e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},29628:e=>{e.exports=require("dom-helpers/addClass")},29825:e=>{e.exports=require("prop-types")},29841:e=>{e.exports=require("dom-helpers/hasClass")},30362:e=>{e.exports=require("@restart/ui/DropdownToggle")},33873:e=>{e.exports=require("path")},36653:e=>{e.exports=require("nprogress")},36955:e=>{e.exports=require("@restart/hooks/useBreakpoint")},37766:e=>{e.exports=require("@restart/hooks/useMergedRefs")},38609:e=>{e.exports=require("react-data-table-component")},39756:e=>{e.exports=import("redux")},40051:e=>{e.exports=require("dom-helpers/removeClass")},40361:e=>{e.exports=require("next/dist/compiled/next-server/pages.runtime.prod.js")},42893:e=>{e.exports=import("react-hot-toast")},43294:e=>{e.exports=require("formik")},50009:e=>{e.exports=require("@restart/ui/DropdownMenu")},56084:(e,r,t)=>{t.d(r,{A:()=>p});var s=t(8732);t(82015);var i=t(38609),o=t.n(i),n=t(88751),a=t(30370);function u(e){let{t:r}=(0,n.useTranslation)("common"),t={rowsPerPageText:r("Rowsperpage")},{columns:i,data:u,totalRows:p,resetPaginationToggle:l,subheader:d,subHeaderComponent:c,handlePerRowsChange:x,handlePageChange:h,rowsPerPage:g,defaultRowsPerPage:m,selectableRows:q,loading:P,pagServer:b,onSelectedRowsChange:f,clearSelectedRows:y,sortServer:j,onSort:S,persistTableHead:w,sortFunction:v,...A}=e,k={paginationComponentOptions:t,noDataComponent:r("NoData"),noHeader:!0,columns:i,data:u||[],dense:!0,paginationResetDefaultPage:l,subHeader:d,progressPending:P,subHeaderComponent:c,pagination:!0,paginationServer:b,paginationPerPage:m||10,paginationRowsPerPageOptions:g||[10,15,20,25,30],paginationTotalRows:p,onChangeRowsPerPage:x,onChangePage:h,selectableRows:q,onSelectedRowsChange:f,clearSelectedRows:y,progressComponent:(0,s.jsx)(a.A,{}),sortIcon:(0,s.jsx)("i",{className:"sort-icon fas fa-exchange-alt"}),sortServer:j,onSort:S,sortFunction:v,persistTableHead:w,className:"rki-table"};return(0,s.jsx)(o(),{...k})}u.defaultProps={subHeader:!1,pagination:!0,totalRows:null,pagServer:!0,onSelectedRowsChange:null,clearSelectedRows:!1,sortServer:!1,persistTableHead:!1};let p=u},56678:(e,r,t)=>{t.r(r),t.d(r,{default:()=>h});var s=t(8732),i=t(19918),o=t.n(i),n=t(27825),a=t.n(n),u=t(81181),p=t(63241),l=t(56084),d=t(88751);let c=({networks:e})=>e&&e.length>0?(0,s.jsx)("ul",{children:e.map((e,r)=>(0,s.jsx)("li",{children:e.title},r))}):null,x=(0,s.jsxs)(u.A,{id:"popover-basic",children:[(0,s.jsx)(u.A.Header,{as:"h3",className:"text-center",children:"NETWORKS"}),(0,s.jsx)(u.A.Body,{children:(0,s.jsxs)("div",{className:"m-2",children:[(0,s.jsxs)("p",{children:[(0,s.jsx)("b",{children:"EMLab"})," - European Mobile Lab"]}),(0,s.jsxs)("p",{children:[(0,s.jsx)("b",{children:"EMT"})," - Emergency Medical Teams"]}),(0,s.jsxs)("p",{children:[(0,s.jsx)("b",{children:"GHPP"})," - Global Health Protection Program"]}),(0,s.jsxs)("p",{children:[(0,s.jsx)("b",{children:"GOARN"})," - Global Outbreak Alert & Response Network"]}),(0,s.jsxs)("p",{children:[(0,s.jsx)("b",{children:"IANPHI"})," - International Association of National Public Health Institutes"]}),(0,s.jsxs)("p",{children:[(0,s.jsx)("b",{children:"STAKOB"})," - St\xe4ndiger Arbeitskreis der Kompetenz-und Behandlungszentren"]}),(0,s.jsxs)("p",{children:[(0,s.jsx)("b",{children:"WHOCC"}),"- World Health Organization Collaborating Centres"]})]})})]}),h=function(e){let{t:r}=(0,d.useTranslation)("common"),{partners:t}=e,i=[{name:r("Organisation"),selector:"title",cell:e=>e&&e.institution?(0,s.jsx)(o(),{href:"/institution/[...routes]",as:`/institution/show/${e.institution._id}`,children:e.institution.title}):"",sortable:!0},{name:r("Country"),selector:"country",cell:e=>e&&e.institution&&e.institution.address&&e.institution.address.country?(0,s.jsx)(o(),{href:"/country/[...routes]",as:`/country/show/${e.institution.address.country._id}`,children:e.institution.address.country.title}):"",sortable:!0},{name:r("Type"),selector:"type.title",cell:e=>e.institution&&e.institution.type&&e.institution.type.title?e.institution.type.title:"",sortable:!0},{name:(0,s.jsx)(p.A,{trigger:"click",placement:"right",overlay:x,children:(0,s.jsxs)("span",{children:[r("Network"),"\xa0\xa0\xa0",(0,s.jsx)("i",{className:"fa fa-info-circle",style:{cursor:"pointer"},"aria-hidden":"true"})]})}),selector:r("Networks"),cell:e=>e.institution&&e.institution.networks&&e.institution.networks.length>0?(0,s.jsx)(c,{networks:e.institution.networks}):""}],n=e=>{if(e.institution.address&&e.institution.address.country)return e.institution.address.country&&e.institution.address.country.title?e.institution.address.country.title.toLowerCase():e.institution.address.country.title},u=e=>{if(e.institution.type&&e.institution.type&&e.institution.type.title)return e.institution.type.title.toLowerCase()};return(0,s.jsx)(l.A,{columns:i,data:t,pagServer:!0,persistTableHead:!0,sortFunction:(e,r,t)=>a().orderBy(e,e=>{if("country"===r)n(e);else if("type.title"===r)u(e);else if(e.institution&&e.institution[r])return e.institution[r].toLowerCase()},t)})}},57664:e=>{e.exports=require("dom-helpers/scrollbarSize")},58928:e=>{e.exports=require("dom-helpers/canUseDOM")},59717:e=>{e.exports=require("@restart/ui/DropdownContext")},60560:e=>{e.exports=require("dom-helpers/contains")},63033:e=>{e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},65447:e=>{e.exports=require("@restart/ui/Button")},67364:e=>{e.exports=require("@restart/hooks/useCallbackRef")},68455:e=>{e.exports=import("redux-saga")},69722:e=>{e.exports=require("es6-promise")},74075:e=>{e.exports=require("zlib")},74716:e=>{e.exports=require("moment")},74987:e=>{e.exports=require("@restart/ui/ModalManager")},78097:e=>{e.exports=require("next-redux-saga")},78634:e=>{e.exports=require("@restart/ui/Anchor")},80237:(e,r)=>{Object.defineProperty(r,"A",{enumerable:!0,get:function(){return t}});var t=function(e){return e.PAGES="PAGES",e.PAGES_API="PAGES_API",e.APP_PAGE="APP_PAGE",e.APP_ROUTE="APP_ROUTE",e.IMAGE="IMAGE",e}({})},81366:e=>{e.exports=require("redux-auth-wrapper/connectedAuthWrapper")},81413:(e,r)=>{Object.defineProperty(r,"M",{enumerable:!0,get:function(){return function e(r,t){return t in r?r[t]:"then"in r&&"function"==typeof r.then?r.then(r=>e(r,t)):"function"==typeof r&&"default"===t?r:void 0}}})},81521:e=>{e.exports=require("dom-helpers/querySelectorAll")},81895:e=>{e.exports=require("@restart/hooks/useEventCallback")},82015:e=>{e.exports=require("react")},86842:e=>{e.exports=require("@restart/ui/SelectableContext")},87571:e=>{e.exports=require("dom-helpers/transitionEnd")},88751:e=>{e.exports=require("next-i18next")},93787:e=>{e.exports=require("redux-persist")},94947:e=>{e.exports=require("@restart/hooks/useTimeout")},96196:e=>{e.exports=require("next-redux-wrapper")},98320:e=>{e.exports=require("@restart/hooks/useIsomorphicEffect")},99460:e=>{e.exports=require("dom-helpers/css")}};var r=require("../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[6089,9216,9616,2386],()=>t(10557));module.exports=s})();