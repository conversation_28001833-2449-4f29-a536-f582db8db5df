"use strict";(()=>{var e={};e.id=3818,e.ids=[636,3220,3818],e.modules={123:e=>{e.exports=require("dom-helpers/removeEventListener")},1332:e=>{e.exports=require("react-custom-scrollbars-2")},1428:e=>{e.exports=import("axios")},1680:e=>{e.exports=require("@restart/ui/utils")},1919:e=>{e.exports=require("react-transition-group/Transition")},3892:e=>{e.exports=require("classnames")},4048:e=>{e.exports=require("react-truncate")},4439:(e,r,s)=>{s.a(e,async(e,t)=>{try{s.r(r),s.d(r,{default:()=>f});var a=s(8732),i=s(7082),o=s(83551),n=s(49481),p=s(91353),d=s(19918),l=s.n(d),u=s(27053),c=s(83616),m=s(88751),x=s(45927),g=s(14062),h=s(35557),A=e([c,g]);[c,g]=A.then?(await A)():A;let f=e=>{let{t:r}=(0,m.useTranslation)("common"),s=()=>(0,a.jsx)("div",{children:(0,a.jsxs)(i.A,{style:{overflowX:"hidden"},fluid:!0,className:"p-0",children:[(0,a.jsx)(o.A,{children:(0,a.jsx)(n.A,{xs:12,children:(0,a.jsx)(u.A,{title:r("adminsetting.areaofwork.Forms.Addareaofwork")})})}),(0,a.jsx)(o.A,{children:(0,a.jsx)(n.A,{xs:12,children:(0,a.jsx)(l(),{href:"/adminsettings/[...routes]",as:"/adminsettings/create_area_of_work",children:(0,a.jsx)(p.A,{variant:"secondary",size:"sm",children:r("adminsetting.areaofwork.Forms.Addareaofwork")})})})}),(0,a.jsx)(o.A,{className:"mt-3",children:(0,a.jsx)(n.A,{xs:12,children:(0,a.jsx)(c.default,{})})})]})}),t=(0,x.default)(()=>(0,a.jsx)(s,{})),d=(0,g.useSelector)(e=>e);return d?.permissions?.area_of_work?.["create:any"]?(0,a.jsx)(t,{}):(0,a.jsx)(h.default,{})};t()}catch(e){t(e)}})},6009:e=>{e.exports=require("dom-helpers/ownerDocument")},6952:e=>{e.exports=require("@restart/ui/Modal")},7374:e=>{e.exports=require("@restart/ui/Dropdown")},8732:e=>{e.exports=require("react/jsx-runtime")},9653:e=>{e.exports=require("@restart/ui/Overlay")},11242:e=>{e.exports=require("redux-persist/integration/react")},11688:e=>{e.exports=require("@restart/hooks/useWillUnmount")},12388:e=>{e.exports=require("dom-helpers/addEventListener")},13364:e=>{e.exports=require("redux-saga/effects")},14062:e=>{e.exports=import("react-redux")},14078:e=>{e.exports=import("swr")},14332:e=>{e.exports=require("uncontrollable")},16116:e=>{e.exports=require("invariant")},18622:e=>{e.exports=require("yup")},19121:e=>{e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},22326:e=>{e.exports=require("react-dom")},22541:e=>{e.exports=require("redux-persist/lib/storage")},23742:(e,r,s)=>{s.a(e,async(e,t)=>{try{s.r(r),s.d(r,{config:()=>h,default:()=>c,getServerSideProps:()=>g,getStaticPaths:()=>x,getStaticProps:()=>m,reportWebVitals:()=>A,routeModule:()=>v,unstable_getServerProps:()=>S,unstable_getServerSideProps:()=>q,unstable_getStaticParams:()=>y,unstable_getStaticPaths:()=>w,unstable_getStaticProps:()=>f});var a=s(63885),i=s(80237),o=s(81413),n=s(9616),p=s.n(n),d=s(72386),l=s(4439),u=e([d,l]);[d,l]=u.then?(await u)():u;let c=(0,o.M)(l,"default"),m=(0,o.M)(l,"getStaticProps"),x=(0,o.M)(l,"getStaticPaths"),g=(0,o.M)(l,"getServerSideProps"),h=(0,o.M)(l,"config"),A=(0,o.M)(l,"reportWebVitals"),f=(0,o.M)(l,"unstable_getStaticProps"),w=(0,o.M)(l,"unstable_getStaticPaths"),y=(0,o.M)(l,"unstable_getStaticParams"),S=(0,o.M)(l,"unstable_getServerProps"),q=(0,o.M)(l,"unstable_getServerSideProps"),v=new a.PagesRouteModule({definition:{kind:i.A.PAGES,page:"/adminsettings/areaOfWork",pathname:"/adminsettings/areaOfWork",bundlePath:"",filename:""},components:{App:d.default,Document:p()},userland:l});t()}catch(e){t(e)}})},25303:e=>{e.exports=require("@restart/ui/NavItem")},26324:e=>{e.exports=require("warning")},27053:(e,r,s)=>{s.d(r,{A:()=>a});var t=s(8732);function a(e){return(0,t.jsx)("h2",{className:"page-heading",children:e.title})}},27825:e=>{e.exports=require("lodash")},27910:e=>{e.exports=require("stream")},28217:e=>{e.exports=require("@restart/ui/DropdownItem")},29021:e=>{e.exports=require("fs")},29294:e=>{e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},29628:e=>{e.exports=require("dom-helpers/addClass")},29825:e=>{e.exports=require("prop-types")},29841:e=>{e.exports=require("dom-helpers/hasClass")},30362:e=>{e.exports=require("@restart/ui/DropdownToggle")},33873:e=>{e.exports=require("path")},35557:(e,r,s)=>{s.r(r),s.d(r,{default:()=>a});var t=s(8732);function a(e){return(0,t.jsx)("div",{className:"container-fluid p-0 response-message-block",children:(0,t.jsx)("div",{className:"message",children:"you don't have permission to access"})})}},36653:e=>{e.exports=require("nprogress")},36955:e=>{e.exports=require("@restart/hooks/useBreakpoint")},37766:e=>{e.exports=require("@restart/hooks/useMergedRefs")},38609:e=>{e.exports=require("react-data-table-component")},39756:e=>{e.exports=import("redux")},40051:e=>{e.exports=require("dom-helpers/removeClass")},40361:e=>{e.exports=require("next/dist/compiled/next-server/pages.runtime.prod.js")},42893:e=>{e.exports=import("react-hot-toast")},43294:e=>{e.exports=require("formik")},45927:(e,r,s)=>{s.r(r),s.d(r,{canAddAreaOfWork:()=>o,canAddContent:()=>j,canAddCountry:()=>n,canAddDeploymentStatus:()=>p,canAddEventStatus:()=>d,canAddExpertise:()=>l,canAddFocalPointApproval:()=>u,canAddHazardTypes:()=>x,canAddHazards:()=>m,canAddLandingPage:()=>k,canAddOperationStatus:()=>f,canAddOrganisationApproval:()=>g,canAddOrganisationNetworks:()=>h,canAddOrganisationTypes:()=>A,canAddProjectStatus:()=>w,canAddRegions:()=>y,canAddRiskLevels:()=>S,canAddSyndromes:()=>q,canAddUpdateTypes:()=>v,canAddUsers:()=>_,canAddVspaceApproval:()=>c,canAddWorldRegion:()=>P,default:()=>C});var t=s(81366),a=s.n(t);let i="create:any",o=a()({authenticatedSelector:e=>!!e.permissions&&!!e.permissions.area_of_work&&!!e.permissions.area_of_work[i],wrapperDisplayName:"CanAddAreaOfWork"}),n=a()({authenticatedSelector:e=>!!e.permissions&&!!e.permissions.country&&!!e.permissions.country[i],wrapperDisplayName:"CanAddCountry"}),p=a()({authenticatedSelector:e=>!!e.permissions&&!!e.permissions.deployment_status&&!!e.permissions.deployment_status[i],wrapperDisplayName:"CanAddDeploymentStatus"}),d=a()({authenticatedSelector:e=>!!e.permissions&&!!e.permissions.event_status&&!!e.permissions.event_status[i],wrapperDisplayName:"CanAddEventStatus"}),l=a()({authenticatedSelector:e=>!!e.permissions&&!!e.permissions.expertise&&!!e.permissions.expertise[i],wrapperDisplayName:"CanAddExpertise"}),u=a()({authenticatedSelector:e=>!!e.permissions&&!!e.permissions.institution_focal_point&&!!e.permissions.institution_focal_point[i],wrapperDisplayName:"CanAddFocalPointApproval"}),c=a()({authenticatedSelector:e=>!!e.permissions&&!!e.permissions.institution_focal_point&&!!e.permissions.institution_focal_point[i],wrapperDisplayName:"CanAddVspaceApproval"}),m=a()({authenticatedSelector:e=>!!e.permissions&&!!e.permissions.hazard&&!!e.permissions.hazard[i],wrapperDisplayName:"CanAddHazards"}),x=a()({authenticatedSelector:e=>!!e.permissions&&!!e.permissions.hazard_type&&!!e.permissions.hazard_type[i],wrapperDisplayName:"CanAddHazardTypes"}),g=a()({authenticatedSelector:e=>!!e.permissions&&!!e.permissions.institution&&!!e.permissions.institution[i],wrapperDisplayName:"CanAddOrganisationApproval"}),h=a()({authenticatedSelector:e=>!!e.permissions&&!!e.permissions.institution_network&&!!e.permissions.institution_network[i],wrapperDisplayName:"CanAddOrganisationNetworks"}),A=a()({authenticatedSelector:e=>!!e.permissions&&!!e.permissions.institution_type&&!!e.permissions.institution_type[i],wrapperDisplayName:"CanAddOrganisationTypes"}),f=a()({authenticatedSelector:e=>!!e.permissions&&!!e.permissions.operation_status&&!!e.permissions.operation_status[i],wrapperDisplayName:"CanAddOperationStatus"}),w=a()({authenticatedSelector:e=>!!e.permissions&&!!e.permissions.project_status&&!!e.permissions.project_status[i],wrapperDisplayName:"CanAddProjectStatus"}),y=a()({authenticatedSelector:e=>!!e.permissions&&!!e.permissions.region&&!!e.permissions.region[i],wrapperDisplayName:"CanAddRegions"}),S=a()({authenticatedSelector:e=>!!e.permissions&&!!e.permissions.risk_level&&!!e.permissions.risk_level[i],wrapperDisplayName:"CanAddRiskLevels"}),q=a()({authenticatedSelector:e=>!!e.permissions&&!!e.permissions.syndrome&&!!e.permissions.syndrome[i],wrapperDisplayName:"CanAddSyndromes"}),v=a()({authenticatedSelector:e=>!!e.permissions&&!!e.permissions.update_type&&!!e.permissions.update_type[i],wrapperDisplayName:"CanAddUpdateTypes"}),_=a()({authenticatedSelector:e=>!!e.permissions&&!!e.permissions.users&&!!e.permissions.users[i],wrapperDisplayName:"CanAddUsers"}),P=a()({authenticatedSelector:e=>!!e.permissions&&!!e.permissions.worl_region&&!!e.permissions.worl_region[i],wrapperDisplayName:"CanAddWorldRegion"}),k=a()({authenticatedSelector:e=>!!e.permissions&&!!e.permissions.landing_page&&!!e.permissions.landing_page[i],wrapperDisplayName:"CanAddLandingPage"}),j=a()({authenticatedSelector:e=>!!e.permissions&&!!e.permissions.operation&&!!e.permissions.operation[i]&&!!e.permissions.project&&!!e.permissions.project[i]&&!!e.permissions.event&&!!e.permissions.event[i]&&!!e.permissions.vspace&&!!e.permissions.vspace[i]&&!!e.permissions.institution&&!!e.permissions.institution[i]&&!!e.permissions.update&&!!e.permissions.update[i]||!1,wrapperDisplayName:"CanAddContent"}),C=o},50009:e=>{e.exports=require("@restart/ui/DropdownMenu")},56084:(e,r,s)=>{s.d(r,{A:()=>d});var t=s(8732);s(82015);var a=s(38609),i=s.n(a),o=s(88751),n=s(30370);function p(e){let{t:r}=(0,o.useTranslation)("common"),s={rowsPerPageText:r("Rowsperpage")},{columns:a,data:p,totalRows:d,resetPaginationToggle:l,subheader:u,subHeaderComponent:c,handlePerRowsChange:m,handlePageChange:x,rowsPerPage:g,defaultRowsPerPage:h,selectableRows:A,loading:f,pagServer:w,onSelectedRowsChange:y,clearSelectedRows:S,sortServer:q,onSort:v,persistTableHead:_,sortFunction:P,...k}=e,j={paginationComponentOptions:s,noDataComponent:r("NoData"),noHeader:!0,columns:a,data:p||[],dense:!0,paginationResetDefaultPage:l,subHeader:u,progressPending:f,subHeaderComponent:c,pagination:!0,paginationServer:w,paginationPerPage:h||10,paginationRowsPerPageOptions:g||[10,15,20,25,30],paginationTotalRows:d,onChangeRowsPerPage:m,onChangePage:x,selectableRows:A,onSelectedRowsChange:y,clearSelectedRows:S,progressComponent:(0,t.jsx)(n.A,{}),sortIcon:(0,t.jsx)("i",{className:"sort-icon fas fa-exchange-alt"}),sortServer:q,onSort:v,sortFunction:P,persistTableHead:_,className:"rki-table"};return(0,t.jsx)(i(),{...j})}p.defaultProps={subHeader:!1,pagination:!0,totalRows:null,pagServer:!0,onSelectedRowsChange:null,clearSelectedRows:!1,sortServer:!1,persistTableHead:!1};let d=p},57664:e=>{e.exports=require("dom-helpers/scrollbarSize")},58928:e=>{e.exports=require("dom-helpers/canUseDOM")},59717:e=>{e.exports=require("@restart/ui/DropdownContext")},60560:e=>{e.exports=require("dom-helpers/contains")},63033:e=>{e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},65447:e=>{e.exports=require("@restart/ui/Button")},67364:e=>{e.exports=require("@restart/hooks/useCallbackRef")},68455:e=>{e.exports=import("redux-saga")},69722:e=>{e.exports=require("es6-promise")},74075:e=>{e.exports=require("zlib")},74716:e=>{e.exports=require("moment")},74987:e=>{e.exports=require("@restart/ui/ModalManager")},78097:e=>{e.exports=require("next-redux-saga")},78634:e=>{e.exports=require("@restart/ui/Anchor")},80237:(e,r)=>{Object.defineProperty(r,"A",{enumerable:!0,get:function(){return s}});var s=function(e){return e.PAGES="PAGES",e.PAGES_API="PAGES_API",e.APP_PAGE="APP_PAGE",e.APP_ROUTE="APP_ROUTE",e.IMAGE="IMAGE",e}({})},81366:e=>{e.exports=require("redux-auth-wrapper/connectedAuthWrapper")},81413:(e,r)=>{Object.defineProperty(r,"M",{enumerable:!0,get:function(){return function e(r,s){return s in r?r[s]:"then"in r&&"function"==typeof r.then?r.then(r=>e(r,s)):"function"==typeof r&&"default"===s?r:void 0}}})},81521:e=>{e.exports=require("dom-helpers/querySelectorAll")},81895:e=>{e.exports=require("@restart/hooks/useEventCallback")},82015:e=>{e.exports=require("react")},83616:(e,r,s)=>{s.a(e,async(e,t)=>{try{s.r(r),s.d(r,{default:()=>g});var a=s(8732),i=s(82015),o=s(19918),n=s.n(o),p=s(12403),d=s(91353),l=s(42893),u=s(56084),c=s(63487),m=s(88751),x=e([l,c]);[l,c]=x.then?(await x)():x;let g=e=>{let{t:r}=(0,m.useTranslation)("common"),[s,t]=(0,i.useState)([]),[,o]=(0,i.useState)(!1),[x,g]=(0,i.useState)(0),[h,A]=(0,i.useState)(10),[f,w]=(0,i.useState)(!1),[y,S]=(0,i.useState)({}),q={sort:{title:"asc"},limit:h,page:1,query:{}},v=[{name:r("adminsetting.areaofwork.Table.Title"),selector:"title"},{name:r("adminsetting.areaofwork.Table.Action"),selector:"",cell:e=>(0,a.jsxs)("div",{children:[(0,a.jsxs)(n(),{href:"/adminsettings/[...routes]",as:`/adminsettings/edit_area_of_work/${e._id}`,children:[" ",(0,a.jsx)("i",{className:"icon fas fa-edit"})]}),"\xa0",(0,a.jsx)("a",{onClick:()=>k(e),children:(0,a.jsx)("i",{className:"icon fas fa-trash-alt"})})," "]})}],_=async e=>{o(!0);let r=await c.A.get("/areaofwork",e);r&&r.data&&r.data.length>0&&(t(r.data),g(r.totalCount),o(!1))},P=async(e,r)=>{q.limit=e,q.page=r,o(!0);let s=await c.A.get("/areaofwork",q);s&&s.data&&s.data.length>0&&(t(s.data),A(e),o(!1))},k=async e=>{S(e._id),w(!0)},j=async()=>{try{await c.A.remove(`/areaofwork/${y}`),_(q),w(!1),l.default.success(r("adminsetting.areaofwork.Table.areaOfWorkDeletedSuccessfully"))}catch(e){l.default.error(r("adminsetting.areaofwork.Table.errorDeletingAreaOfWork"))}},C=()=>w(!1);return(0,i.useEffect)(()=>{_(q)},[]),(0,a.jsxs)("div",{children:[(0,a.jsxs)(p.A,{show:f,onHide:C,children:[(0,a.jsx)(p.A.Header,{closeButton:!0,children:(0,a.jsx)(p.A.Title,{children:r("adminsetting.areaofwork.Table.DeleteAreaofwork")})}),(0,a.jsx)(p.A.Body,{children:r("adminsetting.areaofwork.Table.Areyousurewanttodeletethisareaofwork?")}),(0,a.jsxs)(p.A.Footer,{children:[(0,a.jsx)(d.A,{variant:"secondary",onClick:C,children:r("adminsetting.areaofwork.Table.Cancel")}),(0,a.jsx)(d.A,{variant:"primary",onClick:j,children:r("adminsetting.areaofwork.Table.Yes")})]})]}),(0,a.jsx)(u.A,{columns:v,data:s,totalRows:x,pagServer:!0,handlePerRowsChange:P,handlePageChange:e=>{q.limit=h,q.page=e,_(q)}})]})};t()}catch(e){t(e)}})},86842:e=>{e.exports=require("@restart/ui/SelectableContext")},87571:e=>{e.exports=require("dom-helpers/transitionEnd")},88751:e=>{e.exports=require("next-i18next")},93787:e=>{e.exports=require("redux-persist")},94947:e=>{e.exports=require("@restart/hooks/useTimeout")},96196:e=>{e.exports=require("next-redux-wrapper")},98320:e=>{e.exports=require("@restart/hooks/useIsomorphicEffect")},99460:e=>{e.exports=require("dom-helpers/css")}};var r=require("../../webpack-runtime.js");r.C(e);var s=e=>r(r.s=e),t=r.X(0,[6089,9216,9616,2386],()=>s(23742));module.exports=t})();