(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[1101],{11112:(e,n,s)=>{(window.__NEXT_P=window.__NEXT_P||[]).push(["/institution/ReadMoreModal",function(){return s(57116)}])},57116:(e,n,s)=>{"use strict";s.r(n),s.d(n,{default:()=>l});var d=s(37876),t=s(14232),r=s(31195),i=s(60282),a=s(31753);let l=e=>{let{description:n}=e,[s,l]=(0,t.useState)(!1),{t:o}=(0,a.Bd)("common");return(0,d.jsxs)(d.Fragment,{children:[(0,d.jsxs)(r.A,{show:s,onHide:()=>l(!s),backdrop:"static",keyboard:!1,children:[(0,d.jsx)(r<PERSON><PERSON><PERSON>,{closeButton:!0,children:(0,d.jsx)(r.A.Title,{children:o("Description")})}),(0,d.jsx)(r.A.Body,{children:(0,d.jsx)("div",{className:"_readmore_d",dangerouslySetInnerHTML:{__html:void 0==n?"":n}})})]}),n&&n.length<130?(0,d.jsx)("div",{dangerouslySetInnerHTML:{__html:void 0==n?"":n}}):""!=n?(0,d.jsxs)("div",{children:[(0,d.jsx)("div",{className:"_tabelw",dangerouslySetInnerHTML:{__html:void 0==n?"":n.substring(0,130)+"".concat(n.includes("<p")?"...":"").concat("")}}),(0,d.jsx)("div",{className:"pt-3",children:(0,d.jsx)(i.A,{onClick:()=>l(!s),className:"readMoreBtn mb-3",variant:"outline-light",children:o("ReadMore")})})]}):""]})}}},e=>{var n=n=>e(e.s=n);e.O(0,[636,6593,8792],()=>n(11112)),_N_E=e.O()}]);
//# sourceMappingURL=ReadMoreModal-60c934bf44abb7d1.js.map