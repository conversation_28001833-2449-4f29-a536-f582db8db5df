(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[8384],{35611:(e,s,r)=>{"use strict";r.d(s,{sx:()=>d,s3:()=>t.s3,ks:()=>t.ks,yk:()=>a.A});var a=r(54773),t=r(59200),n=r(37876),i=r(14232),l=r(39593),o=r(29504);let d={RadioGroup:e=>{let{name:s,valueSelected:r,onChange:a,errorMessage:t,children:o}=e,{errors:d,touched:c}=(0,l.j7)(),u=c[s]&&d[s];i.useMemo(()=>({name:s}),[s]);let m=i.Children.map(o,e=>i.isValidElement(e)&&function(e){return"object"==typeof e&&null!==e}(e.props)?i.cloneElement(e,{name:s,...e.props}):e);return(0,n.jsxs)("div",{children:[(0,n.jsx)("div",{className:"radio-group",children:m}),u&&(0,n.jsx)("div",{className:"invalid-feedback d-block",children:t||("string"==typeof d[s]?d[s]:String(d[s]))})]})},RadioItem:e=>{let{id:s,label:r,value:a,name:t,disabled:i}=e,{values:d,setFieldValue:c}=(0,l.j7)(),u=t||s;return(0,n.jsx)(o.A.Check,{type:"radio",id:s,label:r,value:a,name:u,checked:d[u]===a,onChange:e=>{c(u,e.target.value)},disabled:i,inline:!0})}};a.A,t.ks,t.s3},48268:(e,s,r)=>{"use strict";r.d(s,{w:()=>t});let a=e=>{let s,r=localStorage.getItem(e);try{null!==r&&(s=JSON.parse(r))}catch(e){}return s},t=()=>{let e,s=a("persist:root");try{e=JSON.parse(s.user)}catch(e){}return e}},49516:(e,s,r)=>{(window.__NEXT_P=window.__NEXT_P||[]).push(["/reset-password/[passwordToken]",function(){return r(70519)}])},54773:(e,s,r)=>{"use strict";r.d(s,{A:()=>o});var a=r(37876),t=r(14232),n=r(39593),i=r(91408);let l=(0,t.forwardRef)((e,s)=>{let{children:r,onSubmit:t,autoComplete:l,className:o,onKeyPress:d,initialValues:c,...u}=e,m=i.Ik().shape({});return(0,a.jsx)(n.l1,{initialValues:c||{},validationSchema:m,onSubmit:(e,s)=>{let r={preventDefault:()=>{},stopPropagation:()=>{},currentTarget:null,target:null,nativeEvent:new Event("submit"),bubbles:!1,cancelable:!0,defaultPrevented:!1,eventPhase:0,isTrusted:!1,timeStamp:Date.now(),type:"submit",isDefaultPrevented:()=>!1,isPropagationStopped:()=>!1,persist:()=>{}};t&&t(r,e,s)},...u,children:e=>(0,a.jsx)(n.lV,{ref:s,onSubmit:e.handleSubmit,autoComplete:l,className:o,onKeyPress:d,children:"function"==typeof r?r(e):r})})});l.displayName="ValidationFormWrapper";let o=l},59200:(e,s,r)=>{"use strict";r.d(s,{ks:()=>i,s3:()=>l});var a=r(37876);r(14232);var t=r(29504),n=r(39593);let i=e=>{let{name:s,id:r,required:i,validator:l,errorMessage:o,onChange:d,value:c,as:u,multiline:m,rows:h,pattern:p,...v}=e;return(0,a.jsx)(n.D0,{name:s,validate:e=>{let s="string"==typeof e?e:String(e||"");return i&&(!e||""===s.trim())?(null==o?void 0:o.validator)||"This field is required":l&&!l(e)?(null==o?void 0:o.validator)||"Invalid value":p&&e&&!new RegExp(p).test(e)?(null==o?void 0:o.pattern)||"Invalid format":void 0},children:e=>{let{field:s,meta:n}=e;return(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(t.A.Control,{...s,...v,id:r,as:u||"input",rows:h,isInvalid:n.touched&&!!n.error,onChange:e=>{s.onChange(e),d&&d(e)},value:void 0!==c?c:s.value}),n.touched&&n.error?(0,a.jsx)(t.A.Control.Feedback,{type:"invalid",children:n.error}):null]})}})},l=e=>{let{name:s,id:r,required:i,errorMessage:l,onChange:o,value:d,children:c,...u}=e;return(0,a.jsx)(n.D0,{name:s,validate:e=>{if(i&&(!e||""===e))return(null==l?void 0:l.validator)||"This field is required"},children:e=>{let{field:s,meta:n}=e;return(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(t.A.Control,{as:"select",...s,...u,id:r,isInvalid:n.touched&&!!n.error,onChange:e=>{s.onChange(e),o&&o(e)},value:void 0!==d?d:s.value,children:c}),n.touched&&n.error?(0,a.jsx)(t.A.Control.Feedback,{type:"invalid",children:n.error}):null]})}})}},70519:(e,s,r)=>{"use strict";r.r(s),r.d(s,{default:()=>j});var a=r(37876),t=r(89099),n=r.n(t),i=r(31777),l=r(14232),o=r(35611),d=r(54773),c=r(97685),u=r(48230),m=r.n(u),h=r(11041),p=r(21772),v=r(53718),g=r(48268);let x={"RESET_PASSWORD.CHANGE_PASSWORD_ERROR":"Unable to reset password. Contact Administrator","RESET_PASSWORD.WRONG_CURRENT_PASSWORD":"Wrong Current password","RESET_PASSWORD.PASSWORD_CHANGED":"Password Changed Successfully.","RESET_PASSWORD.NO_TOKEN":"Invalid Token. Please reset password and try again","RESET_PASSWORD.EXPIRED_TOKEN":"Token Expired. Please reset password again","RESET_PASSWORD.EXPIRY_RESET_HOURS_NOT_SET":"Unable to reset password. Contact Administrator"},j=(0,i.Ng)(e=>e)(e=>{let{passwordToken:s}=(0,t.useRouter)().query,r=(0,l.useRef)(null),[i,u]=(0,l.useState)(""),[j,w]=(0,l.useState)(""),[f,S]=(0,l.useState)(!1),N=e=>{"password"===e.target.name&&u(e.target.value),"confirmPassword"===e.target.name&&w(e.target.value)},E=async(e,r)=>{if(e.preventDefault(),r.password===r.confirmPassword){let e=await v.A.post("/email/reset-password",{newPasswordToken:s,newPassword:j});e&&e.success?(c.Ay.success(x[e.message]),n().push("/login")):e.data&&e.data.message?c.Ay.error(x[e.data.message]):c.Ay.error(x[e.message])}};return(0,l.useEffect)(()=>{let e=(0,g.w)();e&&e.username&&S(!0)},[]),(0,a.jsx)("div",{className:"loginContainer ",children:(0,a.jsx)("div",{className:"section",children:(0,a.jsx)("div",{className:"container",children:(0,a.jsx)("div",{className:"columns",children:(0,a.jsx)("div",{className:"column  is-two-thirds",children:(0,a.jsxs)("div",{className:"column reset-password",children:[(0,a.jsx)("div",{className:"imgBanner",children:(0,a.jsx)("img",{src:"/images/login-banner.jpg",alt:"RKI Login Banner Image"})}),(0,a.jsxs)(d.A,{className:"formContainer",onSubmit:E,ref:r,children:[(0,a.jsx)("div",{className:"logoContainer",children:(0,a.jsx)(m(),{href:"/",children:(0,a.jsx)("img",{src:"/images/logo.jpg",alt:"Rohert Koch Institut - Logo"})})}),(0,a.jsx)("section",{className:"fieldsContainer",children:f?(0,a.jsx)("div",{children:(0,a.jsxs)("div",{className:"d-flex flex-column justify-content-center align-items-center",children:[(0,a.jsx)(p.g,{icon:h.tUE,color:"#e8ba0d",size:"5x",className:"error-icon"}),(0,a.jsx)("p",{children:"Logged in user cannot use reset password"}),(0,a.jsx)(m(),{href:"/",as:"/",children:(0,a.jsxs)("button",{className:"button is-primary",children:[(0,a.jsx)(p.g,{icon:h._sz,color:"#ffff",size:"1x"})," Back to RKI Dashboard"]})})]})}):(0,a.jsx)(a.Fragment,{children:(0,a.jsxs)("div",{children:[(0,a.jsxs)("div",{className:"mb-3",children:[(0,a.jsx)("label",{htmlFor:"password",children:"Password"}),(0,a.jsx)(o.ks,{name:"password",id:"password",type:"password",required:!0,pattern:"(?=.*[A-Z]).{8,}",errorMessage:{required:"Password is required",pattern:"Password should be at least 8 characters and contains at least one upper case letter"},value:i,onChange:N})]}),(0,a.jsxs)("div",{className:"mb-3",children:[(0,a.jsx)("label",{htmlFor:"confirmPassword",children:"Confirm Password"}),(0,a.jsx)(o.ks,{name:"confirmPassword",id:"confirmPassword",type:"password",required:!0,validator:e=>e&&e===i,errorMessage:{required:"Confirm password is required",validator:"Password does not match"},value:j,onChange:N})]}),(0,a.jsx)("div",{className:"field is-grouped",children:(0,a.jsx)("div",{className:"control",children:(0,a.jsx)("button",{className:"button is-primary",type:"submit",children:"Reset Password"})})})]})})})]})]})})})})})})})}},e=>{var s=s=>e(e.s=s);e.O(0,[7725,1772,636,6593,8792],()=>s(49516)),_N_E=e.O()}]);
//# sourceMappingURL=[passwordToken]-e62ec4e427254bcf.js.map