"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[4631],{74631:(e,s,a)=>{a.r(s),a.d(s,{default:()=>y});var r=a(37876),i=a(14232),n=a(49589),l=a(29335),t=a(56970),o=a(37784),d=a(29504),m=a(60282),u=a(54773),c=a(35611),h=a(89099),g=a.n(h),x=a(71321),j=a.n(x),p=a(67814),A=a(48230),f=a.n(A),b=a(97685),v=a(53718),_=a(31753);let w={username:"",email:"",roles:"",password:"",confirm_password:"",institution:"",region:[],country:"",mobile_number:"",dial_code:"",enabled:"",firstname:"",lastname:"",position:""},y=e=>{let{t:s,i18n:a}=(0,_.Bd)("common"),[x,A]=(0,i.useState)(w),[y,N]=(0,i.useState)([]),[C,S]=(0,i.useState)([]),[L,P]=(0,i.useState)([]),[E,R]=(0,i.useState)([]),[q,G]=(0,i.useState)(null),M=(0,h.useRouter)().query.routes||[],I="de"===a.language?{title_de:"asc"}:{title:"asc"},k=a.language,[T,U]=(0,i.useState)(null),O={query:{},sort:I,limit:"~",languageCode:k},F=async e=>{var s;let a=[{label:"Authenticated",value:"AUTHENTICATED"},{label:"Super Admin",value:"SUPER_ADMIN"},{label:"Platform Admins at Institutions",value:"PLATFORM_ADMIN"},{label:"NGO’s",value:"NGOS"},{label:"EMT User",value:"EMT"},{label:"INIG Stakeholder",value:"INIG_STAKEHOLDER"},{label:"Health Professional",value:"HEALTH_PROFESSIONAL"},{label:"General User",value:"GENERAL_USER"},{label:"EMT National Focal Point",value:"EMT_NATIONAL_FOCALPOINT"}];a=a.sort((e,s)=>e.label.localeCompare(s.label)),N((null==(s=e.roles)?void 0:s.includes("SUPER_ADMIN"))?a:a.filter(e=>"SUPER_ADMIN"!=e.value))},D=e=>{G(e),A(s=>({...s,username:e.username,firstname:e.firstname,lastname:e.lastname,position:e.position,email:e.email,roles:e.roles,institution:e.institution?e.institution._id:"",country:e.country?e.country._id:"",region:e.region?e.region.map((e,s)=>({label:e.title,value:e._id})):[],mobile_number:e.mobile_number?e.mobile_number:"",dial_code:e.dial_code?e.dial_code:"",enabled:e.enabled?"true":"false"}))},H=async()=>{if(M[1]){let e=await v.A.get("users/".concat(M[1]),O);e&&e._id&&D(e),Z(e.country&&e.country._id)}},$=async()=>{let e=await v.A.get("institution",O);e&&e.data&&e.data.length>0&&S(e.data)},z=async()=>{let e=await v.A.get("/country",O);e&&e.data&&e.data.length>0&&R(e.data)};(0,i.useEffect)(()=>{B(),H(),$(),z()},[]);let B=async()=>{let e=await v.A.post("/users/getLoggedUser",{});e&&e.username&&(U(e),F(e))},K=(0,i.useRef)(null),Y=e=>{A(s=>({...s,...e}))},Z=async e=>{let s=[];if(e){let a=await v.A.get("/country_region/".concat(e),O);a&&a.data&&(s=a.data.map((e,s)=>({label:e.title,value:e._id}))).sort((e,s)=>e.label.localeCompare(s.label))}P(s)},V=e=>{let{name:s,value:a}=e.currentTarget;A(e=>({...e,[s]:a})),"country"===s&&(Z(a),Y({region:[]}))},J=async(e,a)=>{e&&"username or email already exists"===e||e&&403===e.status?(b.Ay.error(s("adminsetting.user.form.usernameoremailalreadyexists")),window.scrollTo(0,0)):(b.Ay.success(s(a)),g().push("/adminsettings/users"))},Q=async(e,s)=>{var a,r,i;let n,l;e.preventDefault();let t=s||x,o={username:null==(a=t.username)?void 0:a.toLowerCase().trim(),firstname:null==(r=t.firstname)?void 0:r.trim(),lastname:t.lastname,position:t.position,email:null==(i=t.email)?void 0:i.toLowerCase(),roles:t.roles,institution:t.institution?t.institution:null,region:t.region?t.region.map((e,s)=>e.value):[],country:t.country?t.country:null,mobile_number:t.mobile_number?t.mobile_number:null,dial_code:t.dial_code?t.dial_code:null,enabled:"true"===t.enabled};q&&q._id?(""!==t.password&&(o.password=t.password),l="adminsetting.user.form.Updatedausersuccessfully",n=await v.A.patch("/users/".concat(q._id),o)):(l="adminsetting.user.form.Addedausersuccessfully",o.password=t.password,n=await v.A.post("/users",o)),J(n,l)},W=e=>e===x.password;return(0,r.jsx)(n.A,{className:"formCard",fluid:!0,children:(0,r.jsx)(l.A,{children:(0,r.jsx)(u.A,{onSubmit:Q,ref:K,initialValues:x,enableReinitialize:!0,autoComplete:"off",children:(0,r.jsxs)(l.A.Body,{children:[(0,r.jsx)(t.A,{children:(0,r.jsx)(o.A,{children:(0,r.jsx)(l.A.Title,{children:q&&q._id?s("adminsetting.user.form.EditUser"):s("adminsetting.user.form.CreateUser")})})}),(0,r.jsx)("hr",{}),(0,r.jsx)(t.A,{className:"mb-3",children:(0,r.jsx)(o.A,{children:(0,r.jsxs)(d.A.Group,{children:[(0,r.jsx)(d.A.Label,{className:"required-field",children:s("adminsetting.user.form.Username")}),(0,r.jsx)(c.ks,{name:"username",id:"username",required:!0,value:x.username,validator:e=>""!==String(e||"").trim(),errorMessage:{validator:s("adminsetting.user.form.Youdon'thaveaUsername?")},onChange:V,autoComplete:"off"})]})})}),(0,r.jsxs)(t.A,{className:"mb-3",children:[(0,r.jsxs)(d.A.Group,{as:o.A,children:[(0,r.jsx)(d.A.Label,{className:"required-field",children:s("adminsetting.user.form.Firstname")}),(0,r.jsx)(c.ks,{name:"firstname",required:!0,validator:e=>""!==String(e||"").trim(),errorMessage:{validator:s("adminsetting.user.form.Pleaseenterafirstname")},id:"firstname",value:x.firstname,onChange:V})]}),(0,r.jsxs)(d.A.Group,{as:o.A,children:[(0,r.jsx)(d.A.Label,{children:s("adminsetting.user.form.Lastname")}),(0,r.jsx)(c.ks,{name:"lastname",id:"lastname",value:x.lastname,onChange:V})]})]}),(0,r.jsx)(t.A,{className:"mb-3",children:(0,r.jsx)(o.A,{children:(0,r.jsxs)(d.A.Group,{children:[(0,r.jsx)(d.A.Label,{children:s("adminsetting.user.form.Position")}),(0,r.jsx)(c.ks,{name:"position",id:"position",value:x.position,onChange:V})]})})}),(0,r.jsx)(t.A,{className:"mb-3",children:(0,r.jsx)(o.A,{children:(0,r.jsxs)(d.A.Group,{children:[(0,r.jsx)(d.A.Label,{className:"required-field",children:s("adminsetting.user.form.Email")}),(0,r.jsx)(c.ks,{name:"email",id:"email",type:"email",validator:j().isEmail,required:!0,errorMessage:{validator:s("adminsetting.user.form.Pleaseenteravalidemail")},value:x.email,onChange:V})]})})}),(0,r.jsx)(t.A,{className:"mb-3",children:(0,r.jsx)(o.A,{children:(0,r.jsxs)(d.A.Group,{children:[(0,r.jsx)(d.A.Label,{className:"required-field",children:s("adminsetting.user.form.Role")}),(0,r.jsxs)(c.s3,{required:!0,name:"roles",value:x.roles,errorMessage:s("adminsetting.user.form.PleaseselectaRole"),onChange:V,children:[(0,r.jsx)("option",{value:"",children:s("adminsetting.user.form.SelectRole")}),y.map((e,s)=>(0,r.jsx)("option",{value:e.value,children:e.label},s))]})]})})}),(0,r.jsx)(t.A,{className:"mb-3",children:(0,r.jsx)(o.A,{children:(0,r.jsxs)(d.A.Group,{children:[(0,r.jsx)(d.A.Label,{children:s("adminsetting.user.form.Organisation")}),(0,r.jsxs)(c.s3,{name:"institution",value:x.institution,errorMessage:s("adminsetting.user.form.PleaseselectaOrganisation."),onChange:V,children:[(0,r.jsx)("option",{value:"",children:s("adminsetting.user.form.SelectOrganisation")}),C.map((e,s)=>(0,r.jsx)("option",{value:e._id,children:e.title},s))]})]})})}),(0,r.jsx)(t.A,{className:"mb-3",children:(0,r.jsx)(o.A,{children:(0,r.jsxs)(d.A.Group,{children:[(0,r.jsx)(d.A.Label,{children:s("adminsetting.user.form.Country")}),(0,r.jsxs)(c.s3,{name:"country",id:"country",value:x.country,onChange:V,children:[(0,r.jsx)("option",{value:"",children:s("adminsetting.user.form.SelectCountry")}),E.map((e,s)=>(0,r.jsx)("option",{value:e._id,children:e.title},e._id))]})]})})}),(0,r.jsx)(t.A,{className:"mb-3",children:(0,r.jsx)(o.A,{children:(0,r.jsxs)(d.A.Group,{children:[(0,r.jsx)(d.A.Label,{children:s("adminsetting.user.form.Region")}),(0,r.jsx)(p.KF,{overrideStrings:{selectSomeItems:s("SelectRegions"),allItemsAreSelected:s("adminsetting.user.form.AllRegionsareSelected")},options:L,value:x.region,onChange:e=>{A(s=>({...s,region:e}))},className:"region",labelledBy:s("SelectRegions")})]})})}),(0,r.jsxs)(t.A,{className:"mb-3",children:[(0,r.jsx)(o.A,{xs:6,children:(0,r.jsxs)(d.A.Group,{children:[(0,r.jsx)(d.A.Label,{children:s("adminsetting.user.form.PhoneNumber")}),(0,r.jsxs)(c.s3,{name:"dial_code",id:"dial_code",type:"number",value:x.dial_code,onChange:V,children:[(0,r.jsx)("option",{value:"",children:s("adminsetting.user.form.DialCode")}),E.map((e,s)=>(0,r.jsx)("option",{value:e.dial_code,children:"(".concat(e.dial_code,") ").concat(e.title)},e._id))]})]})}),(0,r.jsx)(o.A,{children:(0,r.jsxs)(d.A.Group,{children:[(0,r.jsx)(d.A.Label,{children:"\xa0"}),(0,r.jsx)(c.ks,{type:"number",name:"mobile_number",id:"mobile_number",value:x.mobile_number,onChange:V})]})})]}),(0,r.jsx)(t.A,{className:"mb-3",children:(0,r.jsx)(o.A,{children:(0,r.jsx)(d.A.Group,{children:q&&q._id?(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(d.A.Label,{children:s("adminsetting.user.form.Password")}),(0,r.jsx)(c.ks,{name:"password",id:"password",type:"password",pattern:"^(?=.*?[A-Z])(?=.*?[a-z])(?=.*?[0-9])(?=.*?[#?!@$%^&*-]).{8,}$",errorMessage:{pattern:s("adminsetting.user.form.Passwordshouldcontainatleast8characters,withatleastonedigit,oneletterinuppercase&onespecialcharacter")},value:x.password,onChange:V})]}):(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(d.A.Label,{className:"required-field",children:s("adminsetting.user.form.Password")}),(0,r.jsx)(c.ks,{name:"password",id:"password",type:"password",required:!0,pattern:"^(?=.*?[A-Z])(?=.*?[a-z])(?=.*?[0-9])(?=.*?[#?!@$%^&*-]).{8,}$",errorMessage:{required:s("adminsetting.user.form.Passwordisrequired"),pattern:s("adminsetting.user.form.Passwordshouldcontainatleast8characters,withatleastonedigit,oneletterinuppercase&onespecialcharacter")},value:x.password,onChange:V})]})})})}),(0,r.jsx)(t.A,{className:"mb-3",children:(0,r.jsx)(o.A,{children:(0,r.jsx)(d.A.Group,{children:q&&q._id?(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(d.A.Label,{children:s("adminsetting.user.form.ConfirmPassword")}),(0,r.jsx)(c.ks,{name:"confirm_password",id:"confirm_password",type:"password",validator:W,errorMessage:{required:s("adminsetting.user.form.Confirmpasswordisrequired"),validator:s("adminsetting.user.form.Passworddoesnotmatch")},value:x.confirm_password,onChange:V})]}):(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(d.A.Label,{className:"required-field",children:s("adminsetting.user.form.ConfirmPassword")}),(0,r.jsx)(c.ks,{name:"confirm_password",id:"confirm_password",type:"password",required:!0,validator:W,errorMessage:{required:s("adminsetting.user.form.Confirmpasswordisrequired"),validator:s("adminsetting.user.form.Passworddoesnotmatch")},value:x.confirm_password,onChange:V})]})})})}),(0,r.jsx)(t.A,{className:"mb-3",children:(0,r.jsx)(o.A,{children:(0,r.jsxs)(d.A.Group,{children:[(0,r.jsx)("label",{children:s("adminsetting.user.form.Enabled")}),(0,r.jsxs)(c.sx.RadioGroup,{name:"enabled",errorMessage:s("adminsetting.user.form.Itisrequired"),valueSelected:x.enabled,onChange:V,children:[(0,r.jsx)(c.sx.RadioItem,{id:"yes",label:"Yes",value:"true"}),(0,r.jsx)(c.sx.RadioItem,{id:"no",label:"No",value:"false"})]})]})})}),(0,r.jsx)(t.A,{className:"my-4",children:(0,r.jsxs)(o.A,{children:[(0,r.jsx)(m.A,{className:"me-2",type:"submit",variant:"primary",children:s("adminsetting.user.form.Submit")}),(0,r.jsx)(m.A,{className:"me-2",onClick:()=>{A(w),window.scrollTo(0,0)},variant:"info",children:s("adminsetting.user.form.Reset")}),(0,r.jsx)(f(),{href:"/adminsettings/[...routes]",as:"/adminsettings/users",children:(0,r.jsx)(m.A,{variant:"secondary",children:s("adminsetting.user.form.Cancel")})})]})})]})})})})}}}]);
//# sourceMappingURL=4631-3f26c77c2eb2b5e2.js.map