(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[227],{29494:(e,a,s)=>{"use strict";s.r(a),s.d(a,{default:()=>u});var r=s(37876),t=s(49589),n=s(56970),c=s(53538),l=s(29504),d=s(21772),o=s(11041),i=s(31753);let u=e=>{let{filterText:a,onFilter:s}=e,{t:u}=(0,i.Bd)("common");return(0,r.jsx)(t.A,{fluid:!0,className:"p-0",children:(0,r.jsx)(n.A,{children:(0,r.jsxs)(c.A,{children:[(0,r.jsx)(l.A.Control,{className:"rounded",type:"text",placeholder:u("SearchHazards"),value:a,onChange:s}),(0,r.jsx)("div",{className:"search-icon",children:(0,r.jsx)(d.g,{icon:o.MjD})})]})})})}},53538:(e,a,s)=>{"use strict";s.d(a,{A:()=>h});var r=s(15039),t=s.n(r),n=s(14232),c=s(77346),l=s(40856),d=s(20348),o=s(37876);let i=n.forwardRef((e,a)=>{let{className:s,bsPrefix:r,as:n="span",...l}=e;return r=(0,c.oU)(r,"input-group-text"),(0,o.jsx)(n,{ref:a,className:t()(s,r),...l})});i.displayName="InputGroupText";let u=n.forwardRef((e,a)=>{let{bsPrefix:s,size:r,hasValidation:l,className:i,as:u="div",...h}=e;s=(0,c.oU)(s,"input-group");let p=(0,n.useMemo)(()=>({}),[]);return(0,o.jsx)(d.A.Provider,{value:p,children:(0,o.jsx)(u,{ref:a,...h,className:t()(i,s,r&&"".concat(s,"-").concat(r),l&&"has-validation")})})});u.displayName="InputGroup";let h=Object.assign(u,{Text:i,Radio:e=>(0,o.jsx)(i,{children:(0,o.jsx)(l.A,{type:"radio",...e})}),Checkbox:e=>(0,o.jsx)(i,{children:(0,o.jsx)(l.A,{type:"checkbox",...e})})})},64092:(e,a,s)=>{(window.__NEXT_P=window.__NEXT_P||[]).push(["/hazard/HazardSearch",function(){return s(29494)}])}},e=>{var a=a=>e(e.s=a);e.O(0,[7725,1772,636,6593,8792],()=>a(64092)),_N_E=e.O()}]);
//# sourceMappingURL=HazardSearch-a925b94af77be387.js.map