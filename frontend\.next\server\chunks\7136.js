exports.id=7136,exports.ids=[7136],exports.modules={30015:(e,s,a)=>{"use strict";a.a(e,async(e,t)=>{try{a.d(s,{A:()=>c});var i=a(8732),n=a(82053),l=a(54131),r=e([l]);l=(r.then?(await r)():r)[0];let c=e=>{let s={pdf:{icon:l.faFilePdf,color:"#FF0000"},docx:{icon:l.faFile,color:"#87CEFA"},doc:{icon:l.faFile,color:"#87CEFA"},xlsx:{icon:l.faFileExcel,color:"#1B4D3E"},xls:{icon:l.faFileExcel,color:"#1B4D3E"},csv:{icon:l.faFileExcel,color:"#1B4D3E"}}[e.extension.replace("."," ").trim()];return(0,i.jsx)("a",{href:`http://localhost:3001/api/v1/files/download/${e._id}`,target:"_blank",children:(0,i.jsx)(n.FontAwesomeIcon,{icon:s.icon,color:s.color})})};t()}catch(e){t(e)}})},42447:(e,s,a)=>{"use strict";a.a(e,async(e,t)=>{try{a.d(s,{A:()=>h});var i=a(8732),n=a(82015),l=a(81149),r=a(82053),c=a(54131),o=a(91353),d=a(88751);a(72025);var p=e([c]);c=(p.then?(await p)():p)[0];let h=e=>{let{t:s}=(0,d.useTranslation)("common"),[a,t]=(0,n.useState)([]),p=e=>{let a=/(http|https):\/\/(\w+:{0,1}\w*)?(\S+)(:[0-9]+)?(\/|\/([\w#!:.?+=&%!\-\/]))?/.test(e.description);return(0,i.jsxs)("div",{className:"carousel-legend",children:[(0,i.jsxs)("p",{className:"lead",children:[(0,i.jsx)("b",{children:s("Filename")})," ",e.originalName||"No Name found"]}),e.description&&(0,i.jsxs)("div",{className:"source_link",children:[(0,i.jsx)("p",{children:(0,i.jsx)("b",{children:s("Source")})}),a?(0,i.jsxs)("div",{children:[(0,i.jsx)(r.FontAwesomeIcon,{icon:c.faLink,size:"1x",color:"#999",className:"me-1"}),(0,i.jsx)("a",{className:"source_link",href:e.description,target:"_blank",rel:"noopener noreferrer",children:e.description})]}):(0,i.jsx)("div",{children:(0,i.jsx)("p",{className:"ps-0 py-0",style:{wordBreak:"break-all"},children:e.description})})]}),e.downloadLink&&(0,i.jsxs)(o.A,{className:"btn btn-success mt-2 btn--download",href:e.downloadLink,children:[s("Download"),(0,i.jsx)(r.FontAwesomeIcon,{icon:c.faDownload,size:"1x",className:"ms-1"})]})]})};return(0,n.useEffect)(()=>{let s=[];e&&e.gallery&&Array.isArray(e.gallery)&&e.gallery.map((a,t)=>{let i,n=a&&a.name.split(".").pop();switch(n){case"JPG":case"jpg":case"jpeg":case"png":i=`http://localhost:3001/api/v1/image/show/${a._id}`;break;case"pdf":i="/images/fileIcons/pdfFile.png";break;case"docx":i="/images/fileIcons/wordFile.png";break;case"xls":case"xlsx":i="/images/fileIcons/xlsFile.png";break;default:i="/images/fileIcons/otherFile.png"}let l=("docx"===n||"pdf"===n||"xls"===n||"xlsx"===n)&&`http://localhost:3001/api/v1/files/download/${a._id}`,r=`${a&&a.original_name?a.original_name:"No Name found"}`,c=e.imageSource&&Array.isArray(e.imageSource)&&e.imageSource.length>0?e.imageSource[t]:"";s.push({src:i,description:c,originalName:r,downloadLink:l})}),t(s)},[e]),(0,i.jsx)("div",{children:a&&0===a.length?(0,i.jsx)("div",{className:"border border-info my-3 mx-0",children:(0,i.jsx)("p",{className:"d-flex d-flex justify-content-center p-2 m-0",children:s("NoFilesFound!")})}):(0,i.jsx)(l.Carousel,{showThumbs:!0,showStatus:!0,showIndicators:!0,infiniteLoop:!0,useKeyboardArrows:!0,autoPlay:!1,stopOnHover:!0,swipeable:!0,dynamicHeight:!1,emulateTouch:!0,renderThumbs:()=>a.map((e,s)=>(0,i.jsx)("img",{src:e.src,alt:`Thumbnail ${s+1}`,style:{width:"60px",height:"60px",objectFit:"cover"}},s)),children:a.map((e,s)=>(0,i.jsxs)("div",{children:[(0,i.jsx)("img",{src:e.src,alt:e.originalName||"Gallery image",style:{maxHeight:"500px",objectFit:"contain"}}),p(e)]},s))})})};t()}catch(e){t(e)}})},53464:(e,s,a)=>{"use strict";a.a(e,async(e,t)=>{try{a.d(s,{A:()=>x});var i=a(8732);a(82015);var n=a(83551),l=a(49481),r=a(63241),c=a(81181),o=a(82053),d=a(54131),p=a(30015),h=e([d,p]);[d,p]=h.then?(await h)():h;let x=e=>{let{document:s,doc_src:a}=e;return(0,i.jsx)("div",{children:s&&s.map((e,s)=>(0,i.jsxs)(n.A,{children:[(0,i.jsx)(p.A,{...e}),(0,i.jsx)(l.A,{children:(0,i.jsxs)("div",{className:"d-flex",children:[(0,i.jsx)("a",{href:`http://localhost:3001/api/v1/files/download/${e._id}`,target:"_blank",children:(0,i.jsx)("p",{children:e.original_name})}),(0,i.jsx)(r.A,{trigger:"click",placement:"right",overlay:(0,i.jsxs)(c.A,{id:"popover-basic",children:[(0,i.jsx)(c.A.Header,{as:"h5",children:"Source"}),(0,i.jsx)(c.A.Body,{children:a&&a[s]})]}),children:(0,i.jsx)(o.FontAwesomeIcon,{className:"ms-1",icon:d.faInfoCircle,color:"#232c3d"})})]})})]},s))})};t()}catch(e){t(e)}})},62550:(e,s,a)=>{"use strict";a.a(e,async(e,t)=>{try{a.d(s,{A:()=>r});var i=a(8732);a(82015);var n=a(42447),l=e([n]);n=(l.then?(await l)():l)[0];let r=e=>{let{data:s,srcText:a}=e;return(0,i.jsx)("div",{children:(0,i.jsx)(n.A,{gallery:s,imageSource:a})})};t()}catch(e){t(e)}})},63349:(e,s,a)=>{"use strict";a.d(s,{A:()=>l});var t=a(8732),i=a(82015),n=a(88751);let l=e=>{let{t:s}=(0,n.useTranslation)("common"),a=parseInt("255"),[l,r]=(0,i.useState)(!1);return(0,t.jsxs)(t.Fragment,{children:[e.description?(0,t.jsx)("div",{dangerouslySetInnerHTML:((s,t)=>({__html:!t&&s.length>a?s.substring(0,a)+"...":e.description}))(e.description,l),className:"operationDesc"}):null,e.description&&e.description.length>a?(0,t.jsx)("button",{type:"button",className:"readMoreText",onClick:()=>r(!l),children:s(l?"readLess":"readMore")}):null]})}},69089:(e,s,a)=>{"use strict";a.a(e,async(e,t)=>{try{a.d(s,{A:()=>d});var i=a(8732);a(82015);var n=a(83551),l=a(49481),r=a(82053),c=a(54131),o=e([c]);c=(o.then?(await o)():o)[0];let d=e=>{let{link:s}=e;return(0,i.jsx)("div",{children:s&&s.map((e,s)=>(0,i.jsx)(n.A,{children:(0,i.jsx)(l.A,{className:"p-0",children:(0,i.jsxs)("div",{className:"d-flex align-items-center mb-2",children:[(0,i.jsx)(r.FontAwesomeIcon,{icon:c.faGlobe,color:"#87CEFA"}),"\xa0\xa0",(0,i.jsx)("a",{href:e.link,target:"_blank",children:(0,i.jsx)("span",{children:e.title})})]})})},s))})};t()}catch(e){t(e)}})},72025:()=>{},77136:(e,s,a)=>{"use strict";a.a(e,async(e,t)=>{try{a.d(s,{A:()=>C});var i=a(8732),n=a(82015),l=a(19918),r=a.n(l),c=a(91353),o=a(13524),d=a(6756),p=a(83551),h=a(18597),x=a(49481),m=a(74716),u=a.n(m),j=a(78219),g=a.n(j),y=a(44233),f=a.n(y),A=a(63487),_=a(63349),b=a(53464),v=a(69089),w=a(62550),N=a(99789),F=a(88751),k=e([A,b,v,w]);[A,b,v,w]=k.then?(await k)():k;let S="MM-D-YYYY HH:mm:ss",T="Calendar Event",C=e=>{let{t:s}=(0,F.useTranslation)("common"),[a,t]=(0,n.useState)({type:"",title:"",description:"",created_at:"",updated_at:"",link:[],reply:[],_id:"",update_type:"",document:[],images:[],images_src:[],contact_details:[]}),[l,m]=(0,n.useState)({enable:!1,event:"close"}),[j,y]=(0,n.useState)(""),[k,C]=(0,n.useState)(!1),$=()=>C(!1),I=async e=>{await A.A.remove(`/updates/${e._id}`),C(!1);let s=`parent_${e.type}`;f().push(`/${e.type}/show/${e[s]}`)};(0,n.useEffect)(()=>{let s=null,a=async e=>{let a=await A.A.get(`/updates/${e}`);if(a&&a.type){let e=await A.A.get(`/updateType/${a.update_type}`);s=setTimeout(()=>{y(e?e.title:""),t(a),m(e=>({...e,enable:!0}))},500)}};e&&e.routes&&"update"===e.routes[2]&&e.routes[3]&&(s&&clearTimeout(s),m({enable:!1,event:"open"}),a(e.routes[3]))},[e.routes]);let E=()=>(0,i.jsx)(r(),{href:{pathname:"/updates/[...routes]",query:{parent_type:a.type,update_type:a.update_type}},as:`/updates/edit/${a._id}?parent_type=${a.type}&update_type=${a.update_type}`,children:(0,i.jsx)("i",{className:"fas fa-pen"})}),D=()=>(0,i.jsxs)(i.Fragment,{children:[(0,i.jsx)("i",{className:"fas fa-trash-alt",onClick:()=>C(!0)}),(0,i.jsxs)(g(),{size:"sm",show:k,onHide:()=>C(!1),"aria-labelledby":"example-modal-sizes-title-sm",children:[(0,i.jsx)(g().Header,{closeButton:!0,children:(0,i.jsx)(g().Title,{id:"example-modal-sizes-title-sm",children:a.title})}),(0,i.jsxs)(g().Body,{children:[s("Areyousureyouwanttodelete")," ",a.title,"?"]}),(0,i.jsxs)(g().Footer,{children:[(0,i.jsx)(c.A,{variant:"secondary",onClick:$,children:s("Cancel")}),(0,i.jsx)(c.A,{variant:"primary",onClick:()=>I(a),children:s("OK")})]})]})]}),L=(0,N.iK)(()=>(0,i.jsx)(E,{})),B=(0,N.Lg)(()=>(0,i.jsx)(D,{}));return(0,i.jsxs)("div",{className:"updatesPopMain",children:[l.enable||"open"!==l.event?null:(0,i.jsx)(o.A,{animation:"border",variant:"primary"}),a&&a._id&&e.routes?(0,i.jsx)(d.A,{in:l.enable,children:(0,i.jsxs)("div",{className:"updatesPopupBlock",children:[(0,i.jsxs)("div",{className:"updateActions",children:[(0,i.jsx)(L,{update:a}),(0,i.jsx)(B,{update:a}),(0,i.jsx)("i",{className:"fas fa-times",onClick:e=>{m({enable:!1,event:"close"})}})]}),(0,i.jsx)(p.A,{children:(0,i.jsx)(h.A.Body,{className:"mt-4",children:(0,i.jsxs)(p.A,{className:"operationData",children:[(0,i.jsxs)(x.A,{children:[(0,i.jsx)("h3",{children:a.title}),(0,i.jsx)(_.A,{description:a.description}),(0,i.jsx)(x.A,{children:(()=>{switch(j){case"Document":case T:return(0,i.jsx)(b.A,{document:a.document,doc_src:a.images_src||[]});case"Image":return(0,i.jsx)(w.A,{data:a.images,...e,srcText:a.images_src});case"General / Notice":return(0,i.jsxs)("div",{children:[(0,i.jsx)(b.A,{document:a.document,doc_src:a.images_src||[]}),(0,i.jsx)(w.A,{data:a.images,...e,srcText:a.images_src})]});case"Link":return(0,i.jsx)(v.A,{...a});default:return null}})()})]}),(0,i.jsxs)(x.A,{lg:5,children:[(0,i.jsxs)("p",{children:[(0,i.jsx)("b",{children:s("UpdateType")}),":",(0,i.jsxs)("span",{children:[" ",j," "]})]}),"Contact"===j&&a.contact_details&&(0,i.jsxs)("p",{children:[(0,i.jsx)("b",{children:s("MobileNo")}),":",(0,i.jsxs)("span",{children:[" ",a.contact_details.mobileNo," "]})]}),"Contact"===j&&a.contact_details&&(0,i.jsxs)("p",{children:[(0,i.jsx)("b",{children:s("TelephoneNo")}),":",(0,i.jsxs)("span",{children:[" ",a.contact_details.telephoneNo," "]})]}),j===T&&a.start_date&&(0,i.jsxs)("p",{children:[(0,i.jsx)("b",{children:s("StartDate")}),":",(0,i.jsxs)("span",{children:[" ",u()(a.start_date).format(S)]})]}),j===T&&a.end_date&&(0,i.jsxs)("p",{children:[(0,i.jsx)("b",{children:s("EndDate")}),":",(0,i.jsx)("span",{children:u()(a.end_date).format(S)})]}),(0,i.jsxs)("p",{children:[(0,i.jsx)("b",{children:s("Created")}),":",(0,i.jsx)("span",{children:u()(a.created_at).format(S)})]}),(0,i.jsxs)("p",{children:[(0,i.jsx)("b",{children:s("LastModified")}),":",(0,i.jsx)("span",{children:u()(a.updated_at).format(S)})]})]})]})})})]})}):null]})};t()}catch(e){t(e)}})}};