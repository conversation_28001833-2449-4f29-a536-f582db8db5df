{"version": 3, "file": "static/chunks/pages/country/permission-7b7e95213e721c1c.js", "mappings": "gFACA,4CACA,sBACA,WACA,OAAe,EAAQ,KAA2C,CAClE,EACA,SAFsB,4FCAf,IAAMA,EAA0BC,CAAAA,EAAAA,QAAAA,CAAAA,CAAoBA,CAAC,CAC1DC,sBAAuB,KACjBC,EAAMC,WAAW,IAAID,EAAMC,WAAW,CAACC,MAAM,IAAIF,EAAMC,WAAW,CAACC,MAAM,CAAC,WAAW,CAK3FC,CAL6F,kBAKzE,yBACtB,GAAG,EAEYN,uBAAuBA,EAAC", "sources": ["webpack://_N_E/?3381", "webpack://_N_E/./pages/country/permission.tsx"], "sourcesContent": ["\n    (window.__NEXT_P = window.__NEXT_P || []).push([\n      \"/country/permission\",\n      function () {\n        return require(\"private-next-pages/country/permission.tsx\");\n      }\n    ]);\n    if(module.hot) {\n      module.hot.dispose(function () {\n        window.__NEXT_P.push([\"/country/permission\"])\n      });\n    }\n  ", "//Import Library\r\nimport React from 'react';\r\nimport connectedAuthWrapper from 'redux-auth-wrapper/connectedAuthWrapper';\r\n\r\nexport const canViewDiscussionUpdate = connectedAuthWrapper({\r\n  authenticatedSelector: (state) => {\r\n    if (state.permissions && state.permissions.update && state.permissions.update['read:any']) {\r\n      return true;\r\n    }\r\n    return false;\r\n  },\r\n  wrapperDisplayName: 'CanViewDiscussionUpdate',\r\n});\r\n\r\nexport default canViewDiscussionUpdate;"], "names": ["canViewDiscussionUpdate", "connectedAuthWrapper", "authenticatedSelector", "state", "permissions", "update", "wrapperDisplayName"], "sourceRoot": "", "ignoreList": []}