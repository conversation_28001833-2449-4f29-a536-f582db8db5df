{"version": 3, "file": "static/chunks/pages/users-3f2035400053ab65.js", "mappings": "qNA+HA,MArHA,SAAoBA,CAAW,EAC7B,GAAM,CAACC,EAAWC,EAAe,CAAGC,CAAAA,EAAAA,EAAAA,EAoHvBC,MApHuBD,CAAQA,CAAC,EAAE,EACzC,EAAGE,EAAW,CAAGF,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,EAAC,GAC1B,CAACG,EAAWC,EAAa,CAAGJ,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAC,GACrC,CAACK,EAASC,EAAW,CAAGN,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAC,IACjC,CAACO,EAAaC,EAAS,CAAGR,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,EAAC,GACnC,CAACS,EAAmBC,EAAc,CAAGV,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAM,CAAC,GAEpDW,EAAc,CAClB,KAAQ,CAAC,WAAc,MAAM,EAC7B,MAASN,EACT,KAAQ,EACR,MAAS,CAAC,CACZ,EAEMO,EAAU,CACd,CACEC,KAAM,WACNC,SAAU,WACVC,KAAOC,GAAWA,EAAEC,QAAQ,EAE9B,CACEJ,KAAM,QACNC,SAAU,QACVC,KAAM,GAAYC,EAAEE,KACtB,EACA,CACEL,KAAM,OACNC,SAAU,OACVC,KAAOC,GAAWA,EAAEG,IAAI,CAAGH,EAAEG,IAAI,CAACC,KAAK,CAAG,EAC5C,EACA,CACEP,KAAM,cACNC,SAAU,cACVC,KAAOC,GAAWA,EAAEK,WAAW,CAAGL,EAAEK,WAAW,CAACD,KAAK,CAAG,EAC1D,EACA,CACEP,KAAM,SACNC,SAAU,GACVC,KAAM,GAAY,WAACO,MAAAA,WAAI,UAACC,IAAIA,CAACC,KAAK,qBAAqBC,GAAI,eAA/BF,MAAoD,CAANP,EAAEU,GAAG,WAAI,SAAW,OAAM,UAACC,IAAAA,CAAEC,QAAS,IAAMC,EAAWb,YAAI,aACvI,EACD,CAEKc,EAAe,UACnB5B,GAAW,GACX,IAAM6B,EAAW,MAAMC,EAAAA,CAAUA,CAACC,GAAG,CAAC,SAAUtB,GAC5CoB,GAAYA,EAASG,IAAI,EAAIH,EAASG,IAAI,CAACC,MAAM,CAAG,GAAG,CACzDpC,EAAegC,EAASG,IAAI,EAC5B9B,EAAa2B,EAASK,UAAU,EAChClC,GAAW,GAEf,EAOMmC,EAAsB,MAAOC,EAAiBC,KAClD5B,EAAY6B,KAAK,CAAGF,EACpB3B,EAAY4B,IAAI,CAAGA,EACnBrC,GAAW,GACX,IAAM6B,EAAW,MAAMC,EAAAA,CAAUA,CAACC,GAAG,CAAC,SAAUtB,GAC5CoB,GAAYA,EAASG,IAAI,EAAIH,EAASG,IAAI,CAACC,MAAM,CAAG,GAAG,CACzDpC,EAAegC,EAASG,IAAI,EAC5B5B,EAAWgC,GACXpC,GAAW,GAEf,EAEAuC,CAAAA,EAAAA,EAAAA,SAAAA,CAASA,CAAC,KACRX,GACF,EAAG,EAAE,EAEL,IAAMD,EAAa,MAAOa,IACxBhC,EAAcgC,GACdlC,GAAS,EACX,EAEMmC,EAAe,UACnB,MAAMX,EAAAA,CAAUA,CAACY,MAAM,CAAC,UAAmC,OAAzBnC,EAAkB,GAAM,GAC1DqB,IACApB,EAAc,CAAC,EAFoC,CAGnDF,GAAS,EACX,EAEMqC,EAAY,IAAMrC,GAAS,GAEjC,MACE,WAACc,MAAAA,WACC,WAACwB,EAAAA,CAAKA,CAAAA,CAACC,KAAMxC,EAAayC,OAAQH,YAChC,UAACC,EAAAA,CAAKA,CAACG,MAAM,EAACC,WAAW,aACvB,UAACJ,EAAAA,CAAKA,CAACK,KAAK,WAAC,kBAEf,UAACL,EAAAA,CAAKA,CAACM,IAAI,WAAC,4CACZ,WAACN,EAAAA,CAAKA,CAACO,MAAM,YACX,UAACC,EAAAA,CAAMA,CAAAA,CAACC,QAAQ,YAAY3B,QAASiB,WAAW,WAGhD,UAACS,EAAAA,CAAMA,CAAAA,CAACC,QAAQ,UAAU3B,QAASe,WAAc,cAMrD,UAACa,EAAAA,CAAQA,CAAAA,CACP5C,QAASA,EACTsB,KAAMpC,EACNK,UAAWA,EACXsD,WAAW,EACXpB,oBAAqBA,EACrBqB,iBA3DoBnB,CA2DFmB,GA1DtB/C,EAAY6B,KAAK,CAAGnC,EACpBM,EAAY4B,IAAI,CAAGA,EACnBT,GACF,MA2DF,mBC5HA,4CACA,SACA,WACA,OAAe,EAAQ,KAAoC,CAC3D,EACA,SAFsB,oGCiCtB,SAAS0B,EAASG,CAAoB,EACpC,GAAM,GAAEC,CAAC,CAAE,CAAGC,CAAAA,EAAAA,EAAAA,EAAAA,CAAcA,CAAC,UACvBC,EAA6B,CACjCC,gBAAiBH,EAAE,cACnB,EACI,SACJhD,CAAO,MACPsB,CAAI,WACJ/B,CAAS,CACT6D,uBAAqB,WACrBC,CAAS,oBACTC,CAAkB,qBAClB7B,CAAmB,kBACnBqB,CAAgB,aAChBS,CAAW,oBACXC,CAAkB,gBAClBC,CAAc,SACdC,CAAO,WACPb,CAAS,sBACTc,CAAoB,mBACpBC,CAAiB,CACjBC,YAAU,QACVC,CAAM,kBACNC,CAAgB,cAChBC,CAAY,CAEZ,CADA,EACGC,EACJ,CAAGlB,EAGEmB,EAAiB,4BACrBhB,EACAiB,gBAAiBnB,EAAE,IAP0C,MAQ7DoB,UAAU,UACVpE,EACAsB,KAAMA,GAAQ,EAAE,CAChB+C,OAAO,EACPC,2BAA4BlB,EAC5BmB,UAAWlB,EACXmB,gBAAiBd,qBACjBJ,EACAmB,YAAY,EACZC,iBAAkB7B,EAClB8B,kBAAmBnB,GAA0C,GAC7DoB,eADwCpB,cACVD,GAA4B,CAAC,GAAI,GAAI,GAAI,CAA3BA,EAA+B,GAAG,CAC9EsB,oBAAqBtF,EACrBuF,oBAAqBrD,EACrBsD,aAAcjC,EACdW,sCACAE,oBACAC,EACAoB,kBAAmB,UAACC,EAAAA,CAAYA,CAAAA,CAAAA,GAChCC,SAAU,UAACC,IAAAA,CAAEC,UAAU,6CACvBvB,SACAC,eACAE,mBACAD,EACAqB,UAAW,WACb,EACA,MACE,UAACC,EAAAA,EAASA,CAAAA,CAAE,GAAGnB,CAAc,EAEjC,CAEAtB,EAAS0C,YAAY,CAAG,CACtBf,WAAW,EACXE,YAAY,EACZlF,UAAW,KACXsD,WAAW,EACXc,qBAAsB,KACtBC,mBAAmB,EACnBC,YAAY,EACZE,iBAAkB,EACpB,EAEA,MAAenB,QAAQA,EAAC,8KCzExB,MA9Bc,IAGV,WAAC2C,EAAAA,CAASA,CAAAA,CAACH,QA2BAI,EA3BU,GA2BLA,EAAC,kBA1Bf,UAACC,EAAAA,CAAGA,CAAAA,CAACL,UAAU,uBACb,UAACM,KAAAA,UAAG,gBAEN,UAACD,EAAAA,CAAGA,CAAAA,UACF,UAACE,EAAAA,CAAGA,CAAAA,CAACC,GAAI,YACP,UAACjF,IAAIA,CAACC,KAAK,qBAAqBC,GAAG,eAA9BF,UAA+C,UAAC+B,EAAAA,CAAMA,CAAAA,CAACC,QAAQ,YAAYkD,KAAK,cAAK,oBAI9F,UAACJ,EAAAA,CAAGA,CAAAA,CAACL,UAAU,gBACb,UAACO,EAAAA,CAAGA,CAAAA,CAACC,GAAI,YACP,UAACvG,EAAAA,OAAUA,CAAAA,CAAAA", "sources": ["webpack://_N_E/./pages/users/UsersTable.tsx", "webpack://_N_E/?f6ac", "webpack://_N_E/./components/common/RKITable.tsx", "webpack://_N_E/./pages/users/index.tsx"], "sourcesContent": ["//Import Library\r\nimport Link from \"next/link\";\r\nimport React , {useEffect, useState} from \"react\";\r\nimport { <PERSON><PERSON>, Button } from 'react-bootstrap';\r\n\r\n//Import services/components\r\nimport RKITable from '../../components/common/RKITable';\r\nimport apiService from \"../../services/apiService\";\r\n\r\n\r\nfunction UsersTable(_props: any) {\r\n  const [tabledata, setDataToTable] = useState([]);\r\n  const [, setLoading] = useState(false);\r\n  const [totalRows, setTotalRows] = useState(0);\r\n  const [perPage, setPerPage] = useState(10);\r\n  const [isModalShow, setModal] = useState(false);\r\n  const [removeUserDetails, setRemoveUser] = useState<any>({});\r\n\r\n  const usersParams = {\r\n    \"sort\": {\"created_at\": \"desc\"},\r\n    \"limit\": perPage,\r\n    \"page\": 1,\r\n    \"query\": {}\r\n  };\r\n\r\n  const columns = [\r\n    {\r\n      name: 'Username',\r\n      selector: 'username',\r\n      cell: (d: any) => d.username\r\n    },\r\n    {\r\n      name: 'Em<PERSON>',\r\n      selector: \"email\",\r\n      cell: (d: any) => d.email\r\n    },\r\n    {\r\n      name: 'Role',\r\n      selector: \"role\",\r\n      cell: (d: any) => d.role ? d.role.title : \"\"\r\n    },\r\n    {\r\n      name: 'Institution',\r\n      selector: \"institution\",\r\n      cell: (d: any) => d.institution ? d.institution.title : \"\"\r\n    },\r\n    {\r\n      name: 'Action',\r\n      selector: \"\",\r\n      cell: (d: any) => <div><Link href=\"/users/[...routes]\" as={`/users/edit/${d._id}`}>Edit</Link>&nbsp;<a onClick={() => removeUser(d)}>Delete</a></div>\r\n    }\r\n  ];\r\n\r\n  const getUsersData = async () => {\r\n    setLoading(true);\r\n    const response = await apiService.get('/users', usersParams);\r\n    if (response && response.data && response.data.length > 0) {\r\n      setDataToTable(response.data);\r\n      setTotalRows(response.totalCount);\r\n      setLoading(false);\r\n    }\r\n  };\r\n  const handlePageChange = (page: any) => {\r\n    usersParams.limit = perPage;\r\n    usersParams.page = page;\r\n    getUsersData();\r\n  };\r\n\r\n  const handlePerRowsChange = async (newPerPage: any, page: any) => {\r\n    usersParams.limit = newPerPage;\r\n    usersParams.page = page;\r\n    setLoading(true);\r\n    const response = await apiService.get('/users', usersParams);\r\n    if (response && response.data && response.data.length > 0) {\r\n      setDataToTable(response.data);\r\n      setPerPage(newPerPage);\r\n      setLoading(false);\r\n    }\r\n  };\r\n\r\n  useEffect(() => {\r\n    getUsersData();\r\n  }, []);\r\n\r\n  const removeUser = async (user: any) => {\r\n    setRemoveUser(user);\r\n    setModal(true);\r\n  }\r\n\r\n  const modalConfirm = async () => {\r\n    await apiService.remove(`/users/${removeUserDetails['_id']}`);\r\n    getUsersData();\r\n    setRemoveUser({});\r\n    setModal(false);\r\n  }\r\n\r\n  const modalHide = () => setModal(false);\r\n\r\n  return (\r\n    <div>\r\n      <Modal show={isModalShow} onHide={modalHide}>\r\n        <Modal.Header closeButton>\r\n          <Modal.Title>Delete User</Modal.Title>\r\n        </Modal.Header>\r\n        <Modal.Body>Are you sure want to delete this user ?</Modal.Body>\r\n        <Modal.Footer>\r\n          <Button variant=\"secondary\" onClick={modalHide}>\r\n            Cancel\r\n          </Button>\r\n          <Button variant=\"primary\" onClick={modalConfirm}>\r\n            Yes\r\n          </Button>\r\n        </Modal.Footer>\r\n      </Modal>\r\n\r\n      <RKITable\r\n        columns={columns}\r\n        data={tabledata}\r\n        totalRows={totalRows}\r\n        pagServer={true}\r\n        handlePerRowsChange={handlePerRowsChange}\r\n        handlePageChange={handlePageChange}\r\n      />\r\n      </div>\r\n  )\r\n}\r\n\r\nexport default UsersTable;", "\n    (window.__NEXT_P = window.__NEXT_P || []).push([\n      \"/users\",\n      function () {\n        return require(\"private-next-pages/users/index.tsx\");\n      }\n    ]);\n    if(module.hot) {\n      module.hot.dispose(function () {\n        window.__NEXT_P.push([\"/users\"])\n      });\n    }\n  ", "//Import Library\r\nimport React from 'react'\r\nimport DataTable  from 'react-data-table-component';\r\n\r\n//Import services/components\r\nimport { useTranslation } from 'next-i18next';\r\nimport CustomLoader from './CustomLoader';\r\n\r\ninterface RKITableProps {\r\n  columns: any[];\r\n  data: any[];\r\n  totalRows?: number;\r\n  resetPaginationToggle?: boolean;\r\n  subheader?: boolean;\r\n  subHeaderComponent?: React.ReactNode;\r\n  handlePerRowsChange?: (newPerPage: number, page: number) => void;\r\n  handlePageChange?: (page: number) => void;\r\n  rowsPerPage?: number[];\r\n  defaultRowsPerPage?: number;\r\n  selectableRows?: boolean;\r\n  loading?: boolean;\r\n  pagServer?: boolean;\r\n  onSelectedRowsChange?: (selectedRows: any) => void;\r\n  clearSelectedRows?: boolean;\r\n  sortServer?: boolean;\r\n  onSort?: (column: any, sortDirection: string) => void;\r\n  persistTableHead?: boolean;\r\n  sortFunction?: (rows: any[], field: any, direction: 'asc' | 'desc') => any[];\r\n  noHeader?: boolean;\r\n  dense?: boolean;\r\n  pagination?: boolean;\r\n  paginationServer?: boolean;\r\n  paginationTotalRows?: number;\r\n  subHeaderAlign?: string;\r\n  [key: string]: any;\r\n}\r\n\r\nfunction RKITable(props: RKITableProps) {\r\n  const { t } = useTranslation('common');\r\n  const paginationComponentOptions = {\r\n    rowsPerPageText: t('Rowsperpage'),\r\n    };\r\n  const {\r\n    columns,\r\n    data,\r\n    totalRows,\r\n    resetPaginationToggle,\r\n    subheader,\r\n    subHeaderComponent,\r\n    handlePerRowsChange,\r\n    handlePageChange,\r\n    rowsPerPage,\r\n    defaultRowsPerPage,\r\n    selectableRows,\r\n    loading,\r\n    pagServer,\r\n    onSelectedRowsChange,\r\n    clearSelectedRows,\r\n    sortServer,\r\n    onSort,\r\n    persistTableHead,\r\n    sortFunction,\r\n    // Filter out any unknown props that might cause DOM warnings\r\n    ...otherProps\r\n  } = props;\r\n\r\n  // Only pass known DataTable props to avoid DOM warnings\r\n  const dataTableProps = {\r\n    paginationComponentOptions,\r\n    noDataComponent: t(\"NoData\"),\r\n    noHeader: true,\r\n    columns,\r\n    data: data || [],\r\n    dense: true,\r\n    paginationResetDefaultPage: resetPaginationToggle,\r\n    subHeader: subheader,\r\n    progressPending: loading,\r\n    subHeaderComponent,\r\n    pagination: true,\r\n    paginationServer: pagServer,\r\n    paginationPerPage: defaultRowsPerPage ? defaultRowsPerPage : 10,\r\n    paginationRowsPerPageOptions: rowsPerPage ? rowsPerPage : [10, 15, 20, 25, 30],\r\n    paginationTotalRows: totalRows,\r\n    onChangeRowsPerPage: handlePerRowsChange,\r\n    onChangePage: handlePageChange,\r\n    selectableRows,\r\n    onSelectedRowsChange,\r\n    clearSelectedRows,\r\n    progressComponent: <CustomLoader/>,\r\n    sortIcon: <i className=\"sort-icon fas fa-exchange-alt\" />,\r\n    sortServer,\r\n    onSort,\r\n    sortFunction,\r\n    persistTableHead,\r\n    className: \"rki-table\"\r\n  };\r\n  return (\r\n    <DataTable {...dataTableProps} />\r\n  )\r\n};\r\n\r\nRKITable.defaultProps = {\r\n  subHeader: false,\r\n  pagination: true,\r\n  totalRows: null,\r\n  pagServer: true,\r\n  onSelectedRowsChange: null,\r\n  clearSelectedRows: false,\r\n  sortServer: false,\r\n  persistTableHead: false\r\n}\r\n\r\nexport default RKITable;\r\n\r\n", "//Import Library\r\nimport React from \"react\";\r\nimport Link from 'next/link';\r\nimport { Container, Row, Col, Button } from 'react-bootstrap';\r\n\r\n//Import services/components\r\nimport UsersTable from \"./UsersTable\";\r\nimport { serverSideTranslations } from 'next-i18next/serverSideTranslations';\r\n\r\nconst Users = (): React.ReactElement => {\r\n\r\n  return (\r\n    <Container className='users-page'>\r\n      <Row className='page-header'>\r\n        <h4>User List</h4>\r\n      </Row>\r\n      <Row>\r\n        <Col xs={12}>\r\n          <Link href='/users/[...routes]' as='/users/create' ><Button variant=\"secondary\" size=\"sm\">\r\n            Add Users\r\n          </Button></Link></Col>\r\n      </Row>\r\n      <Row className=\"mt-3\">\r\n        <Col xs={12}>\r\n          <UsersTable />\r\n        </Col>\r\n      </Row>\r\n    </Container>\r\n  );\r\n}\r\n\r\nexport async function getStaticProps({ locale } : { locale: string}) {\r\n  return {\r\n    props: {\r\n      ...(await serverSideTranslations(locale, ['common'])),\r\n    },\r\n  }\r\n}\r\n\r\nexport default Users;\r\n"], "names": ["_props", "tabledata", "setDataToTable", "useState", "UsersTable", "setLoading", "totalRows", "setTotalRows", "perPage", "setPerPage", "isModalShow", "setModal", "removeUserDetails", "setRemoveUser", "usersParams", "columns", "name", "selector", "cell", "d", "username", "email", "role", "title", "institution", "div", "Link", "href", "as", "_id", "a", "onClick", "removeUser", "getUsersData", "response", "apiService", "get", "data", "length", "totalCount", "handlePerRowsChange", "newPerPage", "page", "limit", "useEffect", "user", "modalConfirm", "remove", "modalHide", "Modal", "show", "onHide", "Header", "closeButton", "Title", "Body", "Footer", "<PERSON><PERSON>", "variant", "RKITable", "pagServer", "handlePageChange", "props", "t", "useTranslation", "paginationComponentOptions", "rowsPerPageText", "resetPaginationToggle", "subheader", "subHeaderComponent", "rowsPerPage", "defaultRowsPerPage", "selectableRows", "loading", "onSelectedRowsChange", "clearSelectedRows", "sortServer", "onSort", "persistTableHead", "sortFunction", "otherProps", "dataTableProps", "noDataComponent", "<PERSON><PERSON><PERSON><PERSON>", "dense", "paginationResetDefaultPage", "subHeader", "progressPending", "pagination", "paginationServer", "paginationPerPage", "paginationRowsPerPageOptions", "paginationTotalRows", "onChangeRowsPerPage", "onChangePage", "progressComponent", "CustomLoader", "sortIcon", "i", "className", "DataTable", "defaultProps", "Container", "Users", "Row", "h4", "Col", "xs", "size"], "sourceRoot": "", "ignoreList": []}