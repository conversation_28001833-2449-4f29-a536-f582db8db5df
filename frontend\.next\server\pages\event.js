"use strict";(()=>{var e={};e.id=5416,e.ids=[636,3220,5416],e.modules={123:e=>{e.exports=require("dom-helpers/removeEventListener")},1332:e=>{e.exports=require("react-custom-scrollbars-2")},1428:e=>{e.exports=import("axios")},1680:e=>{e.exports=require("@restart/ui/utils")},1919:e=>{e.exports=require("react-transition-group/Transition")},2262:(e,r,t)=>{t.a(e,async(e,s)=>{try{t.r(r),t.d(r,{default:()=>f});var a=t(8732),n=t(19918),o=t.n(n),i=t(74716),u=t.n(i),l=t(27825),d=t.n(l),p=t(82015),c=t.n(p),x=t(44233),m=t(56084),h=t(63487),y=t(98060),q=t(88751),v=e([h,y]);[h,y]=v.then?(await v)():v;let g=e=>{let{i18n:r}=(0,q.useTranslation)("common"),t="fr"===r.language?"en":r.language,{hazards:s}=e;return(0,a.jsx)("ul",{children:s.map((e,r)=>e&&e._id&&e.title&&e.title[t]?(0,a.jsx)("li",{children:(0,a.jsx)(o(),{href:"/hazard/[...routes]",as:`/hazard/show/${e._id}`,children:e.title[t].toString()})},r):"")})},f=function(e){let r=(0,x.useRouter)(),{t}=(0,q.useTranslation)("common"),{setEvents:s,selectedRegions:n}=e,[i,l]=c().useState(""),[v,f]=c().useState(""),[_,j]=c().useState(!1),[S,w]=(0,p.useState)([]),[b,A]=(0,p.useState)(!1),[E,C]=(0,p.useState)(0),[z,k]=(0,p.useState)(10),[M,P]=(0,p.useState)(1),[T,D]=(0,p.useState)(null),N={sort:{created_at:"desc"},lean:!0,populate:[{path:"country",select:"coordinates title"},{path:"hazard_type",select:"title"},{path:"hazard",select:"title"}],limit:z,page:1,query:{},select:"-description -operation -world_region -country_regions -syndrome -status -laboratory_confirmed -officially_validated -rki_monitored -risk_assessment -date -more_info -images -user -created_at -updated_at"},[I,H]=(0,p.useState)(N),R=[{name:t("Events.table.EventId"),selector:"title",sortable:!0,width:"20%",cell:e=>(0,a.jsx)(o(),{href:"/event/[...routes]",as:`/event/show/${e._id}`,children:e.title})},{name:t("Events.table.Country"),selector:"country",sortable:!0,cell:e=>e.country&&e.country.title?(0,a.jsx)(o(),{href:"/country/[...routes]",as:`/country/show/${e.country._id}`,children:e.country.title}):""},{name:t("Events.table.HazardType"),selector:"hazard_type",sortable:!0,cell:e=>e.hazard_type&&e.hazard_type.title?e.hazard_type.title:""},{name:t("Events.table.Hazard"),selector:"hazard",cell:e=>(0,a.jsx)(g,{hazards:e.hazard})},{name:t("Events.table.InfoReceivedon"),selector:"created_at",sortable:!0,cell:e=>u()(e.start_date).format("M/D/Y")},{name:t("Events.table.Lastupdated"),selector:"updated_at",sortable:!0,cell:e=>u()(e.updated_at).format("M/D/Y")}],F=async e=>{A(!0),r.query&&r.query.country&&(e.query.country=r.query.country),null===n?delete e.query.world_region:0===n.length?e.query.world_region=["__NO_MATCH__"]:e.query.world_region=n;let t=await h.A.get("/event",e);t&&Array.isArray(t.data)&&(w(t.data),s(t.data),C(t.totalCount)),A(!1)},$=async(e,t)=>{N.limit=e,N.page=t,A(!0),r.query&&r.query.country&&(N.query.country=r.query.country),null===n?delete N.query.world_region:0===n.length?N.query.world_region=["__NO_MATCH__"]:N.query.world_region=n,v&&(N.query={...N.query,hazard_type:v}),T&&(N.sort=T.sort);let a=await h.A.get("/event",N);a&&Array.isArray(a.data)&&(w(a.data),s(a.data),k(e),A(!1)),P(t)},B=async(e,r)=>{A(!0),N.sort={[e.selector]:r},v&&(N.query={...N.query,hazard_type:v}),""!==i&&(N.query={...N.query,title:i}),await F(N),D(N),A(!1)},O=(e,r)=>{e?(I.query.title=e,I.page=r):delete I.query.title,H({...I})},U=(0,p.useRef)(d().debounce((e,r)=>O(e,r),Number("500")||300)).current,V=c().useMemo(()=>{let e=e=>{f(e),e?I.query.hazard_type=e:delete I.query.hazard_type,H({...I})};return(0,a.jsx)(y.default,{onFilter:e=>{l(e.target.value),U(e.target.value,M)},onFilterHazardChange:r=>e(r.target.value),onClear:()=>{i&&(j(!_),l(""))},filterText:i,filterHazard:v})},[i,_,v,n]);return(0,a.jsx)(m.A,{columns:R,data:S,totalRows:E,loading:b,subheader:!0,persistTableHead:!0,onSort:B,sortServer:!0,pagServer:!0,subHeaderComponent:V,handlePerRowsChange:$,handlePageChange:e=>{N.limit=z,N.page=e,v&&(N.query={...N.query,hazard_type:v}),T&&(N.sort=T.sort),F(N),P(e)}})};s()}catch(e){s(e)}})},3892:e=>{e.exports=require("classnames")},4048:e=>{e.exports=require("react-truncate")},6009:e=>{e.exports=require("dom-helpers/ownerDocument")},6952:e=>{e.exports=require("@restart/ui/Modal")},7374:e=>{e.exports=require("@restart/ui/Dropdown")},8732:e=>{e.exports=require("react/jsx-runtime")},9653:e=>{e.exports=require("@restart/ui/Overlay")},11242:e=>{e.exports=require("redux-persist/integration/react")},11688:e=>{e.exports=require("@restart/hooks/useWillUnmount")},12388:e=>{e.exports=require("dom-helpers/addEventListener")},12900:(e,r,t)=>{t.a(e,async(e,s)=>{try{t.r(r),t.d(r,{config:()=>y,default:()=>c,getServerSideProps:()=>h,getStaticPaths:()=>m,getStaticProps:()=>x,reportWebVitals:()=>q,routeModule:()=>S,unstable_getServerProps:()=>_,unstable_getServerSideProps:()=>j,unstable_getStaticParams:()=>f,unstable_getStaticPaths:()=>g,unstable_getStaticProps:()=>v});var a=t(63885),n=t(80237),o=t(81413),i=t(9616),u=t.n(i),l=t(72386),d=t(70709),p=e([l,d]);[l,d]=p.then?(await p)():p;let c=(0,o.M)(d,"default"),x=(0,o.M)(d,"getStaticProps"),m=(0,o.M)(d,"getStaticPaths"),h=(0,o.M)(d,"getServerSideProps"),y=(0,o.M)(d,"config"),q=(0,o.M)(d,"reportWebVitals"),v=(0,o.M)(d,"unstable_getStaticProps"),g=(0,o.M)(d,"unstable_getStaticPaths"),f=(0,o.M)(d,"unstable_getStaticParams"),_=(0,o.M)(d,"unstable_getServerProps"),j=(0,o.M)(d,"unstable_getServerSideProps"),S=new a.PagesRouteModule({definition:{kind:n.A.PAGES,page:"/event",pathname:"/event",bundlePath:"",filename:""},components:{App:l.default,Document:u()},userland:d});s()}catch(e){s(e)}})},13364:e=>{e.exports=require("redux-saga/effects")},14062:e=>{e.exports=import("react-redux")},14078:e=>{e.exports=import("swr")},14332:e=>{e.exports=require("uncontrollable")},16116:e=>{e.exports=require("invariant")},18622:e=>{e.exports=require("yup")},19121:e=>{e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},22326:e=>{e.exports=require("react-dom")},22541:e=>{e.exports=require("redux-persist/lib/storage")},25303:e=>{e.exports=require("@restart/ui/NavItem")},26324:e=>{e.exports=require("warning")},27825:e=>{e.exports=require("lodash")},27910:e=>{e.exports=require("stream")},28217:e=>{e.exports=require("@restart/ui/DropdownItem")},29021:e=>{e.exports=require("fs")},29294:e=>{e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},29628:e=>{e.exports=require("dom-helpers/addClass")},29825:e=>{e.exports=require("prop-types")},29841:e=>{e.exports=require("dom-helpers/hasClass")},30362:e=>{e.exports=require("@restart/ui/DropdownToggle")},30910:(e,r,t)=>{t.r(r),t.d(r,{default:()=>c});var s=t(8732),a=t(27825),n=t.n(a),o=t(19918),i=t.n(o),u=t(82015),l=t(88751),d=t(72953),p=t(89364);let c=e=>{let{i18n:r}=(0,l.useTranslation)("common"),t=r.language,{events:a}=e,[o,c]=(0,u.useState)({}),[x,m]=(0,u.useState)([]),[h,y]=(0,u.useState)({}),[q,v]=(0,u.useState)({}),g=()=>{y(null),v(null)},f=(e,r,t)=>{g(),y(r),v({name:e.name,id:e.id,countryId:e.countryId})},_=()=>{let e=[];n().forEach(a,r=>{e.push({title:r.title,id:r._id,lat:r.country&&r.country.coordinates&&r.country.coordinates[0].latitude,lng:r.country&&r.country.coordinates&&r.country.coordinates[0].longitude,countryId:r.country&&r.country._id})}),m([...e])};return(0,u.useEffect)(()=>{_(),c(n().groupBy(a,"country._id"))},[a]),(0,s.jsx)(d.A,{onClose:g,language:t,activeMarker:h,markerInfo:(0,s.jsx)(e=>{let{info:r}=e;return r&&r.countryId&&o[r.countryId]?(0,s.jsx)("ul",{children:o[r.countryId].map((e,r)=>(0,s.jsx)("li",{children:(0,s.jsx)(i(),{href:"/event/[...routes]",as:`/${t}/event/show/${e._id}`,children:e.title})},r))}):null},{info:q}),children:x.length>=1?x.map((e,r)=>{if(e.lat)return(0,s.jsx)(p.A,{name:e.title,id:e.id,countryId:e.countryId,icon:{url:"/images/map-marker-white.svg"},onClick:f,position:e},r)}):null})}},33873:e=>{e.exports=require("path")},35576:e=>{e.exports=require("next-i18next/serverSideTranslations")},36653:e=>{e.exports=require("nprogress")},36955:e=>{e.exports=require("@restart/hooks/useBreakpoint")},37766:e=>{e.exports=require("@restart/hooks/useMergedRefs")},38609:e=>{e.exports=require("react-data-table-component")},39756:e=>{e.exports=import("redux")},40051:e=>{e.exports=require("dom-helpers/removeClass")},40361:e=>{e.exports=require("next/dist/compiled/next-server/pages.runtime.prod.js")},42893:e=>{e.exports=import("react-hot-toast")},43294:e=>{e.exports=require("formik")},50009:e=>{e.exports=require("@restart/ui/DropdownMenu")},57664:e=>{e.exports=require("dom-helpers/scrollbarSize")},58928:e=>{e.exports=require("dom-helpers/canUseDOM")},59717:e=>{e.exports=require("@restart/ui/DropdownContext")},60560:e=>{e.exports=require("dom-helpers/contains")},63033:e=>{e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},65447:e=>{e.exports=require("@restart/ui/Button")},67364:e=>{e.exports=require("@restart/hooks/useCallbackRef")},68455:e=>{e.exports=import("redux-saga")},69722:e=>{e.exports=require("es6-promise")},70709:(e,r,t)=>{t.a(e,async(e,s)=>{try{t.r(r),t.d(r,{default:()=>S,getStaticProps:()=>j});var a=t(8732),n=t(82015),o=t(19918),i=t.n(o),u=t(20156),l=t.n(u),d=t(7082),p=t(83551),c=t(49481),x=t(27053),m=t(2262),h=t(30910),y=t(88751),q=t(35576),v=t(16553),g=t(75268),f=t(20181),_=e([m,v,f]);async function j({locale:e}){return{props:{...await (0,q.serverSideTranslations)(e,["common"])}}}[m,v,f]=_.then?(await _)():_;let S=()=>{let{t:e}=(0,y.useTranslation)("common"),[r,t]=(0,n.useState)(!1),[s,o]=(0,n.useState)(null),[u,q]=(0,n.useState)([]),_=()=>t(!r),j=()=>(0,a.jsx)(i(),{href:"/event/[...routes]",as:"/event/create",children:(0,a.jsx)(l(),{variant:"secondary",size:"sm",children:e("addEvent")})}),S=(0,g.canAddEvent)(()=>(0,a.jsx)(j,{})),w=e=>{o(e)};return(0,a.jsxs)(d.A,{fluid:!0,className:"p-0",children:[(0,a.jsx)(p.A,{children:(0,a.jsx)(c.A,{xs:12,children:(0,a.jsx)(x.A,{title:e("menu.events")})})}),(0,a.jsx)(p.A,{children:(0,a.jsx)(c.A,{xs:12,children:(0,a.jsx)(h.default,{events:u})})}),(0,a.jsx)(p.A,{children:(0,a.jsx)(c.A,{xs:12,children:(0,a.jsx)(f.A,{filtreg:e=>w(e),selectedRegions:[],regionHandler:w})})}),(0,a.jsx)(p.A,{children:(0,a.jsx)(c.A,{xs:12,className:"ps-4",children:(0,a.jsxs)(p.A,{children:[(0,a.jsx)(c.A,{children:(0,a.jsx)(S,{})}),(0,a.jsx)(c.A,{children:(0,a.jsx)("p",{className:"m-0",children:(0,a.jsxs)("small",{children:[e("Events.table.Doyouknowofaneventthatneedstobeadded")," ",(0,a.jsx)(l(),{variant:"link",size:"sm",className:"p-0 outlineButton",onClick:_,children:e("Events.table.Clickhere")}),(0,a.jsx)("span",{children:"\xa0"}),e("Events.table.toinformthePublicHealthIntelligenceTeam")]})})})]})})}),(0,a.jsx)(p.A,{className:"mt-1",children:(0,a.jsx)(c.A,{xs:12,children:(0,a.jsx)(m.default,{selectedRegions:s,setEvents:q})})}),(0,a.jsx)(v.A,{show:r,onHide:_})]})};s()}catch(e){s(e)}})},74075:e=>{e.exports=require("zlib")},74716:e=>{e.exports=require("moment")},74987:e=>{e.exports=require("@restart/ui/ModalManager")},75268:(e,r,t)=>{t.r(r),t.d(r,{canAddEvent:()=>i,canAddEventForm:()=>u,canEditEvent:()=>l,canEditEventForm:()=>d,canViewDiscussionUpdate:()=>p,default:()=>c});var s=t(8732);t(82015);var a=t(81366),n=t.n(a),o=t(61421);let i=n()({authenticatedSelector:e=>!!e.permissions&&!!e.permissions.event&&!!e.permissions.event["create:any"],wrapperDisplayName:"CanAddEvent"}),u=n()({authenticatedSelector:e=>!!e.permissions&&!!e.permissions.event&&!!e.permissions.event["create:any"],wrapperDisplayName:"CanAddEventForm",FailureComponent:()=>(0,s.jsx)(o.default,{})}),l=n()({authenticatedSelector:(e,r)=>{if(e.permissions&&e.permissions.event){if(e.permissions.event["update:any"])return!0;else if(e.permissions.event["update:own"]&&r.event&&r.event.user&&r.event.user._id===e.user._id)return!0}return!1},wrapperDisplayName:"CanEditEvent"}),d=n()({authenticatedSelector:(e,r)=>{if(e.permissions&&e.permissions.event){if(e.permissions.event["update:any"])return!0;else if(e.permissions.event["update:own"]&&r.event&&r.event.user&&r.event.user._id===e.user._id)return!0}return!1},wrapperDisplayName:"CanEditEventForm",FailureComponent:()=>(0,s.jsx)(o.default,{})}),p=n()({authenticatedSelector:e=>!!e.permissions&&!!e.permissions.update&&!!e.permissions.update["read:any"],wrapperDisplayName:"CanViewDiscussionUpdate"}),c=i},78097:e=>{e.exports=require("next-redux-saga")},78634:e=>{e.exports=require("@restart/ui/Anchor")},81366:e=>{e.exports=require("redux-auth-wrapper/connectedAuthWrapper")},81521:e=>{e.exports=require("dom-helpers/querySelectorAll")},81895:e=>{e.exports=require("@restart/hooks/useEventCallback")},82015:e=>{e.exports=require("react")},86842:e=>{e.exports=require("@restart/ui/SelectableContext")},87571:e=>{e.exports=require("dom-helpers/transitionEnd")},88751:e=>{e.exports=require("next-i18next")},93787:e=>{e.exports=require("redux-persist")},94696:e=>{e.exports=require("@react-google-maps/api")},94947:e=>{e.exports=require("@restart/hooks/useTimeout")},96196:e=>{e.exports=require("next-redux-wrapper")},98060:(e,r,t)=>{t.a(e,async(e,s)=>{try{t.r(r),t.d(r,{default:()=>x});var a=t(8732),n=t(82015),o=t(7082),i=t(83551),u=t(49481),l=t(84517),d=t(63487),p=t(88751),c=e([d]);d=(c.then?(await c)():c)[0];let x=({filterText:e,onFilter:r,onClear:t,onFilterHazardChange:s,filterHazard:c})=>{let[x,m]=(0,n.useState)([]),{t:h}=(0,p.useTranslation)("common"),y=async e=>{let r=await d.A.get("/hazardtype",e);r&&Array.isArray(r.data)&&m(r.data)};return(0,n.useEffect)(()=>{y({query:{},sort:{title:"asc"}})},[]),(0,a.jsx)(o.A,{fluid:!0,className:"p-0",children:(0,a.jsxs)(i.A,{children:[(0,a.jsx)(u.A,{xs:6,className:"p-0",children:(0,a.jsx)(l.A,{type:"text",className:"searchInput",placeholder:h("Events.table.Search"),"aria-label":"Search",value:e,onChange:r})}),(0,a.jsx)(u.A,{xs:6,children:(0,a.jsxs)(l.A,{as:"select","aria-label":"HazardType","aria-placeholder":"Hazard Type",onChange:s,value:c,children:[(0,a.jsx)("option",{value:"",children:h("Events.forms.SelectHazardType")}),x.map((e,r)=>(0,a.jsx)("option",{value:e._id,children:e.title},r))]})})]})})};s()}catch(e){s(e)}})},98320:e=>{e.exports=require("@restart/hooks/useIsomorphicEffect")},99460:e=>{e.exports=require("dom-helpers/css")}};var r=require("../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[6089,9216,9616,2386,5016],()=>t(12900));module.exports=s})();