{"version": 3, "file": "static/chunks/7051-493113db56845a45.js", "mappings": "qJAcA,MANuBA,GAEnB,UAACC,EAAAA,EAAUA,CAAAA,CAAE,GAAGD,CAAK,KAIVE,aAAaA,EAAC,2HCyEtB,IAAMC,EAAQ,CACnBC,WA1C4C,OAAC,MAC7CC,CAAI,eACJC,CAAa,UACbC,CAAQ,cACRC,CAAY,UACZC,CAAQ,CACT,GACO,QAAEC,CAAM,SAAEC,CAAO,CAAE,CAAGC,CAAAA,EAAAA,EAAAA,EAAAA,CAAgBA,GACtCC,EAAWF,CAAO,CAACN,EAAK,EAAIK,CAAM,CAACL,EAAK,CAGzBS,EAAAA,OAAa,CAAC,IAAO,OAAET,EAAK,EAAI,CAACA,EAAK,EAG3D,IAAMU,EAAoBD,EAAAA,QAAc,CAACE,GAAG,CAACP,EAAU,GACrD,EAAIK,cAAoB,CAACG,IAxC7B,IAwCqC,KAxCnBjB,CAAU,EAC1B,MAAwB,UAAjB,OAAOA,GAAgC,OAAVA,CACtC,EAwCmBiB,EAAMjB,KAAK,EACfc,CADkB,CAClBA,YAAkB,CAACG,EAA6C,CACrEZ,OACA,GAAGY,EAAMjB,KAAK,GAIbiB,GAGT,MACE,WAACC,MAAAA,WACC,UAACA,MAAAA,CAAIC,UAAU,uBACZJ,IAEFF,GACC,UAACK,MAAAA,CAAIC,UAAU,oCACZX,GAAiB,kBAAOE,CAAM,CAACL,EAAK,CAAgBK,CAAM,CAACL,EAAK,CAAGe,OAAOV,CAAM,CAACL,EAAK,OAKjG,EAIEgB,UAhE0C,OAAC,CAAEC,IAAE,OAAEC,CAAK,OAAEC,CAAK,MAAEnB,CAAI,UAAEoB,CAAQ,CAAE,GACzE,QAAEC,CAAM,eAAEC,CAAa,CAAE,CAAGf,CAAAA,EAAAA,EAAAA,EAAAA,CAAgBA,GAC5CgB,EAAYvB,GAAQiB,EAE1B,MACE,UAACO,EAAAA,CAAIA,CAACC,KAAK,EACTC,KAAK,QACLT,GAAIA,EACJC,MAAOA,EACPC,MAAOA,EACPnB,KAAMuB,EACNI,QAASN,CAAM,CAACE,EAAU,GAAKJ,EAC/BjB,SAAW0B,IACTN,EAAcC,EAAWK,EAAEC,MAAM,CAACV,KAAK,CACzC,EACAC,SAAUA,EACVU,MAAM,KAGZ,CA8CA,CCzEgBC,CDyEd,ECzEcA,CAAAA,CACLC,EAAAA,EAAAA,CACEC,EAAAA,EAAAA,6HC8Bb,MAvCoB,OAAC,MAAEP,CAAI,IAAET,CAAE,CAAO,GAC9B,CAACiB,EAAMC,EAAQ,CAAGC,CAAAA,EAAAA,EAAAA,KAsCAC,EAAC,CAtCDD,CAAQA,CAAC,GAC3B,GAAEE,CAAC,CAAE,CAAGC,CAAAA,EAAAA,EAAAA,EAAAA,CAAcA,CAAC,UAY7B,MAVAC,CAAAA,EAAAA,EAAAA,SAAAA,CAASA,CAAC,KACJN,EAAO,EACTO,CADY,UACD,IAAMN,EAAQD,EAAO,GAAI,KAEpCQ,IAAAA,IAAW,CAAC,CACVC,SAAU,iBACVC,MAAO,CAAE3B,GAAIA,EAAI4B,OAAQnB,CAAK,CAChC,EAEJ,GAEE,WAACoB,EAAAA,CAAKA,CAAAA,CAACC,MAAM,YACX,UAAClC,MAAAA,CAAIC,UAAU,6BACb,UAACD,MAAAA,CAAIC,UAAU,uBACb,UAACkC,EAAAA,CAAeA,CAAAA,CAACC,KAAMC,EAAAA,GAAOA,CAAEC,MAAM,UAAUC,KAAK,WAGzD,UAACvC,MAAAA,CAAIC,UAAU,4BACb,WAACuC,IAAAA,CAAEvC,UAAU,iBACVY,EAAK,IAAEY,EAAE,oCACV,UAACgB,KAAAA,CAAAA,GACD,UAACC,QAAAA,UAAOjB,EAAE,mCACV,UAACgB,KAAAA,CAAAA,GACD,UAACC,QAAAA,UACC,WAACC,IAAAA,WACE,IACAlB,EAAE,kBAAkB,IAAEJ,EAAK,IAAEI,EAAE,iCAO9C,oFCbA,IAAMmB,EAAwBC,CAAAA,EAAAA,EAAAA,UAAAA,CAAUA,CAA8C,CAAC/D,EAAOgE,KAC5F,GAAM,UAAEvD,CAAQ,UAAEwD,CAAQ,cAAEC,CAAY,CAAE/C,WAAS,CAAEgD,YAAU,CAAEC,eAAa,CAAE,GAAGC,EAAM,CAAGrE,EAGtFsE,EAAmBC,EAAAA,EAAU,GAAGC,KAAK,CAAC,CAAC,GAE7C,MACE,UAACC,EAAAA,EAAMA,CAAAA,CACLL,cAAeA,GAAiB,CAAC,EACjCE,iBAAkBA,EAClBL,SAAU,CAACvC,EAA6BgD,KAEtC,IAAMC,EAAuB,CAC3BC,eAAgB,KAAO,EACvBC,gBAAiB,KAAO,EACxBC,cAAe,KACf5C,OAAQ,KACR6C,YAAa,IAAIC,MAAM,UACvBC,SAAS,EACTC,YAAY,EACZC,kBAAkB,EAClBC,WAAY,EACZC,WAAW,EACXC,UAAWC,KAAKC,GAAG,GACnBzD,KAAM,SACN0D,mBAAoB,KAAM,EAC1BC,qBAAsB,KAAM,EAC5BC,QAAS,KAAO,CAClB,EAEI1B,GAEFA,EAASU,EAAWjD,EAAQgD,CAFhB,CAIhB,EACC,GAAGL,CAAI,UAEP,GACC,UAACxC,EAAAA,EAAIA,CAAAA,CACHmC,IAAKA,EACLC,SAAU2B,EAAYC,YAAY,CAClC3B,aAAcA,EACd/C,UAAWA,EACXgD,WAAYA,WAES,YAApB,OAAO1D,EAA0BA,EAASmF,GAAenF,KAKpE,GAEAqD,EAAsBgC,WAAW,CAAG,wBAEpC,MAAehC,qBAAqBA,EAAC,yEClF9B,IAAMzB,EAAY,OAAC,MACxBhC,CAAI,IACJiB,CAAE,UACFyE,CAAQ,WACRC,CAAS,cACTxF,CAAY,UACZD,CAAQ,OACRiB,CAAK,IACLyE,CAAE,CACFC,WAAS,MACTC,CAAI,CACJC,SAAO,CACP,GAAGpG,EACC,GAuBJ,MACE,UAACqG,EAAAA,EAAKA,CAAAA,CAAChG,KAAMA,EAAMiG,SAtBJ,CAsBcA,GApB7B,IAAMC,EAA2B,UAAf,OAAOC,EAAmBA,EAAMpF,OAAOoF,GAAO,WAChE,GAAiB,EAACA,GAAOD,IAAR,GAAkBE,IAAI,EAAO,CAAC,CACtCjG,EAD0C,KAC1CA,EAAAA,KAAAA,EAAAA,EAAcwF,SAAAA,GAAa,EAA3BxF,uBAGLwF,GAAa,CAACA,EAAUQ,GACnBhG,GADyB,IACzBA,EAAAA,KAAAA,EAAAA,EAAcwF,SAAAA,GAAa,EAA3BxF,cAGL4F,GAAWI,GAET,CADU,CADI,GACAE,OAAON,GACdO,IAAI,CAACH,GACPhG,GADa,IACbA,EAAAA,KAAAA,EAAAA,EAAc4F,OAAAA,GAAW,IAAzB5F,mBAKb,WAIK,OAAC,CAAEoG,OAAK,CAAEC,MAAI,CAAO,SACpB,iCACE,UAAChF,EAAAA,CAAIA,CAACiF,OAAO,EACV,GAAGF,CAAK,CACR,GAAG5G,CAAK,CACTsB,GAAIA,EACJ2E,GAAIA,GAAM,QACVE,KAAMA,EACNY,UAAWF,EAAKlG,OAAO,EAAI,CAAC,CAACkG,EAAKG,KAAK,CACvCzG,SAAU,IACRqG,EAAMrG,QAAQ,CAAC0B,GACX1B,GAAUA,EAAS0B,EACzB,EACAT,WAAiByF,IAAVzF,EAAsBA,EAAQoF,EAAMpF,KAAK,GAEjDqF,EAAKlG,OAAO,EAAIkG,EAAKG,KAAK,CACzB,UAACnF,EAAAA,CAAIA,CAACiF,OAAO,CAACI,QAAQ,EAACnF,KAAK,mBACzB8E,EAAKG,KAAK,GAEX,UAKd,EAAE,EAIyB,OAAC,MAC1B3G,CAAI,IACJiB,CAAE,UACFyE,CAAQ,cACRvF,CAAY,UACZD,CAAQ,OACRiB,CAAK,UACLf,CAAQ,CACR,GAAGT,EACC,GAUJ,MACE,UAACqG,EAAAA,EAAKA,CAAAA,CAAChG,KAAMA,EAAMiG,SATJ,CAScA,GAR7B,GAAIP,GAAa,EAACS,GAAe,IAAhB,CAAQA,CAAQ,CAAC,CAChC,EADoC,IAC7BhG,OAAAA,EAAAA,KAAAA,EAAAA,EAAcwF,SAAAA,GAAa,EAA3BxF,sBAIX,WAIK,OAAC,OAAEoG,CAAK,MAAEC,CAAI,CAAO,SACpB,iCACE,UAAChF,EAAAA,CAAIA,CAACiF,OAAO,EACXb,GAAG,SACF,GAAGW,CAAK,CACR,GAAG5G,CAAK,CACTsB,GAAIA,EACJyF,UAAWF,EAAKlG,OAAO,EAAI,CAAC,CAACkG,EAAKG,KAAK,CACvCzG,SAAU,IACRqG,EAAMrG,QAAQ,CAAC0B,GACX1B,GAAUA,EAAS0B,EACzB,EACAT,WAAiByF,IAAVzF,EAAsBA,EAAQoF,EAAMpF,KAAK,UAE/Cf,IAEFoG,EAAKlG,OAAO,EAAIkG,EAAKG,KAAK,CACzB,UAACnF,EAAAA,CAAIA,CAACiF,OAAO,CAACI,QAAQ,EAACnF,KAAK,mBACzB8E,EAAKG,KAAK,GAEX,UAKd,EAAE,uWC/FF,IAAMG,EAAe,CACjBC,MAAO,GACPC,QAAS,GACTC,UAAW,GACXC,OAAQ,KACRC,iBAAkB,GAClBC,WAAY,KACZC,SAAU,KACVC,aAAc,EAAE,CAChBC,YAAa,GACbC,oBAAqB,EAAE,CACvBC,qBAAsB,EAAE,CACxB9F,QAAS,EACb,EAwwBA,EArwBoB,IAChB,IAAM+F,EAAYC,CAAAA,EAAAA,EAAAA,MAAAA,CAAMA,CAAM,KAowBnBC,CAnwBLC,EAAeF,CAAAA,EAAAA,EAAAA,GAmwBCC,EAAC,CAnwBFD,CAAMA,CAAM,MAC3B,GAAErF,CAAC,MAAEwF,CAAI,CAAE,CAAGvF,CAAAA,EAAAA,EAAAA,EAAAA,CAAcA,CAAC,UAC7BwF,EAAgC,OAAlBD,EAAKE,QAAQ,CAAY,CAAEC,SAAU,KAAM,EAAI,CAAElB,MAAO,KAAM,EAC5EmB,EAAcJ,EAAKE,QAAQ,CAC3B,CAACG,EAAYC,EAAa,CAAGhG,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAM0E,GAC3C,CAACuB,EAAgB,CAAGjG,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAM,CACpCkG,eAAgB,EAAE,CAClBC,cAAe,EAAE,CACjBC,wBAAyB,EAAE,CAC3BC,oBAAqB,EAAE,CACvBC,iBAAkB,EAAE,CACpBC,eAAgB,EAAE,CAClBC,SAAU,EAAE,CACZC,WAAY,EAChB,GACM,CAACC,EAAeC,EAAiB,CAAG3G,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAQ,EAAE,EACtD,CAAC4G,EAAaC,EAAe,CAAG7G,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAQ,EAAE,EAClD,EAAG8G,EAAa,CAAG9G,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAQ,EAAE,EACrC,EAAG+G,EAAiB,CAAG/G,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAQ,EAAE,EACzC,CAACgH,EAAkBC,EAAoB,CAAGjH,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAQ,EAAE,EAC5D,EAAGkH,EAAwB,CAAGlH,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAQ,EAAE,EAChD,EAAGmH,EAAe,CAAGnH,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAQ,EAAE,EACvC,CAACoH,EAAcC,EAAgB,CAAGrH,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,EAAU,GACpD,CAACsH,EAAOC,EAAS,CAAGvH,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,EAAU,GACtC,CAACwH,EAAgBC,EAAkB,CAAGzH,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAQ,EAAE,EACxD,CAAC0H,EAAOC,GAAS,CAAG3H,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAM,MAClC4H,GAAoBrK,EAAMsK,MAAM,EAAuB,QAAnBtK,EAAMsK,MAAM,CAAC,EAAE,EAActK,EAAMsK,MAAM,CAAC,EAAE,CAChF,CAACC,GAA8BC,GAAgC,CAAG/H,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,EAAU,GACpF,CAACqF,GAAsB2C,GAAwB,CAAGhI,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAQ,CACpE,CACIiI,gBAAiB,GACjBC,QAAS,EAAE,CACXC,eAAgB,EAAE,CAClBC,aAAc,EAAE,CAChBC,oBAAqB,EAAE,CACvBC,eAAgB,EAAE,CAClBC,aAAc,EAClB,EACH,EACK,CAACnD,GAAqBoD,GAAuB,CAAGxI,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAQ,CAClE,CACI2E,MAAO,GACP8D,aAAc,GACdC,MAAO,GACPC,IAAK,IACT,EACH,EACK,CAACC,GAAkBC,GAAoB,CAAG7I,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAS,GAC3D,CAAC8I,GAAyBC,GAA2B,CAAG/I,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAC,IACjE,CAACgJ,GAAiBC,GAAmB,CAAGjJ,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAC,IAEjDkJ,GAAgB,CAClB1I,MAAO,CAAC,EACR2I,KAAMxD,EACNyD,MAAO,IACPC,aAAcvD,CAClB,EAEMwD,GAAwB,MAAOC,IACjC,IAAMC,EAAgB,MAAMC,EAAAA,CAAUA,CAACC,GAAG,CAAC,iBAAkBH,GACzDC,GAAiBG,MAAMC,OAAO,CAACJ,EAAcK,IAAI,GAAG,EACnCL,EAAcK,IAAI,EAGvC,MAAMC,EAAgBP,EAA2B1C,EAAgBE,GAEjE,IAAMgD,EAAc,MAAMN,EAAAA,CAAUA,CAACC,GAAG,CAAC,eAAgBH,GACzD,GAAIQ,GAAeJ,MAAMC,OAAO,CAACG,EAAYF,IAAI,EAAG,CAChD,IAAMG,EAAWD,EAAYF,IAAI,CAACtL,GAAG,CAAC,IAClC,GAAoB,mBAAmB,CAAnC0L,EAAKnF,MAAM,CACX,MAAO,CAAEhG,MAAOmL,EAAKtF,KAAK,CAAE5F,MAAOkL,EAAKtB,GAAG,CAEnD,GACA1B,EAAoBiD,IAAAA,OAAS,CAACF,GAClC,CAEA,IAAMG,EAAkB,MAAMV,EAAAA,CAAUA,CAACC,GAAG,CAAC,mBAAoBH,GAC7DY,GAAmBR,MAAMC,OAAO,CAACO,EAAgBN,IAAI,GAAG,EAChCM,EAAgBN,IAAI,EAGhD,IAAMO,EAAqB,MAAMX,EAAAA,CAAUA,CAACC,GAAG,CAAC,sBAAuBH,GACnEa,GAAsBT,MAAMC,OAAO,CAACQ,EAAmBP,IAAI,GAAG,EAC/CO,EAAmBP,IAAI,EAG1C,IAAMQ,EAAa,MAAMZ,EAAAA,CAAUA,CAACC,GAAG,CAAC,cAAeH,GACnDe,EAAe,EAAE,CACjBD,GAAcV,MAAMC,OAAO,CAACS,EAAWR,IAAI,GAAG,EACtCQ,EAAWR,IAAI,CAACtL,GAAG,CAAC,CAAC0L,EAAWM,EAGtBD,EAFP,EAAExL,MAAOmL,EAAKtF,KAAK,CAAE5F,MAAOkL,EAAKtB,GAAG,CAAC,GAIxD,EAEAvI,CAAAA,EAAAA,EAAAA,SAAAA,CAASA,CAAC,KACFwH,IAuBA4C,CAtBuB,KADb,CACoBC,IAC1B,IAAMC,EAAW,MAAMjB,EAAAA,CAAUA,CAACC,GAAG,CAAC,YAA4B,OAAhBnM,EAAMsK,MAAM,CAAC,EAAE,EAAI4C,GACrE,GAAIC,EAAU,CACV,IAAMC,EAA0B,EAAE,CAC5B,QACF7F,CAAM,SACN8F,CAAO,cACP1F,CAAY,sBACZG,CAAoB,YACpBL,CAAU,UACVC,CAAQ,CACX,CAAGyF,EAQJ,OAPAG,SA2rBXA,CACQ,CACb7F,CAAe,CACfC,CAAa,CACbH,CAAW,CACX8F,CAAY,CACZ1F,CAAiB,EAEjBwF,EAAS1F,UAAU,CAAGA,EAAa8F,IAAO9F,GAAY+F,MAAM,GAAK,KACjEL,EAASzF,QAAQ,CAAGA,EAAW6F,IAAO7F,GAAU8F,IADPD,EACa,GAAK,KAC3DJ,EAAS5F,MAAM,CAAGA,GAAUA,EAAO6D,GAAG,CAAG7D,EAAO6D,GAAG,CAAG,KACtD+B,EAASE,OAAO,CAAGA,GAAWA,EAAQjC,GAAG,CAAGiC,EAAQjC,GAAG,CAAG,KAC1D+B,EAASxF,YAAY,CACjBA,GAAgBA,EAAa8F,MAAM,CAAG,EAChC9F,EAAa3G,GAAG,CAAC,CAAC0L,EAAWM,KAClB,CAAEzL,MAAOmL,EAAKtF,KAAK,CAAE5F,MAAOkL,EAAKtB,GAAG,CAAC,GAEhD,EAAE,EA5sBsB+B,EAAU1F,EAAYC,EAAUH,EAAQ8F,EAAS1F,GA+sBvF,SAAS+F,CACoB,CACzBN,CAAuB,CACvBO,CAA8D,CAC9DhC,CAAkB,EAElB7D,GACIA,EAAqB8F,OAAO,CAAC,MAAOlB,EAAWM,KAC3C,IAAMa,EACFnB,EAAK9B,cAAc,EACnB8B,EAAK9B,cAAc,CAAC5J,GAAG,CAAC,CAAC8M,EAAed,KAC7B,CAAEzL,MAAOuM,EAAS1G,KAAK,CAAE5F,MAAOsM,EAAS1C,GAAG,CAAC,GAEtD2C,EACFrB,EAAK9B,cAAc,EACnB8B,EAAK9B,cAAc,CAAC5J,GAAG,CAAC,CAACgN,EAAchB,IAC5BgB,EAAQ5C,GAAG,EAGpB6C,EACFvB,EAAK5B,mBAAmB,EACxB4B,EAAK5B,mBAAmB,CAAC9J,GAAG,CAAC,GAClB,EAAEO,MAAO2M,EAAM9G,KAAK,CAAE5F,MAAO0M,EAAM9C,GAAG,CAAC,GAEhD+C,EACFzB,EAAK5B,mBAAmB,EACxB4B,EAAK5B,mBAAmB,CAAC9J,GAAG,CAAC,GAClBoN,EAAOhD,GAAG,EAEzBgC,EAAiBiB,IAAI,CAAC,CAClB3D,gBAAiBgC,EAAKhC,eAAe,CAACU,GAAG,CACzCT,QAASkD,EACTjD,eAAgBmD,EAChBlD,aAAcoD,EACdnD,oBAAqBqD,EACrBnD,aAAc0B,EAAKhC,eAAe,CAACM,YAAY,CAACI,GAAG,CACnDL,eAAgB,MAAM4C,EAAcjB,EAAKhC,eAAe,CAACU,GAAG,CAAEO,EAClE,EACJ,EACR,EArvBuC7D,EAAsBsF,EAAkBO,GAAeT,GAC1EzC,GAAwB2C,GACxB3E,EAAa,GAAqB,EAAE,GAAG6F,CAAS,CAAE,EAAhB,CAAmBnB,CAAQ,CAAC,GACzCzF,GACfe,EAAa,GAAqB,EAAE,GAAG6F,CAAS,CAAEtM,EAAhB,OAAyB,EAAK,EAG1E,CACJ,GAHc,IAMlB+J,GAAsBJ,IACtB4C,IACJ,EAAG,EAAE,EAEL,IAAMA,GAAc,UAChB,IAAMC,EAAc,MAAMtC,EAAAA,CAAUA,CAACuC,IAAI,CAAC,uBAAwB,CAAC,GACnE,GAAID,GAAeA,EAAY,KAAQ,CAAE,GAAX,EACJA,CAClBE,CADEA,CAAAA,OAAgBF,EAAAA,EAAY,OAAZA,EAAW,GAAXA,EAAAA,EAAsBG,GAAtBH,GAA4B,CAAEI,GAA0B,2BAARA,GAA8C,SAATA,EAAS,EAClGnB,MAAM,CAAG,EACvBjD,CAD0B,GACM,GAEhCA,IAAgC,EAExC,CACJ,EAIMmD,GAAgB,MAAOrM,EAAS4L,KAClC,IAAI2B,EAAkB,EAAE,CACxB,GAAIvN,EAAI,CACJ,IAAM6L,EAAW,MAAMjB,EAAAA,CAAUA,CAACC,GAAG,CAAC,mBAAsB,OAAH7K,GAAM4L,GAC3DC,GAAYA,EAASb,IAAI,EAAE,CAC3BuC,EAAW1B,EAASb,IAAI,CAACtL,GAAG,CAAC,CAAC0L,EAAWM,KAC9B,CAAEzL,MAAOmL,EAAKtF,KAAK,CAAE5F,MAAOkL,EAAKtB,GAAG,CAAC,EAChD,EACSQ,IAAI,CAAC,CAACkD,EAAQjL,IAAWiL,EAAEvN,KAAK,CAACwN,aAAa,CAAClL,EAAEtC,KAAK,EAEvE,CACA,OAAOsN,CACX,EAIMG,GAAe,CAACC,EAAWC,KAClB,cAAPA,GAA+B,MAARD,GACvBxG,EAAa,GAAqB,EAC9B,GAAG6F,CAAS,CACZ5G,EAF8B,OAEpBuH,EACVxH,WAAYwH,EAChB,GAEJxG,EAAa,GAAqB,EAC9B,GAAG6F,CAAS,CACZ,CAACY,CAF6B,CAEzB,CAAED,EACX,EACJ,EAEME,GAAUnH,CAAAA,EAAAA,EAAAA,MAAAA,CAAMA,CAAM,MAEtBoH,GAAS,KACX,IAAMC,EAAQ,IAAIxH,GAAoB,CACtCwH,EAAMhB,IAAI,CAAC,CAAEjH,MAAO,GAAI8D,aAAc,GAAIC,MAAO,GAAIC,IAAK,IAAK,GAC/DH,GAAuBoE,GACvB/D,GAAoB+D,EAAM5B,MAAM,CACpC,EAEM6B,GAAY,CAACC,EAASC,KACxB3H,GAAoB4H,MAAM,CAACD,EAAG,GAC9B,IAAMH,EAAQ,IAAIxH,GAAoB,CACtCoD,GAAuBoE,GACvB/D,GAAoB+D,EAAM5B,MAAM,EACG,GAAG,CAAlC5F,GAAoB4F,MAAM,EAC1B2B,IAER,EAoBMM,GAAa,KACf,IAAMZ,EAAI,CACNpE,gBAAiB,GACjBM,aAAc,GACdL,QAAS,EAAE,CACXC,eAAgB,EAAE,CAClBC,aAAc,EAAE,CAChBC,oBAAqB,EAAE,CACvBC,eAAgB,EAAE,EAEtBN,GAAwB,GAAmB,IAAIkF,EAAUb,EAAE,CAC/D,EAEMc,GAAgB,CAACL,EAASC,KAC5B1H,GAAqB2H,MAAM,CAACD,EAAG,GAC/B/E,GAAwB,IAAI3C,GAAqB,EACb,GAAG,CAAnCA,GAAqB2F,MAAM,EAC3BiC,IAER,EAEMG,GAAoB,IACtBpH,EAAa,GAAqB,EAC9B,GAAG6F,CAAS,CACZ1G,EAF8B,UAEjBpG,EACjB,EACJ,EAUAV,EAAAA,SAAe,CAAC,KACZ,GAAI4H,EAAiB,CACjB,IAAMoH,EAAsB,CAAC,EAC7BC,OAAOC,IAAI,CAACtH,GAAiBkF,OAAO,CAAC,CAAClB,EAAWM,KAC7C,IAAMiD,EACF,CAAwB,CAACvD,EAAK,CAACe,MAAM,CAAG,GACxC,CAAwB,CAACf,EAAK,CAAC1L,GAAG,CAAC,CAACkP,EAAQlD,IACjCkD,EAAE1O,KAAK,EAEtBsO,CAAc,CAACpD,EAAK,CAAGuD,GAAgB,EAAE,GAAVA,GAG1BH,EACb,MACIK,CADG,OACKC,GAAG,CAAC,wBAEpB,EAAG,CAAC1H,EAAgB,EAEpB,IAAM2H,GAAW,MAAOP,IACpB,GAAM,gBAAEnH,CAAc,qBAAEG,CAAmB,CAAE,CAAGgH,EAO1CQ,EAAc,MAAMpE,EAAAA,CAAUA,CAACuC,IAAI,CAAC,eANtB,CAMsC8B,MAL/C,CACHlD,QAAS1E,EACT6D,YAAa1D,CACjB,CACJ,GAEIwH,GAAelE,MAAMC,OAAO,CAACiE,IAI7B/G,EAHe+G,EAAYtP,GAAG,CAAC,CAAC0L,CADW,CACAM,EAG9BwD,EAFF,EAAEjP,MAAOmL,EAAK+D,QAAQ,CAAEjP,MAAOkL,EAAKtB,GAAG,CAAC,GAI3D,EAKMsF,GAAa,MAAOzO,EAAQuN,EAAQnP,KACtC,GAAI4B,EAAEC,MAAM,CAAE,CACV,GAAM,CAAE7B,KAAMsQ,CAAW,OAAEnP,CAAK,CAAE,CAAGS,EAAEC,MAAM,CAC7C4F,EAAoB,CAAC0H,EAAE,CAACmB,EAAY,CAAGnP,EACpB,mBAAfmP,IACA7I,EAAoB,CAAC0H,EAAE,CAACxE,YAAY,CAChC/I,EAAEC,MAAM,CAACD,EAAEC,MAAM,CAAC0O,aAAa,CAAC,CAACC,YAAY,CAAC,oBAClD/I,EAAoB,CAAC0H,EAAE,CAACzE,cAAc,CAAG,MAAM4C,GAAcnM,EAAOmK,IACpE7D,EAAoB,CAAC0H,EAAE,CAAC7E,OAAO,CAAG,EAAE,CAE5C,KACgB,EADT,mBAC8B,IAC7B7C,EAAoB,CAAC0H,EAAE,CAAC7E,OAAO,CAAG1I,EAClC6F,EAAoB,CAAC0H,EAAE,CAAC5E,cAAc,CAAG3I,EAAEjB,GAAG,CAAC,CAAC0L,EAAWM,IAChDN,EAAKlL,KAAK,GAIb,wBAARnB,IACAyH,EAAoB,CAAC0H,EAAE,CAAC3E,YAAY,CAAG5I,EACvC6F,EAAoB,CAAC0H,EAAE,CAAC1E,mBAAmB,CAAG7I,EAAEjB,GAAG,CAAC,CAAC0L,EAAWM,IACrDN,EAAKlL,KAAK,EAErB2O,QAAQC,GAAG,CAACtI,EAAoB,CAAC0H,EAAE,CAAC3E,YAAY,EAC7C/C,EAAoB,CAAC0H,EAAE,CAAC3E,YAAY,CAAC4C,MAAM,CAC1CjC,CAD4C,EACjB,IAE3BA,GAA2B7I,EAAE,8CAKzC8H,GAAwB,IAAI3C,GAAqB,CACrD,EAEMgJ,GAAe,IACjB,GAAM,MAAEzQ,CAAI,OAAEmB,CAAK,CAAE,CAAGS,EAAEC,MAAM,CAC5BD,EAAEC,MAAM,EAAE,EACG,GAAqB,EAC9B,GAAGoM,CAAS,CACZ,CAACjO,CAF6B,CAExB,CAAEmB,EACZ,EAER,EAuBMqE,GAAe,MAAO5D,IAGxB,GAFAA,EAAE2C,cAAc,GAEsB,GAAlC4D,EAAWb,YAAY,CAAC8F,MAAM,EAA+D,GAAtD3F,EAAoB,CAAC,EAAE,CAACgD,mBAAmB,CAAC2C,MAAM,CAAO,CAChGsD,EAAAA,EAAKA,CAAC/J,KAAK,CAACrE,EAAE,0DACdqO,OAAOC,QAAQ,CAAC,EAAG,GACnBzF,GAA2B7I,EAAE,6CAC7B+I,GAAmB/I,EAAE,qCACrB,MACJ,CAEA,GAAsC,GAAlC6F,EAAWb,YAAY,CAAC8F,MAAM,CAAO,CACrCsD,EAAAA,EAAKA,CAAC/J,KAAK,CAACrE,EAAE,qCACdqO,OAAOC,QAAQ,CAAC,EAAG,GACnBvF,GAAmB/I,EAAE,qCACrB,MACJ,CAEA,GAA0D,GAAtDmF,EAAoB,CAAC,EAAE,CAACgD,mBAAmB,CAAC2C,MAAM,CAAO,CACzDsD,EAAAA,EAAKA,CAAC/J,KAAK,CAACrE,EAAE,6CACdqO,OAAOC,QAAQ,CAAC,EAAG,GACnBvF,GAAmB/I,EAAE,6CACrB,MACJ,CAEA,GAA6B,MAAzB6F,EAAWf,UAAU,CAAU,KAC/BS,SAAAA,EAAAA,EAAagJ,OAAAA,GAAbhJ,EAAsBiJ,KAAK,EAC/B,EADIjJ,GACG,KAeCiF,EACAiE,EAfArJ,EAAUmJ,OAAO,EAAE,EACTA,OAAO,CAACG,YAAY,CAAC,WAAY,YAG/C,IAAMC,EAAS3E,IAAAA,GAAK,CAChB7E,GACA6E,IAAAA,YAAc,CAACA,IAAAA,IAAAA,CAAQ,CAAC,kBAAmB,iBAAkB,sBAAuB,eAAe,GAEvGnE,EAAWb,YAAY,CAAGa,EAAWb,YAAY,CAC3Ca,EAAWb,YAAY,CAAC3G,GAAG,CAAC,CAAC0L,EAAWM,IAAYN,EAAKlL,KAAK,EAC9D,EAAE,CACRgH,EAAWX,mBAAmB,CAAGA,GACjCW,EAAWV,oBAAoB,CAAGwJ,EAClCrP,EAAE2C,cAAc,GAGZyF,IACA+G,EAAW,IADD,+BAEVjE,EAAW,MAAMjB,EAAAA,CAAUA,CAACqF,KAAK,CAAC,YAA4B,OAAhBvR,EAAMsK,MAAM,CAAC,EAAE,EAAI9B,KAEjE4I,EAAW,iCACXjE,EAAW,MAAMjB,EAAAA,CAAUA,CAACuC,IAAI,CAAC,WAAYjG,IAEjDgJ,SAsXHA,CACQ,CACb3H,CAAqB,CACrBO,CAA6B,CAC7BJ,CAAuD,CACvDrH,CAAM,CACNyO,CAAa,EAETjE,GAAYA,EAAS/B,GAAG,CACpBvB,CADsB,EAEtBO,EAAS+C,OAAAA,EAAAA,KAAAA,EAAAA,EAAU/B,GAAAA,GAAO+B,EAAS/B,EAA1B+B,CAA6B,EACtCnD,GAAS,KAET+G,EAAAA,EAAKA,CAACU,OAAO,CAAC9O,EAAEyO,IAChBrO,IAAAA,IAAW,CAAC,uBAAwB,iBAA8B,OAAboK,EAAS/B,GAAG,IAGrE2F,EAAAA,EAAKA,CAAC/J,KAAK,CAACmG,EAEpB,EAzYkCA,EAAUtD,EAAcO,GAAUJ,EAAUrH,EAAGyO,EACzE,CACJ,EACA,MACI,WAACM,EAAAA,CAASA,CAAAA,CAACvQ,UAAU,WAAWwQ,KAAK,cACjC,UAACC,EAAAA,CAAIA,CAAAA,UACD,UAAC9N,EAAAA,CAAqBA,CAAAA,CAACG,SAAU4B,GAAc7B,IAAKmL,YAChD,WAACyC,EAAAA,CAAIA,CAACC,IAAI,YACN,UAACC,EAAAA,CAAGA,CAAAA,UACA,UAACC,EAAAA,CAAGA,CAAAA,UACA,UAACH,EAAAA,CAAIA,CAACI,KAAK,WAAE3H,GAAW1H,EAAE,eAAiBA,EAAE,oBAGrD,UAACsP,KAAAA,CAAAA,GACD,WAACH,EAAAA,CAAGA,CAAAA,CAAC3Q,UAAU,iBACX,UAAC4Q,EAAAA,CAAGA,CAAAA,CAACG,GAAI,EAAGC,GAAI,EAAGC,GAAI,YACnB,WAACvQ,EAAAA,CAAIA,CAACwQ,KAAK,YACP,UAACxQ,EAAAA,CAAIA,CAACyQ,KAAK,EAACnR,UAAU,0BAAkBwB,EAAE,WAC1C,UAACN,EAAAA,EAASA,CAAAA,CACNhC,KAAK,QACLiB,GAAG,QACHyE,QAAQ,IACRvE,MAAOgH,EAAWpB,KAAK,CACvBpB,UAAW,GAAgC,IAAhBxE,EAAMiF,IAAI,GACrCjG,aAAc,CACVwF,UAAWrD,EAAE,oBACjB,EACApC,SAAUuQ,UAItB,UAACiB,EAAAA,CAAGA,CAAAA,CAACG,GAAI,EAAGC,GAAI,EAAGC,GAAI,YACnB,WAACvQ,EAAAA,CAAIA,CAACwQ,KAAK,YACP,UAACxQ,EAAAA,CAAIA,CAACyQ,KAAK,WAAE3P,EAAE,aACf,UAACN,EAAAA,EAASA,CAAAA,CACNhC,KAAK,UACLiB,GAAG,UACH8E,QAAQ,gGACR5F,aAAc,CAAE4F,QAASzD,EAAE,0BAA2B,EACtDnB,MAAOgH,EAAWnB,OAAO,CACzB9G,SAAUuQ,aAK1B,UAACgB,EAAAA,CAAGA,CAAAA,CAAC3Q,UAAU,gBACX,UAAC4Q,EAAAA,CAAGA,CAAAA,UACA,WAAClQ,EAAAA,CAAIA,CAACwQ,KAAK,YACP,UAACxQ,EAAAA,CAAIA,CAACyQ,KAAK,WAAE3P,EAAE,iBACf,UAAC4P,EAAAA,CAAeA,CAAAA,CAACC,YAAahK,EAAWZ,WAAW,CAAErH,SAAU,GAAcsP,GAAkB4C,YAI5G,WAACX,EAAAA,CAAGA,CAAAA,CAAC3Q,UAAU,2CACX,UAAC4Q,EAAAA,CAAGA,CAAAA,CAACG,GAAI,EAAGC,GAAI,EAAGC,GAAI,YACnB,WAACvQ,EAAAA,CAAIA,CAACwQ,KAAK,EAACK,MAAO,CAAEC,SAAU,OAAQ,YACnC,UAAC9Q,EAAAA,CAAIA,CAACyQ,KAAK,EAACnR,UAAU,0BACjBwB,EAAE,iCAEP,UAACiQ,EAAAA,EAAWA,CAAAA,CACRC,gBAAiB,CACbC,gBAAiBnQ,EAAE,oBACnBoQ,oBAAqBpQ,EAAE,6BAC3B,EACAqQ,QAAS/I,EACTzI,MAAOgH,EAAWb,YAAY,CAC9BpH,SA1Ib,CA0IuB0S,EA1IdC,KAC5BzK,EAAa,GAAqB,EAC9B,GAAG6F,CAAS,CACZ3G,EAF8B,WAEhB1F,EAClB,GACGA,EAAEwL,MAAM,CACP/B,CADQ,EACW,IAEnBA,GAAmB/I,EAAE,oCAE7B,EAiIoCxB,UAAW,iBACXgS,WAAYxQ,EAAE,sBAEjB8I,IAAmB,UAAC/H,IAAAA,CAAEgP,MAAO,CAAElP,MAAO,KAAM,WAAIiI,UAGzD,UAACsG,EAAAA,CAAGA,CAAAA,CAACG,GAAI,EAAGC,GAAI,EAAGC,GAAI,YACnB,WAACvQ,EAAAA,CAAIA,CAACwQ,KAAK,YACP,UAACxQ,EAAAA,CAAIA,CAACyQ,KAAK,WAAE3P,EAAE,cACf,UAACN,EAAAA,EAASA,CAAAA,CACNhC,KAAK,YACLiB,GAAG,YACHE,MAAOgH,EAAWlB,SAAS,CAC3B/G,SAAUuQ,aAK1B,WAACgB,EAAAA,CAAGA,CAAAA,CAAC3Q,UAAU,iBACX,UAAC4Q,EAAAA,CAAGA,CAAAA,CAACI,GAAI,EAAGC,GAAI,YACZ,WAACvQ,EAAAA,CAAIA,CAACwQ,KAAK,YACP,UAACxQ,EAAAA,CAAIA,CAACyQ,KAAK,WAAE3P,EAAE,mBACf,WAACL,EAAAA,EAAWA,CAAAA,CACRjC,KAAK,SACLiB,GAAG,SACHE,MAA6B,OAAtBgH,EAAWjB,MAAM,CAAY,GAAKiB,EAAWjB,MAAM,CAC1DhH,SAAUuQ,aAEV,UAACsC,SAAAA,CAAO5R,MAAM,YAAImB,EAAE,yBACnBwG,EAAcnI,GAAG,CAAC,CAAC0L,EAAM8C,IAElB,UAAC4D,SAAAA,CAAe5R,MAAOkL,EAAKtB,GAAG,UAC1BsB,EAAKtF,KAAK,EADFoI,YAQjC,UAACuC,EAAAA,CAAGA,CAAAA,CAACI,GAAI,EAAGC,GAAI,EAAGjR,UAAU,6BACzB,WAACU,EAAAA,CAAIA,CAACwQ,KAAK,YACP,UAACP,EAAAA,CAAGA,CAAAA,UACA,UAACC,EAAAA,CAAGA,CAAAA,UACA,UAAClQ,EAAAA,CAAIA,CAACyQ,KAAK,EAACnR,UAAU,0BAAkBwB,EAAE,mBAGlD,UAACpB,QAAAA,CAAMJ,UAAU,wBAAwB6C,IAAKkE,WAC1C,UAAChI,EAAAA,CAAaA,CAAAA,CACVmT,SAAU7K,EAAWf,UAAU,CAC/BlH,SAAU,GAAeyO,GAAaC,EAAM,cAC5CqE,WAAW,eACXC,gBAAiB5Q,EAAE,4BAKnC,UAACoP,EAAAA,CAAGA,CAAAA,CAACI,GAAI,EAAGC,GAAI,WACZ,UAACvQ,EAAAA,CAAIA,CAACC,KAAK,EACPC,KAAK,WACLC,QAASwG,EAAWxG,OAAO,CAC3BzB,SAlSF,CAkSYiT,IAjStC/K,EAAa,GAAqB,EAC9B,GAAG6F,CAAS,CACZtM,EAF8B,MAErB,CAACsM,EAAUtM,OAAO,CAC3B0F,SAAU,KACd,EACJ,EA6RgCnG,MAAOoB,EAAE,mBAGhB6F,EAAWxG,OAAO,EACf,UAAC+P,EAAAA,CAAGA,CAAAA,CAACI,GAAI,EAAGC,GAAI,EAAGjR,UAAU,6BACzB,WAACU,EAAAA,CAAIA,CAACwQ,KAAK,YACP,UAACP,EAAAA,CAAGA,CAAAA,UACA,UAACC,EAAAA,CAAGA,CAAAA,UACA,UAAClQ,EAAAA,CAAIA,CAACyQ,KAAK,WAAE3P,EAAE,iBAGvB,UAACzC,EAAAA,CAAaA,CAAAA,CACVmT,SAAU7K,EAAWd,QAAQ,CAC7BjG,SAAU,CAAC+G,EAAWf,UAAU,CAChClH,SAAU,GAAeyO,GAAaC,EAAM,YAC5CqE,WAAW,eACXG,QAASjL,EAAWf,UAAU,CAC9B8L,gBAAiB5Q,EAAE,2BAMtCmF,GAAqB9G,GAAG,CAAC,CAAC0L,EAAW8C,IAE9B,WAACtO,MAAAA,WACG,UAAC6Q,EAAAA,CAAGA,CAAAA,CAAC5Q,UAAU,yBAAyBgR,GAAI,YACxC,UAACuB,KAAAA,UACG,WAACC,OAAAA,WACIhR,EAAE,WAAW,IAAE6M,EAAI,SAIhC,WAACsC,EAAAA,CAAGA,CAAAA,CAAC3Q,UAAU,iBACX,UAAC4Q,EAAAA,CAAGA,CAAAA,CAACI,GAAI,EAAGC,GAAI,WACZ,WAACvQ,EAAAA,CAAIA,CAACwQ,KAAK,YACP,UAACxQ,EAAAA,CAAIA,CAACyQ,KAAK,EAACnR,UAAU,0BACjBwB,EAAE,yCAEP,WAACL,EAAAA,EAAWA,CAAAA,CACRjC,KAAK,kBACLiB,GAAG,kBACHE,MAAOkL,EAAKhC,eAAe,CAC3BnK,SAAU,GAAYmQ,GAAWzO,EAAGuN,EAAG,aACvCzJ,QAAQ,IACRvF,aAAcmC,EAAE,iCAEhB,UAACyQ,SAAAA,CAAO5R,MAAM,YAAImB,EAAE,mBACnB0G,EAAYrI,GAAG,CAAC,CAAC4S,EAAQ5G,IAElB,UAACoG,SAAAA,CAEGS,mBAAkBD,EAAO5I,YAAY,CAACI,GAAG,CACzC5J,MAAOoS,EAAOxI,GAAG,UAEhBwI,EAAOxM,KAAK,EAJR4F,YAW7B,UAAC+E,EAAAA,CAAGA,CAAAA,CAACI,GAAI,EAAGC,GAAI,WACZ,WAACvQ,EAAAA,CAAIA,CAACwQ,KAAK,EAAClR,UAAU,mBAClB,UAACU,EAAAA,CAAIA,CAACyQ,KAAK,WAAE3P,EAAE,oBACf,UAACiQ,EAAAA,EAAWA,CAAAA,CACRC,gBAAiB,CACbC,gBAAiBnQ,EAAE,iBACnBoQ,oBAAqBpQ,EAAE,wBAC3B,EACAqQ,QAAStG,EAAK3B,cAAc,CAC5BvJ,MAAOkL,EAAK/B,OAAO,CACnBpK,SAAU,GAAYmQ,GAAWzO,EAAGuN,EAAG,qBACvCrO,UAAW,SACXgS,WAAYxQ,EAAE,wBAI1B,UAACoP,EAAAA,CAAGA,CAAAA,CAACI,GAAI,EAAGC,GAAI,WACZ,WAACvQ,EAAAA,CAAIA,CAACwQ,KAAK,EAACK,MAAO,CAAEC,SAAU,OAAQ,YACnC,UAAC9Q,EAAAA,CAAIA,CAACyQ,KAAK,EAACnR,UAAU,0BACjBwB,EAAE,sCAEH,UAACiQ,EAAAA,EAAWA,CAAAA,CACRC,gBAAiB,CACbC,gBAAiBnQ,EAAE,uBACnBoQ,oBAAqBpQ,EAAE,8BAC3B,EACAqQ,QAASvJ,EACTjI,MAAOkL,EAAK7B,YAAY,CACxBtK,SAAU,GAAYmQ,GAAWzO,EAAGuN,EAAG,wBACvCrO,UAAW,eACXgS,WAAYxQ,EAAE,yBAEb4I,IAA2B,UAAC7H,IAAAA,CAAEgP,MAAO,CAAElP,MAAO,KAAM,WAAI+H,aAI7E,UAACrK,MAAAA,UACU,IAANsO,EACG,UAACmE,OAAAA,CAAAA,GAED,UAAC7B,EAAAA,CAAGA,CAAAA,CAAC3Q,UAAU,gBACX,UAAC4Q,EAAAA,CAAGA,CAAAA,CAAC+B,EAAE,IAAC3B,GAAG,aACP,UAAC4B,EAAAA,CAAMA,CAAAA,CAACC,QAAQ,YAAYC,QAAUhS,GAAW2N,GAAc3N,EAAGuN,YAC7D7M,EAAE,oBAhFjB6M,IAyFlB,UAACsC,EAAAA,CAAGA,CAAAA,UACA,UAACC,EAAAA,CAAGA,CAAAA,CAAC+B,EAAE,IAAC3B,GAAG,aACP,UAAC4B,EAAAA,CAAMA,CAAAA,CAACC,QAAQ,YAAYC,QAASvE,YAChC/M,EAAE,2BAIf,UAACsP,KAAAA,CAAAA,GACD,UAACH,EAAAA,CAAGA,CAAAA,UACA,UAACC,EAAAA,CAAGA,CAAAA,UACA,WAAClQ,EAAAA,CAAIA,CAACwQ,KAAK,YACP,UAACxQ,EAAAA,CAAIA,CAACyQ,KAAK,WAAE3P,EAAE,qDACf,WAACuR,EAAAA,CAAIA,CAAAA,CACDC,UAAW9I,GACX+I,SAAU,GAAY9I,GAAoB+I,GAC1C/S,GAAG,qCAEF,IACAuG,GAAoB7G,GAAG,CAAC,CAAC0L,EAAW8C,IAE7B,WAAC8E,EAAAA,CAAGA,CAAAA,CAASC,SAAU,GAAS,OAAN/E,EAAI,GAAKpI,MAAO,gBAAsB,OAANoI,EAAI,aAC1D,WAACsC,EAAAA,CAAGA,CAAAA,WACA,UAACC,EAAAA,CAAGA,CAAAA,CAACI,GAAI,EAAGC,GAAI,WACZ,WAACvQ,EAAAA,CAAIA,CAACwQ,KAAK,EAAClR,UAAU,iBAClB,UAACU,EAAAA,CAAIA,CAACyQ,KAAK,WAAE3P,EAAE,sBACf,UAACN,EAAAA,EAASA,CAAAA,CACNhC,KAAM,QAAc,OAANmP,EAAI,EAAE,UACpBlO,GAAI,QAAc,OAANkO,EAAI,GAChBhO,MAAOkL,EAAKtF,KAAK,CACjB7G,SAAW0B,GAAWuS,SAhe7EA,CAAavS,CAAM,CAAEuN,CAAM,EAChC,IAAMiF,EAAa,IAAI5M,GAAoB,CAC3C4M,CAAU,CAACjF,EAAE,CAACpI,KAAK,CAAGnF,EAAEC,MAAM,CAACV,KAAK,CACpCyJ,GAAuBwJ,GAC3B,EA4dmGxS,EAAGuN,UAIlD,UAACuC,EAAAA,CAAGA,CAAAA,CAACI,GAAI,EAAGC,GAAI,WACZ,WAACvQ,EAAAA,CAAIA,CAACwQ,KAAK,EAAClR,UAAU,iBAClB,UAACU,EAAAA,CAAIA,CAACyQ,KAAK,WAAE3P,EAAE,iBACf,UAACN,EAAAA,EAASA,CAAAA,CACNhC,KAAM,QAAc,OAANmP,EAAI,EAAE,iBACpBlO,GAAI,QAAc,OAANkO,EAAI,GAChBhO,MAAOkL,EAAKxB,YAAY,CACxB3K,SAAU,GAAYmU,CAretF,SAASA,CAAoB,CAAElF,CAAM,EACjC,IAAMiF,EAAa,IAAI5M,GAAoB,CAC3C4M,CAAU,CAACjF,EAAE,CAACtE,YAAY,CAAGjJ,EAAEC,MAAM,CAACV,KAAK,CAC3CyJ,GAAuBwJ,GAC3B,EAieoGxS,EAAGuN,UAInD,UAACuC,EAAAA,CAAGA,CAAAA,CAACI,GAAI,EAAGC,GAAI,WACZ,WAACvQ,EAAAA,CAAIA,CAACwQ,KAAK,EAAClR,UAAU,iBAClB,UAACU,EAAAA,CAAIA,CAACyQ,KAAK,WAAE3P,EAAE,mBACf,UAACN,EAAAA,EAASA,CAAAA,CACNhC,KAAM,QAAc,OAANmP,EAAI,GAClBlO,GAAI,QAAc,OAANkO,EAAI,GAChBhO,MAAOkL,EAAKvB,KAAK,CACjB5K,SAAU,GAAYoU,CA1etF,SAASA,CAAoB,CAAEnF,CAAM,EACjC,IAAMiF,EAAa,IAAI5M,GAAoB,CAC3C4M,CAAU,CAACjF,EAAE,CAACrE,KAAK,CAAGlJ,EAAEC,MAAM,CAACV,KAAK,CACpCyJ,GAAuBwJ,EAC3B,GAseoGxS,EAAGuN,GACvCpJ,QAAQ,wBACR5F,aAAc,CACV4F,QAASzD,EAAE,yBACf,YAKhB,UAACzB,MAAAA,UACU,IAANsO,EACG,UAACmE,OAAAA,CAAAA,GAED,UAAC5B,EAAAA,CAAGA,CAAAA,CAAC+B,EAAE,IAAC3B,GAAG,IAAIhR,UAAU,eACrB,UAAC4S,EAAAA,CAAMA,CAAAA,CACHK,SAAU,GAAY9I,GAAoB+I,GAC1CL,QAAQ,YACRC,QAAS,GAAO3E,GAAUrN,EAAGuN,YAE5B7M,EAAE,kBAlDb6M,IA0DlB,UAAC8E,EAAAA,CAAGA,CAAAA,CACAC,SAAS,MACTnN,MACI,UAAClG,MAAAA,UACG,WAACyS,OAAAA,CAAKM,QAAS7E,aACV,IACD,UAAC/L,EAAAA,CAAeA,CAAAA,CAACC,KAAMsR,EAAAA,GAAMA,CAAEpR,MAAM,6BASrE,UAACsO,EAAAA,CAAGA,CAAAA,CAAC3Q,UAAU,gBACX,WAAC4Q,EAAAA,CAAGA,CAAAA,WACA,UAACH,EAAAA,CAAIA,CAACiD,IAAI,WACN,UAAChR,IAAAA,UAAGlB,EAAE,oBAEV,UAACsP,KAAAA,CAAAA,GACD,UAACpQ,EAAAA,CAAIA,CAACC,KAAK,EACPX,UAAU,OACVM,SAAU,CAAC8I,GACXxI,KAAK,WACLxB,SAAU,IAAMuJ,EAAgB,CAACD,GACjCxJ,KAAK,UACL2B,QAAS6H,EACTtI,MAAOoB,EAAE,yCAIrB,UAACmP,EAAAA,CAAGA,CAAAA,CAAC3Q,UAAU,gBACX,WAAC4Q,EAAAA,CAAGA,CAAAA,WACA,UAACgC,EAAAA,CAAMA,CAAAA,CAAC5S,UAAU,OAAOY,KAAK,SAASiS,QAAQ,UAAUhQ,IAAK+D,EAAWkM,QAASpO,YAC7ElD,EAAE,YAEP,UAACoR,EAAAA,CAAMA,CAAAA,CAAC5S,UAAU,OAAO8S,QAjahC,CAiayCa,IAha1DrM,EAAatB,GACbsD,GAAwB,EAAE,EAC1BQ,GAAuB,EAAE,EAEzB+F,OAAOC,QAAQ,CAAC,EAAG,EACvB,EA2Z4E+C,QAAQ,gBACnDrR,EAAE,WAEP,UAACoS,IAAIA,CAACC,KAAK,WAAW/O,GAAG,oBACrB,UAAC8N,EAAAA,CAAMA,CAAAA,CAACC,QAAQ,qBAAarR,EAAE,0BAOtDoH,GAAS,UAACkL,EAAAA,CAAWA,CAAAA,CAAClT,KAAK,UAAUT,GAAI6I,MAGtD,EAwBA,eAAeoC,EACXZ,CAAkB,CAClBrC,CAA2D,CAC3DE,CAA6D,EAE7D,IAAM6D,EAAU,MAAMnB,EAAAA,CAAUA,CAACC,GAAG,CAAC,WAAYR,GAC7C0B,GAAWjB,MAAMC,OAAO,CAACgB,EAAQf,IAAI,GAAG,EACzBe,EAAQf,IAAI,EAG/B,IAAM4I,EAAY,MAAMhJ,EAAAA,CAAUA,CAACC,GAAG,CAAC,aAAcR,GACjDuJ,GAAa9I,MAAMC,OAAO,CAAC6I,EAAU5I,IAAI,GAAG,EAC3B4I,EAAU5I,IAAI,CAEvC", "sources": ["webpack://_N_E/./components/common/RKIDatePicker.tsx", "webpack://_N_E/./components/common/FormikRadio.tsx", "webpack://_N_E/./components/common/FormValidation.tsx", "webpack://_N_E/./components/common/VspaceModal.tsx", "webpack://_N_E/./components/common/ValidationFormWrapper.tsx", "webpack://_N_E/./components/common/FormikTextInput.tsx", "webpack://_N_E/./pages/project/Form.tsx"], "sourcesContent": ["//Import Library\r\nimport React from 'react'\r\nimport DatePicker from \"react-datepicker\";\r\n\r\ninterface RKIDatePickerProps {\r\n  [key: string]: any;\r\n}\r\n\r\nconst RKIDatePicker = (props: RKIDatePickerProps) => {\r\n  return (\r\n    <DatePicker {...props}  />\r\n  )\r\n};\r\n\r\nexport default RKIDatePicker;\r\n", "import React from 'react';\r\nimport { useFormikContext, Field } from 'formik';\r\nimport { Form } from 'react-bootstrap';\r\n\r\ninterface RadioItemProps {\r\n  id: string;\r\n  label: string;\r\n  value: string;\r\n  name?: string;\r\n  disabled?: boolean;\r\n}\r\n\r\ninterface RadioGroupProps {\r\n  name: string;\r\n  valueSelected: string;\r\n  onChange: (e: React.ChangeEvent<HTMLInputElement>) => void;\r\n  errorMessage?: string;\r\n  children: React.ReactNode;\r\n}\r\n\r\n// Type Guard to ensure child.props is an object\r\nfunction isObject(props: any): props is { [key: string]: any } {\r\n  return typeof props === 'object' && props !== null;\r\n}\r\n\r\nconst RadioItem: React.FC<RadioItemProps> = ({ id, label, value, name, disabled }) => {\r\n  const { values, setFieldValue } = useFormikContext<any>();\r\n  const fieldName = name || id;\r\n\r\n  return (\r\n    <Form.Check\r\n      type=\"radio\"\r\n      id={id}\r\n      label={label}\r\n      value={value}\r\n      name={fieldName}\r\n      checked={values[fieldName] === value}\r\n      onChange={(e) => {\r\n        setFieldValue(fieldName, e.target.value);\r\n      }}\r\n      disabled={disabled}\r\n      inline\r\n    />\r\n  );\r\n};\r\n\r\nconst RadioGroup: React.FC<RadioGroupProps> = ({\r\n  name,\r\n  valueSelected,\r\n  onChange,\r\n  errorMessage,\r\n  children,\r\n}) => {\r\n  const { errors, touched } = useFormikContext<any>();\r\n  const hasError = touched[name] && errors[name];\r\n\r\n  // Create a context to pass the name to RadioItems\r\n  const radioContext = React.useMemo(() => ({ name }), [name]);\r\n\r\n  // Clone children to pass the name, ensuring props are spread safely\r\n  const childrenWithProps = React.Children.map(children, (child) => {\r\n    if (React.isValidElement(child)) {\r\n      // Ensure child.props is an object before spreading\r\n      if (isObject(child.props)) {\r\n        return React.cloneElement(child as React.ReactElement<RadioItemProps>, {\r\n          name,\r\n          ...child.props, // Safely spread child.props\r\n        });\r\n      }\r\n    }\r\n    return child;\r\n  });\r\n\r\n  return (\r\n    <div>\r\n      <div className=\"radio-group\">\r\n        {childrenWithProps}\r\n      </div>\r\n      {hasError && (\r\n        <div className=\"invalid-feedback d-block\">\r\n          {errorMessage || (typeof errors[name] === 'string' ? errors[name] : String(errors[name]))}\r\n        </div>\r\n      )}\r\n    </div>\r\n  );\r\n};\r\n\r\nexport const Radio = {\r\n  RadioGroup,\r\n  RadioItem,\r\n};\r\n\r\nexport default Radio;\r\n", "// This file exports all the components needed to replace react-bootstrap4-form-validation\r\n// It serves as a drop-in replacement for the original library\r\n\r\nimport ValidationForm from './ValidationFormWrapper';\r\nimport { TextInput, SelectGroup } from './FormikTextInput';\r\nimport { Radio } from './FormikRadio';\r\n\r\n// Export all components\r\nexport {\r\n  ValidationForm,\r\n  TextInput,\r\n  SelectGroup,\r\n  Radio\r\n};\r\n\r\n// Also export as default for convenience\r\nexport default {\r\n  ValidationForm,\r\n  TextInput,\r\n  SelectGroup,\r\n  Radio\r\n};\r\n", "//Import Library\r\nimport React, { useState, useEffect } from \"react\";\r\nimport { Modal } from \"react-bootstrap\";\r\nimport { FontAwesomeIcon } from \"@fortawesome/react-fontawesome\";\r\nimport { faCheck } from \"@fortawesome/free-solid-svg-icons\";\r\nimport Router from \"next/router\";\r\n\r\n//Import services/components\r\nimport { useTranslation } from 'next-i18next';\r\n\r\nconst VSpaceModal = ({ type, id }: any) => {\r\n  const [time, setTime] = useState(5);\r\n  const { t } = useTranslation('common');\r\n\r\n  useEffect(() => {\r\n    if (time > 0) {\r\n      setTimeout(() => setTime(time - 1), 1000);\r\n    } else {\r\n      Router.push({\r\n        pathname: \"/vspace/create\",\r\n        query: { id: id, source: type },\r\n      });\r\n    }\r\n  });\r\n  return (\r\n    <Modal show={true}>\r\n      <div className=\"modal--align mt-2\">\r\n        <div className=\"modal--icon\">\r\n          <FontAwesomeIcon icon={faCheck} color=\"#4dc724\" size=\"4x\" />\r\n        </div>\r\n      </div>\r\n      <div className=\"text-center mt-4\">\r\n        <p className=\"lead\">\r\n          {type} {t(\"vspace.formSubmittedSuccessfully\")}\r\n          <br />\r\n          <small>{t(\"vspace.vspaceCreationRedirect\")}</small>\r\n          <br />\r\n          <small>\r\n            <b>\r\n              {\" \"}\r\n              {t(\"vspace.waitFor\")} {time} {t(\"vspace.waitForSec\")}\r\n            </b>\r\n          </small>\r\n        </p>\r\n      </div>\r\n    </Modal>\r\n  );\r\n};\r\n\r\nexport default VSpaceModal;\r\n", "import React, { forwardRef } from 'react';\r\nimport { Formik, Form, FormikProps, FormikHelpers } from 'formik';\r\nimport * as Yup from 'yup';\r\n\r\n// This is a wrapper component that replaces react-bootstrap4-form-validation with Formik\r\ninterface MockEvent {\r\n  preventDefault: () => void;\r\n  stopPropagation: () => void;\r\n  currentTarget: (EventTarget & Element) | null;\r\n  target: EventTarget | null;\r\n  nativeEvent: Event;\r\n  bubbles: boolean;\r\n  cancelable: boolean;\r\n  defaultPrevented: boolean;\r\n  eventPhase: number;\r\n  isTrusted: boolean;\r\n  timeStamp: number;\r\n  type: string;\r\n  isDefaultPrevented: () => boolean;\r\n  isPropagationStopped: () => boolean;\r\n  persist: () => void;\r\n}\r\n\r\ninterface ValidationFormWrapperProps {\r\n  children: React.ReactNode | ((formikProps: FormikProps<any>) => React.ReactNode);\r\n  onSubmit: (event: React.FormEvent | MockEvent, values?: Record<string, any>, actions?: FormikHelpers<Record<string, any>>) => void;\r\n  onErrorSubmit?: (errors: any) => void;\r\n  initialValues?: Record<string, any>;\r\n  enableReinitialize?: boolean;\r\n  autoComplete?: string;\r\n  className?: string;\r\n  onKeyPress?: (e: any) => void;\r\n}\r\n\r\nconst ValidationFormWrapper = forwardRef<HTMLFormElement, ValidationFormWrapperProps>((props, ref) => {\r\n  const { children, onSubmit, autoComplete, className, onKeyPress, initialValues, ...rest } = props;\r\n\r\n  // Create an empty validation schema by default\r\n  const validationSchema = Yup.object().shape({});\r\n\r\n  return (\r\n    <Formik\r\n      initialValues={initialValues || {}}\r\n      validationSchema={validationSchema}\r\n      onSubmit={(values: Record<string, any>, actions: FormikHelpers<Record<string, any>>) => {\r\n        // Create a mock event object with preventDefault method and currentTarget\r\n        const mockEvent: MockEvent = {\r\n          preventDefault: () => {},\r\n          stopPropagation: () => {},\r\n          currentTarget: null, // Set to null to avoid checkValidity errors\r\n          target: null,\r\n          nativeEvent: new Event('submit'),\r\n          bubbles: false,\r\n          cancelable: true,\r\n          defaultPrevented: false,\r\n          eventPhase: 0,\r\n          isTrusted: false,\r\n          timeStamp: Date.now(),\r\n          type: 'submit',\r\n          isDefaultPrevented: () => false,\r\n          isPropagationStopped: () => false,\r\n          persist: () => {}\r\n        };\r\n\r\n        if (onSubmit) {\r\n          // Pass the mock event object to maintain compatibility with the original code\r\n          onSubmit(mockEvent, values, actions);\r\n        }\r\n      }}\r\n      {...rest}\r\n    >\r\n      {(formikProps: FormikProps<any>) => (\r\n        <Form\r\n          ref={ref}\r\n          onSubmit={formikProps.handleSubmit}\r\n          autoComplete={autoComplete}\r\n          className={className}\r\n          onKeyPress={onKeyPress}\r\n        >\r\n          {typeof children === 'function' ? children(formikProps) : children}\r\n        </Form>\r\n      )}\r\n    </Formik>\r\n  );\r\n});\r\n\r\nValidationFormWrapper.displayName = 'ValidationFormWrapper';\r\n\r\nexport default ValidationFormWrapper;\r\n", "import React from 'react';\r\nimport { Form } from 'react-bootstrap';\r\nimport { Field, ErrorMessage, useField } from 'formik';\r\n\r\n// This component mimics the TextInput component from react-bootstrap4-form-validation\r\n// but uses Formik under the hood\r\nexport const TextInput = ({\r\n  name,\r\n  id,\r\n  required,\r\n  validator,\r\n  errorMessage,\r\n  onChange,\r\n  value,\r\n  as,\r\n  multiline,\r\n  rows,\r\n  pattern,\r\n  ...props\r\n}: any) => {\r\n  // Create a custom validation function that mimics the original validator\r\n  const validate = (val: any) => {\r\n    // Safely convert value to string and trim\r\n    const stringVal = typeof val === 'string' ? val : String(val || '');\r\n    if (required && (!val || stringVal.trim() === '')) {\r\n      return errorMessage?.validator || 'This field is required';\r\n    }\r\n\r\n    if (validator && !validator(val)) {\r\n      return errorMessage?.validator || 'Invalid value';\r\n    }\r\n\r\n    if (pattern && val) {\r\n      const regex = new RegExp(pattern);\r\n      if (!regex.test(val)) {\r\n        return errorMessage?.pattern || 'Invalid format';\r\n      }\r\n    }\r\n\r\n    return undefined;\r\n  };\r\n\r\n  return (\r\n    <Field name={name} validate={validate}>\r\n      {({ field, meta }: any) => (\r\n        <>\r\n          <Form.Control\r\n            {...field}\r\n            {...props}\r\n            id={id}\r\n            as={as || 'input'}\r\n            rows={rows}\r\n            isInvalid={meta.touched && !!meta.error}\r\n            onChange={(e: React.ChangeEvent<HTMLInputElement>) => {\r\n              field.onChange(e);\r\n              if (onChange) onChange(e);\r\n            }}\r\n            value={value !== undefined ? value : field.value}\r\n          />\r\n          {meta.touched && meta.error ? (\r\n            <Form.Control.Feedback type=\"invalid\">\r\n              {meta.error}\r\n            </Form.Control.Feedback>\r\n          ) : null}\r\n        </>\r\n      )}\r\n    </Field>\r\n  );\r\n};\r\n\r\n// This component mimics the SelectGroup component from react-bootstrap4-form-validation\r\n// but uses Formik under the hood\r\nexport const SelectGroup = ({\r\n  name,\r\n  id,\r\n  required,\r\n  errorMessage,\r\n  onChange,\r\n  value,\r\n  children,\r\n  ...props\r\n}: any) => {\r\n  // Create a custom validation function that mimics the original validator\r\n  const validate = (val: any) => {\r\n    if (required && (!val || val === '')) {\r\n      return errorMessage?.validator || 'This field is required';\r\n    }\r\n\r\n    return undefined;\r\n  };\r\n\r\n  return (\r\n    <Field name={name} validate={validate}>\r\n      {({ field, meta }: any) => (\r\n        <>\r\n          <Form.Control\r\n            as=\"select\"\r\n            {...field}\r\n            {...props}\r\n            id={id}\r\n            isInvalid={meta.touched && !!meta.error}\r\n            onChange={(e: React.ChangeEvent<HTMLSelectElement>) => {\r\n              field.onChange(e);\r\n              if (onChange) onChange(e);\r\n            }}\r\n            value={value !== undefined ? value : field.value}\r\n          >\r\n            {children}\r\n          </Form.Control>\r\n          {meta.touched && meta.error ? (\r\n            <Form.Control.Feedback type=\"invalid\">\r\n              {meta.error}\r\n            </Form.Control.Feedback>\r\n          ) : null}\r\n        </>\r\n      )}\r\n    </Field>\r\n  );\r\n};\r\n\r\n// Export both components as named exports and as a default object\r\nexport default {\r\n  TextInput,\r\n  SelectGroup\r\n};\r\n", "//Import Library\r\nimport React, { useState, useRef, useEffect } from \"react\";\r\nimport { <PERSON><PERSON>, Card, Form, Container, Row, Col, Tab, Tabs } from \"react-bootstrap\";\r\nimport { MultiSelect } from \"react-multi-select-component\";\r\nimport Router from \"next/router\";\r\nimport Link from \"next/link\";\r\nimport moment from \"moment\";\r\nimport _ from \"lodash\";\r\nimport { FontAwesomeIcon } from \"@fortawesome/react-fontawesome\";\r\nimport { faPlus } from \"@fortawesome/free-solid-svg-icons\";\r\n\r\nimport toast from 'react-hot-toast';\r\nimport { TextInput, SelectGroup } from \"../../components/common/FormValidation\";\r\nimport ValidationFormWrapper from \"../../components/common/ValidationFormWrapper\";\r\n\r\n//Import services/components\r\nimport RKIDatePicker from \"../../components/common/RKIDatePicker\";\r\nimport apiService from \"../../services/apiService\";\r\nimport { useTranslation } from 'next-i18next';\r\nimport VspaceModal from \"./../../components/common/VspaceModal\";\r\nimport { EditorComponent } from \"../../shared/quill-editor/quill-editor.component\";\r\n\r\n//***********Declaration for Initial State**************//\r\nconst initialState = {\r\n    title: \"\",\r\n    website: \"\",\r\n    funded_by: \"\",\r\n    status: null,\r\n    countryTerritory: \"\",\r\n    start_date: null,\r\n    end_date: null,\r\n    area_of_work: [],\r\n    description: \"\",\r\n    institution_invites: [],\r\n    partner_institutions: [],\r\n    checked: false,\r\n};\r\n//***********End of Declaration for Initial State**************//\r\n\r\nconst ProjectForm = (props: any) => {\r\n    const buttonRef = useRef<any>(null);\r\n    const startDateRef = useRef<any>(null);\r\n    const { t, i18n } = useTranslation('common');\r\n    const titleSearch = i18n.language === \"de\" ? { title_de: \"asc\" } : { title: \"asc\" };\r\n    const currentLang = i18n.language;\r\n    const [initialVal, setIntialVal] = useState<any>(initialState);\r\n    const [groupVisibility] = useState<any>({\r\n        invitesCountry: [],\r\n        invitesRegion: [],\r\n        invitesOrganisationType: [],\r\n        invitesOrganisation: [],\r\n        invitesExpertise: [],\r\n        invitesNetWork: [],\r\n        userList: [],\r\n        visibility: true,\r\n    });\r\n    const [statusproject, setProjectStatus] = useState<any[]>([]);\r\n    const [countryList, setcountryList] = useState<any[]>([]);\r\n    const [, setUsersList] = useState<any[]>([]);\r\n    const [, setExpertiseList] = useState<any[]>([]);\r\n    const [organisationList, setorganisationList] = useState<any[]>([]);\r\n    const [, setOrganisationTypeList] = useState<any[]>([]);\r\n    const [, setNetworkList] = useState<any[]>([]);\r\n    const [virtualSpace, setVirtualSpace] = useState<boolean>(false);\r\n    const [modal, setModal] = useState<boolean>(false);\r\n    const [areaofWorkList, setAreaOfWorkList] = useState<any[]>([]);\r\n    const [resId, setResId] = useState<any>(null);\r\n    const editform: boolean = props.routes && props.routes[0] == \"edit\" && props.routes[1];\r\n    const [virtualSpaceAccessPermission, setVirtualSpaceAccessPermission] = useState<boolean>(true);\r\n    const [partner_institutions, setpartner_institutions] = useState<any[]>([\r\n        {\r\n            partner_country: \"\",\r\n            regions: [],\r\n            partner_region: [],\r\n            institutions: [],\r\n            partner_institution: [],\r\n            countryregions: [],\r\n            world_region: \"\",\r\n        },\r\n    ]);\r\n    const [institution_invites, setinstitution_invites] = useState<any[]>([\r\n        {\r\n            title: \"\",\r\n            contact_name: \"\",\r\n            email: \"\",\r\n            _id: null,\r\n        },\r\n    ]);\r\n    const [defaultActiveKey, setdefaultActiveKey] = useState<number>(1);\r\n    const [partnerInstitutionError, setPartnerInstitutionError] = useState(\"\");\r\n    const [areaOfWorkError, setAreaOfWorkError] = useState(\"\");\r\n\r\n    const projectParams = {\r\n        query: {},\r\n        sort: titleSearch,\r\n        limit: \"~\",\r\n        languageCode: currentLang,\r\n    };\r\n\r\n    const getProjectInitialData = async (projectParamsinitalsvalue: any) => {\r\n        const projectStatus = await apiService.get(\"/projectstatus\", projectParamsinitalsvalue);\r\n        if (projectStatus && Array.isArray(projectStatus.data)) {\r\n            setProjectStatus(projectStatus.data);\r\n        }\r\n\r\n        await responseproject(projectParamsinitalsvalue, setcountryList, setExpertiseList);\r\n\r\n        const institution = await apiService.get(\"/institution\", projectParamsinitalsvalue);\r\n        if (institution && Array.isArray(institution.data)) {\r\n            const filtered = institution.data.map((item: any) => {\r\n                if (item.status !== \"Request Pending\") {\r\n                    return { label: item.title, value: item._id };\r\n                }\r\n            });\r\n            setorganisationList(_.compact(filtered));\r\n        }\r\n\r\n        const institutionType = await apiService.get(\"/institutiontype\", projectParamsinitalsvalue);\r\n        if (institutionType && Array.isArray(institutionType.data)) {\r\n            setOrganisationTypeList(institutionType.data);\r\n        }\r\n\r\n        const institutionNetwork = await apiService.get(\"/institutionnetwork\", projectParamsinitalsvalue);\r\n        if (institutionNetwork && Array.isArray(institutionNetwork.data)) {\r\n            setNetworkList(institutionNetwork.data);\r\n        }\r\n\r\n        const areaOfWork = await apiService.get(\"/areaofwork\", projectParamsinitalsvalue);\r\n        let _area: any[] = [];\r\n        if (areaOfWork && Array.isArray(areaOfWork.data)) {\r\n            _area = areaOfWork.data.map((item: any, _i: any) => {\r\n                return { label: item.title, value: item._id };\r\n            });\r\n            setAreaOfWorkList(_area);\r\n        }\r\n    };\r\n\r\n    useEffect(() => {\r\n        if (editform) {\r\n            const getProjectData = async (projectParamsinitial: any) => {\r\n                const response = await apiService.get(`/project/${props.routes[1]}`, projectParamsinitial);\r\n                if (response) {\r\n                    const normalizePartner: any[] = [];\r\n                    const {\r\n                        status,\r\n                        country,\r\n                        area_of_work,\r\n                        partner_institutions,\r\n                        start_date,\r\n                        end_date,\r\n                    } = response;\r\n                    responceStartdate(response, start_date, end_date, status, country, area_of_work);\r\n                    parternerInstution(partner_institutions, normalizePartner, countryRegion, projectParamsinitial);\r\n                    setpartner_institutions(normalizePartner);\r\n                    setIntialVal((prevState: any) => ({ ...prevState, ...response }));\r\n                    const checkEndDate = end_date\r\n                        ? setIntialVal((prevState: any) => ({ ...prevState, checked: true }))\r\n                        : null;\r\n                    return checkEndDate;\r\n                }\r\n            };\r\n            getProjectData(projectParams);\r\n        }\r\n        getProjectInitialData(projectParams);\r\n        initialData();\r\n    }, []);\r\n\r\n    const initialData = async () => {\r\n        const currentUser = await apiService.post(\"/users/getLoggedUser\", {});\r\n        if (currentUser && currentUser['roles']) {\r\n            const filteredRoles = currentUser['roles']?.filter((role: string) => (role == \"EMT_NATIONAL_FOCALPOINT\" || role === \"NGOS\"));\r\n            if (filteredRoles.length > 0) {\r\n                setVirtualSpaceAccessPermission(false);\r\n            } else {\r\n                setVirtualSpaceAccessPermission(true);\r\n            }\r\n        }\r\n    };\r\n\r\n    //*******To Clear the value of the fields*******//\r\n    //*******Get the regions from the country_region api*******//\r\n    const countryRegion = async (id: any, projectParamsinitial: any) => {\r\n        let _regions: any[] = [];\r\n        if (id) {\r\n            const response = await apiService.get(`/country_region/${id}`, projectParamsinitial);\r\n            if (response && response.data) {\r\n                _regions = response.data.map((item: any, _i: any) => {\r\n                    return { label: item.title, value: item._id };\r\n                });\r\n                _regions.sort((a: any, b: any) => a.label.localeCompare(b.label));\r\n            }\r\n        }\r\n        return _regions;\r\n    };\r\n    //******Get the regions for addcountry form******//\r\n    //*****Get the regions for GroupVisibility*****//\r\n    //*****To handle the Date pickers*****//\r\n    const onChangeDate = (date: any, key: any) => {\r\n        if (key == \"start_date\" && date == null) {\r\n            setIntialVal((prevState: any) => ({\r\n                ...prevState,\r\n                end_date: date,\r\n                start_date: date,\r\n            }));\r\n        }\r\n        setIntialVal((prevState: any) => ({\r\n            ...prevState,\r\n            [key]: date,\r\n        }));\r\n    };\r\n\r\n    const formRef = useRef<any>(null);\r\n    //*****To add the Tabs******//\r\n    const tabAdd = () => {\r\n        const _temp = [...institution_invites];\r\n        _temp.push({ title: \"\", contact_name: \"\", email: \"\", _id: null });\r\n        setinstitution_invites(_temp);\r\n        setdefaultActiveKey(_temp.length);\r\n    };\r\n    //******To remove the Tabs*****//\r\n    const removeTab = (_e: any, i: any) => {\r\n        institution_invites.splice(i, 1);\r\n        const _temp = [...institution_invites];\r\n        setinstitution_invites(_temp);\r\n        setdefaultActiveKey(_temp.length);\r\n        if (institution_invites.length === 0) {\r\n            tabAdd();\r\n        }\r\n    };\r\n\r\n    function handleOrgTab(e: any, i: any) {\r\n        const _tempCosts = [...institution_invites];\r\n        _tempCosts[i].title = e.target.value;\r\n        setinstitution_invites(_tempCosts);\r\n    }\r\n\r\n    function handleContTab(e: any, i: any) {\r\n        const _tempCosts = [...institution_invites];\r\n        _tempCosts[i].contact_name = e.target.value;\r\n        setinstitution_invites(_tempCosts);\r\n    }\r\n\r\n    function handleMailTab(e: any, i: any) {\r\n        const _tempCosts = [...institution_invites];\r\n        _tempCosts[i].email = e.target.value;\r\n        setinstitution_invites(_tempCosts);\r\n    }\r\n    //******To handle Add Country Button******//\r\n    const addcountry = () => {\r\n        const a = {\r\n            partner_country: \"\",\r\n            world_region: \"\",\r\n            regions: [],\r\n            partner_region: [],\r\n            institutions: [],\r\n            partner_institution: [],\r\n            countryregions: [],\r\n        };\r\n        setpartner_institutions((oldArray: any) => [...oldArray, a]);\r\n    };\r\n    //******To handle Remove Country Button******//\r\n    const removecountry = (_e: any, i: any) => {\r\n        partner_institutions.splice(i, 1);\r\n        setpartner_institutions([...partner_institutions]);\r\n        if (partner_institutions.length === 0) {\r\n            addcountry();\r\n        }\r\n    };\r\n\r\n    const handleDescription = (value: any) => {\r\n        setIntialVal((prevState: any) => ({\r\n            ...prevState,\r\n            description: value,\r\n        }));\r\n    };\r\n\r\n    const handleEndDateCheckBox = () => {\r\n        setIntialVal((prevState: any) => ({\r\n            ...prevState,\r\n            checked: !prevState.checked,\r\n            end_date: null,\r\n        }));\r\n    };\r\n\r\n    React.useEffect(() => {\r\n        if (groupVisibility) {\r\n            const normalizeGroup: any = {};\r\n            Object.keys(groupVisibility).forEach((item: any, _i: any) => {\r\n                const _data: any[] =\r\n                    (groupVisibility as any)[item].length > 0 &&\r\n                    (groupVisibility as any)[item].map((d: any, _i: any) => {\r\n                        return d.value;\r\n                    });\r\n                normalizeGroup[item] = _data ? _data : [];\r\n            });\r\n\r\n            getUsers(normalizeGroup);\r\n        } else {\r\n            console.log(\"No threshold reached.\");\r\n        }\r\n    }, [groupVisibility]);\r\n\r\n    const getUsers = async (normalizeGroup: any) => {\r\n        const { invitesCountry, invitesOrganisation } = normalizeGroup;\r\n        const groupParams = {\r\n            query: {\r\n                country: invitesCountry,\r\n                institution: invitesOrganisation,\r\n            },\r\n        };\r\n        const userInvites = await apiService.post(\"/user-invite\", groupParams);\r\n        if (userInvites && Array.isArray(userInvites)) {\r\n            const _users = userInvites.map((item: any, _i: any) => {\r\n                return { label: item.username, value: item._id };\r\n            });\r\n            setUsersList(_users);\r\n        }\r\n    };\r\n\r\n    //******To Handle Group Visibility******//\r\n\r\n    //To handle Add Organisation//\r\n    const handleForm = async (e: any, i: any, name: any) => {\r\n        if (e.target) {\r\n            const { name: nameInitial, value } = e.target;\r\n            partner_institutions[i][nameInitial] = value;\r\n            if (nameInitial == \"partner_country\") {\r\n                partner_institutions[i].world_region =\r\n                    e.target[e.target.selectedIndex].getAttribute(\"data-worldregion\");\r\n                partner_institutions[i].countryregions = await countryRegion(value, projectParams);\r\n                partner_institutions[i].regions = [];\r\n            }\r\n        } else {\r\n            if (name == \"countries_regions\") {\r\n                partner_institutions[i].regions = e;\r\n                partner_institutions[i].partner_region = e.map((item: any, _i: any) => {\r\n                    return item.value;\r\n                });\r\n            }\r\n\r\n            if (name == \"partner_institutions\") {\r\n                partner_institutions[i].institutions = e;\r\n                partner_institutions[i].partner_institution = e.map((item: any, _i: any) => {\r\n                    return item.value;\r\n                });\r\n                console.log(partner_institutions[i].institutions);\r\n                if(partner_institutions[i].institutions.length) {\r\n                    setPartnerInstitutionError('')\r\n                } else {\r\n                    setPartnerInstitutionError(t(\"toast.PartnerInstitutionshouldnotbeempty\"))\r\n                }\r\n                    \r\n            }\r\n        }\r\n        setpartner_institutions([...partner_institutions]);\r\n    };\r\n\r\n    const handleChange = (e: any) => {\r\n        const { name, value } = e.target;\r\n        if (e.target) {\r\n            setIntialVal((prevState: any) => ({\r\n                ...prevState,\r\n                [name]: value,\r\n            }));\r\n        }\r\n    };\r\n\r\n    const bindAreaOfWork = (e: any, _name: any) => {\r\n        setIntialVal((prevState: any) => ({\r\n            ...prevState,\r\n            area_of_work: e,\r\n        }));\r\n        if(e.length){\r\n            setAreaOfWorkError('')\r\n        } else {\r\n            setAreaOfWorkError(t(\"toast.AreaofWorkshouldnotbeempty\"))\r\n        }\r\n    };\r\n\r\n    const resetHandler = () => {\r\n        setIntialVal(initialState);\r\n        setpartner_institutions([]);\r\n        setinstitution_invites([]);\r\n        // Reset validation state (Formik handles this automatically)\r\n        window.scrollTo(0, 0);\r\n    };\r\n\r\n    //******Form Submit********//\r\n    const handleSubmit = async (e: any) => {\r\n        e.preventDefault();\r\n\r\n        if (initialVal.area_of_work.length == 0 && partner_institutions[0].partner_institution.length == 0) {\r\n            toast.error(t(\"toast.AreaofWorkandPartnerInstitutionshouldnotbeempty\"));\r\n            window.scrollTo(0, 0);\r\n            setPartnerInstitutionError(t(\"toast.PartnerInstitutionshouldnotbeempty\"))\r\n            setAreaOfWorkError(t(\"toast.AreaofWorkshouldnotbeempty\"))\r\n            return;\r\n        }\r\n\r\n        if (initialVal.area_of_work.length == 0) {\r\n            toast.error(t(\"toast.AreaofWorkshouldnotbeempty\"));\r\n            window.scrollTo(0, 0);\r\n            setAreaOfWorkError(t(\"toast.AreaofWorkshouldnotbeempty\"));\r\n            return;\r\n        }\r\n\r\n        if (partner_institutions[0].partner_institution.length == 0) {\r\n            toast.error(t(\"toast.PartnerInstitutionshouldnotbeempty\"));\r\n            window.scrollTo(0, 0);\r\n            setAreaOfWorkError(t(\"toast.PartnerInstitutionshouldnotbeempty\"))\r\n            return;\r\n        }\r\n\r\n        if (initialVal.start_date == null) {\r\n            startDateRef.current?.focus();\r\n        } else {\r\n            if (buttonRef.current) {\r\n                buttonRef.current.setAttribute(\"disabled\", \"disabled\");\r\n            }\r\n\r\n            const mapped = _.map(\r\n                partner_institutions,\r\n                _.partialRight(_.pick, [\"partner_country\", \"partner_region\", \"partner_institution\", \"world_region\"])\r\n            );\r\n            initialVal.area_of_work = initialVal.area_of_work\r\n                ? initialVal.area_of_work.map((item: any, _i: any) => item.value)\r\n                : [];\r\n            initialVal.institution_invites = institution_invites;\r\n            initialVal.partner_institutions = mapped;\r\n            e.preventDefault();\r\n            let response: any;\r\n            let toastMsg: string;\r\n            if (editform) {\r\n                toastMsg = \"toast.Projectupdatedsuccessfully\";\r\n                response = await apiService.patch(`/project/${props.routes[1]}`, initialVal);\r\n            } else {\r\n                toastMsg = \"toast.Projectaddedsuccessfully\";\r\n                response = await apiService.post(\"/project\", initialVal);\r\n            }\r\n            vspace_condition_func(response, virtualSpace, setResId, setModal, t, toastMsg);\r\n        }\r\n    };\r\n    return (\r\n        <Container className=\"formCard\" fluid>\r\n            <Card>\r\n                <ValidationFormWrapper onSubmit={handleSubmit} ref={formRef}>\r\n                    <Card.Body>\r\n                        <Row>\r\n                            <Col>\r\n                                <Card.Title>{editform ? t(\"editProject\") : t(\"addProject\")}</Card.Title>\r\n                            </Col>\r\n                        </Row>\r\n                        <hr />\r\n                        <Row className=\"mb-3\">\r\n                            <Col md={6} lg={6} sm={12}>\r\n                                <Form.Group>\r\n                                    <Form.Label className=\"required-field\">{t(\"Title\")}</Form.Label>\r\n                                    <TextInput\r\n                                        name=\"title\"\r\n                                        id=\"title\"\r\n                                        required\r\n                                        value={initialVal.title}\r\n                                        validator={(value: any) => value.trim() != \"\"}\r\n                                        errorMessage={{\r\n                                            validator: t(\"PleaseAddtheTitle\"),\r\n                                        }}\r\n                                        onChange={handleChange}\r\n                                    />\r\n                                </Form.Group>\r\n                            </Col>\r\n                            <Col md={6} lg={6} sm={12}>\r\n                                <Form.Group>\r\n                                    <Form.Label>{t(\"Website\")}</Form.Label>\r\n                                    <TextInput\r\n                                        name=\"website\"\r\n                                        id=\"website\"\r\n                                        pattern=\"^(?:http(s)?:\\/\\/)?[\\w.-]+(?:\\.[\\w\\.-]+)+[\\w\\-\\._~:/?#[\\]@!\\$&'\\(\\)\\*\\+,;=.]+$\"\r\n                                        errorMessage={{ pattern: t(\"Pleaseentervalidwebsite\") }}\r\n                                        value={initialVal.website}\r\n                                        onChange={handleChange}\r\n                                    />\r\n                                </Form.Group>\r\n                            </Col>\r\n                        </Row>\r\n                        <Row className=\"mb-3\">\r\n                            <Col>\r\n                                <Form.Group>\r\n                                    <Form.Label>{t(\"Description\")}</Form.Label>\r\n                                    <EditorComponent initContent={initialVal.description} onChange={(evt: any) => handleDescription(evt)} />\r\n                                </Form.Group>\r\n                            </Col>\r\n                        </Row>\r\n                        <Row className=\"d-flex align-items-center mb-3\">\r\n                            <Col md={6} lg={6} sm={12}>\r\n                                <Form.Group style={{ maxWidth: \"500px\" }}>\r\n                                    <Form.Label className=\"required-field\">\r\n                                        {t(\"AreaofWorkthisprojectcovers\")}\r\n                                    </Form.Label>\r\n                                    <MultiSelect\r\n                                        overrideStrings={{\r\n                                            selectSomeItems: t(\"SelectAreaofwork\"),\r\n                                            allItemsAreSelected: t(\"AllAreaofwork'sareSelected\"),\r\n                                        }}\r\n                                        options={areaofWorkList}\r\n                                        value={initialVal.area_of_work}\r\n                                        onChange={bindAreaOfWork}\r\n                                        className={\"project-covers\"}\r\n                                        labelledBy={t(\"Selectareaofwork\")}\r\n                                    />\r\n                                    {areaOfWorkError && <p style={{ color: \"red\" }}>{areaOfWorkError}</p>}\r\n                                </Form.Group>\r\n                            </Col>\r\n                            <Col md={6} lg={6} sm={12}>\r\n                                <Form.Group>\r\n                                    <Form.Label>{t(\"FundedBy\")}</Form.Label>\r\n                                    <TextInput\r\n                                        name=\"funded_by\"\r\n                                        id=\"funded_by\"\r\n                                        value={initialVal.funded_by}\r\n                                        onChange={handleChange}\r\n                                    />\r\n                                </Form.Group>\r\n                            </Col>\r\n                        </Row>\r\n                        <Row className=\"mb-3\">\r\n                            <Col lg={3} sm={12}>\r\n                                <Form.Group>\r\n                                    <Form.Label>{t(\"ProjectStatus\")}</Form.Label>\r\n                                    <SelectGroup\r\n                                        name=\"status\"\r\n                                        id=\"status\"\r\n                                        value={initialVal.status === null ? \"\" : initialVal.status}\r\n                                        onChange={handleChange}\r\n                                    >\r\n                                        <option value=\"\">{t(\"SelectProjectStatus\")}</option>\r\n                                        {statusproject.map((item, i) => {\r\n                                            return (\r\n                                                <option key={i} value={item._id}>\r\n                                                    {item.title}\r\n                                                </option>\r\n                                            );\r\n                                        })}\r\n                                    </SelectGroup>\r\n                                </Form.Group>\r\n                            </Col>\r\n                            <Col lg={3} sm={4} className=\"align-self-center\">\r\n                                <Form.Group>\r\n                                    <Row>\r\n                                        <Col>\r\n                                            <Form.Label className=\"required-field\">{t(\"StartDate\")}</Form.Label>\r\n                                        </Col>\r\n                                    </Row>\r\n                                    <label className=\"date-validation w-100\" ref={startDateRef}>\r\n                                        <RKIDatePicker\r\n                                            selected={initialVal.start_date}\r\n                                            onChange={(date: any) => onChangeDate(date, \"start_date\")}\r\n                                            dateFormat=\"MMMM d, yyyy\"\r\n                                            placeholderText={t(\"SelectStartDate\")}\r\n                                        />\r\n                                    </label>\r\n                                </Form.Group>\r\n                            </Col>\r\n                            <Col lg={2} sm={4}>\r\n                                <Form.Check\r\n                                    type=\"checkbox\"\r\n                                    checked={initialVal.checked}\r\n                                    onChange={handleEndDateCheckBox}\r\n                                    label={t(\"ShowEndDate\")}\r\n                                />\r\n                            </Col>\r\n                            {initialVal.checked && (\r\n                                <Col lg={3} sm={4} className=\"align-self-center\">\r\n                                    <Form.Group>\r\n                                        <Row>\r\n                                            <Col>\r\n                                                <Form.Label>{t(\"EndDate\")}</Form.Label>\r\n                                            </Col>\r\n                                        </Row>\r\n                                        <RKIDatePicker\r\n                                            selected={initialVal.end_date}\r\n                                            disabled={!initialVal.start_date}\r\n                                            onChange={(date: any) => onChangeDate(date, \"end_date\")}\r\n                                            dateFormat=\"MMMM d, yyyy\"\r\n                                            minDate={initialVal.start_date}\r\n                                            placeholderText={t(\"SelectEndDate\")}\r\n                                        />\r\n                                    </Form.Group>\r\n                                </Col>\r\n                            )}\r\n                        </Row>\r\n                        {partner_institutions.map((item: any, i: number) => {\r\n                            return (\r\n                                <div key={i}>\r\n                                    <Col className=\"header-block pb-1 pt-2\" lg={12}>\r\n                                        <h6>\r\n                                            <span>\r\n                                                {t(\"Country\")} {i + 1}\r\n                                            </span>\r\n                                        </h6>\r\n                                    </Col>\r\n                                    <Row className=\"mb-3\">\r\n                                        <Col lg={4} sm={6}>\r\n                                            <Form.Group>\r\n                                                <Form.Label className=\"required-field\">\r\n                                                    {t(\"CountryWheretheProjectistakingplace\")}\r\n                                                </Form.Label>\r\n                                                <SelectGroup\r\n                                                    name=\"partner_country\"\r\n                                                    id=\"partner_country\"\r\n                                                    value={item.partner_country}\r\n                                                    onChange={(e: any) => handleForm(e, i, \"countries\")}\r\n                                                    required\r\n                                                    errorMessage={t(\"thisfieldisrequired\")}\r\n                                                >\r\n                                                    <option value=\"\">{t(\"SelectCountry\")}</option>\r\n                                                    {countryList.map((C_item, _i) => {\r\n                                                        return (\r\n                                                            <option\r\n                                                                key={_i}\r\n                                                                data-worldregion={C_item.world_region._id}\r\n                                                                value={C_item._id}\r\n                                                            >\r\n                                                                {C_item.title}\r\n                                                            </option>\r\n                                                        );\r\n                                                    })}\r\n                                                </SelectGroup>\r\n                                            </Form.Group>\r\n                                        </Col>\r\n                                        <Col lg={4} sm={6}>\r\n                                            <Form.Group className=\"mw-100\">\r\n                                                <Form.Label>{t(\"CountryRegions\")}</Form.Label>\r\n                                                <MultiSelect\r\n                                                    overrideStrings={{\r\n                                                        selectSomeItems: t(\"SelectRegions\"),\r\n                                                        allItemsAreSelected: t(\"AllRegionsareSelected\"),\r\n                                                    }}\r\n                                                    options={item.countryregions}\r\n                                                    value={item.regions}\r\n                                                    onChange={(e: any) => handleForm(e, i, \"countries_regions\")}\r\n                                                    className={\"region\"}\r\n                                                    labelledBy={t(\"SelectRegions\")}\r\n                                                />\r\n                                            </Form.Group>\r\n                                        </Col>\r\n                                        <Col lg={4} sm={6}>\r\n                                            <Form.Group style={{ maxWidth: \"400px\" }}>\r\n                                                <Form.Label className=\"required-field\">\r\n                                                    {t(\"PartnerOrganisations(onplatform)\")}\r\n                                                </Form.Label>\r\n                                                    <MultiSelect\r\n                                                        overrideStrings={{\r\n                                                            selectSomeItems: t(\"SelectOrganisations\"),\r\n                                                            allItemsAreSelected: t(\"AllOrganisationsareselected\"),\r\n                                                        }}\r\n                                                        options={organisationList}\r\n                                                        value={item.institutions}\r\n                                                        onChange={(e: any) => handleForm(e, i, \"partner_institutions\")}\r\n                                                        className={\"organisation\"}\r\n                                                        labelledBy={t(\"SelectOrganisations\")}\r\n                                                        />\r\n                                                        {partnerInstitutionError && <p style={{ color: \"red\" }}>{partnerInstitutionError}</p>}\r\n                                            </Form.Group>\r\n                                        </Col>\r\n                                    </Row>\r\n                                    <div>\r\n                                        {i === 0 ? (\r\n                                            <span></span>\r\n                                        ) : (\r\n                                            <Row className=\"mb-4\">\r\n                                                <Col xs lg=\"4\">\r\n                                                    <Button variant=\"secondary\" onClick={(e: any) => removecountry(e, i)}>\r\n                                                        {t(\"Remove\")}\r\n                                                    </Button>\r\n                                                </Col>\r\n                                            </Row>\r\n                                        )}\r\n                                    </div>\r\n                                </div>\r\n                            );\r\n                        })}\r\n                        <Row>\r\n                            <Col xs lg=\"4\">\r\n                                <Button variant=\"secondary\" onClick={addcountry}>\r\n                                    {t(\"AddAnotherCountry\")}\r\n                                </Button>\r\n                            </Col>\r\n                        </Row>\r\n                        <hr />\r\n                        <Row>\r\n                            <Col>\r\n                                <Form.Group>\r\n                                    <Form.Label>{t(\"PartnerOrganisationnotlisted?Createnewandinvite\")}</Form.Label>\r\n                                    <Tabs\r\n                                        activeKey={defaultActiveKey}\r\n                                        onSelect={(k: any) => setdefaultActiveKey(k)}\r\n                                        id=\"uncontrolled-tab-example\"\r\n                                    >\r\n                                        {\" \"}\r\n                                        {institution_invites.map((item: any, i: number) => {\r\n                                            return (\r\n                                                <Tab key={i} eventKey={`${i + 1}`} title={`Organisation ${i + 1}`}>\r\n                                                    <Row>\r\n                                                        <Col lg={4} sm={6}>\r\n                                                            <Form.Group className=\"pt-4\">\r\n                                                                <Form.Label>{t(\"OrganisationName\")}</Form.Label>\r\n                                                                <TextInput\r\n                                                                    name={`input${i + 1}-title`}\r\n                                                                    id={`input${i + 1}`}\r\n                                                                    value={item.title}\r\n                                                                    onChange={(e: any) => handleOrgTab(e, i)}\r\n                                                                />\r\n                                                            </Form.Group>\r\n                                                        </Col>\r\n                                                        <Col lg={4} sm={6}>\r\n                                                            <Form.Group className=\"pt-4\">\r\n                                                                <Form.Label>{t(\"ContactName\")}</Form.Label>\r\n                                                                <TextInput\r\n                                                                    name={`input${i + 1}-contact_name`}\r\n                                                                    id={`input${i + 1}`}\r\n                                                                    value={item.contact_name}\r\n                                                                    onChange={(e: any) => handleContTab(e, i)}\r\n                                                                />\r\n                                                            </Form.Group>\r\n                                                        </Col>\r\n                                                        <Col lg={4} sm={6}>\r\n                                                            <Form.Group className=\"pt-4\">\r\n                                                                <Form.Label>{t(\"E-MailAddress\")}</Form.Label>\r\n                                                                <TextInput\r\n                                                                    name={`input${i + 1}`}\r\n                                                                    id={`input${i + 1}`}\r\n                                                                    value={item.email}\r\n                                                                    onChange={(e: any) => handleMailTab(e, i)}\r\n                                                                    pattern=\"^[^@]+@[^@]+\\.[^@]+$\"\r\n                                                                    errorMessage={{\r\n                                                                        pattern: t(\"Pleaseenteravalidemail\"),\r\n                                                                    }}\r\n                                                                />\r\n                                                            </Form.Group>\r\n                                                        </Col>\r\n                                                    </Row>\r\n                                                    <div>\r\n                                                        {i === 0 ? (\r\n                                                            <span></span>\r\n                                                        ) : (\r\n                                                            <Col xs lg=\"4\" className=\"p-0\">\r\n                                                                <Button\r\n                                                                    onSelect={(k: any) => setdefaultActiveKey(k)}\r\n                                                                    variant=\"secondary\"\r\n                                                                    onClick={(e) => removeTab(e, i)}\r\n                                                                >\r\n                                                                    {t(\"Remove\")}\r\n                                                                </Button>\r\n                                                            </Col>\r\n                                                        )}\r\n                                                    </div>\r\n                                                </Tab>\r\n                                            );\r\n                                        })}\r\n                                        <Tab\r\n                                            eventKey=\"add\"\r\n                                            title={\r\n                                                <div>\r\n                                                    <span onClick={tabAdd}>\r\n                                                        {\" \"}\r\n                                                        <FontAwesomeIcon icon={faPlus} color=\"#808080\" />\r\n                                                    </span>\r\n                                                </div>\r\n                                            }\r\n                                        ></Tab>\r\n                                    </Tabs>\r\n                                </Form.Group>\r\n                            </Col>\r\n                        </Row>\r\n                        <Row className=\"mt-4\">\r\n                            <Col>\r\n                                <Card.Text>\r\n                                    <b>{t(\"VirtualSpace\")}</b>\r\n                                </Card.Text>\r\n                                <hr />\r\n                                <Form.Check\r\n                                    className=\"pb-4\"\r\n                                    disabled={!virtualSpaceAccessPermission}\r\n                                    type=\"checkbox\"\r\n                                    onChange={() => setVirtualSpace(!virtualSpace)}\r\n                                    name=\"virtula\"\r\n                                    checked={virtualSpace}\r\n                                    label={t(\"WouldliketocreateaVirtualSpace\")}\r\n                                />\r\n                            </Col>\r\n                        </Row>\r\n                        <Row className=\"my-4\">\r\n                            <Col>\r\n                                <Button className=\"me-2\" type=\"submit\" variant=\"primary\" ref={buttonRef} onClick={handleSubmit}>\r\n                                    {t(\"submit\")}\r\n                                </Button>\r\n                                <Button className=\"me-2\" onClick={resetHandler} variant=\"info\">\r\n                                    {t(\"reset\")}\r\n                                </Button>\r\n                                <Link href=\"/project\" as=\"/project\" >\r\n                                    <Button variant=\"secondary\">{t(\"Cancel\")}</Button>\r\n                                </Link>\r\n                            </Col>\r\n                        </Row>\r\n                    </Card.Body>\r\n                </ValidationFormWrapper>\r\n            </Card>\r\n            {modal && <VspaceModal type=\"Project\" id={resId} />}\r\n        </Container>\r\n    );\r\n};\r\n\r\nexport default ProjectForm;\r\nfunction vspace_condition_func(\r\n    response: any,\r\n    virtualSpace: boolean,\r\n    setResId: React.Dispatch<any>,\r\n    setModal: React.Dispatch<React.SetStateAction<boolean>>,\r\n    t: any,\r\n    toastMsg: any\r\n) {\r\n    if (response && response._id) {\r\n        if (virtualSpace) {\r\n            setResId(response?._id && response._id);\r\n            setModal(true);\r\n        } else {\r\n            toast.success(t(toastMsg))\r\n            Router.push(\"/project/[...routes]\", `/project/show/${response._id}`);\r\n        }\r\n    } else {\r\n        toast.error(response)\r\n    }\r\n}\r\n\r\nasync function responseproject(\r\n    projectParams: any,\r\n    setcountryList: React.Dispatch<React.SetStateAction<any[]>>,\r\n    setExpertiseList: React.Dispatch<React.SetStateAction<any[]>>\r\n) {\r\n    const country = await apiService.get(\"/country\", projectParams);\r\n    if (country && Array.isArray(country.data)) {\r\n        setcountryList(country.data);\r\n    }\r\n\r\n    const expertise = await apiService.get(\"/expertise\", projectParams);\r\n    if (expertise && Array.isArray(expertise.data)) {\r\n        setExpertiseList(expertise.data);\r\n    }\r\n}\r\n\r\nfunction responceStartdate(\r\n    response: any,\r\n    start_date: any,\r\n    end_date: any,\r\n    status: any,\r\n    country: any,\r\n    area_of_work: any\r\n) {\r\n    response.start_date = start_date ? moment(start_date).toDate() : null;\r\n    response.end_date = end_date ? moment(end_date).toDate() : null;\r\n    response.status = status && status._id ? status._id : null;\r\n    response.country = country && country._id ? country._id : null;\r\n    response.area_of_work =\r\n        area_of_work && area_of_work.length > 0\r\n            ? area_of_work.map((item: any, _i: any) => {\r\n                  return { label: item.title, value: item._id };\r\n              })\r\n            : [];\r\n}\r\n\r\nfunction parternerInstution(\r\n    partner_institutions: any,\r\n    normalizePartner: any[],\r\n    countryRegion: (id: any, projectParams: any) => Promise<any[]>,\r\n    projectParams: any\r\n) {\r\n    partner_institutions &&\r\n        partner_institutions.forEach(async (item: any, _i: any) => {\r\n            const initRegions =\r\n                item.partner_region &&\r\n                item.partner_region.map((item_val: any, _i: any) => {\r\n                    return { label: item_val.title, value: item_val._id };\r\n                });\r\n            const _regionsId =\r\n                item.partner_region &&\r\n                item.partner_region.map((initial: any, _i: any) => {\r\n                    return initial._id;\r\n                });\r\n\r\n            const initInstitutions =\r\n                item.partner_institution &&\r\n                item.partner_institution.map((_item: any) => {\r\n                    return { label: _item.title, value: _item._id };\r\n                });\r\n            const _institutionsId =\r\n                item.partner_institution &&\r\n                item.partner_institution.map((item_i: any) => {\r\n                    return item_i._id;\r\n                });\r\n            normalizePartner.push({\r\n                partner_country: item.partner_country._id,\r\n                regions: initRegions,\r\n                partner_region: _regionsId,\r\n                institutions: initInstitutions,\r\n                partner_institution: _institutionsId,\r\n                world_region: item.partner_country.world_region._id,\r\n                countryregions: await countryRegion(item.partner_country._id, projectParams),\r\n            });\r\n        });\r\n}\r\n"], "names": ["props", "DatePicker", "RKIDatePicker", "Radio", "RadioGroup", "name", "valueSelected", "onChange", "errorMessage", "children", "errors", "touched", "useFormikContext", "<PERSON><PERSON><PERSON><PERSON>", "React", "childrenWithProps", "map", "child", "div", "className", "String", "RadioItem", "id", "label", "value", "disabled", "values", "setFieldValue", "fieldName", "Form", "Check", "type", "checked", "e", "target", "inline", "ValidationForm", "TextInput", "SelectGroup", "time", "setTime", "useState", "VSpaceModal", "t", "useTranslation", "useEffect", "setTimeout", "Router", "pathname", "query", "source", "Modal", "show", "FontAwesomeIcon", "icon", "faCheck", "color", "size", "p", "br", "small", "b", "ValidationFormWrapper", "forwardRef", "ref", "onSubmit", "autoComplete", "onKeyPress", "initialValues", "rest", "validationSchema", "<PERSON><PERSON>", "shape", "<PERSON><PERSON>", "actions", "mockEvent", "preventDefault", "stopPropagation", "currentTarget", "nativeEvent", "Event", "bubbles", "cancelable", "defaultPrevented", "eventPhase", "isTrusted", "timeStamp", "Date", "now", "isDefaultPrevented", "isPropagationStopped", "persist", "formikProps", "handleSubmit", "displayName", "required", "validator", "as", "multiline", "rows", "pattern", "Field", "validate", "stringVal", "val", "trim", "RegExp", "test", "field", "meta", "Control", "isInvalid", "error", "undefined", "<PERSON><PERSON><PERSON>", "initialState", "title", "website", "funded_by", "status", "countryTerritory", "start_date", "end_date", "area_of_work", "description", "institution_invites", "partner_institutions", "buttonRef", "useRef", "ProjectForm", "startDateRef", "i18n", "titleSearch", "language", "title_de", "currentLang", "initialVal", "setIntialVal", "groupVisibility", "invitesCountry", "invitesRegion", "invitesOrganisationType", "invitesOrganisation", "invitesExpertise", "invitesNetWork", "userList", "visibility", "statusproject", "setProjectStatus", "countryList", "setcountryList", "setUsersList", "setExpertiseList", "organisationList", "setorganisationList", "setOrganisationTypeList", "setNetworkList", "virtualSpace", "setVirtualSpace", "modal", "setModal", "areaofWorkList", "setAreaOfWorkList", "resId", "setResId", "editform", "routes", "virtualSpaceAccessPermission", "setVirtualSpaceAccessPermission", "setpartner_institutions", "partner_country", "regions", "partner_region", "institutions", "partner_institution", "countryregions", "world_region", "setinstitution_invites", "contact_name", "email", "_id", "defaultActiveKey", "setdefaultActiveKey", "partnerInstitutionError", "setPartnerInstitutionError", "areaOfWorkError", "setAreaOfWorkError", "projectParams", "sort", "limit", "languageCode", "getProjectInitialData", "projectParamsinitalsvalue", "projectStatus", "apiService", "get", "Array", "isArray", "data", "responseproject", "institution", "filtered", "item", "_", "institutionType", "institutionNetwork", "areaOfWork", "_area", "_i", "getProjectData", "projectParamsinitial", "response", "normalize<PERSON><PERSON>ner", "country", "responceStartdate", "moment", "toDate", "length", "parternerInstution", "countryRegion", "for<PERSON>ach", "initRegions", "item_val", "_regionsId", "initial", "initInstitutions", "_item", "_institutionsId", "item_i", "push", "prevState", "initialData", "currentUser", "post", "filteredRoles", "filter", "role", "_regions", "a", "localeCompare", "onChangeDate", "date", "key", "formRef", "tabAdd", "_temp", "removeTab", "_e", "i", "splice", "addcountry", "oldArray", "removecountry", "handleDescription", "normalizeGroup", "Object", "keys", "_data", "d", "console", "log", "getUsers", "userInvites", "groupParams", "_users", "username", "handleForm", "nameInitial", "selectedIndex", "getAttribute", "handleChange", "toast", "window", "scrollTo", "current", "focus", "toastMsg", "setAttribute", "mapped", "patch", "vspace_condition_func", "success", "Container", "fluid", "Card", "Body", "Row", "Col", "Title", "hr", "md", "lg", "sm", "Group", "Label", "EditorComponent", "initContent", "evt", "style", "max<PERSON><PERSON><PERSON>", "MultiSelect", "overrideStrings", "selectSomeItems", "allItemsAreSelected", "options", "bindAreaOfWork", "_name", "labelledBy", "option", "selected", "dateFormat", "placeholderText", "handleEndDateCheckBox", "minDate", "h6", "span", "C_item", "data-worldregion", "xs", "<PERSON><PERSON>", "variant", "onClick", "Tabs", "active<PERSON><PERSON>", "onSelect", "k", "Tab", "eventKey", "handleOrgTab", "_tempCosts", "handleContTab", "handleMailTab", "faPlus", "Text", "re<PERSON><PERSON><PERSON><PERSON>", "Link", "href", "VspaceModal", "expertise"], "sourceRoot": "", "ignoreList": []}