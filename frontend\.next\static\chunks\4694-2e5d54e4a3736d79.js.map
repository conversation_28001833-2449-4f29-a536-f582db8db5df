{"version": 3, "file": "static/chunks/4694-2e5d54e4a3736d79.js", "mappings": "iOAoFA,MA/D2BA,IACzB,GAAM,EAAGC,EAAW,CAAGC,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAU,CA8D5BC,GA7DP,YAAEC,CAAU,CA6DYD,EAAC,WA7DXE,CAAY,WAAEC,CAAS,SAAEC,CAAO,CAAE,CAAGP,EACnD,GAAEQ,CAAC,CAAE,CAAGC,CAAAA,EAAAA,EAAAA,EAAAA,CAAcA,CAAC,UAE7BC,CAAAA,EAAAA,EAAAA,SAAAA,CAASA,CAAC,KACG,MAAXH,EAAkBN,GAAW,GAASA,GAAW,EACnD,EAAG,CAACM,EAAQ,EAEZ,IAAMI,EAAgB,IACpBX,EAAMY,KAAK,CAACC,EACd,EAEMC,EAAgB,IACpBd,EAAMe,mBAAmB,CAACC,EAC5B,EAEA,MACE,WAACC,EAAAA,CAASA,CAAAA,CAACC,UAAU,WAAWC,KAAK,cACnC,UAACC,EAAAA,CAAGA,CAAAA,CAACF,UAAU,eAAeG,GAAI,YAChC,UAACC,KAAAA,UACC,UAACC,OAAAA,UAAMf,EAAE,qBAGb,WAACgB,EAAAA,CAAGA,CAAAA,WACF,WAACJ,EAAAA,CAAGA,CAAAA,WACF,WAACK,EAAAA,CAAIA,CAACC,KAAK,YACT,UAACD,EAAAA,CAAIA,CAACE,KAAK,EAACT,UAAU,kCAA0BV,EAAE,sBAClD,UAACoB,EAAAA,CAAaA,CAAAA,CACZC,SAAUvB,EACVwB,QAAS,IAAIC,KACbC,cAAc,IACdC,cAAe,GACfC,SAAU,GAAgB7B,EAAa8B,EAAM,aAC7CC,gBAAkB5B,EAAE,sBACpB6B,WAAW,6BAGe,IAAzBjC,EAAWE,SAAS,EAAc,CAACA,GAAgB,UAACgC,IAAAA,CAAEC,MAAO,CAAGC,MAAO,KAAK,WAAIhC,EAAE,uCAGzF,WAACY,EAAAA,CAAGA,CAAAA,WAEA,WAACK,EAAAA,CAAIA,CAACC,KAAK,YACT,UAACD,EAAAA,CAAIA,CAACE,KAAK,EAACT,UAAU,kCAA0BV,EAAE,oBAClD,UAACoB,EAAAA,CAAaA,CAAAA,CACZC,SAAUtB,EACVyB,cAAc,IACdC,cAAe,GACfC,SAAU,GAAgB7B,EAAa8B,EAAM,WAC7CC,gBAAiB5B,EAAE,sBACnBsB,QAASxB,EACT+B,WAAW,6BAGe,IAAzBjC,EAAWE,SAAS,EAAc,CAACC,GAAc,WAAC+B,IAAAA,CAAEC,MAAO,CAAGC,MAAO,KAAK,YAAIhC,EAAE,gCAAgC,aAI3H,UAACiC,EAAAA,OAAYA,CAAAA,CAACC,KAAM1C,EAAM0C,IAAI,CAAEC,QAAS3C,EAAM4C,MAAM,CAAEhC,MAAO,GAAQD,EAAcE,GAAKE,oBAAqB,GAAeD,EAAcE,OAGjJ,6DCqCA,MA9GkE,OAAC,CACjE6B,OAAK,UACLX,CAAQ,GA4GKY,UA3GbC,EAAc,QA2GmBD,EAAC,UA3GA,QAClCE,EAAS,GAAG,UACZC,GAAW,CAAK,CACjB,GACOC,EAAYC,CAAAA,EAAAA,EAAAA,MAAAA,CAAMA,CAAiB,MACnC,CAACC,EAAWC,EAAa,CAAGnD,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,EAAC,GAG3CQ,CAAAA,EAAAA,EAAAA,SAAAA,CAASA,CAAC,KACJwC,EAAUI,OAAO,EAAI,GAEnB,CAACF,GAAaF,EAAUI,IAFa,GAEN,CAACC,SAFkB,GAEJV,IAChDK,EAAUI,CAD6C,MACtC,CAACC,SAAS,CAAGV,GAAS,GAG7C,EAAG,CAACA,EAAOO,EAAU,EAGrB,IAAMI,EAAc,KACdN,EAAUI,OAAO,EAAIpB,GACvBA,EAASgB,EAAUI,GADc,IACP,CAACC,SAAS,CAExC,EAGME,EAAc,CAACC,EAAiBb,KACpC,GAAwB,aAApB,OAAOc,SAA0B,KAGnCT,EAFAS,SAASF,WAAW,CAACC,GAAS,EAAOb,GAAS,IAC9CW,WACAN,EAAAA,EAAUI,OAAAA,GAAVJ,EAAmBU,KAAK,EAC1B,CACF,CAFIV,CAIJ,MACE,UAACW,MAAAA,CAAI3C,UAAU,0BAA0BqB,MAAO,CAAEuB,OAAQ,gBAAiB,WAEzE,CADC,EACD,GAD8B,GAC9B,wBACE,WAACD,MAAAA,CAAI3C,UAAU,UAAUqB,MAAO,CAAEwB,QAAS,MAAOC,aAAc,iBAAkBC,WAAY,SAAU,YACpG,UAACC,SAAAA,CACCC,KAAK,SACLC,QAAS,IAAMX,EAAY,QAC3BlB,MAAO,CAAE8B,OAAQ,QAASN,QAAS,SAAU,WAE7C,UAACO,SAAAA,UAAO,QAEV,UAACJ,SAAAA,CACCC,KAAK,SACLC,QAAS,IAAMX,EAAY,UAC3BlB,MAAO,CAAE8B,OAAQ,QAASN,QAAS,SAAU,WAE7C,UAACQ,KAAAA,UAAG,QAEN,UAACL,SAAAA,CACCC,KAAK,SACLC,QAAS,IAAMX,EAAY,aAC3BlB,MAAO,CAAE8B,OAAQ,QAASN,QAAS,SAAU,WAE7C,UAACS,IAAAA,UAAE,QAEL,UAACN,SAAAA,CACCC,KAAK,SACLC,QAAS,IAAMX,EAAY,qBAC3BlB,MAAO,CAAE8B,OAAQ,QAASN,QAAS,SAAU,WAC9C,OAGD,UAACG,SAAAA,CACCC,KAAK,SACLC,QAAS,IAAMX,EAAY,uBAC3BlB,MAAO,CAAE8B,OAAQ,QAASN,QAAS,SAAU,WAC9C,OAGD,UAACG,SAAAA,CACCC,KAAK,SACLC,QAAS,KACP,IAAMK,EAAMC,OAAO,sBACfD,GAAKhB,EAAY,aAAcgB,EACrC,EACAlC,MAAO,CAAE8B,OAAQ,QAASN,QAAS,SAAU,WAC9C,YAIH,UAACF,MAAAA,CACCc,IAAKzB,EACL0B,gBAAiB,CAAC3B,EAClB4B,QAASrB,EACTsB,QAAS,IAAMzB,GAAa,GAC5B0B,OAAQ,IAAM1B,GAAa,GAC3Bd,MAAO,CACLwB,QAAS,OACTiB,UAAWhC,EACXiC,UAAWjC,IACXkC,SAAU,OACVC,QAAS,MACX,EACAC,mBAAkB,EAAuB,GAAdrC,EAC3BsC,gCAAgC,QAO5C,EC7GaC,EAAmD,IAC9D,GAAM,aAAEC,CAAW,UAAErD,CAAQ,CAAE,CAAGlC,EAElC,MACE,UAAC8C,EAAoBA,CACnBD,MAAO0C,GAAe,GACtBrD,SAAU,GAFSY,EAEa0C,IAGtC,EAAE,yECJF,MANsB,GAElB,UAACC,EAAAA,EAAUA,CAAAA,CAAE,GAAGzF,CAAK,KAIV4B,aAAaA,EAAC,0ECR7B,IAAM8D,EAAwBC,EAAAA,OAAb,GAA6B,CAAC,GAK5ChB,IALyB,IAAoB,WAC9CzD,CAAS,UACT0E,CAAQ,CACRC,GAAIC,EAAY,KAAK,CACrB,GAAG9F,EACJ,GAEC,OADA4F,EAAWG,CAAAA,EAAAA,EAAAA,EAAAA,CAAkBA,CAACH,EAAU,aACpBI,CAAAA,EAAAA,EAAAA,GAAAA,CAAIA,CAACF,EAAW,CAClCnB,IAAKA,EACLzD,UAAW+E,IAAW/E,EAAW0E,GACjC,GAAG5F,CAAK,EAEZ,GACA0F,EAASQ,WAAW,CAAG,WCbvB,IAAMC,EAA0BR,EAAAA,SAAb,CAA6B,CAAC,GAK9ChB,MAL2B,EAAoB,WAChDzD,CAAS,UACT0E,CAAQ,CACRC,GAAIC,EAAY,KAAK,CACrB,GAAG9F,EACJ,GAEC,OADA4F,EAAWG,CAAAA,EAAAA,EAAAA,EAAAA,CAAkBA,CAACH,EAAU,eACpBI,CAAAA,EAAAA,EAAAA,GAAAA,CAAIA,CAACF,EAAW,CAClCnB,IAAKA,EACLzD,UAAW+E,IAAW/E,EAAW0E,GACjC,GAAG5F,CAAK,EAEZ,GACAmG,EAJyBF,WAIH,CAAG,4BCXzB,IAAMG,EAA0BT,EAAAA,SAAb,CAA6B,CAAC,GAM9ChB,MAN2B,EAAoB,UAChDiB,CAAQ,WACR1E,CAAS,CAET2E,CADA,EACIC,EAAY,KAAK,CACrB,GAAG9F,EACJ,GACOqG,EAASN,CAAAA,EAAAA,EAAAA,EAAAA,CAAkBA,CAACH,EAAU,eACtCU,EAAeC,CAAAA,EAAAA,EAAAA,OAAAA,CAAOA,CAAC,IAAO,EAClCC,mBAAoBH,EACtB,EAAI,CAACA,EAAO,EACZ,MAAoBL,CAAb,EAAaA,EAAAA,GAAAA,CAAIA,CAACS,EAAP,CAAwBA,CAACC,QAAQ,CAAE,CACnD7D,MAAOyD,EACPK,SAAuBX,CAAb,EAAaA,EAAAA,GAAAA,CAAIA,CAACF,EAAP,CACnBnB,IAAKA,EACL,GAAG3E,CAAK,CACRkB,UAAW+E,IAAW/E,EAAWmF,EACnC,EACF,EACF,GACAD,EAAWF,GAJgBD,QAIL,CAAG,aCtBzB,IAAMW,EAAuBjB,EAAAA,MAAb,IAA6B,CAC7C,CACA,EAMGhB,GARwB,KAE1B,UACCiB,CAAQ,WACR1E,CAAS,CACT2F,SAAO,CACPhB,GAAIC,EAAY,KAAK,CACrB,GAAG9F,EACJ,GACOqG,EAASN,CAAAA,EAAAA,EAAAA,EAAAA,CAAkBA,CAACH,EAAU,YAC5C,MAAoBI,CAAb,EAAaA,EAAAA,GAAAA,CAAIA,CAACF,EAAP,CAChBnB,IAAKA,EACLzD,UAAW+E,IAAWY,EAAU,GAAaA,MAAAA,CAAVR,EAAO,EAArBJ,GAAgC,OAARY,CAX0G,EAW9FR,EAAQnF,GACjE,GAAGlB,CACL,EACF,GACA4G,EAAQV,WAAW,CAAG,UChBtB,IAAMY,EAA8BnB,EAAAA,UAAgB,CAAC,EAA9B,CAKpBhB,QALmD,EAApB,SAChCzD,CAAS,UACT0E,CAAQ,CACRC,GAAIC,EAAY,KAAK,CACrB,GAAG9F,EACJ,GAEC,OADA4F,EAAWG,CAAAA,EAAAA,EAAAA,EAAAA,CAAkBA,CAACH,EAAU,oBACpBI,CAAAA,EAAAA,EAAAA,GAAAA,CAAIA,CAACF,EAAW,CAClCnB,IAAKA,EACLzD,UAAW+E,IAAW/E,EAAW0E,GACjC,GAAG5F,CAAK,EAEZ,GACA8G,EAJyBb,WAIC,CAAG,iBCb7B,IAAMc,EAAwBpB,EAAAA,OAAb,GAA6B,CAAC,GAK5ChB,IALyB,IAAoB,CAC9CzD,WAAS,UACT0E,CAAQ,CACRC,GAAIC,EAAY,GAAG,CACnB,GAAG9F,EACJ,GAEC,OAAO,EADI+F,CAAAA,EAAAA,EAAAA,EAAAA,CAAkBA,CAACH,EAAU,aACpBI,CAAAA,EAAAA,EAAAA,GAAAA,CAAIA,CAACF,EAAW,CAClCnB,IAAKA,EACLzD,UAAW+E,IAAW/E,EAAW0E,GACjC,GAAG5F,CACL,EACF,GACA+G,EAASb,WAAW,CAAG,0BCZvB,IAAMc,EAAgBC,CAAAA,EAAAA,EAAAA,CAAAA,CAAgBA,CAAC,MACjCC,EAA4BvB,EAAAA,UAAgB,CAA7B,CAA8B,EAKhDhB,QALiD,WAClDzD,CAAS,UACT0E,CAAQ,CACRC,GAAIC,EAAYkB,CAAa,CAC7B,GAAGhH,EACJ,GAEC,OADA4F,EAAWG,CAAAA,EAAAA,EAAAA,EAAAA,CAAkBA,CAACH,EAAU,iBACpBI,CAAAA,EAAAA,EAAAA,GAAAA,CAAIA,CAACF,EAAW,CAClCnB,IAAKA,EACLzD,UAAW+E,IAAW/E,EAAW0E,GACjC,GAAG5F,CAAK,EAEZ,EACAkH,GAAahB,WAAW,CAAG,eCf3B,IAAMiB,EAAwBxB,EAAAA,OAAb,GAA6B,CAAC,GAK5ChB,IALyB,IAAoB,WAC9CzD,CAAS,CACT0E,UAAQ,CACRC,GAAIC,EAAY,GAAG,CACnB,GAAG9F,EACJ,GAEC,OADA4F,EAAWG,CAAAA,EAAAA,EAAAA,EAAAA,CAAkBA,CAACH,EAAU,aACpBI,CAAAA,EAAAA,EAAAA,GAAAA,CAAIA,CAACF,EAAW,CAClCnB,IAAKA,EACLzD,UAAW+E,IAAW/E,EAAW0E,GACjC,GAAG5F,CAAK,EAEZ,GACAmH,EAJyBlB,WAIL,CAAG,WCZvB,IAAMmB,EAAgBH,CAAAA,EAAAA,EAAAA,CAAAA,CAAgBA,CAAC,MACjCI,EAAyB1B,EAAAA,QAAb,EAA6B,CAAC,GAK7ChB,KAL0B,GAAoB,WAC/CzD,CAAS,UACT0E,CAAQ,CACRC,GAAIC,EAAYsB,CAAa,CAC7B,GAAGpH,EACJ,GAEC,OADA4F,EAAWG,CAAAA,EAAAA,EAAAA,EAAAA,CAAkBA,CAACH,EAAU,cACpBI,CAAAA,EAAAA,EAAAA,GAAAA,CAAIA,CAACF,EAAW,CAClCnB,IAAKA,EACLzD,UAAW+E,IAAW/E,EAAW0E,GACjC,GAAG5F,CACL,EACF,GACAqH,EAAUnB,WAAW,CAAG,YCNxB,IAAMoB,EAAoB3B,EAAAA,GAAb,OAA6B,CAAC,GAAnB,QAAoB,UAC1CC,CAAQ,CACR1E,WAAS,IACTqG,CAAE,MACFC,CAAI,QACJ1D,CAAM,MACN2D,GAAO,CAAK,CACZd,UAAQ,CAERd,CADA,EACIC,EAAY,KAAK,CACrB,GAAG9F,EACJ,GACOqG,EAASN,CAAAA,EAAAA,EAAAA,EAAAA,CAAkBA,CAACH,EAAU,QAC5C,MAAoBI,CAAb,EAAaA,EAAAA,GAAAA,CAAIA,CAACF,EAAP,CAChBnB,IAAKA,EACL,GAAG3E,CAAK,CACRkB,UAAW+E,IAAW/E,EAAWmF,EAAQkB,GAAM,MAAS,GAAnCtB,GAAmC,CAAHsB,GAAMC,GAAQ,QAAa,OAALA,GAAQ1D,GAAU,UAAiB,OAAPA,IACvG6C,IATyJ,KAS/Ic,EAAoBzB,CAAAA,EAAAA,EAAAA,GAAAA,CAAIA,CAACN,EAAU,CAC3CiB,GAD0B,MAAejB,CAE3C,GAAKiB,CACP,EACF,GACAW,EAAKpB,WAAW,CAAG,OACnB,MAAewB,OAAOC,MAAM,CAACL,EAAM,CACjCM,INhBahB,CMgBRA,CACLiB,KNjBoBjB,CKDPS,CLCQ,CMkBrBS,EAFYlB,KDjBUS,EFATH,CGmBHA,CACVa,CAFgBV,ITpBH3B,CSsBPA,CACNsC,GHrByBd,EAAC,CNFLxB,CSwBrBuC,CTxBsB,GSsBRvC,CFtBDyB,CFAQJ,CIyBrBmB,CJzBsB,GIuBRnB,EFvBOI,CLSRf,CKTS,CE0BtB+B,EAFchB,KRxBDhB,CQ0BLA,CACRiC,CPlBwB,GOgBNhC,IRzBKD,EAAC,CGAXW,CK2BDA,CADMX,CAElB,EAAC,SL5B0BW,EAAC,GK2BFA,6HCwCrB,IAAMuB,EAAQ,CACnBC,WA1C4C,OAAC,MAC7CC,CAAI,eACJC,CAAa,CACbtG,UAAQ,CACRuG,cAAY,UACZ9B,CAAQ,CACT,GACO,QAAE+B,CAAM,SAAEC,CAAO,CAAE,CAAGC,CAAAA,EAAAA,EAAAA,EAAAA,CAAgBA,GACtCC,EAAWF,CAAO,CAACJ,EAAK,EAAIG,CAAM,CAACH,EAAK,CAGzB5C,EAAAA,OAAa,CAAC,IAAO,OAAE4C,EAAK,EAAI,CAACA,EAAK,EAG3D,IAAMO,EAAoBnD,EAAAA,QAAc,CAACoD,GAAG,CAACpC,EAAU,GACrD,EAAIhB,cAAoB,CAACqD,IAEnBC,IAF2B,KAxC5BA,CAAmB,EAC1B,MAAwB,UAAjB,OAAOjJ,GAAgC,OAAVA,CACtC,EAwCmBgJ,EAAMhJ,KAAK,EACf2F,CADkB,CAClBA,YAAkB,CAACqD,EAA6C,MACrET,EACA,GAAGS,EAAMhJ,KAAK,GAIbgJ,GAGT,MACE,WAACnF,MAAAA,WACC,UAACA,MAAAA,CAAI3C,UAAU,uBACZ4H,IAEFD,GACC,UAAChF,MAAAA,CAAI3C,UAAU,oCACZuH,GAAiB,kBAAOC,CAAM,CAACH,EAAK,CAAgBG,CAAM,CAACH,EAAK,CAAGW,OAAOR,CAAM,CAACH,GAAK,MAKjG,EAIEY,UAhE0C,OAAC,CAAEtI,IAAE,OAAEuI,CAAK,OAAEvG,CAAK,MAAE0F,CAAI,UAAEtF,CAAQ,CAAE,GACzE,CAAEoG,QAAM,CAAEC,eAAa,CAAE,CAAGV,CAAAA,EAAAA,EAAAA,EAAAA,CAAgBA,GAC5CW,EAAYhB,GAAQ1H,EAE1B,MACE,UAACY,EAAAA,CAAIA,CAAC+H,KAAK,EACTrF,KAAK,QACLtD,GAAIA,EACJuI,MAAOA,EACPvG,MAAOA,EACP0F,KAAMgB,EACNE,QAASJ,CAAM,CAACE,EAAU,GAAK1G,EAC/BX,SAAWwH,IACTJ,EAAcC,EAAWG,EAAEC,MAAM,CAAC9G,KAAK,CACzC,EACAI,SAAUA,EACV2G,MAAM,KAGZ,CA8CA,EAAE,ECzEcC,CAAAA,CACLC,EAAAA,EAAAA,CACEC,EAAAA,EAAAA,8ICwBb,MA7ByB,IACrB,GAAM,CAAEvJ,CAAC,CAAE,CAAGC,CAAAA,EAAAA,EAAAA,EAAAA,CAAcA,CAAC,QA4BlBuJ,EA3BL,OAAEC,CAAK,MA2BcD,EAAC,QA3BbE,CAAc,CAAE,CAAGlK,EAClC,MACI,UAACiB,EAAAA,CAASA,CAAAA,CAACC,UAAU,WAAWC,KAAK,aACjC,UAACK,EAAAA,CAAGA,CAAAA,UACA,UAACJ,EAAAA,CAAGA,CAAAA,UACA,WAACK,EAAAA,CAAIA,CAACC,KAAK,YACP,WAACD,EAAAA,CAAIA,CAACE,KAAK,EAACT,UAAU,2BACjBV,EAAE,uBAAuB,IAAEA,EAAE,mBAElC,UAACsJ,EAAAA,EAASA,CAAAA,CACNvB,KAAK,QACL1H,GAAG,QACHsJ,QAAQ,IACRtH,MAAOoH,EACP/H,SAAUgI,EACVE,UAAW,GAAmC,IAAhBvH,EAAMwH,IAAI,GACxC5B,aAAc,CACV2B,UAAW5J,EAAE,kCACjB,YAO5B,8UCnBA,IAAM8J,EAAmB,CACrBL,MAAO,GACPM,YAAa,KACbjK,UAAW,KACXC,QAAS,KACTiK,eAAgB,KAChBC,WAAY,KACZC,SAAU,KACVC,mBAAoB,GACpBC,OAAQ,EAAE,CACVjH,SAAU,EAAE,CACZkH,WAAY,EAAE,CACdC,QAAS,EAAE,EAGTC,EAAa,CACfd,MAAO,GACPM,YAAa,KACbjK,UAAW,KACXC,QAAS,KACTiK,eAAgB,KAChBG,oBAAoB,EACpBC,OAAQ,EAAE,CACVjH,SAAU,EAAE,CACZkH,WAAY,EAAE,CACdC,QAAS,EAAE,EAGTE,EAAe,CAAEC,YAAa,GAAIC,SAAU,EAAG,EAE/CC,EAAY,CAAC,CAAElB,MAAO,GAAImB,KAAM,EAAG,EAAE,CAgBrCC,EAAwB,CAC1B/K,WAAW,EACXC,QAAS,EACb,WA4fA,MAtfmB,IACf,IAAM+K,EAAUnI,CAAAA,EAAAA,EAAAA,MAAAA,CAAMA,CAAC,KAqfZoI,CAnfL,GAAE/K,CAAC,CAAE,CAAGC,CAAAA,EAAAA,EAAAA,EAAAA,CAAcA,CAAC,UACvB,OAAE+K,CAAK,CAAE,CAAGC,CAAAA,EAAAA,EAAAA,SAAAA,CAASA,GACrB,CAACC,EAAWC,EAAa,CAAGzL,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAMoK,GAC1C,CAACsB,EAAcC,EAAgB,CAAG3L,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAC,CAAE+J,MAAO,EAAG,GACvD,CAACmB,EAAMU,EAAQ,CAAG5L,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAC,CAAC,CAAE+J,MAAO,GAAImB,KAAM,EAAG,EAAE,EACpD,CAACW,EAAiBC,EAAmB,CAAG9L,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAC8K,GACjD,CAAC5K,EAAY6L,EAAc,CAAG/L,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAACmL,GACvC,EAAGa,EAAa,CAAGhM,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAC,IAC5B,CAACiM,EAAYC,EAAc,CAAGlM,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAC,IACvC,CAACmM,EAAkBC,EAA0B,CAAGpM,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAC,EAAE,EAC3D,CAACqM,EAAkBC,EAAoB,CAAGtM,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAC,EAAE,EACrD,CAACuM,EAAoBC,EAAsB,CAAGxM,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAC,EAAE,EACzD,CAACyM,EAAeC,EAAiB,CAAG1M,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAC,EAAE,EAC/C2M,EACF7M,EAAM8M,MAAM,EACZ9M,EAAM8M,MAAM,CAACtB,KAAK,EAClBxL,EAAM8M,MAAM,CAACtB,KAAK,CAACuB,MAAM,EACzB/M,EAAM8M,MAAM,CAACtB,KAAK,CAACuB,MAAM,CAACC,MAAM,CAAG,GACnChN,EAAM8M,MAAM,CAACtB,KAAK,CAACuB,MAAM,CAAC,EAAE,CAU1BE,GAA2B,IAC7B,GAAM,MAAE1E,CAAI,OAAE1F,CAAK,CAAE,CAAG6G,EAAEC,MAAM,CAChCkC,EAAgB,GAAqB,EACjC,GAAGqB,CAAS,CACZ,CAAC3E,CAFgC,CAE3B,CAAE1F,EACZ,EACJ,EAEMsK,GAAuBzD,IACzB,GAAM,MAAEnB,CAAI,OAAE1F,CAAK,CAAE,CAAG6G,EAAEC,MAAM,CAChCqC,EAAmB,GAAqB,EACpC,GAAGkB,CAAS,CACZ,CAAC3E,CAFmC,CAE9B,CAAE1F,EACZ,EACJ,EAEMuK,GAA0B,CAAC1D,EAAwC2D,KACrE,GAAM,MAAE9E,CAAI,OAAE1F,CAAK,CAAE,CAAG6G,EAAEC,MAAM,CAC1B2D,EAAoB,IAAIlC,EAAK,CACnCkC,CAAU,CAACD,EAAE,CAAC9E,EAAK,CAAG1F,EACtBiJ,EAAQwB,EACZ,EAEMC,GAAa,CAACC,EAASH,KACzBjC,EAAKqC,MAAM,CAACJ,EAAG,GACfvB,EAAQ,IAAIV,EAAK,EACG,GAAG,CAAnBA,EAAK4B,MAAM,EACXU,IAER,EAEMA,GAAU,KACZ,IAAMC,EAAI,CAAE1D,MAAO,GAAImB,KAAM,EAAG,EAChCU,EAAS8B,GAAa,IAAIA,EAAUD,EAAE,CAC1C,EAWME,GAAsB,IACxBlC,EAAa,GAAqB,EAC9B,GAAGuB,CAAS,CACZ3C,EAF8B,UAEjB1H,EACjB,EACJ,EAEMxC,GAAe,CAAC8B,EAAY2L,KAC9BnC,EAAa,GAAqB,EAC9B,GAAGuB,CAAS,CACZ,CAACY,CAF6B,CAEzB,CAAE3L,EACX,GAEA8J,EAAeiB,GAAoB,EAC/B,GAAGA,CAAS,CACZ5M,EAF+B,QAEZ,cAARwN,EACXvN,KADwC,GACvB,YAARuN,EACb,EACJ,EAEMC,CAJsC,EAId,IAC1B,CAAI,CAACrC,EAAUpL,SAAS,EAAE,CACtB2L,EAAc,GAAW,EAAE,EAAF,CAAK+B,CAAI,CAAE1N,WAAW,EAAK,IAC7C,GAkCT2N,GAAc,IAChB,GAAM,WAAE3N,CAAS,SAAEC,CAAO,CAAE,CAAGmL,EAC/B,GAAmB,kBAAkB,CAAjCS,IACAF,EAAc,GAAqB,EAC/B,GAAGiB,CAAS,CACZ5M,EAF+B,SAEpB,EACf,GACI,CAACA,GAAa,CAACC,GAEf,MAFwB,CACxB2N,EAAMC,cAAc,GACb,GAIf,GAAmB,SAAfhC,EAAuB,CACvB,IAAIiC,EAAUC,IAAAA,SAAW,CAACjD,EAAM,CAAEnB,MAAO,EAAG,GACxCqE,EAASD,IAAAA,SAAW,CAACjD,EAAM,CAAEA,KAAM,EAAG,GAC1C,GAAgB,CAAC,IAAbgD,GAA6B,CAAC,GAAG,CAAfE,EAElB,OADAJ,EAAMC,cAAc,IACb,CAEf,CACJ,EAEMI,GAAW,MAAO7L,IACpBA,EAAKyB,IAAI,CAAGuH,EAAUjB,UAAU,CAChC,IAAM+D,EAAW,MAAMC,EAAAA,CAAUA,CAACC,KAAK,CAAC,YAAyC,OAA7B1O,EAAM8M,MAAM,CAACtB,KAAK,CAACuB,MAAM,CAAC,EAAE,EAAIrK,GAChF8L,GAAYA,EAASG,GAAG,EAAE,IAC1BC,IAAW,CAAC,IAAyB,OAArBlD,EAAUjB,UAAU,CAAC,gBAAe,IAAiCiB,MAAAA,CAA7BA,EAAUjB,UAAU,CAAC,UAA2B,OAAnBiB,EAAUhB,QAAQ,EAE/G,EAEMmE,GAAe,MAAOnM,IACxB,OAAQ1C,EAAM8M,MAAM,CAACtB,KAAK,CAACsD,WAAW,EAClC,IAAK,YACDpM,EAAK,EAAD,cAAoB,CAAG1C,EAAM8M,MAAM,CAACtB,KAAK,CAACuD,SAAS,CACvD,KACJ,KAAK,QACDrM,EAAK,EAAD,UAAgB,CAAG1C,EAAM8M,MAAM,CAACtB,KAAK,CAACuD,SAAS,CACnD,KACJ,KAAK,UACDrM,EAAK,EAAD,YAAkB,CAAG1C,EAAM8M,MAAM,CAACtB,KAAK,CAACuD,SAAS,CACrD,KACJ,KAAK,SACDrM,EAAK,EAAD,WAAiB,CAAG1C,EAAM8M,MAAM,CAACtB,KAAK,CAACuD,SAAS,CACpD,KACJ,KAAK,UACDrM,EAAK,EAAD,YAAkB,CAAG1C,EAAM8M,MAAM,CAACtB,KAAK,CAACuD,SAAS,CACrD,KACJ,KAAK,SACDrM,EAAK,EAAD,WAAiB,CAAG1C,EAAM8M,MAAM,CAACtB,KAAK,CAACuD,SAAS,CACpD,KACJ,KAAK,cACDrM,EAAK,EAAD,gBAAsB,CAAG1C,EAAM8M,MAAM,CAACtB,KAAK,CAACuD,SAAS,CAKjE,IAAMP,EAAW,MAAMC,EAAAA,CAAUA,CAACO,IAAI,CAAC,WAAYtM,GAE/C8L,GAAYA,EAASG,GAAG,EAAE,IAC1BC,IAAW,CACP,IAAmC,OAA/B5O,EAAM8M,MAAM,CAACtB,KAAK,CAACsD,WAAW,CAAC,gBACnC,IAA2C9O,MAAAA,CAAvCA,EAAM8M,MAAM,CAACtB,KAAK,CAACsD,WAAW,CAAC,UAAqC,OAA7B9O,EAAM8M,MAAM,CAACtB,KAAK,CAACuD,SAAS,EAGnF,EAEME,GAAe,MAAOf,EAAY7E,KACpC,GAAM,CAAEY,OAAK,CAAEM,aAAW,WAAEjK,CAAS,SAAEC,CAAO,oBAAEoK,CAAkB,UAAEhH,CAAQ,SAAEmH,CAAO,QAAEF,CAAM,CAAEC,YAAU,CAAE,CACvGxB,GAAUqC,EACd,GAAIuC,GAAYC,GACZ,KADoB,EAQxB,GALIA,GAASA,EAAMC,cAAc,EAAE,EACzBA,cAAc,GAIpBD,GAASA,EAAMgB,aAAa,GAAgC,IAA5BnB,GAAmC,GAEtC,KAAzBoB,EADeD,aAAa,CACvBE,aAAa,GAAc,YAChClB,EAAMmB,eAAe,GAI7B,IAAMC,EAAclE,EAAKmE,IAAI,CAAEC,GAAiB,IAAXA,EAAEvF,KAAK,EAAoB,IAAVuF,EAAEpE,IAAI,EAC5D,GAAkB,QAAde,GAAwBmD,EACxB,OAEJpD,GAAa,CAH4B,EAIzC,IAAMxJ,EAAO,CACTuH,MAAsB,iBAAfkC,EAAgCP,EAAa3B,KAAK,CAACI,IAAI,GAAKJ,EAAMI,IAAI,GAC7ElG,KAAMnE,EAAM8M,MAAM,CAACtB,KAAK,CAACsD,WAAW,CACpCvE,YAAaA,EACbkF,qBAAsB9E,EACtB+E,WAAYpP,EACZqP,SAAUpP,EACVqP,YAAa5P,EAAM8M,MAAM,CAACtB,KAAK,CAACoE,WAAW,CAC3CxE,KAAMA,EACNzH,SAAUA,GAAsB,EAAE,CAClCoI,KADqBpI,WACJoI,GAAoC,CAAC,EACtDnB,OAAQA,GAAkB,EADSmB,CAEnClB,GADiBD,QACLC,GAA0B,EAAE,CACxCC,OADyBD,CAChBC,GAAoB,EAAE,EAG/B9K,EAAM8M,CAHahC,KAGP,CAACU,KAAK,EAAIxL,EAAM8M,MAAM,CAACtB,KAAK,CAACuB,MAAM,CAACC,MAAM,CAAG,GAAKhN,EAAM8M,MAAM,CAACtB,KAAK,CAACuB,MAAM,CAAC,EAAE,CAC1FwB,CAD4F,EACnF7L,GAETmM,GAAanM,EAErB,EAEMmN,GAAkB,UACpB,IAAMnN,EAAO,MAAM+L,EAAAA,CAAUA,CAACqB,GAAG,CAAC,eAA8C,OAA/B9P,EAAM8M,MAAM,CAACtB,KAAK,CAACoE,WAAW,GAC3ElN,GACA0J,EAAc1J,CADR,CACauH,KAAK,CAEhC,EAEM8F,GAAY,CAACrN,EAAW8L,KAC1B,OAAQA,EAASrK,IAAI,EACjB,IAAK,YACDzB,EAAK,EAAD,MAAY,CAAG8L,EAASwB,gBAAgB,CAC5C,KACJ,KAAK,QACDtN,EAAK,EAAD,MAAY,CAAG8L,EAASyB,YAAY,CACxC,KACJ,KAAK,UACDvN,EAAK,EAAD,MAAY,CAAG8L,EAAS0B,cAAc,CAC1C,KACJ,KAAK,SACDxN,EAAK,EAAD,MAAY,CAAG8L,EAAS2B,aAAa,CACzC,KACJ,KAAK,UACDzN,EAAK,EAAD,MAAY,CAAG8L,EAAS4B,cAAc,CAC1C,KACJ,KAAK,SACD1N,EAAK,EAAD,MAAY,CAAG8L,EAAS6B,aAAa,CACzC,KACJ,KAAK,cACD3N,EAAK,EAAD,MAAY,CAAG8L,EAAS8B,kBAAkB,CAK1D,EAEMC,GAAc,CAAC/B,EAAe9L,KAChC4J,EAA0BkC,EAAS7K,QAAQ,CAAG6K,EAAS7K,QAAQ,CAAG,EAAE,EACpE6I,EAAoBgC,EAAS1D,OAAO,CAAG0D,EAAS1D,OAAO,CAAG,EAAE,EAC5D4B,EAAsB8B,EAAS5D,MAAM,CAAG4D,EAAS5D,MAAM,CAAG,EAAE,EAC5DgC,EAAiB4B,EAAS3D,UAAU,CAAG2D,EAAS3D,UAAU,CAAG,EAAE,EAC/DiB,EAAQ0C,EAASpD,IAAI,CAAC4B,MAAM,CAAGwB,EAASpD,IAAI,CAAGD,GAC/Ca,EAAmBwC,EAASzC,eAAe,CAAGyC,EAASzC,eAAe,CAAGA,GACzEgE,GAAUrN,EAAM8L,EACpB,EACA9N,CAAAA,EAAAA,EAAAA,SAAAA,CAASA,CAAC,KACN,IAAM8P,EAAY,UACd,IAAMhC,EAAW,MAAMC,EAAAA,CAAUA,CAACqB,GAAG,CAAC,YAAyC,OAA7B9P,EAAM8M,MAAM,CAACtB,KAAK,CAACuB,MAAM,CAAC,EAAE,GACxErK,EAAY,CACduH,MAAOuE,EAASvE,KAAK,CACrBM,YAAaiE,EAASjE,WAAW,CACjCjK,UAAWkO,EAASkB,UAAU,CAAGe,IAAOjC,EAASkB,UAAU,EAAEgB,MAAM,GAAK,KACxEnQ,QAASiO,EAASmB,GADqBc,KACb,CAAGA,IAAOjC,EAASmB,QAAQ,EAAEe,MAAM,GAAK,KAClElG,eAAgB,KAChBC,WAAY+D,EAASrK,IAAI,CACzBwG,mBAAoB6D,EAASiB,oBAAoB,CACjD9L,SAAU6K,EAAS7K,QAAQ,CAAG6K,EAAS7K,QAAQ,CAAG,EAAE,CACpDmH,QAAS0D,EAAS1D,OAAO,CAAG0D,EAAS1D,OAAO,CAAG,EAAE,CACjDF,OAAQ4D,EAAS5D,MAAM,CAAG4D,EAAS5D,MAAM,CAAG,EAAE,CAC9CC,WAAY2D,EAAS3D,UAAU,CAAG2D,EAAS3D,UAAU,CAAG,EAAE,EAE9D0F,GAAY/B,EAAU9L,GACtBiJ,EAAajJ,EACjB,EACAmN,KACIhD,GACA2D,GAER,EAAG,EAAE,EAHe,IAKd7P,GAAgB,IAClB,IAAMgQ,EAAM9P,EAAGkI,GAAG,CAAC,GAAe6H,EAAKC,QAAQ,EAC/ClF,EAAa,GAAqB,EAAE,GAAGuB,CAAS,CAAEvJ,EAAhB,OAA0BgN,EAAI,EACpE,EAGMG,GAAmB,IACrB,IAAMH,EAAM9P,EAAGkI,GAAG,CAAC,GAAe6H,EAAKC,QAAQ,EAC/ClF,EAAa,GAAqB,EAAE,GAAGuB,CAAS,CAAEtC,EAAhB,KAAwB+F,EAAI,EAClE,EAEMI,GAAY,IACdpF,EAAa,GAAqB,EAAE,GAAGuB,CAAS,CAAErC,EAAhB,SAA4B7J,EAAU,EAC5E,EAEMgQ,GAAe,IACjBrF,EAAcuB,GAAoB,EAAE,GAAGA,CAAS,CAAEpC,EAAhB,MAAyBmG,CAAU,GACzE,EA4EA,MACI,UAAChQ,EAAAA,CAASA,CAAAA,CAACC,UAAU,WAAWC,KAAK,aACjC,UAACmG,EAAAA,CAAIA,CAAAA,UACD,UAAC4J,EAAAA,CAAqBA,CAAAA,CAACC,SAAUlC,GAActK,IAAK2G,EAAS8F,cAAe1F,EAAW2F,oBAAoB,EAAMC,cA1RlG5I,CA0RiH6I,GAzRxIC,OAAOC,QAAQ,CAAC,EAAG,EACvB,WAyRgB,WAACnK,EAAAA,CAAIA,CAACS,IAAI,YACN,UAACvG,EAAAA,CAAGA,CAAAA,UACA,UAACJ,EAAAA,CAAGA,CAAAA,UACA,WAACkG,EAAAA,CAAIA,CAACO,KAAK,YACL,GAAqD,OAAtCrH,EAAbqM,EAAe,cAAmB,GAAFrM,eAAqB,IAAEA,EAAE,iBAAkB,WAI3F,UAACkR,KAAAA,CAAAA,GACD,UAAClQ,EAAAA,CAAGA,CAAAA,UACA,UAACJ,EAAAA,CAAGA,CAAAA,UACA,WAACK,EAAAA,CAAIA,CAACC,KAAK,YACP,UAACD,EAAAA,CAAIA,CAACE,KAAK,EAACT,UAAU,0BAAkBV,EAAE,kBAC1C,UAACsJ,EAAAA,EAASA,CAAAA,CACNK,QAAQ,IACRC,UAAW,GAAkD,KAA/BlB,OAAOrG,GAAS,IAAIwH,IAAI,GACtD5B,aAAc,CACV2B,UAAW5J,EAAE,iBACjB,EACA2D,KAAK,OACLoE,KAAK,QACL1F,MAAO6I,EAAUzB,KAAK,CACtB/H,SA3Zb,CA2ZuBgI,GA1Z1C,GAAM,MAAE3B,CAAI,OAAE1F,CAAK,CAAE,CAAG6G,EAAEC,MAAM,CAChCgC,EAAa,GAAqB,EAC9B,GAAGuB,CAAS,CACZ,CAAC3E,CAF6B,CAExB,CAAE1F,EACZ,EACJ,WA0ZoB,UAACrB,EAAAA,CAAGA,CAAAA,UACA,UAACJ,EAAAA,CAAGA,CAAAA,UACA,WAACK,EAAAA,CAAIA,CAACC,KAAK,YACP,UAACD,EAAAA,CAAIA,CAACE,KAAK,WAAEnB,EAAE,wBACf,UAAC8E,EAAAA,CAAeA,CAAAA,CAACC,YAAamG,EAAUnB,WAAW,CAAErI,SAAU,GAAiB2L,GAAoB8D,YAI/GC,CAjHA,KACjB,OAAQzF,GACJ,IAAK,eACD,MAAO,UAACnC,EAAAA,OAAgBA,CAAAA,CAACE,eAAgB+C,GAA2B,GAAGrB,CAAY,EAEvF,KAAK,OACD,MACI,UAACiG,EAAAA,OAAQA,CAAAA,CACLzG,KAAMA,EACNgC,wBAAyBA,GACzBG,WAAYA,GACZG,QAASA,IAIrB,KAAK,WACD,MACI,UAACjL,EAAAA,OAAYA,CAAAA,CACTE,QAAS4J,EACTxL,oBAAqB,GAAeiQ,GAAaC,GACjDvO,KAAM2J,EACNzL,MAAO,GAAQD,GAAcE,IAIzC,KAAK,UACD,MAAO,UAACiR,EAAAA,OAAWA,CAAAA,CAAC5H,eAAgBiD,GAAsB,GAAGpB,CAAe,EAEhF,KAAK,iBACD,MACI,UAAC5L,EAAAA,OAAiBA,CAAAA,CACdyC,OAAQ2J,EACRxL,oBAAqB,GAAeiQ,GAAaC,GACjD7Q,WAAYA,EACZC,aAAcA,GACb,GAAGqL,CAAS,CACbhJ,KAAM2J,EACNzL,MAAO,GAAQD,GAAcE,IAIzC,KAAK,QACD,MACI,UAACkR,EAAAA,OAASA,CAAAA,CACNrP,KAAM+J,EACN7J,OAAQ+J,EACR/L,MAAO,GAAQkQ,GAAiBjQ,GAChCE,oBAAqB,GAAegQ,GAAU/P,IAI1D,KAAK,mBACD,MACI,iCACI,UAACyB,EAAAA,OAAYA,CAAAA,CACTE,QAAS4J,EACTxL,oBAAqB,GAAeiQ,GAAaC,GACjDvO,KAAM2J,EACNzL,MAAO,GAAQD,GAAcE,KAEjC,UAACkR,EAAAA,OAASA,CAAAA,CACNnP,OAAQ+J,EACR5L,oBAAqB,GAAegQ,GAAU/P,GAC9C0B,KAAM+J,EACN7L,MAAO,GAAQkQ,GAAiBjQ,OAKhD,SACI,OAAO,IACf,EACJ,IA0CoB,UAACW,EAAAA,CAAGA,CAAAA,CAACN,UAAU,gBACX,UAACE,EAAAA,CAAGA,CAAAA,UACA,UAACK,EAAAA,CAAIA,CAACC,KAAK,EAACsQ,UAAU,8BAClB,UAACvQ,EAAAA,CAAIA,CAAC+H,KAAK,EACPtI,UAAU,QACVL,GAAG,qBACH0H,KAAK,qBACLpE,KAAK,WACLiF,MAAO5I,EAAE,+BACTiJ,QAASiC,EAAUf,kBAAkB,CACrCzI,SAvYPwH,CAuYiBuI,GAtY1C,GAAM,SAAExI,CAAO,CAAE,CAAGC,EAAEC,MAAM,CAE5BgC,EAAa,GAAqB,EAC9B,GAAGuB,CAAS,CACZvC,EAF8B,iBAEVlB,EACxB,EACJ,UAsYoB,UAACjI,EAAAA,CAAGA,CAAAA,CAACN,UAAU,gBACX,WAACE,EAAAA,CAAGA,CAAAA,WACA,UAAC8Q,EAAAA,CAAMA,CAAAA,CACHhR,UAAU,OACViD,KAAK,SACL0C,QAAQ,UACRzC,QAAS,KACL6H,EAAc,GAAqB,EAC/B,GAAGiB,CAAS,CACZ5M,EAF+B,SAEpB,CACf,GACJ,WAECE,EAAE,YAEP,UAAC0R,EAAAA,CAAMA,CAAAA,CAAChR,UAAU,OAAOkD,QA1W9B,CA0WuC+N,IAzW1DxG,EAAa,GAAqB,EAAE,GAAGuB,CAAS,CAAE,EAAhB,CAAmBnC,CAAU,CAAC,GAChEuB,EAA0B,EAAE,EAC5BE,EAAoB,EAAE,EACtBE,EAAsB,EAAE,EACxBE,EAAiB,EAAE,EACnBZ,EAAmBhB,GACnBc,EAAQX,GAERqG,OAAOC,QAAQ,CAAC,EAAG,EACvB,EAgW8E5K,QAAQ,gBACrDrG,EAAE,WAEP,UAAC0R,EAAAA,CAAMA,CAAAA,CAAC9N,QA1XpB,CA0X6BgO,IAzXrC5G,GAA8B,OAAO,GAAtBuB,MAAM,CAAE,EAAE,CACzB6B,IAAAA,IAAW,CAAC,IAA8BpD,MAAAA,CAA1BA,EAAMsD,WAAW,CAAC,UAAwB,OAAhBtD,EAAMuD,SAAS,GAErD/O,GAASA,EAAM8M,MAAM,EAAI9M,EAAM8M,MAAM,CAACtB,KAAK,EAC3CoD,IAAAA,IAAW,CACP,IAAyB,OAArBlD,EAAUjB,UAAU,CAAC,gBACzB,IAAiCiB,MAAAA,CAA7BA,EAAUjB,UAAU,CAAC,UAA2B,OAAnBiB,EAAUhB,QAAQ,EAInE,EA+WsD7D,QAAQ,qBAC7BrG,EAAE,yBASvC,oFCvhBA,IAAM0Q,EAAwBmB,CAAAA,EAAAA,EAAAA,UAAAA,CAAUA,CAA8C,CAACrS,EAAO2E,KAC5F,GAAM,UAAEgC,CAAQ,UAAEwK,CAAQ,cAAEmB,CAAY,CAAEpR,WAAS,CAAEqR,YAAU,eAAEnB,CAAa,CAAE,GAAGoB,EAAM,CAAGxS,EAGtFyS,EAAmBC,EAAAA,EAAU,GAAGC,KAAK,CAAC,CAAC,GAE7C,MACE,UAACC,EAAAA,EAAMA,CAAAA,CACLxB,cAAeA,GAAiB,CAAC,EACjCqB,iBAAkBA,EAClBtB,SAAU,CAAC9H,EAA6BwJ,KAEtC,IAAMC,EAAuB,CAC3B3E,eAAgB,KAAO,EACvBkB,gBAAiB,KAAO,EACxBH,cAAe,KACfvF,OAAQ,KACRoJ,YAAa,IAAIC,MAAM,UACvBC,SAAS,EACTC,YAAY,EACZC,iBAAkB,GAClBC,WAAY,EACZC,WAAW,EACXC,UAAWvR,KAAKwR,GAAG,GACnBpP,KAAM,SACNqP,mBAAoB,KAAM,EAC1BC,qBAAsB,IAAM,GAC5BC,QAAS,KAAO,CAClB,EAEIvC,GAEFA,EAAS2B,EAAWzJ,EAAQwJ,CAFhB,CAIhB,EACC,GAAGL,CAAI,UAENmB,GACA,UAAClS,EAAAA,EAAIA,CAAAA,CACHkD,IAAKA,EACLwM,SAAUwC,EAAYC,YAAY,CAClCtB,aAAcA,EACdpR,UAAWA,EACXqR,WAAYA,WAES,YAApB,OAAO5L,EAA0BA,EAASgN,GAAehN,KAKpE,GAEAuK,EAAsBhL,WAAW,CAAG,wBAEpC,MAAegL,qBAAqBA,EAAC,yEClF9B,IAAMpH,EAAY,OAAC,MACxBvB,CAAI,IACJ1H,CAAE,CACFsJ,UAAQ,CACRC,WAAS,cACT3B,CAAY,UACZvG,CAAQ,OACRW,CAAK,IACLgD,CAAE,WACFgO,CAAS,MACTC,CAAI,SACJC,CAAO,CACP,GAAG/T,EACC,GAuBJ,MACE,UAACgU,EAAAA,EAAKA,CAAAA,CAACzL,KAAMA,EAAM0L,SAtBJ,CAsBcA,GApB7B,IAAMC,EAA2B,UAAf,OAAOC,EAAmBA,EAAMjL,OAAOiL,GAAO,WAChE,GAAiB,EAACA,GAAOD,IAAR,GAAkB7J,IAAI,EAAO,CAAC,CACtC5B,EAD0C,KAC1CA,EAAAA,KAAAA,EAAAA,EAAc2B,SAAAA,GAAa,EAA3B3B,uBAGL2B,GAAa,CAACA,EAAU+J,GACnB1L,GADyB,IACzBA,EAAAA,KAAAA,EAAAA,EAAc2B,SAAAA,GAAa,EAA3B3B,cAGLsL,GAAWI,GAET,CADU,CADI,GACAC,OAAOL,GACdM,IAAI,CAACF,GACP1L,GADa,IACbA,EAAAA,KAAAA,EAAAA,EAAcsL,OAAAA,GAAW,IAAzBtL,mBAKb,WAIK,OAAC,OAAE6L,CAAK,MAAEC,CAAI,CAAO,SACpB,iCACE,UAAC9S,EAAAA,CAAIA,CAAC+S,OAAO,EACV,GAAGF,CAAK,CACR,GAAGtU,CAAK,CACTa,GAAIA,EACJgF,GAAIA,GAAM,QACViO,KAAMA,EACNW,UAAWF,EAAK5L,OAAO,EAAI,CAAC,CAAC4L,EAAKG,KAAK,CACvCxS,SAAU,IACRoS,EAAMpS,QAAQ,CAACwH,GACXxH,GAAUA,EAASwH,EACzB,EACA7G,WAAiB8R,IAAV9R,EAAsBA,EAAQyR,EAAMzR,KAAK,GAEjD0R,EAAK5L,OAAO,EAAI4L,EAAKG,KAAK,CACzB,UAACjT,EAAAA,CAAIA,CAAC+S,OAAO,CAACI,QAAQ,EAACzQ,KAAK,mBACzBoQ,EAAKG,KAAK,GAEX,UAKd,EAIa3K,EAAc,OAAC,MAC1BxB,CAAI,IACJ1H,CAAE,UACFsJ,CAAQ,CACR1B,cAAY,UACZvG,CAAQ,OACRW,CAAK,UACL8D,CAAQ,CACR,GAAG3G,EACC,GAUJ,MACE,UAACgU,EAAAA,EAAKA,CAAAA,CAACzL,KAAMA,EAAM0L,SATJ,CAScA,GAR7B,GAAI9J,GAAa,EAACgK,GAAe,IAAhB,CAAQA,CAAQ,CAAC,CAChC,EADoC,IAC7B1L,OAAAA,EAAAA,KAAAA,EAAAA,EAAc2B,SAAAA,GAAa,EAA3B3B,sBAIX,WAIK,OAAC,OAAE6L,CAAK,MAAEC,CAAI,CAAO,SACpB,iCACE,UAAC9S,EAAAA,CAAIA,CAAC+S,OAAO,EACX3O,GAAG,SACF,GAAGyO,CAAK,CACR,GAAGtU,CAAK,CACTa,GAAIA,EACJ4T,UAAWF,EAAK5L,OAAO,EAAI,CAAC,CAAC4L,EAAKG,KAAK,CACvCxS,SAAU,IACRoS,EAAMpS,QAAQ,CAACwH,GACXxH,GAAUA,EAASwH,EACzB,EACA7G,WAAiB8R,IAAV9R,EAAsBA,EAAQyR,EAAMzR,KAAK,UAE/C8D,IAEF4N,EAAK5L,OAAO,EAAI4L,EAAKG,KAAK,CACzB,UAACjT,EAAAA,CAAIA,CAAC+S,OAAO,CAACI,QAAQ,EAACzQ,KAAK,mBACzBoQ,EAAKG,KAAK,GAEX,UAKd,EAAE,kIC7EF,MAxBkB,IAChB,GAAM,CAAElU,CAAC,CAAE,CAAGC,CAAAA,EAAAA,EAAAA,EAAAA,CAAcA,CAAC,QAuBhBsR,EArBP8C,EAAQ,IACZ7U,CAoBoB+R,CApBdnR,CAoBe,IApBV,CAACC,EACd,EAEMC,EAAgB,IACpBd,EAAMe,mBAAmB,CAACC,EAC5B,EAEA,MACE,UAACC,EAAAA,CAASA,CAAAA,CAACC,UAAU,WAAWC,KAAK,aACnC,WAACC,EAAAA,CAAGA,CAAAA,WACF,UAACI,EAAAA,CAAGA,CAAAA,CAACN,UAAU,eAAeG,GAAI,YAChC,UAACC,KAAAA,UAAG,UAACC,OAAAA,UAAMf,EAAE,sBAEf,UAACgB,EAAAA,CAAGA,CAAAA,UACF,UAACsT,EAAAA,CAAaA,CAAAA,CAACC,MAAO/U,EAAM0C,IAAI,CAAEC,QAAS3C,EAAM4C,MAAM,CAAEoS,SAAU,GAAeH,EAAMhU,GAAKoU,eAAiBjU,GAAqBF,EAAcE,WAK3J,qKCgDA,MAtEiB,IACf,GAAM,CAAER,CAAC,CAAE,CAAGC,CAAAA,EAAAA,EAAAA,EAAAA,CAAcA,CAAC,QAqEhBoR,EApEP,MAAEzG,CAAI,CAoEU,wBApERgC,CAAuB,YAAEG,CAAU,SAAEG,CAAO,CAAE,CAAG1N,EAC/D,MACE,UAACiB,EAAAA,CAASA,CAAAA,CAACC,UAAU,WAAWC,KAAK,aACnC,UAACmG,EAAAA,CAAIA,CAAAA,UACH,UAAC4J,EAAAA,CAAqBA,CAAAA,CAACC,SAAU,KAAO,WACtC,WAAC7J,EAAAA,CAAIA,CAACS,IAAI,YACPqD,GAAQA,EAAKrC,GAAG,CAAC,CAAC6H,EAAWvD,IAE1B,UAACxJ,MAAAA,UACC,WAACrC,EAAAA,CAAGA,CAAAA,WACF,UAACJ,EAAAA,CAAGA,CAAAA,UACF,WAACK,EAAAA,CAAIA,CAACC,KAAK,YACT,UAACD,EAAAA,CAAIA,CAACE,KAAK,EAACT,UAAU,0BAAkBV,EAAE,kBAC1C,UAACiB,EAAAA,CAAIA,CAAC+S,OAAO,EACbjM,KAAK,QACL1H,GAAG,YACHsD,KAAK,OACLtB,MAAO+N,EAAK3G,KAAK,CACjBE,QAAQ,IACRjI,SAAU,GAAOkL,EAAwB1D,EAAG2D,KAC5C,UAAC5L,EAAAA,CAAIA,CAAC+S,OAAO,CAACI,QAAQ,EAACzQ,KAAK,mBAC3B3D,EAAE,iCAIP,UAACY,EAAAA,CAAGA,CAAAA,UACF,WAACK,EAAAA,CAAIA,CAACC,KAAK,YACT,UAACD,EAAAA,CAAIA,CAACE,KAAK,EAACT,UAAU,0BAAkBV,EAAE,iBAC1C,UAACiB,EAAAA,CAAIA,CAAC+S,OAAO,EACXjM,KAAK,OACL1H,GAAG,OACHsD,KAAK,OACLgG,QAAQ,IACRtH,MAAO+N,EAAKxF,IAAI,CAChBlJ,SAAU,GAAOkL,EAAwB1D,EAAG2D,GAC5C0G,QAAQ,uEAEV,UAACtS,EAAAA,CAAIA,CAAC+S,OAAO,CAACI,QAAQ,EAACzQ,KAAK,mBAC3B3D,EAAE,kCAIP,UAACY,EAAAA,CAAGA,CAAAA,UACF,UAACK,EAAAA,CAAIA,CAACC,KAAK,WACF,IAAN2L,EACC,UAACxJ,MAAAA,CAAAA,GAEC,UAACqO,EAAAA,CAAMA,CAAAA,CAACrL,QAAQ,YAAYtE,MAAO,CAAE2S,UAAW,MAAO,EAAG9Q,QAAUsF,GAAM6D,EAAW7D,EAAG2D,YAAM7M,EAAE,6BAShH,UAACgB,EAAAA,CAAGA,CAAAA,UACF,UAACJ,EAAAA,CAAGA,CAAAA,CAAC+T,EAAE,IAAC9T,GAAG,aACT,UAAC6Q,EAAAA,CAAMA,CAAAA,CAACrL,QAAQ,YAAYtE,MAAO,CAAE2S,UAAW,OAAQE,aAAc,MAAO,EAAGhR,QAASsJ,WAAUlN,EAAE,4BAQrH,oIChCA,MAxCoB,IAChB,GAAM,CAAEA,CAAC,CAAE,CAAGC,CAAAA,EAAAA,EAAAA,EAAAA,CAAcA,CAAC,QAuClBqR,EAtCL,SAsCgBA,EAAC,KAtCf5H,CAAc,aAAEe,CAAW,UAAEC,CAAQ,CAAE,CAAGlL,EAElD,MACI,UAACiB,EAAAA,CAASA,CAAAA,CAACC,UAAU,WAAWC,KAAK,aACjC,UAACK,EAAAA,CAAGA,CAAAA,UACA,WAACJ,EAAAA,CAAGA,CAAAA,WACA,WAACK,EAAAA,CAAIA,CAACC,KAAK,YACP,UAACD,EAAAA,CAAIA,CAACE,KAAK,EAACT,UAAU,0BAAkBV,EAAE,yBAC1C,UAACiB,EAAAA,CAAIA,CAAC+S,OAAO,EACTrQ,KAAK,SACLoE,KAAK,cACLxF,YAAavC,EAAE,2BACf2J,QAAQ,IACRtH,MAAOoI,EACP/I,SAAUgI,IAEd,UAACzI,EAAAA,CAAIA,CAAC+S,OAAO,CAACI,QAAQ,EAACzQ,KAAK,mBACvB3D,EAAE,sCAGX,WAACiB,EAAAA,CAAIA,CAACC,KAAK,YACP,UAACD,EAAAA,CAAIA,CAACE,KAAK,EAACT,UAAU,0BAAkBV,EAAE,sBAC1C,UAACiB,EAAAA,CAAIA,CAAC+S,OAAO,EACTrQ,KAAK,SACLoE,KAAK,WACLxF,YAAavC,EAAE,wBACf2J,QAAQ,IACRtH,MAAOqI,EACPhJ,SAAUgI,IAEd,UAACzI,EAAAA,CAAIA,CAAC+S,OAAO,CAACI,QAAQ,EAACzQ,KAAK,mBAAW3D,EAAE,0CAMjE,oCCjDA,IAAM6U,EAAuB1P,QAAb,CAAaA,UAAF,GAAqB,CAAC,MACjD0P,EAAQnP,WAAW,CAAG,oBACtB,MAAemP", "sources": ["webpack://_N_E/./pages/updates/CalendarEventForm.tsx", "webpack://_N_E/./components/common/SimpleRichTextEditor.tsx", "webpack://_N_E/./shared/quill-editor/quill-editor.component.tsx", "webpack://_N_E/./components/common/RKIDatePicker.tsx", "webpack://_N_E/./node_modules/react-bootstrap/esm/CardBody.js", "webpack://_N_E/./node_modules/react-bootstrap/esm/CardFooter.js", "webpack://_N_E/./node_modules/react-bootstrap/esm/CardHeader.js", "webpack://_N_E/./node_modules/react-bootstrap/esm/CardImg.js", "webpack://_N_E/./node_modules/react-bootstrap/esm/CardImgOverlay.js", "webpack://_N_E/./node_modules/react-bootstrap/esm/CardLink.js", "webpack://_N_E/./node_modules/react-bootstrap/esm/CardSubtitle.js", "webpack://_N_E/./node_modules/react-bootstrap/esm/CardText.js", "webpack://_N_E/./node_modules/react-bootstrap/esm/CardTitle.js", "webpack://_N_E/./node_modules/react-bootstrap/esm/Card.js", "webpack://_N_E/./components/common/FormikRadio.tsx", "webpack://_N_E/./components/common/FormValidation.tsx", "webpack://_N_E/./pages/updates/ConversationForm.tsx", "webpack://_N_E/./pages/updates/index.tsx", "webpack://_N_E/./components/common/ValidationFormWrapper.tsx", "webpack://_N_E/./components/common/FormikTextInput.tsx", "webpack://_N_E/./pages/updates/ImageForm.tsx", "webpack://_N_E/./pages/updates/LinkForm.tsx", "webpack://_N_E/./pages/updates/ContactForm.tsx", "webpack://_N_E/./node_modules/react-bootstrap/esm/CardHeaderContext.js"], "sourcesContent": ["//Import Library\r\nimport React, { useEffect, useState } from \"react\";\r\nimport { Form, Container, Row, Col } from \"react-bootstrap\";\r\n\r\n//Import services/components\r\nimport DocumentForm from \"./DocumentForm\";\r\nimport RKIDatePicker from \"../../components/common/RKIDatePicker\";\r\nimport { useTranslation } from 'next-i18next';\r\n\r\n//TOTO refactor\r\ninterface CalendarEventFormProps {\r\n  validation: any;\r\n  onChangeDate: (date: Date, key: string) => void;\r\n  startDate: Date | null;\r\n  endDate: Date | null;\r\n  data: any[];\r\n  getId: (id: any[]) => void;\r\n  imgSrc: any[];\r\n  getSourceCollection: (docSrcArr: any[]) => void;\r\n}\r\n\r\nconst CalendarEventForm = (props: CalendarEventFormProps): React.ReactElement => {\r\n  const [, setEndDate] = useState<boolean>(false);\r\n  const { validation, onChangeDate, startDate, endDate } = props;\r\n  const { t } = useTranslation('common');\r\n\r\n  useEffect(() => {\r\n    endDate == null ? setEndDate(false) : setEndDate(true);\r\n  }, [endDate]);\r\n\r\n  const uploadHandler = (id: any[]) => {\r\n    props.getId(id);\r\n  };\r\n\r\n  const getSourceText = (imgSrcArr: any[]) => {\r\n    props.getSourceCollection(imgSrcArr);\r\n  }\r\n\r\n  return (\r\n    <Container className=\"formCard\" fluid>\r\n      <Col className=\"header-block\" lg={12}>\r\n        <h6>\r\n          <span>{t(\"update.Date\")}</span>\r\n        </h6>\r\n      </Col>\r\n      <Row>\r\n        <Col>\r\n          <Form.Group>\r\n            <Form.Label className=\"d-block required-field\">{t(\"update.StartDate\")}</Form.Label>\r\n            <RKIDatePicker\r\n              selected={startDate}\r\n              minDate={new Date()}\r\n              showTimeSelect\r\n              timeIntervals={15}\r\n              onChange={(date: Date) => onChangeDate(date, \"startDate\")}\r\n              placeholderText= {t(\"update.Selectadate\")}\r\n              dateFormat=\"MMMM d, yyyy h:mm aa\"\r\n            />\r\n          </Form.Group>\r\n          { (  validation.startDate === true && (!startDate)) &&  <p style={{  color: \"red\"}}>{t(\"update.Pleaseenterthestartdate\")}</p>}\r\n\r\n        </Col>\r\n        <Col>\r\n\r\n            <Form.Group>\r\n              <Form.Label className=\"d-block required-field\">{t(\"update.EndDate\")}</Form.Label>\r\n              <RKIDatePicker\r\n                selected={endDate}\r\n                showTimeSelect\r\n                timeIntervals={15}\r\n                onChange={(date: Date) => onChangeDate(date, \"endDate\")}\r\n                placeholderText={t(\"update.Selectadate\")}\r\n                minDate={startDate}\r\n                dateFormat=\"MMMM d, yyyy h:mm aa\"\r\n              />\r\n            </Form.Group>\r\n            { (  validation.startDate === true && (!endDate)) &&  <p style={{  color: \"red\"}}>{t(\"update.Pleaseentertheenddate\")} </p>}\r\n\r\n        </Col>\r\n      </Row>\r\n      <DocumentForm data={props.data} srcText={props.imgSrc} getId={(id) => uploadHandler(id)} getSourceCollection={(imgSrcArr) => getSourceText(imgSrcArr)} />\r\n    </Container>\r\n  );\r\n};\r\nexport default CalendarEventForm;\r\n", "import React, { useRef, useEffect, useState } from 'react';\r\n\r\ninterface SimpleRichTextEditorProps {\r\n  value: string;\r\n  onChange: (content: string) => void;\r\n  placeholder?: string;\r\n  height?: number;\r\n  disabled?: boolean;\r\n}\r\n\r\nconst SimpleRichTextEditor: React.FC<SimpleRichTextEditorProps> = ({\r\n  value,\r\n  onChange,\r\n  placeholder = 'Write something...',\r\n  height = 300,\r\n  disabled = false,\r\n}) => {\r\n  const editorRef = useRef<HTMLDivElement>(null);\r\n  const [isFocused, setIsFocused] = useState(false);\r\n\r\n  // Initialize editor with HTML content\r\n  useEffect(() => {\r\n    if (editorRef.current && typeof window !== 'undefined') {\r\n      // Only update if the editor doesn't have focus to prevent cursor jumping\r\n      if (!isFocused && editorRef.current.innerHTML !== value) {\r\n        editorRef.current.innerHTML = value || '';\r\n      }\r\n    }\r\n  }, [value, isFocused]);\r\n\r\n  // Handle content changes\r\n  const handleInput = () => {\r\n    if (editorRef.current && onChange) {\r\n      onChange(editorRef.current.innerHTML);\r\n    }\r\n  };\r\n\r\n  // Simple toolbar buttons\r\n  const execCommand = (command: string, value?: string) => {\r\n    if (typeof document !== 'undefined') {\r\n      document.execCommand(command, false, value || '');\r\n      handleInput();\r\n      editorRef.current?.focus();\r\n    }\r\n  };\r\n\r\n  return (\r\n    <div className=\"simple-rich-text-editor\" style={{ border: '1px solid #ccc' }}>\r\n      {typeof window !== 'undefined' && (\r\n      <>\r\n        <div className=\"toolbar\" style={{ padding: '8px', borderBottom: '1px solid #ccc', background: '#f5f5f5' }}>\r\n            <button\r\n              type=\"button\"\r\n              onClick={() => execCommand('bold')}\r\n              style={{ margin: '0 5px', padding: '3px 8px' }}\r\n            >\r\n              <strong>B</strong>\r\n            </button>\r\n            <button\r\n              type=\"button\"\r\n              onClick={() => execCommand('italic')}\r\n              style={{ margin: '0 5px', padding: '3px 8px' }}\r\n            >\r\n              <em>I</em>\r\n            </button>\r\n            <button\r\n              type=\"button\"\r\n              onClick={() => execCommand('underline')}\r\n              style={{ margin: '0 5px', padding: '3px 8px' }}\r\n            >\r\n              <u>U</u>\r\n            </button>\r\n            <button\r\n              type=\"button\"\r\n              onClick={() => execCommand('insertOrderedList')}\r\n              style={{ margin: '0 5px', padding: '3px 8px' }}\r\n            >\r\n              OL\r\n            </button>\r\n            <button\r\n              type=\"button\"\r\n              onClick={() => execCommand('insertUnorderedList')}\r\n              style={{ margin: '0 5px', padding: '3px 8px' }}\r\n            >\r\n              UL\r\n            </button>\r\n            <button\r\n              type=\"button\"\r\n              onClick={() => {\r\n                const url = prompt('Enter the link URL');\r\n                if (url) execCommand('createLink', url);\r\n              }}\r\n              style={{ margin: '0 5px', padding: '3px 8px' }}\r\n            >\r\n              Link\r\n            </button>\r\n          </div>\r\n          <div\r\n            ref={editorRef}\r\n            contentEditable={!disabled}\r\n            onInput={handleInput}\r\n            onFocus={() => setIsFocused(true)}\r\n            onBlur={() => setIsFocused(false)}\r\n            style={{\r\n              padding: '15px',\r\n              minHeight: height,\r\n              maxHeight: height * 2,\r\n              overflow: 'auto',\r\n              outline: 'none',\r\n            }}\r\n            data-placeholder={!value ? placeholder : ''}\r\n            suppressContentEditableWarning={true}\r\n          >\r\n          </div>\r\n      </>\r\n      )}\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default SimpleRichTextEditor;\r\n", "// React Imports\r\nimport React from \"react\";\r\nimport SimpleRichTextEditor from \"../../components/common/SimpleRichTextEditor\";\r\n\r\ninterface IEditorComponentProps {\r\n  initContent: string | undefined;\r\n  onChange: Function;\r\n}\r\n\r\nexport const EditorComponent: React.FC<IEditorComponentProps> = (props) => {\r\n  const { initContent, onChange } = props;\r\n\r\n  return (\r\n    <SimpleRichTextEditor\r\n      value={initContent || \"\"}\r\n      onChange={(content) => onChange(content)}\r\n    />\r\n  );\r\n};\r\n", "//Import Library\r\nimport React from 'react'\r\nimport DatePicker from \"react-datepicker\";\r\n\r\ninterface RKIDatePickerProps {\r\n  [key: string]: any;\r\n}\r\n\r\nconst RKIDatePicker = (props: RKIDatePickerProps) => {\r\n  return (\r\n    <DatePicker {...props}  />\r\n  )\r\n};\r\n\r\nexport default RKIDatePicker;\r\n", "\"use client\";\n\nimport * as React from 'react';\nimport classNames from 'classnames';\nimport { useBootstrapPrefix } from './ThemeProvider';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst CardBody = /*#__PURE__*/React.forwardRef(({\n  className,\n  bsPrefix,\n  as: Component = 'div',\n  ...props\n}, ref) => {\n  bsPrefix = useBootstrapPrefix(bsPrefix, 'card-body');\n  return /*#__PURE__*/_jsx(Component, {\n    ref: ref,\n    className: classNames(className, bsPrefix),\n    ...props\n  });\n});\nCardBody.displayName = 'CardBody';\nexport default CardBody;", "\"use client\";\n\nimport * as React from 'react';\nimport classNames from 'classnames';\nimport { useBootstrapPrefix } from './ThemeProvider';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst CardFooter = /*#__PURE__*/React.forwardRef(({\n  className,\n  bsPrefix,\n  as: Component = 'div',\n  ...props\n}, ref) => {\n  bsPrefix = useBootstrapPrefix(bsPrefix, 'card-footer');\n  return /*#__PURE__*/_jsx(Component, {\n    ref: ref,\n    className: classNames(className, bsPrefix),\n    ...props\n  });\n});\nCardFooter.displayName = 'CardFooter';\nexport default CardFooter;", "\"use client\";\n\nimport classNames from 'classnames';\nimport * as React from 'react';\nimport { useMemo } from 'react';\nimport { useBootstrapPrefix } from './ThemeProvider';\nimport CardHeaderContext from './CardHeaderContext';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst CardHeader = /*#__PURE__*/React.forwardRef(({\n  bsPrefix,\n  className,\n  // Need to define the default \"as\" during prop destructuring to be compatible with styled-components github.com/react-bootstrap/react-bootstrap/issues/3595\n  as: Component = 'div',\n  ...props\n}, ref) => {\n  const prefix = useBootstrapPrefix(bsPrefix, 'card-header');\n  const contextValue = useMemo(() => ({\n    cardHeaderBsPrefix: prefix\n  }), [prefix]);\n  return /*#__PURE__*/_jsx(CardHeaderContext.Provider, {\n    value: contextValue,\n    children: /*#__PURE__*/_jsx(Component, {\n      ref: ref,\n      ...props,\n      className: classNames(className, prefix)\n    })\n  });\n});\nCardHeader.displayName = 'CardHeader';\nexport default CardHeader;", "\"use client\";\n\nimport classNames from 'classnames';\nimport * as React from 'react';\nimport { useBootstrapPrefix } from './ThemeProvider';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst CardImg = /*#__PURE__*/React.forwardRef(\n// Need to define the default \"as\" during prop destructuring to be compatible with styled-components github.com/react-bootstrap/react-bootstrap/issues/3595\n({\n  bsPrefix,\n  className,\n  variant,\n  as: Component = 'img',\n  ...props\n}, ref) => {\n  const prefix = useBootstrapPrefix(bsPrefix, 'card-img');\n  return /*#__PURE__*/_jsx(Component, {\n    ref: ref,\n    className: classNames(variant ? `${prefix}-${variant}` : prefix, className),\n    ...props\n  });\n});\nCardImg.displayName = 'CardImg';\nexport default CardImg;", "\"use client\";\n\nimport * as React from 'react';\nimport classNames from 'classnames';\nimport { useBootstrapPrefix } from './ThemeProvider';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst CardImgOverlay = /*#__PURE__*/React.forwardRef(({\n  className,\n  bsPrefix,\n  as: Component = 'div',\n  ...props\n}, ref) => {\n  bsPrefix = useBootstrapPrefix(bsPrefix, 'card-img-overlay');\n  return /*#__PURE__*/_jsx(Component, {\n    ref: ref,\n    className: classNames(className, bsPrefix),\n    ...props\n  });\n});\nCardImgOverlay.displayName = 'CardImgOverlay';\nexport default CardImgOverlay;", "\"use client\";\n\nimport * as React from 'react';\nimport classNames from 'classnames';\nimport { useBootstrapPrefix } from './ThemeProvider';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst CardLink = /*#__PURE__*/React.forwardRef(({\n  className,\n  bsPrefix,\n  as: Component = 'a',\n  ...props\n}, ref) => {\n  bsPrefix = useBootstrapPrefix(bsPrefix, 'card-link');\n  return /*#__PURE__*/_jsx(Component, {\n    ref: ref,\n    className: classNames(className, bsPrefix),\n    ...props\n  });\n});\nCardLink.displayName = 'CardLink';\nexport default CardLink;", "\"use client\";\n\nimport * as React from 'react';\nimport classNames from 'classnames';\nimport { useBootstrapPrefix } from './ThemeProvider';\nimport divWithClassName from './divWithClassName';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst DivStyledAsH6 = divWithClassName('h6');\nconst CardSubtitle = /*#__PURE__*/React.forwardRef(({\n  className,\n  bsPrefix,\n  as: Component = DivStyledAsH6,\n  ...props\n}, ref) => {\n  bsPrefix = useBootstrapPrefix(bsPrefix, 'card-subtitle');\n  return /*#__PURE__*/_jsx(Component, {\n    ref: ref,\n    className: classNames(className, bsPrefix),\n    ...props\n  });\n});\nCardSubtitle.displayName = 'CardSubtitle';\nexport default CardSubtitle;", "\"use client\";\n\nimport * as React from 'react';\nimport classNames from 'classnames';\nimport { useBootstrapPrefix } from './ThemeProvider';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst CardText = /*#__PURE__*/React.forwardRef(({\n  className,\n  bsPrefix,\n  as: Component = 'p',\n  ...props\n}, ref) => {\n  bsPrefix = useBootstrapPrefix(bsPrefix, 'card-text');\n  return /*#__PURE__*/_jsx(Component, {\n    ref: ref,\n    className: classNames(className, bsPrefix),\n    ...props\n  });\n});\nCardText.displayName = 'CardText';\nexport default CardText;", "\"use client\";\n\nimport * as React from 'react';\nimport classNames from 'classnames';\nimport { useBootstrapPrefix } from './ThemeProvider';\nimport divWithClassName from './divWithClassName';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst DivStyledAsH5 = divWithClassName('h5');\nconst CardTitle = /*#__PURE__*/React.forwardRef(({\n  className,\n  bsPrefix,\n  as: Component = DivStyledAsH5,\n  ...props\n}, ref) => {\n  bsPrefix = useBootstrapPrefix(bsPrefix, 'card-title');\n  return /*#__PURE__*/_jsx(Component, {\n    ref: ref,\n    className: classNames(className, bsPrefix),\n    ...props\n  });\n});\nCardTitle.displayName = 'CardTitle';\nexport default CardTitle;", "\"use client\";\n\nimport classNames from 'classnames';\nimport * as React from 'react';\nimport { useBootstrapPrefix } from './ThemeProvider';\nimport CardBody from './CardBody';\nimport CardFooter from './CardFooter';\nimport CardHeader from './CardHeader';\nimport CardImg from './CardImg';\nimport CardImgOverlay from './CardImgOverlay';\nimport CardLink from './CardLink';\nimport CardSubtitle from './CardSubtitle';\nimport CardText from './CardText';\nimport CardTitle from './CardTitle';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst Card = /*#__PURE__*/React.forwardRef(({\n  bsPrefix,\n  className,\n  bg,\n  text,\n  border,\n  body = false,\n  children,\n  // Need to define the default \"as\" during prop destructuring to be compatible with styled-components github.com/react-bootstrap/react-bootstrap/issues/3595\n  as: Component = 'div',\n  ...props\n}, ref) => {\n  const prefix = useBootstrapPrefix(bsPrefix, 'card');\n  return /*#__PURE__*/_jsx(Component, {\n    ref: ref,\n    ...props,\n    className: classNames(className, prefix, bg && `bg-${bg}`, text && `text-${text}`, border && `border-${border}`),\n    children: body ? /*#__PURE__*/_jsx(CardBody, {\n      children: children\n    }) : children\n  });\n});\nCard.displayName = 'Card';\nexport default Object.assign(Card, {\n  Img: CardImg,\n  Title: CardTitle,\n  Subtitle: CardSubtitle,\n  Body: CardBody,\n  Link: CardLink,\n  Text: CardText,\n  Header: CardHeader,\n  Footer: CardFooter,\n  ImgOverlay: CardImgOverlay\n});", "import React from 'react';\r\nimport { useFormikContext, Field } from 'formik';\r\nimport { Form } from 'react-bootstrap';\r\n\r\ninterface RadioItemProps {\r\n  id: string;\r\n  label: string;\r\n  value: string;\r\n  name?: string;\r\n  disabled?: boolean;\r\n}\r\n\r\ninterface RadioGroupProps {\r\n  name: string;\r\n  valueSelected: string;\r\n  onChange: (e: React.ChangeEvent<HTMLInputElement>) => void;\r\n  errorMessage?: string;\r\n  children: React.ReactNode;\r\n}\r\n\r\n// Type Guard to ensure child.props is an object\r\nfunction isObject(props: any): props is { [key: string]: any } {\r\n  return typeof props === 'object' && props !== null;\r\n}\r\n\r\nconst RadioItem: React.FC<RadioItemProps> = ({ id, label, value, name, disabled }) => {\r\n  const { values, setFieldValue } = useFormikContext<any>();\r\n  const fieldName = name || id;\r\n\r\n  return (\r\n    <Form.Check\r\n      type=\"radio\"\r\n      id={id}\r\n      label={label}\r\n      value={value}\r\n      name={fieldName}\r\n      checked={values[fieldName] === value}\r\n      onChange={(e) => {\r\n        setFieldValue(fieldName, e.target.value);\r\n      }}\r\n      disabled={disabled}\r\n      inline\r\n    />\r\n  );\r\n};\r\n\r\nconst RadioGroup: React.FC<RadioGroupProps> = ({\r\n  name,\r\n  valueSelected,\r\n  onChange,\r\n  errorMessage,\r\n  children,\r\n}) => {\r\n  const { errors, touched } = useFormikContext<any>();\r\n  const hasError = touched[name] && errors[name];\r\n\r\n  // Create a context to pass the name to RadioItems\r\n  const radioContext = React.useMemo(() => ({ name }), [name]);\r\n\r\n  // Clone children to pass the name, ensuring props are spread safely\r\n  const childrenWithProps = React.Children.map(children, (child) => {\r\n    if (React.isValidElement(child)) {\r\n      // Ensure child.props is an object before spreading\r\n      if (isObject(child.props)) {\r\n        return React.cloneElement(child as React.ReactElement<RadioItemProps>, {\r\n          name,\r\n          ...child.props, // Safely spread child.props\r\n        });\r\n      }\r\n    }\r\n    return child;\r\n  });\r\n\r\n  return (\r\n    <div>\r\n      <div className=\"radio-group\">\r\n        {childrenWithProps}\r\n      </div>\r\n      {hasError && (\r\n        <div className=\"invalid-feedback d-block\">\r\n          {errorMessage || (typeof errors[name] === 'string' ? errors[name] : String(errors[name]))}\r\n        </div>\r\n      )}\r\n    </div>\r\n  );\r\n};\r\n\r\nexport const Radio = {\r\n  RadioGroup,\r\n  RadioItem,\r\n};\r\n\r\nexport default Radio;\r\n", "// This file exports all the components needed to replace react-bootstrap4-form-validation\r\n// It serves as a drop-in replacement for the original library\r\n\r\nimport ValidationForm from './ValidationFormWrapper';\r\nimport { TextInput, SelectGroup } from './FormikTextInput';\r\nimport { Radio } from './FormikRadio';\r\n\r\n// Export all components\r\nexport {\r\n  ValidationForm,\r\n  TextInput,\r\n  SelectGroup,\r\n  Radio\r\n};\r\n\r\n// Also export as default for convenience\r\nexport default {\r\n  ValidationForm,\r\n  TextInput,\r\n  SelectGroup,\r\n  Radio\r\n};\r\n", "//Import Library\r\nimport React from \"react\";\r\nimport { Form, Container, Row, Col } from \"react-bootstrap\";\r\nimport { TextInput } from \"../../components/common/FormValidation\";\r\n\r\n//Import services/components\r\nimport { useTranslation } from 'next-i18next';\r\n\r\n//TOTO refactor\r\ninterface ConversationFormProps {\r\n  onHandleChange: (e: React.ChangeEvent<HTMLInputElement>) => void;\r\n  title: string;\r\n}\r\n\r\nconst ConversationForm = (props: ConversationFormProps): React.ReactElement => {\r\n    const { t } = useTranslation('common');\r\n    const { title, onHandleChange } = props;\r\n    return (\r\n        <Container className=\"formCard\" fluid>\r\n            <Row>\r\n                <Col>\r\n                    <Form.Group>\r\n                        <Form.Label className=\"required-field\">\r\n                            {t(\"update.Conversation\")} {t(\"update.Title\")}\r\n                        </Form.Label>\r\n                        <TextInput\r\n                            name=\"title\"\r\n                            id=\"title\"\r\n                            required\r\n                            value={title}\r\n                            onChange={onHandleChange}\r\n                            validator={(value: string) => value.trim() != \"\"}\r\n                            errorMessage={{\r\n                                validator: t(\"Pleaseprovideconversationatitle\"),\r\n                            }}\r\n                        />\r\n                    </Form.Group>\r\n                </Col>\r\n            </Row>\r\n        </Container>\r\n    );\r\n};\r\n\r\nexport default ConversationForm;\r\n", "//Import Library\r\nimport React, { useEffect, useState, useRef } from \"react\";\r\nimport { <PERSON><PERSON>, Card, Form, Container, Row, Col } from \"react-bootstrap\";\r\nimport Router, { useRouter } from \"next/router\";\r\nimport moment from \"moment\";\r\nimport _ from \"lodash\";\r\nimport { TextInput } from \"../../components/common/FormValidation\";\r\nimport ValidationFormWrapper from \"../../components/common/ValidationFormWrapper\";\r\n\r\n//Import services/components\r\nimport LinkForm from \"./LinkForm\";\r\nimport ConversationForm from \"./ConversationForm\";\r\nimport DocumentForm from \"./DocumentForm\";\r\nimport ContactForm from \"./ContactForm\";\r\nimport CalendarEventForm from \"./CalendarEventForm\";\r\nimport ImageForm from \"./ImageForm\";\r\nimport apiService from \"../../services/apiService\";\r\nimport { useTranslation } from 'next-i18next';\r\nimport { serverSideTranslations } from 'next-i18next/serverSideTranslations';\r\nimport { EditorComponent } from \"../../shared/quill-editor/quill-editor.component\"\r\n\r\n\r\nconst initialFormValue = {\r\n    title: \"\",\r\n    description: null,\r\n    startDate: null,\r\n    endDate: null,\r\n    classification: null,\r\n    parentType: null,\r\n    parentId: null,\r\n    showAsAnnouncement: false,\r\n    images: [],\r\n    document: [],\r\n    images_src: [],\r\n    doc_src: [],\r\n};\r\n\r\nconst resetState = {\r\n    title: \"\",\r\n    description: null,\r\n    startDate: null,\r\n    endDate: null,\r\n    classification: null,\r\n    showAsAnnouncement: false,\r\n    images: [],\r\n    document: [],\r\n    images_src: [],\r\n    doc_src: [],\r\n};\r\n\r\nconst contactState = { telephoneNo: \"\", mobileNo: \"\" };\r\n\r\nconst linkState = [{ title: \"\", link: \"\" }];\r\n\r\nconst Classifications = [\r\n    {\r\n        name: \"Confidential\",\r\n        value: 1,\r\n    },\r\n    {\r\n        name: \"Restricted\",\r\n        value: 2,\r\n    },\r\n    {\r\n        name: \"Public\",\r\n        value: 3,\r\n    },\r\n];\r\nconst validationIntialState = {\r\n    startDate: false,\r\n    endDate: false,\r\n};\r\n\r\ninterface UpdateFormProps {\r\n  router: any;\r\n}\r\n\r\nconst UpdateForm = (props: UpdateFormProps): React.ReactElement => {\r\n    const formRef = useRef(null);\r\n\r\n    const { t } = useTranslation('common');\r\n    const { query } = useRouter();\r\n    const [formState, setFormState] = useState<any>(initialFormValue);\r\n    const [conversation, setConversation] = useState({ title: \"\" });\r\n    const [link, setLink] = useState([{ title: \"\", link: \"\" }]);\r\n    const [contact_details, setContact_details] = useState(contactState);\r\n    const [validation, setValidation] = useState(validationIntialState);\r\n    const [, setValidated] = useState(false);\r\n    const [updateType, setUpdateType] = useState(\"\");\r\n    const [documentDropZone, setDocumentDropCollection] = useState([]);\r\n    const [docSrcCollection, setDocSrcCollection] = useState([]);\r\n    const [dropZoneCollection, setDropZoneCollection] = useState([]);\r\n    const [srcCollection, setSrcCollection] = useState([]);\r\n    const isEditForm =\r\n        props.router &&\r\n        props.router.query &&\r\n        props.router.query.routes &&\r\n        props.router.query.routes.length > 1 &&\r\n        props.router.query.routes[1];\r\n\r\n    const onHandleChange = (e: React.ChangeEvent<HTMLInputElement>) => {\r\n        const { name, value } = e.target;\r\n        setFormState((prevState: any) => ({\r\n            ...prevState,\r\n            [name]: value,\r\n        }));\r\n    };\r\n\r\n    const conversationHandleChange = (e: React.ChangeEvent<HTMLInputElement>) => {\r\n        const { name, value } = e.target;\r\n        setConversation((prevState: any) => ({\r\n            ...prevState,\r\n            [name]: value,\r\n        }));\r\n    };\r\n\r\n    const ContactHandleChange = (e: React.ChangeEvent<HTMLInputElement>) => {\r\n        const { name, value } = e.target;\r\n        setContact_details((prevState: any) => ({\r\n            ...prevState,\r\n            [name]: value,\r\n        }));\r\n    };\r\n\r\n    const handleChangeforTimeline = (e: React.ChangeEvent<HTMLInputElement>, i: number) => {\r\n        const { name, value } = e.target;\r\n        const _tempCosts: any[] = [...link];\r\n        _tempCosts[i][name] = value;\r\n        setLink(_tempCosts);\r\n    };\r\n\r\n    const removeForm = (_e: any, i: any) => {\r\n        link.splice(i, 1);\r\n        setLink([...link]);\r\n        if (link.length === 0) {\r\n            addform();\r\n        }\r\n    };\r\n\r\n    const addform = () => {\r\n        const a = { title: \"\", link: \"\" };\r\n        setLink((oldArray) => [...oldArray, a]);\r\n    };\r\n\r\n    const onCheckAnnouncement = (e: React.ChangeEvent<HTMLInputElement>) => {\r\n        const { checked } = e.target;\r\n\r\n        setFormState((prevState: any) => ({\r\n            ...prevState,\r\n            showAsAnnouncement: checked,\r\n        }));\r\n    };\r\n\r\n    const onChangeDescription = (value: string) => {\r\n        setFormState((prevState: any) => ({\r\n            ...prevState,\r\n            description: value,\r\n        }));\r\n    };\r\n\r\n    const onChangeDate = (date: Date, key: string) => {\r\n        setFormState((prevState: any) => ({\r\n            ...prevState,\r\n            [key]: date,\r\n        }));\r\n\r\n        setValidation((prevState: any) => ({\r\n            ...prevState,\r\n            startDate: key === \"startDate\" ? true : false,\r\n            endDate: key === \"endDate\" ? true : false,\r\n        }));\r\n    };\r\n\r\n    const checkCustomValidation = () => {\r\n        if (!formState.startDate) {\r\n            setValidation((prev) => ({ ...prev, startDate: true }));\r\n            return false;\r\n        }\r\n        return true;\r\n    };\r\n\r\n    const getItem = () => {\r\n        if (query && query.routes![0] === \"add\") {\r\n            Router.push(`/${query.parent_type}/show/${query.parent_id}`);\r\n        } else {\r\n            if (props && props.router && props.router.query) {\r\n                Router.push(\r\n                    `/${formState.parentType}/[...routes]`,\r\n                    `/${formState.parentType}/show/${formState.parentId}`\r\n                );\r\n            }\r\n        }\r\n    };\r\n\r\n    const onResetHandler = () => {\r\n        setFormState((prevState: any) => ({ ...prevState, ...resetState }));\r\n        setDocumentDropCollection([]);\r\n        setDocSrcCollection([]);\r\n        setDropZoneCollection([]);\r\n        setSrcCollection([]);\r\n        setContact_details(contactState);\r\n        setLink(linkState);\r\n        // Reset validation state (Formik handles this automatically)\r\n        window.scrollTo(0, 0);\r\n    };\r\n\r\n    const handleErrorSubmit = (errors: any) => {\r\n        window.scrollTo(0, 0);\r\n    };\r\n\r\n    const getresponse = (event: Event) => {\r\n        const { startDate, endDate } = formState;\r\n        if (updateType === \"Calendar Event\") {\r\n            setValidation((prevState: any) => ({\r\n                ...prevState,\r\n                startDate: true,\r\n            }));\r\n            if (!startDate || !endDate) {\r\n                event.preventDefault();\r\n                return true;\r\n            }\r\n        }\r\n\r\n        if (updateType === \"Link\") {\r\n            let isTitle = _.findIndex(link, { title: \"\" });\r\n            let isLink = _.findIndex(link, { link: \"\" });\r\n            if (isTitle !== -1 || isLink !== -1) {\r\n                event.preventDefault();\r\n                return true;\r\n            }\r\n        }\r\n    };\r\n\r\n    const getprops = async (data: any) => {\r\n        data.type = formState.parentType;\r\n        const respData = await apiService.patch(`/updates/${props.router.query.routes[1]}`, data);\r\n        if (respData && respData._id) {\r\n            Router.push(`/${formState.parentType}/[...routes]`, `/${formState.parentType}/show/${formState.parentId}`);\r\n        }\r\n    };\r\n\r\n    const getelseprops = async (data: any) => {\r\n        switch (props.router.query.parent_type) {\r\n            case \"operation\":\r\n                data[\"parent_operation\"] = props.router.query.parent_id;\r\n                break;\r\n            case \"event\":\r\n                data[\"parent_event\"] = props.router.query.parent_id;\r\n                break;\r\n            case \"project\":\r\n                data[\"parent_project\"] = props.router.query.parent_id;\r\n                break;\r\n            case \"vspace\":\r\n                data[\"parent_vspace\"] = props.router.query.parent_id;\r\n                break;\r\n            case \"country\":\r\n                data[\"parent_country\"] = props.router.query.parent_id;\r\n                break;\r\n            case \"hazard\":\r\n                data[\"parent_hazard\"] = props.router.query.parent_id;\r\n                break;\r\n            case \"institution\":\r\n                data[\"parent_institution\"] = props.router.query.parent_id;\r\n                break;\r\n            default:\r\n                break;\r\n        }\r\n        const respData = await apiService.post(\"/updates\", data);\r\n\r\n        if (respData && respData._id) {\r\n            Router.push(\r\n                `/${props.router.query.parent_type}/[...routes]`,\r\n                `/${props.router.query.parent_type}/show/${props.router.query.parent_id}`\r\n            );\r\n        }\r\n    };\r\n\r\n    const onSubmitForm = async (event: any, values?: any) => {\r\n        const { title, description, startDate, endDate, showAsAnnouncement, document, doc_src, images, images_src } =\r\n            values || formState;\r\n        if (getresponse(event)) {\r\n            return;\r\n        }\r\n        if (event && event.preventDefault) {\r\n            event.preventDefault();\r\n        }\r\n\r\n        // Skip form validation for ValidationFormWrapper since Formik handles it\r\n        if (event && event.currentTarget && checkCustomValidation() === false) {\r\n            const form = event.currentTarget as HTMLFormElement;\r\n            if (form.checkValidity() === false) {\r\n                event.stopPropagation();\r\n                return;\r\n            }\r\n        }\r\n        const isValidLink = link.find((x) => x.title == \"\" || x.link == \"\");\r\n        if (updateType == \"Link\" && isValidLink) {\r\n            return;\r\n        }\r\n        setValidated(true);\r\n        const data = {\r\n            title: updateType === \"Conversation\" ? conversation.title.trim() : title.trim(),\r\n            type: props.router.query.parent_type,\r\n            description: description,\r\n            show_as_announcement: showAsAnnouncement,\r\n            start_date: startDate,\r\n            end_date: endDate,\r\n            update_type: props.router.query.update_type,\r\n            link: link,\r\n            document: document ? document : [],\r\n            contact_details: contact_details ? contact_details : {},\r\n            images: images ? images : [],\r\n            images_src: images_src ? images_src : [],\r\n            doc_src: doc_src ? doc_src : [],\r\n        };\r\n\r\n        if (props.router.query && props.router.query.routes.length > 1 && props.router.query.routes[1]) {\r\n            getprops(data);\r\n        } else {\r\n            getelseprops(data);\r\n        }\r\n    };\r\n\r\n    const fetchUpdateType = async () => {\r\n        const data = await apiService.get(`/updateType/${props.router.query.update_type}`);\r\n        if (data) {\r\n            setUpdateType(data.title);\r\n        }\r\n    };\r\n\r\n    const getSwitch = (data: any, respData: any) => {\r\n        switch (respData.type) {\r\n            case \"operation\":\r\n                data[\"parentId\"] = respData.parent_operation;\r\n                break;\r\n            case \"event\":\r\n                data[\"parentId\"] = respData.parent_event;\r\n                break;\r\n            case \"project\":\r\n                data[\"parentId\"] = respData.parent_project;\r\n                break;\r\n            case \"vspace\":\r\n                data[\"parentId\"] = respData.parent_vspace;\r\n                break;\r\n            case \"country\":\r\n                data[\"parentId\"] = respData.parent_country;\r\n                break;\r\n            case \"hazard\":\r\n                data[\"parentId\"] = respData.parent_hazard;\r\n                break;\r\n            case \"institution\":\r\n                data[\"parentId\"] = respData.parent_institution;\r\n                break;\r\n            default:\r\n                break;\r\n        }\r\n    };\r\n\r\n    const getdocument = (respData: any, data: any) => {\r\n        setDocumentDropCollection(respData.document ? respData.document : []);\r\n        setDocSrcCollection(respData.doc_src ? respData.doc_src : []);\r\n        setDropZoneCollection(respData.images ? respData.images : []);\r\n        setSrcCollection(respData.images_src ? respData.images_src : []);\r\n        setLink(respData.link.length ? respData.link : linkState);\r\n        setContact_details(respData.contact_details ? respData.contact_details : contact_details);\r\n        getSwitch(data, respData);\r\n    };\r\n    useEffect(() => {\r\n        const fetchData = async () => {\r\n            const respData = await apiService.get(`/updates/${props.router.query.routes[1]}`);\r\n            const data: any = {\r\n                title: respData.title,\r\n                description: respData.description,\r\n                startDate: respData.start_date ? moment(respData.start_date).toDate() : null,\r\n                endDate: respData.end_date ? moment(respData.end_date).toDate() : null,\r\n                classification: null,\r\n                parentType: respData.type,\r\n                showAsAnnouncement: respData.show_as_announcement,\r\n                document: respData.document ? respData.document : [],\r\n                doc_src: respData.doc_src ? respData.doc_src : [],\r\n                images: respData.images ? respData.images : [],\r\n                images_src: respData.images_src ? respData.images_src : [],\r\n            };\r\n            getdocument(respData, data);\r\n            setFormState(data);\r\n        };\r\n        fetchUpdateType();\r\n        if (isEditForm) {\r\n            fetchData();\r\n        }\r\n    }, []);\r\n\r\n    const uploadHandler = (id: any[]) => {\r\n        const ids = id.map((item: any) => item.serverID);\r\n        setFormState((prevState: any) => ({ ...prevState, document: ids }));\r\n    };\r\n\r\n    /**Seperately Hanlde images**/\r\n    const uploadImgHandler = (id: any[]) => {\r\n        const ids = id.map((item: any) => item.serverID);\r\n        setFormState((prevState: any) => ({ ...prevState, images: ids }));\r\n    };\r\n\r\n    const getSource = (imgSrcArr: any[]) => {\r\n        setFormState((prevState: any) => ({ ...prevState, images_src: imgSrcArr }));\r\n    };\r\n\r\n    const getDocSource = (docSrcArr: any[]) => {\r\n        setFormState((prevState: any) => ({ ...prevState, doc_src: docSrcArr }));\r\n    };\r\n\r\n    const getComponent = () => {\r\n        switch (updateType) {\r\n            case \"Conversation\":\r\n                return <ConversationForm onHandleChange={conversationHandleChange} {...conversation} />;\r\n\r\n            case \"Link\":\r\n                return (\r\n                    <LinkForm\r\n                        link={link}\r\n                        handleChangeforTimeline={handleChangeforTimeline}\r\n                        removeForm={removeForm}\r\n                        addform={addform}\r\n                    />\r\n                );\r\n\r\n            case \"Document\":\r\n                return (\r\n                    <DocumentForm\r\n                        srcText={docSrcCollection}\r\n                        getSourceCollection={(docSrcArr) => getDocSource(docSrcArr)}\r\n                        data={documentDropZone}\r\n                        getId={(id) => uploadHandler(id)}\r\n                    />\r\n                );\r\n\r\n            case \"Contact\":\r\n                return <ContactForm onHandleChange={ContactHandleChange} {...contact_details} />;\r\n\r\n            case \"Calendar Event\":\r\n                return (\r\n                    <CalendarEventForm\r\n                        imgSrc={docSrcCollection}\r\n                        getSourceCollection={(docSrcArr) => getDocSource(docSrcArr)}\r\n                        validation={validation}\r\n                        onChangeDate={onChangeDate}\r\n                        {...formState}\r\n                        data={documentDropZone}\r\n                        getId={(id) => uploadHandler(id)}\r\n                    />\r\n                );\r\n\r\n            case \"Image\":\r\n                return (\r\n                    <ImageForm\r\n                        data={dropZoneCollection}\r\n                        imgSrc={srcCollection}\r\n                        getId={(id) => uploadImgHandler(id)}\r\n                        getSourceCollection={(imgSrcArr) => getSource(imgSrcArr)}\r\n                    />\r\n                );\r\n\r\n            case \"General / Notice\":\r\n                return (\r\n                    <>\r\n                        <DocumentForm\r\n                            srcText={docSrcCollection}\r\n                            getSourceCollection={(docSrcArr) => getDocSource(docSrcArr)}\r\n                            data={documentDropZone}\r\n                            getId={(id) => uploadHandler(id)}\r\n                        />\r\n                        <ImageForm\r\n                            imgSrc={srcCollection}\r\n                            getSourceCollection={(imgSrcArr) => getSource(imgSrcArr)}\r\n                            data={dropZoneCollection}\r\n                            getId={(id) => uploadImgHandler(id)}\r\n                        />\r\n                    </>\r\n                );\r\n\r\n            default:\r\n                return null;\r\n        }\r\n    };\r\n\r\n    return (\r\n        <Container className=\"formCard\" fluid>\r\n            <Card>\r\n                <ValidationFormWrapper onSubmit={onSubmitForm} ref={formRef} initialValues={formState} enableReinitialize={true} onErrorSubmit={handleErrorSubmit}>\r\n                    <Card.Body>\r\n                        <Row>\r\n                            <Col>\r\n                                <Card.Title>\r\n                                    {`${isEditForm ? t(\"update.Edit\") : t(\"update.Create\")}`} {t(\"update.Update\")}{\" \"}\r\n                                </Card.Title>\r\n                            </Col>\r\n                        </Row>\r\n                        <hr />\r\n                        <Row>\r\n                            <Col>\r\n                                <Form.Group>\r\n                                    <Form.Label className=\"required-field\">{t(\"update.Title\")}</Form.Label>\r\n                                    <TextInput\r\n                                        required\r\n                                        validator={(value: string) => String(value || '').trim() !== \"\"}\r\n                                        errorMessage={{\r\n                                            validator: t(\"update.provide\"),\r\n                                        }}\r\n                                        type=\"text\"\r\n                                        name=\"title\"\r\n                                        value={formState.title}\r\n                                        onChange={onHandleChange}\r\n                                    />\r\n                                </Form.Group>\r\n                            </Col>\r\n                        </Row>\r\n                        <Row>\r\n                            <Col>\r\n                                <Form.Group>\r\n                                    <Form.Label>{t(\"update.Description\")}</Form.Label>\r\n                                    <EditorComponent initContent={formState.description} onChange={(evt: string) => onChangeDescription(evt)} />\r\n                                </Form.Group>\r\n                            </Col>\r\n                        </Row>\r\n                        {getComponent()}\r\n                        <Row className=\"mt-3\">\r\n                            <Col>\r\n                                <Form.Group controlId=\"showAsAnnouncement\">\r\n                                    <Form.Check\r\n                                        className=\"check\"\r\n                                        id=\"showAsAnnouncement\"\r\n                                        name=\"showAsAnnouncement\"\r\n                                        type=\"checkbox\"\r\n                                        label={t(\"update.Showasanannouncement\")}\r\n                                        checked={formState.showAsAnnouncement}\r\n                                        onChange={onCheckAnnouncement}\r\n                                    />\r\n                                </Form.Group>\r\n                            </Col>\r\n                        </Row>\r\n\r\n                        <Row className=\"my-4\">\r\n                            <Col>\r\n                                <Button\r\n                                    className=\"me-2\"\r\n                                    type=\"submit\"\r\n                                    variant=\"primary\"\r\n                                    onClick={() => {\r\n                                        setValidation((prevState: any) => ({\r\n                                            ...prevState,\r\n                                            startDate: true,\r\n                                        }));\r\n                                    }}\r\n                                >\r\n                                    {t(\"submit\")}\r\n                                </Button>\r\n                                <Button className=\"me-2\" onClick={onResetHandler} variant=\"info\">\r\n                                    {t(\"reset\")}\r\n                                </Button>\r\n                                <Button onClick={getItem} variant=\"secondary\">\r\n                                    {t(\"Cancel\")}\r\n                                </Button>\r\n                            </Col>\r\n                        </Row>\r\n                    </Card.Body>\r\n                </ValidationFormWrapper>\r\n            </Card>\r\n        </Container>\r\n    );\r\n};\r\n\r\nexport async function getStaticProps({ locale }: { locale: string }) {\r\n    return {\r\n        props: {\r\n            ...(await serverSideTranslations(locale, ['common'])),\r\n        },\r\n    }\r\n}\r\n\r\nexport default UpdateForm;\r\n", "import React, { forwardRef } from 'react';\r\nimport { Formik, Form, FormikProps, FormikHelpers } from 'formik';\r\nimport * as Yup from 'yup';\r\n\r\n// This is a wrapper component that replaces react-bootstrap4-form-validation with Formik\r\ninterface MockEvent {\r\n  preventDefault: () => void;\r\n  stopPropagation: () => void;\r\n  currentTarget: (EventTarget & Element) | null;\r\n  target: EventTarget | null;\r\n  nativeEvent: Event;\r\n  bubbles: boolean;\r\n  cancelable: boolean;\r\n  defaultPrevented: boolean;\r\n  eventPhase: number;\r\n  isTrusted: boolean;\r\n  timeStamp: number;\r\n  type: string;\r\n  isDefaultPrevented: () => boolean;\r\n  isPropagationStopped: () => boolean;\r\n  persist: () => void;\r\n}\r\n\r\ninterface ValidationFormWrapperProps {\r\n  children: React.ReactNode | ((formikProps: FormikProps<any>) => React.ReactNode);\r\n  onSubmit: (event: React.FormEvent | MockEvent, values?: Record<string, any>, actions?: FormikHelpers<Record<string, any>>) => void;\r\n  onErrorSubmit?: (errors: any) => void;\r\n  initialValues?: Record<string, any>;\r\n  enableReinitialize?: boolean;\r\n  autoComplete?: string;\r\n  className?: string;\r\n  onKeyPress?: (e: any) => void;\r\n}\r\n\r\nconst ValidationFormWrapper = forwardRef<HTMLFormElement, ValidationFormWrapperProps>((props, ref) => {\r\n  const { children, onSubmit, autoComplete, className, onKeyPress, initialValues, ...rest } = props;\r\n\r\n  // Create an empty validation schema by default\r\n  const validationSchema = Yup.object().shape({});\r\n\r\n  return (\r\n    <Formik\r\n      initialValues={initialValues || {}}\r\n      validationSchema={validationSchema}\r\n      onSubmit={(values: Record<string, any>, actions: FormikHelpers<Record<string, any>>) => {\r\n        // Create a mock event object with preventDefault method and currentTarget\r\n        const mockEvent: MockEvent = {\r\n          preventDefault: () => {},\r\n          stopPropagation: () => {},\r\n          currentTarget: null, // Set to null to avoid checkValidity errors\r\n          target: null,\r\n          nativeEvent: new Event('submit'),\r\n          bubbles: false,\r\n          cancelable: true,\r\n          defaultPrevented: false,\r\n          eventPhase: 0,\r\n          isTrusted: false,\r\n          timeStamp: Date.now(),\r\n          type: 'submit',\r\n          isDefaultPrevented: () => false,\r\n          isPropagationStopped: () => false,\r\n          persist: () => {}\r\n        };\r\n\r\n        if (onSubmit) {\r\n          // Pass the mock event object to maintain compatibility with the original code\r\n          onSubmit(mockEvent, values, actions);\r\n        }\r\n      }}\r\n      {...rest}\r\n    >\r\n      {(formikProps: FormikProps<any>) => (\r\n        <Form\r\n          ref={ref}\r\n          onSubmit={formikProps.handleSubmit}\r\n          autoComplete={autoComplete}\r\n          className={className}\r\n          onKeyPress={onKeyPress}\r\n        >\r\n          {typeof children === 'function' ? children(formikProps) : children}\r\n        </Form>\r\n      )}\r\n    </Formik>\r\n  );\r\n});\r\n\r\nValidationFormWrapper.displayName = 'ValidationFormWrapper';\r\n\r\nexport default ValidationFormWrapper;\r\n", "import React from 'react';\r\nimport { Form } from 'react-bootstrap';\r\nimport { Field, ErrorMessage, useField } from 'formik';\r\n\r\n// This component mimics the TextInput component from react-bootstrap4-form-validation\r\n// but uses Formik under the hood\r\nexport const TextInput = ({\r\n  name,\r\n  id,\r\n  required,\r\n  validator,\r\n  errorMessage,\r\n  onChange,\r\n  value,\r\n  as,\r\n  multiline,\r\n  rows,\r\n  pattern,\r\n  ...props\r\n}: any) => {\r\n  // Create a custom validation function that mimics the original validator\r\n  const validate = (val: any) => {\r\n    // Safely convert value to string and trim\r\n    const stringVal = typeof val === 'string' ? val : String(val || '');\r\n    if (required && (!val || stringVal.trim() === '')) {\r\n      return errorMessage?.validator || 'This field is required';\r\n    }\r\n\r\n    if (validator && !validator(val)) {\r\n      return errorMessage?.validator || 'Invalid value';\r\n    }\r\n\r\n    if (pattern && val) {\r\n      const regex = new RegExp(pattern);\r\n      if (!regex.test(val)) {\r\n        return errorMessage?.pattern || 'Invalid format';\r\n      }\r\n    }\r\n\r\n    return undefined;\r\n  };\r\n\r\n  return (\r\n    <Field name={name} validate={validate}>\r\n      {({ field, meta }: any) => (\r\n        <>\r\n          <Form.Control\r\n            {...field}\r\n            {...props}\r\n            id={id}\r\n            as={as || 'input'}\r\n            rows={rows}\r\n            isInvalid={meta.touched && !!meta.error}\r\n            onChange={(e: React.ChangeEvent<HTMLInputElement>) => {\r\n              field.onChange(e);\r\n              if (onChange) onChange(e);\r\n            }}\r\n            value={value !== undefined ? value : field.value}\r\n          />\r\n          {meta.touched && meta.error ? (\r\n            <Form.Control.Feedback type=\"invalid\">\r\n              {meta.error}\r\n            </Form.Control.Feedback>\r\n          ) : null}\r\n        </>\r\n      )}\r\n    </Field>\r\n  );\r\n};\r\n\r\n// This component mimics the SelectGroup component from react-bootstrap4-form-validation\r\n// but uses Formik under the hood\r\nexport const SelectGroup = ({\r\n  name,\r\n  id,\r\n  required,\r\n  errorMessage,\r\n  onChange,\r\n  value,\r\n  children,\r\n  ...props\r\n}: any) => {\r\n  // Create a custom validation function that mimics the original validator\r\n  const validate = (val: any) => {\r\n    if (required && (!val || val === '')) {\r\n      return errorMessage?.validator || 'This field is required';\r\n    }\r\n\r\n    return undefined;\r\n  };\r\n\r\n  return (\r\n    <Field name={name} validate={validate}>\r\n      {({ field, meta }: any) => (\r\n        <>\r\n          <Form.Control\r\n            as=\"select\"\r\n            {...field}\r\n            {...props}\r\n            id={id}\r\n            isInvalid={meta.touched && !!meta.error}\r\n            onChange={(e: React.ChangeEvent<HTMLSelectElement>) => {\r\n              field.onChange(e);\r\n              if (onChange) onChange(e);\r\n            }}\r\n            value={value !== undefined ? value : field.value}\r\n          >\r\n            {children}\r\n          </Form.Control>\r\n          {meta.touched && meta.error ? (\r\n            <Form.Control.Feedback type=\"invalid\">\r\n              {meta.error}\r\n            </Form.Control.Feedback>\r\n          ) : null}\r\n        </>\r\n      )}\r\n    </Field>\r\n  );\r\n};\r\n\r\n// Export both components as named exports and as a default object\r\nexport default {\r\n  TextInput,\r\n  SelectGroup\r\n};\r\n", "//Import Library\r\nimport React from \"react\";\r\nimport { Container, Row, Col } from \"react-bootstrap\";\r\n\r\n//Import services/components\r\nimport ReactDropZone from \"../../components/common/ReactDropZone\";\r\nimport { useTranslation } from 'next-i18next';\r\n\r\n\r\n//TOTO refactor\r\ninterface ImageFormProps {\r\n  data: any[];\r\n  getId: (id: any[]) => void;\r\n  imgSrc: any[];\r\n  getSourceCollection: (imgSrcArr: any[]) => void;\r\n}\r\n\r\nconst ImageForm = (props: ImageFormProps): React.ReactElement => {\r\n  const { t } = useTranslation('common');\r\n\r\n  const getID = (id: any[]) => {\r\n    props.getId(id)\r\n  }\r\n\r\n  const getSourceText = (imgSrcArr: any[]) => {\r\n    props.getSourceCollection(imgSrcArr);\r\n  }\r\n\r\n  return (\r\n    <Container className=\"formCard\" fluid>\r\n      <Col>\r\n        <Row className='header-block' lg={12}>\r\n          <h6><span>{t(\"update.Image\")}</span></h6>\r\n        </Row>\r\n        <Row>\r\n          <ReactDropZone datas={props.data} srcText={props.imgSrc} getImgID={(id: any[]) => getID(id)} getImageSource={(imgSrcArr: any[]) => getSourceText(imgSrcArr)} />\r\n        </Row>\r\n      </Col>\r\n    </Container>\r\n  );\r\n}\r\nexport default ImageForm;\r\n", "//Import Library\r\nimport React from \"react\";\r\nimport { <PERSON><PERSON>, Card, Form, Container, Row, Col,} from \"react-bootstrap\";\r\nimport { ValidationForm } from \"../../components/common/FormValidation\";\r\n\r\n//Import services/components\r\nimport { useTranslation } from 'next-i18next';\r\nimport ValidationFormWrapper from \"../../components/common/ValidationFormWrapper\";\r\n\r\n\r\n//TOTO refactor\r\ninterface LinkFormProps {\r\n  link: Array<{ title: string; link: string }>;\r\n  handleChangeforTimeline: (e: React.ChangeEvent<any>, i: number) => void;\r\n  removeForm: (e: React.MouseEvent, i: number) => void;\r\n  addform: () => void;\r\n}\r\n\r\nconst LinkForm = (props: LinkFormProps): React.ReactElement => {\r\n  const { t } = useTranslation('common');\r\n  const { link, handleChangeforTimeline, removeForm, addform } = props;\r\n  return (\r\n    <Container className=\"formCard\" fluid>\r\n      <Card>\r\n        <ValidationFormWrapper onSubmit={() => {}}>\r\n          <Card.Body>\r\n            {link && link.map((item: any, i: number) => {\r\n              return (\r\n                <div>\r\n                  <Row>\r\n                    <Col>\r\n                      <Form.Group>\r\n                        <Form.Label className=\"required-field\">{t(\"update.Title\")}</Form.Label>\r\n                        <Form.Control\r\n                        name=\"title\"\r\n                        id=\"timetitle\"\r\n                        type=\"text\"\r\n                        value={item.title}\r\n                        required\r\n                        onChange={(e) => handleChangeforTimeline(e, i)} />\r\n                        <Form.Control.Feedback type=\"invalid\">\r\n                        {t(\"update.TitleisRequired\")}\r\n                  </Form.Control.Feedback>\r\n                      </Form.Group>\r\n                    </Col>\r\n                    <Col>\r\n                      <Form.Group>\r\n                        <Form.Label className=\"required-field\">{t(\"update.Link\")}</Form.Label>\r\n                        <Form.Control\r\n                          name=\"link\"\r\n                          id=\"link\"\r\n                          type=\"text\"\r\n                          required\r\n                          value={item.link}\r\n                          onChange={(e) => handleChangeforTimeline(e, i)}\r\n                          pattern=\"http(s)?://??[\\w.-]+[-a-zA-Z0-9@:%._\\+~#=]{2,256}\\.[a-z]{2,6}.+\"\r\n                        />\r\n                        <Form.Control.Feedback type=\"invalid\">\r\n                        {t(\"update.Providevalidlink\")}\r\n                  </Form.Control.Feedback>\r\n                      </Form.Group>\r\n                    </Col>\r\n                    <Col>\r\n                      <Form.Group>\r\n                        {i === 0 ? (\r\n                          <div></div>\r\n                        ) : (\r\n                            <Button variant=\"secondary\" style={{ marginTop: \"30px\" }} onClick={(e) => removeForm(e, i)} >{t(\"update.Remove\")}\r\n                            </Button>\r\n                          )}\r\n                      </Form.Group>\r\n                    </Col>\r\n                  </Row>\r\n                </div>\r\n              );\r\n            })}\r\n            <Row>\r\n              <Col md lg=\"4\">\r\n                <Button variant=\"secondary\" style={{ marginTop: \"27px\", marginBottom: \"20px\" }} onClick={addform}>{t(\"update.ADD\")}</Button>\r\n              </Col>\r\n            </Row>\r\n          </Card.Body>\r\n        </ValidationFormWrapper>\r\n      </Card>\r\n    </Container>\r\n  );\r\n}\r\n\r\nexport default LinkForm;\r\n", "//Import Library\r\nimport React from \"react\";\r\nimport { Form, Container, Row, Col } from \"react-bootstrap\";\r\n\r\n//Import services/components\r\nimport { useTranslation } from 'next-i18next';\r\n\r\n//TOTO refactor\r\ninterface ContactFormProps {\r\n  onHandleChange: (e: React.ChangeEvent<HTMLInputElement>) => void;\r\n  telephoneNo: string;\r\n  mobileNo: string;\r\n}\r\n\r\nconst ContactForm = (props: ContactFormProps): React.ReactElement => {\r\n    const { t } = useTranslation('common');\r\n    const { onHandleChange, telephoneNo, mobileNo } = props;\r\n\r\n    return (\r\n        <Container className=\"formCard\" fluid>\r\n            <Row>\r\n                <Col>\r\n                    <Form.Group>\r\n                        <Form.Label className=\"required-field\">{t(\"Updates.TelephoneNo\")}</Form.Label>\r\n                        <Form.Control\r\n                            type=\"number\"\r\n                            name=\"telephoneNo\"\r\n                            placeholder={t(\"Updates.TelephoneNumber\")}\r\n                            required\r\n                            value={telephoneNo}\r\n                            onChange={onHandleChange}\r\n                        />\r\n                        <Form.Control.Feedback type=\"invalid\">\r\n                            {t(\"Updates.PleaseTelephoneNumber\")}\r\n                        </Form.Control.Feedback>\r\n                    </Form.Group>\r\n                    <Form.Group>\r\n                        <Form.Label className=\"required-field\">{t(\"Updates.MobileNo\")}</Form.Label>\r\n                        <Form.Control\r\n                            type=\"number\"\r\n                            name=\"mobileNo\"\r\n                            placeholder={t(\"Updates.MobileNumber\")}\r\n                            required\r\n                            value={mobileNo}\r\n                            onChange={onHandleChange}\r\n                        />\r\n                        <Form.Control.Feedback type=\"invalid\">{t(\"Updates.PleaseProvideMobile\")}</Form.Control.Feedback>\r\n                    </Form.Group>\r\n                </Col>\r\n            </Row>\r\n        </Container>\r\n    );\r\n};\r\n\r\nexport default ContactForm;\r\n", "\"use client\";\n\nimport * as React from 'react';\nconst context = /*#__PURE__*/React.createContext(null);\ncontext.displayName = 'CardHeaderContext';\nexport default context;"], "names": ["props", "setEndDate", "useState", "CalendarEventForm", "validation", "onChangeDate", "startDate", "endDate", "t", "useTranslation", "useEffect", "uploadHandler", "getId", "id", "getSourceText", "getSourceCollection", "imgSrcArr", "Container", "className", "fluid", "Col", "lg", "h6", "span", "Row", "Form", "Group", "Label", "RKIDatePicker", "selected", "minDate", "Date", "showTimeSelect", "timeIntervals", "onChange", "date", "placeholderText", "dateFormat", "p", "style", "color", "DocumentForm", "data", "srcText", "imgSrc", "value", "SimpleRichTextEditor", "placeholder", "height", "disabled", "editor<PERSON><PERSON>", "useRef", "isFocused", "setIsFocused", "current", "innerHTML", "handleInput", "execCommand", "command", "document", "focus", "div", "border", "padding", "borderBottom", "background", "button", "type", "onClick", "margin", "strong", "em", "u", "url", "prompt", "ref", "contentEditable", "onInput", "onFocus", "onBlur", "minHeight", "maxHeight", "overflow", "outline", "data-placeholder", "suppressContentEditableWarning", "EditorComponent", "initContent", "content", "DatePicker", "CardBody", "React", "bsPrefix", "as", "Component", "useBootstrapPrefix", "_jsx", "classNames", "displayName", "<PERSON><PERSON><PERSON>er", "<PERSON><PERSON><PERSON><PERSON>", "prefix", "contextValue", "useMemo", "cardHeaderBsPrefix", "CardHeaderContext", "Provider", "children", "CardImg", "variant", "CardImgOverlay", "CardLink", "DivStyledAsH6", "divWithClassName", "CardSubtitle", "CardText", "DivStyledAsH5", "CardTitle", "Card", "bg", "text", "body", "Object", "assign", "Img", "Title", "Subtitle", "Body", "Link", "Text", "Header", "Footer", "ImgOverlay", "Radio", "RadioGroup", "name", "valueSelected", "errorMessage", "errors", "touched", "useFormikContext", "<PERSON><PERSON><PERSON><PERSON>", "childrenWithProps", "map", "child", "isObject", "String", "RadioItem", "label", "values", "setFieldValue", "fieldName", "Check", "checked", "e", "target", "inline", "ValidationForm", "TextInput", "SelectGroup", "ConversationForm", "title", "onHandleChange", "required", "validator", "trim", "initialFormValue", "description", "classification", "parentType", "parentId", "showAsAnnouncement", "images", "images_src", "doc_src", "resetState", "contactState", "telephoneNo", "mobileNo", "linkState", "link", "validationIntialState", "formRef", "UpdateForm", "query", "useRouter", "formState", "setFormState", "conversation", "setConversation", "setLink", "contact_details", "setContact_details", "setValidation", "setValidated", "updateType", "setUpdateType", "documentDropZone", "setDocumentDropCollection", "docSrcCollection", "setDocSrcCollection", "dropZoneCollection", "setDropZoneCollection", "srcCollection", "setSrcCollection", "isEditForm", "router", "routes", "length", "conversationHandleChange", "prevState", "ContactHandleChange", "handleChangeforTimeline", "i", "_tempCosts", "removeForm", "_e", "splice", "addform", "a", "oldArray", "onChangeDescription", "key", "checkCustomValidation", "prev", "getresponse", "event", "preventDefault", "isTitle", "_", "isLink", "getprops", "respData", "apiService", "patch", "_id", "Router", "getelseprops", "parent_type", "parent_id", "post", "onSubmitForm", "currentTarget", "form", "checkValidity", "stopPropagation", "isValidLink", "find", "x", "show_as_announcement", "start_date", "end_date", "update_type", "fetchUpdateType", "get", "getSwitch", "parent_operation", "parent_event", "parent_project", "parent_vspace", "parent_country", "parent_hazard", "parent_institution", "getdocument", "fetchData", "moment", "toDate", "ids", "item", "serverID", "uploadImgHandler", "getSource", "getDocSource", "docSrcArr", "ValidationFormWrapper", "onSubmit", "initialValues", "enableReinitialize", "onErrorSubmit", "handleErrorSubmit", "window", "scrollTo", "hr", "evt", "getComponent", "LinkForm", "ContactForm", "ImageForm", "controlId", "onCheckAnnouncement", "<PERSON><PERSON>", "onResetHandler", "getItem", "forwardRef", "autoComplete", "onKeyPress", "rest", "validationSchema", "<PERSON><PERSON>", "shape", "<PERSON><PERSON>", "actions", "mockEvent", "nativeEvent", "Event", "bubbles", "cancelable", "defaultPrevented", "eventPhase", "isTrusted", "timeStamp", "now", "isDefaultPrevented", "isPropagationStopped", "persist", "formikProps", "handleSubmit", "multiline", "rows", "pattern", "Field", "validate", "stringVal", "val", "RegExp", "test", "field", "meta", "Control", "isInvalid", "error", "undefined", "<PERSON><PERSON><PERSON>", "getID", "ReactDropZone", "datas", "getImgID", "getImageSource", "marginTop", "md", "marginBottom", "context"], "sourceRoot": "", "ignoreList": [4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 23]}