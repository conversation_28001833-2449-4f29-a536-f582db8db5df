(()=>{var e={};e.id=5561,e.ids=[636,3220,5561],e.modules={123:e=>{"use strict";e.exports=require("dom-helpers/removeEventListener")},1332:e=>{"use strict";e.exports=require("react-custom-scrollbars-2")},1428:e=>{"use strict";e.exports=import("axios")},1680:e=>{"use strict";e.exports=require("@restart/ui/utils")},1919:e=>{"use strict";e.exports=require("react-transition-group/Transition")},3892:e=>{"use strict";e.exports=require("classnames")},4048:e=>{"use strict";e.exports=require("react-truncate")},6009:e=>{"use strict";e.exports=require("dom-helpers/ownerDocument")},6952:e=>{"use strict";e.exports=require("@restart/ui/Modal")},7374:e=>{"use strict";e.exports=require("@restart/ui/Dropdown")},8732:e=>{"use strict";e.exports=require("react/jsx-runtime")},9653:e=>{"use strict";e.exports=require("@restart/ui/Overlay")},11242:e=>{"use strict";e.exports=require("redux-persist/integration/react")},11688:e=>{"use strict";e.exports=require("@restart/hooks/useWillUnmount")},12388:e=>{"use strict";e.exports=require("dom-helpers/addEventListener")},13364:e=>{"use strict";e.exports=require("redux-saga/effects")},13408:e=>{"use strict";e.exports=require("@restart/hooks/useUpdateEffect")},14062:e=>{"use strict";e.exports=import("react-redux")},14078:e=>{"use strict";e.exports=import("swr")},14332:e=>{"use strict";e.exports=require("uncontrollable")},16116:e=>{"use strict";e.exports=require("invariant")},18622:e=>{"use strict";e.exports=require("yup")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},22326:e=>{"use strict";e.exports=require("react-dom")},22541:e=>{"use strict";e.exports=require("redux-persist/lib/storage")},25303:e=>{"use strict";e.exports=require("@restart/ui/NavItem")},26324:e=>{"use strict";e.exports=require("warning")},27825:e=>{"use strict";e.exports=require("lodash")},27910:e=>{"use strict";e.exports=require("stream")},28217:e=>{"use strict";e.exports=require("@restart/ui/DropdownItem")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},29628:e=>{"use strict";e.exports=require("dom-helpers/addClass")},29825:e=>{"use strict";e.exports=require("prop-types")},29841:e=>{"use strict";e.exports=require("dom-helpers/hasClass")},30362:e=>{"use strict";e.exports=require("@restart/ui/DropdownToggle")},33873:e=>{"use strict";e.exports=require("path")},36653:e=>{"use strict";e.exports=require("nprogress")},36955:e=>{"use strict";e.exports=require("@restart/hooks/useBreakpoint")},37766:e=>{"use strict";e.exports=require("@restart/hooks/useMergedRefs")},38609:e=>{"use strict";e.exports=require("react-data-table-component")},39756:e=>{"use strict";e.exports=import("redux")},40051:e=>{"use strict";e.exports=require("dom-helpers/removeClass")},40361:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/pages.runtime.prod.js")},42051:e=>{"use strict";e.exports=require("@restart/hooks/useCommittedRef")},42447:(e,r,t)=>{"use strict";t.a(e,async(e,s)=>{try{t.d(r,{A:()=>x});var i=t(8732),o=t(82015),a=t(81149),u=t(82053),c=t(54131),p=t(91353),n=t(88751);t(72025);var l=e([c]);c=(l.then?(await l)():l)[0];let x=e=>{let{t:r}=(0,n.useTranslation)("common"),[t,s]=(0,o.useState)([]),l=e=>{let t=/(http|https):\/\/(\w+:{0,1}\w*)?(\S+)(:[0-9]+)?(\/|\/([\w#!:.?+=&%!\-\/]))?/.test(e.description);return(0,i.jsxs)("div",{className:"carousel-legend",children:[(0,i.jsxs)("p",{className:"lead",children:[(0,i.jsx)("b",{children:r("Filename")})," ",e.originalName||"No Name found"]}),e.description&&(0,i.jsxs)("div",{className:"source_link",children:[(0,i.jsx)("p",{children:(0,i.jsx)("b",{children:r("Source")})}),t?(0,i.jsxs)("div",{children:[(0,i.jsx)(u.FontAwesomeIcon,{icon:c.faLink,size:"1x",color:"#999",className:"me-1"}),(0,i.jsx)("a",{className:"source_link",href:e.description,target:"_blank",rel:"noopener noreferrer",children:e.description})]}):(0,i.jsx)("div",{children:(0,i.jsx)("p",{className:"ps-0 py-0",style:{wordBreak:"break-all"},children:e.description})})]}),e.downloadLink&&(0,i.jsxs)(p.A,{className:"btn btn-success mt-2 btn--download",href:e.downloadLink,children:[r("Download"),(0,i.jsx)(u.FontAwesomeIcon,{icon:c.faDownload,size:"1x",className:"ms-1"})]})]})};return(0,o.useEffect)(()=>{let r=[];e&&e.gallery&&Array.isArray(e.gallery)&&e.gallery.map((t,s)=>{let i,o=t&&t.name.split(".").pop();switch(o){case"JPG":case"jpg":case"jpeg":case"png":i=`http://localhost:3001/api/v1/image/show/${t._id}`;break;case"pdf":i="/images/fileIcons/pdfFile.png";break;case"docx":i="/images/fileIcons/wordFile.png";break;case"xls":case"xlsx":i="/images/fileIcons/xlsFile.png";break;default:i="/images/fileIcons/otherFile.png"}let a=("docx"===o||"pdf"===o||"xls"===o||"xlsx"===o)&&`http://localhost:3001/api/v1/files/download/${t._id}`,u=`${t&&t.original_name?t.original_name:"No Name found"}`,c=e.imageSource&&Array.isArray(e.imageSource)&&e.imageSource.length>0?e.imageSource[s]:"";r.push({src:i,description:c,originalName:u,downloadLink:a})}),s(r)},[e]),(0,i.jsx)("div",{children:t&&0===t.length?(0,i.jsx)("div",{className:"border border-info my-3 mx-0",children:(0,i.jsx)("p",{className:"d-flex d-flex justify-content-center p-2 m-0",children:r("NoFilesFound!")})}):(0,i.jsx)(a.Carousel,{showThumbs:!0,showStatus:!0,showIndicators:!0,infiniteLoop:!0,useKeyboardArrows:!0,autoPlay:!1,stopOnHover:!0,swipeable:!0,dynamicHeight:!1,emulateTouch:!0,renderThumbs:()=>t.map((e,r)=>(0,i.jsx)("img",{src:e.src,alt:`Thumbnail ${r+1}`,style:{width:"60px",height:"60px",objectFit:"cover"}},r)),children:t.map((e,r)=>(0,i.jsxs)("div",{children:[(0,i.jsx)("img",{src:e.src,alt:e.originalName||"Gallery image",style:{maxHeight:"500px",objectFit:"contain"}}),l(e)]},r))})})};s()}catch(e){s(e)}})},42893:e=>{"use strict";e.exports=import("react-hot-toast")},43294:e=>{"use strict";e.exports=require("formik")},50009:e=>{"use strict";e.exports=require("@restart/ui/DropdownMenu")},54131:e=>{"use strict";e.exports=import("@fortawesome/free-solid-svg-icons")},57664:e=>{"use strict";e.exports=require("dom-helpers/scrollbarSize")},58928:e=>{"use strict";e.exports=require("dom-helpers/canUseDOM")},59717:e=>{"use strict";e.exports=require("@restart/ui/DropdownContext")},60560:e=>{"use strict";e.exports=require("dom-helpers/contains")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},65447:e=>{"use strict";e.exports=require("@restart/ui/Button")},67364:e=>{"use strict";e.exports=require("@restart/hooks/useCallbackRef")},68455:e=>{"use strict";e.exports=import("redux-saga")},69722:e=>{"use strict";e.exports=require("es6-promise")},72025:()=>{},74075:e=>{"use strict";e.exports=require("zlib")},74716:e=>{"use strict";e.exports=require("moment")},74987:e=>{"use strict";e.exports=require("@restart/ui/ModalManager")},78097:e=>{"use strict";e.exports=require("next-redux-saga")},78634:e=>{"use strict";e.exports=require("@restart/ui/Anchor")},81149:e=>{"use strict";e.exports=require("react-responsive-carousel")},81366:e=>{"use strict";e.exports=require("redux-auth-wrapper/connectedAuthWrapper")},81521:e=>{"use strict";e.exports=require("dom-helpers/querySelectorAll")},81895:e=>{"use strict";e.exports=require("@restart/hooks/useEventCallback")},82015:e=>{"use strict";e.exports=require("react")},82053:e=>{"use strict";e.exports=require("@fortawesome/react-fontawesome")},86842:e=>{"use strict";e.exports=require("@restart/ui/SelectableContext")},86843:e=>{"use strict";e.exports=require("moment/locale/de")},87571:e=>{"use strict";e.exports=require("dom-helpers/transitionEnd")},88751:e=>{"use strict";e.exports=require("next-i18next")},93551:(e,r,t)=>{"use strict";t.a(e,async(e,s)=>{try{t.r(r),t.d(r,{config:()=>q,default:()=>x,getServerSideProps:()=>h,getStaticPaths:()=>m,getStaticProps:()=>d,reportWebVitals:()=>g,routeModule:()=>j,unstable_getServerProps:()=>v,unstable_getServerSideProps:()=>S,unstable_getStaticParams:()=>w,unstable_getStaticPaths:()=>b,unstable_getStaticProps:()=>f});var i=t(63885),o=t(80237),a=t(81413),u=t(9616),c=t.n(u),p=t(72386),n=t(13245),l=e([p,n]);[p,n]=l.then?(await l)():l;let x=(0,a.M)(n,"default"),d=(0,a.M)(n,"getStaticProps"),m=(0,a.M)(n,"getStaticPaths"),h=(0,a.M)(n,"getServerSideProps"),q=(0,a.M)(n,"config"),g=(0,a.M)(n,"reportWebVitals"),f=(0,a.M)(n,"unstable_getStaticProps"),b=(0,a.M)(n,"unstable_getStaticPaths"),w=(0,a.M)(n,"unstable_getStaticParams"),v=(0,a.M)(n,"unstable_getServerProps"),S=(0,a.M)(n,"unstable_getServerSideProps"),j=new i.PagesRouteModule({definition:{kind:o.A.PAGES,page:"/vspace/VirtualSpaceAccordionSection",pathname:"/vspace/VirtualSpaceAccordionSection",bundlePath:"",filename:""},components:{App:p.default,Document:c()},userland:n});s()}catch(e){s(e)}})},93787:e=>{"use strict";e.exports=require("redux-persist")},94947:e=>{"use strict";e.exports=require("@restart/hooks/useTimeout")},96196:e=>{"use strict";e.exports=require("next-redux-wrapper")},98320:e=>{"use strict";e.exports=require("@restart/hooks/useIsomorphicEffect")},99460:e=>{"use strict";e.exports=require("dom-helpers/css")}};var r=require("../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[6089,9216,9616,4033,2386,2491,3245],()=>t(93551));module.exports=s})();