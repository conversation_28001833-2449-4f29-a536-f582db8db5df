"use strict";(()=>{var e={};e.id=3454,e.ids=[636,3220,3454],e.modules={123:e=>{e.exports=require("dom-helpers/removeEventListener")},1332:e=>{e.exports=require("react-custom-scrollbars-2")},1428:e=>{e.exports=import("axios")},1680:e=>{e.exports=require("@restart/ui/utils")},1919:e=>{e.exports=require("react-transition-group/Transition")},3892:e=>{e.exports=require("classnames")},4048:e=>{e.exports=require("react-truncate")},6009:e=>{e.exports=require("dom-helpers/ownerDocument")},6952:e=>{e.exports=require("@restart/ui/Modal")},7374:e=>{e.exports=require("@restart/ui/Dropdown")},8732:e=>{e.exports=require("react/jsx-runtime")},9653:e=>{e.exports=require("@restart/ui/Overlay")},11242:e=>{e.exports=require("redux-persist/integration/react")},11688:e=>{e.exports=require("@restart/hooks/useWillUnmount")},12388:e=>{e.exports=require("dom-helpers/addEventListener")},12876:(e,r,t)=>{t.a(e,async(e,s)=>{try{t.r(r),t.d(r,{default:()=>q});var o=t(8732);t(82015);var n=t(91353),i=t(83551),a=t(49481),u=t(19918),p=t.n(u),c=t(82053),d=t(54131),l=t(63349),x=t(81426),m=t(88751),v=t(75268),h=e([d,x]);[d,x]=h.then?(await h)():h;let q=e=>{let{t:r}=(0,m.useTranslation)("common"),t=()=>(0,o.jsx)(p(),{href:"/event/[...routes]",as:`/event/edit/${e.routeData.routes[1]}`,children:(0,o.jsxs)(n.A,{variant:"secondary",size:"sm",children:[(0,o.jsx)(c.FontAwesomeIcon,{icon:d.faPen}),"\xa0",r("Events.show.Edit")]})}),s=(0,v.canEditEvent)(()=>(0,o.jsx)(t,{}));return(0,o.jsx)(o.Fragment,{children:(0,o.jsxs)(i.A,{children:[(0,o.jsxs)(a.A,{md:11,children:[(0,o.jsx)("div",{className:"d-flex justify-content-between",children:(0,o.jsxs)("h4",{children:[e?.eventData?.country?`${e.eventData.country.title} | ${String(e.eventData.hazard.map(e=>e&&e.title&&e.title.en?e.title.en:"").join(", "))} (${e.eventData.title})`:"","\xa0\xa0",e?.editAccess&&e?.routeData?.routes[1]?(0,o.jsx)(s,{event:e.eventData}):null]})}),(0,o.jsx)("hr",{}),(0,o.jsx)(l.A,{description:e.eventData.description})]}),(0,o.jsx)(a.A,{md:1,children:(0,o.jsx)(x.A,{entityId:e.routeData.routes[1],entityType:"event"})})]})})};s()}catch(e){s(e)}})},13364:e=>{e.exports=require("redux-saga/effects")},14062:e=>{e.exports=import("react-redux")},14078:e=>{e.exports=import("swr")},14332:e=>{e.exports=require("uncontrollable")},16116:e=>{e.exports=require("invariant")},18622:e=>{e.exports=require("yup")},19121:e=>{e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},22326:e=>{e.exports=require("react-dom")},22541:e=>{e.exports=require("redux-persist/lib/storage")},25303:e=>{e.exports=require("@restart/ui/NavItem")},26324:e=>{e.exports=require("warning")},27825:e=>{e.exports=require("lodash")},27910:e=>{e.exports=require("stream")},28217:e=>{e.exports=require("@restart/ui/DropdownItem")},29021:e=>{e.exports=require("fs")},29294:e=>{e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},29628:e=>{e.exports=require("dom-helpers/addClass")},29825:e=>{e.exports=require("prop-types")},29841:e=>{e.exports=require("dom-helpers/hasClass")},30362:e=>{e.exports=require("@restart/ui/DropdownToggle")},33873:e=>{e.exports=require("path")},36653:e=>{e.exports=require("nprogress")},36955:e=>{e.exports=require("@restart/hooks/useBreakpoint")},37766:e=>{e.exports=require("@restart/hooks/useMergedRefs")},39756:e=>{e.exports=import("redux")},40051:e=>{e.exports=require("dom-helpers/removeClass")},40361:e=>{e.exports=require("next/dist/compiled/next-server/pages.runtime.prod.js")},42893:e=>{e.exports=import("react-hot-toast")},43294:e=>{e.exports=require("formik")},48665:(e,r,t)=>{t.a(e,async(e,s)=>{try{t.r(r),t.d(r,{config:()=>h,default:()=>l,getServerSideProps:()=>v,getStaticPaths:()=>m,getStaticProps:()=>x,reportWebVitals:()=>q,routeModule:()=>S,unstable_getServerProps:()=>A,unstable_getServerSideProps:()=>P,unstable_getStaticParams:()=>y,unstable_getStaticPaths:()=>g,unstable_getStaticProps:()=>f});var o=t(63885),n=t(80237),i=t(81413),a=t(9616),u=t.n(a),p=t(72386),c=t(12876),d=e([p,c]);[p,c]=d.then?(await d)():d;let l=(0,i.M)(c,"default"),x=(0,i.M)(c,"getStaticProps"),m=(0,i.M)(c,"getStaticPaths"),v=(0,i.M)(c,"getServerSideProps"),h=(0,i.M)(c,"config"),q=(0,i.M)(c,"reportWebVitals"),f=(0,i.M)(c,"unstable_getStaticProps"),g=(0,i.M)(c,"unstable_getStaticPaths"),y=(0,i.M)(c,"unstable_getStaticParams"),A=(0,i.M)(c,"unstable_getServerProps"),P=(0,i.M)(c,"unstable_getServerSideProps"),S=new o.PagesRouteModule({definition:{kind:n.A.PAGES,page:"/event/components/EventHeaderSection",pathname:"/event/components/EventHeaderSection",bundlePath:"",filename:""},components:{App:p.default,Document:u()},userland:c});s()}catch(e){s(e)}})},50009:e=>{e.exports=require("@restart/ui/DropdownMenu")},54131:e=>{e.exports=import("@fortawesome/free-solid-svg-icons")},57664:e=>{e.exports=require("dom-helpers/scrollbarSize")},58928:e=>{e.exports=require("dom-helpers/canUseDOM")},59717:e=>{e.exports=require("@restart/ui/DropdownContext")},60560:e=>{e.exports=require("dom-helpers/contains")},63033:e=>{e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},63349:(e,r,t)=>{t.d(r,{A:()=>i});var s=t(8732),o=t(82015),n=t(88751);let i=e=>{let{t:r}=(0,n.useTranslation)("common"),t=parseInt("255"),[i,a]=(0,o.useState)(!1);return(0,s.jsxs)(s.Fragment,{children:[e.description?(0,s.jsx)("div",{dangerouslySetInnerHTML:((r,s)=>({__html:!s&&r.length>t?r.substring(0,t)+"...":e.description}))(e.description,i),className:"operationDesc"}):null,e.description&&e.description.length>t?(0,s.jsx)("button",{type:"button",className:"readMoreText",onClick:()=>a(!i),children:r(i?"readLess":"readMore")}):null]})}},65447:e=>{e.exports=require("@restart/ui/Button")},67364:e=>{e.exports=require("@restart/hooks/useCallbackRef")},68455:e=>{e.exports=import("redux-saga")},69722:e=>{e.exports=require("es6-promise")},74075:e=>{e.exports=require("zlib")},74716:e=>{e.exports=require("moment")},74987:e=>{e.exports=require("@restart/ui/ModalManager")},75268:(e,r,t)=>{t.r(r),t.d(r,{canAddEvent:()=>a,canAddEventForm:()=>u,canEditEvent:()=>p,canEditEventForm:()=>c,canViewDiscussionUpdate:()=>d,default:()=>l});var s=t(8732);t(82015);var o=t(81366),n=t.n(o),i=t(61421);let a=n()({authenticatedSelector:e=>!!e.permissions&&!!e.permissions.event&&!!e.permissions.event["create:any"],wrapperDisplayName:"CanAddEvent"}),u=n()({authenticatedSelector:e=>!!e.permissions&&!!e.permissions.event&&!!e.permissions.event["create:any"],wrapperDisplayName:"CanAddEventForm",FailureComponent:()=>(0,s.jsx)(i.default,{})}),p=n()({authenticatedSelector:(e,r)=>{if(e.permissions&&e.permissions.event){if(e.permissions.event["update:any"])return!0;else if(e.permissions.event["update:own"]&&r.event&&r.event.user&&r.event.user._id===e.user._id)return!0}return!1},wrapperDisplayName:"CanEditEvent"}),c=n()({authenticatedSelector:(e,r)=>{if(e.permissions&&e.permissions.event){if(e.permissions.event["update:any"])return!0;else if(e.permissions.event["update:own"]&&r.event&&r.event.user&&r.event.user._id===e.user._id)return!0}return!1},wrapperDisplayName:"CanEditEventForm",FailureComponent:()=>(0,s.jsx)(i.default,{})}),d=n()({authenticatedSelector:e=>!!e.permissions&&!!e.permissions.update&&!!e.permissions.update["read:any"],wrapperDisplayName:"CanViewDiscussionUpdate"}),l=a},78097:e=>{e.exports=require("next-redux-saga")},78634:e=>{e.exports=require("@restart/ui/Anchor")},80237:(e,r)=>{Object.defineProperty(r,"A",{enumerable:!0,get:function(){return t}});var t=function(e){return e.PAGES="PAGES",e.PAGES_API="PAGES_API",e.APP_PAGE="APP_PAGE",e.APP_ROUTE="APP_ROUTE",e.IMAGE="IMAGE",e}({})},81366:e=>{e.exports=require("redux-auth-wrapper/connectedAuthWrapper")},81413:(e,r)=>{Object.defineProperty(r,"M",{enumerable:!0,get:function(){return function e(r,t){return t in r?r[t]:"then"in r&&"function"==typeof r.then?r.then(r=>e(r,t)):"function"==typeof r&&"default"===t?r:void 0}}})},81426:(e,r,t)=>{t.a(e,async(e,s)=>{try{t.d(r,{A:()=>l});var o=t(8732),n=t(14062),i=t(54131),a=t(82015),u=t(82053),p=t(63487),c=e([n,i,p]);[n,i,p]=c.then?(await c)():c;let d={operation:"Operation",institution:"Institution",event:"Event",project:"Project",vspace:"Vspace"},l=(0,n.connect)(e=>e)(e=>{let{user:r,entityId:t,entityType:s}=e,[n,c]=(0,a.useState)(!1),[l,x]=(0,a.useState)(""),m=async()=>{if(!r?._id)return;let e=await p.A.get("/flag",{query:{entity_id:t,user:r._id,onModel:d[s]}});e&&e.data&&e.data.length>0&&(x(e.data[0]),c(!0))},v=async e=>{if(e.preventDefault(),!r?._id)return;let o=!n,i={entity_type:s,entity_id:t,user:r._id,onModel:d[s]};if(o){let e=await p.A.post("/flag",i);e&&e._id&&(x(e),c(o))}else{let e=await p.A.remove(`/flag/${l._id}`);e&&e.n&&c(o)}};return(0,a.useEffect)(()=>{m()},[]),(0,o.jsx)("div",{className:"subscribe-flag",children:(0,o.jsxs)("a",{href:"",onClick:v,children:[(0,o.jsx)("span",{className:"check",children:n?(0,o.jsx)(u.FontAwesomeIcon,{className:"clickable checkIcon",icon:i.faCheckCircle,color:"#00CC00"}):(0,o.jsx)(u.FontAwesomeIcon,{className:"clickable minusIcon",icon:i.faPlusCircle,color:"#fff"})}),(0,o.jsx)(u.FontAwesomeIcon,{className:"bookmark",icon:i.faBookmark,color:"#d4d4d4"})]})})});s()}catch(e){s(e)}})},81521:e=>{e.exports=require("dom-helpers/querySelectorAll")},81895:e=>{e.exports=require("@restart/hooks/useEventCallback")},82015:e=>{e.exports=require("react")},82053:e=>{e.exports=require("@fortawesome/react-fontawesome")},86842:e=>{e.exports=require("@restart/ui/SelectableContext")},87571:e=>{e.exports=require("dom-helpers/transitionEnd")},88751:e=>{e.exports=require("next-i18next")},93787:e=>{e.exports=require("redux-persist")},94947:e=>{e.exports=require("@restart/hooks/useTimeout")},96196:e=>{e.exports=require("next-redux-wrapper")},98320:e=>{e.exports=require("@restart/hooks/useIsomorphicEffect")},99460:e=>{e.exports=require("dom-helpers/css")}};var r=require("../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[6089,9216,9616,2386],()=>t(48665));module.exports=s})();