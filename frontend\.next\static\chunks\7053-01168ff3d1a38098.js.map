{"version": 3, "file": "static/chunks/7053-01168ff3d1a38098.js", "mappings": "wKA4FA,MArEyB,IACvB,GAAM,CAAEA,MAAI,CAAE,CAAGC,CAAAA,EAAAA,EAAAA,EAAAA,CAAcA,CAAC,GAoEnBC,OAnEPC,EAAcH,EAAKI,KAmEIF,EAAC,CAnEG,CAC3B,SAAEG,CAAO,CAAE,CAAGC,EACd,CAACC,EAAQC,EAAU,CAAQC,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAC,CAAC,GACrC,CAACC,EAAcC,EAAgB,CAAQF,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAC,CAAC,GACjD,CAACG,EAAYC,EAAc,CAAQJ,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAC,CAAC,GAO7CK,EAAc,KAClBH,EAAgB,MAChBE,EAAc,KAChB,EAUME,EAAuB,KAC3BP,EAAU,CACRQ,MAAOX,EAAQW,KAAK,CACpBC,GAAIZ,EAAQa,GAAG,CACfC,IACEd,EAAQe,OAAO,EAAIf,EAAQe,OAAO,CAACC,WAAW,CAC1CC,WAAWjB,EAAQe,OAAO,CAACC,WAAW,CAAC,EAAE,CAACE,QAAQ,EAClD,KACNC,IACEnB,EAAQe,OAAO,EAAIf,EAAQe,OAAO,CAACC,WAAW,CAC1CC,WAAWjB,EAAQe,OAAO,CAACC,WAAW,CAAC,EAAE,CAACI,SAAS,EACnD,IACR,EACF,EAKA,MAHAC,CAAAA,EAAAA,EAAAA,SAAAA,CAASA,CAAC,KACRX,GACF,EAAG,CAACV,EAAQ,EAEV,iCACG,IACAE,GAAUA,EAAOU,EAAE,CAClB,UAACU,EAAAA,CAAOA,CAAAA,CACNC,QAASd,EACTV,SAAUD,EACV0B,cAAe,CAAEV,IAAKZ,EAAOY,GAAG,CAAEK,IAAKjB,EAAOiB,GAAG,EACjDd,aAAcA,EACdE,WAAY,UA7CAkB,IAClB,GAAM,MAAEC,CAAI,CAAE,CAAGD,EACjB,MAAO,UAACE,IAAAA,gBAAGD,EAAAA,KAAAA,EAAAA,EAAME,IAAI,EACvB,EA0CqBC,CAAWH,KAAMnB,aAE9B,UAACuB,EAAAA,CAAYA,CAAAA,CACXF,KAAM1B,EAAOS,KAAK,CAClBoB,KAAM,CACJC,IAAK,8BACP,EACAC,QA1CY,CA0CHC,EA1CqCC,EAAaC,KACnE3B,IACAH,EAAgB6B,GAChB3B,EAAc,CACZoB,KAAMS,EAAcT,IAAI,EAE5B,EAqCUU,SAAUpC,MAGZ,OAGV,oIClDA,MA9BuB,IACnB,GAAM,CAAEqC,CAAC,CAAE,CAAG3C,CAAAA,EAAAA,EAAAA,EAAAA,CAAcA,CAAC,QA6BlB4C,EA5BX,MACI,MA2BqBA,EA3BrB,uBACI,UAACC,MAAAA,CAAIC,UAAU,0BACX,WAACC,EAAAA,CAAGA,CAAAA,WACA,WAACC,EAAAA,CAAGA,CAAAA,CAACF,UAAU,sBAAsBG,GAAI,YACrC,UAACJ,MAAAA,CAAIC,UAAU,yBACX,UAACI,EAAAA,CAAeA,CAAAA,CAACf,KAAMgB,EAAAA,GAAOA,CAAEC,MAAM,OAAOC,KAAK,SAEtD,WAACR,MAAAA,CAAIC,UAAU,0BACX,UAACQ,KAAAA,UAAIX,EAAE,cACP,UAACY,KAAAA,UAAIlD,EAAMmD,SAAS,CAACC,QAAQ,CAACC,MAAM,SAG5C,WAACV,EAAAA,CAAGA,CAAAA,CAACF,UAAU,sBAAsBG,GAAI,YACrC,UAACJ,MAAAA,CAAIC,UAAU,yBACX,UAACI,EAAAA,CAAeA,CAAAA,CAACf,KAAMwB,EAAAA,GAAMA,CAAEP,MAAM,OAAOC,KAAK,SAErD,WAACR,MAAAA,CAAIC,UAAU,0BACX,UAACQ,KAAAA,UAAIX,EAAE,uBACP,UAACY,KAAAA,UAAIlD,EAAMmD,SAAS,CAACI,QAAQ,CAACF,MAAM,eAOhE,2EC8BA,MA/CkD,OAAC,MACjD1B,EAAO,QAAQ,IACfhB,CA6CakB,CA7CR,EAAE,SA6CkBA,EA5CzB2B,EAAY,EAAE,MACdC,CAAI,MACJ3B,CAAI,UACJO,CAAQ,SACRL,CAAO,OACPtB,CAAK,WACLgD,GAAY,CAAK,CAClB,UAsBC,GAAyC,UAAxB,OAAOrB,EAASxB,GAAG,EAAyC,UAAxB,OAAOwB,EAASnB,GAAG,CAKtE,UAACyC,EAAAA,EAAMA,CAAAA,CACLtB,SAAUA,EACVP,KAAMA,EACNpB,MAAOA,GAASiB,EAChB+B,UAAWA,EACX1B,QA/BiBG,CA+BRyB,GA9BP5B,GAeFA,EAdoB,IADT,EAeH6B,KAZNlD,QAYmBuB,IAXnBsB,OACAC,WACApB,CACF,EAGe,UACbA,EACAyB,YAAa,IAAMzB,CACrB,EAE6BF,EAEjC,IAIS,IAYX,0GCzDA,IAAM4B,EAAiB,CACrBZ,UAAW,YACXa,YAAa,cACbC,MAAO,QACPC,QAAS,UACTC,OAAQ,QACV,EAoEA,EAAeC,CAAAA,EAAAA,EAAAA,EAAAA,CAAOA,CAAC,GAAgBC,GA1DG,IACxC,GAAM,CAAEC,KAyD6CC,CAzDzC,CAyD0C,SAzDxCC,CAAQ,YAAEC,CAAU,CAAE,CAAGzE,EACjC,CAAC0E,EAAUC,EAAY,CAAGxE,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,EAAU,GAC5C,CAACyE,EAAWC,EAAa,CAAG1E,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAM,IAG1C2E,EAA2B,UAC/B,GAAI,QAACR,EAAAA,KAAAA,EAAAA,EAAM1D,GAAAA,EAAK,CAAX0D,MACL,IAAMS,EAAY,MAAMC,EAAAA,CAAUA,CAACC,GAAG,CAAC,QAAS,CAACC,MAAO,CAACC,UAAWX,EAAUF,KAAMA,EAAK1D,GAAG,CAAEwE,QAASrB,CAAc,CAACU,EAAW,CAAC,GAC9HM,GAAaA,EAAUM,IAAI,EAAIN,EAAUM,IAAI,CAAChC,MAAM,CAAG,GAAG,CAC5DwB,EAAaE,EAAUM,IAAI,CAAC,EAAE,EAC9BV,GAAY,GAEhB,EAEMW,EAAkB,MAAOnD,IAE7B,GADAA,EAAEoD,cAAc,GACZ,QAACjB,EAAAA,KAAAA,EAAAA,EAAM1D,GAAAA,EAAK,CAAX0D,MACL,IAAMkB,EAAQ,CAACd,EACTe,EAAc,CAClBC,YAAajB,EACbU,UAAWX,EACXF,KAAMA,EAAK1D,GAAG,CACdwE,QAASrB,CAAc,CAACU,EAAW,EAErC,GAAIe,EAAM,CACR,IAAMG,EAAc,MAAMX,EAAAA,CAAUA,CAACY,IAAI,CAAC,QAASH,GAC/CE,GAAUA,EAAO/E,GAAG,EAAE,CACxBiE,EAAac,GACbhB,EAAYa,GAEhB,KAAO,CACL,IAAMK,EAAW,MAAMb,EAAAA,CAAUA,CAACc,MAAM,CAAC,SAAuB,OAAdlB,EAAUhE,GAAG,GAC3DiF,GAAYA,EAASE,CAAC,EAAE,EACdP,EAEhB,CACF,EAKA,MAHApE,CAAAA,EAAAA,EAAAA,SAAAA,CAASA,CAAC,KACR0D,GACF,EAAE,EAAE,EAEF,UAACtC,MAAAA,CAAIC,UAAU,0BACb,WAACf,IAAAA,CAAEsE,KAAK,GAAGhE,QAASsD,YAClB,UAACW,OAAAA,CAAKxD,UAAU,iBACbiC,EACC,UAAC7B,EAAAA,CAAeA,CAAAA,CAACJ,UAAU,sBAAsBX,KAAMoE,EAAAA,GAAaA,CAAEnD,MAAM,YAE5E,UAACF,EAAAA,CAAeA,CAAAA,CAACJ,UAAU,sBAAsBX,KAAMqE,EAAAA,GAAYA,CAAEpD,MAAM,WAG/E,UAACF,EAAAA,CAAeA,CAAAA,CAACJ,UAAU,WAAWX,KAAMsE,EAAAA,GAAUA,CAAErD,MAAM,gBAItE,oKC/BA,MArCsB,IAClB,GAAM,CAAET,CAAC,CAAE,CAAG3C,CAAAA,EAAAA,EAAAA,EAAAA,CAAcA,CAAC,QAoClB0G,EAnCLC,EAAoB,IAElB,OAiCiB,CAjCjB,uBACKC,EAAKC,QAAQ,CACV,UAACC,IAAIA,CACDT,KAAK,yBACLU,GAAI,WAFHD,QAE+C,OAAzBF,EAAKI,SAAS,CAACC,MAAM,CAAC,EAAE,WAE/C,WAACC,EAAAA,CAAMA,CAAAA,CAACC,QAAQ,YAAY9D,KAAK,eAC7B,UAACH,EAAAA,CAAeA,CAAAA,CAACf,KAAMiF,EAAAA,GAAKA,GAAI,OACzBzE,EAAE,aAGnB,KAKR0E,EAAmBC,CAAAA,EAAAA,EAAAA,gBAAAA,CAAgBA,CAAC,IAAM,UAACX,EAAAA,CAAAA,IAEjD,MACI,+BACI,WAACY,UAAAA,CAAQzE,UAAU,2CACf,WAACS,KAAAA,CAAGT,UAAU,2BACT8D,EAAKpD,SAAS,CAACzC,KAAK,CAAC,WACrB6F,EAAKI,SAAS,CAACC,MAAM,EAAIL,EAAKI,SAAS,CAACC,MAAM,CAAC,EAAE,CAC9C,UAACI,EAAAA,CAAiB7D,UAAWoD,EAAKpD,SAAS,GAC3C,QAER,UAACgE,EAAAA,CAAQA,CAAAA,CAAC3C,SAAU+B,EAAKI,SAAS,CAACC,MAAM,CAAC,EAAE,CAAEnC,WAAW,kBAIzE,2EChCA,MARyB,OAAC,UAAEpC,CAAQ,OAQrB+E,OARuBC,CAAY,QAQnBD,EAAC,CAR4B,CAAS,GACnE,MACE,UAACE,EAAAA,EAAUA,CAAAA,CAACjF,SAAUA,EAAUgF,aAAcA,WAC5C,UAAC7E,MAAAA,UAAK+E,KAGZ,ECdMC,EAAO,mBACPC,EAAQ,qBACRC,EAAO,eACPC,EAAW,kBAgTjB,EA/SkB,CAChB,CACE,YAAe,IA6SJC,OA5SX,EA4SoBA,EAAC,IA5SV,CACT,CACE,MAAS,SACX,EACD,EAEH,CACE,YAAeJ,EACf,QAAW,CACT,CACE,MAAS,SACX,EACD,EAEH,CACE,YAAeC,EACf,QAAW,CACT,CACE,MAAS,SACX,EACD,EAEH,CACE,YAAe,iBACf,YAAe,WACf,QAAW,CACT,CACE,WAAc,KAChB,EACD,EAEH,CACE,YAAe,yBACf,YAAeE,EACf,QAAW,CACT,CACE,MAAS,SACX,EACD,EAEH,CACE,YAAe,6BACf,YAAe,SACf,QAAW,CACT,CACE,WAAc,KAChB,EACD,EAEH,CACE,YAAe,6BACf,YAAeH,EACf,QAAW,CACT,CACE,MAAS,SACX,EACD,EAEH,CACE,YAAe,0BACf,YAAeG,EACf,QAAW,CACT,CACE,MAAS,SACX,EACD,EAEH,CACE,YAAe,qBACf,YAAeA,EACf,QAAW,CACT,CACE,MAAS,SACX,EAEJ,EACA,CACE,YAAe,oBACf,YAAe,WACf,QAAW,CACT,CACE,MAAS,SACX,EACD,EAEH,CACE,YAAe,MACf,QAAW,CACT,CACE,WAAc,KAChB,EACD,EAEH,CACE,YAAe,MACf,YAAe,WACf,QAAW,CACT,CACE,MAAS,SACX,EACD,EAEH,CACE,YAAe,MACf,YAAe,cACf,QAAW,CACT,CACE,WAAc,KAChB,EACD,EAEH,CACE,YAAe,MACf,YAAeH,EACf,QAAW,CACT,CACE,MAAS,SACX,EAEJ,EACA,CACE,YAAe,MACf,YAAeC,EACf,QAAW,CACT,CACE,MAAS,SACX,EACD,EAEH,CACE,YAAe,WACf,YAAe,gBACf,QAAW,CACT,CACE,MAAS,SACX,EAEJ,EACA,CACE,YAAe,WACf,YAAeD,EACf,QAAW,CACT,CACE,MAAS,SACX,EACD,EAEH,CACE,YAAe,OACf,QAAW,CACT,CACE,WAAc,KAChB,EACD,EAEH,CACE,YAAe,OACf,YAAe,WACf,QAAW,CACT,CACE,MAAS,SACX,EACD,EAEH,CACE,YAAe,OACf,YAAe,cACf,QAAW,CACT,CACE,WAAc,KAChB,EACD,EAEH,CACE,YAAe,OACf,YAAeA,EACf,QAAW,CACT,CACE,MAAS,SACX,EACD,EAEH,CACE,YAAe,OACf,YAAeC,EACf,QAAW,CACT,CACE,MAAS,SACX,EACD,EAEH,CACE,YAAeC,EACf,YAAe,WACf,QAAW,CACT,CACE,MAAS,SACX,EAEJ,EACA,CACE,YAAeA,EACf,YAAeC,EACf,QAAW,CACT,CACE,MAAS,SACX,EACD,EAEH,CACE,YAAeD,EACf,YAAeF,EACf,QAAW,CACT,CACE,MAAS,SACX,EACD,EAEH,CACE,YAAeE,EACf,YAAeD,EACf,QAAW,CACT,CACE,MAAS,SACX,EACD,EAEH,CACE,YAAe,aACf,YAAe,SACf,QAAW,CACT,CACE,WAAc,KAChB,EACD,EAEH,CACE,YAAe,UACf,QAAW,CACT,CACE,WAAc,KAChB,EACD,EAEH,CACE,YAAe,UACf,YAAeD,EACf,QAAW,CACT,CACE,MAAS,SACX,EACD,EAEH,CACE,YAAe,UACf,YAAeC,EACf,QAAW,CACT,CACE,MAAS,SACX,EACD,EAEH,CACE,YAAe,eACf,YAAe,gBACf,QAAW,CACT,CACE,MAAS,SACX,EACD,EAEH,CACE,YAAe,kBACf,YAAe,WACf,QAAW,CACT,CACE,MAAS,SACX,EACD,EAEH,CACE,YAAe,QACf,YAAe,WACf,QAAW,CACT,CACE,MAAS,SACX,EACD,EAEH,CACE,YAAe,QACf,YAAeD,EACf,QAAW,CACT,CACE,MAAS,SACX,EAEJ,EACD,2BCnND,MAzEwC,OAAC,SAyE1BnG,GAxEbf,CAAU,GAwEUe,EAAC,SAvErBjB,CAAY,eACZmB,CAAa,UACbgG,CAAQ,QACRM,EAAS,GAAG,OACZC,EAAQ,MAAM,CACdhI,UAAQ,MACRiI,EAAO,CAAC,SACRC,EAAU,CAAC,SACX1G,CAAO,CACR,GACO,QAAE2G,CAAM,CAAE,CAAGC,CAAAA,EAAAA,EAAAA,SAAAA,CAASA,GACtB,UAAEC,CAAQ,WAAEC,CAAS,CAAE,CAAIC,CAAAA,EAAAA,EAAAA,CAAAA,CAAaA,UAmB1CD,EAAkB,SAAP,CAAQ5F,MAAAA,UAAI,uBACtB2F,EAGH,UAAC3F,MAAAA,CAAIC,UAAU,yBACb,UAACD,MAAAA,CAAIC,UAAU,WAAW6F,MAAO,OAAER,SAAOD,EAAQxF,SAAU,UAAW,WACrE,WAACkG,EAAAA,EAASA,CAAAA,CACRC,kBAzBe,CACrBV,MAAOA,EACPD,OAA0B,iBAAXA,EAAsB,GAAU,OAAPA,EAAO,MAAMA,CACvD,EAuBQY,OAhBOlH,CAgBCkH,EArBM,CACpB5H,IAAK,SAIyB6H,CAH9BxH,IAAK,SACP,EAmBQ6G,KAAMA,EACNY,OAhBU,CAgBFC,GAfdC,EAAIC,UAAU,CAAC,CACbC,OAAQnB,CACV,EACF,EAaQoB,QAAS,CACPhB,EAhBWJ,MAgBFI,EACTtE,WAAW,EACXuF,kBAAmB,GACnBC,mBAAmB,EACnBC,YAAY,EACZC,gBAAgB,EAChBC,gBAAgB,EAChBC,mBAAmB,CACrB,YAEC/B,EACAjH,GAAcF,GAAgBA,EAAa0D,WAAW,EACrD,UAACsD,EAAgBA,CACf/E,SAAUjC,EAAa0D,SADRsD,EACmB,GAClCC,aAAc,KAEZkC,QAAQC,GAAG,CAAC,qBACZlI,GAAAA,GACF,WAEChB,GAHCgB,QA5BQ,UAACkB,MAAAA,UAAI,mBAsC7B,8IC/CA,MAjB8B,GAEtB,yBAeOiH,MAdH,WAAC/G,EAAAA,CAAGA,CAAAA,EAcqB,SAbrB,WAACC,EAAAA,CAAGA,CAAAA,CAACF,UAAU,UAAUG,GAAI,YACzB,UAAC8G,EAAAA,OAAeA,CAAAA,CAACvG,UAAWnD,EAAM2J,aAAa,CAAEhD,UAAW3G,EAAM2G,SAAS,CAAEH,SAAUxG,EAAM4J,UAAU,GACvG,UAACC,EAAAA,CAAiBA,CAAAA,CAACC,YAAa9J,EAAM2J,aAAa,CAACG,WAAW,GAC/D,UAACC,EAAAA,OAA8BA,CAAAA,CAAC5G,UAAWnD,EAAM2J,aAAa,MAElE,UAAChH,EAAAA,CAAGA,CAAAA,CAACF,UAAU,UAAUG,GAAI,WACzB,UAAChD,EAAAA,CAAgBA,CAAAA,CAACG,QAASC,EAAM2J,aAAa", "sources": ["webpack://_N_E/./components/common/maps/ShowMapContainer.tsx", "webpack://_N_E/./pages/operation/components/OperationStats.tsx", "webpack://_N_E/./components/common/RKIMapMarker.tsx", "webpack://_N_E/./components/common/Bookmark.tsx", "webpack://_N_E/./pages/operation/components/OperationInfo.tsx", "webpack://_N_E/./components/common/RKIMapInfowindow.tsx", "webpack://_N_E/./components/common/mapStyles.tsx", "webpack://_N_E/./components/common/RKIMap1.tsx", "webpack://_N_E/./pages/operation/components/OperationCoverSection.tsx"], "sourcesContent": ["//Import Library\r\nimport React, { useEffect, useState } from \"react\";\r\n\r\n//Import services/components\r\nimport RKIMap1 from \"../RKIMap1\";\r\nimport RKIMapMarker from \"../RKIMapMarker\";\r\nimport { useTranslation } from 'next-i18next';\r\n\r\ninterface MapData {\r\n  _id: string;\r\n  title: string;\r\n  country?: {\r\n    coordinates?: Array<{\r\n      latitude: string;\r\n      longitude: string;\r\n    }>;\r\n  };\r\n}\r\n\r\ninterface ShowMapContainerProps {\r\n  mapdata: MapData;\r\n}\r\n\r\nconst ShowMapContainer = (props: ShowMapContainerProps) => {\r\n  const { i18n } = useTranslation('common');\r\n  const currentLang = i18n.language;\r\n  const { mapdata } = props;\r\n  const [points, setPoints]: any = useState({});\r\n  const [activeMarker, setactiveMarker]: any = useState({});\r\n  const [markerInfo, setMarkerInfo]: any = useState({});\r\n\r\n  const MarkerInfo = (Markerprops: { info: { name?: string } }) => {\r\n    const { info } = Markerprops;\r\n    return <a>{info?.name}</a>;\r\n  };\r\n\r\n  const resetMarker = () => {\r\n    setactiveMarker(null);\r\n    setMarkerInfo(null);\r\n  };\r\n\r\n  const onMarkerClick = (onMarkerprops: { name: string }, marker: any, e: any) => {\r\n    resetMarker();\r\n    setactiveMarker(marker);\r\n    setMarkerInfo({\r\n      name: onMarkerprops.name,\r\n    });\r\n  };\r\n\r\n  const setPointsFromMapData = () => {\r\n    setPoints({\r\n      title: mapdata.title,\r\n      id: mapdata._id,\r\n      lat:\r\n        mapdata.country && mapdata.country.coordinates\r\n          ? parseFloat(mapdata.country.coordinates[0].latitude)\r\n          : null,\r\n      lng:\r\n        mapdata.country && mapdata.country.coordinates\r\n          ? parseFloat(mapdata.country.coordinates[0].longitude)\r\n          : null,\r\n    });\r\n  };\r\n\r\n  useEffect(() => {\r\n    setPointsFromMapData();\r\n  }, [mapdata]);\r\n  return (\r\n    <>\r\n      {\" \"}\r\n      {points && points.id ? (\r\n        <RKIMap1\r\n          onClose={resetMarker}\r\n          language={currentLang}\r\n          initialCenter={{ lat: points.lat, lng: points.lng }}\r\n          activeMarker={activeMarker}\r\n          markerInfo={<MarkerInfo info={markerInfo} />}\r\n        >\r\n          <RKIMapMarker\r\n            name={points.title}\r\n            icon={{\r\n              url: \"/images/map-marker-white.svg\",\r\n            }}\r\n            onClick={onMarkerClick}\r\n            position={points}\r\n          />\r\n        </RKIMap1>\r\n      ) : null}\r\n    </>\r\n  );\r\n};\r\n\r\nexport default ShowMapContainer;\r\n", "//Import Library\r\nimport React from \"react\";\r\nimport { faBell, faUsers } from \"@fortawesome/free-solid-svg-icons\";\r\nimport { FontAwesomeIcon } from \"@fortawesome/react-fontawesome\";\r\nimport { Col, Row } from \"react-bootstrap\";\r\n\r\n//Import services/components\r\nimport { useTranslation } from 'next-i18next';\r\n\r\n\r\nconst operationStats = (props: any) => {\r\n    const { t } = useTranslation('common');\r\n    return (\r\n        <>\r\n            <div className=\"operationStats\">\r\n                <Row>\r\n                    <Col className=\"operationInfo-Items\" md={6}>\r\n                        <div className=\"operationIcon\">\r\n                            <FontAwesomeIcon icon={faUsers} color=\"#fff\" size=\"2x\" />\r\n                        </div>\r\n                        <div className=\"operationInfo\">\r\n                            <h5>{t(\"Partners\")}</h5>\r\n                            <h4>{props.operation.partners.length}</h4>\r\n                        </div>\r\n                    </Col>\r\n                    <Col className=\"operationInfo-Items\" md={6}>\r\n                        <div className=\"operationIcon\">\r\n                            <FontAwesomeIcon icon={faBell} color=\"#fff\" size=\"2x\" />\r\n                        </div>\r\n                        <div className=\"operationInfo\">\r\n                            <h5>{t(\"ActivitiesinField\")}</h5>\r\n                            <h4>{props.operation.timeline.length}</h4>\r\n                        </div>\r\n                    </Col>\r\n                </Row>\r\n            </div>\r\n        </>\r\n    )\r\n}\r\n\r\nexport default operationStats;", "import React from 'react';\r\nimport { Marker } from '@react-google-maps/api';\r\n\r\ninterface RKIMapMarkerProps {\r\n  name?: string;\r\n  id?: string;\r\n  countryId?: string;\r\n  type?: string;\r\n  icon?: {\r\n    url: string;\r\n    scaledSize?: google.maps.Size;\r\n  };\r\n  position: {\r\n    lat: number;\r\n    lng: number;\r\n  };\r\n  onClick?: (props: any, marker: any, e: any) => void;\r\n  title?: string;\r\n  draggable?: boolean;\r\n}\r\n\r\nconst RKIMapMarker: React.FC<RKIMapMarkerProps> = ({\r\n  name = 'Marker',\r\n  id = '',\r\n  countryId = '',\r\n  type,\r\n  icon,\r\n  position,\r\n  onClick,\r\n  title,\r\n  draggable = false,\r\n}) => {\r\n  const handleClick = (e: google.maps.MapMouseEvent) => {\r\n    if (onClick) {\r\n      const markerProps = {\r\n        name,\r\n        id,\r\n        countryId,\r\n        type,\r\n        position,\r\n      };\r\n\r\n      // Create a marker-like object for compatibility with old onClick signature\r\n      const marker = {\r\n        position,\r\n        getPosition: () => position,\r\n      };\r\n\r\n      onClick(markerProps, marker, e);\r\n    }\r\n  };\r\n\r\n  // Ensure position is valid\r\n  if (!position || typeof position.lat !== 'number' || typeof position.lng !== 'number') {\r\n    return null;\r\n  }\r\n\r\n  return (\r\n    <Marker\r\n      position={position}\r\n      icon={icon}\r\n      title={title || name}\r\n      draggable={draggable}\r\n      onClick={handleClick}\r\n    />\r\n  );\r\n};\r\n\r\nexport default RKIMapMarker;\r\n", "//Import Library\r\nimport { connect } from \"react-redux\";\r\nimport {faBookmark, faCheckCircle, faPlusCircle} from \"@fortawesome/free-solid-svg-icons\";\r\nimport {useEffect, useState} from \"react\";\r\nimport {FontAwesomeIcon} from \"@fortawesome/react-fontawesome\";\r\n\r\n//Import services/components\r\nimport apiService from \"../../services/apiService\";\r\n\r\nconst onModelOptions = {\r\n  operation: \"Operation\",\r\n  institution: \"Institution\",\r\n  event: \"Event\",\r\n  project: \"Project\",\r\n  vspace: \"Vspace\"\r\n} as const;\r\n\r\ninterface BookMarkProps {\r\n  user?: {\r\n    _id: string;\r\n  };\r\n  entityId: string;\r\n  entityType: keyof typeof onModelOptions;\r\n}\r\n\r\nconst BookMark: React.FC<BookMarkProps> = (props) => {\r\n  const { user, entityId, entityType } = props;\r\n  const [bookmark, setBookmark] = useState<boolean>(false);\r\n  const [subscribe, setSubscribe] = useState<any>(\"\");\r\n\r\n  //Handle bookmark feature\r\n  const fetchIfContentSubscribed = async () => {\r\n    if (!user?._id) return;\r\n    const checkFlag = await apiService.get('/flag', {query: {entity_id: entityId, user: user._id, onModel: onModelOptions[entityType]}});\r\n    if (checkFlag && checkFlag.data && checkFlag.data.length > 0) {\r\n      setSubscribe(checkFlag.data[0]);\r\n      setBookmark(true);\r\n    }\r\n  };\r\n\r\n  const bookmarkHandler = async (e: React.MouseEvent<HTMLAnchorElement>) => {\r\n    e.preventDefault();\r\n    if (!user?._id) return;\r\n    const flag  = !bookmark;\r\n    const flagPayload = {\r\n      entity_type: entityType,\r\n      entity_id: entityId,\r\n      user: user._id,\r\n      onModel: onModelOptions[entityType]\r\n    }\r\n    if (flag) {\r\n      const flagIt: any = await apiService.post('/flag', flagPayload);\r\n      if (flagIt && flagIt._id) {\r\n        setSubscribe(flagIt);\r\n        setBookmark(flag);\r\n      }\r\n    } else {\r\n      const unFlagIt = await apiService.remove(`/flag/${subscribe._id}`);\r\n      if (unFlagIt && unFlagIt.n) {\r\n        setBookmark(flag);\r\n      }\r\n    }\r\n  }\r\n  //END Bookmark handler\r\n  useEffect(() => {\r\n    fetchIfContentSubscribed();\r\n  },[]);\r\n  return (\r\n    <div className=\"subscribe-flag\">\r\n      <a href=\"\" onClick={bookmarkHandler}>\r\n        <span className=\"check\">\r\n          {bookmark ?\r\n            <FontAwesomeIcon className=\"clickable checkIcon\" icon={faCheckCircle} color=\"#00CC00\" />\r\n            :\r\n            <FontAwesomeIcon className=\"clickable minusIcon\" icon={faPlusCircle} color=\"#fff\"/>\r\n          }\r\n        </span>\r\n        <FontAwesomeIcon className=\"bookmark\" icon={faBookmark} color=\"#d4d4d4\"/>\r\n      </a>\r\n    </div>\r\n  )\r\n}\r\n\r\nexport default connect((state: any) => state)(BookMark);\r\n\r\n", "//Import Library\r\nimport React from \"react\";\r\nimport { But<PERSON> } from \"react-bootstrap\";\r\nimport { FontAwesomeIcon } from \"@fortawesome/react-fontawesome\";\r\nimport Link from \"next/link\";\r\nimport { faPen } from \"@fortawesome/free-solid-svg-icons\";\r\n\r\n//Import services/components\r\nimport Bookmark from \"../../../components/common/Bookmark\";\r\nimport { canEditOperation } from \"../permission\";\r\nimport { useTranslation } from 'next-i18next';\r\n\r\n\r\nconst OperationInfo = (prop: any) => {\r\n    const { t } = useTranslation('common');\r\n    const EditOperationLink = () => {\r\n        return (\r\n            <>\r\n                {prop.editData ? \r\n                    <Link\r\n                        href=\"/operation/[...routes]\"\r\n                        as={`/operation/edit/${prop.routeData.routes[1]}`}\r\n                        >\r\n                        <Button variant=\"secondary\" size=\"sm\">\r\n                            <FontAwesomeIcon icon={faPen} />\r\n                            &nbsp;{t(\"Edit\")}\r\n                        </Button>\r\n                    </Link> \r\n                : \"\"}\r\n            </>\r\n        );\r\n    };\r\n\r\n    const CanEditOperation = canEditOperation(() => <EditOperationLink />);\r\n\r\n    return (\r\n        <>\r\n            <section className=\"d-flex justify-content-between\">\r\n                <h4 className=\"operationTitle\">\r\n                    {prop.operation.title}&nbsp;&nbsp;\r\n                    {prop.routeData.routes && prop.routeData.routes[1] ? (\r\n                        <CanEditOperation operation={prop.operation} />\r\n                    ) : null}\r\n                </h4>\r\n                <Bookmark entityId={prop.routeData.routes[1]} entityType=\"operation\" />\r\n            </section>\r\n        </>\r\n    )\r\n}\r\n\r\nexport default OperationInfo;", "import { InfoWindow } from '@react-google-maps/api';\r\n\r\ninterface Props {\r\n  position: google.maps.LatLngLiteral;\r\n  onCloseClick?: () => void;\r\n  children?: React.ReactNode;\r\n}\r\n\r\nconst RKIMapInfowindow = ({ position, onCloseClick, children }: Props) => {\r\n  return (\r\n    <InfoWindow position={position} onCloseClick={onCloseClick}>\r\n      <div>{children}</div>\r\n    </InfoWindow>\r\n  );\r\n};\r\n\r\nexport default RKIMapInfowindow;\r\n", "const fill = \"labels.text.fill\"\r\nconst stoke = \"labels.text.stroke\"\r\nconst road = \"road.highway\"\r\nconst geometry = \"geometry.stroke\"\r\nconst mapStyles = [\r\n  {\r\n    \"elementType\": \"geometry\",\r\n    \"stylers\": [\r\n      {\r\n        \"color\": \"#1d2c4d\"\r\n      }\r\n    ]\r\n  },\r\n  {\r\n    \"elementType\": fill,\r\n    \"stylers\": [\r\n      {\r\n        \"color\": \"#8ec3b9\"\r\n      }\r\n    ]\r\n  },\r\n  {\r\n    \"elementType\": stoke,\r\n    \"stylers\": [\r\n      {\r\n        \"color\": \"#1a3646\"\r\n      }\r\n    ]\r\n  },\r\n  {\r\n    \"featureType\": \"administrative\",\r\n    \"elementType\": \"geometry\",\r\n    \"stylers\": [\r\n      {\r\n        \"visibility\": \"off\"\r\n      }\r\n    ]\r\n  },\r\n  {\r\n    \"featureType\": \"administrative.country\",\r\n    \"elementType\": geometry,\r\n    \"stylers\": [\r\n      {\r\n        \"color\": \"#4b6878\"\r\n      }\r\n    ]\r\n  },\r\n  {\r\n    \"featureType\": \"administrative.land_parcel\",\r\n    \"elementType\": \"labels\",\r\n    \"stylers\": [\r\n      {\r\n        \"visibility\": \"off\"\r\n      }\r\n    ]\r\n  },\r\n  {\r\n    \"featureType\": \"administrative.land_parcel\",\r\n    \"elementType\": fill,\r\n    \"stylers\": [\r\n      {\r\n        \"color\": \"#64779e\"\r\n      }\r\n    ]\r\n  },\r\n  {\r\n    \"featureType\": \"administrative.province\",\r\n    \"elementType\": geometry,\r\n    \"stylers\": [\r\n      {\r\n        \"color\": \"#4b6878\"\r\n      }\r\n    ]\r\n  },\r\n  {\r\n    \"featureType\": \"landscape.man_made\",\r\n    \"elementType\": geometry,\r\n    \"stylers\": [\r\n      {\r\n        \"color\": \"#334e87\"\r\n      }\r\n    ]\r\n  },\r\n  {\r\n    \"featureType\": \"landscape.natural\",\r\n    \"elementType\": \"geometry\",\r\n    \"stylers\": [\r\n      {\r\n        \"color\": \"#023e58\"\r\n      }\r\n    ]\r\n  },\r\n  {\r\n    \"featureType\": \"poi\",\r\n    \"stylers\": [\r\n      {\r\n        \"visibility\": \"off\"\r\n      }\r\n    ]\r\n  },\r\n  {\r\n    \"featureType\": \"poi\",\r\n    \"elementType\": \"geometry\",\r\n    \"stylers\": [\r\n      {\r\n        \"color\": \"#283d6a\"\r\n      }\r\n    ]\r\n  },\r\n  {\r\n    \"featureType\": \"poi\",\r\n    \"elementType\": \"labels.text\",\r\n    \"stylers\": [\r\n      {\r\n        \"visibility\": \"off\"\r\n      }\r\n    ]\r\n  },\r\n  {\r\n    \"featureType\": \"poi\",\r\n    \"elementType\": fill,\r\n    \"stylers\": [\r\n      {\r\n        \"color\": \"#6f9ba5\"\r\n      }\r\n    ]\r\n  },\r\n  {\r\n    \"featureType\": \"poi\",\r\n    \"elementType\": stoke,\r\n    \"stylers\": [\r\n      {\r\n        \"color\": \"#1d2c4d\"\r\n      }\r\n    ]\r\n  },\r\n  {\r\n    \"featureType\": \"poi.park\",\r\n    \"elementType\": \"geometry.fill\",\r\n    \"stylers\": [\r\n      {\r\n        \"color\": \"#023e58\"\r\n      }\r\n    ]\r\n  },\r\n  {\r\n    \"featureType\": \"poi.park\",\r\n    \"elementType\": fill,\r\n    \"stylers\": [\r\n      {\r\n        \"color\": \"#3C7680\"\r\n      }\r\n    ]\r\n  },\r\n  {\r\n    \"featureType\": \"road\",\r\n    \"stylers\": [\r\n      {\r\n        \"visibility\": \"off\"\r\n      }\r\n    ]\r\n  },\r\n  {\r\n    \"featureType\": \"road\",\r\n    \"elementType\": \"geometry\",\r\n    \"stylers\": [\r\n      {\r\n        \"color\": \"#304a7d\"\r\n      }\r\n    ]\r\n  },\r\n  {\r\n    \"featureType\": \"road\",\r\n    \"elementType\": \"labels.icon\",\r\n    \"stylers\": [\r\n      {\r\n        \"visibility\": \"off\"\r\n      }\r\n    ]\r\n  },\r\n  {\r\n    \"featureType\": \"road\",\r\n    \"elementType\": fill,\r\n    \"stylers\": [\r\n      {\r\n        \"color\": \"#98a5be\"\r\n      }\r\n    ]\r\n  },\r\n  {\r\n    \"featureType\": \"road\",\r\n    \"elementType\": stoke,\r\n    \"stylers\": [\r\n      {\r\n        \"color\": \"#1d2c4d\"\r\n      }\r\n    ]\r\n  },\r\n  {\r\n    \"featureType\": road,\r\n    \"elementType\": \"geometry\",\r\n    \"stylers\": [\r\n      {\r\n        \"color\": \"#2c6675\"\r\n      }\r\n    ]\r\n  },\r\n  {\r\n    \"featureType\": road,\r\n    \"elementType\": geometry,\r\n    \"stylers\": [\r\n      {\r\n        \"color\": \"#255763\"\r\n      }\r\n    ]\r\n  },\r\n  {\r\n    \"featureType\": road,\r\n    \"elementType\": fill,\r\n    \"stylers\": [\r\n      {\r\n        \"color\": \"#b0d5ce\"\r\n      }\r\n    ]\r\n  },\r\n  {\r\n    \"featureType\": road,\r\n    \"elementType\": stoke,\r\n    \"stylers\": [\r\n      {\r\n        \"color\": \"#023e58\"\r\n      }\r\n    ]\r\n  },\r\n  {\r\n    \"featureType\": \"road.local\",\r\n    \"elementType\": \"labels\",\r\n    \"stylers\": [\r\n      {\r\n        \"visibility\": \"off\"\r\n      }\r\n    ]\r\n  },\r\n  {\r\n    \"featureType\": \"transit\",\r\n    \"stylers\": [\r\n      {\r\n        \"visibility\": \"off\"\r\n      }\r\n    ]\r\n  },\r\n  {\r\n    \"featureType\": \"transit\",\r\n    \"elementType\": fill,\r\n    \"stylers\": [\r\n      {\r\n        \"color\": \"#98a5be\"\r\n      }\r\n    ]\r\n  },\r\n  {\r\n    \"featureType\": \"transit\",\r\n    \"elementType\": stoke,\r\n    \"stylers\": [\r\n      {\r\n        \"color\": \"#1d2c4d\"\r\n      }\r\n    ]\r\n  },\r\n  {\r\n    \"featureType\": \"transit.line\",\r\n    \"elementType\": \"geometry.fill\",\r\n    \"stylers\": [\r\n      {\r\n        \"color\": \"#283d6a\"\r\n      }\r\n    ]\r\n  },\r\n  {\r\n    \"featureType\": \"transit.station\",\r\n    \"elementType\": \"geometry\",\r\n    \"stylers\": [\r\n      {\r\n        \"color\": \"#3a4762\"\r\n      }\r\n    ]\r\n  },\r\n  {\r\n    \"featureType\": \"water\",\r\n    \"elementType\": \"geometry\",\r\n    \"stylers\": [\r\n      {\r\n        \"color\": \"#0e1626\"\r\n      }\r\n    ]\r\n  },\r\n  {\r\n    \"featureType\": \"water\",\r\n    \"elementType\": fill,\r\n    \"stylers\": [\r\n      {\r\n        \"color\": \"#4e6d70\"\r\n      }\r\n    ]\r\n  }\r\n];\r\n\r\nexport default mapStyles;", "import React from 'react';\r\nimport { GoogleMap, useJsApiLoader } from '@react-google-maps/api';\r\nimport RKIMapInfowindow from './RKIMapInfowindow';\r\nimport mapStyles from './mapStyles';\r\nimport { useRouter } from \"next/router\";\r\nimport { useGoogleMaps } from './GoogleMapsProvider';\r\n\r\ninterface RKIMap1Props {\r\n  markerInfo?: React.ReactNode;\r\n  activeMarker?: any;\r\n  initialCenter?: { lat: number; lng: number };\r\n  children?: React.ReactNode;\r\n  height?: number | string;\r\n  width?: string;\r\n  language?: string;\r\n  points?: any[];\r\n  zoom?: number;\r\n  minZoom?: number;\r\n  onClose?: () => void\r\n}\r\n\r\nconst RKIMap1: React.FC<RKIMap1Props> = ({\r\n  markerInfo,\r\n  activeMarker,\r\n  initialCenter,\r\n  children,\r\n  height = 300,\r\n  width = \"114%\",\r\n  language,\r\n  zoom = 1,\r\n  minZoom = 1,\r\n  onClose\r\n}) => {\r\n  const { locale } = useRouter();\r\n  const { isLoaded, loadError } =  useGoogleMaps();\r\n  const containerStyle = {\r\n    width: width,\r\n    height: typeof height === 'number' ? `${height}px` : height,\r\n  };\r\n\r\n  const defaultCenter = {\r\n    lat: 52.520017,\r\n    lng: 13.404195,\r\n  };\r\n\r\n  const center = initialCenter || defaultCenter;\r\n\r\n  const onMapLoad = (map: google.maps.Map) => {\r\n    map.setOptions({\r\n      styles: mapStyles,\r\n    });\r\n  };\r\n\r\n  if (loadError) return <div>Error loading maps</div>;\r\n  if (!isLoaded) return <div>Loading Maps...</div>;\r\n\r\n  return (\r\n    <div className=\"map-container\">\r\n      <div className=\"mapprint\" style={{ width, height, position: 'relative' }}>\r\n        <GoogleMap\r\n          mapContainerStyle={containerStyle}\r\n          center={center}\r\n          zoom={zoom}\r\n          onLoad={onMapLoad}\r\n          options={{\r\n            minZoom: minZoom,\r\n            draggable: true,\r\n            keyboardShortcuts: false,\r\n            streetViewControl: false,\r\n            panControl: false,\r\n            clickableIcons: false,\r\n            mapTypeControl: false,\r\n            fullscreenControl: true,\r\n          }}\r\n        >\r\n          {children}\r\n          {markerInfo && activeMarker && activeMarker.getPosition && (\r\n            <RKIMapInfowindow\r\n              position={activeMarker.getPosition()}\r\n              onCloseClick={() => {\r\n                // Handle close if needed\r\n                console.log('close click');\r\n                onClose?.()\r\n              }}\r\n            >\r\n              {markerInfo}\r\n            </RKIMapInfowindow>\r\n          )}\r\n        </GoogleMap>\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default RKIMap1;\r\n", "//Import Library\r\nimport React from \"react\";\r\nimport { Col, Row } from \"react-bootstrap\";\r\n\r\n//Import services/components\r\nimport OperationHeader from \"./OperationInfo\";\r\nimport ReadMoreContainer from \"../../../components/common/readMore/readMore\";\r\nimport ShowMapContainer from \"../../../components/common/maps/ShowMapContainer\";\r\nimport OperationPartnersAndActivities from \"./OperationStats\";\r\n\r\n\r\n\r\ninterface OperationCoverSectionProps {\r\n  operationData: {\r\n    _id: string;\r\n    title: string;\r\n    description: string;\r\n    country?: {\r\n      coordinates?: Array<{\r\n        latitude: string;\r\n        longitude: string;\r\n      }>;\r\n    };\r\n  };\r\n  routeData: any;\r\n  editAccess: boolean;\r\n}\r\n\r\nconst OperationCoverSection = (props: OperationCoverSectionProps) => {\r\n    return (\r\n        <>\r\n            <Row>\r\n                <Col className=\"ps-md-0\" md={7}>\r\n                    <OperationHeader operation={props.operationData} routeData={props.routeData} editData={props.editAccess} />\r\n                    <ReadMoreContainer description={props.operationData.description} />\r\n                    <OperationPartnersAndActivities operation={props.operationData} />\r\n                </Col>\r\n                <Col className=\"pe-md-0\" md={5}>\r\n                    <ShowMapContainer mapdata={props.operationData} />\r\n                </Col>\r\n            </Row>\r\n        </>\r\n    )\r\n}\r\n\r\nexport default OperationCoverSection;"], "names": ["i18n", "useTranslation", "ShowMapContainer", "currentLang", "language", "mapdata", "props", "points", "setPoints", "useState", "activeMarker", "set<PERSON><PERSON><PERSON><PERSON>", "markerInfo", "setMarkerInfo", "reset<PERSON><PERSON><PERSON>", "setPointsFromMapData", "title", "id", "_id", "lat", "country", "coordinates", "parseFloat", "latitude", "lng", "longitude", "useEffect", "RKIMap1", "onClose", "initialCenter", "Markerprops", "info", "a", "name", "MarkerInfo", "R<PERSON>IMapMarker", "icon", "url", "onClick", "onMarkerClick", "marker", "e", "onMarkerprops", "position", "t", "operationStats", "div", "className", "Row", "Col", "md", "FontAwesomeIcon", "faUsers", "color", "size", "h5", "h4", "operation", "partners", "length", "faBell", "timeline", "countryId", "type", "draggable", "<PERSON><PERSON>", "handleClick", "markerProps", "getPosition", "onModelOptions", "institution", "event", "project", "vspace", "connect", "state", "user", "BookMark", "entityId", "entityType", "bookmark", "setBookmark", "subscribe", "setSubscribe", "fetchIfContentSubscribed", "checkFlag", "apiService", "get", "query", "entity_id", "onModel", "data", "bookmarkHandler", "preventDefault", "flag", "flagPayload", "entity_type", "flagIt", "post", "unFlagIt", "remove", "n", "href", "span", "faCheckCircle", "faPlusCircle", "faBookmark", "OperationInfo", "EditOperationLink", "prop", "editData", "Link", "as", "routeData", "routes", "<PERSON><PERSON>", "variant", "faPen", "CanEditOperation", "canEditOperation", "section", "Bookmark", "RKIMapInfowindow", "onCloseClick", "InfoWindow", "children", "fill", "stoke", "road", "geometry", "mapStyles", "height", "width", "zoom", "minZoom", "locale", "useRouter", "isLoaded", "loadError", "useGoogleMaps", "style", "GoogleMap", "mapContainerStyle", "center", "defaultCenter", "onLoad", "onMapLoad", "map", "setOptions", "styles", "options", "keyboardShortcuts", "streetViewControl", "panControl", "clickableIcons", "mapTypeControl", "fullscreenControl", "console", "log", "OperationCoverSection", "OperationHeader", "operationData", "editAccess", "ReadMoreContainer", "description", "OperationPartnersAndActivities"], "sourceRoot": "", "ignoreList": []}