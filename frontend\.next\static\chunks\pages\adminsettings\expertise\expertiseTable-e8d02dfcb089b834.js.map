{"version": 3, "file": "static/chunks/pages/adminsettings/expertise/expertiseTable-e8d02dfcb089b834.js", "mappings": "0KAqCA,SAASA,EAASC,CAAoB,EACpC,GAAM,CAAEC,CAAC,CAAE,CAAGC,CAAAA,EAAAA,EAAAA,EAAAA,CAAcA,CAAC,UACvBC,EAA6B,CACjCC,gBAAiBH,EAAE,cACnB,EACI,CACJI,SAAO,MACPC,CAAI,WACJC,CAAS,uBACTC,CAAqB,WACrBC,CAAS,oBACTC,CAAkB,CAClBC,qBAAmB,CACnBC,kBAAgB,aAChBC,CAAW,oBACXC,CAAkB,gBAClBC,CAAc,SACdC,CAAO,WACPC,CAAS,sBACTC,CAAoB,mBACpBC,CAAiB,YACjBC,CAAU,QACVC,CAAM,kBACNC,CAAgB,cAChBC,CAAY,CAEZ,CADA,EACGC,EACJ,CAAGxB,EAGEyB,EAAiB,4BACrBtB,EACAuB,gBAAiBzB,EAAE,IAP0C,MAQ7D0B,UAAU,UACVtB,EACAC,KAAMA,GAAQ,EAAE,CAChBsB,OAAO,EACPC,2BAA4BrB,EAC5BsB,UAAWrB,EACXsB,gBAAiBf,qBACjBN,EACAsB,WAAY,GACZC,iBAAkBhB,EAClBiB,kBAAmBpB,GAA0C,GAC7DqB,eADwCrB,cACVD,GAA4B,CAAC,GAAI,GAAI,GAAI,CAA3BA,EAA+B,GAAG,CAC9EuB,oBAAqB7B,EACrB8B,oBAAqB1B,EACrB2B,aAAc1B,iBACdG,EACAG,yCACAC,EACAoB,kBAAmB,UAACC,EAAAA,CAAYA,CAAAA,CAAAA,GAChCC,SAAU,UAACC,IAAAA,CAAEC,UAAU,6CACvBvB,SACAC,eACAE,mBACAD,EACAqB,UAAW,WACb,EACA,MACE,UAACC,EAAAA,EAASA,CAAAA,CAAE,GAAGnB,CAAc,EAEjC,CAEA1B,EAAS8C,YAAY,CAAG,CACtBf,WAAW,EACXE,YAAY,EACZzB,UAAW,KACXU,WAAW,EACXC,qBAAsB,KACtBC,mBAAmB,EACnBC,YAAY,EACZE,kBAAkB,CACpB,EAEA,MAAevB,QAAQA,EAAC,SC/GxB,4CACA,0CACA,WACA,OAAe,EAAQ,KAA+D,CACtF,EACA,SAFsB,qKCgItB,MAzHuB,IACnB,GAAM,CAAEE,CAAC,CAAE,CAAGC,CAAAA,EAAAA,EAAAA,EAAAA,CAAcA,CAAC,QAwHlB4C,EAvHL,CAACC,EAAWC,EAAe,CAAGC,CAAAA,EAAAA,EAAAA,CAuHXH,EAAC,KAvHUG,CAAQA,CAAC,EAAE,EACzC,EAAGC,EAAW,CAAGD,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,EAAC,GAC1B,CAAC1C,EAAW4C,EAAa,CAAGF,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAC,GACrC,CAACG,EAASC,EAAW,CAAGJ,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAC,IACjC,CAACK,EAAaC,EAAS,CAAGN,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,EAAC,GACnC,CAACO,EAAiBC,EAAmB,CAAGR,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAC,CAAC,GAGlDS,EAAkB,CACpBC,KAAM,CAAEC,MAAO,KAAM,EACrBC,MAAOT,EACPU,KAAM,EACNC,MAAO,CAAC,CACZ,EAEM1D,EAAU,CACZ,CACI2D,KAAM/D,EAAE,sCACRgE,SAAU,OACd,EACA,CACID,KAAM/D,EAAE,uCACRgE,SAAU,GACVC,KAAM,GACF,WAACC,MAAAA,WACG,UAACC,IAAIA,CAACC,KAAK,6BAA6BC,GAAI,OAAvCF,0BAA8E,OAANG,EAAEC,GAAG,WAE9E,UAAC9B,IAAAA,CAAEC,UAAU,uBAEV,OAEP,UAAC8B,IAAAA,CAAEC,QAAS,GAAOC,EAAWJ,EAAGK,YAC7B,UAAClC,IAAAA,CAAEC,UAAU,4BACZ,MAGjB,EACH,CAEKkC,EAAmB,UACrB3B,GAAW,GACX,IAAM4B,EAAW,MAAMC,EAAAA,CAAUA,CAACC,GAAG,CAAC,aAActB,GAChDoB,GAAYA,EAASxE,IAAI,EAAIwE,EAASxE,IAAI,CAAC2E,MAAM,CAAG,GAAG,CACvDjC,EAAe8B,EAASxE,IAAI,EAC5B6C,EAAa2B,EAASI,UAAU,EAChChC,GAAW,GAEnB,EAQMvC,EAAsB,MAAOwE,EAAiBrB,KAChDJ,EAAgBG,KAAK,CAAGsB,EACxBzB,EAAgBI,IAAI,CAAGA,EACvBZ,GAAW,GACX,IAAM4B,EAAW,MAAMC,EAAAA,CAAUA,CAACC,GAAG,CAAC,aAActB,GAChDoB,GAAYA,EAASxE,IAAI,EAAIwE,EAASxE,IAAI,CAAC2E,MAAM,CAAG,GAAG,CACvDjC,EAAe8B,EAASxE,IAAI,EAC5B+C,EAAW8B,GACXjC,GAAW,GAEnB,EAEMyB,EAAa,MAAOS,EAAUR,KAChCA,EAAES,cAAc,GAChB5B,EAAmB2B,EAAIZ,GAAG,EAC1BjB,GAAS,EACb,EAEM+B,EAAe,UACjB,GAAI,CACA,MAAMP,EAAAA,CAAUA,CAACQ,MAAM,CAAC,cAA8B,OAAhB/B,IACtCqB,IACAtB,EAAS,IACTiC,EAAAA,EAAKA,CAACC,OAAO,CAACxF,EAAE,6DACpB,CAAE,MAAOyF,EAAO,CACZF,EAAAA,EAAKA,CAACE,KAAK,CAACzF,EAAE,uDAClB,CACJ,EAEM0F,EAAY,IAAMpC,GAAS,GAMjC,MAJAqC,CAAAA,EAAAA,EAAAA,SAAAA,CAASA,CAAC,KACNf,GACJ,EAAG,EAAE,EAGD,WAACV,MAAAA,WACG,WAAC0B,EAAAA,CAAKA,CAAAA,CAACC,KAAMxC,EAAayC,OAAQJ,YAC9B,UAACE,EAAAA,CAAKA,CAACG,MAAM,EAACC,WAAW,aACrB,WAACJ,EAAAA,CAAKA,CAACK,KAAK,YAAC,IAAEjG,EAAE,qDAErB,UAAC4F,EAAAA,CAAKA,CAACM,IAAI,WAAElG,EAAE,uEACf,WAAC4F,EAAAA,CAAKA,CAACO,MAAM,YACT,UAACC,EAAAA,CAAMA,CAAAA,CAACC,QAAQ,YAAY5B,QAASiB,WAChC1F,EAAE,yCAEP,UAACoG,EAAAA,CAAMA,CAAAA,CAACC,QAAQ,UAAU5B,QAASY,WAC9BrF,EAAE,4CAKf,UAACF,EAAAA,CAAQA,CAAAA,CACLM,QAASA,EACTC,KAAMyC,EACNxC,UAAWA,EACXU,WAAW,EACXN,oBAAqBA,EACrBC,iBAhEa,CAgEKA,GA/D1B8C,EAAgBG,KAAK,CAAGT,EACxBM,EAAgBI,IAAI,CAAGA,EACvBe,GACJ,MAgEJ", "sources": ["webpack://_N_E/./components/common/RKITable.tsx", "webpack://_N_E/?9b1d", "webpack://_N_E/./pages/adminsettings/expertise/expertiseTable.tsx"], "sourcesContent": ["//Import Library\r\nimport React from 'react'\r\nimport DataTable  from 'react-data-table-component';\r\n\r\n//Import services/components\r\nimport { useTranslation } from 'next-i18next';\r\nimport CustomLoader from './CustomLoader';\r\n\r\ninterface RKITableProps {\r\n  columns: any[];\r\n  data: any[];\r\n  totalRows?: number;\r\n  resetPaginationToggle?: boolean;\r\n  subheader?: boolean;\r\n  subHeaderComponent?: React.ReactNode;\r\n  handlePerRowsChange?: (newPerPage: number, page: number) => void;\r\n  handlePageChange?: (page: number) => void;\r\n  rowsPerPage?: number[];\r\n  defaultRowsPerPage?: number;\r\n  selectableRows?: boolean;\r\n  loading?: boolean;\r\n  pagServer?: boolean;\r\n  onSelectedRowsChange?: (selectedRows: any) => void;\r\n  clearSelectedRows?: boolean;\r\n  sortServer?: boolean;\r\n  onSort?: (column: any, sortDirection: string) => void;\r\n  persistTableHead?: boolean;\r\n  sortFunction?: (rows: any[], field: any, direction: 'asc' | 'desc') => any[];\r\n  noHeader?: boolean;\r\n  dense?: boolean;\r\n  pagination?: boolean;\r\n  paginationServer?: boolean;\r\n  paginationTotalRows?: number;\r\n  subHeaderAlign?: string;\r\n  [key: string]: any;\r\n}\r\n\r\nfunction RKITable(props: RKITableProps) {\r\n  const { t } = useTranslation('common');\r\n  const paginationComponentOptions = {\r\n    rowsPerPageText: t('Rowsperpage'),\r\n    };\r\n  const {\r\n    columns,\r\n    data,\r\n    totalRows,\r\n    resetPaginationToggle,\r\n    subheader,\r\n    subHeaderComponent,\r\n    handlePerRowsChange,\r\n    handlePageChange,\r\n    rowsPerPage,\r\n    defaultRowsPerPage,\r\n    selectableRows,\r\n    loading,\r\n    pagServer,\r\n    onSelectedRowsChange,\r\n    clearSelectedRows,\r\n    sortServer,\r\n    onSort,\r\n    persistTableHead,\r\n    sortFunction,\r\n    // Filter out any unknown props that might cause DOM warnings\r\n    ...otherProps\r\n  } = props;\r\n\r\n  // Only pass known DataTable props to avoid DOM warnings\r\n  const dataTableProps = {\r\n    paginationComponentOptions,\r\n    noDataComponent: t(\"NoData\"),\r\n    noHeader: true,\r\n    columns,\r\n    data: data || [],\r\n    dense: true,\r\n    paginationResetDefaultPage: resetPaginationToggle,\r\n    subHeader: subheader,\r\n    progressPending: loading,\r\n    subHeaderComponent,\r\n    pagination: true,\r\n    paginationServer: pagServer,\r\n    paginationPerPage: defaultRowsPerPage ? defaultRowsPerPage : 10,\r\n    paginationRowsPerPageOptions: rowsPerPage ? rowsPerPage : [10, 15, 20, 25, 30],\r\n    paginationTotalRows: totalRows,\r\n    onChangeRowsPerPage: handlePerRowsChange,\r\n    onChangePage: handlePageChange,\r\n    selectableRows,\r\n    onSelectedRowsChange,\r\n    clearSelectedRows,\r\n    progressComponent: <CustomLoader/>,\r\n    sortIcon: <i className=\"sort-icon fas fa-exchange-alt\" />,\r\n    sortServer,\r\n    onSort,\r\n    sortFunction,\r\n    persistTableHead,\r\n    className: \"rki-table\"\r\n  };\r\n  return (\r\n    <DataTable {...dataTableProps} />\r\n  )\r\n};\r\n\r\nRKITable.defaultProps = {\r\n  subHeader: false,\r\n  pagination: true,\r\n  totalRows: null,\r\n  pagServer: true,\r\n  onSelectedRowsChange: null,\r\n  clearSelectedRows: false,\r\n  sortServer: false,\r\n  persistTableHead: false\r\n}\r\n\r\nexport default RKITable;\r\n\r\n", "\n    (window.__NEXT_P = window.__NEXT_P || []).push([\n      \"/adminsettings/expertise/expertiseTable\",\n      function () {\n        return require(\"private-next-pages/adminsettings/expertise/expertiseTable.tsx\");\n      }\n    ]);\n    if(module.hot) {\n      module.hot.dispose(function () {\n        window.__NEXT_P.push([\"/adminsettings/expertise/expertiseTable\"])\n      });\n    }\n  ", "//Import Library\r\nimport { useState, useEffect } from \"react\";\r\nimport Link from \"next/link\";\r\nimport { <PERSON><PERSON>, Button } from \"react-bootstrap\";\r\nimport toast from 'react-hot-toast';\r\n\r\n//Import services/components\r\nimport RKITable from \"../../../components/common/RKITable\";\r\nimport apiService from \"../../../services/apiService\";\r\nimport { useTranslation } from 'next-i18next';\r\n\r\nconst ExpertiseTable = (props: any) => {\r\n    const { t } = useTranslation('common');\r\n    const [tabledata, setDataToTable] = useState([]);\r\n    const [, setLoading] = useState(false);\r\n    const [totalRows, setTotalRows] = useState(0);\r\n    const [perPage, setPerPage] = useState(10);\r\n    const [isModalShow, setModal] = useState(false);\r\n    const [selectExpertise, setSelectExpertise] = useState({});\r\n\r\n\r\n    const expertiseParams = {\r\n        sort: { title: \"asc\" },\r\n        limit: perPage,\r\n        page: 1,\r\n        query: {},\r\n    };\r\n\r\n    const columns = [\r\n        {\r\n            name: t(\"adminsetting.Expertise.Table.Title\"),\r\n            selector: \"title\",\r\n        },\r\n        {\r\n            name: t(\"adminsetting.Expertise.Table.Action\"),\r\n            selector: \"\",\r\n            cell: (d: any) => (\r\n                <div>\r\n                    <Link href=\"/adminsettings/[...routes]\" as={`/adminsettings/edit_expertise/${d._id}`}>\r\n\r\n                        <i className=\"icon fas fa-edit\" />\r\n\r\n                    </Link>\r\n                    &nbsp;\r\n                    <a onClick={(e) => userAction(d, e)}>\r\n                        <i className=\"icon fas fa-trash-alt\" />\r\n                    </a>{\" \"}\r\n                </div>\r\n            ),\r\n        },\r\n    ];\r\n\r\n    const getExpertiseData = async () => {\r\n        setLoading(true);\r\n        const response = await apiService.get(\"/expertise\", expertiseParams);\r\n        if (response && response.data && response.data.length > 0) {\r\n            setDataToTable(response.data);\r\n            setTotalRows(response.totalCount);\r\n            setLoading(false);\r\n        }\r\n    };\r\n\r\n    const handlePageChange = (page: any) => {\r\n        expertiseParams.limit = perPage;\r\n        expertiseParams.page = page;\r\n        getExpertiseData();\r\n    };\r\n\r\n    const handlePerRowsChange = async (newPerPage: any, page: any) => {\r\n        expertiseParams.limit = newPerPage;\r\n        expertiseParams.page = page;\r\n        setLoading(true);\r\n        const response = await apiService.get(\"/expertise\", expertiseParams);\r\n        if (response && response.data && response.data.length > 0) {\r\n            setDataToTable(response.data);\r\n            setPerPage(newPerPage);\r\n            setLoading(false);\r\n        }\r\n    };\r\n\r\n    const userAction = async (row: any, e: any) => {\r\n        e.preventDefault();\r\n        setSelectExpertise(row._id);\r\n        setModal(true);\r\n    };\r\n\r\n    const modalConfirm = async () => {\r\n        try {\r\n            await apiService.remove(`/expertise/${selectExpertise}`);\r\n            getExpertiseData();\r\n            setModal(false);\r\n            toast.success(t(\"adminsetting.Expertise.Table.expertiseDeletedSuccessfully\"));\r\n        } catch (error) {\r\n            toast.error(t(\"adminsetting.Expertise.Table.errorDeletingExpertise\"));\r\n        }\r\n    };\r\n\r\n    const modalHide = () => setModal(false);\r\n\r\n    useEffect(() => {\r\n        getExpertiseData();\r\n    }, []);\r\n\r\n    return (\r\n        <div>\r\n            <Modal show={isModalShow} onHide={modalHide}>\r\n                <Modal.Header closeButton>\r\n                    <Modal.Title> {t(\"adminsetting.Expertise.Table.DeleteExpertise\")}</Modal.Title>\r\n                </Modal.Header>\r\n                <Modal.Body>{t(\"adminsetting.Expertise.Table.AreyousurewanttodeletethisExpertise?\")}</Modal.Body>\r\n                <Modal.Footer>\r\n                    <Button variant=\"secondary\" onClick={modalHide}>\r\n                        {t(\"adminsetting.Expertise.Table.Cancel\")}\r\n                    </Button>\r\n                    <Button variant=\"primary\" onClick={modalConfirm}>\r\n                        {t(\"adminsetting.Expertise.Table.Yes\")}\r\n                    </Button>\r\n                </Modal.Footer>\r\n            </Modal>\r\n\r\n            <RKITable\r\n                columns={columns}\r\n                data={tabledata}\r\n                totalRows={totalRows}\r\n                pagServer={true}\r\n                handlePerRowsChange={handlePerRowsChange}\r\n                handlePageChange={handlePageChange}\r\n            />\r\n        </div>\r\n    );\r\n};\r\n\r\nexport default ExpertiseTable;\r\n"], "names": ["RKITable", "props", "t", "useTranslation", "paginationComponentOptions", "rowsPerPageText", "columns", "data", "totalRows", "resetPaginationToggle", "subheader", "subHeaderComponent", "handlePerRowsChange", "handlePageChange", "rowsPerPage", "defaultRowsPerPage", "selectableRows", "loading", "pagServer", "onSelectedRowsChange", "clearSelectedRows", "sortServer", "onSort", "persistTableHead", "sortFunction", "otherProps", "dataTableProps", "noDataComponent", "<PERSON><PERSON><PERSON><PERSON>", "dense", "paginationResetDefaultPage", "subHeader", "progressPending", "pagination", "paginationServer", "paginationPerPage", "paginationRowsPerPageOptions", "paginationTotalRows", "onChangeRowsPerPage", "onChangePage", "progressComponent", "CustomLoader", "sortIcon", "i", "className", "DataTable", "defaultProps", "ExpertiseTable", "tabledata", "setDataToTable", "useState", "setLoading", "setTotalRows", "perPage", "setPerPage", "isModalShow", "setModal", "selectExpertise", "setSelectExpertise", "expertiseParams", "sort", "title", "limit", "page", "query", "name", "selector", "cell", "div", "Link", "href", "as", "d", "_id", "a", "onClick", "userAction", "e", "getExpertiseData", "response", "apiService", "get", "length", "totalCount", "newPerPage", "row", "preventDefault", "modalConfirm", "remove", "toast", "success", "error", "modalHide", "useEffect", "Modal", "show", "onHide", "Header", "closeButton", "Title", "Body", "Footer", "<PERSON><PERSON>", "variant"], "sourceRoot": "", "ignoreList": []}