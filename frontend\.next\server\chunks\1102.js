"use strict";exports.id=1102,exports.ids=[1102],exports.modules={71102:(e,s,a)=>{a.a(e,async(e,r)=>{try{a.r(s),a.d(s,{default:()=>C});var i=a(8732),n=a(82015),l=a(7082),t=a(18597),o=a(83551),d=a(49481),m=a(59549),u=a(91353),c=a(66994),h=a(23579),g=a(44233),x=a.n(g),j=a(17906),p=a.n(j),A=a(11e3),f=a(19918),b=a.n(f),v=a(42893),_=a(63487),w=a(88751),y=e([v,_]);[v,_]=y.then?(await y)():y;let N={username:"",email:"",roles:"",password:"",confirm_password:"",institution:"",region:[],country:"",mobile_number:"",dial_code:"",enabled:"",firstname:"",lastname:"",position:""},C=e=>{let{t:s,i18n:a}=(0,w.useTranslation)("common"),[r,j]=(0,n.useState)(N),[f,y]=(0,n.useState)([]),[C,S]=(0,n.useState)([]),[L,P]=(0,n.useState)([]),[R,q]=(0,n.useState)([]),[E,G]=(0,n.useState)(null),M=(0,g.useRouter)().query.routes||[],I="de"===a.language?{title_de:"asc"}:{title:"asc"},T=a.language,[U,k]=(0,n.useState)(null),O={query:{},sort:I,limit:"~",languageCode:T},F=async e=>{let s=[{label:"Authenticated",value:"AUTHENTICATED"},{label:"Super Admin",value:"SUPER_ADMIN"},{label:"Platform Admins at Institutions",value:"PLATFORM_ADMIN"},{label:"NGO’s",value:"NGOS"},{label:"EMT User",value:"EMT"},{label:"INIG Stakeholder",value:"INIG_STAKEHOLDER"},{label:"Health Professional",value:"HEALTH_PROFESSIONAL"},{label:"General User",value:"GENERAL_USER"},{label:"EMT National Focal Point",value:"EMT_NATIONAL_FOCALPOINT"}];s=s.sort((e,s)=>e.label.localeCompare(s.label));let a=e.roles?.includes("SUPER_ADMIN")?s:s.filter(e=>"SUPER_ADMIN"!=e.value);y(a)},$=e=>{G(e),j(s=>({...s,username:e.username,firstname:e.firstname,lastname:e.lastname,position:e.position,email:e.email,roles:e.roles,institution:e.institution?e.institution._id:"",country:e.country?e.country._id:"",region:e.region?e.region.map((e,s)=>({label:e.title,value:e._id})):[],mobile_number:e.mobile_number?e.mobile_number:"",dial_code:e.dial_code?e.dial_code:"",enabled:e.enabled?"true":"false"}))},D=async()=>{if(M[1]){let e=await _.A.get(`users/${M[1]}`,O);e&&e._id&&$(e),K(e.country&&e.country._id)}},H=async()=>{let e=await _.A.get("institution",O);e&&e.data&&e.data.length>0&&S(e.data)},z=async()=>{let e=await _.A.get("/country",O);e&&e.data&&e.data.length>0&&q(e.data)};(0,n.useEffect)(()=>{B(),D(),H(),z()},[]);let B=async()=>{let e=await _.A.post("/users/getLoggedUser",{});e&&e.username&&(k(e),F(e))},Y=(0,n.useRef)(null),Z=e=>{j(s=>({...s,...e}))},K=async e=>{let s=[];if(e){let a=await _.A.get(`/country_region/${e}`,O);a&&a.data&&(s=a.data.map((e,s)=>({label:e.title,value:e._id}))).sort((e,s)=>e.label.localeCompare(s.label))}P(s)},V=e=>{let{name:s,value:a}=e.currentTarget;j(e=>({...e,[s]:a})),"country"===s&&(K(a),Z({region:[]}))},J=async(e,a)=>{e&&"username or email already exists"===e||e&&403===e.status?(v.default.error(s("adminsetting.user.form.usernameoremailalreadyexists")),window.scrollTo(0,0)):(v.default.success(s(a)),x().push("/adminsettings/users"))},Q=async(e,s)=>{let a,i;e.preventDefault();let n=s||r,l={username:n.username?.toLowerCase().trim(),firstname:n.firstname?.trim(),lastname:n.lastname,position:n.position,email:n.email?.toLowerCase(),roles:n.roles,institution:n.institution?n.institution:null,region:n.region?n.region.map((e,s)=>e.value):[],country:n.country?n.country:null,mobile_number:n.mobile_number?n.mobile_number:null,dial_code:n.dial_code?n.dial_code:null,enabled:"true"===n.enabled};E&&E._id?(""!==n.password&&(l.password=n.password),i="adminsetting.user.form.Updatedausersuccessfully",a=await _.A.patch(`/users/${E._id}`,l)):(i="adminsetting.user.form.Addedausersuccessfully",l.password=n.password,a=await _.A.post("/users",l)),J(a,i)},W=e=>e===r.password;return(0,i.jsx)(l.A,{className:"formCard",fluid:!0,children:(0,i.jsx)(t.A,{children:(0,i.jsx)(c.A,{onSubmit:Q,ref:Y,initialValues:r,enableReinitialize:!0,autoComplete:"off",children:(0,i.jsxs)(t.A.Body,{children:[(0,i.jsx)(o.A,{children:(0,i.jsx)(d.A,{children:(0,i.jsx)(t.A.Title,{children:E&&E._id?s("adminsetting.user.form.EditUser"):s("adminsetting.user.form.CreateUser")})})}),(0,i.jsx)("hr",{}),(0,i.jsx)(o.A,{className:"mb-3",children:(0,i.jsx)(d.A,{children:(0,i.jsxs)(m.A.Group,{children:[(0,i.jsx)(m.A.Label,{className:"required-field",children:s("adminsetting.user.form.Username")}),(0,i.jsx)(h.ks,{name:"username",id:"username",required:!0,value:r.username,validator:e=>""!==String(e||"").trim(),errorMessage:{validator:s("adminsetting.user.form.Youdon'thaveaUsername?")},onChange:V,autoComplete:"off"})]})})}),(0,i.jsxs)(o.A,{className:"mb-3",children:[(0,i.jsxs)(m.A.Group,{as:d.A,children:[(0,i.jsx)(m.A.Label,{className:"required-field",children:s("adminsetting.user.form.Firstname")}),(0,i.jsx)(h.ks,{name:"firstname",required:!0,validator:e=>""!==String(e||"").trim(),errorMessage:{validator:s("adminsetting.user.form.Pleaseenterafirstname")},id:"firstname",value:r.firstname,onChange:V})]}),(0,i.jsxs)(m.A.Group,{as:d.A,children:[(0,i.jsx)(m.A.Label,{children:s("adminsetting.user.form.Lastname")}),(0,i.jsx)(h.ks,{name:"lastname",id:"lastname",value:r.lastname,onChange:V})]})]}),(0,i.jsx)(o.A,{className:"mb-3",children:(0,i.jsx)(d.A,{children:(0,i.jsxs)(m.A.Group,{children:[(0,i.jsx)(m.A.Label,{children:s("adminsetting.user.form.Position")}),(0,i.jsx)(h.ks,{name:"position",id:"position",value:r.position,onChange:V})]})})}),(0,i.jsx)(o.A,{className:"mb-3",children:(0,i.jsx)(d.A,{children:(0,i.jsxs)(m.A.Group,{children:[(0,i.jsx)(m.A.Label,{className:"required-field",children:s("adminsetting.user.form.Email")}),(0,i.jsx)(h.ks,{name:"email",id:"email",type:"email",validator:p().isEmail,required:!0,errorMessage:{validator:s("adminsetting.user.form.Pleaseenteravalidemail")},value:r.email,onChange:V})]})})}),(0,i.jsx)(o.A,{className:"mb-3",children:(0,i.jsx)(d.A,{children:(0,i.jsxs)(m.A.Group,{children:[(0,i.jsx)(m.A.Label,{className:"required-field",children:s("adminsetting.user.form.Role")}),(0,i.jsxs)(h.s3,{required:!0,name:"roles",value:r.roles,errorMessage:s("adminsetting.user.form.PleaseselectaRole"),onChange:V,children:[(0,i.jsx)("option",{value:"",children:s("adminsetting.user.form.SelectRole")}),f.map((e,s)=>(0,i.jsx)("option",{value:e.value,children:e.label},s))]})]})})}),(0,i.jsx)(o.A,{className:"mb-3",children:(0,i.jsx)(d.A,{children:(0,i.jsxs)(m.A.Group,{children:[(0,i.jsx)(m.A.Label,{children:s("adminsetting.user.form.Organisation")}),(0,i.jsxs)(h.s3,{name:"institution",value:r.institution,errorMessage:s("adminsetting.user.form.PleaseselectaOrganisation."),onChange:V,children:[(0,i.jsx)("option",{value:"",children:s("adminsetting.user.form.SelectOrganisation")}),C.map((e,s)=>(0,i.jsx)("option",{value:e._id,children:e.title},s))]})]})})}),(0,i.jsx)(o.A,{className:"mb-3",children:(0,i.jsx)(d.A,{children:(0,i.jsxs)(m.A.Group,{children:[(0,i.jsx)(m.A.Label,{children:s("adminsetting.user.form.Country")}),(0,i.jsxs)(h.s3,{name:"country",id:"country",value:r.country,onChange:V,children:[(0,i.jsx)("option",{value:"",children:s("adminsetting.user.form.SelectCountry")}),R.map((e,s)=>(0,i.jsx)("option",{value:e._id,children:e.title},e._id))]})]})})}),(0,i.jsx)(o.A,{className:"mb-3",children:(0,i.jsx)(d.A,{children:(0,i.jsxs)(m.A.Group,{children:[(0,i.jsx)(m.A.Label,{children:s("adminsetting.user.form.Region")}),(0,i.jsx)(A.MultiSelect,{overrideStrings:{selectSomeItems:s("SelectRegions"),allItemsAreSelected:s("adminsetting.user.form.AllRegionsareSelected")},options:L,value:r.region,onChange:e=>{j(s=>({...s,region:e}))},className:"region",labelledBy:s("SelectRegions")})]})})}),(0,i.jsxs)(o.A,{className:"mb-3",children:[(0,i.jsx)(d.A,{xs:6,children:(0,i.jsxs)(m.A.Group,{children:[(0,i.jsx)(m.A.Label,{children:s("adminsetting.user.form.PhoneNumber")}),(0,i.jsxs)(h.s3,{name:"dial_code",id:"dial_code",type:"number",value:r.dial_code,onChange:V,children:[(0,i.jsx)("option",{value:"",children:s("adminsetting.user.form.DialCode")}),R.map((e,s)=>(0,i.jsx)("option",{value:e.dial_code,children:`(${e.dial_code}) ${e.title}`},e._id))]})]})}),(0,i.jsx)(d.A,{children:(0,i.jsxs)(m.A.Group,{children:[(0,i.jsx)(m.A.Label,{children:"\xa0"}),(0,i.jsx)(h.ks,{type:"number",name:"mobile_number",id:"mobile_number",value:r.mobile_number,onChange:V})]})})]}),(0,i.jsx)(o.A,{className:"mb-3",children:(0,i.jsx)(d.A,{children:(0,i.jsx)(m.A.Group,{children:E&&E._id?(0,i.jsxs)(i.Fragment,{children:[(0,i.jsx)(m.A.Label,{children:s("adminsetting.user.form.Password")}),(0,i.jsx)(h.ks,{name:"password",id:"password",type:"password",pattern:"^(?=.*?[A-Z])(?=.*?[a-z])(?=.*?[0-9])(?=.*?[#?!@$%^&*-]).{8,}$",errorMessage:{pattern:s("adminsetting.user.form.Passwordshouldcontainatleast8characters,withatleastonedigit,oneletterinuppercase&onespecialcharacter")},value:r.password,onChange:V})]}):(0,i.jsxs)(i.Fragment,{children:[(0,i.jsx)(m.A.Label,{className:"required-field",children:s("adminsetting.user.form.Password")}),(0,i.jsx)(h.ks,{name:"password",id:"password",type:"password",required:!0,pattern:"^(?=.*?[A-Z])(?=.*?[a-z])(?=.*?[0-9])(?=.*?[#?!@$%^&*-]).{8,}$",errorMessage:{required:s("adminsetting.user.form.Passwordisrequired"),pattern:s("adminsetting.user.form.Passwordshouldcontainatleast8characters,withatleastonedigit,oneletterinuppercase&onespecialcharacter")},value:r.password,onChange:V})]})})})}),(0,i.jsx)(o.A,{className:"mb-3",children:(0,i.jsx)(d.A,{children:(0,i.jsx)(m.A.Group,{children:E&&E._id?(0,i.jsxs)(i.Fragment,{children:[(0,i.jsx)(m.A.Label,{children:s("adminsetting.user.form.ConfirmPassword")}),(0,i.jsx)(h.ks,{name:"confirm_password",id:"confirm_password",type:"password",validator:W,errorMessage:{required:s("adminsetting.user.form.Confirmpasswordisrequired"),validator:s("adminsetting.user.form.Passworddoesnotmatch")},value:r.confirm_password,onChange:V})]}):(0,i.jsxs)(i.Fragment,{children:[(0,i.jsx)(m.A.Label,{className:"required-field",children:s("adminsetting.user.form.ConfirmPassword")}),(0,i.jsx)(h.ks,{name:"confirm_password",id:"confirm_password",type:"password",required:!0,validator:W,errorMessage:{required:s("adminsetting.user.form.Confirmpasswordisrequired"),validator:s("adminsetting.user.form.Passworddoesnotmatch")},value:r.confirm_password,onChange:V})]})})})}),(0,i.jsx)(o.A,{className:"mb-3",children:(0,i.jsx)(d.A,{children:(0,i.jsxs)(m.A.Group,{children:[(0,i.jsx)("label",{children:s("adminsetting.user.form.Enabled")}),(0,i.jsxs)(h.sx.RadioGroup,{name:"enabled",errorMessage:s("adminsetting.user.form.Itisrequired"),valueSelected:r.enabled,onChange:V,children:[(0,i.jsx)(h.sx.RadioItem,{id:"yes",label:"Yes",value:"true"}),(0,i.jsx)(h.sx.RadioItem,{id:"no",label:"No",value:"false"})]})]})})}),(0,i.jsx)(o.A,{className:"my-4",children:(0,i.jsxs)(d.A,{children:[(0,i.jsx)(u.A,{className:"me-2",type:"submit",variant:"primary",children:s("adminsetting.user.form.Submit")}),(0,i.jsx)(u.A,{className:"me-2",onClick:()=>{j(N),window.scrollTo(0,0)},variant:"info",children:s("adminsetting.user.form.Reset")}),(0,i.jsx)(b(),{href:"/adminsettings/[...routes]",as:"/adminsettings/users",children:(0,i.jsx)(u.A,{variant:"secondary",children:s("adminsetting.user.form.Cancel")})})]})})]})})})})};r()}catch(e){r(e)}})}};