(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[9660],{18690:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>p});var r=a(37876);a(14232);var s=a(56970),n=a(37784),i=a(32890),o=a(46864),c=a(29719),d=a(54001),l=a(65910);let p=e=>{let t=(0,o.canViewDiscussionUpdate)(()=>(0,r.jsx)(l.default,{routeData:e.routeData}));return(0,r.jsx)(r.Fragment,{children:(0,r.jsx)(s.A,{children:(0,r.jsxs)(n.A,{className:"projectAccordion",xs:12,children:[(0,r.jsx)(i.A,{children:(0,r.jsx)(c.default,{project:e.projectData})}),(0,r.jsx)(i.A,{children:(0,r.jsx)(t,{})}),(0,r.jsx)(i.A,{children:(0,r.jsx)(d.default,{routeData:e.routeData})})]})})})}},29719:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>u});var r=a(37876),s=a(14232),n=a(11041),i=a(21772),o=a(10841),c=a.n(o),d=a(32890),l=a(29335),p=a(31753);let u=e=>{var t;let a="DD-MM-YYYY HH:mm:ss",{t:o}=(0,p.Bd)("common"),[u,m]=(0,s.useState)(!0);return(0,r.jsx)(r.Fragment,{children:(0,r.jsxs)(d.A.Item,{eventKey:"0",children:[(0,r.jsxs)(d.A.Header,{onClick:()=>m(!u),children:[(0,r.jsx)("div",{className:"cardTitle",children:o("ProjectDetails")}),(0,r.jsx)("div",{className:"cardArrow",children:u?(0,r.jsx)(i.g,{icon:n.QLR,color:"#fff"}):(0,r.jsx)(i.g,{icon:n.EZy,color:"#fff"})})]}),(0,r.jsx)(d.A.Body,{children:(0,r.jsxs)(l.A.Text,{className:"projectDetails ps-0",children:[(0,r.jsxs)("p",{children:[(0,r.jsx)("b",{children:o("Status")}),":",(0,r.jsxs)("span",{children:[" ",e.project.status?e.project.status.title:""]})]}),(0,r.jsxs)("p",{children:[(0,r.jsx)("b",{children:o("Created")}),":",(0,r.jsxs)("span",{children:[" ",e.project.created_at?c()(e.project.created_at).format(a):null," "]})]}),(0,r.jsxs)("p",{children:[(0,r.jsx)("b",{children:o("EndDate")}),":",(0,r.jsxs)("span",{children:[" ",e.project.end_date?c()(e.project.end_date).format("DD-MM-YYYY"):null]})]}),(0,r.jsxs)("p",{children:[(0,r.jsx)("b",{children:o("LastModified")}),":",(0,r.jsxs)("span",{children:[" ",e.project.updated_at?c()(e.project.updated_at).format(a):null," "]})]}),(0,r.jsxs)("p",{children:[(0,r.jsx)("b",{children:o("WeblinktoProject")}),":",(0,r.jsx)("span",{children:(null==(t=e.project)?void 0:t.website)&&(0,r.jsx)("a",{href:e.project.website,target:"_blank",children:e.project.website})})]})]})})]})})}},34381:(e,t,a)=>{"use strict";a.d(t,{A:()=>l});var r=a(37876),s=a(14232),n=a(48230),i=a.n(n),o=a(50749),c=a(53718),d=a(31753);let l=e=>{let{t}=(0,d.Bd)("common"),{type:a,id:n}=e,[l,p]=(0,s.useState)([]),[u,m]=(0,s.useState)(!1),[j,h]=(0,s.useState)(0),[x,g]=(0,s.useState)(10),[_]=(0,s.useState)(!1),f={sort:{created_at:"asc"},limit:x,page:1,query:{}},D=[{name:t("Title"),selector:"title",cell:e=>e&&e.title&&e._id?(0,r.jsx)(i(),{href:"/vspace/[...routes]",as:"/vspace/show/".concat(e._id),children:e.title}):""},{name:t("Owner"),selector:"users",cell:e=>e&&e.user&&e.user.firstname?"".concat(e.user.firstname," ").concat(e.user.lastname):""},{name:t("PublicPrivate"),selector:"visibility",cell:e=>e&&e.visibility?"Public":"Private"},{name:t("NumberofMembers"),selector:"members",cell:e=>e&&e.members?e.members.length:"-"}],v=async e=>{m(!0);let t=await c.A.get("stats/get".concat(a,"WithVspace/").concat(n),f);t&&("Operation"===a?p(t.operation):p(t.project),h(t.totalCount),m(!1))},M=async(e,t)=>{f.limit=e,f.page=t,m(!0);let r=await c.A.get("stats/get".concat(a,"WithVspace/").concat(n),f);r&&("Operation"===a?p(r.operation):p(r.project),g(e),m(!1))};return(0,s.useEffect)(()=>{v(f)},[]),(0,r.jsx)("div",{children:(0,r.jsx)(o.A,{columns:D,data:l,totalRows:j,loading:u,resetPaginationToggle:_,handlePerRowsChange:M,handlePageChange:e=>{f.limit=x,f.page=e,v(f)}})})}},46864:(e,t,a)=>{"use strict";a.r(t),a.d(t,{canAddProject:()=>i,canAddProjectForm:()=>o,canEditProject:()=>c,canEditProjectForm:()=>d,canViewDiscussionUpdate:()=>l,default:()=>p});var r=a(37876);a(14232);var s=a(8178),n=a(59626);let i=(0,s.A)({authenticatedSelector:e=>!!e.permissions&&!!e.permissions.project&&!!e.permissions.project["create:any"],wrapperDisplayName:"CanAddProject"}),o=(0,s.A)({authenticatedSelector:e=>!!e.permissions&&!!e.permissions.project&&!!e.permissions.project["create:any"],wrapperDisplayName:"CanAddProjectForm",FailureComponent:()=>(0,r.jsx)(n.default,{})}),c=(0,s.A)({authenticatedSelector:(e,t)=>{if(e.permissions&&e.permissions.project){if(e.permissions.project["update:any"])return!0;else if(e.permissions.project["update:own"]&&t.project&&t.project.user&&t.project.user._id===e.user._id)return!0}return!1},wrapperDisplayName:"CanEditProject"}),d=(0,s.A)({authenticatedSelector:(e,t)=>{if(e.permissions&&e.permissions.project){if(e.permissions.project["update:any"])return!0;else if(e.permissions.project["update:own"]&&t.project&&t.project.user&&t.project.user._id===e.user._id)return!0}return!1},wrapperDisplayName:"CanEditProjectForm",FailureComponent:()=>(0,r.jsx)(n.default,{})}),l=(0,s.A)({authenticatedSelector:e=>!!e.permissions&&!!e.permissions.update&&!!e.permissions.update["read:any"],wrapperDisplayName:"CanViewDiscussionUpdate"}),p=i},50749:(e,t,a)=>{"use strict";a.d(t,{A:()=>c});var r=a(37876);a(14232);var s=a(89773),n=a(31753),i=a(5507);function o(e){let{t}=(0,n.Bd)("common"),a={rowsPerPageText:t("Rowsperpage")},{columns:o,data:c,totalRows:d,resetPaginationToggle:l,subheader:p,subHeaderComponent:u,handlePerRowsChange:m,handlePageChange:j,rowsPerPage:h,defaultRowsPerPage:x,selectableRows:g,loading:_,pagServer:f,onSelectedRowsChange:D,clearSelectedRows:v,sortServer:M,onSort:w,persistTableHead:y,sortFunction:A,...P}=e,S={paginationComponentOptions:a,noDataComponent:t("NoData"),noHeader:!0,columns:o,data:c||[],dense:!0,paginationResetDefaultPage:l,subHeader:p,progressPending:_,subHeaderComponent:u,pagination:!0,paginationServer:f,paginationPerPage:x||10,paginationRowsPerPageOptions:h||[10,15,20,25,30],paginationTotalRows:d,onChangeRowsPerPage:m,onChangePage:j,selectableRows:g,onSelectedRowsChange:D,clearSelectedRows:v,progressComponent:(0,r.jsx)(i.A,{}),sortIcon:(0,r.jsx)("i",{className:"sort-icon fas fa-exchange-alt"}),sortServer:M,onSort:w,sortFunction:A,persistTableHead:y,className:"rki-table"};return(0,r.jsx)(s.Ay,{...S})}o.defaultProps={subHeader:!1,pagination:!0,totalRows:null,pagServer:!0,onSelectedRowsChange:null,clearSelectedRows:!1,sortServer:!1,persistTableHead:!1};let c=o},54001:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>l});var r=a(37876),s=a(14232),n=a(11041),i=a(21772),o=a(32890),c=a(34381),d=a(31753);let l=e=>{var t,a;let{t:l}=(0,d.Bd)("common"),[p,u]=(0,s.useState)(!1);return(0,r.jsx)(r.Fragment,{children:(0,r.jsxs)(o.A.Item,{eventKey:"0",children:[(0,r.jsxs)(o.A.Header,{onClick:()=>u(!p),children:[(0,r.jsx)("div",{className:"cardTitle",children:l("LinkedVirtualSpace")}),(0,r.jsx)("div",{className:"cardArrow",children:p?(0,r.jsx)(i.g,{icon:n.EZy,color:"#fff"}):(0,r.jsx)(i.g,{icon:n.QLR,color:"#fff"})})]}),(0,r.jsx)(o.A.Body,{children:(0,r.jsx)(c.A,{id:(null==(a=e.routeData)||null==(t=a.routes)?void 0:t[1])||"",type:"Project",vspaceData:[],vspaceDataLoading:!1,vspaceDataTotalRows:0,vspaceDataPerPage:10,vspaceDataCurrentPage:1})})]})})}},65910:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>l});var r=a(37876),s=a(14232),n=a(32890),i=a(21772),o=a(11041),c=a(31753),d=a(48477);let l=e=>{var t;let{t:a}=(0,c.Bd)("common"),[l,p]=(0,s.useState)(!0);return(0,r.jsxs)(n.A.Item,{eventKey:"2",children:[(0,r.jsxs)(n.A.Header,{onClick:()=>p(!l),children:[(0,r.jsx)("div",{className:"cardTitle",children:a("Discussions")}),(0,r.jsx)("div",{className:"cardArrow",children:l?(0,r.jsx)(i.g,{icon:o.QLR,color:"#fff"}):(0,r.jsx)(i.g,{icon:o.EZy,color:"#fff"})})]}),(0,r.jsx)(n.A.Body,{children:(0,r.jsx)(d.A,{type:"project",id:(null==e||null==(t=e.routeData)?void 0:t.routes)?e.routeData.routes[1]:""})})]})}},84135:function(e,t,a){(function(e){"use strict";function t(e,t,a,r){var s={m:["eine Minute","einer Minute"],h:["eine Stunde","einer Stunde"],d:["ein Tag","einem Tag"],dd:[e+" Tage",e+" Tagen"],w:["eine Woche","einer Woche"],M:["ein Monat","einem Monat"],MM:[e+" Monate",e+" Monaten"],y:["ein Jahr","einem Jahr"],yy:[e+" Jahre",e+" Jahren"]};return t?s[a][0]:s[a][1]}e.defineLocale("de",{months:"Januar_Februar_M\xe4rz_April_Mai_Juni_Juli_August_September_Oktober_November_Dezember".split("_"),monthsShort:"Jan._Feb._M\xe4rz_Apr._Mai_Juni_Juli_Aug._Sep._Okt._Nov._Dez.".split("_"),monthsParseExact:!0,weekdays:"Sonntag_Montag_Dienstag_Mittwoch_Donnerstag_Freitag_Samstag".split("_"),weekdaysShort:"So._Mo._Di._Mi._Do._Fr._Sa.".split("_"),weekdaysMin:"So_Mo_Di_Mi_Do_Fr_Sa".split("_"),weekdaysParseExact:!0,longDateFormat:{LT:"HH:mm",LTS:"HH:mm:ss",L:"DD.MM.YYYY",LL:"D. MMMM YYYY",LLL:"D. MMMM YYYY HH:mm",LLLL:"dddd, D. MMMM YYYY HH:mm"},calendar:{sameDay:"[heute um] LT [Uhr]",sameElse:"L",nextDay:"[morgen um] LT [Uhr]",nextWeek:"dddd [um] LT [Uhr]",lastDay:"[gestern um] LT [Uhr]",lastWeek:"[letzten] dddd [um] LT [Uhr]"},relativeTime:{future:"in %s",past:"vor %s",s:"ein paar Sekunden",ss:"%d Sekunden",m:t,mm:"%d Minuten",h:t,hh:"%d Stunden",d:t,dd:t,w:t,ww:"%d Wochen",M:t,MM:t,y:t,yy:t},dayOfMonthOrdinalParse:/\d{1,2}\./,ordinal:"%d.",week:{dow:1,doy:4}})})(a(10841))}}]);
//# sourceMappingURL=9660-c486b44d7ead3c88.js.map