{"version": 3, "file": "static/chunks/pages/vspace/[...routes]-b1ddc33fb65fdf6d.js", "mappings": "+NA0CA,MA9Be,KAEb,IAAMA,EAAcC,CADLC,EAAAA,EAAAA,SAAAA,CAASA,EA6BXC,CA5BcC,KA4BRD,CA5BcH,CA4Bd,KA5BoB,EAAI,EAAE,CACvCK,EAAsBC,CAAAA,EAAAA,EAAAA,gBAAAA,CAAgBA,CAAC,IAAM,UAACC,EAAAA,OAAIA,CAAAA,CAACP,OAAQA,KAEjE,OAAQA,CAAM,CAAC,EAAE,EACf,IAAK,SACH,MAAO,UAACK,EAAAA,CAAAA,EAEV,KAAK,OACH,MAAO,UAACE,EAAAA,OAAIA,CAAAA,CAACP,OAAQA,GACvB,KAAK,OACH,MAAO,UAACQ,EAAAA,OAAUA,CAAAA,CAACR,OAAQA,GAC7B,KAAK,SACH,MAAO,UAACS,EAAAA,OAAaA,CAAAA,CAACT,OAAQA,GAChC,KAAK,eACH,MAAO,UAACU,EAAAA,OAAmBA,CAAAA,CAACV,OAAQA,GACtC,SACE,OAAO,IACX,CACF,kMC8DA,MAlFqB,IAiBjB,GAAM,CAACW,EAASC,EAAW,CAAGC,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAC,IACjC,CAACC,EAAYC,EAAc,CAAGF,CAAAA,CAgEbG,CAhEaH,CAgEZ,CAhEYA,QAAAA,CAAQA,CAjBlB,CACtBI,MAAO,GACPC,YAAa,GACbC,UAAW,KACXC,QAAS,KACTC,WAAY,GACZC,WAAY,GACZC,OAAQ,EAAE,CACVC,SAAS,EACTC,cAAe,GACfC,WAAY,EAAE,CACdC,WAAY,EAAE,CACdC,QAAS,EAAE,CACXC,QAAS,EAAE,CACXC,SAAU,EACd,GAGM,CAACC,EAAMC,EAAQ,CAAGnB,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAC,IACvB,GAAEoB,CAAC,CAAE,CAAGC,CAAAA,EAAAA,EAAAA,EAAAA,CAAcA,CAAC,UAE3BlC,EADSE,CAAAA,EAAAA,EAAAA,SAAAA,CAASA,GACGE,KAAK,CAACJ,MAAM,EAAI,EAAE,CAE7CmC,CAAAA,EAAAA,EAAAA,SAAAA,CAASA,CAAC,KACNC,GACJ,EAAG,EAAE,EACL,IAAMA,EAAY,UACd,IAAMC,EAAiB,MAAMC,EAAAA,CAAUA,CAACC,GAAG,CAAC,WAAqB,OAAVvC,CAAM,CAAC,EAAE,GAC1DwC,EAAQ,MAAMF,EAAAA,CAAUA,CAACC,GAAG,CAAC,UAAoB,OAAVvC,CAAM,CAAC,EAAE,GAClDqC,IACAzB,EAAWyB,EAAef,QADV,EACoB,EACpCP,EAAcsB,IAEdG,GACAR,EAAQQ,EADD,QACe,CAE9B,EACMC,EAAyB,UAC3B3B,EAAWQ,UAAU,EAAG,EAExBoB,MADqBC,IAAAA,IAAM,CAAC7B,EAAW8B,WAAW,CAAE,CAAEC,IAAK7C,CAAM,CAAC,EAAE,IAC5Cc,EAAW8B,WAAW,CAACE,IAAI,CAAC9C,CAAM,CAAC,EAAE,EAC7D,IAAM+C,EAAiB,MAAMT,EAAAA,CAAUA,CAACU,IAAI,CAAC,qCAAoD,OAAflC,EAAW+B,GAAG,EAAI/B,GAChGiC,GAAkBA,EAAeF,GAAG,EAAE,CACtCjC,GAAW,GACXqC,EAAAA,EAAKA,CAACC,OAAO,CAACjB,EAAE,iCAChB9B,IAAAA,IAAW,CAAC,YAEE,kBAAlB4C,GAAsCE,EAAAA,EAAKA,CAACE,KAAK,CAAClB,EAAE,uBACxD,EAQA,MACI,UAACmB,MAAAA,UACG,UAACC,EAAAA,CAAGA,CAAAA,CAACC,UAAU,gBACX,WAACC,EAAAA,CAAGA,CAAAA,WACA,UAACH,MAAAA,UAAI,sCACL,UAACI,IAAAA,UAAGzB,IAAS,uDAAoD,UAACyB,IAAAA,UAAG1C,EAAWG,KAAK,GACrF,UAACwC,KAAAA,CAAAA,GACD,UAACC,EAAAA,CAAMA,CAAAA,CACHC,SAAUhD,EACV2C,UAAU,OACVM,KAAK,SACLC,QAAQ,UACRC,QAASrB,WAERR,EAAE,YAEP,UAACyB,EAAAA,CAAMA,CAAAA,CAACC,SAAUhD,EAAS2C,UAAU,OAAOO,QAAQ,OAAOC,QAtB9C,CAsBuDC,IArBhFnD,EAAW,IACXqC,EAAAA,EAAKA,CAACE,KAAK,CAAClB,EAAE,iCACd9B,IAAAA,IAAW,CAAC,UAChB,WAmBqB8B,EAAE,mBAM3B,mBC3FA,4CACA,sBACA,WACA,OAAe,EAAQ,KAA2C,CAClE,EACA,SAFsB", "sources": ["webpack://_N_E/./pages/vspace/[...routes].tsx", "webpack://_N_E/./pages/vspace/AcceptRequestVspace.tsx", "webpack://_N_E/?ef4b"], "sourcesContent": ["//Import Library\r\nimport { useRouter } from 'next/router';\r\n\r\n//Import services/components\r\nimport Form from './Form';\r\nimport ViewVSpace from './View';\r\nimport { canAddVspaceForm } from \"./permission\";\r\nimport ManageMembers from './ManageMembers';\r\nimport AcceptRequestVspace from './AcceptRequestVspace';\r\nimport { serverSideTranslations } from 'next-i18next/serverSideTranslations';\r\n\r\n\r\nconst Router = () => {\r\n  const router = useRouter()\r\n  const routes: any = router.query.routes || []\r\n  const CanAccessCreateForm = canAddVspaceForm(() => <Form routes={routes} />)\r\n\r\n  switch (routes[0]) {\r\n    case 'create':\r\n      return <CanAccessCreateForm />\r\n\r\n    case 'edit':\r\n      return <Form routes={routes} />\r\n    case 'show':\r\n      return <ViewVSpace routes={routes} />\r\n    case 'manage':\r\n      return <ManageMembers routes={routes} />\r\n    case \"acceptvspace\" :\r\n      return <AcceptRequestVspace routes={routes} />\r\n    default:\r\n      return null;\r\n  }\r\n}\r\n\r\nexport async function getServerSideProps({ locale }: { locale: string }) {\r\n  return {\r\n    props: {\r\n      ...(await serverSideTranslations(locale, ['common'])),\r\n    },\r\n  }\r\n}\r\n\r\nexport default Router\r\n", "//Import Library\r\nimport Router, { useRouter } from \"next/router\";\r\nimport { Button, Col, Row } from \"react-bootstrap\";\r\n\r\nimport toast from 'react-hot-toast';\r\nimport _ from \"lodash\";\r\nimport { useEffect, useState } from \"react\";\r\n\r\n//Import services/components\r\nimport apiService from \"../../services/apiService\";\r\nimport { useTranslation } from 'next-i18next';\r\n\r\nconst AcceptVspace = (_props: any) => {\r\n    const initialState: any = {\r\n        title: \"\",\r\n        description: \"\",\r\n        startDate: null,\r\n        endDate: null,\r\n        searchData: \"\",\r\n        visibility: true,\r\n        images: [],\r\n        checked: false,\r\n        file_category: \"\",\r\n        nonMembers: [],\r\n        images_src: [],\r\n        members: [],\r\n        doc_src: [],\r\n        document: [],\r\n    };\r\n    const [disable, setDisable] = useState(false);\r\n    const [vspacedata, setVspaceData] = useState(initialState);\r\n    const [user, setUser] = useState(\"\");\r\n        const { t } = useTranslation('common');\r\n    const router = useRouter();\r\n    const routes: any = router.query.routes || [];\r\n\r\n    useEffect(() => {\r\n        GetvSpace();\r\n    }, []);\r\n    const GetvSpace = async () => {\r\n        const vspaceResponse = await apiService.get(`/vspace/${routes[1]}`);\r\n        const _user = await apiService.get(`/users/${routes[3]}`);\r\n        if (vspaceResponse) {\r\n            setDisable(vspaceResponse.visibility);\r\n            setVspaceData(vspaceResponse);\r\n        }\r\n        if (_user) {\r\n            setUser(_user.username);\r\n        }\r\n    };\r\n    const acceptedRequesrtVspace = async () => {\r\n        vspacedata.visibility = true;\r\n        const isSubscribed = _.find(vspacedata.subscribers, { _id: routes[3] });\r\n        isSubscribed == null && vspacedata.subscribers.push(routes[3]);\r\n        const Updateresponse = await apiService.post(`/vspace/acceptSubscriptionRequest/${vspacedata._id}`, vspacedata);\r\n        if (Updateresponse && Updateresponse._id) {\r\n            setDisable(true);\r\n            toast.success(t(\"Vspaceissuccessfullyaccepted\"));\r\n            Router.push(\"/vspace\");\r\n        }\r\n        Updateresponse == \"Not authorized\" && toast.error(t(\"Youarenotauthorized\"));\r\n    };\r\n\r\n    const declineRequestVspace = () => {\r\n        setDisable(true);\r\n        toast.error(t(\"VspaceissuccessfullyDeclined\"));\r\n        Router.push(\"/vspace\");\r\n    };\r\n\r\n    return (\r\n        <div>\r\n            <Row className=\"my-4\">\r\n                <Col>\r\n                    <div>Welcome to Robert Koch Institut !</div>\r\n                    <b>{user}</b> has requested to access your private virtual space <b>{vspacedata.title}</b>\r\n                    <br />\r\n                    <Button\r\n                        disabled={disable}\r\n                        className=\"me-2\"\r\n                        type=\"submit\"\r\n                        variant=\"primary\"\r\n                        onClick={acceptedRequesrtVspace}\r\n                    >\r\n                        {t(\"Accept\")}\r\n                    </Button>\r\n                    <Button disabled={disable} className=\"me-2\" variant=\"info\" onClick={declineRequestVspace}>\r\n                        {t(\"Decline\")}\r\n                    </Button>\r\n                </Col>\r\n            </Row>\r\n        </div>\r\n    );\r\n};\r\n\r\nexport default AcceptVspace;\r\n", "\n    (window.__NEXT_P = window.__NEXT_P || []).push([\n      \"/vspace/[...routes]\",\n      function () {\n        return require(\"private-next-pages/vspace/[...routes].tsx\");\n      }\n    ]);\n    if(module.hot) {\n      module.hot.dispose(function () {\n        window.__NEXT_P.push([\"/vspace/[...routes]\"])\n      });\n    }\n  "], "names": ["routes", "router", "useRouter", "Router", "query", "CanAccessCreateForm", "canAddVspaceForm", "Form", "ViewVSpace", "ManageMembers", "AcceptRequestVspace", "disable", "setDisable", "useState", "vspacedata", "setVspaceData", "AcceptVspace", "title", "description", "startDate", "endDate", "searchData", "visibility", "images", "checked", "file_category", "nonMembers", "images_src", "members", "doc_src", "document", "user", "setUser", "t", "useTranslation", "useEffect", "GetvSpace", "vspaceResponse", "apiService", "get", "_user", "acceptedRequesrtVspace", "isSubscribed", "_", "subscribers", "_id", "push", "Updateresponse", "post", "toast", "success", "error", "div", "Row", "className", "Col", "b", "br", "<PERSON><PERSON>", "disabled", "type", "variant", "onClick", "declineRequestVspace"], "sourceRoot": "", "ignoreList": []}