(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[7727],{15641:(e,t,r)=>{"use strict";r.d(t,{A:()=>n});var s=r(37876);r(14232);var a=r(62945);let n=e=>{let{name:t="Marker",id:r="",countryId:n="",type:l,icon:o,position:i,onClick:c,title:u,draggable:p=!1}=e;return i&&"number"==typeof i.lat&&"number"==typeof i.lng?(0,s.jsx)(a.pH,{position:i,icon:o,title:u||t,draggable:p,onClick:e=>{c&&c({name:t,id:r,countryId:n,type:l,position:i},{position:i,getPosition:()=>i},e)}}):null}},23103:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>d});var s=r(37876),a=r(14232),n=r(49589),l=r(56970),o=r(37784),i=r(12697),c=r(29504),u=r(31753),p=r(53718);let d=e=>{let{filterText:t,onFilter:r,onFilterStatusChange:d,onClear:y,filterStatus:m}=e,[f,h]=(0,a.useState)([]),{t:g}=(0,u.Bd)("common"),j=async e=>{let t=await p.A.get("/projectstatus",e);t&&Array.isArray(t.data)&&h(t.data)};return(0,a.useEffect)(()=>{j({query:{},sort:{title:"asc"}})},[]),(0,s.jsx)(n.A,{fluid:!0,className:"p-0",children:(0,s.jsxs)(l.A,{children:[(0,s.jsx)(o.A,{xs:6,className:"ps-0 align-self-end mb-3",children:(0,s.jsx)(i.A,{type:"text",className:"searchInput",placeholder:g("vspace.Search"),"aria-label":"Search",value:t,onChange:r})}),(0,s.jsx)(o.A,{children:(0,s.jsx)(c.A,{children:(0,s.jsxs)(c.A.Group,{as:l.A,controlId:"statusFilter",children:[(0,s.jsx)(c.A.Label,{column:!0,sm:"3",lg:"2",children:"Status"}),(0,s.jsx)(o.A,{className:"ps-0 pe-1",children:(0,s.jsxs)(i.A,{as:"select","aria-label":"Status",onChange:d,value:m,children:[(0,s.jsx)("option",{value:"",children:"All"}),f.map((e,t)=>(0,s.jsx)("option",{value:e._id,children:e.title},t))]})})]})})})]})})}},37669:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>h});var s=r(37876),a=r(48230),n=r.n(a),l=r(14232),o=r(82851),i=r.n(o),c=r(89099),u=r(50749),p=r(23103),d=r(53718),y=r(31753);let m="partner_institutions.partner_country",f=e=>{let{partner_institutions:t}=e;return t&&t.length>0?(0,s.jsx)("ul",{children:t.map((e,t)=>{if(e.partner_country){var r;return(0,s.jsx)("li",{children:(0,s.jsx)(n(),{href:"/country/[...routes]",as:"/country/show/".concat(null==(r=e.partner_country)?void 0:r._id),children:e.partner_country.title})},t)}})}):null},h=function(e){let t=(0,c.useRouter)(),{t:r}=(0,y.Bd)("common"),{setProjects:a,selectedRegions:o}=e,[h,g]=l.useState(""),[j,_]=l.useState(""),[x,T]=l.useState(!1),[A,v]=(0,l.useState)([]),[b,w]=(0,l.useState)(!1),[C,S]=(0,l.useState)(0),[k,P]=(0,l.useState)(10),[q,N]=(0,l.useState)(1),[E,R]=(0,l.useState)(null),F={sort:{created_at:"desc"},lean:!0,limit:k,page:1,query:{},populate:[{path:"area_of_work",select:"title"},{path:m,select:"coordinates title"},{path:"status",select:"title"}],select:"-website -description -start_date -end_date -country -region -partner_institutions.partner_region -partner_institutions.partner_institution -institution_invites -vspace -vspace_visibility -user -created_at -updated_at"},[I,D]=(0,l.useState)(F),H=[{name:r("Project(s)"),selector:"title",sortable:!0,cell:e=>(0,s.jsx)(n(),{href:"/project/[...routes]",as:"/project/show/".concat(null==e?void 0:e._id),children:e.title})},{name:r("Country"),selector:"country",sortable:!0,cell:e=>(0,s.jsx)(f,{partner_institutions:e.partner_institutions})},{name:r("AreaofWork"),selector:"area_of_work",cell:e=>e.area_of_work?e.area_of_work.map(e=>e.title).join(", "):""},{name:r("Status"),selector:"status",sortable:!0,cell:e=>e.status&&e.status.title?e.status.title:""},{name:r("Fundedby"),selector:"funded_by",sortable:!0}],B=async e=>{w(!0),t.query&&t.query.country&&(e.query[m]=[t.query.country]),null===o?delete e.query["partner_institutions.world_region"]:0===o.length?e.query["partner_institutions.world_region"]=["__NO_MATCH__"]:e.query["partner_institutions.world_region"]=o;let r=await d.A.get("/project",e);r&&Array.isArray(r.data)&&(v(r.data),a(r.data),S(r.totalCount)),w(!1)},M=async(e,r)=>{F.limit=e,F.page=r,w(!0),t.query&&t.query.country&&(F.query[m]=[t.query.country]),null===o?delete F.query["partner_institutions.world_region"]:0===o.length?F.query["partner_institutions.world_region"]=["__NO_MATCH__"]:F.query["partner_institutions.world_region"]=o,j&&(F.query={...F.query,status:j}),E&&(F.sort=E.sort);let s=await d.A.get("/project",F);s&&Array.isArray(s.data)&&(v(s.data),a(s.data),P(e),w(!1)),N(r)};(0,l.useEffect)(()=>{I.page=1,B(F)},[o,t]),(0,l.useEffect)(()=>{B(I)},[I]);let O=async(e,t)=>{w(!0),F.sort={[e.selector]:t},j&&(F.query={...F.query,status:j}),""!==h&&(F.query={...F.query,title:h}),await B(F),R(F),w(!1)},L=(e,t)=>{e?(I.query.title=e,I.page=t):delete I.query.title,D({...I})},V=(0,l.useRef)(i().debounce((e,t)=>L(e,t),Number("500")||300)).current,z=(0,l.useMemo)(()=>{let e=e=>{_(e),e?(I.query.status=e,I.page=q):delete I.query.status,D({...I})};return(0,s.jsx)(p.default,{onFilter:e=>{g(e.target.value),V(e.target.value,q)},onFilterStatusChange:t=>e(t.target.value),onClear:()=>{h&&(T(!x),g(""))},filterText:h,filterStatus:j})},[h,j,x,o,q]);return(0,s.jsx)(u.A,{columns:H,data:A,totalRows:C,loading:b,subheader:!0,persistTableHead:!0,onSort:O,sortServer:!0,pagServer:!0,resetPaginationToggle:x,subHeaderComponent:z,handlePerRowsChange:M,handlePageChange:e=>{F.limit=k,F.page=e,j&&(F.query={...F.query,status:j}),E&&(F.sort=E.sort),B(F),N(e)}})}},39598:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>u});var s=r(37876),a=r(14232),n=r(82851),l=r.n(n),o=r(31753),i=r(66619),c=r(15641);let u=e=>{let{i18n:t}=(0,o.Bd)("common"),r=t.language,{projects:n,selectedRegions:u}=e,[p,d]=(0,a.useState)([]),[y,m]=(0,a.useState)({}),[f,h]=(0,a.useState)({}),[g,j]=(0,a.useState)({}),_=()=>{m(null),h(null)},x=(e,t,r)=>{_(),m(t),h({name:e.name,id:e.id,countryId:e.countryId})},T=e=>e.partner_country&&e.partner_country.coordinates,A=e=>e.partner_country&&e.partner_country.coordinates,v=e=>{let t=[];return l().forEach(e.partner_institutions,r=>{console.log("pointer",r),t.push({title:e&&e.title?e.title:"",id:e&&e._id?e._id:"",lat:T(r)&&parseFloat(r.partner_country.coordinates[0].latitude),lng:A(r)&&parseFloat(r.partner_country.coordinates[0].longitude),world_region:r.world_region,countryId:r.partner_country&&r.partner_country._id})}),t[0]},b=()=>{let e=[];l().forEach(n,t=>{let r=v(t);e.push(r)}),d(l().filter(e,function(e){if(u.length>0)return u.includes(e.world_region)}))},w=()=>{let e=[];l().forEach(n,t=>{t.partner_institutions&&t.partner_institutions.length>0&&l().forEach(t.partner_institutions,r=>{r.title=t.title,r.id=t._id,e.push(r)})}),j(l().groupBy(e,"partner_country._id"))};return(0,a.useEffect)(()=>{b(),w()},[n]),(0,s.jsx)(i.A,{onClose:_,language:r,activeMarker:y,markerInfo:(0,s.jsx)(e=>{let{info:t}=e;return t&&t.countryId&&g[t.countryId]?(0,s.jsx)("ul",{children:g[t.countryId].map((e,t)=>(0,s.jsx)("li",{children:(0,s.jsx)("a",{href:"/".concat(r,"/project/show/").concat(e.id),children:e.title})},t))}):null},{info:f}),children:p.length>=1?p.map((e,t)=>(0,s.jsx)(c.A,{name:e.title,id:e.id,countryId:e.countryId,icon:{url:"/images/map-marker-white.svg"},onClick:x,position:e},t)):null})}},43215:(e,t,r)=>{"use strict";r.r(t),r.d(t,{__N_SSG:()=>g,default:()=>j});var s=r(37876),a=r(14232),n=r(48230),l=r.n(n),o=r(60282),i=r(49589),c=r(56970),u=r(37784),p=r(69600),d=r(37669),y=r(39598),m=r(31753),f=r(46864),h=r(69438),g=!0;let j=e=>{let{t}=(0,m.Bd)("common"),[r,n]=(0,a.useState)([]),[g,j]=(0,a.useState)(null),_=()=>(0,s.jsx)(l(),{href:"/project/[...routes]",as:"/project/create",children:(0,s.jsx)(o.A,{variant:"secondary",size:"sm",children:t("addProject")})}),x=(0,f.canAddProject)(()=>(0,s.jsx)(_,{})),T=e=>{j(e)};return(0,s.jsxs)(i.A,{fluid:!0,className:"p-0",children:[(0,s.jsx)(c.A,{children:(0,s.jsx)(u.A,{xs:12,children:(0,s.jsx)(p.A,{title:t("projects")})})}),(0,s.jsx)(c.A,{children:(0,s.jsx)(u.A,{xs:12,children:(0,s.jsx)(y.default,{selectedRegions:g,projects:r})})}),(0,s.jsx)(c.A,{children:(0,s.jsx)(u.A,{xs:12,children:(0,s.jsx)(h.A,{filtreg:e=>T(e),selectedRegions:[],regionHandler:T})})}),(0,s.jsx)(c.A,{children:(0,s.jsx)(u.A,{xs:12,className:"ps-4",children:(0,s.jsx)(x,{})})}),(0,s.jsx)(c.A,{className:"mt-3",children:(0,s.jsx)(u.A,{xs:12,children:(0,s.jsx)(d.default,{selectedRegions:g,setProjects:n})})})]})}},46864:(e,t,r)=>{"use strict";r.r(t),r.d(t,{canAddProject:()=>l,canAddProjectForm:()=>o,canEditProject:()=>i,canEditProjectForm:()=>c,canViewDiscussionUpdate:()=>u,default:()=>p});var s=r(37876);r(14232);var a=r(8178),n=r(59626);let l=(0,a.A)({authenticatedSelector:e=>!!e.permissions&&!!e.permissions.project&&!!e.permissions.project["create:any"],wrapperDisplayName:"CanAddProject"}),o=(0,a.A)({authenticatedSelector:e=>!!e.permissions&&!!e.permissions.project&&!!e.permissions.project["create:any"],wrapperDisplayName:"CanAddProjectForm",FailureComponent:()=>(0,s.jsx)(n.default,{})}),i=(0,a.A)({authenticatedSelector:(e,t)=>{if(e.permissions&&e.permissions.project){if(e.permissions.project["update:any"])return!0;else if(e.permissions.project["update:own"]&&t.project&&t.project.user&&t.project.user._id===e.user._id)return!0}return!1},wrapperDisplayName:"CanEditProject"}),c=(0,a.A)({authenticatedSelector:(e,t)=>{if(e.permissions&&e.permissions.project){if(e.permissions.project["update:any"])return!0;else if(e.permissions.project["update:own"]&&t.project&&t.project.user&&t.project.user._id===e.user._id)return!0}return!1},wrapperDisplayName:"CanEditProjectForm",FailureComponent:()=>(0,s.jsx)(n.default,{})}),u=(0,a.A)({authenticatedSelector:e=>!!e.permissions&&!!e.permissions.update&&!!e.permissions.update["read:any"],wrapperDisplayName:"CanViewDiscussionUpdate"}),p=l},50749:(e,t,r)=>{"use strict";r.d(t,{A:()=>i});var s=r(37876);r(14232);var a=r(89773),n=r(31753),l=r(5507);function o(e){let{t}=(0,n.Bd)("common"),r={rowsPerPageText:t("Rowsperpage")},{columns:o,data:i,totalRows:c,resetPaginationToggle:u,subheader:p,subHeaderComponent:d,handlePerRowsChange:y,handlePageChange:m,rowsPerPage:f,defaultRowsPerPage:h,selectableRows:g,loading:j,pagServer:_,onSelectedRowsChange:x,clearSelectedRows:T,sortServer:A,onSort:v,persistTableHead:b,sortFunction:w,...C}=e,S={paginationComponentOptions:r,noDataComponent:t("NoData"),noHeader:!0,columns:o,data:i||[],dense:!0,paginationResetDefaultPage:u,subHeader:p,progressPending:j,subHeaderComponent:d,pagination:!0,paginationServer:_,paginationPerPage:h||10,paginationRowsPerPageOptions:f||[10,15,20,25,30],paginationTotalRows:c,onChangeRowsPerPage:y,onChangePage:m,selectableRows:g,onSelectedRowsChange:x,clearSelectedRows:T,progressComponent:(0,s.jsx)(l.A,{}),sortIcon:(0,s.jsx)("i",{className:"sort-icon fas fa-exchange-alt"}),sortServer:A,onSort:v,sortFunction:w,persistTableHead:b,className:"rki-table"};return(0,s.jsx)(a.Ay,{...S})}o.defaultProps={subHeader:!1,pagination:!0,totalRows:null,pagServer:!0,onSelectedRowsChange:null,clearSelectedRows:!1,sortServer:!1,persistTableHead:!1};let i=o},66619:(e,t,r)=>{"use strict";r.d(t,{A:()=>y});var s=r(37876);r(14232);var a=r(62945);let n=e=>{let{position:t,onCloseClick:r,children:n}=e;return(0,s.jsx)(a.Fu,{position:t,onCloseClick:r,children:(0,s.jsx)("div",{children:n})})},l="labels.text.fill",o="labels.text.stroke",i="road.highway",c="geometry.stroke",u=[{elementType:"geometry",stylers:[{color:"#1d2c4d"}]},{elementType:l,stylers:[{color:"#8ec3b9"}]},{elementType:o,stylers:[{color:"#1a3646"}]},{featureType:"administrative",elementType:"geometry",stylers:[{visibility:"off"}]},{featureType:"administrative.country",elementType:c,stylers:[{color:"#4b6878"}]},{featureType:"administrative.land_parcel",elementType:"labels",stylers:[{visibility:"off"}]},{featureType:"administrative.land_parcel",elementType:l,stylers:[{color:"#64779e"}]},{featureType:"administrative.province",elementType:c,stylers:[{color:"#4b6878"}]},{featureType:"landscape.man_made",elementType:c,stylers:[{color:"#334e87"}]},{featureType:"landscape.natural",elementType:"geometry",stylers:[{color:"#023e58"}]},{featureType:"poi",stylers:[{visibility:"off"}]},{featureType:"poi",elementType:"geometry",stylers:[{color:"#283d6a"}]},{featureType:"poi",elementType:"labels.text",stylers:[{visibility:"off"}]},{featureType:"poi",elementType:l,stylers:[{color:"#6f9ba5"}]},{featureType:"poi",elementType:o,stylers:[{color:"#1d2c4d"}]},{featureType:"poi.park",elementType:"geometry.fill",stylers:[{color:"#023e58"}]},{featureType:"poi.park",elementType:l,stylers:[{color:"#3C7680"}]},{featureType:"road",stylers:[{visibility:"off"}]},{featureType:"road",elementType:"geometry",stylers:[{color:"#304a7d"}]},{featureType:"road",elementType:"labels.icon",stylers:[{visibility:"off"}]},{featureType:"road",elementType:l,stylers:[{color:"#98a5be"}]},{featureType:"road",elementType:o,stylers:[{color:"#1d2c4d"}]},{featureType:i,elementType:"geometry",stylers:[{color:"#2c6675"}]},{featureType:i,elementType:c,stylers:[{color:"#255763"}]},{featureType:i,elementType:l,stylers:[{color:"#b0d5ce"}]},{featureType:i,elementType:o,stylers:[{color:"#023e58"}]},{featureType:"road.local",elementType:"labels",stylers:[{visibility:"off"}]},{featureType:"transit",stylers:[{visibility:"off"}]},{featureType:"transit",elementType:l,stylers:[{color:"#98a5be"}]},{featureType:"transit",elementType:o,stylers:[{color:"#1d2c4d"}]},{featureType:"transit.line",elementType:"geometry.fill",stylers:[{color:"#283d6a"}]},{featureType:"transit.station",elementType:"geometry",stylers:[{color:"#3a4762"}]},{featureType:"water",elementType:"geometry",stylers:[{color:"#0e1626"}]},{featureType:"water",elementType:l,stylers:[{color:"#4e6d70"}]}];var p=r(89099),d=r(55316);let y=e=>{let{markerInfo:t,activeMarker:r,initialCenter:l,children:o,height:i=300,width:c="114%",language:y,zoom:m=1,minZoom:f=1,onClose:h}=e,{locale:g}=(0,p.useRouter)(),{isLoaded:j,loadError:_}=(0,d._)();return _?(0,s.jsx)("div",{children:"Error loading maps"}):j?(0,s.jsx)("div",{className:"map-container",children:(0,s.jsx)("div",{className:"mapprint",style:{width:c,height:i,position:"relative"},children:(0,s.jsxs)(a.u6,{mapContainerStyle:{width:c,height:"number"==typeof i?"".concat(i,"px"):i},center:l||{lat:52.520017,lng:13.404195},zoom:m,onLoad:e=>{e.setOptions({styles:u})},options:{minZoom:f,draggable:!0,keyboardShortcuts:!1,streetViewControl:!1,panControl:!1,clickableIcons:!1,mapTypeControl:!1,fullscreenControl:!0},children:[o,t&&r&&r.getPosition&&(0,s.jsx)(n,{position:r.getPosition(),onCloseClick:()=>{console.log("close click"),null==h||h()},children:t})]})})}):(0,s.jsx)("div",{children:"Loading Maps..."})}},68945:(e,t,r)=>{(window.__NEXT_P=window.__NEXT_P||[]).push(["/project",function(){return r(43215)}])},69438:(e,t,r)=>{"use strict";r.d(t,{A:()=>d});var s=r(37876),a=r(14232),n=r(82851),l=r.n(n),o=r(29504),i=r(60282),c=r(53718),u=r(31753);function p(e){let{filtreg:t}=e,[r,n]=(0,a.useState)(!0),[p,d]=(0,a.useState)([]),[y,m]=(0,a.useState)([]),{t:f}=(0,u.Bd)("common"),h={query:{},limit:"~",sort:{title:"asc"}},g=async e=>{let r=await c.A.get("/worldregion",e);if(r&&Array.isArray(r.data)){let e=[],s=[];l().each(r.data,(t,r)=>{let a={...t,isChecked:!0};e.push(a),s.push(t._id)}),t(s),m(s),d(e)}};(0,a.useEffect)(()=>{g(h)},[]);let j=e=>{let r=[...p],s=[...y];r.forEach((t,a)=>{t.code===e.target.id&&(r[a].isChecked=e.target.checked,e.target.checked?s.push(t._id):s=s.filter(e=>e!==t._id))}),m(s),t(s),n(!1),d(r)};return(0,s.jsxs)("div",{className:"regions-multi-checkboxes",children:[(0,s.jsx)(o.A.Check,{type:"checkbox",id:"all",label:f("AllRegions"),checked:r,onChange:e=>{let r=p.map(t=>({...t,isChecked:e.target.checked})),s=[];e.target.checked&&(s=r.map(e=>e._id)),t(s),m(s),n(e.target.checked),d(r)}}),p.map((e,t)=>(0,s.jsx)(o.A.Check,{type:"checkbox",id:e.code,label:e.title,value:e.code,onChange:j,checked:p[t].isChecked},t)),(0,s.jsx)(i.A,{onClick:()=>{let e=p.map(e=>({...e,isChecked:!1}));m([]),n(!1),d(e),t([])},className:"btn-plain ps-2",children:f("ClearAll")})]})}p.defaultProps={filtreg:()=>{}};let d=p},69600:(e,t,r)=>{"use strict";r.d(t,{A:()=>a});var s=r(37876);function a(e){return(0,s.jsx)("h2",{className:"page-heading",children:e.title})}}},e=>{var t=t=>e(e.s=t);e.O(0,[9759,9773,636,6593,8792],()=>t(68945)),_N_E=e.O()}]);
//# sourceMappingURL=project-7abb0381927de6f6.js.map