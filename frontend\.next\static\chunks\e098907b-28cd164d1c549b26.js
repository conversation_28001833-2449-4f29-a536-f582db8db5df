"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[9759],{62945:(e,t,s)=>{s.d(t,{Fu:()=>e5,pH:()=>en,u6:()=>x});var n,o,i,r,a,l,u,p=s(37876),c=s(14232),h=s(98477);function g(e){return(g="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function d(e,t,s){var n;return(n=function(e,t){if("object"!=g(e)||!e)return e;var s=e[Symbol.toPrimitive];if(void 0!==s){var n=s.call(e,t||"default");if("object"!=g(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(t,"string"),(t="symbol"==g(n)?n:n+"")in e)?Object.defineProperty(e,t,{value:s,enumerable:!0,configurable:!0,writable:!0}):e[t]=s,e}function m(e){return e&&e.__esModule&&Object.prototype.hasOwnProperty.call(e,"default")?e.default:e}var v=m(o?n:(o=1,n=function(e,t,s,n,o,i,r,a){if(!e){var l;if(void 0===t)l=Error("Minified exception occurred; use the non-minified dev environment for the full error message and additional helpful warnings.");else{var u=[s,n,o,i,r,a],p=0;(l=Error(t.replace(/%s/g,function(){return u[p++]}))).name="Invariant Violation"}throw l.framesToPop=1,l}})),f=(0,c.createContext)(null);function y(e){google.maps.event.removeListener(e)}function L(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[];e.forEach(y)}function b(e){var t,s,n,o,{updaterMap:i,eventMap:r,prevProps:a,nextProps:l,instance:u}=e,p=(t=function(e,t,s){return"function"==typeof l[s]&&e.push(google.maps.event.addListener(u,t,l[s])),e},s=[],Object.keys(r).reduce(function(e,s){return t(e,r[s],s)},s));return n={},o=(e,t)=>{var s=l[t];s!==a[t]&&(n[t]=s,e(u,s))},Object.keys(i).forEach(e=>o(i[e],e)),p}var E={onDblClick:"dblclick",onDragEnd:"dragend",onDragStart:"dragstart",onMapTypeIdChanged:"maptypeid_changed",onMouseMove:"mousemove",onMouseOut:"mouseout",onMouseOver:"mouseover",onMouseDown:"mousedown",onMouseUp:"mouseup",onRightClick:"rightclick",onTilesLoaded:"tilesloaded",onBoundsChanged:"bounds_changed",onCenterChanged:"center_changed",onClick:"click",onDrag:"drag",onHeadingChanged:"heading_changed",onIdle:"idle",onProjectionChanged:"projection_changed",onResize:"resize",onTiltChanged:"tilt_changed",onZoomChanged:"zoom_changed"},w={extraMapTypes(e,t){t.forEach(function(t,s){e.mapTypes.set(String(s),t)})},center(e,t){e.setCenter(t)},clickableIcons(e,t){e.setClickableIcons(t)},heading(e,t){e.setHeading(t)},mapTypeId(e,t){e.setMapTypeId(t)},options(e,t){e.setOptions(t)},streetView(e,t){e.setStreetView(t)},tilt(e,t){e.setTilt(t)},zoom(e,t){e.setZoom(t)}};(0,c.memo)(function(e){var{children:t,options:s,id:n,mapContainerStyle:o,mapContainerClassName:i,center:r,onClick:a,onDblClick:l,onDrag:u,onDragEnd:h,onDragStart:g,onMouseMove:d,onMouseOut:m,onMouseOver:v,onMouseDown:y,onMouseUp:L,onRightClick:b,onCenterChanged:E,onLoad:w,onUnmount:x}=e,[C,M]=(0,c.useState)(null),k=(0,c.useRef)(null),[P,O]=(0,c.useState)(null),[S,I]=(0,c.useState)(null),[j,D]=(0,c.useState)(null),[_,B]=(0,c.useState)(null),[T,z]=(0,c.useState)(null),[R,A]=(0,c.useState)(null),[U,Z]=(0,c.useState)(null),[V,W]=(0,c.useState)(null),[N,H]=(0,c.useState)(null),[G,F]=(0,c.useState)(null),[Y,K]=(0,c.useState)(null),[q,J]=(0,c.useState)(null);return(0,c.useEffect)(()=>{s&&null!==C&&C.setOptions(s)},[C,s]),(0,c.useEffect)(()=>{null!==C&&void 0!==r&&C.setCenter(r)},[C,r]),(0,c.useEffect)(()=>{C&&l&&(null!==S&&google.maps.event.removeListener(S),I(google.maps.event.addListener(C,"dblclick",l)))},[l]),(0,c.useEffect)(()=>{C&&h&&(null!==j&&google.maps.event.removeListener(j),D(google.maps.event.addListener(C,"dragend",h)))},[h]),(0,c.useEffect)(()=>{C&&g&&(null!==_&&google.maps.event.removeListener(_),B(google.maps.event.addListener(C,"dragstart",g)))},[g]),(0,c.useEffect)(()=>{C&&y&&(null!==T&&google.maps.event.removeListener(T),z(google.maps.event.addListener(C,"mousedown",y)))},[y]),(0,c.useEffect)(()=>{C&&d&&(null!==R&&google.maps.event.removeListener(R),A(google.maps.event.addListener(C,"mousemove",d)))},[d]),(0,c.useEffect)(()=>{C&&m&&(null!==U&&google.maps.event.removeListener(U),Z(google.maps.event.addListener(C,"mouseout",m)))},[m]),(0,c.useEffect)(()=>{C&&v&&(null!==V&&google.maps.event.removeListener(V),W(google.maps.event.addListener(C,"mouseover",v)))},[v]),(0,c.useEffect)(()=>{C&&L&&(null!==N&&google.maps.event.removeListener(N),H(google.maps.event.addListener(C,"mouseup",L)))},[L]),(0,c.useEffect)(()=>{C&&b&&(null!==G&&google.maps.event.removeListener(G),F(google.maps.event.addListener(C,"rightclick",b)))},[b]),(0,c.useEffect)(()=>{C&&a&&(null!==Y&&google.maps.event.removeListener(Y),K(google.maps.event.addListener(C,"click",a)))},[a]),(0,c.useEffect)(()=>{C&&u&&(null!==q&&google.maps.event.removeListener(q),J(google.maps.event.addListener(C,"drag",u)))},[u]),(0,c.useEffect)(()=>{C&&E&&(null!==P&&google.maps.event.removeListener(P),O(google.maps.event.addListener(C,"center_changed",E)))},[a]),(0,c.useEffect)(()=>{var e=null===k.current?null:new google.maps.Map(k.current,s);return M(e),null!==e&&w&&w(e),()=>{null!==e&&x&&x(e)}},[]),(0,p.jsx)("div",{id:n,ref:k,style:o,className:i,children:(0,p.jsx)(f.Provider,{value:C,children:null!==C?t:null})})});class x extends c.PureComponent{constructor(){super(...arguments),d(this,"state",{map:null}),d(this,"registeredEvents",[]),d(this,"mapRef",null),d(this,"getInstance",()=>null===this.mapRef?null:new google.maps.Map(this.mapRef,this.props.options)),d(this,"panTo",e=>{var t=this.getInstance();t&&t.panTo(e)}),d(this,"setMapCallback",()=>{null!==this.state.map&&this.props.onLoad&&this.props.onLoad(this.state.map)}),d(this,"getRef",e=>{this.mapRef=e})}componentDidMount(){var e=this.getInstance();this.registeredEvents=b({updaterMap:w,eventMap:E,prevProps:{},nextProps:this.props,instance:e}),this.setState(function(){return{map:e}},this.setMapCallback)}componentDidUpdate(e){null!==this.state.map&&(L(this.registeredEvents),this.registeredEvents=b({updaterMap:w,eventMap:E,prevProps:e,nextProps:this.props,instance:this.state.map}))}componentWillUnmount(){null!==this.state.map&&(this.props.onUnmount&&this.props.onUnmount(this.state.map),L(this.registeredEvents))}render(){return(0,p.jsx)("div",{id:this.props.id,ref:this.getRef,style:this.props.mapContainerStyle,className:this.props.mapContainerClassName,children:(0,p.jsx)(f.Provider,{value:this.state.map,children:null!==this.state.map?this.props.children:null})})}}function C(e,t,s,n,o,i,r){try{var a=e[i](r),l=a.value}catch(e){return void s(e)}a.done?t(l):Promise.resolve(l).then(n,o)}function M(e){return function(){var t=this,s=arguments;return new Promise(function(n,o){var i=e.apply(t,s);function r(e){C(i,n,o,r,a,"next",e)}function a(e){C(i,n,o,r,a,"throw",e)}r(void 0)})}}function k(e){var{googleMapsApiKey:t,googleMapsClientId:s,version:n="weekly",language:o,region:i,libraries:r,channel:a,mapIds:l,authReferrerPolicy:u}=e,p=[];return v(t&&s||!(t&&s),"You need to specify either googleMapsApiKey or googleMapsClientId for @react-google-maps/api load script to work. You cannot use both at the same time."),t?p.push("key=".concat(t)):s&&p.push("client=".concat(s)),n&&p.push("v=".concat(n)),o&&p.push("language=".concat(o)),i&&p.push("region=".concat(i)),r&&r.length&&p.push("libraries=".concat(r.sort().join(","))),a&&p.push("channel=".concat(a)),l&&l.length&&p.push("map_ids=".concat(l.join(","))),u&&p.push("auth_referrer_policy=".concat(u)),p.push("loading=async"),p.push("callback=initMap"),"https://maps.googleapis.com/maps/api/js?".concat(p.join("&"))}var P="undefined"!=typeof document;function O(e){var{url:t,id:s,nonce:n}=e;return P?new Promise(function(e,o){var i=document.getElementById(s),r=window;if(i){var a=i.getAttribute("data-state");if(i.src===t&&"error"!==a)if("ready"===a)return e(s);else{var l=r.initMap,u=i.onerror;r.initMap=function(){l&&l(),e(s)},i.onerror=function(e){u&&u(e),o(e)};return}i.remove()}var p=document.createElement("script");p.type="text/javascript",p.src=t,p.id=s,p.async=!0,p.nonce=n||"",p.onerror=function(e){p.setAttribute("data-state","error"),o(e)},r.initMap=function(){p.setAttribute("data-state","ready"),e(s)},document.head.appendChild(p)}).catch(e=>{throw console.error("injectScript error: ",e),e}):Promise.reject(Error("document is undefined"))}function S(e){var t=e.href;return!!t&&(0===t.indexOf("https://fonts.googleapis.com/css?family=Roboto")||0===t.indexOf("https://fonts.googleapis.com/css?family=Google+Sans+Text"))||("style"===e.tagName.toLowerCase()&&e.styleSheet&&e.styleSheet.cssText&&0===e.styleSheet.cssText.replace("\r\n","").indexOf(".gm-style")?(e.styleSheet.cssText="",!0):"style"===e.tagName.toLowerCase()&&e.innerHTML&&0===e.innerHTML.replace("\r\n","").indexOf(".gm-style")?(e.innerHTML="",!0):"style"===e.tagName.toLowerCase()&&!e.styleSheet&&!e.innerHTML)}function I(){var e=document.getElementsByTagName("head")[0];if(e){var t=e.insertBefore.bind(e);e.insertBefore=function(s,n){return S(s)||Reflect.apply(t,e,[s,n]),s};var s=e.appendChild.bind(e);e.appendChild=function(t){return S(t)||Reflect.apply(s,e,[t]),t}}}var j=!1;function D(){return(0,p.jsx)("div",{children:"Loading..."})}var _={id:"script-loader",version:"weekly"};class B extends c.PureComponent{constructor(){super(...arguments),d(this,"check",null),d(this,"state",{loaded:!1}),d(this,"cleanupCallback",()=>{delete window.google.maps,this.injectScript()}),d(this,"isCleaningUp",M(function*(){return new Promise(function(e){if(j){if(P)var t=window.setInterval(function(){j||(window.clearInterval(t),e())},1)}else e()})})),d(this,"cleanup",()=>{j=!0;var e=document.getElementById(this.props.id);e&&e.parentNode&&e.parentNode.removeChild(e),Array.prototype.slice.call(document.getElementsByTagName("script")).filter(function(e){return"string"==typeof e.src&&e.src.includes("maps.googleapis")}).forEach(function(e){e.parentNode&&e.parentNode.removeChild(e)}),Array.prototype.slice.call(document.getElementsByTagName("link")).filter(function(e){return"https://fonts.googleapis.com/css?family=Roboto:300,400,500,700|Google+Sans"===e.href}).forEach(function(e){e.parentNode&&e.parentNode.removeChild(e)}),Array.prototype.slice.call(document.getElementsByTagName("style")).filter(function(e){return void 0!==e.innerText&&e.innerText.length>0&&e.innerText.includes(".gm-")}).forEach(function(e){e.parentNode&&e.parentNode.removeChild(e)})}),d(this,"injectScript",()=>{this.props.preventGoogleFontsLoading&&I(),v(!!this.props.id,'LoadScript requires "id" prop to be a string: %s',this.props.id),O({id:this.props.id,nonce:this.props.nonce,url:k(this.props)}).then(()=>{this.props.onLoad&&this.props.onLoad(),this.setState(function(){return{loaded:!0}})}).catch(e=>{this.props.onError&&this.props.onError(e),console.error("\n          There has been an Error with loading Google Maps API script, please check that you provided correct google API key (".concat(this.props.googleMapsApiKey||"-",") or Client ID (").concat(this.props.googleMapsClientId||"-",") to <LoadScript />\n          Otherwise it is a Network issue.\n        "))})}),d(this,"getRef",e=>{this.check=e})}componentDidMount(){if(P){if(window.google&&window.google.maps&&!j)return void console.error("google api is already presented");this.isCleaningUp().then(this.injectScript).catch(function(e){console.error("Error at injecting script after cleaning up: ",e)})}}componentDidUpdate(e){this.props.libraries!==e.libraries&&console.warn("Performance warning! LoadScript has been reloaded unintentionally! You should not pass `libraries` prop as new array. Please keep an array of libraries as static class property for Components and PureComponents, or just a const variable outside of component, or somewhere in config files or ENV variables"),P&&e.language!==this.props.language&&(this.cleanup(),this.setState(function(){return{loaded:!1}},this.cleanupCallback))}componentWillUnmount(){P&&(this.cleanup(),window.setTimeout(()=>{this.check||(delete window.google,j=!1)},1),this.props.onUnmount&&this.props.onUnmount())}render(){return(0,p.jsxs)(p.Fragment,{children:[(0,p.jsx)("div",{ref:this.getRef}),this.state.loaded?this.props.children:this.props.loadingElement||(0,p.jsx)(D,{})]})}}function T(e,t){if(null==e)return{};var s,n,o=function(e,t){if(null==e)return{};var s={};for(var n in e)if(({}).hasOwnProperty.call(e,n)){if(t.includes(n))continue;s[n]=e[n]}return s}(e,t);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);for(n=0;n<i.length;n++)s=i[n],t.includes(s)||({}).propertyIsEnumerable.call(e,s)&&(o[s]=e[s])}return o}d(B,"defaultProps",_);var z=["loadingElement","onLoad","onError","onUnmount","children"],R=(0,p.jsx)(D,{});(0,c.memo)(function(e){var{loadingElement:t,onLoad:s,onError:n,onUnmount:o,children:r}=e,{isLoaded:a,loadError:l}=function(e){var{id:t=_.id,version:s=_.version,nonce:n,googleMapsApiKey:o,googleMapsClientId:r,language:a,region:l,libraries:u,preventGoogleFontsLoading:p,channel:h,mapIds:g,authReferrerPolicy:d}=e,m=(0,c.useRef)(!1),[f,y]=(0,c.useState)(!1),[L,b]=(0,c.useState)(void 0);(0,c.useEffect)(function(){return m.current=!0,()=>{m.current=!1}},[]),(0,c.useEffect)(function(){P&&p&&I()},[p]),(0,c.useEffect)(function(){f&&v(!!window.google,"useLoadScript was marked as loaded, but window.google is not present. Something went wrong.")},[f]);var E=k({version:s,googleMapsApiKey:o,googleMapsClientId:r,language:a,region:l,libraries:u,channel:h,mapIds:g,authReferrerPolicy:d});(0,c.useEffect)(function(){if(P){if(window.google&&window.google.maps&&i===E)return void e();O({id:t,url:E,nonce:n}).then(e).catch(function(e){m.current&&b(e),console.warn("\n        There has been an Error with loading Google Maps API script, please check that you provided correct google API key (".concat(o||"-",") or Client ID (").concat(r||"-",")\n        Otherwise it is a Network issue.\n      ")),console.error(e)})}function e(){m.current&&(y(!0),i=E)}},[t,E,n]);var w=(0,c.useRef)(void 0);return(0,c.useEffect)(function(){w.current&&u!==w.current&&console.warn("Performance warning! LoadScript has been reloaded unintentionally! You should not pass `libraries` prop as new array. Please keep an array of libraries as static class property for Components and PureComponents, or just a const variable outside of component, or somewhere in config files or ENV variables"),w.current=u},[u]),{isLoaded:f,loadError:L,url:E}}(T(e,z));return(0,c.useEffect)(function(){a&&"function"==typeof s&&s()},[a,s]),(0,c.useEffect)(function(){l&&"function"==typeof n&&n(l)},[l,n]),(0,c.useEffect)(function(){return()=>{o&&o()}},[o]),a?r:t||R}),"function"==typeof SuppressedError&&SuppressedError;var A="__googleMapsScriptId";!function(e){e[e.INITIALIZED=0]="INITIALIZED",e[e.LOADING=1]="LOADING",e[e.SUCCESS=2]="SUCCESS",e[e.FAILURE=3]="FAILURE"}(r||(r={}));class U{constructor(e){var{apiKey:t,authReferrerPolicy:s,channel:n,client:o,id:i=A,language:r,libraries:a=[],mapIds:l,nonce:u,region:p,retries:c=3,url:h="https://maps.googleapis.com/maps/api/js",version:g}=e;if(this.callbacks=[],this.done=!1,this.loading=!1,this.errors=[],this.apiKey=t,this.authReferrerPolicy=s,this.channel=n,this.client=o,this.id=i||A,this.language=r,this.libraries=a,this.mapIds=l,this.nonce=u,this.region=p,this.retries=c,this.url=h,this.version=g,U.instance){if(!(null)(this.options,U.instance.options))throw Error("Loader must not be called again with different options. ".concat(JSON.stringify(this.options)," !== ").concat(JSON.stringify(U.instance.options)));return U.instance}U.instance=this}get options(){return{version:this.version,apiKey:this.apiKey,channel:this.channel,client:this.client,id:this.id,libraries:this.libraries,language:this.language,region:this.region,mapIds:this.mapIds,nonce:this.nonce,url:this.url,authReferrerPolicy:this.authReferrerPolicy}}get status(){return this.errors.length?r.FAILURE:this.done?r.SUCCESS:this.loading?r.LOADING:r.INITIALIZED}get failed(){return this.done&&!this.loading&&this.errors.length>=this.retries+1}createUrl(){var e=this.url;return e+="?callback=__googleMapsCallback&loading=async",this.apiKey&&(e+="&key=".concat(this.apiKey)),this.channel&&(e+="&channel=".concat(this.channel)),this.client&&(e+="&client=".concat(this.client)),this.libraries.length>0&&(e+="&libraries=".concat(this.libraries.join(","))),this.language&&(e+="&language=".concat(this.language)),this.region&&(e+="&region=".concat(this.region)),this.version&&(e+="&v=".concat(this.version)),this.mapIds&&(e+="&map_ids=".concat(this.mapIds.join(","))),this.authReferrerPolicy&&(e+="&auth_referrer_policy=".concat(this.authReferrerPolicy)),e}deleteScript(){var e=document.getElementById(this.id);e&&e.remove()}load(){return this.loadPromise()}loadPromise(){return new Promise((e,t)=>{this.loadCallback(s=>{s?t(s.error):e(window.google)})})}importLibrary(e){return this.execute(),google.maps.importLibrary(e)}loadCallback(e){this.callbacks.push(e),this.execute()}setScript(){if(document.getElementById(this.id))return void this.callback();var e,t,s,n,o,i,r,a,l,u,p,c,h,g,d,m={key:this.apiKey,channel:this.channel,client:this.client,libraries:this.libraries.length&&this.libraries,v:this.version,mapIds:this.mapIds,language:this.language,region:this.region,authReferrerPolicy:this.authReferrerPolicy};Object.keys(m).forEach(e=>!m[e]&&delete m[e]),(null==(d=null==(g=null==window?void 0:window.google)?void 0:g.maps)?void 0:d.importLibrary)||(n="The Google Maps JavaScript API",o="google",i="importLibrary",r="__ib__",a=document,u=(l=(l=window)[o]||(l[o]={})).maps||(l.maps={}),p=new Set,c=new URLSearchParams,h=()=>e||(e=new Promise((i,l)=>{var h,g,d;return h=this,g=void 0,d=function*(){var h;for(s in yield t=a.createElement("script"),t.id=this.id,c.set("libraries",[...p]+""),m)c.set(s.replace(/[A-Z]/g,e=>"_"+e[0].toLowerCase()),m[s]);c.set("callback",o+".maps."+r),t.src=this.url+"?"+c,u[r]=i,t.onerror=()=>e=l(Error(n+" could not load.")),t.nonce=this.nonce||(null==(h=a.querySelector("script[nonce]"))?void 0:h.nonce)||"",a.head.append(t)},new(g||(g=Promise))(function(e,t){function s(e){try{o(d.next(e))}catch(e){t(e)}}function n(e){try{o(d.throw(e))}catch(e){t(e)}}function o(t){var o;t.done?e(t.value):((o=t.value)instanceof g?o:new g(function(e){e(o)})).then(s,n)}o((d=d.apply(h,[])).next())})})),u[i]?console.warn(n+" only loads once. Ignoring:",m):u[i]=function(e){for(var t=arguments.length,s=Array(t>1?t-1:0),n=1;n<t;n++)s[n-1]=arguments[n];return p.add(e)&&h().then(()=>u[i](e,...s))});var v=this.libraries.map(e=>this.importLibrary(e));v.length||v.push(this.importLibrary("core")),Promise.all(v).then(()=>this.callback(),e=>{var t=new ErrorEvent("error",{error:e});this.loadErrorCallback(t)})}reset(){this.deleteScript(),this.done=!1,this.loading=!1,this.errors=[],this.onerrorEvent=null}resetIfRetryingFailed(){this.failed&&this.reset()}loadErrorCallback(e){if(this.errors.push(e),this.errors.length<=this.retries){var t=this.errors.length*Math.pow(2,this.errors.length);console.error("Failed to load Google Maps script, retrying in ".concat(t," ms.")),setTimeout(()=>{this.deleteScript(),this.setScript()},t)}else this.onerrorEvent=e,this.callback()}callback(){this.done=!0,this.loading=!1,this.callbacks.forEach(e=>{e(this.onerrorEvent)}),this.callbacks=[]}execute(){if(this.resetIfRetryingFailed(),!this.loading)if(this.done)this.callback();else{if(window.google&&window.google.maps&&window.google.maps.version){console.warn("Google Maps already loaded outside @googlemaps/js-api-loader. This may result in undesirable behavior as options and script parameters may not match."),this.callback();return}this.loading=!0,this.setScript()}}}function Z(e,t){var s=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),s.push.apply(s,n)}return s}function V(e){for(var t=1;t<arguments.length;t++){var s=null!=arguments[t]?arguments[t]:{};t%2?Z(Object(s),!0).forEach(function(t){d(e,t,s[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(s)):Z(Object(s)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(s,t))})}return e}var W={},N={options(e,t){e.setOptions(t)}};(0,c.memo)(function(e){var{options:t,onLoad:s,onUnmount:n}=e,o=(0,c.useContext)(f),[i,r]=(0,c.useState)(null);return(0,c.useEffect)(()=>{null!==i&&i.setMap(o)},[o]),(0,c.useEffect)(()=>{t&&null!==i&&i.setOptions(t)},[i,t]),(0,c.useEffect)(()=>{var e=new google.maps.TrafficLayer(V(V({},t),{},{map:o}));return r(e),s&&s(e),()=>{null!==i&&(n&&n(i),i.setMap(null))}},[]),null});class H extends c.PureComponent{constructor(){super(...arguments),d(this,"state",{trafficLayer:null}),d(this,"setTrafficLayerCallback",()=>{null!==this.state.trafficLayer&&this.props.onLoad&&this.props.onLoad(this.state.trafficLayer)}),d(this,"registeredEvents",[])}componentDidMount(){var e=new google.maps.TrafficLayer(V(V({},this.props.options),{},{map:this.context}));this.registeredEvents=b({updaterMap:N,eventMap:W,prevProps:{},nextProps:this.props,instance:e}),this.setState(function(){return{trafficLayer:e}},this.setTrafficLayerCallback)}componentDidUpdate(e){null!==this.state.trafficLayer&&(L(this.registeredEvents),this.registeredEvents=b({updaterMap:N,eventMap:W,prevProps:e,nextProps:this.props,instance:this.state.trafficLayer}))}componentWillUnmount(){null!==this.state.trafficLayer&&(this.props.onUnmount&&this.props.onUnmount(this.state.trafficLayer),L(this.registeredEvents),this.state.trafficLayer.setMap(null))}render(){return null}}d(H,"contextType",f),(0,c.memo)(function(e){var{onLoad:t,onUnmount:s}=e,n=(0,c.useContext)(f),[o,i]=(0,c.useState)(null);return(0,c.useEffect)(()=>{null!==o&&o.setMap(n)},[n]),(0,c.useEffect)(()=>{var e=new google.maps.BicyclingLayer;return i(e),e.setMap(n),t&&t(e),()=>{null!==e&&(s&&s(e),e.setMap(null))}},[]),null});class G extends c.PureComponent{constructor(){super(...arguments),d(this,"state",{bicyclingLayer:null}),d(this,"setBicyclingLayerCallback",()=>{null!==this.state.bicyclingLayer&&(this.state.bicyclingLayer.setMap(this.context),this.props.onLoad&&this.props.onLoad(this.state.bicyclingLayer))})}componentDidMount(){var e=new google.maps.BicyclingLayer;this.setState(()=>({bicyclingLayer:e}),this.setBicyclingLayerCallback)}componentWillUnmount(){null!==this.state.bicyclingLayer&&(this.props.onUnmount&&this.props.onUnmount(this.state.bicyclingLayer),this.state.bicyclingLayer.setMap(null))}render(){return null}}d(G,"contextType",f),(0,c.memo)(function(e){var{onLoad:t,onUnmount:s}=e,n=(0,c.useContext)(f),[o,i]=(0,c.useState)(null);return(0,c.useEffect)(()=>{null!==o&&o.setMap(n)},[n]),(0,c.useEffect)(()=>{var e=new google.maps.TransitLayer;return i(e),e.setMap(n),t&&t(e),()=>{null!==o&&(s&&s(o),o.setMap(null))}},[]),null});class F extends c.PureComponent{constructor(){super(...arguments),d(this,"state",{transitLayer:null}),d(this,"setTransitLayerCallback",()=>{null!==this.state.transitLayer&&(this.state.transitLayer.setMap(this.context),this.props.onLoad&&this.props.onLoad(this.state.transitLayer))})}componentDidMount(){var e=new google.maps.TransitLayer;this.setState(function(){return{transitLayer:e}},this.setTransitLayerCallback)}componentWillUnmount(){null!==this.state.transitLayer&&(this.props.onUnmount&&this.props.onUnmount(this.state.transitLayer),this.state.transitLayer.setMap(null))}render(){return null}}function Y(e,t){var s=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),s.push.apply(s,n)}return s}function K(e){for(var t=1;t<arguments.length;t++){var s=null!=arguments[t]?arguments[t]:{};t%2?Y(Object(s),!0).forEach(function(t){d(e,t,s[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(s)):Y(Object(s)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(s,t))})}return e}d(F,"contextType",f);var q={onCircleComplete:"circlecomplete",onMarkerComplete:"markercomplete",onOverlayComplete:"overlaycomplete",onPolygonComplete:"polygoncomplete",onPolylineComplete:"polylinecomplete",onRectangleComplete:"rectanglecomplete"},J={drawingMode(e,t){e.setDrawingMode(t)},options(e,t){e.setOptions(t)}};(0,c.memo)(function(e){var{options:t,drawingMode:s,onCircleComplete:n,onMarkerComplete:o,onOverlayComplete:i,onPolygonComplete:r,onPolylineComplete:a,onRectangleComplete:l,onLoad:u,onUnmount:p}=e,h=(0,c.useContext)(f),[g,d]=(0,c.useState)(null),[m,y]=(0,c.useState)(null),[L,b]=(0,c.useState)(null),[E,w]=(0,c.useState)(null),[x,C]=(0,c.useState)(null),[M,k]=(0,c.useState)(null),[P,O]=(0,c.useState)(null);return(0,c.useEffect)(()=>{null!==g&&g.setMap(h)},[h]),(0,c.useEffect)(()=>{t&&null!==g&&g.setOptions(t)},[g,t]),(0,c.useEffect)(()=>{null!==g&&g.setDrawingMode(null!=s?s:null)},[g,s]),(0,c.useEffect)(()=>{g&&n&&(null!==m&&google.maps.event.removeListener(m),y(google.maps.event.addListener(g,"circlecomplete",n)))},[g,n]),(0,c.useEffect)(()=>{g&&o&&(null!==L&&google.maps.event.removeListener(L),b(google.maps.event.addListener(g,"markercomplete",o)))},[g,o]),(0,c.useEffect)(()=>{g&&i&&(null!==E&&google.maps.event.removeListener(E),w(google.maps.event.addListener(g,"overlaycomplete",i)))},[g,i]),(0,c.useEffect)(()=>{g&&r&&(null!==x&&google.maps.event.removeListener(x),C(google.maps.event.addListener(g,"polygoncomplete",r)))},[g,r]),(0,c.useEffect)(()=>{g&&a&&(null!==M&&google.maps.event.removeListener(M),k(google.maps.event.addListener(g,"polylinecomplete",a)))},[g,a]),(0,c.useEffect)(()=>{g&&l&&(null!==P&&google.maps.event.removeListener(P),O(google.maps.event.addListener(g,"rectanglecomplete",l)))},[g,l]),(0,c.useEffect)(()=>{v(!!google.maps.drawing,"Did you include prop libraries={['drawing']} in the URL? %s",google.maps.drawing);var e=new google.maps.drawing.DrawingManager(K(K({},t),{},{map:h}));return s&&e.setDrawingMode(s),n&&y(google.maps.event.addListener(e,"circlecomplete",n)),o&&b(google.maps.event.addListener(e,"markercomplete",o)),i&&w(google.maps.event.addListener(e,"overlaycomplete",i)),r&&C(google.maps.event.addListener(e,"polygoncomplete",r)),a&&k(google.maps.event.addListener(e,"polylinecomplete",a)),l&&O(google.maps.event.addListener(e,"rectanglecomplete",l)),d(e),u&&u(e),()=>{null!==g&&(m&&google.maps.event.removeListener(m),L&&google.maps.event.removeListener(L),E&&google.maps.event.removeListener(E),x&&google.maps.event.removeListener(x),M&&google.maps.event.removeListener(M),P&&google.maps.event.removeListener(P),p&&p(g),g.setMap(null))}},[]),null});class X extends c.PureComponent{constructor(e){super(e),d(this,"registeredEvents",[]),d(this,"state",{drawingManager:null}),d(this,"setDrawingManagerCallback",()=>{null!==this.state.drawingManager&&this.props.onLoad&&this.props.onLoad(this.state.drawingManager)}),v(!!google.maps.drawing,"Did you include prop libraries={['drawing']} in the URL? %s",google.maps.drawing)}componentDidMount(){var e=new google.maps.drawing.DrawingManager(K(K({},this.props.options),{},{map:this.context}));this.registeredEvents=b({updaterMap:J,eventMap:q,prevProps:{},nextProps:this.props,instance:e}),this.setState(function(){return{drawingManager:e}},this.setDrawingManagerCallback)}componentDidUpdate(e){null!==this.state.drawingManager&&(L(this.registeredEvents),this.registeredEvents=b({updaterMap:J,eventMap:q,prevProps:e,nextProps:this.props,instance:this.state.drawingManager}))}componentWillUnmount(){null!==this.state.drawingManager&&(this.props.onUnmount&&this.props.onUnmount(this.state.drawingManager),L(this.registeredEvents),this.state.drawingManager.setMap(null))}render(){return null}}function $(e,t){var s=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),s.push.apply(s,n)}return s}function Q(e){for(var t=1;t<arguments.length;t++){var s=null!=arguments[t]?arguments[t]:{};t%2?$(Object(s),!0).forEach(function(t){d(e,t,s[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(s)):$(Object(s)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(s,t))})}return e}d(X,"contextType",f);var ee={onAnimationChanged:"animation_changed",onClick:"click",onClickableChanged:"clickable_changed",onCursorChanged:"cursor_changed",onDblClick:"dblclick",onDrag:"drag",onDragEnd:"dragend",onDraggableChanged:"draggable_changed",onDragStart:"dragstart",onFlatChanged:"flat_changed",onIconChanged:"icon_changed",onMouseDown:"mousedown",onMouseOut:"mouseout",onMouseOver:"mouseover",onMouseUp:"mouseup",onPositionChanged:"position_changed",onRightClick:"rightclick",onShapeChanged:"shape_changed",onTitleChanged:"title_changed",onVisibleChanged:"visible_changed",onZindexChanged:"zindex_changed"},et={animation(e,t){e.setAnimation(t)},clickable(e,t){e.setClickable(t)},cursor(e,t){e.setCursor(t)},draggable(e,t){e.setDraggable(t)},icon(e,t){e.setIcon(t)},label(e,t){e.setLabel(t)},map(e,t){e.setMap(t)},opacity(e,t){e.setOpacity(t)},options(e,t){e.setOptions(t)},position(e,t){e.setPosition(t)},shape(e,t){e.setShape(t)},title(e,t){e.setTitle(t)},visible(e,t){e.setVisible(t)},zIndex(e,t){e.setZIndex(t)}},es={};(0,c.memo)(function(e){var{position:t,options:s,clusterer:n,noClustererRedraw:o,children:i,draggable:r,visible:a,animation:l,clickable:u,cursor:h,icon:g,label:d,opacity:m,shape:v,title:y,zIndex:L,onClick:b,onDblClick:E,onDrag:w,onDragEnd:x,onDragStart:C,onMouseOut:M,onMouseOver:k,onMouseUp:P,onMouseDown:O,onRightClick:S,onClickableChanged:I,onCursorChanged:j,onAnimationChanged:D,onDraggableChanged:_,onFlatChanged:B,onIconChanged:T,onPositionChanged:z,onShapeChanged:R,onTitleChanged:A,onVisibleChanged:U,onZindexChanged:Z,onLoad:V,onUnmount:W}=e,N=(0,c.useContext)(f),[H,G]=(0,c.useState)(null),[F,Y]=(0,c.useState)(null),[K,q]=(0,c.useState)(null),[J,X]=(0,c.useState)(null),[$,ee]=(0,c.useState)(null),[et,en]=(0,c.useState)(null),[eo,ei]=(0,c.useState)(null),[er,ea]=(0,c.useState)(null),[el,eu]=(0,c.useState)(null),[ep,ec]=(0,c.useState)(null),[eh,eg]=(0,c.useState)(null),[ed,em]=(0,c.useState)(null),[ev,ef]=(0,c.useState)(null),[ey,eL]=(0,c.useState)(null),[eb,eE]=(0,c.useState)(null),[ew,ex]=(0,c.useState)(null),[eC,eM]=(0,c.useState)(null),[ek,eP]=(0,c.useState)(null),[eO,eS]=(0,c.useState)(null),[eI,ej]=(0,c.useState)(null),[eD,e_]=(0,c.useState)(null),[eB,eT]=(0,c.useState)(null);(0,c.useEffect)(()=>{null!==H&&H.setMap(N)},[N]),(0,c.useEffect)(()=>{void 0!==s&&null!==H&&H.setOptions(s)},[H,s]),(0,c.useEffect)(()=>{void 0!==r&&null!==H&&H.setDraggable(r)},[H,r]),(0,c.useEffect)(()=>{t&&null!==H&&H.setPosition(t)},[H,t]),(0,c.useEffect)(()=>{void 0!==a&&null!==H&&H.setVisible(a)},[H,a]),(0,c.useEffect)(()=>{null==H||H.setAnimation(l)},[H,l]),(0,c.useEffect)(()=>{H&&void 0!==u&&H.setClickable(u)},[H,u]),(0,c.useEffect)(()=>{H&&void 0!==h&&H.setCursor(h)},[H,h]),(0,c.useEffect)(()=>{H&&void 0!==g&&H.setIcon(g)},[H,g]),(0,c.useEffect)(()=>{H&&void 0!==d&&H.setLabel(d)},[H,d]),(0,c.useEffect)(()=>{H&&void 0!==m&&H.setOpacity(m)},[H,m]),(0,c.useEffect)(()=>{H&&void 0!==v&&H.setShape(v)},[H,v]),(0,c.useEffect)(()=>{H&&void 0!==y&&H.setTitle(y)},[H,y]),(0,c.useEffect)(()=>{H&&void 0!==L&&H.setZIndex(L)},[H,L]),(0,c.useEffect)(()=>{H&&E&&(null!==F&&google.maps.event.removeListener(F),Y(google.maps.event.addListener(H,"dblclick",E)))},[E]),(0,c.useEffect)(()=>{H&&x&&(null!==K&&google.maps.event.removeListener(K),q(google.maps.event.addListener(H,"dragend",x)))},[x]),(0,c.useEffect)(()=>{H&&C&&(null!==J&&google.maps.event.removeListener(J),X(google.maps.event.addListener(H,"dragstart",C)))},[C]),(0,c.useEffect)(()=>{H&&O&&(null!==$&&google.maps.event.removeListener($),ee(google.maps.event.addListener(H,"mousedown",O)))},[O]),(0,c.useEffect)(()=>{H&&M&&(null!==et&&google.maps.event.removeListener(et),en(google.maps.event.addListener(H,"mouseout",M)))},[M]),(0,c.useEffect)(()=>{H&&k&&(null!==eo&&google.maps.event.removeListener(eo),ei(google.maps.event.addListener(H,"mouseover",k)))},[k]),(0,c.useEffect)(()=>{H&&P&&(null!==er&&google.maps.event.removeListener(er),ea(google.maps.event.addListener(H,"mouseup",P)))},[P]),(0,c.useEffect)(()=>{H&&S&&(null!==el&&google.maps.event.removeListener(el),eu(google.maps.event.addListener(H,"rightclick",S)))},[S]),(0,c.useEffect)(()=>{H&&b&&(null!==ep&&google.maps.event.removeListener(ep),ec(google.maps.event.addListener(H,"click",b)))},[b]),(0,c.useEffect)(()=>{H&&w&&(null!==eh&&google.maps.event.removeListener(eh),eg(google.maps.event.addListener(H,"drag",w)))},[w]),(0,c.useEffect)(()=>{H&&I&&(null!==ed&&google.maps.event.removeListener(ed),em(google.maps.event.addListener(H,"clickable_changed",I)))},[I]),(0,c.useEffect)(()=>{H&&j&&(null!==ev&&google.maps.event.removeListener(ev),ef(google.maps.event.addListener(H,"cursor_changed",j)))},[j]),(0,c.useEffect)(()=>{H&&D&&(null!==ey&&google.maps.event.removeListener(ey),eL(google.maps.event.addListener(H,"animation_changed",D)))},[D]),(0,c.useEffect)(()=>{H&&_&&(null!==eb&&google.maps.event.removeListener(eb),eE(google.maps.event.addListener(H,"draggable_changed",_)))},[_]),(0,c.useEffect)(()=>{H&&B&&(null!==ew&&google.maps.event.removeListener(ew),ex(google.maps.event.addListener(H,"flat_changed",B)))},[B]),(0,c.useEffect)(()=>{H&&T&&(null!==eC&&google.maps.event.removeListener(eC),eM(google.maps.event.addListener(H,"icon_changed",T)))},[T]),(0,c.useEffect)(()=>{H&&z&&(null!==ek&&google.maps.event.removeListener(ek),eP(google.maps.event.addListener(H,"position_changed",z)))},[z]),(0,c.useEffect)(()=>{H&&R&&(null!==eO&&google.maps.event.removeListener(eO),eS(google.maps.event.addListener(H,"shape_changed",R)))},[R]),(0,c.useEffect)(()=>{H&&A&&(null!==eI&&google.maps.event.removeListener(eI),ej(google.maps.event.addListener(H,"title_changed",A)))},[A]),(0,c.useEffect)(()=>{H&&U&&(null!==eD&&google.maps.event.removeListener(eD),e_(google.maps.event.addListener(H,"visible_changed",U)))},[U]),(0,c.useEffect)(()=>{H&&Z&&(null!==eB&&google.maps.event.removeListener(eB),eT(google.maps.event.addListener(H,"zindex_changed",Z)))},[Z]),(0,c.useEffect)(()=>{var e=Q(Q(Q({},s||es),n?es:{map:N}),{},{position:t}),i=new google.maps.Marker(e);return n?n.addMarker(i,!!o):i.setMap(N),t&&i.setPosition(t),void 0!==a&&i.setVisible(a),void 0!==r&&i.setDraggable(r),void 0!==u&&i.setClickable(u),"string"==typeof h&&i.setCursor(h),g&&i.setIcon(g),void 0!==d&&i.setLabel(d),void 0!==m&&i.setOpacity(m),v&&i.setShape(v),"string"==typeof y&&i.setTitle(y),"number"==typeof L&&i.setZIndex(L),E&&Y(google.maps.event.addListener(i,"dblclick",E)),x&&q(google.maps.event.addListener(i,"dragend",x)),C&&X(google.maps.event.addListener(i,"dragstart",C)),O&&ee(google.maps.event.addListener(i,"mousedown",O)),M&&en(google.maps.event.addListener(i,"mouseout",M)),k&&ei(google.maps.event.addListener(i,"mouseover",k)),P&&ea(google.maps.event.addListener(i,"mouseup",P)),S&&eu(google.maps.event.addListener(i,"rightclick",S)),b&&ec(google.maps.event.addListener(i,"click",b)),w&&eg(google.maps.event.addListener(i,"drag",w)),I&&em(google.maps.event.addListener(i,"clickable_changed",I)),j&&ef(google.maps.event.addListener(i,"cursor_changed",j)),D&&eL(google.maps.event.addListener(i,"animation_changed",D)),_&&eE(google.maps.event.addListener(i,"draggable_changed",_)),B&&ex(google.maps.event.addListener(i,"flat_changed",B)),T&&eM(google.maps.event.addListener(i,"icon_changed",T)),z&&eP(google.maps.event.addListener(i,"position_changed",z)),R&&eS(google.maps.event.addListener(i,"shape_changed",R)),A&&ej(google.maps.event.addListener(i,"title_changed",A)),U&&e_(google.maps.event.addListener(i,"visible_changed",U)),Z&&eT(google.maps.event.addListener(i,"zindex_changed",Z)),G(i),V&&V(i),()=>{null!==F&&google.maps.event.removeListener(F),null!==K&&google.maps.event.removeListener(K),null!==J&&google.maps.event.removeListener(J),null!==$&&google.maps.event.removeListener($),null!==et&&google.maps.event.removeListener(et),null!==eo&&google.maps.event.removeListener(eo),null!==er&&google.maps.event.removeListener(er),null!==el&&google.maps.event.removeListener(el),null!==ep&&google.maps.event.removeListener(ep),null!==ed&&google.maps.event.removeListener(ed),null!==ev&&google.maps.event.removeListener(ev),null!==ey&&google.maps.event.removeListener(ey),null!==eb&&google.maps.event.removeListener(eb),null!==ew&&google.maps.event.removeListener(ew),null!==eC&&google.maps.event.removeListener(eC),null!==ek&&google.maps.event.removeListener(ek),null!==eI&&google.maps.event.removeListener(eI),null!==eD&&google.maps.event.removeListener(eD),null!==eB&&google.maps.event.removeListener(eB),W&&W(i),n?n.removeMarker(i,!!o):i&&i.setMap(null)}},[]);var ez=(0,c.useMemo)(()=>i?c.Children.map(i,e=>(0,c.isValidElement)(e)?(0,c.cloneElement)(e,{anchor:H}):e):null,[i,H]);return(0,p.jsx)(p.Fragment,{children:ez})||null});class en extends c.PureComponent{constructor(){super(...arguments),d(this,"registeredEvents",[])}componentDidMount(){var e=this;return M(function*(){var t=Q(Q(Q({},e.props.options||es),e.props.clusterer?es:{map:e.context}),{},{position:e.props.position});e.marker=new google.maps.Marker(t),e.props.clusterer?e.props.clusterer.addMarker(e.marker,!!e.props.noClustererRedraw):e.marker.setMap(e.context),e.registeredEvents=b({updaterMap:et,eventMap:ee,prevProps:{},nextProps:e.props,instance:e.marker}),e.props.onLoad&&e.props.onLoad(e.marker)})()}componentDidUpdate(e){this.marker&&(L(this.registeredEvents),this.registeredEvents=b({updaterMap:et,eventMap:ee,prevProps:e,nextProps:this.props,instance:this.marker}))}componentWillUnmount(){this.marker&&(this.props.onUnmount&&this.props.onUnmount(this.marker),L(this.registeredEvents),this.props.clusterer?this.props.clusterer.removeMarker(this.marker,!!this.props.noClustererRedraw):this.marker&&this.marker.setMap(null))}render(){return(this.props.children?c.Children.map(this.props.children,e=>(0,c.isValidElement)(e)?(0,c.cloneElement)(e,{anchor:this.marker}):e):null)||null}}d(en,"contextType",f);var eo=function(){function e(t,s){t.getClusterer().extend(e,google.maps.OverlayView),this.cluster=t,this.clusterClassName=this.cluster.getClusterer().getClusterClass(),this.className=this.clusterClassName,this.styles=s,this.center=void 0,this.div=null,this.sums=null,this.visible=!1,this.boundsChangedListener=null,this.url="",this.height=0,this.width=0,this.anchorText=[0,0],this.anchorIcon=[0,0],this.textColor="black",this.textSize=11,this.textDecoration="none",this.fontWeight="bold",this.fontStyle="normal",this.fontFamily="Arial,sans-serif",this.backgroundPosition="0 0",this.cMouseDownInCluster=null,this.cDraggingMapByCluster=null,this.timeOut=null,this.setMap(t.getMap()),this.onBoundsChanged=this.onBoundsChanged.bind(this),this.onMouseDown=this.onMouseDown.bind(this),this.onClick=this.onClick.bind(this),this.onMouseOver=this.onMouseOver.bind(this),this.onMouseOut=this.onMouseOut.bind(this),this.onAdd=this.onAdd.bind(this),this.onRemove=this.onRemove.bind(this),this.draw=this.draw.bind(this),this.hide=this.hide.bind(this),this.show=this.show.bind(this),this.useStyle=this.useStyle.bind(this),this.setCenter=this.setCenter.bind(this),this.getPosFromLatLng=this.getPosFromLatLng.bind(this)}return e.prototype.onBoundsChanged=function(){this.cDraggingMapByCluster=this.cMouseDownInCluster},e.prototype.onMouseDown=function(){this.cMouseDownInCluster=!0,this.cDraggingMapByCluster=!1},e.prototype.onClick=function(e){if(this.cMouseDownInCluster=!1,!this.cDraggingMapByCluster){var t=this.cluster.getClusterer();if(google.maps.event.trigger(t,"click",this.cluster),google.maps.event.trigger(t,"clusterclick",this.cluster),t.getZoomOnClick()){var s=t.getMaxZoom(),n=this.cluster.getBounds(),o=t.getMap();null!==o&&"fitBounds"in o&&o.fitBounds(n),this.timeOut=window.setTimeout(function(){var e=t.getMap();if(null!==e){"fitBounds"in e&&e.fitBounds(n);var o=e.getZoom()||0;null!==s&&o>s&&e.setZoom(s+1)}},100)}e.cancelBubble=!0,e.stopPropagation&&e.stopPropagation()}},e.prototype.onMouseOver=function(){google.maps.event.trigger(this.cluster.getClusterer(),"mouseover",this.cluster)},e.prototype.onMouseOut=function(){google.maps.event.trigger(this.cluster.getClusterer(),"mouseout",this.cluster)},e.prototype.onAdd=function(){this.div=document.createElement("div"),this.div.className=this.className,this.visible&&this.show(),null==(e=this.getPanes())||e.overlayMouseTarget.appendChild(this.div);var e,t=this.getMap();null!==t&&(this.boundsChangedListener=google.maps.event.addListener(t,"bounds_changed",this.onBoundsChanged),this.div.addEventListener("mousedown",this.onMouseDown),this.div.addEventListener("click",this.onClick),this.div.addEventListener("mouseover",this.onMouseOver),this.div.addEventListener("mouseout",this.onMouseOut))},e.prototype.onRemove=function(){this.div&&this.div.parentNode&&(this.hide(),null!==this.boundsChangedListener&&google.maps.event.removeListener(this.boundsChangedListener),this.div.removeEventListener("mousedown",this.onMouseDown),this.div.removeEventListener("click",this.onClick),this.div.removeEventListener("mouseover",this.onMouseOver),this.div.removeEventListener("mouseout",this.onMouseOut),this.div.parentNode.removeChild(this.div),null!==this.timeOut&&(window.clearTimeout(this.timeOut),this.timeOut=null),this.div=null)},e.prototype.draw=function(){if(this.visible&&null!==this.div&&this.center){var e=this.getPosFromLatLng(this.center);this.div.style.top=null!==e?"".concat(e.y,"px"):"0",this.div.style.left=null!==e?"".concat(e.x,"px"):"0"}},e.prototype.hide=function(){this.div&&(this.div.style.display="none"),this.visible=!1},e.prototype.show=function(){var e,t,s,n,o,i;if(this.div&&this.center){var r=null===this.sums||void 0===this.sums.title||""===this.sums.title?this.cluster.getClusterer().getTitle():this.sums.title,a=this.backgroundPosition.split(" "),l=parseInt((null==(e=a[0])?void 0:e.replace(/^\s+|\s+$/g,""))||"0",10),u=parseInt((null==(t=a[1])?void 0:t.replace(/^\s+|\s+$/g,""))||"0",10),p=this.getPosFromLatLng(this.center);this.div.className=this.className,this.div.setAttribute("style","cursor: pointer; position: absolute; top: ".concat(null!==p?"".concat(p.y,"px"):"0","; left: ").concat(null!==p?"".concat(p.x,"px"):"0","; width: ").concat(this.width,"px; height: ").concat(this.height,"px; "));var c=document.createElement("img");c.alt=r,c.src=this.url,c.width=this.width,c.height=this.height,c.setAttribute("style","position: absolute; top: ".concat(u,"px; left: ").concat(l,"px")),this.cluster.getClusterer().enableRetinaIcons||(c.style.clip="rect(-".concat(u,"px, -").concat(l+this.width,"px, -").concat(u+this.height,", -").concat(l,")"));var h=document.createElement("div");h.setAttribute("style","position: absolute; top: ".concat(this.anchorText[0],"px; left: ").concat(this.anchorText[1],"px; color: ").concat(this.textColor,"; font-size: ").concat(this.textSize,"px; font-family: ").concat(this.fontFamily,"; font-weight: ").concat(this.fontWeight,"; fontStyle: ").concat(this.fontStyle,"; text-decoration: ").concat(this.textDecoration,"; text-align: center; width: ").concat(this.width,"px; line-height: ").concat(this.height,"px")),(null==(s=this.sums)?void 0:s.text)&&(h.innerText="".concat(null==(n=this.sums)?void 0:n.text)),(null==(o=this.sums)?void 0:o.html)&&(h.innerHTML="".concat(null==(i=this.sums)?void 0:i.html)),this.div.innerHTML="",this.div.appendChild(c),this.div.appendChild(h),this.div.title=r,this.div.style.display=""}this.visible=!0},e.prototype.useStyle=function(e){this.sums=e;var t=this.cluster.getClusterer().getStyles(),s=t[Math.min(t.length-1,Math.max(0,e.index-1))];s&&(this.url=s.url,this.height=s.height,this.width=s.width,s.className&&(this.className="".concat(this.clusterClassName," ").concat(s.className)),this.anchorText=s.anchorText||[0,0],this.anchorIcon=s.anchorIcon||[this.height/2,this.width/2],this.textColor=s.textColor||"black",this.textSize=s.textSize||11,this.textDecoration=s.textDecoration||"none",this.fontWeight=s.fontWeight||"bold",this.fontStyle=s.fontStyle||"normal",this.fontFamily=s.fontFamily||"Arial,sans-serif",this.backgroundPosition=s.backgroundPosition||"0 0")},e.prototype.setCenter=function(e){this.center=e},e.prototype.getPosFromLatLng=function(e){var t=this.getProjection().fromLatLngToDivPixel(e);return null!==t&&(t.x-=this.anchorIcon[1],t.y-=this.anchorIcon[0]),t},e}(),ei=function(){function e(e){this.markerClusterer=e,this.map=this.markerClusterer.getMap(),this.gridSize=this.markerClusterer.getGridSize(),this.minClusterSize=this.markerClusterer.getMinimumClusterSize(),this.averageCenter=this.markerClusterer.getAverageCenter(),this.markers=[],this.center=void 0,this.bounds=null,this.clusterIcon=new eo(this,this.markerClusterer.getStyles()),this.getSize=this.getSize.bind(this),this.getMarkers=this.getMarkers.bind(this),this.getCenter=this.getCenter.bind(this),this.getMap=this.getMap.bind(this),this.getClusterer=this.getClusterer.bind(this),this.getBounds=this.getBounds.bind(this),this.remove=this.remove.bind(this),this.addMarker=this.addMarker.bind(this),this.isMarkerInClusterBounds=this.isMarkerInClusterBounds.bind(this),this.calculateBounds=this.calculateBounds.bind(this),this.updateIcon=this.updateIcon.bind(this),this.isMarkerAlreadyAdded=this.isMarkerAlreadyAdded.bind(this)}return e.prototype.getSize=function(){return this.markers.length},e.prototype.getMarkers=function(){return this.markers},e.prototype.getCenter=function(){return this.center},e.prototype.getMap=function(){return this.map},e.prototype.getClusterer=function(){return this.markerClusterer},e.prototype.getBounds=function(){for(var e=new google.maps.LatLngBounds(this.center,this.center),t=this.getMarkers(),s=0;s<t.length;s++){var n=t[s].getPosition();n&&e.extend(n)}return e},e.prototype.remove=function(){this.clusterIcon.setMap(null),this.markers=[],delete this.markers},e.prototype.addMarker=function(e){if(this.isMarkerAlreadyAdded(e))return!1;if(this.center){if(this.averageCenter){var t=e.getPosition();if(t){var s=this.markers.length+1;this.center=new google.maps.LatLng((this.center.lat()*(s-1)+t.lat())/s,(this.center.lng()*(s-1)+t.lng())/s),this.calculateBounds()}}}else{var n,t=e.getPosition();t&&(this.center=t,this.calculateBounds())}e.isAdded=!0,this.markers.push(e);var o=this.markers.length,i=this.markerClusterer.getMaxZoom(),r=null==(n=this.map)?void 0:n.getZoom();if(null!==i&&void 0!==r&&r>i)e.getMap()!==this.map&&e.setMap(this.map);else if(o<this.minClusterSize)e.getMap()!==this.map&&e.setMap(this.map);else if(o===this.minClusterSize)for(var a=0,l=this.markers;a<l.length;a++)l[a].setMap(null);else e.setMap(null);return!0},e.prototype.isMarkerInClusterBounds=function(e){if(null!==this.bounds){var t=e.getPosition();if(t)return this.bounds.contains(t)}return!1},e.prototype.calculateBounds=function(){this.bounds=this.markerClusterer.getExtendedBounds(new google.maps.LatLngBounds(this.center,this.center))},e.prototype.updateIcon=function(){var e,t=this.markers.length,s=this.markerClusterer.getMaxZoom(),n=null==(e=this.map)?void 0:e.getZoom();if(null!==s&&void 0!==n&&n>s||t<this.minClusterSize)return void this.clusterIcon.hide();this.center&&this.clusterIcon.setCenter(this.center),this.clusterIcon.useStyle(this.markerClusterer.getCalculator()(this.markers,this.markerClusterer.getStyles().length)),this.clusterIcon.show()},e.prototype.isMarkerAlreadyAdded=function(e){if(this.markers.includes)return this.markers.includes(e);for(var t=0;t<this.markers.length;t++)if(e===this.markers[t])return!0;return!1},e}();function er(e,t){var s=e.length,n=Math.min(s.toString().length,t);return{text:s.toString(),index:n,title:""}}var ea=[53,56,66,78,90],el=function(){function e(t,s,n){void 0===s&&(s=[]),void 0===n&&(n={}),this.getMinimumClusterSize=this.getMinimumClusterSize.bind(this),this.setMinimumClusterSize=this.setMinimumClusterSize.bind(this),this.getEnableRetinaIcons=this.getEnableRetinaIcons.bind(this),this.setEnableRetinaIcons=this.setEnableRetinaIcons.bind(this),this.addToClosestCluster=this.addToClosestCluster.bind(this),this.getImageExtension=this.getImageExtension.bind(this),this.setImageExtension=this.setImageExtension.bind(this),this.getExtendedBounds=this.getExtendedBounds.bind(this),this.getAverageCenter=this.getAverageCenter.bind(this),this.setAverageCenter=this.setAverageCenter.bind(this),this.getTotalClusters=this.getTotalClusters.bind(this),this.fitMapToMarkers=this.fitMapToMarkers.bind(this),this.getIgnoreHidden=this.getIgnoreHidden.bind(this),this.setIgnoreHidden=this.setIgnoreHidden.bind(this),this.getClusterClass=this.getClusterClass.bind(this),this.setClusterClass=this.setClusterClass.bind(this),this.getTotalMarkers=this.getTotalMarkers.bind(this),this.getZoomOnClick=this.getZoomOnClick.bind(this),this.setZoomOnClick=this.setZoomOnClick.bind(this),this.getBatchSizeIE=this.getBatchSizeIE.bind(this),this.setBatchSizeIE=this.setBatchSizeIE.bind(this),this.createClusters=this.createClusters.bind(this),this.onZoomChanged=this.onZoomChanged.bind(this),this.getImageSizes=this.getImageSizes.bind(this),this.setImageSizes=this.setImageSizes.bind(this),this.getCalculator=this.getCalculator.bind(this),this.setCalculator=this.setCalculator.bind(this),this.removeMarkers=this.removeMarkers.bind(this),this.resetViewport=this.resetViewport.bind(this),this.getImagePath=this.getImagePath.bind(this),this.setImagePath=this.setImagePath.bind(this),this.pushMarkerTo=this.pushMarkerTo.bind(this),this.removeMarker=this.removeMarker.bind(this),this.clearMarkers=this.clearMarkers.bind(this),this.setupStyles=this.setupStyles.bind(this),this.getGridSize=this.getGridSize.bind(this),this.setGridSize=this.setGridSize.bind(this),this.getClusters=this.getClusters.bind(this),this.getMaxZoom=this.getMaxZoom.bind(this),this.setMaxZoom=this.setMaxZoom.bind(this),this.getMarkers=this.getMarkers.bind(this),this.addMarkers=this.addMarkers.bind(this),this.getStyles=this.getStyles.bind(this),this.setStyles=this.setStyles.bind(this),this.addMarker=this.addMarker.bind(this),this.onRemove=this.onRemove.bind(this),this.getTitle=this.getTitle.bind(this),this.setTitle=this.setTitle.bind(this),this.repaint=this.repaint.bind(this),this.onIdle=this.onIdle.bind(this),this.redraw=this.redraw.bind(this),this.onAdd=this.onAdd.bind(this),this.draw=this.draw.bind(this),this.extend=this.extend.bind(this),this.extend(e,google.maps.OverlayView),this.markers=[],this.clusters=[],this.listeners=[],this.activeMap=null,this.ready=!1,this.gridSize=n.gridSize||60,this.minClusterSize=n.minimumClusterSize||2,this.maxZoom=n.maxZoom||null,this.styles=n.styles||[],this.title=n.title||"",this.zoomOnClick=!0,void 0!==n.zoomOnClick&&(this.zoomOnClick=n.zoomOnClick),this.averageCenter=!1,void 0!==n.averageCenter&&(this.averageCenter=n.averageCenter),this.ignoreHidden=!1,void 0!==n.ignoreHidden&&(this.ignoreHidden=n.ignoreHidden),this.enableRetinaIcons=!1,void 0!==n.enableRetinaIcons&&(this.enableRetinaIcons=n.enableRetinaIcons),this.imagePath=n.imagePath||"https://developers.google.com/maps/documentation/javascript/examples/markerclusterer/m",this.imageExtension=n.imageExtension||"png",this.imageSizes=n.imageSizes||ea,this.calculator=n.calculator||er,this.batchSize=n.batchSize||2e3,this.batchSizeIE=n.batchSizeIE||500,this.clusterClass=n.clusterClass||"cluster",-1!==navigator.userAgent.toLowerCase().indexOf("msie")&&(this.batchSize=this.batchSizeIE),this.timerRefStatic=null,this.setupStyles(),this.addMarkers(s,!0),this.setMap(t)}return e.prototype.onZoomChanged=function(){var e,t;this.resetViewport(!1),((null==(e=this.getMap())?void 0:e.getZoom())===(this.get("minZoom")||0)||(null==(t=this.getMap())?void 0:t.getZoom())===this.get("maxZoom"))&&google.maps.event.trigger(this,"idle")},e.prototype.onIdle=function(){this.redraw()},e.prototype.onAdd=function(){var e=this.getMap();this.activeMap=e,this.ready=!0,this.repaint(),null!==e&&(this.listeners=[google.maps.event.addListener(e,"zoom_changed",this.onZoomChanged),google.maps.event.addListener(e,"idle",this.onIdle)])},e.prototype.onRemove=function(){for(var e=0,t=this.markers;e<t.length;e++){var s=t[e];s.getMap()!==this.activeMap&&s.setMap(this.activeMap)}for(var n=0,o=this.clusters;n<o.length;n++)o[n].remove();this.clusters=[];for(var i=0,r=this.listeners;i<r.length;i++){var a=r[i];google.maps.event.removeListener(a)}this.listeners=[],this.activeMap=null,this.ready=!1},e.prototype.draw=function(){},e.prototype.getMap=function(){return null},e.prototype.getPanes=function(){return null},e.prototype.getProjection=function(){return{fromContainerPixelToLatLng:function(){return null},fromDivPixelToLatLng:function(){return null},fromLatLngToContainerPixel:function(){return null},fromLatLngToDivPixel:function(){return null},getVisibleRegion:function(){return null},getWorldWidth:function(){return 0}}},e.prototype.setMap=function(){},e.prototype.addListener=function(){return{remove:function(){}}},e.prototype.bindTo=function(){},e.prototype.get=function(){},e.prototype.notify=function(){},e.prototype.set=function(){},e.prototype.setValues=function(){},e.prototype.unbind=function(){},e.prototype.unbindAll=function(){},e.prototype.setupStyles=function(){if(!(this.styles.length>0))for(var e=0;e<this.imageSizes.length;e++)this.styles.push({url:"".concat(this.imagePath+(e+1),".").concat(this.imageExtension),height:this.imageSizes[e]||0,width:this.imageSizes[e]||0})},e.prototype.fitMapToMarkers=function(){for(var e=this.getMarkers(),t=new google.maps.LatLngBounds,s=0;s<e.length;s++){var n=e[s].getPosition();n&&t.extend(n)}var o=this.getMap();null!==o&&"fitBounds"in o&&o.fitBounds(t)},e.prototype.getGridSize=function(){return this.gridSize},e.prototype.setGridSize=function(e){this.gridSize=e},e.prototype.getMinimumClusterSize=function(){return this.minClusterSize},e.prototype.setMinimumClusterSize=function(e){this.minClusterSize=e},e.prototype.getMaxZoom=function(){return this.maxZoom},e.prototype.setMaxZoom=function(e){this.maxZoom=e},e.prototype.getStyles=function(){return this.styles},e.prototype.setStyles=function(e){this.styles=e},e.prototype.getTitle=function(){return this.title},e.prototype.setTitle=function(e){this.title=e},e.prototype.getZoomOnClick=function(){return this.zoomOnClick},e.prototype.setZoomOnClick=function(e){this.zoomOnClick=e},e.prototype.getAverageCenter=function(){return this.averageCenter},e.prototype.setAverageCenter=function(e){this.averageCenter=e},e.prototype.getIgnoreHidden=function(){return this.ignoreHidden},e.prototype.setIgnoreHidden=function(e){this.ignoreHidden=e},e.prototype.getEnableRetinaIcons=function(){return this.enableRetinaIcons},e.prototype.setEnableRetinaIcons=function(e){this.enableRetinaIcons=e},e.prototype.getImageExtension=function(){return this.imageExtension},e.prototype.setImageExtension=function(e){this.imageExtension=e},e.prototype.getImagePath=function(){return this.imagePath},e.prototype.setImagePath=function(e){this.imagePath=e},e.prototype.getImageSizes=function(){return this.imageSizes},e.prototype.setImageSizes=function(e){this.imageSizes=e},e.prototype.getCalculator=function(){return this.calculator},e.prototype.setCalculator=function(e){this.calculator=e},e.prototype.getBatchSizeIE=function(){return this.batchSizeIE},e.prototype.setBatchSizeIE=function(e){this.batchSizeIE=e},e.prototype.getClusterClass=function(){return this.clusterClass},e.prototype.setClusterClass=function(e){this.clusterClass=e},e.prototype.getMarkers=function(){return this.markers},e.prototype.getTotalMarkers=function(){return this.markers.length},e.prototype.getClusters=function(){return this.clusters},e.prototype.getTotalClusters=function(){return this.clusters.length},e.prototype.addMarker=function(e,t){this.pushMarkerTo(e),t||this.redraw()},e.prototype.addMarkers=function(e,t){for(var s in e)if(Object.prototype.hasOwnProperty.call(e,s)){var n=e[s];n&&this.pushMarkerTo(n)}t||this.redraw()},e.prototype.pushMarkerTo=function(e){var t=this;e.getDraggable()&&google.maps.event.addListener(e,"dragend",function(){t.ready&&(e.isAdded=!1,t.repaint())}),e.isAdded=!1,this.markers.push(e)},e.prototype.removeMarker_=function(e){var t=-1;if(this.markers.indexOf)t=this.markers.indexOf(e);else for(var s=0;s<this.markers.length;s++)if(e===this.markers[s]){t=s;break}return -1!==t&&(e.setMap(null),this.markers.splice(t,1),!0)},e.prototype.removeMarker=function(e,t){var s=this.removeMarker_(e);return!t&&s&&this.repaint(),s},e.prototype.removeMarkers=function(e,t){for(var s=!1,n=0;n<e.length;n++){var o=e[n];s=s||this.removeMarker_(o)}return!t&&s&&this.repaint(),s},e.prototype.clearMarkers=function(){this.resetViewport(!0),this.markers=[]},e.prototype.repaint=function(){var e=this.clusters.slice();this.clusters=[],this.resetViewport(!1),this.redraw(),setTimeout(function(){for(var t=0;t<e.length;t++)e[t].remove()},0)},e.prototype.getExtendedBounds=function(e){var t=this.getProjection(),s=t.fromLatLngToDivPixel(new google.maps.LatLng(e.getNorthEast().lat(),e.getNorthEast().lng()));null!==s&&(s.x+=this.gridSize,s.y-=this.gridSize);var n=t.fromLatLngToDivPixel(new google.maps.LatLng(e.getSouthWest().lat(),e.getSouthWest().lng()));if(null!==n&&(n.x-=this.gridSize,n.y+=this.gridSize),null!==s){var o=t.fromDivPixelToLatLng(s);null!==o&&e.extend(o)}if(null!==n){var i=t.fromDivPixelToLatLng(n);null!==i&&e.extend(i)}return e},e.prototype.redraw=function(){this.createClusters(0)},e.prototype.resetViewport=function(e){for(var t=0,s=this.clusters;t<s.length;t++)s[t].remove();this.clusters=[];for(var n=0,o=this.markers;n<o.length;n++){var i=o[n];i.isAdded=!1,e&&i.setMap(null)}},e.prototype.distanceBetweenPoints=function(e,t){var s=(t.lat()-e.lat())*Math.PI/180,n=(t.lng()-e.lng())*Math.PI/180,o=Math.sin(s/2)*Math.sin(s/2)+Math.cos(e.lat()*Math.PI/180)*Math.cos(t.lat()*Math.PI/180)*Math.sin(n/2)*Math.sin(n/2);return 2*Math.atan2(Math.sqrt(o),Math.sqrt(1-o))*6371},e.prototype.isMarkerInBounds=function(e,t){var s=e.getPosition();return!!s&&t.contains(s)},e.prototype.addToClosestCluster=function(e){for(var t,s=4e4,n=null,o=0,i=this.clusters;o<i.length;o++){var r=(t=i[o]).getCenter(),a=e.getPosition();if(r&&a){var l=this.distanceBetweenPoints(r,a);l<s&&(s=l,n=t)}}n&&n.isMarkerInClusterBounds(e)?n.addMarker(e):((t=new ei(this)).addMarker(e),this.clusters.push(t))},e.prototype.createClusters=function(e){var t=this;if(this.ready){0===e&&(google.maps.event.trigger(this,"clusteringbegin",this),null!==this.timerRefStatic&&(window.clearTimeout(this.timerRefStatic),delete this.timerRefStatic));for(var s=this.getMap(),n=null!==s&&("getBounds"in s)?s.getBounds():null,o=((null==s?void 0:s.getZoom())||0)>3?new google.maps.LatLngBounds(null==n?void 0:n.getSouthWest(),null==n?void 0:n.getNorthEast()):new google.maps.LatLngBounds(new google.maps.LatLng(85.02070771743472,-178.48388434375),new google.maps.LatLng(-85.08136444384544,178.00048865625)),i=this.getExtendedBounds(o),r=Math.min(e+this.batchSize,this.markers.length),a=e;a<r;a++){var l=this.markers[a];l&&!l.isAdded&&this.isMarkerInBounds(l,i)&&(!this.ignoreHidden||this.ignoreHidden&&l.getVisible())&&this.addToClosestCluster(l)}if(r<this.markers.length)this.timerRefStatic=window.setTimeout(function(){t.createClusters(r)},0);else{this.timerRefStatic=null,google.maps.event.trigger(this,"clusteringend",this);for(var u=0,p=this.clusters;u<p.length;u++)p[u].updateIcon()}}},e.prototype.extend=function(e,t){return(function(e){for(var t in e.prototype)this.prototype[t]=e.prototype[t];return this}).apply(e,[t])},e}();function eu(e,t){var s=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),s.push.apply(s,n)}return s}var ep={onClick:"click",onClusteringBegin:"clusteringbegin",onClusteringEnd:"clusteringend",onMouseOut:"mouseout",onMouseOver:"mouseover"},ec={averageCenter(e,t){e.setAverageCenter(t)},batchSizeIE(e,t){e.setBatchSizeIE(t)},calculator(e,t){e.setCalculator(t)},clusterClass(e,t){e.setClusterClass(t)},enableRetinaIcons(e,t){e.setEnableRetinaIcons(t)},gridSize(e,t){e.setGridSize(t)},ignoreHidden(e,t){e.setIgnoreHidden(t)},imageExtension(e,t){e.setImageExtension(t)},imagePath(e,t){e.setImagePath(t)},imageSizes(e,t){e.setImageSizes(t)},maxZoom(e,t){e.setMaxZoom(t)},minimumClusterSize(e,t){e.setMinimumClusterSize(t)},styles(e,t){e.setStyles(t)},title(e,t){e.setTitle(t)},zoomOnClick(e,t){e.setZoomOnClick(t)}},eh={};(0,c.memo)(function(e){var{children:t,options:s,averageCenter:n,batchSizeIE:o,calculator:i,clusterClass:r,enableRetinaIcons:a,gridSize:l,ignoreHidden:u,imageExtension:p,imagePath:h,imageSizes:g,maxZoom:m,minimumClusterSize:v,styles:y,title:L,zoomOnClick:b,onClick:E,onClusteringBegin:w,onClusteringEnd:x,onMouseOver:C,onMouseOut:M,onLoad:k,onUnmount:P}=e,[O,S]=(0,c.useState)(null),I=(0,c.useContext)(f),[j,D]=(0,c.useState)(null),[_,B]=(0,c.useState)(null),[T,z]=(0,c.useState)(null),[R,A]=(0,c.useState)(null),[U,Z]=(0,c.useState)(null);return(0,c.useEffect)(()=>{O&&M&&(null!==R&&google.maps.event.removeListener(R),A(google.maps.event.addListener(O,ep.onMouseOut,M)))},[M]),(0,c.useEffect)(()=>{O&&C&&(null!==U&&google.maps.event.removeListener(U),Z(google.maps.event.addListener(O,ep.onMouseOver,C)))},[C]),(0,c.useEffect)(()=>{O&&E&&(null!==j&&google.maps.event.removeListener(j),D(google.maps.event.addListener(O,ep.onClick,E)))},[E]),(0,c.useEffect)(()=>{O&&w&&(null!==_&&google.maps.event.removeListener(_),B(google.maps.event.addListener(O,ep.onClusteringBegin,w)))},[w]),(0,c.useEffect)(()=>{O&&x&&(null!==T&&google.maps.event.removeListener(T),B(google.maps.event.addListener(O,ep.onClusteringEnd,x)))},[x]),(0,c.useEffect)(()=>{void 0!==n&&null!==O&&ec.averageCenter(O,n)},[O,n]),(0,c.useEffect)(()=>{void 0!==o&&null!==O&&ec.batchSizeIE(O,o)},[O,o]),(0,c.useEffect)(()=>{void 0!==i&&null!==O&&ec.calculator(O,i)},[O,i]),(0,c.useEffect)(()=>{void 0!==r&&null!==O&&ec.clusterClass(O,r)},[O,r]),(0,c.useEffect)(()=>{void 0!==a&&null!==O&&ec.enableRetinaIcons(O,a)},[O,a]),(0,c.useEffect)(()=>{void 0!==l&&null!==O&&ec.gridSize(O,l)},[O,l]),(0,c.useEffect)(()=>{void 0!==u&&null!==O&&ec.ignoreHidden(O,u)},[O,u]),(0,c.useEffect)(()=>{void 0!==p&&null!==O&&ec.imageExtension(O,p)},[O,p]),(0,c.useEffect)(()=>{void 0!==h&&null!==O&&ec.imagePath(O,h)},[O,h]),(0,c.useEffect)(()=>{void 0!==g&&null!==O&&ec.imageSizes(O,g)},[O,g]),(0,c.useEffect)(()=>{void 0!==m&&null!==O&&ec.maxZoom(O,m)},[O,m]),(0,c.useEffect)(()=>{void 0!==v&&null!==O&&ec.minimumClusterSize(O,v)},[O,v]),(0,c.useEffect)(()=>{void 0!==y&&null!==O&&ec.styles(O,y)},[O,y]),(0,c.useEffect)(()=>{void 0!==L&&null!==O&&ec.title(O,L)},[O,L]),(0,c.useEffect)(()=>{void 0!==b&&null!==O&&ec.zoomOnClick(O,b)},[O,b]),(0,c.useEffect)(()=>{if(I){var e=new el(I,[],function(e){for(var t=1;t<arguments.length;t++){var s=null!=arguments[t]?arguments[t]:{};t%2?eu(Object(s),!0).forEach(function(t){d(e,t,s[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(s)):eu(Object(s)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(s,t))})}return e}({},s||eh));return n&&ec.averageCenter(e,n),o&&ec.batchSizeIE(e,o),i&&ec.calculator(e,i),r&&ec.clusterClass(e,r),a&&ec.enableRetinaIcons(e,a),l&&ec.gridSize(e,l),u&&ec.ignoreHidden(e,u),p&&ec.imageExtension(e,p),h&&ec.imagePath(e,h),g&&ec.imageSizes(e,g),m&&ec.maxZoom(e,m),v&&ec.minimumClusterSize(e,v),y&&ec.styles(e,y),L&&ec.title(e,L),b&&ec.zoomOnClick(e,b),M&&A(google.maps.event.addListener(e,ep.onMouseOut,M)),C&&Z(google.maps.event.addListener(e,ep.onMouseOver,C)),E&&D(google.maps.event.addListener(e,ep.onClick,E)),w&&B(google.maps.event.addListener(e,ep.onClusteringBegin,w)),x&&z(google.maps.event.addListener(e,ep.onClusteringEnd,x)),S(e),k&&k(e),()=>{null!==R&&google.maps.event.removeListener(R),null!==U&&google.maps.event.removeListener(U),null!==j&&google.maps.event.removeListener(j),null!==_&&google.maps.event.removeListener(_),null!==T&&google.maps.event.removeListener(T),P&&P(e)}}},[]),null!==O&&t(O)||null});class eg extends c.PureComponent{constructor(){super(...arguments),d(this,"registeredEvents",[]),d(this,"state",{markerClusterer:null}),d(this,"setClustererCallback",()=>{null!==this.state.markerClusterer&&this.props.onLoad&&this.props.onLoad(this.state.markerClusterer)})}componentDidMount(){if(this.context){var e=new el(this.context,[],this.props.options);this.registeredEvents=b({updaterMap:ec,eventMap:ep,prevProps:{},nextProps:this.props,instance:e}),this.setState(()=>({markerClusterer:e}),this.setClustererCallback)}}componentDidUpdate(e){this.state.markerClusterer&&(L(this.registeredEvents),this.registeredEvents=b({updaterMap:ec,eventMap:ep,prevProps:e,nextProps:this.props,instance:this.state.markerClusterer}))}componentWillUnmount(){null!==this.state.markerClusterer&&(this.props.onUnmount&&this.props.onUnmount(this.state.markerClusterer),L(this.registeredEvents),this.state.markerClusterer.setMap(null))}render(){return null!==this.state.markerClusterer?this.props.children(this.state.markerClusterer):null}}function ed(e){e.cancelBubble=!0,e.stopPropagation&&e.stopPropagation()}d(eg,"contextType",f);var em=function(){function e(t){void 0===t&&(t={}),this.getCloseClickHandler=this.getCloseClickHandler.bind(this),this.closeClickHandler=this.closeClickHandler.bind(this),this.createInfoBoxDiv=this.createInfoBoxDiv.bind(this),this.addClickHandler=this.addClickHandler.bind(this),this.getCloseBoxImg=this.getCloseBoxImg.bind(this),this.getBoxWidths=this.getBoxWidths.bind(this),this.setBoxStyle=this.setBoxStyle.bind(this),this.setPosition=this.setPosition.bind(this),this.getPosition=this.getPosition.bind(this),this.setOptions=this.setOptions.bind(this),this.setContent=this.setContent.bind(this),this.setVisible=this.setVisible.bind(this),this.getContent=this.getContent.bind(this),this.getVisible=this.getVisible.bind(this),this.setZIndex=this.setZIndex.bind(this),this.getZIndex=this.getZIndex.bind(this),this.onRemove=this.onRemove.bind(this),this.panBox=this.panBox.bind(this),this.extend=this.extend.bind(this),this.close=this.close.bind(this),this.draw=this.draw.bind(this),this.show=this.show.bind(this),this.hide=this.hide.bind(this),this.open=this.open.bind(this),this.extend(e,google.maps.OverlayView),this.content=t.content||"",this.disableAutoPan=t.disableAutoPan||!1,this.maxWidth=t.maxWidth||0,this.pixelOffset=t.pixelOffset||new google.maps.Size(0,0),this.position=t.position||new google.maps.LatLng(0,0),this.zIndex=t.zIndex||null,this.boxClass=t.boxClass||"infoBox",this.boxStyle=t.boxStyle||{},this.closeBoxMargin=t.closeBoxMargin||"2px",this.closeBoxURL=t.closeBoxURL||"http://www.google.com/intl/en_us/mapfiles/close.gif",""===t.closeBoxURL&&(this.closeBoxURL=""),this.infoBoxClearance=t.infoBoxClearance||new google.maps.Size(1,1),void 0===t.visible&&(void 0===t.isHidden?t.visible=!0:t.visible=!t.isHidden),this.isHidden=!t.visible,this.alignBottom=t.alignBottom||!1,this.pane=t.pane||"floatPane",this.enableEventPropagation=t.enableEventPropagation||!1,this.div=null,this.closeListener=null,this.moveListener=null,this.mapListener=null,this.contextListener=null,this.eventListeners=null,this.fixedWidthSet=null}return e.prototype.createInfoBoxDiv=function(){var e=this;if(!this.div){this.div=document.createElement("div"),this.setBoxStyle(),"string"==typeof this.content?this.div.innerHTML=this.getCloseBoxImg()+this.content:(this.div.innerHTML=this.getCloseBoxImg(),this.div.appendChild(this.content));var t=this.getPanes();if(null!==t&&t[this.pane].appendChild(this.div),this.addClickHandler(),this.div.style.width)this.fixedWidthSet=!0;else if(0!==this.maxWidth&&this.div.offsetWidth>this.maxWidth)this.div.style.width=this.maxWidth+"px",this.fixedWidthSet=!0;else{var s=this.getBoxWidths();this.div.style.width=this.div.offsetWidth-s.left-s.right+"px",this.fixedWidthSet=!1}if(this.panBox(this.disableAutoPan),!this.enableEventPropagation){this.eventListeners=[];for(var n=0,o=["mousedown","mouseover","mouseout","mouseup","click","dblclick","touchstart","touchend","touchmove"];n<o.length;n++){var i=o[n];this.eventListeners.push(google.maps.event.addListener(this.div,i,ed))}this.eventListeners.push(google.maps.event.addListener(this.div,"mouseover",function(){e.div&&(e.div.style.cursor="default")}))}this.contextListener=google.maps.event.addListener(this.div,"contextmenu",function(t){t.returnValue=!1,t.preventDefault&&t.preventDefault(),e.enableEventPropagation||ed(t)}),google.maps.event.trigger(this,"domready")}},e.prototype.getCloseBoxImg=function(){var e="";return""!==this.closeBoxURL&&(e='<img alt="" aria-hidden="true" src=\''+this.closeBoxURL+"' align=right style=' position: relative; cursor: pointer;"+(" margin: "+this.closeBoxMargin)+";'>"),e},e.prototype.addClickHandler=function(){this.closeListener=this.div&&this.div.firstChild&&""!==this.closeBoxURL?google.maps.event.addListener(this.div.firstChild,"click",this.getCloseClickHandler()):null},e.prototype.closeClickHandler=function(e){e.cancelBubble=!0,e.stopPropagation&&e.stopPropagation(),google.maps.event.trigger(this,"closeclick"),this.close()},e.prototype.getCloseClickHandler=function(){return this.closeClickHandler},e.prototype.panBox=function(e){if(this.div&&!e){var t=this.getMap();if(t instanceof google.maps.Map){var s=0,n=0,o=t.getBounds();o&&!o.contains(this.position)&&t.setCenter(this.position);var i=t.getDiv(),r=i.offsetWidth,a=i.offsetHeight,l=this.pixelOffset.width,u=this.pixelOffset.height,p=this.div.offsetWidth,c=this.div.offsetHeight,h=this.infoBoxClearance.width,g=this.infoBoxClearance.height,d=this.getProjection().fromLatLngToContainerPixel(this.position);null!==d&&(d.x<-l+h?s=d.x+l-h:d.x+p+l+h>r&&(s=d.x+p+l+h-r),this.alignBottom?d.y<-u+g+c?n=d.y+u-g-c:d.y+u+g>a&&(n=d.y+u+g-a):d.y<-u+g?n=d.y+u-g:d.y+c+u+g>a&&(n=d.y+c+u+g-a)),(0!==s||0!==n)&&t.panBy(s,n)}}},e.prototype.setBoxStyle=function(){if(this.div){this.div.className=this.boxClass,this.div.style.cssText="";var e=this.boxStyle;for(var t in e)Object.prototype.hasOwnProperty.call(e,t)&&(this.div.style[t]=e[t]);if(this.div.style.webkitTransform="translateZ(0)",void 0!==this.div.style.opacity&&""!==this.div.style.opacity){var s=parseFloat(this.div.style.opacity||"");this.div.style.msFilter='"progid:DXImageTransform.Microsoft.Alpha(Opacity='+100*s+')"',this.div.style.filter="alpha(opacity="+100*s+")"}this.div.style.position="absolute",this.div.style.visibility="hidden",null!==this.zIndex&&(this.div.style.zIndex=this.zIndex+""),this.div.style.overflow||(this.div.style.overflow="auto")}},e.prototype.getBoxWidths=function(){var e={top:0,bottom:0,left:0,right:0};if(!this.div)return e;if(document.defaultView){var t=this.div.ownerDocument,s=t&&t.defaultView?t.defaultView.getComputedStyle(this.div,""):null;s&&(e.top=parseInt(s.borderTopWidth||"",10)||0,e.bottom=parseInt(s.borderBottomWidth||"",10)||0,e.left=parseInt(s.borderLeftWidth||"",10)||0,e.right=parseInt(s.borderRightWidth||"",10)||0)}else if(document.documentElement.currentStyle){var n=this.div.currentStyle;n&&(e.top=parseInt(n.borderTopWidth||"",10)||0,e.bottom=parseInt(n.borderBottomWidth||"",10)||0,e.left=parseInt(n.borderLeftWidth||"",10)||0,e.right=parseInt(n.borderRightWidth||"",10)||0)}return e},e.prototype.onRemove=function(){this.div&&this.div.parentNode&&(this.div.parentNode.removeChild(this.div),this.div=null)},e.prototype.draw=function(){if(this.createInfoBoxDiv(),this.div){var e=this.getProjection().fromLatLngToDivPixel(this.position);null!==e&&(this.div.style.left=e.x+this.pixelOffset.width+"px",this.alignBottom?this.div.style.bottom=-(e.y+this.pixelOffset.height)+"px":this.div.style.top=e.y+this.pixelOffset.height+"px"),this.isHidden?this.div.style.visibility="hidden":this.div.style.visibility="visible"}},e.prototype.setOptions=function(e){void 0===e&&(e={}),void 0!==e.boxClass&&(this.boxClass=e.boxClass,this.setBoxStyle()),void 0!==e.boxStyle&&(this.boxStyle=e.boxStyle,this.setBoxStyle()),void 0!==e.content&&this.setContent(e.content),void 0!==e.disableAutoPan&&(this.disableAutoPan=e.disableAutoPan),void 0!==e.maxWidth&&(this.maxWidth=e.maxWidth),void 0!==e.pixelOffset&&(this.pixelOffset=e.pixelOffset),void 0!==e.alignBottom&&(this.alignBottom=e.alignBottom),void 0!==e.position&&this.setPosition(e.position),void 0!==e.zIndex&&this.setZIndex(e.zIndex),void 0!==e.closeBoxMargin&&(this.closeBoxMargin=e.closeBoxMargin),void 0!==e.closeBoxURL&&(this.closeBoxURL=e.closeBoxURL),void 0!==e.infoBoxClearance&&(this.infoBoxClearance=e.infoBoxClearance),void 0!==e.isHidden&&(this.isHidden=e.isHidden),void 0!==e.visible&&(this.isHidden=!e.visible),void 0!==e.enableEventPropagation&&(this.enableEventPropagation=e.enableEventPropagation),this.div&&this.draw()},e.prototype.setContent=function(e){this.content=e,this.div&&(this.closeListener&&(google.maps.event.removeListener(this.closeListener),this.closeListener=null),this.fixedWidthSet||(this.div.style.width=""),"string"==typeof e?this.div.innerHTML=this.getCloseBoxImg()+e:(this.div.innerHTML=this.getCloseBoxImg(),this.div.appendChild(e)),this.fixedWidthSet||(this.div.style.width=this.div.offsetWidth+"px","string"==typeof e?this.div.innerHTML=this.getCloseBoxImg()+e:(this.div.innerHTML=this.getCloseBoxImg(),this.div.appendChild(e))),this.addClickHandler()),google.maps.event.trigger(this,"content_changed")},e.prototype.setPosition=function(e){this.position=e,this.div&&this.draw(),google.maps.event.trigger(this,"position_changed")},e.prototype.setVisible=function(e){this.isHidden=!e,this.div&&(this.div.style.visibility=this.isHidden?"hidden":"visible")},e.prototype.setZIndex=function(e){this.zIndex=e,this.div&&(this.div.style.zIndex=e+""),google.maps.event.trigger(this,"zindex_changed")},e.prototype.getContent=function(){return this.content},e.prototype.getPosition=function(){return this.position},e.prototype.getZIndex=function(){return this.zIndex},e.prototype.getVisible=function(){return null!=this.getMap()&&!this.isHidden},e.prototype.show=function(){this.isHidden=!1,this.div&&(this.div.style.visibility="visible")},e.prototype.hide=function(){this.isHidden=!0,this.div&&(this.div.style.visibility="hidden")},e.prototype.open=function(e,t){var s=this;t&&(this.position=t.getPosition(),this.moveListener=google.maps.event.addListener(t,"position_changed",function(){var e=t.getPosition();s.setPosition(e)}),this.mapListener=google.maps.event.addListener(t,"map_changed",function(){s.setMap(t.map)})),this.setMap(e),this.div&&this.panBox()},e.prototype.close=function(){if(this.closeListener&&(google.maps.event.removeListener(this.closeListener),this.closeListener=null),this.eventListeners){for(var e=0,t=this.eventListeners;e<t.length;e++){var s=t[e];google.maps.event.removeListener(s)}this.eventListeners=null}this.moveListener&&(google.maps.event.removeListener(this.moveListener),this.moveListener=null),this.mapListener&&(google.maps.event.removeListener(this.mapListener),this.mapListener=null),this.contextListener&&(google.maps.event.removeListener(this.contextListener),this.contextListener=null),this.setMap(null)},e.prototype.extend=function(e,t){return(function(e){for(var t in e.prototype)Object.prototype.hasOwnProperty.call(this,t)||(this.prototype[t]=e.prototype[t]);return this}).apply(e,[t])},e}(),ev=["position"],ef=["position"];function ey(e,t){var s=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),s.push.apply(s,n)}return s}function eL(e){for(var t=1;t<arguments.length;t++){var s=null!=arguments[t]?arguments[t]:{};t%2?ey(Object(s),!0).forEach(function(t){d(e,t,s[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(s)):ey(Object(s)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(s,t))})}return e}var eb={onCloseClick:"closeclick",onContentChanged:"content_changed",onDomReady:"domready",onPositionChanged:"position_changed",onZindexChanged:"zindex_changed"},eE={options(e,t){e.setOptions(t)},position(e,t){t instanceof google.maps.LatLng?e.setPosition(t):e.setPosition(new google.maps.LatLng(t.lat,t.lng))},visible(e,t){e.setVisible(t)},zIndex(e,t){e.setZIndex(t)}},ew={};(0,c.memo)(function(e){var{children:t,anchor:s,options:n,position:o,zIndex:i,onCloseClick:r,onDomReady:a,onContentChanged:l,onPositionChanged:u,onZindexChanged:p,onLoad:g,onUnmount:d}=e,m=(0,c.useContext)(f),[y,L]=(0,c.useState)(null),[b,E]=(0,c.useState)(null),[w,x]=(0,c.useState)(null),[C,M]=(0,c.useState)(null),[k,P]=(0,c.useState)(null),[O,S]=(0,c.useState)(null),I=(0,c.useRef)(null);return(0,c.useEffect)(()=>{m&&null!==y&&(y.close(),s?y.open(m,s):y.getPosition()&&y.open(m))},[m,y,s]),(0,c.useEffect)(()=>{n&&null!==y&&y.setOptions(n)},[y,n]),(0,c.useEffect)(()=>{if(o&&null!==y){var e=o instanceof google.maps.LatLng?o:new google.maps.LatLng(o.lat,o.lng);y.setPosition(e)}},[o]),(0,c.useEffect)(()=>{"number"==typeof i&&null!==y&&y.setZIndex(i)},[i]),(0,c.useEffect)(()=>{y&&r&&(null!==b&&google.maps.event.removeListener(b),E(google.maps.event.addListener(y,"closeclick",r)))},[r]),(0,c.useEffect)(()=>{y&&a&&(null!==w&&google.maps.event.removeListener(w),x(google.maps.event.addListener(y,"domready",a)))},[a]),(0,c.useEffect)(()=>{y&&l&&(null!==C&&google.maps.event.removeListener(C),M(google.maps.event.addListener(y,"content_changed",l)))},[l]),(0,c.useEffect)(()=>{y&&u&&(null!==k&&google.maps.event.removeListener(k),P(google.maps.event.addListener(y,"position_changed",u)))},[u]),(0,c.useEffect)(()=>{y&&p&&(null!==O&&google.maps.event.removeListener(O),S(google.maps.event.addListener(y,"zindex_changed",p)))},[p]),(0,c.useEffect)(()=>{if(m){var e,t=n||ew,{position:o}=t,i=T(t,ev);!o||o instanceof google.maps.LatLng||(e=new google.maps.LatLng(o.lat,o.lng));var c=new em(eL(eL({},i),e?{position:e}:{}));I.current=document.createElement("div"),L(c),r&&E(google.maps.event.addListener(c,"closeclick",r)),a&&x(google.maps.event.addListener(c,"domready",a)),l&&M(google.maps.event.addListener(c,"content_changed",l)),u&&P(google.maps.event.addListener(c,"position_changed",u)),p&&S(google.maps.event.addListener(c,"zindex_changed",p)),c.setContent(I.current),s?c.open(m,s):c.getPosition()?c.open(m):v(!1,"You must provide either an anchor or a position prop for <InfoBox>."),g&&g(c)}return()=>{null!==y&&(b&&google.maps.event.removeListener(b),C&&google.maps.event.removeListener(C),w&&google.maps.event.removeListener(w),k&&google.maps.event.removeListener(k),O&&google.maps.event.removeListener(O),d&&d(y),y.close())}},[]),I.current?(0,h.createPortal)(c.Children.only(t),I.current):null});class ex extends c.PureComponent{constructor(){super(...arguments),d(this,"registeredEvents",[]),d(this,"containerElement",null),d(this,"state",{infoBox:null}),d(this,"open",(e,t)=>{t?null!==this.context&&e.open(this.context,t):e.getPosition()?null!==this.context&&e.open(this.context):v(!1,"You must provide either an anchor or a position prop for <InfoBox>.")}),d(this,"setInfoBoxCallback",()=>{null!==this.state.infoBox&&null!==this.containerElement&&(this.state.infoBox.setContent(this.containerElement),this.open(this.state.infoBox,this.props.anchor),this.props.onLoad&&this.props.onLoad(this.state.infoBox))})}componentDidMount(){var e,t=this.props.options||{},{position:s}=t,n=T(t,ef);!s||s instanceof google.maps.LatLng||(e=new google.maps.LatLng(s.lat,s.lng));var o=new em(eL(eL({},n),e?{position:e}:{}));this.containerElement=document.createElement("div"),this.registeredEvents=b({updaterMap:eE,eventMap:eb,prevProps:{},nextProps:this.props,instance:o}),this.setState({infoBox:o},this.setInfoBoxCallback)}componentDidUpdate(e){var{infoBox:t}=this.state;null!==t&&(L(this.registeredEvents),this.registeredEvents=b({updaterMap:eE,eventMap:eb,prevProps:e,nextProps:this.props,instance:t}))}componentWillUnmount(){var{onUnmount:e}=this.props,{infoBox:t}=this.state;null!==t&&(e&&e(t),L(this.registeredEvents),t.close())}render(){return this.containerElement?(0,h.createPortal)(c.Children.only(this.props.children),this.containerElement):null}}d(ex,"contextType",f);var eC=m(l?a:(l=1,a=function e(t,s){if(t===s)return!0;if(t&&s&&"object"==typeof t&&"object"==typeof s){if(t.constructor!==s.constructor)return!1;if(Array.isArray(t)){if((n=t.length)!=s.length)return!1;for(o=n;0!=o--;)if(!e(t[o],s[o]))return!1;return!0}if(t.constructor===RegExp)return t.source===s.source&&t.flags===s.flags;if(t.valueOf!==Object.prototype.valueOf)return t.valueOf()===s.valueOf();if(t.toString!==Object.prototype.toString)return t.toString()===s.toString();if((n=(i=Object.keys(t)).length)!==Object.keys(s).length)return!1;for(o=n;0!=o--;)if(!Object.prototype.hasOwnProperty.call(s,i[o]))return!1;for(o=n;0!=o--;){var n,o,i,r=i[o];if(!e(t[r],s[r]))return!1}return!0}return t!=t&&s!=s})),eM=[Int8Array,Uint8Array,Uint8ClampedArray,Int16Array,Uint16Array,Int32Array,Uint32Array,Float32Array,Float64Array];class ek{static from(e){if(!(e instanceof ArrayBuffer))throw Error("Data must be an instance of ArrayBuffer.");var[t,s]=new Uint8Array(e,0,2);if(219!==t)throw Error("Data does not appear to be in a KDBush format.");var n=s>>4;if(1!==n)throw Error("Got v".concat(n," data when expected v").concat(1,"."));var o=eM[15&s];if(!o)throw Error("Unrecognized array type.");var[i]=new Uint16Array(e,2,1),[r]=new Uint32Array(e,4,1);return new ek(r,i,o,e)}constructor(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:64,s=arguments.length>2&&void 0!==arguments[2]?arguments[2]:Float64Array,n=arguments.length>3?arguments[3]:void 0;if(isNaN(e)||e<0)throw Error("Unpexpected numItems value: ".concat(e,"."));this.numItems=+e,this.nodeSize=Math.min(Math.max(+t,2),65535),this.ArrayType=s,this.IndexArrayType=e<65536?Uint16Array:Uint32Array;var o=eM.indexOf(this.ArrayType),i=2*e*this.ArrayType.BYTES_PER_ELEMENT,r=e*this.IndexArrayType.BYTES_PER_ELEMENT,a=(8-r%8)%8;if(o<0)throw Error("Unexpected typed array class: ".concat(s,"."));n&&n instanceof ArrayBuffer?(this.data=n,this.ids=new this.IndexArrayType(this.data,8,e),this.coords=new this.ArrayType(this.data,8+r+a,2*e),this._pos=2*e,this._finished=!0):(this.data=new ArrayBuffer(8+i+r+a),this.ids=new this.IndexArrayType(this.data,8,e),this.coords=new this.ArrayType(this.data,8+r+a,2*e),this._pos=0,this._finished=!1,new Uint8Array(this.data,0,2).set([219,16+o]),new Uint16Array(this.data,2,1)[0]=t,new Uint32Array(this.data,4,1)[0]=e)}add(e,t){var s=this._pos>>1;return this.ids[s]=s,this.coords[this._pos++]=e,this.coords[this._pos++]=t,s}finish(){var e=this._pos>>1;if(e!==this.numItems)throw Error("Added ".concat(e," items when expected ").concat(this.numItems,"."));return function e(t,s,n,o,i,r){if(!(i-o<=n)){var a=o+i>>1;(function e(t,s,n,o,i,r){for(;i>o;){if(i-o>600){var a=i-o+1,l=n-o+1,u=Math.log(a),p=.5*Math.exp(2*u/3),c=.5*Math.sqrt(u*p*(a-p)/a)*(l-a/2<0?-1:1),h=Math.max(o,Math.floor(n-l*p/a+c)),g=Math.min(i,Math.floor(n+(a-l)*p/a+c));e(t,s,n,h,g,r)}var d=s[2*n+r],m=o,v=i;for(eP(t,s,o,n),s[2*i+r]>d&&eP(t,s,o,i);m<v;){for(eP(t,s,m,v),m++,v--;s[2*m+r]<d;)m++;for(;s[2*v+r]>d;)v--}s[2*o+r]===d?eP(t,s,o,v):eP(t,s,++v,i),v<=n&&(o=v+1),n<=v&&(i=v-1)}})(t,s,a,o,i,r),e(t,s,n,o,a-1,1-r),e(t,s,n,a+1,i,1-r)}}(this.ids,this.coords,this.nodeSize,0,this.numItems-1,0),this._finished=!0,this}range(e,t,s,n){if(!this._finished)throw Error("Data not yet indexed - call index.finish().");for(var{ids:o,coords:i,nodeSize:r}=this,a=[0,o.length-1,0],l=[];a.length;){var u=a.pop()||0,p=a.pop()||0,c=a.pop()||0;if(p-c<=r){for(var h=c;h<=p;h++){var g=i[2*h],d=i[2*h+1];g>=e&&g<=s&&d>=t&&d<=n&&l.push(o[h])}continue}var m=c+p>>1,v=i[2*m],f=i[2*m+1];v>=e&&v<=s&&f>=t&&f<=n&&l.push(o[m]),(0===u?e<=v:t<=f)&&(a.push(c),a.push(m-1),a.push(1-u)),(0===u?s>=v:n>=f)&&(a.push(m+1),a.push(p),a.push(1-u))}return l}within(e,t,s){if(!this._finished)throw Error("Data not yet indexed - call index.finish().");for(var{ids:n,coords:o,nodeSize:i}=this,r=[0,n.length-1,0],a=[],l=s*s;r.length;){var u=r.pop()||0,p=r.pop()||0,c=r.pop()||0;if(p-c<=i){for(var h=c;h<=p;h++)eS(o[2*h],o[2*h+1],e,t)<=l&&a.push(n[h]);continue}var g=c+p>>1,d=o[2*g],m=o[2*g+1];eS(d,m,e,t)<=l&&a.push(n[g]),(0===u?e-s<=d:t-s<=m)&&(r.push(c),r.push(g-1),r.push(1-u)),(0===u?e+s>=d:t+s>=m)&&(r.push(g+1),r.push(p),r.push(1-u))}return a}}function eP(e,t,s,n){eO(e,s,n),eO(t,2*s,2*n),eO(t,2*s+1,2*n+1)}function eO(e,t,s){var n=e[t];e[t]=e[s],e[s]=n}function eS(e,t,s,n){var o=e-s,i=t-n;return o*o+i*i}var eI={minZoom:0,maxZoom:16,minPoints:2,radius:40,extent:512,nodeSize:64,log:!1,generateId:!1,reduce:null,map:e=>e},ej=Math.fround||(e=>t=>(e[0]=+t,e[0]))(new Float32Array(1));class eD{constructor(e){this.options=Object.assign(Object.create(eI),e),this.trees=Array(this.options.maxZoom+1),this.stride=this.options.reduce?7:6,this.clusterProps=[]}load(e){var{log:t,minZoom:s,maxZoom:n}=this.options;t&&console.time("total time");var o="prepare ".concat(e.length," points");t&&console.time(o),this.points=e;for(var i=[],r=0;r<e.length;r++){var a=e[r];if(a.geometry){var[l,u]=a.geometry.coordinates,p=ej(eT(l)),c=ej(ez(u));i.push(p,c,1/0,r,-1,1),this.options.reduce&&i.push(0)}}var h=this.trees[n+1]=this._createTree(i);t&&console.timeEnd(o);for(var g=n;g>=s;g--){var d=+Date.now();h=this.trees[g]=this._createTree(this._cluster(h,g)),t&&console.log("z%d: %d clusters in %dms",g,h.numItems,Date.now()-d)}return t&&console.timeEnd("total time"),this}getClusters(e,t){var s=((e[0]+180)%360+360)%360-180,n=Math.max(-90,Math.min(90,e[1])),o=180===e[2]?180:((e[2]+180)%360+360)%360-180,i=Math.max(-90,Math.min(90,e[3]));if(e[2]-e[0]>=360)s=-180,o=180;else if(s>o){var r=this.getClusters([s,n,180,i],t),a=this.getClusters([-180,n,o,i],t);return r.concat(a)}var l=this.trees[this._limitZoom(t)],u=l.range(eT(s),ez(i),eT(o),ez(n)),p=l.data,c=[];for(var h of u){var g=this.stride*h;c.push(p[g+5]>1?e_(p,g,this.clusterProps):this.points[p[g+3]])}return c}getChildren(e){var t=this._getOriginId(e),s=this._getOriginZoom(e),n="No cluster with the specified id.",o=this.trees[s];if(!o)throw Error(n);var i=o.data;if(t*this.stride>=i.length)throw Error(n);var r=this.options.radius/(this.options.extent*Math.pow(2,s-1)),a=i[t*this.stride],l=i[t*this.stride+1],u=o.within(a,l,r),p=[];for(var c of u){var h=c*this.stride;i[h+4]===e&&p.push(i[h+5]>1?e_(i,h,this.clusterProps):this.points[i[h+3]])}if(0===p.length)throw Error(n);return p}getLeaves(e,t,s){t=t||10,s=s||0;var n=[];return this._appendLeaves(n,e,t,s,0),n}getTile(e,t,s){var n=this.trees[this._limitZoom(e)],o=Math.pow(2,e),{extent:i,radius:r}=this.options,a=r/i,l=(s-a)/o,u=(s+1+a)/o,p={features:[]};return this._addTileFeatures(n.range((t-a)/o,l,(t+1+a)/o,u),n.data,t,s,o,p),0===t&&this._addTileFeatures(n.range(1-a/o,l,1,u),n.data,o,s,o,p),t===o-1&&this._addTileFeatures(n.range(0,l,a/o,u),n.data,-1,s,o,p),p.features.length?p:null}getClusterExpansionZoom(e){for(var t=this._getOriginZoom(e)-1;t<=this.options.maxZoom;){var s=this.getChildren(e);if(t++,1!==s.length)break;e=s[0].properties.cluster_id}return t}_appendLeaves(e,t,s,n,o){for(var i of this.getChildren(t)){var r=i.properties;if(r&&r.cluster?o+r.point_count<=n?o+=r.point_count:o=this._appendLeaves(e,r.cluster_id,s,n,o):o<n?o++:e.push(i),e.length===s)break}return o}_createTree(e){for(var t=new ek(e.length/this.stride|0,this.options.nodeSize,Float32Array),s=0;s<e.length;s+=this.stride)t.add(e[s],e[s+1]);return t.finish(),t.data=e,t}_addTileFeatures(e,t,s,n,o,i){for(var r of e){var a=r*this.stride,l=t[a+5]>1,u=void 0,p=void 0,c=void 0;if(l)u=eB(t,a,this.clusterProps),p=t[a],c=t[a+1];else{var h=this.points[t[a+3]];u=h.properties;var[g,d]=h.geometry.coordinates;p=eT(g),c=ez(d)}var m={type:1,geometry:[[Math.round(this.options.extent*(p*o-s)),Math.round(this.options.extent*(c*o-n))]],tags:u},v=void 0;void 0!==(v=l||this.options.generateId?t[a+3]:this.points[t[a+3]].id)&&(m.id=v),i.features.push(m)}}_limitZoom(e){return Math.max(this.options.minZoom,Math.min(Math.floor(+e),this.options.maxZoom+1))}_cluster(e,t){for(var{radius:s,extent:n,reduce:o,minPoints:i}=this.options,r=s/(n*Math.pow(2,t)),a=e.data,l=[],u=this.stride,p=0;p<a.length;p+=u)if(!(a[p+2]<=t)){a[p+2]=t;var c=a[p],h=a[p+1],g=e.within(a[p],a[p+1],r),d=a[p+5],m=d;for(var v of g){var f=v*u;a[f+2]>t&&(m+=a[f+5])}if(m>d&&m>=i){var y=c*d,L=h*d,b=void 0,E=-1,w=((p/u|0)<<5)+(t+1)+this.points.length;for(var x of g){var C=x*u;if(!(a[C+2]<=t)){a[C+2]=t;var M=a[C+5];y+=a[C]*M,L+=a[C+1]*M,a[C+4]=w,o&&(b||(b=this._map(a,p,!0),E=this.clusterProps.length,this.clusterProps.push(b)),o(b,this._map(a,C)))}}a[p+4]=w,l.push(y/m,L/m,1/0,w,-1,m),o&&l.push(E)}else{for(var k=0;k<u;k++)l.push(a[p+k]);if(m>1)for(var P of g){var O=P*u;if(!(a[O+2]<=t)){a[O+2]=t;for(var S=0;S<u;S++)l.push(a[O+S])}}}}return l}_getOriginId(e){return e-this.points.length>>5}_getOriginZoom(e){return(e-this.points.length)%32}_map(e,t,s){if(e[t+5]>1){var n=this.clusterProps[e[t+6]];return s?Object.assign({},n):n}var o=this.points[e[t+3]].properties,i=this.options.map(o);return s&&i===o?Object.assign({},i):i}}function e_(e,t,s){return{type:"Feature",id:e[t+3],properties:eB(e,t,s),geometry:{type:"Point",coordinates:[(e[t]-.5)*360,360*Math.atan(Math.exp((180-360*e[t+1])*Math.PI/180))/Math.PI-90]}}}function eB(e,t,s){var n=e[t+5],o=n>=1e4?"".concat(Math.round(n/1e3),"k"):n>=1e3?"".concat(Math.round(n/100)/10,"k"):n,i=e[t+6];return Object.assign(-1===i?{}:Object.assign({},s[i]),{cluster:!0,cluster_id:e[t+3],point_count:n,point_count_abbreviated:o})}function eT(e){return e/360+.5}function ez(e){var t=Math.sin(e*Math.PI/180),s=.5-.25*Math.log((1+t)/(1-t))/Math.PI;return s<0?0:s>1?1:s}function eR(e,t){var s={};for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&0>t.indexOf(n)&&(s[n]=e[n]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var o=0,n=Object.getOwnPropertySymbols(e);o<n.length;o++)0>t.indexOf(n[o])&&Object.prototype.propertyIsEnumerable.call(e,n[o])&&(s[n[o]]=e[n[o]]);return s}class eA{static isAdvancedMarkerAvailable(e){return google.maps.marker&&!0===e.getMapCapabilities().isAdvancedMarkersAvailable}static isAdvancedMarker(e){return google.maps.marker&&e instanceof google.maps.marker.AdvancedMarkerElement}static setMap(e,t){this.isAdvancedMarker(e)?e.map=t:e.setMap(t)}static getPosition(e){if(this.isAdvancedMarker(e)){if(e.position){if(e.position instanceof google.maps.LatLng)return e.position;if(e.position.lat&&e.position.lng)return new google.maps.LatLng(e.position.lat,e.position.lng)}return new google.maps.LatLng(null)}return e.getPosition()}static getVisible(e){return!!this.isAdvancedMarker(e)||e.getVisible()}}class eU{constructor(e){var{markers:t,position:s}=e;this.markers=t,s&&(s instanceof google.maps.LatLng?this._position=s:this._position=new google.maps.LatLng(s))}get bounds(){if(0!==this.markers.length||this._position){var e=new google.maps.LatLngBounds(this._position,this._position);for(var t of this.markers)e.extend(eA.getPosition(t));return e}}get position(){return this._position||this.bounds.getCenter()}get count(){return this.markers.filter(e=>eA.getVisible(e)).length}push(e){this.markers.push(e)}delete(){this.marker&&(eA.setMap(this.marker,null),this.marker=void 0),this.markers.length=0}}var eZ=(e,t,s,n)=>{var o=eV(e.getBounds(),t,n);return s.filter(e=>o.contains(eA.getPosition(e)))},eV=(e,t,s)=>{var{northEast:n,southWest:o}=eH(e,t);return eF(eG({northEast:n,southWest:o},s),t)},eW=(e,t,s)=>{var n=eV(e,t,s),o=n.getNorthEast(),i=n.getSouthWest();return[i.lng(),i.lat(),o.lng(),o.lat()]},eN=(e,t)=>{var s=(t.lat-e.lat)*Math.PI/180,n=(t.lng-e.lng)*Math.PI/180,o=Math.sin(s/2),i=Math.sin(n/2),r=o*o+Math.cos(e.lat*Math.PI/180)*Math.cos(t.lat*Math.PI/180)*i*i;return 2*Math.atan2(Math.sqrt(r),Math.sqrt(1-r))*6371},eH=(e,t)=>({northEast:t.fromLatLngToDivPixel(e.getNorthEast()),southWest:t.fromLatLngToDivPixel(e.getSouthWest())}),eG=(e,t)=>{var{northEast:s,southWest:n}=e;return s.x+=t,s.y-=t,n.x-=t,n.y+=t,{northEast:s,southWest:n}},eF=(e,t)=>{var{northEast:s,southWest:n}=e,o=t.fromDivPixelToLatLng(n),i=t.fromDivPixelToLatLng(s);return new google.maps.LatLngBounds(o,i)};class eY{constructor(e){var{maxZoom:t=16}=e;this.maxZoom=t}noop(e){var{markers:t}=e;return eq(t)}}class eK extends eY{constructor(e){var{viewportPadding:t=60}=e;super(eR(e,["viewportPadding"])),this.viewportPadding=60,this.viewportPadding=t}calculate(e){var{markers:t,map:s,mapCanvasProjection:n}=e;return s.getZoom()>=this.maxZoom?{clusters:this.noop({markers:t}),changed:!1}:{clusters:this.cluster({markers:eZ(s,n,t,this.viewportPadding),map:s,mapCanvasProjection:n})}}}var eq=e=>e.map(e=>new eU({position:eA.getPosition(e),markers:[e]}));class eJ extends eY{constructor(e){var{maxZoom:t,radius:s=60}=e,n=eR(e,["maxZoom","radius"]);super({maxZoom:t}),this.state={zoom:-1},this.superCluster=new eD(Object.assign({maxZoom:this.maxZoom,radius:s},n))}calculate(e){var t=!1,s={zoom:e.map.getZoom()};if(!eC(e.markers,this.markers)){t=!0,this.markers=[...e.markers];var n=this.markers.map(e=>{var t=eA.getPosition(e);return{type:"Feature",geometry:{type:"Point",coordinates:[t.lng(),t.lat()]},properties:{marker:e}}});this.superCluster.load(n)}return!t&&(this.state.zoom<=this.maxZoom||s.zoom<=this.maxZoom)&&(t=!eC(this.state,s)),this.state=s,t&&(this.clusters=this.cluster(e)),{clusters:this.clusters,changed:t}}cluster(e){var{map:t}=e;return this.superCluster.getClusters([-180,-90,180,90],Math.round(t.getZoom())).map(e=>this.transformCluster(e))}transformCluster(e){var{geometry:{coordinates:[t,s]},properties:n}=e;if(n.cluster)return new eU({markers:this.superCluster.getLeaves(n.cluster_id,1/0).map(e=>e.properties.marker),position:{lat:s,lng:t}});var o=n.marker;return new eU({markers:[o],position:eA.getPosition(o)})}}class eX{constructor(e,t){this.markers={sum:e.length};var s=t.map(e=>e.count),n=s.reduce((e,t)=>e+t,0);this.clusters={count:t.length,markers:{mean:n/t.length,sum:n,min:Math.min(...s),max:Math.max(...s)}}}}class e${render(e,t,s){var{count:n,position:o}=e,i=n>Math.max(10,t.clusters.markers.mean)?"#ff0000":"#0000ff",r='<svg fill="'.concat(i,'" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 240 240" width="50" height="50">\n<circle cx="120" cy="120" opacity=".6" r="70" />\n<circle cx="120" cy="120" opacity=".3" r="90" />\n<circle cx="120" cy="120" opacity=".2" r="110" />\n<text x="50%" y="50%" style="fill:#fff" text-anchor="middle" font-size="50" dominant-baseline="middle" font-family="roboto,arial,sans-serif">').concat(n,"</text>\n</svg>"),a="Cluster of ".concat(n," markers"),l=Number(google.maps.Marker.MAX_ZINDEX)+n;if(eA.isAdvancedMarkerAvailable(s)){var u=new DOMParser().parseFromString(r,"image/svg+xml").documentElement;return u.setAttribute("transform","translate(0 25)"),new google.maps.marker.AdvancedMarkerElement({map:s,position:o,zIndex:l,title:a,content:u})}var p={position:o,zIndex:l,title:a,icon:{url:"data:image/svg+xml;base64,".concat(btoa(r)),anchor:new google.maps.Point(25,25)}};return new google.maps.Marker(p)}}class eQ{constructor(){!function(e,t){for(var s in t.prototype)e.prototype[s]=t.prototype[s]}(eQ,google.maps.OverlayView)}}!function(e){e.CLUSTERING_BEGIN="clusteringbegin",e.CLUSTERING_END="clusteringend",e.CLUSTER_CLICK="click"}(u||(u={}));var e0=(e,t,s)=>{s.fitBounds(t.bounds)};class e1 extends eQ{constructor(e){var{map:t,markers:s=[],algorithmOptions:n={},algorithm:o=new eJ(n),renderer:i=new e$,onClusterClick:r=e0}=e;super(),this.markers=[...s],this.clusters=[],this.algorithm=o,this.renderer=i,this.onClusterClick=r,t&&this.setMap(t)}addMarker(e,t){!this.markers.includes(e)&&(this.markers.push(e),t||this.render())}addMarkers(e,t){e.forEach(e=>{this.addMarker(e,!0)}),t||this.render()}removeMarker(e,t){var s=this.markers.indexOf(e);return -1!==s&&(eA.setMap(e,null),this.markers.splice(s,1),t||this.render(),!0)}removeMarkers(e,t){var s=!1;return e.forEach(e=>{s=this.removeMarker(e,!0)||s}),s&&!t&&this.render(),s}clearMarkers(e){this.markers.length=0,e||this.render()}render(){var e=this.getMap();if(e instanceof google.maps.Map&&e.getProjection()){google.maps.event.trigger(this,u.CLUSTERING_BEGIN,this);var{clusters:t,changed:s}=this.algorithm.calculate({markers:this.markers,map:e,mapCanvasProjection:this.getProjection()});if(s||void 0==s){var n=new Set;for(var o of t)1==o.markers.length&&n.add(o.markers[0]);var i=[];for(var r of this.clusters)null!=r.marker&&(1==r.markers.length?n.has(r.marker)||eA.setMap(r.marker,null):i.push(r.marker));this.clusters=t,this.renderClusters(),requestAnimationFrame(()=>i.forEach(e=>eA.setMap(e,null)))}google.maps.event.trigger(this,u.CLUSTERING_END,this)}}onAdd(){this.idleListener=this.getMap().addListener("idle",this.render.bind(this)),this.render()}onRemove(){google.maps.event.removeListener(this.idleListener),this.reset()}reset(){this.markers.forEach(e=>eA.setMap(e,null)),this.clusters.forEach(e=>e.delete()),this.clusters=[]}renderClusters(){var e=new eX(this.markers,this.clusters),t=this.getMap();this.clusters.forEach(s=>{1===s.markers.length?s.marker=s.markers[0]:(s.marker=this.renderer.render(s,e,t),s.markers.forEach(e=>eA.setMap(e,null)),this.onClusterClick&&s.marker.addListener("click",e=>{google.maps.event.trigger(this,u.CLUSTER_CLICK,s),this.onClusterClick(e,s,t)})),eA.setMap(s.marker,t)})}}function e2(e,t){var s=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),s.push.apply(s,n)}return s}function e3(e){for(var t=1;t<arguments.length;t++){var s=null!=arguments[t]?arguments[t]:{};t%2?e2(Object(s),!0).forEach(function(t){d(e,t,s[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(s)):e2(Object(s)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(s,t))})}return e}(0,c.memo)(function(e){var{children:t,options:s}=e,n=function(e){var t,s=(v(!!c.useContext,"useGoogleMap is React hook and requires React version 16.8+"),v(!!(t=(0,c.useContext)(f)),"useGoogleMap needs a GoogleMap available up in the tree"),t),[n,o]=(0,c.useState)(null);return(0,c.useEffect)(()=>{s&&null===n&&o(new e1(e3(e3({},e),{},{map:s})))},[s]),n}(s);return null!==n?t(n):null});var e8={onCloseClick:"closeclick",onContentChanged:"content_changed",onDomReady:"domready",onPositionChanged:"position_changed",onZindexChanged:"zindex_changed"},e6={options(e,t){e.setOptions(t)},position(e,t){e.setPosition(t)},zIndex(e,t){e.setZIndex(t)}};(0,c.memo)(function(e){var{children:t,anchor:s,options:n,position:o,zIndex:i,onCloseClick:r,onDomReady:a,onContentChanged:l,onPositionChanged:u,onZindexChanged:p,onLoad:g,onUnmount:d}=e,m=(0,c.useContext)(f),[y,L]=(0,c.useState)(null),[b,E]=(0,c.useState)(null),[w,x]=(0,c.useState)(null),[C,M]=(0,c.useState)(null),[k,P]=(0,c.useState)(null),[O,S]=(0,c.useState)(null),I=(0,c.useRef)(null);return(0,c.useEffect)(()=>{null!==y&&(y.close(),s?y.open(m,s):y.getPosition()&&y.open(m))},[m,y,s]),(0,c.useEffect)(()=>{n&&null!==y&&y.setOptions(n)},[y,n]),(0,c.useEffect)(()=>{o&&null!==y&&y.setPosition(o)},[o]),(0,c.useEffect)(()=>{"number"==typeof i&&null!==y&&y.setZIndex(i)},[i]),(0,c.useEffect)(()=>{y&&r&&(null!==b&&google.maps.event.removeListener(b),E(google.maps.event.addListener(y,"closeclick",r)))},[r]),(0,c.useEffect)(()=>{y&&a&&(null!==w&&google.maps.event.removeListener(w),x(google.maps.event.addListener(y,"domready",a)))},[a]),(0,c.useEffect)(()=>{y&&l&&(null!==C&&google.maps.event.removeListener(C),M(google.maps.event.addListener(y,"content_changed",l)))},[l]),(0,c.useEffect)(()=>{y&&u&&(null!==k&&google.maps.event.removeListener(k),P(google.maps.event.addListener(y,"position_changed",u)))},[u]),(0,c.useEffect)(()=>{y&&p&&(null!==O&&google.maps.event.removeListener(O),S(google.maps.event.addListener(y,"zindex_changed",p)))},[p]),(0,c.useEffect)(()=>{var e=new google.maps.InfoWindow(n);return L(e),I.current=document.createElement("div"),r&&E(google.maps.event.addListener(e,"closeclick",r)),a&&x(google.maps.event.addListener(e,"domready",a)),l&&M(google.maps.event.addListener(e,"content_changed",l)),u&&P(google.maps.event.addListener(e,"position_changed",u)),p&&S(google.maps.event.addListener(e,"zindex_changed",p)),e.setContent(I.current),o&&e.setPosition(o),i&&e.setZIndex(i),s?e.open(m,s):e.getPosition()?e.open(m):v(!1,"You must provide either an anchor (typically render it inside a <Marker>) or a position props for <InfoWindow>."),g&&g(e),()=>{b&&google.maps.event.removeListener(b),C&&google.maps.event.removeListener(C),w&&google.maps.event.removeListener(w),k&&google.maps.event.removeListener(k),O&&google.maps.event.removeListener(O),d&&d(e),e.close()}},[]),I.current?(0,h.createPortal)(c.Children.only(t),I.current):null});class e5 extends c.PureComponent{constructor(){super(...arguments),d(this,"registeredEvents",[]),d(this,"containerElement",null),d(this,"state",{infoWindow:null}),d(this,"open",(e,t)=>{t?e.open(this.context,t):e.getPosition()?e.open(this.context):v(!1,"You must provide either an anchor (typically render it inside a <Marker>) or a position props for <InfoWindow>.")}),d(this,"setInfoWindowCallback",()=>{null!==this.state.infoWindow&&null!==this.containerElement&&(this.state.infoWindow.setContent(this.containerElement),this.open(this.state.infoWindow,this.props.anchor),this.props.onLoad&&this.props.onLoad(this.state.infoWindow))})}componentDidMount(){var e=new google.maps.InfoWindow(this.props.options);this.containerElement=document.createElement("div"),this.registeredEvents=b({updaterMap:e6,eventMap:e8,prevProps:{},nextProps:this.props,instance:e}),this.setState(()=>({infoWindow:e}),this.setInfoWindowCallback)}componentDidUpdate(e){null!==this.state.infoWindow&&(L(this.registeredEvents),this.registeredEvents=b({updaterMap:e6,eventMap:e8,prevProps:e,nextProps:this.props,instance:this.state.infoWindow}))}componentWillUnmount(){null!==this.state.infoWindow&&(L(this.registeredEvents),this.props.onUnmount&&this.props.onUnmount(this.state.infoWindow),this.state.infoWindow.close())}render(){return this.containerElement?(0,h.createPortal)(c.Children.only(this.props.children),this.containerElement):null}}function e4(e,t){var s=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),s.push.apply(s,n)}return s}function e9(e){for(var t=1;t<arguments.length;t++){var s=null!=arguments[t]?arguments[t]:{};t%2?e4(Object(s),!0).forEach(function(t){d(e,t,s[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(s)):e4(Object(s)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(s,t))})}return e}d(e5,"contextType",f);var e7={onClick:"click",onDblClick:"dblclick",onDrag:"drag",onDragEnd:"dragend",onDragStart:"dragstart",onMouseDown:"mousedown",onMouseMove:"mousemove",onMouseOut:"mouseout",onMouseOver:"mouseover",onMouseUp:"mouseup",onRightClick:"rightclick"},te={draggable(e,t){e.setDraggable(t)},editable(e,t){e.setEditable(t)},map(e,t){e.setMap(t)},options(e,t){e.setOptions(t)},path(e,t){e.setPath(t)},visible(e,t){e.setVisible(t)}},tt={};(0,c.memo)(function(e){var{options:t,draggable:s,editable:n,visible:o,path:i,onDblClick:r,onDragEnd:a,onDragStart:l,onMouseDown:u,onMouseMove:p,onMouseOut:h,onMouseOver:g,onMouseUp:d,onRightClick:m,onClick:v,onDrag:y,onLoad:L,onUnmount:b}=e,E=(0,c.useContext)(f),[w,x]=(0,c.useState)(null),[C,M]=(0,c.useState)(null),[k,P]=(0,c.useState)(null),[O,S]=(0,c.useState)(null),[I,j]=(0,c.useState)(null),[D,_]=(0,c.useState)(null),[B,T]=(0,c.useState)(null),[z,R]=(0,c.useState)(null),[A,U]=(0,c.useState)(null),[Z,V]=(0,c.useState)(null),[W,N]=(0,c.useState)(null),[H,G]=(0,c.useState)(null);return(0,c.useEffect)(()=>{null!==w&&w.setMap(E)},[E]),(0,c.useEffect)(()=>{void 0!==t&&null!==w&&w.setOptions(t)},[w,t]),(0,c.useEffect)(()=>{void 0!==s&&null!==w&&w.setDraggable(s)},[w,s]),(0,c.useEffect)(()=>{void 0!==n&&null!==w&&w.setEditable(n)},[w,n]),(0,c.useEffect)(()=>{void 0!==o&&null!==w&&w.setVisible(o)},[w,o]),(0,c.useEffect)(()=>{void 0!==i&&null!==w&&w.setPath(i)},[w,i]),(0,c.useEffect)(()=>{w&&r&&(null!==C&&google.maps.event.removeListener(C),M(google.maps.event.addListener(w,"dblclick",r)))},[r]),(0,c.useEffect)(()=>{w&&a&&(null!==k&&google.maps.event.removeListener(k),P(google.maps.event.addListener(w,"dragend",a)))},[a]),(0,c.useEffect)(()=>{w&&l&&(null!==O&&google.maps.event.removeListener(O),S(google.maps.event.addListener(w,"dragstart",l)))},[l]),(0,c.useEffect)(()=>{w&&u&&(null!==I&&google.maps.event.removeListener(I),j(google.maps.event.addListener(w,"mousedown",u)))},[u]),(0,c.useEffect)(()=>{w&&p&&(null!==D&&google.maps.event.removeListener(D),_(google.maps.event.addListener(w,"mousemove",p)))},[p]),(0,c.useEffect)(()=>{w&&h&&(null!==B&&google.maps.event.removeListener(B),T(google.maps.event.addListener(w,"mouseout",h)))},[h]),(0,c.useEffect)(()=>{w&&g&&(null!==z&&google.maps.event.removeListener(z),R(google.maps.event.addListener(w,"mouseover",g)))},[g]),(0,c.useEffect)(()=>{w&&d&&(null!==A&&google.maps.event.removeListener(A),U(google.maps.event.addListener(w,"mouseup",d)))},[d]),(0,c.useEffect)(()=>{w&&m&&(null!==Z&&google.maps.event.removeListener(Z),V(google.maps.event.addListener(w,"rightclick",m)))},[m]),(0,c.useEffect)(()=>{w&&v&&(null!==W&&google.maps.event.removeListener(W),N(google.maps.event.addListener(w,"click",v)))},[v]),(0,c.useEffect)(()=>{w&&y&&(null!==H&&google.maps.event.removeListener(H),G(google.maps.event.addListener(w,"drag",y)))},[y]),(0,c.useEffect)(()=>{var e=new google.maps.Polyline(e9(e9({},t||tt),{},{map:E}));return i&&e.setPath(i),void 0!==o&&e.setVisible(o),void 0!==n&&e.setEditable(n),void 0!==s&&e.setDraggable(s),r&&M(google.maps.event.addListener(e,"dblclick",r)),a&&P(google.maps.event.addListener(e,"dragend",a)),l&&S(google.maps.event.addListener(e,"dragstart",l)),u&&j(google.maps.event.addListener(e,"mousedown",u)),p&&_(google.maps.event.addListener(e,"mousemove",p)),h&&T(google.maps.event.addListener(e,"mouseout",h)),g&&R(google.maps.event.addListener(e,"mouseover",g)),d&&U(google.maps.event.addListener(e,"mouseup",d)),m&&V(google.maps.event.addListener(e,"rightclick",m)),v&&N(google.maps.event.addListener(e,"click",v)),y&&G(google.maps.event.addListener(e,"drag",y)),x(e),L&&L(e),()=>{null!==C&&google.maps.event.removeListener(C),null!==k&&google.maps.event.removeListener(k),null!==O&&google.maps.event.removeListener(O),null!==I&&google.maps.event.removeListener(I),null!==D&&google.maps.event.removeListener(D),null!==B&&google.maps.event.removeListener(B),null!==z&&google.maps.event.removeListener(z),null!==A&&google.maps.event.removeListener(A),null!==Z&&google.maps.event.removeListener(Z),null!==W&&google.maps.event.removeListener(W),b&&b(e),e.setMap(null)}},[]),null});class ts extends c.PureComponent{constructor(){super(...arguments),d(this,"registeredEvents",[]),d(this,"state",{polyline:null}),d(this,"setPolylineCallback",()=>{null!==this.state.polyline&&this.props.onLoad&&this.props.onLoad(this.state.polyline)})}componentDidMount(){var e=new google.maps.Polyline(e9(e9({},this.props.options),{},{map:this.context}));this.registeredEvents=b({updaterMap:te,eventMap:e7,prevProps:{},nextProps:this.props,instance:e}),this.setState(function(){return{polyline:e}},this.setPolylineCallback)}componentDidUpdate(e){null!==this.state.polyline&&(L(this.registeredEvents),this.registeredEvents=b({updaterMap:te,eventMap:e7,prevProps:e,nextProps:this.props,instance:this.state.polyline}))}componentWillUnmount(){null!==this.state.polyline&&(this.props.onUnmount&&this.props.onUnmount(this.state.polyline),L(this.registeredEvents),this.state.polyline.setMap(null))}render(){return null}}function tn(e,t){var s=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),s.push.apply(s,n)}return s}function to(e){for(var t=1;t<arguments.length;t++){var s=null!=arguments[t]?arguments[t]:{};t%2?tn(Object(s),!0).forEach(function(t){d(e,t,s[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(s)):tn(Object(s)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(s,t))})}return e}d(ts,"contextType",f);var ti={onClick:"click",onDblClick:"dblclick",onDrag:"drag",onDragEnd:"dragend",onDragStart:"dragstart",onMouseDown:"mousedown",onMouseMove:"mousemove",onMouseOut:"mouseout",onMouseOver:"mouseover",onMouseUp:"mouseup",onRightClick:"rightclick"},tr={draggable(e,t){e.setDraggable(t)},editable(e,t){e.setEditable(t)},map(e,t){e.setMap(t)},options(e,t){e.setOptions(t)},path(e,t){e.setPath(t)},paths(e,t){e.setPaths(t)},visible(e,t){e.setVisible(t)}};(0,c.memo)(function(e){var{options:t,draggable:s,editable:n,visible:o,path:i,paths:r,onDblClick:a,onDragEnd:l,onDragStart:u,onMouseDown:p,onMouseMove:h,onMouseOut:g,onMouseOver:d,onMouseUp:m,onRightClick:v,onClick:y,onDrag:L,onLoad:b,onUnmount:E,onEdit:w}=e,x=(0,c.useContext)(f),[C,M]=(0,c.useState)(null),[k,P]=(0,c.useState)(null),[O,S]=(0,c.useState)(null),[I,j]=(0,c.useState)(null),[D,_]=(0,c.useState)(null),[B,T]=(0,c.useState)(null),[z,R]=(0,c.useState)(null),[A,U]=(0,c.useState)(null),[Z,V]=(0,c.useState)(null),[W,N]=(0,c.useState)(null),[H,G]=(0,c.useState)(null),[F,Y]=(0,c.useState)(null);return(0,c.useEffect)(()=>{null!==C&&C.setMap(x)},[x]),(0,c.useEffect)(()=>{void 0!==t&&null!==C&&C.setOptions(t)},[C,t]),(0,c.useEffect)(()=>{void 0!==s&&null!==C&&C.setDraggable(s)},[C,s]),(0,c.useEffect)(()=>{void 0!==n&&null!==C&&C.setEditable(n)},[C,n]),(0,c.useEffect)(()=>{void 0!==o&&null!==C&&C.setVisible(o)},[C,o]),(0,c.useEffect)(()=>{void 0!==i&&null!==C&&C.setPath(i)},[C,i]),(0,c.useEffect)(()=>{void 0!==r&&null!==C&&C.setPaths(r)},[C,r]),(0,c.useEffect)(()=>{C&&"function"==typeof a&&(null!==k&&google.maps.event.removeListener(k),P(google.maps.event.addListener(C,"dblclick",a)))},[a]),(0,c.useEffect)(()=>{C&&(google.maps.event.addListener(C.getPath(),"insert_at",()=>{null==w||w(C)}),google.maps.event.addListener(C.getPath(),"set_at",()=>{null==w||w(C)}),google.maps.event.addListener(C.getPath(),"remove_at",()=>{null==w||w(C)}))},[C,w]),(0,c.useEffect)(()=>{C&&"function"==typeof l&&(null!==O&&google.maps.event.removeListener(O),S(google.maps.event.addListener(C,"dragend",l)))},[l]),(0,c.useEffect)(()=>{C&&"function"==typeof u&&(null!==I&&google.maps.event.removeListener(I),j(google.maps.event.addListener(C,"dragstart",u)))},[u]),(0,c.useEffect)(()=>{C&&"function"==typeof p&&(null!==D&&google.maps.event.removeListener(D),_(google.maps.event.addListener(C,"mousedown",p)))},[p]),(0,c.useEffect)(()=>{C&&"function"==typeof h&&(null!==B&&google.maps.event.removeListener(B),T(google.maps.event.addListener(C,"mousemove",h)))},[h]),(0,c.useEffect)(()=>{C&&"function"==typeof g&&(null!==z&&google.maps.event.removeListener(z),R(google.maps.event.addListener(C,"mouseout",g)))},[g]),(0,c.useEffect)(()=>{C&&"function"==typeof d&&(null!==A&&google.maps.event.removeListener(A),U(google.maps.event.addListener(C,"mouseover",d)))},[d]),(0,c.useEffect)(()=>{C&&"function"==typeof m&&(null!==Z&&google.maps.event.removeListener(Z),V(google.maps.event.addListener(C,"mouseup",m)))},[m]),(0,c.useEffect)(()=>{C&&"function"==typeof v&&(null!==W&&google.maps.event.removeListener(W),N(google.maps.event.addListener(C,"rightclick",v)))},[v]),(0,c.useEffect)(()=>{C&&"function"==typeof y&&(null!==H&&google.maps.event.removeListener(H),G(google.maps.event.addListener(C,"click",y)))},[y]),(0,c.useEffect)(()=>{C&&"function"==typeof L&&(null!==F&&google.maps.event.removeListener(F),Y(google.maps.event.addListener(C,"drag",L)))},[L]),(0,c.useEffect)(()=>{var e=new google.maps.Polygon(to(to({},t),{},{map:x}));return i&&e.setPath(i),r&&e.setPaths(r),void 0!==o&&e.setVisible(o),void 0!==n&&e.setEditable(n),void 0!==s&&e.setDraggable(s),a&&P(google.maps.event.addListener(e,"dblclick",a)),l&&S(google.maps.event.addListener(e,"dragend",l)),u&&j(google.maps.event.addListener(e,"dragstart",u)),p&&_(google.maps.event.addListener(e,"mousedown",p)),h&&T(google.maps.event.addListener(e,"mousemove",h)),g&&R(google.maps.event.addListener(e,"mouseout",g)),d&&U(google.maps.event.addListener(e,"mouseover",d)),m&&V(google.maps.event.addListener(e,"mouseup",m)),v&&N(google.maps.event.addListener(e,"rightclick",v)),y&&G(google.maps.event.addListener(e,"click",y)),L&&Y(google.maps.event.addListener(e,"drag",L)),M(e),b&&b(e),()=>{null!==k&&google.maps.event.removeListener(k),null!==O&&google.maps.event.removeListener(O),null!==I&&google.maps.event.removeListener(I),null!==D&&google.maps.event.removeListener(D),null!==B&&google.maps.event.removeListener(B),null!==z&&google.maps.event.removeListener(z),null!==A&&google.maps.event.removeListener(A),null!==Z&&google.maps.event.removeListener(Z),null!==W&&google.maps.event.removeListener(W),null!==H&&google.maps.event.removeListener(H),E&&E(e),e.setMap(null)}},[]),null});class ta extends c.PureComponent{constructor(){super(...arguments),d(this,"registeredEvents",[])}componentDidMount(){var e=this.props.options||{};this.polygon=new google.maps.Polygon(e),this.polygon.setMap(this.context),this.registeredEvents=b({updaterMap:tr,eventMap:ti,prevProps:{},nextProps:this.props,instance:this.polygon}),this.props.onLoad&&this.props.onLoad(this.polygon)}componentDidUpdate(e){this.polygon&&(L(this.registeredEvents),this.registeredEvents=b({updaterMap:tr,eventMap:ti,prevProps:e,nextProps:this.props,instance:this.polygon}))}componentWillUnmount(){this.polygon&&(this.props.onUnmount&&this.props.onUnmount(this.polygon),L(this.registeredEvents),this.polygon&&this.polygon.setMap(null))}render(){return null}}function tl(e,t){var s=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),s.push.apply(s,n)}return s}function tu(e){for(var t=1;t<arguments.length;t++){var s=null!=arguments[t]?arguments[t]:{};t%2?tl(Object(s),!0).forEach(function(t){d(e,t,s[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(s)):tl(Object(s)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(s,t))})}return e}d(ta,"contextType",f);var tp={onBoundsChanged:"bounds_changed",onClick:"click",onDblClick:"dblclick",onDrag:"drag",onDragEnd:"dragend",onDragStart:"dragstart",onMouseDown:"mousedown",onMouseMove:"mousemove",onMouseOut:"mouseout",onMouseOver:"mouseover",onMouseUp:"mouseup",onRightClick:"rightclick"},tc={bounds(e,t){e.setBounds(t)},draggable(e,t){e.setDraggable(t)},editable(e,t){e.setEditable(t)},map(e,t){e.setMap(t)},options(e,t){e.setOptions(t)},visible(e,t){e.setVisible(t)}};(0,c.memo)(function(e){var{options:t,bounds:s,draggable:n,editable:o,visible:i,onDblClick:r,onDragEnd:a,onDragStart:l,onMouseDown:u,onMouseMove:p,onMouseOut:h,onMouseOver:g,onMouseUp:d,onRightClick:m,onClick:v,onDrag:y,onBoundsChanged:L,onLoad:b,onUnmount:E}=e,w=(0,c.useContext)(f),[x,C]=(0,c.useState)(null),[M,k]=(0,c.useState)(null),[P,O]=(0,c.useState)(null),[S,I]=(0,c.useState)(null),[j,D]=(0,c.useState)(null),[_,B]=(0,c.useState)(null),[T,z]=(0,c.useState)(null),[R,A]=(0,c.useState)(null),[U,Z]=(0,c.useState)(null),[V,W]=(0,c.useState)(null),[N,H]=(0,c.useState)(null),[G,F]=(0,c.useState)(null),[Y,K]=(0,c.useState)(null);return(0,c.useEffect)(()=>{null!==x&&x.setMap(w)},[w]),(0,c.useEffect)(()=>{void 0!==t&&null!==x&&x.setOptions(t)},[x,t]),(0,c.useEffect)(()=>{void 0!==n&&null!==x&&x.setDraggable(n)},[x,n]),(0,c.useEffect)(()=>{void 0!==o&&null!==x&&x.setEditable(o)},[x,o]),(0,c.useEffect)(()=>{void 0!==i&&null!==x&&x.setVisible(i)},[x,i]),(0,c.useEffect)(()=>{void 0!==s&&null!==x&&x.setBounds(s)},[x,s]),(0,c.useEffect)(()=>{x&&r&&(null!==M&&google.maps.event.removeListener(M),k(google.maps.event.addListener(x,"dblclick",r)))},[r]),(0,c.useEffect)(()=>{x&&a&&(null!==P&&google.maps.event.removeListener(P),O(google.maps.event.addListener(x,"dragend",a)))},[a]),(0,c.useEffect)(()=>{x&&l&&(null!==S&&google.maps.event.removeListener(S),I(google.maps.event.addListener(x,"dragstart",l)))},[l]),(0,c.useEffect)(()=>{x&&u&&(null!==j&&google.maps.event.removeListener(j),D(google.maps.event.addListener(x,"mousedown",u)))},[u]),(0,c.useEffect)(()=>{x&&p&&(null!==_&&google.maps.event.removeListener(_),B(google.maps.event.addListener(x,"mousemove",p)))},[p]),(0,c.useEffect)(()=>{x&&h&&(null!==T&&google.maps.event.removeListener(T),z(google.maps.event.addListener(x,"mouseout",h)))},[h]),(0,c.useEffect)(()=>{x&&g&&(null!==R&&google.maps.event.removeListener(R),A(google.maps.event.addListener(x,"mouseover",g)))},[g]),(0,c.useEffect)(()=>{x&&d&&(null!==U&&google.maps.event.removeListener(U),Z(google.maps.event.addListener(x,"mouseup",d)))},[d]),(0,c.useEffect)(()=>{x&&m&&(null!==V&&google.maps.event.removeListener(V),W(google.maps.event.addListener(x,"rightclick",m)))},[m]),(0,c.useEffect)(()=>{x&&v&&(null!==N&&google.maps.event.removeListener(N),H(google.maps.event.addListener(x,"click",v)))},[v]),(0,c.useEffect)(()=>{x&&y&&(null!==G&&google.maps.event.removeListener(G),F(google.maps.event.addListener(x,"drag",y)))},[y]),(0,c.useEffect)(()=>{x&&L&&(null!==Y&&google.maps.event.removeListener(Y),K(google.maps.event.addListener(x,"bounds_changed",L)))},[L]),(0,c.useEffect)(()=>{var e=new google.maps.Rectangle(tu(tu({},t),{},{map:w}));return void 0!==i&&e.setVisible(i),void 0!==o&&e.setEditable(o),void 0!==n&&e.setDraggable(n),void 0!==s&&e.setBounds(s),r&&k(google.maps.event.addListener(e,"dblclick",r)),a&&O(google.maps.event.addListener(e,"dragend",a)),l&&I(google.maps.event.addListener(e,"dragstart",l)),u&&D(google.maps.event.addListener(e,"mousedown",u)),p&&B(google.maps.event.addListener(e,"mousemove",p)),h&&z(google.maps.event.addListener(e,"mouseout",h)),g&&A(google.maps.event.addListener(e,"mouseover",g)),d&&Z(google.maps.event.addListener(e,"mouseup",d)),m&&W(google.maps.event.addListener(e,"rightclick",m)),v&&H(google.maps.event.addListener(e,"click",v)),y&&F(google.maps.event.addListener(e,"drag",y)),L&&K(google.maps.event.addListener(e,"bounds_changed",L)),C(e),b&&b(e),()=>{null!==M&&google.maps.event.removeListener(M),null!==P&&google.maps.event.removeListener(P),null!==S&&google.maps.event.removeListener(S),null!==j&&google.maps.event.removeListener(j),null!==_&&google.maps.event.removeListener(_),null!==T&&google.maps.event.removeListener(T),null!==R&&google.maps.event.removeListener(R),null!==U&&google.maps.event.removeListener(U),null!==V&&google.maps.event.removeListener(V),null!==N&&google.maps.event.removeListener(N),null!==G&&google.maps.event.removeListener(G),null!==Y&&google.maps.event.removeListener(Y),E&&E(e),e.setMap(null)}},[]),null});class th extends c.PureComponent{constructor(){super(...arguments),d(this,"registeredEvents",[]),d(this,"state",{rectangle:null}),d(this,"setRectangleCallback",()=>{null!==this.state.rectangle&&this.props.onLoad&&this.props.onLoad(this.state.rectangle)})}componentDidMount(){var e=new google.maps.Rectangle(tu(tu({},this.props.options),{},{map:this.context}));this.registeredEvents=b({updaterMap:tc,eventMap:tp,prevProps:{},nextProps:this.props,instance:e}),this.setState(function(){return{rectangle:e}},this.setRectangleCallback)}componentDidUpdate(e){null!==this.state.rectangle&&(L(this.registeredEvents),this.registeredEvents=b({updaterMap:tc,eventMap:tp,prevProps:e,nextProps:this.props,instance:this.state.rectangle}))}componentWillUnmount(){null!==this.state.rectangle&&(this.props.onUnmount&&this.props.onUnmount(this.state.rectangle),L(this.registeredEvents),this.state.rectangle.setMap(null))}render(){return null}}function tg(e,t){var s=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),s.push.apply(s,n)}return s}function td(e){for(var t=1;t<arguments.length;t++){var s=null!=arguments[t]?arguments[t]:{};t%2?tg(Object(s),!0).forEach(function(t){d(e,t,s[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(s)):tg(Object(s)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(s,t))})}return e}d(th,"contextType",f);var tm={onCenterChanged:"center_changed",onRadiusChanged:"radius_changed",onClick:"click",onDblClick:"dblclick",onDrag:"drag",onDragEnd:"dragend",onDragStart:"dragstart",onMouseDown:"mousedown",onMouseMove:"mousemove",onMouseOut:"mouseout",onMouseOver:"mouseover",onMouseUp:"mouseup",onRightClick:"rightclick"},tv={center(e,t){e.setCenter(t)},draggable(e,t){e.setDraggable(t)},editable(e,t){e.setEditable(t)},map(e,t){e.setMap(t)},options(e,t){e.setOptions(t)},radius(e,t){e.setRadius(t)},visible(e,t){e.setVisible(t)}},tf={};(0,c.memo)(function(e){var{options:t,center:s,radius:n,draggable:o,editable:i,visible:r,onDblClick:a,onDragEnd:l,onDragStart:u,onMouseDown:p,onMouseMove:h,onMouseOut:g,onMouseOver:d,onMouseUp:m,onRightClick:v,onClick:y,onDrag:L,onCenterChanged:b,onRadiusChanged:E,onLoad:w,onUnmount:x}=e,C=(0,c.useContext)(f),[M,k]=(0,c.useState)(null),[P,O]=(0,c.useState)(null),[S,I]=(0,c.useState)(null),[j,D]=(0,c.useState)(null),[_,B]=(0,c.useState)(null),[T,z]=(0,c.useState)(null),[R,A]=(0,c.useState)(null),[U,Z]=(0,c.useState)(null),[V,W]=(0,c.useState)(null),[N,H]=(0,c.useState)(null),[G,F]=(0,c.useState)(null),[Y,K]=(0,c.useState)(null),[q,J]=(0,c.useState)(null),[X,$]=(0,c.useState)(null);return(0,c.useEffect)(()=>{null!==M&&M.setMap(C)},[C]),(0,c.useEffect)(()=>{void 0!==t&&null!==M&&M.setOptions(t)},[M,t]),(0,c.useEffect)(()=>{void 0!==o&&null!==M&&M.setDraggable(o)},[M,o]),(0,c.useEffect)(()=>{void 0!==i&&null!==M&&M.setEditable(i)},[M,i]),(0,c.useEffect)(()=>{void 0!==r&&null!==M&&M.setVisible(r)},[M,r]),(0,c.useEffect)(()=>{"number"==typeof n&&null!==M&&M.setRadius(n)},[M,n]),(0,c.useEffect)(()=>{void 0!==s&&null!==M&&M.setCenter(s)},[M,s]),(0,c.useEffect)(()=>{M&&a&&(null!==P&&google.maps.event.removeListener(P),O(google.maps.event.addListener(M,"dblclick",a)))},[a]),(0,c.useEffect)(()=>{M&&l&&(null!==S&&google.maps.event.removeListener(S),I(google.maps.event.addListener(M,"dragend",l)))},[l]),(0,c.useEffect)(()=>{M&&u&&(null!==j&&google.maps.event.removeListener(j),D(google.maps.event.addListener(M,"dragstart",u)))},[u]),(0,c.useEffect)(()=>{M&&p&&(null!==_&&google.maps.event.removeListener(_),B(google.maps.event.addListener(M,"mousedown",p)))},[p]),(0,c.useEffect)(()=>{M&&h&&(null!==T&&google.maps.event.removeListener(T),z(google.maps.event.addListener(M,"mousemove",h)))},[h]),(0,c.useEffect)(()=>{M&&g&&(null!==R&&google.maps.event.removeListener(R),A(google.maps.event.addListener(M,"mouseout",g)))},[g]),(0,c.useEffect)(()=>{M&&d&&(null!==U&&google.maps.event.removeListener(U),Z(google.maps.event.addListener(M,"mouseover",d)))},[d]),(0,c.useEffect)(()=>{M&&m&&(null!==V&&google.maps.event.removeListener(V),W(google.maps.event.addListener(M,"mouseup",m)))},[m]),(0,c.useEffect)(()=>{M&&v&&(null!==N&&google.maps.event.removeListener(N),H(google.maps.event.addListener(M,"rightclick",v)))},[v]),(0,c.useEffect)(()=>{M&&y&&(null!==G&&google.maps.event.removeListener(G),F(google.maps.event.addListener(M,"click",y)))},[y]),(0,c.useEffect)(()=>{M&&L&&(null!==Y&&google.maps.event.removeListener(Y),K(google.maps.event.addListener(M,"drag",L)))},[L]),(0,c.useEffect)(()=>{M&&b&&(null!==q&&google.maps.event.removeListener(q),J(google.maps.event.addListener(M,"center_changed",b)))},[y]),(0,c.useEffect)(()=>{M&&E&&(null!==X&&google.maps.event.removeListener(X),$(google.maps.event.addListener(M,"radius_changed",E)))},[E]),(0,c.useEffect)(()=>{var e=new google.maps.Circle(td(td({},t||tf),{},{map:C}));return"number"==typeof n&&e.setRadius(n),void 0!==s&&e.setCenter(s),"number"==typeof n&&e.setRadius(n),void 0!==r&&e.setVisible(r),void 0!==i&&e.setEditable(i),void 0!==o&&e.setDraggable(o),a&&O(google.maps.event.addListener(e,"dblclick",a)),l&&I(google.maps.event.addListener(e,"dragend",l)),u&&D(google.maps.event.addListener(e,"dragstart",u)),p&&B(google.maps.event.addListener(e,"mousedown",p)),h&&z(google.maps.event.addListener(e,"mousemove",h)),g&&A(google.maps.event.addListener(e,"mouseout",g)),d&&Z(google.maps.event.addListener(e,"mouseover",d)),m&&W(google.maps.event.addListener(e,"mouseup",m)),v&&H(google.maps.event.addListener(e,"rightclick",v)),y&&F(google.maps.event.addListener(e,"click",y)),L&&K(google.maps.event.addListener(e,"drag",L)),b&&J(google.maps.event.addListener(e,"center_changed",b)),E&&$(google.maps.event.addListener(e,"radius_changed",E)),k(e),w&&w(e),()=>{null!==P&&google.maps.event.removeListener(P),null!==S&&google.maps.event.removeListener(S),null!==j&&google.maps.event.removeListener(j),null!==_&&google.maps.event.removeListener(_),null!==T&&google.maps.event.removeListener(T),null!==R&&google.maps.event.removeListener(R),null!==U&&google.maps.event.removeListener(U),null!==V&&google.maps.event.removeListener(V),null!==N&&google.maps.event.removeListener(N),null!==G&&google.maps.event.removeListener(G),null!==q&&google.maps.event.removeListener(q),null!==X&&google.maps.event.removeListener(X),x&&x(e),e.setMap(null)}},[]),null});class ty extends c.PureComponent{constructor(){super(...arguments),d(this,"registeredEvents",[]),d(this,"state",{circle:null}),d(this,"setCircleCallback",()=>{null!==this.state.circle&&this.props.onLoad&&this.props.onLoad(this.state.circle)})}componentDidMount(){var e=new google.maps.Circle(td(td({},this.props.options),{},{map:this.context}));this.registeredEvents=b({updaterMap:tv,eventMap:tm,prevProps:{},nextProps:this.props,instance:e}),this.setState(function(){return{circle:e}},this.setCircleCallback)}componentDidUpdate(e){null!==this.state.circle&&(L(this.registeredEvents),this.registeredEvents=b({updaterMap:tv,eventMap:tm,prevProps:e,nextProps:this.props,instance:this.state.circle}))}componentWillUnmount(){if(null!==this.state.circle){var e;this.props.onUnmount&&this.props.onUnmount(this.state.circle),L(this.registeredEvents),null==(e=this.state.circle)||e.setMap(null)}}render(){return null}}function tL(e,t){var s=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),s.push.apply(s,n)}return s}function tb(e){for(var t=1;t<arguments.length;t++){var s=null!=arguments[t]?arguments[t]:{};t%2?tL(Object(s),!0).forEach(function(t){d(e,t,s[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(s)):tL(Object(s)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(s,t))})}return e}d(ty,"contextType",f);var tE={onClick:"click",onDblClick:"dblclick",onMouseDown:"mousedown",onMouseOut:"mouseout",onMouseOver:"mouseover",onMouseUp:"mouseup",onRightClick:"rightclick",onAddFeature:"addfeature",onRemoveFeature:"removefeature",onRemoveProperty:"removeproperty",onSetGeometry:"setgeometry",onSetProperty:"setproperty"},tw={add(e,t){e.add(t)},addgeojson(e,t,s){e.addGeoJson(t,s)},contains(e,t){e.contains(t)},foreach(e,t){e.forEach(t)},loadgeojson(e,t,s,n){e.loadGeoJson(t,s,n)},overridestyle(e,t,s){e.overrideStyle(t,s)},remove(e,t){e.remove(t)},revertstyle(e,t){e.revertStyle(t)},controlposition(e,t){e.setControlPosition(t)},controls(e,t){e.setControls(t)},drawingmode(e,t){e.setDrawingMode(t)},map(e,t){e.setMap(t)},style(e,t){e.setStyle(t)},togeojson(e,t){e.toGeoJson(t)}};(0,c.memo)(function(e){var{options:t,onClick:s,onDblClick:n,onMouseDown:o,onMouseMove:i,onMouseOut:r,onMouseOver:a,onMouseUp:l,onRightClick:u,onAddFeature:p,onRemoveFeature:h,onRemoveProperty:g,onSetGeometry:d,onSetProperty:m,onLoad:v,onUnmount:y}=e,L=(0,c.useContext)(f),[b,E]=(0,c.useState)(null),[w,x]=(0,c.useState)(null),[C,M]=(0,c.useState)(null),[k,P]=(0,c.useState)(null),[O,S]=(0,c.useState)(null),[I,j]=(0,c.useState)(null),[D,_]=(0,c.useState)(null),[B,T]=(0,c.useState)(null),[z,R]=(0,c.useState)(null),[A,U]=(0,c.useState)(null),[Z,V]=(0,c.useState)(null),[W,N]=(0,c.useState)(null),[H,G]=(0,c.useState)(null),[F,Y]=(0,c.useState)(null);return(0,c.useEffect)(()=>{null!==b&&b.setMap(L)},[L]),(0,c.useEffect)(()=>{b&&n&&(null!==w&&google.maps.event.removeListener(w),x(google.maps.event.addListener(b,"dblclick",n)))},[n]),(0,c.useEffect)(()=>{b&&o&&(null!==C&&google.maps.event.removeListener(C),M(google.maps.event.addListener(b,"mousedown",o)))},[o]),(0,c.useEffect)(()=>{b&&i&&(null!==k&&google.maps.event.removeListener(k),P(google.maps.event.addListener(b,"mousemove",i)))},[i]),(0,c.useEffect)(()=>{b&&r&&(null!==O&&google.maps.event.removeListener(O),S(google.maps.event.addListener(b,"mouseout",r)))},[r]),(0,c.useEffect)(()=>{b&&a&&(null!==I&&google.maps.event.removeListener(I),j(google.maps.event.addListener(b,"mouseover",a)))},[a]),(0,c.useEffect)(()=>{b&&l&&(null!==D&&google.maps.event.removeListener(D),_(google.maps.event.addListener(b,"mouseup",l)))},[l]),(0,c.useEffect)(()=>{b&&u&&(null!==B&&google.maps.event.removeListener(B),T(google.maps.event.addListener(b,"rightclick",u)))},[u]),(0,c.useEffect)(()=>{b&&s&&(null!==z&&google.maps.event.removeListener(z),R(google.maps.event.addListener(b,"click",s)))},[s]),(0,c.useEffect)(()=>{b&&p&&(null!==A&&google.maps.event.removeListener(A),U(google.maps.event.addListener(b,"addfeature",p)))},[p]),(0,c.useEffect)(()=>{b&&h&&(null!==Z&&google.maps.event.removeListener(Z),V(google.maps.event.addListener(b,"removefeature",h)))},[h]),(0,c.useEffect)(()=>{b&&g&&(null!==W&&google.maps.event.removeListener(W),N(google.maps.event.addListener(b,"removeproperty",g)))},[g]),(0,c.useEffect)(()=>{b&&d&&(null!==H&&google.maps.event.removeListener(H),G(google.maps.event.addListener(b,"setgeometry",d)))},[d]),(0,c.useEffect)(()=>{b&&m&&(null!==F&&google.maps.event.removeListener(F),Y(google.maps.event.addListener(b,"setproperty",m)))},[m]),(0,c.useEffect)(()=>{if(null!==L){var e=new google.maps.Data(tb(tb({},t),{},{map:L}));n&&x(google.maps.event.addListener(e,"dblclick",n)),o&&M(google.maps.event.addListener(e,"mousedown",o)),i&&P(google.maps.event.addListener(e,"mousemove",i)),r&&S(google.maps.event.addListener(e,"mouseout",r)),a&&j(google.maps.event.addListener(e,"mouseover",a)),l&&_(google.maps.event.addListener(e,"mouseup",l)),u&&T(google.maps.event.addListener(e,"rightclick",u)),s&&R(google.maps.event.addListener(e,"click",s)),p&&U(google.maps.event.addListener(e,"addfeature",p)),h&&V(google.maps.event.addListener(e,"removefeature",h)),g&&N(google.maps.event.addListener(e,"removeproperty",g)),d&&G(google.maps.event.addListener(e,"setgeometry",d)),m&&Y(google.maps.event.addListener(e,"setproperty",m)),E(e),v&&v(e)}return()=>{b&&(null!==w&&google.maps.event.removeListener(w),null!==C&&google.maps.event.removeListener(C),null!==k&&google.maps.event.removeListener(k),null!==O&&google.maps.event.removeListener(O),null!==I&&google.maps.event.removeListener(I),null!==D&&google.maps.event.removeListener(D),null!==B&&google.maps.event.removeListener(B),null!==z&&google.maps.event.removeListener(z),null!==A&&google.maps.event.removeListener(A),null!==Z&&google.maps.event.removeListener(Z),null!==W&&google.maps.event.removeListener(W),null!==H&&google.maps.event.removeListener(H),null!==F&&google.maps.event.removeListener(F),y&&y(b),b.setMap(null))}},[]),null});class tx extends c.PureComponent{constructor(){super(...arguments),d(this,"registeredEvents",[]),d(this,"state",{data:null}),d(this,"setDataCallback",()=>{null!==this.state.data&&this.props.onLoad&&this.props.onLoad(this.state.data)})}componentDidMount(){if(null!==this.context){var e=new google.maps.Data(tb(tb({},this.props.options),{},{map:this.context}));this.registeredEvents=b({updaterMap:tw,eventMap:tE,prevProps:{},nextProps:this.props,instance:e}),this.setState(()=>({data:e}),this.setDataCallback)}}componentDidUpdate(e){null!==this.state.data&&(L(this.registeredEvents),this.registeredEvents=b({updaterMap:tw,eventMap:tE,prevProps:e,nextProps:this.props,instance:this.state.data}))}componentWillUnmount(){null!==this.state.data&&(this.props.onUnmount&&this.props.onUnmount(this.state.data),L(this.registeredEvents),this.state.data&&this.state.data.setMap(null))}render(){return null}}function tC(e,t){var s=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),s.push.apply(s,n)}return s}function tM(e){for(var t=1;t<arguments.length;t++){var s=null!=arguments[t]?arguments[t]:{};t%2?tC(Object(s),!0).forEach(function(t){d(e,t,s[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(s)):tC(Object(s)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(s,t))})}return e}d(tx,"contextType",f);var tk={onClick:"click",onDefaultViewportChanged:"defaultviewport_changed",onStatusChanged:"status_changed"},tP={options(e,t){e.setOptions(t)},url(e,t){e.setUrl(t)},zIndex(e,t){e.setZIndex(t)}};class tO extends c.PureComponent{constructor(){super(...arguments),d(this,"registeredEvents",[]),d(this,"state",{kmlLayer:null}),d(this,"setKmlLayerCallback",()=>{null!==this.state.kmlLayer&&this.props.onLoad&&this.props.onLoad(this.state.kmlLayer)})}componentDidMount(){var e=new google.maps.KmlLayer(tM(tM({},this.props.options),{},{map:this.context}));this.registeredEvents=b({updaterMap:tP,eventMap:tk,prevProps:{},nextProps:this.props,instance:e}),this.setState(function(){return{kmlLayer:e}},this.setKmlLayerCallback)}componentDidUpdate(e){null!==this.state.kmlLayer&&(L(this.registeredEvents),this.registeredEvents=b({updaterMap:tP,eventMap:tk,prevProps:e,nextProps:this.props,instance:this.state.kmlLayer}))}componentWillUnmount(){null!==this.state.kmlLayer&&(this.props.onUnmount&&this.props.onUnmount(this.state.kmlLayer),L(this.registeredEvents),this.state.kmlLayer.setMap(null))}render(){return null}}function tS(e,t){return"function"==typeof t?t(e.offsetWidth,e.offsetHeight):{x:0,y:0}}function tI(e,t){return new t(e.lat,e.lng)}function tj(e,t){return new t(new google.maps.LatLng(e.ne.lat,e.ne.lng),new google.maps.LatLng(e.sw.lat,e.sw.lng))}function tD(e,t,s,n){var o,i,r,a,l;return void 0!==s?(o=google.maps.LatLngBounds,i=s instanceof o?s:tj(s,o),r=e&&e.fromLatLngToDivPixel(i.getNorthEast()),a=e&&e.fromLatLngToDivPixel(i.getSouthWest()),r&&a?{left:"".concat(a.x+t.x,"px"),top:"".concat(r.y+t.y,"px"),width:"".concat(r.x-a.x-t.x,"px"),height:"".concat(a.y-r.y-t.y,"px")}:{left:"-9999px",top:"-9999px"}):function(e,t,s){var n=e&&e.fromLatLngToDivPixel(s);if(n){var{x:o,y:i}=n;return{left:"".concat(o+t.x,"px"),top:"".concat(i+t.y,"px")}}return{left:"-9999px",top:"-9999px"}}(e,t,(l=google.maps.LatLng,n instanceof l?n:tI(n,l)))}function t_(e,t){var s=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),s.push.apply(s,n)}return s}function tB(e,t){var s=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),s.push.apply(s,n)}return s}function tT(e){return e?(e instanceof google.maps.LatLng?e:new google.maps.LatLng(e.lat,e.lng))+"":""}function tz(e){return e?(e instanceof google.maps.LatLngBounds?e:new google.maps.LatLngBounds(new google.maps.LatLng(e.south,e.east),new google.maps.LatLng(e.north,e.west)))+"":""}d(tO,"contextType",f),(0,c.memo)(function(e){var{position:t,bounds:s,mapPaneName:n,zIndex:o,onLoad:i,onUnmount:r,getPixelPositionOffset:a,children:l}=e,u=(0,c.useContext)(f),p=(0,c.useMemo)(()=>{var e=document.createElement("div");return e.style.position="absolute",e},[]),g=(0,c.useMemo)(()=>(function(e,t,s,n,o){class i extends google.maps.OverlayView{constructor(e,t,s,n){super(),this.container=e,this.pane=t,this.position=s,this.bounds=n}onAdd(){var e,t=null==(e=this.getPanes())?void 0:e[this.pane];null==t||t.appendChild(this.container)}draw(){for(var[e,t]of Object.entries(tD(this.getProjection(),function(e){for(var t=1;t<arguments.length;t++){var s=null!=arguments[t]?arguments[t]:{};t%2?t_(Object(s),!0).forEach(function(t){d(e,t,s[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(s)):t_(Object(s)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(s,t))})}return e}({},this.container?tS(this.container,o):{x:0,y:0}),this.bounds,this.position)))this.container.style[e]=t}onRemove(){null!==this.container.parentNode&&this.container.parentNode.removeChild(this.container)}}return new i(e,t,s,n)})(p,n,t,s,a),[p,n,t,s]);return(0,c.useEffect)(()=>(null==i||i(g),null==g||g.setMap(u),()=>{null==r||r(g),null==g||g.setMap(null)}),[u,g]),(0,c.useEffect)(()=>{p.style.zIndex="".concat(o)},[o,p]),h.createPortal(l,p)});class tR extends c.PureComponent{constructor(e){super(e),d(this,"state",{paneEl:null,containerStyle:{position:"absolute"}}),d(this,"updatePane",()=>{var e=this.props.mapPaneName,t=this.overlayView.getPanes();v(!!e,"OverlayView requires props.mapPaneName but got %s",e),t?this.setState({paneEl:t[e]}):this.setState({paneEl:null})}),d(this,"onAdd",()=>{var e,t;this.updatePane(),null==(e=(t=this.props).onLoad)||e.call(t,this.overlayView)}),d(this,"onPositionElement",()=>{var e,t,s,n,o=tD(this.overlayView.getProjection(),function(e){for(var t=1;t<arguments.length;t++){var s=null!=arguments[t]?arguments[t]:{};t%2?tB(Object(s),!0).forEach(function(t){d(e,t,s[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(s)):tB(Object(s)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(s,t))})}return e}({x:0,y:0},this.containerRef.current?tS(this.containerRef.current,this.props.getPixelPositionOffset):{}),this.props.bounds,this.props.position);!function(e,t){return e.left===t.left&&e.top===t.top&&e.width===t.height&&e.height===t.height}(o,{left:this.state.containerStyle.left,top:this.state.containerStyle.top,width:this.state.containerStyle.width,height:this.state.containerStyle.height})&&this.setState({containerStyle:{top:null!=(e=o.top)?e:0,left:null!=(t=o.left)?t:0,width:null!=(s=o.width)?s:0,height:null!=(n=o.height)?n:0,position:"absolute"}})}),d(this,"draw",()=>{this.onPositionElement()}),d(this,"onRemove",()=>{var e,t;this.setState(()=>({paneEl:null})),null==(e=(t=this.props).onUnmount)||e.call(t,this.overlayView)}),this.containerRef=(0,c.createRef)();var t=new google.maps.OverlayView;t.onAdd=this.onAdd,t.draw=this.draw,t.onRemove=this.onRemove,this.overlayView=t}componentDidMount(){this.overlayView.setMap(this.context)}componentDidUpdate(e){var t=tT(e.position),s=tT(this.props.position),n=tz(e.bounds),o=tz(this.props.bounds);(t!==s||n!==o)&&this.overlayView.draw(),e.mapPaneName!==this.props.mapPaneName&&this.updatePane()}componentWillUnmount(){this.overlayView.setMap(null)}render(){var e=this.state.paneEl;return e?h.createPortal((0,p.jsx)("div",{ref:this.containerRef,style:this.state.containerStyle,children:c.Children.only(this.props.children)}),e):null}}function tA(e,t){var s=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),s.push.apply(s,n)}return s}function tU(e){for(var t=1;t<arguments.length;t++){var s=null!=arguments[t]?arguments[t]:{};t%2?tA(Object(s),!0).forEach(function(t){d(e,t,s[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(s)):tA(Object(s)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(s,t))})}return e}d(tR,"FLOAT_PANE","floatPane"),d(tR,"MAP_PANE","mapPane"),d(tR,"MARKER_LAYER","markerLayer"),d(tR,"OVERLAY_LAYER","overlayLayer"),d(tR,"OVERLAY_MOUSE_TARGET","overlayMouseTarget"),d(tR,"contextType",f);var tZ={onDblClick:"dblclick",onClick:"click"},tV={opacity(e,t){e.setOpacity(t)}};(0,c.memo)(function(e){var{url:t,bounds:s,options:n,visible:o}=e,i=(0,c.useContext)(f),r=new google.maps.LatLngBounds(new google.maps.LatLng(s.south,s.west),new google.maps.LatLng(s.north,s.east)),a=(0,c.useMemo)(()=>new google.maps.GroundOverlay(t,r,n),[]);return(0,c.useEffect)(()=>{null!==a&&a.setMap(i)},[i]),(0,c.useEffect)(()=>{void 0!==t&&null!==a&&(a.set("url",t),a.setMap(i))},[a,t]),(0,c.useEffect)(()=>{void 0!==o&&null!==a&&a.setOpacity(+!!o)},[a,o]),(0,c.useEffect)(()=>{var e=new google.maps.LatLngBounds(new google.maps.LatLng(s.south,s.west),new google.maps.LatLng(s.north,s.east));void 0!==s&&null!==a&&(a.set("bounds",e),a.setMap(i))},[a,s]),null});class tW extends c.PureComponent{constructor(){super(...arguments),d(this,"registeredEvents",[]),d(this,"state",{groundOverlay:null}),d(this,"setGroundOverlayCallback",()=>{null!==this.state.groundOverlay&&this.props.onLoad&&this.props.onLoad(this.state.groundOverlay)})}componentDidMount(){v(!!this.props.url||!!this.props.bounds,"For GroundOverlay, url and bounds are passed in to constructor and are immutable after instantiated. This is the behavior of Google Maps JavaScript API v3 ( See https://developers.google.com/maps/documentation/javascript/reference#GroundOverlay) Hence, use the corresponding two props provided by `react-google-maps-api`, url and bounds. In some cases, you'll need the GroundOverlay component to reflect the changes of url and bounds. You can leverage the React's key property to remount the component. Typically, just `key={url}` would serve your need. See https://github.com/tomchentw/react-google-maps/issues/655");var e=new google.maps.GroundOverlay(this.props.url,this.props.bounds,tU(tU({},this.props.options),{},{map:this.context}));this.registeredEvents=b({updaterMap:tV,eventMap:tZ,prevProps:{},nextProps:this.props,instance:e}),this.setState(function(){return{groundOverlay:e}},this.setGroundOverlayCallback)}componentDidUpdate(e){null!==this.state.groundOverlay&&(L(this.registeredEvents),this.registeredEvents=b({updaterMap:tV,eventMap:tZ,prevProps:e,nextProps:this.props,instance:this.state.groundOverlay}))}componentWillUnmount(){this.state.groundOverlay&&(this.props.onUnmount&&this.props.onUnmount(this.state.groundOverlay),this.state.groundOverlay.setMap(null))}render(){return null}}function tN(e,t){var s=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),s.push.apply(s,n)}return s}function tH(e){for(var t=1;t<arguments.length;t++){var s=null!=arguments[t]?arguments[t]:{};t%2?tN(Object(s),!0).forEach(function(t){d(e,t,s[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(s)):tN(Object(s)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(s,t))})}return e}d(tW,"defaultProps",{onLoad:function(){}}),d(tW,"contextType",f);var tG={},tF={data(e,t){e.setData(t)},map(e,t){e.setMap(t)},options(e,t){e.setOptions(t)}};(0,c.memo)(function(e){var{data:t,onLoad:s,onUnmount:n,options:o}=e,i=(0,c.useContext)(f),[r,a]=(0,c.useState)(null);return(0,c.useEffect)(()=>{google.maps.visualization||v(!!google.maps.visualization,'Did you include prop libraries={["visualization"]} in useJsApiScript? %s',google.maps.visualization)},[]),(0,c.useEffect)(()=>{v(!!t,"data property is required in HeatmapLayer %s",t)},[t]),(0,c.useEffect)(()=>{null!==r&&r.setMap(i)},[i]),(0,c.useEffect)(()=>{o&&null!==r&&r.setOptions(o)},[r,o]),(0,c.useEffect)(()=>{var e=new google.maps.visualization.HeatmapLayer(tH(tH({},o),{},{data:t,map:i}));return a(e),s&&s(e),()=>{null!==r&&(n&&n(r),r.setMap(null))}},[]),null});class tY extends c.PureComponent{constructor(){super(...arguments),d(this,"registeredEvents",[]),d(this,"state",{heatmapLayer:null}),d(this,"setHeatmapLayerCallback",()=>{null!==this.state.heatmapLayer&&this.props.onLoad&&this.props.onLoad(this.state.heatmapLayer)})}componentDidMount(){v(!!google.maps.visualization,'Did you include prop libraries={["visualization"]} to <LoadScript />? %s',google.maps.visualization),v(!!this.props.data,"data property is required in HeatmapLayer %s",this.props.data);var e=new google.maps.visualization.HeatmapLayer(tH(tH({},this.props.options),{},{data:this.props.data,map:this.context}));this.registeredEvents=b({updaterMap:tF,eventMap:tG,prevProps:{},nextProps:this.props,instance:e}),this.setState(function(){return{heatmapLayer:e}},this.setHeatmapLayerCallback)}componentDidUpdate(e){L(this.registeredEvents),this.registeredEvents=b({updaterMap:tF,eventMap:tG,prevProps:e,nextProps:this.props,instance:this.state.heatmapLayer})}componentWillUnmount(){null!==this.state.heatmapLayer&&(this.props.onUnmount&&this.props.onUnmount(this.state.heatmapLayer),L(this.registeredEvents),this.state.heatmapLayer.setMap(null))}render(){return null}}d(tY,"contextType",f);var tK={onCloseClick:"closeclick",onPanoChanged:"pano_changed",onPositionChanged:"position_changed",onPovChanged:"pov_changed",onResize:"resize",onStatusChanged:"status_changed",onVisibleChanged:"visible_changed",onZoomChanged:"zoom_changed"},tq={register(e,t,s){e.registerPanoProvider(t,s)},links(e,t){e.setLinks(t)},motionTracking(e,t){e.setMotionTracking(t)},options(e,t){e.setOptions(t)},pano(e,t){e.setPano(t)},position(e,t){e.setPosition(t)},pov(e,t){e.setPov(t)},visible(e,t){e.setVisible(t)},zoom(e,t){e.setZoom(t)}};class tJ extends c.PureComponent{constructor(){super(...arguments),d(this,"registeredEvents",[]),d(this,"state",{streetViewPanorama:null}),d(this,"setStreetViewPanoramaCallback",()=>{null!==this.state.streetViewPanorama&&this.props.onLoad&&this.props.onLoad(this.state.streetViewPanorama)})}componentDidMount(){var e,t,s=null!=(e=null==(t=this.context)?void 0:t.getStreetView())?e:null;this.registeredEvents=b({updaterMap:tq,eventMap:tK,prevProps:{},nextProps:this.props,instance:s}),this.setState(()=>({streetViewPanorama:s}),this.setStreetViewPanoramaCallback)}componentDidUpdate(e){null!==this.state.streetViewPanorama&&(L(this.registeredEvents),this.registeredEvents=b({updaterMap:tq,eventMap:tK,prevProps:e,nextProps:this.props,instance:this.state.streetViewPanorama}))}componentWillUnmount(){null!==this.state.streetViewPanorama&&(this.props.onUnmount&&this.props.onUnmount(this.state.streetViewPanorama),L(this.registeredEvents),this.state.streetViewPanorama.setVisible(!1))}render(){return null}}d(tJ,"contextType",f);class tX extends c.PureComponent{constructor(){super(...arguments),d(this,"state",{streetViewService:null}),d(this,"setStreetViewServiceCallback",()=>{null!==this.state.streetViewService&&this.props.onLoad&&this.props.onLoad(this.state.streetViewService)})}componentDidMount(){var e=new google.maps.StreetViewService;this.setState(function(){return{streetViewService:e}},this.setStreetViewServiceCallback)}componentWillUnmount(){null!==this.state.streetViewService&&this.props.onUnmount&&this.props.onUnmount(this.state.streetViewService)}render(){return null}}d(tX,"contextType",f),c.PureComponent;var t$={onDirectionsChanged:"directions_changed"},tQ={directions(e,t){e.setDirections(t)},map(e,t){e.setMap(t)},options(e,t){e.setOptions(t)},panel(e,t){e.setPanel(t)},routeIndex(e,t){e.setRouteIndex(t)}};class t0 extends c.PureComponent{constructor(){super(...arguments),d(this,"registeredEvents",[]),d(this,"state",{directionsRenderer:null}),d(this,"setDirectionsRendererCallback",()=>{null!==this.state.directionsRenderer&&(this.state.directionsRenderer.setMap(this.context),this.props.onLoad&&this.props.onLoad(this.state.directionsRenderer))})}componentDidMount(){var e=new google.maps.DirectionsRenderer(this.props.options);this.registeredEvents=b({updaterMap:tQ,eventMap:t$,prevProps:{},nextProps:this.props,instance:e}),this.setState(function(){return{directionsRenderer:e}},this.setDirectionsRendererCallback)}componentDidUpdate(e){null!==this.state.directionsRenderer&&(L(this.registeredEvents),this.registeredEvents=b({updaterMap:tQ,eventMap:t$,prevProps:e,nextProps:this.props,instance:this.state.directionsRenderer}))}componentWillUnmount(){null!==this.state.directionsRenderer&&(this.props.onUnmount&&this.props.onUnmount(this.state.directionsRenderer),L(this.registeredEvents),this.state.directionsRenderer&&this.state.directionsRenderer.setMap(null))}render(){return null}}d(t0,"contextType",f),c.PureComponent;var t1={onPlacesChanged:"places_changed"},t2={bounds(e,t){e.setBounds(t)}};class t3 extends c.PureComponent{constructor(){super(...arguments),d(this,"registeredEvents",[]),d(this,"containerElement",(0,c.createRef)()),d(this,"state",{searchBox:null}),d(this,"setSearchBoxCallback",()=>{null!==this.state.searchBox&&this.props.onLoad&&this.props.onLoad(this.state.searchBox)})}componentDidMount(){if(v(!!google.maps.places,'You need to provide libraries={["places"]} prop to <LoadScript /> component %s',google.maps.places),null!==this.containerElement&&null!==this.containerElement.current){var e=this.containerElement.current.querySelector("input");if(null!==e){var t=new google.maps.places.SearchBox(e,this.props.options);this.registeredEvents=b({updaterMap:t2,eventMap:t1,prevProps:{},nextProps:this.props,instance:t}),this.setState(function(){return{searchBox:t}},this.setSearchBoxCallback)}}}componentDidUpdate(e){null!==this.state.searchBox&&(L(this.registeredEvents),this.registeredEvents=b({updaterMap:t2,eventMap:t1,prevProps:e,nextProps:this.props,instance:this.state.searchBox}))}componentWillUnmount(){null!==this.state.searchBox&&(this.props.onUnmount&&this.props.onUnmount(this.state.searchBox),L(this.registeredEvents))}render(){return(0,p.jsx)("div",{ref:this.containerElement,children:c.Children.only(this.props.children)})}}d(t3,"contextType",f);var t8={onPlaceChanged:"place_changed"},t6={bounds(e,t){e.setBounds(t)},restrictions(e,t){e.setComponentRestrictions(t)},fields(e,t){e.setFields(t)},options(e,t){e.setOptions(t)},types(e,t){e.setTypes(t)}};class t5 extends c.PureComponent{constructor(){super(...arguments),d(this,"registeredEvents",[]),d(this,"containerElement",(0,c.createRef)()),d(this,"state",{autocomplete:null}),d(this,"setAutocompleteCallback",()=>{null!==this.state.autocomplete&&this.props.onLoad&&this.props.onLoad(this.state.autocomplete)})}componentDidMount(){v(!!google.maps.places,'You need to provide libraries={["places"]} prop to <LoadScript /> component %s',google.maps.places);var e,t=null==(e=this.containerElement.current)?void 0:e.querySelector("input");if(t){var s=new google.maps.places.Autocomplete(t,this.props.options);this.registeredEvents=b({updaterMap:t6,eventMap:t8,prevProps:{},nextProps:this.props,instance:s}),this.setState(()=>({autocomplete:s}),this.setAutocompleteCallback)}}componentDidUpdate(e){L(this.registeredEvents),this.registeredEvents=b({updaterMap:t6,eventMap:t8,prevProps:e,nextProps:this.props,instance:this.state.autocomplete})}componentWillUnmount(){null!==this.state.autocomplete&&L(this.registeredEvents)}render(){return(0,p.jsx)("div",{ref:this.containerElement,className:this.props.className,children:c.Children.only(this.props.children)})}}d(t5,"defaultProps",{className:""}),d(t5,"contextType",f)}}]);
//# sourceMappingURL=e098907b-28cd164d1c549b26.js.map