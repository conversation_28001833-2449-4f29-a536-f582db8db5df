"use strict";exports.id=8706,exports.ids=[8706],exports.modules={13054:(e,s,a)=>{a.a(e,async(e,t)=>{try{a.d(s,{A:()=>w});var n=a(8732),r=a(64801),c=a(74716),i=a.n(c);a(16444);var l=a(44233),o=a.n(l),d=a(7082),u=a(83551),m=a(49481),p=a(82053),h=a(54131),v=a(27825),x=a.n(v),j=a(88751),f=e([h]);h=(f.then?(await f)():f)[0];let A=(0,r.momentLocalizer)(i());function b(e){let{t:s,i18n:a}=(0,j.useTranslation)("common"),t=a.language,{eventsList:c,style:i,minicalendar:l,views:d}=e,u={};return e.showEventCounts&&(u={eventWrapper:N,month:{dateHeader:e=>(0,n.jsx)(_,{eventsList:c,...e})}}),l&&(u.toolbar=g),(0,n.jsx)(r.Calendar,{culture:t,localizer:A,events:c,views:d,startAccessor:"start_date",endAccessor:"end_date",style:i,components:u,messages:{today:s("today"),previous:s("back"),next:s("Next"),month:s("Month"),week:s("Week"),day:s("Day")},onSelectEvent:e=>{let s=Object.keys(e).filter(e=>e.includes("parent")).toString(),a=s.split("_")[1];o().push(`/${a}/show/${e[s]}/update/${e._id}`)}})}function g(e){return(0,n.jsx)(d.A,{className:"mb-1",children:(0,n.jsxs)(u.A,{children:[(0,n.jsx)(m.A,{className:"p-0",md:1,children:(0,n.jsx)("i",{style:{cursor:"pointer"},onClick:()=>e.onNavigate("PREV"),className:"fas fa-chevron-left"})}),(0,n.jsx)(m.A,{className:"text-center",md:10,children:(0,n.jsx)("span",{className:"rbc-toolbar-label",children:e.label})}),(0,n.jsx)(m.A,{className:"p-0 text-end",md:1,children:(0,n.jsx)("i",{style:{cursor:"pointer"},onClick:()=>e.onNavigate("NEXT"),className:"fas fa-chevron-right"})})]})})}b.defaultProps={minicalendar:!1,views:["month"]};let y=(e,s)=>{let a=0;return x().forEach(e,e=>{let t=i()(e.start_date).set({hour:0,minute:0,second:0,millisecond:0}),n=i()(e.end_date).set({hour:0,minute:0,second:0,millisecond:0});i()(s).isBetween(t,n,null,"[]")&&(a+=1)}),a},_=({date:e,label:s,eventsList:a})=>{let t=y(a,e),r=i()(e).isBefore(new Date,"day");return(0,n.jsxs)("div",{className:"rbc-date-cell",onClick:()=>o().push("/events-calendar"),children:[(0,n.jsx)("a",{href:"#",children:s}),t>0&&(0,n.jsxs)("span",{className:"d-flex justify-content-start align-items-center fa-stack",children:[(0,n.jsx)(p.FontAwesomeIcon,{icon:h.faStar,color:r?"grey":"#04A6FB",size:"lg"}),(0,n.jsx)("span",{className:"eventCount",children:t})]})]})},N=e=>(0,n.jsx)("div",{onSelect:e.onSelect}),w=b;t()}catch(e){t(e)}})},44549:(e,s,a)=>{a.r(s),a.d(s,{default:()=>i});var t=a(8732),n=a(27825),r=a.n(n),c=a(56084);let i=e=>(0,t.jsx)(t.Fragment,{children:(0,t.jsx)(c.A,{noHeader:!0,columns:e.Monitoringmembers.columns,data:e.Monitoringmembers.subscribers,dense:!0,paginationServer:!0,pagServer:!0,paginationTotalRows:0,subHeader:!0,subHeaderAlign:"left",pagination:!0,persistTableHead:!0,sortFunction:(e,s,a)=>r().orderBy(e,e=>e[s]?e[s].toLowerCase():e[s],a)})})},81426:(e,s,a)=>{a.a(e,async(e,t)=>{try{a.d(s,{A:()=>m});var n=a(8732),r=a(14062),c=a(54131),i=a(82015),l=a(82053),o=a(63487),d=e([r,c,o]);[r,c,o]=d.then?(await d)():d;let u={operation:"Operation",institution:"Institution",event:"Event",project:"Project",vspace:"Vspace"},m=(0,r.connect)(e=>e)(e=>{let{user:s,entityId:a,entityType:t}=e,[r,d]=(0,i.useState)(!1),[m,p]=(0,i.useState)(""),h=async()=>{if(!s?._id)return;let e=await o.A.get("/flag",{query:{entity_id:a,user:s._id,onModel:u[t]}});e&&e.data&&e.data.length>0&&(p(e.data[0]),d(!0))},v=async e=>{if(e.preventDefault(),!s?._id)return;let n=!r,c={entity_type:t,entity_id:a,user:s._id,onModel:u[t]};if(n){let e=await o.A.post("/flag",c);e&&e._id&&(p(e),d(n))}else{let e=await o.A.remove(`/flag/${m._id}`);e&&e.n&&d(n)}};return(0,i.useEffect)(()=>{h()},[]),(0,n.jsx)("div",{className:"subscribe-flag",children:(0,n.jsxs)("a",{href:"",onClick:v,children:[(0,n.jsx)("span",{className:"check",children:r?(0,n.jsx)(l.FontAwesomeIcon,{className:"clickable checkIcon",icon:c.faCheckCircle,color:"#00CC00"}):(0,n.jsx)(l.FontAwesomeIcon,{className:"clickable minusIcon",icon:c.faPlusCircle,color:"#fff"})}),(0,n.jsx)(l.FontAwesomeIcon,{className:"bookmark",icon:c.faBookmark,color:"#d4d4d4"})]})})});t()}catch(e){t(e)}})},88706:(e,s,a)=>{a.a(e,async(e,t)=>{try{a.r(s),a.d(s,{default:()=>D});var n=a(8732),r=a(82015),c=a(91353),i=a(93024),l=a(7082),o=a(83551),d=a(49481),u=a(44233),m=a(74716),p=a.n(m);a(86843);var h=a(82053),v=a(54131),x=a(19918),j=a.n(x),f=a(63487),b=a(82491),g=a(63349),A=a(88751),y=a(77136),_=a(90639),N=a(81426),w=a(45400),S=a(44549),C=a(99435),M=a(13245),k=e([v,f,b,y,_,N,M]);[v,f,b,y,_,N,M]=k.then?(await k)():k;let q={true:"Public - Accessible to all site users",false:"Private - Accessible only to group members"},E=[{name:"User Name",selector:"username",sortable:!0},{name:"Email",selector:"email",sortable:!0}],D=e=>{let{t:s,i18n:a}=(0,A.useTranslation)("common"),[t,m]=(0,r.useState)({description:"",owner:""}),x="fr"===a.language?"en":a.language,[k,D]=(0,r.useState)([]),[F,I]=(0,r.useState)([]),[T,R]=(0,r.useState)([]),[$]=(0,r.useState)([]),[H]=(0,r.useState)(x),[P,B]=(0,r.useState)([]),[L,U]=(0,r.useState)([]),[Y,V]=(0,r.useState)(!1),[z,O]=(0,r.useState)(!1),W=(0,u.useRouter)().query.routes||[],G={sort:{doc_created_at:"dsc"},Doctable:!0},K={sort:{doc_created_at:"dsc"},collation:"en",updateDoctable:!0},X=async()=>{let e=[];V(!0);let s=await f.A.get(`/vspace/${W[1]}`,K);s&&s.data&&Array.isArray(s.data)&&s.data.length>=1&&(s.data.forEach((s,a)=>{s.document&&s.document.length>0&&s.document.map((a,t)=>{a.description=s.document[t].docsrc,e.push(a)})}),R(e)),V(!1)},J=async()=>{let e=[];O(!0);let s=await f.A.get(`/vspace/${W[1]}`,G);s&&s.data&&Array.isArray(s.data)&&s.data.length>=1&&(s.data.forEach((s,a)=>{s.document&&s.document.length>0&&s.document.map((a,t)=>{a.description=s.document[t].docsrc,e.push(a)})}),U(e)),O(!1)},Q=async()=>{try{let e=await f.A.get(`/vspace/${W[1]}`),s=await f.A.get("/vspace-request-subscribers/getRequestedToMe",{query:{vspace:W[1]},select:"-vspace -requested_to -created_at -updated_at"});if(s&&s.data&&s.data.length>0){let e=s.data.map(e=>(e.requested_by._id=e._id,e.requested_by));D(e)}let a=e?.nonMembers.filter(s=>!e?.members.map(e=>e.email).includes(s));if(a.length>0){let s=await f.A.get("/users",{query:{vspace_status:"Approved",is_vspace:!0},sort:{username:"asc"},limit:"~"}),t=a.map(e=>s.data.filter(s=>s.email==e).length>0?s.data.filter(s=>s.email==e)[0]._id:"");if((t=t.filter(e=>""!=e)).length>0){let a=t,n=e&&e.members.map(e=>e._id);await f.A.patch(`/vspace/${e._id}`,{...e,members:[...n,...a],nonMembers:""===e.nonMembers[0]?"":e.nonMembers});let r=a?.map(e=>s?.data.filter(s=>s._id==e).length>0?s.data.filter(s=>s._id==e)[0]:[]);r=r.filter(e=>""!=e),e.members=e.members.concat(r)}}I(e.members),m(e),B([{title:"VSpace",start:e.start_date,end:e.end_date,allDay:!0,images:e.images,images_src:e.images_src,document:e.document,doc_src:e.doc_src}])}catch(e){console.log(e)}};(0,r.useEffect)(()=>{Q(),X(),J()},[]);let Z=[{name:s("vspace.UserName"),selector:"username",sortable:!0},{name:s("vspace.Email"),selector:"email",sortable:!0}],[ee,es]=(0,r.useState)(!0),[ea,et]=(0,r.useState)(!0),[en,er]=(0,r.useState)(!0),[ec,ei]=(0,r.useState)(!0),el=()=>(0,n.jsx)(j(),{href:"/vspace/[...routes]",as:`/vspace/edit/${t._id}`,children:(0,n.jsxs)(c.A,{variant:"secondary",size:"sm",children:[(0,n.jsx)(h.FontAwesomeIcon,{icon:v.faPen}),"\xa0",s("vspace.Edit")]})}),eo=()=>(0,n.jsx)(j(),{href:{pathname:"/vspace/[...routes]",query:{id:e.routes[1]}},as:`/vspace/manage?id=${e.routes[1]}`,children:(0,n.jsx)("span",{children:(0,n.jsx)(h.FontAwesomeIcon,{icon:v.faUserCog,size:"2x",color:"#232c3d",className:"clickable"})})}),ed=()=>(0,n.jsxs)(i.A.Item,{eventKey:"3",children:[(0,n.jsxs)(i.A.Header,{onClick:()=>ei(!ec),children:[(0,n.jsx)("div",{className:"cardTitle",children:s("vspace.Discussions")}),(0,n.jsx)("div",{className:"cardArrow",children:ec?(0,n.jsx)(h.FontAwesomeIcon,{icon:v.faPlus,color:"#fff"}):(0,n.jsx)(h.FontAwesomeIcon,{icon:v.faMinus,color:"#fff"})})]}),(0,n.jsx)(i.A.Body,{children:(0,n.jsx)(b.A,{type:"vspace",id:e&&e.routes?e.routes[1]:null})})]});(0,w.canViewDiscussionUpdate)(()=>(0,n.jsx)(ed,{}));let eu=(0,w.canEditVspace)(()=>(0,n.jsx)(el,{})),em=(0,w.canEditVspace)(()=>(0,n.jsx)(eo,{})),ep={Monitoringmembers:{columns:E,subscribers:F},SubscribeRequestUsers:{requestedColumns:Z,vspaceRequest:k},vSpaceLoading:z,vSpaceSort:e=>{G.sort={[e.columnSelector]:e.sortDirection},J()},documentAccoirdianProps:{vSpaceDocs:L,updateLoading:Y,updateSort:e=>{K.sort={[e.columnSelector]:e.sortDirection},X()},docSrc:$},calenderEvents:P,document:T};return(0,n.jsxs)(l.A,{fluid:!0,className:"vspaceDetails",children:[(0,n.jsx)(y.A,{routes:e.routes}),(0,n.jsxs)(o.A,{style:{marginTop:"10px",marginBottom:"25px"},children:[(0,n.jsx)(d.A,{className:"ps-md-1 pe-md-1",md:8,children:function(e,s,a,t,r){return(0,n.jsxs)("div",{className:"vspaceCard",children:[(0,n.jsxs)("div",{className:"vspaceTitle",children:[(0,n.jsxs)("h4",{children:[e.title,"\xa0\xa0",s.routes&&s.routes[1]?(0,n.jsx)(a,{vspace:e}):null]}),(0,n.jsx)("section",{className:"d-flex justify-content-between",children:(0,n.jsx)(N.A,{entityId:s.routes[1],entityType:"vspace"})})]}),(0,n.jsx)("div",{className:"vspaceDesc",children:(0,n.jsx)(g.A,{description:e.description})}),(0,n.jsxs)("div",{className:"vspaceInfo",children:[(0,n.jsxs)("p",{children:[(0,n.jsx)("b",{children:t("vspace.Ownedby")}),": ",e.user&&e.user.username?`${e.user.username}`:""," "]}),(0,n.jsxs)("p",{children:[(0,n.jsx)("b",{children:t("vspace.Groupvisibility")}),": ",q[e.visibility]]}),(0,n.jsxs)("p",{children:[(0,n.jsx)("b",{children:t("vspace.Created")}),": ",p()(e.created_at).locale(r).format("ddd, D MMMM YYYY")]}),(0,n.jsxs)("p",{children:[(0,n.jsx)("b",{children:t("vspace.LastModified")}),": ",p()(e.updated_at).locale(r).format("ddd, D MMMM YYYY")]})]})]})}(t,e,eu,s,H)}),(0,n.jsx)(d.A,{className:"pe-md-1",md:4,children:(0,n.jsxs)("div",{className:"vspaceCard vspaceCalendar",children:[(0,n.jsx)("div",{className:"vspaceTitle",children:(0,n.jsx)("h4",{children:s("calendar")})}),(0,n.jsx)(_.default,{type:"vspace",id:e&&e.routes?e.routes[1]:null})]})})]}),(0,n.jsx)(o.A,{children:(0,n.jsxs)(d.A,{className:"ps-1 pe-1",children:[(0,n.jsxs)("div",{className:"d-flex justify-content-between",children:[(0,n.jsx)("h4",{children:s("vspace.Monitoringandevaluationmembers")}),e.routes&&e.routes[1]?(0,n.jsx)(em,{vspace:t}):null]}),(0,n.jsx)(S.default,{...ep})]})}),(0,n.jsx)(o.A,{children:(0,n.jsxs)(d.A,{className:"ps-1 pe-1",children:[(0,n.jsx)("h4",{children:s("vspace.SubscribeRequestUsers")}),k&&k.length>0?(0,n.jsx)(C.default,{...ep}):(0,n.jsx)("div",{className:"nodataFound",children:s("vspace.Nodataavailable")})]})}),(0,n.jsx)(o.A,{children:(0,n.jsx)(d.A,{className:"vspaceAccordion ps-1 pe-1",xs:12,children:(0,n.jsx)(M.default,{...ep})})})]})};t()}catch(e){t(e)}})},90639:(e,s,a)=>{a.a(e,async(e,t)=>{try{a.r(s),a.d(s,{default:()=>o});var n=a(8732),r=a(82015);a(27825);var c=a(13054),i=a(63487),l=e([c,i]);[c,i]=l.then?(await l)():l;let o=function(e){let[s,a]=(0,r.useState)([]);return(0,n.jsx)(c.A,{eventsList:s,minicalendar:!0,showEventCounts:!0})};t()}catch(e){t(e)}})},99435:(e,s,a)=>{a.r(s),a.d(s,{default:()=>i});var t=a(8732),n=a(56084),r=a(27825),c=a.n(r);let i=e=>(0,t.jsx)(t.Fragment,{children:(0,t.jsx)(n.A,{noHeader:!0,columns:e.SubscribeRequestUsers.requestedColumns,data:e.SubscribeRequestUsers.vspaceRequest,dense:!0,paginationServer:!0,pagServer:!0,paginationTotalRows:0,subHeader:!0,subHeaderAlign:"left",pagination:!0,persistTableHead:!0,sortFunction:(e,s,a)=>c().orderBy(e,e=>e[s]?e[s].toLowerCase():e[s],a)})})}};