(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[3166],{8019:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>s});var a=r(37876),i=r(14232),n=r(53718),o=r(87453),l=r(31753);let s=()=>{let{i18n:e}=(0,l.Bd)("common");e.language&&e.language;let[t,r]=(0,i.useState)({title:"",description:"",pageCategory:"",images:"",isEnabled:!0}),[s,c]=(0,i.useState)(!1),d={query:{pageCategory:""},sort:{title:"asc"},limit:"~"},u=async()=>{let e=await g();if(c(!0),e.length>0){d.query.pageCategory=e;let t=await n.A.get("/landingPage",d);if(Array.isArray(t.data)&&t.data.length>0){let e=t.data[t.data.length-1];e.images=t&&e.images.length>0&&!0===e.isEnabled?e.images.map((e,t)=>String("".concat("http://localhost:3001/api/v1","/image/show/").concat(e._id))):"/images/logo.jpg",e.description=t&&e.description.length>0&&!0===e.isEnabled?e.description:"The Robert Koch Institut is taking over the coordination of the “WHO AMR Surveillance and Quality Assessment Collaborating Centres Network” this autumn 2019. The network supports the World Health Organization (WHO) to reduce drug-resistant infections globally. It focuses on further developing the global antimicrobial resistance (AMR) surveillance system (GLASS), and promoting exchange and peer support between countries.",r(e),g(),c(!1)}}},g=async()=>{let e=await n.A.get("/pagecategory",{query:{title:"AboutUs"}});return!!e&&!!e.data&&e.data.length>0&&e.data[0]._id};(0,i.useEffect)(()=>{u()},[]);let h=t.description.replace(/\&nbsp;/g," ");return(0,a.jsx)("div",{className:"aboutUs",children:!0===s?(0,a.jsx)(o.A,{}):(0,a.jsxs)("div",{children:[(0,a.jsx)("img",{className:"logoImg",src:t.images,alt:""}),(0,a.jsx)("div",{dangerouslySetInnerHTML:{__html:h}})," "]})})}},9644:(e,t,r)=>{(window.__NEXT_P=window.__NEXT_P||[]).push(["/dashboard/AboutUs",function(){return r(8019)}])},67140:(e,t,r)=>{"use strict";r.d(t,{Ay:()=>s});var a=r(14232),i=function(){return(i=Object.assign||function(e){for(var t,r=1,a=arguments.length;r<a;r++)for(var i in t=arguments[r])Object.prototype.hasOwnProperty.call(t,i)&&(e[i]=t[i]);return e}).apply(this,arguments)},n=function(e){var t=e.animate,r=void 0===t||t,n=e.animateBegin,o=e.backgroundColor,l=void 0===o?"#f5f6f7":o,s=e.backgroundOpacity,c=void 0===s?1:s,d=e.baseUrl,u=void 0===d?"":d,g=e.children,h=e.foregroundColor,p=e.foregroundOpacity,f=e.gradientRatio,m=void 0===f?2:f,y=e.gradientDirection,b=e.uniqueKey,v=e.interval,E=e.rtl,x=e.speed,w=e.style,O=e.title,C=void 0===O?"Loading...":O,k=e.beforeMask,_=void 0===k?null:k,j=function(e,t){var r={};for(var a in e)Object.prototype.hasOwnProperty.call(e,a)&&0>t.indexOf(a)&&(r[a]=e[a]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var i=0,a=Object.getOwnPropertySymbols(e);i<a.length;i++)0>t.indexOf(a[i])&&Object.prototype.propertyIsEnumerable.call(e,a[i])&&(r[a[i]]=e[a[i]]);return r}(e,["animate","animateBegin","backgroundColor","backgroundOpacity","baseUrl","children","foregroundColor","foregroundOpacity","gradientRatio","gradientDirection","uniqueKey","interval","rtl","speed","style","title","beforeMask"]),A=b||Math.random().toString(36).substring(6),N=A+"-diff",S=A+"-animated-diff",P=A+"-aria",T="0; "+(void 0===v?.25:v)+"; 1",q=(void 0===x?1.2:x)+"s";return(0,a.createElement)("svg",i({"aria-labelledby":P,role:"img",style:i(i({},void 0===w?{}:w),void 0!==E&&E?{transform:"scaleX(-1)"}:null)},j),C?(0,a.createElement)("title",{id:P},C):null,_&&(0,a.isValidElement)(_)?_:null,(0,a.createElement)("rect",{role:"presentation",x:"0",y:"0",width:"100%",height:"100%",clipPath:"url("+u+"#"+N+")",style:{fill:"url("+u+"#"+S+")"}}),(0,a.createElement)("defs",null,(0,a.createElement)("clipPath",{id:N},g),(0,a.createElement)("linearGradient",{id:S,gradientTransform:"top-bottom"===(void 0===y?"left-right":y)?"rotate(90)":void 0},(0,a.createElement)("stop",{offset:"0%",stopColor:l,stopOpacity:c},r&&(0,a.createElement)("animate",{attributeName:"offset",values:-m+"; "+-m+"; 1",keyTimes:T,dur:q,repeatCount:"indefinite",begin:n})),(0,a.createElement)("stop",{offset:"50%",stopColor:void 0===h?"#eee":h,stopOpacity:void 0===p?1:p},r&&(0,a.createElement)("animate",{attributeName:"offset",values:-m/2+"; "+-m/2+"; "+(1+m/2),keyTimes:T,dur:q,repeatCount:"indefinite",begin:n})),(0,a.createElement)("stop",{offset:"100%",stopColor:l,stopOpacity:c},r&&(0,a.createElement)("animate",{attributeName:"offset",values:"0; 0; "+(1+m),keyTimes:T,dur:q,repeatCount:"indefinite",begin:n})))))},o=function(e){return e.children?(0,a.createElement)(n,i({},e)):(0,a.createElement)(l,i({},e))},l=function(e){return(0,a.createElement)(o,i({viewBox:"0 0 476 124"},e),(0,a.createElement)("rect",{x:"48",y:"8",width:"88",height:"6",rx:"3"}),(0,a.createElement)("rect",{x:"48",y:"26",width:"52",height:"6",rx:"3"}),(0,a.createElement)("rect",{x:"0",y:"56",width:"410",height:"6",rx:"3"}),(0,a.createElement)("rect",{x:"0",y:"72",width:"380",height:"6",rx:"3"}),(0,a.createElement)("rect",{x:"0",y:"88",width:"178",height:"6",rx:"3"}),(0,a.createElement)("circle",{cx:"20",cy:"20",r:"20"}))};let s=o},87453:(e,t,r)=>{"use strict";r.d(t,{A:()=>n});var a=r(37876),i=r(67140);function n(){return(0,a.jsxs)(i.Ay,{viewBox:"0 0 380 70",height:50,width:317,speed:2,title:"Loading",foregroundColor:"#f7f7f7",backgroundColor:"#ecebeb",uniqueKey:"operation",children:[(0,a.jsx)("rect",{x:"10",y:"0",rx:"4",ry:"4",width:"320",height:"25"}),(0,a.jsx)("rect",{x:"40",y:"40",rx:"3",ry:"3",width:"250",height:"20"})]})}}},e=>{var t=t=>e(e.s=t);e.O(0,[636,6593,8792],()=>t(9644)),_N_E=e.O()}]);
//# sourceMappingURL=AboutUs-f66e482149529a2d.js.map