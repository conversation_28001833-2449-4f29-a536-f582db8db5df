(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[3790],{22797:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>d});var a=r(37876);r(14232);var s=r(49589),n=r(56970),l=r(37784),o=r(29504),i=r(67814),c=r(31753);let d=e=>{let{filterText:t,onFilter:r,onClear:d,handleGroupHandler:u,groupType:p,options:m}=e,{t:h}=(0,c.Bd)("common");return(0,a.jsx)(s.A,{fluid:!0,className:"p-0",children:(0,a.jsx)(n.A,{children:(0,a.jsx)(l.A,{xs:4,md:4,lg:4,children:(0,a.jsx)(o.A.Group,{style:{maxWidth:"800px"},children:(0,a.jsx)(i.KF,{overrideStrings:{selectSomeItems:h("ChooseGroup"),allItemsAreSelected:"All Groups are Selected"},options:m,value:p,onChange:u,className:"choose-group",labelledBy:"Select Network"})})})})})}},50749:(e,t,r)=>{"use strict";r.d(t,{A:()=>i});var a=r(37876);r(14232);var s=r(89773),n=r(31753),l=r(5507);function o(e){let{t}=(0,n.Bd)("common"),r={rowsPerPageText:t("Rowsperpage")},{columns:o,data:i,totalRows:c,resetPaginationToggle:d,subheader:u,subHeaderComponent:p,handlePerRowsChange:m,handlePageChange:h,rowsPerPage:g,defaultRowsPerPage:v,selectableRows:x,loading:b,pagServer:f,onSelectedRowsChange:y,clearSelectedRows:w,sortServer:k,onSort:j,persistTableHead:C,sortFunction:S,...A}=e,N={paginationComponentOptions:r,noDataComponent:t("NoData"),noHeader:!0,columns:o,data:i||[],dense:!0,paginationResetDefaultPage:d,subHeader:u,progressPending:b,subHeaderComponent:p,pagination:!0,paginationServer:f,paginationPerPage:v||10,paginationRowsPerPageOptions:g||[10,15,20,25,30],paginationTotalRows:c,onChangeRowsPerPage:m,onChangePage:h,selectableRows:x,onSelectedRowsChange:y,clearSelectedRows:w,progressComponent:(0,a.jsx)(l.A,{}),sortIcon:(0,a.jsx)("i",{className:"sort-icon fas fa-exchange-alt"}),sortServer:k,onSort:j,sortFunction:S,persistTableHead:C,className:"rki-table"};return(0,a.jsx)(s.Ay,{...N})}o.defaultProps={subHeader:!1,pagination:!0,totalRows:null,pagServer:!0,onSelectedRowsChange:null,clearSelectedRows:!1,sortServer:!1,persistTableHead:!1};let i=o},52027:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>v});var a=r(37876),s=r(14232),n=r(31777),l=r(82851),o=r.n(l),i=r(48230),c=r.n(i),d=r(31195),u=r(60282),p=r(22797),m=r(50749),h=r(53718),g=r(31753);let v=(0,n.Ng)(e=>e)(e=>{let t=[{value:"institution",label:"Organisations"},{value:"operation",label:"Operations"},{value:"project",label:"Projects"},{value:"event",label:"Events"},{value:"vspace",label:"Virtual Spaces"}],[r,n]=(0,s.useState)([]),[,l]=(0,s.useState)(!1),[i,v]=(0,s.useState)(!1),[x,b]=(0,s.useState)(0),[f,y]=(0,s.useState)(10),[w,k]=(0,s.useState)(""),[j,C]=(0,s.useState)(!1),[S,A]=(0,s.useState)(t),[N,R]=(0,s.useState)({}),{t:E}=(0,g.Bd)("common"),_=async e=>{e&&e._id&&R(e._id),v(!0)},P={sort:{created_at:"asc"},populate:{path:"entity_id",select:"title"},lean:!0,limit:f,page:1,query:{user:e.user&&e.user._id?e.user._id:""}},O=[{name:E("Title"),selector:"",cell:e=>e.entity_id&&e.entity_id.title?(0,a.jsx)(c(),{href:"/".concat(e.entity_type,"/[...routes]"),as:"/".concat(e.entity_type,"/show/").concat(e.entity_id._id),children:e.entity_id.title}):""},{name:E("Group"),selector:"group",cell:e=>e.onModel&&"Institution"===e.onModel?"Organisation":e.onModel},{name:E("Remove"),selector:"",cell:e=>(0,a.jsx)("div",{onClick:()=>_(e),style:{cursor:"pointer"},children:(0,a.jsx)("i",{className:"icon fas fa-trash-alt"})})}],T=async()=>{l(!0);let e=await h.A.get("/flag",P);e&&e.data&&(n(e.data),b(e.totalCount),l(!1))},I=async(e,t)=>{P.limit=e,P.page=t,l(!0);let r=o().map(S,"value");r&&r.length>0&&(P.query={...P.query,entity_type:r});let a=await h.A.get("/flag",P);a&&a.data&&a.data.length>0&&(n(a.data),y(e),l(!1))};(0,s.useEffect)(()=>{P.page=1,T()},[]);let M=s.useMemo(()=>{let e=e=>{e&&(P.populate.match={title:{$regex:e}},T())},r=o().debounce(t=>e(t),Number("500")||300);return(0,a.jsx)(p.default,{onFilter:e=>{k(e.target.value),r(e.target.value)},onClear:()=>{w&&(C(!j),k(""))},filterText:w,handleGroupHandler:e=>{A(e);let t=o().map(e,"value");0===t.length?n([]):(P.query={...P.query,entity_type:t},T())},groupType:S,options:t})},[w,S,j]),D=()=>v(!1),W=async()=>{if(v(!1),await h.A.remove("/flag/".concat(N)),S&&Array.isArray(S)){let e=o().map(S,"value");e&&e.length>0&&(P.query={...P.query,entity_type:e})}T()};return(0,a.jsxs)("div",{className:"my-bookmark-table",children:[(0,a.jsxs)(d.A,{show:i,onHide:D,children:[(0,a.jsx)(d.A.Header,{closeButton:!0,children:(0,a.jsx)(d.A.Title,{children:E("Removebookmark")})}),(0,a.jsx)(d.A.Body,{children:E("Areyousurewanttoremovefromyourbookmark")}),(0,a.jsxs)(d.A.Footer,{children:[(0,a.jsx)(u.A,{variant:"secondary",onClick:D,children:E("Cancel")}),(0,a.jsx)(u.A,{variant:"primary",onClick:W,children:E("bookmarkDeleteYes")})]})]}),(0,a.jsx)(m.A,{columns:O,data:r,totalRows:x,subheader:!0,pagServer:!0,resetPaginationToggle:j,subHeaderComponent:M,handlePerRowsChange:I,handlePageChange:e=>{P.limit=f,P.page=e,""!==w&&(P.query={title:w});let t=o().map(S,"value");t&&t.length>0&&(P.query={...P.query,entity_type:t}),T()}})]})})},58380:(e,t,r)=>{(window.__NEXT_P=window.__NEXT_P||[]).push(["/profile/bookmarkTable",function(){return r(52027)}])},67814:(e,t,r)=>{"use strict";r.d(t,{KF:()=>j});var a=r(14232),s=r(37876);!function(e,{insertAt:t}={}){if(!e||typeof document>"u")return;let r=document.head||document.getElementsByTagName("head")[0],a=document.createElement("style");a.type="text/css","top"===t&&r.firstChild?r.insertBefore(a,r.firstChild):r.appendChild(a),a.styleSheet?a.styleSheet.cssText=e:a.appendChild(document.createTextNode(e))}(`.rmsc{--rmsc-main: #4285f4;--rmsc-hover: #f1f3f5;--rmsc-selected: #e2e6ea;--rmsc-border: #ccc;--rmsc-gray: #aaa;--rmsc-bg: #fff;--rmsc-p: 10px;--rmsc-radius: 4px;--rmsc-h: 38px}.rmsc *{box-sizing:border-box;transition:all .2s ease}.rmsc .gray{color:var(--rmsc-gray)}.rmsc .dropdown-content{position:absolute;z-index:1;top:100%;width:100%;padding-top:8px}.rmsc .dropdown-content .panel-content{overflow:hidden;border-radius:var(--rmsc-radius);background:var(--rmsc-bg);box-shadow:0 0 0 1px #0000001a,0 4px 11px #0000001a}.rmsc .dropdown-container{position:relative;outline:0;background-color:var(--rmsc-bg);border:1px solid var(--rmsc-border);border-radius:var(--rmsc-radius)}.rmsc .dropdown-container[aria-disabled=true]:focus-within{box-shadow:var(--rmsc-gray) 0 0 0 1px;border-color:var(--rmsc-gray)}.rmsc .dropdown-container:focus-within{box-shadow:var(--rmsc-main) 0 0 0 1px;border-color:var(--rmsc-main)}.rmsc .dropdown-heading{position:relative;padding:0 var(--rmsc-p);display:flex;align-items:center;width:100%;height:var(--rmsc-h);cursor:default;outline:0}.rmsc .dropdown-heading .dropdown-heading-value{overflow:hidden;text-overflow:ellipsis;white-space:nowrap;flex:1}.rmsc .clear-selected-button{cursor:pointer;background:none;border:0;padding:0;display:flex}.rmsc .options{max-height:260px;overflow-y:auto;margin:0;padding-left:0}.rmsc .options li{list-style:none;margin:0}.rmsc .select-item{box-sizing:border-box;cursor:pointer;display:block;padding:var(--rmsc-p);outline-offset:-1px;outline-color:var(--rmsc-primary)}.rmsc .select-item:hover{background:var(--rmsc-hover)}.rmsc .select-item.selected{background:var(--rmsc-selected)}.rmsc .no-options{padding:var(--rmsc-p);text-align:center;color:var(--rmsc-gray)}.rmsc .search{width:100%;position:relative;border-bottom:1px solid var(--rmsc-border)}.rmsc .search input{background:none;height:var(--rmsc-h);padding:0 var(--rmsc-p);width:100%;outline:0;border:0;font-size:1em}.rmsc .search input:focus{background:var(--rmsc-hover)}.rmsc .search-clear-button{cursor:pointer;position:absolute;top:0;right:0;bottom:0;background:none;border:0;padding:0 calc(var(--rmsc-p) / 2)}.rmsc .search-clear-button [hidden]{display:none}.rmsc .item-renderer{display:flex;align-items:baseline}.rmsc .item-renderer input{margin:0 5px 0 0}.rmsc .item-renderer.disabled{opacity:.5}.rmsc .spinner{animation:rotate 2s linear infinite}.rmsc .spinner .path{stroke:var(--rmsc-border);stroke-width:4px;stroke-linecap:round;animation:dash 1.5s ease-in-out infinite}@keyframes rotate{to{transform:rotate(360deg)}}@keyframes dash{0%{stroke-dasharray:1,150;stroke-dashoffset:0}50%{stroke-dasharray:90,150;stroke-dashoffset:-35}to{stroke-dasharray:90,150;stroke-dashoffset:-124}}
`);var n={allItemsAreSelected:"All items are selected.",clearSearch:"Clear Search",clearSelected:"Clear Selected",noOptions:"No options",search:"Search",selectAll:"Select All",selectAllFiltered:"Select All (Filtered)",selectSomeItems:"Select...",create:"Create"},l={value:[],hasSelectAll:!0,className:"multi-select",debounceDuration:200,options:[]},o=a.createContext({}),i=({props:e,children:t})=>{let[r,i]=(0,a.useState)(e.options);return(0,a.useEffect)(()=>{i(e.options)},[e.options]),(0,s.jsx)(o.Provider,{value:{t:t=>{var r;return(null==(r=e.overrideStrings)?void 0:r[t])||n[t]},...l,...e,options:r,setOptions:i},children:t})},c=()=>a.useContext(o),d={when:!0,eventTypes:["keydown"]};function u(e,t,r){let s=(0,a.useMemo)(()=>Array.isArray(e)?e:[e],[e]),n=Object.assign({},d,r),{when:l,eventTypes:o}=n,i=(0,a.useRef)(t),{target:c}=n;(0,a.useEffect)(()=>{i.current=t});let u=(0,a.useCallback)(e=>{s.some(t=>e.key===t||e.code===t)&&i.current(e)},[s]);(0,a.useEffect)(()=>{if(l&&"u">typeof window){let e=c?c.current:window;return o.forEach(t=>{e&&e.addEventListener(t,u)}),()=>{o.forEach(t=>{e&&e.removeEventListener(t,u)})}}},[l,o,s,c,t])}var p={ARROW_DOWN:"ArrowDown",ARROW_UP:"ArrowUp",ENTER:"Enter",ESCAPE:"Escape",SPACE:"Space"},m=(e,t)=>{let r;return function(...a){clearTimeout(r),r=setTimeout(()=>{e.apply(null,a)},t)}},h=()=>(0,s.jsxs)("svg",{width:"24",height:"24",fill:"none",stroke:"currentColor",strokeWidth:"2",className:"dropdown-search-clear-icon gray",children:[(0,s.jsx)("line",{x1:"18",y1:"6",x2:"6",y2:"18"}),(0,s.jsx)("line",{x1:"6",y1:"6",x2:"18",y2:"18"})]}),g=({checked:e,option:t,onClick:r,disabled:a})=>(0,s.jsxs)("div",{className:`item-renderer ${a?"disabled":""}`,children:[(0,s.jsx)("input",{type:"checkbox",onChange:r,checked:e,tabIndex:-1,disabled:a}),(0,s.jsx)("span",{children:t.label})]}),v=({itemRenderer:e=g,option:t,checked:r,tabIndex:n,disabled:l,onSelectionChanged:o,onClick:i})=>{let c=(0,a.useRef)(),d=()=>{l||o(!r)};return u([p.ENTER,p.SPACE],e=>{d(),e.preventDefault()},{target:c}),(0,s.jsx)("label",{className:`select-item ${r?"selected":""}`,role:"option","aria-selected":r,tabIndex:n,ref:c,children:(0,s.jsx)(e,{option:t,checked:r,onClick:e=>{d(),i(e)},disabled:l})})},x=({options:e,onClick:t,skipIndex:r})=>{let{disabled:a,value:n,onChange:l,ItemRenderer:o}=c(),i=(e,t)=>{a||l(t?[...n,e]:n.filter(t=>t.value!==e.value))};return(0,s.jsx)(s.Fragment,{children:e.map((e,l)=>{let c=l+r;return(0,s.jsx)("li",{children:(0,s.jsx)(v,{tabIndex:c,option:e,onSelectionChanged:t=>i(e,t),checked:!!n.find(t=>t.value===e.value),onClick:e=>t(e,c),itemRenderer:o,disabled:e.disabled||a})},(null==e?void 0:e.key)||l)})})},b=()=>{let{t:e,onChange:t,options:r,setOptions:n,value:l,filterOptions:o,ItemRenderer:i,disabled:d,disableSearch:g,hasSelectAll:b,ClearIcon:f,debounceDuration:y,isCreatable:w,onCreateOption:k}=c(),j=(0,a.useRef)(),C=(0,a.useRef)(),[S,A]=(0,a.useState)(""),[N,R]=(0,a.useState)(r),[E,_]=(0,a.useState)(""),[P,O]=(0,a.useState)(0),T=(0,a.useCallback)(m(e=>_(e),y),[]),I=(0,a.useMemo)(()=>{let e=0;return g||(e+=1),b&&(e+=1),e},[g,b]),M={label:e(S?"selectAllFiltered":"selectAll"),value:""},D=e=>{let t=N.filter(e=>!e.disabled).map(e=>e.value);if(e){let e=[...l.map(e=>e.value),...t];return(o?N:r).filter(t=>e.includes(t.value))}return l.filter(e=>!t.includes(e.value))},W=()=>{var e;_(""),A(""),null==(e=null==C?void 0:C.current)||e.focus()},q=e=>O(e);u([p.ARROW_DOWN,p.ARROW_UP],e=>{switch(e.code){case p.ARROW_UP:H(-1);break;case p.ARROW_DOWN:H(1);break;default:return}e.stopPropagation(),e.preventDefault()},{target:j});let B=async()=>{let e={label:S,value:S,__isNew__:!0};k&&(e=await k(S)),n([e,...r]),W(),t([...l,e])},F=async()=>o?await o(r,E):function(e,t){return t?e.filter(({label:e,value:r})=>null!=e&&null!=r&&e.toLowerCase().includes(t.toLowerCase())):e}(r,E),H=e=>{let t=P+e;O(t=Math.min(t=Math.max(0,t),r.length+Math.max(I-1,0)))};(0,a.useEffect)(()=>{var e,t;null==(t=null==(e=null==j?void 0:j.current)?void 0:e.querySelector(`[tabIndex='${P}']`))||t.focus()},[P]);let[L,$]=(0,a.useMemo)(()=>{let e=N.filter(e=>!e.disabled);return[e.every(e=>-1!==l.findIndex(t=>t.value===e.value)),0!==e.length]},[N,l]);(0,a.useEffect)(()=>{F().then(R)},[E,r]);let z=(0,a.useRef)();u([p.ENTER],B,{target:z});let G=w&&S&&!N.some(e=>(null==e?void 0:e.value)===S);return(0,s.jsxs)("div",{className:"select-panel",role:"listbox",ref:j,children:[!g&&(0,s.jsxs)("div",{className:"search",children:[(0,s.jsx)("input",{placeholder:e("search"),type:"text","aria-describedby":e("search"),onChange:e=>{T(e.target.value),A(e.target.value),O(0)},onFocus:()=>{O(0)},value:S,ref:C,tabIndex:0}),(0,s.jsx)("button",{type:"button",className:"search-clear-button",hidden:!S,onClick:W,"aria-label":e("clearSearch"),children:f||(0,s.jsx)(h,{})})]}),(0,s.jsxs)("ul",{className:"options",children:[b&&$&&(0,s.jsx)(v,{tabIndex:+(1!==I),checked:L,option:M,onSelectionChanged:e=>{t(D(e))},onClick:()=>q(1),itemRenderer:i,disabled:d}),N.length?(0,s.jsx)(x,{skipIndex:I,options:N,onClick:(e,t)=>q(t)}):G?(0,s.jsx)("li",{onClick:B,className:"select-item creatable",tabIndex:1,ref:z,children:`${e("create")} "${S}"`}):(0,s.jsx)("li",{className:"no-options",children:e("noOptions")})]})]})},f=({expanded:e})=>(0,s.jsx)("svg",{width:"24",height:"24",fill:"none",stroke:"currentColor",strokeWidth:"2",className:"dropdown-heading-dropdown-arrow gray",children:(0,s.jsx)("path",{d:e?"M18 15 12 9 6 15":"M6 9L12 15 18 9"})}),y=()=>{let{t:e,value:t,options:r,valueRenderer:a}=c(),n=0===t.length,l=t.length===r.length,o=a&&a(t,r);return n?(0,s.jsx)("span",{className:"gray",children:o||e("selectSomeItems")}):(0,s.jsx)("span",{children:o||(l?e("allItemsAreSelected"):t.map(e=>e.label).join(", "))})},w=({size:e=24})=>(0,s.jsx)("span",{style:{width:e,marginRight:"0.2rem"},children:(0,s.jsx)("svg",{width:e,height:e,className:"spinner",viewBox:"0 0 50 50",style:{display:"inline",verticalAlign:"middle"},children:(0,s.jsx)("circle",{cx:"25",cy:"25",r:"20",fill:"none",className:"path"})})}),k=()=>{let{t:e,onMenuToggle:t,ArrowRenderer:r,shouldToggleOnHover:n,isLoading:l,disabled:o,onChange:i,labelledBy:d,value:m,isOpen:g,defaultIsOpen:v,ClearSelectedIcon:x,closeOnChangedValue:k}=c();(0,a.useEffect)(()=>{k&&A(!1)},[m]);let[j,C]=(0,a.useState)(!0),[S,A]=(0,a.useState)(v),[N,R]=(0,a.useState)(!1),E=(0,a.useRef)();(function(e,t){let r=(0,a.useRef)(!1);(0,a.useEffect)(()=>{r.current?e():r.current=!0},t)})(()=>{t&&t(S)},[S]),(0,a.useEffect)(()=>{void 0===v&&"boolean"==typeof g&&(C(!1),A(g))},[g]),u([p.ENTER,p.ARROW_DOWN,p.SPACE,p.ESCAPE],e=>{var t;["text","button"].includes(e.target.type)&&[p.SPACE,p.ENTER].includes(e.code)||(j&&(e.code===p.ESCAPE?(A(!1),null==(t=null==E?void 0:E.current)||t.focus()):A(!0)),e.preventDefault())},{target:E});let _=e=>{j&&n&&A(e)};return(0,s.jsxs)("div",{tabIndex:0,className:"dropdown-container","aria-labelledby":d,"aria-expanded":S,"aria-readonly":!0,"aria-disabled":o,ref:E,onFocus:()=>!N&&R(!0),onBlur:e=>{!e.currentTarget.contains(e.relatedTarget)&&j&&(R(!1),A(!1))},onMouseEnter:()=>_(!0),onMouseLeave:()=>_(!1),children:[(0,s.jsxs)("div",{className:"dropdown-heading",onClick:()=>{j&&A(!l&&!o&&!S)},children:[(0,s.jsx)("div",{className:"dropdown-heading-value",children:(0,s.jsx)(y,{})}),l&&(0,s.jsx)(w,{}),m.length>0&&null!==x&&(0,s.jsx)("button",{type:"button",className:"clear-selected-button",onClick:e=>{e.stopPropagation(),i([]),j&&A(!1)},disabled:o,"aria-label":e("clearSelected"),children:x||(0,s.jsx)(h,{})}),(0,s.jsx)(r||f,{expanded:S})]}),S&&(0,s.jsx)("div",{className:"dropdown-content",children:(0,s.jsx)("div",{className:"panel-content",children:(0,s.jsx)(b,{})})})]})},j=e=>(0,s.jsx)(i,{props:e,children:(0,s.jsx)("div",{className:`rmsc ${e.className||"multi-select"}`,children:(0,s.jsx)(k,{})})})}},e=>{var t=t=>e(e.s=t);e.O(0,[9773,636,6593,8792],()=>t(58380)),_N_E=e.O()}]);
//# sourceMappingURL=bookmarkTable-a7b77b0a2d9d2681.js.map