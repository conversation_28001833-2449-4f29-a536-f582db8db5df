(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[4772],{9880:(e,a,o)=>{(window.__NEXT_P=window.__NEXT_P||[]).push(["/vspace/DocumentAccordian",function(){return o(48587)}])},48587:(e,a,o)=>{"use strict";o.r(a),o.d(a,{default:()=>l});var n=o(37876),t=o(11041),s=o(21772),r=o(32890),c=o(14232),i=o(66404),d=o(31753);let l=e=>{let{t:a}=(0,d.Bd)("common"),[o,l]=(0,c.useState)(!1);return(0,n.jsx)(n.Fragment,{children:(0,n.jsxs)(r.A.Item,{eventKey:"0",children:[(0,n.jsxs)(r<PERSON><PERSON><PERSON>,{onClick:()=>l(!o),children:[(0,n.jsx)("div",{className:"cardTitle",children:a("vspace.Documents")}),(0,n.jsx)("div",{className:"cardArrow",children:o?(0,n.jsx)(s.g,{icon:t.EZy,color:"#fff"}):(0,n.jsx)(s.g,{icon:t.QLR,color:"#fff"})})]}),(0,n.jsxs)(r.A.Body,{children:[(0,n.jsx)(i.A,{loading:e.vSpaceLoading,sortProps:e.vSpaceSort,docs:e.documentAccoirdianProps.vSpaceDocs||[],docsDescription:e.calenderEvents.length>0&&e.calenderEvents.map(e=>e.doc_src).flat(1)}),(0,n.jsx)("h6",{className:"mt-3",children:a("vspace.DocumentsfromUpdates")}),(0,n.jsx)(i.A,{loading:e.documentAccoirdianProps.updateLoading,sortProps:e.documentAccoirdianProps.updateSort,docs:e.document||[],docsDescription:e.documentAccoirdianProps.docSrc})]})]})})}},50749:(e,a,o)=>{"use strict";o.d(a,{A:()=>i});var n=o(37876);o(14232);var t=o(89773),s=o(31753),r=o(5507);function c(e){let{t:a}=(0,s.Bd)("common"),o={rowsPerPageText:a("Rowsperpage")},{columns:c,data:i,totalRows:d,resetPaginationToggle:l,subheader:p,subHeaderComponent:m,handlePerRowsChange:u,handlePageChange:g,rowsPerPage:h,defaultRowsPerPage:v,selectableRows:_,loading:x,pagServer:f,onSelectedRowsChange:P,clearSelectedRows:w,sortServer:j,onSort:A,persistTableHead:S,sortFunction:D,...N}=e,b={paginationComponentOptions:o,noDataComponent:a("NoData"),noHeader:!0,columns:c,data:i||[],dense:!0,paginationResetDefaultPage:l,subHeader:p,progressPending:x,subHeaderComponent:m,pagination:!0,paginationServer:f,paginationPerPage:v||10,paginationRowsPerPageOptions:h||[10,15,20,25,30],paginationTotalRows:d,onChangeRowsPerPage:u,onChangePage:g,selectableRows:_,onSelectedRowsChange:P,clearSelectedRows:w,progressComponent:(0,n.jsx)(r.A,{}),sortIcon:(0,n.jsx)("i",{className:"sort-icon fas fa-exchange-alt"}),sortServer:j,onSort:A,sortFunction:D,persistTableHead:S,className:"rki-table"};return(0,n.jsx)(t.Ay,{...b})}c.defaultProps={subHeader:!1,pagination:!0,totalRows:null,pagServer:!0,onSelectedRowsChange:null,clearSelectedRows:!1,sortServer:!1,persistTableHead:!1};let i=c},66404:(e,a,o)=>{"use strict";o.d(a,{A:()=>i});var n=o(37876);o(14232);var t=o(10841),s=o.n(t),r=o(50749),c=o(31753);let i=e=>{let{docs:a,docsDescription:o,sortProps:t,loading:i}=e,d=async(e,a)=>{t({columnSelector:e.selector,sortDirection:a})},{t:l}=(0,c.Bd)("common"),p=[{name:l("FileType"),width:"15%",selector:"extension",cell:e=>e&&e.extension&&e.extension},{name:l("FileName"),width:"25%",selector:"document_title",cell:e=>e&&e.original_name&&(0,n.jsx)("a",{href:"".concat("http://localhost:3001/api/v1","/files/download/").concat(e._id),target:"_blank",children:e.original_name.split(".").slice(0,-1).join(".")}),sortable:!0},{name:l("Description"),selector:"description",cell:e=>e&&e.description&&e.description},{name:l("UploadedDate"),width:"25%",selector:"doc_created_at",cell:e=>e&&e.updated_at&&s()(e.updated_at).format("MM/DD/YYYY"),sortable:!0}];return(0,n.jsx)(r.A,{columns:p,data:a,pagServer:!0,onSort:d,persistTableHead:!0,loading:i})}}},e=>{var a=a=>e(e.s=a);e.O(0,[7725,9773,1772,636,6593,8792],()=>a(9880)),_N_E=e.O()}]);
//# sourceMappingURL=DocumentAccordian-da2e9956020764cb.js.map