//Import Library
import React, { useState, useEffect } from "react";
import { connect } from "react-redux";
import { Form, Card, Alert, Button, Col, Row } from "react-bootstrap";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import { faArrowCircleRight, faExclamationTriangle, faArrowAltCircleLeft } from "@fortawesome/free-solid-svg-icons";
import { TextInput, SelectGroup } from "../../components/common/FormValidation";
import ValidationFormWrapper from "../../components/common/ValidationFormWrapper";
import Router from "next/router";
import toast from 'react-hot-toast';

//Import services/components
import { loadLoggedinUserData } from "../../stores/userActions";
import Confirmation from "../profile/confirmation";
import apiService from "../../services/apiService";
import authService from "../../services/authService";
import { useTranslation } from 'next-i18next';
import { ChangeEvent } from "../../types";

const DeclartionForm = (props: any) => {
    const { t, i18n } = useTranslation('common');
    const titleSearch = i18n.language === "de" ? { title_de: "asc" } : { title: "asc" };
    const currentLang = i18n.language;
    const { authToken, userProfile } = props;
        const consentState: any = {
        dataConsentPolicy: false,
        restrictedUsePolicy: false,
        acceptCookiesPolicy: false,
        medicalConsentPolicy: false,
    };

    const userData = {
        username: "",
        email: "",
        dial_code: "",
        firstname: "",
        lastname: "",
        position: "",
        mobile_number: "",
        password: "",
        confirm_password: "",
    };

    const [warningDialog, setWarningDialog] = useState<boolean>(false);
    const [consent, setConsent] = useState<any>(consentState);
    const [disabledBtn, setDisabledBtn] = useState<boolean>(true);
    const [passwordCard, setPasswordCard] = useState<boolean>(false);
    const [newUser, setNewUser] = useState<any>(userData);
    const [countryList, setCountryList] = useState<any[]>([]);

    useEffect(() => {
        const dialCodeParams = {
            query: {},
            sort: titleSearch,
            limit: "~",
            languageCode: currentLang,
        };

        const countryData = async (dialCodeParamsinit: any) => {
            const response = await apiService.get("/country", dialCodeParamsinit);
            if (response && Array.isArray(response.data)) {
                setCountryList(response.data);
            }
        };
        countryData(dialCodeParams);
    }, []);

    const consentHandler = (e: React.ChangeEvent<HTMLInputElement>) => {
        const { name, checked } = e.target;
        setConsent((prevState: any) => ({ ...prevState, [name]: checked }));
    };

    useEffect(() => {
        i18n.changeLanguage(userProfile && userProfile.languageCode);
        const dialResponse = userProfile && userProfile.dial_code ? userProfile.dial_code : "";
        userProfile &&
            setNewUser((prevState: any) => ({
                ...prevState,
                ...userProfile,
                password: "",
                dial_code: dialResponse,
            }));
    }, [userProfile]);

    useEffect(() => {
        if (
            consent.dataConsentPolicy &&
            consent.restrictedUsePolicy &&
            consent.acceptCookiesPolicy &&
            consent.medicalConsentPolicy
        ) {
            setDisabledBtn(false);
        } else {
            setPasswordCard(false);
            setDisabledBtn(true);
        }
    }, [consent, setConsent, passwordCard]);

    const passwordCardHandler = () => {
        setPasswordCard(!passwordCard);
    };

    const closeHandler = (val: boolean) => {
        setWarningDialog(val);
    };

    const matchPassword = (value: any) => {
        return value === newUser.password;
    };

    const declineHandler = () => {
        setWarningDialog(!warningDialog);
    };

    const userHandler = (e: React.ChangeEvent<HTMLInputElement>) => {
        const { name, value } = e.target;
        setNewUser((prevState: any) => ({
            ...prevState,
            [name]: value,
        }));
    };

    const handleSubmit = async (e: any) => {
        e.preventDefault();
        const obj = {
            code: authToken,
            username: newUser.username.toLowerCase().trim(),
            firstname: newUser.firstname,
            lastname: newUser.lastname,
            email: newUser.email.toLowerCase().trim(),
            dial_code: newUser.dial_code,
            mobile_number: newUser.mobile_number,
            password: newUser.password,
            enabled: true,
            ...consent,
        };

        const response = await apiService.post("/saveInviteUserDetails", obj);
        if (response && response._id) {
            /**Redirect to home**/
            const { auth } = authService;
            let isSuperAdmin = false;
            if (response.roles.includes("SUPER_ADMIN")) {
                isSuperAdmin = true;
            }
            const loginAuth = await auth(
                {
                    username: obj.username,
                    password: obj.password,
                }
            );
            if (loginAuth && loginAuth.status === 201) {
                toast.success(t("toast.AccountcreatedSuccesfully"));
                Router.push("/");
                props.dispatch(loadLoggedinUserData());
            } else {
                const messageStausCode = loginAuth.status + " " + loginAuth.statusText;
                toast.error(messageStausCode)
                Router.push("/home");
            }
            /**End *******/
        }
    };

    return (
        <>
            <div className="text-center mt-2">
                <img src="/images/logo.jpg" alt="Rohert Koch Institut - Logo" />
            </div>
            <div className="d-flex justify-content-center align-items-center">
                {!passwordCard && (
                    <Card className="px-3 declarationcard">
                        <p className="lead px-3 pt-3 mb-0">
                            <b>{t("declaration.header")}</b>
                        </p>
                        <hr className="hr--cardfront" />
                        <Card.Body>
                            <Alert variant="success" className="mt-0">
                                {t("declaration.info")}
                            </Alert>
                            <Form.Check
                                className="pb-4"
                                type="checkbox"
                                onChange={consentHandler}
                                name="dataConsentPolicy"
                                checked={consent.dataConsentPolicy}
                                value="consent1"
                                label={t("declaration.consent1")}
                            />
                            <Form.Check
                                className="pb-4"
                                onChange={consentHandler}
                                type="checkbox"
                                name="medicalConsentPolicy"
                                checked={consent.medicalConsentPolicy}
                                value="consent2"
                                label={t("declaration.consent2")}
                            />
                            <Form.Check
                                className="pb-4"
                                onChange={consentHandler}
                                type="checkbox"
                                name="restrictedUsePolicy"
                                checked={consent.restrictedUsePolicy}
                                value="consent3"
                                label={t("declaration.consent3")}
                            />
                            <Form.Check
                                className="pb-4"
                                type="checkbox"
                                name="acceptCookiesPolicy"
                                onChange={consentHandler}
                                checked={consent.acceptCookiesPolicy}
                                value="consent4"
                                label={t("declaration.consent4")}
                            />
                            <div className="d-flex">
                                {disabledBtn && (
                                    <Button onClick={declineHandler} variant="danger" className="d-grid w-100">
                                        {t("declaration.decline")}
                                    </Button>
                                )}
                                {!disabledBtn && (
                                    <Button variant="success" className="mt-0 d-grid w-100" onClick={passwordCardHandler}>
                                        {t("declaration.accept")}
                                    </Button>
                                )}
                            </div>
                        </Card.Body>
                    </Card>
                )}
            </div>
            {passwordCard && (
                <div className="d-flex justify-content-center align-items-center w-100" id="main-content">
                    <ValidationFormWrapper onSubmit={handleSubmit} initialValues={newUser} enableReinitialize={true}>
                        <Card className="declarationcard">
                            <div className="d-flex align-items-center ms-3">
                                <FontAwesomeIcon
                                    icon={faArrowAltCircleLeft}
                                    onClick={passwordCardHandler}
                                    size="2x"
                                    className="icon--arrow"
                                />
                                <p className="lead px-3 pt-3 pb-0 mb-0">
                                    <b>{t("setInfo.header")}</b>
                                </p>
                            </div>
                            <hr className="hr--cardback" />
                            <Alert className="mx-3 mb-0 mt-3" variant="warning">
                                <FontAwesomeIcon icon={faExclamationTriangle} />
                                &nbsp;{t("setInfo.info")}
                            </Alert>
                            <Card.Body>
                                <Row>
                                    <Col>
                                        <Form.Group as={Row} controlId="username">
                                            <Form.Label className="required-field" column sm="3">
                                                {t("setInfo.username")}
                                            </Form.Label>
                                            <Col sm="9">
                                                <TextInput
                                                    className="form-control"
                                                    name="username"
                                                    id="username"
                                                    errorMessage="Please enter your username"
                                                    placeholder="Enter your username"
                                                    type="text"
                                                    required
                                                    value={newUser.username}
                                                    onChange={userHandler}
                                                />
                                            </Col>
                                        </Form.Group>
                                    </Col>
                                </Row>
                                <Row>
                                    <Col>
                                        <Form.Group as={Row} controlId="name">
                                            <Form.Label className="required-field" column sm="3">
                                                {t("setInfo.name")}
                                            </Form.Label>
                                            <Col>
                                                <TextInput
                                                    className="form-control"
                                                    name="firstname"
                                                    id="firstname"
                                                    type="text"
                                                    required
                                                    errorMessage="Please enter your first name"
                                                    placeholder="Enter your first name"
                                                    value={newUser.firstname}
                                                    onChange={userHandler}
                                                />
                                            </Col>
                                            <Col>
                                                <TextInput
                                                    className="form-control"
                                                    name="lastname"
                                                    id="lastname"
                                                    type="text"
                                                    placeholder="Enter your last name"
                                                    value={newUser.lastname}
                                                    onChange={userHandler}
                                                />
                                            </Col>
                                        </Form.Group>
                                    </Col>
                                </Row>
                                <Row>
                                    <Col>
                                        <Form.Group as={Row} controlId="position">
                                            <Form.Label column sm="3">
                                                {t("setInfo.position")}
                                            </Form.Label>
                                            <Col sm="9">
                                                <TextInput
                                                    className="form-control"
                                                    name="position"
                                                    id="position"
                                                    type="text"
                                                    placeholder="Enter the position"
                                                    value={newUser.position}
                                                    onChange={userHandler}
                                                />
                                            </Col>
                                        </Form.Group>
                                    </Col>
                                </Row>
                                <Row>
                                    <Col>
                                        <Form.Group as={Row} controlId="Email">
                                            <Form.Label className="required-field" column sm="3">
                                                {t("setInfo.email")}
                                            </Form.Label>
                                            <Col sm="9">
                                                <TextInput
                                                    name="email"
                                                    id="email"
                                                    placeholder="Enter your email"
                                                    type="text"
                                                    required
                                                    disabled={true}
                                                    value={newUser.email}
                                                    onChange={userHandler}
                                                />
                                            </Col>
                                        </Form.Group>
                                    </Col>
                                </Row>
                                <Row>
                                    <Col>
                                        <Form.Group as={Row} controlId="mobile_number">
                                            <Form.Label column sm="3">
                                                {t("setInfo.phno")}
                                            </Form.Label>
                                            <Col>
                                                <SelectGroup
                                                    name="dial_code"
                                                    id="dialCode"
                                                    value={newUser.dial_code}
                                                    onChange={userHandler}
                                                    style={{
                                                        backgroundColor: "inherit",
                                                        borderRadius: "5px",
                                                        color: "#495057",
                                                    }}
                                                >
                                                    <option value="">Dial Code</option>
                                                    {countryList.map((item, i) => {
                                                        return (
                                                            <option
                                                                key={i}
                                                                value={item.dial_code}
                                                            >{`(${item.dial_code}) ${item.title}`}</option>
                                                        );
                                                    })}
                                                </SelectGroup>
                                            </Col>
                                            <Col>
                                                <TextInput
                                                    name="mobile_number"
                                                    id="mobile_number"
                                                    type="text"
                                                    placeholder="Enter the phone number"
                                                    value={newUser.mobile_number}
                                                    onChange={userHandler}
                                                />
                                            </Col>
                                        </Form.Group>
                                    </Col>
                                </Row>
                                <Row>
                                    <Col>
                                        <Form.Group as={Row} controlId="Password">
                                            <Form.Label className="required-field" column sm="3">
                                                {t("setInfo.password")}
                                            </Form.Label>
                                            <Col sm="9">
                                                <TextInput
                                                    name="password"
                                                    id="password"
                                                    type="password"
                                                    pattern="^(?=.*?[A-Z])(?=.*?[a-z])(?=.*?[0-9])(?=.*?[#?!@$%^&*-]).{8,}$"
                                                    errorMessage={{
                                                        required: "Please enter the password",
                                                        pattern:
                                                            "Password should contain at least 8 characters, with at least one digit, one letter in upper case & one special character",
                                                    }}
                                                    value={newUser.password}
                                                    onChange={userHandler}
                                                    required
                                                />
                                            </Col>
                                        </Form.Group>
                                    </Col>
                                </Row>
                                <Row>
                                    <Col>
                                        <Form.Group as={Row} controlId="Password">
                                            <Form.Label className="required-field" column sm="3">
                                                {t("setInfo.confirmpassword")}
                                            </Form.Label>
                                            <Col sm="9">
                                                <TextInput
                                                    name="confirm_password"
                                                    id="confirm_password"
                                                    type="password"
                                                    validator={matchPassword}
                                                    errorMessage={{
                                                        required: "Confirm password is required",
                                                        validator: "Password does not match",
                                                    }}
                                                    required
                                                    value={newUser.confirm_password}
                                                    onChange={userHandler}
                                                />
                                            </Col>
                                        </Form.Group>
                                    </Col>
                                </Row>
                                <div className="d-flex justify-content-end">
                                    <Button className="w-20" variant="success" type="submit">
                                        {t("setInfo.accept")}&nbsp;&nbsp;
                                        <FontAwesomeIcon icon={faArrowCircleRight} color="white" />
                                    </Button>
                                </div>
                            </Card.Body>
                        </Card>
                    </ValidationFormWrapper>
                </div>
            )}
            <Confirmation
                userId={authToken}
                endpoint="/deleteUser"
                isopen={warningDialog}
                manageDialog={(val: any) => closeHandler(val)}
            />
        </>
    );
};

const mapStateToProps = () => ({});
export default connect(mapStateToProps)(DeclartionForm);
