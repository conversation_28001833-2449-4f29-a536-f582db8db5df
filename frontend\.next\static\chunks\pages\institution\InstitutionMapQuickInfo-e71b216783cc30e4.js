(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[9314],{19667:(e,t,n)=>{"use strict";n.r(t),n.d(t,{default:()=>u});var i=n(37876),r=n(80942),s=n(14232),a=n(48230),l=n.n(a),c=n(53718),o=n(31753);let u=function(e){let[t,n]=(0,s.useState)({institutions:"",german_operations:"",projects:"",german_countryId:"",german_institutions:""}),{t:a}=(0,o.Bd)("common"),u=async e=>{let t=await c.A.get("/stats/institutions",e);t&&n(t)};return(0,s.useEffect)(()=>{u({query:{status:{$ne:"Request Pending"}},sort:{title:"asc"},limit:"~"})},[]),(0,i.jsx)("div",{className:"quick-info-filter",children:(0,i.jsxs)(r.A,{style:{marginLeft:"-10px"},children:[(0,i.jsx)(r.A.Item,{children:(0,i.jsx)(l(),{href:{pathname:"/institution"},children:(0,i.jsxs)("div",{className:"info-item",children:[(0,i.jsx)("div",{className:"quickinfo-img",children:(0,i.jsx)("img",{src:"/images/quickinfo1.png",width:"27",height:"30",alt:"Organization Quick Info"})}),(0,i.jsxs)("span",{children:[(0,i.jsx)("b",{children:t.institutions})," ",a("AllOrganisations")]})]})})}),(0,i.jsx)(r.A.Item,{children:(0,i.jsxs)(l(),{href:{pathname:"/institution",query:{country:t.german_countryId}},children:[(0,i.jsx)("div",{className:"quickinfo-img",children:(0,i.jsx)("img",{src:"/images/quickinfo2.png",width:"24",height:"23",alt:"Organization Quick Info"})}),(0,i.jsxs)("span",{children:[(0,i.jsx)("b",{children:t.german_institutions})," ",a("GermanOrganisations")]})]})}),(0,i.jsx)(r.A.Item,{children:(0,i.jsxs)(l(),{href:"/project",as:"/project",children:[(0,i.jsx)("div",{className:"quickinfo-img",children:(0,i.jsx)("img",{src:"/images/quickinfo3.png",width:"24",height:"21",alt:"Organization Quick Info"})}),(0,i.jsxs)("span",{children:[(0,i.jsx)("b",{children:t.projects})," ",a("Projects")]})]})})]})})}},65688:(e,t,n)=>{"use strict";n.d(t,{A:()=>x});var i=n(3173),r=n(14232),s=n(72888),a=n(59672),l=n(14867),c=n(8258),o=n(629),u=n(74522),d=n(69455),f=n(37876);let h=["as","onSelect","activeKey","role","onKeyDown"],m=()=>{},g=(0,u.sE)("event-key"),j=r.forwardRef((e,t)=>{let n,d,{as:j="div",onSelect:x,activeKey:p,role:v,onKeyDown:y}=e,w=function(e,t){if(null==e)return{};var n={};for(var i in e)if(({}).hasOwnProperty.call(e,i)){if(t.indexOf(i)>=0)continue;n[i]=e[i]}return n}(e,h),A=(0,s.A)(),k=(0,r.useRef)(!1),b=(0,r.useContext)(c.A),I=(0,r.useContext)(o.A);I&&(v=v||"tablist",p=I.activeKey,n=I.getControlledId,d=I.getControllerId);let _=(0,r.useRef)(null),N=e=>{let t=_.current;if(!t)return null;let n=(0,i.A)(t,`[${g}]:not([aria-disabled=true])`),r=t.querySelector("[aria-selected=true]");if(!r||r!==document.activeElement)return null;let s=n.indexOf(r);if(-1===s)return null;let a=s+e;return a>=n.length&&(a=0),a<0&&(a=n.length-1),n[a]},O=(e,t)=>{null!=e&&(null==x||x(e,t),null==b||b(e,t))};(0,r.useEffect)(()=>{if(_.current&&k.current){let e=_.current.querySelector(`[${g}][aria-selected=true]`);null==e||e.focus()}k.current=!1});let q=(0,a.A)(t,_);return(0,f.jsx)(c.A.Provider,{value:O,children:(0,f.jsx)(l.A.Provider,{value:{role:v,activeKey:(0,c.u)(p),getControlledId:n||m,getControllerId:d||m},children:(0,f.jsx)(j,Object.assign({},w,{onKeyDown:e=>{let t;if(null==y||y(e),I){switch(e.key){case"ArrowLeft":case"ArrowUp":t=N(-1);break;case"ArrowRight":case"ArrowDown":t=N(1);break;default:return}t&&(e.preventDefault(),O(t.dataset[(0,u.y)("EventKey")]||null,e),k.current=!0,A())}},ref:q,role:v}))})})});j.displayName="Nav";let x=Object.assign(j,{Item:d.A})},75228:(e,t,n)=>{(window.__NEXT_P=window.__NEXT_P||[]).push(["/institution/InstitutionMapQuickInfo",function(){return n(19667)}])},80942:(e,t,n)=>{"use strict";n.d(t,{A:()=>g});var i=n(15039),r=n.n(i),s=n(14232);n(68547);var a=n(22631),l=n(65688),c=n(77346),o=n(76959),u=n(69455),d=n(8258),f=n(37876);let h=s.forwardRef((e,t)=>{let{bsPrefix:n,active:i,disabled:s,eventKey:a,className:l,variant:h,action:m,as:g,...j}=e;n=(0,c.oU)(n,"list-group-item");let[x,p]=(0,u.M)({key:(0,d.u)(a,j.href),active:i,...j}),v=(0,o.A)(e=>{if(s){e.preventDefault(),e.stopPropagation();return}x.onClick(e)});s&&void 0===j.tabIndex&&(j.tabIndex=-1,j["aria-disabled"]=!0);let y=g||(m?j.href?"a":"button":"div");return(0,f.jsx)(y,{ref:t,...j,...x,onClick:v,className:r()(l,n,p.isActive&&"active",s&&"disabled",h&&"".concat(n,"-").concat(h),m&&"".concat(n,"-action"))})});h.displayName="ListGroupItem";let m=s.forwardRef((e,t)=>{let n,{className:i,bsPrefix:s,variant:o,horizontal:u,numbered:d,as:h="div",...m}=(0,a.Zw)(e,{activeKey:"onSelect"}),g=(0,c.oU)(s,"list-group");return u&&(n=!0===u?"horizontal":"horizontal-".concat(u)),(0,f.jsx)(l.A,{ref:t,...m,as:h,className:r()(i,g,o&&"".concat(g,"-").concat(o),n&&"".concat(g,"-").concat(n),d&&"".concat(g,"-numbered"))})});m.displayName="ListGroup";let g=Object.assign(m,{Item:h})}},e=>{var t=t=>e(e.s=t);e.O(0,[636,6593,8792],()=>t(75228)),_N_E=e.O()}]);
//# sourceMappingURL=InstitutionMapQuickInfo-e71b216783cc30e4.js.map