{"version": 3, "file": "static/chunks/pages/institution/components/DiscussionAccordion-b7952a492ef4ada1.js", "mappings": "+EACA,4CACA,8CACA,WACA,OAAe,EAAQ,KAAmE,CAC1F,EACA,SAFsB,kHCsBtB,MAlB4B,IACxB,GAAM,GAAEA,CAAC,CAAE,CAAGC,CAAAA,EAAAA,EAAAA,EAAAA,CAAcA,CAAC,MAiBlBC,IAhBX,MACI,SAe0BA,CAfzBC,CAe0B,CAf1BA,CAASA,CAAAA,CAACC,iBAAiB,aACxB,WAACD,EAAAA,CAASA,CAACE,IAAI,EAACC,SAAS,cACrB,UAACH,EAAAA,CAASA,CAACI,MAAM,WACb,UAACC,MAAAA,CAAIC,UAAU,qBAAaT,EAAE,mBAElC,UAACG,EAAAA,CAASA,CAACO,IAAI,WACX,UAACC,EAAAA,CAAUA,CAAAA,CACPC,KAAK,SACLC,GAAIC,GAASA,EAAMC,MAAM,CAAGD,EAAMC,MAAM,CAAC,EAAE,CAAG,aAMtE,yBCjBuC,CAGtC,YAA4B,aAI7B,oBACA,OACA,iCACA,iCACA,0BACA,0BACA,+BACA,8BACA,8BACA,4BACA,6BAEA,yBAGA,qBACA,qGACA,KAEA,YACA,2EACA,oBACA,SACA,oEACA,KAEA,uDACA,8CACA,sBACA,gBACA,WACA,eACA,eACA,kBACA,yBACA,+BACA,CAAS,CACT,UACA,8BACA,aACA,+BACA,8BACA,gCACA,uCACA,CAAS,CACT,cACA,eACA,cACA,sBACA,iBACA,IACA,gBACA,IACA,gBACA,IACA,KACA,IACA,eACA,IACA,KACA,IACA,IACA,CAAS,CACT,2BAAoC,IAAI,IACxC,cACA,MACA,MACA,KACA,CAAS,CACJ,EAIL,CAAC,CAhFiD,EAAQ,KAAW,YAAZ", "sources": ["webpack://_N_E/?9394", "webpack://_N_E/./pages/institution/components/DiscussionAccordion.tsx", "webpack://_N_E/./node_modules/moment/locale/de.js"], "sourcesContent": ["\n    (window.__NEXT_P = window.__NEXT_P || []).push([\n      \"/institution/components/DiscussionAccordion\",\n      function () {\n        return require(\"private-next-pages/institution/components/DiscussionAccordion.tsx\");\n      }\n    ]);\n    if(module.hot) {\n      module.hot.dispose(function () {\n        window.__NEXT_P.push([\"/institution/components/DiscussionAccordion\"])\n      });\n    }\n  ", "//Import Library\r\nimport React from \"react\";\r\nimport { Accordion } from \"react-bootstrap\";\r\n\r\n//Import services/components\r\nimport Discussion from \"../../../components/common/disussion\";\r\nimport { useTranslation } from 'next-i18next';\r\n\r\nconst DiscussionAccordion = (props: any) => {\r\n    const { t } = useTranslation('common');\r\n    return (\r\n        <Accordion defaultActiveKey=\"2\">\r\n            <Accordion.Item eventKey=\"2\">\r\n                <Accordion.Header>\r\n                    <div className=\"cardTitle\">{t(\"Discussions\")}</div>\r\n                </Accordion.Header>\r\n                <Accordion.Body>\r\n                    <Discussion\r\n                        type=\"hazard\"\r\n                        id={props && props.routes ? props.routes[1] : null}\r\n                    />\r\n                </Accordion.Body>\r\n            </Accordion.Item>\r\n        </Accordion>\r\n    );\r\n};\r\nexport default DiscussionAccordion;", "//! moment.js locale configuration\n//! locale : German [de]\n//! author : lluchs : https://github.com/lluchs\n//! author: <PERSON><PERSON><PERSON>: https://github.com/Oire\n//! author : <PERSON><PERSON><PERSON><PERSON> : https://github.com/mik01aj\n\n;(function (global, factory) {\n   typeof exports === 'object' && typeof module !== 'undefined'\n       && typeof require === 'function' ? factory(require('../moment')) :\n   typeof define === 'function' && define.amd ? define(['../moment'], factory) :\n   factory(global.moment)\n}(this, (function (moment) { 'use strict';\n\n    //! moment.js locale configuration\n\n    function processRelativeTime(number, withoutSuffix, key, isFuture) {\n        var format = {\n            m: ['eine Minute', 'einer Minute'],\n            h: ['eine Stunde', 'einer Stunde'],\n            d: ['ein Tag', 'einem Tag'],\n            dd: [number + ' Tage', number + ' Tagen'],\n            w: ['eine Woche', 'einer Woche'],\n            M: ['ein <PERSON><PERSON>', 'einem Monat'],\n            MM: [number + ' <PERSON><PERSON>', number + ' Monaten'],\n            y: ['ein Jahr', 'einem Jahr'],\n            yy: [number + ' Jahre', number + ' Jahren'],\n        };\n        return withoutSuffix ? format[key][0] : format[key][1];\n    }\n\n    var de = moment.defineLocale('de', {\n        months: 'Januar_Februar_März_April_Mai_Juni_Juli_August_September_Oktober_November_Dezember'.split(\n            '_'\n        ),\n        monthsShort:\n            'Jan._Feb._März_Apr._Mai_Juni_Juli_Aug._Sep._Okt._Nov._Dez.'.split('_'),\n        monthsParseExact: true,\n        weekdays:\n            'Sonntag_Montag_Dienstag_Mittwoch_Donnerstag_Freitag_Samstag'.split(\n                '_'\n            ),\n        weekdaysShort: 'So._Mo._Di._Mi._Do._Fr._Sa.'.split('_'),\n        weekdaysMin: 'So_Mo_Di_Mi_Do_Fr_Sa'.split('_'),\n        weekdaysParseExact: true,\n        longDateFormat: {\n            LT: 'HH:mm',\n            LTS: 'HH:mm:ss',\n            L: 'DD.MM.YYYY',\n            LL: 'D. MMMM YYYY',\n            LLL: 'D. MMMM YYYY HH:mm',\n            LLLL: 'dddd, D. MMMM YYYY HH:mm',\n        },\n        calendar: {\n            sameDay: '[heute um] LT [Uhr]',\n            sameElse: 'L',\n            nextDay: '[morgen um] LT [Uhr]',\n            nextWeek: 'dddd [um] LT [Uhr]',\n            lastDay: '[gestern um] LT [Uhr]',\n            lastWeek: '[letzten] dddd [um] LT [Uhr]',\n        },\n        relativeTime: {\n            future: 'in %s',\n            past: 'vor %s',\n            s: 'ein paar Sekunden',\n            ss: '%d Sekunden',\n            m: processRelativeTime,\n            mm: '%d Minuten',\n            h: processRelativeTime,\n            hh: '%d Stunden',\n            d: processRelativeTime,\n            dd: processRelativeTime,\n            w: processRelativeTime,\n            ww: '%d Wochen',\n            M: processRelativeTime,\n            MM: processRelativeTime,\n            y: processRelativeTime,\n            yy: processRelativeTime,\n        },\n        dayOfMonthOrdinalParse: /\\d{1,2}\\./,\n        ordinal: '%d.',\n        week: {\n            dow: 1, // Monday is the first day of the week.\n            doy: 4, // The week that contains Jan 4th is the first week of the year.\n        },\n    });\n\n    return de;\n\n})));\n"], "names": ["t", "useTranslation", "DiscussionAccordion", "Accordion", "defaultActiveKey", "<PERSON><PERSON>", "eventKey", "Header", "div", "className", "Body", "Discussion", "type", "id", "props", "routes"], "sourceRoot": "", "ignoreList": [2]}