"use strict";exports.id=49,exports.ids=[49],exports.modules={4982:(e,t,s)=>{s.a(e,async(e,n)=>{try{s.r(t),s.d(t,{default:()=>v});var a=s(8732),r=s(82015),i=s(14062),l=s(12403),o=s(59549),d=s(83551),c=s(49481),u=s(91353),m=s(42893),p=s(23579),h=s(66994),x=s(49856),f=s(63487),A=s(88751),j=e([i,m,f]);[i,m,f]=j.then?(await j)():j;let v=(0,i.connect)()(e=>{let{t}=(0,A.useTranslation)("common"),{isOpen:s,manageClose:n,data:i}=e,j=()=>{n(!1)},[v,g]=(0,r.useState)({_id:"",username:"",firstname:"",lastname:"",position:"",institution:"",role:"",image:null,email:"",password:"",dataConsentPolicy:"",restrictedUsePolicy:"",acceptCookiesPolicy:"",withdrawConsentPolicy:"",medicalConsentPolicy:"",fullDataProtectionConsentPolicy:""}),[y,b]=(0,r.useState)([]);(0,r.useEffect)(()=>{(async()=>{let e=i&&i.institution&&i.institution._id?i.institution._id:"";g(t=>({...t,...i,institution:e,image:null}))})()},[i]),(0,r.useEffect)(()=>{let e={query:{},sort:{title:"asc"},limit:"~",select:"-contact_name -description -type -networks -expertise -hazard_types -hazards -address -focal_points -website -telephone -twitter -header -use_default_header -images -status -email -user -created_at -updated_at -primary_focal_point"};(async()=>{let t=await f.A.get("/institution",e);t&&Array.isArray(t.data)&&b(t.data)})()},[]);let P=e=>{if(e.target){let{name:t,value:s}=e.target;g(e=>({...e,[t]:s}))}},I=async s=>{s.preventDefault(),await f.A.post("/users/updateProfile",v)&&(e.dispatch((0,x.js)()),m.default.success(t("toast.ProfileUpdatedSuccessfully")),n(!1))};return(0,a.jsx)("div",{children:(0,a.jsx)(l.A,{show:s,onHide:j,size:"xl",className:"w-100",centered:!0,children:(0,a.jsxs)(h.A,{className:"m-4",onSubmit:I,initialValues:v,enableReinitialize:!0,children:[(0,a.jsx)(l.A.Header,{closeButton:!0,children:(0,a.jsx)(l.A.Title,{children:t("setInfo.editprofile")})}),(0,a.jsxs)(l.A.Body,{children:[(0,a.jsx)("h5",{children:t("setInfo.MyInformation")}),(0,a.jsxs)("div",{className:"p-2 w-100",children:[(0,a.jsxs)(o.A.Group,{as:d.A,controlId:"username",className:"mb-3",children:[(0,a.jsx)(o.A.Label,{column:!0,md:"4",xs:"5",lg:"2",className:"required-field",children:t("setInfo.username")}),(0,a.jsx)(c.A,{md:"8",xs:"7",lg:"10",children:(0,a.jsx)(p.ks,{name:"username",required:!0,type:"text",value:v&&v.username,placeholder:t("setInfo.EnterYourName"),onChange:P})})]}),(0,a.jsxs)(o.A.Group,{as:d.A,controlId:"username",className:"mb-3",children:[(0,a.jsx)(o.A.Label,{column:!0,md:"4",xs:"5",lg:"2",className:"required-field",children:t("setInfo.name")}),(0,a.jsx)(c.A,{md:"8",xs:"7",lg:"10",children:(0,a.jsxs)(d.A,{children:[(0,a.jsx)(c.A,{xs:"12",sm:"6",children:(0,a.jsx)(p.ks,{name:"firstname",required:!0,type:"text",value:v&&v.firstname,placeholder:t("setInfo.Enteryourfirstname"),errorMessage:t("setInfo.Pleaseenteryourfirstname"),onChange:P})}),(0,a.jsx)(c.A,{xs:"12",sm:"6",className:"pt-2 pt-sm-0",children:(0,a.jsx)(p.ks,{name:"lastname",type:"text",value:v&&v.lastname,placeholder:t("setInfo.EnterYourlastname"),onChange:P})})]})})]}),(0,a.jsxs)(o.A.Group,{as:d.A,controlId:"position",className:"mb-3",children:[(0,a.jsx)(o.A.Label,{column:!0,md:"4",xs:"5",lg:"2",children:t("setInfo.position")}),(0,a.jsx)(c.A,{md:"8",xs:"7",lg:"10",children:(0,a.jsx)(p.ks,{name:"position",type:"text",value:v&&v.position,placeholder:t("setInfo.EnterYourposition"),onChange:P})})]}),(0,a.jsxs)(o.A.Group,{as:d.A,controlId:"institution",className:"mb-3",children:[(0,a.jsx)(o.A.Label,{column:!0,md:"4",xs:"5",lg:"2",children:t("setInfo.organisation")}),(0,a.jsx)(c.A,{md:"8",xs:"7",lg:"10",children:(0,a.jsxs)(p.s3,{name:"partner_institution",id:"partner_institution",value:v.institution,onChange:e=>{let{value:t}=e.target;g(e=>({...e,institution:t}))},style:{backgroundColor:"inherit",borderRadius:"5px",color:"#495057"},children:[(0,a.jsx)("option",{value:"",children:t("setInfo.SelectOrganisation")}),y.map((e,t)=>(0,a.jsx)("option",{value:e._id,children:e.title}))]})})]}),(0,a.jsxs)(o.A.Group,{as:d.A,controlId:"Email",className:"mb-3",children:[(0,a.jsx)(o.A.Label,{column:!0,md:"4",xs:"5",lg:"2",className:"required-field",children:t("setInfo.email")}),(0,a.jsx)(c.A,{md:"8",xs:"7",lg:"10",children:(0,a.jsx)(p.ks,{required:!0,name:"email",type:"text",value:v&&v.email,placeholder:t("setInfo.EnterYourEmail"),onChange:P})})]}),(0,a.jsxs)(o.A.Group,{as:d.A,controlId:"password",className:"mb-3",children:[(0,a.jsx)(o.A.Label,{column:!0,md:"4",xs:"5",lg:"2",children:t("setInfo.password")}),(0,a.jsx)(c.A,{md:"8",xs:"7",lg:"10",children:(0,a.jsx)(p.ks,{name:"password",type:"password",value:v.password,placeholder:t("setInfo.EnterYourNewPassword"),onChange:P,pattern:"^(?=.*?[A-Z])(?=.*?[a-z])(?=.*?[0-9])(?=.*?[#?!@$%^&*-]).{8,}$",errorMessage:{pattern:t("setInfo.Passwordshouldcontainatleastcharacter")}})})]})]})]}),(0,a.jsxs)(l.A.Footer,{className:"pb-0",children:[(0,a.jsx)(u.A,{variant:"primary",type:"submit",children:t("setInfo.savechanges")}),(0,a.jsx)(u.A,{variant:"danger",onClick:j,children:t("setInfo.Cancel")})]})]})})})});n()}catch(e){n(e)}})},15653:(e,t,s)=>{s.d(t,{ks:()=>i,s3:()=>l});var n=s(8732);s(82015);var a=s(59549),r=s(43294);let i=({name:e,id:t,required:s,validator:i,errorMessage:l,onChange:o,value:d,as:c,multiline:u,rows:m,pattern:p,...h})=>(0,n.jsx)(r.Field,{name:e,validate:e=>{let t="string"==typeof e?e:String(e||"");return s&&(!e||""===t.trim())?l?.validator||"This field is required":i&&!i(e)?l?.validator||"Invalid value":p&&e&&!new RegExp(p).test(e)?l?.pattern||"Invalid format":void 0},children:({field:e,meta:s})=>(0,n.jsxs)(n.Fragment,{children:[(0,n.jsx)(a.A.Control,{...e,...h,id:t,as:c||"input",rows:m,isInvalid:s.touched&&!!s.error,onChange:t=>{e.onChange(t),o&&o(t)},value:void 0!==d?d:e.value}),s.touched&&s.error?(0,n.jsx)(a.A.Control.Feedback,{type:"invalid",children:s.error}):null]})}),l=({name:e,id:t,required:s,errorMessage:i,onChange:l,value:o,children:d,...c})=>(0,n.jsx)(r.Field,{name:e,validate:e=>{if(s&&(!e||""===e))return i?.validator||"This field is required"},children:({field:e,meta:s})=>(0,n.jsxs)(n.Fragment,{children:[(0,n.jsx)(a.A.Control,{as:"select",...e,...c,id:t,isInvalid:s.touched&&!!s.error,onChange:t=>{e.onChange(t),l&&l(t)},value:void 0!==o?o:e.value,children:d}),s.touched&&s.error?(0,n.jsx)(a.A.Control.Feedback,{type:"invalid",children:s.error}):null]})})},23579:(e,t,s)=>{s.d(t,{sx:()=>c,s3:()=>a.s3,ks:()=>a.ks,yk:()=>n.A});var n=s(66994),a=s(15653),r=s(8732),i=s(82015),l=s.n(i),o=s(43294),d=s(59549);let c={RadioGroup:({name:e,valueSelected:t,onChange:s,errorMessage:n,children:a})=>{let{errors:i,touched:d}=(0,o.useFormikContext)(),c=d[e]&&i[e];l().useMemo(()=>({name:e}),[e]);let u=l().Children.map(a,t=>l().isValidElement(t)&&function(e){return"object"==typeof e&&null!==e}(t.props)?l().cloneElement(t,{name:e,...t.props}):t);return(0,r.jsxs)("div",{children:[(0,r.jsx)("div",{className:"radio-group",children:u}),c&&(0,r.jsx)("div",{className:"invalid-feedback d-block",children:n||("string"==typeof i[e]?i[e]:String(i[e]))})]})},RadioItem:({id:e,label:t,value:s,name:n,disabled:a})=>{let{values:i,setFieldValue:l}=(0,o.useFormikContext)(),c=n||e;return(0,r.jsx)(d.A.Check,{type:"radio",id:e,label:t,value:s,name:c,checked:i[c]===s,onChange:e=>{l(c,e.target.value)},disabled:a,inline:!0})}};n.A,a.ks,a.s3},66994:(e,t,s)=>{s.d(t,{A:()=>o});var n=s(8732),a=s(82015),r=s(43294),i=s(18622);let l=(0,a.forwardRef)((e,t)=>{let{children:s,onSubmit:a,autoComplete:l,className:o,onKeyPress:d,initialValues:c,...u}=e,m=i.object().shape({});return(0,n.jsx)(r.Formik,{initialValues:c||{},validationSchema:m,onSubmit:(e,t)=>{let s={preventDefault:()=>{},stopPropagation:()=>{},currentTarget:null,target:null,nativeEvent:new Event("submit"),bubbles:!1,cancelable:!0,defaultPrevented:!1,eventPhase:0,isTrusted:!1,timeStamp:Date.now(),type:"submit",isDefaultPrevented:()=>!1,isPropagationStopped:()=>!1,persist:()=>{}};a&&a(s,e,t)},...u,children:e=>(0,n.jsx)(r.Form,{ref:t,onSubmit:e.handleSubmit,autoComplete:l,className:o,onKeyPress:d,children:"function"==typeof s?s(e):s})})});l.displayName="ValidationFormWrapper";let o=l},80237:(e,t)=>{Object.defineProperty(t,"A",{enumerable:!0,get:function(){return s}});var s=function(e){return e.PAGES="PAGES",e.PAGES_API="PAGES_API",e.APP_PAGE="APP_PAGE",e.APP_ROUTE="APP_ROUTE",e.IMAGE="IMAGE",e}({})},81413:(e,t)=>{Object.defineProperty(t,"M",{enumerable:!0,get:function(){return function e(t,s){return s in t?t[s]:"then"in t&&"function"==typeof t.then?t.then(t=>e(t,s)):"function"==typeof t&&"default"===s?t:void 0}}})}};