"use strict";(()=>{var e={};e.id=518,e.ids=[518,636,3220],e.modules={123:e=>{e.exports=require("dom-helpers/removeEventListener")},1332:e=>{e.exports=require("react-custom-scrollbars-2")},1428:e=>{e.exports=import("axios")},1680:e=>{e.exports=require("@restart/ui/utils")},1919:e=>{e.exports=require("react-transition-group/Transition")},3892:e=>{e.exports=require("classnames")},4048:e=>{e.exports=require("react-truncate")},6009:e=>{e.exports=require("dom-helpers/ownerDocument")},6952:e=>{e.exports=require("@restart/ui/Modal")},7364:(e,r,t)=>{t.a(e,async(e,s)=>{try{t.r(r),t.d(r,{config:()=>g,default:()=>c,getServerSideProps:()=>h,getStaticPaths:()=>m,getStaticProps:()=>x,reportWebVitals:()=>q,routeModule:()=>w,unstable_getServerProps:()=>v,unstable_getServerSideProps:()=>S,unstable_getStaticParams:()=>y,unstable_getStaticPaths:()=>j,unstable_getStaticProps:()=>f});var i=t(63885),n=t(80237),a=t(81413),o=t(9616),u=t.n(o),p=t(72386),d=t(19435),l=e([p,d]);[p,d]=l.then?(await l)():l;let c=(0,a.M)(d,"default"),x=(0,a.M)(d,"getStaticProps"),m=(0,a.M)(d,"getStaticPaths"),h=(0,a.M)(d,"getServerSideProps"),g=(0,a.M)(d,"config"),q=(0,a.M)(d,"reportWebVitals"),f=(0,a.M)(d,"unstable_getStaticProps"),j=(0,a.M)(d,"unstable_getStaticPaths"),y=(0,a.M)(d,"unstable_getStaticParams"),v=(0,a.M)(d,"unstable_getServerProps"),S=(0,a.M)(d,"unstable_getServerSideProps"),w=new i.PagesRouteModule({definition:{kind:n.A.PAGES,page:"/institution",pathname:"/institution",bundlePath:"",filename:""},components:{App:p.default,Document:u()},userland:d});s()}catch(e){s(e)}})},7374:e=>{e.exports=require("@restart/ui/Dropdown")},8732:e=>{e.exports=require("react/jsx-runtime")},9532:e=>{e.exports=require("@restart/ui/Nav")},9653:e=>{e.exports=require("@restart/ui/Overlay")},11e3:e=>{e.exports=require("react-multi-select-component")},11242:e=>{e.exports=require("redux-persist/integration/react")},11688:e=>{e.exports=require("@restart/hooks/useWillUnmount")},12388:e=>{e.exports=require("dom-helpers/addEventListener")},13364:e=>{e.exports=require("redux-saga/effects")},14062:e=>{e.exports=import("react-redux")},14078:e=>{e.exports=import("swr")},14332:e=>{e.exports=require("uncontrollable")},16116:e=>{e.exports=require("invariant")},18622:e=>{e.exports=require("yup")},19121:e=>{e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},19435:(e,r,t)=>{t.a(e,async(e,s)=>{try{t.r(r),t.d(r,{default:()=>w,getStaticProps:()=>S});var i=t(8732),n=t(82015),a=t(19918),o=t.n(a),u=t(20156),p=t.n(u),d=t(7082),l=t(83551),c=t(49481),x=t(20181),m=t(27053),h=t(54642),g=t(87256),q=t(66972),f=t(88751),j=t(28966),y=t(35576),v=e([x,h,g]);async function S({locale:e}){return{props:{...await (0,y.serverSideTranslations)(e,["common"])}}}[x,h,g]=v.then?(await v)():v;let w=()=>{let[e,r]=(0,n.useState)([]),[t,s]=(0,n.useState)([]),{t:a}=(0,f.useTranslation)("common"),u=()=>(0,i.jsx)(o(),{href:"/institution/[...routes]",as:"/institution/create",children:(0,i.jsx)(p(),{variant:"secondary",size:"sm",children:a("addOrganisation")})}),y=e=>{s(e)},v=(0,j.canAddInstitution)(()=>(0,i.jsx)(u,{}));return(0,i.jsxs)(d.A,{fluid:!0,className:"p-0",children:[(0,i.jsx)(l.A,{children:(0,i.jsx)(c.A,{xs:12,children:(0,i.jsx)(m.A,{title:a("menu.organisations")})})}),(0,i.jsx)(l.A,{children:(0,i.jsxs)(c.A,{xs:12,className:"organisationmap_div",children:[(0,i.jsx)("div",{className:"organisationMap",children:(0,i.jsx)(q.default,{institutions:e})}),(0,i.jsx)("div",{className:"organisationInfo",children:(0,i.jsx)(g.default,{})})]})}),(0,i.jsx)(l.A,{children:(0,i.jsx)(c.A,{xs:12,children:(0,i.jsx)(x.A,{filtreg:e=>y(e),selectedRegions:[],regionHandler:y})})}),(0,i.jsx)(l.A,{children:(0,i.jsx)(c.A,{xs:12,className:"ps-4",children:(0,i.jsx)(v,{})})}),(0,i.jsx)(l.A,{className:"mt-3",children:(0,i.jsx)(c.A,{xs:12,children:(0,i.jsx)(h.default,{selectedRegions:t,setInstitutions:r})})})]})};s()}catch(e){s(e)}})},19803:(e,r,t)=>{t.d(r,{A:()=>q});var s=t(3892),i=t.n(s),n=t(82015);t(26324);var a=t(14332),o=t(9532),u=t.n(o),p=t(80739),d=t(81895),l=t.n(d),c=t(25303),x=t(86842),m=t(8732);let h=n.forwardRef(({bsPrefix:e,active:r,disabled:t,eventKey:s,className:n,variant:a,action:o,as:u,...d},h)=>{e=(0,p.oU)(e,"list-group-item");let[g,q]=(0,c.useNavItem)({key:(0,x.makeEventKey)(s,d.href),active:r,...d}),f=l()(e=>{if(t){e.preventDefault(),e.stopPropagation();return}g.onClick(e)});t&&void 0===d.tabIndex&&(d.tabIndex=-1,d["aria-disabled"]=!0);let j=u||(o?d.href?"a":"button":"div");return(0,m.jsx)(j,{ref:h,...d,...g,onClick:f,className:i()(n,e,q.isActive&&"active",t&&"disabled",a&&`${e}-${a}`,o&&`${e}-action`)})});h.displayName="ListGroupItem";let g=n.forwardRef((e,r)=>{let t,{className:s,bsPrefix:n,variant:o,horizontal:d,numbered:l,as:c="div",...x}=(0,a.useUncontrolled)(e,{activeKey:"onSelect"}),h=(0,p.oU)(n,"list-group");return d&&(t=!0===d?"horizontal":`horizontal-${d}`),(0,m.jsx)(u(),{ref:r,...x,as:c,className:i()(s,h,o&&`${h}-${o}`,t&&`${h}-${t}`,l&&`${h}-numbered`)})});g.displayName="ListGroup";let q=Object.assign(g,{Item:h})},22326:e=>{e.exports=require("react-dom")},22541:e=>{e.exports=require("redux-persist/lib/storage")},25303:e=>{e.exports=require("@restart/ui/NavItem")},26324:e=>{e.exports=require("warning")},27825:e=>{e.exports=require("lodash")},27910:e=>{e.exports=require("stream")},28217:e=>{e.exports=require("@restart/ui/DropdownItem")},28966:(e,r,t)=>{t.r(r),t.d(r,{canAddInstitution:()=>o,canAddInstitutionForm:()=>u,canEditInstitution:()=>p,canEditInstitutionForm:()=>d,canManageFocalPoints:()=>c,canViewDiscussionUpdate:()=>l,default:()=>x});var s=t(8732);t(82015);var i=t(81366),n=t.n(i),a=t(61421);let o=n()({authenticatedSelector:e=>!!e.permissions&&!!e.permissions.institution&&!!e.permissions.institution["create:any"],wrapperDisplayName:"CanAddInstitution"}),u=n()({authenticatedSelector:e=>!!e.permissions&&!!e.permissions.institution&&!!e.permissions.institution["create:any"],wrapperDisplayName:"CanAddInstitutionForm",FailureComponent:()=>(0,s.jsx)(a.default,{})}),p=n()({authenticatedSelector:(e,r)=>{if(e.permissions&&e.permissions.institution){if(e.permissions.institution["update:any"])return!0;else if(e.permissions.institution["update:own"]&&r.institution&&r.institution.user&&r.institution.user===e.user._id)return!0}return!1},wrapperDisplayName:"CanEditInstitution"}),d=n()({authenticatedSelector:(e,r)=>{if(e.permissions&&e.permissions.institution){if(e.permissions.institution["update:any"])return!0;else if(e.permissions.institution["update:own"]&&r.institution&&r.institution.user&&r.institution.user===e.user._id)return!0}return!1},wrapperDisplayName:"CanEditInstitutionForm",FailureComponent:()=>(0,s.jsx)(a.default,{})}),l=n()({authenticatedSelector:e=>!!e.permissions&&!!e.permissions.update&&!!e.permissions.update["read:any"],wrapperDisplayName:"CanViewDiscussionUpdate"}),c=n()({authenticatedSelector:(e,r)=>{if(e.permissions&&e.permissions.institution_focal_point){if(e.permissions.institution_focal_point["update:any"])return!0;else if(e.permissions.institution_focal_point["update:own"]&&r.institution&&r.institution.user&&r.institution.user===e.user._id)return!0}return!1},wrapperDisplayName:"canManageFocalPoints"}),x=o},29021:e=>{e.exports=require("fs")},29294:e=>{e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},29628:e=>{e.exports=require("dom-helpers/addClass")},29825:e=>{e.exports=require("prop-types")},29841:e=>{e.exports=require("dom-helpers/hasClass")},30362:e=>{e.exports=require("@restart/ui/DropdownToggle")},33873:e=>{e.exports=require("path")},35576:e=>{e.exports=require("next-i18next/serverSideTranslations")},36653:e=>{e.exports=require("nprogress")},36955:e=>{e.exports=require("@restart/hooks/useBreakpoint")},37766:e=>{e.exports=require("@restart/hooks/useMergedRefs")},38609:e=>{e.exports=require("react-data-table-component")},39756:e=>{e.exports=import("redux")},40051:e=>{e.exports=require("dom-helpers/removeClass")},40361:e=>{e.exports=require("next/dist/compiled/next-server/pages.runtime.prod.js")},42893:e=>{e.exports=import("react-hot-toast")},43294:e=>{e.exports=require("formik")},50009:e=>{e.exports=require("@restart/ui/DropdownMenu")},57664:e=>{e.exports=require("dom-helpers/scrollbarSize")},58928:e=>{e.exports=require("dom-helpers/canUseDOM")},59717:e=>{e.exports=require("@restart/ui/DropdownContext")},60560:e=>{e.exports=require("dom-helpers/contains")},63033:e=>{e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},65447:e=>{e.exports=require("@restart/ui/Button")},66972:(e,r,t)=>{t.r(r),t.d(r,{default:()=>d});var s=t(8732),i=t(82015),n=t(27825),a=t.n(n),o=t(72953),u=t(89364),p=t(88751);let d=e=>{let{i18n:r}=(0,p.useTranslation)("common"),t=r.language,{institutions:n}=e,[d,l]=(0,i.useState)({}),[c,x]=(0,i.useState)([]),[m,h]=(0,i.useState)({}),g=()=>{l(null),h(null)},q=(e,r,t)=>{g(),l(r),h({name:e.name,id:e.id,countryId:e.countryId})},f=()=>{let e=[],r=n.filter(e=>"Request Pending"!=e.status);a().forEach(r,r=>{e.push({title:r.title,id:r._id,countryId:r&&r.address&&r.address.country&&r.address.country._id,lat:r.address&&r.address.country&&r.address.country.coordinates&&parseFloat(r.address.country.coordinates[0].latitude),lng:r.address&&r.address.country&&r.address.country.coordinates&&parseFloat(r.address.country.coordinates[0].longitude)})}),x([...e])};return(0,i.useEffect)(()=>{f()},[n]),(0,s.jsx)(o.A,{onClose:g,language:t,points:c,activeMarker:d,markerInfo:(0,s.jsx)(e=>{let{info:r}=e;if(r&&r.countryId){let e=n.filter(e=>"Request Pending"!=e.status).filter(e=>e.address&&e.address.country&&e.address.country._id==r.countryId);return(0,s.jsx)("ul",{children:e.map((e,r)=>(0,s.jsx)("li",{children:(0,s.jsx)("a",{href:`/${t}/institution/show/${e._id}`,children:e.title})},r))})}return null},{info:m}),children:c.length>=1?c.map((e,r)=>(0,s.jsx)(u.A,{name:e.title,id:e.id,countryId:e.countryId,icon:{url:"/images/map-marker-white.svg"},onClick:q,position:e},r)):null})}},67364:e=>{e.exports=require("@restart/hooks/useCallbackRef")},68455:e=>{e.exports=import("redux-saga")},69722:e=>{e.exports=require("es6-promise")},74075:e=>{e.exports=require("zlib")},74716:e=>{e.exports=require("moment")},74987:e=>{e.exports=require("@restart/ui/ModalManager")},78097:e=>{e.exports=require("next-redux-saga")},78634:e=>{e.exports=require("@restart/ui/Anchor")},81366:e=>{e.exports=require("redux-auth-wrapper/connectedAuthWrapper")},81521:e=>{e.exports=require("dom-helpers/querySelectorAll")},81895:e=>{e.exports=require("@restart/hooks/useEventCallback")},82015:e=>{e.exports=require("react")},86842:e=>{e.exports=require("@restart/ui/SelectableContext")},87256:(e,r,t)=>{t.a(e,async(e,s)=>{try{t.r(r),t.d(r,{default:()=>c});var i=t(8732),n=t(19803),a=t(82015),o=t(19918),u=t.n(o),p=t(63487),d=t(88751),l=e([p]);p=(l.then?(await l)():l)[0];let c=function(e){let[r,t]=(0,a.useState)({institutions:"",german_operations:"",projects:"",german_countryId:"",german_institutions:""}),{t:s}=(0,d.useTranslation)("common");return(0,i.jsx)("div",{className:"quick-info-filter",children:(0,i.jsxs)(n.A,{style:{marginLeft:"-10px"},children:[(0,i.jsx)(n.A.Item,{children:(0,i.jsx)(u(),{href:{pathname:"/institution"},children:(0,i.jsxs)("div",{className:"info-item",children:[(0,i.jsx)("div",{className:"quickinfo-img",children:(0,i.jsx)("img",{src:"/images/quickinfo1.png",width:"27",height:"30",alt:"Organization Quick Info"})}),(0,i.jsxs)("span",{children:[(0,i.jsx)("b",{children:r.institutions})," ",s("AllOrganisations")]})]})})}),(0,i.jsx)(n.A.Item,{children:(0,i.jsxs)(u(),{href:{pathname:"/institution",query:{country:r.german_countryId}},children:[(0,i.jsx)("div",{className:"quickinfo-img",children:(0,i.jsx)("img",{src:"/images/quickinfo2.png",width:"24",height:"23",alt:"Organization Quick Info"})}),(0,i.jsxs)("span",{children:[(0,i.jsx)("b",{children:r.german_institutions})," ",s("GermanOrganisations")]})]})}),(0,i.jsx)(n.A.Item,{children:(0,i.jsxs)(u(),{href:"/project",as:"/project",children:[(0,i.jsx)("div",{className:"quickinfo-img",children:(0,i.jsx)("img",{src:"/images/quickinfo3.png",width:"24",height:"21",alt:"Organization Quick Info"})}),(0,i.jsxs)("span",{children:[(0,i.jsx)("b",{children:r.projects})," ",s("Projects")]})]})})]})})};s()}catch(e){s(e)}})},87571:e=>{e.exports=require("dom-helpers/transitionEnd")},88751:e=>{e.exports=require("next-i18next")},93787:e=>{e.exports=require("redux-persist")},94696:e=>{e.exports=require("@react-google-maps/api")},94947:e=>{e.exports=require("@restart/hooks/useTimeout")},96196:e=>{e.exports=require("next-redux-wrapper")},98320:e=>{e.exports=require("@restart/hooks/useIsomorphicEffect")},99460:e=>{e.exports=require("dom-helpers/css")}};var r=require("../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[6089,9216,9616,2386,5016,4642],()=>t(7364));module.exports=s})();