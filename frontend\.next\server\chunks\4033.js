"use strict";exports.id=4033,exports.ids=[4033],exports.modules={13866:(e,t,r)=>{var n=r(92921);t.__esModule=!0,t.default=void 0;var l=n(r(81895)),a=n(r(13408)),u=n(r(42051)),o=n(r(94947)),i=n(r(78634)),s=n(r(3892)),f=function(e,t){if(e&&e.__esModule)return e;if(null===e||"object"!=typeof e&&"function"!=typeof e)return{default:e};var r=O(t);if(r&&r.has(e))return r.get(e);var n={__proto__:null},l=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var a in e)if("default"!==a&&Object.prototype.hasOwnProperty.call(e,a)){var u=l?Object.getOwnPropertyDescriptor(e,a):null;u&&(u.get||u.set)?Object.defineProperty(n,a,u):n[a]=e[a]}return n.default=e,r&&r.set(e,n),n}(r(82015)),c=r(14332),d=n(r(90616)),p=n(r(15931)),v=r(91417),y=r(11940),h=n(r(80732)),b=n(r(22199)),m=n(r(93998)),j=r(8732);function O(e){if("function"!=typeof WeakMap)return null;var t=new WeakMap,r=new WeakMap;return(O=function(e){return e?r:t})(e)}let P=f.forwardRef(({defaultActiveIndex:e=0,...t},r)=>{let n,{as:d="div",bsPrefix:p,slide:O=!0,fade:P=!1,controls:_=!0,indicators:g=!0,indicatorLabels:x=[],activeIndex:w,onSelect:M,onSlide:k,onSlid:N,interval:E=5e3,keyboard:C=!0,onKeyDown:$,pause:A="hover",onMouseOver:W,onMouseOut:S,wrap:D=!0,touch:R=!0,onTouchStart:I,onTouchMove:B,onTouchEnd:G,prevIcon:T=(0,j.jsx)("span",{"aria-hidden":"true",className:"carousel-control-prev-icon"}),prevLabel:L="Previous",nextIcon:U=(0,j.jsx)("span",{"aria-hidden":"true",className:"carousel-control-next-icon"}),nextLabel:V="Next",variant:X,className:F,children:H,...K}=(0,c.useUncontrolled)({defaultActiveIndex:e,...t},{activeIndex:"onSelect"}),q=(0,y.useBootstrapPrefix)(p,"carousel"),z=(0,y.useIsRTL)(),J=(0,f.useRef)(null),[Q,Y]=(0,f.useState)("next"),[Z,ee]=(0,f.useState)(!1),[et,er]=(0,f.useState)(!1),[en,el]=(0,f.useState)(w||0);(0,f.useEffect)(()=>{et||w===en||(J.current?Y(J.current):Y((w||0)>en?"next":"prev"),O&&er(!0),el(w||0))},[w,et,en,O]),(0,f.useEffect)(()=>{J.current&&(J.current=null)});let ea=0;(0,v.forEach)(H,(e,t)=>{++ea,t===w&&(n=e.props.interval)});let eu=(0,u.default)(n),eo=(0,f.useCallback)(e=>{if(et)return;let t=en-1;if(t<0){if(!D)return;t=ea-1}J.current="prev",null==M||M(t,e)},[et,en,M,D,ea]),ei=(0,l.default)(e=>{if(et)return;let t=en+1;if(t>=ea){if(!D)return;t=0}J.current="next",null==M||M(t,e)}),es=(0,f.useRef)();(0,f.useImperativeHandle)(r,()=>({element:es.current,prev:eo,next:ei}));let ef=(0,l.default)(()=>{!document.hidden&&function(e){if(!e||!e.style||!e.parentNode||!e.parentNode.style)return!1;let t=getComputedStyle(e);return"none"!==t.display&&"hidden"!==t.visibility&&"none"!==getComputedStyle(e.parentNode).display}(es.current)&&(z?eo():ei())}),ec="next"===Q?"start":"end";(0,a.default)(()=>{O||(null==k||k(en,ec),null==N||N(en,ec))},[en]);let ed=`${q}-item-${Q}`,ep=`${q}-item-${ec}`,ev=(0,f.useCallback)(e=>{(0,b.default)(e),null==k||k(en,ec)},[k,en,ec]),ey=(0,f.useCallback)(()=>{er(!1),null==N||N(en,ec)},[N,en,ec]),eh=(0,f.useCallback)(e=>{if(C&&!/input|textarea/i.test(e.target.tagName))switch(e.key){case"ArrowLeft":e.preventDefault(),z?ei(e):eo(e);return;case"ArrowRight":e.preventDefault(),z?eo(e):ei(e);return}null==$||$(e)},[C,$,eo,ei,z]),eb=(0,f.useCallback)(e=>{"hover"===A&&ee(!0),null==W||W(e)},[A,W]),em=(0,f.useCallback)(e=>{ee(!1),null==S||S(e)},[S]),ej=(0,f.useRef)(0),eO=(0,f.useRef)(0),eP=(0,o.default)(),e_=(0,f.useCallback)(e=>{ej.current=e.touches[0].clientX,eO.current=0,"hover"===A&&ee(!0),null==I||I(e)},[A,I]),eg=(0,f.useCallback)(e=>{e.touches&&e.touches.length>1?eO.current=0:eO.current=e.touches[0].clientX-ej.current,null==B||B(e)},[B]),ex=(0,f.useCallback)(e=>{if(R){let t=eO.current;Math.abs(t)>40&&(t>0?eo(e):ei(e))}"hover"===A&&eP.set(()=>{ee(!1)},E||void 0),null==G||G(e)},[R,A,eo,ei,eP,E,G]),ew=null!=E&&!Z&&!et,eM=(0,f.useRef)();(0,f.useEffect)(()=>{var e,t;if(!ew)return;let r=z?eo:ei;return eM.current=window.setInterval(document.visibilityState?ef:r,null!=(e=null!=(t=eu.current)?t:E)?e:void 0),()=>{null!==eM.current&&clearInterval(eM.current)}},[ew,eo,ei,eu,E,ef,z]);let ek=(0,f.useMemo)(()=>g&&Array.from({length:ea},(e,t)=>e=>{null==M||M(t,e)}),[g,ea,M]);return(0,j.jsxs)(d,{ref:es,...K,onKeyDown:eh,onMouseOver:eb,onMouseOut:em,onTouchStart:e_,onTouchMove:eg,onTouchEnd:ex,className:(0,s.default)(F,q,O&&"slide",P&&`${q}-fade`,X&&`${q}-${X}`),children:[g&&(0,j.jsx)("div",{className:`${q}-indicators`,children:(0,v.map)(H,(e,t)=>(0,j.jsx)("button",{type:"button","data-bs-target":"","aria-label":null!=x&&x.length?x[t]:`Slide ${t+1}`,className:t===en?"active":void 0,onClick:ek?ek[t]:void 0,"aria-current":t===en},t))}),(0,j.jsx)("div",{className:`${q}-inner`,children:(0,v.map)(H,(e,t)=>{let r=t===en;return O?(0,j.jsx)(m.default,{in:r,onEnter:r?ev:void 0,onEntered:r?ey:void 0,addEndListener:h.default,children:(t,n)=>f.cloneElement(e,{...n,className:(0,s.default)(e.props.className,r&&"entered"!==t&&ed,("entered"===t||"exiting"===t)&&"active",("entering"===t||"exiting"===t)&&ep)})}):f.cloneElement(e,{className:(0,s.default)(e.props.className,r&&"active")})})}),_&&(0,j.jsxs)(j.Fragment,{children:[(D||0!==w)&&(0,j.jsxs)(i.default,{className:`${q}-control-prev`,onClick:eo,children:[T,L&&(0,j.jsx)("span",{className:"visually-hidden",children:L})]}),(D||w!==ea-1)&&(0,j.jsxs)(i.default,{className:`${q}-control-next`,onClick:ei,children:[U,V&&(0,j.jsx)("span",{className:"visually-hidden",children:V})]})]})]})});P.displayName="Carousel",t.default=Object.assign(P,{Caption:d.default,Item:p.default}),e.exports=t.default},15931:(e,t,r)=>{var n=r(92921);t.__esModule=!0,t.default=void 0;var l=n(r(3892)),a=function(e,t){if(e&&e.__esModule)return e;if(null===e||"object"!=typeof e&&"function"!=typeof e)return{default:e};var r=i(t);if(r&&r.has(e))return r.get(e);var n={__proto__:null},l=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var a in e)if("default"!==a&&Object.prototype.hasOwnProperty.call(e,a)){var u=l?Object.getOwnPropertyDescriptor(e,a):null;u&&(u.get||u.set)?Object.defineProperty(n,a,u):n[a]=e[a]}return n.default=e,r&&r.set(e,n),n}(r(82015)),u=r(11940),o=r(8732);function i(e){if("function"!=typeof WeakMap)return null;var t=new WeakMap,r=new WeakMap;return(i=function(e){return e?r:t})(e)}let s=a.forwardRef(({as:e="div",bsPrefix:t,className:r,...n},a)=>{let i=(0,l.default)(r,(0,u.useBootstrapPrefix)(t,"carousel-item"));return(0,o.jsx)(e,{ref:a,...n,className:i})});s.displayName="CarouselItem",t.default=s,e.exports=t.default},20174:(e,t,r)=>{var n=r(92921);t.Ay=void 0;var l=n(r(3892)),a=function(e,t){if(e&&e.__esModule)return e;if(null===e||"object"!=typeof e&&"function"!=typeof e)return{default:e};var r=i(t);if(r&&r.has(e))return r.get(e);var n={__proto__:null},l=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var a in e)if("default"!==a&&Object.prototype.hasOwnProperty.call(e,a)){var u=l?Object.getOwnPropertyDescriptor(e,a):null;u&&(u.get||u.set)?Object.defineProperty(n,a,u):n[a]=e[a]}return n.default=e,r&&r.set(e,n),n}(r(82015)),u=r(11940),o=r(8732);function i(e){if("function"!=typeof WeakMap)return null;var t=new WeakMap,r=new WeakMap;return(i=function(e){return e?r:t})(e)}let s=a.forwardRef((e,t)=>{let[{className:r,...n},{as:a="div",bsPrefix:i,spans:s}]=function({as:e,bsPrefix:t,className:r,...n}){t=(0,u.useBootstrapPrefix)(t,"col");let a=(0,u.useBootstrapBreakpoints)(),o=(0,u.useBootstrapMinBreakpoint)(),i=[],s=[];return a.forEach(e=>{let r,l,a,u=n[e];delete n[e],"object"==typeof u&&null!=u?{span:r,offset:l,order:a}=u:r=u;let f=e!==o?`-${e}`:"";r&&i.push(!0===r?`${t}${f}`:`${t}${f}-${r}`),null!=a&&s.push(`order${f}-${a}`),null!=l&&s.push(`offset${f}-${l}`)}),[{...n,className:(0,l.default)(r,...i,...s)},{as:e,bsPrefix:t,spans:i}]}(e);return(0,o.jsx)(a,{...n,ref:t,className:(0,l.default)(r,!s.length&&i)})});s.displayName="Col",t.Ay=s},80237:(e,t)=>{Object.defineProperty(t,"A",{enumerable:!0,get:function(){return r}});var r=function(e){return e.PAGES="PAGES",e.PAGES_API="PAGES_API",e.APP_PAGE="APP_PAGE",e.APP_ROUTE="APP_ROUTE",e.IMAGE="IMAGE",e}({})},81413:(e,t)=>{Object.defineProperty(t,"M",{enumerable:!0,get:function(){return function e(t,r){return r in t?t[r]:"then"in t&&"function"==typeof t.then?t.then(t=>e(t,r)):"function"==typeof t&&"default"===r?t:void 0}}})},90616:(e,t,r)=>{var n=r(92921);t.__esModule=!0,t.default=void 0;var l=function(e,t){if(e&&e.__esModule)return e;if(null===e||"object"!=typeof e&&"function"!=typeof e)return{default:e};var r=i(t);if(r&&r.has(e))return r.get(e);var n={__proto__:null},l=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var a in e)if("default"!==a&&Object.prototype.hasOwnProperty.call(e,a)){var u=l?Object.getOwnPropertyDescriptor(e,a):null;u&&(u.get||u.set)?Object.defineProperty(n,a,u):n[a]=e[a]}return n.default=e,r&&r.set(e,n),n}(r(82015)),a=n(r(3892)),u=r(11940),o=r(8732);function i(e){if("function"!=typeof WeakMap)return null;var t=new WeakMap,r=new WeakMap;return(i=function(e){return e?r:t})(e)}let s=l.forwardRef(({className:e,bsPrefix:t,as:r="div",...n},l)=>(t=(0,u.useBootstrapPrefix)(t,"carousel-caption"),(0,o.jsx)(r,{ref:l,className:(0,a.default)(e,t),...n})));s.displayName="CarouselCaption",t.default=s,e.exports=t.default},91417:(e,t,r)=>{t.__esModule=!0,t.forEach=function(e,t){let r=0;n.Children.forEach(e,e=>{n.isValidElement(e)&&t(e,r++)})},t.hasChildOfType=function(e,t){return n.Children.toArray(e).some(e=>n.isValidElement(e)&&e.type===t)},t.map=function(e,t){let r=0;return n.Children.map(e,e=>n.isValidElement(e)?t(e,r++):e)};var n=function(e,t){if(e&&e.__esModule)return e;if(null===e||"object"!=typeof e&&"function"!=typeof e)return{default:e};var r=l(t);if(r&&r.has(e))return r.get(e);var n={__proto__:null},a=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var u in e)if("default"!==u&&Object.prototype.hasOwnProperty.call(e,u)){var o=a?Object.getOwnPropertyDescriptor(e,u):null;o&&(o.get||o.set)?Object.defineProperty(n,u,o):n[u]=e[u]}return n.default=e,r&&r.set(e,n),n}(r(82015));function l(e){if("function"!=typeof WeakMap)return null;var t=new WeakMap,r=new WeakMap;return(l=function(e){return e?r:t})(e)}}};