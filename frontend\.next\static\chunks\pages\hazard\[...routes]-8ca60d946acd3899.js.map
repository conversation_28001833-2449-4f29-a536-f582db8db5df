{"version": 3, "file": "static/chunks/pages/hazard/[...routes]-8ca60d946acd3899.js", "mappings": "kLA2BA,MApBe,KAEb,IAAMA,EADSC,CAAAA,EAAAA,EAAAA,SAAAA,CAASA,EAmBXC,CAlBaC,KAAK,CAACH,CAkBZ,KAlBkB,EAAI,EAAE,OAE5C,SAAQA,CAAM,CAAC,EAAE,CAEH,UAACI,EAAAA,OAAUA,CAAAA,CAACJ,OAAQA,IAEvB,IAEb,mBChBA,4CACA,sBACA,WACA,OAAe,EAAQ,KAA2C,CAClE,EACA,SAFsB", "sources": ["webpack://_N_E/./pages/hazard/[...routes].tsx", "webpack://_N_E/?f0c0"], "sourcesContent": ["//Import Library\r\nimport { useRouter } from 'next/router';\r\n\r\n//Import services/components\r\nimport HazardShow from './HazardShow';\r\nimport { serverSideTranslations } from 'next-i18next/serverSideTranslations';\r\n\r\nconst Router = () => {\r\n  const router = useRouter()\r\n  const routes:any = router.query.routes || []\r\n\r\n  switch (routes[0]) {\r\n    case 'show':\r\n        return (<HazardShow routes={routes} />)\r\n    default:\r\n      return null;\r\n  }\r\n}\r\n\r\nexport async function getServerSideProps({ locale }: { locale: string }) {\r\n  return {\r\n    props: {\r\n      ...(await serverSideTranslations(locale, ['common'])),\r\n    },\r\n  }\r\n}\r\n\r\nexport default Router;\r\n", "\n    (window.__NEXT_P = window.__NEXT_P || []).push([\n      \"/hazard/[...routes]\",\n      function () {\n        return require(\"private-next-pages/hazard/[...routes].tsx\");\n      }\n    ]);\n    if(module.hot) {\n      module.hot.dispose(function () {\n        window.__NEXT_P.push([\"/hazard/[...routes]\"])\n      });\n    }\n  "], "names": ["routes", "useRouter", "Router", "query", "HazardShow"], "sourceRoot": "", "ignoreList": []}