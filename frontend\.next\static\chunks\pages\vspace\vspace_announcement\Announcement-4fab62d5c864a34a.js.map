{"version": 3, "file": "static/chunks/pages/vspace/vspace_announcement/Announcement-4fab62d5c864a34a.js", "mappings": "uIAgCA,MAVA,cACA,MAAkB,YAAM,IASM,CAR5B,eAAS,MACX,cACA,aACA,MACA,CACA,UACA,CAAG,GACH,4FCzBA,IAAMA,EAA+BC,EAAAA,UAAgB,CAAC,GAA9B,QAA+B,GAApB,QACjCC,CAAS,UACTC,CAAQ,CACRC,GAAIC,EAAY,KAAK,CACrB,GAAGC,EACJ,GAEC,OAAO,EADIC,CAAAA,EAAAA,EAAAA,EAAAA,CAAkBA,CAACJ,EAAU,oBACpBK,CAAAA,EAAAA,EAAAA,GAAAA,CAAIA,CAACH,EAAW,CAClCI,IAAKA,EACLP,UAAWQ,IAAWR,EAAWC,GACjC,GAAGG,CAAK,EAEZ,GACAN,EAAgBW,WAAW,CAAG,kBCb9B,IAAMC,EAA4BX,EAAAA,UAAgB,CAA7B,CAA8B,EAMhDQ,QAN6B,CAE9BL,CADA,EACIC,EAAY,KAAK,UACrBF,CAAQ,WACRD,CAAS,CACT,GAAGI,EACJ,GACOO,EAAiBH,IAAWR,EAAWK,CAAAA,EAAAA,EAAAA,EAAAA,CAAkBA,CAACJ,EAAU,GAAzCO,eACjC,MAAoBF,CAAb,EAAaA,EAAAA,GAAAA,CAAIA,CAACH,EAAP,CAChBI,IAAKA,EACL,GAAGH,CAAK,CACRJ,UAAWW,CACb,EACF,GACAD,EAAaD,WAAW,CAAG,iBAbkI,8CCsB7J,IAAMG,EAGNb,EAAAA,OAFA,GAEgB,CAAC,GAGdQ,IALQ,GACX,EA2EMM,EA1EY,oBAChBC,EAAqB,CAAC,CACtB,GAAGC,EACJ,GACO,CAEJb,CADA,EACIC,EAAY,IAP0B,CAOrB,UACrBF,CAAQ,OACRe,GAAQ,CAAI,MACZC,GAAO,CAAK,UACZC,EAAW,EAAI,YACfC,GAAa,CAAI,iBACjBC,EAAkB,EAAE,aACpBC,CAAW,UACXC,CAAQ,SACRC,CAAO,QACPC,CAAM,CACNC,WAAW,GAAI,IAZ4I,MAa3JC,GAAW,CAAI,WACfC,CAAS,OACTC,EAAQ,OAAO,aACfC,CAAW,YACXC,CAAU,MACVC,GAAO,CAAI,OACXC,GAAQ,CAAI,cACZC,CAAY,aACZC,CAAW,YACXC,CAAU,UACVC,EAAwB9B,CAAAA,EAAAA,EAAAA,GAAAA,CAAb,CAAkB,OAAQ,CACnC,EADoB,YACL,OACfN,UAAW,4BACb,EAAE,WACFqC,EAAY,UAAU,CACtBC,WAAW,CAAahC,EAAAA,EAAAA,GAAAA,CAAIA,CAAC,CAAP,MAAe,CACnC,cAAe,OACfN,UAAW,4BACb,EAAE,WACFuC,EAAY,MAAM,SAClBC,CAAO,WACPxC,CAAS,UACTyC,CAAQ,CACR,GAAGrC,EACJ,CAAGsC,CAAAA,EAAAA,EAAAA,EAAAA,CAAeA,CAAC,oBAClB5B,EACA,GAAGC,CAAiB,EACnB,CACDM,YAAa,UACf,GACMsB,EAAStC,CAAAA,EAAAA,EAAAA,EAAAA,CAAkBA,CAACJ,EAAU,YACtC2C,EAAQC,CAAAA,EAAAA,EAAAA,EAAAA,CAAQA,GAChBC,EAAmBC,CAAAA,EAAAA,EAAAA,MAAAA,CAAMA,CAAC,MAC1B,CAACC,EAAWC,EAAa,CAAGC,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAC,QACrC,CAACC,GAAQC,GAAU,CAAGF,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,EAAC,GAC/B,CAACG,GAAWC,GAAa,CAAGJ,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,EAAC,GACrC,CAACK,GAAqBC,GAAuB,CAAGN,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAC7B,GAAe,GAC9EoC,CAAAA,EAAAA,EAAAA,SAAAA,CAASA,CAAC,KACHJ,IAAahC,IAAgBkC,KAC5BT,EAAiBY,OAAO,CAC1BT,CAD4B,CACfH,EAAiBY,EAFqB,KAEd,EAErCT,EAAa,CAAC5B,IAAe,EAAKkC,GAAsB,OAAS,QAE/DvC,GACFsC,IADS,GAGXE,GAAuBnC,GAAe,GAE1C,EAAG,CAACA,EAAagC,GAAWE,GAAqBvC,EAAM,EACvDyC,CAAAA,EAAAA,EAAAA,SAAAA,CAASA,CAAC,KACJX,EAAiBY,OAAO,EAAE,CAC5BZ,EAAiBY,OAAO,CAAG,KAE/B,GACA,IAAIC,GAAc,EAKlBC,CAAAA,EAAAA,EAAAA,EAAAA,CAAOA,CAACnB,EAAU,CAACoB,EAAOC,KACxB,EAAEH,GACEG,IAAUzC,IACZR,EAAsBgD,EAAMzD,KAAK,CAACqB,QAAAA,CAEtC,GACA,IAAMsC,GAAyBC,CAAAA,EAAAA,EAAAA,CAAAA,CAAeA,CAACnD,GACzCoD,GAAOC,CAAAA,EAAAA,EAAAA,WAAAA,CAAWA,CAACC,IACvB,GAAId,GACF,OAEF,CAHe,GAGXe,EAAkBb,GAAsB,EAC5C,GAAIa,EAAkB,EAAG,CACvB,GAAI,CAACrC,EACH,IADS,GAGXqC,EAAkBT,GAAc,CAClC,CACAb,EAAiBY,OAAO,CAAG,OACf,MAAZpC,GAAoBA,EAAS8C,EAAiBD,EAChD,EAAG,CAACd,GAAWE,GAAqBjC,EAAUS,EAAM4B,GAAY,EAG1DU,GAAOC,CAAAA,EAAAA,EAAAA,CAAAA,CAAgBA,CAACH,IAC5B,GAAId,GACF,OAEF,CAHe,GAGXe,EAAkBb,GAAsB,EAC5C,GAAIa,GAAmBT,GAAa,CAClC,GAAI,CAAC5B,EACH,IADS,GAGXqC,EAAkB,CACpB,CACAtB,EAAiBY,OAAO,CAAG,OACf,MAAZpC,GAAoBA,EAAS8C,EAAiBD,EAChD,GACMI,GAAaxB,CAAAA,EAAAA,EAAAA,MAAAA,CAAMA,GACzByB,CAAAA,EAAAA,EAAAA,mBAAAA,CAAmBA,CAACjE,EAAK,IAAO,EAC9BkE,QAASF,GAAWb,OAAO,MAC3BO,QACAI,GACF,GAGA,IAAMK,GAAkBJ,CAAAA,EAAAA,EAAAA,CAAAA,CAAgBA,CAAC,KACnC,CAACK,SAASC,MAAM,EAtIxB,SAAmBH,CAAO,EACxB,GAAI,CAACA,GAAW,CAACA,EAAQI,KAAK,EAAI,CAACJ,EAAQK,UAAU,EAAI,CAACL,EAAQK,UAAU,CAACD,KAAK,CAChF,CADkF,MAC3E,EAET,IAAME,EAAeC,iBAAiBP,GACtC,MAAgC,SAAzBM,EAAaE,OAAO,EAA2C,WAA5BF,EAAaG,UAAU,EAAkE,SAAjDF,iBAAiBP,EAAQK,UAAU,EAAEG,OAAO,EAiI1FV,GAAWb,OAAO,GAAG,CACjDd,EACFqB,KADS,KAMf,GACMkB,GAAiBnC,WAAuB,QAAU,MACxDoC,EAAgB,KACVpE,IAIO,GAJA,GAIXO,EALa6D,CAKM7D,EAAQgC,GAAqB4B,IACtC,MAAV3D,GAAkBA,EAAO+B,GAAqB4B,IAChD,EAAG,CAAC5B,GAAoB,EACxB,IAAM8B,GAAiB,GAAkBrC,MAAAA,CAAfL,EAAO,UAAkB,OAAVK,GACnCsC,GAAuB,GAAkBH,MAAAA,CAAfxC,EAAO,UAAuB,OAAfwC,IACzCI,GAAcrB,CAAAA,EAAAA,EAAAA,WAAAA,CAAWA,CAACsB,IAC9BC,CAAAA,EAAAA,EAAAA,CAAAA,CAAoBA,CAACD,GACV,MAAXjE,GAAmBA,EAAQgC,GAAqB4B,GAClD,EAAG,CAAC5D,EAASgC,GAAqB4B,GAAe,EAC3CO,GAAgBxB,CAAAA,EAAAA,EAAAA,WAAAA,CAAWA,CAAC,KAChCZ,IAAa,GACH,MAAV9B,GAAkBA,EAAO+B,GAAqB4B,GAChD,EAAG,CAAC3D,EAAQ+B,GAAqB4B,GAAe,EAC1CQ,GAAgBzB,CAAAA,EAAAA,EAAAA,WAAAA,CAAWA,CAACC,IAChC,GAAIzC,GAAY,CAAC,kBAAkBkE,IAAI,CAACzB,EAAM0B,MAAM,CAACC,OAAO,EAC1D,CAD6D,MACrD3B,EAAM4B,GAAG,EACf,IAAK,YACH5B,EAAM6B,cAAc,GAChBpD,EACFyB,GAAKF,EADI,CAGTF,GAAKE,GAEP,MACF,KAAK,aACHA,EAAM6B,cAAc,GAChBpD,EACFqB,GAAKE,EADI,CAGTE,GAAKF,GAEP,MAEJ,CAEW,MAAbxC,GAAqBA,EAAUwC,EACjC,EAAG,CAACzC,EAAUC,EAAWsC,GAAMI,GAAMzB,EAAM,EACrCqD,GAAkB/B,CAAAA,EAAAA,EAAAA,WAAAA,CAAWA,CAACC,IACpB,SAAS,CAAnBvC,GACFwB,IAAU,GAEG,MAAfvB,GAAuBA,EAAYsC,EACrC,EAAG,CAACvC,EAAOC,EAAY,EACjBqE,GAAiBhC,CAAAA,EAAAA,EAAAA,WAAAA,CAAWA,CAACC,IACjCf,IAAU,GACI,MAAdtB,GAAsBA,EAAWqC,EACnC,EAAG,CAACrC,EAAW,EACTqE,GAAiBpD,CAAAA,EAAAA,EAAAA,MAAAA,CAAMA,CAAC,GACxBqD,GAAiBrD,CAAAA,EAAAA,EAAAA,MAAAA,CAAMA,CAAC,GACxBsD,GAAsBC,CAAAA,EAAAA,EAAAA,CAAAA,CAAUA,GAChCC,GAAmBrC,CAAAA,EAAAA,EAAAA,WAAAA,CAAWA,CAACC,IACnCgC,GAAezC,OAAO,CAAGS,EAAMqC,OAAO,CAAC,EAAE,CAACC,OAAO,CACjDL,GAAe1C,OAAO,CAAG,EACX,SAAS,CAAnB9B,GACFwB,IAAU,GAEI,MAAhBnB,GAAwBA,EAAakC,EACvC,EAAG,CAACvC,EAAOK,EAAa,EAClByE,GAAkBxC,CAAAA,EAAAA,EAAAA,WAAAA,CAAWA,CAACC,IAC9BA,EAAMqC,OAAO,EAAIrC,EAAMqC,OAAO,CAACG,MAAM,CAAG,EAC1CP,CAD6C,EAC9B1C,OAAO,CAAG,EAEzB0C,GAAe1C,OAAO,CAAGS,EAAMqC,OAAO,CAAC,EAAE,CAACC,OAAO,CAAGN,GAAezC,OAAO,CAE7D,MAAfxB,GAAuBA,EAAYiC,EACrC,EAAG,CAACjC,EAAY,EACV0E,GAAiB1C,CAAAA,EAAAA,EAAAA,WAAAA,CAAWA,CAACC,IACjC,GAAInC,EAAO,CACT,IAAM6E,EAAcT,GAAe1C,OAAO,CACtCoD,KAAKC,GAAG,CAACF,GA1NK,KA2NZA,EAAc,EAChB5C,CADmB,EADK+C,GAIxB3C,GAAKF,GAGX,CACIvC,OAR2C,EAQxB,IACrByE,GAAoBY,GAAG,CAAC,KACtB7D,IAAU,EACZ,EAAG3B,QAAYyF,GAEH,MAAd/E,GAAsBA,EAAWgC,EACnC,EAAG,CAACnC,EAAOJ,EAAOqC,GAAMI,GAAMgC,GAAqB5E,EAAUU,EAAW,EAClEgF,GAAyB,MAAZ1F,GAAoB,CAAC0B,IAAU,CAACE,GAC7C+D,GAAoBrE,CAAAA,EAAAA,EAAAA,MAAAA,CAAMA,GAChCU,CAAAA,EAAAA,EAAAA,SAAAA,CAASA,CAAC,KACR,IAAI4D,EAAMC,EACV,GAAI,CAACH,GACH,OAAOD,EADQ,EAGXK,EAAW3E,EAAQqB,GAAOI,GAEhC,OADA+C,GAAkB1D,OAAO,CAAG8D,OAAOC,WAAW,CAAC9C,SAAS+C,eAAe,CAAGhD,GAAkB6C,EAAU,OAACF,EAAO,OAACC,EAAwBvD,GAAuBL,OAAAA,EAAmB4D,EAAwB7F,CAAAA,CAAO,CAAa4F,OAAOH,GAC7N,KACDE,MAAoC,IAAlB1D,OAAO,EAC3BiE,cAAcP,GAAkB1D,OAAO,CAE3C,CACF,EAAG,CAACyD,GAAYlD,GAAMI,GAAMN,GAAwBtC,EAAUiD,GAAiB9B,EAAM,EACrF,IAAMgF,GAAoBC,CAAAA,EAAAA,EAAAA,OAAAA,CAAOA,CAAC,IAAM1G,GAAc2G,MAAMC,IAAI,CAAC,CAC/DpB,OAAQhD,EACV,EAAG,CAACqE,EAAGlE,IAAUK,IACH,MAAZ7C,GAAoBA,EAASwC,EAAOK,EACtC,GAAI,CAAChD,EAAYwC,GAAarC,EAAS,EACvC,MAAoB2G,CAAb,EAAaA,EAAAA,IAAAA,CAAKA,CAAC9H,CAAR,CAAmB,CACnCI,IAAKgE,GACL,GAAGnE,CAAK,CACRuB,UAAWgE,GACX9D,YAAaoE,GACbnE,WAAYoE,GACZjE,aAAcsE,GACdrE,YAAawE,GACbvE,WAAYyE,GACZ5G,UAAWQ,IAAWR,EAAW2C,EAAQ3B,GAAS,QAASC,CAAtCT,EAA8C,GAAU,OAAPmC,EAAO,SAAQH,GAAW,GAAaA,MAAAA,CAAVG,EAAO,KAAW,OAARH,IAC7GC,SAAU,CAACtB,GAA2Bb,CAAAA,EAAAA,EAAAA,GAAAA,CAAIA,CAAC,CAAlB,KAAyB,CAChDN,KADkC,KACvB,GAAU,OAAP2C,EAAO,eACrBF,SAAUyF,CAAAA,EAAAA,EAAAA,EAAAA,CAAGA,CAACzF,EAAU,CAACuF,EAAGlE,IAAuBxD,CAAAA,EAAAA,CAAb,CAAaA,GAAAA,CAAIA,CAAC,KAAP,IAAiB,CAChE6H,KAAM,SACN,iBAAkB,GAAG,aAEY,MAAnB/G,GAA2BA,EAAgBuF,MAAM,CAAGvF,CAAe,CAAC0C,EAAM,CAAG,IAF9B,KAEiD,OAAVA,EAAQ,GAC5G9D,UAAW8D,IAAUP,GAAsB,cAAW2D,EACtDkB,QAASR,GAAoBA,EAAiB,CAAC9D,EAAM,MAAGoD,EACxD,eAAgBpD,IAAUP,EAC5B,EAAGO,GACL,GAAiBxD,CAAAA,EAAAA,EAAAA,GAAAA,CAAIA,CAAC,MAAO,CAC3BN,UAAW,GAAU,OAAP2C,EAAO,UACrBF,SAAUyF,CAAAA,EAAAA,EAAAA,EAAAA,CAAGA,CAACzF,EAAU,CAACoB,EAAOC,KAC9B,IAAMuE,EAAWvE,IAAUP,GAC3B,OAAOvC,EAAqBV,CAAAA,EAAAA,EAAAA,CAAb,EAAaA,CAAIA,CAACgI,EAAAA,CAAiBA,CAAE,CAClDC,EADwB,CACpBF,EACJG,QAASH,EAAW9C,QAAc2B,EAClCuB,UAAWJ,EAAW3C,QAAgBwB,EACtCwB,eAAgBC,EAAAA,CAAqBA,CACrClG,SAAU,CAACmG,EAAQC,IAA4B9I,EAAAA,OAAb,KAA+B,CAAC8D,EAAO,CACvE,EAD2C,CACxCgF,CAAU,CACb7I,UAAWQ,IAAWqD,EAAMzD,KAAK,CAACJ,QAAbQ,CAAsB,CAAE6H,GAAuB,YAAXO,GAAwBvD,GAAgB,CAAY,YAAXuD,GAAmC,YAAXA,CAAW,CAAQ,EAAM,SAAU,CAAY,aAAXA,GAAoC,YAAXA,CAAW,CAAQ,EAAMtD,GAClN,EACF,GAAoBvF,EAAb,WAAW,CAAoB,CAAC8D,EAAO,CAC5C7D,UAAWQ,IAAWqD,EAAMzD,KAAK,CAACJ,QAAbQ,CAAsB,CAAE6H,GAAY,SAC3D,EACF,EACF,GAAInH,GAAyB+G,CAAAA,EAAAA,EAAAA,IAAb,CAAkBA,CAACa,EAAAA,OAAR,CAAiBA,CAAE,CAC5CrG,SAAU,CAAEV,IAAQV,MAAsB,EAAa4G,CAAAA,EAAAA,EAAAA,IAAF,CAAOA,CAACc,EAAAA,CAAMA,CAAE,CACnE/I,UAAW,GAAU,OAAP2C,EAAO,iBACrByF,QAASnE,GACTxB,SAAU,CAACL,EAAUC,GAA0B/B,CAAAA,EAAAA,EAAAA,GAAAA,CAAIA,CAAjB,OAA0B,CAC1DN,GAD2C,OAChC,kBACXyC,SAAUJ,CACZ,GAAG,GACAN,IAAQV,IAAgBsC,IAAc,GAAmBsE,CAAAA,EAAb,EAAaA,IAAAA,CAAKA,CAACc,EAAAA,CAAR,CAAgB,CAC1E/I,UAAW,GAAU,OAAP2C,EAAO,iBACrByF,QAAS/D,GACT5B,SAAU,CAACH,EAAUC,GAA0BjC,CAAAA,EAAAA,EAAAA,GAAAA,CAAIA,CAAjB,OAA0B,CAC1DN,GAD2C,OAChC,kBACXyC,SAAUF,CACZ,GAAG,GACF,GACF,EAEP,GACA3B,EAASH,WAAW,CAAG,WACvB,MAAeuI,OAAOC,MAAM,CAACrI,EAAU,CACrCsI,QFzTapJ,CEyTJA,CACTqJ,KDzTazI,CCyTPA,EACN,EAAC,GF3T2BZ,EAAC,ECCJY,CCwTDZ,CDxTE,GCyTRY,qLC3TpB,SAAS0I,EAAuBhJ,CAAkC,EAChE,GAAM,eAAEiJ,CAAa,CAAE,CAAGjJ,EAC1B,MACE,UAACkJ,MAAAA,UACED,EAAcnB,GAAG,CAAC,CAACqB,EAAMzF,IAEtB,UAAC0F,EAAAA,CAAGA,CAAAA,CAACxJ,UAAU,4BACb,UAACyJ,EAAAA,OAAgBA,CAAAA,CAACF,KAAMA,KADazF,KAOjD,CAwHA,MAtHA,SAAS4F,EACP,GAAM,GAAEC,CAAC,CAAE,CAAGC,CAAAA,EAAAA,EAAAA,EAAAA,CAAcA,CAAC,UAEvBC,CAmHmBH,CApHVI,CAAAA,EAAAA,EAAAA,SAAAA,CAASA,GACGC,KAAK,CAACF,MAAM,EAAI,EAAE,CACvC,CAACR,EAAeW,EAAiB,CAAG9G,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAU,EAAE,EAExD,CAAC+G,EAAQC,EAAU,CAAGhH,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAC,GAE/B,CAACiH,EAAkB,CAAGjH,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAC,GAE/BkH,EAAiB,KACrBJ,EAAiB,EAAE,CACrB,EAEMK,EAAgB,CACpBN,MAAO,CAAEO,sBAAsB,EAAMC,cAAeV,CAAM,CAAC,EAAE,EAC7DW,KAAM,CAAEC,WAAY,MAAO,EAC3BC,MAAO,IACPC,OAAQ,2GACV,EAEMC,EAAqB,qBAAOC,EAAAA,UAAAA,MAAAA,CAAAA,GAAAA,KAAAA,IAAAA,SAAAA,CAAAA,EAAAA,CAAAA,SAAAA,CAAAA,EAAAA,CAASR,EACnCS,EAAW,MAAMC,EAAAA,CAAUA,CAACC,GAAG,CAAC,WAAYH,GAC9CC,GAAYA,EAASG,IAAI,EAAIH,EAASG,IAAI,CAACtE,MAAM,CAAG,EAEtDqD,CAFyD,CACvChC,IAAAA,KAAO,CAAC8C,EAASG,GAClBC,CADsB,CAAE,IAGzCd,GAEJ,EAEA3G,CAAAA,EAAAA,EAAAA,SAAAA,CAASA,CAAC,KACRmH,GACF,EAAG,EAAE,EAEL,IAAMO,EAAiB,IACrB,IAAIrH,EAAQmG,EACN,CAACmB,EAAKC,EAAI,CAAG,CAAC,EAAGlB,EAAoB,EAAE,CAE3B,QAAQ,CAAtBnH,EACFc,IAEqB,QAAQ,CAAtBd,GACPc,IAGEA,EAAQuH,IACVvH,CADe,CACP,GAGNA,EAAQsH,IACVtH,CADe,CACPuH,CAAAA,EAIN,EAAe1E,MAAM,CAAG7C,GAAW,GAAG,CACxCA,EAAQuF,EAAc1C,MAAM,EAAG,EAG7B,EAAeA,MAAM,CAAG7C,GAAW,GAAG,CACxCA,GAAQ,EAEVoG,EAAUpG,EACZ,EAEA,MACE,UAACwF,MAAAA,CAAItJ,UAAU,8BACZqJ,GAAiBA,EAAc1C,MAAM,CAAG,EACvC,iCACE,UAAC2E,EAAAA,CAASA,CAAAA,CAACC,KAAK,aACd,WAAC/B,EAAAA,CAAGA,CAAAA,WACF,UAACgC,EAAAA,CAAGA,CAAAA,CAACC,GAAI,GAAIzL,UAAU,QAEtBqJ,GAAiBA,EAAc1C,MAAM,CAAG,EACvC,UAAC6E,EAAAA,CAAGA,CAAAA,CAACC,GAAI,EAAGzL,UAAU,yCACpB,WAACsJ,MAAAA,CAAItJ,UAAU,gCACb,UAAC0L,IAAAA,CAAE1L,UAAU,wBAAwBoI,QAAS,IAAM+C,EAAe,iBACjE,UAACQ,IAAAA,CAAE3L,UAAU,yBAEf,UAAC0L,IAAAA,CAAE1L,UAAU,yBAAyBoI,QAAS,IAAM+C,EAAe,iBAClE,UAACQ,IAAAA,CAAE3L,UAAU,+BAIjB,UAGR,UAACsL,EAAAA,CAASA,CAAAA,CAACC,KAAK,aACd,UAAC/B,EAAAA,CAAGA,CAAAA,UACF,UAACgC,EAAAA,CAAGA,CAAAA,CAACC,GAAI,GAAIzL,UAAU,eACrB,UAACY,EAAAA,CAAQA,CAAAA,CAACO,YAAY,EAAOD,UAAU,EAAOO,SAAU,KAAMJ,YAAa4I,WACxEZ,EAAcnB,GAAG,CAAC,CAACqB,EAAMzF,IAEtB,UAAClD,EAAAA,CAAQA,CAACuI,IAAI,WACZ,UAACC,EAAAA,CAAuBC,cAAeE,KADrBzF,eAWhC,UAACwH,EAAAA,CAASA,CAAAA,CAACC,OAAO,WAChB,UAAC/B,EAAAA,CAAGA,CAAAA,UACF,UAACgC,EAAAA,CAAGA,CAAAA,CAACC,GAAI,GAAIzL,UAAU,eACrB,UAACsJ,MAAAA,CAAItJ,UAAU,kCACf,UAAC4L,IAAAA,CAAE5L,UAAU,wDAAgD2J,EAAE,mCAQ/E,mBCrJA,4CACA,2CACA,WACA,OAAe,EAAQ,KAAgE,CACvF,EACA,SAFsB,mGCGP,SAASF,EAAiBrJ,CAAU,MAqC9BmJ,IAnCnB,GAAM,MAAEA,CAAI,CAAE,CAAGnJ,EASjB,MACE,WAACoL,EAAAA,CAAGA,CAAAA,CAACxL,UAAU,MAAMyL,GAAI,aACvB,UAACI,IAAIA,CAACC,KAAM,IAAc,OAAVvC,EAAKpB,IAAI,CAAC,gBAAejI,GAAI,EAAxC2L,EAA8DtC,MAAAA,CAAlBA,EAAKpB,IAAI,CAAC,UAA0CoB,MAAAA,CAAlCA,CAAI,CAACwC,EAAYxC,EAyBjF,UAAoB,OAAVA,EAAKpB,IAAI,EAzBoE,CAAC,YAAmB,OAAToB,EAAKyC,GAAG,WAE1G,EAAMC,MAAM,EAAI1C,EAAK0C,MAAM,CAAC,EAAE,CAC7B,UAACC,MAAAA,CAAIC,IAAK,GAAwC5C,MAAAA,CAArC6C,8BAAsB,CAAC,gBAAiC,OAAnB7C,EAAK0C,MAAM,CAAC,EAAE,CAACD,GAAG,EAAIK,IAAI,eAC1ErM,UAAU,gBACV,UAAC2L,IAAAA,CAAE3L,UAAU,iCAGnB,WAACsJ,MAAAA,CAAItJ,UAAU,yBACb,UAAC6L,IAAIA,CAACC,KAAM,IAAc,OAAVvC,EAAKpB,IAAI,CAAC,gBAAejI,GAAI,EAAxC2L,EAA8DtC,MAAAA,CAAlBA,EAAKpB,IAAI,CAAC,UAA0CoB,MAAAA,CAAlCA,CAAI,CAAC+C,EAAY/C,EAYnF,UAAoB,OAAVA,EAAKpB,IAAI,EAZsE,CAAC,YAAmB,OAAToB,EAAKyC,GAAG,WAC1GzC,GAAQA,EAAKgD,KAAK,CAAGhD,EAAKgD,KAAK,CAAG,KAErC,UAACX,IAAAA,UACErC,GAAQA,EAAKiD,WAAW,CAAGC,CAtBX,IACvB,IAAMnD,EAAM3E,SAAS+H,aAAa,CAAC,OACnCpD,EAAIqD,SAAS,CAAGC,EAChB,IAAMC,EAASvD,EAAIwD,WAAW,EAAIxD,EAAIyD,SAAS,EAAI,GACnD,OAAQF,EAAOlG,MAAM,CAXF,EAWKqG,EAAiB,GAA2C,OAAxCH,EAAOI,SAAS,CAAC,EAAGD,KAAoB,OAAOH,CAC7F,GAiBqDtD,CAlB8B,CAkBzBiD,WAAW,EAAI,YAK3E", "sources": ["webpack://_N_E/./node_modules/@restart/hooks/esm/useUpdateEffect.js", "webpack://_N_E/./node_modules/react-bootstrap/esm/CarouselCaption.js", "webpack://_N_E/./node_modules/react-bootstrap/esm/CarouselItem.js", "webpack://_N_E/./node_modules/react-bootstrap/esm/Carousel.js", "webpack://_N_E/./pages/vspace/vspace_announcement/Announcement.tsx", "webpack://_N_E/?1e2f", "webpack://_N_E/./pages/vspace/vspace_announcement/AnnouncementItem.tsx"], "sourcesContent": ["import { useEffect, useRef } from 'react';\n\n/**\n * Runs an effect only when the dependencies have changed, skipping the\n * initial \"on mount\" run. Caution, if the dependency list never changes,\n * the effect is **never run**\n *\n * ```ts\n *  const ref = useRef<HTMLInput>(null);\n *\n *  // focuses an element only if the focus changes, and not on mount\n *  useUpdateEffect(() => {\n *    const element = ref.current?.children[focusedIdx] as HTMLElement\n *\n *    element?.focus()\n *\n *  }, [focusedIndex])\n * ```\n * @param effect An effect to run on mount\n *\n * @category effects\n */\nfunction useUpdateEffect(fn, deps) {\n  const isFirst = useRef(true);\n  useEffect(() => {\n    if (isFirst.current) {\n      isFirst.current = false;\n      return;\n    }\n    return fn();\n  }, deps);\n}\nexport default useUpdateEffect;", "\"use client\";\n\nimport * as React from 'react';\nimport classNames from 'classnames';\nimport { useBootstrapPrefix } from './ThemeProvider';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst CarouselCaption = /*#__PURE__*/React.forwardRef(({\n  className,\n  bsPrefix,\n  as: Component = 'div',\n  ...props\n}, ref) => {\n  bsPrefix = useBootstrapPrefix(bsPrefix, 'carousel-caption');\n  return /*#__PURE__*/_jsx(Component, {\n    ref: ref,\n    className: classNames(className, bsPrefix),\n    ...props\n  });\n});\nCarouselCaption.displayName = 'CarouselCaption';\nexport default CarouselCaption;", "\"use client\";\n\nimport classNames from 'classnames';\nimport * as React from 'react';\nimport { useBootstrapPrefix } from './ThemeProvider';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst CarouselItem = /*#__PURE__*/React.forwardRef(({\n  // Need to define the default \"as\" during prop destructuring to be compatible with styled-components github.com/react-bootstrap/react-bootstrap/issues/3595\n  as: Component = 'div',\n  bsPrefix,\n  className,\n  ...props\n}, ref) => {\n  const finalClassName = classNames(className, useBootstrapPrefix(bsPrefix, 'carousel-item'));\n  return /*#__PURE__*/_jsx(Component, {\n    ref: ref,\n    ...props,\n    className: finalClassName\n  });\n});\nCarouselItem.displayName = 'CarouselItem';\nexport default CarouselItem;", "\"use client\";\n\nimport useEventCallback from '@restart/hooks/useEventCallback';\nimport useUpdateEffect from '@restart/hooks/useUpdateEffect';\nimport useCommittedRef from '@restart/hooks/useCommittedRef';\nimport useTimeout from '@restart/hooks/useTimeout';\nimport Anchor from '@restart/ui/Anchor';\nimport classNames from 'classnames';\nimport * as React from 'react';\nimport { useCallback, useEffect, useImperativeHandle, useMemo, useRef, useState } from 'react';\nimport { useUncontrolled } from 'uncontrollable';\nimport CarouselCaption from './CarouselCaption';\nimport CarouselItem from './CarouselItem';\nimport { map, forEach } from './ElementChildren';\nimport { useBootstrapPrefix, useIsRTL } from './ThemeProvider';\nimport transitionEndListener from './transitionEndListener';\nimport triggerBrowserReflow from './triggerBrowserReflow';\nimport TransitionWrapper from './TransitionWrapper';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nimport { jsxs as _jsxs } from \"react/jsx-runtime\";\nimport { Fragment as _Fragment } from \"react/jsx-runtime\";\nconst SWIPE_THRESHOLD = 40;\nfunction isVisible(element) {\n  if (!element || !element.style || !element.parentNode || !element.parentNode.style) {\n    return false;\n  }\n  const elementStyle = getComputedStyle(element);\n  return elementStyle.display !== 'none' && elementStyle.visibility !== 'hidden' && getComputedStyle(element.parentNode).display !== 'none';\n}\nconst Carousel =\n/*#__PURE__*/\n// eslint-disable-next-line react/display-name\nReact.forwardRef(({\n  defaultActiveIndex = 0,\n  ...uncontrolledProps\n}, ref) => {\n  const {\n    // Need to define the default \"as\" during prop destructuring to be compatible with styled-components github.com/react-bootstrap/react-bootstrap/issues/3595\n    as: Component = 'div',\n    bsPrefix,\n    slide = true,\n    fade = false,\n    controls = true,\n    indicators = true,\n    indicatorLabels = [],\n    activeIndex,\n    onSelect,\n    onSlide,\n    onSlid,\n    interval = 5000,\n    keyboard = true,\n    onKeyDown,\n    pause = 'hover',\n    onMouseOver,\n    onMouseOut,\n    wrap = true,\n    touch = true,\n    onTouchStart,\n    onTouchMove,\n    onTouchEnd,\n    prevIcon = /*#__PURE__*/_jsx(\"span\", {\n      \"aria-hidden\": \"true\",\n      className: \"carousel-control-prev-icon\"\n    }),\n    prevLabel = 'Previous',\n    nextIcon = /*#__PURE__*/_jsx(\"span\", {\n      \"aria-hidden\": \"true\",\n      className: \"carousel-control-next-icon\"\n    }),\n    nextLabel = 'Next',\n    variant,\n    className,\n    children,\n    ...props\n  } = useUncontrolled({\n    defaultActiveIndex,\n    ...uncontrolledProps\n  }, {\n    activeIndex: 'onSelect'\n  });\n  const prefix = useBootstrapPrefix(bsPrefix, 'carousel');\n  const isRTL = useIsRTL();\n  const nextDirectionRef = useRef(null);\n  const [direction, setDirection] = useState('next');\n  const [paused, setPaused] = useState(false);\n  const [isSliding, setIsSliding] = useState(false);\n  const [renderedActiveIndex, setRenderedActiveIndex] = useState(activeIndex || 0);\n  useEffect(() => {\n    if (!isSliding && activeIndex !== renderedActiveIndex) {\n      if (nextDirectionRef.current) {\n        setDirection(nextDirectionRef.current);\n      } else {\n        setDirection((activeIndex || 0) > renderedActiveIndex ? 'next' : 'prev');\n      }\n      if (slide) {\n        setIsSliding(true);\n      }\n      setRenderedActiveIndex(activeIndex || 0);\n    }\n  }, [activeIndex, isSliding, renderedActiveIndex, slide]);\n  useEffect(() => {\n    if (nextDirectionRef.current) {\n      nextDirectionRef.current = null;\n    }\n  });\n  let numChildren = 0;\n  let activeChildInterval;\n\n  // Iterate to grab all of the children's interval values\n  // (and count them, too)\n  forEach(children, (child, index) => {\n    ++numChildren;\n    if (index === activeIndex) {\n      activeChildInterval = child.props.interval;\n    }\n  });\n  const activeChildIntervalRef = useCommittedRef(activeChildInterval);\n  const prev = useCallback(event => {\n    if (isSliding) {\n      return;\n    }\n    let nextActiveIndex = renderedActiveIndex - 1;\n    if (nextActiveIndex < 0) {\n      if (!wrap) {\n        return;\n      }\n      nextActiveIndex = numChildren - 1;\n    }\n    nextDirectionRef.current = 'prev';\n    onSelect == null || onSelect(nextActiveIndex, event);\n  }, [isSliding, renderedActiveIndex, onSelect, wrap, numChildren]);\n\n  // This is used in the setInterval, so it should not invalidate.\n  const next = useEventCallback(event => {\n    if (isSliding) {\n      return;\n    }\n    let nextActiveIndex = renderedActiveIndex + 1;\n    if (nextActiveIndex >= numChildren) {\n      if (!wrap) {\n        return;\n      }\n      nextActiveIndex = 0;\n    }\n    nextDirectionRef.current = 'next';\n    onSelect == null || onSelect(nextActiveIndex, event);\n  });\n  const elementRef = useRef();\n  useImperativeHandle(ref, () => ({\n    element: elementRef.current,\n    prev,\n    next\n  }));\n\n  // This is used in the setInterval, so it should not invalidate.\n  const nextWhenVisible = useEventCallback(() => {\n    if (!document.hidden && isVisible(elementRef.current)) {\n      if (isRTL) {\n        prev();\n      } else {\n        next();\n      }\n    }\n  });\n  const slideDirection = direction === 'next' ? 'start' : 'end';\n  useUpdateEffect(() => {\n    if (slide) {\n      // These callbacks will be handled by the <Transition> callbacks.\n      return;\n    }\n    onSlide == null || onSlide(renderedActiveIndex, slideDirection);\n    onSlid == null || onSlid(renderedActiveIndex, slideDirection);\n  }, [renderedActiveIndex]);\n  const orderClassName = `${prefix}-item-${direction}`;\n  const directionalClassName = `${prefix}-item-${slideDirection}`;\n  const handleEnter = useCallback(node => {\n    triggerBrowserReflow(node);\n    onSlide == null || onSlide(renderedActiveIndex, slideDirection);\n  }, [onSlide, renderedActiveIndex, slideDirection]);\n  const handleEntered = useCallback(() => {\n    setIsSliding(false);\n    onSlid == null || onSlid(renderedActiveIndex, slideDirection);\n  }, [onSlid, renderedActiveIndex, slideDirection]);\n  const handleKeyDown = useCallback(event => {\n    if (keyboard && !/input|textarea/i.test(event.target.tagName)) {\n      switch (event.key) {\n        case 'ArrowLeft':\n          event.preventDefault();\n          if (isRTL) {\n            next(event);\n          } else {\n            prev(event);\n          }\n          return;\n        case 'ArrowRight':\n          event.preventDefault();\n          if (isRTL) {\n            prev(event);\n          } else {\n            next(event);\n          }\n          return;\n        default:\n      }\n    }\n    onKeyDown == null || onKeyDown(event);\n  }, [keyboard, onKeyDown, prev, next, isRTL]);\n  const handleMouseOver = useCallback(event => {\n    if (pause === 'hover') {\n      setPaused(true);\n    }\n    onMouseOver == null || onMouseOver(event);\n  }, [pause, onMouseOver]);\n  const handleMouseOut = useCallback(event => {\n    setPaused(false);\n    onMouseOut == null || onMouseOut(event);\n  }, [onMouseOut]);\n  const touchStartXRef = useRef(0);\n  const touchDeltaXRef = useRef(0);\n  const touchUnpauseTimeout = useTimeout();\n  const handleTouchStart = useCallback(event => {\n    touchStartXRef.current = event.touches[0].clientX;\n    touchDeltaXRef.current = 0;\n    if (pause === 'hover') {\n      setPaused(true);\n    }\n    onTouchStart == null || onTouchStart(event);\n  }, [pause, onTouchStart]);\n  const handleTouchMove = useCallback(event => {\n    if (event.touches && event.touches.length > 1) {\n      touchDeltaXRef.current = 0;\n    } else {\n      touchDeltaXRef.current = event.touches[0].clientX - touchStartXRef.current;\n    }\n    onTouchMove == null || onTouchMove(event);\n  }, [onTouchMove]);\n  const handleTouchEnd = useCallback(event => {\n    if (touch) {\n      const touchDeltaX = touchDeltaXRef.current;\n      if (Math.abs(touchDeltaX) > SWIPE_THRESHOLD) {\n        if (touchDeltaX > 0) {\n          prev(event);\n        } else {\n          next(event);\n        }\n      }\n    }\n    if (pause === 'hover') {\n      touchUnpauseTimeout.set(() => {\n        setPaused(false);\n      }, interval || undefined);\n    }\n    onTouchEnd == null || onTouchEnd(event);\n  }, [touch, pause, prev, next, touchUnpauseTimeout, interval, onTouchEnd]);\n  const shouldPlay = interval != null && !paused && !isSliding;\n  const intervalHandleRef = useRef();\n  useEffect(() => {\n    var _ref, _activeChildIntervalR;\n    if (!shouldPlay) {\n      return undefined;\n    }\n    const nextFunc = isRTL ? prev : next;\n    intervalHandleRef.current = window.setInterval(document.visibilityState ? nextWhenVisible : nextFunc, (_ref = (_activeChildIntervalR = activeChildIntervalRef.current) != null ? _activeChildIntervalR : interval) != null ? _ref : undefined);\n    return () => {\n      if (intervalHandleRef.current !== null) {\n        clearInterval(intervalHandleRef.current);\n      }\n    };\n  }, [shouldPlay, prev, next, activeChildIntervalRef, interval, nextWhenVisible, isRTL]);\n  const indicatorOnClicks = useMemo(() => indicators && Array.from({\n    length: numChildren\n  }, (_, index) => event => {\n    onSelect == null || onSelect(index, event);\n  }), [indicators, numChildren, onSelect]);\n  return /*#__PURE__*/_jsxs(Component, {\n    ref: elementRef,\n    ...props,\n    onKeyDown: handleKeyDown,\n    onMouseOver: handleMouseOver,\n    onMouseOut: handleMouseOut,\n    onTouchStart: handleTouchStart,\n    onTouchMove: handleTouchMove,\n    onTouchEnd: handleTouchEnd,\n    className: classNames(className, prefix, slide && 'slide', fade && `${prefix}-fade`, variant && `${prefix}-${variant}`),\n    children: [indicators && /*#__PURE__*/_jsx(\"div\", {\n      className: `${prefix}-indicators`,\n      children: map(children, (_, index) => /*#__PURE__*/_jsx(\"button\", {\n        type: \"button\",\n        \"data-bs-target\": \"\" // Bootstrap requires this in their css.\n        ,\n        \"aria-label\": indicatorLabels != null && indicatorLabels.length ? indicatorLabels[index] : `Slide ${index + 1}`,\n        className: index === renderedActiveIndex ? 'active' : undefined,\n        onClick: indicatorOnClicks ? indicatorOnClicks[index] : undefined,\n        \"aria-current\": index === renderedActiveIndex\n      }, index))\n    }), /*#__PURE__*/_jsx(\"div\", {\n      className: `${prefix}-inner`,\n      children: map(children, (child, index) => {\n        const isActive = index === renderedActiveIndex;\n        return slide ? /*#__PURE__*/_jsx(TransitionWrapper, {\n          in: isActive,\n          onEnter: isActive ? handleEnter : undefined,\n          onEntered: isActive ? handleEntered : undefined,\n          addEndListener: transitionEndListener,\n          children: (status, innerProps) => /*#__PURE__*/React.cloneElement(child, {\n            ...innerProps,\n            className: classNames(child.props.className, isActive && status !== 'entered' && orderClassName, (status === 'entered' || status === 'exiting') && 'active', (status === 'entering' || status === 'exiting') && directionalClassName)\n          })\n        }) : ( /*#__PURE__*/React.cloneElement(child, {\n          className: classNames(child.props.className, isActive && 'active')\n        }));\n      })\n    }), controls && /*#__PURE__*/_jsxs(_Fragment, {\n      children: [(wrap || activeIndex !== 0) && /*#__PURE__*/_jsxs(Anchor, {\n        className: `${prefix}-control-prev`,\n        onClick: prev,\n        children: [prevIcon, prevLabel && /*#__PURE__*/_jsx(\"span\", {\n          className: \"visually-hidden\",\n          children: prevLabel\n        })]\n      }), (wrap || activeIndex !== numChildren - 1) && /*#__PURE__*/_jsxs(Anchor, {\n        className: `${prefix}-control-next`,\n        onClick: next,\n        children: [nextIcon, nextLabel && /*#__PURE__*/_jsx(\"span\", {\n          className: \"visually-hidden\",\n          children: nextLabel\n        })]\n      })]\n    })]\n  });\n});\nCarousel.displayName = 'Carousel';\nexport default Object.assign(Carousel, {\n  Caption: CarouselCaption,\n  Item: CarouselItem\n});", "//Import Library\r\nimport React, { useEffect, useState } from 'react';\r\nimport { Container, Row } from \"react-bootstrap\";\r\nimport Col from \"react-bootstrap/Col\";\r\nimport { useRouter } from \"next/router\";\r\n\r\n//Import services/components\r\nimport _ from 'lodash';\r\nimport AnnouncementItem from \"./AnnouncementItem\";\r\nimport Carousel from 'react-bootstrap/Carousel'\r\nimport apiService from \"../../../services/apiService\";\r\nimport { useTranslation } from 'next-i18next';\r\n\r\n\r\n//TODO: Need to use RKISingleItemCarousel component to reuse our exisiting component\r\ninterface ListOfAnnouncementItemProps {\r\n  announcements: any[][];\r\n}\r\n\r\nfunction ListOfAnnouncementItem(props: ListOfAnnouncementItemProps) {\r\n  const { announcements } = props;\r\n  return (\r\n    <div>\r\n      {announcements.map((item, index) => {\r\n        return (\r\n          <Row className=\"announcementItem\" key={index}>\r\n            <AnnouncementItem item={item} />\r\n          </Row>\r\n        )\r\n      })}\r\n    </div>\r\n  )\r\n}\r\n\r\nfunction Announcement() {\r\n  const { t } = useTranslation('common');\r\n  const router = useRouter();\r\n  const routes: any = router.query.routes || [];\r\n  const [announcements, setAnnouncements] = useState<any[][]>([]);\r\n\r\n  const [cindex, setCindex] = useState(0);\r\n\r\n  const [carouselItemCount] = useState(3);\r\n\r\n  const setEmptyNotice = () => {\r\n    setAnnouncements([]);\r\n  };\r\n\r\n  const updatesParams = {\r\n    query: { show_as_announcement: true, parent_vspace: routes[1] },\r\n    sort: { created_at: \"desc\" },\r\n    limit: \"~\",\r\n    select: \"-created_at -update_type -contact_details -document -end_date -link -media -reply -start_date -updated_at\"\r\n  };\r\n\r\n  const fetchAnnouncements = async (params = updatesParams) => {\r\n    const response = await apiService.get('/updates', params);\r\n    if (response && response.data && response.data.length > 0) {\r\n      const partition = _.chunk(response.data, 3);\r\n      setAnnouncements(partition)\r\n    } else {\r\n      setEmptyNotice()\r\n    }\r\n  };\r\n\r\n  useEffect(() => {\r\n    fetchAnnouncements();\r\n  }, [])\r\n\r\n  const toggleCarousel = (direction: 'next' | 'prev') => {\r\n    let index = cindex;\r\n    const [min, max] = [0, carouselItemCount - 1];\r\n\r\n    if (direction === 'next') {\r\n      index++\r\n    }\r\n    else if (direction === 'prev') {\r\n      index--\r\n    }\r\n\r\n    if (index > max) {\r\n      index = 0\r\n    }\r\n\r\n    if (index < min) {\r\n      index = max\r\n    }\r\n\r\n\r\n    if ((announcements.length - index) === 1) {\r\n      index = announcements.length - 1;\r\n    }\r\n\r\n    if ((announcements.length - index) === 0) {\r\n      index = 1;\r\n    }\r\n    setCindex(index);\r\n  };\r\n\r\n  return (\r\n    <div className=\"announcements mt-0\">\r\n      {announcements && announcements.length > 0 ? (\r\n        <>\r\n          <Container fluid>\r\n            <Row>\r\n              <Col xs={10} className=\"p-0\">\r\n              </Col>\r\n              {announcements && announcements.length > 1 ?\r\n                <Col xs={2} className=\"text-end carousel-control p-0\">\r\n                  <div className=\"carousel-navigation\">\r\n                    <a className=\"left carousel-control\" onClick={() => toggleCarousel('prev')}>\r\n                      <i className=\"fa fa-chevron-left\" />\r\n                    </a>\r\n                    <a className=\"right carousel-control\" onClick={() => toggleCarousel('next')}>\r\n                      <i className=\"fa fa-chevron-right\" />\r\n                    </a>\r\n                  </div>\r\n                </Col>\r\n                : null}\r\n            </Row>\r\n          </Container>\r\n          <Container fluid>\r\n            <Row>\r\n              <Col xs={12} className=\"p-0\">\r\n                <Carousel indicators={false} controls={false} interval={null} activeIndex={cindex}>\r\n                  {announcements.map((item, index) => {\r\n                    return (\r\n                      <Carousel.Item key={index}>\r\n                        <ListOfAnnouncementItem announcements={item} />\r\n                      </Carousel.Item>\r\n                    )\r\n                  })}\r\n                </Carousel>\r\n              </Col>\r\n            </Row>\r\n          </Container>\r\n        </>\r\n      ) : (\r\n          <Container fluid={true}>\r\n            <Row>\r\n              <Col xs={12} className=\"p-0\">\r\n                <div className=\"border border-info m-3\">\r\n                <p className=\"d-flex d-flex justify-content-center p-2 m-0\">{t(\"NoAnnouncementFound!\")}</p>\r\n                </div>\r\n              </Col>\r\n            </Row>\r\n          </Container>\r\n        )}\r\n    </div>\r\n  )\r\n}\r\n\r\nexport default Announcement;", "\n    (window.__NEXT_P = window.__NEXT_P || []).push([\n      \"/vspace/vspace_announcement/Announcement\",\n      function () {\n        return require(\"private-next-pages/vspace/vspace_announcement/Announcement.tsx\");\n      }\n    ]);\n    if(module.hot) {\n      module.hot.dispose(function () {\n        window.__NEXT_P.push([\"/vspace/vspace_announcement/Announcement\"])\n      });\n    }\n  ", "//Import Library\r\nimport { Col } from \"react-bootstrap\";\r\nimport <PERSON> from \"next/link\";\r\n\r\nconst truncateLength = 260;\r\n\r\n//TODO: Remove the maths random number for image after updates completed with image upload\r\nexport default function AnnouncementItem(props: any) {\r\n\r\n  const { item } = props;\r\n\r\n  const getTrimmedString = (html: any) => {\r\n    const div = document.createElement(\"div\");\r\n    div.innerHTML = html;\r\n    const string = div.textContent || div.innerText || \"\";\r\n    return (string.length > truncateLength ? `${string.substring(0, truncateLength - 3)}...` : string);\r\n  }\r\n\r\n  return (\r\n    <Col className=\"p-0\" xs={12}>\r\n      <Link href={`/${item.type}/[...routes]`} as={`/${item.type}/show/${item[Parent_func(item)]}/update/${item._id}`}>\r\n\r\n        {(item.images && item.images[0]) ?\r\n          <img src={`${process.env.API_SERVER}/image/show/${item.images[0]._id}`} alt=\"announcement\"\r\n            className=\"announceImg\" />\r\n          : <i className=\"fa fa-bullhorn announceImg\" />}\r\n\r\n      </Link>\r\n      <div className=\"announceDesc\">\r\n        <Link href={`/${item.type}/[...routes]`} as={`/${item.type}/show/${item[newFunction(item)]}/update/${item._id}`}>\r\n          {item && item.title ? item.title : ''}\r\n        </Link>\r\n        <p>\r\n          {item && item.description ? getTrimmedString(item.description) : null}\r\n        </p>\r\n      </div>\r\n    </Col>\r\n  );\r\n}\r\n\r\nfunction newFunction(item: any) {\r\n  return `parent_${item.type}`;\r\n}\r\n\r\nfunction Parent_func(item: any) {\r\n  return `parent_${item.type}`;\r\n}\r\n"], "names": ["CarouselCaption", "React", "className", "bsPrefix", "as", "Component", "props", "useBootstrapPrefix", "_jsx", "ref", "classNames", "displayName", "CarouselItem", "finalClassName", "Carousel", "activeChildInterval", "defaultActiveIndex", "uncontrolledProps", "slide", "fade", "controls", "indicators", "indicatorLabels", "activeIndex", "onSelect", "onSlide", "onSlid", "interval", "keyboard", "onKeyDown", "pause", "onMouseOver", "onMouseOut", "wrap", "touch", "onTouchStart", "onTouchMove", "onTouchEnd", "prevIcon", "prevLabel", "nextIcon", "next<PERSON><PERSON><PERSON>", "variant", "children", "useUncontrolled", "prefix", "isRTL", "useIsRTL", "nextDirectionRef", "useRef", "direction", "setDirection", "useState", "paused", "setPaused", "isSliding", "setIsSliding", "renderedActiveIndex", "setRenderedActiveIndex", "useEffect", "current", "numC<PERSON><PERSON>n", "for<PERSON>ach", "child", "index", "activeChildIntervalRef", "useCommittedRef", "prev", "useCallback", "event", "nextActiveIndex", "next", "useEventCallback", "elementRef", "useImperativeHandle", "element", "nextWhenVisible", "document", "hidden", "style", "parentNode", "elementStyle", "getComputedStyle", "display", "visibility", "slideDirection", "useUpdateEffect", "orderClassName", "directionalClassName", "handleEnter", "node", "triggerBrowserReflow", "handleEntered", "handleKeyDown", "test", "target", "tagName", "key", "preventDefault", "handleMouseOver", "handleMouseOut", "touchStartXRef", "touchDeltaXRef", "touchUnpauseTimeout", "useTimeout", "handleTouchStart", "touches", "clientX", "handleTouchMove", "length", "handleTouchEnd", "touchDeltaX", "Math", "abs", "SWIPE_THRESHOLD", "set", "undefined", "shouldPlay", "intervalHandleRef", "_ref", "_activeChildIntervalR", "nextFunc", "window", "setInterval", "visibilityState", "clearInterval", "indicatorOnClicks", "useMemo", "Array", "from", "_", "_jsxs", "map", "type", "onClick", "isActive", "TransitionWrapper", "in", "onEnter", "onEntered", "addEndListener", "transitionEndListener", "status", "innerProps", "_Fragment", "<PERSON><PERSON>", "Object", "assign", "Caption", "<PERSON><PERSON>", "ListOfAnnouncementItem", "announcements", "div", "item", "Row", "AnnouncementItem", "Announcement", "t", "useTranslation", "routes", "useRouter", "query", "setAnnouncements", "cindex", "setCindex", "carouselItemCount", "setEmptyNotice", "updatesParams", "show_as_announcement", "parent_vspace", "sort", "created_at", "limit", "select", "fetchAnnouncements", "params", "response", "apiService", "get", "data", "partition", "toggleCarousel", "min", "max", "Container", "fluid", "Col", "xs", "a", "i", "p", "Link", "href", "Parent_func", "_id", "images", "img", "src", "process", "alt", "newFunction", "title", "description", "getTrimmedString", "createElement", "innerHTML", "html", "string", "textContent", "innerText", "truncate<PERSON><PERSON>th", "substring"], "sourceRoot": "", "ignoreList": [0, 1, 2, 3]}