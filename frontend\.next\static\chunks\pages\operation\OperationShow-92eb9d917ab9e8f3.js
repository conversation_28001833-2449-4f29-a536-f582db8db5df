(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[785],{1071:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>u});var r=a(37876),s=a(11041),l=a(21772),o=a(10841),i=a.n(o),n=a(14232),d=a(56970),c=a(37784),m=a(31753);let u=e=>{let{t}=(0,m.Bd)("common"),[a,o]=(0,n.useState)(40);(0,n.useEffect)(()=>{var e;null==(e=document.getElementById("timeline-container"))||e.scroll(a,5e3)},[a]);let u={1:"/images/home/<USER>",2:"/images/home/<USER>",3:"/images/home/<USER>",4:"/images/home/<USER>",5:"/images/home/<USER>",6:"/images/home/<USER>",7:"/images/home/<USER>"};return(0,r.jsx)(r.Fragment,{children:(0,r.jsx)(d.A,{children:(0,r.jsx)(c.A,{className:"operatinTimeline",xs:12,children:(0,r.jsxs)("div",{className:"progress_main_sec",style:{marginTop:"90px"},children:[e.operation&&e.operation.timeline.length>2&&(0,r.jsxs)("div",{children:[(0,r.jsx)("div",{className:"prev",onClick:()=>{let e=a-50;o(e<0?0:e)},style:{cursor:"pointer"},children:(0,r.jsx)("span",{children:(0,r.jsx)(l.g,{icon:s.Uec})})}),(0,r.jsx)("div",{className:"next",onClick:()=>{o(a+50)},style:{cursor:"pointer"},children:(0,r.jsx)("span",{children:(0,r.jsx)(l.g,{icon:s.vmR})})})]}),(0,r.jsx)("div",{className:"progressbar-container",id:"timeline-container",children:(0,r.jsx)("ul",{className:"progressbar",children:e.operation&&e.operation.timeline&&e.operation.timeline.map((a,s)=>(0,r.jsxs)("li",{style:{zIndex:e.operation.timeline.length-s},children:[(0,r.jsx)("div",{className:"timelineIcon",children:(0,r.jsx)("img",{src:u[a.iconclass],width:"80px",height:"80px"})}),a.timetitle?(0,r.jsx)("p",{className:"step-label",children:a.timetitle}):(0,r.jsx)("p",{className:"step-label",children:t("NoTitle")}),a.date?(0,r.jsx)("p",{className:"step-text",children:i()(a.date).format("MM-D-YYYY")}):(0,r.jsx)("p",{className:"step-text",children:t("NoDate")})]},s))})})]})})})})}},9839:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>u});var r=a(37876),s=a(14232),l=a(49589),o=a(53718),i=a(37308),n=a(31753),d=a(77053),c=a(64990),m=a(1071);let u=e=>{let{t}=(0,n.Bd)("common"),[a,u]=(0,s.useState)({title:"",timeline:[],description:"",hazard_type:{title:""},hazard:[],syndrome:{title:""},created_at:"",updated_at:"",country:{title:""},status:{title:""},start_date:"",end_date:"",partners:[],images:[],images_src:[],document:[],doc_src:[]}),[p,g]=(0,s.useState)(!1),[h,f]=(0,s.useState)(!1),[x,N]=(0,s.useState)([]),[_,j]=(0,s.useState)([]),[y,A]=(0,s.useState)(!1),v={sort:{doc_created_at:"asc"},Doctable:!0},w={sort:{doc_created_at:"asc"},DocUpdatetable:!0},C=async()=>{let t=[];f(!0);let a=await o.A.get("/operation/".concat(e.routes[1]),v);a&&Array.isArray(a)&&a.length>=1&&a[0].document&&a[0].document.length>=1&&(a.forEach(e=>{e.document&&e.document.length>0&&e.document.map((a,r)=>{a.description=e.document[r].docsrc,t.push(a)})}),N(t)),f(!1)},E=async()=>{let t=[];f(!0);let a=await o.A.get("/operation/".concat(e.routes[1]),w);a&&Array.isArray(a)&&a.length>=1&&a[0].document&&a[0].document.length>=1&&(a.forEach(e=>{e.document&&e.document.length>0&&e.document.map((a,r)=>{a.description=e.document[r].docsrc,t.push(a)})}),j(t)),f(!1)};(0,s.useEffect)(()=>{R()},[]);let R=async()=>{let t=await o.A.post("/users/getLoggedUser",{});t&&t.roles&&t.roles.length&&e.routes&&e.routes[1]&&((async a=>{g(!0);let r=await o.A.get("/operation/".concat(e.routes[1]),a);r&&(u(r),function(e,t){A(!1),t&&t.roles&&(t.roles.includes("SUPER_ADMIN")||t.roles.filter(e=>"EMT_NATIONAL_FOCALPOINT"==e).length>0&&e.user._id==t._id||t.roles.filter(e=>"EMT"==e).length>0&&e.user._id==t._id||t.roles.filter(e=>"INIG_STAKEHOLDER"==e).length>0&&e.user._id==t._id||t.roles.filter(e=>"GENERAL_USER"==e).length>0&&e.user._id==t._id?A(!0):t.roles.filter(e=>"PLATFORM_ADMIN"==e).length>0&&e.user._id==t._id&&A(!0))}(r,t)),g(!1)})({}),C(),E())},U={operationData:a,routeData:e,editAccess:y,documentAccoirdianProps:{loading:h,sortProps:e=>{v.sort={[e.columnSelector]:e.sortDirection},C()},Document:x,updateDocument:_,sortUpdateProps:e=>{w.sort={[e.columnSelector]:e.sortDirection},E()}}};return(0,r.jsx)(r.Fragment,{children:(null==a?void 0:a.title)?(0,r.jsxs)(l.A,{className:"operationDetail",fluid:!0,children:[(0,r.jsx)(i.A,{routes:e.routes}),(0,r.jsx)(d.default,{...U}),(0,r.jsx)(m.default,{operation:a}),(0,r.jsx)(c.default,{...U})]}):(0,r.jsx)(r.Fragment,{})})}},29335:(e,t,a)=>{"use strict";a.d(t,{A:()=>A});var r=a(15039),s=a.n(r),l=a(14232),o=a(77346),i=a(37876);let n=l.forwardRef((e,t)=>{let{className:a,bsPrefix:r,as:l="div",...n}=e;return r=(0,o.oU)(r,"card-body"),(0,i.jsx)(l,{ref:t,className:s()(a,r),...n})});n.displayName="CardBody";let d=l.forwardRef((e,t)=>{let{className:a,bsPrefix:r,as:l="div",...n}=e;return r=(0,o.oU)(r,"card-footer"),(0,i.jsx)(l,{ref:t,className:s()(a,r),...n})});d.displayName="CardFooter";var c=a(81764);let m=l.forwardRef((e,t)=>{let{bsPrefix:a,className:r,as:n="div",...d}=e,m=(0,o.oU)(a,"card-header"),u=(0,l.useMemo)(()=>({cardHeaderBsPrefix:m}),[m]);return(0,i.jsx)(c.A.Provider,{value:u,children:(0,i.jsx)(n,{ref:t,...d,className:s()(r,m)})})});m.displayName="CardHeader";let u=l.forwardRef((e,t)=>{let{bsPrefix:a,className:r,variant:l,as:n="img",...d}=e,c=(0,o.oU)(a,"card-img");return(0,i.jsx)(n,{ref:t,className:s()(l?"".concat(c,"-").concat(l):c,r),...d})});u.displayName="CardImg";let p=l.forwardRef((e,t)=>{let{className:a,bsPrefix:r,as:l="div",...n}=e;return r=(0,o.oU)(r,"card-img-overlay"),(0,i.jsx)(l,{ref:t,className:s()(a,r),...n})});p.displayName="CardImgOverlay";let g=l.forwardRef((e,t)=>{let{className:a,bsPrefix:r,as:l="a",...n}=e;return r=(0,o.oU)(r,"card-link"),(0,i.jsx)(l,{ref:t,className:s()(a,r),...n})});g.displayName="CardLink";var h=a(46052);let f=(0,h.A)("h6"),x=l.forwardRef((e,t)=>{let{className:a,bsPrefix:r,as:l=f,...n}=e;return r=(0,o.oU)(r,"card-subtitle"),(0,i.jsx)(l,{ref:t,className:s()(a,r),...n})});x.displayName="CardSubtitle";let N=l.forwardRef((e,t)=>{let{className:a,bsPrefix:r,as:l="p",...n}=e;return r=(0,o.oU)(r,"card-text"),(0,i.jsx)(l,{ref:t,className:s()(a,r),...n})});N.displayName="CardText";let _=(0,h.A)("h5"),j=l.forwardRef((e,t)=>{let{className:a,bsPrefix:r,as:l=_,...n}=e;return r=(0,o.oU)(r,"card-title"),(0,i.jsx)(l,{ref:t,className:s()(a,r),...n})});j.displayName="CardTitle";let y=l.forwardRef((e,t)=>{let{bsPrefix:a,className:r,bg:l,text:d,border:c,body:m=!1,children:u,as:p="div",...g}=e,h=(0,o.oU)(a,"card");return(0,i.jsx)(p,{ref:t,...g,className:s()(r,h,l&&"bg-".concat(l),d&&"text-".concat(d),c&&"border-".concat(c)),children:m?(0,i.jsx)(n,{children:u}):u})});y.displayName="Card";let A=Object.assign(y,{Img:u,Title:j,Subtitle:x,Body:n,Link:g,Text:N,Header:m,Footer:d,ImgOverlay:p})},73564:(e,t,a)=>{(window.__NEXT_P=window.__NEXT_P||[]).push(["/operation/OperationShow",function(){return a(9839)}])},81764:(e,t,a)=>{"use strict";a.d(t,{A:()=>s});let r=a(14232).createContext(null);r.displayName="CardHeaderContext";let s=r}},e=>{var t=t=>e(e.s=t);e.O(0,[7725,9759,9773,1772,7126,8477,7308,4672,7053,636,6593,8792],()=>t(73564)),_N_E=e.O()}]);
//# sourceMappingURL=OperationShow-92eb9d917ab9e8f3.js.map