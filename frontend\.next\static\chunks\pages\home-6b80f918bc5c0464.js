(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[9845],{22609:(e,t,n)=>{(window.__NEXT_P=window.__NEXT_P||[]).push(["/home",function(){return n(73544)}])},73544:(e,t,n)=>{"use strict";n.r(t),n.d(t,{__N_SSG:()=>C,default:()=>H});var i=n(37876),s=n(14232),a=n(56970),r=n(37784),o=n(48230),l=n.n(o),c=n(63847),h=n(8441),d=n(31195),m=n(32890),u=n(53718),p=n(31753);function g(e){let[t,n]=(0,s.useState)(!1),[,a]=(0,s.useState)({title:"",description:"",pageCategory:"",isEnabled:!0}),r=e=>({__html:e}),{t:o}=(0,p.Bd)("common"),l=async()=>{let e={query:{pageCategory:""},sort:{title:"asc"},limit:"~"},t=await g();if(t.length>0){e.query.pageCategory=t;let n=await u.A.get("/landingPage",e);Array.isArray(n.data)&&n.data.length>0&&(n.data[0].title=n&&n.data[0].title.length>0&&!0===n.data[0].isEnabled?n.data[0].title:j,n.data[0].description=n&&n.data[0].description.length>0&&!0===n.data[0].isEnabled?n.data[0].description:f,a(n.data[0]),g())}};(0,s.useEffect)(()=>{l()},[]);let g=async()=>{let e=await u.A.get("/pagecategory",{query:{title:"Help"}});return!!e&&!!e.data&&e.data.length>0&&e.data[0]._id},f="The RKI Platform is accessible by invitation only to cooperation partner organisations in the field of Health Protection and their staff, through their organisations focal points. If your organisation is already part of the platform and you would like to join please check with your lead focal point as they will be able to add you. <br /><br />  If your organisation is not registered, but you would like to add your organization to our platform, you can email the team at  <EMAIL>.<br/><br/>  We ask that all users of the platform allow their name, title and email to be shared to facilitate communications across the network. We have data protection rules in place to protect against the misuse of data. For further information please click <a href='https://www.rki.de/DE/Service/Datenschutz/datenschutzerklaerung_node.html' target='_blank'>here</a><br /><br />Thank you for your interest.<br/>Kind regards,",j="How do I access the RKI Platform?";return(0,i.jsxs)("div",{children:[(0,i.jsx)("a",{onClick:()=>n(!0),children:(0,i.jsx)(c.A,{placement:"bottom",delay:{show:250,hide:400},overlay:(0,i.jsx)(h.A,{id:"print-tooltip",children:o("Help")}),children:(0,i.jsx)("i",{className:"fas fa-question-circle"})})}),(0,i.jsxs)(d.A,{size:"lg",show:t,onHide:()=>n(!1),children:[(0,i.jsx)(d.A.Header,{closeButton:!0,children:(0,i.jsx)(d.A.Title,{id:"help-modal",children:o("Help")})}),(0,i.jsx)(d.A.Body,{children:(0,i.jsx)("ul",{children:(0,i.jsx)(m.A,{children:(0,i.jsxs)(m.A.Item,{eventKey:"0",children:[(0,i.jsx)("li",{className:"help-content-li-title",children:(0,i.jsx)(m.A.Header,{children:(0,i.jsx)("div",{dangerouslySetInnerHTML:r(j)})})}),(0,i.jsx)(m.A.Body,{className:"help-content-li-content",children:(0,i.jsx)("div",{dangerouslySetInnerHTML:r(f)})})]})})})})]})]})}let f=e=>{let{onScroll:t}=e;return(0,i.jsx)("div",{className:"header-block",children:(0,i.jsx)("div",{className:"header-container",style:{},children:(0,i.jsxs)(a.A,{children:[(0,i.jsx)(r.A,{sm:3,className:"logo-image",children:(0,i.jsx)("img",{src:"/images/home/<USER>",height:"60px"})}),(0,i.jsx)(r.A,{sm:9,children:(0,i.jsxs)("ul",{className:"quick-menu",children:[(0,i.jsx)("li",{children:(0,i.jsx)("a",{onClick:()=>t(0),children:"Home"})}),(0,i.jsx)("li",{children:(0,i.jsx)("a",{onClick:()=>t(1),children:"Our network"})}),(0,i.jsx)("li",{children:(0,i.jsx)("a",{onClick:()=>t(2),children:"About us"})}),(0,i.jsx)("li",{children:(0,i.jsx)("a",{onClick:()=>t(3),children:"Contact us"})}),(0,i.jsx)("li",{children:(0,i.jsx)(g,{show:!1,onHide:()=>{}})}),(0,i.jsx)("li",{className:"login",children:(0,i.jsx)(l(),{href:"/login",children:"Login"})})]})})]})})})},j={0:"images/home/<USER>",1:"images/home/<USER>",2:"images/home/<USER>",3:"images/home/<USER>",4:"images/home/<USER>",5:"images/home/<USER>",6:"images/home/<USER>",7:"images/home/<USER>",8:"images/home/<USER>"},x={0:"images/home/<USER>",1:"images/home/<USER>",2:"images/home/<USER>",3:"images/home/<USER>",4:"images/home/<USER>"},b=[{title:"SARS-CoV-2 in Germany",content:"After a temporary stabilisation of case numbers at a higher level in late August and early September 2020, a steep increase in case numbers ensued in October in all federal states. Due to measures implemented at the beginning of November the rise in cases could be stopped, albeit no considerable reduction in case numbers ensued. Since 04/12/2020 case numbers have been sharply increasing again... ",href:"https://www.rki.de/EN/Content/infections/epidemiology/outbreaks/COVID-19/Situationsberichte_Tab.html;jsessionid=0D6329A535C4B9D303C461108C27A545.internet051",image:"images/home/<USER>"}],w=[{title:"The Robert Koch Institut as international hub for health protection networks",content:"When there are health emergencies across the world, such as disease outbreaks, the Robert Koch Institut’s expertise is in ever greater demand. RKI staff are involved in various international research projects and programmes. Thus, the RKI helps to tackle urgent public health problems and improve people’s health worldwide. ",href:"https://www.rki.de/EN/Content/Institute/International/international_activities_node.html"},{title:"About the Global Health Protection Programme",content:"As part of its international commitments, Germany is providing  increasing support to partner countries for the management of outbreaks and the development of reliable heafthcare systems. To this end, the German Federal Ministry of Health has launched a Global Health​ Protection Programme to improve and promote health at global scale. ",href:"https://ghpp.de/en/"},{title:"Global Health Policy",content:"Global health issues are closely related to several other policy areas such as development, security, trade and travel, the economy, human rights, nutrition, agriculture, research, employment, education, migration, the environment and climate protection, as well as humanitarian aid. ",href:"https://www.bundesgesundheitsministerium.de/en/international-co-operation.html"},{title:"German Biosecurity Programme",content:"The German Biosecurity, founded by the Federal Foreign Office, is intended to help partner countries tackle biosecurity threats, such as the intentional misuse of biological pathogens and toxins or outbreaks of highly pathogenic diseases and pandemics. The aim is to strengthen the health services of our partner countries in Africa, Central Asia and Eastern Europe, thus enhancing their national security. ",href:"https://www.auswaertiges-amt.de/en/aussenpolitik/themen/abruestung/uebersicht-bcwaffen-node/-/239362"},{title:"Emergency Mobile Laboratory",content:"The European Mobile Laboratory (EMLab) Consortium deploy state of the art boxed field laboratories as well as trained scientists and technicians to epidemics and outbreaks of infectious diseases caused by pathogens up to risk group 4 to perform diagnostics and supporting clinical laboratory analysis on site. ",href:"https://www.emlab.eu/"},{title:"Emergency Medical Teams",content:"The purpose of the Emergency Medical Teams initiative is to improve the timeliness and quality of health services provided by national and international EMTs and enhance the capacity of national health systems in leading the activation and coordination of this response in the immediate aftermath of a disaster, outbreak and/or other emergency. ",href:"https://extranet.who.int/emt/"},{title:"Over 800 institutions in over 80 countries supporting WHO programmes",content:"WHO collaborating centres are institutions such as research institutes, parts of universities or academies, which are designated by the Director-General to carry out activities in support of the Organization's programmes. Currently there are 24 from over 800 WHO collaborating centres in Germany. ",href:"https://www.who.int/about/who-we-are/structure/collaborating-centres"},{title:"Global Outbreak Alert and Response Network (GOARN) ",content:"GOARN is a collaboration of existing institutions and networks, constantly alert and ready to respond. The network pools human and technical resources for rapid identification, confirmation and response to outbreaks of international importance. ",href:"https://extranet.who.int/goarn"},{title:"International Association of National Public Health Institutes",content:"The International Association of National Public Health Institutes is a member organization of government agencies working to improve national disease prevention and response. IANPHI is made up of 100+ members, located in approximately 90 countries. "}],y=[{title:"PEI_Germany",tag:"@PEI_Germany",content:"Exten\xadsively drug-resis\xadtant Kleb\xadsiella pneu\xadmoniae out\xadbreak, north-eastern Ger\xadmany, 2019 (Euro\xadsurveillance, 12.12.2019)"},{title:"BMG_Bund",tag:"@BMG_Bund",content:"Angesichts der Entwicklung in Italien rechnet Bundesgesundheitsminister Jens Spahn damit, dass sich das Coronavirus auch in Deutschland"},{title:"Bfarm_de",tag:"@bfarm_de",content:"Was genau macht eigentlich das BfArM? Knapp vier Minuten Film geben Einblicke in unsere Aufgaben, unseren Arbeitsalltag und unsere Motivation."},{title:"FZBorstel",tag:"@FZBorstel",content:"The Research Center Borstel, Germany is hosting the next #lipidomics forum! Please check it out, share, spread the word, retweet, register!"},{title:"Loeffler_News",tag:"@Loeffler_News",content:"With the current cases in Poland, #AfricanSwineFever made a westerly leap of about 250 km and has now moved about 80 km from the German border."}],v=()=>(0,i.jsx)("div",{className:"slider",children:(0,i.jsx)(a.A,{children:(0,i.jsx)(r.A,{xs:12,children:b.map((e,t)=>(0,i.jsxs)("div",{className:"myslider",children:[(0,i.jsx)("img",{src:"images/home/<USER>",width:"100%"}),(0,i.jsxs)("div",{className:"sliderContent",children:[(0,i.jsx)("div",{className:"sliderTitle",children:e.title}),(0,i.jsxs)("div",{className:"sliderDesc",children:[e.content,(0,i.jsx)(l(),{href:e.href,target:"_blank",children:"More Information"})]})]})]},t))})})}),k=()=>(0,i.jsx)("div",{className:"welcome",children:(0,i.jsxs)("div",{className:"about-section",children:[(0,i.jsx)("div",{className:"title",children:"Welcome to the new Knowledge Platform for international health protection"}),(0,i.jsx)("p",{children:"The purpose of the new Knowledge Platform is to enable German Institutions and partners, who work in the field of international health protection, to share information in order to strengthen our all’s capacity to respond and engage all partners effectively within this network. Non-commercial entities and institutions with an ability to support outbreak control and public health emergencies are welcome to the network. The site disseminates information concerning events, projects, operations, partner institutions and activities of the network. In addition, the platform also creates a forum for partners to collaborate through virtual spaces, including spaces created for specific purpose such as WHO Collaboration Centres or Emergency Medical Teams. The development of the platform was financed by the German Ministry of Health."})]})}),N=()=>(0,i.jsxs)(r.A,{sm:8,children:[(0,i.jsx)("div",{className:"block-title network",children:"Our Networks"}),w.map((e,t)=>(0,i.jsxs)("div",{className:"network-news",children:[(0,i.jsx)("img",{src:j[t]}),(0,i.jsxs)("div",{className:"newsContent",children:[(0,i.jsx)("div",{className:"newsTitle",children:e.title}),(0,i.jsxs)("div",{className:"newsDesc",children:[e.content,e.href?(0,i.jsx)(l(),{href:e.href,target:"_blank",children:"Find more information..."}):null]})]})]},t))]}),A=()=>(0,i.jsxs)(r.A,{sm:4,children:[(0,i.jsx)("div",{className:"block-title news-feeds",children:"News Feeds"}),y.map((e,t)=>(0,i.jsxs)("div",{className:"feed-news",children:[(0,i.jsx)("img",{src:x[t]}),(0,i.jsxs)("div",{className:"newsContent",children:[(0,i.jsxs)("div",{className:"newsTitle",children:[e.title," ",(0,i.jsx)("span",{children:e.tag})]}),(0,i.jsx)("div",{className:"newsDesc",children:e.content})]})]},t))]}),I=e=>{let{innerRef:t}=e;return(0,i.jsx)("div",{className:"about-us",ref:t,children:(0,i.jsx)("div",{className:"aboutus-wrap",children:(0,i.jsxs)(a.A,{xs:12,children:[(0,i.jsx)(r.A,{sm:6,children:(0,i.jsx)("img",{src:"/images/home/<USER>",alt:"About",width:"100%"})}),(0,i.jsx)(r.A,{sm:6,children:(0,i.jsxs)("div",{className:"content",children:[(0,i.jsx)("div",{className:"title",children:"About us"}),(0,i.jsx)("div",{className:"desc",children:"The German Ministry of Health has commissioned the Robert Koch Institut (RKI) to develop this platform. Under the coordination of the Centre for International Health Protection, based at RKI in Berlin, we want to support our partners in exchanging information in order to better coordinate the activities of all of them and thus strengthen the response capacity of all. "})]})})]})})})},T=e=>{let{innerRef:t}=e;return(0,i.jsx)("div",{className:"landing-footer",ref:t,children:(0,i.jsx)("div",{className:"footer-block",children:(0,i.jsx)(r.A,{xs:12,children:(0,i.jsxs)(a.A,{children:[(0,i.jsx)(r.A,{sm:3,style:{marginTop:"10px"},children:(0,i.jsx)("img",{src:"/images/home/<USER>",className:"img-fluid"})}),(0,i.jsx)(r.A,{sm:3,style:{marginTop:"10px"},children:(0,i.jsx)("div",{className:"footerLeft",children:(0,i.jsxs)("div",{style:{lineHeight:"14px"},children:[(0,i.jsx)("p",{children:"The Robert Koch Institut is a Federal Institute within the portfolio of the Federal Ministry of Health"}),(0,i.jsxs)("p",{children:[(0,i.jsx)("br",{}),(0,i.jsx)("span",{style:{fontFamily:"verdana"},children:"\xa9"})," ",new Date().getFullYear()," Robert Koch Institute"]}),(0,i.jsx)("p",{children:"All rights reserved unless explicitly granted."})]})})}),(0,i.jsx)(r.A,{sm:3,children:(0,i.jsxs)("div",{className:"contactInfo",style:{marginTop:"3px"},children:[(0,i.jsx)("p",{style:{fontSize:"16px"},children:"Robert Koch Institut"}),(0,i.jsxs)("p",{children:["Federal Information Centre for International",(0,i.jsx)("br",{}),"Health Protection (ZIG1)"]}),(0,i.jsx)("p",{children:"Nordufer 20"}),(0,i.jsx)("p",{children:"13353 Berlin "}),(0,i.jsx)("p",{children:"Germany"}),(0,i.jsx)("br",{}),(0,i.jsx)("br",{})]})}),(0,i.jsx)(r.A,{sm:3,style:{marginTop:"10px"},children:(0,i.jsxs)("div",{className:"contactNum",children:[(0,i.jsx)("p",{children:"Tel: +49 (0) 3018 7540"}),(0,i.jsx)("p",{children:"Email: <EMAIL>"})]})})]})})})})},_=()=>{let e=(0,s.useRef)(null),t=(0,s.useRef)(null),n=(0,s.useRef)(null),r=(0,s.useRef)(null);return(0,i.jsxs)("div",{className:"landing",children:[(0,i.jsx)(f,{onScroll:i=>{let s=[e,t,n,r];if(s[i]&&s[i].current){let e=window.pageYOffset+s[i].current.getBoundingClientRect().top-100;window.scrollTo({behavior:"smooth",top:e})}}}),(0,i.jsx)("div",{className:"horizontal-line"}),(0,i.jsx)("div",{className:"home",ref:e,children:(0,i.jsx)(v,{})}),(0,i.jsx)(k,{}),(0,i.jsx)("div",{ref:t,children:(0,i.jsxs)(a.A,{className:"feeds-section",children:[(0,i.jsx)(N,{}),(0,i.jsx)(A,{})]})}),(0,i.jsx)(I,{innerRef:n}),(0,i.jsx)(T,{innerRef:r})]})};var C=!0;let H=()=>(0,i.jsx)(_,{})}},e=>{var t=t=>e(e.s=t);e.O(0,[636,6593,8792],()=>t(22609)),_N_E=e.O()}]);
//# sourceMappingURL=home-6b80f918bc5c0464.js.map