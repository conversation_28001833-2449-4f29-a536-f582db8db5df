"use strict";exports.id=1522,exports.ids=[1522],exports.modules={41522:(e,t,s)=>{s.a(e,async(e,a)=>{try{s.r(t),s.d(t,{default:()=>E});var i=s(8732),r=s(82015),l=s.n(r),n=s(7082),d=s(18597),o=s(83551),c=s(49481),p=s(59549),u=s(91353),x=s(96158),h=s(72521),m=s(82053),j=s(54131),g=s(11e3),A=s(27825),y=s.n(A),f=s(74716),_=s.n(f),v=s(19918),b=s.n(v),S=s(23579),w=s(66994),C=s(42893),N=s(44233),k=s.n(N),I=s(98178),z=s(63487),D=s(58070),T=s(88271),M=s(88751),O=s(24047),L=e([j,C,I,z,T]);[j,C,I,z,T]=L.then?(await L)():L;let E=e=>{let{t,i18n:s}=(0,M.useTranslation)("common"),a="fr"===s.language?"en":s.language,A="de"===s.language?{title_de:"asc"}:{title:"asc"},f=a?`title.${a}`:"title.en",v=(0,r.useRef)(null),N=(0,r.useRef)(null),[L,E]=(0,r.useState)({invitesCountry:"",invitesRegion:[],invitesOrganisationType:"",invitesOrganisation:"",invitesExpertise:"",invitesNetWork:"",visibility:!0}),[G]=(0,r.useState)(f),[F,q]=(0,r.useState)([]),[R,P]=(0,r.useState)([]),[$,B]=(0,r.useState)([]),[H,W]=(0,r.useState)([]),[U,V]=(0,r.useState)([]),[K,J]=(0,r.useState)([]),[,Q]=(0,r.useState)([]),[X,Y]=(0,r.useState)(1),[Z,ee]=(0,r.useState)([{institution:"",expertise:[],partner_expertise:[],organisation_status:"",work_description:"",status:""}]),[et,es]=(0,r.useState)([]),[ea,ei]=(0,r.useState)([]),[er,el]=(0,r.useState)([]),[en,ed]=(0,r.useState)([]),[eo,ec]=(0,r.useState)([]),[ep,eu]=(0,r.useState)([]),[ex,eh]=(0,r.useState)([]),[em,ej]=(0,r.useState)(!1),[eg]=(0,r.useState)(a),[eA,ey]=(0,r.useState)(!1),[ef,e_]=(0,r.useState)(null),[ev,eb]=(0,r.useState)([{timetitle:"",iconclass:"",date:null}]),[eS,ew]=(0,r.useState)(!0),eC=e.routes&&"edit"===e.routes[0]&&e.routes[1],eN={title:"",country:"",world_region:"",region:[],status:" ",hazard_type:"",hazard:[],syndrome:null,active_tab:1,start_date:null,TimeLineStartDate:new Date,end_date:null,description:"",images:[],document:[],checked:!1,images_src:[],doc_src:[]},ek={query:{},sort:A,limit:"~",languageCode:a},[eI,ez]=(0,r.useState)(eN),eD=()=>{let e=[...Z];e.push({status:"",institution:"",expertise:[],organisation_status:"",work_description:"",partner_expertise:[]}),ee(e),Y(e.length)},eT=(e,t)=>{Z.splice(t,1);let s=[...Z];ee(s),Y(s.length),0===Z.length&&eD()},eM=(e,t)=>{if(e.target){let{name:s,value:a}=e.target;Z[t][s]=a,"organisation_status"===s&&(Z[t].status=a)}else Z[t].partner_expertise=e,Z[t].expertise=e.map(e=>e.value);ee([...Z])},eO=async e=>{let t=await z.A.get("/operation_status",e);t&&Array.isArray(t.data)&&V(t.data);let s=await z.A.get("/country",e);s&&Array.isArray(s.data)&&J(s.data);let a=await z.A.get("/hazardtype",e);a&&Array.isArray(a.data)&&es(a.data);let i=await z.A.get("/syndrome",e);i&&Array.isArray(i.data)&&ec(i.data),e.select="-contact_name -description -type -networks -expertise -hazard_types -hazards -address -focal_points -website -telephone -twitter -header -use_default_header -images -status -email -user -created_at -updated_at -primary_focal_point",e.query.status={$ne:"Request Pending"};let r=await z.A.get("/institution",e);r&&Array.isArray(r.data)&&eh(r.data);let l=await z.A.get("/expertise",e);if(l&&Array.isArray(l.data)){let e=l.data.map((e,t)=>({label:e.title,value:e._id}));eu(e)}let n=await z.A.get("/deploymentstatus",e);n&&Array.isArray(n.data)&&ed(n.data)},eL=(e,t,s)=>(q(e.images?e.images:[]),P(e.images_src?e.images_src:[]),W(e.document?e.document:[]),B(e.doc_src?e.doc_src:[]),ee(t),e$(e.country),eB(e.hazard_type),ez(t=>({...t,...e})),s?ez(e=>({...e,checked:!0})):null),eE=(e,t,s,a)=>{a.push({institution:e.institution&&e.institution._id,partner_expertise:t,expertise:s,organisation_status:e&&e.status?e.status._id:"",status:e&&e.status?e.status._id:"",work_description:e.work_description})},eG=e=>{let{status:t,country:s,syndromes:a,region:i,hazard_type:r,hazard:l,timeline:n,start_date:d,end_date:o}=e;e.status=t&&t._id?t._id:" ",e.start_date=d?_()(d).toDate():null,e.end_date=o?_()(o).toDate():null,e.country=s&&s._id?s._id:" ",e.syndrome=a&&a._id?a._id:null,e.hazard_type=r&&r._id?r._id:"",e.hazard=l?l.map(e=>({label:e.title[eg],value:e._id})):[],e.region=i?i.map(e=>({label:e.title,value:e._id})):[],n&&eb(n)},eF=async()=>{let t=await z.A.get(`/operation/${e.routes[1]}`,ek);if(t){let e=[],{end_date:s,partners:a}=t;eG(t),a&&a.forEach(t=>{let s=t.expertise&&t.expertise.map(e=>({label:e.title,value:e._id})),a=t.expertise&&t.expertise.map(e=>e._id);eE(t,s,a,e)}),eL(t,e,s)}},eq=async()=>{let e=await z.A.post("/users/getLoggedUser",{});e&&e.roles&&((e.roles?.filter(e=>"EMT_NATIONAL_FOCALPOINT"==e)).length>0?ew(!1):ew(!0))};(0,r.useEffect)(()=>{eC&&eF(),eO(ek),eq()},[]);let eR=e=>{ez(t=>({...t,...e})),E(t=>({...t,...e}))},eP=async e=>{let t=[];if(e){let s=await z.A.get(`/country_region/${e}`,ek);s&&s.data&&(t=s.data.map((e,t)=>({label:e.title,value:e._id}))).sort((e,t)=>e.label.localeCompare(t.label))}return t},e$=async e=>{ei(await eP(e))},eB=async e=>{let t=[];if(e){let s=await z.A.get(`/hazard_hazard_type/${e}`,{query:{enabled:!0},sort:{[G]:"asc"},limit:"~",select:"-description -first_letter -hazard_type -picture -picture_source  -created_at -updated_at"});s&&s.data&&(t=s.data.map(e=>({label:e.title[eg],value:e._id})))}el(t)},eH=()=>{let e={timetitle:"",iconclass:"",date:null};eb(t=>[...t,e])},eW=(e,t)=>{ev.splice(t,1),eb([...ev]),0===ev.length&&eH()},eU=e=>{if(e.target){let{name:t,value:s}=e.target;if("country"===t){let t=e.target.selectedIndex;if(e.target&&t&&null!=t){let s=e.target[t].getAttribute("data-worldregion");ez(e=>({...e,world_region:s}))}}ez(e=>({...e,[t]:s})),"country"===t&&(e$(s),eR({region:[]})),"hazard_type"===t&&(eB(s),eR({hazard:[]}))}else ez(t=>({...t,region:e}))};l().useEffect(()=>{if(L){let e={};Object.keys(L).forEach((t,s)=>{let a=L[t].length>0&&L[t].map(e=>e.value);e[t]=a||[]}),eV(e)}},[L]);let eV=async e=>{let{invitesCountry:t,invitesOrganisation:s}=e,a=await z.A.post("/user-invite",{query:{country:t,institution:s}});if(a&&Array.isArray(a)){let e=a.map((e,t)=>({label:e.username,value:e._id}));Q(e)}},eK=(e,t)=>{let{name:s,value:a}=e.target,i=[...ev];i[t][s]=a,eb(i)},eJ=(e,t)=>{"start_date"===t&&null==e||"start_date"===t?ez(t=>({...t,end_date:null,start_date:e})):ez(s=>({...s,[t]:e}))},eQ=(e,t)=>{ev[t].date=e,eb([...ev])},eX=e=>{ez(t=>({...t,description:e}))},eY=async(s,a,i)=>{let r,l;s&&s.preventDefault&&s.preventDefault(),N.current&&N.current.setAttribute("disabled","disabled");let n=y().map(Z,y().partialRight(y().pick,["institution","expertise","status","work_description"])),d={title:eI.title.trim(),description:eI.description,status:eI.status,start_date:eI.start_date,end_date:eI.end_date,country:eI.country,world_region:eI.world_region,syndrome:eI.syndrome,hazard_type:eI.hazard_type,hazard:eI.hazard?eI.hazard.map((e,t)=>e.value):[],region:eI.region?eI.region.map((e,t)=>e.value):[],timeline:ev.length>0&&""!==ev[0].timetitle?ev:[],partners:n,images:eI.images,images_src:eI.images_src,document:eI.document,doc_src:eI.doc_src};eC?(l="toast.Operationupdatedsuccessfully",r=await z.A.patch(`/operation/${e.routes[1]}`,d)):(l="toast.Operationaddedsuccessfully",r=await z.A.post("/operation",d)),r&&r._id?em?(e_(r?._id&&r._id),ey(!0)):(C.default.success(t(l)),k().push("/operation/[...routes]",`/operation/show/${r._id}`)):(l=eC?"toast.OperationNotupdatedsuccessfully":"toast.OperationNotaddedsuccessfully",C.default.error(t(l))),N.current&&N.current.removeAttribute("disabled")},eZ=e=>{let t=[],s=[];e.length>0&&e.map(e=>{e.type&&(e.type.includes("pdf")||e.type.includes("docx")||e.type.includes("xlsx")||e.type.includes("xls"))?s.push(e.serverID):t.push(e.serverID)}),ez(e=>({...e,images:t})),ez(e=>({...e,document:s}))},e0=e=>{ez(t=>({...t,images_src:e}))},e1=e=>{ez(t=>({...t,doc_src:e}))};return(0,i.jsxs)(n.A,{className:"formCard",fluid:!0,children:[(0,i.jsx)(d.A,{children:(0,i.jsx)(w.A,{onSubmit:eY,ref:v,onErrorSubmit:e=>{let t=-1,s=y().map(Z,y().partialRight(y().pick,["institution","expertise","status","work_description"])),a=0;for(let e in s){if(""===s[e].institution||""===s[e].status){t=a;break}a++}if(t>-1){Y(t+1);let e=document.getElementById("btnAddForm");e?.scrollIntoView()}},initialValues:eI,enableReinitialize:!0,children:(0,i.jsxs)(d.A.Body,{children:[(0,i.jsx)(o.A,{children:(0,i.jsx)(c.A,{children:(0,i.jsx)(d.A.Title,{children:eC?t("editOperation"):t("addOperation")})})}),(0,i.jsx)("hr",{}),(0,i.jsxs)(o.A,{className:"mb-3",children:[(0,i.jsx)(c.A,{md:!0,lg:6,sm:12,children:(0,i.jsxs)(p.A.Group,{children:[(0,i.jsx)(p.A.Label,{className:"required-field",children:t("CountryOrTerritory")}),(0,i.jsxs)(S.s3,{name:"country",id:"country",value:eI.country,onChange:eU,required:!0,errorMessage:t("thisfieldisrequired"),children:[(0,i.jsx)("option",{value:"",children:t("SelectCountry")}),K.map((e,t)=>(0,i.jsx)("option",{"data-worldregion":e.world_region._id,value:e._id,children:e.title},t))]})]})}),(0,i.jsx)(c.A,{md:!0,lg:6,sm:12,children:(0,i.jsxs)(p.A.Group,{style:{maxWidth:"600px"},children:[(0,i.jsx)(p.A.Label,{children:t("CountryRegions")}),(0,i.jsx)(g.MultiSelect,{overrideStrings:{selectSomeItems:t("SelectRegions"),allItemsAreSelected:"All Regions are Selected"},options:ea,value:eI.region,onChange:eU,className:"region",labelledBy:"Select Country Regions"})]})})]}),(0,i.jsxs)(o.A,{className:"mb-3",children:[(0,i.jsx)(c.A,{md:!0,lg:4,sm:12,children:(0,i.jsxs)(p.A.Group,{children:[(0,i.jsx)(p.A.Label,{className:"required-field",children:t("HazardType")}),(0,i.jsxs)(S.s3,{name:"hazard_type",id:"hazard_type",value:eI.hazard_type,onChange:eU,required:!0,errorMessage:t("thisfieldisrequired"),children:[(0,i.jsx)("option",{value:"",children:t("SelectHazardType")}),et.map((e,t)=>(0,i.jsx)("option",{value:e._id,children:e.title},t))]})]})}),(0,i.jsx)(c.A,{md:!0,lg:4,sm:12,children:(0,i.jsxs)(p.A.Group,{children:[(0,i.jsx)(p.A.Label,{children:t("Hazard")}),(0,i.jsx)(p.A.Group,{children:(0,i.jsx)(g.MultiSelect,{overrideStrings:{selectSomeItems:t("SelectHazard"),allItemsAreSelected:"All Hazards are Selected"},options:er,value:eI.hazard,onChange:e=>{ez(t=>({...t,hazard:e}))},className:"hazard",labelledBy:"Select Hazards"})})]})}),(0,i.jsx)(c.A,{md:!0,lg:4,sm:12,children:(0,i.jsxs)(p.A.Group,{children:[(0,i.jsx)(p.A.Label,{children:t("Syndrome")}),(0,i.jsxs)(S.s3,{name:"syndrome",id:"syndrome",value:eI.syndrome,onChange:eU,children:[(0,i.jsx)("option",{value:"",children:t("SelectSyndrome")}),eo.map((e,t)=>(0,i.jsx)("option",{value:e._id,children:e.title},t))]})]})})]}),(0,i.jsx)(o.A,{className:"mb-3",children:(0,i.jsx)(c.A,{children:(0,i.jsxs)(p.A.Group,{children:[(0,i.jsx)(p.A.Label,{className:"required-field",children:t("Title")}),(0,i.jsx)(S.ks,{name:"title",id:"title",required:!0,value:eI.title,validator:e=>""!==e.trim(),errorMessage:{validator:t("PleaseAddtheTitle")},onChange:eU})]})})}),(0,i.jsx)(o.A,{className:"mb-3",children:(0,i.jsx)(c.A,{children:(0,i.jsxs)(p.A.Group,{children:[(0,i.jsx)(p.A.Label,{children:t("Description")}),(0,i.jsx)(O.x,{initContent:eI.description,onChange:e=>eX(e)})]})})}),(0,i.jsxs)(o.A,{className:"mb-3",children:[U.length?(0,i.jsx)(c.A,{md:!0,lg:3,sm:12,children:(0,i.jsxs)(p.A.Group,{children:[(0,i.jsx)(p.A.Label,{className:"required-field",children:t("OperationStatus")}),(0,i.jsxs)(S.s3,{name:"status",id:"status",value:eI.status,onChange:eU,required:!0,errorMessage:t("thisfieldisrequired"),children:[(0,i.jsx)("option",{value:"",children:t("SelectOperationStatus")}),U.map((e,t)=>(0,i.jsx)("option",{value:e._id,children:e.title},t))]})]})}):null,(0,i.jsx)(c.A,{md:!0,lg:3,sm:12,children:(0,i.jsxs)(p.A.Group,{children:[(0,i.jsx)(o.A,{children:(0,i.jsx)(c.A,{children:(0,i.jsx)(p.A.Label,{children:t("OperationStartDate")})})}),(0,i.jsx)(D.A,{selected:eI.start_date,onChange:e=>eJ(e,"start_date"),dateFormat:"MMMM d, yyyy",placeholderText:t("SelectStartDate")})]})}),(0,i.jsx)(c.A,{md:!0,lg:2,sm:12,children:(0,i.jsx)(p.A.Check,{type:"checkbox",checked:eI.checked,onChange:()=>{ez(e=>({...e,checked:!e.checked,end_date:null}))},label:t("ShowEndDate")})}),eI.checked&&(0,i.jsx)(c.A,{md:!0,lg:3,sm:12,children:(0,i.jsxs)(p.A.Group,{children:[(0,i.jsx)(o.A,{children:(0,i.jsx)(c.A,{children:(0,i.jsx)(p.A.Label,{children:t("OperationEndDate")})})}),(0,i.jsx)(D.A,{selected:eI.end_date,disabled:!eI.start_date,onChange:e=>eJ(e,"end_date"),dateFormat:"MMMM d, yyyy",minDate:eI.start_date,placeholderText:t("SelectEndDate")})]})})]}),(0,i.jsx)(d.A.Text,{children:(0,i.jsx)("b",{children:t("Timeline")})}),(0,i.jsx)("hr",{}),ev.map((e,s)=>(0,i.jsx)("div",{children:(0,i.jsxs)(o.A,{children:[(0,i.jsx)(c.A,{md:4,children:(0,i.jsxs)(p.A.Group,{children:[(0,i.jsx)(p.A.Label,{children:t("Title")}),(0,i.jsx)(S.ks,{name:"timetitle",id:"timetitle",value:e.timetitle,onChange:e=>eK(e,s)})]})}),(0,i.jsx)(c.A,{md:3,children:(0,i.jsxs)(p.A.Group,{children:[(0,i.jsx)(p.A.Label,{children:t("IconClass")}),(0,i.jsxs)(S.s3,{name:"iconclass",id:"iconclass",value:e.iconclass,onChange:e=>eK(e,s),children:[(0,i.jsx)("option",{value:"-1",children:"-Select-"}),(0,i.jsx)("option",{value:"1",children:"RFA"}),(0,i.jsx)("option",{value:"2",children:"Alert"}),(0,i.jsx)("option",{value:"3",children:"Mission to Country"}),(0,i.jsx)("option",{value:"4",children:"Calendar Event"}),(0,i.jsx)("option",{value:"5",children:"Documents"}),(0,i.jsx)("option",{value:"6",children:"Meeting"}),(0,i.jsx)("option",{value:"7",children:"Others"})]})]})}),(0,i.jsx)(c.A,{md:3,children:(0,i.jsxs)(p.A.Group,{children:[(0,i.jsx)(o.A,{children:(0,i.jsx)(c.A,{children:(0,i.jsx)(p.A.Label,{children:t("StartDate")})})}),(0,i.jsx)(D.A,{selected:e.date?_()(e.date).toDate():null,onChange:e=>eQ(e,s),dateFormat:"MMMM d, yyyy",placeholderText:t("SelectStartDate")})]})}),(0,i.jsx)(c.A,{md:2,className:"text-md-center",children:(0,i.jsx)(p.A.Group,{children:0===s?(0,i.jsx)("div",{}):(0,i.jsx)(u.A,{variant:"secondary",style:{marginTop:"30px"},onClick:e=>eW(e,s),children:t("Remove")})})})]})},s)),(0,i.jsx)(o.A,{children:(0,i.jsx)(c.A,{md:!0,lg:"4",children:(0,i.jsx)(u.A,{id:"btnAddForm",variant:"secondary",style:{marginTop:"27px",marginBottom:"20px"},onClick:eH,children:t("ADDANOTHERITEM")})})}),(0,i.jsx)(o.A,{className:"mt-1",children:(0,i.jsxs)(c.A,{children:[(0,i.jsx)(d.A.Text,{children:(0,i.jsx)("b",{children:t("AddOrganisation(s)")})}),(0,i.jsx)("hr",{})]})}),(0,i.jsx)(o.A,{className:"mb-3",children:(0,i.jsx)(c.A,{children:(0,i.jsx)(p.A.Group,{children:(0,i.jsxs)(x.A,{activeKey:X,onSelect:e=>Y(e),id:"uncontrolled-tab-example",children:[" ",Z.map((e,s)=>(0,i.jsxs)(h.A,{eventKey:`${s+1}`,title:`Organisation ${s+1}`,children:[(0,i.jsxs)(o.A,{className:"mb-3",children:[(0,i.jsx)(c.A,{md:4,children:(0,i.jsxs)(p.A.Group,{style:{paddingTop:"20px"},children:[(0,i.jsx)(p.A.Label,{className:"required-field",children:t("NameofAssociatedOrganisation")}),(0,i.jsxs)(S.s3,{name:"institution",id:"institution",value:e.institution,onChange:e=>eM(e,s),required:!0,errorMessage:t("thisfieldisrequired"),children:[(0,i.jsx)("option",{value:"",children:t("SelectOrganisation")}),ex.map((e,t)=>(0,i.jsx)("option",{value:e._id,children:e.title},t))]})]})}),(0,i.jsx)(c.A,{md:4,children:(0,i.jsxs)(p.A.Group,{style:{paddingTop:"20px",maxWidth:"400px"},children:[(0,i.jsx)(p.A.Label,{children:t("WorkingExpertise")}),(0,i.jsx)(g.MultiSelect,{overrideStrings:{selectSomeItems:t("ChooseExpertise"),allItemsAreSelected:"All Expertise are Selected"},onChange:e=>eM(e,s),options:ep,value:e.partner_expertise,className:"work-expert",labelledBy:"Select Expertise"})]})}),(0,i.jsx)(c.A,{md:4,children:(0,i.jsxs)(p.A.Group,{style:{paddingTop:"20px"},children:[(0,i.jsx)(p.A.Label,{className:"required-field",children:t("Status")}),(0,i.jsxs)(S.s3,{name:"organisation_status",id:"organisation_status",value:e.status,onChange:e=>eM(e,s),required:!0,errorMessage:t("thisfieldisrequired"),children:[(0,i.jsx)("option",{value:"",children:t("SelectStatus")}),en.map((e,t)=>(0,i.jsx)("option",{value:e._id,children:e.title},t))]})]})})]}),(0,i.jsx)(o.A,{children:(0,i.jsx)(c.A,{children:(0,i.jsxs)(p.A.Group,{children:[(0,i.jsx)(p.A.Label,{children:t("Description")}),(0,i.jsx)(O.x,{initContent:e.work_description,onChange:e=>(function(e,t){let s=[...Z];s[t].work_description=e,ee(s)})(e,s)})]})})}),(0,i.jsx)("div",{children:0===s?(0,i.jsx)("span",{}):(0,i.jsx)(c.A,{xs:!0,lg:"4",children:(0,i.jsx)(u.A,{onSelect:e=>Y(e),variant:"secondary",onClick:e=>eT(e,s),children:t("Remove")})})})]},s)),(0,i.jsx)(h.A,{title:(0,i.jsx)("div",{children:(0,i.jsx)("span",{onClick:eD,children:(0,i.jsx)(m.FontAwesomeIcon,{icon:j.faPlus,color:"#808080"})})})})]})})})}),(0,i.jsxs)(c.A,{className:"px-0",children:[(0,i.jsx)(d.A.Text,{children:(0,i.jsx)("b",{children:t("MediaGallery")})}),(0,i.jsx)("hr",{})]}),(0,i.jsx)(o.A,{children:(0,i.jsx)(c.A,{children:(0,i.jsx)(I.A,{datas:F,srcText:R,getImgID:e=>eZ(e),getImageSource:e=>e0(e)})})}),(0,i.jsxs)(c.A,{className:"px-0 mt-4",children:[(0,i.jsx)(d.A.Text,{children:(0,i.jsx)("b",{children:t("Documents")})}),(0,i.jsx)("hr",{})]}),(0,i.jsx)(o.A,{children:(0,i.jsx)(c.A,{children:(0,i.jsx)(I.A,{type:"application",datas:H,srcText:$,getImgID:e=>eZ(e),getImageSource:e=>e1(e)})})}),(0,i.jsx)(o.A,{className:"mt-4",children:(0,i.jsxs)(c.A,{children:[(0,i.jsx)(d.A.Text,{children:(0,i.jsx)("b",{children:t("VirtualSpace")})}),(0,i.jsx)("hr",{}),(0,i.jsx)(p.A.Check,{className:"p-0",type:"checkbox",disabled:!eS,onChange:()=>ej(!em),name:"virtula",checked:em,label:t("WouldliketocreateaVirtualSpace")})]})}),(0,i.jsx)(o.A,{className:"my-4",children:(0,i.jsxs)(c.A,{children:[(0,i.jsx)(u.A,{className:"me-2",type:"submit",variant:"primary",ref:N,onClick:eY,children:t("submit")}),(0,i.jsx)(u.A,{className:"me-2",onClick:()=>{ez(eN),q([]),P([]),W([]),B([]),eh([]),eb([]),ee([]),window.scrollTo(0,0)},variant:"info",children:t("reset")}),(0,i.jsx)(b(),{href:"/operation",as:"/operation",children:(0,i.jsx)(u.A,{variant:"secondary",children:t("Cancel")})})]})})]})})}),eA&&(0,i.jsx)(T.A,{type:"Operation",id:ef})]})};a()}catch(e){a(e)}})},98178:(e,t,s)=>{s.a(e,async(e,a)=>{try{s.d(t,{A:()=>S});var i=s(8732),r=s(82015),l=s(16029),n=s(82053),d=s(54131),o=s(49481),c=s(59549),p=s(91353),u=s(12403),x=s(27825),h=s.n(x),m=s(42893),j=s(63487),g=s(88751),A=e([d,m,j]);[d,m,j]=A.then?(await A)():A;let y=[],f={flex:1,display:"flex",flexDirection:"column",alignItems:"center",justifyContent:"center",width:"100%",height:"100%",borderWidth:.1,borderColor:"#fafafa",backgroundColor:"#fafafa",color:"black",transition:"border  .24s ease-in-out",padding:"15px"},_={display:"flex",padding:"10px",width:"100%",border:"2px solid gray",flexDirection:"column",justifyContent:"flex-start",flexWrap:"wrap",marginTop:20},v={width:"150px"},b={borderColor:"#2196f3"},S=e=>{let t,{t:s}=(0,g.useTranslation)("common"),[a,x]=(0,r.useState)(!1),[A,S]=(0,r.useState)(),w="application"==e.type?0x1400000:"20971520",[C,N]=(0,r.useState)([]),[k,I]=(0,r.useState)(!0),[z,D]=(0,r.useState)([]),T=e&&"application"===e.type?"/files":"/image",M=async e=>{await j.A.remove(`${T}/${e}`)},O=e=>{S(e),x(!0)},L=(e,t)=>{let s=[...z];s[t]=e.target.value,D(s)},E=t=>{switch(t&&t.name.split(".").pop()){case"JPG":case"jpg":case"jpeg":case"jpg":case"png":return(0,i.jsx)("img",{src:t.preview,style:v});case"pdf":return(0,i.jsx)("img",{src:"/images/fileIcons/pdfFile.png",className:"application"===e.type?"docPreview":"imgPreview"});case"docx":default:return(0,i.jsx)("img",{src:"/images/fileIcons/wordFile.png",className:"application"===e.type?"docPreview":"imgPreview"});case"xls":case"xlsx":return(0,i.jsx)("img",{src:"/images/fileIcons/xlsFile.png",className:"application"===e.type?"docPreview":"imgPreview"})}},G=()=>x(!1),F=()=>{x(!1)},q=t=>{let s=(t=A)&&t._id?{serverID:t._id}:{file:t},a=h().findIndex(y,s),i=[...z];i.splice(a,1),D(i),M(y[a].serverID),y.splice(a,1),e.getImgID(y,e.index?e.index:0);let r=[...C];r.splice(r.indexOf(t),1),N(r),x(!1)},R=C.map((t,r)=>(0,i.jsxs)("div",{children:[(0,i.jsx)(o.A,{xs:12,children:(0,i.jsxs)("div",{className:"row",children:[(0,i.jsx)(o.A,{md:4,lg:3,className:"application text-center align-self-center"===e.type?"docImagePreview text-center align-self-center":"imgPreview text-center align-self-center",children:E(t)}),(0,i.jsx)(o.A,{md:5,lg:7,className:"align-self-center",children:(0,i.jsxs)(c.A,{children:[(0,i.jsxs)(c.A.Group,{controlId:"filename",children:[(0,i.jsx)(c.A.Label,{className:"mt-2",children:s("FileName")}),(0,i.jsx)(c.A.Control,{size:"sm",type:"text",disabled:!0,value:t.original_name?t.original_name:t.name})]}),(0,i.jsxs)(c.A.Group,{controlId:"description",children:[(0,i.jsx)(c.A.Label,{children:"application"===e.type?s("ShortDescription/(Max255Characters)"):s("Source/Description")}),(0,i.jsx)(c.A.Control,{maxLength:"application"===e.type?255:void 0,size:"sm",type:"text",placeholder:"application"===e.type?s("`Enteryourdocumentdescription`"):s("`Enteryourimagesource/description`"),value:z[r],onChange:e=>L(e,r)})]})]})}),(0,i.jsx)(o.A,{md:3,lg:2,className:"align-self-center text-center",onClick:()=>O(t),children:(0,i.jsx)(p.A,{variant:"dark",children:s("Remove")})})]})}),(0,i.jsxs)(u.A,{show:a,onHide:G,children:[(0,i.jsx)(u.A.Header,{closeButton:!0,children:(0,i.jsx)(u.A.Title,{children:s("DeleteFile")})}),(0,i.jsx)(u.A.Body,{children:s("Areyousurewanttodeletethisfile?")}),(0,i.jsxs)(u.A.Footer,{children:[(0,i.jsx)(p.A,{variant:"secondary",onClick:F,children:s("Cancel")}),(0,i.jsx)(p.A,{variant:"primary",onClick:()=>q(t),children:s("yes")})]})]})]},r));(0,r.useEffect)(()=>{C.forEach(e=>URL.revokeObjectURL(e.preview)),y=[]},[]),(0,r.useEffect)(()=>{e.getImageSource(z)},[z]),(0,r.useEffect)(()=>{D(e.srcText)},[e.srcText]),(0,r.useEffect)(()=>{if(e&&"true"===e.singleUpload&&I(!1),e&&e.datas){let t=e.datas.map((t,s)=>(y.push({serverID:t._id,index:e.index?e.index:0,type:t.name.split(".")[1]}),{...t,preview:`http://localhost:3001/api/v1/image/show/${t._id}`}));N([...t])}},[e.datas]);let P=async(t,s)=>{if(t.length>s)try{let a=new FormData;a.append("file",t[s]);let i=await j.A.post(T,a,{"Content-Type":"multipart/form-data"});y.push({serverID:i._id,file:t[s],index:e.index?e.index:0,type:t[s].name.split(".")[1]}),P(t,s+1)}catch(e){P(t,s+1)}else e.getImgID(y,e.index?e.index:0)},$=(0,r.useCallback)(async e=>{await P(e,0);let t=e.map(e=>Object.assign(e,{preview:URL.createObjectURL(e)}));k?N(e=>[...e,...t]):N([...t])},[]),{getRootProps:B,getInputProps:H,isDragActive:W,isDragAccept:U,isDragReject:V,fileRejections:K}=(0,l.useDropzone)({accept:e&&e.type?"application/pdf, application/vnd.openxmlformats-officedocument.wordprocessingml.document, application/vnd.oasis.opendocument.text,application/vnd.openxmlformats-officedocument.spreadsheetml.sheet,text/csv":"image/*",multiple:k,minSize:0,maxSize:w,onDrop:$,validator:function(e){if("/image"===T){if("image"!==e.type.substring(0,5))return m.default.error(s("toast.filetypenotsupport")),{code:"file-invalid-type",message:"File type not supported"}}else if("/files"===T&&"image"===e.type.substring(0,5))return m.default.error(s("toast.filetypenotsupport")),{code:"file-invalid-type",message:"File type not supported"};return null}}),J=(0,r.useMemo)(()=>({...f,...W?b:{outline:"2px dashed #bbb"},...U?{outline:"2px dashed #595959"}:{outline:"2px dashed #bbb"},...V?{outline:"2px dashed red"}:{activeStyle:b}}),[W,V]);t=e&&"application"===e.type?(0,i.jsx)("small",{style:{color:"#595959"},children:s("DocumentWeSupport")}):(0,i.jsx)("small",{style:{color:"#595959"},children:s("ImageWeSupport")});let Q=K.length>0&&K[0].file.size>w;return(0,i.jsxs)(i.Fragment,{children:[(0,i.jsx)("div",{className:" d-flex justify-content-center align-items-center mt-3",style:{width:"100%",height:"180px"},children:(0,i.jsxs)("div",{...B({style:J}),children:[(0,i.jsx)("input",{...H()}),(0,i.jsx)(n.FontAwesomeIcon,{icon:d.faCloudUploadAlt,size:"4x",color:"#999"}),(0,i.jsx)("p",{style:{color:"#595959",marginBottom:"0px"},children:s("Drag'n'dropsomefileshere,orclicktoselectfiles")}),!k&&(0,i.jsxs)("small",{style:{color:"#595959"},children:[(0,i.jsx)("b",{children:"Note:"})," One single image will be accepted"]}),t,(e.type,Q&&(0,i.jsxs)("small",{className:"text-danger mt-2",children:[(0,i.jsx)(n.FontAwesomeIcon,{icon:d.faExclamationCircle,size:"1x",color:"red"})," ",s("FileistoolargeItshouldbelessthan20MB")]})),V&&(0,i.jsxs)("small",{className:"text-danger",style:{color:"#595959"},children:[(0,i.jsx)(n.FontAwesomeIcon,{icon:d.faExclamationCircle,size:"1x",color:"red"})," ",s("Filetypenotacceptedsorr")]})]})}),C.length>0&&(0,i.jsx)("div",{style:_,children:R})]})};a()}catch(e){a(e)}})}};