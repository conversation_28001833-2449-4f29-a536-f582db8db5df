"use strict";(()=>{var e={};e.id=286,e.ids=[286,636,3220],e.modules={123:e=>{e.exports=require("dom-helpers/removeEventListener")},1332:e=>{e.exports=require("react-custom-scrollbars-2")},1428:e=>{e.exports=import("axios")},1680:e=>{e.exports=require("@restart/ui/utils")},1919:e=>{e.exports=require("react-transition-group/Transition")},3892:e=>{e.exports=require("classnames")},4048:e=>{e.exports=require("react-truncate")},6009:e=>{e.exports=require("dom-helpers/ownerDocument")},6952:e=>{e.exports=require("@restart/ui/Modal")},7374:e=>{e.exports=require("@restart/ui/Dropdown")},8732:e=>{e.exports=require("react/jsx-runtime")},8929:(e,r,t)=>{t.r(r),t.d(r,{default:()=>c});var s=t(8732),a=t(82015),o=t(27825),i=t.n(o),l=t(89364),n=t(72953),p=t(88751);let u=e=>{let{i18n:r}=(0,p.useTranslation)("common"),t=r.language,[o,u]=(0,a.useState)({}),[c,d]=(0,a.useState)({}),[m,x]=(0,a.useState)([]),{countries:y}=e,g=()=>{u(null),d(null)},h=(e,r,t)=>{g(),u(r),d({name:e.name,id:e.id})},f=()=>{let e=[];y&&y.data&&i().forEach(y.data,r=>{e.push({title:r.title,id:r._id,lat:r.coordinates&&r.coordinates[0]?r.coordinates[0].latitude:null,lng:r.coordinates&&r.coordinates[0]?r.coordinates[0].longitude:null})}),x([...e])};return(0,a.useEffect)(()=>{f()},[y]),(0,s.jsx)(n.A,{language:t,points:m,height:300,activeMarker:o,markerInfo:(0,s.jsx)(e=>{let{info:r}=e;return(0,s.jsx)("a",{href:`/${t}/country/show/${r?.id}`,children:r?.name})},{info:c}),onClose:g,children:m&&m.length>=1?m.map((e,r)=>{if(e&&e.lat)return(0,s.jsx)(l.A,{name:e.title,id:e.id,icon:{url:"/images/map-marker-white.svg"},onClick:h,position:e},r)}):null})};u.defaultProps={countries:{data:[]}};let c=u},9653:e=>{e.exports=require("@restart/ui/Overlay")},11242:e=>{e.exports=require("redux-persist/integration/react")},11688:e=>{e.exports=require("@restart/hooks/useWillUnmount")},12388:e=>{e.exports=require("dom-helpers/addEventListener")},13364:e=>{e.exports=require("redux-saga/effects")},13449:(e,r,t)=>{t.r(r),t.d(r,{default:()=>u});var s=t(8732),a=t(19918),o=t.n(a),i=t(50843),l=t.n(i),n=t(88751);let p=e=>{let{t:r}=(0,n.useTranslation)("common"),{countries:t,setActivePage:a}=e;return(0,s.jsxs)("div",{children:[(0,s.jsx)("div",{className:"alphabetLists",children:t&&t.data&&t.data.length>0?(0,s.jsx)("ul",{children:t.data.map((e,r)=>(0,s.jsx)("li",{className:"clearfix",children:(0,s.jsx)(o(),{href:"/country/[...routes]",as:`/country/show/${e._id}`,children:(0,s.jsx)("div",{children:e.title})})},r))}):r("NoCountriesfound")}),t&&t.data?(0,s.jsx)("div",{className:"countries-pagination",children:(0,s.jsx)(l(),{pageCount:Math.ceil(t.totalCount/t.limit),pageRangeDisplayed:5,marginPagesDisplayed:2,onPageChange:e=>{a(e.selected+1)},forcePage:t.page-1,containerClassName:"pagination",pageClassName:"page-item",pageLinkClassName:"page-link",previousClassName:"page-item",previousLinkClassName:"page-link",nextClassName:"page-item",nextLinkClassName:"page-link",activeClassName:"active",disabledClassName:"disabled",previousLabel:"‹",nextLabel:"›"})}):null]})};p.defaultProps={countries:{page:1,limit:10,totalCount:10,data:[]}};let u=p},14062:e=>{e.exports=import("react-redux")},14078:e=>{e.exports=import("swr")},14332:e=>{e.exports=require("uncontrollable")},16116:e=>{e.exports=require("invariant")},18622:e=>{e.exports=require("yup")},19121:e=>{e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},20181:(e,r,t)=>{t.a(e,async(e,s)=>{try{t.d(r,{A:()=>d});var a=t(8732),o=t(82015);t(27825);var i=t(59549),l=t(91353),n=t(63487),p=t(88751),u=e([n]);function c(e){let{filtreg:r}=e,[t,s]=(0,o.useState)(!0),[n,u]=(0,o.useState)([]),[c,d]=(0,o.useState)([]),{t:m}=(0,p.useTranslation)("common"),x=e=>{let t=[...n],a=[...c];t.forEach((r,s)=>{r.code===e.target.id&&(t[s].isChecked=e.target.checked,e.target.checked?a.push(r._id):a=a.filter(e=>e!==r._id))}),d(a),r(a),s(!1),u(t)};return(0,a.jsxs)("div",{className:"regions-multi-checkboxes",children:[(0,a.jsx)(i.A.Check,{type:"checkbox",id:"all",label:m("AllRegions"),checked:t,onChange:e=>{let t=n.map(r=>({...r,isChecked:e.target.checked})),a=[];e.target.checked&&(a=t.map(e=>e._id)),r(a),d(a),s(e.target.checked),u(t)}}),n.map((e,r)=>(0,a.jsx)(i.A.Check,{type:"checkbox",id:e.code,label:e.title,value:e.code,onChange:x,checked:n[r].isChecked},r)),(0,a.jsx)(l.A,{onClick:()=>{let e=n.map(e=>({...e,isChecked:!1}));d([]),s(!1),u(e),r([])},className:"btn-plain ps-2",children:m("ClearAll")})]})}n=(u.then?(await u)():u)[0],c.defaultProps={filtreg:()=>{}};let d=c;s()}catch(e){s(e)}})},22326:e=>{e.exports=require("react-dom")},22541:e=>{e.exports=require("redux-persist/lib/storage")},25303:e=>{e.exports=require("@restart/ui/NavItem")},26324:e=>{e.exports=require("warning")},27053:(e,r,t)=>{t.d(r,{A:()=>a});var s=t(8732);function a(e){return(0,s.jsx)("h2",{className:"page-heading",children:e.title})}},27825:e=>{e.exports=require("lodash")},27910:e=>{e.exports=require("stream")},28217:e=>{e.exports=require("@restart/ui/DropdownItem")},29021:e=>{e.exports=require("fs")},29294:e=>{e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},29628:e=>{e.exports=require("dom-helpers/addClass")},29825:e=>{e.exports=require("prop-types")},29841:e=>{e.exports=require("dom-helpers/hasClass")},30362:e=>{e.exports=require("@restart/ui/DropdownToggle")},33873:e=>{e.exports=require("path")},35576:e=>{e.exports=require("next-i18next/serverSideTranslations")},36653:e=>{e.exports=require("nprogress")},36955:e=>{e.exports=require("@restart/hooks/useBreakpoint")},37766:e=>{e.exports=require("@restart/hooks/useMergedRefs")},39756:e=>{e.exports=import("redux")},40051:e=>{e.exports=require("dom-helpers/removeClass")},40361:e=>{e.exports=require("next/dist/compiled/next-server/pages.runtime.prod.js")},42893:e=>{e.exports=import("react-hot-toast")},43294:e=>{e.exports=require("formik")},50009:e=>{e.exports=require("@restart/ui/DropdownMenu")},50843:e=>{e.exports=require("react-paginate")},57664:e=>{e.exports=require("dom-helpers/scrollbarSize")},58928:e=>{e.exports=require("dom-helpers/canUseDOM")},59717:e=>{e.exports=require("@restart/ui/DropdownContext")},60560:e=>{e.exports=require("dom-helpers/contains")},63033:e=>{e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},63584:(e,r,t)=>{t.a(e,async(e,s)=>{try{t.r(r),t.d(r,{config:()=>g,default:()=>d,getServerSideProps:()=>y,getStaticPaths:()=>x,getStaticProps:()=>m,reportWebVitals:()=>h,routeModule:()=>A,unstable_getServerProps:()=>q,unstable_getServerSideProps:()=>b,unstable_getStaticParams:()=>v,unstable_getStaticPaths:()=>T,unstable_getStaticProps:()=>f});var a=t(63885),o=t(80237),i=t(81413),l=t(9616),n=t.n(l),p=t(72386),u=t(63939),c=e([p,u]);[p,u]=c.then?(await c)():c;let d=(0,i.M)(u,"default"),m=(0,i.M)(u,"getStaticProps"),x=(0,i.M)(u,"getStaticPaths"),y=(0,i.M)(u,"getServerSideProps"),g=(0,i.M)(u,"config"),h=(0,i.M)(u,"reportWebVitals"),f=(0,i.M)(u,"unstable_getStaticProps"),T=(0,i.M)(u,"unstable_getStaticPaths"),v=(0,i.M)(u,"unstable_getStaticParams"),q=(0,i.M)(u,"unstable_getServerProps"),b=(0,i.M)(u,"unstable_getServerSideProps"),A=new a.PagesRouteModule({definition:{kind:o.A.PAGES,page:"/country",pathname:"/country",bundlePath:"",filename:""},components:{App:p.default,Document:n()},userland:u});s()}catch(e){s(e)}})},63939:(e,r,t)=>{t.a(e,async(e,s)=>{try{t.r(r),t.d(r,{default:()=>v,getStaticProps:()=>T});var a=t(8732),o=t(82015),i=t(7082),l=t(83551),n=t(49481),p=t(14062),u=t(8929),c=t(64541),d=t(13449),m=t(27053),x=t(20181),y=t(88751),g=t(63487),h=t(35576),f=e([p,x,g]);async function T({locale:e}){return{props:{...await (0,h.serverSideTranslations)(e,["common"])}}}[p,x,g]=f.then?(await f)():f;let v=(0,p.connect)(e=>e)(e=>{let{t:r,i18n:t}=(0,y.useTranslation)("common"),s="de"===t.language?{title_de:"asc"}:{title:"asc"},p=r("All"),[h,f]=(0,o.useState)(p),[T,v]=(0,o.useState)([]),[q,b]=(0,o.useState)({}),[A,k]=(0,o.useState)(1),j={sort:s,limit:48,page:A,query:{},select:"-health_profile -security_advice -created_at -updated_at"},C=async(e,r=j)=>{if(0===T.length)return void b({data:[],totalCount:0,totalPages:0,page:1,limit:48});r="All"===h||"Alle"===h?{...r,query:{world_region:T}}:{...r,page:1,query:{languageCode:e,first_letter:h,world_region:T}};let t=await g.A.get("/country",{...r,languageCode:e});t&&t.data&&(console.log("response.data",t.data),console.log("response.data",t),b(t))};return(0,o.useEffect)(()=>{C(r("language"),j)},[h,T,A,r("language")]),(0,a.jsxs)(i.A,{fluid:!0,className:"p-0",children:[(0,a.jsx)(l.A,{children:(0,a.jsx)(n.A,{md:12,children:(0,a.jsx)(m.A,{title:r("menu.countries")})})}),(0,a.jsx)(l.A,{children:(0,a.jsx)(n.A,{md:12,children:(0,a.jsx)(u.default,{countries:q})})}),(0,a.jsx)(l.A,{children:(0,a.jsx)(n.A,{md:12,children:(0,a.jsx)(x.A,{filtreg:v,selectedRegions:T,regionHandler:v})})}),(0,a.jsx)(l.A,{children:(0,a.jsx)(n.A,{md:12,children:(0,a.jsx)(c.default,{selectedAlpha:h,setselectedAlpha:f})})}),(0,a.jsx)(l.A,{children:(0,a.jsx)(n.A,{md:12,children:(0,a.jsx)(d.default,{setActivePage:k,countries:q})})})]})});s()}catch(e){s(e)}})},64541:(e,r,t)=>{t.r(r),t.d(r,{default:()=>i});var s=t(8732),a=t(82427),o=t(88751);let i=e=>{let{selectedAlpha:r,setselectedAlpha:t}=e,{i18n:i}=(0,o.useTranslation)("common"),l="en"==i.language?a.E:a.M;return(0,s.jsx)("div",{className:"alphabetContainer",children:(0,s.jsx)("ul",{children:l.map((e,a)=>(0,s.jsx)("li",{children:(0,s.jsx)("a",{onClick:()=>t(e),className:`${r==e?"active":null}`,children:e})},a))})})}},65447:e=>{e.exports=require("@restart/ui/Button")},67364:e=>{e.exports=require("@restart/hooks/useCallbackRef")},68455:e=>{e.exports=import("redux-saga")},69722:e=>{e.exports=require("es6-promise")},72953:(e,r,t)=>{t.d(r,{A:()=>m});var s=t(8732);t(82015);var a=t(94696);let o=({position:e,onCloseClick:r,children:t})=>(0,s.jsx)(a.InfoWindow,{position:e,onCloseClick:r,children:(0,s.jsx)("div",{children:t})}),i="labels.text.fill",l="labels.text.stroke",n="road.highway",p="geometry.stroke",u=[{elementType:"geometry",stylers:[{color:"#1d2c4d"}]},{elementType:i,stylers:[{color:"#8ec3b9"}]},{elementType:l,stylers:[{color:"#1a3646"}]},{featureType:"administrative",elementType:"geometry",stylers:[{visibility:"off"}]},{featureType:"administrative.country",elementType:p,stylers:[{color:"#4b6878"}]},{featureType:"administrative.land_parcel",elementType:"labels",stylers:[{visibility:"off"}]},{featureType:"administrative.land_parcel",elementType:i,stylers:[{color:"#64779e"}]},{featureType:"administrative.province",elementType:p,stylers:[{color:"#4b6878"}]},{featureType:"landscape.man_made",elementType:p,stylers:[{color:"#334e87"}]},{featureType:"landscape.natural",elementType:"geometry",stylers:[{color:"#023e58"}]},{featureType:"poi",stylers:[{visibility:"off"}]},{featureType:"poi",elementType:"geometry",stylers:[{color:"#283d6a"}]},{featureType:"poi",elementType:"labels.text",stylers:[{visibility:"off"}]},{featureType:"poi",elementType:i,stylers:[{color:"#6f9ba5"}]},{featureType:"poi",elementType:l,stylers:[{color:"#1d2c4d"}]},{featureType:"poi.park",elementType:"geometry.fill",stylers:[{color:"#023e58"}]},{featureType:"poi.park",elementType:i,stylers:[{color:"#3C7680"}]},{featureType:"road",stylers:[{visibility:"off"}]},{featureType:"road",elementType:"geometry",stylers:[{color:"#304a7d"}]},{featureType:"road",elementType:"labels.icon",stylers:[{visibility:"off"}]},{featureType:"road",elementType:i,stylers:[{color:"#98a5be"}]},{featureType:"road",elementType:l,stylers:[{color:"#1d2c4d"}]},{featureType:n,elementType:"geometry",stylers:[{color:"#2c6675"}]},{featureType:n,elementType:p,stylers:[{color:"#255763"}]},{featureType:n,elementType:i,stylers:[{color:"#b0d5ce"}]},{featureType:n,elementType:l,stylers:[{color:"#023e58"}]},{featureType:"road.local",elementType:"labels",stylers:[{visibility:"off"}]},{featureType:"transit",stylers:[{visibility:"off"}]},{featureType:"transit",elementType:i,stylers:[{color:"#98a5be"}]},{featureType:"transit",elementType:l,stylers:[{color:"#1d2c4d"}]},{featureType:"transit.line",elementType:"geometry.fill",stylers:[{color:"#283d6a"}]},{featureType:"transit.station",elementType:"geometry",stylers:[{color:"#3a4762"}]},{featureType:"water",elementType:"geometry",stylers:[{color:"#0e1626"}]},{featureType:"water",elementType:i,stylers:[{color:"#4e6d70"}]}];var c=t(44233),d=t(40691);let m=({markerInfo:e,activeMarker:r,initialCenter:t,children:i,height:l=300,width:n="114%",language:p,zoom:m=1,minZoom:x=1,onClose:y})=>{let{locale:g}=(0,c.useRouter)(),{isLoaded:h,loadError:f}=(0,d._)(),T={width:n,height:"number"==typeof l?`${l}px`:l};return f?(0,s.jsx)("div",{children:"Error loading maps"}):h?(0,s.jsx)("div",{className:"map-container",children:(0,s.jsx)("div",{className:"mapprint",style:{width:n,height:l,position:"relative"},children:(0,s.jsxs)(a.GoogleMap,{mapContainerStyle:T,center:t||{lat:52.520017,lng:13.404195},zoom:m,onLoad:e=>{e.setOptions({styles:u})},options:{minZoom:x,draggable:!0,keyboardShortcuts:!1,streetViewControl:!1,panControl:!1,clickableIcons:!1,mapTypeControl:!1,fullscreenControl:!0},children:[i,e&&r&&r.getPosition&&(0,s.jsx)(o,{position:r.getPosition(),onCloseClick:()=>{console.log("close click"),y?.()},children:e})]})})}):(0,s.jsx)("div",{children:"Loading Maps..."})}},74075:e=>{e.exports=require("zlib")},74716:e=>{e.exports=require("moment")},74987:e=>{e.exports=require("@restart/ui/ModalManager")},78097:e=>{e.exports=require("next-redux-saga")},78634:e=>{e.exports=require("@restart/ui/Anchor")},80237:(e,r)=>{Object.defineProperty(r,"A",{enumerable:!0,get:function(){return t}});var t=function(e){return e.PAGES="PAGES",e.PAGES_API="PAGES_API",e.APP_PAGE="APP_PAGE",e.APP_ROUTE="APP_ROUTE",e.IMAGE="IMAGE",e}({})},81366:e=>{e.exports=require("redux-auth-wrapper/connectedAuthWrapper")},81413:(e,r)=>{Object.defineProperty(r,"M",{enumerable:!0,get:function(){return function e(r,t){return t in r?r[t]:"then"in r&&"function"==typeof r.then?r.then(r=>e(r,t)):"function"==typeof r&&"default"===t?r:void 0}}})},81521:e=>{e.exports=require("dom-helpers/querySelectorAll")},81895:e=>{e.exports=require("@restart/hooks/useEventCallback")},82015:e=>{e.exports=require("react")},82427:(e,r,t)=>{t.d(r,{E:()=>a,M:()=>s});let s=["A","B","C","D","E","F","G","H","I","J","K","L","M","N","O","P","Q","R","S","T","U","V","W","X","Y","Z","Alle"],a=["A","B","C","D","E","F","G","H","I","J","K","L","M","N","O","P","Q","R","S","T","U","V","W","X","Y","Z","All"]},86842:e=>{e.exports=require("@restart/ui/SelectableContext")},87571:e=>{e.exports=require("dom-helpers/transitionEnd")},88751:e=>{e.exports=require("next-i18next")},89364:(e,r,t)=>{t.d(r,{A:()=>o});var s=t(8732);t(82015);var a=t(94696);let o=({name:e="Marker",id:r="",countryId:t="",type:o,icon:i,position:l,onClick:n,title:p,draggable:u=!1})=>l&&"number"==typeof l.lat&&"number"==typeof l.lng?(0,s.jsx)(a.Marker,{position:l,icon:i,title:p||e,draggable:u,onClick:s=>{n&&n({name:e,id:r,countryId:t,type:o,position:l},{position:l,getPosition:()=>l},s)}}):null},93787:e=>{e.exports=require("redux-persist")},94696:e=>{e.exports=require("@react-google-maps/api")},94947:e=>{e.exports=require("@restart/hooks/useTimeout")},96196:e=>{e.exports=require("next-redux-wrapper")},98320:e=>{e.exports=require("@restart/hooks/useIsomorphicEffect")},99460:e=>{e.exports=require("dom-helpers/css")}};var r=require("../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[6089,9216,9616,2386],()=>t(63584));module.exports=s})();