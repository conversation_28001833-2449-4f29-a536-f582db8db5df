"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[8601],{8601:(e,a,t)=>{t.r(a),t.d(a,{default:()=>w});var s=t(37876),r=t(14232),c=t(32890),l=t(56970),d=t(37784),n=t(33939),i=t(21772),o=t(11041),u=t(37308),h=t(31753),m=t(86207),x=t(91112),j=t(50457),p=t(44340),f=t(49487),g=t(39134),N=t(9227),y=t(48477),v=t(53718);let A="-country -description -hazard_type -created_at -region -start_date -status -syndrome -title -timeline -user -world_region -_id -part",w=e=>{var a,t,w,z,_,S,C,D,b,B;let{t:F,i18n:I}=(0,h.Bd)("common"),R="fr"===I.language?"en":I.language,[k,U]=(0,r.useState)({title:"",description:"",picture:""}),[E,H]=(0,r.useState)(!1),[L,O]=(0,r.useState)([]),[P,T]=(0,r.useState)([]),[Z,q]=(0,r.useState)([]),[K,M]=(0,r.useState)([]),[Q,V]=(0,r.useState)(!1),[G,J]=(0,r.useState)(!1),[W,X]=(0,r.useState)(!0),[Y]=(0,r.useState)(!1),[$,ee]=(0,r.useState)(""),[ea,et]=(0,r.useState)([]),[es,er]=(0,r.useState)([]),[ec,el]=(0,r.useState)([]),[ed]=(0,r.useState)([]),[en,ei]=(0,r.useState)([]),[eo,eu]=(0,r.useState)(!1),eh={query:{},limit:"~",sort:{title:"asc"}},em=async a=>{let t=[],s=[],r=[],c=await v.A.get("/hazard/".concat(e.routes[1]),a);c&&c.data&&c.data.length>0&&(c.data.forEach((e,a)=>{e.document&&e.document.length>0&&e.document.map((a,t)=>{a.description=e.document[t].docsrc,s.push(a)}),e.images&&e.images.length>0&&e.images.map((e,a)=>{r.push(e)}),e.images_src&&e.images_src.length>0&&e.images_src.map((e,a)=>{t.push(e)})}),et(s),el((null==r?void 0:r.reduce((e,a)=>{let t=e.find(e=>a._id===e._id);return t?t.count++:e.push({...a,count:1}),e},[])).flat(1/0)),ei(t.flat(1/0)))},ex=async a=>{let t=[],s=await v.A.get("/hazard/".concat(e.routes[1]),a);s&&s.data&&s.data.length>0&&(s.data.forEach((e,a)=>{e.document&&e.document.length>0&&e.document.map((a,s)=>{a.description=e.document[s].docsrc,t.push(a)})}),er(t))},ej=async a=>{H(!0);let t=await v.A.get("/hazard/".concat(e.routes[1]),a);t&&(t.picture=(null==t?void 0:t.picture)&&t.picture._id?"".concat("http://localhost:3001/api/v1","/image/show/").concat(t.picture._id):"/images/disease-placeholder.3f65b286.jpg",(null==t?void 0:t.picture_source)&&ee(t.picture_source),U(t),H(!1)),H(!1)},ep=async a=>{let t=await v.A.get("/hazard/".concat(e.routes[1],"/events/Closed"),a);O(t&&t.data?t.data:[])},ef=async a=>{let t=await v.A.get("/hazard/".concat(e.routes[1],"/events/Current"),a);T(t&&t.data?t.data:[])},eg=async a=>{let t=await v.A.get("/hazard/".concat(e.routes[1],"/operations"),a);q(t&&t.data?t.data:[])},eN=async a=>{let t=await v.A.get("/hazard/".concat(e.routes[1],"/institutions"),a);M(t&&t.data?t.data:[])};(0,r.useEffect)(()=>{e.routes&&e.routes[1]&&(ej({}),ep(eh),ef(eh),eg(eh),eN(eh),em({sort:{doc_created_at:"asc"},limit:"~",Doctable:!0,collation:"en",select:A}),ex({sort:{doc_created_at:"asc"},limit:"~",DocUpdatetable:!0,collation:"en",select:A}))},[]);let ey=()=>(0,s.jsxs)(c.A.Item,{eventKey:"1",children:[(0,s.jsxs)(c.A.Header,{onClick:()=>X(!W),children:[(0,s.jsx)("div",{className:"cardTitle",children:F("discussions")}),(0,s.jsx)("div",{className:"cardArrow",children:W?(0,s.jsx)(i.g,{icon:o.QLR,color:"#fff"}):(0,s.jsx)(i.g,{icon:o.EZy,color:"#fff"})})]}),(0,s.jsx)(c.A.Body,{children:(0,s.jsx)(y.A,{type:"hazard",id:e&&e.routes?e.routes[1]:null})})]});(0,m.canViewDiscussionUpdate)(()=>(0,s.jsx)(ey,{}));let ev=RegExp("^(http[s]?:\\/\\/(www\\.)?|ftp:\\/\\/(www\\.)?|www\\.){1}([0-9A-Za-z-\\.@:%_+~#=]+)+((\\.[a-zA-Z]{2,3})+)(/(.)*)?(\\?(.)*)?").test($);return(0,s.jsxs)("div",{className:"hazardDetails",children:[(0,s.jsx)(u.A,{routes:e.routes}),E||k.title?(0,s.jsxs)(s.Fragment,{children:[k&&k.picture?(0,s.jsxs)(s.Fragment,{children:[(a=k,t=R,(0,s.jsx)(x.default,{hazardData:a,currentLang:t})),(0,s.jsx)(l.A,{children:(0,s.jsx)(d.A,{className:"mt-2 ps-4",md:{span:6,offset:6},children:(0,s.jsx)("div",{children:(0,s.jsxs)("p",{className:" py-1",style:{fontSize:"12px"},children:[(0,s.jsxs)("i",{children:[F("imageSourceCredit"),": "]}),$?function(e,a){return e&&a?(0,s.jsx)("a",{target:"_blank",href:a,children:a}):(0,s.jsx)("span",{style:{color:"#234799"},children:a})}(ev,$):(0,s.jsx)("span",{style:{color:"#234799"},children:F("noSourceFound")})]})})})})]}):(0,s.jsx)("div",{className:"d-flex justify-content-center p-5",children:(0,s.jsx)(n.A,{animation:"grow"})}),(0,s.jsx)("br",{}),(0,s.jsxs)(l.A,{children:[(0,s.jsx)(d.A,{children:(w=F,z=P,(0,s.jsx)(f.default,{t:w,hazardCurrentEventData:z}))}),(0,s.jsx)(d.A,{children:(_=F,S=Z,(0,s.jsx)(j.default,{t:_,hazardOperationData:S}))})]}),(0,s.jsx)("br",{}),(0,s.jsxs)(l.A,{children:[(0,s.jsx)(d.A,{children:(C=F,D=K,(0,s.jsx)(p.default,{t:C,hazardInstitutionData:D}))}),(0,s.jsx)(d.A,{children:(b=F,B=L,(0,s.jsx)(g.default,{t:b,hazardPastEventData:B}))})]}),(0,s.jsx)(N.default,{t:F,images:ec,imgSrc:en,routeData:e,documentAccoirdianProps:{loading:Y,Document:ea,updateDocument:es,hazardDocSort:e=>{let a={sort:{},limit:"~",Doctable:!0,collation:"en",select:A};a.sort={[e.columnSelector]:e.sortDirection},em(a)},hazardDocUpdateSort:e=>{let a={sort:{},limit:"~",DocUpdatetable:!0,collation:"en",select:A};a.sort={[e.columnSelector]:e.sortDirection},ex(a)},docSrc:ed}})]}):(0,s.jsx)("div",{className:"nodataFound",children:F("vspace.Nodataavailable")})]})}},29335:(e,a,t)=>{t.d(a,{A:()=>A});var s=t(15039),r=t.n(s),c=t(14232),l=t(77346),d=t(37876);let n=c.forwardRef((e,a)=>{let{className:t,bsPrefix:s,as:c="div",...n}=e;return s=(0,l.oU)(s,"card-body"),(0,d.jsx)(c,{ref:a,className:r()(t,s),...n})});n.displayName="CardBody";let i=c.forwardRef((e,a)=>{let{className:t,bsPrefix:s,as:c="div",...n}=e;return s=(0,l.oU)(s,"card-footer"),(0,d.jsx)(c,{ref:a,className:r()(t,s),...n})});i.displayName="CardFooter";var o=t(81764);let u=c.forwardRef((e,a)=>{let{bsPrefix:t,className:s,as:n="div",...i}=e,u=(0,l.oU)(t,"card-header"),h=(0,c.useMemo)(()=>({cardHeaderBsPrefix:u}),[u]);return(0,d.jsx)(o.A.Provider,{value:h,children:(0,d.jsx)(n,{ref:a,...i,className:r()(s,u)})})});u.displayName="CardHeader";let h=c.forwardRef((e,a)=>{let{bsPrefix:t,className:s,variant:c,as:n="img",...i}=e,o=(0,l.oU)(t,"card-img");return(0,d.jsx)(n,{ref:a,className:r()(c?"".concat(o,"-").concat(c):o,s),...i})});h.displayName="CardImg";let m=c.forwardRef((e,a)=>{let{className:t,bsPrefix:s,as:c="div",...n}=e;return s=(0,l.oU)(s,"card-img-overlay"),(0,d.jsx)(c,{ref:a,className:r()(t,s),...n})});m.displayName="CardImgOverlay";let x=c.forwardRef((e,a)=>{let{className:t,bsPrefix:s,as:c="a",...n}=e;return s=(0,l.oU)(s,"card-link"),(0,d.jsx)(c,{ref:a,className:r()(t,s),...n})});x.displayName="CardLink";var j=t(46052);let p=(0,j.A)("h6"),f=c.forwardRef((e,a)=>{let{className:t,bsPrefix:s,as:c=p,...n}=e;return s=(0,l.oU)(s,"card-subtitle"),(0,d.jsx)(c,{ref:a,className:r()(t,s),...n})});f.displayName="CardSubtitle";let g=c.forwardRef((e,a)=>{let{className:t,bsPrefix:s,as:c="p",...n}=e;return s=(0,l.oU)(s,"card-text"),(0,d.jsx)(c,{ref:a,className:r()(t,s),...n})});g.displayName="CardText";let N=(0,j.A)("h5"),y=c.forwardRef((e,a)=>{let{className:t,bsPrefix:s,as:c=N,...n}=e;return s=(0,l.oU)(s,"card-title"),(0,d.jsx)(c,{ref:a,className:r()(t,s),...n})});y.displayName="CardTitle";let v=c.forwardRef((e,a)=>{let{bsPrefix:t,className:s,bg:c,text:i,border:o,body:u=!1,children:h,as:m="div",...x}=e,j=(0,l.oU)(t,"card");return(0,d.jsx)(m,{ref:a,...x,className:r()(s,j,c&&"bg-".concat(c),i&&"text-".concat(i),o&&"border-".concat(o)),children:u?(0,d.jsx)(n,{children:h}):h})});v.displayName="Card";let A=Object.assign(v,{Img:h,Title:y,Subtitle:f,Body:n,Link:x,Text:g,Header:u,Footer:i,ImgOverlay:m})},39134:(e,a,t)=>{t.r(a),t.d(a,{default:()=>n});var s=t(37876),r=t(29335),c=t(48230),l=t.n(c),d=t(31753);let n=e=>{let a=e.hazardPastEventData,{t}=(0,d.Bd)("common");return(0,s.jsx)(s.Fragment,{children:(0,s.jsx)("div",{className:"rki-carousel-card",children:(0,s.jsxs)(r.A,{className:"infoCard",children:[(0,s.jsx)(r.A.Header,{className:"text-center",children:t("hazardshow.pastevents")}),(0,s.jsx)(r.A.Body,{className:"hazardBody",children:a&&a.length>0?a.map((e,a)=>(0,s.jsx)("ul",{className:"ulItems",children:(0,s.jsxs)("li",{className:"liItems",children:[(0,s.jsx)(l(),{href:"/event/[...routes]",as:"/event/show/".concat(e._id),children:e&&e.title?"".concat(e.title):""},e._id),(0,s.jsxs)("span",{children:[" ","(",e&&e.country?"".concat(e.country.title):"",")"]})]},"index")})):(0,s.jsx)("span",{className:"text-center",children:t("noRecordFound")})})]})})})}},44340:(e,a,t)=>{t.r(a),t.d(a,{default:()=>n});var s=t(37876),r=t(29335),c=t(48230),l=t.n(c),d=t(31753);let n=e=>{let a=e.hazardInstitutionData,{t}=(0,d.Bd)("common");return(0,s.jsx)(s.Fragment,{children:(0,s.jsx)("div",{className:"rki-carousel-card",children:(0,s.jsxs)(r.A,{className:"infoCard",children:[(0,s.jsx)(r.A.Header,{className:"text-center",children:t("hazardshow.organisations")}),(0,s.jsx)(r.A.Body,{className:"hazardBody",children:a&&a.length>0?a.map((e,a)=>(0,s.jsx)("ul",{className:"ulItems",children:(0,s.jsxs)("li",{className:"liItems",children:[(0,s.jsx)(l(),{href:"/institution/[...routes]",as:"/institution/show/".concat(e._id),children:e&&e.title?"".concat(e.title):""}),(0,s.jsxs)("span",{children:[" ","(",e&&e.address&&e.address.country?"".concat(e.address.country.title):"",")"]})]},a)})):(0,s.jsx)("span",{className:"text-center",children:t("noRecordFound")})})]})})})}},49487:(e,a,t)=>{t.r(a),t.d(a,{default:()=>n});var s=t(37876),r=t(29335),c=t(48230),l=t.n(c),d=t(31753);let n=e=>{let a=e.hazardCurrentEventData,{t}=(0,d.Bd)("common");return(0,s.jsx)(s.Fragment,{children:(0,s.jsx)("div",{className:"rki-carousel-card",children:(0,s.jsxs)(r.A,{className:"infoCard",children:[(0,s.jsx)(r.A.Header,{className:"text-center",children:t("hazardshow.currentevents")}),(0,s.jsx)(r.A.Body,{className:"hazardBody",children:a&&a.length>0?a.map((e,a)=>(0,s.jsx)("ul",{className:"ulItems",children:(0,s.jsxs)("li",{className:"liItems",children:[(0,s.jsx)(l(),{href:"/event/[...routes]",as:"/event/show/".concat(e._id),children:e&&e.title?"".concat(e.title):""},e._id),(0,s.jsxs)("span",{children:[" ","(",e&&e.country?"".concat(e.country.title):"",")"]})]},a)})):(0,s.jsx)("span",{className:"text-center",children:t("noRecordFound")})})]})})})}},50457:(e,a,t)=>{t.r(a),t.d(a,{default:()=>n});var s=t(37876),r=t(48230),c=t.n(r),l=t(29335),d=t(31753);let n=e=>{let a=e.hazardOperationData,{t}=(0,d.Bd)("common");return(0,s.jsx)(s.Fragment,{children:(0,s.jsx)("div",{className:"rki-carousel-card",children:(0,s.jsxs)(l.A,{className:"infoCard",children:[(0,s.jsx)(l.A.Header,{className:"text-center",children:t("hazardshow.currentoperations")}),(0,s.jsx)(l.A.Body,{className:"hazardBody",children:a&&a.length>0?a.map((e,a)=>(0,s.jsx)("ul",{className:"ulItems",children:(0,s.jsxs)("li",{className:"liItems",children:[(0,s.jsx)(c(),{href:"/operation/[...routes]",as:"/operation/show/".concat(e._id),children:e&&e.title?"".concat(e.title):""},e._id),(0,s.jsxs)("span",{children:[" ","(",e&&e.country?"".concat(e.country.title):"",")"]})]},a)})):(0,s.jsx)("span",{className:"text-center",children:t("noSourceFound")})})]})})})}},81764:(e,a,t)=>{t.d(a,{A:()=>r});let s=t(14232).createContext(null);s.displayName="CardHeaderContext";let r=s},91112:(e,a,t)=>{t.r(a),t.d(a,{default:()=>d});var s=t(37876),r=t(56970),c=t(37784),l=t(72800);let d=e=>{let a=e.hazardData,t=e.currentLang;return(0,s.jsx)(s.Fragment,{children:(0,s.jsxs)(r.A,{children:[(0,s.jsxs)(c.A,{className:"ps-4",children:[(0,s.jsx)("h2",{children:a.title&&a.title[t]?a.title[t]:""}),(0,s.jsx)(l.A,{description:a.description&&a.description[t]?a.description[t]:""})]}),(0,s.jsx)(c.A,{style:{display:"flex"},children:(0,s.jsx)("img",{src:a.picture,style:{width:"100%",height:"400px",backgroundSize:"cover"},alt:"banner"})})]})})}}}]);
//# sourceMappingURL=8601-1185d09862d4fa36.js.map