"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[4694],{2582:(e,t,r)=>{r.r(t),r.d(t,{default:()=>p});var a=r(37876),n=r(14232),l=r(49589),s=r(37784),i=r(56970),d=r(29504),o=r(31427),c=r(12613),u=r(31753);let p=e=>{let[,t]=(0,n.useState)(!1),{validation:r,onChangeDate:p,startDate:m,endDate:h}=e,{t:x}=(0,u.Bd)("common");(0,n.useEffect)(()=>{null==h?t(!1):t(!0)},[h]);let j=t=>{e.getId(t)},g=t=>{e.getSourceCollection(t)};return(0,a.jsxs)(l.A,{className:"formCard",fluid:!0,children:[(0,a.jsx)(s.A,{className:"header-block",lg:12,children:(0,a.jsx)("h6",{children:(0,a.jsx)("span",{children:x("update.Date")})})}),(0,a.jsxs)(i.A,{children:[(0,a.jsxs)(s.A,{children:[(0,a.jsxs)(d.A.Group,{children:[(0,a.jsx)(d.A.Label,{className:"d-block required-field",children:x("update.StartDate")}),(0,a.jsx)(c.A,{selected:m,minDate:new Date,showTimeSelect:!0,timeIntervals:15,onChange:e=>p(e,"startDate"),placeholderText:x("update.Selectadate"),dateFormat:"MMMM d, yyyy h:mm aa"})]}),!0===r.startDate&&!m&&(0,a.jsx)("p",{style:{color:"red"},children:x("update.Pleaseenterthestartdate")})]}),(0,a.jsxs)(s.A,{children:[(0,a.jsxs)(d.A.Group,{children:[(0,a.jsx)(d.A.Label,{className:"d-block required-field",children:x("update.EndDate")}),(0,a.jsx)(c.A,{selected:h,showTimeSelect:!0,timeIntervals:15,onChange:e=>p(e,"endDate"),placeholderText:x("update.Selectadate"),minDate:m,dateFormat:"MMMM d, yyyy h:mm aa"})]}),!0===r.startDate&&!h&&(0,a.jsxs)("p",{style:{color:"red"},children:[x("update.Pleaseentertheenddate")," "]})]})]}),(0,a.jsx)(o.default,{data:e.data,srcText:e.imgSrc,getId:e=>j(e),getSourceCollection:e=>g(e)})]})}},5671:(e,t,r)=>{r.d(t,{x:()=>s});var a=r(37876),n=r(14232);let l=e=>{let{value:t,onChange:r,placeholder:l="Write something...",height:s=300,disabled:i=!1}=e,d=(0,n.useRef)(null),[o,c]=(0,n.useState)(!1);(0,n.useEffect)(()=>{d.current&&1&&!o&&d.current.innerHTML!==t&&(d.current.innerHTML=t||"")},[t,o]);let u=()=>{d.current&&r&&r(d.current.innerHTML)},p=(e,t)=>{if("undefined"!=typeof document){var r;document.execCommand(e,!1,t||""),u(),null==(r=d.current)||r.focus()}};return(0,a.jsx)("div",{className:"simple-rich-text-editor",style:{border:"1px solid #ccc"},children:(0,a.jsxs)(a.Fragment,{children:[(0,a.jsxs)("div",{className:"toolbar",style:{padding:"8px",borderBottom:"1px solid #ccc",background:"#f5f5f5"},children:[(0,a.jsx)("button",{type:"button",onClick:()=>p("bold"),style:{margin:"0 5px",padding:"3px 8px"},children:(0,a.jsx)("strong",{children:"B"})}),(0,a.jsx)("button",{type:"button",onClick:()=>p("italic"),style:{margin:"0 5px",padding:"3px 8px"},children:(0,a.jsx)("em",{children:"I"})}),(0,a.jsx)("button",{type:"button",onClick:()=>p("underline"),style:{margin:"0 5px",padding:"3px 8px"},children:(0,a.jsx)("u",{children:"U"})}),(0,a.jsx)("button",{type:"button",onClick:()=>p("insertOrderedList"),style:{margin:"0 5px",padding:"3px 8px"},children:"OL"}),(0,a.jsx)("button",{type:"button",onClick:()=>p("insertUnorderedList"),style:{margin:"0 5px",padding:"3px 8px"},children:"UL"}),(0,a.jsx)("button",{type:"button",onClick:()=>{let e=prompt("Enter the link URL");e&&p("createLink",e)},style:{margin:"0 5px",padding:"3px 8px"},children:"Link"})]}),(0,a.jsx)("div",{ref:d,contentEditable:!i,onInput:u,onFocus:()=>c(!0),onBlur:()=>c(!1),style:{padding:"15px",minHeight:s,maxHeight:2*s,overflow:"auto",outline:"none"},"data-placeholder":t?"":l,suppressContentEditableWarning:!0})]})})},s=e=>{let{initContent:t,onChange:r}=e;return(0,a.jsx)(l,{value:t||"",onChange:e=>r(e)})}},12613:(e,t,r)=>{r.d(t,{A:()=>l});var a=r(37876);r(14232);var n=r(56856);let l=e=>(0,a.jsx)(n.Ay,{...e})},29335:(e,t,r)=>{r.d(t,{A:()=>b});var a=r(15039),n=r.n(a),l=r(14232),s=r(77346),i=r(37876);let d=l.forwardRef((e,t)=>{let{className:r,bsPrefix:a,as:l="div",...d}=e;return a=(0,s.oU)(a,"card-body"),(0,i.jsx)(l,{ref:t,className:n()(r,a),...d})});d.displayName="CardBody";let o=l.forwardRef((e,t)=>{let{className:r,bsPrefix:a,as:l="div",...d}=e;return a=(0,s.oU)(a,"card-footer"),(0,i.jsx)(l,{ref:t,className:n()(r,a),...d})});o.displayName="CardFooter";var c=r(81764);let u=l.forwardRef((e,t)=>{let{bsPrefix:r,className:a,as:d="div",...o}=e,u=(0,s.oU)(r,"card-header"),p=(0,l.useMemo)(()=>({cardHeaderBsPrefix:u}),[u]);return(0,i.jsx)(c.A.Provider,{value:p,children:(0,i.jsx)(d,{ref:t,...o,className:n()(a,u)})})});u.displayName="CardHeader";let p=l.forwardRef((e,t)=>{let{bsPrefix:r,className:a,variant:l,as:d="img",...o}=e,c=(0,s.oU)(r,"card-img");return(0,i.jsx)(d,{ref:t,className:n()(l?"".concat(c,"-").concat(l):c,a),...o})});p.displayName="CardImg";let m=l.forwardRef((e,t)=>{let{className:r,bsPrefix:a,as:l="div",...d}=e;return a=(0,s.oU)(a,"card-img-overlay"),(0,i.jsx)(l,{ref:t,className:n()(r,a),...d})});m.displayName="CardImgOverlay";let h=l.forwardRef((e,t)=>{let{className:r,bsPrefix:a,as:l="a",...d}=e;return a=(0,s.oU)(a,"card-link"),(0,i.jsx)(l,{ref:t,className:n()(r,a),...d})});h.displayName="CardLink";var x=r(46052);let j=(0,x.A)("h6"),g=l.forwardRef((e,t)=>{let{className:r,bsPrefix:a,as:l=j,...d}=e;return a=(0,s.oU)(a,"card-subtitle"),(0,i.jsx)(l,{ref:t,className:n()(r,a),...d})});g.displayName="CardSubtitle";let y=l.forwardRef((e,t)=>{let{className:r,bsPrefix:a,as:l="p",...d}=e;return a=(0,s.oU)(a,"card-text"),(0,i.jsx)(l,{ref:t,className:n()(r,a),...d})});y.displayName="CardText";let v=(0,x.A)("h5"),f=l.forwardRef((e,t)=>{let{className:r,bsPrefix:a,as:l=v,...d}=e;return a=(0,s.oU)(a,"card-title"),(0,i.jsx)(l,{ref:t,className:n()(r,a),...d})});f.displayName="CardTitle";let A=l.forwardRef((e,t)=>{let{bsPrefix:r,className:a,bg:l,text:o,border:c,body:u=!1,children:p,as:m="div",...h}=e,x=(0,s.oU)(r,"card");return(0,i.jsx)(m,{ref:t,...h,className:n()(a,x,l&&"bg-".concat(l),o&&"text-".concat(o),c&&"border-".concat(c)),children:u?(0,i.jsx)(d,{children:p}):p})});A.displayName="Card";let b=Object.assign(A,{Img:p,Title:f,Subtitle:g,Body:d,Link:h,Text:y,Header:u,Footer:o,ImgOverlay:m})},35611:(e,t,r)=>{r.d(t,{sx:()=>o,s3:()=>n.s3,ks:()=>n.ks,yk:()=>a.A});var a=r(54773),n=r(59200),l=r(37876),s=r(14232),i=r(39593),d=r(29504);let o={RadioGroup:e=>{let{name:t,valueSelected:r,onChange:a,errorMessage:n,children:d}=e,{errors:o,touched:c}=(0,i.j7)(),u=c[t]&&o[t];s.useMemo(()=>({name:t}),[t]);let p=s.Children.map(d,e=>s.isValidElement(e)&&function(e){return"object"==typeof e&&null!==e}(e.props)?s.cloneElement(e,{name:t,...e.props}):e);return(0,l.jsxs)("div",{children:[(0,l.jsx)("div",{className:"radio-group",children:p}),u&&(0,l.jsx)("div",{className:"invalid-feedback d-block",children:n||("string"==typeof o[t]?o[t]:String(o[t]))})]})},RadioItem:e=>{let{id:t,label:r,value:a,name:n,disabled:s}=e,{values:o,setFieldValue:c}=(0,i.j7)(),u=n||t;return(0,l.jsx)(d.A.Check,{type:"radio",id:t,label:r,value:a,name:u,checked:o[u]===a,onChange:e=>{c(u,e.target.value)},disabled:s,inline:!0})}};a.A,n.ks,n.s3},44117:(e,t,r)=>{r.r(t),r.d(t,{default:()=>c});var a=r(37876);r(14232);var n=r(49589),l=r(56970),s=r(37784),i=r(29504),d=r(35611),o=r(31753);let c=e=>{let{t}=(0,o.Bd)("common"),{title:r,onHandleChange:c}=e;return(0,a.jsx)(n.A,{className:"formCard",fluid:!0,children:(0,a.jsx)(l.A,{children:(0,a.jsx)(s.A,{children:(0,a.jsxs)(i.A.Group,{children:[(0,a.jsxs)(i.A.Label,{className:"required-field",children:[t("update.Conversation")," ",t("update.Title")]}),(0,a.jsx)(d.ks,{name:"title",id:"title",required:!0,value:r,onChange:c,validator:e=>""!=e.trim(),errorMessage:{validator:t("Pleaseprovideconversationatitle")}})]})})})})}},44694:(e,t,r)=>{r.r(t),r.d(t,{__N_SSG:()=>L,default:()=>R});var a=r(37876),n=r(14232),l=r(49589),s=r(29335),i=r(56970),d=r(37784),o=r(29504),c=r(60282),u=r(89099),p=r.n(u),m=r(10841),h=r.n(m),x=r(82851),j=r.n(x),g=r(35611),y=r(54773),v=r(76936),f=r(44117),A=r(31427),b=r(78612),C=r(2582),k=r(72983),_=r(53718),N=r(31753),S=r(5671);let w={title:"",description:null,startDate:null,endDate:null,classification:null,parentType:null,parentId:null,showAsAnnouncement:!1,images:[],document:[],images_src:[],doc_src:[]},D={title:"",description:null,startDate:null,endDate:null,classification:null,showAsAnnouncement:!1,images:[],document:[],images_src:[],doc_src:[]},I={telephoneNo:"",mobileNo:""},T=[{title:"",link:""}],q={startDate:!1,endDate:!1};var L=!0;let R=e=>{let t=(0,n.useRef)(null),{t:r}=(0,N.Bd)("common"),{query:m}=(0,u.useRouter)(),[x,L]=(0,n.useState)(w),[R,U]=(0,n.useState)({title:""}),[E,M]=(0,n.useState)([{title:"",link:""}]),[F,P]=(0,n.useState)(I),[B,G]=(0,n.useState)(q),[,H]=(0,n.useState)(!1),[z,V]=(0,n.useState)(""),[O,W]=(0,n.useState)([]),[K,Z]=(0,n.useState)([]),[J,Q]=(0,n.useState)([]),[X,Y]=(0,n.useState)([]),$=e.router&&e.router.query&&e.router.query.routes&&e.router.query.routes.length>1&&e.router.query.routes[1],ee=e=>{let{name:t,value:r}=e.target;U(e=>({...e,[t]:r}))},et=e=>{let{name:t,value:r}=e.target;P(e=>({...e,[t]:r}))},er=(e,t)=>{let{name:r,value:a}=e.target,n=[...E];n[t][r]=a,M(n)},ea=(e,t)=>{E.splice(t,1),M([...E]),0===E.length&&en()},en=()=>{let e={title:"",link:""};M(t=>[...t,e])},el=e=>{L(t=>({...t,description:e}))},es=(e,t)=>{L(r=>({...r,[t]:e})),G(e=>({...e,startDate:"startDate"===t,endDate:"endDate"===t}))},ei=()=>!!x.startDate||(G(e=>({...e,startDate:!0})),!1),ed=e=>{let{startDate:t,endDate:r}=x;if("Calendar Event"===z&&(G(e=>({...e,startDate:!0})),!t||!r))return e.preventDefault(),!0;if("Link"===z){let t=j().findIndex(E,{title:""}),r=j().findIndex(E,{link:""});if(-1!==t||-1!==r)return e.preventDefault(),!0}},eo=async t=>{t.type=x.parentType;let r=await _.A.patch("/updates/".concat(e.router.query.routes[1]),t);r&&r._id&&p().push("/".concat(x.parentType,"/[...routes]"),"/".concat(x.parentType,"/show/").concat(x.parentId))},ec=async t=>{switch(e.router.query.parent_type){case"operation":t.parent_operation=e.router.query.parent_id;break;case"event":t.parent_event=e.router.query.parent_id;break;case"project":t.parent_project=e.router.query.parent_id;break;case"vspace":t.parent_vspace=e.router.query.parent_id;break;case"country":t.parent_country=e.router.query.parent_id;break;case"hazard":t.parent_hazard=e.router.query.parent_id;break;case"institution":t.parent_institution=e.router.query.parent_id}let r=await _.A.post("/updates",t);r&&r._id&&p().push("/".concat(e.router.query.parent_type,"/[...routes]"),"/".concat(e.router.query.parent_type,"/show/").concat(e.router.query.parent_id))},eu=async(t,r)=>{let{title:a,description:n,startDate:l,endDate:s,showAsAnnouncement:i,document:d,doc_src:o,images:c,images_src:u}=r||x;if(ed(t))return;if(t&&t.preventDefault&&t.preventDefault(),t&&t.currentTarget&&!1===ei()&&!1===t.currentTarget.checkValidity())return void t.stopPropagation();let p=E.find(e=>""==e.title||""==e.link);if("Link"==z&&p)return;H(!0);let m={title:"Conversation"===z?R.title.trim():a.trim(),type:e.router.query.parent_type,description:n,show_as_announcement:i,start_date:l,end_date:s,update_type:e.router.query.update_type,link:E,document:d||[],contact_details:F||{},images:c||[],images_src:u||[],doc_src:o||[]};e.router.query&&e.router.query.routes.length>1&&e.router.query.routes[1]?eo(m):ec(m)},ep=async()=>{let t=await _.A.get("/updateType/".concat(e.router.query.update_type));t&&V(t.title)},em=(e,t)=>{switch(t.type){case"operation":e.parentId=t.parent_operation;break;case"event":e.parentId=t.parent_event;break;case"project":e.parentId=t.parent_project;break;case"vspace":e.parentId=t.parent_vspace;break;case"country":e.parentId=t.parent_country;break;case"hazard":e.parentId=t.parent_hazard;break;case"institution":e.parentId=t.parent_institution}},eh=(e,t)=>{W(e.document?e.document:[]),Z(e.doc_src?e.doc_src:[]),Q(e.images?e.images:[]),Y(e.images_src?e.images_src:[]),M(e.link.length?e.link:T),P(e.contact_details?e.contact_details:F),em(t,e)};(0,n.useEffect)(()=>{let t=async()=>{let t=await _.A.get("/updates/".concat(e.router.query.routes[1])),r={title:t.title,description:t.description,startDate:t.start_date?h()(t.start_date).toDate():null,endDate:t.end_date?h()(t.end_date).toDate():null,classification:null,parentType:t.type,showAsAnnouncement:t.show_as_announcement,document:t.document?t.document:[],doc_src:t.doc_src?t.doc_src:[],images:t.images?t.images:[],images_src:t.images_src?t.images_src:[]};eh(t,r),L(r)};ep(),$&&t()},[]);let ex=e=>{let t=e.map(e=>e.serverID);L(e=>({...e,document:t}))},ej=e=>{let t=e.map(e=>e.serverID);L(e=>({...e,images:t}))},eg=e=>{L(t=>({...t,images_src:e}))},ey=e=>{L(t=>({...t,doc_src:e}))};return(0,a.jsx)(l.A,{className:"formCard",fluid:!0,children:(0,a.jsx)(s.A,{children:(0,a.jsx)(y.A,{onSubmit:eu,ref:t,initialValues:x,enableReinitialize:!0,onErrorSubmit:e=>{window.scrollTo(0,0)},children:(0,a.jsxs)(s.A.Body,{children:[(0,a.jsx)(i.A,{children:(0,a.jsx)(d.A,{children:(0,a.jsxs)(s.A.Title,{children:["".concat(r($?"update.Edit":"update.Create"))," ",r("update.Update")," "]})})}),(0,a.jsx)("hr",{}),(0,a.jsx)(i.A,{children:(0,a.jsx)(d.A,{children:(0,a.jsxs)(o.A.Group,{children:[(0,a.jsx)(o.A.Label,{className:"required-field",children:r("update.Title")}),(0,a.jsx)(g.ks,{required:!0,validator:e=>""!==String(e||"").trim(),errorMessage:{validator:r("update.provide")},type:"text",name:"title",value:x.title,onChange:e=>{let{name:t,value:r}=e.target;L(e=>({...e,[t]:r}))}})]})})}),(0,a.jsx)(i.A,{children:(0,a.jsx)(d.A,{children:(0,a.jsxs)(o.A.Group,{children:[(0,a.jsx)(o.A.Label,{children:r("update.Description")}),(0,a.jsx)(S.x,{initContent:x.description,onChange:e=>el(e)})]})})}),(()=>{switch(z){case"Conversation":return(0,a.jsx)(f.default,{onHandleChange:ee,...R});case"Link":return(0,a.jsx)(v.default,{link:E,handleChangeforTimeline:er,removeForm:ea,addform:en});case"Document":return(0,a.jsx)(A.default,{srcText:K,getSourceCollection:e=>ey(e),data:O,getId:e=>ex(e)});case"Contact":return(0,a.jsx)(b.default,{onHandleChange:et,...F});case"Calendar Event":return(0,a.jsx)(C.default,{imgSrc:K,getSourceCollection:e=>ey(e),validation:B,onChangeDate:es,...x,data:O,getId:e=>ex(e)});case"Image":return(0,a.jsx)(k.default,{data:J,imgSrc:X,getId:e=>ej(e),getSourceCollection:e=>eg(e)});case"General / Notice":return(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(A.default,{srcText:K,getSourceCollection:e=>ey(e),data:O,getId:e=>ex(e)}),(0,a.jsx)(k.default,{imgSrc:X,getSourceCollection:e=>eg(e),data:J,getId:e=>ej(e)})]});default:return null}})(),(0,a.jsx)(i.A,{className:"mt-3",children:(0,a.jsx)(d.A,{children:(0,a.jsx)(o.A.Group,{controlId:"showAsAnnouncement",children:(0,a.jsx)(o.A.Check,{className:"check",id:"showAsAnnouncement",name:"showAsAnnouncement",type:"checkbox",label:r("update.Showasanannouncement"),checked:x.showAsAnnouncement,onChange:e=>{let{checked:t}=e.target;L(e=>({...e,showAsAnnouncement:t}))}})})})}),(0,a.jsx)(i.A,{className:"my-4",children:(0,a.jsxs)(d.A,{children:[(0,a.jsx)(c.A,{className:"me-2",type:"submit",variant:"primary",onClick:()=>{G(e=>({...e,startDate:!0}))},children:r("submit")}),(0,a.jsx)(c.A,{className:"me-2",onClick:()=>{L(e=>({...e,...D})),W([]),Z([]),Q([]),Y([]),P(I),M(T),window.scrollTo(0,0)},variant:"info",children:r("reset")}),(0,a.jsx)(c.A,{onClick:()=>{m&&"add"===m.routes[0]?p().push("/".concat(m.parent_type,"/show/").concat(m.parent_id)):e&&e.router&&e.router.query&&p().push("/".concat(x.parentType,"/[...routes]"),"/".concat(x.parentType,"/show/").concat(x.parentId))},variant:"secondary",children:r("Cancel")})]})})]})})})})}},54773:(e,t,r)=>{r.d(t,{A:()=>d});var a=r(37876),n=r(14232),l=r(39593),s=r(91408);let i=(0,n.forwardRef)((e,t)=>{let{children:r,onSubmit:n,autoComplete:i,className:d,onKeyPress:o,initialValues:c,...u}=e,p=s.Ik().shape({});return(0,a.jsx)(l.l1,{initialValues:c||{},validationSchema:p,onSubmit:(e,t)=>{let r={preventDefault:()=>{},stopPropagation:()=>{},currentTarget:null,target:null,nativeEvent:new Event("submit"),bubbles:!1,cancelable:!0,defaultPrevented:!1,eventPhase:0,isTrusted:!1,timeStamp:Date.now(),type:"submit",isDefaultPrevented:()=>!1,isPropagationStopped:()=>!1,persist:()=>{}};n&&n(r,e,t)},...u,children:e=>(0,a.jsx)(l.lV,{ref:t,onSubmit:e.handleSubmit,autoComplete:i,className:d,onKeyPress:o,children:"function"==typeof r?r(e):r})})});i.displayName="ValidationFormWrapper";let d=i},59200:(e,t,r)=>{r.d(t,{ks:()=>s,s3:()=>i});var a=r(37876);r(14232);var n=r(29504),l=r(39593);let s=e=>{let{name:t,id:r,required:s,validator:i,errorMessage:d,onChange:o,value:c,as:u,multiline:p,rows:m,pattern:h,...x}=e;return(0,a.jsx)(l.D0,{name:t,validate:e=>{let t="string"==typeof e?e:String(e||"");return s&&(!e||""===t.trim())?(null==d?void 0:d.validator)||"This field is required":i&&!i(e)?(null==d?void 0:d.validator)||"Invalid value":h&&e&&!new RegExp(h).test(e)?(null==d?void 0:d.pattern)||"Invalid format":void 0},children:e=>{let{field:t,meta:l}=e;return(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(n.A.Control,{...t,...x,id:r,as:u||"input",rows:m,isInvalid:l.touched&&!!l.error,onChange:e=>{t.onChange(e),o&&o(e)},value:void 0!==c?c:t.value}),l.touched&&l.error?(0,a.jsx)(n.A.Control.Feedback,{type:"invalid",children:l.error}):null]})}})},i=e=>{let{name:t,id:r,required:s,errorMessage:i,onChange:d,value:o,children:c,...u}=e;return(0,a.jsx)(l.D0,{name:t,validate:e=>{if(s&&(!e||""===e))return(null==i?void 0:i.validator)||"This field is required"},children:e=>{let{field:t,meta:l}=e;return(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(n.A.Control,{as:"select",...t,...u,id:r,isInvalid:l.touched&&!!l.error,onChange:e=>{t.onChange(e),d&&d(e)},value:void 0!==o?o:t.value,children:c}),l.touched&&l.error?(0,a.jsx)(n.A.Control.Feedback,{type:"invalid",children:l.error}):null]})}})}},72983:(e,t,r)=>{r.r(t),r.d(t,{default:()=>o});var a=r(37876);r(14232);var n=r(49589),l=r(37784),s=r(56970),i=r(89673),d=r(31753);let o=e=>{let{t}=(0,d.Bd)("common"),r=t=>{e.getId(t)},o=t=>{e.getSourceCollection(t)};return(0,a.jsx)(n.A,{className:"formCard",fluid:!0,children:(0,a.jsxs)(l.A,{children:[(0,a.jsx)(s.A,{className:"header-block",lg:12,children:(0,a.jsx)("h6",{children:(0,a.jsx)("span",{children:t("update.Image")})})}),(0,a.jsx)(s.A,{children:(0,a.jsx)(i.A,{datas:e.data,srcText:e.imgSrc,getImgID:e=>r(e),getImageSource:e=>o(e)})})]})})}},76936:(e,t,r)=>{r.r(t),r.d(t,{default:()=>p});var a=r(37876);r(14232);var n=r(49589),l=r(29335),s=r(56970),i=r(37784),d=r(29504),o=r(60282),c=r(31753),u=r(54773);let p=e=>{let{t}=(0,c.Bd)("common"),{link:r,handleChangeforTimeline:p,removeForm:m,addform:h}=e;return(0,a.jsx)(n.A,{className:"formCard",fluid:!0,children:(0,a.jsx)(l.A,{children:(0,a.jsx)(u.A,{onSubmit:()=>{},children:(0,a.jsxs)(l.A.Body,{children:[r&&r.map((e,r)=>(0,a.jsx)("div",{children:(0,a.jsxs)(s.A,{children:[(0,a.jsx)(i.A,{children:(0,a.jsxs)(d.A.Group,{children:[(0,a.jsx)(d.A.Label,{className:"required-field",children:t("update.Title")}),(0,a.jsx)(d.A.Control,{name:"title",id:"timetitle",type:"text",value:e.title,required:!0,onChange:e=>p(e,r)}),(0,a.jsx)(d.A.Control.Feedback,{type:"invalid",children:t("update.TitleisRequired")})]})}),(0,a.jsx)(i.A,{children:(0,a.jsxs)(d.A.Group,{children:[(0,a.jsx)(d.A.Label,{className:"required-field",children:t("update.Link")}),(0,a.jsx)(d.A.Control,{name:"link",id:"link",type:"text",required:!0,value:e.link,onChange:e=>p(e,r),pattern:"http(s)?://??[\\w.-]+[-a-zA-Z0-9@:%._\\+~#=]{2,256}\\.[a-z]{2,6}.+"}),(0,a.jsx)(d.A.Control.Feedback,{type:"invalid",children:t("update.Providevalidlink")})]})}),(0,a.jsx)(i.A,{children:(0,a.jsx)(d.A.Group,{children:0===r?(0,a.jsx)("div",{}):(0,a.jsx)(o.A,{variant:"secondary",style:{marginTop:"30px"},onClick:e=>m(e,r),children:t("update.Remove")})})})]})})),(0,a.jsx)(s.A,{children:(0,a.jsx)(i.A,{md:!0,lg:"4",children:(0,a.jsx)(o.A,{variant:"secondary",style:{marginTop:"27px",marginBottom:"20px"},onClick:h,children:t("update.ADD")})})})]})})})})}},78612:(e,t,r)=>{r.r(t),r.d(t,{default:()=>o});var a=r(37876);r(14232);var n=r(49589),l=r(56970),s=r(37784),i=r(29504),d=r(31753);let o=e=>{let{t}=(0,d.Bd)("common"),{onHandleChange:r,telephoneNo:o,mobileNo:c}=e;return(0,a.jsx)(n.A,{className:"formCard",fluid:!0,children:(0,a.jsx)(l.A,{children:(0,a.jsxs)(s.A,{children:[(0,a.jsxs)(i.A.Group,{children:[(0,a.jsx)(i.A.Label,{className:"required-field",children:t("Updates.TelephoneNo")}),(0,a.jsx)(i.A.Control,{type:"number",name:"telephoneNo",placeholder:t("Updates.TelephoneNumber"),required:!0,value:o,onChange:r}),(0,a.jsx)(i.A.Control.Feedback,{type:"invalid",children:t("Updates.PleaseTelephoneNumber")})]}),(0,a.jsxs)(i.A.Group,{children:[(0,a.jsx)(i.A.Label,{className:"required-field",children:t("Updates.MobileNo")}),(0,a.jsx)(i.A.Control,{type:"number",name:"mobileNo",placeholder:t("Updates.MobileNumber"),required:!0,value:c,onChange:r}),(0,a.jsx)(i.A.Control.Feedback,{type:"invalid",children:t("Updates.PleaseProvideMobile")})]})]})})})}},81764:(e,t,r)=>{r.d(t,{A:()=>n});let a=r(14232).createContext(null);a.displayName="CardHeaderContext";let n=a}}]);
//# sourceMappingURL=4694-2e5d54e4a3736d79.js.map