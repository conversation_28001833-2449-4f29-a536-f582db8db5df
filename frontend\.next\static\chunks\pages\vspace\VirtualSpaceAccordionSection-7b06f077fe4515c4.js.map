{"version": 3, "file": "static/chunks/pages/vspace/VirtualSpaceAccordionSection-7b06f077fe4515c4.js", "mappings": "wMAuKA,MAnIoB,IAClB,GAAM,GAAEA,CAAC,CAAE,CAAGC,CAAAA,EAAAA,EAAAA,EAAAA,CAAcA,CAAC,MAkIhBC,IAjIP,CAACC,EAAQC,EAAU,CAAGC,CAAAA,EAiIH,EAjIGA,QAAAA,CAAQA,CAAc,EAAE,EAG9CC,EAAoB,IACxB,IAAMC,EAAc,8EAA8EC,IAAI,CAACC,EAAKC,WAAW,EAEvH,MACE,WAACC,MAAAA,CAAIC,UAAU,4BACb,WAACC,IAAAA,CAAED,UAAU,iBACX,UAACE,IAAAA,UAAGd,EAAE,cAAgB,IAAES,EAAKM,YAAY,EAAI,mBAE9CN,EAAKC,WAAW,EACf,WAACC,MAAAA,CAAIC,UAAU,wBACb,UAACC,IAAAA,UAAE,UAACC,IAAAA,UAAGd,EAAE,cACRO,EACC,WAACI,MAAAA,WACC,UAACK,EAAAA,CAAeA,CAAAA,CAACC,KAAMC,EAAAA,GAAMA,CAAEC,KAAK,KAAKC,MAAM,OAAOR,UAAU,SAChE,UAACS,IAAAA,CAAET,UAAU,cAAcU,KAAMb,EAAKC,WAAW,CAAEa,OAAO,SAASC,IAAI,+BACpEf,EAAKC,WAAW,MAIrB,UAACC,MAAAA,UACC,UAACE,IAAAA,CAAED,UAAU,YAAYa,MAAO,CAAEC,UAAW,WAAY,WACtDjB,EAAKC,WAAW,QAM1BD,EAAKkB,YAAY,EAChB,WAACC,EAAAA,CAAMA,CAAAA,CAAChB,UAAU,qCAAqCU,KAAMb,EAAKkB,YAAY,WAC3E3B,EAAE,YACH,UAACgB,EAAAA,CAAeA,CAAAA,CAACC,KAAMY,EAAAA,GAAUA,CAAEV,KAAK,KAAKP,UAAU,cAKjE,EA6CA,MA3CAkB,CAAAA,EAAAA,EAAAA,SAAAA,CAASA,CAAC,KACR,IAAMC,EAA8B,EAAE,CACtCC,GAASA,EAAMC,OAAO,EAAIC,MAAMC,OAAO,CAACH,EAAMC,OAAO,GAAKD,EAAMC,OAAO,CAACG,GAAG,CAAC,CAAC3B,EAAM4B,KACjF,IACIC,EADEC,EAAW9B,GAAQA,EAAK+B,IAAI,CAACC,KAAK,CAAC,KAAKC,GAAG,GAGjD,OAAQH,GACN,IAAK,MACL,IAAK,MACL,IAAK,OACL,IAAK,MACHD,EAAS,GAAwC7B,MAAAA,CAArCkC,8BAAsB,CAAC,gBAAuB,OAATlC,EAAKmC,GAAG,EACzD,KACF,KAAK,MACHN,EAAS,gCACT,KACF,KAAK,OACHA,EAAS,iCACT,KACF,KAAK,MACL,IAAK,OACHA,EAAS,gCACT,KACF,SACEA,EAAS,iCACb,CAEA,IAAMO,EAAQ,CAAc,SAAbN,GAAoC,QAAbA,GAAmC,QAAbA,GAAmC,SAAbA,CAAa,CAAK,EAC/F,GAA4C9B,MAAAA,CAAzCkC,8BAAsB,CAAC,oBAA2B,OAATlC,EAAKmC,GAAG,EACnDE,EAAQ,GAAqE,OAAlErC,GAAQA,EAAKsC,aAAa,CAAGtC,EAAKsC,aAAa,CAAG,iBAC7DC,EAAehB,EAAMiB,WAAW,EAAIf,MAAMC,OAAO,CAACH,EAAMiB,WAAW,GACpEjB,EAAMiB,WAAW,CAACC,MAAM,CAAG,EAAIlB,EAAMiB,WAAW,CAACZ,EAAE,CAAG,GAE3DN,EAAeoB,IAAI,CAAC,CAClBC,IAAKd,EACL5B,YAAasC,EACbjC,aAAc+B,EACdnB,aAAckB,CAChB,EACF,GACAzC,EAAU2B,EACZ,EAAG,CAACC,EAAM,EAGR,UAACrB,MAAAA,UACER,GAA4B,IAAlBA,EAAO+C,MAAM,CACtB,UAACvC,MAAAA,CAAIC,UAAU,wCACb,UAACC,IAAAA,CAAED,UAAU,wDAAgDZ,EAAE,qBAGjE,UAACqD,EAAAA,EAAQA,CAAAA,CACPC,YAAY,EACZC,YAAY,EACZC,gBAAgB,EAChBC,cAAc,EACdC,mBAAmB,EACnBC,UAAU,EACVC,aAAa,EACbC,WAAW,EACXC,eAAe,EACfC,cAAc,EACdC,aAAc,IACZ7D,EAAOiC,GAAG,CAAC,CAAC3B,EAAMwD,IAChB,UAACC,MAAAA,CAECd,IAAK3C,EAAK2C,GAAG,CACbe,IAAK,aAAuB,OAAVF,EAAQ,GAC1BxC,MAAO,CAAE2C,MAAO,OAAQC,OAAQ,OAAQC,UAAW,OAAQ,GAHtDL,aAQV9D,EAAOiC,GAAG,CAAC,CAAC3B,EAAMwD,IACjB,WAACtD,MAAAA,WACC,UAACuD,MAAAA,CACCd,IAAK3C,EAAK2C,GAAG,CACbe,IAAK1D,EAAKM,YAAY,EAAI,gBAC1BU,MAAO,CAAE8C,UAAW,QAASD,UAAW,SAAU,IAEnDhE,EAAkBG,KANXwD,OActB,2ECrIA,MAVA,cACA,MAAkB,YAAM,IASM,CAR5B,eAAS,MACX,cACA,aACA,MACA,CACA,UACA,CAAG,GACH,4FCzBA,IAAMO,EAA+BC,EAAAA,UAAgB,CAAC,GAKnDC,QALoD,GAApB,QACjC9D,CAAS,UACT+D,CAAQ,CACRC,GAAIC,EAAY,KAAK,CACrB,GAAG7C,EACJ,GAEC,OAAO,EADI8C,CAAAA,EAAAA,EAAAA,EAAAA,CAAkBA,CAACH,EAAU,oBACpBI,CAAAA,EAAAA,EAAAA,GAAAA,CAAIA,CAACF,EAAW,CAClCH,IAAKA,EACL9D,UAAWoE,IAAWpE,EAAW+D,GACjC,GAAG3C,CAAK,EAEZ,GACAwC,EAJyBQ,WAIE,CAAG,kBCb9B,IAAMC,EAA4BR,EAAAA,UAAgB,CAAC,GAMhDC,QAN6B,CAE9BE,CADA,EACIC,EAAY,KAAK,UACrBF,CAAQ,WACR/D,CAAS,CACT,GAAGoB,EACJ,GACOkD,EAAiBF,IAAWpE,EAAWkE,CAAAA,EAAAA,EAAAA,EAAAA,CAAkBA,CAACH,EAAU,GAAzCK,eACjC,MAAoBD,CAAb,EAAaA,EAAAA,GAAAA,CAAIA,CAACF,EAAW,CAClCH,IAAKA,EACL,GAAG1C,CAAK,CACRpB,UAAWsE,CACb,EACF,GACAD,EAAaE,WAAW,CAAG,iBAbkI,8CCsB7J,IAAM9B,EAGNoB,EAAAA,OAFA,GAEgB,CAAC,GAGdC,IALQ,GACX,EA2EMU,EA1EY,oBAChBC,EAAqB,CAAC,CACtB,GAAGC,EACJ,GACO,CAEJV,CADA,EACIC,EAAY,IAP0B,CAOrB,UACrBF,CAAQ,OACRY,GAAQ,CAAI,MACZC,GAAO,CAAK,UACZC,GAAW,CAAI,CACfC,cAAa,CAAI,iBACjBC,EAAkB,EAAE,aACpBC,CAAW,UACXC,CAAQ,CACRC,SAAO,QACPC,CAAM,UACNC,EAAW,GAAI,IAZ4I,MAa3JC,GAAW,CAAI,WACfC,CAAS,OACTC,EAAQ,OAAO,aACfC,CAAW,YACXC,CAAU,MACVC,GAAO,CAAI,OACXC,GAAQ,CAAI,CACZC,cAAY,aACZC,CAAW,YACXC,CAAU,UACVC,EAAwB5B,CAAAA,EAAAA,EAAAA,GAAAA,CAAb,CAAkB,OAAQ,CACnC,EADoB,YACL,OACfnE,UAAW,4BACb,EAAE,CACFgG,YAAY,UAAU,UACtBC,EAAwB9B,CAAAA,EAAAA,EAAAA,GAAAA,CAAb,CAAkB,OAAQ,CACnC,EADoB,YACL,OACfnE,UAAW,4BACb,EAAE,WACFkG,EAAY,MAAM,SAClBC,CAAO,WACPnG,CAAS,UACToG,CAAQ,CACR,GAAGhF,EACJ,CAAGiF,CAAAA,EAAAA,EAAAA,EAAAA,CAAeA,CAAC,oBAClB5B,EACA,GAAGC,CAAiB,EACnB,CACDM,YAAa,UACf,GACMsB,EAASpC,CAAAA,EAAAA,EAAAA,EAAAA,CAAkBA,CAACH,EAAU,YACtCwC,EAAQC,CAAAA,EAAAA,EAAAA,EAAAA,CAAQA,GAChBC,EAAmBC,CAAAA,EAAAA,EAAAA,MAAAA,CAAMA,CAAC,MAC1B,CAACC,EAAWC,EAAa,CAAGnH,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAC,QACrC,CAACoH,GAAQC,GAAU,CAAGrH,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,EAAC,GAC/B,CAACsH,GAAWC,GAAa,CAAGvH,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAC,IACrC,CAACwH,GAAqBC,GAAuB,CAAGzH,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAACuF,GAAe,GAC9E9D,CAAAA,EAAAA,EAAAA,SAAAA,CAASA,CAAC,KACH6F,IAAa/B,IAAgBiC,KAC5BR,EAAiBU,OAAO,CAC1BP,CAD4B,CACfH,EAAiBU,EAFqB,KAEd,EAErCP,EAAa,CAAC5B,IAAe,EAAKiC,GAAsB,OAAS,QAE/DtC,GACFqC,IADS,GAGXE,GAAuBlC,GAAe,GAE1C,EAAG,CAACA,EAAa+B,GAAWE,GAAqBtC,EAAM,EACvDzD,CAAAA,EAAAA,EAAAA,SAAAA,CAASA,CAAC,KACJuF,EAAiBU,OAAO,EAAE,CAC5BV,EAAiBU,OAAO,CAAG,KAE/B,GACA,IAAIC,GAAc,EAKlBC,CAAAA,EAAAA,EAAAA,EAAAA,CAAOA,CAACjB,EAAU,CAACkB,EAAOjE,KACxB,EAAE+D,GACE/D,IAAU2B,IACZR,EAAsB8C,EAAMlG,KADH,CACSgE,QAAAA,CAEtC,GACA,IAAMmC,GAAyBC,CAAAA,EAAAA,EAAAA,CAAAA,CAAeA,CAAChD,GACzCiD,GAAOC,CAAAA,EAAAA,EAAAA,WAAAA,CAAWA,CAACC,IACvB,GAAIZ,GACF,OAEF,CAHe,GAGXa,EAAkBX,GAAsB,EAC5C,GAAIW,EAAkB,EAAG,CACvB,GAAI,CAAClC,EACH,IADS,GAGXkC,EAAkBR,GAAc,CAClC,CACAX,EAAiBU,OAAO,CAAG,OACf,MAAZlC,GAAoBA,EAAS2C,EAAiBD,EAChD,EAAG,CAACZ,GAAWE,GAAqBhC,EAAUS,EAAM0B,GAAY,EAG1DS,GAAOC,CAAAA,EAAAA,EAAAA,CAAAA,CAAgBA,CAACH,IAC5B,GAAIZ,GACF,OAEF,CAHe,GAGXa,EAAkBX,GAAsB,EAC5C,GAAIW,GAAmBR,GAAa,CAClC,GAAI,CAAC1B,EACH,IADS,GAGXkC,EAAkB,CACpB,CACAnB,EAAiBU,OAAO,CAAG,OACf,MAAZlC,GAAoBA,EAAS2C,EAAiBD,EAChD,GACMI,GAAarB,CAAAA,EAAAA,EAAAA,MAAAA,CAAMA,GACzBsB,CAAAA,EAAAA,EAAAA,mBAAAA,CAAmBA,CAAClE,EAAK,IAAO,EAC9BmE,QAASF,GAAWZ,OAAO,CAC3BM,QACAI,QACF,GAGA,IAAMK,GAAkBJ,CAAAA,EAAAA,EAAAA,CAAAA,CAAgBA,CAAC,KACnC,CAACK,SAASC,MAAM,EAtIxB,SAASC,CAAiB,EACxB,GAAI,CAACJ,GAAW,CAACA,EAAQpH,KAAK,EAAI,CAACoH,EAAQK,UAAU,EAAI,CAACL,EAAQK,UAAU,CAACzH,KAAK,CAChF,CADkF,MAC3E,EAET,IAAM0H,EAAeC,iBAAiBP,GACtC,MAAgC,SAAzBM,EAAaE,OAAO,EAA2C,WAA5BF,EAAaG,UAAU,EAAkE,SAAjDF,iBAAiBP,EAAQK,UAAU,EAAEG,OAAO,EAiI1FV,GAAWZ,OAAO,GAAG,CACjDZ,EACFkB,KADS,KAMf,GACMkB,GAA+B,SAAdhC,EAAuB,QAAU,MACxDiC,EAAgB,KACVjE,IAIO,GAJA,GAIXO,EALa0D,CAKM1D,EAAQ+B,GAAqB0B,IACtC,MAAVxD,GAAkBA,EAAO8B,GAAqB0B,IAChD,EAAG,CAAC1B,GAAoB,EACxB,IAAM4B,GAAiB,GAAkBlC,MAAAA,CAAfL,EAAO,UAAkB,OAAVK,GACnCmC,GAAuB,GAAkBH,MAAAA,CAAfrC,EAAO,UAAuB,OAAfqC,IACzCI,GAAcrB,CAAAA,EAAAA,EAAAA,WAAAA,CAAWA,CAACsB,IAC9BC,CAAAA,EAAAA,EAAAA,CAAAA,CAAoBA,CAACD,GACV,MAAX9D,GAAmBA,EAAQ+B,GAAqB0B,GAClD,EAAG,CAACzD,EAAS+B,GAAqB0B,GAAe,EAC3CO,GAAgBxB,CAAAA,EAAAA,EAAAA,WAAAA,CAAWA,CAAC,KAChCV,IAAa,GACH,MAAV7B,GAAkBA,EAAO8B,GAAqB0B,GAChD,EAAG,CAACxD,EAAQ8B,GAAqB0B,GAAe,EAC1CQ,GAAgBzB,CAAAA,EAAAA,EAAAA,WAAAA,CAAWA,CAACC,IAChC,GAAItC,GAAY,CAAC,kBAAkBzF,IAAI,CAAC+H,EAAMhH,MAAM,CAACyI,OAAO,EAC1D,CAD6D,MACrDzB,EAAM0B,GAAG,EACf,IAAK,YACH1B,EAAM2B,cAAc,GAChB/C,EACFsB,GAAKF,EADI,CAGTF,GAAKE,GAEP,MACF,KAAK,aACHA,EAAM2B,cAAc,GAChB/C,EACFkB,GAAKE,EADI,CAGTE,GAAKF,GAEP,MAEJ,CAEW,MAAbrC,GAAqBA,EAAUqC,EACjC,EAAG,CAACtC,EAAUC,EAAWmC,GAAMI,GAAMtB,EAAM,EACrCgD,GAAkB7B,CAAAA,EAAAA,EAAAA,WAAAA,CAAWA,CAACC,IACpB,SAAS,CAAnBpC,GACFuB,IAAU,GAEG,MAAftB,GAAuBA,EAAYmC,EACrC,EAAG,CAACpC,EAAOC,EAAY,EACjBgE,GAAiB9B,CAAAA,EAAAA,EAAAA,WAAAA,CAAWA,CAACC,IACjCb,IAAU,GACI,MAAdrB,GAAsBA,EAAWkC,EACnC,EAAG,CAAClC,EAAW,EACTgE,GAAiB/C,CAAAA,EAAAA,EAAAA,MAAAA,CAAMA,CAAC,GACxBgD,GAAiBhD,CAAAA,EAAAA,EAAAA,MAAAA,CAAMA,CAAC,GACxBiD,GAAsBC,CAAAA,EAAAA,EAAAA,CAAAA,CAAUA,GAChCC,GAAmBnC,CAAAA,EAAAA,EAAAA,WAAAA,CAAWA,CAACC,IACnC8B,GAAetC,OAAO,CAAGQ,EAAMmC,OAAO,CAAC,EAAE,CAACC,OAAO,CACjDL,GAAevC,OAAO,CAAG,EACX,SAAS,CAAnB5B,GACFuB,IAAU,GAEI,MAAhBlB,GAAwBA,EAAa+B,EACvC,EAAG,CAACpC,EAAOK,EAAa,EAClBoE,GAAkBtC,CAAAA,EAAAA,EAAAA,WAAAA,CAAWA,CAACC,IAC9BA,EAAMmC,OAAO,EAAInC,EAAMmC,OAAO,CAACxH,MAAM,CAAG,EAC1CoH,CAD6C,EAC9BvC,OAAO,CAAG,EAEzBuC,GAAevC,OAAO,CAAGQ,EAAMmC,OAAO,CAAC,EAAE,CAACC,OAAO,CAAGN,GAAetC,OAAO,CAE5EtB,SAAuBA,EAAY8B,EACrC,EAAG,CAAC9B,EAAY,EACVoE,GAAiBvC,CAAAA,EAAAA,EAAAA,WAAAA,CAAWA,CAACC,IACjC,GAAIhC,EAAO,CACT,IAAMuE,EAAcR,GAAevC,OAAO,CACtCgD,KAAKC,GAAG,CAACF,GA1NK,KA2NZA,EAAc,EAChBzC,CADmB,EADK4C,GAIxBxC,GAAKF,GAGX,CACc,OARiC,EAQxB,CAAnBpC,GACFoE,GAAoBW,GAAG,CAAC,KACtBxD,IAAU,EACZ,EAAG1B,QAAYmF,GAEH,SAAQzE,EAAW6B,EACnC,EAAG,CAAChC,EAAOJ,EAAOkC,GAAMI,GAAM8B,GAAqBvE,EAAUU,EAAW,EAClE0E,GAAyB,MAAZpF,GAAoB,CAACyB,IAAU,CAACE,GAC7C0D,GAAoB/D,CAAAA,EAAAA,EAAAA,MAAAA,CAAMA,GAChCxF,CAAAA,EAAAA,EAAAA,SAAAA,CAASA,CAAC,KACR,IAAIwJ,EAAMC,EACV,GAAI,CAACH,GACH,OAAOD,EADQ,EAGXK,EAAWrE,EAAQkB,GAAOI,GAEhC,OADA4C,GAAkBtD,OAAO,CAAG0D,OAAOC,WAAW,CAAC3C,SAAS4C,eAAe,CAAG7C,GAAkB0C,EAAU,OAACF,EAAO,OAACC,EAAwBpD,GAAuBJ,OAAO,EAAYwD,EAAwBvF,CAAAA,CAAO,CAAasF,OAAOH,GAC7N,KAC6B,MAAM,CAApCE,GAAkBtD,OAAO,EAC3B6D,cAAcP,GAAkBtD,OAAO,CAE3C,CACF,EAAG,CAACqD,GAAY/C,GAAMI,GAAMN,GAAwBnC,EAAU8C,GAAiB3B,EAAM,EACrF,IAAM0E,GAAoBC,CAAAA,EAAAA,EAAAA,OAAAA,CAAOA,CAAC,IAAMpG,GAAcxD,MAAM6J,IAAI,CAAC,CAC/D7I,OAAQ8E,EACV,EAAG,CAACgE,EAAG/H,IAAUsE,IACH,MAAZ1C,GAAoBA,EAAS5B,EAAOsE,EACtC,GAAI,CAAC7C,EAAYsC,GAAanC,EAAS,EACvC,MAAoBoG,CAAb,EAAaA,EAAAA,IAAAA,CAAKA,CAACpH,CAAR,CAAmB,CACnCH,IAAKiE,GACL,GAAG3G,CAAK,CACRkE,UAAW6D,GACX3D,YAAa+D,GACb9D,WAAY+D,GACZ5D,aAAciE,GACdhE,YAAamE,GACblE,WAAYmE,GACZjK,UAAWoE,IAAWpE,EAAWsG,EAAQ3B,GAAS,QAASC,CAAtCR,EAA8C,GAAU,OAAPkC,EAAO,SAAQH,GAAW,GAAaA,MAAAA,CAAVG,EAAO,KAAW,OAARH,IAC7GC,SAAU,CAACtB,GAA2BX,CAAAA,EAAAA,EAAAA,GAAAA,CAAIA,CAAC,CAAlB,KAAyB,CAChDnE,KADkC,KACvB,GAAU,OAAPsG,EAAO,eACrBF,SAAU5E,CAAAA,EAAAA,EAAAA,EAAAA,CAAGA,CAAC4E,EAAU,CAACgF,EAAG/H,IAAuBc,CAAAA,EAAAA,CAAb,CAAaA,GAAAA,CAAIA,CAAC,KAAP,IAAiB,CAChEmH,KAAM,SACN,iBAAkB,GAAG,aAEY,MAAnBvG,GAA2BA,EAAgBzC,MAAM,CAAGyC,CAAe,CAAC1B,EAAM,CAAG,IAF9B,KAEiD,OAAVA,EAAQ,GAC5GrD,UAAWqD,IAAU4D,GAAsB,cAAWsD,EACtDgB,QAASN,GAAoBA,EAAiB,CAAC5H,EAAM,CAAGkH,OACxD,eAAgBlH,IAAU4D,EAC5B,EAAG5D,GACL,GAAiBc,CAAAA,EAAAA,EAAAA,GAAAA,CAAIA,CAAC,MAAO,CAC3BnE,UAAW,GAAU,OAAPsG,EAAO,UACrBF,SAAU5E,CAAAA,EAAAA,EAAAA,EAAAA,CAAGA,CAAC4E,EAAU,CAACkB,EAAOjE,KAC9B,IAAMmI,EAAWnI,IAAU4D,GAC3B,OAAOtC,EAAqBR,CAAAA,EAAAA,EAAAA,CAAb,EAAaA,CAAIA,CAACsH,EAAAA,CAAiBA,CAAE,CAClDC,EADwB,CACpBF,EACJG,QAASH,EAAWzC,GAAcwB,OAClCqB,UAAWJ,EAAWtC,QAAgBqB,EACtCsB,eAAgBC,EAAAA,CAAqBA,CACrC1F,SAAU,CAAC2F,EAAQC,IAA4BnI,EAAAA,OAAb,KAA+B,CAACyD,EAAO,CACvE,EAD2C,CACxC0E,CAAU,CACbhM,UAAWoE,IAAWkD,EAAMlG,KAAK,CAACpB,QAAboE,CAAsB,CAAEoH,GAAYO,eAAwBlD,GAAgB,CAAY,YAAXkD,GAAmC,YAAXA,CAAW,CAAQ,EAAM,SAAU,CAAY,aAAXA,GAAoC,YAAXA,CAAW,CAAQ,EAAMjD,GAClN,EACF,GAAoBjF,EAAb,WAAW,CAAoB,CAACyD,EAAO,CAC5CtH,UAAWoE,IAAWkD,EAAMlG,KAAK,CAACpB,QAAboE,CAAsB,CAAEoH,GAAY,SAC3D,EACF,EACF,GAAI3G,GAAyBwG,CAAAA,EAAAA,EAAAA,IAAb,CAAkBA,CAACY,EAAAA,OAAR,CAAiBA,CAAE,CAC5C7F,SAAU,CAAEV,CAAAA,OAAQV,CAAgB,GAAmBqG,CAAAA,EAAb,EAAaA,IAAAA,CAAKA,CAACa,EAAAA,CAAR,CAAgB,CACnElM,UAAW,GAAU,OAAPsG,EAAO,iBACrBiF,QAAS9D,GACTrB,SAAU,CAACL,EAAUC,GAA0B7B,CAAAA,EAAAA,EAAAA,GAAAA,CAAIA,CAAC,OAAQ,CAC1DnE,GAD2C,OAChC,kBACXoG,SAAUJ,CACZ,GAAG,GACAN,CAAAA,GAAQV,IAAgBoC,IAAc,GAAmBiE,CAAAA,EAAb,EAAaA,IAAAA,CAAKA,CAACa,EAAAA,CAAR,CAAgB,CAC1ElM,UAAW,GAAU,OAAPsG,EAAO,iBACrBiF,QAAS1D,GACTzB,SAAU,CAACH,EAAUC,GAA0B/B,CAAAA,EAAAA,EAAAA,GAAAA,CAAIA,CAAC,OAAQ,CAC1DnE,GAD2C,OAChC,kBACXoG,SAAUF,CACZ,GAAG,GACF,GACF,EAEP,GACAzD,EAAS8B,WAAW,CAAG,WACvB,MAAe4H,OAAOC,MAAM,CAAC3J,EAAU,CACrC4J,QFzTazI,CEyTJA,CACT0I,KDzTajI,CCyTPA,EACN,EAAC,GF3T2BT,EAAC,ECCJS,CCwTDT,CDxTE,GCyTRS,IC7UpB,4CACA,uCACA,WACA,OAAe,EAAQ,KAA4D,CACnF,EACA,SAFsB,gBCIiB,CAGtC,YAA4B,aAI7B,oBACA,OACA,iCACA,iCACA,0BACA,0BACA,+BACA,8BACA,8BACA,4BACA,6BAEA,yBAGA,qBACA,qGACA,KAEA,YACA,2EACA,oBACA,SACA,oEACA,KAEA,uDACA,8CACA,sBACA,gBACA,WACA,eACA,eACA,kBACA,yBACA,+BACA,CAAS,CACT,UACA,8BACA,aACA,+BACA,8BACA,gCACA,uCACA,CAAS,CACT,cACA,eACA,cACA,sBACA,iBACA,IACA,gBACA,IACA,gBACA,IACA,KACA,IACA,eACA,IACA,KACA,IACA,IACA,CAAS,CACT,2BAAoC,IAAI,IACxC,cACA,MACA,MACA,KACA,CAAS,CACJ,EAIL,CAAC,CAhFiD,EAAQ,KAAW,YAAZ", "sources": ["webpack://_N_E/./components/common/ReactImages.tsx", "webpack://_N_E/./node_modules/@restart/hooks/esm/useUpdateEffect.js", "webpack://_N_E/./node_modules/react-bootstrap/esm/CarouselCaption.js", "webpack://_N_E/./node_modules/react-bootstrap/esm/CarouselItem.js", "webpack://_N_E/./node_modules/react-bootstrap/esm/Carousel.js", "webpack://_N_E/?ca7b", "webpack://_N_E/./node_modules/moment/locale/de.js"], "sourcesContent": ["\r\n//Import Library\r\nimport React, { useState, useEffect } from 'react';\r\nimport { Carousel } from 'react-responsive-carousel';\r\nimport { FontAwesomeIcon } from \"@fortawesome/react-fontawesome\";\r\nimport {\r\n  faLink, faDownload\r\n} from \"@fortawesome/free-solid-svg-icons\";\r\nimport { Button } from \"react-bootstrap\";\r\n\r\n//Import services/components\r\nimport { useTranslation } from 'next-i18next';\r\n\r\n// Import CSS for react-responsive-carousel\r\nimport \"react-responsive-carousel/lib/styles/carousel.min.css\";\r\n\r\n// Define types for image items\r\ninterface ImageItem {\r\n  src: string;\r\n  description: string;\r\n  originalName: string;\r\n  downloadLink: string | false;\r\n}\r\n\r\ninterface ReactImagesProps {\r\n  gallery?: Array<{\r\n    _id: string;\r\n    name: string;\r\n    original_name?: string;\r\n    src?: string;\r\n    caption?: string;\r\n    alt?: string;\r\n  }> | boolean;\r\n  imageSource?: string[] | boolean;\r\n}\r\n\r\nconst ReactImages = (props: ReactImagesProps) => {\r\n  const { t } = useTranslation('common');\r\n  const [images, setImages] = useState<ImageItem[]>([]);\r\n\r\n  // Render image description and metadata\r\n  const renderImageLegend = (item: ImageItem) => {\r\n    const isValidLink = /(http|https):\\/\\/(\\w+:{0,1}\\w*)?(\\S+)(:[0-9]+)?(\\/|\\/([\\w#!:.?+=&%!\\-\\/]))?/.test(item.description);\r\n\r\n    return (\r\n      <div className=\"carousel-legend\">\r\n        <p className=\"lead\">\r\n          <b>{t(\"Filename\")}</b> {item.originalName || \"No Name found\"}\r\n        </p>\r\n        {item.description && (\r\n          <div className=\"source_link\">\r\n            <p><b>{t(\"Source\")}</b></p>\r\n            {isValidLink ? (\r\n              <div>\r\n                <FontAwesomeIcon icon={faLink} size=\"1x\" color=\"#999\" className=\"me-1\" />\r\n                <a className=\"source_link\" href={item.description} target=\"_blank\" rel=\"noopener noreferrer\">\r\n                  {item.description}\r\n                </a>\r\n              </div>\r\n            ) : (\r\n              <div>\r\n                <p className=\"ps-0 py-0\" style={{ wordBreak: \"break-all\" }}>\r\n                  {item.description}\r\n                </p>\r\n              </div>\r\n            )}\r\n          </div>\r\n        )}\r\n        {item.downloadLink && (\r\n          <Button className=\"btn btn-success mt-2 btn--download\" href={item.downloadLink}>\r\n            {t(\"Download\")}\r\n            <FontAwesomeIcon icon={faDownload} size=\"1x\" className=\"ms-1\" />\r\n          </Button>\r\n        )}\r\n      </div>\r\n    );\r\n  };\r\n\r\n  useEffect(() => {\r\n    const carouselImages: ImageItem[] = [];\r\n    props && props.gallery && Array.isArray(props.gallery) && props.gallery.map((item, i) => {\r\n      const fileType = item && item.name.split('.').pop();\r\n      let imgSrc;\r\n\r\n      switch (fileType) {\r\n        case \"JPG\":\r\n        case \"jpg\":\r\n        case \"jpeg\":\r\n        case \"png\":\r\n          imgSrc = `${process.env.API_SERVER}/image/show/${item._id}`;\r\n          break;\r\n        case \"pdf\":\r\n          imgSrc = \"/images/fileIcons/pdfFile.png\";\r\n          break;\r\n        case \"docx\":\r\n          imgSrc = \"/images/fileIcons/wordFile.png\";\r\n          break;\r\n        case \"xls\":\r\n        case 'xlsx':\r\n          imgSrc = \"/images/fileIcons/xlsFile.png\";\r\n          break;\r\n        default:\r\n          imgSrc = \"/images/fileIcons/otherFile.png\";\r\n      }\r\n\r\n      const _link = (fileType === \"docx\" || fileType === \"pdf\" || fileType === \"xls\" || fileType === \"xlsx\")\r\n        && `${process.env.API_SERVER}/files/download/${item._id}`;\r\n      const _name = `${item && item.original_name ? item.original_name : \"No Name found\"}`;\r\n      const _description = props.imageSource && Array.isArray(props.imageSource)\r\n        && props.imageSource.length > 0 ? props.imageSource[i] : \"\";\r\n\r\n      carouselImages.push({\r\n        src: imgSrc,\r\n        description: _description,\r\n        originalName: _name,\r\n        downloadLink: _link\r\n      });\r\n    });\r\n    setImages(carouselImages);\r\n  }, [props]);\r\n\r\n  return (\r\n    <div>\r\n      {images && images.length === 0 ? (\r\n        <div className=\"border border-info my-3 mx-0\">\r\n          <p className=\"d-flex d-flex justify-content-center p-2 m-0\">{t(\"NoFilesFound!\")}</p>\r\n        </div>\r\n      ) : (\r\n        <Carousel\r\n          showThumbs={true}\r\n          showStatus={true}\r\n          showIndicators={true}\r\n          infiniteLoop={true}\r\n          useKeyboardArrows={true}\r\n          autoPlay={false}\r\n          stopOnHover={true}\r\n          swipeable={true}\r\n          dynamicHeight={false}\r\n          emulateTouch={true}\r\n          renderThumbs={() =>\r\n            images.map((item, index) => (\r\n              <img\r\n                key={index}\r\n                src={item.src}\r\n                alt={`Thumbnail ${index + 1}`}\r\n                style={{ width: '60px', height: '60px', objectFit: 'cover' }}\r\n              />\r\n            ))\r\n          }\r\n        >\r\n          {images.map((item, index) => (\r\n            <div key={index}>\r\n              <img\r\n                src={item.src}\r\n                alt={item.originalName || \"Gallery image\"}\r\n                style={{ maxHeight: '500px', objectFit: 'contain' }}\r\n              />\r\n              {renderImageLegend(item)}\r\n            </div>\r\n          ))}\r\n        </Carousel>\r\n      )}\r\n    </div>\r\n  );\r\n\r\n}\r\n\r\nexport default ReactImages;\r\n", "import { useEffect, useRef } from 'react';\n\n/**\n * Runs an effect only when the dependencies have changed, skipping the\n * initial \"on mount\" run. Caution, if the dependency list never changes,\n * the effect is **never run**\n *\n * ```ts\n *  const ref = useRef<HTMLInput>(null);\n *\n *  // focuses an element only if the focus changes, and not on mount\n *  useUpdateEffect(() => {\n *    const element = ref.current?.children[focusedIdx] as HTMLElement\n *\n *    element?.focus()\n *\n *  }, [focusedIndex])\n * ```\n * @param effect An effect to run on mount\n *\n * @category effects\n */\nfunction useUpdateEffect(fn, deps) {\n  const isFirst = useRef(true);\n  useEffect(() => {\n    if (isFirst.current) {\n      isFirst.current = false;\n      return;\n    }\n    return fn();\n  }, deps);\n}\nexport default useUpdateEffect;", "\"use client\";\n\nimport * as React from 'react';\nimport classNames from 'classnames';\nimport { useBootstrapPrefix } from './ThemeProvider';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst CarouselCaption = /*#__PURE__*/React.forwardRef(({\n  className,\n  bsPrefix,\n  as: Component = 'div',\n  ...props\n}, ref) => {\n  bsPrefix = useBootstrapPrefix(bsPrefix, 'carousel-caption');\n  return /*#__PURE__*/_jsx(Component, {\n    ref: ref,\n    className: classNames(className, bsPrefix),\n    ...props\n  });\n});\nCarouselCaption.displayName = 'CarouselCaption';\nexport default CarouselCaption;", "\"use client\";\n\nimport classNames from 'classnames';\nimport * as React from 'react';\nimport { useBootstrapPrefix } from './ThemeProvider';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst CarouselItem = /*#__PURE__*/React.forwardRef(({\n  // Need to define the default \"as\" during prop destructuring to be compatible with styled-components github.com/react-bootstrap/react-bootstrap/issues/3595\n  as: Component = 'div',\n  bsPrefix,\n  className,\n  ...props\n}, ref) => {\n  const finalClassName = classNames(className, useBootstrapPrefix(bsPrefix, 'carousel-item'));\n  return /*#__PURE__*/_jsx(Component, {\n    ref: ref,\n    ...props,\n    className: finalClassName\n  });\n});\nCarouselItem.displayName = 'CarouselItem';\nexport default CarouselItem;", "\"use client\";\n\nimport useEventCallback from '@restart/hooks/useEventCallback';\nimport useUpdateEffect from '@restart/hooks/useUpdateEffect';\nimport useCommittedRef from '@restart/hooks/useCommittedRef';\nimport useTimeout from '@restart/hooks/useTimeout';\nimport Anchor from '@restart/ui/Anchor';\nimport classNames from 'classnames';\nimport * as React from 'react';\nimport { useCallback, useEffect, useImperativeHandle, useMemo, useRef, useState } from 'react';\nimport { useUncontrolled } from 'uncontrollable';\nimport CarouselCaption from './CarouselCaption';\nimport CarouselItem from './CarouselItem';\nimport { map, forEach } from './ElementChildren';\nimport { useBootstrapPrefix, useIsRTL } from './ThemeProvider';\nimport transitionEndListener from './transitionEndListener';\nimport triggerBrowserReflow from './triggerBrowserReflow';\nimport TransitionWrapper from './TransitionWrapper';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nimport { jsxs as _jsxs } from \"react/jsx-runtime\";\nimport { Fragment as _Fragment } from \"react/jsx-runtime\";\nconst SWIPE_THRESHOLD = 40;\nfunction isVisible(element) {\n  if (!element || !element.style || !element.parentNode || !element.parentNode.style) {\n    return false;\n  }\n  const elementStyle = getComputedStyle(element);\n  return elementStyle.display !== 'none' && elementStyle.visibility !== 'hidden' && getComputedStyle(element.parentNode).display !== 'none';\n}\nconst Carousel =\n/*#__PURE__*/\n// eslint-disable-next-line react/display-name\nReact.forwardRef(({\n  defaultActiveIndex = 0,\n  ...uncontrolledProps\n}, ref) => {\n  const {\n    // Need to define the default \"as\" during prop destructuring to be compatible with styled-components github.com/react-bootstrap/react-bootstrap/issues/3595\n    as: Component = 'div',\n    bsPrefix,\n    slide = true,\n    fade = false,\n    controls = true,\n    indicators = true,\n    indicatorLabels = [],\n    activeIndex,\n    onSelect,\n    onSlide,\n    onSlid,\n    interval = 5000,\n    keyboard = true,\n    onKeyDown,\n    pause = 'hover',\n    onMouseOver,\n    onMouseOut,\n    wrap = true,\n    touch = true,\n    onTouchStart,\n    onTouchMove,\n    onTouchEnd,\n    prevIcon = /*#__PURE__*/_jsx(\"span\", {\n      \"aria-hidden\": \"true\",\n      className: \"carousel-control-prev-icon\"\n    }),\n    prevLabel = 'Previous',\n    nextIcon = /*#__PURE__*/_jsx(\"span\", {\n      \"aria-hidden\": \"true\",\n      className: \"carousel-control-next-icon\"\n    }),\n    nextLabel = 'Next',\n    variant,\n    className,\n    children,\n    ...props\n  } = useUncontrolled({\n    defaultActiveIndex,\n    ...uncontrolledProps\n  }, {\n    activeIndex: 'onSelect'\n  });\n  const prefix = useBootstrapPrefix(bsPrefix, 'carousel');\n  const isRTL = useIsRTL();\n  const nextDirectionRef = useRef(null);\n  const [direction, setDirection] = useState('next');\n  const [paused, setPaused] = useState(false);\n  const [isSliding, setIsSliding] = useState(false);\n  const [renderedActiveIndex, setRenderedActiveIndex] = useState(activeIndex || 0);\n  useEffect(() => {\n    if (!isSliding && activeIndex !== renderedActiveIndex) {\n      if (nextDirectionRef.current) {\n        setDirection(nextDirectionRef.current);\n      } else {\n        setDirection((activeIndex || 0) > renderedActiveIndex ? 'next' : 'prev');\n      }\n      if (slide) {\n        setIsSliding(true);\n      }\n      setRenderedActiveIndex(activeIndex || 0);\n    }\n  }, [activeIndex, isSliding, renderedActiveIndex, slide]);\n  useEffect(() => {\n    if (nextDirectionRef.current) {\n      nextDirectionRef.current = null;\n    }\n  });\n  let numChildren = 0;\n  let activeChildInterval;\n\n  // Iterate to grab all of the children's interval values\n  // (and count them, too)\n  forEach(children, (child, index) => {\n    ++numChildren;\n    if (index === activeIndex) {\n      activeChildInterval = child.props.interval;\n    }\n  });\n  const activeChildIntervalRef = useCommittedRef(activeChildInterval);\n  const prev = useCallback(event => {\n    if (isSliding) {\n      return;\n    }\n    let nextActiveIndex = renderedActiveIndex - 1;\n    if (nextActiveIndex < 0) {\n      if (!wrap) {\n        return;\n      }\n      nextActiveIndex = numChildren - 1;\n    }\n    nextDirectionRef.current = 'prev';\n    onSelect == null || onSelect(nextActiveIndex, event);\n  }, [isSliding, renderedActiveIndex, onSelect, wrap, numChildren]);\n\n  // This is used in the setInterval, so it should not invalidate.\n  const next = useEventCallback(event => {\n    if (isSliding) {\n      return;\n    }\n    let nextActiveIndex = renderedActiveIndex + 1;\n    if (nextActiveIndex >= numChildren) {\n      if (!wrap) {\n        return;\n      }\n      nextActiveIndex = 0;\n    }\n    nextDirectionRef.current = 'next';\n    onSelect == null || onSelect(nextActiveIndex, event);\n  });\n  const elementRef = useRef();\n  useImperativeHandle(ref, () => ({\n    element: elementRef.current,\n    prev,\n    next\n  }));\n\n  // This is used in the setInterval, so it should not invalidate.\n  const nextWhenVisible = useEventCallback(() => {\n    if (!document.hidden && isVisible(elementRef.current)) {\n      if (isRTL) {\n        prev();\n      } else {\n        next();\n      }\n    }\n  });\n  const slideDirection = direction === 'next' ? 'start' : 'end';\n  useUpdateEffect(() => {\n    if (slide) {\n      // These callbacks will be handled by the <Transition> callbacks.\n      return;\n    }\n    onSlide == null || onSlide(renderedActiveIndex, slideDirection);\n    onSlid == null || onSlid(renderedActiveIndex, slideDirection);\n  }, [renderedActiveIndex]);\n  const orderClassName = `${prefix}-item-${direction}`;\n  const directionalClassName = `${prefix}-item-${slideDirection}`;\n  const handleEnter = useCallback(node => {\n    triggerBrowserReflow(node);\n    onSlide == null || onSlide(renderedActiveIndex, slideDirection);\n  }, [onSlide, renderedActiveIndex, slideDirection]);\n  const handleEntered = useCallback(() => {\n    setIsSliding(false);\n    onSlid == null || onSlid(renderedActiveIndex, slideDirection);\n  }, [onSlid, renderedActiveIndex, slideDirection]);\n  const handleKeyDown = useCallback(event => {\n    if (keyboard && !/input|textarea/i.test(event.target.tagName)) {\n      switch (event.key) {\n        case 'ArrowLeft':\n          event.preventDefault();\n          if (isRTL) {\n            next(event);\n          } else {\n            prev(event);\n          }\n          return;\n        case 'ArrowRight':\n          event.preventDefault();\n          if (isRTL) {\n            prev(event);\n          } else {\n            next(event);\n          }\n          return;\n        default:\n      }\n    }\n    onKeyDown == null || onKeyDown(event);\n  }, [keyboard, onKeyDown, prev, next, isRTL]);\n  const handleMouseOver = useCallback(event => {\n    if (pause === 'hover') {\n      setPaused(true);\n    }\n    onMouseOver == null || onMouseOver(event);\n  }, [pause, onMouseOver]);\n  const handleMouseOut = useCallback(event => {\n    setPaused(false);\n    onMouseOut == null || onMouseOut(event);\n  }, [onMouseOut]);\n  const touchStartXRef = useRef(0);\n  const touchDeltaXRef = useRef(0);\n  const touchUnpauseTimeout = useTimeout();\n  const handleTouchStart = useCallback(event => {\n    touchStartXRef.current = event.touches[0].clientX;\n    touchDeltaXRef.current = 0;\n    if (pause === 'hover') {\n      setPaused(true);\n    }\n    onTouchStart == null || onTouchStart(event);\n  }, [pause, onTouchStart]);\n  const handleTouchMove = useCallback(event => {\n    if (event.touches && event.touches.length > 1) {\n      touchDeltaXRef.current = 0;\n    } else {\n      touchDeltaXRef.current = event.touches[0].clientX - touchStartXRef.current;\n    }\n    onTouchMove == null || onTouchMove(event);\n  }, [onTouchMove]);\n  const handleTouchEnd = useCallback(event => {\n    if (touch) {\n      const touchDeltaX = touchDeltaXRef.current;\n      if (Math.abs(touchDeltaX) > SWIPE_THRESHOLD) {\n        if (touchDeltaX > 0) {\n          prev(event);\n        } else {\n          next(event);\n        }\n      }\n    }\n    if (pause === 'hover') {\n      touchUnpauseTimeout.set(() => {\n        setPaused(false);\n      }, interval || undefined);\n    }\n    onTouchEnd == null || onTouchEnd(event);\n  }, [touch, pause, prev, next, touchUnpauseTimeout, interval, onTouchEnd]);\n  const shouldPlay = interval != null && !paused && !isSliding;\n  const intervalHandleRef = useRef();\n  useEffect(() => {\n    var _ref, _activeChildIntervalR;\n    if (!shouldPlay) {\n      return undefined;\n    }\n    const nextFunc = isRTL ? prev : next;\n    intervalHandleRef.current = window.setInterval(document.visibilityState ? nextWhenVisible : nextFunc, (_ref = (_activeChildIntervalR = activeChildIntervalRef.current) != null ? _activeChildIntervalR : interval) != null ? _ref : undefined);\n    return () => {\n      if (intervalHandleRef.current !== null) {\n        clearInterval(intervalHandleRef.current);\n      }\n    };\n  }, [shouldPlay, prev, next, activeChildIntervalRef, interval, nextWhenVisible, isRTL]);\n  const indicatorOnClicks = useMemo(() => indicators && Array.from({\n    length: numChildren\n  }, (_, index) => event => {\n    onSelect == null || onSelect(index, event);\n  }), [indicators, numChildren, onSelect]);\n  return /*#__PURE__*/_jsxs(Component, {\n    ref: elementRef,\n    ...props,\n    onKeyDown: handleKeyDown,\n    onMouseOver: handleMouseOver,\n    onMouseOut: handleMouseOut,\n    onTouchStart: handleTouchStart,\n    onTouchMove: handleTouchMove,\n    onTouchEnd: handleTouchEnd,\n    className: classNames(className, prefix, slide && 'slide', fade && `${prefix}-fade`, variant && `${prefix}-${variant}`),\n    children: [indicators && /*#__PURE__*/_jsx(\"div\", {\n      className: `${prefix}-indicators`,\n      children: map(children, (_, index) => /*#__PURE__*/_jsx(\"button\", {\n        type: \"button\",\n        \"data-bs-target\": \"\" // Bootstrap requires this in their css.\n        ,\n        \"aria-label\": indicatorLabels != null && indicatorLabels.length ? indicatorLabels[index] : `Slide ${index + 1}`,\n        className: index === renderedActiveIndex ? 'active' : undefined,\n        onClick: indicatorOnClicks ? indicatorOnClicks[index] : undefined,\n        \"aria-current\": index === renderedActiveIndex\n      }, index))\n    }), /*#__PURE__*/_jsx(\"div\", {\n      className: `${prefix}-inner`,\n      children: map(children, (child, index) => {\n        const isActive = index === renderedActiveIndex;\n        return slide ? /*#__PURE__*/_jsx(TransitionWrapper, {\n          in: isActive,\n          onEnter: isActive ? handleEnter : undefined,\n          onEntered: isActive ? handleEntered : undefined,\n          addEndListener: transitionEndListener,\n          children: (status, innerProps) => /*#__PURE__*/React.cloneElement(child, {\n            ...innerProps,\n            className: classNames(child.props.className, isActive && status !== 'entered' && orderClassName, (status === 'entered' || status === 'exiting') && 'active', (status === 'entering' || status === 'exiting') && directionalClassName)\n          })\n        }) : ( /*#__PURE__*/React.cloneElement(child, {\n          className: classNames(child.props.className, isActive && 'active')\n        }));\n      })\n    }), controls && /*#__PURE__*/_jsxs(_Fragment, {\n      children: [(wrap || activeIndex !== 0) && /*#__PURE__*/_jsxs(Anchor, {\n        className: `${prefix}-control-prev`,\n        onClick: prev,\n        children: [prevIcon, prevLabel && /*#__PURE__*/_jsx(\"span\", {\n          className: \"visually-hidden\",\n          children: prevLabel\n        })]\n      }), (wrap || activeIndex !== numChildren - 1) && /*#__PURE__*/_jsxs(Anchor, {\n        className: `${prefix}-control-next`,\n        onClick: next,\n        children: [nextIcon, nextLabel && /*#__PURE__*/_jsx(\"span\", {\n          className: \"visually-hidden\",\n          children: nextLabel\n        })]\n      })]\n    })]\n  });\n});\nCarousel.displayName = 'Carousel';\nexport default Object.assign(Carousel, {\n  Caption: CarouselCaption,\n  Item: CarouselItem\n});", "\n    (window.__NEXT_P = window.__NEXT_P || []).push([\n      \"/vspace/VirtualSpaceAccordionSection\",\n      function () {\n        return require(\"private-next-pages/vspace/VirtualSpaceAccordionSection.tsx\");\n      }\n    ]);\n    if(module.hot) {\n      module.hot.dispose(function () {\n        window.__NEXT_P.push([\"/vspace/VirtualSpaceAccordionSection\"])\n      });\n    }\n  ", "//! moment.js locale configuration\n//! locale : German [de]\n//! author : lluchs : https://github.com/lluchs\n//! author: <PERSON><PERSON><PERSON>: https://github.com/Oire\n//! author : <PERSON><PERSON><PERSON><PERSON> : https://github.com/mik01aj\n\n;(function (global, factory) {\n   typeof exports === 'object' && typeof module !== 'undefined'\n       && typeof require === 'function' ? factory(require('../moment')) :\n   typeof define === 'function' && define.amd ? define(['../moment'], factory) :\n   factory(global.moment)\n}(this, (function (moment) { 'use strict';\n\n    //! moment.js locale configuration\n\n    function processRelativeTime(number, withoutSuffix, key, isFuture) {\n        var format = {\n            m: ['eine Minute', 'einer Minute'],\n            h: ['eine Stunde', 'einer Stunde'],\n            d: ['ein Tag', 'einem Tag'],\n            dd: [number + ' Tage', number + ' Tagen'],\n            w: ['eine Woche', 'einer Woche'],\n            M: ['ein <PERSON><PERSON>', 'einem Monat'],\n            MM: [number + ' <PERSON><PERSON>', number + ' Monaten'],\n            y: ['ein Jahr', 'einem Jahr'],\n            yy: [number + ' Jahre', number + ' Jahren'],\n        };\n        return withoutSuffix ? format[key][0] : format[key][1];\n    }\n\n    var de = moment.defineLocale('de', {\n        months: 'Januar_Februar_März_April_Mai_Juni_Juli_August_September_Oktober_November_Dezember'.split(\n            '_'\n        ),\n        monthsShort:\n            'Jan._Feb._März_Apr._Mai_Juni_Juli_Aug._Sep._Okt._Nov._Dez.'.split('_'),\n        monthsParseExact: true,\n        weekdays:\n            'Sonntag_Montag_Dienstag_Mittwoch_Donnerstag_Freitag_Samstag'.split(\n                '_'\n            ),\n        weekdaysShort: 'So._Mo._Di._Mi._Do._Fr._Sa.'.split('_'),\n        weekdaysMin: 'So_Mo_Di_Mi_Do_Fr_Sa'.split('_'),\n        weekdaysParseExact: true,\n        longDateFormat: {\n            LT: 'HH:mm',\n            LTS: 'HH:mm:ss',\n            L: 'DD.MM.YYYY',\n            LL: 'D. MMMM YYYY',\n            LLL: 'D. MMMM YYYY HH:mm',\n            LLLL: 'dddd, D. MMMM YYYY HH:mm',\n        },\n        calendar: {\n            sameDay: '[heute um] LT [Uhr]',\n            sameElse: 'L',\n            nextDay: '[morgen um] LT [Uhr]',\n            nextWeek: 'dddd [um] LT [Uhr]',\n            lastDay: '[gestern um] LT [Uhr]',\n            lastWeek: '[letzten] dddd [um] LT [Uhr]',\n        },\n        relativeTime: {\n            future: 'in %s',\n            past: 'vor %s',\n            s: 'ein paar Sekunden',\n            ss: '%d Sekunden',\n            m: processRelativeTime,\n            mm: '%d Minuten',\n            h: processRelativeTime,\n            hh: '%d Stunden',\n            d: processRelativeTime,\n            dd: processRelativeTime,\n            w: processRelativeTime,\n            ww: '%d Wochen',\n            M: processRelativeTime,\n            MM: processRelativeTime,\n            y: processRelativeTime,\n            yy: processRelativeTime,\n        },\n        dayOfMonthOrdinalParse: /\\d{1,2}\\./,\n        ordinal: '%d.',\n        week: {\n            dow: 1, // Monday is the first day of the week.\n            doy: 4, // The week that contains Jan 4th is the first week of the year.\n        },\n    });\n\n    return de;\n\n})));\n"], "names": ["t", "useTranslation", "ReactImages", "images", "setImages", "useState", "renderImageLegend", "isValidLink", "test", "item", "description", "div", "className", "p", "b", "originalName", "FontAwesomeIcon", "icon", "faLink", "size", "color", "a", "href", "target", "rel", "style", "wordBreak", "downloadLink", "<PERSON><PERSON>", "faDownload", "useEffect", "carouselImages", "props", "gallery", "Array", "isArray", "map", "i", "imgSrc", "fileType", "name", "split", "pop", "process", "_id", "_link", "_name", "original_name", "_description", "imageSource", "length", "push", "src", "Carousel", "showThumbs", "showStatus", "showIndicators", "infiniteLoop", "useKeyboardArrows", "autoPlay", "stopOnHover", "swipeable", "dynamicHeight", "emulate<PERSON><PERSON><PERSON>", "renderThumbs", "index", "img", "alt", "width", "height", "objectFit", "maxHeight", "CarouselCaption", "React", "ref", "bsPrefix", "as", "Component", "useBootstrapPrefix", "_jsx", "classNames", "CarouselItem", "finalClassName", "displayName", "activeChildInterval", "defaultActiveIndex", "uncontrolledProps", "slide", "fade", "controls", "indicators", "indicatorLabels", "activeIndex", "onSelect", "onSlide", "onSlid", "interval", "keyboard", "onKeyDown", "pause", "onMouseOver", "onMouseOut", "wrap", "touch", "onTouchStart", "onTouchMove", "onTouchEnd", "prevIcon", "prevLabel", "nextIcon", "next<PERSON><PERSON><PERSON>", "variant", "children", "useUncontrolled", "prefix", "isRTL", "useIsRTL", "nextDirectionRef", "useRef", "direction", "setDirection", "paused", "setPaused", "isSliding", "setIsSliding", "renderedActiveIndex", "setRenderedActiveIndex", "current", "numC<PERSON><PERSON>n", "for<PERSON>ach", "child", "activeChildIntervalRef", "useCommittedRef", "prev", "useCallback", "event", "nextActiveIndex", "next", "useEventCallback", "elementRef", "useImperativeHandle", "element", "nextWhenVisible", "document", "hidden", "isVisible", "parentNode", "elementStyle", "getComputedStyle", "display", "visibility", "slideDirection", "useUpdateEffect", "orderClassName", "directionalClassName", "handleEnter", "node", "triggerBrowserReflow", "handleEntered", "handleKeyDown", "tagName", "key", "preventDefault", "handleMouseOver", "handleMouseOut", "touchStartXRef", "touchDeltaXRef", "touchUnpauseTimeout", "useTimeout", "handleTouchStart", "touches", "clientX", "handleTouchMove", "handleTouchEnd", "touchDeltaX", "Math", "abs", "SWIPE_THRESHOLD", "set", "undefined", "shouldPlay", "intervalHandleRef", "_ref", "_activeChildIntervalR", "nextFunc", "window", "setInterval", "visibilityState", "clearInterval", "indicatorOnClicks", "useMemo", "from", "_", "_jsxs", "type", "onClick", "isActive", "TransitionWrapper", "in", "onEnter", "onEntered", "addEndListener", "transitionEndListener", "status", "innerProps", "_Fragment", "<PERSON><PERSON>", "Object", "assign", "Caption", "<PERSON><PERSON>"], "sourceRoot": "", "ignoreList": [1, 2, 3, 4, 6]}