{"version": 3, "file": "static/chunks/9379-7663e352585abb7c.js", "mappings": "uIAwHA,MA9GkE,OAAC,OACjEA,CAAK,UACLC,CAAQ,GA4GKC,UA3GbC,EAAc,QA2GmBD,EAAC,UA3GA,CAClCE,SAAS,GAAG,UACZC,EAAW,EAAK,CACjB,GACOC,EAAYC,CAAAA,EAAAA,EAAAA,MAAAA,CAAMA,CAAiB,MACnC,CAACC,EAAWC,EAAa,CAAGC,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,EAAC,GAG3CC,CAAAA,EAAAA,EAAAA,SAAAA,CAASA,CAAC,KACJL,EAAUM,OAAO,EAAI,GAEnB,CAACJ,GAAaF,EAAUM,IAFa,GAEN,CAACC,SAFkB,GAEJb,GAChDM,GAAUM,CAD6C,MACtC,CAACC,SAAS,CAAGb,GAAS,GAG7C,EAAG,CAACA,EAAOQ,EAAU,EAGrB,IAAMM,EAAc,KACdR,EAAUM,OAAO,EAAIX,GACvBA,EAASK,EAAUM,GADc,IACP,CAACC,SAAS,CAExC,EAGME,EAAc,CAACC,EAAiBhB,KACpC,GAAwB,aAApB,OAAOiB,SAA0B,KAGnCX,EAFAW,SAASF,WAAW,CAACC,GAAS,EAAOhB,GAAS,IAC9Cc,WACAR,EAAAA,EAAUM,OAAAA,GAAVN,EAAmBY,KAAK,EAC1B,CACF,CAFIZ,CAIJ,MACE,UAACa,MAAAA,CAAIC,UAAU,0BAA0BC,MAAO,CAAEC,OAAQ,gBAAiB,WAEzE,MAD8B,GAC9B,wBACE,WAACH,MAAAA,CAAIC,UAAU,UAAUC,MAAO,CAAEE,QAAS,MAAOC,aAAc,iBAAkBC,WAAY,SAAU,YACpG,UAACC,SAAAA,CACCC,KAAK,SACLC,QAAS,IAAMb,EAAY,QAC3BM,MAAO,CAAEQ,OAAQ,QAASN,QAAS,SAAU,WAE7C,UAACO,SAAAA,UAAO,QAEV,UAACJ,SAAAA,CACCC,KAAK,SACLC,QAAS,IAAMb,EAAY,UAC3BM,MAAO,CAAEQ,OAAQ,QAASN,QAAS,SAAU,WAE7C,UAACQ,KAAAA,UAAG,QAEN,UAACL,SAAAA,CACCC,KAAK,SACLC,QAAS,IAAMb,EAAY,aAC3BM,MAAO,CAAEQ,OAAQ,QAASN,QAAS,SAAU,WAE7C,UAACS,IAAAA,UAAE,QAEL,UAACN,SAAAA,CACCC,KAAK,SACLC,QAAS,IAAMb,EAAY,qBAC3BM,MAAO,CAAEQ,OAAQ,QAASN,QAAS,SAAU,WAC9C,OAGD,UAACG,SAAAA,CACCC,KAAK,SACLC,QAAS,IAAMb,EAAY,uBAC3BM,MAAO,CAAEQ,OAAQ,QAASN,QAAS,SAAU,WAC9C,OAGD,UAACG,SAAAA,CACCC,KAAK,SACLC,QAAS,KACP,IAAMK,EAAMC,OAAO,sBACfD,GAAKlB,EAAY,aAAckB,EACrC,EACAZ,MAAO,CAAEQ,OAAQ,QAASN,QAAS,SAAU,WAC9C,YAIH,UAACJ,MAAAA,CACCgB,IAAK7B,EACL8B,gBAAiB,CAAC/B,EAClBgC,QAASvB,EACTwB,QAAS,IAAM7B,GAAa,GAC5B8B,OAAQ,IAAM9B,GAAa,GAC3BY,MAAO,CACLE,QAAS,OACTiB,UAAWpC,EACXqC,UAAoB,EAATrC,EACXsC,SAAU,OACVC,QAAS,MACX,EACAC,mBAAkB,EAAuB,GAAdzC,EAC3B0C,gCAAgC,QAO5C,EC7GaC,EAAmD,IAC9D,GAAM,aAAEC,CAAW,UAAE9C,CAAQ,CAAE,CAAG+C,EAElC,MACE,UAAC9C,EAAoBA,CACnBF,MAAO+C,GAAe,GACtB9C,SAAU,GAAaA,EAASgD,IAGtC,EAAE,4ICqEK,IAAMC,EAAQ,CACnBC,WA1C4C,OAAC,MAC7CC,CAAI,eACJC,CAAa,CACbpD,UAAQ,cACRqD,CAAY,UACZC,CAAQ,CACT,GACO,QAAEC,CAAM,SAAEC,CAAO,CAAE,CAAGC,CAAAA,EAAAA,EAAAA,EAAAA,CAAgBA,GACtCC,EAAWF,CAAO,CAACL,EAAK,EAAII,CAAM,CAACJ,EAAK,CAGzBQ,EAAAA,OAAa,CAAC,IAAO,OAAER,EAAK,EAAI,CAACA,EAAK,EAG3D,IAAMS,EAAoBD,EAAAA,QAAc,CAACE,GAAG,CAACP,EAAWQ,GAClDH,EAAAA,cAAoB,CAACG,IAEnBC,IAF2B,KAxC5BA,CAAmB,EAC1B,MAAwB,UAAjB,OAAOhB,GAAgC,OAAVA,CACtC,EAwCmBe,EAAMf,KAAK,EACfY,CADkB,CAClBA,YAAkB,CAACG,EAA6C,MACrEX,EACA,GAAGW,EAAMf,KAAK,GAIbe,GAGT,MACE,WAAC5C,MAAAA,WACC,UAACA,MAAAA,CAAIC,UAAU,uBACZyC,IAEFF,GACC,UAACxC,MAAAA,CAAIC,UAAU,oCACZkC,GAAiB,kBAAOE,CAAM,CAACJ,EAAK,CAAgBI,CAAM,CAACJ,EAAK,CAAGa,OAAOT,CAAM,CAACJ,GAAK,MAKjG,EAIEc,UAhE0C,OAAC,IAAEC,CAAE,OAAEC,CAAK,OAAEpE,CAAK,MAAEoD,CAAI,CAAE/C,UAAQ,CAAE,GACzE,QAAEgE,CAAM,eAAEC,CAAa,CAAE,CAAGZ,CAAAA,EAAAA,EAAAA,EAAAA,CAAgBA,GAC5Ca,EAAYnB,GAAQe,EAE1B,MACE,UAACK,EAAAA,CAAIA,CAACC,KAAK,EACT9C,KAAK,QACLwC,GAAIA,EACJC,MAAOA,EACPpE,MAAOA,EACPoD,KAAMmB,EACNG,QAASL,CAAM,CAACE,EAAU,GAAKvE,EAC/BC,SAAU,IACRqE,EAAcC,EAAWI,EAAEC,MAAM,CAAC5E,KAAK,CACzC,EACAK,SAAUA,EACVwE,MAAM,KAGZ,CA8CA,ECzEgBC,EAAAA,CAAAA,CACLC,EAAAA,EAAAA,CACEC,EAAAA,EAAAA,mFCeb,IAAMC,EAAwBC,CAAAA,EAAAA,EAAAA,UAAAA,CAAUA,CAA8C,CAAClC,EAAOb,KAC5F,GAAM,UAAEoB,CAAQ,CAAE4B,UAAQ,cAAEC,CAAY,WAAEhE,CAAS,YAAEiE,CAAU,eAAEC,CAAa,CAAE,GAAGC,EAAM,CAAGvC,EAGtFwC,EAAmBC,EAAAA,EAAU,GAAGC,KAAK,CAAC,CAAC,GAE7C,MACE,UAACC,EAAAA,EAAMA,CAAAA,CACLL,cAAeA,GAAiB,CAAC,EACjCE,iBAAkBA,EAClBL,SAAU,CAACd,EAA6BuB,KAEtC,IAAMC,EAAuB,CAC3BC,eAAgB,KAAO,EACvBC,gBAAiB,KAAO,EACxBC,cAAe,KACfpB,OAAQ,KACRqB,YAAa,IAAIC,MAAM,UACvBC,SAAS,EACTC,YAAY,EACZC,kBAAkB,EAClBC,WAAY,EACZC,WAAW,EACXC,UAAWC,KAAKC,GAAG,GACnB/E,KAAM,SACNgF,mBAAoB,KAAM,EAC1BC,qBAAsB,KAAM,EAC5BC,QAAS,KAAO,CAClB,EAEI1B,GAEFA,EAASU,EAAWxB,EAAQuB,CAFhB,CAIhB,EACC,GAAGL,CAAI,UAENuB,GACA,UAACtC,EAAAA,EAAIA,CAAAA,CACHrC,IAAKA,EACLgD,SAAU2B,EAAYC,YAAY,CAClC3B,aAAcA,EACdhE,UAAWA,EACXiE,WAAYA,WAES,YAApB,OAAO9B,EAA0BA,EAASuD,GAAevD,KAKpE,GAEA0B,EAAsB+B,WAAW,CAAG,wBAEpC,MAAe/B,qBAAqBA,EAAC,yEClF9B,IAAMF,EAAY,OAAC,MACxB3B,CAAI,CACJe,IAAE,UACF8C,CAAQ,WACRC,CAAS,cACT5D,CAAY,UACZrD,CAAQ,CACRD,OAAK,IACLmH,CAAE,WACFC,CAAS,MACTC,CAAI,SACJC,CAAO,CACP,GAAGtE,EACC,GAuBJ,MACE,UAACuE,EAAAA,EAAKA,CAAAA,CAACnE,KAAMA,EAAMoE,SAtBJ,CAsBcA,GApB7B,IAAMC,EAA2B,UAAf,OAAOC,EAAmBA,EAAMzD,OAAOyD,GAAO,WAChE,GAAiB,EAACA,GAA4B,IAA7B,CAAQD,EAAUE,IAAI,EAAO,CAAC,CACtCrE,EAD0C,KAC1CA,EAAAA,KAAAA,EAAAA,EAAc4D,SAAS,GAAI,EAA3B5D,uBAGL4D,GAAa,CAACA,EAAUQ,GACnBpE,GADyB,IACzBA,EAAAA,KAAAA,EAAAA,EAAc4D,SAAAA,GAAa,EAA3B5D,cAGLgE,GAAWI,GAET,CADU,CADI,GACAE,OAAON,GACdO,IAAI,CAACH,GACPpE,GADa,IACbA,EAAAA,KAAAA,EAAAA,EAAcgE,OAAAA,GAAW,IAAzBhE,mBAKb,WAIK,OAAC,OAAEwE,CAAK,CAAEC,MAAI,CAAO,SACpB,iCACE,UAACvD,EAAAA,CAAIA,CAACwD,OAAO,EACV,GAAGF,CAAK,CACR,GAAG9E,CAAK,CACTmB,GAAIA,EACJgD,GAAIA,GAAM,QACVE,KAAMA,EACNY,UAAWF,EAAKtE,OAAO,EAAI,CAAC,CAACsE,EAAKG,KAAK,CACvCjI,SAAU,IACR6H,EAAM7H,QAAQ,CAAC0E,GACX1E,GAAUA,EAAS0E,EACzB,EACA3E,WAAiBmI,IAAVnI,EAAsBA,EAAQ8H,EAAM9H,KAAK,GAEjD+H,EAAKtE,OAAO,EAAIsE,EAAKG,KAAK,CACzB,UAAC1D,EAAAA,CAAIA,CAACwD,OAAO,CAACI,QAAQ,EAACzG,KAAK,mBACzBoG,EAAKG,KAAK,GAEX,UAKd,EAAE,EAIyB,OAAC,MAC1B9E,CAAI,IACJe,CAAE,UACF8C,CAAQ,cACR3D,CAAY,UACZrD,CAAQ,OACRD,CAAK,UACLuD,CAAQ,CACR,GAAGP,EACC,GAUJ,MACE,UAACuE,EAAAA,EAAKA,CAAAA,CAACnE,KAAMA,EAAMoE,SATJ,CAScA,GAR7B,GAAIP,GAAa,EAACS,GAAe,IAAhB,CAAQA,CAAQ,CAAC,CAChC,EADoC,IAC7BpE,OAAAA,EAAAA,KAAAA,EAAAA,EAAc4D,SAAAA,GAAa,EAA3B5D,sBAIX,WAIK,OAAC,OAAEwE,CAAK,MAAEC,CAAI,CAAO,SACpB,iCACE,UAACvD,EAAAA,CAAIA,CAACwD,OAAO,EACXb,GAAG,SACF,GAAGW,CAAK,CACR,GAAG9E,CAAK,CACTmB,GAAIA,EACJ8D,UAAWF,EAAKtE,OAAO,EAAI,CAAC,CAACsE,EAAKG,KAAK,CACvCjI,SAAU,IACR6H,EAAM7H,QAAQ,CAAC0E,GACX1E,GAAUA,EAAS0E,EACzB,EACA3E,WAAiBmI,IAAVnI,EAAsBA,EAAQ8H,EAAM9H,KAAK,UAE/CuD,IAEFwE,EAAKtE,OAAO,EAAIsE,EAAKG,KAAK,CACzB,UAAC1D,EAAAA,CAAIA,CAACwD,OAAO,CAACI,QAAQ,EAACzG,KAAK,mBACzBoG,EAAKG,KAAK,GAEX,UAKd,EAAE,8LC7FF,IAAIG,EAAmB,EAAE,CAEnBC,EAAiB,CACrBC,KAAM,EACNC,QAAS,OACTC,cAAe,SACfC,WAAY,SACZC,eAAgB,SAChBC,MAAO,OACPxI,OAAQ,OACRyI,YAAa,GACbC,YAAa,UACbC,gBAAiB,UACjBC,MAAO,QACPC,WAAY,2BACZ1H,QAAS,MACX,EAYM2H,EAAuB,CAC3BV,QAAS,OACTjH,QAAS,OACTqH,MAAO,OACPtH,OAAQ,iBACRmH,cAAe,SACfE,eAAgB,aAChBQ,SAAU,OACVC,UAAW,EACb,EAcMC,EAAM,CACVT,MAAO,OACT,EAEMU,EAAmB,CACvBR,YAAa,SACf,EA4WA,EA1WsB,IACpB,IAmSIS,EAnSE,GAAEC,CAAC,CAAE,CAAGC,CAAAA,EAAAA,EAAAA,EAAAA,CAAcA,CAAC,GAyWhBC,OAxWP,CAACC,EAAWC,EAAa,CAAGlJ,CAAAA,CAwWP,CAxWOA,EAAAA,QAAAA,CAAQA,EAAC,GACrC,CAACmJ,EAAYC,EAAc,CAAGpJ,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,GACtCqJ,EACJ/G,iBAAMrB,IAAI,CAAoB,UAAWqI,UAAwB,CAC7D,CAACC,EAAOC,EAAS,CAAGxJ,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAQ,EAAE,EACtC,CAACyJ,EAAOC,EAAS,CAAG1J,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,EAAC,GAC7B,CAAC2J,EAAaC,EAAe,CAAG5J,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAW,EAAE,EAErD6J,EAAWvH,GAAwB,gBAAfA,EAAMrB,IAAI,CAAqB,SAAW,SAC9D6I,EAAc,MAAOrG,IACZ,MAAMsG,EAAAA,CAAUA,CAACC,MAAM,CAAC,GAAevG,MAAAA,CAAZoG,EAAS,KAAM,OAAHpG,GACtD,EAEMwG,EAAa,IACjBb,EAAcc,GACdhB,GAAa,EACf,EAEMiB,EAAe,CAAClG,EAA8DmG,KAClF,IAAMC,EAAQ,IAAIV,EAAY,CAC9BU,CAAK,CAACD,EAAM,CAAGnG,EAAEC,MAAM,CAAC5E,KAAK,CAC7BsK,EAAeS,EACjB,EAEMC,EAAe,IAEnB,OADiBJ,GAAQA,EAAKxH,IAAI,CAAC6H,KAAK,CAAC,KAAKC,GAAG,IAE/C,IAAK,MACL,IAAK,MACL,IAAK,OACL,IAAK,MACL,IAAK,MACH,MAAO,UAAC7B,MAAAA,CAAI8B,IAAKP,EAAKQ,OAAO,CAAE/J,MAAOgI,GACxC,KAAK,MACH,MACE,UAACA,MAAAA,CACC8B,IAAI,gCACJ/J,UACiB,gBAAf4B,EAAMrB,IAAI,CAAqB,aAAe,cAItD,KAAK,OAmBL,QAlBE,MACE,UAAC0H,MAAAA,CACC8B,IAAI,iCACJ/J,UACiB,gBAAf4B,EAAMrB,IAAI,CAAqB,aAAe,cAItD,KAAK,MACL,IAAK,OACH,MACE,UAAC0H,MAAAA,CACC8B,IAAI,gCACJ/J,UACE4B,kBAAMrB,IAAI,CAAqB,aAAe,cAaxD,CACF,EAEM0J,EAAY,IAAMzB,GAAa,GAE/B0B,EAAgB,KACpB1B,GAAa,EACf,EAEM2B,EAAgB,IAEpB,IAAMC,EACJC,CAFFA,EAAe5B,CAAAA,GAEG4B,EAAaC,GAAG,CAC5B,CAAEC,SAAUF,EAAaC,GAAG,EAC5B,CAAEd,KAAMa,CAAa,EACrBG,EAASC,IAAAA,SAAW,CAACxD,EAAMmD,GAE3BM,EAAY,IAAIzB,EAAY,CAClCyB,EAAUC,MAAM,CAACH,EAAQ,GACzBtB,EAAewB,GAEftB,EAAYnC,CAAI,CAACuD,EAAO,CAACD,QAAQ,EACjCtD,EAAK0D,MAAM,CAACH,EAAQ,GACpB5I,EAAMgJ,QAAQ,CAAC3D,EAAMrF,EAAM8H,KAAK,CAAG9H,EAAM8H,KAAK,CAAG,GACjD,IAAMmB,EAAW,IAAIhC,EAAM,CAC3BgC,EAASF,MAAM,CAACE,EAASC,OAAO,CAACT,GAAe,GAChDvB,EAAS+B,GACTrC,GAAa,EACf,EAEMuC,EAAclC,EAAMnG,GAAG,CAAC,CAAC8G,EAAWwB,IAEtC,WAACjL,MAAAA,WACC,UAACkL,EAAAA,CAAGA,CAAAA,CAACC,GAAI,YACP,WAACnL,MAAAA,CAAIC,UAAU,gBACb,UAACiL,EAAAA,CAAGA,CAAAA,CACFE,GAAI,EACJC,GAAI,EACJpL,UACiB,gDAATO,IAAI,CACN,gDACA,oDAGLqJ,EAAaJ,KAEhB,UAACyB,EAAAA,CAAGA,CAAAA,CAACE,GAAI,EAAGC,GAAI,EAAGpL,UAAU,6BAC3B,WAACoD,EAAAA,CAAIA,CAAAA,WACH,WAACA,EAAAA,CAAIA,CAACiI,KAAK,EAACC,UAAU,qBACpB,UAAClI,EAAAA,CAAIA,CAACmI,KAAK,EAACvL,UAAU,gBAAQoI,EAAE,cAChC,UAAChF,EAAAA,CAAIA,CAACwD,OAAO,EACX4E,KAAK,KACLjL,KAAK,OACLtB,QAAQ,IACRL,MAAO4K,EAAKiC,aAAa,CAAGjC,EAAKiC,aAAa,CAAGjC,EAAKxH,IAAI,MAG9D,WAACoB,EAAAA,CAAIA,CAACiI,KAAK,EAACC,UAAU,wBACpB,UAAClI,EAAAA,CAAIA,CAACmI,KAAK,WACO,gBAAf3J,EAAMrB,IAAI,CACP6H,EAAE,uCACFA,EAAE,wBAER,UAAChF,EAAAA,CAAIA,CAACwD,OAAO,EACX8E,UAA0B,gBAAf9J,EAAMrB,IAAI,CAAqB,IAAMwG,OAChDyE,KAAK,KACLjL,KAAK,OACLxB,YACiB,gBAAf6C,EAAMrB,IAAI,CACN6H,EAAE,kCACFA,EAAE,sCAERxJ,MAAOqK,CAAW,CAAC+B,EAAE,CACrBnM,SAAU,GAAO4K,EAAalG,EAAGyH,aAKzC,UAACC,EAAAA,CAAGA,CAAAA,CACFE,GAAI,EACJC,GAAI,EACJpL,UAAU,gCACVQ,QAAS,IAAM+I,EAAWC,YAE1B,UAACmC,EAAAA,CAAMA,CAAAA,CAACC,QAAQ,gBAAQxD,EAAE,mBAIhC,WAACyD,EAAAA,CAAKA,CAAAA,CAACC,KAAMvD,EAAWwD,OAAQ9B,YAC9B,UAAC4B,EAAAA,CAAKA,CAACG,MAAM,EAACC,WAAW,aACvB,UAACJ,EAAAA,CAAKA,CAACK,KAAK,WAAE9D,EAAE,kBAElB,UAACyD,EAAAA,CAAKA,CAACM,IAAI,WAAE/D,EAAE,qCACf,WAACyD,EAAAA,CAAKA,CAACO,MAAM,YACX,UAACT,EAAAA,CAAMA,CAAAA,CAACC,QAAQ,YAAYpL,QAAS0J,WAClC9B,EAAE,YAEL,UAACuD,EAAAA,CAAMA,CAAAA,CAACC,QAAQ,UAAUpL,QAAS,IAAM2J,EAAcX,YACpDpB,EAAE,iBAlED4C,IA0EdzL,CAAAA,EAAAA,EAAAA,SAAAA,CAASA,CAAC,KACRsJ,EAAMwD,OAAO,CAAC,GAAUC,IAAIC,eAAe,CAAC/C,EAAKQ,OAAO,GACxD/C,EAAO,EAAE,EACR,EAAE,EAEL1H,CAAAA,EAAAA,EAAAA,SAAAA,CAASA,CAAC,KACRqC,EAAM4K,cAAc,CAACvD,EACvB,EAAG,CAACA,EAAY,EAEhB1J,CAAAA,EAAAA,EAAAA,SAAAA,CAASA,CAAC,KACR2J,EAAetH,EAAM6K,OAAO,CAC9B,EAAG,CAAC7K,EAAM6K,OAAO,CAAC,EAElBlN,CAAAA,EAAAA,EAAAA,SAAAA,CAASA,CAAC,KACRqC,GAAgC,SAAvBA,EAAM8K,YAAY,EAAe1D,GAAS,GAC/CpH,GAASA,EAAM+K,KAAK,EAAE,EAaf,IAZM/K,EAAM+K,KAAK,CAACjK,GAAG,CAAC,CAACkK,EAAWC,KACzC5F,EAAK6F,IAAI,CAAC,CACRvC,SAAUqC,EAAKtC,GAAG,CAClBZ,MAAO9H,EAAM8H,KAAK,CAAG9H,EAAM8H,KAAK,CAAG,EACnCnJ,KAAMqM,EAAK5K,IAAI,CAAC6H,KAAK,CAAC,IAAI,CAAC,EAAE,GAEV,CACnB,GAAG+C,CAAI,CACP5C,QAAS,GAAwC4C,MAAAA,CAArChE,8BAAsB,CAAC,gBAAuB,OAATgE,EAAKtC,GAAG,CAC3D,IAGkB,CAExB,EAAG,CAAC1I,EAAM+K,KAAK,CAAC,EAEhB,IAAMI,EAAc,MAAOC,EAAqBtD,KAC9C,GAAIsD,EAAaC,MAAM,CAAGvD,EACxB,GAAI,CACF,CAF6B,GAEvBwD,EAAY,IAAIC,SACtBD,EAAKE,MAAM,CAAC,OAAQJ,CAAY,CAACtD,EAAM,EACvC,IAAM2D,EAAM,MAAMhE,EAAAA,CAAUA,CAACiE,IAAI,CAACnE,EAAU+D,EAAM,CAChD,eAAgB,qBAClB,GACAjG,EAAK6F,IAAI,CAAC,CACRvC,SAAU8C,EAAI/C,GAAG,CACjBd,KAAMwD,CAAY,CAACtD,EAAM,CACzBA,MAAO9H,EAAM8H,KAAK,CAAG9H,EAAM8H,KAAK,CAAG,EACnCnJ,KAAMyM,CAAY,CAACtD,EAAM,CAAC1H,IAAI,CAAC6H,KAAK,CAAC,IAAI,CAAC,EAAE,GAE9CkD,EAAYC,EAActD,EAAQ,EACpC,CAAE,MAAO5C,EAAO,CACdiG,EAAYC,EAActD,EAAQ,EACpC,MAEA9H,EAAMgJ,QAAQ,CAAC3D,EAAMrF,EAAM8H,KAAK,CAAG9H,EAAM8H,KAAK,CAAG,EAErD,EAEM6D,EAASC,CAAAA,EAAAA,EAAAA,WAAAA,CAAWA,CAAC,MAAOC,IAChC,MAAMV,EAAYU,EAAc,GAChC,IAAMC,EAAWD,EAAa/K,GAAG,CAAC,GAChCiL,OAAOC,MAAM,CAACpE,EAAM,CAClBQ,QAASsC,IAAIuB,eAAe,CAACrE,EAC/B,IAEFT,EACID,EAAS,GAAe,IAAIgF,KAAcJ,EAAS,EACnD5E,EAAS,IAAI4E,EAAS,CAC5B,EAAG,EAAE,EAkBC,cACJK,CAAY,CACZC,eAAa,cACbC,CAAY,cACZC,CAAY,cACZC,CAAY,gBACZC,CAAc,CACf,CAAGC,CAAAA,EAAAA,EAAAA,EAAAA,CAAWA,CAAC,CACdC,OACE1M,GAASA,EAAMrB,IAAI,CACf,+MACA,UACNgO,SAAUxF,EACVyF,QAAS,EACTC,QAAS9F,EACT4E,SACAzH,UAhCF,CAgCa4I,QAhCJA,CAA8B,EACrC,GAAiB,UAAU,CAAvBvF,GACF,GAAkC,SAAS,CAAvCK,EAAKjJ,IAAI,CAACoO,SAAS,CAAC,EAAG,GAIzB,OADAC,EAAAA,EAAKA,CAAC9H,KAAK,CAACsB,EAAE,6BACP,CAAEyG,KAAM,oBAAqBC,QAAS,yBAA0B,CACzE,MACK,GAAiB,UAAU,CAAvB3F,GAC2B,OAAM,GAAI,EAAnC5I,IAAI,CAACoO,SAAS,CAAC,EAAG,GAE3B,OADAC,EAAAA,EAAKA,CAAC9H,KAAK,CAACsB,EAAE,6BACP,CAAEyG,KAAM,oBAAqBC,QAAS,yBAA0B,EAG3E,OAAO,IACT,CAkBA,GAEM7O,EAAQ8O,CAAAA,EAAAA,EAAAA,OAAAA,CAAOA,CACnB,IAAO,EACL,GAAG7H,CAAS,CACZ,GAAI+G,EAAe/F,EAAc,CAAE3G,QAAS,iBAAkB,CAAC,CAC/D,GAAI2M,EACA,CAAE3M,QAAS,oBAAqB,EAChC,CAAEA,QAAS,iBAAkB,CAAC,CAClC,GAAI4M,EAAe,CAAE5M,QAAS,gBAAiB,EAAI,CAAE2G,aAAY,CAAC,CACpE,EACA,CAAC+F,EAAcE,EAAa,EAK5BhG,EADEvG,GAAwB,eAAe,CAA9BA,EAAMrB,IAAI,CAEnB,UAACyO,QAAAA,CAAM/O,MAAO,CAAE2H,MAAO,SAAU,WAAIQ,EAAE,uBAIvC,UAAC4G,QAAAA,CAAM/O,MAAO,CAAE2H,MAAO,SAAU,WAAIQ,EAAE,oBAI3C,IAAM6G,EACJb,EAAenB,MAAM,CAAG,GAAKmB,CAAc,CAAC,EAAE,CAAC5E,IAAI,CAACgC,IAAI,CAAG7C,EAC7D,MACE,iCACE,UAAC5I,MAAAA,CACCC,UAAU,yDACVC,MAAO,CAAEuH,MAAO,OAAQxI,OAAQ,OAAQ,WAExC,WAACe,MAAAA,CAAK,GAAGgO,EAAa,OAAE9N,CAAM,EAAE,WAC9B,UAACiP,QAAAA,CAAO,GAAGlB,GAAe,GAC1B,UAACmB,EAAAA,CAAeA,CAAAA,CAACC,KAAMC,EAAAA,GAAgBA,CAAE7D,KAAK,KAAK5D,MAAM,SACzD,UAAC0H,IAAAA,CAAErP,MAAO,CAAE2H,MAAO,UAAW2H,aAAc,KAAM,WAC/CnH,EAAE,mDAGJ,CAACW,GACA,WAACiG,QAAAA,CAAM/O,MAAO,CAAE2H,MAAO,SAAU,YAC/B,UAAC4H,IAAAA,UAAE,UAAS,wCAGfrH,GACAvG,EAAMrB,IAAI,CACP0O,GACE,CAFU,EAEV,QAACD,QAAAA,CAAMhP,UAAU,6BACf,UAACmP,EAAAA,CAAeA,CAAAA,CACdC,KAAMK,EAAAA,GAAmBA,CACzBjE,KAAK,KACL5D,MAAM,QACL,IACFQ,EAAE,4CAaV+F,CAVGc,EAWF,WAACD,QAAAA,CAAMhP,UAAU,cAAcC,MAAO,CAAE2H,MAAO,SAAU,YACvD,UAACuH,EAAAA,CAAeA,CAAAA,CACdC,KAAMK,EAAAA,GAAmBA,CACzBjE,KAAK,KACL5D,MAAM,QACL,IACFQ,EAAE,mCAKVS,EAAMoE,MAAM,CAAG,GAAK,UAAClN,MAAAA,CAAIE,MAAO6H,WAAkBiD,MAGzD,wGC3bA,IAAM2E,EAAiBC,IAAAA,KAAe,CAAC,CAAC,QAAS,MAAM,EAC1CC,EAAgBD,IAAAA,SAAmB,CAAC,CAACD,EAAgBC,IAAAA,KAAe,CAAC,CAChFE,GAAIH,CACN,GAAIC,IAAAA,KAAe,CAAC,CAClBxE,GAAIuE,CACN,GAAIC,IAAAA,KAAe,CAAC,CAClBvE,GAAIsE,CACN,GAAIC,IAAAA,KAAe,CAAC,CAClBG,GAAIJ,CACN,GAAIC,IAAAA,KAAe,CAAC,CAClBI,IAAKL,CACP,GAAIC,IAAAA,MAAgB,CAAC,EAAE,eCJvB,IAAMK,EAAY,CAKhBjN,GAAI4M,IAAAA,MAAgB,CAEpBM,KAAMN,IAAAA,MAAgB,CAEtBnP,QAASmP,IAAAA,IAAc,CAEvBO,MAAOP,IAAAA,IAAc,CAACQ,UAAU,CAEhClR,SAAU0Q,IAAAA,IAAc,CAQxBS,MAAOR,EAEPS,SAAUV,EAFUC,EAEVD,MAAgB,CAE1BW,kBAAmBX,IAAAA,IAAc,CAMjCY,eAAgBZ,IAAAA,MAAgB,CAMhCa,YAAab,IAAAA,KAAe,CAAC,CAAC,OAAO,EAMrCc,KAAMd,IAAAA,IAAc,CAEpBe,SAAUf,IAAAA,MAAgB,CAE1B/D,QAAS+D,IAAAA,MAAgB,CAEzBnE,KAAMmE,IAAAA,MAAgB,EAYlBgB,EAA8BnO,EAAAA,UAAgB,CAAC,EAA9B,CAepBzB,QAfmD,EAApB,KAChCmP,CAAK,UACL/N,CAAQ,UACRuO,CAAQ,gBACRH,CAAc,SACd3E,CAAO,MACPJ,CAAI,UACJ6E,CAAQ,CACRC,mBAAiB,UACjBrR,CAAQ,MACRgR,CAAI,IACJlN,CAAE,aACFyN,CAAW,CACXC,MAAI,CACJ,GAAG7O,EACJ,SAAuBgP,CAAb,EAAaA,EAAAA,IAAAA,CAAKA,CAACC,CAAR,CAAQA,CAAQA,CAAE,CACtC9P,IAAKA,EACL,GAAGa,CAAK,CACRO,SAAU,CAAc2O,CAAAA,EAAAA,EAAAA,GAAAA,CAAIA,CAACC,EAAAA,CAAcA,CAAE,CAC3ChO,GAAIA,EACJkN,KAAMA,EACNzE,KAAMA,EACNI,QAASA,EACT3M,SAAUA,EACV+R,cAAeN,EACfvO,SAAU+N,CACZ,GAAiBY,CAAAA,EAAAA,EAAAA,GAAAA,CAAIA,CAACG,EAAAA,CAAYA,CAAE,CAClCC,KAAMb,EACNc,cAAeb,EACfC,eAAgBA,EAChB3E,QAAS4E,EACTC,KAAMA,EACNtO,SAAUA,CACZ,GACF,KACAwO,EAAe/K,WAAW,CAAG,iBAC7B+K,EAAeX,SAAS,CAAGA,EAC3B,MAAeW,cAAcA,EAAC,uQC2K9B,MAlQwB,IACpB,GAAM,CAAEvI,CAAC,MAAEgJ,CAAI,CAAE,CAAG/I,CAAAA,EAAAA,EAAAA,EAAAA,CAAcA,CAAC,CAiQxBgJ,SA/PLC,EAAW1P,EAAM2P,EA+PGF,EAAC,EA/PE,EAAwB,iBAApBzP,EAAM2P,MAAM,CAAC,EAAE,EAAuB3P,EAAM2P,MAAM,CAAC,EAAE,CAChFC,EAAkB,CACpBtB,MAAO,GACPuB,YAAa,GACbC,aAAc,GACdC,WAAW,EACXC,OAAQ,EAAE,CACVC,WAAY,EAAE,CACdC,SAAUV,EAAKU,QAAQ,EAErB,CAACC,EAAYC,EAAc,CAAG1S,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAMkS,GAC5C,CAACS,EAAoBC,EAAsB,CAAG5S,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAQ,EAAE,EAChE,CAAC6S,EAAeC,EAAiB,CAAG9S,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAQ,EAAE,EACtD,CAAC+S,EAAMC,EAAQ,CAAGhT,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAQ,EAAE,EACpC,CAACoS,EAAca,EAAgB,CAAGjT,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAQ,EAAE,EAUpDkT,EAAiB,IACnBR,EAAc,GAAqB,EAC/B,GAAGlE,CAAS,CACX,EAF8B,OAEjBlB,EAAF,IAAW,CAC3B,EACJ,EAEM6F,EAAa,CACfC,MAAO,CAAC,EACRC,KAAM,CAAEzC,MAAO,KAAM,EACrBvH,MAAO,IACPiK,OAAQ,8BACZ,EAEMC,EAAU,UACZ,IAAMC,EAAW,MAAMzJ,EAAAA,CAAUA,CAAC0J,GAAG,CAAC,YAAaN,EAC/CK,IACAR,EAAQQ,EAASE,EADP,EACW,CAE7B,EAYMC,EAAoB,IACtBjB,EAAelE,GAAoB,EAC/B,GAAGA,CAAS,CACZ2D,EAF+B,UAElB7S,EACjB,EACJ,EASMsU,EAAQ,IACV,IAAM5I,EAAMvH,EAAGL,GAAG,CAAC,GAAUkK,EAAKrC,QAAQ,EAC1CyH,EAAc,GAAqB,EAAE,GAAGlE,CAAS,CAAE8D,EAAhB,KAAwBtH,EAAI,EACnE,EAEM6I,EAAY,IACdnB,EAAc,GAAqB,EAAE,GAAGlE,CAAS,CAAE+D,EAAhB,SAA4BuB,EAAU,EAC7E,EAEMzN,EAAe,MAAO0N,EAAYpQ,SAWhC6P,EACAQ,EAXJD,EAAM3O,cAAc,GACpB,IAAM0F,EAAM,CACR8F,MAAO6B,EAAW7B,KAAK,CAAC3J,IAAI,GAC5BkL,YAAaM,EAAWN,WAAW,CACnCE,UAAWI,EAAWJ,SAAS,CAC/BC,OAAQG,EAAWH,MAAM,CACzBC,WAAYE,EAAWF,UAAU,CACjCC,SAAUC,EAAWD,QAAQ,EAK7BR,GACAgC,EAAW,KADD,4DAEVR,EAAW,MAAMzJ,EAAAA,CAAUA,CAACkK,KAAK,CAAC,gBAAgC,OAAhB3R,EAAM2P,MAAM,CAAC,EAAE,EAAInH,KAErEkJ,EAAW,+DACXR,EAAW,MAAMzJ,EAAAA,CAAUA,CAACiE,IAAI,CAAC,eAAgBlD,IAEjD0I,GAAYA,EAASxI,GAAG,EAAE,EAC1BsE,EAAKA,CAAC4E,OAAO,CAACpL,EAAEkL,IAChBG,IAAAA,IAAW,CAAC,2BAEZ7E,EAAAA,EAAKA,CAAC9H,KAAK,CAACgM,EAEpB,EAEMY,EAAkB,UACpB,IAAMZ,EAAW,MAAMzJ,EAAAA,CAAUA,CAAC0J,GAAG,CAAC,iBAEtC,GAAID,GAAYa,MAAMC,OAAO,CAACd,EAASE,IAAI,EAAG,CAC1C,IAAMA,EAAOF,EAASE,IAAI,CAACa,MAAM,CAAC,GAAejH,cAAKsD,KAAK,EAC3D8C,EAAKc,OAAO,CAAClB,CAHAtI,IAAK,GAAI4F,MAAO,QAAS,GAItCqC,EAAgBS,EACpB,CACJ,EAEMe,EAAqB,UACvB,IAAMjB,EAAW,MAAMzJ,EAAAA,CAAUA,CAAC0J,GAAG,CAAC,gBAAgC,OAAhBnR,EAAM2P,MAAM,CAAC,EAAE,GACrE,GAAIuB,EAAU,CACV,GAAM,cAAEpB,CAAY,CAAE,CAAGoB,EACzBA,EAASpB,YAAY,CAAGA,GAAgBA,EAAapH,GAAG,CAAGoH,EAAapH,GAAG,CAAG,GAC9E4H,EAAsBY,EAASlB,MAAM,CAAGkB,EAASlB,MAAM,CAAG,EAAE,EAC5DQ,EAAiBU,EAASjB,UAAU,CAAGiB,EAASjB,UAAU,CAAG,EAAE,EAC/DG,EAAc,GAAqB,EAAE,GAAGlE,CAAS,CAAE,EAAhB,CAAmBgF,CAAQ,CAAC,EACnE,CACJ,EACAvT,CAAAA,EAAAA,EAAAA,SAAAA,CAASA,CAAC,KACNsT,IACIvB,GACAyC,IAEJL,GAHc,EAIf,EAAE,EAEL,IAAMM,EAAU7U,CAAAA,EAAAA,EAAAA,MAAAA,CAAMA,CAAC,MAEvB,MACI,UAAC8U,EAAAA,CAASA,CAAAA,CAACjU,UAAU,WAAWkU,KAAK,aACjC,UAACC,EAAAA,CAAIA,CAAAA,UACD,UAACtQ,EAAAA,CAAqBA,CAAAA,CAACE,SAAU4B,EAAc5E,IAAKiT,EAAS9P,cAAe6N,EAAYqC,oBAAoB,WACxG,WAACD,EAAAA,CAAIA,CAAChI,IAAI,YACN,UAACkI,EAAAA,CAAGA,CAAAA,UACA,UAACpJ,EAAAA,CAAGA,CAAAA,UACA,UAACkJ,EAAAA,CAAIA,CAACjI,KAAK,WAAE9D,EAAE,mDAGvB,UAACkM,KAAAA,CAAAA,GACD,UAACD,EAAAA,CAAGA,CAAAA,CAACrU,UAAU,gBACX,UAACiL,EAAAA,CAAGA,CAAAA,CAACE,EAAE,IAACC,GAAI,GAAIyE,GAAI,YAChB,WAACzM,EAAAA,CAAIA,CAACiI,KAAK,YACP,UAACjI,EAAAA,CAAIA,CAACmI,KAAK,WAAEnD,EAAE,qCACf,WAACxE,EAAAA,EAAWA,CAAAA,CACR5B,KAAK,QACLe,GAAG,QACHnE,MAAOmT,EAAW7B,KAAK,CACvBpK,UAAW,GAAoC,KAAjBlH,EAAM2H,IAAI,GACxCV,QAAQ,IACR3D,aAAc,CACV4D,UAAWsC,EAAE,8CACjB,EACAvJ,SAnHf,CAmHyB0V,GAlH1C,GAAIhR,EAAEC,MAAM,CAAE,CACV,GAAM,MAAExB,CAAI,OAAEpD,CAAK,CAAE,CAAG2E,EAAEC,MAAM,CAChCwO,EAAc,GAAqB,EAC/B,GAAGlE,CAAS,CACZ,CAAC9L,CAF8B,CAEzB,CAAEpD,CACZ,GACJ,CACJ,YA6GoC,UAAC4V,SAAAA,CAAc5V,MAAM,YAAG,UAAZ,IACZ,UAAC4V,SAAAA,CAAoB5V,MAAM,kBAAS,UAAxB,UACZ,UAAC4V,SAAAA,CAAsB5V,MAAM,oBAAW,YAA5B,sBAK5B,UAACyV,EAAAA,CAAGA,CAAAA,CAACrU,UAAU,gBACX,UAACiL,EAAAA,CAAGA,CAAAA,CAACE,GAAI,WACL,WAAC/H,EAAAA,CAAIA,CAACiI,KAAK,YACP,UAACjI,EAAAA,CAAIA,CAACmI,KAAK,EAACvL,UAAU,gBACjBoI,EAAE,8CAEP,UAACuI,EAAAA,CAAcA,CAAAA,CACXT,MAAO6B,EAAWD,QAAQ,CAAC2C,WAAW,GACtC7I,QAAQ,oBACR7I,GAAG,iBACH/C,UAAU,oBAETqS,GACGA,EAAK3P,GAAG,CAAC,CAACkK,EAAM5B,IACZ,UAACjL,MAAAA,UACG,WAAC8Q,EAAAA,CAAQA,CAAC6D,IAAI,EACVC,OAAQ/H,EAAKgI,IAAI,GAAK7C,EAAWD,QAAQ,CACzC+C,SAAUjI,EAAKtC,GAAG,CAClB9J,QAAS,IAAMgS,EAAe5F,aAE7BA,EAAKgI,IAAI,CAACH,WAAW,GAAG,IAAE7H,EAAKsD,KAAK,CAACuE,WAAW,OAN/CzJ,aAclC,UAACqJ,EAAAA,CAAGA,CAAAA,CAACrU,UAAU,gBACX,UAACiL,EAAAA,CAAGA,CAAAA,UACA,WAAC7H,EAAAA,CAAIA,CAACiI,KAAK,YACP,UAACjI,EAAAA,CAAIA,CAACmI,KAAK,WAAEnD,EAAE,2CACf,UAAC1G,EAAAA,CAAeA,CAAAA,CAACC,YAAaoQ,EAAWN,WAAW,CAAE5S,SAAU,GAAiBoU,EAAkB6B,YAI/G,UAACT,EAAAA,CAAGA,CAAAA,CAACrU,UAAU,gBACX,UAACiL,EAAAA,CAAGA,CAAAA,UACA,WAAC7H,EAAAA,CAAIA,CAACiI,KAAK,YACP,UAACjI,EAAAA,CAAIA,CAACmI,KAAK,WAAEnD,EAAE,sCACf,UAACE,EAAAA,CAAaA,CAAAA,CACVqE,MAAOsF,EACPxF,QAAS0F,EACTvH,SAAU,GAAesI,EAAMnQ,GAC/ByJ,eAAgB,GAAsB2G,EAAUC,YAKhE,UAACiB,EAAAA,CAAGA,CAAAA,CAACrU,UAAU,gBACX,UAACiL,EAAAA,CAAGA,CAAAA,UACA,UAAC7H,EAAAA,CAAIA,CAACiI,KAAK,WACP,UAACjI,EAAAA,CAAIA,CAACC,KAAK,EACPC,QAASyO,EAAWJ,SAAS,CAC7B3P,KAAK,YACLxB,QAlKf,CAkKwBuU,IAjKzC/C,EAAelE,GAAoB,EAC/B,GAAGA,CAAS,CACZ6D,EAF+B,QAEpB,CAAC7D,EAAU6D,SAAS,CACnC,EACJ,EA8JoC3O,MAAOoF,EAAE,uCACT7H,KAAK,mBAMrB,UAAC8T,EAAAA,CAAGA,CAAAA,CAACrU,UAAU,gBACX,WAACiL,EAAAA,CAAGA,CAAAA,WACA,UAACU,EAAAA,CAAMA,CAAAA,CAAC3L,UAAU,OAAOO,KAAK,SAASqL,QAAQ,mBAC1CxD,EAAE,sCAEP,UAACuD,EAAAA,CAAMA,CAAAA,CAAC3L,UAAU,OAAOQ,QA7NhC,CA6NyCwU,IA5N1DhD,EAAcR,GACdU,EAAsB,EAAE,EACxBE,EAAiB,EAAE,EAEnB6C,OAAOC,QAAQ,CAAC,EAAG,EACvB,EAuN4EtJ,QAAQ,gBACnDxD,EAAE,qCAEP,UAAC+M,IAAIA,CACDlF,KAAK,6BACLlK,GAAK,OAFJoP,2BAID,UAACxJ,EAAAA,CAAMA,CAAAA,CAACC,QAAQ,qBAAaxD,EAAE,qDASnE", "sources": ["webpack://_N_E/./components/common/SimpleRichTextEditor.tsx", "webpack://_N_E/./shared/quill-editor/quill-editor.component.tsx", "webpack://_N_E/./components/common/FormikRadio.tsx", "webpack://_N_E/./components/common/FormValidation.tsx", "webpack://_N_E/./components/common/ValidationFormWrapper.tsx", "webpack://_N_E/./components/common/FormikTextInput.tsx", "webpack://_N_E/./components/common/ReactDropZone.tsx", "webpack://_N_E/./node_modules/react-bootstrap/esm/types.js", "webpack://_N_E/./node_modules/react-bootstrap/esm/DropdownButton.js", "webpack://_N_E/./pages/adminsettings/landingPage/form.tsx"], "sourcesContent": ["import React, { useRef, useEffect, useState } from 'react';\r\n\r\ninterface SimpleRichTextEditorProps {\r\n  value: string;\r\n  onChange: (content: string) => void;\r\n  placeholder?: string;\r\n  height?: number;\r\n  disabled?: boolean;\r\n}\r\n\r\nconst SimpleRichTextEditor: React.FC<SimpleRichTextEditorProps> = ({\r\n  value,\r\n  onChange,\r\n  placeholder = 'Write something...',\r\n  height = 300,\r\n  disabled = false,\r\n}) => {\r\n  const editorRef = useRef<HTMLDivElement>(null);\r\n  const [isFocused, setIsFocused] = useState(false);\r\n\r\n  // Initialize editor with HTML content\r\n  useEffect(() => {\r\n    if (editorRef.current && typeof window !== 'undefined') {\r\n      // Only update if the editor doesn't have focus to prevent cursor jumping\r\n      if (!isFocused && editorRef.current.innerHTML !== value) {\r\n        editorRef.current.innerHTML = value || '';\r\n      }\r\n    }\r\n  }, [value, isFocused]);\r\n\r\n  // Handle content changes\r\n  const handleInput = () => {\r\n    if (editorRef.current && onChange) {\r\n      onChange(editorRef.current.innerHTML);\r\n    }\r\n  };\r\n\r\n  // Simple toolbar buttons\r\n  const execCommand = (command: string, value?: string) => {\r\n    if (typeof document !== 'undefined') {\r\n      document.execCommand(command, false, value || '');\r\n      handleInput();\r\n      editorRef.current?.focus();\r\n    }\r\n  };\r\n\r\n  return (\r\n    <div className=\"simple-rich-text-editor\" style={{ border: '1px solid #ccc' }}>\r\n      {typeof window !== 'undefined' && (\r\n      <>\r\n        <div className=\"toolbar\" style={{ padding: '8px', borderBottom: '1px solid #ccc', background: '#f5f5f5' }}>\r\n            <button\r\n              type=\"button\"\r\n              onClick={() => execCommand('bold')}\r\n              style={{ margin: '0 5px', padding: '3px 8px' }}\r\n            >\r\n              <strong>B</strong>\r\n            </button>\r\n            <button\r\n              type=\"button\"\r\n              onClick={() => execCommand('italic')}\r\n              style={{ margin: '0 5px', padding: '3px 8px' }}\r\n            >\r\n              <em>I</em>\r\n            </button>\r\n            <button\r\n              type=\"button\"\r\n              onClick={() => execCommand('underline')}\r\n              style={{ margin: '0 5px', padding: '3px 8px' }}\r\n            >\r\n              <u>U</u>\r\n            </button>\r\n            <button\r\n              type=\"button\"\r\n              onClick={() => execCommand('insertOrderedList')}\r\n              style={{ margin: '0 5px', padding: '3px 8px' }}\r\n            >\r\n              OL\r\n            </button>\r\n            <button\r\n              type=\"button\"\r\n              onClick={() => execCommand('insertUnorderedList')}\r\n              style={{ margin: '0 5px', padding: '3px 8px' }}\r\n            >\r\n              UL\r\n            </button>\r\n            <button\r\n              type=\"button\"\r\n              onClick={() => {\r\n                const url = prompt('Enter the link URL');\r\n                if (url) execCommand('createLink', url);\r\n              }}\r\n              style={{ margin: '0 5px', padding: '3px 8px' }}\r\n            >\r\n              Link\r\n            </button>\r\n          </div>\r\n          <div\r\n            ref={editorRef}\r\n            contentEditable={!disabled}\r\n            onInput={handleInput}\r\n            onFocus={() => setIsFocused(true)}\r\n            onBlur={() => setIsFocused(false)}\r\n            style={{\r\n              padding: '15px',\r\n              minHeight: height,\r\n              maxHeight: height * 2,\r\n              overflow: 'auto',\r\n              outline: 'none',\r\n            }}\r\n            data-placeholder={!value ? placeholder : ''}\r\n            suppressContentEditableWarning={true}\r\n          >\r\n          </div>\r\n      </>\r\n      )}\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default SimpleRichTextEditor;\r\n", "// React Imports\r\nimport React from \"react\";\r\nimport SimpleRichTextEditor from \"../../components/common/SimpleRichTextEditor\";\r\n\r\ninterface IEditorComponentProps {\r\n  initContent: string | undefined;\r\n  onChange: Function;\r\n}\r\n\r\nexport const EditorComponent: React.FC<IEditorComponentProps> = (props) => {\r\n  const { initContent, onChange } = props;\r\n\r\n  return (\r\n    <SimpleRichTextEditor\r\n      value={initContent || \"\"}\r\n      onChange={(content) => onChange(content)}\r\n    />\r\n  );\r\n};\r\n", "import React from 'react';\r\nimport { useFormikContext, Field } from 'formik';\r\nimport { Form } from 'react-bootstrap';\r\n\r\ninterface RadioItemProps {\r\n  id: string;\r\n  label: string;\r\n  value: string;\r\n  name?: string;\r\n  disabled?: boolean;\r\n}\r\n\r\ninterface RadioGroupProps {\r\n  name: string;\r\n  valueSelected: string;\r\n  onChange: (e: React.ChangeEvent<HTMLInputElement>) => void;\r\n  errorMessage?: string;\r\n  children: React.ReactNode;\r\n}\r\n\r\n// Type Guard to ensure child.props is an object\r\nfunction isObject(props: any): props is { [key: string]: any } {\r\n  return typeof props === 'object' && props !== null;\r\n}\r\n\r\nconst RadioItem: React.FC<RadioItemProps> = ({ id, label, value, name, disabled }) => {\r\n  const { values, setFieldValue } = useFormikContext<any>();\r\n  const fieldName = name || id;\r\n\r\n  return (\r\n    <Form.Check\r\n      type=\"radio\"\r\n      id={id}\r\n      label={label}\r\n      value={value}\r\n      name={fieldName}\r\n      checked={values[fieldName] === value}\r\n      onChange={(e) => {\r\n        setFieldValue(fieldName, e.target.value);\r\n      }}\r\n      disabled={disabled}\r\n      inline\r\n    />\r\n  );\r\n};\r\n\r\nconst RadioGroup: React.FC<RadioGroupProps> = ({\r\n  name,\r\n  valueSelected,\r\n  onChange,\r\n  errorMessage,\r\n  children,\r\n}) => {\r\n  const { errors, touched } = useFormikContext<any>();\r\n  const hasError = touched[name] && errors[name];\r\n\r\n  // Create a context to pass the name to RadioItems\r\n  const radioContext = React.useMemo(() => ({ name }), [name]);\r\n\r\n  // Clone children to pass the name, ensuring props are spread safely\r\n  const childrenWithProps = React.Children.map(children, (child) => {\r\n    if (React.isValidElement(child)) {\r\n      // Ensure child.props is an object before spreading\r\n      if (isObject(child.props)) {\r\n        return React.cloneElement(child as React.ReactElement<RadioItemProps>, {\r\n          name,\r\n          ...child.props, // Safely spread child.props\r\n        });\r\n      }\r\n    }\r\n    return child;\r\n  });\r\n\r\n  return (\r\n    <div>\r\n      <div className=\"radio-group\">\r\n        {childrenWithProps}\r\n      </div>\r\n      {hasError && (\r\n        <div className=\"invalid-feedback d-block\">\r\n          {errorMessage || (typeof errors[name] === 'string' ? errors[name] : String(errors[name]))}\r\n        </div>\r\n      )}\r\n    </div>\r\n  );\r\n};\r\n\r\nexport const Radio = {\r\n  RadioGroup,\r\n  RadioItem,\r\n};\r\n\r\nexport default Radio;\r\n", "// This file exports all the components needed to replace react-bootstrap4-form-validation\r\n// It serves as a drop-in replacement for the original library\r\n\r\nimport ValidationForm from './ValidationFormWrapper';\r\nimport { TextInput, SelectGroup } from './FormikTextInput';\r\nimport { Radio } from './FormikRadio';\r\n\r\n// Export all components\r\nexport {\r\n  ValidationForm,\r\n  TextInput,\r\n  SelectGroup,\r\n  Radio\r\n};\r\n\r\n// Also export as default for convenience\r\nexport default {\r\n  ValidationForm,\r\n  TextInput,\r\n  SelectGroup,\r\n  Radio\r\n};\r\n", "import React, { forwardRef } from 'react';\r\nimport { Formik, Form, FormikProps, FormikHelpers } from 'formik';\r\nimport * as Yup from 'yup';\r\n\r\n// This is a wrapper component that replaces react-bootstrap4-form-validation with Formik\r\ninterface MockEvent {\r\n  preventDefault: () => void;\r\n  stopPropagation: () => void;\r\n  currentTarget: (EventTarget & Element) | null;\r\n  target: EventTarget | null;\r\n  nativeEvent: Event;\r\n  bubbles: boolean;\r\n  cancelable: boolean;\r\n  defaultPrevented: boolean;\r\n  eventPhase: number;\r\n  isTrusted: boolean;\r\n  timeStamp: number;\r\n  type: string;\r\n  isDefaultPrevented: () => boolean;\r\n  isPropagationStopped: () => boolean;\r\n  persist: () => void;\r\n}\r\n\r\ninterface ValidationFormWrapperProps {\r\n  children: React.ReactNode | ((formikProps: FormikProps<any>) => React.ReactNode);\r\n  onSubmit: (event: React.FormEvent | MockEvent, values?: Record<string, any>, actions?: FormikHelpers<Record<string, any>>) => void;\r\n  onErrorSubmit?: (errors: any) => void;\r\n  initialValues?: Record<string, any>;\r\n  enableReinitialize?: boolean;\r\n  autoComplete?: string;\r\n  className?: string;\r\n  onKeyPress?: (e: any) => void;\r\n}\r\n\r\nconst ValidationFormWrapper = forwardRef<HTMLFormElement, ValidationFormWrapperProps>((props, ref) => {\r\n  const { children, onSubmit, autoComplete, className, onKeyPress, initialValues, ...rest } = props;\r\n\r\n  // Create an empty validation schema by default\r\n  const validationSchema = Yup.object().shape({});\r\n\r\n  return (\r\n    <Formik\r\n      initialValues={initialValues || {}}\r\n      validationSchema={validationSchema}\r\n      onSubmit={(values: Record<string, any>, actions: FormikHelpers<Record<string, any>>) => {\r\n        // Create a mock event object with preventDefault method and currentTarget\r\n        const mockEvent: MockEvent = {\r\n          preventDefault: () => {},\r\n          stopPropagation: () => {},\r\n          currentTarget: null, // Set to null to avoid checkValidity errors\r\n          target: null,\r\n          nativeEvent: new Event('submit'),\r\n          bubbles: false,\r\n          cancelable: true,\r\n          defaultPrevented: false,\r\n          eventPhase: 0,\r\n          isTrusted: false,\r\n          timeStamp: Date.now(),\r\n          type: 'submit',\r\n          isDefaultPrevented: () => false,\r\n          isPropagationStopped: () => false,\r\n          persist: () => {}\r\n        };\r\n\r\n        if (onSubmit) {\r\n          // Pass the mock event object to maintain compatibility with the original code\r\n          onSubmit(mockEvent, values, actions);\r\n        }\r\n      }}\r\n      {...rest}\r\n    >\r\n      {(formikProps: FormikProps<any>) => (\r\n        <Form\r\n          ref={ref}\r\n          onSubmit={formikProps.handleSubmit}\r\n          autoComplete={autoComplete}\r\n          className={className}\r\n          onKeyPress={onKeyPress}\r\n        >\r\n          {typeof children === 'function' ? children(formikProps) : children}\r\n        </Form>\r\n      )}\r\n    </Formik>\r\n  );\r\n});\r\n\r\nValidationFormWrapper.displayName = 'ValidationFormWrapper';\r\n\r\nexport default ValidationFormWrapper;\r\n", "import React from 'react';\r\nimport { Form } from 'react-bootstrap';\r\nimport { Field, ErrorMessage, useField } from 'formik';\r\n\r\n// This component mimics the TextInput component from react-bootstrap4-form-validation\r\n// but uses Formik under the hood\r\nexport const TextInput = ({\r\n  name,\r\n  id,\r\n  required,\r\n  validator,\r\n  errorMessage,\r\n  onChange,\r\n  value,\r\n  as,\r\n  multiline,\r\n  rows,\r\n  pattern,\r\n  ...props\r\n}: any) => {\r\n  // Create a custom validation function that mimics the original validator\r\n  const validate = (val: any) => {\r\n    // Safely convert value to string and trim\r\n    const stringVal = typeof val === 'string' ? val : String(val || '');\r\n    if (required && (!val || stringVal.trim() === '')) {\r\n      return errorMessage?.validator || 'This field is required';\r\n    }\r\n\r\n    if (validator && !validator(val)) {\r\n      return errorMessage?.validator || 'Invalid value';\r\n    }\r\n\r\n    if (pattern && val) {\r\n      const regex = new RegExp(pattern);\r\n      if (!regex.test(val)) {\r\n        return errorMessage?.pattern || 'Invalid format';\r\n      }\r\n    }\r\n\r\n    return undefined;\r\n  };\r\n\r\n  return (\r\n    <Field name={name} validate={validate}>\r\n      {({ field, meta }: any) => (\r\n        <>\r\n          <Form.Control\r\n            {...field}\r\n            {...props}\r\n            id={id}\r\n            as={as || 'input'}\r\n            rows={rows}\r\n            isInvalid={meta.touched && !!meta.error}\r\n            onChange={(e: React.ChangeEvent<HTMLInputElement>) => {\r\n              field.onChange(e);\r\n              if (onChange) onChange(e);\r\n            }}\r\n            value={value !== undefined ? value : field.value}\r\n          />\r\n          {meta.touched && meta.error ? (\r\n            <Form.Control.Feedback type=\"invalid\">\r\n              {meta.error}\r\n            </Form.Control.Feedback>\r\n          ) : null}\r\n        </>\r\n      )}\r\n    </Field>\r\n  );\r\n};\r\n\r\n// This component mimics the SelectGroup component from react-bootstrap4-form-validation\r\n// but uses Formik under the hood\r\nexport const SelectGroup = ({\r\n  name,\r\n  id,\r\n  required,\r\n  errorMessage,\r\n  onChange,\r\n  value,\r\n  children,\r\n  ...props\r\n}: any) => {\r\n  // Create a custom validation function that mimics the original validator\r\n  const validate = (val: any) => {\r\n    if (required && (!val || val === '')) {\r\n      return errorMessage?.validator || 'This field is required';\r\n    }\r\n\r\n    return undefined;\r\n  };\r\n\r\n  return (\r\n    <Field name={name} validate={validate}>\r\n      {({ field, meta }: any) => (\r\n        <>\r\n          <Form.Control\r\n            as=\"select\"\r\n            {...field}\r\n            {...props}\r\n            id={id}\r\n            isInvalid={meta.touched && !!meta.error}\r\n            onChange={(e: React.ChangeEvent<HTMLSelectElement>) => {\r\n              field.onChange(e);\r\n              if (onChange) onChange(e);\r\n            }}\r\n            value={value !== undefined ? value : field.value}\r\n          >\r\n            {children}\r\n          </Form.Control>\r\n          {meta.touched && meta.error ? (\r\n            <Form.Control.Feedback type=\"invalid\">\r\n              {meta.error}\r\n            </Form.Control.Feedback>\r\n          ) : null}\r\n        </>\r\n      )}\r\n    </Field>\r\n  );\r\n};\r\n\r\n// Export both components as named exports and as a default object\r\nexport default {\r\n  TextInput,\r\n  SelectGroup\r\n};\r\n", "//Import Library\r\nimport React, { useMemo, useEffect, useState, useCallback } from \"react\";\r\nimport { useDropzone } from \"react-dropzone\";\r\nimport { FontAwesomeIcon } from \"@fortawesome/react-fontawesome\";\r\nimport {\r\n  faExclamationCircle,\r\n  faCloudUploadAlt,\r\n} from \"@fortawesome/free-solid-svg-icons\";\r\nimport { Form, Button, Modal, Col } from \"react-bootstrap\";\r\nimport _ from \"lodash\";\r\n\r\nimport toast from 'react-hot-toast';\r\n\r\n//Import services/components\r\nimport apiService from \"../../services/apiService\";\r\nimport { useTranslation } from 'next-i18next';\r\n\r\n// Define type for temp array items\r\ninterface TempItem {\r\n  serverID: string;\r\n  file?: any;\r\n  index: number;\r\n  type: string;\r\n}\r\n\r\nlet temp: TempItem[] = [];\r\n\r\nconst baseStyle: any = {\r\n  flex: 1,\r\n  display: \"flex\",\r\n  flexDirection: \"column\",\r\n  alignItems: \"center\",\r\n  justifyContent: \"center\",\r\n  width: \"100%\",\r\n  height: \"100%\",\r\n  borderWidth: 0.1,\r\n  borderColor: \"#fafafa\",\r\n  backgroundColor: \"#fafafa\",\r\n  color: \"black\",\r\n  transition: \"border  .24s ease-in-out\",\r\n  padding: \"15px\",\r\n};\r\n\r\nconst thumb: any = {\r\n  display: \"flex\",\r\n  borderRadius: 2,\r\n  border: \"1px solid #ddd\",\r\n  margin: 8,\r\n  height: 175,\r\n  boxShadow: \"0 0 15px 0.25px rgba(0,0,0,0.15)\",\r\n  boxSizing: \"border-box\",\r\n};\r\n\r\nconst thumbsContainer: any = {\r\n  display: \"flex\",\r\n  padding: \"10px\",\r\n  width: \"100%\",\r\n  border: \"2px solid gray\",\r\n  flexDirection: \"column\",\r\n  justifyContent: \"flex-start\",\r\n  flexWrap: \"wrap\",\r\n  marginTop: 20,\r\n};\r\n\r\nconst thumbInner: any = {\r\n  display: \"flex\",\r\n  minWidth: 0,\r\n};\r\n\r\nconst deleteIcon: any = {\r\n  display: \"flex\",\r\n  justifyContent: \"center\",\r\n  alignItems: \"center\",\r\n  marginLeft: 30,\r\n};\r\n\r\nconst img = {\r\n  width: \"150px\",\r\n};\r\n\r\nconst activeStyle: any = {\r\n  borderColor: \"#2196f3\",\r\n};\r\n\r\nconst ReactDropZone = (props: any) => {\r\n  const { t } = useTranslation('common');\r\n  const [modalShow, setModalShow] = useState(false);\r\n  const [deleteFile, setDeleteFile] = useState();\r\n  const limit: any =\r\n    props.type == \"application\" ? 20971520 : process.env.UPLOAD_LIMIT;\r\n  const [files, setFiles] = useState<any[]>([]);\r\n  const [multi, setMulti] = useState(true);\r\n  const [imageSource, setImageSource] = useState<string[]>([]);\r\n\r\n  const endpoint = props && props.type === \"application\" ? \"/files\" : \"/image\";\r\n  const imageDelete = async (id: string) => {\r\n    const _res = await apiService.remove(`${endpoint}/${id}`);\r\n  };\r\n\r\n  const removeFile = (file: any) => {\r\n    setDeleteFile(file);\r\n    setModalShow(true);\r\n  };\r\n\r\n  const handleSource = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>, index: number) => {\r\n    const items = [...imageSource];\r\n    items[index] = e.target.value;\r\n    setImageSource(items);\r\n  };\r\n\r\n  const getComponent = (file: any) => {\r\n    const fileType = file && file.name.split(\".\").pop();\r\n    switch (fileType) {\r\n      case \"JPG\":\r\n      case \"jpg\":\r\n      case \"jpeg\":\r\n      case \"jpg\":\r\n      case \"png\":\r\n        return <img src={file.preview} style={img} />;\r\n      case \"pdf\":\r\n        return (\r\n          <img\r\n            src=\"/images/fileIcons/pdfFile.png\"\r\n            className={\r\n              props.type === \"application\" ? \"docPreview\" : \"imgPreview\"\r\n            }\r\n          />\r\n        );\r\n      case \"docx\":\r\n        return (\r\n          <img\r\n            src=\"/images/fileIcons/wordFile.png\"\r\n            className={\r\n              props.type === \"application\" ? \"docPreview\" : \"imgPreview\"\r\n            }\r\n          />\r\n        );\r\n      case \"xls\":\r\n      case \"xlsx\":\r\n        return (\r\n          <img\r\n            src=\"/images/fileIcons/xlsFile.png\"\r\n            className={\r\n              props.type === \"application\" ? \"docPreview\" : \"imgPreview\"\r\n            }\r\n          />\r\n        );\r\n      default:\r\n        return (\r\n          <img\r\n            src=\"/images/fileIcons/wordFile.png\"\r\n            className={\r\n              props.type === \"application\" ? \"docPreview\" : \"imgPreview\"\r\n            }\r\n          />\r\n        );\r\n    }\r\n  };\r\n\r\n  const modalHide = () => setModalShow(false);\r\n\r\n  const cancelHandler = () => {\r\n    setModalShow(false);\r\n  };\r\n\r\n  const submitHandler = (fileselector: any) => {\r\n    fileselector = deleteFile;\r\n    const obj =\r\n      fileselector && fileselector._id\r\n        ? { serverID: fileselector._id }\r\n        : { file: fileselector };\r\n    const _index = _.findIndex(temp, obj);\r\n    //**Delete the source Field**//\r\n    const removeSrc = [...imageSource];\r\n    removeSrc.splice(_index, 1);\r\n    setImageSource(removeSrc);\r\n    //**End**/\r\n    imageDelete(temp[_index].serverID);\r\n    temp.splice(_index, 1);\r\n    props.getImgID(temp, props.index ? props.index : 0);\r\n    const newFiles = [...files];\r\n    newFiles.splice(newFiles.indexOf(fileselector), 1);\r\n    setFiles(newFiles);\r\n    setModalShow(false);\r\n  };\r\n\r\n  const thumbs: any = files.map((file: any, i) => {\r\n    return (\r\n      <div key={i}>\r\n        <Col xs={12}>\r\n          <div className=\"row\">\r\n            <Col\r\n              md={4}\r\n              lg={3}\r\n              className={\r\n                props.type === \"application text-center align-self-center\"\r\n                  ? \"docImagePreview text-center align-self-center\"\r\n                  : \"imgPreview text-center align-self-center\"\r\n              }\r\n            >\r\n              {getComponent(file)}\r\n            </Col>\r\n            <Col md={5} lg={7} className=\"align-self-center\">\r\n              <Form>\r\n                <Form.Group controlId=\"filename\">\r\n                  <Form.Label className=\"mt-2\">{t(\"FileName\")}</Form.Label>\r\n                  <Form.Control\r\n                    size=\"sm\"\r\n                    type=\"text\"\r\n                    disabled\r\n                    value={file.original_name ? file.original_name : file.name}\r\n                  />\r\n                </Form.Group>\r\n                <Form.Group controlId=\"description\">\r\n                  <Form.Label>\r\n                    {props.type === \"application\"\r\n                      ? t(\"ShortDescription/(Max255Characters)\")\r\n                      : t(\"Source/Description\")}\r\n                  </Form.Label>\r\n                  <Form.Control\r\n                    maxLength={props.type === \"application\" ? 255 : undefined}\r\n                    size=\"sm\"\r\n                    type=\"text\"\r\n                    placeholder={\r\n                      props.type === \"application\"\r\n                        ? t(\"`Enteryourdocumentdescription`\")\r\n                        : t(\"`Enteryourimagesource/description`\")\r\n                    }\r\n                    value={imageSource[i]}\r\n                    onChange={(e) => handleSource(e, i)}\r\n                  />\r\n                </Form.Group>\r\n              </Form>\r\n            </Col>\r\n            <Col\r\n              md={3}\r\n              lg={2}\r\n              className=\"align-self-center text-center\"\r\n              onClick={() => removeFile(file)}\r\n            >\r\n              <Button variant=\"dark\">{t(\"Remove\")}</Button>\r\n            </Col>\r\n          </div>\r\n        </Col>\r\n        <Modal show={modalShow} onHide={modalHide}>\r\n          <Modal.Header closeButton>\r\n            <Modal.Title>{t(\"DeleteFile\")}</Modal.Title>\r\n          </Modal.Header>\r\n          <Modal.Body>{t(\"Areyousurewanttodeletethisfile?\")}</Modal.Body>\r\n          <Modal.Footer>\r\n            <Button variant=\"secondary\" onClick={cancelHandler}>\r\n              {t(\"Cancel\")}\r\n            </Button>\r\n            <Button variant=\"primary\" onClick={() => submitHandler(file)}>\r\n              {t(\"yes\")}\r\n            </Button>\r\n          </Modal.Footer>\r\n        </Modal>\r\n      </div>\r\n    );\r\n  });\r\n\r\n  useEffect(() => {\r\n    files.forEach((file) => URL.revokeObjectURL(file.preview));\r\n    temp = [];\r\n  }, []);\r\n\r\n  useEffect(() => {\r\n    props.getImageSource(imageSource);\r\n  }, [imageSource]);\r\n\r\n  useEffect(() => {\r\n    setImageSource(props.srcText);\r\n  }, [props.srcText]);\r\n\r\n  useEffect(() => {\r\n    props && props.singleUpload === \"true\" && setMulti(false);\r\n    if (props && props.datas) {\r\n      const newObj = props.datas.map((item: any, _i: number) => {\r\n        temp.push({\r\n          serverID: item._id,\r\n          index: props.index ? props.index : 0,\r\n          type: item.name.split(\".\")[1],\r\n        });\r\n        const previewState = {\r\n          ...item,\r\n          preview: `${process.env.API_SERVER}/image/show/${item._id}`,\r\n        };\r\n        return previewState;\r\n      });\r\n      setFiles([...newObj]);\r\n    }\r\n  }, [props.datas]);\r\n\r\n  const filesUpload = async (filesinitial: any[], index: number) => {\r\n    if (filesinitial.length > index) {\r\n      try {\r\n        const form: any = new FormData();\r\n        form.append(\"file\", filesinitial[index]);\r\n        const res = await apiService.post(endpoint, form, {\r\n          \"Content-Type\": \"multipart/form-data\",\r\n        });\r\n        temp.push({\r\n          serverID: res._id,\r\n          file: filesinitial[index],\r\n          index: props.index ? props.index : 0,\r\n          type: filesinitial[index].name.split(\".\")[1],\r\n        });\r\n        filesUpload(filesinitial, index + 1);\r\n      } catch (error) {\r\n        filesUpload(filesinitial, index + 1);\r\n      }\r\n    } else {\r\n      props.getImgID(temp, props.index ? props.index : 0);\r\n    }\r\n  };\r\n\r\n  const onDrop = useCallback(async (ondrop_files: any[]) => {\r\n    await filesUpload(ondrop_files, 0);\r\n    const accFiles = ondrop_files.map((file: any) =>\r\n      Object.assign(file, {\r\n        preview: URL.createObjectURL(file),\r\n      })\r\n    );\r\n    multi\r\n      ? setFiles((prevState) => [...prevState, ...accFiles])\r\n      : setFiles([...accFiles]);\r\n  }, []);\r\n\r\n  function nameLengthValidator(file: File) {\r\n    if (endpoint === \"/image\") {\r\n      if (file.type.substring(0, 5) === \"image\") {\r\n        return null;\r\n      } else {\r\n        toast.error(t(\"toast.filetypenotsupport\"));\r\n        return { code: \"file-invalid-type\", message: \"File type not supported\" };\r\n      }\r\n    } else if (endpoint === \"/files\") {\r\n      if (!(file.type.substring(0, 5) !== \"image\")) {\r\n        toast.error(t(\"toast.filetypenotsupport\"));\r\n        return { code: \"file-invalid-type\", message: \"File type not supported\" };\r\n      }\r\n    }\r\n    return null;\r\n  }\r\n  const {\r\n    getRootProps,\r\n    getInputProps,\r\n    isDragActive,\r\n    isDragAccept,\r\n    isDragReject,\r\n    fileRejections,\r\n  } = useDropzone({\r\n    accept:\r\n      props && props.type\r\n        ? \"application/pdf, application/vnd.openxmlformats-officedocument.wordprocessingml.document, application/vnd.oasis.opendocument.text,application/vnd.openxmlformats-officedocument.spreadsheetml.sheet,text/csv\"\r\n        : \"image/*\",\r\n    multiple: multi,\r\n    minSize: 0,\r\n    maxSize: limit,\r\n    onDrop,\r\n    validator: nameLengthValidator,\r\n  });\r\n\r\n  const style = useMemo(\r\n    () => ({\r\n      ...baseStyle,\r\n      ...(isDragActive ? activeStyle : { outline: \"2px dashed #bbb\" }),\r\n      ...(isDragAccept\r\n        ? { outline: \"2px dashed #595959\" }\r\n        : { outline: \"2px dashed #bbb\" }),\r\n      ...(isDragReject ? { outline: \"2px dashed red\" } : { activeStyle }),\r\n    }),\r\n    [isDragActive, isDragReject]\r\n  );\r\n\r\n  let dropZoneMsg;\r\n  if (props && props.type === \"application\") {\r\n    dropZoneMsg = (\r\n      <small style={{ color: \"#595959\" }}>{t(\"DocumentWeSupport\")}</small>\r\n    );\r\n  } else {\r\n    dropZoneMsg = (\r\n      <small style={{ color: \"#595959\" }}>{t(\"ImageWeSupport\")}</small>\r\n    );\r\n  }\r\n\r\n  const isFileTooLarge =\r\n    fileRejections.length > 0 && fileRejections[0].file.size > limit;\r\n  return (\r\n    <>\r\n      <div\r\n        className=\" d-flex justify-content-center align-items-center mt-3\"\r\n        style={{ width: \"100%\", height: \"180px\" }}\r\n      >\r\n        <div {...getRootProps({ style })}>\r\n          <input {...getInputProps()} />\r\n          <FontAwesomeIcon icon={faCloudUploadAlt} size=\"4x\" color=\"#999\" />\r\n          <p style={{ color: \"#595959\", marginBottom: \"0px\" }}>\r\n            {t(\"Drag'n'dropsomefileshere,orclicktoselectfiles\")}\r\n          </p>\r\n\r\n          {!multi && (\r\n            <small style={{ color: \"#595959\" }}>\r\n              <b>Note:</b> One single image will be accepted\r\n            </small>\r\n          )}\r\n          {dropZoneMsg}\r\n          {props.type === \"application\"\r\n            ? isFileTooLarge && (\r\n                <small className=\"text-danger mt-2\">\r\n                  <FontAwesomeIcon\r\n                    icon={faExclamationCircle}\r\n                    size=\"1x\"\r\n                    color=\"red\"\r\n                  />{\" \"}\r\n                  {t(\"FileistoolargeItshouldbelessthan20MB\")}\r\n                </small>\r\n              )\r\n            : isFileTooLarge && (\r\n                <small className=\"text-danger mt-2\">\r\n                  <FontAwesomeIcon\r\n                    icon={faExclamationCircle}\r\n                    size=\"1x\"\r\n                    color=\"red\"\r\n                  />{\" \"}\r\n                  {t(\"FileistoolargeItshouldbelessthan20MB\")}\r\n                </small>\r\n              )}\r\n          {isDragReject && (\r\n            <small className=\"text-danger\" style={{ color: \"#595959\" }}>\r\n              <FontAwesomeIcon\r\n                icon={faExclamationCircle}\r\n                size=\"1x\"\r\n                color=\"red\"\r\n              />{\" \"}\r\n              {t(\"Filetypenotacceptedsorr\")}\r\n            </small>\r\n          )}\r\n        </div>\r\n      </div>\r\n      {files.length > 0 && <div style={thumbsContainer}>{thumbs}</div>}\r\n    </>\r\n  );\r\n};\r\n\r\nexport default ReactDropZone;\r\n", "import PropTypes from 'prop-types';\nconst alignDirection = PropTypes.oneOf(['start', 'end']);\nexport const alignPropType = PropTypes.oneOfType([alignDirection, PropTypes.shape({\n  sm: alignDirection\n}), PropTypes.shape({\n  md: alignDirection\n}), PropTypes.shape({\n  lg: alignDirection\n}), PropTypes.shape({\n  xl: alignDirection\n}), PropTypes.shape({\n  xxl: alignDirection\n}), PropTypes.object]);", "import * as React from 'react';\nimport PropTypes from 'prop-types';\nimport Dropdown from './Dropdown';\nimport DropdownToggle from './DropdownToggle';\nimport DropdownMenu from './DropdownMenu';\nimport { alignPropType } from './types';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nimport { jsxs as _jsxs } from \"react/jsx-runtime\";\nconst propTypes = {\n  /**\n   * An html id attribute for the Toggle button, necessary for assistive technologies, such as screen readers.\n   * @type {string}\n   */\n  id: PropTypes.string,\n  /** An `href` passed to the Toggle component */\n  href: PropTypes.string,\n  /** An `onClick` handler passed to the Toggle component */\n  onClick: PropTypes.func,\n  /** The content of the non-toggle Button.  */\n  title: PropTypes.node.isRequired,\n  /** Disables both Buttons  */\n  disabled: PropTypes.bool,\n  /**\n   * Aligns the dropdown menu.\n   *\n   * _see [DropdownMenu](#dropdown-menu-props) for more details_\n   *\n   * @type {\"start\"|\"end\"|{ sm: \"start\"|\"end\" }|{ md: \"start\"|\"end\" }|{ lg: \"start\"|\"end\" }|{ xl: \"start\"|\"end\"}|{ xxl: \"start\"|\"end\"} }\n   */\n  align: alignPropType,\n  /** An ARIA accessible role applied to the Menu component. When set to 'menu', The dropdown */\n  menuRole: PropTypes.string,\n  /** Whether to render the dropdown menu in the DOM before the first time it is shown */\n  renderMenuOnMount: PropTypes.bool,\n  /**\n   *  Which event when fired outside the component will cause it to be closed.\n   *\n   * _see [DropdownMenu](#dropdown-menu-props) for more details_\n   */\n  rootCloseEvent: PropTypes.string,\n  /**\n   * Menu color variant.\n   *\n   * Omitting this will use the default light color.\n   */\n  menuVariant: PropTypes.oneOf(['dark']),\n  /**\n   * Allow Dropdown to flip in case of an overlapping on the reference element. For more information refer to\n   * Popper.js's flip [docs](https://popper.js.org/docs/v2/modifiers/flip/).\n   *\n   */\n  flip: PropTypes.bool,\n  /** @ignore */\n  bsPrefix: PropTypes.string,\n  /** @ignore */\n  variant: PropTypes.string,\n  /** @ignore */\n  size: PropTypes.string\n};\n\n/**\n * A convenience component for simple or general use dropdowns. Renders a `Button` toggle and all `children`\n * are passed directly to the default `Dropdown.Menu`. This component accepts all of\n * [`Dropdown`'s props](#dropdown-props).\n *\n * _All unknown props are passed through to the `Dropdown` component._ Only\n * the Button `variant`, `size` and `bsPrefix` props are passed to the toggle,\n * along with menu-related props are passed to the `Dropdown.Menu`\n */\nconst DropdownButton = /*#__PURE__*/React.forwardRef(({\n  title,\n  children,\n  bsPrefix,\n  rootCloseEvent,\n  variant,\n  size,\n  menuRole,\n  renderMenuOnMount,\n  disabled,\n  href,\n  id,\n  menuVariant,\n  flip,\n  ...props\n}, ref) => /*#__PURE__*/_jsxs(Dropdown, {\n  ref: ref,\n  ...props,\n  children: [/*#__PURE__*/_jsx(DropdownToggle, {\n    id: id,\n    href: href,\n    size: size,\n    variant: variant,\n    disabled: disabled,\n    childBsPrefix: bsPrefix,\n    children: title\n  }), /*#__PURE__*/_jsx(DropdownMenu, {\n    role: menuRole,\n    renderOnMount: renderMenuOnMount,\n    rootCloseEvent: rootCloseEvent,\n    variant: menuVariant,\n    flip: flip,\n    children: children\n  })]\n}));\nDropdownButton.displayName = 'DropdownButton';\nDropdownButton.propTypes = propTypes;\nexport default DropdownButton;", "//Import Library\r\nimport React, { useState, useRef, useEffect } from \"react\";\r\nimport { <PERSON><PERSON>, Card, Form, Container, Row, Col, DropdownButton, Dropdown } from \"react-bootstrap\";\r\nimport Router from \"next/router\";\r\nimport { SelectGroup } from \"../../../components/common/FormValidation\";\r\nimport ValidationFormWrapper from \"../../../components/common/ValidationFormWrapper\";\r\nimport toast from 'react-hot-toast';\r\nimport Link from \"next/link\";\r\n\r\n//Import services/components\r\nimport apiService from \"../../../services/apiService\";\r\nimport ReactDropZone from \"../../../components/common/ReactDropZone\";\r\nimport { useTranslation } from 'next-i18next';\r\nimport { EditorComponent } from \"../../../shared/quill-editor/quill-editor.component\";\r\n\r\ninterface LandingPageFormProps {\r\n    [key: string]: any;\r\n}\r\n\r\nconst LandingPageForm = (props: LandingPageFormProps) => {\r\n    const { t, i18n } = useTranslation(\"common\");\r\n\r\n    const editform = props.routes && props.routes[0] === \"edit_landing\" && props.routes[1];\r\n    const _initialLanding = {\r\n        title: \"\",\r\n        description: \"\",\r\n        pageCategory: \"\",\r\n        isEnabled: true,\r\n        images: [],\r\n        images_src: [],\r\n        language: i18n.language,\r\n    };\r\n    const [initialVal, setinitialVal] = useState<any>(_initialLanding);\r\n    const [dropZoneCollection, setDropZoneCollection] = useState<any[]>([]);\r\n    const [srcCollection, setSrcCollection] = useState<any[]>([]);\r\n    const [lang, setLang] = useState<any[]>([]);\r\n    const [pageCategory, setPageCategory] = useState<any[]>([]);\r\n\r\n    const resetHandler = () => {\r\n        setinitialVal(_initialLanding);\r\n        setDropZoneCollection([]);\r\n        setSrcCollection([]);\r\n        // Reset validation state (Formik handles this automatically)\r\n        window.scrollTo(0, 0);\r\n    };\r\n\r\n    const onChangeLocale = (item: any): void => {\r\n        setinitialVal((prevState: any) => ({\r\n            ...prevState,\r\n            [\"language\"]: item.abbr,\r\n        }));\r\n    };\r\n\r\n    const langParams = {\r\n        query: {},\r\n        sort: { title: \"asc\" },\r\n        limit: \"~\",\r\n        select: \"-_id -created_at -updated_at\",\r\n    };\r\n\r\n    const getLang = async () => {\r\n        const response = await apiService.get(\"/language\", langParams);\r\n        if (response) {\r\n            setLang(response.data);\r\n        }\r\n    };\r\n\r\n    const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement>) => {\r\n        if (e.target) {\r\n            const { name, value } = e.target;\r\n            setinitialVal((prevState: any) => ({\r\n                ...prevState,\r\n                [name]: value,\r\n            }));\r\n        }\r\n    };\r\n\r\n    const handleDescription = (value: string) => {\r\n        setinitialVal((prevState: any) => ({\r\n            ...prevState,\r\n            description: value,\r\n        }));\r\n    };\r\n\r\n    const radiohandler = () => {\r\n        setinitialVal((prevState: any) => ({\r\n            ...prevState,\r\n            isEnabled: !prevState.isEnabled,\r\n        }));\r\n    };\r\n\r\n    const getID = (id: any[]) => {\r\n        const _id = id.map((item) => item.serverID);\r\n        setinitialVal((prevState: any) => ({ ...prevState, images: _id }));\r\n    };\r\n\r\n    const getSource = (imgSrcArr: any[]) => {\r\n        setinitialVal((prevState: any) => ({ ...prevState, images_src: imgSrcArr }));\r\n    };\r\n\r\n    const handleSubmit = async (event: any, values?: any) => {\r\n        event.preventDefault();\r\n        const obj = {\r\n            title: initialVal.title.trim(),\r\n            description: initialVal.description,\r\n            isEnabled: initialVal.isEnabled,\r\n            images: initialVal.images,\r\n            images_src: initialVal.images_src,\r\n            language: initialVal.language,\r\n        };\r\n\r\n        let response;\r\n        let toastMsg;\r\n        if (editform) {\r\n            toastMsg = \"adminsetting.landing.form.EditableContentisupdatedsuccessfully\";\r\n            response = await apiService.patch(`/landingPage/${props.routes[1]}`, obj);\r\n        } else {\r\n            toastMsg = \"adminsetting.landing.form.EditableContentisaddedsuccessfully\";\r\n            response = await apiService.post(\"/landingPage\", obj);\r\n        }\r\n        if (response && response._id) {\r\n            toast.success(t(toastMsg));\r\n            Router.push(\"/adminsettings/landing\");\r\n        } else {\r\n            toast.error(response);\r\n        }\r\n    };\r\n\r\n    const getPageCategory = async () => {\r\n        const response = await apiService.get(\"/pagecategory\");\r\n        const select = { _id: \"\", title: \"Select\" };\r\n        if (response && Array.isArray(response.data)) {\r\n            const data = response.data.filter((item: any) => item.title === \"AboutUs\");\r\n            data.unshift(select);\r\n            setPageCategory(data);\r\n        }\r\n    };\r\n\r\n    const getLandingPageData = async () => {\r\n        const response = await apiService.get(`/landingPage/${props.routes[1]}`);\r\n        if (response) {\r\n            const { pageCategory } = response;\r\n            response.pageCategory = pageCategory && pageCategory._id ? pageCategory._id : \"\";\r\n            setDropZoneCollection(response.images ? response.images : []);\r\n            setSrcCollection(response.images_src ? response.images_src : []);\r\n            setinitialVal((prevState: any) => ({ ...prevState, ...response }));\r\n        }\r\n    };\r\n    useEffect(() => {\r\n        getLang();\r\n        if (editform) {\r\n            getLandingPageData();\r\n        }\r\n        getPageCategory();\r\n    }, []);\r\n\r\n    const formRef = useRef(null);\r\n\r\n    return (\r\n        <Container className=\"formCard\" fluid>\r\n            <Card>\r\n                <ValidationFormWrapper onSubmit={handleSubmit} ref={formRef} initialValues={initialVal} enableReinitialize={true}>\r\n                    <Card.Body>\r\n                        <Row>\r\n                            <Col>\r\n                                <Card.Title>{t(\"adminsetting.landing.form.EditableContent\")}</Card.Title>\r\n                            </Col>\r\n                        </Row>\r\n                        <hr />\r\n                        <Row className=\"mb-3\">\r\n                            <Col md lg={12} sm={12}>\r\n                                <Form.Group>\r\n                                    <Form.Label>{t(\"adminsetting.landing.form.Title\")}</Form.Label>\r\n                                    <SelectGroup\r\n                                        name=\"title\"\r\n                                        id=\"title\"\r\n                                        value={initialVal.title}\r\n                                        validator={(value: string) => value.trim() !== \"\"}\r\n                                        required\r\n                                        errorMessage={{\r\n                                            validator: t(\"adminsetting.landing.form.PleaseAddtheTitle\"),\r\n                                        }}\r\n                                        onChange={handleChange}\r\n                                    >\r\n                                        <option key=\"\" value=\"\">Select</option>\r\n                                        <option key=\"Header\" value=\"Header\">Header</option>\r\n                                        <option key=\"About Us\" value=\"About Us\">About Us</option>\r\n                                    </SelectGroup>\r\n                                </Form.Group>\r\n                            </Col>\r\n                        </Row>\r\n                        <Row className=\"mb-3\">\r\n                            <Col md={5}>\r\n                                <Form.Group>\r\n                                    <Form.Label className=\"pe-3\">\r\n                                        {t(\"adminsetting.landing.form.chooseLanguage\")}\r\n                                    </Form.Label>\r\n                                    <DropdownButton\r\n                                        title={initialVal.language.toUpperCase()}\r\n                                        variant=\"outline-secondary\"\r\n                                        id=\"basic-dropdown\"\r\n                                        className=\"d-inline\"\r\n                                    >\r\n                                        {lang &&\r\n                                            lang.map((item, i) => (\r\n                                                <div key={i}>\r\n                                                    <Dropdown.Item\r\n                                                        active={item.abbr === initialVal.language}\r\n                                                        eventKey={item._id}\r\n                                                        onClick={() => onChangeLocale(item)}\r\n                                                    >\r\n                                                        {item.abbr.toUpperCase()}-{item.title.toUpperCase()}\r\n                                                    </Dropdown.Item>\r\n                                                </div>\r\n                                            ))}\r\n                                    </DropdownButton>\r\n                                </Form.Group>\r\n                            </Col>\r\n                        </Row>\r\n                        <Row className=\"mb-3\">\r\n                            <Col>\r\n                                <Form.Group>\r\n                                    <Form.Label>{t(\"adminsetting.landing.form.Description\")}</Form.Label>\r\n                                    <EditorComponent initContent={initialVal.description} onChange={(evt: string) => handleDescription(evt)} />\r\n                                </Form.Group>\r\n                            </Col>\r\n                        </Row>\r\n                        <Row className=\"mb-3\">\r\n                            <Col>\r\n                                <Form.Group>\r\n                                    <Form.Label>{t(\"adminsetting.landing.form.Images\")}</Form.Label>\r\n                                    <ReactDropZone\r\n                                        datas={dropZoneCollection}\r\n                                        srcText={srcCollection}\r\n                                        getImgID={(id: any[]) => getID(id)}\r\n                                        getImageSource={(imgSrcArr: any[]) => getSource(imgSrcArr)}\r\n                                    />\r\n                                </Form.Group>\r\n                            </Col>\r\n                        </Row>\r\n                        <Row className=\"mb-3\">\r\n                            <Col>\r\n                                <Form.Group>\r\n                                    <Form.Check\r\n                                        checked={initialVal.isEnabled}\r\n                                        name=\"isEnabled\"\r\n                                        onClick={radiohandler}\r\n                                        label={t(\"adminsetting.landing.form.Published\")}\r\n                                        type=\"checkbox\"\r\n                                    />\r\n                                </Form.Group>\r\n                            </Col>\r\n                        </Row>\r\n\r\n                        <Row className=\"my-4\">\r\n                            <Col>\r\n                                <Button className=\"me-2\" type=\"submit\" variant=\"primary\">\r\n                                    {t(\"adminsetting.landing.form.Submit\")}\r\n                                </Button>\r\n                                <Button className=\"me-2\" onClick={resetHandler} variant=\"info\">\r\n                                    {t(\"adminsetting.landing.form.Reset\")}\r\n                                </Button>\r\n                                <Link\r\n                                    href=\"/adminsettings/[...routes]\"\r\n                                    as={`/adminsettings/landing`}\r\n                                    >\r\n                                    <Button variant=\"secondary\">{t(\"adminsetting.landing.form.Cancel\")}</Button>\r\n                                </Link>\r\n                            </Col>\r\n                        </Row>\r\n                    </Card.Body>\r\n                </ValidationFormWrapper>\r\n            </Card>\r\n        </Container>\r\n    );\r\n};\r\n\r\nexport default LandingPageForm;\r\n"], "names": ["value", "onChange", "SimpleRichTextEditor", "placeholder", "height", "disabled", "editor<PERSON><PERSON>", "useRef", "isFocused", "setIsFocused", "useState", "useEffect", "current", "innerHTML", "handleInput", "execCommand", "command", "document", "focus", "div", "className", "style", "border", "padding", "borderBottom", "background", "button", "type", "onClick", "margin", "strong", "em", "u", "url", "prompt", "ref", "contentEditable", "onInput", "onFocus", "onBlur", "minHeight", "maxHeight", "overflow", "outline", "data-placeholder", "suppressContentEditableWarning", "EditorComponent", "initContent", "props", "content", "Radio", "RadioGroup", "name", "valueSelected", "errorMessage", "children", "errors", "touched", "useFormikContext", "<PERSON><PERSON><PERSON><PERSON>", "React", "childrenWithProps", "map", "child", "isObject", "String", "RadioItem", "id", "label", "values", "setFieldValue", "fieldName", "Form", "Check", "checked", "e", "target", "inline", "ValidationForm", "TextInput", "SelectGroup", "ValidationFormWrapper", "forwardRef", "onSubmit", "autoComplete", "onKeyPress", "initialValues", "rest", "validationSchema", "<PERSON><PERSON>", "shape", "<PERSON><PERSON>", "actions", "mockEvent", "preventDefault", "stopPropagation", "currentTarget", "nativeEvent", "Event", "bubbles", "cancelable", "defaultPrevented", "eventPhase", "isTrusted", "timeStamp", "Date", "now", "isDefaultPrevented", "isPropagationStopped", "persist", "formikProps", "handleSubmit", "displayName", "required", "validator", "as", "multiline", "rows", "pattern", "Field", "validate", "stringVal", "val", "trim", "RegExp", "test", "field", "meta", "Control", "isInvalid", "error", "undefined", "<PERSON><PERSON><PERSON>", "temp", "baseStyle", "flex", "display", "flexDirection", "alignItems", "justifyContent", "width", "borderWidth", "borderColor", "backgroundColor", "color", "transition", "thumbsContainer", "flexWrap", "marginTop", "img", "activeStyle", "dropZoneMsg", "t", "useTranslation", "ReactDropZone", "modalShow", "setModalShow", "deleteFile", "setDeleteFile", "limit", "process", "files", "setFiles", "multi", "set<PERSON><PERSON><PERSON>", "imageSource", "setImageSource", "endpoint", "imageDelete", "apiService", "remove", "removeFile", "file", "handleSource", "index", "items", "getComponent", "split", "pop", "src", "preview", "modalHide", "cancelHandler", "<PERSON><PERSON><PERSON><PERSON>", "obj", "fileselector", "_id", "serverID", "_index", "_", "removeSrc", "splice", "getImgID", "newFiles", "indexOf", "thumbs", "i", "Col", "xs", "md", "lg", "Group", "controlId", "Label", "size", "original_name", "max<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "variant", "Modal", "show", "onHide", "Header", "closeButton", "Title", "Body", "Footer", "for<PERSON>ach", "URL", "revokeObjectURL", "getImageSource", "srcText", "singleUpload", "datas", "item", "_i", "push", "filesUpload", "filesinitial", "length", "form", "FormData", "append", "res", "post", "onDrop", "useCallback", "ondrop_files", "accFiles", "Object", "assign", "createObjectURL", "prevState", "getRootProps", "getInputProps", "isDragActive", "isDragAccept", "isDragReject", "fileRejections", "useDropzone", "accept", "multiple", "minSize", "maxSize", "nameLengthValidator", "substring", "toast", "code", "message", "useMemo", "small", "isFileTooLarge", "input", "FontAwesomeIcon", "icon", "faCloudUploadAlt", "p", "marginBottom", "b", "faExclamationCircle", "alignDirection", "PropTypes", "alignPropType", "sm", "xl", "xxl", "propTypes", "href", "title", "isRequired", "align", "menuRole", "renderMenuOnMount", "rootCloseEvent", "menuVariant", "flip", "bsPrefix", "DropdownButton", "_jsxs", "Dropdown", "_jsx", "DropdownToggle", "childBsPrefix", "DropdownMenu", "role", "renderOnMount", "i18n", "LandingPageForm", "editform", "routes", "_initialLanding", "description", "pageCategory", "isEnabled", "images", "images_src", "language", "initialVal", "setinitialVal", "dropZoneCollection", "setDropZoneCollection", "srcCollection", "setSrcCollection", "lang", "setLang", "setPageCategory", "onChangeLocale", "langParams", "query", "sort", "select", "getLang", "response", "get", "data", "handleDescription", "getID", "getSource", "imgSrcArr", "event", "toastMsg", "patch", "success", "Router", "getPageCategory", "Array", "isArray", "filter", "unshift", "getLandingPageData", "formRef", "Container", "fluid", "Card", "enableReinitialize", "Row", "hr", "handleChange", "option", "toUpperCase", "<PERSON><PERSON>", "active", "abbr", "eventKey", "evt", "radiohandler", "re<PERSON><PERSON><PERSON><PERSON>", "window", "scrollTo", "Link"], "sourceRoot": "", "ignoreList": [7, 8]}