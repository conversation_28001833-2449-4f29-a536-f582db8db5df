(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[3756],{22352:(e,t,i)=>{"use strict";i.r(t),i.d(t,{canAddOperation:()=>s,canAddOperationForm:()=>o,canEditOperation:()=>p,canEditOperationForm:()=>c,canViewDiscussionUpdate:()=>d,default:()=>l});var a=i(37876);i(14232);var n=i(8178),r=i(59626);let s=(0,n.A)({authenticatedSelector:e=>!!e.permissions&&!!e.permissions.operation&&!!e.permissions.operation["create:any"],wrapperDisplayName:"CanAddOperation"}),o=(0,n.A)({authenticatedSelector:e=>!!e.permissions&&!!e.permissions.operation&&!!e.permissions.operation["create:any"],wrapperDisplayName:"CanAddOperationForm",FailureComponent:()=>(0,a.jsx)(r.default,{})}),p=(0,n.A)({authenticatedSelector:(e,t)=>{if(e.permissions&&e.permissions.operation){if(e.permissions.operation["update:any"])return!0;else if(e.permissions.operation["update:own"]&&t.operation&&t.operation.user&&t.operation.user._id===e.user._id)return!0}return!1},wrapperDisplayName:"CanEditOperation"}),c=(0,n.A)({authenticatedSelector:(e,t)=>{if(e.permissions&&e.permissions.operation){if(e.permissions.operation["update:any"])return!0;else if(e.permissions.operation["update:own"]&&t.operation&&t.operation.user&&t.operation.user._id===e.user._id)return!0}return!1},wrapperDisplayName:"CanEditOperationForm",FailureComponent:()=>(0,a.jsx)(r.default,{})}),d=(0,n.A)({authenticatedSelector:e=>!!e.permissions&&!!e.permissions.update&&!!e.permissions.update["read:any"],wrapperDisplayName:"CanViewDiscussionUpdate"}),l=s},31647:(e,t,i)=>{"use strict";i.d(t,{A:()=>d});var a=i(37876),n=i(31777),r=i(11041),s=i(14232),o=i(21772),p=i(53718);let c={operation:"Operation",institution:"Institution",event:"Event",project:"Project",vspace:"Vspace"},d=(0,n.Ng)(e=>e)(e=>{let{user:t,entityId:i,entityType:n}=e,[d,l]=(0,s.useState)(!1),[u,m]=(0,s.useState)(""),f=async()=>{if(!(null==t?void 0:t._id))return;let e=await p.A.get("/flag",{query:{entity_id:i,user:t._id,onModel:c[n]}});e&&e.data&&e.data.length>0&&(m(e.data[0]),l(!0))},h=async e=>{if(e.preventDefault(),!(null==t?void 0:t._id))return;let a=!d,r={entity_type:n,entity_id:i,user:t._id,onModel:c[n]};if(a){let e=await p.A.post("/flag",r);e&&e._id&&(m(e),l(a))}else{let e=await p.A.remove("/flag/".concat(u._id));e&&e.n&&l(a)}};return(0,s.useEffect)(()=>{f()},[]),(0,a.jsx)("div",{className:"subscribe-flag",children:(0,a.jsxs)("a",{href:"",onClick:h,children:[(0,a.jsx)("span",{className:"check",children:d?(0,a.jsx)(o.g,{className:"clickable checkIcon",icon:r.SGM,color:"#00CC00"}):(0,a.jsx)(o.g,{className:"clickable minusIcon",icon:r.OQW,color:"#fff"})}),(0,a.jsx)(o.g,{className:"bookmark",icon:r.G06,color:"#d4d4d4"})]})})})},40524:(e,t,i)=>{(window.__NEXT_P=window.__NEXT_P||[]).push(["/operation/components/OperationInfo",function(){return i(50903)}])},50903:(e,t,i)=>{"use strict";i.r(t),i.d(t,{default:()=>u});var a=i(37876);i(14232);var n=i(60282),r=i(21772),s=i(48230),o=i.n(s),p=i(11041),c=i(31647),d=i(22352),l=i(31753);let u=e=>{let{t}=(0,l.Bd)("common"),i=()=>(0,a.jsx)(a.Fragment,{children:e.editData?(0,a.jsx)(o(),{href:"/operation/[...routes]",as:"/operation/edit/".concat(e.routeData.routes[1]),children:(0,a.jsxs)(n.A,{variant:"secondary",size:"sm",children:[(0,a.jsx)(r.g,{icon:p.hpd}),"\xa0",t("Edit")]})}):""}),s=(0,d.canEditOperation)(()=>(0,a.jsx)(i,{}));return(0,a.jsx)(a.Fragment,{children:(0,a.jsxs)("section",{className:"d-flex justify-content-between",children:[(0,a.jsxs)("h4",{className:"operationTitle",children:[e.operation.title,"\xa0\xa0",e.routeData.routes&&e.routeData.routes[1]?(0,a.jsx)(s,{operation:e.operation}):null]}),(0,a.jsx)(c.A,{entityId:e.routeData.routes[1],entityType:"operation"})]})})}}},e=>{var t=t=>e(e.s=t);e.O(0,[7725,1772,636,6593,8792],()=>t(40524)),_N_E=e.O()}]);
//# sourceMappingURL=OperationInfo-b88a2f5f45fbea66.js.map