{"version": 3, "file": "static/chunks/pages/country/components/CountryDocumentAccordion-2a4c16bbc58186d9.js", "mappings": "sLAoCA,MA5BiC,IAC7B,GAAM,CAAEA,CAAC,CAAE,CAAGC,CAAAA,EAAAA,EAAAA,EAAAA,CAAcA,CAAC,QA2BlBC,EA1BX,MACI,UAACC,EAAAA,CAASA,CAAAA,CAACC,CAyBoBF,EAAC,cAzBJ,aACxB,WAACC,EAAAA,CAASA,CAACE,IAAI,EAACC,SAAS,cACrB,UAACH,EAAAA,CAASA,CAACI,MAAM,WACb,UAACC,MAAAA,CAAIC,UAAU,qBAAaT,EAAE,iBAElC,WAACG,EAAAA,CAASA,CAACO,IAAI,YACX,UAACC,EAAAA,CAAaA,CAAAA,CACVC,QAASC,EAAMD,OAAO,CACtBE,UAAWD,EAAME,UAAU,CAC3BC,KAAMH,EAAMI,QAAQ,CACpBC,gBAAiBL,EAAMM,MAAM,GAEjC,UAACC,KAAAA,CAAGX,UAAU,gBAAQT,EAAE,0BACxB,UAACW,EAAAA,CAAaA,CAAAA,CACVC,QAASC,EAAMD,OAAO,CACtBE,UAAWD,EAAMQ,kBAAkB,CACnCL,KAAMH,EAAMS,cAAc,CAC1BJ,gBAAiBL,EAAMM,MAAM,UAMrD,6GCGA,SAASI,EAASV,CAAoB,EACpC,GAAM,CAAEb,CAAC,CAAE,CAAGC,CAAAA,EAAAA,EAAAA,EAAAA,CAAcA,CAAC,UACvBuB,EAA6B,CACjCC,gBAAiBzB,EAAE,cACnB,EACI,SACJ0B,CAAO,MACPC,CAAI,WACJC,CAAS,uBACTC,CAAqB,WACrBC,CAAS,oBACTC,CAAkB,qBAClBC,CAAmB,kBACnBC,CAAgB,CAChBC,aAAW,oBACXC,CAAkB,gBAClBC,CAAc,SACdxB,CAAO,WACPyB,CAAS,sBACTC,CAAoB,mBACpBC,CAAiB,YACjBC,CAAU,QACVC,CAAM,kBACNC,CAAgB,cAChBC,CAAY,CAEZ,CADA,EACGC,EACJ,CAAG/B,EAGEgC,EAAiB,4BACrBrB,EACAsB,gBAAiB9C,EAAE,IAP0C,MAQ7D+C,UAAU,EACVrB,UACAC,KAAMA,GAAQ,EAAE,CAChBqB,OAAO,EACPC,2BAA4BpB,EAC5BqB,UAAWpB,EACXqB,gBAAiBvC,qBACjBmB,EACAqB,YAAY,EACZC,iBAAkBhB,EAClBiB,kBAAmBnB,GAA0C,GAC7DoB,eADwCpB,cACVD,GAA4B,CAAC,GAAI,GAAI,GAAI,CAA3BA,EAA+B,GAAG,CAC9EsB,oBAAqB5B,EACrB6B,oBAAqBzB,EACrB0B,aAAczB,iBACdG,uBACAE,oBACAC,EACAoB,kBAAmB,UAACC,EAAAA,CAAYA,CAAAA,CAAAA,GAChCC,SAAU,UAACC,IAAAA,CAAErD,UAAU,6CACvB+B,SACAC,eACAE,mBACAD,EACAjC,UAAW,WACb,EACA,MACE,UAACsD,EAAAA,EAASA,CAAAA,CAAE,GAAGlB,CAAc,EAEjC,CAEAtB,EAASyC,YAAY,CAAG,CACtBd,WAAW,EACXE,YAAY,EACZxB,UAAW,KACXS,WAAW,EACXC,qBAAsB,KACtBC,mBAAmB,EACnBC,YAAY,EACZE,kBAAkB,CACpB,EAEA,MAAenB,QAAQA,EAAC,6GC3CxB,MAtDoD,OAAC,MAAEP,CAAI,cAsD5CL,GAtD8CO,CAAe,SAsDhDP,EAtDkDG,CAAS,SAAEF,CAAO,CAAE,GAE1FqD,EAAa,MAAOC,EAAaC,KAKrCrD,EAJiB,CACfsD,OAGQC,QAHQH,EAAOI,QAAQ,CAC/BH,cAAeA,CACjB,EAEF,EAEM,GAAEnE,CAAC,CAAE,CAAGC,CAAAA,EAAAA,EAAAA,EAAAA,CAAcA,CAAC,UAGvByB,EAAU,CACd,CACE6C,KAAMvE,EAAE,YACRwE,MAAO,MACPF,SAAU,YACVG,KAAM,GAAYC,GAAKA,EAAEC,SAAS,EAAID,EAAEC,SAAS,EAEnD,CACEJ,KAAMvE,EAAE,YACRwE,MAAO,MACPF,SAAU,iBACVG,KAAM,GAAYC,GAAKA,EAAEE,aAAa,EAAI,UAACC,IAAAA,CAAEC,KAAM,GAA4CJ,MAAAA,CAAzCK,8BAAsB,CAAC,oBAAwB,OAANL,EAAEM,GAAG,EAAIC,OAAO,kBAAUP,EAAEE,aAAa,CAACM,KAAK,CAAC,KAAKC,KAAK,CAAC,EAAG,CAAC,GAAGC,IAAI,CAAC,OACtKC,UAAU,CACZ,EACA,CACEd,KAAMvE,EAAE,eACRsE,SAAU,cACVG,KAAM,GAAYC,GAAKA,EAAEY,WAAW,EAAIZ,EAAEY,WAAW,EAEvD,CACEf,KAAMvE,EAAE,gBACRwE,MAAO,MACPF,SAAU,iBACVG,KAAM,GAAYC,GAAKA,EAAEa,UAAU,EAAIC,IAAOd,EAAEa,UAAU,EAAEE,MAAM,CAAC,cACnEJ,MAD6CG,IACnC,CACZ,EACD,CAED,MACE,UAACjE,EAAAA,CAAQA,CAAAA,CACPG,QAASA,EACTC,KAAMX,EACNqB,UAAW,GACXI,OAAQwB,EACRvB,gBAAgB,IAChB9B,QAASA,GAIf,mBClEA,4CACA,+CACA,WACA,OAAe,EAAQ,KAAoE,CAC3F,EACA,SAFsB", "sources": ["webpack://_N_E/./pages/country/components/CountryDocumentAccordion.tsx", "webpack://_N_E/./components/common/RKITable.tsx", "webpack://_N_E/./components/common/DocumentTable.tsx", "webpack://_N_E/?1da3"], "sourcesContent": ["//Import Library\r\nimport React from \"react\";\r\nimport { Accordion } from \"react-bootstrap\";\r\n\r\n//Import services/components\r\nimport { useTranslation } from 'next-i18next';\r\nimport DocumentTable from \"../../../components/common/DocumentTable\";\r\n\r\nconst CountryDocumentAccordion = (props: any) => {\r\n    const { t } = useTranslation('common');\r\n    return (\r\n        <Accordion defaultActiveKey=\"0\">\r\n            <Accordion.Item eventKey=\"0\">\r\n                <Accordion.Header>\r\n                    <div className=\"cardTitle\">{t(\"Documents\")}</div>\r\n                </Accordion.Header>\r\n                <Accordion.Body>\r\n                    <DocumentTable\r\n                        loading={props.loading}\r\n                        sortProps={props.updateSort}\r\n                        docs={props.document}\r\n                        docsDescription={props.docSrc}\r\n                    />\r\n                    <h6 className=\"mt-3\">{t(\"DocumentsfromUpdates\")}</h6>\r\n                    <DocumentTable\r\n                        loading={props.loading}\r\n                        sortProps={props.updateDocumentSort}\r\n                        docs={props.updateDocument}\r\n                        docsDescription={props.docSrc}\r\n                    />\r\n                </Accordion.Body>\r\n            </Accordion.Item>\r\n        </Accordion>\r\n    )\r\n}\r\n\r\nexport default CountryDocumentAccordion;", "//Import Library\r\nimport React from 'react'\r\nimport DataTable  from 'react-data-table-component';\r\n\r\n//Import services/components\r\nimport { useTranslation } from 'next-i18next';\r\nimport CustomLoader from './CustomLoader';\r\n\r\ninterface RKITableProps {\r\n  columns: any[];\r\n  data: any[];\r\n  totalRows?: number;\r\n  resetPaginationToggle?: boolean;\r\n  subheader?: boolean;\r\n  subHeaderComponent?: React.ReactNode;\r\n  handlePerRowsChange?: (newPerPage: number, page: number) => void;\r\n  handlePageChange?: (page: number) => void;\r\n  rowsPerPage?: number[];\r\n  defaultRowsPerPage?: number;\r\n  selectableRows?: boolean;\r\n  loading?: boolean;\r\n  pagServer?: boolean;\r\n  onSelectedRowsChange?: (selectedRows: any) => void;\r\n  clearSelectedRows?: boolean;\r\n  sortServer?: boolean;\r\n  onSort?: (column: any, sortDirection: string) => void;\r\n  persistTableHead?: boolean;\r\n  sortFunction?: (rows: any[], field: any, direction: 'asc' | 'desc') => any[];\r\n  noHeader?: boolean;\r\n  dense?: boolean;\r\n  pagination?: boolean;\r\n  paginationServer?: boolean;\r\n  paginationTotalRows?: number;\r\n  subHeaderAlign?: string;\r\n  [key: string]: any;\r\n}\r\n\r\nfunction RKITable(props: RKITableProps) {\r\n  const { t } = useTranslation('common');\r\n  const paginationComponentOptions = {\r\n    rowsPerPageText: t('Rowsperpage'),\r\n    };\r\n  const {\r\n    columns,\r\n    data,\r\n    totalRows,\r\n    resetPaginationToggle,\r\n    subheader,\r\n    subHeaderComponent,\r\n    handlePerRowsChange,\r\n    handlePageChange,\r\n    rowsPerPage,\r\n    defaultRowsPerPage,\r\n    selectableRows,\r\n    loading,\r\n    pagServer,\r\n    onSelectedRowsChange,\r\n    clearSelectedRows,\r\n    sortServer,\r\n    onSort,\r\n    persistTableHead,\r\n    sortFunction,\r\n    // Filter out any unknown props that might cause DOM warnings\r\n    ...otherProps\r\n  } = props;\r\n\r\n  // Only pass known DataTable props to avoid DOM warnings\r\n  const dataTableProps = {\r\n    paginationComponentOptions,\r\n    noDataComponent: t(\"NoData\"),\r\n    noHeader: true,\r\n    columns,\r\n    data: data || [],\r\n    dense: true,\r\n    paginationResetDefaultPage: resetPaginationToggle,\r\n    subHeader: subheader,\r\n    progressPending: loading,\r\n    subHeaderComponent,\r\n    pagination: true,\r\n    paginationServer: pagServer,\r\n    paginationPerPage: defaultRowsPerPage ? defaultRowsPerPage : 10,\r\n    paginationRowsPerPageOptions: rowsPerPage ? rowsPerPage : [10, 15, 20, 25, 30],\r\n    paginationTotalRows: totalRows,\r\n    onChangeRowsPerPage: handlePerRowsChange,\r\n    onChangePage: handlePageChange,\r\n    selectableRows,\r\n    onSelectedRowsChange,\r\n    clearSelectedRows,\r\n    progressComponent: <CustomLoader/>,\r\n    sortIcon: <i className=\"sort-icon fas fa-exchange-alt\" />,\r\n    sortServer,\r\n    onSort,\r\n    sortFunction,\r\n    persistTableHead,\r\n    className: \"rki-table\"\r\n  };\r\n  return (\r\n    <DataTable {...dataTableProps} />\r\n  )\r\n};\r\n\r\nRKITable.defaultProps = {\r\n  subHeader: false,\r\n  pagination: true,\r\n  totalRows: null,\r\n  pagServer: true,\r\n  onSelectedRowsChange: null,\r\n  clearSelectedRows: false,\r\n  sortServer: false,\r\n  persistTableHead: false\r\n}\r\n\r\nexport default RKITable;\r\n\r\n", "//Import Library\r\nimport React from \"react\";\r\nimport moment from \"moment\";\r\n\r\n//Import services/components\r\nimport RKITable from '../../components/common/RKITable';\r\nimport { useTranslation } from 'next-i18next';\r\n\r\ninterface DocumentTableProps {\r\n  docs: any[];\r\n  docsDescription: string;\r\n  sortProps: (sortObj: { columnSelector: string; sortDirection: string }) => void;\r\n  loading: boolean;\r\n}\r\n\r\nconst DocumentTable: React.FC<DocumentTableProps> = ({ docs, docsDescription, sortProps, loading }) => {\r\n\r\n  const handleSort = async (column: any, sortDirection: string) => {\r\n    const objSlect = {\r\n      columnSelector: column.selector,\r\n      sortDirection: sortDirection\r\n    }\r\n    sortProps(objSlect);\r\n  };\r\n\r\n  const { t } = useTranslation('common');\r\n\r\n\r\n  const columns = [\r\n    {\r\n      name: t(\"FileType\"),\r\n      width: \"15%\",\r\n      selector: 'extension',\r\n      cell: (d: any) => d && d.extension && d.extension,\r\n    },\r\n    {\r\n      name: t(\"FileName\"),\r\n      width: \"25%\",\r\n      selector: \"document_title\",\r\n      cell: (d: any) => d && d.original_name && <a href={`${process.env.API_SERVER}/files/download/${d._id}`} target=\"_blank\">{d.original_name.split('.').slice(0, -1).join('.')}</a>,\r\n      sortable: true\r\n    },\r\n    {\r\n      name: t(\"Description\"),\r\n      selector: 'description',\r\n      cell: (d: any) => d && d.description && d.description,\r\n    },\r\n    {\r\n      name: t(\"UploadedDate\"),\r\n      width: \"25%\",\r\n      selector: 'doc_created_at',\r\n      cell: (d: any) => d && d.updated_at && moment(d.updated_at).format('MM/DD/YYYY'),\r\n      sortable: true\r\n    }\r\n  ];\r\n\r\n  return (\r\n    <RKITable\r\n      columns={columns}\r\n      data={docs}\r\n      pagServer={true}\r\n      onSort={handleSort}\r\n      persistTableHead\r\n      loading={loading}\r\n    />\r\n\r\n  )\r\n}\r\n\r\nexport default DocumentTable;\r\n", "\n    (window.__NEXT_P = window.__NEXT_P || []).push([\n      \"/country/components/CountryDocumentAccordion\",\n      function () {\n        return require(\"private-next-pages/country/components/CountryDocumentAccordion.tsx\");\n      }\n    ]);\n    if(module.hot) {\n      module.hot.dispose(function () {\n        window.__NEXT_P.push([\"/country/components/CountryDocumentAccordion\"])\n      });\n    }\n  "], "names": ["t", "useTranslation", "CountryDocumentAccordion", "Accordion", "defaultActiveKey", "<PERSON><PERSON>", "eventKey", "Header", "div", "className", "Body", "DocumentTable", "loading", "props", "sortProps", "updateSort", "docs", "document", "docsDescription", "docSrc", "h6", "updateDocumentSort", "updateDocument", "RKITable", "paginationComponentOptions", "rowsPerPageText", "columns", "data", "totalRows", "resetPaginationToggle", "subheader", "subHeaderComponent", "handlePerRowsChange", "handlePageChange", "rowsPerPage", "defaultRowsPerPage", "selectableRows", "pagServer", "onSelectedRowsChange", "clearSelectedRows", "sortServer", "onSort", "persistTableHead", "sortFunction", "otherProps", "dataTableProps", "noDataComponent", "<PERSON><PERSON><PERSON><PERSON>", "dense", "paginationResetDefaultPage", "subHeader", "progressPending", "pagination", "paginationServer", "paginationPerPage", "paginationRowsPerPageOptions", "paginationTotalRows", "onChangeRowsPerPage", "onChangePage", "progressComponent", "CustomLoader", "sortIcon", "i", "DataTable", "defaultProps", "handleSort", "column", "sortDirection", "columnSelector", "objSlect", "selector", "name", "width", "cell", "d", "extension", "original_name", "a", "href", "process", "_id", "target", "split", "slice", "join", "sortable", "description", "updated_at", "moment", "format"], "sourceRoot": "", "ignoreList": []}