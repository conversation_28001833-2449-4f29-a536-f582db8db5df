"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[2897],{73278:(e,n,t)=>{t.d(n,{Ay:()=>N});var i=t(57078),r=t(8972),o=t(38333),a=t(50705),u=t(14232),l=t(44501),c=t(12906),d=t(74253),s=t(40670),p=t(1584),f=t(66702),m=t(4073);function h(e,n){var t=Object.create(null);return e&&u.Children.map(e,function(e){return e}).forEach(function(e){t[e.key]=n&&(0,u.isValidElement)(e)?n(e):e}),t}function v(e,n,t){return null!=t[n]?t[n]:e.props[n]}var E=Object.values||function(e){return Object.keys(e).map(function(n){return e[n]})},A=function(e){function n(n,t){var i=e.call(this,n,t)||this,r=i.handleExited.bind((0,p.A)(i));return i.state={contextValue:{isMounting:!0},handleExited:r,firstRender:!0},i}(0,f.A)(n,e);var t=n.prototype;return t.componentDidMount=function(){this.mounted=!0,this.setState({contextValue:{isMounting:!1}})},t.componentWillUnmount=function(){this.mounted=!1},n.getDerivedStateFromProps=function(e,n){var t,i,r=n.children,o=n.handleExited;return{children:n.firstRender?h(e.children,function(n){return(0,u.cloneElement)(n,{onExited:o.bind(null,n),in:!0,appear:v(n,"appear",e),enter:v(n,"enter",e),exit:v(n,"exit",e)})}):(Object.keys(i=function(e,n){function t(t){return t in n?n[t]:e[t]}e=e||{},n=n||{};var i,r=Object.create(null),o=[];for(var a in e)a in n?o.length&&(r[a]=o,o=[]):o.push(a);var u={};for(var l in n){if(r[l])for(i=0;i<r[l].length;i++){var c=r[l][i];u[r[l][i]]=t(c)}u[l]=t(l)}for(i=0;i<o.length;i++)u[o[i]]=t(o[i]);return u}(r,t=h(e.children))).forEach(function(n){var a=i[n];if((0,u.isValidElement)(a)){var l=n in r,c=n in t,d=r[n],s=(0,u.isValidElement)(d)&&!d.props.in;c&&(!l||s)?i[n]=(0,u.cloneElement)(a,{onExited:o.bind(null,a),in:!0,exit:v(a,"exit",e),enter:v(a,"enter",e)}):c||!l||s?c&&l&&(0,u.isValidElement)(d)&&(i[n]=(0,u.cloneElement)(a,{onExited:o.bind(null,a),in:d.props.in,exit:v(a,"exit",e),enter:v(a,"enter",e)})):i[n]=(0,u.cloneElement)(a,{in:!1})}}),i),firstRender:!1}},t.handleExited=function(e,n){var t=h(this.props.children);e.key in t||(e.props.onExited&&e.props.onExited(n),this.mounted&&this.setState(function(n){var t=(0,l.A)({},n.children);return delete t[e.key],{children:t}}))},t.render=function(){var e=this.props,n=e.component,t=e.childFactory,i=(0,s.A)(e,["component","childFactory"]),r=this.state.contextValue,o=E(this.state.children).map(t);return(delete i.appear,delete i.enter,delete i.exit,null===n)?u.createElement(m.A.Provider,{value:r},o):u.createElement(m.A.Provider,{value:r},u.createElement(n,i,o))},n}(u.Component);A.propTypes={},A.defaultProps={component:"div",childFactory:function(e){return e}},t(98477),t(97);var g=["in","onExited","appear","enter","exit"],x=["component","duration","in","onExited"],V=function(e){var n=e.component,t=e.duration,o=void 0===t?1:t,a=e.in;e.onExited;var c=(0,r.A)(e,x),s=(0,u.useRef)(null),p={entering:{opacity:0},entered:{opacity:1,transition:"opacity ".concat(o,"ms")},exiting:{opacity:0},exited:{opacity:0}};return u.createElement(d.Ay,{mountOnEnter:!0,unmountOnExit:!0,in:a,timeout:o,nodeRef:s},function(e){var t={style:(0,i.A)({},p[e]),ref:s};return u.createElement(n,(0,l.A)({innerProps:t},c))})},w=function(e){var n=e.children,t=e.in,r=e.onExited,o=(0,u.useRef)(null),a=(0,u.useState)("auto"),l=(0,c.A)(a,2),s=l[0],p=l[1];(0,u.useEffect)(function(){var e=o.current;if(e){var n=window.requestAnimationFrame(function(){return p(e.getBoundingClientRect().width)});return function(){return window.cancelAnimationFrame(n)}}},[]);var f=function(e){switch(e){default:return{width:s};case"exiting":return{width:0,transition:"width ".concat(260,"ms ease-out")};case"exited":return{width:0}}};return u.createElement(d.Ay,{enter:!1,mountOnEnter:!0,unmountOnExit:!0,in:t,onExited:function(){var e=o.current;e&&(null==r||r(e))},timeout:260,nodeRef:o},function(e){return u.createElement("div",{ref:o,style:(0,i.A)({overflow:"hidden",whiteSpace:"nowrap"},f(e))},n)})},y=["in","onExited"],O=["component"],C=["children"],b=function(e){var n=e.component,t=S((0,r.A)(e,O));return u.createElement(A,(0,l.A)({component:n},t))},S=function(e){var n=e.children,t=(0,r.A)(e,C),o=t.isMulti,a=t.hasValue,l=t.innerProps,d=t.selectProps,s=d.components,p=d.controlShouldRenderValue,f=(0,u.useState)(o&&p&&a),m=(0,c.A)(f,2),h=m[0],v=m[1],E=(0,u.useState)(!1),A=(0,c.A)(E,2),g=A[0],x=A[1];(0,u.useEffect)(function(){a&&!h&&v(!0)},[a,h]),(0,u.useEffect)(function(){g&&!a&&h&&v(!1),x(!1)},[g,a,h]);var V=function(){return x(!0)},w=(0,i.A)((0,i.A)({},l),{},{style:(0,i.A)((0,i.A)({},null==l?void 0:l.style),{},{display:o&&a||h?"flex":"grid"})});return(0,i.A)((0,i.A)({},t),{},{innerProps:w,children:u.Children.toArray(n).map(function(e){if(o&&u.isValidElement(e)){if(e.type===s.MultiValue)return u.cloneElement(e,{onExited:V});if(e.type===s.Placeholder&&h)return null}return e})})},P=["Input","MultiValue","Placeholder","SingleValue","ValueContainer"],M=function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},n=(0,a.F)({components:e}),t=n.Input,o=n.MultiValue,c=n.Placeholder,d=n.SingleValue,s=n.ValueContainer,p=(0,r.A)(n,P);return(0,i.A)({Input:function(e){e.in,e.onExited,e.appear,e.enter,e.exit;var n=(0,r.A)(e,g);return u.createElement(t,n)},MultiValue:function(e){var n=e.in,t=e.onExited,i=(0,r.A)(e,y);return u.createElement(w,{in:n,onExited:t},u.createElement(o,(0,l.A)({cropWithEllipsis:n},i)))},Placeholder:function(e){return u.createElement(V,(0,l.A)({component:c,duration:e.isMulti?260:1},e))},SingleValue:function(e){return u.createElement(V,(0,l.A)({component:d},e))},ValueContainer:function(e){return e.isMulti?u.createElement(b,(0,l.A)({component:s},e)):u.createElement(A,(0,l.A)({component:s},e))}},p)},L=M();L.Input,L.MultiValue,L.Placeholder,L.SingleValue,L.ValueContainer;var N=(0,o.A)(M)},79314:(e,n,t)=>{t.d(n,{A:()=>m});var i=t(44501),r=t(14232),o=t(82618),a=t(68898),u=t(57078),l=t(2389),c=t(8972),d=t(50705),s=["allowCreateWhileLoading","createOptionPosition","formatCreateLabel","isValidNewOption","getNewOptionData","onCreateOption","options","onChange"],p=function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"",n=arguments.length>1?arguments[1]:void 0,t=arguments.length>2?arguments[2]:void 0,i=String(e).toLowerCase(),r=String(t.getOptionValue(n)).toLowerCase(),o=String(t.getOptionLabel(n)).toLowerCase();return r===i||o===i},f={formatCreateLabel:function(e){return'Create "'.concat(e,'"')},isValidNewOption:function(e,n,t,i){return!(!e||n.some(function(n){return p(e,n,i)})||t.some(function(n){return p(e,n,i)}))},getNewOptionData:function(e,n){return{label:n,value:e,__isNew__:!0}}};t(98477),t(97);var m=(0,r.forwardRef)(function(e,n){var t,p,m,h,v,E,A,g,x,V,w,y,O,C,b,S,P,M,L,N,k,R,_,D,F,j,I,W,H=(m=void 0!==(p=(t=(0,a.u)(e)).allowCreateWhileLoading)&&p,v=void 0===(h=t.createOptionPosition)?"last":h,A=void 0===(E=t.formatCreateLabel)?f.formatCreateLabel:E,x=void 0===(g=t.isValidNewOption)?f.isValidNewOption:g,w=void 0===(V=t.getNewOptionData)?f.getNewOptionData:V,y=t.onCreateOption,C=void 0===(O=t.options)?[]:O,b=t.onChange,M=void 0===(P=(S=(0,c.A)(t,s)).getOptionValue)?o.g:P,N=void 0===(L=S.getOptionLabel)?o.b:L,k=S.inputValue,R=S.isLoading,_=S.isMulti,D=S.value,F=S.name,j=(0,r.useMemo)(function(){return x(k,(0,d.H)(D),C,{getOptionValue:M,getOptionLabel:N})?w(k,A(k)):void 0},[A,w,N,M,k,x,C,D]),I=(0,r.useMemo)(function(){return(m||!R)&&j?"first"===v?[j].concat((0,l.A)(C)):[].concat((0,l.A)(C),[j]):C},[m,v,R,j,C]),W=(0,r.useCallback)(function(e,n){if("select-option"!==n.action)return b(e,n);var t=Array.isArray(e)?e:[e];if(t[t.length-1]===j){if(y)y(k);else{var i=w(k,k);b((0,d.D)(_,[].concat((0,l.A)((0,d.H)(D)),[i]),i),{action:"create-option",name:F,option:i})}return}b(e,n)},[w,k,_,F,j,y,b,D]),(0,u.A)((0,u.A)({},S),{},{options:I,onChange:W}));return r.createElement(o.S,(0,i.A)({ref:n},H))})}}]);
//# sourceMappingURL=2897-4412e0964d085631.js.map