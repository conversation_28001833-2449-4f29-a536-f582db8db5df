"use strict";(()=>{var e={};e.id=7480,e.ids=[636,3220,7480],e.modules={123:e=>{e.exports=require("dom-helpers/removeEventListener")},1332:e=>{e.exports=require("react-custom-scrollbars-2")},1428:e=>{e.exports=import("axios")},1680:e=>{e.exports=require("@restart/ui/utils")},1919:e=>{e.exports=require("react-transition-group/Transition")},2262:(e,r,t)=>{t.a(e,async(e,a)=>{try{t.r(r),t.d(r,{default:()=>v});var s=t(8732),o=t(19918),n=t.n(o),i=t(74716),u=t.n(i),l=t(27825),p=t.n(l),d=t(82015),c=t.n(d),x=t(44233),h=t(56084),q=t(63487),g=t(98060),y=t(88751),m=e([q,g]);[q,g]=m.then?(await m)():m;let _=e=>{let{i18n:r}=(0,y.useTranslation)("common"),t="fr"===r.language?"en":r.language,{hazards:a}=e;return(0,s.jsx)("ul",{children:a.map((e,r)=>e&&e._id&&e.title&&e.title[t]?(0,s.jsx)("li",{children:(0,s.jsx)(n(),{href:"/hazard/[...routes]",as:`/hazard/show/${e._id}`,children:e.title[t].toString()})},r):"")})},v=function(e){let r=(0,x.useRouter)(),{t}=(0,y.useTranslation)("common"),{setEvents:a,selectedRegions:o}=e,[i,l]=c().useState(""),[m,v]=c().useState(""),[b,f]=c().useState(!1),[P,S]=(0,d.useState)([]),[w,A]=(0,d.useState)(!1),[E,j]=(0,d.useState)(0),[M,z]=(0,d.useState)(10),[C,T]=(0,d.useState)(1),[k,R]=(0,d.useState)(null),H={sort:{created_at:"desc"},lean:!0,populate:[{path:"country",select:"coordinates title"},{path:"hazard_type",select:"title"},{path:"hazard",select:"title"}],limit:M,page:1,query:{},select:"-description -operation -world_region -country_regions -syndrome -status -laboratory_confirmed -officially_validated -rki_monitored -risk_assessment -date -more_info -images -user -created_at -updated_at"},[D,I]=(0,d.useState)(H),N=[{name:t("Events.table.EventId"),selector:"title",sortable:!0,width:"20%",cell:e=>(0,s.jsx)(n(),{href:"/event/[...routes]",as:`/event/show/${e._id}`,children:e.title})},{name:t("Events.table.Country"),selector:"country",sortable:!0,cell:e=>e.country&&e.country.title?(0,s.jsx)(n(),{href:"/country/[...routes]",as:`/country/show/${e.country._id}`,children:e.country.title}):""},{name:t("Events.table.HazardType"),selector:"hazard_type",sortable:!0,cell:e=>e.hazard_type&&e.hazard_type.title?e.hazard_type.title:""},{name:t("Events.table.Hazard"),selector:"hazard",cell:e=>(0,s.jsx)(_,{hazards:e.hazard})},{name:t("Events.table.InfoReceivedon"),selector:"created_at",sortable:!0,cell:e=>u()(e.start_date).format("M/D/Y")},{name:t("Events.table.Lastupdated"),selector:"updated_at",sortable:!0,cell:e=>u()(e.updated_at).format("M/D/Y")}],G=async e=>{A(!0),r.query&&r.query.country&&(e.query.country=r.query.country),null===o?delete e.query.world_region:0===o.length?e.query.world_region=["__NO_MATCH__"]:e.query.world_region=o;let t=await q.A.get("/event",e);t&&Array.isArray(t.data)&&(S(t.data),a(t.data),j(t.totalCount)),A(!1)},O=async(e,t)=>{H.limit=e,H.page=t,A(!0),r.query&&r.query.country&&(H.query.country=r.query.country),null===o?delete H.query.world_region:0===o.length?H.query.world_region=["__NO_MATCH__"]:H.query.world_region=o,m&&(H.query={...H.query,hazard_type:m}),k&&(H.sort=k.sort);let s=await q.A.get("/event",H);s&&Array.isArray(s.data)&&(S(s.data),a(s.data),z(e),A(!1)),T(t)},U=async(e,r)=>{A(!0),H.sort={[e.selector]:r},m&&(H.query={...H.query,hazard_type:m}),""!==i&&(H.query={...H.query,title:i}),await G(H),R(H),A(!1)},W=(e,r)=>{e?(D.query.title=e,D.page=r):delete D.query.title,I({...D})},L=(0,d.useRef)(p().debounce((e,r)=>W(e,r),Number("500")||300)).current,$=c().useMemo(()=>{let e=e=>{v(e),e?D.query.hazard_type=e:delete D.query.hazard_type,I({...D})};return(0,s.jsx)(g.default,{onFilter:e=>{l(e.target.value),L(e.target.value,C)},onFilterHazardChange:r=>e(r.target.value),onClear:()=>{i&&(f(!b),l(""))},filterText:i,filterHazard:m})},[i,b,m,o]);return(0,s.jsx)(h.A,{columns:N,data:P,totalRows:E,loading:w,subheader:!0,persistTableHead:!0,onSort:U,sortServer:!0,pagServer:!0,subHeaderComponent:$,handlePerRowsChange:O,handlePageChange:e=>{H.limit=M,H.page=e,m&&(H.query={...H.query,hazard_type:m}),k&&(H.sort=k.sort),G(H),T(e)}})};a()}catch(e){a(e)}})},3892:e=>{e.exports=require("classnames")},4048:e=>{e.exports=require("react-truncate")},6009:e=>{e.exports=require("dom-helpers/ownerDocument")},6952:e=>{e.exports=require("@restart/ui/Modal")},7374:e=>{e.exports=require("@restart/ui/Dropdown")},8732:e=>{e.exports=require("react/jsx-runtime")},9653:e=>{e.exports=require("@restart/ui/Overlay")},11242:e=>{e.exports=require("redux-persist/integration/react")},11688:e=>{e.exports=require("@restart/hooks/useWillUnmount")},12388:e=>{e.exports=require("dom-helpers/addEventListener")},13364:e=>{e.exports=require("redux-saga/effects")},14062:e=>{e.exports=import("react-redux")},14078:e=>{e.exports=import("swr")},14332:e=>{e.exports=require("uncontrollable")},16116:e=>{e.exports=require("invariant")},18622:e=>{e.exports=require("yup")},19121:e=>{e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},22326:e=>{e.exports=require("react-dom")},22541:e=>{e.exports=require("redux-persist/lib/storage")},25303:e=>{e.exports=require("@restart/ui/NavItem")},26324:e=>{e.exports=require("warning")},27825:e=>{e.exports=require("lodash")},27910:e=>{e.exports=require("stream")},28217:e=>{e.exports=require("@restart/ui/DropdownItem")},29021:e=>{e.exports=require("fs")},29294:e=>{e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},29628:e=>{e.exports=require("dom-helpers/addClass")},29825:e=>{e.exports=require("prop-types")},29841:e=>{e.exports=require("dom-helpers/hasClass")},30362:e=>{e.exports=require("@restart/ui/DropdownToggle")},33873:e=>{e.exports=require("path")},36653:e=>{e.exports=require("nprogress")},36955:e=>{e.exports=require("@restart/hooks/useBreakpoint")},37766:e=>{e.exports=require("@restart/hooks/useMergedRefs")},38609:e=>{e.exports=require("react-data-table-component")},39756:e=>{e.exports=import("redux")},40051:e=>{e.exports=require("dom-helpers/removeClass")},40361:e=>{e.exports=require("next/dist/compiled/next-server/pages.runtime.prod.js")},42893:e=>{e.exports=import("react-hot-toast")},43294:e=>{e.exports=require("formik")},50009:e=>{e.exports=require("@restart/ui/DropdownMenu")},56084:(e,r,t)=>{t.d(r,{A:()=>l});var a=t(8732);t(82015);var s=t(38609),o=t.n(s),n=t(88751),i=t(30370);function u(e){let{t:r}=(0,n.useTranslation)("common"),t={rowsPerPageText:r("Rowsperpage")},{columns:s,data:u,totalRows:l,resetPaginationToggle:p,subheader:d,subHeaderComponent:c,handlePerRowsChange:x,handlePageChange:h,rowsPerPage:q,defaultRowsPerPage:g,selectableRows:y,loading:m,pagServer:_,onSelectedRowsChange:v,clearSelectedRows:b,sortServer:f,onSort:P,persistTableHead:S,sortFunction:w,...A}=e,E={paginationComponentOptions:t,noDataComponent:r("NoData"),noHeader:!0,columns:s,data:u||[],dense:!0,paginationResetDefaultPage:p,subHeader:d,progressPending:m,subHeaderComponent:c,pagination:!0,paginationServer:_,paginationPerPage:g||10,paginationRowsPerPageOptions:q||[10,15,20,25,30],paginationTotalRows:l,onChangeRowsPerPage:x,onChangePage:h,selectableRows:y,onSelectedRowsChange:v,clearSelectedRows:b,progressComponent:(0,a.jsx)(i.A,{}),sortIcon:(0,a.jsx)("i",{className:"sort-icon fas fa-exchange-alt"}),sortServer:f,onSort:P,sortFunction:w,persistTableHead:S,className:"rki-table"};return(0,a.jsx)(o(),{...E})}u.defaultProps={subHeader:!1,pagination:!0,totalRows:null,pagServer:!0,onSelectedRowsChange:null,clearSelectedRows:!1,sortServer:!1,persistTableHead:!1};let l=u},57664:e=>{e.exports=require("dom-helpers/scrollbarSize")},58928:e=>{e.exports=require("dom-helpers/canUseDOM")},59717:e=>{e.exports=require("@restart/ui/DropdownContext")},60560:e=>{e.exports=require("dom-helpers/contains")},63033:e=>{e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},65447:e=>{e.exports=require("@restart/ui/Button")},67364:e=>{e.exports=require("@restart/hooks/useCallbackRef")},68455:e=>{e.exports=import("redux-saga")},69722:e=>{e.exports=require("es6-promise")},74075:e=>{e.exports=require("zlib")},74716:e=>{e.exports=require("moment")},74987:e=>{e.exports=require("@restart/ui/ModalManager")},78097:e=>{e.exports=require("next-redux-saga")},78634:e=>{e.exports=require("@restart/ui/Anchor")},80237:(e,r)=>{Object.defineProperty(r,"A",{enumerable:!0,get:function(){return t}});var t=function(e){return e.PAGES="PAGES",e.PAGES_API="PAGES_API",e.APP_PAGE="APP_PAGE",e.APP_ROUTE="APP_ROUTE",e.IMAGE="IMAGE",e}({})},81366:e=>{e.exports=require("redux-auth-wrapper/connectedAuthWrapper")},81413:(e,r)=>{Object.defineProperty(r,"M",{enumerable:!0,get:function(){return function e(r,t){return t in r?r[t]:"then"in r&&"function"==typeof r.then?r.then(r=>e(r,t)):"function"==typeof r&&"default"===t?r:void 0}}})},81521:e=>{e.exports=require("dom-helpers/querySelectorAll")},81895:e=>{e.exports=require("@restart/hooks/useEventCallback")},82015:e=>{e.exports=require("react")},86181:(e,r,t)=>{t.a(e,async(e,a)=>{try{t.r(r),t.d(r,{config:()=>g,default:()=>c,getServerSideProps:()=>q,getStaticPaths:()=>h,getStaticProps:()=>x,reportWebVitals:()=>y,routeModule:()=>P,unstable_getServerProps:()=>b,unstable_getServerSideProps:()=>f,unstable_getStaticParams:()=>v,unstable_getStaticPaths:()=>_,unstable_getStaticProps:()=>m});var s=t(63885),o=t(80237),n=t(81413),i=t(9616),u=t.n(i),l=t(72386),p=t(2262),d=e([l,p]);[l,p]=d.then?(await d)():d;let c=(0,n.M)(p,"default"),x=(0,n.M)(p,"getStaticProps"),h=(0,n.M)(p,"getStaticPaths"),q=(0,n.M)(p,"getServerSideProps"),g=(0,n.M)(p,"config"),y=(0,n.M)(p,"reportWebVitals"),m=(0,n.M)(p,"unstable_getStaticProps"),_=(0,n.M)(p,"unstable_getStaticPaths"),v=(0,n.M)(p,"unstable_getStaticParams"),b=(0,n.M)(p,"unstable_getServerProps"),f=(0,n.M)(p,"unstable_getServerSideProps"),P=new s.PagesRouteModule({definition:{kind:o.A.PAGES,page:"/event/EventsTable",pathname:"/event/EventsTable",bundlePath:"",filename:""},components:{App:l.default,Document:u()},userland:p});a()}catch(e){a(e)}})},86842:e=>{e.exports=require("@restart/ui/SelectableContext")},87571:e=>{e.exports=require("dom-helpers/transitionEnd")},88751:e=>{e.exports=require("next-i18next")},93787:e=>{e.exports=require("redux-persist")},94947:e=>{e.exports=require("@restart/hooks/useTimeout")},96196:e=>{e.exports=require("next-redux-wrapper")},98060:(e,r,t)=>{t.a(e,async(e,a)=>{try{t.r(r),t.d(r,{default:()=>x});var s=t(8732),o=t(82015),n=t(7082),i=t(83551),u=t(49481),l=t(84517),p=t(63487),d=t(88751),c=e([p]);p=(c.then?(await c)():c)[0];let x=({filterText:e,onFilter:r,onClear:t,onFilterHazardChange:a,filterHazard:c})=>{let[x,h]=(0,o.useState)([]),{t:q}=(0,d.useTranslation)("common"),g=async e=>{let r=await p.A.get("/hazardtype",e);r&&Array.isArray(r.data)&&h(r.data)};return(0,o.useEffect)(()=>{g({query:{},sort:{title:"asc"}})},[]),(0,s.jsx)(n.A,{fluid:!0,className:"p-0",children:(0,s.jsxs)(i.A,{children:[(0,s.jsx)(u.A,{xs:6,className:"p-0",children:(0,s.jsx)(l.A,{type:"text",className:"searchInput",placeholder:q("Events.table.Search"),"aria-label":"Search",value:e,onChange:r})}),(0,s.jsx)(u.A,{xs:6,children:(0,s.jsxs)(l.A,{as:"select","aria-label":"HazardType","aria-placeholder":"Hazard Type",onChange:a,value:c,children:[(0,s.jsx)("option",{value:"",children:q("Events.forms.SelectHazardType")}),x.map((e,r)=>(0,s.jsx)("option",{value:e._id,children:e.title},r))]})})]})})};a()}catch(e){a(e)}})},98320:e=>{e.exports=require("@restart/hooks/useIsomorphicEffect")},99460:e=>{e.exports=require("dom-helpers/css")}};var r=require("../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),a=r.X(0,[6089,9216,9616,2386],()=>t(86181));module.exports=a})();