(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[7651],{46864:(e,r,s)=>{"use strict";s.r(r),s.d(r,{canAddProject:()=>o,canAddProjectForm:()=>a,canEditProject:()=>n,canEditProjectForm:()=>c,canViewDiscussionUpdate:()=>u,default:()=>d});var t=s(37876);s(14232);var i=s(8178),p=s(59626);let o=(0,i.A)({authenticatedSelector:e=>!!e.permissions&&!!e.permissions.project&&!!e.permissions.project["create:any"],wrapperDisplayName:"CanAddProject"}),a=(0,i.A)({authenticatedSelector:e=>!!e.permissions&&!!e.permissions.project&&!!e.permissions.project["create:any"],wrapperDisplayName:"CanAddProjectForm",FailureComponent:()=>(0,t.jsx)(p.default,{})}),n=(0,i.A)({authenticatedSelector:(e,r)=>{if(e.permissions&&e.permissions.project){if(e.permissions.project["update:any"])return!0;else if(e.permissions.project["update:own"]&&r.project&&r.project.user&&r.project.user._id===e.user._id)return!0}return!1},wrapperDisplayName:"CanEditProject"}),c=(0,i.A)({authenticatedSelector:(e,r)=>{if(e.permissions&&e.permissions.project){if(e.permissions.project["update:any"])return!0;else if(e.permissions.project["update:own"]&&r.project&&r.project.user&&r.project.user._id===e.user._id)return!0}return!1},wrapperDisplayName:"CanEditProjectForm",FailureComponent:()=>(0,t.jsx)(p.default,{})}),u=(0,i.A)({authenticatedSelector:e=>!!e.permissions&&!!e.permissions.update&&!!e.permissions.update["read:any"],wrapperDisplayName:"CanViewDiscussionUpdate"}),d=o},84754:(e,r,s)=>{(window.__NEXT_P=window.__NEXT_P||[]).push(["/project/permission",function(){return s(46864)}])}},e=>{var r=r=>e(e.s=r);e.O(0,[636,6593,8792],()=>r(84754)),_N_E=e.O()}]);
//# sourceMappingURL=permission-f81ce61f30decd18.js.map