(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[9069],{16280:(e,l,r)=>{(window.__NEXT_P=window.__NEXT_P||[]).push(["/updates/ContactForm",function(){return r(78612)}])},78612:(e,l,r)=>{"use strict";r.r(l),r.d(l,{default:()=>t});var a=r(37876);r(14232);var n=r(49589),d=r(56970),s=r(37784),o=r(29504),i=r(31753);let t=e=>{let{t:l}=(0,i.Bd)("common"),{onHandleChange:r,telephoneNo:t,mobileNo:u}=e;return(0,a.jsx)(n.A,{className:"formCard",fluid:!0,children:(0,a.jsx)(d.A,{children:(0,a.jsxs)(s.A,{children:[(0,a.jsxs)(o.A.Group,{children:[(0,a.jsx)(o.A.Label,{className:"required-field",children:l("Updates.TelephoneNo")}),(0,a.jsx)(o.A.Control,{type:"number",name:"telephoneNo",placeholder:l("Updates.TelephoneNumber"),required:!0,value:t,onChange:r}),(0,a.jsx)(o.A.Control.Feedback,{type:"invalid",children:l("Updates.PleaseTelephoneNumber")})]}),(0,a.jsxs)(o.A.Group,{children:[(0,a.jsx)(o.A.Label,{className:"required-field",children:l("Updates.MobileNo")}),(0,a.jsx)(o.A.Control,{type:"number",name:"mobileNo",placeholder:l("Updates.MobileNumber"),required:!0,value:u,onChange:r}),(0,a.jsx)(o.A.Control.Feedback,{type:"invalid",children:l("Updates.PleaseProvideMobile")})]})]})})})}}},e=>{var l=l=>e(e.s=l);e.O(0,[636,6593,8792],()=>l(16280)),_N_E=e.O()}]);
//# sourceMappingURL=ContactForm-332ede3061d7c4a9.js.map