"use strict";(()=>{var e={};e.id=1380,e.ids=[636,1380,3220],e.modules={123:e=>{e.exports=require("dom-helpers/removeEventListener")},1332:e=>{e.exports=require("react-custom-scrollbars-2")},1428:e=>{e.exports=import("axios")},1680:e=>{e.exports=require("@restart/ui/utils")},1919:e=>{e.exports=require("react-transition-group/Transition")},3892:e=>{e.exports=require("classnames")},4048:e=>{e.exports=require("react-truncate")},6009:e=>{e.exports=require("dom-helpers/ownerDocument")},6952:e=>{e.exports=require("@restart/ui/Modal")},7374:e=>{e.exports=require("@restart/ui/Dropdown")},8732:e=>{e.exports=require("react/jsx-runtime")},9653:e=>{e.exports=require("@restart/ui/Overlay")},11242:e=>{e.exports=require("redux-persist/integration/react")},11688:e=>{e.exports=require("@restart/hooks/useWillUnmount")},12388:e=>{e.exports=require("dom-helpers/addEventListener")},13364:e=>{e.exports=require("redux-saga/effects")},14062:e=>{e.exports=import("react-redux")},14078:e=>{e.exports=import("swr")},14332:e=>{e.exports=require("uncontrollable")},16116:e=>{e.exports=require("invariant")},18622:e=>{e.exports=require("yup")},19121:e=>{e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},22326:e=>{e.exports=require("react-dom")},22541:e=>{e.exports=require("redux-persist/lib/storage")},25303:e=>{e.exports=require("@restart/ui/NavItem")},26324:e=>{e.exports=require("warning")},27825:e=>{e.exports=require("lodash")},27910:e=>{e.exports=require("stream")},28217:e=>{e.exports=require("@restart/ui/DropdownItem")},29021:e=>{e.exports=require("fs")},29294:e=>{e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},29628:e=>{e.exports=require("dom-helpers/addClass")},29825:e=>{e.exports=require("prop-types")},29841:e=>{e.exports=require("dom-helpers/hasClass")},30362:e=>{e.exports=require("@restart/ui/DropdownToggle")},33873:e=>{e.exports=require("path")},36653:e=>{e.exports=require("nprogress")},36955:e=>{e.exports=require("@restart/hooks/useBreakpoint")},37766:e=>{e.exports=require("@restart/hooks/useMergedRefs")},38609:e=>{e.exports=require("react-data-table-component")},39756:e=>{e.exports=import("redux")},40051:e=>{e.exports=require("dom-helpers/removeClass")},40361:e=>{e.exports=require("next/dist/compiled/next-server/pages.runtime.prod.js")},42893:e=>{e.exports=import("react-hot-toast")},43294:e=>{e.exports=require("formik")},43635:(e,r,t)=>{t.a(e,async(e,s)=>{try{t.r(r),t.d(r,{config:()=>q,default:()=>c,getServerSideProps:()=>g,getStaticPaths:()=>m,getStaticProps:()=>x,reportWebVitals:()=>h,routeModule:()=>f,unstable_getServerProps:()=>b,unstable_getServerSideProps:()=>S,unstable_getStaticParams:()=>A,unstable_getStaticPaths:()=>v,unstable_getStaticProps:()=>P});var a=t(63885),o=t(80237),i=t(81413),n=t(9616),p=t.n(n),u=t(72386),l=t(47818),d=e([u,l]);[u,l]=d.then?(await d)():d;let c=(0,i.M)(l,"default"),x=(0,i.M)(l,"getStaticProps"),m=(0,i.M)(l,"getStaticPaths"),g=(0,i.M)(l,"getServerSideProps"),q=(0,i.M)(l,"config"),h=(0,i.M)(l,"reportWebVitals"),P=(0,i.M)(l,"unstable_getStaticProps"),v=(0,i.M)(l,"unstable_getStaticPaths"),A=(0,i.M)(l,"unstable_getStaticParams"),b=(0,i.M)(l,"unstable_getServerProps"),S=(0,i.M)(l,"unstable_getServerSideProps"),f=new a.PagesRouteModule({definition:{kind:o.A.PAGES,page:"/adminsettings/approval/VspaceAdmin",pathname:"/adminsettings/approval/VspaceAdmin",bundlePath:"",filename:""},components:{App:u.default,Document:p()},userland:l});s()}catch(e){s(e)}})},47818:(e,r,t)=>{t.a(e,async(e,s)=>{try{t.r(r),t.d(r,{default:()=>x});var a=t(8732),o=t(82015),i=t(91353),n=t(12403),p=t(42893),u=t(63487),l=t(56084),d=t(88751),c=e([p,u]);[p,u]=c.then?(await c)():c;let x=function(e){let{t:r}=(0,d.useTranslation)("common"),[t,s]=(0,o.useState)([]),[,c]=(0,o.useState)(!1),[x,m]=(0,o.useState)(0),[g,q]=(0,o.useState)(10),[h,P]=(0,o.useState)(!1),[v,A]=(0,o.useState)(""),[b,S]=(0,o.useState)({}),f={sort:{created_at:"desc"},limit:g,page:1,query:{vspace_status:"Request Pending"}},w=[{name:r("adminsetting.FocalPointsApprovalTable.Username"),selector:"username",cell:e=>e.username},{name:r("adminsetting.FocalPointsApprovalTable.Email"),selector:"email",cell:e=>e.email},{name:r("adminsetting.FocalPointsApprovalTable.Action"),selector:"",cell:e=>(0,a.jsxs)("div",{children:[(0,a.jsx)(i.A,{variant:"primary",size:"sm",onClick:()=>T(e,"approve"),children:r("adminsetting.FocalPointsApprovalTable.aprov")}),"\xa0",(0,a.jsx)(i.A,{variant:"secondary",size:"sm",onClick:()=>T(e,"reject"),children:r("adminsetting.FocalPointsApprovalTable.Reject")})]})}],j=async()=>{c(!0);let e=await u.A.get("/users",f);e&&e.data&&(s(e.data),m(e.totalCount),c(!1))},y=async(e,r)=>{f.limit=e,f.page=r,c(!0);let t=await u.A.get("/users",f);t&&t.data&&t.data.length>0&&(s(t.data),q(e),c(!1))},T=async(e,r)=>{P(!0),A(r),e&&e._id&&S({...e,vspace_status:"approve"===r?"Approved":"Rejected"})},_=async()=>{if("Rejected"===b.vspace_status)await u.A.remove(`/users/${b._id}`),j(),p.default.error(r("adminsetting.FocalPointsApprovalTable.Rejected")),S({}),P(!1);else{let e=await u.A.patch(`/users/${b._id}`,b);if(e&&403===e.status)return void p.default.error(e.response&&e.response.message?e.response.message:r("adminsetting.FocalPointsApprovalTable.Somethingwentswrong"));j(),p.default.success(r("adminsetting.FocalPointsApprovalTable.Approvemm")),S({}),P(!1)}},C=()=>P(!1);return(0,a.jsxs)("div",{children:[(0,a.jsxs)(n.A,{show:h,onHide:C,children:[(0,a.jsx)(n.A.Header,{closeButton:!0,children:(0,a.jsxs)(n.A.Title,{children:[v.charAt(0).toUpperCase()+v.slice(1)," ",r("adminsetting.FocalPointsApprovalTable.User")]})}),(0,a.jsxs)(n.A.Body,{children:[r("adminsetting.FocalPointsApprovalTable.Areyousurewantto")," ",v," ",r("adminsetting.FocalPointsApprovalTable.thisuser?")]}),(0,a.jsxs)(n.A.Footer,{children:[(0,a.jsx)(i.A,{variant:"secondary",onClick:C,children:r("adminsetting.FocalPointsApprovalTable.Cancel")}),(0,a.jsx)(i.A,{variant:"primary",onClick:_,children:r("adminsetting.FocalPointsApprovalTable.Yes")})]})]}),(0,a.jsx)(l.A,{columns:w,data:t,totalRows:x,pagServer:!0,handlePerRowsChange:y,handlePageChange:e=>{f.limit=g,f.page=e,j()}})]})};s()}catch(e){s(e)}})},50009:e=>{e.exports=require("@restart/ui/DropdownMenu")},56084:(e,r,t)=>{t.d(r,{A:()=>u});var s=t(8732);t(82015);var a=t(38609),o=t.n(a),i=t(88751),n=t(30370);function p(e){let{t:r}=(0,i.useTranslation)("common"),t={rowsPerPageText:r("Rowsperpage")},{columns:a,data:p,totalRows:u,resetPaginationToggle:l,subheader:d,subHeaderComponent:c,handlePerRowsChange:x,handlePageChange:m,rowsPerPage:g,defaultRowsPerPage:q,selectableRows:h,loading:P,pagServer:v,onSelectedRowsChange:A,clearSelectedRows:b,sortServer:S,onSort:f,persistTableHead:w,sortFunction:j,...y}=e,T={paginationComponentOptions:t,noDataComponent:r("NoData"),noHeader:!0,columns:a,data:p||[],dense:!0,paginationResetDefaultPage:l,subHeader:d,progressPending:P,subHeaderComponent:c,pagination:!0,paginationServer:v,paginationPerPage:q||10,paginationRowsPerPageOptions:g||[10,15,20,25,30],paginationTotalRows:u,onChangeRowsPerPage:x,onChangePage:m,selectableRows:h,onSelectedRowsChange:A,clearSelectedRows:b,progressComponent:(0,s.jsx)(n.A,{}),sortIcon:(0,s.jsx)("i",{className:"sort-icon fas fa-exchange-alt"}),sortServer:S,onSort:f,sortFunction:j,persistTableHead:w,className:"rki-table"};return(0,s.jsx)(o(),{...T})}p.defaultProps={subHeader:!1,pagination:!0,totalRows:null,pagServer:!0,onSelectedRowsChange:null,clearSelectedRows:!1,sortServer:!1,persistTableHead:!1};let u=p},57664:e=>{e.exports=require("dom-helpers/scrollbarSize")},58928:e=>{e.exports=require("dom-helpers/canUseDOM")},59717:e=>{e.exports=require("@restart/ui/DropdownContext")},60560:e=>{e.exports=require("dom-helpers/contains")},63033:e=>{e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},65447:e=>{e.exports=require("@restart/ui/Button")},67364:e=>{e.exports=require("@restart/hooks/useCallbackRef")},68455:e=>{e.exports=import("redux-saga")},69722:e=>{e.exports=require("es6-promise")},74075:e=>{e.exports=require("zlib")},74716:e=>{e.exports=require("moment")},74987:e=>{e.exports=require("@restart/ui/ModalManager")},78097:e=>{e.exports=require("next-redux-saga")},78634:e=>{e.exports=require("@restart/ui/Anchor")},80237:(e,r)=>{Object.defineProperty(r,"A",{enumerable:!0,get:function(){return t}});var t=function(e){return e.PAGES="PAGES",e.PAGES_API="PAGES_API",e.APP_PAGE="APP_PAGE",e.APP_ROUTE="APP_ROUTE",e.IMAGE="IMAGE",e}({})},81366:e=>{e.exports=require("redux-auth-wrapper/connectedAuthWrapper")},81413:(e,r)=>{Object.defineProperty(r,"M",{enumerable:!0,get:function(){return function e(r,t){return t in r?r[t]:"then"in r&&"function"==typeof r.then?r.then(r=>e(r,t)):"function"==typeof r&&"default"===t?r:void 0}}})},81521:e=>{e.exports=require("dom-helpers/querySelectorAll")},81895:e=>{e.exports=require("@restart/hooks/useEventCallback")},82015:e=>{e.exports=require("react")},86842:e=>{e.exports=require("@restart/ui/SelectableContext")},87571:e=>{e.exports=require("dom-helpers/transitionEnd")},88751:e=>{e.exports=require("next-i18next")},93787:e=>{e.exports=require("redux-persist")},94947:e=>{e.exports=require("@restart/hooks/useTimeout")},96196:e=>{e.exports=require("next-redux-wrapper")},98320:e=>{e.exports=require("@restart/hooks/useIsomorphicEffect")},99460:e=>{e.exports=require("dom-helpers/css")}};var r=require("../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[6089,9216,9616,2386],()=>t(43635));module.exports=s})();