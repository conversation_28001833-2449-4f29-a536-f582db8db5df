//Import Library
import express from "express";
import next from "next";
import helmet from "helmet";
import cookieParser from "cookie-parser";

//Import services/components
// import nextI18next from './i18n';

const port = process.env.PORT || 3000
const app = next({ dev: process.env.NODE_ENV !== 'production' })
const handle = app.getRequestHandler();

(async () => {
  await app.prepare()
  const server = express()

  // Add cookie parsing middleware
  server.use(cookieParser());
  server.use(helmet.hidePoweredBy());

  // await nextI18next.initPromise

  server.get('*', (req, res) => handle(req, res))

  server.listen(port)
  console.log(`> Ready on http://localhost:${port}`) // eslint-disable-line no-console
})()
