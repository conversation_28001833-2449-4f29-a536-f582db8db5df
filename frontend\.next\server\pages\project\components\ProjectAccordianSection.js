"use strict";(()=>{var e={};e.id=6797,e.ids=[636,3220,6797],e.modules={123:e=>{e.exports=require("dom-helpers/removeEventListener")},1332:e=>{e.exports=require("react-custom-scrollbars-2")},1428:e=>{e.exports=import("axios")},1680:e=>{e.exports=require("@restart/ui/utils")},1919:e=>{e.exports=require("react-transition-group/Transition")},3892:e=>{e.exports=require("classnames")},4048:e=>{e.exports=require("react-truncate")},6009:e=>{e.exports=require("dom-helpers/ownerDocument")},6417:(e,r,t)=>{t.d(r,{A:()=>a});let s=t(82015).createContext(null);s.displayName="CardHeaderContext";let a=s},6952:e=>{e.exports=require("@restart/ui/Modal")},7374:e=>{e.exports=require("@restart/ui/Dropdown")},8732:e=>{e.exports=require("react/jsx-runtime")},9653:e=>{e.exports=require("@restart/ui/Overlay")},11242:e=>{e.exports=require("redux-persist/integration/react")},11688:e=>{e.exports=require("@restart/hooks/useWillUnmount")},12388:e=>{e.exports=require("dom-helpers/addEventListener")},13364:e=>{e.exports=require("redux-saga/effects")},14062:e=>{e.exports=import("react-redux")},14078:e=>{e.exports=import("swr")},14332:e=>{e.exports=require("uncontrollable")},16116:e=>{e.exports=require("invariant")},18597:(e,r,t)=>{t.d(r,{A:()=>w});var s=t(3892),a=t.n(s),o=t(82015),i=t(80739),n=t(8732);let c=o.forwardRef(({className:e,bsPrefix:r,as:t="div",...s},o)=>(r=(0,i.oU)(r,"card-body"),(0,n.jsx)(t,{ref:o,className:a()(e,r),...s})));c.displayName="CardBody";let l=o.forwardRef(({className:e,bsPrefix:r,as:t="div",...s},o)=>(r=(0,i.oU)(r,"card-footer"),(0,n.jsx)(t,{ref:o,className:a()(e,r),...s})));l.displayName="CardFooter";var p=t(6417);let d=o.forwardRef(({bsPrefix:e,className:r,as:t="div",...s},c)=>{let l=(0,i.oU)(e,"card-header"),d=(0,o.useMemo)(()=>({cardHeaderBsPrefix:l}),[l]);return(0,n.jsx)(p.A.Provider,{value:d,children:(0,n.jsx)(t,{ref:c,...s,className:a()(r,l)})})});d.displayName="CardHeader";let u=o.forwardRef(({bsPrefix:e,className:r,variant:t,as:s="img",...o},c)=>{let l=(0,i.oU)(e,"card-img");return(0,n.jsx)(s,{ref:c,className:a()(t?`${l}-${t}`:l,r),...o})});u.displayName="CardImg";let x=o.forwardRef(({className:e,bsPrefix:r,as:t="div",...s},o)=>(r=(0,i.oU)(r,"card-img-overlay"),(0,n.jsx)(t,{ref:o,className:a()(e,r),...s})));x.displayName="CardImgOverlay";let m=o.forwardRef(({className:e,bsPrefix:r,as:t="a",...s},o)=>(r=(0,i.oU)(r,"card-link"),(0,n.jsx)(t,{ref:o,className:a()(e,r),...s})));m.displayName="CardLink";var j=t(7783);let f=(0,j.A)("h6"),h=o.forwardRef(({className:e,bsPrefix:r,as:t=f,...s},o)=>(r=(0,i.oU)(r,"card-subtitle"),(0,n.jsx)(t,{ref:o,className:a()(e,r),...s})));h.displayName="CardSubtitle";let g=o.forwardRef(({className:e,bsPrefix:r,as:t="p",...s},o)=>(r=(0,i.oU)(r,"card-text"),(0,n.jsx)(t,{ref:o,className:a()(e,r),...s})));g.displayName="CardText";let P=(0,j.A)("h5"),v=o.forwardRef(({className:e,bsPrefix:r,as:t=P,...s},o)=>(r=(0,i.oU)(r,"card-title"),(0,n.jsx)(t,{ref:o,className:a()(e,r),...s})));v.displayName="CardTitle";let q=o.forwardRef(({bsPrefix:e,className:r,bg:t,text:s,border:o,body:l=!1,children:p,as:d="div",...u},x)=>{let m=(0,i.oU)(e,"card");return(0,n.jsx)(d,{ref:x,...u,className:a()(r,m,t&&`bg-${t}`,s&&`text-${s}`,o&&`border-${o}`),children:l?(0,n.jsx)(c,{children:p}):p})});q.displayName="Card";let w=Object.assign(q,{Img:u,Title:v,Subtitle:h,Body:c,Link:m,Text:g,Header:d,Footer:l,ImgOverlay:x})},18622:e=>{e.exports=require("yup")},19121:e=>{e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},22326:e=>{e.exports=require("react-dom")},22541:e=>{e.exports=require("redux-persist/lib/storage")},25303:e=>{e.exports=require("@restart/ui/NavItem")},26324:e=>{e.exports=require("warning")},27825:e=>{e.exports=require("lodash")},27910:e=>{e.exports=require("stream")},28217:e=>{e.exports=require("@restart/ui/DropdownItem")},29021:e=>{e.exports=require("fs")},29294:e=>{e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},29628:e=>{e.exports=require("dom-helpers/addClass")},29825:e=>{e.exports=require("prop-types")},29841:e=>{e.exports=require("dom-helpers/hasClass")},30362:e=>{e.exports=require("@restart/ui/DropdownToggle")},33873:e=>{e.exports=require("path")},36653:e=>{e.exports=require("nprogress")},36955:e=>{e.exports=require("@restart/hooks/useBreakpoint")},37766:e=>{e.exports=require("@restart/hooks/useMergedRefs")},38058:(e,r,t)=>{t.a(e,async(e,s)=>{try{t.d(r,{A:()=>u});var a=t(8732),o=t(82015),i=t(19918),n=t.n(i),c=t(56084),l=t(63487),p=t(88751),d=e([l]);l=(d.then?(await d)():d)[0];let u=e=>{let{t:r}=(0,p.useTranslation)("common"),{type:t,id:s}=e,[i,d]=(0,o.useState)([]),[u,x]=(0,o.useState)(!1),[m,j]=(0,o.useState)(0),[f,h]=(0,o.useState)(10),[g]=(0,o.useState)(!1),P={sort:{created_at:"asc"},limit:f,page:1,query:{}},v=[{name:r("Title"),selector:"title",cell:e=>e&&e.title&&e._id?(0,a.jsx)(n(),{href:"/vspace/[...routes]",as:`/vspace/show/${e._id}`,children:e.title}):""},{name:r("Owner"),selector:"users",cell:e=>e&&e.user&&e.user.firstname?`${e.user.firstname} ${e.user.lastname}`:""},{name:r("PublicPrivate"),selector:"visibility",cell:e=>e&&e.visibility?"Public":"Private"},{name:r("NumberofMembers"),selector:"members",cell:e=>e&&e.members?e.members.length:"-"}],q=async e=>{x(!0);let r=await l.A.get(`stats/get${t}WithVspace/${s}`,P);r&&("Operation"===t?d(r.operation):d(r.project),j(r.totalCount),x(!1))},w=async(e,r)=>{P.limit=e,P.page=r,x(!0);let a=await l.A.get(`stats/get${t}WithVspace/${s}`,P);a&&("Operation"===t?d(a.operation):d(a.project),h(e),x(!1))};return(0,o.useEffect)(()=>{q(P)},[]),(0,a.jsx)("div",{children:(0,a.jsx)(c.A,{columns:v,data:i,totalRows:m,loading:u,resetPaginationToggle:g,handlePerRowsChange:w,handlePageChange:e=>{P.limit=f,P.page=e,q(P)}})})};s()}catch(e){s(e)}})},38609:e=>{e.exports=require("react-data-table-component")},39756:e=>{e.exports=import("redux")},40051:e=>{e.exports=require("dom-helpers/removeClass")},40361:e=>{e.exports=require("next/dist/compiled/next-server/pages.runtime.prod.js")},42893:e=>{e.exports=import("react-hot-toast")},43294:e=>{e.exports=require("formik")},46472:(e,r,t)=>{t.a(e,async(e,s)=>{try{t.r(r),t.d(r,{default:()=>u});var a=t(8732),o=t(82015),i=t(54131),n=t(82053),c=t(93024),l=t(38058),p=t(88751),d=e([i,l]);[i,l]=d.then?(await d)():d;let u=e=>{let{t:r}=(0,p.useTranslation)("common"),[t,s]=(0,o.useState)(!1);return(0,a.jsx)(a.Fragment,{children:(0,a.jsxs)(c.A.Item,{eventKey:"0",children:[(0,a.jsxs)(c.A.Header,{onClick:()=>s(!t),children:[(0,a.jsx)("div",{className:"cardTitle",children:r("LinkedVirtualSpace")}),(0,a.jsx)("div",{className:"cardArrow",children:t?(0,a.jsx)(n.FontAwesomeIcon,{icon:i.faMinus,color:"#fff"}):(0,a.jsx)(n.FontAwesomeIcon,{icon:i.faPlus,color:"#fff"})})]}),(0,a.jsx)(c.A.Body,{children:(0,a.jsx)(l.A,{id:e.routeData?.routes?.[1]||"",type:"Project",vspaceData:[],vspaceDataLoading:!1,vspaceDataTotalRows:0,vspaceDataPerPage:10,vspaceDataCurrentPage:1})})]})})};s()}catch(e){s(e)}})},46851:(e,r,t)=>{t.a(e,async(e,s)=>{try{t.r(r),t.d(r,{config:()=>f,default:()=>u,getServerSideProps:()=>j,getStaticPaths:()=>m,getStaticProps:()=>x,reportWebVitals:()=>h,routeModule:()=>A,unstable_getServerProps:()=>q,unstable_getServerSideProps:()=>w,unstable_getStaticParams:()=>v,unstable_getStaticPaths:()=>P,unstable_getStaticProps:()=>g});var a=t(63885),o=t(80237),i=t(81413),n=t(9616),c=t.n(n),l=t(72386),p=t(95433),d=e([l,p]);[l,p]=d.then?(await d)():d;let u=(0,i.M)(p,"default"),x=(0,i.M)(p,"getStaticProps"),m=(0,i.M)(p,"getStaticPaths"),j=(0,i.M)(p,"getServerSideProps"),f=(0,i.M)(p,"config"),h=(0,i.M)(p,"reportWebVitals"),g=(0,i.M)(p,"unstable_getStaticProps"),P=(0,i.M)(p,"unstable_getStaticPaths"),v=(0,i.M)(p,"unstable_getStaticParams"),q=(0,i.M)(p,"unstable_getServerProps"),w=(0,i.M)(p,"unstable_getServerSideProps"),A=new a.PagesRouteModule({definition:{kind:o.A.PAGES,page:"/project/components/ProjectAccordianSection",pathname:"/project/components/ProjectAccordianSection",bundlePath:"",filename:""},components:{App:l.default,Document:c()},userland:p});s()}catch(e){s(e)}})},50009:e=>{e.exports=require("@restart/ui/DropdownMenu")},53238:(e,r,t)=>{t.a(e,async(e,s)=>{try{t.r(r),t.d(r,{default:()=>m});var a=t(8732),o=t(82015),i=t(54131),n=t(82053),c=t(74716),l=t.n(c),p=t(93024),d=t(18597),u=t(88751),x=e([i]);i=(x.then?(await x)():x)[0];let m=e=>{let r="DD-MM-YYYY HH:mm:ss",{t}=(0,u.useTranslation)("common"),[s,c]=(0,o.useState)(!0);return(0,a.jsx)(a.Fragment,{children:(0,a.jsxs)(p.A.Item,{eventKey:"0",children:[(0,a.jsxs)(p.A.Header,{onClick:()=>c(!s),children:[(0,a.jsx)("div",{className:"cardTitle",children:t("ProjectDetails")}),(0,a.jsx)("div",{className:"cardArrow",children:s?(0,a.jsx)(n.FontAwesomeIcon,{icon:i.faPlus,color:"#fff"}):(0,a.jsx)(n.FontAwesomeIcon,{icon:i.faMinus,color:"#fff"})})]}),(0,a.jsx)(p.A.Body,{children:(0,a.jsxs)(d.A.Text,{className:"projectDetails ps-0",children:[(0,a.jsxs)("p",{children:[(0,a.jsx)("b",{children:t("Status")}),":",(0,a.jsxs)("span",{children:[" ",e.project.status?e.project.status.title:""]})]}),(0,a.jsxs)("p",{children:[(0,a.jsx)("b",{children:t("Created")}),":",(0,a.jsxs)("span",{children:[" ",e.project.created_at?l()(e.project.created_at).format(r):null," "]})]}),(0,a.jsxs)("p",{children:[(0,a.jsx)("b",{children:t("EndDate")}),":",(0,a.jsxs)("span",{children:[" ",e.project.end_date?l()(e.project.end_date).format("DD-MM-YYYY"):null]})]}),(0,a.jsxs)("p",{children:[(0,a.jsx)("b",{children:t("LastModified")}),":",(0,a.jsxs)("span",{children:[" ",e.project.updated_at?l()(e.project.updated_at).format(r):null," "]})]}),(0,a.jsxs)("p",{children:[(0,a.jsx)("b",{children:t("WeblinktoProject")}),":",(0,a.jsx)("span",{children:e.project?.website&&(0,a.jsx)("a",{href:e.project.website,target:"_blank",children:e.project.website})})]})]})})]})})};s()}catch(e){s(e)}})},54131:e=>{e.exports=import("@fortawesome/free-solid-svg-icons")},56084:(e,r,t)=>{t.d(r,{A:()=>l});var s=t(8732);t(82015);var a=t(38609),o=t.n(a),i=t(88751),n=t(30370);function c(e){let{t:r}=(0,i.useTranslation)("common"),t={rowsPerPageText:r("Rowsperpage")},{columns:a,data:c,totalRows:l,resetPaginationToggle:p,subheader:d,subHeaderComponent:u,handlePerRowsChange:x,handlePageChange:m,rowsPerPage:j,defaultRowsPerPage:f,selectableRows:h,loading:g,pagServer:P,onSelectedRowsChange:v,clearSelectedRows:q,sortServer:w,onSort:A,persistTableHead:y,sortFunction:b,...S}=e,C={paginationComponentOptions:t,noDataComponent:r("NoData"),noHeader:!0,columns:a,data:c||[],dense:!0,paginationResetDefaultPage:p,subHeader:d,progressPending:g,subHeaderComponent:u,pagination:!0,paginationServer:P,paginationPerPage:f||10,paginationRowsPerPageOptions:j||[10,15,20,25,30],paginationTotalRows:l,onChangeRowsPerPage:x,onChangePage:m,selectableRows:h,onSelectedRowsChange:v,clearSelectedRows:q,progressComponent:(0,s.jsx)(n.A,{}),sortIcon:(0,s.jsx)("i",{className:"sort-icon fas fa-exchange-alt"}),sortServer:w,onSort:A,sortFunction:b,persistTableHead:y,className:"rki-table"};return(0,s.jsx)(o(),{...C})}c.defaultProps={subHeader:!1,pagination:!0,totalRows:null,pagServer:!0,onSelectedRowsChange:null,clearSelectedRows:!1,sortServer:!1,persistTableHead:!1};let l=c},57664:e=>{e.exports=require("dom-helpers/scrollbarSize")},58928:e=>{e.exports=require("dom-helpers/canUseDOM")},59717:e=>{e.exports=require("@restart/ui/DropdownContext")},60560:e=>{e.exports=require("dom-helpers/contains")},63033:e=>{e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},65447:e=>{e.exports=require("@restart/ui/Button")},67364:e=>{e.exports=require("@restart/hooks/useCallbackRef")},68455:e=>{e.exports=import("redux-saga")},69722:e=>{e.exports=require("es6-promise")},74075:e=>{e.exports=require("zlib")},74716:e=>{e.exports=require("moment")},74987:e=>{e.exports=require("@restart/ui/ModalManager")},76429:(e,r,t)=>{t.a(e,async(e,s)=>{try{t.r(r),t.d(r,{default:()=>u});var a=t(8732),o=t(82015),i=t(93024),n=t(82053),c=t(54131),l=t(88751),p=t(82491),d=e([c,p]);[c,p]=d.then?(await d)():d;let u=e=>{let{t:r}=(0,l.useTranslation)("common"),[t,s]=(0,o.useState)(!0);return(0,a.jsxs)(i.A.Item,{eventKey:"2",children:[(0,a.jsxs)(i.A.Header,{onClick:()=>s(!t),children:[(0,a.jsx)("div",{className:"cardTitle",children:r("Discussions")}),(0,a.jsx)("div",{className:"cardArrow",children:t?(0,a.jsx)(n.FontAwesomeIcon,{icon:c.faPlus,color:"#fff"}):(0,a.jsx)(n.FontAwesomeIcon,{icon:c.faMinus,color:"#fff"})})]}),(0,a.jsx)(i.A.Body,{children:(0,a.jsx)(p.A,{type:"project",id:e?.routeData?.routes?e.routeData.routes[1]:""})})]})};s()}catch(e){s(e)}})},78097:e=>{e.exports=require("next-redux-saga")},78634:e=>{e.exports=require("@restart/ui/Anchor")},80237:(e,r)=>{Object.defineProperty(r,"A",{enumerable:!0,get:function(){return t}});var t=function(e){return e.PAGES="PAGES",e.PAGES_API="PAGES_API",e.APP_PAGE="APP_PAGE",e.APP_ROUTE="APP_ROUTE",e.IMAGE="IMAGE",e}({})},81366:e=>{e.exports=require("redux-auth-wrapper/connectedAuthWrapper")},81413:(e,r)=>{Object.defineProperty(r,"M",{enumerable:!0,get:function(){return function e(r,t){return t in r?r[t]:"then"in r&&"function"==typeof r.then?r.then(r=>e(r,t)):"function"==typeof r&&"default"===t?r:void 0}}})},81521:e=>{e.exports=require("dom-helpers/querySelectorAll")},81895:e=>{e.exports=require("@restart/hooks/useEventCallback")},82015:e=>{e.exports=require("react")},82053:e=>{e.exports=require("@fortawesome/react-fontawesome")},86842:e=>{e.exports=require("@restart/ui/SelectableContext")},86843:e=>{e.exports=require("moment/locale/de")},87571:e=>{e.exports=require("dom-helpers/transitionEnd")},88751:e=>{e.exports=require("next-i18next")},93787:e=>{e.exports=require("redux-persist")},94947:e=>{e.exports=require("@restart/hooks/useTimeout")},95433:(e,r,t)=>{t.a(e,async(e,s)=>{try{t.r(r),t.d(r,{default:()=>x});var a=t(8732);t(82015);var o=t(83551),i=t(49481),n=t(93024),c=t(99775),l=t(53238),p=t(46472),d=t(76429),u=e([l,p,d]);[l,p,d]=u.then?(await u)():u;let x=e=>{let r=(0,c.canViewDiscussionUpdate)(()=>(0,a.jsx)(d.default,{routeData:e.routeData}));return(0,a.jsx)(a.Fragment,{children:(0,a.jsx)(o.A,{children:(0,a.jsxs)(i.A,{className:"projectAccordion",xs:12,children:[(0,a.jsx)(n.A,{children:(0,a.jsx)(l.default,{project:e.projectData})}),(0,a.jsx)(n.A,{children:(0,a.jsx)(r,{})}),(0,a.jsx)(n.A,{children:(0,a.jsx)(p.default,{routeData:e.routeData})})]})})})};s()}catch(e){s(e)}})},96196:e=>{e.exports=require("next-redux-wrapper")},98320:e=>{e.exports=require("@restart/hooks/useIsomorphicEffect")},99460:e=>{e.exports=require("dom-helpers/css")},99775:(e,r,t)=>{t.r(r),t.d(r,{canAddProject:()=>n,canAddProjectForm:()=>c,canEditProject:()=>l,canEditProjectForm:()=>p,canViewDiscussionUpdate:()=>d,default:()=>u});var s=t(8732);t(82015);var a=t(81366),o=t.n(a),i=t(61421);let n=o()({authenticatedSelector:e=>!!e.permissions&&!!e.permissions.project&&!!e.permissions.project["create:any"],wrapperDisplayName:"CanAddProject"}),c=o()({authenticatedSelector:e=>!!e.permissions&&!!e.permissions.project&&!!e.permissions.project["create:any"],wrapperDisplayName:"CanAddProjectForm",FailureComponent:()=>(0,s.jsx)(i.default,{})}),l=o()({authenticatedSelector:(e,r)=>{if(e.permissions&&e.permissions.project){if(e.permissions.project["update:any"])return!0;else if(e.permissions.project["update:own"]&&r.project&&r.project.user&&r.project.user._id===e.user._id)return!0}return!1},wrapperDisplayName:"CanEditProject"}),p=o()({authenticatedSelector:(e,r)=>{if(e.permissions&&e.permissions.project){if(e.permissions.project["update:any"])return!0;else if(e.permissions.project["update:own"]&&r.project&&r.project.user&&r.project.user._id===e.user._id)return!0}return!1},wrapperDisplayName:"CanEditProjectForm",FailureComponent:()=>(0,s.jsx)(i.default,{})}),d=o()({authenticatedSelector:e=>!!e.permissions&&!!e.permissions.update&&!!e.permissions.update["read:any"],wrapperDisplayName:"CanViewDiscussionUpdate"}),u=n}};var r=require("../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[6089,9216,9616,2386,2491],()=>t(46851));module.exports=s})();