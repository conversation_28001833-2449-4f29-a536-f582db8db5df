{"version": 3, "file": "static/chunks/4631-3f26c77c2eb2b5e2.js", "mappings": "qVAgBA,IAAMA,EAAe,CACjBC,SAAU,GACVC,MAAO,GACPC,MAAO,GACPC,SAAU,GACVC,iBAAkB,GAClBC,YAAa,GACbC,OAAQ,EAAE,CACVC,QAAS,GACTC,cAAe,GACfC,UAAW,GACXC,QAAS,GACTC,UAAW,GACXC,SAAU,GACVC,SAAU,EACd,EA8iBA,EAxiBiB,IACb,GAAM,GAAEC,CAAC,MAAEC,CAAI,CAAE,CAAGC,CAAAA,EAAAA,EAAAA,EAAAA,CAAcA,CAAC,OAuiBhBC,EAAC,CAtiBd,CAACC,EAAWC,EAAa,CAAGC,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAMrB,GAC1C,CAACG,EAAOmB,EAAS,CAAGD,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAQ,EAAE,EACtC,CAACE,EAAcC,EAAgB,CAAGH,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAgB,EAAE,EAC5D,CAACI,EAASC,EAAW,CAAGL,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAQ,EAAE,EAC1C,CAACM,EAAaC,EAAe,CAAGP,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAY,EAAE,EACtD,CAACQ,EAAaC,EAAe,CAAGT,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAc,MAGtDU,EAAcC,CAFLC,EAAAA,EAAAA,SAAAA,CAASA,GAEGC,KAAK,CAACH,MAAM,EAAI,EAAE,CACvCI,EAAgC,OAAlBnB,EAAKoB,QAAQ,CAAY,CAAEC,SAAU,KAAM,EAAI,CAAEC,MAAO,KAAM,EAC5EC,EAAcvB,EAAKoB,QAAQ,CAC3B,CAACI,EAAcC,EAAgB,CAAGpB,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAc,MAExDqB,EAAa,CACfR,MAAO,CAAC,EACRS,KAAMR,EACNS,MAAO,IACPC,aAAcN,CAClB,EAEMO,EAAW,MAAOC,QAaAA,EAZpB,IAAIC,EAAS,CACT,CAAEC,MAAO,gBAAiBC,MAAO,eAAgB,EACjD,CAAED,MAAO,cAAeC,MAAO,aAAc,EAC7C,CAAED,MAAO,kCAAmCC,MAAO,gBAAiB,EACpE,CAAED,MAAO,QAASC,MAAO,MAAO,EAChC,CAAED,MAAO,WAAYC,MAAO,KAAM,EAClC,CAAED,MAAO,mBAAoBC,MAAO,kBAAmB,EACvD,CAAED,MAAO,sBAAuBC,MAAO,qBAAsB,EAC7D,CAAED,MAAO,eAAgBC,MAAO,cAAe,EAC/C,CAAED,MAAO,2BAA4BC,MAAO,yBAA0B,EACzE,CACDF,EAASA,EAAOL,IAAI,CAAC,CAACQ,EAAGC,IAAMD,EAAEF,KAAK,CAACI,aAAa,CAACD,EAAEH,KAAK,GAE5D3B,EADmB,OACVgC,CADWP,EAAAA,EAAgB5C,KAAAA,EAAhB4C,KAAAA,EAAAA,EAAuBQ,GAAvBR,KAA+B,CAAC,gBAAgEC,EAA/CA,EAAOQ,MAAM,CAACC,GAAgB,eAAXA,EAAEP,KAAK,EAEnG,EAEMQ,EAAgBC,IAClB7B,EAAe6B,GACfvC,EAAa,GAAgB,EACzB,EADyB,CACtBwC,CAAI,CACP3D,SAAU0D,EAAS1D,QAAQ,CAC3BW,UAAW+C,EAAS/C,SAAS,CAC7BC,SAAU8C,EAAS9C,QAAQ,CAC3BC,SAAU6C,EAAS7C,QAAQ,CAC3BZ,MAAOyD,EAASzD,KAAK,CACrBC,MAAOwD,EAASxD,KAAK,CACrBG,YAAaqD,EAASrD,WAAW,CAAGqD,EAASrD,WAAW,CAACuD,GAAG,CAAG,GAC/DrD,QAASmD,EAASnD,OAAO,CAAGmD,EAASnD,OAAO,CAACqD,GAAG,CAAG,GACnDtD,OAAQoD,EAASpD,MAAM,CACjBoD,EAASpD,MAAM,CAACuD,GAAG,CAAC,CAACC,EAAMC,KAChB,CAAEf,MAAOc,EAAKzB,KAAK,CAAEY,MAAOa,EAAKF,GAAG,CAAC,GAEhD,EAAE,CACRpD,cAAekD,EAASlD,aAAa,CAAGkD,EAASlD,aAAa,CAAG,GACjEC,UAAWiD,EAASjD,SAAS,CAAGiD,EAASjD,SAAS,CAAG,GACrDC,QAASgD,EAAShD,OAAO,CAAG,OAAS,QACzC,EACJ,EAEMsD,EAAU,UACZ,GAAIlC,CAAM,CAAC,EAAE,CAAE,CACX,IAAM4B,EAAiB,MAAMO,EAAAA,CAAUA,CAACC,GAAG,CAAC,SAAmB,OAAVpC,CAAM,CAAC,EAAE,EAAIW,GAC9DiB,GAAYA,EAASE,GAAG,EAAE,EACbF,GAEjBS,EAAWT,EAASnD,OAAO,EAAI,EAAUA,OAAO,CAASqD,GAAG,CAChE,CACJ,EAEMQ,EAAiB,UACnB,IAAMC,EAA8C,MAAMJ,EAAAA,CAAUA,CAACC,GAAG,CAAC,cAAezB,EACpF4B,IAAmBA,EAAgBC,IAAI,EAAID,EAAgBC,IAAI,CAACC,MAAM,CAAG,GAAG,EAC5DF,EAAgBC,IAAI,CAE5C,EAEME,EAAe,UACjB,IAAMd,EAAmC,MAAMO,EAAAA,CAAUA,CAACC,GAAG,CAAC,WAAYzB,GACtEiB,GAAYA,EAASY,IAAI,EAAIZ,EAASY,IAAI,CAACC,MAAM,CAAG,GAAG,EACxCb,EAASY,IAAI,CAEpC,EACAG,CAAAA,EAAAA,EAAAA,SAAAA,CAASA,CAAC,KACNC,IACAV,IACAI,IACAI,GACJ,EAAG,EAAE,EAEL,IAAME,EAAmB,UACrB,IAAMC,EAAoB,MAAMV,EAAAA,CAAUA,CAACW,IAAI,CAAC,uBAAwB,CAAC,GACrED,GAAeA,EAAY3E,QAAQ,EAAE,CACrCwC,EAAgBmC,GAChB9B,EAAS8B,GAEjB,EAEME,EAAUC,CAAAA,EAAAA,EAAAA,MAAAA,CAAMA,CAAC,MAEjBC,EAAa,IACf5D,EAAa,GAAqB,EAC9B,GAAG6D,CAAS,CACZ,EAF8B,CAE3BC,CAAG,CACV,EACJ,EASMd,EAAa,MAAOe,IACtB,IAAIC,EAAkB,EAAE,CACxB,GAAID,EAAI,CACJ,IAAMxB,EAAkC,MAAMO,EAAAA,CAAUA,CAACC,GAAG,CAAC,mBAAsB,OAAHgB,GAAMzC,GAClFiB,GAAYA,EAASY,IAAI,EAAE,CAC3Ba,EAAWzB,EAASY,IAAI,CAACT,GAAG,CAAC,CAACC,EAAMC,KACzB,CAAEf,MAAOc,EAAKzB,KAAK,CAAEY,MAAOa,EAAKF,GAAG,CAAC,EAChD,EACSlB,IAAI,CAAC,CAACQ,EAAGC,IAAMD,EAAEF,KAAK,CAACI,aAAa,CAACD,EAAEH,KAAK,EAE7D,CACAvB,EAAW0D,EACf,EAEMC,EAAgBC,IAClB,GAAM,MAAEC,CAAI,OAAErC,CAAK,CAAE,CAAGoC,EAAEE,aAAa,CACvCpE,EAAa,GAAqB,EAC9B,GAAG6D,CAAS,CACZ,CAACM,CAF6B,CAExB,CAAErC,EACZ,GACa,WAAW,CAApBqC,IACAnB,EAAWlB,GACX8B,EAAW,CAAEzE,OAAQ,EAAE,GAE/B,EAQMkF,EAAc,MAAO9B,EAAe+B,KAClC,GAA0B,qCAAb/B,GAAqDA,GAAgC,KAAM,CAA1BA,EAASgC,MAAM,EAC7FC,EAAAA,EAAKA,CAACC,KAAK,CAAC9E,EAAE,wDACd+E,OAAOC,QAAQ,CAAC,EAAG,KAEnBH,EAAAA,EAAKA,CAACI,OAAO,CAACjF,EAAE2E,IAChBO,IAAAA,IAAW,CAAC,wBAEpB,EAEMC,EAAe,MAAOZ,EAAQa,SAKlBC,EACCA,EAGJA,MAaPzC,EACA+B,EAtBJJ,EAAEe,cAAc,GAEhB,IAAMD,EAAaD,GAAUhF,EACvBoD,EAAY,CACdtE,QAAQ,QAAEmG,EAAAA,EAAWnG,QAAAA,EAAXmG,KAAAA,EAAAA,EAAqBE,GAArBF,QAAgC,GAAGG,IAAI,GACjD3F,SAAS,QAAEwF,EAAAA,EAAWxF,SAAS,EAApBwF,KAAAA,EAAAA,EAAsBG,GAAtBH,CAA0B,GACrCvF,SAAUuF,EAAWvF,QAAQ,CAC7BC,SAAUsF,EAAWtF,QAAQ,CAC7BZ,KAAK,QAAEkG,EAAAA,EAAWlG,KAAAA,EAAXkG,KAAAA,EAAAA,EAAkBE,GAAlBF,QAA6B,GACpCjG,MAAOiG,EAAWjG,KAAK,CACvBG,YAAa8F,EAAW9F,WAAW,CAAG8F,EAAW9F,WAAW,CAAG,KAC/DC,OAAQ6F,EAAW7F,MAAM,CACnB6F,EAAW7F,MAAM,CAACuD,GAAG,CAAC,CAACC,EAAWC,IACvBD,EAAKb,KAAK,EAErB,EAAE,CACR1C,QAAS4F,EAAW5F,OAAO,CAAG4F,EAAW5F,OAAO,CAAG,KACnDC,cAAe2F,EAAW3F,aAAa,CAAG2F,EAAW3F,aAAa,CAAG,KACrEC,UAAW0F,EAAW1F,SAAS,CAAG0F,EAAW1F,SAAS,CAAG,KACzDC,QAAgC,SAAvByF,EAAWzF,KAA4B,EAArB,CAI3BkB,IAAeA,EAAY,GAAM,EACL,IAAI,CAA5BuE,EAAWhG,QAAQ,GACnBmE,EAAK,EAAD,MAAY,CAAG6B,EAAWhG,QAAAA,EAElCsF,EAAW,kDACX/B,EAAW,MAAMO,EAAAA,CAAUA,CAACsC,KAAK,CAAC,UAA6B,OAAnB3E,EAAY,GAAM,EAAI0C,IAAX,CAEvDmB,EAAW,gDACXnB,EAAK,EAAD,MAAY,CAAG6B,EAAWhG,QAAQ,CACtCuD,EAAW,MAAMO,EAAAA,CAAUA,CAACW,IAAI,CAAC,SAAUN,IAE/CkB,EAAY9B,EAAU+B,EAC1B,EAEMe,EAAgB,GACXvD,IAAU/B,EAAUf,QAAQ,CAGvC,MACI,UAACsG,EAAAA,CAASA,CAAAA,CAACC,UAAU,WAAWC,KAAK,aACjC,UAACC,EAAAA,CAAIA,CAAAA,UACD,UAACC,EAAAA,CAAqBA,CAAAA,CAACC,SAAUb,EAAcc,IAAKlC,EAASmC,cAAe9F,EAAW+F,oBAAoB,EAAMC,aAAa,eAC1H,WAACN,EAAAA,CAAIA,CAACO,IAAI,YACN,UAACC,EAAAA,CAAGA,CAAAA,UACA,UAACC,EAAAA,CAAGA,CAAAA,UACA,UAACT,EAAAA,CAAIA,CAACU,KAAK,WACN1F,GAAeA,EAAY,GAAM,CAC5Bd,EAAE,GADmB,gCAErBA,EAAE,2CAIpB,UAACyG,KAAAA,CAAAA,GAED,UAACH,EAAAA,CAAGA,CAAAA,CAACV,UAAU,gBACX,UAACW,EAAAA,CAAGA,CAAAA,UACA,WAACG,EAAAA,CAAIA,CAACC,KAAK,YACP,UAACD,EAAAA,CAAIA,CAACE,KAAK,EAAChB,UAAU,0BACjB5F,EAAE,qCAEP,UAAC6G,EAAAA,EAASA,CAAAA,CACNrC,KAAK,WACLJ,GAAG,WACH0C,QAAQ,IACR3E,MAAO/B,EAAUlB,QAAQ,CACzB6H,UAAY5E,GAA8C,KAA/B6E,OAAO7E,GAAS,IAAIqD,IAAI,GACnDyB,aAAc,CACVF,UAAW/G,EAAE,gDACjB,EACAkH,SAAU5C,EACV8B,aAAa,eAK7B,WAACE,EAAAA,CAAGA,CAAAA,CAACV,UAAU,iBACX,WAACc,EAAAA,CAAIA,CAACC,KAAK,EAACQ,GAAIZ,EAAAA,CAAGA,WACf,UAACG,EAAAA,CAAIA,CAACE,KAAK,EAAChB,UAAU,0BACjB5F,EAAE,sCAEP,UAAC6G,EAAAA,EAASA,CAAAA,CACNrC,KAAK,YACLsC,QAAQ,IACRC,UAAW,GAAgBC,YAAO7E,GAAS,IAAIqD,IAAI,GACnDyB,aAAc,CACVF,UAAW/G,EAAE,+CACjB,EACAoE,GAAG,YACHjC,MAAO/B,EAAUP,SAAS,CAC1BqH,SAAU5C,OAGlB,WAACoC,EAAAA,CAAIA,CAACC,KAAK,EAACQ,GAAIZ,EAAAA,CAAGA,WACf,UAACG,EAAAA,CAAIA,CAACE,KAAK,WAAE5G,EAAE,qCACf,UAAC6G,EAAAA,EAASA,CAAAA,CACNrC,KAAK,WACLJ,GAAG,WACHjC,MAAO/B,EAAUN,QAAQ,CACzBoH,SAAU5C,UAKtB,UAACgC,EAAAA,CAAGA,CAAAA,CAACV,UAAU,gBACX,UAACW,EAAAA,CAAGA,CAAAA,UACA,WAACG,EAAAA,CAAIA,CAACC,KAAK,YACP,UAACD,EAAAA,CAAIA,CAACE,KAAK,WAAE5G,EAAE,qCACf,UAAC6G,EAAAA,EAASA,CAAAA,CACNrC,KAAK,WACLJ,GAAG,WACHjC,MAAO/B,EAAUL,QAAQ,CACzBmH,SAAU5C,WAK1B,UAACgC,EAAAA,CAAGA,CAAAA,CAACV,UAAU,gBACX,UAACW,EAAAA,CAAGA,CAAAA,UACA,WAACG,EAAAA,CAAIA,CAACC,KAAK,YACP,UAACD,EAAAA,CAAIA,CAACE,KAAK,EAAChB,UAAU,0BACjB5F,EAAE,kCAEP,UAAC6G,EAAAA,EAASA,CAAAA,CACNrC,KAAK,QACLJ,GAAG,QACHgD,KAAK,QACLL,UAAWA,CAAAA,GAAAA,OAAAA,CAAiB,QACpB,IACRE,aAAc,CAAEF,UAAW/G,EAAE,gDAAiD,EAC9EmC,MAAO/B,EAAUjB,KAAK,CACtB+H,SAAU5C,WAM1B,UAACgC,EAAAA,CAAGA,CAAAA,CAACV,UAAU,gBACX,UAACW,EAAAA,CAAGA,CAAAA,UACA,WAACG,EAAAA,CAAIA,CAACC,KAAK,YACP,UAACD,EAAAA,CAAIA,CAACE,KAAK,EAAChB,UAAU,0BACjB5F,EAAE,iCAEP,WAACqH,EAAAA,EAAWA,CAAAA,CACRP,QAAQ,IACRtC,KAAK,QACLrC,MAAO/B,EAAUhB,KAAK,CACtB6H,aAAcjH,EAAE,4CAChBkH,SAAU5C,YAEV,UAACgD,SAAAA,CAAOnF,MAAM,YAAInC,EAAE,uCACnBZ,EAAM2D,GAAG,CAAC,CAACC,EAAMuE,IAEV,UAACD,SAAAA,CAAmBnF,MAAOa,EAAKb,KAAK,UAChCa,EAAKd,KAAK,EADFqF,cAUrC,UAACjB,EAAAA,CAAGA,CAAAA,CAACV,UAAU,gBACX,UAACW,EAAAA,CAAGA,CAAAA,UACA,WAACG,EAAAA,CAAIA,CAACC,KAAK,YACP,UAACD,EAAAA,CAAIA,CAACE,KAAK,WAAE5G,EAAE,yCACf,WAACqH,EAAAA,EAAWA,CAAAA,CACR7C,KAAK,cACLrC,MAAO/B,EAAUb,WAAW,CAC5B0H,aAAcjH,EAAE,qDAChBkH,SAAU5C,YAEV,UAACgD,SAAAA,CAAOnF,MAAM,YAAInC,EAAE,+CACnBQ,EAAauC,GAAG,CAAC,CAACC,EAAMuE,IAEjB,UAACD,SAAAA,CAAmBnF,MAAOa,EAAKF,GAAG,UAC9BE,EAAKzB,KAAK,EADFgG,cAUrC,UAACjB,EAAAA,CAAGA,CAAAA,CAACV,UAAU,gBACX,UAACW,EAAAA,CAAGA,CAAAA,UACA,WAACG,EAAAA,CAAIA,CAACC,KAAK,YACP,UAACD,EAAAA,CAAIA,CAACE,KAAK,WAAE5G,EAAE,oCACf,WAACqH,EAAAA,EAAWA,CAAAA,CACR7C,KAAK,UACLJ,GAAG,UACHjC,MAAO/B,EAAUX,OAAO,CACxByH,SAAU5C,YAEV,UAACgD,SAAAA,CAAOnF,MAAM,YAAInC,EAAE,0CACnBY,EAAYmC,GAAG,CAAC,CAACC,EAAMC,IAEhB,UAACqE,SAAAA,CAAsBnF,MAAOa,EAAKF,GAAG,UACjCE,EAAKzB,KAAK,EADFyB,EAAKF,GAAG,aAU7C,UAACwD,EAAAA,CAAGA,CAAAA,CAACV,UAAU,gBACX,UAACW,EAAAA,CAAGA,CAAAA,UACA,WAACG,EAAAA,CAAIA,CAACC,KAAK,YACP,UAACD,EAAAA,CAAIA,CAACE,KAAK,WAAE5G,EAAE,mCACf,UAACwH,EAAAA,EAAWA,CAAAA,CACRC,gBAAiB,CACbC,gBAAiB1H,EAAE,iBACnB2H,oBAAqB3H,EAAE,+CAC3B,EACA4H,QAASlH,EACTyB,MAAO/B,EAAUZ,MAAM,CACvB0H,SAhRT,CAgRmBW,GA/Q1CxH,EAAc6D,GAAoB,EAC9B,GAAGA,CAAS,CACZ1E,EAF8B,KAEtB+E,EACZ,EACJ,EA4QoCqB,UAAW,SACXkC,WAAY9H,EAAE,0BAK9B,WAACsG,EAAAA,CAAGA,CAAAA,CAACV,UAAU,iBACX,UAACW,EAAAA,CAAGA,CAAAA,CAACwB,GAAI,WACL,WAACrB,EAAAA,CAAIA,CAACC,KAAK,YACP,UAACD,EAAAA,CAAIA,CAACE,KAAK,WAAE5G,EAAE,wCACf,WAACqH,EAAAA,EAAWA,CAAAA,CACR7C,KAAK,YACLJ,GAAG,YACHgD,KAAK,SACLjF,MAAO/B,EAAUT,SAAS,CAC1BuH,SAAU5C,YAEV,UAACgD,SAAAA,CAAOnF,MAAM,YAAInC,EAAE,qCACnBY,EAAYmC,GAAG,CAAC,CAACC,EAAMC,IAEhB,UAACqE,SAAAA,CAAsBnF,MAAOa,EAAKrD,SAAS,UACvC,IAAuBqD,MAAAA,CAAnBA,EAAKrD,SAAS,CAAC,MAAe,OAAXqD,EAAKzB,KAAK,GADzByB,EAAKF,GAAG,WAQzC,UAACyD,EAAAA,CAAGA,CAAAA,UACA,WAACG,EAAAA,CAAIA,CAACC,KAAK,YACP,UAACD,EAAAA,CAAIA,CAACE,KAAK,WAAC,SACZ,UAACC,EAAAA,EAASA,CAAAA,CACNO,KAAK,SACL5C,KAAK,gBACLJ,GAAG,gBACHjC,MAAO/B,EAAUV,aAAa,CAC9BwH,SAAU5C,YAK1B,UAACgC,EAAAA,CAAGA,CAAAA,CAACV,UAAU,gBACX,UAACW,EAAAA,CAAGA,CAAAA,UACA,UAACG,EAAAA,CAAIA,CAACC,KAAK,WACN7F,GAAeA,EAAY,GAAM,CAC9B,iCACI,UAAC4F,EAAAA,CAAIA,CAACE,KAAK,WAAE5G,EAAE,qCACf,UAAC6G,EAAAA,EAASA,CAAAA,CACNrC,KAAK,WACLJ,GAAG,WACHgD,KAAK,WACLY,QAAQ,iEACRf,aAAc,CACVe,QAAShI,EACL,8HAER,EACAmC,MAAO/B,EAAUf,QAAQ,CACzB6H,SAAU5C,OAIlB,iCACI,UAACoC,EAAAA,CAAIA,CAACE,KAAK,EAAChB,UAAU,0BACjB5F,EAAE,qCAEP,UAAC6G,EAAAA,EAASA,CAAAA,CACNrC,KAAK,WACLJ,GAAG,WACHgD,KAAK,WACLN,QAAQ,IACRkB,QAAQ,iEACRf,aAAc,CACVH,SAAU9G,EAAE,6CACZgI,QAAShI,EACL,8HAER,EACAmC,MAAO/B,EAAUf,QAAQ,CACzB6H,SAAU5C,aAQlC,UAACgC,EAAAA,CAAGA,CAAAA,CAACV,UAAU,gBACX,UAACW,EAAAA,CAAGA,CAAAA,UACA,UAACG,EAAAA,CAAIA,CAACC,KAAK,WACN7F,GAAeA,EAAY,GAAM,CAC9B,KADuB,IACvB,wBACI,UAAC4F,EAAAA,CAAIA,CAACE,KAAK,WAAE5G,EAAE,4CACf,UAAC6G,EAAAA,EAASA,CAAAA,CACNrC,KAAK,mBACLJ,GAAG,mBACHgD,KAAK,WACLL,UAAWrB,EACXuB,aAAc,CACVH,SAAU9G,EAAE,oDACZ+G,UAAW/G,EAAE,8CACjB,EACAmC,MAAO/B,EAAUd,gBAAgB,CACjC4H,SAAU5C,OAIlB,iCACI,UAACoC,EAAAA,CAAIA,CAACE,KAAK,EAAChB,UAAU,0BACjB5F,EAAE,4CAEP,UAAC6G,EAAAA,EAASA,CAAAA,CACNrC,KAAK,mBACLJ,GAAG,mBACHgD,KAAK,WACLN,QAAQ,IACRC,UAAWrB,EACXuB,aAAc,CACVH,SAAU9G,EAAE,oDACZ+G,UAAW/G,EAAE,8CACjB,EACAmC,MAAO/B,EAAUd,gBAAgB,CACjC4H,SAAU5C,aAQlC,UAACgC,EAAAA,CAAGA,CAAAA,CAACV,UAAU,gBACX,UAACW,EAAAA,CAAGA,CAAAA,UACA,WAACG,EAAAA,CAAIA,CAACC,KAAK,YACP,UAACzE,QAAAA,UAAOlC,EAAE,oCACV,WAACiI,EAAAA,EAAKA,CAACC,UAAU,EACb1D,KAAK,UACLyC,aAAcjH,EAAE,uCAChBmI,cAAe/H,EAAUR,OAAO,CAChCsH,SAAU5C,YAEV,UAAC2D,EAAAA,EAAKA,CAACG,SAAS,EAAChE,GAAG,MAAMlC,MAAM,MAAMC,MAAM,SAC5C,UAAC8F,EAAAA,EAAKA,CAACG,SAAS,EAAChE,GAAG,KAAKlC,MAAM,KAAKC,MAAM,oBAM1D,UAACmE,EAAAA,CAAGA,CAAAA,CAACV,UAAU,gBACX,WAACW,EAAAA,CAAGA,CAAAA,WACA,UAAC8B,EAAAA,CAAMA,CAAAA,CAACzC,UAAU,OAAOwB,KAAK,SAASkB,QAAQ,mBAC1CtI,EAAE,mCAEP,UAACqI,EAAAA,CAAMA,CAAAA,CAACzC,UAAU,OAAO2C,QAxYhC,CAwYyCC,IAvY1DnI,EAAapB,GAEb8F,OAAOC,QAAQ,CAAC,EAAG,EACvB,EAoY4EsD,QAAQ,gBACnDtI,EAAE,kCAEP,UAACyI,IAAIA,CACDC,KAAK,6BACLvB,GAAK,OAFJsB,yBAID,UAACJ,EAAAA,CAAMA,CAAAA,CAACC,QAAQ,qBAAatI,EAAE,kDASnE", "sources": ["webpack://_N_E/./pages/adminsettings/user/forms.tsx"], "sourcesContent": ["//Import Library\nimport React, { useState, useRef, useEffect } from \"react\";\nimport { <PERSON><PERSON>, Card, Form, Container, Row, Col } from \"react-bootstrap\";\nimport ValidationFormWrapper from \"../../../components/common/ValidationFormWrapper\";\nimport { TextInput, SelectGroup, Radio } from \"../../../components/common/FormValidation\";\nimport Router, { useRouter } from \"next/router\";\nimport validator from \"validator\";\nimport { MultiSelect } from \"react-multi-select-component\";\nimport Link from \"next/link\";\nimport toast from 'react-hot-toast';\n\n//Import services/components\nimport apiService from \"../../../services/apiService\";\nimport { useTranslation } from 'next-i18next';\nimport { User, Country, Region, Institution, ApiResponse } from \"../../../types\";\n\nconst initialState = {\n    username: \"\",\n    email: \"\",\n    roles: \"\",\n    password: \"\",\n    confirm_password: \"\",\n    institution: \"\",\n    region: [],\n    country: \"\",\n    mobile_number: \"\",\n    dial_code: \"\",\n    enabled: \"\",\n    firstname: \"\",\n    lastname: \"\",\n    position: \"\",\n};\n\ninterface UserFormProps {\n    [key: string]: any;\n}\n\nconst UserForm = (_props: UserFormProps) => {\n    const { t, i18n } = useTranslation('common');\n    const [formState, setFormState] = useState<any>(initialState);\n    const [roles, setRoles] = useState<any[]>([]);\n    const [institutions, setInstitutions] = useState<Institution[]>([]);\n    const [regions, setRegions] = useState<any[]>([]);\n    const [countryList, setcountryList] = useState<Country[]>([]);\n    const [userDetails, setUserDetails] = useState<User | null>(null);\n    const router = useRouter();\n\n    const routes: any = router.query.routes || [];\n    const titleSearch = i18n.language === \"de\" ? { title_de: \"asc\" } : { title: \"asc\" };\n    const currentLang = i18n.language;\n    const [loggedInUser, setloggedInUser] = useState<User | null>(null);\n\n    const userParams = {\n        query: {},\n        sort: titleSearch,\n        limit: \"~\",\n        languageCode: currentLang,\n    };\n\n    const getRoles = async (currentUserData: User) => {\n        let roless = [\n            { label: \"Authenticated\", value: \"AUTHENTICATED\" },\n            { label: \"Super Admin\", value: \"SUPER_ADMIN\" },\n            { label: \"Platform Admins at Institutions\", value: \"PLATFORM_ADMIN\" },\n            { label: \"NGO’s\", value: \"NGOS\" },\n            { label: \"EMT User\", value: \"EMT\" },\n            { label: \"INIG Stakeholder\", value: \"INIG_STAKEHOLDER\" },\n            { label: \"Health Professional\", value: \"HEALTH_PROFESSIONAL\" },\n            { label: \"General User\", value: \"GENERAL_USER\" },\n            { label: \"EMT National Focal Point\", value: \"EMT_NATIONAL_FOCALPOINT\" },\n        ];\n        roless = roless.sort((a, b) => a.label.localeCompare(b.label));\n        let filteredRole = !currentUserData.roles?.includes(\"SUPER_ADMIN\") ? roless.filter(x => x.value != \"SUPER_ADMIN\") : roless;\n        setRoles(filteredRole);\n    };\n\n    const get_response = (response: User) => {\n        setUserDetails(response);\n        setFormState((prev: any) => ({\n            ...prev,\n            username: response.username,\n            firstname: response.firstname,\n            lastname: response.lastname,\n            position: response.position,\n            email: response.email,\n            roles: response.roles,\n            institution: response.institution ? response.institution._id : \"\",\n            country: response.country ? response.country._id : \"\",\n            region: response.region\n                ? response.region.map((item, _i) => {\n                      return { label: item.title, value: item._id };\n                  })\n                : [],\n            mobile_number: response.mobile_number ? response.mobile_number : \"\",\n            dial_code: response.dial_code ? response.dial_code : \"\",\n            enabled: response.enabled ? \"true\" : \"false\",\n        }));\n    };\n\n    const getUser = async () => {\n        if (routes[1]) {\n            const response: User = await apiService.get(`users/${routes[1]}`, userParams);\n            if (response && response._id) {\n                get_response(response);\n            }\n            getRegions(response.country && (response.country as any)._id);\n        }\n    };\n\n    const getInsitutions = async () => {\n        const insitutionsList: ApiResponse<Institution[]> = await apiService.get(\"institution\", userParams);\n        if (insitutionsList && insitutionsList.data && insitutionsList.data.length > 0) {\n            setInstitutions(insitutionsList.data);\n        }\n    };\n\n    const getCountries = async () => {\n        const response: ApiResponse<Country[]> = await apiService.get(\"/country\", userParams);\n        if (response && response.data && response.data.length > 0) {\n            setcountryList(response.data);\n        }\n    };\n    useEffect(() => {\n        loggedInUserData();\n        getUser();\n        getInsitutions();\n        getCountries();\n    }, []);\n\n    const loggedInUserData = async () => {\n        const currentUser: User = await apiService.post(\"/users/getLoggedUser\", {});\n        if (currentUser && currentUser.username) {\n            setloggedInUser(currentUser);\n            getRoles(currentUser);\n        }\n    };\n\n    const formRef = useRef(null);\n\n    const clearValue = (obj: any) => {\n        setFormState((prevState: any) => ({\n            ...prevState,\n            ...obj,\n        }));\n    };\n\n    const bindCountryRegions = (e: any) => {\n        setFormState((prevState: any) => ({\n            ...prevState,\n            region: e,\n        }));\n    };\n\n    const getRegions = async (id: string) => {\n        let _regions: any[] = [];\n        if (id) {\n            const response: ApiResponse<Region[]> = await apiService.get(`/country_region/${id}`, userParams);\n            if (response && response.data) {\n                _regions = response.data.map((item, _i) => {\n                    return { label: item.title, value: item._id };\n                });\n                _regions.sort((a, b) => a.label.localeCompare(b.label));\n            }\n        }\n        setRegions(_regions);\n    };\n\n    const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement>) => {\n        const { name, value } = e.currentTarget;\n        setFormState((prevState: any) => ({\n            ...prevState,\n            [name]: value,\n        }));\n        if (name === \"country\") {\n            getRegions(value);\n            clearValue({ region: [] });\n        }\n    };\n\n    const resetHandler = () => {\n        setFormState(initialState);\n        // Reset validation state (Formik handles this automatically)\n        window.scrollTo(0, 0);\n    };\n\n    const getresponse = async (response: any, toastMsg: string) => {\n        if ((response && response === \"username or email already exists\") || (response && response.status === 403)) {\n            toast.error(t(\"adminsetting.user.form.usernameoremailalreadyexists\"));\n            window.scrollTo(0, 0);\n        } else {\n            toast.success(t(toastMsg))\n            Router.push(\"/adminsettings/users\");\n        }\n    };\n\n    const handleSubmit = async (e: any, values?: any) => {\n        e.preventDefault();\n        // Use Formik values if available, otherwise fall back to formState\n        const formValues = values || formState;\n        const data: any = {\n            username: formValues.username?.toLowerCase().trim(),\n            firstname: formValues.firstname?.trim(),\n            lastname: formValues.lastname,\n            position: formValues.position,\n            email: formValues.email?.toLowerCase(),\n            roles: formValues.roles,\n            institution: formValues.institution ? formValues.institution : null,\n            region: formValues.region\n                ? formValues.region.map((item: any, _i: any) => {\n                      return item.value;\n                  })\n                : [],\n            country: formValues.country ? formValues.country : null,\n            mobile_number: formValues.mobile_number ? formValues.mobile_number : null,\n            dial_code: formValues.dial_code ? formValues.dial_code : null,\n            enabled: formValues.enabled === \"true\" ? true : false,\n        };\n        let response;\n        let toastMsg;\n        if (userDetails && userDetails[\"_id\"]) {\n            if (formValues.password !== \"\") {\n                data[\"password\"] = formValues.password;\n            }\n            toastMsg = \"adminsetting.user.form.Updatedausersuccessfully\";\n            response = await apiService.patch(`/users/${userDetails[\"_id\"]}`, data);\n        } else {\n            toastMsg = \"adminsetting.user.form.Addedausersuccessfully\";\n            data[\"password\"] = formValues.password;\n            response = await apiService.post(\"/users\", data);\n        }\n        getresponse(response, toastMsg);\n    };\n\n    const matchPassword = (value: any) => {\n        return value === formState.password;\n    };\n\n    return (\n        <Container className=\"formCard\" fluid>\n            <Card>\n                <ValidationFormWrapper onSubmit={handleSubmit} ref={formRef} initialValues={formState} enableReinitialize={true} autoComplete=\"off\">\n                    <Card.Body>\n                        <Row>\n                            <Col>\n                                <Card.Title>\n                                    {userDetails && userDetails[\"_id\"]\n                                        ? t(\"adminsetting.user.form.EditUser\")\n                                        : t(\"adminsetting.user.form.CreateUser\")}\n                                </Card.Title>\n                            </Col>\n                        </Row>\n                        <hr />\n\n                        <Row className=\"mb-3\">\n                            <Col>\n                                <Form.Group>\n                                    <Form.Label className=\"required-field\">\n                                        {t(\"adminsetting.user.form.Username\")}\n                                    </Form.Label>\n                                    <TextInput\n                                        name=\"username\"\n                                        id=\"username\"\n                                        required\n                                        value={formState.username}\n                                        validator={(value: any) => String(value || '').trim() !== \"\"}\n                                        errorMessage={{\n                                            validator: t(\"adminsetting.user.form.Youdon'thaveaUsername?\"),\n                                        }}\n                                        onChange={handleChange}\n                                        autoComplete=\"off\"\n                                    />\n                                </Form.Group>\n                            </Col>\n                        </Row>\n                        <Row className=\"mb-3\">\n                            <Form.Group as={Col}>\n                                <Form.Label className=\"required-field\">\n                                    {t(\"adminsetting.user.form.Firstname\")}\n                                </Form.Label>\n                                <TextInput\n                                    name=\"firstname\"\n                                    required\n                                    validator={(value: any) => String(value || '').trim() !== \"\"}\n                                    errorMessage={{\n                                        validator: t(\"adminsetting.user.form.Pleaseenterafirstname\"),\n                                    }}\n                                    id=\"firstname\"\n                                    value={formState.firstname}\n                                    onChange={handleChange}\n                                />\n                            </Form.Group>\n                            <Form.Group as={Col}>\n                                <Form.Label>{t(\"adminsetting.user.form.Lastname\")}</Form.Label>\n                                <TextInput\n                                    name=\"lastname\"\n                                    id=\"lastname\"\n                                    value={formState.lastname}\n                                    onChange={handleChange}\n                                />\n                            </Form.Group>\n                        </Row>\n\n                        <Row className=\"mb-3\">\n                            <Col>\n                                <Form.Group>\n                                    <Form.Label>{t(\"adminsetting.user.form.Position\")}</Form.Label>\n                                    <TextInput\n                                        name=\"position\"\n                                        id=\"position\"\n                                        value={formState.position}\n                                        onChange={handleChange}\n                                    />\n                                </Form.Group>\n                            </Col>\n                        </Row>\n                        <Row className=\"mb-3\">\n                            <Col>\n                                <Form.Group>\n                                    <Form.Label className=\"required-field\">\n                                        {t(\"adminsetting.user.form.Email\")}\n                                    </Form.Label>\n                                    <TextInput\n                                        name=\"email\"\n                                        id=\"email\"\n                                        type=\"email\"\n                                        validator={validator.isEmail}\n                                        required\n                                        errorMessage={{ validator: t(\"adminsetting.user.form.Pleaseenteravalidemail\") }}\n                                        value={formState.email}\n                                        onChange={handleChange}\n                                    />\n                                </Form.Group>\n                            </Col>\n                        </Row>\n\n                        <Row className=\"mb-3\">\n                            <Col>\n                                <Form.Group>\n                                    <Form.Label className=\"required-field\">\n                                        {t(\"adminsetting.user.form.Role\")}\n                                    </Form.Label>\n                                    <SelectGroup\n                                        required\n                                        name=\"roles\"\n                                        value={formState.roles}\n                                        errorMessage={t(\"adminsetting.user.form.PleaseselectaRole\")}\n                                        onChange={handleChange}\n                                    >\n                                        <option value=\"\">{t(\"adminsetting.user.form.SelectRole\")}</option>\n                                        {roles.map((item, index) => {\n                                            return (\n                                                <option key={index} value={item.value}>\n                                                    {item.label}\n                                                </option>\n                                            );\n                                        })}\n                                    </SelectGroup>\n                                </Form.Group>\n                            </Col>\n                        </Row>\n\n                        <Row className=\"mb-3\">\n                            <Col>\n                                <Form.Group>\n                                    <Form.Label>{t(\"adminsetting.user.form.Organisation\")}</Form.Label>\n                                    <SelectGroup\n                                        name=\"institution\"\n                                        value={formState.institution}\n                                        errorMessage={t(\"adminsetting.user.form.PleaseselectaOrganisation.\")}\n                                        onChange={handleChange}\n                                    >\n                                        <option value=\"\">{t(\"adminsetting.user.form.SelectOrganisation\")}</option>\n                                        {institutions.map((item, index) => {\n                                            return (\n                                                <option key={index} value={item._id}>\n                                                    {item.title}\n                                                </option>\n                                            );\n                                        })}\n                                    </SelectGroup>\n                                </Form.Group>\n                            </Col>\n                        </Row>\n\n                        <Row className=\"mb-3\">\n                            <Col>\n                                <Form.Group>\n                                    <Form.Label>{t(\"adminsetting.user.form.Country\")}</Form.Label>\n                                    <SelectGroup\n                                        name=\"country\"\n                                        id=\"country\"\n                                        value={formState.country}\n                                        onChange={handleChange}\n                                    >\n                                        <option value=\"\">{t(\"adminsetting.user.form.SelectCountry\")}</option>\n                                        {countryList.map((item, _i) => {\n                                            return (\n                                                <option key={item._id} value={item._id}>\n                                                    {item.title}\n                                                </option>\n                                            );\n                                        })}\n                                    </SelectGroup>\n                                </Form.Group>\n                            </Col>\n                        </Row>\n\n                        <Row className=\"mb-3\">\n                            <Col>\n                                <Form.Group>\n                                    <Form.Label>{t(\"adminsetting.user.form.Region\")}</Form.Label>\n                                    <MultiSelect\n                                        overrideStrings={{\n                                            selectSomeItems: t(\"SelectRegions\"),\n                                            allItemsAreSelected: t(\"adminsetting.user.form.AllRegionsareSelected\"),\n                                        }}\n                                        options={regions}\n                                        value={formState.region}\n                                        onChange={bindCountryRegions}\n                                        className={\"region\"}\n                                        labelledBy={t(\"SelectRegions\")}\n                                    />\n                                </Form.Group>\n                            </Col>\n                        </Row>\n                        <Row className=\"mb-3\">\n                            <Col xs={6}>\n                                <Form.Group>\n                                    <Form.Label>{t(\"adminsetting.user.form.PhoneNumber\")}</Form.Label>\n                                    <SelectGroup\n                                        name=\"dial_code\"\n                                        id=\"dial_code\"\n                                        type=\"number\"\n                                        value={formState.dial_code}\n                                        onChange={handleChange}\n                                    >\n                                        <option value=\"\">{t(\"adminsetting.user.form.DialCode\")}</option>\n                                        {countryList.map((item, _i) => {\n                                            return (\n                                                <option key={item._id} value={item.dial_code}>\n                                                    {`(${item.dial_code}) ${item.title}`}\n                                                </option>\n                                            );\n                                        })}\n                                    </SelectGroup>\n                                </Form.Group>\n                            </Col>\n                            <Col>\n                                <Form.Group>\n                                    <Form.Label>&nbsp;</Form.Label>\n                                    <TextInput\n                                        type=\"number\"\n                                        name=\"mobile_number\"\n                                        id=\"mobile_number\"\n                                        value={formState.mobile_number}\n                                        onChange={handleChange}\n                                    />\n                                </Form.Group>\n                            </Col>\n                        </Row>\n                        <Row className=\"mb-3\">\n                            <Col>\n                                <Form.Group>\n                                    {userDetails && userDetails[\"_id\"] ? (\n                                        <>\n                                            <Form.Label>{t(\"adminsetting.user.form.Password\")}</Form.Label>\n                                            <TextInput\n                                                name=\"password\"\n                                                id=\"password\"\n                                                type=\"password\"\n                                                pattern=\"^(?=.*?[A-Z])(?=.*?[a-z])(?=.*?[0-9])(?=.*?[#?!@$%^&*-]).{8,}$\"\n                                                errorMessage={{\n                                                    pattern: t(\n                                                        \"adminsetting.user.form.Passwordshouldcontainatleast8characters,withatleastonedigit,oneletterinuppercase&onespecialcharacter\"\n                                                    ),\n                                                }}\n                                                value={formState.password}\n                                                onChange={handleChange}\n                                            />\n                                        </>\n                                    ) : (\n                                        <>\n                                            <Form.Label className=\"required-field\">\n                                                {t(\"adminsetting.user.form.Password\")}\n                                            </Form.Label>\n                                            <TextInput\n                                                name=\"password\"\n                                                id=\"password\"\n                                                type=\"password\"\n                                                required\n                                                pattern=\"^(?=.*?[A-Z])(?=.*?[a-z])(?=.*?[0-9])(?=.*?[#?!@$%^&*-]).{8,}$\"\n                                                errorMessage={{\n                                                    required: t(\"adminsetting.user.form.Passwordisrequired\"),\n                                                    pattern: t(\n                                                        \"adminsetting.user.form.Passwordshouldcontainatleast8characters,withatleastonedigit,oneletterinuppercase&onespecialcharacter\"\n                                                    ),\n                                                }}\n                                                value={formState.password}\n                                                onChange={handleChange}\n                                            />\n                                        </>\n                                    )}\n                                </Form.Group>\n                            </Col>\n                        </Row>\n\n                        <Row className=\"mb-3\">\n                            <Col>\n                                <Form.Group>\n                                    {userDetails && userDetails[\"_id\"] ? (\n                                        <>\n                                            <Form.Label>{t(\"adminsetting.user.form.ConfirmPassword\")}</Form.Label>\n                                            <TextInput\n                                                name=\"confirm_password\"\n                                                id=\"confirm_password\"\n                                                type=\"password\"\n                                                validator={matchPassword}\n                                                errorMessage={{\n                                                    required: t(\"adminsetting.user.form.Confirmpasswordisrequired\"),\n                                                    validator: t(\"adminsetting.user.form.Passworddoesnotmatch\"),\n                                                }}\n                                                value={formState.confirm_password}\n                                                onChange={handleChange}\n                                            />\n                                        </>\n                                    ) : (\n                                        <>\n                                            <Form.Label className=\"required-field\">\n                                                {t(\"adminsetting.user.form.ConfirmPassword\")}\n                                            </Form.Label>\n                                            <TextInput\n                                                name=\"confirm_password\"\n                                                id=\"confirm_password\"\n                                                type=\"password\"\n                                                required\n                                                validator={matchPassword}\n                                                errorMessage={{\n                                                    required: t(\"adminsetting.user.form.Confirmpasswordisrequired\"),\n                                                    validator: t(\"adminsetting.user.form.Passworddoesnotmatch\"),\n                                                }}\n                                                value={formState.confirm_password}\n                                                onChange={handleChange}\n                                            />\n                                        </>\n                                    )}\n                                </Form.Group>\n                            </Col>\n                        </Row>\n\n                        <Row className=\"mb-3\">\n                            <Col>\n                                <Form.Group>\n                                    <label>{t(\"adminsetting.user.form.Enabled\")}</label>\n                                    <Radio.RadioGroup\n                                        name=\"enabled\"\n                                        errorMessage={t(\"adminsetting.user.form.Itisrequired\")}\n                                        valueSelected={formState.enabled}\n                                        onChange={handleChange}\n                                    >\n                                        <Radio.RadioItem id=\"yes\" label=\"Yes\" value=\"true\" />\n                                        <Radio.RadioItem id=\"no\" label=\"No\" value=\"false\" />\n                                    </Radio.RadioGroup>\n                                </Form.Group>\n                            </Col>\n                        </Row>\n\n                        <Row className=\"my-4\">\n                            <Col>\n                                <Button className=\"me-2\" type=\"submit\" variant=\"primary\">\n                                    {t(\"adminsetting.user.form.Submit\")}\n                                </Button>\n                                <Button className=\"me-2\" onClick={resetHandler} variant=\"info\">\n                                    {t(\"adminsetting.user.form.Reset\")}\n                                </Button>\n                                <Link\n                                    href=\"/adminsettings/[...routes]\"\n                                    as={`/adminsettings/users`}\n                                    >\n                                    <Button variant=\"secondary\">{t(\"adminsetting.user.form.Cancel\")}</Button>\n                                </Link>\n                            </Col>\n                        </Row>\n                    </Card.Body>\n                </ValidationFormWrapper>\n            </Card>\n        </Container>\n    );\n};\n\nexport default UserForm;\n"], "names": ["initialState", "username", "email", "roles", "password", "confirm_password", "institution", "region", "country", "mobile_number", "dial_code", "enabled", "firstname", "lastname", "position", "t", "i18n", "useTranslation", "UserForm", "formState", "setFormState", "useState", "setRoles", "institutions", "setInstitutions", "regions", "setRegions", "countryList", "setcountryList", "userDetails", "setUserDetails", "routes", "router", "useRouter", "query", "titleSearch", "language", "title_de", "title", "currentLang", "loggedInUser", "setloggedInUser", "userParams", "sort", "limit", "languageCode", "getRoles", "currentUserData", "roless", "label", "value", "a", "b", "localeCompare", "filteredRole", "includes", "filter", "x", "get_response", "response", "prev", "_id", "map", "item", "_i", "getUser", "apiService", "get", "getRegions", "getInsitutions", "insitutionsList", "data", "length", "getCountries", "useEffect", "loggedInUserData", "currentUser", "post", "formRef", "useRef", "clearValue", "prevState", "obj", "id", "_regions", "handleChange", "e", "name", "currentTarget", "getresponse", "toastMsg", "status", "toast", "error", "window", "scrollTo", "success", "Router", "handleSubmit", "values", "formValues", "preventDefault", "toLowerCase", "trim", "patch", "matchPassword", "Container", "className", "fluid", "Card", "ValidationFormWrapper", "onSubmit", "ref", "initialValues", "enableReinitialize", "autoComplete", "Body", "Row", "Col", "Title", "hr", "Form", "Group", "Label", "TextInput", "required", "validator", "String", "errorMessage", "onChange", "as", "type", "SelectGroup", "option", "index", "MultiSelect", "overrideStrings", "selectSomeItems", "allItemsAreSelected", "options", "bindCountryRegions", "labelledBy", "xs", "pattern", "Radio", "RadioGroup", "valueSelected", "RadioItem", "<PERSON><PERSON>", "variant", "onClick", "re<PERSON><PERSON><PERSON><PERSON>", "Link", "href"], "sourceRoot": "", "ignoreList": []}