"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[6798],{143:(e,s,i)=>{i.r(s),i.d(s,{default:()=>c});var t=i(37876),a=i(49589),n=i(56970),r=i(37784),o=i(2827),d=i(14232),l=i(31753),p=i(53718);let c=e=>{let{filterText:s,onFilter:i,onHandleSearch:c,onClear:m,onKeyPress:u}=e,{t:A}=(0,l.Bd)("common"),[g,y]=(0,d.useState)([]),[,h]=(0,d.useState)(!1),S={sort:{created_at:"desc"},limit:"~",page:1,query:{},select:"-country -dial_code -firstname -region -institution.address -institution.contact_name -institution.description -institution.dial_code -institution.document -institution.doc_src -institution.images -institution.images_src -institution.expertise -institution.hazards -institution.hazard_types -institution.header -institution.networks -institution.partners -institution.telephone -institution.twitter -institution.type -institution.unit -institution.user -mobile_number -position"},_=async()=>{h(!0);let e=await p.A.get("/users",S);e&&Array.isArray(e.data)&&(y(e.data.map((e,s)=>({label:e.username,value:e._id}))),h(!1))};return(0,d.useEffect)(()=>{_()},[]),(0,t.jsx)(a.A,{fluid:!0,className:"p-0",children:(0,t.jsx)(n.A,{children:(0,t.jsx)(r.A,{xs:6,md:4,className:"p-0",children:(0,t.jsx)(o.Ay,{autoFocus:!0,isClearable:!0,isSearchable:!0,onKeyDown:u,onChange:i,placeholder:A("adminsetting.user.table.Usernameoremail"),options:g})})})})}},21546:(e,s,i)=>{i.r(s),i.d(s,{canAddAreaOfWork:()=>n,canAddContent:()=>C,canAddCountry:()=>r,canAddDeploymentStatus:()=>o,canAddEventStatus:()=>d,canAddExpertise:()=>l,canAddFocalPointApproval:()=>p,canAddHazardTypes:()=>u,canAddHazards:()=>m,canAddLandingPage:()=>j,canAddOperationStatus:()=>h,canAddOrganisationApproval:()=>A,canAddOrganisationNetworks:()=>g,canAddOrganisationTypes:()=>y,canAddProjectStatus:()=>S,canAddRegions:()=>_,canAddRiskLevels:()=>w,canAddSyndromes:()=>f,canAddUpdateTypes:()=>x,canAddUsers:()=>v,canAddVspaceApproval:()=>c,canAddWorldRegion:()=>N,default:()=>D});var t=i(8178);let a="create:any",n=(0,t.A)({authenticatedSelector:e=>!!e.permissions&&!!e.permissions.area_of_work&&!!e.permissions.area_of_work[a],wrapperDisplayName:"CanAddAreaOfWork"}),r=(0,t.A)({authenticatedSelector:e=>!!e.permissions&&!!e.permissions.country&&!!e.permissions.country[a],wrapperDisplayName:"CanAddCountry"}),o=(0,t.A)({authenticatedSelector:e=>!!e.permissions&&!!e.permissions.deployment_status&&!!e.permissions.deployment_status[a],wrapperDisplayName:"CanAddDeploymentStatus"}),d=(0,t.A)({authenticatedSelector:e=>!!e.permissions&&!!e.permissions.event_status&&!!e.permissions.event_status[a],wrapperDisplayName:"CanAddEventStatus"}),l=(0,t.A)({authenticatedSelector:e=>!!e.permissions&&!!e.permissions.expertise&&!!e.permissions.expertise[a],wrapperDisplayName:"CanAddExpertise"}),p=(0,t.A)({authenticatedSelector:e=>!!e.permissions&&!!e.permissions.institution_focal_point&&!!e.permissions.institution_focal_point[a],wrapperDisplayName:"CanAddFocalPointApproval"}),c=(0,t.A)({authenticatedSelector:e=>!!e.permissions&&!!e.permissions.institution_focal_point&&!!e.permissions.institution_focal_point[a],wrapperDisplayName:"CanAddVspaceApproval"}),m=(0,t.A)({authenticatedSelector:e=>!!e.permissions&&!!e.permissions.hazard&&!!e.permissions.hazard[a],wrapperDisplayName:"CanAddHazards"}),u=(0,t.A)({authenticatedSelector:e=>!!e.permissions&&!!e.permissions.hazard_type&&!!e.permissions.hazard_type[a],wrapperDisplayName:"CanAddHazardTypes"}),A=(0,t.A)({authenticatedSelector:e=>!!e.permissions&&!!e.permissions.institution&&!!e.permissions.institution[a],wrapperDisplayName:"CanAddOrganisationApproval"}),g=(0,t.A)({authenticatedSelector:e=>!!e.permissions&&!!e.permissions.institution_network&&!!e.permissions.institution_network[a],wrapperDisplayName:"CanAddOrganisationNetworks"}),y=(0,t.A)({authenticatedSelector:e=>!!e.permissions&&!!e.permissions.institution_type&&!!e.permissions.institution_type[a],wrapperDisplayName:"CanAddOrganisationTypes"}),h=(0,t.A)({authenticatedSelector:e=>!!e.permissions&&!!e.permissions.operation_status&&!!e.permissions.operation_status[a],wrapperDisplayName:"CanAddOperationStatus"}),S=(0,t.A)({authenticatedSelector:e=>!!e.permissions&&!!e.permissions.project_status&&!!e.permissions.project_status[a],wrapperDisplayName:"CanAddProjectStatus"}),_=(0,t.A)({authenticatedSelector:e=>!!e.permissions&&!!e.permissions.region&&!!e.permissions.region[a],wrapperDisplayName:"CanAddRegions"}),w=(0,t.A)({authenticatedSelector:e=>!!e.permissions&&!!e.permissions.risk_level&&!!e.permissions.risk_level[a],wrapperDisplayName:"CanAddRiskLevels"}),f=(0,t.A)({authenticatedSelector:e=>!!e.permissions&&!!e.permissions.syndrome&&!!e.permissions.syndrome[a],wrapperDisplayName:"CanAddSyndromes"}),x=(0,t.A)({authenticatedSelector:e=>!!e.permissions&&!!e.permissions.update_type&&!!e.permissions.update_type[a],wrapperDisplayName:"CanAddUpdateTypes"}),v=(0,t.A)({authenticatedSelector:e=>!!e.permissions&&!!e.permissions.users&&!!e.permissions.users[a],wrapperDisplayName:"CanAddUsers"}),N=(0,t.A)({authenticatedSelector:e=>!!e.permissions&&!!e.permissions.worl_region&&!!e.permissions.worl_region[a],wrapperDisplayName:"CanAddWorldRegion"}),j=(0,t.A)({authenticatedSelector:e=>!!e.permissions&&!!e.permissions.landing_page&&!!e.permissions.landing_page[a],wrapperDisplayName:"CanAddLandingPage"}),C=(0,t.A)({authenticatedSelector:e=>!!e.permissions&&!!e.permissions.operation&&!!e.permissions.operation[a]&&!!e.permissions.project&&!!e.permissions.project[a]&&!!e.permissions.event&&!!e.permissions.event[a]&&!!e.permissions.vspace&&!!e.permissions.vspace[a]&&!!e.permissions.institution&&!!e.permissions.institution[a]&&!!e.permissions.update&&!!e.permissions.update[a]||!1,wrapperDisplayName:"CanAddContent"}),D=n},50749:(e,s,i)=>{i.d(s,{A:()=>d});var t=i(37876);i(14232);var a=i(89773),n=i(31753),r=i(5507);function o(e){let{t:s}=(0,n.Bd)("common"),i={rowsPerPageText:s("Rowsperpage")},{columns:o,data:d,totalRows:l,resetPaginationToggle:p,subheader:c,subHeaderComponent:m,handlePerRowsChange:u,handlePageChange:A,rowsPerPage:g,defaultRowsPerPage:y,selectableRows:h,loading:S,pagServer:_,onSelectedRowsChange:w,clearSelectedRows:f,sortServer:x,onSort:v,persistTableHead:N,sortFunction:j,...C}=e,D={paginationComponentOptions:i,noDataComponent:s("NoData"),noHeader:!0,columns:o,data:d||[],dense:!0,paginationResetDefaultPage:p,subHeader:c,progressPending:S,subHeaderComponent:m,pagination:!0,paginationServer:_,paginationPerPage:y||10,paginationRowsPerPageOptions:g||[10,15,20,25,30],paginationTotalRows:l,onChangeRowsPerPage:u,onChangePage:A,selectableRows:h,onSelectedRowsChange:w,clearSelectedRows:f,progressComponent:(0,t.jsx)(r.A,{}),sortIcon:(0,t.jsx)("i",{className:"sort-icon fas fa-exchange-alt"}),sortServer:x,onSort:v,sortFunction:j,persistTableHead:N,className:"rki-table"};return(0,t.jsx)(a.Ay,{...D})}o.defaultProps={subHeader:!1,pagination:!0,totalRows:null,pagServer:!0,onSelectedRowsChange:null,clearSelectedRows:!1,sortServer:!1,persistTableHead:!1};let d=o},55216:(e,s,i)=>{i.r(s),i.d(s,{default:()=>a});var t=i(37876);function a(e){return(0,t.jsx)("div",{className:"container-fluid p-0 response-message-block",children:(0,t.jsx)("div",{className:"message",children:"you don't have permission to access"})})}},69600:(e,s,i)=>{i.d(s,{A:()=>a});var t=i(37876);function a(e){return(0,t.jsx)("h2",{className:"page-heading",children:e.title})}},81749:(e,s,i)=>{i.r(s),i.d(s,{default:()=>A});var t=i(37876),a=i(48230),n=i.n(a),r=i(14232),o=i(31195),d=i(60282),l=i(97685),p=i(50749),c=i(53718),m=i(143),u=i(31753);function A(e){let{t:s}=(0,u.Bd)("common"),[i,a]=(0,r.useState)([]),[A,g]=(0,r.useState)(!1),[y,h]=(0,r.useState)(0),[S,_]=(0,r.useState)(10),[w,f]=(0,r.useState)(10),[x,v]=(0,r.useState)(!1),[N,j]=(0,r.useState)({}),[C,D]=r.useState(""),[P,E]=r.useState(!1),[b,R]=(0,r.useState)({}),k={sort:{created_at:"desc"},limit:w,page:1,query:{}},U=[{name:s("adminsetting.user.table.Username"),selector:"username",cell:e=>e.username},{name:s("adminsetting.user.table.Email"),selector:"email",cell:e=>e.email},{name:s("adminsetting.user.table.Role"),selector:"role",cell:e=>e.roles?e.roles:""},{name:s("adminsetting.user.table.Organisation"),selector:"institution",cell:e=>e.institution?e.institution.title:""},{name:s("adminsetting.user.table.Action"),selector:"",cell:e=>(0,t.jsx)(t.Fragment,{children:e.isEdit?(0,t.jsxs)("div",{children:[(0,t.jsx)(n(),{href:"/adminsettings/[...routes]",as:"/adminsettings/edit_user/".concat(e._id),children:(0,t.jsx)("i",{className:"icon fas fa-edit"})}),"\xa0",(0,t.jsx)("a",{onClick:()=>z(e),children:(0,t.jsx)("i",{className:"icon fas fa-trash-alt"})})]}):""})}],H=async e=>{g(!0);let s=await c.A.get("/users",e);if(s&&Array.isArray(s.data)){var i;(null==(i=b.roles)?void 0:i.includes("SUPER_ADMIN"))?s.data.map(e=>e.isEdit=!0):(s.data.filter(e=>e.roles.includes("SUPER_ADMIN")).map(e=>e.isEdit=!1),s.data.filter(e=>!e.roles.includes("SUPER_ADMIN")).map(e=>e.isEdit=!0)),a(s.data),h(s.totalCount),g(!1),T(s.data)}},O=async(e,s)=>{k.limit=e,k.page=s,g(!0);let i=await c.A.get("/users",k);if(i&&Array.isArray(i.data)){var t;(null==(t=b.roles)?void 0:t.includes("SUPER_ADMIN"))?i.data.map(e=>e.isEdit=!0):(i.data.filter(e=>e.roles.includes("SUPER_ADMIN")).map(e=>e.isEdit=!1),i.data.filter(e=>!e.roles.includes("SUPER_ADMIN")).map(e=>e.isEdit=!0)),a(i.data),f(e),g(!1)}};(0,r.useEffect)(()=>{H(k)},[]);let T=async e=>{let s=await c.A.post("/users/getLoggedUser",{});s&&s.username&&(R(s),console.log(e),s.roles.includes("SUPER_ADMIN")?e.map(e=>e.isEdit=!0):(e.filter(e=>e.roles.includes("SUPER_ADMIN")).map(e=>e.isEdit=!1),e.filter(e=>!e.roles.includes("SUPER_ADMIN")).map(e=>e.isEdit=!0)),a(e),_(e))},z=async e=>{j(e._id),v(!0)},I=async()=>{try{await c.A.remove("/users/".concat(N)),H(k),v(!1),l.Ay.success(s("adminsetting.user.table.userDeletedSuccessfully"))}catch(e){l.Ay.error(s("adminsetting.user.table.errorDeletingUser"))}},M=()=>v(!1),q=r.useMemo(()=>{let e=e=>{e&&(RegExp("^[^@]+@[^@]+\\.[^@]+$").test(e.toLowerCase())?k.query={email:e}:k.query={username:e}),H(k),k.query={}},s=()=>{e(C)},i=()=>{s()};return(0,t.jsx)(m.default,{onFilter:s=>{s&&s.label?(D(s.label),e(s.label)):(k.query={},D(""),H(k))},onClear:()=>{C&&(E(!P),D(""))},filterText:C,onHandleSearch:s,onKeyPress:e=>{"Enter"===e.key&&i()}})},[C]);return(0,t.jsxs)("div",{children:[(0,t.jsxs)(o.A,{show:x,onHide:M,children:[(0,t.jsx)(o.A.Header,{closeButton:!0,children:(0,t.jsx)(o.A.Title,{children:s("adminsetting.user.table.DeleteUser")})}),(0,t.jsx)(o.A.Body,{children:s("adminsetting.user.table.Areyousurewanttodeletethisuser?")}),(0,t.jsxs)(o.A.Footer,{children:[(0,t.jsx)(d.A,{variant:"secondary",onClick:M,children:s("adminsetting.user.table.Cancel")}),(0,t.jsx)(d.A,{variant:"primary",onClick:I,children:s("adminsetting.user.table.Yes")})]})]}),(0,t.jsx)(p.A,{columns:e.trim&&"actions"===e.trim?U.slice(0,-1):U,data:i,totalRows:y,loading:A,subheader:!0,pagServer:!0,resetPaginationToggle:P,subHeaderComponent:q,handlePerRowsChange:O,handlePageChange:e=>{k.limit=w,k.page=e,H(k)}})]})}},86798:(e,s,i)=>{i.r(s),i.d(s,{default:()=>y});var t=i(37876),a=i(49589),n=i(56970),r=i(37784),o=i(60282),d=i(48230),l=i.n(d),p=i(69600),c=i(81749),m=i(31753),u=i(21546),A=i(31777),g=i(55216);let y=e=>{var s,i;let{t:d}=(0,m.Bd)("common"),y=()=>(0,t.jsxs)(a.A,{style:{overflowX:"hidden"},fluid:!0,className:"p-0",children:[(0,t.jsx)(n.A,{children:(0,t.jsx)(r.A,{xs:12,children:(0,t.jsx)(p.A,{title:d("adminsetting.user.form.Users")})})}),(0,t.jsx)(n.A,{children:(0,t.jsx)(r.A,{xs:12,children:(0,t.jsx)(l(),{href:"/adminsettings/[...routes]",as:"/adminsettings/create_user",children:(0,t.jsx)(o.A,{variant:"secondary",size:"sm",children:d("adminsetting.user.form.AddUser")})})})}),(0,t.jsx)(n.A,{className:"mt-3",children:(0,t.jsx)(r.A,{xs:12,children:(0,t.jsx)(c.default,{})})})]}),h=(0,u.canAddUsers)(()=>(0,t.jsx)(y,{})),S=(0,A.d4)(e=>e);return(null==S||null==(i=S.permissions)||null==(s=i.users)?void 0:s["create:any"])?(0,t.jsx)(h,{}):(0,t.jsx)(g.default,{})}}}]);
//# sourceMappingURL=6798-cd8493213a33a66d.js.map