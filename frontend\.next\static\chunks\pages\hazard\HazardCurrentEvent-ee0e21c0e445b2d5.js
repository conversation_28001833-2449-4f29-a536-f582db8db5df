(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[986],{29335:(e,a,r)=>{"use strict";r.d(a,{A:()=>C});var s=r(15039),t=r.n(s),d=r(14232),l=r(77346),c=r(37876);let n=d.forwardRef((e,a)=>{let{className:r,bsPrefix:s,as:d="div",...n}=e;return s=(0,l.oU)(s,"card-body"),(0,c.jsx)(d,{ref:a,className:t()(r,s),...n})});n.displayName="CardBody";let i=d.forwardRef((e,a)=>{let{className:r,bsPrefix:s,as:d="div",...n}=e;return s=(0,l.oU)(s,"card-footer"),(0,c.jsx)(d,{ref:a,className:t()(r,s),...n})});i.displayName="CardFooter";var o=r(81764);let m=d.forwardRef((e,a)=>{let{bsPrefix:r,className:s,as:n="div",...i}=e,m=(0,l.oU)(r,"card-header"),f=(0,d.useMemo)(()=>({cardHeaderBsPrefix:m}),[m]);return(0,c.jsx)(o.A.Provider,{value:f,children:(0,c.jsx)(n,{ref:a,...i,className:t()(s,m)})})});m.displayName="CardHeader";let f=d.forwardRef((e,a)=>{let{bsPrefix:r,className:s,variant:d,as:n="img",...i}=e,o=(0,l.oU)(r,"card-img");return(0,c.jsx)(n,{ref:a,className:t()(d?"".concat(o,"-").concat(d):o,s),...i})});f.displayName="CardImg";let u=d.forwardRef((e,a)=>{let{className:r,bsPrefix:s,as:d="div",...n}=e;return s=(0,l.oU)(s,"card-img-overlay"),(0,c.jsx)(d,{ref:a,className:t()(r,s),...n})});u.displayName="CardImgOverlay";let N=d.forwardRef((e,a)=>{let{className:r,bsPrefix:s,as:d="a",...n}=e;return s=(0,l.oU)(s,"card-link"),(0,c.jsx)(d,{ref:a,className:t()(r,s),...n})});N.displayName="CardLink";var x=r(46052);let h=(0,x.A)("h6"),j=d.forwardRef((e,a)=>{let{className:r,bsPrefix:s,as:d=h,...n}=e;return s=(0,l.oU)(s,"card-subtitle"),(0,c.jsx)(d,{ref:a,className:t()(r,s),...n})});j.displayName="CardSubtitle";let v=d.forwardRef((e,a)=>{let{className:r,bsPrefix:s,as:d="p",...n}=e;return s=(0,l.oU)(s,"card-text"),(0,c.jsx)(d,{ref:a,className:t()(r,s),...n})});v.displayName="CardText";let y=(0,x.A)("h5"),p=d.forwardRef((e,a)=>{let{className:r,bsPrefix:s,as:d=y,...n}=e;return s=(0,l.oU)(s,"card-title"),(0,c.jsx)(d,{ref:a,className:t()(r,s),...n})});p.displayName="CardTitle";let w=d.forwardRef((e,a)=>{let{bsPrefix:r,className:s,bg:d,text:i,border:o,body:m=!1,children:f,as:u="div",...N}=e,x=(0,l.oU)(r,"card");return(0,c.jsx)(u,{ref:a,...N,className:t()(s,x,d&&"bg-".concat(d),i&&"text-".concat(i),o&&"border-".concat(o)),children:m?(0,c.jsx)(n,{children:f}):f})});w.displayName="Card";let C=Object.assign(w,{Img:f,Title:p,Subtitle:j,Body:n,Link:N,Text:v,Header:m,Footer:i,ImgOverlay:u})},42332:(e,a,r)=>{(window.__NEXT_P=window.__NEXT_P||[]).push(["/hazard/HazardCurrentEvent",function(){return r(49487)}])},49487:(e,a,r)=>{"use strict";r.r(a),r.d(a,{default:()=>n});var s=r(37876),t=r(29335),d=r(48230),l=r.n(d),c=r(31753);let n=e=>{let a=e.hazardCurrentEventData,{t:r}=(0,c.Bd)("common");return(0,s.jsx)(s.Fragment,{children:(0,s.jsx)("div",{className:"rki-carousel-card",children:(0,s.jsxs)(t.A,{className:"infoCard",children:[(0,s.jsx)(t.A.Header,{className:"text-center",children:r("hazardshow.currentevents")}),(0,s.jsx)(t.A.Body,{className:"hazardBody",children:a&&a.length>0?a.map((e,a)=>(0,s.jsx)("ul",{className:"ulItems",children:(0,s.jsxs)("li",{className:"liItems",children:[(0,s.jsx)(l(),{href:"/event/[...routes]",as:"/event/show/".concat(e._id),children:e&&e.title?"".concat(e.title):""},e._id),(0,s.jsxs)("span",{children:[" ","(",e&&e.country?"".concat(e.country.title):"",")"]})]},a)})):(0,s.jsx)("span",{className:"text-center",children:r("noRecordFound")})})]})})})}},81764:(e,a,r)=>{"use strict";r.d(a,{A:()=>t});let s=r(14232).createContext(null);s.displayName="CardHeaderContext";let t=s}},e=>{var a=a=>e(e.s=a);e.O(0,[636,6593,8792],()=>a(42332)),_N_E=e.O()}]);
//# sourceMappingURL=HazardCurrentEvent-ee0e21c0e445b2d5.js.map