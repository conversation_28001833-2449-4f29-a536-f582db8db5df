{"c": ["webpack"], "r": ["pages/declarationform/[...routes]"], "m": ["(pages-dir-browser)/./node_modules/next/dist/build/webpack/loaders/next-client-pages-loader.js?absolutePagePath=D%3A%5Crki%5Cfrontend%5Cpages%5Cdeclarationform%5C%5B...routes%5D.tsx&page=%2Fdeclarationform%2F%5B...routes%5D!", "(pages-dir-browser)/./node_modules/react-bootstrap/esm/Alert.js", "(pages-dir-browser)/./node_modules/react-bootstrap/esm/AlertHeading.js", "(pages-dir-browser)/./node_modules/react-bootstrap/esm/AlertLink.js", "(pages-dir-browser)/./pages/declarationform/[...routes].tsx", "(pages-dir-browser)/./pages/declarationform/declarationform.tsx", "(pages-dir-browser)/./pages/declarationform/invalidLink.tsx", "(pages-dir-browser)/./pages/profile/confirmation.tsx", "(pages-dir-browser)/__barrel_optimize__?names=<PERSON><PERSON>,<PERSON><PERSON>,<PERSON>,Col,Form,Row!=!./node_modules/react-bootstrap/esm/index.js", "(pages-dir-browser)/__barrel_optimize__?names=Button,Card!=!./node_modules/react-bootstrap/esm/index.js"]}