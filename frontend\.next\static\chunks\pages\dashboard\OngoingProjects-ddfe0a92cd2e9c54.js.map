{"version": 3, "file": "static/chunks/pages/dashboard/OngoingProjects-ddfe0a92cd2e9c54.js", "mappings": "8OAgBA,SAASA,EAAUC,CAAqB,EACtC,GAAM,MAAEC,CAAI,CAAE,CAAGD,SACjB,EAASE,MAAM,CAAG,EAEd,UAACC,EAAAA,CAASA,CAAAA,UACPF,EAAKG,GAAG,CAAC,CAACC,EAAWC,IAElB,UAACH,EAAAA,CAASA,CAACI,IAAI,WAEb,UAACC,IAAIA,CAACC,KAAK,uBAAuBC,GAAI,aAAjCF,IAA2D,OAATH,EAAKM,GAAG,WAE5DN,EAAKO,KAAK,IAHRN,MAYV,IACT,CASA,SAASO,EAAYb,CAAuB,EAC1C,GAAM,SAAEc,CAAO,CAAE,CAAGd,EACpB,MACE,UAACQ,IAAIA,CACHC,KAAK,uBACLC,GAAI,aAFDF,IAE6B,OAAXM,EAAQC,EAAE,EAC/BC,UAAU,6BAEV,UAACC,OAAAA,CAAKD,UAAU,8BAAsBF,EAAQI,IAAI,IAIxD,CA+EA,MAxEA,SAASC,CAA2C,EAClD,GAAM,CAACC,CAAC,WAuEKD,WAvEHE,CAAoB,CAAC,CAAGrB,CAuENmB,CAtEtBG,CAsEuB,CAtEVF,EAAE,mBACf,CAACN,EAASS,EAAW,CAAGC,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAA4C,CAAEN,KAAM,GAAIH,GAAI,GAAId,KAAM,EAAE,GACxG,CAACwB,EAASC,EAAW,CAAGF,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,EAAC,GAEjCG,EAAiB,KACrBJ,EAAW,CAAEL,KAAME,EAAE,sBAAuBL,GAAI,GAAId,KAAM,EAAE,EAC9D,EAEM2B,EAAgB,UACpB,IAAMC,EAAgB,CACpBC,MAAO,CAAEC,OAAQ,EAAE,EACnBC,KAAM,CAAEC,WAAY,MAAO,EAC3BC,MAAO,GACPC,OAAQ,sRACV,EACMC,EAAgB,MAAMC,IAC5B,GAAID,EAAU,CACZP,EAAcC,KAAK,CAACC,MAAM,CAAGK,EAE7B,GAAI,CACFV,GAAW,GACX,IAAMY,EAAW,MAAMC,EAAAA,CAAUA,CAACC,GAAG,CAAC,WAAYX,GAClDH,GAAW,GACPe,MAAMC,OAAO,CAACJ,EAASK,IAAI,GAAKL,EAASK,IAAI,CAACzC,MAAM,CAAG,GAAG,EACjD,CAAEgB,KAAMoB,EAASK,IAAI,CAAC,EAAE,CAAC/B,KAAK,CAAEG,GAAIuB,EAASK,IAAI,CAAC,EAAE,CAAChC,GAAG,CAAEV,KAAMqC,EAASK,IAAI,GACxFtB,EAAqBiB,EAASK,IAAI,GAElChB,GAEJ,CAAE,MAAOiB,EAAG,CACVjB,GACF,CACF,MACEA,CADK,EAGT,EAEMU,EAAqB,UACzB,IAAMQ,EAAW,MAAMN,EAAAA,CAAUA,CAACC,GAAG,CAAC,kBACtC,GAAIK,GAAYA,EAASF,IAAI,EAAIE,EAASF,IAAI,CAACzC,MAAM,CAAG,EAAG,CACzD,IAAMkC,EAAkB,EAAE,CAM1B,OALAU,IAAAA,OAAS,CAACD,EAASF,IAAI,CAAE,SAAUtC,CAAS,EACvB,WAAW,CAA1BA,EAAKO,KAAK,EACZwB,EAASW,IAAI,CAAC1C,EAAKM,GAAG,CAE1B,GACOyB,CACT,CACA,OAAO,CACT,EAEAY,CAAAA,EAAAA,EAAAA,SAAAA,CAASA,CAAC,KACRpB,GACF,EAAG,EAAE,EAEL,IAAM3B,EAAO,CACXgD,QAAS3B,EACTJ,KAAM,UAACnB,EAAAA,CAAUE,KAAMa,EAAQb,IAAI,EACrC,EAEA,MACE,UAACiD,EAAAA,CAAOA,CAAAA,CACNC,gBAAiB,uBACjBlD,KAAMA,EACNmD,OAAQ9B,EACRJ,KAAMO,EAAU,UAAC4B,EAAAA,CAAeA,CAAAA,CAAAA,GAAM,UAACxC,EAAAA,CAAYC,QAASA,KAGlE,0GCjIA,IAAMwC,EAAwBC,EAAAA,OAAb,GAA6B,CAAC,GAK5CC,IALyB,IAAoB,WAC9CxC,CAAS,UACTyC,CAAQ,CACR/C,GAAIgD,EAAY,KAAK,CACrB,GAAG1D,EACJ,GAEC,OADAyD,EAAWE,CAAAA,EAAAA,EAAAA,EAAAA,CAAkBA,CAACF,EAAU,aACpBG,CAAAA,EAAAA,EAAAA,GAAAA,CAAIA,CAACF,EAAW,CAClCF,IAAKA,EACLxC,UAAW6C,IAAW7C,EAAWyC,GACjC,GAAGzD,CAAK,EAEZ,GACAsD,EAASQ,WAAW,CAAG,WCbvB,IAAMC,EAA0BR,EAAAA,SAAb,CAA6B,CAAC,GAK9CC,MAL2B,EAAoB,WAChDxC,CAAS,UACTyC,CAAQ,CACR/C,GAAIgD,EAAY,KAAK,CACrB,GAAG1D,EACJ,GAEC,OADAyD,EAAWE,CAAAA,EAAAA,EAAAA,EAAAA,CAAkBA,CAACF,EAAU,eACpBG,CAAAA,EAAAA,EAAAA,GAAAA,CAAIA,CAACF,EAAW,CAClCF,IAAKA,EACLxC,UAAW6C,IAAW7C,EAAWyC,GACjC,GAAGzD,CAAK,EAEZ,GACA+D,EAAWD,WAAW,CAAG,4BCXzB,IAAME,EAA0BT,EAAAA,SAAb,CAA6B,CAAC,GAM9CC,MAN2B,EAAoB,UAChDC,CAAQ,WACRzC,CAAS,CAETN,CADA,EACIgD,EAAY,KAAK,CACrB,GAAG1D,EACJ,GACOiE,EAASN,CAAAA,EAAAA,EAAAA,EAAAA,CAAkBA,CAACF,EAAU,eACtCS,EAAeC,CAAAA,EAAAA,EAAAA,OAAAA,CAAOA,CAAC,IAAO,EAClCC,mBAAoBH,EACtB,EAAI,CAACA,EAAO,EACZ,MAAoBL,CAAb,EAAaA,EAAAA,GAAAA,CAAIA,CAACS,EAAAA,CAAiBA,CAACC,QAAQ,CAAE,CACnDC,MAAOL,EACPM,SAAuBZ,CAAb,EAAaA,EAAAA,GAAAA,CAAIA,CAACF,EAAP,CACnBF,IAAKA,EACL,GAAGxD,CAAK,CACRgB,UAAW6C,IAAW7C,EAAWiD,EACnC,EACF,EACF,GACAD,EAAWF,GAJgBD,QAIL,CAAG,aCtBzB,IAAMY,EAAuBlB,EAAAA,MAAb,IAA6B,CAC7C,CACA,EAMGC,GARwB,KAE1B,UACCC,CAAQ,WACRzC,CAAS,SACT0D,CAAO,CACPhE,GAAIgD,EAAY,KAAK,CACrB,GAAG1D,EACJ,GACOiE,EAASN,CAAAA,EAAAA,EAAAA,EAAAA,CAAkBA,CAACF,EAAU,YAC5C,MAAoBG,CAAb,EAAaA,EAAAA,GAAAA,CAAIA,CAACF,EAAP,CAChBF,IAAKA,EACLxC,UAAW6C,IAAWa,EAAU,GAAaA,MAAAA,CAAVT,EAAO,EAArBJ,GAAgC,OAARa,CAX0G,EAW9FT,EAAQjD,GACjE,GAAGhB,CAAK,EAEZ,EACAyE,GAAQX,WAAW,CAAG,UChBtB,IAAMa,EAA8BpB,EAAAA,UAAgB,CAAC,EAA9B,CAKpBC,QALmD,EAApB,SAChCxC,CAAS,UACTyC,CAAQ,CACR/C,GAAIgD,EAAY,KAAK,CACrB,GAAG1D,EACJ,GAEC,OAAO,EADI2D,CAAAA,EAAAA,EAAAA,EAAAA,CAAkBA,CACX,EADsB,oBACpBC,CAAAA,EAAAA,EAAAA,GAAAA,CAAIA,CAACF,EAAW,CAClCF,IAAKA,EACLxC,UAAW6C,IAAW7C,EAAWyC,GACjC,GAAGzD,CAAK,EAEZ,GACA2E,EAJyBd,WAIC,CAAG,iBCb7B,IAAMe,EAAwBrB,EAAAA,OAAb,GAA6B,CAAC,GAK5CC,IALyB,IAAoB,CAC9CxC,WAAS,UACTyC,CAAQ,CACR/C,GAAIgD,EAAY,GAAG,CACnB,GAAG1D,EACJ,GAEC,OADAyD,EAAWE,CAAAA,EAAAA,EAAAA,EAAAA,CAAkBA,CACX,EADsB,aACpBC,CAAAA,EAAAA,EAAAA,GAAAA,CAAIA,CAACF,EAAW,CAClCF,IAAKA,EACLxC,UAAW6C,IAAW7C,EAAWyC,GACjC,GAAGzD,CAAK,EAEZ,GACA4E,EAJyBf,WAIL,CAAG,0BCZvB,IAAMgB,EAAgBC,CAAAA,EAAAA,EAAAA,CAAAA,CAAgBA,CAAC,MACjCC,EAA4BxB,EAAAA,UAAgB,CAA7B,CAA8B,EAKhDC,QAL6B,WAC9BxC,CAAS,UACTyC,CAAQ,CACR/C,GAAIgD,EAAYmB,CAAa,CAC7B,GAAG7E,EACJ,GAEC,OADAyD,EAAWE,CAAAA,EAAAA,EAAAA,EAAAA,CAAkBA,CAACF,EAAU,iBACpBG,CAAAA,EAAAA,EAAAA,GAAAA,CAAIA,CAACF,EAAW,CAClCF,IAAKA,EACLxC,UAAW6C,IAAW7C,EAAWyC,GACjC,GAAGzD,CAAK,EAEZ,GACA+E,EAJyBlB,WAID,CAAG,eCf3B,IAAMmB,EAAwBzB,EAAAA,OAAb,GAA6B,CAAC,GAK5CC,IALyB,IAAoB,WAC9CxC,CAAS,UACTyC,CAAQ,CACR/C,GAAIgD,EAAY,GAAG,CACnB,GAAG1D,EACJ,GAEC,OADAyD,EAAWE,CAAAA,EAAAA,EAAAA,EAAAA,CAAkBA,CAACF,EAAU,aACpBG,CAAAA,EAAAA,EAAAA,GAAAA,CAAIA,CAACF,EAAW,CAClCF,IAAKA,EACLxC,UAAW6C,IAAW7C,EAAWyC,GACjC,GAAGzD,CAAK,EAEZ,GACAgF,EAJyBnB,WAIL,CAAG,WCZvB,IAAMoB,EAAgBH,CAAAA,EAAAA,EAAAA,CAAAA,CAAgBA,CAAC,MACjCI,EAAyB3B,EAAAA,QAAb,EAA6B,CAAC,GAK7CC,KAL0B,GAAoB,WAC/CxC,CAAS,UACTyC,CAAQ,CACR/C,GAAIgD,EAAYuB,CAAa,CAC7B,GAAGjF,EACJ,GAEC,OADAyD,EAAWE,CAAAA,EAAAA,EAAAA,EAAAA,CAAkBA,CAACF,EAAU,cACpBG,CAAAA,EAAAA,EAAAA,GAAAA,CAAIA,CAACF,EAAW,CAClCF,IAAKA,EACLxC,UAAW6C,IAAW7C,EAAWyC,GACjC,GAAGzD,CAAK,EAEZ,GACAkF,EAJyBrB,WAIJ,CAAG,YCNxB,IAAMsB,EAAoB5B,EAAAA,GAAb,OAA6B,CAAC,GAAnB,QAAoB,CAC1CE,UAAQ,CACRzC,WAAS,IACToE,CAAE,MACFC,CAAI,QACJC,CAAM,CACNpE,QAAO,CAAK,UACZsD,CAAQ,CAER9D,CADA,EACIgD,EAAY,KAAK,CACrB,GAAG1D,EACJ,GACOiE,EAASN,CAAAA,EAAAA,EAAAA,EAAAA,CAAkBA,CAACF,EAAU,QAC5C,MAAoBG,CAAb,EAAaA,EAAAA,GAAAA,CAAIA,CAACF,EAAW,CAClCF,IAAKA,EACL,GAAGxD,CAAK,CACRgB,UAAW6C,IAAW7C,EAAWiD,EAAQmB,GAAM,MAAS,GAAnCvB,GAAmC,CAAHuB,GAAMC,GAAQ,QAAa,OAALA,GAAQC,GAAU,UAAiB,OAAPA,IACvGd,IATyJ,KAS/ItD,EAAoB0C,CAAAA,EAAAA,EAAAA,GAAAA,CAAIA,CAACN,EAAU,CAC3CkB,GAD0B,MAAelB,CAE3C,GAAKkB,CACP,EACF,EACAW,GAAKrB,WAAW,CAAG,OACnB,MAAeyB,OAAOC,MAAM,CAACL,EAAM,CACjCM,INhBahB,CMgBRA,CACLiB,KNjBoBjB,CKDPS,CCkBNA,CACPS,EAFYlB,KDjBUS,EAAC,CCmBbH,CACVa,CAFgBV,ITpBH5B,CSsBPA,CACN9C,GHrByBuE,EDFZH,CLAQtB,CSwBrBuC,CTxBsB,GSsBRvC,CFtBD0B,CEwBPA,CACNc,CJzBsB,GIuBRlB,EFvBOI,CLSRhB,COgBLA,CACR+B,EAFcf,KRxBDjB,CQ0BLA,CACRiC,CPlBwB,GOgBNhC,IRzBKD,EAAC,CGAXY,CK2BDA,CADMZ,CAElB,EAAC,SL5B0BY,EAAC,GK2BFA,EC9C5B,4CACA,6BACA,WACA,OAAe,EAAQ,KAAkD,CACzE,EACA,SAFsB,sJCJtB,uDAcA,SACA,EAAuB,QAAQ,cAC/B,EAAyB,YAAgB,SACzC,IAeA,IAfA,CAEA,WACA,WACA,YACA,OACA,YACA,CAAM,EACN,WAxBA,KAA+C,oBAA0B,SAAY,sBAAuB,2BAA8B,4BAAiC,UAAe,UAwB1L,KAGA,EAAsB,OAAc,GACpC,EAA0B,YAAM,KAChC,EAAyB,gBAAU,CAAC,GAAiB,EACrD,EAAqB,gBAAU,CAAC,GAAU,EAE1C,IACA,eACA,cAEA,oBACA,qBAEA,MAAmB,YAAM,OACzB,MACA,gBACA,kBACA,MAAkB,OAAG,OAAsB,EAAe,8BAC1D,0CACA,8CACA,mBACA,sBACA,UAGA,OAFA,mBACA,oBACA,MAEA,UACA,UACA,gBACA,gBACA,EAyBE,eAAS,MACX,yBACA,kCAA6D,EAAe,uBAC5E,mBACA,CACA,YACA,CAAG,EACH,MAAoB,OAAa,MACjC,MAAsB,SAAI,CAAC,GAAiB,WAC5C,QACA,SAA2B,SAAI,CAAC,GAAU,WAC1C,OACA,OAEA,UAAmB,OAAY,IAC/B,qBACA,oBACA,CAAO,CACP,SAA6B,SAAI,mBAA4B,IAC7D,UA3CA,QAKA,EAHA,GADA,cACA,GAIA,cACA,gBACA,cACA,QACA,KACA,kBACA,gBACA,OACA,KACA,SACA,MACA,CACA,IACA,mBACA,YAAyC,OAAQ,uBACjD,aACA,KACA,EAqBA,MACA,MACA,CAAO,EACP,CAAK,CACL,CAAG,CACH,CAAC,EACD,oBACA,MAAe,iBACf,KAAQ,GAAO,CACd,CAAC,gEC/FF,aAQA,MAPA,8BACA,iCAAiD,IAAO,IAExD,aADA,eACA,uDAEA,QACA,GACA,qBACA,EAoBA,cACA,0UAAwzB,2EAnBxzB,cACA,SACA,0EACA,YACA,4DACA,8CAA6D,WAAc,IAC3E,uEACA,kBAEA,QACA,EASwzB,qOACxzB,KAPA,cACA,aACA,aAMA,YACA,qBACA,YAEA,MAAuB,EANvB,mBAMuB,GAAkB,GACzC,EAPA,mBAOA,IAEA,MAAY,mBAAa,UAAmB,2CAAmE,CAT/G,cAAwzB,GAAxzB,cAKA,CAA2B,wBAA0B,KAI0D,CAAqB,IACpI,EAAgB,mBAAa,UAAY,KAAY,SACrD,GAAsB,oBAAc,WAC5B,mBAAa,SAAW,8FAA+H,yBAAmD,EAC1M,mBAAa,aACT,mBAAa,aAAe,KAAY,IACxC,mBAAa,mBAAqB,uBAP9C,eARA,4BAQA,mBAO8C,CAAsD,CACpF,mBAAa,SAAW,sCAA0E,IAAc,mBAAa,YAAc,oCAAqD,QAAwB,qDAAkF,GAC1S,mBAAa,SAAW,uBAjBxC,oBAiBwC,YAjBxC,cAiBwC,CAA2E,IAAc,mBAAa,YAAc,sCAAyD,UAA4B,KACjP,uDAA0H,GAC1G,mBAAa,SAAW,wCAA4E,IAAc,mBAAa,YAAc,kCAAqC,GAAG,0DAAuG,KAC5S,EAEA,cACA,kBAA4B,mBAAa,OAAiB,KAAY,mBAAa,OAAwC,IAC3H,EAEA,cAAoD,MAAQ,mBAAa,MAA2B,sBAAwB,IACxH,mBAAa,SAAW,0CAAoD,EAC5E,mBAAa,SAAW,2CAAqD,EAC7E,mBAAa,SAAW,2CAAqD,EAC7E,mBAAa,SAAW,2CAAqD,EAC7E,mBAAa,SAAW,2CAAqD,EAC7E,mBAAa,WAAa,uBAA6B,IAoC3D,MAAe,aAAa,EAAC,8JCzG7B,IAAMsB,EAA6B1C,EAAAA,UAAgB,CAAC,CAA9B,EAUnBC,QAVkD,CAApB,SAC/BC,CAAQ,QACRyC,CAAM,UACNC,CAAQ,UACRC,CAAQ,CACRpF,WAAS,CACT0D,SAAO,QACP2B,CAAM,IACN3F,CAAE,CACF,GAAGV,EACJ,GACCyD,EAAWE,CAAAA,EAAAA,EAAAA,EAAAA,CAAkBA,CAACF,EAAU,mBACxC,GAAM,CAAC6C,EAAcC,EAAK,CAAGC,CAAAA,EAAAA,EAAAA,CAAAA,CAAUA,CAAC,CACtCC,IAAKC,CAAAA,EAAAA,EAAAA,CAAAA,CAAYA,CAACN,EAAUpG,EAAMS,IAAI,SACtCyF,EACA,GAAGlG,CAAK,GAEJ2G,EAAcC,CAAAA,EAAAA,EAAAA,CAAAA,CAAgBA,CAACC,IACnC,GAAIV,EAAU,CACZU,EAAMC,cAAc,GACpBD,EAAME,eAAe,GACrB,MACF,CACAT,EAAaU,OAAO,CAACH,EACvB,GACIV,GAAYnG,KAAmBiH,MAAbC,KAAwB,GAAhB,GAC5BlH,EAAMkH,QAAQ,CAAG,CAAC,EAClBlH,CAAK,CAAC,gBAAgB,EAAG,GAE3B,IAAM0D,EAAYhD,IAAO2F,EAAAA,EAAe5F,IAAI,CAAG,IAAM,SAAW,MAAI,CAEpE,MAAoBmD,CAAAA,EAAAA,EAAAA,GAAAA,CAAIA,CAACF,EAAW,CAClCF,IAAKA,EACL,GAAGxD,CAAK,CACR,GAAGsG,CAAY,CACfU,QAASL,EACT3F,UAAW6C,IAAW7C,EAAWyC,EAAU8C,EAAKY,QAAQ,EAAnCtD,SAAiDsC,GAAY,WAAYzB,GAAW,GAAeA,MAAAA,CAAZjB,EAAS,KAAW,OAARiB,GAAW2B,GAAU,GAAY,OAAT5C,EAAS,WAC3J,EACF,EACAwC,GAAcnC,WAAW,CAAG,gBCvC5B,IAAM3D,EAAyBoD,EAAAA,QAAb,EAA6B,CAAC,CAACvD,EAAOwD,KACtD,IAaI4D,EAbE,WACJpG,CAAS,CACTyC,SAAU4D,CAAe,SACzB3C,CAAO,YACP4C,CAAU,UACVC,CAAQ,EACR,EACA7G,EAAK,KAAK,CACV,GAAG8G,EACJ,CAAGC,CAAAA,EAAAA,EAAAA,EAAAA,CAAeA,CAACzH,EAAO,CACzB0H,UAAW,UACb,GACMjE,EAAWE,CAAAA,EAAAA,EAAAA,EAAAA,CAAkBA,CAAC0D,EAAiB,cAMrD,OAJIC,GACFF,IAAmC,IAGnB,CAJF,CAC4B,aAAe,cAAyB,OAAXE,EAAAA,EAGrD1D,CAAAA,EAAAA,EAAAA,GAAAA,CAAIA,CAAC+D,EAAAA,CAAOA,CAAE,CAChCnE,IAb2J,EAc3J,GAAGgE,CAAe,CAClB9G,GAAIA,EACJM,UAAW6C,IAAW7C,EAAWyC,EAAUiB,GAAW,GAAeA,MAAAA,CAAZjB,EAAS,KAAW,OAARiB,GAAW0C,GAAqB,GAAeA,MAAAA,CAAZ3D,EAAS,KAAqB,OAAlB2D,GAAqBG,GAAY,GAAY,OAAT9D,EAAS,aACnK,EACF,GACAtD,EAAU2D,WAAW,CAAG,YACxB,MAAeyB,OAAOC,MAAM,CAACrF,EAAW,CACtCI,KDYa0F,CCZPA,EACN,EAAC,QDWyBA,EAAC,GCZRA,iCCnCrB,IAAM2B,EAAuBrE,QAAb,CAAaA,UAAF,GAAqB,CAAC,MACjDqE,EAAQ9D,WAAW,CAAG,oBACtB,MAAe8D,OAAOA,EAAC,kECDR,SAASvE,IACtB,MACE,WAACwE,EAAAA,EAAaA,CAAAA,CACZC,QAAQ,aACRC,OAAQ,GACRC,MAAO,IACPC,MAAO,EACPrH,MAAO,UACPsH,gBAAgB,UAChBC,gBAAgB,UAChBC,UAAW,sBAEX,UAACC,OAAAA,CAAKC,EAAE,KAAKC,EAAE,IAAIC,GAAG,IAAIC,GAAG,IAAIT,MAAM,MAAMD,OAAO,OACpD,UAACM,OAAAA,CAAKC,EAAE,KAAKC,EAAE,KAAKC,GAAG,IAAIC,GAAG,IAAIT,MAAM,MAAMD,OAAO,SAG3D,iGCLA,SAASW,EAAU1I,CAAqB,EACtC,GAAM,MAAEC,CAAI,iBAAEkD,CAAe,CAAE,CAAGnD,EAClC,MACE,WAAC2I,EAAAA,CAAKA,CAAAA,CACH,GAAG3I,CAAK,CACTmD,gBAAiBA,EACjByF,kBAAgB,gCAChBC,QAAQ,cAER,UAACF,EAAAA,CAAKA,CAAC7C,MAAM,EAACgD,WAAW,aACvB,UAACH,EAAAA,CAAKA,CAACjD,KAAK,EAAC3E,GAAG,yCACbd,EAAKgD,OAAO,KAGjB,UAAC0F,EAAAA,CAAKA,CAAC/C,IAAI,WACR3F,EAAKiB,IAAI,KAIlB,CAUA,SAAS6C,EAAW/D,CAAsB,EACxC,GAAM,MAAEC,CAAI,CAAE,CAAGD,EACX,CAAC+I,EAAWC,EAAa,CAAGzF,EAAAA,QAAc,EAAC,UACjD,GAAYtD,EAAKiB,IAAI,CAEjB,CAFmB,EAEnB,8BACE,UAAC+H,SAAAA,CAAOC,KAAK,SAASlC,QAAS,IAAMgC,GAAa,GAAOG,MAAO,CAAE7D,OAAQ,OAAQ8D,WAAY,OAAQC,QAAS,CAAE,WAC/G,UAAClE,EAAAA,CAAIA,CAACY,MAAM,WACV,UAACuD,IAAAA,CAAEtI,UAAU,4BAGhBhB,EAAMC,IAAI,EAAI,UAACyI,EAAAA,CAAUzI,KAAMD,EAAMC,IAAI,CAAEsJ,KAAMR,EAAWS,OAAQ,IAAMR,GAAa,GAAQ7F,gBAAiBnD,EAAMmD,eAAe,MAIrI,IACT,CA4BA,MAhBA,SAASD,CAA2B,EAClC,GAAM,QAAEE,CAAM,CAAElC,GAeHgC,GAfO,CAAE,CAAGlD,EAEzB,EAaqB,IAZnB,WAACmF,EAAAA,CAAIA,CAAAA,CAACnE,UAAU,iCACd,UAACmE,EAAAA,CAAIA,CAACW,MAAM,WAAE1C,IACd,UAAC+B,EAAAA,CAAIA,CAACS,IAAI,WACR,UAACT,EAAAA,CAAIA,CAACU,IAAI,WACP3E,MAGL,UAAC6C,EAAAA,CAAY,GAAG/D,CAAK,KAG3B", "sources": ["webpack://_N_E/./pages/dashboard/OngoingProjects.tsx", "webpack://_N_E/./node_modules/react-bootstrap/esm/CardBody.js", "webpack://_N_E/./node_modules/react-bootstrap/esm/CardFooter.js", "webpack://_N_E/./node_modules/react-bootstrap/esm/CardHeader.js", "webpack://_N_E/./node_modules/react-bootstrap/esm/CardImg.js", "webpack://_N_E/./node_modules/react-bootstrap/esm/CardImgOverlay.js", "webpack://_N_E/./node_modules/react-bootstrap/esm/CardLink.js", "webpack://_N_E/./node_modules/react-bootstrap/esm/CardSubtitle.js", "webpack://_N_E/./node_modules/react-bootstrap/esm/CardText.js", "webpack://_N_E/./node_modules/react-bootstrap/esm/CardTitle.js", "webpack://_N_E/./node_modules/react-bootstrap/esm/Card.js", "webpack://_N_E/?a991", "webpack://_N_E/./node_modules/@restart/ui/esm/Nav.js", "webpack://_N_E/./node_modules/react-content-loader/dist/react-content-loader.es.js", "webpack://_N_E/./node_modules/react-bootstrap/esm/ListGroupItem.js", "webpack://_N_E/./node_modules/react-bootstrap/esm/ListGroup.js", "webpack://_N_E/./node_modules/react-bootstrap/esm/CardHeaderContext.js", "webpack://_N_E/./components/common/placeholders/CardPlaceholder.tsx", "webpack://_N_E/./components/common/RKICard.tsx"], "sourcesContent": ["//Import Library\r\nimport React, { useEffect, useState } from 'react';\r\nimport _ from \"lodash\";\r\nimport Link from \"next/link\";\r\nimport { ListGroup } from 'react-bootstrap';\r\n\r\n//Import services/components\r\nimport RKICard from \"../../components/common/RKICard\";\r\nimport CardPlaceholder from \"../../components/common/placeholders/CardPlaceholder\";\r\nimport apiService from \"../../services/apiService\";\r\n\r\n\r\ninterface ListItemsProps {\r\n  list: any[];\r\n}\r\n\r\nfunction ListItems(props: ListItemsProps) {\r\n  const { list } = props;\r\n  if (list.length > 0) {\r\n    return (\r\n      <ListGroup>\r\n        {list.map((item: any, index: number) => {\r\n          return (\r\n            <ListGroup.Item\r\n              key={index}>\r\n              <Link href=\"/project/[...routes]\" as={`/project/show/${item._id}`}>\r\n\r\n                {item.title}\r\n\r\n              </Link>\r\n            </ListGroup.Item>\r\n          );\r\n        })}\r\n      </ListGroup>\r\n    );\r\n  }\r\n  return null;\r\n}\r\n\r\ninterface CardDetailsProps {\r\n  project: {\r\n    body: string;\r\n    id: string;\r\n  };\r\n}\r\n\r\nfunction CardDetails(props: CardDetailsProps) {\r\n  const { project } = props;\r\n  return (\r\n    <Link\r\n      href='/project/[...routes]'\r\n      as={`/project/show/${project.id}`}\r\n      className='active-op-project'>\r\n\r\n      <span className=\"project-title link\">{project.body}</span>\r\n\r\n    </Link>\r\n  );\r\n}\r\n\r\ninterface OngoingProjectsProps {\r\n  t: (key: string) => string;\r\n  fetchOngoingProjects: (projects: any[]) => void;\r\n}\r\n\r\nfunction OngoingProjects(props: OngoingProjectsProps) {\r\n  const {t, fetchOngoingProjects} = props;\r\n  const cardHeader = t(\"OngoingProjects\");\r\n  const [project, setProject] = useState<{ body: string; id: string; list: any[] }>({ body: \"\", id: \"\", list: [] });\r\n  const [loading, setLoading] = useState(true);\r\n\r\n  const setEmptyNotice = () => {\r\n    setProject({ body: t(\"NoProjectavailable\"), id: \"\", list: [] })\r\n  };\r\n\r\n  const fetchProjects = async () => {\r\n    const projectParams = {\r\n      query: { status: [] },\r\n      sort: { created_at: \"desc\" },\r\n      limit: 10,\r\n      select: \"-website -description -funded_by -status -start_date -end_date -region -area_of_work -institution_invites -vspace -vspace_visibility -user -created_at -updated_at -partner_institutions.partner_region -partner_institutions.partner_institution -partner_institutions.world_region\"\r\n    };\r\n    const statusId: any = await fetchProjectStatus();\r\n    if (statusId) {\r\n      projectParams.query.status = statusId;\r\n\r\n      try {\r\n        setLoading(true);\r\n        const projects = await apiService.get('/project', projectParams);\r\n        setLoading(false);\r\n        if (Array.isArray(projects.data) && projects.data.length > 0) {\r\n          setProject({ body: projects.data[0].title, id: projects.data[0]._id, list: projects.data })\r\n          fetchOngoingProjects(projects.data);\r\n        } else {\r\n          setEmptyNotice()\r\n        }\r\n      } catch (e) {\r\n        setEmptyNotice()\r\n      }\r\n    } else {\r\n      setEmptyNotice()\r\n    }\r\n  };\r\n\r\n  const fetchProjectStatus = async () => {\r\n    const response = await apiService.get('/projectStatus');\r\n    if (response && response.data && response.data.length > 0) {\r\n      const statusId: any[] = []\r\n      _.forEach(response.data, function (item: any) {\r\n        if (item.title === \"Ongoing\") {\r\n          statusId.push(item._id);\r\n        }\r\n      });\r\n      return statusId;\r\n    }\r\n    return false;\r\n  };\r\n\r\n  useEffect(() => {\r\n    fetchProjects();\r\n  }, []);\r\n\r\n  const list = {\r\n    heading: cardHeader,\r\n    body: <ListItems list={project.list} />\r\n  };\r\n\r\n  return (\r\n    <RKICard\r\n      dialogClassName={\"ongoing-project-list\"}\r\n      list={list}\r\n      header={cardHeader}\r\n      body={loading ? <CardPlaceholder /> : <CardDetails project={project} />}\r\n    />\r\n  )\r\n}\r\n\r\nexport default OngoingProjects;", "\"use client\";\n\nimport * as React from 'react';\nimport classNames from 'classnames';\nimport { useBootstrapPrefix } from './ThemeProvider';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst CardBody = /*#__PURE__*/React.forwardRef(({\n  className,\n  bsPrefix,\n  as: Component = 'div',\n  ...props\n}, ref) => {\n  bsPrefix = useBootstrapPrefix(bsPrefix, 'card-body');\n  return /*#__PURE__*/_jsx(Component, {\n    ref: ref,\n    className: classNames(className, bsPrefix),\n    ...props\n  });\n});\nCardBody.displayName = 'CardBody';\nexport default CardBody;", "\"use client\";\n\nimport * as React from 'react';\nimport classNames from 'classnames';\nimport { useBootstrapPrefix } from './ThemeProvider';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst CardFooter = /*#__PURE__*/React.forwardRef(({\n  className,\n  bsPrefix,\n  as: Component = 'div',\n  ...props\n}, ref) => {\n  bsPrefix = useBootstrapPrefix(bsPrefix, 'card-footer');\n  return /*#__PURE__*/_jsx(Component, {\n    ref: ref,\n    className: classNames(className, bsPrefix),\n    ...props\n  });\n});\nCardFooter.displayName = 'CardFooter';\nexport default CardFooter;", "\"use client\";\n\nimport classNames from 'classnames';\nimport * as React from 'react';\nimport { useMemo } from 'react';\nimport { useBootstrapPrefix } from './ThemeProvider';\nimport CardHeaderContext from './CardHeaderContext';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst CardHeader = /*#__PURE__*/React.forwardRef(({\n  bsPrefix,\n  className,\n  // Need to define the default \"as\" during prop destructuring to be compatible with styled-components github.com/react-bootstrap/react-bootstrap/issues/3595\n  as: Component = 'div',\n  ...props\n}, ref) => {\n  const prefix = useBootstrapPrefix(bsPrefix, 'card-header');\n  const contextValue = useMemo(() => ({\n    cardHeaderBsPrefix: prefix\n  }), [prefix]);\n  return /*#__PURE__*/_jsx(CardHeaderContext.Provider, {\n    value: contextValue,\n    children: /*#__PURE__*/_jsx(Component, {\n      ref: ref,\n      ...props,\n      className: classNames(className, prefix)\n    })\n  });\n});\nCardHeader.displayName = 'CardHeader';\nexport default CardHeader;", "\"use client\";\n\nimport classNames from 'classnames';\nimport * as React from 'react';\nimport { useBootstrapPrefix } from './ThemeProvider';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst CardImg = /*#__PURE__*/React.forwardRef(\n// Need to define the default \"as\" during prop destructuring to be compatible with styled-components github.com/react-bootstrap/react-bootstrap/issues/3595\n({\n  bsPrefix,\n  className,\n  variant,\n  as: Component = 'img',\n  ...props\n}, ref) => {\n  const prefix = useBootstrapPrefix(bsPrefix, 'card-img');\n  return /*#__PURE__*/_jsx(Component, {\n    ref: ref,\n    className: classNames(variant ? `${prefix}-${variant}` : prefix, className),\n    ...props\n  });\n});\nCardImg.displayName = 'CardImg';\nexport default CardImg;", "\"use client\";\n\nimport * as React from 'react';\nimport classNames from 'classnames';\nimport { useBootstrapPrefix } from './ThemeProvider';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst CardImgOverlay = /*#__PURE__*/React.forwardRef(({\n  className,\n  bsPrefix,\n  as: Component = 'div',\n  ...props\n}, ref) => {\n  bsPrefix = useBootstrapPrefix(bsPrefix, 'card-img-overlay');\n  return /*#__PURE__*/_jsx(Component, {\n    ref: ref,\n    className: classNames(className, bsPrefix),\n    ...props\n  });\n});\nCardImgOverlay.displayName = 'CardImgOverlay';\nexport default CardImgOverlay;", "\"use client\";\n\nimport * as React from 'react';\nimport classNames from 'classnames';\nimport { useBootstrapPrefix } from './ThemeProvider';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst CardLink = /*#__PURE__*/React.forwardRef(({\n  className,\n  bsPrefix,\n  as: Component = 'a',\n  ...props\n}, ref) => {\n  bsPrefix = useBootstrapPrefix(bsPrefix, 'card-link');\n  return /*#__PURE__*/_jsx(Component, {\n    ref: ref,\n    className: classNames(className, bsPrefix),\n    ...props\n  });\n});\nCardLink.displayName = 'CardLink';\nexport default CardLink;", "\"use client\";\n\nimport * as React from 'react';\nimport classNames from 'classnames';\nimport { useBootstrapPrefix } from './ThemeProvider';\nimport divWithClassName from './divWithClassName';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst DivStyledAsH6 = divWithClassName('h6');\nconst CardSubtitle = /*#__PURE__*/React.forwardRef(({\n  className,\n  bsPrefix,\n  as: Component = DivStyledAsH6,\n  ...props\n}, ref) => {\n  bsPrefix = useBootstrapPrefix(bsPrefix, 'card-subtitle');\n  return /*#__PURE__*/_jsx(Component, {\n    ref: ref,\n    className: classNames(className, bsPrefix),\n    ...props\n  });\n});\nCardSubtitle.displayName = 'CardSubtitle';\nexport default CardSubtitle;", "\"use client\";\n\nimport * as React from 'react';\nimport classNames from 'classnames';\nimport { useBootstrapPrefix } from './ThemeProvider';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst CardText = /*#__PURE__*/React.forwardRef(({\n  className,\n  bsPrefix,\n  as: Component = 'p',\n  ...props\n}, ref) => {\n  bsPrefix = useBootstrapPrefix(bsPrefix, 'card-text');\n  return /*#__PURE__*/_jsx(Component, {\n    ref: ref,\n    className: classNames(className, bsPrefix),\n    ...props\n  });\n});\nCardText.displayName = 'CardText';\nexport default CardText;", "\"use client\";\n\nimport * as React from 'react';\nimport classNames from 'classnames';\nimport { useBootstrapPrefix } from './ThemeProvider';\nimport divWithClassName from './divWithClassName';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst DivStyledAsH5 = divWithClassName('h5');\nconst CardTitle = /*#__PURE__*/React.forwardRef(({\n  className,\n  bsPrefix,\n  as: Component = DivStyledAsH5,\n  ...props\n}, ref) => {\n  bsPrefix = useBootstrapPrefix(bsPrefix, 'card-title');\n  return /*#__PURE__*/_jsx(Component, {\n    ref: ref,\n    className: classNames(className, bsPrefix),\n    ...props\n  });\n});\nCardTitle.displayName = 'CardTitle';\nexport default CardTitle;", "\"use client\";\n\nimport classNames from 'classnames';\nimport * as React from 'react';\nimport { useBootstrapPrefix } from './ThemeProvider';\nimport CardBody from './CardBody';\nimport CardFooter from './CardFooter';\nimport CardHeader from './CardHeader';\nimport CardImg from './CardImg';\nimport CardImgOverlay from './CardImgOverlay';\nimport CardLink from './CardLink';\nimport CardSubtitle from './CardSubtitle';\nimport CardText from './CardText';\nimport CardTitle from './CardTitle';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst Card = /*#__PURE__*/React.forwardRef(({\n  bsPrefix,\n  className,\n  bg,\n  text,\n  border,\n  body = false,\n  children,\n  // Need to define the default \"as\" during prop destructuring to be compatible with styled-components github.com/react-bootstrap/react-bootstrap/issues/3595\n  as: Component = 'div',\n  ...props\n}, ref) => {\n  const prefix = useBootstrapPrefix(bsPrefix, 'card');\n  return /*#__PURE__*/_jsx(Component, {\n    ref: ref,\n    ...props,\n    className: classNames(className, prefix, bg && `bg-${bg}`, text && `text-${text}`, border && `border-${border}`),\n    children: body ? /*#__PURE__*/_jsx(CardBody, {\n      children: children\n    }) : children\n  });\n});\nCard.displayName = 'Card';\nexport default Object.assign(Card, {\n  Img: CardImg,\n  Title: CardTitle,\n  Subtitle: CardSubtitle,\n  Body: CardBody,\n  Link: CardLink,\n  Text: CardText,\n  Header: CardHeader,\n  Footer: CardFooter,\n  ImgOverlay: CardImgOverlay\n});", "\n    (window.__NEXT_P = window.__NEXT_P || []).push([\n      \"/dashboard/OngoingProjects\",\n      function () {\n        return require(\"private-next-pages/dashboard/OngoingProjects.tsx\");\n      }\n    ]);\n    if(module.hot) {\n      module.hot.dispose(function () {\n        window.__NEXT_P.push([\"/dashboard/OngoingProjects\"])\n      });\n    }\n  ", "const _excluded = [\"as\", \"onSelect\", \"activeKey\", \"role\", \"onKeyDown\"];\nfunction _objectWithoutPropertiesLoose(r, e) { if (null == r) return {}; var t = {}; for (var n in r) if ({}.hasOwnProperty.call(r, n)) { if (e.indexOf(n) >= 0) continue; t[n] = r[n]; } return t; }\nimport qsa from 'dom-helpers/querySelectorAll';\nimport * as React from 'react';\nimport { useContext, useEffect, useRef } from 'react';\nimport useForceUpdate from '@restart/hooks/useForceUpdate';\nimport useMergedRefs from '@restart/hooks/useMergedRefs';\nimport NavContext from './NavContext';\nimport SelectableContext, { makeEventKey } from './SelectableContext';\nimport TabContext from './TabContext';\nimport { dataAttr, dataProp } from './DataKey';\nimport NavItem from './NavItem';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\n// eslint-disable-next-line @typescript-eslint/no-empty-function\nconst noop = () => {};\nconst EVENT_KEY_ATTR = dataAttr('event-key');\nconst Nav = /*#__PURE__*/React.forwardRef((_ref, ref) => {\n  let {\n      // Need to define the default \"as\" during prop destructuring to be compatible with styled-components github.com/react-bootstrap/react-bootstrap/issues/3595\n      as: Component = 'div',\n      onSelect,\n      activeKey,\n      role,\n      onKeyDown\n    } = _ref,\n    props = _objectWithoutPropertiesLoose(_ref, _excluded);\n  // A ref and forceUpdate for refocus, b/c we only want to trigger when needed\n  // and don't want to reset the set in the effect\n  const forceUpdate = useForceUpdate();\n  const needsRefocusRef = useRef(false);\n  const parentOnSelect = useContext(SelectableContext);\n  const tabContext = useContext(TabContext);\n  let getControlledId, getControllerId;\n  if (tabContext) {\n    role = role || 'tablist';\n    activeKey = tabContext.activeKey;\n    // TODO: do we need to duplicate these?\n    getControlledId = tabContext.getControlledId;\n    getControllerId = tabContext.getControllerId;\n  }\n  const listNode = useRef(null);\n  const getNextActiveTab = offset => {\n    const currentListNode = listNode.current;\n    if (!currentListNode) return null;\n    const items = qsa(currentListNode, `[${EVENT_KEY_ATTR}]:not([aria-disabled=true])`);\n    const activeChild = currentListNode.querySelector('[aria-selected=true]');\n    if (!activeChild || activeChild !== document.activeElement) return null;\n    const index = items.indexOf(activeChild);\n    if (index === -1) return null;\n    let nextIndex = index + offset;\n    if (nextIndex >= items.length) nextIndex = 0;\n    if (nextIndex < 0) nextIndex = items.length - 1;\n    return items[nextIndex];\n  };\n  const handleSelect = (key, event) => {\n    if (key == null) return;\n    onSelect == null ? void 0 : onSelect(key, event);\n    parentOnSelect == null ? void 0 : parentOnSelect(key, event);\n  };\n  const handleKeyDown = event => {\n    onKeyDown == null ? void 0 : onKeyDown(event);\n    if (!tabContext) {\n      return;\n    }\n    let nextActiveChild;\n    switch (event.key) {\n      case 'ArrowLeft':\n      case 'ArrowUp':\n        nextActiveChild = getNextActiveTab(-1);\n        break;\n      case 'ArrowRight':\n      case 'ArrowDown':\n        nextActiveChild = getNextActiveTab(1);\n        break;\n      default:\n        return;\n    }\n    if (!nextActiveChild) return;\n    event.preventDefault();\n    handleSelect(nextActiveChild.dataset[dataProp('EventKey')] || null, event);\n    needsRefocusRef.current = true;\n    forceUpdate();\n  };\n  useEffect(() => {\n    if (listNode.current && needsRefocusRef.current) {\n      const activeChild = listNode.current.querySelector(`[${EVENT_KEY_ATTR}][aria-selected=true]`);\n      activeChild == null ? void 0 : activeChild.focus();\n    }\n    needsRefocusRef.current = false;\n  });\n  const mergedRef = useMergedRefs(ref, listNode);\n  return /*#__PURE__*/_jsx(SelectableContext.Provider, {\n    value: handleSelect,\n    children: /*#__PURE__*/_jsx(NavContext.Provider, {\n      value: {\n        role,\n        // used by NavLink to determine it's role\n        activeKey: makeEventKey(activeKey),\n        getControlledId: getControlledId || noop,\n        getControllerId: getControllerId || noop\n      },\n      children: /*#__PURE__*/_jsx(Component, Object.assign({}, props, {\n        onKeyDown: handleKeyDown,\n        ref: mergedRef,\n        role: role\n      }))\n    })\n  });\n});\nNav.displayName = 'Nav';\nexport default Object.assign(Nav, {\n  Item: NavItem\n});", "import { createElement, isValidElement } from 'react';\n\n/*! *****************************************************************************\r\nCopyright (c) Microsoft Corporation. All rights reserved.\r\nLicensed under the Apache License, Version 2.0 (the \"License\"); you may not use\r\nthis file except in compliance with the License. You may obtain a copy of the\r\nLicense at http://www.apache.org/licenses/LICENSE-2.0\r\n\r\nTHIS CODE IS PROVIDED ON AN *AS IS* BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\nKIND, EITHER EXPRESS OR IMPLIED, INCLUDING WITHOUT LIMITATION ANY IMPLIED\r\nWARRANTIES OR CONDITIONS OF TITLE, FITNESS FOR A PARTICULAR PURPOSE,\r\nMERCHANTABLITY OR NON-INFRINGEMENT.\r\n\r\nSee the Apache Version 2.0 License for specific language governing permissions\r\nand limitations under the License.\r\n***************************************************************************** */\r\n\r\nvar __assign = function() {\r\n    __assign = Object.assign || function __assign(t) {\r\n        for (var s, i = 1, n = arguments.length; i < n; i++) {\r\n            s = arguments[i];\r\n            for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p)) t[p] = s[p];\r\n        }\r\n        return t;\r\n    };\r\n    return __assign.apply(this, arguments);\r\n};\r\n\r\nfunction __rest(s, e) {\r\n    var t = {};\r\n    for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0)\r\n        t[p] = s[p];\r\n    if (s != null && typeof Object.getOwnPropertySymbols === \"function\")\r\n        for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {\r\n            if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i]))\r\n                t[p[i]] = s[p[i]];\r\n        }\r\n    return t;\r\n}\n\nvar uid = (function () {\r\n    return Math.random()\r\n        .toString(36)\r\n        .substring(6);\r\n});\n\nvar SVG = function (_a) {\r\n    var _b = _a.animate, animate = _b === void 0 ? true : _b, animateBegin = _a.animateBegin, _c = _a.backgroundColor, backgroundColor = _c === void 0 ? '#f5f6f7' : _c, _d = _a.backgroundOpacity, backgroundOpacity = _d === void 0 ? 1 : _d, _e = _a.baseUrl, baseUrl = _e === void 0 ? '' : _e, children = _a.children, _f = _a.foregroundColor, foregroundColor = _f === void 0 ? '#eee' : _f, _g = _a.foregroundOpacity, foregroundOpacity = _g === void 0 ? 1 : _g, _h = _a.gradientRatio, gradientRatio = _h === void 0 ? 2 : _h, _j = _a.gradientDirection, gradientDirection = _j === void 0 ? 'left-right' : _j, uniqueKey = _a.uniqueKey, _k = _a.interval, interval = _k === void 0 ? 0.25 : _k, _l = _a.rtl, rtl = _l === void 0 ? false : _l, _m = _a.speed, speed = _m === void 0 ? 1.2 : _m, _o = _a.style, style = _o === void 0 ? {} : _o, _p = _a.title, title = _p === void 0 ? 'Loading...' : _p, _q = _a.beforeMask, beforeMask = _q === void 0 ? null : _q, props = __rest(_a, [\"animate\", \"animateBegin\", \"backgroundColor\", \"backgroundOpacity\", \"baseUrl\", \"children\", \"foregroundColor\", \"foregroundOpacity\", \"gradientRatio\", \"gradientDirection\", \"uniqueKey\", \"interval\", \"rtl\", \"speed\", \"style\", \"title\", \"beforeMask\"]);\r\n    var fixedId = uniqueKey || uid();\r\n    var idClip = fixedId + \"-diff\";\r\n    var idGradient = fixedId + \"-animated-diff\";\r\n    var idAria = fixedId + \"-aria\";\r\n    var rtlStyle = rtl ? { transform: 'scaleX(-1)' } : null;\r\n    var keyTimes = \"0; \" + interval + \"; 1\";\r\n    var dur = speed + \"s\";\r\n    var gradientTransform = gradientDirection === 'top-bottom' ? 'rotate(90)' : undefined;\r\n    return (createElement(\"svg\", __assign({ \"aria-labelledby\": idAria, role: \"img\", style: __assign(__assign({}, style), rtlStyle) }, props),\r\n        title ? createElement(\"title\", { id: idAria }, title) : null,\r\n        beforeMask && isValidElement(beforeMask) ? beforeMask : null,\r\n        createElement(\"rect\", { role: \"presentation\", x: \"0\", y: \"0\", width: \"100%\", height: \"100%\", clipPath: \"url(\" + baseUrl + \"#\" + idClip + \")\", style: { fill: \"url(\" + baseUrl + \"#\" + idGradient + \")\" } }),\r\n        createElement(\"defs\", null,\r\n            createElement(\"clipPath\", { id: idClip }, children),\r\n            createElement(\"linearGradient\", { id: idGradient, gradientTransform: gradientTransform },\r\n                createElement(\"stop\", { offset: \"0%\", stopColor: backgroundColor, stopOpacity: backgroundOpacity }, animate && (createElement(\"animate\", { attributeName: \"offset\", values: -gradientRatio + \"; \" + -gradientRatio + \"; 1\", keyTimes: keyTimes, dur: dur, repeatCount: \"indefinite\", begin: animateBegin }))),\r\n                createElement(\"stop\", { offset: \"50%\", stopColor: foregroundColor, stopOpacity: foregroundOpacity }, animate && (createElement(\"animate\", { attributeName: \"offset\", values: -gradientRatio / 2 + \"; \" + -gradientRatio / 2 + \"; \" + (1 +\r\n                        gradientRatio / 2), keyTimes: keyTimes, dur: dur, repeatCount: \"indefinite\", begin: animateBegin }))),\r\n                createElement(\"stop\", { offset: \"100%\", stopColor: backgroundColor, stopOpacity: backgroundOpacity }, animate && (createElement(\"animate\", { attributeName: \"offset\", values: \"0; 0; \" + (1 + gradientRatio), keyTimes: keyTimes, dur: dur, repeatCount: \"indefinite\", begin: animateBegin })))))));\r\n};\n\nvar ContentLoader = function (props) {\r\n    return props.children ? createElement(SVG, __assign({}, props)) : createElement(ReactContentLoaderFacebook, __assign({}, props));\r\n};\n\nvar ReactContentLoaderFacebook = function (props) { return (createElement(ContentLoader, __assign({ viewBox: \"0 0 476 124\" }, props),\r\n    createElement(\"rect\", { x: \"48\", y: \"8\", width: \"88\", height: \"6\", rx: \"3\" }),\r\n    createElement(\"rect\", { x: \"48\", y: \"26\", width: \"52\", height: \"6\", rx: \"3\" }),\r\n    createElement(\"rect\", { x: \"0\", y: \"56\", width: \"410\", height: \"6\", rx: \"3\" }),\r\n    createElement(\"rect\", { x: \"0\", y: \"72\", width: \"380\", height: \"6\", rx: \"3\" }),\r\n    createElement(\"rect\", { x: \"0\", y: \"88\", width: \"178\", height: \"6\", rx: \"3\" }),\r\n    createElement(\"circle\", { cx: \"20\", cy: \"20\", r: \"20\" }))); };\n\nvar ReactContentLoaderInstagram = function (props) { return (createElement(ContentLoader, __assign({ viewBox: \"0 0 400 460\" }, props),\r\n    createElement(\"circle\", { cx: \"31\", cy: \"31\", r: \"15\" }),\r\n    createElement(\"rect\", { x: \"58\", y: \"18\", rx: \"2\", ry: \"2\", width: \"140\", height: \"10\" }),\r\n    createElement(\"rect\", { x: \"58\", y: \"34\", rx: \"2\", ry: \"2\", width: \"140\", height: \"10\" }),\r\n    createElement(\"rect\", { x: \"0\", y: \"60\", rx: \"2\", ry: \"2\", width: \"400\", height: \"400\" }))); };\n\nvar ReactContentLoaderCode = function (props) { return (createElement(ContentLoader, __assign({ viewBox: \"0 0 340 84\" }, props),\r\n    createElement(\"rect\", { x: \"0\", y: \"0\", width: \"67\", height: \"11\", rx: \"3\" }),\r\n    createElement(\"rect\", { x: \"76\", y: \"0\", width: \"140\", height: \"11\", rx: \"3\" }),\r\n    createElement(\"rect\", { x: \"127\", y: \"48\", width: \"53\", height: \"11\", rx: \"3\" }),\r\n    createElement(\"rect\", { x: \"187\", y: \"48\", width: \"72\", height: \"11\", rx: \"3\" }),\r\n    createElement(\"rect\", { x: \"18\", y: \"48\", width: \"100\", height: \"11\", rx: \"3\" }),\r\n    createElement(\"rect\", { x: \"0\", y: \"71\", width: \"37\", height: \"11\", rx: \"3\" }),\r\n    createElement(\"rect\", { x: \"18\", y: \"23\", width: \"140\", height: \"11\", rx: \"3\" }),\r\n    createElement(\"rect\", { x: \"166\", y: \"23\", width: \"173\", height: \"11\", rx: \"3\" }))); };\n\nvar ReactContentLoaderListStyle = function (props) { return (createElement(ContentLoader, __assign({ viewBox: \"0 0 400 110\" }, props),\r\n    createElement(\"rect\", { x: \"0\", y: \"0\", rx: \"3\", ry: \"3\", width: \"250\", height: \"10\" }),\r\n    createElement(\"rect\", { x: \"20\", y: \"20\", rx: \"3\", ry: \"3\", width: \"220\", height: \"10\" }),\r\n    createElement(\"rect\", { x: \"20\", y: \"40\", rx: \"3\", ry: \"3\", width: \"170\", height: \"10\" }),\r\n    createElement(\"rect\", { x: \"0\", y: \"60\", rx: \"3\", ry: \"3\", width: \"250\", height: \"10\" }),\r\n    createElement(\"rect\", { x: \"20\", y: \"80\", rx: \"3\", ry: \"3\", width: \"200\", height: \"10\" }),\r\n    createElement(\"rect\", { x: \"20\", y: \"100\", rx: \"3\", ry: \"3\", width: \"80\", height: \"10\" }))); };\n\nvar ReactContentLoaderBulletList = function (props) { return (createElement(ContentLoader, __assign({ viewBox: \"0 0 245 125\" }, props),\r\n    createElement(\"circle\", { cx: \"10\", cy: \"20\", r: \"8\" }),\r\n    createElement(\"rect\", { x: \"25\", y: \"15\", rx: \"5\", ry: \"5\", width: \"220\", height: \"10\" }),\r\n    createElement(\"circle\", { cx: \"10\", cy: \"50\", r: \"8\" }),\r\n    createElement(\"rect\", { x: \"25\", y: \"45\", rx: \"5\", ry: \"5\", width: \"220\", height: \"10\" }),\r\n    createElement(\"circle\", { cx: \"10\", cy: \"80\", r: \"8\" }),\r\n    createElement(\"rect\", { x: \"25\", y: \"75\", rx: \"5\", ry: \"5\", width: \"220\", height: \"10\" }),\r\n    createElement(\"circle\", { cx: \"10\", cy: \"110\", r: \"8\" }),\r\n    createElement(\"rect\", { x: \"25\", y: \"105\", rx: \"5\", ry: \"5\", width: \"220\", height: \"10\" }))); };\n\nexport default ContentLoader;\nexport { ReactContentLoaderBulletList as BulletList, ReactContentLoaderCode as Code, ReactContentLoaderFacebook as Facebook, ReactContentLoaderInstagram as Instagram, ReactContentLoaderListStyle as List };\n//# sourceMappingURL=react-content-loader.es.js.map\n", "\"use client\";\n\nimport classNames from 'classnames';\nimport * as React from 'react';\nimport warning from 'warning';\nimport useEventCallback from '@restart/hooks/useEventCallback';\nimport { useNavItem } from '@restart/ui/NavItem';\nimport { makeEventKey } from '@restart/ui/SelectableContext';\nimport { useBootstrapPrefix } from './ThemeProvider';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst ListGroupItem = /*#__PURE__*/React.forwardRef(({\n  bsPrefix,\n  active,\n  disabled,\n  eventKey,\n  className,\n  variant,\n  action,\n  as,\n  ...props\n}, ref) => {\n  bsPrefix = useBootstrapPrefix(bsPrefix, 'list-group-item');\n  const [navItemProps, meta] = useNavItem({\n    key: makeEventKey(eventKey, props.href),\n    active,\n    ...props\n  });\n  const handleClick = useEventCallback(event => {\n    if (disabled) {\n      event.preventDefault();\n      event.stopPropagation();\n      return;\n    }\n    navItemProps.onClick(event);\n  });\n  if (disabled && props.tabIndex === undefined) {\n    props.tabIndex = -1;\n    props['aria-disabled'] = true;\n  }\n  const Component = as || (action ? props.href ? 'a' : 'button' : 'div');\n  process.env.NODE_ENV !== \"production\" ? warning(as || !(!action && props.href), '`action=false` and `href` should not be used together.') : void 0;\n  return /*#__PURE__*/_jsx(Component, {\n    ref: ref,\n    ...props,\n    ...navItemProps,\n    onClick: handleClick,\n    className: classNames(className, bsPrefix, meta.isActive && 'active', disabled && 'disabled', variant && `${bsPrefix}-${variant}`, action && `${bsPrefix}-action`)\n  });\n});\nListGroupItem.displayName = 'ListGroupItem';\nexport default ListGroupItem;", "\"use client\";\n\nimport classNames from 'classnames';\nimport * as React from 'react';\nimport warning from 'warning';\nimport { useUncontrolled } from 'uncontrollable';\nimport BaseNav from '@restart/ui/Nav';\nimport { useBootstrapPrefix } from './ThemeProvider';\nimport ListGroupItem from './ListGroupItem';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst ListGroup = /*#__PURE__*/React.forwardRef((props, ref) => {\n  const {\n    className,\n    bsPrefix: initialBsPrefix,\n    variant,\n    horizontal,\n    numbered,\n    // Need to define the default \"as\" during prop destructuring to be compatible with styled-components github.com/react-bootstrap/react-bootstrap/issues/3595\n    as = 'div',\n    ...controlledProps\n  } = useUncontrolled(props, {\n    activeKey: 'onSelect'\n  });\n  const bsPrefix = useBootstrapPrefix(initialBsPrefix, 'list-group');\n  let horizontalVariant;\n  if (horizontal) {\n    horizontalVariant = horizontal === true ? 'horizontal' : `horizontal-${horizontal}`;\n  }\n  process.env.NODE_ENV !== \"production\" ? warning(!(horizontal && variant === 'flush'), '`variant=\"flush\"` and `horizontal` should not be used together.') : void 0;\n  return /*#__PURE__*/_jsx(BaseNav, {\n    ref: ref,\n    ...controlledProps,\n    as: as,\n    className: classNames(className, bsPrefix, variant && `${bsPrefix}-${variant}`, horizontalVariant && `${bsPrefix}-${horizontalVariant}`, numbered && `${bsPrefix}-numbered`)\n  });\n});\nListGroup.displayName = 'ListGroup';\nexport default Object.assign(ListGroup, {\n  Item: ListGroupItem\n});", "\"use client\";\n\nimport * as React from 'react';\nconst context = /*#__PURE__*/React.createContext(null);\ncontext.displayName = 'CardHeaderContext';\nexport default context;", "//Import Library\r\nimport ContentLoader from 'react-content-loader';\r\n\r\n// No props needed - component uses hardcoded values\r\nexport default function CardPlaceholder() {\r\n  return(\r\n    <ContentLoader\r\n      viewBox=\"0 0 380 70\"\r\n      height={50}\r\n      width={317}\r\n      speed={2}\r\n      title={'Loading'}\r\n      foregroundColor=\"#f7f7f7\"\r\n      backgroundColor=\"#ecebeb\"\r\n      uniqueKey={\"operation\"}\r\n    >\r\n      <rect x=\"10\" y=\"0\" rx=\"4\" ry=\"4\" width=\"320\" height=\"25\" />\r\n      <rect x=\"40\" y=\"40\" rx=\"3\" ry=\"3\" width=\"250\" height=\"20\" />\r\n    </ContentLoader>\r\n  )\r\n}\r\n", "//Import Library\r\nimport React from \"react\";\r\nimport { Card } from \"react-bootstrap\";\r\nimport Modal from \"react-bootstrap/Modal\";\r\n\r\ninterface ListModalProps {\r\n  list: {\r\n    heading: string;\r\n    body: React.ReactNode;\r\n  };\r\n  dialogClassName?: string;\r\n  show: boolean;\r\n  onHide: () => void;\r\n}\r\n\r\nfunction ListModal(props: ListModalProps) {\r\n  const { list, dialogClassName } = props;\r\n  return (\r\n    <Modal\r\n      {...props}\r\n      dialogClassName={dialogClassName}\r\n      aria-labelledby=\"contained-modal-title-vcenter\"\r\n      centered\r\n    >\r\n      <Modal.Header closeButton>\r\n        <Modal.Title id=\"contained-modal-title-vcenter\">\r\n          {list.heading}\r\n        </Modal.Title>\r\n      </Modal.Header>\r\n      <Modal.Body>\r\n        {list.body}\r\n      </Modal.Body>\r\n    </Modal>\r\n  )\r\n}\r\n\r\ninterface CardFooterProps {\r\n  list?: {\r\n    body: React.ReactNode;\r\n    heading: string;\r\n  };\r\n  dialogClassName?: string;\r\n}\r\n\r\nfunction CardFooter(props: CardFooterProps) {\r\n  const { list } = props;\r\n  const [modalShow, setModalShow] = React.useState(false);\r\n  if (list && list.body) {\r\n    return (\r\n      <>\r\n        <button type=\"button\" onClick={() => setModalShow(true)} style={{ border: 'none', background: 'none', padding: 0 }}>\r\n          <Card.Footer>\r\n            <i className=\"fas fa-chevron-down\" />\r\n          </Card.Footer>\r\n        </button>\r\n        {props.list && <ListModal list={props.list} show={modalShow} onHide={() => setModalShow(false)} dialogClassName={props.dialogClassName} />}\r\n      </>\r\n    )\r\n  }\r\n  return null;\r\n}\r\n\r\ninterface RKICardProps {\r\n  header: string;\r\n  body: React.ReactNode;\r\n  list?: {\r\n    body: React.ReactNode;\r\n    heading: string;\r\n  };\r\n  dialogClassName?: string;\r\n}\r\n\r\nfunction RKICard(props: RKICardProps) {\r\n  const { header, body } = props\r\n\r\n  return (\r\n    <Card className=\"text-center infoCard\">\r\n      <Card.Header>{header}</Card.Header>\r\n      <Card.Body>\r\n        <Card.Text>\r\n          {body}\r\n        </Card.Text>\r\n      </Card.Body>\r\n      <CardFooter {...props} />\r\n    </Card>\r\n  )\r\n}\r\n\r\nexport default RKICard;\r\n"], "names": ["ListItems", "props", "list", "length", "ListGroup", "map", "item", "index", "<PERSON><PERSON>", "Link", "href", "as", "_id", "title", "CardDetails", "project", "id", "className", "span", "body", "OngoingProjects", "t", "fetchOngoingProjects", "<PERSON><PERSON><PERSON><PERSON>", "setProject", "useState", "loading", "setLoading", "setEmptyNotice", "fetchProjects", "projectParams", "query", "status", "sort", "created_at", "limit", "select", "statusId", "fetchProjectStatus", "projects", "apiService", "get", "Array", "isArray", "data", "e", "response", "_", "push", "useEffect", "heading", "RKICard", "dialogClassName", "header", "CardPlaceholder", "CardBody", "React", "ref", "bsPrefix", "Component", "useBootstrapPrefix", "_jsx", "classNames", "displayName", "<PERSON><PERSON><PERSON>er", "<PERSON><PERSON><PERSON><PERSON>", "prefix", "contextValue", "useMemo", "cardHeaderBsPrefix", "CardHeaderContext", "Provider", "value", "children", "CardImg", "variant", "CardImgOverlay", "CardLink", "DivStyledAsH6", "divWithClassName", "CardSubtitle", "CardText", "DivStyledAsH5", "CardTitle", "Card", "bg", "text", "border", "Object", "assign", "Img", "Title", "Subtitle", "Body", "Text", "Header", "Footer", "ImgOverlay", "ListGroupItem", "active", "disabled", "eventKey", "action", "navItemProps", "meta", "useNavItem", "key", "makeEventKey", "handleClick", "useEventCallback", "event", "preventDefault", "stopPropagation", "onClick", "undefined", "tabIndex", "isActive", "horizontalVariant", "initialBsPrefix", "horizontal", "numbered", "controlledProps", "useUncontrolled", "active<PERSON><PERSON>", "BaseNav", "context", "ContentLoader", "viewBox", "height", "width", "speed", "foregroundColor", "backgroundColor", "<PERSON><PERSON><PERSON>", "rect", "x", "y", "rx", "ry", "ListModal", "Modal", "aria-<PERSON>by", "centered", "closeButton", "modalShow", "setModalShow", "button", "type", "style", "background", "padding", "i", "show", "onHide"], "sourceRoot": "", "ignoreList": [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 12, 13, 14, 15, 16]}