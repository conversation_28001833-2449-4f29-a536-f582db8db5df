"use strict";(()=>{var e={};e.id=7505,e.ids=[636,3220,7505],e.modules={123:e=>{e.exports=require("dom-helpers/removeEventListener")},1332:e=>{e.exports=require("react-custom-scrollbars-2")},1428:e=>{e.exports=import("axios")},1680:e=>{e.exports=require("@restart/ui/utils")},1919:e=>{e.exports=require("react-transition-group/Transition")},3892:e=>{e.exports=require("classnames")},4048:e=>{e.exports=require("react-truncate")},6009:e=>{e.exports=require("dom-helpers/ownerDocument")},6952:e=>{e.exports=require("@restart/ui/Modal")},7374:e=>{e.exports=require("@restart/ui/Dropdown")},8732:e=>{e.exports=require("react/jsx-runtime")},9653:e=>{e.exports=require("@restart/ui/Overlay")},11242:e=>{e.exports=require("redux-persist/integration/react")},11688:e=>{e.exports=require("@restart/hooks/useWillUnmount")},12388:e=>{e.exports=require("dom-helpers/addEventListener")},13364:e=>{e.exports=require("redux-saga/effects")},14062:e=>{e.exports=import("react-redux")},14078:e=>{e.exports=import("swr")},14332:e=>{e.exports=require("uncontrollable")},16116:e=>{e.exports=require("invariant")},18622:e=>{e.exports=require("yup")},19121:e=>{e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},21127:(e,r,t)=>{t.a(e,async(e,s)=>{try{t.r(r),t.d(r,{config:()=>g,default:()=>x,getServerSideProps:()=>h,getStaticPaths:()=>m,getStaticProps:()=>d,reportWebVitals:()=>q,routeModule:()=>A,unstable_getServerProps:()=>k,unstable_getServerSideProps:()=>P,unstable_getStaticParams:()=>j,unstable_getStaticPaths:()=>f,unstable_getStaticProps:()=>v});var i=t(63885),o=t(80237),n=t(81413),a=t(9616),u=t.n(a),p=t(72386),l=t(79029),c=e([p,l]);[p,l]=c.then?(await c)():c;let x=(0,n.M)(l,"default"),d=(0,n.M)(l,"getStaticProps"),m=(0,n.M)(l,"getStaticPaths"),h=(0,n.M)(l,"getServerSideProps"),g=(0,n.M)(l,"config"),q=(0,n.M)(l,"reportWebVitals"),v=(0,n.M)(l,"unstable_getStaticProps"),f=(0,n.M)(l,"unstable_getStaticPaths"),j=(0,n.M)(l,"unstable_getStaticParams"),k=(0,n.M)(l,"unstable_getServerProps"),P=(0,n.M)(l,"unstable_getServerSideProps"),A=new i.PagesRouteModule({definition:{kind:o.A.PAGES,page:"/event/components/RiskAssessmentAccordion",pathname:"/event/components/RiskAssessmentAccordion",bundlePath:"",filename:""},components:{App:p.default,Document:u()},userland:l});s()}catch(e){s(e)}})},22326:e=>{e.exports=require("react-dom")},22541:e=>{e.exports=require("redux-persist/lib/storage")},25303:e=>{e.exports=require("@restart/ui/NavItem")},26324:e=>{e.exports=require("warning")},27825:e=>{e.exports=require("lodash")},27910:e=>{e.exports=require("stream")},28217:e=>{e.exports=require("@restart/ui/DropdownItem")},29021:e=>{e.exports=require("fs")},29294:e=>{e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},29628:e=>{e.exports=require("dom-helpers/addClass")},29825:e=>{e.exports=require("prop-types")},29841:e=>{e.exports=require("dom-helpers/hasClass")},30362:e=>{e.exports=require("@restart/ui/DropdownToggle")},33873:e=>{e.exports=require("path")},36653:e=>{e.exports=require("nprogress")},36955:e=>{e.exports=require("@restart/hooks/useBreakpoint")},37766:e=>{e.exports=require("@restart/hooks/useMergedRefs")},39756:e=>{e.exports=import("redux")},40051:e=>{e.exports=require("dom-helpers/removeClass")},40361:e=>{e.exports=require("next/dist/compiled/next-server/pages.runtime.prod.js")},42893:e=>{e.exports=import("react-hot-toast")},43294:e=>{e.exports=require("formik")},50009:e=>{e.exports=require("@restart/ui/DropdownMenu")},54131:e=>{e.exports=import("@fortawesome/free-solid-svg-icons")},57664:e=>{e.exports=require("dom-helpers/scrollbarSize")},58928:e=>{e.exports=require("dom-helpers/canUseDOM")},59717:e=>{e.exports=require("@restart/ui/DropdownContext")},60560:e=>{e.exports=require("dom-helpers/contains")},63033:e=>{e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},63349:(e,r,t)=>{t.d(r,{A:()=>n});var s=t(8732),i=t(82015),o=t(88751);let n=e=>{let{t:r}=(0,o.useTranslation)("common"),t=parseInt("255"),[n,a]=(0,i.useState)(!1);return(0,s.jsxs)(s.Fragment,{children:[e.description?(0,s.jsx)("div",{dangerouslySetInnerHTML:((r,s)=>({__html:!s&&r.length>t?r.substring(0,t)+"...":e.description}))(e.description,n),className:"operationDesc"}):null,e.description&&e.description.length>t?(0,s.jsx)("button",{type:"button",className:"readMoreText",onClick:()=>a(!n),children:r(n?"readLess":"readMore")}):null]})}},65447:e=>{e.exports=require("@restart/ui/Button")},67364:e=>{e.exports=require("@restart/hooks/useCallbackRef")},68455:e=>{e.exports=import("redux-saga")},69722:e=>{e.exports=require("es6-promise")},74075:e=>{e.exports=require("zlib")},74716:e=>{e.exports=require("moment")},74987:e=>{e.exports=require("@restart/ui/ModalManager")},78097:e=>{e.exports=require("next-redux-saga")},78634:e=>{e.exports=require("@restart/ui/Anchor")},79029:(e,r,t)=>{t.a(e,async(e,s)=>{try{t.r(r),t.d(r,{default:()=>d});var i=t(8732),o=t(82015),n=t(54131),a=t(82053),u=t(93024),p=t(88751),l=t(63349),c=e([n]);n=(c.then?(await c)():c)[0];let x={Low:"risk0",Medium:"risk1",High:"risk2","Very High":"risk3"},d=e=>{let{t:r}=(0,p.useTranslation)("common"),[t,s]=(0,o.useState)(!1),{risk_assessment:c}=e;return(0,i.jsx)(i.Fragment,{children:(0,i.jsxs)(u.A.Item,{eventKey:"0",children:[(0,i.jsxs)(u.A.Header,{onClick:()=>s(!t),children:[(0,i.jsx)("div",{className:"cardTitle",children:r("Events.show.RiskAssessment")}),(0,i.jsx)("div",{className:"cardArrow",children:t?(0,i.jsx)(a.FontAwesomeIcon,{icon:n.faMinus,color:"#fff"}):(0,i.jsx)(a.FontAwesomeIcon,{icon:n.faPlus,color:"#fff"})})]}),(0,i.jsx)(u.A.Body,{children:c.country?(0,i.jsxs)("div",{children:[function(e,r){return(0,i.jsxs)("div",{className:"riskDetails",children:[e.country?(0,i.jsxs)("div",{className:"riskItems",children:[(0,i.jsx)("div",{className:`riskIcon ${x[e.country.title]}`,children:(0,i.jsx)("img",{src:"/images/event_country.png",width:"30",height:"30",alt:"Risk Assessment Info"})}),(0,i.jsxs)("div",{className:"riskInfo",children:[(0,i.jsx)("h5",{children:r("Events.show.Country")}),(0,i.jsx)("h4",{children:e.country.title})]})]}):(0,i.jsx)(i.Fragment,{}),e.region?(0,i.jsxs)("div",{className:"riskItems",children:[(0,i.jsx)("div",{className:`riskIcon ${e&&e.region?x[e.region.title]:""}`,children:(0,i.jsx)("img",{src:"/images/event_region.png",width:"35",height:"26",alt:"Risk Assessment Info"})}),(0,i.jsxs)("div",{className:"riskInfo",children:[(0,i.jsx)("h5",{children:r("Events.show.Region")}),(0,i.jsx)("h4",{children:e&&e.region?e.region.title:""})]})]}):(0,i.jsx)(i.Fragment,{}),e.international?(0,i.jsxs)("div",{className:"riskItems",children:[(0,i.jsx)("div",{className:`riskIcon ${e&&e.international?x[e.international.title]:""}`,children:(0,i.jsx)("img",{src:"/images/event_international.png",width:"38",height:"38",alt:"Risk Assessment Info"})}),(0,i.jsxs)("div",{className:"riskInfo",children:[(0,i.jsx)("h5",{children:r("Events.show.International")}),(0,i.jsx)("h4",{children:e&&e.international?e.international.title:""})]})]}):(0,i.jsx)(i.Fragment,{})]})}(c,r),function(e){return(0,i.jsx)("div",{className:"mt-4",children:(0,i.jsx)(l.A,{description:e&&e.risk_assessment.description?e.risk_assessment.description:""})})}(e)]}):null})]})})};s()}catch(e){s(e)}})},80237:(e,r)=>{Object.defineProperty(r,"A",{enumerable:!0,get:function(){return t}});var t=function(e){return e.PAGES="PAGES",e.PAGES_API="PAGES_API",e.APP_PAGE="APP_PAGE",e.APP_ROUTE="APP_ROUTE",e.IMAGE="IMAGE",e}({})},81366:e=>{e.exports=require("redux-auth-wrapper/connectedAuthWrapper")},81413:(e,r)=>{Object.defineProperty(r,"M",{enumerable:!0,get:function(){return function e(r,t){return t in r?r[t]:"then"in r&&"function"==typeof r.then?r.then(r=>e(r,t)):"function"==typeof r&&"default"===t?r:void 0}}})},81521:e=>{e.exports=require("dom-helpers/querySelectorAll")},81895:e=>{e.exports=require("@restart/hooks/useEventCallback")},82015:e=>{e.exports=require("react")},82053:e=>{e.exports=require("@fortawesome/react-fontawesome")},86842:e=>{e.exports=require("@restart/ui/SelectableContext")},87571:e=>{e.exports=require("dom-helpers/transitionEnd")},88751:e=>{e.exports=require("next-i18next")},93787:e=>{e.exports=require("redux-persist")},94947:e=>{e.exports=require("@restart/hooks/useTimeout")},96196:e=>{e.exports=require("next-redux-wrapper")},98320:e=>{e.exports=require("@restart/hooks/useIsomorphicEffect")},99460:e=>{e.exports=require("dom-helpers/css")}};var r=require("../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[6089,9216,9616,2386],()=>t(21127));module.exports=s})();