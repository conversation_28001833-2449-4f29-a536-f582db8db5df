{"version": 3, "file": "static/chunks/pages/institution/[...routes]-865682169224eccf.js", "mappings": "0OAgFA,MAnEe,OAAC,QAAEA,CAAM,CAAoB,GACpCC,EAAcD,EAAOE,IAkEdC,CAlEmB,CAACF,IAkEdE,EAlEoB,EAAI,EAAE,CACvC,CAACC,EAAaC,EAAe,CAAGC,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAC,IACzC,CAACC,EAAYC,EAAc,CAAGF,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAC,IACvC,CAACG,EAAcC,EAAgB,CAAGJ,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAC,IAE3CK,EAAuB,UAC3B,IAAMC,EAAkB,MAAMC,EAAAA,CAAUA,CAACC,GAAG,CAAC,gBAA0B,OAAVb,CAAM,CAAC,EAAE,GACtEI,EAAeO,GACfG,EAAgBH,EAClB,EAEAI,CAAAA,EAAAA,EAAAA,SAAAA,CAASA,CAAC,KACRL,GACF,EAAG,EAAE,EAEL,IAAMI,EAAkB,MAAOH,IAC7B,IAAMK,EAAO,MAAMJ,EAAAA,CAAUA,CAACK,IAAI,CAAC,uBAAwB,CAAC,GACxDD,GAAQA,EAAKE,KAAK,EAAIF,EAAKE,KAAK,CAACC,MAAM,EAAE,CACzCV,EAAgBO,GAChBT,GAAc,GACVS,GAAQA,EAAK,EAAD,GAAS,EAAE,CACrBA,EAAK,EAAD,GAAS,CAACI,QAAQ,CAAC,gBAAgB,EAG3B,EAAD,GAAS,CAACA,QAAQ,CAAC,mBAAqBT,EAAgB,IAAO,EAAIK,EAAK,EAAD,CAAO,CAEzFT,CAF2F,EAE7E,GACPS,EAAK,EAAD,GAAS,CAACI,QAAQ,CAAC,iBAAmBT,EAAgBU,IAAI,EAAIL,EAAK,EAAD,CAAO,EAEtFT,GAAc,IAI1B,EAEQe,EAAuBC,CAAAA,EAAAA,EAAAA,qBAAAA,CAAqBA,CAAC,IAAM,UAACC,EAAAA,OAAeA,CAAAA,CAACrB,YAAa,GAAIH,OAAQA,KAC7FyB,EAAoBC,CAAAA,EAAAA,EAAAA,sBAAAA,CAAsBA,CAAC,IAAM,UAACF,EAAAA,OAAeA,CAAAA,CAACrB,YAAaA,EAAaH,OAAQA,KACpG2B,EAA0BC,CAAAA,EAAAA,EAAAA,oBAAAA,CAAoBA,CAAC,IAAM,UAACC,EAAAA,OAAqBA,CAAAA,CAAC1B,YAAaA,EAAaH,OAAQA,KAEpH,OAAQA,CAAM,CAAC,EAAE,EACf,IAAK,SACH,MAAO,UAACsB,EAAAA,CAAAA,EACV,KAAK,OACH,GAAIhB,EAAc,MAAO,IAAT,CAAS,KAACmB,EAAAA,CAAkBtB,YAAaA,EAAaH,OAAQA,IACvE,GAAIQ,MAAoB,MAAO,UAACsB,EAAAA,OAAeA,CAAAA,CAAAA,EACxD,KAAK,OACH,MAAO,UAACC,EAAAA,OAAeA,CAAAA,CAAC/B,OAAQA,GAElC,KAAK,aACH,GAAIG,MAAsB,GACxB,MAAO,UAACwB,EAAAA,CAAwBxB,YAAaA,IAE/C,KACF,SACE,OAAO,IACX,CACF,mBCrEA,4CACA,2BACA,WACA,OAAe,EAAQ,KAAgD,CACvE,EACA,SAFsB,oECJP,SAAS2B,EAAgBE,CAAW,EAC/C,MACE,UAACC,MAAAA,CAAIC,UAAU,sDACb,UAACD,MAAAA,CAAIC,UAAU,mBAAU,yCAG/B,gCCCF,IAEA,IAFA,aAAiC,gBAA2C,YAAgB,WAAkB,KAAO,WAA2B,8BAAwD,kBAAgC,6BAAuD,kCAA+D,uBAA2L,EAAlI,yBAAqE,UAA6D,GAAwB,GAIjjB,IAAoB,CAoQpB,YAzDA,GAkDA,iEAnDA,yDAEA,IA0BA,EA1BA,+BACA,+CACA,qCAEA,2CACA,qCACA,iBAEA,wCACA,oDACA,kDACA,iBAEA,8BAaA,oDAMA,oEACA,kCACA,yBACA,8BANA,uCA8BA,GA9BA,EA+BA,EAtQA,MAAa,EAAQ,KAAO,EAE5B,OAIA,GANoB,CAIH,EAAQ,KAAY,GAIrC,EAAgB,EAAQ,KAAW,EAEnC,UAFuB,CAEvB,GAAuC,0BAAuC,WAI9E,gBAAkD,MAAa,kFAAyF,yDAIxJ,uBAF2C,sBAG3C,GAH2C,OAG3C,EAH0G,kFAG1G,GAEA,aATkD,SAclD,aAdkD,EAA0C,qDAgB5F,QANA,EAEA,IAIA,kCAAoE,IAAa,IACjF,kBAGA,4HACA,uBACA,SACA,CAAK,kCACL,cACA,wBACA,mBAEA,uBAEA,OACA,IACA,UAEA,CAAK,oBACL,IAoMA,EAzBA,EACA,EA5KA,qBAgNA,oEAZA,oDAEA,gCACA,6BA5BA,EAvKA,EAyKA,CADA,4DAEA,4BAEA,uEACA,GA5KA,CAAK,6BACL,cACA,kBACA,qBACA,oBAEA,YACA,QAEA,gBACA,UAGA,OACA,KACA,UAEA,CAAK,gCACL,uDACA,CAAK,mCACL,2DACA,qBACA,CAAK,6BACL,cACA,UACA,YACA,YAUA,MATA,eAEA,CACA,QACA,UACA,UACA,iBAIA,CAAK,OACL,CAyDA,OAhIA,EAHiN,wCAAyE,aAAe,MAGzS,EAHyS,2CAA0E,EAAG,gDAGtX,KAHsX,YAGtX,CAHsX,EA4EtX,MACA,aACA,iBACA,WAEA,aACA,UACA,YACA,YACA,oBACA,aACA,qBAGA,+BACA,MACA,CACA,2CACA,gBACA,kBACA,CAAW,CACX,gCACS,CACT,wBACA,MACA,CAAY,gCAAkC,CAC9C,gDACA,MACA,CAAc,qCAAuC,CACrD,2BACA,KACA,KACA,GAEA,EACA,IACA,wBACA,MACA,CAAgB,6CAA+C,CAC/D,oBACA,+BACA,SACA,CAAoB,yBACpB,6BACA,CAAqB,uBAA+B,CACpD,QAEA,CAAe,KAKf,CACA,CAAG,GAEH,CACA,CAAC,2BACD,uBACA,yBACA,mCACA,+BACA,wBACA,mCACA,6BACA,oDACA,2BACA,0BACA,8BACA,gCACA,kCACC,iBACD,UACA,eACA,mBACA,WACA,CAAK,CACL,cACA,CAAG,EACH,gBACA,mBACA,WACA,CAAK,CACL,cACA,CAAG,EACH,2BACA,WACA,CAAG,CACH,uBACA,iBACA,mBACA,uBACA,WACA,CAAG,CACH,sBACA,WACA,CAAG,CACH,0BACA,WACA,CAAG,CACH,4BACA,WACA,CACA,CAAC", "sources": ["webpack://_N_E/./pages/institution/[...routes].tsx", "webpack://_N_E/?4021", "webpack://_N_E/./pages/rNoAccess.tsx", "webpack://_N_E/./node_modules/react-confirm-alert/lib/index.js"], "sourcesContent": ["//Import Library\r\nimport { useState, useEffect } from 'react';\r\n\r\n//Import services/components\r\nimport InstitutionForm from \"./Form\";\r\nimport InstitutionShow from \"./InstitutionShow\";\r\nimport InstitutionFocalPoint from './InstitutionFocalPoint'\r\nimport apiService from \"../../services/apiService\";\r\nimport {canAddInstitutionForm, canEditInstitutionForm, canManageFocalPoints} from \"./permission\";\r\nimport NoAccessMessage from '../rNoAccess';\r\nimport { serverSideTranslations } from 'next-i18next/serverSideTranslations';\r\n\r\n\r\nconst Router = ({ router } : { router: any }) => {\r\n  const routes: any = router.query.routes || [];\r\n  const [institution, setInstitution] = useState('');\r\n  const [editAccess, setEditAccess] = useState(false);\r\n  const [loggedInUser, setLoggedInUser] = useState('');\r\n\r\n  const fetchInstitutionData = async () => {\r\n    const institutionData = await apiService.get(`/institution/${routes[1]}`);\r\n    setInstitution(institutionData);\r\n    getLoggedInUser(institutionData);\r\n  }\r\n\r\n  useEffect(() => {\r\n    fetchInstitutionData();\r\n  }, []);\r\n\r\n  const getLoggedInUser = async (institutionData: any) => {\r\n    const data = await apiService.post(\"/users/getLoggedUser\", {});\r\n    if (data && data.roles && data.roles.length) {\r\n        setLoggedInUser(data);\r\n        setEditAccess(false);\r\n        if (data && data['roles']) {\r\n          if (data['roles'].includes(\"SUPER_ADMIN\")) {\r\n            //SUPER_ADMIN can Edit all organisations\r\n            setEditAccess(true);\r\n          } else if (data['roles'].includes(\"PLATFORM_ADMIN\") && institutionData['user'] == data['_id']) {\r\n              //PLATFORM_ADMIN can Edit organisations which is added by them only\r\n              setEditAccess(true);\r\n          } else if (data['roles'].includes(\"GENERAL_USER\") && institutionData.user == data['_id']) {\r\n            //\"GENERAL_USER\" can Edit organisations which is added by them only\r\n            setEditAccess(true);\r\n          }\r\n        }\r\n    }\r\n};\r\n\r\n  const CanAccessCreateForm  = canAddInstitutionForm(() => <InstitutionForm institution={''} routes={routes} />)\r\n  const CanAccessEditForm = canEditInstitutionForm(() => <InstitutionForm institution={institution} routes={routes} />)\r\n  const CanAccessFocalPointPage = canManageFocalPoints(() => <InstitutionFocalPoint institution={institution} routes={routes} />)\r\n\r\n  switch (routes[0]) {\r\n    case \"create\":\r\n      return <CanAccessCreateForm />\r\n    case \"edit\":\r\n      if (editAccess) { return <CanAccessEditForm institution={institution} routes={routes} /> }\r\n      else { if (loggedInUser != '') return <NoAccessMessage /> }\r\n    case \"show\":\r\n      return <InstitutionShow routes={routes} />;\r\n\r\n    case \"focalpoint\":\r\n      if (institution !== null) {\r\n        return <CanAccessFocalPointPage institution={institution} />;\r\n      }\r\n      break;\r\n    default:\r\n      return null;\r\n  }\r\n};\r\n\r\nexport async function getServerSideProps({ locale }: { locale: string }) {\r\n  return {\r\n    props: {\r\n      ...(await serverSideTranslations(locale, ['common'])),\r\n    },\r\n  }\r\n}\r\n\r\nexport default Router;", "\n    (window.__NEXT_P = window.__NEXT_P || []).push([\n      \"/institution/[...routes]\",\n      function () {\n        return require(\"private-next-pages/institution/[...routes].tsx\");\n      }\n    ]);\n    if(module.hot) {\n      module.hot.dispose(function () {\n        window.__NEXT_P.push([\"/institution/[...routes]\"])\n      });\n    }\n  ", "export default function NoAccessMessage(_props: any) {\r\n    return (\r\n      <div className=\"container-fluid p-0 response-message-block\">\r\n        <div className=\"message\">you don't have permission to access</div>\r\n      </div>\r\n    )\r\n  }", "'use strict';\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = undefined;\n\nvar _createClass = function () { function defineProperties(target, props) { for (var i = 0; i < props.length; i++) { var descriptor = props[i]; descriptor.enumerable = descriptor.enumerable || false; descriptor.configurable = true; if (\"value\" in descriptor) descriptor.writable = true; Object.defineProperty(target, descriptor.key, descriptor); } } return function (Constructor, protoProps, staticProps) { if (protoProps) defineProperties(Constructor.prototype, protoProps); if (staticProps) defineProperties(Constructor, staticProps); return Constructor; }; }();\n\nvar _class, _temp2;\n\nexports.confirmAlert = confirmAlert;\n\nvar _react = require('react');\n\nvar _react2 = _interopRequireDefault(_react);\n\nvar _propTypes = require('prop-types');\n\nvar _propTypes2 = _interopRequireDefault(_propTypes);\n\nvar _reactDom = require('react-dom');\n\nfunction _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }\n\nfunction _classCallCheck(instance, Constructor) { if (!(instance instanceof Constructor)) { throw new TypeError(\"Cannot call a class as a function\"); } }\n\nfunction _possibleConstructorReturn(self, call) { if (!self) { throw new ReferenceError(\"this hasn't been initialised - super() hasn't been called\"); } return call && (typeof call === \"object\" || typeof call === \"function\") ? call : self; }\n\nfunction _inherits(subClass, superClass) { if (typeof superClass !== \"function\" && superClass !== null) { throw new TypeError(\"Super expression must either be null or a function, not \" + typeof superClass); } subClass.prototype = Object.create(superClass && superClass.prototype, { constructor: { value: subClass, enumerable: false, writable: true, configurable: true } }); if (superClass) Object.setPrototypeOf ? Object.setPrototypeOf(subClass, superClass) : subClass.__proto__ = superClass; }\n\nvar ReactConfirmAlert = (_temp2 = _class = function (_Component) {\n  _inherits(ReactConfirmAlert, _Component);\n\n  function ReactConfirmAlert() {\n    var _ref;\n\n    var _temp, _this, _ret;\n\n    _classCallCheck(this, ReactConfirmAlert);\n\n    for (var _len = arguments.length, args = Array(_len), _key = 0; _key < _len; _key++) {\n      args[_key] = arguments[_key];\n    }\n\n    return _ret = (_temp = (_this = _possibleConstructorReturn(this, (_ref = ReactConfirmAlert.__proto__ || Object.getPrototypeOf(ReactConfirmAlert)).call.apply(_ref, [this].concat(args))), _this), _this.handleClickButton = function (button) {\n      if (button.onClick) button.onClick();\n      _this.close();\n    }, _this.handleClickOverlay = function (e) {\n      var _this$props = _this.props,\n          closeOnClickOutside = _this$props.closeOnClickOutside,\n          onClickOutside = _this$props.onClickOutside;\n\n      var isClickOutside = e.target === _this.overlay;\n\n      if (closeOnClickOutside && isClickOutside) {\n        onClickOutside();\n        _this.close();\n      }\n    }, _this.close = function () {\n      var afterClose = _this.props.afterClose;\n\n      removeBodyClass();\n      removeElementReconfirm();\n      removeSVGBlurReconfirm(afterClose);\n    }, _this.keyboardClose = function (event) {\n      var _this$props2 = _this.props,\n          closeOnEscape = _this$props2.closeOnEscape,\n          onKeypressEscape = _this$props2.onKeypressEscape,\n          keyCodeForClose = _this$props2.keyCodeForClose;\n\n      var keyCode = event.keyCode;\n      var isKeyCodeEscape = keyCode === 27;\n\n      if (keyCodeForClose.includes(keyCode)) {\n        _this.close();\n      }\n\n      if (closeOnEscape && isKeyCodeEscape) {\n        onKeypressEscape(event);\n        _this.close();\n      }\n    }, _this.componentDidMount = function () {\n      document.addEventListener('keydown', _this.keyboardClose, false);\n    }, _this.componentWillUnmount = function () {\n      document.removeEventListener('keydown', _this.keyboardClose, false);\n      _this.props.willUnmount();\n    }, _this.renderCustomUI = function () {\n      var _this$props3 = _this.props,\n          title = _this$props3.title,\n          message = _this$props3.message,\n          buttons = _this$props3.buttons,\n          customUI = _this$props3.customUI;\n\n      var dataCustomUI = {\n        title: title,\n        message: message,\n        buttons: buttons,\n        onClose: _this.close\n      };\n\n      return customUI(dataCustomUI);\n    }, _temp), _possibleConstructorReturn(_this, _ret);\n  }\n\n  _createClass(ReactConfirmAlert, [{\n    key: 'render',\n    value: function render() {\n      var _this2 = this;\n\n      var _props = this.props,\n          title = _props.title,\n          message = _props.message,\n          buttons = _props.buttons,\n          childrenElement = _props.childrenElement,\n          customUI = _props.customUI,\n          overlayClassName = _props.overlayClassName;\n\n\n      return _react2.default.createElement(\n        'div',\n        {\n          className: 'react-confirm-alert-overlay ' + overlayClassName,\n          ref: function ref(dom) {\n            return _this2.overlay = dom;\n          },\n          onClick: this.handleClickOverlay\n        },\n        _react2.default.createElement(\n          'div',\n          { className: 'react-confirm-alert' },\n          customUI ? this.renderCustomUI() : _react2.default.createElement(\n            'div',\n            { className: 'react-confirm-alert-body' },\n            title && _react2.default.createElement(\n              'h1',\n              null,\n              title\n            ),\n            message,\n            childrenElement(),\n            _react2.default.createElement(\n              'div',\n              { className: 'react-confirm-alert-button-group' },\n              buttons.map(function (button, i) {\n                return _react2.default.createElement(\n                  'button',\n                  { key: i, onClick: function onClick() {\n                      return _this2.handleClickButton(button);\n                    }, className: button.className },\n                  button.label\n                );\n              })\n            )\n          )\n        )\n      );\n    }\n  }]);\n\n  return ReactConfirmAlert;\n}(_react.Component), _class.propTypes = {\n  title: _propTypes2.default.string,\n  message: _propTypes2.default.string,\n  buttons: _propTypes2.default.array.isRequired,\n  childrenElement: _propTypes2.default.func,\n  customUI: _propTypes2.default.func,\n  closeOnClickOutside: _propTypes2.default.bool,\n  closeOnEscape: _propTypes2.default.bool,\n  keyCodeForClose: _propTypes2.default.arrayOf(_propTypes2.default.number),\n  willUnmount: _propTypes2.default.func,\n  afterClose: _propTypes2.default.func,\n  onClickOutside: _propTypes2.default.func,\n  onKeypressEscape: _propTypes2.default.func,\n  overlayClassName: _propTypes2.default.string\n}, _class.defaultProps = {\n  buttons: [{\n    label: 'Cancel',\n    onClick: function onClick() {\n      return null;\n    },\n    className: null\n  }, {\n    label: 'Confirm',\n    onClick: function onClick() {\n      return null;\n    },\n    className: null\n  }],\n  childrenElement: function childrenElement() {\n    return null;\n  },\n  closeOnClickOutside: true,\n  closeOnEscape: true,\n  keyCodeForClose: [],\n  willUnmount: function willUnmount() {\n    return null;\n  },\n  afterClose: function afterClose() {\n    return null;\n  },\n  onClickOutside: function onClickOutside() {\n    return null;\n  },\n  onKeypressEscape: function onKeypressEscape() {\n    return null;\n  }\n}, _temp2);\nexports.default = ReactConfirmAlert;\n\n\nfunction createSVGBlurReconfirm() {\n  // If has svg ignore to create the svg\n  var svg = document.getElementById('react-confirm-alert-firm-svg');\n  if (svg) return;\n  var svgNS = 'http://www.w3.org/2000/svg';\n  var feGaussianBlur = document.createElementNS(svgNS, 'feGaussianBlur');\n  feGaussianBlur.setAttribute('stdDeviation', '0.3');\n\n  var filter = document.createElementNS(svgNS, 'filter');\n  filter.setAttribute('id', 'gaussian-blur');\n  filter.appendChild(feGaussianBlur);\n\n  var svgElem = document.createElementNS(svgNS, 'svg');\n  svgElem.setAttribute('id', 'react-confirm-alert-firm-svg');\n  svgElem.setAttribute('class', 'react-confirm-alert-svg');\n  svgElem.appendChild(filter);\n\n  document.body.appendChild(svgElem);\n}\n\nfunction removeSVGBlurReconfirm(afterClose) {\n  var svg = document.getElementById('react-confirm-alert-firm-svg');\n  if (svg) {\n    svg.parentNode.removeChild(svg);\n  }\n  document.body.children[0].classList.remove('react-confirm-alert-blur');\n  afterClose();\n}\n\nfunction createElementReconfirm(properties) {\n  var divTarget = document.getElementById('react-confirm-alert');\n  if (divTarget) {\n    // Rerender - the mounted ReactConfirmAlert\n    (0, _reactDom.render)(_react2.default.createElement(ReactConfirmAlert, properties), divTarget);\n  } else {\n    // Mount the ReactConfirmAlert component\n    document.body.children[0].classList.add('react-confirm-alert-blur');\n    divTarget = document.createElement('div');\n    divTarget.id = 'react-confirm-alert';\n    document.body.appendChild(divTarget);\n    (0, _reactDom.render)(_react2.default.createElement(ReactConfirmAlert, properties), divTarget);\n  }\n}\n\nfunction removeElementReconfirm() {\n  var target = document.getElementById('react-confirm-alert');\n  if (target) {\n    (0, _reactDom.unmountComponentAtNode)(target);\n    target.parentNode.removeChild(target);\n  }\n}\n\nfunction addBodyClass() {\n  document.body.classList.add('react-confirm-alert-body-element');\n}\n\nfunction removeBodyClass() {\n  document.body.classList.remove('react-confirm-alert-body-element');\n}\n\nfunction confirmAlert(properties) {\n  addBodyClass();\n  createSVGBlurReconfirm();\n  createElementReconfirm(properties);\n}"], "names": ["router", "routes", "query", "Router", "institution", "setInstitution", "useState", "editAccess", "setEditAccess", "loggedInUser", "setLoggedInUser", "fetchInstitutionData", "institutionData", "apiService", "get", "getLoggedInUser", "useEffect", "data", "post", "roles", "length", "includes", "user", "CanAccessCreateForm", "canAddInstitutionForm", "InstitutionForm", "CanAccessEditForm", "canEditInstitutionForm", "CanAccessFocalPointPage", "canManageFocalPoints", "InstitutionFocalPoint", "NoAccessMessage", "InstitutionShow", "_props", "div", "className"], "sourceRoot": "", "ignoreList": [3]}