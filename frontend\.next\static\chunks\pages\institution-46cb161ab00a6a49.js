(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[518],{15641:(e,t,i)=>{"use strict";i.d(t,{A:()=>n});var s=i(37876);i(14232);var r=i(62945);let n=e=>{let{name:t="Marker",id:i="",countryId:n="",type:a,icon:l,position:o,onClick:c,title:u,draggable:d=!1}=e;return o&&"number"==typeof o.lat&&"number"==typeof o.lng?(0,s.jsx)(r.pH,{position:o,icon:l,title:u||t,draggable:d,onClick:e=>{c&&c({name:t,id:i,countryId:n,type:a,position:o},{position:o,getPosition:()=>o},e)}}):null}},19667:(e,t,i)=>{"use strict";i.r(t),i.d(t,{default:()=>u});var s=i(37876),r=i(80942),n=i(14232),a=i(48230),l=i.n(a),o=i(53718),c=i(31753);let u=function(e){let[t,i]=(0,n.useState)({institutions:"",german_operations:"",projects:"",german_countryId:"",german_institutions:""}),{t:a}=(0,c.Bd)("common"),u=async e=>{let t=await o.A.get("/stats/institutions",e);t&&i(t)};return(0,n.useEffect)(()=>{u({query:{status:{$ne:"Request Pending"}},sort:{title:"asc"},limit:"~"})},[]),(0,s.jsx)("div",{className:"quick-info-filter",children:(0,s.jsxs)(r.A,{style:{marginLeft:"-10px"},children:[(0,s.jsx)(r.A.Item,{children:(0,s.jsx)(l(),{href:{pathname:"/institution"},children:(0,s.jsxs)("div",{className:"info-item",children:[(0,s.jsx)("div",{className:"quickinfo-img",children:(0,s.jsx)("img",{src:"/images/quickinfo1.png",width:"27",height:"30",alt:"Organization Quick Info"})}),(0,s.jsxs)("span",{children:[(0,s.jsx)("b",{children:t.institutions})," ",a("AllOrganisations")]})]})})}),(0,s.jsx)(r.A.Item,{children:(0,s.jsxs)(l(),{href:{pathname:"/institution",query:{country:t.german_countryId}},children:[(0,s.jsx)("div",{className:"quickinfo-img",children:(0,s.jsx)("img",{src:"/images/quickinfo2.png",width:"24",height:"23",alt:"Organization Quick Info"})}),(0,s.jsxs)("span",{children:[(0,s.jsx)("b",{children:t.german_institutions})," ",a("GermanOrganisations")]})]})}),(0,s.jsx)(r.A.Item,{children:(0,s.jsxs)(l(),{href:"/project",as:"/project",children:[(0,s.jsx)("div",{className:"quickinfo-img",children:(0,s.jsx)("img",{src:"/images/quickinfo3.png",width:"24",height:"21",alt:"Organization Quick Info"})}),(0,s.jsxs)("span",{children:[(0,s.jsx)("b",{children:t.projects})," ",a("Projects")]})]})})]})})}},33859:(e,t,i)=>{"use strict";i.r(t),i.d(t,{canAddInstitution:()=>a,canAddInstitutionForm:()=>l,canEditInstitution:()=>o,canEditInstitutionForm:()=>c,canManageFocalPoints:()=>d,canViewDiscussionUpdate:()=>u,default:()=>p});var s=i(37876);i(14232);var r=i(8178),n=i(59626);let a=(0,r.A)({authenticatedSelector:e=>!!e.permissions&&!!e.permissions.institution&&!!e.permissions.institution["create:any"],wrapperDisplayName:"CanAddInstitution"}),l=(0,r.A)({authenticatedSelector:e=>!!e.permissions&&!!e.permissions.institution&&!!e.permissions.institution["create:any"],wrapperDisplayName:"CanAddInstitutionForm",FailureComponent:()=>(0,s.jsx)(n.default,{})}),o=(0,r.A)({authenticatedSelector:(e,t)=>{if(e.permissions&&e.permissions.institution){if(e.permissions.institution["update:any"])return!0;else if(e.permissions.institution["update:own"]&&t.institution&&t.institution.user&&t.institution.user===e.user._id)return!0}return!1},wrapperDisplayName:"CanEditInstitution"}),c=(0,r.A)({authenticatedSelector:(e,t)=>{if(e.permissions&&e.permissions.institution){if(e.permissions.institution["update:any"])return!0;else if(e.permissions.institution["update:own"]&&t.institution&&t.institution.user&&t.institution.user===e.user._id)return!0}return!1},wrapperDisplayName:"CanEditInstitutionForm",FailureComponent:()=>(0,s.jsx)(n.default,{})}),u=(0,r.A)({authenticatedSelector:e=>!!e.permissions&&!!e.permissions.update&&!!e.permissions.update["read:any"],wrapperDisplayName:"CanViewDiscussionUpdate"}),d=(0,r.A)({authenticatedSelector:(e,t)=>{if(e.permissions&&e.permissions.institution_focal_point){if(e.permissions.institution_focal_point["update:any"])return!0;else if(e.permissions.institution_focal_point["update:own"]&&t.institution&&t.institution.user&&t.institution.user===e.user._id)return!0}return!1},wrapperDisplayName:"canManageFocalPoints"}),p=a},37266:(e,t,i)=>{"use strict";i.r(t),i.d(t,{__N_SSG:()=>x,default:()=>j});var s=i(37876),r=i(14232),n=i(48230),a=i.n(n),l=i(60282),o=i(49589),c=i(56970),u=i(37784),d=i(69438),p=i(69600),y=i(14217),m=i(19667),f=i(71377),h=i(31753),g=i(33859),x=!0;let j=()=>{let[e,t]=(0,r.useState)([]),[i,n]=(0,r.useState)([]),{t:x}=(0,h.Bd)("common"),j=()=>(0,s.jsx)(a(),{href:"/institution/[...routes]",as:"/institution/create",children:(0,s.jsx)(l.A,{variant:"secondary",size:"sm",children:x("addOrganisation")})}),v=e=>{n(e)},T=(0,g.canAddInstitution)(()=>(0,s.jsx)(j,{}));return(0,s.jsxs)(o.A,{fluid:!0,className:"p-0",children:[(0,s.jsx)(c.A,{children:(0,s.jsx)(u.A,{xs:12,children:(0,s.jsx)(p.A,{title:x("menu.organisations")})})}),(0,s.jsx)(c.A,{children:(0,s.jsxs)(u.A,{xs:12,className:"organisationmap_div",children:[(0,s.jsx)("div",{className:"organisationMap",children:(0,s.jsx)(f.default,{institutions:e})}),(0,s.jsx)("div",{className:"organisationInfo",children:(0,s.jsx)(m.default,{})})]})}),(0,s.jsx)(c.A,{children:(0,s.jsx)(u.A,{xs:12,children:(0,s.jsx)(d.A,{filtreg:e=>v(e),selectedRegions:[],regionHandler:v})})}),(0,s.jsx)(c.A,{children:(0,s.jsx)(u.A,{xs:12,className:"ps-4",children:(0,s.jsx)(T,{})})}),(0,s.jsx)(c.A,{className:"mt-3",children:(0,s.jsx)(u.A,{xs:12,children:(0,s.jsx)(y.default,{selectedRegions:i,setInstitutions:t})})})]})}},65688:(e,t,i)=>{"use strict";i.d(t,{A:()=>g});var s=i(3173),r=i(14232),n=i(72888),a=i(59672),l=i(14867),o=i(8258),c=i(629),u=i(74522),d=i(69455),p=i(37876);let y=["as","onSelect","activeKey","role","onKeyDown"],m=()=>{},f=(0,u.sE)("event-key"),h=r.forwardRef((e,t)=>{let i,d,{as:h="div",onSelect:g,activeKey:x,role:j,onKeyDown:v}=e,T=function(e,t){if(null==e)return{};var i={};for(var s in e)if(({}).hasOwnProperty.call(e,s)){if(t.indexOf(s)>=0)continue;i[s]=e[s]}return i}(e,y),A=(0,n.A)(),k=(0,r.useRef)(!1),b=(0,r.useContext)(o.A),w=(0,r.useContext)(c.A);w&&(j=j||"tablist",x=w.activeKey,i=w.getControlledId,d=w.getControllerId);let _=(0,r.useRef)(null),C=e=>{let t=_.current;if(!t)return null;let i=(0,s.A)(t,`[${f}]:not([aria-disabled=true])`),r=t.querySelector("[aria-selected=true]");if(!r||r!==document.activeElement)return null;let n=i.indexOf(r);if(-1===n)return null;let a=n+e;return a>=i.length&&(a=0),a<0&&(a=i.length-1),i[a]},I=(e,t)=>{null!=e&&(null==g||g(e,t),null==b||b(e,t))};(0,r.useEffect)(()=>{if(_.current&&k.current){let e=_.current.querySelector(`[${f}][aria-selected=true]`);null==e||e.focus()}k.current=!1});let N=(0,a.A)(t,_);return(0,p.jsx)(o.A.Provider,{value:I,children:(0,p.jsx)(l.A.Provider,{value:{role:j,activeKey:(0,o.u)(x),getControlledId:i||m,getControllerId:d||m},children:(0,p.jsx)(h,Object.assign({},T,{onKeyDown:e=>{let t;if(null==v||v(e),w){switch(e.key){case"ArrowLeft":case"ArrowUp":t=C(-1);break;case"ArrowRight":case"ArrowDown":t=C(1);break;default:return}t&&(e.preventDefault(),I(t.dataset[(0,u.y)("EventKey")]||null,e),k.current=!0,A())}},ref:N,role:j}))})})});h.displayName="Nav";let g=Object.assign(h,{Item:d.A})},66619:(e,t,i)=>{"use strict";i.d(t,{A:()=>y});var s=i(37876);i(14232);var r=i(62945);let n=e=>{let{position:t,onCloseClick:i,children:n}=e;return(0,s.jsx)(r.Fu,{position:t,onCloseClick:i,children:(0,s.jsx)("div",{children:n})})},a="labels.text.fill",l="labels.text.stroke",o="road.highway",c="geometry.stroke",u=[{elementType:"geometry",stylers:[{color:"#1d2c4d"}]},{elementType:a,stylers:[{color:"#8ec3b9"}]},{elementType:l,stylers:[{color:"#1a3646"}]},{featureType:"administrative",elementType:"geometry",stylers:[{visibility:"off"}]},{featureType:"administrative.country",elementType:c,stylers:[{color:"#4b6878"}]},{featureType:"administrative.land_parcel",elementType:"labels",stylers:[{visibility:"off"}]},{featureType:"administrative.land_parcel",elementType:a,stylers:[{color:"#64779e"}]},{featureType:"administrative.province",elementType:c,stylers:[{color:"#4b6878"}]},{featureType:"landscape.man_made",elementType:c,stylers:[{color:"#334e87"}]},{featureType:"landscape.natural",elementType:"geometry",stylers:[{color:"#023e58"}]},{featureType:"poi",stylers:[{visibility:"off"}]},{featureType:"poi",elementType:"geometry",stylers:[{color:"#283d6a"}]},{featureType:"poi",elementType:"labels.text",stylers:[{visibility:"off"}]},{featureType:"poi",elementType:a,stylers:[{color:"#6f9ba5"}]},{featureType:"poi",elementType:l,stylers:[{color:"#1d2c4d"}]},{featureType:"poi.park",elementType:"geometry.fill",stylers:[{color:"#023e58"}]},{featureType:"poi.park",elementType:a,stylers:[{color:"#3C7680"}]},{featureType:"road",stylers:[{visibility:"off"}]},{featureType:"road",elementType:"geometry",stylers:[{color:"#304a7d"}]},{featureType:"road",elementType:"labels.icon",stylers:[{visibility:"off"}]},{featureType:"road",elementType:a,stylers:[{color:"#98a5be"}]},{featureType:"road",elementType:l,stylers:[{color:"#1d2c4d"}]},{featureType:o,elementType:"geometry",stylers:[{color:"#2c6675"}]},{featureType:o,elementType:c,stylers:[{color:"#255763"}]},{featureType:o,elementType:a,stylers:[{color:"#b0d5ce"}]},{featureType:o,elementType:l,stylers:[{color:"#023e58"}]},{featureType:"road.local",elementType:"labels",stylers:[{visibility:"off"}]},{featureType:"transit",stylers:[{visibility:"off"}]},{featureType:"transit",elementType:a,stylers:[{color:"#98a5be"}]},{featureType:"transit",elementType:l,stylers:[{color:"#1d2c4d"}]},{featureType:"transit.line",elementType:"geometry.fill",stylers:[{color:"#283d6a"}]},{featureType:"transit.station",elementType:"geometry",stylers:[{color:"#3a4762"}]},{featureType:"water",elementType:"geometry",stylers:[{color:"#0e1626"}]},{featureType:"water",elementType:a,stylers:[{color:"#4e6d70"}]}];var d=i(89099),p=i(55316);let y=e=>{let{markerInfo:t,activeMarker:i,initialCenter:a,children:l,height:o=300,width:c="114%",language:y,zoom:m=1,minZoom:f=1,onClose:h}=e,{locale:g}=(0,d.useRouter)(),{isLoaded:x,loadError:j}=(0,p._)();return j?(0,s.jsx)("div",{children:"Error loading maps"}):x?(0,s.jsx)("div",{className:"map-container",children:(0,s.jsx)("div",{className:"mapprint",style:{width:c,height:o,position:"relative"},children:(0,s.jsxs)(r.u6,{mapContainerStyle:{width:c,height:"number"==typeof o?"".concat(o,"px"):o},center:a||{lat:52.520017,lng:13.404195},zoom:m,onLoad:e=>{e.setOptions({styles:u})},options:{minZoom:f,draggable:!0,keyboardShortcuts:!1,streetViewControl:!1,panControl:!1,clickableIcons:!1,mapTypeControl:!1,fullscreenControl:!0},children:[l,t&&i&&i.getPosition&&(0,s.jsx)(n,{position:i.getPosition(),onCloseClick:()=>{console.log("close click"),null==h||h()},children:t})]})})}):(0,s.jsx)("div",{children:"Loading Maps..."})}},69438:(e,t,i)=>{"use strict";i.d(t,{A:()=>p});var s=i(37876),r=i(14232),n=i(82851),a=i.n(n),l=i(29504),o=i(60282),c=i(53718),u=i(31753);function d(e){let{filtreg:t}=e,[i,n]=(0,r.useState)(!0),[d,p]=(0,r.useState)([]),[y,m]=(0,r.useState)([]),{t:f}=(0,u.Bd)("common"),h={query:{},limit:"~",sort:{title:"asc"}},g=async e=>{let i=await c.A.get("/worldregion",e);if(i&&Array.isArray(i.data)){let e=[],s=[];a().each(i.data,(t,i)=>{let r={...t,isChecked:!0};e.push(r),s.push(t._id)}),t(s),m(s),p(e)}};(0,r.useEffect)(()=>{g(h)},[]);let x=e=>{let i=[...d],s=[...y];i.forEach((t,r)=>{t.code===e.target.id&&(i[r].isChecked=e.target.checked,e.target.checked?s.push(t._id):s=s.filter(e=>e!==t._id))}),m(s),t(s),n(!1),p(i)};return(0,s.jsxs)("div",{className:"regions-multi-checkboxes",children:[(0,s.jsx)(l.A.Check,{type:"checkbox",id:"all",label:f("AllRegions"),checked:i,onChange:e=>{let i=d.map(t=>({...t,isChecked:e.target.checked})),s=[];e.target.checked&&(s=i.map(e=>e._id)),t(s),m(s),n(e.target.checked),p(i)}}),d.map((e,t)=>(0,s.jsx)(l.A.Check,{type:"checkbox",id:e.code,label:e.title,value:e.code,onChange:x,checked:d[t].isChecked},t)),(0,s.jsx)(o.A,{onClick:()=>{let e=d.map(e=>({...e,isChecked:!1}));m([]),n(!1),p(e),t([])},className:"btn-plain ps-2",children:f("ClearAll")})]})}d.defaultProps={filtreg:()=>{}};let p=d},69600:(e,t,i)=>{"use strict";i.d(t,{A:()=>r});var s=i(37876);function r(e){return(0,s.jsx)("h2",{className:"page-heading",children:e.title})}},71377:(e,t,i)=>{"use strict";i.r(t),i.d(t,{default:()=>u});var s=i(37876),r=i(14232),n=i(82851),a=i.n(n),l=i(66619),o=i(15641),c=i(31753);let u=e=>{let{i18n:t}=(0,c.Bd)("common"),i=t.language,{institutions:n}=e,[u,d]=(0,r.useState)({}),[p,y]=(0,r.useState)([]),[m,f]=(0,r.useState)({}),h=()=>{d(null),f(null)},g=(e,t,i)=>{h(),d(t),f({name:e.name,id:e.id,countryId:e.countryId})},x=()=>{let e=[],t=n.filter(e=>"Request Pending"!=e.status);a().forEach(t,t=>{e.push({title:t.title,id:t._id,countryId:t&&t.address&&t.address.country&&t.address.country._id,lat:t.address&&t.address.country&&t.address.country.coordinates&&parseFloat(t.address.country.coordinates[0].latitude),lng:t.address&&t.address.country&&t.address.country.coordinates&&parseFloat(t.address.country.coordinates[0].longitude)})}),y([...e])};return(0,r.useEffect)(()=>{x()},[n]),(0,s.jsx)(l.A,{onClose:h,language:i,points:p,activeMarker:u,markerInfo:(0,s.jsx)(e=>{let{info:t}=e;if(t&&t.countryId){let e=n.filter(e=>"Request Pending"!=e.status).filter(e=>e.address&&e.address.country&&e.address.country._id==t.countryId);return(0,s.jsx)("ul",{children:e.map((e,t)=>(0,s.jsx)("li",{children:(0,s.jsx)("a",{href:"/".concat(i,"/institution/show/").concat(e._id),children:e.title})},t))})}return null},{info:m}),children:p.length>=1?p.map((e,t)=>(0,s.jsx)(o.A,{name:e.title,id:e.id,countryId:e.countryId,icon:{url:"/images/map-marker-white.svg"},onClick:g,position:e},t)):null})}},80942:(e,t,i)=>{"use strict";i.d(t,{A:()=>f});var s=i(15039),r=i.n(s),n=i(14232);i(68547);var a=i(22631),l=i(65688),o=i(77346),c=i(76959),u=i(69455),d=i(8258),p=i(37876);let y=n.forwardRef((e,t)=>{let{bsPrefix:i,active:s,disabled:n,eventKey:a,className:l,variant:y,action:m,as:f,...h}=e;i=(0,o.oU)(i,"list-group-item");let[g,x]=(0,u.M)({key:(0,d.u)(a,h.href),active:s,...h}),j=(0,c.A)(e=>{if(n){e.preventDefault(),e.stopPropagation();return}g.onClick(e)});n&&void 0===h.tabIndex&&(h.tabIndex=-1,h["aria-disabled"]=!0);let v=f||(m?h.href?"a":"button":"div");return(0,p.jsx)(v,{ref:t,...h,...g,onClick:j,className:r()(l,i,x.isActive&&"active",n&&"disabled",y&&"".concat(i,"-").concat(y),m&&"".concat(i,"-action"))})});y.displayName="ListGroupItem";let m=n.forwardRef((e,t)=>{let i,{className:s,bsPrefix:n,variant:c,horizontal:u,numbered:d,as:y="div",...m}=(0,a.Zw)(e,{activeKey:"onSelect"}),f=(0,o.oU)(n,"list-group");return u&&(i=!0===u?"horizontal":"horizontal-".concat(u)),(0,p.jsx)(l.A,{ref:t,...m,as:y,className:r()(s,f,c&&"".concat(f,"-").concat(c),i&&"".concat(f,"-").concat(i),d&&"".concat(f,"-numbered"))})});m.displayName="ListGroup";let f=Object.assign(m,{Item:y})},85173:(e,t,i)=>{(window.__NEXT_P=window.__NEXT_P||[]).push(["/institution",function(){return i(37266)}])}},e=>{var t=t=>e(e.s=t);e.O(0,[9759,9773,4217,636,6593,8792],()=>t(85173)),_N_E=e.O()}]);
//# sourceMappingURL=institution-46cb161ab00a6a49.js.map