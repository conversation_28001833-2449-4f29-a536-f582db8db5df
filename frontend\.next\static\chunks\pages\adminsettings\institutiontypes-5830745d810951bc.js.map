{"version": 3, "file": "static/chunks/pages/adminsettings/institutiontypes-5830745d810951bc.js", "mappings": "0qBAGA,IAAMA,EAAS,aACFC,EAAmBC,CAAAA,EAAAA,EAAAA,CAAAA,CAAoBA,CAAC,CACnDC,sBAAuB,KACjBC,EAAMC,WAAW,IAAID,EAAMC,WAAW,CAACC,YAAY,IAAIF,EAAMC,WAAW,CAACC,YAAY,CAACN,EAAO,CAKnGO,CALqG,kBAKjF,kBACtB,GAAG,EAE0BL,CAAAA,EAAAA,EAAAA,CAAAA,CAAoBA,CAAC,CAChDC,sBAAuB,KACjBC,EAAMC,WAAW,IAAID,EAAMC,WAAW,CAACG,OAAO,IAAIJ,EAAMC,WAAW,CAACG,OAAO,CAACR,EAAO,CAKzFO,CAL2F,kBAKvE,eACtB,GAAG,EAEmCL,CAAAA,EAAAA,EAAAA,CAAAA,CAAoBA,CAAC,CACzDC,sBAAuB,KACjBC,EAAMC,WAAW,IAAID,EAAMC,WAAW,CAACI,iBAAiB,IAAIL,EAAMC,WAAW,CAACI,iBAAiB,CAACT,EAAO,CAK7GO,CAL+G,kBAK3F,wBACtB,GAAG,EAE8BL,CAAAA,EAAAA,EAAAA,CAAAA,CAAoBA,CAAC,CACpDC,sBAAuB,KACjBC,EAAMC,WAAW,IAAID,EAAMC,WAAW,CAACK,YAAY,IAAIN,EAAMC,WAAW,CAACK,YAAY,CAACV,EAAO,CAKnGO,CALqG,kBAKjF,mBACtB,GAAG,EAE4BL,CAAAA,EAAAA,EAAAA,CAAAA,CAAoBA,CAAC,CAClDC,sBAAuB,KACjBC,EAAMC,WAAW,IAAID,EAAMC,WAAW,CAACM,SAAS,IAAIP,EAAMC,WAAW,CAACM,SAAS,CAACX,EAAO,CAK7FO,CAL+F,kBAK3E,iBACtB,GAAG,EAEqCL,CAAAA,EAAAA,EAAAA,CAAAA,CAAoBA,CAAC,CAC3DC,sBAAuB,KACjBC,EAAMC,WAAW,IAAID,EAAMC,WAAW,CAACO,uBAAuB,IAAIR,EAAMC,WAAW,CAACO,uBAAuB,CAACZ,EAAO,CAKzHO,CAL2H,kBAKvG,0BACtB,GAAG,EAEiCL,CAAAA,EAAAA,EAAAA,CAAAA,CAAoBA,CAAC,CACvDC,sBAAuB,KACjBC,EAAMC,WAAW,IAAID,EAAMC,WAAW,CAACO,uBAAuB,IAAIR,EAAMC,WAAW,CAACO,uBAAuB,CAACZ,EAAO,CAKzHO,CAL2H,kBAKvG,sBACtB,GAAG,EAC0BL,CAAAA,EAAAA,EAAAA,CAAAA,CAAoBA,CAAC,CAChDC,sBAAwBC,KAClBA,EAAMC,WAAW,IAAID,EAAMC,WAAW,CAACQ,MAAM,IAAIT,EAAMC,WAAW,CAACQ,MAAM,CAACb,EAAO,CAKvFO,CALyF,kBAKrE,eACtB,GAAG,EAE8BL,CAAAA,EAAAA,EAAAA,CAAAA,CAAoBA,CAAC,CACpDC,sBAAwBC,KAClBA,EAAMC,WAAW,IAAID,EAAMC,WAAW,CAACS,WAAW,IAAIV,EAAMC,WAAW,CAACS,WAAW,CAACd,EAAO,CAKjGO,CALmG,kBAK/E,mBACtB,GAAG,EAEuCL,CAAAA,EAAAA,EAAAA,CAAAA,CAAoBA,CAAC,CAC7DC,sBAAuB,GACjBC,IAAMC,WAAW,IAAID,EAAMC,WAAW,CAACU,WAAW,IAAIX,EAAMC,WAAW,CAACU,WAAW,CAACf,EAAO,CAKjGO,CALmG,kBAK/E,4BACtB,GAAG,EAEuCL,CAAAA,EAAAA,EAAAA,CAAAA,CAAoBA,CAAC,CAC7DC,sBAAwBC,GAClBA,IAAMC,WAAW,IAAID,EAAMC,WAAW,CAACW,mBAAmB,IAAIZ,EAAMC,WAAW,CAACW,mBAAmB,CAAChB,EAAO,CAKjHO,CALmH,kBAK/F,4BACtB,GAAG,EAEoCL,CAAAA,EAAAA,EAAAA,CAAAA,CAAoBA,CAAC,CAC1DC,sBAAuB,KACjBC,EAAMC,WAAW,IAAID,EAAMC,WAAW,CAACY,gBAAgB,IAAIb,EAAMC,WAAW,CAACY,gBAAgB,CAACjB,EAAO,CAK3GO,CAL6G,kBAKzF,yBACtB,GAAG,EAEkCL,CAAAA,EAAAA,EAAAA,CAAAA,CAAoBA,CAAC,CACxDC,sBAAuB,KACjBC,EAAMC,WAAW,IAAID,EAAMC,WAAW,CAACa,gBAAgB,IAAId,EAAMC,WAAW,CAACa,gBAAgB,CAAClB,EAAO,CAK3GO,CAL6G,kBAKzF,uBACtB,GAAG,EAEgCL,CAAAA,EAAAA,EAAAA,CAAAA,CAAoBA,CAAC,CACtDC,sBAAuB,KACjBC,EAAMC,WAAW,IAAID,EAAMC,WAAW,CAACc,cAAc,IAAIf,EAAMC,WAAW,CAACc,cAAc,CAACnB,EAAO,CAKvGO,CALyG,kBAKrF,qBACtB,GAAG,EAE0BL,CAAAA,EAAAA,EAAAA,CAAAA,CAAoBA,CAAC,CAChDC,sBAAwBC,KAClBA,EAAMC,WAAW,IAAID,EAAMC,WAAW,CAACe,MAAM,IAAIhB,EAAMC,WAAW,CAACe,MAAM,CAACpB,EAAO,CAKvFO,CALyF,kBAKrE,eACtB,GAAG,EAE6BL,CAAAA,EAAAA,EAAAA,CAAAA,CAAoBA,CAAC,CACnDC,sBAAuB,KACjBC,EAAMC,WAAW,IAAID,EAAMC,WAAW,CAACgB,UAAU,IAAIjB,EAAMC,WAAW,CAACgB,UAAU,CAACrB,EAAO,CAK/FO,CALiG,kBAK7E,kBACtB,GAAG,EAE4BL,CAAAA,EAAAA,EAAAA,CAAAA,CAAoBA,CAAC,CAClDC,sBAAwBC,KAClBA,EAAMC,WAAW,IAAID,EAAMC,WAAW,CAACiB,QAAQ,IAAIlB,EAAMC,WAAW,CAACiB,QAAQ,CAACtB,EAAO,CAK3FO,CAL6F,kBAKzE,iBACtB,GAAG,EAE8BL,CAAAA,EAAAA,EAAAA,CAAAA,CAAoBA,CAAC,CACpDC,sBAAuB,GACjBC,IAAMC,WAAW,IAAID,EAAMC,WAAW,CAACkB,WAAW,IAAInB,EAAMC,WAAW,CAACkB,WAAW,CAACvB,EAAO,CAKjGO,CALmG,kBAK/E,mBACtB,GAEaiB,EAActB,CAAAA,EAAAA,EAAAA,CAAAA,CAAoBA,CAAC,CAC9CC,sBAAuB,KACjBC,EAAMC,WAAW,IAAID,EAAMC,WAAW,CAACoB,KAAK,IAAIrB,EAAMC,WAAW,CAACoB,KAAK,CAACzB,EAAO,CAKrFO,CALuF,kBAKnE,aACtB,GAAG,EAE8BL,CAAAA,EAAAA,EAAAA,CAAAA,CAAoBA,CAAC,CACpDC,sBAAwBC,KAClBA,EAAMC,WAAW,IAAID,EAAMC,WAAW,CAACqB,WAAW,IAAItB,EAAMC,WAAW,CAACqB,WAAW,CAAC1B,EAAO,CAKjGO,CALmG,kBAK/E,mBACtB,GAAG,EAE8BL,CAAAA,EAAAA,EAAAA,CAAAA,CAAoBA,CAAC,CACpDC,sBAAuB,KACjBC,EAAMC,WAAW,IAAID,EAAMC,WAAW,CAACsB,YAAY,IAAIvB,EAAMC,WAAW,CAACsB,YAAY,CAAC3B,EAAO,CAKnGO,CALqG,kBAKjF,mBACtB,GAAG,EAE0BL,CAAAA,EAAAA,EAAAA,CAAAA,CAAoBA,CAAC,CAChDC,sBAAwBC,GACtB,EAAIA,EAAMC,WAAW,IAAID,EAAMC,WAAW,CAACuB,SAAS,IAAIxB,EAAMC,WAAW,CAACuB,SAAS,CAAC5B,EAAO,IAAII,EAAMC,WAAW,CAACwB,OAAO,IAAIzB,EAAMC,WAAW,CAACwB,OAAO,CAAC7B,EAAO,IAAGI,EAAMC,WAAW,CAACyB,KAAK,IAAI1B,EAAMC,WAAW,CAACyB,KAAK,CAAC9B,EAAO,IAAGI,EAAMC,WAAW,CAAC0B,MAAM,IAAI3B,EAAMC,WAAW,CAAC0B,MAAM,CAAC/B,EAAO,IAAGI,EAAMC,WAAW,CAACU,WAAW,IAAIX,EAAMC,WAAW,CAACU,WAAW,CAACf,EAAO,IAAGI,EAAMC,WAAW,CAAC2B,MAAM,IAAI5B,EAAMC,WAAW,CAAC2B,MAAM,CAAChC,EAAO,EAAE,CAG5Z,EAETO,mBAAoB,eACtB,GAAG,EAEYN,gBAAgBA,EAAC,CC9NhC,4CACA,kCACA,WACA,OAAe,EAAQ,KAA6D,CACpF,EACA,SAFsB,sMCkDtB,MAxC8BgC,QAiCtB7B,EAAAA,EAhCN,GAAM,GAAE8B,CAAC,CAAE,CAAGC,CAAAA,EAAAA,EAAAA,EAAAA,CAAcA,CAAC,UACvBC,EAA2B,IAE7B,EAoC6BC,CApC7B,CAoC8B,CApC9B,MAACC,EAAAA,CAASA,CAAAA,CAACC,MAAO,CAAEC,UAAW,QAAS,EAAGC,KAAK,IAACC,UAAU,gBACzD,UAACC,EAAAA,CAAGA,CAAAA,UACF,UAACC,EAAAA,CAAGA,CAAAA,CAACC,GAAI,YACP,UAACC,EAAAA,CAAWA,CAAAA,CAACC,MAAOb,EAAE,yDAG1B,UAACS,EAAAA,CAAGA,CAAAA,UACF,UAACC,EAAAA,CAAGA,CAAAA,CAACC,GAAI,YACP,UAACG,IAAIA,CACHC,KAAK,6BACLC,GAAG,OAFAF,2CAIH,UAACG,EAAAA,CAAMA,CAAAA,CAACC,QAAQ,YAAYC,KAAK,cAChCnB,EAAE,8DAKT,UAACS,EAAAA,CAAGA,CAAAA,CAACD,UAAU,gBACb,UAACE,EAAAA,CAAGA,CAAAA,CAACC,GAAI,YACP,UAACS,EAAAA,OAAoBA,CAAAA,CAAAA,UAOzBC,EAA2BC,CAAAA,EAAAA,EAAAA,uBAAAA,CAAuBA,CAAC,IAAM,UAACpB,EAAAA,CAAAA,IAC1DhC,EAAYqD,CAAAA,EAAAA,EAAAA,EAAAA,CAAWA,CAAC,GAAWrD,SACzC,IAAI,GAAEA,GAAAA,OAAAA,EAAAA,EAAOC,IAAPD,OAAOC,GAAPD,OAAAA,EAAAA,EAAoBa,gBAAgB,EAApCb,KAAAA,EAAAA,CAAsC,CAAC,GAAvCA,UAAoD,EAIxD,UAACmD,EAAAA,CAAAA,GAHM,UAACG,EAAAA,OAAeA,CAAAA,CAAAA,EAK3B,6GChBA,SAASC,EAASC,CAAoB,EACpC,GAAM,GAAE1B,CAAC,CAAE,CAAGC,CAAAA,EAAAA,EAAAA,EAAAA,CAAcA,CAAC,UACvB0B,EAA6B,CACjCC,gBAAiB5B,EAAE,cACnB,EACI,SACJ6B,CAAO,MACPC,CAAI,WACJC,CAAS,uBACTC,CAAqB,WACrBC,CAAS,oBACTC,CAAkB,qBAClBC,CAAmB,CACnBC,kBAAgB,aAChBC,CAAW,oBACXC,CAAkB,gBAClBC,CAAc,SACdC,CAAO,WACPC,CAAS,sBACTC,CAAoB,mBACpBC,CAAiB,YACjBC,CAAU,QACVC,CAAM,kBACNC,CAAgB,cAChBC,CAAY,CAEZ,CADA,EACGC,EACJ,CAAGtB,EAGEuB,EAAiB,4BACrBtB,EACAuB,gBAAiBlD,EAAE,IAP0C,MAQ7DmD,UAAU,UACVtB,EACAC,KAAMA,GAAQ,EAAE,CAChBsB,OAAO,EACPC,2BAA4BrB,EAC5BsB,UAAWrB,EACXsB,gBAAiBf,qBACjBN,EACAsB,YAAY,EACZC,iBAAkBhB,EAClBiB,kBAAmBpB,GAA0C,GAC7DqB,eADwCrB,cACVD,GAA4B,CAAC,GAAI,GAAI,GAAI,CAA3BA,EAA+B,GAAG,CAC9EuB,oBAAqB7B,EACrB8B,oBAAqB1B,EACrB2B,aAAc1B,iBACdG,uBACAG,oBACAC,EACAoB,kBAAmB,UAACC,EAAAA,CAAYA,CAAAA,CAAAA,GAChCC,SAAU,UAACC,IAAAA,CAAE1D,UAAU,6CACvBoC,SACAC,eACAE,mBACAD,EACAtC,UAAW,WACb,EACA,MACE,UAAC2D,EAAAA,EAASA,CAAAA,CAAE,GAAGlB,CAAc,EAEjC,CAEAxB,EAAS2C,YAAY,CAAG,CACtBd,WAAW,EACXE,YAAY,EACZzB,UAAW,KACXU,UAAW,GACXC,qBAAsB,KACtBC,mBAAmB,EACnBC,YAAY,EACZE,kBAAkB,CACpB,EAEA,MAAerB,QAAQA,EAAC,oKCexB,MApH6B,IACzB,GAAM,CAAC4C,EAAWC,EAAe,CAAGC,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAC,EAAE,EACzC,EAAGC,EAAW,CAAGD,CAAAA,EAAAA,EAAAA,MAkHQnD,EAlHRmD,CAAQA,EAAC,GAC1B,CAACxC,EAAW0C,EAAa,CAAGF,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAC,GACrC,CAACG,EAASC,EAAW,CAAGJ,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAC,IACjC,CAACK,EAAaC,EAAS,CAAGN,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,EAAC,GACnC,CAACO,EAAuBC,EAAyB,CAAGR,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAC,CAAC,GAC9DS,EAAY,IAAMH,EAAS,IAC3B,GAAE7E,CAAC,CAAE,CAAGC,CAAAA,EAAAA,EAAAA,EAAAA,CAAcA,CAAC,UAEvB4B,EAAU,CACZ,CACIoD,KAAMjF,EAAE,SACRkF,SAAU,OACd,EACA,CACID,KAAMjF,EAAE,UACRkF,SAAU,GACVC,KAAM,GACF,WAACC,MAAAA,WACG,UAACtE,IAAIA,CAACC,KAAK,6BAA6BC,GAAI,OAAvCF,iCAAqF,OAANuE,EAAEC,GAAG,WAErF,UAACpB,IAAAA,CAAE1D,UAAU,uBAEV,OAEP,UAAC+E,IAAAA,CAAEC,QAAS,IAAMC,EAAWJ,YACzB,UAACnB,IAAAA,CAAE1D,UAAU,4BACZ,MAGjB,EACH,CACKkF,EAAwB,CAC1BC,KAAM,CAAE9E,MAAO,KAAM,EACrB+E,MAAOlB,EACPmB,KAAM,EACNC,MAAO,CAAC,CACZ,EAEAC,CAAAA,EAAAA,EAAAA,SAAAA,CAASA,CAAC,KACNC,GACJ,EAAG,EAAE,EAEL,IAAMA,EAAyB,UAC3BxB,GAAW,GACX,IAAMyB,EAAW,MAAMC,EAAAA,CAAUA,CAACC,GAAG,CAAC,mBAAoBT,GACtDO,GAAYA,EAASnE,IAAI,EAAImE,EAASnE,IAAI,CAACsE,MAAM,CAAG,GAAG,CACvD9B,EAAe2B,EAASnE,IAAI,EAC5B2C,EAAawB,EAASI,UAAU,EAChC7B,GAAW,GAEnB,EAQMrC,EAAsB,MAAOmE,EAAiBT,KAChDH,EAAsBE,KAAK,CAAGU,EAC9BZ,EAAsBG,IAAI,CAAGA,EAC7BrB,EAAW,IACX,IAAMyB,EAAW,MAAMC,EAAAA,CAAUA,CAACC,GAAG,CAAC,mBAAoBT,GACtDO,GAAYA,EAASnE,IAAI,EAAImE,EAASnE,IAAI,CAACsE,MAAM,CAAG,GAAG,CACvD9B,EAAe2B,EAASnE,IAAI,EAC5B6C,EAAW2B,GACX9B,GAAW,GAEnB,EAEM+B,EAAe,UACjB,GAAI,CACA,MAAML,EAAAA,CAAUA,CAACM,MAAM,CAAC,oBAA0C,OAAtB1B,IAC5CkB,IACAnB,GAAS,GACT4B,EAAAA,EAAKA,CAACC,OAAO,CAAC1G,EAAE,mEACpB,CAAE,MAAO2G,EAAO,CACZF,EAAAA,EAAKA,CAACE,KAAK,CAAC3G,EAAE,6DAClB,CACJ,EAEMyF,EAAa,MAAOmB,IACtB7B,EAAyB6B,EAAItB,GAAG,EAChCT,GAAS,EACb,EAEA,MACI,WAACO,MAAAA,WACG,WAACyB,EAAAA,CAAKA,CAAAA,CAACC,KAAMlC,EAAamC,OAAQ/B,YAC9B,UAAC6B,EAAAA,CAAKA,CAACG,MAAM,EAACC,WAAW,aACrB,UAACJ,EAAAA,CAAKA,CAACK,KAAK,WAAElH,EAAE,6CAEpB,UAAC6G,EAAAA,CAAKA,CAACM,IAAI,WAAEnH,EAAE,yCACf,WAAC6G,EAAAA,CAAKA,CAACO,MAAM,YACT,UAACnG,EAAAA,CAAMA,CAAAA,CAACC,QAAQ,YAAYsE,QAASR,WAChChF,EAAE,YAEP,UAACiB,EAAAA,CAAMA,CAAAA,CAACC,QAAQ,UAAUsE,QAASe,WAC9BvG,EAAE,eAKf,UAACyB,EAAAA,CAAQA,CAAAA,CACLI,QAASA,EACTC,KAAMuC,EACNtC,UAAWA,EACXU,WAAW,EACXN,oBAAqBA,EACrBC,iBAzDcyD,CAyDIzD,GAxD1BsD,EAAsBE,KAAK,CAAGlB,EAC9BgB,EAAsBG,IAAI,CAAGA,EAC7BG,GACJ,MAyDJ,6EC9He,SAASxE,EAAgBzB,CAAW,EAC/C,MACE,UAACqF,MAAAA,CAAI5E,UAAU,sDACb,UAAC4E,MAAAA,CAAI5E,UAAU,mBAAU,yCAG/B,gECFa,SAASI,EAAYc,CAAuB,EACzD,MACE,UAAC2F,KAAAA,CAAG7G,UAAU,wBAAgBkB,EAAMb,KAAK,EAE7C", "sources": ["webpack://_N_E/./pages/adminsettings/permissions.tsx", "webpack://_N_E/?d73f", "webpack://_N_E/./pages/adminsettings/institutiontypes/index.tsx", "webpack://_N_E/./components/common/RKITable.tsx", "webpack://_N_E/./pages/adminsettings/institutiontypes/institutionTypeTable.tsx", "webpack://_N_E/./pages/rNoAccess.tsx", "webpack://_N_E/./components/common/PageHeading.tsx"], "sourcesContent": ["//Import services/components\r\nimport connectedAuthWrapper from 'redux-auth-wrapper/connectedAuthWrapper';\r\n\r\nconst create = \"create:any\";\r\nexport const canAddAreaOfWork = connectedAuthWrapper({\r\n  authenticatedSelector: (state) => {\r\n    if (state.permissions && state.permissions.area_of_work && state.permissions.area_of_work[create]) {\r\n      return true;\r\n    }\r\n    return false;\r\n  },\r\n  wrapperDisplayName: 'CanAddAreaOfWork',\r\n});\r\n\r\nexport const canAddCountry = connectedAuthWrapper({\r\n  authenticatedSelector: (state) => {\r\n    if (state.permissions && state.permissions.country && state.permissions.country[create]) {\r\n      return true;\r\n    }\r\n    return false;\r\n  },\r\n  wrapperDisplayName: 'CanAddCountry',\r\n});\r\n\r\nexport const canAddDeploymentStatus = connectedAuthWrapper({\r\n  authenticatedSelector: (state) => {\r\n    if (state.permissions && state.permissions.deployment_status && state.permissions.deployment_status[create]) {\r\n      return true;\r\n    }\r\n    return false;\r\n  },\r\n  wrapperDisplayName: 'CanAddDeploymentStatus',\r\n});\r\n\r\nexport const canAddEventStatus = connectedAuthWrapper({\r\n  authenticatedSelector: (state) => {\r\n    if (state.permissions && state.permissions.event_status && state.permissions.event_status[create]) {\r\n      return true;\r\n    }\r\n    return false;\r\n  },\r\n  wrapperDisplayName: 'CanAddEventStatus',\r\n});\r\n\r\nexport const canAddExpertise = connectedAuthWrapper({\r\n  authenticatedSelector: (state) => {\r\n    if (state.permissions && state.permissions.expertise && state.permissions.expertise[create]) {\r\n      return true;\r\n    }\r\n    return false;\r\n  },\r\n  wrapperDisplayName: 'CanAddExpertise',\r\n});\r\n\r\nexport const canAddFocalPointApproval = connectedAuthWrapper({\r\n  authenticatedSelector: (state) => {\r\n    if (state.permissions && state.permissions.institution_focal_point && state.permissions.institution_focal_point[create]) {\r\n      return true;\r\n    }\r\n    return false;\r\n  },\r\n  wrapperDisplayName: 'CanAddFocalPointApproval',\r\n});\r\n\r\nexport const canAddVspaceApproval = connectedAuthWrapper({\r\n  authenticatedSelector: (state) => {\r\n    if (state.permissions && state.permissions.institution_focal_point && state.permissions.institution_focal_point[create]) {\r\n      return true;\r\n    }\r\n    return false;\r\n  },\r\n  wrapperDisplayName: 'CanAddVspaceApproval',\r\n});\r\nexport const canAddHazards = connectedAuthWrapper({\r\n  authenticatedSelector: (state) => {\r\n    if (state.permissions && state.permissions.hazard && state.permissions.hazard[create]) {\r\n      return true;\r\n    }\r\n    return false;\r\n  },\r\n  wrapperDisplayName: 'CanAddHazards',\r\n});\r\n\r\nexport const canAddHazardTypes = connectedAuthWrapper({\r\n  authenticatedSelector: (state) => {\r\n    if (state.permissions && state.permissions.hazard_type && state.permissions.hazard_type[create]) {\r\n      return true;\r\n    }\r\n    return false;\r\n  },\r\n  wrapperDisplayName: 'CanAddHazardTypes',\r\n}); \r\n\r\nexport const canAddOrganisationApproval = connectedAuthWrapper({\r\n  authenticatedSelector: (state) => {\r\n    if (state.permissions && state.permissions.institution && state.permissions.institution[create]) {\r\n      return true;\r\n    }\r\n    return false;\r\n  },\r\n  wrapperDisplayName: 'CanAddOrganisationApproval',\r\n}); \r\n\r\nexport const canAddOrganisationNetworks = connectedAuthWrapper({\r\n  authenticatedSelector: (state) => {\r\n    if (state.permissions && state.permissions.institution_network && state.permissions.institution_network[create]) {\r\n      return true;\r\n    }\r\n    return false;\r\n  },\r\n  wrapperDisplayName: 'CanAddOrganisationNetworks',\r\n});\r\n\r\nexport const canAddOrganisationTypes = connectedAuthWrapper({\r\n  authenticatedSelector: (state) => {\r\n    if (state.permissions && state.permissions.institution_type && state.permissions.institution_type[create]) {\r\n      return true;\r\n    }\r\n    return false;\r\n  },\r\n  wrapperDisplayName: 'CanAddOrganisationTypes',\r\n});\r\n\r\nexport const canAddOperationStatus = connectedAuthWrapper({\r\n  authenticatedSelector: (state) => {\r\n    if (state.permissions && state.permissions.operation_status && state.permissions.operation_status[create]) {\r\n      return true;\r\n    }\r\n    return false;\r\n  },\r\n  wrapperDisplayName: 'CanAddOperationStatus',\r\n});\r\n\r\nexport const canAddProjectStatus = connectedAuthWrapper({\r\n  authenticatedSelector: (state) => {\r\n    if (state.permissions && state.permissions.project_status && state.permissions.project_status[create]) {\r\n      return true;\r\n    }\r\n    return false;\r\n  },\r\n  wrapperDisplayName: 'CanAddProjectStatus',\r\n});\r\n\r\nexport const canAddRegions = connectedAuthWrapper({\r\n  authenticatedSelector: (state) => {\r\n    if (state.permissions && state.permissions.region && state.permissions.region[create]) {\r\n      return true;\r\n    }\r\n    return false;\r\n  },\r\n  wrapperDisplayName: 'CanAddRegions',\r\n});\r\n\r\nexport const canAddRiskLevels = connectedAuthWrapper({\r\n  authenticatedSelector: (state) => {\r\n    if (state.permissions && state.permissions.risk_level && state.permissions.risk_level[create]) {\r\n      return true;\r\n    }\r\n    return false;\r\n  },\r\n  wrapperDisplayName: 'CanAddRiskLevels',\r\n});\r\n\r\nexport const canAddSyndromes = connectedAuthWrapper({\r\n  authenticatedSelector: (state) => {\r\n    if (state.permissions && state.permissions.syndrome && state.permissions.syndrome[create]) {\r\n      return true;\r\n    }\r\n    return false;\r\n  },\r\n  wrapperDisplayName: 'CanAddSyndromes',\r\n});\r\n\r\nexport const canAddUpdateTypes = connectedAuthWrapper({\r\n  authenticatedSelector: (state) => {\r\n    if (state.permissions && state.permissions.update_type && state.permissions.update_type[create]) {\r\n      return true;\r\n    }\r\n    return false;\r\n  },\r\n  wrapperDisplayName: 'CanAddUpdateTypes',\r\n});\r\n\r\nexport const canAddUsers = connectedAuthWrapper({\r\n  authenticatedSelector: (state) => {\r\n    if (state.permissions && state.permissions.users && state.permissions.users[create]) {\r\n      return true;\r\n    }\r\n    return false;\r\n  },\r\n  wrapperDisplayName: 'CanAddUsers',\r\n});\r\n\r\nexport const canAddWorldRegion = connectedAuthWrapper({\r\n  authenticatedSelector: (state) => {\r\n    if (state.permissions && state.permissions.worl_region && state.permissions.worl_region[create]) {\r\n      return true;\r\n    }\r\n    return false;\r\n  },\r\n  wrapperDisplayName: 'CanAddWorldRegion',\r\n});\r\n\r\nexport const canAddLandingPage = connectedAuthWrapper({\r\n  authenticatedSelector: (state) => {\r\n    if (state.permissions && state.permissions.landing_page && state.permissions.landing_page[create]) {\r\n      return true;\r\n    }\r\n    return false;\r\n  },\r\n  wrapperDisplayName: 'CanAddLandingPage',\r\n});\r\n\r\nexport const canAddContent = connectedAuthWrapper({\r\n  authenticatedSelector: (state) => {\r\n    if (state.permissions && state.permissions.operation && state.permissions.operation[create] && state.permissions.project && state.permissions.project[create]&& state.permissions.event && state.permissions.event[create]&& state.permissions.vspace && state.permissions.vspace[create]&& state.permissions.institution && state.permissions.institution[create]&& state.permissions.update && state.permissions.update[create]) {\r\n      return true;\r\n    }\r\n    return false;\r\n  },\r\n  wrapperDisplayName: 'CanAddContent',\r\n});\r\n\r\nexport default canAddAreaOfWork;", "\n    (window.__NEXT_P = window.__NEXT_P || []).push([\n      \"/adminsettings/institutiontypes\",\n      function () {\n        return require(\"private-next-pages/adminsettings/institutiontypes/index.tsx\");\n      }\n    ]);\n    if(module.hot) {\n      module.hot.dispose(function () {\n        window.__NEXT_P.push([\"/adminsettings/institutiontypes\"])\n      });\n    }\n  ", "//Import Library\r\nimport { Contain<PERSON>, <PERSON>, <PERSON>, Button } from \"react-bootstrap\";\r\nimport Link from \"next/link\";\r\n\r\n//Import services/components\r\nimport PageHeading from \"../../../components/common/PageHeading\";\r\nimport InstitutionTypeTable from \"./institutionTypeTable\";\r\nimport { useTranslation } from 'next-i18next';\r\nimport { canAddOrganisationTypes } from \"../permissions\";\r\nimport { useSelector } from \"react-redux\";\r\nimport NoAccessMessage from \"../../rNoAccess\";\r\n\r\n\r\n\r\nconst InstitutionTypeIndex = (_props: any) => {\r\n  const { t } = useTranslation('common');\r\n  const ShowInstitutionTypeIndex = () => {\r\n    return (\r\n      <Container style={{ overflowX: \"hidden\" }} fluid className=\"p-0\">\r\n        <Row>\r\n          <Col xs={12}>\r\n            <PageHeading title={t(\"adminsetting.Organisationtypes.OrganisationType\")} />\r\n          </Col>\r\n        </Row>\r\n        <Row>\r\n          <Col xs={12}>\r\n            <Link\r\n              href=\"/adminsettings/[...routes]\"\r\n              as=\"/adminsettings/create_institution_type\"\r\n              >\r\n              <Button variant=\"secondary\" size=\"sm\">\r\n              {t(\"adminsetting.Organisationtypes.AddOrganisationType\")}\r\n              </Button>\r\n            </Link>\r\n          </Col>\r\n        </Row>\r\n        <Row className=\"mt-3\">\r\n          <Col xs={12}>\r\n            <InstitutionTypeTable />\r\n          </Col>\r\n        </Row>\r\n      </Container>\r\n    );\r\n  }\r\n  \r\n  const ShowAddOrganisationTypes = canAddOrganisationTypes(() => <ShowInstitutionTypeIndex />);\r\n  const state:any = useSelector((state) => state);\r\n  if (!(state?.permissions?.institution_type?.['create:any'])) {\r\n    return <NoAccessMessage />\r\n  }\r\n  return(\r\n    <ShowAddOrganisationTypes />\r\n  )\r\n}\r\nexport default InstitutionTypeIndex;", "//Import Library\r\nimport React from 'react'\r\nimport DataTable  from 'react-data-table-component';\r\n\r\n//Import services/components\r\nimport { useTranslation } from 'next-i18next';\r\nimport CustomLoader from './CustomLoader';\r\n\r\ninterface RKITableProps {\r\n  columns: any[];\r\n  data: any[];\r\n  totalRows?: number;\r\n  resetPaginationToggle?: boolean;\r\n  subheader?: boolean;\r\n  subHeaderComponent?: React.ReactNode;\r\n  handlePerRowsChange?: (newPerPage: number, page: number) => void;\r\n  handlePageChange?: (page: number) => void;\r\n  rowsPerPage?: number[];\r\n  defaultRowsPerPage?: number;\r\n  selectableRows?: boolean;\r\n  loading?: boolean;\r\n  pagServer?: boolean;\r\n  onSelectedRowsChange?: (selectedRows: any) => void;\r\n  clearSelectedRows?: boolean;\r\n  sortServer?: boolean;\r\n  onSort?: (column: any, sortDirection: string) => void;\r\n  persistTableHead?: boolean;\r\n  sortFunction?: (rows: any[], field: any, direction: 'asc' | 'desc') => any[];\r\n  noHeader?: boolean;\r\n  dense?: boolean;\r\n  pagination?: boolean;\r\n  paginationServer?: boolean;\r\n  paginationTotalRows?: number;\r\n  subHeaderAlign?: string;\r\n  [key: string]: any;\r\n}\r\n\r\nfunction RKITable(props: RKITableProps) {\r\n  const { t } = useTranslation('common');\r\n  const paginationComponentOptions = {\r\n    rowsPerPageText: t('Rowsperpage'),\r\n    };\r\n  const {\r\n    columns,\r\n    data,\r\n    totalRows,\r\n    resetPaginationToggle,\r\n    subheader,\r\n    subHeaderComponent,\r\n    handlePerRowsChange,\r\n    handlePageChange,\r\n    rowsPerPage,\r\n    defaultRowsPerPage,\r\n    selectableRows,\r\n    loading,\r\n    pagServer,\r\n    onSelectedRowsChange,\r\n    clearSelectedRows,\r\n    sortServer,\r\n    onSort,\r\n    persistTableHead,\r\n    sortFunction,\r\n    // Filter out any unknown props that might cause DOM warnings\r\n    ...otherProps\r\n  } = props;\r\n\r\n  // Only pass known DataTable props to avoid DOM warnings\r\n  const dataTableProps = {\r\n    paginationComponentOptions,\r\n    noDataComponent: t(\"NoData\"),\r\n    noHeader: true,\r\n    columns,\r\n    data: data || [],\r\n    dense: true,\r\n    paginationResetDefaultPage: resetPaginationToggle,\r\n    subHeader: subheader,\r\n    progressPending: loading,\r\n    subHeaderComponent,\r\n    pagination: true,\r\n    paginationServer: pagServer,\r\n    paginationPerPage: defaultRowsPerPage ? defaultRowsPerPage : 10,\r\n    paginationRowsPerPageOptions: rowsPerPage ? rowsPerPage : [10, 15, 20, 25, 30],\r\n    paginationTotalRows: totalRows,\r\n    onChangeRowsPerPage: handlePerRowsChange,\r\n    onChangePage: handlePageChange,\r\n    selectableRows,\r\n    onSelectedRowsChange,\r\n    clearSelectedRows,\r\n    progressComponent: <CustomLoader/>,\r\n    sortIcon: <i className=\"sort-icon fas fa-exchange-alt\" />,\r\n    sortServer,\r\n    onSort,\r\n    sortFunction,\r\n    persistTableHead,\r\n    className: \"rki-table\"\r\n  };\r\n  return (\r\n    <DataTable {...dataTableProps} />\r\n  )\r\n};\r\n\r\nRKITable.defaultProps = {\r\n  subHeader: false,\r\n  pagination: true,\r\n  totalRows: null,\r\n  pagServer: true,\r\n  onSelectedRowsChange: null,\r\n  clearSelectedRows: false,\r\n  sortServer: false,\r\n  persistTableHead: false\r\n}\r\n\r\nexport default RKITable;\r\n\r\n", "//Import Library\r\nimport Link from \"next/link\";\r\nimport { useState, useEffect } from \"react\";\r\nimport { <PERSON><PERSON>, But<PERSON> } from \"react-bootstrap\";\r\nimport toast from 'react-hot-toast';\r\n\r\n//Import services/components\r\nimport RKITable from \"../../../components/common/RKITable\";\r\nimport apiService from \"../../../services/apiService\";\r\nimport { useTranslation } from 'next-i18next';\r\n\r\nconst InstitutionTypeTable = (_props: any) => {\r\n    const [tabledata, setDataToTable] = useState([]);\r\n    const [, setLoading] = useState(false);\r\n    const [totalRows, setTotalRows] = useState(0);\r\n    const [perPage, setPerPage] = useState(10);\r\n    const [isModalShow, setModal] = useState(false);\r\n    const [selectInstitutionType, setSelectInstitutionType] = useState({});\r\n    const modalHide = () => setModal(false);\r\n    const { t } = useTranslation('common');\r\n\r\n    const columns = [\r\n        {\r\n            name: t(\"Title\"),\r\n            selector: \"title\",\r\n        },\r\n        {\r\n            name: t(\"action\"),\r\n            selector: \"\",\r\n            cell: (d: any) => (\r\n                <div>\r\n                    <Link href=\"/adminsettings/[...routes]\" as={`/adminsettings/edit_institution_type/${d._id}`}>\r\n\r\n                        <i className=\"icon fas fa-edit\" />\r\n\r\n                    </Link>\r\n                    &nbsp;\r\n                    <a onClick={() => userAction(d)}>\r\n                        <i className=\"icon fas fa-trash-alt\" />\r\n                    </a>{\" \"}\r\n                </div>\r\n            ),\r\n        },\r\n    ];\r\n    const institutionTypeParams = {\r\n        sort: { title: \"asc\" },\r\n        limit: perPage,\r\n        page: 1,\r\n        query: {},\r\n    };\r\n\r\n    useEffect(() => {\r\n        getInstitutionTypeData();\r\n    }, []);\r\n\r\n    const getInstitutionTypeData = async () => {\r\n        setLoading(true);\r\n        const response = await apiService.get(\"/institutiontype\", institutionTypeParams);\r\n        if (response && response.data && response.data.length > 0) {\r\n            setDataToTable(response.data);\r\n            setTotalRows(response.totalCount);\r\n            setLoading(false);\r\n        }\r\n    };\r\n\r\n    const handlePageChange = (page: any) => {\r\n        institutionTypeParams.limit = perPage;\r\n        institutionTypeParams.page = page;\r\n        getInstitutionTypeData();\r\n    };\r\n\r\n    const handlePerRowsChange = async (newPerPage: any, page: any) => {\r\n        institutionTypeParams.limit = newPerPage;\r\n        institutionTypeParams.page = page;\r\n        setLoading(true);\r\n        const response = await apiService.get(\"/institutiontype\", institutionTypeParams);\r\n        if (response && response.data && response.data.length > 0) {\r\n            setDataToTable(response.data);\r\n            setPerPage(newPerPage);\r\n            setLoading(false);\r\n        }\r\n    };\r\n\r\n    const modalConfirm = async () => {\r\n        try {\r\n            await apiService.remove(`/institutiontype/${selectInstitutionType}`);\r\n            getInstitutionTypeData();\r\n            setModal(false);\r\n            toast.success(t(\"adminsetting.Organisationtypes.Table.orgTypeDeletedSuccessfully\"));\r\n        } catch (error) {\r\n            toast.error(t(\"adminsetting.Organisationtypes.Table.errorDeletingOrgType\"));\r\n        }\r\n    };\r\n\r\n    const userAction = async (row: any) => {\r\n        setSelectInstitutionType(row._id);\r\n        setModal(true);\r\n    };\r\n\r\n    return (\r\n        <div>\r\n            <Modal show={isModalShow} onHide={modalHide}>\r\n                <Modal.Header closeButton>\r\n                    <Modal.Title>{t(\"adminsetting.Organisationtypes.Delete\")}</Modal.Title>\r\n                </Modal.Header>\r\n                <Modal.Body>{t(\"adminsetting.Organisationtypes.sure\")}</Modal.Body>\r\n                <Modal.Footer>\r\n                    <Button variant=\"secondary\" onClick={modalHide}>\r\n                        {t(\"Cancel\")}\r\n                    </Button>\r\n                    <Button variant=\"primary\" onClick={modalConfirm}>\r\n                        {t(\"yes\")}\r\n                    </Button>\r\n                </Modal.Footer>\r\n            </Modal>\r\n\r\n            <RKITable\r\n                columns={columns}\r\n                data={tabledata}\r\n                totalRows={totalRows}\r\n                pagServer={true}\r\n                handlePerRowsChange={handlePerRowsChange}\r\n                handlePageChange={handlePageChange}\r\n            />\r\n        </div>\r\n    );\r\n};\r\nexport default InstitutionTypeTable;\r\n", "export default function NoAccessMessage(_props: any) {\r\n    return (\r\n      <div className=\"container-fluid p-0 response-message-block\">\r\n        <div className=\"message\">you don't have permission to access</div>\r\n      </div>\r\n    )\r\n  }", "interface PageHeadingProps {\r\n  title: string; // Required based on actual usage\r\n}\r\n\r\nexport default function PageHeading(props: PageHeadingProps) {\r\n  return (\r\n    <h2 className=\"page-heading\">{props.title}</h2>\r\n  )\r\n}\r\n"], "names": ["create", "canAddAreaOfWork", "connectedAuthWrapper", "authenticatedSelector", "state", "permissions", "area_of_work", "wrapperDisplayName", "country", "deployment_status", "event_status", "expertise", "institution_focal_point", "hazard", "hazard_type", "institution", "institution_network", "institution_type", "operation_status", "project_status", "region", "risk_level", "syndrome", "update_type", "canAddUsers", "users", "worl_region", "landing_page", "operation", "project", "event", "vspace", "update", "_props", "t", "useTranslation", "ShowInstitutionTypeIndex", "InstitutionTypeIndex", "Container", "style", "overflowX", "fluid", "className", "Row", "Col", "xs", "PageHeading", "title", "Link", "href", "as", "<PERSON><PERSON>", "variant", "size", "InstitutionTypeTable", "ShowAddOrganisationTypes", "canAddOrganisationTypes", "useSelector", "NoAccessMessage", "RKITable", "props", "paginationComponentOptions", "rowsPerPageText", "columns", "data", "totalRows", "resetPaginationToggle", "subheader", "subHeaderComponent", "handlePerRowsChange", "handlePageChange", "rowsPerPage", "defaultRowsPerPage", "selectableRows", "loading", "pagServer", "onSelectedRowsChange", "clearSelectedRows", "sortServer", "onSort", "persistTableHead", "sortFunction", "otherProps", "dataTableProps", "noDataComponent", "<PERSON><PERSON><PERSON><PERSON>", "dense", "paginationResetDefaultPage", "subHeader", "progressPending", "pagination", "paginationServer", "paginationPerPage", "paginationRowsPerPageOptions", "paginationTotalRows", "onChangeRowsPerPage", "onChangePage", "progressComponent", "CustomLoader", "sortIcon", "i", "DataTable", "defaultProps", "tabledata", "setDataToTable", "useState", "setLoading", "setTotalRows", "perPage", "setPerPage", "isModalShow", "setModal", "selectInstitutionType", "setSelectInstitutionType", "modalHide", "name", "selector", "cell", "div", "d", "_id", "a", "onClick", "userAction", "institutionTypeParams", "sort", "limit", "page", "query", "useEffect", "getInstitutionTypeData", "response", "apiService", "get", "length", "totalCount", "newPerPage", "modalConfirm", "remove", "toast", "success", "error", "row", "Modal", "show", "onHide", "Header", "closeButton", "Title", "Body", "Footer", "h2"], "sourceRoot": "", "ignoreList": []}