"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/_app",{

/***/ "(pages-dir-browser)/./components/layout/authenticated/SideBar.tsx":
/*!*****************************************************!*\
  !*** ./components/layout/authenticated/SideBar.tsx ***!
  \*****************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ SideBar)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(pages-dir-browser)/./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _navigation_NavMenuList__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../../navigation/NavMenuList */ \"(pages-dir-browser)/./components/navigation/NavMenuList.tsx\");\n//Import services/components\n\n\nconst navMenuItems = __webpack_require__(/*! ../../../api/menu.json */ \"(pages-dir-browser)/./api/menu.json\");\nfunction SideBar(props) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"sideMenu\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_navigation_NavMenuList__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n            items: navMenuItems\n        }, void 0, false, {\n            fileName: \"D:\\\\rki\\\\frontend\\\\components\\\\layout\\\\authenticated\\\\SideBar.tsx\",\n            lineNumber: 13,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\rki\\\\frontend\\\\components\\\\layout\\\\authenticated\\\\SideBar.tsx\",\n        lineNumber: 12,\n        columnNumber: 5\n    }, this);\n}\n_c = SideBar;\nvar _c;\n$RefreshReg$(_c, \"SideBar\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHBhZ2VzLWRpci1icm93c2VyKS8uL2NvbXBvbmVudHMvbGF5b3V0L2F1dGhlbnRpY2F0ZWQvU2lkZUJhci50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7OztBQUFBLDRCQUE0Qjs7QUFDMkI7QUFFdkQsTUFBTUMsZUFBZUMsbUJBQU9BLENBQUMsbUVBQXdCO0FBTXRDLFNBQVNDLFFBQVFDLEtBQW1CO0lBQ2pELHFCQUNFLDhEQUFDQztRQUFJQyxXQUFVO2tCQUNiLDRFQUFDTiwrREFBV0E7WUFBQ08sT0FBT047Ozs7Ozs7Ozs7O0FBRzFCO0tBTndCRSIsInNvdXJjZXMiOlsiRDpcXHJraVxcZnJvbnRlbmRcXGNvbXBvbmVudHNcXGxheW91dFxcYXV0aGVudGljYXRlZFxcU2lkZUJhci50c3giXSwic291cmNlc0NvbnRlbnQiOlsiLy9JbXBvcnQgc2VydmljZXMvY29tcG9uZW50c1xuaW1wb3J0IE5hdk1lbnVMaXN0IGZyb20gJy4uLy4uL25hdmlnYXRpb24vTmF2TWVudUxpc3QnO1xuXG5jb25zdCBuYXZNZW51SXRlbXMgPSByZXF1aXJlKCcuLi8uLi8uLi9hcGkvbWVudS5qc29uJyk7XG5cbmludGVyZmFjZSBTaWRlQmFyUHJvcHMge1xuICBba2V5OiBzdHJpbmddOiBhbnk7XG59XG5cbmV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIFNpZGVCYXIocHJvcHM6IFNpZGVCYXJQcm9wcykge1xuICByZXR1cm4gKFxuICAgIDxkaXYgY2xhc3NOYW1lPVwic2lkZU1lbnVcIj5cbiAgICAgIDxOYXZNZW51TGlzdCBpdGVtcz17bmF2TWVudUl0ZW1zfSAvPlxuICAgIDwvZGl2PlxuICApXG59XG4iXSwibmFtZXMiOlsiTmF2TWVudUxpc3QiLCJuYXZNZW51SXRlbXMiLCJyZXF1aXJlIiwiU2lkZUJhciIsInByb3BzIiwiZGl2IiwiY2xhc3NOYW1lIiwiaXRlbXMiXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(pages-dir-browser)/./components/layout/authenticated/SideBar.tsx\n"));

/***/ })

});