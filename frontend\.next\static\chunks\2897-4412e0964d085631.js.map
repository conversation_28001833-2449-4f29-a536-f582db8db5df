{"version": 3, "file": "static/chunks/2897-4412e0964d085631.js", "mappings": "oPAQO,gBAKP,0BAOA,OANA,GAAgB,UAAQ,mBACxB,QACA,CAAG,sBAEH,SARA,GAAoB,oBAAc,CAQlC,GARkC,EAQlC,IACA,CAAG,EACH,CACA,CAiEA,kBACA,kCC9EA,iCACA,sCACA,YACG,CACH,EAuBA,cAGA,gBAKA,IAJA,EAEA,uBAEA,sBAA+C,OAAsB,KAUrE,KAV+E,EAG/E,SACA,cACA,aACA,CAAO,CACP,eACA,cACA,EACA,CACA,CAlBE,OAAc,MAoBhB,kBAqEA,OAnEA,+BACA,gBACA,eACA,cACA,aACA,CACA,CAAK,CACL,EAEA,kCACA,eACA,EAEA,yCACA,ID6BA,EACA,EC9BA,aACA,iBAEA,OACA,SAFA,cDgBA,yBACA,MAAW,kBAAY,IACvB,wBACA,CCjBgF,EDiBhF,GACA,cClBmG,MAA/C,GDmBpD,kBCnBoD,GDoBpD,gBCpBoD,EDqBpD,CAAK,CACL,CAAG,GAKH,YADA,WA/DO,KAIP,cACA,wBAJA,CAKI,CALJ,MACA,QAQA,IAcA,EAdA,sBACA,KAEA,eACA,OACA,WACA,OACA,MAGA,UAKA,SAEA,gBACA,QACA,QAAkB,cAAqC,KACvD,cACA,eACA,CAGA,SACA,CAGA,CAHI,GAGJ,IAAc,WAAwB,IACtC,gBAGA,QACA,ECPmG,EDyBnG,kBAEA,oBACA,WACA,GAAS,oBAAc,KACvB,WC9BmG,ED+BnG,SACA,OACA,EAAoB,oBAAc,iBAElC,CAFsE,EAEtE,QAEA,KAAsB,kBAAY,IAClC,wBACA,MACA,gBCxCmG,GDyCnG,kBCzCmG,ED0CnG,CAAO,EACD,SAMA,MAA+B,oBAAc,KAInD,MAAsB,kBAAY,IAClC,wBACA,cACA,gBCxDmG,GDyDnG,kBCzDmG,ED0DnG,EAAO,EAZP,KAAsB,kBAAY,IAClC,KACA,CAAO,EAYP,CAAG,EACH,GC5DA,cACA,CACA,EAAI,EAGJ,2BACA,MAA8B,EAAe,oBAC7C,cAEA,kBACA,oBAGA,cACA,0BACA,MAAuB,OAAQ,GAAG,aAGlC,OADA,gBACA,CACA,UACA,CACA,CAAO,EAEP,EAEA,oBACA,iBACA,cACA,iBACA,EAAgB,OAA6B,iCAE7C,0BACA,sCAKA,CAJA,gBACA,eACA,cAEA,UAC0B,eAAmB,CAAC,GAAsB,WACpE,OACA,CAAO,IAGiB,eAAmB,CAAC,GAAsB,WAClE,OACA,CAAK,CAAe,eAAmB,QACvC,EAEA,CACA,CAAC,CAAC,WAAe,EAEjB,YAyDE,EAzD0B,CA0D5B,KA1DiE,GAAG,CAyDnE,CAAC,IACF,CA5KA,CACA,gBACA,yBACA,QACA,CACA,iBCHA,gDAeA,2CACA,cACA,kBACA,aACA,iBACA,OACA,WACA,MAAgB,OAAwB,MACxC,EAAgB,YAAM,OACtB,GACA,UACA,SACA,CAAK,CACL,SACA,UACA,oCACA,CAAK,CACL,SACA,SACA,CAAK,CACL,QACA,SACA,CACA,EACA,OAAsB,eAAmB,CAAC,IAAU,EACpD,gBACA,iBACA,KACA,UACA,SACA,CAAG,aACH,OACA,MAAa,OAAa,GAAG,OAC7B,KACA,EACA,OAAwB,eAAmB,GAAM,OAAQ,EACzD,YACA,CAAK,IACL,CAAG,CACH,EASA,cACA,iBACA,OACA,aACA,EAAY,YAAM,OAClB,EAAkB,cAAQ,SAC1B,EAAiB,OAAc,MAC/B,OACA,OACE,eAAS,YACX,gBACA,MAUA,8CACA,yCACA,CAAK,EACL,kBACA,qCACA,EACA,CAAG,KACH,kBACA,UACA,QACA,OACA,OACA,CACA,eACA,OACA,QACA,2BAxCA,IAwCA,cACA,CACA,cACA,OACA,OACA,CACA,CACA,EACA,OAAsB,eAAmB,CAAC,IAAU,EACpD,SACA,gBACA,iBACA,KACA,oBACA,gBACA,GACA,eACA,CAAK,CACL,QA1DA,IA2DA,SACA,CAAG,aACH,OAAwB,eAAmB,QAC3C,MACA,MAAa,OAAa,EAC1B,kBACA,mBACA,CAAO,MACP,CAAK,GACL,CAAG,CACH,EAEA,oBAwCA,gBACA,eAWA,cACA,kBAEA,IADgB,OAAwB,OAExC,OAAsB,eAAmB,CAAC,EAAiB,OAAQ,EACnE,QADyD,EACzD,CACA,CAAG,IACH,EACA,cACA,iBACA,EAAY,OAAwB,MACpC,YACA,aACA,eACA,gBACA,eACA,6BACA,EAAkB,cAAQ,UAC1B,EAAiB,OAAc,MAC/B,OACA,OACA,EAAmB,cAAQ,KAC3B,EAAiB,OAAc,MAC/B,OACA,OACE,eAAS,YACX,OACA,KAEA,CAAG,QACD,eAAS,YACX,UACA,MAEA,KACA,CAAG,UACH,iBACA,YACA,EAiBA,EAAsB,OAAa,CAAC,OAAa,GAAG,MAAiB,EACrE,MAAW,OAAa,CAAC,OAAa,GAAG,2BAA+E,EACxH,6BACA,CAAK,CACL,CAAG,EAKH,MAJiB,OAAa,CAAC,OAAa,GAAG,MAAY,EAC3D,aACA,SAAc,UAAc,gBAvB5B,YACA,MAAgC,gBAAoB,KAEpD,yBACA,OAA4B,cAAkB,IAC9C,UACA,CAAS,EAIT,6BACA,WAEA,CACA,QACA,EASA,CAAG,CAEH,EAGA,sEACA,aACA,gEACA,EAAmB,OAAiB,EACpC,YACA,CAAG,EACH,UACA,eACA,gBACA,gBACA,mBACA,EAAW,OAAwB,MACnC,MAAS,OAAa,EACtB,MArQA,YACA,KACA,WACA,SACA,QACA,OACA,MAAkB,OAAwB,MAC1C,OAAwB,eAAmB,CA8P3C,EA9P2C,EAC3C,EA8PA,WAlIA,YACA,WACA,aACA,EAAc,OAAwB,MACtC,OAAwB,eAAmB,IAC3C,KACA,UACA,CAAK,CAAe,eAAmB,CA2HvC,EA3H0D,OAAQ,EAClE,kBACA,CAAK,KACL,EAyHA,YAnHA,YACA,OAAwB,eAAmB,GAAO,OAAQ,EAC1D,UAiHA,EAhHA,mBA9FA,IA8FA,CACA,CAAK,IACL,EA+GA,YAxGA,YACA,OAAwB,eAAmB,GAAO,OAAQ,EAC1D,UAsGA,CArGA,CAAK,IACL,EAqGA,eA7FA,YACA,iBAAwC,eAAmB,GAAwB,OAAQ,EAC3F,UA2FA,CA1FA,CAAK,KAAyB,eAAmB,CAAC,EAAiB,OAAQ,EAC3E,IDR8B,ICOmC,EA0FjE,CAxFA,CAAK,IACL,CAwFA,CAAG,GACH,EACA,KACA,SACA,aACA,cACA,cACA,iBACA,MAAY,OAAU,gIC/RtB,qJACA,aACA,gEACA,yCACA,yCACA,0BACA,4CACA,4CACA,mBACA,EACA,GACA,8BACA,8BACA,CAAG,CACH,mCACA,+BACA,eACA,CAAK,sBACL,eACA,EAAK,CACL,CAAG,CACH,+BACA,OACA,QACA,QACA,YACA,CACA,CACA,iBCHA,MAPmC,gBAAU,eAE7C,IDSA,EACA,EACA,EACA,EACA,EACA,EACA,EACA,EACA,EACA,EACA,EACA,EACA,EACA,EACA,EACA,EACA,EACA,EACA,EACA,EACA,EACA,EACA,EACA,EACA,EACA,EAMA,EAGA,EC3CA,GDWA,WCXoB,CDUpB,GADA,ECVuB,OAAe,KDWtC,0BACA,EAEA,YADA,0BACA,SAEA,YADA,uBACA,sBAEA,YADA,sBACA,qBAEA,YADA,sBACA,qBACA,mBAEA,YADA,aACA,KACA,aAGA,YADA,GADA,EAAsB,OAAwB,OAC9C,gBAC0D,GAAc,GAExE,YADA,oBAC2D,GAAc,GACzE,eACA,cACA,YACA,UACA,SACA,EAAkB,aAAO,YACzB,WAAwC,OAAU,OAClD,iBACA,gBACA,CAAK,kBACL,CAAG,oBACH,EAAgB,aAAO,YACvB,wCAAwH,OAAkB,eAA4B,OAAkB,UACxL,CAAG,cACH,EAAiB,iBAAW,eAC5B,8BACA,cAEA,6BACA,sBACA,cAAqD,CACrD,aAMA,EAAsB,OAAY,aAAoB,OAAkB,CAAC,OAAU,aALnF,CACA,uBACA,OACA,QACA,EAEA,CACA,MACA,CACA,MACA,CAAG,oBACM,OAAa,CAAC,OAAa,GAAG,MAAsB,EAC7D,UACA,UACA,CAAG,GChEH,OAAsB,eAAmB,CAAC,GAAM,CAAE,OAAQ,EAC1D,KACA,CAAG,IACH,CAAC", "sources": ["webpack://_N_E/./node_modules/react-transition-group/esm/utils/ChildMapping.js", "webpack://_N_E/./node_modules/react-transition-group/esm/TransitionGroup.js", "webpack://_N_E/./node_modules/react-select/animated/dist/react-select-animated.esm.js", "webpack://_N_E/./node_modules/react-select/dist/useCreatable-84008237.esm.js", "webpack://_N_E/./node_modules/react-select/creatable/dist/react-select-creatable.esm.js"], "sourcesContent": ["import { Children, cloneElement, isValidElement } from 'react';\n/**\n * Given `this.props.children`, return an object mapping key to child.\n *\n * @param {*} children `this.props.children`\n * @return {object} Mapping of key to child\n */\n\nexport function getChildMapping(children, mapFn) {\n  var mapper = function mapper(child) {\n    return mapFn && isValidElement(child) ? mapFn(child) : child;\n  };\n\n  var result = Object.create(null);\n  if (children) Children.map(children, function (c) {\n    return c;\n  }).forEach(function (child) {\n    // run the map function here instead so that the key is the computed one\n    result[child.key] = mapper(child);\n  });\n  return result;\n}\n/**\n * When you're adding or removing children some may be added or removed in the\n * same render pass. We want to show *both* since we want to simultaneously\n * animate elements in and out. This function takes a previous set of keys\n * and a new set of keys and merges them with its best guess of the correct\n * ordering. In the future we may expose some of the utilities in\n * ReactMultiChild to make this easy, but for now React itself does not\n * directly have this concept of the union of prevChildren and nextChildren\n * so we implement it here.\n *\n * @param {object} prev prev children as returned from\n * `ReactTransitionChildMapping.getChildMapping()`.\n * @param {object} next next children as returned from\n * `ReactTransitionChildMapping.getChildMapping()`.\n * @return {object} a key set that contains all keys in `prev` and all keys\n * in `next` in a reasonable order.\n */\n\nexport function mergeChildMappings(prev, next) {\n  prev = prev || {};\n  next = next || {};\n\n  function getValueForKey(key) {\n    return key in next ? next[key] : prev[key];\n  } // For each key of `next`, the list of keys to insert before that key in\n  // the combined list\n\n\n  var nextKeysPending = Object.create(null);\n  var pendingKeys = [];\n\n  for (var prevKey in prev) {\n    if (prevKey in next) {\n      if (pendingKeys.length) {\n        nextKeysPending[prevKey] = pendingKeys;\n        pendingKeys = [];\n      }\n    } else {\n      pendingKeys.push(prevKey);\n    }\n  }\n\n  var i;\n  var childMapping = {};\n\n  for (var nextKey in next) {\n    if (nextKeysPending[nextKey]) {\n      for (i = 0; i < nextKeysPending[nextKey].length; i++) {\n        var pendingNextKey = nextKeysPending[nextKey][i];\n        childMapping[nextKeysPending[nextKey][i]] = getValueForKey(pendingNextKey);\n      }\n    }\n\n    childMapping[nextKey] = getValueForKey(nextKey);\n  } // Finally, add the keys which didn't appear before any key in `next`\n\n\n  for (i = 0; i < pendingKeys.length; i++) {\n    childMapping[pendingKeys[i]] = getValueForKey(pendingKeys[i]);\n  }\n\n  return childMapping;\n}\n\nfunction getProp(child, prop, props) {\n  return props[prop] != null ? props[prop] : child.props[prop];\n}\n\nexport function getInitialChildMapping(props, onExited) {\n  return getChildMapping(props.children, function (child) {\n    return cloneElement(child, {\n      onExited: onExited.bind(null, child),\n      in: true,\n      appear: getProp(child, 'appear', props),\n      enter: getProp(child, 'enter', props),\n      exit: getProp(child, 'exit', props)\n    });\n  });\n}\nexport function getNextChildMapping(nextProps, prevChildMapping, onExited) {\n  var nextChildMapping = getChildMapping(nextProps.children);\n  var children = mergeChildMappings(prevChildMapping, nextChildMapping);\n  Object.keys(children).forEach(function (key) {\n    var child = children[key];\n    if (!isValidElement(child)) return;\n    var hasPrev = (key in prevChildMapping);\n    var hasNext = (key in nextChildMapping);\n    var prevChild = prevChildMapping[key];\n    var isLeaving = isValidElement(prevChild) && !prevChild.props.in; // item is new (entering)\n\n    if (hasNext && (!hasPrev || isLeaving)) {\n      // console.log('entering', key)\n      children[key] = cloneElement(child, {\n        onExited: onExited.bind(null, child),\n        in: true,\n        exit: getProp(child, 'exit', nextProps),\n        enter: getProp(child, 'enter', nextProps)\n      });\n    } else if (!hasNext && hasPrev && !isLeaving) {\n      // item is old (exiting)\n      // console.log('leaving', key)\n      children[key] = cloneElement(child, {\n        in: false\n      });\n    } else if (hasNext && hasPrev && isValidElement(prevChild)) {\n      // item hasn't changed transition states\n      // copy over the last transition props;\n      // console.log('unchanged', key)\n      children[key] = cloneElement(child, {\n        onExited: onExited.bind(null, child),\n        in: prevChild.props.in,\n        exit: getProp(child, 'exit', nextProps),\n        enter: getProp(child, 'enter', nextProps)\n      });\n    }\n  });\n  return children;\n}", "import _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _assertThisInitialized from \"@babel/runtime/helpers/esm/assertThisInitialized\";\nimport _inheritsLoose from \"@babel/runtime/helpers/esm/inheritsLoose\";\nimport PropTypes from 'prop-types';\nimport React from 'react';\nimport TransitionGroupContext from './TransitionGroupContext';\nimport { getChildMapping, getInitialChildMapping, getNextChildMapping } from './utils/ChildMapping';\n\nvar values = Object.values || function (obj) {\n  return Object.keys(obj).map(function (k) {\n    return obj[k];\n  });\n};\n\nvar defaultProps = {\n  component: 'div',\n  childFactory: function childFactory(child) {\n    return child;\n  }\n};\n/**\n * The `<TransitionGroup>` component manages a set of transition components\n * (`<Transition>` and `<CSSTransition>`) in a list. Like with the transition\n * components, `<TransitionGroup>` is a state machine for managing the mounting\n * and unmounting of components over time.\n *\n * Consider the example below. As items are removed or added to the TodoList the\n * `in` prop is toggled automatically by the `<TransitionGroup>`.\n *\n * Note that `<TransitionGroup>`  does not define any animation behavior!\n * Exactly _how_ a list item animates is up to the individual transition\n * component. This means you can mix and match animations across different list\n * items.\n */\n\nvar TransitionGroup = /*#__PURE__*/function (_React$Component) {\n  _inheritsLoose(TransitionGroup, _React$Component);\n\n  function TransitionGroup(props, context) {\n    var _this;\n\n    _this = _React$Component.call(this, props, context) || this;\n\n    var handleExited = _this.handleExited.bind(_assertThisInitialized(_this)); // Initial children should all be entering, dependent on appear\n\n\n    _this.state = {\n      contextValue: {\n        isMounting: true\n      },\n      handleExited: handleExited,\n      firstRender: true\n    };\n    return _this;\n  }\n\n  var _proto = TransitionGroup.prototype;\n\n  _proto.componentDidMount = function componentDidMount() {\n    this.mounted = true;\n    this.setState({\n      contextValue: {\n        isMounting: false\n      }\n    });\n  };\n\n  _proto.componentWillUnmount = function componentWillUnmount() {\n    this.mounted = false;\n  };\n\n  TransitionGroup.getDerivedStateFromProps = function getDerivedStateFromProps(nextProps, _ref) {\n    var prevChildMapping = _ref.children,\n        handleExited = _ref.handleExited,\n        firstRender = _ref.firstRender;\n    return {\n      children: firstRender ? getInitialChildMapping(nextProps, handleExited) : getNextChildMapping(nextProps, prevChildMapping, handleExited),\n      firstRender: false\n    };\n  } // node is `undefined` when user provided `nodeRef` prop\n  ;\n\n  _proto.handleExited = function handleExited(child, node) {\n    var currentChildMapping = getChildMapping(this.props.children);\n    if (child.key in currentChildMapping) return;\n\n    if (child.props.onExited) {\n      child.props.onExited(node);\n    }\n\n    if (this.mounted) {\n      this.setState(function (state) {\n        var children = _extends({}, state.children);\n\n        delete children[child.key];\n        return {\n          children: children\n        };\n      });\n    }\n  };\n\n  _proto.render = function render() {\n    var _this$props = this.props,\n        Component = _this$props.component,\n        childFactory = _this$props.childFactory,\n        props = _objectWithoutPropertiesLoose(_this$props, [\"component\", \"childFactory\"]);\n\n    var contextValue = this.state.contextValue;\n    var children = values(this.state.children).map(childFactory);\n    delete props.appear;\n    delete props.enter;\n    delete props.exit;\n\n    if (Component === null) {\n      return /*#__PURE__*/React.createElement(TransitionGroupContext.Provider, {\n        value: contextValue\n      }, children);\n    }\n\n    return /*#__PURE__*/React.createElement(TransitionGroupContext.Provider, {\n      value: contextValue\n    }, /*#__PURE__*/React.createElement(Component, props, children));\n  };\n\n  return TransitionGroup;\n}(React.Component);\n\nTransitionGroup.propTypes = process.env.NODE_ENV !== \"production\" ? {\n  /**\n   * `<TransitionGroup>` renders a `<div>` by default. You can change this\n   * behavior by providing a `component` prop.\n   * If you use React v16+ and would like to avoid a wrapping `<div>` element\n   * you can pass in `component={null}`. This is useful if the wrapping div\n   * borks your css styles.\n   */\n  component: PropTypes.any,\n\n  /**\n   * A set of `<Transition>` components, that are toggled `in` and out as they\n   * leave. the `<TransitionGroup>` will inject specific transition props, so\n   * remember to spread them through if you are wrapping the `<Transition>` as\n   * with our `<Fade>` example.\n   *\n   * While this component is meant for multiple `Transition` or `CSSTransition`\n   * children, sometimes you may want to have a single transition child with\n   * content that you want to be transitioned out and in when you change it\n   * (e.g. routes, images etc.) In that case you can change the `key` prop of\n   * the transition child as you change its content, this will cause\n   * `TransitionGroup` to transition the child out and back in.\n   */\n  children: PropTypes.node,\n\n  /**\n   * A convenience prop that enables or disables appear animations\n   * for all children. Note that specifying this will override any defaults set\n   * on individual children Transitions.\n   */\n  appear: PropTypes.bool,\n\n  /**\n   * A convenience prop that enables or disables enter animations\n   * for all children. Note that specifying this will override any defaults set\n   * on individual children Transitions.\n   */\n  enter: PropTypes.bool,\n\n  /**\n   * A convenience prop that enables or disables exit animations\n   * for all children. Note that specifying this will override any defaults set\n   * on individual children Transitions.\n   */\n  exit: PropTypes.bool,\n\n  /**\n   * You may need to apply reactive updates to a child as it is exiting.\n   * This is generally done by using `cloneElement` however in the case of an exiting\n   * child the element has already been removed and not accessible to the consumer.\n   *\n   * If you do need to update a child as it leaves you can provide a `childFactory`\n   * to wrap every child, even the ones that are leaving.\n   *\n   * @type Function(child: ReactElement) -> ReactElement\n   */\n  childFactory: PropTypes.func\n} : {};\nTransitionGroup.defaultProps = defaultProps;\nexport default TransitionGroup;", "import _objectSpread from '@babel/runtime/helpers/esm/objectSpread2';\nimport _objectWithoutProperties from '@babel/runtime/helpers/esm/objectWithoutProperties';\nimport memoizeOne from 'memoize-one';\nimport { F as defaultComponents } from '../../dist/index-641ee5b8.esm.js';\nimport * as React from 'react';\nimport { useRef, useState, useEffect } from 'react';\nimport _extends from '@babel/runtime/helpers/esm/extends';\nimport _slicedToArray from '@babel/runtime/helpers/esm/slicedToArray';\nimport { Transition, TransitionGroup } from 'react-transition-group';\nimport '@emotion/react';\nimport '@babel/runtime/helpers/typeof';\nimport '@babel/runtime/helpers/taggedTemplateLiteral';\nimport '@babel/runtime/helpers/defineProperty';\nimport 'react-dom';\nimport '@floating-ui/dom';\nimport 'use-isomorphic-layout-effect';\n\nvar _excluded$4 = [\"in\", \"onExited\", \"appear\", \"enter\", \"exit\"];\n// strip transition props off before spreading onto select component\nvar AnimatedInput = function AnimatedInput(WrappedComponent) {\n  return function (_ref) {\n    _ref.in;\n      _ref.onExited;\n      _ref.appear;\n      _ref.enter;\n      _ref.exit;\n      var props = _objectWithoutProperties(_ref, _excluded$4);\n    return /*#__PURE__*/React.createElement(WrappedComponent, props);\n  };\n};\nvar AnimatedInput$1 = AnimatedInput;\n\nvar _excluded$3 = [\"component\", \"duration\", \"in\", \"onExited\"];\nvar Fade = function Fade(_ref) {\n  var Tag = _ref.component,\n    _ref$duration = _ref.duration,\n    duration = _ref$duration === void 0 ? 1 : _ref$duration,\n    inProp = _ref.in;\n    _ref.onExited;\n    var props = _objectWithoutProperties(_ref, _excluded$3);\n  var nodeRef = useRef(null);\n  var transition = {\n    entering: {\n      opacity: 0\n    },\n    entered: {\n      opacity: 1,\n      transition: \"opacity \".concat(duration, \"ms\")\n    },\n    exiting: {\n      opacity: 0\n    },\n    exited: {\n      opacity: 0\n    }\n  };\n  return /*#__PURE__*/React.createElement(Transition, {\n    mountOnEnter: true,\n    unmountOnExit: true,\n    in: inProp,\n    timeout: duration,\n    nodeRef: nodeRef\n  }, function (state) {\n    var innerProps = {\n      style: _objectSpread({}, transition[state]),\n      ref: nodeRef\n    };\n    return /*#__PURE__*/React.createElement(Tag, _extends({\n      innerProps: innerProps\n    }, props));\n  });\n};\n\n// ==============================\n// Collapse Transition\n// ==============================\n\nvar collapseDuration = 260;\n// wrap each MultiValue with a collapse transition; decreases width until\n// finally removing from DOM\nvar Collapse = function Collapse(_ref2) {\n  var children = _ref2.children,\n    _in = _ref2.in,\n    _onExited = _ref2.onExited;\n  var ref = useRef(null);\n  var _useState = useState('auto'),\n    _useState2 = _slicedToArray(_useState, 2),\n    width = _useState2[0],\n    setWidth = _useState2[1];\n  useEffect(function () {\n    var el = ref.current;\n    if (!el) return;\n\n    /*\n      Here we're invoking requestAnimationFrame with a callback invoking our\n      call to getBoundingClientRect and setState in order to resolve an edge case\n      around portalling. Certain portalling solutions briefly remove children from the DOM\n      before appending them to the target node. This is to avoid us trying to call getBoundingClientrect\n      while the Select component is in this state.\n    */\n    // cannot use `offsetWidth` because it is rounded\n    var rafId = window.requestAnimationFrame(function () {\n      return setWidth(el.getBoundingClientRect().width);\n    });\n    return function () {\n      return window.cancelAnimationFrame(rafId);\n    };\n  }, []);\n  var getStyleFromStatus = function getStyleFromStatus(status) {\n    switch (status) {\n      default:\n        return {\n          width: width\n        };\n      case 'exiting':\n        return {\n          width: 0,\n          transition: \"width \".concat(collapseDuration, \"ms ease-out\")\n        };\n      case 'exited':\n        return {\n          width: 0\n        };\n    }\n  };\n  return /*#__PURE__*/React.createElement(Transition, {\n    enter: false,\n    mountOnEnter: true,\n    unmountOnExit: true,\n    in: _in,\n    onExited: function onExited() {\n      var el = ref.current;\n      if (!el) return;\n      _onExited === null || _onExited === void 0 ? void 0 : _onExited(el);\n    },\n    timeout: collapseDuration,\n    nodeRef: ref\n  }, function (status) {\n    return /*#__PURE__*/React.createElement(\"div\", {\n      ref: ref,\n      style: _objectSpread({\n        overflow: 'hidden',\n        whiteSpace: 'nowrap'\n      }, getStyleFromStatus(status))\n    }, children);\n  });\n};\n\nvar _excluded$2 = [\"in\", \"onExited\"];\n// strip transition props off before spreading onto actual component\n\nvar AnimatedMultiValue = function AnimatedMultiValue(WrappedComponent) {\n  return function (_ref) {\n    var inProp = _ref.in,\n      onExited = _ref.onExited,\n      props = _objectWithoutProperties(_ref, _excluded$2);\n    return /*#__PURE__*/React.createElement(Collapse, {\n      in: inProp,\n      onExited: onExited\n    }, /*#__PURE__*/React.createElement(WrappedComponent, _extends({\n      cropWithEllipsis: inProp\n    }, props)));\n  };\n};\nvar AnimatedMultiValue$1 = AnimatedMultiValue;\n\n// fade in when last multi-value removed, otherwise instant\nvar AnimatedPlaceholder = function AnimatedPlaceholder(WrappedComponent) {\n  return function (props) {\n    return /*#__PURE__*/React.createElement(Fade, _extends({\n      component: WrappedComponent,\n      duration: props.isMulti ? collapseDuration : 1\n    }, props));\n  };\n};\nvar AnimatedPlaceholder$1 = AnimatedPlaceholder;\n\n// instant fade; all transition-group children must be transitions\n\nvar AnimatedSingleValue = function AnimatedSingleValue(WrappedComponent) {\n  return function (props) {\n    return /*#__PURE__*/React.createElement(Fade, _extends({\n      component: WrappedComponent\n    }, props));\n  };\n};\nvar AnimatedSingleValue$1 = AnimatedSingleValue;\n\nvar _excluded$1 = [\"component\"],\n  _excluded2 = [\"children\"];\n// make ValueContainer a transition group\nvar AnimatedValueContainer = function AnimatedValueContainer(WrappedComponent) {\n  return function (props) {\n    return props.isMulti ? /*#__PURE__*/React.createElement(IsMultiValueContainer, _extends({\n      component: WrappedComponent\n    }, props)) : /*#__PURE__*/React.createElement(TransitionGroup, _extends({\n      component: WrappedComponent\n    }, props));\n  };\n};\nvar IsMultiValueContainer = function IsMultiValueContainer(_ref) {\n  var component = _ref.component,\n    restProps = _objectWithoutProperties(_ref, _excluded$1);\n  var multiProps = useIsMultiValueContainer(restProps);\n  return /*#__PURE__*/React.createElement(TransitionGroup, _extends({\n    component: component\n  }, multiProps));\n};\nvar useIsMultiValueContainer = function useIsMultiValueContainer(_ref2) {\n  var children = _ref2.children,\n    props = _objectWithoutProperties(_ref2, _excluded2);\n  var isMulti = props.isMulti,\n    hasValue = props.hasValue,\n    innerProps = props.innerProps,\n    _props$selectProps = props.selectProps,\n    components = _props$selectProps.components,\n    controlShouldRenderValue = _props$selectProps.controlShouldRenderValue;\n  var _useState = useState(isMulti && controlShouldRenderValue && hasValue),\n    _useState2 = _slicedToArray(_useState, 2),\n    cssDisplayFlex = _useState2[0],\n    setCssDisplayFlex = _useState2[1];\n  var _useState3 = useState(false),\n    _useState4 = _slicedToArray(_useState3, 2),\n    removingValue = _useState4[0],\n    setRemovingValue = _useState4[1];\n  useEffect(function () {\n    if (hasValue && !cssDisplayFlex) {\n      setCssDisplayFlex(true);\n    }\n  }, [hasValue, cssDisplayFlex]);\n  useEffect(function () {\n    if (removingValue && !hasValue && cssDisplayFlex) {\n      setCssDisplayFlex(false);\n    }\n    setRemovingValue(false);\n  }, [removingValue, hasValue, cssDisplayFlex]);\n  var onExited = function onExited() {\n    return setRemovingValue(true);\n  };\n  var childMapper = function childMapper(child) {\n    if (isMulti && /*#__PURE__*/React.isValidElement(child)) {\n      // Add onExited callback to MultiValues\n      if (child.type === components.MultiValue) {\n        return /*#__PURE__*/React.cloneElement(child, {\n          onExited: onExited\n        });\n      }\n      // While container flexed, Input cursor is shown after Placeholder text,\n      // so remove Placeholder until display is set back to grid\n      if (child.type === components.Placeholder && cssDisplayFlex) {\n        return null;\n      }\n    }\n    return child;\n  };\n  var newInnerProps = _objectSpread(_objectSpread({}, innerProps), {}, {\n    style: _objectSpread(_objectSpread({}, innerProps === null || innerProps === void 0 ? void 0 : innerProps.style), {}, {\n      display: isMulti && hasValue || cssDisplayFlex ? 'flex' : 'grid'\n    })\n  });\n  var newProps = _objectSpread(_objectSpread({}, props), {}, {\n    innerProps: newInnerProps,\n    children: React.Children.toArray(children).map(childMapper)\n  });\n  return newProps;\n};\nvar AnimatedValueContainer$1 = AnimatedValueContainer;\n\nvar _excluded = [\"Input\", \"MultiValue\", \"Placeholder\", \"SingleValue\", \"ValueContainer\"];\nvar makeAnimated = function makeAnimated() {\n  var externalComponents = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {};\n  var components = defaultComponents({\n    components: externalComponents\n  });\n  var Input = components.Input,\n    MultiValue = components.MultiValue,\n    Placeholder = components.Placeholder,\n    SingleValue = components.SingleValue,\n    ValueContainer = components.ValueContainer,\n    rest = _objectWithoutProperties(components, _excluded);\n  return _objectSpread({\n    Input: AnimatedInput$1(Input),\n    MultiValue: AnimatedMultiValue$1(MultiValue),\n    Placeholder: AnimatedPlaceholder$1(Placeholder),\n    SingleValue: AnimatedSingleValue$1(SingleValue),\n    ValueContainer: AnimatedValueContainer$1(ValueContainer)\n  }, rest);\n};\nvar AnimatedComponents = makeAnimated();\nvar Input = AnimatedComponents.Input;\nvar MultiValue = AnimatedComponents.MultiValue;\nvar Placeholder = AnimatedComponents.Placeholder;\nvar SingleValue = AnimatedComponents.SingleValue;\nvar ValueContainer = AnimatedComponents.ValueContainer;\nvar index = memoizeOne(makeAnimated);\n\nexport { Input, MultiValue, Placeholder, SingleValue, ValueContainer, index as default };\n", "import _objectSpread from '@babel/runtime/helpers/esm/objectSpread2';\nimport _toConsumableArray from '@babel/runtime/helpers/esm/toConsumableArray';\nimport _objectWithoutProperties from '@babel/runtime/helpers/esm/objectWithoutProperties';\nimport { useMemo, useCallback } from 'react';\nimport { H as cleanValue, D as valueTernary } from './index-641ee5b8.esm.js';\nimport { g as getOptionValue, b as getOptionLabel } from './Select-aab027f3.esm.js';\n\nvar _excluded = [\"allowCreateWhileLoading\", \"createOptionPosition\", \"formatCreateLabel\", \"isValidNewOption\", \"getNewOptionData\", \"onCreateOption\", \"options\", \"onChange\"];\nvar compareOption = function compareOption() {\n  var inputValue = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : '';\n  var option = arguments.length > 1 ? arguments[1] : undefined;\n  var accessors = arguments.length > 2 ? arguments[2] : undefined;\n  var candidate = String(inputValue).toLowerCase();\n  var optionValue = String(accessors.getOptionValue(option)).toLowerCase();\n  var optionLabel = String(accessors.getOptionLabel(option)).toLowerCase();\n  return optionValue === candidate || optionLabel === candidate;\n};\nvar builtins = {\n  formatCreateLabel: function formatCreateLabel(inputValue) {\n    return \"Create \\\"\".concat(inputValue, \"\\\"\");\n  },\n  isValidNewOption: function isValidNewOption(inputValue, selectValue, selectOptions, accessors) {\n    return !(!inputValue || selectValue.some(function (option) {\n      return compareOption(inputValue, option, accessors);\n    }) || selectOptions.some(function (option) {\n      return compareOption(inputValue, option, accessors);\n    }));\n  },\n  getNewOptionData: function getNewOptionData(inputValue, optionLabel) {\n    return {\n      label: optionLabel,\n      value: inputValue,\n      __isNew__: true\n    };\n  }\n};\nfunction useCreatable(_ref) {\n  var _ref$allowCreateWhile = _ref.allowCreateWhileLoading,\n    allowCreateWhileLoading = _ref$allowCreateWhile === void 0 ? false : _ref$allowCreateWhile,\n    _ref$createOptionPosi = _ref.createOptionPosition,\n    createOptionPosition = _ref$createOptionPosi === void 0 ? 'last' : _ref$createOptionPosi,\n    _ref$formatCreateLabe = _ref.formatCreateLabel,\n    formatCreateLabel = _ref$formatCreateLabe === void 0 ? builtins.formatCreateLabel : _ref$formatCreateLabe,\n    _ref$isValidNewOption = _ref.isValidNewOption,\n    isValidNewOption = _ref$isValidNewOption === void 0 ? builtins.isValidNewOption : _ref$isValidNewOption,\n    _ref$getNewOptionData = _ref.getNewOptionData,\n    getNewOptionData = _ref$getNewOptionData === void 0 ? builtins.getNewOptionData : _ref$getNewOptionData,\n    onCreateOption = _ref.onCreateOption,\n    _ref$options = _ref.options,\n    propsOptions = _ref$options === void 0 ? [] : _ref$options,\n    propsOnChange = _ref.onChange,\n    restSelectProps = _objectWithoutProperties(_ref, _excluded);\n  var _restSelectProps$getO = restSelectProps.getOptionValue,\n    getOptionValue$1 = _restSelectProps$getO === void 0 ? getOptionValue : _restSelectProps$getO,\n    _restSelectProps$getO2 = restSelectProps.getOptionLabel,\n    getOptionLabel$1 = _restSelectProps$getO2 === void 0 ? getOptionLabel : _restSelectProps$getO2,\n    inputValue = restSelectProps.inputValue,\n    isLoading = restSelectProps.isLoading,\n    isMulti = restSelectProps.isMulti,\n    value = restSelectProps.value,\n    name = restSelectProps.name;\n  var newOption = useMemo(function () {\n    return isValidNewOption(inputValue, cleanValue(value), propsOptions, {\n      getOptionValue: getOptionValue$1,\n      getOptionLabel: getOptionLabel$1\n    }) ? getNewOptionData(inputValue, formatCreateLabel(inputValue)) : undefined;\n  }, [formatCreateLabel, getNewOptionData, getOptionLabel$1, getOptionValue$1, inputValue, isValidNewOption, propsOptions, value]);\n  var options = useMemo(function () {\n    return (allowCreateWhileLoading || !isLoading) && newOption ? createOptionPosition === 'first' ? [newOption].concat(_toConsumableArray(propsOptions)) : [].concat(_toConsumableArray(propsOptions), [newOption]) : propsOptions;\n  }, [allowCreateWhileLoading, createOptionPosition, isLoading, newOption, propsOptions]);\n  var onChange = useCallback(function (newValue, actionMeta) {\n    if (actionMeta.action !== 'select-option') {\n      return propsOnChange(newValue, actionMeta);\n    }\n    var valueArray = Array.isArray(newValue) ? newValue : [newValue];\n    if (valueArray[valueArray.length - 1] === newOption) {\n      if (onCreateOption) onCreateOption(inputValue);else {\n        var newOptionData = getNewOptionData(inputValue, inputValue);\n        var newActionMeta = {\n          action: 'create-option',\n          name: name,\n          option: newOptionData\n        };\n        propsOnChange(valueTernary(isMulti, [].concat(_toConsumableArray(cleanValue(value)), [newOptionData]), newOptionData), newActionMeta);\n      }\n      return;\n    }\n    propsOnChange(newValue, actionMeta);\n  }, [getNewOptionData, inputValue, isMulti, name, newOption, onCreateOption, propsOnChange, value]);\n  return _objectSpread(_objectSpread({}, restSelectProps), {}, {\n    options: options,\n    onChange: onChange\n  });\n}\n\nexport { useCreatable as u };\n", "import _extends from '@babel/runtime/helpers/esm/extends';\nimport * as React from 'react';\nimport { forwardRef } from 'react';\nimport { S as Select } from '../../dist/Select-aab027f3.esm.js';\nimport { u as useStateManager } from '../../dist/useStateManager-7e1e8489.esm.js';\nimport { u as useCreatable } from '../../dist/useCreatable-84008237.esm.js';\nexport { u as useCreatable } from '../../dist/useCreatable-84008237.esm.js';\nimport '@babel/runtime/helpers/objectSpread2';\nimport '@babel/runtime/helpers/classCallCheck';\nimport '@babel/runtime/helpers/createClass';\nimport '@babel/runtime/helpers/inherits';\nimport '@babel/runtime/helpers/createSuper';\nimport '@babel/runtime/helpers/toConsumableArray';\nimport '../../dist/index-641ee5b8.esm.js';\nimport '@emotion/react';\nimport '@babel/runtime/helpers/slicedToArray';\nimport '@babel/runtime/helpers/objectWithoutProperties';\nimport '@babel/runtime/helpers/typeof';\nimport '@babel/runtime/helpers/taggedTemplateLiteral';\nimport '@babel/runtime/helpers/defineProperty';\nimport 'react-dom';\nimport '@floating-ui/dom';\nimport 'use-isomorphic-layout-effect';\nimport 'memoize-one';\n\nvar CreatableSelect = /*#__PURE__*/forwardRef(function (props, ref) {\n  var creatableProps = useStateManager(props);\n  var selectProps = useCreatable(creatableProps);\n  return /*#__PURE__*/React.createElement(Select, _extends({\n    ref: ref\n  }, selectProps));\n});\nvar CreatableSelect$1 = CreatableSelect;\n\nexport { CreatableSelect$1 as default };\n"], "names": [], "sourceRoot": "", "ignoreList": [0, 1, 2, 3, 4]}