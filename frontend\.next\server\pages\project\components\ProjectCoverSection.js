"use strict";(()=>{var e={};e.id=950,e.ids=[636,950,3220],e.modules={123:e=>{e.exports=require("dom-helpers/removeEventListener")},1332:e=>{e.exports=require("react-custom-scrollbars-2")},1428:e=>{e.exports=import("axios")},1680:e=>{e.exports=require("@restart/ui/utils")},1919:e=>{e.exports=require("react-transition-group/Transition")},3892:e=>{e.exports=require("classnames")},4048:e=>{e.exports=require("react-truncate")},6009:e=>{e.exports=require("dom-helpers/ownerDocument")},6952:e=>{e.exports=require("@restart/ui/Modal")},7374:e=>{e.exports=require("@restart/ui/Dropdown")},8732:e=>{e.exports=require("react/jsx-runtime")},9653:e=>{e.exports=require("@restart/ui/Overlay")},11242:e=>{e.exports=require("redux-persist/integration/react")},11688:e=>{e.exports=require("@restart/hooks/useWillUnmount")},12388:e=>{e.exports=require("dom-helpers/addEventListener")},13364:e=>{e.exports=require("redux-saga/effects")},14062:e=>{e.exports=import("react-redux")},14078:e=>{e.exports=import("swr")},14332:e=>{e.exports=require("uncontrollable")},16116:e=>{e.exports=require("invariant")},18622:e=>{e.exports=require("yup")},19121:e=>{e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},22326:e=>{e.exports=require("react-dom")},22541:e=>{e.exports=require("redux-persist/lib/storage")},23252:(e,r,t)=>{t.a(e,async(e,s)=>{try{t.r(r),t.d(r,{default:()=>q});var o=t(8732);t(82015);var a=t(91353),i=t(83551),n=t(19918),p=t.n(n),c=t(82053),u=t(54131),l=t(74716),d=t.n(l),x=t(88751),m=t(99775),j=t(81426),h=e([u,j]);[u,j]=h.then?(await h)():h;let q=e=>{let{t:r}=(0,x.useTranslation)("common"),t=()=>(0,o.jsx)(o.Fragment,{children:e.editAccess?(0,o.jsx)(p(),{href:"/project/[...routes]",as:`/project/edit/${e.routeData.routes[1]}`,children:(0,o.jsxs)(a.A,{variant:"secondary",size:"sm",children:[(0,o.jsx)(c.FontAwesomeIcon,{icon:u.faPen}),"\xa0",r("Edit")]})}):""}),s=(0,m.canEditProject)(()=>(0,o.jsx)(t,{}));return(0,o.jsx)(o.Fragment,{children:(0,o.jsx)(i.A,{className:"projectRow",children:(0,o.jsxs)("div",{className:"projectBanner",children:[(0,o.jsx)("div",{className:"projectImg",children:(0,o.jsx)("img",{src:"/images/project-banner.jpg",alt:"Project Detail"})}),function(e,r,t,s,a){return(0,o.jsxs)("div",{className:"projectTitleBlock",children:[(0,o.jsxs)("h4",{className:"projectTitle",children:[e.title,"\xa0\xa0",r.routes&&r.routes[1]?(0,o.jsx)(t,{project:e}):null]}),(0,o.jsxs)("div",{className:"projectDate",children:[(0,o.jsxs)("div",{className:"projectStart",children:[(0,o.jsx)("i",{className:"fas fa-calendar-alt"}),(0,o.jsxs)("div",{children:[(0,o.jsxs)("h6",{style:{color:"white"},children:[s("StartDate"),":"]}),(0,o.jsx)("h5",{children:e.start_date?d()(e.start_date).format(a):null})]})]}),(0,o.jsxs)("div",{className:"projectStatus me-2",children:[(0,o.jsx)("i",{className:"fas fa-hourglass-half"}),(0,o.jsxs)("div",{children:[(0,o.jsxs)("h6",{style:{color:"white"},children:[s("Status"),":"]}),(0,o.jsx)("h5",{children:e.status&&e.status.title})]})]}),(0,o.jsx)(j.A,{entityId:r.routes[1],entityType:"project"})]})]})}(e.projectData,e.routeData,s,r,"DD-MM-YYYY")]})})})};s()}catch(e){s(e)}})},25303:e=>{e.exports=require("@restart/ui/NavItem")},26324:e=>{e.exports=require("warning")},27825:e=>{e.exports=require("lodash")},27910:e=>{e.exports=require("stream")},28217:e=>{e.exports=require("@restart/ui/DropdownItem")},29021:e=>{e.exports=require("fs")},29294:e=>{e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},29628:e=>{e.exports=require("dom-helpers/addClass")},29825:e=>{e.exports=require("prop-types")},29841:e=>{e.exports=require("dom-helpers/hasClass")},30362:e=>{e.exports=require("@restart/ui/DropdownToggle")},33873:e=>{e.exports=require("path")},36653:e=>{e.exports=require("nprogress")},36763:(e,r,t)=>{t.a(e,async(e,s)=>{try{t.r(r),t.d(r,{config:()=>h,default:()=>d,getServerSideProps:()=>j,getStaticPaths:()=>m,getStaticProps:()=>x,reportWebVitals:()=>q,routeModule:()=>y,unstable_getServerProps:()=>v,unstable_getServerSideProps:()=>S,unstable_getStaticParams:()=>P,unstable_getStaticPaths:()=>g,unstable_getStaticProps:()=>f});var o=t(63885),a=t(80237),i=t(81413),n=t(9616),p=t.n(n),c=t(72386),u=t(23252),l=e([c,u]);[c,u]=l.then?(await l)():l;let d=(0,i.M)(u,"default"),x=(0,i.M)(u,"getStaticProps"),m=(0,i.M)(u,"getStaticPaths"),j=(0,i.M)(u,"getServerSideProps"),h=(0,i.M)(u,"config"),q=(0,i.M)(u,"reportWebVitals"),f=(0,i.M)(u,"unstable_getStaticProps"),g=(0,i.M)(u,"unstable_getStaticPaths"),P=(0,i.M)(u,"unstable_getStaticParams"),v=(0,i.M)(u,"unstable_getServerProps"),S=(0,i.M)(u,"unstable_getServerSideProps"),y=new o.PagesRouteModule({definition:{kind:a.A.PAGES,page:"/project/components/ProjectCoverSection",pathname:"/project/components/ProjectCoverSection",bundlePath:"",filename:""},components:{App:c.default,Document:p()},userland:u});s()}catch(e){s(e)}})},36955:e=>{e.exports=require("@restart/hooks/useBreakpoint")},37766:e=>{e.exports=require("@restart/hooks/useMergedRefs")},39756:e=>{e.exports=import("redux")},40051:e=>{e.exports=require("dom-helpers/removeClass")},40361:e=>{e.exports=require("next/dist/compiled/next-server/pages.runtime.prod.js")},42893:e=>{e.exports=import("react-hot-toast")},43294:e=>{e.exports=require("formik")},50009:e=>{e.exports=require("@restart/ui/DropdownMenu")},54131:e=>{e.exports=import("@fortawesome/free-solid-svg-icons")},57664:e=>{e.exports=require("dom-helpers/scrollbarSize")},58928:e=>{e.exports=require("dom-helpers/canUseDOM")},59717:e=>{e.exports=require("@restart/ui/DropdownContext")},60560:e=>{e.exports=require("dom-helpers/contains")},63033:e=>{e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},65447:e=>{e.exports=require("@restart/ui/Button")},67364:e=>{e.exports=require("@restart/hooks/useCallbackRef")},68455:e=>{e.exports=import("redux-saga")},69722:e=>{e.exports=require("es6-promise")},74075:e=>{e.exports=require("zlib")},74716:e=>{e.exports=require("moment")},74987:e=>{e.exports=require("@restart/ui/ModalManager")},78097:e=>{e.exports=require("next-redux-saga")},78634:e=>{e.exports=require("@restart/ui/Anchor")},80237:(e,r)=>{Object.defineProperty(r,"A",{enumerable:!0,get:function(){return t}});var t=function(e){return e.PAGES="PAGES",e.PAGES_API="PAGES_API",e.APP_PAGE="APP_PAGE",e.APP_ROUTE="APP_ROUTE",e.IMAGE="IMAGE",e}({})},81366:e=>{e.exports=require("redux-auth-wrapper/connectedAuthWrapper")},81413:(e,r)=>{Object.defineProperty(r,"M",{enumerable:!0,get:function(){return function e(r,t){return t in r?r[t]:"then"in r&&"function"==typeof r.then?r.then(r=>e(r,t)):"function"==typeof r&&"default"===t?r:void 0}}})},81426:(e,r,t)=>{t.a(e,async(e,s)=>{try{t.d(r,{A:()=>d});var o=t(8732),a=t(14062),i=t(54131),n=t(82015),p=t(82053),c=t(63487),u=e([a,i,c]);[a,i,c]=u.then?(await u)():u;let l={operation:"Operation",institution:"Institution",event:"Event",project:"Project",vspace:"Vspace"},d=(0,a.connect)(e=>e)(e=>{let{user:r,entityId:t,entityType:s}=e,[a,u]=(0,n.useState)(!1),[d,x]=(0,n.useState)(""),m=async()=>{if(!r?._id)return;let e=await c.A.get("/flag",{query:{entity_id:t,user:r._id,onModel:l[s]}});e&&e.data&&e.data.length>0&&(x(e.data[0]),u(!0))},j=async e=>{if(e.preventDefault(),!r?._id)return;let o=!a,i={entity_type:s,entity_id:t,user:r._id,onModel:l[s]};if(o){let e=await c.A.post("/flag",i);e&&e._id&&(x(e),u(o))}else{let e=await c.A.remove(`/flag/${d._id}`);e&&e.n&&u(o)}};return(0,n.useEffect)(()=>{m()},[]),(0,o.jsx)("div",{className:"subscribe-flag",children:(0,o.jsxs)("a",{href:"",onClick:j,children:[(0,o.jsx)("span",{className:"check",children:a?(0,o.jsx)(p.FontAwesomeIcon,{className:"clickable checkIcon",icon:i.faCheckCircle,color:"#00CC00"}):(0,o.jsx)(p.FontAwesomeIcon,{className:"clickable minusIcon",icon:i.faPlusCircle,color:"#fff"})}),(0,o.jsx)(p.FontAwesomeIcon,{className:"bookmark",icon:i.faBookmark,color:"#d4d4d4"})]})})});s()}catch(e){s(e)}})},81521:e=>{e.exports=require("dom-helpers/querySelectorAll")},81895:e=>{e.exports=require("@restart/hooks/useEventCallback")},82015:e=>{e.exports=require("react")},82053:e=>{e.exports=require("@fortawesome/react-fontawesome")},86842:e=>{e.exports=require("@restart/ui/SelectableContext")},87571:e=>{e.exports=require("dom-helpers/transitionEnd")},88751:e=>{e.exports=require("next-i18next")},93787:e=>{e.exports=require("redux-persist")},94947:e=>{e.exports=require("@restart/hooks/useTimeout")},96196:e=>{e.exports=require("next-redux-wrapper")},98320:e=>{e.exports=require("@restart/hooks/useIsomorphicEffect")},99460:e=>{e.exports=require("dom-helpers/css")},99775:(e,r,t)=>{t.r(r),t.d(r,{canAddProject:()=>n,canAddProjectForm:()=>p,canEditProject:()=>c,canEditProjectForm:()=>u,canViewDiscussionUpdate:()=>l,default:()=>d});var s=t(8732);t(82015);var o=t(81366),a=t.n(o),i=t(61421);let n=a()({authenticatedSelector:e=>!!e.permissions&&!!e.permissions.project&&!!e.permissions.project["create:any"],wrapperDisplayName:"CanAddProject"}),p=a()({authenticatedSelector:e=>!!e.permissions&&!!e.permissions.project&&!!e.permissions.project["create:any"],wrapperDisplayName:"CanAddProjectForm",FailureComponent:()=>(0,s.jsx)(i.default,{})}),c=a()({authenticatedSelector:(e,r)=>{if(e.permissions&&e.permissions.project){if(e.permissions.project["update:any"])return!0;else if(e.permissions.project["update:own"]&&r.project&&r.project.user&&r.project.user._id===e.user._id)return!0}return!1},wrapperDisplayName:"CanEditProject"}),u=a()({authenticatedSelector:(e,r)=>{if(e.permissions&&e.permissions.project){if(e.permissions.project["update:any"])return!0;else if(e.permissions.project["update:own"]&&r.project&&r.project.user&&r.project.user._id===e.user._id)return!0}return!1},wrapperDisplayName:"CanEditProjectForm",FailureComponent:()=>(0,s.jsx)(i.default,{})}),l=a()({authenticatedSelector:e=>!!e.permissions&&!!e.permissions.update&&!!e.permissions.update["read:any"],wrapperDisplayName:"CanViewDiscussionUpdate"}),d=n}};var r=require("../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[6089,9216,9616,2386],()=>t(36763));module.exports=s})();