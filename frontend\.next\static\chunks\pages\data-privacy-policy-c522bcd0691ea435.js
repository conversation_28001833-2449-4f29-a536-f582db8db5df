(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[7330],{29335:(a,e,r)=>{"use strict";r.d(e,{A:()=>g});var t=r(15039),d=r.n(t),s=r(14232),l=r(77346),i=r(37876);let c=s.forwardRef((a,e)=>{let{className:r,bsPrefix:t,as:s="div",...c}=a;return t=(0,l.oU)(t,"card-body"),(0,i.jsx)(s,{ref:e,className:d()(r,t),...c})});c.displayName="CardBody";let o=s.forwardRef((a,e)=>{let{className:r,bsPrefix:t,as:s="div",...c}=a;return t=(0,l.oU)(t,"card-footer"),(0,i.jsx)(s,{ref:e,className:d()(r,t),...c})});o.displayName="CardFooter";var n=r(81764);let x=s.forwardRef((a,e)=>{let{bsPrefix:r,className:t,as:c="div",...o}=a,x=(0,l.oU)(r,"card-header"),h=(0,s.useMemo)(()=>({cardHeaderBsPrefix:x}),[x]);return(0,i.jsx)(n.A.Provider,{value:h,children:(0,i.jsx)(c,{ref:e,...o,className:d()(t,x)})})});x.displayName="CardHeader";let h=s.forwardRef((a,e)=>{let{bsPrefix:r,className:t,variant:s,as:c="img",...o}=a,n=(0,l.oU)(r,"card-img");return(0,i.jsx)(c,{ref:e,className:d()(s?"".concat(n,"-").concat(s):n,t),...o})});h.displayName="CardImg";let j=s.forwardRef((a,e)=>{let{className:r,bsPrefix:t,as:s="div",...c}=a;return t=(0,l.oU)(t,"card-img-overlay"),(0,i.jsx)(s,{ref:e,className:d()(r,t),...c})});j.displayName="CardImgOverlay";let f=s.forwardRef((a,e)=>{let{className:r,bsPrefix:t,as:s="a",...c}=a;return t=(0,l.oU)(t,"card-link"),(0,i.jsx)(s,{ref:e,className:d()(r,t),...c})});f.displayName="CardLink";var y=r(46052);let u=(0,y.A)("h6"),p=s.forwardRef((a,e)=>{let{className:r,bsPrefix:t,as:s=u,...c}=a;return t=(0,l.oU)(t,"card-subtitle"),(0,i.jsx)(s,{ref:e,className:d()(r,t),...c})});p.displayName="CardSubtitle";let b=s.forwardRef((a,e)=>{let{className:r,bsPrefix:t,as:s="p",...c}=a;return t=(0,l.oU)(t,"card-text"),(0,i.jsx)(s,{ref:e,className:d()(r,t),...c})});b.displayName="CardText";let P=(0,y.A)("h5"),m=s.forwardRef((a,e)=>{let{className:r,bsPrefix:t,as:s=P,...c}=a;return t=(0,l.oU)(t,"card-title"),(0,i.jsx)(s,{ref:e,className:d()(r,t),...c})});m.displayName="CardTitle";let N=s.forwardRef((a,e)=>{let{bsPrefix:r,className:t,bg:s,text:o,border:n,body:x=!1,children:h,as:j="div",...f}=a,y=(0,l.oU)(r,"card");return(0,i.jsx)(j,{ref:e,...f,className:d()(t,y,s&&"bg-".concat(s),o&&"text-".concat(o),n&&"border-".concat(n)),children:x?(0,i.jsx)(c,{children:h}):h})});N.displayName="Card";let g=Object.assign(N,{Img:h,Title:m,Subtitle:p,Body:c,Link:f,Text:b,Header:x,Footer:o,ImgOverlay:j})},39659:(a,e,r)=>{"use strict";r.r(e),r.d(e,{__N_SSP:()=>l,default:()=>i});var t=r(37876),d=r(29335),s=r(31753),l=!0;let i=()=>{let{t:a}=(0,s.Bd)("common");return(0,t.jsx)(d.A,{className:"privacyCard",children:(0,t.jsxs)(d.A.Body,{children:[(0,t.jsx)("h2",{children:a("dataPolicy.mainheader")}),(0,t.jsx)("p",{children:a("dataPolicy.info1")}),(0,t.jsxs)("p",{children:[(0,t.jsxs)("strong",{children:[" ",a("dataPolicy.info2")]}),(0,t.jsx)("br",{}),a("dataPolicy.info3"),(0,t.jsx)("br",{}),a("dataPolicy.info4"),(0,t.jsx)("br",{}),a("dataPolicy.info5")]}),(0,t.jsx)("p",{children:a("dataPolicy.info6")}),(0,t.jsxs)("p",{children:[(0,t.jsx)("strong",{children:a("dataPolicy.info7")}),(0,t.jsx)("br",{}),a("dataPolicy.info8"),(0,t.jsx)("br",{}),a("dataPolicy.info9"),(0,t.jsx)("br",{}),a("dataPolicy.info10"),(0,t.jsx)("br",{}),a("dataPolicy.info11"),(0,t.jsx)("br",{}),a("dataPolicy.info12")]}),(0,t.jsxs)("p",{children:[a("dataPolicy.info13"),(0,t.jsx)("br",{}),a("dataPolicy.info14"),(0,t.jsx)("br",{}),a("dataPolicy.info15"),(0,t.jsx)("br",{}),a("dataPolicy.info16")," ",(0,t.jsx)("a",{href:"https://rki-uat.adapptlabs.com/",children:"https://rki-uat.adapptlabs.com/"})]}),(0,t.jsxs)("p",{children:[(0,t.jsx)("strong",{children:a("dataPolicy.info17")}),(0,t.jsx)("br",{}),a("dataPolicy.info18")]}),(0,t.jsxs)("p",{children:[a("dataPolicy.info19"),(0,t.jsx)("br",{}),a("dataPolicy.info20")]}),(0,t.jsxs)("p",{children:[(0,t.jsxs)("strong",{children:[" ",a("dataPolicy.info21")]}),(0,t.jsx)("br",{}),a("dataPolicy.info22")]}),(0,t.jsxs)("p",{children:[(0,t.jsx)("strong",{children:a("dataPolicy.info23")}),(0,t.jsx)("br",{}),a("dataPolicy.info24"),(0,t.jsx)("br",{}),(0,t.jsx)("a",{target:"_blank",href:"https://adappt.co.uk/",children:"https://adappt.co.uk/"})]}),(0,t.jsx)("h3",{children:a("dataPolicy.subheader.header")}),(0,t.jsx)("p",{children:(0,t.jsx)("strong",{children:a("dataPolicy.subheader.text1")})}),(0,t.jsx)("p",{children:a("dataPolicy.subheader.text2")}),(0,t.jsx)("p",{children:(0,t.jsxs)("strong",{children:[" ",a("dataPolicy.subheader.text3")]})}),(0,t.jsx)("p",{children:a("dataPolicy.subheader.text4")}),(0,t.jsx)("p",{children:a("dataPolicy.subheader.text5")}),(0,t.jsxs)("ul",{children:[(0,t.jsx)("li",{children:a("dataPolicy.subheader.text6")}),(0,t.jsx)("li",{children:a("dataPolicy.subheader.text7")}),(0,t.jsx)("li",{children:a("dataPolicy.subheader.text8")}),(0,t.jsx)("li",{children:a("dataPolicy.subheader.text9")}),(0,t.jsx)("li",{children:a("dataPolicy.subheader.text10")}),(0,t.jsx)("li",{children:a("dataPolicy.subheader.text11")})]}),(0,t.jsx)("p",{children:a("dataPolicy.subheader.text12")}),(0,t.jsx)("p",{children:a("dataPolicy.subheader.text13")}),(0,t.jsxs)("p",{children:[a("dataPolicy.subheader.text14")," ",(0,t.jsx)("a",{target:"_blank",href:a("dataPolicy.subheader.text14a"),children:a("dataPolicy.subheader.text14b")})]})]})})}},81764:(a,e,r)=>{"use strict";r.d(e,{A:()=>d});let t=r(14232).createContext(null);t.displayName="CardHeaderContext";let d=t},83980:(a,e,r)=>{(window.__NEXT_P=window.__NEXT_P||[]).push(["/data-privacy-policy",function(){return r(39659)}])}},a=>{var e=e=>a(a.s=e);a.O(0,[636,6593,8792],()=>e(83980)),_N_E=a.O()}]);
//# sourceMappingURL=data-privacy-policy-c522bcd0691ea435.js.map