(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[6312],{50749:(t,e,n)=>{"use strict";n.d(e,{A:()=>l});var i=n(37876);n(14232);var s=n(89773),r=n(31753),o=n(5507);function a(t){let{t:e}=(0,r.Bd)("common"),n={rowsPerPageText:e("Rowsperpage")},{columns:a,data:l,totalRows:c,resetPaginationToggle:u,subheader:d,subHeaderComponent:p,handlePerRowsChange:h,handlePageChange:g,rowsPerPage:x,defaultRowsPerPage:j,selectableRows:y,loading:b,pagServer:w,onSelectedRowsChange:m,clearSelectedRows:f,sortServer:P,onSort:N,persistTableHead:k,sortFunction:C,...A}=t,_={paginationComponentOptions:n,noDataComponent:e("NoData"),noHeader:!0,columns:a,data:l||[],dense:!0,paginationResetDefaultPage:u,subHeader:d,progressPending:b,subHeaderComponent:p,pagination:!0,paginationServer:w,paginationPerPage:j||10,paginationRowsPerPageOptions:x||[10,15,20,25,30],paginationTotalRows:c,onChangeRowsPerPage:h,onChangePage:g,selectableRows:y,onSelectedRowsChange:m,clearSelectedRows:f,progressComponent:(0,i.jsx)(o.A,{}),sortIcon:(0,i.jsx)("i",{className:"sort-icon fas fa-exchange-alt"}),sortServer:P,onSort:N,sortFunction:C,persistTableHead:k,className:"rki-table"};return(0,i.jsx)(s.Ay,{..._})}a.defaultProps={subHeader:!1,pagination:!0,totalRows:null,pagServer:!0,onSelectedRowsChange:null,clearSelectedRows:!1,sortServer:!1,persistTableHead:!1};let l=a},88517:(t,e,n)=>{"use strict";n.r(e),n.d(e,{default:()=>g});var i=n(37876),s=n(48230),r=n.n(s),o=n(82851),a=n.n(o),l=n(39658),c=n(63847),u=n(50749),d=n(31753);let p=t=>{let{networks:e}=t;return e&&e.length>0?(0,i.jsx)("ul",{children:e.map((t,e)=>(0,i.jsx)("li",{children:t.title},e))}):null},h=(0,i.jsxs)(l.A,{id:"popover-basic",children:[(0,i.jsx)(l.A.Header,{as:"h3",className:"text-center",children:"NETWORKS"}),(0,i.jsx)(l.A.Body,{children:(0,i.jsxs)("div",{className:"m-2",children:[(0,i.jsxs)("p",{children:[(0,i.jsx)("b",{children:"EMLab"})," - European Mobile Lab"]}),(0,i.jsxs)("p",{children:[(0,i.jsx)("b",{children:"EMT"})," - Emergency Medical Teams"]}),(0,i.jsxs)("p",{children:[(0,i.jsx)("b",{children:"GHPP"})," - Global Health Protection Program"]}),(0,i.jsxs)("p",{children:[(0,i.jsx)("b",{children:"GOARN"})," - Global Outbreak Alert & Response Network"]}),(0,i.jsxs)("p",{children:[(0,i.jsx)("b",{children:"IANPHI"})," - International Association of National Public Health Institutes"]}),(0,i.jsxs)("p",{children:[(0,i.jsx)("b",{children:"STAKOB"})," - St\xe4ndiger Arbeitskreis der Kompetenz-und Behandlungszentren"]}),(0,i.jsxs)("p",{children:[(0,i.jsx)("b",{children:"WHOCC"}),"- World Health Organization Collaborating Centres"]})]})})]}),g=function(t){let{t:e}=(0,d.Bd)("common"),{partners:n}=t,s=[{name:e("Organisation"),selector:"title",cell:t=>t&&t.institution?(0,i.jsx)(r(),{href:"/institution/[...routes]",as:"/institution/show/".concat(t.institution._id),children:t.institution.title}):"",sortable:!0},{name:e("Country"),selector:"country",cell:t=>t&&t.institution&&t.institution.address&&t.institution.address.country?(0,i.jsx)(r(),{href:"/country/[...routes]",as:"/country/show/".concat(t.institution.address.country._id),children:t.institution.address.country.title}):"",sortable:!0},{name:e("Type"),selector:"type.title",cell:t=>t.institution&&t.institution.type&&t.institution.type.title?t.institution.type.title:"",sortable:!0},{name:(0,i.jsx)(c.A,{trigger:"click",placement:"right",overlay:h,children:(0,i.jsxs)("span",{children:[e("Network"),"\xa0\xa0\xa0",(0,i.jsx)("i",{className:"fa fa-info-circle",style:{cursor:"pointer"},"aria-hidden":"true"})]})}),selector:e("Networks"),cell:t=>t.institution&&t.institution.networks&&t.institution.networks.length>0?(0,i.jsx)(p,{networks:t.institution.networks}):""}],o=t=>{if(t.institution.address&&t.institution.address.country)return t.institution.address.country&&t.institution.address.country.title?t.institution.address.country.title.toLowerCase():t.institution.address.country.title},l=t=>{if(t.institution.type&&t.institution.type&&t.institution.type.title)return t.institution.type.title.toLowerCase()};return(0,i.jsx)(u.A,{columns:s,data:n,pagServer:!0,persistTableHead:!0,sortFunction:(t,e,n)=>a().orderBy(t,t=>{if("country"===e)o(t);else if("type.title"===e)l(t);else if(t.institution&&t.institution[e])return t.institution[e].toLowerCase()},n)})}},99488:(t,e,n)=>{(window.__NEXT_P=window.__NEXT_P||[]).push(["/operation/OperationPartners",function(){return n(88517)}])}},t=>{var e=e=>t(t.s=e);t.O(0,[9773,636,6593,8792],()=>e(99488)),_N_E=t.O()}]);
//# sourceMappingURL=OperationPartners-5e250fb58f049d12.js.map