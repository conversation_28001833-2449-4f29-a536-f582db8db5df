"use strict";(()=>{var e={};e.id=4386,e.ids=[636,3220,4386],e.modules={123:e=>{e.exports=require("dom-helpers/removeEventListener")},1332:e=>{e.exports=require("react-custom-scrollbars-2")},1428:e=>{e.exports=import("axios")},1680:e=>{e.exports=require("@restart/ui/utils")},1919:e=>{e.exports=require("react-transition-group/Transition")},2051:(e,r,t)=>{t.a(e,async(e,s)=>{try{t.r(r),t.d(r,{config:()=>h,default:()=>x,getServerSideProps:()=>q,getStaticPaths:()=>m,getStaticProps:()=>c,reportWebVitals:()=>g,routeModule:()=>w,unstable_getServerProps:()=>v,unstable_getServerSideProps:()=>b,unstable_getStaticParams:()=>S,unstable_getStaticPaths:()=>P,unstable_getStaticProps:()=>f});var o=t(63885),i=t(80237),a=t(81413),n=t(9616),p=t.n(n),u=t(72386),d=t(60072),l=e([u,d]);[u,d]=l.then?(await l)():l;let x=(0,a.M)(d,"default"),c=(0,a.M)(d,"getStaticProps"),m=(0,a.M)(d,"getStaticPaths"),q=(0,a.M)(d,"getServerSideProps"),h=(0,a.M)(d,"config"),g=(0,a.M)(d,"reportWebVitals"),f=(0,a.M)(d,"unstable_getStaticProps"),P=(0,a.M)(d,"unstable_getStaticPaths"),S=(0,a.M)(d,"unstable_getStaticParams"),v=(0,a.M)(d,"unstable_getServerProps"),b=(0,a.M)(d,"unstable_getServerSideProps"),w=new o.PagesRouteModule({definition:{kind:i.A.PAGES,page:"/operation/components/OperationCoverSection",pathname:"/operation/components/OperationCoverSection",bundlePath:"",filename:""},components:{App:u.default,Document:p()},userland:d});s()}catch(e){s(e)}})},3892:e=>{e.exports=require("classnames")},4048:e=>{e.exports=require("react-truncate")},6009:e=>{e.exports=require("dom-helpers/ownerDocument")},6952:e=>{e.exports=require("@restart/ui/Modal")},7374:e=>{e.exports=require("@restart/ui/Dropdown")},8732:e=>{e.exports=require("react/jsx-runtime")},9653:e=>{e.exports=require("@restart/ui/Overlay")},11242:e=>{e.exports=require("redux-persist/integration/react")},11688:e=>{e.exports=require("@restart/hooks/useWillUnmount")},12388:e=>{e.exports=require("dom-helpers/addEventListener")},13364:e=>{e.exports=require("redux-saga/effects")},14062:e=>{e.exports=import("react-redux")},14078:e=>{e.exports=import("swr")},14332:e=>{e.exports=require("uncontrollable")},16116:e=>{e.exports=require("invariant")},18622:e=>{e.exports=require("yup")},19121:e=>{e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},22326:e=>{e.exports=require("react-dom")},22541:e=>{e.exports=require("redux-persist/lib/storage")},25303:e=>{e.exports=require("@restart/ui/NavItem")},26324:e=>{e.exports=require("warning")},27825:e=>{e.exports=require("lodash")},27910:e=>{e.exports=require("stream")},28163:(e,r,t)=>{t.r(r),t.d(r,{canAddOperation:()=>n,canAddOperationForm:()=>p,canEditOperation:()=>u,canEditOperationForm:()=>d,canViewDiscussionUpdate:()=>l,default:()=>x});var s=t(8732);t(82015);var o=t(81366),i=t.n(o),a=t(61421);let n=i()({authenticatedSelector:e=>!!e.permissions&&!!e.permissions.operation&&!!e.permissions.operation["create:any"],wrapperDisplayName:"CanAddOperation"}),p=i()({authenticatedSelector:e=>!!e.permissions&&!!e.permissions.operation&&!!e.permissions.operation["create:any"],wrapperDisplayName:"CanAddOperationForm",FailureComponent:()=>(0,s.jsx)(a.default,{})}),u=i()({authenticatedSelector:(e,r)=>{if(e.permissions&&e.permissions.operation){if(e.permissions.operation["update:any"])return!0;else if(e.permissions.operation["update:own"]&&r.operation&&r.operation.user&&r.operation.user._id===e.user._id)return!0}return!1},wrapperDisplayName:"CanEditOperation"}),d=i()({authenticatedSelector:(e,r)=>{if(e.permissions&&e.permissions.operation){if(e.permissions.operation["update:any"])return!0;else if(e.permissions.operation["update:own"]&&r.operation&&r.operation.user&&r.operation.user._id===e.user._id)return!0}return!1},wrapperDisplayName:"CanEditOperationForm",FailureComponent:()=>(0,s.jsx)(a.default,{})}),l=i()({authenticatedSelector:e=>!!e.permissions&&!!e.permissions.update&&!!e.permissions.update["read:any"],wrapperDisplayName:"CanViewDiscussionUpdate"}),x=n},28217:e=>{e.exports=require("@restart/ui/DropdownItem")},29021:e=>{e.exports=require("fs")},29294:e=>{e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},29628:e=>{e.exports=require("dom-helpers/addClass")},29825:e=>{e.exports=require("prop-types")},29841:e=>{e.exports=require("dom-helpers/hasClass")},30362:e=>{e.exports=require("@restart/ui/DropdownToggle")},33873:e=>{e.exports=require("path")},36653:e=>{e.exports=require("nprogress")},36955:e=>{e.exports=require("@restart/hooks/useBreakpoint")},37766:e=>{e.exports=require("@restart/hooks/useMergedRefs")},39756:e=>{e.exports=import("redux")},40051:e=>{e.exports=require("dom-helpers/removeClass")},40361:e=>{e.exports=require("next/dist/compiled/next-server/pages.runtime.prod.js")},42893:e=>{e.exports=import("react-hot-toast")},43294:e=>{e.exports=require("formik")},50009:e=>{e.exports=require("@restart/ui/DropdownMenu")},54131:e=>{e.exports=import("@fortawesome/free-solid-svg-icons")},57664:e=>{e.exports=require("dom-helpers/scrollbarSize")},58928:e=>{e.exports=require("dom-helpers/canUseDOM")},59717:e=>{e.exports=require("@restart/ui/DropdownContext")},60560:e=>{e.exports=require("dom-helpers/contains")},63033:e=>{e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},63349:(e,r,t)=>{t.d(r,{A:()=>a});var s=t(8732),o=t(82015),i=t(88751);let a=e=>{let{t:r}=(0,i.useTranslation)("common"),t=parseInt("255"),[a,n]=(0,o.useState)(!1);return(0,s.jsxs)(s.Fragment,{children:[e.description?(0,s.jsx)("div",{dangerouslySetInnerHTML:((r,s)=>({__html:!s&&r.length>t?r.substring(0,t)+"...":e.description}))(e.description,a),className:"operationDesc"}):null,e.description&&e.description.length>t?(0,s.jsx)("button",{type:"button",className:"readMoreText",onClick:()=>n(!a),children:r(a?"readLess":"readMore")}):null]})}},65447:e=>{e.exports=require("@restart/ui/Button")},67364:e=>{e.exports=require("@restart/hooks/useCallbackRef")},68455:e=>{e.exports=import("redux-saga")},69722:e=>{e.exports=require("es6-promise")},74075:e=>{e.exports=require("zlib")},74716:e=>{e.exports=require("moment")},74987:e=>{e.exports=require("@restart/ui/ModalManager")},78097:e=>{e.exports=require("next-redux-saga")},78634:e=>{e.exports=require("@restart/ui/Anchor")},80237:(e,r)=>{Object.defineProperty(r,"A",{enumerable:!0,get:function(){return t}});var t=function(e){return e.PAGES="PAGES",e.PAGES_API="PAGES_API",e.APP_PAGE="APP_PAGE",e.APP_ROUTE="APP_ROUTE",e.IMAGE="IMAGE",e}({})},81366:e=>{e.exports=require("redux-auth-wrapper/connectedAuthWrapper")},81413:(e,r)=>{Object.defineProperty(r,"M",{enumerable:!0,get:function(){return function e(r,t){return t in r?r[t]:"then"in r&&"function"==typeof r.then?r.then(r=>e(r,t)):"function"==typeof r&&"default"===t?r:void 0}}})},81521:e=>{e.exports=require("dom-helpers/querySelectorAll")},81895:e=>{e.exports=require("@restart/hooks/useEventCallback")},82015:e=>{e.exports=require("react")},82053:e=>{e.exports=require("@fortawesome/react-fontawesome")},86842:e=>{e.exports=require("@restart/ui/SelectableContext")},87571:e=>{e.exports=require("dom-helpers/transitionEnd")},88751:e=>{e.exports=require("next-i18next")},93787:e=>{e.exports=require("redux-persist")},94696:e=>{e.exports=require("@react-google-maps/api")},94947:e=>{e.exports=require("@restart/hooks/useTimeout")},96196:e=>{e.exports=require("next-redux-wrapper")},98320:e=>{e.exports=require("@restart/hooks/useIsomorphicEffect")},99460:e=>{e.exports=require("dom-helpers/css")}};var r=require("../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[6089,9216,9616,2386,72],()=>t(2051));module.exports=s})();