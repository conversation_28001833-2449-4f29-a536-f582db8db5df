{"version": 3, "file": "static/chunks/pages/operation/components/VirtualSpaceAccordian-64734e02ea045795.js", "mappings": "2MA0CA,MAhC8B,QAkBJA,EAAAA,EAjBtB,GAAM,GAAEC,CAAC,CAAE,CAAGC,CAAAA,EAAAA,EAAAA,EA+BHC,CA/BiBD,CAAC,UACvB,CAACE,EAASC,EAAW,CAAGC,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,EAAC,GACvC,MACI,+BACI,WAACC,EAAAA,CAASA,CAACC,IAAI,EAACC,SAAS,cACrB,WAACF,EAAAA,CAASA,CAACG,MAAM,EAACC,QAAS,IAAMN,EAAW,CAACD,aACzC,UAACQ,MAAAA,CAAIC,UAAU,qBAAaZ,EAAE,wBAC9B,UAACW,MAAAA,CAAIC,UAAU,qBACVT,EACG,UAACU,EAAAA,CAAeA,CAAAA,CAACC,KAAMC,EAAAA,GAAOA,CAAEC,MAAM,SAEtC,UAACH,EAAAA,CAAeA,CAAAA,CAACC,KAAMG,EAAAA,GAAMA,CAAED,MAAM,cAIjD,UAACV,EAAAA,CAASA,CAACY,IAAI,WACX,UAACC,EAAAA,CAAWA,CAAAA,CACVC,GAAIrB,QAAAA,EAAAA,EAAMsB,SAAAA,GAANtB,OAAAA,EAAAA,EAAAA,MAAiBuB,EAAjBvB,KAAAA,EAAAA,CAAyB,CAAC,EAAE,CAA5BA,EAAgC,GACpCwB,KAAK,YACLC,WAAY,EAAE,CACdC,mBAAmB,EACnBC,oBAAqB,EACrBC,kBAAmB,GACnBC,sBAAuB,UAM7C,gICoEA,MAzFoB,IAClB,GAAM,CAAE5B,GAAC,CAAE,CAAGC,CAAAA,EAAAA,EAAAA,EAAAA,CAAcA,CAAC,MAwFhBkB,IAvFP,MAAEI,CAAI,EAuFa,EAvFXH,CAAE,CAAE,CAAGrB,EACf,CAAC8B,EAAWC,EAAe,CAAGzB,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAC,EAAE,EACzC,CAAC0B,EAASC,EAAW,CAAG3B,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAC,IACjC,CAAC4B,EAAWC,EAAa,CAAG7B,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAC,GACrC,CAAC8B,EAASC,EAAW,CAAG/B,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAC,IACjC,CAACgC,EAAsB,CAAGhC,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,EAAC,GAEnCiC,EAAe,CACnBC,KAAM,CAAEC,WAAY,KAAM,EAC1BC,MAAON,EACPO,KAAM,EACNC,MAAO,CAAC,CACV,EAEMC,EAAU,CACd,CACEC,KAAM7C,EAAE,SACR8C,SAAU,QACVC,KAAM,GAAYC,GAAKA,EAAEC,KAAK,EAAID,EAAEE,GAAG,CAAG,UAACC,IAAIA,CAACC,KAAK,sBAAsBC,GAAI,cAAhCF,EAAsD,OAANH,EAAEE,GAAG,WAAMF,EAAEC,KAAK,GAAW,EAC9H,EACA,CACEJ,KAAM7C,EAAE,SACR8C,SAAU,QACVC,KAAM,GAAYC,GAAKA,EAAEM,IAAI,EAAIN,EAAEM,IAAI,CAACC,SAAS,CAAG,GAAuBP,MAAAA,CAApBA,EAAEM,IAAI,CAACC,SAAS,CAAC,KAAmB,OAAhBP,EAAEM,IAAI,CAACE,QAAQ,EAAK,EACjG,EACA,CACEX,KAAM7C,EAAE,iBACR8C,SAAU,aACVC,KAAM,GAAYC,GAAKA,EAAES,UAAU,CAAG,SAAW,SAEnD,EACA,CACEZ,KAAM7C,EAAE,mBACR8C,SAAU,UACVC,KAAM,GAAYC,GAAKA,EAAEU,OAAO,CAAGV,EAAEU,OAAO,CAACC,MAAM,CAAG,GACxD,EACD,CAEKC,EAAkB,MAAOC,IAC7B7B,EAAW,IACX,IAAM8B,EAAW,MAAMC,EAAAA,CAAUA,CAACC,GAAG,CAAC,YAA8B5C,MAAAA,CAAlBG,EAAK,eAAgB,OAAHH,GAAMkB,GACtEwB,IACO,MADG,QACZvC,EAAuBO,EAAegC,EAASG,SAAS,EAAInC,EAAegC,EAASI,OAAO,EAC3FhC,EAAa4B,EAASK,UAAU,EAChCnC,GAAW,GAEf,EAQMoC,EAAsB,MAAOC,EAAoB3B,KACrDJ,EAAaG,KAAK,CAAG4B,EACrB/B,EAAaI,IAAI,CAAGA,EACpBV,EAAW,IACX,IAAM8B,EAAW,MAAMC,EAAAA,CAAUA,CAACC,GAAG,CAAC,YAA8B5C,MAAAA,CAAlBG,EAAK,eAAgB,OAAHH,GAAMkB,GACtEwB,IACO,MADG,QACZvC,EAAuBO,EAAegC,EAASG,SAAS,EAAInC,EAAegC,EAASI,OAAO,EAC3F9B,EAAWiC,GACXrC,EAAW,IAEf,EAQA,MANAsC,CAAAA,EAAAA,EAAAA,SAAAA,CAASA,CAAC,KACRV,EAAgBtB,EAClB,EAAG,EAAE,EAKH,UAAC3B,MAAAA,UACC,UAAC4D,EAAAA,CAAQA,CAAAA,CACP3B,QAASA,EACT4B,KAAM3C,EACNI,UAAWA,EACXF,QAASA,EACTM,sBAAuBA,EACvB+B,oBAAqBA,EACrBK,iBAjCoB/B,CAiCF+B,GAhCtBnC,EAAaG,KAAK,CAAGN,EACrBG,EAAaI,IAAI,CAAGA,EACpBkB,EAAgBtB,EAClB,KAiCF,mBCzGA,4CACA,8CACA,WACA,OAAe,EAAQ,KAAmE,CAC1F,EACA,SAFsB,oGCiCtB,SAASiC,EAASxE,CAAoB,EACpC,GAAM,GAAEC,CAAC,CAAE,CAAGC,CAAAA,EAAAA,EAAAA,EAAAA,CAAcA,CAAC,UACvByE,EAA6B,CACjCC,gBAAiB3E,EAAE,cACnB,EACI,SACJ4C,CAAO,MACP4B,CAAI,CACJvC,WAAS,uBACTI,CAAqB,WACrBuC,CAAS,oBACTC,CAAkB,qBAClBT,CAAmB,kBACnBK,CAAgB,aAChBK,CAAW,oBACXC,CAAkB,gBAClBC,CAAc,CACdjD,SAAO,CACPkD,WAAS,sBACTC,CAAoB,mBACpBC,CAAiB,YACjBC,CAAU,QACVC,CAAM,kBACNC,CAAgB,cAChBC,CAAY,CAEZ,CADA,EACGC,EACJ,CAAGzF,EAGE0F,EAAiB,CACrBf,6BACAgB,gBAAiB1F,EAAE,IAP0C,MAQ7D2F,UAAU,UACV/C,EACA4B,KAAMA,GAAQ,EAAE,CAChBoB,OAAO,EACPC,2BAA4BxD,EAC5ByD,UAAWlB,EACXmB,gBAAiBhE,qBACjB8C,EACAmB,YAAY,EACZC,iBAAkBhB,EAClBiB,kBAAmBnB,GAA0C,GAC7DoB,eADwCpB,cACVD,GAA4B,CAAC,GAAI,GAAI,GAAI,CAA3BA,EAA+B,GAAG,CAC9EsB,oBAAqBnE,EACrBoE,oBAAqBjC,EACrBkC,aAAc7B,iBACdO,uBACAE,oBACAC,EACAoB,kBAAmB,UAACC,EAAAA,CAAYA,CAAAA,CAAAA,GAChCC,SAAU,UAACC,IAAAA,CAAE9F,UAAU,6CACvBwE,EACAC,SACAE,gCACAD,EACA1E,UAAW,WACb,EACA,MACE,UAAC+F,EAAAA,EAASA,CAAAA,CAAE,GAAGlB,CAAc,EAEjC,CAEAlB,EAASqC,YAAY,CAAG,CACtBd,WAAW,EACXE,YAAY,EACZ/D,UAAW,KACXgD,WAAW,EACXC,qBAAsB,KACtBC,mBAAmB,EACnBC,YAAY,EACZE,iBAAkB,EACpB,EAEA,MAAef,QAAQA,EAAC", "sources": ["webpack://_N_E/./pages/operation/components/VirtualSpaceAccordian.tsx", "webpack://_N_E/./components/common/VspaceTable.tsx", "webpack://_N_E/?68fb", "webpack://_N_E/./components/common/RKITable.tsx"], "sourcesContent": ["//Import Library\r\nimport React, { useState } from \"react\";\r\nimport { Accordion, Card } from \"react-bootstrap\"\r\nimport { FontAwesomeIcon } from \"@fortawesome/react-fontawesome\";\r\nimport { faMinus, faPlus } from \"@fortawesome/free-solid-svg-icons\";\r\n\r\n//Import services/components\r\nimport { useTranslation } from 'next-i18next';\r\nimport VspaceTable from \"../../../components/common/VspaceTable\";\r\n\r\nconst VirtualSpaceAccordian = (props: any) => {\r\n    const { t } = useTranslation('common');\r\n    const [section, setSection] = useState(false);\r\n    return (\r\n        <>\r\n            <Accordion.Item eventKey=\"0\">\r\n                <Accordion.Header onClick={() => setSection(!section)}>\r\n                    <div className=\"cardTitle\">{t(\"LinkedVirtualSpace\")}</div>\r\n                    <div className=\"cardArrow\">\r\n                        {section ? (\r\n                            <FontAwesomeIcon icon={faMinus} color=\"#fff\" />\r\n                        ) : (\r\n                            <FontAwesomeIcon icon={faPlus} color=\"#fff\" />\r\n                        )}\r\n                    </div>\r\n                </Accordion.Header>\r\n                <Accordion.Body>\r\n                    <VspaceTable\r\n                      id={props.routeData?.routes?.[1] || ''}\r\n                      type=\"Operation\"\r\n                      vspaceData={[]}\r\n                      vspaceDataLoading={false}\r\n                      vspaceDataTotalRows={0}\r\n                      vspaceDataPerPage={10}\r\n                      vspaceDataCurrentPage={1}\r\n                    />\r\n                </Accordion.Body>\r\n            </Accordion.Item>\r\n        </>\r\n    )\r\n}\r\n\r\nexport default VirtualSpaceAccordian;", "//Import Library\r\nimport { useEffect, useState } from \"react\";\r\nimport Link from 'next/link';\r\n\r\n//Import services/components\r\nimport RKITable from \"../../components/common/RKITable\";\r\nimport apiService from \"../../services/apiService\";\r\nimport { useTranslation } from 'next-i18next';\r\n\r\ninterface VspaceTableProps {\r\n  vspaceData: any;\r\n  vspaceDataLoading: boolean;\r\n  vspaceDataTotalRows: number;\r\n  vspaceDataPerPage: number;\r\n  vspaceDataCurrentPage: number;\r\n  type: string;\r\n  id: string;\r\n}\r\n\r\nconst VspaceTable = (props: VspaceTableProps) => {\r\n  const { t } = useTranslation('common');\r\n  const { type, id } = props;\r\n  const [tabledata, setDataToTable] = useState([]);\r\n  const [loading, setLoading] = useState(false);\r\n  const [totalRows, setTotalRows] = useState(0);\r\n  const [perPage, setPerPage] = useState(10);\r\n  const [resetPaginationToggle] = useState(false);\r\n\r\n  const vSpaceParams = {\r\n    sort: { created_at: \"asc\" },\r\n    limit: perPage,\r\n    page: 1,\r\n    query: {},\r\n  };\r\n\r\n  const columns = [\r\n    {\r\n      name: t(\"Title\"),\r\n      selector: \"title\",\r\n      cell: (d: any) => d && d.title && d._id ? <Link href=\"/vspace/[...routes]\" as={`/vspace/show/${d._id}`} >{d.title}</Link> : \"\",\r\n    },\r\n    {\r\n      name: t(\"Owner\"),\r\n      selector: \"users\",\r\n      cell: (d: any) => d && d.user && d.user.firstname ? `${d.user.firstname} ${d.user.lastname}` : \"\"\r\n    },\r\n    {\r\n      name: t(\"PublicPrivate\"),\r\n      selector: \"visibility\",\r\n      cell: (d: any) => d && d.visibility ? \"Public\" : \"Private\",\r\n\r\n    },\r\n    {\r\n      name: t(\"NumberofMembers\"),\r\n      selector: \"members\",\r\n      cell: (d: any) => d && d.members ? d.members.length : \"-\",\r\n    }\r\n  ];\r\n\r\n  const getLinkedVspace = async (vSpaceParams1: any) => {\r\n    setLoading(true);\r\n    const response = await apiService.get(`stats/get${type}WithVspace/${id}`, vSpaceParams);\r\n    if (response) {\r\n      type === \"Operation\" ? setDataToTable(response.operation) : setDataToTable(response.project);\r\n      setTotalRows(response.totalCount);\r\n      setLoading(false);\r\n    }\r\n  };\r\n\r\n  const handlePageChange = (page: number) => {\r\n    vSpaceParams.limit = perPage;\r\n    vSpaceParams.page = page;\r\n    getLinkedVspace(vSpaceParams);\r\n  };\r\n\r\n  const handlePerRowsChange = async (newPerPage: number, page: number) => {\r\n    vSpaceParams.limit = newPerPage;\r\n    vSpaceParams.page = page;\r\n    setLoading(true);\r\n    const response = await apiService.get(`stats/get${type}WithVspace/${id}`, vSpaceParams);\r\n    if (response) {\r\n      type === \"Operation\" ? setDataToTable(response.operation) : setDataToTable(response.project);\r\n      setPerPage(newPerPage);\r\n      setLoading(false);\r\n    }\r\n  };\r\n\r\n  useEffect(() => {\r\n    getLinkedVspace(vSpaceParams);\r\n  }, []);\r\n\r\n\r\n\r\n  return (\r\n    <div>\r\n      <RKITable\r\n        columns={columns}\r\n        data={tabledata}\r\n        totalRows={totalRows}\r\n        loading={loading}\r\n        resetPaginationToggle={resetPaginationToggle}\r\n        handlePerRowsChange={handlePerRowsChange}\r\n        handlePageChange={handlePageChange}\r\n      />\r\n    </div>\r\n  );\r\n}\r\n\r\nexport default VspaceTable;\r\n", "\n    (window.__NEXT_P = window.__NEXT_P || []).push([\n      \"/operation/components/VirtualSpaceAccordian\",\n      function () {\n        return require(\"private-next-pages/operation/components/VirtualSpaceAccordian.tsx\");\n      }\n    ]);\n    if(module.hot) {\n      module.hot.dispose(function () {\n        window.__NEXT_P.push([\"/operation/components/VirtualSpaceAccordian\"])\n      });\n    }\n  ", "//Import Library\r\nimport React from 'react'\r\nimport DataTable  from 'react-data-table-component';\r\n\r\n//Import services/components\r\nimport { useTranslation } from 'next-i18next';\r\nimport CustomLoader from './CustomLoader';\r\n\r\ninterface RKITableProps {\r\n  columns: any[];\r\n  data: any[];\r\n  totalRows?: number;\r\n  resetPaginationToggle?: boolean;\r\n  subheader?: boolean;\r\n  subHeaderComponent?: React.ReactNode;\r\n  handlePerRowsChange?: (newPerPage: number, page: number) => void;\r\n  handlePageChange?: (page: number) => void;\r\n  rowsPerPage?: number[];\r\n  defaultRowsPerPage?: number;\r\n  selectableRows?: boolean;\r\n  loading?: boolean;\r\n  pagServer?: boolean;\r\n  onSelectedRowsChange?: (selectedRows: any) => void;\r\n  clearSelectedRows?: boolean;\r\n  sortServer?: boolean;\r\n  onSort?: (column: any, sortDirection: string) => void;\r\n  persistTableHead?: boolean;\r\n  sortFunction?: (rows: any[], field: any, direction: 'asc' | 'desc') => any[];\r\n  noHeader?: boolean;\r\n  dense?: boolean;\r\n  pagination?: boolean;\r\n  paginationServer?: boolean;\r\n  paginationTotalRows?: number;\r\n  subHeaderAlign?: string;\r\n  [key: string]: any;\r\n}\r\n\r\nfunction RKITable(props: RKITableProps) {\r\n  const { t } = useTranslation('common');\r\n  const paginationComponentOptions = {\r\n    rowsPerPageText: t('Rowsperpage'),\r\n    };\r\n  const {\r\n    columns,\r\n    data,\r\n    totalRows,\r\n    resetPaginationToggle,\r\n    subheader,\r\n    subHeaderComponent,\r\n    handlePerRowsChange,\r\n    handlePageChange,\r\n    rowsPerPage,\r\n    defaultRowsPerPage,\r\n    selectableRows,\r\n    loading,\r\n    pagServer,\r\n    onSelectedRowsChange,\r\n    clearSelectedRows,\r\n    sortServer,\r\n    onSort,\r\n    persistTableHead,\r\n    sortFunction,\r\n    // Filter out any unknown props that might cause DOM warnings\r\n    ...otherProps\r\n  } = props;\r\n\r\n  // Only pass known DataTable props to avoid DOM warnings\r\n  const dataTableProps = {\r\n    paginationComponentOptions,\r\n    noDataComponent: t(\"NoData\"),\r\n    noHeader: true,\r\n    columns,\r\n    data: data || [],\r\n    dense: true,\r\n    paginationResetDefaultPage: resetPaginationToggle,\r\n    subHeader: subheader,\r\n    progressPending: loading,\r\n    subHeaderComponent,\r\n    pagination: true,\r\n    paginationServer: pagServer,\r\n    paginationPerPage: defaultRowsPerPage ? defaultRowsPerPage : 10,\r\n    paginationRowsPerPageOptions: rowsPerPage ? rowsPerPage : [10, 15, 20, 25, 30],\r\n    paginationTotalRows: totalRows,\r\n    onChangeRowsPerPage: handlePerRowsChange,\r\n    onChangePage: handlePageChange,\r\n    selectableRows,\r\n    onSelectedRowsChange,\r\n    clearSelectedRows,\r\n    progressComponent: <CustomLoader/>,\r\n    sortIcon: <i className=\"sort-icon fas fa-exchange-alt\" />,\r\n    sortServer,\r\n    onSort,\r\n    sortFunction,\r\n    persistTableHead,\r\n    className: \"rki-table\"\r\n  };\r\n  return (\r\n    <DataTable {...dataTableProps} />\r\n  )\r\n};\r\n\r\nRKITable.defaultProps = {\r\n  subHeader: false,\r\n  pagination: true,\r\n  totalRows: null,\r\n  pagServer: true,\r\n  onSelectedRowsChange: null,\r\n  clearSelectedRows: false,\r\n  sortServer: false,\r\n  persistTableHead: false\r\n}\r\n\r\nexport default RKITable;\r\n\r\n"], "names": ["props", "t", "useTranslation", "VirtualSpaceAccordian", "section", "setSection", "useState", "Accordion", "<PERSON><PERSON>", "eventKey", "Header", "onClick", "div", "className", "FontAwesomeIcon", "icon", "faMinus", "color", "faPlus", "Body", "VspaceTable", "id", "routeData", "routes", "type", "vspaceData", "vspaceDataLoading", "vspaceDataTotalRows", "vspaceDataPerPage", "vspaceDataCurrentPage", "tabledata", "setDataToTable", "loading", "setLoading", "totalRows", "setTotalRows", "perPage", "setPerPage", "resetPaginationToggle", "vSpaceParams", "sort", "created_at", "limit", "page", "query", "columns", "name", "selector", "cell", "d", "title", "_id", "Link", "href", "as", "user", "firstname", "lastname", "visibility", "members", "length", "getLinkedVspace", "vSpaceParams1", "response", "apiService", "get", "operation", "project", "totalCount", "handlePerRowsChange", "newPerPage", "useEffect", "RKITable", "data", "handlePageChange", "paginationComponentOptions", "rowsPerPageText", "subheader", "subHeaderComponent", "rowsPerPage", "defaultRowsPerPage", "selectableRows", "pagServer", "onSelectedRowsChange", "clearSelectedRows", "sortServer", "onSort", "persistTableHead", "sortFunction", "otherProps", "dataTableProps", "noDataComponent", "<PERSON><PERSON><PERSON><PERSON>", "dense", "paginationResetDefaultPage", "subHeader", "progressPending", "pagination", "paginationServer", "paginationPerPage", "paginationRowsPerPageOptions", "paginationTotalRows", "onChangeRowsPerPage", "onChangePage", "progressComponent", "CustomLoader", "sortIcon", "i", "DataTable", "defaultProps"], "sourceRoot": "", "ignoreList": []}