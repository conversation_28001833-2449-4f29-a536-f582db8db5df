"use strict";(()=>{var e={};e.id=158,e.ids=[158,636,3220],e.modules={123:e=>{e.exports=require("dom-helpers/removeEventListener")},1332:e=>{e.exports=require("react-custom-scrollbars-2")},1428:e=>{e.exports=import("axios")},1680:e=>{e.exports=require("@restart/ui/utils")},1919:e=>{e.exports=require("react-transition-group/Transition")},3892:e=>{e.exports=require("classnames")},4048:e=>{e.exports=require("react-truncate")},6009:e=>{e.exports=require("dom-helpers/ownerDocument")},6952:e=>{e.exports=require("@restart/ui/Modal")},7374:e=>{e.exports=require("@restart/ui/Dropdown")},8732:e=>{e.exports=require("react/jsx-runtime")},9653:e=>{e.exports=require("@restart/ui/Overlay")},11242:e=>{e.exports=require("redux-persist/integration/react")},11688:e=>{e.exports=require("@restart/hooks/useWillUnmount")},12388:e=>{e.exports=require("dom-helpers/addEventListener")},13364:e=>{e.exports=require("redux-saga/effects")},14062:e=>{e.exports=import("react-redux")},14078:e=>{e.exports=import("swr")},14332:e=>{e.exports=require("uncontrollable")},16116:e=>{e.exports=require("invariant")},18622:e=>{e.exports=require("yup")},19121:e=>{e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},22326:e=>{e.exports=require("react-dom")},22541:e=>{e.exports=require("redux-persist/lib/storage")},25303:e=>{e.exports=require("@restart/ui/NavItem")},26324:e=>{e.exports=require("warning")},27825:e=>{e.exports=require("lodash")},27910:e=>{e.exports=require("stream")},28217:e=>{e.exports=require("@restart/ui/DropdownItem")},29021:e=>{e.exports=require("fs")},29294:e=>{e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},29628:e=>{e.exports=require("dom-helpers/addClass")},29825:e=>{e.exports=require("prop-types")},29841:e=>{e.exports=require("dom-helpers/hasClass")},30362:e=>{e.exports=require("@restart/ui/DropdownToggle")},33852:(e,r,t)=>{t.a(e,async(e,s)=>{try{t.r(r),t.d(r,{default:()=>b});var o=t(8732),i=t(44233),a=t.n(i),u=t(83551),p=t(49481),n=t(91353),c=t(42893),l=t(27825),x=t.n(l),d=t(82015),q=t(63487),m=t(88751),h=e([c,q]);[c,q]=h.then?(await h)():h;let b=e=>{let[r,t]=(0,d.useState)(!1),[s,l]=(0,d.useState)({title:"",description:"",startDate:null,endDate:null,searchData:"",visibility:!0,images:[],checked:!1,file_category:"",nonMembers:[],images_src:[],members:[],doc_src:[],document:[]}),[h,b]=(0,d.useState)(""),{t:g}=(0,m.useTranslation)("common"),v=(0,i.useRouter)().query.routes||[];(0,d.useEffect)(()=>{f()},[]);let f=async()=>{let e=await q.A.get(`/vspace/${v[1]}`),r=await q.A.get(`/users/${v[3]}`);e&&(t(e.visibility),l(e)),r&&b(r.username)},P=async()=>{s.visibility=!0;let e=x().find(s.subscribers,{_id:v[3]});null==e&&s.subscribers.push(v[3]);let r=await q.A.post(`/vspace/acceptSubscriptionRequest/${s._id}`,s);r&&r._id&&(t(!0),c.default.success(g("Vspaceissuccessfullyaccepted")),a().push("/vspace")),"Not authorized"==r&&c.default.error(g("Youarenotauthorized"))};return(0,o.jsx)("div",{children:(0,o.jsx)(u.A,{className:"my-4",children:(0,o.jsxs)(p.A,{children:[(0,o.jsx)("div",{children:"Welcome to Robert Koch Institut !"}),(0,o.jsx)("b",{children:h})," has requested to access your private virtual space ",(0,o.jsx)("b",{children:s.title}),(0,o.jsx)("br",{}),(0,o.jsx)(n.A,{disabled:r,className:"me-2",type:"submit",variant:"primary",onClick:P,children:g("Accept")}),(0,o.jsx)(n.A,{disabled:r,className:"me-2",variant:"info",onClick:()=>{t(!0),c.default.error(g("VspaceissuccessfullyDeclined")),a().push("/vspace")},children:g("Decline")})]})})})};s()}catch(e){s(e)}})},33873:e=>{e.exports=require("path")},36653:e=>{e.exports=require("nprogress")},36955:e=>{e.exports=require("@restart/hooks/useBreakpoint")},37766:e=>{e.exports=require("@restart/hooks/useMergedRefs")},39756:e=>{e.exports=import("redux")},40051:e=>{e.exports=require("dom-helpers/removeClass")},40361:e=>{e.exports=require("next/dist/compiled/next-server/pages.runtime.prod.js")},42893:e=>{e.exports=import("react-hot-toast")},43294:e=>{e.exports=require("formik")},50009:e=>{e.exports=require("@restart/ui/DropdownMenu")},57664:e=>{e.exports=require("dom-helpers/scrollbarSize")},58363:(e,r,t)=>{t.a(e,async(e,s)=>{try{t.r(r),t.d(r,{config:()=>h,default:()=>x,getServerSideProps:()=>m,getStaticPaths:()=>q,getStaticProps:()=>d,reportWebVitals:()=>b,routeModule:()=>A,unstable_getServerProps:()=>P,unstable_getServerSideProps:()=>S,unstable_getStaticParams:()=>f,unstable_getStaticPaths:()=>v,unstable_getStaticProps:()=>g});var o=t(63885),i=t(80237),a=t(81413),u=t(9616),p=t.n(u),n=t(72386),c=t(33852),l=e([n,c]);[n,c]=l.then?(await l)():l;let x=(0,a.M)(c,"default"),d=(0,a.M)(c,"getStaticProps"),q=(0,a.M)(c,"getStaticPaths"),m=(0,a.M)(c,"getServerSideProps"),h=(0,a.M)(c,"config"),b=(0,a.M)(c,"reportWebVitals"),g=(0,a.M)(c,"unstable_getStaticProps"),v=(0,a.M)(c,"unstable_getStaticPaths"),f=(0,a.M)(c,"unstable_getStaticParams"),P=(0,a.M)(c,"unstable_getServerProps"),S=(0,a.M)(c,"unstable_getServerSideProps"),A=new o.PagesRouteModule({definition:{kind:i.A.PAGES,page:"/vspace/AcceptRequestVspace",pathname:"/vspace/AcceptRequestVspace",bundlePath:"",filename:""},components:{App:n.default,Document:p()},userland:c});s()}catch(e){s(e)}})},58928:e=>{e.exports=require("dom-helpers/canUseDOM")},59717:e=>{e.exports=require("@restart/ui/DropdownContext")},60560:e=>{e.exports=require("dom-helpers/contains")},63033:e=>{e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},65447:e=>{e.exports=require("@restart/ui/Button")},67364:e=>{e.exports=require("@restart/hooks/useCallbackRef")},68455:e=>{e.exports=import("redux-saga")},69722:e=>{e.exports=require("es6-promise")},74075:e=>{e.exports=require("zlib")},74716:e=>{e.exports=require("moment")},74987:e=>{e.exports=require("@restart/ui/ModalManager")},78097:e=>{e.exports=require("next-redux-saga")},78634:e=>{e.exports=require("@restart/ui/Anchor")},80237:(e,r)=>{Object.defineProperty(r,"A",{enumerable:!0,get:function(){return t}});var t=function(e){return e.PAGES="PAGES",e.PAGES_API="PAGES_API",e.APP_PAGE="APP_PAGE",e.APP_ROUTE="APP_ROUTE",e.IMAGE="IMAGE",e}({})},81366:e=>{e.exports=require("redux-auth-wrapper/connectedAuthWrapper")},81413:(e,r)=>{Object.defineProperty(r,"M",{enumerable:!0,get:function(){return function e(r,t){return t in r?r[t]:"then"in r&&"function"==typeof r.then?r.then(r=>e(r,t)):"function"==typeof r&&"default"===t?r:void 0}}})},81521:e=>{e.exports=require("dom-helpers/querySelectorAll")},81895:e=>{e.exports=require("@restart/hooks/useEventCallback")},82015:e=>{e.exports=require("react")},86842:e=>{e.exports=require("@restart/ui/SelectableContext")},87571:e=>{e.exports=require("dom-helpers/transitionEnd")},88751:e=>{e.exports=require("next-i18next")},93787:e=>{e.exports=require("redux-persist")},94947:e=>{e.exports=require("@restart/hooks/useTimeout")},96196:e=>{e.exports=require("next-redux-wrapper")},98320:e=>{e.exports=require("@restart/hooks/useIsomorphicEffect")},99460:e=>{e.exports=require("dom-helpers/css")}};var r=require("../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[6089,9216,9616,2386],()=>t(58363));module.exports=s})();