{"version": 3, "file": "static/chunks/pages/adminsettings/user-2c3b1db79a73d64e.js", "mappings": "8EACA,4CACA,sBACA,WACA,OAAe,EAAQ,KAAiD,CACxE,EACA,SAFsB", "sources": ["webpack://_N_E/?5925"], "sourcesContent": ["\n    (window.__NEXT_P = window.__NEXT_P || []).push([\n      \"/adminsettings/user\",\n      function () {\n        return require(\"private-next-pages/adminsettings/user/index.tsx\");\n      }\n    ]);\n    if(module.hot) {\n      module.hot.dispose(function () {\n        window.__NEXT_P.push([\"/adminsettings/user\"])\n      });\n    }\n  "], "names": [], "sourceRoot": "", "ignoreList": []}