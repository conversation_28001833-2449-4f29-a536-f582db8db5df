{"version": 3, "file": "static/chunks/pages/event/components/DiscussionAccordion-bd833062f09ffaad.js", "mappings": "gFACA,4CACA,wCACA,WACA,OAAe,EAAQ,KAA6D,CACpF,EACA,SAFsB,sIC8BtB,MAxB4B,IACxB,GAAM,CAAEA,GAAC,CAAE,CAAGC,CAAAA,EAAAA,EAAAA,EAAAA,CAAcA,CAAC,MAuBlBC,IAtBL,CAACC,EAASC,EAAW,CAAGC,CAAAA,EAAAA,EAAAA,IAsBAH,EAAC,EAtBDG,CAAQA,CAAC,IACvC,MACI,+BACI,WAACC,EAAAA,CAASA,CAACC,IAAI,EAACC,SAAS,cACrB,WAACF,EAAAA,CAASA,CAACG,MAAM,EAACC,QAAS,IAAMN,EAAW,CAACD,aACzC,UAACQ,MAAAA,CAAIC,UAAU,qBAAaZ,EAAE,6BAC9B,UAACW,MAAAA,CAAIC,UAAU,qBACVT,EACG,UAACU,EAAAA,CAAeA,CAAAA,CAACC,KAAMC,EAAAA,GAAOA,CAAEC,MAAM,SAEtC,UAACH,EAAAA,CAAeA,CAAAA,CAACC,KAAMG,EAAAA,GAAMA,CAAED,MAAM,cAIjD,UAACV,EAAAA,CAASA,CAACY,IAAI,WACX,UAACC,EAAAA,CAAUA,CAAAA,CAACC,KAAK,QAAQC,GAAIC,OAAAA,EAAAA,KAAAA,EAAAA,EAAOC,MAAAA,CAAPD,CAAgBA,EAAMC,MAAM,CAAC,EAAE,CAAG,aAKnF,yBCxBuC,CAGtC,YAA4B,aAI7B,oBACA,OACA,iCACA,iCACA,0BACA,0BACA,+BACA,8BACA,8BACA,4BACA,6BAEA,yBAGA,qBACA,qGACA,KAEA,YACA,2EACA,oBACA,SACA,oEACA,KAEA,uDACA,8CACA,sBACA,gBACA,WACA,eACA,eACA,kBACA,yBACA,+BACA,CAAS,CACT,UACA,8BACA,aACA,+BACA,8BACA,gCACA,uCACA,CAAS,CACT,cACA,eACA,cACA,sBACA,iBACA,IACA,gBACA,IACA,gBACA,IACA,KACA,IACA,eACA,IACA,KACA,IACA,IACA,CAAS,CACT,2BAAoC,IAAI,IACxC,cACA,MACA,MACA,KACA,CAAS,CACJ,EAIL,CAAC,CAhFiD,EAAQ,KAAW,YAAZ", "sources": ["webpack://_N_E/?dcf8", "webpack://_N_E/./pages/event/components/DiscussionAccordion.tsx", "webpack://_N_E/./node_modules/moment/locale/de.js"], "sourcesContent": ["\n    (window.__NEXT_P = window.__NEXT_P || []).push([\n      \"/event/components/DiscussionAccordion\",\n      function () {\n        return require(\"private-next-pages/event/components/DiscussionAccordion.tsx\");\n      }\n    ]);\n    if(module.hot) {\n      module.hot.dispose(function () {\n        window.__NEXT_P.push([\"/event/components/DiscussionAccordion\"])\n      });\n    }\n  ", "//Import Library\r\nimport React, { useState } from \"react\";\r\nimport { Accordion, Card } from \"react-bootstrap\";\r\nimport { FontAwesomeIcon } from \"@fortawesome/react-fontawesome\";\r\nimport { faMinus, faPlus } from \"@fortawesome/free-solid-svg-icons\";\r\n\r\n//Import services/components\r\nimport { useTranslation } from 'next-i18next';\r\nimport Discussion from \"../../../components/common/disussion\";\r\n\r\nconst DiscussionAccordion = (props: any) => {\r\n    const { t } = useTranslation('common');\r\n    const [section, setSection] = useState(false);\r\n    return (\r\n        <>\r\n            <Accordion.Item eventKey=\"0\">\r\n                <Accordion.Header onClick={() => setSection(!section)}>\r\n                    <div className=\"cardTitle\">{t(\"Events.show.Discussions\")}</div>\r\n                    <div className=\"cardArrow\">\r\n                        {section ? (\r\n                            <FontAwesomeIcon icon={faMinus} color=\"#fff\" />\r\n                        ) : (\r\n                            <FontAwesomeIcon icon={faPlus} color=\"#fff\" />\r\n                        )}\r\n                    </div>\r\n                </Accordion.Header>\r\n                <Accordion.Body>\r\n                    <Discussion type=\"event\" id={props?.routes ? props.routes[1] : null} />\r\n                </Accordion.Body>\r\n            </Accordion.Item>\r\n        </>\r\n    )\r\n};\r\n\r\nexport default DiscussionAccordion;", "//! moment.js locale configuration\n//! locale : German [de]\n//! author : lluchs : https://github.com/lluchs\n//! author: <PERSON><PERSON><PERSON>: https://github.com/Oire\n//! author : <PERSON><PERSON><PERSON><PERSON> : https://github.com/mik01aj\n\n;(function (global, factory) {\n   typeof exports === 'object' && typeof module !== 'undefined'\n       && typeof require === 'function' ? factory(require('../moment')) :\n   typeof define === 'function' && define.amd ? define(['../moment'], factory) :\n   factory(global.moment)\n}(this, (function (moment) { 'use strict';\n\n    //! moment.js locale configuration\n\n    function processRelativeTime(number, withoutSuffix, key, isFuture) {\n        var format = {\n            m: ['eine Minute', 'einer Minute'],\n            h: ['eine Stunde', 'einer Stunde'],\n            d: ['ein Tag', 'einem Tag'],\n            dd: [number + ' Tage', number + ' Tagen'],\n            w: ['eine Woche', 'einer Woche'],\n            M: ['ein <PERSON><PERSON>', 'einem Monat'],\n            MM: [number + ' <PERSON><PERSON>', number + ' Monaten'],\n            y: ['ein Jahr', 'einem Jahr'],\n            yy: [number + ' Jahre', number + ' Jahren'],\n        };\n        return withoutSuffix ? format[key][0] : format[key][1];\n    }\n\n    var de = moment.defineLocale('de', {\n        months: 'Januar_Februar_März_April_Mai_Juni_Juli_August_September_Oktober_November_Dezember'.split(\n            '_'\n        ),\n        monthsShort:\n            'Jan._Feb._März_Apr._Mai_Juni_Juli_Aug._Sep._Okt._Nov._Dez.'.split('_'),\n        monthsParseExact: true,\n        weekdays:\n            'Sonntag_Montag_Dienstag_Mittwoch_Donnerstag_Freitag_Samstag'.split(\n                '_'\n            ),\n        weekdaysShort: 'So._Mo._Di._Mi._Do._Fr._Sa.'.split('_'),\n        weekdaysMin: 'So_Mo_Di_Mi_Do_Fr_Sa'.split('_'),\n        weekdaysParseExact: true,\n        longDateFormat: {\n            LT: 'HH:mm',\n            LTS: 'HH:mm:ss',\n            L: 'DD.MM.YYYY',\n            LL: 'D. MMMM YYYY',\n            LLL: 'D. MMMM YYYY HH:mm',\n            LLLL: 'dddd, D. MMMM YYYY HH:mm',\n        },\n        calendar: {\n            sameDay: '[heute um] LT [Uhr]',\n            sameElse: 'L',\n            nextDay: '[morgen um] LT [Uhr]',\n            nextWeek: 'dddd [um] LT [Uhr]',\n            lastDay: '[gestern um] LT [Uhr]',\n            lastWeek: '[letzten] dddd [um] LT [Uhr]',\n        },\n        relativeTime: {\n            future: 'in %s',\n            past: 'vor %s',\n            s: 'ein paar Sekunden',\n            ss: '%d Sekunden',\n            m: processRelativeTime,\n            mm: '%d Minuten',\n            h: processRelativeTime,\n            hh: '%d Stunden',\n            d: processRelativeTime,\n            dd: processRelativeTime,\n            w: processRelativeTime,\n            ww: '%d Wochen',\n            M: processRelativeTime,\n            MM: processRelativeTime,\n            y: processRelativeTime,\n            yy: processRelativeTime,\n        },\n        dayOfMonthOrdinalParse: /\\d{1,2}\\./,\n        ordinal: '%d.',\n        week: {\n            dow: 1, // Monday is the first day of the week.\n            doy: 4, // The week that contains Jan 4th is the first week of the year.\n        },\n    });\n\n    return de;\n\n})));\n"], "names": ["t", "useTranslation", "DiscussionAccordion", "section", "setSection", "useState", "Accordion", "<PERSON><PERSON>", "eventKey", "Header", "onClick", "div", "className", "FontAwesomeIcon", "icon", "faMinus", "color", "faPlus", "Body", "Discussion", "type", "id", "props", "routes"], "sourceRoot": "", "ignoreList": [2]}