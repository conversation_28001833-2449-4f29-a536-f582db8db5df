"use strict";exports.id=7032,exports.ids=[7032],exports.modules={67032:(e,s,i)=>{i.a(e,async(e,t)=>{try{i.r(s),i.d(s,{default:()=>f});var r=i(8732),n=i(82015),d=i(7082),a=i(18597),o=i(83551),l=i(49481),c=i(59549),m=i(91353),u=i(66994),h=i(23579),g=i(42893),x=i(44233),A=i.n(x),C=i(19918),j=i.n(C),p=i(82053),y=i(54131),_=i(88751),v=i(63487),F=e([g,y,v]);[g,y,v]=F.then?(await F)():F;let f=e=>{let{t:s}=(0,_.useTranslation)("common"),i={_id:"",title:"",code:"",code3:"",dial_code:"",coordinates:[{latitude:"",longitude:""}],world_region:"",health_profile:"",security_advice:""},[t,x]=(0,n.useState)(i),[C,F]=(0,n.useState)([]),[f,b]=(0,n.useState)(!1),w=e.routes&&(e.routes[0]===s("adminsetting.Countries.Forms.edit_country")||"edit_land"===s("adminsetting.Countries.Forms.edit_country"))&&e.routes[1],N=(0,n.useRef)(null),k=async(i,r)=>{let n,d;i.preventDefault();let a=r||t,o=a.title.charAt(0).toUpperCase()+a.title.slice(1),l={title:o?.trim(),title_de:o?.trim(),code:a.code?.trim(),code3:a.code3,dial_code:a.dial_code,first_letter:{en:a.title.charAt(0).toUpperCase(),fr:a.title.charAt(0).toUpperCase(),de:a.title.charAt(0).toUpperCase()},coordinates:a.coordinates,world_region:a.world_region,health_profile:a.health_profile,security_advice:a.security_advice};w?(d="adminsetting.Countries.Forms.Countryisupdatedsuccessfully",n=await v.A.patch(`/country/${e.routes[1]}`,l)):(d="adminsetting.Countries.Forms.Countryisaddedsuccessfully",n=await v.A.post("/country",l)),n&&n._id?(g.default.success(s(d)),A().push("/adminsettings/country")):n?.errorCode===11e3?g.default.error(s("duplicatesNotAllowed")):g.default.error(n)},L=e=>{if(e.target){let{name:s,value:i}=e.target;"longitude"===s||"latitude"===s?x(e=>({...e,coordinates:[{...t.coordinates?.[0],[s]:i}]})):x(e=>({...e,[s]:i}))}},M=async e=>{let s=await v.A.get("/worldregion",e);s&&F(s.data)};return(0,n.useEffect)(()=>{let s={query:{},sort:{title:"asc"},limit:"~"};w&&(async s=>{let i=await v.A.get(`/country/${e.routes[1]}`,s);if(i){let{world_region:e}=i;i.world_region=e&&e._id?e._id:"",x(e=>({...e,...i}))}})(s),M(s)},[]),(0,r.jsx)("div",{children:(0,r.jsx)(d.A,{className:"formCard",fluid:!0,children:(0,r.jsx)(a.A,{style:{marginTop:"5px",boxShadow:"0 4px 8px 0 rgba(0, 0, 0, 0.2), 0 6px 20px 0 rgba(0, 0, 0, 0.19)"},children:(0,r.jsx)(u.A,{onSubmit:k,ref:N,initialValues:t,enableReinitialize:!0,children:(0,r.jsxs)(a.A.Body,{children:[(0,r.jsx)(o.A,{children:(0,r.jsx)(l.A,{children:(0,r.jsx)(a.A.Title,{children:w?s("adminsetting.Countries.Forms.EditCountry"):s("adminsetting.Countries.Forms.AddCountry")})})}),(0,r.jsx)("hr",{}),(0,r.jsx)(o.A,{className:"mb-3",children:(0,r.jsx)(l.A,{md:!0,lg:12,sm:12,children:(0,r.jsxs)(c.A.Group,{children:[(0,r.jsx)(c.A.Label,{className:"required-field",children:s("adminsetting.Countries.Forms.CountryName")}),(0,r.jsx)(h.ks,{name:"title",id:"title",required:!0,value:t.title,validator:e=>""!==String(e||"").trim(),errorMessage:{validator:s("adminsetting.Countries.Forms.PleaseAddtheCountryName")},onChange:L})]})})}),(0,r.jsxs)(o.A,{className:"mb-3",children:[(0,r.jsx)(l.A,{md:!0,lg:4,sm:12,children:(0,r.jsxs)(c.A.Group,{children:[(0,r.jsx)(c.A.Label,{className:"required-field",children:s("adminsetting.Countries.Forms.CountryCode")}),(0,r.jsx)(h.ks,{name:"code",id:"code",required:!0,value:t.code,errorMessage:{validator:s("adminsetting.Countries.Forms.PleaseAddtheCountryCode")},onChange:L})]})}),(0,r.jsx)(l.A,{md:!0,lg:4,sm:12,children:(0,r.jsxs)(c.A.Group,{children:[(0,r.jsx)(c.A.Label,{children:s("adminsetting.Countries.Forms.CountryCode3")}),(0,r.jsx)(h.ks,{name:"code3",id:"code3",value:t.code3,errorMessage:s("adminsetting.Countries.Forms.PleaseAddtheCountryCode3"),onChange:L})]})}),(0,r.jsx)(l.A,{md:!0,lg:4,sm:12,children:(0,r.jsxs)(c.A.Group,{children:[(0,r.jsxs)(c.A.Label,{children:[s("adminsetting.Countries.Forms.DialCode")," "]}),(0,r.jsx)(h.ks,{name:"dial_code",id:"dial code",value:t.dial_code,errorMessage:s("adminsetting.Countries.Forms.PleaseAddtheCountryDialCode"),onChange:L})]})})]}),(0,r.jsx)(o.A,{className:"mb-3",children:(0,r.jsx)(l.A,{children:(0,r.jsx)(a.A.Title,{children:s("adminsetting.Countries.Forms.Co-ordinates")})})}),f?(0,r.jsxs)(o.A,{className:"mb-3",children:[(0,r.jsx)(l.A,{md:!0,lg:4,sm:12,children:(0,r.jsxs)(c.A.Group,{children:[(0,r.jsx)(c.A.Label,{children:s("adminsetting.Countries.Forms.Latitude")}),(0,r.jsx)(h.ks,{name:"latitude",id:"latitude",value:t.coordinates?.[0]?.latitude||"",errorMessage:s("adminsetting.Countries.Forms.PleaseAddtheLatitude"),onChange:L})]})}),(0,r.jsx)(l.A,{md:!0,lg:4,sm:12,children:(0,r.jsxs)(c.A.Group,{children:[(0,r.jsx)(c.A.Label,{children:s("adminsetting.Countries.Forms.Longitude")}),(0,r.jsx)(h.ks,{name:"longitude",id:"longitude",value:t.coordinates?.[0]?.longitude||"",errorMessage:s("adminsetting.Countries.Forms.PleaseAddtheLongitude"),onChange:L})]})}),(0,r.jsx)(l.A,{md:!0,lg:4,sm:12,children:(0,r.jsx)("div",{style:{marginTop:"30px"},children:(0,r.jsxs)(m.A,{variant:"secondary",onClick:()=>b(!f),children:[" ",(0,r.jsx)(p.FontAwesomeIcon,{icon:y.faMapMarkerAlt,className:"me-2"}),s("adminsetting.Countries.Forms.HideCoordinates")]})})})]}):(0,r.jsx)("div",{className:"mb-3",children:(0,r.jsxs)(m.A,{variant:"secondary",onClick:()=>b(!f),children:[" ",(0,r.jsx)(p.FontAwesomeIcon,{icon:y.faMapMarkerAlt,className:"me-2"}),s("adminsetting.Countries.Forms.coordinatesBtnText")]})}),(0,r.jsxs)(o.A,{className:"mb-3",children:[(0,r.jsx)(l.A,{md:!0,lg:4,sm:12,children:(0,r.jsxs)(c.A.Group,{children:[(0,r.jsx)(c.A.Label,{className:"required-field",children:s("adminsetting.Countries.Forms.WorldRegion")}),(0,r.jsxs)(h.s3,{name:"world_region",id:"world_region",value:t.world_region,errorMessage:{validator:s("adminsetting.Countries.Forms.PleaseAddtheWorldRegion")},required:!0,onChange:L,children:[(0,r.jsx)("option",{value:"",children:s("adminsetting.Countries.Forms.SelectWorldRegion")}),C.length>=1?C.map((e,s)=>(0,r.jsx)("option",{value:e._id,children:e.title},e._id)):null]})]})}),(0,r.jsx)(l.A,{md:!0,lg:4,sm:12,children:(0,r.jsxs)(c.A.Group,{children:[(0,r.jsx)(c.A.Label,{children:s("adminsetting.Countries.Forms.Healthprofile")}),(0,r.jsx)(h.ks,{name:"health_profile",id:"health_profile",value:t.health_profile,errorMessage:s("adminsetting.Countries.Forms.PleaseAddtheHealthProfile"),onChange:L})]})}),(0,r.jsx)(l.A,{md:!0,lg:4,sm:12,children:(0,r.jsxs)(c.A.Group,{children:[(0,r.jsx)(c.A.Label,{children:s("adminsetting.Countries.Forms.SecurityAdvice")}),(0,r.jsx)(h.ks,{name:"security_advice",id:"security_advice",value:t.security_advice,errorMessage:s("adminsetting.Countries.Forms.PleaseAddtheSecurityAdvice"),onChange:L})]})})]}),(0,r.jsx)(o.A,{className:"my-4",children:(0,r.jsxs)(l.A,{children:[(0,r.jsx)(m.A,{className:"me-2",type:"submit",variant:"primary",children:s("adminsetting.Countries.Forms.Submit")}),(0,r.jsx)(m.A,{className:"me-2",onClick:()=>{x(i),window.scrollTo(0,0)},variant:"info",children:s("adminsetting.Countries.Forms.Reset")}),(0,r.jsx)(j(),{href:"/adminsettings/[...routes]",as:"/adminsettings/country",children:(0,r.jsx)(m.A,{variant:"secondary",children:s("adminsetting.Countries.Forms.Cancel")})})]})})]})})})})})};t()}catch(e){t(e)}})}};