"use strict";exports.id=3620,exports.ids=[3620],exports.modules={5517:(e,s,t)=>{t.a(e,async(e,n)=>{try{t.r(s),t.d(s,{default:()=>x});var i=t(8732);t(82015);var a=t(83551),r=t(49481),o=t(93024),c=t(79029),l=t(75268),d=t(32566),u=t(28241),m=t(30336),h=e([c,d,u,m]);[c,d,u,m]=h.then?(await h)():h;let x=e=>{let s=(0,l.canViewDiscussionUpdate)(()=>(0,i.jsx)(d.default,{...e.routeData}));return(0,i.jsx)(i.Fragment,{children:(0,i.jsx)(a.A,{children:(0,i.jsxs)(r.A,{className:"eventAccordion",xs:12,children:[(0,i.jsx)(o.A,{children:(0,i.jsx)(c.default,{...e.eventData})}),(0,i.jsx)(o.A,{children:(0,i.jsx)(u.default,{...e.eventData})}),(0,i.jsx)(o.A,{children:(0,i.jsx)(m.default,{...e.eventData})}),(0,i.jsx)(o.A,{children:(0,i.jsx)(s,{})})]})})})};n()}catch(e){n(e)}})},12876:(e,s,t)=>{t.a(e,async(e,n)=>{try{t.r(s),t.d(s,{default:()=>f});var i=t(8732);t(82015);var a=t(91353),r=t(83551),o=t(49481),c=t(19918),l=t.n(c),d=t(82053),u=t(54131),m=t(63349),h=t(81426),x=t(88751),j=t(75268),v=e([u,h]);[u,h]=v.then?(await v)():v;let f=e=>{let{t:s}=(0,x.useTranslation)("common"),t=()=>(0,i.jsx)(l(),{href:"/event/[...routes]",as:`/event/edit/${e.routeData.routes[1]}`,children:(0,i.jsxs)(a.A,{variant:"secondary",size:"sm",children:[(0,i.jsx)(d.FontAwesomeIcon,{icon:u.faPen}),"\xa0",s("Events.show.Edit")]})}),n=(0,j.canEditEvent)(()=>(0,i.jsx)(t,{}));return(0,i.jsx)(i.Fragment,{children:(0,i.jsxs)(r.A,{children:[(0,i.jsxs)(o.A,{md:11,children:[(0,i.jsx)("div",{className:"d-flex justify-content-between",children:(0,i.jsxs)("h4",{children:[e?.eventData?.country?`${e.eventData.country.title} | ${String(e.eventData.hazard.map(e=>e&&e.title&&e.title.en?e.title.en:"").join(", "))} (${e.eventData.title})`:"","\xa0\xa0",e?.editAccess&&e?.routeData?.routes[1]?(0,i.jsx)(n,{event:e.eventData}):null]})}),(0,i.jsx)("hr",{}),(0,i.jsx)(m.A,{description:e.eventData.description})]}),(0,i.jsx)(o.A,{md:1,children:(0,i.jsx)(h.A,{entityId:e.routeData.routes[1],entityType:"event"})})]})})};n()}catch(e){n(e)}})},28241:(e,s,t)=>{t.a(e,async(e,n)=>{try{t.r(s),t.d(s,{default:()=>m});var i=t(8732),a=t(82015),r=t(93024),o=t(82053),c=t(54131),l=t(88751),d=t(63349),u=e([c]);c=(u.then?(await u)():u)[0];let m=e=>{let{t:s}=(0,l.useTranslation)("common"),[t,n]=(0,a.useState)(!1);return(0,i.jsx)(i.Fragment,{children:(0,i.jsxs)(r.A.Item,{eventKey:"0",children:[(0,i.jsxs)(r.A.Header,{onClick:()=>n(!t),children:[(0,i.jsx)("div",{className:"cardTitle",children:s("Events.show.MoreInformation")}),(0,i.jsx)("div",{className:"cardArrow",children:t?(0,i.jsx)(o.FontAwesomeIcon,{icon:c.faMinus,color:"#fff"}):(0,i.jsx)(o.FontAwesomeIcon,{icon:c.faPlus,color:"#fff"})})]}),(0,i.jsx)(r.A.Body,{children:(0,i.jsx)(d.A,{description:e.more_info})})]})})};n()}catch(e){n(e)}})},30336:(e,s,t)=>{t.a(e,async(e,n)=>{try{t.r(s),t.d(s,{default:()=>m});var i=t(8732),a=t(82015),r=t(54131),o=t(82053),c=t(93024),l=t(42447),d=t(88751),u=e([r,l]);[r,l]=u.then?(await u)():u;let m=e=>{let{t:s}=(0,d.useTranslation)("common"),[t,n]=(0,a.useState)(!1);return(0,i.jsx)(i.Fragment,{children:(0,i.jsxs)(c.A.Item,{eventKey:"0",children:[(0,i.jsxs)(c.A.Header,{onClick:()=>n(!t),children:[(0,i.jsx)("div",{className:"cardTitle",children:s("Events.show.MediaGallery")}),(0,i.jsx)("div",{className:"cardArrow",children:t?(0,i.jsx)(o.FontAwesomeIcon,{icon:r.faMinus,color:"#fff"}):(0,i.jsx)(o.FontAwesomeIcon,{icon:r.faPlus,color:"#fff"})})]}),(0,i.jsx)(c.A.Body,{children:(0,i.jsx)(l.A,{gallery:e.images,imageSource:e.images_src})})]})})};n()}catch(e){n(e)}})},32566:(e,s,t)=>{t.a(e,async(e,n)=>{try{t.r(s),t.d(s,{default:()=>m});var i=t(8732),a=t(82015),r=t(93024),o=t(82053),c=t(54131),l=t(88751),d=t(82491),u=e([c,d]);[c,d]=u.then?(await u)():u;let m=e=>{let{t:s}=(0,l.useTranslation)("common"),[t,n]=(0,a.useState)(!1);return(0,i.jsx)(i.Fragment,{children:(0,i.jsxs)(r.A.Item,{eventKey:"0",children:[(0,i.jsxs)(r.A.Header,{onClick:()=>n(!t),children:[(0,i.jsx)("div",{className:"cardTitle",children:s("Events.show.Discussions")}),(0,i.jsx)("div",{className:"cardArrow",children:t?(0,i.jsx)(o.FontAwesomeIcon,{icon:c.faMinus,color:"#fff"}):(0,i.jsx)(o.FontAwesomeIcon,{icon:c.faPlus,color:"#fff"})})]}),(0,i.jsx)(r.A.Body,{children:(0,i.jsx)(d.A,{type:"event",id:e?.routes?e.routes[1]:null})})]})})};n()}catch(e){n(e)}})},43620:(e,s,t)=>{t.a(e,async(e,n)=>{try{t.r(s),t.d(s,{default:()=>h});var i=t(8732),a=t(82015),r=t(7082),o=t(77136),c=t(63487),l=t(12876),d=t(11314),u=t(5517),m=e([o,c,l,u]);[o,c,l,u]=m.then?(await m)():m;let h=e=>{let[s,t]=(0,a.useState)(!1),[n,m]=(0,a.useState)({title:"",laboratory_confirmed:"",officially_validated:"",status:{title:""},syndrome:{title:""},country:{title:""},hazard_type:{title:""},rki_monitored:"",risk_assessment:{country:{title:""},international:{title:""},region:{title:""}},hazard:[],country_regions:[],operation:{title:""},description:"",more_info:"",images:[],images_src:[]}),h=async(s,n)=>{let i=await c.A.get(`/event/${e.routes[1]}`,s);i&&(m(i),function(e,s){t(!1),s&&s.roles&&(s.roles.includes("SUPER_ADMIN")||s.roles.includes("GENERAL_USER")&&e.user._id==s._id?t(!0):s.roles.includes("PLATFORM_ADMIN")&&e.user._id==s._id&&t(!0))}(i,n))};(0,a.useEffect)(()=>{e.routes&&e.routes[1]&&x()},[]);let x=async()=>{let e=await c.A.post("/users/getLoggedUser",{});h({},e)},j={eventData:n,routeData:e,editAccess:s};return(0,i.jsxs)(r.A,{className:"eventDetail",fluid:!0,children:[(0,i.jsx)(o.A,{routes:e.routes}),(0,i.jsx)(l.default,{...j}),(0,i.jsx)(d.default,{eventData:n}),(0,i.jsx)(u.default,{...j})]})};n()}catch(e){n(e)}})},75268:(e,s,t)=>{t.r(s),t.d(s,{canAddEvent:()=>o,canAddEventForm:()=>c,canEditEvent:()=>l,canEditEventForm:()=>d,canViewDiscussionUpdate:()=>u,default:()=>m});var n=t(8732);t(82015);var i=t(81366),a=t.n(i),r=t(61421);let o=a()({authenticatedSelector:e=>!!e.permissions&&!!e.permissions.event&&!!e.permissions.event["create:any"],wrapperDisplayName:"CanAddEvent"}),c=a()({authenticatedSelector:e=>!!e.permissions&&!!e.permissions.event&&!!e.permissions.event["create:any"],wrapperDisplayName:"CanAddEventForm",FailureComponent:()=>(0,n.jsx)(r.default,{})}),l=a()({authenticatedSelector:(e,s)=>{if(e.permissions&&e.permissions.event){if(e.permissions.event["update:any"])return!0;else if(e.permissions.event["update:own"]&&s.event&&s.event.user&&s.event.user._id===e.user._id)return!0}return!1},wrapperDisplayName:"CanEditEvent"}),d=a()({authenticatedSelector:(e,s)=>{if(e.permissions&&e.permissions.event){if(e.permissions.event["update:any"])return!0;else if(e.permissions.event["update:own"]&&s.event&&s.event.user&&s.event.user._id===e.user._id)return!0}return!1},wrapperDisplayName:"CanEditEventForm",FailureComponent:()=>(0,n.jsx)(r.default,{})}),u=a()({authenticatedSelector:e=>!!e.permissions&&!!e.permissions.update&&!!e.permissions.update["read:any"],wrapperDisplayName:"CanViewDiscussionUpdate"}),m=o},79029:(e,s,t)=>{t.a(e,async(e,n)=>{try{t.r(s),t.d(s,{default:()=>h});var i=t(8732),a=t(82015),r=t(54131),o=t(82053),c=t(93024),l=t(88751),d=t(63349),u=e([r]);r=(u.then?(await u)():u)[0];let m={Low:"risk0",Medium:"risk1",High:"risk2","Very High":"risk3"},h=e=>{let{t:s}=(0,l.useTranslation)("common"),[t,n]=(0,a.useState)(!1),{risk_assessment:u}=e;return(0,i.jsx)(i.Fragment,{children:(0,i.jsxs)(c.A.Item,{eventKey:"0",children:[(0,i.jsxs)(c.A.Header,{onClick:()=>n(!t),children:[(0,i.jsx)("div",{className:"cardTitle",children:s("Events.show.RiskAssessment")}),(0,i.jsx)("div",{className:"cardArrow",children:t?(0,i.jsx)(o.FontAwesomeIcon,{icon:r.faMinus,color:"#fff"}):(0,i.jsx)(o.FontAwesomeIcon,{icon:r.faPlus,color:"#fff"})})]}),(0,i.jsx)(c.A.Body,{children:u.country?(0,i.jsxs)("div",{children:[function(e,s){return(0,i.jsxs)("div",{className:"riskDetails",children:[e.country?(0,i.jsxs)("div",{className:"riskItems",children:[(0,i.jsx)("div",{className:`riskIcon ${m[e.country.title]}`,children:(0,i.jsx)("img",{src:"/images/event_country.png",width:"30",height:"30",alt:"Risk Assessment Info"})}),(0,i.jsxs)("div",{className:"riskInfo",children:[(0,i.jsx)("h5",{children:s("Events.show.Country")}),(0,i.jsx)("h4",{children:e.country.title})]})]}):(0,i.jsx)(i.Fragment,{}),e.region?(0,i.jsxs)("div",{className:"riskItems",children:[(0,i.jsx)("div",{className:`riskIcon ${e&&e.region?m[e.region.title]:""}`,children:(0,i.jsx)("img",{src:"/images/event_region.png",width:"35",height:"26",alt:"Risk Assessment Info"})}),(0,i.jsxs)("div",{className:"riskInfo",children:[(0,i.jsx)("h5",{children:s("Events.show.Region")}),(0,i.jsx)("h4",{children:e&&e.region?e.region.title:""})]})]}):(0,i.jsx)(i.Fragment,{}),e.international?(0,i.jsxs)("div",{className:"riskItems",children:[(0,i.jsx)("div",{className:`riskIcon ${e&&e.international?m[e.international.title]:""}`,children:(0,i.jsx)("img",{src:"/images/event_international.png",width:"38",height:"38",alt:"Risk Assessment Info"})}),(0,i.jsxs)("div",{className:"riskInfo",children:[(0,i.jsx)("h5",{children:s("Events.show.International")}),(0,i.jsx)("h4",{children:e&&e.international?e.international.title:""})]})]}):(0,i.jsx)(i.Fragment,{})]})}(u,s),function(e){return(0,i.jsx)("div",{className:"mt-4",children:(0,i.jsx)(d.A,{description:e&&e.risk_assessment.description?e.risk_assessment.description:""})})}(e)]}):null})]})})};n()}catch(e){n(e)}})},81426:(e,s,t)=>{t.a(e,async(e,n)=>{try{t.d(s,{A:()=>m});var i=t(8732),a=t(14062),r=t(54131),o=t(82015),c=t(82053),l=t(63487),d=e([a,r,l]);[a,r,l]=d.then?(await d)():d;let u={operation:"Operation",institution:"Institution",event:"Event",project:"Project",vspace:"Vspace"},m=(0,a.connect)(e=>e)(e=>{let{user:s,entityId:t,entityType:n}=e,[a,d]=(0,o.useState)(!1),[m,h]=(0,o.useState)(""),x=async()=>{if(!s?._id)return;let e=await l.A.get("/flag",{query:{entity_id:t,user:s._id,onModel:u[n]}});e&&e.data&&e.data.length>0&&(h(e.data[0]),d(!0))},j=async e=>{if(e.preventDefault(),!s?._id)return;let i=!a,r={entity_type:n,entity_id:t,user:s._id,onModel:u[n]};if(i){let e=await l.A.post("/flag",r);e&&e._id&&(h(e),d(i))}else{let e=await l.A.remove(`/flag/${m._id}`);e&&e.n&&d(i)}};return(0,o.useEffect)(()=>{x()},[]),(0,i.jsx)("div",{className:"subscribe-flag",children:(0,i.jsxs)("a",{href:"",onClick:j,children:[(0,i.jsx)("span",{className:"check",children:a?(0,i.jsx)(c.FontAwesomeIcon,{className:"clickable checkIcon",icon:r.faCheckCircle,color:"#00CC00"}):(0,i.jsx)(c.FontAwesomeIcon,{className:"clickable minusIcon",icon:r.faPlusCircle,color:"#fff"})}),(0,i.jsx)(c.FontAwesomeIcon,{className:"bookmark",icon:r.faBookmark,color:"#d4d4d4"})]})})});n()}catch(e){n(e)}})}};