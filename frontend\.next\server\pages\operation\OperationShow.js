"use strict";(()=>{var e={};e.id=785,e.ids=[636,785,3220],e.modules={123:e=>{e.exports=require("dom-helpers/removeEventListener")},1332:e=>{e.exports=require("react-custom-scrollbars-2")},1428:e=>{e.exports=import("axios")},1680:e=>{e.exports=require("@restart/ui/utils")},1919:e=>{e.exports=require("react-transition-group/Transition")},3892:e=>{e.exports=require("classnames")},4048:e=>{e.exports=require("react-truncate")},6009:e=>{e.exports=require("dom-helpers/ownerDocument")},6417:(e,r,t)=>{t.d(r,{A:()=>a});let s=t(82015).createContext(null);s.displayName="CardHeaderContext";let a=s},6952:e=>{e.exports=require("@restart/ui/Modal")},7374:e=>{e.exports=require("@restart/ui/Dropdown")},8732:e=>{e.exports=require("react/jsx-runtime")},9653:e=>{e.exports=require("@restart/ui/Overlay")},11242:e=>{e.exports=require("redux-persist/integration/react")},11688:e=>{e.exports=require("@restart/hooks/useWillUnmount")},12388:e=>{e.exports=require("dom-helpers/addEventListener")},13364:e=>{e.exports=require("redux-saga/effects")},14062:e=>{e.exports=import("react-redux")},14078:e=>{e.exports=import("swr")},14332:e=>{e.exports=require("uncontrollable")},16116:e=>{e.exports=require("invariant")},18597:(e,r,t)=>{t.d(r,{A:()=>v});var s=t(3892),a=t.n(s),o=t(82015),i=t(80739),n=t(8732);let l=o.forwardRef(({className:e,bsPrefix:r,as:t="div",...s},o)=>(r=(0,i.oU)(r,"card-body"),(0,n.jsx)(t,{ref:o,className:a()(e,r),...s})));l.displayName="CardBody";let p=o.forwardRef(({className:e,bsPrefix:r,as:t="div",...s},o)=>(r=(0,i.oU)(r,"card-footer"),(0,n.jsx)(t,{ref:o,className:a()(e,r),...s})));p.displayName="CardFooter";var u=t(6417);let d=o.forwardRef(({bsPrefix:e,className:r,as:t="div",...s},l)=>{let p=(0,i.oU)(e,"card-header"),d=(0,o.useMemo)(()=>({cardHeaderBsPrefix:p}),[p]);return(0,n.jsx)(u.A.Provider,{value:d,children:(0,n.jsx)(t,{ref:l,...s,className:a()(r,p)})})});d.displayName="CardHeader";let c=o.forwardRef(({bsPrefix:e,className:r,variant:t,as:s="img",...o},l)=>{let p=(0,i.oU)(e,"card-img");return(0,n.jsx)(s,{ref:l,className:a()(t?`${p}-${t}`:p,r),...o})});c.displayName="CardImg";let m=o.forwardRef(({className:e,bsPrefix:r,as:t="div",...s},o)=>(r=(0,i.oU)(r,"card-img-overlay"),(0,n.jsx)(t,{ref:o,className:a()(e,r),...s})));m.displayName="CardImgOverlay";let x=o.forwardRef(({className:e,bsPrefix:r,as:t="a",...s},o)=>(r=(0,i.oU)(r,"card-link"),(0,n.jsx)(t,{ref:o,className:a()(e,r),...s})));x.displayName="CardLink";var g=t(7783);let h=(0,g.A)("h6"),f=o.forwardRef(({className:e,bsPrefix:r,as:t=h,...s},o)=>(r=(0,i.oU)(r,"card-subtitle"),(0,n.jsx)(t,{ref:o,className:a()(e,r),...s})));f.displayName="CardSubtitle";let q=o.forwardRef(({className:e,bsPrefix:r,as:t="p",...s},o)=>(r=(0,i.oU)(r,"card-text"),(0,n.jsx)(t,{ref:o,className:a()(e,r),...s})));q.displayName="CardText";let A=(0,g.A)("h5"),y=o.forwardRef(({className:e,bsPrefix:r,as:t=A,...s},o)=>(r=(0,i.oU)(r,"card-title"),(0,n.jsx)(t,{ref:o,className:a()(e,r),...s})));y.displayName="CardTitle";let j=o.forwardRef(({bsPrefix:e,className:r,bg:t,text:s,border:o,body:p=!1,children:u,as:d="div",...c},m)=>{let x=(0,i.oU)(e,"card");return(0,n.jsx)(d,{ref:m,...c,className:a()(r,x,t&&`bg-${t}`,s&&`text-${s}`,o&&`border-${o}`),children:p?(0,n.jsx)(l,{children:u}):u})});j.displayName="Card";let v=Object.assign(j,{Img:c,Title:y,Subtitle:f,Body:l,Link:x,Text:q,Header:d,Footer:p,ImgOverlay:m})},18622:e=>{e.exports=require("yup")},19121:e=>{e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},22326:e=>{e.exports=require("react-dom")},22541:e=>{e.exports=require("redux-persist/lib/storage")},25303:e=>{e.exports=require("@restart/ui/NavItem")},26324:e=>{e.exports=require("warning")},27825:e=>{e.exports=require("lodash")},27910:e=>{e.exports=require("stream")},28217:e=>{e.exports=require("@restart/ui/DropdownItem")},29021:e=>{e.exports=require("fs")},29294:e=>{e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},29628:e=>{e.exports=require("dom-helpers/addClass")},29825:e=>{e.exports=require("prop-types")},29841:e=>{e.exports=require("dom-helpers/hasClass")},30362:e=>{e.exports=require("@restart/ui/DropdownToggle")},32448:(e,r,t)=>{t.a(e,async(e,s)=>{try{t.r(r),t.d(r,{default:()=>x});var a=t(8732),o=t(54131),i=t(82053),n=t(74716),l=t.n(n),p=t(82015),u=t(83551),d=t(49481),c=t(88751),m=e([o]);o=(m.then?(await m)():m)[0];let x=e=>{let{t:r}=(0,c.useTranslation)("common"),[t,s]=(0,p.useState)(40);(0,p.useEffect)(()=>{document.getElementById("timeline-container")?.scroll(t,5e3)},[t]);let n={1:"/images/home/<USER>",2:"/images/home/<USER>",3:"/images/home/<USER>",4:"/images/home/<USER>",5:"/images/home/<USER>",6:"/images/home/<USER>",7:"/images/home/<USER>"};return(0,a.jsx)(a.Fragment,{children:(0,a.jsx)(u.A,{children:(0,a.jsx)(d.A,{className:"operatinTimeline",xs:12,children:(0,a.jsxs)("div",{className:"progress_main_sec",style:{marginTop:"90px"},children:[e.operation&&e.operation.timeline.length>2&&(0,a.jsxs)("div",{children:[(0,a.jsx)("div",{className:"prev",onClick:()=>{let e=t-50;s(e<0?0:e)},style:{cursor:"pointer"},children:(0,a.jsx)("span",{children:(0,a.jsx)(i.FontAwesomeIcon,{icon:o.faAngleLeft})})}),(0,a.jsx)("div",{className:"next",onClick:()=>{s(t+50)},style:{cursor:"pointer"},children:(0,a.jsx)("span",{children:(0,a.jsx)(i.FontAwesomeIcon,{icon:o.faAngleRight})})})]}),(0,a.jsx)("div",{className:"progressbar-container",id:"timeline-container",children:(0,a.jsx)("ul",{className:"progressbar",children:e.operation&&e.operation.timeline&&e.operation.timeline.map((t,s)=>(0,a.jsxs)("li",{style:{zIndex:e.operation.timeline.length-s},children:[(0,a.jsx)("div",{className:"timelineIcon",children:(0,a.jsx)("img",{src:n[t.iconclass],width:"80px",height:"80px"})}),t.timetitle?(0,a.jsx)("p",{className:"step-label",children:t.timetitle}):(0,a.jsx)("p",{className:"step-label",children:r("NoTitle")}),t.date?(0,a.jsx)("p",{className:"step-text",children:l()(t.date).format("MM-D-YYYY")}):(0,a.jsx)("p",{className:"step-text",children:r("NoDate")})]},s))})})]})})})})};s()}catch(e){s(e)}})},33873:e=>{e.exports=require("path")},36653:e=>{e.exports=require("nprogress")},36955:e=>{e.exports=require("@restart/hooks/useBreakpoint")},37766:e=>{e.exports=require("@restart/hooks/useMergedRefs")},38609:e=>{e.exports=require("react-data-table-component")},39756:e=>{e.exports=import("redux")},40051:e=>{e.exports=require("dom-helpers/removeClass")},40361:e=>{e.exports=require("next/dist/compiled/next-server/pages.runtime.prod.js")},42893:e=>{e.exports=import("react-hot-toast")},43294:e=>{e.exports=require("formik")},50009:e=>{e.exports=require("@restart/ui/DropdownMenu")},54131:e=>{e.exports=import("@fortawesome/free-solid-svg-icons")},57664:e=>{e.exports=require("dom-helpers/scrollbarSize")},58928:e=>{e.exports=require("dom-helpers/canUseDOM")},59717:e=>{e.exports=require("@restart/ui/DropdownContext")},60560:e=>{e.exports=require("dom-helpers/contains")},63033:e=>{e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},65447:e=>{e.exports=require("@restart/ui/Button")},67364:e=>{e.exports=require("@restart/hooks/useCallbackRef")},68455:e=>{e.exports=import("redux-saga")},69722:e=>{e.exports=require("es6-promise")},74075:e=>{e.exports=require("zlib")},74716:e=>{e.exports=require("moment")},74987:e=>{e.exports=require("@restart/ui/ModalManager")},76684:(e,r,t)=>{t.a(e,async(e,s)=>{try{t.r(r),t.d(r,{default:()=>x});var a=t(8732),o=t(82015),i=t(7082),n=t(63487),l=t(77136),p=t(88751),u=t(60072),d=t(95645),c=t(32448),m=e([n,l,u,d,c]);[n,l,u,d,c]=m.then?(await m)():m;let x=e=>{let{t:r}=(0,p.useTranslation)("common"),[t,s]=(0,o.useState)({title:"",timeline:[],description:"",hazard_type:{title:""},hazard:[],syndrome:{title:""},created_at:"",updated_at:"",country:{title:""},status:{title:""},start_date:"",end_date:"",partners:[],images:[],images_src:[],document:[],doc_src:[]}),[m,x]=(0,o.useState)(!1),[g,h]=(0,o.useState)(!1),[f,q]=(0,o.useState)([]),[A,y]=(0,o.useState)([]),[j,v]=(0,o.useState)(!1),P={sort:{doc_created_at:"asc"},Doctable:!0},_={sort:{doc_created_at:"asc"},DocUpdatetable:!0},S=async()=>{let r=[];h(!0);let t=await n.A.get(`/operation/${e.routes[1]}`,P);t&&Array.isArray(t)&&t.length>=1&&t[0].document&&t[0].document.length>=1&&(t.forEach(e=>{e.document&&e.document.length>0&&e.document.map((t,s)=>{t.description=e.document[s].docsrc,r.push(t)})}),q(r)),h(!1)},N=async()=>{let r=[];h(!0);let t=await n.A.get(`/operation/${e.routes[1]}`,_);t&&Array.isArray(t)&&t.length>=1&&t[0].document&&t[0].document.length>=1&&(t.forEach(e=>{e.document&&e.document.length>0&&e.document.map((t,s)=>{t.description=e.document[s].docsrc,r.push(t)})}),y(r)),h(!1)};(0,o.useEffect)(()=>{b()},[]);let b=async()=>{let r=await n.A.post("/users/getLoggedUser",{});r&&r.roles&&r.roles.length&&e.routes&&e.routes[1]&&((async t=>{x(!0);let a=await n.A.get(`/operation/${e.routes[1]}`,t);a&&(s(a),function(e,r){v(!1),r&&r.roles&&(r.roles.includes("SUPER_ADMIN")||r.roles.filter(e=>"EMT_NATIONAL_FOCALPOINT"==e).length>0&&e.user._id==r._id||r.roles.filter(e=>"EMT"==e).length>0&&e.user._id==r._id||r.roles.filter(e=>"INIG_STAKEHOLDER"==e).length>0&&e.user._id==r._id||r.roles.filter(e=>"GENERAL_USER"==e).length>0&&e.user._id==r._id?v(!0):r.roles.filter(e=>"PLATFORM_ADMIN"==e).length>0&&e.user._id==r._id&&v(!0))}(a,r)),x(!1)})({}),S(),N())},w={operationData:t,routeData:e,editAccess:j,documentAccoirdianProps:{loading:g,sortProps:e=>{P.sort={[e.columnSelector]:e.sortDirection},S()},Document:f,updateDocument:A,sortUpdateProps:e=>{_.sort={[e.columnSelector]:e.sortDirection},N()}}};return(0,a.jsx)(a.Fragment,{children:t?.title?(0,a.jsxs)(i.A,{className:"operationDetail",fluid:!0,children:[(0,a.jsx)(l.A,{routes:e.routes}),(0,a.jsx)(u.default,{...w}),(0,a.jsx)(c.default,{operation:t}),(0,a.jsx)(d.default,{...w})]}):(0,a.jsx)(a.Fragment,{})})};s()}catch(e){s(e)}})},78097:e=>{e.exports=require("next-redux-saga")},78634:e=>{e.exports=require("@restart/ui/Anchor")},80237:(e,r)=>{Object.defineProperty(r,"A",{enumerable:!0,get:function(){return t}});var t=function(e){return e.PAGES="PAGES",e.PAGES_API="PAGES_API",e.APP_PAGE="APP_PAGE",e.APP_ROUTE="APP_ROUTE",e.IMAGE="IMAGE",e}({})},81149:e=>{e.exports=require("react-responsive-carousel")},81366:e=>{e.exports=require("redux-auth-wrapper/connectedAuthWrapper")},81413:(e,r)=>{Object.defineProperty(r,"M",{enumerable:!0,get:function(){return function e(r,t){return t in r?r[t]:"then"in r&&"function"==typeof r.then?r.then(r=>e(r,t)):"function"==typeof r&&"default"===t?r:void 0}}})},81521:e=>{e.exports=require("dom-helpers/querySelectorAll")},81895:e=>{e.exports=require("@restart/hooks/useEventCallback")},82015:e=>{e.exports=require("react")},82053:e=>{e.exports=require("@fortawesome/react-fontawesome")},86842:e=>{e.exports=require("@restart/ui/SelectableContext")},86843:e=>{e.exports=require("moment/locale/de")},87571:e=>{e.exports=require("dom-helpers/transitionEnd")},88751:e=>{e.exports=require("next-i18next")},93787:e=>{e.exports=require("redux-persist")},94696:e=>{e.exports=require("@react-google-maps/api")},94947:e=>{e.exports=require("@restart/hooks/useTimeout")},96196:e=>{e.exports=require("next-redux-wrapper")},97265:(e,r,t)=>{t.a(e,async(e,s)=>{try{t.r(r),t.d(r,{config:()=>h,default:()=>c,getServerSideProps:()=>g,getStaticPaths:()=>x,getStaticProps:()=>m,reportWebVitals:()=>f,routeModule:()=>P,unstable_getServerProps:()=>j,unstable_getServerSideProps:()=>v,unstable_getStaticParams:()=>y,unstable_getStaticPaths:()=>A,unstable_getStaticProps:()=>q});var a=t(63885),o=t(80237),i=t(81413),n=t(9616),l=t.n(n),p=t(72386),u=t(76684),d=e([p,u]);[p,u]=d.then?(await d)():d;let c=(0,i.M)(u,"default"),m=(0,i.M)(u,"getStaticProps"),x=(0,i.M)(u,"getStaticPaths"),g=(0,i.M)(u,"getServerSideProps"),h=(0,i.M)(u,"config"),f=(0,i.M)(u,"reportWebVitals"),q=(0,i.M)(u,"unstable_getStaticProps"),A=(0,i.M)(u,"unstable_getStaticPaths"),y=(0,i.M)(u,"unstable_getStaticParams"),j=(0,i.M)(u,"unstable_getServerProps"),v=(0,i.M)(u,"unstable_getServerSideProps"),P=new a.PagesRouteModule({definition:{kind:o.A.PAGES,page:"/operation/OperationShow",pathname:"/operation/OperationShow",bundlePath:"",filename:""},components:{App:p.default,Document:l()},userland:u});s()}catch(e){s(e)}})},98320:e=>{e.exports=require("@restart/hooks/useIsomorphicEffect")},99460:e=>{e.exports=require("dom-helpers/css")}};var r=require("../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[6089,9216,9616,2386,2491,7136,5645,72],()=>t(97265));module.exports=s})();