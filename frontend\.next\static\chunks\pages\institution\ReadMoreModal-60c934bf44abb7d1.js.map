{"version": 3, "file": "static/chunks/pages/institution/ReadMoreModal-60c934bf44abb7d1.js", "mappings": "gFACA,4CACA,6BACA,WACA,OAAe,EAAQ,KAAkD,CACzE,EACA,SAFsB,gHC4CtB,MAxCsB,OAAC,aAAEA,CAAW,CAA4B,GACxD,CAACC,EAAOC,EAAU,CAAGC,CAAAA,EAAAA,EAAAA,KAuCDC,EAAC,CAvCAD,CAAQA,EAAC,GAC9B,GAAEE,CAAC,CAAE,CAAGC,CAAAA,EAAAA,EAAAA,EAAAA,CAAcA,CAAC,UAE7B,MACE,iCACE,WAACC,EAAAA,CAAKA,CAAAA,CACJC,KAAMP,EACNQ,OAAQ,IAAMP,EAAU,CAACD,GACzBS,SAAS,SACTC,UAAU,YAEV,UAACJ,EAAAA,CAAKA,CAACK,MAAM,EAACC,WAAW,aACvB,UAACN,EAAAA,CAAKA,CAACO,KAAK,WAAET,EAAE,mBAElB,UAACE,EAAAA,CAAKA,CAACQ,IAAI,WACT,UAACC,MAAAA,CAAIC,UAAU,cAAcC,wBAAyB,CAAEC,YAAuBC,GAAfpB,EAA2B,GAAKA,CAAY,SAI/GA,GAAeA,EAAYqB,MAAM,CAAG,IACnC,UAACL,MAAAA,CAAIE,wBAAyB,CAAEC,YAAuBC,GAAfpB,EAA2B,GAAKA,CAAY,IACnE,IAAfA,EACF,WAACgB,MAAAA,WACC,UAACA,MAAAA,CAAIC,UAAU,UAAWC,wBAAyB,CAAEC,YAAuBC,GAAfpB,EAA2B,GAAKA,EAAYsB,SAAS,CAAC,EAAG,KAAO,GAA6CC,MAAAA,CAA1CvB,EAAYwB,QAAQ,CAAC,MAAQ,MAAQ,IAAa,OAARD,GAAU,IACpL,UAACP,MAAAA,CAAIC,UAAU,gBACb,UAACQ,EAAAA,CAAMA,CAAAA,CACLC,QAAS,IAAMxB,EAAU,CAACD,GAC1BgB,UAAU,mBACVU,QAAQ,yBAERtB,EAAE,mBAIN,KAGV", "sources": ["webpack://_N_E/?259d", "webpack://_N_E/./pages/institution/ReadMoreModal.tsx"], "sourcesContent": ["\n    (window.__NEXT_P = window.__NEXT_P || []).push([\n      \"/institution/ReadMoreModal\",\n      function () {\n        return require(\"private-next-pages/institution/ReadMoreModal.tsx\");\n      }\n    ]);\n    if(module.hot) {\n      module.hot.dispose(function () {\n        window.__NEXT_P.push([\"/institution/ReadMoreModal\"])\n      });\n    }\n  ", "//Import Library\r\nimport { useState } from \"react\";\r\nimport { Modal, Button } from \"react-bootstrap\";\r\n\r\n//Import services/components\r\nimport { useTranslation } from 'next-i18next';\r\n\r\n\r\nconst ReadMoreModal = ({ description } : { description: string }) => {\r\n  const [modal, showModal] = useState(false);\r\n  const { t } = useTranslation('common');\r\n  const _string = \"\"\r\n  return (\r\n    <>\r\n      <Modal\r\n        show={modal}\r\n        onHide={() => showModal(!modal)}\r\n        backdrop=\"static\"\r\n        keyboard={false}\r\n      >\r\n        <Modal.Header closeButton>\r\n          <Modal.Title>{t(\"Description\")}</Modal.Title>\r\n        </Modal.Header>\r\n        <Modal.Body>\r\n          <div className=\"_readmore_d\" dangerouslySetInnerHTML={{ __html: description == undefined ? \"\" : description }}></div>\r\n        </Modal.Body>\r\n      </Modal>\r\n\r\n      {description && description.length < 130 ? (\r\n        <div dangerouslySetInnerHTML={{ __html: description == undefined ? \"\" : description }}></div>\r\n      ) : description != '' ? (\r\n        <div>\r\n          <div className=\"_tabelw\"  dangerouslySetInnerHTML={{ __html: description == undefined ? \"\" : description.substring(0, 130) + `${description.includes(\"<p\") ? '...' : ''}${_string}` }}></div>\r\n          <div className=\"pt-3\">\r\n            <Button\r\n              onClick={() => showModal(!modal)}\r\n              className=\"readMoreBtn mb-3\"\r\n              variant=\"outline-light\"\r\n            >\r\n             {t(\"ReadMore\")} \r\n            </Button>\r\n          </div>\r\n        </div>\r\n      ) : \"\"}\r\n    </>\r\n  );\r\n};\r\n\r\nexport default ReadMoreModal;\r\n"], "names": ["description", "modal", "showModal", "useState", "ReadMoreModal", "t", "useTranslation", "Modal", "show", "onHide", "backdrop", "keyboard", "Header", "closeButton", "Title", "Body", "div", "className", "dangerouslySetInnerHTML", "__html", "undefined", "length", "substring", "_string", "includes", "<PERSON><PERSON>", "onClick", "variant"], "sourceRoot": "", "ignoreList": []}