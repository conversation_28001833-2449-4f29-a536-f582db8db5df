"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[3804],{8019:(e,t,a)=>{a.r(t),a.d(t,{default:()=>c});var s=a(37876),n=a(14232),i=a(53718),r=a(87453),l=a(31753);let c=()=>{let{i18n:e}=(0,l.Bd)("common");e.language&&e.language;let[t,a]=(0,n.useState)({title:"",description:"",pageCategory:"",images:"",isEnabled:!0}),[c,o]=(0,n.useState)(!1),d={query:{pageCategory:""},sort:{title:"asc"},limit:"~"},u=async()=>{let e=await h();if(o(!0),e.length>0){d.query.pageCategory=e;let t=await i.A.get("/landingPage",d);if(Array.isArray(t.data)&&t.data.length>0){let e=t.data[t.data.length-1];e.images=t&&e.images.length>0&&!0===e.isEnabled?e.images.map((e,t)=>String("".concat("http://localhost:3001/api/v1","/image/show/").concat(e._id))):"/images/logo.jpg",e.description=t&&e.description.length>0&&!0===e.isEnabled?e.description:"The Robert Koch Institut is taking over the coordination of the “WHO AMR Surveillance and Quality Assessment Collaborating Centres Network” this autumn 2019. The network supports the World Health Organization (WHO) to reduce drug-resistant infections globally. It focuses on further developing the global antimicrobial resistance (AMR) surveillance system (GLASS), and promoting exchange and peer support between countries.",a(e),h(),o(!1)}}},h=async()=>{let e=await i.A.get("/pagecategory",{query:{title:"AboutUs"}});return!!e&&!!e.data&&e.data.length>0&&e.data[0]._id};(0,n.useEffect)(()=>{u()},[]);let p=t.description.replace(/\&nbsp;/g," ");return(0,s.jsx)("div",{className:"aboutUs",children:!0===c?(0,s.jsx)(r.A,{}):(0,s.jsxs)("div",{children:[(0,s.jsx)("img",{className:"logoImg",src:t.images,alt:""}),(0,s.jsx)("div",{dangerouslySetInnerHTML:{__html:p}})," "]})})}},10324:(e,t,a)=>{a.r(t),a.d(t,{default:()=>l});var s=a(37876),n=a(37784),i=a(48230),r=a.n(i);function l(e){let{item:t}=e,a=t.images.length-1,i="parent_".concat(t.type);return(0,s.jsxs)(n.A,{className:"p-0",xs:12,children:[(0,s.jsx)(r(),{href:"/".concat(t.type,"/[...routes]"),as:"/".concat(t.type,"/show/").concat(t[i],"/update/").concat(t._id),children:t.images&&t.images[a]?(0,s.jsx)("img",{src:"".concat("http://localhost:3001/api/v1","/image/show/").concat(t.images[a]._id),alt:"announcement",className:"announceImg"}):(0,s.jsx)("i",{className:"fa fa-bullhorn announceImg"})}),(0,s.jsxs)("div",{className:"announceDesc",children:[(0,s.jsx)(r(),{href:"/".concat(t.type,"/[...routes]"),as:"/".concat(t.type,"/show/").concat(t[i],"/update/").concat(t._id),children:t&&t.title?t.title:""}),(0,s.jsx)("p",{children:t&&t.description?(e=>{let t=document.createElement("div");t.innerHTML=e;let a=t.textContent||t.innerText||"";return a.length>260?"".concat(a.substring(0,257),"..."):a})(t.description):null})]})]})}},22837:(e,t,a)=>{a.r(t),a.d(t,{default:()=>x});var s=a(37876),n=a(14232),i=a(82851),r=a.n(i),l=a(48230),c=a.n(l),o=a(80942),d=a(98661),u=a(87453),h=a(53718);function p(e){let{list:t}=e;return t.length>0?(0,s.jsx)(o.A,{children:t.map((e,t)=>(0,s.jsx)(o.A.Item,{children:(0,s.jsx)(c(),{href:"/project/[...routes]",as:"/project/show/".concat(e._id),children:e.title})},t))}):null}function g(e){let{project:t}=e;return(0,s.jsx)(c(),{href:"/project/[...routes]",as:"/project/show/".concat(t.id),className:"active-op-project",children:(0,s.jsx)("span",{className:"project-title link",children:t.body})})}let x=function(e){let{t,fetchOngoingProjects:a}=e,i=t("OngoingProjects"),[l,c]=(0,n.useState)({body:"",id:"",list:[]}),[o,x]=(0,n.useState)(!0),j=()=>{c({body:t("NoProjectavailable"),id:"",list:[]})},m=async()=>{let e={query:{status:[]},sort:{created_at:"desc"},limit:10,select:"-website -description -funded_by -status -start_date -end_date -region -area_of_work -institution_invites -vspace -vspace_visibility -user -created_at -updated_at -partner_institutions.partner_region -partner_institutions.partner_institution -partner_institutions.world_region"},t=await f();if(t){e.query.status=t;try{x(!0);let t=await h.A.get("/project",e);x(!1),Array.isArray(t.data)&&t.data.length>0?(c({body:t.data[0].title,id:t.data[0]._id,list:t.data}),a(t.data)):j()}catch(e){j()}}else j()},f=async()=>{let e=await h.A.get("/projectStatus");if(e&&e.data&&e.data.length>0){let t=[];return r().forEach(e.data,function(e){"Ongoing"===e.title&&t.push(e._id)}),t}return!1};(0,n.useEffect)(()=>{m()},[]);let y={heading:i,body:(0,s.jsx)(p,{list:l.list})};return(0,s.jsx)(d.A,{dialogClassName:"ongoing-project-list",list:y,header:i,body:o?(0,s.jsx)(u.A,{}):(0,s.jsx)(g,{project:l})})}},51337:(e,t,a)=>{a.d(t,{A:()=>b});var s=a(37876),n=a(30523),i=a(10841),r=a.n(i);a(90932);var l=a(89099),c=a.n(l),o=a(49589),d=a(56970),u=a(37784),h=a(21772),p=a(11041),g=a(82851),x=a.n(g),j=a(31753);let m=(0,n.ye)(r());function f(e){let{t,i18n:a}=(0,j.Bd)("common"),i=a.language,{eventsList:r,style:l,minicalendar:o,views:d}=e,u={};return e.showEventCounts&&(u={eventWrapper:_,month:{dateHeader:e=>(0,s.jsx)(v,{eventsList:r,...e})}}),o&&(u.toolbar=y),(0,s.jsx)(n.Vv,{culture:i,localizer:m,events:r,views:d,startAccessor:"start_date",endAccessor:"end_date",style:l,components:u,messages:{today:t("today"),previous:t("back"),next:t("Next"),month:t("Month"),week:t("Week"),day:t("Day")},onSelectEvent:e=>{let t=Object.keys(e).filter(e=>e.includes("parent")).toString(),a=t.split("_")[1];c().push("/".concat(a,"/show/").concat(e[t],"/update/").concat(e._id))}})}function y(e){return(0,s.jsx)(o.A,{className:"mb-1",children:(0,s.jsxs)(d.A,{children:[(0,s.jsx)(u.A,{className:"p-0",md:1,children:(0,s.jsx)("i",{style:{cursor:"pointer"},onClick:()=>e.onNavigate("PREV"),className:"fas fa-chevron-left"})}),(0,s.jsx)(u.A,{className:"text-center",md:10,children:(0,s.jsx)("span",{className:"rbc-toolbar-label",children:e.label})}),(0,s.jsx)(u.A,{className:"p-0 text-end",md:1,children:(0,s.jsx)("i",{style:{cursor:"pointer"},onClick:()=>e.onNavigate("NEXT"),className:"fas fa-chevron-right"})})]})})}f.defaultProps={minicalendar:!1,views:["month"]};let A=(e,t)=>{let a=0;return x().forEach(e,e=>{let s=r()(e.start_date).set({hour:0,minute:0,second:0,millisecond:0}),n=r()(e.end_date).set({hour:0,minute:0,second:0,millisecond:0});r()(t).isBetween(s,n,null,"[]")&&(a+=1)}),a},v=e=>{let{date:t,label:a,eventsList:n}=e,i=A(n,t),l=r()(t).isBefore(new Date,"day");return(0,s.jsxs)("div",{className:"rbc-date-cell",onClick:()=>c().push("/events-calendar"),children:[(0,s.jsx)("a",{href:"#",children:a}),i>0&&(0,s.jsxs)("span",{className:"d-flex justify-content-start align-items-center fa-stack",children:[(0,s.jsx)(h.g,{icon:p.yy,color:l?"grey":"#04A6FB",size:"lg"}),(0,s.jsx)("span",{className:"eventCount",children:i})]})]})},_=e=>(0,s.jsx)("div",{onSelect:e.onSelect}),b=f},71745:(e,t,a)=>{a.r(t),a.d(t,{default:()=>g});var s=a(37876),n=a(14232),i=a(56970),r=a(49589),l=a(82851),c=a.n(l),o=a(50650),d=a(37784),u=a(10324),h=a(53718);function p(e){let{announcements:t}=e;return(0,s.jsx)("div",{children:t.map((e,t)=>(0,s.jsx)(i.A,{className:"announcementItem",children:(0,s.jsx)(u.default,{item:e})},t))})}let g=function(e){let{t}=e,[a,l]=(0,n.useState)([]),[u,g]=(0,n.useState)(0),[x]=(0,n.useState)(3),j=()=>{l([])},m={query:{show_as_announcement:!0},sort:{created_at:"desc"},limit:9,select:"-created_at -update_type -contact_details -document -end_date -link -media -reply -start_date -updated_at -user"},f=async function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:m,t=await h.A.get("/updates",e);t&&t.data&&t.data.length>0?l(c().chunk(t.data,3)):j()};(0,n.useEffect)(()=>{f()},[]);let y=e=>{let t=u,[s,n]=[0,x-1];"next"===e&&t<n?t++:"prev"===e&&t>0&&t--,a.length-t==1&&(t=a.length-1),a.length-t==0&&(t=1),g(t)};return(0,s.jsx)("div",{className:"announcements",children:a&&a.length>0?(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)(r.A,{fluid:!0,children:(0,s.jsxs)(i.A,{children:[(0,s.jsx)(d.A,{xs:10,className:"p-0",children:(0,s.jsx)("h4",{children:t("announcements")})}),a&&a.length>1?(0,s.jsx)(d.A,{xs:2,className:"text-end carousel-control p-0",children:(0,s.jsxs)("div",{className:"carousel-navigation",children:[(0,s.jsx)("a",{className:"left carousel-control",onClick:()=>y("prev"),children:(0,s.jsx)("i",{className:"fa fa-chevron-left"})}),(0,s.jsx)("a",{className:"right carousel-control",onClick:()=>y("next"),children:(0,s.jsx)("i",{className:"fa fa-chevron-right"})})]})}):null]})}),(0,s.jsx)(r.A,{fluid:!0,children:(0,s.jsx)(i.A,{children:(0,s.jsx)(d.A,{xs:12,className:"p-0",children:(0,s.jsx)(o.A,{indicators:!1,controls:!1,interval:null,activeIndex:u,children:a.map((e,t)=>(0,s.jsx)(o.A.Item,{children:(0,s.jsx)(p,{announcements:e})},t))})})})})]}):(0,s.jsx)(r.A,{fluid:!0,children:(0,s.jsxs)(i.A,{children:[(0,s.jsx)(d.A,{xs:10,className:"p-0",children:(0,s.jsx)("h4",{children:t("announcements")})}),(0,s.jsxs)(d.A,{xs:12,className:"p-0",children:[(0,s.jsx)("div",{className:"border p-3",children:t("NoAnnouncementsavailabletodisplay")}),(0,s.jsx)("br",{})]})]})})})}},83804:(e,t,a)=>{a.r(t),a.d(t,{__N_SSG:()=>j,default:()=>m});var s=a(37876),n=a(49589),i=a(56970),r=a(37784),l=a(31777),c=a(14232),o=a(8019),d=a(89877),u=a(22837),h=a(71745),p=a(1147),g=a(88861),x=a(53718),j=!0;let m=(0,l.Ng)(e=>e)(function(e){let{t,user:a}=e,[l,j]=(0,c.useState)(""),[m,f]=(0,c.useState)([]),[y,A]=(0,c.useState)([]),[v,_]=(0,c.useState)("Approved"),[b,N]=(0,c.useState)(""),w=async()=>{let e=await x.A.post("/users/getLoggedUser",{});(null==e?void 0:e.is_focal_point)===!1&&(null==e?void 0:e.is_vspace)===!1?_("Approved"):(null==e?void 0:e.status)?N(function(e){switch(e){case"Request Pending":return"WaitingForFocalPoint";case"Approved":return"SucessForFocalPoint"}}(e.status)):(null==e?void 0:e.vspace_status)?N(function(e){switch(e){case"Request Pending":return"WaitingForVspace";case"Approved":return"SucessForVspace"}}(e.vspace_status)):N("RejectedForFocalPoint")};return(0,c.useEffect)(()=>{a.is_focal_point&&_("Approved"==a.status?"Approved":"Pending"),a.is_vspace&&_("Approved"==a.vspace_status?"Approved":"Pending"),j(a&&a.username?a.username:""),w()},[a]),(0,s.jsx)(s.Fragment,{children:"Approved"==v?(0,s.jsxs)(n.A,{fluid:!0,className:"p-0 dashboardScreen",children:[(0,s.jsxs)("h2",{children:[t("hello")," ",l]}),(0,s.jsxs)(n.A,{fluid:!0,children:[(0,s.jsxs)(i.A,{children:[(0,s.jsxs)(r.A,{className:"ps-lg-0 dashboardLeft",lg:"8",children:[(0,s.jsx)(i.A,{children:(0,s.jsx)(r.A,{xs:"12",children:(0,s.jsx)(o.default,{})})}),(0,s.jsx)(i.A,{children:(0,s.jsx)(r.A,{xs:12,children:(0,s.jsxs)(i.A,{children:[(0,s.jsx)(r.A,{md:"6",className:"ongoingBlock",children:(0,s.jsx)(d.default,{t:t,fetchOngoingOperations:f})}),(0,s.jsx)(r.A,{md:"6",className:"ongoingBlock",children:(0,s.jsx)(u.default,{t:t,fetchOngoingProjects:A})})]})})})]}),(0,s.jsx)(r.A,{className:"pe-lg-0 dashboard-calendar",lg:"4",children:(0,s.jsx)(g.default,{})})]}),(0,s.jsx)(i.A,{children:(0,s.jsx)(r.A,{className:"p-lg-0",xs:"12",children:(0,s.jsx)(h.default,{t:t})})}),(0,s.jsx)(i.A,{children:(0,s.jsx)(r.A,{className:"p-lg-0",xs:12,children:(0,s.jsx)(p.default,{t:t,ongoingProjects:y,ongoingOperations:m})})})]})]}):(0,s.jsxs)("div",{className:"Focalpoint",children:[(0,s.jsx)("h2",{children:"Welcome to Robert Koch Institut !"}),(0,s.jsx)("div",{children:(0,s.jsx)("h5",{className:"text-muted",children:t(b)})})]})})})},87453:(e,t,a)=>{a.d(t,{A:()=>i});var s=a(37876),n=a(67140);function i(){return(0,s.jsxs)(n.Ay,{viewBox:"0 0 380 70",height:50,width:317,speed:2,title:"Loading",foregroundColor:"#f7f7f7",backgroundColor:"#ecebeb",uniqueKey:"operation",children:[(0,s.jsx)("rect",{x:"10",y:"0",rx:"4",ry:"4",width:"320",height:"25"}),(0,s.jsx)("rect",{x:"40",y:"40",rx:"3",ry:"3",width:"250",height:"20"})]})}},88861:(e,t,a)=>{a.r(t),a.d(t,{default:()=>o});var s=a(37876),n=a(14232),i=a(82851),r=a.n(i),l=a(51337),c=a(53718);let o=function(e){let[t,a]=(0,n.useState)([]),i=async()=>{let e=await c.A.get("/updateType",{query:{title:"Calendar Event"}});e&&e.data&&e.data.length>0&&o(e.data[0]._id)},o=async e=>{let t=await c.A.get("/updates",{query:{update_type:e},sort:{created_at:"desc"},limit:20,select:"-contact_details -description -document -images -link -media -parent_operation -reply -show_as_announcement -type -user"});t&&t.data&&(r().forEach(t.data,function(e,a){t.data[a].allDay=!1}),a(t.data))};return(0,n.useEffect)(()=>{i()},[]),(0,s.jsx)(l.A,{eventsList:t,minicalendar:!0,showEventCounts:!0})}},89877:(e,t,a)=>{a.r(t),a.d(t,{default:()=>x});var s=a(37876),n=a(14232),i=a(82851),r=a.n(i),l=a(48230),c=a.n(l),o=a(80942),d=a(98661),u=a(53718),h=a(87453);function p(e){let{list:t}=e;return t.length>0?(0,s.jsx)(o.A,{children:t.map((e,t)=>(0,s.jsx)(o.A.Item,{children:(0,s.jsx)(c(),{href:"/operation/[...routes]",as:"/operation/show/".concat(e._id),children:e.title})},t))}):null}function g(e){let{operation:t}=e;return(0,s.jsx)(c(),{href:"/operation/[...routes]",as:"/operation/show/".concat(t.id),className:"active-op-project",children:(0,s.jsx)("span",{className:"project-title link",children:t.body})})}let x=function(e){let{t,fetchOngoingOperations:a}=e,i=t("OngoingOperations"),[l,c]=(0,n.useState)({body:"",id:"",list:[]}),[o,x]=(0,n.useState)(!0),j=()=>{c({body:t("Nooperationavailable"),id:"",list:[]})},m=async()=>{let e={query:{status:[]},sort:{created_at:"desc"},limit:10,select:"-description -status -start_date -end_date -timeline -world_region -region -hazard_type -hazard -syndrome -partners -vspace -vspace_visibility -images -user -created_at -updated_at"},t=await f();if(t){e.query.status=t;try{x(!0);let t=await u.A.get("/operation",e);x(!1),Array.isArray(t.data)&&t.data.length>0?(c({body:t.data[0].title,id:t.data[0]._id,list:t.data}),a(t.data)):j()}catch(e){j()}}else j()},f=async()=>{let e=await u.A.get("/operation_status");if(e&&e.data&&e.data.length>0){let t=[];return r().forEach(e.data,function(e){"Ongoing"==e.title&&t.push(e._id)}),t}return!1};(0,n.useEffect)(()=>{m()},[]);let y={heading:i,body:(0,s.jsx)(p,{list:l.list})};return(0,s.jsx)(d.A,{dialogClassName:"ongoing-project-list",list:y,header:i,body:o?(0,s.jsx)(h.A,{}):(0,s.jsx)(g,{operation:l})})}},98661:(e,t,a)=>{a.d(t,{A:()=>o});var s=a(37876),n=a(14232),i=a(29335),r=a(31195);function l(e){let{list:t,dialogClassName:a}=e;return(0,s.jsxs)(r.A,{...e,dialogClassName:a,"aria-labelledby":"contained-modal-title-vcenter",centered:!0,children:[(0,s.jsx)(r.A.Header,{closeButton:!0,children:(0,s.jsx)(r.A.Title,{id:"contained-modal-title-vcenter",children:t.heading})}),(0,s.jsx)(r.A.Body,{children:t.body})]})}function c(e){let{list:t}=e,[a,r]=n.useState(!1);return t&&t.body?(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)("button",{type:"button",onClick:()=>r(!0),style:{border:"none",background:"none",padding:0},children:(0,s.jsx)(i.A.Footer,{children:(0,s.jsx)("i",{className:"fas fa-chevron-down"})})}),e.list&&(0,s.jsx)(l,{list:e.list,show:a,onHide:()=>r(!1),dialogClassName:e.dialogClassName})]}):null}let o=function(e){let{header:t,body:a}=e;return(0,s.jsxs)(i.A,{className:"text-center infoCard",children:[(0,s.jsx)(i.A.Header,{children:t}),(0,s.jsx)(i.A.Body,{children:(0,s.jsx)(i.A.Text,{children:a})}),(0,s.jsx)(c,{...e})]})}}}]);
//# sourceMappingURL=3804-fa0e772526f54b9d.js.map