//Import Library
import { AxiosResponse } from 'axios';
import * as axiosLib from 'axios';
const axios = axiosLib.default;

//Import services/components
import authService from './authService';
import errorResponseHandler from './errorHandler';


class ApiService {
public axios: any;
  constructor() {
    axios.defaults.baseURL = process.env.API_SERVER;
    axios.defaults.withCredentials = true

  this.axios= axios;

  }

   getLanguage = () => {
    // Get language from localStorage or default to 'en'
    return (typeof window !== 'undefined' && window.localStorage.getItem('i18nextLng')) || 'en';
  };

  public post = async (url: string, data: Record<string, any> = {}, config: any = {}): Promise<any> => {
    const headers: any = await authService.getAuthHeader();
    data['language'] = data['language'] ? data['language'] : this.getLanguage();
    try {
      const response: AxiosResponse = await axios.post(url, data, { headers: { ...headers, ...config }, withCredentials: true });
      if ((response.status === 201 || response.status === 200) && response.data) {
        return response.data;
      }
    } catch (err) {
      return errorResponseHandler((err as any)?.response ? (err as any).response : {});
    }
  }

  public get = async (url: string, params?: object): Promise<any> => {


    const configs = {
      params: {
        ...params
      },
      // headers: headers
    };
    try {
      const response: AxiosResponse = await axios.get(url, {...configs, withCredentials: true});
      if (response.status === 200 && response.data) {
        return response.data;
      }
    } catch (err) {
      return errorResponseHandler((err as any)?.response ? (err as any).response : {});
    }
  }

  public update = async (url: string, data: Record<string, any> = {}): Promise<any> => {
    const headers = await authService.getAuthHeader();

    try {
      const response: AxiosResponse = await axios.put(url, data, { headers: headers, withCredentials: true });
      if (response.status === 200 && response.data) {
        return response.data;
      }
    } catch (err) {
      return errorResponseHandler((err as any)?.response ? (err as any).response : {});
    }
  }

  public patch = async (url: string, data: Record<string, any> = {}): Promise<any> => {
    const headers = await authService.getAuthHeader();
    (data as any)['language'] = (data as any)['language'] ? (data as any)['language'] : this.getLanguage();
    try {
      const response: AxiosResponse = await axios.patch(url, data, { headers: headers, withCredentials: true });
      if (response.status === 200 && response.data) {
        return response.data;
      }
    } catch (err) {
      return errorResponseHandler((err as any)?.response ? (err as any).response : {});
    }
  }

  public remove = async (url: string): Promise<any> => {
    const headers = await authService.getAuthHeader();

    try {
      const response: AxiosResponse = await axios.delete(url, { headers: headers, withCredentials: true });
      if (response.status === 200 && response.data) {
        return response.data;
      }
    } catch (err) {
      return errorResponseHandler((err as any)?.response ? (err as any).response : {});
    }
  }
}

export default new ApiService();
