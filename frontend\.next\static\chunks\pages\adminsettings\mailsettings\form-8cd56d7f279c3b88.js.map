{"version": 3, "file": "static/chunks/pages/adminsettings/mailsettings/form-8cd56d7f279c3b88.js", "mappings": "gFACA,4CACA,mCACA,WACA,OAAe,EAAQ,KAAwD,CAC/E,EACA,SAFsB,iGCEtB,IAAMA,EAAwBC,EAAAA,OAAb,GAA6B,CAAC,GAK5CC,IALyB,IAAoB,WAC9CC,CAAS,UACTC,CAAQ,CACRC,GAAIC,EAAY,KAAK,CACrB,GAAGC,EACJ,GAEC,OADAH,EAAWI,CAAAA,EAAAA,EAAAA,EAAAA,CAAkBA,CAACJ,EAAU,aACpBK,CAAAA,EAAAA,EAAAA,GAAAA,CAAIA,CAACH,EAAW,CAClCJ,IAAKA,EACLC,UAAWO,IAAWP,EAAWC,GACjC,GAAGG,CAAK,EAEZ,GACAP,EAJyBU,WAIL,CAAG,WCbvB,IAAMC,EAA0BV,EAAAA,SAAb,CAA6B,CAAC,GAK9CC,MAL2B,EAAoB,WAChDC,CAAS,UACTC,CAAQ,CACRC,GAAIC,EAAY,KAAK,CACrB,GAAGC,EACJ,GAEC,OADAH,EAAWI,CAAAA,EAAAA,EAAAA,EAAAA,CAAkBA,CAACJ,EAAU,eACpBK,CAAAA,EAAAA,EAAAA,GAAAA,CAAIA,CAACH,EAAW,CAClCJ,IAAKA,EACLC,UAAWO,IAAWP,EAAWC,GACjC,GAAGG,CACL,EACF,GACAI,EAJyBD,WAIH,CAAG,4BCXzB,IAAME,EAA0BX,EAAAA,SAAb,CAA6B,CAAC,GAM9CC,MAN2B,EAAoB,UAChDE,CAAQ,WACRD,CAAS,CAETE,CADA,EACIC,EAAY,KAAK,CACrB,GAAGC,EACJ,GACOM,EAASL,CAAAA,EAAAA,EAAAA,EAAAA,CAAkBA,CAACJ,EAAU,eACtCU,EAAeC,CAAAA,EAAAA,EAAAA,OAAAA,CAAOA,CAAC,IAAO,EAClCC,mBAAoBH,EACtB,EAAI,CAACA,EAAO,EACZ,MAAoBJ,CAAb,EAAaA,EAAAA,GAAAA,CAAIA,CAACQ,EAAAA,CAAiBA,CAACC,QAAQ,CAAE,CACnDC,MAAOL,EACPM,SAAuBX,CAAb,EAAaA,EAAAA,GAAAA,CAAIA,CAACH,EAAP,CACnBJ,IAAKA,EACL,GAAGK,CAAK,CACRJ,UAAWO,IAAWP,EAAWU,EACnC,EACF,EACF,EACAD,GAAWS,GAJgBX,QAIL,CAAG,aCtBzB,IAAMY,EAAuBrB,EAAAA,MAAb,IAA6B,CAC7C,CACA,EAMGC,GARwB,KAE1B,UACCE,CAAQ,WACRD,CAAS,SACToB,CAAO,CACPlB,GAAIC,EAAY,KAAK,CACrB,GAAGC,EACJ,GACOM,EAASL,CAAAA,EAAAA,EAAAA,EAAAA,CAAkBA,CAACJ,EAAU,YAC5C,MAAoBK,CAAb,EAAaA,EAAAA,GAAAA,CAAIA,CAACH,EAAP,CAChBJ,IAAKA,EACLC,UAAWO,IAAWa,EAAU,GAAaA,MAAAA,CAAVV,EAAO,EAArBH,GAAgC,OAARa,CAX0G,EAW9FV,EAAQV,GACjE,GAAGI,CAAK,EAEZ,GACAe,EAAQD,WAAW,CAAG,UChBtB,IAAMG,EAA8BvB,EAAAA,UAAgB,CAAC,EAA9B,CAKpBC,QALmD,EAApB,SAChCC,CAAS,UACTC,CAAQ,CACRC,GAAIC,EAAY,KAAK,CACrB,GAAGC,EACJ,GAEC,OADAH,EAAWI,CAAAA,EAAAA,EAAAA,EAAAA,CAAkBA,CAACJ,EAAU,oBACpBK,CAAAA,EAAAA,EAAAA,GAAAA,CAAIA,CAACH,EAAW,CAClCJ,IAAKA,EACLC,UAAWO,IAAWP,EAAWC,GACjC,GAAGG,CACL,EACF,EACAiB,GAAeH,WAAW,CAAG,iBCb7B,IAAMI,EAAwBxB,EAAAA,OAAb,GAA6B,CAAC,GAK5CC,IALyB,IAAoB,WAC9CC,CAAS,UACTC,CAAQ,CACRC,GAAIC,EAAY,GAAG,CACnB,GAAGC,EACJ,GAEC,OAAO,EADIC,CAAAA,EAAAA,EAAAA,EAAAA,CAAkBA,CAACJ,EAAU,aACpBK,CAAAA,EAAAA,EAAAA,GAAAA,CAAIA,CAACH,EAAW,CAClCJ,IAAKA,EACLC,UAAWO,IAAWP,EAAWC,GACjC,GAAGG,CAAK,EAEZ,GACAkB,EAJyBf,WAIL,CAAG,0BCZvB,IAAMgB,EAAgBC,CAAAA,EAAAA,EAAAA,CAAAA,CAAgBA,CAAC,MACjCC,EAA4B3B,EAAAA,UAAgB,CAA7B,CAA8B,EAKhDC,QAL6B,WAC9BC,CAAS,CACTC,UAAQ,CACRC,GAAIC,EAAYoB,CAAa,CAC7B,GAAGnB,EACJ,GAEC,OADAH,EAAWI,CAAAA,EAAAA,EAAAA,EAAAA,CAAkBA,CAACJ,EAAU,iBACpBK,CAAAA,EAAAA,EAAAA,GAAAA,CAAIA,CAACH,EAAW,CAClCJ,IAAKA,EACLC,UAAWO,IAAWP,EAAWC,GACjC,GAAGG,CACL,EACF,GACAqB,EAAaP,WAAW,CAAG,eCf3B,IAAMQ,EAAwB5B,EAAAA,OAAb,GAA6B,CAAC,GAK5CC,IALyB,IAAoB,CAC9CC,WAAS,UACTC,CAAQ,CACRC,GAAIC,EAAY,GAAG,CACnB,GAAGC,EACJ,GAEC,OAAO,EADIC,CAAAA,EAAAA,EAAAA,EAAAA,CAAkBA,CAACJ,EAAU,aACpBK,CAAAA,EAAAA,EAAAA,GAAAA,CAAIA,CAACH,EAAW,CAClCJ,IAAKA,EACLC,UAAWO,IAAWP,EAAWC,GACjC,GAAGG,CAAK,EAEZ,GACAsB,EAJyBnB,WAIL,CAAG,WCZvB,IAAMoB,EAAgBH,CAAAA,EAAAA,EAAAA,CAAAA,CAAgBA,CAAC,MACjCI,EAAyB9B,EAAAA,QAAb,EAA6B,CAAC,GAK7CC,KAL0B,GAAoB,WAC/CC,CAAS,CACTC,UAAQ,CACRC,GAAIC,EAAYwB,CAAa,CAC7B,GAAGvB,EACJ,GAEC,OAAO,EADIC,CAAAA,EAAAA,EAAAA,EAAAA,CAAkBA,CAACJ,EAAU,cACpBK,CAAAA,EAAAA,EAAAA,GAAAA,CAAIA,CAACH,EAAW,CAClCJ,IAAKA,EACLC,UAAWO,IAAWP,EAAWC,GACjC,GAAGG,CAAK,EAEZ,GACAwB,EAAUV,WAAW,CAAG,YCNxB,IAAMW,EAAoB/B,EAAAA,GAAb,OAA6B,CAAC,GAAnB,QAAoB,UAC1CG,CAAQ,WACRD,CAAS,IACT8B,CAAE,MACFC,CAAI,QACJC,CAAM,MACNC,GAAO,CAAK,UACZhB,CAAQ,CAERf,CADA,EACIC,EAAY,KAAK,CACrB,GAAGC,EACJ,GACOM,EAASL,CAAAA,EAAAA,EAAAA,EAAAA,CAAkBA,CAACJ,EAAU,QAC5C,MAAoBK,CAAb,EAAaA,EAAAA,GAAAA,CAAIA,CAACH,EAAP,CAChBJ,IAAKA,EACL,GAAGK,CAAK,CACRJ,UAAWO,IAAWP,EAAWU,EAAQoB,GAAM,MAAS,GAAnCvB,GAAmC,CAAHuB,GAAMC,GAAQ,QAAa,OAALA,GAAQC,GAAU,UAAiB,OAAPA,IACvGf,IATyJ,KAS/IgB,EAAoB3B,CAAAA,EAAAA,EAAb,GAAaA,CAAIA,CAACT,EAAU,CAC3CoB,GAD0B,MAAepB,CAE3C,GAAKoB,CACP,EACF,GACAY,EAAKX,WAAW,CAAG,OACnB,MAAegB,OAAOC,MAAM,CAACN,EAAM,CACjCO,INhBajB,CMgBRA,CACLkB,KNjBoBlB,CKDPS,CLCQ,CMkBrBU,EAFYnB,KDjBUS,EAAC,CCmBbH,CACVc,CAFgBX,ITpBH/B,CSsBPA,CACN2C,GHrByBf,EDFZH,CLAQzB,CSwBrB4C,CTxBsB,GSsBR5C,CFtBD6B,CFAQJ,CIyBrBoB,CJzBsB,GIuBRpB,EFvBOI,CLSRjB,CKTS,CE0BtBkC,EAFcjB,KRxBDlB,CCSUC,COkBvBmC,CPlBwB,GOgBNnC,IRzBKD,EAAC,CGAXa,CK4Bf,CAFoBb,CAElB,EAAC,SL5B0Ba,EAAC,GK2BFA,0ICwCrB,IAAMwB,EAAQ,CACnBC,WA1C4C,OAAC,MAC7CC,CAAI,eACJC,CAAa,UACbC,CAAQ,cACRC,CAAY,UACZjC,CAAQ,CACT,GACO,QAAEkC,CAAM,CAAEC,SAAO,CAAE,CAAGC,CAAAA,EAAAA,EAAAA,EAAAA,CAAgBA,GACtCC,EAAWF,CAAO,CAACL,EAAK,EAAII,CAAM,CAACJ,EAAK,CAGzBjD,EAAAA,OAAa,CAAC,IAAO,OAAEiD,EAAK,EAAI,CAACA,EAAK,EAG3D,IAAMQ,EAAoBzD,EAAAA,QAAc,CAAC0D,GAAG,CAACvC,EAAWwC,GACtD,EAAI3D,cAAoB,CAAC2D,IAxC7B,IAwCqC,KAxCnBrD,CAAU,EAC1B,MAAwB,UAAjB,OAAOA,GAAgC,OAAVA,CACtC,EAwCmBqD,EAAMrD,KAAK,EACfN,CADkB,CAClBA,YAAkB,CAAC2D,EAA6C,MACrEV,EACA,GAAGU,EAAMrD,KAAK,GAIbqD,GAGT,MACE,WAACC,MAAAA,WACC,UAACA,MAAAA,CAAI1D,UAAU,uBACZuD,IAEFD,GACC,UAACI,MAAAA,CAAI1D,UAAU,oCACZkD,GAAiB,CAAwB,UAAxB,OAAOC,CAAM,CAACJ,EAAK,CAAgBI,CAAM,CAACJ,EAAK,CAAGY,OAAOR,CAAM,CAACJ,GAAK,MAKjG,EAIEa,UAhE0C,OAAC,CAAEC,IAAE,OAAEC,CAAK,OAAE9C,CAAK,MAAE+B,CAAI,UAAEgB,CAAQ,CAAE,GACzE,QAAEC,CAAM,eAAEC,CAAa,CAAE,CAAGZ,CAAAA,EAAAA,EAAAA,EAAAA,CAAgBA,GAC5Ca,EAAYnB,GAAQc,EAE1B,MACE,UAACM,EAAAA,CAAIA,CAACC,KAAK,EACTC,KAAK,QACLR,GAAIA,EACJC,MAAOA,EACP9C,MAAOA,EACP+B,KAAMmB,EACNI,QAASN,CAAM,CAACE,EAAU,GAAKlD,EAC/BiC,SAAU,IACRgB,EAAcC,EAAWK,EAAEC,MAAM,CAACxD,KAAK,CACzC,EACA+C,SAAUA,EACVU,MAAM,KAGZ,CA8CA,EAAE,ECzEcC,CAAAA,CACLC,EAAAA,EAAAA,CACEC,EAAAA,EAAAA,gGCeb,IAAMC,EAAwBC,CAAAA,EAAAA,EAAAA,UAAAA,CAAUA,CAA8C,CAAC1E,EAAOL,KAC5F,GAAM,UAAEkB,CAAQ,UAAE8D,CAAQ,cAAEC,CAAY,WAAEhF,CAAS,YAAEiF,CAAU,eAAEC,CAAa,CAAE,GAAGC,EAAM,CAAG/E,EAGtFgF,EAAmBC,EAAAA,EAAU,GAAGC,KAAK,CAAC,CAAC,GAE7C,MACE,UAACC,EAAAA,EAAMA,CAAAA,CACLL,cAAeA,GAAiB,CAAC,EACjCE,iBAAkBA,EAClBL,SAAU,CAACf,EAA6BwB,KAEtC,IAAMC,EAAuB,CAC3BC,eAAgB,KAAO,EACvBC,gBAAiB,KAAO,EACxBC,cAAe,KACfpB,OAAQ,KACRqB,YAAa,IAAIC,MAAM,UACvBC,QAAS,GACTC,YAAY,EACZC,kBAAkB,EAClBC,WAAY,EACZC,WAAW,EACXC,UAAWC,KAAKC,GAAG,GACnBjC,KAAM,SACNkC,mBAAoB,KAAM,EAC1BC,qBAAsB,KAAM,EAC5BC,QAAS,KAAO,CAClB,CAEI1B,IAEFA,EAASU,EAAWzB,EAFR,EAIhB,EACC,GAAGmB,CAAI,UAEP,GACC,UAAChB,EAAAA,EAAIA,CAAAA,CACHpE,IAAKA,EACLgF,SAAU2B,EAAYC,YAAY,CAClC3B,aAAcA,EACdhF,UAAWA,EACXiF,WAAYA,WAES,YAApB,OAAOhE,EAA0BA,EAASyF,GAAezF,KAKpE,EAEA4D,GAAsB3D,WAAW,CAAG,wBAEpC,MAAe2D,qBAAqBA,EAAC,sFClF9B,IAAMF,EAAY,OAAC,MACxB5B,CAAI,CACJc,IAAE,UACF+C,CAAQ,WACRC,CAAS,cACT3D,CAAY,UACZD,CAAQ,OACRjC,CAAK,IACLd,CAAE,WACF4G,CAAS,MACTC,CAAI,SACJC,CAAO,CACP,GAAG5G,EACC,GAuBJ,MACE,UAAC6G,EAAAA,EAAKA,CAAAA,CAAClE,KAAMA,EAAMmE,SAtBJ,CAsBcA,GApB7B,IAAMC,EAA2B,UAAf,OAAOC,EAAmBA,EAAMzD,OAAOyD,GAAO,WAChE,GAAiB,EAACA,GAA4B,IAA7B,CAAQD,EAAUE,IAAI,EAAO,CAAC,CACtCnE,EAD0C,KAC1CA,EAAAA,KAAAA,EAAAA,EAAc2D,SAAAA,GAAa,EAA3B3D,uBAGL2D,GAAa,CAACA,EAAUO,GACnBlE,GADyB,IACzBA,EAAAA,KAAAA,EAAAA,EAAc2D,SAAAA,GAAa,EAA3B3D,cAGL8D,GAAWI,GAET,CADU,CADI,GACAE,OAAON,GACdO,IAAI,CAACH,GACPlE,GADa,IACbA,EAAAA,KAAAA,EAAAA,EAAc8D,OAAAA,GAAW,IAAzB9D,mBAKb,WAIK,OAAC,OAAEsE,CAAK,MAAEC,CAAI,CAAO,SACpB,iCACE,UAACtD,EAAAA,CAAIA,CAACuD,OAAO,EACV,GAAGF,CAAK,CACR,GAAGpH,CAAK,CACTyD,GAAIA,EACJ3D,GAAIA,GAAM,QACV6G,KAAMA,EACNY,UAAWF,EAAKrE,OAAO,EAAI,CAAC,CAACqE,EAAKG,KAAK,CACvC3E,SAAU,IACRuE,EAAMvE,QAAQ,CAACsB,GACXtB,GAAUA,EAASsB,EACzB,EACAvD,WAAiB6G,IAAV7G,EAAsBA,EAAQwG,EAAMxG,KAAK,GAEjDyG,EAAKrE,OAAO,EAAIqE,EAAKG,KAAK,CACzB,UAACzD,EAAAA,CAAIA,CAACuD,OAAO,CAACI,QAAQ,EAACzD,KAAK,mBACzBoD,EAAKG,KAAK,GAEX,UAKd,EAAE,EAIyB,OAAC,MAC1B7E,CAAI,IACJc,CAAE,UACF+C,CAAQ,CACR1D,cAAY,UACZD,CAAQ,OACRjC,CAAK,UACLC,CAAQ,CACR,GAAGb,EACC,GAUJ,MACE,UAAC6G,EAAAA,EAAKA,CAAAA,CAAClE,KAAMA,EAAMmE,SATJ,CAScA,GAR7B,GAAIN,GAAa,EAACQ,GAAe,IAAhB,CAAQA,CAAQ,CAAC,CAChC,EADoC,IAC7BlE,OAAAA,EAAAA,KAAAA,EAAAA,EAAc2D,SAAAA,GAAa,EAA3B3D,sBAIX,WAIK,OAAC,CAAEsE,OAAK,CAAEC,MAAI,CAAO,SACpB,iCACE,UAACtD,EAAAA,CAAIA,CAACuD,OAAO,EACXxH,GAAG,SACF,GAAGsH,CAAK,CACR,GAAGpH,CAAK,CACTyD,GAAIA,EACJ8D,UAAWF,EAAKrE,OAAO,EAAI,CAAC,CAACqE,EAAKG,KAAK,CACvC3E,SAAWsB,IACTiD,EAAMvE,QAAQ,CAACsB,GACXtB,GAAUA,EAASsB,EACzB,EACAvD,WAAiB6G,IAAV7G,EAAsBA,EAAQwG,EAAMxG,KAAK,UAE/CC,IAEFwG,EAAKrE,OAAO,EAAIqE,EAAKG,KAAK,CACzB,UAACzD,EAAAA,CAAIA,CAACuD,OAAO,CAACI,QAAQ,EAACzD,KAAK,mBACzBoD,EAAKG,KAAK,GAEX,UAKd,EAAE,mOCQF,MA7GyB,IAMvB,GAAM,CAACG,EAAYC,EAAc,CAAGC,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAMC,CAHhDC,MAAO,EACT,GAGQC,EAAWhI,EAAMiI,EAsGK,IAtGC,EAAwB,cAApBjI,EAAMiI,MAAM,CAAC,EAAE,EAAoBjI,EAAMiI,MAAM,CAAC,EAAE,CAC/E,GAAEC,CAAC,CAAE,CAAGC,CAAAA,EAAAA,EAAAA,EAAAA,CAAcA,CAAC,UAGvBC,EAAUC,CAAAA,EAAAA,EAAAA,MAAAA,CAAMA,CAAC,MAiBjB9B,EAAe,MAAO+B,EAAY1E,SAQlC2E,EAPAD,GAAOA,EAAMhD,cAAc,GAG/B,IAAMkD,EAAM,CACVT,MAFiBnE,IAAU+D,CAAAA,EAETI,KAAK,CAACd,IAAI,EAC9B,CAQIsB,EAJFA,EADEP,EACS,MAAMS,EADL,CACeA,CAACC,KAAK,CAAC,UAA0B,OAAhB1I,EAAMiI,MAAM,CAAC,EAAE,EAAIO,GAEpD,MAAMC,EAAAA,CAAUA,CAACE,IAAI,CAAC,SAAUH,KAE7BD,EAASK,GAAG,EAAE,EAC5BC,EAAKA,CAACC,OAAO,CAACZ,EAAE,4BAChBa,IAAAA,IAAW,CAAC,wBAEZF,EAAAA,EAAKA,CAACrB,KAAK,CAACe,EAEhB,EAiBA,MAfAS,CAAAA,EAAAA,EAAAA,SAAAA,CAASA,CAAC,KACR,IAAMC,EAAa,CACjBC,MAAO,CAAC,EACRC,KAAM,CAAEpB,MAAO,KAAM,EACrBqB,MAAO,GACT,EACIpB,GAKFqB,CAJoB,MADR,IAEV,IAAMd,EAAW,MAAME,EAAAA,CAAUA,CAACa,GAAG,CAAC,UAA0B,OAAhBtJ,EAAMiI,MAAM,CAAC,EAAE,EAAIgB,GACnErB,EAAc,GAAqB,EAAE,GAAG2B,CAAS,CAAE,EAAhB,CAAmBhB,CAAQ,CAAC,GACjE,GAGJ,EAAG,EAAE,EAGH,UAACjF,MAAAA,UACC,UAACkG,EAAAA,CAASA,CAAAA,CAAC5J,UAAU,WAAW6J,KAAK,aACnC,UAAChI,EAAAA,CAAIA,CAAAA,CAACiI,MAAO,CAAEC,UAAW,MAAOC,UAAW,kEAAmE,WAC7G,UAACnF,EAAAA,CAAqBA,CAAAA,CAACE,SAAU4B,EAAc5G,IAAKyI,EAAStD,cAAe6C,EAAYkC,mBAAoB,YAC1G,WAACpI,EAAAA,CAAIA,CAACU,IAAI,YACR,UAAC2H,EAAAA,CAAGA,CAAAA,UACF,UAACC,EAAAA,CAAGA,CAAAA,UACF,UAACtI,EAAAA,CAAIA,CAACQ,KAAK,WAAEiG,EAAE,cAGnB,UAAC8B,KAAAA,CAAAA,GACD,UAACF,EAAAA,CAAGA,CAAAA,UACF,UAACC,EAAAA,CAAGA,CAAAA,CAACE,EAAE,IAACC,GAAI,EAAGC,GAAI,YACjB,WAACpG,EAAAA,CAAIA,CAACqG,KAAK,YACT,UAACrG,EAAAA,CAAIA,CAACsG,KAAK,EAACzK,UAAU,0BAAkBsI,EAAE,UAC1C,UAAC3D,EAAAA,EAASA,CAAAA,CACR5B,KAAK,QACLc,GAAG,QACH+C,QAAQ,IAAC5F,MAAO+G,EAAWI,KAAK,CAChCtB,UAAY,GAAmBlD,YAAO3C,GAAS,IAAIqG,IAAI,GACtDqD,eAAiBpC,EAAE,aACnBpF,aAAc,CACb2D,UAAWyB,EAAE,mBAAmB,EAClCrF,SAvEC,CAuES0H,GAtE5B,GAAIpG,EAAEC,MAAM,CAAE,CACZ,GAAM,MAAEzB,CAAI,OAAE/B,CAAK,CAAE,CAAGuD,EAAEC,MAAM,CAChCwD,EAAc,GAAqB,EACjC,GAAG2B,CAAS,CACZ,CAAC5G,CAFgC,CAE3B,CAAE/B,EACV,EACF,CACF,WAoEY,WAACkJ,EAAAA,CAAGA,CAAAA,CAAClK,UAAU,iBACb,UAACmK,EAAAA,CAAGA,CAAAA,CAACS,EAAE,IAACN,GAAG,aACT,UAACO,EAAAA,CAAMA,CAAAA,CAACxG,KAAK,SAASjD,QAAQ,mBAAWkH,EAAE,cAE7C,UAAC6B,EAAAA,CAAGA,CAAAA,UACF,UAACU,EAAAA,CAAMA,CAAAA,CAACC,QAtFH,CAsFYC,IApF/BC,OAAOC,QAAQ,CAAC,EAAG,EACrB,EAmF+C7J,QAAQ,gBAAQkH,EAAE,0BASnE,iDC1HA,IAAM4C,EAAuBpL,QAAb,CAAaA,UAAF,GAAqB,CAAC,MACjDoL,EAAQhK,WAAW,CAAG,oBACtB,MAAegK,OAAOA,EAAC", "sources": ["webpack://_N_E/?7361", "webpack://_N_E/./node_modules/react-bootstrap/esm/CardBody.js", "webpack://_N_E/./node_modules/react-bootstrap/esm/CardFooter.js", "webpack://_N_E/./node_modules/react-bootstrap/esm/CardHeader.js", "webpack://_N_E/./node_modules/react-bootstrap/esm/CardImg.js", "webpack://_N_E/./node_modules/react-bootstrap/esm/CardImgOverlay.js", "webpack://_N_E/./node_modules/react-bootstrap/esm/CardLink.js", "webpack://_N_E/./node_modules/react-bootstrap/esm/CardSubtitle.js", "webpack://_N_E/./node_modules/react-bootstrap/esm/CardText.js", "webpack://_N_E/./node_modules/react-bootstrap/esm/CardTitle.js", "webpack://_N_E/./node_modules/react-bootstrap/esm/Card.js", "webpack://_N_E/./components/common/FormikRadio.tsx", "webpack://_N_E/./components/common/FormValidation.tsx", "webpack://_N_E/./components/common/ValidationFormWrapper.tsx", "webpack://_N_E/./components/common/FormikTextInput.tsx", "webpack://_N_E/./pages/adminsettings/mailsettings/form.tsx", "webpack://_N_E/./node_modules/react-bootstrap/esm/CardHeaderContext.js"], "sourcesContent": ["\n    (window.__NEXT_P = window.__NEXT_P || []).push([\n      \"/adminsettings/mailsettings/form\",\n      function () {\n        return require(\"private-next-pages/adminsettings/mailsettings/form.tsx\");\n      }\n    ]);\n    if(module.hot) {\n      module.hot.dispose(function () {\n        window.__NEXT_P.push([\"/adminsettings/mailsettings/form\"])\n      });\n    }\n  ", "\"use client\";\n\nimport * as React from 'react';\nimport classNames from 'classnames';\nimport { useBootstrapPrefix } from './ThemeProvider';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst CardBody = /*#__PURE__*/React.forwardRef(({\n  className,\n  bsPrefix,\n  as: Component = 'div',\n  ...props\n}, ref) => {\n  bsPrefix = useBootstrapPrefix(bsPrefix, 'card-body');\n  return /*#__PURE__*/_jsx(Component, {\n    ref: ref,\n    className: classNames(className, bsPrefix),\n    ...props\n  });\n});\nCardBody.displayName = 'CardBody';\nexport default CardBody;", "\"use client\";\n\nimport * as React from 'react';\nimport classNames from 'classnames';\nimport { useBootstrapPrefix } from './ThemeProvider';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst CardFooter = /*#__PURE__*/React.forwardRef(({\n  className,\n  bsPrefix,\n  as: Component = 'div',\n  ...props\n}, ref) => {\n  bsPrefix = useBootstrapPrefix(bsPrefix, 'card-footer');\n  return /*#__PURE__*/_jsx(Component, {\n    ref: ref,\n    className: classNames(className, bsPrefix),\n    ...props\n  });\n});\nCardFooter.displayName = 'CardFooter';\nexport default CardFooter;", "\"use client\";\n\nimport classNames from 'classnames';\nimport * as React from 'react';\nimport { useMemo } from 'react';\nimport { useBootstrapPrefix } from './ThemeProvider';\nimport CardHeaderContext from './CardHeaderContext';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst CardHeader = /*#__PURE__*/React.forwardRef(({\n  bsPrefix,\n  className,\n  // Need to define the default \"as\" during prop destructuring to be compatible with styled-components github.com/react-bootstrap/react-bootstrap/issues/3595\n  as: Component = 'div',\n  ...props\n}, ref) => {\n  const prefix = useBootstrapPrefix(bsPrefix, 'card-header');\n  const contextValue = useMemo(() => ({\n    cardHeaderBsPrefix: prefix\n  }), [prefix]);\n  return /*#__PURE__*/_jsx(CardHeaderContext.Provider, {\n    value: contextValue,\n    children: /*#__PURE__*/_jsx(Component, {\n      ref: ref,\n      ...props,\n      className: classNames(className, prefix)\n    })\n  });\n});\nCardHeader.displayName = 'CardHeader';\nexport default CardHeader;", "\"use client\";\n\nimport classNames from 'classnames';\nimport * as React from 'react';\nimport { useBootstrapPrefix } from './ThemeProvider';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst CardImg = /*#__PURE__*/React.forwardRef(\n// Need to define the default \"as\" during prop destructuring to be compatible with styled-components github.com/react-bootstrap/react-bootstrap/issues/3595\n({\n  bsPrefix,\n  className,\n  variant,\n  as: Component = 'img',\n  ...props\n}, ref) => {\n  const prefix = useBootstrapPrefix(bsPrefix, 'card-img');\n  return /*#__PURE__*/_jsx(Component, {\n    ref: ref,\n    className: classNames(variant ? `${prefix}-${variant}` : prefix, className),\n    ...props\n  });\n});\nCardImg.displayName = 'CardImg';\nexport default CardImg;", "\"use client\";\n\nimport * as React from 'react';\nimport classNames from 'classnames';\nimport { useBootstrapPrefix } from './ThemeProvider';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst CardImgOverlay = /*#__PURE__*/React.forwardRef(({\n  className,\n  bsPrefix,\n  as: Component = 'div',\n  ...props\n}, ref) => {\n  bsPrefix = useBootstrapPrefix(bsPrefix, 'card-img-overlay');\n  return /*#__PURE__*/_jsx(Component, {\n    ref: ref,\n    className: classNames(className, bsPrefix),\n    ...props\n  });\n});\nCardImgOverlay.displayName = 'CardImgOverlay';\nexport default CardImgOverlay;", "\"use client\";\n\nimport * as React from 'react';\nimport classNames from 'classnames';\nimport { useBootstrapPrefix } from './ThemeProvider';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst CardLink = /*#__PURE__*/React.forwardRef(({\n  className,\n  bsPrefix,\n  as: Component = 'a',\n  ...props\n}, ref) => {\n  bsPrefix = useBootstrapPrefix(bsPrefix, 'card-link');\n  return /*#__PURE__*/_jsx(Component, {\n    ref: ref,\n    className: classNames(className, bsPrefix),\n    ...props\n  });\n});\nCardLink.displayName = 'CardLink';\nexport default CardLink;", "\"use client\";\n\nimport * as React from 'react';\nimport classNames from 'classnames';\nimport { useBootstrapPrefix } from './ThemeProvider';\nimport divWithClassName from './divWithClassName';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst DivStyledAsH6 = divWithClassName('h6');\nconst CardSubtitle = /*#__PURE__*/React.forwardRef(({\n  className,\n  bsPrefix,\n  as: Component = DivStyledAsH6,\n  ...props\n}, ref) => {\n  bsPrefix = useBootstrapPrefix(bsPrefix, 'card-subtitle');\n  return /*#__PURE__*/_jsx(Component, {\n    ref: ref,\n    className: classNames(className, bsPrefix),\n    ...props\n  });\n});\nCardSubtitle.displayName = 'CardSubtitle';\nexport default CardSubtitle;", "\"use client\";\n\nimport * as React from 'react';\nimport classNames from 'classnames';\nimport { useBootstrapPrefix } from './ThemeProvider';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst CardText = /*#__PURE__*/React.forwardRef(({\n  className,\n  bsPrefix,\n  as: Component = 'p',\n  ...props\n}, ref) => {\n  bsPrefix = useBootstrapPrefix(bsPrefix, 'card-text');\n  return /*#__PURE__*/_jsx(Component, {\n    ref: ref,\n    className: classNames(className, bsPrefix),\n    ...props\n  });\n});\nCardText.displayName = 'CardText';\nexport default CardText;", "\"use client\";\n\nimport * as React from 'react';\nimport classNames from 'classnames';\nimport { useBootstrapPrefix } from './ThemeProvider';\nimport divWithClassName from './divWithClassName';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst DivStyledAsH5 = divWithClassName('h5');\nconst CardTitle = /*#__PURE__*/React.forwardRef(({\n  className,\n  bsPrefix,\n  as: Component = DivStyledAsH5,\n  ...props\n}, ref) => {\n  bsPrefix = useBootstrapPrefix(bsPrefix, 'card-title');\n  return /*#__PURE__*/_jsx(Component, {\n    ref: ref,\n    className: classNames(className, bsPrefix),\n    ...props\n  });\n});\nCardTitle.displayName = 'CardTitle';\nexport default CardTitle;", "\"use client\";\n\nimport classNames from 'classnames';\nimport * as React from 'react';\nimport { useBootstrapPrefix } from './ThemeProvider';\nimport CardBody from './CardBody';\nimport CardFooter from './CardFooter';\nimport CardHeader from './CardHeader';\nimport CardImg from './CardImg';\nimport CardImgOverlay from './CardImgOverlay';\nimport CardLink from './CardLink';\nimport CardSubtitle from './CardSubtitle';\nimport CardText from './CardText';\nimport CardTitle from './CardTitle';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst Card = /*#__PURE__*/React.forwardRef(({\n  bsPrefix,\n  className,\n  bg,\n  text,\n  border,\n  body = false,\n  children,\n  // Need to define the default \"as\" during prop destructuring to be compatible with styled-components github.com/react-bootstrap/react-bootstrap/issues/3595\n  as: Component = 'div',\n  ...props\n}, ref) => {\n  const prefix = useBootstrapPrefix(bsPrefix, 'card');\n  return /*#__PURE__*/_jsx(Component, {\n    ref: ref,\n    ...props,\n    className: classNames(className, prefix, bg && `bg-${bg}`, text && `text-${text}`, border && `border-${border}`),\n    children: body ? /*#__PURE__*/_jsx(CardBody, {\n      children: children\n    }) : children\n  });\n});\nCard.displayName = 'Card';\nexport default Object.assign(Card, {\n  Img: CardImg,\n  Title: CardTitle,\n  Subtitle: CardSubtitle,\n  Body: CardBody,\n  Link: CardLink,\n  Text: CardText,\n  Header: CardHeader,\n  Footer: CardFooter,\n  ImgOverlay: CardImgOverlay\n});", "import React from 'react';\r\nimport { useFormikContext, Field } from 'formik';\r\nimport { Form } from 'react-bootstrap';\r\n\r\ninterface RadioItemProps {\r\n  id: string;\r\n  label: string;\r\n  value: string;\r\n  name?: string;\r\n  disabled?: boolean;\r\n}\r\n\r\ninterface RadioGroupProps {\r\n  name: string;\r\n  valueSelected: string;\r\n  onChange: (e: React.ChangeEvent<HTMLInputElement>) => void;\r\n  errorMessage?: string;\r\n  children: React.ReactNode;\r\n}\r\n\r\n// Type Guard to ensure child.props is an object\r\nfunction isObject(props: any): props is { [key: string]: any } {\r\n  return typeof props === 'object' && props !== null;\r\n}\r\n\r\nconst RadioItem: React.FC<RadioItemProps> = ({ id, label, value, name, disabled }) => {\r\n  const { values, setFieldValue } = useFormikContext<any>();\r\n  const fieldName = name || id;\r\n\r\n  return (\r\n    <Form.Check\r\n      type=\"radio\"\r\n      id={id}\r\n      label={label}\r\n      value={value}\r\n      name={fieldName}\r\n      checked={values[fieldName] === value}\r\n      onChange={(e) => {\r\n        setFieldValue(fieldName, e.target.value);\r\n      }}\r\n      disabled={disabled}\r\n      inline\r\n    />\r\n  );\r\n};\r\n\r\nconst RadioGroup: React.FC<RadioGroupProps> = ({\r\n  name,\r\n  valueSelected,\r\n  onChange,\r\n  errorMessage,\r\n  children,\r\n}) => {\r\n  const { errors, touched } = useFormikContext<any>();\r\n  const hasError = touched[name] && errors[name];\r\n\r\n  // Create a context to pass the name to RadioItems\r\n  const radioContext = React.useMemo(() => ({ name }), [name]);\r\n\r\n  // Clone children to pass the name, ensuring props are spread safely\r\n  const childrenWithProps = React.Children.map(children, (child) => {\r\n    if (React.isValidElement(child)) {\r\n      // Ensure child.props is an object before spreading\r\n      if (isObject(child.props)) {\r\n        return React.cloneElement(child as React.ReactElement<RadioItemProps>, {\r\n          name,\r\n          ...child.props, // Safely spread child.props\r\n        });\r\n      }\r\n    }\r\n    return child;\r\n  });\r\n\r\n  return (\r\n    <div>\r\n      <div className=\"radio-group\">\r\n        {childrenWithProps}\r\n      </div>\r\n      {hasError && (\r\n        <div className=\"invalid-feedback d-block\">\r\n          {errorMessage || (typeof errors[name] === 'string' ? errors[name] : String(errors[name]))}\r\n        </div>\r\n      )}\r\n    </div>\r\n  );\r\n};\r\n\r\nexport const Radio = {\r\n  RadioGroup,\r\n  RadioItem,\r\n};\r\n\r\nexport default Radio;\r\n", "// This file exports all the components needed to replace react-bootstrap4-form-validation\r\n// It serves as a drop-in replacement for the original library\r\n\r\nimport ValidationForm from './ValidationFormWrapper';\r\nimport { TextInput, SelectGroup } from './FormikTextInput';\r\nimport { Radio } from './FormikRadio';\r\n\r\n// Export all components\r\nexport {\r\n  ValidationForm,\r\n  TextInput,\r\n  SelectGroup,\r\n  Radio\r\n};\r\n\r\n// Also export as default for convenience\r\nexport default {\r\n  ValidationForm,\r\n  TextInput,\r\n  SelectGroup,\r\n  Radio\r\n};\r\n", "import React, { forwardRef } from 'react';\r\nimport { Formik, Form, FormikProps, FormikHelpers } from 'formik';\r\nimport * as Yup from 'yup';\r\n\r\n// This is a wrapper component that replaces react-bootstrap4-form-validation with Formik\r\ninterface MockEvent {\r\n  preventDefault: () => void;\r\n  stopPropagation: () => void;\r\n  currentTarget: (EventTarget & Element) | null;\r\n  target: EventTarget | null;\r\n  nativeEvent: Event;\r\n  bubbles: boolean;\r\n  cancelable: boolean;\r\n  defaultPrevented: boolean;\r\n  eventPhase: number;\r\n  isTrusted: boolean;\r\n  timeStamp: number;\r\n  type: string;\r\n  isDefaultPrevented: () => boolean;\r\n  isPropagationStopped: () => boolean;\r\n  persist: () => void;\r\n}\r\n\r\ninterface ValidationFormWrapperProps {\r\n  children: React.ReactNode | ((formikProps: FormikProps<any>) => React.ReactNode);\r\n  onSubmit: (event: React.FormEvent | MockEvent, values?: Record<string, any>, actions?: FormikHelpers<Record<string, any>>) => void;\r\n  onErrorSubmit?: (errors: any) => void;\r\n  initialValues?: Record<string, any>;\r\n  enableReinitialize?: boolean;\r\n  autoComplete?: string;\r\n  className?: string;\r\n  onKeyPress?: (e: any) => void;\r\n}\r\n\r\nconst ValidationFormWrapper = forwardRef<HTMLFormElement, ValidationFormWrapperProps>((props, ref) => {\r\n  const { children, onSubmit, autoComplete, className, onKeyPress, initialValues, ...rest } = props;\r\n\r\n  // Create an empty validation schema by default\r\n  const validationSchema = Yup.object().shape({});\r\n\r\n  return (\r\n    <Formik\r\n      initialValues={initialValues || {}}\r\n      validationSchema={validationSchema}\r\n      onSubmit={(values: Record<string, any>, actions: FormikHelpers<Record<string, any>>) => {\r\n        // Create a mock event object with preventDefault method and currentTarget\r\n        const mockEvent: MockEvent = {\r\n          preventDefault: () => {},\r\n          stopPropagation: () => {},\r\n          currentTarget: null, // Set to null to avoid checkValidity errors\r\n          target: null,\r\n          nativeEvent: new Event('submit'),\r\n          bubbles: false,\r\n          cancelable: true,\r\n          defaultPrevented: false,\r\n          eventPhase: 0,\r\n          isTrusted: false,\r\n          timeStamp: Date.now(),\r\n          type: 'submit',\r\n          isDefaultPrevented: () => false,\r\n          isPropagationStopped: () => false,\r\n          persist: () => {}\r\n        };\r\n\r\n        if (onSubmit) {\r\n          // Pass the mock event object to maintain compatibility with the original code\r\n          onSubmit(mockEvent, values, actions);\r\n        }\r\n      }}\r\n      {...rest}\r\n    >\r\n      {(formikProps: FormikProps<any>) => (\r\n        <Form\r\n          ref={ref}\r\n          onSubmit={formikProps.handleSubmit}\r\n          autoComplete={autoComplete}\r\n          className={className}\r\n          onKeyPress={onKeyPress}\r\n        >\r\n          {typeof children === 'function' ? children(formikProps) : children}\r\n        </Form>\r\n      )}\r\n    </Formik>\r\n  );\r\n});\r\n\r\nValidationFormWrapper.displayName = 'ValidationFormWrapper';\r\n\r\nexport default ValidationFormWrapper;\r\n", "import React from 'react';\r\nimport { Form } from 'react-bootstrap';\r\nimport { Field, ErrorMessage, useField } from 'formik';\r\n\r\n// This component mimics the TextInput component from react-bootstrap4-form-validation\r\n// but uses Formik under the hood\r\nexport const TextInput = ({\r\n  name,\r\n  id,\r\n  required,\r\n  validator,\r\n  errorMessage,\r\n  onChange,\r\n  value,\r\n  as,\r\n  multiline,\r\n  rows,\r\n  pattern,\r\n  ...props\r\n}: any) => {\r\n  // Create a custom validation function that mimics the original validator\r\n  const validate = (val: any) => {\r\n    // Safely convert value to string and trim\r\n    const stringVal = typeof val === 'string' ? val : String(val || '');\r\n    if (required && (!val || stringVal.trim() === '')) {\r\n      return errorMessage?.validator || 'This field is required';\r\n    }\r\n\r\n    if (validator && !validator(val)) {\r\n      return errorMessage?.validator || 'Invalid value';\r\n    }\r\n\r\n    if (pattern && val) {\r\n      const regex = new RegExp(pattern);\r\n      if (!regex.test(val)) {\r\n        return errorMessage?.pattern || 'Invalid format';\r\n      }\r\n    }\r\n\r\n    return undefined;\r\n  };\r\n\r\n  return (\r\n    <Field name={name} validate={validate}>\r\n      {({ field, meta }: any) => (\r\n        <>\r\n          <Form.Control\r\n            {...field}\r\n            {...props}\r\n            id={id}\r\n            as={as || 'input'}\r\n            rows={rows}\r\n            isInvalid={meta.touched && !!meta.error}\r\n            onChange={(e: React.ChangeEvent<HTMLInputElement>) => {\r\n              field.onChange(e);\r\n              if (onChange) onChange(e);\r\n            }}\r\n            value={value !== undefined ? value : field.value}\r\n          />\r\n          {meta.touched && meta.error ? (\r\n            <Form.Control.Feedback type=\"invalid\">\r\n              {meta.error}\r\n            </Form.Control.Feedback>\r\n          ) : null}\r\n        </>\r\n      )}\r\n    </Field>\r\n  );\r\n};\r\n\r\n// This component mimics the SelectGroup component from react-bootstrap4-form-validation\r\n// but uses Formik under the hood\r\nexport const SelectGroup = ({\r\n  name,\r\n  id,\r\n  required,\r\n  errorMessage,\r\n  onChange,\r\n  value,\r\n  children,\r\n  ...props\r\n}: any) => {\r\n  // Create a custom validation function that mimics the original validator\r\n  const validate = (val: any) => {\r\n    if (required && (!val || val === '')) {\r\n      return errorMessage?.validator || 'This field is required';\r\n    }\r\n\r\n    return undefined;\r\n  };\r\n\r\n  return (\r\n    <Field name={name} validate={validate}>\r\n      {({ field, meta }: any) => (\r\n        <>\r\n          <Form.Control\r\n            as=\"select\"\r\n            {...field}\r\n            {...props}\r\n            id={id}\r\n            isInvalid={meta.touched && !!meta.error}\r\n            onChange={(e: React.ChangeEvent<HTMLSelectElement>) => {\r\n              field.onChange(e);\r\n              if (onChange) onChange(e);\r\n            }}\r\n            value={value !== undefined ? value : field.value}\r\n          >\r\n            {children}\r\n          </Form.Control>\r\n          {meta.touched && meta.error ? (\r\n            <Form.Control.Feedback type=\"invalid\">\r\n              {meta.error}\r\n            </Form.Control.Feedback>\r\n          ) : null}\r\n        </>\r\n      )}\r\n    </Field>\r\n  );\r\n};\r\n\r\n// Export both components as named exports and as a default object\r\nexport default {\r\n  TextInput,\r\n  SelectGroup\r\n};\r\n", "//Import Library\r\nimport { useState, useRef, useEffect } from \"react\";\r\nimport { Container, Card, Row, Col, Form, Button } from \"react-bootstrap\";\r\nimport ValidationFormWrapper from \"../../../components/common/ValidationFormWrapper\";\r\nimport { TextInput } from \"../../../components/common/FormValidation\";\r\nimport Router from \"next/router\";\r\n\r\nimport toast from 'react-hot-toast';\r\n\r\n//Import services/components\r\nimport apiService from \"../../../services/apiService\";\r\nimport { useTranslation } from 'next-i18next';\r\n\r\ninterface MailSettingsFormProps {\r\n    [key: string]: any;\r\n}\r\n\r\nconst MailSettingsForm = (props: MailSettingsFormProps) => {\r\n\r\n  const _initialrole = {\r\n    title: '',\r\n  }\r\n\r\n  const [initialVal, setInitialVal] = useState<any>(_initialrole);\r\n    const editform = props.routes && props.routes[0] === \"edit_role\" && props.routes[1];\r\n  const { t } = useTranslation('common');\r\n\r\n\r\n  const formRef = useRef(null);\r\n\r\n  const resetHandler = () => {\r\n    // Reset validation state (Formik handles this automatically)\r\n    window.scrollTo(0, 0);\r\n  };\r\n\r\n  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement>) => {\r\n    if (e.target) {\r\n      const { name, value } = e.target;\r\n      setInitialVal((prevState: any) => ({\r\n        ...prevState,\r\n        [name]: value\r\n      }));\r\n    }\r\n  }\r\n\r\n  const handleSubmit = async (event: any, values?: any) => {\r\n    if (event) event.preventDefault();\r\n    // Use Formik values if available, otherwise fall back to initialVal\r\n    const formValues = values || initialVal;\r\n    const obj = {\r\n      title: formValues.title.trim(),\r\n    };\r\n\r\n    let response;\r\n    if (editform) {\r\n      response = await apiService.patch(`/roles/${props.routes[1]}`, obj);\r\n    } else {\r\n      response = await apiService.post(\"/roles\", obj);\r\n    }\r\n    if (response && response._id) {\r\n      toast.success(t(\"Roleisaddedsuccessfully\"));\r\n      Router.push(\"/adminsettings/role\");\r\n    } else {\r\n      toast.error(response)\r\n    }\r\n  }\r\n\r\n  useEffect(() => {\r\n    const roleParams = {\r\n      query: {},\r\n      sort: { title: \"asc\" },\r\n      limit: \"~\",\r\n    };\r\n    if (editform) {\r\n      const getRoleData = async () => {\r\n        const response = await apiService.get(`/roles/${props.routes[1]}`, roleParams);\r\n        setInitialVal((prevState: any) => ({ ...prevState, ...response }));\r\n      }\r\n      getRoleData();\r\n    }\r\n  }, []);\r\n\r\n  return (\r\n    <div>\r\n      <Container className=\"formCard\" fluid>\r\n        <Card style={{ marginTop: \"5px\", boxShadow: \"0 4px 8px 0 rgba(0, 0, 0, 0.2), 0 6px 20px 0 rgba(0, 0, 0, 0.19)\" }}>\r\n          <ValidationFormWrapper onSubmit={handleSubmit} ref={formRef} initialValues={initialVal} enableReinitialize={true}>\r\n            <Card.Body>\r\n              <Row>\r\n                <Col>\r\n                  <Card.Title>{t(\"Role\")}</Card.Title>\r\n                </Col>\r\n              </Row>\r\n              <hr />\r\n              <Row>\r\n                <Col md lg={6} sm={12}>\r\n                  <Form.Group>\r\n                    <Form.Label className=\"required-field\">{t(\"Role\")}</Form.Label>\r\n                    <TextInput\r\n                      name=\"title\"\r\n                      id=\"title\"\r\n                      required value={initialVal.title}\r\n                      validator={((value: string) => String(value || '').trim() !== \"\")}\r\n                       successMessage= {t(\"Looksgood\")}\r\n                       errorMessage={{\r\n                        validator: t(\"PleaseAddtheRole\")}}\r\n                      onChange={handleChange}\r\n                    />\r\n                  </Form.Group>\r\n                </Col>\r\n              </Row>\r\n              <Row className=\"my-4\">\r\n                <Col xs lg=\"2\">\r\n                  <Button type=\"submit\" variant=\"primary\">{t(\"submit\")}</Button>\r\n                </Col>\r\n                <Col>\r\n                  <Button onClick={resetHandler} variant=\"info\">{t(\"reset\")}</Button>\r\n                </Col>\r\n              </Row>\r\n            </Card.Body>\r\n          </ValidationFormWrapper>\r\n        </Card>\r\n      </Container>\r\n    </div>\r\n  );\r\n}\r\nexport default MailSettingsForm;\r\n", "\"use client\";\n\nimport * as React from 'react';\nconst context = /*#__PURE__*/React.createContext(null);\ncontext.displayName = 'CardHeaderContext';\nexport default context;"], "names": ["CardBody", "React", "ref", "className", "bsPrefix", "as", "Component", "props", "useBootstrapPrefix", "_jsx", "classNames", "<PERSON><PERSON><PERSON>er", "<PERSON><PERSON><PERSON><PERSON>", "prefix", "contextValue", "useMemo", "cardHeaderBsPrefix", "CardHeaderContext", "Provider", "value", "children", "displayName", "CardImg", "variant", "CardImgOverlay", "CardLink", "DivStyledAsH6", "divWithClassName", "CardSubtitle", "CardText", "DivStyledAsH5", "CardTitle", "Card", "bg", "text", "border", "body", "Object", "assign", "Img", "Title", "Subtitle", "Body", "Link", "Text", "Header", "Footer", "ImgOverlay", "Radio", "RadioGroup", "name", "valueSelected", "onChange", "errorMessage", "errors", "touched", "useFormikContext", "<PERSON><PERSON><PERSON><PERSON>", "childrenWithProps", "map", "child", "div", "String", "RadioItem", "id", "label", "disabled", "values", "setFieldValue", "fieldName", "Form", "Check", "type", "checked", "e", "target", "inline", "ValidationForm", "TextInput", "SelectGroup", "ValidationFormWrapper", "forwardRef", "onSubmit", "autoComplete", "onKeyPress", "initialValues", "rest", "validationSchema", "<PERSON><PERSON>", "shape", "<PERSON><PERSON>", "actions", "mockEvent", "preventDefault", "stopPropagation", "currentTarget", "nativeEvent", "Event", "bubbles", "cancelable", "defaultPrevented", "eventPhase", "isTrusted", "timeStamp", "Date", "now", "isDefaultPrevented", "isPropagationStopped", "persist", "formikProps", "handleSubmit", "required", "validator", "multiline", "rows", "pattern", "Field", "validate", "stringVal", "val", "trim", "RegExp", "test", "field", "meta", "Control", "isInvalid", "error", "undefined", "<PERSON><PERSON><PERSON>", "initialVal", "setInitialVal", "useState", "_initialrole", "title", "editform", "routes", "t", "useTranslation", "formRef", "useRef", "event", "response", "obj", "apiService", "patch", "post", "_id", "toast", "success", "Router", "useEffect", "roleParams", "query", "sort", "limit", "getRoleData", "get", "prevState", "Container", "fluid", "style", "marginTop", "boxShadow", "enableReinitialize", "Row", "Col", "hr", "md", "lg", "sm", "Group", "Label", "successMessage", "handleChange", "xs", "<PERSON><PERSON>", "onClick", "re<PERSON><PERSON><PERSON><PERSON>", "window", "scrollTo", "context"], "sourceRoot": "", "ignoreList": [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 16]}