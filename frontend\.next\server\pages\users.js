"use strict";(()=>{var e={};e.id=9424,e.ids=[636,3220,9424],e.modules={123:e=>{e.exports=require("dom-helpers/removeEventListener")},283:(e,r,t)=>{t.a(e,async(e,s)=>{try{t.r(r),t.d(r,{default:()=>x});var a=t(8732),o=t(19918),i=t.n(o),n=t(82015),u=t(12403),p=t(91353),l=t(56084),d=t(63487),c=e([d]);d=(c.then?(await c)():c)[0];let x=function(e){let[r,t]=(0,n.useState)([]),[,s]=(0,n.useState)(!1),[o,c]=(0,n.useState)(0),[x,m]=(0,n.useState)(10),[h,g]=(0,n.useState)(!1),[q,P]=(0,n.useState)({}),A={sort:{created_at:"desc"},limit:x,page:1,query:{}},S=[{name:"Username",selector:"username",cell:e=>e.username},{name:"Email",selector:"email",cell:e=>e.email},{name:"Role",selector:"role",cell:e=>e.role?e.role.title:""},{name:"Institution",selector:"institution",cell:e=>e.institution?e.institution.title:""},{name:"Action",selector:"",cell:e=>(0,a.jsxs)("div",{children:[(0,a.jsx)(i(),{href:"/users/[...routes]",as:`/users/edit/${e._id}`,children:"Edit"}),"\xa0",(0,a.jsx)("a",{onClick:()=>j(e),children:"Delete"})]})}],v=async()=>{s(!0);let e=await d.A.get("/users",A);e&&e.data&&e.data.length>0&&(t(e.data),c(e.totalCount),s(!1))},f=async(e,r)=>{A.limit=e,A.page=r,s(!0);let a=await d.A.get("/users",A);a&&a.data&&a.data.length>0&&(t(a.data),m(e),s(!1))},j=async e=>{P(e),g(!0)},w=async()=>{await d.A.remove(`/users/${q._id}`),v(),P({}),g(!1)},b=()=>g(!1);return(0,a.jsxs)("div",{children:[(0,a.jsxs)(u.A,{show:h,onHide:b,children:[(0,a.jsx)(u.A.Header,{closeButton:!0,children:(0,a.jsx)(u.A.Title,{children:"Delete User"})}),(0,a.jsx)(u.A.Body,{children:"Are you sure want to delete this user ?"}),(0,a.jsxs)(u.A.Footer,{children:[(0,a.jsx)(p.A,{variant:"secondary",onClick:b,children:"Cancel"}),(0,a.jsx)(p.A,{variant:"primary",onClick:w,children:"Yes"})]})]}),(0,a.jsx)(l.A,{columns:S,data:r,totalRows:o,pagServer:!0,handlePerRowsChange:f,handlePageChange:e=>{A.limit=x,A.page=e,v()}})]})};s()}catch(e){s(e)}})},1332:e=>{e.exports=require("react-custom-scrollbars-2")},1428:e=>{e.exports=import("axios")},1680:e=>{e.exports=require("@restart/ui/utils")},1919:e=>{e.exports=require("react-transition-group/Transition")},3892:e=>{e.exports=require("classnames")},4048:e=>{e.exports=require("react-truncate")},6009:e=>{e.exports=require("dom-helpers/ownerDocument")},6952:e=>{e.exports=require("@restart/ui/Modal")},7374:e=>{e.exports=require("@restart/ui/Dropdown")},8732:e=>{e.exports=require("react/jsx-runtime")},9653:e=>{e.exports=require("@restart/ui/Overlay")},11242:e=>{e.exports=require("redux-persist/integration/react")},11688:e=>{e.exports=require("@restart/hooks/useWillUnmount")},12388:e=>{e.exports=require("dom-helpers/addEventListener")},13364:e=>{e.exports=require("redux-saga/effects")},14062:e=>{e.exports=import("react-redux")},14078:e=>{e.exports=import("swr")},14332:e=>{e.exports=require("uncontrollable")},16116:e=>{e.exports=require("invariant")},18622:e=>{e.exports=require("yup")},19121:e=>{e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},22326:e=>{e.exports=require("react-dom")},22541:e=>{e.exports=require("redux-persist/lib/storage")},25303:e=>{e.exports=require("@restart/ui/NavItem")},26324:e=>{e.exports=require("warning")},27825:e=>{e.exports=require("lodash")},27910:e=>{e.exports=require("stream")},28217:e=>{e.exports=require("@restart/ui/DropdownItem")},29021:e=>{e.exports=require("fs")},29294:e=>{e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},29628:e=>{e.exports=require("dom-helpers/addClass")},29825:e=>{e.exports=require("prop-types")},29841:e=>{e.exports=require("dom-helpers/hasClass")},30362:e=>{e.exports=require("@restart/ui/DropdownToggle")},33873:e=>{e.exports=require("path")},35576:e=>{e.exports=require("next-i18next/serverSideTranslations")},36653:e=>{e.exports=require("nprogress")},36955:e=>{e.exports=require("@restart/hooks/useBreakpoint")},37766:e=>{e.exports=require("@restart/hooks/useMergedRefs")},38609:e=>{e.exports=require("react-data-table-component")},39756:e=>{e.exports=import("redux")},40051:e=>{e.exports=require("dom-helpers/removeClass")},40361:e=>{e.exports=require("next/dist/compiled/next-server/pages.runtime.prod.js")},42893:e=>{e.exports=import("react-hot-toast")},43294:e=>{e.exports=require("formik")},50009:e=>{e.exports=require("@restart/ui/DropdownMenu")},56084:(e,r,t)=>{t.d(r,{A:()=>p});var s=t(8732);t(82015);var a=t(38609),o=t.n(a),i=t(88751),n=t(30370);function u(e){let{t:r}=(0,i.useTranslation)("common"),t={rowsPerPageText:r("Rowsperpage")},{columns:a,data:u,totalRows:p,resetPaginationToggle:l,subheader:d,subHeaderComponent:c,handlePerRowsChange:x,handlePageChange:m,rowsPerPage:h,defaultRowsPerPage:g,selectableRows:q,loading:P,pagServer:A,onSelectedRowsChange:S,clearSelectedRows:v,sortServer:f,onSort:j,persistTableHead:w,sortFunction:b,...y}=e,M={paginationComponentOptions:t,noDataComponent:r("NoData"),noHeader:!0,columns:a,data:u||[],dense:!0,paginationResetDefaultPage:l,subHeader:d,progressPending:P,subHeaderComponent:c,pagination:!0,paginationServer:A,paginationPerPage:g||10,paginationRowsPerPageOptions:h||[10,15,20,25,30],paginationTotalRows:p,onChangeRowsPerPage:x,onChangePage:m,selectableRows:q,onSelectedRowsChange:S,clearSelectedRows:v,progressComponent:(0,s.jsx)(n.A,{}),sortIcon:(0,s.jsx)("i",{className:"sort-icon fas fa-exchange-alt"}),sortServer:f,onSort:j,sortFunction:b,persistTableHead:w,className:"rki-table"};return(0,s.jsx)(o(),{...M})}u.defaultProps={subHeader:!1,pagination:!0,totalRows:null,pagServer:!0,onSelectedRowsChange:null,clearSelectedRows:!1,sortServer:!1,persistTableHead:!1};let p=u},57664:e=>{e.exports=require("dom-helpers/scrollbarSize")},58928:e=>{e.exports=require("dom-helpers/canUseDOM")},59717:e=>{e.exports=require("@restart/ui/DropdownContext")},60560:e=>{e.exports=require("dom-helpers/contains")},63033:e=>{e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},65447:e=>{e.exports=require("@restart/ui/Button")},67364:e=>{e.exports=require("@restart/hooks/useCallbackRef")},68455:e=>{e.exports=import("redux-saga")},69722:e=>{e.exports=require("es6-promise")},72589:(e,r,t)=>{t.a(e,async(e,s)=>{try{t.r(r),t.d(r,{default:()=>h,getStaticProps:()=>m});var a=t(8732);t(82015);var o=t(19918),i=t.n(o),n=t(7082),u=t(83551),p=t(49481),l=t(91353),d=t(283),c=t(35576),x=e([d]);async function m({locale:e}){return{props:{...await (0,c.serverSideTranslations)(e,["common"])}}}d=(x.then?(await x)():x)[0];let h=()=>(0,a.jsxs)(n.A,{className:"users-page",children:[(0,a.jsx)(u.A,{className:"page-header",children:(0,a.jsx)("h4",{children:"User List"})}),(0,a.jsx)(u.A,{children:(0,a.jsx)(p.A,{xs:12,children:(0,a.jsx)(i(),{href:"/users/[...routes]",as:"/users/create",children:(0,a.jsx)(l.A,{variant:"secondary",size:"sm",children:"Add Users"})})})}),(0,a.jsx)(u.A,{className:"mt-3",children:(0,a.jsx)(p.A,{xs:12,children:(0,a.jsx)(d.default,{})})})]});s()}catch(e){s(e)}})},73060:(e,r,t)=>{t.a(e,async(e,s)=>{try{t.r(r),t.d(r,{config:()=>g,default:()=>c,getServerSideProps:()=>h,getStaticPaths:()=>m,getStaticProps:()=>x,reportWebVitals:()=>q,routeModule:()=>j,unstable_getServerProps:()=>v,unstable_getServerSideProps:()=>f,unstable_getStaticParams:()=>S,unstable_getStaticPaths:()=>A,unstable_getStaticProps:()=>P});var a=t(63885),o=t(80237),i=t(81413),n=t(9616),u=t.n(n),p=t(72386),l=t(72589),d=e([p,l]);[p,l]=d.then?(await d)():d;let c=(0,i.M)(l,"default"),x=(0,i.M)(l,"getStaticProps"),m=(0,i.M)(l,"getStaticPaths"),h=(0,i.M)(l,"getServerSideProps"),g=(0,i.M)(l,"config"),q=(0,i.M)(l,"reportWebVitals"),P=(0,i.M)(l,"unstable_getStaticProps"),A=(0,i.M)(l,"unstable_getStaticPaths"),S=(0,i.M)(l,"unstable_getStaticParams"),v=(0,i.M)(l,"unstable_getServerProps"),f=(0,i.M)(l,"unstable_getServerSideProps"),j=new a.PagesRouteModule({definition:{kind:o.A.PAGES,page:"/users",pathname:"/users",bundlePath:"",filename:""},components:{App:p.default,Document:u()},userland:l});s()}catch(e){s(e)}})},74075:e=>{e.exports=require("zlib")},74716:e=>{e.exports=require("moment")},74987:e=>{e.exports=require("@restart/ui/ModalManager")},78097:e=>{e.exports=require("next-redux-saga")},78634:e=>{e.exports=require("@restart/ui/Anchor")},80237:(e,r)=>{Object.defineProperty(r,"A",{enumerable:!0,get:function(){return t}});var t=function(e){return e.PAGES="PAGES",e.PAGES_API="PAGES_API",e.APP_PAGE="APP_PAGE",e.APP_ROUTE="APP_ROUTE",e.IMAGE="IMAGE",e}({})},81366:e=>{e.exports=require("redux-auth-wrapper/connectedAuthWrapper")},81413:(e,r)=>{Object.defineProperty(r,"M",{enumerable:!0,get:function(){return function e(r,t){return t in r?r[t]:"then"in r&&"function"==typeof r.then?r.then(r=>e(r,t)):"function"==typeof r&&"default"===t?r:void 0}}})},81521:e=>{e.exports=require("dom-helpers/querySelectorAll")},81895:e=>{e.exports=require("@restart/hooks/useEventCallback")},82015:e=>{e.exports=require("react")},86842:e=>{e.exports=require("@restart/ui/SelectableContext")},87571:e=>{e.exports=require("dom-helpers/transitionEnd")},88751:e=>{e.exports=require("next-i18next")},93787:e=>{e.exports=require("redux-persist")},94947:e=>{e.exports=require("@restart/hooks/useTimeout")},96196:e=>{e.exports=require("next-redux-wrapper")},98320:e=>{e.exports=require("@restart/hooks/useIsomorphicEffect")},99460:e=>{e.exports=require("dom-helpers/css")}};var r=require("../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[6089,9216,9616,2386],()=>t(73060));module.exports=s})();