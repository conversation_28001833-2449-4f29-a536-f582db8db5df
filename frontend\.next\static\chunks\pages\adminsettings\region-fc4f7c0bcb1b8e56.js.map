{"version": 3, "file": "static/chunks/pages/adminsettings/region-fc4f7c0bcb1b8e56.js", "mappings": "0qBAGA,IAAMA,EAAS,aACFC,EAAmBC,CAAAA,EAAAA,EAAAA,CAAAA,CAAoBA,CAAC,CACnDC,sBAAuB,KACjBC,EAAMC,WAAW,IAAID,EAAMC,WAAW,CAACC,YAAY,IAAIF,EAAMC,WAAW,CAACC,YAAY,CAACN,EAAO,CAKnGO,CALqG,kBAKjF,kBACtB,GAAG,EAE0BL,CAAAA,EAAAA,EAAAA,CAAAA,CAAoBA,CAAC,CAChDC,sBAAuB,GACjBC,IAAMC,WAAW,IAAID,EAAMC,WAAW,CAACG,OAAO,IAAIJ,EAAMC,WAAW,CAACG,OAAO,CAACR,EAAO,CAKzFO,CAL2F,kBAKvE,eACtB,GAEaE,EAAyBP,CAAAA,EAAAA,EAAAA,CAAAA,CAAoBA,CAAC,CACzDC,sBAAuB,KACjBC,EAAMC,WAAW,IAAID,EAAMC,WAAW,CAACK,iBAAiB,IAAIN,EAAMC,WAAW,CAACK,iBAAiB,CAACV,EAAO,CAK7GO,CAL+G,kBAK3F,wBACtB,GAAG,EAE8BL,CAAAA,EAAAA,EAAAA,CAAAA,CAAoBA,CAAC,CACpDC,sBAAuB,KACjBC,EAAMC,WAAW,IAAID,EAAMC,WAAW,CAACM,YAAY,IAAIP,EAAMC,WAAW,CAACM,YAAY,CAACX,EAAO,CAKnGO,CALqG,kBAKjF,mBACtB,GAAG,EAE4BL,CAAAA,EAAAA,EAAAA,CAAAA,CAAoBA,CAAC,CAClDC,sBAAuB,KACjBC,EAAMC,WAAW,IAAID,EAAMC,WAAW,CAACO,SAAS,IAAIR,EAAMC,WAAW,CAACO,SAAS,CAACZ,EAAO,CAK7FO,CAL+F,kBAK3E,iBACtB,GAAG,EAEqCL,CAAAA,EAAAA,EAAAA,CAAAA,CAAoBA,CAAC,CAC3DC,sBAAuB,KACjBC,EAAMC,WAAW,IAAID,EAAMC,WAAW,CAACQ,uBAAuB,IAAIT,EAAMC,WAAW,CAACQ,uBAAuB,CAACb,EAAO,CAKzHO,CAL2H,kBAKvG,0BACtB,GAAG,EAEiCL,CAAAA,EAAAA,EAAAA,CAAAA,CAAoBA,CAAC,CACvDC,sBAAuB,KACjBC,EAAMC,WAAW,IAAID,EAAMC,WAAW,CAACQ,uBAAuB,IAAIT,EAAMC,WAAW,CAACQ,uBAAuB,CAACb,EAAO,CAKzHO,CAL2H,kBAKvG,sBACtB,GAAG,EAC0BL,CAAAA,EAAAA,EAAAA,CAAAA,CAAoBA,CAAC,CAChDC,sBAAuB,KACjBC,EAAMC,WAAW,IAAID,EAAMC,WAAW,CAACS,MAAM,IAAIV,EAAMC,WAAW,CAACS,MAAM,CAACd,EAAO,CAKvFO,CALyF,kBAKrE,eACtB,GAAG,EAE8BL,CAAAA,EAAAA,EAAAA,CAAAA,CAAoBA,CAAC,CACpDC,sBAAuB,KACjBC,EAAMC,WAAW,IAAID,EAAMC,WAAW,CAACU,WAAW,IAAIX,EAAMC,WAAW,CAACU,WAAW,CAACf,EAAO,CAKjGO,CALmG,kBAK/E,mBACtB,GAAG,EAEuCL,CAAAA,EAAAA,EAAAA,CAAAA,CAAoBA,CAAC,CAC7DC,sBAAuB,KACjBC,EAAMC,WAAW,IAAID,EAAMC,WAAW,CAACW,WAAW,IAAIZ,EAAMC,WAAW,CAACW,WAAW,CAAChB,EAAO,CAKjGO,CALmG,kBAK/E,4BACtB,GAAG,EAEuCL,CAAAA,EAAAA,EAAAA,CAAAA,CAAoBA,CAAC,CAC7DC,sBAAuB,GACjBC,IAAMC,WAAW,IAAID,EAAMC,WAAW,CAACY,mBAAmB,IAAIb,EAAMC,WAAW,CAACY,mBAAmB,CAACjB,EAAO,CAKjHO,CALmH,kBAK/F,4BACtB,GAAG,EAEoCL,CAAAA,EAAAA,EAAAA,CAAAA,CAAoBA,CAAC,CAC1DC,sBAAuB,KACjBC,EAAMC,WAAW,IAAID,EAAMC,WAAW,CAACa,gBAAgB,IAAId,EAAMC,WAAW,CAACa,gBAAgB,CAAClB,EAAO,CAK3GO,CAL6G,kBAKzF,yBACtB,GAEaY,EAAwBjB,CAAAA,EAAAA,EAAAA,CAAAA,CAAoBA,CAAC,CACxDC,sBAAuB,KACjBC,EAAMC,WAAW,IAAID,EAAMC,WAAW,CAACe,gBAAgB,IAAIhB,EAAMC,WAAW,CAACe,gBAAgB,CAACpB,EAAO,CAK3GO,CAL6G,kBAKzF,uBACtB,GAAG,EAEgCL,CAAAA,EAAAA,EAAAA,CAAAA,CAAoBA,CAAC,CACtDC,sBAAuB,KACjBC,EAAMC,WAAW,IAAID,EAAMC,WAAW,CAACgB,cAAc,IAAIjB,EAAMC,WAAW,CAACgB,cAAc,CAACrB,EAAO,CAKvGO,CALyG,kBAKrF,qBACtB,GAAG,EAE0BL,CAAAA,EAAAA,EAAAA,CAAAA,CAAoBA,CAAC,CAChDC,sBAAuB,KACjBC,EAAMC,WAAW,IAAID,EAAMC,WAAW,CAACiB,MAAM,IAAIlB,EAAMC,WAAW,CAACiB,MAAM,CAACtB,EAAO,CAKvFO,CALyF,kBAKrE,eACtB,GAAG,EAE6BL,CAAAA,EAAAA,EAAAA,CAAAA,CAAoBA,CAAC,CACnDC,sBAAuB,GACjBC,IAAMC,WAAW,IAAID,EAAMC,WAAW,CAACkB,UAAU,IAAInB,EAAMC,WAAW,CAACkB,UAAU,CAACvB,EAAO,CAK/FO,CALiG,kBAK7E,kBACtB,GAAG,EAE4BL,CAAAA,EAAAA,EAAAA,CAAAA,CAAoBA,CAAC,CAClDC,sBAAuB,KACjBC,EAAMC,WAAW,IAAID,EAAMC,WAAW,CAACmB,QAAQ,IAAIpB,EAAMC,WAAW,CAACmB,QAAQ,CAACxB,EAAO,CAK3FO,CAL6F,kBAKzE,iBACtB,GAAG,EAE8BL,CAAAA,EAAAA,EAAAA,CAAAA,CAAoBA,CAAC,CACpDC,sBAAuB,KACjBC,EAAMC,WAAW,IAAID,EAAMC,WAAW,CAACoB,WAAW,IAAIrB,EAAMC,WAAW,CAACoB,WAAW,CAACzB,EAAO,CAKjGO,CALmG,kBAK/E,mBACtB,GAAG,EAEwBL,CAAAA,EAAAA,EAAAA,CAAAA,CAAoBA,CAAC,CAC9CC,sBAAwBC,KAClBA,EAAMC,WAAW,IAAID,EAAMC,WAAW,CAACqB,KAAK,IAAItB,EAAMC,WAAW,CAACqB,KAAK,CAAC1B,EAAO,CAKrFO,CALuF,kBAKnE,aACtB,GAAG,EAE8BL,CAAAA,EAAAA,EAAAA,CAAAA,CAAoBA,CAAC,CACpDC,sBAAuB,KACjBC,EAAMC,WAAW,IAAID,EAAMC,WAAW,CAACsB,WAAW,IAAIvB,EAAMC,WAAW,CAACsB,WAAW,CAAC3B,EAAO,CAKjGO,CALmG,kBAK/E,mBACtB,GAAG,EAE8BL,CAAAA,EAAAA,EAAAA,CAAAA,CAAoBA,CAAC,CACpDC,sBAAuB,KACjBC,EAAMC,WAAW,IAAID,EAAMC,WAAW,CAACuB,YAAY,IAAIxB,EAAMC,WAAW,CAACuB,YAAY,CAAC5B,EAAO,CAKnGO,CALqG,kBAKjF,mBACtB,GAAG,EAE0BL,CAAAA,EAAAA,EAAAA,CAAAA,CAAoBA,CAAC,CAChDC,sBAAwBC,GACtB,EAAIA,EAAMC,WAAW,IAAID,EAAMC,WAAW,CAACwB,SAAS,IAAIzB,EAAMC,WAAW,CAACwB,SAAS,CAAC7B,EAAO,IAAII,EAAMC,WAAW,CAACyB,OAAO,IAAI1B,EAAMC,WAAW,CAACyB,OAAO,CAAC9B,EAAO,IAAGI,EAAMC,WAAW,CAAC0B,KAAK,IAAI3B,EAAMC,WAAW,CAAC0B,KAAK,CAAC/B,EAAO,IAAGI,EAAMC,WAAW,CAAC2B,MAAM,IAAI5B,EAAMC,WAAW,CAAC2B,MAAM,CAAChC,EAAO,IAAGI,EAAMC,WAAW,CAACW,WAAW,IAAIZ,EAAMC,WAAW,CAACW,WAAW,CAAChB,EAAO,IAAGI,EAAMC,WAAW,CAAC4B,MAAM,IAAI7B,EAAMC,WAAW,CAAC4B,MAAM,CAACjC,EAAO,EAAE,CAG5Z,EAETO,mBAAoB,eACtB,GAAG,EAEYN,gBAAgBA,EAAC,2FC1LhC,SAASiC,EAASC,CAAoB,EACpC,GAAM,CAAEC,GAAC,CAAE,CAAGC,CAAAA,EAAAA,EAAAA,EAAAA,CAAcA,CAAC,UACvBC,EAA6B,CACjCC,gBAAiBH,EAAE,cACnB,EACI,SACJI,CAAO,MACPC,CAAI,WACJC,CAAS,CACTC,uBAAqB,WACrBC,CAAS,oBACTC,CAAkB,qBAClBC,CAAmB,kBACnBC,CAAgB,aAChBC,CAAW,oBACXC,CAAkB,gBAClBC,CAAc,SACdC,CAAO,CACPC,WAAS,sBACTC,CAAoB,mBACpBC,CAAiB,YACjBC,CAAU,QACVC,CAAM,kBACNC,CAAgB,cAChBC,CAAY,CAEZ,CADA,EACGC,EACJ,CAAGxB,EAGEyB,EAAiB,4BACrBtB,EACAuB,gBAAiBzB,EAAE,IAP0C,MAQ7D0B,UAAU,UACVtB,EACAC,KAAMA,GAAQ,EAAE,CAChBsB,OAAO,EACPC,2BAA4BrB,EAC5BsB,UAAWrB,EACXsB,gBAAiBf,qBACjBN,EACAsB,YAAY,EACZC,iBAAkBhB,EAClBiB,kBAAmBpB,GAA0C,GAC7DqB,eADwCrB,cACVD,GAA4B,CAAC,GAAI,GAAI,GAAI,CAA3BA,EAA+B,GAAG,CAC9EuB,oBAAqB7B,EACrB8B,oBAAqB1B,EACrB2B,aAAc1B,iBACdG,uBACAG,oBACAC,EACAoB,kBAAmB,UAACC,EAAAA,CAAYA,CAAAA,CAAAA,GAChCC,SAAU,UAACC,IAAAA,CAAEC,UAAU,6CACvBvB,SACAC,EACAE,gCACAD,EACAqB,UAAW,WACb,EACA,MACE,UAACC,EAAAA,EAASA,CAAAA,CAAE,GAAGnB,CAAc,EAEjC,CAEA1B,EAAS8C,YAAY,CAAG,CACtBf,WAAW,EACXE,YAAY,EACZzB,UAAW,KACXU,WAAW,EACXC,qBAAsB,KACtBC,mBAAmB,EACnBC,YAAY,EACZE,kBAAkB,CACpB,EAEA,MAAevB,QAAQA,EAAC,mEChHT,SAAS+C,EAAgBC,CAAW,EAC/C,MACE,UAACC,MAAAA,CAAIL,UAAU,sDACb,UAACK,MAAAA,CAAIL,UAAU,mBAAU,yCAG/B,mBCLF,4CACA,wBACA,WACA,OAAe,EAAQ,KAAmD,CAC1E,EACA,SAFsB,gLCqJtB,MA5IoB,IAChB,GAAM,GAAE1C,CAAC,CAAE,CAAGC,CAAAA,EAAAA,EAAAA,EAAAA,CAAcA,CAAC,MA2IlB+C,IA1IL,CAACC,EAAWC,EAAe,CAAGC,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAC,EAAE,EACzC,EAAGC,EAAW,CAAGD,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,EAAC,GAC1B,CAAC7C,EAAW+C,EAAa,CAAGF,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAC,GACrC,CAACG,EAASC,EAAW,CAAGJ,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAC,IACjC,CAACK,EAAaC,EAAS,CAAGN,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,EAAC,GACnC,CAACO,EAAqBC,EAAgB,CAAGR,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAC,CAAC,GACnD,CAACS,EAAWC,EAAa,CAAQV,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAC,IAE1CW,EAAe,CACjBC,KAAM,CAAEC,MAAO,KAAM,EACrBC,MAAOX,EACPY,KAAM,EACNC,MAAO,CAAC,CACZ,EAEM/D,EAAU,CACZ,CACIgE,KAAMpE,EAAE,+BACRqE,SAAU,GAAcC,EAAIN,KAAK,CACjCO,UAAU,CACd,EACA,CACIH,KAAMpE,EAAE,gCACRqE,SAAU,QAAcC,QAAAA,CAAAA,OAAAA,EAAAA,EAAIlG,OAAAA,EAAJkG,KAAAA,EAAAA,EAAaN,GAAbM,EAAaN,GAAS,IAC9CO,UAAU,EACVC,KAAM,GAAaC,EAAErG,OAAO,EAAIqG,EAAErG,OAAO,CAAC4F,KAAK,CAAGS,EAAErG,OAAO,CAAC4F,KAAK,CAAG,EACxE,EACA,CACII,KAAMpE,EAAE,+BACRqE,SAAWC,GAAaA,EAAII,GAAG,CAC/BH,UAAU,EACVC,KAAOC,GACH,WAAC1B,MAAAA,WACG,UAAC4B,IAAIA,CAACC,KAAK,6BAA6BC,GAAI,OAAvCF,uBAA2E,OAANF,EAAEC,GAAG,WAE3E,UAACjC,IAAAA,CAAEC,UAAU,uBAEV,OAEP,UAACoC,IAAAA,CAAEC,QAAS,IAAMC,EAAWP,YACzB,UAAChC,IAAAA,CAAEC,UAAU,4BACZ,MAGjB,EACH,CAEKuC,EAAgB,UAClB7B,GAAW,GACX,IAAM8B,EAAW,MAAMC,EAAAA,CAAUA,CAACC,GAAG,CAAC,UAAWtB,GAC7CoB,GAAYA,EAAS7E,IAAI,EAAE,CAC3B6C,EAAegC,EAAS7E,IAAI,EAC5BgD,EAAa6B,EAASG,UAAU,EAChCjC,GAAW,GAEnB,EASM1C,EAAsB,MAAO4E,EAAiBpB,KAChDJ,EAAaG,KAAK,CAAGqB,EACrBxB,EAAaI,IAAI,CAAGA,EACpBN,IAAcE,EAAaK,KAAK,CAAG,CAArBL,QAAgCF,EAAU2B,KAAK,CAAC,EAC9DnC,GAAW,GACX,IAAM8B,EAAW,MAAMC,EAAAA,CAAUA,CAACC,GAAG,CAAC,UAAWtB,GAC7CoB,GAAYA,EAAS7E,IAAI,EAAI6E,EAAS7E,IAAI,CAACmF,MAAM,CAAG,GAAG,CACvDtC,EAAegC,EAAS7E,IAAI,EAC5BkD,EAAW+B,GACXlC,GAAW,GAEnB,EAEM4B,EAAa,MAAOV,IACtBX,EAAgBW,EAAII,GAAG,EACvBjB,GAAS,EACb,EAEMgC,EAAe,UACjB,GAAI,CACA,MAAMN,EAAAA,CAAUA,CAACO,MAAM,CAAC,WAA+B,OAApBhC,IACnCuB,IACAxB,GAAS,GACTkC,EAAAA,EAAKA,CAACC,OAAO,CAAC5F,EAAE,wDACpB,CAAE,MAAO6F,EAAO,CACZF,EAAAA,EAAKA,CAACE,KAAK,CAAC7F,EAAE,kDAClB,CACJ,EAEM8F,EAAY,IAAMrC,EAAS,IAgBjC,MATAsC,CAAAA,EAAAA,EAAAA,SAAAA,CAASA,CAAC,KACNd,GACJ,EAAG,EAAE,EAELc,CAAAA,EAAAA,EAAAA,SAAAA,CAASA,CAAC,KACNnC,IAAcE,EAAaK,KAAK,CAAG,CAArBL,QAAgCF,EAAU2B,KAAK,CAAC,EAC9DN,GACJ,EAAG,CAACrB,EAAU,EAGV,WAACb,MAAAA,CAAIL,UAAU,0BACX,WAACsD,EAAAA,CAAKA,CAAAA,CAACC,KAAMzC,EAAa0C,OAAQJ,YAC9B,UAACE,EAAAA,CAAKA,CAACG,MAAM,EAACC,WAAW,aACrB,UAACJ,EAAAA,CAAKA,CAACK,KAAK,WAAErG,EAAE,yCAEpB,UAACgG,EAAAA,CAAKA,CAACM,IAAI,WAAEtG,EAAE,4DACf,WAACgG,EAAAA,CAAKA,CAACO,MAAM,YACT,UAACC,EAAAA,CAAMA,CAAAA,CAACC,QAAQ,YAAY1B,QAASe,WAChC9F,EAAE,iCAEP,UAACwG,EAAAA,CAAMA,CAAAA,CAACC,QAAQ,UAAU1B,QAASU,WAC9BzF,EAAE,oCAIf,UAAC0G,EAAAA,OAAiBA,CAAAA,CAACC,eA7BL,CA6BqBC,GA5BvC/C,EAAazF,EACjB,EA2B0DmH,MAAO3B,IACzD,UAAC9D,EAAAA,CAAQA,CAAAA,CACLM,QAASA,EACTC,KAAM4C,EACN3C,UAAWA,EACXU,UAAW,GACXN,oBAAqBA,EACrBC,iBA3Ea,CA2EKA,GA1E1BmD,EAAaG,KAAK,CAAGX,EACrBQ,EAAaI,IAAI,CAAGA,EACpBN,GAAcE,GAAaK,KAAK,CAAG,CAArBL,QAAgCF,EAAU2B,KAAK,CAAC,EAC9DN,GACJ,MA0EJ,gECnJe,SAAS4B,EAAY9G,CAAuB,EACzD,MACE,UAAC+G,KAAAA,CAAGpE,UAAU,wBAAgB3C,EAAMiE,KAAK,EAE7C,kOCsDA,MAjDoB,QAiCZhG,EAAAA,EAhCN,GAAM,GAAEgC,CAAC,CAAE,CAAGC,CAAAA,EAAAA,EAAAA,EAAAA,CAAcA,CAAC,SAgDL8G,CA/ClBC,CA+CmB,CA/CF,IAEnB,WAACC,EAAAA,CAASA,CAAAA,CAACC,MAAO,CAAEC,SAAU,QAAS,EAAGC,KAAK,IAAC1E,UAAU,gBACxD,UAAC2E,EAAAA,CAAGA,CAAAA,UACF,UAACC,EAAAA,CAAGA,CAAAA,CAACC,GAAI,YACP,UAACV,EAAAA,CAAWA,CAAAA,CAAC7C,MAAOhE,EAAE,sCAG1B,UAACqH,EAAAA,CAAGA,CAAAA,UACF,UAACC,EAAAA,CAAGA,CAAAA,CAACC,GAAI,YACP,UAAC5C,IAAIA,CACHC,KAAK,6BACLC,GAAG,OAFAF,iCAIH,UAAC6B,EAAAA,CAAMA,CAAAA,CAACC,QAAQ,YAAYe,KAAK,cAC9BxH,EAAE,0CAKX,UAACqH,EAAAA,CAAGA,CAAAA,CAAC3E,UAAU,gBACb,UAAC4E,EAAAA,CAAGA,CAAAA,CAACC,GAAI,YACP,UAACvE,EAAAA,OAAWA,CAAAA,CAAAA,UAOhByE,EAAiBC,CAAAA,EAAAA,EAAAA,aAAAA,CAAaA,CAAC,IAAM,UAACV,EAAAA,CAAAA,IACtChJ,EAAY2J,CAAAA,EAAAA,EAAAA,EAAAA,CAAWA,CAAC,GAAW3J,SACzC,IAAI,GAAEA,GAAAA,OAAAA,EAAAA,EAAOC,IAAPD,OAAOC,GAAPD,OAAAA,EAAAA,EAAAA,MAAoBkB,EAApBlB,KAAAA,EAAAA,CAA4B,CAAC,GAA7BA,UAA0C,EAI9C,CAJiD,EAIjD,OAACyJ,EAAAA,CAAAA,GAHM,UAAC5E,EAAAA,OAAeA,CAAAA,CAAAA,EAK3B,yJCLA,MArC0B,OAAC,gBAAE8D,CAAc,IAqC5BD,GArC8BnB,CAAK,CAAO,GACjD,GAAEvF,CAAC,KAoCqB0G,CApCpBkB,CAAI,CAAE,CAAG3H,CAAAA,EAAAA,EAAAA,EAAAA,CAAcA,CAAC,UAC5B4H,EAAgC,OAAlBD,EAAKE,QAAQ,CAAW,CAACC,SAAU,KAAK,EAAI,CAAC/D,MAAO,KAAK,EACvEgE,EAAcJ,EAAKE,QAAQ,CAC3B,CAACG,EAAWC,EAAa,CAAG/E,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAQ,EAAE,EAC9CgF,EAAgB,CACpBpE,KAAM8D,EACN5D,MAAO,IACPmE,aAAaJ,CACf,EAWA,MATAjC,CAAAA,EAAAA,EAAAA,SAAAA,CAASA,CAAC,KAORsC,CANuB,UACrB,IAAMnD,EAAW,MAAMC,EAAAA,CAAUA,CAACC,GAAG,CAAC,WAAY+C,GAC9CjD,GAAYA,EAAS7E,IAAI,EAAI6E,EAAS7E,IAAI,CAACmF,MAAM,CAAG,GACtD0C,EAAahD,EAAS7E,IAAI,EAE9B,GAEF,EAAG,EAAE,EAEH,UAAC4G,EAAAA,CAASA,CAAAA,CAACG,KAAK,aACd,UAACC,EAAAA,CAAGA,CAAAA,UACF,UAACC,EAAAA,CAAGA,CAAAA,CAACgB,GAAI,EAAG5F,UAAU,gBACpB,UAAC6F,EAAAA,EAAMA,CAAAA,CACLhD,MAAO,CAACA,EAAM,CACdiD,YAAaxI,EAAE,sCACfyI,aAAa,EACbC,SAAU/B,EACVgC,QAASV,EAAUzC,MAAM,CAAG,EAAIyC,EAAUW,GAAG,CAAC,CAACC,EAAMC,IAAQ,EAAEvD,MAAOsD,EAAKnE,GAAG,CAAEqE,MAAOF,EAAK7E,KAAK,CAAC,GAAM,EAAE,QAMtH", "sources": ["webpack://_N_E/./pages/adminsettings/permissions.tsx", "webpack://_N_E/./components/common/RKITable.tsx", "webpack://_N_E/./pages/rNoAccess.tsx", "webpack://_N_E/?31a1", "webpack://_N_E/./pages/adminsettings/region/regionTable.tsx", "webpack://_N_E/./components/common/PageHeading.tsx", "webpack://_N_E/./pages/adminsettings/region/index.tsx", "webpack://_N_E/./pages/adminsettings/region/regionTableFilter.tsx"], "sourcesContent": ["//Import services/components\r\nimport connectedAuthWrapper from 'redux-auth-wrapper/connectedAuthWrapper';\r\n\r\nconst create = \"create:any\";\r\nexport const canAddAreaOfWork = connectedAuthWrapper({\r\n  authenticatedSelector: (state) => {\r\n    if (state.permissions && state.permissions.area_of_work && state.permissions.area_of_work[create]) {\r\n      return true;\r\n    }\r\n    return false;\r\n  },\r\n  wrapperDisplayName: 'CanAddAreaOfWork',\r\n});\r\n\r\nexport const canAddCountry = connectedAuthWrapper({\r\n  authenticatedSelector: (state) => {\r\n    if (state.permissions && state.permissions.country && state.permissions.country[create]) {\r\n      return true;\r\n    }\r\n    return false;\r\n  },\r\n  wrapperDisplayName: 'CanAddCountry',\r\n});\r\n\r\nexport const canAddDeploymentStatus = connectedAuthWrapper({\r\n  authenticatedSelector: (state) => {\r\n    if (state.permissions && state.permissions.deployment_status && state.permissions.deployment_status[create]) {\r\n      return true;\r\n    }\r\n    return false;\r\n  },\r\n  wrapperDisplayName: 'CanAddDeploymentStatus',\r\n});\r\n\r\nexport const canAddEventStatus = connectedAuthWrapper({\r\n  authenticatedSelector: (state) => {\r\n    if (state.permissions && state.permissions.event_status && state.permissions.event_status[create]) {\r\n      return true;\r\n    }\r\n    return false;\r\n  },\r\n  wrapperDisplayName: 'CanAddEventStatus',\r\n});\r\n\r\nexport const canAddExpertise = connectedAuthWrapper({\r\n  authenticatedSelector: (state) => {\r\n    if (state.permissions && state.permissions.expertise && state.permissions.expertise[create]) {\r\n      return true;\r\n    }\r\n    return false;\r\n  },\r\n  wrapperDisplayName: 'CanAddExpertise',\r\n});\r\n\r\nexport const canAddFocalPointApproval = connectedAuthWrapper({\r\n  authenticatedSelector: (state) => {\r\n    if (state.permissions && state.permissions.institution_focal_point && state.permissions.institution_focal_point[create]) {\r\n      return true;\r\n    }\r\n    return false;\r\n  },\r\n  wrapperDisplayName: 'CanAddFocalPointApproval',\r\n});\r\n\r\nexport const canAddVspaceApproval = connectedAuthWrapper({\r\n  authenticatedSelector: (state) => {\r\n    if (state.permissions && state.permissions.institution_focal_point && state.permissions.institution_focal_point[create]) {\r\n      return true;\r\n    }\r\n    return false;\r\n  },\r\n  wrapperDisplayName: 'CanAddVspaceApproval',\r\n});\r\nexport const canAddHazards = connectedAuthWrapper({\r\n  authenticatedSelector: (state) => {\r\n    if (state.permissions && state.permissions.hazard && state.permissions.hazard[create]) {\r\n      return true;\r\n    }\r\n    return false;\r\n  },\r\n  wrapperDisplayName: 'CanAddHazards',\r\n});\r\n\r\nexport const canAddHazardTypes = connectedAuthWrapper({\r\n  authenticatedSelector: (state) => {\r\n    if (state.permissions && state.permissions.hazard_type && state.permissions.hazard_type[create]) {\r\n      return true;\r\n    }\r\n    return false;\r\n  },\r\n  wrapperDisplayName: 'CanAddHazardTypes',\r\n}); \r\n\r\nexport const canAddOrganisationApproval = connectedAuthWrapper({\r\n  authenticatedSelector: (state) => {\r\n    if (state.permissions && state.permissions.institution && state.permissions.institution[create]) {\r\n      return true;\r\n    }\r\n    return false;\r\n  },\r\n  wrapperDisplayName: 'CanAddOrganisationApproval',\r\n}); \r\n\r\nexport const canAddOrganisationNetworks = connectedAuthWrapper({\r\n  authenticatedSelector: (state) => {\r\n    if (state.permissions && state.permissions.institution_network && state.permissions.institution_network[create]) {\r\n      return true;\r\n    }\r\n    return false;\r\n  },\r\n  wrapperDisplayName: 'CanAddOrganisationNetworks',\r\n});\r\n\r\nexport const canAddOrganisationTypes = connectedAuthWrapper({\r\n  authenticatedSelector: (state) => {\r\n    if (state.permissions && state.permissions.institution_type && state.permissions.institution_type[create]) {\r\n      return true;\r\n    }\r\n    return false;\r\n  },\r\n  wrapperDisplayName: 'CanAddOrganisationTypes',\r\n});\r\n\r\nexport const canAddOperationStatus = connectedAuthWrapper({\r\n  authenticatedSelector: (state) => {\r\n    if (state.permissions && state.permissions.operation_status && state.permissions.operation_status[create]) {\r\n      return true;\r\n    }\r\n    return false;\r\n  },\r\n  wrapperDisplayName: 'CanAddOperationStatus',\r\n});\r\n\r\nexport const canAddProjectStatus = connectedAuthWrapper({\r\n  authenticatedSelector: (state) => {\r\n    if (state.permissions && state.permissions.project_status && state.permissions.project_status[create]) {\r\n      return true;\r\n    }\r\n    return false;\r\n  },\r\n  wrapperDisplayName: 'CanAddProjectStatus',\r\n});\r\n\r\nexport const canAddRegions = connectedAuthWrapper({\r\n  authenticatedSelector: (state) => {\r\n    if (state.permissions && state.permissions.region && state.permissions.region[create]) {\r\n      return true;\r\n    }\r\n    return false;\r\n  },\r\n  wrapperDisplayName: 'CanAddRegions',\r\n});\r\n\r\nexport const canAddRiskLevels = connectedAuthWrapper({\r\n  authenticatedSelector: (state) => {\r\n    if (state.permissions && state.permissions.risk_level && state.permissions.risk_level[create]) {\r\n      return true;\r\n    }\r\n    return false;\r\n  },\r\n  wrapperDisplayName: 'CanAddRiskLevels',\r\n});\r\n\r\nexport const canAddSyndromes = connectedAuthWrapper({\r\n  authenticatedSelector: (state) => {\r\n    if (state.permissions && state.permissions.syndrome && state.permissions.syndrome[create]) {\r\n      return true;\r\n    }\r\n    return false;\r\n  },\r\n  wrapperDisplayName: 'CanAddSyndromes',\r\n});\r\n\r\nexport const canAddUpdateTypes = connectedAuthWrapper({\r\n  authenticatedSelector: (state) => {\r\n    if (state.permissions && state.permissions.update_type && state.permissions.update_type[create]) {\r\n      return true;\r\n    }\r\n    return false;\r\n  },\r\n  wrapperDisplayName: 'CanAddUpdateTypes',\r\n});\r\n\r\nexport const canAddUsers = connectedAuthWrapper({\r\n  authenticatedSelector: (state) => {\r\n    if (state.permissions && state.permissions.users && state.permissions.users[create]) {\r\n      return true;\r\n    }\r\n    return false;\r\n  },\r\n  wrapperDisplayName: 'CanAddUsers',\r\n});\r\n\r\nexport const canAddWorldRegion = connectedAuthWrapper({\r\n  authenticatedSelector: (state) => {\r\n    if (state.permissions && state.permissions.worl_region && state.permissions.worl_region[create]) {\r\n      return true;\r\n    }\r\n    return false;\r\n  },\r\n  wrapperDisplayName: 'CanAddWorldRegion',\r\n});\r\n\r\nexport const canAddLandingPage = connectedAuthWrapper({\r\n  authenticatedSelector: (state) => {\r\n    if (state.permissions && state.permissions.landing_page && state.permissions.landing_page[create]) {\r\n      return true;\r\n    }\r\n    return false;\r\n  },\r\n  wrapperDisplayName: 'CanAddLandingPage',\r\n});\r\n\r\nexport const canAddContent = connectedAuthWrapper({\r\n  authenticatedSelector: (state) => {\r\n    if (state.permissions && state.permissions.operation && state.permissions.operation[create] && state.permissions.project && state.permissions.project[create]&& state.permissions.event && state.permissions.event[create]&& state.permissions.vspace && state.permissions.vspace[create]&& state.permissions.institution && state.permissions.institution[create]&& state.permissions.update && state.permissions.update[create]) {\r\n      return true;\r\n    }\r\n    return false;\r\n  },\r\n  wrapperDisplayName: 'CanAddContent',\r\n});\r\n\r\nexport default canAddAreaOfWork;", "//Import Library\r\nimport React from 'react'\r\nimport DataTable  from 'react-data-table-component';\r\n\r\n//Import services/components\r\nimport { useTranslation } from 'next-i18next';\r\nimport CustomLoader from './CustomLoader';\r\n\r\ninterface RKITableProps {\r\n  columns: any[];\r\n  data: any[];\r\n  totalRows?: number;\r\n  resetPaginationToggle?: boolean;\r\n  subheader?: boolean;\r\n  subHeaderComponent?: React.ReactNode;\r\n  handlePerRowsChange?: (newPerPage: number, page: number) => void;\r\n  handlePageChange?: (page: number) => void;\r\n  rowsPerPage?: number[];\r\n  defaultRowsPerPage?: number;\r\n  selectableRows?: boolean;\r\n  loading?: boolean;\r\n  pagServer?: boolean;\r\n  onSelectedRowsChange?: (selectedRows: any) => void;\r\n  clearSelectedRows?: boolean;\r\n  sortServer?: boolean;\r\n  onSort?: (column: any, sortDirection: string) => void;\r\n  persistTableHead?: boolean;\r\n  sortFunction?: (rows: any[], field: any, direction: 'asc' | 'desc') => any[];\r\n  noHeader?: boolean;\r\n  dense?: boolean;\r\n  pagination?: boolean;\r\n  paginationServer?: boolean;\r\n  paginationTotalRows?: number;\r\n  subHeaderAlign?: string;\r\n  [key: string]: any;\r\n}\r\n\r\nfunction RKITable(props: RKITableProps) {\r\n  const { t } = useTranslation('common');\r\n  const paginationComponentOptions = {\r\n    rowsPerPageText: t('Rowsperpage'),\r\n    };\r\n  const {\r\n    columns,\r\n    data,\r\n    totalRows,\r\n    resetPaginationToggle,\r\n    subheader,\r\n    subHeaderComponent,\r\n    handlePerRowsChange,\r\n    handlePageChange,\r\n    rowsPerPage,\r\n    defaultRowsPerPage,\r\n    selectableRows,\r\n    loading,\r\n    pagServer,\r\n    onSelectedRowsChange,\r\n    clearSelectedRows,\r\n    sortServer,\r\n    onSort,\r\n    persistTableHead,\r\n    sortFunction,\r\n    // Filter out any unknown props that might cause DOM warnings\r\n    ...otherProps\r\n  } = props;\r\n\r\n  // Only pass known DataTable props to avoid DOM warnings\r\n  const dataTableProps = {\r\n    paginationComponentOptions,\r\n    noDataComponent: t(\"NoData\"),\r\n    noHeader: true,\r\n    columns,\r\n    data: data || [],\r\n    dense: true,\r\n    paginationResetDefaultPage: resetPaginationToggle,\r\n    subHeader: subheader,\r\n    progressPending: loading,\r\n    subHeaderComponent,\r\n    pagination: true,\r\n    paginationServer: pagServer,\r\n    paginationPerPage: defaultRowsPerPage ? defaultRowsPerPage : 10,\r\n    paginationRowsPerPageOptions: rowsPerPage ? rowsPerPage : [10, 15, 20, 25, 30],\r\n    paginationTotalRows: totalRows,\r\n    onChangeRowsPerPage: handlePerRowsChange,\r\n    onChangePage: handlePageChange,\r\n    selectableRows,\r\n    onSelectedRowsChange,\r\n    clearSelectedRows,\r\n    progressComponent: <CustomLoader/>,\r\n    sortIcon: <i className=\"sort-icon fas fa-exchange-alt\" />,\r\n    sortServer,\r\n    onSort,\r\n    sortFunction,\r\n    persistTableHead,\r\n    className: \"rki-table\"\r\n  };\r\n  return (\r\n    <DataTable {...dataTableProps} />\r\n  )\r\n};\r\n\r\nRKITable.defaultProps = {\r\n  subHeader: false,\r\n  pagination: true,\r\n  totalRows: null,\r\n  pagServer: true,\r\n  onSelectedRowsChange: null,\r\n  clearSelectedRows: false,\r\n  sortServer: false,\r\n  persistTableHead: false\r\n}\r\n\r\nexport default RKITable;\r\n\r\n", "export default function NoAccessMessage(_props: any) {\r\n    return (\r\n      <div className=\"container-fluid p-0 response-message-block\">\r\n        <div className=\"message\">you don't have permission to access</div>\r\n      </div>\r\n    )\r\n  }", "\n    (window.__NEXT_P = window.__NEXT_P || []).push([\n      \"/adminsettings/region\",\n      function () {\n        return require(\"private-next-pages/adminsettings/region/index.tsx\");\n      }\n    ]);\n    if(module.hot) {\n      module.hot.dispose(function () {\n        window.__NEXT_P.push([\"/adminsettings/region\"])\n      });\n    }\n  ", "//Import Library\r\nimport { useState, useEffect } from \"react\";\r\n\r\nimport toast from 'react-hot-toast';\r\nimport Link from \"next/link\";\r\nimport { Mo<PERSON>, Button } from \"react-bootstrap\";\r\n\r\n//Import services/components\r\nimport apiService from \"../../../services/apiService\";\r\nimport RKITable from \"../../../components/common/RKITable\";\r\nimport RegionTableFilter from \"./regionTableFilter\";\r\nimport { useTranslation } from 'next-i18next';\r\n\r\nconst RegionTable = (_props: any) => {\r\n    const { t } = useTranslation('common');\r\n    const [tabledata, setDataToTable] = useState([]);\r\n    const [, setLoading] = useState(false);\r\n    const [totalRows, setTotalRows] = useState(0);\r\n    const [perPage, setPerPage] = useState(10);\r\n    const [isModalShow, setModal] = useState(false);\r\n    const [selectRegionDetails, setSelectRegion] = useState({});\r\n    const [countryId, setCountryId]: any = useState(\"\");\r\n\r\n    const regionParams = {\r\n        sort: { title: \"asc\" },\r\n        limit: perPage,\r\n        page: 1,\r\n        query: {},\r\n    };\r\n\r\n    const columns = [\r\n        {\r\n            name: t(\"adminsetting.Regions.Region\"),\r\n            selector: (row: any) => row.title,\r\n            sortable: true,\r\n        },\r\n        {\r\n            name: t(\"adminsetting.Regions.Country\"),\r\n            selector: (row: any) => row.country?.title || '',\r\n            sortable: true,\r\n            cell: (d: any) => (d.country && d.country.title ? d.country.title : \"\"),\r\n        },\r\n        {\r\n            name: t(\"adminsetting.Regions.Action\"),\r\n            selector: (row: any) => row._id,\r\n            sortable: false,\r\n            cell: (d: any) => (\r\n                <div>\r\n                    <Link href=\"/adminsettings/[...routes]\" as={`/adminsettings/edit_region/${d._id}`}>\r\n\r\n                        <i className=\"icon fas fa-edit\" />\r\n\r\n                    </Link>\r\n                    &nbsp;\r\n                    <a onClick={() => userAction(d)}>\r\n                        <i className=\"icon fas fa-trash-alt\" />\r\n                    </a>{\" \"}\r\n                </div>\r\n            ),\r\n        },\r\n    ];\r\n\r\n    const getRegionData = async () => {\r\n        setLoading(true);\r\n        const response = await apiService.get(\"/region\", regionParams);\r\n        if (response && response.data) {\r\n            setDataToTable(response.data);\r\n            setTotalRows(response.totalCount);\r\n            setLoading(false);\r\n        }\r\n    };\r\n\r\n    const handlePageChange = (page: any) => {\r\n        regionParams.limit = perPage;\r\n        regionParams.page = page;\r\n        countryId && (regionParams.query = { country: countryId.value });\r\n        getRegionData();\r\n    };\r\n\r\n    const handlePerRowsChange = async (newPerPage: any, page: any) => {\r\n        regionParams.limit = newPerPage;\r\n        regionParams.page = page;\r\n        countryId && (regionParams.query = { country: countryId.value });\r\n        setLoading(true);\r\n        const response = await apiService.get(\"/region\", regionParams);\r\n        if (response && response.data && response.data.length > 0) {\r\n            setDataToTable(response.data);\r\n            setPerPage(newPerPage);\r\n            setLoading(false);\r\n        }\r\n    };\r\n\r\n    const userAction = async (row: any) => {\r\n        setSelectRegion(row._id);\r\n        setModal(true);\r\n    };\r\n\r\n    const modalConfirm = async () => {\r\n        try {\r\n            await apiService.remove(`/region/${selectRegionDetails}`);\r\n            getRegionData();\r\n            setModal(false);\r\n            toast.success(t(\"adminsetting.Regions.Table.regionDeletedSuccessfully\"));\r\n        } catch (error) {\r\n            toast.error(t(\"adminsetting.Regions.Table.errorDeletingRegion\"));\r\n        }\r\n    };\r\n\r\n    const modalHide = () => setModal(false);\r\n\r\n    //Handle Country Search\r\n    const handleCountry = (country: any) => {\r\n        setCountryId(country);\r\n    };\r\n\r\n    useEffect(() => {\r\n        getRegionData();\r\n    }, []);\r\n\r\n    useEffect(() => {\r\n        countryId && (regionParams.query = { country: countryId.value });\r\n        getRegionData();\r\n    }, [countryId]);\r\n\r\n    return (\r\n        <div className=\"region__table\">\r\n            <Modal show={isModalShow} onHide={modalHide}>\r\n                <Modal.Header closeButton>\r\n                    <Modal.Title>{t(\"adminsetting.Regions.DeleteRegion\")}</Modal.Title>\r\n                </Modal.Header>\r\n                <Modal.Body>{t(\"adminsetting.Regions.Areyousurewanttodeletethisregion?\")}</Modal.Body>\r\n                <Modal.Footer>\r\n                    <Button variant=\"secondary\" onClick={modalHide}>\r\n                        {t(\"adminsetting.Regions.Cancel\")}\r\n                    </Button>\r\n                    <Button variant=\"primary\" onClick={modalConfirm}>\r\n                        {t(\"adminsetting.Regions.Yes\")}\r\n                    </Button>\r\n                </Modal.Footer>\r\n            </Modal>\r\n            <RegionTableFilter countryHandler={handleCountry} value={countryId} />\r\n            <RKITable\r\n                columns={columns}\r\n                data={tabledata}\r\n                totalRows={totalRows}\r\n                pagServer={true}\r\n                handlePerRowsChange={handlePerRowsChange}\r\n                handlePageChange={handlePageChange}\r\n            />\r\n        </div>\r\n    );\r\n};\r\n\r\nexport default RegionTable;\r\n", "interface PageHeadingProps {\r\n  title: string; // Required based on actual usage\r\n}\r\n\r\nexport default function PageHeading(props: PageHeadingProps) {\r\n  return (\r\n    <h2 className=\"page-heading\">{props.title}</h2>\r\n  )\r\n}\r\n", "//Import Library\r\nimport { Container, <PERSON>, <PERSON>, Button } from \"react-bootstrap\";\r\nimport Link from \"next/link\";\r\n\r\n//Import services/components\r\nimport PageHeading from \"../../../components/common/PageHeading\";\r\nimport RegionTable from \"./regionTable\";\r\nimport { useTranslation } from 'next-i18next';\r\nimport { serverSideTranslations } from 'next-i18next/serverSideTranslations';\r\nimport { canAddRegions } from \"../permissions\";\r\nimport { useSelector } from \"react-redux\";\r\nimport NoAccessMessage from \"../../rNoAccess\";\r\n\r\nconst RegionIndex = (_props: any) => {\r\n  const { t } = useTranslation('common');\r\n  const SowRegionIndex = () => {\r\n    return (\r\n      <Container style={{ overflow: \"hidden\" }} fluid className=\"p-0\">\r\n        <Row>\r\n          <Col xs={12}>\r\n            <PageHeading title={t(\"adminsetting.Regions.Regions\")} />\r\n          </Col>\r\n        </Row>\r\n        <Row>\r\n          <Col xs={12}>\r\n            <Link\r\n              href=\"/adminsettings/[...routes]\"\r\n              as=\"/adminsettings/create_region\"\r\n              >\r\n              <Button variant=\"secondary\" size=\"sm\">\r\n                {t(\"adminsetting.Regions.AddRegion\")}\r\n              </Button>\r\n            </Link>\r\n          </Col>\r\n        </Row>\r\n        <Row className=\"mt-3\">\r\n          <Col xs={12}>\r\n            <RegionTable />\r\n          </Col>\r\n        </Row>\r\n      </Container>\r\n    );\r\n  }\r\n\r\n  const ShowAddRegions = canAddRegions(() => <SowRegionIndex />);\r\n  const state:any = useSelector((state) => state);\r\n  if (!(state?.permissions?.region?.['create:any'])) {\r\n    return <NoAccessMessage />\r\n  }\r\n  return(\r\n    <ShowAddRegions />\r\n  )\r\n};\r\n\r\nexport async function getServerSideProps({ locale } : { locale: string }) {\r\n  return {\r\n    props: {\r\n      ...(await serverSideTranslations(locale, ['common'])),\r\n    },\r\n  }\r\n}\r\n\r\nexport default RegionIndex;\r\n", "//Import Library\r\nimport { Col, Container, Row } from \"react-bootstrap\";\r\nimport Select from 'react-select';\r\nimport React, { useEffect, useState } from \"react\";\r\n\r\n//Import services/components\r\nimport apiService from \"../../../services/apiService\";\r\nimport { useTranslation } from 'next-i18next';\r\n\r\n\r\nconst RegionTableFilter = ({ countryHandler, value }: any) => {\r\n  const { t,i18n } = useTranslation('common');\r\n  const titleSearch = i18n.language === 'de'? {title_de: \"asc\"} : {title: \"asc\"};\r\n  const currentLang = i18n.language;\r\n  const [countries, setCountreis] = useState<any[]>([]);\r\n  const countryParams = {\r\n    sort: titleSearch,\r\n    limit: \"~\",\r\n    languageCode:currentLang\r\n  };\r\n\r\n  useEffect(() => {\r\n    const fetchCountries = async () => {\r\n      const response = await apiService.get(\"/country\", countryParams);\r\n      if (response && response.data && response.data.length > 0) {\r\n        setCountreis(response.data);\r\n      }\r\n    }\r\n    fetchCountries();\r\n  }, [])\r\n  return (\r\n    <Container fluid>\r\n      <Row>\r\n        <Col md={4} className=\"ps-1\">\r\n          <Select\r\n            value={[value]}\r\n            placeholder={t(\"adminsetting.Regions.SelectCountry\")}\r\n            isClearable={true}\r\n            onChange={countryHandler}\r\n            options={countries.length > 0 ? countries.map((item, _i) => ({ value: item._id, label: item.title })) : []}\r\n          />\r\n        </Col>\r\n      </Row>\r\n    </Container>\r\n  )\r\n};\r\n\r\nexport default RegionTableFilter;"], "names": ["create", "canAddAreaOfWork", "connectedAuthWrapper", "authenticatedSelector", "state", "permissions", "area_of_work", "wrapperDisplayName", "country", "canAddDeploymentStatus", "deployment_status", "event_status", "expertise", "institution_focal_point", "hazard", "hazard_type", "institution", "institution_network", "institution_type", "canAddOperationStatus", "operation_status", "project_status", "region", "risk_level", "syndrome", "update_type", "users", "worl_region", "landing_page", "operation", "project", "event", "vspace", "update", "RKITable", "props", "t", "useTranslation", "paginationComponentOptions", "rowsPerPageText", "columns", "data", "totalRows", "resetPaginationToggle", "subheader", "subHeaderComponent", "handlePerRowsChange", "handlePageChange", "rowsPerPage", "defaultRowsPerPage", "selectableRows", "loading", "pagServer", "onSelectedRowsChange", "clearSelectedRows", "sortServer", "onSort", "persistTableHead", "sortFunction", "otherProps", "dataTableProps", "noDataComponent", "<PERSON><PERSON><PERSON><PERSON>", "dense", "paginationResetDefaultPage", "subHeader", "progressPending", "pagination", "paginationServer", "paginationPerPage", "paginationRowsPerPageOptions", "paginationTotalRows", "onChangeRowsPerPage", "onChangePage", "progressComponent", "CustomLoader", "sortIcon", "i", "className", "DataTable", "defaultProps", "NoAccessMessage", "_props", "div", "RegionTable", "tabledata", "setDataToTable", "useState", "setLoading", "setTotalRows", "perPage", "setPerPage", "isModalShow", "setModal", "selectRegionDetails", "setSelectRegion", "countryId", "setCountryId", "regionParams", "sort", "title", "limit", "page", "query", "name", "selector", "row", "sortable", "cell", "d", "_id", "Link", "href", "as", "a", "onClick", "userAction", "getRegionData", "response", "apiService", "get", "totalCount", "newPerPage", "value", "length", "modalConfirm", "remove", "toast", "success", "error", "modalHide", "useEffect", "Modal", "show", "onHide", "Header", "closeButton", "Title", "Body", "Footer", "<PERSON><PERSON>", "variant", "RegionTableFilter", "<PERSON><PERSON><PERSON><PERSON>", "handleCountry", "PageHeading", "h2", "RegionIndex", "SowRegionIndex", "Container", "style", "overflow", "fluid", "Row", "Col", "xs", "size", "ShowAddRegions", "canAddRegions", "useSelector", "i18n", "titleSearch", "language", "title_de", "currentLang", "countries", "setCountreis", "countryParams", "languageCode", "fetchCountries", "md", "Select", "placeholder", "isClearable", "onChange", "options", "map", "item", "_i", "label"], "sourceRoot": "", "ignoreList": []}