"use strict";(()=>{var e={};e.id=5024,e.ids=[636,3220,5024],e.modules={123:e=>{e.exports=require("dom-helpers/removeEventListener")},1332:e=>{e.exports=require("react-custom-scrollbars-2")},1428:e=>{e.exports=import("axios")},1680:e=>{e.exports=require("@restart/ui/utils")},1919:e=>{e.exports=require("react-transition-group/Transition")},3892:e=>{e.exports=require("classnames")},4048:e=>{e.exports=require("react-truncate")},6009:e=>{e.exports=require("dom-helpers/ownerDocument")},6952:e=>{e.exports=require("@restart/ui/Modal")},7374:e=>{e.exports=require("@restart/ui/Dropdown")},8732:e=>{e.exports=require("react/jsx-runtime")},9653:e=>{e.exports=require("@restart/ui/Overlay")},11242:e=>{e.exports=require("redux-persist/integration/react")},11688:e=>{e.exports=require("@restart/hooks/useWillUnmount")},12388:e=>{e.exports=require("dom-helpers/addEventListener")},13364:e=>{e.exports=require("redux-saga/effects")},14062:e=>{e.exports=import("react-redux")},14078:e=>{e.exports=import("swr")},14332:e=>{e.exports=require("uncontrollable")},16116:e=>{e.exports=require("invariant")},18622:e=>{e.exports=require("yup")},19121:e=>{e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},22326:e=>{e.exports=require("react-dom")},22541:e=>{e.exports=require("redux-persist/lib/storage")},25303:e=>{e.exports=require("@restart/ui/NavItem")},26324:e=>{e.exports=require("warning")},27825:e=>{e.exports=require("lodash")},27910:e=>{e.exports=require("stream")},28217:e=>{e.exports=require("@restart/ui/DropdownItem")},29021:e=>{e.exports=require("fs")},29294:e=>{e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},29628:e=>{e.exports=require("dom-helpers/addClass")},29825:e=>{e.exports=require("prop-types")},29841:e=>{e.exports=require("dom-helpers/hasClass")},30362:e=>{e.exports=require("@restart/ui/DropdownToggle")},33873:e=>{e.exports=require("path")},36653:e=>{e.exports=require("nprogress")},36955:e=>{e.exports=require("@restart/hooks/useBreakpoint")},37766:e=>{e.exports=require("@restart/hooks/useMergedRefs")},38609:e=>{e.exports=require("react-data-table-component")},39756:e=>{e.exports=import("redux")},40051:e=>{e.exports=require("dom-helpers/removeClass")},40361:e=>{e.exports=require("next/dist/compiled/next-server/pages.runtime.prod.js")},42893:e=>{e.exports=import("react-hot-toast")},43294:e=>{e.exports=require("formik")},50009:e=>{e.exports=require("@restart/ui/DropdownMenu")},54131:e=>{e.exports=import("@fortawesome/free-solid-svg-icons")},56084:(e,r,t)=>{t.d(r,{A:()=>u});var o=t(8732);t(82015);var s=t(38609),a=t.n(s),i=t(88751),n=t(30370);function p(e){let{t:r}=(0,i.useTranslation)("common"),t={rowsPerPageText:r("Rowsperpage")},{columns:s,data:p,totalRows:u,resetPaginationToggle:c,subheader:d,subHeaderComponent:l,handlePerRowsChange:x,handlePageChange:m,rowsPerPage:g,defaultRowsPerPage:q,selectableRows:h,loading:P,pagServer:f,onSelectedRowsChange:A,clearSelectedRows:S,sortServer:v,onSort:b,persistTableHead:w,sortFunction:j,...D}=e,M={paginationComponentOptions:t,noDataComponent:r("NoData"),noHeader:!0,columns:s,data:p||[],dense:!0,paginationResetDefaultPage:c,subHeader:d,progressPending:P,subHeaderComponent:l,pagination:!0,paginationServer:f,paginationPerPage:q||10,paginationRowsPerPageOptions:g||[10,15,20,25,30],paginationTotalRows:u,onChangeRowsPerPage:x,onChangePage:m,selectableRows:h,onSelectedRowsChange:A,clearSelectedRows:S,progressComponent:(0,o.jsx)(n.A,{}),sortIcon:(0,o.jsx)("i",{className:"sort-icon fas fa-exchange-alt"}),sortServer:v,onSort:b,sortFunction:j,persistTableHead:w,className:"rki-table"};return(0,o.jsx)(a(),{...M})}p.defaultProps={subHeader:!1,pagination:!0,totalRows:null,pagServer:!0,onSelectedRowsChange:null,clearSelectedRows:!1,sortServer:!1,persistTableHead:!1};let u=p},57664:e=>{e.exports=require("dom-helpers/scrollbarSize")},58928:e=>{e.exports=require("dom-helpers/canUseDOM")},59717:e=>{e.exports=require("@restart/ui/DropdownContext")},60560:e=>{e.exports=require("dom-helpers/contains")},63033:e=>{e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},65447:e=>{e.exports=require("@restart/ui/Button")},67364:e=>{e.exports=require("@restart/hooks/useCallbackRef")},68455:e=>{e.exports=import("redux-saga")},69722:e=>{e.exports=require("es6-promise")},73579:(e,r,t)=>{t.a(e,async(e,o)=>{try{t.r(r),t.d(r,{config:()=>q,default:()=>l,getServerSideProps:()=>g,getStaticPaths:()=>m,getStaticProps:()=>x,reportWebVitals:()=>h,routeModule:()=>b,unstable_getServerProps:()=>S,unstable_getServerSideProps:()=>v,unstable_getStaticParams:()=>A,unstable_getStaticPaths:()=>f,unstable_getStaticProps:()=>P});var s=t(63885),a=t(80237),i=t(81413),n=t(9616),p=t.n(n),u=t(72386),c=t(94846),d=e([u,c]);[u,c]=d.then?(await d)():d;let l=(0,i.M)(c,"default"),x=(0,i.M)(c,"getStaticProps"),m=(0,i.M)(c,"getStaticPaths"),g=(0,i.M)(c,"getServerSideProps"),q=(0,i.M)(c,"config"),h=(0,i.M)(c,"reportWebVitals"),P=(0,i.M)(c,"unstable_getStaticProps"),f=(0,i.M)(c,"unstable_getStaticPaths"),A=(0,i.M)(c,"unstable_getStaticParams"),S=(0,i.M)(c,"unstable_getServerProps"),v=(0,i.M)(c,"unstable_getServerSideProps"),b=new s.PagesRouteModule({definition:{kind:a.A.PAGES,page:"/hazard/DocumentAccordian",pathname:"/hazard/DocumentAccordian",bundlePath:"",filename:""},components:{App:u.default,Document:p()},userland:c});o()}catch(e){o(e)}})},74075:e=>{e.exports=require("zlib")},74716:e=>{e.exports=require("moment")},74987:e=>{e.exports=require("@restart/ui/ModalManager")},78097:e=>{e.exports=require("next-redux-saga")},78634:e=>{e.exports=require("@restart/ui/Anchor")},80237:(e,r)=>{Object.defineProperty(r,"A",{enumerable:!0,get:function(){return t}});var t=function(e){return e.PAGES="PAGES",e.PAGES_API="PAGES_API",e.APP_PAGE="APP_PAGE",e.APP_ROUTE="APP_ROUTE",e.IMAGE="IMAGE",e}({})},81366:e=>{e.exports=require("redux-auth-wrapper/connectedAuthWrapper")},81413:(e,r)=>{Object.defineProperty(r,"M",{enumerable:!0,get:function(){return function e(r,t){return t in r?r[t]:"then"in r&&"function"==typeof r.then?r.then(r=>e(r,t)):"function"==typeof r&&"default"===t?r:void 0}}})},81521:e=>{e.exports=require("dom-helpers/querySelectorAll")},81895:e=>{e.exports=require("@restart/hooks/useEventCallback")},82015:e=>{e.exports=require("react")},82053:e=>{e.exports=require("@fortawesome/react-fontawesome")},86842:e=>{e.exports=require("@restart/ui/SelectableContext")},87571:e=>{e.exports=require("dom-helpers/transitionEnd")},88751:e=>{e.exports=require("next-i18next")},93787:e=>{e.exports=require("redux-persist")},94846:(e,r,t)=>{t.a(e,async(e,o)=>{try{t.r(r),t.d(r,{default:()=>l});var s=t(8732),a=t(54131),i=t(82053),n=t(93024),p=t(82015),u=t(97377),c=t(88751),d=e([a]);a=(d.then?(await d)():d)[0];let l=e=>{let{t:r}=(0,c.useTranslation)("common"),[t,o]=(0,p.useState)(!1);return(0,s.jsx)(s.Fragment,{children:(0,s.jsxs)(n.A.Item,{eventKey:"0",children:[(0,s.jsxs)(n.A.Header,{onClick:()=>o(!t),children:[(0,s.jsx)("div",{className:"cardTitle",children:r("documents")}),(0,s.jsx)("div",{className:"cardArrow",children:t?(0,s.jsx)(i.FontAwesomeIcon,{icon:a.faMinus,color:"#fff"}):(0,s.jsx)(i.FontAwesomeIcon,{icon:a.faPlus,color:"#fff"})})]}),(0,s.jsxs)(n.A.Body,{children:[(0,s.jsx)(u.A,{loading:e.documentAccoirdianProps.loading,sortProps:e.documentAccoirdianProps.hazardDocSort,docs:e.documentAccoirdianProps.Document||[],docsDescription:e.documentAccoirdianProps.docSrc}),(0,s.jsx)("h6",{className:"mt-3",children:r("DocumentsfromUpdates")}),(0,s.jsx)(u.A,{loading:e.documentAccoirdianProps.loading,sortProps:e.documentAccoirdianProps.hazardDocUpdateSort,docs:e.documentAccoirdianProps.updateDocument||[],docsDescription:e.documentAccoirdianProps.docSrc})]})]})})};o()}catch(e){o(e)}})},94947:e=>{e.exports=require("@restart/hooks/useTimeout")},96196:e=>{e.exports=require("next-redux-wrapper")},97377:(e,r,t)=>{t.d(r,{A:()=>p});var o=t(8732);t(82015);var s=t(74716),a=t.n(s),i=t(56084),n=t(88751);let p=({docs:e,docsDescription:r,sortProps:t,loading:s})=>{let p=async(e,r)=>{t({columnSelector:e.selector,sortDirection:r})},{t:u}=(0,n.useTranslation)("common"),c=[{name:u("FileType"),width:"15%",selector:"extension",cell:e=>e&&e.extension&&e.extension},{name:u("FileName"),width:"25%",selector:"document_title",cell:e=>e&&e.original_name&&(0,o.jsx)("a",{href:`http://localhost:3001/api/v1/files/download/${e._id}`,target:"_blank",children:e.original_name.split(".").slice(0,-1).join(".")}),sortable:!0},{name:u("Description"),selector:"description",cell:e=>e&&e.description&&e.description},{name:u("UploadedDate"),width:"25%",selector:"doc_created_at",cell:e=>e&&e.updated_at&&a()(e.updated_at).format("MM/DD/YYYY"),sortable:!0}];return(0,o.jsx)(i.A,{columns:c,data:e,pagServer:!0,onSort:p,persistTableHead:!0,loading:s})}},98320:e=>{e.exports=require("@restart/hooks/useIsomorphicEffect")},99460:e=>{e.exports=require("dom-helpers/css")}};var r=require("../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),o=r.X(0,[6089,9216,9616,2386],()=>t(73579));module.exports=o})();