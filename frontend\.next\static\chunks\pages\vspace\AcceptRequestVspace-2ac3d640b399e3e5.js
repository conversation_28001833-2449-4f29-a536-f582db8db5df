(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[158],{58332:(e,s,c)=>{(window.__NEXT_P=window.__NEXT_P||[]).push(["/vspace/AcceptRequestVspace",function(){return c(88133)}])},88133:(e,s,c)=>{"use strict";c.r(s),c.d(s,{default:()=>m});var t=c(37876),i=c(89099),a=c.n(i),r=c(56970),n=c(37784),u=c(60282),l=c(97685),d=c(82851),o=c.n(d),p=c(14232),h=c(53718),b=c(31753);let m=e=>{let[s,c]=(0,p.useState)(!1),[d,m]=(0,p.useState)({title:"",description:"",startDate:null,endDate:null,searchData:"",visibility:!0,images:[],checked:!1,file_category:"",nonMembers:[],images_src:[],members:[],doc_src:[],document:[]}),[_,v]=(0,p.useState)(""),{t:y}=(0,b.Bd)("common"),A=(0,i.useRouter)().query.routes||[];(0,p.useEffect)(()=>{f()},[]);let f=async()=>{let e=await h.A.get("/vspace/".concat(A[1])),s=await h.A.get("/users/".concat(A[3]));e&&(c(e.visibility),m(e)),s&&v(s.username)},j=async()=>{d.visibility=!0,null==o().find(d.subscribers,{_id:A[3]})&&d.subscribers.push(A[3]);let e=await h.A.post("/vspace/acceptSubscriptionRequest/".concat(d._id),d);e&&e._id&&(c(!0),l.Ay.success(y("Vspaceissuccessfullyaccepted")),a().push("/vspace")),"Not authorized"==e&&l.Ay.error(y("Youarenotauthorized"))};return(0,t.jsx)("div",{children:(0,t.jsx)(r.A,{className:"my-4",children:(0,t.jsxs)(n.A,{children:[(0,t.jsx)("div",{children:"Welcome to Robert Koch Institut !"}),(0,t.jsx)("b",{children:_})," has requested to access your private virtual space ",(0,t.jsx)("b",{children:d.title}),(0,t.jsx)("br",{}),(0,t.jsx)(u.A,{disabled:s,className:"me-2",type:"submit",variant:"primary",onClick:j,children:y("Accept")}),(0,t.jsx)(u.A,{disabled:s,className:"me-2",variant:"info",onClick:()=>{c(!0),l.Ay.error(y("VspaceissuccessfullyDeclined")),a().push("/vspace")},children:y("Decline")})]})})})}}},e=>{var s=s=>e(e.s=s);e.O(0,[636,6593,8792],()=>s(58332)),_N_E=e.O()}]);
//# sourceMappingURL=AcceptRequestVspace-2ac3d640b399e3e5.js.map