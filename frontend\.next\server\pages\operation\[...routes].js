"use strict";(()=>{var e={};e.id=1008,e.ids=[636,1008,3220],e.modules={123:e=>{e.exports=require("dom-helpers/removeEventListener")},1332:e=>{e.exports=require("react-custom-scrollbars-2")},1428:e=>{e.exports=import("axios")},1680:e=>{e.exports=require("@restart/ui/utils")},1919:e=>{e.exports=require("react-transition-group/Transition")},2989:(e,r,t)=>{t.a(e,async(e,s)=>{try{t.r(r),t.d(r,{config:()=>h,default:()=>d,getServerSideProps:()=>g,getStaticPaths:()=>m,getStaticProps:()=>x,reportWebVitals:()=>q,routeModule:()=>y,unstable_getServerProps:()=>v,unstable_getServerSideProps:()=>j,unstable_getStaticParams:()=>_,unstable_getStaticPaths:()=>S,unstable_getStaticProps:()=>f});var o=t(63885),a=t(80237),i=t(81413),n=t(9616),u=t.n(n),p=t(72386),l=t(7870),c=e([p,l]);[p,l]=c.then?(await c)():c;let d=(0,i.M)(l,"default"),x=(0,i.M)(l,"getStaticProps"),m=(0,i.M)(l,"getStaticPaths"),g=(0,i.M)(l,"getServerSideProps"),h=(0,i.M)(l,"config"),q=(0,i.M)(l,"reportWebVitals"),f=(0,i.M)(l,"unstable_getStaticProps"),S=(0,i.M)(l,"unstable_getStaticPaths"),_=(0,i.M)(l,"unstable_getStaticParams"),v=(0,i.M)(l,"unstable_getServerProps"),j=(0,i.M)(l,"unstable_getServerSideProps"),y=new o.PagesRouteModule({definition:{kind:a.A.PAGES,page:"/operation/[...routes]",pathname:"/operation/[...routes]",bundlePath:"",filename:""},components:{App:p.default,Document:u()},userland:l});s()}catch(e){s(e)}})},3892:e=>{e.exports=require("classnames")},4048:e=>{e.exports=require("react-truncate")},6009:e=>{e.exports=require("dom-helpers/ownerDocument")},6952:e=>{e.exports=require("@restart/ui/Modal")},7374:e=>{e.exports=require("@restart/ui/Dropdown")},7870:(e,r,t)=>{t.a(e,async(e,s)=>{try{t.r(r),t.d(r,{default:()=>d,getServerSideProps:()=>c});var o=t(8732),a=t(44233),i=t(41522),n=t(76684),u=t(28163),p=t(35576),l=e([i,n]);async function c({locale:e}){return{props:{...await (0,p.serverSideTranslations)(e,["common"])}}}[i,n]=l.then?(await l)():l;let d=()=>{let e=(0,a.useRouter)().query.routes||[],r=(0,u.canAddOperationForm)(()=>(0,o.jsx)(i.default,{routes:e}));switch(e[0]){case"create":return(0,o.jsx)(r,{});case"edit":return(0,o.jsx)(i.default,{routes:e});case"show":return(0,o.jsx)(n.default,{routes:e});default:return null}};s()}catch(e){s(e)}})},8732:e=>{e.exports=require("react/jsx-runtime")},9532:e=>{e.exports=require("@restart/ui/Nav")},9653:e=>{e.exports=require("@restart/ui/Overlay")},11e3:e=>{e.exports=require("react-multi-select-component")},11242:e=>{e.exports=require("redux-persist/integration/react")},11688:e=>{e.exports=require("@restart/hooks/useWillUnmount")},12388:e=>{e.exports=require("dom-helpers/addEventListener")},13364:e=>{e.exports=require("redux-saga/effects")},14062:e=>{e.exports=import("react-redux")},14078:e=>{e.exports=import("swr")},14332:e=>{e.exports=require("uncontrollable")},16029:e=>{e.exports=require("react-dropzone")},16116:e=>{e.exports=require("invariant")},18622:e=>{e.exports=require("yup")},19121:e=>{e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},21964:e=>{e.exports=require("@restart/ui/TabPanel")},22326:e=>{e.exports=require("react-dom")},22541:e=>{e.exports=require("redux-persist/lib/storage")},25303:e=>{e.exports=require("@restart/ui/NavItem")},26324:e=>{e.exports=require("warning")},27825:e=>{e.exports=require("lodash")},27910:e=>{e.exports=require("stream")},28217:e=>{e.exports=require("@restart/ui/DropdownItem")},29021:e=>{e.exports=require("fs")},29294:e=>{e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},29628:e=>{e.exports=require("dom-helpers/addClass")},29780:e=>{e.exports=require("react-datepicker")},29825:e=>{e.exports=require("prop-types")},29841:e=>{e.exports=require("dom-helpers/hasClass")},30362:e=>{e.exports=require("@restart/ui/DropdownToggle")},32448:(e,r,t)=>{t.a(e,async(e,s)=>{try{t.r(r),t.d(r,{default:()=>m});var o=t(8732),a=t(54131),i=t(82053),n=t(74716),u=t.n(n),p=t(82015),l=t(83551),c=t(49481),d=t(88751),x=e([a]);a=(x.then?(await x)():x)[0];let m=e=>{let{t:r}=(0,d.useTranslation)("common"),[t,s]=(0,p.useState)(40);(0,p.useEffect)(()=>{document.getElementById("timeline-container")?.scroll(t,5e3)},[t]);let n={1:"/images/home/<USER>",2:"/images/home/<USER>",3:"/images/home/<USER>",4:"/images/home/<USER>",5:"/images/home/<USER>",6:"/images/home/<USER>",7:"/images/home/<USER>"};return(0,o.jsx)(o.Fragment,{children:(0,o.jsx)(l.A,{children:(0,o.jsx)(c.A,{className:"operatinTimeline",xs:12,children:(0,o.jsxs)("div",{className:"progress_main_sec",style:{marginTop:"90px"},children:[e.operation&&e.operation.timeline.length>2&&(0,o.jsxs)("div",{children:[(0,o.jsx)("div",{className:"prev",onClick:()=>{let e=t-50;s(e<0?0:e)},style:{cursor:"pointer"},children:(0,o.jsx)("span",{children:(0,o.jsx)(i.FontAwesomeIcon,{icon:a.faAngleLeft})})}),(0,o.jsx)("div",{className:"next",onClick:()=>{s(t+50)},style:{cursor:"pointer"},children:(0,o.jsx)("span",{children:(0,o.jsx)(i.FontAwesomeIcon,{icon:a.faAngleRight})})})]}),(0,o.jsx)("div",{className:"progressbar-container",id:"timeline-container",children:(0,o.jsx)("ul",{className:"progressbar",children:e.operation&&e.operation.timeline&&e.operation.timeline.map((t,s)=>(0,o.jsxs)("li",{style:{zIndex:e.operation.timeline.length-s},children:[(0,o.jsx)("div",{className:"timelineIcon",children:(0,o.jsx)("img",{src:n[t.iconclass],width:"80px",height:"80px"})}),t.timetitle?(0,o.jsx)("p",{className:"step-label",children:t.timetitle}):(0,o.jsx)("p",{className:"step-label",children:r("NoTitle")}),t.date?(0,o.jsx)("p",{className:"step-text",children:u()(t.date).format("MM-D-YYYY")}):(0,o.jsx)("p",{className:"step-text",children:r("NoDate")})]},s))})})]})})})})};s()}catch(e){s(e)}})},33873:e=>{e.exports=require("path")},35576:e=>{e.exports=require("next-i18next/serverSideTranslations")},36653:e=>{e.exports=require("nprogress")},36955:e=>{e.exports=require("@restart/hooks/useBreakpoint")},37766:e=>{e.exports=require("@restart/hooks/useMergedRefs")},38609:e=>{e.exports=require("react-data-table-component")},39756:e=>{e.exports=import("redux")},40051:e=>{e.exports=require("dom-helpers/removeClass")},40361:e=>{e.exports=require("next/dist/compiled/next-server/pages.runtime.prod.js")},42893:e=>{e.exports=import("react-hot-toast")},43294:e=>{e.exports=require("formik")},50009:e=>{e.exports=require("@restart/ui/DropdownMenu")},54131:e=>{e.exports=import("@fortawesome/free-solid-svg-icons")},57664:e=>{e.exports=require("dom-helpers/scrollbarSize")},58928:e=>{e.exports=require("dom-helpers/canUseDOM")},59717:e=>{e.exports=require("@restart/ui/DropdownContext")},60560:e=>{e.exports=require("dom-helpers/contains")},63033:e=>{e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},65209:e=>{e.exports=require("@restart/ui/TabContext")},65447:e=>{e.exports=require("@restart/ui/Button")},67364:e=>{e.exports=require("@restart/hooks/useCallbackRef")},68455:e=>{e.exports=import("redux-saga")},69722:e=>{e.exports=require("es6-promise")},70947:e=>{e.exports=require("@restart/ui/Tabs")},74075:e=>{e.exports=require("zlib")},74716:e=>{e.exports=require("moment")},74987:e=>{e.exports=require("@restart/ui/ModalManager")},76684:(e,r,t)=>{t.a(e,async(e,s)=>{try{t.r(r),t.d(r,{default:()=>m});var o=t(8732),a=t(82015),i=t(7082),n=t(63487),u=t(77136),p=t(88751),l=t(60072),c=t(95645),d=t(32448),x=e([n,u,l,c,d]);[n,u,l,c,d]=x.then?(await x)():x;let m=e=>{let{t:r}=(0,p.useTranslation)("common"),[t,s]=(0,a.useState)({title:"",timeline:[],description:"",hazard_type:{title:""},hazard:[],syndrome:{title:""},created_at:"",updated_at:"",country:{title:""},status:{title:""},start_date:"",end_date:"",partners:[],images:[],images_src:[],document:[],doc_src:[]}),[x,m]=(0,a.useState)(!1),[g,h]=(0,a.useState)(!1),[q,f]=(0,a.useState)([]),[S,_]=(0,a.useState)([]),[v,j]=(0,a.useState)(!1),y={sort:{doc_created_at:"asc"},Doctable:!0},A={sort:{doc_created_at:"asc"},DocUpdatetable:!0},b=async()=>{let r=[];h(!0);let t=await n.A.get(`/operation/${e.routes[1]}`,y);t&&Array.isArray(t)&&t.length>=1&&t[0].document&&t[0].document.length>=1&&(t.forEach(e=>{e.document&&e.document.length>0&&e.document.map((t,s)=>{t.description=e.document[s].docsrc,r.push(t)})}),f(r)),h(!1)},w=async()=>{let r=[];h(!0);let t=await n.A.get(`/operation/${e.routes[1]}`,A);t&&Array.isArray(t)&&t.length>=1&&t[0].document&&t[0].document.length>=1&&(t.forEach(e=>{e.document&&e.document.length>0&&e.document.map((t,s)=>{t.description=e.document[s].docsrc,r.push(t)})}),_(r)),h(!1)};(0,a.useEffect)(()=>{P()},[]);let P=async()=>{let r=await n.A.post("/users/getLoggedUser",{});r&&r.roles&&r.roles.length&&e.routes&&e.routes[1]&&((async t=>{m(!0);let o=await n.A.get(`/operation/${e.routes[1]}`,t);o&&(s(o),function(e,r){j(!1),r&&r.roles&&(r.roles.includes("SUPER_ADMIN")||r.roles.filter(e=>"EMT_NATIONAL_FOCALPOINT"==e).length>0&&e.user._id==r._id||r.roles.filter(e=>"EMT"==e).length>0&&e.user._id==r._id||r.roles.filter(e=>"INIG_STAKEHOLDER"==e).length>0&&e.user._id==r._id||r.roles.filter(e=>"GENERAL_USER"==e).length>0&&e.user._id==r._id?j(!0):r.roles.filter(e=>"PLATFORM_ADMIN"==e).length>0&&e.user._id==r._id&&j(!0))}(o,r)),m(!1)})({}),b(),w())},M={operationData:t,routeData:e,editAccess:v,documentAccoirdianProps:{loading:g,sortProps:e=>{y.sort={[e.columnSelector]:e.sortDirection},b()},Document:q,updateDocument:S,sortUpdateProps:e=>{A.sort={[e.columnSelector]:e.sortDirection},w()}}};return(0,o.jsx)(o.Fragment,{children:t?.title?(0,o.jsxs)(i.A,{className:"operationDetail",fluid:!0,children:[(0,o.jsx)(u.A,{routes:e.routes}),(0,o.jsx)(l.default,{...M}),(0,o.jsx)(d.default,{operation:t}),(0,o.jsx)(c.default,{...M})]}):(0,o.jsx)(o.Fragment,{})})};s()}catch(e){s(e)}})},78097:e=>{e.exports=require("next-redux-saga")},78634:e=>{e.exports=require("@restart/ui/Anchor")},80860:e=>{e.exports=require("@restart/ui/NoopTransition")},81149:e=>{e.exports=require("react-responsive-carousel")},81366:e=>{e.exports=require("redux-auth-wrapper/connectedAuthWrapper")},81521:e=>{e.exports=require("dom-helpers/querySelectorAll")},81895:e=>{e.exports=require("@restart/hooks/useEventCallback")},82015:e=>{e.exports=require("react")},82053:e=>{e.exports=require("@fortawesome/react-fontawesome")},86842:e=>{e.exports=require("@restart/ui/SelectableContext")},86843:e=>{e.exports=require("moment/locale/de")},87571:e=>{e.exports=require("dom-helpers/transitionEnd")},88751:e=>{e.exports=require("next-i18next")},93787:e=>{e.exports=require("redux-persist")},94696:e=>{e.exports=require("@react-google-maps/api")},94947:e=>{e.exports=require("@restart/hooks/useTimeout")},96196:e=>{e.exports=require("next-redux-wrapper")},98320:e=>{e.exports=require("@restart/hooks/useIsomorphicEffect")},99460:e=>{e.exports=require("dom-helpers/css")}};var r=require("../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[6089,9216,9616,2386,2491,7136,5387,5645,72,1522],()=>t(2989));module.exports=s})();