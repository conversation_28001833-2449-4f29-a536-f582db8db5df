{"version": 3, "file": "static/chunks/pages/hazard/HazardCoverSection-784d6d679c3ad2cb.js", "mappings": "gFACA,4CACA,6BACA,WACA,OAAe,EAAQ,KAAkD,CACzE,EACA,SAFsB,6ECoCtB,MA9B0B,IACxB,GAAM,CAAEA,CAAC,CAAE,CAAGC,CAAAA,EAAAA,EAAAA,EAAAA,CAAcA,CAAC,QA6BhBC,EA5BPC,EAAiBC,SAASC,IA4BFH,CA5B8B,CA4B7B,CA3BzB,CAACI,CADyD,CAAK,EACpC,CAAGC,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,EAAC,GAO7C,MACE,iCAEIC,EAAMC,WAAW,CACjB,UAACC,MAAAA,CACCC,wBAAyBC,CAVZ,CAACC,EAAqBC,KAElC,CAAEC,OADe,CAAED,GAAqBD,EAAYG,MAAM,CAAGb,EAAkBU,EAAYI,SAAS,CAAC,EAAGd,GAAkB,MAAQK,EAAMC,WAAW,CACzH,CACnC,EAO8CD,EAAMC,WAAW,CAACH,GACxDY,UAAU,kBAEH,KAGTV,EAAMC,WAAW,EAAID,EAAMC,WAAW,CAACO,MAAM,CAAGb,EAC9C,UAACgB,SAAAA,CAAOC,KAAK,SAASF,UAAU,eAAeG,QAAS,IAAMC,EAAc,CAAChB,YAChEN,EAAbM,EAAe,WAAgB,GAAFN,WACjB,OAItB,8GCaA,MAhC2B,IACvB,IAAMuB,EAAaf,EAAMe,UAAU,CAC7BC,EAAchB,EAAMgB,CA8BfC,UA9B0B,CACrC,MACI,+BACI,WAACC,EAAAA,CAAGA,CAAAA,WACA,WAACC,EAAAA,CAAGA,CAAAA,CAACT,UAAU,iBACX,UAACU,KAAAA,UACAL,EAAWM,KAAK,EAAIN,EAAWM,KAAK,CAACL,EAAY,CAC5CD,EAAWM,KAAK,CAACL,EAAY,CAC7B,KAEN,UAACtB,EAAAA,CAAiBA,CAAAA,CAClBO,YACIc,EAAWd,WAAW,EAAIc,EAAWd,WAAW,CAACe,EAAY,CAC3DD,EAAWd,WAAW,CAACe,EAAY,CACnC,QAIV,UAACG,EAAAA,CAAGA,CAAAA,CAACG,MAAO,CAAEC,QAAS,MAAO,WAC1B,UAACC,MAAAA,CACDC,IAAKV,EAAWW,OAAO,CACvBJ,MAAO,CAAEK,MAAO,OAAQC,OAAQ,QAASC,eAAgB,OAAQ,EACjEC,IAAI,iBAMxB", "sources": ["webpack://_N_E/?96ef", "webpack://_N_E/./components/common/readMore/readMore.tsx", "webpack://_N_E/./pages/hazard/HazardCoverSection.tsx"], "sourcesContent": ["\n    (window.__NEXT_P = window.__NEXT_P || []).push([\n      \"/hazard/HazardCoverSection\",\n      function () {\n        return require(\"private-next-pages/hazard/HazardCoverSection.tsx\");\n      }\n    ]);\n    if(module.hot) {\n      module.hot.dispose(function () {\n        window.__NEXT_P.push([\"/hazard/HazardCoverSection\"])\n      });\n    }\n  ", "//Import Library\r\nimport { useState } from \"react\";\r\n\r\n//Import services/components\r\nimport { useTranslation } from 'next-i18next';\r\n\r\ninterface ReadMoreContainerProps {\r\n  description: string;\r\n}\r\n\r\nconst ReadMoreContainer = (props: ReadMoreContainerProps) => {\r\n  const { t } = useTranslation('common');\r\n  const readMoreLength = parseInt(process.env.READ_MORE_LENGTH || '200');\r\n  const [isReadMore, setIsReadMore] = useState(false);\r\n\r\n  const createMarkup = (htmlContent: string, isReadMoreInitial: boolean) => {\r\n    const truncateContent = (!isReadMoreInitial && htmlContent.length > readMoreLength) ? htmlContent.substring(0, readMoreLength) + \"...\" : props.description;\r\n    return { __html: truncateContent };\r\n  };\r\n\r\n  return (\r\n    <>\r\n      {\r\n        props.description  ?\r\n        <div\r\n          dangerouslySetInnerHTML={createMarkup(props.description,isReadMore)}\r\n          className=\"operationDesc\"\r\n        >\r\n        </div> : null\r\n      }\r\n      {\r\n        props.description && props.description.length > readMoreLength ?\r\n          <button type=\"button\" className=\"readMoreText\" onClick={() => setIsReadMore(!isReadMore)}>\r\n         {isReadMore ? t(\"readLess\") : t(\"readMore\")}\r\n          </button> : null\r\n      }\r\n    </>\r\n  )\r\n}\r\n\r\nexport default ReadMoreContainer;\r\n", "//Import Library\r\nimport { Col, Row } from \"react-bootstrap\";\r\n\r\n//Import services/components\r\nimport ReadMoreContainer from \"../../components/common/readMore/readMore\";\r\n\r\ninterface HazardCoverSectionProps {\r\n  hazardData: {\r\n    title?: {\r\n      [key: string]: string;\r\n    };\r\n    description?: {\r\n      [key: string]: string;\r\n    };\r\n    picture?: string;\r\n  };\r\n  currentLang: string;\r\n}\r\n\r\nconst HazardCoverSection = (props: HazardCoverSectionProps) => {\r\n    const hazardData = props.hazardData;\r\n    const currentLang = props.currentLang;\r\n    return(\r\n        <>\r\n            <Row>\r\n                <Col className=\"ps-4\">\r\n                    <h2>\r\n                    {hazardData.title && hazardData.title[currentLang]\r\n                        ? hazardData.title[currentLang]\r\n                        : \"\"}\r\n                    </h2>\r\n                    <ReadMoreContainer\r\n                    description={\r\n                        hazardData.description && hazardData.description[currentLang]\r\n                        ? hazardData.description[currentLang]\r\n                        : \"\"\r\n                    }\r\n                    />\r\n                </Col>\r\n                <Col style={{ display: \"flex\" }}>\r\n                    <img\r\n                    src={hazardData.picture}\r\n                    style={{ width: \"100%\", height: \"400px\", backgroundSize: \"cover\" }}\r\n                    alt=\"banner\"\r\n                    />\r\n                </Col>\r\n            </Row>\r\n        </>\r\n    )\r\n};\r\n\r\nexport default HazardCoverSection;"], "names": ["t", "useTranslation", "ReadMoreContainer", "readMoreLength", "parseInt", "process", "isReadMore", "useState", "props", "description", "div", "dangerouslySetInnerHTML", "createMarkup", "htmlContent", "isReadMoreInitial", "__html", "length", "substring", "className", "button", "type", "onClick", "setIsReadMore", "hazardData", "currentLang", "HazardCoverSection", "Row", "Col", "h2", "title", "style", "display", "img", "src", "picture", "width", "height", "backgroundSize", "alt"], "sourceRoot": "", "ignoreList": []}