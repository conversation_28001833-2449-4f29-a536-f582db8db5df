"use strict";(()=>{var e={};e.id=7509,e.ids=[636,3220,7509],e.modules={123:e=>{e.exports=require("dom-helpers/removeEventListener")},1332:e=>{e.exports=require("react-custom-scrollbars-2")},1428:e=>{e.exports=import("axios")},1680:e=>{e.exports=require("@restart/ui/utils")},1919:e=>{e.exports=require("react-transition-group/Transition")},3892:e=>{e.exports=require("classnames")},4048:e=>{e.exports=require("react-truncate")},6009:e=>{e.exports=require("dom-helpers/ownerDocument")},6952:e=>{e.exports=require("@restart/ui/Modal")},7374:e=>{e.exports=require("@restart/ui/Dropdown")},8732:e=>{e.exports=require("react/jsx-runtime")},9653:e=>{e.exports=require("@restart/ui/Overlay")},11242:e=>{e.exports=require("redux-persist/integration/react")},11688:e=>{e.exports=require("@restart/hooks/useWillUnmount")},12388:e=>{e.exports=require("dom-helpers/addEventListener")},13364:e=>{e.exports=require("redux-saga/effects")},14062:e=>{e.exports=import("react-redux")},14078:e=>{e.exports=import("swr")},14332:e=>{e.exports=require("uncontrollable")},16116:e=>{e.exports=require("invariant")},18622:e=>{e.exports=require("yup")},19121:e=>{e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},22326:e=>{e.exports=require("react-dom")},22541:e=>{e.exports=require("redux-persist/lib/storage")},25303:e=>{e.exports=require("@restart/ui/NavItem")},26324:e=>{e.exports=require("warning")},27053:(e,r,s)=>{s.d(r,{A:()=>a});var t=s(8732);function a(e){return(0,t.jsx)("h2",{className:"page-heading",children:e.title})}},27825:e=>{e.exports=require("lodash")},27910:e=>{e.exports=require("stream")},28217:e=>{e.exports=require("@restart/ui/DropdownItem")},28690:(e,r,s)=>{s.a(e,async(e,t)=>{try{s.r(r),s.d(r,{default:()=>q,getServerSideProps:()=>S});var a=s(8732),i=s(7082),n=s(83551),o=s(49481),p=s(91353),d=s(19918),l=s.n(d),u=s(27053),c=s(51758),m=s(88751),g=s(35576),x=s(45927),h=s(14062),A=s(35557),y=e([c,h]);async function S({locale:e}){return{props:{...await (0,g.serverSideTranslations)(e,["common"])}}}[c,h]=y.then?(await y)():y;let q=e=>{let{t:r}=(0,m.useTranslation)("common"),s=()=>(0,a.jsxs)(i.A,{style:{overflow:"hidden"},fluid:!0,className:"p-0",children:[(0,a.jsx)(n.A,{children:(0,a.jsx)(o.A,{xs:12,children:(0,a.jsx)(u.A,{title:r("adminsetting.Regions.Regions")})})}),(0,a.jsx)(n.A,{children:(0,a.jsx)(o.A,{xs:12,children:(0,a.jsx)(l(),{href:"/adminsettings/[...routes]",as:"/adminsettings/create_region",children:(0,a.jsx)(p.A,{variant:"secondary",size:"sm",children:r("adminsetting.Regions.AddRegion")})})})}),(0,a.jsx)(n.A,{className:"mt-3",children:(0,a.jsx)(o.A,{xs:12,children:(0,a.jsx)(c.default,{})})})]}),t=(0,x.canAddRegions)(()=>(0,a.jsx)(s,{})),d=(0,h.useSelector)(e=>e);return d?.permissions?.region?.["create:any"]?(0,a.jsx)(t,{}):(0,a.jsx)(A.default,{})};t()}catch(e){t(e)}})},29021:e=>{e.exports=require("fs")},29294:e=>{e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},29628:e=>{e.exports=require("dom-helpers/addClass")},29825:e=>{e.exports=require("prop-types")},29841:e=>{e.exports=require("dom-helpers/hasClass")},30362:e=>{e.exports=require("@restart/ui/DropdownToggle")},33873:e=>{e.exports=require("path")},35557:(e,r,s)=>{s.r(r),s.d(r,{default:()=>a});var t=s(8732);function a(e){return(0,t.jsx)("div",{className:"container-fluid p-0 response-message-block",children:(0,t.jsx)("div",{className:"message",children:"you don't have permission to access"})})}},35576:e=>{e.exports=require("next-i18next/serverSideTranslations")},36653:e=>{e.exports=require("nprogress")},36955:e=>{e.exports=require("@restart/hooks/useBreakpoint")},37766:e=>{e.exports=require("@restart/hooks/useMergedRefs")},38609:e=>{e.exports=require("react-data-table-component")},39026:(e,r,s)=>{s.a(e,async(e,t)=>{try{s.r(r),s.d(r,{config:()=>h,default:()=>c,getServerSideProps:()=>x,getStaticPaths:()=>g,getStaticProps:()=>m,reportWebVitals:()=>A,routeModule:()=>w,unstable_getServerProps:()=>v,unstable_getServerSideProps:()=>f,unstable_getStaticParams:()=>q,unstable_getStaticPaths:()=>S,unstable_getStaticProps:()=>y});var a=s(63885),i=s(80237),n=s(81413),o=s(9616),p=s.n(o),d=s(72386),l=s(28690),u=e([d,l]);[d,l]=u.then?(await u)():u;let c=(0,n.M)(l,"default"),m=(0,n.M)(l,"getStaticProps"),g=(0,n.M)(l,"getStaticPaths"),x=(0,n.M)(l,"getServerSideProps"),h=(0,n.M)(l,"config"),A=(0,n.M)(l,"reportWebVitals"),y=(0,n.M)(l,"unstable_getStaticProps"),S=(0,n.M)(l,"unstable_getStaticPaths"),q=(0,n.M)(l,"unstable_getStaticParams"),v=(0,n.M)(l,"unstable_getServerProps"),f=(0,n.M)(l,"unstable_getServerSideProps"),w=new a.PagesRouteModule({definition:{kind:i.A.PAGES,page:"/adminsettings/region",pathname:"/adminsettings/region",bundlePath:"",filename:""},components:{App:d.default,Document:p()},userland:l});t()}catch(e){t(e)}})},39756:e=>{e.exports=import("redux")},40051:e=>{e.exports=require("dom-helpers/removeClass")},40361:e=>{e.exports=require("next/dist/compiled/next-server/pages.runtime.prod.js")},42404:(e,r,s)=>{s.a(e,async(e,t)=>{try{s.r(r),s.d(r,{default:()=>m});var a=s(8732),i=s(7082),n=s(83551),o=s(49481),p=s(99800),d=s(82015),l=s(63487),u=s(88751),c=e([p,l]);[p,l]=c.then?(await c)():c;let m=({countryHandler:e,value:r})=>{let{t:s,i18n:t}=(0,u.useTranslation)("common"),c="de"===t.language?{title_de:"asc"}:{title:"asc"},m=t.language,[g,x]=(0,d.useState)([]),h={sort:c,limit:"~",languageCode:m};return(0,d.useEffect)(()=>{(async()=>{let e=await l.A.get("/country",h);e&&e.data&&e.data.length>0&&x(e.data)})()},[]),(0,a.jsx)(i.A,{fluid:!0,children:(0,a.jsx)(n.A,{children:(0,a.jsx)(o.A,{md:4,className:"ps-1",children:(0,a.jsx)(p.default,{value:[r],placeholder:s("adminsetting.Regions.SelectCountry"),isClearable:!0,onChange:e,options:g.length>0?g.map((e,r)=>({value:e._id,label:e.title})):[]})})})})};t()}catch(e){t(e)}})},42893:e=>{e.exports=import("react-hot-toast")},43294:e=>{e.exports=require("formik")},45927:(e,r,s)=>{s.r(r),s.d(r,{canAddAreaOfWork:()=>n,canAddContent:()=>C,canAddCountry:()=>o,canAddDeploymentStatus:()=>p,canAddEventStatus:()=>d,canAddExpertise:()=>l,canAddFocalPointApproval:()=>u,canAddHazardTypes:()=>g,canAddHazards:()=>m,canAddLandingPage:()=>j,canAddOperationStatus:()=>y,canAddOrganisationApproval:()=>x,canAddOrganisationNetworks:()=>h,canAddOrganisationTypes:()=>A,canAddProjectStatus:()=>S,canAddRegions:()=>q,canAddRiskLevels:()=>v,canAddSyndromes:()=>f,canAddUpdateTypes:()=>w,canAddUsers:()=>_,canAddVspaceApproval:()=>c,canAddWorldRegion:()=>P,default:()=>b});var t=s(81366),a=s.n(t);let i="create:any",n=a()({authenticatedSelector:e=>!!e.permissions&&!!e.permissions.area_of_work&&!!e.permissions.area_of_work[i],wrapperDisplayName:"CanAddAreaOfWork"}),o=a()({authenticatedSelector:e=>!!e.permissions&&!!e.permissions.country&&!!e.permissions.country[i],wrapperDisplayName:"CanAddCountry"}),p=a()({authenticatedSelector:e=>!!e.permissions&&!!e.permissions.deployment_status&&!!e.permissions.deployment_status[i],wrapperDisplayName:"CanAddDeploymentStatus"}),d=a()({authenticatedSelector:e=>!!e.permissions&&!!e.permissions.event_status&&!!e.permissions.event_status[i],wrapperDisplayName:"CanAddEventStatus"}),l=a()({authenticatedSelector:e=>!!e.permissions&&!!e.permissions.expertise&&!!e.permissions.expertise[i],wrapperDisplayName:"CanAddExpertise"}),u=a()({authenticatedSelector:e=>!!e.permissions&&!!e.permissions.institution_focal_point&&!!e.permissions.institution_focal_point[i],wrapperDisplayName:"CanAddFocalPointApproval"}),c=a()({authenticatedSelector:e=>!!e.permissions&&!!e.permissions.institution_focal_point&&!!e.permissions.institution_focal_point[i],wrapperDisplayName:"CanAddVspaceApproval"}),m=a()({authenticatedSelector:e=>!!e.permissions&&!!e.permissions.hazard&&!!e.permissions.hazard[i],wrapperDisplayName:"CanAddHazards"}),g=a()({authenticatedSelector:e=>!!e.permissions&&!!e.permissions.hazard_type&&!!e.permissions.hazard_type[i],wrapperDisplayName:"CanAddHazardTypes"}),x=a()({authenticatedSelector:e=>!!e.permissions&&!!e.permissions.institution&&!!e.permissions.institution[i],wrapperDisplayName:"CanAddOrganisationApproval"}),h=a()({authenticatedSelector:e=>!!e.permissions&&!!e.permissions.institution_network&&!!e.permissions.institution_network[i],wrapperDisplayName:"CanAddOrganisationNetworks"}),A=a()({authenticatedSelector:e=>!!e.permissions&&!!e.permissions.institution_type&&!!e.permissions.institution_type[i],wrapperDisplayName:"CanAddOrganisationTypes"}),y=a()({authenticatedSelector:e=>!!e.permissions&&!!e.permissions.operation_status&&!!e.permissions.operation_status[i],wrapperDisplayName:"CanAddOperationStatus"}),S=a()({authenticatedSelector:e=>!!e.permissions&&!!e.permissions.project_status&&!!e.permissions.project_status[i],wrapperDisplayName:"CanAddProjectStatus"}),q=a()({authenticatedSelector:e=>!!e.permissions&&!!e.permissions.region&&!!e.permissions.region[i],wrapperDisplayName:"CanAddRegions"}),v=a()({authenticatedSelector:e=>!!e.permissions&&!!e.permissions.risk_level&&!!e.permissions.risk_level[i],wrapperDisplayName:"CanAddRiskLevels"}),f=a()({authenticatedSelector:e=>!!e.permissions&&!!e.permissions.syndrome&&!!e.permissions.syndrome[i],wrapperDisplayName:"CanAddSyndromes"}),w=a()({authenticatedSelector:e=>!!e.permissions&&!!e.permissions.update_type&&!!e.permissions.update_type[i],wrapperDisplayName:"CanAddUpdateTypes"}),_=a()({authenticatedSelector:e=>!!e.permissions&&!!e.permissions.users&&!!e.permissions.users[i],wrapperDisplayName:"CanAddUsers"}),P=a()({authenticatedSelector:e=>!!e.permissions&&!!e.permissions.worl_region&&!!e.permissions.worl_region[i],wrapperDisplayName:"CanAddWorldRegion"}),j=a()({authenticatedSelector:e=>!!e.permissions&&!!e.permissions.landing_page&&!!e.permissions.landing_page[i],wrapperDisplayName:"CanAddLandingPage"}),C=a()({authenticatedSelector:e=>!!e.permissions&&!!e.permissions.operation&&!!e.permissions.operation[i]&&!!e.permissions.project&&!!e.permissions.project[i]&&!!e.permissions.event&&!!e.permissions.event[i]&&!!e.permissions.vspace&&!!e.permissions.vspace[i]&&!!e.permissions.institution&&!!e.permissions.institution[i]&&!!e.permissions.update&&!!e.permissions.update[i]||!1,wrapperDisplayName:"CanAddContent"}),b=n},50009:e=>{e.exports=require("@restart/ui/DropdownMenu")},51758:(e,r,s)=>{s.a(e,async(e,t)=>{try{s.r(r),s.d(r,{default:()=>h});var a=s(8732),i=s(82015),n=s(42893),o=s(19918),p=s.n(o),d=s(12403),l=s(91353),u=s(63487),c=s(56084),m=s(42404),g=s(88751),x=e([n,u,m]);[n,u,m]=x.then?(await x)():x;let h=e=>{let{t:r}=(0,g.useTranslation)("common"),[s,t]=(0,i.useState)([]),[,o]=(0,i.useState)(!1),[x,h]=(0,i.useState)(0),[A,y]=(0,i.useState)(10),[S,q]=(0,i.useState)(!1),[v,f]=(0,i.useState)({}),[w,_]=(0,i.useState)(""),P={sort:{title:"asc"},limit:A,page:1,query:{}},j=[{name:r("adminsetting.Regions.Region"),selector:e=>e.title,sortable:!0},{name:r("adminsetting.Regions.Country"),selector:e=>e.country?.title||"",sortable:!0,cell:e=>e.country&&e.country.title?e.country.title:""},{name:r("adminsetting.Regions.Action"),selector:e=>e._id,sortable:!1,cell:e=>(0,a.jsxs)("div",{children:[(0,a.jsx)(p(),{href:"/adminsettings/[...routes]",as:`/adminsettings/edit_region/${e._id}`,children:(0,a.jsx)("i",{className:"icon fas fa-edit"})}),"\xa0",(0,a.jsx)("a",{onClick:()=>R(e),children:(0,a.jsx)("i",{className:"icon fas fa-trash-alt"})})," "]})}],C=async()=>{o(!0);let e=await u.A.get("/region",P);e&&e.data&&(t(e.data),h(e.totalCount),o(!1))},b=async(e,r)=>{P.limit=e,P.page=r,w&&(P.query={country:w.value}),o(!0);let s=await u.A.get("/region",P);s&&s.data&&s.data.length>0&&(t(s.data),y(e),o(!1))},R=async e=>{f(e._id),q(!0)},D=async()=>{try{await u.A.remove(`/region/${v}`),C(),q(!1),n.default.success(r("adminsetting.Regions.Table.regionDeletedSuccessfully"))}catch(e){n.default.error(r("adminsetting.Regions.Table.errorDeletingRegion"))}},N=()=>q(!1);return(0,i.useEffect)(()=>{C()},[]),(0,i.useEffect)(()=>{w&&(P.query={country:w.value}),C()},[w]),(0,a.jsxs)("div",{className:"region__table",children:[(0,a.jsxs)(d.A,{show:S,onHide:N,children:[(0,a.jsx)(d.A.Header,{closeButton:!0,children:(0,a.jsx)(d.A.Title,{children:r("adminsetting.Regions.DeleteRegion")})}),(0,a.jsx)(d.A.Body,{children:r("adminsetting.Regions.Areyousurewanttodeletethisregion?")}),(0,a.jsxs)(d.A.Footer,{children:[(0,a.jsx)(l.A,{variant:"secondary",onClick:N,children:r("adminsetting.Regions.Cancel")}),(0,a.jsx)(l.A,{variant:"primary",onClick:D,children:r("adminsetting.Regions.Yes")})]})]}),(0,a.jsx)(m.default,{countryHandler:e=>{_(e)},value:w}),(0,a.jsx)(c.A,{columns:j,data:s,totalRows:x,pagServer:!0,handlePerRowsChange:b,handlePageChange:e=>{P.limit=A,P.page=e,w&&(P.query={country:w.value}),C()}})]})};t()}catch(e){t(e)}})},56084:(e,r,s)=>{s.d(r,{A:()=>d});var t=s(8732);s(82015);var a=s(38609),i=s.n(a),n=s(88751),o=s(30370);function p(e){let{t:r}=(0,n.useTranslation)("common"),s={rowsPerPageText:r("Rowsperpage")},{columns:a,data:p,totalRows:d,resetPaginationToggle:l,subheader:u,subHeaderComponent:c,handlePerRowsChange:m,handlePageChange:g,rowsPerPage:x,defaultRowsPerPage:h,selectableRows:A,loading:y,pagServer:S,onSelectedRowsChange:q,clearSelectedRows:v,sortServer:f,onSort:w,persistTableHead:_,sortFunction:P,...j}=e,C={paginationComponentOptions:s,noDataComponent:r("NoData"),noHeader:!0,columns:a,data:p||[],dense:!0,paginationResetDefaultPage:l,subHeader:u,progressPending:y,subHeaderComponent:c,pagination:!0,paginationServer:S,paginationPerPage:h||10,paginationRowsPerPageOptions:x||[10,15,20,25,30],paginationTotalRows:d,onChangeRowsPerPage:m,onChangePage:g,selectableRows:A,onSelectedRowsChange:q,clearSelectedRows:v,progressComponent:(0,t.jsx)(o.A,{}),sortIcon:(0,t.jsx)("i",{className:"sort-icon fas fa-exchange-alt"}),sortServer:f,onSort:w,sortFunction:P,persistTableHead:_,className:"rki-table"};return(0,t.jsx)(i(),{...C})}p.defaultProps={subHeader:!1,pagination:!0,totalRows:null,pagServer:!0,onSelectedRowsChange:null,clearSelectedRows:!1,sortServer:!1,persistTableHead:!1};let d=p},57664:e=>{e.exports=require("dom-helpers/scrollbarSize")},58928:e=>{e.exports=require("dom-helpers/canUseDOM")},59717:e=>{e.exports=require("@restart/ui/DropdownContext")},60560:e=>{e.exports=require("dom-helpers/contains")},63033:e=>{e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},65447:e=>{e.exports=require("@restart/ui/Button")},67364:e=>{e.exports=require("@restart/hooks/useCallbackRef")},68455:e=>{e.exports=import("redux-saga")},69722:e=>{e.exports=require("es6-promise")},74075:e=>{e.exports=require("zlib")},74716:e=>{e.exports=require("moment")},74987:e=>{e.exports=require("@restart/ui/ModalManager")},78097:e=>{e.exports=require("next-redux-saga")},78634:e=>{e.exports=require("@restart/ui/Anchor")},80237:(e,r)=>{Object.defineProperty(r,"A",{enumerable:!0,get:function(){return s}});var s=function(e){return e.PAGES="PAGES",e.PAGES_API="PAGES_API",e.APP_PAGE="APP_PAGE",e.APP_ROUTE="APP_ROUTE",e.IMAGE="IMAGE",e}({})},81366:e=>{e.exports=require("redux-auth-wrapper/connectedAuthWrapper")},81413:(e,r)=>{Object.defineProperty(r,"M",{enumerable:!0,get:function(){return function e(r,s){return s in r?r[s]:"then"in r&&"function"==typeof r.then?r.then(r=>e(r,s)):"function"==typeof r&&"default"===s?r:void 0}}})},81521:e=>{e.exports=require("dom-helpers/querySelectorAll")},81895:e=>{e.exports=require("@restart/hooks/useEventCallback")},82015:e=>{e.exports=require("react")},86842:e=>{e.exports=require("@restart/ui/SelectableContext")},87571:e=>{e.exports=require("dom-helpers/transitionEnd")},88751:e=>{e.exports=require("next-i18next")},93787:e=>{e.exports=require("redux-persist")},94947:e=>{e.exports=require("@restart/hooks/useTimeout")},96196:e=>{e.exports=require("next-redux-wrapper")},98320:e=>{e.exports=require("@restart/hooks/useIsomorphicEffect")},99460:e=>{e.exports=require("dom-helpers/css")},99800:e=>{e.exports=import("react-select")}};var r=require("../../webpack-runtime.js");r.C(e);var s=e=>r(r.s=e),t=r.X(0,[6089,9216,9616,2386],()=>s(39026));module.exports=t})();