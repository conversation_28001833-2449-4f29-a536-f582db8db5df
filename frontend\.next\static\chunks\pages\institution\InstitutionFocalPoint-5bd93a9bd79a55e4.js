(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[918],{33657:(t,i,n)=>{"use strict";n.d(i,{A:()=>a});var e=n(53718);class s{arrayDifference(t,i){let n=[];return t.forEach(t=>{0===i.filter(i=>i._id===t.value).length&&n.push(t.value)}),n}async deleteInvitations(t,i){t.forEach(async t=>{let n=await e.A.get("/users/".concat(t));n.institutionInvites.length&&(n.institutionInvites=n.institutionInvites.filter(t=>t.institutionId!==i),await e.A.patch("/users/".concat(t),n))})}isDuplicateInvite(t,i){return!!t.filter(t=>t.institutionId===i).length}getNewInviteMeta(t,i){return{institutionName:i,institutionId:t,status:"Request Pending"}}async getUserData(t,i,n){let s={};return i&&n?await e.A.get("/users",{query:{email:i,username:n}}):await e.A.get("/users/".concat(t))}async sendInvitations(t,i,n){t.forEach(async t=>{let s=t._id;if(s){let t=await this.getUserData(s);t&&(t.institutionInvites||(t.institutionInvites=[]),t.institutionInvites=t.institutionInvites.map(t=>(t.institutionId===i&&"Rejected"===t.status&&(t.status="Request Pending"),t)),this.isDuplicateInvite(t.institutionInvites||[],i)||t.institutionInvites.push(this.getNewInviteMeta(i,n)),await e.A.patch("/users/".concat(s),t))}else this.inviteNewUserWithEmail(t.email,t.username,i,n)})}async inviteNewUserWithEmail(t,i,n,s){let a=await e.A.get("/users",{query:{email:t,username:i}});a&&a.data[0]&&((a=a.data[0]).institutionInvites.push(this.getNewInviteMeta(n,s)),await e.A.patch("/users/".concat(a._id),a))}}let a=new s},33859:(t,i,n)=>{"use strict";n.r(i),n.d(i,{canAddInstitution:()=>u,canAddInstitutionForm:()=>r,canEditInstitution:()=>o,canEditInstitutionForm:()=>p,canManageFocalPoints:()=>d,canViewDiscussionUpdate:()=>c,default:()=>l});var e=n(37876);n(14232);var s=n(8178),a=n(59626);let u=(0,s.A)({authenticatedSelector:t=>!!t.permissions&&!!t.permissions.institution&&!!t.permissions.institution["create:any"],wrapperDisplayName:"CanAddInstitution"}),r=(0,s.A)({authenticatedSelector:t=>!!t.permissions&&!!t.permissions.institution&&!!t.permissions.institution["create:any"],wrapperDisplayName:"CanAddInstitutionForm",FailureComponent:()=>(0,e.jsx)(a.default,{})}),o=(0,s.A)({authenticatedSelector:(t,i)=>{if(t.permissions&&t.permissions.institution){if(t.permissions.institution["update:any"])return!0;else if(t.permissions.institution["update:own"]&&i.institution&&i.institution.user&&i.institution.user===t.user._id)return!0}return!1},wrapperDisplayName:"CanEditInstitution"}),p=(0,s.A)({authenticatedSelector:(t,i)=>{if(t.permissions&&t.permissions.institution){if(t.permissions.institution["update:any"])return!0;else if(t.permissions.institution["update:own"]&&i.institution&&i.institution.user&&i.institution.user===t.user._id)return!0}return!1},wrapperDisplayName:"CanEditInstitutionForm",FailureComponent:()=>(0,e.jsx)(a.default,{})}),c=(0,s.A)({authenticatedSelector:t=>!!t.permissions&&!!t.permissions.update&&!!t.permissions.update["read:any"],wrapperDisplayName:"CanViewDiscussionUpdate"}),d=(0,s.A)({authenticatedSelector:(t,i)=>{if(t.permissions&&t.permissions.institution_focal_point){if(t.permissions.institution_focal_point["update:any"])return!0;else if(t.permissions.institution_focal_point["update:own"]&&i.institution&&i.institution.user&&i.institution.user===t.user._id)return!0}return!1},wrapperDisplayName:"canManageFocalPoints"}),l=u},38476:(t,i,n)=>{(window.__NEXT_P=window.__NEXT_P||[]).push(["/institution/InstitutionFocalPoint",function(){return n(12780)}])}},t=>{var i=i=>t(t.s=i);t.O(0,[9773,2633,2780,636,6593,8792],()=>i(38476)),_N_E=t.O()}]);
//# sourceMappingURL=InstitutionFocalPoint-5bd93a9bd79a55e4.js.map