"use strict";(()=>{var e={};e.id=2058,e.ids=[636,2058,3220],e.modules={123:e=>{e.exports=require("dom-helpers/removeEventListener")},1332:e=>{e.exports=require("react-custom-scrollbars-2")},1428:e=>{e.exports=import("axios")},1680:e=>{e.exports=require("@restart/ui/utils")},1919:e=>{e.exports=require("react-transition-group/Transition")},3892:e=>{e.exports=require("classnames")},4048:e=>{e.exports=require("react-truncate")},6009:e=>{e.exports=require("dom-helpers/ownerDocument")},6417:(e,r,t)=>{t.d(r,{A:()=>a});let s=t(82015).createContext(null);s.displayName="CardHeaderContext";let a=s},6952:e=>{e.exports=require("@restart/ui/Modal")},7374:e=>{e.exports=require("@restart/ui/Dropdown")},8732:e=>{e.exports=require("react/jsx-runtime")},9653:e=>{e.exports=require("@restart/ui/Overlay")},11242:e=>{e.exports=require("redux-persist/integration/react")},11688:e=>{e.exports=require("@restart/hooks/useWillUnmount")},12388:e=>{e.exports=require("dom-helpers/addEventListener")},13364:e=>{e.exports=require("redux-saga/effects")},14062:e=>{e.exports=import("react-redux")},14078:e=>{e.exports=import("swr")},14332:e=>{e.exports=require("uncontrollable")},16116:e=>{e.exports=require("invariant")},18597:(e,r,t)=>{t.d(r,{A:()=>b});var s=t(3892),a=t.n(s),o=t(82015),i=t(80739),u=t(8732);let n=o.forwardRef(({className:e,bsPrefix:r,as:t="div",...s},o)=>(r=(0,i.oU)(r,"card-body"),(0,u.jsx)(t,{ref:o,className:a()(e,r),...s})));n.displayName="CardBody";let p=o.forwardRef(({className:e,bsPrefix:r,as:t="div",...s},o)=>(r=(0,i.oU)(r,"card-footer"),(0,u.jsx)(t,{ref:o,className:a()(e,r),...s})));p.displayName="CardFooter";var d=t(6417);let l=o.forwardRef(({bsPrefix:e,className:r,as:t="div",...s},n)=>{let p=(0,i.oU)(e,"card-header"),l=(0,o.useMemo)(()=>({cardHeaderBsPrefix:p}),[p]);return(0,u.jsx)(d.A.Provider,{value:l,children:(0,u.jsx)(t,{ref:n,...s,className:a()(r,p)})})});l.displayName="CardHeader";let x=o.forwardRef(({bsPrefix:e,className:r,variant:t,as:s="img",...o},n)=>{let p=(0,i.oU)(e,"card-img");return(0,u.jsx)(s,{ref:n,className:a()(t?`${p}-${t}`:p,r),...o})});x.displayName="CardImg";let c=o.forwardRef(({className:e,bsPrefix:r,as:t="div",...s},o)=>(r=(0,i.oU)(r,"card-img-overlay"),(0,u.jsx)(t,{ref:o,className:a()(e,r),...s})));c.displayName="CardImgOverlay";let m=o.forwardRef(({className:e,bsPrefix:r,as:t="a",...s},o)=>(r=(0,i.oU)(r,"card-link"),(0,u.jsx)(t,{ref:o,className:a()(e,r),...s})));m.displayName="CardLink";var q=t(7783);let h=(0,q.A)("h6"),f=o.forwardRef(({className:e,bsPrefix:r,as:t=h,...s},o)=>(r=(0,i.oU)(r,"card-subtitle"),(0,u.jsx)(t,{ref:o,className:a()(e,r),...s})));f.displayName="CardSubtitle";let g=o.forwardRef(({className:e,bsPrefix:r,as:t="p",...s},o)=>(r=(0,i.oU)(r,"card-text"),(0,u.jsx)(t,{ref:o,className:a()(e,r),...s})));g.displayName="CardText";let v=(0,q.A)("h5"),P=o.forwardRef(({className:e,bsPrefix:r,as:t=v,...s},o)=>(r=(0,i.oU)(r,"card-title"),(0,u.jsx)(t,{ref:o,className:a()(e,r),...s})));P.displayName="CardTitle";let y=o.forwardRef(({bsPrefix:e,className:r,bg:t,text:s,border:o,body:p=!1,children:d,as:l="div",...x},c)=>{let m=(0,i.oU)(e,"card");return(0,u.jsx)(l,{ref:c,...x,className:a()(r,m,t&&`bg-${t}`,s&&`text-${s}`,o&&`border-${o}`),children:p?(0,u.jsx)(n,{children:d}):d})});y.displayName="Card";let b=Object.assign(y,{Img:x,Title:P,Subtitle:f,Body:n,Link:m,Text:g,Header:l,Footer:p,ImgOverlay:c})},18622:e=>{e.exports=require("yup")},19121:e=>{e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},22326:e=>{e.exports=require("react-dom")},22541:e=>{e.exports=require("redux-persist/lib/storage")},25303:e=>{e.exports=require("@restart/ui/NavItem")},26324:e=>{e.exports=require("warning")},27825:e=>{e.exports=require("lodash")},27910:e=>{e.exports=require("stream")},28217:e=>{e.exports=require("@restart/ui/DropdownItem")},29021:e=>{e.exports=require("fs")},29294:e=>{e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},29628:e=>{e.exports=require("dom-helpers/addClass")},29825:e=>{e.exports=require("prop-types")},29841:e=>{e.exports=require("dom-helpers/hasClass")},30362:e=>{e.exports=require("@restart/ui/DropdownToggle")},33873:e=>{e.exports=require("path")},36653:e=>{e.exports=require("nprogress")},36955:e=>{e.exports=require("@restart/hooks/useBreakpoint")},37766:e=>{e.exports=require("@restart/hooks/useMergedRefs")},39756:e=>{e.exports=import("redux")},40051:e=>{e.exports=require("dom-helpers/removeClass")},40361:e=>{e.exports=require("next/dist/compiled/next-server/pages.runtime.prod.js")},42893:e=>{e.exports=import("react-hot-toast")},43294:e=>{e.exports=require("formik")},50009:e=>{e.exports=require("@restart/ui/DropdownMenu")},57664:e=>{e.exports=require("dom-helpers/scrollbarSize")},58928:e=>{e.exports=require("dom-helpers/canUseDOM")},59717:e=>{e.exports=require("@restart/ui/DropdownContext")},60560:e=>{e.exports=require("dom-helpers/contains")},63033:e=>{e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},65447:e=>{e.exports=require("@restart/ui/Button")},67364:e=>{e.exports=require("@restart/hooks/useCallbackRef")},68455:e=>{e.exports=import("redux-saga")},69722:e=>{e.exports=require("es6-promise")},74075:e=>{e.exports=require("zlib")},74716:e=>{e.exports=require("moment")},74987:e=>{e.exports=require("@restart/ui/ModalManager")},78097:e=>{e.exports=require("next-redux-saga")},78634:e=>{e.exports=require("@restart/ui/Anchor")},80237:(e,r)=>{Object.defineProperty(r,"A",{enumerable:!0,get:function(){return t}});var t=function(e){return e.PAGES="PAGES",e.PAGES_API="PAGES_API",e.APP_PAGE="APP_PAGE",e.APP_ROUTE="APP_ROUTE",e.IMAGE="IMAGE",e}({})},81366:e=>{e.exports=require("redux-auth-wrapper/connectedAuthWrapper")},81413:(e,r)=>{Object.defineProperty(r,"M",{enumerable:!0,get:function(){return function e(r,t){return t in r?r[t]:"then"in r&&"function"==typeof r.then?r.then(r=>e(r,t)):"function"==typeof r&&"default"===t?r:void 0}}})},81521:e=>{e.exports=require("dom-helpers/querySelectorAll")},81895:e=>{e.exports=require("@restart/hooks/useEventCallback")},82015:e=>{e.exports=require("react")},86842:e=>{e.exports=require("@restart/ui/SelectableContext")},87571:e=>{e.exports=require("dom-helpers/transitionEnd")},87824:(e,r,t)=>{t.r(r),t.d(r,{default:()=>n});var s=t(8732),a=t(19918),o=t.n(a),i=t(18597),u=t(88751);let n=e=>{let r=e.hazardOperationData,{t}=(0,u.useTranslation)("common");return(0,s.jsx)(s.Fragment,{children:(0,s.jsx)("div",{className:"rki-carousel-card",children:(0,s.jsxs)(i.A,{className:"infoCard",children:[(0,s.jsx)(i.A.Header,{className:"text-center",children:t("hazardshow.currentoperations")}),(0,s.jsx)(i.A.Body,{className:"hazardBody",children:r&&r.length>0?r.map((e,r)=>(0,s.jsx)("ul",{className:"ulItems",children:(0,s.jsxs)("li",{className:"liItems",children:[(0,s.jsx)(o(),{href:"/operation/[...routes]",as:`/operation/show/${e._id}`,children:e&&e.title?`${e.title}`:""},e._id),(0,s.jsxs)("span",{children:[" ","(",e&&e.country?`${e.country.title}`:"",")"]})]},r)})):(0,s.jsx)("span",{className:"text-center",children:t("noSourceFound")})})]})})})}},88751:e=>{e.exports=require("next-i18next")},93787:e=>{e.exports=require("redux-persist")},94947:e=>{e.exports=require("@restart/hooks/useTimeout")},96196:e=>{e.exports=require("next-redux-wrapper")},97035:(e,r,t)=>{t.a(e,async(e,s)=>{try{t.r(r),t.d(r,{config:()=>h,default:()=>x,getServerSideProps:()=>q,getStaticPaths:()=>m,getStaticProps:()=>c,reportWebVitals:()=>f,routeModule:()=>j,unstable_getServerProps:()=>y,unstable_getServerSideProps:()=>b,unstable_getStaticParams:()=>P,unstable_getStaticPaths:()=>v,unstable_getStaticProps:()=>g});var a=t(63885),o=t(80237),i=t(81413),u=t(9616),n=t.n(u),p=t(72386),d=t(87824),l=e([p]);p=(l.then?(await l)():l)[0];let x=(0,i.M)(d,"default"),c=(0,i.M)(d,"getStaticProps"),m=(0,i.M)(d,"getStaticPaths"),q=(0,i.M)(d,"getServerSideProps"),h=(0,i.M)(d,"config"),f=(0,i.M)(d,"reportWebVitals"),g=(0,i.M)(d,"unstable_getStaticProps"),v=(0,i.M)(d,"unstable_getStaticPaths"),P=(0,i.M)(d,"unstable_getStaticParams"),y=(0,i.M)(d,"unstable_getServerProps"),b=(0,i.M)(d,"unstable_getServerSideProps"),j=new a.PagesRouteModule({definition:{kind:o.A.PAGES,page:"/hazard/HazardOperation",pathname:"/hazard/HazardOperation",bundlePath:"",filename:""},components:{App:p.default,Document:n()},userland:d});s()}catch(e){s(e)}})},98320:e=>{e.exports=require("@restart/hooks/useIsomorphicEffect")},99460:e=>{e.exports=require("dom-helpers/css")}};var r=require("../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[6089,9216,9616,2386],()=>t(97035));module.exports=s})();