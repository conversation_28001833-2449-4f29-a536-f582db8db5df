{"version": 3, "file": "static/chunks/pages/adminsettings/approval/AdminTable-033cac25d1cdba85.js", "mappings": "+EACA,4CACA,qCACA,WACA,OAAe,EAAQ,KAA0D,CACjF,EACA,SAFsB,oGCiCtB,SAASA,EAASC,CAAoB,EACpC,GAAM,CAAEC,CAAC,CAAE,CAAGC,CAAAA,EAAAA,EAAAA,EAAAA,CAAcA,CAAC,UACvBC,EAA6B,CACjCC,gBAAiBH,EAAE,cACnB,EACI,SACJI,CAAO,MACPC,CAAI,WACJC,CAAS,uBACTC,CAAqB,WACrBC,CAAS,oBACTC,CAAkB,qBAClBC,CAAmB,kBACnBC,CAAgB,aAChBC,CAAW,CACXC,oBAAkB,gBAClBC,CAAc,SACdC,CAAO,WACPC,CAAS,sBACTC,CAAoB,mBACpBC,CAAiB,YACjBC,CAAU,QACVC,CAAM,kBACNC,CAAgB,cAChBC,CAAY,CAEZ,CADA,EACGC,EACJ,CAAGxB,EAGEyB,EAAiB,4BACrBtB,EACAuB,gBAAiBzB,EAAE,IAP0C,MAQ7D0B,UAAU,UACVtB,EACAC,KAAMA,GAAQ,EAAE,CAChBsB,OAAO,EACPC,2BAA4BrB,EAC5BsB,UAAWrB,EACXsB,gBAAiBf,qBACjBN,EACAsB,YAAY,EACZC,iBAAkBhB,EAClBiB,kBAAmBpB,GAA0C,GAC7DqB,eADwCrB,cACVD,GAA4B,CAAC,GAAI,GAAI,GAAI,CAA3BA,EAA+B,GAAG,CAC9EuB,oBAAqB7B,EACrB8B,oBAAqB1B,EACrB2B,aAAc1B,iBACdG,uBACAG,oBACAC,EACAoB,kBAAmB,UAACC,EAAAA,CAAYA,CAAAA,CAAAA,GAChCC,SAAU,UAACC,IAAAA,CAAEC,UAAU,6CACvBvB,SACAC,EACAE,gCACAD,EACAqB,UAAW,WACb,EACA,MACE,UAACC,EAAAA,EAASA,CAAAA,CAAE,GAAGnB,CAAc,EAEjC,CAEA1B,EAAS8C,YAAY,CAAG,CACtBf,WAAW,EACXE,YAAY,EACZzB,UAAW,KACXU,WAAW,EACXC,qBAAsB,KACtBC,mBAAmB,EACnBC,YAAY,EACZE,kBAAkB,CACpB,EAEA,MAAevB,QAAQA,EAAC,gJCoHxB,MA9MA,SAAS+C,CAAsB,EAC3B,GAAM,CAAE7C,CAAC,CAAE,CAAGC,CAAAA,EAAAA,EAAAA,EAAAA,CAAcA,CAAC,UACvB,CAAC6C,CA4Me,CA5MJC,EAAe,CAAGC,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAQ,EAAE,EAChD,EAAGC,EAAW,CAAGD,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,EAAU,GACnC,CAAC1C,EAAW4C,EAAa,CAAGF,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAS,GAC7C,CAACG,EAASC,EAAW,CAAGJ,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAS,IACzC,CAACK,EAAaC,EAAS,CAAGN,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,EAAU,GAC5C,CAACO,EAAWC,EAAa,CAAGR,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAS,IAC7C,CAACS,EAAmBC,EAAqB,CAAGV,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAM,CAAC,GAG3DW,EAAc,CAChBC,KAAM,CAAEC,WAAY,MAAO,EAC3BC,MAAO,IACPC,KAAM,EACNC,MAAO,CAAE,4BAA6B,iBAAkB,CAC5D,EAEM5D,EAAU,CACZ,CACI6D,KAAMjE,EAAE,kDACRkE,SAAU,WACVC,KAAM,GAAYC,EAAEC,QAAQ,EAEhC,CACIJ,KAAMjE,EAAE,oBACRkE,SAAU,kBACVC,KAAOC,GAAWA,EAAEE,eAAe,EAEvC,CACIL,KAAMjE,EAAE,+CACRkE,SAAU,QACVC,KAAM,GAAYC,EAAEG,KAAK,EAE7B,CACIN,KAAMjE,EAAE,gDACRkE,SAAU,GACVC,KAAM,GACF,WAACK,MAAAA,WAC4B,aAAxBJ,EAAEK,iBAAiB,EAAmBL,sBAAEK,iBAAiB,CACtD,iCACI,UAACC,EAAAA,CAAMA,CAAAA,CAACC,QAAQ,UAAUC,KAAK,KAAKC,QAAS,IAAMC,EAAWV,EAAG,oBAC5DpE,EAAE,oBACE,UAIb,yBAEqB,aAAxBoE,EAAEK,iBAAiB,EAA2C,oBAAxBL,EAAEK,iBAAiB,CACtD,UAACC,EAAAA,CAAMA,CAAAA,CAACC,QAAQ,YAAYC,KAAK,KAAKC,QAAS,IAAMC,EAAWV,EAAG,mBAC9DpE,EAAE,kBAGP,2BAIhB,EACH,CAEK+E,EAAe,IACjB,IAAIC,EAAmB,EAAE,CAezB,OAdA3E,EAAK4E,OAAO,CAAC,UACTC,GAAAA,EAAMC,YAAND,MAAwB,CAACD,OAAO,CAAC,IACzBG,GAAqBA,mBAAgD,GAA9BC,MAAM,EAC7CL,EAAUM,IAAI,CAAC,CACX,GAAGJ,CAAI,CACP,GAAG,CACCK,cAAeH,EAAkBG,aAAa,CAC9CjB,gBAAiBc,EAAkBd,eAAe,CAClDG,kBAAmBW,EAAkBC,MAAM,CAC9C,EAGb,EACJ,GACOL,CACX,EAEMQ,EAAe,UACjBvC,EAAW,IACX,IAAMwC,EAAW,MAAMC,EAAAA,CAAUA,CAACC,GAAG,CAAC,SAAUhC,GAChD,GAAI8B,GAAYA,EAASpF,IAAI,CAAE,CAC3B,IAAI2E,EAAYD,EAAaU,EAASpF,IAAI,EAC1C0C,EAAe6C,EAAcZ,EAAW7B,EAAS,IACjDD,EAAa8B,EAAUa,MAAM,EAC7B5C,GAAW,EACf,CACJ,EACMtC,EAAmB,MAAOoD,IAC5Bd,GAAW,GACX,IAAMwC,EAAW,MAAMC,EAAAA,CAAUA,CAACC,GAAG,CAAC,SAAUhC,GAC5C8B,GAAYA,EAASpF,IAAI,EAAIoF,EAASpF,IAAI,CAACwF,MAAM,CAAG,GAAG,CAEvD9C,EAAe6C,EADCb,EAAaU,EAASpF,IAAI,EACF8C,EAAX6B,IAC7B/B,GAAW,GAEnB,EAEMvC,EAAsB,MAAOoF,EAAoB/B,KACnDd,GAAW,GACX,IAAMwC,EAAW,MAAMC,EAAAA,CAAUA,CAACC,GAAG,CAAC,SAAUhC,GAC5C8B,GAAYA,EAASpF,IAAI,EAAIoF,EAASpF,IAAI,CAACwF,MAAM,CAAG,GAAG,CAEvD9C,EAAe6C,EADCb,EAAaU,EAASpF,IAAI,EACFyF,EAAXd,IAC7B5B,EAAW0C,GACX7C,GAAW,GAEnB,EAEM2C,EAAgB,CAACG,EAAcC,EAAmBC,IAC7CF,EAAMG,KAAK,CAAC,IAAe,EAAKF,EAAWC,EAAcD,GAGpEG,CAAAA,EAAAA,EAAAA,SAAAA,CAASA,CAAC,KACNX,GACJ,EAAG,EAAE,EAEL,IAAMV,EAAa,MAAOV,EAAQiB,KAC9Be,QAAQC,GAAG,CAACjC,EAAGiB,GACf/B,GAAS,GACTE,EAAa6B,GACTjB,GAAKA,EAAEkC,GAAG,EAAE,EAES,CAAE,GAAGlC,CAAC,CAAEiB,OADA,CACQkB,WADnBlB,EAAuB,WAAa,UACP,EAEvD,EAEMmB,EAAe,cAebC,EAMJ,GApBAhD,EAAkB,cAAiB,CAAlB,CAAqB,EACtCA,EAAkB,MAAS,CAAG,QAAb,GAEbA,GACAA,EAAkB,eAAD,GAAsB,EACvCA,EAAkB,eAAD,GAAsB,CAACoC,MAAM,EAChD,EACoB,eAAD,GAAsB,CAACa,GAAG,CAAC,IACpCC,EAAOpB,aAAa,GAAK9B,EAAkB,aAAgB,EAAE,CAC7DkD,EAAOtB,MAAM,CAAiB,YAAd9B,EAA0B,WAAa,YAEpDoD,IAIE,WAAW,CAAzBpD,EACC,MAAMmC,EAAAA,CAAUA,CAACkB,MAAM,CAAC,UAAmC,OAAzBnD,EAAkB,GAAM,GAE1DgD,EAAc,MAAMf,CAF+B,CAE/BA,CAAUA,CAACmB,KAAK,CAAC,UAAmC,OAAzBpD,EAAkB,GAAM,EAAIA,GAE3EgD,GAAsC,IAF0B,EAEjDA,EAAYpB,MAAM,CAAU,YAC3CyB,EAAAA,EAAKA,CAACC,KAAK,CACPN,EAAYhB,QAAQ,EAAIgB,EAAYhB,QAAQ,CAACuB,OAAO,CAC9CP,EAAYhB,QAAQ,CAACuB,OAAO,CAC5BhH,EAAE,8DAIZwF,IACkB,WAAW,CAAzBjC,EACAuD,EAAAA,EAAKA,CAACG,OAAO,CAACjH,EAAE,oDAEhB8G,EAAAA,EAAKA,CAACC,KAAK,CAAC/G,EAAE,mDAElB0D,EAAqB,CAAC,GACtBJ,GAAS,EAEjB,EAEM4D,EAAY,IAAM5D,GAAS,GAEjC,MACI,WAACkB,MAAAA,WACG,WAAC2C,EAAAA,CAAKA,CAAAA,CAACC,KAAM/D,EAAagE,OAAQH,YAC9B,UAACC,EAAAA,CAAKA,CAACG,MAAM,EAACC,WAAW,aACrB,WAACJ,EAAAA,CAAKA,CAACK,KAAK,YACPjE,EAAUkE,MAAM,CAAC,GAAGC,WAAW,GAAKnE,EAAU2C,KAAK,CAAC,GAAI,IACxDlG,EAAE,mDAGX,WAACmH,EAAAA,CAAKA,CAACQ,IAAI,YACN3H,EAAE,0DAA0D,IAAEuD,EAAW,IACzEvD,EAAE,sDAEP,WAACmH,EAAAA,CAAKA,CAACS,MAAM,YACT,UAAClD,EAAAA,CAAMA,CAAAA,CAACC,QAAQ,YAAYE,QAASqC,WAChClH,EAAE,kDAEP,UAAC0E,EAAAA,CAAMA,CAAAA,CAACC,QAAQ,UAAUE,QAAS2B,WAC9BxG,EAAE,qDAKf,UAACF,EAAAA,CAAQA,CAAAA,CACLM,QAASA,EACTC,KAAMyC,EACNxC,UAAWA,EACXU,WAAW,EACXN,oBAAqBA,EACrBC,iBAAkBA,MAIlC", "sources": ["webpack://_N_E/?2fff", "webpack://_N_E/./components/common/RKITable.tsx", "webpack://_N_E/./pages/adminsettings/approval/AdminTable.tsx"], "sourcesContent": ["\n    (window.__NEXT_P = window.__NEXT_P || []).push([\n      \"/adminsettings/approval/AdminTable\",\n      function () {\n        return require(\"private-next-pages/adminsettings/approval/AdminTable.tsx\");\n      }\n    ]);\n    if(module.hot) {\n      module.hot.dispose(function () {\n        window.__NEXT_P.push([\"/adminsettings/approval/AdminTable\"])\n      });\n    }\n  ", "//Import Library\r\nimport React from 'react'\r\nimport DataTable  from 'react-data-table-component';\r\n\r\n//Import services/components\r\nimport { useTranslation } from 'next-i18next';\r\nimport CustomLoader from './CustomLoader';\r\n\r\ninterface RKITableProps {\r\n  columns: any[];\r\n  data: any[];\r\n  totalRows?: number;\r\n  resetPaginationToggle?: boolean;\r\n  subheader?: boolean;\r\n  subHeaderComponent?: React.ReactNode;\r\n  handlePerRowsChange?: (newPerPage: number, page: number) => void;\r\n  handlePageChange?: (page: number) => void;\r\n  rowsPerPage?: number[];\r\n  defaultRowsPerPage?: number;\r\n  selectableRows?: boolean;\r\n  loading?: boolean;\r\n  pagServer?: boolean;\r\n  onSelectedRowsChange?: (selectedRows: any) => void;\r\n  clearSelectedRows?: boolean;\r\n  sortServer?: boolean;\r\n  onSort?: (column: any, sortDirection: string) => void;\r\n  persistTableHead?: boolean;\r\n  sortFunction?: (rows: any[], field: any, direction: 'asc' | 'desc') => any[];\r\n  noHeader?: boolean;\r\n  dense?: boolean;\r\n  pagination?: boolean;\r\n  paginationServer?: boolean;\r\n  paginationTotalRows?: number;\r\n  subHeaderAlign?: string;\r\n  [key: string]: any;\r\n}\r\n\r\nfunction RKITable(props: RKITableProps) {\r\n  const { t } = useTranslation('common');\r\n  const paginationComponentOptions = {\r\n    rowsPerPageText: t('Rowsperpage'),\r\n    };\r\n  const {\r\n    columns,\r\n    data,\r\n    totalRows,\r\n    resetPaginationToggle,\r\n    subheader,\r\n    subHeaderComponent,\r\n    handlePerRowsChange,\r\n    handlePageChange,\r\n    rowsPerPage,\r\n    defaultRowsPerPage,\r\n    selectableRows,\r\n    loading,\r\n    pagServer,\r\n    onSelectedRowsChange,\r\n    clearSelectedRows,\r\n    sortServer,\r\n    onSort,\r\n    persistTableHead,\r\n    sortFunction,\r\n    // Filter out any unknown props that might cause DOM warnings\r\n    ...otherProps\r\n  } = props;\r\n\r\n  // Only pass known DataTable props to avoid DOM warnings\r\n  const dataTableProps = {\r\n    paginationComponentOptions,\r\n    noDataComponent: t(\"NoData\"),\r\n    noHeader: true,\r\n    columns,\r\n    data: data || [],\r\n    dense: true,\r\n    paginationResetDefaultPage: resetPaginationToggle,\r\n    subHeader: subheader,\r\n    progressPending: loading,\r\n    subHeaderComponent,\r\n    pagination: true,\r\n    paginationServer: pagServer,\r\n    paginationPerPage: defaultRowsPerPage ? defaultRowsPerPage : 10,\r\n    paginationRowsPerPageOptions: rowsPerPage ? rowsPerPage : [10, 15, 20, 25, 30],\r\n    paginationTotalRows: totalRows,\r\n    onChangeRowsPerPage: handlePerRowsChange,\r\n    onChangePage: handlePageChange,\r\n    selectableRows,\r\n    onSelectedRowsChange,\r\n    clearSelectedRows,\r\n    progressComponent: <CustomLoader/>,\r\n    sortIcon: <i className=\"sort-icon fas fa-exchange-alt\" />,\r\n    sortServer,\r\n    onSort,\r\n    sortFunction,\r\n    persistTableHead,\r\n    className: \"rki-table\"\r\n  };\r\n  return (\r\n    <DataTable {...dataTableProps} />\r\n  )\r\n};\r\n\r\nRKITable.defaultProps = {\r\n  subHeader: false,\r\n  pagination: true,\r\n  totalRows: null,\r\n  pagServer: true,\r\n  onSelectedRowsChange: null,\r\n  clearSelectedRows: false,\r\n  sortServer: false,\r\n  persistTableHead: false\r\n}\r\n\r\nexport default RKITable;\r\n\r\n", "//Import Library\r\nimport React, { useEffect, useState } from \"react\";\r\nimport { <PERSON><PERSON>, <PERSON><PERSON> } from \"react-bootstrap\";\r\nimport toast from 'react-hot-toast';\r\n\r\n//Import services/components\r\nimport RKITable from \"../../../components/common/RKITable\";\r\nimport apiService from \"../../../services/apiService\";\r\nimport { useTranslation } from 'next-i18next';\r\n\r\ninterface UserData {\r\n    _id: string;\r\n    username: string;\r\n    email: string;\r\n    institutionInvites: Array<{\r\n        institutionId: string;\r\n        institutionName: string;\r\n        status: string;\r\n    }>;\r\n    [key: string]: any;\r\n}\r\n\r\nfunction AdminTable(_props: any) {\r\n    const { t } = useTranslation('common');\r\n    const [tabledata, setDataToTable] = useState<any[]>([]);\r\n    const [, setLoading] = useState<boolean>(false);\r\n    const [totalRows, setTotalRows] = useState<number>(0);\r\n    const [perPage, setPerPage] = useState<number>(10);\r\n    const [isModalShow, setModal] = useState<boolean>(false);\r\n    const [newStatus, setNewStatus] = useState<string>(\"\");\r\n    const [selectUserDetails, setSelectUserDetails] = useState<any>({});\r\n\r\n\r\n    const usersParams = {\r\n        sort: { created_at: \"desc\" },\r\n        limit: \"~\",\r\n        page: 1,\r\n        query: { \"institutionInvites.status\": \"Request Pending\" },\r\n    };\r\n\r\n    const columns = [\r\n        {\r\n            name: t(\"adminsetting.FocalPointsApprovalTable.Username\"),\r\n            selector: \"username\",\r\n            cell: (d: any) => d.username,\r\n        },\r\n        {\r\n            name: t(\"OrganisationName\"),\r\n            selector: \"institutionName\",\r\n            cell: (d: any) => d.institutionName,\r\n        },\r\n        {\r\n            name: t(\"adminsetting.FocalPointsApprovalTable.Email\"),\r\n            selector: \"email\",\r\n            cell: (d: any) => d.email,\r\n        },\r\n        {\r\n            name: t(\"adminsetting.FocalPointsApprovalTable.Action\"),\r\n            selector: \"\",\r\n            cell: (d: any) => (\r\n                <div>\r\n                    {d.institutionStatus === \"Rejected\" || d.institutionStatus === \"Request Pending\" ? (\r\n                        <>\r\n                            <Button variant=\"primary\" size=\"sm\" onClick={() => userAction(d, \"approve\")}>\r\n                                {t(\"instu.Approves\")}\r\n                            </Button>\r\n                            &nbsp;\r\n                        </>\r\n                    ) : (\r\n                        <></>\r\n                    )}\r\n                    {d.institutionStatus === \"Approved\" || d.institutionStatus === \"Request Pending\" ? (\r\n                        <Button variant=\"secondary\" size=\"sm\" onClick={() => userAction(d, \"reject\")}>\r\n                            {t(\"instu.Reject\")}\r\n                        </Button>\r\n                    ) : (\r\n                        <></>\r\n                    )}\r\n                </div>\r\n            ),\r\n        },\r\n    ];\r\n\r\n    const getTableData = (data: UserData[]) => {\r\n        let tableData: any[] = [];\r\n        data.forEach((user) => {\r\n            user?.institutionInvites.forEach((institutionInvite) => {\r\n                if (institutionInvite && institutionInvite.status === \"Request Pending\") {\r\n                    tableData.push({\r\n                        ...user,\r\n                        ...{\r\n                            institutionId: institutionInvite.institutionId,\r\n                            institutionName: institutionInvite.institutionName,\r\n                            institutionStatus: institutionInvite.status,\r\n                        },\r\n                    });\r\n                }\r\n            });\r\n        });\r\n        return tableData;\r\n    };\r\n\r\n    const getUsersData = async () => {\r\n        setLoading(true);\r\n        const response = await apiService.get(\"/users\", usersParams);\r\n        if (response && response.data) {\r\n            let tableData = getTableData(response.data);\r\n            setDataToTable(localPaginate(tableData, perPage, 1));\r\n            setTotalRows(tableData.length);\r\n            setLoading(false);\r\n        }\r\n    };\r\n    const handlePageChange = async (page: number) => {\r\n        setLoading(true);\r\n        const response = await apiService.get(\"/users\", usersParams);\r\n        if (response && response.data && response.data.length > 0) {\r\n            let tableData = getTableData(response.data);\r\n            setDataToTable(localPaginate(tableData, perPage, page));\r\n            setLoading(false);\r\n        }\r\n    };\r\n\r\n    const handlePerRowsChange = async (newPerPage: number, page: number) => {\r\n        setLoading(true);\r\n        const response = await apiService.get(\"/users\", usersParams);\r\n        if (response && response.data && response.data.length > 0) {\r\n            let tableData = getTableData(response.data);\r\n            setDataToTable(localPaginate(tableData, newPerPage, page));\r\n            setPerPage(newPerPage);\r\n            setLoading(false);\r\n        }\r\n    };\r\n\r\n    const localPaginate = (array: any[], page_size: number, page_number: number) => {\r\n        return array.slice((page_number - 1) * page_size, page_number * page_size);\r\n    };\r\n\r\n    useEffect(() => {\r\n        getUsersData();\r\n    }, []);\r\n\r\n    const userAction = async (d: any, status: string) => {\r\n        console.log(d, status);\r\n        setModal(true);\r\n        setNewStatus(status);\r\n        if (d && d._id) {\r\n            const setStatus = status === \"approve\" ? \"Approved\" : \"Rejected\";\r\n            setSelectUserDetails({ ...d, status: setStatus });\r\n        }\r\n    };\r\n\r\n    const modalConfirm = async () => {\r\n        selectUserDetails[\"is_focal_point\"] = true;\r\n        selectUserDetails[\"status\"] = \"Approved\";\r\n        if (\r\n            selectUserDetails &&\r\n            selectUserDetails[\"institutionInvites\"] &&\r\n            selectUserDetails[\"institutionInvites\"].length\r\n        ) {\r\n            selectUserDetails[\"institutionInvites\"].map((invite: any) => {\r\n                if (invite.institutionId === selectUserDetails[\"institutionId\"]) {\r\n                    invite.status = newStatus === \"approve\" ? \"Approved\" : \"Rejected\";\r\n                }\r\n                return invite;\r\n            });\r\n        }\r\n        let updatedData;\r\n        if(newStatus !== \"approve\") {\r\n            await apiService.remove(`/users/${selectUserDetails[\"_id\"]}`);\r\n        } else {\r\n            updatedData = await apiService.patch(`/users/${selectUserDetails[\"_id\"]}`, selectUserDetails);\r\n        }\r\n        if (updatedData && updatedData.status === 403) {\r\n            toast.error(\r\n                updatedData.response && updatedData.response.message\r\n                    ? updatedData.response.message\r\n                    : t(\"adminsetting.FocalPointsApprovalTable.Somethingwentswrong\")\r\n            );\r\n            return;\r\n        } else {\r\n            getUsersData();\r\n            if (newStatus === \"approve\") {\r\n                toast.success(t(\"adminsetting.FocalPointsApprovalTable.Approvemm\"));\r\n            } else {\r\n                toast.error(t(\"adminsetting.FocalPointsApprovalTable.Rejected\"));\r\n            }\r\n            setSelectUserDetails({});\r\n            setModal(false);\r\n        }\r\n    };\r\n\r\n    const modalHide = () => setModal(false);\r\n\r\n    return (\r\n        <div>\r\n            <Modal show={isModalShow} onHide={modalHide}>\r\n                <Modal.Header closeButton>\r\n                    <Modal.Title>\r\n                        {newStatus.charAt(0).toUpperCase() + newStatus.slice(1)}{\" \"}\r\n                        {t(\"adminsetting.FocalPointsApprovalTable.User\")}\r\n                    </Modal.Title>\r\n                </Modal.Header>\r\n                <Modal.Body>\r\n                    {t(\"adminsetting.FocalPointsApprovalTable.Areyousurewantto\")} {newStatus}{\" \"}\r\n                    {t(\"adminsetting.FocalPointsApprovalTable.thisuser?\")}\r\n                </Modal.Body>\r\n                <Modal.Footer>\r\n                    <Button variant=\"secondary\" onClick={modalHide}>\r\n                        {t(\"adminsetting.FocalPointsApprovalTable.Cancel\")}\r\n                    </Button>\r\n                    <Button variant=\"primary\" onClick={modalConfirm}>\r\n                        {t(\"adminsetting.FocalPointsApprovalTable.Yes\")}\r\n                    </Button>\r\n                </Modal.Footer>\r\n            </Modal>\r\n\r\n            <RKITable\r\n                columns={columns}\r\n                data={tabledata}\r\n                totalRows={totalRows}\r\n                pagServer={true}\r\n                handlePerRowsChange={handlePerRowsChange}\r\n                handlePageChange={handlePageChange}\r\n            />\r\n        </div>\r\n    );\r\n}\r\n\r\nexport default AdminTable;\r\n"], "names": ["RKITable", "props", "t", "useTranslation", "paginationComponentOptions", "rowsPerPageText", "columns", "data", "totalRows", "resetPaginationToggle", "subheader", "subHeaderComponent", "handlePerRowsChange", "handlePageChange", "rowsPerPage", "defaultRowsPerPage", "selectableRows", "loading", "pagServer", "onSelectedRowsChange", "clearSelectedRows", "sortServer", "onSort", "persistTableHead", "sortFunction", "otherProps", "dataTableProps", "noDataComponent", "<PERSON><PERSON><PERSON><PERSON>", "dense", "paginationResetDefaultPage", "subHeader", "progressPending", "pagination", "paginationServer", "paginationPerPage", "paginationRowsPerPageOptions", "paginationTotalRows", "onChangeRowsPerPage", "onChangePage", "progressComponent", "CustomLoader", "sortIcon", "i", "className", "DataTable", "defaultProps", "AdminTable", "tabledata", "setDataToTable", "useState", "setLoading", "setTotalRows", "perPage", "setPerPage", "isModalShow", "setModal", "newStatus", "setNewStatus", "selectUserDetails", "setSelectUserDetails", "usersParams", "sort", "created_at", "limit", "page", "query", "name", "selector", "cell", "d", "username", "institutionName", "email", "div", "institutionStatus", "<PERSON><PERSON>", "variant", "size", "onClick", "userAction", "getTableData", "tableData", "for<PERSON>ach", "user", "institutionInvites", "institutionInvite", "status", "push", "institutionId", "getUsersData", "response", "apiService", "get", "localPaginate", "length", "newPerPage", "array", "page_size", "page_number", "slice", "useEffect", "console", "log", "_id", "setStatus", "modalConfirm", "updatedData", "map", "invite", "remove", "patch", "toast", "error", "message", "success", "modalHide", "Modal", "show", "onHide", "Header", "closeButton", "Title", "char<PERSON>t", "toUpperCase", "Body", "Footer"], "sourceRoot": "", "ignoreList": []}