{"version": 3, "file": "static/chunks/pages/vspace/vspace_announcement/AnnouncementItem-190990dba3b49ca9.js", "mappings": "gFACA,4CACA,+CACA,WACA,OAAe,EAAQ,KAAoE,CAC3F,EACA,SAFsB,mGCGP,SAASA,EAAiBC,CAAU,MAqC9BC,IAnCnB,GAAM,CAAEA,MAAI,CAAE,CAAGD,EASjB,MACE,WAACE,EAAAA,CAAGA,CAAAA,CAACC,UAAU,MAAMC,GAAI,aACvB,UAACC,IAAIA,CAACC,KAAM,IAAc,OAAVL,EAAKM,IAAI,CAAC,gBAAeC,GAAI,EAAxCH,EAA8DJ,MAAAA,CAAlBA,EAAKM,IAAI,CAAC,UAA0CN,MAAAA,CAAlCA,CAAI,CAACQ,EAAYR,EAyBjF,UAAoB,OAAVA,EAAKM,IAAI,EAzBoE,CAAC,YAAmB,OAATN,EAAKS,GAAG,WAE1G,EAAMC,MAAM,EAAIV,EAAKU,MAAM,CAAC,EAAE,CAC7B,UAACC,MAAAA,CAAIC,IAAK,GAAwCZ,MAAAA,CAArCa,8BAAsB,CAAC,gBAAiC,OAAnBb,EAAKU,MAAM,CAAC,EAAE,CAACD,GAAG,EAAIK,IAAI,eAC1EZ,UAAU,gBACV,UAACa,IAAAA,CAAEb,UAAU,iCAGnB,WAACc,MAAAA,CAAId,UAAU,yBACb,UAACE,IAAIA,CAACC,KAAM,IAAc,OAAVL,EAAKM,IAAI,CAAC,gBAAeC,GAAI,EAAxCH,EAA8DJ,MAAAA,CAAlBA,EAAKM,IAAI,CAAC,UAA0CN,MAAAA,CAAlCA,CAAI,CAACiB,EAAYjB,EAW9D,UACD,OAAVA,EAAKM,IAAI,EAZsE,CAAC,YAAmB,OAATN,EAAKS,GAAG,WAC1GT,GAAQA,EAAKkB,KAAK,CAAGlB,EAAKkB,KAAK,CAAG,KAErC,UAACC,IAAAA,UACEnB,GAAQA,EAAKoB,WAAW,CAAGC,CAtBVC,IACxB,IAAMN,EAAMO,SAASC,aAAa,CAAC,OACnCR,EAAIS,SAAS,CAAGH,EAChB,IAAMI,EAASV,EAAIW,WAAW,EAAIX,EAAIY,SAAS,EAAI,GACnD,OAAQF,EAAOG,MAAM,CAXF,EAWKC,EAAiB,GAA2C,OAAxCJ,EAAOK,SAAS,CAAC,EAAGD,KAAoB,OAAOJ,CAC7F,GAiBqD1B,CAlB8B,CAkBzBoB,WAAW,EAAI,YAK3E", "sources": ["webpack://_N_E/?5904", "webpack://_N_E/./pages/vspace/vspace_announcement/AnnouncementItem.tsx"], "sourcesContent": ["\n    (window.__NEXT_P = window.__NEXT_P || []).push([\n      \"/vspace/vspace_announcement/AnnouncementItem\",\n      function () {\n        return require(\"private-next-pages/vspace/vspace_announcement/AnnouncementItem.tsx\");\n      }\n    ]);\n    if(module.hot) {\n      module.hot.dispose(function () {\n        window.__NEXT_P.push([\"/vspace/vspace_announcement/AnnouncementItem\"])\n      });\n    }\n  ", "//Import Library\r\nimport { Col } from \"react-bootstrap\";\r\nimport <PERSON> from \"next/link\";\r\n\r\nconst truncateLength = 260;\r\n\r\n//TODO: Remove the maths random number for image after updates completed with image upload\r\nexport default function AnnouncementItem(props: any) {\r\n\r\n  const { item } = props;\r\n\r\n  const getTrimmedString = (html: any) => {\r\n    const div = document.createElement(\"div\");\r\n    div.innerHTML = html;\r\n    const string = div.textContent || div.innerText || \"\";\r\n    return (string.length > truncateLength ? `${string.substring(0, truncateLength - 3)}...` : string);\r\n  }\r\n\r\n  return (\r\n    <Col className=\"p-0\" xs={12}>\r\n      <Link href={`/${item.type}/[...routes]`} as={`/${item.type}/show/${item[Parent_func(item)]}/update/${item._id}`}>\r\n\r\n        {(item.images && item.images[0]) ?\r\n          <img src={`${process.env.API_SERVER}/image/show/${item.images[0]._id}`} alt=\"announcement\"\r\n            className=\"announceImg\" />\r\n          : <i className=\"fa fa-bullhorn announceImg\" />}\r\n\r\n      </Link>\r\n      <div className=\"announceDesc\">\r\n        <Link href={`/${item.type}/[...routes]`} as={`/${item.type}/show/${item[newFunction(item)]}/update/${item._id}`}>\r\n          {item && item.title ? item.title : ''}\r\n        </Link>\r\n        <p>\r\n          {item && item.description ? getTrimmedString(item.description) : null}\r\n        </p>\r\n      </div>\r\n    </Col>\r\n  );\r\n}\r\n\r\nfunction newFunction(item: any) {\r\n  return `parent_${item.type}`;\r\n}\r\n\r\nfunction Parent_func(item: any) {\r\n  return `parent_${item.type}`;\r\n}\r\n"], "names": ["AnnouncementItem", "props", "item", "Col", "className", "xs", "Link", "href", "type", "as", "Parent_func", "_id", "images", "img", "src", "process", "alt", "i", "div", "newFunction", "title", "p", "description", "getTrimmedString", "html", "document", "createElement", "innerHTML", "string", "textContent", "innerText", "length", "truncate<PERSON><PERSON>th", "substring"], "sourceRoot": "", "ignoreList": []}