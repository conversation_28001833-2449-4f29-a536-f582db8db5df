(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[4285],{51432:(e,t,r)=>{(window.__NEXT_P=window.__NEXT_P||[]).push(["/hazard/HazardCoverSection",function(){return r(91112)}])},72800:(e,t,r)=>{"use strict";r.d(t,{A:()=>a});var s=r(37876),n=r(14232),i=r(31753);let a=e=>{let{t}=(0,i.Bd)("common"),r=parseInt("255"),[a,l]=(0,n.useState)(!1);return(0,s.jsxs)(s.Fragment,{children:[e.description?(0,s.jsx)("div",{dangerouslySetInnerHTML:((t,s)=>({__html:!s&&t.length>r?t.substring(0,r)+"...":e.description}))(e.description,a),className:"operationDesc"}):null,e.description&&e.description.length>r?(0,s.jsx)("button",{type:"button",className:"readMoreText",onClick:()=>l(!a),children:t(a?"readLess":"readMore")}):null]})}},91112:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>l});var s=r(37876),n=r(56970),i=r(37784),a=r(72800);let l=e=>{let t=e.hazardData,r=e.currentLang;return(0,s.jsx)(s.Fragment,{children:(0,s.jsxs)(n.A,{children:[(0,s.jsxs)(i.A,{className:"ps-4",children:[(0,s.jsx)("h2",{children:t.title&&t.title[r]?t.title[r]:""}),(0,s.jsx)(a.A,{description:t.description&&t.description[r]?t.description[r]:""})]}),(0,s.jsx)(i.A,{style:{display:"flex"},children:(0,s.jsx)("img",{src:t.picture,style:{width:"100%",height:"400px",backgroundSize:"cover"},alt:"banner"})})]})})}}},e=>{var t=t=>e(e.s=t);e.O(0,[636,6593,8792],()=>t(51432)),_N_E=e.O()}]);
//# sourceMappingURL=HazardCoverSection-784d6d679c3ad2cb.js.map