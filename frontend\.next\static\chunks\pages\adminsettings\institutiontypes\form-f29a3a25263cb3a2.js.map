{"version": 3, "file": "static/chunks/pages/adminsettings/institutiontypes/form-f29a3a25263cb3a2.js", "mappings": "gFACA,4CACA,uCACA,WACA,OAAe,EAAQ,KAA4D,CACnF,EACA,SAFsB,gPC8ItB,MAhI4B,IACxB,IAAMA,EAA0B,CAC5BC,MAAO,EACX,EAEM,CAACC,EAAYC,EAAc,CAAGC,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAkBJ,GAExDK,EAAoBC,EAAMC,MAAM,EAAwB,0BAApBD,EAAMC,MAAM,CAAC,EAAE,EAAgCD,EAAMC,MAAM,CAAC,EAAE,CAClG,GAAEC,CAAC,CAAE,CAAGC,CAAAA,EAAAA,EAAAA,EAAAA,CAAcA,CAAC,UAEvBC,EAAUC,CAAAA,EAAAA,EAAAA,MAAAA,CAAMA,CAAC,MAkBjBC,EAAe,MAAOC,QAGbX,MAGPY,EACAC,EANJF,EAAMG,cAAc,GACpB,IAAMC,EAAM,CACRhB,KAAK,OAAEC,GAAAA,OAAAA,EAAAA,EAAYD,KAAAA,EAAZC,EAAAA,GAAAA,EAAAA,EAAmBgB,GAAnBhB,CAAuB,EAClC,EAIIG,GACAU,EAAW,KADD,0CAEVD,EAAW,MAAMK,EAAAA,CAAUA,CAACC,KAAK,CAAC,oBAAoC,OAAhBd,EAAMC,MAAM,CAAC,EAAE,EAAIU,KAEzEF,EAAW,yCACXD,EAAW,MAAMK,EAAAA,CAAUA,CAACE,IAAI,CAAC,mBAAoBJ,IAErDH,GAAYA,EAASQ,GAAG,EAAE,EAC1BC,EAAKA,CAACC,OAAO,CAAChB,EAAEO,IAChBU,IAAAA,IAAW,CAAC,oCAERX,OAAAA,EAAAA,KAAAA,EAAAA,EAAUY,SAAS,CAAnBZ,GAAwB,KACxBS,EAD+B,EAC1BA,CAACI,KAAK,CAACnB,EAAE,yBAEde,EAAAA,EAAKA,CAACI,KAAK,CAACb,EAGxB,EAiBA,MAfAc,CAAAA,EAAAA,EAAAA,SAAAA,CAASA,CAAC,KACN,IAAMC,EAAwB,CAC1BC,MAAO,CAAC,EACRC,KAAM,CAAE9B,MAAO,KAAM,EACrB+B,MAAO,GACX,EACI3B,GAKA4B,CAJ+B,MADrB,IAEN,IAAMnB,EAA4B,MAAMK,EAAAA,CAAUA,CAACe,GAAG,CAAC,oBAAoC,OAAhB5B,EAAMC,MAAM,CAAC,EAAE,EAAIsB,GAC9F1B,EAAc,GAAgB,EAAE,GAAGgC,CAAS,CAAE,EAAhB,CAAmBrB,CAAQ,CAAC,GAC9D,GAGR,EAAG,EAAE,EAGD,UAACsB,MAAAA,UACG,UAACC,EAAAA,CAASA,CAAAA,CAACC,UAAU,WAAWC,KAAK,aACjC,UAACC,EAAAA,CAAIA,CAAAA,CACDC,MAAO,CACHC,UAAW,MACXC,UAAW,kEACf,WAEA,UAACC,EAAAA,CAAqBA,CAAAA,CAACC,SAAUjC,EAAckC,IAAKpC,EAASqC,cAAe7C,EAAY8C,mBAAoB,YACxG,WAACR,EAAAA,CAAIA,CAACS,IAAI,YACN,UAACC,EAAAA,CAAGA,CAAAA,UACA,UAACC,EAAAA,CAAGA,CAAAA,UACA,UAACX,EAAAA,CAAIA,CAACY,KAAK,WAAE5C,EAAE,0BAGvB,UAAC6C,KAAAA,CAAAA,GACD,UAACH,EAAAA,CAAGA,CAAAA,UACA,UAACC,EAAAA,CAAGA,CAAAA,CAACG,EAAE,IAACC,GAAI,EAAGC,GAAI,YACf,WAACC,EAAAA,CAAIA,CAACC,KAAK,YACP,UAACD,EAAAA,CAAIA,CAACE,KAAK,EAACrB,UAAU,0BAAkB9B,EAAE,sBAC1C,UAACoD,EAAAA,EAASA,CAAAA,CACNC,KAAK,QACLC,GAAG,QACHC,QAAQ,IACRC,MAAO9D,EAAWD,KAAK,CACvBgE,UAAYD,GAAkBE,YAAOF,GAAS,IAAI9C,IAAI,GACtDiD,aAAc,CACVF,UAAWzD,EAAE,qCACjB,EACA4D,SAlFnB,CAkF6BC,GAjF9C,GAAIC,EAAEC,MAAM,CAAE,CACV,GAAM,MAAEV,CAAI,OAAEG,CAAK,CAAE,CAAGM,EAAEC,MAAM,CAChCpE,EAAc,GAAgB,EAC1B,GAAGgC,CAAS,CACZ,CAAC0B,CAFyB,CAEpB,CAAEG,EACZ,EACJ,CACJ,WA+EwB,UAACd,EAAAA,CAAGA,CAAAA,CAACZ,UAAU,gBACX,WAACa,EAAAA,CAAGA,CAAAA,WACA,UAACqB,EAAAA,CAAMA,CAAAA,CAAClC,UAAU,OAAOmC,KAAK,SAASC,QAAQ,mBAC1ClE,EAAE,YAEP,UAACgE,EAAAA,CAAMA,CAAAA,CAAClC,UAAU,OAAOqC,QAlGpC,CAkG6CC,IAjG9DzE,EAAcH,GAEd6E,OAAOC,QAAQ,CAAC,EAAG,EACvB,EA8FgFJ,QAAQ,gBACnDlE,EAAE,WAEP,UAACuE,IAAIA,CACDC,KAAK,6BACLC,GAAK,OAFJF,oCAID,UAACP,EAAAA,CAAMA,CAAAA,CAACE,QAAQ,qBAAalE,EAAE,6BAUvE,0GC3IA,IAAM0E,EAAwBC,EAAAA,OAAb,GAA6B,CAAC,GAK5CrC,IALyB,IAAoB,WAC9CR,CAAS,UACT8C,CAAQ,CACRH,GAAII,EAAY,KAAK,CACrB,GAAG/E,EACJ,GAEC,OAAO,EADIgF,CAAAA,EAAAA,EAAAA,EAAAA,CAAkBA,CAACF,EAAU,aACpBG,CAAAA,EAAAA,EAAAA,GAAAA,CAAIA,CAACF,EAAW,CAClCvC,IAAKA,EACLR,UAAWkD,IAAWlD,EAAW8C,GACjC,GAAG9E,CAAK,EAEZ,EACA4E,GAASO,WAAW,CAAG,WCbvB,IAAMC,EAA0BP,EAAAA,SAAb,CAA6B,CAAC,GAK9CrC,MAL2B,EAAoB,WAChDR,CAAS,UACT8C,CAAQ,CACRH,GAAII,EAAY,KAAK,CACrB,GAAG/E,EACJ,GAEC,OADA8E,EAAWE,CAAAA,EAAAA,EAAAA,EAAAA,CAAkBA,CACX,EADsB,eACpBC,CAAAA,EAAAA,EAAAA,GAAAA,CAAIA,CAACF,EAAW,CAClCvC,IAAKA,EACLR,UAAWkD,IAAWlD,EAAW8C,GACjC,GAAG9E,CAAK,EAEZ,GACAoF,EAJyBF,WAIH,CAAG,4BCXzB,IAAMG,EAA0BR,EAAAA,SAAb,CAA6B,CAAC,GAM9CrC,MAN2B,EAAoB,UAChDsC,CAAQ,WACR9C,CAAS,CAET2C,CADA,EACII,EAAY,KAAK,CACrB,GAAG/E,EACJ,GACOsF,EAASN,CAAAA,EAAAA,EAAAA,EAAAA,CAAkBA,CAACF,EAAU,eACtCS,EAAeC,CAAAA,EAAAA,EAAAA,OAAAA,CAAOA,CAAC,IAAO,EAClCC,mBAAoBH,EACtB,EAAI,CAACA,EAAO,EACZ,MAAoBL,CAAAA,EAAAA,EAAAA,GAAAA,CAAIA,CAACS,EAAAA,CAAiBA,CAACC,QAAQ,CAAE,CACnDjC,MAAO6B,EACPK,SAAuBX,CAAb,EAAaA,EAAAA,GAAAA,CAAIA,CAACF,EAAW,CACrCvC,IAAKA,EACL,GAAGxC,CAAK,CACRgC,UAAWkD,IAAWlD,EAAWsD,EACnC,EACF,EACF,GACAD,EAAWF,GAJgBD,QAIL,CAAG,aCtBzB,IAAMW,EAAuBhB,EAAAA,MAAb,IAA6B,CAC7C,CACA,EAMGrC,GARwB,KAE1B,UACCsC,CAAQ,WACR9C,CAAS,SACToC,CAAO,CACPO,GAAII,EAAY,KAAK,CACrB,GAAG/E,EACJ,GACOsF,EAASN,CAAAA,EAAAA,EAAAA,EAAAA,CAAkBA,CAACF,EAAU,YAC5C,MAAoBG,CAAb,EAAaA,EAAAA,GAAAA,CAAIA,CAACF,EAAP,CAChBvC,IAAKA,EACLR,UAAWkD,IAAWd,EAAU,GAAaA,MAAAA,CAAVkB,EAAO,EAArBJ,GAAgC,OAARd,CAX0G,EAW9FkB,EAAQtD,GACjE,GAAGhC,CAAK,EAEZ,GACA6F,EAAQV,WAAW,CAAG,UChBtB,IAAMW,EAA8BjB,EAAAA,UAAgB,CAAC,EAA9B,CAKpBrC,QALmD,EAApB,SAChCR,CAAS,CACT8C,UAAQ,CACRH,GAAII,EAAY,KAAK,CACrB,GAAG/E,EACJ,GAEC,OADA8E,EAAWE,CAAAA,EAAAA,EAAAA,EAAAA,CAAkBA,CAACF,EAAU,oBACpBG,CAAAA,EAAAA,EAAAA,GAAAA,CAAIA,CAACF,EAAW,CAClCvC,IAAKA,EACLR,UAAWkD,IAAWlD,EAAW8C,GACjC,GAAG9E,CAAK,EAEZ,GACA8F,EAJyBZ,WAIC,CAAG,iBCb7B,IAAMa,EAAwBlB,EAAAA,OAAb,GAA6B,CAAC,GAK5CrC,IALyB,IAAoB,WAC9CR,CAAS,UACT8C,CAAQ,CACRH,GAAII,EAAY,GAAG,CACnB,GAAG/E,EACJ,GAEC,OADA8E,EAAWE,CAAAA,EAAAA,EAAAA,EAAAA,CAAkBA,CACX,EADsB,aACpBC,CAAAA,EAAAA,EAAAA,GAAAA,CAAIA,CAACF,EAAW,CAClCvC,IAAKA,EACLR,UAAWkD,IAAWlD,EAAW8C,GACjC,GAAG9E,CAAK,EAEZ,GACA+F,EAASZ,WAAW,CAAG,0BCZvB,IAAMa,EAAgBC,CAAAA,EAAAA,EAAAA,CAAAA,CAAgBA,CAAC,MACjCC,EAA4BrB,EAAAA,UAAgB,CAA7B,CAA8B,EAKhDrC,QAL6B,WAC9BR,CAAS,UACT8C,CAAQ,CACRH,GAAII,EAAYiB,CAAa,CAC7B,GAAGhG,EACJ,GAEC,OAAO,EADIgF,CAAAA,EAAAA,EAAAA,EAAAA,CAAkBA,CAACF,EAAU,iBACpBG,CAAAA,EAAAA,EAAAA,GAAAA,CAAIA,CAACF,EAAW,CAClCvC,IAAKA,EACLR,UAAWkD,IAAWlD,EAAW8C,GACjC,GAAG9E,CAAK,EAEZ,GACAkG,EAAaf,WAAW,CAAG,eCf3B,IAAMgB,EAAwBtB,EAAAA,OAAb,GAA6B,CAAC,GAK5CrC,IALyB,IAAoB,WAC9CR,CAAS,UACT8C,CAAQ,CACRH,GAAII,EAAY,GAAG,CACnB,GAAG/E,EACJ,GAEC,OADA8E,EAAWE,CAAAA,EAAAA,EAAAA,EAAAA,CAAkBA,CAACF,EAAU,aACpBG,CAAAA,EAAAA,EAAAA,GAAAA,CAAIA,CAACF,EAAW,CAClCvC,IAAKA,EACLR,UAAWkD,IAAWlD,EAAW8C,GACjC,GAAG9E,CAAK,EAEZ,GACAmG,EAJyBjB,WAIL,CAAG,WCZvB,IAAMkB,EAAgBH,CAAAA,EAAAA,EAAAA,CAAAA,CAAgBA,CAAC,MACjCI,EAAyBxB,EAAAA,QAAb,EAA6B,CAAC,GAK7CrC,KAL0B,GAAoB,WAC/CR,CAAS,UACT8C,CAAQ,CACRH,GAAII,EAAYqB,CAAa,CAC7B,GAAGpG,EACJ,GAEC,OADA8E,EAAWE,CAAAA,EAAAA,EAAAA,EAAAA,CAAkBA,CAACF,EAAU,cACpBG,CAAAA,EAAAA,EAAAA,GAAAA,CAAIA,CAACF,EAAW,CAClCvC,IAAKA,EACLR,UAAWkD,IAAWlD,EAAW8C,GACjC,GAAG9E,CAAK,EAEZ,GACAqG,EAJyBnB,WAIJ,CAAG,YCNxB,IAAMhD,EAAoB2C,EAAAA,GAAb,OAA6B,CAAC,GAAnB,QAAoB,UAC1CC,CAAQ,WACR9C,CAAS,IACTsE,CAAE,MACFC,CAAI,CACJC,QAAM,MACNC,GAAO,CAAK,UACZb,CAAQ,CAERjB,CADA,EACII,EAAY,KAAK,CACrB,GAAG/E,EACJ,GACOsF,EAASN,CAAAA,EAAAA,EAAAA,EAAAA,CAAkBA,CAACF,EAAU,QAC5C,MAAoBG,CAAb,EAAaA,EAAAA,GAAAA,CAAIA,CAACF,EAAP,CAChBvC,IAAKA,EACL,GAAGxC,CAAK,CACRgC,UAAWkD,IAAWlD,EAAWsD,EAAQgB,GAAM,MAAS,GAAnCpB,GAAmC,CAAHoB,GAAMC,GAAQ,QAAa,OAALA,GAAQC,GAAU,UAAiB,OAAPA,IACvGZ,IATyJ,KAS/Ia,EAAoBxB,CAAAA,EAAAA,EAAb,GAAaA,CAAIA,CAACL,EAAU,CAC3CgB,GAD0B,MAAehB,CAE3C,GAAKgB,CACP,EACF,GACA1D,EAAKiD,WAAW,CAAG,OACnB,MAAeuB,OAAOC,MAAM,CAACzE,EAAM,CACjC0E,INhBaf,CMgBRA,CACL/C,KNjBoB+C,CKDPQ,CLCQ,CMkBrBQ,EAFYhB,KDjBUQ,EAAC,CCmBbH,CACVvD,CAFgB0D,ITpBHzB,CSsBPA,CACNH,GHrByByB,EAAC,CNFLtB,CSwBrBkC,CTxBsB,GSsBRlC,CFtBDuB,CFAQJ,CIyBrBgB,CJzBsB,GIuBRhB,EFvBOI,CLSRd,CKTS,CE0BtB2B,EAFcb,KRxBDf,CQ0BLA,CACR6B,CPlBwB,GOgBN5B,IRzBKD,EAAC,CGAXU,CK2BDA,CADMV,CAElB,EAAC,SL5B0BU,EAAC,GK2BFA,0ICwCrB,IAAMoB,EAAQ,CACnBC,WA1C4C,OAAC,MAC7C5D,CAAI,eACJ6D,CAAa,UACbtD,CAAQ,cACRD,CAAY,UACZ+B,CAAQ,CACT,GACO,QAAEyB,CAAM,CAAEC,SAAO,CAAE,CAAGC,CAAAA,EAAAA,EAAAA,EAAAA,CAAgBA,GACtCC,EAAWF,CAAO,CAAC/D,EAAK,EAAI8D,CAAM,CAAC9D,EAAK,CAGzBsB,EAAAA,OAAa,CAAC,IAAO,OAAEtB,EAAK,EAAI,CAACA,EAAK,EAG3D,IAAMkE,EAAoB5C,EAAAA,QAAc,CAAC6C,GAAG,CAAC9B,EAAU,GACrD,EAAIf,cAAoB,CAAC8C,IAEnBC,IAF2B,KAxC5BA,CAAmB,EAC1B,MAAO,iBAAO5H,GAAgC,OAAVA,CACtC,EAwCmB2H,EAAM3H,KAAK,EACf6E,CADkB,CAClBA,YAAkB,CAAC8C,EAA6C,MACrEpE,EACA,GAAGoE,EAAM3H,KACX,GAGG2H,GAGT,MACE,WAAC7F,MAAAA,WACC,UAACA,MAAAA,CAAIE,UAAU,uBACZyF,IAEFD,GACC,UAAC1F,MAAAA,CAAIE,UAAU,oCACZ6B,GAAiB,kBAAOwD,CAAM,CAAC9D,EAAK,CAAgB8D,CAAM,CAAC9D,EAAK,CAAGK,OAAOyD,CAAM,CAAC9D,GAAK,MAKjG,EAIEsE,UAhE0C,OAAC,IAAErE,CAAE,OAAEsE,CAAK,OAAEpE,CAAK,CAAEH,MAAI,CAAEwE,UAAQ,CAAE,GACzE,QAAEC,CAAM,eAAEC,CAAa,CAAE,CAAGV,CAAAA,EAAAA,EAAAA,EAAAA,CAAgBA,GAC5CW,EAAY3E,GAAQC,EAE1B,MACE,UAACL,EAAAA,CAAIA,CAACgF,KAAK,EACThE,KAAK,QACLX,GAAIA,EACJsE,MAAOA,EACPpE,MAAOA,EACPH,KAAM2E,EACNE,QAASJ,CAAM,CAACE,EAAU,GAAKxE,EAC/BI,SAAU,IACRmE,EAAcC,EAAWlE,EAAEC,MAAM,CAACP,KAAK,CACzC,EACAqE,SAAUA,EACVM,MAAM,KAGZ,CA8CA,EAAE,ECzEcC,CAAAA,CACLhF,EAAAA,EAAAA,CACEiF,EAAAA,EAAAA,gGCeb,IAAMjG,EAAwBkG,CAAAA,EAAAA,EAAAA,UAAAA,CAAUA,CAA8C,CAACxI,EAAOwC,KAC5F,GAAM,UAAEoD,CAAQ,UAAErD,CAAQ,cAAEkG,CAAY,WAAEzG,CAAS,YAAE0G,CAAU,CAAEjG,eAAa,CAAE,GAAGkG,EAAM,CAAG3I,EAGtF4I,EAAmBC,EAAAA,EAAU,GAAGC,KAAK,CAAC,CAAC,GAE7C,MACE,UAACC,EAAAA,EAAMA,CAAAA,CACLtG,cAAeA,GAAiB,CAAC,EACjCmG,iBAAkBA,EAClBrG,SAAU,CAACyF,EAA6BgB,KAEtC,IAAMC,EAAuB,CAC3BvI,eAAgB,KAAO,EACvBwI,gBAAiB,KAAO,EACxBC,cAAe,KACflF,OAAQ,KACRmF,YAAa,IAAIC,MAAM,UACvBC,SAAS,EACTC,WAAY,GACZC,kBAAkB,EAClBC,WAAY,EACZC,WAAW,EACXC,UAAWC,KAAKC,GAAG,GACnB1F,KAAM,SACN2F,mBAAoB,KAAM,EAC1BC,qBAAsB,IAAM,GAC5BC,QAAS,KAAO,CAClB,EAEIzH,GAEFA,EAAS0G,EAAWjB,EAAQgB,CAFhB,CAIhB,EACC,GAAGL,CAAI,UAEP,GACC,UAACxF,EAAAA,EAAIA,CAAAA,CACHX,IAAKA,EACLD,SAAU0H,EAAY3J,YAAY,CAClCmI,aAAcA,EACdzG,UAAWA,EACX0G,WAAYA,WAES,YAApB,OAAO9C,EAA0BA,EAASqE,GAAerE,KAKpE,GAEAtD,EAAsB6C,WAAW,CAAG,wBAEpC,MAAe7C,qBAAqBA,EAAC,sFClF9B,IAAMgB,EAAY,OAAC,MACxBC,CAAI,IACJC,CAAE,UACFC,CAAQ,WACRE,CAAS,cACTE,CAAY,CACZC,UAAQ,OACRJ,CAAK,IACLiB,CAAE,WACFuF,CAAS,MACTC,CAAI,SACJC,CAAO,CACP,GAAGpK,EACC,GAuBJ,MACE,UAACqK,EAAAA,EAAKA,CAAAA,CAAC9G,KAAMA,EAAM+G,SAtBJ,CAsBcA,GApB7B,IAAMC,EAA2B,UAAf,OAAOC,EAAmBA,EAAM5G,OAAO4G,GAAO,WAChE,GAAiB,EAACA,GAAOD,IAAR,GAAkB3J,IAAI,EAAO,CAAC,CACtCiD,EAD0C,KAC1CA,EAAAA,KAAAA,EAAAA,EAAcF,SAAAA,GAAa,EAA3BE,uBAGLF,GAAa,CAACA,EAAU6G,GACnB3G,GADyB,IACzBA,EAAAA,KAAAA,EAAAA,EAAcF,SAAAA,GAAa,EAA3BE,cAGLuG,GAAWI,GAET,CAACC,CAFa,GACAC,OAAON,GACdO,IAAI,CAACH,GACP3G,GADa,IACbA,EAAAA,KAAAA,EAAAA,EAAcuG,OAAAA,GAAW,IAAzBvG,mBAKb,WAIK,OAAC,OAAE+G,CAAK,MAAEC,CAAI,CAAO,SACpB,iCACE,UAAC1H,EAAAA,CAAIA,CAAC2H,OAAO,EACV,GAAGF,CAAK,CACR,GAAG5K,CAAK,CACTwD,GAAIA,EACJmB,GAAIA,GAAM,QACVwF,KAAMA,EACNY,UAAWF,EAAKvD,OAAO,EAAI,CAAC,CAACuD,EAAKxJ,KAAK,CACvCyC,SAAU,IACR8G,EAAM9G,QAAQ,CAACE,GACXF,GAAUA,EAASE,EACzB,EACAN,WAAiBsH,IAAVtH,EAAsBA,EAAQkH,EAAMlH,KAAK,GAEjDmH,EAAKvD,OAAO,EAAIuD,EAAKxJ,KAAK,CACzB,UAAC8B,EAAAA,CAAIA,CAAC2H,OAAO,CAACG,QAAQ,EAAC9G,KAAK,mBACzB0G,EAAKxJ,KAAK,GAEX,UAKd,EAAE,EAIyB,OAAC,MAC1BkC,CAAI,IACJC,CAAE,UACFC,CAAQ,cACRI,CAAY,UACZC,CAAQ,OACRJ,CAAK,UACLkC,CAAQ,CACR,GAAG5F,EACC,GAUJ,MACE,UAACqK,EAAAA,EAAKA,CAAAA,CAAC9G,KAAMA,EAAM+G,SATJ,CAScA,GAR7B,GAAI7G,GAAa,EAAC+G,GAAe,IAAhB,CAAQA,CAAQ,CAAC,CAChC,EADoC,IAC7B3G,OAAAA,EAAAA,KAAAA,EAAAA,EAAcF,SAAAA,GAAa,EAA3BE,sBAIX,WAIK,OAAC,OAAE+G,CAAK,MAAEC,CAAI,CAAO,SACpB,iCACE,UAAC1H,EAAAA,CAAIA,CAAC2H,OAAO,EACXnG,GAAG,SACF,GAAGiG,CAAK,CACR,GAAG5K,CAAK,CACTwD,GAAIA,EACJuH,UAAWF,EAAKvD,OAAO,EAAI,CAAC,CAACuD,EAAKxJ,KAAK,CACvCyC,SAAU,IACR8G,EAAM9G,QAAQ,CAACE,GACXF,GAAUA,EAASE,EACzB,EACAN,WAAiBsH,IAAVtH,EAAsBA,EAAQkH,EAAMlH,KAAK,UAE/CkC,IAEFiF,EAAKvD,OAAO,EAAIuD,EAAKxJ,KAAK,CACzB,UAAC8B,EAAAA,CAAIA,CAAC2H,OAAO,CAACG,QAAQ,EAAC9G,KAAK,mBACzB0G,EAAKxJ,KAAK,GAEX,UAKd,EAAE,+CCnHF,IAAM6J,EAAuBrG,QAAb,CAAaA,UAAF,GAAqB,CAAC,MACjDqG,EAAQ/F,WAAW,CAAG,oBACtB,MAAe+F,OAAOA,EAAC", "sources": ["webpack://_N_E/?8066", "webpack://_N_E/./pages/adminsettings/institutiontypes/form.tsx", "webpack://_N_E/./node_modules/react-bootstrap/esm/CardBody.js", "webpack://_N_E/./node_modules/react-bootstrap/esm/CardFooter.js", "webpack://_N_E/./node_modules/react-bootstrap/esm/CardHeader.js", "webpack://_N_E/./node_modules/react-bootstrap/esm/CardImg.js", "webpack://_N_E/./node_modules/react-bootstrap/esm/CardImgOverlay.js", "webpack://_N_E/./node_modules/react-bootstrap/esm/CardLink.js", "webpack://_N_E/./node_modules/react-bootstrap/esm/CardSubtitle.js", "webpack://_N_E/./node_modules/react-bootstrap/esm/CardText.js", "webpack://_N_E/./node_modules/react-bootstrap/esm/CardTitle.js", "webpack://_N_E/./node_modules/react-bootstrap/esm/Card.js", "webpack://_N_E/./components/common/FormikRadio.tsx", "webpack://_N_E/./components/common/FormValidation.tsx", "webpack://_N_E/./components/common/ValidationFormWrapper.tsx", "webpack://_N_E/./components/common/FormikTextInput.tsx", "webpack://_N_E/./node_modules/react-bootstrap/esm/CardHeaderContext.js"], "sourcesContent": ["\n    (window.__NEXT_P = window.__NEXT_P || []).push([\n      \"/adminsettings/institutiontypes/form\",\n      function () {\n        return require(\"private-next-pages/adminsettings/institutiontypes/form.tsx\");\n      }\n    ]);\n    if(module.hot) {\n      module.hot.dispose(function () {\n        window.__NEXT_P.push([\"/adminsettings/institutiontypes/form\"])\n      });\n    }\n  ", "//Import Library\r\nimport { useState, useRef, useEffect } from \"react\";\r\nimport { Container, Card, Row, Col, Form, Button } from \"react-bootstrap\";\r\nimport { TextInput } from \"../../../components/common/FormValidation\";\r\nimport ValidationFormWrapper from \"../../../components/common/ValidationFormWrapper\";\r\nimport Router from \"next/router\";\r\nimport toast from 'react-hot-toast';\r\nimport Link from \"next/link\";\r\n\r\n//Import services/components\r\nimport apiService from \"../../../services/apiService\";\r\nimport { InstitutionType } from \"../../../types\";\r\nimport { useTranslation } from 'next-i18next';\r\n\r\ninterface InstitutionTypeFormProps {\r\n    [key: string]: any;\r\n}\r\n\r\nconst InstitutionTypeForm = (props: InstitutionTypeFormProps) => {\r\n    const _initialinstitutionType = {\r\n        title: \"\",\r\n    };\r\n\r\n    const [initialVal, setInitialVal] = useState<InstitutionType>(_initialinstitutionType);\r\n\r\n    const editform: boolean = props.routes && props.routes[0] === \"edit_institution_type\" && props.routes[1];\r\n    const { t } = useTranslation('common');\r\n\r\n    const formRef = useRef(null);\r\n\r\n    const resetHandler = () => {\r\n        setInitialVal(_initialinstitutionType);\r\n        // Reset validation state (Formik handles this automatically)\r\n        window.scrollTo(0, 0);\r\n    };\r\n\r\n    const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement>) => {\r\n        if (e.target) {\r\n            const { name, value } = e.target;\r\n            setInitialVal((prevState) => ({\r\n                ...prevState,\r\n                [name]: value,\r\n            }));\r\n        }\r\n    };\r\n\r\n    const handleSubmit = async (event: any) => {\r\n        event.preventDefault();\r\n        const obj = {\r\n            title: initialVal?.title?.trim(),\r\n        };\r\n\r\n        let response;\r\n        let toastMsg;\r\n        if (editform) {\r\n            toastMsg = \"adminsetting.Organisationtypes.updatesuccess\";\r\n            response = await apiService.patch(`/institutiontype/${props.routes[1]}`, obj);\r\n        } else {\r\n            toastMsg = \"adminsetting.Organisationtypes.success\";\r\n            response = await apiService.post(\"/institutiontype\", obj);\r\n        }\r\n        if (response && response._id) {\r\n            toast.success(t(toastMsg));\r\n            Router.push(\"/adminsettings/institution_type\");\r\n        } else {\r\n            if (response?.errorCode === 11000) {\r\n                toast.error(t(\"duplicatesNotAllowed\"));\r\n            } else {\r\n                toast.error(response);\r\n            }\r\n        }\r\n    };\r\n\r\n    useEffect(() => {\r\n        const institutionTypeParams = {\r\n            query: {},\r\n            sort: { title: \"asc\" },\r\n            limit: \"~\",\r\n        };\r\n        if (editform) {\r\n            const getInstitutionTypeData = async () => {\r\n                const response: InstitutionType = await apiService.get(`/institutiontype/${props.routes[1]}`, institutionTypeParams);\r\n                setInitialVal((prevState) => ({ ...prevState, ...response }));\r\n            };\r\n            getInstitutionTypeData();\r\n        }\r\n    }, []);\r\n\r\n    return (\r\n        <div>\r\n            <Container className=\"formCard\" fluid>\r\n                <Card\r\n                    style={{\r\n                        marginTop: \"5px\",\r\n                        boxShadow: \"0 4px 8px 0 rgba(0, 0, 0, 0.2), 0 6px 20px 0 rgba(0, 0, 0, 0.19)\",\r\n                    }}\r\n                >\r\n                    <ValidationFormWrapper onSubmit={handleSubmit} ref={formRef} initialValues={initialVal} enableReinitialize={true}>\r\n                        <Card.Body>\r\n                            <Row>\r\n                                <Col>\r\n                                    <Card.Title>{t(\"OrganisationType\")}</Card.Title>\r\n                                </Col>\r\n                            </Row>\r\n                            <hr />\r\n                            <Row>\r\n                                <Col md lg={6} sm={12}>\r\n                                    <Form.Group>\r\n                                        <Form.Label className=\"required-field\">{t(\"OrganisationType\")}</Form.Label>\r\n                                        <TextInput\r\n                                            name=\"title\"\r\n                                            id=\"title\"\r\n                                            required\r\n                                            value={initialVal.title}\r\n                                            validator={(value: string) => String(value || '').trim() !== \"\"}\r\n                                            errorMessage={{\r\n                                                validator: t(\"adminsetting.Organisationtypes.add\"),\r\n                                            }}\r\n                                            onChange={handleChange}\r\n                                        />\r\n                                    </Form.Group>\r\n                                </Col>\r\n                            </Row>\r\n                            <Row className=\"my-4\">\r\n                                <Col>\r\n                                    <Button className=\"me-2\" type=\"submit\" variant=\"primary\">\r\n                                        {t(\"submit\")}\r\n                                    </Button>\r\n                                    <Button className=\"me-2\" onClick={resetHandler} variant=\"info\">\r\n                                        {t(\"reset\")}\r\n                                    </Button>\r\n                                    <Link\r\n                                        href=\"/adminsettings/[...routes]\"\r\n                                        as={`/adminsettings/institution_type`}\r\n                                        >\r\n                                        <Button variant=\"secondary\">{t(\"Cancel\")}</Button>\r\n                                    </Link>\r\n                                </Col>\r\n                            </Row>\r\n                        </Card.Body>\r\n                    </ValidationFormWrapper>\r\n                </Card>\r\n            </Container>\r\n        </div>\r\n    );\r\n};\r\nexport default InstitutionTypeForm;\r\n", "\"use client\";\n\nimport * as React from 'react';\nimport classNames from 'classnames';\nimport { useBootstrapPrefix } from './ThemeProvider';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst CardBody = /*#__PURE__*/React.forwardRef(({\n  className,\n  bsPrefix,\n  as: Component = 'div',\n  ...props\n}, ref) => {\n  bsPrefix = useBootstrapPrefix(bsPrefix, 'card-body');\n  return /*#__PURE__*/_jsx(Component, {\n    ref: ref,\n    className: classNames(className, bsPrefix),\n    ...props\n  });\n});\nCardBody.displayName = 'CardBody';\nexport default CardBody;", "\"use client\";\n\nimport * as React from 'react';\nimport classNames from 'classnames';\nimport { useBootstrapPrefix } from './ThemeProvider';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst CardFooter = /*#__PURE__*/React.forwardRef(({\n  className,\n  bsPrefix,\n  as: Component = 'div',\n  ...props\n}, ref) => {\n  bsPrefix = useBootstrapPrefix(bsPrefix, 'card-footer');\n  return /*#__PURE__*/_jsx(Component, {\n    ref: ref,\n    className: classNames(className, bsPrefix),\n    ...props\n  });\n});\nCardFooter.displayName = 'CardFooter';\nexport default CardFooter;", "\"use client\";\n\nimport classNames from 'classnames';\nimport * as React from 'react';\nimport { useMemo } from 'react';\nimport { useBootstrapPrefix } from './ThemeProvider';\nimport CardHeaderContext from './CardHeaderContext';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst CardHeader = /*#__PURE__*/React.forwardRef(({\n  bsPrefix,\n  className,\n  // Need to define the default \"as\" during prop destructuring to be compatible with styled-components github.com/react-bootstrap/react-bootstrap/issues/3595\n  as: Component = 'div',\n  ...props\n}, ref) => {\n  const prefix = useBootstrapPrefix(bsPrefix, 'card-header');\n  const contextValue = useMemo(() => ({\n    cardHeaderBsPrefix: prefix\n  }), [prefix]);\n  return /*#__PURE__*/_jsx(CardHeaderContext.Provider, {\n    value: contextValue,\n    children: /*#__PURE__*/_jsx(Component, {\n      ref: ref,\n      ...props,\n      className: classNames(className, prefix)\n    })\n  });\n});\nCardHeader.displayName = 'CardHeader';\nexport default CardHeader;", "\"use client\";\n\nimport classNames from 'classnames';\nimport * as React from 'react';\nimport { useBootstrapPrefix } from './ThemeProvider';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst CardImg = /*#__PURE__*/React.forwardRef(\n// Need to define the default \"as\" during prop destructuring to be compatible with styled-components github.com/react-bootstrap/react-bootstrap/issues/3595\n({\n  bsPrefix,\n  className,\n  variant,\n  as: Component = 'img',\n  ...props\n}, ref) => {\n  const prefix = useBootstrapPrefix(bsPrefix, 'card-img');\n  return /*#__PURE__*/_jsx(Component, {\n    ref: ref,\n    className: classNames(variant ? `${prefix}-${variant}` : prefix, className),\n    ...props\n  });\n});\nCardImg.displayName = 'CardImg';\nexport default CardImg;", "\"use client\";\n\nimport * as React from 'react';\nimport classNames from 'classnames';\nimport { useBootstrapPrefix } from './ThemeProvider';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst CardImgOverlay = /*#__PURE__*/React.forwardRef(({\n  className,\n  bsPrefix,\n  as: Component = 'div',\n  ...props\n}, ref) => {\n  bsPrefix = useBootstrapPrefix(bsPrefix, 'card-img-overlay');\n  return /*#__PURE__*/_jsx(Component, {\n    ref: ref,\n    className: classNames(className, bsPrefix),\n    ...props\n  });\n});\nCardImgOverlay.displayName = 'CardImgOverlay';\nexport default CardImgOverlay;", "\"use client\";\n\nimport * as React from 'react';\nimport classNames from 'classnames';\nimport { useBootstrapPrefix } from './ThemeProvider';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst CardLink = /*#__PURE__*/React.forwardRef(({\n  className,\n  bsPrefix,\n  as: Component = 'a',\n  ...props\n}, ref) => {\n  bsPrefix = useBootstrapPrefix(bsPrefix, 'card-link');\n  return /*#__PURE__*/_jsx(Component, {\n    ref: ref,\n    className: classNames(className, bsPrefix),\n    ...props\n  });\n});\nCardLink.displayName = 'CardLink';\nexport default CardLink;", "\"use client\";\n\nimport * as React from 'react';\nimport classNames from 'classnames';\nimport { useBootstrapPrefix } from './ThemeProvider';\nimport divWithClassName from './divWithClassName';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst DivStyledAsH6 = divWithClassName('h6');\nconst CardSubtitle = /*#__PURE__*/React.forwardRef(({\n  className,\n  bsPrefix,\n  as: Component = DivStyledAsH6,\n  ...props\n}, ref) => {\n  bsPrefix = useBootstrapPrefix(bsPrefix, 'card-subtitle');\n  return /*#__PURE__*/_jsx(Component, {\n    ref: ref,\n    className: classNames(className, bsPrefix),\n    ...props\n  });\n});\nCardSubtitle.displayName = 'CardSubtitle';\nexport default CardSubtitle;", "\"use client\";\n\nimport * as React from 'react';\nimport classNames from 'classnames';\nimport { useBootstrapPrefix } from './ThemeProvider';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst CardText = /*#__PURE__*/React.forwardRef(({\n  className,\n  bsPrefix,\n  as: Component = 'p',\n  ...props\n}, ref) => {\n  bsPrefix = useBootstrapPrefix(bsPrefix, 'card-text');\n  return /*#__PURE__*/_jsx(Component, {\n    ref: ref,\n    className: classNames(className, bsPrefix),\n    ...props\n  });\n});\nCardText.displayName = 'CardText';\nexport default CardText;", "\"use client\";\n\nimport * as React from 'react';\nimport classNames from 'classnames';\nimport { useBootstrapPrefix } from './ThemeProvider';\nimport divWithClassName from './divWithClassName';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst DivStyledAsH5 = divWithClassName('h5');\nconst CardTitle = /*#__PURE__*/React.forwardRef(({\n  className,\n  bsPrefix,\n  as: Component = DivStyledAsH5,\n  ...props\n}, ref) => {\n  bsPrefix = useBootstrapPrefix(bsPrefix, 'card-title');\n  return /*#__PURE__*/_jsx(Component, {\n    ref: ref,\n    className: classNames(className, bsPrefix),\n    ...props\n  });\n});\nCardTitle.displayName = 'CardTitle';\nexport default CardTitle;", "\"use client\";\n\nimport classNames from 'classnames';\nimport * as React from 'react';\nimport { useBootstrapPrefix } from './ThemeProvider';\nimport CardBody from './CardBody';\nimport CardFooter from './CardFooter';\nimport CardHeader from './CardHeader';\nimport CardImg from './CardImg';\nimport CardImgOverlay from './CardImgOverlay';\nimport CardLink from './CardLink';\nimport CardSubtitle from './CardSubtitle';\nimport CardText from './CardText';\nimport CardTitle from './CardTitle';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst Card = /*#__PURE__*/React.forwardRef(({\n  bsPrefix,\n  className,\n  bg,\n  text,\n  border,\n  body = false,\n  children,\n  // Need to define the default \"as\" during prop destructuring to be compatible with styled-components github.com/react-bootstrap/react-bootstrap/issues/3595\n  as: Component = 'div',\n  ...props\n}, ref) => {\n  const prefix = useBootstrapPrefix(bsPrefix, 'card');\n  return /*#__PURE__*/_jsx(Component, {\n    ref: ref,\n    ...props,\n    className: classNames(className, prefix, bg && `bg-${bg}`, text && `text-${text}`, border && `border-${border}`),\n    children: body ? /*#__PURE__*/_jsx(CardBody, {\n      children: children\n    }) : children\n  });\n});\nCard.displayName = 'Card';\nexport default Object.assign(Card, {\n  Img: CardImg,\n  Title: CardTitle,\n  Subtitle: CardSubtitle,\n  Body: CardBody,\n  Link: CardLink,\n  Text: CardText,\n  Header: CardHeader,\n  Footer: CardFooter,\n  ImgOverlay: CardImgOverlay\n});", "import React from 'react';\r\nimport { useFormikContext, Field } from 'formik';\r\nimport { Form } from 'react-bootstrap';\r\n\r\ninterface RadioItemProps {\r\n  id: string;\r\n  label: string;\r\n  value: string;\r\n  name?: string;\r\n  disabled?: boolean;\r\n}\r\n\r\ninterface RadioGroupProps {\r\n  name: string;\r\n  valueSelected: string;\r\n  onChange: (e: React.ChangeEvent<HTMLInputElement>) => void;\r\n  errorMessage?: string;\r\n  children: React.ReactNode;\r\n}\r\n\r\n// Type Guard to ensure child.props is an object\r\nfunction isObject(props: any): props is { [key: string]: any } {\r\n  return typeof props === 'object' && props !== null;\r\n}\r\n\r\nconst RadioItem: React.FC<RadioItemProps> = ({ id, label, value, name, disabled }) => {\r\n  const { values, setFieldValue } = useFormikContext<any>();\r\n  const fieldName = name || id;\r\n\r\n  return (\r\n    <Form.Check\r\n      type=\"radio\"\r\n      id={id}\r\n      label={label}\r\n      value={value}\r\n      name={fieldName}\r\n      checked={values[fieldName] === value}\r\n      onChange={(e) => {\r\n        setFieldValue(fieldName, e.target.value);\r\n      }}\r\n      disabled={disabled}\r\n      inline\r\n    />\r\n  );\r\n};\r\n\r\nconst RadioGroup: React.FC<RadioGroupProps> = ({\r\n  name,\r\n  valueSelected,\r\n  onChange,\r\n  errorMessage,\r\n  children,\r\n}) => {\r\n  const { errors, touched } = useFormikContext<any>();\r\n  const hasError = touched[name] && errors[name];\r\n\r\n  // Create a context to pass the name to RadioItems\r\n  const radioContext = React.useMemo(() => ({ name }), [name]);\r\n\r\n  // Clone children to pass the name, ensuring props are spread safely\r\n  const childrenWithProps = React.Children.map(children, (child) => {\r\n    if (React.isValidElement(child)) {\r\n      // Ensure child.props is an object before spreading\r\n      if (isObject(child.props)) {\r\n        return React.cloneElement(child as React.ReactElement<RadioItemProps>, {\r\n          name,\r\n          ...child.props, // Safely spread child.props\r\n        });\r\n      }\r\n    }\r\n    return child;\r\n  });\r\n\r\n  return (\r\n    <div>\r\n      <div className=\"radio-group\">\r\n        {childrenWithProps}\r\n      </div>\r\n      {hasError && (\r\n        <div className=\"invalid-feedback d-block\">\r\n          {errorMessage || (typeof errors[name] === 'string' ? errors[name] : String(errors[name]))}\r\n        </div>\r\n      )}\r\n    </div>\r\n  );\r\n};\r\n\r\nexport const Radio = {\r\n  RadioGroup,\r\n  RadioItem,\r\n};\r\n\r\nexport default Radio;\r\n", "// This file exports all the components needed to replace react-bootstrap4-form-validation\r\n// It serves as a drop-in replacement for the original library\r\n\r\nimport ValidationForm from './ValidationFormWrapper';\r\nimport { TextInput, SelectGroup } from './FormikTextInput';\r\nimport { Radio } from './FormikRadio';\r\n\r\n// Export all components\r\nexport {\r\n  ValidationForm,\r\n  TextInput,\r\n  SelectGroup,\r\n  Radio\r\n};\r\n\r\n// Also export as default for convenience\r\nexport default {\r\n  ValidationForm,\r\n  TextInput,\r\n  SelectGroup,\r\n  Radio\r\n};\r\n", "import React, { forwardRef } from 'react';\r\nimport { Formik, Form, FormikProps, FormikHelpers } from 'formik';\r\nimport * as Yup from 'yup';\r\n\r\n// This is a wrapper component that replaces react-bootstrap4-form-validation with Formik\r\ninterface MockEvent {\r\n  preventDefault: () => void;\r\n  stopPropagation: () => void;\r\n  currentTarget: (EventTarget & Element) | null;\r\n  target: EventTarget | null;\r\n  nativeEvent: Event;\r\n  bubbles: boolean;\r\n  cancelable: boolean;\r\n  defaultPrevented: boolean;\r\n  eventPhase: number;\r\n  isTrusted: boolean;\r\n  timeStamp: number;\r\n  type: string;\r\n  isDefaultPrevented: () => boolean;\r\n  isPropagationStopped: () => boolean;\r\n  persist: () => void;\r\n}\r\n\r\ninterface ValidationFormWrapperProps {\r\n  children: React.ReactNode | ((formikProps: FormikProps<any>) => React.ReactNode);\r\n  onSubmit: (event: React.FormEvent | MockEvent, values?: Record<string, any>, actions?: FormikHelpers<Record<string, any>>) => void;\r\n  onErrorSubmit?: (errors: any) => void;\r\n  initialValues?: Record<string, any>;\r\n  enableReinitialize?: boolean;\r\n  autoComplete?: string;\r\n  className?: string;\r\n  onKeyPress?: (e: any) => void;\r\n}\r\n\r\nconst ValidationFormWrapper = forwardRef<HTMLFormElement, ValidationFormWrapperProps>((props, ref) => {\r\n  const { children, onSubmit, autoComplete, className, onKeyPress, initialValues, ...rest } = props;\r\n\r\n  // Create an empty validation schema by default\r\n  const validationSchema = Yup.object().shape({});\r\n\r\n  return (\r\n    <Formik\r\n      initialValues={initialValues || {}}\r\n      validationSchema={validationSchema}\r\n      onSubmit={(values: Record<string, any>, actions: FormikHelpers<Record<string, any>>) => {\r\n        // Create a mock event object with preventDefault method and currentTarget\r\n        const mockEvent: MockEvent = {\r\n          preventDefault: () => {},\r\n          stopPropagation: () => {},\r\n          currentTarget: null, // Set to null to avoid checkValidity errors\r\n          target: null,\r\n          nativeEvent: new Event('submit'),\r\n          bubbles: false,\r\n          cancelable: true,\r\n          defaultPrevented: false,\r\n          eventPhase: 0,\r\n          isTrusted: false,\r\n          timeStamp: Date.now(),\r\n          type: 'submit',\r\n          isDefaultPrevented: () => false,\r\n          isPropagationStopped: () => false,\r\n          persist: () => {}\r\n        };\r\n\r\n        if (onSubmit) {\r\n          // Pass the mock event object to maintain compatibility with the original code\r\n          onSubmit(mockEvent, values, actions);\r\n        }\r\n      }}\r\n      {...rest}\r\n    >\r\n      {(formikProps: FormikProps<any>) => (\r\n        <Form\r\n          ref={ref}\r\n          onSubmit={formikProps.handleSubmit}\r\n          autoComplete={autoComplete}\r\n          className={className}\r\n          onKeyPress={onKeyPress}\r\n        >\r\n          {typeof children === 'function' ? children(formikProps) : children}\r\n        </Form>\r\n      )}\r\n    </Formik>\r\n  );\r\n});\r\n\r\nValidationFormWrapper.displayName = 'ValidationFormWrapper';\r\n\r\nexport default ValidationFormWrapper;\r\n", "import React from 'react';\r\nimport { Form } from 'react-bootstrap';\r\nimport { Field, ErrorMessage, useField } from 'formik';\r\n\r\n// This component mimics the TextInput component from react-bootstrap4-form-validation\r\n// but uses Formik under the hood\r\nexport const TextInput = ({\r\n  name,\r\n  id,\r\n  required,\r\n  validator,\r\n  errorMessage,\r\n  onChange,\r\n  value,\r\n  as,\r\n  multiline,\r\n  rows,\r\n  pattern,\r\n  ...props\r\n}: any) => {\r\n  // Create a custom validation function that mimics the original validator\r\n  const validate = (val: any) => {\r\n    // Safely convert value to string and trim\r\n    const stringVal = typeof val === 'string' ? val : String(val || '');\r\n    if (required && (!val || stringVal.trim() === '')) {\r\n      return errorMessage?.validator || 'This field is required';\r\n    }\r\n\r\n    if (validator && !validator(val)) {\r\n      return errorMessage?.validator || 'Invalid value';\r\n    }\r\n\r\n    if (pattern && val) {\r\n      const regex = new RegExp(pattern);\r\n      if (!regex.test(val)) {\r\n        return errorMessage?.pattern || 'Invalid format';\r\n      }\r\n    }\r\n\r\n    return undefined;\r\n  };\r\n\r\n  return (\r\n    <Field name={name} validate={validate}>\r\n      {({ field, meta }: any) => (\r\n        <>\r\n          <Form.Control\r\n            {...field}\r\n            {...props}\r\n            id={id}\r\n            as={as || 'input'}\r\n            rows={rows}\r\n            isInvalid={meta.touched && !!meta.error}\r\n            onChange={(e: React.ChangeEvent<HTMLInputElement>) => {\r\n              field.onChange(e);\r\n              if (onChange) onChange(e);\r\n            }}\r\n            value={value !== undefined ? value : field.value}\r\n          />\r\n          {meta.touched && meta.error ? (\r\n            <Form.Control.Feedback type=\"invalid\">\r\n              {meta.error}\r\n            </Form.Control.Feedback>\r\n          ) : null}\r\n        </>\r\n      )}\r\n    </Field>\r\n  );\r\n};\r\n\r\n// This component mimics the SelectGroup component from react-bootstrap4-form-validation\r\n// but uses Formik under the hood\r\nexport const SelectGroup = ({\r\n  name,\r\n  id,\r\n  required,\r\n  errorMessage,\r\n  onChange,\r\n  value,\r\n  children,\r\n  ...props\r\n}: any) => {\r\n  // Create a custom validation function that mimics the original validator\r\n  const validate = (val: any) => {\r\n    if (required && (!val || val === '')) {\r\n      return errorMessage?.validator || 'This field is required';\r\n    }\r\n\r\n    return undefined;\r\n  };\r\n\r\n  return (\r\n    <Field name={name} validate={validate}>\r\n      {({ field, meta }: any) => (\r\n        <>\r\n          <Form.Control\r\n            as=\"select\"\r\n            {...field}\r\n            {...props}\r\n            id={id}\r\n            isInvalid={meta.touched && !!meta.error}\r\n            onChange={(e: React.ChangeEvent<HTMLSelectElement>) => {\r\n              field.onChange(e);\r\n              if (onChange) onChange(e);\r\n            }}\r\n            value={value !== undefined ? value : field.value}\r\n          >\r\n            {children}\r\n          </Form.Control>\r\n          {meta.touched && meta.error ? (\r\n            <Form.Control.Feedback type=\"invalid\">\r\n              {meta.error}\r\n            </Form.Control.Feedback>\r\n          ) : null}\r\n        </>\r\n      )}\r\n    </Field>\r\n  );\r\n};\r\n\r\n// Export both components as named exports and as a default object\r\nexport default {\r\n  TextInput,\r\n  SelectGroup\r\n};\r\n", "\"use client\";\n\nimport * as React from 'react';\nconst context = /*#__PURE__*/React.createContext(null);\ncontext.displayName = 'CardHeaderContext';\nexport default context;"], "names": ["_initialinstitutionType", "title", "initialVal", "setInitialVal", "useState", "editform", "props", "routes", "t", "useTranslation", "formRef", "useRef", "handleSubmit", "event", "response", "toastMsg", "preventDefault", "obj", "trim", "apiService", "patch", "post", "_id", "toast", "success", "Router", "errorCode", "error", "useEffect", "institutionTypeParams", "query", "sort", "limit", "getInstitutionTypeData", "get", "prevState", "div", "Container", "className", "fluid", "Card", "style", "marginTop", "boxShadow", "ValidationFormWrapper", "onSubmit", "ref", "initialValues", "enableReinitialize", "Body", "Row", "Col", "Title", "hr", "md", "lg", "sm", "Form", "Group", "Label", "TextInput", "name", "id", "required", "value", "validator", "String", "errorMessage", "onChange", "handleChange", "e", "target", "<PERSON><PERSON>", "type", "variant", "onClick", "re<PERSON><PERSON><PERSON><PERSON>", "window", "scrollTo", "Link", "href", "as", "CardBody", "React", "bsPrefix", "Component", "useBootstrapPrefix", "_jsx", "classNames", "displayName", "<PERSON><PERSON><PERSON>er", "<PERSON><PERSON><PERSON><PERSON>", "prefix", "contextValue", "useMemo", "cardHeaderBsPrefix", "CardHeaderContext", "Provider", "children", "CardImg", "CardImgOverlay", "CardLink", "DivStyledAsH6", "divWithClassName", "CardSubtitle", "CardText", "DivStyledAsH5", "CardTitle", "bg", "text", "border", "body", "Object", "assign", "Img", "Subtitle", "Text", "Header", "Footer", "ImgOverlay", "Radio", "RadioGroup", "valueSelected", "errors", "touched", "useFormikContext", "<PERSON><PERSON><PERSON><PERSON>", "childrenWithProps", "map", "child", "isObject", "RadioItem", "label", "disabled", "values", "setFieldValue", "fieldName", "Check", "checked", "inline", "ValidationForm", "SelectGroup", "forwardRef", "autoComplete", "onKeyPress", "rest", "validationSchema", "<PERSON><PERSON>", "shape", "<PERSON><PERSON>", "actions", "mockEvent", "stopPropagation", "currentTarget", "nativeEvent", "Event", "bubbles", "cancelable", "defaultPrevented", "eventPhase", "isTrusted", "timeStamp", "Date", "now", "isDefaultPrevented", "isPropagationStopped", "persist", "formikProps", "multiline", "rows", "pattern", "Field", "validate", "stringVal", "val", "regex", "RegExp", "test", "field", "meta", "Control", "isInvalid", "undefined", "<PERSON><PERSON><PERSON>", "context"], "sourceRoot": "", "ignoreList": [2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 16]}