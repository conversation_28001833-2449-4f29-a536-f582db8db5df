{"version": 3, "file": "static/chunks/276-8e7a2ca44b0093a8.js", "mappings": "uPAwDA,MA1C+B,IAC3B,GAAM,GAAEA,CAAC,CAAE,CAAGC,CAAAA,EAAAA,EAAAA,EAAAA,CAAcA,CAAC,MAyClBC,IAxCL,CAACC,EAASC,EAAW,CAAGC,CAAAA,EAAAA,EAAAA,OAwCGH,CAxCHG,CAAQA,EAAC,GAEjCC,EAAsB,IAEtB,WAACC,EAAAA,CAASA,CAACC,IAAI,EAACC,SAAS,cACvB,WAACF,EAAAA,CAASA,CAACG,MAAM,EAACC,QAAS,IAAMP,EAAW,CAACD,aAC3C,UAACS,MAAAA,CAAIC,UAAU,qBAAab,EAAE,wBAC9B,UAACY,MAAAA,CAAIC,UAAU,qBACZV,EAAU,UAACW,EAAAA,CAAeA,CAAAA,CAACC,KAAMC,EAAAA,GAAOA,CAAEC,MAAM,SAC/C,UAACH,EAAAA,CAAeA,CAAAA,CAACC,KAAMG,EAAAA,GAAMA,CAAED,MAAM,cAG3C,UAACV,EAAAA,CAASA,CAACY,IAAI,WACb,UAACC,EAAAA,CAAUA,CAAAA,CACTC,KAAK,SACLC,GAAIC,GAASA,EAAMC,MAAM,CAAGD,EAAMC,MAAM,CAAC,EAAE,CAAG,YAMpDC,EAA0BC,CAAAA,EAAAA,EAAAA,uBAAAA,CAAuBA,CAAC,IAAM,UAACpB,EAAAA,CAAAA,IAC/D,MACI,iCACI,UAACC,EAAAA,CAASA,CAAAA,UACN,UAACoB,EAAAA,OAAkBA,CAAAA,CAAE,GAAGJ,CAAK,KAEjC,UAAChB,EAAAA,CAASA,CAAAA,UACN,UAACqB,EAAAA,OAAsBA,CAAAA,CAAE,GAAGL,CAAK,KAErC,UAAChB,EAAAA,CAASA,CAAAA,UACN,UAACsB,EAAAA,OAAqBA,CAAAA,CAAE,GAAGN,CAAK,KAEpC,UAAChB,EAAAA,CAASA,CAAAA,UACN,UAACkB,EAAAA,CAAAA,OAIjB,kICtBA,MArB+B,IAC3B,GAAM,GAAEzB,CAAC,CAAE,CAAGC,CAAAA,EAAAA,EAAAA,EAAAA,CAAcA,CAAC,MAoBlB2B,IAnBL,CAACzB,EAASC,EAAW,CAAGC,CAAAA,EAAAA,EAAAA,OAmBGuB,CAnBHvB,CAAQA,EAAC,GACvC,MACI,+BACI,WAACE,EAAAA,CAASA,CAACC,IAAI,EAACC,SAAS,cACvB,WAACF,EAAAA,CAASA,CAACG,MAAM,EAACC,QAAS,IAAMP,EAAW,CAACD,aAC3C,UAACS,MAAAA,CAAIC,UAAU,qBAAab,EAAE,0BAC9B,UAACY,MAAAA,CAAIC,UAAU,qBACZV,EAAU,UAACW,EAAAA,CAAeA,CAAAA,CAACC,KAAMC,EAAAA,GAAOA,CAAEC,MAAM,SAC/C,UAACH,EAAAA,CAAeA,CAAAA,CAACC,KAAMG,EAAAA,GAAMA,CAAED,MAAM,cAG3C,UAACV,EAAAA,CAASA,CAACY,IAAI,WACb,UAACW,EAAAA,OAAYA,CAAAA,CAAAA,SAK7B,kICGA,MAvB0B,IACtB,GAAM,GAAE9B,CAAC,CAAE,CAAGC,CAAAA,EAAAA,EAAAA,EAAAA,CAAcA,CAAC,MAsBlB8B,IArBL,CAAC5B,EAASC,EAAW,CAAGC,CAAAA,EAAAA,EAAAA,EAqBF0B,EAAC,IArBC1B,CAAQA,EAAC,GACvC,MACI,+BACI,WAACE,EAAAA,CAASA,CAACC,IAAI,EAACC,SAAS,cACrB,WAACF,EAAAA,CAASA,CAACG,MAAM,EAACC,QAAS,IAAMP,EAAW,CAACD,aACzC,UAACS,MAAAA,CAAIC,UAAU,qBAAab,EAAE,sBAC9B,UAACY,MAAAA,CAAIC,UAAU,qBACdV,EAAU,UAACW,EAAAA,CAAeA,CAAAA,CAACC,KAAMC,EAAAA,GAAOA,CAAEC,MAAM,SAC7C,UAACH,EAAAA,CAAeA,CAAAA,CAACC,KAAMG,EAAAA,GAAMA,CAAED,MAAM,cAG7C,WAACV,EAAAA,CAASA,CAACY,IAAI,YACX,UAACa,EAAAA,CAAaA,CAAAA,CAACC,QAASV,EAAMW,aAAa,CAAEC,UAAWZ,EAAMa,UAAU,CAAEC,KAAMd,EAAMe,uBAAuB,CAACC,UAAU,EAAI,EAAE,CAAEC,gBAAiBjB,EAAMkB,cAAc,CAACC,MAAM,CAAG,GAAKnB,EAAMkB,cAAc,CAACE,GAAG,CAAC,GAAeC,EAAKC,OAAO,EAAEC,IAAI,CAAC,KAC/O,UAACC,KAAAA,CAAGlC,UAAU,gBAAQb,EAAE,iCACxB,UAACgC,EAAAA,CAAaA,CAAAA,CAACC,QAASV,EAAMe,uBAAuB,CAACU,aAAa,CAAEb,UAAWZ,EAAMe,uBAAuB,CAACW,UAAU,CAAEZ,KAAMd,EAAM2B,QAAQ,EAAI,EAAE,CAAEV,gBAAiBjB,EAAMe,uBAAuB,CAACa,MAAM,UAK/N,kICSA,MAvB8B,IAC1B,GAAM,GAAEnD,CAAC,CAAE,CAAGC,CAAAA,EAAAA,EAAAA,EAAAA,CAAcA,CAAC,MAsBlB4B,IArBL,CAAC1B,EAASC,EAAW,CAAGC,CAAAA,EAAAA,EAAAA,MAqBEwB,EArBFxB,CAAQA,EAAC,GAEvC,MACI,+BACI,WAACE,EAAAA,CAASA,CAACC,IAAI,EAACC,SAAS,cACvB,WAACF,EAAAA,CAASA,CAACG,MAAM,EAACC,QAAS,IAAMP,EAAW,CAACD,aAC3C,UAACS,MAAAA,CAAIC,UAAU,qBAAab,EAAE,yBAC9B,UAACY,MAAAA,CAAIC,UAAU,qBACZV,EAAU,UAACW,EAAAA,CAAeA,CAAAA,CAACC,KAAMC,EAAAA,GAAOA,CAAEC,MAAM,SAC/C,UAACH,EAAAA,CAAeA,CAAAA,CAACC,KAAMG,EAAAA,GAAMA,CAAED,MAAM,cAG3C,UAACV,EAAAA,CAASA,CAACY,IAAI,WACb,UAACiC,EAAAA,CAAWA,CAAAA,CAACC,QAAS9B,EAAMkB,cAAc,CAACC,MAAM,CAAG,GAAKnB,EAAMkB,cAAc,CAACE,GAAG,CAACC,GAAQA,EAAKU,MAAM,EAAER,IAAI,CAAC,GAC1GS,YAAahC,EAAMkB,cAAc,CAACC,MAAM,CAAG,GAAKnB,EAAMkB,cAAc,CAACE,GAAG,CAACC,GAAQA,EAAKY,UAAU,EAAEV,IAAI,CAAC,WAKzH,gGCDA,SAASW,EAASlC,CAAoB,EACpC,GAAM,GAAEvB,CAAC,CAAE,CAAGC,CAAAA,EAAAA,EAAAA,EAAAA,CAAcA,CAAC,UACvByD,EAA6B,CACjCC,gBAAiB3D,EAAE,cACnB,EACI,SACJ4D,CAAO,MACPC,CAAI,WACJC,CAAS,CACTC,uBAAqB,CACrBC,WAAS,oBACTC,CAAkB,qBAClBC,CAAmB,kBACnBC,CAAgB,aAChBC,CAAW,oBACXC,CAAkB,gBAClBC,CAAc,SACdrC,CAAO,CACPsC,WAAS,sBACTC,CAAoB,mBACpBC,CAAiB,YACjBC,CAAU,QACVC,CAAM,kBACNC,CAAgB,cAChBC,CAAY,CAEZ,CADA,EACGC,EACJ,CAAGvD,EAGEwD,EAAiB,4BACrBrB,EACAsB,gBAAiBhF,EAAE,IAP0C,MAQ7DiF,UAAU,UACVrB,EACAC,KAAMA,GAAQ,EAAE,CAChBqB,OAAO,EACPC,2BAA4BpB,EAC5BqB,UAAWpB,EACXqB,gBAAiBpD,qBACjBgC,EACAqB,YAAY,EACZC,iBAAkBhB,EAClBiB,kBAAmBnB,GAA0C,GAC7DoB,eADwCpB,cACVD,GAA4B,CAAC,GAAI,GAAI,GAAI,CAA3BA,EAA+B,GAAG,CAC9EsB,oBAAqB5B,EACrB6B,oBAAqBzB,EACrB0B,aAAczB,iBACdG,uBACAE,oBACAC,EACAoB,kBAAmB,UAACC,EAAAA,CAAYA,CAAAA,CAAAA,GAChCC,SAAU,UAACC,IAAAA,CAAEnF,UAAU,6CACvB6D,SACAC,eACAE,mBACAD,EACA/D,UAAW,WACb,EACA,MACE,UAACoF,EAAAA,EAASA,CAAAA,CAAE,GAAGlB,CAAc,EAEjC,CAEAtB,EAASyC,YAAY,CAAG,CACtBd,WAAW,EACXE,YAAY,EACZxB,UAAW,KACXS,WAAW,EACXC,qBAAsB,KACtBC,mBAAmB,EACnBC,WAAY,GACZE,kBAAkB,CACpB,EAEA,MAAenB,QAAQA,EAAC,6KC7FxB,SAAS0C,EAAuB5E,CAAkC,EAChE,GAAM,eAAE6E,CAAa,CAAE,CAAG7E,EAC1B,MACE,UAACX,MAAAA,UACEwF,EAAczD,GAAG,CAAC,CAACC,EAAMyD,IAEtB,UAACC,EAAAA,CAAGA,CAAAA,CAACzF,UAAU,4BACb,UAAC0F,EAAAA,OAAgBA,CAAAA,CAAC3D,KAAMA,KADayD,KAOjD,CAwHA,MAtHA,SAASvE,EACP,GAAM,GAAE9B,CAAC,CAAE,CAAGC,CAAAA,EAAAA,EAAAA,EAAAA,CAAcA,CAAC,UAEvBuB,CAmHmBM,CAnHL0E,CADLC,EAAAA,EAAAA,SAAAA,CAASA,GACGC,KAAK,CAAClF,MAAM,EAAI,EAAE,CACvC,CAAC4E,EAAeO,EAAiB,CAAGtG,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAU,EAAE,EAExD,CAACuG,EAAQC,EAAU,CAAGxG,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAC,GAE/B,CAACyG,EAAkB,CAAGzG,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAC,GAE/B0G,EAAiB,KACrBJ,EAAiB,EAAE,CACrB,EAEMK,EAAgB,CACpBN,MAAO,CAAEO,sBAAsB,EAAMC,cAAe1F,CAAM,CAAC,EAAE,EAC7D2F,KAAM,CAAEC,WAAY,MAAO,EAC3BC,MAAO,IACPC,OAAQ,2GACV,EAEMC,EAAqB,qBAAOC,EAAAA,UAAAA,MAAAA,CAAAA,GAAAA,KAAAA,IAAAA,SAAAA,CAAAA,EAAAA,CAAAA,SAAAA,CAAAA,EAAAA,CAASR,EACnCS,EAAW,MAAMC,EAAAA,CAAUA,CAACC,GAAG,CAAC,WAAYH,GAC9CC,GAAYA,EAAS5D,IAAI,EAAI4D,EAAS5D,IAAI,CAACnB,MAAM,CAAG,EAEtDiE,CAFyD,CACvCiB,IAAAA,KAAO,CAACH,EAAS5D,GAClBgE,CADsB,CAAE,IAGzCd,GAEJ,EAEAe,CAAAA,EAAAA,EAAAA,SAAAA,CAASA,CAAC,KACRP,GACF,EAAG,EAAE,EAEL,IAAMQ,EAAiB,IACrB,IAAI1B,EAAQO,EACN,CAACoB,EAAKC,EAAI,CAAG,CAAC,EAAGnB,EAAoB,EAAE,CAE3B,QAAQ,GACxBT,IAEqB,QAAQ,CAAtB6B,GACP7B,IAGEA,EAAQ4B,IACV5B,CADe,EACP,EAGNA,EAAQ2B,IACV3B,CADe,CACP4B,CAAAA,EAIN,EAAevF,MAAM,CAAG2D,GAAW,GAAG,GAChCD,EAAc1D,MAAM,EAAG,EAG7B,EAAeA,MAAM,CAAG2D,GAAW,GAAG,CACxCA,GAAQ,EAEVQ,EAAUR,EACZ,EAEA,MACE,UAACzF,MAAAA,CAAIC,UAAU,8BACZuF,GAAiBA,EAAc1D,MAAM,CAAG,EACvC,iCACE,UAACyF,EAAAA,CAASA,CAAAA,CAACC,KAAK,aACd,WAAC9B,EAAAA,CAAGA,CAAAA,WACF,UAAC+B,EAAAA,CAAGA,CAAAA,CAACC,GAAI,GAAIzH,UAAU,QAEtBuF,GAAiBA,EAAc1D,MAAM,CAAG,EACvC,UAAC2F,EAAAA,CAAGA,CAAAA,CAACC,GAAI,EAAGzH,UAAU,yCACpB,WAACD,MAAAA,CAAIC,UAAU,gCACb,UAAC0H,IAAAA,CAAE1H,UAAU,wBAAwBF,QAAS,IAAMoH,EAAe,iBACjE,UAAC/B,IAAAA,CAAEnF,UAAU,yBAEf,UAAC0H,IAAAA,CAAE1H,UAAU,yBAAyBF,QAAS,IAAMoH,EAAe,iBAClE,UAAC/B,IAAAA,CAAEnF,UAAU,+BAIjB,UAGR,UAACsH,EAAAA,CAASA,CAAAA,CAACC,KAAK,aACd,UAAC9B,EAAAA,CAAGA,CAAAA,UACF,UAAC+B,EAAAA,CAAGA,CAAAA,CAACC,GAAI,GAAIzH,UAAU,eACrB,UAAC2H,EAAAA,CAAQA,CAAAA,CAACC,YAAY,EAAOC,UAAU,EAAOC,SAAU,KAAMC,YAAahC,WACxER,EAAczD,GAAG,CAAC,CAACC,EAAMyD,IAEtB,UAACmC,EAAAA,CAAQA,CAAChI,IAAI,WACZ,UAAC2F,EAAAA,CAAuBC,cAAexD,KADrByD,eAWhC,UAAC8B,EAAAA,CAASA,CAAAA,CAACC,OAAO,WAChB,UAAC9B,EAAAA,CAAGA,CAAAA,UACF,UAAC+B,EAAAA,CAAGA,CAAAA,CAACC,GAAI,GAAIzH,UAAU,eACrB,UAACD,MAAAA,CAAIC,UAAU,kCACf,UAACgI,IAAAA,CAAEhI,UAAU,wDAAgDb,EAAE,mCAQ/E,0GCjFA,MAtDoD,OAAC,MAAEqC,CAAI,cAsD5CL,GAtD8CQ,CAAe,SAsDhDR,EAtDkDG,CAAS,SAAEF,CAAO,CAAE,GAE1F6G,EAAa,MAAOC,EAAaC,KAKrC7G,EAJiB,CACf8G,OAGQC,QAHQH,EAAOI,QAAQ,CAC/BH,cAAeA,CACjB,EAEF,EAEM,GAAEhJ,CAAC,CAAE,CAAGC,CAAAA,EAAAA,EAAAA,EAAAA,CAAcA,CAAC,UAGvB2D,EAAU,CACd,CACEwF,KAAMpJ,EAAE,YACRqJ,MAAO,MACPF,SAAU,YACVG,KAAM,GAAYC,GAAKA,EAAEC,SAAS,EAAID,EAAEC,SAAS,EAEnD,CACEJ,KAAMpJ,EAAE,YACRqJ,MAAO,MACPF,SAAU,iBACVG,KAAOC,GAAWA,GAAKA,EAAEE,aAAa,EAAI,UAAClB,IAAAA,CAAEmB,KAAM,GAA4CH,MAAAA,CAAzCI,8BAAsB,CAAC,oBAAwB,OAANJ,EAAEK,GAAG,EAAIC,OAAO,kBAAUN,EAAEE,aAAa,CAACK,KAAK,CAAC,KAAKC,KAAK,CAAC,EAAG,CAAC,GAAGC,IAAI,CAAC,OACtKC,UAAU,CACZ,EACA,CACEb,KAAMpJ,EAAE,eACRmJ,SAAU,cACVG,KAAM,GAAYC,GAAKA,EAAEW,WAAW,EAAIX,EAAEW,WAAW,EAEvD,CACEd,KAAMpJ,EAAE,gBACRqJ,MAAO,MACPF,SAAU,iBACVG,KAAM,GAAYC,GAAKA,EAAEY,UAAU,EAAIC,IAAOb,EAAEY,UAAU,EAAEE,MAAM,CAAC,cACnEJ,MAD6CG,IACnC,CACZ,EACD,CAED,MACE,UAAC3G,EAAAA,CAAQA,CAAAA,CACPG,QAASA,EACTC,KAAMxB,EACNkC,UAAW,GACXI,OAAQmE,EACRlE,gBAAgB,IAChB3C,QAASA,GAIf,sNC5DO,IAAMqI,EAAeC,CAAAA,EAAAA,EAAAA,CAAAA,CAAoBA,CAAC,CAC/CC,sBAAuB,KACjBC,EAAMC,WAAW,IAAID,EAAMC,WAAW,CAACC,MAAM,IAAIF,EAAMC,WAAW,CAACC,MAAM,CAAC,aAAa,CAK7FC,CAL+F,kBAK3E,cACtB,GAAG,EAE6BL,CAAAA,EAAAA,EAAAA,CAAAA,CAAoBA,CAAC,CACnDC,sBAAuB,KACjBC,EAAMC,WAAW,IAAID,EAAMC,WAAW,CAACC,MAAM,IAAIF,EAAMC,WAAW,CAACC,MAAM,CAAC,aAAa,CAK7FC,CAL+F,kBAK3E,mBACpBC,iBAAkB,IAAM,UAACC,EAAAA,OAAIA,CAAAA,CAAAA,EAC/B,GAEaC,EAAgBR,CAAAA,EAAAA,EAAAA,CAAAA,CAAoBA,CAAC,CAChDC,sBAAuB,CAACC,EAAOlJ,KAC7B,GAAIkJ,EAAMC,WAAW,EAAID,EAAMC,WAAW,CAACC,MAAM,EAAE,GAC7CF,EAAMC,WAAW,CAACC,MAAM,CAAC,aAAa,CACxC,CAD0C,MACnC,OAEP,GAAIF,EAAMC,WAAW,CAACC,MAAM,CAAC,aAAa,EAAE,EAChCA,MAAM,EAAIpJ,EAAMoJ,MAAM,CAACK,IAAI,EAAIzJ,EAAMoJ,MAAM,CAACK,IAAI,CAACpB,GAAG,GAAKa,EAAMO,IAAI,CAACpB,GAAG,CAC/E,CADiF,KAC1E,EAGb,CAEF,OAAO,CACT,EACAgB,mBAAoB,eACtB,GAAG,EAE8BL,CAAAA,EAAAA,EAAAA,CAAAA,CAAoBA,CAAC,CACpDC,sBAAuB,CAACC,EAAOlJ,KAC7B,GAAIkJ,EAAMC,WAAW,EAAID,EAAMC,WAAW,CAACC,MAAM,EAAE,GAC7CF,EAAMC,WAAW,CAACC,MAAM,CAAC,aAAa,CACxC,CAD0C,KACnC,QAEP,GAAIF,EAAMC,WAAW,CAACC,MAAM,CAAC,aAAa,EACpCpJ,EAAMoJ,MAAM,EAAIpJ,EAAMoJ,MAAM,CAACK,IAAI,EAAIzJ,EAAMoJ,MAAM,CAACK,IAAI,CAACpB,GAAG,GAAKa,EAAMO,IAAI,CAACpB,GAAG,CAC/E,CADiF,MAC1E,CAGb,CAEF,OAAO,CACT,EACAgB,mBAAoB,oBACpBC,iBAAkB,IAAM,UAACC,EAAAA,OAAIA,CAAAA,CAAAA,EAC/B,GAAG,EAEoCP,CAAAA,EAAAA,EAAAA,CAAAA,CAAoBA,CAAC,CAC1DC,sBAAuB,GACjBC,IAAMC,WAAW,IAAID,EAAMC,WAAW,CAACO,MAAM,IAAIR,EAAMC,WAAW,CAACO,MAAM,CAAC,WAAW,CAK3FL,CAL6F,kBAKzE,yBACtB,GAAG,EAEYN,YAAYA,EAAC,iFCpEb,SAAS/D,EAAiBhF,CAAU,QAiC9BqB,EA/BnB,EA+B4B,CA/BtB,MAAEA,CAAI,CAAE,CAAGrB,EASjB,MACE,WAAC8G,EAAAA,CAAGA,CAAAA,CAACxH,UAAU,MAAMyH,GAAI,aACvB,UAAC4C,IAAIA,CAACxB,KAAM,IAAc,OAAV9G,EAAKvB,IAAI,CAAC,gBAAe8J,GAAI,EAAxCD,EAA8DtI,MAAAA,CAAlBA,EAAKvB,IAAI,CAAC,UAA0CuB,MAAAA,CAAlCA,CAAI,CAwBxDA,EAxBqEA,EAwB5D,UACD,OAAVA,EAAKvB,IAAI,EAzBoE,CAAC,YAAmB,OAATuB,EAAKgH,GAAG,WAE1G,EAAMtG,MAAM,EAAIV,EAAKU,MAAM,CAAC,EAAE,CAC7B,UAAC8H,MAAAA,CAAIC,IAAK,GAAwCzI,MAAAA,CAArC+G,8BAAsB,CAAC,gBAAiC,OAAnB/G,EAAKU,MAAM,CAAC,EAAE,CAACsG,GAAG,EAAI0B,IAAI,eAC1EzK,UAAU,gBACV,UAACmF,IAAAA,CAAEnF,UAAU,iCAGnB,WAACD,MAAAA,CAAIC,UAAU,yBACb,UAACqK,IAAIA,CAACxB,KAAM,IAAc,OAAV9G,EAAKvB,IAAI,CAAC,gBAAe8J,GAAI,EAAxCD,EAA8DtI,MAAAA,CAAlBA,EAAKvB,IAAI,CAAC,UAA0CuB,MAAAA,CAAlCA,CAAI,CAAC2I,EAAY3I,EAYnF,UAAoB,OAAVA,EAAKvB,IAAI,EAZsE,CAAC,YAAmB,OAATuB,EAAKgH,GAAG,WAC1GhH,GAAQA,EAAK4I,KAAK,CAAG5I,EAAK4I,KAAK,CAAG,KAErC,UAAC3C,IAAAA,UACEjG,GAAQA,EAAKsH,WAAW,CAAGuB,CAtBX,IACvB,IAAM7K,EAAMsC,SAASwI,aAAa,CAAC,OACnC9K,EAAI+K,SAAS,CAAGC,EAChB,IAAMC,EAASjL,EAAIkL,WAAW,EAAIlL,EAAImL,SAAS,EAAI,GACnD,OAAQF,EAAOnJ,MAAM,CAXF,EAWKsJ,EAAiB,GAA2C,OAAxCH,EAAOI,SAAS,CAAC,EAAGD,KAAoB,OAAOH,EAC7F,EAiBqDjJ,CAlB8B,CAkBzBsH,WAAW,EAAI,YAK3E", "sources": ["webpack://_N_E/./pages/vspace/VirtualSpaceAccordionSection.tsx", "webpack://_N_E/./pages/vspace/AnnouncementsAccordian.tsx", "webpack://_N_E/./pages/vspace/DocumentAccordian.tsx", "webpack://_N_E/./pages/vspace/MediaGalleryAccordian.tsx", "webpack://_N_E/./components/common/RKITable.tsx", "webpack://_N_E/./pages/vspace/vspace_announcement/Announcement.tsx", "webpack://_N_E/./components/common/DocumentTable.tsx", "webpack://_N_E/./pages/vspace/permission.tsx", "webpack://_N_E/./pages/vspace/vspace_announcement/AnnouncementItem.tsx"], "sourcesContent": ["//Import Library\r\nimport { Accordion, Card } from \"react-bootstrap\";\r\nimport { faPlus, faMinus } from \"@fortawesome/free-solid-svg-icons\";\r\nimport { FontAwesomeIcon } from \"@fortawesome/react-fontawesome\";\r\nimport { useState } from \"react\";\r\n\r\n//Import services/components\r\nimport DocumentsAccordian from './DocumentAccordian';\r\nimport AnnouncementsAccordian from \"./AnnouncementsAccordian\";\r\nimport MediaGalleryAccordian from \"./MediaGalleryAccordian\";\r\nimport { canViewDiscussionUpdate} from \"./permission\";\r\nimport Discussion from \"../../components/common/disussion\";\r\nimport { useTranslation } from 'next-i18next';\r\n\r\nconst VspaceAccordianSection = (props: any) => {\r\n    const { t } = useTranslation('common');\r\n    const [section, setSection] = useState(false);\r\n\r\n    const DiscussionComponent = () => {\r\n        return (\r\n          <Accordion.Item eventKey=\"3\">\r\n            <Accordion.Header onClick={() => setSection(!section)}>\r\n              <div className=\"cardTitle\">{t(\"vspace.Discussions\")}</div>\r\n              <div className=\"cardArrow\">\r\n                {section ? <FontAwesomeIcon icon={faMinus} color=\"#fff\" /> :\r\n                  <FontAwesomeIcon icon={faPlus} color=\"#fff\" />}\r\n              </div>\r\n            </Accordion.Header>\r\n            <Accordion.Body>\r\n              <Discussion\r\n                type=\"vspace\"\r\n                id={props && props.routes ? props.routes[1] : null}\r\n              />\r\n            </Accordion.Body>\r\n          </Accordion.Item>\r\n        )\r\n    };\r\n    const CanViewDiscussionUpdate = canViewDiscussionUpdate(() => <DiscussionComponent />)\r\n    return(\r\n        <>\r\n            <Accordion>\r\n                <DocumentsAccordian {...props} />\r\n            </Accordion>\r\n            <Accordion>\r\n                <AnnouncementsAccordian {...props} />\r\n            </Accordion>\r\n            <Accordion>\r\n                <MediaGalleryAccordian {...props} />\r\n            </Accordion>\r\n            <Accordion>\r\n                <CanViewDiscussionUpdate />\r\n            </Accordion>\r\n        </>\r\n    )\r\n};\r\n\r\nexport default VspaceAccordianSection;", "//Import Library\r\nimport { faPlus, faMinus } from \"@fortawesome/free-solid-svg-icons\";\r\nimport { FontAwesomeIcon } from \"@fortawesome/react-fontawesome\";\r\nimport { Card, Accordion } from \"react-bootstrap\";\r\nimport { useState } from \"react\";\r\n\r\n//Import services/components\r\nimport Announcement from \"./vspace_announcement/Announcement\";\r\nimport { useTranslation } from 'next-i18next';\r\n\r\n\r\nconst AnnouncementsAccordian = (props: any) => {\r\n    const { t } = useTranslation('common');\r\n    const [section, setSection] = useState(false);\r\n    return(\r\n        <>\r\n            <Accordion.Item eventKey=\"1\">\r\n              <Accordion.Header onClick={() => setSection(!section)}>\r\n                <div className=\"cardTitle\">{t(\"vspace.Announcements\")}</div>\r\n                <div className=\"cardArrow\">\r\n                  {section ? <FontAwesomeIcon icon={faMinus} color=\"#fff\" /> :\r\n                    <FontAwesomeIcon icon={faPlus} color=\"#fff\" />}\r\n                </div>\r\n              </Accordion.Header>\r\n              <Accordion.Body>\r\n                <Announcement />\r\n              </Accordion.Body>\r\n            </Accordion.Item>\r\n        </>\r\n    )\r\n};\r\n\r\nexport default AnnouncementsAccordian;", "//Import Library\r\nimport { faPlus, faMinus } from \"@fortawesome/free-solid-svg-icons\";\r\nimport { FontAwesomeIcon } from \"@fortawesome/react-fontawesome\";\r\nimport { Card, Accordion } from \"react-bootstrap\";\r\nimport { useState } from \"react\";\r\n\r\n//Import services/components\r\nimport DocumentTable from \"../../components/common/DocumentTable\";\r\nimport { useTranslation } from 'next-i18next';\r\n\r\nconst DocumentAccordian = (props: any) => {\r\n    const { t } = useTranslation('common');\r\n    const [section, setSection] = useState(false);\r\n    return (\r\n        <>\r\n            <Accordion.Item eventKey=\"0\">\r\n                <Accordion.Header onClick={() => setSection(!section)}>\r\n                    <div className=\"cardTitle\">{t(\"vspace.Documents\")}</div>\r\n                    <div className=\"cardArrow\">\r\n                    {section ? <FontAwesomeIcon icon={faMinus} color=\"#fff\" /> :\r\n                        <FontAwesomeIcon icon={faPlus} color=\"#fff\" />}\r\n                    </div>\r\n                </Accordion.Header>\r\n                <Accordion.Body>\r\n                    <DocumentTable loading={props.vSpaceLoading} sortProps={props.vSpaceSort} docs={props.documentAccoirdianProps.vSpaceDocs || []} docsDescription={props.calenderEvents.length > 0 && props.calenderEvents.map((item: any) => item.doc_src).flat(1)} />\r\n                    <h6 className=\"mt-3\">{t(\"vspace.DocumentsfromUpdates\")}</h6>\r\n                    <DocumentTable loading={props.documentAccoirdianProps.updateLoading} sortProps={props.documentAccoirdianProps.updateSort} docs={props.document || []} docsDescription={props.documentAccoirdianProps.docSrc} />\r\n                </Accordion.Body>\r\n            </Accordion.Item>\r\n        </>\r\n    )\r\n};\r\n\r\nexport default DocumentAccordian;", "//Import Library\r\nimport { faPlus, faMinus } from \"@fortawesome/free-solid-svg-icons\";\r\nimport { FontAwesomeIcon } from \"@fortawesome/react-fontawesome\";\r\nimport { useState } from \"react\";\r\nimport { Card, Accordion } from \"react-bootstrap\";\r\n\r\n//Import services/components\r\nimport ReactImages from \"../../components/common/ReactImages\";\r\nimport { useTranslation } from 'next-i18next';\r\n\r\ninterface MediaGalleryAccordianProps {\r\n  calenderEvents: Array<{\r\n    images: any[];\r\n    images_src: any[];\r\n  }>;\r\n}\r\n\r\nconst MediaGalleryAccordian = (props: MediaGalleryAccordianProps) => {\r\n    const { t } = useTranslation('common');\r\n    const [section, setSection] = useState(false);\r\n\r\n    return (\r\n        <>\r\n            <Accordion.Item eventKey=\"2\">\r\n              <Accordion.Header onClick={() => setSection(!section)}>\r\n                <div className=\"cardTitle\">{t(\"vspace.MediaGallery\")}</div>\r\n                <div className=\"cardArrow\">\r\n                  {section ? <FontAwesomeIcon icon={faMinus} color=\"#fff\" /> :\r\n                    <FontAwesomeIcon icon={faPlus} color=\"#fff\" />}\r\n                </div>\r\n              </Accordion.Header>\r\n              <Accordion.Body>\r\n                <ReactImages gallery={props.calenderEvents.length > 0 && props.calenderEvents.map(item => item.images).flat(1)}\r\n                  imageSource={props.calenderEvents.length > 0 && props.calenderEvents.map(item => item.images_src).flat(1)} />\r\n              </Accordion.Body>\r\n            </Accordion.Item>\r\n        </>\r\n    )\r\n};\r\n\r\nexport default MediaGalleryAccordian;", "//Import Library\r\nimport React from 'react'\r\nimport DataTable  from 'react-data-table-component';\r\n\r\n//Import services/components\r\nimport { useTranslation } from 'next-i18next';\r\nimport CustomLoader from './CustomLoader';\r\n\r\ninterface RKITableProps {\r\n  columns: any[];\r\n  data: any[];\r\n  totalRows?: number;\r\n  resetPaginationToggle?: boolean;\r\n  subheader?: boolean;\r\n  subHeaderComponent?: React.ReactNode;\r\n  handlePerRowsChange?: (newPerPage: number, page: number) => void;\r\n  handlePageChange?: (page: number) => void;\r\n  rowsPerPage?: number[];\r\n  defaultRowsPerPage?: number;\r\n  selectableRows?: boolean;\r\n  loading?: boolean;\r\n  pagServer?: boolean;\r\n  onSelectedRowsChange?: (selectedRows: any) => void;\r\n  clearSelectedRows?: boolean;\r\n  sortServer?: boolean;\r\n  onSort?: (column: any, sortDirection: string) => void;\r\n  persistTableHead?: boolean;\r\n  sortFunction?: (rows: any[], field: any, direction: 'asc' | 'desc') => any[];\r\n  noHeader?: boolean;\r\n  dense?: boolean;\r\n  pagination?: boolean;\r\n  paginationServer?: boolean;\r\n  paginationTotalRows?: number;\r\n  subHeaderAlign?: string;\r\n  [key: string]: any;\r\n}\r\n\r\nfunction RKITable(props: RKITableProps) {\r\n  const { t } = useTranslation('common');\r\n  const paginationComponentOptions = {\r\n    rowsPerPageText: t('Rowsperpage'),\r\n    };\r\n  const {\r\n    columns,\r\n    data,\r\n    totalRows,\r\n    resetPaginationToggle,\r\n    subheader,\r\n    subHeaderComponent,\r\n    handlePerRowsChange,\r\n    handlePageChange,\r\n    rowsPerPage,\r\n    defaultRowsPerPage,\r\n    selectableRows,\r\n    loading,\r\n    pagServer,\r\n    onSelectedRowsChange,\r\n    clearSelectedRows,\r\n    sortServer,\r\n    onSort,\r\n    persistTableHead,\r\n    sortFunction,\r\n    // Filter out any unknown props that might cause DOM warnings\r\n    ...otherProps\r\n  } = props;\r\n\r\n  // Only pass known DataTable props to avoid DOM warnings\r\n  const dataTableProps = {\r\n    paginationComponentOptions,\r\n    noDataComponent: t(\"NoData\"),\r\n    noHeader: true,\r\n    columns,\r\n    data: data || [],\r\n    dense: true,\r\n    paginationResetDefaultPage: resetPaginationToggle,\r\n    subHeader: subheader,\r\n    progressPending: loading,\r\n    subHeaderComponent,\r\n    pagination: true,\r\n    paginationServer: pagServer,\r\n    paginationPerPage: defaultRowsPerPage ? defaultRowsPerPage : 10,\r\n    paginationRowsPerPageOptions: rowsPerPage ? rowsPerPage : [10, 15, 20, 25, 30],\r\n    paginationTotalRows: totalRows,\r\n    onChangeRowsPerPage: handlePerRowsChange,\r\n    onChangePage: handlePageChange,\r\n    selectableRows,\r\n    onSelectedRowsChange,\r\n    clearSelectedRows,\r\n    progressComponent: <CustomLoader/>,\r\n    sortIcon: <i className=\"sort-icon fas fa-exchange-alt\" />,\r\n    sortServer,\r\n    onSort,\r\n    sortFunction,\r\n    persistTableHead,\r\n    className: \"rki-table\"\r\n  };\r\n  return (\r\n    <DataTable {...dataTableProps} />\r\n  )\r\n};\r\n\r\nRKITable.defaultProps = {\r\n  subHeader: false,\r\n  pagination: true,\r\n  totalRows: null,\r\n  pagServer: true,\r\n  onSelectedRowsChange: null,\r\n  clearSelectedRows: false,\r\n  sortServer: false,\r\n  persistTableHead: false\r\n}\r\n\r\nexport default RKITable;\r\n\r\n", "//Import Library\r\nimport React, { useEffect, useState } from 'react';\r\nimport { Container, Row } from \"react-bootstrap\";\r\nimport Col from \"react-bootstrap/Col\";\r\nimport { useRouter } from \"next/router\";\r\n\r\n//Import services/components\r\nimport _ from 'lodash';\r\nimport AnnouncementItem from \"./AnnouncementItem\";\r\nimport Carousel from 'react-bootstrap/Carousel'\r\nimport apiService from \"../../../services/apiService\";\r\nimport { useTranslation } from 'next-i18next';\r\n\r\n\r\n//TODO: Need to use RKISingleItemCarousel component to reuse our exisiting component\r\ninterface ListOfAnnouncementItemProps {\r\n  announcements: any[][];\r\n}\r\n\r\nfunction ListOfAnnouncementItem(props: ListOfAnnouncementItemProps) {\r\n  const { announcements } = props;\r\n  return (\r\n    <div>\r\n      {announcements.map((item, index) => {\r\n        return (\r\n          <Row className=\"announcementItem\" key={index}>\r\n            <AnnouncementItem item={item} />\r\n          </Row>\r\n        )\r\n      })}\r\n    </div>\r\n  )\r\n}\r\n\r\nfunction Announcement() {\r\n  const { t } = useTranslation('common');\r\n  const router = useRouter();\r\n  const routes: any = router.query.routes || [];\r\n  const [announcements, setAnnouncements] = useState<any[][]>([]);\r\n\r\n  const [cindex, setCindex] = useState(0);\r\n\r\n  const [carouselItemCount] = useState(3);\r\n\r\n  const setEmptyNotice = () => {\r\n    setAnnouncements([]);\r\n  };\r\n\r\n  const updatesParams = {\r\n    query: { show_as_announcement: true, parent_vspace: routes[1] },\r\n    sort: { created_at: \"desc\" },\r\n    limit: \"~\",\r\n    select: \"-created_at -update_type -contact_details -document -end_date -link -media -reply -start_date -updated_at\"\r\n  };\r\n\r\n  const fetchAnnouncements = async (params = updatesParams) => {\r\n    const response = await apiService.get('/updates', params);\r\n    if (response && response.data && response.data.length > 0) {\r\n      const partition = _.chunk(response.data, 3);\r\n      setAnnouncements(partition)\r\n    } else {\r\n      setEmptyNotice()\r\n    }\r\n  };\r\n\r\n  useEffect(() => {\r\n    fetchAnnouncements();\r\n  }, [])\r\n\r\n  const toggleCarousel = (direction: 'next' | 'prev') => {\r\n    let index = cindex;\r\n    const [min, max] = [0, carouselItemCount - 1];\r\n\r\n    if (direction === 'next') {\r\n      index++\r\n    }\r\n    else if (direction === 'prev') {\r\n      index--\r\n    }\r\n\r\n    if (index > max) {\r\n      index = 0\r\n    }\r\n\r\n    if (index < min) {\r\n      index = max\r\n    }\r\n\r\n\r\n    if ((announcements.length - index) === 1) {\r\n      index = announcements.length - 1;\r\n    }\r\n\r\n    if ((announcements.length - index) === 0) {\r\n      index = 1;\r\n    }\r\n    setCindex(index);\r\n  };\r\n\r\n  return (\r\n    <div className=\"announcements mt-0\">\r\n      {announcements && announcements.length > 0 ? (\r\n        <>\r\n          <Container fluid>\r\n            <Row>\r\n              <Col xs={10} className=\"p-0\">\r\n              </Col>\r\n              {announcements && announcements.length > 1 ?\r\n                <Col xs={2} className=\"text-end carousel-control p-0\">\r\n                  <div className=\"carousel-navigation\">\r\n                    <a className=\"left carousel-control\" onClick={() => toggleCarousel('prev')}>\r\n                      <i className=\"fa fa-chevron-left\" />\r\n                    </a>\r\n                    <a className=\"right carousel-control\" onClick={() => toggleCarousel('next')}>\r\n                      <i className=\"fa fa-chevron-right\" />\r\n                    </a>\r\n                  </div>\r\n                </Col>\r\n                : null}\r\n            </Row>\r\n          </Container>\r\n          <Container fluid>\r\n            <Row>\r\n              <Col xs={12} className=\"p-0\">\r\n                <Carousel indicators={false} controls={false} interval={null} activeIndex={cindex}>\r\n                  {announcements.map((item, index) => {\r\n                    return (\r\n                      <Carousel.Item key={index}>\r\n                        <ListOfAnnouncementItem announcements={item} />\r\n                      </Carousel.Item>\r\n                    )\r\n                  })}\r\n                </Carousel>\r\n              </Col>\r\n            </Row>\r\n          </Container>\r\n        </>\r\n      ) : (\r\n          <Container fluid={true}>\r\n            <Row>\r\n              <Col xs={12} className=\"p-0\">\r\n                <div className=\"border border-info m-3\">\r\n                <p className=\"d-flex d-flex justify-content-center p-2 m-0\">{t(\"NoAnnouncementFound!\")}</p>\r\n                </div>\r\n              </Col>\r\n            </Row>\r\n          </Container>\r\n        )}\r\n    </div>\r\n  )\r\n}\r\n\r\nexport default Announcement;", "//Import Library\r\nimport React from \"react\";\r\nimport moment from \"moment\";\r\n\r\n//Import services/components\r\nimport RKITable from '../../components/common/RKITable';\r\nimport { useTranslation } from 'next-i18next';\r\n\r\ninterface DocumentTableProps {\r\n  docs: any[];\r\n  docsDescription: string;\r\n  sortProps: (sortObj: { columnSelector: string; sortDirection: string }) => void;\r\n  loading: boolean;\r\n}\r\n\r\nconst DocumentTable: React.FC<DocumentTableProps> = ({ docs, docsDescription, sortProps, loading }) => {\r\n\r\n  const handleSort = async (column: any, sortDirection: string) => {\r\n    const objSlect = {\r\n      columnSelector: column.selector,\r\n      sortDirection: sortDirection\r\n    }\r\n    sortProps(objSlect);\r\n  };\r\n\r\n  const { t } = useTranslation('common');\r\n\r\n\r\n  const columns = [\r\n    {\r\n      name: t(\"FileType\"),\r\n      width: \"15%\",\r\n      selector: 'extension',\r\n      cell: (d: any) => d && d.extension && d.extension,\r\n    },\r\n    {\r\n      name: t(\"FileName\"),\r\n      width: \"25%\",\r\n      selector: \"document_title\",\r\n      cell: (d: any) => d && d.original_name && <a href={`${process.env.API_SERVER}/files/download/${d._id}`} target=\"_blank\">{d.original_name.split('.').slice(0, -1).join('.')}</a>,\r\n      sortable: true\r\n    },\r\n    {\r\n      name: t(\"Description\"),\r\n      selector: 'description',\r\n      cell: (d: any) => d && d.description && d.description,\r\n    },\r\n    {\r\n      name: t(\"UploadedDate\"),\r\n      width: \"25%\",\r\n      selector: 'doc_created_at',\r\n      cell: (d: any) => d && d.updated_at && moment(d.updated_at).format('MM/DD/YYYY'),\r\n      sortable: true\r\n    }\r\n  ];\r\n\r\n  return (\r\n    <RKITable\r\n      columns={columns}\r\n      data={docs}\r\n      pagServer={true}\r\n      onSort={handleSort}\r\n      persistTableHead\r\n      loading={loading}\r\n    />\r\n\r\n  )\r\n}\r\n\r\nexport default DocumentTable;\r\n", "//Import Library\r\nimport React from 'react';\r\nimport connectedAuthWrapper from 'redux-auth-wrapper/connectedAuthWrapper';\r\n\r\n//Import services/components\r\nimport R403 from \"../r403\";\r\n\r\nexport const canAddVspace = connectedAuthWrapper({\r\n  authenticatedSelector: (state) => {\r\n    if (state.permissions && state.permissions.vspace && state.permissions.vspace['create:any']) {\r\n      return true;\r\n    }\r\n    return false;\r\n  },\r\n  wrapperDisplayName: 'CanAddVspace',\r\n});\r\n\r\nexport const canAddVspaceForm = connectedAuthWrapper({\r\n  authenticatedSelector: (state) => {\r\n    if (state.permissions && state.permissions.vspace && state.permissions.vspace['create:any']) {\r\n      return true;\r\n    }\r\n    return false;\r\n  },\r\n  wrapperDisplayName: 'CanAddVspaceForm',\r\n  FailureComponent: () => <R403/>\r\n});\r\n\r\nexport const canEditVspace = connectedAuthWrapper({\r\n  authenticatedSelector: (state, props) => {\r\n    if (state.permissions && state.permissions.vspace) {\r\n      if (state.permissions.vspace['update:any']) {\r\n        return true;\r\n      } else { //if update:own\r\n        if (state.permissions.vspace['update:own']) {\r\n          if (props.vspace && props.vspace.user && props.vspace.user._id === state.user._id) {\r\n            return true;\r\n          }  \r\n        }\r\n      }\r\n    }\r\n    return false;\r\n  },\r\n  wrapperDisplayName: 'CanEditVspace',\r\n});\r\n\r\nexport const canEditVspaceForm = connectedAuthWrapper({\r\n  authenticatedSelector: (state, props) => {\r\n    if (state.permissions && state.permissions.vspace) {\r\n      if (state.permissions.vspace['update:any']) {\r\n        return true;\r\n      } else { //if update:own\r\n        if (state.permissions.vspace['update:own']) {\r\n          if (props.vspace && props.vspace.user && props.vspace.user._id === state.user._id) {\r\n            return true;\r\n          }\r\n        }\r\n      }\r\n    }\r\n    return false;\r\n  },\r\n  wrapperDisplayName: 'CanEditVspaceForm',\r\n  FailureComponent: () => <R403/>\r\n});\r\n\r\nexport const canViewDiscussionUpdate = connectedAuthWrapper({\r\n  authenticatedSelector: (state) => {\r\n    if (state.permissions && state.permissions.update && state.permissions.update['read:any']) {\r\n      return true;\r\n    }\r\n    return false;\r\n  },\r\n  wrapperDisplayName: 'CanViewDiscussionUpdate',\r\n});\r\n\r\nexport default canAddVspace;", "//Import Library\r\nimport { Col } from \"react-bootstrap\";\r\nimport <PERSON> from \"next/link\";\r\n\r\nconst truncateLength = 260;\r\n\r\n//TODO: Remove the maths random number for image after updates completed with image upload\r\nexport default function AnnouncementItem(props: any) {\r\n\r\n  const { item } = props;\r\n\r\n  const getTrimmedString = (html: any) => {\r\n    const div = document.createElement(\"div\");\r\n    div.innerHTML = html;\r\n    const string = div.textContent || div.innerText || \"\";\r\n    return (string.length > truncateLength ? `${string.substring(0, truncateLength - 3)}...` : string);\r\n  }\r\n\r\n  return (\r\n    <Col className=\"p-0\" xs={12}>\r\n      <Link href={`/${item.type}/[...routes]`} as={`/${item.type}/show/${item[Parent_func(item)]}/update/${item._id}`}>\r\n\r\n        {(item.images && item.images[0]) ?\r\n          <img src={`${process.env.API_SERVER}/image/show/${item.images[0]._id}`} alt=\"announcement\"\r\n            className=\"announceImg\" />\r\n          : <i className=\"fa fa-bullhorn announceImg\" />}\r\n\r\n      </Link>\r\n      <div className=\"announceDesc\">\r\n        <Link href={`/${item.type}/[...routes]`} as={`/${item.type}/show/${item[newFunction(item)]}/update/${item._id}`}>\r\n          {item && item.title ? item.title : ''}\r\n        </Link>\r\n        <p>\r\n          {item && item.description ? getTrimmedString(item.description) : null}\r\n        </p>\r\n      </div>\r\n    </Col>\r\n  );\r\n}\r\n\r\nfunction newFunction(item: any) {\r\n  return `parent_${item.type}`;\r\n}\r\n\r\nfunction Parent_func(item: any) {\r\n  return `parent_${item.type}`;\r\n}\r\n"], "names": ["t", "useTranslation", "VspaceAccordianSection", "section", "setSection", "useState", "DiscussionComponent", "Accordion", "<PERSON><PERSON>", "eventKey", "Header", "onClick", "div", "className", "FontAwesomeIcon", "icon", "faMinus", "color", "faPlus", "Body", "Discussion", "type", "id", "props", "routes", "CanViewDiscussionUpdate", "canViewDiscussionUpdate", "DocumentsAccordian", "Announcements<PERSON><PERSON><PERSON>an", "MediaGalleryAccordian", "Announcement", "DocumentAccordian", "DocumentTable", "loading", "vSpaceLoading", "sortProps", "vSpaceSort", "docs", "documentAccoirdianProps", "vSpaceDocs", "docsDescription", "calenderEvents", "length", "map", "item", "doc_src", "flat", "h6", "updateLoading", "updateSort", "document", "docSrc", "ReactImages", "gallery", "images", "imageSource", "images_src", "RKITable", "paginationComponentOptions", "rowsPerPageText", "columns", "data", "totalRows", "resetPaginationToggle", "subheader", "subHeaderComponent", "handlePerRowsChange", "handlePageChange", "rowsPerPage", "defaultRowsPerPage", "selectableRows", "pagServer", "onSelectedRowsChange", "clearSelectedRows", "sortServer", "onSort", "persistTableHead", "sortFunction", "otherProps", "dataTableProps", "noDataComponent", "<PERSON><PERSON><PERSON><PERSON>", "dense", "paginationResetDefaultPage", "subHeader", "progressPending", "pagination", "paginationServer", "paginationPerPage", "paginationRowsPerPageOptions", "paginationTotalRows", "onChangeRowsPerPage", "onChangePage", "progressComponent", "CustomLoader", "sortIcon", "i", "DataTable", "defaultProps", "ListOfAnnouncementItem", "announcements", "index", "Row", "AnnouncementItem", "router", "useRouter", "query", "setAnnouncements", "cindex", "setCindex", "carouselItemCount", "setEmptyNotice", "updatesParams", "show_as_announcement", "parent_vspace", "sort", "created_at", "limit", "select", "fetchAnnouncements", "params", "response", "apiService", "get", "_", "partition", "useEffect", "toggleCarousel", "min", "max", "direction", "Container", "fluid", "Col", "xs", "a", "Carousel", "indicators", "controls", "interval", "activeIndex", "p", "handleSort", "column", "sortDirection", "columnSelector", "objSlect", "selector", "name", "width", "cell", "d", "extension", "original_name", "href", "process", "_id", "target", "split", "slice", "join", "sortable", "description", "updated_at", "moment", "format", "canAddVspace", "connectedAuthWrapper", "authenticatedSelector", "state", "permissions", "vspace", "wrapperDisplayName", "FailureComponent", "R403", "canEditVspace", "user", "update", "Link", "as", "img", "src", "alt", "newFunction", "title", "getTrimmedString", "createElement", "innerHTML", "html", "string", "textContent", "innerText", "truncate<PERSON><PERSON>th", "substring"], "sourceRoot": "", "ignoreList": []}