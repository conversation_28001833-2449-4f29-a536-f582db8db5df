"use strict";(()=>{var e={};e.id=4564,e.ids=[636,3220,4564],e.modules={123:e=>{e.exports=require("dom-helpers/removeEventListener")},1332:e=>{e.exports=require("react-custom-scrollbars-2")},1428:e=>{e.exports=import("axios")},1680:e=>{e.exports=require("@restart/ui/utils")},1919:e=>{e.exports=require("react-transition-group/Transition")},3892:e=>{e.exports=require("classnames")},4048:e=>{e.exports=require("react-truncate")},6009:e=>{e.exports=require("dom-helpers/ownerDocument")},6417:(e,r,t)=>{t.d(r,{A:()=>a});let s=t(82015).createContext(null);s.displayName="CardHeaderContext";let a=s},6858:(e,r,t)=>{t.d(r,{A:()=>p});var s=t(8732),a=t(82015),o=t.n(a),i=t(18597),n=t(78219),l=t.n(n);function d(e){let{list:r,dialogClassName:t}=e;return(0,s.jsxs)(l(),{...e,dialogClassName:t,"aria-labelledby":"contained-modal-title-vcenter",centered:!0,children:[(0,s.jsx)(l().Header,{closeButton:!0,children:(0,s.jsx)(l().Title,{id:"contained-modal-title-vcenter",children:r.heading})}),(0,s.jsx)(l().Body,{children:r.body})]})}function u(e){let{list:r}=e,[t,a]=o().useState(!1);return r&&r.body?(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)("button",{type:"button",onClick:()=>a(!0),style:{border:"none",background:"none",padding:0},children:(0,s.jsx)(i.A.Footer,{children:(0,s.jsx)("i",{className:"fas fa-chevron-down"})})}),e.list&&(0,s.jsx)(d,{list:e.list,show:t,onHide:()=>a(!1),dialogClassName:e.dialogClassName})]}):null}let p=function(e){let{header:r,body:t}=e;return(0,s.jsxs)(i.A,{className:"text-center infoCard",children:[(0,s.jsx)(i.A.Header,{children:r}),(0,s.jsx)(i.A.Body,{children:(0,s.jsx)(i.A.Text,{children:t})}),(0,s.jsx)(u,{...e})]})}},6952:e=>{e.exports=require("@restart/ui/Modal")},7374:e=>{e.exports=require("@restart/ui/Dropdown")},8732:e=>{e.exports=require("react/jsx-runtime")},9532:e=>{e.exports=require("@restart/ui/Nav")},9653:e=>{e.exports=require("@restart/ui/Overlay")},11242:e=>{e.exports=require("redux-persist/integration/react")},11688:e=>{e.exports=require("@restart/hooks/useWillUnmount")},12388:e=>{e.exports=require("dom-helpers/addEventListener")},13364:e=>{e.exports=require("redux-saga/effects")},14062:e=>{e.exports=import("react-redux")},14078:e=>{e.exports=import("swr")},14332:e=>{e.exports=require("uncontrollable")},16116:e=>{e.exports=require("invariant")},18597:(e,r,t)=>{t.d(r,{A:()=>y});var s=t(3892),a=t.n(s),o=t(82015),i=t(80739),n=t(8732);let l=o.forwardRef(({className:e,bsPrefix:r,as:t="div",...s},o)=>(r=(0,i.oU)(r,"card-body"),(0,n.jsx)(t,{ref:o,className:a()(e,r),...s})));l.displayName="CardBody";let d=o.forwardRef(({className:e,bsPrefix:r,as:t="div",...s},o)=>(r=(0,i.oU)(r,"card-footer"),(0,n.jsx)(t,{ref:o,className:a()(e,r),...s})));d.displayName="CardFooter";var u=t(6417);let p=o.forwardRef(({bsPrefix:e,className:r,as:t="div",...s},l)=>{let d=(0,i.oU)(e,"card-header"),p=(0,o.useMemo)(()=>({cardHeaderBsPrefix:d}),[d]);return(0,n.jsx)(u.A.Provider,{value:p,children:(0,n.jsx)(t,{ref:l,...s,className:a()(r,d)})})});p.displayName="CardHeader";let c=o.forwardRef(({bsPrefix:e,className:r,variant:t,as:s="img",...o},l)=>{let d=(0,i.oU)(e,"card-img");return(0,n.jsx)(s,{ref:l,className:a()(t?`${d}-${t}`:d,r),...o})});c.displayName="CardImg";let x=o.forwardRef(({className:e,bsPrefix:r,as:t="div",...s},o)=>(r=(0,i.oU)(r,"card-img-overlay"),(0,n.jsx)(t,{ref:o,className:a()(e,r),...s})));x.displayName="CardImgOverlay";let m=o.forwardRef(({className:e,bsPrefix:r,as:t="a",...s},o)=>(r=(0,i.oU)(r,"card-link"),(0,n.jsx)(t,{ref:o,className:a()(e,r),...s})));m.displayName="CardLink";var h=t(7783);let f=(0,h.A)("h6"),g=o.forwardRef(({className:e,bsPrefix:r,as:t=f,...s},o)=>(r=(0,i.oU)(r,"card-subtitle"),(0,n.jsx)(t,{ref:o,className:a()(e,r),...s})));g.displayName="CardSubtitle";let q=o.forwardRef(({className:e,bsPrefix:r,as:t="p",...s},o)=>(r=(0,i.oU)(r,"card-text"),(0,n.jsx)(t,{ref:o,className:a()(e,r),...s})));q.displayName="CardText";let j=(0,h.A)("h5"),b=o.forwardRef(({className:e,bsPrefix:r,as:t=j,...s},o)=>(r=(0,i.oU)(r,"card-title"),(0,n.jsx)(t,{ref:o,className:a()(e,r),...s})));b.displayName="CardTitle";let v=o.forwardRef(({bsPrefix:e,className:r,bg:t,text:s,border:o,body:d=!1,children:u,as:p="div",...c},x)=>{let m=(0,i.oU)(e,"card");return(0,n.jsx)(p,{ref:x,...c,className:a()(r,m,t&&`bg-${t}`,s&&`text-${s}`,o&&`border-${o}`),children:d?(0,n.jsx)(l,{children:u}):u})});v.displayName="Card";let y=Object.assign(v,{Img:c,Title:b,Subtitle:g,Body:l,Link:m,Text:q,Header:p,Footer:d,ImgOverlay:x})},18622:e=>{e.exports=require("yup")},19121:e=>{e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},19803:(e,r,t)=>{t.d(r,{A:()=>g});var s=t(3892),a=t.n(s),o=t(82015);t(26324);var i=t(14332),n=t(9532),l=t.n(n),d=t(80739),u=t(81895),p=t.n(u),c=t(25303),x=t(86842),m=t(8732);let h=o.forwardRef(({bsPrefix:e,active:r,disabled:t,eventKey:s,className:o,variant:i,action:n,as:l,...u},h)=>{e=(0,d.oU)(e,"list-group-item");let[f,g]=(0,c.useNavItem)({key:(0,x.makeEventKey)(s,u.href),active:r,...u}),q=p()(e=>{if(t){e.preventDefault(),e.stopPropagation();return}f.onClick(e)});t&&void 0===u.tabIndex&&(u.tabIndex=-1,u["aria-disabled"]=!0);let j=l||(n?u.href?"a":"button":"div");return(0,m.jsx)(j,{ref:h,...u,...f,onClick:q,className:a()(o,e,g.isActive&&"active",t&&"disabled",i&&`${e}-${i}`,n&&`${e}-action`)})});h.displayName="ListGroupItem";let f=o.forwardRef((e,r)=>{let t,{className:s,bsPrefix:o,variant:n,horizontal:u,numbered:p,as:c="div",...x}=(0,i.useUncontrolled)(e,{activeKey:"onSelect"}),h=(0,d.oU)(o,"list-group");return u&&(t=!0===u?"horizontal":`horizontal-${u}`),(0,m.jsx)(l(),{ref:r,...x,as:c,className:a()(s,h,n&&`${h}-${n}`,t&&`${h}-${t}`,p&&`${h}-numbered`)})});f.displayName="ListGroup";let g=Object.assign(f,{Item:h})},22326:e=>{e.exports=require("react-dom")},22541:e=>{e.exports=require("redux-persist/lib/storage")},25303:e=>{e.exports=require("@restart/ui/NavItem")},26324:e=>{e.exports=require("warning")},27825:e=>{e.exports=require("lodash")},27910:e=>{e.exports=require("stream")},28217:e=>{e.exports=require("@restart/ui/DropdownItem")},29021:e=>{e.exports=require("fs")},29294:e=>{e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},29628:e=>{e.exports=require("dom-helpers/addClass")},29825:e=>{e.exports=require("prop-types")},29841:e=>{e.exports=require("dom-helpers/hasClass")},30362:e=>{e.exports=require("@restart/ui/DropdownToggle")},32412:(e,r,t)=>{t.d(r,{A:()=>i});var s=t(8732),a=t(52449),o=t.n(a);function i(){return(0,s.jsxs)(o(),{viewBox:"0 0 380 70",height:50,width:317,speed:2,title:"Loading",foregroundColor:"#f7f7f7",backgroundColor:"#ecebeb",uniqueKey:"operation",children:[(0,s.jsx)("rect",{x:"10",y:"0",rx:"4",ry:"4",width:"320",height:"25"}),(0,s.jsx)("rect",{x:"40",y:"40",rx:"3",ry:"3",width:"250",height:"20"})]})}},33873:e=>{e.exports=require("path")},36653:e=>{e.exports=require("nprogress")},36955:e=>{e.exports=require("@restart/hooks/useBreakpoint")},37766:e=>{e.exports=require("@restart/hooks/useMergedRefs")},39756:e=>{e.exports=import("redux")},40051:e=>{e.exports=require("dom-helpers/removeClass")},40361:e=>{e.exports=require("next/dist/compiled/next-server/pages.runtime.prod.js")},42893:e=>{e.exports=import("react-hot-toast")},43294:e=>{e.exports=require("formik")},50009:e=>{e.exports=require("@restart/ui/DropdownMenu")},52449:e=>{e.exports=require("react-content-loader")},57664:e=>{e.exports=require("dom-helpers/scrollbarSize")},58928:e=>{e.exports=require("dom-helpers/canUseDOM")},59717:e=>{e.exports=require("@restart/ui/DropdownContext")},60560:e=>{e.exports=require("dom-helpers/contains")},62613:(e,r,t)=>{t.a(e,async(e,s)=>{try{t.r(r),t.d(r,{config:()=>f,default:()=>c,getServerSideProps:()=>h,getStaticPaths:()=>m,getStaticProps:()=>x,reportWebVitals:()=>g,routeModule:()=>P,unstable_getServerProps:()=>v,unstable_getServerSideProps:()=>y,unstable_getStaticParams:()=>b,unstable_getStaticPaths:()=>j,unstable_getStaticProps:()=>q});var a=t(63885),o=t(80237),i=t(81413),n=t(9616),l=t.n(n),d=t(72386),u=t(87178),p=e([d,u]);[d,u]=p.then?(await p)():p;let c=(0,i.M)(u,"default"),x=(0,i.M)(u,"getStaticProps"),m=(0,i.M)(u,"getStaticPaths"),h=(0,i.M)(u,"getServerSideProps"),f=(0,i.M)(u,"config"),g=(0,i.M)(u,"reportWebVitals"),q=(0,i.M)(u,"unstable_getStaticProps"),j=(0,i.M)(u,"unstable_getStaticPaths"),b=(0,i.M)(u,"unstable_getStaticParams"),v=(0,i.M)(u,"unstable_getServerProps"),y=(0,i.M)(u,"unstable_getServerSideProps"),P=new a.PagesRouteModule({definition:{kind:o.A.PAGES,page:"/dashboard/OngoingProjects",pathname:"/dashboard/OngoingProjects",bundlePath:"",filename:""},components:{App:d.default,Document:l()},userland:u});s()}catch(e){s(e)}})},63033:e=>{e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},65447:e=>{e.exports=require("@restart/ui/Button")},67364:e=>{e.exports=require("@restart/hooks/useCallbackRef")},68455:e=>{e.exports=import("redux-saga")},69722:e=>{e.exports=require("es6-promise")},74075:e=>{e.exports=require("zlib")},74716:e=>{e.exports=require("moment")},74987:e=>{e.exports=require("@restart/ui/ModalManager")},78097:e=>{e.exports=require("next-redux-saga")},78634:e=>{e.exports=require("@restart/ui/Anchor")},80237:(e,r)=>{Object.defineProperty(r,"A",{enumerable:!0,get:function(){return t}});var t=function(e){return e.PAGES="PAGES",e.PAGES_API="PAGES_API",e.APP_PAGE="APP_PAGE",e.APP_ROUTE="APP_ROUTE",e.IMAGE="IMAGE",e}({})},81366:e=>{e.exports=require("redux-auth-wrapper/connectedAuthWrapper")},81413:(e,r)=>{Object.defineProperty(r,"M",{enumerable:!0,get:function(){return function e(r,t){return t in r?r[t]:"then"in r&&"function"==typeof r.then?r.then(r=>e(r,t)):"function"==typeof r&&"default"===t?r:void 0}}})},81521:e=>{e.exports=require("dom-helpers/querySelectorAll")},81895:e=>{e.exports=require("@restart/hooks/useEventCallback")},82015:e=>{e.exports=require("react")},86842:e=>{e.exports=require("@restart/ui/SelectableContext")},87178:(e,r,t)=>{t.a(e,async(e,s)=>{try{t.r(r),t.d(r,{default:()=>h});var a=t(8732),o=t(82015);t(27825);var i=t(19918),n=t.n(i),l=t(19803),d=t(6858),u=t(32412),p=t(63487),c=e([p]);function x(e){let{list:r}=e;return r.length>0?(0,a.jsx)(l.A,{children:r.map((e,r)=>(0,a.jsx)(l.A.Item,{children:(0,a.jsx)(n(),{href:"/project/[...routes]",as:`/project/show/${e._id}`,children:e.title})},r))}):null}function m(e){let{project:r}=e;return(0,a.jsx)(n(),{href:"/project/[...routes]",as:`/project/show/${r.id}`,className:"active-op-project",children:(0,a.jsx)("span",{className:"project-title link",children:r.body})})}p=(c.then?(await c)():c)[0];let h=function(e){let{t:r,fetchOngoingProjects:t}=e,s=r("OngoingProjects"),[i,n]=(0,o.useState)({body:"",id:"",list:[]}),[l,p]=(0,o.useState)(!0),c={heading:s,body:(0,a.jsx)(x,{list:i.list})};return(0,a.jsx)(d.A,{dialogClassName:"ongoing-project-list",list:c,header:s,body:l?(0,a.jsx)(u.A,{}):(0,a.jsx)(m,{project:i})})};s()}catch(e){s(e)}})},87571:e=>{e.exports=require("dom-helpers/transitionEnd")},88751:e=>{e.exports=require("next-i18next")},93787:e=>{e.exports=require("redux-persist")},94947:e=>{e.exports=require("@restart/hooks/useTimeout")},96196:e=>{e.exports=require("next-redux-wrapper")},98320:e=>{e.exports=require("@restart/hooks/useIsomorphicEffect")},99460:e=>{e.exports=require("dom-helpers/css")}};var r=require("../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[6089,9216,9616,2386],()=>t(62613));module.exports=s})();