{"version": 3, "file": "static/chunks/pages/adminsettings/updateType/updateTypeTable-aaf5e30c039133d0.js", "mappings": "2OAqIA,MAzHyBA,IACrB,GAAM,CAAEC,CAAC,CAAE,CAAGC,CAAAA,EAAAA,EAAAA,EAAAA,CAAcA,CAAC,QAwHlBC,EAvHL,CAACC,EAAWC,EAAe,CAAGC,CAAAA,EAAAA,EAAAA,EAuHVH,EAAC,IAvHSG,CAAQA,CAAC,EAAE,EACzC,EAAGC,EAAW,CAAGD,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,EAAC,GAC1B,CAACE,EAAWC,EAAa,CAAGH,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAC,GACrC,CAACI,EAASC,EAAW,CAAGL,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAC,IACjC,CAACM,EAAaC,EAAS,CAAGP,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,EAAC,GACnC,CAACQ,EAAkBC,EAAoB,CAAGT,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAC,CAAC,GACpDU,EAAY,IAAMH,GAAS,GAE3BI,EAAU,CACZ,CACIC,KAAMjB,EAAE,kCACRkB,SAAU,OACd,EACA,CACID,KAAMjB,EAAE,iCACRkB,SAAU,OACVC,KAAM,GAAY,UAACC,IAAAA,CAAEC,UAAW,OAAc,OAAPC,EAAEC,IAAI,GACjD,EACA,CACIN,KAAMjB,EAAE,mCACRkB,SAAU,GACVC,KAAM,GACF,WAACK,MAAAA,WACG,UAACC,IAAIA,CAACC,KAAK,6BAA6BC,GAAI,OAAvCF,4BAAgF,OAANH,EAAEM,GAAG,WAEhF,UAACR,IAAAA,CAAEC,UAAU,uBAEV,OAEP,UAACQ,IAAAA,CAAEC,QAAS,IAAMC,EAAWT,YACzB,UAACF,IAAAA,CAAEC,UAAU,4BACZ,MAGjB,EACH,CACKW,EAAmB,CACrBC,KAAM,CAAEC,MAAO,KAAM,EACrBC,MAAO1B,EACP2B,KAAM,EACNC,MAAO,CAAC,CACZ,EAEAC,CAAAA,EAAAA,EAAAA,SAAAA,CAASA,CAAC,KACNC,EAAkBP,EACtB,EAAG,EAAE,EAEL,IAAMO,EAAoB,MAAOC,IAC7BlC,GAAW,GACX,IAAMmC,EAAW,MAAMC,EAAAA,CAAUA,CAACC,GAAG,CAAC,cAAeH,GACjDC,GAAYA,EAASG,IAAI,EAAIH,EAASG,IAAI,CAACC,MAAM,CAAG,GAAG,CACvDzC,EAAeqC,EAASG,IAAI,EAC5BpC,EAAaiC,EAASK,UAAU,EAChCxC,GAAW,GAEnB,EAQMyC,EAAsB,MAAOC,EAAiBZ,KAChDJ,EAAiBG,KAAK,CAAGa,EACzBhB,EAAiBI,IAAI,CAAGA,EACxB9B,GAAW,GACX,IAAMmC,EAAW,MAAMC,EAAAA,CAAUA,CAACC,GAAG,CAAC,cAAeX,GACjDS,GAAYA,EAASG,IAAI,EAAIH,EAASG,IAAI,CAACC,MAAM,CAAG,GAAG,CACvDzC,EAAeqC,EAASG,IAAI,EAC5BlC,EAAWsC,GACX1C,GAAW,GAEnB,EAEM2C,EAAe,UACjB,GAAI,CACA,MAAMP,EAAAA,CAAUA,CAACQ,MAAM,CAAC,eAAgC,OAAjBrC,IACvC0B,EAAkBP,GAClBpB,GAAS,GACTuC,EAAAA,EAAKA,CAACC,OAAO,CAACpD,EAAE,gEACpB,CAAE,MAAOqD,EAAO,CACZF,EAAAA,EAAKA,CAACE,KAAK,CAACrD,EAAE,0DAClB,CACJ,EAEM+B,EAAa,MAAOuB,IACtBxC,EAAoBwC,EAAI1B,GAAG,EAC3BhB,GAAS,EACb,EAEA,MACI,WAACY,MAAAA,WACG,WAAC+B,EAAAA,CAAKA,CAAAA,CAACC,KAAM7C,EAAa8C,OAAQ1C,YAC9B,UAACwC,EAAAA,CAAKA,CAACG,MAAM,EAACC,WAAW,aACrB,UAACJ,EAAAA,CAAKA,CAACK,KAAK,WAAE5D,EAAE,iDAEpB,UAACuD,EAAAA,CAAKA,CAACM,IAAI,WAAE7D,EAAE,oEACf,WAACuD,EAAAA,CAAKA,CAACO,MAAM,YACT,UAACC,EAAAA,CAAMA,CAAAA,CAACC,QAAQ,YAAYlC,QAASf,WAChCf,EAAE,qCAEP,UAAC+D,EAAAA,CAAMA,CAAAA,CAACC,QAAQ,UAAUlC,QAASmB,WAC9BjD,EAAE,wCAKf,UAACiE,EAAAA,CAAQA,CAAAA,CACLjD,QAASA,EACT4B,KAAMzC,EACNI,UAAWA,EACX2D,UAAW,GACXnB,oBAAqBA,EACrBoB,iBAzDa,CAyDKA,GAxD1BnC,EAAiBG,KAAK,CAAG1B,EACzBuB,EAAiBI,IAAI,CAAGA,EACxBG,EAAkBP,EACtB,MAyDJ,6GC/FA,SAASiC,EAASG,CAAoB,EACpC,GAAM,CAAEpE,CAAC,CAAE,CAAGC,CAAAA,EAAAA,EAAAA,EAAAA,CAAcA,CAAC,UACvBoE,EAA6B,CACjCC,gBAAiBtE,EAAE,cACnB,EACI,SACJgB,CAAO,MACP4B,CAAI,WACJrC,CAAS,uBACTgE,CAAqB,WACrBC,CAAS,oBACTC,CAAkB,qBAClB1B,CAAmB,kBACnBoB,CAAgB,aAChBO,CAAW,oBACXC,CAAkB,gBAClBC,CAAc,SACdC,CAAO,WACPX,CAAS,sBACTY,CAAoB,mBACpBC,CAAiB,YACjBC,CAAU,QACVC,CAAM,kBACNC,CAAgB,cAChBC,CAAY,CAEZ,CADA,EACGC,EACJ,CAAGhB,EAGEiB,EAAiB,4BACrBhB,EACAiB,gBAAiBtF,EAAE,IAP0C,MAQ7DuF,UAAU,UACVvE,EACA4B,KAAMA,GAAQ,EAAE,CAChB4C,OAAO,EACPC,2BAA4BlB,EAC5BmB,UAAWlB,EACXmB,gBAAiBd,qBACjBJ,EACAmB,YAAY,EACZC,iBAAkB3B,EAClB4B,kBAAmBnB,GAA0C,GAC7DoB,eADwCpB,cACVD,GAA4B,CAAC,GAAI,GAAI,GAAI,CAA3BA,EAA+B,GAAG,CAC9EsB,oBAAqBzF,EACrB0F,oBAAqBlD,EACrBmD,aAAc/B,iBACdS,uBACAE,oBACAC,EACAoB,kBAAmB,UAACC,EAAAA,CAAYA,CAAAA,CAAAA,GAChCC,SAAU,UAACjF,IAAAA,CAAEC,UAAU,6CACvB2D,SACAC,eACAE,mBACAD,EACA7D,UAAW,WACb,EACA,MACE,UAACiF,EAAAA,EAASA,CAAAA,CAAE,GAAGjB,CAAc,EAEjC,CAEApB,EAASsC,YAAY,CAAG,CACtBb,WAAW,EACXE,WAAY,GACZrF,UAAW,KACX2D,WAAW,EACXY,qBAAsB,KACtBC,mBAAmB,EACnBC,YAAY,EACZE,kBAAkB,CACpB,EAEA,MAAejB,QAAQA,EAAC,SC/GxB,4CACA,4CACA,WACA,OAAe,EAAQ,KAAiE,CACxF,EACA,SAFsB", "sources": ["webpack://_N_E/./pages/adminsettings/updateType/updateTypeTable.tsx", "webpack://_N_E/./components/common/RKITable.tsx", "webpack://_N_E/?21d1"], "sourcesContent": ["//Import Library\r\nimport Link from \"next/link\";\r\nimport { useState, useEffect } from \"react\";\r\nimport { <PERSON><PERSON>, But<PERSON> } from \"react-bootstrap\";\r\n\r\nimport toast from 'react-hot-toast';\r\n\r\n//Import services/components\r\nimport RKITable from \"../../../components/common/RKITable\";\r\nimport { useTranslation } from 'next-i18next';\r\nimport apiService from \"../../../services/apiService\";\r\n\r\nconst UpdateTypeTable = (_props: any) => {\r\n    const { t } = useTranslation('common');\r\n    const [tabledata, setDataToTable] = useState([]);\r\n    const [, setLoading] = useState(false);\r\n    const [totalRows, setTotalRows] = useState(0);\r\n    const [perPage, setPerPage] = useState(10);\r\n    const [isModalShow, setModal] = useState(false);\r\n    const [selectUpdateType, setSelectUpdateType] = useState({});\r\n    const modalHide = () => setModal(false);\r\n    \r\n    const columns = [\r\n        {\r\n            name: t(\"adminsetting.updatestype.Title\"),\r\n            selector: \"title\",\r\n        },\r\n        {\r\n            name: t(\"adminsetting.updatestype.Icon\"),\r\n            selector: \"icon\",\r\n            cell: (d: any) => <i className={`fas ${d.icon}`}></i>,\r\n        },\r\n        {\r\n            name: t(\"adminsetting.updatestype.Action\"),\r\n            selector: \"\",\r\n            cell: (d: any) => (\r\n                <div>\r\n                    <Link href=\"/adminsettings/[...routes]\" as={`/adminsettings/edit_update_type/${d._id}`}>\r\n\r\n                        <i className=\"icon fas fa-edit\" />\r\n\r\n                    </Link>\r\n                    &nbsp;\r\n                    <a onClick={() => userAction(d)}>\r\n                        <i className=\"icon fas fa-trash-alt\" />\r\n                    </a>{\" \"}\r\n                </div>\r\n            ),\r\n        },\r\n    ];\r\n    const updateTypeParams = {\r\n        sort: { title: \"asc\" },\r\n        limit: perPage,\r\n        page: 1,\r\n        query: {},\r\n    };\r\n\r\n    useEffect(() => {\r\n        getUpdateTypeData(updateTypeParams);\r\n    }, []);\r\n\r\n    const getUpdateTypeData = async (updateTypeParams_initials: any) => {\r\n        setLoading(true);\r\n        const response = await apiService.get(\"/updatetype\", updateTypeParams_initials);\r\n        if (response && response.data && response.data.length > 0) {\r\n            setDataToTable(response.data);\r\n            setTotalRows(response.totalCount);\r\n            setLoading(false);\r\n        }\r\n    };\r\n\r\n    const handlePageChange = (page: any) => {\r\n        updateTypeParams.limit = perPage;\r\n        updateTypeParams.page = page;\r\n        getUpdateTypeData(updateTypeParams);\r\n    };\r\n\r\n    const handlePerRowsChange = async (newPerPage: any, page: any) => {\r\n        updateTypeParams.limit = newPerPage;\r\n        updateTypeParams.page = page;\r\n        setLoading(true);\r\n        const response = await apiService.get(\"/updatetype\", updateTypeParams);\r\n        if (response && response.data && response.data.length > 0) {\r\n            setDataToTable(response.data);\r\n            setPerPage(newPerPage);\r\n            setLoading(false);\r\n        }\r\n    };\r\n\r\n    const modalConfirm = async () => {\r\n        try {\r\n            await apiService.remove(`/updatetype/${selectUpdateType}`);\r\n            getUpdateTypeData(updateTypeParams);\r\n            setModal(false);\r\n            toast.success(t(\"adminsetting.updatestype.Table.updateTypeDeletedSuccessfully\"));\r\n        } catch (error) {\r\n            toast.error(t(\"adminsetting.updatestype.Table.errorDeletingUpdateType\"));\r\n        }\r\n    };\r\n\r\n    const userAction = async (row: any) => {\r\n        setSelectUpdateType(row._id);\r\n        setModal(true);\r\n    };\r\n\r\n    return (\r\n        <div>\r\n            <Modal show={isModalShow} onHide={modalHide}>\r\n                <Modal.Header closeButton>\r\n                    <Modal.Title>{t(\"adminsetting.updatestype.DeleteUpdateType\")}</Modal.Title>\r\n                </Modal.Header>\r\n                <Modal.Body>{t(\"adminsetting.updatestype.Areyousurewanttodeletethisupdatetype?\")}</Modal.Body>\r\n                <Modal.Footer>\r\n                    <Button variant=\"secondary\" onClick={modalHide}>\r\n                        {t(\"adminsetting.updatestype.Cancel\")}\r\n                    </Button>\r\n                    <Button variant=\"primary\" onClick={modalConfirm}>\r\n                        {t(\"adminsetting.updatestype.Yes\")}\r\n                    </Button>\r\n                </Modal.Footer>\r\n            </Modal>\r\n\r\n            <RKITable\r\n                columns={columns}\r\n                data={tabledata}\r\n                totalRows={totalRows}\r\n                pagServer={true}\r\n                handlePerRowsChange={handlePerRowsChange}\r\n                handlePageChange={handlePageChange}\r\n            />\r\n        </div>\r\n    );\r\n};\r\nexport default UpdateTypeTable;\r\n", "//Import Library\r\nimport React from 'react'\r\nimport DataTable  from 'react-data-table-component';\r\n\r\n//Import services/components\r\nimport { useTranslation } from 'next-i18next';\r\nimport CustomLoader from './CustomLoader';\r\n\r\ninterface RKITableProps {\r\n  columns: any[];\r\n  data: any[];\r\n  totalRows?: number;\r\n  resetPaginationToggle?: boolean;\r\n  subheader?: boolean;\r\n  subHeaderComponent?: React.ReactNode;\r\n  handlePerRowsChange?: (newPerPage: number, page: number) => void;\r\n  handlePageChange?: (page: number) => void;\r\n  rowsPerPage?: number[];\r\n  defaultRowsPerPage?: number;\r\n  selectableRows?: boolean;\r\n  loading?: boolean;\r\n  pagServer?: boolean;\r\n  onSelectedRowsChange?: (selectedRows: any) => void;\r\n  clearSelectedRows?: boolean;\r\n  sortServer?: boolean;\r\n  onSort?: (column: any, sortDirection: string) => void;\r\n  persistTableHead?: boolean;\r\n  sortFunction?: (rows: any[], field: any, direction: 'asc' | 'desc') => any[];\r\n  noHeader?: boolean;\r\n  dense?: boolean;\r\n  pagination?: boolean;\r\n  paginationServer?: boolean;\r\n  paginationTotalRows?: number;\r\n  subHeaderAlign?: string;\r\n  [key: string]: any;\r\n}\r\n\r\nfunction RKITable(props: RKITableProps) {\r\n  const { t } = useTranslation('common');\r\n  const paginationComponentOptions = {\r\n    rowsPerPageText: t('Rowsperpage'),\r\n    };\r\n  const {\r\n    columns,\r\n    data,\r\n    totalRows,\r\n    resetPaginationToggle,\r\n    subheader,\r\n    subHeaderComponent,\r\n    handlePerRowsChange,\r\n    handlePageChange,\r\n    rowsPerPage,\r\n    defaultRowsPerPage,\r\n    selectableRows,\r\n    loading,\r\n    pagServer,\r\n    onSelectedRowsChange,\r\n    clearSelectedRows,\r\n    sortServer,\r\n    onSort,\r\n    persistTableHead,\r\n    sortFunction,\r\n    // Filter out any unknown props that might cause DOM warnings\r\n    ...otherProps\r\n  } = props;\r\n\r\n  // Only pass known DataTable props to avoid DOM warnings\r\n  const dataTableProps = {\r\n    paginationComponentOptions,\r\n    noDataComponent: t(\"NoData\"),\r\n    noHeader: true,\r\n    columns,\r\n    data: data || [],\r\n    dense: true,\r\n    paginationResetDefaultPage: resetPaginationToggle,\r\n    subHeader: subheader,\r\n    progressPending: loading,\r\n    subHeaderComponent,\r\n    pagination: true,\r\n    paginationServer: pagServer,\r\n    paginationPerPage: defaultRowsPerPage ? defaultRowsPerPage : 10,\r\n    paginationRowsPerPageOptions: rowsPerPage ? rowsPerPage : [10, 15, 20, 25, 30],\r\n    paginationTotalRows: totalRows,\r\n    onChangeRowsPerPage: handlePerRowsChange,\r\n    onChangePage: handlePageChange,\r\n    selectableRows,\r\n    onSelectedRowsChange,\r\n    clearSelectedRows,\r\n    progressComponent: <CustomLoader/>,\r\n    sortIcon: <i className=\"sort-icon fas fa-exchange-alt\" />,\r\n    sortServer,\r\n    onSort,\r\n    sortFunction,\r\n    persistTableHead,\r\n    className: \"rki-table\"\r\n  };\r\n  return (\r\n    <DataTable {...dataTableProps} />\r\n  )\r\n};\r\n\r\nRKITable.defaultProps = {\r\n  subHeader: false,\r\n  pagination: true,\r\n  totalRows: null,\r\n  pagServer: true,\r\n  onSelectedRowsChange: null,\r\n  clearSelectedRows: false,\r\n  sortServer: false,\r\n  persistTableHead: false\r\n}\r\n\r\nexport default RKITable;\r\n\r\n", "\n    (window.__NEXT_P = window.__NEXT_P || []).push([\n      \"/adminsettings/updateType/updateTypeTable\",\n      function () {\n        return require(\"private-next-pages/adminsettings/updateType/updateTypeTable.tsx\");\n      }\n    ]);\n    if(module.hot) {\n      module.hot.dispose(function () {\n        window.__NEXT_P.push([\"/adminsettings/updateType/updateTypeTable\"])\n      });\n    }\n  "], "names": ["_props", "t", "useTranslation", "UpdateTypeTable", "tabledata", "setDataToTable", "useState", "setLoading", "totalRows", "setTotalRows", "perPage", "setPerPage", "isModalShow", "setModal", "selectUpdateType", "setSelectUpdateType", "modalHide", "columns", "name", "selector", "cell", "i", "className", "d", "icon", "div", "Link", "href", "as", "_id", "a", "onClick", "userAction", "updateTypeParams", "sort", "title", "limit", "page", "query", "useEffect", "getUpdateTypeData", "updateTypeParams_initials", "response", "apiService", "get", "data", "length", "totalCount", "handlePerRowsChange", "newPerPage", "modalConfirm", "remove", "toast", "success", "error", "row", "Modal", "show", "onHide", "Header", "closeButton", "Title", "Body", "Footer", "<PERSON><PERSON>", "variant", "RKITable", "pagServer", "handlePageChange", "props", "paginationComponentOptions", "rowsPerPageText", "resetPaginationToggle", "subheader", "subHeaderComponent", "rowsPerPage", "defaultRowsPerPage", "selectableRows", "loading", "onSelectedRowsChange", "clearSelectedRows", "sortServer", "onSort", "persistTableHead", "sortFunction", "otherProps", "dataTableProps", "noDataComponent", "<PERSON><PERSON><PERSON><PERSON>", "dense", "paginationResetDefaultPage", "subHeader", "progressPending", "pagination", "paginationServer", "paginationPerPage", "paginationRowsPerPageOptions", "paginationTotalRows", "onChangeRowsPerPage", "onChangePage", "progressComponent", "CustomLoader", "sortIcon", "DataTable", "defaultProps"], "sourceRoot": "", "ignoreList": []}