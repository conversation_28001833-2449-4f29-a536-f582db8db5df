{"version": 3, "file": "static/chunks/pages/operation/components/OperationInfo-b88a2f5f45fbea66.js", "mappings": "4SAOO,IAAMA,EAAkBC,CAAAA,EAAAA,EAAAA,CAAAA,CAAoBA,CAAC,CAClDC,sBAAuB,KACjBC,EAAMC,WAAW,IAAID,EAAMC,WAAW,CAACC,SAAS,IAAIF,EAAMC,WAAW,CAACC,SAAS,CAAC,aAAa,CAKnGC,CALqG,kBAKjF,iBACtB,GAAG,EAEgCL,CAAAA,EAAAA,EAAAA,CAAAA,CAAoBA,CAAC,CACtDC,sBAAuB,KACjBC,EAAMC,WAAW,IAAID,EAAMC,WAAW,CAACC,SAAS,IAAIF,EAAMC,WAAW,CAACC,SAAS,CAAC,aAAa,CAKnGC,CALqG,kBAKjF,sBACpBC,iBAAkB,IAAM,UAACC,EAAAA,OAAIA,CAAAA,CAAAA,EAC/B,GAAG,EAE6BP,CAAAA,EAAAA,EAAAA,CAAAA,CAAoBA,CAAC,CACnDC,sBAAuB,CAACC,EAAOM,KAC7B,GAAIN,EAAMC,WAAW,EAAID,EAAMC,WAAW,CAACC,SAAS,EAAE,GAChDF,EAAMC,WAAW,CAACC,SAAS,CAAC,aAAa,CAC3C,CAD6C,MACtC,OAEP,GAAIF,EAAMC,WAAW,CAACC,SAAS,CAAC,aAAa,EAAE,EACnCA,SAAS,EAAII,EAAMJ,SAAS,CAACK,IAAI,EAAID,EAAMJ,SAAS,CAACK,IAAI,CAACC,GAAG,GAAKR,EAAMO,IAAI,CAACC,GAAG,CACxF,CAD0F,MACnF,CAGb,CAEF,OAAO,CACT,EACAL,mBAAoB,kBACtB,GAAG,EAEiCL,CAAAA,EAAAA,EAAAA,CAAAA,CAAoBA,CAAC,CACvDC,sBAAuB,CAACC,EAAOM,KAC7B,GAAIN,EAAMC,WAAW,EAAID,EAAMC,WAAW,CAACC,SAAS,EAAE,GAChDF,EAAMC,WAAW,CAACC,SAAS,CAAC,aAAa,CAC3C,CAD6C,MACtC,OAEP,GAAIF,EAAMC,WAAW,CAACC,SAAS,CAAC,aAAa,EAAE,EACnCA,SAAS,EAAII,EAAMJ,SAAS,CAACK,IAAI,EAAID,EAAMJ,SAAS,CAACK,IAAI,CAACC,GAAG,GAAKR,EAAMO,IAAI,CAACC,GAAG,CACxF,CAD0F,MACnF,CAGb,CAEF,OAAO,CACT,EACAL,mBAAoB,uBACpBC,iBAAkB,IAAM,UAACC,EAAAA,OAAIA,CAAAA,CAAAA,EAC/B,GAAG,EAEoCP,CAAAA,EAAAA,EAAAA,CAAAA,CAAoBA,CAAC,CAC1DC,sBAAuB,KACjBC,EAAMC,WAAW,IAAID,EAAMC,WAAW,CAACQ,MAAM,IAAIT,EAAMC,WAAW,CAACQ,MAAM,CAAC,WAAW,CAK3FN,CAL6F,kBAKzE,yBACtB,GAAG,EAEYN,eAAeA,EAAC,sGClE/B,IAAMa,EAAiB,CACrBR,UAAW,YACXS,YAAa,cACbC,MAAO,QACPC,QAAS,UACTC,OAAQ,QACV,EAoEA,EAAeC,CAAAA,EAAAA,EAAAA,EAAAA,CAAOA,CAAC,GAAgBf,GA1DG,IACxC,GAAM,MAAEO,CAAI,CAyD0C,SAzDxCS,CAAQ,YAAEC,CAAU,CAAE,CAAGX,EACjC,CAACY,EAAUC,EAAY,CAAGC,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,EAAU,GAC5C,CAACC,EAAWC,EAAa,CAAGF,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAM,IAG1CG,EAA2B,UAC/B,GAAI,QAAChB,EAAAA,KAAAA,EAAAA,EAAMC,GAAAA,EAAK,CAAXD,MACL,IAAMiB,EAAY,MAAMC,EAAAA,CAAUA,CAACC,GAAG,CAAC,QAAS,CAACC,MAAO,CAACC,UAAWZ,EAAUT,KAAMA,EAAKC,GAAG,CAAEqB,QAASnB,CAAc,CAACO,EAAW,CAAC,GAC9HO,GAAaA,EAAUM,IAAI,EAAIN,EAAUM,IAAI,CAACC,MAAM,CAAG,GAAG,CAC5DT,EAAaE,EAAUM,IAAI,CAAC,EAAE,EAC9BX,GAAY,GAEhB,EAEMa,EAAkB,MAAOC,IAE7B,GADAA,EAAEC,cAAc,GACZ,QAAC3B,EAAAA,KAAAA,EAAAA,EAAMC,GAAAA,EAAK,CAAXD,MACL,IAAM4B,EAAQ,CAACjB,EACTkB,EAAc,CAClBC,YAAapB,EACbW,UAAWZ,EACXT,KAAMA,EAAKC,GAAG,CACdqB,QAASnB,CAAc,CAACO,EAAW,EAErC,GAAIkB,EAAM,CACR,IAAMG,EAAc,MAAMb,EAAAA,CAAUA,CAACc,IAAI,CAAC,QAASH,GAC/CE,GAAUA,EAAO9B,GAAG,EAAE,CACxBc,EAAagB,GACbnB,EAAYgB,GAEhB,KAAO,CACL,IAAMK,EAAW,MAAMf,EAAAA,CAAUA,CAACgB,MAAM,CAAC,SAAuB,OAAdpB,EAAUb,GAAG,GAC3DgC,GAAYA,EAASE,CAAC,EAAE,EACdP,EAEhB,CACF,EAKA,MAHAQ,CAAAA,EAAAA,EAAAA,SAAAA,CAASA,CAAC,KACRpB,GACF,EAAE,EAAE,EAEF,UAACqB,MAAAA,CAAIC,UAAU,0BACb,WAACC,IAAAA,CAAEC,KAAK,GAAGC,QAAShB,YAClB,UAACiB,OAAAA,CAAKJ,UAAU,iBACb3B,EACC,UAACgC,EAAAA,CAAeA,CAAAA,CAACL,UAAU,sBAAsBM,KAAMC,EAAAA,GAAaA,CAAEC,MAAM,YAE5E,UAACH,EAAAA,CAAeA,CAAAA,CAACL,UAAU,sBAAsBM,KAAMG,EAAAA,GAAYA,CAAED,MAAM,WAG/E,UAACH,EAAAA,CAAeA,CAAAA,CAACL,UAAU,WAAWM,KAAMI,EAAAA,GAAUA,CAAEF,MAAM,gBAItE,oBChFA,4CACA,sCACA,WACA,OAAe,EAAQ,KAA2D,CAClF,EACA,SAFsB,uKC8CtB,MArCsB,IAClB,GAAM,CAAEG,CAAC,CAAE,CAAGC,CAAAA,EAAAA,EAAAA,EAAAA,CAAcA,CAAC,QAoClBC,EAnCLC,EAAoB,IAElB,OAiCiB,CAjCjB,uBACKC,EAAKC,QAAQ,CACV,UAACC,IAAIA,CACDf,KAAK,yBACLgB,GAAI,WAFHD,QAE+C,OAAzBF,EAAKI,SAAS,CAACC,MAAM,CAAC,EAAE,WAE/C,WAACC,EAAAA,CAAMA,CAAAA,CAACC,QAAQ,YAAYC,KAAK,eAC7B,UAAClB,EAAAA,CAAeA,CAAAA,CAACC,KAAMkB,EAAAA,GAAKA,GAAI,OACzBb,EAAE,aAGnB,KAKRc,EAAmBC,CAAAA,EAAAA,EAAAA,gBAAAA,CAAgBA,CAAC,IAAM,UAACZ,EAAAA,CAAAA,IAEjD,MACI,+BACI,WAACa,UAAAA,CAAQ3B,UAAU,2CACf,WAAC4B,KAAAA,CAAG5B,UAAU,2BACTe,EAAK1D,SAAS,CAACwE,KAAK,CAAC,WACrBd,EAAKI,SAAS,CAACC,MAAM,EAAIL,EAAKI,SAAS,CAACC,MAAM,CAAC,EAAE,CAC9C,UAACK,EAAAA,CAAiBpE,UAAW0D,EAAK1D,SAAS,GAC3C,QAER,UAACyE,EAAAA,CAAQA,CAAAA,CAAC3D,SAAU4C,EAAKI,SAAS,CAACC,MAAM,CAAC,EAAE,CAAEhD,WAAW,kBAIzE", "sources": ["webpack://_N_E/./pages/operation/permission.tsx", "webpack://_N_E/./components/common/Bookmark.tsx", "webpack://_N_E/?0238", "webpack://_N_E/./pages/operation/components/OperationInfo.tsx"], "sourcesContent": ["//Import Library\r\nimport React from 'react';\r\nimport connectedAuthWrapper from 'redux-auth-wrapper/connectedAuthWrapper';\r\n\r\n//Import services/components\r\nimport R403 from \"../r403\";\r\n\r\nexport const canAddOperation = connectedAuthWrapper({\r\n  authenticatedSelector: (state) => {\r\n    if (state.permissions && state.permissions.operation && state.permissions.operation['create:any']) {\r\n      return true;\r\n    }\r\n    return false;\r\n  },\r\n  wrapperDisplayName: 'CanAddOperation',\r\n});\r\n\r\nexport const canAddOperationForm = connectedAuthWrapper({\r\n  authenticatedSelector: (state) => {\r\n    if (state.permissions && state.permissions.operation && state.permissions.operation['create:any']) {\r\n      return true;\r\n    }\r\n    return false;\r\n  },\r\n  wrapperDisplayName: 'CanAddOperationForm',\r\n  FailureComponent: () => <R403/>\r\n});\r\n\r\nexport const canEditOperation = connectedAuthWrapper({\r\n  authenticatedSelector: (state, props) => {\r\n    if (state.permissions && state.permissions.operation) {\r\n      if (state.permissions.operation['update:any']) {\r\n        return true;\r\n      } else { //if update:own\r\n        if (state.permissions.operation['update:own']) {\r\n          if (props.operation && props.operation.user && props.operation.user._id === state.user._id) {\r\n            return true;\r\n          }\r\n        }\r\n      }\r\n    }\r\n    return false;\r\n  },\r\n  wrapperDisplayName: 'CanEditOperation',\r\n});\r\n\r\nexport const canEditOperationForm = connectedAuthWrapper({\r\n  authenticatedSelector: (state, props) => {\r\n    if (state.permissions && state.permissions.operation) {\r\n      if (state.permissions.operation['update:any']) {\r\n        return true;\r\n      } else { //if update:own\r\n        if (state.permissions.operation['update:own']) {\r\n          if (props.operation && props.operation.user && props.operation.user._id === state.user._id) {\r\n            return true;\r\n          }\r\n        }\r\n      }\r\n    }\r\n    return false;\r\n  },\r\n  wrapperDisplayName: 'CanEditOperationForm',\r\n  FailureComponent: () => <R403/>\r\n});\r\n\r\nexport const canViewDiscussionUpdate = connectedAuthWrapper({\r\n  authenticatedSelector: (state) => {\r\n    if (state.permissions && state.permissions.update && state.permissions.update['read:any']) {\r\n      return true;\r\n    }\r\n    return false;\r\n  },\r\n  wrapperDisplayName: 'CanViewDiscussionUpdate',\r\n});\r\n\r\nexport default canAddOperation;", "//Import Library\r\nimport { connect } from \"react-redux\";\r\nimport {faBookmark, faCheckCircle, faPlusCircle} from \"@fortawesome/free-solid-svg-icons\";\r\nimport {useEffect, useState} from \"react\";\r\nimport {FontAwesomeIcon} from \"@fortawesome/react-fontawesome\";\r\n\r\n//Import services/components\r\nimport apiService from \"../../services/apiService\";\r\n\r\nconst onModelOptions = {\r\n  operation: \"Operation\",\r\n  institution: \"Institution\",\r\n  event: \"Event\",\r\n  project: \"Project\",\r\n  vspace: \"Vspace\"\r\n} as const;\r\n\r\ninterface BookMarkProps {\r\n  user?: {\r\n    _id: string;\r\n  };\r\n  entityId: string;\r\n  entityType: keyof typeof onModelOptions;\r\n}\r\n\r\nconst BookMark: React.FC<BookMarkProps> = (props) => {\r\n  const { user, entityId, entityType } = props;\r\n  const [bookmark, setBookmark] = useState<boolean>(false);\r\n  const [subscribe, setSubscribe] = useState<any>(\"\");\r\n\r\n  //Handle bookmark feature\r\n  const fetchIfContentSubscribed = async () => {\r\n    if (!user?._id) return;\r\n    const checkFlag = await apiService.get('/flag', {query: {entity_id: entityId, user: user._id, onModel: onModelOptions[entityType]}});\r\n    if (checkFlag && checkFlag.data && checkFlag.data.length > 0) {\r\n      setSubscribe(checkFlag.data[0]);\r\n      setBookmark(true);\r\n    }\r\n  };\r\n\r\n  const bookmarkHandler = async (e: React.MouseEvent<HTMLAnchorElement>) => {\r\n    e.preventDefault();\r\n    if (!user?._id) return;\r\n    const flag  = !bookmark;\r\n    const flagPayload = {\r\n      entity_type: entityType,\r\n      entity_id: entityId,\r\n      user: user._id,\r\n      onModel: onModelOptions[entityType]\r\n    }\r\n    if (flag) {\r\n      const flagIt: any = await apiService.post('/flag', flagPayload);\r\n      if (flagIt && flagIt._id) {\r\n        setSubscribe(flagIt);\r\n        setBookmark(flag);\r\n      }\r\n    } else {\r\n      const unFlagIt = await apiService.remove(`/flag/${subscribe._id}`);\r\n      if (unFlagIt && unFlagIt.n) {\r\n        setBookmark(flag);\r\n      }\r\n    }\r\n  }\r\n  //END Bookmark handler\r\n  useEffect(() => {\r\n    fetchIfContentSubscribed();\r\n  },[]);\r\n  return (\r\n    <div className=\"subscribe-flag\">\r\n      <a href=\"\" onClick={bookmarkHandler}>\r\n        <span className=\"check\">\r\n          {bookmark ?\r\n            <FontAwesomeIcon className=\"clickable checkIcon\" icon={faCheckCircle} color=\"#00CC00\" />\r\n            :\r\n            <FontAwesomeIcon className=\"clickable minusIcon\" icon={faPlusCircle} color=\"#fff\"/>\r\n          }\r\n        </span>\r\n        <FontAwesomeIcon className=\"bookmark\" icon={faBookmark} color=\"#d4d4d4\"/>\r\n      </a>\r\n    </div>\r\n  )\r\n}\r\n\r\nexport default connect((state: any) => state)(BookMark);\r\n\r\n", "\n    (window.__NEXT_P = window.__NEXT_P || []).push([\n      \"/operation/components/OperationInfo\",\n      function () {\n        return require(\"private-next-pages/operation/components/OperationInfo.tsx\");\n      }\n    ]);\n    if(module.hot) {\n      module.hot.dispose(function () {\n        window.__NEXT_P.push([\"/operation/components/OperationInfo\"])\n      });\n    }\n  ", "//Import Library\r\nimport React from \"react\";\r\nimport { But<PERSON> } from \"react-bootstrap\";\r\nimport { FontAwesomeIcon } from \"@fortawesome/react-fontawesome\";\r\nimport Link from \"next/link\";\r\nimport { faPen } from \"@fortawesome/free-solid-svg-icons\";\r\n\r\n//Import services/components\r\nimport Bookmark from \"../../../components/common/Bookmark\";\r\nimport { canEditOperation } from \"../permission\";\r\nimport { useTranslation } from 'next-i18next';\r\n\r\n\r\nconst OperationInfo = (prop: any) => {\r\n    const { t } = useTranslation('common');\r\n    const EditOperationLink = () => {\r\n        return (\r\n            <>\r\n                {prop.editData ? \r\n                    <Link\r\n                        href=\"/operation/[...routes]\"\r\n                        as={`/operation/edit/${prop.routeData.routes[1]}`}\r\n                        >\r\n                        <Button variant=\"secondary\" size=\"sm\">\r\n                            <FontAwesomeIcon icon={faPen} />\r\n                            &nbsp;{t(\"Edit\")}\r\n                        </Button>\r\n                    </Link> \r\n                : \"\"}\r\n            </>\r\n        );\r\n    };\r\n\r\n    const CanEditOperation = canEditOperation(() => <EditOperationLink />);\r\n\r\n    return (\r\n        <>\r\n            <section className=\"d-flex justify-content-between\">\r\n                <h4 className=\"operationTitle\">\r\n                    {prop.operation.title}&nbsp;&nbsp;\r\n                    {prop.routeData.routes && prop.routeData.routes[1] ? (\r\n                        <CanEditOperation operation={prop.operation} />\r\n                    ) : null}\r\n                </h4>\r\n                <Bookmark entityId={prop.routeData.routes[1]} entityType=\"operation\" />\r\n            </section>\r\n        </>\r\n    )\r\n}\r\n\r\nexport default OperationInfo;"], "names": ["canAddOperation", "connectedAuthWrapper", "authenticatedSelector", "state", "permissions", "operation", "wrapperDisplayName", "FailureComponent", "R403", "props", "user", "_id", "update", "onModelOptions", "institution", "event", "project", "vspace", "connect", "entityId", "entityType", "bookmark", "setBookmark", "useState", "subscribe", "setSubscribe", "fetchIfContentSubscribed", "checkFlag", "apiService", "get", "query", "entity_id", "onModel", "data", "length", "bookmarkHandler", "e", "preventDefault", "flag", "flagPayload", "entity_type", "flagIt", "post", "unFlagIt", "remove", "n", "useEffect", "div", "className", "a", "href", "onClick", "span", "FontAwesomeIcon", "icon", "faCheckCircle", "color", "faPlusCircle", "faBookmark", "t", "useTranslation", "OperationInfo", "EditOperationLink", "prop", "editData", "Link", "as", "routeData", "routes", "<PERSON><PERSON>", "variant", "size", "faPen", "CanEditOperation", "canEditOperation", "section", "h4", "title", "Bookmark"], "sourceRoot": "", "ignoreList": []}