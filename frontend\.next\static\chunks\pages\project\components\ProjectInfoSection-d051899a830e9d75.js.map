{"version": 3, "file": "static/chunks/pages/project/components/ProjectInfoSection-d051899a830e9d75.js", "mappings": "8EACA,4CACA,yCACA,WACA,OAAe,EAAQ,KAA8D,CACrF,EACA,SAFsB,wICQtB,IAAMA,EAA0B,IAC9B,IAAIC,EAA2B,EAAE,CAYjC,OAAOA,MAXPC,GAAAA,EAAsBC,OAAO,CAAEC,QAC3BA,SAAAA,EAAAA,CADJF,CACgBG,mBAAAA,GAAZD,EAAiCD,OAAO,CAAC,CAAzCC,GACIH,EAAkBK,IAAI,CAAC,CACnBC,IAAKC,EAAKD,GAAG,CACbE,MAAOD,EAAKC,KAAK,EAEzB,EACJ,GACAR,EAAoBA,EAAkBS,MAAM,CACxC,CAACC,EAAOC,EAAOC,IAASA,EAAKC,SAAS,CAAC,GAAOC,EAAER,GAAG,GAAKI,EAAMJ,GAAG,IAAMK,EAG7E,EAiCA,EA/B2B,IACzB,GAAM,GAAEI,CAAC,CAAE,CAAGC,CAAAA,EAAAA,EAAAA,EAAAA,CAAcA,CAAC,MA8BhBC,IA7BT,aAAEC,CA6ByBD,EAAC,oBA7BbhB,CAAoB,CAAE,CAAGkB,EAAMC,OAAO,CACnDpB,EAAoBD,EAAwBE,GAElD,MACE,+BACE,WAACoB,EAAAA,CAAGA,CAAAA,CAACC,UAAU,6BACb,UAACC,EAAAA,CAAGA,CAAAA,CAACD,UAAU,mBAAmBE,GAAI,WACpC,UAACC,EAAAA,CAAiBA,CAAAA,CAACP,YAAaA,MAElC,WAACK,EAAAA,CAAGA,CAAAA,CAACC,GAAI,EAAGF,UAAU,wBACpB,UAACI,EAAAA,CAAOA,CAAAA,CACNC,OAAQZ,EAAE,sBACVa,KAAMC,SAkBTA,CAA4C,CAAEC,CAAqB,EAC1E,GAAM,cAAEC,CAAY,WAAEC,CAAS,CAAE,CAAGF,EACpC,MACE,WAACG,MAAAA,WACC,WAACA,MAAAA,CAAIX,UAAU,4BACb,WAACY,KAAAA,WAAInB,EAAE,cAAc,QACrB,UAACoB,IAAAA,gBAAGJ,EAAAA,KAAAA,EAAAA,EAAcK,GAAG,CAAE7B,GAASA,EAAKC,KAAjCuB,EAAwCM,IAAI,CAAC,WAEnD,WAACJ,MAAAA,CAAIX,UAAU,4BACb,WAACY,KAAAA,WAAInB,EAAE,YAAY,QACnB,UAACoB,IAAAA,UAAGH,SAIZ,EAhCoCjB,EAAGI,EAAMC,OAAO,IAE1C,UAACM,EAAAA,CAAOA,CAAAA,CACNC,OAAQZ,EAAE,uBACVa,KAAMU,SAwCTA,CAAyC,EAChD,MACE,UAACC,KAAAA,CAAGjB,UAAU,gCACXrB,EAAAA,KAAAA,EAAAA,EAAsBmC,GAAG,CAAC,CAACjC,EAAaQ,IAChC,UAAC6B,CADTvC,IACSuC,UAAgBrC,OAAAA,EAAAA,KAAAA,EAAAA,EAAaK,KAAAA,GAAS,IAA9BG,CAAQR,IAIhC,EAhDgCH,KAGtB,UAAC0B,EAAAA,CAAOA,CAAAA,CACNC,OAAQZ,EAAE,6BACVa,KAAMa,SAyBTA,CAAwC,EAC/C,MACE,UAACF,KAAAA,CAAGjB,UAAU,gCACXrB,EAAAA,KAAAA,EAAAA,EAAsBmC,GAAG,CAAC,CAACjC,EAAaQ,SACfR,EAAxB,IADDF,EACQ,UAACuC,KAAAA,UAAgBrC,OAAAA,GAAAA,OAAAA,EAAAA,EAAauC,UAAbvC,KAAauC,EAAbvC,KAAAA,EAAAA,EAA8BK,GAA9BL,EAAmC,GAAI,IAA/CQ,EAClB,IAGN,EAjC+BV,YAM/B,0GCnDA,IAAM0C,EAAwBC,EAAAA,OAAb,GAA6B,CAAC,GAK5CC,IALyB,IAAoB,WAC9CvB,CAAS,UACTwB,CAAQ,CACRC,GAAIC,EAAY,KAAK,CACrB,GAAG7B,EACJ,GAEC,OADA2B,EAAWG,CAAAA,EAAAA,EAAAA,EAAAA,CAAkBA,CAACH,EAAU,aACpBI,CAAAA,EAAAA,EAAAA,GAAAA,CAAIA,CAACF,EAAW,CAClCH,IAAKA,EACLvB,UAAW6B,IAAW7B,EAAWwB,GACjC,GAAG3B,CAAK,EAEZ,GACAwB,EAASS,WAAW,CAAG,WCbvB,IAAMC,EAA0BT,EAAAA,SAAb,CAA6B,CAAC,GAK9CC,MAL2B,EAAoB,WAChDvB,CAAS,UACTwB,CAAQ,CACRC,GAAIC,EAAY,KAAK,CACrB,GAAG7B,EACJ,GAEC,OAAO,EADI8B,CAAAA,EAAAA,EAAAA,EAAAA,CAAkBA,CAACH,EAAU,eACpBI,CAAAA,EAAAA,EAAAA,GAAAA,CAAIA,CAACF,EAAW,CAClCH,IAAKA,EACLvB,UAAW6B,IAAW7B,EAAWwB,GACjC,GAAG3B,CAAK,EAEZ,GACAkC,EAAWD,WAAW,CAAG,4BCXzB,IAAME,EAA0BV,EAAAA,SAAb,CAA6B,CAAC,GAM9CC,MAN2B,EAAoB,UAChDC,CAAQ,WACRxB,CAAS,CAETyB,CADA,EACIC,EAAY,KAAK,CACrB,GAAG7B,EACJ,GACOoC,EAASN,CAAAA,EAAAA,EAAAA,EAAAA,CAAkBA,CAACH,EAAU,eACtCU,EAAeC,CAAAA,EAAAA,EAAAA,OAAAA,CAAOA,CAAC,IAAO,EAClCC,mBAAoBH,EACtB,EAAI,CAACA,EAAO,EACZ,MAAoBL,CAAb,EAAaA,EAAAA,GAAAA,CAAIA,CAACS,EAAAA,CAAiBA,CAACC,QAAQ,CAAE,CACnDlD,MAAO8C,EACPK,SAAuBX,CAAb,EAAaA,EAAAA,GAAAA,CAAIA,CAACF,EAAP,CACnBH,IAAKA,EACL,GAAG1B,CAAK,CACRG,UAAW6B,IAAW7B,EAAWiC,EACnC,EACF,EACF,GACAD,EAAWF,GAJgBD,QAIL,CAAG,aCtBzB,IAAMW,EAAuBlB,EAAAA,MAAb,IAA6B,CAC7C,CACA,EAMGC,GARwB,KAE1B,UACCC,CAAQ,WACRxB,CAAS,SACTyC,CAAO,CACPhB,GAAIC,EAAY,KAAK,CACrB,GAAG7B,EACJ,GACOoC,EAASN,CAAAA,EAAAA,EAAAA,EAAAA,CAAkBA,CAACH,EAAU,YAC5C,MAAoBI,CAAAA,EAAAA,EAAAA,GAAAA,CAAIA,CAACF,EAAW,CAClCH,IAAKA,EACLvB,UAAW6B,IAAWY,EAAU,GAAaA,MAAAA,CAAVR,EAAO,EAArBJ,GAAgC,OAARY,CAX0G,EAW9FR,EAAQjC,GACjE,GAAGH,CAAK,EAEZ,EACA2C,GAAQV,WAAW,CAAG,UChBtB,IAAMY,EAA8BpB,EAAAA,UAAgB,CAAC,EAA9B,CAKpBC,QALmD,EAApB,SAChCvB,CAAS,UACTwB,CAAQ,CACRC,GAAIC,EAAY,KAAK,CACrB,GAAG7B,EACJ,GAEC,OADA2B,EAAWG,CAAAA,EAAAA,EAAAA,EAAAA,CAAkBA,CAACH,EAAU,oBACpBI,CAAAA,EAAAA,EAAAA,GAAAA,CAAIA,CAACF,EAAW,CAClCH,IAAKA,EACLvB,UAAW6B,IAAW7B,EAAWwB,GACjC,GAAG3B,CAAK,EAEZ,GACA6C,EAJyBb,WAIC,CAAG,iBCb7B,IAAMc,EAAwBrB,EAAAA,OAAb,GAA6B,CAAC,GAK5CC,IALyB,IAAoB,CAC9CvB,WAAS,CACTwB,UAAQ,CACRC,GAAIC,EAAY,GAAG,CACnB,GAAG7B,EACJ,GAEC,OADA2B,EAAWG,CAAAA,EAAAA,EAAAA,EAAAA,CAAkBA,CAACH,EAAU,aACpBI,CAAAA,EAAAA,EAAAA,GAAAA,CAAIA,CAACF,EAAW,CAClCH,IAAKA,EACLvB,UAAW6B,IAAW7B,EAAWwB,GACjC,GAAG3B,CAAK,EAEZ,GACA8C,EAJyBd,WAIL,CAAG,0BCZvB,IAAMe,EAAgBC,CAAAA,EAAAA,EAAAA,CAAAA,CAAgBA,CAAC,MACjCC,EAA4BxB,EAAAA,UAAgB,CAA7B,CAA8B,EAKhDC,QAL6B,WAC9BvB,CAAS,UACTwB,CAAQ,CACRC,GAAIC,EAAYkB,CAAa,CAC7B,GAAG/C,EACJ,GAEC,OADA2B,EAAWG,CAAAA,EAAAA,EAAAA,EAAAA,CAAkBA,CAACH,EAAU,iBACpBI,CAAAA,EAAAA,EAAAA,GAAAA,CAAIA,CAACF,EAAW,CAClCH,IAAKA,EACLvB,UAAW6B,IAAW7B,EAAWwB,GACjC,GAAG3B,CAAK,EAEZ,GACAiD,EAAahB,WAAW,CAAG,eCf3B,IAAMiB,EAAwBzB,EAAAA,OAAb,GAA6B,CAAC,GAK5CC,IALyB,IAAoB,CAC9CvB,WAAS,CACTwB,UAAQ,CACRC,GAAIC,EAAY,GAAG,CACnB,GAAG7B,EACJ,GAEC,OADA2B,EAAWG,CAAAA,EAAAA,EAAAA,EAAAA,CAAkBA,CAACH,EAAU,aACpBI,CAAAA,EAAAA,EAAAA,GAAAA,CAAIA,CAACF,EAAW,CAClCH,IAAKA,EACLvB,UAAW6B,IAAW7B,EAAWwB,GACjC,GAAG3B,CACL,EACF,GACAkD,EAJyBlB,WAIL,CAAG,WCZvB,IAAMmB,EAAgBH,CAAAA,EAAAA,EAAAA,CAAAA,CAAgBA,CAAC,MACjCI,EAAyB3B,EAAAA,QAAb,EAA6B,CAAC,GAK7CC,KAL0B,GAAoB,WAC/CvB,CAAS,UACTwB,CAAQ,CACRC,GAAIC,EAAYsB,CAAa,CAC7B,GAAGnD,EACJ,GAEC,OADA2B,EAAWG,CAAAA,EAAAA,EAAAA,EAAAA,CAAkBA,CAACH,EAAU,cACpBI,CAAAA,EAAAA,EAAAA,GAAAA,CAAIA,CAACF,EAAW,CAClCH,IAAKA,EACLvB,UAAW6B,IAAW7B,EAAWwB,GACjC,GAAG3B,CAAK,EAEZ,GACAoD,EAAUnB,WAAW,CAAG,YCNxB,IAAMoB,EAAoB5B,EAAAA,GAAb,OAA6B,CAAC,GAAnB,QAAoB,UAC1CE,CAAQ,WACRxB,CAAS,IACTmD,CAAE,MACFC,CAAI,QACJC,CAAM,MACN/C,GAAO,CAAK,UACZiC,CAAQ,CAERd,CADA,EACIC,EAAY,KAAK,CACrB,GAAG7B,EACJ,GACOoC,EAASN,CAAAA,EAAAA,EAAAA,EAAAA,CAAkBA,CAACH,EAAU,QAC5C,MAAoBI,CAAb,EAAaA,EAAAA,GAAAA,CAAIA,CAACF,EAAP,CAChBH,IAAKA,EACL,GAAG1B,CAAK,CACRG,UAAW6B,IAAW7B,EAAWiC,EAAQkB,GAAM,MAAS,GAAnCtB,GAAmC,CAAHsB,GAAMC,GAAQ,QAAa,OAALA,GAAQC,GAAU,UAAiB,OAAPA,IACvGd,IATyJ,KAS/IjC,EAAoBsB,CAAAA,EAAAA,EAAb,GAAaA,CAAIA,CAACP,EAAU,CAC3CkB,GAD0B,MAAelB,CAE3C,GAAKkB,CACP,EACF,GACAW,EAAKpB,WAAW,CAAG,OACnB,MAAewB,OAAOC,MAAM,CAACL,EAAM,CACjCM,INhBahB,CMgBRA,CACLiB,KNjBoBjB,CKDPS,CLCQ,CMkBrBS,EAFYlB,KDjBUS,EAAC,CCmBbH,CACVa,CAFgBV,ITpBH5B,CSsBPA,CACNuC,GHrByBd,EDFZH,CLAQtB,CSwBrBwC,CTxBsB,GSsBRxC,CFtBD0B,CEwBPA,CACNe,CJzBsB,GIuBRnB,EFvBOI,CLSRf,COgBLA,CACR+B,EAFchB,KRxBDhB,CCSUC,COkBvBgC,CPlBwB,GOgBNhC,IRzBKD,EAAC,CGAXW,CK2BDA,CADMX,CAElB,EAAC,SL5B0BW,EAAC,GK2BFA,qECP5B,MA9B0B,IACxB,GAAM,CAAEjD,GAAC,CAAE,CAAGC,CAAAA,EAAAA,EAAAA,EAAAA,CAAcA,CAAC,MA6BhBS,IA5BP8D,EAAiBC,SAASC,EA4BFhE,EAAC,CA5B6B,EACtD,CAACiE,CADyD,CAC7CC,EAAc,CAAGC,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,EAAC,GAO7C,MACE,iCAEIzE,EAAMD,WAAW,CACjB,UAACe,MAAAA,CACC4D,wBAAyBC,CAVZ,CAACC,EAAqBC,KAElC,CAAEC,OADe,CAAED,GAAqBD,EAAYG,MAAM,CAAGX,EAAkBQ,EAAYI,SAAS,CAAC,EAAGZ,GAAkB,MAAQpE,EAAMD,WAAW,CACzH,CACnC,EAO8CC,EAAMD,WAAW,CAACwE,GACxDpE,UAAU,kBAEH,KAGTH,EAAMD,WAAW,EAAIC,EAAMD,WAAW,CAACgF,MAAM,CAAGX,EAC9C,UAACa,SAAAA,CAAOC,KAAK,SAAS/E,UAAU,eAAegF,QAAS,IAAMX,EAAc,CAACD,YAChE3E,EAAb2E,EAAe,WAAgB,GAAF3E,WACjB,OAItB,iDCnCA,IAAMwF,EAAuB3D,QAAb,CAAaA,UAAF,GAAqB,CAAC,MACjD2D,EAAQnD,WAAW,CAAG,oBACtB,MAAemD,OAAOA,EAAC,wFCUvB,SAASC,EAAUrF,CAAqB,EACtC,GAAM,MAAEsF,CAAI,iBAAEC,CAAe,CAAE,CAAGvF,EAClC,MACE,WAACwF,EAAAA,CAAKA,CAAAA,CACH,GAAGxF,CAAK,CACTuF,gBAAiBA,EACjBE,kBAAgB,gCAChBC,QAAQ,cAER,UAACF,EAAAA,CAAKA,CAACvB,MAAM,EAAC0B,WAAW,aACvB,UAACH,EAAAA,CAAKA,CAAC5B,KAAK,EAACgC,GAAG,yCACbN,EAAKO,OAAO,KAGjB,UAACL,EAAAA,CAAKA,CAAC1B,IAAI,WACRwB,EAAK7E,IAAI,KAIlB,CAUA,SAASyB,EAAWlC,CAAsB,EACxC,GAAM,MAAEsF,CAAI,CAAE,CAAGtF,EACX,CAAC8F,EAAWC,EAAa,CAAGtE,EAAAA,QAAc,EAAC,UACjD,GAAY6D,EAAK7E,IAAI,CAEjB,iCACE,UAACwE,SAAAA,CAAOC,KAAK,SAASC,QAAS,IAAMY,GAAa,GAAOC,MAAO,CAAExC,OAAQ,OAAQyC,WAAY,OAAQC,QAAS,CAAE,WAC/G,UAAC7C,EAAAA,CAAIA,CAACa,MAAM,WACV,UAACiC,IAAAA,CAAEhG,UAAU,4BAGhBH,EAAMsF,IAAI,EAAI,UAACD,EAAAA,CAAUC,KAAMtF,EAAMsF,IAAI,CAAEc,KAAMN,EAAWO,OAAQ,IAAMN,GAAa,GAAQR,gBAAiBvF,EAAMuF,eAAe,MAIrI,IACT,CA4BA,MAhBA,SAAShF,CAA2B,EAClC,GAAM,QAAEC,CAAM,CAAEC,GAeHF,GAfO,CAAE,CAAGP,EAEzB,EAaqB,IAZnB,WAACqD,EAAAA,CAAIA,CAAAA,CAAClD,UAAU,iCACd,UAACkD,EAAAA,CAAIA,CAACY,MAAM,WAAEzD,IACd,UAAC6C,EAAAA,CAAIA,CAACS,IAAI,WACR,UAACT,EAAAA,CAAIA,CAACW,IAAI,WACPvD,MAGL,UAACyB,EAAAA,CAAY,GAAGlC,CAAK,KAG3B", "sources": ["webpack://_N_E/?899f", "webpack://_N_E/./pages/project/components/ProjectInfoSection.tsx", "webpack://_N_E/./node_modules/react-bootstrap/esm/CardBody.js", "webpack://_N_E/./node_modules/react-bootstrap/esm/CardFooter.js", "webpack://_N_E/./node_modules/react-bootstrap/esm/CardHeader.js", "webpack://_N_E/./node_modules/react-bootstrap/esm/CardImg.js", "webpack://_N_E/./node_modules/react-bootstrap/esm/CardImgOverlay.js", "webpack://_N_E/./node_modules/react-bootstrap/esm/CardLink.js", "webpack://_N_E/./node_modules/react-bootstrap/esm/CardSubtitle.js", "webpack://_N_E/./node_modules/react-bootstrap/esm/CardText.js", "webpack://_N_E/./node_modules/react-bootstrap/esm/CardTitle.js", "webpack://_N_E/./node_modules/react-bootstrap/esm/Card.js", "webpack://_N_E/./components/common/readMore/readMore.tsx", "webpack://_N_E/./node_modules/react-bootstrap/esm/CardHeaderContext.js", "webpack://_N_E/./components/common/RKICard.tsx"], "sourcesContent": ["\n    (window.__NEXT_P = window.__NEXT_P || []).push([\n      \"/project/components/ProjectInfoSection\",\n      function () {\n        return require(\"private-next-pages/project/components/ProjectInfoSection.tsx\");\n      }\n    ]);\n    if(module.hot) {\n      module.hot.dispose(function () {\n        window.__NEXT_P.push([\"/project/components/ProjectInfoSection\"])\n      });\n    }\n  ", "// React Imports\r\nimport React from \"react\";\r\nimport { Col, Row } from \"react-bootstrap\";\r\nimport { useTranslation } from 'next-i18next';\r\n\r\n// Components Imports\r\nimport RKICard from \"../../../components/common/RKICard\";\r\nimport ReadMoreContainer from \"../../../components/common/readMore/readMore\";\r\n\r\n// Interfaces Imports\r\nimport { IProject } from \"../../../shared/interfaces/project.interface\";\r\n\r\nconst filterPartnerInstitutes = (partner_institutions: any[]) => {\r\n  let partnerInstitutes: any[] = [];\r\n  partner_institutions?.forEach((institution) => {\r\n      institution.partner_institution?.forEach((item: any) => {\r\n          partnerInstitutes.push({\r\n              _id: item._id,\r\n              title: item.title,\r\n          });\r\n      });\r\n  });\r\n  partnerInstitutes = partnerInstitutes.filter(\r\n      (value, index, self) => self.findIndex((m) => m._id === value._id) === index\r\n  );\r\n  return partnerInstitutes;\r\n};\r\n\r\nconst ProjectInfoSection = (props: { project: IProject }) => {\r\n  const { t } = useTranslation('common');\r\n  let { description, partner_institutions } = props.project;\r\n  const partnerInstitutes = filterPartnerInstitutes(partner_institutions);\r\n\r\n  return (\r\n    <>\r\n      <Row className=\"projectInfoBlock\">\r\n        <Col className=\"projectDescBlock\" md={8}>\r\n          <ReadMoreContainer description={description} />\r\n        </Col>\r\n        <Col md={4} className=\"projectInfo\">\r\n          <RKICard\r\n            header={t(\"ProjectInformation\")}\r\n            body={Project_info_func(t, props.project)}\r\n          />\r\n          <RKICard\r\n            header={t(\"PartnerOrganisation\")}\r\n            body={paratner_func(partnerInstitutes)}\r\n          />\r\n\r\n          <RKICard\r\n            header={t(\"CountriesCoveredbyProject\")}\r\n            body={project_func(partner_institutions)}\r\n          />\r\n        </Col>\r\n      </Row>\r\n    </>\r\n  );\r\n};\r\n\r\nexport default ProjectInfoSection;\r\nfunction Project_info_func(t: (key: string) => string, projectData: IProject) {\r\n  const { area_of_work, funded_by } = projectData;\r\n  return (\r\n    <div>\r\n      <div className=\"projetInfoItems\">\r\n        <h6>{t(\"AreaofWork\")}: </h6>\r\n        <p>{area_of_work?.map((item) => item.title).join(\", \")}</p>\r\n      </div>\r\n      <div className=\"projetInfoItems\">\r\n        <h6>{t(\"FundedBy\")}: </h6>\r\n        <p>{funded_by}</p>\r\n      </div>\r\n    </div>\r\n  );\r\n}\r\n\r\nfunction project_func(partner_institutions: any[]) {\r\n  return (\r\n    <ul className=\"projectPartner\">\r\n      {partner_institutions?.map((institution, index) => {\r\n        return <li key={index}>{institution?.partner_country?.title || \"\"}</li>;\r\n      })}\r\n    </ul>\r\n  );\r\n}\r\n\r\nfunction paratner_func(partner_institutions: any[]) {\r\n  return (\r\n    <ul className=\"projectPartner\">\r\n      {partner_institutions?.map((institution, index) => {\r\n        return <li key={index}>{institution?.title || \"\"}</li>;\r\n      })}\r\n    </ul>\r\n  );\r\n}\r\n", "\"use client\";\n\nimport * as React from 'react';\nimport classNames from 'classnames';\nimport { useBootstrapPrefix } from './ThemeProvider';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst CardBody = /*#__PURE__*/React.forwardRef(({\n  className,\n  bsPrefix,\n  as: Component = 'div',\n  ...props\n}, ref) => {\n  bsPrefix = useBootstrapPrefix(bsPrefix, 'card-body');\n  return /*#__PURE__*/_jsx(Component, {\n    ref: ref,\n    className: classNames(className, bsPrefix),\n    ...props\n  });\n});\nCardBody.displayName = 'CardBody';\nexport default CardBody;", "\"use client\";\n\nimport * as React from 'react';\nimport classNames from 'classnames';\nimport { useBootstrapPrefix } from './ThemeProvider';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst CardFooter = /*#__PURE__*/React.forwardRef(({\n  className,\n  bsPrefix,\n  as: Component = 'div',\n  ...props\n}, ref) => {\n  bsPrefix = useBootstrapPrefix(bsPrefix, 'card-footer');\n  return /*#__PURE__*/_jsx(Component, {\n    ref: ref,\n    className: classNames(className, bsPrefix),\n    ...props\n  });\n});\nCardFooter.displayName = 'CardFooter';\nexport default CardFooter;", "\"use client\";\n\nimport classNames from 'classnames';\nimport * as React from 'react';\nimport { useMemo } from 'react';\nimport { useBootstrapPrefix } from './ThemeProvider';\nimport CardHeaderContext from './CardHeaderContext';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst CardHeader = /*#__PURE__*/React.forwardRef(({\n  bsPrefix,\n  className,\n  // Need to define the default \"as\" during prop destructuring to be compatible with styled-components github.com/react-bootstrap/react-bootstrap/issues/3595\n  as: Component = 'div',\n  ...props\n}, ref) => {\n  const prefix = useBootstrapPrefix(bsPrefix, 'card-header');\n  const contextValue = useMemo(() => ({\n    cardHeaderBsPrefix: prefix\n  }), [prefix]);\n  return /*#__PURE__*/_jsx(CardHeaderContext.Provider, {\n    value: contextValue,\n    children: /*#__PURE__*/_jsx(Component, {\n      ref: ref,\n      ...props,\n      className: classNames(className, prefix)\n    })\n  });\n});\nCardHeader.displayName = 'CardHeader';\nexport default CardHeader;", "\"use client\";\n\nimport classNames from 'classnames';\nimport * as React from 'react';\nimport { useBootstrapPrefix } from './ThemeProvider';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst CardImg = /*#__PURE__*/React.forwardRef(\n// Need to define the default \"as\" during prop destructuring to be compatible with styled-components github.com/react-bootstrap/react-bootstrap/issues/3595\n({\n  bsPrefix,\n  className,\n  variant,\n  as: Component = 'img',\n  ...props\n}, ref) => {\n  const prefix = useBootstrapPrefix(bsPrefix, 'card-img');\n  return /*#__PURE__*/_jsx(Component, {\n    ref: ref,\n    className: classNames(variant ? `${prefix}-${variant}` : prefix, className),\n    ...props\n  });\n});\nCardImg.displayName = 'CardImg';\nexport default CardImg;", "\"use client\";\n\nimport * as React from 'react';\nimport classNames from 'classnames';\nimport { useBootstrapPrefix } from './ThemeProvider';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst CardImgOverlay = /*#__PURE__*/React.forwardRef(({\n  className,\n  bsPrefix,\n  as: Component = 'div',\n  ...props\n}, ref) => {\n  bsPrefix = useBootstrapPrefix(bsPrefix, 'card-img-overlay');\n  return /*#__PURE__*/_jsx(Component, {\n    ref: ref,\n    className: classNames(className, bsPrefix),\n    ...props\n  });\n});\nCardImgOverlay.displayName = 'CardImgOverlay';\nexport default CardImgOverlay;", "\"use client\";\n\nimport * as React from 'react';\nimport classNames from 'classnames';\nimport { useBootstrapPrefix } from './ThemeProvider';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst CardLink = /*#__PURE__*/React.forwardRef(({\n  className,\n  bsPrefix,\n  as: Component = 'a',\n  ...props\n}, ref) => {\n  bsPrefix = useBootstrapPrefix(bsPrefix, 'card-link');\n  return /*#__PURE__*/_jsx(Component, {\n    ref: ref,\n    className: classNames(className, bsPrefix),\n    ...props\n  });\n});\nCardLink.displayName = 'CardLink';\nexport default CardLink;", "\"use client\";\n\nimport * as React from 'react';\nimport classNames from 'classnames';\nimport { useBootstrapPrefix } from './ThemeProvider';\nimport divWithClassName from './divWithClassName';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst DivStyledAsH6 = divWithClassName('h6');\nconst CardSubtitle = /*#__PURE__*/React.forwardRef(({\n  className,\n  bsPrefix,\n  as: Component = DivStyledAsH6,\n  ...props\n}, ref) => {\n  bsPrefix = useBootstrapPrefix(bsPrefix, 'card-subtitle');\n  return /*#__PURE__*/_jsx(Component, {\n    ref: ref,\n    className: classNames(className, bsPrefix),\n    ...props\n  });\n});\nCardSubtitle.displayName = 'CardSubtitle';\nexport default CardSubtitle;", "\"use client\";\n\nimport * as React from 'react';\nimport classNames from 'classnames';\nimport { useBootstrapPrefix } from './ThemeProvider';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst CardText = /*#__PURE__*/React.forwardRef(({\n  className,\n  bsPrefix,\n  as: Component = 'p',\n  ...props\n}, ref) => {\n  bsPrefix = useBootstrapPrefix(bsPrefix, 'card-text');\n  return /*#__PURE__*/_jsx(Component, {\n    ref: ref,\n    className: classNames(className, bsPrefix),\n    ...props\n  });\n});\nCardText.displayName = 'CardText';\nexport default CardText;", "\"use client\";\n\nimport * as React from 'react';\nimport classNames from 'classnames';\nimport { useBootstrapPrefix } from './ThemeProvider';\nimport divWithClassName from './divWithClassName';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst DivStyledAsH5 = divWithClassName('h5');\nconst CardTitle = /*#__PURE__*/React.forwardRef(({\n  className,\n  bsPrefix,\n  as: Component = DivStyledAsH5,\n  ...props\n}, ref) => {\n  bsPrefix = useBootstrapPrefix(bsPrefix, 'card-title');\n  return /*#__PURE__*/_jsx(Component, {\n    ref: ref,\n    className: classNames(className, bsPrefix),\n    ...props\n  });\n});\nCardTitle.displayName = 'CardTitle';\nexport default CardTitle;", "\"use client\";\n\nimport classNames from 'classnames';\nimport * as React from 'react';\nimport { useBootstrapPrefix } from './ThemeProvider';\nimport CardBody from './CardBody';\nimport CardFooter from './CardFooter';\nimport CardHeader from './CardHeader';\nimport CardImg from './CardImg';\nimport CardImgOverlay from './CardImgOverlay';\nimport CardLink from './CardLink';\nimport CardSubtitle from './CardSubtitle';\nimport CardText from './CardText';\nimport CardTitle from './CardTitle';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst Card = /*#__PURE__*/React.forwardRef(({\n  bsPrefix,\n  className,\n  bg,\n  text,\n  border,\n  body = false,\n  children,\n  // Need to define the default \"as\" during prop destructuring to be compatible with styled-components github.com/react-bootstrap/react-bootstrap/issues/3595\n  as: Component = 'div',\n  ...props\n}, ref) => {\n  const prefix = useBootstrapPrefix(bsPrefix, 'card');\n  return /*#__PURE__*/_jsx(Component, {\n    ref: ref,\n    ...props,\n    className: classNames(className, prefix, bg && `bg-${bg}`, text && `text-${text}`, border && `border-${border}`),\n    children: body ? /*#__PURE__*/_jsx(CardBody, {\n      children: children\n    }) : children\n  });\n});\nCard.displayName = 'Card';\nexport default Object.assign(Card, {\n  Img: CardImg,\n  Title: CardTitle,\n  Subtitle: CardSubtitle,\n  Body: CardBody,\n  Link: CardLink,\n  Text: CardText,\n  Header: CardHeader,\n  Footer: CardFooter,\n  ImgOverlay: CardImgOverlay\n});", "//Import Library\r\nimport { useState } from \"react\";\r\n\r\n//Import services/components\r\nimport { useTranslation } from 'next-i18next';\r\n\r\ninterface ReadMoreContainerProps {\r\n  description: string;\r\n}\r\n\r\nconst ReadMoreContainer = (props: ReadMoreContainerProps) => {\r\n  const { t } = useTranslation('common');\r\n  const readMoreLength = parseInt(process.env.READ_MORE_LENGTH || '200');\r\n  const [isReadMore, setIsReadMore] = useState(false);\r\n\r\n  const createMarkup = (htmlContent: string, isReadMoreInitial: boolean) => {\r\n    const truncateContent = (!isReadMoreInitial && htmlContent.length > readMoreLength) ? htmlContent.substring(0, readMoreLength) + \"...\" : props.description;\r\n    return { __html: truncateContent };\r\n  };\r\n\r\n  return (\r\n    <>\r\n      {\r\n        props.description  ?\r\n        <div\r\n          dangerouslySetInnerHTML={createMarkup(props.description,isReadMore)}\r\n          className=\"operationDesc\"\r\n        >\r\n        </div> : null\r\n      }\r\n      {\r\n        props.description && props.description.length > readMoreLength ?\r\n          <button type=\"button\" className=\"readMoreText\" onClick={() => setIsReadMore(!isReadMore)}>\r\n         {isReadMore ? t(\"readLess\") : t(\"readMore\")}\r\n          </button> : null\r\n      }\r\n    </>\r\n  )\r\n}\r\n\r\nexport default ReadMoreContainer;\r\n", "\"use client\";\n\nimport * as React from 'react';\nconst context = /*#__PURE__*/React.createContext(null);\ncontext.displayName = 'CardHeaderContext';\nexport default context;", "//Import Library\r\nimport React from \"react\";\r\nimport { Card } from \"react-bootstrap\";\r\nimport Modal from \"react-bootstrap/Modal\";\r\n\r\ninterface ListModalProps {\r\n  list: {\r\n    heading: string;\r\n    body: React.ReactNode;\r\n  };\r\n  dialogClassName?: string;\r\n  show: boolean;\r\n  onHide: () => void;\r\n}\r\n\r\nfunction ListModal(props: ListModalProps) {\r\n  const { list, dialogClassName } = props;\r\n  return (\r\n    <Modal\r\n      {...props}\r\n      dialogClassName={dialogClassName}\r\n      aria-labelledby=\"contained-modal-title-vcenter\"\r\n      centered\r\n    >\r\n      <Modal.Header closeButton>\r\n        <Modal.Title id=\"contained-modal-title-vcenter\">\r\n          {list.heading}\r\n        </Modal.Title>\r\n      </Modal.Header>\r\n      <Modal.Body>\r\n        {list.body}\r\n      </Modal.Body>\r\n    </Modal>\r\n  )\r\n}\r\n\r\ninterface CardFooterProps {\r\n  list?: {\r\n    body: React.ReactNode;\r\n    heading: string;\r\n  };\r\n  dialogClassName?: string;\r\n}\r\n\r\nfunction CardFooter(props: CardFooterProps) {\r\n  const { list } = props;\r\n  const [modalShow, setModalShow] = React.useState(false);\r\n  if (list && list.body) {\r\n    return (\r\n      <>\r\n        <button type=\"button\" onClick={() => setModalShow(true)} style={{ border: 'none', background: 'none', padding: 0 }}>\r\n          <Card.Footer>\r\n            <i className=\"fas fa-chevron-down\" />\r\n          </Card.Footer>\r\n        </button>\r\n        {props.list && <ListModal list={props.list} show={modalShow} onHide={() => setModalShow(false)} dialogClassName={props.dialogClassName} />}\r\n      </>\r\n    )\r\n  }\r\n  return null;\r\n}\r\n\r\ninterface RKICardProps {\r\n  header: string;\r\n  body: React.ReactNode;\r\n  list?: {\r\n    body: React.ReactNode;\r\n    heading: string;\r\n  };\r\n  dialogClassName?: string;\r\n}\r\n\r\nfunction RKICard(props: RKICardProps) {\r\n  const { header, body } = props\r\n\r\n  return (\r\n    <Card className=\"text-center infoCard\">\r\n      <Card.Header>{header}</Card.Header>\r\n      <Card.Body>\r\n        <Card.Text>\r\n          {body}\r\n        </Card.Text>\r\n      </Card.Body>\r\n      <CardFooter {...props} />\r\n    </Card>\r\n  )\r\n}\r\n\r\nexport default RKICard;\r\n"], "names": ["filterPartnerInstitutes", "partnerInstitutes", "partner_institutions", "for<PERSON>ach", "institution", "partner_institution", "push", "_id", "item", "title", "filter", "value", "index", "self", "findIndex", "m", "t", "useTranslation", "ProjectInfoSection", "description", "props", "project", "Row", "className", "Col", "md", "ReadMoreContainer", "RKICard", "header", "body", "Project_info_func", "projectData", "area_of_work", "funded_by", "div", "h6", "p", "map", "join", "paratner_func", "ul", "li", "project_func", "partner_country", "CardBody", "React", "ref", "bsPrefix", "as", "Component", "useBootstrapPrefix", "_jsx", "classNames", "displayName", "<PERSON><PERSON><PERSON>er", "<PERSON><PERSON><PERSON><PERSON>", "prefix", "contextValue", "useMemo", "cardHeaderBsPrefix", "CardHeaderContext", "Provider", "children", "CardImg", "variant", "CardImgOverlay", "CardLink", "DivStyledAsH6", "divWithClassName", "CardSubtitle", "CardText", "DivStyledAsH5", "CardTitle", "Card", "bg", "text", "border", "Object", "assign", "Img", "Title", "Subtitle", "Body", "Link", "Text", "Header", "Footer", "ImgOverlay", "readMoreLength", "parseInt", "process", "isReadMore", "setIsReadMore", "useState", "dangerouslySetInnerHTML", "createMarkup", "htmlContent", "isReadMoreInitial", "__html", "length", "substring", "button", "type", "onClick", "context", "ListModal", "list", "dialogClassName", "Modal", "aria-<PERSON>by", "centered", "closeButton", "id", "heading", "modalShow", "setModalShow", "style", "background", "padding", "i", "show", "onHide"], "sourceRoot": "", "ignoreList": [2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 13]}