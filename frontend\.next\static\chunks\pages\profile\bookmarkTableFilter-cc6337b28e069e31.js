(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[5580],{2464:(e,r,t)=>{(window.__NEXT_P=window.__NEXT_P||[]).push(["/profile/bookmarkTableFilter",function(){return t(22797)}])},22797:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>d});var s=t(37876);t(14232);var a=t(49589),n=t(56970),l=t(37784),o=t(29504),i=t(67814),c=t(31753);let d=e=>{let{filterText:r,onFilter:t,onClear:d,handleGroupHandler:u,groupType:p,options:m}=e,{t:h}=(0,c.Bd)("common");return(0,s.jsx)(a.A,{fluid:!0,className:"p-0",children:(0,s.jsx)(n.A,{children:(0,s.jsx)(l.A,{xs:4,md:4,lg:4,children:(0,s.jsx)(o.A.Group,{style:{maxWidth:"800px"},children:(0,s.jsx)(i.KF,{overrideStrings:{selectSomeItems:h("ChooseGroup"),allItemsAreSelected:"All Groups are Selected"},options:m,value:p,onChange:u,className:"choose-group",labelledBy:"Select Network"})})})})})}},67814:(e,r,t)=>{"use strict";t.d(r,{KF:()=>j});var s=t(14232),a=t(37876);!function(e,{insertAt:r}={}){if(!e||typeof document>"u")return;let t=document.head||document.getElementsByTagName("head")[0],s=document.createElement("style");s.type="text/css","top"===r&&t.firstChild?t.insertBefore(s,t.firstChild):t.appendChild(s),s.styleSheet?s.styleSheet.cssText=e:s.appendChild(document.createTextNode(e))}(`.rmsc{--rmsc-main: #4285f4;--rmsc-hover: #f1f3f5;--rmsc-selected: #e2e6ea;--rmsc-border: #ccc;--rmsc-gray: #aaa;--rmsc-bg: #fff;--rmsc-p: 10px;--rmsc-radius: 4px;--rmsc-h: 38px}.rmsc *{box-sizing:border-box;transition:all .2s ease}.rmsc .gray{color:var(--rmsc-gray)}.rmsc .dropdown-content{position:absolute;z-index:1;top:100%;width:100%;padding-top:8px}.rmsc .dropdown-content .panel-content{overflow:hidden;border-radius:var(--rmsc-radius);background:var(--rmsc-bg);box-shadow:0 0 0 1px #0000001a,0 4px 11px #0000001a}.rmsc .dropdown-container{position:relative;outline:0;background-color:var(--rmsc-bg);border:1px solid var(--rmsc-border);border-radius:var(--rmsc-radius)}.rmsc .dropdown-container[aria-disabled=true]:focus-within{box-shadow:var(--rmsc-gray) 0 0 0 1px;border-color:var(--rmsc-gray)}.rmsc .dropdown-container:focus-within{box-shadow:var(--rmsc-main) 0 0 0 1px;border-color:var(--rmsc-main)}.rmsc .dropdown-heading{position:relative;padding:0 var(--rmsc-p);display:flex;align-items:center;width:100%;height:var(--rmsc-h);cursor:default;outline:0}.rmsc .dropdown-heading .dropdown-heading-value{overflow:hidden;text-overflow:ellipsis;white-space:nowrap;flex:1}.rmsc .clear-selected-button{cursor:pointer;background:none;border:0;padding:0;display:flex}.rmsc .options{max-height:260px;overflow-y:auto;margin:0;padding-left:0}.rmsc .options li{list-style:none;margin:0}.rmsc .select-item{box-sizing:border-box;cursor:pointer;display:block;padding:var(--rmsc-p);outline-offset:-1px;outline-color:var(--rmsc-primary)}.rmsc .select-item:hover{background:var(--rmsc-hover)}.rmsc .select-item.selected{background:var(--rmsc-selected)}.rmsc .no-options{padding:var(--rmsc-p);text-align:center;color:var(--rmsc-gray)}.rmsc .search{width:100%;position:relative;border-bottom:1px solid var(--rmsc-border)}.rmsc .search input{background:none;height:var(--rmsc-h);padding:0 var(--rmsc-p);width:100%;outline:0;border:0;font-size:1em}.rmsc .search input:focus{background:var(--rmsc-hover)}.rmsc .search-clear-button{cursor:pointer;position:absolute;top:0;right:0;bottom:0;background:none;border:0;padding:0 calc(var(--rmsc-p) / 2)}.rmsc .search-clear-button [hidden]{display:none}.rmsc .item-renderer{display:flex;align-items:baseline}.rmsc .item-renderer input{margin:0 5px 0 0}.rmsc .item-renderer.disabled{opacity:.5}.rmsc .spinner{animation:rotate 2s linear infinite}.rmsc .spinner .path{stroke:var(--rmsc-border);stroke-width:4px;stroke-linecap:round;animation:dash 1.5s ease-in-out infinite}@keyframes rotate{to{transform:rotate(360deg)}}@keyframes dash{0%{stroke-dasharray:1,150;stroke-dashoffset:0}50%{stroke-dasharray:90,150;stroke-dashoffset:-35}to{stroke-dasharray:90,150;stroke-dashoffset:-124}}
`);var n={allItemsAreSelected:"All items are selected.",clearSearch:"Clear Search",clearSelected:"Clear Selected",noOptions:"No options",search:"Search",selectAll:"Select All",selectAllFiltered:"Select All (Filtered)",selectSomeItems:"Select...",create:"Create"},l={value:[],hasSelectAll:!0,className:"multi-select",debounceDuration:200,options:[]},o=s.createContext({}),i=({props:e,children:r})=>{let[t,i]=(0,s.useState)(e.options);return(0,s.useEffect)(()=>{i(e.options)},[e.options]),(0,a.jsx)(o.Provider,{value:{t:r=>{var t;return(null==(t=e.overrideStrings)?void 0:t[r])||n[r]},...l,...e,options:t,setOptions:i},children:r})},c=()=>s.useContext(o),d={when:!0,eventTypes:["keydown"]};function u(e,r,t){let a=(0,s.useMemo)(()=>Array.isArray(e)?e:[e],[e]),n=Object.assign({},d,t),{when:l,eventTypes:o}=n,i=(0,s.useRef)(r),{target:c}=n;(0,s.useEffect)(()=>{i.current=r});let u=(0,s.useCallback)(e=>{a.some(r=>e.key===r||e.code===r)&&i.current(e)},[a]);(0,s.useEffect)(()=>{if(l&&"u">typeof window){let e=c?c.current:window;return o.forEach(r=>{e&&e.addEventListener(r,u)}),()=>{o.forEach(r=>{e&&e.removeEventListener(r,u)})}}},[l,o,a,c,r])}var p={ARROW_DOWN:"ArrowDown",ARROW_UP:"ArrowUp",ENTER:"Enter",ESCAPE:"Escape",SPACE:"Space"},m=(e,r)=>{let t;return function(...s){clearTimeout(t),t=setTimeout(()=>{e.apply(null,s)},r)}},h=()=>(0,a.jsxs)("svg",{width:"24",height:"24",fill:"none",stroke:"currentColor",strokeWidth:"2",className:"dropdown-search-clear-icon gray",children:[(0,a.jsx)("line",{x1:"18",y1:"6",x2:"6",y2:"18"}),(0,a.jsx)("line",{x1:"6",y1:"6",x2:"18",y2:"18"})]}),x=({checked:e,option:r,onClick:t,disabled:s})=>(0,a.jsxs)("div",{className:`item-renderer ${s?"disabled":""}`,children:[(0,a.jsx)("input",{type:"checkbox",onChange:t,checked:e,tabIndex:-1,disabled:s}),(0,a.jsx)("span",{children:r.label})]}),v=({itemRenderer:e=x,option:r,checked:t,tabIndex:n,disabled:l,onSelectionChanged:o,onClick:i})=>{let c=(0,s.useRef)(),d=()=>{l||o(!t)};return u([p.ENTER,p.SPACE],e=>{d(),e.preventDefault()},{target:c}),(0,a.jsx)("label",{className:`select-item ${t?"selected":""}`,role:"option","aria-selected":t,tabIndex:n,ref:c,children:(0,a.jsx)(e,{option:r,checked:t,onClick:e=>{d(),i(e)},disabled:l})})},b=({options:e,onClick:r,skipIndex:t})=>{let{disabled:s,value:n,onChange:l,ItemRenderer:o}=c(),i=(e,r)=>{s||l(r?[...n,e]:n.filter(r=>r.value!==e.value))};return(0,a.jsx)(a.Fragment,{children:e.map((e,l)=>{let c=l+t;return(0,a.jsx)("li",{children:(0,a.jsx)(v,{tabIndex:c,option:e,onSelectionChanged:r=>i(e,r),checked:!!n.find(r=>r.value===e.value),onClick:e=>r(e,c),itemRenderer:o,disabled:e.disabled||s})},(null==e?void 0:e.key)||l)})})},f=()=>{let{t:e,onChange:r,options:t,setOptions:n,value:l,filterOptions:o,ItemRenderer:i,disabled:d,disableSearch:x,hasSelectAll:f,ClearIcon:g,debounceDuration:y,isCreatable:w,onCreateOption:k}=c(),j=(0,s.useRef)(),C=(0,s.useRef)(),[S,N]=(0,s.useState)(""),[E,A]=(0,s.useState)(t),[R,O]=(0,s.useState)(""),[I,_]=(0,s.useState)(0),T=(0,s.useCallback)(m(e=>O(e),y),[]),P=(0,s.useMemo)(()=>{let e=0;return x||(e+=1),f&&(e+=1),e},[x,f]),W={label:e(S?"selectAllFiltered":"selectAll"),value:""},M=e=>{let r=E.filter(e=>!e.disabled).map(e=>e.value);if(e){let e=[...l.map(e=>e.value),...r];return(o?E:t).filter(r=>e.includes(r.value))}return l.filter(e=>!r.includes(e.value))},D=()=>{var e;O(""),N(""),null==(e=null==C?void 0:C.current)||e.focus()},F=e=>_(e);u([p.ARROW_DOWN,p.ARROW_UP],e=>{switch(e.code){case p.ARROW_UP:$(-1);break;case p.ARROW_DOWN:$(1);break;default:return}e.stopPropagation(),e.preventDefault()},{target:j});let B=async()=>{let e={label:S,value:S,__isNew__:!0};k&&(e=await k(S)),n([e,...t]),D(),r([...l,e])},L=async()=>o?await o(t,R):function(e,r){return r?e.filter(({label:e,value:t})=>null!=e&&null!=t&&e.toLowerCase().includes(r.toLowerCase())):e}(t,R),$=e=>{let r=I+e;_(r=Math.min(r=Math.max(0,r),t.length+Math.max(P-1,0)))};(0,s.useEffect)(()=>{var e,r;null==(r=null==(e=null==j?void 0:j.current)?void 0:e.querySelector(`[tabIndex='${I}']`))||r.focus()},[I]);let[z,U]=(0,s.useMemo)(()=>{let e=E.filter(e=>!e.disabled);return[e.every(e=>-1!==l.findIndex(r=>r.value===e.value)),0!==e.length]},[E,l]);(0,s.useEffect)(()=>{L().then(A)},[R,t]);let G=(0,s.useRef)();u([p.ENTER],B,{target:G});let K=w&&S&&!E.some(e=>(null==e?void 0:e.value)===S);return(0,a.jsxs)("div",{className:"select-panel",role:"listbox",ref:j,children:[!x&&(0,a.jsxs)("div",{className:"search",children:[(0,a.jsx)("input",{placeholder:e("search"),type:"text","aria-describedby":e("search"),onChange:e=>{T(e.target.value),N(e.target.value),_(0)},onFocus:()=>{_(0)},value:S,ref:C,tabIndex:0}),(0,a.jsx)("button",{type:"button",className:"search-clear-button",hidden:!S,onClick:D,"aria-label":e("clearSearch"),children:g||(0,a.jsx)(h,{})})]}),(0,a.jsxs)("ul",{className:"options",children:[f&&U&&(0,a.jsx)(v,{tabIndex:+(1!==P),checked:z,option:W,onSelectionChanged:e=>{r(M(e))},onClick:()=>F(1),itemRenderer:i,disabled:d}),E.length?(0,a.jsx)(b,{skipIndex:P,options:E,onClick:(e,r)=>F(r)}):K?(0,a.jsx)("li",{onClick:B,className:"select-item creatable",tabIndex:1,ref:G,children:`${e("create")} "${S}"`}):(0,a.jsx)("li",{className:"no-options",children:e("noOptions")})]})]})},g=({expanded:e})=>(0,a.jsx)("svg",{width:"24",height:"24",fill:"none",stroke:"currentColor",strokeWidth:"2",className:"dropdown-heading-dropdown-arrow gray",children:(0,a.jsx)("path",{d:e?"M18 15 12 9 6 15":"M6 9L12 15 18 9"})}),y=()=>{let{t:e,value:r,options:t,valueRenderer:s}=c(),n=0===r.length,l=r.length===t.length,o=s&&s(r,t);return n?(0,a.jsx)("span",{className:"gray",children:o||e("selectSomeItems")}):(0,a.jsx)("span",{children:o||(l?e("allItemsAreSelected"):r.map(e=>e.label).join(", "))})},w=({size:e=24})=>(0,a.jsx)("span",{style:{width:e,marginRight:"0.2rem"},children:(0,a.jsx)("svg",{width:e,height:e,className:"spinner",viewBox:"0 0 50 50",style:{display:"inline",verticalAlign:"middle"},children:(0,a.jsx)("circle",{cx:"25",cy:"25",r:"20",fill:"none",className:"path"})})}),k=()=>{let{t:e,onMenuToggle:r,ArrowRenderer:t,shouldToggleOnHover:n,isLoading:l,disabled:o,onChange:i,labelledBy:d,value:m,isOpen:x,defaultIsOpen:v,ClearSelectedIcon:b,closeOnChangedValue:k}=c();(0,s.useEffect)(()=>{k&&N(!1)},[m]);let[j,C]=(0,s.useState)(!0),[S,N]=(0,s.useState)(v),[E,A]=(0,s.useState)(!1),R=(0,s.useRef)();(function(e,r){let t=(0,s.useRef)(!1);(0,s.useEffect)(()=>{t.current?e():t.current=!0},r)})(()=>{r&&r(S)},[S]),(0,s.useEffect)(()=>{void 0===v&&"boolean"==typeof x&&(C(!1),N(x))},[x]),u([p.ENTER,p.ARROW_DOWN,p.SPACE,p.ESCAPE],e=>{var r;["text","button"].includes(e.target.type)&&[p.SPACE,p.ENTER].includes(e.code)||(j&&(e.code===p.ESCAPE?(N(!1),null==(r=null==R?void 0:R.current)||r.focus()):N(!0)),e.preventDefault())},{target:R});let O=e=>{j&&n&&N(e)};return(0,a.jsxs)("div",{tabIndex:0,className:"dropdown-container","aria-labelledby":d,"aria-expanded":S,"aria-readonly":!0,"aria-disabled":o,ref:R,onFocus:()=>!E&&A(!0),onBlur:e=>{!e.currentTarget.contains(e.relatedTarget)&&j&&(A(!1),N(!1))},onMouseEnter:()=>O(!0),onMouseLeave:()=>O(!1),children:[(0,a.jsxs)("div",{className:"dropdown-heading",onClick:()=>{j&&N(!l&&!o&&!S)},children:[(0,a.jsx)("div",{className:"dropdown-heading-value",children:(0,a.jsx)(y,{})}),l&&(0,a.jsx)(w,{}),m.length>0&&null!==b&&(0,a.jsx)("button",{type:"button",className:"clear-selected-button",onClick:e=>{e.stopPropagation(),i([]),j&&N(!1)},disabled:o,"aria-label":e("clearSelected"),children:b||(0,a.jsx)(h,{})}),(0,a.jsx)(t||g,{expanded:S})]}),S&&(0,a.jsx)("div",{className:"dropdown-content",children:(0,a.jsx)("div",{className:"panel-content",children:(0,a.jsx)(f,{})})})]})},j=e=>(0,a.jsx)(i,{props:e,children:(0,a.jsx)("div",{className:`rmsc ${e.className||"multi-select"}`,children:(0,a.jsx)(k,{})})})}},e=>{var r=r=>e(e.s=r);e.O(0,[636,6593,8792],()=>r(2464)),_N_E=e.O()}]);
//# sourceMappingURL=bookmarkTableFilter-cc6337b28e069e31.js.map