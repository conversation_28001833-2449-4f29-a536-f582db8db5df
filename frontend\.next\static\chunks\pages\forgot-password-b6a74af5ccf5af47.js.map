{"version": 3, "file": "static/chunks/pages/forgot-password-b6a74af5ccf5af47.js", "mappings": "gFACA,4CACA,mBACA,WACA,OAAe,EAAQ,KAAwC,CAC/D,EACA,SAFsB,wCCJf,IAAMA,EAAU,IAErB,IADIC,EACAC,EAAcC,aAAaH,OAAO,CAACI,GACvC,GAAI,CACkB,MAAM,KACxBH,EAAQI,KAAKC,KAAK,CAACJ,EAAAA,CAEvB,CAAE,MAAOK,EAAO,CAAC,CACjB,OAAON,CACT,EAAE,EAEqB,KAErB,IADIO,EACEC,EAAOT,EAAQ,gBACrB,GAAI,CACFQ,EAAOH,KAAKC,KAAK,CAACG,EAAKD,IAAI,CAC7B,CAAE,MAAOD,EAAO,CAAC,CACjB,OAAOC,CACT,EAAE,uLCDF,IAAME,EAAuB,CAC3B,qBAAsB,kDACtB,mCAAoC,mDACpC,yBAA0B,oCAC1B,uBAAwB,yDACxB,+BAAgC,kDAChC,4BAA6B,iDAC/B,EAoIA,EAAeC,CAAAA,EAAAA,EAAAA,EAAAA,CAAOA,CAAC,GAAWC,GAlIX,IAErB,GAAM,CAACJ,EAAMK,EAAQ,CAAGC,CAAAA,EAAAA,EAAAA,CAgI8BC,EAAC,KAhI/BD,CAAQA,CAACE,CADPC,MAAO,GAAIC,SAAU,EAAG,GAG5C,CAACC,EAAaC,EAAe,CAAGN,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,EAAC,GACzC,CAACO,EAAUC,EAAY,CAAGR,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAC,IACnC,CAACS,EAAYC,EAAc,CAAGV,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,EAAC,GAEvCW,EAAgBC,IACpBJ,EAAYI,EAAEC,MAAM,CAAC1B,KAAK,CAC5B,EAEO2B,EAAc,MAAOX,IAC1B,IAAMY,EAAW,MAAMC,EAAAA,CAAUA,CAACC,GAAG,CAAC,0BAAgC,OAANd,IAChE,GAAIY,GAAYA,EAASG,OAAO,CAG9B,CAHgC,GAChCC,EAAAA,EAAKA,CAACD,OAAO,CAACtB,CAAe,CAACmB,EAASK,OAAO,CAAC,EAC/Cd,GAAe,GACXZ,GAAQA,EAAKS,KAAK,CAAE,CACtB,GAAM,QAAEkB,CAAM,CAAE,CAAGC,EAAAA,CAAWA,OACxBD,IACNX,GAAc,GAChB,MAEIK,EAASQ,IAAI,EAAIR,EAASQ,IAAI,CAACH,OAAO,CACxCD,CAD0C,CAC1CA,EAAKA,CAAC1B,KAAK,CAACG,CAAe,CAACmB,EAASK,OAAO,CAAC,EAE7CD,EAAAA,EAAKA,CAAC1B,KAAK,CAAC,yDAGjB,EAGK+B,EAAgB,MAAOZ,IAC3BA,EAAEa,cAAc,GAChB,IAAItB,EAAQI,EACRb,GAAQA,EAAKS,KAAK,EAAE,CACtBA,EAAQT,EAAKS,KAAAA,EAEXA,EAEFW,EAAYX,GAFH,EAKTgB,EAAKA,CAAC1B,KAAK,CAAC,iDAEhB,EAUA,MARAiC,CAAAA,EAAAA,EAAAA,SAAAA,CAASA,CAAC,KACR,IAAMhC,EAAOiC,CAAAA,EAAAA,EAAAA,CAAAA,CAAOA,GAChBjC,GAAQA,EAAKU,QAAQ,EAAE,CACzBL,EAAQL,GACRgB,GAAc,GAElB,EAAE,EAAE,EAGF,UAACkB,MAAAA,CAAIC,UAAU,2BACb,UAACD,MAAAA,CAAIC,UAAU,mBACb,UAACD,MAAAA,CAAIC,UAAU,qBACb,UAACD,MAAAA,CAAIC,UAAU,mBACb,UAACD,MAAAA,CAAIC,UAAU,iCACb,WAACD,MAAAA,CAAIC,UAAU,6BACb,UAACD,MAAAA,CAAIC,UAAU,qBACb,UAACC,MAAAA,CAAIC,IAAI,2BAA2BC,IAAI,6BAE1C,WAACC,OAAAA,CAAKJ,UAAU,gBAAgBK,SAAUV,YACxC,UAACI,MAAAA,CAAIC,UAAU,yBACb,UAACM,IAAIA,CAACC,KAAM,aAEV,UAACN,MAAAA,CAAIC,IAAI,KAFNI,cAEyBH,IAAI,oCAIlCvB,EAGI,UAACmB,MAAAA,UACL,WAACS,UAAAA,WACC,WAACC,IAAAA,WAAE,iDAA+C5C,EAAKS,KAAK,CAAC,qEAC7D,UAACyB,MAAAA,CAAIC,UAAU,4BACb,UAACD,MAAAA,CAAIC,UAAU,mBACb,UAACU,SAAAA,CAAOV,UAAU,oBAAoBW,KAAK,kBAAS,4BAR5C,+BAyBvBnC,CAxBUoC,CAwBK,WAACb,MAAAA,WACrB,WAACA,MAAAA,CAAIC,UAAU,iBACb,UAACa,QAAAA,CAAMb,UAAU,iBAAQ,wBACzB,UAACc,QAAAA,CACCd,UAAU,eACVW,KAAK,OACLI,KAAK,WACLC,SAAUlC,EACVmC,QAAQ,SAEZ,UAAClB,MAAAA,CAAIC,UAAU,4BACb,UAACD,MAAAA,CAAIC,UAAU,mBACb,UAACU,SAAAA,CAAOV,UAAU,oBAAoBW,KAAK,kBAAS,0BAK/C,WAACZ,MAAAA,CAAIC,UAAU,yEACxB,UAACD,MAAAA,UACC,UAACmB,EAAAA,CAAeA,CAAAA,CAACC,KAAMC,EAAAA,GAAaA,CAAEC,MAAM,UAAUC,KAAK,KAAKtB,UAAU,mBAE5E,UAACS,IAAAA,CAAET,UAAU,0CAAiC,sGAE9C,UAACM,IAAIA,CAACC,KAAK,QAAQgB,GAAG,iBACpB,WAACb,SAAAA,CAAOV,UAAU,8BAAoB,UAACkB,EAAAA,CAAeA,CAAAA,CAACC,KAAMK,EAAAA,GAAiBA,CAAEH,MAAM,QACpFC,KAAK,OAAO,6CAKtB", "sources": ["webpack://_N_E/?8fae", "webpack://_N_E/./shared/services/local-storage.ts", "webpack://_N_E/./pages/forgot-password.tsx"], "sourcesContent": ["\n    (window.__NEXT_P = window.__NEXT_P || []).push([\n      \"/forgot-password\",\n      function () {\n        return require(\"private-next-pages/forgot-password.tsx\");\n      }\n    ]);\n    if(module.hot) {\n      module.hot.dispose(function () {\n        window.__NEXT_P.push([\"/forgot-password\"])\n      });\n    }\n  ", "export const getItem = (key: string) => {\r\n  let value;\r\n  let stringValue = localStorage.getItem(key);\r\n  try {\r\n    if (stringValue !== null) {\r\n      value = JSON.parse(stringValue);\r\n    }\r\n  } catch (error) {}\r\n  return value;\r\n};\r\n\r\nexport const getUser = () => {\r\n  let user;\r\n  const root = getItem(\"persist:root\");\r\n  try {\r\n    user = JSON.parse(root.user);\r\n  } catch (error) {}\r\n  return user;\r\n};\r\n", "//Import Library\r\nimport React, {FormEvent, useEffect, useState} from 'react';\r\nimport {FontAwesomeIcon} from \"@fortawesome/react-fontawesome\";\r\nimport {\r\n  faCheckCircle,\r\n  faArrowCircleLeft\r\n} from \"@fortawesome/free-solid-svg-icons\";\r\nimport toast from 'react-hot-toast';\r\nimport Link from \"next/link\";\r\nimport { connect } from \"react-redux\";\r\n\r\n//Import services/components\r\nimport apiService from \"../services/apiService\";\r\nimport authService from \"../services/authService\";\r\nimport { Iuser } from '../shared/interfaces/user.interface';\r\nimport { getItem, getUser } from \"../shared/services/local-storage\"\r\n\r\nconst responseMessage: any = {\r\n  \"LOGIN.EMAIL_RESENT\": \"Password reset successfully. Please check email\",\r\n  \"REGISTRATION.ERROR.MAIL_NOT_SENT\": \"Error resetting password. Unable to find account\",\r\n  \"LOGIN.ERROR.SEND_EMAIL\": \"You have entered a invalid e-mail\",\r\n  \"LOGIN.USER_NOT_FOUND\": \"Error Resetting Password. Please enter validate E-mail\",\r\n  \"REGISTER.USER_NOT_REGISTERED\": \"Unable to reset password. Contact Administrator\",\r\n  \"LOGIN.ERROR.GENERIC_ERROR\": \"Unable to reset password. Contact Administrator\"\r\n};\r\n\r\nconst ForgetPassword = (props: any) => {\r\n  const initUser: Iuser = { email: \"\", username: \"\" };\r\n  const [user, setUser] = useState(initUser);\r\n\r\n  const [successPage, setSuccessPage] = useState(true);\r\n  const [userInfo, setUserInfo] = useState(\"\")\r\n  const [isLoggedIn, setIsLoggedIn] = useState(false);\r\n\r\n  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {\r\n    setUserInfo(e.target.value)\r\n  }\r\n\r\n   const getresponse = async (email: string) =>{\r\n    const response = await apiService.get(`/email/forgot-password/${email}`);\r\n    if (response && response.success) {\r\n      toast.success(responseMessage[response.message]);\r\n      setSuccessPage(false);\r\n      if (user && user.email) {\r\n        const { logout } = authService;\r\n        await logout();\r\n        setIsLoggedIn(false);\r\n      }\r\n    } else {\r\n      if (response.data && response.data.message) {\r\n        toast.error(responseMessage[response.message]);\r\n      } else {\r\n        toast.error(\"Error Resetting Password. Please enter validate E-mail\");\r\n      }\r\n    }\r\n   }\r\n\r\n\r\n  const submitHandler = async (e: FormEvent<HTMLFormElement>) => {\r\n    e.preventDefault();\r\n    let email = userInfo;\r\n    if (user && user.email) {\r\n      email = user.email;\r\n    }\r\n    if (email) {\r\n\r\n      getresponse(email)\r\n\r\n    } else {\r\n      toast.error(\"Error reseting password. Contact administrator\");\r\n    }\r\n  }\r\n\r\n  useEffect(() => {\r\n    const user = getUser();\r\n    if (user && user.username) {\r\n      setUser(user)\r\n      setIsLoggedIn(true)\r\n    }\r\n  },[])\r\n\r\n  return (\r\n    <div className=\"loginContainer \">\r\n      <div className='section'>\r\n        <div className='container'>\r\n          <div className='columns'>\r\n            <div className='column  is-two-thirds'>\r\n              <div className='column loginForm'>\r\n                <div className=\"imgBanner\">\r\n                  <img src=\"/images/login-banner.jpg\" alt=\"RKI Login Banner Image\"/>\r\n                </div>\r\n                <form className=\"formContainer\" onSubmit={submitHandler}>\r\n                  <div className=\"logoContainer\">\r\n                    <Link href={'/'}>\r\n\r\n                      <img src=\"/images/logo.jpg\" alt=\"Rohert Koch Institut - Logo\"/>\r\n\r\n                    </Link>\r\n                  </div>\r\n                  {!isLoggedIn ? (<>\r\n                    {success_func()\r\n                    }\r\n                  </>):(<div>\r\n                    <section>\r\n                      <p>Password reset instructions will be mailed to {user.email}. You must log out to use the password reset link in the email.</p>\r\n                      <div className='field is-grouped'>\r\n                        <div className='control'>\r\n                          <button className='button is-primary' type='submit'>\r\n                            Reset Password\r\n                          </button>\r\n                        </div>\r\n                      </div>\r\n                    </section>\r\n                  </div>)}\r\n                </form>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n\r\n  function success_func() {\r\n    return successPage ? (<div>\r\n      <div className='mb-3'>\r\n        <label className='label'>Enter your email id</label>\r\n        <input\r\n          className='form-control'\r\n          type='text'\r\n          name='username'\r\n          onChange={handleChange}\r\n          required />\r\n      </div>\r\n      <div className='field is-grouped'>\r\n        <div className='control'>\r\n          <button className='button is-primary' type='submit'>\r\n            Reset Password\r\n          </button>\r\n        </div>\r\n      </div>\r\n    </div>) : (<div className=\"d-flex flex-column justify-content-center align-items-center\">\r\n      <div>\r\n        <FontAwesomeIcon icon={faCheckCircle} color=\"#1a273a\" size=\"5x\" className=\"success-icon\" />\r\n      </div>\r\n      <p className=\"text-center lead mt-3 infotext\">Your one time password reset link is sent to your\r\n        email. Please use that and reset your password.</p>\r\n      <Link href=\"/home\" as=\"/home\" >\r\n        <button className=\"button is-primary\"><FontAwesomeIcon icon={faArrowCircleLeft} color=\"#ffff\"\r\n          size=\"1x\" /> Back to RKI Home\r\n        </button>\r\n      </Link>\r\n    </div>);\r\n  }\r\n}\r\n\r\nexport default connect((state) => state)(ForgetPassword);"], "names": ["getItem", "value", "stringValue", "localStorage", "key", "JSON", "parse", "error", "user", "root", "responseMessage", "connect", "state", "setUser", "useState", "ForgetPassword", "initUser", "email", "username", "successPage", "setSuccessPage", "userInfo", "setUserInfo", "isLoggedIn", "setIsLoggedIn", "handleChange", "e", "target", "getresponse", "response", "apiService", "get", "success", "toast", "message", "logout", "authService", "data", "<PERSON><PERSON><PERSON><PERSON>", "preventDefault", "useEffect", "getUser", "div", "className", "img", "src", "alt", "form", "onSubmit", "Link", "href", "section", "p", "button", "type", "success_func", "label", "input", "name", "onChange", "required", "FontAwesomeIcon", "icon", "faCheckCircle", "color", "size", "as", "faArrowCircleLeft"], "sourceRoot": "", "ignoreList": []}