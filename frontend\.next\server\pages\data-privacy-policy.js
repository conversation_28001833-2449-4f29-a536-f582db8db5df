"use strict";(()=>{var e={};e.id=7330,e.ids=[636,3220,7330],e.modules={123:e=>{e.exports=require("dom-helpers/removeEventListener")},1332:e=>{e.exports=require("react-custom-scrollbars-2")},1428:e=>{e.exports=import("axios")},1680:e=>{e.exports=require("@restart/ui/utils")},1919:e=>{e.exports=require("react-transition-group/Transition")},3892:e=>{e.exports=require("classnames")},4048:e=>{e.exports=require("react-truncate")},6009:e=>{e.exports=require("dom-helpers/ownerDocument")},6417:(e,r,t)=>{t.d(r,{A:()=>a});let s=t(82015).createContext(null);s.displayName="CardHeaderContext";let a=s},6952:e=>{e.exports=require("@restart/ui/Modal")},7374:e=>{e.exports=require("@restart/ui/Dropdown")},8732:e=>{e.exports=require("react/jsx-runtime")},9653:e=>{e.exports=require("@restart/ui/Overlay")},11242:e=>{e.exports=require("redux-persist/integration/react")},11688:e=>{e.exports=require("@restart/hooks/useWillUnmount")},12388:e=>{e.exports=require("dom-helpers/addEventListener")},13364:e=>{e.exports=require("redux-saga/effects")},14062:e=>{e.exports=import("react-redux")},14078:e=>{e.exports=import("swr")},14332:e=>{e.exports=require("uncontrollable")},16116:e=>{e.exports=require("invariant")},18597:(e,r,t)=>{t.d(r,{A:()=>g});var s=t(3892),a=t.n(s),i=t(82015),o=t(80739),d=t(8732);let l=i.forwardRef(({className:e,bsPrefix:r,as:t="div",...s},i)=>(r=(0,o.oU)(r,"card-body"),(0,d.jsx)(t,{ref:i,className:a()(e,r),...s})));l.displayName="CardBody";let n=i.forwardRef(({className:e,bsPrefix:r,as:t="div",...s},i)=>(r=(0,o.oU)(r,"card-footer"),(0,d.jsx)(t,{ref:i,className:a()(e,r),...s})));n.displayName="CardFooter";var p=t(6417);let x=i.forwardRef(({bsPrefix:e,className:r,as:t="div",...s},l)=>{let n=(0,o.oU)(e,"card-header"),x=(0,i.useMemo)(()=>({cardHeaderBsPrefix:n}),[n]);return(0,d.jsx)(p.A.Provider,{value:x,children:(0,d.jsx)(t,{ref:l,...s,className:a()(r,n)})})});x.displayName="CardHeader";let u=i.forwardRef(({bsPrefix:e,className:r,variant:t,as:s="img",...i},l)=>{let n=(0,o.oU)(e,"card-img");return(0,d.jsx)(s,{ref:l,className:a()(t?`${n}-${t}`:n,r),...i})});u.displayName="CardImg";let c=i.forwardRef(({className:e,bsPrefix:r,as:t="div",...s},i)=>(r=(0,o.oU)(r,"card-img-overlay"),(0,d.jsx)(t,{ref:i,className:a()(e,r),...s})));c.displayName="CardImgOverlay";let h=i.forwardRef(({className:e,bsPrefix:r,as:t="a",...s},i)=>(r=(0,o.oU)(r,"card-link"),(0,d.jsx)(t,{ref:i,className:a()(e,r),...s})));h.displayName="CardLink";var f=t(7783);let m=(0,f.A)("h6"),P=i.forwardRef(({className:e,bsPrefix:r,as:t=m,...s},i)=>(r=(0,o.oU)(r,"card-subtitle"),(0,d.jsx)(t,{ref:i,className:a()(e,r),...s})));P.displayName="CardSubtitle";let y=i.forwardRef(({className:e,bsPrefix:r,as:t="p",...s},i)=>(r=(0,o.oU)(r,"card-text"),(0,d.jsx)(t,{ref:i,className:a()(e,r),...s})));y.displayName="CardText";let j=(0,f.A)("h5"),b=i.forwardRef(({className:e,bsPrefix:r,as:t=j,...s},i)=>(r=(0,o.oU)(r,"card-title"),(0,d.jsx)(t,{ref:i,className:a()(e,r),...s})));b.displayName="CardTitle";let q=i.forwardRef(({bsPrefix:e,className:r,bg:t,text:s,border:i,body:n=!1,children:p,as:x="div",...u},c)=>{let h=(0,o.oU)(e,"card");return(0,d.jsx)(x,{ref:c,...u,className:a()(r,h,t&&`bg-${t}`,s&&`text-${s}`,i&&`border-${i}`),children:n?(0,d.jsx)(l,{children:p}):p})});q.displayName="Card";let g=Object.assign(q,{Img:u,Title:b,Subtitle:P,Body:l,Link:h,Text:y,Header:x,Footer:n,ImgOverlay:c})},18622:e=>{e.exports=require("yup")},19121:e=>{e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},21e3:(e,r,t)=>{t.r(r),t.d(r,{default:()=>l,getServerSideProps:()=>d});var s=t(8732),a=t(18597),i=t(88751),o=t(35576);async function d({locale:e}){return{props:{...await (0,o.serverSideTranslations)(e,["common"])}}}let l=()=>{let{t:e}=(0,i.useTranslation)("common");return(0,s.jsx)(a.A,{className:"privacyCard",children:(0,s.jsxs)(a.A.Body,{children:[(0,s.jsx)("h2",{children:e("dataPolicy.mainheader")}),(0,s.jsx)("p",{children:e("dataPolicy.info1")}),(0,s.jsxs)("p",{children:[(0,s.jsxs)("strong",{children:[" ",e("dataPolicy.info2")]}),(0,s.jsx)("br",{}),e("dataPolicy.info3"),(0,s.jsx)("br",{}),e("dataPolicy.info4"),(0,s.jsx)("br",{}),e("dataPolicy.info5")]}),(0,s.jsx)("p",{children:e("dataPolicy.info6")}),(0,s.jsxs)("p",{children:[(0,s.jsx)("strong",{children:e("dataPolicy.info7")}),(0,s.jsx)("br",{}),e("dataPolicy.info8"),(0,s.jsx)("br",{}),e("dataPolicy.info9"),(0,s.jsx)("br",{}),e("dataPolicy.info10"),(0,s.jsx)("br",{}),e("dataPolicy.info11"),(0,s.jsx)("br",{}),e("dataPolicy.info12")]}),(0,s.jsxs)("p",{children:[e("dataPolicy.info13"),(0,s.jsx)("br",{}),e("dataPolicy.info14"),(0,s.jsx)("br",{}),e("dataPolicy.info15"),(0,s.jsx)("br",{}),e("dataPolicy.info16")," ",(0,s.jsx)("a",{href:"https://rki-uat.adapptlabs.com/",children:"https://rki-uat.adapptlabs.com/"})]}),(0,s.jsxs)("p",{children:[(0,s.jsx)("strong",{children:e("dataPolicy.info17")}),(0,s.jsx)("br",{}),e("dataPolicy.info18")]}),(0,s.jsxs)("p",{children:[e("dataPolicy.info19"),(0,s.jsx)("br",{}),e("dataPolicy.info20")]}),(0,s.jsxs)("p",{children:[(0,s.jsxs)("strong",{children:[" ",e("dataPolicy.info21")]}),(0,s.jsx)("br",{}),e("dataPolicy.info22")]}),(0,s.jsxs)("p",{children:[(0,s.jsx)("strong",{children:e("dataPolicy.info23")}),(0,s.jsx)("br",{}),e("dataPolicy.info24"),(0,s.jsx)("br",{}),(0,s.jsx)("a",{target:"_blank",href:"https://adappt.co.uk/",children:"https://adappt.co.uk/"})]}),(0,s.jsx)("h3",{children:e("dataPolicy.subheader.header")}),(0,s.jsx)("p",{children:(0,s.jsx)("strong",{children:e("dataPolicy.subheader.text1")})}),(0,s.jsx)("p",{children:e("dataPolicy.subheader.text2")}),(0,s.jsx)("p",{children:(0,s.jsxs)("strong",{children:[" ",e("dataPolicy.subheader.text3")]})}),(0,s.jsx)("p",{children:e("dataPolicy.subheader.text4")}),(0,s.jsx)("p",{children:e("dataPolicy.subheader.text5")}),(0,s.jsxs)("ul",{children:[(0,s.jsx)("li",{children:e("dataPolicy.subheader.text6")}),(0,s.jsx)("li",{children:e("dataPolicy.subheader.text7")}),(0,s.jsx)("li",{children:e("dataPolicy.subheader.text8")}),(0,s.jsx)("li",{children:e("dataPolicy.subheader.text9")}),(0,s.jsx)("li",{children:e("dataPolicy.subheader.text10")}),(0,s.jsx)("li",{children:e("dataPolicy.subheader.text11")})]}),(0,s.jsx)("p",{children:e("dataPolicy.subheader.text12")}),(0,s.jsx)("p",{children:e("dataPolicy.subheader.text13")}),(0,s.jsxs)("p",{children:[e("dataPolicy.subheader.text14")," ",(0,s.jsx)("a",{target:"_blank",href:e("dataPolicy.subheader.text14a"),children:e("dataPolicy.subheader.text14b")})]})]})})}},22326:e=>{e.exports=require("react-dom")},22541:e=>{e.exports=require("redux-persist/lib/storage")},25303:e=>{e.exports=require("@restart/ui/NavItem")},26324:e=>{e.exports=require("warning")},27825:e=>{e.exports=require("lodash")},27910:e=>{e.exports=require("stream")},28217:e=>{e.exports=require("@restart/ui/DropdownItem")},29021:e=>{e.exports=require("fs")},29294:e=>{e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},29628:e=>{e.exports=require("dom-helpers/addClass")},29825:e=>{e.exports=require("prop-types")},29841:e=>{e.exports=require("dom-helpers/hasClass")},30362:e=>{e.exports=require("@restart/ui/DropdownToggle")},33873:e=>{e.exports=require("path")},35576:e=>{e.exports=require("next-i18next/serverSideTranslations")},36653:e=>{e.exports=require("nprogress")},36955:e=>{e.exports=require("@restart/hooks/useBreakpoint")},37766:e=>{e.exports=require("@restart/hooks/useMergedRefs")},39756:e=>{e.exports=import("redux")},40051:e=>{e.exports=require("dom-helpers/removeClass")},40361:e=>{e.exports=require("next/dist/compiled/next-server/pages.runtime.prod.js")},42893:e=>{e.exports=import("react-hot-toast")},43294:e=>{e.exports=require("formik")},50009:e=>{e.exports=require("@restart/ui/DropdownMenu")},57664:e=>{e.exports=require("dom-helpers/scrollbarSize")},58928:e=>{e.exports=require("dom-helpers/canUseDOM")},59717:e=>{e.exports=require("@restart/ui/DropdownContext")},60560:e=>{e.exports=require("dom-helpers/contains")},62253:(e,r,t)=>{t.a(e,async(e,s)=>{try{t.r(r),t.d(r,{config:()=>m,default:()=>u,getServerSideProps:()=>f,getStaticPaths:()=>h,getStaticProps:()=>c,reportWebVitals:()=>P,routeModule:()=>v,unstable_getServerProps:()=>q,unstable_getServerSideProps:()=>g,unstable_getStaticParams:()=>b,unstable_getStaticPaths:()=>j,unstable_getStaticProps:()=>y});var a=t(63885),i=t(80237),o=t(81413),d=t(9616),l=t.n(d),n=t(72386),p=t(21e3),x=e([n]);n=(x.then?(await x)():x)[0];let u=(0,o.M)(p,"default"),c=(0,o.M)(p,"getStaticProps"),h=(0,o.M)(p,"getStaticPaths"),f=(0,o.M)(p,"getServerSideProps"),m=(0,o.M)(p,"config"),P=(0,o.M)(p,"reportWebVitals"),y=(0,o.M)(p,"unstable_getStaticProps"),j=(0,o.M)(p,"unstable_getStaticPaths"),b=(0,o.M)(p,"unstable_getStaticParams"),q=(0,o.M)(p,"unstable_getServerProps"),g=(0,o.M)(p,"unstable_getServerSideProps"),v=new a.PagesRouteModule({definition:{kind:i.A.PAGES,page:"/data-privacy-policy",pathname:"/data-privacy-policy",bundlePath:"",filename:""},components:{App:n.default,Document:l()},userland:p});s()}catch(e){s(e)}})},63033:e=>{e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},65447:e=>{e.exports=require("@restart/ui/Button")},67364:e=>{e.exports=require("@restart/hooks/useCallbackRef")},68455:e=>{e.exports=import("redux-saga")},69722:e=>{e.exports=require("es6-promise")},74075:e=>{e.exports=require("zlib")},74716:e=>{e.exports=require("moment")},74987:e=>{e.exports=require("@restart/ui/ModalManager")},78097:e=>{e.exports=require("next-redux-saga")},78634:e=>{e.exports=require("@restart/ui/Anchor")},80237:(e,r)=>{Object.defineProperty(r,"A",{enumerable:!0,get:function(){return t}});var t=function(e){return e.PAGES="PAGES",e.PAGES_API="PAGES_API",e.APP_PAGE="APP_PAGE",e.APP_ROUTE="APP_ROUTE",e.IMAGE="IMAGE",e}({})},81366:e=>{e.exports=require("redux-auth-wrapper/connectedAuthWrapper")},81413:(e,r)=>{Object.defineProperty(r,"M",{enumerable:!0,get:function(){return function e(r,t){return t in r?r[t]:"then"in r&&"function"==typeof r.then?r.then(r=>e(r,t)):"function"==typeof r&&"default"===t?r:void 0}}})},81521:e=>{e.exports=require("dom-helpers/querySelectorAll")},81895:e=>{e.exports=require("@restart/hooks/useEventCallback")},82015:e=>{e.exports=require("react")},86842:e=>{e.exports=require("@restart/ui/SelectableContext")},87571:e=>{e.exports=require("dom-helpers/transitionEnd")},88751:e=>{e.exports=require("next-i18next")},93787:e=>{e.exports=require("redux-persist")},94947:e=>{e.exports=require("@restart/hooks/useTimeout")},96196:e=>{e.exports=require("next-redux-wrapper")},98320:e=>{e.exports=require("@restart/hooks/useIsomorphicEffect")},99460:e=>{e.exports=require("dom-helpers/css")}};var r=require("../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[6089,9216,9616,2386],()=>t(62253));module.exports=s})();