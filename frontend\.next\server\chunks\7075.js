"use strict";exports.id=7075,exports.ids=[7075],exports.modules={87075:(e,s,a)=>{a.a(e,async(e,t)=>{try{a.r(s),a.d(s,{default:()=>q});var l=a(8732),r=a(82015),c=a(91353),n=a(7082),i=a(83551),d=a(49481),o=a(13524),u=a(12403),p=a(27825),m=a.n(p),v=a(19918),h=a.n(v),b=a(82053),f=a(54131),x=a(99800),j=a(59984),A=a(42738),y=a(42893),g=a(44233),w=a.n(g),S=a(56084),M=a(63487),N=a(88751),k=e([f,x,j,A,y,M]);[f,x,j,A,y,M]=k.then?(await k)():k;let _=e=>{let s,a,t=(0,g.useRouter)(),{t:p}=(0,N.useTranslation)("common"),v=(0,j.default)(),[k,_]=(0,r.useState)(null),[q,C]=(0,r.useState)([]),[V,P]=(0,r.useState)([]),[T,U]=(0,r.useState)(""),[H,R]=(0,r.useState)([]),[$,I]=(0,r.useState)([]),[D,E]=(0,r.useState)(!1),[F,O]=(0,r.useState)(null),[z,B]=(0,r.useState)(null),[K,L]=(0,r.useState)(!1),Y=t&&t.query&&t.query.id,G={query:{},sort:{username:"asc"},limit:"~"},J=async()=>{let e=await M.A.get(`/vspace/${Y}`);e&&_(e)},Q=async()=>{let e=await M.A.get("/users",G);e?.data?.length&&(e.data=e.data.filter(e=>"Request Pending"!==e.vspace_status&&"Request Pending"!==e.status));let s=await M.A.get(`/vspace/${Y}`);if(e&&s&&s.members){let a=s.members&&s.members.map(e=>e._id),t=e.data.map((e,s)=>({label:e.username,value:e._id})).filter(e=>-1===a.indexOf(e.value));C(t)}},W={query:{vspace:Y},select:"-vspace -requested_to -created_at -updated_at",limit:"~"},X=async()=>{let e=await M.A.get("/vspace-request-subscribers/getRequestedToMe",W);if(e&&e.data&&e.data.length>0){let s=e.data.map(e=>(e.requested_by._id=e._id,e.requested_by));I(s)}else I(e&&e.data)};(0,r.useEffect)(()=>{J(),Q(),X()},[]),(0,r.useEffect)(()=>{J(),Q(),X()},[K]);let Z=async e=>{if(T)switch(e.key){case"Enter":case"Tab":if(/\S+@\S+\.\S+/.test(T)){let s=await M.A.post("/vspace/filterNonmember",{email:T});""===s.message?R([...H,{label:T,value:T}]):y.default.error(`${T}  is already exist`),U(""),e.preventDefault()}else e.preventDefault(),y.default.error(p("vspace.Pleaseentervalidemailaddress")),U("")}},ee=async()=>{let e={_id:F._id,status:z},s=await M.A.post("/vspace/requestStatus",e);s&&200===s.status&&L(!K)},es=(e,s)=>{E(!0),B(s),O(e)},ea=async()=>{await M.A.remove(`/vspace/${Y}`),y.default.success(p("vspace.DeletedtheVirtualSpacesuccessfully")),w().push("/vspace")},et=[{name:p("vspace.UserName"),selector:"username",sortable:!0},{name:p("vspace.Email"),selector:"email",sortable:!0},{name:p("vspace.Action"),cell:e=>e?(0,l.jsx)("a",{onClick:()=>es(e._id,"platformMember"),style:{cursor:"pointer"},children:(0,l.jsx)("i",{className:"icon fas fa-trash-alt"})}):""}],el=[{name:p("vspace.UserName"),selector:"username",sortable:!0},{name:p("vspace.Email"),selector:"email",sortable:!0},{name:p("vspace.Action"),cell:e=>(0,l.jsxs)("div",{children:[(0,l.jsx)(c.A,{variant:"primary",size:"sm",onClick:()=>es(e,"approved"),children:p("vspace.Approve")}),"\xa0",(0,l.jsx)(c.A,{variant:"secondary",size:"sm",onClick:()=>es(e,"declined"),children:p("vspace.Reject")})]})}];switch(z){case"platformMember":s=p("vspace.Deleteuserfromvirtualspace"),a=p("vspace.AreyousurewanttoremovetheuserfromtheVirtualSpace?");break;case"approved":s=p("vspace.ApproveUser"),a=p("vspace.AreyousurewanttoaddtheusertoVirtualSpace?");break;case"declined":s=p("vspace.RejectUser"),a=p("vspace.AreyousurewanttorejecttheuserfromVirtualSpace?");break;case"deleteVspace":s=p("vspace.DeleteVirtualSpace"),a=p("vspace.AreyousurewanttodeletetheVirtualSpace?")}let er=(e,s,a)=>m().orderBy(e,e=>e[s]?e[s].toLowerCase():e[s],a),ec=async()=>{let e=k&&k.members.filter(e=>e._id!==F).map(e=>e._id),s=k&&k.subscribers.filter(e=>e._id!==F);await M.A.patch(`/vspace/${Y}`,{...k,members:e,nonMembers:""===k.nonMembers[0]?"":k.nonMembers,subscribers:s.map(e=>e._id)}),L(!K)},en=()=>{E(!1),O(null),B(null)},ei=async e=>{let s=H&&H.map(e=>e.value),a=V&&V.map(e=>e.value),t=k&&k.members.map(e=>e._id);"platformMembers"===e&&V.length>0?(await M.A.patch(`/vspace/${Y}`,{...k,members:[...t,...a],nonMembers:""===k.nonMembers[0]?"":k.nonMembers}),y.default.success(p("vspace.UserInvitedsuccessfully")),P([]),L(!K)):"nonPlatformMembers"===e&&H.length>0?(await M.A.patch(`/vspace/${Y}`,{...k,nonMembers:s,members:t}),y.default.success(p("vspace.UserInvitedsuccessfully")),R([]),L(!K)):y.default.error(p("vspace.Pleasechoosemembersoraddemailtoinvite"))};return(0,l.jsx)("div",{className:"pe-2",children:(0,l.jsxs)(n.A,{children:[k?(0,l.jsxs)(l.Fragment,{children:[(0,l.jsx)(i.A,{className:"mt-3",children:(0,l.jsx)(d.A,{className:"p-0",children:(0,l.jsxs)("div",{className:"d-flex justify-content-between",children:[(0,l.jsx)("div",{children:(0,l.jsxs)("h4",{children:[(0,l.jsx)(h(),{href:"/vspace/[...routes]",as:`/vspace/show/${Y}`,className:"h5 p-0 m-0",children:k.title}),(0,l.jsx)(h(),{href:"/vspace/[...routes]",as:`/vspace/edit/${Y}`,children:(0,l.jsxs)(c.A,{variant:"secondary",size:"sm",className:"ms-2",children:[(0,l.jsx)(b.FontAwesomeIcon,{icon:f.faPen}),"\xa0",p("vspace.Edit")]})})]})}),(0,l.jsxs)(c.A,{variant:"dark",onClick:()=>{E(!0),B("deleteVspace"),O(Y)},children:[" ",(0,l.jsx)(b.FontAwesomeIcon,{icon:f.faTrashAlt,color:"#fff",className:"me-2"}),p("vspace.DeleteVirtualSpace")]})]})})}),(0,l.jsx)(i.A,{children:(0,l.jsxs)(d.A,{className:"header-block",lg:12,children:[(0,l.jsx)("h6",{children:(0,l.jsx)("span",{children:p("vspace.MonitoringandEvaluationMembers")})}),(0,l.jsx)(S.A,{noHeader:!0,columns:et,data:k.members,dense:!0,paginationServer:!0,pagServer:!0,paginationTotalRows:0,subHeader:!0,subHeaderAlign:"left",pagination:!0,persistTableHead:!0,sortFunction:er})]})}),(0,l.jsx)(i.A,{children:(0,l.jsxs)(d.A,{className:"header-block",lg:12,children:[(0,l.jsx)("h6",{children:(0,l.jsx)("span",{children:p("vspace.SubscribeRequestUsers")})}),$&&$.length>0?(0,l.jsx)(S.A,{noHeader:!0,columns:el,data:$,dense:!0,paginationServer:!0,pagServer:!0,paginationTotalRows:0,subHeader:!0,subHeaderAlign:"left",pagination:!0,persistTableHead:!0,sortFunction:er}):(0,l.jsx)("div",{className:"nodataFound",children:p("vspace.Nodataavailable")})]})}),(0,l.jsx)(i.A,{children:(0,l.jsxs)(d.A,{className:"header-block",children:[(0,l.jsx)("h6",{children:(0,l.jsx)("span",{children:p("vspace.PlatformMembersInvites")})}),(0,l.jsxs)("div",{children:[(0,l.jsx)(x.default,{closeMenuOnSelect:!1,components:v,isMulti:!0,value:V||[],placeholder:p("vspace.SelectUsers"),onChange:s=>{let a=e.allOption||{label:"All users",value:"*"};s&&s.length>0&&s[s.length-1].value===a.value?P(q):P(s)},options:[e.allOption||{label:"All users",value:"*"},...q||[]]}),(0,l.jsx)(c.A,{className:"mt-3",variant:"primary",onClick:()=>ei("platformMembers"),children:p("vspace.AddMembers")})]})]})}),(0,l.jsx)(i.A,{children:(0,l.jsxs)(d.A,{className:"header-block",children:[(0,l.jsx)("h6",{children:(0,l.jsx)("span",{children:p("vspace.Non-PlatformMemberInvites(by email)")})}),(0,l.jsx)("small",{children:p("vspace.PressTabtoseparatemultipleemailid(s)")}),(0,l.jsxs)("div",{children:[(0,l.jsx)(A.default,{components:v,inputValue:T,isClearable:!0,isMulti:!0,menuIsOpen:!1,onChange:()=>R(H||[]),onInputChange:e=>U(e),onKeyDown:Z,placeholder:p("vspace.Typeemailid(s),pressingenterbetweeneachone"),value:H||[]}),(0,l.jsx)(c.A,{className:"mt-3",variant:"primary",onClick:()=>ei("nonPlatformMembers"),children:p("vspace.AddMembers")})]})]})})]}):(0,l.jsx)("div",{className:"d-flex justify-content-center align-items-center ",children:(0,l.jsx)(o.A,{animation:"border",variant:"primary"})}),(0,l.jsxs)(u.A,{show:D,onHide:()=>E(!1),children:[(0,l.jsx)(u.A.Header,{closeButton:!0,children:(0,l.jsx)(u.A.Title,{children:s})}),(0,l.jsx)(u.A.Body,{children:a}),(0,l.jsxs)("div",{className:"d-flex justify-content-end m-2",children:[(0,l.jsx)(c.A,{variant:"secondary",onClick:()=>{switch(z){case"platformMember":case"declined":case"approved":case"deleteVspace":en()}},className:"me-2",children:p("vspace.No")}),(0,l.jsx)(c.A,{variant:"primary",onClick:()=>{switch(z){case"platformMember":y.default.success(p("vspace.Userremovedsuccessfully")),en(),ec();break;case"approved":y.default.success(p("vspace.Userapprovedsuccessfully")),en(),ee();break;case"declined":y.default.success(p("vspace.Userrejectedsuccessfully")),en(),ee();break;case"deleteVspace":E(!1),B(null),ea()}},children:p("vspace.Yes")})]})]})]})})};_.defaultProps={allOption:{label:"All users",value:"*"}};let q=_;t()}catch(e){t(e)}})}};