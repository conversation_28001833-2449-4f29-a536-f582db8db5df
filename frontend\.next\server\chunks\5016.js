"use strict";exports.id=5016,exports.ids=[5016],exports.modules={20156:(e,t,r)=>{var a=r(92921);t.__esModule=!0,t.default=void 0;var l=a(r(3892)),o=function(e,t){if(e&&e.__esModule)return e;if(null===e||"object"!=typeof e&&"function"!=typeof e)return{default:e};var r=p(t);if(r&&r.has(e))return r.get(e);var a={__proto__:null},l=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var o in e)if("default"!==o&&Object.prototype.hasOwnProperty.call(e,o)){var n=l?Object.getOwnPropertyDescriptor(e,o):null;n&&(n.get||n.set)?Object.defineProperty(a,o,n):a[o]=e[o]}return a.default=e,r&&r.set(e,a),a}(r(82015)),n=r(65447),s=r(11940),i=r(8732);function p(e){if("function"!=typeof WeakMap)return null;var t=new WeakMap,r=new WeakMap;return(p=function(e){return e?r:t})(e)}let c=o.forwardRef(({as:e,bsPrefix:t,variant:r="primary",size:a,active:o=!1,disabled:p=!1,className:c,...y},d)=>{let u=(0,s.useBootstrapPrefix)(t,"btn"),[f,{tagName:m}]=(0,n.useButtonProps)({tagName:e,disabled:p,...y});return(0,i.jsx)(m,{...f,...y,ref:d,disabled:p,className:(0,l.default)(c,u,o&&"active",r&&`${u}-${r}`,a&&`${u}-${a}`,y.href&&p&&"disabled")})});c.displayName="Button",t.default=c,e.exports=t.default},20181:(e,t,r)=>{r.a(e,async(e,a)=>{try{r.d(t,{A:()=>d});var l=r(8732),o=r(82015);r(27825);var n=r(59549),s=r(91353),i=r(63487),p=r(88751),c=e([i]);function y(e){let{filtreg:t}=e,[r,a]=(0,o.useState)(!0),[i,c]=(0,o.useState)([]),[y,d]=(0,o.useState)([]),{t:u}=(0,p.useTranslation)("common"),f=e=>{let r=[...i],l=[...y];r.forEach((t,a)=>{t.code===e.target.id&&(r[a].isChecked=e.target.checked,e.target.checked?l.push(t._id):l=l.filter(e=>e!==t._id))}),d(l),t(l),a(!1),c(r)};return(0,l.jsxs)("div",{className:"regions-multi-checkboxes",children:[(0,l.jsx)(n.A.Check,{type:"checkbox",id:"all",label:u("AllRegions"),checked:r,onChange:e=>{let r=i.map(t=>({...t,isChecked:e.target.checked})),l=[];e.target.checked&&(l=r.map(e=>e._id)),t(l),d(l),a(e.target.checked),c(r)}}),i.map((e,t)=>(0,l.jsx)(n.A.Check,{type:"checkbox",id:e.code,label:e.title,value:e.code,onChange:f,checked:i[t].isChecked},t)),(0,l.jsx)(s.A,{onClick:()=>{let e=i.map(e=>({...e,isChecked:!1}));d([]),a(!1),c(e),t([])},className:"btn-plain ps-2",children:u("ClearAll")})]})}i=(c.then?(await c)():c)[0],y.defaultProps={filtreg:()=>{}};let d=y;a()}catch(e){a(e)}})},27053:(e,t,r)=>{r.d(t,{A:()=>l});var a=r(8732);function l(e){return(0,a.jsx)("h2",{className:"page-heading",children:e.title})}},56084:(e,t,r)=>{r.d(t,{A:()=>p});var a=r(8732);r(82015);var l=r(38609),o=r.n(l),n=r(88751),s=r(30370);function i(e){let{t}=(0,n.useTranslation)("common"),r={rowsPerPageText:t("Rowsperpage")},{columns:l,data:i,totalRows:p,resetPaginationToggle:c,subheader:y,subHeaderComponent:d,handlePerRowsChange:u,handlePageChange:f,rowsPerPage:m,defaultRowsPerPage:T,selectableRows:g,loading:b,pagServer:h,onSelectedRowsChange:v,clearSelectedRows:P,sortServer:k,onSort:x,persistTableHead:j,sortFunction:A,...C}=e,w={paginationComponentOptions:r,noDataComponent:t("NoData"),noHeader:!0,columns:l,data:i||[],dense:!0,paginationResetDefaultPage:c,subHeader:y,progressPending:b,subHeaderComponent:d,pagination:!0,paginationServer:h,paginationPerPage:T||10,paginationRowsPerPageOptions:m||[10,15,20,25,30],paginationTotalRows:p,onChangeRowsPerPage:u,onChangePage:f,selectableRows:g,onSelectedRowsChange:v,clearSelectedRows:P,progressComponent:(0,a.jsx)(s.A,{}),sortIcon:(0,a.jsx)("i",{className:"sort-icon fas fa-exchange-alt"}),sortServer:k,onSort:x,sortFunction:A,persistTableHead:j,className:"rki-table"};return(0,a.jsx)(o(),{...w})}i.defaultProps={subHeader:!1,pagination:!0,totalRows:null,pagServer:!0,onSelectedRowsChange:null,clearSelectedRows:!1,sortServer:!1,persistTableHead:!1};let p=i},72953:(e,t,r)=>{r.d(t,{A:()=>u});var a=r(8732);r(82015);var l=r(94696);let o=({position:e,onCloseClick:t,children:r})=>(0,a.jsx)(l.InfoWindow,{position:e,onCloseClick:t,children:(0,a.jsx)("div",{children:r})}),n="labels.text.fill",s="labels.text.stroke",i="road.highway",p="geometry.stroke",c=[{elementType:"geometry",stylers:[{color:"#1d2c4d"}]},{elementType:n,stylers:[{color:"#8ec3b9"}]},{elementType:s,stylers:[{color:"#1a3646"}]},{featureType:"administrative",elementType:"geometry",stylers:[{visibility:"off"}]},{featureType:"administrative.country",elementType:p,stylers:[{color:"#4b6878"}]},{featureType:"administrative.land_parcel",elementType:"labels",stylers:[{visibility:"off"}]},{featureType:"administrative.land_parcel",elementType:n,stylers:[{color:"#64779e"}]},{featureType:"administrative.province",elementType:p,stylers:[{color:"#4b6878"}]},{featureType:"landscape.man_made",elementType:p,stylers:[{color:"#334e87"}]},{featureType:"landscape.natural",elementType:"geometry",stylers:[{color:"#023e58"}]},{featureType:"poi",stylers:[{visibility:"off"}]},{featureType:"poi",elementType:"geometry",stylers:[{color:"#283d6a"}]},{featureType:"poi",elementType:"labels.text",stylers:[{visibility:"off"}]},{featureType:"poi",elementType:n,stylers:[{color:"#6f9ba5"}]},{featureType:"poi",elementType:s,stylers:[{color:"#1d2c4d"}]},{featureType:"poi.park",elementType:"geometry.fill",stylers:[{color:"#023e58"}]},{featureType:"poi.park",elementType:n,stylers:[{color:"#3C7680"}]},{featureType:"road",stylers:[{visibility:"off"}]},{featureType:"road",elementType:"geometry",stylers:[{color:"#304a7d"}]},{featureType:"road",elementType:"labels.icon",stylers:[{visibility:"off"}]},{featureType:"road",elementType:n,stylers:[{color:"#98a5be"}]},{featureType:"road",elementType:s,stylers:[{color:"#1d2c4d"}]},{featureType:i,elementType:"geometry",stylers:[{color:"#2c6675"}]},{featureType:i,elementType:p,stylers:[{color:"#255763"}]},{featureType:i,elementType:n,stylers:[{color:"#b0d5ce"}]},{featureType:i,elementType:s,stylers:[{color:"#023e58"}]},{featureType:"road.local",elementType:"labels",stylers:[{visibility:"off"}]},{featureType:"transit",stylers:[{visibility:"off"}]},{featureType:"transit",elementType:n,stylers:[{color:"#98a5be"}]},{featureType:"transit",elementType:s,stylers:[{color:"#1d2c4d"}]},{featureType:"transit.line",elementType:"geometry.fill",stylers:[{color:"#283d6a"}]},{featureType:"transit.station",elementType:"geometry",stylers:[{color:"#3a4762"}]},{featureType:"water",elementType:"geometry",stylers:[{color:"#0e1626"}]},{featureType:"water",elementType:n,stylers:[{color:"#4e6d70"}]}];var y=r(44233),d=r(40691);let u=({markerInfo:e,activeMarker:t,initialCenter:r,children:n,height:s=300,width:i="114%",language:p,zoom:u=1,minZoom:f=1,onClose:m})=>{let{locale:T}=(0,y.useRouter)(),{isLoaded:g,loadError:b}=(0,d._)(),h={width:i,height:"number"==typeof s?`${s}px`:s};return b?(0,a.jsx)("div",{children:"Error loading maps"}):g?(0,a.jsx)("div",{className:"map-container",children:(0,a.jsx)("div",{className:"mapprint",style:{width:i,height:s,position:"relative"},children:(0,a.jsxs)(l.GoogleMap,{mapContainerStyle:h,center:r||{lat:52.520017,lng:13.404195},zoom:u,onLoad:e=>{e.setOptions({styles:c})},options:{minZoom:f,draggable:!0,keyboardShortcuts:!1,streetViewControl:!1,panControl:!1,clickableIcons:!1,mapTypeControl:!1,fullscreenControl:!0},children:[n,e&&t&&t.getPosition&&(0,a.jsx)(o,{position:t.getPosition(),onCloseClick:()=>{console.log("close click"),m?.()},children:e})]})})}):(0,a.jsx)("div",{children:"Loading Maps..."})}},80237:(e,t)=>{Object.defineProperty(t,"A",{enumerable:!0,get:function(){return r}});var r=function(e){return e.PAGES="PAGES",e.PAGES_API="PAGES_API",e.APP_PAGE="APP_PAGE",e.APP_ROUTE="APP_ROUTE",e.IMAGE="IMAGE",e}({})},81413:(e,t)=>{Object.defineProperty(t,"M",{enumerable:!0,get:function(){return function e(t,r){return r in t?t[r]:"then"in t&&"function"==typeof t.then?t.then(t=>e(t,r)):"function"==typeof t&&"default"===r?t:void 0}}})},89364:(e,t,r)=>{r.d(t,{A:()=>o});var a=r(8732);r(82015);var l=r(94696);let o=({name:e="Marker",id:t="",countryId:r="",type:o,icon:n,position:s,onClick:i,title:p,draggable:c=!1})=>s&&"number"==typeof s.lat&&"number"==typeof s.lng?(0,a.jsx)(l.Marker,{position:s,icon:n,title:p||e,draggable:c,onClick:a=>{i&&i({name:e,id:t,countryId:r,type:o,position:s},{position:s,getPosition:()=>s},a)}}):null}};