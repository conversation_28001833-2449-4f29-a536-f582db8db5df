(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[4672],{10703:(e,t,n)=>{"use strict";n.r(t),n.d(t,{default:()=>d});var r=n(37876),a=n(14232),i=n(32890),s=n(21772),o=n(11041),c=n(31753),l=n(34381);let d=e=>{var t,n;let{t:d}=(0,c.Bd)("common"),[u,p]=(0,a.useState)(!1);return(0,r.jsx)(r.Fragment,{children:(0,r.jsxs)(i.A.Item,{eventKey:"0",children:[(0,r.jsxs)(i.A.<PERSON>,{onClick:()=>p(!u),children:[(0,r.jsx)("div",{className:"cardTitle",children:d("LinkedVirtualSpace")}),(0,r.jsx)("div",{className:"cardArrow",children:u?(0,r.jsx)(s.g,{icon:o.EZy,color:"#fff"}):(0,r.jsx)(s.g,{icon:o.QLR,color:"#fff"})})]}),(0,r.jsx)(i.A.Body,{children:(0,r.jsx)(l.A,{id:(null==(n=e.routeData)||null==(t=n.routes)?void 0:t[1])||"",type:"Operation",vspaceData:[],vspaceDataLoading:!1,vspaceDataTotalRows:0,vspaceDataPerPage:10,vspaceDataCurrentPage:1})})]})})}},16341:(e,t,n)=>{"use strict";n.r(t),n.d(t,{default:()=>m});var r=n(37876),a=n(14232),i=n(11041),s=n(21772),o=n(10841),c=n.n(o),l=n(32890),d=n(56970),u=n(37784),p=n(31753);let m=e=>{let{t}=(0,p.Bd)("common"),n="MM-D-YYYY HH:mm:ss",o="MM-D-YYYY",[m,h]=(0,a.useState)(!1);return(0,r.jsx)(r.Fragment,{children:(0,r.jsxs)(l.A.Item,{eventKey:"0",children:[(0,r.jsxs)(l.A.Header,{onClick:()=>h(!m),children:[(0,r.jsx)("div",{className:"cardTitle",children:t("OperationDetails")}),(0,r.jsx)("div",{className:"cardArrow",children:m?(0,r.jsx)(s.g,{icon:i.EZy,color:"#fff"}):(0,r.jsx)(s.g,{icon:i.QLR,color:"#fff"})})]}),(0,r.jsx)(l.A.Body,{children:(0,r.jsxs)(d.A,{className:"operationData",children:[(0,r.jsxs)(u.A,{md:!0,lg:6,sm:12,className:"ps-0",children:[(0,r.jsxs)("p",{children:[(0,r.jsx)("b",{children:t("HazardType")}),":",(0,r.jsx)("span",{children:e.operation.hazard_type?e.operation.hazard_type.title:null})]}),function(e,t){return(0,r.jsxs)("div",{className:"d-flex mb-2 pb-1",children:[(0,r.jsx)("b",{children:e("Hazard")}),":",(0,r.jsx)("span",{children:(0,r.jsx)("ul",{className:"comma-separated",children:t.hazard&&t.hazard.length>=1?t.hazard.map((e,t)=>(0,r.jsx)("li",{children:e.title.en},t)):null})})]})}(t,e.operation),(0,r.jsxs)("p",{children:[(0,r.jsx)("b",{children:t("Syndrome")}),":",(0,r.jsx)("span",{children:e.operation.syndrome?e.operation.syndrome.title:null})]}),(0,r.jsxs)("p",{children:[(0,r.jsx)("b",{children:t("Created")}),":",(0,r.jsx)("span",{children:c()(e.operation.created_at).format(n)})]}),(0,r.jsxs)("p",{children:[(0,r.jsx)("b",{children:t("LastModified")}),":",(0,r.jsx)("span",{children:c()(e.operation.updated_at).format(n)})]})]}),(0,r.jsxs)(u.A,{md:!0,lg:6,sm:12,className:"ps-0",children:[(0,r.jsxs)("p",{children:[(0,r.jsx)("b",{children:t("CountryOrTerritory")}),":",(0,r.jsx)("span",{children:e.operation.country?e.operation.country.title:null})]}),(0,r.jsxs)("p",{children:[(0,r.jsx)("b",{children:t("OperationStatus")}),":",(0,r.jsxs)("span",{children:[" ",e.operation.status.title," "]})]}),(0,r.jsxs)("p",{children:[(0,r.jsx)("b",{children:t("StartDate")}),":",(0,r.jsx)("span",{children:e.operation.start_date?c()(e.operation.start_date).format(o):null})]}),(0,r.jsxs)("p",{children:[(0,r.jsx)("b",{children:t("EndDate")}),":",(0,r.jsx)("span",{children:e.operation.end_date?c()(e.operation.end_date).format(o):null})]})]})]})})]})})}},22352:(e,t,n)=>{"use strict";n.r(t),n.d(t,{canAddOperation:()=>s,canAddOperationForm:()=>o,canEditOperation:()=>c,canEditOperationForm:()=>l,canViewDiscussionUpdate:()=>d,default:()=>u});var r=n(37876);n(14232);var a=n(8178),i=n(59626);let s=(0,a.A)({authenticatedSelector:e=>!!e.permissions&&!!e.permissions.operation&&!!e.permissions.operation["create:any"],wrapperDisplayName:"CanAddOperation"}),o=(0,a.A)({authenticatedSelector:e=>!!e.permissions&&!!e.permissions.operation&&!!e.permissions.operation["create:any"],wrapperDisplayName:"CanAddOperationForm",FailureComponent:()=>(0,r.jsx)(i.default,{})}),c=(0,a.A)({authenticatedSelector:(e,t)=>{if(e.permissions&&e.permissions.operation){if(e.permissions.operation["update:any"])return!0;else if(e.permissions.operation["update:own"]&&t.operation&&t.operation.user&&t.operation.user._id===e.user._id)return!0}return!1},wrapperDisplayName:"CanEditOperation"}),l=(0,a.A)({authenticatedSelector:(e,t)=>{if(e.permissions&&e.permissions.operation){if(e.permissions.operation["update:any"])return!0;else if(e.permissions.operation["update:own"]&&t.operation&&t.operation.user&&t.operation.user._id===e.user._id)return!0}return!1},wrapperDisplayName:"CanEditOperationForm",FailureComponent:()=>(0,r.jsx)(i.default,{})}),d=(0,a.A)({authenticatedSelector:e=>!!e.permissions&&!!e.permissions.update&&!!e.permissions.update["read:any"],wrapperDisplayName:"CanViewDiscussionUpdate"}),u=s},25150:(e,t,n)=>{"use strict";n.r(t),n.d(t,{default:()=>d});var r=n(37876),a=n(14232),i=n(32890),s=n(21772),o=n(11041),c=n(66404),l=n(31753);let d=e=>{let{t}=(0,l.Bd)("common"),[n,d]=(0,a.useState)(!1);return(0,r.jsx)(r.Fragment,{children:(0,r.jsxs)(i.A.Item,{eventKey:"0",children:[(0,r.jsxs)(i.A.Header,{onClick:()=>d(!n),children:[(0,r.jsx)("div",{className:"cardTitle",children:t("Documents")}),(0,r.jsx)("div",{className:"cardArrow",children:n?(0,r.jsx)(s.g,{icon:o.EZy,color:"#fff"}):(0,r.jsx)(s.g,{icon:o.QLR,color:"#fff"})})]}),(0,r.jsxs)(i.A.Body,{children:[(0,r.jsx)(c.A,{loading:e.documentAccoirdianData.documentAccoirdianProps.loading,sortProps:e.documentAccoirdianData.documentAccoirdianProps.sortProps,docs:e.documentAccoirdianData.documentAccoirdianProps.Document||[],docsDescription:e.documentAccoirdianData.operationData.doc_src}),(0,r.jsx)("h6",{className:"mt-3",children:t("DocumentsfromUpdates")}),(0,r.jsx)(c.A,{loading:e.documentAccoirdianData.documentAccoirdianProps.loading,sortProps:e.documentAccoirdianData.documentAccoirdianProps.sortUpdateProps,docs:e.documentAccoirdianData.documentAccoirdianProps.updateDocument||[],docsDescription:e.documentAccoirdianData.operationData.doc_src})]})]})})}},34381:(e,t,n)=>{"use strict";n.d(t,{A:()=>d});var r=n(37876),a=n(14232),i=n(48230),s=n.n(i),o=n(50749),c=n(53718),l=n(31753);let d=e=>{let{t}=(0,l.Bd)("common"),{type:n,id:i}=e,[d,u]=(0,a.useState)([]),[p,m]=(0,a.useState)(!1),[h,x]=(0,a.useState)(0),[j,g]=(0,a.useState)(10),[f]=(0,a.useState)(!1),y={sort:{created_at:"asc"},limit:j,page:1,query:{}},A=[{name:t("Title"),selector:"title",cell:e=>e&&e.title&&e._id?(0,r.jsx)(s(),{href:"/vspace/[...routes]",as:"/vspace/show/".concat(e._id),children:e.title}):""},{name:t("Owner"),selector:"users",cell:e=>e&&e.user&&e.user.firstname?"".concat(e.user.firstname," ").concat(e.user.lastname):""},{name:t("PublicPrivate"),selector:"visibility",cell:e=>e&&e.visibility?"Public":"Private"},{name:t("NumberofMembers"),selector:"members",cell:e=>e&&e.members?e.members.length:"-"}],_=async e=>{m(!0);let t=await c.A.get("stats/get".concat(n,"WithVspace/").concat(i),y);t&&("Operation"===n?u(t.operation):u(t.project),x(t.totalCount),m(!1))},D=async(e,t)=>{y.limit=e,y.page=t,m(!0);let r=await c.A.get("stats/get".concat(n,"WithVspace/").concat(i),y);r&&("Operation"===n?u(r.operation):u(r.project),g(e),m(!1))};return(0,a.useEffect)(()=>{_(y)},[]),(0,r.jsx)("div",{children:(0,r.jsx)(o.A,{columns:A,data:d,totalRows:h,loading:p,resetPaginationToggle:f,handlePerRowsChange:D,handlePageChange:e=>{y.limit=j,y.page=e,_(y)}})})}},50749:(e,t,n)=>{"use strict";n.d(t,{A:()=>c});var r=n(37876);n(14232);var a=n(89773),i=n(31753),s=n(5507);function o(e){let{t}=(0,i.Bd)("common"),n={rowsPerPageText:t("Rowsperpage")},{columns:o,data:c,totalRows:l,resetPaginationToggle:d,subheader:u,subHeaderComponent:p,handlePerRowsChange:m,handlePageChange:h,rowsPerPage:x,defaultRowsPerPage:j,selectableRows:g,loading:f,pagServer:y,onSelectedRowsChange:A,clearSelectedRows:_,sortServer:D,onSort:v,persistTableHead:w,sortFunction:b,...M}=e,S={paginationComponentOptions:n,noDataComponent:t("NoData"),noHeader:!0,columns:o,data:c||[],dense:!0,paginationResetDefaultPage:d,subHeader:u,progressPending:f,subHeaderComponent:p,pagination:!0,paginationServer:y,paginationPerPage:j||10,paginationRowsPerPageOptions:x||[10,15,20,25,30],paginationTotalRows:l,onChangeRowsPerPage:m,onChangePage:h,selectableRows:g,onSelectedRowsChange:A,clearSelectedRows:_,progressComponent:(0,r.jsx)(s.A,{}),sortIcon:(0,r.jsx)("i",{className:"sort-icon fas fa-exchange-alt"}),sortServer:D,onSort:v,sortFunction:b,persistTableHead:w,className:"rki-table"};return(0,r.jsx)(a.Ay,{...S})}o.defaultProps={subHeader:!1,pagination:!0,totalRows:null,pagServer:!0,onSelectedRowsChange:null,clearSelectedRows:!1,sortServer:!1,persistTableHead:!1};let c=o},64990:(e,t,n)=>{"use strict";n.r(t),n.d(t,{default:()=>f});var r=n(37876),a=n(14232),i=n(32890),s=n(56970),o=n(37784),c=n(88659),l=n(21772),d=n(11041),u=n(31753),p=n(48477),m=n(22352),h=n(10703),x=n(16341),j=n(99238),g=n(25150);let f=e=>{let{t}=(0,u.Bd)("common"),[n,f]=(0,a.useState)(!1);console.log(e,"propsdata");let y=()=>{var a;return(0,r.jsxs)(i.A.Item,{eventKey:"0",children:[(0,r.jsxs)(i.A.Header,{onClick:()=>f(!n),children:[(0,r.jsx)("div",{className:"cardTitle",children:t("Discussions")}),(0,r.jsx)("div",{className:"cardArrow",children:n?(0,r.jsx)(l.g,{icon:d.EZy,color:"#fff"}):(0,r.jsx)(l.g,{icon:d.QLR,color:"#fff"})})]}),(0,r.jsx)(i.A.Body,{children:(0,r.jsx)(p.A,{type:"operation",id:(null==e||null==(a=e.routeData)?void 0:a.routes)?e.routeData.routes[1]:null})})]})},A=(0,m.canViewDiscussionUpdate)(()=>(0,r.jsx)(y,{}));return(0,r.jsx)(r.Fragment,{children:(0,r.jsx)(s.A,{children:(0,r.jsxs)(o.A,{className:"operationAccordion",xs:12,children:[(0,r.jsx)(i.A,{children:(0,r.jsx)(x.default,{operation:e.operationData})}),(0,r.jsx)(i.A,{children:(0,r.jsx)(c.default,{operation:e.operationData})}),(0,r.jsx)(i.A,{children:(0,r.jsx)(j.default,{operation:e.operationData})}),(0,r.jsx)(i.A,{children:(0,r.jsx)(g.default,{documentAccoirdianData:e})}),(0,r.jsx)(i.A,{children:(0,r.jsx)(A,{})}),(0,r.jsx)(i.A,{children:(0,r.jsx)(h.default,{routeData:e.routeData})})]})})})}},66404:(e,t,n)=>{"use strict";n.d(t,{A:()=>c});var r=n(37876);n(14232);var a=n(10841),i=n.n(a),s=n(50749),o=n(31753);let c=e=>{let{docs:t,docsDescription:n,sortProps:a,loading:c}=e,l=async(e,t)=>{a({columnSelector:e.selector,sortDirection:t})},{t:d}=(0,o.Bd)("common"),u=[{name:d("FileType"),width:"15%",selector:"extension",cell:e=>e&&e.extension&&e.extension},{name:d("FileName"),width:"25%",selector:"document_title",cell:e=>e&&e.original_name&&(0,r.jsx)("a",{href:"".concat("http://localhost:3001/api/v1","/files/download/").concat(e._id),target:"_blank",children:e.original_name.split(".").slice(0,-1).join(".")}),sortable:!0},{name:d("Description"),selector:"description",cell:e=>e&&e.description&&e.description},{name:d("UploadedDate"),width:"25%",selector:"doc_created_at",cell:e=>e&&e.updated_at&&i()(e.updated_at).format("MM/DD/YYYY"),sortable:!0}];return(0,r.jsx)(s.A,{columns:u,data:t,pagServer:!0,onSort:l,persistTableHead:!0,loading:c})}},84135:function(e,t,n){(function(e){"use strict";function t(e,t,n,r){var a={m:["eine Minute","einer Minute"],h:["eine Stunde","einer Stunde"],d:["ein Tag","einem Tag"],dd:[e+" Tage",e+" Tagen"],w:["eine Woche","einer Woche"],M:["ein Monat","einem Monat"],MM:[e+" Monate",e+" Monaten"],y:["ein Jahr","einem Jahr"],yy:[e+" Jahre",e+" Jahren"]};return t?a[n][0]:a[n][1]}e.defineLocale("de",{months:"Januar_Februar_M\xe4rz_April_Mai_Juni_Juli_August_September_Oktober_November_Dezember".split("_"),monthsShort:"Jan._Feb._M\xe4rz_Apr._Mai_Juni_Juli_Aug._Sep._Okt._Nov._Dez.".split("_"),monthsParseExact:!0,weekdays:"Sonntag_Montag_Dienstag_Mittwoch_Donnerstag_Freitag_Samstag".split("_"),weekdaysShort:"So._Mo._Di._Mi._Do._Fr._Sa.".split("_"),weekdaysMin:"So_Mo_Di_Mi_Do_Fr_Sa".split("_"),weekdaysParseExact:!0,longDateFormat:{LT:"HH:mm",LTS:"HH:mm:ss",L:"DD.MM.YYYY",LL:"D. MMMM YYYY",LLL:"D. MMMM YYYY HH:mm",LLLL:"dddd, D. MMMM YYYY HH:mm"},calendar:{sameDay:"[heute um] LT [Uhr]",sameElse:"L",nextDay:"[morgen um] LT [Uhr]",nextWeek:"dddd [um] LT [Uhr]",lastDay:"[gestern um] LT [Uhr]",lastWeek:"[letzten] dddd [um] LT [Uhr]"},relativeTime:{future:"in %s",past:"vor %s",s:"ein paar Sekunden",ss:"%d Sekunden",m:t,mm:"%d Minuten",h:t,hh:"%d Stunden",d:t,dd:t,w:t,ww:"%d Wochen",M:t,MM:t,y:t,yy:t},dayOfMonthOrdinalParse:/\d{1,2}\./,ordinal:"%d.",week:{dow:1,doy:4}})})(n(10841))},88517:(e,t,n)=>{"use strict";n.r(t),n.d(t,{default:()=>h});var r=n(37876),a=n(48230),i=n.n(a),s=n(82851),o=n.n(s),c=n(39658),l=n(63847),d=n(50749),u=n(31753);let p=e=>{let{networks:t}=e;return t&&t.length>0?(0,r.jsx)("ul",{children:t.map((e,t)=>(0,r.jsx)("li",{children:e.title},t))}):null},m=(0,r.jsxs)(c.A,{id:"popover-basic",children:[(0,r.jsx)(c.A.Header,{as:"h3",className:"text-center",children:"NETWORKS"}),(0,r.jsx)(c.A.Body,{children:(0,r.jsxs)("div",{className:"m-2",children:[(0,r.jsxs)("p",{children:[(0,r.jsx)("b",{children:"EMLab"})," - European Mobile Lab"]}),(0,r.jsxs)("p",{children:[(0,r.jsx)("b",{children:"EMT"})," - Emergency Medical Teams"]}),(0,r.jsxs)("p",{children:[(0,r.jsx)("b",{children:"GHPP"})," - Global Health Protection Program"]}),(0,r.jsxs)("p",{children:[(0,r.jsx)("b",{children:"GOARN"})," - Global Outbreak Alert & Response Network"]}),(0,r.jsxs)("p",{children:[(0,r.jsx)("b",{children:"IANPHI"})," - International Association of National Public Health Institutes"]}),(0,r.jsxs)("p",{children:[(0,r.jsx)("b",{children:"STAKOB"})," - St\xe4ndiger Arbeitskreis der Kompetenz-und Behandlungszentren"]}),(0,r.jsxs)("p",{children:[(0,r.jsx)("b",{children:"WHOCC"}),"- World Health Organization Collaborating Centres"]})]})})]}),h=function(e){let{t}=(0,u.Bd)("common"),{partners:n}=e,a=[{name:t("Organisation"),selector:"title",cell:e=>e&&e.institution?(0,r.jsx)(i(),{href:"/institution/[...routes]",as:"/institution/show/".concat(e.institution._id),children:e.institution.title}):"",sortable:!0},{name:t("Country"),selector:"country",cell:e=>e&&e.institution&&e.institution.address&&e.institution.address.country?(0,r.jsx)(i(),{href:"/country/[...routes]",as:"/country/show/".concat(e.institution.address.country._id),children:e.institution.address.country.title}):"",sortable:!0},{name:t("Type"),selector:"type.title",cell:e=>e.institution&&e.institution.type&&e.institution.type.title?e.institution.type.title:"",sortable:!0},{name:(0,r.jsx)(l.A,{trigger:"click",placement:"right",overlay:m,children:(0,r.jsxs)("span",{children:[t("Network"),"\xa0\xa0\xa0",(0,r.jsx)("i",{className:"fa fa-info-circle",style:{cursor:"pointer"},"aria-hidden":"true"})]})}),selector:t("Networks"),cell:e=>e.institution&&e.institution.networks&&e.institution.networks.length>0?(0,r.jsx)(p,{networks:e.institution.networks}):""}],s=e=>{if(e.institution.address&&e.institution.address.country)return e.institution.address.country&&e.institution.address.country.title?e.institution.address.country.title.toLowerCase():e.institution.address.country.title},c=e=>{if(e.institution.type&&e.institution.type&&e.institution.type.title)return e.institution.type.title.toLowerCase()};return(0,r.jsx)(d.A,{columns:a,data:n,pagServer:!0,persistTableHead:!0,sortFunction:(e,t,n)=>o().orderBy(e,e=>{if("country"===t)s(e);else if("type.title"===t)c(e);else if(e.institution&&e.institution[t])return e.institution[t].toLowerCase()},n)})}},88659:(e,t,n)=>{"use strict";n.r(t),n.d(t,{default:()=>d});var r=n(37876),a=n(11041),i=n(21772),s=n(14232),o=n(32890),c=n(88517),l=n(31753);let d=e=>{let{t}=(0,l.Bd)("common"),[n,d]=(0,s.useState)(!1);return(0,r.jsx)(r.Fragment,{children:(0,r.jsxs)(o.A.Item,{eventKey:"0",children:[(0,r.jsxs)(o.A.Header,{onClick:()=>d(!n),children:[(0,r.jsx)("div",{className:"cardTitle",children:t("Partners")}),(0,r.jsx)("div",{className:"cardArrow",children:n?(0,r.jsx)(i.g,{icon:a.EZy,color:"#fff"}):(0,r.jsx)(i.g,{icon:a.QLR,color:"#fff"})})]}),(0,r.jsx)(o.A.Body,{children:(0,r.jsx)(c.default,{partners:e.operation.partners})})]})})}},99238:(e,t,n)=>{"use strict";n.r(t),n.d(t,{default:()=>d});var r=n(37876),a=n(14232),i=n(32890),s=n(21772),o=n(11041),c=n(31753),l=n(33458);let d=e=>{let{t}=(0,c.Bd)("common"),[n,d]=(0,a.useState)(!1);return(0,r.jsx)(r.Fragment,{children:(0,r.jsxs)(i.A.Item,{eventKey:"0",children:[(0,r.jsxs)(i.A.Header,{onClick:()=>d(!n),children:[(0,r.jsx)("div",{className:"cardTitle",children:t("MediaGallery")}),(0,r.jsx)("div",{className:"cardArrow",children:n?(0,r.jsx)(s.g,{icon:o.EZy,color:"#fff"}):(0,r.jsx)(s.g,{icon:o.QLR,color:"#fff"})})]}),(0,r.jsx)(i.A.Body,{children:(0,r.jsx)(l.A,{gallery:e.operation.images,imageSource:e.operation.images_src})})]})})}}}]);
//# sourceMappingURL=4672-eed4b28a487ffdec.js.map