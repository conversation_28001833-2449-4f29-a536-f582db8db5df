"use strict";(()=>{var e={};e.id=1107,e.ids=[636,1107,3220],e.modules={123:e=>{e.exports=require("dom-helpers/removeEventListener")},1332:e=>{e.exports=require("react-custom-scrollbars-2")},1428:e=>{e.exports=import("axios")},1680:e=>{e.exports=require("@restart/ui/utils")},1735:(e,r,t)=>{t.a(e,async(e,s)=>{try{t.r(r),t.d(r,{config:()=>h,default:()=>x,getServerSideProps:()=>q,getStaticPaths:()=>m,getStaticProps:()=>c,reportWebVitals:()=>g,routeModule:()=>j,unstable_getServerProps:()=>f,unstable_getServerSideProps:()=>M,unstable_getStaticParams:()=>b,unstable_getStaticPaths:()=>v,unstable_getStaticProps:()=>S});var o=t(63885),a=t(80237),i=t(81413),p=t(9616),u=t.n(p),n=t(72386),l=t(81279),d=e([n,l]);[n,l]=d.then?(await d)():d;let x=(0,i.M)(l,"default"),c=(0,i.M)(l,"getStaticProps"),m=(0,i.M)(l,"getStaticPaths"),q=(0,i.M)(l,"getServerSideProps"),h=(0,i.M)(l,"config"),g=(0,i.M)(l,"reportWebVitals"),S=(0,i.M)(l,"unstable_getStaticProps"),v=(0,i.M)(l,"unstable_getStaticPaths"),b=(0,i.M)(l,"unstable_getStaticParams"),f=(0,i.M)(l,"unstable_getServerProps"),M=(0,i.M)(l,"unstable_getServerSideProps"),j=new o.PagesRouteModule({definition:{kind:a.A.PAGES,page:"/updates/CalendarEventForm",pathname:"/updates/CalendarEventForm",bundlePath:"",filename:""},components:{App:n.default,Document:u()},userland:l});s()}catch(e){s(e)}})},1919:e=>{e.exports=require("react-transition-group/Transition")},3892:e=>{e.exports=require("classnames")},4048:e=>{e.exports=require("react-truncate")},6009:e=>{e.exports=require("dom-helpers/ownerDocument")},6952:e=>{e.exports=require("@restart/ui/Modal")},7374:e=>{e.exports=require("@restart/ui/Dropdown")},8732:e=>{e.exports=require("react/jsx-runtime")},9653:e=>{e.exports=require("@restart/ui/Overlay")},11242:e=>{e.exports=require("redux-persist/integration/react")},11688:e=>{e.exports=require("@restart/hooks/useWillUnmount")},12388:e=>{e.exports=require("dom-helpers/addEventListener")},13364:e=>{e.exports=require("redux-saga/effects")},14062:e=>{e.exports=import("react-redux")},14078:e=>{e.exports=import("swr")},14332:e=>{e.exports=require("uncontrollable")},16029:e=>{e.exports=require("react-dropzone")},16116:e=>{e.exports=require("invariant")},18622:e=>{e.exports=require("yup")},19121:e=>{e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},22326:e=>{e.exports=require("react-dom")},22541:e=>{e.exports=require("redux-persist/lib/storage")},25303:e=>{e.exports=require("@restart/ui/NavItem")},26324:e=>{e.exports=require("warning")},27825:e=>{e.exports=require("lodash")},27910:e=>{e.exports=require("stream")},28217:e=>{e.exports=require("@restart/ui/DropdownItem")},29021:e=>{e.exports=require("fs")},29294:e=>{e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},29628:e=>{e.exports=require("dom-helpers/addClass")},29780:e=>{e.exports=require("react-datepicker")},29825:e=>{e.exports=require("prop-types")},29841:e=>{e.exports=require("dom-helpers/hasClass")},30362:e=>{e.exports=require("@restart/ui/DropdownToggle")},33873:e=>{e.exports=require("path")},36653:e=>{e.exports=require("nprogress")},36955:e=>{e.exports=require("@restart/hooks/useBreakpoint")},37766:e=>{e.exports=require("@restart/hooks/useMergedRefs")},39756:e=>{e.exports=import("redux")},40051:e=>{e.exports=require("dom-helpers/removeClass")},40361:e=>{e.exports=require("next/dist/compiled/next-server/pages.runtime.prod.js")},42893:e=>{e.exports=import("react-hot-toast")},43294:e=>{e.exports=require("formik")},50009:e=>{e.exports=require("@restart/ui/DropdownMenu")},54131:e=>{e.exports=import("@fortawesome/free-solid-svg-icons")},57664:e=>{e.exports=require("dom-helpers/scrollbarSize")},58070:(e,r,t)=>{t.d(r,{A:()=>i});var s=t(8732);t(82015);var o=t(29780),a=t.n(o);let i=e=>(0,s.jsx)(a(),{...e})},58928:e=>{e.exports=require("dom-helpers/canUseDOM")},59717:e=>{e.exports=require("@restart/ui/DropdownContext")},60560:e=>{e.exports=require("dom-helpers/contains")},63033:e=>{e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},65447:e=>{e.exports=require("@restart/ui/Button")},67364:e=>{e.exports=require("@restart/hooks/useCallbackRef")},68455:e=>{e.exports=import("redux-saga")},69722:e=>{e.exports=require("es6-promise")},74075:e=>{e.exports=require("zlib")},74716:e=>{e.exports=require("moment")},74987:e=>{e.exports=require("@restart/ui/ModalManager")},78097:e=>{e.exports=require("next-redux-saga")},78634:e=>{e.exports=require("@restart/ui/Anchor")},81279:(e,r,t)=>{t.a(e,async(e,s)=>{try{t.r(r),t.d(r,{default:()=>m});var o=t(8732),a=t(82015),i=t(7082),p=t(49481),u=t(83551),n=t(59549),l=t(25782),d=t(58070),x=t(88751),c=e([l]);l=(c.then?(await c)():c)[0];let m=e=>{let[,r]=(0,a.useState)(!1),{validation:t,onChangeDate:s,startDate:c,endDate:m}=e,{t:q}=(0,x.useTranslation)("common");(0,a.useEffect)(()=>{null==m?r(!1):r(!0)},[m]);let h=r=>{e.getId(r)},g=r=>{e.getSourceCollection(r)};return(0,o.jsxs)(i.A,{className:"formCard",fluid:!0,children:[(0,o.jsx)(p.A,{className:"header-block",lg:12,children:(0,o.jsx)("h6",{children:(0,o.jsx)("span",{children:q("update.Date")})})}),(0,o.jsxs)(u.A,{children:[(0,o.jsxs)(p.A,{children:[(0,o.jsxs)(n.A.Group,{children:[(0,o.jsx)(n.A.Label,{className:"d-block required-field",children:q("update.StartDate")}),(0,o.jsx)(d.A,{selected:c,minDate:new Date,showTimeSelect:!0,timeIntervals:15,onChange:e=>s(e,"startDate"),placeholderText:q("update.Selectadate"),dateFormat:"MMMM d, yyyy h:mm aa"})]}),!0===t.startDate&&!c&&(0,o.jsx)("p",{style:{color:"red"},children:q("update.Pleaseenterthestartdate")})]}),(0,o.jsxs)(p.A,{children:[(0,o.jsxs)(n.A.Group,{children:[(0,o.jsx)(n.A.Label,{className:"d-block required-field",children:q("update.EndDate")}),(0,o.jsx)(d.A,{selected:m,showTimeSelect:!0,timeIntervals:15,onChange:e=>s(e,"endDate"),placeholderText:q("update.Selectadate"),minDate:c,dateFormat:"MMMM d, yyyy h:mm aa"})]}),!0===t.startDate&&!m&&(0,o.jsxs)("p",{style:{color:"red"},children:[q("update.Pleaseentertheenddate")," "]})]})]}),(0,o.jsx)(l.default,{data:e.data,srcText:e.imgSrc,getId:e=>h(e),getSourceCollection:e=>g(e)})]})};s()}catch(e){s(e)}})},81366:e=>{e.exports=require("redux-auth-wrapper/connectedAuthWrapper")},81521:e=>{e.exports=require("dom-helpers/querySelectorAll")},81895:e=>{e.exports=require("@restart/hooks/useEventCallback")},82015:e=>{e.exports=require("react")},82053:e=>{e.exports=require("@fortawesome/react-fontawesome")},86842:e=>{e.exports=require("@restart/ui/SelectableContext")},87571:e=>{e.exports=require("dom-helpers/transitionEnd")},88751:e=>{e.exports=require("next-i18next")},93787:e=>{e.exports=require("redux-persist")},94947:e=>{e.exports=require("@restart/hooks/useTimeout")},96196:e=>{e.exports=require("next-redux-wrapper")},98320:e=>{e.exports=require("@restart/hooks/useIsomorphicEffect")},99460:e=>{e.exports=require("dom-helpers/css")}};var r=require("../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[6089,9216,9616,2386,4764],()=>t(1735));module.exports=s})();