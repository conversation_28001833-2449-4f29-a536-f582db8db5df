{"version": 3, "file": "static/chunks/pages/adminsettings/institutionNetworks/institutionNetworkTable-35a46191fd5c6cb5.js", "mappings": "iQA2KA,MAhKiCA,IAC7B,GAAM,CAACC,EAAWC,EAAe,CAAGC,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAC,EAAE,EACzC,EAAGC,EAAW,CAAGD,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAC,CA8JG,GA7J7B,CAACE,EAAWC,EAAa,CAAGH,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAC,GACrC,CAACI,EAASC,EAAW,CAAGL,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAC,IACjC,CAACM,EAAaC,EAAS,CAAGP,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,EAAC,GACnC,CAACQ,EAA0BC,EAA4B,CAAGT,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAC,CAAC,GACpEU,EAAY,IAAMH,EAAS,IAC3B,GAAEI,CAAC,CAAE,CAAGC,CAAAA,EAAAA,EAAAA,EAAAA,CAAcA,CAAC,UAIvBC,EACF,WAACC,EAAAA,CAAOA,CAAAA,CAACC,GAAG,0BACR,UAACD,EAAAA,CAAOA,CAACE,MAAM,EAACC,GAAG,KAAKC,UAAU,uBAAc,aAGhD,UAACJ,EAAAA,CAAOA,CAACK,IAAI,WACT,WAACC,MAAAA,CAAIF,UAAU,gBACX,WAACG,IAAAA,WACG,UAACC,IAAAA,UAAE,UAAS,4BAEhB,WAACD,IAAAA,WACG,UAACC,IAAAA,UAAE,QAAO,gCAEd,WAACD,IAAAA,WACG,UAACC,IAAAA,UAAE,SAAQ,yCAEf,WAACD,IAAAA,WACG,UAACC,IAAAA,UAAE,UAAS,iDAEhB,WAACD,IAAAA,WACG,UAACC,IAAAA,UAAE,WAAU,uEAEjB,WAACD,IAAAA,WACG,UAACC,IAAAA,UAAE,WAAU,uEAEjB,WAACD,IAAAA,WACG,UAACC,IAAAA,UAAE,UAAS,+DAgB1BC,EAAU,CACZ,CACIC,KAXJ,UAACC,EAAAA,CAAcA,CAAAA,CAACC,QAAQ,QAAQC,UAAU,QAAQC,QAASf,WACvD,WAACgB,OAAAA,WACIlB,EAAE,SAAS,eACZ,UAACmB,IAAAA,CAAEZ,UAAU,oBAAoBa,MAAO,CAAEC,OAAQ,SAAU,EAAGC,cAAY,cAS/EC,SAAU,OACd,EACA,CACIV,KAAMb,EAAE,UACRuB,SAAU,GACVC,KAAM,GACF,WAACf,MAAAA,WACG,UAACgB,IAAIA,CAACC,KAAK,6BAA6BpB,GAAI,OAAvCmB,oCAAwF,OAANE,EAAEC,GAAG,WAExF,UAACT,IAAAA,CAAEZ,UAAU,uBAEV,OAEP,UAACsB,IAAAA,CAAEC,QAAS,IAAMC,EAAWJ,YACzB,UAACR,IAAAA,CAAEZ,UAAU,8BAI7B,EACH,CACKyB,EAA2B,CAC7BC,KAAM,CAAEC,MAAO,KAAM,EACrBC,MAAO1C,EACP2C,KAAM,EACNC,MAAO,CAAC,CACZ,EAEAC,CAAAA,EAAAA,EAAAA,SAAAA,CAASA,CAAC,KACNC,GACJ,EAAG,EAAE,EAEL,IAAMA,EAA4B,UAC9BjD,GAAW,GACX,IAAMkD,EAAW,MAAMC,EAAAA,CAAUA,CAACC,GAAG,CAAC,sBAAuBV,GACzDQ,GAAYA,EAASG,IAAI,EAAIH,EAASG,IAAI,CAACC,MAAM,CAAG,GAAG,CACvDxD,EAAeoD,EAASG,IAAI,EAC5BnD,EAAagD,EAASK,UAAU,EAChCvD,GAAW,GAEnB,EAQMwD,EAAsB,MAAOC,EAAiBX,KAChDJ,EAAyBG,KAAK,CAAGY,EACjCf,EAAyBI,IAAI,CAAGA,EAChC9C,GAAW,GACX,IAAMkD,EAAW,MAAMC,EAAAA,CAAUA,CAACC,GAAG,CAAC,sBAAuBV,GACzDQ,GAAYA,EAASG,IAAI,EAAIH,EAASG,IAAI,CAACC,MAAM,CAAG,GAAG,CACvDxD,EAAeoD,EAASG,IAAI,EAC5BjD,EAAWqD,GACXzD,GAAW,GAEnB,EAEM0D,EAAe,UACjB,GAAI,CACA,MAAMP,EAAAA,CAAUA,CAACQ,MAAM,CAAC,uBAAgD,OAAzBpD,IAC/C0C,IACA3C,GAAS,GACTsD,EAAAA,EAAKA,CAACC,OAAO,CAACnD,EAAE,yEACpB,CAAE,MAAOoD,EAAO,CACZF,EAAAA,EAAKA,CAACE,KAAK,CAACpD,EAAE,mEAClB,CACJ,EAEM+B,EAAa,MAAOsB,IACtBvD,EAA4BuD,EAAIzB,GAAG,EACnChC,EAAS,GACb,EAEA,MACI,WAACa,MAAAA,WACG,WAAC6C,EAAAA,CAAKA,CAAAA,CAACC,KAAM5D,EAAa6D,OAAQzD,YAC9B,UAACuD,EAAAA,CAAKA,CAACjD,MAAM,EAACoD,WAAW,aACrB,UAACH,EAAAA,CAAKA,CAACI,KAAK,WAAE1D,EAAE,gDAEpB,UAACsD,EAAAA,CAAKA,CAAC9C,IAAI,WAAER,EAAE,4CACf,WAACsD,EAAAA,CAAKA,CAACK,MAAM,YACT,UAACC,EAAAA,CAAMA,CAAAA,CAACC,QAAQ,YAAY/B,QAAS/B,WAChCC,EAAE,YAEP,UAAC4D,EAAAA,CAAMA,CAAAA,CAACC,QAAQ,UAAU/B,QAASkB,WAC9BhD,EAAE,eAKf,UAAC8D,EAAAA,CAAQA,CAAAA,CACLlD,QAASA,EACT+B,KAAMxD,EACNI,UAAWA,EACXwE,WAAW,EACXjB,oBAAqBA,EACrBkB,iBAzDc5B,CAyDI4B,GAxD1BhC,EAAyBG,KAAK,CAAG1C,EACjCuC,EAAyBI,IAAI,CAAGA,EAChCG,GACJ,MAyDJ,6GCrIA,SAASuB,EAASG,CAAoB,EACpC,GAAM,CAAEjE,CAAC,CAAE,CAAGC,CAAAA,EAAAA,EAAAA,EAAAA,CAAcA,CAAC,UACvBiE,EAA6B,CACjCC,gBAAiBnE,EAAE,cACnB,EACI,SACJY,CAAO,MACP+B,CAAI,WACJpD,CAAS,uBACT6E,CAAqB,WACrBC,CAAS,oBACTC,CAAkB,qBAClBxB,CAAmB,kBACnBkB,CAAgB,aAChBO,CAAW,oBACXC,CAAkB,gBAClBC,CAAc,SACdC,CAAO,CACPX,WAAS,CACTY,sBAAoB,mBACpBC,CAAiB,YACjBC,CAAU,QACVC,CAAM,kBACNC,CAAgB,CAChBC,cAAY,CAEZ,CADA,EACGC,EACJ,CAAGhB,EAGEiB,EAAiB,4BACrBhB,EACAiB,gBAAiBnF,EAAE,IAP0C,MAQ7DoF,UAAU,UACVxE,EACA+B,KAAMA,GAAQ,EAAE,CAChB0C,OAAO,EACPC,2BAA4BlB,EAC5BmB,UAAWlB,EACXmB,gBAAiBd,qBACjBJ,EACAmB,YAAY,EACZC,iBAAkB3B,EAClB4B,kBAAmBnB,GAA0C,GAC7DoB,eADwCpB,cACVD,GAA4B,CAAC,GAAI,GAAI,GAAI,CAA3BA,EAA+B,GAAG,CAC9EsB,oBAAqBtG,EACrBuG,oBAAqBhD,EACrBiD,aAAc/B,iBACdS,uBACAE,oBACAC,EACAoB,kBAAmB,UAACC,EAAAA,CAAYA,CAAAA,CAAAA,GAChCC,SAAU,UAAC/E,IAAAA,CAAEZ,UAAU,6CACvBsE,SACAC,eACAE,mBACAD,EACAxE,UAAW,WACb,EACA,MACE,UAAC4F,EAAAA,EAASA,CAAAA,CAAE,GAAGjB,CAAc,EAEjC,CAEApB,EAASsC,YAAY,CAAG,CACtBb,WAAW,EACXE,YAAY,EACZlG,UAAW,KACXwE,WAAW,EACXY,qBAAsB,KACtBC,mBAAmB,EACnBC,YAAY,EACZE,kBAAkB,CACpB,EAEA,MAAejB,QAAQA,EAAC,SC/GxB,4CACA,6DACA,WACA,OAAe,EAAQ,KAAkF,CACzG,EACA,SAFsB", "sources": ["webpack://_N_E/./pages/adminsettings/institutionNetworks/institutionNetworkTable.tsx", "webpack://_N_E/./components/common/RKITable.tsx", "webpack://_N_E/?b714"], "sourcesContent": ["//Import Library\r\nimport Link from \"next/link\";\r\nimport { useState, useEffect } from \"react\";\r\nimport { Modal, Button, Popover, OverlayTrigger } from \"react-bootstrap\";\r\nimport toast from 'react-hot-toast';\r\n\r\n//Import services/components\r\nimport RKITable from \"../../../components/common/RKITable\";\r\nimport apiService from \"../../../services/apiService\";\r\nimport { useTranslation } from 'next-i18next';\r\n\r\nconst InstitutionNetworkTable = (_props: any) => {\r\n    const [tabledata, setDataToTable] = useState([]);\r\n    const [, setLoading] = useState(false);\r\n    const [totalRows, setTotalRows] = useState(0);\r\n    const [perPage, setPerPage] = useState(10);\r\n    const [isModalShow, setModal] = useState(false);\r\n    const [selectInstitutionNetwork, setSelectInstitutionNetwork] = useState({});\r\n    const modalHide = () => setModal(false);\r\n    const { t } = useTranslation('common');\r\n\r\n\r\n    // For network popover\r\n    const Networkpopover = (\r\n        <Popover id=\"popover-basic\">\r\n            <Popover.Header as=\"h3\" className=\"text-center\">\r\n                NETWORKS\r\n            </Popover.Header>\r\n            <Popover.Body>\r\n                <div className=\"m-2\">\r\n                    <p>\r\n                        <b>EMLab</b> - European Mobile Lab\r\n                    </p>\r\n                    <p>\r\n                        <b>EMT</b> - Emergency Medical Teams\r\n                    </p>\r\n                    <p>\r\n                        <b>GHPP</b> - Global Health Protection Program\r\n                    </p>\r\n                    <p>\r\n                        <b>GOARN</b> - Global Outbreak Alert & Response Network\r\n                    </p>\r\n                    <p>\r\n                        <b>IANPHI</b> - International Association of National Public Health Institutes\r\n                    </p>\r\n                    <p>\r\n                        <b>STAKOB</b> - Ständiger Arbeitskreis der Kompetenz-und Behandlungszentren\r\n                    </p>\r\n                    <p>\r\n                        <b>WHOCC</b>- World Health Organization Collaborating Centres\r\n                    </p>\r\n                </div>\r\n            </Popover.Body>\r\n        </Popover>\r\n    );\r\n    const icons = (\r\n        <OverlayTrigger trigger=\"click\" placement=\"right\" overlay={Networkpopover}>\r\n            <span>\r\n                {t(\"Title\")}&nbsp;&nbsp;&nbsp;\r\n                <i className=\"fa fa-info-circle\" style={{ cursor: \"pointer\" }} aria-hidden=\"true\"></i>\r\n            </span>\r\n        </OverlayTrigger>\r\n    );\r\n    // End\r\n\r\n    const columns = [\r\n        {\r\n            name: icons,\r\n            selector: \"title\",\r\n        },\r\n        {\r\n            name: t(\"action\"),\r\n            selector: \"\",\r\n            cell: (d: any) => (\r\n                <div>\r\n                    <Link href=\"/adminsettings/[...routes]\" as={`/adminsettings/edit_institution_network/${d._id}`}>\r\n\r\n                        <i className=\"icon fas fa-edit\" />\r\n\r\n                    </Link>\r\n                    &nbsp;\r\n                    <a onClick={() => userAction(d)}>\r\n                        <i className=\"icon fas fa-trash-alt\" />\r\n                    </a>\r\n                </div>\r\n            ),\r\n        },\r\n    ];\r\n    const institutionNetworkParams = {\r\n        sort: { title: \"asc\" },\r\n        limit: perPage,\r\n        page: 1,\r\n        query: {},\r\n    };\r\n\r\n    useEffect(() => {\r\n        getInstitutionNetworkData();\r\n    }, []);\r\n\r\n    const getInstitutionNetworkData = async () => {\r\n        setLoading(true);\r\n        const response = await apiService.get(\"/institutionnetwork\", institutionNetworkParams);\r\n        if (response && response.data && response.data.length > 0) {\r\n            setDataToTable(response.data);\r\n            setTotalRows(response.totalCount);\r\n            setLoading(false);\r\n        }\r\n    };\r\n\r\n    const handlePageChange = (page: any) => {\r\n        institutionNetworkParams.limit = perPage;\r\n        institutionNetworkParams.page = page;\r\n        getInstitutionNetworkData();\r\n    };\r\n\r\n    const handlePerRowsChange = async (newPerPage: any, page: any) => {\r\n        institutionNetworkParams.limit = newPerPage;\r\n        institutionNetworkParams.page = page;\r\n        setLoading(true);\r\n        const response = await apiService.get(\"/institutionnetwork\", institutionNetworkParams);\r\n        if (response && response.data && response.data.length > 0) {\r\n            setDataToTable(response.data);\r\n            setPerPage(newPerPage);\r\n            setLoading(false);\r\n        }\r\n    };\r\n\r\n    const modalConfirm = async () => {\r\n        try {\r\n            await apiService.remove(`/institutionnetwork/${selectInstitutionNetwork}`);\r\n            getInstitutionNetworkData();\r\n            setModal(false);\r\n            toast.success(t(\"adminsetting.Organisationnetworks.Table.orgNetworkDeletedSuccessfully\"));\r\n        } catch (error) {\r\n            toast.error(t(\"adminsetting.Organisationnetworks.Table.errorDeletingOrgNetwork\"));\r\n        }\r\n    };\r\n\r\n    const userAction = async (row: any) => {\r\n        setSelectInstitutionNetwork(row._id);\r\n        setModal(true);\r\n    };\r\n\r\n    return (\r\n        <div>\r\n            <Modal show={isModalShow} onHide={modalHide}>\r\n                <Modal.Header closeButton>\r\n                    <Modal.Title>{t(\"adminsetting.Organisationnetworks.Delete\")}</Modal.Title>\r\n                </Modal.Header>\r\n                <Modal.Body>{t(\"adminsetting.Organisationnetworks.sure\")}</Modal.Body>\r\n                <Modal.Footer>\r\n                    <Button variant=\"secondary\" onClick={modalHide}>\r\n                        {t(\"cancel\")}\r\n                    </Button>\r\n                    <Button variant=\"primary\" onClick={modalConfirm}>\r\n                        {t(\"yes\")}\r\n                    </Button>\r\n                </Modal.Footer>\r\n            </Modal>\r\n\r\n            <RKITable\r\n                columns={columns}\r\n                data={tabledata}\r\n                totalRows={totalRows}\r\n                pagServer={true}\r\n                handlePerRowsChange={handlePerRowsChange}\r\n                handlePageChange={handlePageChange}\r\n            />\r\n        </div>\r\n    );\r\n};\r\nexport default InstitutionNetworkTable;\r\n", "//Import Library\r\nimport React from 'react'\r\nimport DataTable  from 'react-data-table-component';\r\n\r\n//Import services/components\r\nimport { useTranslation } from 'next-i18next';\r\nimport CustomLoader from './CustomLoader';\r\n\r\ninterface RKITableProps {\r\n  columns: any[];\r\n  data: any[];\r\n  totalRows?: number;\r\n  resetPaginationToggle?: boolean;\r\n  subheader?: boolean;\r\n  subHeaderComponent?: React.ReactNode;\r\n  handlePerRowsChange?: (newPerPage: number, page: number) => void;\r\n  handlePageChange?: (page: number) => void;\r\n  rowsPerPage?: number[];\r\n  defaultRowsPerPage?: number;\r\n  selectableRows?: boolean;\r\n  loading?: boolean;\r\n  pagServer?: boolean;\r\n  onSelectedRowsChange?: (selectedRows: any) => void;\r\n  clearSelectedRows?: boolean;\r\n  sortServer?: boolean;\r\n  onSort?: (column: any, sortDirection: string) => void;\r\n  persistTableHead?: boolean;\r\n  sortFunction?: (rows: any[], field: any, direction: 'asc' | 'desc') => any[];\r\n  noHeader?: boolean;\r\n  dense?: boolean;\r\n  pagination?: boolean;\r\n  paginationServer?: boolean;\r\n  paginationTotalRows?: number;\r\n  subHeaderAlign?: string;\r\n  [key: string]: any;\r\n}\r\n\r\nfunction RKITable(props: RKITableProps) {\r\n  const { t } = useTranslation('common');\r\n  const paginationComponentOptions = {\r\n    rowsPerPageText: t('Rowsperpage'),\r\n    };\r\n  const {\r\n    columns,\r\n    data,\r\n    totalRows,\r\n    resetPaginationToggle,\r\n    subheader,\r\n    subHeaderComponent,\r\n    handlePerRowsChange,\r\n    handlePageChange,\r\n    rowsPerPage,\r\n    defaultRowsPerPage,\r\n    selectableRows,\r\n    loading,\r\n    pagServer,\r\n    onSelectedRowsChange,\r\n    clearSelectedRows,\r\n    sortServer,\r\n    onSort,\r\n    persistTableHead,\r\n    sortFunction,\r\n    // Filter out any unknown props that might cause DOM warnings\r\n    ...otherProps\r\n  } = props;\r\n\r\n  // Only pass known DataTable props to avoid DOM warnings\r\n  const dataTableProps = {\r\n    paginationComponentOptions,\r\n    noDataComponent: t(\"NoData\"),\r\n    noHeader: true,\r\n    columns,\r\n    data: data || [],\r\n    dense: true,\r\n    paginationResetDefaultPage: resetPaginationToggle,\r\n    subHeader: subheader,\r\n    progressPending: loading,\r\n    subHeaderComponent,\r\n    pagination: true,\r\n    paginationServer: pagServer,\r\n    paginationPerPage: defaultRowsPerPage ? defaultRowsPerPage : 10,\r\n    paginationRowsPerPageOptions: rowsPerPage ? rowsPerPage : [10, 15, 20, 25, 30],\r\n    paginationTotalRows: totalRows,\r\n    onChangeRowsPerPage: handlePerRowsChange,\r\n    onChangePage: handlePageChange,\r\n    selectableRows,\r\n    onSelectedRowsChange,\r\n    clearSelectedRows,\r\n    progressComponent: <CustomLoader/>,\r\n    sortIcon: <i className=\"sort-icon fas fa-exchange-alt\" />,\r\n    sortServer,\r\n    onSort,\r\n    sortFunction,\r\n    persistTableHead,\r\n    className: \"rki-table\"\r\n  };\r\n  return (\r\n    <DataTable {...dataTableProps} />\r\n  )\r\n};\r\n\r\nRKITable.defaultProps = {\r\n  subHeader: false,\r\n  pagination: true,\r\n  totalRows: null,\r\n  pagServer: true,\r\n  onSelectedRowsChange: null,\r\n  clearSelectedRows: false,\r\n  sortServer: false,\r\n  persistTableHead: false\r\n}\r\n\r\nexport default RKITable;\r\n\r\n", "\n    (window.__NEXT_P = window.__NEXT_P || []).push([\n      \"/adminsettings/institutionNetworks/institutionNetworkTable\",\n      function () {\n        return require(\"private-next-pages/adminsettings/institutionNetworks/institutionNetworkTable.tsx\");\n      }\n    ]);\n    if(module.hot) {\n      module.hot.dispose(function () {\n        window.__NEXT_P.push([\"/adminsettings/institutionNetworks/institutionNetworkTable\"])\n      });\n    }\n  "], "names": ["_props", "tabledata", "setDataToTable", "useState", "setLoading", "totalRows", "setTotalRows", "perPage", "setPerPage", "isModalShow", "setModal", "selectInstitutionNetwork", "setSelectInstitutionNetwork", "modalHide", "t", "useTranslation", "Networkpopover", "Popover", "id", "Header", "as", "className", "Body", "div", "p", "b", "columns", "name", "OverlayTrigger", "trigger", "placement", "overlay", "span", "i", "style", "cursor", "aria-hidden", "selector", "cell", "Link", "href", "d", "_id", "a", "onClick", "userAction", "institutionNetworkParams", "sort", "title", "limit", "page", "query", "useEffect", "getInstitutionNetworkData", "response", "apiService", "get", "data", "length", "totalCount", "handlePerRowsChange", "newPerPage", "modalConfirm", "remove", "toast", "success", "error", "row", "Modal", "show", "onHide", "closeButton", "Title", "Footer", "<PERSON><PERSON>", "variant", "RKITable", "pagServer", "handlePageChange", "props", "paginationComponentOptions", "rowsPerPageText", "resetPaginationToggle", "subheader", "subHeaderComponent", "rowsPerPage", "defaultRowsPerPage", "selectableRows", "loading", "onSelectedRowsChange", "clearSelectedRows", "sortServer", "onSort", "persistTableHead", "sortFunction", "otherProps", "dataTableProps", "noDataComponent", "<PERSON><PERSON><PERSON><PERSON>", "dense", "paginationResetDefaultPage", "subHeader", "progressPending", "pagination", "paginationServer", "paginationPerPage", "paginationRowsPerPageOptions", "paginationTotalRows", "onChangeRowsPerPage", "onChangePage", "progressComponent", "CustomLoader", "sortIcon", "DataTable", "defaultProps"], "sourceRoot": "", "ignoreList": []}