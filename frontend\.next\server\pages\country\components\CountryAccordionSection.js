(()=>{var e={};e.id=825,e.ids=[636,825,3220],e.modules={123:e=>{"use strict";e.exports=require("dom-helpers/removeEventListener")},1332:e=>{"use strict";e.exports=require("react-custom-scrollbars-2")},1428:e=>{"use strict";e.exports=import("axios")},1680:e=>{"use strict";e.exports=require("@restart/ui/utils")},1919:e=>{"use strict";e.exports=require("react-transition-group/Transition")},3892:e=>{"use strict";e.exports=require("classnames")},4048:e=>{"use strict";e.exports=require("react-truncate")},6009:e=>{"use strict";e.exports=require("dom-helpers/ownerDocument")},6952:e=>{"use strict";e.exports=require("@restart/ui/Modal")},7374:e=>{"use strict";e.exports=require("@restart/ui/Dropdown")},8732:e=>{"use strict";e.exports=require("react/jsx-runtime")},9653:e=>{"use strict";e.exports=require("@restart/ui/Overlay")},11242:e=>{"use strict";e.exports=require("redux-persist/integration/react")},11688:e=>{"use strict";e.exports=require("@restart/hooks/useWillUnmount")},12388:e=>{"use strict";e.exports=require("dom-helpers/addEventListener")},13364:e=>{"use strict";e.exports=require("redux-saga/effects")},14062:e=>{"use strict";e.exports=import("react-redux")},14078:e=>{"use strict";e.exports=import("swr")},14332:e=>{"use strict";e.exports=require("uncontrollable")},16116:e=>{"use strict";e.exports=require("invariant")},18622:e=>{"use strict";e.exports=require("yup")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},22326:e=>{"use strict";e.exports=require("react-dom")},22541:e=>{"use strict";e.exports=require("redux-persist/lib/storage")},25303:e=>{"use strict";e.exports=require("@restart/ui/NavItem")},26324:e=>{"use strict";e.exports=require("warning")},27825:e=>{"use strict";e.exports=require("lodash")},27910:e=>{"use strict";e.exports=require("stream")},28217:e=>{"use strict";e.exports=require("@restart/ui/DropdownItem")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},29628:e=>{"use strict";e.exports=require("dom-helpers/addClass")},29825:e=>{"use strict";e.exports=require("prop-types")},29841:e=>{"use strict";e.exports=require("dom-helpers/hasClass")},30362:e=>{"use strict";e.exports=require("@restart/ui/DropdownToggle")},33873:e=>{"use strict";e.exports=require("path")},36653:e=>{"use strict";e.exports=require("nprogress")},36955:e=>{"use strict";e.exports=require("@restart/hooks/useBreakpoint")},37766:e=>{"use strict";e.exports=require("@restart/hooks/useMergedRefs")},38609:e=>{"use strict";e.exports=require("react-data-table-component")},39756:e=>{"use strict";e.exports=import("redux")},40051:e=>{"use strict";e.exports=require("dom-helpers/removeClass")},40361:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/pages.runtime.prod.js")},42447:(e,t,r)=>{"use strict";r.a(e,async(e,s)=>{try{r.d(t,{A:()=>p});var i=r(8732),a=r(82015),o=r(81149),n=r(82053),c=r(54131),u=r(91353),l=r(88751);r(72025);var d=e([c]);c=(d.then?(await d)():d)[0];let p=e=>{let{t}=(0,l.useTranslation)("common"),[r,s]=(0,a.useState)([]),d=e=>{let r=/(http|https):\/\/(\w+:{0,1}\w*)?(\S+)(:[0-9]+)?(\/|\/([\w#!:.?+=&%!\-\/]))?/.test(e.description);return(0,i.jsxs)("div",{className:"carousel-legend",children:[(0,i.jsxs)("p",{className:"lead",children:[(0,i.jsx)("b",{children:t("Filename")})," ",e.originalName||"No Name found"]}),e.description&&(0,i.jsxs)("div",{className:"source_link",children:[(0,i.jsx)("p",{children:(0,i.jsx)("b",{children:t("Source")})}),r?(0,i.jsxs)("div",{children:[(0,i.jsx)(n.FontAwesomeIcon,{icon:c.faLink,size:"1x",color:"#999",className:"me-1"}),(0,i.jsx)("a",{className:"source_link",href:e.description,target:"_blank",rel:"noopener noreferrer",children:e.description})]}):(0,i.jsx)("div",{children:(0,i.jsx)("p",{className:"ps-0 py-0",style:{wordBreak:"break-all"},children:e.description})})]}),e.downloadLink&&(0,i.jsxs)(u.A,{className:"btn btn-success mt-2 btn--download",href:e.downloadLink,children:[t("Download"),(0,i.jsx)(n.FontAwesomeIcon,{icon:c.faDownload,size:"1x",className:"ms-1"})]})]})};return(0,a.useEffect)(()=>{let t=[];e&&e.gallery&&Array.isArray(e.gallery)&&e.gallery.map((r,s)=>{let i,a=r&&r.name.split(".").pop();switch(a){case"JPG":case"jpg":case"jpeg":case"png":i=`http://localhost:3001/api/v1/image/show/${r._id}`;break;case"pdf":i="/images/fileIcons/pdfFile.png";break;case"docx":i="/images/fileIcons/wordFile.png";break;case"xls":case"xlsx":i="/images/fileIcons/xlsFile.png";break;default:i="/images/fileIcons/otherFile.png"}let o=("docx"===a||"pdf"===a||"xls"===a||"xlsx"===a)&&`http://localhost:3001/api/v1/files/download/${r._id}`,n=`${r&&r.original_name?r.original_name:"No Name found"}`,c=e.imageSource&&Array.isArray(e.imageSource)&&e.imageSource.length>0?e.imageSource[s]:"";t.push({src:i,description:c,originalName:n,downloadLink:o})}),s(t)},[e]),(0,i.jsx)("div",{children:r&&0===r.length?(0,i.jsx)("div",{className:"border border-info my-3 mx-0",children:(0,i.jsx)("p",{className:"d-flex d-flex justify-content-center p-2 m-0",children:t("NoFilesFound!")})}):(0,i.jsx)(o.Carousel,{showThumbs:!0,showStatus:!0,showIndicators:!0,infiniteLoop:!0,useKeyboardArrows:!0,autoPlay:!1,stopOnHover:!0,swipeable:!0,dynamicHeight:!1,emulateTouch:!0,renderThumbs:()=>r.map((e,t)=>(0,i.jsx)("img",{src:e.src,alt:`Thumbnail ${t+1}`,style:{width:"60px",height:"60px",objectFit:"cover"}},t)),children:r.map((e,t)=>(0,i.jsxs)("div",{children:[(0,i.jsx)("img",{src:e.src,alt:e.originalName||"Gallery image",style:{maxHeight:"500px",objectFit:"contain"}}),d(e)]},t))})})};s()}catch(e){s(e)}})},42893:e=>{"use strict";e.exports=import("react-hot-toast")},43294:e=>{"use strict";e.exports=require("formik")},43438:(e,t,r)=>{"use strict";r.r(t),r.d(t,{canViewDiscussionUpdate:()=>i,default:()=>a}),r(82015);var s=r(81366);let i=r.n(s)()({authenticatedSelector:e=>!!e.permissions&&!!e.permissions.update&&!!e.permissions.update["read:any"],wrapperDisplayName:"CanViewDiscussionUpdate"}),a=i},46396:(e,t,r)=>{"use strict";r.a(e,async(e,s)=>{try{r.r(t),r.d(t,{default:()=>u});var i=r(8732);r(82015);var a=r(93024),o=r(42447),n=r(88751),c=e([o]);o=(c.then?(await c)():c)[0];let u=e=>{let{t}=(0,n.useTranslation)("common");return(0,i.jsx)(a.A,{defaultActiveKey:"0",children:(0,i.jsxs)(a.A.Item,{eventKey:"0",children:[(0,i.jsx)(a.A.Header,{children:(0,i.jsx)("div",{className:"cardTitle",children:t("MediaGallery")})}),(0,i.jsx)(a.A.Body,{children:(0,i.jsx)(o.A,{gallery:e.images,imageSource:e.imgSrc})})]})})};s()}catch(e){s(e)}})},50009:e=>{"use strict";e.exports=require("@restart/ui/DropdownMenu")},54131:e=>{"use strict";e.exports=import("@fortawesome/free-solid-svg-icons")},56077:(e,t,r)=>{"use strict";r.a(e,async(e,s)=>{try{r.r(t),r.d(t,{default:()=>p});var i=r(8732);r(82015);var a=r(93024),o=r(67640),n=r(46396),c=r(90717),u=r(73888),l=r(43438),d=e([o,n,u]);[o,n,u]=d.then?(await d)():d;let p=e=>{let t=(0,l.default)(()=>(0,i.jsx)(u.default,{...e.prop}));return(0,i.jsxs)(i.Fragment,{children:[(0,i.jsx)(a.A,{defaultActiveKey:"0",className:"countryAccordionNew",children:(0,i.jsx)(o.default,{...e})}),(0,i.jsx)(a.A,{className:"countryAccordionNew",children:(0,i.jsx)(n.default,{...e})}),(0,i.jsx)(a.A,{className:"countryAccordionNew",children:(0,i.jsx)(c.default,{...e})}),(0,i.jsx)(a.A,{className:"countryAccordionNew",children:(0,i.jsx)(t,{...e})})]})};s()}catch(e){s(e)}})},56084:(e,t,r)=>{"use strict";r.d(t,{A:()=>u});var s=r(8732);r(82015);var i=r(38609),a=r.n(i),o=r(88751),n=r(30370);function c(e){let{t}=(0,o.useTranslation)("common"),r={rowsPerPageText:t("Rowsperpage")},{columns:i,data:c,totalRows:u,resetPaginationToggle:l,subheader:d,subHeaderComponent:p,handlePerRowsChange:x,handlePageChange:m,rowsPerPage:h,defaultRowsPerPage:g,selectableRows:f,loading:j,pagServer:y,onSelectedRowsChange:A,clearSelectedRows:q,sortServer:v,onSort:w,persistTableHead:b,sortFunction:S,...P}=e,N={paginationComponentOptions:r,noDataComponent:t("NoData"),noHeader:!0,columns:i,data:c||[],dense:!0,paginationResetDefaultPage:l,subHeader:d,progressPending:j,subHeaderComponent:p,pagination:!0,paginationServer:y,paginationPerPage:g||10,paginationRowsPerPageOptions:h||[10,15,20,25,30],paginationTotalRows:u,onChangeRowsPerPage:x,onChangePage:m,selectableRows:f,onSelectedRowsChange:A,clearSelectedRows:q,progressComponent:(0,s.jsx)(n.A,{}),sortIcon:(0,s.jsx)("i",{className:"sort-icon fas fa-exchange-alt"}),sortServer:v,onSort:w,sortFunction:S,persistTableHead:b,className:"rki-table"};return(0,s.jsx)(a(),{...N})}c.defaultProps={subHeader:!1,pagination:!0,totalRows:null,pagServer:!0,onSelectedRowsChange:null,clearSelectedRows:!1,sortServer:!1,persistTableHead:!1};let u=c},57664:e=>{"use strict";e.exports=require("dom-helpers/scrollbarSize")},58928:e=>{"use strict";e.exports=require("dom-helpers/canUseDOM")},59717:e=>{"use strict";e.exports=require("@restart/ui/DropdownContext")},60560:e=>{"use strict";e.exports=require("dom-helpers/contains")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},65447:e=>{"use strict";e.exports=require("@restart/ui/Button")},67364:e=>{"use strict";e.exports=require("@restart/hooks/useCallbackRef")},67640:(e,t,r)=>{"use strict";r.a(e,async(e,s)=>{try{r.r(t),r.d(t,{default:()=>p});var i=r(8732);r(82015);var a=r(93024),o=r(7082),n=r(83551),c=r(49481),u=r(67666),l=r(88751),d=e([u]);u=(d.then?(await d)():d)[0];let p=e=>{let{t}=(0,l.useTranslation)("common");return(0,i.jsx)(a.A,{defaultActiveKey:"0",children:(0,i.jsxs)(a.A.Item,{eventKey:"0",children:[(0,i.jsx)(a.A.Header,{children:(0,i.jsx)("div",{className:"cardTitle",children:t("Organisation")})}),(0,i.jsx)(a.A.Body,{children:(0,i.jsx)(o.A,{fluid:!0,children:(0,i.jsx)(n.A,{children:(0,i.jsx)(c.A,{children:(0,i.jsx)(u.default,{...e.prop})})})})})]})})};s()}catch(e){s(e)}})},67666:(e,t,r)=>{"use strict";r.a(e,async(e,s)=>{try{r.r(t),r.d(t,{default:()=>p});var i=r(8732),a=r(82015),o=r(19918),n=r.n(o),c=r(56084),u=r(63487),l=r(88751),d=e([u]);u=(d.then?(await d)():d)[0];let p=function(e){let[t,r]=(0,a.useState)([]),[,s]=(0,a.useState)(!1),[o,d]=(0,a.useState)(0),[p]=(0,a.useState)(10),x=e&&e.routes?e.routes[1]:null,[m,h]=(0,a.useState)(null),{t:g,i18n:f}=(0,l.useTranslation)("common"),j=f.language,y={sort:{},limit:p,page:1,instiTable:!0,query:{},languageCode:j},A=[{name:g("Organisation"),selector:"title",cell:e=>(0,i.jsx)(n(),{href:"/institution/[...routes]",as:`/institution/show/${e._id}`,children:e.title}),sortable:!0,maxWidth:"200px"},{name:g("ContactName"),selector:"contact_name",cell:e=>e.user?e.user.username:"",maxWidth:"200px"},{name:g("Expertise"),selector:"expertise",maxWidth:"200px"},{name:g("Region"),selector:"address.region",maxWidth:"200px"}],q=async(e,t)=>{s(!0),y.sort={[e.selector]:t};let r={sort:{[e.selector]:t},limit:p,page:1,instiTable:!0,query:{}};h(r),v(r)},v=async e=>{s(!0),0==Object.keys(e.sort).length&&(e.sort={created_at:"desc"});let t=await u.A.get(`/country/${x}/institution`,e);s(!0),t&&t.data&&t.data.length>0&&(t.data.forEach((e,r)=>{t.data[r].expertise=e.expertise.map(e=>e.title).join(", "),t.data[r].address.region=e.address.region.map(e=>e.title).join(", ")}),r(t.data),d(t.totalCount)),s(!1)},w=async(e,t)=>{y.limit=e,y.page=t,m&&(y.sort=m.sort),v(y)};return(0,i.jsx)(c.A,{columns:A,data:t,totalRows:o,handlePerRowsChange:w,handlePageChange:e=>{y.limit=p,y.page=e,m&&(y.sort=m.sort),v(y)},persistTableHead:!0,onSort:q})};s()}catch(e){s(e)}})},68455:e=>{"use strict";e.exports=import("redux-saga")},69722:e=>{"use strict";e.exports=require("es6-promise")},72025:()=>{},73888:(e,t,r)=>{"use strict";r.a(e,async(e,s)=>{try{r.r(t),r.d(t,{default:()=>u});var i=r(8732);r(82015);var a=r(93024),o=r(82491),n=r(88751),c=e([o]);o=(c.then?(await c)():c)[0];let u=e=>{let{t}=(0,n.useTranslation)("common");return(0,i.jsx)(a.A,{defaultActiveKey:"1",children:(0,i.jsxs)(a.A.Item,{eventKey:"1",children:[(0,i.jsx)(a.A.Header,{children:(0,i.jsx)("div",{className:"cardTitle",children:t("Discussion")})}),(0,i.jsx)(a.A.Body,{children:(0,i.jsx)(o.A,{type:"country",id:e?.routes?e.routes[1]:null})})]})})};s()}catch(e){s(e)}})},74075:e=>{"use strict";e.exports=require("zlib")},74716:e=>{"use strict";e.exports=require("moment")},74987:e=>{"use strict";e.exports=require("@restart/ui/ModalManager")},78097:e=>{"use strict";e.exports=require("next-redux-saga")},78634:e=>{"use strict";e.exports=require("@restart/ui/Anchor")},80237:(e,t)=>{"use strict";Object.defineProperty(t,"A",{enumerable:!0,get:function(){return r}});var r=function(e){return e.PAGES="PAGES",e.PAGES_API="PAGES_API",e.APP_PAGE="APP_PAGE",e.APP_ROUTE="APP_ROUTE",e.IMAGE="IMAGE",e}({})},81149:e=>{"use strict";e.exports=require("react-responsive-carousel")},81366:e=>{"use strict";e.exports=require("redux-auth-wrapper/connectedAuthWrapper")},81413:(e,t)=>{"use strict";Object.defineProperty(t,"M",{enumerable:!0,get:function(){return function e(t,r){return r in t?t[r]:"then"in t&&"function"==typeof t.then?t.then(t=>e(t,r)):"function"==typeof t&&"default"===r?t:void 0}}})},81521:e=>{"use strict";e.exports=require("dom-helpers/querySelectorAll")},81895:e=>{"use strict";e.exports=require("@restart/hooks/useEventCallback")},82015:e=>{"use strict";e.exports=require("react")},82053:e=>{"use strict";e.exports=require("@fortawesome/react-fontawesome")},82879:(e,t,r)=>{"use strict";r.a(e,async(e,s)=>{try{r.r(t),r.d(t,{config:()=>g,default:()=>p,getServerSideProps:()=>h,getStaticPaths:()=>m,getStaticProps:()=>x,reportWebVitals:()=>f,routeModule:()=>w,unstable_getServerProps:()=>q,unstable_getServerSideProps:()=>v,unstable_getStaticParams:()=>A,unstable_getStaticPaths:()=>y,unstable_getStaticProps:()=>j});var i=r(63885),a=r(80237),o=r(81413),n=r(9616),c=r.n(n),u=r(72386),l=r(56077),d=e([u,l]);[u,l]=d.then?(await d)():d;let p=(0,o.M)(l,"default"),x=(0,o.M)(l,"getStaticProps"),m=(0,o.M)(l,"getStaticPaths"),h=(0,o.M)(l,"getServerSideProps"),g=(0,o.M)(l,"config"),f=(0,o.M)(l,"reportWebVitals"),j=(0,o.M)(l,"unstable_getStaticProps"),y=(0,o.M)(l,"unstable_getStaticPaths"),A=(0,o.M)(l,"unstable_getStaticParams"),q=(0,o.M)(l,"unstable_getServerProps"),v=(0,o.M)(l,"unstable_getServerSideProps"),w=new i.PagesRouteModule({definition:{kind:a.A.PAGES,page:"/country/components/CountryAccordionSection",pathname:"/country/components/CountryAccordionSection",bundlePath:"",filename:""},components:{App:u.default,Document:c()},userland:l});s()}catch(e){s(e)}})},86842:e=>{"use strict";e.exports=require("@restart/ui/SelectableContext")},86843:e=>{"use strict";e.exports=require("moment/locale/de")},87571:e=>{"use strict";e.exports=require("dom-helpers/transitionEnd")},88751:e=>{"use strict";e.exports=require("next-i18next")},90717:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>n});var s=r(8732);r(82015);var i=r(93024),a=r(88751),o=r(97377);let n=e=>{let{t}=(0,a.useTranslation)("common");return(0,s.jsx)(i.A,{defaultActiveKey:"0",children:(0,s.jsxs)(i.A.Item,{eventKey:"0",children:[(0,s.jsx)(i.A.Header,{children:(0,s.jsx)("div",{className:"cardTitle",children:t("Documents")})}),(0,s.jsxs)(i.A.Body,{children:[(0,s.jsx)(o.A,{loading:e.loading,sortProps:e.updateSort,docs:e.document,docsDescription:e.docSrc}),(0,s.jsx)("h6",{className:"mt-3",children:t("DocumentsfromUpdates")}),(0,s.jsx)(o.A,{loading:e.loading,sortProps:e.updateDocumentSort,docs:e.updateDocument,docsDescription:e.docSrc})]})]})})}},93787:e=>{"use strict";e.exports=require("redux-persist")},94947:e=>{"use strict";e.exports=require("@restart/hooks/useTimeout")},96196:e=>{"use strict";e.exports=require("next-redux-wrapper")},97377:(e,t,r)=>{"use strict";r.d(t,{A:()=>c});var s=r(8732);r(82015);var i=r(74716),a=r.n(i),o=r(56084),n=r(88751);let c=({docs:e,docsDescription:t,sortProps:r,loading:i})=>{let c=async(e,t)=>{r({columnSelector:e.selector,sortDirection:t})},{t:u}=(0,n.useTranslation)("common"),l=[{name:u("FileType"),width:"15%",selector:"extension",cell:e=>e&&e.extension&&e.extension},{name:u("FileName"),width:"25%",selector:"document_title",cell:e=>e&&e.original_name&&(0,s.jsx)("a",{href:`http://localhost:3001/api/v1/files/download/${e._id}`,target:"_blank",children:e.original_name.split(".").slice(0,-1).join(".")}),sortable:!0},{name:u("Description"),selector:"description",cell:e=>e&&e.description&&e.description},{name:u("UploadedDate"),width:"25%",selector:"doc_created_at",cell:e=>e&&e.updated_at&&a()(e.updated_at).format("MM/DD/YYYY"),sortable:!0}];return(0,s.jsx)(o.A,{columns:l,data:e,pagServer:!0,onSort:c,persistTableHead:!0,loading:i})}},98320:e=>{"use strict";e.exports=require("@restart/hooks/useIsomorphicEffect")},99460:e=>{"use strict";e.exports=require("dom-helpers/css")}};var t=require("../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[6089,9216,9616,2386,2491],()=>r(82879));module.exports=s})();