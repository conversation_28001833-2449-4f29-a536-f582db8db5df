"use strict";(()=>{var e={};e.id=1834,e.ids=[636,1834,3220],e.modules={123:e=>{e.exports=require("dom-helpers/removeEventListener")},1332:e=>{e.exports=require("react-custom-scrollbars-2")},1428:e=>{e.exports=import("axios")},1680:e=>{e.exports=require("@restart/ui/utils")},1919:e=>{e.exports=require("react-transition-group/Transition")},3892:e=>{e.exports=require("classnames")},4048:e=>{e.exports=require("react-truncate")},6009:e=>{e.exports=require("dom-helpers/ownerDocument")},6952:e=>{e.exports=require("@restart/ui/Modal")},7374:e=>{e.exports=require("@restart/ui/Dropdown")},8732:e=>{e.exports=require("react/jsx-runtime")},9653:e=>{e.exports=require("@restart/ui/Overlay")},11242:e=>{e.exports=require("redux-persist/integration/react")},11688:e=>{e.exports=require("@restart/hooks/useWillUnmount")},12388:e=>{e.exports=require("dom-helpers/addEventListener")},13364:e=>{e.exports=require("redux-saga/effects")},14062:e=>{e.exports=import("react-redux")},14078:e=>{e.exports=import("swr")},14332:e=>{e.exports=require("uncontrollable")},16116:e=>{e.exports=require("invariant")},18622:e=>{e.exports=require("yup")},19121:e=>{e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},22326:e=>{e.exports=require("react-dom")},22541:e=>{e.exports=require("redux-persist/lib/storage")},25303:e=>{e.exports=require("@restart/ui/NavItem")},26324:e=>{e.exports=require("warning")},27825:e=>{e.exports=require("lodash")},27910:e=>{e.exports=require("stream")},28217:e=>{e.exports=require("@restart/ui/DropdownItem")},29021:e=>{e.exports=require("fs")},29294:e=>{e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},29628:e=>{e.exports=require("dom-helpers/addClass")},29825:e=>{e.exports=require("prop-types")},29841:e=>{e.exports=require("dom-helpers/hasClass")},30362:e=>{e.exports=require("@restart/ui/DropdownToggle")},33873:e=>{e.exports=require("path")},36653:e=>{e.exports=require("nprogress")},36955:e=>{e.exports=require("@restart/hooks/useBreakpoint")},37766:e=>{e.exports=require("@restart/hooks/useMergedRefs")},38368:(e,r,t)=>{t.a(e,async(e,s)=>{try{t.r(r),t.d(r,{default:()=>m});var a=t(8732),n=t(19918),i=t.n(n),o=t(82015),u=t(12403),l=t(91353),p=t(42893),d=t(56084),x=t(63487),g=t(88751),c=e([p,x]);[p,x]=c.then?(await c)():c;let m=e=>{let{t:r,i18n:t}=(0,g.useTranslation)("common");t.language&&t.language;let[s,n]=(0,o.useState)([]),[,c]=(0,o.useState)(!1),[m,q]=(0,o.useState)(0),[h,P]=(0,o.useState)(10),[b,S]=(0,o.useState)(!1),[f,v]=(0,o.useState)({}),A=()=>S(!1),w=[{name:r("adminsetting.landing.table.Title"),selector:"title",cell:e=>e.title},{name:r("adminsetting.landing.table.Enabled"),selector:"isEnabled",cell:e=>e.isEnabled?"Yes":"No"},{name:r("adminsetting.landing.table.Action"),selector:"",cell:e=>(0,a.jsxs)("div",{children:[(0,a.jsx)(i(),{href:"/adminsettings/[...routes]",as:`/adminsettings/edit_landing/${e._id}`,children:(0,a.jsx)("i",{className:"icon fas fa-edit"})}),"\xa0"," "]})}],y={sort:{title:"asc"},limit:h,page:1};(0,o.useEffect)(()=>{j()},[]);let j=async()=>{c(!0);let e=await x.A.get("/landingPage",y);e&&(n(e.data),q(e.totalCount),c(!1))},E=async(e,r)=>{y.limit=e,y.page=r,c(!0);let t=await x.A.get("/landingPage",y);t&&t.data&&t.data.length>0&&(n(t.data),P(e),c(!1))},M=async()=>{try{await x.A.remove(`/landingPage/${f}`),j(),S(!1),p.default.success(r("adminsetting.landing.table.landingDeletedSuccessfully"))}catch(e){p.default.error(r("adminsetting.landing.table.errorDeletingLanding"))}};return(0,a.jsxs)("div",{children:[(0,a.jsxs)(u.A,{show:b,onHide:A,children:[(0,a.jsx)(u.A.Header,{closeButton:!0,children:(0,a.jsx)(u.A.Title,{children:r("adminsetting.landing.table.DeleteEditableContent")})}),(0,a.jsx)(u.A.Body,{children:r("adminsetting.landing.table.AreyousurewanttodeletethisEditableContent?")}),(0,a.jsxs)(u.A.Footer,{children:[(0,a.jsx)(l.A,{variant:"secondary",onClick:A,children:r("adminsetting.landing.table.Cancel")}),(0,a.jsx)(l.A,{variant:"primary",onClick:M,children:r("adminsetting.landing.table.Yes")})]})]}),(0,a.jsx)(d.A,{columns:w,data:s,totalRows:m,pagServer:!0,handlePerRowsChange:E,handlePageChange:e=>{y.limit=h,y.page=e,j()}})]})};s()}catch(e){s(e)}})},38609:e=>{e.exports=require("react-data-table-component")},39756:e=>{e.exports=import("redux")},40051:e=>{e.exports=require("dom-helpers/removeClass")},40361:e=>{e.exports=require("next/dist/compiled/next-server/pages.runtime.prod.js")},42893:e=>{e.exports=import("react-hot-toast")},43294:e=>{e.exports=require("formik")},50009:e=>{e.exports=require("@restart/ui/DropdownMenu")},56084:(e,r,t)=>{t.d(r,{A:()=>l});var s=t(8732);t(82015);var a=t(38609),n=t.n(a),i=t(88751),o=t(30370);function u(e){let{t:r}=(0,i.useTranslation)("common"),t={rowsPerPageText:r("Rowsperpage")},{columns:a,data:u,totalRows:l,resetPaginationToggle:p,subheader:d,subHeaderComponent:x,handlePerRowsChange:g,handlePageChange:c,rowsPerPage:m,defaultRowsPerPage:q,selectableRows:h,loading:P,pagServer:b,onSelectedRowsChange:S,clearSelectedRows:f,sortServer:v,onSort:A,persistTableHead:w,sortFunction:y,...j}=e,E={paginationComponentOptions:t,noDataComponent:r("NoData"),noHeader:!0,columns:a,data:u||[],dense:!0,paginationResetDefaultPage:p,subHeader:d,progressPending:P,subHeaderComponent:x,pagination:!0,paginationServer:b,paginationPerPage:q||10,paginationRowsPerPageOptions:m||[10,15,20,25,30],paginationTotalRows:l,onChangeRowsPerPage:g,onChangePage:c,selectableRows:h,onSelectedRowsChange:S,clearSelectedRows:f,progressComponent:(0,s.jsx)(o.A,{}),sortIcon:(0,s.jsx)("i",{className:"sort-icon fas fa-exchange-alt"}),sortServer:v,onSort:A,sortFunction:y,persistTableHead:w,className:"rki-table"};return(0,s.jsx)(n(),{...E})}u.defaultProps={subHeader:!1,pagination:!0,totalRows:null,pagServer:!0,onSelectedRowsChange:null,clearSelectedRows:!1,sortServer:!1,persistTableHead:!1};let l=u},56583:(e,r,t)=>{t.a(e,async(e,s)=>{try{t.r(r),t.d(r,{config:()=>q,default:()=>x,getServerSideProps:()=>m,getStaticPaths:()=>c,getStaticProps:()=>g,reportWebVitals:()=>h,routeModule:()=>A,unstable_getServerProps:()=>f,unstable_getServerSideProps:()=>v,unstable_getStaticParams:()=>S,unstable_getStaticPaths:()=>b,unstable_getStaticProps:()=>P});var a=t(63885),n=t(80237),i=t(81413),o=t(9616),u=t.n(o),l=t(72386),p=t(38368),d=e([l,p]);[l,p]=d.then?(await d)():d;let x=(0,i.M)(p,"default"),g=(0,i.M)(p,"getStaticProps"),c=(0,i.M)(p,"getStaticPaths"),m=(0,i.M)(p,"getServerSideProps"),q=(0,i.M)(p,"config"),h=(0,i.M)(p,"reportWebVitals"),P=(0,i.M)(p,"unstable_getStaticProps"),b=(0,i.M)(p,"unstable_getStaticPaths"),S=(0,i.M)(p,"unstable_getStaticParams"),f=(0,i.M)(p,"unstable_getServerProps"),v=(0,i.M)(p,"unstable_getServerSideProps"),A=new a.PagesRouteModule({definition:{kind:n.A.PAGES,page:"/adminsettings/landingPage/landingPageTable",pathname:"/adminsettings/landingPage/landingPageTable",bundlePath:"",filename:""},components:{App:l.default,Document:u()},userland:p});s()}catch(e){s(e)}})},57664:e=>{e.exports=require("dom-helpers/scrollbarSize")},58928:e=>{e.exports=require("dom-helpers/canUseDOM")},59717:e=>{e.exports=require("@restart/ui/DropdownContext")},60560:e=>{e.exports=require("dom-helpers/contains")},63033:e=>{e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},65447:e=>{e.exports=require("@restart/ui/Button")},67364:e=>{e.exports=require("@restart/hooks/useCallbackRef")},68455:e=>{e.exports=import("redux-saga")},69722:e=>{e.exports=require("es6-promise")},74075:e=>{e.exports=require("zlib")},74716:e=>{e.exports=require("moment")},74987:e=>{e.exports=require("@restart/ui/ModalManager")},78097:e=>{e.exports=require("next-redux-saga")},78634:e=>{e.exports=require("@restart/ui/Anchor")},80237:(e,r)=>{Object.defineProperty(r,"A",{enumerable:!0,get:function(){return t}});var t=function(e){return e.PAGES="PAGES",e.PAGES_API="PAGES_API",e.APP_PAGE="APP_PAGE",e.APP_ROUTE="APP_ROUTE",e.IMAGE="IMAGE",e}({})},81366:e=>{e.exports=require("redux-auth-wrapper/connectedAuthWrapper")},81413:(e,r)=>{Object.defineProperty(r,"M",{enumerable:!0,get:function(){return function e(r,t){return t in r?r[t]:"then"in r&&"function"==typeof r.then?r.then(r=>e(r,t)):"function"==typeof r&&"default"===t?r:void 0}}})},81521:e=>{e.exports=require("dom-helpers/querySelectorAll")},81895:e=>{e.exports=require("@restart/hooks/useEventCallback")},82015:e=>{e.exports=require("react")},86842:e=>{e.exports=require("@restart/ui/SelectableContext")},87571:e=>{e.exports=require("dom-helpers/transitionEnd")},88751:e=>{e.exports=require("next-i18next")},93787:e=>{e.exports=require("redux-persist")},94947:e=>{e.exports=require("@restart/hooks/useTimeout")},96196:e=>{e.exports=require("next-redux-wrapper")},98320:e=>{e.exports=require("@restart/hooks/useIsomorphicEffect")},99460:e=>{e.exports=require("dom-helpers/css")}};var r=require("../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[6089,9216,9616,2386],()=>t(56583));module.exports=s})();