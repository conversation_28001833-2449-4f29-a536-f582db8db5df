(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[5561],{33458:(e,n,t)=>{"use strict";t.d(n,{A:()=>u});var a=t(37876),r=t(14232),s=t(65418),l=t(21772),i=t(11041),c=t(60282),o=t(31753);t(57637);let u=e=>{let{t:n}=(0,o.Bd)("common"),[t,u]=(0,r.useState)([]),d=e=>{let t=/(http|https):\/\/(\w+:{0,1}\w*)?(\S+)(:[0-9]+)?(\/|\/([\w#!:.?+=&%!\-\/]))?/.test(e.description);return(0,a.jsxs)("div",{className:"carousel-legend",children:[(0,a.jsxs)("p",{className:"lead",children:[(0,a.jsx)("b",{children:n("Filename")})," ",e.originalName||"No Name found"]}),e.description&&(0,a.jsxs)("div",{className:"source_link",children:[(0,a.jsx)("p",{children:(0,a.jsx)("b",{children:n("Source")})}),t?(0,a.jsxs)("div",{children:[(0,a.jsx)(l.g,{icon:i.CQO,size:"1x",color:"#999",className:"me-1"}),(0,a.jsx)("a",{className:"source_link",href:e.description,target:"_blank",rel:"noopener noreferrer",children:e.description})]}):(0,a.jsx)("div",{children:(0,a.jsx)("p",{className:"ps-0 py-0",style:{wordBreak:"break-all"},children:e.description})})]}),e.downloadLink&&(0,a.jsxs)(c.A,{className:"btn btn-success mt-2 btn--download",href:e.downloadLink,children:[n("Download"),(0,a.jsx)(l.g,{icon:i.cbP,size:"1x",className:"ms-1"})]})]})};return(0,r.useEffect)(()=>{let n=[];e&&e.gallery&&Array.isArray(e.gallery)&&e.gallery.map((t,a)=>{let r,s=t&&t.name.split(".").pop();switch(s){case"JPG":case"jpg":case"jpeg":case"png":r="".concat("http://localhost:3001/api/v1","/image/show/").concat(t._id);break;case"pdf":r="/images/fileIcons/pdfFile.png";break;case"docx":r="/images/fileIcons/wordFile.png";break;case"xls":case"xlsx":r="/images/fileIcons/xlsFile.png";break;default:r="/images/fileIcons/otherFile.png"}let l=("docx"===s||"pdf"===s||"xls"===s||"xlsx"===s)&&"".concat("http://localhost:3001/api/v1","/files/download/").concat(t._id),i="".concat(t&&t.original_name?t.original_name:"No Name found"),c=e.imageSource&&Array.isArray(e.imageSource)&&e.imageSource.length>0?e.imageSource[a]:"";n.push({src:r,description:c,originalName:i,downloadLink:l})}),u(n)},[e]),(0,a.jsx)("div",{children:t&&0===t.length?(0,a.jsx)("div",{className:"border border-info my-3 mx-0",children:(0,a.jsx)("p",{className:"d-flex d-flex justify-content-center p-2 m-0",children:n("NoFilesFound!")})}):(0,a.jsx)(s.FN,{showThumbs:!0,showStatus:!0,showIndicators:!0,infiniteLoop:!0,useKeyboardArrows:!0,autoPlay:!1,stopOnHover:!0,swipeable:!0,dynamicHeight:!1,emulateTouch:!0,renderThumbs:()=>t.map((e,n)=>(0,a.jsx)("img",{src:e.src,alt:"Thumbnail ".concat(n+1),style:{width:"60px",height:"60px",objectFit:"cover"}},n)),children:t.map((e,n)=>(0,a.jsxs)("div",{children:[(0,a.jsx)("img",{src:e.src,alt:e.originalName||"Gallery image",style:{maxHeight:"500px",objectFit:"contain"}}),d(e)]},n))})})}},50650:(e,n,t)=>{"use strict";t.d(n,{A:()=>y});var a=t(76959),r=t(14232);let s=function(e,n){let t=(0,r.useRef)(!0);(0,r.useEffect)(()=>{if(t.current){t.current=!1;return}return e()},n)};var l=t(84467),i=t(55987),c=t(10401),o=t(15039),u=t.n(o),d=t(22631),m=t(77346),h=t(37876);let p=r.forwardRef((e,n)=>{let{className:t,bsPrefix:a,as:r="div",...s}=e;return a=(0,m.oU)(a,"carousel-caption"),(0,h.jsx)(r,{ref:n,className:u()(t,a),...s})});p.displayName="CarouselCaption";let f=r.forwardRef((e,n)=>{let{as:t="div",bsPrefix:a,className:r,...s}=e,l=u()(r,(0,m.oU)(a,"carousel-item"));return(0,h.jsx)(t,{ref:n,...s,className:l})});f.displayName="CarouselItem";var x=t(49285),g=t(66270),_=t(79043),v=t(56640);let N=r.forwardRef((e,n)=>{let t,{defaultActiveIndex:o=0,...p}=e,{as:f="div",bsPrefix:N,slide:y=!0,fade:b=!1,controls:w=!0,indicators:M=!0,indicatorLabels:j=[],activeIndex:k,onSelect:S,onSlide:A,onSlid:L,interval:T=5e3,keyboard:C=!0,onKeyDown:D,pause:E="hover",onMouseOver:F,onMouseOut:Y,wrap:H=!0,touch:J=!0,onTouchStart:I,onTouchMove:O,onTouchEnd:R,prevIcon:P=(0,h.jsx)("span",{"aria-hidden":"true",className:"carousel-control-prev-icon"}),prevLabel:z="Previous",nextIcon:U=(0,h.jsx)("span",{"aria-hidden":"true",className:"carousel-control-next-icon"}),nextLabel:W="Next",variant:X,className:B,children:G,...K}=(0,d.Zw)({defaultActiveIndex:o,...p},{activeIndex:"onSelect"}),Q=(0,m.oU)(N,"carousel"),V=(0,m.Wz)(),Z=(0,r.useRef)(null),[q,$]=(0,r.useState)("next"),[ee,en]=(0,r.useState)(!1),[et,ea]=(0,r.useState)(!1),[er,es]=(0,r.useState)(k||0);(0,r.useEffect)(()=>{et||k===er||(Z.current?$(Z.current):$((k||0)>er?"next":"prev"),y&&ea(!0),es(k||0))},[k,et,er,y]),(0,r.useEffect)(()=>{Z.current&&(Z.current=null)});let el=0;(0,x.jJ)(G,(e,n)=>{++el,n===k&&(t=e.props.interval)});let ei=(0,l.A)(t),ec=(0,r.useCallback)(e=>{if(et)return;let n=er-1;if(n<0){if(!H)return;n=el-1}Z.current="prev",null==S||S(n,e)},[et,er,S,H,el]),eo=(0,a.A)(e=>{if(et)return;let n=er+1;if(n>=el){if(!H)return;n=0}Z.current="next",null==S||S(n,e)}),eu=(0,r.useRef)();(0,r.useImperativeHandle)(n,()=>({element:eu.current,prev:ec,next:eo}));let ed=(0,a.A)(()=>{!document.hidden&&function(e){if(!e||!e.style||!e.parentNode||!e.parentNode.style)return!1;let n=getComputedStyle(e);return"none"!==n.display&&"hidden"!==n.visibility&&"none"!==getComputedStyle(e.parentNode).display}(eu.current)&&(V?ec():eo())}),em="next"===q?"start":"end";s(()=>{y||(null==A||A(er,em),null==L||L(er,em))},[er]);let eh="".concat(Q,"-item-").concat(q),ep="".concat(Q,"-item-").concat(em),ef=(0,r.useCallback)(e=>{(0,_.A)(e),null==A||A(er,em)},[A,er,em]),ex=(0,r.useCallback)(()=>{ea(!1),null==L||L(er,em)},[L,er,em]),eg=(0,r.useCallback)(e=>{if(C&&!/input|textarea/i.test(e.target.tagName))switch(e.key){case"ArrowLeft":e.preventDefault(),V?eo(e):ec(e);return;case"ArrowRight":e.preventDefault(),V?ec(e):eo(e);return}null==D||D(e)},[C,D,ec,eo,V]),e_=(0,r.useCallback)(e=>{"hover"===E&&en(!0),null==F||F(e)},[E,F]),ev=(0,r.useCallback)(e=>{en(!1),null==Y||Y(e)},[Y]),eN=(0,r.useRef)(0),ey=(0,r.useRef)(0),eb=(0,i.A)(),ew=(0,r.useCallback)(e=>{eN.current=e.touches[0].clientX,ey.current=0,"hover"===E&&en(!0),null==I||I(e)},[E,I]),eM=(0,r.useCallback)(e=>{e.touches&&e.touches.length>1?ey.current=0:ey.current=e.touches[0].clientX-eN.current,null==O||O(e)},[O]),ej=(0,r.useCallback)(e=>{if(J){let n=ey.current;Math.abs(n)>40&&(n>0?ec(e):eo(e))}"hover"===E&&eb.set(()=>{en(!1)},T||void 0),null==R||R(e)},[J,E,ec,eo,eb,T,R]),ek=null!=T&&!ee&&!et,eS=(0,r.useRef)();(0,r.useEffect)(()=>{var e,n;if(!ek)return;let t=V?ec:eo;return eS.current=window.setInterval(document.visibilityState?ed:t,null!=(e=null!=(n=ei.current)?n:T)?e:void 0),()=>{null!==eS.current&&clearInterval(eS.current)}},[ek,ec,eo,ei,T,ed,V]);let eA=(0,r.useMemo)(()=>M&&Array.from({length:el},(e,n)=>e=>{null==S||S(n,e)}),[M,el,S]);return(0,h.jsxs)(f,{ref:eu,...K,onKeyDown:eg,onMouseOver:e_,onMouseOut:ev,onTouchStart:ew,onTouchMove:eM,onTouchEnd:ej,className:u()(B,Q,y&&"slide",b&&"".concat(Q,"-fade"),X&&"".concat(Q,"-").concat(X)),children:[M&&(0,h.jsx)("div",{className:"".concat(Q,"-indicators"),children:(0,x.Tj)(G,(e,n)=>(0,h.jsx)("button",{type:"button","data-bs-target":"","aria-label":null!=j&&j.length?j[n]:"Slide ".concat(n+1),className:n===er?"active":void 0,onClick:eA?eA[n]:void 0,"aria-current":n===er},n))}),(0,h.jsx)("div",{className:"".concat(Q,"-inner"),children:(0,x.Tj)(G,(e,n)=>{let t=n===er;return y?(0,h.jsx)(v.A,{in:t,onEnter:t?ef:void 0,onEntered:t?ex:void 0,addEndListener:g.A,children:(n,a)=>r.cloneElement(e,{...a,className:u()(e.props.className,t&&"entered"!==n&&eh,("entered"===n||"exiting"===n)&&"active",("entering"===n||"exiting"===n)&&ep)})}):r.cloneElement(e,{className:u()(e.props.className,t&&"active")})})}),w&&(0,h.jsxs)(h.Fragment,{children:[(H||0!==k)&&(0,h.jsxs)(c.A,{className:"".concat(Q,"-control-prev"),onClick:ec,children:[P,z&&(0,h.jsx)("span",{className:"visually-hidden",children:z})]}),(H||k!==el-1)&&(0,h.jsxs)(c.A,{className:"".concat(Q,"-control-next"),onClick:eo,children:[U,W&&(0,h.jsx)("span",{className:"visually-hidden",children:W})]})]})]})});N.displayName="Carousel";let y=Object.assign(N,{Caption:p,Item:f})},67456:(e,n,t)=>{(window.__NEXT_P=window.__NEXT_P||[]).push(["/vspace/VirtualSpaceAccordionSection",function(){return t(20276)}])},84135:function(e,n,t){(function(e){"use strict";function n(e,n,t,a){var r={m:["eine Minute","einer Minute"],h:["eine Stunde","einer Stunde"],d:["ein Tag","einem Tag"],dd:[e+" Tage",e+" Tagen"],w:["eine Woche","einer Woche"],M:["ein Monat","einem Monat"],MM:[e+" Monate",e+" Monaten"],y:["ein Jahr","einem Jahr"],yy:[e+" Jahre",e+" Jahren"]};return n?r[t][0]:r[t][1]}e.defineLocale("de",{months:"Januar_Februar_M\xe4rz_April_Mai_Juni_Juli_August_September_Oktober_November_Dezember".split("_"),monthsShort:"Jan._Feb._M\xe4rz_Apr._Mai_Juni_Juli_Aug._Sep._Okt._Nov._Dez.".split("_"),monthsParseExact:!0,weekdays:"Sonntag_Montag_Dienstag_Mittwoch_Donnerstag_Freitag_Samstag".split("_"),weekdaysShort:"So._Mo._Di._Mi._Do._Fr._Sa.".split("_"),weekdaysMin:"So_Mo_Di_Mi_Do_Fr_Sa".split("_"),weekdaysParseExact:!0,longDateFormat:{LT:"HH:mm",LTS:"HH:mm:ss",L:"DD.MM.YYYY",LL:"D. MMMM YYYY",LLL:"D. MMMM YYYY HH:mm",LLLL:"dddd, D. MMMM YYYY HH:mm"},calendar:{sameDay:"[heute um] LT [Uhr]",sameElse:"L",nextDay:"[morgen um] LT [Uhr]",nextWeek:"dddd [um] LT [Uhr]",lastDay:"[gestern um] LT [Uhr]",lastWeek:"[letzten] dddd [um] LT [Uhr]"},relativeTime:{future:"in %s",past:"vor %s",s:"ein paar Sekunden",ss:"%d Sekunden",m:n,mm:"%d Minuten",h:n,hh:"%d Stunden",d:n,dd:n,w:n,ww:"%d Wochen",M:n,MM:n,y:n,yy:n},dayOfMonthOrdinalParse:/\d{1,2}\./,ordinal:"%d.",week:{dow:1,doy:4}})})(t(10841))}},e=>{var n=n=>e(e.s=n);e.O(0,[7725,9773,1772,7126,8477,276,636,6593,8792],()=>n(67456)),_N_E=e.O()}]);
//# sourceMappingURL=VirtualSpaceAccordionSection-7b06f077fe4515c4.js.map