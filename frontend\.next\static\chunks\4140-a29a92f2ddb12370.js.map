{"version": 3, "file": "static/chunks/4140-a29a92f2ddb12370.js", "mappings": "qXAqBA,IAAMA,EAAiBC,IACnB,IAiNIC,EACAC,EAlNEC,EAASC,CAAAA,EAAAA,EAAAA,SAAAA,CAASA,GAClB,GAAEC,CAAC,CAAE,CAAGC,CAAAA,EAAAA,EAAAA,EAAAA,CAAcA,CAAC,UACnBC,EAAqBC,CAAAA,EAAAA,EAAAA,EAAAA,CAAYA,GACrC,CAACC,EAAYC,EAAc,CAAGC,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAM,MAC5C,CAACC,EAAWC,EAAa,CAAGF,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAQ,EAAE,EAC9C,CAACG,EAAeC,EAAe,CAAGJ,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAQ,EAAE,EACpD,CAACK,EAAYC,EAAc,CAAGN,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAS,IAC/C,CAACO,EAAOC,EAAS,CAAGR,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAQ,EAAE,EACtC,CAACS,EAAqBC,EAAuB,CAAGV,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAQ,EAAE,EAClE,CAACW,EAAOC,EAAS,CAAGZ,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,EAAU,GACtC,CAACa,EAAkBC,EAAoB,CAAGd,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAM,MACxD,CAACe,EAAWC,EAAa,CAAGhB,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAM,MAC1C,CAACiB,EAAaC,EAAe,CAAGlB,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,EAAU,GAElDmB,EAAU3B,GAAUA,EAAO4B,KAAK,EAAI5B,EAAO4B,KAAK,CAACC,EAAE,CAGnDC,EAAY,CACdF,MAAO,CAAC,EACRG,KAAM,CAAEC,SAAU,KAAM,EACxBC,MAAO,GACX,EAGMC,EAAgB,UAClB,IAAMC,EAAW,MAAMC,EAAAA,CAAUA,CAACC,GAAG,CAAC,WAAmB,OAARV,IAC7CQ,GACA5B,EAAc4B,EAEtB,EAGMG,CANY,CAMM,cAEhBC,EADJ,IAAIA,EAAW,MAAMH,EAAAA,CAAUA,CAACC,GAAG,CAAE,SAASP,UAC1CS,GAAAA,OAAAA,EAAAA,EAAUC,IAAAA,EAAVD,CAAAA,IAAAA,EAAAA,EAAgBE,GAAhBF,GAAsB,IACtBA,EAASC,IAAI,CAAGD,EAASC,IAAI,CAACE,MAAM,CAAC,GACL,oBAA5BC,EAAUC,aAAa,EAA+C,oBAArBD,EAAUE,MAAM,CAAiC,EAE1G,IAAMV,EAAW,MAAMC,EAAAA,CAAUA,CAACC,GAAG,CAAC,WAAmB,OAARV,IACjD,GAAIY,GAAYJ,GAAYA,EAASW,OAAO,CAAE,CAC1C,IAAMC,EAAMZ,EAASW,OAAO,EAAIX,EAASW,OAAO,CAACE,GAAG,CAAC,GAAeC,EAAKC,GAAG,EAG5ExC,EADsByC,EADEX,IAAI,CAACQ,GAAG,CAEnBI,CAFqBH,EAAWI,IAAa,EAAEC,MAAOL,EAAKjB,QAAQ,CAAEjB,MAAOkC,EAAKC,GAAG,CAAC,GACrER,MAAM,CAAC,GAA2C,CAAC,IAA7BK,EAAIQ,OAAO,CAACN,EAAKlC,KAAK,GAE7E,CACJ,EAIMyC,EAAmB,CACrB5B,MAAO,CAAE6B,OAAQ9B,CAAQ,EACzB+B,OAAQ,gDACRzB,MAAO,GACX,EACM0B,EAAmB,UACrB,IAAMC,EAAqB,MAAMxB,EAAAA,CAAUA,CAACC,GAAG,CAC1C,+CACDmB,GAEAI,GAAsBA,EAAmBpB,IAAI,EAAIoB,EAAmBpB,IAAI,CAACC,MAAM,CAAG,EAKlFvB,CALqF,CAC9D0C,EAAmBpB,IAAI,CAACQ,GAAG,CAAC,IAC/Ca,EAAEC,IAGiBC,QAHL,CAACb,GAAG,CAAGW,EAAEX,GAAG,CACnBW,EAAEC,YAAY,IAIzB5C,EAAuB0C,GAAsBA,EAAmBpB,IAAI,CAE5E,EAGAwB,CAAAA,EAAAA,EAAAA,SAAAA,CAASA,CAAC,KACN9B,IACAI,IACAqB,GACJ,EAAG,EAAE,EAGLK,CAAAA,EAAAA,EAAAA,SAAAA,CAASA,CAAC,KACN9B,IACAI,IACAqB,GACJ,EAAG,CAAClC,EAAY,EAmBhB,IAAMwC,EAAgB,MAAOC,IACzB,GAAKrD,CAAD,CAGJ,OAAQqD,EAAMC,CAHG,EAGA,EACb,IAAK,QACL,IAAK,MAEU,eACJC,IAAI,CAACvD,IAIe,IAAI,CAAvBsC,CAFW,GAFM,GAEAf,EAAAA,CAAUA,CAACiC,IAAI,CAAC,0BADxB,CAAEC,MAAOzD,CAAW,EAC+B2B,EAErD+B,OAAO,CACdvD,EAAS,IAAID,EAAO,CAAEuC,MAAOzC,EAAYE,MAAOF,CAAW,EAAE,EAE7D2D,EAAAA,EAAKA,CAACC,KAAK,CAAC,GAAc,OAAX5D,EAAW,uBAE9BC,EAAc,IACdoD,EAAMQ,cAAc,KAEpBR,EAAMQ,cAAc,GACpBF,EAAAA,EAAKA,CAACC,KAAK,CAACvE,EAAE,wCACdY,EAAc,IAE1B,CACJ,EAGM6D,GAAgB,UAClB,IAAMC,EAAW,CACb1B,IAAK7B,EAAiB6B,GAAG,CACzBL,OAAQtB,CACZ,EACMY,EAAW,MAAMC,EAAAA,CAAUA,CAACiC,IAAI,CAAC,wBAAyBO,GAC5DzC,GAAgC,KAAK,CAAzBA,EAASU,MAAM,EAC3BnB,EAAe,CAACD,EAExB,EAGMoD,GAAe,CAAChB,EAAQiB,KAC1B1D,GAAS,GACTI,EAAasD,GACbxD,EAAoBuC,EACxB,EAGMkB,GAAmB,UACrB,MAAM3C,EAAAA,CAAUA,CAAC4C,MAAM,CAAC,WAAmB,OAARrD,IACnC6C,EAAAA,EAAKA,CAACS,OAAO,CAAC/E,EAAE,8CAChBgF,IAAAA,IAAW,CAAC,UAChB,EAGMC,GAAU,CACZ,CACIC,KAAMlF,EAAE,mBACRmF,SAAU,WACVC,UAAU,CACd,EACA,CACIF,KAAMlF,EAAE,gBACRmF,SAAU,QACVC,UAAU,CACd,EACA,CACIF,KAAMlF,EAAE,iBACRqF,KAAM,GACF1B,EACI,UAAC2B,IAAAA,CAAEC,QAAS,IAAMZ,GAAahB,EAAEX,GAAG,CAAE,kBAAmBwC,MAAO,CAAEC,OAAQ,SAAU,WAChF,UAACC,IAAAA,CAAEC,UAAU,4BAGjB,EAEZ,EACH,CAGKC,GAAmB,CACrB,CACIV,KAAMlF,EAAE,mBACRmF,SAAU,WACVC,UAAU,CACd,EACA,CACIF,KAAMlF,EAAE,gBACRmF,SAAU,QACVC,UAAU,CACd,EACA,CACIF,KAAMlF,EAAE,iBACRqF,KAAM,GACF,WAACQ,MAAAA,WACG,UAACC,EAAAA,CAAMA,CAAAA,CAACC,QAAQ,UAAUC,KAAK,KAAKT,QAAS,IAAMZ,GAAahB,EAAG,qBAC9D3D,EAAE,oBACE,OAET,UAAC8F,EAAAA,CAAMA,CAAAA,CAACC,QAAQ,YAAYC,KAAK,KAAKT,QAAS,IAAMZ,GAAahB,EAAG,qBAChE3D,EAAE,qBAInB,EACH,CAKD,OAAQqB,GACJ,IAAK,iBACDzB,EAAaI,EAAE,qCACfH,EAAcG,EAAE,4DAChB,KACJ,KAAK,WACDJ,EAAaI,EAAE,sBACfH,EAAcG,EAAE,oDAChB,KACJ,KAAK,WACDJ,EAAaI,EAAE,qBACfH,EAAcG,EAAE,yDAChB,KACJ,KAAK,eACDJ,EAAaI,EAAE,6BACfH,EAAcG,EAAE,gDAExB,CAGA,IAAMiG,GAA8B,CAACC,EAAWC,EAAYC,IAOjDC,IAAAA,OAAS,CAACH,EANG,GAChB,CAAO,CAACC,EAAM,CACHG,CADK,CACDH,EAAM,CAACI,WAAW,GAE1BD,CAAG,CAACH,EAAM,CAEeC,GAIlCI,GAAuB,UACzB,IAAMC,EACFrG,GAAcA,EAAWwC,OAAO,CAACJ,MAAM,CAAC,GAAeO,EAAKC,GAAG,GAAK7B,GAAkB2B,GAAG,CAAC,GAAeC,EAAKC,GAAG,EAC/G0D,EACFtG,GAAcA,EAAWuG,WAAW,CAACnE,MAAM,CAAEO,GAAcA,EAAKC,GAAG,GAAK7B,EAC5E,OAAMe,EAAAA,CAAUA,CAAC0E,KAAK,CAAC,WAAmB,OAARnF,GAAW,CACzC,GAAGrB,CAAU,CACbwC,QAAS6D,EACTI,WAAyC,KAA7BzG,EAAWyG,UAAU,CAAC,EAAE,CAAU,GAAKzG,EAAWyG,UAAU,CACxEF,YAAaD,EAAoB5D,GAAG,CAAC,GAAeC,EAAKC,GAAG,CAChE,GACAxB,EAAe,CAACD,EACpB,EAGMuF,GAAe,KACjB5F,GAAS,GACTE,EAAoB,MACpBE,EAAa,KACjB,EA2DMyF,GAAgB,MAAOnC,IACzB,IAAMoC,EAAoBnG,GAASA,EAAMiC,GAAG,CAAC,GAAeC,EAAKlC,KAAK,EAChEoG,EAAiBxG,GAAiBA,EAAcqC,GAAG,CAAC,GAAeC,EAAKlC,KAAK,EAC7EqG,EAAa9G,GAAcA,EAAWwC,OAAO,CAACE,GAAG,CAAC,GAAeC,EAAKC,GAAG,EAClE,oBAAT4B,GAA8BnE,EAAc8B,MAAM,CAAG,GAAG,MAClDL,EAAAA,CAAUA,CAAC0E,KAAK,CAAC,WAAmB,OAARnF,GAAW,CACzC,GAAGrB,CAAU,CACbwC,QAAS,IAAIsE,KAAeD,EAAe,CAC3CJ,WAAyC,KAA7BzG,EAAWyG,UAAU,CAAC,EAAE,CAAU,GAAKzG,EAAWyG,UAAU,GAE5EvC,EAAAA,EAAKA,CAACS,OAAO,CAAC/E,EAAE,mCAChBU,EAAe,EAAE,EACjBc,EAAe,CAACD,IACA,uBAATqD,GAAiC/D,EAAM0B,MAAM,CAAG,GAAG,MACpDL,EAAAA,CAAUA,CAAC0E,KAAK,CAAC,WAAmB,OAARnF,GAAW,CACzC,GAAGrB,CAAU,CACbyG,WAAYG,EACZpE,QAASsE,CACb,GACA5C,EAAAA,EAAKA,CAACS,OAAO,CAAC/E,EAAE,mCAChBc,EAAS,EAAE,EACXU,EAAe,CAACD,IAEhB+C,EAAAA,EAAKA,CAACC,KAAK,CAACvE,EAAE,gDAEtB,EAEA,MACI,UAAC6F,MAAAA,CAAIF,UAAU,gBACX,WAACwB,EAAAA,CAASA,CAAAA,WACL/G,EACG,iCACI,UAACgH,EAAAA,CAAGA,CAAAA,CAACzB,UAAU,gBACX,UAAC0B,EAAAA,CAAGA,CAAAA,CAAC1B,UAAU,eACX,WAACE,MAAAA,CAAIF,UAAU,2CACX,UAACE,MAAAA,UACG,WAACyB,KAAAA,WACG,UAACC,IAAIA,CACDC,KAAK,sBACLC,GAAI,cAFHF,EAE2B,OAAR9F,GACpBkE,UAAU,sBACTvF,EAAWsH,KAAK,GAErB,UAACH,IAAIA,CAACC,KAAK,sBAAsBC,GAAI,cAAhCF,EAAwD,OAAR9F,YACjD,WAACqE,EAAAA,CAAMA,CAAAA,CAACC,QAAQ,YAAYC,KAAK,KAAKL,UAAU,iBAC5C,UAACgC,EAAAA,CAAeA,CAAAA,CAACC,KAAMC,EAAAA,GAAKA,GAAI,OACzB7H,EAAE,yBAKzB,WAAC8F,EAAAA,CAAMA,CAAAA,CAACC,QAAQ,OAAOR,QA3G3B,CA2GoCuC,IA1G5D5G,GAAS,GACTI,EAAa,gBACbF,EAAoBK,EACxB,YAwGqC,IACD,UAACkG,EAAAA,CAAeA,CAAAA,CAACC,KAAMG,EAAAA,GAAUA,CAAEC,MAAM,OAAOrC,UAAU,SACzD3F,EAAE,uCAKnB,UAACoH,EAAAA,CAAGA,CAAAA,UACA,WAACC,EAAAA,CAAGA,CAAAA,CAAC1B,UAAU,eAAesC,GAAI,aAC9B,UAACC,KAAAA,UACG,UAACC,OAAAA,UAAMnI,EAAE,6CAEb,UAACoI,EAAAA,CAAQA,CAAAA,CACLC,UAAU,EACVpD,QAASA,GACT3C,KAAMlC,EAAWwC,OAAO,CACxB0F,MAAO,GACPC,gBAAgB,IAChBC,WAAW,EACXC,oBAAqB,EACrBC,SAAS,IACTC,eAAe,OACfC,YAAY,EACZC,gBAAgB,IAChBC,aAAc7C,UAI1B,UAACmB,EAAAA,CAAGA,CAAAA,UACA,WAACC,EAAAA,CAAGA,CAAAA,CAAC1B,UAAU,eAAesC,GAAI,aAC9B,UAACC,KAAAA,UACG,UAACC,OAAAA,UAAMnI,EAAE,oCAEZe,GAAuBA,EAAoBwB,MAAM,CAAG,EACjD,UAAC6F,EAAAA,CAAQA,CAAAA,CACLC,UAAU,EACVpD,QAASW,GACTtD,KAAMvB,EACNuH,OAAO,EACPC,gBAAgB,IAChBC,WAAW,EACXC,oBAAqB,EACrBC,SAAS,IACTC,eAAe,OACfC,YAAY,EACZC,gBAAgB,IAChBC,aAAc7C,KAGlB,UAACJ,MAAAA,CAAIF,UAAU,uBAAe3F,EAAE,iCAI5C,UAACoH,EAAAA,CAAGA,CAAAA,UACA,WAACC,EAAAA,CAAGA,CAAAA,CAAC1B,UAAU,yBACX,UAACuC,KAAAA,UACG,UAACC,OAAAA,UAAMnI,EAAE,qCAEb,WAAC6F,MAAAA,WACG,UAACkD,EAAAA,EAAMA,CAAAA,CACHC,mBAAmB,EACnBC,WAAY/I,EACZgJ,OAAO,IACPrI,MAAOJ,GAAiB,EAAE,CAC1B0I,YAAanJ,EAAE,sBACfoJ,SAhWfC,CAgWyBC,GA/V1C,IAAMC,EAAY5J,EAAM4J,SAAS,EAAI,CAAEnG,MAAO,YAAavC,MAAO,GAAI,EAClEwI,GAAYA,EAAS9G,MAAM,CAAG,GAAK8G,CAAQ,CAACA,EAAS9G,MAAM,CAAG,EAAE,CAAC1B,KAAK,GAAK0I,EAAU1I,KAAK,CAC1FH,CAD4F,CAC7EH,GAEfG,EAAe2I,EAEvB,EA0VoCG,QAAS,CACL7J,EAAM4J,SAAS,EAAI,CAAEnG,MAAO,YAAavC,MAAO,GAAI,KAChDN,GAAa,EAAE,CACtB,GAEL,UAACuF,EAAAA,CAAMA,CAAAA,CACHH,UAAU,OACVI,QAAQ,UACRR,QAAS,IAAMwB,GAAc,4BAE5B/G,EAAE,+BAKnB,UAACoH,EAAAA,CAAGA,CAAAA,UACA,WAACC,EAAAA,CAAGA,CAAAA,CAAC1B,UAAU,yBACX,UAACuC,KAAAA,UACG,UAACC,OAAAA,UAAMnI,EAAE,kDAEb,UAACyJ,QAAAA,UAAOzJ,EAAE,iDACV,WAAC6F,MAAAA,WACG,UAAC6D,EAAAA,CAAeA,CAAAA,CACZT,WAAY/I,EACZS,WAAYA,EACZgJ,WAAW,IACXT,OAAO,IACPU,YAAY,EACZR,SAnXf,CAmXyBS,GAlXnC/I,EAASD,GAAgB,EAAE,EAmXFiJ,CAnXRjJ,aAGF,CAgXyBkJ,EAhXFnJ,EAAcoJ,GAiX3BC,UAAWlG,EACXoF,YAAanJ,EAAE,qDACfa,MAAOA,GAAS,EAAE,GAEtB,UAACiF,EAAAA,CAAMA,CAAAA,CACHH,UAAU,OACVI,QAAQ,UACRR,QAAS,IAAMwB,GAAc,+BAE5B/G,EAAE,kCAOvB,UAAC6F,MAAAA,CAAIF,UAAU,6DACX,UAACuE,EAAAA,CAAOA,CAAAA,CAACC,UAAU,SAASpE,QAAQ,cAG5C,WAACqE,EAAAA,CAAKA,CAAAA,CAACC,KAAMpJ,EAAOqJ,OAAQ,IAAMpJ,GAAS,aACvC,UAACkJ,EAAAA,CAAKA,CAACG,MAAM,EAACC,WAAW,aACrB,UAACJ,EAAAA,CAAKA,CAACK,KAAK,WAAE7K,MAElB,UAACwK,EAAAA,CAAKA,CAACM,IAAI,WAAE7K,IACb,WAACgG,MAAAA,CAAIF,UAAU,2CACX,UAACG,EAAAA,CAAMA,CAAAA,CAACC,QAAQ,YAAYR,QAnMpB,CAmM6BoF,IAlMrD,OAAQtJ,GACJ,IAAK,iBAIL,IAAK,WAIL,IAAK,WAIL,IAAK,eAXDyF,IAcR,CACJ,EAiL8EnB,UAAU,gBAC/D3F,EAAE,eAEP,UAAC8F,EAAAA,CAAMA,CAAAA,CAACC,QAAQ,UAAUR,QAlOlB,CAkO2BqF,IAjOnD,OAAQvJ,GACJ,IAAK,iBACDiD,EAAAA,EAAKA,CAACS,OAAO,CAAC/E,EAAE,mCAChB8G,KACAN,KACA,KAEJ,KAAK,WACDlC,EAAAA,EAAKA,CAACS,OAAO,CAAC/E,EAAE,oCAChB8G,KACArC,KACA,KAEJ,KAAK,WACDH,EAAAA,EAAKA,CAACS,OAAO,CAAC/E,EAAE,oCAChB8G,KACArC,KACA,KAEJ,KAAK,eACDvD,GAAS,GACTI,EAAa,MACbuD,IAER,CACJ,WAyMyB7E,EAAE,0BAO/B,EAEAN,EAAcmL,YAAY,CAAG,CACzBtB,UAAW,CACPnG,MAAO,YACPvC,MAAO,GACX,CACJ,EAEA,MAAenB", "sources": ["webpack://_N_E/./pages/vspace/ManageMembers.tsx"], "sourcesContent": ["//Import Library\r\nimport React, { useState, useEffect } from \"react\";\r\nimport { <PERSON>, Container, <PERSON>, Spinner, Button, Modal } from \"react-bootstrap\";\r\nimport _ from \"lodash\";\r\nimport Link from \"next/link\";\r\nimport { FontAwesomeIcon } from \"@fortawesome/react-fontawesome\";\r\nimport { faPen, faTrashAlt } from \"@fortawesome/free-solid-svg-icons\";\r\nimport Select from \"react-select\";\r\nimport makeAnimated from \"react-select/animated\";\r\nimport CreatableSelect from \"react-select/creatable\";\r\n\r\nimport toast from 'react-hot-toast';\r\nimport Router, { useRouter } from \"next/router\";\r\n\r\n//Import services/components\r\nimport RKITable from \"../../components/common/RKITable\";\r\nimport apiService from \"../../services/apiService\";\r\nimport { useTranslation } from 'next-i18next';\r\n\r\nconst vspaceerror = \"vspace.error\";\r\nconst vspacesuccess = \"vspace.success\";\r\nconst ManageMembers = (props: any) => {\r\n    const router = useRouter();\r\n    const { t } = useTranslation('common');\r\n        const animatedComponents = makeAnimated();\r\n    const [vspaceInfo, setVspaceInfo] = useState<any>(null);\r\n    const [usersList, setUsersList] = useState<any[]>([]);\r\n    const [selectedUsers, setSelectUsers] = useState<any[]>([]);\r\n    const [inputValue, setInputValue] = useState<string>(\"\");\r\n    const [value, setValue] = useState<any[]>([]);\r\n    const [vspaceRequestedUser, setVspaceRequestedUser] = useState<any[]>([]);\r\n    const [modal, setModal] = useState<boolean>(false);\r\n    const [platformMemberId, setPlatFormMemberId] = useState<any>(null);\r\n    const [modalType, setModalType] = useState<any>(null);\r\n    const [refreshPage, setRefreshpage] = useState<boolean>(false);\r\n\r\n    const routeId = router && router.query && router.query.id;\r\n\r\n    //Params for Data Fecthing\r\n    const userParms = {\r\n        query: {},\r\n        sort: { username: \"asc\" },\r\n        limit: \"~\",\r\n    };\r\n\r\n    //Get Vspace details by ID\r\n    const getVspaceInfo = async () => {\r\n        const response = await apiService.get(`/vspace/${routeId}`);\r\n        if (response) {\r\n            setVspaceInfo(response);\r\n        }\r\n    };\r\n\r\n    //Filter the choose members\r\n    const filteredMembers = async () => {\r\n        let userList = await apiService.get(`/users`, userParms);\r\n        if (userList?.data?.length)\r\n            userList.data = userList.data.filter((_userList: any) =>\r\n                _userList.vspace_status === \"Request Pending\" || _userList.status === \"Request Pending\" ? false : true\r\n            );\r\n        const response = await apiService.get(`/vspace/${routeId}`);\r\n        if (userList && response && response.members) {\r\n            const ids = response.members && response.members.map((item: any) => item._id);\r\n            const _users = userList.data.map((item: any, _i: any) => ({ label: item.username, value: item._id }));\r\n            const filteredUsers = _users.filter((item: any) => ids.indexOf(item.value) === -1);\r\n            setUsersList(filteredUsers);\r\n        }\r\n    };\r\n\r\n    //Fecth Vpsace Request\r\n\r\n    const requestUserParms = {\r\n        query: { vspace: routeId },\r\n        select: \"-vspace -requested_to -created_at -updated_at\",\r\n        limit: \"~\",\r\n    };\r\n    const getVspaceRequest = async () => {\r\n        const usrRequestedVspace = await apiService.get(\r\n            `/vspace-request-subscribers/getRequestedToMe`,\r\n            requestUserParms\r\n        );\r\n        if (usrRequestedVspace && usrRequestedVspace.data && usrRequestedVspace.data.length > 0) {\r\n            const requestedUsers = usrRequestedVspace.data.map((d: any) => {\r\n                d.requested_by._id = d._id;\r\n                return d.requested_by;\r\n            });\r\n            setVspaceRequestedUser(requestedUsers);\r\n        } else {\r\n            setVspaceRequestedUser(usrRequestedVspace && usrRequestedVspace.data);\r\n        }\r\n    };\r\n\r\n    //For Handle Api Calls\r\n    useEffect(() => {\r\n        getVspaceInfo();\r\n        filteredMembers();\r\n        getVspaceRequest();\r\n    }, []);\r\n\r\n    //For Refersh the page\r\n    useEffect(() => {\r\n        getVspaceInfo();\r\n        filteredMembers();\r\n        getVspaceRequest();\r\n    }, [refreshPage]);\r\n\r\n    //Hanlde User Change\r\n    const userHandler = (selected: any) => {\r\n        const allOption = props.allOption || { label: \"All users\", value: \"*\" };\r\n        if (selected && selected.length > 0 && selected[selected.length - 1].value === allOption.value) {\r\n            setSelectUsers(usersList);\r\n        } else {\r\n            setSelectUsers(selected);\r\n        }\r\n    };\r\n\r\n    //Hanldle Invite By email\r\n    const handleChange = () => {\r\n        return setValue(value ? value : []);\r\n    };\r\n\r\n    const handleInputChange = (inputchanges: any) => setInputValue(inputchanges);\r\n\r\n    const handleKeyDown = async (event: any) => {\r\n        if (!inputValue) {\r\n            return;\r\n        }\r\n        switch (event.key) {\r\n            case \"Enter\":\r\n            case \"Tab\":\r\n                //Validate it is email or not\r\n                const re = /\\S+@\\S+\\.\\S+/;\r\n                if (re.test(inputValue)) {\r\n                    const data = { email: inputValue };\r\n                    const _users = await apiService.post(\"/vspace/filterNonmember\", data);\r\n\r\n                    if (_users.message === \"\") {\r\n                        setValue([...value, { label: inputValue, value: inputValue }]);\r\n                    } else {\r\n                        toast.error(`${inputValue}  is already exist`)\r\n                    }\r\n                    setInputValue(\"\");\r\n                    event.preventDefault();\r\n                } else {\r\n                    event.preventDefault();\r\n                    toast.error(t(\"vspace.Pleaseentervalidemailaddress\"))\r\n                    setInputValue(\"\");\r\n                }\r\n        }\r\n    };\r\n\r\n    //Appoved or Reject Requested User\r\n    const requestStatus = async () => {\r\n        const postdata = {\r\n            _id: platformMemberId._id,\r\n            status: modalType,\r\n        };\r\n        const response = await apiService.post(\"/vspace/requestStatus\", postdata);\r\n        if (response && response.status === 200) {\r\n            setRefreshpage(!refreshPage);\r\n        }\r\n    };\r\n\r\n    //Hanlde modal\r\n    const modalHandler = (d: any, type: any) => {\r\n        setModal(true);\r\n        setModalType(type);\r\n        setPlatFormMemberId(d);\r\n    };\r\n\r\n    //Delete Vspace\r\n    const deleteVspaceForm = async () => {\r\n        await apiService.remove(`/vspace/${routeId}`);\r\n        toast.success(t(\"vspace.DeletedtheVirtualSpacesuccessfully\"))\r\n        Router.push(\"/vspace\");\r\n    };\r\n\r\n    //Columns for Monitoring memebrs Table\r\n    const columns = [\r\n        {\r\n            name: t(\"vspace.UserName\"),\r\n            selector: \"username\",\r\n            sortable: true,\r\n        },\r\n        {\r\n            name: t(\"vspace.Email\"),\r\n            selector: \"email\",\r\n            sortable: true,\r\n        },\r\n        {\r\n            name: t(\"vspace.Action\"),\r\n            cell: (d: any) =>\r\n                d ? (\r\n                    <a onClick={() => modalHandler(d._id, \"platformMember\")} style={{ cursor: \"pointer\" }}>\r\n                        <i className=\"icon fas fa-trash-alt\" />\r\n                    </a>\r\n                ) : (\r\n                    \"\"\r\n                ),\r\n        },\r\n    ];\r\n\r\n    //Columns for Requested user\r\n    const requestedColumns = [\r\n        {\r\n            name: t(\"vspace.UserName\"),\r\n            selector: \"username\",\r\n            sortable: true,\r\n        },\r\n        {\r\n            name: t(\"vspace.Email\"),\r\n            selector: \"email\",\r\n            sortable: true,\r\n        },\r\n        {\r\n            name: t(\"vspace.Action\"),\r\n            cell: (d: any) => (\r\n                <div>\r\n                    <Button variant=\"primary\" size=\"sm\" onClick={() => modalHandler(d, \"approved\")}>\r\n                        {t(\"vspace.Approve\")}\r\n                    </Button>\r\n                    &nbsp;\r\n                    <Button variant=\"secondary\" size=\"sm\" onClick={() => modalHandler(d, \"declined\")}>\r\n                        {t(\"vspace.Reject\")}\r\n                    </Button>\r\n                </div>\r\n            ),\r\n        },\r\n    ];\r\n\r\n    //Hanlde Popup Message\r\n    let headerText;\r\n    let bodyMessage;\r\n    switch (modalType) {\r\n        case \"platformMember\":\r\n            headerText = t(\"vspace.Deleteuserfromvirtualspace\");\r\n            bodyMessage = t(\"vspace.AreyousurewanttoremovetheuserfromtheVirtualSpace?\");\r\n            break;\r\n        case \"approved\":\r\n            headerText = t(\"vspace.ApproveUser\");\r\n            bodyMessage = t(\"vspace.AreyousurewanttoaddtheusertoVirtualSpace?\");\r\n            break;\r\n        case \"declined\":\r\n            headerText = t(\"vspace.RejectUser\");\r\n            bodyMessage = t(\"vspace.AreyousurewanttorejecttheuserfromVirtualSpace?\");\r\n            break;\r\n        case \"deleteVspace\":\r\n            headerText = t(\"vspace.DeleteVirtualSpace\");\r\n            bodyMessage = t(\"vspace.AreyousurewanttodeletetheVirtualSpace?\");\r\n            break;\r\n    }\r\n\r\n    //sort Function\r\n    const sortSubscribeRequestTousers = (rows: any, field: any, direction: any) => {\r\n        const handleField = (row: any) => {\r\n            if (row[field]) {\r\n                return row[field].toLowerCase();\r\n            }\r\n            return row[field];\r\n        };\r\n        return _.orderBy(rows, handleField, direction);\r\n    };\r\n\r\n    //Api Request to Delete the users\r\n    const membersDeleteHandler = async () => {\r\n        const filteredMemberss =\r\n            vspaceInfo && vspaceInfo.members.filter((item: any) => item._id !== platformMemberId).map((item: any) => item._id);\r\n        const filteredSubscribers =\r\n            vspaceInfo && vspaceInfo.subscribers.filter((item: any) => item._id !== platformMemberId);\r\n        await apiService.patch(`/vspace/${routeId}`, {\r\n            ...vspaceInfo,\r\n            members: filteredMemberss,\r\n            nonMembers: vspaceInfo.nonMembers[0] === \"\" ? \"\" : vspaceInfo.nonMembers,\r\n            subscribers: filteredSubscribers.map((item: any) => item._id),\r\n        });\r\n        setRefreshpage(!refreshPage);\r\n    };\r\n\r\n    //Empty Data & modal Handler\r\n    const modalHanlder = () => {\r\n        setModal(false);\r\n        setPlatFormMemberId(null);\r\n        setModalType(null);\r\n    };\r\n\r\n    //Vspcae DeleteHandler\r\n    const deleteVspaceHandler = () => {\r\n        setModal(true);\r\n        setModalType(\"deleteVspace\");\r\n        setPlatFormMemberId(routeId);\r\n    };\r\n\r\n    //Modal Confirm to delete or Approve\r\n    const modalConfirmHandler = () => {\r\n        switch (modalType) {\r\n            case \"platformMember\":\r\n                toast.success(t(\"vspace.Userremovedsuccessfully\"))\r\n                modalHanlder();\r\n                membersDeleteHandler();\r\n                break;\r\n\r\n            case \"approved\":\r\n                toast.success(t(\"vspace.Userapprovedsuccessfully\"))\r\n                modalHanlder();\r\n                requestStatus();\r\n                break;\r\n\r\n            case \"declined\":\r\n                toast.success(t(\"vspace.Userrejectedsuccessfully\"));\r\n                modalHanlder();\r\n                requestStatus();\r\n                break;\r\n\r\n            case \"deleteVspace\":\r\n                setModal(false);\r\n                setModalType(null);\r\n                deleteVspaceForm();\r\n                break;\r\n        }\r\n    };\r\n\r\n    const modalRejectHanldler = () => {\r\n        switch (modalType) {\r\n            case \"platformMember\":\r\n                modalHanlder();\r\n                break;\r\n\r\n            case \"declined\":\r\n                modalHanlder();\r\n                break;\r\n\r\n            case \"approved\":\r\n                modalHanlder();\r\n                break;\r\n\r\n            case \"deleteVspace\":\r\n                modalHanlder();\r\n                break;\r\n        }\r\n    };\r\n\r\n    //Submit Hanlder to send data to server\r\n    const submitHandler = async (type: any) => {\r\n        const choosenNonMembers = value && value.map((item: any) => item.value);\r\n        const choosenMembers = selectedUsers && selectedUsers.map((item: any) => item.value);\r\n        const oldMembers = vspaceInfo && vspaceInfo.members.map((item: any) => item._id);\r\n        if (type === \"platformMembers\" && selectedUsers.length > 0) {\r\n            await apiService.patch(`/vspace/${routeId}`, {\r\n                ...vspaceInfo,\r\n                members: [...oldMembers, ...choosenMembers],\r\n                nonMembers: vspaceInfo.nonMembers[0] === \"\" ? \"\" : vspaceInfo.nonMembers,\r\n            });\r\n            toast.success(t(\"vspace.UserInvitedsuccessfully\"));\r\n            setSelectUsers([]);\r\n            setRefreshpage(!refreshPage);\r\n        } else if (type === \"nonPlatformMembers\" && value.length > 0) {\r\n            await apiService.patch(`/vspace/${routeId}`, {\r\n                ...vspaceInfo,\r\n                nonMembers: choosenNonMembers,\r\n                members: oldMembers,\r\n            });\r\n            toast.success(t(\"vspace.UserInvitedsuccessfully\"));\r\n            setValue([]);\r\n            setRefreshpage(!refreshPage);\r\n        } else {\r\n            toast.error(t(\"vspace.Pleasechoosemembersoraddemailtoinvite\"));\r\n        }\r\n    };\r\n\r\n    return (\r\n        <div className=\"pe-2\">\r\n            <Container>\r\n                {vspaceInfo ? (\r\n                    <>\r\n                        <Row className=\"mt-3\">\r\n                            <Col className=\"p-0\">\r\n                                <div className=\"d-flex justify-content-between\">\r\n                                    <div>\r\n                                        <h4>\r\n                                            <Link\r\n                                                href=\"/vspace/[...routes]\"\r\n                                                as={`/vspace/show/${routeId}`}\r\n                                                className=\"h5 p-0 m-0\">\r\n                                                {vspaceInfo.title}\r\n                                            </Link>\r\n                                            <Link href=\"/vspace/[...routes]\" as={`/vspace/edit/${routeId}`} >\r\n                                                <Button variant=\"secondary\" size=\"sm\" className=\"ms-2\">\r\n                                                    <FontAwesomeIcon icon={faPen} />\r\n                                                    &nbsp;{t(\"vspace.Edit\")}\r\n                                                </Button>\r\n                                            </Link>\r\n                                        </h4>\r\n                                    </div>\r\n                                    <Button variant=\"dark\" onClick={deleteVspaceHandler}>\r\n                                        {\" \"}\r\n                                        <FontAwesomeIcon icon={faTrashAlt} color=\"#fff\" className=\"me-2\" />\r\n                                        {t(\"vspace.DeleteVirtualSpace\")}\r\n                                    </Button>\r\n                                </div>\r\n                            </Col>\r\n                        </Row>\r\n                        <Row>\r\n                            <Col className=\"header-block\" lg={12}>\r\n                                <h6>\r\n                                    <span>{t(\"vspace.MonitoringandEvaluationMembers\")}</span>\r\n                                </h6>\r\n                                <RKITable\r\n                                    noHeader={true}\r\n                                    columns={columns}\r\n                                    data={vspaceInfo.members}\r\n                                    dense={true}\r\n                                    paginationServer\r\n                                    pagServer={true}\r\n                                    paginationTotalRows={0}\r\n                                    subHeader\r\n                                    subHeaderAlign=\"left\"\r\n                                    pagination={true}\r\n                                    persistTableHead\r\n                                    sortFunction={sortSubscribeRequestTousers}\r\n                                />\r\n                            </Col>\r\n                        </Row>\r\n                        <Row>\r\n                            <Col className=\"header-block\" lg={12}>\r\n                                <h6>\r\n                                    <span>{t(\"vspace.SubscribeRequestUsers\")}</span>\r\n                                </h6>\r\n                                {vspaceRequestedUser && vspaceRequestedUser.length > 0 ? (\r\n                                    <RKITable\r\n                                        noHeader={true}\r\n                                        columns={requestedColumns}\r\n                                        data={vspaceRequestedUser}\r\n                                        dense={true}\r\n                                        paginationServer\r\n                                        pagServer={true}\r\n                                        paginationTotalRows={0}\r\n                                        subHeader\r\n                                        subHeaderAlign=\"left\"\r\n                                        pagination={true}\r\n                                        persistTableHead\r\n                                        sortFunction={sortSubscribeRequestTousers}\r\n                                    />\r\n                                ) : (\r\n                                    <div className=\"nodataFound\">{t(\"vspace.Nodataavailable\")}</div>\r\n                                )}\r\n                            </Col>\r\n                        </Row>\r\n                        <Row>\r\n                            <Col className=\"header-block\">\r\n                                <h6>\r\n                                    <span>{t(\"vspace.PlatformMembersInvites\")}</span>\r\n                                </h6>\r\n                                <div>\r\n                                    <Select\r\n                                        closeMenuOnSelect={false}\r\n                                        components={animatedComponents}\r\n                                        isMulti\r\n                                        value={selectedUsers || []}\r\n                                        placeholder={t(\"vspace.SelectUsers\")}\r\n                                        onChange={userHandler}\r\n                                        options={[\r\n                                            props.allOption || { label: \"All users\", value: \"*\" },\r\n                                            ...(usersList || [])\r\n                                        ]}\r\n                                    />\r\n                                    <Button\r\n                                        className=\"mt-3\"\r\n                                        variant=\"primary\"\r\n                                        onClick={() => submitHandler(\"platformMembers\")}\r\n                                    >\r\n                                        {t(\"vspace.AddMembers\")}\r\n                                    </Button>\r\n                                </div>\r\n                            </Col>\r\n                        </Row>\r\n                        <Row>\r\n                            <Col className=\"header-block\">\r\n                                <h6>\r\n                                    <span>{t(\"vspace.Non-PlatformMemberInvites(by email)\")}</span>\r\n                                </h6>\r\n                                <small>{t(\"vspace.PressTabtoseparatemultipleemailid(s)\")}</small>\r\n                                <div>\r\n                                    <CreatableSelect\r\n                                        components={animatedComponents}\r\n                                        inputValue={inputValue}\r\n                                        isClearable\r\n                                        isMulti\r\n                                        menuIsOpen={false}\r\n                                        onChange={handleChange}\r\n                                        onInputChange={handleInputChange}\r\n                                        onKeyDown={handleKeyDown}\r\n                                        placeholder={t(\"vspace.Typeemailid(s),pressingenterbetweeneachone\")}\r\n                                        value={value || []}\r\n                                    />\r\n                                    <Button\r\n                                        className=\"mt-3\"\r\n                                        variant=\"primary\"\r\n                                        onClick={() => submitHandler(\"nonPlatformMembers\")}\r\n                                    >\r\n                                        {t(\"vspace.AddMembers\")}\r\n                                    </Button>\r\n                                </div>\r\n                            </Col>\r\n                        </Row>\r\n                    </>\r\n                ) : (\r\n                    <div className=\"d-flex justify-content-center align-items-center \">\r\n                        <Spinner animation=\"border\" variant=\"primary\" />\r\n                    </div>\r\n                )}\r\n                <Modal show={modal} onHide={() => setModal(false)}>\r\n                    <Modal.Header closeButton>\r\n                        <Modal.Title>{headerText}</Modal.Title>\r\n                    </Modal.Header>\r\n                    <Modal.Body>{bodyMessage}</Modal.Body>\r\n                    <div className=\"d-flex justify-content-end m-2\">\r\n                        <Button variant=\"secondary\" onClick={modalRejectHanldler} className=\"me-2\">\r\n                            {t(\"vspace.No\")}\r\n                        </Button>\r\n                        <Button variant=\"primary\" onClick={modalConfirmHandler}>\r\n                            {t(\"vspace.Yes\")}\r\n                        </Button>\r\n                    </div>\r\n                </Modal>\r\n            </Container>\r\n        </div>\r\n    );\r\n};\r\n\r\nManageMembers.defaultProps = {\r\n    allOption: {\r\n        label: \"All users\",\r\n        value: \"*\",\r\n    },\r\n};\r\n\r\nexport default ManageMembers;\r\n"], "names": ["ManageMembers", "props", "headerText", "bodyMessage", "router", "useRouter", "t", "useTranslation", "animatedComponents", "makeAnimated", "vspaceInfo", "setVspaceInfo", "useState", "usersList", "setUsersList", "selectedUsers", "setSelectUsers", "inputValue", "setInputValue", "value", "setValue", "vspaceRequestedUser", "setVspaceRequestedUser", "modal", "setModal", "platformMemberId", "setPlatFormMemberId", "modalType", "setModalType", "refreshPage", "setRefreshpage", "routeId", "query", "id", "userParms", "sort", "username", "limit", "getVspaceInfo", "response", "apiService", "get", "filteredMembers", "userList", "data", "length", "filter", "_userList", "vspace_status", "status", "members", "ids", "map", "item", "_id", "_users", "filteredUsers", "_i", "label", "indexOf", "requestUserParms", "vspace", "select", "getVspaceRequest", "usrRequestedVspace", "d", "requested_by", "requestedUsers", "useEffect", "handleKeyDown", "event", "key", "test", "post", "email", "message", "toast", "error", "preventDefault", "requestStatus", "postdata", "modal<PERSON><PERSON>ler", "type", "deleteVspaceForm", "remove", "success", "Router", "columns", "name", "selector", "sortable", "cell", "a", "onClick", "style", "cursor", "i", "className", "requestedColumns", "div", "<PERSON><PERSON>", "variant", "size", "sortSubscribeRequestTousers", "rows", "field", "direction", "_", "row", "toLowerCase", "membersDeleteHandler", "filteredMemberss", "filteredSubscribers", "subscribers", "patch", "nonMembers", "modalHanlder", "<PERSON><PERSON><PERSON><PERSON>", "choosenNonMembers", "choosenMembers", "old<PERSON><PERSON><PERSON>", "Container", "Row", "Col", "h4", "Link", "href", "as", "title", "FontAwesomeIcon", "icon", "faPen", "deleteVspaceHandler", "faTrashAlt", "color", "lg", "h6", "span", "RKITable", "<PERSON><PERSON><PERSON><PERSON>", "dense", "paginationServer", "pagServer", "paginationTotalRows", "subHeader", "subHeaderAlign", "pagination", "persistTableHead", "sortFunction", "Select", "closeMenuOnSelect", "components", "is<PERSON><PERSON><PERSON>", "placeholder", "onChange", "selected", "userHandler", "allOption", "options", "small", "CreatableSelect", "isClearable", "menuIsOpen", "handleChange", "onInputChange", "handleInputChange", "inputchanges", "onKeyDown", "Spinner", "animation", "Modal", "show", "onHide", "Header", "closeButton", "Title", "Body", "modalRejectHanldler", "modalConfirmHandler", "defaultProps"], "sourceRoot": "", "ignoreList": []}