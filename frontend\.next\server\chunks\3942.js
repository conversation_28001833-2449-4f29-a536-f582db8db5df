"use strict";exports.id=3942,exports.ids=[3942],exports.modules={23942:(e,t,r)=>{r.a(e,async(e,a)=>{try{r.r(t),r.d(t,{default:()=>R});var s=r(8732),n=r(82015),i=r.n(n),l=r(7082),o=r(18597),d=r(83551),c=r(49481),u=r(59549),h=r(91353),p=r(96158),x=r(72521),j=r(11e3),A=r(44233),m=r.n(A),g=r(19918),_=r.n(g),y=r(74716),f=r.n(y),b=r(27825),v=r.n(b),w=r(82053),S=r(54131),k=r(42893),C=r(23579),N=r(66994),P=r(58070),L=r(63487),M=r(88751),G=r(88271),T=r(24047),I=e([S,k,L,G]);[S,k,L,G]=I.then?(await I)():I;let O={title:"",website:"",funded_by:"",status:null,countryTerritory:"",start_date:null,end_date:null,area_of_work:[],description:"",institution_invites:[],partner_institutions:[],checked:!1},R=e=>{let t=(0,n.useRef)(null),r=(0,n.useRef)(null),{t:a,i18n:A}=(0,M.useTranslation)("common"),g="de"===A.language?{title_de:"asc"}:{title:"asc"},y=A.language,[b,I]=(0,n.useState)(O),[R]=(0,n.useState)({invitesCountry:[],invitesRegion:[],invitesOrganisationType:[],invitesOrganisation:[],invitesExpertise:[],invitesNetWork:[],userList:[],visibility:!0}),[W,q]=(0,n.useState)([]),[D,E]=(0,n.useState)([]),[,B]=(0,n.useState)([]),[,F]=(0,n.useState)([]),[K,V]=(0,n.useState)([]),[,U]=(0,n.useState)([]),[,z]=(0,n.useState)([]),[H,J]=(0,n.useState)(!1),[Q,X]=(0,n.useState)(!1),[Y,Z]=(0,n.useState)([]),[ee,et]=(0,n.useState)(null),er=e.routes&&"edit"==e.routes[0]&&e.routes[1],[ea,es]=(0,n.useState)(!0),[en,ei]=(0,n.useState)([{partner_country:"",regions:[],partner_region:[],institutions:[],partner_institution:[],countryregions:[],world_region:""}]),[el,eo]=(0,n.useState)([{title:"",contact_name:"",email:"",_id:null}]),[ed,ec]=(0,n.useState)(1),[eu,eh]=(0,n.useState)(""),[ep,ex]=(0,n.useState)(""),ej={query:{},sort:g,limit:"~",languageCode:y},eA=async e=>{let t=await L.A.get("/projectstatus",e);t&&Array.isArray(t.data)&&q(t.data),await $(e,E,F);let r=await L.A.get("/institution",e);if(r&&Array.isArray(r.data)){let e=r.data.map(e=>{if("Request Pending"!==e.status)return{label:e.title,value:e._id}});V(v().compact(e))}let a=await L.A.get("/institutiontype",e);a&&Array.isArray(a.data)&&U(a.data);let s=await L.A.get("/institutionnetwork",e);s&&Array.isArray(s.data)&&z(s.data);let n=await L.A.get("/areaofwork",e),i=[];n&&Array.isArray(n.data)&&(i=n.data.map((e,t)=>({label:e.title,value:e._id})),Z(i))};(0,n.useEffect)(()=>{er&&(async t=>{let r=await L.A.get(`/project/${e.routes[1]}`,t);if(r){let e=[],{status:a,country:s,area_of_work:n,partner_institutions:i,start_date:l,end_date:o}=r;return function(e,t,r,a,s,n){e.start_date=t?f()(t).toDate():null,e.end_date=r?f()(r).toDate():null,e.status=a&&a._id?a._id:null,e.country=s&&s._id?s._id:null,e.area_of_work=n&&n.length>0?n.map((e,t)=>({label:e.title,value:e._id})):[]}(r,l,o,a,s,n),function(e,t,r,a){e&&e.forEach(async(e,s)=>{let n=e.partner_region&&e.partner_region.map((e,t)=>({label:e.title,value:e._id})),i=e.partner_region&&e.partner_region.map((e,t)=>e._id),l=e.partner_institution&&e.partner_institution.map(e=>({label:e.title,value:e._id})),o=e.partner_institution&&e.partner_institution.map(e=>e._id);t.push({partner_country:e.partner_country._id,regions:n,partner_region:i,institutions:l,partner_institution:o,world_region:e.partner_country.world_region._id,countryregions:await r(e.partner_country._id,a)})})}(i,e,eg,t),ei(e),I(e=>({...e,...r})),o&&I(e=>({...e,checked:!0}))}})(ej),eA(ej),em()},[]);let em=async()=>{let e=await L.A.post("/users/getLoggedUser",{});e&&e.roles&&((e.roles?.filter(e=>"EMT_NATIONAL_FOCALPOINT"==e||"NGOS"===e)).length>0?es(!1):es(!0))},eg=async(e,t)=>{let r=[];if(e){let a=await L.A.get(`/country_region/${e}`,t);a&&a.data&&(r=a.data.map((e,t)=>({label:e.title,value:e._id}))).sort((e,t)=>e.label.localeCompare(t.label))}return r},e_=(e,t)=>{"start_date"==t&&null==e&&I(t=>({...t,end_date:e,start_date:e})),I(r=>({...r,[t]:e}))},ey=(0,n.useRef)(null),ef=()=>{let e=[...el];e.push({title:"",contact_name:"",email:"",_id:null}),eo(e),ec(e.length)},eb=(e,t)=>{el.splice(t,1);let r=[...el];eo(r),ec(r.length),0===el.length&&ef()},ev=()=>{let e={partner_country:"",world_region:"",regions:[],partner_region:[],institutions:[],partner_institution:[],countryregions:[]};ei(t=>[...t,e])},ew=(e,t)=>{en.splice(t,1),ei([...en]),0===en.length&&ev()},eS=e=>{I(t=>({...t,description:e}))};i().useEffect(()=>{if(R){let e={};Object.keys(R).forEach((t,r)=>{let a=R[t].length>0&&R[t].map((e,t)=>e.value);e[t]=a||[]}),ek(e)}else console.log("No threshold reached.")},[R]);let ek=async e=>{let{invitesCountry:t,invitesOrganisation:r}=e,a=await L.A.post("/user-invite",{query:{country:t,institution:r}});if(a&&Array.isArray(a)){let e=a.map((e,t)=>({label:e.username,value:e._id}));B(e)}},eC=async(e,t,r)=>{if(e.target){let{name:r,value:a}=e.target;en[t][r]=a,"partner_country"==r&&(en[t].world_region=e.target[e.target.selectedIndex].getAttribute("data-worldregion"),en[t].countryregions=await eg(a,ej),en[t].regions=[])}else"countries_regions"==r&&(en[t].regions=e,en[t].partner_region=e.map((e,t)=>e.value)),"partner_institutions"==r&&(en[t].institutions=e,en[t].partner_institution=e.map((e,t)=>e.value),console.log(en[t].institutions),en[t].institutions.length?eh(""):eh(a("toast.PartnerInstitutionshouldnotbeempty")));ei([...en])},eN=e=>{let{name:t,value:r}=e.target;e.target&&I(e=>({...e,[t]:r}))},eP=async s=>{if(s.preventDefault(),0==b.area_of_work.length&&0==en[0].partner_institution.length){k.default.error(a("toast.AreaofWorkandPartnerInstitutionshouldnotbeempty")),window.scrollTo(0,0),eh(a("toast.PartnerInstitutionshouldnotbeempty")),ex(a("toast.AreaofWorkshouldnotbeempty"));return}if(0==b.area_of_work.length){k.default.error(a("toast.AreaofWorkshouldnotbeempty")),window.scrollTo(0,0),ex(a("toast.AreaofWorkshouldnotbeempty"));return}if(0==en[0].partner_institution.length){k.default.error(a("toast.PartnerInstitutionshouldnotbeempty")),window.scrollTo(0,0),ex(a("toast.PartnerInstitutionshouldnotbeempty"));return}if(null==b.start_date)r.current?.focus();else{let r,n;t.current&&t.current.setAttribute("disabled","disabled");let i=v().map(en,v().partialRight(v().pick,["partner_country","partner_region","partner_institution","world_region"]));b.area_of_work=b.area_of_work?b.area_of_work.map((e,t)=>e.value):[],b.institution_invites=el,b.partner_institutions=i,s.preventDefault(),er?(n="toast.Projectupdatedsuccessfully",r=await L.A.patch(`/project/${e.routes[1]}`,b)):(n="toast.Projectaddedsuccessfully",r=await L.A.post("/project",b)),function(e,t,r,a,s,n){e&&e._id?t?(r(e?._id&&e._id),a(!0)):(k.default.success(s(n)),m().push("/project/[...routes]",`/project/show/${e._id}`)):k.default.error(e)}(r,H,et,X,a,n)}};return(0,s.jsxs)(l.A,{className:"formCard",fluid:!0,children:[(0,s.jsx)(o.A,{children:(0,s.jsx)(N.A,{onSubmit:eP,ref:ey,children:(0,s.jsxs)(o.A.Body,{children:[(0,s.jsx)(d.A,{children:(0,s.jsx)(c.A,{children:(0,s.jsx)(o.A.Title,{children:er?a("editProject"):a("addProject")})})}),(0,s.jsx)("hr",{}),(0,s.jsxs)(d.A,{className:"mb-3",children:[(0,s.jsx)(c.A,{md:6,lg:6,sm:12,children:(0,s.jsxs)(u.A.Group,{children:[(0,s.jsx)(u.A.Label,{className:"required-field",children:a("Title")}),(0,s.jsx)(C.ks,{name:"title",id:"title",required:!0,value:b.title,validator:e=>""!=e.trim(),errorMessage:{validator:a("PleaseAddtheTitle")},onChange:eN})]})}),(0,s.jsx)(c.A,{md:6,lg:6,sm:12,children:(0,s.jsxs)(u.A.Group,{children:[(0,s.jsx)(u.A.Label,{children:a("Website")}),(0,s.jsx)(C.ks,{name:"website",id:"website",pattern:"^(?:http(s)?:\\/\\/)?[\\w.-]+(?:\\.[\\w\\.-]+)+[\\w\\-\\._~:/?#[\\]@!\\$&'\\(\\)\\*\\+,;=.]+$",errorMessage:{pattern:a("Pleaseentervalidwebsite")},value:b.website,onChange:eN})]})})]}),(0,s.jsx)(d.A,{className:"mb-3",children:(0,s.jsx)(c.A,{children:(0,s.jsxs)(u.A.Group,{children:[(0,s.jsx)(u.A.Label,{children:a("Description")}),(0,s.jsx)(T.x,{initContent:b.description,onChange:e=>eS(e)})]})})}),(0,s.jsxs)(d.A,{className:"d-flex align-items-center mb-3",children:[(0,s.jsx)(c.A,{md:6,lg:6,sm:12,children:(0,s.jsxs)(u.A.Group,{style:{maxWidth:"500px"},children:[(0,s.jsx)(u.A.Label,{className:"required-field",children:a("AreaofWorkthisprojectcovers")}),(0,s.jsx)(j.MultiSelect,{overrideStrings:{selectSomeItems:a("SelectAreaofwork"),allItemsAreSelected:a("AllAreaofwork'sareSelected")},options:Y,value:b.area_of_work,onChange:(e,t)=>{I(t=>({...t,area_of_work:e})),e.length?ex(""):ex(a("toast.AreaofWorkshouldnotbeempty"))},className:"project-covers",labelledBy:a("Selectareaofwork")}),ep&&(0,s.jsx)("p",{style:{color:"red"},children:ep})]})}),(0,s.jsx)(c.A,{md:6,lg:6,sm:12,children:(0,s.jsxs)(u.A.Group,{children:[(0,s.jsx)(u.A.Label,{children:a("FundedBy")}),(0,s.jsx)(C.ks,{name:"funded_by",id:"funded_by",value:b.funded_by,onChange:eN})]})})]}),(0,s.jsxs)(d.A,{className:"mb-3",children:[(0,s.jsx)(c.A,{lg:3,sm:12,children:(0,s.jsxs)(u.A.Group,{children:[(0,s.jsx)(u.A.Label,{children:a("ProjectStatus")}),(0,s.jsxs)(C.s3,{name:"status",id:"status",value:null===b.status?"":b.status,onChange:eN,children:[(0,s.jsx)("option",{value:"",children:a("SelectProjectStatus")}),W.map((e,t)=>(0,s.jsx)("option",{value:e._id,children:e.title},t))]})]})}),(0,s.jsx)(c.A,{lg:3,sm:4,className:"align-self-center",children:(0,s.jsxs)(u.A.Group,{children:[(0,s.jsx)(d.A,{children:(0,s.jsx)(c.A,{children:(0,s.jsx)(u.A.Label,{className:"required-field",children:a("StartDate")})})}),(0,s.jsx)("label",{className:"date-validation w-100",ref:r,children:(0,s.jsx)(P.A,{selected:b.start_date,onChange:e=>e_(e,"start_date"),dateFormat:"MMMM d, yyyy",placeholderText:a("SelectStartDate")})})]})}),(0,s.jsx)(c.A,{lg:2,sm:4,children:(0,s.jsx)(u.A.Check,{type:"checkbox",checked:b.checked,onChange:()=>{I(e=>({...e,checked:!e.checked,end_date:null}))},label:a("ShowEndDate")})}),b.checked&&(0,s.jsx)(c.A,{lg:3,sm:4,className:"align-self-center",children:(0,s.jsxs)(u.A.Group,{children:[(0,s.jsx)(d.A,{children:(0,s.jsx)(c.A,{children:(0,s.jsx)(u.A.Label,{children:a("EndDate")})})}),(0,s.jsx)(P.A,{selected:b.end_date,disabled:!b.start_date,onChange:e=>e_(e,"end_date"),dateFormat:"MMMM d, yyyy",minDate:b.start_date,placeholderText:a("SelectEndDate")})]})})]}),en.map((e,t)=>(0,s.jsxs)("div",{children:[(0,s.jsx)(c.A,{className:"header-block pb-1 pt-2",lg:12,children:(0,s.jsx)("h6",{children:(0,s.jsxs)("span",{children:[a("Country")," ",t+1]})})}),(0,s.jsxs)(d.A,{className:"mb-3",children:[(0,s.jsx)(c.A,{lg:4,sm:6,children:(0,s.jsxs)(u.A.Group,{children:[(0,s.jsx)(u.A.Label,{className:"required-field",children:a("CountryWheretheProjectistakingplace")}),(0,s.jsxs)(C.s3,{name:"partner_country",id:"partner_country",value:e.partner_country,onChange:e=>eC(e,t,"countries"),required:!0,errorMessage:a("thisfieldisrequired"),children:[(0,s.jsx)("option",{value:"",children:a("SelectCountry")}),D.map((e,t)=>(0,s.jsx)("option",{"data-worldregion":e.world_region._id,value:e._id,children:e.title},t))]})]})}),(0,s.jsx)(c.A,{lg:4,sm:6,children:(0,s.jsxs)(u.A.Group,{className:"mw-100",children:[(0,s.jsx)(u.A.Label,{children:a("CountryRegions")}),(0,s.jsx)(j.MultiSelect,{overrideStrings:{selectSomeItems:a("SelectRegions"),allItemsAreSelected:a("AllRegionsareSelected")},options:e.countryregions,value:e.regions,onChange:e=>eC(e,t,"countries_regions"),className:"region",labelledBy:a("SelectRegions")})]})}),(0,s.jsx)(c.A,{lg:4,sm:6,children:(0,s.jsxs)(u.A.Group,{style:{maxWidth:"400px"},children:[(0,s.jsx)(u.A.Label,{className:"required-field",children:a("PartnerOrganisations(onplatform)")}),(0,s.jsx)(j.MultiSelect,{overrideStrings:{selectSomeItems:a("SelectOrganisations"),allItemsAreSelected:a("AllOrganisationsareselected")},options:K,value:e.institutions,onChange:e=>eC(e,t,"partner_institutions"),className:"organisation",labelledBy:a("SelectOrganisations")}),eu&&(0,s.jsx)("p",{style:{color:"red"},children:eu})]})})]}),(0,s.jsx)("div",{children:0===t?(0,s.jsx)("span",{}):(0,s.jsx)(d.A,{className:"mb-4",children:(0,s.jsx)(c.A,{xs:!0,lg:"4",children:(0,s.jsx)(h.A,{variant:"secondary",onClick:e=>ew(e,t),children:a("Remove")})})})})]},t)),(0,s.jsx)(d.A,{children:(0,s.jsx)(c.A,{xs:!0,lg:"4",children:(0,s.jsx)(h.A,{variant:"secondary",onClick:ev,children:a("AddAnotherCountry")})})}),(0,s.jsx)("hr",{}),(0,s.jsx)(d.A,{children:(0,s.jsx)(c.A,{children:(0,s.jsxs)(u.A.Group,{children:[(0,s.jsx)(u.A.Label,{children:a("PartnerOrganisationnotlisted?Createnewandinvite")}),(0,s.jsxs)(p.A,{activeKey:ed,onSelect:e=>ec(e),id:"uncontrolled-tab-example",children:[" ",el.map((e,t)=>(0,s.jsxs)(x.A,{eventKey:`${t+1}`,title:`Organisation ${t+1}`,children:[(0,s.jsxs)(d.A,{children:[(0,s.jsx)(c.A,{lg:4,sm:6,children:(0,s.jsxs)(u.A.Group,{className:"pt-4",children:[(0,s.jsx)(u.A.Label,{children:a("OrganisationName")}),(0,s.jsx)(C.ks,{name:`input${t+1}-title`,id:`input${t+1}`,value:e.title,onChange:e=>(function(e,t){let r=[...el];r[t].title=e.target.value,eo(r)})(e,t)})]})}),(0,s.jsx)(c.A,{lg:4,sm:6,children:(0,s.jsxs)(u.A.Group,{className:"pt-4",children:[(0,s.jsx)(u.A.Label,{children:a("ContactName")}),(0,s.jsx)(C.ks,{name:`input${t+1}-contact_name`,id:`input${t+1}`,value:e.contact_name,onChange:e=>(function(e,t){let r=[...el];r[t].contact_name=e.target.value,eo(r)})(e,t)})]})}),(0,s.jsx)(c.A,{lg:4,sm:6,children:(0,s.jsxs)(u.A.Group,{className:"pt-4",children:[(0,s.jsx)(u.A.Label,{children:a("E-MailAddress")}),(0,s.jsx)(C.ks,{name:`input${t+1}`,id:`input${t+1}`,value:e.email,onChange:e=>(function(e,t){let r=[...el];r[t].email=e.target.value,eo(r)})(e,t),pattern:"^[^@]+@[^@]+\\.[^@]+$",errorMessage:{pattern:a("Pleaseenteravalidemail")}})]})})]}),(0,s.jsx)("div",{children:0===t?(0,s.jsx)("span",{}):(0,s.jsx)(c.A,{xs:!0,lg:"4",className:"p-0",children:(0,s.jsx)(h.A,{onSelect:e=>ec(e),variant:"secondary",onClick:e=>eb(e,t),children:a("Remove")})})})]},t)),(0,s.jsx)(x.A,{eventKey:"add",title:(0,s.jsx)("div",{children:(0,s.jsxs)("span",{onClick:ef,children:[" ",(0,s.jsx)(w.FontAwesomeIcon,{icon:S.faPlus,color:"#808080"})]})})})]})]})})}),(0,s.jsx)(d.A,{className:"mt-4",children:(0,s.jsxs)(c.A,{children:[(0,s.jsx)(o.A.Text,{children:(0,s.jsx)("b",{children:a("VirtualSpace")})}),(0,s.jsx)("hr",{}),(0,s.jsx)(u.A.Check,{className:"pb-4",disabled:!ea,type:"checkbox",onChange:()=>J(!H),name:"virtula",checked:H,label:a("WouldliketocreateaVirtualSpace")})]})}),(0,s.jsx)(d.A,{className:"my-4",children:(0,s.jsxs)(c.A,{children:[(0,s.jsx)(h.A,{className:"me-2",type:"submit",variant:"primary",ref:t,onClick:eP,children:a("submit")}),(0,s.jsx)(h.A,{className:"me-2",onClick:()=>{I(O),ei([]),eo([]),window.scrollTo(0,0)},variant:"info",children:a("reset")}),(0,s.jsx)(_(),{href:"/project",as:"/project",children:(0,s.jsx)(h.A,{variant:"secondary",children:a("Cancel")})})]})})]})})}),Q&&(0,s.jsx)(G.A,{type:"Project",id:ee})]})};async function $(e,t,r){let a=await L.A.get("/country",e);a&&Array.isArray(a.data)&&t(a.data);let s=await L.A.get("/expertise",e);s&&Array.isArray(s.data)&&r(s.data)}a()}catch(e){a(e)}})}};