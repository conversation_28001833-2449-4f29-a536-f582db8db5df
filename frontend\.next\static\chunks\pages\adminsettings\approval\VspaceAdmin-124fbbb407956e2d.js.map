{"version": 3, "file": "static/chunks/pages/adminsettings/approval/VspaceAdmin-124fbbb407956e2d.js", "mappings": "gFACA,4CACA,sCACA,WACA,OAAe,EAAQ,KAA2D,CAClF,EACA,SAFsB,oGCiCtB,SAASA,EAASC,CAAoB,EACpC,GAAM,CAAEC,GAAC,CAAE,CAAGC,CAAAA,EAAAA,EAAAA,EAAAA,CAAcA,CAAC,UACvBC,EAA6B,CACjCC,gBAAiBH,EAAE,cACnB,EACI,CACJI,SAAO,MACPC,CAAI,WACJC,CAAS,uBACTC,CAAqB,WACrBC,CAAS,oBACTC,CAAkB,qBAClBC,CAAmB,kBACnBC,CAAgB,CAChBC,aAAW,oBACXC,CAAkB,gBAClBC,CAAc,SACdC,CAAO,WACPC,CAAS,sBACTC,CAAoB,CACpBC,mBAAiB,YACjBC,CAAU,QACVC,CAAM,kBACNC,CAAgB,cAChBC,CAAY,CAEZ,CADA,EACGC,EACJ,CAAGxB,EAGEyB,EAAiB,4BACrBtB,EACAuB,gBAAiBzB,EAAE,IAP0C,MAQ7D0B,UAAU,UACVtB,EACAC,KAAMA,GAAQ,EAAE,CAChBsB,MAAO,GACPC,2BAA4BrB,EAC5BsB,UAAWrB,EACXsB,gBAAiBf,qBACjBN,EACAsB,YAAY,EACZC,iBAAkBhB,EAClBiB,kBAAmBpB,GAA0C,GAC7DqB,eADwCrB,cACVD,GAA4B,CAAC,GAAI,GAAI,GAAI,CAA3BA,EAA+B,GAAG,CAC9EuB,oBAAqB7B,EACrB8B,oBAAqB1B,EACrB2B,aAAc1B,iBACdG,uBACAG,oBACAC,EACAoB,kBAAmB,UAACC,EAAAA,CAAYA,CAAAA,CAAAA,GAChCC,SAAU,UAACC,IAAAA,CAAEC,UAAU,6CACvBvB,SACAC,eACAE,mBACAD,EACAqB,UAAW,WACb,EACA,MACE,UAACC,EAAAA,EAASA,CAAAA,CAAE,GAAGnB,CAAc,EAEjC,CAEA1B,EAAS8C,YAAY,CAAG,CACtBf,WAAW,EACXE,YAAY,EACZzB,UAAW,KACXU,WAAW,EACXC,qBAAsB,KACtBC,mBAAmB,EACnBC,YAAY,EACZE,kBAAkB,CACpB,EAEA,MAAevB,QAAQA,EAAC,gJC0DxB,MAhKA,SAAqB+C,CAAW,EAC9B,GAAM,GAAE7C,CAAC,CAAE,CAAGC,CAAAA,EAAAA,EAAAA,EAAAA,CAAcA,CAAC,SA+JL6C,CA9JlB,CAACC,EAAWC,EAAe,CAAGC,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAQ,EAAE,EAChD,EAAGC,EAAW,CAAGD,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,EAAU,GACnC,CAAC3C,EAAW6C,EAAa,CAAGF,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAS,GAC7C,CAACG,EAASC,EAAW,CAAGJ,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAS,IACzC,CAACK,EAAaC,EAAS,CAAGN,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,EAAU,GAC5C,CAACO,EAAWC,EAAa,CAAGR,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAS,IAC7C,CAACS,EAAmBC,EAAqB,CAAGV,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAM,CAAC,GAG3DW,EAAc,CAClBC,KAAM,CAAEC,WAAY,MAAO,EAC3BC,MAAOX,EACPY,KAAM,EACNC,MAAO,CAAEC,cAAe,iBAAkB,CAC5C,EAEM9D,EAAU,CACd,CACE+D,KAAMnE,EAAE,kDACRoE,SAAU,WACVC,KAAM,GAAYC,EAAEC,QAAQ,EAE9B,CACEJ,KAAMnE,EAAE,+CACRoE,SAAU,QACVC,KAAM,GAAYC,EAAEE,KAAK,EAE3B,CACEL,KAAMnE,EAAE,gDACRoE,SAAU,GACVC,KAAM,GACJ,WAACI,MAAAA,WACC,UAACC,EAAAA,CAAMA,CAAAA,CACLC,QAAQ,UACRC,KAAK,KACLC,QAAS,IAAMC,EAAWR,EAAG,oBAE5BtE,EAAE,iDACI,OAET,UAAC0E,EAAAA,CAAMA,CAAAA,CACLC,QAAQ,YACRC,KAAK,KACLC,QAAS,IAAMC,EAAWR,EAAG,mBAE5BtE,EAAE,oDAIX,EACD,CAEK+E,EAAe,UAEnB7B,GAAW,GACX,IAAM8B,EAAW,MAAMC,EAAAA,CAAUA,CAACC,GAAG,CAAC,SAAUtB,GAC5CoB,GAAYA,EAAS3E,IAAI,EAAE,CAC7B2C,EAAegC,EAAS3E,IAAI,EAC5B8C,EAAa6B,EAASG,UAAU,EAChCjC,GAAW,GAEf,EAOMxC,EAAsB,MAAO0E,EAAoBpB,KACrDJ,EAAYG,KAAK,CAAGqB,EACpBxB,EAAYI,IAAI,CAAGA,EACnBd,GAAW,GACX,IAAM8B,EAAW,MAAMC,EAAAA,CAAUA,CAACC,GAAG,CAAC,SAAUtB,GAC5CoB,GAAYA,EAAS3E,IAAI,EAAI2E,EAAS3E,IAAI,CAACgF,MAAM,CAAG,GAAG,CACzDrC,EAAegC,EAAS3E,IAAI,EAC5BgD,EAAW+B,GACXlC,GAAW,GAEf,EAEAoC,CAAAA,EAAAA,EAAAA,SAAAA,CAASA,CAAC,KACRP,GACF,EAAG,EAAE,EAEL,IAAMD,EAAa,MAAOR,EAAQiB,KAChChC,GAAS,GACTE,EAAa8B,GACTjB,GAAKA,EAAEkB,GAAG,EAAE,EAEO,CAAE,GAAGlB,CAAC,CAAEJ,cADA,CACeuB,WAD1BF,EAAuB,WAAY,UACC,EAG1D,EAEMG,EAAe,UAEnB,GAA2C,YAAY,CAAnDhC,EAAkB,aAAgB,CAEpC,CAFmB,KAEbuB,EAAAA,CAAUA,CAACU,MAAM,CAAC,UAAmC,OAAzBjC,EAAkB,GAAM,GAC1DqB,IACAa,EAAAA,EAAKA,CAF8C,KAExC,CAAC5F,EAAE,mDACd2D,EAAqB,CAAC,GACtBJ,GAAS,OAEJ,CACL,IAAMsC,EAAc,MAAMZ,EAAAA,CAAUA,CAACa,KAAK,CACxC,UAAmC,OAAzBpC,EAAkB,GAAM,EAClCA,GAEF,GAAImC,GAAsC,CAHb,KAGVA,EAAYN,MAAM,CAAU,YAC7CK,EAAAA,EAAKA,CAACG,KAAK,CACTF,EAAYb,QAAQ,EAAIa,EAAYb,QAAQ,CAACgB,OAAO,CAChDH,EAAYb,QAAQ,CAACgB,OAAO,CAC5BhG,EAAE,8DAIR+E,IACAa,EAAAA,EAAKA,CAACK,OAAO,CAACjG,EAAE,oDAChB2D,EAAqB,CAAC,GACtBJ,EAAS,GAEb,CACF,EAEM2C,EAAY,IAAM3C,GAAS,GAEjC,MACE,WAACkB,MAAAA,WACC,WAAC0B,EAAAA,CAAKA,CAAAA,CAACC,KAAM9C,EAAa+C,OAAQH,YAChC,UAACC,EAAAA,CAAKA,CAACG,MAAM,EAACC,WAAW,aACvB,WAACJ,EAAAA,CAAKA,CAACK,KAAK,YACThD,EAAUiD,MAAM,CAAC,GAAGC,WAAW,GAAKlD,EAAUmD,KAAK,CAAC,GAAG,IAAE3G,EAAE,mDAGhE,WAACmG,EAAAA,CAAKA,CAACS,IAAI,YAAE5G,EAAE,0DAA0D,IAAEwD,EAAU,IAAExD,EAAE,sDACzF,WAACmG,EAAAA,CAAKA,CAACU,MAAM,YACX,UAACnC,EAAAA,CAAMA,CAAAA,CAACC,QAAQ,YAAYE,QAASqB,WAClClG,EAAE,kDAEL,UAAC0E,EAAAA,CAAMA,CAAAA,CAACC,QAAQ,UAAUE,QAASa,WAChC1F,EAAE,qDAKT,UAACF,EAAAA,CAAQA,CAAAA,CACPM,QAASA,EACTC,KAAM0C,EACNzC,UAAWA,EACXU,WAAW,EACXN,oBAAqBA,EACrBC,iBA1FmB,CA0FDA,GAzFtBiD,EAAYG,KAAK,CAAGX,EACpBQ,EAAYI,IAAI,CAAGA,EACnBe,GACF,MA0FF", "sources": ["webpack://_N_E/?1fb3", "webpack://_N_E/./components/common/RKITable.tsx", "webpack://_N_E/./pages/adminsettings/approval/VspaceAdmin.tsx"], "sourcesContent": ["\n    (window.__NEXT_P = window.__NEXT_P || []).push([\n      \"/adminsettings/approval/VspaceAdmin\",\n      function () {\n        return require(\"private-next-pages/adminsettings/approval/VspaceAdmin.tsx\");\n      }\n    ]);\n    if(module.hot) {\n      module.hot.dispose(function () {\n        window.__NEXT_P.push([\"/adminsettings/approval/VspaceAdmin\"])\n      });\n    }\n  ", "//Import Library\r\nimport React from 'react'\r\nimport DataTable  from 'react-data-table-component';\r\n\r\n//Import services/components\r\nimport { useTranslation } from 'next-i18next';\r\nimport CustomLoader from './CustomLoader';\r\n\r\ninterface RKITableProps {\r\n  columns: any[];\r\n  data: any[];\r\n  totalRows?: number;\r\n  resetPaginationToggle?: boolean;\r\n  subheader?: boolean;\r\n  subHeaderComponent?: React.ReactNode;\r\n  handlePerRowsChange?: (newPerPage: number, page: number) => void;\r\n  handlePageChange?: (page: number) => void;\r\n  rowsPerPage?: number[];\r\n  defaultRowsPerPage?: number;\r\n  selectableRows?: boolean;\r\n  loading?: boolean;\r\n  pagServer?: boolean;\r\n  onSelectedRowsChange?: (selectedRows: any) => void;\r\n  clearSelectedRows?: boolean;\r\n  sortServer?: boolean;\r\n  onSort?: (column: any, sortDirection: string) => void;\r\n  persistTableHead?: boolean;\r\n  sortFunction?: (rows: any[], field: any, direction: 'asc' | 'desc') => any[];\r\n  noHeader?: boolean;\r\n  dense?: boolean;\r\n  pagination?: boolean;\r\n  paginationServer?: boolean;\r\n  paginationTotalRows?: number;\r\n  subHeaderAlign?: string;\r\n  [key: string]: any;\r\n}\r\n\r\nfunction RKITable(props: RKITableProps) {\r\n  const { t } = useTranslation('common');\r\n  const paginationComponentOptions = {\r\n    rowsPerPageText: t('Rowsperpage'),\r\n    };\r\n  const {\r\n    columns,\r\n    data,\r\n    totalRows,\r\n    resetPaginationToggle,\r\n    subheader,\r\n    subHeaderComponent,\r\n    handlePerRowsChange,\r\n    handlePageChange,\r\n    rowsPerPage,\r\n    defaultRowsPerPage,\r\n    selectableRows,\r\n    loading,\r\n    pagServer,\r\n    onSelectedRowsChange,\r\n    clearSelectedRows,\r\n    sortServer,\r\n    onSort,\r\n    persistTableHead,\r\n    sortFunction,\r\n    // Filter out any unknown props that might cause DOM warnings\r\n    ...otherProps\r\n  } = props;\r\n\r\n  // Only pass known DataTable props to avoid DOM warnings\r\n  const dataTableProps = {\r\n    paginationComponentOptions,\r\n    noDataComponent: t(\"NoData\"),\r\n    noHeader: true,\r\n    columns,\r\n    data: data || [],\r\n    dense: true,\r\n    paginationResetDefaultPage: resetPaginationToggle,\r\n    subHeader: subheader,\r\n    progressPending: loading,\r\n    subHeaderComponent,\r\n    pagination: true,\r\n    paginationServer: pagServer,\r\n    paginationPerPage: defaultRowsPerPage ? defaultRowsPerPage : 10,\r\n    paginationRowsPerPageOptions: rowsPerPage ? rowsPerPage : [10, 15, 20, 25, 30],\r\n    paginationTotalRows: totalRows,\r\n    onChangeRowsPerPage: handlePerRowsChange,\r\n    onChangePage: handlePageChange,\r\n    selectableRows,\r\n    onSelectedRowsChange,\r\n    clearSelectedRows,\r\n    progressComponent: <CustomLoader/>,\r\n    sortIcon: <i className=\"sort-icon fas fa-exchange-alt\" />,\r\n    sortServer,\r\n    onSort,\r\n    sortFunction,\r\n    persistTableHead,\r\n    className: \"rki-table\"\r\n  };\r\n  return (\r\n    <DataTable {...dataTableProps} />\r\n  )\r\n};\r\n\r\nRKITable.defaultProps = {\r\n  subHeader: false,\r\n  pagination: true,\r\n  totalRows: null,\r\n  pagServer: true,\r\n  onSelectedRowsChange: null,\r\n  clearSelectedRows: false,\r\n  sortServer: false,\r\n  persistTableHead: false\r\n}\r\n\r\nexport default RKITable;\r\n\r\n", "//Import Library\r\nimport React, { useEffect, useState } from \"react\";\r\nimport { <PERSON><PERSON>, <PERSON><PERSON> } from \"react-bootstrap\";\r\nimport toast from 'react-hot-toast';\r\n\r\n//Import services/components\r\nimport apiService from \"../../../services/apiService\";\r\nimport RKITable from \"../../../components/common/RKITable\";\r\nimport { useTranslation } from 'next-i18next';\r\n\r\nfunction VspaceAdmin(_props: any) {\r\n  const { t } = useTranslation('common');\r\n  const [tabledata, setDataToTable] = useState<any[]>([]);\r\n  const [, setLoading] = useState<boolean>(false);\r\n  const [totalRows, setTotalRows] = useState<number>(0);\r\n  const [perPage, setPerPage] = useState<number>(10);\r\n  const [isModalShow, setModal] = useState<boolean>(false);\r\n  const [newStatus, setNewStatus] = useState<string>(\"\");\r\n  const [selectUserDetails, setSelectUserDetails] = useState<any>({});\r\n\r\n\r\n  const usersParams = {\r\n    sort: { created_at: \"desc\" },\r\n    limit: perPage,\r\n    page: 1,\r\n    query: { vspace_status: \"Request Pending\" },\r\n  };\r\n\r\n  const columns = [\r\n    {\r\n      name: t(\"adminsetting.FocalPointsApprovalTable.Username\"),\r\n      selector: \"username\",\r\n      cell: (d: any) => d.username,\r\n    },\r\n    {\r\n      name: t(\"adminsetting.FocalPointsApprovalTable.Email\"),\r\n      selector: \"email\",\r\n      cell: (d: any) => d.email,\r\n    },\r\n    {\r\n      name: t(\"adminsetting.FocalPointsApprovalTable.Action\"),\r\n      selector: \"\",\r\n      cell: (d: any) => (\r\n        <div>\r\n          <Button\r\n            variant=\"primary\"\r\n            size=\"sm\"\r\n            onClick={() => userAction(d, \"approve\")}\r\n          >\r\n            {t(\"adminsetting.FocalPointsApprovalTable.aprov\")}\r\n          </Button>\r\n          &nbsp;\r\n          <Button\r\n            variant=\"secondary\"\r\n            size=\"sm\"\r\n            onClick={() => userAction(d, \"reject\")}\r\n          >\r\n            {t(\"adminsetting.FocalPointsApprovalTable.Reject\")}\r\n          </Button>\r\n        </div>\r\n      ),\r\n    },\r\n  ];\r\n\r\n  const getUsersData = async () => {\r\n\r\n    setLoading(true);\r\n    const response = await apiService.get(\"/users\", usersParams);\r\n    if (response && response.data) {\r\n      setDataToTable(response.data);\r\n      setTotalRows(response.totalCount);\r\n      setLoading(false);\r\n    }\r\n  };\r\n  const handlePageChange = (page: number) => {\r\n    usersParams.limit = perPage;\r\n    usersParams.page = page;\r\n    getUsersData();\r\n  };\r\n\r\n  const handlePerRowsChange = async (newPerPage: number, page: number) => {\r\n    usersParams.limit = newPerPage;\r\n    usersParams.page = page;\r\n    setLoading(true);\r\n    const response = await apiService.get(\"/users\", usersParams);\r\n    if (response && response.data && response.data.length > 0) {\r\n      setDataToTable(response.data);\r\n      setPerPage(newPerPage);\r\n      setLoading(false);\r\n    }\r\n  };\r\n\r\n  useEffect(() => {\r\n    getUsersData();\r\n  }, []);\r\n\r\n  const userAction = async (d: any, status: string) => {\r\n    setModal(true);\r\n    setNewStatus(status);\r\n    if (d && d._id) {\r\n      const setStatus = status === \"approve\" ? \"Approved\" :\"Rejected\";\r\n      setSelectUserDetails({ ...d, vspace_status: setStatus });\r\n    }\r\n\r\n  };\r\n\r\n  const modalConfirm = async () => {\r\n\r\n    if( selectUserDetails['vspace_status'] === \"Rejected\" ){\r\n\r\n      await apiService.remove(`/users/${selectUserDetails[\"_id\"]}`);\r\n      getUsersData();\r\n      toast.error(t(\"adminsetting.FocalPointsApprovalTable.Rejected\"));\r\n      setSelectUserDetails({});\r\n      setModal(false);\r\n\r\n    } else {\r\n      const updatedData = await apiService.patch(\r\n        `/users/${selectUserDetails[\"_id\"]}`,\r\n        selectUserDetails\r\n      );\r\n      if (updatedData && updatedData.status === 403) {\r\n        toast.error(\r\n          updatedData.response && updatedData.response.message\r\n            ? updatedData.response.message\r\n            : t(\"adminsetting.FocalPointsApprovalTable.Somethingwentswrong\")\r\n        );\r\n        return;\r\n      } else {\r\n        getUsersData();\r\n        toast.success(t(\"adminsetting.FocalPointsApprovalTable.Approvemm\"));\r\n        setSelectUserDetails({});\r\n        setModal(false);\r\n      }\r\n    }\r\n  };\r\n\r\n  const modalHide = () => setModal(false);\r\n\r\n  return (\r\n    <div>\r\n      <Modal show={isModalShow} onHide={modalHide}>\r\n        <Modal.Header closeButton>\r\n          <Modal.Title>\r\n            {newStatus.charAt(0).toUpperCase() + newStatus.slice(1)} {t(\"adminsetting.FocalPointsApprovalTable.User\")}\r\n          </Modal.Title>\r\n        </Modal.Header>\r\n        <Modal.Body>{t(\"adminsetting.FocalPointsApprovalTable.Areyousurewantto\")} {newStatus} {t(\"adminsetting.FocalPointsApprovalTable.thisuser?\")}</Modal.Body>\r\n        <Modal.Footer>\r\n          <Button variant=\"secondary\" onClick={modalHide}>\r\n            {t(\"adminsetting.FocalPointsApprovalTable.Cancel\")}\r\n          </Button>\r\n          <Button variant=\"primary\" onClick={modalConfirm}>\r\n            {t(\"adminsetting.FocalPointsApprovalTable.Yes\")}\r\n          </Button>\r\n        </Modal.Footer>\r\n      </Modal>\r\n\r\n      <RKITable\r\n        columns={columns}\r\n        data={tabledata}\r\n        totalRows={totalRows}\r\n        pagServer={true}\r\n        handlePerRowsChange={handlePerRowsChange}\r\n        handlePageChange={handlePageChange}\r\n      />\r\n    </div>\r\n  );\r\n}\r\n\r\nexport default VspaceAdmin;\r\n"], "names": ["RKITable", "props", "t", "useTranslation", "paginationComponentOptions", "rowsPerPageText", "columns", "data", "totalRows", "resetPaginationToggle", "subheader", "subHeaderComponent", "handlePerRowsChange", "handlePageChange", "rowsPerPage", "defaultRowsPerPage", "selectableRows", "loading", "pagServer", "onSelectedRowsChange", "clearSelectedRows", "sortServer", "onSort", "persistTableHead", "sortFunction", "otherProps", "dataTableProps", "noDataComponent", "<PERSON><PERSON><PERSON><PERSON>", "dense", "paginationResetDefaultPage", "subHeader", "progressPending", "pagination", "paginationServer", "paginationPerPage", "paginationRowsPerPageOptions", "paginationTotalRows", "onChangeRowsPerPage", "onChangePage", "progressComponent", "CustomLoader", "sortIcon", "i", "className", "DataTable", "defaultProps", "_props", "VspaceAdmin", "tabledata", "setDataToTable", "useState", "setLoading", "setTotalRows", "perPage", "setPerPage", "isModalShow", "setModal", "newStatus", "setNewStatus", "selectUserDetails", "setSelectUserDetails", "usersParams", "sort", "created_at", "limit", "page", "query", "vspace_status", "name", "selector", "cell", "d", "username", "email", "div", "<PERSON><PERSON>", "variant", "size", "onClick", "userAction", "getUsersData", "response", "apiService", "get", "totalCount", "newPerPage", "length", "useEffect", "status", "_id", "setStatus", "modalConfirm", "remove", "toast", "updatedData", "patch", "error", "message", "success", "modalHide", "Modal", "show", "onHide", "Header", "closeButton", "Title", "char<PERSON>t", "toUpperCase", "slice", "Body", "Footer"], "sourceRoot": "", "ignoreList": []}