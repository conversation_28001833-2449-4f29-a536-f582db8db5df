{"version": 3, "file": "static/chunks/pages/institution/InstitutionMapQuickInfo-e71b216783cc30e4.js", "mappings": "0MAgGA,MAvFA,SAAiCA,CAAU,EASvC,GAAM,CAACC,EAAqBC,EAAuB,CAAGC,CAAAA,EAAAA,EAAAA,EA8E3CC,MA9E2CD,CAAQA,CAACE,CAP3DC,aAAc,CAqFgBF,EApF9BG,kBAAmB,GACnBC,SAAU,GACVC,iBAAkB,GAClBC,oBAAqB,EACzB,GAGM,GAAEC,CAAC,CAAE,CAAGC,CAAAA,EAAAA,EAAAA,EAAAA,CAAcA,CAAC,UACvBC,EAAyB,MAAOC,IAClC,IAAMC,EAAW,MAAMC,EAAAA,CAAUA,CAACC,GAAG,CAAC,sBAAuBH,GACzDC,GACAb,EAAuBa,EAE/B,EAUA,CAbkB,KAKlBG,CAAAA,EAAAA,EAAAA,SAAAA,CAASA,CAAC,KAMNL,EAL0B,CACtBM,MAAO,CAAEC,OAAQ,CAAEC,IAAK,CAILC,gBAJuB,CAAE,EAC5CC,KAAM,CAAEC,MAAO,KAAM,EACrBC,MAAO,GACX,EAEJ,EAAG,EAAE,EAED,UAACC,MAAAA,CAAIC,UAAU,6BACX,WAACC,EAAAA,CAASA,CAAAA,CAACC,MAAO,CAAEC,WAAY,OAAQ,YACpC,UAACF,EAAAA,CAASA,CAACG,IAAI,WACX,UAACC,IAAIA,CAACC,KAAM,CAAEC,SAAU,cAAe,WAEnC,IAFCF,CAED,MAACN,MAAAA,CAAIC,UAAU,sBACX,UAACD,MAAAA,CAAIC,UAAU,yBACX,UAACQ,MAAAA,CACGC,IAAI,yBACJC,MAAM,KACNC,OAAO,KACPC,IAAI,8BAGZ,WAACC,OAAAA,WACG,UAACC,IAAAA,UAAGxC,EAAoBK,YAAY,GAAK,IAAEK,EAAE,8BAM7D,UAACiB,EAAAA,CAASA,CAACG,IAAI,WACX,WAACC,IAAIA,CAACC,KAAM,CAAEC,SAAU,eAAgBf,MAAO,CAAEuB,OAA5CV,CAAqD/B,EAAoBQ,gBAAgB,CAAG,YAE7F,UAACiB,MAAAA,CAAIC,UAAU,yBACX,UAACQ,MAAAA,CACGC,IAAI,yBACJC,MAAM,KACNC,OAAO,KACPC,IAAI,8BAGZ,WAACC,OAAAA,WACG,UAACC,IAAAA,UAAGxC,EAAoBS,mBAAmB,GAAK,IAAEC,EAAE,+BAKhE,UAACiB,EAAAA,CAASA,CAACG,IAAI,WACX,WAACC,IAAIA,CAACC,KAAM,WAAYU,GAAI,qBAExB,IAFCX,CAED,KAACN,MAAAA,CAAIC,UAAU,yBACX,UAACQ,MAAAA,CACGC,IAAI,yBACJC,MAAM,KACNC,OAAO,KACPC,IAAI,8BAGZ,WAACC,OAAAA,WACG,UAACC,IAAAA,UAAGxC,EAAoBO,QAAQ,GAAK,IAAEG,EAAE,wBAQrE,+JC9FA,uDAcA,SACA,EAAuB,QAAQ,cAC/B,EAAyB,YAAgB,SACzC,IAeA,IAfA,CAEA,WACA,WACA,YACA,OACA,YACA,CAAM,EACN,WAxBA,KAA+C,oBAA0B,SAAY,sBAAuB,2BAA8B,4BAAiC,UAAe,UAwB1L,KAGA,EAAsB,OAAc,GACpC,EAA0B,YAAM,KAChC,EAAyB,gBAAU,CAAC,GAAiB,EACrD,EAAqB,gBAAU,CAAC,GAAU,EAE1C,IACA,eACA,cAEA,oBACA,qBAEA,MAAmB,YAAM,OACzB,MACA,gBACA,kBACA,MAAkB,OAAG,OAAsB,EAAe,8BAC1D,0CACA,8CACA,mBACA,sBACA,UAGA,OAFA,mBACA,oBACA,MAEA,UACA,UACA,gBACA,gBACA,EAyBE,eAAS,MACX,yBACA,kCAA6D,EAAe,uBAC5E,mBACA,CACA,YACA,CAAG,EACH,MAAoB,OAAa,MACjC,MAAsB,SAAI,CAAC,GAAiB,WAC5C,QACA,SAA2B,SAAI,CAAC,GAAU,WAC1C,OACA,OAEA,UAAmB,OAAY,IAC/B,qBACA,oBACA,CAAO,CACP,SAA6B,SAAI,mBAA4B,IAC7D,UA3CA,QAKA,EAHA,GADA,cACA,GAIA,cACA,gBACA,cACA,QACA,KACA,kBACA,gBACA,OACA,KACA,SACA,MACA,CACA,IACA,mBACA,YAAyC,OAAQ,uBACjD,aACA,KACA,EAqBA,MACA,MACA,CAAO,EACP,CAAK,CACL,CAAG,CACH,CAAC,EACD,oBACA,MAAe,iBACf,KAAQ,GAAO,CACd,CAAC,kBC/GF,4CACA,uCACA,WACA,OAAe,EAAQ,KAA4D,CACnF,EACA,SAFsB,oKCMtB,IAAMiC,EAA6BC,EAAAA,UAAgB,CAAC,CAA9B,EAUnBC,QAVkD,CAApB,SAC/BC,CAAQ,QACRC,CAAM,UACNC,CAAQ,UACRC,CAAQ,WACRvB,CAAS,SACTwB,CAAO,QACPC,CAAM,IACNT,CAAE,CACF,GAAG3C,EACJ,GACC+C,EAAWM,CAAAA,EAAAA,EAAAA,EAAAA,CAAkBA,CAACN,EAAU,mBACxC,GAAM,CAACO,EAAcC,EAAK,CAAGC,CAAAA,EAAAA,EAAAA,CAAAA,CAAUA,CAAC,CACtCC,IAAKC,CAAAA,EAAAA,EAAAA,CAAAA,CAAYA,CAACR,EAAUlD,EAAMiC,IAAI,SACtCe,EACA,GAAGhD,CAAK,GAEJ2D,EAAcC,CAAAA,EAAAA,EAAAA,CAAAA,CAAgBA,CAACC,IACnC,GAAIZ,EAAU,CACZY,EAAMC,cAAc,GACpBD,EAAME,eAAe,GACrB,MACF,CACAT,EAAaU,OAAO,CAACH,EACvB,GACIZ,QAA+BgB,IAAnBjE,EAAMkE,KAAwB,GAAhB,GAC5BlE,EAAMkE,QAAQ,CAAG,CAAC,EAClBlE,CAAK,CAAC,gBAAgB,EAAG,GAE3B,IAAMmE,EAAYxB,IAAOS,EAAAA,EAAenB,IAAI,CAAG,IAAM,SAAW,MAAI,CAEpE,MAAoBmC,CAAAA,EAAAA,EAAAA,GAAAA,CAAIA,CAACD,EAAW,CAClCrB,IAAKA,EACL,GAAG9C,CAAK,CACR,GAAGsD,CAAY,CACfU,QAASL,EACThC,UAAW0C,IAAW1C,EAAWoB,EAAUQ,EAAKe,QAAQ,EAAI,SAAUrB,GAAY,WAAYE,GAAW,GAAeA,MAAAA,CAAZJ,EAAS,KAAW,OAARI,GAAWC,GAAU,GAAY,OAATL,EAAS,WAC3J,EACF,GACAH,EAAc2B,WAAW,CAAG,gBCvC5B,IAAM3C,EAAyBiB,EAAAA,QAAb,EAA6B,CAAC,CAAC7C,EAAO8C,KAA3B,IAcvB0B,EAbE,WACJ7C,CAAS,CACToB,SAAU0B,CAAe,SACzBtB,CAAO,YACPuB,CAAU,UACVC,CAAQ,EACR,EACAhC,EAAK,KAAK,CACV,GAAGiC,EACJ,CAAGC,CAAAA,EAAAA,EAAAA,EAAAA,CAAeA,CAAC7E,EAAO,CACzB8E,UAAW,UACb,GACM/B,EAAWM,CAAAA,EAAAA,EAAAA,EAAAA,CAAkBA,CAACoB,EAAiB,cAMrD,OAJIC,IACFF,GAAmC,IAAfE,CADN,CAC4B,aAAe,cAAyB,OAAXA,EAAAA,EAGrDN,CAAAA,EAAAA,EAAAA,GAAAA,CAAIA,CAACW,EAAAA,CAAOA,CAAE,CAChCjC,IAb2J,EAc3J,GAAG8B,CAAe,CAClBjC,GAAIA,EACJhB,UAAW0C,IAAW1C,EAAWoB,EAAUI,GAAW,GAAeA,MAAhDkB,CAAoCtB,EAAS,KAAW,OAARI,GAAWqB,GAAqB,GAAeA,MAAAA,CAAZzB,EAAS,KAAqB,OAAlByB,GAAqBG,GAAY,GAAY,OAAT5B,EAAS,aACnK,EACF,GACAnB,EAAU2C,WAAW,CAAG,YACxB,MAAeS,OAAOC,MAAM,CAACrD,EAAW,CACtCG,KDYaa,CCZPA,EACN,EAAC,QDWyBA,EAAC,GCZRA", "sources": ["webpack://_N_E/./pages/institution/InstitutionMapQuickInfo.tsx", "webpack://_N_E/./node_modules/@restart/ui/esm/Nav.js", "webpack://_N_E/?6961", "webpack://_N_E/./node_modules/react-bootstrap/esm/ListGroupItem.js", "webpack://_N_E/./node_modules/react-bootstrap/esm/ListGroup.js"], "sourcesContent": ["//Import Library\r\nimport { ListGroup } from \"react-bootstrap\";\r\nimport { useEffect, useState } from \"react\";\r\nimport Link from \"next/link\";\r\n\r\n//Import services/components\r\nimport apiService from \"../../services/apiService\";\r\nimport { useTranslation } from 'next-i18next';\r\n\r\nfunction InstitutionMapQuickInfo(props: any) {\r\n    const initialVal = {\r\n        institutions: \"\",\r\n        german_operations: \"\",\r\n        projects: \"\",\r\n        german_countryId: \"\",\r\n        german_institutions: \"\",\r\n    };\r\n\r\n    const [organizationDetails, setOrganizationDetails] = useState(initialVal);\r\n    const { t } = useTranslation('common');\r\n    const getOrganizationDetails = async (operationParams: any) => {\r\n        const response = await apiService.get(\"/stats/institutions\", operationParams);\r\n        if (response) {\r\n            setOrganizationDetails(response);\r\n        }\r\n    };\r\n\r\n    useEffect(() => {\r\n        const InstitutionParams = {\r\n            query: { status: { $ne: \"Request Pending\" } },\r\n            sort: { title: \"asc\" },\r\n            limit: \"~\",\r\n        };\r\n        getOrganizationDetails(InstitutionParams);\r\n    }, []);\r\n    return (\r\n        <div className=\"quick-info-filter\">\r\n            <ListGroup style={{ marginLeft: \"-10px\" }}>\r\n                <ListGroup.Item>\r\n                    <Link href={{ pathname: \"/institution\" }}>\r\n\r\n                        <div className=\"info-item\">\r\n                            <div className=\"quickinfo-img\">\r\n                                <img\r\n                                    src=\"/images/quickinfo1.png\"\r\n                                    width=\"27\"\r\n                                    height=\"30\"\r\n                                    alt=\"Organization Quick Info\"\r\n                                />\r\n                            </div>\r\n                            <span>\r\n                                <b>{organizationDetails.institutions}</b> {t(\"AllOrganisations\")}\r\n                            </span>\r\n                        </div>\r\n\r\n                    </Link>\r\n                </ListGroup.Item>\r\n                <ListGroup.Item>\r\n                    <Link href={{ pathname: \"/institution\", query: { country: organizationDetails.german_countryId } }}>\r\n\r\n                        <div className=\"quickinfo-img\">\r\n                            <img\r\n                                src=\"/images/quickinfo2.png\"\r\n                                width=\"24\"\r\n                                height=\"23\"\r\n                                alt=\"Organization Quick Info\"\r\n                            />\r\n                        </div>\r\n                        <span>\r\n                            <b>{organizationDetails.german_institutions}</b> {t(\"GermanOrganisations\")}\r\n                        </span>\r\n\r\n                    </Link>\r\n                </ListGroup.Item>\r\n                <ListGroup.Item>\r\n                    <Link href={\"/project\"} as={\"/project\"}>\r\n\r\n                        <div className=\"quickinfo-img\">\r\n                            <img\r\n                                src=\"/images/quickinfo3.png\"\r\n                                width=\"24\"\r\n                                height=\"21\"\r\n                                alt=\"Organization Quick Info\"\r\n                            />\r\n                        </div>\r\n                        <span>\r\n                            <b>{organizationDetails.projects}</b> {t(\"Projects\")}\r\n                        </span>\r\n\r\n                    </Link>\r\n                </ListGroup.Item>\r\n            </ListGroup>\r\n        </div>\r\n    );\r\n}\r\n\r\nexport default InstitutionMapQuickInfo;\r\n", "const _excluded = [\"as\", \"onSelect\", \"activeKey\", \"role\", \"onKeyDown\"];\nfunction _objectWithoutPropertiesLoose(r, e) { if (null == r) return {}; var t = {}; for (var n in r) if ({}.hasOwnProperty.call(r, n)) { if (e.indexOf(n) >= 0) continue; t[n] = r[n]; } return t; }\nimport qsa from 'dom-helpers/querySelectorAll';\nimport * as React from 'react';\nimport { useContext, useEffect, useRef } from 'react';\nimport useForceUpdate from '@restart/hooks/useForceUpdate';\nimport useMergedRefs from '@restart/hooks/useMergedRefs';\nimport NavContext from './NavContext';\nimport SelectableContext, { makeEventKey } from './SelectableContext';\nimport TabContext from './TabContext';\nimport { dataAttr, dataProp } from './DataKey';\nimport NavItem from './NavItem';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\n// eslint-disable-next-line @typescript-eslint/no-empty-function\nconst noop = () => {};\nconst EVENT_KEY_ATTR = dataAttr('event-key');\nconst Nav = /*#__PURE__*/React.forwardRef((_ref, ref) => {\n  let {\n      // Need to define the default \"as\" during prop destructuring to be compatible with styled-components github.com/react-bootstrap/react-bootstrap/issues/3595\n      as: Component = 'div',\n      onSelect,\n      activeKey,\n      role,\n      onKeyDown\n    } = _ref,\n    props = _objectWithoutPropertiesLoose(_ref, _excluded);\n  // A ref and forceUpdate for refocus, b/c we only want to trigger when needed\n  // and don't want to reset the set in the effect\n  const forceUpdate = useForceUpdate();\n  const needsRefocusRef = useRef(false);\n  const parentOnSelect = useContext(SelectableContext);\n  const tabContext = useContext(TabContext);\n  let getControlledId, getControllerId;\n  if (tabContext) {\n    role = role || 'tablist';\n    activeKey = tabContext.activeKey;\n    // TODO: do we need to duplicate these?\n    getControlledId = tabContext.getControlledId;\n    getControllerId = tabContext.getControllerId;\n  }\n  const listNode = useRef(null);\n  const getNextActiveTab = offset => {\n    const currentListNode = listNode.current;\n    if (!currentListNode) return null;\n    const items = qsa(currentListNode, `[${EVENT_KEY_ATTR}]:not([aria-disabled=true])`);\n    const activeChild = currentListNode.querySelector('[aria-selected=true]');\n    if (!activeChild || activeChild !== document.activeElement) return null;\n    const index = items.indexOf(activeChild);\n    if (index === -1) return null;\n    let nextIndex = index + offset;\n    if (nextIndex >= items.length) nextIndex = 0;\n    if (nextIndex < 0) nextIndex = items.length - 1;\n    return items[nextIndex];\n  };\n  const handleSelect = (key, event) => {\n    if (key == null) return;\n    onSelect == null ? void 0 : onSelect(key, event);\n    parentOnSelect == null ? void 0 : parentOnSelect(key, event);\n  };\n  const handleKeyDown = event => {\n    onKeyDown == null ? void 0 : onKeyDown(event);\n    if (!tabContext) {\n      return;\n    }\n    let nextActiveChild;\n    switch (event.key) {\n      case 'ArrowLeft':\n      case 'ArrowUp':\n        nextActiveChild = getNextActiveTab(-1);\n        break;\n      case 'ArrowRight':\n      case 'ArrowDown':\n        nextActiveChild = getNextActiveTab(1);\n        break;\n      default:\n        return;\n    }\n    if (!nextActiveChild) return;\n    event.preventDefault();\n    handleSelect(nextActiveChild.dataset[dataProp('EventKey')] || null, event);\n    needsRefocusRef.current = true;\n    forceUpdate();\n  };\n  useEffect(() => {\n    if (listNode.current && needsRefocusRef.current) {\n      const activeChild = listNode.current.querySelector(`[${EVENT_KEY_ATTR}][aria-selected=true]`);\n      activeChild == null ? void 0 : activeChild.focus();\n    }\n    needsRefocusRef.current = false;\n  });\n  const mergedRef = useMergedRefs(ref, listNode);\n  return /*#__PURE__*/_jsx(SelectableContext.Provider, {\n    value: handleSelect,\n    children: /*#__PURE__*/_jsx(NavContext.Provider, {\n      value: {\n        role,\n        // used by NavLink to determine it's role\n        activeKey: makeEventKey(activeKey),\n        getControlledId: getControlledId || noop,\n        getControllerId: getControllerId || noop\n      },\n      children: /*#__PURE__*/_jsx(Component, Object.assign({}, props, {\n        onKeyDown: handleKeyDown,\n        ref: mergedRef,\n        role: role\n      }))\n    })\n  });\n});\nNav.displayName = 'Nav';\nexport default Object.assign(Nav, {\n  Item: NavItem\n});", "\n    (window.__NEXT_P = window.__NEXT_P || []).push([\n      \"/institution/InstitutionMapQuickInfo\",\n      function () {\n        return require(\"private-next-pages/institution/InstitutionMapQuickInfo.tsx\");\n      }\n    ]);\n    if(module.hot) {\n      module.hot.dispose(function () {\n        window.__NEXT_P.push([\"/institution/InstitutionMapQuickInfo\"])\n      });\n    }\n  ", "\"use client\";\n\nimport classNames from 'classnames';\nimport * as React from 'react';\nimport warning from 'warning';\nimport useEventCallback from '@restart/hooks/useEventCallback';\nimport { useNavItem } from '@restart/ui/NavItem';\nimport { makeEventKey } from '@restart/ui/SelectableContext';\nimport { useBootstrapPrefix } from './ThemeProvider';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst ListGroupItem = /*#__PURE__*/React.forwardRef(({\n  bsPrefix,\n  active,\n  disabled,\n  eventKey,\n  className,\n  variant,\n  action,\n  as,\n  ...props\n}, ref) => {\n  bsPrefix = useBootstrapPrefix(bsPrefix, 'list-group-item');\n  const [navItemProps, meta] = useNavItem({\n    key: makeEventKey(eventKey, props.href),\n    active,\n    ...props\n  });\n  const handleClick = useEventCallback(event => {\n    if (disabled) {\n      event.preventDefault();\n      event.stopPropagation();\n      return;\n    }\n    navItemProps.onClick(event);\n  });\n  if (disabled && props.tabIndex === undefined) {\n    props.tabIndex = -1;\n    props['aria-disabled'] = true;\n  }\n  const Component = as || (action ? props.href ? 'a' : 'button' : 'div');\n  process.env.NODE_ENV !== \"production\" ? warning(as || !(!action && props.href), '`action=false` and `href` should not be used together.') : void 0;\n  return /*#__PURE__*/_jsx(Component, {\n    ref: ref,\n    ...props,\n    ...navItemProps,\n    onClick: handleClick,\n    className: classNames(className, bsPrefix, meta.isActive && 'active', disabled && 'disabled', variant && `${bsPrefix}-${variant}`, action && `${bsPrefix}-action`)\n  });\n});\nListGroupItem.displayName = 'ListGroupItem';\nexport default ListGroupItem;", "\"use client\";\n\nimport classNames from 'classnames';\nimport * as React from 'react';\nimport warning from 'warning';\nimport { useUncontrolled } from 'uncontrollable';\nimport BaseNav from '@restart/ui/Nav';\nimport { useBootstrapPrefix } from './ThemeProvider';\nimport ListGroupItem from './ListGroupItem';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst ListGroup = /*#__PURE__*/React.forwardRef((props, ref) => {\n  const {\n    className,\n    bsPrefix: initialBsPrefix,\n    variant,\n    horizontal,\n    numbered,\n    // Need to define the default \"as\" during prop destructuring to be compatible with styled-components github.com/react-bootstrap/react-bootstrap/issues/3595\n    as = 'div',\n    ...controlledProps\n  } = useUncontrolled(props, {\n    activeKey: 'onSelect'\n  });\n  const bsPrefix = useBootstrapPrefix(initialBsPrefix, 'list-group');\n  let horizontalVariant;\n  if (horizontal) {\n    horizontalVariant = horizontal === true ? 'horizontal' : `horizontal-${horizontal}`;\n  }\n  process.env.NODE_ENV !== \"production\" ? warning(!(horizontal && variant === 'flush'), '`variant=\"flush\"` and `horizontal` should not be used together.') : void 0;\n  return /*#__PURE__*/_jsx(BaseNav, {\n    ref: ref,\n    ...controlledProps,\n    as: as,\n    className: classNames(className, bsPrefix, variant && `${bsPrefix}-${variant}`, horizontalVariant && `${bsPrefix}-${horizontalVariant}`, numbered && `${bsPrefix}-numbered`)\n  });\n});\nListGroup.displayName = 'ListGroup';\nexport default Object.assign(ListGroup, {\n  Item: ListGroupItem\n});"], "names": ["props", "organizationDetails", "setOrganizationDetails", "useState", "InstitutionMapQuickInfo", "initialVal", "institutions", "german_operations", "projects", "german_countryId", "german_institutions", "t", "useTranslation", "getOrganizationDetails", "operationParams", "response", "apiService", "get", "useEffect", "query", "status", "$ne", "InstitutionParams", "sort", "title", "limit", "div", "className", "ListGroup", "style", "marginLeft", "<PERSON><PERSON>", "Link", "href", "pathname", "img", "src", "width", "height", "alt", "span", "b", "country", "as", "ListGroupItem", "React", "ref", "bsPrefix", "active", "disabled", "eventKey", "variant", "action", "useBootstrapPrefix", "navItemProps", "meta", "useNavItem", "key", "makeEventKey", "handleClick", "useEventCallback", "event", "preventDefault", "stopPropagation", "onClick", "undefined", "tabIndex", "Component", "_jsx", "classNames", "isActive", "displayName", "horizontalVariant", "initialBsPrefix", "horizontal", "numbered", "controlledProps", "useUncontrolled", "active<PERSON><PERSON>", "BaseNav", "Object", "assign"], "sourceRoot": "", "ignoreList": [1, 3, 4]}