"use strict";(()=>{var e={};e.id=2461,e.ids=[636,2461,3220],e.modules={123:e=>{e.exports=require("dom-helpers/removeEventListener")},1332:e=>{e.exports=require("react-custom-scrollbars-2")},1428:e=>{e.exports=import("axios")},1680:e=>{e.exports=require("@restart/ui/utils")},1919:e=>{e.exports=require("react-transition-group/Transition")},3892:e=>{e.exports=require("classnames")},4048:e=>{e.exports=require("react-truncate")},6009:e=>{e.exports=require("dom-helpers/ownerDocument")},6952:e=>{e.exports=require("@restart/ui/Modal")},7374:e=>{e.exports=require("@restart/ui/Dropdown")},8732:e=>{e.exports=require("react/jsx-runtime")},9653:e=>{e.exports=require("@restart/ui/Overlay")},11242:e=>{e.exports=require("redux-persist/integration/react")},11688:e=>{e.exports=require("@restart/hooks/useWillUnmount")},12388:e=>{e.exports=require("dom-helpers/addEventListener")},13364:e=>{e.exports=require("redux-saga/effects")},14062:e=>{e.exports=import("react-redux")},14078:e=>{e.exports=import("swr")},14332:e=>{e.exports=require("uncontrollable")},16116:e=>{e.exports=require("invariant")},17098:(e,r,s)=>{s.a(e,async(e,t)=>{try{s.r(r),s.d(r,{default:()=>f});var a=s(8732),i=s(19918),n=s.n(i),o=s(82015),p=s.n(o),d=s(59549),l=s(12403),u=s(91353),c=s(27825),m=s.n(c),x=s(42893),h=s(56084),g=s(63487),A=s(94544),y=s(88751),S=e([x,g]);[x,g]=S.then?(await S)():S;let f=()=>{let{t:e,i18n:r}=(0,y.useTranslation)("common"),s="fr"===r.language?"en":r.language,t=s?`title.${s}`:"title.en",[i,c]=(0,o.useState)([]),[,S]=(0,o.useState)(!1),[f,q]=(0,o.useState)(0),[v,w]=(0,o.useState)(10),[_,P]=p().useState(""),[j,C]=p().useState(!1),[b,N]=(0,o.useState)(!1),[D,z]=(0,o.useState)({}),[k]=(0,o.useState)(t),T=async r=>{let t=m().findIndex(i,{_id:r.target.name});if(t>-1){i[t].enabled=!i[t].enabled,c([...i]);let a=await g.A.patch(`/hazard/${r.target.name}`,i[t]);a&&a._id?x.default.success(`${a.title[s]} ${e("updatedSuccessfully")}`):x.default.error(a)}else x.default.error(e("indexNotFound"))},M=({_id:e,enabled:r})=>(0,a.jsx)(d.A.Check,{className:"ms-4",type:"switch",name:e,id:e,label:"",checked:r,onChange:e=>T(e)}),E=[{name:e("menu.hazards"),selector:k,cell:e=>e&&e.title&&e.title[s]?e.title[s]:""},{name:e("hazardType"),selector:"hazard_type",cell:e=>e&&e.hazard_type&&e.hazard_type.title?e.hazard_type.title:""},{name:e("published"),selector:"enabled",cell:e=>(0,a.jsx)(M,{...e})},{name:e("action"),selector:"",cell:e=>(0,a.jsxs)("div",{children:[(0,a.jsx)(n(),{href:"/adminsettings/[...routes]",as:`/adminsettings/edit_hazard/${e._id}`,children:(0,a.jsx)("i",{className:"icon fas fa-edit"})}),"\xa0",(0,a.jsx)("span",{onClick:()=>I(e),style:{cursor:"pointer"},children:(0,a.jsx)("i",{className:"icon fas fa-trash-alt"})})]})}];(0,o.useEffect)(()=>{O()},[]);let R={sort:{[k]:"asc"},limit:v,page:1,query:{}},O=async()=>{S(!0);let e=await g.A.get("/hazard",R);e&&e.data&&e.data.length>0&&(c(e.data),q(e.totalCount),S(!1))},H=async(e,r)=>{R.limit=e,R.page=r,S(!0);let s=await g.A.get("/hazard",R);s&&s.data&&s.data.length>0&&(c(s.data),w(e),S(!1))},I=async e=>{z(e._id),N(!0)},G=async()=>{try{await g.A.remove(`/hazard/${D}`),O(),N(!1),x.default.success(e("adminsetting.hazard.Table.hazardDeletedSuccessfully"))}catch(r){x.default.error(e("adminsetting.hazard.Table.errorDeletingHazard"))}},W=()=>N(!1),U=p().useMemo(()=>{let e=e=>{e&&(R.query={[k]:e}),O()},r=m().debounce(r=>e(r),Number("500")||300);return(0,a.jsx)(A.default,{onFilter:e=>{P(e.target.value),r(e.target.value)},onClear:()=>{_&&(C(!j),P(""))},filterText:_})},[_]);return(0,a.jsxs)("div",{children:[(0,a.jsxs)(l.A,{show:b,onHide:W,children:[(0,a.jsx)(l.A.Header,{closeButton:!0,children:(0,a.jsx)(l.A.Title,{children:e("deleteHazard")})}),(0,a.jsx)(l.A.Body,{children:e("areYouSureWantToDeleteThisHazard")}),(0,a.jsxs)(l.A.Footer,{children:[(0,a.jsx)(u.A,{variant:"secondary",onClick:W,children:e("cancel")}),(0,a.jsx)(u.A,{variant:"primary",onClick:G,children:e("yes")})]})]}),(0,a.jsx)(h.A,{columns:E,data:i,totalRows:f,pagServer:!0,subheader:!0,resetPaginationToggle:j,subHeaderComponent:U,handlePerRowsChange:H,handlePageChange:e=>{R.limit=v,R.page=e,O()}})]})};t()}catch(e){t(e)}})},18622:e=>{e.exports=require("yup")},19121:e=>{e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},22326:e=>{e.exports=require("react-dom")},22541:e=>{e.exports=require("redux-persist/lib/storage")},25303:e=>{e.exports=require("@restart/ui/NavItem")},26324:e=>{e.exports=require("warning")},27053:(e,r,s)=>{s.d(r,{A:()=>a});var t=s(8732);function a(e){return(0,t.jsx)("h2",{className:"page-heading",children:e.title})}},27825:e=>{e.exports=require("lodash")},27910:e=>{e.exports=require("stream")},28217:e=>{e.exports=require("@restart/ui/DropdownItem")},29021:e=>{e.exports=require("fs")},29294:e=>{e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},29628:e=>{e.exports=require("dom-helpers/addClass")},29825:e=>{e.exports=require("prop-types")},29841:e=>{e.exports=require("dom-helpers/hasClass")},30362:e=>{e.exports=require("@restart/ui/DropdownToggle")},33873:e=>{e.exports=require("path")},35557:(e,r,s)=>{s.r(r),s.d(r,{default:()=>a});var t=s(8732);function a(e){return(0,t.jsx)("div",{className:"container-fluid p-0 response-message-block",children:(0,t.jsx)("div",{className:"message",children:"you don't have permission to access"})})}},35576:e=>{e.exports=require("next-i18next/serverSideTranslations")},36653:e=>{e.exports=require("nprogress")},36955:e=>{e.exports=require("@restart/hooks/useBreakpoint")},37766:e=>{e.exports=require("@restart/hooks/useMergedRefs")},38609:e=>{e.exports=require("react-data-table-component")},39756:e=>{e.exports=import("redux")},40051:e=>{e.exports=require("dom-helpers/removeClass")},40361:e=>{e.exports=require("next/dist/compiled/next-server/pages.runtime.prod.js")},42893:e=>{e.exports=import("react-hot-toast")},43294:e=>{e.exports=require("formik")},45927:(e,r,s)=>{s.r(r),s.d(r,{canAddAreaOfWork:()=>n,canAddContent:()=>C,canAddCountry:()=>o,canAddDeploymentStatus:()=>p,canAddEventStatus:()=>d,canAddExpertise:()=>l,canAddFocalPointApproval:()=>u,canAddHazardTypes:()=>x,canAddHazards:()=>m,canAddLandingPage:()=>j,canAddOperationStatus:()=>y,canAddOrganisationApproval:()=>h,canAddOrganisationNetworks:()=>g,canAddOrganisationTypes:()=>A,canAddProjectStatus:()=>S,canAddRegions:()=>f,canAddRiskLevels:()=>q,canAddSyndromes:()=>v,canAddUpdateTypes:()=>w,canAddUsers:()=>_,canAddVspaceApproval:()=>c,canAddWorldRegion:()=>P,default:()=>b});var t=s(81366),a=s.n(t);let i="create:any",n=a()({authenticatedSelector:e=>!!e.permissions&&!!e.permissions.area_of_work&&!!e.permissions.area_of_work[i],wrapperDisplayName:"CanAddAreaOfWork"}),o=a()({authenticatedSelector:e=>!!e.permissions&&!!e.permissions.country&&!!e.permissions.country[i],wrapperDisplayName:"CanAddCountry"}),p=a()({authenticatedSelector:e=>!!e.permissions&&!!e.permissions.deployment_status&&!!e.permissions.deployment_status[i],wrapperDisplayName:"CanAddDeploymentStatus"}),d=a()({authenticatedSelector:e=>!!e.permissions&&!!e.permissions.event_status&&!!e.permissions.event_status[i],wrapperDisplayName:"CanAddEventStatus"}),l=a()({authenticatedSelector:e=>!!e.permissions&&!!e.permissions.expertise&&!!e.permissions.expertise[i],wrapperDisplayName:"CanAddExpertise"}),u=a()({authenticatedSelector:e=>!!e.permissions&&!!e.permissions.institution_focal_point&&!!e.permissions.institution_focal_point[i],wrapperDisplayName:"CanAddFocalPointApproval"}),c=a()({authenticatedSelector:e=>!!e.permissions&&!!e.permissions.institution_focal_point&&!!e.permissions.institution_focal_point[i],wrapperDisplayName:"CanAddVspaceApproval"}),m=a()({authenticatedSelector:e=>!!e.permissions&&!!e.permissions.hazard&&!!e.permissions.hazard[i],wrapperDisplayName:"CanAddHazards"}),x=a()({authenticatedSelector:e=>!!e.permissions&&!!e.permissions.hazard_type&&!!e.permissions.hazard_type[i],wrapperDisplayName:"CanAddHazardTypes"}),h=a()({authenticatedSelector:e=>!!e.permissions&&!!e.permissions.institution&&!!e.permissions.institution[i],wrapperDisplayName:"CanAddOrganisationApproval"}),g=a()({authenticatedSelector:e=>!!e.permissions&&!!e.permissions.institution_network&&!!e.permissions.institution_network[i],wrapperDisplayName:"CanAddOrganisationNetworks"}),A=a()({authenticatedSelector:e=>!!e.permissions&&!!e.permissions.institution_type&&!!e.permissions.institution_type[i],wrapperDisplayName:"CanAddOrganisationTypes"}),y=a()({authenticatedSelector:e=>!!e.permissions&&!!e.permissions.operation_status&&!!e.permissions.operation_status[i],wrapperDisplayName:"CanAddOperationStatus"}),S=a()({authenticatedSelector:e=>!!e.permissions&&!!e.permissions.project_status&&!!e.permissions.project_status[i],wrapperDisplayName:"CanAddProjectStatus"}),f=a()({authenticatedSelector:e=>!!e.permissions&&!!e.permissions.region&&!!e.permissions.region[i],wrapperDisplayName:"CanAddRegions"}),q=a()({authenticatedSelector:e=>!!e.permissions&&!!e.permissions.risk_level&&!!e.permissions.risk_level[i],wrapperDisplayName:"CanAddRiskLevels"}),v=a()({authenticatedSelector:e=>!!e.permissions&&!!e.permissions.syndrome&&!!e.permissions.syndrome[i],wrapperDisplayName:"CanAddSyndromes"}),w=a()({authenticatedSelector:e=>!!e.permissions&&!!e.permissions.update_type&&!!e.permissions.update_type[i],wrapperDisplayName:"CanAddUpdateTypes"}),_=a()({authenticatedSelector:e=>!!e.permissions&&!!e.permissions.users&&!!e.permissions.users[i],wrapperDisplayName:"CanAddUsers"}),P=a()({authenticatedSelector:e=>!!e.permissions&&!!e.permissions.worl_region&&!!e.permissions.worl_region[i],wrapperDisplayName:"CanAddWorldRegion"}),j=a()({authenticatedSelector:e=>!!e.permissions&&!!e.permissions.landing_page&&!!e.permissions.landing_page[i],wrapperDisplayName:"CanAddLandingPage"}),C=a()({authenticatedSelector:e=>!!e.permissions&&!!e.permissions.operation&&!!e.permissions.operation[i]&&!!e.permissions.project&&!!e.permissions.project[i]&&!!e.permissions.event&&!!e.permissions.event[i]&&!!e.permissions.vspace&&!!e.permissions.vspace[i]&&!!e.permissions.institution&&!!e.permissions.institution[i]&&!!e.permissions.update&&!!e.permissions.update[i]||!1,wrapperDisplayName:"CanAddContent"}),b=n},45962:(e,r,s)=>{s.a(e,async(e,t)=>{try{s.r(r),s.d(r,{default:()=>f,getStaticProps:()=>S});var a=s(8732),i=s(7082),n=s(83551),o=s(49481),p=s(91353),d=s(19918),l=s.n(d),u=s(27053),c=s(17098),m=s(88751),x=s(35576),h=s(45927),g=s(14062),A=s(35557),y=e([c,g]);async function S({locale:e}){return{props:{...await (0,x.serverSideTranslations)(e,["common"])}}}[c,g]=y.then?(await y)():y;let f=e=>{let{t:r}=(0,m.useTranslation)("common"),s=()=>(0,a.jsxs)(i.A,{style:{overflowX:"hidden"},fluid:!0,className:"p-0",children:[(0,a.jsx)(n.A,{children:(0,a.jsx)(o.A,{xs:12,children:(0,a.jsx)(u.A,{title:r("menu.hazards")})})}),(0,a.jsx)(n.A,{children:(0,a.jsx)(o.A,{xs:12,children:(0,a.jsx)(l(),{href:"/adminsettings/[...routes]",as:"/adminsettings/create_hazard",children:(0,a.jsx)(p.A,{variant:"secondary",size:"sm",children:r("addHazard")})})})}),(0,a.jsx)(n.A,{className:"mt-3",children:(0,a.jsx)(o.A,{xs:12,children:(0,a.jsx)(c.default,{})})})]}),t=(0,h.canAddHazards)(()=>(0,a.jsx)(s,{})),d=(0,g.useSelector)(e=>e);return d?.permissions?.hazard?.["create:any"]?(0,a.jsx)(t,{}):(0,a.jsx)(A.default,{})};t()}catch(e){t(e)}})},50009:e=>{e.exports=require("@restart/ui/DropdownMenu")},56084:(e,r,s)=>{s.d(r,{A:()=>d});var t=s(8732);s(82015);var a=s(38609),i=s.n(a),n=s(88751),o=s(30370);function p(e){let{t:r}=(0,n.useTranslation)("common"),s={rowsPerPageText:r("Rowsperpage")},{columns:a,data:p,totalRows:d,resetPaginationToggle:l,subheader:u,subHeaderComponent:c,handlePerRowsChange:m,handlePageChange:x,rowsPerPage:h,defaultRowsPerPage:g,selectableRows:A,loading:y,pagServer:S,onSelectedRowsChange:f,clearSelectedRows:q,sortServer:v,onSort:w,persistTableHead:_,sortFunction:P,...j}=e,C={paginationComponentOptions:s,noDataComponent:r("NoData"),noHeader:!0,columns:a,data:p||[],dense:!0,paginationResetDefaultPage:l,subHeader:u,progressPending:y,subHeaderComponent:c,pagination:!0,paginationServer:S,paginationPerPage:g||10,paginationRowsPerPageOptions:h||[10,15,20,25,30],paginationTotalRows:d,onChangeRowsPerPage:m,onChangePage:x,selectableRows:A,onSelectedRowsChange:f,clearSelectedRows:q,progressComponent:(0,t.jsx)(o.A,{}),sortIcon:(0,t.jsx)("i",{className:"sort-icon fas fa-exchange-alt"}),sortServer:v,onSort:w,sortFunction:P,persistTableHead:_,className:"rki-table"};return(0,t.jsx)(i(),{...C})}p.defaultProps={subHeader:!1,pagination:!0,totalRows:null,pagServer:!0,onSelectedRowsChange:null,clearSelectedRows:!1,sortServer:!1,persistTableHead:!1};let d=p},57664:e=>{e.exports=require("dom-helpers/scrollbarSize")},58928:e=>{e.exports=require("dom-helpers/canUseDOM")},59717:e=>{e.exports=require("@restart/ui/DropdownContext")},60560:e=>{e.exports=require("dom-helpers/contains")},63033:e=>{e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},65447:e=>{e.exports=require("@restart/ui/Button")},67364:e=>{e.exports=require("@restart/hooks/useCallbackRef")},68455:e=>{e.exports=import("redux-saga")},69722:e=>{e.exports=require("es6-promise")},74075:e=>{e.exports=require("zlib")},74716:e=>{e.exports=require("moment")},74987:e=>{e.exports=require("@restart/ui/ModalManager")},77418:(e,r,s)=>{s.a(e,async(e,t)=>{try{s.r(r),s.d(r,{config:()=>g,default:()=>c,getServerSideProps:()=>h,getStaticPaths:()=>x,getStaticProps:()=>m,reportWebVitals:()=>A,routeModule:()=>w,unstable_getServerProps:()=>q,unstable_getServerSideProps:()=>v,unstable_getStaticParams:()=>f,unstable_getStaticPaths:()=>S,unstable_getStaticProps:()=>y});var a=s(63885),i=s(80237),n=s(81413),o=s(9616),p=s.n(o),d=s(72386),l=s(45962),u=e([d,l]);[d,l]=u.then?(await u)():u;let c=(0,n.M)(l,"default"),m=(0,n.M)(l,"getStaticProps"),x=(0,n.M)(l,"getStaticPaths"),h=(0,n.M)(l,"getServerSideProps"),g=(0,n.M)(l,"config"),A=(0,n.M)(l,"reportWebVitals"),y=(0,n.M)(l,"unstable_getStaticProps"),S=(0,n.M)(l,"unstable_getStaticPaths"),f=(0,n.M)(l,"unstable_getStaticParams"),q=(0,n.M)(l,"unstable_getServerProps"),v=(0,n.M)(l,"unstable_getServerSideProps"),w=new a.PagesRouteModule({definition:{kind:i.A.PAGES,page:"/adminsettings/hazard",pathname:"/adminsettings/hazard",bundlePath:"",filename:""},components:{App:d.default,Document:p()},userland:l});t()}catch(e){t(e)}})},78097:e=>{e.exports=require("next-redux-saga")},78634:e=>{e.exports=require("@restart/ui/Anchor")},80237:(e,r)=>{Object.defineProperty(r,"A",{enumerable:!0,get:function(){return s}});var s=function(e){return e.PAGES="PAGES",e.PAGES_API="PAGES_API",e.APP_PAGE="APP_PAGE",e.APP_ROUTE="APP_ROUTE",e.IMAGE="IMAGE",e}({})},81366:e=>{e.exports=require("redux-auth-wrapper/connectedAuthWrapper")},81413:(e,r)=>{Object.defineProperty(r,"M",{enumerable:!0,get:function(){return function e(r,s){return s in r?r[s]:"then"in r&&"function"==typeof r.then?r.then(r=>e(r,s)):"function"==typeof r&&"default"===s?r:void 0}}})},81521:e=>{e.exports=require("dom-helpers/querySelectorAll")},81895:e=>{e.exports=require("@restart/hooks/useEventCallback")},82015:e=>{e.exports=require("react")},86842:e=>{e.exports=require("@restart/ui/SelectableContext")},87571:e=>{e.exports=require("dom-helpers/transitionEnd")},88751:e=>{e.exports=require("next-i18next")},93787:e=>{e.exports=require("redux-persist")},94544:(e,r,s)=>{s.r(r),s.d(r,{default:()=>d});var t=s(8732),a=s(7082),i=s(83551),n=s(49481),o=s(84517),p=s(88751);let d=({filterText:e,onFilter:r,onClear:s})=>{let{t:d}=(0,p.useTranslation)("common");return(0,t.jsx)(a.A,{fluid:!0,className:"p-0",children:(0,t.jsx)(i.A,{children:(0,t.jsx)(n.A,{md:4,className:"p-0",children:(0,t.jsx)(o.A,{type:"text",className:"searchInput",placeholder:d("adminsetting.hazard.Search"),"aria-label":"Search",value:e,onChange:r})})})})}},94947:e=>{e.exports=require("@restart/hooks/useTimeout")},96196:e=>{e.exports=require("next-redux-wrapper")},98320:e=>{e.exports=require("@restart/hooks/useIsomorphicEffect")},99460:e=>{e.exports=require("dom-helpers/css")}};var r=require("../../webpack-runtime.js");r.C(e);var s=e=>r(r.s=e),t=r.X(0,[6089,9216,9616,2386],()=>s(77418));module.exports=t})();