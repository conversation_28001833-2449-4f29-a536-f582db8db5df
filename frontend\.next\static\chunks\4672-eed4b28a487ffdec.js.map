{"version": 3, "file": "static/chunks/4672-eed4b28a487ffdec.js", "mappings": "4MA0CA,MAhC+BA,QAkBLA,EAAAA,EAjBtB,GAAM,GAAEC,CAAC,CAAE,CAAGC,CAAAA,EAAAA,EAAAA,EAAAA,CAAcA,CAAC,UACvB,CAACC,EAASC,EAAW,CAAGC,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,EAAC,GACvC,MACI,+BACI,WAACC,EAAAA,CAASA,CAACC,IAAI,EAACC,SAAS,cACrB,WAACF,EAAAA,CAASA,CAACG,MAAM,EAACC,QAAS,IAAMN,EAAW,CAACD,aACzC,UAACQ,MAAAA,CAAIC,UAAU,qBAAaX,EAAE,wBAC9B,UAACU,MAAAA,CAAIC,UAAU,qBACVT,EACG,UAACU,EAAAA,CAAeA,CAAAA,CAACC,KAAMC,EAAAA,GAAOA,CAAEC,MAAM,SAEtC,UAACH,EAAAA,CAAeA,CAAAA,CAACC,KAAMG,EAAAA,GAAMA,CAAED,MAAM,cAIjD,UAACV,EAAAA,CAASA,CAACY,IAAI,WACX,UAACC,EAAAA,CAAWA,CAAAA,CACVC,GAAIpB,OAAAA,GAAAA,EAAMqB,SAAAA,GAANrB,OAAAA,EAAAA,EAAiBsB,MAAAA,EAAjBtB,KAAAA,EAAAA,CAAyB,CAAC,EAAE,CAA5BA,EAAgC,GACpCuB,KAAK,YACLC,WAAY,EAAE,CACdC,mBAAmB,EACnBC,oBAAqB,EACrBC,kBAAmB,GACnBC,sBAAuB,UAM7C,8KCmEA,MAjGmC5B,IAC/B,GAAM,CAAEC,CAAC,CAAE,CAAGC,CAAAA,EAAAA,EAAAA,EAAAA,CAAcA,CAAC,QAgGlB2B,EA/FLC,EAAa,qBACbC,EAAwB,YACxB,CAAC5B,EAASC,EAAW,CAAGC,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,EAAC,GACvC,MACI,+BACI,WAACC,EAAAA,CAASA,CAACC,IAAI,EAACC,SAAS,cACrB,WAACF,EAAAA,CAASA,CAACG,MAAM,EAACC,QAAS,IAAMN,EAAW,CAACD,aACzC,UAACQ,MAAAA,CAAIC,UAAU,qBAAaX,EAAE,sBAC9B,UAACU,MAAAA,CAAIC,UAAU,qBACVT,EACG,UAACU,EAAAA,CAAeA,CAAAA,CAACC,KAAMC,EAAAA,GAAOA,CAAEC,MAAM,SAEtC,UAACH,EAAAA,CAAeA,CAAAA,CAACC,KAAMG,EAAAA,GAAMA,CAAED,MAAM,cAIjD,UAACV,EAAAA,CAASA,CAACY,IAAI,WACP,WAACc,EAAAA,CAAGA,CAAAA,CAACpB,UAAU,0BACX,WAACqB,EAAAA,CAAGA,CAAAA,CAACC,EAAE,IAACC,GAAI,EAAGC,GAAI,GAAIxB,UAAU,iBAC7B,WAACyB,IAAAA,WACG,UAACC,IAAAA,UAAGrC,EAAE,gBAAkB,IACxB,UAACsC,OAAAA,UACIvC,EAAMwC,SAAS,CAACC,WAAW,CACtBzC,EAAMwC,SAAS,CAACC,WAAW,CAACC,KAAK,CACjC,UAGbC,SAqExBA,CACmB,CACxBC,CAkBC,EAED,MACI,WAACjC,MAAAA,CAAIC,UAAU,6BACX,UAAC0B,IAAAA,UAAGrC,EAAE,YAAc,IACpB,UAACsC,OAAAA,UACG,UAACM,KAAAA,CAAGjC,UAAU,2BACTgC,EAAcE,MAAM,EAAIF,EAAcE,MAAM,CAACC,MAAM,EAAI,EAClDH,EAAcE,MAAM,CAACE,GAAG,CAAC,CAACC,EAAMC,IACvB,UAACC,KAAAA,UAAYF,EAAKP,KAAK,CAACU,EAAE,EAAjBF,IAElB,WAK1B,EAzGuDjD,EAAGD,EAAMwC,SAAS,EACzC,WAACH,IAAAA,WACG,UAACC,IAAAA,UAAGrC,EAAE,cAAgB,IACtB,UAACsC,OAAAA,UACIvC,EAAMwC,SAAS,CAACa,QAAQ,CACnBrD,EAAMwC,SAAS,CAACa,QAAQ,CAACX,KAAK,CAC9B,UAGd,WAACL,IAAAA,WACG,UAACC,IAAAA,UAAGrC,EAAE,aAAe,IACrB,UAACsC,OAAAA,UACIe,IAAOtD,EAAMwC,SAAS,CAACe,UAAU,EAAEC,MAAM,CACtC1B,QAIZ,EALewB,CAKf,QAACjB,IAAAA,WACG,UAACC,IAAAA,UAAGrC,EAAE,kBAAoB,IAC1B,UAACsC,OAAAA,UACIe,IAAOtD,EAAMwC,SAAS,CAACiB,UAAU,EAAED,MAAM,CACtC1B,UADGwB,CAOnB,WAACrB,EAAAA,CAAGA,CAAAA,CAACC,EAAE,IAACC,GAAI,EAAGC,GAAI,GAAIxB,UAAU,iBAC7B,WAACyB,IAAAA,WACG,UAACC,IAAAA,UAAGrC,EAAE,wBAA0B,IAChC,UAACsC,OAAAA,UACIvC,EAAMwC,SAAS,CAACkB,OAAO,CAClB1D,EAAMwC,SAAS,CAACkB,OAAO,CAAChB,KAAK,CAC7B,UAGd,WAACL,IAAAA,WACG,UAACC,IAAAA,UAAGrC,EAAE,qBAAuB,IAC7B,WAACsC,OAAAA,WAAK,IAAEvC,EAAMwC,SAAS,CAACmB,MAAM,CAACjB,KAAK,CAAC,UAEzC,WAACL,IAAAA,WACG,UAACC,IAAAA,UAAGrC,EAAE,eAAiB,IACvB,UAACsC,OAAAA,UACIvC,EAAMwC,SAAS,CAACoB,UAAU,CACrBN,IAAOtD,EAAMwC,SAAS,CAACoB,UAAU,EAAEJ,MAAM,CACvCzB,GAEF,OAHMuB,GAMpB,WAACjB,IAAAA,WACG,UAACC,IAAAA,UAAGrC,EAAE,aAAe,IACrB,UAACsC,OAAAA,UACIvC,EAAMwC,SAAS,CAACqB,QAAQ,CACnBP,IAAOtD,EAAMwC,SAAS,CAACqB,QAAQ,EAAEL,MAAM,CACrCzB,GAEF,SAHMuB,aAYpD,+OClGO,IAAMQ,EAAkBC,CAAAA,EAAAA,EAAAA,CAAAA,CAAoBA,CAAC,CAClDC,sBAAuB,GACjBC,IAAMC,WAAW,IAAID,EAAMC,WAAW,CAAC1B,SAAS,IAAIyB,EAAMC,WAAW,CAAC1B,SAAS,CAAC,aAAa,CAKnG2B,CALqG,kBAKjF,iBACtB,GAAG,EAEgCJ,CAAAA,EAAAA,EAAAA,CAAAA,CAAoBA,CAAC,CACtDC,sBAAuB,GACjBC,IAAMC,WAAW,IAAID,EAAMC,WAAW,CAAC1B,SAAS,IAAIyB,EAAMC,WAAW,CAAC1B,SAAS,CAAC,aAAa,CAKnG2B,CALqG,kBAKjF,sBACpBC,iBAAkB,IAAM,UAACC,EAAAA,OAAIA,CAAAA,CAAAA,EAC/B,GAEaC,EAAmBP,CAAAA,EAAAA,EAAAA,CAAAA,CAAoBA,CAAC,CACnDC,sBAAuB,CAACC,EAAOjE,KAC7B,GAAIiE,EAAMC,WAAW,EAAID,EAAMC,WAAW,CAAC1B,SAAS,EAAE,GAChDyB,EAAMC,WAAW,CAAC1B,SAAS,CAAC,aAAa,CAC3C,CAD6C,MACtC,OAEP,GAAIyB,EAAMC,WAAW,CAAC1B,SAAS,CAAC,aAAa,EAAE,EACnCA,SAAS,EAAIxC,EAAMwC,SAAS,CAAC+B,IAAI,EAAIvE,EAAMwC,SAAS,CAAC+B,IAAI,CAACC,GAAG,GAAKP,EAAMM,IAAI,CAACC,GAAG,CACxF,CAD0F,KACnF,EAGb,CAEF,OAAO,CACT,EACAL,mBAAoB,kBACtB,GAAG,EAEiCJ,CAAAA,EAAAA,EAAAA,CAAAA,CAAoBA,CAAC,CACvDC,sBAAuB,CAACC,EAAOjE,KAC7B,GAAIiE,EAAMC,WAAW,EAAID,EAAMC,WAAW,CAAC1B,SAAS,EAAE,GAChDyB,EAAMC,WAAW,CAAC1B,SAAS,CAAC,aAAa,CAC3C,CAD6C,KACtC,QAEP,GAAIyB,EAAMC,WAAW,CAAC1B,SAAS,CAAC,aAAa,EAAE,EACnCA,SAAS,EAAIxC,EAAMwC,SAAS,CAAC+B,IAAI,EAAIvE,EAAMwC,SAAS,CAAC+B,IAAI,CAACC,GAAG,GAAKP,EAAMM,IAAI,CAACC,GAAG,CACxF,CAD0F,KACnF,EAGb,CAEF,MAAO,EACT,EACAL,mBAAoB,uBACpBC,iBAAkB,IAAM,UAACC,EAAAA,OAAIA,CAAAA,CAAAA,EAC/B,GAAG,EAEoCN,CAAAA,EAAAA,EAAAA,CAAAA,CAAoBA,CAAC,CAC1DC,sBAAuB,KACjBC,EAAMC,WAAW,IAAID,EAAMC,WAAW,CAACO,MAAM,IAAIR,EAAMC,WAAW,CAACO,MAAM,CAAC,WAAW,CAK3FN,CAL6F,kBAKzE,yBACtB,GAAG,EAEYL,eAAeA,EAAC,8HC5B/B,MArC2B,IACvB,GAAM,CAAE7D,CAAC,CAAE,CAAGC,CAAAA,EAAAA,EAAAA,EAAAA,CAAcA,CAAC,QAoClBwE,EAnCL,CAACvE,EAASC,EAAW,CAAGC,CAAAA,EAAAA,EAAAA,KAmCDqE,EAAC,CAnCArE,CAAQA,EAAC,GAEvC,MACI,+BACI,WAACC,EAAAA,CAASA,CAACC,IAAI,EAACC,SAAS,cACrB,WAACF,EAAAA,CAASA,CAACG,MAAM,EAACC,QAAS,IAAMN,EAAW,CAACD,aACzC,UAACQ,MAAAA,CAAIC,UAAU,qBAAaX,EAAE,eAC9B,UAACU,MAAAA,CAAIC,UAAU,qBACVT,EACG,UAACU,EAAAA,CAAeA,CAAAA,CAACC,KAAMC,EAAAA,GAAOA,CAAEC,MAAM,SAEtC,UAACH,EAAAA,CAAeA,CAAAA,CAACC,KAAMG,EAAAA,GAAMA,CAAED,MAAM,cAIjD,WAACV,EAAAA,CAASA,CAACY,IAAI,YACP,UAACyD,EAAAA,CAAaA,CAAAA,CACVC,QAAS5E,EAAM6E,sBAAsB,CAACC,uBAAuB,CAACF,OAAO,CACrEG,UAAW/E,EAAM6E,sBAAsB,CAACC,uBAAuB,CAACC,SAAS,CACzEC,KAAMhF,EAAM6E,sBAAsB,CAACC,uBAAuB,CAACG,QAAQ,EAAI,EAAE,CACzEC,gBAAiBlF,EAAM6E,sBAAsB,CAACjC,aAAa,CAACuC,OAAO,GAEvE,UAACC,KAAAA,CAAGxE,UAAU,gBAAQX,EAAE,0BACxB,UAAC0E,EAAAA,CAAaA,CAAAA,CACVC,QAAS5E,EAAM6E,sBAAsB,CAACC,uBAAuB,CAACF,OAAO,CACrEG,UAAW/E,EAAM6E,sBAAsB,CAACC,uBAAuB,CAACO,eAAe,CAC/EL,KAAMhF,EAAM6E,sBAAsB,CAACC,uBAAuB,CAACQ,cAAc,EAAI,EAAE,CAC/EJ,gBAAiBlF,EAAM6E,sBAAsB,CAACjC,aAAa,CAACuC,OAAO,UAM/F,gIC+DA,MAzFoB,IAClB,GAAM,CAAElF,CAAC,CAAE,CAAGC,CAAAA,EAAAA,EAAAA,EAAAA,CAAcA,CAAC,QAwFhBiB,EAvFP,MAAEI,CAAI,EAuFYJ,EAvFVC,CAAE,CAAE,CAAGpB,EACf,CAACuF,EAAWC,EAAe,CAAGnF,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAC,EAAE,EACzC,CAACuE,EAASa,EAAW,CAAGpF,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,EAAC,GACjC,CAACqF,EAAWC,EAAa,CAAGtF,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAC,GACrC,CAACuF,EAASC,EAAW,CAAGxF,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAC,IACjC,CAACyF,EAAsB,CAAGzF,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,EAAC,GAEnC0F,EAAe,CACnBC,KAAM,CAAEzC,WAAY,KAAM,EAC1B0C,MAAOL,EACPM,KAAM,EACNC,MAAO,CAAC,CACV,EAEMC,EAAU,CACd,CACEC,KAAMpG,EAAE,SACRqG,SAAU,QACVC,KAAM,GAAYC,GAAKA,EAAE9D,KAAK,EAAI8D,EAAEhC,GAAG,CAAG,UAACiC,IAAIA,CAACC,KAAK,sBAAsBC,GAAI,cAAhCF,EAAsD,OAAND,EAAEhC,GAAG,WAAMgC,EAAE9D,KAAK,GAAW,EAC9H,EACA,CACE2D,KAAMpG,EAAE,SACRqG,SAAU,QACVC,KAAM,GAAYC,GAAKA,EAAEjC,IAAI,EAAIiC,EAAEjC,IAAI,CAACqC,SAAS,CAAG,GAAuBJ,MAAAA,CAApBA,EAAEjC,IAAI,CAACqC,SAAS,CAAC,KAAmB,OAAhBJ,EAAEjC,IAAI,CAACsC,QAAQ,EAAK,EACjG,EACA,CACER,KAAMpG,EAAE,iBACRqG,SAAU,aACVC,KAAM,GAAYC,GAAKA,EAAEM,UAAU,CAAG,SAAW,SAEnD,EACA,CACET,KAAMpG,EAAE,mBACRqG,SAAU,UACVC,KAAM,GAAYC,GAAKA,EAAEO,OAAO,CAAGP,EAAEO,OAAO,CAAChE,MAAM,CAAG,GACxD,EACD,CAEKiE,EAAkB,MAAOC,IAC7BxB,GAAW,GACX,IAAMyB,EAAW,MAAMC,EAAAA,CAAUA,CAACC,GAAG,CAAC,YAA8BhG,MAAAA,CAAlBG,EAAK,eAAgB,OAAHH,GAAM2E,GACtEmB,IACO,MADG,QACZ3F,EAAuBiE,EAAe0B,EAAS1E,SAAS,EAAIgD,EAAe0B,EAASG,OAAO,EAC3F1B,EAAauB,EAASI,UAAU,EAChC7B,EAAW,IAEf,EAQM8B,EAAsB,MAAOC,EAAoBtB,KACrDH,EAAaE,KAAK,CAAGuB,EACrBzB,EAAaG,IAAI,CAAGA,EACpBT,GAAW,GACX,IAAMyB,EAAW,MAAMC,EAAAA,CAAUA,CAACC,GAAG,CAAC,YAA8BhG,MAAAA,CAAlBG,EAAK,eAAgB,OAAHH,GAAM2E,GACtEmB,IACO,MADG,QACZ3F,EAAuBiE,EAAe0B,EAAS1E,SAAS,EAAIgD,EAAe0B,EAASG,OAAO,EAC3FxB,EAAW2B,GACX/B,GAAW,GAEf,EAQA,MANAgC,CAAAA,EAAAA,EAAAA,SAAAA,CAASA,CAAC,KACRT,EAAgBjB,EAClB,EAAG,EAAE,EAKH,UAACpF,MAAAA,UACC,UAAC+G,EAAAA,CAAQA,CAAAA,CACPtB,QAASA,EACTuB,KAAMpC,EACNG,UAAWA,EACXd,QAASA,EACTkB,sBAAuBA,EACvByB,oBAAqBA,EACrBK,iBAjCmB,CAiCDA,GAhCtB7B,EAAaE,KAAK,CAAGL,EACrBG,EAAaG,IAAI,CAAGA,EACpBc,EAAgBjB,EAClB,KAiCF,6GCrEA,SAAS2B,EAAS1H,CAAoB,EACpC,GAAM,CAAEC,CAAC,CAAE,CAAGC,CAAAA,EAAAA,EAAAA,EAAAA,CAAcA,CAAC,UACvB2H,EAA6B,CACjCC,gBAAiB7H,EAAE,cACnB,EACI,SACJmG,CAAO,MACPuB,CAAI,WACJjC,CAAS,uBACTI,CAAqB,WACrBiC,CAAS,CACTC,oBAAkB,CAClBT,qBAAmB,kBACnBK,CAAgB,aAChBK,CAAW,oBACXC,CAAkB,gBAClBC,CAAc,SACdvD,CAAO,WACPwD,CAAS,sBACTC,CAAoB,mBACpBC,CAAiB,YACjBC,CAAU,QACVC,CAAM,CACNC,kBAAgB,cAChBC,CAAY,CAEZ,CADA,EACGC,EACJ,CAAG3I,EAGE4I,EAAiB,4BACrBf,EACAgB,gBAAiB5I,EAAE,IAP0C,MAQ7D6I,UAAU,EACV1C,UACAuB,KAAMA,GAAQ,EAAE,CAChBoB,OAAO,EACPC,2BAA4BlD,EAC5BmD,UAAWlB,EACXmB,gBAAiBtE,qBACjBoD,EACAmB,YAAY,EACZC,iBAAkBhB,EAClBiB,kBAAmBnB,GAA0C,GAC7DoB,eADwCpB,cACVD,GAA4B,CAAC,GAAI,GAAI,GAAI,CAA3BA,EAA+B,GAAG,CAC9EsB,oBAAqB7D,EACrB8D,oBAAqBjC,EACrBkC,aAAc7B,iBACdO,uBACAE,oBACAC,EACAoB,kBAAmB,UAACC,EAAAA,CAAYA,CAAAA,CAAAA,GAChCC,SAAU,UAAC1G,IAAAA,CAAEtC,UAAU,6CACvB2H,SACAC,eACAE,mBACAD,EACA7H,UAAW,WACb,EACA,MACE,UAACiJ,EAAAA,EAASA,CAAAA,CAAE,GAAGjB,CAAc,EAEjC,CAEAlB,EAASoC,YAAY,CAAG,CACtBb,WAAW,EACXE,YAAY,EACZzD,UAAW,KACX0C,UAAW,GACXC,qBAAsB,KACtBC,mBAAmB,EACnBC,YAAY,EACZE,kBAAkB,CACpB,EAEA,MAAef,QAAQA,EAAC,6NCjCxB,MA/DkC,IAC9B,GAAM,CAAEzH,CAAC,CAAE,CAAGC,CAAAA,EAAAA,EAAAA,EAAAA,CAAcA,CAAC,QA8DlB6J,EA7DL,CAAC5J,EAASC,EAAW,CAAGC,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,EAAC,CA6DH0J,EA5DpCC,QAAQC,GAAG,CAACjK,EAAO,aAEnB,IAAMkK,EAAsB,SAgBJlK,EAfpB,MACI,WAACM,EAAAA,CAASA,CAACC,IAAI,EAACC,SAAS,cACrB,WAACF,EAAAA,CAASA,CAACG,MAAM,EAACC,QAAS,IAAMN,EAAW,CAACD,aACzC,UAACQ,MAAAA,CAAIC,UAAU,qBAAaX,EAAE,iBAC9B,UAACU,MAAAA,CAAIC,UAAU,qBACVT,EACG,UAACU,EAAAA,CAAeA,CAAAA,CAACC,KAAMC,EAAAA,GAAOA,CAAEC,MAAM,SAEtC,UAACH,EAAAA,CAAeA,CAAAA,CAACC,KAAMG,EAAAA,GAAMA,CAAED,MAAM,cAIjD,UAACV,EAAAA,CAASA,CAACY,IAAI,WACX,UAACiJ,EAAAA,CAAUA,CAAAA,CACP5I,KAAK,YACLH,GAAIpB,OAAAA,GAAAA,OAAAA,EAAAA,EAAOqB,IAAPrB,KAAOqB,EAAPrB,KAAAA,EAAAA,EAAkBsB,GAAlBtB,GAAwB,EAAGA,EAAMqB,SAAS,CAACC,MAAM,CAAC,EAAE,CAAG,WAK/E,EAEM8I,EAA0BC,CAAAA,EAAAA,EAAAA,uBAAAA,CAAuBA,CAAC,IACpD,UAACH,EAAAA,CAAAA,IAEL,MACI,+BACI,UAAClI,EAAAA,CAAGA,CAAAA,UACA,WAACC,EAAAA,CAAGA,CAAAA,CAACrB,UAAU,qBAAqB0J,GAAI,aACpC,UAAChK,EAAAA,CAASA,CAAAA,UACN,UAACuB,EAAAA,OAAyBA,CAAAA,CAACW,UAAWxC,EAAM4C,aAAa,KAG7D,UAACtC,EAAAA,CAASA,CAAAA,UACN,UAACiK,EAAAA,OAAiBA,CAAAA,CAAC/H,UAAWxC,EAAM4C,aAAa,KAGrD,UAACtC,EAAAA,CAASA,CAAAA,UACN,UAACkK,EAAAA,OAAqBA,CAAAA,CAAChI,UAAWxC,EAAM4C,aAAa,KAGzD,UAACtC,EAAAA,CAASA,CAAAA,UACN,UAACoE,EAAAA,OAAkBA,CAAAA,CAACG,uBAAwB7E,MAGhD,UAACM,EAAAA,CAASA,CAAAA,UACN,UAAC8J,EAAAA,CAAAA,KAEL,UAAC9J,EAAAA,CAASA,CAAAA,UACN,UAACmK,EAAAA,OAAqBA,CAAAA,CAACpJ,UAAWrB,EAAMqB,SAAS,WAMzE,uHCRA,MAtDoD,OAAC,MAAE2D,CAAI,cAsD5CL,GAtD8CO,CAAe,SAsDhDP,EAtDkDI,CAAS,SAAEH,CAAO,CAAE,GAE1F8F,EAAa,MAAOC,EAAaC,KAKrC7F,EAJiB,CACf8F,OAGQC,QAHQH,EAAOrE,QAAQ,CAC/BsE,cAAeA,CACjB,EAEF,EAEM,CAAE3K,GAAC,CAAE,CAAGC,CAAAA,EAAAA,EAAAA,EAAAA,CAAcA,CAAC,UAGvBkG,EAAU,CACd,CACEC,KAAMpG,EAAE,YACR8K,MAAO,MACPzE,SAAU,YACVC,KAAM,GAAYC,GAAKA,EAAEwE,SAAS,EAAIxE,EAAEwE,SAAS,EAEnD,CACE3E,KAAMpG,EAAE,YACR8K,MAAO,MACPzE,SAAU,iBACVC,KAAM,GAAYC,GAAKA,EAAEyE,aAAa,EAAI,UAACC,IAAAA,CAAExE,KAAM,GAA4CF,MAAAA,CAAzC2E,8BAAsB,CAAC,oBAAwB,OAAN3E,EAAEhC,GAAG,EAAI4G,OAAO,kBAAU5E,EAAEyE,aAAa,CAACI,KAAK,CAAC,KAAKC,KAAK,CAAC,EAAG,CAAC,GAAGC,IAAI,CAAC,OACtKC,UAAU,CACZ,EACA,CACEnF,KAAMpG,EAAE,eACRqG,SAAU,cACVC,KAAOC,GAAWA,GAAKA,EAAEiF,WAAW,EAAIjF,EAAEiF,WAAW,EAEvD,CACEpF,KAAMpG,EAAE,gBACR8K,MAAO,MACPzE,SAAU,iBACVC,KAAM,GAAYC,GAAKA,EAAE/C,UAAU,EAAIH,IAAOkD,EAAE/C,UAAU,EAAED,MAAM,CAAC,cACnEgI,MAD6ClI,IACnC,CACZ,EACD,CAED,MACE,UAACoE,EAAAA,CAAQA,CAAAA,CACPtB,QAASA,EACTuB,KAAM3C,EACNoD,WAAW,EACXI,OAAQkC,EACRjC,gBAAgB,IAChB7D,QAASA,GAIf,yBCxDC,aAA4B,aAI7B,oBACA,OACA,iCACA,iCACA,0BACA,0BACA,+BACA,8BACA,8BACA,4BACA,6BAEA,yBAGA,qBACA,qGACA,KAEA,YACA,2EACA,oBACA,SACA,oEACA,KAEA,uDACA,8CACA,sBACA,gBACA,WACA,eACA,eACA,kBACA,yBACA,+BACA,CAAS,CACT,UACA,8BACA,aACA,+BACA,8BACA,gCACA,uCACA,CAAS,CACT,cACA,eACA,cACA,sBACA,iBACA,IACA,gBACA,IACA,gBACA,IACA,KACA,IACA,eACA,IACA,KACA,IACA,IACA,CAAS,CACT,2BAAoC,IAAI,IACxC,cACA,MACA,MACA,KACA,CAAS,CACJ,CAIL,CAAC,EAhFiD,EAAQ,KAAW,YAAZ,sJCCzD,IAAM8G,EAAW,OAAC,CAAEC,UAAQ,CAAsB,UAChD,GAAgBA,EAAS5I,MAAM,CAAG,EAE9B,CAFiC,EAEjC,OAACF,KAAAA,UACE8I,EAAS3I,GAAG,CAAC,CAACC,EAAM2I,IACZ,UAACzI,KAAAA,UAAgBF,EAAKP,KAAK,EAAlBkJ,MAKjB,IACT,EAGMC,EACJ,WAACC,EAAAA,CAAOA,CAAAA,CAAC1K,GAAG,0BACV,UAAC0K,EAAAA,CAAOA,CAACrL,MAAM,EAACkG,GAAG,KAAK/F,UAAU,uBAAc,aAGhD,UAACkL,EAAAA,CAAOA,CAAC5K,IAAI,WACX,WAACP,MAAAA,CAAIC,UAAU,gBACb,WAACyB,IAAAA,WACC,UAACC,IAAAA,UAAE,UAAS,4BAEd,WAACD,IAAAA,WACC,UAACC,IAAAA,UAAE,QAAO,gCAEZ,WAACD,IAAAA,WACC,UAACC,IAAAA,UAAE,SAAQ,yCAEb,WAACD,IAAAA,WACC,UAACC,IAAAA,UAAE,UAAS,iDAEd,WAACD,IAAAA,WACC,UAACC,IAAAA,UAAE,WAAU,uEAGf,WAACD,IAAAA,WACC,UAACC,IAAAA,UAAE,WAAU,uEAGf,WAACD,IAAAA,WACC,UAACC,IAAAA,UAAE,UAAS,+DAkItB,EA3HA,SAASyJ,CAA4B,EACnC,GAAM,CAAE9L,CAAC,CAAE,CAAGC,CAAAA,EAAAA,EAAAA,EAAAA,CAAcA,CAAC,UACvB,OAyHwB6L,EAAC,CAzHvBC,CAAQ,CAAE,CAAGhM,EACfoG,EAAU,CACd,CACEC,KAAMpG,EAAE,gBACRqG,SAAU,QACVC,KAAOC,GACLA,GAAKA,EAAEyF,WAAW,CAChB,UAACxF,IAAIA,CACHC,KAAK,2BACLC,GAAI,SAFDF,YAEwC,OAAlBD,EAAEyF,WAAW,CAACzH,GAAG,WAEzCgC,EAAEyF,WAAW,CAACvJ,KAAK,GAGtB,GAEJ8I,UAAU,CACZ,EACA,CACEnF,KAAMpG,EAAE,WACRqG,SAAU,UACVC,KAAM,GACJC,GACAA,EAAEyF,WAAW,EACbzF,EAAEyF,WAAW,CAACC,OAAO,EACrB1F,EAAEyF,WAAW,CAACC,OAAO,CAACxI,OAAO,CAC3B,UAAC+C,IAAIA,CACHC,KAAK,uBACLC,GAAI,aAFDF,IAEoD,OAAlCD,EAAEyF,WAAW,CAACC,OAAO,CAACxI,OAAO,CAACc,GAAG,WAErDgC,EAAEyF,WAAW,CAACC,OAAO,CAACxI,OAAO,CAAChB,KAAK,GAGtC,GAEJ8I,UAAU,CACZ,EACA,CACEnF,KAAMpG,EAAE,QACRqG,SAAU,aACVC,KAAM,GACJC,EAAEyF,WAAW,EAAIzF,EAAEyF,WAAW,CAAC1K,IAAI,EAAIiF,EAAEyF,WAAW,CAAC1K,IAAI,CAACmB,KAAK,CAC3D8D,EAAEyF,WAAW,CAAC1K,IAAI,CAACmB,KAAK,CACxB,GACN8I,UAAU,CACZ,EACA,CACEnF,KACE,UAAC8F,EAAAA,CAAcA,CAAAA,CACbC,QAAQ,QACRC,UAAU,QACVC,QAAST,WAET,WAACtJ,OAAAA,WACEtC,EAAE,WAAW,eACd,UAACiD,IAAAA,CACCtC,UAAU,oBACV2L,MAAO,CAAEC,OAAQ,SAAU,EAC3BC,cAAY,cAKpBnG,SAAUrG,EAAE,YACZsG,KAAM,GACJC,EAAEyF,WAAW,EACbzF,EAAEyF,WAAW,CAACN,QAAQ,EACtBnF,EAAEyF,WAAW,CAACN,QAAQ,CAAC5I,MAAM,CAAG,EAC9B,UAAC2I,EAAAA,CAASC,SAAUnF,EAAEyF,WAAW,CAACN,QAAQ,GAE1C,EAEN,EACD,CAEKe,EAAY,IAChB,GAAIC,EAAIV,WAAW,CAACC,OAAO,EAAIS,EAAIV,WAAW,CAACC,OAAO,CAACxI,OAAO,EAAE,MAC9D,EACMuI,WAAW,CAACC,OAAO,CAACxI,OAAO,EAC/BiJ,EAAIV,WAAW,CAACC,OAAO,CAACxI,OAAO,CAAChB,KAAK,CAE9BiK,CADP,CACWV,WAAW,CAACC,OAAO,CAACxI,OAAO,CAAChB,KAAK,CAACkK,WAAW,GAEnDD,EAAIV,WAAW,CAACC,OAAO,CAACxI,OAAO,CAAChB,KAAK,EAI1CmK,EAAiB,IACrB,GAAIF,EAAIV,WAAW,CAAC1K,IAAI,EAAE,EAChB0K,WAAW,CAAC1K,IAAI,EAAIoL,EAAIV,WAAW,CAAC1K,IAAI,CAACmB,KAAK,CACpD,CADsD,MAC/CiK,EAAIV,WAAW,CAAC1K,IAAI,CAACmB,KAAK,CAACkK,WAAW,EAGnD,EAiBA,MACE,UAAClF,EAAAA,CAAQA,CAAAA,CACPtB,QAASA,EACTuB,KAAMqE,EACN5D,WAAW,EACXK,gBAAgB,IAChBC,aArBe,CAACoE,EAAWC,EAAeC,IAYrCC,IAAAA,OAAS,CAACH,EAXIH,IACnB,GAAc,WAAW,CAArBI,EACFL,EAAUC,QACL,GAAc,cAAc,GACjCE,EAAeF,QAEf,GAAIA,EAAIV,WAAW,EAAIU,EAAIV,WAAW,CAACc,EAAM,CAC3C,CAD6C,MACtCJ,EAAIV,WAAW,CAACc,EAAM,CAACH,WAAW,EAG/C,EACoCI,IAYxC,+ICjJA,MAxB0B,IACtB,GAAM,CAAE/M,CAAC,CAAE,CAAGC,CAAAA,EAAAA,EAAAA,EAAAA,CAAcA,CAAC,QAuBlBqK,EAtBL,CAACpK,EAASC,EAAW,CAAGC,CAAAA,EAAAA,EAAAA,IAsBFkK,EAAC,EAtBClK,CAAQA,EAAC,GACvC,MACI,+BACI,WAACC,EAAAA,CAASA,CAACC,IAAI,EAACC,SAAS,cACrB,WAACF,EAAAA,CAASA,CAACG,MAAM,EAACC,QAAS,IAAMN,EAAW,CAACD,aACzC,UAACQ,MAAAA,CAAIC,UAAU,qBAAaX,EAAE,cAC9B,UAACU,MAAAA,CAAIC,UAAU,qBACVT,EACG,UAACU,EAAAA,CAAeA,CAAAA,CAACC,KAAMC,EAAAA,GAAOA,CAAEC,MAAM,SAEtC,UAACH,EAAAA,CAAeA,CAAAA,CAACC,KAAMG,EAAAA,GAAMA,CAAED,MAAM,cAIjD,UAACV,EAAAA,CAASA,CAACY,IAAI,WACX,UAAC6K,EAAAA,OAAiBA,CAAAA,CAACC,SAAUhM,EAAMwC,SAAS,CAACwJ,QAAQ,SAKzE,+ICKA,MA3B8B,IAC1B,GAAM,CAAE/L,CAAC,CAAE,CAAGC,CAAAA,EAAAA,EAAAA,EAAAA,CAAcA,CAAC,QA0BlBsK,EAzBL,CAACrK,EAASC,EAAW,CAAGC,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAyBL,CAzBM,GACvC,MACI,+BACI,WAACC,EAAAA,CAASA,CAACC,IAAI,EAACC,SAAS,cACrB,WAACF,EAAAA,CAASA,CAACG,MAAM,EAACC,QAAS,IAAMN,EAAW,CAACD,aACzC,UAACQ,MAAAA,CAAIC,UAAU,qBAAaX,EAAE,kBAC9B,UAACU,MAAAA,CAAIC,UAAU,qBACVT,EACG,UAACU,EAAAA,CAAeA,CAAAA,CAACC,KAAMC,EAAAA,GAAOA,CAAEC,MAAM,SAEtC,UAACH,EAAAA,CAAeA,CAAAA,CAACC,KAAMG,EAAAA,GAAMA,CAAED,MAAM,cAIjD,UAACV,EAAAA,CAASA,CAACY,IAAI,WACX,UAACgM,EAAAA,CAAWA,CAAAA,CACRC,QAASnN,EAAMwC,SAAS,CAAC4K,MAAM,CAC/BC,YAAarN,EAAMwC,SAAS,CAAC8K,UAAU,SAM/D", "sources": ["webpack://_N_E/./pages/operation/components/VirtualSpaceAccordian.tsx", "webpack://_N_E/./pages/operation/components/OperationDetailsAccordian.tsx", "webpack://_N_E/./pages/operation/permission.tsx", "webpack://_N_E/./pages/operation/components/DocumentsAccordian.tsx", "webpack://_N_E/./components/common/VspaceTable.tsx", "webpack://_N_E/./components/common/RKITable.tsx", "webpack://_N_E/./pages/operation/components/OperationAccordianSection.tsx", "webpack://_N_E/./components/common/DocumentTable.tsx", "webpack://_N_E/./node_modules/moment/locale/de.js", "webpack://_N_E/./pages/operation/OperationPartners.tsx", "webpack://_N_E/./pages/operation/components/PartnersAccordian.tsx", "webpack://_N_E/./pages/operation/components/MediaGalleryAccordian.tsx"], "sourcesContent": ["//Import Library\r\nimport React, { useState } from \"react\";\r\nimport { Accordion, Card } from \"react-bootstrap\"\r\nimport { FontAwesomeIcon } from \"@fortawesome/react-fontawesome\";\r\nimport { faMinus, faPlus } from \"@fortawesome/free-solid-svg-icons\";\r\n\r\n//Import services/components\r\nimport { useTranslation } from 'next-i18next';\r\nimport VspaceTable from \"../../../components/common/VspaceTable\";\r\n\r\nconst VirtualSpaceAccordian = (props: any) => {\r\n    const { t } = useTranslation('common');\r\n    const [section, setSection] = useState(false);\r\n    return (\r\n        <>\r\n            <Accordion.Item eventKey=\"0\">\r\n                <Accordion.Header onClick={() => setSection(!section)}>\r\n                    <div className=\"cardTitle\">{t(\"LinkedVirtualSpace\")}</div>\r\n                    <div className=\"cardArrow\">\r\n                        {section ? (\r\n                            <FontAwesomeIcon icon={faMinus} color=\"#fff\" />\r\n                        ) : (\r\n                            <FontAwesomeIcon icon={faPlus} color=\"#fff\" />\r\n                        )}\r\n                    </div>\r\n                </Accordion.Header>\r\n                <Accordion.Body>\r\n                    <VspaceTable\r\n                      id={props.routeData?.routes?.[1] || ''}\r\n                      type=\"Operation\"\r\n                      vspaceData={[]}\r\n                      vspaceDataLoading={false}\r\n                      vspaceDataTotalRows={0}\r\n                      vspaceDataPerPage={10}\r\n                      vspaceDataCurrentPage={1}\r\n                    />\r\n                </Accordion.Body>\r\n            </Accordion.Item>\r\n        </>\r\n    )\r\n}\r\n\r\nexport default VirtualSpaceAccordian;", "//Import Library\r\nimport React, { useState } from \"react\";\r\nimport { faMinus, faPlus } from \"@fortawesome/free-solid-svg-icons\";\r\nimport { FontAwesomeIcon } from \"@fortawesome/react-fontawesome\";\r\nimport moment from \"moment\";\r\nimport { Accordion, Card, Col, Row } from \"react-bootstrap\";\r\n\r\n//Import services/components\r\nimport { useTranslation } from 'next-i18next';\r\n\r\nconst OperationDetailsAccordian = (props: any) => {\r\n    const { t } = useTranslation('common');\r\n    const formatDate = \"MM-D-YYYY HH:mm:ss\";\r\n    const formatDateWithoutTime = \"MM-D-YYYY\";\r\n    const [section, setSection] = useState(false);\r\n    return (\r\n        <>\r\n            <Accordion.Item eventKey=\"0\">\r\n                <Accordion.Header onClick={() => setSection(!section)}>\r\n                    <div className=\"cardTitle\">{t(\"OperationDetails\")}</div>\r\n                    <div className=\"cardArrow\">\r\n                        {section ? (\r\n                            <FontAwesomeIcon icon={faMinus} color=\"#fff\" />\r\n                        ) : (\r\n                            <FontAwesomeIcon icon={faPlus} color=\"#fff\" />\r\n                        )}\r\n                    </div>\r\n                </Accordion.Header>\r\n                <Accordion.Body>\r\n                        <Row className=\"operationData\">\r\n                            <Col md lg={6} sm={12} className=\"ps-0\">\r\n                                <p>\r\n                                    <b>{t(\"HazardType\")}</b>:\r\n                                    <span>\r\n                                        {props.operation.hazard_type\r\n                                            ? props.operation.hazard_type.title\r\n                                            : null}\r\n                                    </span>\r\n                                </p>\r\n                                {hazard_separated_func(t, props.operation)}\r\n                                <p>\r\n                                    <b>{t(\"Syndrome\")}</b>:\r\n                                    <span>\r\n                                        {props.operation.syndrome\r\n                                            ? props.operation.syndrome.title\r\n                                            : null}\r\n                                    </span>\r\n                                </p>\r\n                                <p>\r\n                                    <b>{t(\"Created\")}</b>:\r\n                                    <span>\r\n                                        {moment(props.operation.created_at).format(\r\n                                            formatDate\r\n                                        )}\r\n                                    </span>\r\n                                </p>\r\n                                <p>\r\n                                    <b>{t(\"LastModified\")}</b>:\r\n                                    <span>\r\n                                        {moment(props.operation.updated_at).format(\r\n                                            formatDate\r\n                                        )}\r\n                                    </span>\r\n                                </p>\r\n                            </Col>\r\n\r\n                            <Col md lg={6} sm={12} className=\"ps-0\">\r\n                                <p>\r\n                                    <b>{t(\"CountryOrTerritory\")}</b>:\r\n                                    <span>\r\n                                        {props.operation.country\r\n                                            ? props.operation.country.title\r\n                                            : null}\r\n                                    </span>\r\n                                </p>\r\n                                <p>\r\n                                    <b>{t(\"OperationStatus\")}</b>:\r\n                                    <span> {props.operation.status.title} </span>\r\n                                </p>\r\n                                <p>\r\n                                    <b>{t(\"StartDate\")}</b>:\r\n                                    <span>\r\n                                        {props.operation.start_date\r\n                                            ? moment(props.operation.start_date).format(\r\n                                                formatDateWithoutTime\r\n                                            )\r\n                                            : null}\r\n                                    </span>\r\n                                </p>\r\n                                <p>\r\n                                    <b>{t(\"EndDate\")}</b>:\r\n                                    <span>\r\n                                        {props.operation.end_date\r\n                                            ? moment(props.operation.end_date).format(\r\n                                                formatDateWithoutTime\r\n                                            )\r\n                                            : null}\r\n                                    </span>\r\n                                </p>\r\n                            </Col>\r\n                        </Row>\r\n                </Accordion.Body>\r\n            </Accordion.Item>\r\n        </>\r\n    )\r\n}\r\n\r\nexport default OperationDetailsAccordian;\r\nfunction hazard_separated_func(\r\n    t: (p: string) => string,\r\n    operationData: {\r\n        title: string;\r\n        timeline: any[];\r\n        description: string;\r\n        hazard_type: { title: string };\r\n        hazard: any[];\r\n        syndrome: { title: string };\r\n        created_at: string;\r\n        updated_at: string;\r\n        country: { title: string };\r\n        status: { title: string };\r\n        start_date: string;\r\n        end_date: string;\r\n        partners: any[];\r\n        images: any[];\r\n        images_src: any[];\r\n        document: any[];\r\n        doc_src: any[];\r\n    }\r\n) {\r\n    return (\r\n        <div className=\"d-flex mb-2 pb-1\">\r\n            <b>{t(\"Hazard\")}</b>:\r\n            <span>\r\n                <ul className=\"comma-separated\">\r\n                    {operationData.hazard && operationData.hazard.length >= 1\r\n                        ? operationData.hazard.map((item, i) => {\r\n                            return <li key={i}>{item.title.en}</li>;\r\n                        })\r\n                        : null}\r\n                </ul>\r\n            </span>\r\n        </div>\r\n    );\r\n}", "//Import Library\r\nimport React from 'react';\r\nimport connectedAuthWrapper from 'redux-auth-wrapper/connectedAuthWrapper';\r\n\r\n//Import services/components\r\nimport R403 from \"../r403\";\r\n\r\nexport const canAddOperation = connectedAuthWrapper({\r\n  authenticatedSelector: (state) => {\r\n    if (state.permissions && state.permissions.operation && state.permissions.operation['create:any']) {\r\n      return true;\r\n    }\r\n    return false;\r\n  },\r\n  wrapperDisplayName: 'CanAddOperation',\r\n});\r\n\r\nexport const canAddOperationForm = connectedAuthWrapper({\r\n  authenticatedSelector: (state) => {\r\n    if (state.permissions && state.permissions.operation && state.permissions.operation['create:any']) {\r\n      return true;\r\n    }\r\n    return false;\r\n  },\r\n  wrapperDisplayName: 'CanAddOperationForm',\r\n  FailureComponent: () => <R403/>\r\n});\r\n\r\nexport const canEditOperation = connectedAuthWrapper({\r\n  authenticatedSelector: (state, props) => {\r\n    if (state.permissions && state.permissions.operation) {\r\n      if (state.permissions.operation['update:any']) {\r\n        return true;\r\n      } else { //if update:own\r\n        if (state.permissions.operation['update:own']) {\r\n          if (props.operation && props.operation.user && props.operation.user._id === state.user._id) {\r\n            return true;\r\n          }\r\n        }\r\n      }\r\n    }\r\n    return false;\r\n  },\r\n  wrapperDisplayName: 'CanEditOperation',\r\n});\r\n\r\nexport const canEditOperationForm = connectedAuthWrapper({\r\n  authenticatedSelector: (state, props) => {\r\n    if (state.permissions && state.permissions.operation) {\r\n      if (state.permissions.operation['update:any']) {\r\n        return true;\r\n      } else { //if update:own\r\n        if (state.permissions.operation['update:own']) {\r\n          if (props.operation && props.operation.user && props.operation.user._id === state.user._id) {\r\n            return true;\r\n          }\r\n        }\r\n      }\r\n    }\r\n    return false;\r\n  },\r\n  wrapperDisplayName: 'CanEditOperationForm',\r\n  FailureComponent: () => <R403/>\r\n});\r\n\r\nexport const canViewDiscussionUpdate = connectedAuthWrapper({\r\n  authenticatedSelector: (state) => {\r\n    if (state.permissions && state.permissions.update && state.permissions.update['read:any']) {\r\n      return true;\r\n    }\r\n    return false;\r\n  },\r\n  wrapperDisplayName: 'CanViewDiscussionUpdate',\r\n});\r\n\r\nexport default canAddOperation;", "//Import services/components\r\nimport React, { useState } from \"react\";\r\nimport { Accordion, Card } from \"react-bootstrap\";\r\nimport { FontAwesomeIcon } from \"@fortawesome/react-fontawesome\";\r\nimport { faMinus, faPlus } from \"@fortawesome/free-solid-svg-icons\";\r\n\r\n//Import services/components\r\nimport DocumentTable from \"../../../components/common/DocumentTable\";\r\nimport { useTranslation } from 'next-i18next';\r\n\r\nconst DocumentsAccordian = (props: any) => {\r\n    const { t } = useTranslation('common');\r\n    const [section, setSection] = useState(false);\r\n\r\n    return (\r\n        <>\r\n            <Accordion.Item eventKey=\"0\">\r\n                <Accordion.Header onClick={() => setSection(!section)}>\r\n                    <div className=\"cardTitle\">{t(\"Documents\")}</div>\r\n                    <div className=\"cardArrow\">\r\n                        {section ? (\r\n                            <FontAwesomeIcon icon={faMinus} color=\"#fff\" />\r\n                        ) : (\r\n                            <FontAwesomeIcon icon={faPlus} color=\"#fff\" />\r\n                        )}\r\n                    </div>\r\n                </Accordion.Header>\r\n                <Accordion.Body>\r\n                        <DocumentTable\r\n                            loading={props.documentAccoirdianData.documentAccoirdianProps.loading}\r\n                            sortProps={props.documentAccoirdianData.documentAccoirdianProps.sortProps}\r\n                            docs={props.documentAccoirdianData.documentAccoirdianProps.Document || []}\r\n                            docsDescription={props.documentAccoirdianData.operationData.doc_src}\r\n                        />\r\n                        <h6 className=\"mt-3\">{t(\"DocumentsfromUpdates\")}</h6>\r\n                        <DocumentTable\r\n                            loading={props.documentAccoirdianData.documentAccoirdianProps.loading}\r\n                            sortProps={props.documentAccoirdianData.documentAccoirdianProps.sortUpdateProps}\r\n                            docs={props.documentAccoirdianData.documentAccoirdianProps.updateDocument || []}\r\n                            docsDescription={props.documentAccoirdianData.operationData.doc_src}\r\n                        />\r\n                </Accordion.Body>\r\n            </Accordion.Item>\r\n        </>\r\n    )\r\n}\r\n\r\nexport default DocumentsAccordian;", "//Import Library\r\nimport { useEffect, useState } from \"react\";\r\nimport Link from 'next/link';\r\n\r\n//Import services/components\r\nimport RKITable from \"../../components/common/RKITable\";\r\nimport apiService from \"../../services/apiService\";\r\nimport { useTranslation } from 'next-i18next';\r\n\r\ninterface VspaceTableProps {\r\n  vspaceData: any;\r\n  vspaceDataLoading: boolean;\r\n  vspaceDataTotalRows: number;\r\n  vspaceDataPerPage: number;\r\n  vspaceDataCurrentPage: number;\r\n  type: string;\r\n  id: string;\r\n}\r\n\r\nconst VspaceTable = (props: VspaceTableProps) => {\r\n  const { t } = useTranslation('common');\r\n  const { type, id } = props;\r\n  const [tabledata, setDataToTable] = useState([]);\r\n  const [loading, setLoading] = useState(false);\r\n  const [totalRows, setTotalRows] = useState(0);\r\n  const [perPage, setPerPage] = useState(10);\r\n  const [resetPaginationToggle] = useState(false);\r\n\r\n  const vSpaceParams = {\r\n    sort: { created_at: \"asc\" },\r\n    limit: perPage,\r\n    page: 1,\r\n    query: {},\r\n  };\r\n\r\n  const columns = [\r\n    {\r\n      name: t(\"Title\"),\r\n      selector: \"title\",\r\n      cell: (d: any) => d && d.title && d._id ? <Link href=\"/vspace/[...routes]\" as={`/vspace/show/${d._id}`} >{d.title}</Link> : \"\",\r\n    },\r\n    {\r\n      name: t(\"Owner\"),\r\n      selector: \"users\",\r\n      cell: (d: any) => d && d.user && d.user.firstname ? `${d.user.firstname} ${d.user.lastname}` : \"\"\r\n    },\r\n    {\r\n      name: t(\"PublicPrivate\"),\r\n      selector: \"visibility\",\r\n      cell: (d: any) => d && d.visibility ? \"Public\" : \"Private\",\r\n\r\n    },\r\n    {\r\n      name: t(\"NumberofMembers\"),\r\n      selector: \"members\",\r\n      cell: (d: any) => d && d.members ? d.members.length : \"-\",\r\n    }\r\n  ];\r\n\r\n  const getLinkedVspace = async (vSpaceParams1: any) => {\r\n    setLoading(true);\r\n    const response = await apiService.get(`stats/get${type}WithVspace/${id}`, vSpaceParams);\r\n    if (response) {\r\n      type === \"Operation\" ? setDataToTable(response.operation) : setDataToTable(response.project);\r\n      setTotalRows(response.totalCount);\r\n      setLoading(false);\r\n    }\r\n  };\r\n\r\n  const handlePageChange = (page: number) => {\r\n    vSpaceParams.limit = perPage;\r\n    vSpaceParams.page = page;\r\n    getLinkedVspace(vSpaceParams);\r\n  };\r\n\r\n  const handlePerRowsChange = async (newPerPage: number, page: number) => {\r\n    vSpaceParams.limit = newPerPage;\r\n    vSpaceParams.page = page;\r\n    setLoading(true);\r\n    const response = await apiService.get(`stats/get${type}WithVspace/${id}`, vSpaceParams);\r\n    if (response) {\r\n      type === \"Operation\" ? setDataToTable(response.operation) : setDataToTable(response.project);\r\n      setPerPage(newPerPage);\r\n      setLoading(false);\r\n    }\r\n  };\r\n\r\n  useEffect(() => {\r\n    getLinkedVspace(vSpaceParams);\r\n  }, []);\r\n\r\n\r\n\r\n  return (\r\n    <div>\r\n      <RKITable\r\n        columns={columns}\r\n        data={tabledata}\r\n        totalRows={totalRows}\r\n        loading={loading}\r\n        resetPaginationToggle={resetPaginationToggle}\r\n        handlePerRowsChange={handlePerRowsChange}\r\n        handlePageChange={handlePageChange}\r\n      />\r\n    </div>\r\n  );\r\n}\r\n\r\nexport default VspaceTable;\r\n", "//Import Library\r\nimport React from 'react'\r\nimport DataTable  from 'react-data-table-component';\r\n\r\n//Import services/components\r\nimport { useTranslation } from 'next-i18next';\r\nimport CustomLoader from './CustomLoader';\r\n\r\ninterface RKITableProps {\r\n  columns: any[];\r\n  data: any[];\r\n  totalRows?: number;\r\n  resetPaginationToggle?: boolean;\r\n  subheader?: boolean;\r\n  subHeaderComponent?: React.ReactNode;\r\n  handlePerRowsChange?: (newPerPage: number, page: number) => void;\r\n  handlePageChange?: (page: number) => void;\r\n  rowsPerPage?: number[];\r\n  defaultRowsPerPage?: number;\r\n  selectableRows?: boolean;\r\n  loading?: boolean;\r\n  pagServer?: boolean;\r\n  onSelectedRowsChange?: (selectedRows: any) => void;\r\n  clearSelectedRows?: boolean;\r\n  sortServer?: boolean;\r\n  onSort?: (column: any, sortDirection: string) => void;\r\n  persistTableHead?: boolean;\r\n  sortFunction?: (rows: any[], field: any, direction: 'asc' | 'desc') => any[];\r\n  noHeader?: boolean;\r\n  dense?: boolean;\r\n  pagination?: boolean;\r\n  paginationServer?: boolean;\r\n  paginationTotalRows?: number;\r\n  subHeaderAlign?: string;\r\n  [key: string]: any;\r\n}\r\n\r\nfunction RKITable(props: RKITableProps) {\r\n  const { t } = useTranslation('common');\r\n  const paginationComponentOptions = {\r\n    rowsPerPageText: t('Rowsperpage'),\r\n    };\r\n  const {\r\n    columns,\r\n    data,\r\n    totalRows,\r\n    resetPaginationToggle,\r\n    subheader,\r\n    subHeaderComponent,\r\n    handlePerRowsChange,\r\n    handlePageChange,\r\n    rowsPerPage,\r\n    defaultRowsPerPage,\r\n    selectableRows,\r\n    loading,\r\n    pagServer,\r\n    onSelectedRowsChange,\r\n    clearSelectedRows,\r\n    sortServer,\r\n    onSort,\r\n    persistTableHead,\r\n    sortFunction,\r\n    // Filter out any unknown props that might cause DOM warnings\r\n    ...otherProps\r\n  } = props;\r\n\r\n  // Only pass known DataTable props to avoid DOM warnings\r\n  const dataTableProps = {\r\n    paginationComponentOptions,\r\n    noDataComponent: t(\"NoData\"),\r\n    noHeader: true,\r\n    columns,\r\n    data: data || [],\r\n    dense: true,\r\n    paginationResetDefaultPage: resetPaginationToggle,\r\n    subHeader: subheader,\r\n    progressPending: loading,\r\n    subHeaderComponent,\r\n    pagination: true,\r\n    paginationServer: pagServer,\r\n    paginationPerPage: defaultRowsPerPage ? defaultRowsPerPage : 10,\r\n    paginationRowsPerPageOptions: rowsPerPage ? rowsPerPage : [10, 15, 20, 25, 30],\r\n    paginationTotalRows: totalRows,\r\n    onChangeRowsPerPage: handlePerRowsChange,\r\n    onChangePage: handlePageChange,\r\n    selectableRows,\r\n    onSelectedRowsChange,\r\n    clearSelectedRows,\r\n    progressComponent: <CustomLoader/>,\r\n    sortIcon: <i className=\"sort-icon fas fa-exchange-alt\" />,\r\n    sortServer,\r\n    onSort,\r\n    sortFunction,\r\n    persistTableHead,\r\n    className: \"rki-table\"\r\n  };\r\n  return (\r\n    <DataTable {...dataTableProps} />\r\n  )\r\n};\r\n\r\nRKITable.defaultProps = {\r\n  subHeader: false,\r\n  pagination: true,\r\n  totalRows: null,\r\n  pagServer: true,\r\n  onSelectedRowsChange: null,\r\n  clearSelectedRows: false,\r\n  sortServer: false,\r\n  persistTableHead: false\r\n}\r\n\r\nexport default RKITable;\r\n\r\n", "//Import Library\r\nimport React, { useState } from \"react\";\r\nimport { Accordion, Card, Col, Row } from \"react-bootstrap\";\r\nimport PartnersAccordian from \"./PartnersAccordian\";\r\nimport { FontAwesomeIcon } from \"@fortawesome/react-fontawesome\";\r\nimport { faMinus, faPlus } from \"@fortawesome/free-solid-svg-icons\";\r\n\r\n//Import services/components\r\nimport { useTranslation } from 'next-i18next';\r\nimport Discussion from \"../../../components/common/disussion\";\r\nimport { canViewDiscussionUpdate } from \"../permission\";\r\nimport VirtualSpaceAccordian from \"./VirtualSpaceAccordian\";\r\nimport OperationDetailsAccordian from \"./OperationDetailsAccordian\";\r\nimport MediaGalleryAccordian from \"./MediaGalleryAccordian\";\r\nimport DocumentsAccordian from \"./DocumentsAccordian\";\r\n\r\nconst OperationAccordianSection = (props: any) => {\r\n    const { t } = useTranslation('common');\r\n    const [section, setSection] = useState(false);\r\n    console.log(props, \"propsdata\")\r\n\r\n    const DiscussionComponent = () => {\r\n        return (\r\n            <Accordion.Item eventKey=\"0\">\r\n                <Accordion.Header onClick={() => setSection(!section)}>\r\n                    <div className=\"cardTitle\">{t(\"Discussions\")}</div>\r\n                    <div className=\"cardArrow\">\r\n                        {section ? (\r\n                            <FontAwesomeIcon icon={faMinus} color=\"#fff\" />\r\n                        ) : (\r\n                            <FontAwesomeIcon icon={faPlus} color=\"#fff\" />\r\n                        )}\r\n                    </div>\r\n                </Accordion.Header>\r\n                <Accordion.Body>\r\n                    <Discussion\r\n                        type=\"operation\"\r\n                        id={props?.routeData?.routes ? props.routeData.routes[1] : null}\r\n                    />\r\n                </Accordion.Body>\r\n            </Accordion.Item>\r\n        );\r\n    };\r\n\r\n    const CanViewDiscussionUpdate = canViewDiscussionUpdate(() => (\r\n        <DiscussionComponent />\r\n    ));\r\n    return (\r\n        <>\r\n            <Row>\r\n                <Col className=\"operationAccordion\" xs={12}>\r\n                    <Accordion>\r\n                        <OperationDetailsAccordian operation={props.operationData} />\r\n                    </Accordion>\r\n\r\n                    <Accordion>\r\n                        <PartnersAccordian operation={props.operationData} />\r\n                    </Accordion>\r\n\r\n                    <Accordion>\r\n                        <MediaGalleryAccordian operation={props.operationData} />\r\n                    </Accordion>\r\n\r\n                    <Accordion>\r\n                        <DocumentsAccordian documentAccoirdianData={props} />\r\n                    </Accordion>\r\n\r\n                    <Accordion>\r\n                        <CanViewDiscussionUpdate />\r\n                    </Accordion>\r\n                    <Accordion>\r\n                        <VirtualSpaceAccordian routeData={props.routeData} />\r\n                    </Accordion>\r\n                </Col>\r\n            </Row>\r\n        </>\r\n    )\r\n}\r\n\r\nexport default OperationAccordianSection;", "//Import Library\r\nimport React from \"react\";\r\nimport moment from \"moment\";\r\n\r\n//Import services/components\r\nimport RKITable from '../../components/common/RKITable';\r\nimport { useTranslation } from 'next-i18next';\r\n\r\ninterface DocumentTableProps {\r\n  docs: any[];\r\n  docsDescription: string;\r\n  sortProps: (sortObj: { columnSelector: string; sortDirection: string }) => void;\r\n  loading: boolean;\r\n}\r\n\r\nconst DocumentTable: React.FC<DocumentTableProps> = ({ docs, docsDescription, sortProps, loading }) => {\r\n\r\n  const handleSort = async (column: any, sortDirection: string) => {\r\n    const objSlect = {\r\n      columnSelector: column.selector,\r\n      sortDirection: sortDirection\r\n    }\r\n    sortProps(objSlect);\r\n  };\r\n\r\n  const { t } = useTranslation('common');\r\n\r\n\r\n  const columns = [\r\n    {\r\n      name: t(\"FileType\"),\r\n      width: \"15%\",\r\n      selector: 'extension',\r\n      cell: (d: any) => d && d.extension && d.extension,\r\n    },\r\n    {\r\n      name: t(\"FileName\"),\r\n      width: \"25%\",\r\n      selector: \"document_title\",\r\n      cell: (d: any) => d && d.original_name && <a href={`${process.env.API_SERVER}/files/download/${d._id}`} target=\"_blank\">{d.original_name.split('.').slice(0, -1).join('.')}</a>,\r\n      sortable: true\r\n    },\r\n    {\r\n      name: t(\"Description\"),\r\n      selector: 'description',\r\n      cell: (d: any) => d && d.description && d.description,\r\n    },\r\n    {\r\n      name: t(\"UploadedDate\"),\r\n      width: \"25%\",\r\n      selector: 'doc_created_at',\r\n      cell: (d: any) => d && d.updated_at && moment(d.updated_at).format('MM/DD/YYYY'),\r\n      sortable: true\r\n    }\r\n  ];\r\n\r\n  return (\r\n    <RKITable\r\n      columns={columns}\r\n      data={docs}\r\n      pagServer={true}\r\n      onSort={handleSort}\r\n      persistTableHead\r\n      loading={loading}\r\n    />\r\n\r\n  )\r\n}\r\n\r\nexport default DocumentTable;\r\n", "//! moment.js locale configuration\n//! locale : German [de]\n//! author : lluchs : https://github.com/lluchs\n//! author: <PERSON><PERSON><PERSON>: https://github.com/Oire\n//! author : <PERSON><PERSON><PERSON><PERSON> : https://github.com/mik01aj\n\n;(function (global, factory) {\n   typeof exports === 'object' && typeof module !== 'undefined'\n       && typeof require === 'function' ? factory(require('../moment')) :\n   typeof define === 'function' && define.amd ? define(['../moment'], factory) :\n   factory(global.moment)\n}(this, (function (moment) { 'use strict';\n\n    //! moment.js locale configuration\n\n    function processRelativeTime(number, withoutSuffix, key, isFuture) {\n        var format = {\n            m: ['eine Minute', 'einer Minute'],\n            h: ['eine Stunde', 'einer Stunde'],\n            d: ['ein Tag', 'einem Tag'],\n            dd: [number + ' Tage', number + ' Tagen'],\n            w: ['eine Woche', 'einer Woche'],\n            M: ['ein <PERSON><PERSON>', 'einem Monat'],\n            MM: [number + ' <PERSON><PERSON>', number + ' Monaten'],\n            y: ['ein Jahr', 'einem Jahr'],\n            yy: [number + ' Jahre', number + ' Jahren'],\n        };\n        return withoutSuffix ? format[key][0] : format[key][1];\n    }\n\n    var de = moment.defineLocale('de', {\n        months: 'Januar_Februar_März_April_Mai_Juni_Juli_August_September_Oktober_November_Dezember'.split(\n            '_'\n        ),\n        monthsShort:\n            'Jan._Feb._März_Apr._Mai_Juni_Juli_Aug._Sep._Okt._Nov._Dez.'.split('_'),\n        monthsParseExact: true,\n        weekdays:\n            'Sonntag_Montag_Dienstag_Mittwoch_Donnerstag_Freitag_Samstag'.split(\n                '_'\n            ),\n        weekdaysShort: 'So._Mo._Di._Mi._Do._Fr._Sa.'.split('_'),\n        weekdaysMin: 'So_Mo_Di_Mi_Do_Fr_Sa'.split('_'),\n        weekdaysParseExact: true,\n        longDateFormat: {\n            LT: 'HH:mm',\n            LTS: 'HH:mm:ss',\n            L: 'DD.MM.YYYY',\n            LL: 'D. MMMM YYYY',\n            LLL: 'D. MMMM YYYY HH:mm',\n            LLLL: 'dddd, D. MMMM YYYY HH:mm',\n        },\n        calendar: {\n            sameDay: '[heute um] LT [Uhr]',\n            sameElse: 'L',\n            nextDay: '[morgen um] LT [Uhr]',\n            nextWeek: 'dddd [um] LT [Uhr]',\n            lastDay: '[gestern um] LT [Uhr]',\n            lastWeek: '[letzten] dddd [um] LT [Uhr]',\n        },\n        relativeTime: {\n            future: 'in %s',\n            past: 'vor %s',\n            s: 'ein paar Sekunden',\n            ss: '%d Sekunden',\n            m: processRelativeTime,\n            mm: '%d Minuten',\n            h: processRelativeTime,\n            hh: '%d Stunden',\n            d: processRelativeTime,\n            dd: processRelativeTime,\n            w: processRelativeTime,\n            ww: '%d Wochen',\n            M: processRelativeTime,\n            MM: processRelativeTime,\n            y: processRelativeTime,\n            yy: processRelativeTime,\n        },\n        dayOfMonthOrdinalParse: /\\d{1,2}\\./,\n        ordinal: '%d.',\n        week: {\n            dow: 1, // Monday is the first day of the week.\n            doy: 4, // The week that contains Jan 4th is the first week of the year.\n        },\n    });\n\n    return de;\n\n})));\n", "//Import Library\r\nimport Link from \"next/link\";\r\nimport _ from \"lodash\";\r\nimport { Popover, OverlayTrigger } from \"react-bootstrap\";\r\n\r\n//Import services/components\r\nimport RKITable from \"../../components/common/RKITable\";\r\nimport { useTranslation } from 'next-i18next';\r\n\r\nconst Networks = ({ networks }: { networks: any[]}) => {\r\n  if (networks && networks.length > 0) {\r\n    return (\r\n      <ul>\r\n        {networks.map((item, index) => {\r\n          return <li key={index}>{item.title}</li>;\r\n        })}\r\n      </ul>\r\n    );\r\n  }\r\n  return null;\r\n};\r\n\r\n// For network popover\r\nconst Networkpopover = (\r\n  <Popover id=\"popover-basic\">\r\n    <Popover.Header as=\"h3\" className=\"text-center\">\r\n      NETWORKS\r\n    </Popover.Header>\r\n    <Popover.Body>\r\n      <div className=\"m-2\">\r\n        <p>\r\n          <b>EMLab</b> - European Mobile Lab\r\n        </p>\r\n        <p>\r\n          <b>EMT</b> - Emergency Medical Teams\r\n        </p>\r\n        <p>\r\n          <b>GHPP</b> - Global Health Protection Program\r\n        </p>\r\n        <p>\r\n          <b>GOARN</b> - Global Outbreak Alert & Response Network\r\n        </p>\r\n        <p>\r\n          <b>IANPHI</b> - International Association of National Public Health\r\n          Institutes\r\n        </p>\r\n        <p>\r\n          <b>STAKOB</b> - Ständiger Arbeitskreis der Kompetenz-und\r\n          Behandlungszentren\r\n        </p>\r\n        <p>\r\n          <b>WHOCC</b>- World Health Organization Collaborating Centres\r\n        </p>\r\n      </div>\r\n    </Popover.Body>\r\n  </Popover>\r\n);\r\n\r\nfunction OperationPartners(props: any) {\r\n  const { t } = useTranslation('common');\r\n  const { partners } = props;\r\n  const columns = [\r\n    {\r\n      name: t(\"Organisation\"),\r\n      selector: \"title\",\r\n      cell: (d: any) =>\r\n        d && d.institution ? (\r\n          <Link\r\n            href=\"/institution/[...routes]\"\r\n            as={`/institution/show/${d.institution._id}`}\r\n          >\r\n            {d.institution.title}\r\n          </Link>\r\n        ) : (\r\n          \"\"\r\n        ),\r\n      sortable: true,\r\n    },\r\n    {\r\n      name: t(\"Country\"),\r\n      selector: \"country\",\r\n      cell: (d: any) =>\r\n        d &&\r\n        d.institution &&\r\n        d.institution.address &&\r\n        d.institution.address.country ? (\r\n          <Link\r\n            href=\"/country/[...routes]\"\r\n            as={`/country/show/${d.institution.address.country._id}`}\r\n          >\r\n            {d.institution.address.country.title}\r\n          </Link>\r\n        ) : (\r\n          \"\"\r\n        ),\r\n      sortable: true,\r\n    },\r\n    {\r\n      name: t(\"Type\"),\r\n      selector: \"type.title\",\r\n      cell: (d: any) =>\r\n        d.institution && d.institution.type && d.institution.type.title\r\n          ? d.institution.type.title\r\n          : \"\",\r\n      sortable: true,\r\n    },\r\n    {\r\n      name: (\r\n        <OverlayTrigger\r\n          trigger=\"click\"\r\n          placement=\"right\"\r\n          overlay={Networkpopover}\r\n        >\r\n          <span>\r\n            {t(\"Network\")}&nbsp;&nbsp;&nbsp;\r\n            <i\r\n              className=\"fa fa-info-circle\"\r\n              style={{ cursor: \"pointer\" }}\r\n              aria-hidden=\"true\"\r\n            ></i>\r\n          </span>\r\n        </OverlayTrigger>\r\n      ),\r\n      selector: t(\"Networks\"),\r\n      cell: (d: any) =>\r\n        d.institution &&\r\n        d.institution.networks &&\r\n        d.institution.networks.length > 0 ? (\r\n          <Networks networks={d.institution.networks} />\r\n        ) : (\r\n          \"\"\r\n        ),\r\n    },\r\n  ];\r\n\r\n  const get_field = (row: any) => {\r\n    if (row.institution.address && row.institution.address.country) {\r\n      if (\r\n        row.institution.address.country &&\r\n        row.institution.address.country.title\r\n      ) {\r\n        return row.institution.address.country.title.toLowerCase();\r\n      }\r\n      return row.institution.address.country.title;\r\n    }\r\n  };\r\n\r\n  const get_fieldtitle = (row: any) => {\r\n    if (row.institution.type) {\r\n      if (row.institution.type && row.institution.type.title) {\r\n        return row.institution.type.title.toLowerCase();\r\n      }\r\n    }\r\n  };\r\n\r\n  const customSort = (rows: any, field: string, direction: any) => {\r\n    const handleField = (row: any) => {\r\n      if (field === \"country\") {\r\n        get_field(row);\r\n      } else if (field === \"type.title\") {\r\n        get_fieldtitle(row);\r\n      } else {\r\n        if (row.institution && row.institution[field]) {\r\n          return row.institution[field].toLowerCase();\r\n        }\r\n      }\r\n    };\r\n    return _.orderBy(rows, handleField, direction);\r\n  };\r\n\r\n  return (\r\n    <RKITable\r\n      columns={columns}\r\n      data={partners}\r\n      pagServer={true}\r\n      persistTableHead\r\n      sortFunction={customSort}\r\n    />\r\n  );\r\n}\r\n\r\nexport default OperationPartners;\r\n", "//Import Library\r\nimport { faMinus, faPlus } from \"@fortawesome/free-solid-svg-icons\";\r\nimport { FontAwesomeIcon } from \"@fortawesome/react-fontawesome\";\r\nimport React, { useState } from \"react\";\r\nimport { Accordion, Card } from \"react-bootstrap\";\r\n\r\n//Import services/components\r\nimport OperationPartners from \"../OperationPartners\";\r\nimport { useTranslation } from 'next-i18next';\r\n\r\nconst PartnersAccordian = (props: any) => {\r\n    const { t } = useTranslation('common');\r\n    const [section, setSection] = useState(false);\r\n    return (\r\n        <>\r\n            <Accordion.Item eventKey=\"0\">\r\n                <Accordion.Header onClick={() => setSection(!section)}>\r\n                    <div className=\"cardTitle\">{t(\"Partners\")}</div>\r\n                    <div className=\"cardArrow\">\r\n                        {section ? (\r\n                            <FontAwesomeIcon icon={faMinus} color=\"#fff\" />\r\n                        ) : (\r\n                            <FontAwesomeIcon icon={faPlus} color=\"#fff\" />\r\n                        )}\r\n                    </div>\r\n                </Accordion.Header>\r\n                <Accordion.Body>\r\n                    <OperationPartners partners={props.operation.partners} />\r\n                </Accordion.Body>\r\n            </Accordion.Item>\r\n        </>\r\n    )\r\n}\r\n\r\nexport default PartnersAccordian;", "//Import services/components\r\nimport React, { useState } from \"react\";\r\nimport { Accordion, Card } from \"react-bootstrap\";\r\nimport { FontAwesomeIcon } from \"@fortawesome/react-fontawesome\";\r\nimport { faMinus, faPlus } from \"@fortawesome/free-solid-svg-icons\";\r\n\r\n//Import services/components\r\nimport { useTranslation } from 'next-i18next';\r\nimport ReactImages from \"../../../components/common/ReactImages\";\r\n\r\nconst MediaGalleryAccordian = (props: any) => {\r\n    const { t } = useTranslation('common');\r\n    const [section, setSection] = useState(false);\r\n    return (\r\n        <>\r\n            <Accordion.Item eventKey=\"0\">\r\n                <Accordion.Header onClick={() => setSection(!section)}>\r\n                    <div className=\"cardTitle\">{t(\"MediaGallery\")}</div>\r\n                    <div className=\"cardArrow\">\r\n                        {section ? (\r\n                            <FontAwesomeIcon icon={faMinus} color=\"#fff\" />\r\n                        ) : (\r\n                            <FontAwesomeIcon icon={faPlus} color=\"#fff\" />\r\n                        )}\r\n                    </div>\r\n                </Accordion.Header>\r\n                <Accordion.Body>\r\n                    <ReactImages\r\n                        gallery={props.operation.images}\r\n                        imageSource={props.operation.images_src}\r\n                    />\r\n                </Accordion.Body>\r\n            </Accordion.Item>\r\n        </>\r\n    )\r\n}\r\n\r\nexport default MediaGalleryAccordian;"], "names": ["props", "t", "useTranslation", "section", "setSection", "useState", "Accordion", "<PERSON><PERSON>", "eventKey", "Header", "onClick", "div", "className", "FontAwesomeIcon", "icon", "faMinus", "color", "faPlus", "Body", "VspaceTable", "id", "routeData", "routes", "type", "vspaceData", "vspaceDataLoading", "vspaceDataTotalRows", "vspaceDataPerPage", "vspaceDataCurrentPage", "OperationDetailsAccordian", "formatDate", "formatDateWithoutTime", "Row", "Col", "md", "lg", "sm", "p", "b", "span", "operation", "hazard_type", "title", "hazard_separated_func", "operationData", "ul", "hazard", "length", "map", "item", "i", "li", "en", "syndrome", "moment", "created_at", "format", "updated_at", "country", "status", "start_date", "end_date", "canAddOperation", "connectedAuthWrapper", "authenticatedSelector", "state", "permissions", "wrapperDisplayName", "FailureComponent", "R403", "canEditOperation", "user", "_id", "update", "DocumentsAccordian", "DocumentTable", "loading", "documentAccoirdianData", "documentAccoirdianProps", "sortProps", "docs", "Document", "docsDescription", "doc_src", "h6", "sortUpdateProps", "updateDocument", "tabledata", "setDataToTable", "setLoading", "totalRows", "setTotalRows", "perPage", "setPerPage", "resetPaginationToggle", "vSpaceParams", "sort", "limit", "page", "query", "columns", "name", "selector", "cell", "d", "Link", "href", "as", "firstname", "lastname", "visibility", "members", "getLinkedVspace", "vSpaceParams1", "response", "apiService", "get", "project", "totalCount", "handlePerRowsChange", "newPerPage", "useEffect", "RKITable", "data", "handlePageChange", "paginationComponentOptions", "rowsPerPageText", "subheader", "subHeaderComponent", "rowsPerPage", "defaultRowsPerPage", "selectableRows", "pagServer", "onSelectedRowsChange", "clearSelectedRows", "sortServer", "onSort", "persistTableHead", "sortFunction", "otherProps", "dataTableProps", "noDataComponent", "<PERSON><PERSON><PERSON><PERSON>", "dense", "paginationResetDefaultPage", "subHeader", "progressPending", "pagination", "paginationServer", "paginationPerPage", "paginationRowsPerPageOptions", "paginationTotalRows", "onChangeRowsPerPage", "onChangePage", "progressComponent", "CustomLoader", "sortIcon", "DataTable", "defaultProps", "OperationAccordianSection", "console", "log", "DiscussionComponent", "Discussion", "CanViewDiscussionUpdate", "canViewDiscussionUpdate", "xs", "PartnersAccordian", "MediaGalleryAccordian", "VirtualSpaceAccordian", "handleSort", "column", "sortDirection", "columnSelector", "objSlect", "width", "extension", "original_name", "a", "process", "target", "split", "slice", "join", "sortable", "description", "Networks", "networks", "index", "Networkpopover", "Popover", "OperationPartners", "partners", "institution", "address", "OverlayTrigger", "trigger", "placement", "overlay", "style", "cursor", "aria-hidden", "get_field", "row", "toLowerCase", "get_fieldtitle", "rows", "field", "direction", "_", "ReactImages", "gallery", "images", "imageSource", "images_src"], "sourceRoot": "", "ignoreList": [8]}