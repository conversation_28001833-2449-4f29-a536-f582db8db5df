import React from 'react';
import { Toaster } from 'react-hot-toast';
import { Spinner } from 'react-bootstrap';
import App, { AppContext } from 'next/app';
import { createWrapper } from 'next-redux-wrapper';

//Import services/components
import { appWithTranslation } from 'next-i18next';
import Layout from '../components/layout/authenticated/Layout';
import Footer from '../components/layout/authenticated/Footer';
import '../styles/global.scss';
import withAuthSync from '../components/hoc/AuthSync';
import createStore from '../store';
import { canAccessRoutes } from './routePermissions';
import { GoogleMapsProvider } from '../components/common/GoogleMapsProvider';
import { useRouter } from 'next/router';


// Create the wrapper with proper configuration to avoid console warnings
const wrapper = createWrapper(createStore, {
  debug: false, // Disable debug to reduce console messages
  serializeState: (state) => state,
  deserializeState: (state) => state,
});

if (process.env.NODE_ENV === 'development' && module.hot) {
  module.hot.addStatusHandler(status => {
    if (typeof window !== 'undefined' && status === 'ready') {
      (window as any)['__webpack_reload_css__'] = true;
    }
  });
}

interface MyAppProps {
  Component: React.ComponentType<any>;
  pageProps: Record<string, any>;
  router: any;
  isPublicRoute: boolean;
  isLoading: boolean;
}

function MyApp({Component, pageProps, router, isPublicRoute, isLoading}: MyAppProps) {

  const CanAccessRoutes = canAccessRoutes(() => <Component {...pageProps} router={router}/>);
  const { locale } = useRouter();

  function public_route_func(): React.ReactNode {
    return isPublicRoute ? (<Component {...pageProps} router={router} />) : (
      <Layout router={router}>
        <CanAccessRoutes router={router} pageProps={pageProps} />
        <Footer />
      </Layout>
    );
  }

  return (
    <GoogleMapsProvider language={locale || 'en'}>
      {isLoading ? (
        <Spinner animation="border" variant="primary"/>
      ) : (
        <>
          {public_route_func()}
          <Toaster position="top-right" reverseOrder={false} />
        </>
      )}
    </GoogleMapsProvider>
  );
}

MyApp.getInitialProps = async (appContext: AppContext) => {
  const appProps = await App.getInitialProps(appContext)
  return {...appProps}
}

// Apply HOCs in the correct order for modern wrapper
const WrappedApp = withAuthSync(appWithTranslation(MyApp));
export default wrapper.withRedux(WrappedApp);