import React from 'react';
import {Provider} from 'react-redux'
import { createWrapper } from 'next-redux-wrapper'
import {PersistGate} from 'redux-persist/integration/react'
import {persistStore} from 'redux-persist'
import { Toaster } from 'react-hot-toast';
import { Spinner } from 'react-bootstrap';
import App, { AppContext } from 'next/app';

//Import services/components
import { appWithTranslation } from 'next-i18next';
import Layout from '../components/layout/authenticated/Layout';
import Footer from '../components/layout/authenticated/Footer';
import '../styles/global.scss';
import withAuthSync from '../components/hoc/AuthSync';
import createStore from '../store';
import { canAccessRoutes } from './routePermissions';
import { GoogleMapsProvider } from '../components/common/GoogleMapsProvider';
import { useRouter } from 'next/router';


// Create the wrapper
const wrapper = createWrapper(createStore, { debug: process.env.NODE_ENV === 'development' });

if (process.env.NODE_ENV === 'development' && module.hot) {
  module.hot.addStatusHandler(status => {
    if (typeof window !== 'undefined' && status === 'ready') {
      (window as any)['__webpack_reload_css__'] = true;
    }
  });
}

interface MyAppProps {
  Component: React.ComponentType<any>;
  pageProps: Record<string, any>;
  router: any;
  isPublicRoute: boolean;
  isLoading: boolean;
}

function MyApp({Component, pageProps, router, isPublicRoute, isLoading, ...rest}: MyAppProps & any) {

  const CanAccessRoutes = canAccessRoutes(() => <Component {...pageProps} router={router}/>);
  const { locale } = useRouter();

  // Get store from wrapper
  const store = wrapper.useWrappedStore(rest).store;
  const persistor = persistStore(store);

  return (
    <GoogleMapsProvider language={locale || 'en'}>
    <Provider store={store}>
      <PersistGate loading={null} persistor={persistor}>
        {isLoading ? (
            <Spinner animation="border" variant="primary"/>
          ) : (
          <>
            {public_route_func()}
            <Toaster position="top-right" reverseOrder={false} />
          </>
        )}
      </PersistGate>
    </Provider>
    </GoogleMapsProvider>

  )

  function public_route_func(): React.ReactNode {
    return isPublicRoute ? (<Component {...pageProps} router={router} />) : (
      <Layout router={router}>
        <CanAccessRoutes router={router} pageProps={pageProps} />
        <Footer />
      </Layout>
    );
  }
}

MyApp.getInitialProps = async (appContext: AppContext) => {
  const appProps = await App.getInitialProps(appContext)
  return {...appProps}
}

export default wrapper.withRedux(appWithTranslation(withAuthSync(MyApp)));