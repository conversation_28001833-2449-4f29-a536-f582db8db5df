{"version": 3, "file": "static/chunks/pages/adminsettings/worldregion/form-1cd4edf21eb4d600.js", "mappings": "sTAuKA,MArJyBA,IACrB,GAAM,CAAEC,GAAC,CAAE,CAAGC,CAAAA,EAAAA,EAAAA,EAAAA,CAAcA,CAAC,MAoJlBC,IAlJLC,EAAsB,CACxBC,MAAO,EAiJeF,CAhJtBG,CAgJuB,IAhJjB,EACV,EAEM,CAACC,EAAYC,EAAc,CAAGC,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAcL,GAEpDM,EAAWV,EAAMW,MAAM,EAAwB,qBAApBX,EAAMW,MAAM,CAAC,EAAE,EAA2BX,EAAMW,MAAM,CAAC,EAAE,CAEpFC,EAAUC,CAAAA,EAAAA,EAAAA,MAAAA,CAAMA,CAAC,MAQjBC,EAAe,IACjB,GAAIC,EAAEC,MAAM,CAAE,CACV,GAAM,MAAEC,CAAI,OAAEC,CAAK,CAAE,CAAGH,EAAEC,MAAM,CAChCR,EAAc,GAAgB,EAC1B,GAAGW,CAAS,CACZ,CAACF,CAFyB,CAEpB,CAAEC,EACZ,EACJ,CACJ,EAEME,EAAe,MAAOC,QAOpBC,EACAC,EAPJF,EAAMG,cAAc,GACpB,IAAMC,EAAM,CACRpB,MAAOE,EAAWF,KAAK,CAACqB,IAAI,GAC5BpB,KAAMC,EAAWD,IAAI,EAKrBI,GACAa,EAAW,KADD,4DAEVD,EAAW,MAAMK,EAAAA,CAAUA,CAACC,KAAK,CAAC,gBAAgC,OAAhB5B,EAAMW,MAAM,CAAC,EAAE,EAAIc,KAErEF,EAAW,+DACXD,EAAW,MAAMK,EAAAA,CAAUA,CAACE,IAAI,CAAC,eAAgBJ,IAEjDH,GAAYA,EAASQ,GAAG,EAAE,EAC1BC,EAAKA,CAACC,OAAO,CAAC/B,EAAEsB,IAChBU,IAAAA,IAAW,CAAC,+BAERX,OAAAA,EAAAA,KAAAA,EAAAA,EAAUY,SAAAA,CAAVZ,GAAwB,KACxBS,EAD+B,EAC1BA,CAACI,KAAK,CAAClC,EAAE,yBAEd8B,EAAAA,EAAKA,CAACI,KAAK,CAACb,EAGxB,EAiBA,MAfAc,CAAAA,EAAAA,EAAAA,SAAAA,CAASA,CAAC,KACN,IAAMC,EAAoB,CACtBC,MAAO,CAAC,EACRC,KAAM,CAAElC,MAAO,KAAM,EACrBmC,MAAO,GACX,EACI9B,GAKA+B,CAJ2B,MADjB,IAEN,IAAMnB,EAAwB,MAAMK,EAAAA,CAAUA,CAACe,GAAG,CAAC,gBAAgC,OAAhB1C,EAAMW,MAAM,CAAC,EAAE,EAAI0B,GACtF7B,EAAc,GAAgB,EAAE,GAAGW,CAAS,CAAE,EAAhB,CAAmBG,CAAQ,CAAC,GAC9D,GAGR,EAAG,EAAE,EAGD,UAACqB,MAAAA,UACG,UAACC,EAAAA,CAASA,CAAAA,CAACC,UAAU,WAAWC,KAAK,aACjC,UAACC,EAAAA,CAAIA,CAAAA,CACDC,MAAO,CACHC,UAAW,MACXC,UAAW,kEACf,WAEA,UAACC,EAAAA,CAAqBA,CAAAA,CAACC,SAAUhC,EAAciC,IAAKzC,EAAS0C,cAAe/C,EAAYgD,oBAAoB,WACxG,WAACR,EAAAA,CAAIA,CAACS,IAAI,YACN,UAACC,EAAAA,CAAGA,CAAAA,UACA,UAACC,EAAAA,CAAGA,CAAAA,UACA,UAACX,EAAAA,CAAIA,CAACY,KAAK,WAAE1D,EAAE,mDAGvB,UAAC2D,KAAAA,CAAAA,GACD,WAACH,EAAAA,CAAGA,CAAAA,WACA,UAACC,EAAAA,CAAGA,CAAAA,CAACG,EAAE,IAACC,GAAI,EAAGC,GAAI,YACf,WAACC,EAAAA,CAAIA,CAACC,KAAK,YACP,UAACD,EAAAA,CAAIA,CAACE,KAAK,EAACrB,UAAU,0BACjB5C,EAAE,+CAEP,UAACkE,EAAAA,EAASA,CAAAA,CACNlD,KAAK,QACLmD,GAAG,QACHC,QAAQ,IACRnD,MAAOX,EAAWF,KAAK,CACvBiE,UAAW,GAAmBC,YAAOrD,GAAS,IAAIQ,IAAI,GACtD8C,aAAc,CACVF,UAAWrE,EAAE,wDACjB,EACAwE,SAAU3D,SAItB,UAAC4C,EAAAA,CAAGA,CAAAA,CAACG,EAAE,IAACC,GAAI,EAAGC,GAAI,YACf,WAACC,EAAAA,CAAIA,CAACC,KAAK,YACP,UAACD,EAAAA,CAAIA,CAACE,KAAK,EAACrB,UAAU,0BACjB5C,EAAE,wCAEP,UAACkE,EAAAA,EAASA,CAAAA,CACNlD,KAAK,OACLmD,GAAG,OACHC,QAAQ,IACRnD,MAAOX,EAAWD,IAAI,CACtBgE,UAAW,GAAkD,KAA/BC,OAAOrD,GAAS,IAAIQ,IAAI,GACtD8C,aAAc,CAAEF,UAAWrE,EAAE,iDAAiD,EAC9EwE,SAAU3D,YAK1B,UAAC2C,EAAAA,CAAGA,CAAAA,CAACZ,UAAU,gBACX,WAACa,EAAAA,CAAGA,CAAAA,WACA,UAACgB,EAAAA,CAAMA,CAAAA,CAAC7B,UAAU,OAAO8B,KAAK,SAASC,QAAQ,mBAC1C3E,EAAE,0CAEP,UAACyE,EAAAA,CAAMA,CAAAA,CAAC7B,UAAU,OAAOgC,QArHpC,CAqH6CC,IApH9DtE,EAAcJ,GAEd2E,OAAOC,QAAQ,CAAC,EAAG,EACvB,EAiHgFJ,QAAQ,gBACnD3E,EAAE,yCAEP,UAACgF,IAAIA,CACDC,KAAK,6BACLC,GAAK,OAFJF,+BAID,UAACP,EAAAA,CAAMA,CAAAA,CAACE,QAAQ,qBAAa3E,EAAE,2DAUvE,0GChKA,IAAMmF,EAAwBC,EAAAA,OAAb,GAA6B,CAAC,GAK5ChC,IALyB,IAAoB,WAC9CR,CAAS,UACTyC,CAAQ,CACRH,GAAII,EAAY,KAAK,CACrB,GAAGvF,EACJ,GAEC,OADAsF,EAAWE,CAAAA,EAAAA,EAAAA,EAAAA,CAAkBA,CAACF,EAAU,aACpBG,CAAAA,EAAAA,EAAAA,GAAAA,CAAIA,CAACF,EAAW,CAClClC,IAAKA,EACLR,UAAW6C,IAAW7C,EAAWyC,GACjC,GAAGtF,CAAK,EAEZ,GACAoF,EAASO,WAAW,CAAG,WCbvB,IAAMC,EAA0BP,EAAAA,SAAb,CAA6B,CAAC,GAK9ChC,MAL2B,EAAoB,WAChDR,CAAS,UACTyC,CAAQ,CACRH,GAAII,EAAY,KAAK,CACrB,GAAGvF,EACJ,GAEC,OADAsF,EAAWE,CAAAA,EAAAA,EAAAA,EAAAA,CAAkBA,CAACF,EAAU,eACpBG,CAAAA,EAAAA,EAAAA,GAAAA,CAAIA,CAACF,EAAW,CAClClC,IAAKA,EACLR,UAAW6C,IAAW7C,EAAWyC,GACjC,GAAGtF,CAAK,EAEZ,GACA4F,EAJyBF,WAIH,CAAG,4BCXzB,IAAMG,EAA0BR,EAAAA,SAAb,CAA6B,CAAC,GAM9ChC,MAN2B,EAAoB,UAChDiC,CAAQ,WACRzC,CAAS,CAETsC,CADA,EACII,EAAY,KAAK,CACrB,GAAGvF,EACJ,GACO8F,EAASN,CAAAA,EAAAA,EAAAA,EAAAA,CAAkBA,CAACF,EAAU,eACtCS,EAAeC,CAAAA,EAAAA,EAAAA,OAAAA,CAAOA,CAAC,IAAO,EAClCC,mBAAoBH,EACtB,EAAI,CAACA,EAAO,EACZ,MAAoBL,CAAb,EAAaA,EAAAA,GAAAA,CAAIA,CAACS,EAAP,CAAwBA,CAACC,QAAQ,CAAE,CACnDjF,MAAO6E,EACPK,SAAuBX,CAAb,EAAaA,EAAAA,GAAAA,CAAIA,CAACF,EAAW,CACrClC,IAAKA,EACL,GAAGrD,CAAK,CACR6C,UAAW6C,IAAW7C,EAAWiD,EACnC,EACF,EACF,GACAD,EAAWF,GAJgBD,QAIL,CAAG,aCtBzB,IAAMW,EAAuBhB,EAAAA,MAAb,IAA6B,CAC7C,CACA,EAMGhC,GARwB,KAE1B,UACCiC,CAAQ,WACRzC,CAAS,CACT+B,SAAO,CACPO,GAAII,EAAY,KAAK,CACrB,GAAGvF,EACJ,GACO8F,EAASN,CAAAA,EAAAA,EAAAA,EAAAA,CAAkBA,CAACF,EAAU,YAC5C,MAAoBG,CAAAA,EAAAA,EAAAA,GAAAA,CAAIA,CAACF,EAAW,CAClClC,IAAKA,EACLR,UAAW6C,IAAWd,EAAU,GAAaA,MAAAA,CAAVkB,EAAO,EAArBJ,GAAgC,OAARd,CAX0G,EAW9FkB,EAAQjD,GACjE,GAAG7C,CAAK,EAEZ,GACAqG,EAAQV,WAAW,CAAG,UChBtB,IAAMW,EAA8BjB,EAAAA,UAAgB,CAAC,EAA9B,CAKpBhC,QALmD,EAApB,SAChCR,CAAS,UACTyC,CAAQ,CACRH,GAAII,EAAY,KAAK,CACrB,GAAGvF,EACJ,GAEC,OADAsF,EAAWE,CAAAA,EAAAA,EAAAA,EAAAA,CAAkBA,CAACF,EAAU,oBACpBG,CAAAA,EAAAA,EAAAA,GAAAA,CAAIA,CAACF,EAAW,CAClClC,IAAKA,EACLR,UAAW6C,IAAW7C,EAAWyC,GACjC,GAAGtF,CACL,EACF,GACAsG,EAAeX,WAAW,CAAG,iBCb7B,IAAMY,EAAwBlB,EAAAA,OAAb,GAA6B,CAAC,GAK5ChC,IALyB,IAAoB,WAC9CR,CAAS,UACTyC,CAAQ,CACRH,GAAII,EAAY,GAAG,CACnB,GAAGvF,EACJ,GAEC,OADAsF,EAAWE,CAAAA,EAAAA,EAAAA,EAAAA,CAAkBA,CACX,EADsB,aACpBC,CAAAA,EAAAA,EAAAA,GAAAA,CAAIA,CAACF,EAAW,CAClClC,IAAKA,EACLR,UAAW6C,IAAW7C,EAAWyC,GACjC,GAAGtF,CAAK,EAEZ,GACAuG,EAJyBb,WAIL,CAAG,0BCZvB,IAAMc,EAAgBC,CAAAA,EAAAA,EAAAA,CAAAA,CAAgBA,CAAC,MACjCC,EAA4BrB,EAAAA,UAAgB,CAA7B,CAA8B,EAKhDhC,QAL6B,WAC9BR,CAAS,CACTyC,UAAQ,CACRH,GAAII,EAAYiB,CAAa,CAC7B,GAAGxG,EACJ,GAEC,OAAO,EADIwF,CAAAA,EAAAA,EAAAA,EAAAA,CAAkBA,CAACF,EAAU,iBACpBG,CAAAA,EAAAA,EAAAA,GAAAA,CAAIA,CAACF,EAAW,CAClClC,IAAKA,EACLR,UAAW6C,IAAW7C,EAAWyC,GACjC,GAAGtF,CAAK,EAEZ,GACA0G,EAAaf,WAAW,CAAG,eCf3B,IAAMgB,EAAwBtB,EAAAA,OAAb,GAA6B,CAAC,GAK5ChC,IALyB,IAAoB,WAC9CR,CAAS,UACTyC,CAAQ,CACRH,GAAII,EAAY,GAAG,CACnB,GAAGvF,EACJ,GAEC,OADAsF,EAAWE,CAAAA,EAAAA,EAAAA,EAAAA,CAAkBA,CAACF,EAAU,aACpBG,CAAAA,EAAAA,EAAAA,GAAAA,CAAIA,CAACF,EAAW,CAClClC,IAAKA,EACLR,UAAW6C,IAAW7C,EAAWyC,GACjC,GAAGtF,CAAK,EAEZ,GACA2G,EAJyBjB,WAIL,CAAG,WCZvB,IAAMkB,EAAgBH,CAAAA,EAAAA,EAAAA,CAAAA,CAAgBA,CAAC,MACjCI,EAAyBxB,EAAAA,QAAb,EAA6B,CAAC,GAK7ChC,KAL0B,GAAoB,WAC/CR,CAAS,UACTyC,CAAQ,CACRH,GAAII,EAAYqB,CAAa,CAC7B,GAAG5G,EACJ,GAEC,OAAO,EADIwF,CAAAA,EAAAA,EAAAA,EAAAA,CAAkBA,CAACF,EAAU,cACpBG,CAAAA,EAAAA,EAAAA,GAAAA,CAAIA,CAACF,EAAW,CAClClC,IAAKA,EACLR,UAAW6C,IAAW7C,EAAWyC,GACjC,GAAGtF,CAAK,EAEZ,GACA6G,EAJyBnB,WAIJ,CAAG,YCNxB,IAAM3C,EAAoBsC,EAAAA,GAAb,OAA6B,CAAC,GAAnB,QAAoB,UAC1CC,CAAQ,WACRzC,CAAS,IACTiE,CAAE,CACFC,MAAI,QACJC,CAAM,MACNC,EAAO,EAAK,UACZb,CAAQ,CAERjB,CADA,EACII,EAAY,KAAK,CACrB,GAAGvF,EACJ,GACO8F,EAASN,CAAAA,EAAAA,EAAAA,EAAAA,CAAkBA,CAACF,EAAU,QAC5C,MAAoBG,CAAb,EAAaA,EAAAA,GAAAA,CAAIA,CAACF,EAAP,CAChBlC,IAAKA,EACL,GAAGrD,CAAK,CACR6C,UAAW6C,IAAW7C,EAAWiD,EAAQgB,GAAM,MAAS,GAAnCpB,GAAmC,CAAHoB,GAAMC,GAAQ,QAAa,OAALA,GAAQC,GAAU,UAAiB,OAAPA,IACvGZ,IATyJ,KAS/Ia,EAAoBxB,CAAAA,EAAAA,EAAb,GAAaA,CAAIA,CAACL,EAAU,CAC3CgB,GAD0B,MAAehB,CAE3C,GAAKgB,CACP,EACF,GACArD,EAAK4C,WAAW,CAAG,OACnB,MAAeuB,OAAOC,MAAM,CAACpE,EAAM,CACjCqE,INhBaf,CMgBRA,CACL1C,KNjBoB0C,CKDPQ,CLCQ,CMkBrBQ,EAFYhB,KDjBUQ,EFATH,CGmBHA,CACVlD,CAFgBqD,ITpBHzB,CSsBPA,CACNH,GHrByByB,EDFZH,CLAQnB,CSwBrBkC,CTxBsB,GSsBRlC,CFtBDuB,CFAQJ,CIyBrBgB,CJzBsB,GIuBRhB,EFvBOI,CLSRd,CKTS,CE0BtB2B,EAFcb,KRxBDf,CQ0BLA,CACR6B,CPlBwB,GOgBN5B,IRzBKD,EAAC,CGAXU,CK2BDA,CADMV,CAElB,EAAC,SL5B0BU,EAAC,GK2BFA,0ICwCrB,IAAMoB,EAAQ,CACnBC,WA1C4C,OAAC,MAC7C1G,CAAI,CACJ2G,eAAa,CACbnD,UAAQ,cACRD,CAAY,UACZ4B,CAAQ,CACT,GACO,QAAEyB,CAAM,SAAEC,CAAO,CAAE,CAAGC,CAAAA,EAAAA,EAAAA,EAAAA,CAAgBA,GACtCC,EAAWF,CAAO,CAAC7G,EAAK,EAAI4G,CAAM,CAAC5G,EAAK,CAGzBoE,EAAAA,OAAa,CAAC,IAAO,OAAEpE,EAAK,EAAI,CAACA,EAAK,EAG3D,IAAMgH,EAAoB5C,EAAAA,QAAc,CAAC6C,GAAG,CAAC9B,EAAU,GACrD,EAAIf,cAAoB,CAAC8C,IAxC7B,IAwCqC,KAxCnBnI,CAAU,EAC1B,MAAwB,UAAjB,OAAOA,GAAgC,OAAVA,CACtC,EAwCmBmI,EAAMnI,KAAK,EACfqF,CADkB,CAClBA,YAAkB,CAAC8C,EAA6C,MACrElH,EACA,GAAGkH,EAAMnI,KAAK,GAIbmI,GAGT,MACE,WAACxF,MAAAA,WACC,UAACA,MAAAA,CAAIE,UAAU,uBACZoF,IAEFD,GACC,UAACrF,MAAAA,CAAIE,UAAU,oCACZ2B,GAAiB,CAAwB,UAAxB,OAAOqD,CAAM,CAAC5G,EAAK,CAAgB4G,CAAM,CAAC5G,EAAK,CAAGsD,OAAOsD,CAAM,CAAC5G,EAAK,OAKjG,EAIEmH,UAhE0C,OAAC,IAAEhE,CAAE,OAAEiE,CAAK,OAAEnH,CAAK,MAAED,CAAI,UAAEqH,CAAQ,CAAE,GACzE,QAAEC,CAAM,eAAEC,CAAa,CAAE,CAAGT,CAAAA,EAAAA,EAAAA,EAAAA,CAAgBA,GAC5CU,EAAYxH,GAAQmD,EAE1B,MACE,UAACJ,EAAAA,CAAIA,CAAC0E,KAAK,EACT/D,KAAK,QACLP,GAAIA,EACJiE,MAAOA,EACPnH,MAAOA,EACPD,KAAMwH,EACNE,QAASJ,CAAM,CAACE,EAAU,GAAKvH,EAC/BuD,SAAU,IACR+D,EAAcC,EAAW1H,EAAEC,MAAM,CAACE,KAAK,CACzC,EACAoH,SAAUA,EACVM,MAAM,KAGZ,CA8CA,EAAE,ECzEcC,CAAAA,CACL1E,EAAAA,EAAAA,CACE2E,EAAAA,EAAAA,kBClBb,4CACA,kCACA,WACA,OAAe,EAAQ,KAAuD,CAC9E,EACA,SAFsB,wFC8BtB,IAAM3F,EAAwB4F,CAAAA,EAAAA,EAAAA,UAAAA,CAAUA,CAA8C,CAAC/I,EAAOqD,KAC5F,GAAM,UAAE+C,CAAQ,UAAEhD,CAAQ,cAAE4F,CAAY,WAAEnG,CAAS,YAAEoG,CAAU,eAAE3F,CAAa,CAAE,GAAG4F,EAAM,CAAGlJ,EAGtFmJ,EAAmBC,EAAAA,EAAU,GAAGC,KAAK,CAAC,CAAC,GAE7C,MACE,UAACC,EAAAA,EAAMA,CAAAA,CACLhG,cAAeA,GAAiB,CAAC,EACjC6F,iBAAkBA,EAClB/F,SAAU,CAACmF,EAA6BgB,KAEtC,IAAMC,EAAuB,CAC3BhI,eAAgB,KAAO,EACvBiI,gBAAiB,KAAO,EACxBC,cAAe,KACf1I,OAAQ,KACR2I,YAAa,IAAIC,MAAM,UACvBC,SAAS,EACTC,YAAY,EACZC,kBAAkB,EAClBC,WAAY,EACZC,UAAW,GACXC,UAAWC,KAAKC,GAAG,GACnBzF,KAAM,SACN0F,mBAAoB,KAAM,EAC1BC,qBAAsB,KAAM,EAC5BC,QAAS,KAAO,CAClB,EAEInH,GAEFA,EAASoG,EAAWjB,EAAQgB,CAFhB,CAIhB,EACC,GAAGL,CAAI,UAEP,GACC,UAAClF,EAAAA,EAAIA,CAAAA,CACHX,IAAKA,EACLD,SAAUoH,EAAYpJ,YAAY,CAClC4H,aAAcA,EACdnG,UAAWA,EACXoG,WAAYA,WAES,YAApB,OAAO7C,EAA0BA,EAASoE,GAAepE,KAKpE,EAEAjD,GAAsBwC,WAAW,CAAG,wBAEpC,MAAexC,qBAAqBA,EAAC,sFClF9B,IAAMgB,EAAY,OAAC,MACxBlD,CAAI,IACJmD,CAAE,UACFC,CAAQ,WACRC,CAAS,cACTE,CAAY,UACZC,CAAQ,OACRvD,CAAK,IACLiE,CAAE,WACFsF,CAAS,MACTC,CAAI,SACJC,CAAO,CACP,GAAG3K,EACC,GAuBJ,MACE,UAAC4K,EAAAA,EAAKA,CAAAA,CAAC3J,KAAMA,EAAM4J,SAtBJ,CAsBcA,GApB7B,IAAMC,EAA2B,UAAf,OAAOC,EAAmBA,EAAMxG,OAAOwG,GAAO,WAChE,GAAiB,EAACA,GAA4B,IAA7B,CAAQD,EAAUpJ,IAAI,EAAO,CAAC,CACtC8C,EAD0C,KAC1CA,EAAAA,KAAAA,EAAAA,EAAcF,SAAS,GAAI,EAA3BE,uBAGLF,GAAa,CAACA,EAAUyG,GACnBvG,GADyB,IACzBA,EAAAA,KAAAA,EAAAA,EAAcF,SAAAA,GAAa,EAA3BE,cAGLmG,GAAWI,GAET,CADU,CADI,GACAC,OAAOL,GACdM,IAAI,CAACF,GACPvG,GADa,IACbA,EAAAA,KAAAA,EAAAA,EAAcmG,OAAAA,GAAW,IAAzBnG,mBAKb,WAIK,OAAC,OAAE0G,CAAK,MAAEC,CAAI,CAAO,SACpB,iCACE,UAACnH,EAAAA,CAAIA,CAACoH,OAAO,EACV,GAAGF,CAAK,CACR,GAAGlL,CAAK,CACToE,GAAIA,EACJe,GAAIA,GAAM,QACVuF,KAAMA,EACNW,UAAWF,EAAKrD,OAAO,EAAI,CAAC,CAACqD,EAAKhJ,KAAK,CACvCsC,SAAU,IACRyG,EAAMzG,QAAQ,CAAC1D,GACX0D,GAAUA,EAAS1D,EACzB,EACAG,WAAiBoK,IAAVpK,EAAsBA,EAAQgK,EAAMhK,KAAK,GAEjDiK,EAAKrD,OAAO,EAAIqD,EAAKhJ,KAAK,CACzB,UAAC6B,EAAAA,CAAIA,CAACoH,OAAO,CAACG,QAAQ,EAAC5G,KAAK,mBACzBwG,EAAKhJ,KAAK,GAEX,UAKd,EAAE,EAIyB,OAAC,MAC1BlB,CAAI,IACJmD,CAAE,UACFC,CAAQ,cACRG,CAAY,UACZC,CAAQ,OACRvD,CAAK,UACLkF,CAAQ,CACR,GAAGpG,EACC,GAUJ,MACE,UAAC4K,EAAAA,EAAKA,CAAAA,CAAC3J,KAAMA,EAAM4J,SATJ,CAScA,GAR7B,GAAIxG,GAAa,EAAC0G,GAAe,IAAhB,CAAQA,CAAQ,CAAC,CAChC,EADoC,IAC7BvG,OAAAA,EAAAA,KAAAA,EAAAA,EAAcF,SAAAA,GAAa,EAA3BE,sBAIX,WAIK,OAAC,OAAE0G,CAAK,MAAEC,CAAI,CAAO,SACpB,iCACE,UAACnH,EAAAA,CAAIA,CAACoH,OAAO,EACXjG,GAAG,SACF,GAAG+F,CAAK,CACR,GAAGlL,CAAK,CACToE,GAAIA,EACJiH,UAAWF,EAAKrD,OAAO,EAAI,CAAC,CAACqD,EAAKhJ,KAAK,CACvCsC,SAAW1D,IACTmK,EAAMzG,QAAQ,CAAC1D,GACX0D,GAAUA,EAAS1D,EACzB,EACAG,WAAiBoK,IAAVpK,EAAsBA,EAAQgK,EAAMhK,KAAK,UAE/CkF,IAEF+E,EAAKrD,OAAO,EAAIqD,EAAKhJ,KAAK,CACzB,UAAC6B,EAAAA,CAAIA,CAACoH,OAAO,CAACG,QAAQ,EAAC5G,KAAK,mBACzBwG,EAAKhJ,KAAK,GAEX,UAKd,EAAE,+CCnHF,IAAMqJ,EAAuBnG,QAAb,CAAaA,UAAF,GAAqB,CAAC,MACjDmG,EAAQ7F,WAAW,CAAG,oBACtB,MAAe6F,OAAOA,EAAC", "sources": ["webpack://_N_E/./pages/adminsettings/worldregion/form.tsx", "webpack://_N_E/./node_modules/react-bootstrap/esm/CardBody.js", "webpack://_N_E/./node_modules/react-bootstrap/esm/CardFooter.js", "webpack://_N_E/./node_modules/react-bootstrap/esm/CardHeader.js", "webpack://_N_E/./node_modules/react-bootstrap/esm/CardImg.js", "webpack://_N_E/./node_modules/react-bootstrap/esm/CardImgOverlay.js", "webpack://_N_E/./node_modules/react-bootstrap/esm/CardLink.js", "webpack://_N_E/./node_modules/react-bootstrap/esm/CardSubtitle.js", "webpack://_N_E/./node_modules/react-bootstrap/esm/CardText.js", "webpack://_N_E/./node_modules/react-bootstrap/esm/CardTitle.js", "webpack://_N_E/./node_modules/react-bootstrap/esm/Card.js", "webpack://_N_E/./components/common/FormikRadio.tsx", "webpack://_N_E/./components/common/FormValidation.tsx", "webpack://_N_E/?2f39", "webpack://_N_E/./components/common/ValidationFormWrapper.tsx", "webpack://_N_E/./components/common/FormikTextInput.tsx", "webpack://_N_E/./node_modules/react-bootstrap/esm/CardHeaderContext.js"], "sourcesContent": ["//Import Library\r\nimport { useState, useRef, useEffect } from \"react\";\r\nimport { Container, Card, Row, Col, Form, Button } from \"react-bootstrap\";\r\nimport { ValidationForm, TextInput } from \"../../../components/common/FormValidation\";\r\nimport Router from \"next/router\";\r\nimport toast from 'react-hot-toast';\r\nimport Link from \"next/link\";\r\n\r\n//Import services/components\r\nimport { WorldRegion } from \"../../../types\";\r\nimport apiService from \"../../../services/apiService\";\r\nimport { useTranslation } from 'next-i18next';\r\nimport ValidationFormWrapper from \"../../../components/common/ValidationFormWrapper\";\r\n\r\ninterface WorldregionFormProps {\r\n    [key: string]: any;\r\n}\r\n\r\nconst WorldregionForm = (props: WorldregionFormProps) => {\r\n    const { t } = useTranslation('common');\r\n\r\n    const _initialworldregion = {\r\n        title: \"\",\r\n        code: \"\",\r\n    };\r\n\r\n    const [initialVal, setInitialVal] = useState<WorldRegion>(_initialworldregion);\r\n\r\n    const editform = props.routes && props.routes[0] === \"edit_worldregion\" && props.routes[1];\r\n\r\n    const formRef = useRef(null);\r\n\r\n    const resetHandler = () => {\r\n        setInitialVal(_initialworldregion);\r\n        // Reset validation state (Formik handles this automatically)\r\n        window.scrollTo(0, 0);\r\n    };\r\n\r\n    const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement>) => {\r\n        if (e.target) {\r\n            const { name, value } = e.target;\r\n            setInitialVal((prevState) => ({\r\n                ...prevState,\r\n                [name]: value,\r\n            }));\r\n        }\r\n    };\r\n\r\n    const handleSubmit = async (event: any) => {\r\n        event.preventDefault();\r\n        const obj = {\r\n            title: initialVal.title.trim(),\r\n            code: initialVal.code,\r\n        };\r\n\r\n        let response;\r\n        let toastMsg;\r\n        if (editform) {\r\n            toastMsg = \"adminsetting.worldregion.form.Worldregionisupdatedsuccessfully\";\r\n            response = await apiService.patch(`/worldregion/${props.routes[1]}`, obj);\r\n        } else {\r\n            toastMsg = \"adminsetting.worldregion.form.Worldregionisaddedsuccessfully\";\r\n            response = await apiService.post(\"/worldregion\", obj);\r\n        }\r\n        if (response && response._id) {\r\n            toast.success(t(toastMsg));\r\n            Router.push(\"/adminsettings/worldregion\");\r\n        } else {\r\n            if (response?.errorCode === 11000) {\r\n                toast.error(t(\"duplicatesNotAllowed\"));\r\n            } else {\r\n                toast.error(response);\r\n            }\r\n        }\r\n    };\r\n\r\n    useEffect(() => {\r\n        const worldregionParams = {\r\n            query: {},\r\n            sort: { title: \"asc\" },\r\n            limit: \"~\",\r\n        };\r\n        if (editform) {\r\n            const getWorldregionData = async () => {\r\n                const response: WorldRegion = await apiService.get(`/worldregion/${props.routes[1]}`, worldregionParams);\r\n                setInitialVal((prevState) => ({ ...prevState, ...response }));\r\n            };\r\n            getWorldregionData();\r\n        }\r\n    }, []);\r\n\r\n    return (\r\n        <div>\r\n            <Container className=\"formCard\" fluid>\r\n                <Card\r\n                    style={{\r\n                        marginTop: \"5px\",\r\n                        boxShadow: \"0 4px 8px 0 rgba(0, 0, 0, 0.2), 0 6px 20px 0 rgba(0, 0, 0, 0.19)\",\r\n                    }}\r\n                >\r\n                    <ValidationFormWrapper onSubmit={handleSubmit} ref={formRef} initialValues={initialVal} enableReinitialize={true}>\r\n                        <Card.Body>\r\n                            <Row>\r\n                                <Col>\r\n                                    <Card.Title>{t(\"adminsetting.worldregion.form.WorldRegion\")}</Card.Title>\r\n                                </Col>\r\n                            </Row>\r\n                            <hr />\r\n                            <Row>\r\n                                <Col md lg={6} sm={12}>\r\n                                    <Form.Group>\r\n                                        <Form.Label className=\"required-field\">\r\n                                            {t(\"adminsetting.worldregion.form.WorldRegion\")}\r\n                                        </Form.Label>\r\n                                        <TextInput\r\n                                            name=\"title\"\r\n                                            id=\"title\"\r\n                                            required\r\n                                            value={initialVal.title}\r\n                                            validator={(value: string) => String(value || '').trim() !== \"\"}\r\n                                            errorMessage={{\r\n                                                validator: t(\"adminsetting.worldregion.form.PleaseAddtheWorldRegion\"),\r\n                                            }}\r\n                                            onChange={handleChange}\r\n                                        />\r\n                                    </Form.Group>\r\n                                </Col>\r\n                                <Col md lg={6} sm={12}>\r\n                                    <Form.Group>\r\n                                        <Form.Label className=\"required-field\">\r\n                                            {t(\"adminsetting.worldregion.form.Code\")}\r\n                                        </Form.Label>\r\n                                        <TextInput\r\n                                            name=\"code\"\r\n                                            id=\"code\"\r\n                                            required\r\n                                            value={initialVal.code}\r\n                                            validator={(value: string) => String(value || '').trim() !== \"\"}\r\n                                            errorMessage={{ validator: t(\"adminsetting.worldregion.form.PleaseAddthecode\")}}\r\n                                            onChange={handleChange}\r\n                                        />\r\n                                    </Form.Group>\r\n                                </Col>\r\n                            </Row>\r\n                            <Row className=\"my-4\">\r\n                                <Col>\r\n                                    <Button className=\"me-2\" type=\"submit\" variant=\"primary\">\r\n                                        {t(\"adminsetting.worldregion.form.Submit\")}\r\n                                    </Button>\r\n                                    <Button className=\"me-2\" onClick={resetHandler} variant=\"info\">\r\n                                        {t(\"adminsetting.worldregion.form.Reset\")}\r\n                                    </Button>\r\n                                    <Link\r\n                                        href=\"/adminsettings/[...routes]\"\r\n                                        as={`/adminsettings/worldregion`}\r\n                                        >\r\n                                        <Button variant=\"secondary\">{t(\"adminsetting.worldregion.form.Cancel\")}</Button>\r\n                                    </Link>\r\n                                </Col>\r\n                            </Row>\r\n                        </Card.Body>\r\n                    </ValidationFormWrapper>\r\n                </Card>\r\n            </Container>\r\n        </div>\r\n    );\r\n};\r\nexport default WorldregionForm;\r\n", "\"use client\";\n\nimport * as React from 'react';\nimport classNames from 'classnames';\nimport { useBootstrapPrefix } from './ThemeProvider';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst CardBody = /*#__PURE__*/React.forwardRef(({\n  className,\n  bsPrefix,\n  as: Component = 'div',\n  ...props\n}, ref) => {\n  bsPrefix = useBootstrapPrefix(bsPrefix, 'card-body');\n  return /*#__PURE__*/_jsx(Component, {\n    ref: ref,\n    className: classNames(className, bsPrefix),\n    ...props\n  });\n});\nCardBody.displayName = 'CardBody';\nexport default CardBody;", "\"use client\";\n\nimport * as React from 'react';\nimport classNames from 'classnames';\nimport { useBootstrapPrefix } from './ThemeProvider';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst CardFooter = /*#__PURE__*/React.forwardRef(({\n  className,\n  bsPrefix,\n  as: Component = 'div',\n  ...props\n}, ref) => {\n  bsPrefix = useBootstrapPrefix(bsPrefix, 'card-footer');\n  return /*#__PURE__*/_jsx(Component, {\n    ref: ref,\n    className: classNames(className, bsPrefix),\n    ...props\n  });\n});\nCardFooter.displayName = 'CardFooter';\nexport default CardFooter;", "\"use client\";\n\nimport classNames from 'classnames';\nimport * as React from 'react';\nimport { useMemo } from 'react';\nimport { useBootstrapPrefix } from './ThemeProvider';\nimport CardHeaderContext from './CardHeaderContext';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst CardHeader = /*#__PURE__*/React.forwardRef(({\n  bsPrefix,\n  className,\n  // Need to define the default \"as\" during prop destructuring to be compatible with styled-components github.com/react-bootstrap/react-bootstrap/issues/3595\n  as: Component = 'div',\n  ...props\n}, ref) => {\n  const prefix = useBootstrapPrefix(bsPrefix, 'card-header');\n  const contextValue = useMemo(() => ({\n    cardHeaderBsPrefix: prefix\n  }), [prefix]);\n  return /*#__PURE__*/_jsx(CardHeaderContext.Provider, {\n    value: contextValue,\n    children: /*#__PURE__*/_jsx(Component, {\n      ref: ref,\n      ...props,\n      className: classNames(className, prefix)\n    })\n  });\n});\nCardHeader.displayName = 'CardHeader';\nexport default CardHeader;", "\"use client\";\n\nimport classNames from 'classnames';\nimport * as React from 'react';\nimport { useBootstrapPrefix } from './ThemeProvider';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst CardImg = /*#__PURE__*/React.forwardRef(\n// Need to define the default \"as\" during prop destructuring to be compatible with styled-components github.com/react-bootstrap/react-bootstrap/issues/3595\n({\n  bsPrefix,\n  className,\n  variant,\n  as: Component = 'img',\n  ...props\n}, ref) => {\n  const prefix = useBootstrapPrefix(bsPrefix, 'card-img');\n  return /*#__PURE__*/_jsx(Component, {\n    ref: ref,\n    className: classNames(variant ? `${prefix}-${variant}` : prefix, className),\n    ...props\n  });\n});\nCardImg.displayName = 'CardImg';\nexport default CardImg;", "\"use client\";\n\nimport * as React from 'react';\nimport classNames from 'classnames';\nimport { useBootstrapPrefix } from './ThemeProvider';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst CardImgOverlay = /*#__PURE__*/React.forwardRef(({\n  className,\n  bsPrefix,\n  as: Component = 'div',\n  ...props\n}, ref) => {\n  bsPrefix = useBootstrapPrefix(bsPrefix, 'card-img-overlay');\n  return /*#__PURE__*/_jsx(Component, {\n    ref: ref,\n    className: classNames(className, bsPrefix),\n    ...props\n  });\n});\nCardImgOverlay.displayName = 'CardImgOverlay';\nexport default CardImgOverlay;", "\"use client\";\n\nimport * as React from 'react';\nimport classNames from 'classnames';\nimport { useBootstrapPrefix } from './ThemeProvider';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst CardLink = /*#__PURE__*/React.forwardRef(({\n  className,\n  bsPrefix,\n  as: Component = 'a',\n  ...props\n}, ref) => {\n  bsPrefix = useBootstrapPrefix(bsPrefix, 'card-link');\n  return /*#__PURE__*/_jsx(Component, {\n    ref: ref,\n    className: classNames(className, bsPrefix),\n    ...props\n  });\n});\nCardLink.displayName = 'CardLink';\nexport default CardLink;", "\"use client\";\n\nimport * as React from 'react';\nimport classNames from 'classnames';\nimport { useBootstrapPrefix } from './ThemeProvider';\nimport divWithClassName from './divWithClassName';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst DivStyledAsH6 = divWithClassName('h6');\nconst CardSubtitle = /*#__PURE__*/React.forwardRef(({\n  className,\n  bsPrefix,\n  as: Component = DivStyledAsH6,\n  ...props\n}, ref) => {\n  bsPrefix = useBootstrapPrefix(bsPrefix, 'card-subtitle');\n  return /*#__PURE__*/_jsx(Component, {\n    ref: ref,\n    className: classNames(className, bsPrefix),\n    ...props\n  });\n});\nCardSubtitle.displayName = 'CardSubtitle';\nexport default CardSubtitle;", "\"use client\";\n\nimport * as React from 'react';\nimport classNames from 'classnames';\nimport { useBootstrapPrefix } from './ThemeProvider';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst CardText = /*#__PURE__*/React.forwardRef(({\n  className,\n  bsPrefix,\n  as: Component = 'p',\n  ...props\n}, ref) => {\n  bsPrefix = useBootstrapPrefix(bsPrefix, 'card-text');\n  return /*#__PURE__*/_jsx(Component, {\n    ref: ref,\n    className: classNames(className, bsPrefix),\n    ...props\n  });\n});\nCardText.displayName = 'CardText';\nexport default CardText;", "\"use client\";\n\nimport * as React from 'react';\nimport classNames from 'classnames';\nimport { useBootstrapPrefix } from './ThemeProvider';\nimport divWithClassName from './divWithClassName';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst DivStyledAsH5 = divWithClassName('h5');\nconst CardTitle = /*#__PURE__*/React.forwardRef(({\n  className,\n  bsPrefix,\n  as: Component = DivStyledAsH5,\n  ...props\n}, ref) => {\n  bsPrefix = useBootstrapPrefix(bsPrefix, 'card-title');\n  return /*#__PURE__*/_jsx(Component, {\n    ref: ref,\n    className: classNames(className, bsPrefix),\n    ...props\n  });\n});\nCardTitle.displayName = 'CardTitle';\nexport default CardTitle;", "\"use client\";\n\nimport classNames from 'classnames';\nimport * as React from 'react';\nimport { useBootstrapPrefix } from './ThemeProvider';\nimport CardBody from './CardBody';\nimport CardFooter from './CardFooter';\nimport CardHeader from './CardHeader';\nimport CardImg from './CardImg';\nimport CardImgOverlay from './CardImgOverlay';\nimport CardLink from './CardLink';\nimport CardSubtitle from './CardSubtitle';\nimport CardText from './CardText';\nimport CardTitle from './CardTitle';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst Card = /*#__PURE__*/React.forwardRef(({\n  bsPrefix,\n  className,\n  bg,\n  text,\n  border,\n  body = false,\n  children,\n  // Need to define the default \"as\" during prop destructuring to be compatible with styled-components github.com/react-bootstrap/react-bootstrap/issues/3595\n  as: Component = 'div',\n  ...props\n}, ref) => {\n  const prefix = useBootstrapPrefix(bsPrefix, 'card');\n  return /*#__PURE__*/_jsx(Component, {\n    ref: ref,\n    ...props,\n    className: classNames(className, prefix, bg && `bg-${bg}`, text && `text-${text}`, border && `border-${border}`),\n    children: body ? /*#__PURE__*/_jsx(CardBody, {\n      children: children\n    }) : children\n  });\n});\nCard.displayName = 'Card';\nexport default Object.assign(Card, {\n  Img: CardImg,\n  Title: CardTitle,\n  Subtitle: CardSubtitle,\n  Body: CardBody,\n  Link: CardLink,\n  Text: CardText,\n  Header: CardHeader,\n  Footer: CardFooter,\n  ImgOverlay: CardImgOverlay\n});", "import React from 'react';\r\nimport { useFormikContext, Field } from 'formik';\r\nimport { Form } from 'react-bootstrap';\r\n\r\ninterface RadioItemProps {\r\n  id: string;\r\n  label: string;\r\n  value: string;\r\n  name?: string;\r\n  disabled?: boolean;\r\n}\r\n\r\ninterface RadioGroupProps {\r\n  name: string;\r\n  valueSelected: string;\r\n  onChange: (e: React.ChangeEvent<HTMLInputElement>) => void;\r\n  errorMessage?: string;\r\n  children: React.ReactNode;\r\n}\r\n\r\n// Type Guard to ensure child.props is an object\r\nfunction isObject(props: any): props is { [key: string]: any } {\r\n  return typeof props === 'object' && props !== null;\r\n}\r\n\r\nconst RadioItem: React.FC<RadioItemProps> = ({ id, label, value, name, disabled }) => {\r\n  const { values, setFieldValue } = useFormikContext<any>();\r\n  const fieldName = name || id;\r\n\r\n  return (\r\n    <Form.Check\r\n      type=\"radio\"\r\n      id={id}\r\n      label={label}\r\n      value={value}\r\n      name={fieldName}\r\n      checked={values[fieldName] === value}\r\n      onChange={(e) => {\r\n        setFieldValue(fieldName, e.target.value);\r\n      }}\r\n      disabled={disabled}\r\n      inline\r\n    />\r\n  );\r\n};\r\n\r\nconst RadioGroup: React.FC<RadioGroupProps> = ({\r\n  name,\r\n  valueSelected,\r\n  onChange,\r\n  errorMessage,\r\n  children,\r\n}) => {\r\n  const { errors, touched } = useFormikContext<any>();\r\n  const hasError = touched[name] && errors[name];\r\n\r\n  // Create a context to pass the name to RadioItems\r\n  const radioContext = React.useMemo(() => ({ name }), [name]);\r\n\r\n  // Clone children to pass the name, ensuring props are spread safely\r\n  const childrenWithProps = React.Children.map(children, (child) => {\r\n    if (React.isValidElement(child)) {\r\n      // Ensure child.props is an object before spreading\r\n      if (isObject(child.props)) {\r\n        return React.cloneElement(child as React.ReactElement<RadioItemProps>, {\r\n          name,\r\n          ...child.props, // Safely spread child.props\r\n        });\r\n      }\r\n    }\r\n    return child;\r\n  });\r\n\r\n  return (\r\n    <div>\r\n      <div className=\"radio-group\">\r\n        {childrenWithProps}\r\n      </div>\r\n      {hasError && (\r\n        <div className=\"invalid-feedback d-block\">\r\n          {errorMessage || (typeof errors[name] === 'string' ? errors[name] : String(errors[name]))}\r\n        </div>\r\n      )}\r\n    </div>\r\n  );\r\n};\r\n\r\nexport const Radio = {\r\n  RadioGroup,\r\n  RadioItem,\r\n};\r\n\r\nexport default Radio;\r\n", "// This file exports all the components needed to replace react-bootstrap4-form-validation\r\n// It serves as a drop-in replacement for the original library\r\n\r\nimport ValidationForm from './ValidationFormWrapper';\r\nimport { TextInput, SelectGroup } from './FormikTextInput';\r\nimport { Radio } from './FormikRadio';\r\n\r\n// Export all components\r\nexport {\r\n  ValidationForm,\r\n  TextInput,\r\n  SelectGroup,\r\n  Radio\r\n};\r\n\r\n// Also export as default for convenience\r\nexport default {\r\n  ValidationForm,\r\n  TextInput,\r\n  SelectGroup,\r\n  Radio\r\n};\r\n", "\n    (window.__NEXT_P = window.__NEXT_P || []).push([\n      \"/adminsettings/worldregion/form\",\n      function () {\n        return require(\"private-next-pages/adminsettings/worldregion/form.tsx\");\n      }\n    ]);\n    if(module.hot) {\n      module.hot.dispose(function () {\n        window.__NEXT_P.push([\"/adminsettings/worldregion/form\"])\n      });\n    }\n  ", "import React, { forwardRef } from 'react';\r\nimport { Formik, Form, FormikProps, FormikHelpers } from 'formik';\r\nimport * as Yup from 'yup';\r\n\r\n// This is a wrapper component that replaces react-bootstrap4-form-validation with Formik\r\ninterface MockEvent {\r\n  preventDefault: () => void;\r\n  stopPropagation: () => void;\r\n  currentTarget: (EventTarget & Element) | null;\r\n  target: EventTarget | null;\r\n  nativeEvent: Event;\r\n  bubbles: boolean;\r\n  cancelable: boolean;\r\n  defaultPrevented: boolean;\r\n  eventPhase: number;\r\n  isTrusted: boolean;\r\n  timeStamp: number;\r\n  type: string;\r\n  isDefaultPrevented: () => boolean;\r\n  isPropagationStopped: () => boolean;\r\n  persist: () => void;\r\n}\r\n\r\ninterface ValidationFormWrapperProps {\r\n  children: React.ReactNode | ((formikProps: FormikProps<any>) => React.ReactNode);\r\n  onSubmit: (event: React.FormEvent | MockEvent, values?: Record<string, any>, actions?: FormikHelpers<Record<string, any>>) => void;\r\n  onErrorSubmit?: (errors: any) => void;\r\n  initialValues?: Record<string, any>;\r\n  enableReinitialize?: boolean;\r\n  autoComplete?: string;\r\n  className?: string;\r\n  onKeyPress?: (e: any) => void;\r\n}\r\n\r\nconst ValidationFormWrapper = forwardRef<HTMLFormElement, ValidationFormWrapperProps>((props, ref) => {\r\n  const { children, onSubmit, autoComplete, className, onKeyPress, initialValues, ...rest } = props;\r\n\r\n  // Create an empty validation schema by default\r\n  const validationSchema = Yup.object().shape({});\r\n\r\n  return (\r\n    <Formik\r\n      initialValues={initialValues || {}}\r\n      validationSchema={validationSchema}\r\n      onSubmit={(values: Record<string, any>, actions: FormikHelpers<Record<string, any>>) => {\r\n        // Create a mock event object with preventDefault method and currentTarget\r\n        const mockEvent: MockEvent = {\r\n          preventDefault: () => {},\r\n          stopPropagation: () => {},\r\n          currentTarget: null, // Set to null to avoid checkValidity errors\r\n          target: null,\r\n          nativeEvent: new Event('submit'),\r\n          bubbles: false,\r\n          cancelable: true,\r\n          defaultPrevented: false,\r\n          eventPhase: 0,\r\n          isTrusted: false,\r\n          timeStamp: Date.now(),\r\n          type: 'submit',\r\n          isDefaultPrevented: () => false,\r\n          isPropagationStopped: () => false,\r\n          persist: () => {}\r\n        };\r\n\r\n        if (onSubmit) {\r\n          // Pass the mock event object to maintain compatibility with the original code\r\n          onSubmit(mockEvent, values, actions);\r\n        }\r\n      }}\r\n      {...rest}\r\n    >\r\n      {(formikProps: FormikProps<any>) => (\r\n        <Form\r\n          ref={ref}\r\n          onSubmit={formikProps.handleSubmit}\r\n          autoComplete={autoComplete}\r\n          className={className}\r\n          onKeyPress={onKeyPress}\r\n        >\r\n          {typeof children === 'function' ? children(formikProps) : children}\r\n        </Form>\r\n      )}\r\n    </Formik>\r\n  );\r\n});\r\n\r\nValidationFormWrapper.displayName = 'ValidationFormWrapper';\r\n\r\nexport default ValidationFormWrapper;\r\n", "import React from 'react';\r\nimport { Form } from 'react-bootstrap';\r\nimport { Field, ErrorMessage, useField } from 'formik';\r\n\r\n// This component mimics the TextInput component from react-bootstrap4-form-validation\r\n// but uses Formik under the hood\r\nexport const TextInput = ({\r\n  name,\r\n  id,\r\n  required,\r\n  validator,\r\n  errorMessage,\r\n  onChange,\r\n  value,\r\n  as,\r\n  multiline,\r\n  rows,\r\n  pattern,\r\n  ...props\r\n}: any) => {\r\n  // Create a custom validation function that mimics the original validator\r\n  const validate = (val: any) => {\r\n    // Safely convert value to string and trim\r\n    const stringVal = typeof val === 'string' ? val : String(val || '');\r\n    if (required && (!val || stringVal.trim() === '')) {\r\n      return errorMessage?.validator || 'This field is required';\r\n    }\r\n\r\n    if (validator && !validator(val)) {\r\n      return errorMessage?.validator || 'Invalid value';\r\n    }\r\n\r\n    if (pattern && val) {\r\n      const regex = new RegExp(pattern);\r\n      if (!regex.test(val)) {\r\n        return errorMessage?.pattern || 'Invalid format';\r\n      }\r\n    }\r\n\r\n    return undefined;\r\n  };\r\n\r\n  return (\r\n    <Field name={name} validate={validate}>\r\n      {({ field, meta }: any) => (\r\n        <>\r\n          <Form.Control\r\n            {...field}\r\n            {...props}\r\n            id={id}\r\n            as={as || 'input'}\r\n            rows={rows}\r\n            isInvalid={meta.touched && !!meta.error}\r\n            onChange={(e: React.ChangeEvent<HTMLInputElement>) => {\r\n              field.onChange(e);\r\n              if (onChange) onChange(e);\r\n            }}\r\n            value={value !== undefined ? value : field.value}\r\n          />\r\n          {meta.touched && meta.error ? (\r\n            <Form.Control.Feedback type=\"invalid\">\r\n              {meta.error}\r\n            </Form.Control.Feedback>\r\n          ) : null}\r\n        </>\r\n      )}\r\n    </Field>\r\n  );\r\n};\r\n\r\n// This component mimics the SelectGroup component from react-bootstrap4-form-validation\r\n// but uses Formik under the hood\r\nexport const SelectGroup = ({\r\n  name,\r\n  id,\r\n  required,\r\n  errorMessage,\r\n  onChange,\r\n  value,\r\n  children,\r\n  ...props\r\n}: any) => {\r\n  // Create a custom validation function that mimics the original validator\r\n  const validate = (val: any) => {\r\n    if (required && (!val || val === '')) {\r\n      return errorMessage?.validator || 'This field is required';\r\n    }\r\n\r\n    return undefined;\r\n  };\r\n\r\n  return (\r\n    <Field name={name} validate={validate}>\r\n      {({ field, meta }: any) => (\r\n        <>\r\n          <Form.Control\r\n            as=\"select\"\r\n            {...field}\r\n            {...props}\r\n            id={id}\r\n            isInvalid={meta.touched && !!meta.error}\r\n            onChange={(e: React.ChangeEvent<HTMLSelectElement>) => {\r\n              field.onChange(e);\r\n              if (onChange) onChange(e);\r\n            }}\r\n            value={value !== undefined ? value : field.value}\r\n          >\r\n            {children}\r\n          </Form.Control>\r\n          {meta.touched && meta.error ? (\r\n            <Form.Control.Feedback type=\"invalid\">\r\n              {meta.error}\r\n            </Form.Control.Feedback>\r\n          ) : null}\r\n        </>\r\n      )}\r\n    </Field>\r\n  );\r\n};\r\n\r\n// Export both components as named exports and as a default object\r\nexport default {\r\n  TextInput,\r\n  SelectGroup\r\n};\r\n", "\"use client\";\n\nimport * as React from 'react';\nconst context = /*#__PURE__*/React.createContext(null);\ncontext.displayName = 'CardHeaderContext';\nexport default context;"], "names": ["props", "t", "useTranslation", "WorldregionForm", "_initialworldregion", "title", "code", "initialVal", "setInitialVal", "useState", "editform", "routes", "formRef", "useRef", "handleChange", "e", "target", "name", "value", "prevState", "handleSubmit", "event", "response", "toastMsg", "preventDefault", "obj", "trim", "apiService", "patch", "post", "_id", "toast", "success", "Router", "errorCode", "error", "useEffect", "worldregionParams", "query", "sort", "limit", "getWorldregionData", "get", "div", "Container", "className", "fluid", "Card", "style", "marginTop", "boxShadow", "ValidationFormWrapper", "onSubmit", "ref", "initialValues", "enableReinitialize", "Body", "Row", "Col", "Title", "hr", "md", "lg", "sm", "Form", "Group", "Label", "TextInput", "id", "required", "validator", "String", "errorMessage", "onChange", "<PERSON><PERSON>", "type", "variant", "onClick", "re<PERSON><PERSON><PERSON><PERSON>", "window", "scrollTo", "Link", "href", "as", "CardBody", "React", "bsPrefix", "Component", "useBootstrapPrefix", "_jsx", "classNames", "displayName", "<PERSON><PERSON><PERSON>er", "<PERSON><PERSON><PERSON><PERSON>", "prefix", "contextValue", "useMemo", "cardHeaderBsPrefix", "CardHeaderContext", "Provider", "children", "CardImg", "CardImgOverlay", "CardLink", "DivStyledAsH6", "divWithClassName", "CardSubtitle", "CardText", "DivStyledAsH5", "CardTitle", "bg", "text", "border", "body", "Object", "assign", "Img", "Subtitle", "Text", "Header", "Footer", "ImgOverlay", "Radio", "RadioGroup", "valueSelected", "errors", "touched", "useFormikContext", "<PERSON><PERSON><PERSON><PERSON>", "childrenWithProps", "map", "child", "RadioItem", "label", "disabled", "values", "setFieldValue", "fieldName", "Check", "checked", "inline", "ValidationForm", "SelectGroup", "forwardRef", "autoComplete", "onKeyPress", "rest", "validationSchema", "<PERSON><PERSON>", "shape", "<PERSON><PERSON>", "actions", "mockEvent", "stopPropagation", "currentTarget", "nativeEvent", "Event", "bubbles", "cancelable", "defaultPrevented", "eventPhase", "isTrusted", "timeStamp", "Date", "now", "isDefaultPrevented", "isPropagationStopped", "persist", "formikProps", "multiline", "rows", "pattern", "Field", "validate", "stringVal", "val", "RegExp", "test", "field", "meta", "Control", "isInvalid", "undefined", "<PERSON><PERSON><PERSON>", "context"], "sourceRoot": "", "ignoreList": [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 16]}