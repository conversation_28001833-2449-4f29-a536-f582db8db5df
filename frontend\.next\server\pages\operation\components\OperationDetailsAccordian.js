"use strict";(()=>{var e={};e.id=4006,e.ids=[636,3220,4006],e.modules={123:e=>{e.exports=require("dom-helpers/removeEventListener")},1332:e=>{e.exports=require("react-custom-scrollbars-2")},1428:e=>{e.exports=import("axios")},1680:e=>{e.exports=require("@restart/ui/utils")},1919:e=>{e.exports=require("react-transition-group/Transition")},3892:e=>{e.exports=require("classnames")},4048:e=>{e.exports=require("react-truncate")},6009:e=>{e.exports=require("dom-helpers/ownerDocument")},6952:e=>{e.exports=require("@restart/ui/Modal")},7374:e=>{e.exports=require("@restart/ui/Dropdown")},8732:e=>{e.exports=require("react/jsx-runtime")},9653:e=>{e.exports=require("@restart/ui/Overlay")},11242:e=>{e.exports=require("redux-persist/integration/react")},11688:e=>{e.exports=require("@restart/hooks/useWillUnmount")},12388:e=>{e.exports=require("dom-helpers/addEventListener")},13364:e=>{e.exports=require("redux-saga/effects")},14062:e=>{e.exports=import("react-redux")},14078:e=>{e.exports=import("swr")},14332:e=>{e.exports=require("uncontrollable")},16116:e=>{e.exports=require("invariant")},18622:e=>{e.exports=require("yup")},19121:e=>{e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},22326:e=>{e.exports=require("react-dom")},22541:e=>{e.exports=require("redux-persist/lib/storage")},24292:(e,r,t)=>{t.a(e,async(e,s)=>{try{t.r(r),t.d(r,{default:()=>m});var o=t(8732),a=t(82015),i=t(54131),n=t(82053),p=t(74716),u=t.n(p),l=t(93024),d=t(83551),x=t(49481),c=t(88751),h=e([i]);i=(h.then?(await h)():h)[0];let m=e=>{let{t:r}=(0,c.useTranslation)("common"),t="MM-D-YYYY HH:mm:ss",s="MM-D-YYYY",[p,h]=(0,a.useState)(!1);return(0,o.jsx)(o.Fragment,{children:(0,o.jsxs)(l.A.Item,{eventKey:"0",children:[(0,o.jsxs)(l.A.Header,{onClick:()=>h(!p),children:[(0,o.jsx)("div",{className:"cardTitle",children:r("OperationDetails")}),(0,o.jsx)("div",{className:"cardArrow",children:p?(0,o.jsx)(n.FontAwesomeIcon,{icon:i.faMinus,color:"#fff"}):(0,o.jsx)(n.FontAwesomeIcon,{icon:i.faPlus,color:"#fff"})})]}),(0,o.jsx)(l.A.Body,{children:(0,o.jsxs)(d.A,{className:"operationData",children:[(0,o.jsxs)(x.A,{md:!0,lg:6,sm:12,className:"ps-0",children:[(0,o.jsxs)("p",{children:[(0,o.jsx)("b",{children:r("HazardType")}),":",(0,o.jsx)("span",{children:e.operation.hazard_type?e.operation.hazard_type.title:null})]}),function(e,r){return(0,o.jsxs)("div",{className:"d-flex mb-2 pb-1",children:[(0,o.jsx)("b",{children:e("Hazard")}),":",(0,o.jsx)("span",{children:(0,o.jsx)("ul",{className:"comma-separated",children:r.hazard&&r.hazard.length>=1?r.hazard.map((e,r)=>(0,o.jsx)("li",{children:e.title.en},r)):null})})]})}(r,e.operation),(0,o.jsxs)("p",{children:[(0,o.jsx)("b",{children:r("Syndrome")}),":",(0,o.jsx)("span",{children:e.operation.syndrome?e.operation.syndrome.title:null})]}),(0,o.jsxs)("p",{children:[(0,o.jsx)("b",{children:r("Created")}),":",(0,o.jsx)("span",{children:u()(e.operation.created_at).format(t)})]}),(0,o.jsxs)("p",{children:[(0,o.jsx)("b",{children:r("LastModified")}),":",(0,o.jsx)("span",{children:u()(e.operation.updated_at).format(t)})]})]}),(0,o.jsxs)(x.A,{md:!0,lg:6,sm:12,className:"ps-0",children:[(0,o.jsxs)("p",{children:[(0,o.jsx)("b",{children:r("CountryOrTerritory")}),":",(0,o.jsx)("span",{children:e.operation.country?e.operation.country.title:null})]}),(0,o.jsxs)("p",{children:[(0,o.jsx)("b",{children:r("OperationStatus")}),":",(0,o.jsxs)("span",{children:[" ",e.operation.status.title," "]})]}),(0,o.jsxs)("p",{children:[(0,o.jsx)("b",{children:r("StartDate")}),":",(0,o.jsx)("span",{children:e.operation.start_date?u()(e.operation.start_date).format(s):null})]}),(0,o.jsxs)("p",{children:[(0,o.jsx)("b",{children:r("EndDate")}),":",(0,o.jsx)("span",{children:e.operation.end_date?u()(e.operation.end_date).format(s):null})]})]})]})})]})})};s()}catch(e){s(e)}})},25303:e=>{e.exports=require("@restart/ui/NavItem")},26324:e=>{e.exports=require("warning")},26927:(e,r,t)=>{t.a(e,async(e,s)=>{try{t.r(r),t.d(r,{config:()=>q,default:()=>x,getServerSideProps:()=>m,getStaticPaths:()=>h,getStaticProps:()=>c,reportWebVitals:()=>j,routeModule:()=>S,unstable_getServerProps:()=>b,unstable_getServerSideProps:()=>A,unstable_getStaticParams:()=>P,unstable_getStaticPaths:()=>g,unstable_getStaticProps:()=>f});var o=t(63885),a=t(80237),i=t(81413),n=t(9616),p=t.n(n),u=t(72386),l=t(24292),d=e([u,l]);[u,l]=d.then?(await d)():d;let x=(0,i.M)(l,"default"),c=(0,i.M)(l,"getStaticProps"),h=(0,i.M)(l,"getStaticPaths"),m=(0,i.M)(l,"getServerSideProps"),q=(0,i.M)(l,"config"),j=(0,i.M)(l,"reportWebVitals"),f=(0,i.M)(l,"unstable_getStaticProps"),g=(0,i.M)(l,"unstable_getStaticPaths"),P=(0,i.M)(l,"unstable_getStaticParams"),b=(0,i.M)(l,"unstable_getServerProps"),A=(0,i.M)(l,"unstable_getServerSideProps"),S=new o.PagesRouteModule({definition:{kind:a.A.PAGES,page:"/operation/components/OperationDetailsAccordian",pathname:"/operation/components/OperationDetailsAccordian",bundlePath:"",filename:""},components:{App:u.default,Document:p()},userland:l});s()}catch(e){s(e)}})},27825:e=>{e.exports=require("lodash")},27910:e=>{e.exports=require("stream")},28217:e=>{e.exports=require("@restart/ui/DropdownItem")},29021:e=>{e.exports=require("fs")},29294:e=>{e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},29628:e=>{e.exports=require("dom-helpers/addClass")},29825:e=>{e.exports=require("prop-types")},29841:e=>{e.exports=require("dom-helpers/hasClass")},30362:e=>{e.exports=require("@restart/ui/DropdownToggle")},33873:e=>{e.exports=require("path")},36653:e=>{e.exports=require("nprogress")},36955:e=>{e.exports=require("@restart/hooks/useBreakpoint")},37766:e=>{e.exports=require("@restart/hooks/useMergedRefs")},39756:e=>{e.exports=import("redux")},40051:e=>{e.exports=require("dom-helpers/removeClass")},40361:e=>{e.exports=require("next/dist/compiled/next-server/pages.runtime.prod.js")},42893:e=>{e.exports=import("react-hot-toast")},43294:e=>{e.exports=require("formik")},50009:e=>{e.exports=require("@restart/ui/DropdownMenu")},54131:e=>{e.exports=import("@fortawesome/free-solid-svg-icons")},57664:e=>{e.exports=require("dom-helpers/scrollbarSize")},58928:e=>{e.exports=require("dom-helpers/canUseDOM")},59717:e=>{e.exports=require("@restart/ui/DropdownContext")},60560:e=>{e.exports=require("dom-helpers/contains")},63033:e=>{e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},65447:e=>{e.exports=require("@restart/ui/Button")},67364:e=>{e.exports=require("@restart/hooks/useCallbackRef")},68455:e=>{e.exports=import("redux-saga")},69722:e=>{e.exports=require("es6-promise")},74075:e=>{e.exports=require("zlib")},74716:e=>{e.exports=require("moment")},74987:e=>{e.exports=require("@restart/ui/ModalManager")},78097:e=>{e.exports=require("next-redux-saga")},78634:e=>{e.exports=require("@restart/ui/Anchor")},80237:(e,r)=>{Object.defineProperty(r,"A",{enumerable:!0,get:function(){return t}});var t=function(e){return e.PAGES="PAGES",e.PAGES_API="PAGES_API",e.APP_PAGE="APP_PAGE",e.APP_ROUTE="APP_ROUTE",e.IMAGE="IMAGE",e}({})},81366:e=>{e.exports=require("redux-auth-wrapper/connectedAuthWrapper")},81413:(e,r)=>{Object.defineProperty(r,"M",{enumerable:!0,get:function(){return function e(r,t){return t in r?r[t]:"then"in r&&"function"==typeof r.then?r.then(r=>e(r,t)):"function"==typeof r&&"default"===t?r:void 0}}})},81521:e=>{e.exports=require("dom-helpers/querySelectorAll")},81895:e=>{e.exports=require("@restart/hooks/useEventCallback")},82015:e=>{e.exports=require("react")},82053:e=>{e.exports=require("@fortawesome/react-fontawesome")},86842:e=>{e.exports=require("@restart/ui/SelectableContext")},87571:e=>{e.exports=require("dom-helpers/transitionEnd")},88751:e=>{e.exports=require("next-i18next")},93787:e=>{e.exports=require("redux-persist")},94947:e=>{e.exports=require("@restart/hooks/useTimeout")},96196:e=>{e.exports=require("next-redux-wrapper")},98320:e=>{e.exports=require("@restart/hooks/useIsomorphicEffect")},99460:e=>{e.exports=require("dom-helpers/css")}};var r=require("../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[6089,9216,9616,2386],()=>t(26927));module.exports=s})();