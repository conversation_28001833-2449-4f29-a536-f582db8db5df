{"version": 3, "file": "static/chunks/pages/updates/ContactForm-332ede3061d7c4a9.js", "mappings": "gFACA,4CACA,uBACA,WACA,OAAe,EAAQ,KAA4C,CACnE,EACA,SAFsB,wICkDtB,MAxCoB,IAChB,GAAM,GAAEA,CAAC,CAAE,CAAGC,CAAAA,EAAAA,EAAAA,EAAAA,CAAcA,CAAC,MAuClBC,IAtCL,OAsCgBA,EAAC,OAtCfC,CAAc,aAAEC,CAAW,CAAEC,UAAQ,CAAE,CAAGC,EAElD,MACI,UAACC,EAAAA,CAASA,CAAAA,CAACC,UAAU,WAAWC,KAAK,aACjC,UAACC,EAAAA,CAAGA,CAAAA,UACA,WAACC,EAAAA,CAAGA,CAAAA,WACA,WAACC,EAAAA,CAAIA,CAACC,KAAK,YACP,UAACD,EAAAA,CAAIA,CAACE,KAAK,EAACN,UAAU,0BAAkBR,EAAE,yBAC1C,UAACY,EAAAA,CAAIA,CAACG,OAAO,EACTC,KAAK,SACLC,KAAK,cACLC,YAAalB,EAAE,2BACfmB,QAAQ,IACRC,MAAOhB,EACPiB,SAAUlB,IAEd,UAACS,EAAAA,CAAIA,CAACG,OAAO,CAACO,QAAQ,EAACN,KAAK,mBACvBhB,EAAE,sCAGX,WAACY,EAAAA,CAAIA,CAACC,KAAK,YACP,UAACD,EAAAA,CAAIA,CAACE,KAAK,EAACN,UAAU,0BAAkBR,EAAE,sBAC1C,UAACY,EAAAA,CAAIA,CAACG,OAAO,EACTC,KAAK,SACLC,KAAK,WACLC,YAAalB,EAAE,wBACfmB,QAAQ,IACRC,MAAOf,EACPgB,SAAUlB,IAEd,UAACS,EAAAA,CAAIA,CAACG,OAAO,CAACO,QAAQ,EAACN,KAAK,mBAAWhB,EAAE,0CAMjE", "sources": ["webpack://_N_E/?7f64", "webpack://_N_E/./pages/updates/ContactForm.tsx"], "sourcesContent": ["\n    (window.__NEXT_P = window.__NEXT_P || []).push([\n      \"/updates/ContactForm\",\n      function () {\n        return require(\"private-next-pages/updates/ContactForm.tsx\");\n      }\n    ]);\n    if(module.hot) {\n      module.hot.dispose(function () {\n        window.__NEXT_P.push([\"/updates/ContactForm\"])\n      });\n    }\n  ", "//Import Library\r\nimport React from \"react\";\r\nimport { Form, Container, Row, Col } from \"react-bootstrap\";\r\n\r\n//Import services/components\r\nimport { useTranslation } from 'next-i18next';\r\n\r\n//TOTO refactor\r\ninterface ContactFormProps {\r\n  onHandleChange: (e: React.ChangeEvent<HTMLInputElement>) => void;\r\n  telephoneNo: string;\r\n  mobileNo: string;\r\n}\r\n\r\nconst ContactForm = (props: ContactFormProps): React.ReactElement => {\r\n    const { t } = useTranslation('common');\r\n    const { onHandleChange, telephoneNo, mobileNo } = props;\r\n\r\n    return (\r\n        <Container className=\"formCard\" fluid>\r\n            <Row>\r\n                <Col>\r\n                    <Form.Group>\r\n                        <Form.Label className=\"required-field\">{t(\"Updates.TelephoneNo\")}</Form.Label>\r\n                        <Form.Control\r\n                            type=\"number\"\r\n                            name=\"telephoneNo\"\r\n                            placeholder={t(\"Updates.TelephoneNumber\")}\r\n                            required\r\n                            value={telephoneNo}\r\n                            onChange={onHandleChange}\r\n                        />\r\n                        <Form.Control.Feedback type=\"invalid\">\r\n                            {t(\"Updates.PleaseTelephoneNumber\")}\r\n                        </Form.Control.Feedback>\r\n                    </Form.Group>\r\n                    <Form.Group>\r\n                        <Form.Label className=\"required-field\">{t(\"Updates.MobileNo\")}</Form.Label>\r\n                        <Form.Control\r\n                            type=\"number\"\r\n                            name=\"mobileNo\"\r\n                            placeholder={t(\"Updates.MobileNumber\")}\r\n                            required\r\n                            value={mobileNo}\r\n                            onChange={onHandleChange}\r\n                        />\r\n                        <Form.Control.Feedback type=\"invalid\">{t(\"Updates.PleaseProvideMobile\")}</Form.Control.Feedback>\r\n                    </Form.Group>\r\n                </Col>\r\n            </Row>\r\n        </Container>\r\n    );\r\n};\r\n\r\nexport default ContactForm;\r\n"], "names": ["t", "useTranslation", "ContactForm", "onHandleChange", "telephoneNo", "mobileNo", "props", "Container", "className", "fluid", "Row", "Col", "Form", "Group", "Label", "Control", "type", "name", "placeholder", "required", "value", "onChange", "<PERSON><PERSON><PERSON>"], "sourceRoot": "", "ignoreList": []}