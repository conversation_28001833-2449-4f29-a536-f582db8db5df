//Import services/components
import NavMenuItem from './NavMenuItem';
import connectedAuthWrapper from 'redux-auth-wrapper/connectedAuthWrapper';

export const canAccessPages = connectedAuthWrapper({
  authenticatedSelector: (state: any, props: any) => {
    if (state.permissions && state.permissions[props.item.id] && state.permissions[props.item.id]['read:any']) {
      if(state.user && state.user.is_focal_point){
        return state.user.status == "Approved" ? true :false;
      }
      if(state.user && state.user.is_vspace){
        return state.user.vspace_status == "Approved" ? true :false;
      }
      return true;
    }
    return false;
  },
  wrapperDisplayName: 'CanAccessPages',
});


interface SideBarProps {
  items: any[];
}

export default function SideBar(props: SideBarProps) {
  const {items} = props;
  const CanAccessPages = canAccessPages((PageProps: any) => <NavMenuItem item={PageProps.item} />);
  return (
    <ul>
      {items.map((item: any, index: number) => {
        return <CanAccessPages key={index} item={item} />
      })}
    </ul>
  )
}