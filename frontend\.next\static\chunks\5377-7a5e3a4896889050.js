"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[5377],{45377:(e,i,s)=>{s.r(i),s.d(i,{default:()=>_});var r=s(37876),t=s(14232),n=s(49589),d=s(29335),o=s(56970),a=s(37784),l=s(29504),c=s(60282),u=s(54773),m=s(35611),h=s(97685),g=s(89099),x=s.n(g),A=s(48230),C=s.n(A),j=s(21772),p=s(11041),y=s(31753),v=s(53718);let _=e=>{var i,s,g,A;let{t:_}=(0,y.Bd)("common"),F={_id:"",title:"",code:"",code3:"",dial_code:"",coordinates:[{latitude:"",longitude:""}],world_region:"",health_profile:"",security_advice:""},[f,b]=(0,t.useState)(F),[N,w]=(0,t.useState)([]),[k,L]=(0,t.useState)(!1),P=e.routes&&(e.routes[0]===_("adminsetting.Countries.Forms.edit_country")||"edit_land"===_("adminsetting.Countries.Forms.edit_country"))&&e.routes[1],S=(0,t.useRef)(null),G=async(i,s)=>{var r;let t,n;i.preventDefault();let d=s||f,o=d.title.charAt(0).toUpperCase()+d.title.slice(1),a={title:null==o?void 0:o.trim(),title_de:null==o?void 0:o.trim(),code:null==(r=d.code)?void 0:r.trim(),code3:d.code3,dial_code:d.dial_code,first_letter:{en:d.title.charAt(0).toUpperCase(),fr:d.title.charAt(0).toUpperCase(),de:d.title.charAt(0).toUpperCase()},coordinates:d.coordinates,world_region:d.world_region,health_profile:d.health_profile,security_advice:d.security_advice};P?(n="adminsetting.Countries.Forms.Countryisupdatedsuccessfully",t=await v.A.patch("/country/".concat(e.routes[1]),a)):(n="adminsetting.Countries.Forms.Countryisaddedsuccessfully",t=await v.A.post("/country",a)),t&&t._id?(h.Ay.success(_(n)),x().push("/adminsettings/country")):(null==t?void 0:t.errorCode)===11e3?h.Ay.error(_("duplicatesNotAllowed")):h.Ay.error(t)},M=e=>{if(e.target){let{name:i,value:s}=e.target;"longitude"===i||"latitude"===i?b(e=>{var r;return{...e,coordinates:[{...null==(r=f.coordinates)?void 0:r[0],[i]:s}]}}):b(e=>({...e,[i]:s}))}},q=async e=>{let i=await v.A.get("/worldregion",e);i&&w(i.data)};return(0,t.useEffect)(()=>{let i={query:{},sort:{title:"asc"},limit:"~"};P&&(async i=>{let s=await v.A.get("/country/".concat(e.routes[1]),i);if(s){let{world_region:e}=s;s.world_region=e&&e._id?e._id:"",b(e=>({...e,...s}))}})(i),q(i)},[]),(0,r.jsx)("div",{children:(0,r.jsx)(n.A,{className:"formCard",fluid:!0,children:(0,r.jsx)(d.A,{style:{marginTop:"5px",boxShadow:"0 4px 8px 0 rgba(0, 0, 0, 0.2), 0 6px 20px 0 rgba(0, 0, 0, 0.19)"},children:(0,r.jsx)(u.A,{onSubmit:G,ref:S,initialValues:f,enableReinitialize:!0,children:(0,r.jsxs)(d.A.Body,{children:[(0,r.jsx)(o.A,{children:(0,r.jsx)(a.A,{children:(0,r.jsx)(d.A.Title,{children:P?_("adminsetting.Countries.Forms.EditCountry"):_("adminsetting.Countries.Forms.AddCountry")})})}),(0,r.jsx)("hr",{}),(0,r.jsx)(o.A,{className:"mb-3",children:(0,r.jsx)(a.A,{md:!0,lg:12,sm:12,children:(0,r.jsxs)(l.A.Group,{children:[(0,r.jsx)(l.A.Label,{className:"required-field",children:_("adminsetting.Countries.Forms.CountryName")}),(0,r.jsx)(m.ks,{name:"title",id:"title",required:!0,value:f.title,validator:e=>""!==String(e||"").trim(),errorMessage:{validator:_("adminsetting.Countries.Forms.PleaseAddtheCountryName")},onChange:M})]})})}),(0,r.jsxs)(o.A,{className:"mb-3",children:[(0,r.jsx)(a.A,{md:!0,lg:4,sm:12,children:(0,r.jsxs)(l.A.Group,{children:[(0,r.jsx)(l.A.Label,{className:"required-field",children:_("adminsetting.Countries.Forms.CountryCode")}),(0,r.jsx)(m.ks,{name:"code",id:"code",required:!0,value:f.code,errorMessage:{validator:_("adminsetting.Countries.Forms.PleaseAddtheCountryCode")},onChange:M})]})}),(0,r.jsx)(a.A,{md:!0,lg:4,sm:12,children:(0,r.jsxs)(l.A.Group,{children:[(0,r.jsx)(l.A.Label,{children:_("adminsetting.Countries.Forms.CountryCode3")}),(0,r.jsx)(m.ks,{name:"code3",id:"code3",value:f.code3,errorMessage:_("adminsetting.Countries.Forms.PleaseAddtheCountryCode3"),onChange:M})]})}),(0,r.jsx)(a.A,{md:!0,lg:4,sm:12,children:(0,r.jsxs)(l.A.Group,{children:[(0,r.jsxs)(l.A.Label,{children:[_("adminsetting.Countries.Forms.DialCode")," "]}),(0,r.jsx)(m.ks,{name:"dial_code",id:"dial code",value:f.dial_code,errorMessage:_("adminsetting.Countries.Forms.PleaseAddtheCountryDialCode"),onChange:M})]})})]}),(0,r.jsx)(o.A,{className:"mb-3",children:(0,r.jsx)(a.A,{children:(0,r.jsx)(d.A.Title,{children:_("adminsetting.Countries.Forms.Co-ordinates")})})}),k?(0,r.jsxs)(o.A,{className:"mb-3",children:[(0,r.jsx)(a.A,{md:!0,lg:4,sm:12,children:(0,r.jsxs)(l.A.Group,{children:[(0,r.jsx)(l.A.Label,{children:_("adminsetting.Countries.Forms.Latitude")}),(0,r.jsx)(m.ks,{name:"latitude",id:"latitude",value:(null==(s=f.coordinates)||null==(i=s[0])?void 0:i.latitude)||"",errorMessage:_("adminsetting.Countries.Forms.PleaseAddtheLatitude"),onChange:M})]})}),(0,r.jsx)(a.A,{md:!0,lg:4,sm:12,children:(0,r.jsxs)(l.A.Group,{children:[(0,r.jsx)(l.A.Label,{children:_("adminsetting.Countries.Forms.Longitude")}),(0,r.jsx)(m.ks,{name:"longitude",id:"longitude",value:(null==(A=f.coordinates)||null==(g=A[0])?void 0:g.longitude)||"",errorMessage:_("adminsetting.Countries.Forms.PleaseAddtheLongitude"),onChange:M})]})}),(0,r.jsx)(a.A,{md:!0,lg:4,sm:12,children:(0,r.jsx)("div",{style:{marginTop:"30px"},children:(0,r.jsxs)(c.A,{variant:"secondary",onClick:()=>L(!k),children:[" ",(0,r.jsx)(j.g,{icon:p.Pcr,className:"me-2"}),_("adminsetting.Countries.Forms.HideCoordinates")]})})})]}):(0,r.jsx)("div",{className:"mb-3",children:(0,r.jsxs)(c.A,{variant:"secondary",onClick:()=>L(!k),children:[" ",(0,r.jsx)(j.g,{icon:p.Pcr,className:"me-2"}),_("adminsetting.Countries.Forms.coordinatesBtnText")]})}),(0,r.jsxs)(o.A,{className:"mb-3",children:[(0,r.jsx)(a.A,{md:!0,lg:4,sm:12,children:(0,r.jsxs)(l.A.Group,{children:[(0,r.jsx)(l.A.Label,{className:"required-field",children:_("adminsetting.Countries.Forms.WorldRegion")}),(0,r.jsxs)(m.s3,{name:"world_region",id:"world_region",value:f.world_region,errorMessage:{validator:_("adminsetting.Countries.Forms.PleaseAddtheWorldRegion")},required:!0,onChange:M,children:[(0,r.jsx)("option",{value:"",children:_("adminsetting.Countries.Forms.SelectWorldRegion")}),N.length>=1?N.map((e,i)=>(0,r.jsx)("option",{value:e._id,children:e.title},e._id)):null]})]})}),(0,r.jsx)(a.A,{md:!0,lg:4,sm:12,children:(0,r.jsxs)(l.A.Group,{children:[(0,r.jsx)(l.A.Label,{children:_("adminsetting.Countries.Forms.Healthprofile")}),(0,r.jsx)(m.ks,{name:"health_profile",id:"health_profile",value:f.health_profile,errorMessage:_("adminsetting.Countries.Forms.PleaseAddtheHealthProfile"),onChange:M})]})}),(0,r.jsx)(a.A,{md:!0,lg:4,sm:12,children:(0,r.jsxs)(l.A.Group,{children:[(0,r.jsx)(l.A.Label,{children:_("adminsetting.Countries.Forms.SecurityAdvice")}),(0,r.jsx)(m.ks,{name:"security_advice",id:"security_advice",value:f.security_advice,errorMessage:_("adminsetting.Countries.Forms.PleaseAddtheSecurityAdvice"),onChange:M})]})})]}),(0,r.jsx)(o.A,{className:"my-4",children:(0,r.jsxs)(a.A,{children:[(0,r.jsx)(c.A,{className:"me-2",type:"submit",variant:"primary",children:_("adminsetting.Countries.Forms.Submit")}),(0,r.jsx)(c.A,{className:"me-2",onClick:()=>{b(F),window.scrollTo(0,0)},variant:"info",children:_("adminsetting.Countries.Forms.Reset")}),(0,r.jsx)(C(),{href:"/adminsettings/[...routes]",as:"/adminsettings/country",children:(0,r.jsx)(c.A,{variant:"secondary",children:_("adminsetting.Countries.Forms.Cancel")})})]})})]})})})})})}}}]);
//# sourceMappingURL=5377-7a5e3a4896889050.js.map