"use strict";exports.id=9137,exports.ids=[9137],exports.modules={176:(e,t,s)=>{s.a(e,async(e,a)=>{try{s.r(t),s.d(t,{default:()=>o});var n=s(8732),l=s(82015);s(27825);var r=s(13054),i=s(63487),c=e([r,i]);[r,i]=c.then?(await c)():c;let o=function(e){let[t,s]=(0,l.useState)([]);return(0,n.jsx)(r.A,{eventsList:t,minicalendar:!0,showEventCounts:!0})};a()}catch(e){a(e)}})},13054:(e,t,s)=>{s.a(e,async(e,a)=>{try{s.d(t,{A:()=>S});var n=s(8732),l=s(64801),r=s(74716),i=s.n(r);s(16444);var c=s(44233),o=s.n(c),d=s(7082),h=s(83551),u=s(49481),x=s(82053),m=s(54131),j=s(27825),g=s.n(j),p=s(88751),f=e([m]);m=(f.then?(await f)():f)[0];let A=(0,l.momentLocalizer)(i());function v(e){let{t,i18n:s}=(0,p.useTranslation)("common"),a=s.language,{eventsList:r,style:i,minicalendar:c,views:d}=e,h={};return e.showEventCounts&&(h={eventWrapper:w,month:{dateHeader:e=>(0,n.jsx)(b,{eventsList:r,...e})}}),c&&(h.toolbar=y),(0,n.jsx)(l.Calendar,{culture:a,localizer:A,events:r,views:d,startAccessor:"start_date",endAccessor:"end_date",style:i,components:h,messages:{today:t("today"),previous:t("back"),next:t("Next"),month:t("Month"),week:t("Week"),day:t("Day")},onSelectEvent:e=>{let t=Object.keys(e).filter(e=>e.includes("parent")).toString(),s=t.split("_")[1];o().push(`/${s}/show/${e[t]}/update/${e._id}`)}})}function y(e){return(0,n.jsx)(d.A,{className:"mb-1",children:(0,n.jsxs)(h.A,{children:[(0,n.jsx)(u.A,{className:"p-0",md:1,children:(0,n.jsx)("i",{style:{cursor:"pointer"},onClick:()=>e.onNavigate("PREV"),className:"fas fa-chevron-left"})}),(0,n.jsx)(u.A,{className:"text-center",md:10,children:(0,n.jsx)("span",{className:"rbc-toolbar-label",children:e.label})}),(0,n.jsx)(u.A,{className:"p-0 text-end",md:1,children:(0,n.jsx)("i",{style:{cursor:"pointer"},onClick:()=>e.onNavigate("NEXT"),className:"fas fa-chevron-right"})})]})})}v.defaultProps={minicalendar:!1,views:["month"]};let N=(e,t)=>{let s=0;return g().forEach(e,e=>{let a=i()(e.start_date).set({hour:0,minute:0,second:0,millisecond:0}),n=i()(e.end_date).set({hour:0,minute:0,second:0,millisecond:0});i()(t).isBetween(a,n,null,"[]")&&(s+=1)}),s},b=({date:e,label:t,eventsList:s})=>{let a=N(s,e),l=i()(e).isBefore(new Date,"day");return(0,n.jsxs)("div",{className:"rbc-date-cell",onClick:()=>o().push("/events-calendar"),children:[(0,n.jsx)("a",{href:"#",children:t}),a>0&&(0,n.jsxs)("span",{className:"d-flex justify-content-start align-items-center fa-stack",children:[(0,n.jsx)(x.FontAwesomeIcon,{icon:m.faStar,color:l?"grey":"#04A6FB",size:"lg"}),(0,n.jsx)("span",{className:"eventCount",children:a})]})]})},w=e=>(0,n.jsx)("div",{onSelect:e.onSelect}),S=v;a()}catch(e){a(e)}})},19803:(e,t,s)=>{s.d(t,{A:()=>p});var a=s(3892),n=s.n(a),l=s(82015);s(26324);var r=s(14332),i=s(9532),c=s.n(i),o=s(80739),d=s(81895),h=s.n(d),u=s(25303),x=s(86842),m=s(8732);let j=l.forwardRef(({bsPrefix:e,active:t,disabled:s,eventKey:a,className:l,variant:r,action:i,as:c,...d},j)=>{e=(0,o.oU)(e,"list-group-item");let[g,p]=(0,u.useNavItem)({key:(0,x.makeEventKey)(a,d.href),active:t,...d}),f=h()(e=>{if(s){e.preventDefault(),e.stopPropagation();return}g.onClick(e)});s&&void 0===d.tabIndex&&(d.tabIndex=-1,d["aria-disabled"]=!0);let v=c||(i?d.href?"a":"button":"div");return(0,m.jsx)(v,{ref:j,...d,...g,onClick:f,className:n()(l,e,p.isActive&&"active",s&&"disabled",r&&`${e}-${r}`,i&&`${e}-action`)})});j.displayName="ListGroupItem";let g=l.forwardRef((e,t)=>{let s,{className:a,bsPrefix:l,variant:i,horizontal:d,numbered:h,as:u="div",...x}=(0,r.useUncontrolled)(e,{activeKey:"onSelect"}),j=(0,o.oU)(l,"list-group");return d&&(s=!0===d?"horizontal":`horizontal-${d}`),(0,m.jsx)(c(),{ref:t,...x,as:u,className:n()(a,j,i&&`${j}-${i}`,s&&`${j}-${s}`,h&&`${j}-numbered`)})});g.displayName="ListGroup";let p=Object.assign(g,{Item:j})},51612:(e,t,s)=>{s.a(e,async(e,a)=>{try{s.r(t),s.d(t,{default:()=>d});var n=s(8732),l=s(82015),r=s(63487),i=s(32412),c=s(88751),o=e([r]);r=(o.then?(await o)():o)[0];let d=()=>{let{i18n:e}=(0,c.useTranslation)("common");e.language&&e.language;let[t,s]=(0,l.useState)({title:"",description:"",pageCategory:"",images:"",isEnabled:!0}),[a,o]=(0,l.useState)(!1),d={query:{pageCategory:""},sort:{title:"asc"},limit:"~"},h=async()=>{let e=await u();if(o(!0),e.length>0){d.query.pageCategory=e;let t=await r.A.get("/landingPage",d);if(Array.isArray(t.data)&&t.data.length>0){let e=t.data[t.data.length-1];e.images=t&&e.images.length>0&&!0===e.isEnabled?e.images.map((e,t)=>String(`http://localhost:3001/api/v1/image/show/${e._id}`)):"/images/logo.jpg",e.description=t&&e.description.length>0&&!0===e.isEnabled?e.description:"The Robert Koch Institut is taking over the coordination of the “WHO AMR Surveillance and Quality Assessment Collaborating Centres Network” this autumn 2019. The network supports the World Health Organization (WHO) to reduce drug-resistant infections globally. It focuses on further developing the global antimicrobial resistance (AMR) surveillance system (GLASS), and promoting exchange and peer support between countries.",s(e),u(),o(!1)}}},u=async()=>{let e=await r.A.get("/pagecategory",{query:{title:"AboutUs"}});return!!e&&!!e.data&&e.data.length>0&&e.data[0]._id};(0,l.useEffect)(()=>{h()},[]);let x=t.description.replace(/\&nbsp;/g," ");return(0,n.jsx)("div",{className:"aboutUs",children:!0===a?(0,n.jsx)(i.A,{}):(0,n.jsxs)("div",{children:[(0,n.jsx)("img",{className:"logoImg",src:t.images,alt:""}),(0,n.jsx)("div",{dangerouslySetInnerHTML:{__html:x}})," "]})})};a()}catch(e){a(e)}})},55176:(e,t,s)=>{s.a(e,async(e,a)=>{try{s.r(t),s.d(t,{default:()=>j});var n=s(8732),l=s(82015),r=s(83551),i=s(7082);s(27825);var c=s(13866),o=s.n(c),d=s(20174),h=s(85795),u=s(63487),x=e([u]);function m(e){let{announcements:t}=e;return(0,n.jsx)("div",{children:t.map((e,t)=>(0,n.jsx)(r.A,{className:"announcementItem",children:(0,n.jsx)(h.default,{item:e})},t))})}u=(x.then?(await x)():x)[0];let j=function({t:e}){let[t,s]=(0,l.useState)([]),[a,c]=(0,l.useState)(0),[h]=(0,l.useState)(3),u=e=>{let s=a,[n,l]=[0,h-1];"next"===e&&s<l?s++:"prev"===e&&s>0&&s--,t.length-s==1&&(s=t.length-1),t.length-s==0&&(s=1),c(s)};return(0,n.jsx)("div",{className:"announcements",children:t&&t.length>0?(0,n.jsxs)(n.Fragment,{children:[(0,n.jsx)(i.A,{fluid:!0,children:(0,n.jsxs)(r.A,{children:[(0,n.jsx)(d.Ay,{xs:10,className:"p-0",children:(0,n.jsx)("h4",{children:e("announcements")})}),t&&t.length>1?(0,n.jsx)(d.Ay,{xs:2,className:"text-end carousel-control p-0",children:(0,n.jsxs)("div",{className:"carousel-navigation",children:[(0,n.jsx)("a",{className:"left carousel-control",onClick:()=>u("prev"),children:(0,n.jsx)("i",{className:"fa fa-chevron-left"})}),(0,n.jsx)("a",{className:"right carousel-control",onClick:()=>u("next"),children:(0,n.jsx)("i",{className:"fa fa-chevron-right"})})]})}):null]})}),(0,n.jsx)(i.A,{fluid:!0,children:(0,n.jsx)(r.A,{children:(0,n.jsx)(d.Ay,{xs:12,className:"p-0",children:(0,n.jsx)(o(),{indicators:!1,controls:!1,interval:null,activeIndex:a,children:t.map((e,t)=>(0,n.jsx)(o().Item,{children:(0,n.jsx)(m,{announcements:e})},t))})})})})]}):(0,n.jsx)(i.A,{fluid:!0,children:(0,n.jsxs)(r.A,{children:[(0,n.jsx)(d.Ay,{xs:10,className:"p-0",children:(0,n.jsx)("h4",{children:e("announcements")})}),(0,n.jsxs)(d.Ay,{xs:12,className:"p-0",children:[(0,n.jsx)("div",{className:"border p-3",children:e("NoAnnouncementsavailabletodisplay")}),(0,n.jsx)("br",{})]})]})})})};a()}catch(e){a(e)}})},79137:(e,t,s)=>{s.a(e,async(e,a)=>{try{s.r(t),s.d(t,{default:()=>y,getStaticProps:()=>v});var n=s(8732),l=s(7082),r=s(83551),i=s(49481),c=s(14062),o=s(82015),d=s(35576),h=s(51612),u=s(69958),x=s(87178),m=s(55176),j=s(5584),g=s(176),p=s(63487),f=e([c,h,u,x,m,j,g,p]);async function v({locale:e}){return{props:{...await (0,d.serverSideTranslations)(e,["common"])}}}[c,h,u,x,m,j,g,p]=f.then?(await f)():f;let y=(0,c.connect)(e=>e)(function({t:e,user:t}){let[s,a]=(0,o.useState)(""),[c,d]=(0,o.useState)([]),[p,f]=(0,o.useState)([]),[v,y]=(0,o.useState)("Approved"),[A,N]=(0,o.useState)("");return(0,n.jsx)(n.Fragment,{children:"Approved"==v?(0,n.jsxs)(l.A,{fluid:!0,className:"p-0 dashboardScreen",children:[(0,n.jsxs)("h2",{children:[e("hello")," ",s]}),(0,n.jsxs)(l.A,{fluid:!0,children:[(0,n.jsxs)(r.A,{children:[(0,n.jsxs)(i.A,{className:"ps-lg-0 dashboardLeft",lg:"8",children:[(0,n.jsx)(r.A,{children:(0,n.jsx)(i.A,{xs:"12",children:(0,n.jsx)(h.default,{})})}),(0,n.jsx)(r.A,{children:(0,n.jsx)(i.A,{xs:12,children:(0,n.jsxs)(r.A,{children:[(0,n.jsx)(i.A,{md:"6",className:"ongoingBlock",children:(0,n.jsx)(u.default,{t:e,fetchOngoingOperations:d})}),(0,n.jsx)(i.A,{md:"6",className:"ongoingBlock",children:(0,n.jsx)(x.default,{t:e,fetchOngoingProjects:f})})]})})})]}),(0,n.jsx)(i.A,{className:"pe-lg-0 dashboard-calendar",lg:"4",children:(0,n.jsx)(g.default,{})})]}),(0,n.jsx)(r.A,{children:(0,n.jsx)(i.A,{className:"p-lg-0",xs:"12",children:(0,n.jsx)(m.default,{t:e})})}),(0,n.jsx)(r.A,{children:(0,n.jsx)(i.A,{className:"p-lg-0",xs:12,children:(0,n.jsx)(j.default,{t:e,ongoingProjects:p,ongoingOperations:c})})})]})]}):(0,n.jsxs)("div",{className:"Focalpoint",children:[(0,n.jsx)("h2",{children:"Welcome to Robert Koch Institut !"}),(0,n.jsx)("div",{children:(0,n.jsx)("h5",{className:"text-muted",children:e(A)})})]})})});a()}catch(e){a(e)}})},85795:(e,t,s)=>{s.r(t),s.d(t,{default:()=>i});var a=s(8732),n=s(49481),l=s(19918),r=s.n(l);function i(e){let{item:t}=e,s=t.images.length-1,l=`parent_${t.type}`;return(0,a.jsxs)(n.A,{className:"p-0",xs:12,children:[(0,a.jsx)(r(),{href:`/${t.type}/[...routes]`,as:`/${t.type}/show/${t[l]}/update/${t._id}`,children:t.images&&t.images[s]?(0,a.jsx)("img",{src:`http://localhost:3001/api/v1/image/show/${t.images[s]._id}`,alt:"announcement",className:"announceImg"}):(0,a.jsx)("i",{className:"fa fa-bullhorn announceImg"})}),(0,a.jsxs)("div",{className:"announceDesc",children:[(0,a.jsx)(r(),{href:`/${t.type}/[...routes]`,as:`/${t.type}/show/${t[l]}/update/${t._id}`,children:t&&t.title?t.title:""}),(0,a.jsx)("p",{children:t&&t.description?(e=>{let t=document.createElement("div");t.innerHTML=e;let s=t.textContent||t.innerText||"";return s.length>260?`${s.substring(0,257)}...`:s})(t.description):null})]})]})}},87178:(e,t,s)=>{s.a(e,async(e,a)=>{try{s.r(t),s.d(t,{default:()=>j});var n=s(8732),l=s(82015);s(27825);var r=s(19918),i=s.n(r),c=s(19803),o=s(6858),d=s(32412),h=s(63487),u=e([h]);function x(e){let{list:t}=e;return t.length>0?(0,n.jsx)(c.A,{children:t.map((e,t)=>(0,n.jsx)(c.A.Item,{children:(0,n.jsx)(i(),{href:"/project/[...routes]",as:`/project/show/${e._id}`,children:e.title})},t))}):null}function m(e){let{project:t}=e;return(0,n.jsx)(i(),{href:"/project/[...routes]",as:`/project/show/${t.id}`,className:"active-op-project",children:(0,n.jsx)("span",{className:"project-title link",children:t.body})})}h=(u.then?(await u)():u)[0];let j=function(e){let{t,fetchOngoingProjects:s}=e,a=t("OngoingProjects"),[r,i]=(0,l.useState)({body:"",id:"",list:[]}),[c,h]=(0,l.useState)(!0),u={heading:a,body:(0,n.jsx)(x,{list:r.list})};return(0,n.jsx)(o.A,{dialogClassName:"ongoing-project-list",list:u,header:a,body:c?(0,n.jsx)(d.A,{}):(0,n.jsx)(m,{project:r})})};a()}catch(e){a(e)}})}};