//Import Library
import {useEffect, useState} from "react";

//Import services/components
import ListContainer from "./ListContainer"
import apiService from "../../services/apiService";

interface ActiveProjectOperationsProps {
  t: (key: string) => string;
  ongoingProjects: any[];
  ongoingOperations: any[];
}

export default function ActiveProjectOperations(props: ActiveProjectOperationsProps) {

  const {t, ongoingOperations, ongoingProjects} = props;
  const [eventsData, setEventsData] = useState<any[]>([]);
  const fetchEvents = async () => {
    const eventParams: any = {
      query: { status: [] },
      sort: { created_at: "desc" },
      limit: 10,
      select: "-description -operation -world_region -country_regions -hazard_type -hazard -syndrome -status -laboratory_confirmed -officially_validated -rki_monitored -risk_assessment -date -more_info -images -user -created_at -updated_at"
    };
    const fetchEventsStatus = await fetchEventtStatus();
    console.log('fetchEventsStatus result:', fetchEventsStatus);
    if (fetchEventsStatus) {
      eventParams.query.status.push(fetchEventsStatus);
    }
    console.log('Event params for API call:', eventParams);
    const response = await apiService.get('event', eventParams);
    console.log('Events API response:', response);
    if (response && Array.isArray(response.data) && response.data.length > 0) {
      console.log('Setting events data:', response.data.length, 'events');
      setEventsData(response.data);
    } else {
      console.log('No events data received or empty array');
      setEventsData([]);
    }
  }

  const fetchEventtStatus = async () => {
    const response = await apiService.get('/eventStatus', {query: {title: 'Current'}});
    console.log('Event status API response:', response);
    if (response && response.data && response.data.length > 0) {
      console.log('Found Current status ID:', response.data[0]._id);
      return response.data[0]._id;
    }
    console.log('No Current status found, will fetch all events');
    return false;
  };

  useEffect(() => {
    fetchEvents();
  },[]);

  return (
    <div className="active-projects-announcements">
      <h4>{t('allActivity')}</h4>
      <ListContainer t={t} currentEvents={eventsData} ongoingProjects={ongoingProjects} ongoingOperations={ongoingOperations} />
    </div>
  )
}