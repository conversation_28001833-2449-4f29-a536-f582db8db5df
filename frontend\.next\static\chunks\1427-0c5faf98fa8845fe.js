"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[1427],{31427:(e,t,i)=>{i.r(t),i.d(t,{default:()=>o});var s=i(37876);i(14232);var l=i(49589),a=i(37784),r=i(56970),n=i(89673),c=i(31753);let o=e=>{let{t}=(0,c.Bd)("common"),i=t=>{e.getId(t)},o=t=>{e.getSourceCollection(t)};return(0,s.jsx)(l.A,{className:"formCard mt-0 p-0",fluid:!0,children:(0,s.jsxs)(a.A,{children:[(0,s.jsx)(r.A,{className:"header-block",lg:12,children:(0,s.jsx)("h6",{children:(0,s.jsx)("span",{children:t("update.Documents")})})}),(0,s.jsx)(r.A,{children:(0,s.jsx)(n.A,{datas:e.data,type:"application",srcText:e.srcText,getImgID:e=>i(e),getImageSource:e=>o(e)})})]})})}},89673:(e,t,i)=>{i.d(t,{A:()=>A});var s=i(37876),l=i(14232),a=i(17336),r=i(21772),n=i(11041),c=i(37784),o=i(29504),d=i(60282),p=i(31195),x=i(82851),m=i.n(x),g=i(97685),u=i(53718),h=i(31753);let f=[],j={flex:1,display:"flex",flexDirection:"column",alignItems:"center",justifyContent:"center",width:"100%",height:"100%",borderWidth:.1,borderColor:"#fafafa",backgroundColor:"#fafafa",color:"black",transition:"border  .24s ease-in-out",padding:"15px"},y={display:"flex",padding:"10px",width:"100%",border:"2px solid gray",flexDirection:"column",justifyContent:"flex-start",flexWrap:"wrap",marginTop:20},v={width:"150px"},w={borderColor:"#2196f3"},A=e=>{let t,{t:i}=(0,h.Bd)("common"),[x,A]=(0,l.useState)(!1),[b,I]=(0,l.useState)(),C="application"==e.type?0x1400000:"20971520",[N,D]=(0,l.useState)([]),[k,S]=(0,l.useState)(!0),[F,E]=(0,l.useState)([]),_=e&&"application"===e.type?"/files":"/image",P=async e=>{await u.A.remove("".concat(_,"/").concat(e))},z=e=>{I(e),A(!0)},B=(e,t)=>{let i=[...F];i[t]=e.target.value,E(i)},L=t=>{switch(t&&t.name.split(".").pop()){case"JPG":case"jpg":case"jpeg":case"jpg":case"png":return(0,s.jsx)("img",{src:t.preview,style:v});case"pdf":return(0,s.jsx)("img",{src:"/images/fileIcons/pdfFile.png",className:"application"===e.type?"docPreview":"imgPreview"});case"docx":default:return(0,s.jsx)("img",{src:"/images/fileIcons/wordFile.png",className:"application"===e.type?"docPreview":"imgPreview"});case"xls":case"xlsx":return(0,s.jsx)("img",{src:"/images/fileIcons/xlsFile.png",className:"application"===e.type?"docPreview":"imgPreview"})}},T=()=>A(!1),U=()=>{A(!1)},O=t=>{let i=(t=b)&&t._id?{serverID:t._id}:{file:t},s=m().findIndex(f,i),l=[...F];l.splice(s,1),E(l),P(f[s].serverID),f.splice(s,1),e.getImgID(f,e.index?e.index:0);let a=[...N];a.splice(a.indexOf(t),1),D(a),A(!1)},R=N.map((t,l)=>(0,s.jsxs)("div",{children:[(0,s.jsx)(c.A,{xs:12,children:(0,s.jsxs)("div",{className:"row",children:[(0,s.jsx)(c.A,{md:4,lg:3,className:"application text-center align-self-center"===e.type?"docImagePreview text-center align-self-center":"imgPreview text-center align-self-center",children:L(t)}),(0,s.jsx)(c.A,{md:5,lg:7,className:"align-self-center",children:(0,s.jsxs)(o.A,{children:[(0,s.jsxs)(o.A.Group,{controlId:"filename",children:[(0,s.jsx)(o.A.Label,{className:"mt-2",children:i("FileName")}),(0,s.jsx)(o.A.Control,{size:"sm",type:"text",disabled:!0,value:t.original_name?t.original_name:t.name})]}),(0,s.jsxs)(o.A.Group,{controlId:"description",children:[(0,s.jsx)(o.A.Label,{children:"application"===e.type?i("ShortDescription/(Max255Characters)"):i("Source/Description")}),(0,s.jsx)(o.A.Control,{maxLength:"application"===e.type?255:void 0,size:"sm",type:"text",placeholder:"application"===e.type?i("`Enteryourdocumentdescription`"):i("`Enteryourimagesource/description`"),value:F[l],onChange:e=>B(e,l)})]})]})}),(0,s.jsx)(c.A,{md:3,lg:2,className:"align-self-center text-center",onClick:()=>z(t),children:(0,s.jsx)(d.A,{variant:"dark",children:i("Remove")})})]})}),(0,s.jsxs)(p.A,{show:x,onHide:T,children:[(0,s.jsx)(p.A.Header,{closeButton:!0,children:(0,s.jsx)(p.A.Title,{children:i("DeleteFile")})}),(0,s.jsx)(p.A.Body,{children:i("Areyousurewanttodeletethisfile?")}),(0,s.jsxs)(p.A.Footer,{children:[(0,s.jsx)(d.A,{variant:"secondary",onClick:U,children:i("Cancel")}),(0,s.jsx)(d.A,{variant:"primary",onClick:()=>O(t),children:i("yes")})]})]})]},l));(0,l.useEffect)(()=>{N.forEach(e=>URL.revokeObjectURL(e.preview)),f=[]},[]),(0,l.useEffect)(()=>{e.getImageSource(F)},[F]),(0,l.useEffect)(()=>{E(e.srcText)},[e.srcText]),(0,l.useEffect)(()=>{e&&"true"===e.singleUpload&&S(!1),e&&e.datas&&D([...e.datas.map((t,i)=>(f.push({serverID:t._id,index:e.index?e.index:0,type:t.name.split(".")[1]}),{...t,preview:"".concat("http://localhost:3001/api/v1","/image/show/").concat(t._id)}))])},[e.datas]);let W=async(t,i)=>{if(t.length>i)try{let s=new FormData;s.append("file",t[i]);let l=await u.A.post(_,s,{"Content-Type":"multipart/form-data"});f.push({serverID:l._id,file:t[i],index:e.index?e.index:0,type:t[i].name.split(".")[1]}),W(t,i+1)}catch(e){W(t,i+1)}else e.getImgID(f,e.index?e.index:0)},G=(0,l.useCallback)(async e=>{await W(e,0);let t=e.map(e=>Object.assign(e,{preview:URL.createObjectURL(e)}));k?D(e=>[...e,...t]):D([...t])},[]),{getRootProps:M,getInputProps:H,isDragActive:J,isDragAccept:V,isDragReject:q,fileRejections:K}=(0,a.VB)({accept:e&&e.type?"application/pdf, application/vnd.openxmlformats-officedocument.wordprocessingml.document, application/vnd.oasis.opendocument.text,application/vnd.openxmlformats-officedocument.spreadsheetml.sheet,text/csv":"image/*",multiple:k,minSize:0,maxSize:C,onDrop:G,validator:function(e){if("/image"===_){if("image"!==e.type.substring(0,5))return g.Ay.error(i("toast.filetypenotsupport")),{code:"file-invalid-type",message:"File type not supported"}}else if("/files"===_&&"image"===e.type.substring(0,5))return g.Ay.error(i("toast.filetypenotsupport")),{code:"file-invalid-type",message:"File type not supported"};return null}}),Q=(0,l.useMemo)(()=>({...j,...J?w:{outline:"2px dashed #bbb"},...V?{outline:"2px dashed #595959"}:{outline:"2px dashed #bbb"},...q?{outline:"2px dashed red"}:{activeStyle:w}}),[J,q]);t=e&&"application"===e.type?(0,s.jsx)("small",{style:{color:"#595959"},children:i("DocumentWeSupport")}):(0,s.jsx)("small",{style:{color:"#595959"},children:i("ImageWeSupport")});let X=K.length>0&&K[0].file.size>C;return(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)("div",{className:" d-flex justify-content-center align-items-center mt-3",style:{width:"100%",height:"180px"},children:(0,s.jsxs)("div",{...M({style:Q}),children:[(0,s.jsx)("input",{...H()}),(0,s.jsx)(r.g,{icon:n.rOd,size:"4x",color:"#999"}),(0,s.jsx)("p",{style:{color:"#595959",marginBottom:"0px"},children:i("Drag'n'dropsomefileshere,orclicktoselectfiles")}),!k&&(0,s.jsxs)("small",{style:{color:"#595959"},children:[(0,s.jsx)("b",{children:"Note:"})," One single image will be accepted"]}),t,(e.type,X&&(0,s.jsxs)("small",{className:"text-danger mt-2",children:[(0,s.jsx)(r.g,{icon:n.tUE,size:"1x",color:"red"})," ",i("FileistoolargeItshouldbelessthan20MB")]})),q&&(0,s.jsxs)("small",{className:"text-danger",style:{color:"#595959"},children:[(0,s.jsx)(r.g,{icon:n.tUE,size:"1x",color:"red"})," ",i("Filetypenotacceptedsorr")]})]})}),N.length>0&&(0,s.jsx)("div",{style:y,children:R})]})}}}]);
//# sourceMappingURL=1427-0c5faf98fa8845fe.js.map