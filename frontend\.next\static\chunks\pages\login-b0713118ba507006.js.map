{"version": 3, "file": "static/chunks/pages/login-b0713118ba507006.js", "mappings": "+EACA,4CACA,SACA,WACA,OAAe,EAAQ,KAA8B,CACrD,EACA,SAFsB,4MC6KtB,MAAeA,CAAAA,EAAAA,EAAAA,EAAAA,CAAOA,GA9JT,IACT,CA6J0BC,EA7JpB,GAAEC,CAAC,CAAE,CAAGC,CAAAA,EAAAA,EAAAA,EAAAA,CAAcA,CAAC,UACrB,CAACC,EAASC,EAAW,CAAGC,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAwD,CAC5FC,SAAU,GACVC,SAAU,GACVC,WAAY,EAChB,GACM,EAAGC,EAAQ,CAAGJ,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,EAAC,GAEvBK,EAAiB,IAED,IAAI,CAAlBC,EAAEC,OAAO,EACTC,EAAaF,EAErB,EAEM,CAACG,EAAaC,EAAe,CAAGV,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAC,CAC3CW,QAAS,GACTC,SAAS,CACb,GAEAC,CAAAA,EAAAA,EAAAA,SAAAA,CAASA,CAAC,KACN,IAAMV,EAAoD,SAAvCW,aAAaC,OAAO,CAAC,cAGxChB,EAAW,CAAEE,SAFAE,CAEUa,CAFGF,aAAaC,OAAO,CAAC,QAAU,GAE5Bb,SADZC,CACsBD,CADTY,aAAaC,OAAO,CAAC,YAAc,GAChBZ,WAAYA,CAAW,EAC5E,EAAG,EAAE,EAQL,IAAMc,EAAe,IACjBP,EAAe,CAAEC,QAAS,GAAIC,SAAS,CAAM,GAC7Cb,EAAW,CAAE,GAAGD,CAAO,CAAE,CAACQ,EAAEY,MAAM,CAACC,IAAI,CAAC,CAAEb,EAAEY,MAAM,CAACE,KAAK,EAC5D,EAEMZ,EAAe,MAAOF,IACxBA,EAAEe,cAAc,GAChB,GAAM,MAAEC,CAAI,QAAEC,CAAM,CAAE,CAAGC,EAAAA,CAAWA,CACpC1B,EAAQG,QAAQ,CAAGH,EAAQG,QAAQ,CAACwB,IAAI,GACxC,IAAMC,EAAW,MAAMJ,EAAKxB,GACxB4B,GAAYA,QAASC,MAAM,EAAYD,EAASE,IAAI,EAAIF,EAASE,IAAI,CAACC,SAAS,EAAE,EACjFC,EAAKA,CAACC,OAAO,CAACnC,EAAE,kBAEhBoC,IAAAA,IAAW,CAAC,KACZC,EAAMC,QAAQ,CAACC,CAAAA,EAAAA,EAAAA,EAAAA,CAAoBA,KACR,KAAK,CAAzBT,EAASC,MAAM,EACtB,MAAMJ,IACNb,EAAe,CAAEE,SAAS,EAAMD,QAASf,EAAE,qBAAsB,KAEjEQ,GAAQ,GACRL,EAAW,GAAgB,EAAE,GAAGqC,CAAS,CAAEnC,EAAhB,OAA0B,GAAIC,SAAU,GAAG,GAClEwB,KAAyB,GAAhBC,MAAM,EACfjB,EAAe,CAAEE,QAAS,GAAMD,QAASf,EAAE,wBAAyB,IAG5EkB,aAAauB,OAAO,CAAC,aAAcvC,EAAQK,UAAU,CAACmC,QAAQ,IAC9DxB,aAAauB,OAAO,CAAC,OAAQvC,EAAQK,UAAU,CAAGL,EAAQG,QAAQ,CAAG,IACrEa,aAAauB,OAAO,CAAC,WAAYvC,EAAQK,UAAU,CAAGL,EAAQI,QAAQ,CAAG,GAC7E,EAEA,MACI,WAACqC,MAAAA,CAAIC,UAAU,2BACV/B,EAAYG,OAAO,CAChB,UAAC6B,EAAAA,CAAKA,CAAAA,CAAgBC,QAAS,kBAC1BjC,EAAYE,OAAO,EADZ,UAIZ,yBAEJ,UAAC4B,MAAAA,CAAIC,UAAU,mBACX,UAACD,MAAAA,CAAIC,UAAU,qBACX,WAACD,MAAAA,CAAIC,UAAU,oBACX,UAACD,MAAAA,CAAIC,UAAU,iCACX,WAACD,MAAAA,CAAIC,UAAU,6BACX,UAACD,MAAAA,CAAIC,UAAU,qBACX,UAACG,MAAAA,CAAIC,IAAI,2BAA2BC,IAAI,6BAE5C,WAACC,OAAAA,CAAKN,UAAU,gBAAgBO,SAAUvC,YACtC,UAAC+B,MAAAA,CAAIC,UAAU,yBACX,UAACQ,IAAIA,CAACC,KAAM,aAER,UAACN,MAAAA,CAAIC,IAAI,KAFRI,cAE2BH,IAAI,oCAIxC,WAACN,MAAAA,WACG,WAACA,MAAAA,CAAIC,UAAU,iBACX,UAACU,QAAAA,CAAMV,UAAU,iBAAQ,sBACzB,UAACW,QAAAA,CACGX,UAAU,eACVY,KAAK,OACLC,YAAY,8BACZlC,KAAK,WACLC,MAAOtB,EAAQG,QAAQ,CACvBqD,SAAUrC,EACVsC,WAAYlD,EACZmD,QAAQ,SAGhB,WAACjB,MAAAA,CAAIC,UAAU,iBACX,UAACU,QAAAA,CAAMV,UAAU,iBAAQ,aACzB,UAACW,QAAAA,CACGX,UAAU,eACVY,KAAK,WACLhC,MAAOtB,EAAQI,QAAQ,CACvBmD,YAAY,WACZlC,KAAK,WACLmC,SAAUrC,EACVsC,WAAYlD,EACZmD,QAAQ,SAGhB,WAACjB,MAAAA,CAAIC,UAAU,2DACX,WAACU,QAAAA,WACG,UAACC,QAAAA,CACGhC,KAAK,aACLsC,QAAS3D,EAAQK,UAAU,CAC3BmD,SA7FnB,CA6F6BI,GA5FtD,IAAMP,EAAQQ,EAAMzC,MAAM,CACpBE,EAAuB,aAAf+B,EAAMC,IAAI,CAAkBD,EAAMM,OAAO,CAAGN,EAAM/B,KAAK,CACrErB,EAAW,CAAE,GAAGD,CAAO,CAAE,CAACqD,EAAMhC,IAAI,CAAC,CAAEC,CAAM,GAC7CV,EAAe,CAAEC,QAAS,GAAIC,SAAS,CAAM,EACjD,EAyFgDwC,KAAK,aACN,IAAI,iBAGX,UAACJ,IAAIA,CAACC,KAAK,mBAAmBW,GAAG,iBAA5BZ,WACD,UAACa,IAAAA,CAAErB,UAAU,gCAAuB,0BAI5C,UAACD,MAAAA,CAAIC,UAAU,4BACX,UAACD,MAAAA,CAAIC,UAAU,mBACX,UAACsB,SAAAA,CAAOtB,UAAU,oBAAoBY,KAAK,kBAAS,2BAS5E,UAACb,MAAAA,CAAIC,UAAU,oBAMvC,4IChKA,IAAMuB,EAAgBC,CAAAA,EAAAA,EAAAA,CAAAA,CAAgBA,CAAC,MACvCD,EAAcE,WAAW,CAAG,gBAC5B,IAAMC,EAA4BC,EAAAA,UAAgB,CAA7B,CAA8B,EAKhDC,QAL6B,CAC9B5B,WAAS,UACT6B,CAAQ,CACRT,GAAIU,EAAYP,CAAa,CAC7B,GAAG9B,EACJ,GAEC,OADAoC,EAAWE,CAAAA,EAAAA,EAAAA,EAAAA,CAAkBA,CACX,EADsB,iBACpBC,CAAAA,EAAAA,EAAAA,GAAAA,CAAIA,CAACF,EAAW,CAClCF,IAAKA,EACL5B,UAAWiC,IAAWjC,EAAW6B,GACjC,GAAGpC,CAAK,EAEZ,GACAiC,EAAaD,WAAW,CAAG,8BCf3B,IAAMS,EAAyBP,EAAAA,QAAb,EAA6B,CAAC,GAK7CC,KAL0B,GAAoB,WAC/C5B,CAAS,UACT6B,CAAQ,CACRT,GAAIU,EAAYK,EAAAA,CAAM,CACtB,GAAG1C,EACJ,GAEC,OADAoC,EAAWE,CAAAA,EAAAA,EAAAA,EAAAA,CAAkBA,CAACF,EAAU,cACpBG,CAAAA,EAAAA,EAAAA,GAAAA,CAAIA,CAACF,EAAW,CAClCF,IAAKA,EACL5B,UAAWiC,IAAWjC,EAAW6B,GACjC,GAAGpC,CAAK,EAEZ,GACAyC,EAAUT,WAAW,CAAG,sCCPxB,IAAMxB,EAAqB0B,EAAAA,IAAb,MAA6B,CAAC,CAACS,EAAmBR,CAAvC,IACvB,GAAM,UACJC,CAAQ,MACRQ,GAAO,CAAI,YACXC,EAAa,aAAa,cAC1BC,CAAY,WACZvC,CAAS,UACTwC,CAAQ,SACRtC,EAAU,SAAS,SACnBuC,CAAO,CACPC,aAAW,YACXC,EAAaC,EAAAA,CAAI,CACjB,GAAGnD,EACJ,CAAGoD,CAAAA,EAAAA,EAAAA,EAAAA,CAAeA,CAACT,EAAmB,CACrCC,KAAM,SACR,GACMS,EAASf,CAAAA,EAAAA,EAAAA,EAAAA,CAAkBA,CAACF,EAAU,SACtCkB,EAAcC,CAAAA,EAAAA,EAAAA,CAAAA,CAAgBA,CAAClF,IAC/B2E,GACFA,GAAQ,EAAO3E,CADJ,CAGf,GACMmF,GAA4B,IAAfN,EAAsBC,EAAAA,CAAIA,CAAGD,EAC1CO,EAAqBC,CAAAA,EAAAA,EAAAA,CAAb,GAAaA,CAAKA,CAAC,MAAR,CACvBC,KAAM,QACN,GAAI,CAACH,EAAaxD,OAAQ4D,CAAS,CACnCzB,IAAKA,EACL5B,UAAWiC,IAAWjC,EAAW8C,EAAQ5C,GAAW,GAAaA,MAA5C+B,CAAkCa,EAAO,KAAW,OAAR5C,GAAWwC,GAAe,GAAU,OAAPI,EAAO,iBACrGN,SAAU,CAACE,GAA4BV,CAAAA,EAAAA,EAAAA,GAAAA,CAAIA,CAACsB,EAAlB,CAA6BA,CAAE,CACvDC,QADmC,EAEnC,aAAcjB,EACdpC,QAASqC,CACX,GAAIC,EAAS,UAEf,EACoBR,CAAAA,CADhB,CACgBA,EAAAA,GAAAA,CAAIA,CAACiB,EAAY,CACnCO,eAAe,EACf,GAAG/D,CAAK,CACRmC,IAAKyB,OACLI,GAAIpB,EACJG,SAAUU,CACZ,GAPwBb,EAAOa,EAAQ,IAQzC,GACAjD,EAAMwB,WAAW,CAAG,QACpB,MAAeiC,OAAOC,MAAM,CAAC1D,EAAO,CAClCO,KDrCa0B,CCqCPA,CACN0B,ODtCsB1B,CDETR,CCFU,ECuCvB,CAFeQ,CAEd,OFrCwBR,EAAC,GEoCLA", "sources": ["webpack://_N_E/?67e0", "webpack://_N_E/./pages/login.tsx", "webpack://_N_E/./node_modules/react-bootstrap/esm/AlertHeading.js", "webpack://_N_E/./node_modules/react-bootstrap/esm/AlertLink.js", "webpack://_N_E/./node_modules/react-bootstrap/esm/Alert.js"], "sourcesContent": ["\n    (window.__NEXT_P = window.__NEXT_P || []).push([\n      \"/login\",\n      function () {\n        return require(\"private-next-pages/login.tsx\");\n      }\n    ]);\n    if(module.hot) {\n      module.hot.dispose(function () {\n        window.__NEXT_P.push([\"/login\"])\n      });\n    }\n  ", "//Import Library\r\nimport { useEffect, useState } from \"react\";\r\nimport { connect } from \"react-redux\";\r\nimport Router from \"next/router\";\r\nimport toast from 'react-hot-toast';\r\nimport Link from \"next/link\";\r\nimport { Alert } from \"react-bootstrap\";\r\n\r\n//Import services/components\r\nimport { loadLoggedinUserData } from \"../stores/userActions\";\r\nimport authService from \"./../services/authService\";\r\nimport { useTranslation } from 'next-i18next';\r\nimport { serverSideTranslations } from 'next-i18next/serverSideTranslations';\r\n\r\ninterface HomeProps {\r\n  isAdminLogin?: boolean;\r\n  [key: string]: any;\r\n}\r\n\r\nconst Home = (props: HomeProps) => {\r\n    const { t } = useTranslation('common');\r\n      const [contact, setContact] = useState<{ username: any; password: any; rememberMe: boolean }>({\r\n        username: \"\",\r\n        password: \"\",\r\n        rememberMe: false,\r\n    });\r\n    const [, setShow] = useState(false);\r\n\r\n    const handleKeypress = (e: React.KeyboardEvent) => {\r\n        //it triggers by pressing the enter key\r\n        if (e.keyCode === 13) {\r\n            handleSubmit(e);\r\n        }\r\n    };\r\n\r\n    const [erroMessage, setErroMessage] = useState({\r\n        message: \"\",\r\n        display: false,\r\n    });\r\n\r\n    useEffect(() => {\r\n        const rememberMe = localStorage.getItem(\"rememberMe\") === \"true\";\r\n        const user = rememberMe ? localStorage.getItem(\"user\") : \"\";\r\n        const password = rememberMe ? localStorage.getItem(\"password\") : \"\";\r\n        setContact({ username: user, password: password, rememberMe: rememberMe });\r\n    }, []);\r\n\r\n    const RememberhandleChange = (event: any) => {\r\n        const input = event.target;\r\n        const value = input.type === \"checkbox\" ? input.checked : input.value;\r\n        setContact({ ...contact, [input.name]: value });\r\n        setErroMessage({ message: \"\", display: false });\r\n    };\r\n    const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {\r\n        setErroMessage({ message: \"\", display: false });\r\n        setContact({ ...contact, [e.target.name]: e.target.value });\r\n    };\r\n\r\n    const handleSubmit = async (e: React.FormEvent) => {\r\n        e.preventDefault();\r\n        const { auth, logout } = authService;\r\n        contact.username = contact.username.trim();\r\n        const response = await auth(contact);\r\n        if (response && response.status === 201 && response.data && response.data.isEnabled) {\r\n            toast.success(t(\"login.success\"));\r\n\r\n            Router.push(\"/\");\r\n            props.dispatch(loadLoggedinUserData());\r\n        } else if (response.status === 403) {\r\n            await logout();\r\n            setErroMessage({ display: true, message: t(\"login.unauthAccess\") });\r\n        } else {\r\n            setShow(true);\r\n            setContact((prevState) => ({ ...prevState, username: \"\", password: \"\" }));\r\n            if (response.status === 401) {\r\n                setErroMessage({ display: true, message: t(\"login.invalidUserPass\") });\r\n            }\r\n        }\r\n        localStorage.setItem(\"rememberMe\", contact.rememberMe.toString());\r\n        localStorage.setItem(\"user\", contact.rememberMe ? contact.username : \"\");\r\n        localStorage.setItem(\"password\", contact.rememberMe ? contact.password : \"\");\r\n    };\r\n\r\n    return (\r\n        <div className=\"loginContainer\">\r\n            {erroMessage.display ? (\r\n                <Alert key={\"danger\"} variant={\"danger\"}>\r\n                    {erroMessage.message}\r\n                </Alert>\r\n            ) : (\r\n                <></>\r\n            )}\r\n            <div className=\"section\">\r\n                <div className=\"container\">\r\n                    <div className=\"columns\">\r\n                        <div className=\"column  is-two-thirds\">\r\n                            <div className=\"column loginForm\">\r\n                                <div className=\"imgBanner\">\r\n                                    <img src=\"/images/login-banner.jpg\" alt=\"RKI Login Banner Image\" />\r\n                                </div>\r\n                                <form className=\"formContainer\" onSubmit={handleSubmit}>\r\n                                    <div className=\"logoContainer\">\r\n                                        <Link href={\"/\"}>\r\n\r\n                                            <img src=\"/images/logo.jpg\" alt=\"Rohert Koch Institut - Logo\" />\r\n\r\n                                        </Link>\r\n                                    </div>\r\n                                    <div>\r\n                                        <div className=\"mb-3\">\r\n                                            <label className=\"label\">Username or Email</label>\r\n                                            <input\r\n                                                className=\"form-control\"\r\n                                                type=\"text\"\r\n                                                placeholder=\"Enter the Username or Email\"\r\n                                                name=\"username\"\r\n                                                value={contact.username}\r\n                                                onChange={handleChange}\r\n                                                onKeyPress={handleKeypress}\r\n                                                required\r\n                                            />\r\n                                        </div>\r\n                                        <div className=\"mb-3\">\r\n                                            <label className=\"label\">Password</label>\r\n                                            <input\r\n                                                className=\"form-control\"\r\n                                                type=\"password\"\r\n                                                value={contact.password}\r\n                                                placeholder=\"Password\"\r\n                                                name=\"password\"\r\n                                                onChange={handleChange}\r\n                                                onKeyPress={handleKeypress}\r\n                                                required\r\n                                            />\r\n                                        </div>\r\n                                        <div className=\"mb-3 form-check d-flex justify-content-between\">\r\n                                            <label>\r\n                                                <input\r\n                                                    name=\"rememberMe\"\r\n                                                    checked={contact.rememberMe}\r\n                                                    onChange={RememberhandleChange}\r\n                                                    type=\"checkbox\"\r\n                                                />{\" \"}\r\n                                                Remember me\r\n                                            </label>\r\n                                            <Link href=\"/forgot-password\" as=\"/forgot-password\" >\r\n                                                <p className=\"text-primary btn p-0\">Forgot Password?</p>\r\n                                            </Link>\r\n                                        </div>\r\n\r\n                                        <div className=\"field is-grouped\">\r\n                                            <div className=\"control\">\r\n                                                <button className=\"button is-primary\" type=\"submit\">\r\n                                                    Sign in\r\n                                                </button>\r\n                                            </div>\r\n                                        </div>\r\n                                    </div>\r\n                                </form>\r\n                            </div>\r\n                        </div>\r\n                        <div className=\"column\" />\r\n                    </div>\r\n                </div>\r\n            </div>\r\n        </div>\r\n    );\r\n};\r\n\r\nexport async function getStaticProps({ locale }: { locale: string }) {\r\n    return {\r\n        props: {\r\n            ...(await serverSideTranslations(locale, ['common'])),\r\n        },\r\n    }\r\n}\r\n\r\nexport default connect()(Home);\r\n", "\"use client\";\n\nimport * as React from 'react';\nimport classNames from 'classnames';\nimport { useBootstrapPrefix } from './ThemeProvider';\nimport divWithClassName from './divWithClassName';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst DivStyledAsH4 = divWithClassName('h4');\nDivStyledAsH4.displayName = 'DivStyledAsH4';\nconst AlertHeading = /*#__PURE__*/React.forwardRef(({\n  className,\n  bsPrefix,\n  as: Component = DivStyledAsH4,\n  ...props\n}, ref) => {\n  bsPrefix = useBootstrapPrefix(bsPrefix, 'alert-heading');\n  return /*#__PURE__*/_jsx(Component, {\n    ref: ref,\n    className: classNames(className, bsPrefix),\n    ...props\n  });\n});\nAlertHeading.displayName = 'AlertHeading';\nexport default AlertHeading;", "\"use client\";\n\nimport * as React from 'react';\nimport classNames from 'classnames';\nimport Anchor from '@restart/ui/Anchor';\nimport { useBootstrapPrefix } from './ThemeProvider';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst AlertLink = /*#__PURE__*/React.forwardRef(({\n  className,\n  bsPrefix,\n  as: Component = Anchor,\n  ...props\n}, ref) => {\n  bsPrefix = useBootstrapPrefix(bsPrefix, 'alert-link');\n  return /*#__PURE__*/_jsx(Component, {\n    ref: ref,\n    className: classNames(className, bsPrefix),\n    ...props\n  });\n});\nAlertLink.displayName = 'AlertLink';\nexport default AlertLink;", "\"use client\";\n\nimport classNames from 'classnames';\nimport * as React from 'react';\nimport { useUncontrolled } from 'uncontrollable';\nimport useEventCallback from '@restart/hooks/useEventCallback';\nimport { useBootstrapPrefix } from './ThemeProvider';\nimport AlertHeading from './AlertHeading';\nimport AlertLink from './AlertLink';\nimport Fade from './Fade';\nimport CloseButton from './CloseButton';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nimport { jsxs as _jsxs } from \"react/jsx-runtime\";\nconst Alert = /*#__PURE__*/React.forwardRef((uncontrolledProps, ref) => {\n  const {\n    bsPrefix,\n    show = true,\n    closeLabel = 'Close alert',\n    closeVariant,\n    className,\n    children,\n    variant = 'primary',\n    onClose,\n    dismissible,\n    transition = Fade,\n    ...props\n  } = useUncontrolled(uncontrolledProps, {\n    show: 'onClose'\n  });\n  const prefix = useBootstrapPrefix(bsPrefix, 'alert');\n  const handleClose = useEventCallback(e => {\n    if (onClose) {\n      onClose(false, e);\n    }\n  });\n  const Transition = transition === true ? Fade : transition;\n  const alert = /*#__PURE__*/_jsxs(\"div\", {\n    role: \"alert\",\n    ...(!Transition ? props : undefined),\n    ref: ref,\n    className: classNames(className, prefix, variant && `${prefix}-${variant}`, dismissible && `${prefix}-dismissible`),\n    children: [dismissible && /*#__PURE__*/_jsx(CloseButton, {\n      onClick: handleClose,\n      \"aria-label\": closeLabel,\n      variant: closeVariant\n    }), children]\n  });\n  if (!Transition) return show ? alert : null;\n  return /*#__PURE__*/_jsx(Transition, {\n    unmountOnExit: true,\n    ...props,\n    ref: undefined,\n    in: show,\n    children: alert\n  });\n});\nAlert.displayName = 'Alert';\nexport default Object.assign(Alert, {\n  Link: AlertLink,\n  Heading: AlertHeading\n});"], "names": ["connect", "Home", "t", "useTranslation", "contact", "setContact", "useState", "username", "password", "rememberMe", "setShow", "handleKeypress", "e", "keyCode", "handleSubmit", "erroMessage", "setErroMessage", "message", "display", "useEffect", "localStorage", "getItem", "user", "handleChange", "target", "name", "value", "preventDefault", "auth", "logout", "authService", "trim", "response", "status", "data", "isEnabled", "toast", "success", "Router", "props", "dispatch", "loadLoggedinUserData", "prevState", "setItem", "toString", "div", "className", "<PERSON><PERSON>", "variant", "img", "src", "alt", "form", "onSubmit", "Link", "href", "label", "input", "type", "placeholder", "onChange", "onKeyPress", "required", "checked", "RememberhandleChange", "event", "as", "p", "button", "DivStyledAsH4", "divWithClassName", "displayName", "AlertHeading", "React", "ref", "bsPrefix", "Component", "useBootstrapPrefix", "_jsx", "classNames", "AlertLink", "<PERSON><PERSON>", "uncontrolledProps", "show", "<PERSON><PERSON><PERSON><PERSON>", "closeVariant", "children", "onClose", "dismissible", "transition", "Fade", "useUncontrolled", "prefix", "handleClose", "useEventCallback", "Transition", "alert", "_jsxs", "role", "undefined", "CloseButton", "onClick", "unmountOnExit", "in", "Object", "assign", "Heading"], "sourceRoot": "", "ignoreList": [2, 3, 4]}