{"version": 3, "file": "static/chunks/pages/dashboard/AnnouncementItem-ca79264cc65a7473.js", "mappings": "wKAiBe,SAASA,EAAiBC,CAA4B,EAEnE,GAAM,MAACC,CAAI,CAAC,CAAGD,EASTE,EAAYD,EAAKE,MAAM,CAACC,MAAM,CAAE,EAChCC,EAAS,UAAoB,OAAVJ,EAAKK,IAAI,EAElC,MACE,WAACC,EAAAA,CAAGA,CAAAA,CAACC,UAAU,MAAMC,GAAI,aACvB,UAACC,IAAIA,CAACC,KAAM,IAAc,OAAVV,EAAKK,IAAI,CAAC,gBAAeM,GAAI,EAAxCF,EAA8DT,MAAAA,CAAlBA,EAAKK,IAAI,CAAC,UAA+BL,MAAAA,CAAvBA,CAAI,CAACI,EAAO,CAAC,YAAmB,OAATJ,EAAKY,GAAG,WAE/F,EAAMV,MAAM,EAAIF,EAAKE,MAAM,CAACD,EAAS,CACpC,UAACY,MAAAA,CAAIC,IAAK,GAAwCd,MAAAA,CAArCe,8BAAsB,CAAC,gBAAwC,OAA1Bf,EAAKE,MAAM,CAACD,EAAS,CAACW,GAAG,EAAII,IAAI,eAC9ET,UAAU,gBACb,UAACU,IAAAA,CAAEV,UAAU,iCAGnB,WAACW,MAAAA,CAAIX,UAAU,yBACb,UAACE,IAAIA,CAACC,KAAM,IAAc,OAAVV,EAAKK,IAAI,CAAC,gBAAeM,GAAI,EAAxCF,EAA8DT,MAAAA,CAAlBA,EAAKK,IAAI,CAAC,UAA+BL,MAAAA,CAAvBA,CAAI,CAACI,EAAO,CAAC,YAAmB,OAATJ,EAAKY,GAAG,WAC/FZ,GAAQA,EAAKmB,KAAK,CAAGnB,EAAKmB,KAAK,CAAG,KAErC,UAACC,IAAAA,UACEpB,GAAQA,EAAKqB,WAAW,CAAGC,CAzBX,IACvB,IAAMJ,EAAMK,SAASC,aAAa,CAAC,OACnCN,EAAIO,SAAS,CAAGC,EAChB,IAAMC,EAAST,EAAIU,WAAW,EAAIV,EAAIW,SAAS,EAAI,GACnD,OAAQF,EAAOxB,MAAM,CArBF,EAqBK2B,EAAiB,GAA2C,OAAxCH,EAAOI,SAAS,CAAC,EAAGD,KAAoB,OAAOH,CAC7F,GAoBqD3B,CArB8B,CAqBzBqB,WAAW,EAAI,YAK3E,mBClDA,4CACA,8BACA,WACA,OAAe,EAAQ,KAAmD,CAC1E,EACA,SAFsB", "sources": ["webpack://_N_E/./pages/dashboard/AnnouncementItem.tsx", "webpack://_N_E/?9019"], "sourcesContent": ["//Import Library\r\nimport {Col} from \"react-bootstrap\";\r\nimport <PERSON> from \"next/link\";\r\n\r\nconst truncateLength = 260;\r\n\r\n//TODO: Remove the maths random number for image after updates completed with image upload\r\ninterface AnnouncementItemProps {\r\n  item: {\r\n    _id: string;\r\n    title: string;\r\n    description: string;\r\n    type: string;\r\n    [key: string]: any;\r\n  };\r\n}\r\n\r\nexport default function AnnouncementItem(props: AnnouncementItemProps) {\r\n\r\n  const {item} = props;\r\n\r\n  const getTrimmedString = (html: string) => {\r\n    const div = document.createElement(\"div\");\r\n    div.innerHTML = html;\r\n    const string = div.textContent || div.innerText || \"\";\r\n    return (string.length > truncateLength ? `${string.substring(0, truncateLength - 3)}...` : string);\r\n  }\r\n\r\n  const indexNew =  item.images.length -1;\r\n  const parent = `parent_${item.type}`;\r\n\r\n  return (\r\n    <Col className=\"p-0\" xs={12}>\r\n      <Link href={`/${item.type}/[...routes]`} as={`/${item.type}/show/${item[parent]}/update/${item._id}`}>\r\n\r\n        {(item.images && item.images[indexNew]) ?\r\n          <img src={`${process.env.API_SERVER}/image/show/${item.images[indexNew]._id}`} alt=\"announcement\"\r\n               className=\"announceImg\"/>\r\n          : <i className=\"fa fa-bullhorn announceImg\"/>}\r\n\r\n      </Link>\r\n      <div className=\"announceDesc\">\r\n        <Link href={`/${item.type}/[...routes]`} as={`/${item.type}/show/${item[parent]}/update/${item._id}`}>\r\n          {item && item.title ? item.title : ''}\r\n        </Link>\r\n        <p>\r\n          {item && item.description ? getTrimmedString(item.description) : null}\r\n        </p>\r\n      </div>\r\n    </Col>\r\n  );\r\n}", "\n    (window.__NEXT_P = window.__NEXT_P || []).push([\n      \"/dashboard/AnnouncementItem\",\n      function () {\n        return require(\"private-next-pages/dashboard/AnnouncementItem.tsx\");\n      }\n    ]);\n    if(module.hot) {\n      module.hot.dispose(function () {\n        window.__NEXT_P.push([\"/dashboard/AnnouncementItem\"])\n      });\n    }\n  "], "names": ["AnnouncementItem", "props", "item", "indexNew", "images", "length", "parent", "type", "Col", "className", "xs", "Link", "href", "as", "_id", "img", "src", "process", "alt", "i", "div", "title", "p", "description", "getTrimmedString", "document", "createElement", "innerHTML", "html", "string", "textContent", "innerText", "truncate<PERSON><PERSON>th", "substring"], "sourceRoot": "", "ignoreList": []}