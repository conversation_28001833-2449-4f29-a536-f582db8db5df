{"version": 3, "file": "static/chunks/pages/hazard/HazardOperation-4667993e73133294.js", "mappings": "uKAMA,IAAMA,EAAwBC,EAAAA,OAAb,GAA6B,CAAC,GAK5CC,IALyB,IAAoB,WAC9CC,CAAS,UACTC,CAAQ,CACRC,GAAIC,EAAY,KAAK,CACrB,GAAGC,EACJ,GAEC,OADAH,EAAWI,CAAAA,EAAAA,EAAAA,EAAAA,CAAkBA,CAACJ,EAAU,aACpBK,CAAAA,EAAAA,EAAAA,GAAAA,CAAIA,CAACH,EAAW,CAClCJ,IAAKA,EACLC,UAAWO,IAAWP,EAAWC,GACjC,GAAGG,CAAK,EAEZ,GACAP,EAASW,WAAW,CAAG,WCbvB,IAAMC,EAA0BX,EAAAA,SAAb,CAA6B,CAAC,GAK9CC,MAL2B,EAAoB,WAChDC,CAAS,UACTC,CAAQ,CACRC,GAAIC,EAAY,KAAK,CACrB,GAAGC,EACJ,GAEC,OADAH,EAAWI,CAAAA,EAAAA,EAAAA,EAAAA,CAAkBA,CACX,EADsB,eACpBC,CAAAA,EAAAA,EAAAA,GAAAA,CAAIA,CAACH,EAAW,CAClCJ,IAAKA,EACLC,UAAWO,IAAWP,EAAWC,GACjC,GAAGG,CAAK,EAEZ,GACAK,EAJyBF,WAIH,CAAG,4BCXzB,IAAMG,EAA0BZ,EAAAA,SAAb,CAA6B,CAAC,GAM9CC,MAN2B,EAAoB,UAChDE,CAAQ,WACRD,CAAS,CAETE,CADA,EACIC,EAAY,KAAK,CACrB,GAAGC,EACJ,GACOO,EAASN,CAAAA,EAAAA,EAAAA,EAAAA,CAAkBA,CAACJ,EAAU,eACtCW,EAAeC,CAAAA,EAAAA,EAAAA,OAAAA,CAAOA,CAAC,IAAO,EAClCC,mBAAoBH,EACtB,EAAI,CAACA,EAAO,EACZ,MAAoBL,CAAb,EAAaA,EAAAA,GAAAA,CAAIA,CAACS,EAAAA,CAAiBA,CAACC,QAAQ,CAAE,CACnDC,MAAOL,EACPM,SAAuBZ,CAAb,EAAaA,EAAAA,GAAAA,CAAIA,CAACH,EAAW,CACrCJ,IAAKA,EACL,GAAGK,CAAK,CACRJ,UAAWO,IAAWP,EAAWW,EACnC,EACF,EACF,EACAD,GAAWF,GAJgBD,QAIL,CAAG,aCtBzB,IAAMY,EAAuBrB,EAAAA,MAAb,IAA6B,CAC7C,CACA,EAMGC,GARwB,KAE1B,CACCE,UAAQ,WACRD,CAAS,SACToB,CAAO,CACPlB,GAAIC,EAAY,KAAK,CACrB,GAAGC,EACJ,GACOO,EAASN,CAAAA,EAAAA,EAAAA,EAAAA,CAAkBA,CAACJ,EAAU,YAC5C,MAAoBK,CAAb,EAAaA,EAAAA,GAAAA,CAAIA,CAACH,EAAP,CAChBJ,IAAKA,EACLC,UAAWO,IAAWa,EAAU,GAAaA,MAAAA,CAAVT,EAAO,EAArBJ,GAAgC,OAARa,CAX0G,EAW9FT,EAAQX,GACjE,GAAGI,CAAK,EAEZ,GACAe,EAAQX,WAAW,CAAG,UChBtB,IAAMa,EAA8BvB,EAAAA,UAAgB,CAAC,EAA9B,CAKpBC,QALmD,EAApB,SAChCC,CAAS,UACTC,CAAQ,CACRC,GAAIC,EAAY,KAAK,CACrB,GAAGC,EACJ,GAEC,OADAH,EAAWI,CAAAA,EAAAA,EAAAA,EAAAA,CAAkBA,CAACJ,EAAU,oBACpBK,CAAAA,EAAAA,EAAAA,GAAAA,CAAIA,CAACH,EAAW,CAClCJ,IAAKA,EACLC,UAAWO,IAAWP,EAAWC,GACjC,GAAGG,CAAK,EAEZ,EACAiB,GAJyBd,WAIC,CAAG,iBCb7B,IAAMe,EAAwBxB,EAAAA,OAAb,GAA6B,CAAC,GAK5CC,IALyB,IAAoB,WAC9CC,CAAS,UACTC,CAAQ,CACRC,GAAIC,EAAY,GAAG,CACnB,GAAGC,EACJ,GAEC,OADAH,EAAWI,CAAAA,EAAAA,EAAAA,EAAAA,CAAkBA,CACX,EADsB,aACpBC,CAAAA,EAAAA,EAAAA,GAAAA,CAAIA,CAACH,EAAW,CAClCJ,IAAKA,EACLC,UAAWO,IAAWP,EAAWC,GACjC,GAAGG,CAAK,EAEZ,GACAkB,EAJyBf,WAIL,CAAG,0BCZvB,IAAMgB,EAAgBC,CAAAA,EAAAA,EAAAA,CAAAA,CAAgBA,CAAC,MACjCC,EAA4B3B,EAAAA,UAAgB,CAA7B,CAA8B,EAKhDC,QAL6B,WAC9BC,CAAS,UACTC,CAAQ,CACRC,GAAIC,EAAYoB,CAAa,CAC7B,GAAGnB,EACJ,GAEC,OADAH,EAAWI,CAAAA,EAAAA,EAAAA,EAAAA,CAAkBA,CAACJ,EAAU,iBACpBK,CAAAA,EAAAA,EAAAA,GAAAA,CAAIA,CAACH,EAAW,CAClCJ,IAAKA,EACLC,UAAWO,IAAWP,EAAWC,GACjC,GAAGG,CAAK,EAEZ,GACAqB,EAJyBlB,WAID,CAAG,eCf3B,IAAMmB,EAAwB5B,EAAAA,OAAb,GAA6B,CAAC,GAK5CC,IALyB,IAAoB,CAC9CC,WAAS,UACTC,CAAQ,CACRC,GAAIC,EAAY,GAAG,CACnB,GAAGC,EACJ,GAEC,OADAH,EAAWI,CAAAA,EAAAA,EAAAA,EAAAA,CAAkBA,CAACJ,EAAU,aACpBK,CAAAA,EAAAA,EAAAA,GAAAA,CAAIA,CAACH,EAAW,CAClCJ,IAAKA,EACLC,UAAWO,IAAWP,EAAWC,GACjC,GAAGG,CAAK,EAEZ,GACAsB,EAJyBnB,WAIL,CAAG,WCZvB,IAAMoB,EAAgBH,CAAAA,EAAAA,EAAAA,CAAAA,CAAgBA,CAAC,MACjCI,EAAyB9B,EAAAA,QAAb,EAA6B,CAAC,GAK7CC,KAL0B,GAAoB,CAC/CC,WAAS,UACTC,CAAQ,CACRC,GAAIC,EAAYwB,CAAa,CAC7B,GAAGvB,EACJ,GAEC,OADAH,EAAWI,CAAAA,EAAAA,EAAAA,EAAAA,CAAkBA,CAACJ,EAAU,cACpBK,CAAAA,EAAAA,EAAAA,GAAAA,CAAIA,CAACH,EAAW,CAClCJ,IAAKA,EACLC,UAAWO,IAAWP,EAAWC,GACjC,GAAGG,CAAK,EAEZ,EACAwB,GAJyBrB,WAIJ,CAAG,YCNxB,IAAMsB,EAAoB/B,EAAAA,GAAb,OAA6B,CAAC,GAAnB,QAAoB,UAC1CG,CAAQ,WACRD,CAAS,IACT8B,CAAE,CACFC,MAAI,CACJC,QAAM,MACNC,GAAO,CAAK,UACZf,CAAQ,CAERhB,CADA,EACIC,EAAY,KAAK,CACrB,GAAGC,EACJ,GACOO,EAASN,CAAAA,EAAAA,EAAAA,EAAAA,CAAkBA,CAACJ,EAAU,QAC5C,MAAoBK,CAAb,EAAaA,EAAAA,GAAAA,CAAIA,CAACH,EAAP,CAChBJ,IAAKA,EACL,GAAGK,CAAK,CACRJ,UAAWO,IAAWP,EAAWW,EAAQmB,GAAM,MAAS,GAAnCvB,GAAmC,CAAHuB,GAAMC,GAAQ,QAAa,OAALA,GAAQC,GAAU,UAAiB,OAAPA,IACvGd,IATyJ,KAS/Ie,EAAoB3B,CAAAA,EAAAA,EAAb,GAAaA,CAAIA,CAACT,EAAU,CAC3CqB,GAD0B,MAAerB,CAE3C,GAAKqB,CACP,EACF,EACAW,GAAKrB,WAAW,CAAG,OACnB,MAAe0B,OAAOC,MAAM,CAACN,EAAM,CACjCO,INhBajB,CMgBRA,CACLkB,KNjBoBlB,CKDPS,CLCQ,CMkBrBU,EAFYnB,KDjBUS,EAAC,CCmBbH,CACVc,CAFgBX,ITpBH/B,CSsBPA,CACN2C,GHrByBf,EAAC,CNFL5B,CSwBrB4C,CTxBsB,GSsBR5C,CFtBD6B,CFAQJ,CIyBrBoB,CJzBsB,GIuBRpB,EFvBOI,CLSRhB,CKTS,CE0BtBiC,EAFcjB,KRxBDjB,CCSUC,COkBvBkC,CPlBwB,GOgBNlC,IRzBKD,EAAC,CGAXY,CK2BDA,CADMZ,CAElB,EAAC,SL5B0BY,EAAC,GK2BFA,sGCK5B,MAxCwB,IACpB,IAAIwB,EAAsBzC,EAAMyC,gBAuCrBC,GAvCwC,CAC7C,GAAEC,CAAC,CAAE,CAAGC,CAAAA,EAAAA,EAAAA,EAAAA,CAAcA,CAAC,UAC7B,MACI,+BACI,UAACC,MAAAA,CAAIjD,UAAU,6BACX,WAAC6B,EAAAA,CAAIA,CAAAA,CAAC7B,UAAU,qBACZ,UAAC6B,EAAAA,CAAIA,CAACa,MAAM,EAAC1C,UAAU,uBACtB+C,EAAE,kCAGH,UAAClB,EAAAA,CAAIA,CAACU,IAAI,EAACvC,UAAU,sBACpB6C,GAAuBA,EAAoBK,MAAM,CAAG,EACjDL,EAAoBM,GAAG,CAAC,CAACC,EAAMC,IAC/B,UAACC,KAAAA,CAAGtD,UAAU,mBACV,WAACuD,KAAAA,CAAevD,UAAU,oBAC1B,UAACwC,IAAIA,CAEDgB,KAAK,yBACLtD,GAAI,WAHHsC,QAG+B,OAATY,EAAKK,GAAG,WAE9BL,GAAQA,EAAKM,KAAK,CAAG,GAAc,OAAXN,EAAKM,KAAK,EAAK,IAJnCN,EAAKK,GAAG,EAMjB,WAACE,OAAAA,WACI,IAAI,IACHP,GAAQA,EAAKQ,OAAO,CAAG,GAAsB,OAAnBR,EAAKQ,OAAO,CAACF,KAAK,EAAK,GAAG,SAVjDL,MAgBb,UAACM,OAAAA,CAAK3D,UAAU,uBAAe+C,EAAE,2BAOzD,iDC/CA,IAAMc,EAAuB/D,QAAb,CAAaA,UAAF,GAAqB,CAAC,MACjD+D,EAAQrD,WAAW,CAAG,oBACtB,MAAeqD,OAAOA,EAAC,UCJvB,4CACA,0BACA,WACA,OAAe,EAAQ,KAA+C,CACtE,EACA,SAFsB", "sources": ["webpack://_N_E/./node_modules/react-bootstrap/esm/CardBody.js", "webpack://_N_E/./node_modules/react-bootstrap/esm/CardFooter.js", "webpack://_N_E/./node_modules/react-bootstrap/esm/CardHeader.js", "webpack://_N_E/./node_modules/react-bootstrap/esm/CardImg.js", "webpack://_N_E/./node_modules/react-bootstrap/esm/CardImgOverlay.js", "webpack://_N_E/./node_modules/react-bootstrap/esm/CardLink.js", "webpack://_N_E/./node_modules/react-bootstrap/esm/CardSubtitle.js", "webpack://_N_E/./node_modules/react-bootstrap/esm/CardText.js", "webpack://_N_E/./node_modules/react-bootstrap/esm/CardTitle.js", "webpack://_N_E/./node_modules/react-bootstrap/esm/Card.js", "webpack://_N_E/./pages/hazard/HazardOperation.tsx", "webpack://_N_E/./node_modules/react-bootstrap/esm/CardHeaderContext.js", "webpack://_N_E/?5e75"], "sourcesContent": ["\"use client\";\n\nimport * as React from 'react';\nimport classNames from 'classnames';\nimport { useBootstrapPrefix } from './ThemeProvider';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst CardBody = /*#__PURE__*/React.forwardRef(({\n  className,\n  bsPrefix,\n  as: Component = 'div',\n  ...props\n}, ref) => {\n  bsPrefix = useBootstrapPrefix(bsPrefix, 'card-body');\n  return /*#__PURE__*/_jsx(Component, {\n    ref: ref,\n    className: classNames(className, bsPrefix),\n    ...props\n  });\n});\nCardBody.displayName = 'CardBody';\nexport default CardBody;", "\"use client\";\n\nimport * as React from 'react';\nimport classNames from 'classnames';\nimport { useBootstrapPrefix } from './ThemeProvider';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst CardFooter = /*#__PURE__*/React.forwardRef(({\n  className,\n  bsPrefix,\n  as: Component = 'div',\n  ...props\n}, ref) => {\n  bsPrefix = useBootstrapPrefix(bsPrefix, 'card-footer');\n  return /*#__PURE__*/_jsx(Component, {\n    ref: ref,\n    className: classNames(className, bsPrefix),\n    ...props\n  });\n});\nCardFooter.displayName = 'CardFooter';\nexport default CardFooter;", "\"use client\";\n\nimport classNames from 'classnames';\nimport * as React from 'react';\nimport { useMemo } from 'react';\nimport { useBootstrapPrefix } from './ThemeProvider';\nimport CardHeaderContext from './CardHeaderContext';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst CardHeader = /*#__PURE__*/React.forwardRef(({\n  bsPrefix,\n  className,\n  // Need to define the default \"as\" during prop destructuring to be compatible with styled-components github.com/react-bootstrap/react-bootstrap/issues/3595\n  as: Component = 'div',\n  ...props\n}, ref) => {\n  const prefix = useBootstrapPrefix(bsPrefix, 'card-header');\n  const contextValue = useMemo(() => ({\n    cardHeaderBsPrefix: prefix\n  }), [prefix]);\n  return /*#__PURE__*/_jsx(CardHeaderContext.Provider, {\n    value: contextValue,\n    children: /*#__PURE__*/_jsx(Component, {\n      ref: ref,\n      ...props,\n      className: classNames(className, prefix)\n    })\n  });\n});\nCardHeader.displayName = 'CardHeader';\nexport default CardHeader;", "\"use client\";\n\nimport classNames from 'classnames';\nimport * as React from 'react';\nimport { useBootstrapPrefix } from './ThemeProvider';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst CardImg = /*#__PURE__*/React.forwardRef(\n// Need to define the default \"as\" during prop destructuring to be compatible with styled-components github.com/react-bootstrap/react-bootstrap/issues/3595\n({\n  bsPrefix,\n  className,\n  variant,\n  as: Component = 'img',\n  ...props\n}, ref) => {\n  const prefix = useBootstrapPrefix(bsPrefix, 'card-img');\n  return /*#__PURE__*/_jsx(Component, {\n    ref: ref,\n    className: classNames(variant ? `${prefix}-${variant}` : prefix, className),\n    ...props\n  });\n});\nCardImg.displayName = 'CardImg';\nexport default CardImg;", "\"use client\";\n\nimport * as React from 'react';\nimport classNames from 'classnames';\nimport { useBootstrapPrefix } from './ThemeProvider';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst CardImgOverlay = /*#__PURE__*/React.forwardRef(({\n  className,\n  bsPrefix,\n  as: Component = 'div',\n  ...props\n}, ref) => {\n  bsPrefix = useBootstrapPrefix(bsPrefix, 'card-img-overlay');\n  return /*#__PURE__*/_jsx(Component, {\n    ref: ref,\n    className: classNames(className, bsPrefix),\n    ...props\n  });\n});\nCardImgOverlay.displayName = 'CardImgOverlay';\nexport default CardImgOverlay;", "\"use client\";\n\nimport * as React from 'react';\nimport classNames from 'classnames';\nimport { useBootstrapPrefix } from './ThemeProvider';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst CardLink = /*#__PURE__*/React.forwardRef(({\n  className,\n  bsPrefix,\n  as: Component = 'a',\n  ...props\n}, ref) => {\n  bsPrefix = useBootstrapPrefix(bsPrefix, 'card-link');\n  return /*#__PURE__*/_jsx(Component, {\n    ref: ref,\n    className: classNames(className, bsPrefix),\n    ...props\n  });\n});\nCardLink.displayName = 'CardLink';\nexport default CardLink;", "\"use client\";\n\nimport * as React from 'react';\nimport classNames from 'classnames';\nimport { useBootstrapPrefix } from './ThemeProvider';\nimport divWithClassName from './divWithClassName';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst DivStyledAsH6 = divWithClassName('h6');\nconst CardSubtitle = /*#__PURE__*/React.forwardRef(({\n  className,\n  bsPrefix,\n  as: Component = DivStyledAsH6,\n  ...props\n}, ref) => {\n  bsPrefix = useBootstrapPrefix(bsPrefix, 'card-subtitle');\n  return /*#__PURE__*/_jsx(Component, {\n    ref: ref,\n    className: classNames(className, bsPrefix),\n    ...props\n  });\n});\nCardSubtitle.displayName = 'CardSubtitle';\nexport default CardSubtitle;", "\"use client\";\n\nimport * as React from 'react';\nimport classNames from 'classnames';\nimport { useBootstrapPrefix } from './ThemeProvider';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst CardText = /*#__PURE__*/React.forwardRef(({\n  className,\n  bsPrefix,\n  as: Component = 'p',\n  ...props\n}, ref) => {\n  bsPrefix = useBootstrapPrefix(bsPrefix, 'card-text');\n  return /*#__PURE__*/_jsx(Component, {\n    ref: ref,\n    className: classNames(className, bsPrefix),\n    ...props\n  });\n});\nCardText.displayName = 'CardText';\nexport default CardText;", "\"use client\";\n\nimport * as React from 'react';\nimport classNames from 'classnames';\nimport { useBootstrapPrefix } from './ThemeProvider';\nimport divWithClassName from './divWithClassName';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst DivStyledAsH5 = divWithClassName('h5');\nconst CardTitle = /*#__PURE__*/React.forwardRef(({\n  className,\n  bsPrefix,\n  as: Component = DivStyledAsH5,\n  ...props\n}, ref) => {\n  bsPrefix = useBootstrapPrefix(bsPrefix, 'card-title');\n  return /*#__PURE__*/_jsx(Component, {\n    ref: ref,\n    className: classNames(className, bsPrefix),\n    ...props\n  });\n});\nCardTitle.displayName = 'CardTitle';\nexport default CardTitle;", "\"use client\";\n\nimport classNames from 'classnames';\nimport * as React from 'react';\nimport { useBootstrapPrefix } from './ThemeProvider';\nimport CardBody from './CardBody';\nimport CardFooter from './CardFooter';\nimport CardHeader from './CardHeader';\nimport CardImg from './CardImg';\nimport CardImgOverlay from './CardImgOverlay';\nimport CardLink from './CardLink';\nimport CardSubtitle from './CardSubtitle';\nimport CardText from './CardText';\nimport CardTitle from './CardTitle';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst Card = /*#__PURE__*/React.forwardRef(({\n  bsPrefix,\n  className,\n  bg,\n  text,\n  border,\n  body = false,\n  children,\n  // Need to define the default \"as\" during prop destructuring to be compatible with styled-components github.com/react-bootstrap/react-bootstrap/issues/3595\n  as: Component = 'div',\n  ...props\n}, ref) => {\n  const prefix = useBootstrapPrefix(bsPrefix, 'card');\n  return /*#__PURE__*/_jsx(Component, {\n    ref: ref,\n    ...props,\n    className: classNames(className, prefix, bg && `bg-${bg}`, text && `text-${text}`, border && `border-${border}`),\n    children: body ? /*#__PURE__*/_jsx(CardBody, {\n      children: children\n    }) : children\n  });\n});\nCard.displayName = 'Card';\nexport default Object.assign(Card, {\n  Img: CardImg,\n  Title: CardTitle,\n  Subtitle: CardSubtitle,\n  Body: CardBody,\n  Link: CardLink,\n  Text: CardText,\n  Header: CardHeader,\n  Footer: CardFooter,\n  ImgOverlay: CardImgOverlay\n});", "//Import Library\r\nimport Link from \"next/link\";\r\nimport { Card } from \"react-bootstrap\";\r\n\r\n//Import services/components\r\nimport { useTranslation } from 'next-i18next';\r\n\r\ninterface HazardOperationProps {\r\n  t: (key: string) => string;\r\n  hazardOperationData: any[];\r\n}\r\n\r\nconst HazardOperation = (props: HazardOperationProps) => {\r\n    let hazardOperationData = props.hazardOperationData;\r\n    const { t } = useTranslation('common');\r\n    return (\r\n        <>\r\n            <div className=\"rki-carousel-card\">\r\n                <Card className=\"infoCard\">\r\n                    <Card.Header className=\"text-center\">\r\n                    {t(\"hazardshow.currentoperations\")}\r\n                    </Card.Header>\r\n\r\n                    <Card.Body className=\"hazardBody\">\r\n                    {hazardOperationData && hazardOperationData.length > 0 ? (\r\n                        hazardOperationData.map((item, index) => (\r\n                        <ul className=\"ulItems\">\r\n                            <li key={index} className=\"liItems\">\r\n                            <Link\r\n                                key={item._id}\r\n                                href=\"/operation/[...routes]\"\r\n                                as={`/operation/show/${item._id}`}\r\n                            >\r\n                                {item && item.title ? `${item.title}` : \"\"}\r\n                            </Link>\r\n                            <span>\r\n                                {\" \"}\r\n                                ({item && item.country ? `${item.country.title}` : \"\"})\r\n                            </span>\r\n                            </li>\r\n                        </ul>\r\n                        ))\r\n                    ) : (\r\n                        <span className=\"text-center\">{t(\"noSourceFound\")}</span>\r\n                    )}\r\n                    </Card.Body>\r\n                </Card>\r\n            </div>\r\n        </>\r\n    );\r\n};\r\n\r\nexport default HazardOperation;", "\"use client\";\n\nimport * as React from 'react';\nconst context = /*#__PURE__*/React.createContext(null);\ncontext.displayName = 'CardHeaderContext';\nexport default context;", "\n    (window.__NEXT_P = window.__NEXT_P || []).push([\n      \"/hazard/HazardOperation\",\n      function () {\n        return require(\"private-next-pages/hazard/HazardOperation.tsx\");\n      }\n    ]);\n    if(module.hot) {\n      module.hot.dispose(function () {\n        window.__NEXT_P.push([\"/hazard/HazardOperation\"])\n      });\n    }\n  "], "names": ["CardBody", "React", "ref", "className", "bsPrefix", "as", "Component", "props", "useBootstrapPrefix", "_jsx", "classNames", "displayName", "<PERSON><PERSON><PERSON>er", "<PERSON><PERSON><PERSON><PERSON>", "prefix", "contextValue", "useMemo", "cardHeaderBsPrefix", "CardHeaderContext", "Provider", "value", "children", "CardImg", "variant", "CardImgOverlay", "CardLink", "DivStyledAsH6", "divWithClassName", "CardSubtitle", "CardText", "DivStyledAsH5", "CardTitle", "Card", "bg", "text", "border", "body", "Object", "assign", "Img", "Title", "Subtitle", "Body", "Link", "Text", "Header", "Footer", "ImgOverlay", "hazardOperationData", "HazardOperation", "t", "useTranslation", "div", "length", "map", "item", "index", "ul", "li", "href", "_id", "title", "span", "country", "context"], "sourceRoot": "", "ignoreList": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 11]}