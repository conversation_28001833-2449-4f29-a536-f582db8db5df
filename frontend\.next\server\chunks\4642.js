"use strict";exports.id=4642,exports.ids=[4642],exports.modules={54642:(e,t,a)=>{a.a(e,async(e,r)=>{try{a.r(t),a.d(t,{default:()=>q});var s=a(8732),l=a(82015),n=a.n(l),i=a(19918),o=a.n(i),d=a(27825),u=a.n(d),c=a(81181),y=a(63241),h=a(44233),p=a(56084),g=a(89860),m=a(63487),x=a(88751),_=e([g,m]);[g,m]=_.then?(await _)():_;let q=function(e){let t=(0,h.useRouter)(),{setInstitutions:a,selectedRegions:r}=e,[i,d]=n().useState(""),[_,q]=n().useState([]),[j,w]=n().useState(""),[b,A]=n().useState(!1),[S,f]=(0,l.useState)([]),[N,v]=(0,l.useState)(!1),[k,C]=(0,l.useState)(0),[T,P]=(0,l.useState)(10),[R,z]=(0,l.useState)(1),[H,O]=(0,l.useState)(null),{t:I}=(0,x.useTranslation)("common"),[M,$]=(0,l.useState)(0),E={sort:{created_at:"desc"},limit:T,page:1,query:{status:{$not:{$eq:"Request Pending"}}},select:"-contact_name -description -expertisem -hazard_types -hazards -address.region -focal_points -website -telephone -twitter -header -use_default_header -images -email -user -created_at -updated_at -primary_focal_point -partners"},[B,G]=(0,l.useState)(E),F=(0,s.jsxs)(c.A,{id:"popover-basic",children:[(0,s.jsx)(c.A.Header,{as:"h3",className:"text-center",children:"NETWORKS"}),(0,s.jsx)(c.A.Body,{children:(0,s.jsxs)("ul",{children:[(0,s.jsxs)("li",{children:[(0,s.jsx)("b",{children:"EMLab"})," - European Mobile Lab"]}),(0,s.jsxs)("li",{children:[(0,s.jsx)("b",{children:"EMT"})," -Emergency Medical Teams"]}),(0,s.jsxs)("li",{children:[(0,s.jsx)("b",{children:"GHPP"})," - Global Health Protection Program"]}),(0,s.jsxs)("li",{children:[(0,s.jsx)("b",{children:"GOARN"})," - Global Outbreak Alert & Response Network"]}),(0,s.jsxs)("li",{children:[(0,s.jsx)("b",{children:"IANPHI"})," - International Association of National Public Health Institutes"]}),(0,s.jsxs)("li",{children:[(0,s.jsx)("b",{children:"STAKOB"})," - St\xe4ndiger Arbeitskreis der Kompetenz-und Behandlungszentren"]}),(0,s.jsxs)("li",{children:[(0,s.jsx)("b",{children:"WHOCC"}),"- World Health Organization Collaborating Centres"]})]})})]}),K=(0,s.jsx)(y.A,{trigger:"click",placement:"right",overlay:F,children:(0,s.jsxs)("span",{children:[I("Network"),"\xa0\xa0\xa0",(0,s.jsx)("i",{className:"fas fa-info-circle",style:{cursor:"pointer"},"aria-hidden":"true"})]})}),W=[{name:I("Organization"),selector:"title",sortable:!0,cell:e=>(0,s.jsx)(o(),{href:"/institution/[...routes]",as:`/institution/show/${e._id}`,children:e.title})},{name:I("Country"),selector:"country",sortable:!0,cell:e=>e.address?.country?.title||""},{name:I("Type"),selector:"type",sortable:!0,cell:e=>e.type?.title||""},{name:K,selector:"title",cell:e=>e.networks?.map(e=>e.title).join(", ")||""}],L=(e,t)=>{let r=[];r=e.filter(e=>"Request Pending"!=e.status);let s=t-r.length;f(r),a(e),C(t-s),$(t)},D=async e=>{v(!0),t.query&&t.query.country&&(e.query["address.country"]=t.query.country),null===r?delete e.query["address.world_region"]:0===r.length?e.query["address.world_region"]="__NO_MATCH__":e.query["address.world_region"]=r;let a=await m.A.get("/institution",e);a&&Array.isArray(a.data)&&L(a.data,a.totalCount),v(!1)},J=async(e,a)=>{v(!0);let s={sort:H?H.sort:{created_at:"desc"},limit:e,page:a,query:{status:{$not:{$eq:"Request Pending"}}},select:"-contact_name -description -expertisem -hazard_types -hazards -address.region -focal_points -website -telephone -twitter -header -use_default_header -images -email -user -created_at -updated_at -primary_focal_point -partners"};t.query&&t.query.country&&(s.query["address.country"]=t.query.country),r&&r.length>0&&(s.query["address.world_region"]=r),""!==i&&(s.query.title=i),j&&(s.query.networks=j);let l=u().map(_,"value");l&&l.length>0&&(s.query.type=l);let n=await m.A.get("/institution",s);n&&Array.isArray(n.data)&&(console.log({data:n.data}),L(n.data,n.totalCount),P(e),v(!1)),z(a)},Q=async(e,t)=>{v(!0),E.sort={[e.selector]:t};let a=u().map(_,"value");_&&_.length>0&&(E.query={...E.query,type:a}),j&&(E.query={...E.query,networks:j}),""!==i&&(E.query={...E.query,title:i}),console.log("Sorting by:",e.selector),await D(E),O(E),v(!1)},U=(e,t)=>{e?(B.query.title=e,B.page=t):delete B.query.title,G({...B})},V=(0,l.useRef)(u().debounce((e,t)=>U(e,t),Number("500")||300)).current,X=n().useMemo(()=>{let e=e=>{w(e),e?(B.query.networks=e,B.page=R):delete B.query.networks,G({...B})};return(0,s.jsx)(g.default,{onFilter:e=>{d(e.target.value),V(e.target.value,R)},onFilterTypeChange:e=>{q([...e]);let t=u().map(e,"value");t&&t.length>0?(B.query.type=t,B.page=R):delete B.query.type,G({...B})},onFilterNetworkChange:t=>e(t.target.value),onClear:()=>{i&&(A(!b),d(""))},filterText:i,filterType:_,filterNetwork:j})},[i,_,b,j,r,R]);return(0,s.jsx)("div",{className:"institution__table",children:(0,s.jsx)(p.A,{columns:W,data:S,totalRows:M,loading:N,subheader:!0,persistTableHead:!0,onSort:Q,sortServer:!0,pagServer:!0,subHeaderComponent:X,handlePerRowsChange:J,handlePageChange:e=>{let t={sort:H?H.sort:{created_at:"desc"},limit:T,page:e,query:{status:{$not:{$eq:"Request Pending"}}},select:"-contact_name -description -expertisem -hazard_types -hazards -address.region -focal_points -website -telephone -twitter -header -use_default_header -images -email -user -created_at -updated_at -primary_focal_point -partners"};""!==i&&(t.query.title=i),j&&(t.query.networks=j);let a=u().map(_,"value");a&&a.length>0&&(t.query.type=a),r&&r.length>0&&(t.query["address.world_region"]=r),D(t),z(e)}})})};r()}catch(e){r(e)}})},89860:(e,t,a)=>{a.a(e,async(e,r)=>{try{a.r(t),a.d(t,{default:()=>m});var s=a(8732),l=a(82015),n=a(27825),i=a.n(n),o=a(7082),d=a(83551),u=a(49481),c=a(84517),y=a(11e3),h=a(63487),p=a(88751),g=e([h]);h=(g.then?(await g)():g)[0];let m=({filterText:e,onFilter:t,onFilterTypeChange:a,onClear:r,filterType:n,onFilterNetworkChange:g,filterNetwork:m})=>{let[x,_]=(0,l.useState)([]),[q,j]=(0,l.useState)([]),{t:w}=(0,p.useTranslation)("common"),b=async e=>{let t=await h.A.get("/institutionType",e);if(t&&Array.isArray(t.data)){let e=i().map(t.data,e=>({label:e.title,value:e._id}));_(e)}},A=async e=>{let t=await h.A.get("/institutionNetwork",e);t&&Array.isArray(t.data)&&j(t.data)};return(0,l.useEffect)(()=>{b({query:{},sort:{title:"asc"}}),A({query:{},sort:{title:"asc"}})},[]),(0,s.jsx)(o.A,{fluid:!0,className:"p-0",children:(0,s.jsxs)(d.A,{children:[(0,s.jsx)(u.A,{xs:4,className:"p-0",children:(0,s.jsx)(c.A,{type:"text",className:"searchInput",placeholder:w("search"),"aria-label":"Search",value:e,onChange:t})}),(0,s.jsx)(u.A,{xs:4,children:(0,s.jsx)(y.MultiSelect,{overrideStrings:{selectSomeItems:w("SelectType"),allItemsAreSelected:"All Types are Selected"},onChange:a,value:n,options:x,className:"select-type",labelledBy:w("SelectType")})}),(0,s.jsx)(u.A,{xs:4,className:"p-0",children:(0,s.jsxs)(c.A,{as:"select","aria-label":"Network","aria-placeholder":"Network",onChange:g,value:m,children:[(0,s.jsx)("option",{value:"",children:w("SelectNetwork")}),q.map((e,t)=>(0,s.jsx)("option",{value:e._id,children:e.title},t))]})})]})})};r()}catch(e){r(e)}})}};