"use strict";(()=>{var e={};e.id=9405,e.ids=[636,3220,9405],e.modules={123:e=>{e.exports=require("dom-helpers/removeEventListener")},1332:e=>{e.exports=require("react-custom-scrollbars-2")},1428:e=>{e.exports=import("axios")},1680:e=>{e.exports=require("@restart/ui/utils")},1919:e=>{e.exports=require("react-transition-group/Transition")},3892:e=>{e.exports=require("classnames")},4048:e=>{e.exports=require("react-truncate")},6009:e=>{e.exports=require("dom-helpers/ownerDocument")},6952:e=>{e.exports=require("@restart/ui/Modal")},7374:e=>{e.exports=require("@restart/ui/Dropdown")},8732:e=>{e.exports=require("react/jsx-runtime")},9653:e=>{e.exports=require("@restart/ui/Overlay")},11242:e=>{e.exports=require("redux-persist/integration/react")},11688:e=>{e.exports=require("@restart/hooks/useWillUnmount")},12388:e=>{e.exports=require("dom-helpers/addEventListener")},13364:e=>{e.exports=require("redux-saga/effects")},14062:e=>{e.exports=import("react-redux")},14078:e=>{e.exports=import("swr")},14332:e=>{e.exports=require("uncontrollable")},16116:e=>{e.exports=require("invariant")},18622:e=>{e.exports=require("yup")},19121:e=>{e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},22326:e=>{e.exports=require("react-dom")},22541:e=>{e.exports=require("redux-persist/lib/storage")},25303:e=>{e.exports=require("@restart/ui/NavItem")},26324:e=>{e.exports=require("warning")},27825:e=>{e.exports=require("lodash")},27910:e=>{e.exports=require("stream")},28217:e=>{e.exports=require("@restart/ui/DropdownItem")},29021:e=>{e.exports=require("fs")},29294:e=>{e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},29628:e=>{e.exports=require("dom-helpers/addClass")},29825:e=>{e.exports=require("prop-types")},29841:e=>{e.exports=require("dom-helpers/hasClass")},30362:e=>{e.exports=require("@restart/ui/DropdownToggle")},33873:e=>{e.exports=require("path")},35576:e=>{e.exports=require("next-i18next/serverSideTranslations")},36653:e=>{e.exports=require("nprogress")},36955:e=>{e.exports=require("@restart/hooks/useBreakpoint")},37766:e=>{e.exports=require("@restart/hooks/useMergedRefs")},39756:e=>{e.exports=import("redux")},40051:e=>{e.exports=require("dom-helpers/removeClass")},40227:(e,r,t)=>{t.a(e,async(e,s)=>{try{t.r(r),t.d(r,{config:()=>h,default:()=>d,getServerSideProps:()=>m,getStaticPaths:()=>q,getStaticProps:()=>c,reportWebVitals:()=>g,routeModule:()=>y,unstable_getServerProps:()=>w,unstable_getServerSideProps:()=>b,unstable_getStaticParams:()=>S,unstable_getStaticPaths:()=>v,unstable_getStaticProps:()=>f});var o=t(63885),a=t(80237),i=t(81413),u=t(9616),n=t.n(u),p=t(72386),x=t(68713),l=e([p,x]);[p,x]=l.then?(await l)():l;let d=(0,i.M)(x,"default"),c=(0,i.M)(x,"getStaticProps"),q=(0,i.M)(x,"getStaticPaths"),m=(0,i.M)(x,"getServerSideProps"),h=(0,i.M)(x,"config"),g=(0,i.M)(x,"reportWebVitals"),f=(0,i.M)(x,"unstable_getStaticProps"),v=(0,i.M)(x,"unstable_getStaticPaths"),S=(0,i.M)(x,"unstable_getStaticParams"),w=(0,i.M)(x,"unstable_getServerProps"),b=(0,i.M)(x,"unstable_getServerSideProps"),y=new o.PagesRouteModule({definition:{kind:a.A.PAGES,page:"/declarationform/[...routes]",pathname:"/declarationform/[...routes]",bundlePath:"",filename:""},components:{App:p.default,Document:n()},userland:x});s()}catch(e){s(e)}})},40361:e=>{e.exports=require("next/dist/compiled/next-server/pages.runtime.prod.js")},42893:e=>{e.exports=import("react-hot-toast")},43294:e=>{e.exports=require("formik")},50009:e=>{e.exports=require("@restart/ui/DropdownMenu")},54131:e=>{e.exports=import("@fortawesome/free-solid-svg-icons")},57664:e=>{e.exports=require("dom-helpers/scrollbarSize")},58928:e=>{e.exports=require("dom-helpers/canUseDOM")},59717:e=>{e.exports=require("@restart/ui/DropdownContext")},60560:e=>{e.exports=require("dom-helpers/contains")},63033:e=>{e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},65447:e=>{e.exports=require("@restart/ui/Button")},67364:e=>{e.exports=require("@restart/hooks/useCallbackRef")},68455:e=>{e.exports=import("redux-saga")},68713:(e,r,t)=>{t.a(e,async(e,s)=>{try{t.r(r),t.d(r,{default:()=>q,getServerSideProps:()=>c});var o=t(8732),a=t(82015),i=t(44233),u=t(13524),n=t(63487),p=t(81493),x=t(94584),l=t(35576),d=e([n,p,x]);async function c({locale:e}){return{props:{...await (0,l.serverSideTranslations)(e,["common"])}}}[n,p,x]=d.then?(await d)():d;let q=()=>{let e=(0,i.useRouter)(),[r,t]=(0,a.useState)(null),[s,l]=(0,a.useState)(null),d=e.query.routes[0],c=async r=>{let s=await n.A.post("/userLinkValidate",r),o=e.query.languageCode?e.query.languageCode:"en";return s?[t(!0),l({...s,languageCode:o})]:t(!1)};(0,a.useEffect)(()=>{c({code:d})},[]);let q=(0,o.jsx)("div",{className:"d-flex justify-content-center align-items-center",style:{padding:"24px"},children:(0,o.jsx)(u.A,{animation:"border",variant:"primary"})});switch(r){case!0:q=(0,o.jsx)(p.default,{userProfile:s,authToken:d});break;case!1:q=(0,o.jsx)(x.default,{})}return(0,o.jsx)("div",{children:q})};s()}catch(e){s(e)}})},69722:e=>{e.exports=require("es6-promise")},74075:e=>{e.exports=require("zlib")},74716:e=>{e.exports=require("moment")},74987:e=>{e.exports=require("@restart/ui/ModalManager")},78097:e=>{e.exports=require("next-redux-saga")},78634:e=>{e.exports=require("@restart/ui/Anchor")},81366:e=>{e.exports=require("redux-auth-wrapper/connectedAuthWrapper")},81521:e=>{e.exports=require("dom-helpers/querySelectorAll")},81895:e=>{e.exports=require("@restart/hooks/useEventCallback")},82015:e=>{e.exports=require("react")},82053:e=>{e.exports=require("@fortawesome/react-fontawesome")},86842:e=>{e.exports=require("@restart/ui/SelectableContext")},87571:e=>{e.exports=require("dom-helpers/transitionEnd")},88751:e=>{e.exports=require("next-i18next")},93787:e=>{e.exports=require("redux-persist")},94584:(e,r,t)=>{t.a(e,async(e,s)=>{try{t.r(r),t.d(r,{default:()=>d});var o=t(8732);t(82015);var a=t(18597),i=t(91353),u=t(54131),n=t(82053),p=t(44233),x=t.n(p),l=e([u]);u=(l.then?(await l)():l)[0];let d=()=>(0,o.jsx)("div",{className:"d-flex justify-content-center align-content-center",children:(0,o.jsxs)(a.A,{className:"text-center m-4 p-5",style:{boxShadow:"0 10px 20px #777",borderRadius:"10px",width:"50vw"},children:[(0,o.jsx)("div",{className:"text-center pt-2",children:(0,o.jsx)(n.FontAwesomeIcon,{icon:u.faExclamationTriangle,color:"indianRed",style:{background:"#d6deec",padding:"60px",borderRadius:"50%",width:"300px",height:"300px",fontSize:"100px"}})}),(0,o.jsxs)(a.A.Body,{children:[(0,o.jsx)("h4",{children:(0,o.jsx)("b",{children:"Huh! Looks like invalid link."})}),(0,o.jsxs)(i.A,{className:"mt-3",variant:"danger",onClick:()=>{x().push("/home")},children:[(0,o.jsx)(n.FontAwesomeIcon,{icon:u.faArrowCircleLeft,color:"white"}),"\xa0\xa0Back to RKI Home"]})]})]})});s()}catch(e){s(e)}})},94947:e=>{e.exports=require("@restart/hooks/useTimeout")},96196:e=>{e.exports=require("next-redux-wrapper")},98320:e=>{e.exports=require("@restart/hooks/useIsomorphicEffect")},99460:e=>{e.exports=require("dom-helpers/css")}};var r=require("../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[6089,9216,9616,2386,5461],()=>t(40227));module.exports=s})();