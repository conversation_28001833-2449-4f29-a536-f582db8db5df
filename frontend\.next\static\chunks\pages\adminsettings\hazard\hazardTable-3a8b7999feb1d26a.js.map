{"version": 3, "file": "static/chunks/pages/adminsettings/hazard/hazardTable-3a8b7999feb1d26a.js", "mappings": "gMA4BA,MAtB0B,OAAC,YAAEA,CAAU,QAsBxBC,EAtB0BC,CAAQ,SAAEC,CAAO,CAAM,GAsBhCF,EAAC,CArBvBG,CAAC,CAAE,CAAGC,CAAAA,EAAAA,EAAAA,EAAAA,CAAcA,CAAC,UAG7B,MACE,UAACC,EAAAA,CAASA,CAAAA,CAACC,KAAK,IAACC,UAAU,eACzB,UAACC,EAAAA,CAAGA,CAAAA,UACF,UAACC,EAAAA,CAAGA,CAAAA,CAACC,GAAI,EAAGH,UAAU,eACpB,UAACI,EAAAA,CAAWA,CAAAA,CACVC,KAAK,OACLL,UAAU,cACVM,YAAaV,EAAE,8BACfW,aAAW,SACXC,MAAOhB,EACPiB,SAAUf,SAMtB,uNCmLA,MAhMoB,KAChB,GAAM,GAAEE,CAAC,MAAEc,CAAI,CAAE,CAAGb,CAAAA,EAAAA,EAAAA,EAAAA,CAAcA,CAAC,SA+Lbc,CA9LhBC,CA8LiB,CA9Le,OAAlBF,EAAKG,QAAQ,CAAY,KAAOH,EAAKG,QAAQ,CAG3D,CAACC,EAAWC,EAAe,CAAGC,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAQ,EAAE,EAChD,EAAGC,EAAW,CAAGD,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,EAAC,GAC1B,CAACE,EAAWC,EAAa,CAAGH,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAC,GACrC,CAACI,EAASC,EAAW,CAAGL,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAC,IACjC,CAACxB,EAAY8B,EAAc,CAAGC,EAAAA,QAAc,CAAC,IAC7C,CAACC,EAAuBC,EAAyB,CAAGF,EAAAA,QAAc,EAAC,GACnE,CAACG,EAAaC,EAAS,CAAGX,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,EAAC,GACnC,CAACY,EAAcC,EAAgB,CAAGb,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAC,CAAC,GAC5C,CAACc,EAAS,CAAGd,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAACe,EAVM,SAAqB,OAAZnB,GAAgB,YAarDoB,EAAkB,MAAOC,IAC3B,IAAMC,EAAQC,IAAAA,SAAW,CAACrB,EAAW,CAAEsB,IAAKH,EAAEI,MAAM,CAACC,IAAI,GACzD,GAAIJ,EAAQ,CAAC,EAAG,CACZpB,CAAS,CAACoB,EAAM,CAACK,OAAO,CAAG,CAACzB,CAAS,CAACoB,EAAM,CAACK,OAAO,CACpDxB,EAAe,IAAID,EAAU,EAC7B,IAAM0B,EAAW,MAAMC,EAAAA,CAAUA,CAACC,KAAK,CAAC,WAAyB,OAAdT,EAAEI,MAAM,CAACC,IAAI,EAAIxB,CAAS,CAACoB,EAAM,CAChFM,IAAYA,EAASJ,GAAG,CACxBO,CAD0B,CAC1BA,EAAKA,CAACC,OAAO,CAAC,GAAkChD,MAAAA,CAA/B4C,EAASK,KAAK,CAACjC,EAAY,CAAC,KAA4B,OAAzBhB,EAAE,yBAElD+C,EAAAA,EAAKA,CAACG,KAAK,CAACN,EAEpB,MACIG,CADG,CACHA,EAAKA,CAACG,KAAK,CAAClD,EAAE,iBAEtB,EAEMmD,EAAS,OAAC,KAAEX,CAAG,SAAEG,CAAO,CAA8B,SACxD,UAACS,EAAAA,CAAIA,CAACC,KAAK,EACPjD,UAAU,OACVK,KAAK,SACLiC,KAAMF,EACNc,GAAId,EACJe,MAAM,GACNC,QAASb,EACT9B,SAAWwB,GAAMD,EAAgBC,MAGnCoB,EAAU,CACZ,CACIf,KAAM1C,EAAE,gBACR0D,SAAUxB,EACVyB,KAAM,GAAaC,GAAKA,EAAEX,KAAK,EAAIW,EAAEX,KAAK,CAACjC,EAAY,CAAG4C,EAAEX,KAAK,CAACjC,EAAY,CAAG,EACrF,EACA,CACI0B,KAAM1C,EAAE,cACR0D,SAAU,cACVC,KAAM,GAAaC,GAAKA,EAAEC,WAAW,EAAID,EAAEC,WAAW,CAACZ,KAAK,CAAGW,EAAEC,WAAW,CAACZ,KAAK,CAAG,EACzF,EACA,CACIP,KAAM1C,EAAE,aACR0D,SAAU,UACVC,KAAM,GAAc,UAACR,EAAAA,CAAQ,GAAGW,CAAG,EACvC,EACA,CACIpB,KAAM1C,EAAE,UACR0D,SAAU,GACVC,KAAM,GACF,WAACI,MAAAA,WACG,UAACC,IAAIA,CAACC,KAAK,6BAA6BC,GAAI,OAAvCF,uBAA2E,OAANJ,EAAEpB,GAAG,WAE3E,UAAC2B,IAAAA,CAAE/D,UAAU,uBAEV,OAEP,UAACgE,OAAAA,CAAKC,QAAS,IAAMC,EAAWV,GAAIW,MAAO,CAAEC,OAAQ,SAAU,WAC3D,UAACL,IAAAA,CAAE/D,UAAU,8BAI7B,EACH,CAEDqE,CAAAA,EAAAA,EAAAA,SAAAA,CAASA,CAAC,KACNC,GACJ,EAAG,EAAE,EAEL,IAAMC,EAAe,CACjBC,KAAM,CAAE,CAAC1C,EAAS,CAAE,KAAM,EAC1B2C,MAAOrD,EACPsD,KAAM,EACNC,MAAO,CAAC,CACZ,EAEML,EAAiB,UACnBrD,GAAW,GACX,IAAMuB,EAAW,MAAMC,EAAAA,CAAUA,CAACmC,GAAG,CAAC,UAAWL,GAC7C/B,GAAYA,EAASqC,IAAI,EAAIrC,EAASqC,IAAI,CAACC,MAAM,CAAG,GAAG,CACvD/D,EAAeyB,EAASqC,IAAI,EAC5B1D,EAAaqB,EAASuC,UAAU,EAChC9D,GAAW,GAEnB,EAQM+D,EAAsB,MAAOC,EAAiBP,KAChDH,EAAaE,KAAK,CAAGQ,EACrBV,EAAaG,IAAI,CAAGA,EACpBzD,GAAW,GACX,IAAMuB,EAAW,MAAMC,EAAAA,CAAUA,CAACmC,GAAG,CAAC,UAAWL,GAC7C/B,GAAYA,EAASqC,IAAI,EAAIrC,EAASqC,IAAI,CAACC,MAAM,CAAG,GAAG,CACvD/D,EAAeyB,EAASqC,IAAI,EAC5BxD,EAAW4D,GACXhE,GAAW,GAEnB,EAEMiD,EAAa,MAAOR,IACtB7B,EAAgB6B,EAAItB,GAAG,EACvBT,GAAS,EACb,EAEMuD,EAAe,UACjB,GAAI,CACA,MAAMzC,EAAAA,CAAUA,CAAC0C,MAAM,CAAC,WAAwB,OAAbvD,IACnC0C,IACA3C,GAAS,GACTgB,EAAAA,EAAKA,CAACC,OAAO,CAAChD,EAAE,uDACpB,CAAE,MAAOkD,EAAO,CACZH,EAAAA,EAAKA,CAACG,KAAK,CAAClD,EAAE,iDAClB,CACJ,EAEMwF,EAAY,IAAMzD,GAAS,GAE3B0D,EAAyB9D,EAAAA,OAAa,CAAC,KAQzC,IAAM+D,EAAY,IACVC,GAAG,CACHhB,EAAaI,KAAK,CAAG,CAAE,CAAC7C,EAAS,CAAEyD,EAAE,EAEzCjB,GACJ,EAEMkB,EAAoBrD,IAAAA,QAAU,CAAC,GAAemD,EAAUC,GAAIE,OAAOC,KAAgC,GAAK,KAO9G,MAAO,UAACjG,EAAAA,OAAiBA,CAAAA,CAACC,SALL,CAKeiG,GAJhCrE,EAAcW,EAAEI,MAAM,CAAC7B,KAAK,EAC5BgF,EAAkBvD,EAAEI,MAAM,CAAC7B,KAAK,CACpC,EAEkDb,QArB9B,CAqBuCiG,IApBnDpG,IACAiC,EAAyB,CAACD,GAC1BF,EAAc,IAEtB,EAgBwE9B,WAAYA,GACxF,EAAG,CAACA,EAAW,EAEf,MACI,WAACmE,MAAAA,WACG,WAACkC,EAAAA,CAAKA,CAAAA,CAACC,KAAMpE,EAAaqE,OAAQX,YAC9B,UAACS,EAAAA,CAAKA,CAACG,MAAM,EAACC,WAAW,aACrB,UAACJ,EAAAA,CAAKA,CAACK,KAAK,WAAEtG,EAAE,oBAEpB,UAACiG,EAAAA,CAAKA,CAACM,IAAI,WAAEvG,EAAE,sCACf,WAACiG,EAAAA,CAAKA,CAACO,MAAM,YACT,UAACC,EAAAA,CAAMA,CAAAA,CAACC,QAAQ,YAAYrC,QAASmB,WAChCxF,EAAE,YAEP,UAACyG,EAAAA,CAAMA,CAAAA,CAACC,QAAQ,UAAUrC,QAASiB,WAC9BtF,EAAE,eAKf,UAAC2G,EAAAA,CAAQA,CAAAA,CACLlD,QAASA,EACTwB,KAAM/D,EACNI,UAAWA,EACXsF,WAAW,EACXC,SAAS,IACTjF,sBAAuBA,EACvBkF,mBAAoBrB,EACpBL,oBAAqBA,EACrB2B,iBAvFa,CAuFKA,GAtF1BpC,EAAaE,KAAK,CAAGrD,EACrBmD,EAAaG,IAAI,CAAGA,EACpBJ,GACJ,MAuFJ,mBC1MA,4CACA,oCACA,WACA,OAAe,EAAQ,KAAyD,CAChF,EACA,SAFsB,oGCiCtB,SAASiC,EAASK,CAAoB,EACpC,GAAM,GAAEhH,CAAC,CAAE,CAAGC,CAAAA,EAAAA,EAAAA,EAAAA,CAAcA,CAAC,UACvBgH,EAA6B,CACjCC,gBAAiBlH,EAAE,cACnB,EACI,SACJyD,CAAO,MACPwB,CAAI,WACJ3D,CAAS,uBACTM,CAAqB,WACrBiF,CAAS,oBACTC,CAAkB,CAClB1B,qBAAmB,kBACnB2B,CAAgB,aAChBI,CAAW,oBACXC,CAAkB,gBAClBC,CAAc,CACdC,SAAO,WACPV,CAAS,sBACTW,CAAoB,mBACpBC,CAAiB,YACjBC,CAAU,QACVC,CAAM,CACNC,kBAAgB,cAChBC,CAAY,CAEZ,CADA,EACGC,EACJ,CAAGb,EAGEc,EAAiB,4BACrBb,EACAc,gBAAiB/H,EAAE,IAP0C,MAQ7DgI,UAAU,UACVvE,EACAwB,KAAMA,GAAQ,EAAE,CAChBgD,OAAO,EACPC,2BAA4BtG,EAC5BuG,UAAWtB,EACXuB,gBAAiBd,qBACjBR,EACAuB,YAAY,EACZC,iBAAkB1B,EAClB2B,kBAAmBnB,GAA0C,GAC7DoB,eADwCpB,cACVD,GAA4B,CAAC,GAAI,GAAI,GAAI,CAA3BA,EAA+B,GAAG,CAC9EsB,oBAAqBnH,EACrBoH,oBAAqBtD,EACrBuD,aAAc5B,iBACdM,uBACAE,EACAC,oBACAoB,kBAAmB,UAACC,EAAAA,CAAYA,CAAAA,CAAAA,GAChCC,SAAU,UAAC3E,IAAAA,CAAE/D,UAAU,6CACvBqH,SACAC,eACAE,mBACAD,EACAvH,UAAW,WACb,EACA,MACE,UAAC2I,EAAAA,EAASA,CAAAA,CAAE,GAAGjB,CAAc,EAEjC,CAEAnB,EAASqC,YAAY,CAAG,CACtBb,WAAW,EACXE,YAAY,EACZ/G,UAAW,KACXsF,WAAW,EACXW,qBAAsB,KACtBC,mBAAmB,EACnBC,YAAY,EACZE,kBAAkB,CACpB,EAEA,MAAehB,QAAQA,EAAC", "sources": ["webpack://_N_E/./pages/adminsettings/hazard/hazardTableFilter.tsx", "webpack://_N_E/./pages/adminsettings/hazard/hazardTable.tsx", "webpack://_N_E/?ec58", "webpack://_N_E/./components/common/RKITable.tsx"], "sourcesContent": ["//Import Library\r\nimport {Col, Container, FormControl, Row} from \"react-bootstrap\";\r\n\r\n//Import services/components\r\nimport { useTranslation } from 'next-i18next';\r\n\r\nconst HazardTableFilter = ({ filterText, onFilter ,onClear}: any) => {\r\n  const { t } = useTranslation('common');\r\n\r\n\r\n  return (\r\n    <Container fluid className=\"p-0\">\r\n      <Row>\r\n        <Col md={4} className=\"p-0\">\r\n          <FormControl\r\n            type=\"text\"\r\n            className=\"searchInput\"\r\n            placeholder={t(\"adminsetting.hazard.Search\")}\r\n            aria-label=\"Search\"\r\n            value={filterText}\r\n            onChange={onFilter}\r\n          />\r\n        </Col>\r\n      </Row>\r\n    </Container>\r\n  )\r\n};\r\n\r\nexport default HazardTableFilter;\r\n", "//Import Library\r\nimport Link from \"next/link\";\r\nimport React, { useState, useEffect } from \"react\";\r\nimport { <PERSON>dal, Button, Form } from \"react-bootstrap\";\r\nimport _ from \"lodash\";\r\nimport toast from 'react-hot-toast';\r\n\r\n//Import services/components\r\nimport RKITable from \"../../../components/common/RKITable\";\r\nimport apiService from \"../../../services/apiService\";\r\nimport HazardTableFilter from \"./hazardTableFilter\";\r\nimport { useTranslation } from 'next-i18next';\r\n\r\nconst HazardTable = () => {\r\n    const { t, i18n } = useTranslation(\"common\");\r\n    const currentLang = i18n.language === \"fr\" ? \"en\" : i18n.language;\r\n    const titleSearch = currentLang ? `title.${currentLang}` : \"title.en\";\r\n\r\n    const [tabledata, setDataToTable] = useState<any[]>([]);\r\n    const [, setLoading] = useState(false);\r\n    const [totalRows, setTotalRows] = useState(0);\r\n    const [perPage, setPerPage] = useState(10);\r\n    const [filterText, setFilterText] = React.useState(\"\");\r\n    const [resetPaginationToggle, setResetPaginationToggle] = React.useState(false);\r\n    const [isModalShow, setModal] = useState(false);\r\n    const [selectHazard, setSelectHazard] = useState({});\r\n    const [currLang] = useState(titleSearch);\r\n\r\n\r\n    const handleMonitored = async (e: React.ChangeEvent<HTMLInputElement>) => {\r\n        const index = _.findIndex(tabledata, { _id: e.target.name });\r\n        if (index > -1) {\r\n            tabledata[index].enabled = !tabledata[index].enabled;\r\n            setDataToTable([...tabledata]);\r\n            const response = await apiService.patch(`/hazard/${e.target.name}`, tabledata[index]);\r\n            if (response && response._id) {\r\n                toast.success(`${response.title[currentLang]} ${t(\"updatedSuccessfully\")}`);\r\n            } else {\r\n                toast.error(response);\r\n            }\r\n        } else {\r\n            toast.error(t(\"indexNotFound\"));\r\n        }\r\n    };\r\n\r\n    const Toggle = ({ _id, enabled } : { _id: any, enabled: any}) => (\r\n        <Form.Check\r\n            className=\"ms-4\"\r\n            type=\"switch\"\r\n            name={_id}\r\n            id={_id}\r\n            label=\"\"\r\n            checked={enabled}\r\n            onChange={(e) => handleMonitored(e)}\r\n        />\r\n    );\r\n    const columns = [\r\n        {\r\n            name: t(\"menu.hazards\"),\r\n            selector: currLang,\r\n            cell: (d: any) => (d && d.title && d.title[currentLang] ? d.title[currentLang] : \"\"),\r\n        },\r\n        {\r\n            name: t(\"hazardType\"),\r\n            selector: \"hazard_type\",\r\n            cell: (d :any) => (d && d.hazard_type && d.hazard_type.title ? d.hazard_type.title : \"\"),\r\n        },\r\n        {\r\n            name: t(\"published\"),\r\n            selector: \"enabled\",\r\n            cell: (row :any) => <Toggle {...row} />,\r\n        },\r\n        {\r\n            name: t(\"action\"),\r\n            selector: \"\",\r\n            cell: (d :any) => (\r\n                <div>\r\n                    <Link href=\"/adminsettings/[...routes]\" as={`/adminsettings/edit_hazard/${d._id}`}>\r\n\r\n                        <i className=\"icon fas fa-edit\" />\r\n\r\n                    </Link>\r\n                    &nbsp;\r\n                    <span onClick={() => userAction(d)} style={{ cursor: \"pointer\" }}>\r\n                        <i className=\"icon fas fa-trash-alt\" />\r\n                    </span>\r\n                </div>\r\n            ),\r\n        },\r\n    ];\r\n\r\n    useEffect(() => {\r\n        getHazardsData();\r\n    }, []);\r\n\r\n    const hazardParams = {\r\n        sort: { [currLang]: \"asc\" },\r\n        limit: perPage,\r\n        page: 1,\r\n        query: {},\r\n    };\r\n\r\n    const getHazardsData = async () => {\r\n        setLoading(true);\r\n        const response = await apiService.get(\"/hazard\", hazardParams);\r\n        if (response && response.data && response.data.length > 0) {\r\n            setDataToTable(response.data);\r\n            setTotalRows(response.totalCount);\r\n            setLoading(false);\r\n        }\r\n    };\r\n\r\n    const handlePageChange = (page: any) => {\r\n        hazardParams.limit = perPage;\r\n        hazardParams.page = page;\r\n        getHazardsData();\r\n    };\r\n\r\n    const handlePerRowsChange = async (newPerPage: any, page: any) => {\r\n        hazardParams.limit = newPerPage;\r\n        hazardParams.page = page;\r\n        setLoading(true);\r\n        const response = await apiService.get(\"/hazard\", hazardParams);\r\n        if (response && response.data && response.data.length > 0) {\r\n            setDataToTable(response.data);\r\n            setPerPage(newPerPage);\r\n            setLoading(false);\r\n        }\r\n    };\r\n\r\n    const userAction = async (row: any) => {\r\n        setSelectHazard(row._id);\r\n        setModal(true);\r\n    };\r\n\r\n    const modalConfirm = async () => {\r\n        try {\r\n            await apiService.remove(`/hazard/${selectHazard}`);\r\n            getHazardsData();\r\n            setModal(false);\r\n            toast.success(t(\"adminsetting.hazard.Table.hazardDeletedSuccessfully\"));\r\n        } catch (error) {\r\n            toast.error(t(\"adminsetting.hazard.Table.errorDeletingHazard\"));\r\n        }\r\n    };\r\n\r\n    const modalHide = () => setModal(false);\r\n\r\n    const subHeaderComponentMemo = React.useMemo(() => {\r\n        const handleClear = () => {\r\n            if (filterText) {\r\n                setResetPaginationToggle(!resetPaginationToggle);\r\n                setFilterText(\"\");\r\n            }\r\n        };\r\n\r\n        const sendQuery = (q: any) => {\r\n            if (q) {\r\n                hazardParams.query = { [currLang]: q };\r\n            }\r\n            getHazardsData();\r\n        };\r\n\r\n        const handleSearchTitle = _.debounce((q: string) => sendQuery(q), Number(process.env.SEARCH_DEBOUNCE_TIME) || 300);\r\n\r\n        const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {\r\n            setFilterText(e.target.value);\r\n            handleSearchTitle(e.target.value);\r\n        };\r\n\r\n        return <HazardTableFilter onFilter={handleChange} onClear={handleClear} filterText={filterText} />;\r\n    }, [filterText]);\r\n\r\n    return (\r\n        <div>\r\n            <Modal show={isModalShow} onHide={modalHide}>\r\n                <Modal.Header closeButton>\r\n                    <Modal.Title>{t(\"deleteHazard\")}</Modal.Title>\r\n                </Modal.Header>\r\n                <Modal.Body>{t(\"areYouSureWantToDeleteThisHazard\")}</Modal.Body>\r\n                <Modal.Footer>\r\n                    <Button variant=\"secondary\" onClick={modalHide}>\r\n                        {t(\"cancel\")}\r\n                    </Button>\r\n                    <Button variant=\"primary\" onClick={modalConfirm}>\r\n                        {t(\"yes\")}\r\n                    </Button>\r\n                </Modal.Footer>\r\n            </Modal>\r\n\r\n            <RKITable\r\n                columns={columns}\r\n                data={tabledata}\r\n                totalRows={totalRows}\r\n                pagServer={true}\r\n                subheader\r\n                resetPaginationToggle={resetPaginationToggle}\r\n                subHeaderComponent={subHeaderComponentMemo}\r\n                handlePerRowsChange={handlePerRowsChange}\r\n                handlePageChange={handlePageChange}\r\n            />\r\n        </div>\r\n    );\r\n};\r\n\r\nexport default HazardTable;\r\n", "\n    (window.__NEXT_P = window.__NEXT_P || []).push([\n      \"/adminsettings/hazard/hazardTable\",\n      function () {\n        return require(\"private-next-pages/adminsettings/hazard/hazardTable.tsx\");\n      }\n    ]);\n    if(module.hot) {\n      module.hot.dispose(function () {\n        window.__NEXT_P.push([\"/adminsettings/hazard/hazardTable\"])\n      });\n    }\n  ", "//Import Library\r\nimport React from 'react'\r\nimport DataTable  from 'react-data-table-component';\r\n\r\n//Import services/components\r\nimport { useTranslation } from 'next-i18next';\r\nimport CustomLoader from './CustomLoader';\r\n\r\ninterface RKITableProps {\r\n  columns: any[];\r\n  data: any[];\r\n  totalRows?: number;\r\n  resetPaginationToggle?: boolean;\r\n  subheader?: boolean;\r\n  subHeaderComponent?: React.ReactNode;\r\n  handlePerRowsChange?: (newPerPage: number, page: number) => void;\r\n  handlePageChange?: (page: number) => void;\r\n  rowsPerPage?: number[];\r\n  defaultRowsPerPage?: number;\r\n  selectableRows?: boolean;\r\n  loading?: boolean;\r\n  pagServer?: boolean;\r\n  onSelectedRowsChange?: (selectedRows: any) => void;\r\n  clearSelectedRows?: boolean;\r\n  sortServer?: boolean;\r\n  onSort?: (column: any, sortDirection: string) => void;\r\n  persistTableHead?: boolean;\r\n  sortFunction?: (rows: any[], field: any, direction: 'asc' | 'desc') => any[];\r\n  noHeader?: boolean;\r\n  dense?: boolean;\r\n  pagination?: boolean;\r\n  paginationServer?: boolean;\r\n  paginationTotalRows?: number;\r\n  subHeaderAlign?: string;\r\n  [key: string]: any;\r\n}\r\n\r\nfunction RKITable(props: RKITableProps) {\r\n  const { t } = useTranslation('common');\r\n  const paginationComponentOptions = {\r\n    rowsPerPageText: t('Rowsperpage'),\r\n    };\r\n  const {\r\n    columns,\r\n    data,\r\n    totalRows,\r\n    resetPaginationToggle,\r\n    subheader,\r\n    subHeaderComponent,\r\n    handlePerRowsChange,\r\n    handlePageChange,\r\n    rowsPerPage,\r\n    defaultRowsPerPage,\r\n    selectableRows,\r\n    loading,\r\n    pagServer,\r\n    onSelectedRowsChange,\r\n    clearSelectedRows,\r\n    sortServer,\r\n    onSort,\r\n    persistTableHead,\r\n    sortFunction,\r\n    // Filter out any unknown props that might cause DOM warnings\r\n    ...otherProps\r\n  } = props;\r\n\r\n  // Only pass known DataTable props to avoid DOM warnings\r\n  const dataTableProps = {\r\n    paginationComponentOptions,\r\n    noDataComponent: t(\"NoData\"),\r\n    noHeader: true,\r\n    columns,\r\n    data: data || [],\r\n    dense: true,\r\n    paginationResetDefaultPage: resetPaginationToggle,\r\n    subHeader: subheader,\r\n    progressPending: loading,\r\n    subHeaderComponent,\r\n    pagination: true,\r\n    paginationServer: pagServer,\r\n    paginationPerPage: defaultRowsPerPage ? defaultRowsPerPage : 10,\r\n    paginationRowsPerPageOptions: rowsPerPage ? rowsPerPage : [10, 15, 20, 25, 30],\r\n    paginationTotalRows: totalRows,\r\n    onChangeRowsPerPage: handlePerRowsChange,\r\n    onChangePage: handlePageChange,\r\n    selectableRows,\r\n    onSelectedRowsChange,\r\n    clearSelectedRows,\r\n    progressComponent: <CustomLoader/>,\r\n    sortIcon: <i className=\"sort-icon fas fa-exchange-alt\" />,\r\n    sortServer,\r\n    onSort,\r\n    sortFunction,\r\n    persistTableHead,\r\n    className: \"rki-table\"\r\n  };\r\n  return (\r\n    <DataTable {...dataTableProps} />\r\n  )\r\n};\r\n\r\nRKITable.defaultProps = {\r\n  subHeader: false,\r\n  pagination: true,\r\n  totalRows: null,\r\n  pagServer: true,\r\n  onSelectedRowsChange: null,\r\n  clearSelectedRows: false,\r\n  sortServer: false,\r\n  persistTableHead: false\r\n}\r\n\r\nexport default RKITable;\r\n\r\n"], "names": ["filterText", "HazardTableFilter", "onFilter", "onClear", "t", "useTranslation", "Container", "fluid", "className", "Row", "Col", "md", "FormControl", "type", "placeholder", "aria-label", "value", "onChange", "i18n", "HazardTable", "currentLang", "language", "tabledata", "setDataToTable", "useState", "setLoading", "totalRows", "setTotalRows", "perPage", "setPerPage", "setFilterText", "React", "resetPaginationToggle", "setResetPaginationToggle", "isModalShow", "setModal", "selectHazard", "setSelectHazard", "currLang", "titleSearch", "handleMonitored", "e", "index", "_", "_id", "target", "name", "enabled", "response", "apiService", "patch", "toast", "success", "title", "error", "Toggle", "Form", "Check", "id", "label", "checked", "columns", "selector", "cell", "d", "hazard_type", "row", "div", "Link", "href", "as", "i", "span", "onClick", "userAction", "style", "cursor", "useEffect", "getHazardsData", "hazardParams", "sort", "limit", "page", "query", "get", "data", "length", "totalCount", "handlePerRowsChange", "newPerPage", "modalConfirm", "remove", "modalHide", "subHeaderComponentMemo", "<PERSON><PERSON><PERSON><PERSON>", "q", "handleSearchTitle", "Number", "process", "handleChange", "handleClear", "Modal", "show", "onHide", "Header", "closeButton", "Title", "Body", "Footer", "<PERSON><PERSON>", "variant", "RKITable", "pagServer", "subheader", "subHeaderComponent", "handlePageChange", "props", "paginationComponentOptions", "rowsPerPageText", "rowsPerPage", "defaultRowsPerPage", "selectableRows", "loading", "onSelectedRowsChange", "clearSelectedRows", "sortServer", "onSort", "persistTableHead", "sortFunction", "otherProps", "dataTableProps", "noDataComponent", "<PERSON><PERSON><PERSON><PERSON>", "dense", "paginationResetDefaultPage", "subHeader", "progressPending", "pagination", "paginationServer", "paginationPerPage", "paginationRowsPerPageOptions", "paginationTotalRows", "onChangeRowsPerPage", "onChangePage", "progressComponent", "CustomLoader", "sortIcon", "DataTable", "defaultProps"], "sourceRoot": "", "ignoreList": []}