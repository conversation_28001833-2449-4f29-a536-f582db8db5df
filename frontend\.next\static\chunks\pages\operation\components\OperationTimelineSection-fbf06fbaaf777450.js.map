{"version": 3, "file": "static/chunks/pages/operation/components/OperationTimelineSection-fbf06fbaaf777450.js", "mappings": "+NAgHA,MArGkCA,IAC9B,GAAM,GAAEC,CAAC,CAAE,CAAGC,CAAAA,EAAAA,EAAAA,EAAAA,CAAcA,CAAC,MAoGlBC,IAlGL,CAACC,EAAUC,EAAY,CAAGC,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAC,CAkGL,GAhGpCC,CAAAA,EAAAA,EAAAA,SAAAA,CAASA,CAAC,SACNC,QAAAA,GAAAA,SAASC,cAAc,CAAC,wBAAxBD,EAA+CE,MAAM,CAACN,EAAU,CAAhEI,GACJ,EAAG,CAACJ,EAAS,EAWb,IAAMO,EAAoB,CACtB,EAAG,uBACH,EAAG,yBACH,EAAG,2BACH,EAAG,wBACH,EAAG,4BACH,EAAG,2BACH,EAAG,4BACP,EAEA,MACI,+BACI,UAACC,EAAAA,CAAGA,CAAAA,UACA,UAACC,EAAAA,CAAGA,CAAAA,CAACC,UAAU,mBAAmBC,GAAI,YAClC,WAACC,MAAAA,CAAIF,UAAU,oBAAoBG,MAAO,CAAEC,UAAW,MAAO,YACzDlB,EAAMmB,SAAS,EAAInB,EAAMmB,SAAS,CAACC,QAAQ,CAACC,MAAM,CAAG,GAClD,WAACL,MAAAA,WACG,UAACA,MAAAA,CACGF,UAAU,OACVQ,QA5BZ,CA4BqBC,IA3BrC,IAAMC,EAAOpB,EAAW,GACxBC,EAAYmB,EAAO,EAAI,EAAIA,EAC/B,EA0BgCP,MAAO,CAAEQ,OAAQ,SAAU,WAE3B,UAACC,OAAAA,UACG,UAACC,EAAAA,CAAeA,CAAAA,CAACC,KAAMC,EAAAA,GAAWA,OAG1C,UAACb,MAAAA,CACGF,UAAU,OACVQ,QAhCX,CAgCoBQ,IA/BrCzB,EAAYD,EAAW,GAC3B,EA+BgCa,MAAO,CAAEQ,OAAQ,SAAU,WAE3B,UAACC,OAAAA,UACG,UAACC,EAAAA,CAAeA,CAAAA,CAACC,KAAMG,EAAAA,GAAYA,UAMnD,UAACf,MAAAA,CAAIF,UAAU,wBAAwBkB,GAAG,8BACtC,UAACC,KAAAA,CAAGnB,UAAU,uBACTd,EAAMmB,SAAS,EACZnB,EAAMmB,SAAS,CAACC,QAAQ,EACxBpB,EAAMmB,SAAS,CAACC,QAAQ,CAACc,GAAG,CAAC,CAACC,EAAWC,IAEjC,WAACC,KAAAA,CACGpB,MAAO,CACHqB,OAAQtC,EAAMmB,SAAS,CAACC,QAAQ,CAACC,MAAM,CAAGe,CAC9C,YAGA,UAACpB,MAAAA,CAAIF,UAAU,wBACX,UAACyB,MAAAA,CACGC,IAAK7B,CAAY,CAACwB,EAAKM,SAAS,CAAC,CACjCC,MAAM,OACNC,OAAO,WAGdR,EAAKS,SAAS,CACX,UAACC,IAAAA,CAAE/B,UAAU,sBAAcqB,EAAKS,SAAS,GAEzC,UAACC,IAAAA,CAAE/B,UAAU,sBAAcb,EAAE,aAEhCkC,EAAKW,IAAI,CACN,UAACD,IAAAA,CAAE/B,UAAU,qBACRiC,IAAOZ,EAAKW,IAAI,EAAEE,MAAM,CACrBC,eAIR,UAACJ,CALUE,GAKVF,CAAE/B,UAAU,qBAAab,EAAE,cArB3BmC,gBAiCrD,mBC7GA,4CACA,iDACA,WACA,OAAe,EAAQ,IAAsE,CAC7F,EACA,UAFsB", "sources": ["webpack://_N_E/./pages/operation/components/OperationTimelineSection.tsx", "webpack://_N_E/?cc56"], "sourcesContent": ["//Import Library\r\nimport { faAngleLeft, faAngleRight } from \"@fortawesome/free-solid-svg-icons\";\r\nimport { FontAwesomeIcon } from \"@fortawesome/react-fontawesome\";\r\nimport moment from \"moment\";\r\nimport React, { useState, useEffect } from \"react\";\r\nimport { Col, Row } from \"react-bootstrap\";\r\n\r\n//Import services/components\r\nimport { useTranslation } from 'next-i18next';\r\n\r\n\r\nconst OperationTimelineSection = (props: any) => {\r\n    const { t } = useTranslation('common');\r\n    const formatDateWithoutTime = \"MM-D-YYYY\";\r\n    const [scrolled, setScrolled] = useState(40);\r\n\r\n    useEffect(() => {\r\n        document.getElementById(\"timeline-container\")?.scroll(scrolled, 5000);\r\n    }, [scrolled]);\r\n\r\n    const onLeftArrow = () => {\r\n        const temp = scrolled - 50;\r\n        setScrolled(temp < 0 ? 0 : temp);\r\n    };\r\n\r\n    const onRigthArrow = () => {\r\n        setScrolled(scrolled + 50);\r\n    };\r\n\r\n    const timelineIcon: any = {\r\n        1: \"/images/home/<USER>\",\r\n        2: \"/images/home/<USER>\",\r\n        3: \"/images/home/<USER>\",\r\n        4: \"/images/home/<USER>\",\r\n        5: \"/images/home/<USER>\",\r\n        6: \"/images/home/<USER>\",\r\n        7: \"/images/home/<USER>\",\r\n    };\r\n\r\n    return (\r\n        <>\r\n            <Row>\r\n                <Col className=\"operatinTimeline\" xs={12}>\r\n                    <div className=\"progress_main_sec\" style={{ marginTop: \"90px\" }}>\r\n                        {props.operation && props.operation.timeline.length > 2 && (\r\n                            <div>\r\n                                <div\r\n                                    className=\"prev\"\r\n                                    onClick={onLeftArrow}\r\n                                    style={{ cursor: \"pointer\" }}\r\n                                >\r\n                                    <span>\r\n                                        <FontAwesomeIcon icon={faAngleLeft} />\r\n                                    </span>\r\n                                </div>\r\n                                <div\r\n                                    className=\"next\"\r\n                                    onClick={onRigthArrow}\r\n                                    style={{ cursor: \"pointer\" }}\r\n                                >\r\n                                    <span>\r\n                                        <FontAwesomeIcon icon={faAngleRight} />\r\n                                    </span>\r\n                                </div>\r\n                            </div>\r\n                        )}\r\n\r\n                        <div className=\"progressbar-container\" id=\"timeline-container\">\r\n                            <ul className=\"progressbar\">\r\n                                {props.operation &&\r\n                                    props.operation.timeline &&\r\n                                    props.operation.timeline.map((item: any, i: any) => {\r\n                                        return (\r\n                                            <li\r\n                                                style={{\r\n                                                    zIndex: props.operation.timeline.length - i,\r\n                                                }}\r\n                                                key={i}\r\n                                            >\r\n                                                <div className=\"timelineIcon\">\r\n                                                    <img\r\n                                                        src={timelineIcon[item.iconclass]}\r\n                                                        width=\"80px\"\r\n                                                        height=\"80px\"\r\n                                                    />\r\n                                                </div>\r\n                                                {item.timetitle ? (\r\n                                                    <p className=\"step-label\">{item.timetitle}</p>\r\n                                                ) : (\r\n                                                    <p className=\"step-label\">{t(\"NoTitle\")}</p>\r\n                                                )}\r\n                                                {item.date ? (\r\n                                                    <p className=\"step-text\">\r\n                                                        {moment(item.date).format(\r\n                                                            formatDateWithoutTime\r\n                                                        )}\r\n                                                    </p>\r\n                                                ) : (\r\n                                                    <p className=\"step-text\">{t(\"NoDate\")}</p>\r\n                                                )}\r\n                                            </li>\r\n                                        );\r\n                                    })}\r\n                            </ul>\r\n                        </div>\r\n                    </div>\r\n                </Col>\r\n            </Row>\r\n        </>\r\n    )\r\n}\r\n\r\nexport default OperationTimelineSection;", "\n    (window.__NEXT_P = window.__NEXT_P || []).push([\n      \"/operation/components/OperationTimelineSection\",\n      function () {\n        return require(\"private-next-pages/operation/components/OperationTimelineSection.tsx\");\n      }\n    ]);\n    if(module.hot) {\n      module.hot.dispose(function () {\n        window.__NEXT_P.push([\"/operation/components/OperationTimelineSection\"])\n      });\n    }\n  "], "names": ["props", "t", "useTranslation", "OperationTimelineSection", "scrolled", "setScrolled", "useState", "useEffect", "document", "getElementById", "scroll", "timelineIcon", "Row", "Col", "className", "xs", "div", "style", "marginTop", "operation", "timeline", "length", "onClick", "onLeftArrow", "temp", "cursor", "span", "FontAwesomeIcon", "icon", "faAngleLeft", "onRigthArrow", "faAngleRight", "id", "ul", "map", "item", "i", "li", "zIndex", "img", "src", "iconclass", "width", "height", "timetitle", "p", "date", "moment", "format", "formatDateWithoutTime"], "sourceRoot": "", "ignoreList": []}