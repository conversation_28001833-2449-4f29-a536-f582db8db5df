(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[8707],{35522:(e,t,n)=>{"use strict";n.r(t),n.d(t,{default:()=>u});var a=n(37876),s=n(11041),l=n(21772),c=n(32890),r=n(14232),i=n(51618),o=n(31753);let u=e=>{let{t}=(0,o.Bd)("common"),[n,u]=(0,r.useState)(!1);return(0,a.jsx)(a.Fragment,{children:(0,a.jsxs)(c.A.Item,{eventKey:"1",children:[(0,a.jsxs)(c.<PERSON><PERSON>,{onClick:()=>u(!n),children:[(0,a.jsx)("div",{className:"cardTitle",children:t("vspace.Announcements")}),(0,a.jsx)("div",{className:"cardArrow",children:n?(0,a.jsx)(l.g,{icon:s.EZy,color:"#fff"}):(0,a.jsx)(l.g,{icon:s.QLR,color:"#fff"})})]}),(0,a.jsx)(c.A.Body,{children:(0,a.jsx)(i.default,{})})]})})}},50650:(e,t,n)=>{"use strict";n.d(t,{A:()=>A});var a=n(76959),s=n(14232);let l=function(e,t){let n=(0,s.useRef)(!0);(0,s.useEffect)(()=>{if(n.current){n.current=!1;return}return e()},t)};var c=n(84467),r=n(55987),i=n(10401),o=n(15039),u=n.n(o),d=n(22631),m=n(77346),h=n(37876);let x=s.forwardRef((e,t)=>{let{className:n,bsPrefix:a,as:s="div",...l}=e;return a=(0,m.oU)(a,"carousel-caption"),(0,h.jsx)(s,{ref:t,className:u()(n,a),...l})});x.displayName="CarouselCaption";let f=s.forwardRef((e,t)=>{let{as:n="div",bsPrefix:a,className:s,...l}=e,c=u()(s,(0,m.oU)(a,"carousel-item"));return(0,h.jsx)(n,{ref:t,...l,className:c})});f.displayName="CarouselItem";var p=n(49285),v=n(66270),j=n(79043),N=n(56640);let g=s.forwardRef((e,t)=>{let n,{defaultActiveIndex:o=0,...x}=e,{as:f="div",bsPrefix:g,slide:A=!0,fade:y=!1,controls:_=!0,indicators:C=!0,indicatorLabels:b=[],activeIndex:k,onSelect:w,onSlide:E,onSlid:S,interval:I=5e3,keyboard:R=!0,onKeyDown:T,pause:M="hover",onMouseOver:O,onMouseOut:D,wrap:F=!0,touch:L=!0,onTouchStart:X,onTouchMove:B,onTouchEnd:H,prevIcon:P=(0,h.jsx)("span",{"aria-hidden":"true",className:"carousel-control-prev-icon"}),prevLabel:U="Previous",nextIcon:q=(0,h.jsx)("span",{"aria-hidden":"true",className:"carousel-control-next-icon"}),nextLabel:K="Next",variant:Z,className:z,children:J,...Q}=(0,d.Zw)({defaultActiveIndex:o,...x},{activeIndex:"onSelect"}),W=(0,m.oU)(g,"carousel"),G=(0,m.Wz)(),V=(0,s.useRef)(null),[Y,$]=(0,s.useState)("next"),[ee,et]=(0,s.useState)(!1),[en,ea]=(0,s.useState)(!1),[es,el]=(0,s.useState)(k||0);(0,s.useEffect)(()=>{en||k===es||(V.current?$(V.current):$((k||0)>es?"next":"prev"),A&&ea(!0),el(k||0))},[k,en,es,A]),(0,s.useEffect)(()=>{V.current&&(V.current=null)});let ec=0;(0,p.jJ)(J,(e,t)=>{++ec,t===k&&(n=e.props.interval)});let er=(0,c.A)(n),ei=(0,s.useCallback)(e=>{if(en)return;let t=es-1;if(t<0){if(!F)return;t=ec-1}V.current="prev",null==w||w(t,e)},[en,es,w,F,ec]),eo=(0,a.A)(e=>{if(en)return;let t=es+1;if(t>=ec){if(!F)return;t=0}V.current="next",null==w||w(t,e)}),eu=(0,s.useRef)();(0,s.useImperativeHandle)(t,()=>({element:eu.current,prev:ei,next:eo}));let ed=(0,a.A)(()=>{!document.hidden&&function(e){if(!e||!e.style||!e.parentNode||!e.parentNode.style)return!1;let t=getComputedStyle(e);return"none"!==t.display&&"hidden"!==t.visibility&&"none"!==getComputedStyle(e.parentNode).display}(eu.current)&&(G?ei():eo())}),em="next"===Y?"start":"end";l(()=>{A||(null==E||E(es,em),null==S||S(es,em))},[es]);let eh="".concat(W,"-item-").concat(Y),ex="".concat(W,"-item-").concat(em),ef=(0,s.useCallback)(e=>{(0,j.A)(e),null==E||E(es,em)},[E,es,em]),ep=(0,s.useCallback)(()=>{ea(!1),null==S||S(es,em)},[S,es,em]),ev=(0,s.useCallback)(e=>{if(R&&!/input|textarea/i.test(e.target.tagName))switch(e.key){case"ArrowLeft":e.preventDefault(),G?eo(e):ei(e);return;case"ArrowRight":e.preventDefault(),G?ei(e):eo(e);return}null==T||T(e)},[R,T,ei,eo,G]),ej=(0,s.useCallback)(e=>{"hover"===M&&et(!0),null==O||O(e)},[M,O]),eN=(0,s.useCallback)(e=>{et(!1),null==D||D(e)},[D]),eg=(0,s.useRef)(0),eA=(0,s.useRef)(0),ey=(0,r.A)(),e_=(0,s.useCallback)(e=>{eg.current=e.touches[0].clientX,eA.current=0,"hover"===M&&et(!0),null==X||X(e)},[M,X]),eC=(0,s.useCallback)(e=>{e.touches&&e.touches.length>1?eA.current=0:eA.current=e.touches[0].clientX-eg.current,null==B||B(e)},[B]),eb=(0,s.useCallback)(e=>{if(L){let t=eA.current;Math.abs(t)>40&&(t>0?ei(e):eo(e))}"hover"===M&&ey.set(()=>{et(!1)},I||void 0),null==H||H(e)},[L,M,ei,eo,ey,I,H]),ek=null!=I&&!ee&&!en,ew=(0,s.useRef)();(0,s.useEffect)(()=>{var e,t;if(!ek)return;let n=G?ei:eo;return ew.current=window.setInterval(document.visibilityState?ed:n,null!=(e=null!=(t=er.current)?t:I)?e:void 0),()=>{null!==ew.current&&clearInterval(ew.current)}},[ek,ei,eo,er,I,ed,G]);let eE=(0,s.useMemo)(()=>C&&Array.from({length:ec},(e,t)=>e=>{null==w||w(t,e)}),[C,ec,w]);return(0,h.jsxs)(f,{ref:eu,...Q,onKeyDown:ev,onMouseOver:ej,onMouseOut:eN,onTouchStart:e_,onTouchMove:eC,onTouchEnd:eb,className:u()(z,W,A&&"slide",y&&"".concat(W,"-fade"),Z&&"".concat(W,"-").concat(Z)),children:[C&&(0,h.jsx)("div",{className:"".concat(W,"-indicators"),children:(0,p.Tj)(J,(e,t)=>(0,h.jsx)("button",{type:"button","data-bs-target":"","aria-label":null!=b&&b.length?b[t]:"Slide ".concat(t+1),className:t===es?"active":void 0,onClick:eE?eE[t]:void 0,"aria-current":t===es},t))}),(0,h.jsx)("div",{className:"".concat(W,"-inner"),children:(0,p.Tj)(J,(e,t)=>{let n=t===es;return A?(0,h.jsx)(N.A,{in:n,onEnter:n?ef:void 0,onEntered:n?ep:void 0,addEndListener:v.A,children:(t,a)=>s.cloneElement(e,{...a,className:u()(e.props.className,n&&"entered"!==t&&eh,("entered"===t||"exiting"===t)&&"active",("entering"===t||"exiting"===t)&&ex)})}):s.cloneElement(e,{className:u()(e.props.className,n&&"active")})})}),_&&(0,h.jsxs)(h.Fragment,{children:[(F||0!==k)&&(0,h.jsxs)(i.A,{className:"".concat(W,"-control-prev"),onClick:ei,children:[P,U&&(0,h.jsx)("span",{className:"visually-hidden",children:U})]}),(F||k!==ec-1)&&(0,h.jsxs)(i.A,{className:"".concat(W,"-control-next"),onClick:eo,children:[q,K&&(0,h.jsx)("span",{className:"visually-hidden",children:K})]})]})]})});g.displayName="Carousel";let A=Object.assign(g,{Caption:x,Item:f})},51618:(e,t,n)=>{"use strict";n.r(t),n.d(t,{default:()=>p});var a=n(37876),s=n(14232),l=n(56970),c=n(49589),r=n(37784),i=n(89099),o=n(82851),u=n.n(o),d=n(91951),m=n(50650),h=n(53718),x=n(31753);function f(e){let{announcements:t}=e;return(0,a.jsx)("div",{children:t.map((e,t)=>(0,a.jsx)(l.A,{className:"announcementItem",children:(0,a.jsx)(d.default,{item:e})},t))})}let p=function(){let{t:e}=(0,x.Bd)("common"),t=(0,i.useRouter)().query.routes||[],[n,o]=(0,s.useState)([]),[d,p]=(0,s.useState)(0),[v]=(0,s.useState)(3),j=()=>{o([])},N={query:{show_as_announcement:!0,parent_vspace:t[1]},sort:{created_at:"desc"},limit:"~",select:"-created_at -update_type -contact_details -document -end_date -link -media -reply -start_date -updated_at"},g=async function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:N,t=await h.A.get("/updates",e);t&&t.data&&t.data.length>0?o(u().chunk(t.data,3)):j()};(0,s.useEffect)(()=>{g()},[]);let A=e=>{let t=d,[a,s]=[0,v-1];"next"===e?t++:"prev"===e&&t--,t>s&&(t=0),t<a&&(t=s),n.length-t==1&&(t=n.length-1),n.length-t==0&&(t=1),p(t)};return(0,a.jsx)("div",{className:"announcements mt-0",children:n&&n.length>0?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(c.A,{fluid:!0,children:(0,a.jsxs)(l.A,{children:[(0,a.jsx)(r.A,{xs:10,className:"p-0"}),n&&n.length>1?(0,a.jsx)(r.A,{xs:2,className:"text-end carousel-control p-0",children:(0,a.jsxs)("div",{className:"carousel-navigation",children:[(0,a.jsx)("a",{className:"left carousel-control",onClick:()=>A("prev"),children:(0,a.jsx)("i",{className:"fa fa-chevron-left"})}),(0,a.jsx)("a",{className:"right carousel-control",onClick:()=>A("next"),children:(0,a.jsx)("i",{className:"fa fa-chevron-right"})})]})}):null]})}),(0,a.jsx)(c.A,{fluid:!0,children:(0,a.jsx)(l.A,{children:(0,a.jsx)(r.A,{xs:12,className:"p-0",children:(0,a.jsx)(m.A,{indicators:!1,controls:!1,interval:null,activeIndex:d,children:n.map((e,t)=>(0,a.jsx)(m.A.Item,{children:(0,a.jsx)(f,{announcements:e})},t))})})})})]}):(0,a.jsx)(c.A,{fluid:!0,children:(0,a.jsx)(l.A,{children:(0,a.jsx)(r.A,{xs:12,className:"p-0",children:(0,a.jsx)("div",{className:"border border-info m-3",children:(0,a.jsx)("p",{className:"d-flex d-flex justify-content-center p-2 m-0",children:e("NoAnnouncementFound!")})})})})})})}},52232:(e,t,n)=>{(window.__NEXT_P=window.__NEXT_P||[]).push(["/vspace/AnnouncementsAccordian",function(){return n(35522)}])},91951:(e,t,n)=>{"use strict";n.r(t),n.d(t,{default:()=>r});var a=n(37876),s=n(37784),l=n(48230),c=n.n(l);function r(e){var t,n;let{item:l}=e;return(0,a.jsxs)(s.A,{className:"p-0",xs:12,children:[(0,a.jsx)(c(),{href:"/".concat(l.type,"/[...routes]"),as:"/".concat(l.type,"/show/").concat(l[t=l,"parent_".concat(t.type)],"/update/").concat(l._id),children:l.images&&l.images[0]?(0,a.jsx)("img",{src:"".concat("http://localhost:3001/api/v1","/image/show/").concat(l.images[0]._id),alt:"announcement",className:"announceImg"}):(0,a.jsx)("i",{className:"fa fa-bullhorn announceImg"})}),(0,a.jsxs)("div",{className:"announceDesc",children:[(0,a.jsx)(c(),{href:"/".concat(l.type,"/[...routes]"),as:"/".concat(l.type,"/show/").concat(l[n=l,"parent_".concat(n.type)],"/update/").concat(l._id),children:l&&l.title?l.title:""}),(0,a.jsx)("p",{children:l&&l.description?(e=>{let t=document.createElement("div");t.innerHTML=e;let n=t.textContent||t.innerText||"";return n.length>260?"".concat(n.substring(0,257),"..."):n})(l.description):null})]})]})}}},e=>{var t=t=>e(e.s=t);e.O(0,[7725,1772,636,6593,8792],()=>t(52232)),_N_E=e.O()}]);
//# sourceMappingURL=AnnouncementsAccordian-982dc83a0fd77e01.js.map