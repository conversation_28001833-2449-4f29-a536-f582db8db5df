"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[8012],{33657:(e,t,i)=>{i.d(t,{A:()=>l});var a=i(53718);class s{arrayDifference(e,t){let i=[];return e.forEach(e=>{0===t.filter(t=>t._id===e.value).length&&i.push(e.value)}),i}async deleteInvitations(e,t){e.forEach(async e=>{let i=await a.A.get("/users/".concat(e));i.institutionInvites.length&&(i.institutionInvites=i.institutionInvites.filter(e=>e.institutionId!==t),await a.A.patch("/users/".concat(e),i))})}isDuplicateInvite(e,t){return!!e.filter(e=>e.institutionId===t).length}getNewInviteMeta(e,t){return{institutionName:t,institutionId:e,status:"Request Pending"}}async getUserData(e,t,i){let s={};return t&&i?await a.A.get("/users",{query:{email:t,username:i}}):await a.A.get("/users/".concat(e))}async sendInvitations(e,t,i){e.forEach(async e=>{let s=e._id;if(s){let e=await this.getUserData(s);e&&(e.institutionInvites||(e.institutionInvites=[]),e.institutionInvites=e.institutionInvites.map(e=>(e.institutionId===t&&"Rejected"===e.status&&(e.status="Request Pending"),e)),this.isDuplicateInvite(e.institutionInvites||[],t)||e.institutionInvites.push(this.getNewInviteMeta(t,i)),await a.A.patch("/users/".concat(s),e))}else this.inviteNewUserWithEmail(e.email,e.username,t,i)})}async inviteNewUserWithEmail(e,t,i,s){let l=await a.A.get("/users",{query:{email:e,username:t}});l&&l.data[0]&&((l=l.data[0]).institutionInvites.push(this.getNewInviteMeta(i,s)),await a.A.patch("/users/".concat(l._id),l))}}let l=new s},40928:(e,t,i)=>{i.r(t),i.d(t,{default:()=>g});var a=i(37876),s=i(14232),l=i(93131),n=i.n(l),r=i(7940),o=i.n(r),d=i(31195),c=i(56970),u=i(37784),p=i(60282),m=i(97685),h=i(53718),x=i(31753);let g=e=>{let{isOpen:t,onModalClose:i,image:l,getId:r,fileName:g,getBlob:j}=e,[y,f]=(0,s.useState)(1),[A,v]=(0,s.useState)(""),[b,w]=(0,s.useState)(null),_=(0,s.useRef)(null),{t:S}=(0,x.Bd)("common");(0,s.useEffect)(()=>{v(g)},[g]);let C=async()=>{let e=(e=>{var t;let i=e.split(","),a=null==(t=i[0].match(/:(.*?);/))?void 0:t[1],s=atob(i[1]),l=s.length,n=new Uint8Array(l);for(;l--;)n[l]=s.charCodeAt(l);return new Blob([n],{type:a})})(_.current.getImage().toDataURL("image/jpeg",.6));j((window.URL||window.webkitURL).createObjectURL(e));let t=new FormData;t.append("file",e,A);try{let e=await h.A.post("/image",t,{"Content-Type":"multipart/form-data"});e&&e._id&&r(e._id)}catch(e){throw"Something wrong in server || your data!"}m.Ay.success(S("toast.CroppedtheimageSuccessfully")),i(!1),w(null),v("none"),f(1)};return(0,a.jsx)(a.Fragment,{children:(0,a.jsx)("div",{children:(0,a.jsxs)(d.A,{show:t,size:"lg","aria-labelledby":"ProfileEdit",onHide:()=>i(!1),centered:!0,children:[(0,a.jsxs)(d.A.Body,{children:[(0,a.jsxs)("div",{className:"d-flex flex-column justify-content-center align-items-center imgRotate",children:[(0,a.jsx)(n(),{ref:_,width:700,height:400,borderRadius:2,scale:y,color:[0,0,0,.6],image:b||l,style:{width:"100%",height:"auto"}}),(0,a.jsx)("div",{className:"info-identifier",children:(0,a.jsx)("span",{children:S("ThisareawillcontainyourInstitutionandfocalpointinformation")})})]}),(0,a.jsx)("div",{className:"mx-2 my-3",children:(0,a.jsxs)(c.A,{children:[(0,a.jsx)(u.A,{sm:1,md:1,lg:1,className:"pe-0",children:(0,a.jsx)("b",{children:S("Zoom")})}),(0,a.jsx)(u.A,{sm:11,md:11,lg:11,children:(0,a.jsx)(o(),{value:y,tooltip:"auto",min:1,max:10,step:.01,variant:"primary",onChange:e=>f(Number(e.target.value))})})]})})]}),(0,a.jsxs)(d.A.Footer,{children:[(0,a.jsx)(p.A,{onClick:C,children:S("Crop")}),(0,a.jsx)(p.A,{variant:"danger",onClick:()=>i(!1),children:S("Cancel")})]})]})})})}},48012:(e,t,i)=>{i.r(t),i.d(t,{default:()=>L});var a=i(37876),s=i(14232),l=i(89099),n=i.n(l),r=i(82851),o=i.n(r),d=i(48230),c=i.n(d),u=i(67814),p=i(49589),m=i(29335),h=i(56970),x=i(37784),g=i(29504),j=i(53538),y=i(888),f=i(54975),A=i(60282),v=i(59200),b=i(54773),w=i(97685),_=i(89673),S=i(53718),C=i(33657),I=i(31753),N=i(62677),k=i(5671);let L=e=>{let{routes:t,institution:i}=e,r=(0,s.useRef)(null),d=(0,s.useRef)(null),{t:L,i18n:D}=(0,I.Bd)("common"),E={organisationName:"",title:"",country:"",world_region:"",website:"",organisationType:null,telephone:"",dial_code:L("DialCode"),contact_name:"",twitter:"",addressL1:"",addressL2:"",city:"",region:[],users:[],institution:"",expertise:[],network:[],hazards:[],description:"",images:[],header:null,department:"",unit:"",use_default_header:!0,images_src:[]},{query:T}=(0,l.useRouter)(),P=D.language,R="de"===D.language?{title_de:"asc"}:{title:"asc"},[q,F]=(0,s.useState)([]),[z,G]=(0,s.useState)([]),[U,O]=(0,s.useState)([]),[B,M]=(0,s.useState)(null),[W,K]=(0,s.useState)([]),[H,V]=(0,s.useState)([]),[$,J]=(0,s.useState)([]),[Q,Z]=(0,s.useState)([]),[X,Y]=(0,s.useState)([]),[ee,et]=(0,s.useState)(1),[,ei]=(0,s.useState)([]),[ea,es]=(0,s.useState)([{username:"",email:"",institution:"",focal_dial_code:L("DialCode"),mobile_number:"",_id:null}]),[el,en]=(0,s.useState)([]),[er,eo]=(0,s.useState)([]),[ed,ec]=(0,s.useState)([]),[eu,ep]=(0,s.useState)([]),em=t&&"edit"==t[0]&&!!t[1],[eh,ex]=(0,s.useState)(E),[eg,ej]=(0,s.useState)([]),[ey,ef]=(0,s.useState)([]),eA={query:{},sort:{title:"asc"},limit:"~"},ev={query:{},sort:{title:"asc"},select:"-networks -expertise -focal_points -hazard_types -description -twitter -telephone -images -header -partners -type -user -website -hazards -contact_name -updated_at -created_at -__v",limit:"~",lean:!0};(0,s.useEffect)(()=>{if("edit"==t[0]&&i&&i){!function(e){e.organisationType=e&&e.type?e.type._id:"",e.addressL1=e.address.line_1,e.addressL2=e.address.line_2,e.city=e.address.city,e.country=e.address.country&&e.address.country._id?e.address.country._id:" ",e.world_region=e.address&&e.address.world_region?e.address.world_region:"",e.region=e.address.region?e.address.region.map((e,t)=>({label:e.title,value:e._id})):[]}(i);let{_expertise:e,_network:t,_partners:a,_hazards:s}=function(e,t){let i=e.expertise?e.expertise.map((e,t)=>({label:e.title,value:e._id})):[],a=e.networks?e.networks.map((e,t)=>({label:e.title,value:e._id})):[],s=e.partners?e.partners.map((e,t)=>({label:e.title,value:e._id})):[],l=e.hazards?e.hazards.map(e=>({label:e.title[t],value:e._id})):[];return e.users=e.focal_points?e.focal_points.map((e,t)=>({label:e.username,value:e._id})):[],{_expertise:i,_network:a,_partners:s,_hazards:l}}(i,"fr"===D.language?"en":D.language);eE(i.country,eA),V(e),J(t),eU(a),K(s),ei(s),G(i.images?i.images:[]),O(i.images_src?i.images_src:[]),M(i.header&&i.header._id?i.header._id:null),ex(e=>({...e,...i}))}eD(),eC(),eb(),eI(eA),ek(eA),eL(eA),eN(ev)},[]);let eb=async()=>{let e=await S.A.get("/country",{sort:R,limit:"~",select:"-code -code3 -coordinates -created_at -first_letter -health_profile -security_advice -updated_at",lean:!0,languageCode:P});e&&Array.isArray(e.data)&&F(e.data)},ew=e=>{if(e&&e.data&&e.data.length>0){let i=[];e.data.forEach(e=>{e.institutionInvites.filter(e=>e.institutionId===t[1]&&"Approved"===e.status).length&&i.push({label:e.username,value:e._id,email:e.email})}),ej(i),ex(e=>({...e,users:i}))}},e_=(e,i)=>{let a=!1;return e.forEach(e=>{e.institutionInvites.forEach(s=>{if(s.institutionId===t[1]&&"Request Pending"===s.status&&i._id===e._id){a=!0;return}})}),a},eS=e=>{let t=[];return(e=e.filter(t=>!e_(e,t))).forEach(e=>{t.push({label:e.username,value:e._id})}),t},eC=async()=>{let e=await S.A.get("/users",{query:{},sort:{title:"asc"},limit:"~",select:"-firstname -lastname -email -password -role -country -region -institution -is_focal_point -image -mobile_number -enabled -created_at -updated_at -dataConsentPolicy -restrictedUsePolicy -acceptCookiesPolicy -withdrawConsentPolicy -medicalConsentPolicy -fullDataProtectionConsentPolicy -emailActivateToken"});if(em&&ew(e),e&&Array.isArray(e.data)){let t=[];if(em){let i=eS(e.data);e.data.forEach(e=>{t.push({label:e.username,value:e._id})}),Y(i)}else e.data.forEach(e=>{t.push({label:e.username,value:e._id})}),Y(t)}},eI=async e=>{let t=await S.A.get("/InstitutionType",e);t&&Array.isArray(t.data)&&en(t.data)},eN=async e=>{let t=await S.A.get("/institution",e);t&&Array.isArray(t.data)&&ef(t.data.filter(e=>e._id!=T.routes[1]).map((e,t)=>({label:e.title,value:e._id})))},ek=async e=>{let t=await S.A.get("/Expertise",e);t&&Array.isArray(t.data)&&eo(t.data.map((e,t)=>({label:e.title,value:e._id})))},eL=async e=>{let t=await S.A.get("/InstitutionNetwork",e);t&&Array.isArray(t.data)&&ec(t.data.map((e,t)=>({label:e.title,value:e._id})))},eD=async()=>{let e=[],t=await S.A.get("/hazardtype",{query:{},limit:"~",sort:{title:"asc"}});t&&Array.isArray(t.data)&&o().each(t.data,(t,i)=>{e.push(t._id)});let i=await S.A.get("/hazard",{query:{hazard_type:e},limit:"~",sort:{title:"asc"}});i&&Array.isArray(i.data)&&ep(i.data.map((e,t)=>({label:e.title[P],value:e._id})))},eE=async(e,t)=>{let i=[];if(e){let a=await S.A.get("/country_region/".concat(e),t);a&&a.data&&(i=a.data.map((e,t)=>({label:e.title,value:e._id}))).sort((e,t)=>e.label.localeCompare(t.label))}Z(i)},eT=e=>{ex(t=>({...t,...e}))},eP=e=>{let{name:t,value:i}=e.target;if(ex(e=>({...e,[t]:i})),"country"==t){let t=e.target.selectedIndex;if(e.target&&t&&null!=t){let i=e.target[t].getAttribute("data-worldregion");ex(e=>({...e,world_region:i}))}}"country"==t&&(eE(i,eA),eT({region:[]}))},eR=(e,t)=>{ex(i=>({...i,[t]:e}))},eq=e=>{ex(t=>({...t,description:e}))},[eF]=(0,s.useState)(""),[ez]=(0,s.useState)(""),[eG,eU]=(0,s.useState)([]),eO=()=>{let e=[...ea];e.push({username:"",email:"",focal_dial_code:L("DialCode"),mobile_number:"",institution:"",_id:null}),es(e),et(e.length)},eB=(e,t)=>{ea.splice(t,1);let i=[...ea];es(i),et(i.length),0===ea.length&&eO()},eM=(e,t)=>{let i=[...ea];i[t][e.target.name]=e.target.value,es(i)},eW=(e,i)=>{if(em){let a=C.A.arrayDifference(eg,e);C.A.deleteInvitations(a,t[1]),C.A.sendInvitations(e,t[1],i.title)}else e.forEach(async e=>{var t,a;let s,l=e._id;if(l?s=await S.A.get("/users/".concat(l)):e.email&&e.username&&(l=(s=(null==(s=await S.A.get("/users",{query:{email:e.email,username:e.username}}))||null==(t=s.data)?void 0:t.length)?s.data[0]:{})._id),s&&s._id){let e=[];(null==s||null==(a=s.institutionInvites)?void 0:a.length)?(e=[...s.institutionInvites]).filter(e=>e.institutionId===i._id).length<=0?e.push({institutionName:i.title,institutionId:i._id,status:"Request Pending"}):e=e.map(e=>(e.institutionId===i._id&&"Rejected"===e.status&&(e.status="Request Pending"),e)):e=[{institutionName:i.title,institutionId:i._id,status:"Request Pending"}],s.institutionInvites=e,await S.A.patch("/users/".concat(l),s)}})},eK=async e=>{var i,a;let s,l;e.preventDefault();let r=eh.users.map((e,t)=>({_id:e.value}));for(let e=0;e<ea.length;e++)!function(e,t){e[t].username=e[t].username.toLowerCase(),e[t].email=e[t].email.toLowerCase()}(ea,e);let d=o().uniqBy(ea,"username"),c=o().uniqBy(ea,"email");if(d.length!=ea.length||c.length!=ea.length)return void w.Ay.error(L("toast.UniqueFocalpointIssue"));let u={title:eh.title.trim(),contact_name:eh.contact_name,networks:$?$.map((e,t)=>e.value):[],partners:eG?eG.map((e,t)=>e.value):[],expertise:H?H.map((e,t)=>e.value):[],hazards:eh.hazards,address:{country:eh.country,world_region:eh.world_region,region:eh.region?eh.region.map((e,t)=>e.value):[],line_1:eh.addressL1,line_2:eh.addressL2,city:eh.city},focal_points:[...ea,...r],website:eh.website,telephone:eh.telephone,dial_code:eh.dial_code,twitter:eh.twitter,type:eh.organisationType,description:eh.description,images:eh.images,header:eh.header,department:eh.department,unit:eh.unit,use_default_header:null==eh.header,images_src:eh.images_src,primary_focal_point:eh.primary_focal_point};W&&W.length&&(u.hazards=W.map(e=>e.value)),em?((null==u||null==(i=u.focal_points)?void 0:i.length)>0&&!u.focal_points.find(e=>e._id===eh.primary_focal_point)&&(u.primary_focal_point=""),l="toast.Organisationupdatedsuccessfully",s=await S.A.patch("/institution/".concat(t[1]),u)):(l="toast.Organisationaddedsuccessfully",s=await S.A.post("/institution",u)),s&&s._id?((null==(a=u.focal_points)?void 0:a.length)&&eW(u.focal_points,s),L("addOrganisation"),w.Ay.success(L(l)),n().push("/institution/[...routes]","/institution/show/".concat(s._id))):(l=em?"toast.OrganisationNotupdatedsuccessfully":"toast.OrganisationNotaddedsuccessfully","toast.usernameoremailusedalready"==s&&(l="toast.OrganisationNameShouldUnique"),w.Ay.error(L(l)))},eH=(e,t)=>{let i={},a=e.filter(e=>e.index==t).map(e=>e.serverID);switch(t){case 0:i={header:String(a)};break;case 1:break;case 2:i={images:a}}ex(e=>({...e,...i}))},eV=e=>{ex(t=>({...t,images_src:e}))},e$=e=>{ex(t=>({...t,header:e,use_default_header:null==e}))};return(0,a.jsx)(b.A,{onSubmit:eK,ref:d,initialValues:eh,enableReinitialize:!0,children:(0,a.jsx)(p.A,{className:"formCard",fluid:!0,children:(0,a.jsx)(m.A,{children:(0,a.jsxs)(m.A.Body,{children:[(0,a.jsx)(h.A,{children:(0,a.jsx)(x.A,{children:(0,a.jsx)(m.A.Title,{children:"edit"===t[0]?L("editOrganisation"):L("addOrganisation")})})}),(0,a.jsx)(h.A,{className:"mb-3",children:(0,a.jsx)(x.A,{children:(0,a.jsxs)(g.A.Group,{children:[(0,a.jsx)(g.A.Label,{className:"required-field",children:L("OrganisationName")}),(0,a.jsx)(v.ks,{name:"title",id:"title",required:!0,value:eh.title,onChange:eP,validator:e=>""!=e.trim(),errorMessage:{validator:L("PleaseAddtheOrganisationName")},as:"input",multiline:!1,rows:1})]})})}),(0,a.jsx)(h.A,{className:"mb-3",children:(0,a.jsx)(x.A,{children:(0,a.jsxs)(g.A.Group,{children:[(0,a.jsxs)(g.A.Label,{children:[" ",L("Description")]}),(0,a.jsx)(k.x,{initContent:eh.description,onChange:e=>eq(e)})]})})}),(0,a.jsx)(h.A,{className:"mb-3",children:(0,a.jsx)(x.A,{children:(0,a.jsxs)(g.A.Group,{children:[(0,a.jsxs)(g.A.Label,{className:"d-inline switch-right",children:[L("HeaderImage"),(0,a.jsx)(g.A.Check,{className:"ms-2 d-inline institution_switch",type:"switch",checked:eh.use_default_header,id:"custom-switch",label:L("UseDefaultHeader")})]}),(0,a.jsx)(N.default,{getId:e=>e$(e),header:B,type:"image"})]})})}),(0,a.jsxs)(h.A,{className:"mb-3",children:[(0,a.jsx)(x.A,{lg:4,sm:6,children:(0,a.jsxs)(g.A.Group,{children:[(0,a.jsx)(g.A.Label,{children:L("Website")}),(0,a.jsx)(v.ks,{name:"website",id:"website",value:eh.website,pattern:"^(?:http(s)?:\\/\\/)?[\\w.-]+(?:\\.[\\w\\.-]+)+[\\w\\-\\._~:/?#[\\]@!\\$&'\\(\\)\\*\\+,;=.]+$",errorMessage:{pattern:L("Pleaseentervalidwebsite")},onChange:eP,required:!1,as:"input",multiline:!1,rows:1})]})}),(0,a.jsx)(x.A,{lg:4,sm:6,children:(0,a.jsxs)(j.A,{className:"row ms-0 me-0",children:[(0,a.jsxs)(g.A.Group,{className:"institutionDialCode col-5 col-lg-4 ps-0 pe-0",children:[(0,a.jsx)(g.A.Label,{children:L("Telephone")}),(0,a.jsxs)(v.s3,{type:"numbers",name:"dial_code",id:"dialCode",value:eh.dial_code,onChange:eP,required:!1,errorMessage:"",children:[(0,a.jsx)("option",{value:eh.dial_code,children:eh.dial_code}),q.map((e,t)=>(0,a.jsx)("option",{value:e.dial_code,children:"(".concat(e.dial_code,") ").concat(e.title)},t))]})]}),(0,a.jsxs)(g.A.Group,{className:"institutionTelephone col-7 col-lg-8 pe-0",children:[(0,a.jsx)(g.A.Label,{children:"\xa0"}),(0,a.jsx)(v.ks,{name:"telephone",id:"telephone",value:eh.telephone,onChange:eP,required:!1,as:"input",multiline:!1,rows:1,errorMessage:{}})]})]})}),(0,a.jsx)(x.A,{lg:4,sm:6,children:(0,a.jsxs)(g.A.Group,{children:[(0,a.jsx)(g.A.Label,{children:L("Twitter")}),(0,a.jsx)(v.ks,{name:"twitter",id:"twitter",value:eh.twitter,onChange:eP,required:!1,as:"input",multiline:!1,rows:1,errorMessage:{},validator:()=>!0,pattern:""})]})})]}),(0,a.jsx)(m.A.Text,{children:(0,a.jsx)("b",{children:L("Address")})}),(0,a.jsxs)(h.A,{className:"mb-3",children:[(0,a.jsx)(x.A,{lg:!0,md:2,children:(0,a.jsxs)(g.A.Group,{children:[(0,a.jsx)(g.A.Label,{children:L("Address1")}),(0,a.jsx)(v.ks,{name:"addressL1",id:"addressL1",value:eh.addressL1,onChange:eP,required:!1,as:"input",multiline:!1,rows:1,errorMessage:{},validator:()=>!0,pattern:""})]})}),(0,a.jsx)(x.A,{lg:!0,md:2,children:(0,a.jsxs)(g.A.Group,{children:[(0,a.jsx)(g.A.Label,{children:L("Address2")}),(0,a.jsx)(v.ks,{name:"addressL2",id:"addressL2",value:eh.addressL2,onChange:eP,required:!1,as:"input",multiline:!1,rows:1,errorMessage:{},validator:()=>!0,pattern:""})]})}),(0,a.jsx)(x.A,{lg:!0,md:2,children:(0,a.jsxs)(g.A.Group,{children:[(0,a.jsx)(g.A.Label,{children:L("City")}),(0,a.jsx)(v.ks,{name:"city",id:"city",value:eh.city,onChange:eP,required:!1,as:"input",multiline:!1,rows:1,errorMessage:{},validator:()=>!0,pattern:""})]})}),(0,a.jsx)(x.A,{lg:!0,md:3,children:(0,a.jsxs)(g.A.Group,{children:[(0,a.jsx)(g.A.Label,{className:"required-field",children:L("Country")}),(0,a.jsxs)(v.s3,{name:"country",id:"country",value:eh.country,onChange:eP,errorMessage:L("thisfieldisrequired"),required:!0,children:[(0,a.jsx)("option",{value:"",children:L("SelectCountry")}),q.map((e,t)=>(0,a.jsx)("option",{"data-worldregion":e.world_region,value:e._id,children:e.title},t))]})]})}),(0,a.jsx)(x.A,{lg:!0,md:3,children:(0,a.jsxs)(g.A.Group,{style:{maxWidth:"250px"},children:[(0,a.jsx)(g.A.Label,{children:L("CountryRegions")}),(0,a.jsx)(u.KF,{overrideStrings:{selectSomeItems:L("SelectRegions"),allItemsAreSelected:"All Regions are Selected"},options:Q,value:eh.region,className:"region",onChange:e=>eR(e,"region"),labelledBy:L("SelectRegions")})]})})]}),(0,a.jsx)(m.A.Text,{children:(0,a.jsx)("b",{children:L("FocalPoints")})}),(0,a.jsx)("hr",{}),(0,a.jsx)(h.A,{className:"mb-3",children:(0,a.jsx)(x.A,{children:(0,a.jsx)(g.A.Group,{children:(0,a.jsxs)(y.A,{activeKey:ee,onSelect:e=>et(e),id:"uncontrolled-tab-example",children:[(0,a.jsx)(f.A,{eventKey:"",title:(0,a.jsx)("div",{children:(0,a.jsx)("p",{children:L("ExistingUser")})}),children:(0,a.jsx)(h.A,{children:(0,a.jsx)(x.A,{md:!0,lg:4,children:(0,a.jsxs)(g.A.Group,{style:{paddingTop:"20px"},children:[(0,a.jsx)(g.A.Label,{children:L("ExistingUser")}),(0,a.jsx)(u.KF,{overrideStrings:{selectSomeItems:"Select Users",allItemsAreSelected:"All Users are Selected"},options:X,onChange:e=>eR(e,"users"),className:"user",value:eh.users,labelledBy:L("SelectUsers")})]})})})}),ea.map((e,t)=>(0,a.jsxs)(f.A,{eventKey:"".concat(t+1),title:"".concat(L("FocalPoints")," ").concat(t+1),children:[(0,a.jsxs)(h.A,{className:"mb-3",children:[(0,a.jsx)(x.A,{lg:4,sm:6,children:(0,a.jsxs)(g.A.Group,{style:{paddingTop:"20px"},children:[(0,a.jsx)(x.A,{className:"p-0",children:(0,a.jsx)(g.A.Label,{children:L("Name")})}),(0,a.jsx)("label",{className:"w-100",children:(0,a.jsx)(v.ks,{name:"username",id:"username",value:e.username,onChange:e=>eM(e,t)})})]})}),(0,a.jsx)(x.A,{lg:4,sm:6,children:(0,a.jsxs)(g.A.Group,{style:{paddingTop:"20px"},children:[(0,a.jsx)(x.A,{className:"p-0",children:(0,a.jsx)(g.A.Label,{children:L("Email")})}),(0,a.jsx)("label",{className:"w-100",children:(0,a.jsx)(v.ks,{name:"email",id:"email",value:e.email,onChange:e=>eM(e,t),pattern:"^[^@]+@[^@]+\\.[^@]+$",errorMessage:{pattern:"Please enter a valid email"}})})]})}),(0,a.jsx)(x.A,{lg:4,sm:6,children:(0,a.jsxs)(j.A,{className:"row ms-0 me-0",children:[(0,a.jsxs)(g.A.Group,{style:{paddingTop:"20px"},className:"institutionDialCode col-5 col-lg-4 ps-0 pe-0",children:[(0,a.jsx)(g.A.Label,{children:L("Telephone")}),(0,a.jsxs)(v.s3,{type:"numbers",name:"focal_dial_code",id:"focal_dial_Code",value:e.focal_dial_code,onChange:e=>eM(e,t),children:[(0,a.jsx)("option",{value:e.focal_dial_code,children:e.focal_dial_code}),q.map((e,t)=>(0,a.jsx)("option",{value:e.dial_code,children:"(".concat(e.dial_code,") ").concat(e.title)},t))]})]}),(0,a.jsxs)(g.A.Group,{style:{paddingTop:"20px"},className:"institutionTelephone col-7 col-lg-8 pe-0",children:[(0,a.jsx)(g.A.Label,{children:"\xa0"}),(0,a.jsx)(v.ks,{name:"mobile_number",id:"mobile_number",value:e.mobile_number,onChange:e=>eM(e,t)})]})]})}),(0,a.jsx)(x.A,{children:(0,a.jsx)(g.A.Group,{className:"m-0",children:(0,a.jsx)("input",{name:"institution",id:"institution",value:eh.title,type:"hidden"})})})]}),(0,a.jsx)(h.A,{children:0===t?(0,a.jsx)("span",{}):(0,a.jsx)(x.A,{xs:!0,lg:"4",children:(0,a.jsx)(A.A,{onSelect:e=>et(e),variant:"secondary",onClick:e=>eB(e,t),children:L("Remove")})})})]})),(0,a.jsx)(f.A,{title:(0,a.jsx)("div",{children:(0,a.jsxs)("span",{onClick:eO,children:[" ",(0,a.jsx)("p",{children:L("AddnewFocalPoint")})]})})})]})})})}),(0,a.jsx)(m.A.Text,{children:(0,a.jsx)("b",{children:L("MoreInfo")})}),(0,a.jsxs)(h.A,{className:"mb-3",children:[(0,a.jsx)(x.A,{lg:!0,md:4,children:(0,a.jsxs)(g.A.Group,{children:[(0,a.jsx)(g.A.Label,{children:L("OrganisationType")}),(0,a.jsxs)(v.s3,{name:"organisationType",id:"organisationType",value:null===eh.organisationType?"":eh.organisationType,onChange:eP,required:!1,errorMessage:"",children:[(0,a.jsx)("option",{value:"",children:L("SelectOrganisationType")}),el.map((e,t)=>(0,a.jsx)("option",{value:e._id,children:e.title},t))]})]})}),(0,a.jsx)(x.A,{lg:!0,md:4,children:(0,a.jsxs)(g.A.Group,{children:[(0,a.jsx)(g.A.Label,{children:L("Network")}),(0,a.jsx)(u.KF,{overrideStrings:{selectSomeItems:L("SelectNetwork"),allItemsAreSelected:"All Networks are Selected"},options:ed,value:$,onChange:e=>{J(e)},className:"net-work",labelledBy:L("SelectNetwork")}),(0,a.jsx)("span",{children:ez})]})}),(0,a.jsx)(x.A,{lg:!0,md:4,children:(0,a.jsxs)(g.A.Group,{children:[(0,a.jsx)(g.A.Label,{children:L("Expertise")}),(0,a.jsx)(u.KF,{overrideStrings:{selectSomeItems:L("SelectExpertise"),allItemsAreSelected:"All Expertise are Selected"},options:er,value:H,onChange:e=>{V(e)},className:"expertise",labelledBy:L("SelectExpertise")}),eF]})})]}),(0,a.jsxs)(h.A,{className:"mb-3",children:[(0,a.jsx)(x.A,{md:!0,lg:6,children:(0,a.jsxs)(g.A.Group,{style:{maxWidth:"600px"},children:[(0,a.jsx)(g.A.Label,{children:L("ExpertisewithHazards")}),(0,a.jsx)(u.KF,{overrideStrings:{selectSomeItems:L("ExpertisewithHazards"),allItemsAreSelected:"All Hazards are Selected"},options:eu,filterOptions:(e,t)=>{if(!t)return e;let i=RegExp("^".concat(t),"gi");return e.filter(e=>{let{label:t}=e;return t&&t.match(i)})},onChange:e=>{K(e)},value:W,className:"partners",labelledBy:L("SelectPartners")})]})}),(0,a.jsx)(x.A,{md:!0,lg:6,children:(0,a.jsxs)(g.A.Group,{style:{maxWidth:"600px"},children:[(0,a.jsx)(g.A.Label,{children:L("Partners")}),(0,a.jsx)(u.KF,{overrideStrings:{selectSomeItems:L("SelectPartners"),allItemsAreSelected:"All Partners are Selected"},options:ey,onChange:e=>{eU(e)},value:eG,className:"partners",labelledBy:L("SelectPartners")})]})})]}),(0,a.jsxs)(h.A,{className:"mb-3",children:[(0,a.jsx)(x.A,{sm:12,md:6,lg:6,children:(0,a.jsxs)(g.A.Group,{children:[(0,a.jsx)(g.A.Label,{children:L("Department")}),(0,a.jsx)(v.ks,{name:"department",id:"department",value:eh.department,onChange:eP,required:!1,as:"input",multiline:!1,rows:1,errorMessage:{},validator:()=>!0,pattern:""})]})}),(0,a.jsx)(x.A,{sm:12,md:6,lg:6,children:(0,a.jsxs)(g.A.Group,{children:[(0,a.jsx)(g.A.Label,{children:L("Unit")}),(0,a.jsx)(v.ks,{name:"unit",id:"unit",value:eh.unit,onChange:eP,required:!1,as:"input",multiline:!1,rows:1,errorMessage:{},validator:()=>!0,pattern:""})]})})]}),(0,a.jsx)(m.A.Text,{children:(0,a.jsx)("b",{children:L("MediaGallery")})}),(0,a.jsx)(h.A,{children:(0,a.jsx)(x.A,{children:(0,a.jsx)(_.A,{index:2,datas:z,srcText:U,getImgID:(e,t)=>eH(e,t),getImageSource:e=>eV(e)})})}),(0,a.jsx)(h.A,{className:"my-4",children:(0,a.jsxs)(x.A,{children:[(0,a.jsx)(A.A,{className:"me-2",type:"submit",variant:"primary",ref:r,children:L("submit")}),(0,a.jsx)(A.A,{className:"me-2",onClick:()=>{ex(E),G([]),O([]),M(null),es([]),V([]),J([]),en([]),ep([]),ei([]),K([]),eU([]),window.scrollTo(0,0)},variant:"info",children:L("reset")}),(0,a.jsx)(c(),{href:"/institution",as:"/institution",children:(0,a.jsx)(A.A,{variant:"secondary",children:L("Cancel")})})]})})]})})})})}},53538:(e,t,i)=>{i.d(t,{A:()=>p});var a=i(15039),s=i.n(a),l=i(14232),n=i(77346),r=i(40856),o=i(20348),d=i(37876);let c=l.forwardRef((e,t)=>{let{className:i,bsPrefix:a,as:l="span",...r}=e;return a=(0,n.oU)(a,"input-group-text"),(0,d.jsx)(l,{ref:t,className:s()(i,a),...r})});c.displayName="InputGroupText";let u=l.forwardRef((e,t)=>{let{bsPrefix:i,size:a,hasValidation:r,className:c,as:u="div",...p}=e;i=(0,n.oU)(i,"input-group");let m=(0,l.useMemo)(()=>({}),[]);return(0,d.jsx)(o.A.Provider,{value:m,children:(0,d.jsx)(u,{ref:t,...p,className:s()(c,i,a&&"".concat(i,"-").concat(a),r&&"has-validation")})})});u.displayName="InputGroup";let p=Object.assign(u,{Text:c,Radio:e=>(0,d.jsx)(c,{children:(0,d.jsx)(r.A,{type:"radio",...e})}),Checkbox:e=>(0,d.jsx)(c,{children:(0,d.jsx)(r.A,{type:"checkbox",...e})})})},54773:(e,t,i)=>{i.d(t,{A:()=>o});var a=i(37876),s=i(14232),l=i(39593),n=i(91408);let r=(0,s.forwardRef)((e,t)=>{let{children:i,onSubmit:s,autoComplete:r,className:o,onKeyPress:d,initialValues:c,...u}=e,p=n.Ik().shape({});return(0,a.jsx)(l.l1,{initialValues:c||{},validationSchema:p,onSubmit:(e,t)=>{let i={preventDefault:()=>{},stopPropagation:()=>{},currentTarget:null,target:null,nativeEvent:new Event("submit"),bubbles:!1,cancelable:!0,defaultPrevented:!1,eventPhase:0,isTrusted:!1,timeStamp:Date.now(),type:"submit",isDefaultPrevented:()=>!1,isPropagationStopped:()=>!1,persist:()=>{}};s&&s(i,e,t)},...u,children:e=>(0,a.jsx)(l.lV,{ref:t,onSubmit:e.handleSubmit,autoComplete:r,className:o,onKeyPress:d,children:"function"==typeof i?i(e):i})})});r.displayName="ValidationFormWrapper";let o=r},59200:(e,t,i)=>{i.d(t,{ks:()=>n,s3:()=>r});var a=i(37876);i(14232);var s=i(29504),l=i(39593);let n=e=>{let{name:t,id:i,required:n,validator:r,errorMessage:o,onChange:d,value:c,as:u,multiline:p,rows:m,pattern:h,...x}=e;return(0,a.jsx)(l.D0,{name:t,validate:e=>{let t="string"==typeof e?e:String(e||"");return n&&(!e||""===t.trim())?(null==o?void 0:o.validator)||"This field is required":r&&!r(e)?(null==o?void 0:o.validator)||"Invalid value":h&&e&&!new RegExp(h).test(e)?(null==o?void 0:o.pattern)||"Invalid format":void 0},children:e=>{let{field:t,meta:l}=e;return(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(s.A.Control,{...t,...x,id:i,as:u||"input",rows:m,isInvalid:l.touched&&!!l.error,onChange:e=>{t.onChange(e),d&&d(e)},value:void 0!==c?c:t.value}),l.touched&&l.error?(0,a.jsx)(s.A.Control.Feedback,{type:"invalid",children:l.error}):null]})}})},r=e=>{let{name:t,id:i,required:n,errorMessage:r,onChange:o,value:d,children:c,...u}=e;return(0,a.jsx)(l.D0,{name:t,validate:e=>{if(n&&(!e||""===e))return(null==r?void 0:r.validator)||"This field is required"},children:e=>{let{field:t,meta:l}=e;return(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(s.A.Control,{as:"select",...t,...u,id:i,isInvalid:l.touched&&!!l.error,onChange:e=>{t.onChange(e),o&&o(e)},value:void 0!==d?d:t.value,children:c}),l.touched&&l.error?(0,a.jsx)(s.A.Control.Feedback,{type:"invalid",children:l.error}):null]})}})}},62677:(e,t,i)=>{i.r(t),i.d(t,{default:()=>p});var a=i(37876),s=i(14232),l=i(17336),n=i(21772),r=i(11041),o=i(97685),d=i(40928),c=i(53718),u=i(31753);let p=e=>{let{getId:t,header:i,type:p}=e,{t:m}=(0,u.Bd)("common"),[h,x]=(0,s.useState)(!1),[g,j]=(0,s.useState)([]),[y,f]=(0,s.useState)(""),[A,v]=(0,s.useState)(""),b="application"===p?"/files":"/image";(0,s.useEffect)(()=>{i?v("".concat("http://localhost:3001/api/v1","/image/show/").concat(i)):v(null)},[i]);let w={flex:1,display:"flex",flexDirection:"column",alignItems:"center",justifyContent:"center",width:"100%",height:"100%",borderWidth:.1,borderColor:"#fafafa",backgroundColor:"#fafafa",color:"black",transition:"border  .24s ease-in-out"},_={borderColor:"#2196f3"},S=e=>{x(e)},{getRootProps:C,getInputProps:I,isDragActive:N,isDragAccept:k,isDragReject:L,fileRejections:D}=(0,l.VB)({noClick:!1,accept:"image/*",multiple:!1,minSize:0,maxSize:2e6,onDrop:e=>{j(e.map((e,t)=>Object.assign(e,{preview:URL.createObjectURL(e)}))),e.length>0&&x(!0)},validator:function(e){return"/image"===b?"image"===e.type.substring(0,5)||o.Ay.error(m("toast.filetypenotsupport")):"/files"===b&&"image"===e.type.substring(0,5)&&o.Ay.error(m("toast.filetypenotsupport")),null}}),E=(0,s.useMemo)(()=>({...w,...N?_:{outline:"2px dashed #bbb"},...k?{outline:"2px dashed #595959"}:{outline:"2px dashed #bbb"},...L?{outline:"2px dashed red"}:{activeStyle:_}}),[N,L]),T=D.length>0&&D[0].file.size>2e6,P=e=>{f(e),t(e)},R=async()=>{let e;(e=i?await c.A.remove("image/".concat(i)):await c.A.remove("image/".concat(y)))&&e._id&&(v(null),P(null))},q=e=>{v(e)};return(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)("div",{className:" d-flex justify-content-center align-items-center mt-3",style:{width:"100%",height:"180px"},children:(0,a.jsxs)("div",{...C({style:E}),children:[(0,a.jsx)("input",{...I()}),(0,a.jsx)(n.g,{icon:r.rOd,size:"4x",color:"#999"}),(0,a.jsx)("p",{style:{color:"#595959",marginBottom:"0px"},children:m("Drag'n'dropsomefileshere,orclicktoselectfiles")}),(0,a.jsxs)("small",{style:{color:"#595959"},children:[(0,a.jsx)("b",{children:m("Note:")})," ",m("Onesingleimagewillbeaccepted")]}),T&&(0,a.jsxs)("small",{className:"text-danger mt-2",children:[(0,a.jsx)(n.g,{icon:r.tUE,size:"1x",color:"red"}),"\xa0",m("FileistoolargeItshouldbelessthan2MB")]}),L&&(0,a.jsxs)("small",{className:"text-danger",style:{color:"red"},children:[(0,a.jsx)(n.g,{icon:r.tUE,size:"1x",color:"red"}),m("Filetypenotacceptedsorr")]})]})}),A&&(0,a.jsx)(a.Fragment,{children:(0,a.jsx)("div",{style:{display:"flex",flexDirection:"row",justifyContent:"flex-start",flexWrap:"wrap",marginTop:20},children:(0,a.jsxs)("div",{style:{display:"inline-flex",borderRadius:2,border:"1px solid #ddd",marginBottom:8,marginRight:20,width:170,height:100,padding:2,position:"relative",boxShadow:"0 0 15px 0.25px rgba(0,0,0,0.25)",boxSizing:"border-box"},children:[(0,a.jsx)("div",{style:{display:"flex"},children:(0,a.jsx)("img",{src:A,style:{display:"block",height:"100%"}})}),(0,a.jsx)(n.g,{icon:r.s0Q,style:{position:"absolute",fontSize:"22px",top:"-10px",right:"-10px",zIndex:1e3,cursor:"pointer",backgroundColor:"#fff",color:"#000",borderRadius:"50%"},color:"black",onClick:R})]})})}),(0,a.jsx)(d.default,{isOpen:h,getId:e=>P(e),image:g&&g[0]?g[0].preview:"",onModalClose:e=>S(e),fileName:g&&g[0]?g[0].name:"",getBlob:e=>q(e)})]})}},89673:(e,t,i)=>{i.d(t,{A:()=>b});var a=i(37876),s=i(14232),l=i(17336),n=i(21772),r=i(11041),o=i(37784),d=i(29504),c=i(60282),u=i(31195),p=i(82851),m=i.n(p),h=i(97685),x=i(53718),g=i(31753);let j=[],y={flex:1,display:"flex",flexDirection:"column",alignItems:"center",justifyContent:"center",width:"100%",height:"100%",borderWidth:.1,borderColor:"#fafafa",backgroundColor:"#fafafa",color:"black",transition:"border  .24s ease-in-out",padding:"15px"},f={display:"flex",padding:"10px",width:"100%",border:"2px solid gray",flexDirection:"column",justifyContent:"flex-start",flexWrap:"wrap",marginTop:20},A={width:"150px"},v={borderColor:"#2196f3"},b=e=>{let t,{t:i}=(0,g.Bd)("common"),[p,b]=(0,s.useState)(!1),[w,_]=(0,s.useState)(),S="application"==e.type?0x1400000:"20971520",[C,I]=(0,s.useState)([]),[N,k]=(0,s.useState)(!0),[L,D]=(0,s.useState)([]),E=e&&"application"===e.type?"/files":"/image",T=async e=>{await x.A.remove("".concat(E,"/").concat(e))},P=e=>{_(e),b(!0)},R=(e,t)=>{let i=[...L];i[t]=e.target.value,D(i)},q=t=>{switch(t&&t.name.split(".").pop()){case"JPG":case"jpg":case"jpeg":case"jpg":case"png":return(0,a.jsx)("img",{src:t.preview,style:A});case"pdf":return(0,a.jsx)("img",{src:"/images/fileIcons/pdfFile.png",className:"application"===e.type?"docPreview":"imgPreview"});case"docx":default:return(0,a.jsx)("img",{src:"/images/fileIcons/wordFile.png",className:"application"===e.type?"docPreview":"imgPreview"});case"xls":case"xlsx":return(0,a.jsx)("img",{src:"/images/fileIcons/xlsFile.png",className:"application"===e.type?"docPreview":"imgPreview"})}},F=()=>b(!1),z=()=>{b(!1)},G=t=>{let i=(t=w)&&t._id?{serverID:t._id}:{file:t},a=m().findIndex(j,i),s=[...L];s.splice(a,1),D(s),T(j[a].serverID),j.splice(a,1),e.getImgID(j,e.index?e.index:0);let l=[...C];l.splice(l.indexOf(t),1),I(l),b(!1)},U=C.map((t,s)=>(0,a.jsxs)("div",{children:[(0,a.jsx)(o.A,{xs:12,children:(0,a.jsxs)("div",{className:"row",children:[(0,a.jsx)(o.A,{md:4,lg:3,className:"application text-center align-self-center"===e.type?"docImagePreview text-center align-self-center":"imgPreview text-center align-self-center",children:q(t)}),(0,a.jsx)(o.A,{md:5,lg:7,className:"align-self-center",children:(0,a.jsxs)(d.A,{children:[(0,a.jsxs)(d.A.Group,{controlId:"filename",children:[(0,a.jsx)(d.A.Label,{className:"mt-2",children:i("FileName")}),(0,a.jsx)(d.A.Control,{size:"sm",type:"text",disabled:!0,value:t.original_name?t.original_name:t.name})]}),(0,a.jsxs)(d.A.Group,{controlId:"description",children:[(0,a.jsx)(d.A.Label,{children:"application"===e.type?i("ShortDescription/(Max255Characters)"):i("Source/Description")}),(0,a.jsx)(d.A.Control,{maxLength:"application"===e.type?255:void 0,size:"sm",type:"text",placeholder:"application"===e.type?i("`Enteryourdocumentdescription`"):i("`Enteryourimagesource/description`"),value:L[s],onChange:e=>R(e,s)})]})]})}),(0,a.jsx)(o.A,{md:3,lg:2,className:"align-self-center text-center",onClick:()=>P(t),children:(0,a.jsx)(c.A,{variant:"dark",children:i("Remove")})})]})}),(0,a.jsxs)(u.A,{show:p,onHide:F,children:[(0,a.jsx)(u.A.Header,{closeButton:!0,children:(0,a.jsx)(u.A.Title,{children:i("DeleteFile")})}),(0,a.jsx)(u.A.Body,{children:i("Areyousurewanttodeletethisfile?")}),(0,a.jsxs)(u.A.Footer,{children:[(0,a.jsx)(c.A,{variant:"secondary",onClick:z,children:i("Cancel")}),(0,a.jsx)(c.A,{variant:"primary",onClick:()=>G(t),children:i("yes")})]})]})]},s));(0,s.useEffect)(()=>{C.forEach(e=>URL.revokeObjectURL(e.preview)),j=[]},[]),(0,s.useEffect)(()=>{e.getImageSource(L)},[L]),(0,s.useEffect)(()=>{D(e.srcText)},[e.srcText]),(0,s.useEffect)(()=>{e&&"true"===e.singleUpload&&k(!1),e&&e.datas&&I([...e.datas.map((t,i)=>(j.push({serverID:t._id,index:e.index?e.index:0,type:t.name.split(".")[1]}),{...t,preview:"".concat("http://localhost:3001/api/v1","/image/show/").concat(t._id)}))])},[e.datas]);let O=async(t,i)=>{if(t.length>i)try{let a=new FormData;a.append("file",t[i]);let s=await x.A.post(E,a,{"Content-Type":"multipart/form-data"});j.push({serverID:s._id,file:t[i],index:e.index?e.index:0,type:t[i].name.split(".")[1]}),O(t,i+1)}catch(e){O(t,i+1)}else e.getImgID(j,e.index?e.index:0)},B=(0,s.useCallback)(async e=>{await O(e,0);let t=e.map(e=>Object.assign(e,{preview:URL.createObjectURL(e)}));N?I(e=>[...e,...t]):I([...t])},[]),{getRootProps:M,getInputProps:W,isDragActive:K,isDragAccept:H,isDragReject:V,fileRejections:$}=(0,l.VB)({accept:e&&e.type?"application/pdf, application/vnd.openxmlformats-officedocument.wordprocessingml.document, application/vnd.oasis.opendocument.text,application/vnd.openxmlformats-officedocument.spreadsheetml.sheet,text/csv":"image/*",multiple:N,minSize:0,maxSize:S,onDrop:B,validator:function(e){if("/image"===E){if("image"!==e.type.substring(0,5))return h.Ay.error(i("toast.filetypenotsupport")),{code:"file-invalid-type",message:"File type not supported"}}else if("/files"===E&&"image"===e.type.substring(0,5))return h.Ay.error(i("toast.filetypenotsupport")),{code:"file-invalid-type",message:"File type not supported"};return null}}),J=(0,s.useMemo)(()=>({...y,...K?v:{outline:"2px dashed #bbb"},...H?{outline:"2px dashed #595959"}:{outline:"2px dashed #bbb"},...V?{outline:"2px dashed red"}:{activeStyle:v}}),[K,V]);t=e&&"application"===e.type?(0,a.jsx)("small",{style:{color:"#595959"},children:i("DocumentWeSupport")}):(0,a.jsx)("small",{style:{color:"#595959"},children:i("ImageWeSupport")});let Q=$.length>0&&$[0].file.size>S;return(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)("div",{className:" d-flex justify-content-center align-items-center mt-3",style:{width:"100%",height:"180px"},children:(0,a.jsxs)("div",{...M({style:J}),children:[(0,a.jsx)("input",{...W()}),(0,a.jsx)(n.g,{icon:r.rOd,size:"4x",color:"#999"}),(0,a.jsx)("p",{style:{color:"#595959",marginBottom:"0px"},children:i("Drag'n'dropsomefileshere,orclicktoselectfiles")}),!N&&(0,a.jsxs)("small",{style:{color:"#595959"},children:[(0,a.jsx)("b",{children:"Note:"})," One single image will be accepted"]}),t,(e.type,Q&&(0,a.jsxs)("small",{className:"text-danger mt-2",children:[(0,a.jsx)(n.g,{icon:r.tUE,size:"1x",color:"red"})," ",i("FileistoolargeItshouldbelessthan20MB")]})),V&&(0,a.jsxs)("small",{className:"text-danger",style:{color:"#595959"},children:[(0,a.jsx)(n.g,{icon:r.tUE,size:"1x",color:"red"})," ",i("Filetypenotacceptedsorr")]})]})}),C.length>0&&(0,a.jsx)("div",{style:f,children:U})]})}}}]);
//# sourceMappingURL=8012-82cafc2057c62d81.js.map