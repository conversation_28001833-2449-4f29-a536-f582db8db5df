{"version": 3, "file": "static/chunks/pages/project/ProjectsTable-9271e67518031cc1.js", "mappings": "kOAgFA,MAxE4B,OAAC,YAC3BA,CAAU,QAuEGC,EAtEbC,CAAQ,gBAsEwBD,EAAC,IArEjCE,CAAoB,SACpBC,CAAO,cACPC,CAAY,CAOb,GACO,CAACC,EAAQC,EAAU,CAAGC,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAC,EAAE,EACjC,GAAEC,CAAC,CAAE,CAAGC,CAAAA,EAAAA,EAAAA,EAAAA,CAAcA,CAAC,UAEvBC,EAAmB,MAAOC,IAC9B,IAAMC,EAAW,MAAMC,EAAAA,CAAUA,CAACC,GAAG,CAAC,iBAAkBH,GACpDC,GAAYG,MAAMC,OAAO,CAACJ,EAASK,IAAI,GAAG,EAAYL,EAASK,IAAI,CACzE,EASA,MAPAC,CAAAA,EAAAA,EAAAA,SAAAA,CAASA,CAAC,KACRR,EAAiB,CACfS,MAAO,CAAC,EACRC,KAAM,CAAEC,MAAO,KAAM,CACvB,EACF,EAAG,EAAE,EAGH,UAACC,EAAAA,CAASA,CAAAA,CAACC,KAAK,IAACC,UAAU,eACzB,WAACC,EAAAA,CAAGA,CAAAA,WACF,UAACC,EAAAA,CAAGA,CAAAA,CAACC,GAAI,EAAGH,UAAU,oCACpB,UAACI,EAAAA,CAAWA,CAAAA,CACVC,KAAK,OACLL,UAAU,cACVM,YAAatB,EAAE,iBACfuB,aAAW,SACXC,MAAOjC,EACPkC,SAAUhC,MAGd,UAACyB,EAAAA,CAAGA,CAAAA,UACF,UAACQ,EAAAA,CAAIA,CAAAA,UACH,WAACA,EAAAA,CAAIA,CAACC,KAAK,EAACC,GAAIX,EAAAA,CAAGA,CAAEY,UAAU,yBAC7B,UAACH,EAAAA,CAAIA,CAACI,KAAK,EAACC,MAAM,IAACC,GAAG,IAAIC,GAAG,aAAI,WAGjC,UAACf,EAAAA,CAAGA,CAAAA,CAACF,UAAU,qBACb,WAACI,EAAAA,CAAWA,CAAAA,CACVQ,GAAG,SACHL,aAAW,SACXE,SAAU/B,EACV8B,MAAO5B,YAEP,UAACsC,SAAAA,CAAOV,MAAO,YAAI,QAClB3B,EAAOsC,GAAG,CAAC,CAACC,EAAWC,IAEpB,UAACH,SAAAA,CAAmBV,MAAOY,EAAKE,GAAG,UAChCF,EAAKvB,KAAK,EADAwB,oBAanC,uLClEA,IAAME,EAAU,uCACVC,EAAc,OAAC,CAAEC,sBAAoB,CAAO,UAChD,GAA4BA,EAAqBC,MAAM,CAAG,EAEtD,CAFyD,EAEzD,OAACC,KAAAA,UACEF,EAAqBN,GAAG,CAAC,CAACC,EAAWC,KACpC,GAAID,EAAKQ,eAAe,CAAE,KAKGR,EAJ3B,MACE,UAACS,KAAAA,UACC,UAACC,IAAIA,CACHC,KAAK,uBACLnB,GAAI,aAFDkB,IAE4C,aAA1BV,GAAAA,EAAKQ,eAAAA,EAALR,KAAAA,EAAAA,EAAsBE,GAAtBF,WAEpBA,EAAKQ,eAAe,CAAC/B,KAAK,IALtBwB,EASb,CACF,KAIC,IACT,EA4PA,EA1PA,SAASW,CAAwB,EAC/B,IAAMC,EAASC,CAAAA,EAAAA,EAAAA,KAyPFF,IAzPEE,CAASA,GAClB,GAAElD,CAAC,CAAE,CAAGC,CAAAA,EAAAA,EAAAA,EAAAA,CAAcA,CAAC,UACvB,aAAEkD,CAAW,CAAEC,iBAAe,CAAE,CAAGC,EACnC,CAAC9D,EAAY+D,EAAc,CAAGC,EAAAA,QAAc,CAAC,IAC7C,CAAC3D,EAAc4D,EAAgB,CAAGD,EAAAA,QAAc,CAAC,IACjD,CAACE,EAAuBC,EAAyB,CAAGH,EAAAA,QAAc,CACtE,IAEI,CAACI,EAAWC,EAAe,CAAG7D,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAC,EAAE,EACzC,CAAC8D,EAASC,EAAW,CAAG/D,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,EAAC,GACjC,CAACgE,EAAWC,EAAa,CAAGjE,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAC,GACrC,CAACkE,EAASC,EAAW,CAAGnE,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAC,IACjC,CAACoE,EAASC,EAAW,CAAGrE,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAC,GACjC,CAACsE,EAAUC,EAAY,CAAGvE,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAM,MAGxCI,EAAqB,CACzBS,KAAM,CAAE2D,WAAY,MAAO,EAC3BC,MAAM,EACNC,MAAOR,EACPS,KAAM,EACN/D,MAAO,CAAC,EACRgE,SAAU,CACR,CAAEC,KAAM,eAAgBC,OAAQ,OAAQ,EACxC,CACED,KAAMrC,EACNsC,OAAQ,mBACV,EACA,CAAED,KAAM,SAAUC,OAAQ,OAAQ,EACnC,CACDA,OACE,2NACJ,EAEM,CAACC,EAAYC,EAAc,CAAGhF,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAACI,GAEvC6E,EAAU,CACd,CACEC,KAAMjF,EAAE,cACRkF,SAAU,QACVC,UAAU,EACVC,KAAM,GACJ,UAACtC,IAAIA,CAACC,KAAK,uBAAuBnB,GAAI,aAAjCkB,IAAyD,aAAPuC,EAAAA,KAAAA,EAAAA,EAAG/C,GAAG,WAC1D+C,EAAExE,KAAK,EAGd,EACA,CACEoE,KAAMjF,EAAE,WACRkF,SAAU,UACVC,UAAU,EACVC,KAAM,GACJ,UAAC5C,EAAAA,CAAYC,qBAAsB4C,EAAE5C,oBAAoB,EAE7D,EACA,CACEwC,KAAMjF,EAAE,cACRkF,SAAU,eACVE,KAAM,GAAaC,EAAEC,YAAY,CAAGD,EAAEC,YAAY,CAACnD,GAAG,CAAC,GAAeC,EAAKvB,KAAK,EAAE0E,IAAI,CAAC,MAAQ,EACjG,EACA,CACEN,KAAMjF,EAAE,UACRkF,SAAU,SACVC,UAAU,EACVC,KAAM,GAAaC,EAAExF,MAAM,EAAIwF,EAAExF,MAAM,CAACgB,KAAK,CAAGwE,EAAExF,MAAM,CAACgB,KAAK,CAAG,EACnE,EACA,CACEoE,KAAMjF,EAAE,YACRkF,SAAU,YACVC,UAAU,CACZ,EACD,CAEKK,EAAkB,MAAOC,IAC7B3B,GAAW,GAEPb,EAAOtC,KAAK,EAAIsC,EAAOtC,KAAK,CAAC+E,OAAO,EAAE,CACxCD,EAAqB9E,KAAK,CAAC4B,EAAQ,CAAG,CACpCU,EAAOtC,KAAK,CAAC+E,OAAO,CACrB,EAIqB,MAAM,CAA1BtC,EAEF,OAAOqC,EAAqB9E,KAAK,CAAC,oCAAoC,CAClC,GAAG,CAA9ByC,EAAgBV,MAAM,CAE/B+C,EAAqB9E,KAAK,CAAC,oCAAoC,CAAG,CAAC,eAAe,CAGlF8E,EAAqB9E,KAAK,CAAC,oCAAoC,CAAGyC,EAGpE,IAAMhD,EAAW,MAAMC,EAAAA,CAAUA,CAACC,GAAG,CAAC,WAAYmF,GAC9CrF,GAAYG,MAAMC,OAAO,CAACJ,EAASK,IAAI,GAAG,CAC5CmD,EAAexD,EAASK,IAAI,EAC5B0C,EAAY/C,EAASK,IAAI,EACzBuD,EAAa5D,EAASuF,UAAU,GAGlC7B,GAAW,EACb,EAmBM8B,EAAsB,MAAOC,EAAiBnB,KAClDvE,EAAcsE,KAAK,CAAGoB,EACtB1F,EAAcuE,IAAI,CAAGA,EACrBZ,GAAW,GAEPb,EAAOtC,KAAK,EAAIsC,EAAOtC,KAAK,CAAC+E,OAAO,EAAE,CACxCvF,EAAcQ,KAAK,CAAC4B,EAAQ,CAAG,CAC7BU,EAAOtC,KAAK,CAAC+E,OAAO,CACrB,EAIqB,MAAM,CAA1BtC,EACF,OAAOjD,EAAcQ,KAAK,CAAC,oCAAoC,CAC3B,GAAG,CAA9ByC,EAAgBV,MAAM,CAC/BvC,EAAcQ,KAAK,CAAC,oCAAoC,CAAG,CAAC,eAAe,CAE3ER,EAAcQ,KAAK,CAAC,oCAAoC,CAAGyC,EAG7DxD,IAAiBO,EAAcQ,KAAK,CAAG,CAAE,GAAxBR,EAAyCQ,KAAK,CAAEd,OAAQD,EAAa,EACtFyE,IAAalE,EAAcS,IAAI,CAAGyD,CAArBlE,CAA8BS,IAAAA,EAE3C,IAAMR,EAAW,MAAMC,EAAAA,CAAUA,CAACC,GAAG,CAAC,WAAYH,GAC9CC,GAAYG,MAAMC,OAAO,CAACJ,EAASK,IAAI,GAAG,CAC5CmD,EAAexD,EAASK,IAAI,EAC5B0C,EAAY/C,EAASK,IAAI,EACzByD,EAAW2B,GACX/B,EAAW,KAGbM,EAAWM,EACb,EAGAhE,CAAAA,EAAAA,EAAAA,SAAAA,CAASA,CAAC,KACRoE,EAAWJ,IAAI,CAAG,EAClBc,EAAgBrF,EAClB,EAAG,CAACiD,EAAiBH,EAAO,EAE5BvC,CAAAA,EAAAA,EAAAA,SAAAA,CAASA,CAAC,KACR8E,EAAgBV,EAClB,EAAG,CAACA,EAAW,EAGf,IAAMgB,EAAa,MAAO/D,EAAagE,KACrCjC,GAAW,GACX3D,EAAcS,IAAI,CAAG,CACnB,CAACmB,EAAOmD,QAAQ,CAAC,CAAEa,CACrB,EACAnG,IAAiBO,EAAcQ,KAAK,CAAG,CAAE,GAAxBR,EAAyCQ,KAAK,CAAEd,OAAQD,EAAa,EACvE,KAAfL,CAAsBY,GAAAA,EAAcQ,KAAK,CAAG,CAAE,GAAGR,EAAcQ,KAAK,CAAEE,MAAOtB,EAAW,EAExF,MAAMiG,EAAgBrF,GACtBmE,EAAYnE,GACZ2D,GAAW,EACb,EAEMkC,EAAY,CAACC,EAAQvB,KACrBuB,GAAG,EACMtF,KAAK,CAAC,KAAQ,CAAGsF,EAC5BnB,EAAWJ,IAAI,CAAGA,GAGlB,OAAOI,EAAWnE,KAAK,CAACE,KAAK,CAC7BkE,EAAc,CAAE,GAAGD,CAAU,EAEjC,EAEMoB,EAAoBC,CAAAA,EAAAA,EAAAA,MAAAA,CAAMA,CAC9BC,IAAAA,QAAU,CAAC,CAACH,EAAGvB,IAASsB,EAAUC,EAAGvB,GAAO2B,OAAOC,KAAgC,GAAK,MACxFC,OAAO,CAEHC,EAAyBC,CAAAA,EAAAA,EAAAA,OAAAA,CAAOA,CAAC,KAQrC,IAAMC,EAA2B,IAC/BlD,EAAgB3D,GACZA,GACFiF,EAAWnE,GADD,EACM,CAAC,MAAS,CAAGd,EAC7BiF,EAAWJ,IAAI,CAAGP,GAGlB,OAAOW,EAAWnE,KAAK,CAACd,MAAM,CAC9BkF,EAAc,CAAE,GAAGD,CAAU,EAEjC,EAOA,MACE,UAACtF,EAAAA,OAAmBA,CAAAA,CAClBC,SAPiB,CAOPkH,GANZrD,EAAcsD,EAAEC,MAAM,CAACrF,KAAK,EAC5B0E,EAAkBU,EAAEC,MAAM,CAACrF,KAAK,CAAE2C,EACpC,EAKIzE,qBAAuBkH,GAAWF,EAAyBE,EAAEC,MAAM,CAACrF,KAAK,EACzE7B,QA5BgB,CA4BPmH,IA3BPvH,IACFmE,EAAyB,CAACD,GAC1BH,EAAc,IAElB,EAwBI/D,WAAYA,EACZK,aAAcA,GAGpB,EAAG,CAACL,EAAYK,EAAc6D,EAAuBL,EAAiBe,EAAQ,EAE9E,MACE,UAAC4C,EAAAA,CAAQA,CAAAA,CACP/B,QAASA,EACTvE,KAAMkD,EACNI,UAAWA,EACXF,QAASA,EACTmD,SAAS,IACTC,gBAAgB,IAChBC,OAAQpB,EACRqB,UAAU,IACVC,WAAW,EACX3D,sBAAuBA,EACvB4D,mBAAoBb,EACpBZ,oBAAqBA,EACrB0B,iBA3IqB,CA2IHA,GA1IpBnH,EAAcsE,KAAK,CAAGR,EACtB9D,EAAcuE,IAAI,CAAGA,EAEjB9E,IACFO,EAAcQ,KAAK,CAAG,CAAE,CADR,EACWR,EAAcQ,KAAK,CAAEd,OAAQD,EAAa,EAGvEyE,IAAalE,EAAcS,IAAI,CAAGyD,CAArBlE,CAA8BS,IAAAA,EAG3C4E,EAAgBrF,GAChBiE,EAAWM,EACb,GAiIF,6GCxPA,SAASqC,EAAS1D,CAAoB,EACpC,GAAM,CAAErD,CAAC,CAAE,CAAGC,CAAAA,EAAAA,EAAAA,EAAAA,CAAcA,CAAC,UACvBsH,EAA6B,CACjCC,gBAAiBxH,EAAE,cACnB,EACI,SACJgF,CAAO,MACPvE,CAAI,WACJsD,CAAS,uBACTN,CAAqB,WACrBuD,CAAS,oBACTK,CAAkB,qBAClBzB,CAAmB,kBACnB0B,CAAgB,aAChBG,CAAW,oBACXC,CAAkB,gBAClBC,CAAc,SACd9D,CAAO,WACPuD,CAAS,sBACTQ,CAAoB,CACpBC,mBAAiB,YACjBV,CAAU,QACVD,CAAM,kBACND,CAAgB,cAChBa,CAAY,CAEZ,CADA,EACGC,EACJ,CAAG1E,EAGE2E,EAAiB,CACrBT,6BACAU,gBAAiBjI,EAAE,IAP0C,MAQ7DkI,UAAU,UACVlD,EACAvE,KAAMA,GAAQ,EAAE,CAChB0H,OAAO,EACPC,2BAA4B3E,EAC5B4E,UAAWrB,EACXsB,gBAAiBzE,qBACjBwD,EACAkB,YAAY,EACZC,iBAAkBpB,EAClBqB,kBAAmBf,GAA0C,GAC7DgB,eADwChB,cACVD,GAA4B,CAAC,GAAI,GAAI,GAAI,CAA3BA,EAA+B,GAAG,CAC9EkB,oBAAqB5E,EACrB6E,oBAAqBhD,EACrBiD,aAAcvB,iBACdK,uBACAC,oBACAC,EACAiB,kBAAmB,UAACC,EAAAA,CAAYA,CAAAA,CAAAA,GAChCC,SAAU,UAACC,IAAAA,CAAEjI,UAAU,6CACvBmG,SACAD,eACAY,mBACAb,EACAjG,UAAW,WACb,EACA,MACE,UAACkI,EAAAA,EAASA,CAAAA,CAAE,GAAGlB,CAAc,EAEjC,CAEAjB,EAASoC,YAAY,CAAG,CACtBd,WAAW,EACXE,WAAY,GACZxE,UAAW,KACXqD,WAAW,EACXQ,qBAAsB,KACtBC,mBAAmB,EACnBV,YAAY,EACZF,iBAAkB,EACpB,EAEA,MAAeF,QAAQA,EAAC,SC/GxB,4CACA,yBACA,WACA,OAAe,EAAQ,KAA8C,CACrE,EACA,SAFsB", "sources": ["webpack://_N_E/./pages/project/ProjectsTableFilter.tsx", "webpack://_N_E/./pages/project/ProjectsTable.tsx", "webpack://_N_E/./components/common/RKITable.tsx", "webpack://_N_E/?666e"], "sourcesContent": ["// Import Library\r\nimport { useEffect, useState } from \"react\";\r\nimport { Col, Container, FormControl, Form, Row } from \"react-bootstrap\";\r\n\r\n//Import services/components\r\nimport { useTranslation } from 'next-i18next';\r\nimport apiService from \"../../services/apiService\";\r\n\r\nconst ProjectsTableFilter = ({\r\n  filterText,\r\n  onFilter,\r\n  onFilterStatusChange,\r\n  onClear,\r\n  filterStatus,\r\n}: {\r\n  filterText: any,\r\n  onFilter: any,\r\n  onFilterStatusChange: any,\r\n  onClear: any,\r\n  filterStatus: any,\r\n}) => {\r\n  const [status, setStatus] = useState([]);\r\n  const { t } = useTranslation('common');\r\n\r\n  const getProjectStatus = async (projectParams: any) => {\r\n    const response = await apiService.get(\"/projectstatus\", projectParams);\r\n    if (response && Array.isArray(response.data)) { setStatus(response.data) }\r\n  };\r\n\r\n  useEffect(() => {\r\n    getProjectStatus({\r\n      query: {},\r\n      sort: { title: \"asc\" },\r\n    });\r\n  }, []);\r\n\r\n  return (\r\n    <Container fluid className=\"p-0\">\r\n      <Row>\r\n        <Col xs={6} className=\"ps-0 align-self-end mb-3\">\r\n          <FormControl\r\n            type=\"text\"\r\n            className=\"searchInput\"\r\n            placeholder={t(\"vspace.Search\")}\r\n            aria-label=\"Search\"\r\n            value={filterText}\r\n            onChange={onFilter}\r\n          />\r\n        </Col>\r\n        <Col>\r\n          <Form>\r\n            <Form.Group as={Row} controlId=\"statusFilter\">\r\n              <Form.Label column sm=\"3\" lg=\"2\">\r\n                Status\r\n              </Form.Label>\r\n              <Col className=\"ps-0 pe-1\">\r\n                <FormControl\r\n                  as=\"select\"\r\n                  aria-label=\"Status\"\r\n                  onChange={onFilterStatusChange}\r\n                  value={filterStatus}\r\n                >\r\n                  <option value={\"\"}>All</option>\r\n                  {status.map((item: any, index) => {\r\n                    return (\r\n                      <option key={index} value={item._id}>\r\n                        {item.title}\r\n                      </option>\r\n                    );\r\n                  })}\r\n                </FormControl>\r\n              </Col>\r\n            </Form.Group>\r\n          </Form>\r\n        </Col>\r\n      </Row>\r\n    </Container>\r\n  );\r\n};\r\n\r\nexport default ProjectsTableFilter;\r\n", "//Import Library\r\nimport Link from \"next/link\";\r\nimport React, { useEffect, useState, useMemo, useRef } from \"react\";\r\nimport _ from \"lodash\";\r\nimport { useRouter } from \"next/router\";\r\n\r\n//Import services/components\r\nimport RKITable from \"../../components/common/RKITable\";\r\nimport ProjectsTableFilter from \"./ProjectsTableFilter\";\r\nimport apiService from \"../../services/apiService\";\r\nimport { useTranslation } from 'next-i18next';\r\n\r\nconst partner = \"partner_institutions.partner_country\"\r\nconst CountryLink = ({ partner_institutions }: any) => {\r\n  if (partner_institutions && partner_institutions.length > 0) {\r\n    return (\r\n      <ul>\r\n        {partner_institutions.map((item: any, index: any) => {\r\n          if (item.partner_country) {\r\n            return (\r\n              <li key={index}>\r\n                <Link\r\n                  href=\"/country/[...routes]\"\r\n                  as={`/country/show/${item.partner_country?._id}`}\r\n                >\r\n                  {item.partner_country.title}\r\n                </Link>\r\n              </li>\r\n            );\r\n          }\r\n        })}\r\n      </ul>\r\n    );\r\n  }\r\n  return null;\r\n};\r\n\r\nfunction ProjectsTable(props: any) {\r\n  const router = useRouter();\r\n  const { t } = useTranslation('common');\r\n  const { setProjects, selectedRegions } = props;\r\n  const [filterText, setFilterText] = React.useState(\"\");\r\n  const [filterStatus, setFilterStatus] = React.useState(\"\");\r\n  const [resetPaginationToggle, setResetPaginationToggle] = React.useState(\r\n    false\r\n  );\r\n  const [tabledata, setDataToTable] = useState([]);\r\n  const [loading, setLoading] = useState(false);\r\n  const [totalRows, setTotalRows] = useState(0);\r\n  const [perPage, setPerPage] = useState(10);\r\n  const [pageNum, setPageNum] = useState(1);\r\n  const [pageSort, setPageSort] = useState<any>(null);\r\n\r\n\r\n  const projectParams: any = {\r\n    sort: { created_at: \"desc\" },\r\n    lean: true,\r\n    limit: perPage,\r\n    page: 1,\r\n    query: {},\r\n    populate: [\r\n      { path: \"area_of_work\", select: \"title\" },\r\n      {\r\n        path: partner, // \"partner_institutions.partner_country\"\r\n        select: \"coordinates title\",\r\n      },\r\n      { path: \"status\", select: \"title\" },\r\n    ],\r\n    select:\r\n      \"-website -description -start_date -end_date -country -region -partner_institutions.partner_region -partner_institutions.partner_institution -institution_invites -vspace -vspace_visibility -user -created_at -updated_at\",\r\n  };\r\n\r\n  const [projParams, setProjParams] = useState(projectParams);\r\n\r\n  const columns = [\r\n    {\r\n      name: t(\"Project(s)\"),\r\n      selector: \"title\",\r\n      sortable: true,\r\n      cell: (d: any) => (\r\n        <Link href=\"/project/[...routes]\" as={`/project/show/${d?._id}`}>\r\n          {d.title}\r\n        </Link>\r\n      ),\r\n    },\r\n    {\r\n      name: t(\"Country\"),\r\n      selector: \"country\",\r\n      sortable: true,\r\n      cell: (d: any) => (\r\n        <CountryLink partner_institutions={d.partner_institutions} />\r\n      ),\r\n    },\r\n    {\r\n      name: t(\"AreaofWork\"),\r\n      selector: \"area_of_work\",\r\n      cell: (d: any) => (d.area_of_work ? d.area_of_work.map((item: any) => item.title).join(\", \") : \"\"),\r\n    },\r\n    {\r\n      name: t(\"Status\"),\r\n      selector: \"status\",\r\n      sortable: true,\r\n      cell: (d: any) => (d.status && d.status.title ? d.status.title : \"\"),\r\n    },\r\n    {\r\n      name: t(\"Fundedby\"),\r\n      selector: \"funded_by\",\r\n      sortable: true,\r\n    },\r\n  ];\r\n\r\n  const getProjectsData = async (projectParamsinitial: any) => {\r\n    setLoading(true);\r\n\r\n    if (router.query && router.query.country) {\r\n      projectParamsinitial.query[partner] = [\r\n        router.query.country,\r\n      ];\r\n    }\r\n\r\n    // Handle selectedRegions with proper condition\r\n    if (selectedRegions === null) {\r\n      // First load: don't apply region filter\r\n      delete projectParamsinitial.query[\"partner_institutions.world_region\"];\r\n    } else if (selectedRegions.length === 0) {\r\n      // No regions selected: force zero results\r\n      projectParamsinitial.query[\"partner_institutions.world_region\"] = [\"__NO_MATCH__\"];\r\n    } else {\r\n      // Normal filtering\r\n      projectParamsinitial.query[\"partner_institutions.world_region\"] = selectedRegions;\r\n    }\r\n\r\n    const response = await apiService.get(\"/project\", projectParamsinitial);\r\n    if (response && Array.isArray(response.data)) {\r\n      setDataToTable(response.data);\r\n      setProjects(response.data);\r\n      setTotalRows(response.totalCount);\r\n    }\r\n\r\n    setLoading(false);\r\n  };\r\n\r\n\r\n  const handlePageChange = (page: any) => {\r\n    projectParams.limit = perPage;\r\n    projectParams.page = page;\r\n\r\n    if (filterStatus) {\r\n      projectParams.query = { ...projectParams.query, status: filterStatus };\r\n    }\r\n\r\n    pageSort && (projectParams.sort = pageSort.sort);\r\n\r\n    // Get the data\r\n    getProjectsData(projectParams);\r\n    setPageNum(page);\r\n  };\r\n\r\n\r\n  const handlePerRowsChange = async (newPerPage: any, page: any) => {\r\n    projectParams.limit = newPerPage;\r\n    projectParams.page = page;\r\n    setLoading(true);\r\n\r\n    if (router.query && router.query.country) {\r\n      projectParams.query[partner] = [\r\n        router.query.country,\r\n      ];\r\n    }\r\n\r\n    // Handle selected regions similarly as in `getProjectsData()`\r\n    if (selectedRegions === null) {\r\n      delete projectParams.query[\"partner_institutions.world_region\"];\r\n    } else if (selectedRegions.length === 0) {\r\n      projectParams.query[\"partner_institutions.world_region\"] = [\"__NO_MATCH__\"];\r\n    } else {\r\n      projectParams.query[\"partner_institutions.world_region\"] = selectedRegions;\r\n    }\r\n\r\n    filterStatus && (projectParams.query = { ...projectParams.query, status: filterStatus });\r\n    pageSort && (projectParams.sort = pageSort.sort);\r\n\r\n    const response = await apiService.get(\"/project\", projectParams);\r\n    if (response && Array.isArray(response.data)) {\r\n      setDataToTable(response.data);\r\n      setProjects(response.data);\r\n      setPerPage(newPerPage);\r\n      setLoading(false);\r\n    }\r\n\r\n    setPageNum(page);\r\n  };\r\n\r\n\r\n  useEffect(() => {\r\n    projParams.page = 1;\r\n    getProjectsData(projectParams);\r\n  }, [selectedRegions, router]);\r\n\r\n  useEffect(() => {\r\n    getProjectsData(projParams);\r\n  }, [projParams]);\r\n\r\n\r\n  const handleSort = async (column: any, sortDirection: any) => {\r\n    setLoading(true);\r\n    projectParams.sort = {\r\n      [column.selector]: sortDirection,\r\n    };\r\n    filterStatus && (projectParams.query = { ...projectParams.query, status: filterStatus });\r\n    filterText !== \"\" && (projectParams.query = { ...projectParams.query, title: filterText });\r\n\r\n    await getProjectsData(projectParams);\r\n    setPageSort(projectParams);\r\n    setLoading(false);\r\n  };\r\n\r\n  const sendQuery = (q: any, page: any) => {\r\n    if (q) {\r\n      projParams.query[\"title\"] = q;\r\n      projParams.page = page;\r\n      setProjParams({ ...projParams });\r\n    } else {\r\n      delete projParams.query.title;\r\n      setProjParams({ ...projParams });\r\n    }\r\n  };\r\n\r\n  const handleSearchTitle = useRef(\r\n    _.debounce((q, page) => sendQuery(q, page), Number(process.env.SEARCH_DEBOUNCE_TIME) || 300)\r\n  ).current;\r\n\r\n  const subHeaderComponentMemo = useMemo(() => {\r\n    const handleClear = () => {\r\n      if (filterText) {\r\n        setResetPaginationToggle(!resetPaginationToggle);\r\n        setFilterText(\"\");\r\n      }\r\n    };\r\n\r\n    const handleFilterStatusChange = (status: any) => {\r\n      setFilterStatus(status);\r\n      if (status) {\r\n        projParams.query[\"status\"] = status;\r\n        projParams.page = pageNum;\r\n        setProjParams({ ...projParams });\r\n      } else {\r\n        delete projParams.query.status;\r\n        setProjParams({ ...projParams });\r\n      }\r\n    };\r\n\r\n    const handleChange = (e: any) => {\r\n      setFilterText(e.target.value);\r\n      handleSearchTitle(e.target.value, pageNum);\r\n    };\r\n\r\n    return (\r\n      <ProjectsTableFilter\r\n        onFilter={handleChange}\r\n        onFilterStatusChange={(e: any) => handleFilterStatusChange(e.target.value)}\r\n        onClear={handleClear}\r\n        filterText={filterText}\r\n        filterStatus={filterStatus}\r\n      />\r\n    );\r\n  }, [filterText, filterStatus, resetPaginationToggle, selectedRegions, pageNum]);\r\n\r\n  return (\r\n    <RKITable\r\n      columns={columns}\r\n      data={tabledata}\r\n      totalRows={totalRows}\r\n      loading={loading}\r\n      subheader\r\n      persistTableHead\r\n      onSort={handleSort}\r\n      sortServer\r\n      pagServer={true}\r\n      resetPaginationToggle={resetPaginationToggle}\r\n      subHeaderComponent={subHeaderComponentMemo}\r\n      handlePerRowsChange={handlePerRowsChange}\r\n      handlePageChange={handlePageChange}\r\n    />\r\n  );\r\n}\r\n\r\nexport default ProjectsTable;\r\n", "//Import Library\r\nimport React from 'react'\r\nimport DataTable  from 'react-data-table-component';\r\n\r\n//Import services/components\r\nimport { useTranslation } from 'next-i18next';\r\nimport CustomLoader from './CustomLoader';\r\n\r\ninterface RKITableProps {\r\n  columns: any[];\r\n  data: any[];\r\n  totalRows?: number;\r\n  resetPaginationToggle?: boolean;\r\n  subheader?: boolean;\r\n  subHeaderComponent?: React.ReactNode;\r\n  handlePerRowsChange?: (newPerPage: number, page: number) => void;\r\n  handlePageChange?: (page: number) => void;\r\n  rowsPerPage?: number[];\r\n  defaultRowsPerPage?: number;\r\n  selectableRows?: boolean;\r\n  loading?: boolean;\r\n  pagServer?: boolean;\r\n  onSelectedRowsChange?: (selectedRows: any) => void;\r\n  clearSelectedRows?: boolean;\r\n  sortServer?: boolean;\r\n  onSort?: (column: any, sortDirection: string) => void;\r\n  persistTableHead?: boolean;\r\n  sortFunction?: (rows: any[], field: any, direction: 'asc' | 'desc') => any[];\r\n  noHeader?: boolean;\r\n  dense?: boolean;\r\n  pagination?: boolean;\r\n  paginationServer?: boolean;\r\n  paginationTotalRows?: number;\r\n  subHeaderAlign?: string;\r\n  [key: string]: any;\r\n}\r\n\r\nfunction RKITable(props: RKITableProps) {\r\n  const { t } = useTranslation('common');\r\n  const paginationComponentOptions = {\r\n    rowsPerPageText: t('Rowsperpage'),\r\n    };\r\n  const {\r\n    columns,\r\n    data,\r\n    totalRows,\r\n    resetPaginationToggle,\r\n    subheader,\r\n    subHeaderComponent,\r\n    handlePerRowsChange,\r\n    handlePageChange,\r\n    rowsPerPage,\r\n    defaultRowsPerPage,\r\n    selectableRows,\r\n    loading,\r\n    pagServer,\r\n    onSelectedRowsChange,\r\n    clearSelectedRows,\r\n    sortServer,\r\n    onSort,\r\n    persistTableHead,\r\n    sortFunction,\r\n    // Filter out any unknown props that might cause DOM warnings\r\n    ...otherProps\r\n  } = props;\r\n\r\n  // Only pass known DataTable props to avoid DOM warnings\r\n  const dataTableProps = {\r\n    paginationComponentOptions,\r\n    noDataComponent: t(\"NoData\"),\r\n    noHeader: true,\r\n    columns,\r\n    data: data || [],\r\n    dense: true,\r\n    paginationResetDefaultPage: resetPaginationToggle,\r\n    subHeader: subheader,\r\n    progressPending: loading,\r\n    subHeaderComponent,\r\n    pagination: true,\r\n    paginationServer: pagServer,\r\n    paginationPerPage: defaultRowsPerPage ? defaultRowsPerPage : 10,\r\n    paginationRowsPerPageOptions: rowsPerPage ? rowsPerPage : [10, 15, 20, 25, 30],\r\n    paginationTotalRows: totalRows,\r\n    onChangeRowsPerPage: handlePerRowsChange,\r\n    onChangePage: handlePageChange,\r\n    selectableRows,\r\n    onSelectedRowsChange,\r\n    clearSelectedRows,\r\n    progressComponent: <CustomLoader/>,\r\n    sortIcon: <i className=\"sort-icon fas fa-exchange-alt\" />,\r\n    sortServer,\r\n    onSort,\r\n    sortFunction,\r\n    persistTableHead,\r\n    className: \"rki-table\"\r\n  };\r\n  return (\r\n    <DataTable {...dataTableProps} />\r\n  )\r\n};\r\n\r\nRKITable.defaultProps = {\r\n  subHeader: false,\r\n  pagination: true,\r\n  totalRows: null,\r\n  pagServer: true,\r\n  onSelectedRowsChange: null,\r\n  clearSelectedRows: false,\r\n  sortServer: false,\r\n  persistTableHead: false\r\n}\r\n\r\nexport default RKITable;\r\n\r\n", "\n    (window.__NEXT_P = window.__NEXT_P || []).push([\n      \"/project/ProjectsTable\",\n      function () {\n        return require(\"private-next-pages/project/ProjectsTable.tsx\");\n      }\n    ]);\n    if(module.hot) {\n      module.hot.dispose(function () {\n        window.__NEXT_P.push([\"/project/ProjectsTable\"])\n      });\n    }\n  "], "names": ["filterText", "ProjectsTableFilter", "onFilter", "onFilterStatusChange", "onClear", "filterStatus", "status", "setStatus", "useState", "t", "useTranslation", "getProjectStatus", "projectParams", "response", "apiService", "get", "Array", "isArray", "data", "useEffect", "query", "sort", "title", "Container", "fluid", "className", "Row", "Col", "xs", "FormControl", "type", "placeholder", "aria-label", "value", "onChange", "Form", "Group", "as", "controlId", "Label", "column", "sm", "lg", "option", "map", "item", "index", "_id", "partner", "CountryLink", "partner_institutions", "length", "ul", "partner_country", "li", "Link", "href", "ProjectsTable", "router", "useRouter", "setProjects", "selectedRegions", "props", "setFilterText", "React", "setFilterStatus", "resetPaginationToggle", "setResetPaginationToggle", "tabledata", "setDataToTable", "loading", "setLoading", "totalRows", "setTotalRows", "perPage", "setPerPage", "pageNum", "setPageNum", "pageSort", "setPageSort", "created_at", "lean", "limit", "page", "populate", "path", "select", "projP<PERSON><PERSON>", "setProjParams", "columns", "name", "selector", "sortable", "cell", "d", "area_of_work", "join", "getProjectsData", "projectParamsinitial", "country", "totalCount", "handlePerRowsChange", "newPerPage", "handleSort", "sortDirection", "<PERSON><PERSON><PERSON><PERSON>", "q", "handleSearchTitle", "useRef", "_", "Number", "process", "current", "subHeaderComponentMemo", "useMemo", "handleFilterStatusChange", "handleChange", "e", "target", "handleClear", "RKITable", "subheader", "persistTableHead", "onSort", "sortServer", "pagServer", "subHeaderComponent", "handlePageChange", "paginationComponentOptions", "rowsPerPageText", "rowsPerPage", "defaultRowsPerPage", "selectableRows", "onSelectedRowsChange", "clearSelectedRows", "sortFunction", "otherProps", "dataTableProps", "noDataComponent", "<PERSON><PERSON><PERSON><PERSON>", "dense", "paginationResetDefaultPage", "subHeader", "progressPending", "pagination", "paginationServer", "paginationPerPage", "paginationRowsPerPageOptions", "paginationTotalRows", "onChangeRowsPerPage", "onChangePage", "progressComponent", "CustomLoader", "sortIcon", "i", "DataTable", "defaultProps"], "sourceRoot": "", "ignoreList": []}