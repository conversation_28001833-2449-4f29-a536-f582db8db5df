"use strict";(()=>{var e={};e.id=7852,e.ids=[636,3220,7852],e.modules={123:e=>{e.exports=require("dom-helpers/removeEventListener")},1332:e=>{e.exports=require("react-custom-scrollbars-2")},1428:e=>{e.exports=import("axios")},1680:e=>{e.exports=require("@restart/ui/utils")},1919:e=>{e.exports=require("react-transition-group/Transition")},3892:e=>{e.exports=require("classnames")},4048:e=>{e.exports=require("react-truncate")},6009:e=>{e.exports=require("dom-helpers/ownerDocument")},6417:(e,r,t)=>{t.d(r,{A:()=>a});let s=t(82015).createContext(null);s.displayName="CardHeaderContext";let a=s},6952:e=>{e.exports=require("@restart/ui/Modal")},7374:e=>{e.exports=require("@restart/ui/Dropdown")},8732:e=>{e.exports=require("react/jsx-runtime")},9653:e=>{e.exports=require("@restart/ui/Overlay")},11242:e=>{e.exports=require("redux-persist/integration/react")},11688:e=>{e.exports=require("@restart/hooks/useWillUnmount")},12388:e=>{e.exports=require("dom-helpers/addEventListener")},13364:e=>{e.exports=require("redux-saga/effects")},13408:e=>{e.exports=require("@restart/hooks/useUpdateEffect")},14062:e=>{e.exports=import("react-redux")},14078:e=>{e.exports=import("swr")},14332:e=>{e.exports=require("uncontrollable")},16116:e=>{e.exports=require("invariant")},16444:e=>{e.exports=require("moment/locale/fr")},18597:(e,r,t)=>{t.d(r,{A:()=>y});var s=t(3892),a=t.n(s),o=t(82015),i=t(80739),p=t(8732);let u=o.forwardRef(({className:e,bsPrefix:r,as:t="div",...s},o)=>(r=(0,i.oU)(r,"card-body"),(0,p.jsx)(t,{ref:o,className:a()(e,r),...s})));u.displayName="CardBody";let d=o.forwardRef(({className:e,bsPrefix:r,as:t="div",...s},o)=>(r=(0,i.oU)(r,"card-footer"),(0,p.jsx)(t,{ref:o,className:a()(e,r),...s})));d.displayName="CardFooter";var l=t(6417);let x=o.forwardRef(({bsPrefix:e,className:r,as:t="div",...s},u)=>{let d=(0,i.oU)(e,"card-header"),x=(0,o.useMemo)(()=>({cardHeaderBsPrefix:d}),[d]);return(0,p.jsx)(l.A.Provider,{value:x,children:(0,p.jsx)(t,{ref:u,...s,className:a()(r,d)})})});x.displayName="CardHeader";let n=o.forwardRef(({bsPrefix:e,className:r,variant:t,as:s="img",...o},u)=>{let d=(0,i.oU)(e,"card-img");return(0,p.jsx)(s,{ref:u,className:a()(t?`${d}-${t}`:d,r),...o})});n.displayName="CardImg";let c=o.forwardRef(({className:e,bsPrefix:r,as:t="div",...s},o)=>(r=(0,i.oU)(r,"card-img-overlay"),(0,p.jsx)(t,{ref:o,className:a()(e,r),...s})));c.displayName="CardImgOverlay";let m=o.forwardRef(({className:e,bsPrefix:r,as:t="a",...s},o)=>(r=(0,i.oU)(r,"card-link"),(0,p.jsx)(t,{ref:o,className:a()(e,r),...s})));m.displayName="CardLink";var q=t(7783);let f=(0,q.A)("h6"),g=o.forwardRef(({className:e,bsPrefix:r,as:t=f,...s},o)=>(r=(0,i.oU)(r,"card-subtitle"),(0,p.jsx)(t,{ref:o,className:a()(e,r),...s})));g.displayName="CardSubtitle";let h=o.forwardRef(({className:e,bsPrefix:r,as:t="p",...s},o)=>(r=(0,i.oU)(r,"card-text"),(0,p.jsx)(t,{ref:o,className:a()(e,r),...s})));h.displayName="CardText";let v=(0,q.A)("h5"),b=o.forwardRef(({className:e,bsPrefix:r,as:t=v,...s},o)=>(r=(0,i.oU)(r,"card-title"),(0,p.jsx)(t,{ref:o,className:a()(e,r),...s})));b.displayName="CardTitle";let w=o.forwardRef(({bsPrefix:e,className:r,bg:t,text:s,border:o,body:d=!1,children:l,as:x="div",...n},c)=>{let m=(0,i.oU)(e,"card");return(0,p.jsx)(x,{ref:c,...n,className:a()(r,m,t&&`bg-${t}`,s&&`text-${s}`,o&&`border-${o}`),children:d?(0,p.jsx)(u,{children:l}):l})});w.displayName="Card";let y=Object.assign(w,{Img:n,Title:b,Subtitle:g,Body:u,Link:m,Text:h,Header:x,Footer:d,ImgOverlay:c})},18622:e=>{e.exports=require("yup")},19121:e=>{e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},22326:e=>{e.exports=require("react-dom")},22541:e=>{e.exports=require("redux-persist/lib/storage")},25303:e=>{e.exports=require("@restart/ui/NavItem")},26324:e=>{e.exports=require("warning")},27825:e=>{e.exports=require("lodash")},27910:e=>{e.exports=require("stream")},28217:e=>{e.exports=require("@restart/ui/DropdownItem")},29021:e=>{e.exports=require("fs")},29294:e=>{e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},29628:e=>{e.exports=require("dom-helpers/addClass")},29825:e=>{e.exports=require("prop-types")},29841:e=>{e.exports=require("dom-helpers/hasClass")},30362:e=>{e.exports=require("@restart/ui/DropdownToggle")},33873:e=>{e.exports=require("path")},36653:e=>{e.exports=require("nprogress")},36955:e=>{e.exports=require("@restart/hooks/useBreakpoint")},37766:e=>{e.exports=require("@restart/hooks/useMergedRefs")},38609:e=>{e.exports=require("react-data-table-component")},39756:e=>{e.exports=import("redux")},40051:e=>{e.exports=require("dom-helpers/removeClass")},40361:e=>{e.exports=require("next/dist/compiled/next-server/pages.runtime.prod.js")},42051:e=>{e.exports=require("@restart/hooks/useCommittedRef")},42893:e=>{e.exports=import("react-hot-toast")},43294:e=>{e.exports=require("formik")},50009:e=>{e.exports=require("@restart/ui/DropdownMenu")},54131:e=>{e.exports=import("@fortawesome/free-solid-svg-icons")},54837:(e,r,t)=>{t.a(e,async(e,s)=>{try{t.r(r),t.d(r,{config:()=>f,default:()=>n,getServerSideProps:()=>q,getStaticPaths:()=>m,getStaticProps:()=>c,reportWebVitals:()=>g,routeModule:()=>S,unstable_getServerProps:()=>w,unstable_getServerSideProps:()=>y,unstable_getStaticParams:()=>b,unstable_getStaticPaths:()=>v,unstable_getStaticProps:()=>h});var a=t(63885),o=t(80237),i=t(81413),p=t(9616),u=t.n(p),d=t(72386),l=t(88706),x=e([d,l]);[d,l]=x.then?(await x)():x;let n=(0,i.M)(l,"default"),c=(0,i.M)(l,"getStaticProps"),m=(0,i.M)(l,"getStaticPaths"),q=(0,i.M)(l,"getServerSideProps"),f=(0,i.M)(l,"config"),g=(0,i.M)(l,"reportWebVitals"),h=(0,i.M)(l,"unstable_getStaticProps"),v=(0,i.M)(l,"unstable_getStaticPaths"),b=(0,i.M)(l,"unstable_getStaticParams"),w=(0,i.M)(l,"unstable_getServerProps"),y=(0,i.M)(l,"unstable_getServerSideProps"),S=new a.PagesRouteModule({definition:{kind:o.A.PAGES,page:"/vspace/View",pathname:"/vspace/View",bundlePath:"",filename:""},components:{App:d.default,Document:u()},userland:l});s()}catch(e){s(e)}})},57664:e=>{e.exports=require("dom-helpers/scrollbarSize")},58928:e=>{e.exports=require("dom-helpers/canUseDOM")},59717:e=>{e.exports=require("@restart/ui/DropdownContext")},60560:e=>{e.exports=require("dom-helpers/contains")},63033:e=>{e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},64801:e=>{e.exports=require("react-big-calendar")},65447:e=>{e.exports=require("@restart/ui/Button")},67364:e=>{e.exports=require("@restart/hooks/useCallbackRef")},68455:e=>{e.exports=import("redux-saga")},69722:e=>{e.exports=require("es6-promise")},74075:e=>{e.exports=require("zlib")},74716:e=>{e.exports=require("moment")},74987:e=>{e.exports=require("@restart/ui/ModalManager")},78097:e=>{e.exports=require("next-redux-saga")},78634:e=>{e.exports=require("@restart/ui/Anchor")},81149:e=>{e.exports=require("react-responsive-carousel")},81366:e=>{e.exports=require("redux-auth-wrapper/connectedAuthWrapper")},81521:e=>{e.exports=require("dom-helpers/querySelectorAll")},81895:e=>{e.exports=require("@restart/hooks/useEventCallback")},82015:e=>{e.exports=require("react")},82053:e=>{e.exports=require("@fortawesome/react-fontawesome")},86842:e=>{e.exports=require("@restart/ui/SelectableContext")},86843:e=>{e.exports=require("moment/locale/de")},87571:e=>{e.exports=require("dom-helpers/transitionEnd")},88751:e=>{e.exports=require("next-i18next")},93787:e=>{e.exports=require("redux-persist")},94947:e=>{e.exports=require("@restart/hooks/useTimeout")},96196:e=>{e.exports=require("next-redux-wrapper")},98320:e=>{e.exports=require("@restart/hooks/useIsomorphicEffect")},99460:e=>{e.exports=require("dom-helpers/css")}};var r=require("../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[6089,9216,9616,4033,2386,2491,7136,3245,8706],()=>t(54837));module.exports=s})();