"use strict";(()=>{var e={};e.id=4063,e.ids=[636,3220,4063],e.modules={123:e=>{e.exports=require("dom-helpers/removeEventListener")},962:(e,r,t)=>{t.a(e,async(e,a)=>{try{t.r(r),t.d(r,{config:()=>g,default:()=>m,getServerSideProps:()=>h,getStaticPaths:()=>p,getStaticProps:()=>x,reportWebVitals:()=>j,routeModule:()=>q,unstable_getServerProps:()=>v,unstable_getServerSideProps:()=>b,unstable_getStaticParams:()=>y,unstable_getStaticPaths:()=>A,unstable_getStaticProps:()=>f});var s=t(63885),n=t(80237),o=t(81413),l=t(9616),i=t.n(l),c=t(72386),d=t(24532),u=e([c,d]);[c,d]=u.then?(await u)():u;let m=(0,o.M)(d,"default"),x=(0,o.M)(d,"getStaticProps"),p=(0,o.M)(d,"getStaticPaths"),h=(0,o.M)(d,"getServerSideProps"),g=(0,o.M)(d,"config"),j=(0,o.M)(d,"reportWebVitals"),f=(0,o.M)(d,"unstable_getStaticProps"),A=(0,o.M)(d,"unstable_getStaticPaths"),y=(0,o.M)(d,"unstable_getStaticParams"),v=(0,o.M)(d,"unstable_getServerProps"),b=(0,o.M)(d,"unstable_getServerSideProps"),q=new s.PagesRouteModule({definition:{kind:n.A.PAGES,page:"/profile",pathname:"/profile",bundlePath:"",filename:""},components:{App:c.default,Document:i()},userland:d});a()}catch(e){a(e)}})},1332:e=>{e.exports=require("react-custom-scrollbars-2")},1428:e=>{e.exports=import("axios")},1680:e=>{e.exports=require("@restart/ui/utils")},1919:e=>{e.exports=require("react-transition-group/Transition")},3892:e=>{e.exports=require("classnames")},4048:e=>{e.exports=require("react-truncate")},6009:e=>{e.exports=require("dom-helpers/ownerDocument")},6417:(e,r,t)=>{t.d(r,{A:()=>s});let a=t(82015).createContext(null);a.displayName="CardHeaderContext";let s=a},6952:e=>{e.exports=require("@restart/ui/Modal")},7374:e=>{e.exports=require("@restart/ui/Dropdown")},8732:e=>{e.exports=require("react/jsx-runtime")},9653:e=>{e.exports=require("@restart/ui/Overlay")},11e3:e=>{e.exports=require("react-multi-select-component")},11242:e=>{e.exports=require("redux-persist/integration/react")},11688:e=>{e.exports=require("@restart/hooks/useWillUnmount")},12103:e=>{e.exports=require("react-avatar-editor")},12388:e=>{e.exports=require("dom-helpers/addEventListener")},13364:e=>{e.exports=require("redux-saga/effects")},14062:e=>{e.exports=import("react-redux")},14078:e=>{e.exports=import("swr")},14332:e=>{e.exports=require("uncontrollable")},16116:e=>{e.exports=require("invariant")},18597:(e,r,t)=>{t.d(r,{A:()=>b});var a=t(3892),s=t.n(a),n=t(82015),o=t(80739),l=t(8732);let i=n.forwardRef(({className:e,bsPrefix:r,as:t="div",...a},n)=>(r=(0,o.oU)(r,"card-body"),(0,l.jsx)(t,{ref:n,className:s()(e,r),...a})));i.displayName="CardBody";let c=n.forwardRef(({className:e,bsPrefix:r,as:t="div",...a},n)=>(r=(0,o.oU)(r,"card-footer"),(0,l.jsx)(t,{ref:n,className:s()(e,r),...a})));c.displayName="CardFooter";var d=t(6417);let u=n.forwardRef(({bsPrefix:e,className:r,as:t="div",...a},i)=>{let c=(0,o.oU)(e,"card-header"),u=(0,n.useMemo)(()=>({cardHeaderBsPrefix:c}),[c]);return(0,l.jsx)(d.A.Provider,{value:u,children:(0,l.jsx)(t,{ref:i,...a,className:s()(r,c)})})});u.displayName="CardHeader";let m=n.forwardRef(({bsPrefix:e,className:r,variant:t,as:a="img",...n},i)=>{let c=(0,o.oU)(e,"card-img");return(0,l.jsx)(a,{ref:i,className:s()(t?`${c}-${t}`:c,r),...n})});m.displayName="CardImg";let x=n.forwardRef(({className:e,bsPrefix:r,as:t="div",...a},n)=>(r=(0,o.oU)(r,"card-img-overlay"),(0,l.jsx)(t,{ref:n,className:s()(e,r),...a})));x.displayName="CardImgOverlay";let p=n.forwardRef(({className:e,bsPrefix:r,as:t="a",...a},n)=>(r=(0,o.oU)(r,"card-link"),(0,l.jsx)(t,{ref:n,className:s()(e,r),...a})));p.displayName="CardLink";var h=t(7783);let g=(0,h.A)("h6"),j=n.forwardRef(({className:e,bsPrefix:r,as:t=g,...a},n)=>(r=(0,o.oU)(r,"card-subtitle"),(0,l.jsx)(t,{ref:n,className:s()(e,r),...a})));j.displayName="CardSubtitle";let f=n.forwardRef(({className:e,bsPrefix:r,as:t="p",...a},n)=>(r=(0,o.oU)(r,"card-text"),(0,l.jsx)(t,{ref:n,className:s()(e,r),...a})));f.displayName="CardText";let A=(0,h.A)("h5"),y=n.forwardRef(({className:e,bsPrefix:r,as:t=A,...a},n)=>(r=(0,o.oU)(r,"card-title"),(0,l.jsx)(t,{ref:n,className:s()(e,r),...a})));y.displayName="CardTitle";let v=n.forwardRef(({bsPrefix:e,className:r,bg:t,text:a,border:n,body:c=!1,children:d,as:u="div",...m},x)=>{let p=(0,o.oU)(e,"card");return(0,l.jsx)(u,{ref:x,...m,className:s()(r,p,t&&`bg-${t}`,a&&`text-${a}`,n&&`border-${n}`),children:c?(0,l.jsx)(i,{children:d}):d})});v.displayName="Card";let b=Object.assign(v,{Img:m,Title:y,Subtitle:j,Body:i,Link:p,Text:f,Header:u,Footer:c,ImgOverlay:x})},18622:e=>{e.exports=require("yup")},19121:e=>{e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},19442:e=>{e.exports=require("react-bootstrap-range-slider")},22326:e=>{e.exports=require("react-dom")},22541:e=>{e.exports=require("redux-persist/lib/storage")},24250:(e,r,t)=>{t.a(e,async(e,a)=>{try{t.d(r,{A:()=>f});var s=t(8732),n=t(82015),o=t(12103),l=t.n(o),i=t(19442),c=t.n(i),d=t(12403),u=t(83551),m=t(49481),x=t(91353),p=t(42893),h=t(63487),g=t(88751),j=e([p,h]);[p,h]=j.then?(await j)():j;let f=({isOpen:e,onModalClose:r,image:t})=>{let{t:a}=(0,g.useTranslation)("common"),o=a("setInfo.Choosefile"),[i,j]=(0,n.useState)(1),[f,A]=(0,n.useState)(0),[y,v]=(0,n.useState)(""),[b,q]=(0,n.useState)(null),w=(0,n.useRef)(null),N=async()=>{let e=w.current.getImageScaledToCanvas().toDataURL("image/jpeg",.8),t=(e=>{let r=e.split(","),t=r[0].match(/:(.*?);/),a=t?t[1]:"",s=atob(r[1]),n=s.length,o=new Uint8Array(n);for(;n--;)o[n]=s.charCodeAt(n);return new Blob([o],{type:a})})(e),s=new FormData;s.append("file",t,y);try{let e=await h.A.post("/image",s,{"Content-Type":"multipart/form-data"});e&&e._id&&await h.A.post("/users/updateProfile",{image:e._id})&&p.default.success(a("setInfo.ProfileUpdatedSuccessfully"))}catch(e){throw e instanceof Error?e:Error("Unknown error occurred")}r(!1),q(null),v(o),j(1)};return(0,s.jsx)(s.Fragment,{children:(0,s.jsx)("div",{children:(0,s.jsxs)(d.A,{show:e,size:"lg","aria-labelledby":"ProfileEdit",onHide:()=>r(!1),centered:!0,children:[(0,s.jsx)(d.A.Header,{children:(0,s.jsx)(d.A.Title,{id:"contained-modal-title-vcenter",children:a("setInfo.EditYourImage")})}),(0,s.jsxs)(d.A.Body,{children:[(0,s.jsx)("div",{className:"d-flex flex-column justify-content-center align-items-center",children:(0,s.jsx)(l(),{ref:w,borderRadius:100,scale:i,rotate:f,color:[0,0,0,.6],image:b||"/images/rkiProfile.jpg"})}),(0,s.jsx)("div",{className:"my-3 mx-2",children:(0,s.jsxs)(u.A,{className:"align-items-center mb-4",children:[(0,s.jsx)(m.A,{sm:2,md:2,lg:2,className:"pe-0",children:(0,s.jsx)("b",{children:a("setInfo.Uploadaimage")})}),(0,s.jsx)(m.A,{sm:10,md:10,lg:10,children:(0,s.jsxs)("div",{className:"form-control custom-file position-relative",children:[(0,s.jsx)("input",{type:"file",name:"files",className:"form-control-input form-control custom-file-input",accept:"image/*",id:"customFile",onChange:e=>{e.target.files&&e.target.files[0]&&(v(e.target.files[0].name),q(URL.createObjectURL(e.target.files[0])))}}),(0,s.jsx)("label",{className:"custom-file-label form-control-label w-100","data-browse":a("Browse"),children:a("setInfo.Choosefile")})]})})]})}),(0,s.jsx)("div",{className:"my-3 mx-2",children:(0,s.jsxs)(u.A,{children:[(0,s.jsx)(m.A,{sm:2,md:2,lg:2,className:"pe-0",children:(0,s.jsx)("b",{children:a("setInfo.Zoom")})}),(0,s.jsx)(m.A,{sm:4,md:4,lg:4,children:(0,s.jsx)(c(),{value:i,tooltip:"auto",min:1,max:10,step:.1,variant:"primary",onChange:e=>j(Number(e.target.value))})}),(0,s.jsx)(m.A,{sm:2,md:2,lg:2,className:"pe-0",children:(0,s.jsx)("b",{children:a("setInfo.Rotate")})}),(0,s.jsx)(m.A,{sm:4,md:4,lg:4,children:(0,s.jsx)(c(),{value:f,tooltip:"auto",tooltipLabel:e=>`${e}\xb0`,min:0,max:360,variant:"primary",onChange:e=>A(Number(e.target.value))})})]})})]}),(0,s.jsxs)(d.A.Footer,{children:[(0,s.jsx)(x.A,{onClick:N,children:a("setInfo.SaveChanges")}),(0,s.jsx)(x.A,{variant:"danger",onClick:()=>{r(!1),q(null)},children:a("Cancel")})]})]})})})};a()}catch(e){a(e)}})},24532:(e,r,t)=>{t.a(e,async(e,a)=>{try{t.r(r),t.d(r,{default:()=>S,getStaticProps:()=>C});var s=t(8732),n=t(82015),o=t(14062),l=t(7082),i=t(83551),c=t(49481),d=t(18597),u=t(13524),m=t(59549),x=t(91353),p=t(82053),h=t(54131),g=t(4982),j=t(88396),f=t(52508),A=t(49856),y=t(63487),v=t(88751),b=t(35576),q=t(24250),w=t(27053),N=e([o,h,g,j,f,y,q]);async function C({locale:e}){return{props:{...await (0,b.serverSideTranslations)(e,["common"])}}}[o,h,g,j,f,y,q]=N.then?(await N)():N;let S=(0,o.connect)()(e=>{let{t:r}=(0,v.useTranslation)("common"),[t,a]=(0,n.useState)(!1),[o,b]=(0,n.useState)(!1),[N,C]=(0,n.useState)(!1),S=e=>{a(e),b(!1)},[k,P]=(0,n.useState)({username:"",institution:"",email:"",image:null,firstname:"",lastname:"",position:""});(0,n.useEffect)(()=>{(async e=>{let r=await y.A.post("/users/getLoggedUser",e);r&&(r.image=r.image&&r.image._id?`http://localhost:3001/api/v1/image/show/${r.image._id}`:"/images/rkiProfile.jpg",P(e=>({...e,...r})))})({})},[t,N]);let I=r=>{e.dispatch((0,A.js)()),C(r)};return(0,s.jsxs)(l.A,{fluid:!0,className:"p-0",children:[(0,s.jsx)(i.A,{children:(0,s.jsx)(c.A,{xs:12,children:(0,s.jsx)(w.A,{title:r("setInfo.myprofile")})})}),(0,s.jsx)(i.A,{children:(0,s.jsx)(c.A,{xs:!0,lg:12,children:(0,s.jsx)(l.A,{fluid:!0,children:(0,s.jsx)(i.A,{children:(0,s.jsx)(c.A,{children:(0,s.jsxs)(d.A,{className:" mt-3 form--outline profile--card",children:[(0,s.jsxs)("div",{className:"row mx-3 mt-4",children:[(0,s.jsx)("div",{className:"col-lg-3 col-md-4 text-center",children:k.image?(0,s.jsx)(s.Fragment,{children:(0,s.jsx)("img",{className:"imgStyle",src:k&&k.image?k.image:""})}):(0,s.jsx)(u.A,{className:"text-center m-5",animation:"grow",variant:"dark"})}),(0,s.jsx)("div",{className:"col-md-9 w-100",children:(0,s.jsx)(m.A,{children:(0,s.jsxs)("div",{children:[(0,s.jsxs)(m.A.Group,{as:i.A,controlId:"username",className:"align-items-center mb-3",children:[(0,s.jsx)(m.A.Label,{column:!0,md:"3",xs:"5",lg:"2",className:"px-1",children:r("setInfo.username")}),(0,s.jsx)(c.A,{xs:"1",children:(0,s.jsx)("span",{children:":"})}),(0,s.jsx)(c.A,{md:"8",xs:"5",lg:"9",children:(0,s.jsx)(m.A.Control,{plaintext:!0,readOnly:!0,defaultValue:k.username})})]}),(0,s.jsxs)(m.A.Group,{as:i.A,controlId:"name",className:"align-items-center mb-3",children:[(0,s.jsx)(m.A.Label,{column:!0,md:"3",xs:"5",lg:"2",className:"px-1",children:r("setInfo.name")}),(0,s.jsx)(c.A,{xs:"1",children:(0,s.jsx)("span",{children:":"})}),(0,s.jsx)(c.A,{md:"8",xs:"5",lg:"9",children:(0,s.jsx)(m.A.Control,{plaintext:!0,readOnly:!0,defaultValue:k&&k.firstname?`${k.firstname} ${k.lastname}`:""})})]}),(0,s.jsxs)(m.A.Group,{as:i.A,controlId:"position",className:"align-items-center mb-3",children:[(0,s.jsx)(m.A.Label,{column:!0,md:"3",xs:"5",lg:"2",className:"px-1",children:r("setInfo.position")}),(0,s.jsx)(c.A,{xs:"1",children:(0,s.jsx)("span",{children:":"})}),(0,s.jsx)(c.A,{md:"8",xs:"5",lg:"9",children:(0,s.jsx)(m.A.Control,{plaintext:!0,readOnly:!0,defaultValue:k&&k.position?k.position:""})})]}),(0,s.jsxs)(m.A.Group,{as:i.A,controlId:"Organization",className:"align-items-center mb-3",children:[(0,s.jsx)(m.A.Label,{column:!0,md:"3",xs:"5",lg:"2",className:"px-1",children:r("setInfo.organisation")}),(0,s.jsx)(c.A,{xs:"1",children:(0,s.jsx)("span",{children:":"})}),(0,s.jsx)(c.A,{md:"8",xs:"5",lg:"9",children:(0,s.jsx)(m.A.Control,{readOnly:!0,plaintext:!0,defaultValue:k.institution&&k.institution.title})})]}),(0,s.jsx)(q.A,{image:k.image,isOpen:N,onModalClose:e=>I(e)}),(0,s.jsxs)(m.A.Group,{as:i.A,controlId:"Email",className:"align-items-center mb-3",children:[(0,s.jsx)(m.A.Label,{column:!0,md:"3",xs:"5",lg:"2",className:"px-1",children:r("setInfo.email")}),(0,s.jsx)(c.A,{xs:"1",children:(0,s.jsx)("span",{children:":"})}),(0,s.jsx)(c.A,{md:"8",xs:"5",lg:"9",children:(0,s.jsx)(m.A.Control,{plaintext:!0,readOnly:!0,defaultValue:k.email})})]}),(0,s.jsxs)(m.A.Group,{as:i.A,controlId:"consent",className:"align-items-center mb-3",children:[(0,s.jsx)(m.A.Label,{column:!0,md:"3",xs:"5",lg:"2",className:"px-1",children:r("setInfo.Consent")}),(0,s.jsx)(c.A,{xs:"1",children:(0,s.jsx)("span",{children:":"})}),(0,s.jsx)(c.A,{md:"8",xs:"5",lg:"9",children:(0,s.jsx)(p.FontAwesomeIcon,{className:"clickable",icon:h.faCheckSquare,color:"#293F92",onClick:()=>b(!0)})})]})]})})})]}),(0,s.jsxs)("div",{className:"mb-3 mx-3",children:[(0,s.jsxs)(x.A,{onClick:()=>{C(!0)},variant:"secondary",children:[(0,s.jsx)(p.FontAwesomeIcon,{icon:h.faEdit}),"\xa0",r("setInfo.editmyimage")]}),(0,s.jsxs)(x.A,{className:"float-right",onClick:()=>a(!0),variant:"secondary",children:[(0,s.jsx)(p.FontAwesomeIcon,{icon:h.faEdit}),"\xa0",r("setInfo.editmyprofile")]})]})]})})})})})}),t&&(0,s.jsx)(g.default,{data:k,isOpen:t,manageClose:e=>S(e)}),(0,s.jsxs)(c.A,{xs:12,className:"mt-3",children:[(0,s.jsx)(w.A,{title:r("MyBookmarks")}),(0,s.jsx)(f.default,{})]}),o&&(0,s.jsx)(j.default,{isOpen:o,manageClose:e=>S(e),id:k._id})]})});a()}catch(e){a(e)}})},25169:(e,r,t)=>{t.d(r,{A:()=>y});var a=t(3892),s=t.n(a),n=t(82015),o=t(14332),l=t(81895),i=t.n(l),c=t(80739),d=t(7783),u=t(8732);let m=(0,d.A)("h4");m.displayName="DivStyledAsH4";let x=n.forwardRef(({className:e,bsPrefix:r,as:t=m,...a},n)=>(r=(0,c.oU)(r,"alert-heading"),(0,u.jsx)(t,{ref:n,className:s()(e,r),...a})));x.displayName="AlertHeading";var p=t(78634),h=t.n(p);let g=n.forwardRef(({className:e,bsPrefix:r,as:t=h(),...a},n)=>(r=(0,c.oU)(r,"alert-link"),(0,u.jsx)(t,{ref:n,className:s()(e,r),...a})));g.displayName="AlertLink";var j=t(19799),f=t(73087);let A=n.forwardRef((e,r)=>{let{bsPrefix:t,show:a=!0,closeLabel:n="Close alert",closeVariant:l,className:d,children:m,variant:x="primary",onClose:p,dismissible:h,transition:g=j.A,...A}=(0,o.useUncontrolled)(e,{show:"onClose"}),y=(0,c.oU)(t,"alert"),v=i()(e=>{p&&p(!1,e)}),b=!0===g?j.A:g,q=(0,u.jsxs)("div",{role:"alert",...!b?A:void 0,ref:r,className:s()(d,y,x&&`${y}-${x}`,h&&`${y}-dismissible`),children:[h&&(0,u.jsx)(f.A,{onClick:v,"aria-label":n,variant:l}),m]});return b?(0,u.jsx)(b,{unmountOnExit:!0,...A,ref:void 0,in:a,children:q}):a?q:null});A.displayName="Alert";let y=Object.assign(A,{Link:g,Heading:x})},25303:e=>{e.exports=require("@restart/ui/NavItem")},26324:e=>{e.exports=require("warning")},27053:(e,r,t)=>{t.d(r,{A:()=>s});var a=t(8732);function s(e){return(0,a.jsx)("h2",{className:"page-heading",children:e.title})}},27825:e=>{e.exports=require("lodash")},27910:e=>{e.exports=require("stream")},28217:e=>{e.exports=require("@restart/ui/DropdownItem")},29021:e=>{e.exports=require("fs")},29294:e=>{e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},29628:e=>{e.exports=require("dom-helpers/addClass")},29825:e=>{e.exports=require("prop-types")},29841:e=>{e.exports=require("dom-helpers/hasClass")},30362:e=>{e.exports=require("@restart/ui/DropdownToggle")},33873:e=>{e.exports=require("path")},35576:e=>{e.exports=require("next-i18next/serverSideTranslations")},36653:e=>{e.exports=require("nprogress")},36955:e=>{e.exports=require("@restart/hooks/useBreakpoint")},37766:e=>{e.exports=require("@restart/hooks/useMergedRefs")},38609:e=>{e.exports=require("react-data-table-component")},39756:e=>{e.exports=import("redux")},40051:e=>{e.exports=require("dom-helpers/removeClass")},40361:e=>{e.exports=require("next/dist/compiled/next-server/pages.runtime.prod.js")},42893:e=>{e.exports=import("react-hot-toast")},43294:e=>{e.exports=require("formik")},50009:e=>{e.exports=require("@restart/ui/DropdownMenu")},52508:(e,r,t)=>{t.a(e,async(e,a)=>{try{t.r(r),t.d(r,{default:()=>A});var s=t(8732),n=t(82015),o=t.n(n),l=t(14062),i=t(27825),c=t.n(i),d=t(19918),u=t.n(d),m=t(12403),x=t(91353),p=t(62706),h=t(56084),g=t(63487),j=t(88751),f=e([l,g]);[l,g]=f.then?(await f)():f;let A=(0,l.connect)(e=>e)(e=>{let r=[{value:"institution",label:"Organisations"},{value:"operation",label:"Operations"},{value:"project",label:"Projects"},{value:"event",label:"Events"},{value:"vspace",label:"Virtual Spaces"}],[t,a]=(0,n.useState)([]),[,l]=(0,n.useState)(!1),[i,d]=(0,n.useState)(!1),[f,A]=(0,n.useState)(0),[y,v]=(0,n.useState)(10),[b,q]=(0,n.useState)(""),[w,N]=(0,n.useState)(!1),[C,S]=(0,n.useState)(r),[k,P]=(0,n.useState)({}),{t:I}=(0,j.useTranslation)("common"),R=async e=>{e&&e._id&&P(e._id),d(!0)},_={sort:{created_at:"asc"},populate:{path:"entity_id",select:"title"},lean:!0,limit:y,page:1,query:{user:e.user&&e.user._id?e.user._id:""}},T=[{name:I("Title"),selector:"",cell:e=>e.entity_id&&e.entity_id.title?(0,s.jsx)(u(),{href:`/${e.entity_type}/[...routes]`,as:`/${e.entity_type}/show/${e.entity_id._id}`,children:e.entity_id.title}):""},{name:I("Group"),selector:"group",cell:e=>e.onModel&&"Institution"===e.onModel?"Organisation":e.onModel},{name:I("Remove"),selector:"",cell:e=>(0,s.jsx)("div",{onClick:()=>R(e),style:{cursor:"pointer"},children:(0,s.jsx)("i",{className:"icon fas fa-trash-alt"})})}],M=async()=>{l(!0);let e=await g.A.get("/flag",_);e&&e.data&&(a(e.data),A(e.totalCount),l(!1))},U=async(e,r)=>{_.limit=e,_.page=r,l(!0);let t=c().map(C,"value");t&&t.length>0&&(_.query={..._.query,entity_type:t});let s=await g.A.get("/flag",_);s&&s.data&&s.data.length>0&&(a(s.data),v(e),l(!1))};(0,n.useEffect)(()=>{_.page=1,M()},[]);let O=o().useMemo(()=>{let e=e=>{e&&(_.populate.match={title:{$regex:e}},M())},t=c().debounce(r=>e(r),Number("500")||300);return(0,s.jsx)(p.default,{onFilter:e=>{q(e.target.value),t(e.target.value)},onClear:()=>{b&&(N(!w),q(""))},filterText:b,handleGroupHandler:e=>{S(e);let r=c().map(e,"value");0===r.length?a([]):(_.query={..._.query,entity_type:r},M())},groupType:C,options:r})},[b,C,w]),H=()=>d(!1),$=async()=>{if(d(!1),await g.A.remove(`/flag/${k}`),C&&Array.isArray(C)){let e=c().map(C,"value");e&&e.length>0&&(_.query={..._.query,entity_type:e})}M()};return(0,s.jsxs)("div",{className:"my-bookmark-table",children:[(0,s.jsxs)(m.A,{show:i,onHide:H,children:[(0,s.jsx)(m.A.Header,{closeButton:!0,children:(0,s.jsx)(m.A.Title,{children:I("Removebookmark")})}),(0,s.jsx)(m.A.Body,{children:I("Areyousurewanttoremovefromyourbookmark")}),(0,s.jsxs)(m.A.Footer,{children:[(0,s.jsx)(x.A,{variant:"secondary",onClick:H,children:I("Cancel")}),(0,s.jsx)(x.A,{variant:"primary",onClick:$,children:I("bookmarkDeleteYes")})]})]}),(0,s.jsx)(h.A,{columns:T,data:t,totalRows:f,subheader:!0,pagServer:!0,resetPaginationToggle:w,subHeaderComponent:O,handlePerRowsChange:U,handlePageChange:e=>{_.limit=y,_.page=e,""!==b&&(_.query={title:b});let r=c().map(C,"value");r&&r.length>0&&(_.query={..._.query,entity_type:r}),M()}})]})});a()}catch(e){a(e)}})},54131:e=>{e.exports=import("@fortawesome/free-solid-svg-icons")},56084:(e,r,t)=>{t.d(r,{A:()=>c});var a=t(8732);t(82015);var s=t(38609),n=t.n(s),o=t(88751),l=t(30370);function i(e){let{t:r}=(0,o.useTranslation)("common"),t={rowsPerPageText:r("Rowsperpage")},{columns:s,data:i,totalRows:c,resetPaginationToggle:d,subheader:u,subHeaderComponent:m,handlePerRowsChange:x,handlePageChange:p,rowsPerPage:h,defaultRowsPerPage:g,selectableRows:j,loading:f,pagServer:A,onSelectedRowsChange:y,clearSelectedRows:v,sortServer:b,onSort:q,persistTableHead:w,sortFunction:N,...C}=e,S={paginationComponentOptions:t,noDataComponent:r("NoData"),noHeader:!0,columns:s,data:i||[],dense:!0,paginationResetDefaultPage:d,subHeader:u,progressPending:f,subHeaderComponent:m,pagination:!0,paginationServer:A,paginationPerPage:g||10,paginationRowsPerPageOptions:h||[10,15,20,25,30],paginationTotalRows:c,onChangeRowsPerPage:x,onChangePage:p,selectableRows:j,onSelectedRowsChange:y,clearSelectedRows:v,progressComponent:(0,a.jsx)(l.A,{}),sortIcon:(0,a.jsx)("i",{className:"sort-icon fas fa-exchange-alt"}),sortServer:b,onSort:q,sortFunction:N,persistTableHead:w,className:"rki-table"};return(0,a.jsx)(n(),{...S})}i.defaultProps={subHeader:!1,pagination:!0,totalRows:null,pagServer:!0,onSelectedRowsChange:null,clearSelectedRows:!1,sortServer:!1,persistTableHead:!1};let c=i},57664:e=>{e.exports=require("dom-helpers/scrollbarSize")},58928:e=>{e.exports=require("dom-helpers/canUseDOM")},59717:e=>{e.exports=require("@restart/ui/DropdownContext")},60560:e=>{e.exports=require("dom-helpers/contains")},62706:(e,r,t)=>{t.r(r),t.d(r,{default:()=>d});var a=t(8732);t(82015);var s=t(7082),n=t(83551),o=t(49481),l=t(59549),i=t(11e3),c=t(88751);let d=({filterText:e,onFilter:r,onClear:t,handleGroupHandler:d,groupType:u,options:m})=>{let{t:x}=(0,c.useTranslation)("common");return(0,a.jsx)(s.A,{fluid:!0,className:"p-0",children:(0,a.jsx)(n.A,{children:(0,a.jsx)(o.A,{xs:4,md:4,lg:4,children:(0,a.jsx)(l.A.Group,{style:{maxWidth:"800px"},children:(0,a.jsx)(i.MultiSelect,{overrideStrings:{selectSomeItems:x("ChooseGroup"),allItemsAreSelected:"All Groups are Selected"},options:m,value:u,onChange:d,className:"choose-group",labelledBy:"Select Network"})})})})})}},63033:e=>{e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},65447:e=>{e.exports=require("@restart/ui/Button")},67364:e=>{e.exports=require("@restart/hooks/useCallbackRef")},68455:e=>{e.exports=import("redux-saga")},69722:e=>{e.exports=require("es6-promise")},74075:e=>{e.exports=require("zlib")},74716:e=>{e.exports=require("moment")},74987:e=>{e.exports=require("@restart/ui/ModalManager")},78097:e=>{e.exports=require("next-redux-saga")},78634:e=>{e.exports=require("@restart/ui/Anchor")},81366:e=>{e.exports=require("redux-auth-wrapper/connectedAuthWrapper")},81521:e=>{e.exports=require("dom-helpers/querySelectorAll")},81895:e=>{e.exports=require("@restart/hooks/useEventCallback")},82015:e=>{e.exports=require("react")},82053:e=>{e.exports=require("@fortawesome/react-fontawesome")},82601:(e,r,t)=>{t.a(e,async(e,a)=>{try{t.r(r),t.d(r,{default:()=>p});var s=t(8732);t(82015);var n=t(12403),o=t(91353),l=t(82053),i=t(54131),c=t(44233),d=t.n(c),u=t(63487),m=t(88751),x=e([i,u]);[i,u]=x.then?(await x)():x;let p=e=>{let{isopen:r,manageDialog:t,userId:a,endpoint:c}=e,{t:x}=(0,m.useTranslation)("common"),p=()=>{t(!1)},h=async()=>{let e;("/users"===c?await u.A.remove(`${c}/${a}`):await u.A.post(`${c}`,{code:a}))&&(t(!1),d().push("/home"))};return(0,s.jsxs)(n.A,{show:r,onHide:p,children:[(0,s.jsx)("div",{className:"text-center p-2",children:(0,s.jsx)(l.FontAwesomeIcon,{icon:i.faExclamationTriangle,size:"5x",color:"indianRed",style:{background:"#d6deec",padding:"19px",borderRadius:"50%",width:"100px",height:"100px"}})}),(0,s.jsx)(n.A.Body,{children:(0,s.jsx)("b",{children:x("AreyousureyouwishtoleavetheKnowledgePlatform")})}),(0,s.jsxs)(n.A.Footer,{children:[(0,s.jsx)(o.A,{variant:"secondary",onClick:p,children:x("No")}),(0,s.jsx)(o.A,{variant:"danger",onClick:h,children:x("YesDeleteMe")})]})]})};a()}catch(e){a(e)}})},86842:e=>{e.exports=require("@restart/ui/SelectableContext")},87571:e=>{e.exports=require("dom-helpers/transitionEnd")},88396:(e,r,t)=>{t.a(e,async(e,a)=>{try{t.r(r),t.d(r,{default:()=>m});var s=t(8732),n=t(82015),o=t(12403),l=t(25169),i=t(59549),c=t(82601),d=t(88751),u=e([c]);c=(u.then?(await u)():u)[0];let m=({isOpen:e,manageClose:r,id:t})=>{let{t:a}=(0,d.useTranslation)("common"),u={consent1:!0,consent2:!0,consent3:!0,consent4:!0},[m,x]=(0,n.useState)(u),[p,h]=(0,n.useState)(!1),g=e=>{let{name:r,checked:t}=e.target;x(e=>({...e,[r]:t})),h(!p)},j=e=>{x(u),h(e)};return(0,s.jsx)(s.Fragment,{children:(0,s.jsxs)(o.A,{show:e,onHide:()=>{r(!1)},size:"xl",id:"main-content",className:"w-100",children:[(0,s.jsx)(o.A.Header,{closeButton:!0,children:(0,s.jsx)(o.A.Title,{children:a("declaration.title")})}),(0,s.jsx)(o.A.Body,{children:(0,s.jsxs)("div",{className:"p-3 w-100",children:[(0,s.jsx)(l.A,{variant:"danger",children:a("declaration.info")}),(0,s.jsx)(i.A.Check,{className:"pb-4",type:"checkbox",name:"consent1",onChange:g,checked:m.consent1,value:"consent1",label:a("declaration.consent1")}),(0,s.jsx)(i.A.Check,{className:"pb-4",name:"consent2",onChange:g,type:"checkbox",checked:m.consent2,value:"consent2",label:a("declaration.consent2")}),(0,s.jsx)(i.A.Check,{className:"pb-4",name:"consent3",onChange:g,type:"checkbox",checked:m.consent3,value:"consent3",label:a("declaration.consent3")}),(0,s.jsx)(i.A.Check,{className:"pb-4",type:"checkbox",name:"consent4",value:"consent4",label:a("declaration.consent4"),checked:m.consent4,onChange:g})]})}),(0,s.jsx)(c.default,{endpoint:"/users",userId:t||"",isopen:p,manageDialog:e=>j(e)})]})})};a()}catch(e){a(e)}})},88751:e=>{e.exports=require("next-i18next")},93787:e=>{e.exports=require("redux-persist")},94947:e=>{e.exports=require("@restart/hooks/useTimeout")},96196:e=>{e.exports=require("next-redux-wrapper")},98320:e=>{e.exports=require("@restart/hooks/useIsomorphicEffect")},99460:e=>{e.exports=require("dom-helpers/css")}};var r=require("../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),a=r.X(0,[6089,9216,9616,2386,49],()=>t(962));module.exports=a})();