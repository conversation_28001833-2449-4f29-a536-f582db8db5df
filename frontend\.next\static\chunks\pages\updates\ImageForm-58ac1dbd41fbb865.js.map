{"version": 3, "file": "static/chunks/pages/updates/ImageForm-58ac1dbd41fbb865.js", "mappings": "+EACA,4CACA,qBACA,WACA,OAAe,EAAQ,KAA0C,CACjE,EACA,SAFsB,wICqCtB,MAxBkB,IAChB,GAAM,CAAEA,CAAC,CAAE,CAAGC,CAAAA,EAAAA,EAAAA,EAAAA,CAAcA,CAAC,QAuBhBC,EArBPC,EAAQ,IACZC,CAoBoBF,CApBdG,CAoBe,IApBV,CAACC,EACd,EAEMC,EAAgB,IACpBH,EAAMI,mBAAmB,CAACC,EAC5B,EAEA,MACE,UAACC,EAAAA,CAASA,CAAAA,CAACC,UAAU,WAAWC,KAAK,aACnC,WAACC,EAAAA,CAAGA,CAAAA,WACF,UAACC,EAAAA,CAAGA,CAAAA,CAACH,UAAU,eAAeI,GAAI,YAChC,UAACC,KAAAA,UAAG,UAACC,OAAAA,UAAMjB,EAAE,sBAEf,UAACc,EAAAA,CAAGA,CAAAA,UACF,UAACI,EAAAA,CAAaA,CAAAA,CAACC,MAAOf,EAAMgB,IAAI,CAAEC,QAASjB,EAAMkB,MAAM,CAAEC,SAAU,GAAepB,EAAMG,GAAKkB,eAAgB,GAAsBjB,EAAcE,WAK3J,6MCfA,IAAIgB,EAAmB,EAAE,CAEnBC,EAAiB,CACrBC,KAAM,EACNC,QAAS,OACTC,cAAe,SACfC,WAAY,SACZC,eAAgB,SAChBC,MAAO,OACPC,OAAQ,OACRC,YAAa,GACbC,YAAa,UACbC,gBAAiB,UACjBC,MAAO,QACPC,WAAY,2BACZC,QAAS,MACX,EAYMC,EAAuB,CAC3BZ,QAAS,OACTW,QAAS,OACTP,MAAO,OACPS,OAAQ,iBACRZ,cAAe,SACfE,eAAgB,aAChBW,SAAU,OACVC,UAAW,EACb,EAcMC,EAAM,CACVZ,MAAO,OACT,EAEMa,EAAmB,CACvBV,YAAa,SACf,EA4WA,EA1WsB,IACpB,IAmSIW,EAnSE,GAAE9C,CAAC,CAAE,CAAGC,CAAAA,EAAAA,EAAAA,EAAAA,CAAcA,CAAC,GAyWhBiB,OAxWP,CAAC6B,EAAWC,EAAa,CAAGC,CAAAA,CAwWP,CAxWOA,EAAAA,QAAAA,CAAQA,EAAC,GACrC,CAACC,EAAYC,EAAc,CAAGF,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,GACtCG,EACJhD,iBAAMiD,IAAI,CAAoB,UAAWC,UAAwB,CAC7D,CAACC,EAAOC,EAAS,CAAGP,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAQ,EAAE,EACtC,CAACQ,EAAOC,EAAS,CAAGT,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,EAAC,GAC7B,CAACU,EAAaC,EAAe,CAAGX,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAW,EAAE,EAErDY,EAAWzD,GAAwB,gBAAfA,EAAMiD,IAAI,CAAqB,SAAW,SAC9DS,EAAc,MAAOxD,IACZ,MAAMyD,EAAAA,CAAUA,CAACC,MAAM,CAAC,GAAe1D,MAAAA,CAAZuD,EAAS,KAAM,OAAHvD,GACtD,EAEM2D,EAAcC,IAClBf,EAAce,GACdlB,GAAa,EACf,EAEMmB,EAAe,CAACC,EAA8DC,KAClF,IAAMC,EAAQ,IAAIX,EAAY,CAC9BW,CAAK,CAACD,EAAM,CAAGD,EAAEG,MAAM,CAACC,KAAK,CAC7BZ,EAAeU,EACjB,EAEMG,EAAe,IAEnB,OADiBP,GAAQA,EAAKQ,IAAI,CAACC,KAAK,CAAC,KAAKC,GAAG,IAE/C,IAAK,MACL,IAAK,MACL,IAAK,OACL,IAAK,MACL,IAAK,MACH,MAAO,UAAChC,MAAAA,CAAIiC,IAAKX,EAAKY,OAAO,CAAEC,MAAOnC,GACxC,KAAK,MACH,MACE,UAACA,MAAAA,CACCiC,IAAI,gCACJlE,UACiB,gBAAfP,EAAMiD,IAAI,CAAqB,aAAe,cAItD,KAAK,OAmBL,QAlBE,MACE,UAACT,MAAAA,CACCiC,IAAI,iCACJlE,UACiB,gBAAfP,EAAMiD,IAAI,CAAqB,aAAe,cAItD,KAAK,MACL,IAAK,OACH,MACE,UAACT,MAAAA,CACCiC,IAAI,gCACJlE,UACiB,gBAAfP,EAAMiD,IAAI,CAAqB,aAAe,cAaxD,CACF,EAEM2B,EAAY,IAAMhC,GAAa,GAE/BiC,EAAgB,KACpBjC,EAAa,GACf,EAEMkC,EAAgB,IAEpB,IAAMC,EACJC,CAFFA,EAAelC,CAAAA,GAEGkC,EAAaC,GAAG,CAC5B,CAAEC,SAAUF,EAAaC,GAAG,EAC5B,CAAEnB,KAAMkB,CAAa,EACrBG,EAASC,IAAAA,SAAW,CAAC/D,EAAM0D,GAE3BM,EAAY,IAAI9B,EAAY,CAClC8B,EAAUC,MAAM,CAACH,EAAQ,GACzB3B,EAAe6B,GAEf3B,EAAYrC,CAAI,CAAC8D,EAAO,CAACD,QAAQ,EACjC7D,EAAKiE,MAAM,CAACH,EAAQ,GACpBnF,EAAMmB,QAAQ,CAACE,EAAMrB,EAAMiE,KAAK,CAAGjE,EAAMiE,KAAK,CAAG,GACjD,IAAMsB,EAAW,IAAIpC,EAAM,CAC3BoC,EAASD,MAAM,CAACC,EAASC,OAAO,CAACR,GAAe,GAChD5B,EAASmC,GACT3C,GAAa,EACf,EAEM6C,EAActC,EAAMuC,GAAG,CAAC,CAAC5B,EAAW6B,IAEtC,WAACC,MAAAA,WACC,UAACnF,EAAAA,CAAGA,CAAAA,CAACoF,GAAI,YACP,WAACD,MAAAA,CAAIrF,UAAU,gBACb,UAACE,EAAAA,CAAGA,CAAAA,CACFqF,GAAI,EACJnF,GAAI,EACJJ,UACiB,8CAAfP,EAAMiD,IAAI,CACN,gDACA,oDAGLoB,EAAaP,KAEhB,UAACrD,EAAAA,CAAGA,CAAAA,CAACqF,GAAI,EAAGnF,GAAI,EAAGJ,UAAU,6BAC3B,WAACwF,EAAAA,CAAIA,CAAAA,WACH,WAACA,EAAAA,CAAIA,CAACC,KAAK,EAACC,UAAU,qBACpB,UAACF,EAAAA,CAAIA,CAACG,KAAK,EAAC3F,UAAU,gBAAQX,EAAE,cAChC,UAACmG,EAAAA,CAAIA,CAACI,OAAO,EACXC,KAAK,KACLnD,KAAK,OACLoD,QAAQ,IACRjC,MAAON,EAAKwC,aAAa,CAAGxC,EAAKwC,aAAa,CAAGxC,EAAKQ,IAAI,MAG9D,WAACyB,EAAAA,CAAIA,CAACC,KAAK,EAACC,UAAU,wBACpB,UAACF,EAAAA,CAAIA,CAACG,KAAK,WACO,gBAAflG,EAAMiD,IAAI,CACPrD,EAAE,uCACFA,EAAE,wBAER,UAACmG,EAAAA,CAAIA,CAACI,OAAO,EACXI,UAA0B,gBAAfvG,EAAMiD,IAAI,CAAqB,SAAMuD,EAChDJ,KAAK,KACLnD,KAAK,OACLwD,YACEzG,kBAAMiD,IAAI,CACNrD,EAAE,kCACFA,EAAE,sCAERwE,MAAOb,CAAW,CAACoC,EAAE,CACrBe,SAAU,GAAO3C,EAAaC,EAAG2B,aAKzC,UAAClF,EAAAA,CAAGA,CAAAA,CACFqF,GAAI,EACJnF,GAAI,EACJJ,UAAU,gCACVoG,QAAS,IAAM9C,EAAWC,YAE1B,UAAC8C,EAAAA,CAAMA,CAAAA,CAACC,QAAQ,gBAAQjH,EAAE,mBAIhC,WAACkH,EAAAA,CAAKA,CAAAA,CAACC,KAAMpE,EAAWqE,OAAQpC,YAC9B,UAACkC,EAAAA,CAAKA,CAACG,MAAM,EAACC,WAAW,aACvB,UAACJ,EAAAA,CAAKA,CAACK,KAAK,WAAEvH,EAAE,kBAElB,UAACkH,EAAAA,CAAKA,CAACM,IAAI,WAAExH,EAAE,qCACf,WAACkH,EAAAA,CAAKA,CAACO,MAAM,YACX,UAACT,EAAAA,CAAMA,CAAAA,CAACC,QAAQ,YAAYF,QAAS9B,WAClCjF,EAAE,YAEL,UAACgH,EAAAA,CAAMA,CAAAA,CAACC,QAAQ,UAAUF,QAAS,IAAM7B,EAAchB,YACpDlE,EAAE,iBAlED+F,IA0Ed2B,CAAAA,EAAAA,EAAAA,SAAAA,CAASA,CAAC,KACRnE,EAAMoE,OAAO,CAAEzD,GAAS0D,IAAIC,eAAe,CAAC3D,EAAKY,OAAO,GACxDrD,EAAO,EACT,EAAG,EAAE,EAELiG,CAAAA,EAAAA,EAAAA,SAAAA,CAASA,CAAC,KACRtH,EAAMoB,cAAc,CAACmC,EACvB,EAAG,CAACA,EAAY,EAEhB+D,CAAAA,EAAAA,EAAAA,SAAAA,CAASA,CAAC,KACR9D,EAAexD,EAAMiB,OAAO,CAC9B,EAAG,CAACjB,EAAMiB,OAAO,CAAC,EAElBqG,CAAAA,EAAAA,EAAAA,SAAAA,CAASA,CAAC,KACRtH,GAAgC,SAAvBA,EAAM0H,YAAY,EAAepE,GAAS,GAC/CtD,GAASA,EAAMe,KAAK,EAAE,EAaf,IAZMf,EAAMe,KAAK,CAAC2E,GAAG,CAAC,CAACiC,EAAWC,KACzCvG,EAAKwG,IAAI,CAAC,CACR3C,SAAUyC,EAAK1C,GAAG,CAClBhB,MAAOjE,EAAMiE,KAAK,CAAGjE,EAAMiE,KAAK,CAAG,EACnChB,KAAM0E,EAAKrD,IAAI,CAACC,KAAK,CAAC,IAAI,CAAC,EAAE,GAEV,CACnB,GAAGoD,CAAI,CACPjD,QAAS,GAAwCiD,MAAAA,CAArCzE,8BAAsB,CAAC,gBAAuB,OAATyE,EAAK1C,GAAG,CAC3D,IAGkB,CAExB,EAAG,CAACjF,EAAMe,KAAK,CAAC,EAEhB,IAAM+G,EAAc,MAAOC,EAAqB9D,KAC9C,GAAI8D,EAAaC,MAAM,CAAG/D,EACxB,GAAI,CACF,CAF6B,GAEvBgE,EAAY,IAAIC,SACtBD,EAAKE,MAAM,CAAC,OAAQJ,CAAY,CAAC9D,EAAM,EACvC,IAAMmE,EAAM,MAAMzE,EAAAA,CAAUA,CAAC0E,IAAI,CAAC5E,EAAUwE,EAAM,CAChD,eAAgB,qBAClB,GACA5G,EAAKwG,IAAI,CAAC,CACR3C,SAAUkD,EAAInD,GAAG,CACjBnB,KAAMiE,CAAY,CAAC9D,EAAM,CACzBA,MAAOjE,EAAMiE,KAAK,CAAGjE,EAAMiE,KAAK,CAAG,EACnChB,KAAM8E,CAAY,CAAC9D,EAAM,CAACK,IAAI,CAACC,KAAK,CAAC,IAAI,CAAC,EAAE,GAE9CuD,EAAYC,EAAc9D,EAAQ,EACpC,CAAE,MAAOqE,EAAO,CACdR,EAAYC,EAAc9D,EAAQ,EACpC,MAEAjE,EAAMmB,QAAQ,CAACE,EAAMrB,EAAMiE,KAAK,CAAGjE,EAAMiE,KAAK,CAAG,EAErD,EAEMsE,EAASC,CAAAA,EAAAA,EAAAA,WAAAA,CAAWA,CAAC,MAAOC,IAChC,MAAMX,EAAYW,EAAc,GAChC,IAAMC,EAAWD,EAAa/C,GAAG,CAAC,GAChCiD,OAAOC,MAAM,CAAC9E,EAAM,CAClBY,QAAS8C,IAAIqB,eAAe,CAAC/E,EAC/B,IAEFT,EACID,EAAS,GAAe,IAAI0F,KAAcJ,EAAS,EACnDtF,EAAS,IAAIsF,EAAS,CAC5B,EAAG,EAAE,EAkBC,cACJK,CAAY,eACZC,CAAa,cACbC,CAAY,cACZC,CAAY,cACZC,CAAY,gBACZC,CAAc,CACf,CAAGC,CAAAA,EAAAA,EAAAA,EAAAA,CAAWA,CAAC,CACdC,OACEtJ,GAASA,EAAMiD,IAAI,CACf,+MACA,UACNsG,SAAUlG,EACVmG,QAAS,EACTC,QAASzG,SACTuF,EACAmB,UAhCF,CAgCaC,QAhCJA,CAA8B,EACrC,GAAiB,UAAU,CAAvBlG,GACF,GAAkC,SAAS,CAAvCK,EAAKb,IAAI,CAAC2G,SAAS,CAAC,EAAG,GAIzB,OADAC,EAAAA,EAAKA,CAACvB,KAAK,CAAC1I,EAAE,6BACP,CAAEkK,KAAM,oBAAqBC,QAAS,yBAA0B,CACzE,MACK,GAAiB,UAAU,CAAvBtG,GAC2B,OAAM,GAApCK,EAAKb,IAAI,CAAC2G,SAAS,CAAC,EAAG,GAE3B,OADAC,EAAAA,EAAKA,CAACvB,KAAK,CAAC1I,EAAE,6BACP,CAAEkK,KAAM,oBAAqBC,QAAS,yBAA0B,EAG3E,OAAO,IACT,CAkBA,GAEMpF,EAAQqF,CAAAA,EAAAA,EAAAA,OAAAA,CAAOA,CACnB,IAAO,EACL,GAAG1I,CAAS,CACZ,GAAI2H,EAAexG,EAAc,CAAEwH,QAAS,iBAAkB,CAAC,CAC/D,GAAIf,EACA,CAAEe,QAAS,oBAAqB,EAChC,CAAEA,QAAS,iBAAkB,CAAC,CAClC,GAAId,EAAe,CAAEc,QAAS,gBAAiB,EAAI,aAAExH,CAAY,CAAC,CACpE,EACA,CAACwG,EAAcE,EAAa,EAK5BzG,EADE1C,GAAwB,eAAe,CAA9BA,EAAMiD,IAAI,CAEnB,UAACiH,QAAAA,CAAMvF,MAAO,CAAE1C,MAAO,SAAU,WAAIrC,EAAE,uBAIvC,UAACsK,QAAAA,CAAMvF,MAAO,CAAE1C,MAAO,SAAU,WAAIrC,EAAE,oBAI3C,IAAMuK,EACJf,EAAepB,MAAM,CAAG,GAAKoB,CAAc,CAAC,EAAE,CAACtF,IAAI,CAACsC,IAAI,CAAGpD,EAC7D,MACE,iCACE,UAAC4C,MAAAA,CACCrF,UAAU,yDACVoE,MAAO,CAAE/C,MAAO,OAAQC,OAAQ,OAAQ,WAExC,WAAC+D,MAAAA,CAAK,GAAGmD,EAAa,OAAEpE,CAAM,EAAE,WAC9B,UAACyF,QAAAA,CAAO,GAAGpB,GAAe,GAC1B,UAACqB,EAAAA,CAAeA,CAAAA,CAACC,KAAMC,EAAAA,GAAgBA,CAAEnE,KAAK,KAAKnE,MAAM,SACzD,UAACuI,IAAAA,CAAE7F,MAAO,CAAE1C,MAAO,UAAWwI,aAAc,KAAM,WAC/C7K,EAAE,mDAGJ,CAACyD,GACA,WAAC6G,QAAAA,CAAMvF,MAAO,CAAE1C,MAAO,SAAU,YAC/B,UAACyI,IAAAA,UAAE,UAAS,wCAGfhI,GACA1C,EAAMiD,IAAI,CACPkH,GACE,CAFU,EAEV,QAACD,QAAAA,CAAM3J,UAAU,6BACf,UAAC8J,EAAAA,CAAeA,CAAAA,CACdC,KAAMK,EAAAA,GAAmBA,CACzBvE,KAAK,KACLnE,MAAM,QACL,IACFrC,EAAE,4CAaVuJ,CAVGgB,EAWF,WAACD,QAAAA,CAAM3J,UAAU,cAAcoE,MAAO,CAAE1C,MAAO,SAAU,YACvD,UAACoI,EAAAA,CAAeA,CAAAA,CACdC,KAAMK,EAAAA,GAAmBA,CACzBvE,KAAK,KACLnE,MAAM,QACL,IACFrC,EAAE,mCAKVuD,EAAM6E,MAAM,CAAG,GAAK,UAACpC,MAAAA,CAAIjB,MAAOvC,WAAkBqD,MAGzD", "sources": ["webpack://_N_E/?19d6", "webpack://_N_E/./pages/updates/ImageForm.tsx", "webpack://_N_E/./components/common/ReactDropZone.tsx"], "sourcesContent": ["\n    (window.__NEXT_P = window.__NEXT_P || []).push([\n      \"/updates/ImageForm\",\n      function () {\n        return require(\"private-next-pages/updates/ImageForm.tsx\");\n      }\n    ]);\n    if(module.hot) {\n      module.hot.dispose(function () {\n        window.__NEXT_P.push([\"/updates/ImageForm\"])\n      });\n    }\n  ", "//Import Library\r\nimport React from \"react\";\r\nimport { Container, Row, Col } from \"react-bootstrap\";\r\n\r\n//Import services/components\r\nimport ReactDropZone from \"../../components/common/ReactDropZone\";\r\nimport { useTranslation } from 'next-i18next';\r\n\r\n\r\n//TOTO refactor\r\ninterface ImageFormProps {\r\n  data: any[];\r\n  getId: (id: any[]) => void;\r\n  imgSrc: any[];\r\n  getSourceCollection: (imgSrcArr: any[]) => void;\r\n}\r\n\r\nconst ImageForm = (props: ImageFormProps): React.ReactElement => {\r\n  const { t } = useTranslation('common');\r\n\r\n  const getID = (id: any[]) => {\r\n    props.getId(id)\r\n  }\r\n\r\n  const getSourceText = (imgSrcArr: any[]) => {\r\n    props.getSourceCollection(imgSrcArr);\r\n  }\r\n\r\n  return (\r\n    <Container className=\"formCard\" fluid>\r\n      <Col>\r\n        <Row className='header-block' lg={12}>\r\n          <h6><span>{t(\"update.Image\")}</span></h6>\r\n        </Row>\r\n        <Row>\r\n          <ReactDropZone datas={props.data} srcText={props.imgSrc} getImgID={(id: any[]) => getID(id)} getImageSource={(imgSrcArr: any[]) => getSourceText(imgSrcArr)} />\r\n        </Row>\r\n      </Col>\r\n    </Container>\r\n  );\r\n}\r\nexport default ImageForm;\r\n", "//Import Library\r\nimport React, { useMemo, useEffect, useState, useCallback } from \"react\";\r\nimport { useDropzone } from \"react-dropzone\";\r\nimport { FontAwesomeIcon } from \"@fortawesome/react-fontawesome\";\r\nimport {\r\n  faExclamationCircle,\r\n  faCloudUploadAlt,\r\n} from \"@fortawesome/free-solid-svg-icons\";\r\nimport { Form, Button, Modal, Col } from \"react-bootstrap\";\r\nimport _ from \"lodash\";\r\n\r\nimport toast from 'react-hot-toast';\r\n\r\n//Import services/components\r\nimport apiService from \"../../services/apiService\";\r\nimport { useTranslation } from 'next-i18next';\r\n\r\n// Define type for temp array items\r\ninterface TempItem {\r\n  serverID: string;\r\n  file?: any;\r\n  index: number;\r\n  type: string;\r\n}\r\n\r\nlet temp: TempItem[] = [];\r\n\r\nconst baseStyle: any = {\r\n  flex: 1,\r\n  display: \"flex\",\r\n  flexDirection: \"column\",\r\n  alignItems: \"center\",\r\n  justifyContent: \"center\",\r\n  width: \"100%\",\r\n  height: \"100%\",\r\n  borderWidth: 0.1,\r\n  borderColor: \"#fafafa\",\r\n  backgroundColor: \"#fafafa\",\r\n  color: \"black\",\r\n  transition: \"border  .24s ease-in-out\",\r\n  padding: \"15px\",\r\n};\r\n\r\nconst thumb: any = {\r\n  display: \"flex\",\r\n  borderRadius: 2,\r\n  border: \"1px solid #ddd\",\r\n  margin: 8,\r\n  height: 175,\r\n  boxShadow: \"0 0 15px 0.25px rgba(0,0,0,0.15)\",\r\n  boxSizing: \"border-box\",\r\n};\r\n\r\nconst thumbsContainer: any = {\r\n  display: \"flex\",\r\n  padding: \"10px\",\r\n  width: \"100%\",\r\n  border: \"2px solid gray\",\r\n  flexDirection: \"column\",\r\n  justifyContent: \"flex-start\",\r\n  flexWrap: \"wrap\",\r\n  marginTop: 20,\r\n};\r\n\r\nconst thumbInner: any = {\r\n  display: \"flex\",\r\n  minWidth: 0,\r\n};\r\n\r\nconst deleteIcon: any = {\r\n  display: \"flex\",\r\n  justifyContent: \"center\",\r\n  alignItems: \"center\",\r\n  marginLeft: 30,\r\n};\r\n\r\nconst img = {\r\n  width: \"150px\",\r\n};\r\n\r\nconst activeStyle: any = {\r\n  borderColor: \"#2196f3\",\r\n};\r\n\r\nconst ReactDropZone = (props: any) => {\r\n  const { t } = useTranslation('common');\r\n  const [modalShow, setModalShow] = useState(false);\r\n  const [deleteFile, setDeleteFile] = useState();\r\n  const limit: any =\r\n    props.type == \"application\" ? 20971520 : process.env.UPLOAD_LIMIT;\r\n  const [files, setFiles] = useState<any[]>([]);\r\n  const [multi, setMulti] = useState(true);\r\n  const [imageSource, setImageSource] = useState<string[]>([]);\r\n\r\n  const endpoint = props && props.type === \"application\" ? \"/files\" : \"/image\";\r\n  const imageDelete = async (id: string) => {\r\n    const _res = await apiService.remove(`${endpoint}/${id}`);\r\n  };\r\n\r\n  const removeFile = (file: any) => {\r\n    setDeleteFile(file);\r\n    setModalShow(true);\r\n  };\r\n\r\n  const handleSource = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>, index: number) => {\r\n    const items = [...imageSource];\r\n    items[index] = e.target.value;\r\n    setImageSource(items);\r\n  };\r\n\r\n  const getComponent = (file: any) => {\r\n    const fileType = file && file.name.split(\".\").pop();\r\n    switch (fileType) {\r\n      case \"JPG\":\r\n      case \"jpg\":\r\n      case \"jpeg\":\r\n      case \"jpg\":\r\n      case \"png\":\r\n        return <img src={file.preview} style={img} />;\r\n      case \"pdf\":\r\n        return (\r\n          <img\r\n            src=\"/images/fileIcons/pdfFile.png\"\r\n            className={\r\n              props.type === \"application\" ? \"docPreview\" : \"imgPreview\"\r\n            }\r\n          />\r\n        );\r\n      case \"docx\":\r\n        return (\r\n          <img\r\n            src=\"/images/fileIcons/wordFile.png\"\r\n            className={\r\n              props.type === \"application\" ? \"docPreview\" : \"imgPreview\"\r\n            }\r\n          />\r\n        );\r\n      case \"xls\":\r\n      case \"xlsx\":\r\n        return (\r\n          <img\r\n            src=\"/images/fileIcons/xlsFile.png\"\r\n            className={\r\n              props.type === \"application\" ? \"docPreview\" : \"imgPreview\"\r\n            }\r\n          />\r\n        );\r\n      default:\r\n        return (\r\n          <img\r\n            src=\"/images/fileIcons/wordFile.png\"\r\n            className={\r\n              props.type === \"application\" ? \"docPreview\" : \"imgPreview\"\r\n            }\r\n          />\r\n        );\r\n    }\r\n  };\r\n\r\n  const modalHide = () => setModalShow(false);\r\n\r\n  const cancelHandler = () => {\r\n    setModalShow(false);\r\n  };\r\n\r\n  const submitHandler = (fileselector: any) => {\r\n    fileselector = deleteFile;\r\n    const obj =\r\n      fileselector && fileselector._id\r\n        ? { serverID: fileselector._id }\r\n        : { file: fileselector };\r\n    const _index = _.findIndex(temp, obj);\r\n    //**Delete the source Field**//\r\n    const removeSrc = [...imageSource];\r\n    removeSrc.splice(_index, 1);\r\n    setImageSource(removeSrc);\r\n    //**End**/\r\n    imageDelete(temp[_index].serverID);\r\n    temp.splice(_index, 1);\r\n    props.getImgID(temp, props.index ? props.index : 0);\r\n    const newFiles = [...files];\r\n    newFiles.splice(newFiles.indexOf(fileselector), 1);\r\n    setFiles(newFiles);\r\n    setModalShow(false);\r\n  };\r\n\r\n  const thumbs: any = files.map((file: any, i) => {\r\n    return (\r\n      <div key={i}>\r\n        <Col xs={12}>\r\n          <div className=\"row\">\r\n            <Col\r\n              md={4}\r\n              lg={3}\r\n              className={\r\n                props.type === \"application text-center align-self-center\"\r\n                  ? \"docImagePreview text-center align-self-center\"\r\n                  : \"imgPreview text-center align-self-center\"\r\n              }\r\n            >\r\n              {getComponent(file)}\r\n            </Col>\r\n            <Col md={5} lg={7} className=\"align-self-center\">\r\n              <Form>\r\n                <Form.Group controlId=\"filename\">\r\n                  <Form.Label className=\"mt-2\">{t(\"FileName\")}</Form.Label>\r\n                  <Form.Control\r\n                    size=\"sm\"\r\n                    type=\"text\"\r\n                    disabled\r\n                    value={file.original_name ? file.original_name : file.name}\r\n                  />\r\n                </Form.Group>\r\n                <Form.Group controlId=\"description\">\r\n                  <Form.Label>\r\n                    {props.type === \"application\"\r\n                      ? t(\"ShortDescription/(Max255Characters)\")\r\n                      : t(\"Source/Description\")}\r\n                  </Form.Label>\r\n                  <Form.Control\r\n                    maxLength={props.type === \"application\" ? 255 : undefined}\r\n                    size=\"sm\"\r\n                    type=\"text\"\r\n                    placeholder={\r\n                      props.type === \"application\"\r\n                        ? t(\"`Enteryourdocumentdescription`\")\r\n                        : t(\"`Enteryourimagesource/description`\")\r\n                    }\r\n                    value={imageSource[i]}\r\n                    onChange={(e) => handleSource(e, i)}\r\n                  />\r\n                </Form.Group>\r\n              </Form>\r\n            </Col>\r\n            <Col\r\n              md={3}\r\n              lg={2}\r\n              className=\"align-self-center text-center\"\r\n              onClick={() => removeFile(file)}\r\n            >\r\n              <Button variant=\"dark\">{t(\"Remove\")}</Button>\r\n            </Col>\r\n          </div>\r\n        </Col>\r\n        <Modal show={modalShow} onHide={modalHide}>\r\n          <Modal.Header closeButton>\r\n            <Modal.Title>{t(\"DeleteFile\")}</Modal.Title>\r\n          </Modal.Header>\r\n          <Modal.Body>{t(\"Areyousurewanttodeletethisfile?\")}</Modal.Body>\r\n          <Modal.Footer>\r\n            <Button variant=\"secondary\" onClick={cancelHandler}>\r\n              {t(\"Cancel\")}\r\n            </Button>\r\n            <Button variant=\"primary\" onClick={() => submitHandler(file)}>\r\n              {t(\"yes\")}\r\n            </Button>\r\n          </Modal.Footer>\r\n        </Modal>\r\n      </div>\r\n    );\r\n  });\r\n\r\n  useEffect(() => {\r\n    files.forEach((file) => URL.revokeObjectURL(file.preview));\r\n    temp = [];\r\n  }, []);\r\n\r\n  useEffect(() => {\r\n    props.getImageSource(imageSource);\r\n  }, [imageSource]);\r\n\r\n  useEffect(() => {\r\n    setImageSource(props.srcText);\r\n  }, [props.srcText]);\r\n\r\n  useEffect(() => {\r\n    props && props.singleUpload === \"true\" && setMulti(false);\r\n    if (props && props.datas) {\r\n      const newObj = props.datas.map((item: any, _i: number) => {\r\n        temp.push({\r\n          serverID: item._id,\r\n          index: props.index ? props.index : 0,\r\n          type: item.name.split(\".\")[1],\r\n        });\r\n        const previewState = {\r\n          ...item,\r\n          preview: `${process.env.API_SERVER}/image/show/${item._id}`,\r\n        };\r\n        return previewState;\r\n      });\r\n      setFiles([...newObj]);\r\n    }\r\n  }, [props.datas]);\r\n\r\n  const filesUpload = async (filesinitial: any[], index: number) => {\r\n    if (filesinitial.length > index) {\r\n      try {\r\n        const form: any = new FormData();\r\n        form.append(\"file\", filesinitial[index]);\r\n        const res = await apiService.post(endpoint, form, {\r\n          \"Content-Type\": \"multipart/form-data\",\r\n        });\r\n        temp.push({\r\n          serverID: res._id,\r\n          file: filesinitial[index],\r\n          index: props.index ? props.index : 0,\r\n          type: filesinitial[index].name.split(\".\")[1],\r\n        });\r\n        filesUpload(filesinitial, index + 1);\r\n      } catch (error) {\r\n        filesUpload(filesinitial, index + 1);\r\n      }\r\n    } else {\r\n      props.getImgID(temp, props.index ? props.index : 0);\r\n    }\r\n  };\r\n\r\n  const onDrop = useCallback(async (ondrop_files: any[]) => {\r\n    await filesUpload(ondrop_files, 0);\r\n    const accFiles = ondrop_files.map((file: any) =>\r\n      Object.assign(file, {\r\n        preview: URL.createObjectURL(file),\r\n      })\r\n    );\r\n    multi\r\n      ? setFiles((prevState) => [...prevState, ...accFiles])\r\n      : setFiles([...accFiles]);\r\n  }, []);\r\n\r\n  function nameLengthValidator(file: File) {\r\n    if (endpoint === \"/image\") {\r\n      if (file.type.substring(0, 5) === \"image\") {\r\n        return null;\r\n      } else {\r\n        toast.error(t(\"toast.filetypenotsupport\"));\r\n        return { code: \"file-invalid-type\", message: \"File type not supported\" };\r\n      }\r\n    } else if (endpoint === \"/files\") {\r\n      if (!(file.type.substring(0, 5) !== \"image\")) {\r\n        toast.error(t(\"toast.filetypenotsupport\"));\r\n        return { code: \"file-invalid-type\", message: \"File type not supported\" };\r\n      }\r\n    }\r\n    return null;\r\n  }\r\n  const {\r\n    getRootProps,\r\n    getInputProps,\r\n    isDragActive,\r\n    isDragAccept,\r\n    isDragReject,\r\n    fileRejections,\r\n  } = useDropzone({\r\n    accept:\r\n      props && props.type\r\n        ? \"application/pdf, application/vnd.openxmlformats-officedocument.wordprocessingml.document, application/vnd.oasis.opendocument.text,application/vnd.openxmlformats-officedocument.spreadsheetml.sheet,text/csv\"\r\n        : \"image/*\",\r\n    multiple: multi,\r\n    minSize: 0,\r\n    maxSize: limit,\r\n    onDrop,\r\n    validator: nameLengthValidator,\r\n  });\r\n\r\n  const style = useMemo(\r\n    () => ({\r\n      ...baseStyle,\r\n      ...(isDragActive ? activeStyle : { outline: \"2px dashed #bbb\" }),\r\n      ...(isDragAccept\r\n        ? { outline: \"2px dashed #595959\" }\r\n        : { outline: \"2px dashed #bbb\" }),\r\n      ...(isDragReject ? { outline: \"2px dashed red\" } : { activeStyle }),\r\n    }),\r\n    [isDragActive, isDragReject]\r\n  );\r\n\r\n  let dropZoneMsg;\r\n  if (props && props.type === \"application\") {\r\n    dropZoneMsg = (\r\n      <small style={{ color: \"#595959\" }}>{t(\"DocumentWeSupport\")}</small>\r\n    );\r\n  } else {\r\n    dropZoneMsg = (\r\n      <small style={{ color: \"#595959\" }}>{t(\"ImageWeSupport\")}</small>\r\n    );\r\n  }\r\n\r\n  const isFileTooLarge =\r\n    fileRejections.length > 0 && fileRejections[0].file.size > limit;\r\n  return (\r\n    <>\r\n      <div\r\n        className=\" d-flex justify-content-center align-items-center mt-3\"\r\n        style={{ width: \"100%\", height: \"180px\" }}\r\n      >\r\n        <div {...getRootProps({ style })}>\r\n          <input {...getInputProps()} />\r\n          <FontAwesomeIcon icon={faCloudUploadAlt} size=\"4x\" color=\"#999\" />\r\n          <p style={{ color: \"#595959\", marginBottom: \"0px\" }}>\r\n            {t(\"Drag'n'dropsomefileshere,orclicktoselectfiles\")}\r\n          </p>\r\n\r\n          {!multi && (\r\n            <small style={{ color: \"#595959\" }}>\r\n              <b>Note:</b> One single image will be accepted\r\n            </small>\r\n          )}\r\n          {dropZoneMsg}\r\n          {props.type === \"application\"\r\n            ? isFileTooLarge && (\r\n                <small className=\"text-danger mt-2\">\r\n                  <FontAwesomeIcon\r\n                    icon={faExclamationCircle}\r\n                    size=\"1x\"\r\n                    color=\"red\"\r\n                  />{\" \"}\r\n                  {t(\"FileistoolargeItshouldbelessthan20MB\")}\r\n                </small>\r\n              )\r\n            : isFileTooLarge && (\r\n                <small className=\"text-danger mt-2\">\r\n                  <FontAwesomeIcon\r\n                    icon={faExclamationCircle}\r\n                    size=\"1x\"\r\n                    color=\"red\"\r\n                  />{\" \"}\r\n                  {t(\"FileistoolargeItshouldbelessthan20MB\")}\r\n                </small>\r\n              )}\r\n          {isDragReject && (\r\n            <small className=\"text-danger\" style={{ color: \"#595959\" }}>\r\n              <FontAwesomeIcon\r\n                icon={faExclamationCircle}\r\n                size=\"1x\"\r\n                color=\"red\"\r\n              />{\" \"}\r\n              {t(\"Filetypenotacceptedsorr\")}\r\n            </small>\r\n          )}\r\n        </div>\r\n      </div>\r\n      {files.length > 0 && <div style={thumbsContainer}>{thumbs}</div>}\r\n    </>\r\n  );\r\n};\r\n\r\nexport default ReactDropZone;\r\n"], "names": ["t", "useTranslation", "ImageForm", "getID", "props", "getId", "id", "getSourceText", "getSourceCollection", "imgSrcArr", "Container", "className", "fluid", "Col", "Row", "lg", "h6", "span", "ReactDropZone", "datas", "data", "srcText", "imgSrc", "getImgID", "getImageSource", "temp", "baseStyle", "flex", "display", "flexDirection", "alignItems", "justifyContent", "width", "height", "borderWidth", "borderColor", "backgroundColor", "color", "transition", "padding", "thumbsContainer", "border", "flexWrap", "marginTop", "img", "activeStyle", "dropZoneMsg", "modalShow", "setModalShow", "useState", "deleteFile", "setDeleteFile", "limit", "type", "process", "files", "setFiles", "multi", "set<PERSON><PERSON><PERSON>", "imageSource", "setImageSource", "endpoint", "imageDelete", "apiService", "remove", "removeFile", "file", "handleSource", "e", "index", "items", "target", "value", "getComponent", "name", "split", "pop", "src", "preview", "style", "modalHide", "cancelHandler", "<PERSON><PERSON><PERSON><PERSON>", "obj", "fileselector", "_id", "serverID", "_index", "_", "removeSrc", "splice", "newFiles", "indexOf", "thumbs", "map", "i", "div", "xs", "md", "Form", "Group", "controlId", "Label", "Control", "size", "disabled", "original_name", "max<PERSON><PERSON><PERSON>", "undefined", "placeholder", "onChange", "onClick", "<PERSON><PERSON>", "variant", "Modal", "show", "onHide", "Header", "closeButton", "Title", "Body", "Footer", "useEffect", "for<PERSON>ach", "URL", "revokeObjectURL", "singleUpload", "item", "_i", "push", "filesUpload", "filesinitial", "length", "form", "FormData", "append", "res", "post", "error", "onDrop", "useCallback", "ondrop_files", "accFiles", "Object", "assign", "createObjectURL", "prevState", "getRootProps", "getInputProps", "isDragActive", "isDragAccept", "isDragReject", "fileRejections", "useDropzone", "accept", "multiple", "minSize", "maxSize", "validator", "nameLengthValidator", "substring", "toast", "code", "message", "useMemo", "outline", "small", "isFileTooLarge", "input", "FontAwesomeIcon", "icon", "faCloudUploadAlt", "p", "marginBottom", "b", "faExclamationCircle"], "sourceRoot": "", "ignoreList": []}