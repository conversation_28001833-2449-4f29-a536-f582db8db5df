"use strict";exports.id=3929,exports.ids=[3929],exports.modules={27053:(e,s,t)=>{t.d(s,{A:()=>a});var i=t(8732);function a(e){return(0,i.jsx)("h2",{className:"page-heading",children:e.title})}},35557:(e,s,t)=>{t.r(s),t.d(s,{default:()=>a});var i=t(8732);function a(e){return(0,i.jsx)("div",{className:"container-fluid p-0 response-message-block",children:(0,i.jsx)("div",{className:"message",children:"you don't have permission to access"})})}},45927:(e,s,t)=>{t.r(s),t.d(s,{canAddAreaOfWork:()=>r,canAddContent:()=>D,canAddCountry:()=>o,canAddDeploymentStatus:()=>d,canAddEventStatus:()=>l,canAddExpertise:()=>p,canAddFocalPointApproval:()=>c,canAddHazardTypes:()=>A,canAddHazards:()=>u,canAddLandingPage:()=>v,canAddOperationStatus:()=>S,canAddOrganisationApproval:()=>g,canAddOrganisationNetworks:()=>y,canAddOrganisationTypes:()=>h,canAddProjectStatus:()=>_,canAddRegions:()=>x,canAddRiskLevels:()=>w,canAddSyndromes:()=>f,canAddUpdateTypes:()=>j,canAddUsers:()=>N,canAddVspaceApproval:()=>m,canAddWorldRegion:()=>C,default:()=>P});var i=t(81366),a=t.n(i);let n="create:any",r=a()({authenticatedSelector:e=>!!e.permissions&&!!e.permissions.area_of_work&&!!e.permissions.area_of_work[n],wrapperDisplayName:"CanAddAreaOfWork"}),o=a()({authenticatedSelector:e=>!!e.permissions&&!!e.permissions.country&&!!e.permissions.country[n],wrapperDisplayName:"CanAddCountry"}),d=a()({authenticatedSelector:e=>!!e.permissions&&!!e.permissions.deployment_status&&!!e.permissions.deployment_status[n],wrapperDisplayName:"CanAddDeploymentStatus"}),l=a()({authenticatedSelector:e=>!!e.permissions&&!!e.permissions.event_status&&!!e.permissions.event_status[n],wrapperDisplayName:"CanAddEventStatus"}),p=a()({authenticatedSelector:e=>!!e.permissions&&!!e.permissions.expertise&&!!e.permissions.expertise[n],wrapperDisplayName:"CanAddExpertise"}),c=a()({authenticatedSelector:e=>!!e.permissions&&!!e.permissions.institution_focal_point&&!!e.permissions.institution_focal_point[n],wrapperDisplayName:"CanAddFocalPointApproval"}),m=a()({authenticatedSelector:e=>!!e.permissions&&!!e.permissions.institution_focal_point&&!!e.permissions.institution_focal_point[n],wrapperDisplayName:"CanAddVspaceApproval"}),u=a()({authenticatedSelector:e=>!!e.permissions&&!!e.permissions.hazard&&!!e.permissions.hazard[n],wrapperDisplayName:"CanAddHazards"}),A=a()({authenticatedSelector:e=>!!e.permissions&&!!e.permissions.hazard_type&&!!e.permissions.hazard_type[n],wrapperDisplayName:"CanAddHazardTypes"}),g=a()({authenticatedSelector:e=>!!e.permissions&&!!e.permissions.institution&&!!e.permissions.institution[n],wrapperDisplayName:"CanAddOrganisationApproval"}),y=a()({authenticatedSelector:e=>!!e.permissions&&!!e.permissions.institution_network&&!!e.permissions.institution_network[n],wrapperDisplayName:"CanAddOrganisationNetworks"}),h=a()({authenticatedSelector:e=>!!e.permissions&&!!e.permissions.institution_type&&!!e.permissions.institution_type[n],wrapperDisplayName:"CanAddOrganisationTypes"}),S=a()({authenticatedSelector:e=>!!e.permissions&&!!e.permissions.operation_status&&!!e.permissions.operation_status[n],wrapperDisplayName:"CanAddOperationStatus"}),_=a()({authenticatedSelector:e=>!!e.permissions&&!!e.permissions.project_status&&!!e.permissions.project_status[n],wrapperDisplayName:"CanAddProjectStatus"}),x=a()({authenticatedSelector:e=>!!e.permissions&&!!e.permissions.region&&!!e.permissions.region[n],wrapperDisplayName:"CanAddRegions"}),w=a()({authenticatedSelector:e=>!!e.permissions&&!!e.permissions.risk_level&&!!e.permissions.risk_level[n],wrapperDisplayName:"CanAddRiskLevels"}),f=a()({authenticatedSelector:e=>!!e.permissions&&!!e.permissions.syndrome&&!!e.permissions.syndrome[n],wrapperDisplayName:"CanAddSyndromes"}),j=a()({authenticatedSelector:e=>!!e.permissions&&!!e.permissions.update_type&&!!e.permissions.update_type[n],wrapperDisplayName:"CanAddUpdateTypes"}),N=a()({authenticatedSelector:e=>!!e.permissions&&!!e.permissions.users&&!!e.permissions.users[n],wrapperDisplayName:"CanAddUsers"}),C=a()({authenticatedSelector:e=>!!e.permissions&&!!e.permissions.worl_region&&!!e.permissions.worl_region[n],wrapperDisplayName:"CanAddWorldRegion"}),v=a()({authenticatedSelector:e=>!!e.permissions&&!!e.permissions.landing_page&&!!e.permissions.landing_page[n],wrapperDisplayName:"CanAddLandingPage"}),D=a()({authenticatedSelector:e=>!!e.permissions&&!!e.permissions.operation&&!!e.permissions.operation[n]&&!!e.permissions.project&&!!e.permissions.project[n]&&!!e.permissions.event&&!!e.permissions.event[n]&&!!e.permissions.vspace&&!!e.permissions.vspace[n]&&!!e.permissions.institution&&!!e.permissions.institution[n]&&!!e.permissions.update&&!!e.permissions.update[n]||!1,wrapperDisplayName:"CanAddContent"}),P=r},51538:(e,s,t)=>{t.a(e,async(e,i)=>{try{t.r(s),t.d(s,{default:()=>u});var a=t(8732),n=t(7082),r=t(83551),o=t(49481),d=t(99800),l=t(82015),p=t(88751),c=t(63487),m=e([d,c]);[d,c]=m.then?(await m)():m;let u=({filterText:e,onFilter:s,onHandleSearch:t,onClear:i,onKeyPress:m})=>{let{t:u}=(0,p.useTranslation)("common"),[A,g]=(0,l.useState)([]),[,y]=(0,l.useState)(!1),h={sort:{created_at:"desc"},limit:"~",page:1,query:{},select:"-country -dial_code -firstname -region -institution.address -institution.contact_name -institution.description -institution.dial_code -institution.document -institution.doc_src -institution.images -institution.images_src -institution.expertise -institution.hazards -institution.hazard_types -institution.header -institution.networks -institution.partners -institution.telephone -institution.twitter -institution.type -institution.unit -institution.user -mobile_number -position"},S=async()=>{y(!0);let e=await c.A.get("/users",h);if(e&&Array.isArray(e.data)){let s=e.data.map((e,s)=>({label:e.username,value:e._id}));g(s),y(!1)}};return(0,l.useEffect)(()=>{S()},[]),(0,a.jsx)(n.A,{fluid:!0,className:"p-0",children:(0,a.jsx)(r.A,{children:(0,a.jsx)(o.A,{xs:6,md:4,className:"p-0",children:(0,a.jsx)(d.default,{autoFocus:!0,isClearable:!0,isSearchable:!0,onKeyDown:m,onChange:s,placeholder:u("adminsetting.user.table.Usernameoremail"),options:A})})})})};i()}catch(e){i(e)}})},56084:(e,s,t)=>{t.d(s,{A:()=>l});var i=t(8732);t(82015);var a=t(38609),n=t.n(a),r=t(88751),o=t(30370);function d(e){let{t:s}=(0,r.useTranslation)("common"),t={rowsPerPageText:s("Rowsperpage")},{columns:a,data:d,totalRows:l,resetPaginationToggle:p,subheader:c,subHeaderComponent:m,handlePerRowsChange:u,handlePageChange:A,rowsPerPage:g,defaultRowsPerPage:y,selectableRows:h,loading:S,pagServer:_,onSelectedRowsChange:x,clearSelectedRows:w,sortServer:f,onSort:j,persistTableHead:N,sortFunction:C,...v}=e,D={paginationComponentOptions:t,noDataComponent:s("NoData"),noHeader:!0,columns:a,data:d||[],dense:!0,paginationResetDefaultPage:p,subHeader:c,progressPending:S,subHeaderComponent:m,pagination:!0,paginationServer:_,paginationPerPage:y||10,paginationRowsPerPageOptions:g||[10,15,20,25,30],paginationTotalRows:l,onChangeRowsPerPage:u,onChangePage:A,selectableRows:h,onSelectedRowsChange:x,clearSelectedRows:w,progressComponent:(0,i.jsx)(o.A,{}),sortIcon:(0,i.jsx)("i",{className:"sort-icon fas fa-exchange-alt"}),sortServer:f,onSort:j,sortFunction:C,persistTableHead:N,className:"rki-table"};return(0,i.jsx)(n(),{...D})}d.defaultProps={subHeader:!1,pagination:!0,totalRows:null,pagServer:!0,onSelectedRowsChange:null,clearSelectedRows:!1,sortServer:!1,persistTableHead:!1};let l=d},74748:(e,s,t)=>{t.a(e,async(e,i)=>{try{t.r(s),t.d(s,{default:()=>h});var a=t(8732),n=t(19918),r=t.n(n),o=t(82015),d=t.n(o),l=t(12403),p=t(91353),c=t(42893),m=t(56084),u=t(63487),A=t(51538),g=t(88751),y=e([c,u,A]);function h(e){let{t:s}=(0,g.useTranslation)("common"),[t,i]=(0,o.useState)([]),[n,y]=(0,o.useState)(!1),[h,S]=(0,o.useState)(0),[_,x]=(0,o.useState)(10),[w,f]=(0,o.useState)(10),[j,N]=(0,o.useState)(!1),[C,v]=(0,o.useState)({}),[D,P]=d().useState(""),[b,E]=d().useState(!1),[R,U]=(0,o.useState)({}),k={sort:{created_at:"desc"},limit:w,page:1,query:{}},T=[{name:s("adminsetting.user.table.Username"),selector:"username",cell:e=>e.username},{name:s("adminsetting.user.table.Email"),selector:"email",cell:e=>e.email},{name:s("adminsetting.user.table.Role"),selector:"role",cell:e=>e.roles?e.roles:""},{name:s("adminsetting.user.table.Organisation"),selector:"institution",cell:e=>e.institution?e.institution.title:""},{name:s("adminsetting.user.table.Action"),selector:"",cell:e=>(0,a.jsx)(a.Fragment,{children:e.isEdit?(0,a.jsxs)("div",{children:[(0,a.jsx)(r(),{href:"/adminsettings/[...routes]",as:`/adminsettings/edit_user/${e._id}`,children:(0,a.jsx)("i",{className:"icon fas fa-edit"})}),"\xa0",(0,a.jsx)("a",{onClick:()=>I(e),children:(0,a.jsx)("i",{className:"icon fas fa-trash-alt"})})]}):""})}],H=async e=>{y(!0);let s=await u.A.get("/users",e);s&&Array.isArray(s.data)&&(R.roles?.includes("SUPER_ADMIN")?s.data.map(e=>e.isEdit=!0):(s.data.filter(e=>e.roles.includes("SUPER_ADMIN")).map(e=>e.isEdit=!1),s.data.filter(e=>!e.roles.includes("SUPER_ADMIN")).map(e=>e.isEdit=!0)),i(s.data),S(s.totalCount),y(!1),z(s.data))},O=async(e,s)=>{k.limit=e,k.page=s,y(!0);let t=await u.A.get("/users",k);t&&Array.isArray(t.data)&&(R.roles?.includes("SUPER_ADMIN")?t.data.map(e=>e.isEdit=!0):(t.data.filter(e=>e.roles.includes("SUPER_ADMIN")).map(e=>e.isEdit=!1),t.data.filter(e=>!e.roles.includes("SUPER_ADMIN")).map(e=>e.isEdit=!0)),i(t.data),f(e),y(!1))},z=async e=>{let s=await u.A.post("/users/getLoggedUser",{});s&&s.username&&(U(s),console.log(e),s.roles.includes("SUPER_ADMIN")?e.map(e=>e.isEdit=!0):(e.filter(e=>e.roles.includes("SUPER_ADMIN")).map(e=>e.isEdit=!1),e.filter(e=>!e.roles.includes("SUPER_ADMIN")).map(e=>e.isEdit=!0)),i(e),x(e))},I=async e=>{v(e._id),N(!0)},M=async()=>{try{await u.A.remove(`/users/${C}`),H(k),N(!1),c.default.success(s("adminsetting.user.table.userDeletedSuccessfully"))}catch(e){c.default.error(s("adminsetting.user.table.errorDeletingUser"))}},q=()=>N(!1),F=d().useMemo(()=>{let e=e=>{e&&(RegExp("^[^@]+@[^@]+\\.[^@]+$").test(e.toLowerCase())?k.query={email:e}:k.query={username:e}),H(k),k.query={}},s=()=>{e(D)},t=()=>{s()};return(0,a.jsx)(A.default,{onFilter:s=>{s&&s.label?(P(s.label),e(s.label)):(k.query={},P(""),H(k))},onClear:()=>{D&&(E(!b),P(""))},filterText:D,onHandleSearch:s,onKeyPress:e=>{"Enter"===e.key&&t()}})},[D]);return(0,a.jsxs)("div",{children:[(0,a.jsxs)(l.A,{show:j,onHide:q,children:[(0,a.jsx)(l.A.Header,{closeButton:!0,children:(0,a.jsx)(l.A.Title,{children:s("adminsetting.user.table.DeleteUser")})}),(0,a.jsx)(l.A.Body,{children:s("adminsetting.user.table.Areyousurewanttodeletethisuser?")}),(0,a.jsxs)(l.A.Footer,{children:[(0,a.jsx)(p.A,{variant:"secondary",onClick:q,children:s("adminsetting.user.table.Cancel")}),(0,a.jsx)(p.A,{variant:"primary",onClick:M,children:s("adminsetting.user.table.Yes")})]})]}),(0,a.jsx)(m.A,{columns:e.trim&&"actions"===e.trim?T.slice(0,-1):T,data:t,totalRows:h,loading:n,subheader:!0,pagServer:!0,resetPaginationToggle:b,subHeaderComponent:F,handlePerRowsChange:O,handlePageChange:e=>{k.limit=w,k.page=e,H(k)}})]})}[c,u,A]=y.then?(await y)():y,i()}catch(e){i(e)}})},83929:(e,s,t)=>{t.a(e,async(e,i)=>{try{t.r(s),t.d(s,{default:()=>S});var a=t(8732),n=t(7082),r=t(83551),o=t(49481),d=t(91353),l=t(19918),p=t.n(l),c=t(27053),m=t(74748),u=t(88751),A=t(45927),g=t(14062),y=t(35557),h=e([m,g]);[m,g]=h.then?(await h)():h;let S=e=>{let{t:s}=(0,u.useTranslation)("common"),t=()=>(0,a.jsxs)(n.A,{style:{overflowX:"hidden"},fluid:!0,className:"p-0",children:[(0,a.jsx)(r.A,{children:(0,a.jsx)(o.A,{xs:12,children:(0,a.jsx)(c.A,{title:s("adminsetting.user.form.Users")})})}),(0,a.jsx)(r.A,{children:(0,a.jsx)(o.A,{xs:12,children:(0,a.jsx)(p(),{href:"/adminsettings/[...routes]",as:"/adminsettings/create_user",children:(0,a.jsx)(d.A,{variant:"secondary",size:"sm",children:s("adminsetting.user.form.AddUser")})})})}),(0,a.jsx)(r.A,{className:"mt-3",children:(0,a.jsx)(o.A,{xs:12,children:(0,a.jsx)(m.default,{})})})]}),i=(0,A.canAddUsers)(()=>(0,a.jsx)(t,{})),l=(0,g.useSelector)(e=>e);return l?.permissions?.users?.["create:any"]?(0,a.jsx)(i,{}):(0,a.jsx)(y.default,{})};i()}catch(e){i(e)}})}};