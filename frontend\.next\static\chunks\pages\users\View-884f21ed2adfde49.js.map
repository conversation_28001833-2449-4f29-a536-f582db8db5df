{"version": 3, "file": "static/chunks/pages/users/View-884f21ed2adfde49.js", "mappings": "+EACA,4CACA,cACA,WACA,OAAe,EAAQ,KAAmC,CAC1D,EACA,SAFsB,oECCtB,MALiB,GAEb,UAACA,MAAAA,SAGUC,CAHN,OAGcA,EAAC", "sources": ["webpack://_N_E/?0f43", "webpack://_N_E/./pages/users/View.tsx"], "sourcesContent": ["\n    (window.__NEXT_P = window.__NEXT_P || []).push([\n      \"/users/View\",\n      function () {\n        return require(\"private-next-pages/users/View.tsx\");\n      }\n    ]);\n    if(module.hot) {\n      module.hot.dispose(function () {\n        window.__NEXT_P.push([\"/users/View\"])\n      });\n    }\n  ", "const Viewuser = (_props: any) => {\r\n  return (\r\n    <div>View Users</div>\r\n  )\r\n};\r\nexport default Viewuser;"], "names": ["div", "Viewuser"], "sourceRoot": "", "ignoreList": []}