{"version": 3, "file": "static/chunks/5266-a70ace75115de296.js", "mappings": "4FA0BA,MAAY,EAAQ,KAAO,EAC3B,EAAgB,EAAQ,KAAY,CADjB,CAEnB,EAAiB,EAAQ,KAAY,CADd,CAGvB,UAFwB,CAExB,GAAqC,+CAA4D,WAEjG,WACA,OACA,OAiBA,aAQA,MAPA,8BACA,iCAAiD,IAAO,IAExD,aADA,eACA,uDAEA,QACA,GACA,qBACA,EAEA,gBACA,SACA,0EACA,YACA,4DACA,8CAA6D,WAAc,IAC3E,uEACA,kBAEA,QACA,CAGA,yCACA,6MACA,0CAAwE,4DAAqF,mCAA+C,uBAC5M,KACA,GACA,IACA,CAAS,wBACT,KACA,GACA,IACA,CAAS,2FAAkH,IAC3H,CAAC,EACD,oBACA,qCACA,2BAAuF,4CAA6E,yJACpK,YACA,gBADoC,IACpC,iFAA+M,iBAA8D,oOAC7Q,6BACA,KAnBA,eAoBA,uBACA,uDACA,QAAiC,CAPmI,cAAsS,GAOza,kEACjC,4BACA,oBACA,4BACA,iBACA,CAAK,QACL,gCAAiF,IAAa,8GAb9F,wBAAuF,CAaO,CAA0M,MACxS,0CACA,gGACA,4BACA,sBAIA,uCAA8D,YAAwB,CACtF,EACA,oCAAgF,wBAAsD,CArBtI,eAA+M,IAqBzE,EAAqB,GAAM,aALjK,MAKiK,OAHjK,GADA,QACA,GAGiK,CAH5G,IAG4G,CAA2D,EAAG,CArBhB,cAA8D,GAsB7Q,+BAA6D,+BAAwC,mBACrG,+BAA6D,+BAAwC,GACrG,CAAC,EACD,aACA,0EACA,wBACA,6BACA,qBACA,qBACA,sBACA,wBACA,kCACA,oGACA,4BACA,6CACA,mDACA,4BACA,8BACA,8BACA,2BACA,2BAIA,UAFA,yCCvImE,mBAA2N,KAAoB,aAAa,cAAc,+CAA+C,WAAW,kBAAkB,gBAAgB,YAAY,WAAW,KAAK,WAAW,+GAA+G,kBAAkB,yCAAyC,kDAAkD,WAAW,aAAa,oCAAoC,YAAY,mBAAmB,KAAK,mBAAmB,sEAAsE,UAAS,uBAAwB,gBAAgB,uBAAuB,mGAAmG,wDAAuD,uBAAwB,cAAc,YAAY,mBAAmB,KAAK,yCAAyC,wCAAwC,YAAY,mIAAmI,gEAAgE,EAAE,SAAS,cAAc,iEAAiE,8CAA6C,IAAK,gBAAgB,8CAA8C,wBAAuB,MAAsX,cAAc,gGAAoG,SAAud,gBAAgB,mBAAmB,6BAA6B,mBAAmB,6DAAsE,4BAA4B,IAAI,iCAAiC,2DAA2D,OAAO,SAAS,SAAS,QAAQ,IAAI,8BAA8B,QAAQ,cAAc,UAAS,0BAA0B,6JAAiK,GAAG,gBAAgB,MAAM,oCAAoC,oDAAoD,gLAAgL,gBAAgB,oCAAoC,uBAA2B,IAAI,cAAc,SAAS,gBAAgB,iCAAiC,gBAAkB,oBAAoB,YAAY,iEAAqE,qBAAqB,0BAA0B,sDAAsD,EAAE,2JAA6J,OAAO,OAAO,8IAA8I,SAAS,iIAAiI,UAAU,OAAO,uEAAuE,SAAS,kEAAkE,0GAA0G,UAAU,cAAc,MAAmO,oBAApN,uGAA4G,CAAwG,EAAxG,wCAA0C,aAAa,MAAiD,EAAjD,6BAAqC,OAAY,EAAZ,GAAY,IAAvlF,EAAknF,OAAlnF,aAAiB,oDAA4D,uBAA5D,SAA+F,qCAAqC,IAAI,0EAA0E,MAAM,SAAS,UAAU,GAAG,WAAkB,UAAi2E,GAA90E,gJAA+1E,cAAc,MAAM,qBAAqB,0EAA8E,wCAAwC,gCAAgC,wCAAwC,yCAAyC,iFAAiF,gBAAgB,gDAAgD,iCAAiC,oFAAoF,QAAQ,EAAE,uCAAuC,iDAAiD,wBAAwB,EAAE,oCAAoC,2BAA2B,QAAQ,uBAAuB,uCAAuC,oCAAoC,4DAA4D,gHAAgH,gNAAwP,WAAxP,eAAiO,uBAAuB,qCAAmD,sFAAsF,2CAA2C,0DAA0D,iCAAiC,WAAW,kBAAkB,WAAgB,yCAAyC,sCAAsC,uCAAuC,2FAA2F,SAAS,IAAI,8BAA8B,YAAY,eAAe,MAAM,EAAE,2EAA2E,SAAS,KAAK,SAAS,KAAK,WAAW,yPAAyP,EAAE,6CAA6C,yOAAyO,mCAAmC,oeAAoe,EAAE,4CAA4C,MAAM,mRAAmR,EAAE,kCAAkC,mEAAmE,EAAE,mCAAmC,yDAAyD,iCAAiC,EAAE,qCAAqC,gEAAgE,yCAAiD,kCAAjD,EAAiD,SAAjD,CAAiD,WAAjD,EAAiD,SAAjD,CAAiD,8BAAmG,8CAA8C,EAAE,gCAAgC,gDAAgD,mGAAmG,uCAAuC,0FAA0F,yBAAyB,gVAAgV,EAAE,8CAA8C,mFAAmF,mIAAmI,EAAE,iCAA2H,kBAA1F,oFAA0F,EAAwB,EAAE,iCAA2H,kBAA1F,oFAA0F,EAAwB,EAAE,uCAAuC,4BAA4B,0CAA0C,gFAAgF,qCAAqC,kCAAkC,yFAAyF,MAAM,EAAE,4DAA4D,GAAG,EAAE,kCAAkC,GAAM,8DAAsE,qBAAqB,qBAAqB,IAAI,yBAAyB,KAAK,SAAS,MAAM,iBAAjK,EAAiK,CAAoB,+LAAgM,EAAE,yCAAyC,+BAA+B,4GAA4G,mBAAmB,EAAE,yCAAyC,2CAA2C,sfAAsf,EAAE,4CAA4C,sBAAsB,8IAA8I,kDAAkD,2BAA2B,EAAE,8BAA8B,qGAAqG,iKAAuK,oWAAgX,EAAE,8BAA8B,uUAAzpV,cAAgB,oBAAoB,sBAAsB,oBAAoB,cAAc,sBAAsB,WAAW,wCAAwC,SAAS,MAAM,8EAA8E,WAAW,yFAAyF,UAAmzU,+UAAwpB,wGAAwG,IAAI,4DAA4D,QAAQ,+HAA+H,mBAAmB,QAAQ,wBAAr/N,EAAq/N,KAAiC,GAAG,wBAAwB,+GAA+G,gCAAgC,mDAAmD,iFAAiF,sBAAsB,wJAA4J,+LAA+L,sCAAsC,icAAic,sBAAsB,0GAA0G,2BAA2B,0BAA0B,2BAA2B,uBAAuB,yBAAyB,8BAA8B,0EAA0E,IAAI,CAAltd,EAAQ,KAAY,EAAE,EAAQ,KAAO,GAA9B,CAAiM,CAAC", "sources": ["webpack://_N_E/./node_modules/react-bootstrap-range-slider/dist/index.js", "webpack://_N_E/./node_modules/react-avatar-editor/dist/index.js"], "sourcesContent": ["/**\n * MIT License\n *\n * Copyright (c) 2020 <PERSON>\n *\n * Permission is hereby granted, free of charge, to any person obtaining a copy\n * of this software and associated documentation files (the \"Software\"), to deal\n * in the Software without restriction, including without limitation the rights\n * to use, copy, modify, merge, publish, distribute, sublicense, and/or sell\n * copies of the Software, and to permit persons to whom the Software is\n * furnished to do so, subject to the following conditions:\n *\n * The above copyright notice and this permission notice shall be included in all\n * copies or substantial portions of the Software.\n *\n * THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\n * IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\n * FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\n * AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\n * LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\n * OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE\n * SOFTWARE.\n */\n\n'use strict';\n\nvar React = require('react');\nvar PropTypes = require('prop-types');\nvar classNames = require('classnames');\n\nfunction _interopDefaultLegacy (e) { return e && typeof e === 'object' && 'default' in e ? e : { 'default': e }; }\n\nvar React__default = /*#__PURE__*/_interopDefaultLegacy(React);\nvar PropTypes__default = /*#__PURE__*/_interopDefaultLegacy(PropTypes);\nvar classNames__default = /*#__PURE__*/_interopDefaultLegacy(classNames);\n\n/*! *****************************************************************************\r\nCopyright (c) Microsoft Corporation.\r\n\r\nPermission to use, copy, modify, and/or distribute this software for any\r\npurpose with or without fee is hereby granted.\r\n\r\nTHE SOFTWARE IS PROVIDED \"AS IS\" AND THE AUTHOR DISCLAIMS ALL WARRANTIES WITH\r\nREGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF MERCHANTABILITY\r\nAND FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY SPECIAL, DIRECT,\r\nINDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES WHATSOEVER RESULTING FROM\r\nLOSS OF USE, DATA OR PROFITS, WHETHER IN AN ACTION OF CONTRACT, NEGLIGENCE OR\r\nOTHER TORTIOUS ACTION, ARISING OUT OF OR IN CONNECTION WITH THE USE OR\r\nPERFORMANCE OF THIS SOFTWARE.\r\n***************************************************************************** */\r\n\r\nvar __assign = function() {\r\n    __assign = Object.assign || function __assign(t) {\r\n        for (var s, i = 1, n = arguments.length; i < n; i++) {\r\n            s = arguments[i];\r\n            for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p)) t[p] = s[p];\r\n        }\r\n        return t;\r\n    };\r\n    return __assign.apply(this, arguments);\r\n};\r\n\r\nfunction __rest(s, e) {\r\n    var t = {};\r\n    for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0)\r\n        t[p] = s[p];\r\n    if (s != null && typeof Object.getOwnPropertySymbols === \"function\")\r\n        for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {\r\n            if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i]))\r\n                t[p[i]] = s[p[i]];\r\n        }\r\n    return t;\r\n}\n\nvar DEFAULT_CLASS_PREFIX = 'range-slider';\nvar Input = React__default['default'].forwardRef(function (_a, ref) {\n    var classes = _a.classes, value = _a.value, min = _a.min, max = _a.max, onChange = _a.onChange, onMouseUpOrTouchEnd = _a.onMouseUpOrTouchEnd, onMouseUp = _a.onMouseUp, onTouchEnd = _a.onTouchEnd, rest = __rest(_a, [\"classes\", \"value\", \"min\", \"max\", \"onChange\", \"onMouseUpOrTouchEnd\", \"onMouseUp\", \"onTouchEnd\"]);\n    return (React__default['default'].createElement(\"input\", __assign({ ref: ref, type: \"range\", value: value, min: min, max: max, onChange: function (ev) { return onChange(ev, ev.target.valueAsNumber); }, onMouseUp: function (ev) {\n            onMouseUpOrTouchEnd(ev);\n            if (onMouseUp)\n                onMouseUp(ev);\n        }, onTouchEnd: function (ev) {\n            onMouseUpOrTouchEnd(ev);\n            if (onTouchEnd)\n                onTouchEnd(ev);\n        }, className: classes, \"aria-valuenow\": Number(value), \"aria-valuemin\": Number(min), \"aria-valuemax\": Number(max) }, rest)));\n});\nvar InputMemo = React__default['default'].memo(Input);\nvar RangeSlider = React__default['default'].forwardRef(function (_a, ref) {\n    var value = _a.value, _b = _a.onChange, onChange = _b === void 0 ? function () { } : _b, _c = _a.onAfterChange, onAfterChange = _c === void 0 ? function () { } : _c, _d = _a.disabled, disabled = _d === void 0 ? false : _d, size = _a.size, _e = _a.min, min = _e === void 0 ? 0 : _e, _f = _a.max, max = _f === void 0 ? 100 : _f, step = _a.step, _g = _a.variant, variant = _g === void 0 ? 'primary' : _g, _h = _a.inputProps, inputProps = _h === void 0 ? {} : _h, // deprecated; add additional props directly instead\n    _j = _a.tooltip, // deprecated; add additional props directly instead\n    tooltip = _j === void 0 ? 'auto' : _j, _k = _a.tooltipPlacement, tooltipPlacement = _k === void 0 ? 'bottom' : _k, tooltipLabel = _a.tooltipLabel, _l = _a.tooltipStyle, tooltipStyle = _l === void 0 ? {} : _l, _m = _a.tooltipProps, tooltipProps = _m === void 0 ? {} : _m, bsPrefix = _a.bsPrefix, className = _a.className, props = __rest(_a, [\"value\", \"onChange\", \"onAfterChange\", \"disabled\", \"size\", \"min\", \"max\", \"step\", \"variant\", \"inputProps\", \"tooltip\", \"tooltipPlacement\", \"tooltipLabel\", \"tooltipStyle\", \"tooltipProps\", \"bsPrefix\", \"className\"]);\n    var _o = React.useState(), prevValue = _o[0], setPrevValue = _o[1];\n    var prefix = bsPrefix || DEFAULT_CLASS_PREFIX;\n    var isTooltip = tooltip === 'auto' || tooltip === 'on';\n    var classes = classNames__default['default'](className, prefix, size && prefix + \"--\" + size, disabled && 'disabled', variant && prefix + \"--\" + variant);\n    var _p = __assign(__assign({}, inputProps), props), onMouseUp = _p.onMouseUp, onTouchEnd = _p.onTouchEnd, restProps = __rest(_p, [\"onMouseUp\", \"onTouchEnd\"]);\n    var onMouseUpOrTouchEnd = React.useCallback(function (ev) {\n        if (prevValue !== ev.target.value)\n            onAfterChange(ev, ev.target.valueAsNumber);\n        setPrevValue(ev.target.value);\n    }, [prevValue, onAfterChange]);\n    var inputEl = (React__default['default'].createElement(InputMemo, __assign({}, __assign({ disabled: disabled, value: value, min: min, max: max, ref: ref, step: step, classes: classes, onMouseUpOrTouchEnd: onMouseUpOrTouchEnd, onTouchEnd: onTouchEnd, onMouseUp: onMouseUp, onChange: onChange }, restProps))));\n    var wrapClasses = classNames__default['default'](prefix + \"__wrap\", size && prefix + \"__wrap--\" + size);\n    var tooltipClasses = classNames__default['default'](prefix + \"__tooltip\", isTooltip && prefix + \"__tooltip--\" + tooltip, tooltipPlacement && prefix + \"__tooltip--\" + tooltipPlacement, disabled && prefix + \"__tooltip--disabled\");\n    var thumbRadius = size === 'sm' ? 8 : (size === 'lg' ? 12 : 10);\n    var fract = (Number(value) - min) / (max - min);\n    var percentLeft = fract * 100;\n    var fractFromCentre = (fract - 0.5) * 2;\n    var adjustment = fractFromCentre * -thumbRadius; // Half thumb width\n    return (React__default['default'].createElement(\"span\", { className: wrapClasses },\n        inputEl,\n        isTooltip && (React__default['default'].createElement(\"div\", __assign({ className: tooltipClasses, style: __assign(__assign({}, (tooltipStyle || {})), { left: \"calc(\" + percentLeft + \"% + \" + adjustment + \"px)\" }) }, tooltipProps),\n            React__default['default'].createElement(\"div\", { className: prefix + \"__tooltip__label\" }, tooltipLabel ? tooltipLabel(Number(value)) : value),\n            React__default['default'].createElement(\"div\", { className: prefix + \"__tooltip__caret\" })))));\n});\nRangeSlider.propTypes = {\n    value: PropTypes__default['default'].oneOfType([PropTypes__default['default'].number, PropTypes__default['default'].string]).isRequired,\n    onChange: PropTypes__default['default'].func,\n    onAfterChange: PropTypes__default['default'].func,\n    min: PropTypes__default['default'].number,\n    max: PropTypes__default['default'].number,\n    step: PropTypes__default['default'].number,\n    disabled: PropTypes__default['default'].bool,\n    size: PropTypes__default['default'].oneOf(['sm', 'lg']),\n    variant: PropTypes__default['default'].oneOf(['primary', 'secondary', 'success', 'danger', 'warning', 'info', 'dark', 'light']),\n    inputProps: PropTypes__default['default'].object,\n    tooltip: PropTypes__default['default'].oneOf(['auto', 'on', 'off']),\n    tooltipPlacement: PropTypes__default['default'].oneOf(['top', 'bottom']),\n    tooltipLabel: PropTypes__default['default'].func,\n    tooltipStyle: PropTypes__default['default'].object,\n    tooltipProps: PropTypes__default['default'].object,\n    className: PropTypes__default['default'].string,\n    bsPrefix: PropTypes__default['default'].string,\n};\nvar RangeSlider$1 = React__default['default'].memo(RangeSlider);\n\nmodule.exports = RangeSlider$1;\n//# sourceMappingURL=index.js.map\n", "!function(e,t){\"object\"==typeof exports&&\"undefined\"!=typeof module?module.exports=t(require(\"prop-types\"),require(\"react\")):\"function\"==typeof define&&define.amd?define([\"prop-types\",\"react\"],t):(e=\"undefined\"!=typeof globalThis?globalThis:e||self).AvatarEditor=t(e.PropTypes,e.React)}(this,function(e,t){\"use strict\";function o(e){return e&&\"object\"==typeof e&&\"default\"in e?e:{default:e}}var n=o(e),i=o(t);function r(e,t){for(var o=0;o<t.length;o++){var n=t[o];n.enumerable=n.enumerable||!1,n.configurable=!0,\"value\"in n&&(n.writable=!0),Object.defineProperty(e,n.key,n)}}function s(e,t,o){return t in e?Object.defineProperty(e,t,{value:o,enumerable:!0,configurable:!0,writable:!0}):e[t]=o,e}function u(){return(u=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var o=arguments[t];for(var n in o)Object.prototype.hasOwnProperty.call(o,n)&&(e[n]=o[n])}return e}).apply(this,arguments)}function a(t,e){var o,n=Object.keys(t);return Object.getOwnPropertySymbols&&(o=Object.getOwnPropertySymbols(t),e&&(o=o.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),n.push.apply(n,o)),n}function y(t){for(var e=1;e<arguments.length;e++){var o=null!=arguments[e]?arguments[e]:{};e%2?a(Object(o),!0).forEach(function(e){s(t,e,o[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(o)):a(Object(o)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(o,e))})}return t}function h(e){return(h=Object.setPrototypeOf?Object.getPrototypeOf:function(e){return e.__proto__||Object.getPrototypeOf(e)})(e)}function c(e,t){return(c=Object.setPrototypeOf||function(e,t){return e.__proto__=t,e})(e,t)}function l(e,t){if(null==e)return{};var o,n=function(e,t){if(null==e)return{};for(var o,n={},a=Object.keys(e),r=0;r<a.length;r++)o=a[r],0<=t.indexOf(o)||(n[o]=e[o]);return n}(e,t);if(Object.getOwnPropertySymbols)for(var a=Object.getOwnPropertySymbols(e),r=0;r<a.length;r++)o=a[r],0<=t.indexOf(o)||Object.prototype.propertyIsEnumerable.call(e,o)&&(n[o]=e[o]);return n}function d(e){if(void 0===e)throw new ReferenceError(\"this hasn't been initialised - super() hasn't been called\");return e}function p(r){var i=function(){if(\"undefined\"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if(\"function\"==typeof Proxy)return!0;try{return Date.prototype.toString.call(Reflect.construct(Date,[],function(){})),!0}catch(e){return!1}}();return function(){var e,t,o,n,a=h(r);return t=i?(e=h(this).constructor,Reflect.construct(a,arguments,e)):a.apply(this,arguments),o=this,!(n=t)||\"object\"!=typeof n&&\"function\"!=typeof n?d(o):n}}function m(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){if(\"undefined\"==typeof Symbol||!(Symbol.iterator in Object(e)))return;var o=[],n=!0,a=!1,r=void 0;try{for(var i,s=e[Symbol.iterator]();!(n=(i=s.next()).done)&&(o.push(i.value),!t||o.length!==t);n=!0);}catch(e){a=!0,r=e}finally{try{n||null==s.return||s.return()}finally{if(a)throw r}}return o}(e,t)||g(e,t)||function(){throw new TypeError(\"Invalid attempt to destructure non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.\")}()}function g(e,t){if(e){if(\"string\"==typeof e)return f(e,t);var o=Object.prototype.toString.call(e).slice(8,-1);return\"Object\"===o&&e.constructor&&(o=e.constructor.name),\"Map\"===o||\"Set\"===o?Array.from(e):\"Arguments\"===o||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(o)?f(e,t):void 0}}function f(e,t){(null==t||t>e.length)&&(t=e.length);for(var o=0,n=new Array(t);o<t;o++)n[o]=e[o];return n}function v(a,r){return new Promise(function(e,t){var o,n=new Image;n.onload=function(){return e(n)},n.onerror=t,!1==(null!==(o=a)&&!!o.match(/^\\s*data:([a-z]+\\/[a-z]+(;[a-z-]+=[a-z-]+)?)?(;base64)?,[a-z0-9!$&',()*+;=\\-._~:@/?%\\s]*\\s*$/i))&&r&&(n.crossOrigin=r),n.src=a})}var b,w=!(\"undefined\"==typeof window||\"undefined\"==typeof navigator||!(\"ontouchstart\"in window||0<navigator.msMaxTouchPoints)),M=\"undefined\"!=typeof File,O={touch:{react:{down:\"onTouchStart\",mouseDown:\"onMouseDown\",drag:\"onTouchMove\",move:\"onTouchMove\",mouseMove:\"onMouseMove\",up:\"onTouchEnd\",mouseUp:\"onMouseUp\"},native:{down:\"touchstart\",mouseDown:\"mousedown\",drag:\"touchmove\",move:\"touchmove\",mouseMove:\"mousemove\",up:\"touchend\",mouseUp:\"mouseup\"}},desktop:{react:{down:\"onMouseDown\",drag:\"onDragOver\",move:\"onMouseMove\",up:\"onMouseUp\"},native:{down:\"mousedown\",drag:\"dragStart\",move:\"mousemove\",up:\"mouseup\"}}},I=w?O.touch:O.desktop,P=\"undefined\"!=typeof window&&window.devicePixelRatio?window.devicePixelRatio:1,C={x:.5,y:.5},x=function(){!function(e,t){if(\"function\"!=typeof t&&null!==t)throw new TypeError(\"Super expression must either be null or a function\");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),t&&c(e,t)}(a,i[\"default\"].Component);var e,t,o,n=p(a);function a(e){var v;return function(e,t){if(!(e instanceof t))throw new TypeError(\"Cannot call a class as a function\")}(this,a),s(d(v=n.call(this,e)),\"state\",{drag:!1,my:null,mx:null,image:C}),s(d(v),\"handleImageReady\",function(e){var t=v.getInitialSize(e.width,e.height);t.resource=e,t.x=.5,t.y=.5,t.backgroundColor=v.props.backgroundColor,v.setState({drag:!1,image:t},v.props.onImageReady),v.props.onLoadSuccess(t)}),s(d(v),\"clearImage\",function(){v.canvas.getContext(\"2d\").clearRect(0,0,v.canvas.width,v.canvas.height),v.setState({image:C})}),s(d(v),\"handleMouseDown\",function(e){(e=e||window.event).preventDefault(),v.setState({drag:!0,mx:null,my:null})}),s(d(v),\"handleMouseUp\",function(){v.state.drag&&(v.setState({drag:!1}),v.props.onMouseUp())}),s(d(v),\"handleMouseMove\",function(e){var t,o,n,a,r,i,s,u,h,c,l,d,p,g,f,m;e=e||window.event,!1!==v.state.drag&&(e.preventDefault(),n={mx:t=e.targetTouches?e.targetTouches[0].pageX:e.clientX,my:o=e.targetTouches?e.targetTouches[0].pageY:e.clientY},m=v.props.rotate,m=(m%=360)<0?m+360:m,v.state.mx&&v.state.my&&(a=v.state.mx-t,r=v.state.my-o,i=v.state.image.width*v.props.scale,s=v.state.image.height*v.props.scale,h=(u=v.getCroppingRect()).x,c=u.y,h*=i,c*=s,l=function(e){return e*(Math.PI/180)},d=Math.cos(l(m)),g=c+-a*(p=Math.sin(l(m)))+r*d,f={x:(h+a*d+r*p)/i+1/v.props.scale*v.getXScale()/2,y:g/s+1/v.props.scale*v.getYScale()/2},v.props.onPositionChange(f),n.image=y(y({},v.state.image),f)),v.setState(n),v.props.onMouseMove(e))}),s(d(v),\"setCanvas\",function(e){v.canvas=e}),v.canvas=null,v}return e=a,(t=[{key:\"componentDidMount\",value:function(){this.props.disableHiDPIScaling&&(P=1);var e,t,o=this.canvas.getContext(\"2d\");this.props.image&&this.loadImage(this.props.image),this.paint(o),document&&(e=!!function(){var t=!1;try{var e=Object.defineProperty({},\"passive\",{get:function(){t=!0}});window.addEventListener(\"test\",e,e),window.removeEventListener(\"test\",e,e)}catch(e){t=!1}return t}()&&{passive:!1},t=I.native,document.addEventListener(t.move,this.handleMouseMove,e),document.addEventListener(t.up,this.handleMouseUp,e),w&&(document.addEventListener(t.mouseMove,this.handleMouseMove,e),document.addEventListener(t.mouseUp,this.handleMouseUp,e)))}},{key:\"componentDidUpdate\",value:function(e,t){this.props.image&&this.props.image!==e.image||this.props.width!==e.width||this.props.height!==e.height||this.props.backgroundColor!==e.backgroundColor?this.loadImage(this.props.image):this.props.image||t.image===C||this.clearImage();var o=this.canvas.getContext(\"2d\");o.clearRect(0,0,this.canvas.width,this.canvas.height),this.paint(o),this.paintImage(o,this.state.image,this.props.border),e.image===this.props.image&&e.width===this.props.width&&e.height===this.props.height&&e.position===this.props.position&&e.scale===this.props.scale&&e.rotate===this.props.rotate&&t.my===this.state.my&&t.mx===this.state.mx&&t.image.x===this.state.image.x&&t.image.y===this.state.image.y&&t.backgroundColor===this.state.backgroundColor||this.props.onImageChange()}},{key:\"componentWillUnmount\",value:function(){var e;document&&(e=I.native,document.removeEventListener(e.move,this.handleMouseMove,!1),document.removeEventListener(e.up,this.handleMouseUp,!1),w&&(document.removeEventListener(e.mouseMove,this.handleMouseMove,!1),document.removeEventListener(e.mouseUp,this.handleMouseUp,!1)))}},{key:\"isVertical\",value:function(){return!this.props.disableCanvasRotation&&this.props.rotate%180!=0}},{key:\"getBorders\",value:function(e){var t=0<arguments.length&&void 0!==e?e:this.props.border;return Array.isArray(t)?t:[t,t]}},{key:\"getDimensions\",value:function(){var e=this.props,t=e.width,o=e.height,n=e.rotate,a=e.border,r={},i=m(this.getBorders(a),2),s=i[0],u=i[1],h=t,c=o;return this.isVertical()?(r.width=c,r.height=h):(r.width=h,r.height=c),r.width+=2*s,r.height+=2*u,{canvas:r,rotate:n,width:t,height:o,border:a}}},{key:\"getImage\",value:function(){var e=this.getCroppingRect(),t=this.state.image;e.x*=t.resource.width,e.y*=t.resource.height,e.width*=t.resource.width,e.height*=t.resource.height;var o=document.createElement(\"canvas\");this.isVertical()?(o.width=e.height,o.height=e.width):(o.width=e.width,o.height=e.height);var n=o.getContext(\"2d\");return n.translate(o.width/2,o.height/2),n.rotate(this.props.rotate*Math.PI/180),n.translate(-o.width/2,-o.height/2),this.isVertical()&&n.translate((o.width-o.height)/2,(o.height-o.width)/2),t.backgroundColor&&(n.fillStyle=t.backgroundColor,n.fillRect(-e.x,-e.y,t.resource.width,t.resource.height)),n.drawImage(t.resource,-e.x,-e.y),o}},{key:\"getImageScaledToCanvas\",value:function(){var e=this.getDimensions(),t=e.width,o=e.height,n=document.createElement(\"canvas\");return this.isVertical()?(n.width=o,n.height=t):(n.width=t,n.height=o),this.paintImage(n.getContext(\"2d\"),this.state.image,0,1),n}},{key:\"getXScale\",value:function(){var e=this.props.width/this.props.height,t=this.state.image.width/this.state.image.height;return Math.min(1,e/t)}},{key:\"getYScale\",value:function(){var e=this.props.height/this.props.width,t=this.state.image.height/this.state.image.width;return Math.min(1,e/t)}},{key:\"getCroppingRect\",value:function(){var e=this.props.position||{x:this.state.image.x,y:this.state.image.y},t=1/this.props.scale*this.getXScale(),o=1/this.props.scale*this.getYScale(),n={x:e.x-t/2,y:e.y-o/2,width:t,height:o},a=0,r=1-n.width,i=0,s=1-n.height;return(this.props.disableBoundaryChecks||1<t||1<o)&&(a=-n.width,i=-n.height,s=r=1),y(y({},n),{},{x:Math.max(a,Math.min(n.x,r)),y:Math.max(i,Math.min(n.y,s))})}},{key:\"loadImage\",value:function(e){var t;M&&e instanceof File?this.loadingImage=(t=e,new Promise(function(o,n){var e=new FileReader;e.onload=function(e){try{var t=v(e.target.result);o(t)}catch(e){n(e)}},e.readAsDataURL(t)}).then(this.handleImageReady).catch(this.props.onLoadFailure)):\"string\"==typeof e&&(this.loadingImage=v(e,this.props.crossOrigin).then(this.handleImageReady).catch(this.props.onLoadFailure))}},{key:\"getInitialSize\",value:function(e,t){var o,n,a=this.getDimensions();return t/e<a.height/a.width?n=e*((o=this.getDimensions().height)/t):o=t*((n=this.getDimensions().width)/e),{height:o,width:n}}},{key:\"paintImage\",value:function(e,t,o,n){var a,r=3<arguments.length&&void 0!==n?n:P;t.resource&&(a=this.calculatePosition(t,o),e.save(),e.translate(e.canvas.width/2,e.canvas.height/2),e.rotate(this.props.rotate*Math.PI/180),e.translate(-e.canvas.width/2,-e.canvas.height/2),this.isVertical()&&e.translate((e.canvas.width-e.canvas.height)/2,(e.canvas.height-e.canvas.width)/2),e.scale(r,r),e.globalCompositeOperation=\"destination-over\",e.drawImage(t.resource,a.x,a.y,a.width,a.height),t.backgroundColor&&(e.fillStyle=t.backgroundColor,e.fillRect(a.x,a.y,a.width,a.height)),e.restore())}},{key:\"calculatePosition\",value:function(e,t){e=e||this.state.image;var o=m(this.getBorders(t),2),n=o[0],a=o[1],r=this.getCroppingRect(),i=e.width*this.props.scale,s=e.height*this.props.scale,u=-r.x*i,h=-r.y*s;return this.isVertical()?(u+=a,h+=n):(u+=n,h+=a),{x:u,y:h,height:s,width:i}}},{key:\"paint\",value:function(e){e.save(),e.scale(P,P),e.translate(0,0),e.fillStyle=\"rgba(\"+this.props.color.slice(0,4).join(\",\")+\")\";var t,o,n,a,r,i,s,u,h=this.props.borderRadius,c=this.getDimensions(),l=m(this.getBorders(c.border),2),d=l[0],p=l[1],g=c.canvas.height,f=c.canvas.width,h=Math.max(h,0);h=Math.min(h,f/2-d,g/2-p),e.beginPath(),t=e,a=f-2*(o=d),r=g-2*(n=p),0===(i=h)?t.rect(o,n,a,r):(s=a-i,u=r-i,t.translate(o,n),t.arc(i,i,i,Math.PI,1.5*Math.PI),t.lineTo(s,0),t.arc(s,i,i,1.5*Math.PI,2*Math.PI),t.lineTo(a,u),t.arc(s,u,i,2*Math.PI,.5*Math.PI),t.lineTo(i,r),t.arc(i,u,i,.5*Math.PI,Math.PI),t.translate(-o,-n)),e.rect(f,0,-f,g),e.fill(\"evenodd\"),e.restore()}},{key:\"render\",value:function(){var e=this.props,t=(e.scale,e.rotate,e.image,e.border,e.borderRadius,e.width,e.height,e.position,e.color,e.backgroundColor,e.style),o=(e.crossOrigin,e.onLoadFailure,e.onLoadSuccess,e.onImageReady,e.onImageChange,e.onMouseUp,e.onMouseMove,e.onPositionChange,e.disableBoundaryChecks,e.disableHiDPIScaling,e.disableCanvasRotation,l(e,[\"scale\",\"rotate\",\"image\",\"border\",\"borderRadius\",\"width\",\"height\",\"position\",\"color\",\"backgroundColor\",\"style\",\"crossOrigin\",\"onLoadFailure\",\"onLoadSuccess\",\"onImageReady\",\"onImageChange\",\"onMouseUp\",\"onMouseMove\",\"onPositionChange\",\"disableBoundaryChecks\",\"disableHiDPIScaling\",\"disableCanvasRotation\"])),n=this.getDimensions(),a={width:n.canvas.width,height:n.canvas.height,cursor:this.state.drag?\"grabbing\":\"grab\",touchAction:\"none\"},r={width:n.canvas.width*P,height:n.canvas.height*P,style:y(y({},a),t)};return r[I.react.down]=this.handleMouseDown,w&&(r[I.react.mouseDown]=this.handleMouseDown),i.default.createElement(\"canvas\",u({ref:this.setCanvas},r,o))}}])&&r(e.prototype,t),o&&r(e,o),a}();return s(x,\"propTypes\",{scale:n.default.number,rotate:n.default.number,image:n.default.oneOfType([n.default.string].concat(function(e){if(Array.isArray(e))return f(e)}(b=M?[n.default.instanceOf(File)]:[])||function(e){if(\"undefined\"!=typeof Symbol&&Symbol.iterator in Object(e))return Array.from(e)}(b)||g(b)||function(){throw new TypeError(\"Invalid attempt to spread non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.\")}())),border:n.default.oneOfType([n.default.number,n.default.arrayOf(n.default.number)]),borderRadius:n.default.number,width:n.default.number,height:n.default.number,position:n.default.shape({x:n.default.number,y:n.default.number}),color:n.default.arrayOf(n.default.number),backgroundColor:n.default.string,crossOrigin:n.default.oneOf([\"\",\"anonymous\",\"use-credentials\"]),onLoadFailure:n.default.func,onLoadSuccess:n.default.func,onImageReady:n.default.func,onImageChange:n.default.func,onMouseUp:n.default.func,onMouseMove:n.default.func,onPositionChange:n.default.func,disableBoundaryChecks:n.default.bool,disableHiDPIScaling:n.default.bool,disableCanvasRotation:n.default.bool}),s(x,\"defaultProps\",{scale:1,rotate:0,border:25,borderRadius:0,width:200,height:200,color:[0,0,0,.5],onLoadFailure:function(){},onLoadSuccess:function(){},onImageReady:function(){},onImageChange:function(){},onMouseUp:function(){},onMouseMove:function(){},onPositionChange:function(){},disableBoundaryChecks:!1,disableHiDPIScaling:!1,disableCanvasRotation:!0}),x});\n"], "names": [], "sourceRoot": "", "ignoreList": [0, 1]}