{"version": 3, "file": "static/chunks/pages/dashboard/OngoingOperations-a48baf5a99b5215c.js", "mappings": "gFACA,4CACA,+BACA,WACA,OAAe,EAAQ,KAAoD,CAC3E,EACA,SAFsB,kECAP,SAASA,IACtB,MACE,WAACC,EAAAA,EAAaA,CAAAA,CACZC,QAAQ,aACRC,OAAQ,GACRC,MAAO,IACPC,MAAO,EACPC,MAAO,UACPC,gBAAgB,UAChBC,gBAAgB,UAChBC,UAAW,sBAEX,UAACC,OAAAA,CAAKC,EAAE,KAAKC,EAAE,IAAIC,GAAG,IAAIC,GAAG,IAAIV,MAAM,MAAMD,OAAO,OACpD,UAACO,OAAAA,CAAKC,EAAE,KAAKC,EAAE,KAAKC,GAAG,IAAIC,GAAG,IAAIV,MAAM,MAAMD,OAAO,SAG3D,4KCJA,SAASY,EAAUC,CAAqB,EACtC,GAAM,MAAEC,CAAI,CAAE,CAAGD,SACjB,EAASE,MAAM,CAAG,EAEd,CAFiB,EAEjB,OAACC,EAAAA,CAASA,CAAAA,UACPF,EAAKG,GAAG,CAAC,CAACC,EAAWC,IAElB,UAACH,EAAAA,CAASA,CAACI,IAAI,WAEb,UAACC,IAAIA,CAACC,KAAK,yBAAyBC,GAAI,WAAnCF,QAA+D,OAATH,EAAKM,GAAG,WAEhEN,EAAKf,KAAK,IAHRgB,MAYV,IACT,CASA,SAASM,EAAYZ,CAAuB,EAC1C,GAAM,WAAEa,CAAS,CAAE,CAAGb,EACtB,MACE,UAACQ,IAAIA,CACHC,KAAK,yBACLC,GAAI,WAFDF,QAEiC,OAAbK,EAAUC,EAAE,EACnCC,UAAU,6BAEV,UAACC,OAAAA,CAAKD,UAAU,8BAAsBF,EAAUI,IAAI,IAI1D,CAgFA,MAzEA,SAASC,CAA+C,EACtD,GAAM,CAACC,CAAC,WAwEKD,aAxEHE,CAAsB,CAAC,CAAGpB,CAwENkB,CAvExBG,CAuEyB,CAvEZF,EAAE,qBACf,CAACN,EAAWS,EAAa,CAAGC,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAA4C,CAAEN,KAAM,GAAIH,GAAI,GAAIb,KAAM,EAAE,GAC5G,CAACuB,EAASC,EAAW,CAAGF,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,EAAC,GAEjCG,EAAiB,KACrBJ,EAAa,CAAEL,KAAME,EAAE,wBAAyBL,GAAI,GAAIb,KAAM,EAAG,EACnE,EAEM0B,EAAiB,UACrB,IAAMC,EAAkB,CACtBC,MAAO,CAAEC,OAAQ,EAAE,EACnBC,KAAM,CAAEC,WAAY,MAAO,EAC3BC,MAAO,GACPC,OAAQ,sLACV,EACMC,EAAgB,MAAMC,IAE5B,GAAID,EAAU,CACZP,EAAgBC,KAAK,CAACC,MAAM,CAAGK,EAC/B,GAAI,CACFV,EAAW,IACX,IAAMY,EAAa,MAAMC,EAAAA,CAAUA,CAACC,GAAG,CAAC,aAAcX,GACtDH,GAAW,GACPe,MAAMC,OAAO,CAACJ,EAAWK,IAAI,GAAKL,EAAWK,IAAI,CAACxC,MAAM,CAAG,GAAG,EACnD,CAAEe,KAAMoB,EAAWK,IAAI,CAAC,EAAE,CAACpD,KAAK,CAAEwB,GAAIuB,EAAWK,IAAI,CAAC,EAAE,CAAC/B,GAAG,CAAEV,KAAMoC,EAAWK,IAAI,GAChGtB,EAAuBiB,EAAWK,IAAI,GAEtChB,GAEJ,CAAE,MAAOiB,EAAG,CACVjB,GACF,CACF,MACEA,CADK,EAIT,EAEMU,EAAuB,UAC3B,IAAMQ,EAAW,MAAMN,EAAAA,CAAUA,CAACC,GAAG,CAAC,qBACtC,GAAIK,GAAYA,EAASF,IAAI,EAAIE,EAASF,IAAI,CAACxC,MAAM,CAAG,EAAG,CACzD,IAAMiC,EAAkB,EAAE,CAM1B,OALAU,IAAAA,OAAS,CAACD,EAASF,IAAI,CAAE,SAAUrC,CAAS,EACxB,WAAdA,EAAKf,KAAK,EACZ6C,EAASW,IAAI,CAACzC,EAAKM,GAAG,CAE1B,GACOwB,CACT,CACA,OAAO,CACT,EAEAY,CAAAA,EAAAA,EAAAA,SAAAA,CAASA,CAAC,KACRpB,GACF,EAAG,EAAE,EAEL,IAAM1B,EAAO,CACX+C,QAAS3B,EACTJ,KAAM,UAAClB,EAAAA,CAAUE,KAAMY,EAAUZ,IAAI,EACvC,EAEA,MACE,UAACgD,EAAAA,CAAOA,CAAAA,CACNC,gBAAiB,uBACjBjD,KAAMA,EACNkD,OAAQ9B,EACRJ,KAAMO,EAAU,UAACxC,EAAAA,CAAeA,CAAAA,CAAAA,GAAM,UAAC4B,EAAAA,CAAYC,UAAWA,KAGpE,iGCzHA,SAASuC,EAAUpD,CAAqB,EACtC,GAAM,MAAEC,CAAI,iBAAEiD,CAAe,CAAE,CAAGlD,EAClC,MACE,WAACqD,EAAAA,CAAKA,CAAAA,CACH,GAAGrD,CAAK,CACTkD,gBAAiBA,EACjBI,kBAAgB,gCAChBC,QAAQ,cAER,UAACF,EAAAA,CAAKA,CAACG,MAAM,EAACC,WAAW,aACvB,UAACJ,EAAAA,CAAKA,CAACK,KAAK,EAAC5C,GAAG,yCACbb,EAAK+C,OAAO,KAGjB,UAACK,EAAAA,CAAKA,CAACM,IAAI,WACR1D,EAAKgB,IAAI,KAIlB,CAUA,SAAS2C,EAAW5D,CAAsB,EACxC,GAAM,MAAEC,CAAI,CAAE,CAAGD,EACX,CAAC6D,EAAWC,EAAa,CAAGC,EAAAA,QAAc,EAAC,UACjD,GAAY9D,EAAKgB,IAAI,CAEjB,iCACE,UAAC+C,SAAAA,CAAOC,KAAK,SAASC,QAAS,IAAMJ,GAAa,GAAOK,MAAO,CAAEC,OAAQ,OAAQC,WAAY,OAAQC,QAAS,CAAE,WAC/G,UAACC,EAAAA,CAAIA,CAACC,MAAM,WACV,UAACC,IAAAA,CAAE1D,UAAU,4BAGhBf,EAAMC,IAAI,EAAI,UAACmD,EAAAA,CAAUnD,KAAMD,EAAMC,IAAI,CAAEyE,KAAMb,EAAWc,OAAQ,IAAMb,GAAa,GAAQZ,gBAAiBlD,EAAMkD,eAAe,MAIrI,IACT,CA4BA,MAhBA,SAASD,CAA2B,EAClC,GAAM,QAAEE,CAAM,IAeDF,EAfGhC,CAAI,CAAE,CAAGjB,EAEzB,EAaqB,IAZnB,WAACuE,EAAAA,CAAIA,CAAAA,CAACxD,UAAU,iCACd,UAACwD,EAAAA,CAAIA,CAACf,MAAM,WAAEL,IACd,UAACoB,EAAAA,CAAIA,CAACZ,IAAI,WACR,UAACY,EAAAA,CAAIA,CAACK,IAAI,WACP3D,MAGL,UAAC2C,EAAAA,CAAY,GAAG5D,CAAK,KAG3B", "sources": ["webpack://_N_E/?ef95", "webpack://_N_E/./components/common/placeholders/CardPlaceholder.tsx", "webpack://_N_E/./pages/dashboard/OngoingOperations.tsx", "webpack://_N_E/./components/common/RKICard.tsx"], "sourcesContent": ["\n    (window.__NEXT_P = window.__NEXT_P || []).push([\n      \"/dashboard/OngoingOperations\",\n      function () {\n        return require(\"private-next-pages/dashboard/OngoingOperations.tsx\");\n      }\n    ]);\n    if(module.hot) {\n      module.hot.dispose(function () {\n        window.__NEXT_P.push([\"/dashboard/OngoingOperations\"])\n      });\n    }\n  ", "//Import Library\r\nimport ContentLoader from 'react-content-loader';\r\n\r\n// No props needed - component uses hardcoded values\r\nexport default function CardPlaceholder() {\r\n  return(\r\n    <ContentLoader\r\n      viewBox=\"0 0 380 70\"\r\n      height={50}\r\n      width={317}\r\n      speed={2}\r\n      title={'Loading'}\r\n      foregroundColor=\"#f7f7f7\"\r\n      backgroundColor=\"#ecebeb\"\r\n      uniqueKey={\"operation\"}\r\n    >\r\n      <rect x=\"10\" y=\"0\" rx=\"4\" ry=\"4\" width=\"320\" height=\"25\" />\r\n      <rect x=\"40\" y=\"40\" rx=\"3\" ry=\"3\" width=\"250\" height=\"20\" />\r\n    </ContentLoader>\r\n  )\r\n}\r\n", "//Import Library\r\nimport React, { useEffect, useState } from 'react';\r\nimport _ from \"lodash\";\r\nimport Link from \"next/link\";\r\nimport ListGroup from \"react-bootstrap/ListGroup\";\r\n\r\n//Import services/components\r\nimport RKICard from \"../../components/common/RKICard\";\r\nimport apiService from '../../services/apiService';\r\nimport CardPlaceholder from \"../../components/common/placeholders/CardPlaceholder\";\r\n\r\n\r\ninterface ListItemsProps {\r\n  list: any[];\r\n}\r\n\r\nfunction ListItems(props: ListItemsProps) {\r\n  const { list } = props;\r\n  if (list.length > 0) {\r\n    return (\r\n      <ListGroup>\r\n        {list.map((item: any, index: number) => {\r\n          return (\r\n            <ListGroup.Item\r\n              key={index}>\r\n              <Link href=\"/operation/[...routes]\" as={`/operation/show/${item._id}`}>\r\n\r\n                {item.title}\r\n\r\n              </Link>\r\n            </ListGroup.Item>\r\n          );\r\n        })}\r\n      </ListGroup>\r\n    );\r\n  }\r\n  return null;\r\n}\r\n\r\ninterface CardDetailsProps {\r\n  operation: {\r\n    body: string;\r\n    id: string;\r\n  };\r\n}\r\n\r\nfunction CardDetails(props: CardDetailsProps) {\r\n  const { operation } = props;\r\n  return (\r\n    <Link\r\n      href='/operation/[...routes]'\r\n      as={`/operation/show/${operation.id}`}\r\n      className='active-op-project'>\r\n\r\n      <span className=\"project-title link\">{operation.body}</span>\r\n\r\n    </Link>\r\n  );\r\n}\r\n\r\ninterface OngoingOperationsProps {\r\n  t: (key: string) => string;\r\n  fetchOngoingOperations: (operations: any[]) => void;\r\n}\r\n\r\nfunction OngoingOperations(props: OngoingOperationsProps) {\r\n  const {t, fetchOngoingOperations} = props;\r\n  const cardHeader = t(\"OngoingOperations\");\r\n  const [operation, setOperation] = useState<{ body: string; id: string; list: any[] }>({ body: \"\", id: \"\", list: [] });\r\n  const [loading, setLoading] = useState(true);\r\n\r\n  const setEmptyNotice = () => {\r\n    setOperation({ body: t(\"Nooperationavailable\"), id: \"\", list: [] })\r\n  };\r\n\r\n  const fetchOperation = async () => {\r\n    const operationParams = {\r\n      query: { status: [] },\r\n      sort: { created_at: \"desc\" },\r\n      limit: 10,\r\n      select: \"-description -status -start_date -end_date -timeline -world_region -region -hazard_type -hazard -syndrome -partners -vspace -vspace_visibility -images -user -created_at -updated_at\"\r\n    };\r\n    const statusId: any = await fetchOperationStatus();\r\n\r\n    if (statusId) {\r\n      operationParams.query.status = statusId;\r\n      try {\r\n        setLoading(true);\r\n        const operations = await apiService.get('/operation', operationParams);\r\n        setLoading(false);\r\n        if (Array.isArray(operations.data) && operations.data.length > 0) {\r\n          setOperation({ body: operations.data[0].title, id: operations.data[0]._id, list: operations.data })\r\n          fetchOngoingOperations(operations.data);\r\n        } else {\r\n          setEmptyNotice()\r\n        }\r\n      } catch (e) {\r\n        setEmptyNotice()\r\n      }\r\n    } else {\r\n      setEmptyNotice()\r\n    }\r\n\r\n  };\r\n\r\n  const fetchOperationStatus = async () => {\r\n    const response = await apiService.get('/operation_status');\r\n    if (response && response.data && response.data.length > 0) {\r\n      const statusId: any[] = []\r\n      _.forEach(response.data, function (item: any) {\r\n        if (item.title == 'Ongoing') {\r\n          statusId.push(item._id);\r\n        }\r\n      });\r\n      return statusId;\r\n    }\r\n    return false;\r\n  };\r\n\r\n  useEffect(() => {\r\n    fetchOperation();\r\n  }, []);\r\n\r\n  const list = {\r\n    heading: cardHeader,\r\n    body: <ListItems list={operation.list} />\r\n  };\r\n\r\n  return (\r\n    <RKICard\r\n      dialogClassName={\"ongoing-project-list\"}\r\n      list={list}\r\n      header={cardHeader}\r\n      body={loading ? <CardPlaceholder /> : <CardDetails operation={operation} />}\r\n    />\r\n  )\r\n}\r\n\r\nexport default OngoingOperations;", "//Import Library\r\nimport React from \"react\";\r\nimport { Card } from \"react-bootstrap\";\r\nimport Modal from \"react-bootstrap/Modal\";\r\n\r\ninterface ListModalProps {\r\n  list: {\r\n    heading: string;\r\n    body: React.ReactNode;\r\n  };\r\n  dialogClassName?: string;\r\n  show: boolean;\r\n  onHide: () => void;\r\n}\r\n\r\nfunction ListModal(props: ListModalProps) {\r\n  const { list, dialogClassName } = props;\r\n  return (\r\n    <Modal\r\n      {...props}\r\n      dialogClassName={dialogClassName}\r\n      aria-labelledby=\"contained-modal-title-vcenter\"\r\n      centered\r\n    >\r\n      <Modal.Header closeButton>\r\n        <Modal.Title id=\"contained-modal-title-vcenter\">\r\n          {list.heading}\r\n        </Modal.Title>\r\n      </Modal.Header>\r\n      <Modal.Body>\r\n        {list.body}\r\n      </Modal.Body>\r\n    </Modal>\r\n  )\r\n}\r\n\r\ninterface CardFooterProps {\r\n  list?: {\r\n    body: React.ReactNode;\r\n    heading: string;\r\n  };\r\n  dialogClassName?: string;\r\n}\r\n\r\nfunction CardFooter(props: CardFooterProps) {\r\n  const { list } = props;\r\n  const [modalShow, setModalShow] = React.useState(false);\r\n  if (list && list.body) {\r\n    return (\r\n      <>\r\n        <button type=\"button\" onClick={() => setModalShow(true)} style={{ border: 'none', background: 'none', padding: 0 }}>\r\n          <Card.Footer>\r\n            <i className=\"fas fa-chevron-down\" />\r\n          </Card.Footer>\r\n        </button>\r\n        {props.list && <ListModal list={props.list} show={modalShow} onHide={() => setModalShow(false)} dialogClassName={props.dialogClassName} />}\r\n      </>\r\n    )\r\n  }\r\n  return null;\r\n}\r\n\r\ninterface RKICardProps {\r\n  header: string;\r\n  body: React.ReactNode;\r\n  list?: {\r\n    body: React.ReactNode;\r\n    heading: string;\r\n  };\r\n  dialogClassName?: string;\r\n}\r\n\r\nfunction RKICard(props: RKICardProps) {\r\n  const { header, body } = props\r\n\r\n  return (\r\n    <Card className=\"text-center infoCard\">\r\n      <Card.Header>{header}</Card.Header>\r\n      <Card.Body>\r\n        <Card.Text>\r\n          {body}\r\n        </Card.Text>\r\n      </Card.Body>\r\n      <CardFooter {...props} />\r\n    </Card>\r\n  )\r\n}\r\n\r\nexport default RKICard;\r\n"], "names": ["CardPlaceholder", "ContentLoader", "viewBox", "height", "width", "speed", "title", "foregroundColor", "backgroundColor", "<PERSON><PERSON><PERSON>", "rect", "x", "y", "rx", "ry", "ListItems", "props", "list", "length", "ListGroup", "map", "item", "index", "<PERSON><PERSON>", "Link", "href", "as", "_id", "CardDetails", "operation", "id", "className", "span", "body", "OngoingOperations", "t", "fetchOngoingOperations", "<PERSON><PERSON><PERSON><PERSON>", "setOperation", "useState", "loading", "setLoading", "setEmptyNotice", "fetchOperation", "operationParams", "query", "status", "sort", "created_at", "limit", "select", "statusId", "fetchOperationStatus", "operations", "apiService", "get", "Array", "isArray", "data", "e", "response", "_", "push", "useEffect", "heading", "RKICard", "dialogClassName", "header", "ListModal", "Modal", "aria-<PERSON>by", "centered", "Header", "closeButton", "Title", "Body", "<PERSON><PERSON><PERSON>er", "modalShow", "setModalShow", "React", "button", "type", "onClick", "style", "border", "background", "padding", "Card", "Footer", "i", "show", "onHide", "Text"], "sourceRoot": "", "ignoreList": []}