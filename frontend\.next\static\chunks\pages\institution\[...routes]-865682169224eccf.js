(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[8237],{31732:(e,t,n)=>{"use strict";n.r(t),n.d(t,{__N_SSP:()=>d,default:()=>f});var r=n(37876),o=n(14232),l=n(48012),a=n(90627),s=n(12780),u=n(53718),c=n(33859),i=n(55216),d=!0;let f=e=>{let{router:t}=e,n=t.query.routes||[],[d,f]=(0,o.useState)(""),[m,p]=(0,o.useState)(!1),[y,b]=(0,o.useState)(""),v=async()=>{let e=await u.A.get("/institution/".concat(n[1]));f(e),C(e)};(0,o.useEffect)(()=>{v()},[]);let C=async e=>{let t=await u.A.post("/users/getLoggedUser",{});t&&t.roles&&t.roles.length&&(b(t),p(!1),t&&t.roles&&(t.roles.includes("SUPER_ADMIN")||t.roles.includes("PLATFORM_ADMIN")&&e.user==t._id?p(!0):t.roles.includes("GENERAL_USER")&&e.user==t._id&&p(!0)))},h=(0,c.canAddInstitutionForm)(()=>(0,r.jsx)(l.default,{institution:"",routes:n})),E=(0,c.canEditInstitutionForm)(()=>(0,r.jsx)(l.default,{institution:d,routes:n})),g=(0,c.canManageFocalPoints)(()=>(0,r.jsx)(s.default,{institution:d,routes:n}));switch(n[0]){case"create":return(0,r.jsx)(h,{});case"edit":if(m)return(0,r.jsx)(E,{institution:d,routes:n});if(""!=y)return(0,r.jsx)(i.default,{});case"show":return(0,r.jsx)(a.default,{routes:n});case"focalpoint":if(null!==d)return(0,r.jsx)(g,{institution:d});break;default:return null}}},47104:(e,t,n)=>{(window.__NEXT_P=window.__NEXT_P||[]).push(["/institution/[...routes]",function(){return n(31732)}])},55216:(e,t,n)=>{"use strict";n.r(t),n.d(t,{default:()=>o});var r=n(37876);function o(e){return(0,r.jsx)("div",{className:"container-fluid p-0 response-message-block",children:(0,r.jsx)("div",{className:"message",children:"you don't have permission to access"})})}},87902:(e,t,n)=>{"use strict";var r,o,l=function(){function e(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,r.key,r)}}return function(t,n,r){return n&&e(t.prototype,n),r&&e(t,r),t}}();t.ZX=function(e){if(document.body.classList.add("react-confirm-alert-body-element"),!document.getElementById("react-confirm-alert-firm-svg")){var t,n="http://www.w3.org/2000/svg",r=document.createElementNS(n,"feGaussianBlur");r.setAttribute("stdDeviation","0.3");var o=document.createElementNS(n,"filter");o.setAttribute("id","gaussian-blur"),o.appendChild(r);var l=document.createElementNS(n,"svg");l.setAttribute("id","react-confirm-alert-firm-svg"),l.setAttribute("class","react-confirm-alert-svg"),l.appendChild(o),document.body.appendChild(l)}(t=document.getElementById("react-confirm-alert"))||(document.body.children[0].classList.add("react-confirm-alert-blur"),(t=document.createElement("div")).id="react-confirm-alert",document.body.appendChild(t)),(0,c.render)(s.default.createElement(f,e),t)};var a=n(14232),s=i(a),u=i(n(95062)),c=n(98477);function i(e){return e&&e.__esModule?e:{default:e}}function d(e,t){if(!e)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return t&&("object"==typeof t||"function"==typeof t)?t:e}var f=(o=r=function(e){if("function"!=typeof e&&null!==e)throw TypeError("Super expression must either be null or a function, not "+typeof e);function t(){if(!(this instanceof t))throw TypeError("Cannot call a class as a function");for(var e,n,r,o=arguments.length,l=Array(o),a=0;a<o;a++)l[a]=arguments[a];return n=r=d(this,(e=t.__proto__||Object.getPrototypeOf(t)).call.apply(e,[this].concat(l))),r.handleClickButton=function(e){e.onClick&&e.onClick(),r.close()},r.handleClickOverlay=function(e){var t=r.props,n=t.closeOnClickOutside,o=t.onClickOutside,l=e.target===r.overlay;n&&l&&(o(),r.close())},r.close=function(){var e,t,n,o=r.props.afterClose;document.body.classList.remove("react-confirm-alert-body-element"),(e=document.getElementById("react-confirm-alert"))&&((0,c.unmountComponentAtNode)(e),e.parentNode.removeChild(e)),t=o,(n=document.getElementById("react-confirm-alert-firm-svg"))&&n.parentNode.removeChild(n),document.body.children[0].classList.remove("react-confirm-alert-blur"),t()},r.keyboardClose=function(e){var t=r.props,n=t.closeOnEscape,o=t.onKeypressEscape,l=t.keyCodeForClose,a=e.keyCode,s=27===a;l.includes(a)&&r.close(),n&&s&&(o(e),r.close())},r.componentDidMount=function(){document.addEventListener("keydown",r.keyboardClose,!1)},r.componentWillUnmount=function(){document.removeEventListener("keydown",r.keyboardClose,!1),r.props.willUnmount()},r.renderCustomUI=function(){var e=r.props,t=e.title,n=e.message,o=e.buttons;return(0,e.customUI)({title:t,message:n,buttons:o,onClose:r.close})},d(r,n)}return t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,enumerable:!1,writable:!0,configurable:!0}}),e&&(Object.setPrototypeOf?Object.setPrototypeOf(t,e):t.__proto__=e),l(t,[{key:"render",value:function(){var e=this,t=this.props,n=t.title,r=t.message,o=t.buttons,l=t.childrenElement,a=t.customUI,u=t.overlayClassName;return s.default.createElement("div",{className:"react-confirm-alert-overlay "+u,ref:function(t){return e.overlay=t},onClick:this.handleClickOverlay},s.default.createElement("div",{className:"react-confirm-alert"},a?this.renderCustomUI():s.default.createElement("div",{className:"react-confirm-alert-body"},n&&s.default.createElement("h1",null,n),r,l(),s.default.createElement("div",{className:"react-confirm-alert-button-group"},o.map(function(t,n){return s.default.createElement("button",{key:n,onClick:function(){return e.handleClickButton(t)},className:t.className},t.label)})))))}}]),t}(a.Component),r.propTypes={title:u.default.string,message:u.default.string,buttons:u.default.array.isRequired,childrenElement:u.default.func,customUI:u.default.func,closeOnClickOutside:u.default.bool,closeOnEscape:u.default.bool,keyCodeForClose:u.default.arrayOf(u.default.number),willUnmount:u.default.func,afterClose:u.default.func,onClickOutside:u.default.func,onKeypressEscape:u.default.func,overlayClassName:u.default.string},r.defaultProps={buttons:[{label:"Cancel",onClick:function(){return null},className:null},{label:"Confirm",onClick:function(){return null},className:null}],childrenElement:function(){return null},closeOnClickOutside:!0,closeOnEscape:!0,keyCodeForClose:[],willUnmount:function(){return null},afterClose:function(){return null},onClickOutside:function(){return null},onKeypressEscape:function(){return null}},o)}},e=>{var t=t=>e(e.s=t);e.O(0,[7725,9773,1772,7126,7336,5939,5266,8477,7308,9810,8012,5293,2780,636,6593,8792],()=>t(47104)),_N_E=e.O()}]);
//# sourceMappingURL=[...routes]-865682169224eccf.js.map