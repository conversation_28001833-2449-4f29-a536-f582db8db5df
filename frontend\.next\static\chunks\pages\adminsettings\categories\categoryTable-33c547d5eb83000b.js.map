{"version": 3, "file": "static/chunks/pages/adminsettings/categories/categoryTable-33c547d5eb83000b.js", "mappings": "gFACA,4CACA,0CACA,WACA,OAAe,EAAQ,KAA+D,CACtF,EACA,SAFsB,0JC8GtB,MAvGsB,IACpB,GAAM,CAACA,EAAWC,EAAe,CAAGC,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAC,EAAE,EACzC,EAAGC,EAAW,CAAGD,CAAAA,EAAAA,CAqGGE,CArGHF,CAqGI,OArGJA,CAAQA,EAAC,GAC1B,CAACG,EAAWC,EAAa,CAAGJ,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAC,GACrC,CAACK,EAASC,EAAW,CAAGN,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAC,IACjC,CAACO,EAAaC,EAAS,CAAGR,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,EAAC,GACnC,CAACS,EAAgBC,EAAkB,CAAGV,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAC,CAAC,GAChD,GAAEW,CAAC,CAAE,CAAGC,CAAAA,EAAAA,EAAAA,EAAAA,CAAcA,CAAC,UAGvBC,EAAiB,CACrB,KAAQ,CAAE,MAAS,KAAM,EACzB,MAASR,EACT,KAAQ,EACR,MAAS,CAAC,CACZ,EAEMS,EAAU,CACd,CACEC,KAAM,QACNC,SAAU,OACZ,EACA,CACED,KAAM,SACNC,SAAU,GACVC,KAAM,GAAY,WAACC,MAAAA,WAAI,UAACC,IAAIA,CAACC,KAAK,6BAA6BC,GAAI,OAAvCF,yBAA6E,OAANG,EAAEC,GAAG,WAAK,UAACC,IAAAA,CAAEC,UAAU,uBAA4B,OAAM,UAACN,IAAIA,CAACC,KAAK,IAAIM,QAAS,IAAMC,EAAWL,YAAI,SAAxCH,CAAyCK,IAAAA,CAAEC,UAAU,4BAAiC,MACzP,EACD,CAEKG,EAAkB,UACtB3B,GAAW,GACX,IAAM4B,EAAW,MAAMC,EAAAA,CAAUA,CAACC,GAAG,CAAC,YAAalB,GAC/CgB,GAAYA,EAASG,IAAI,EAAIH,EAASG,IAAI,CAACC,MAAM,CAAG,GAAG,CACzDlC,EAAe8B,EAASG,IAAI,EAC5B5B,EAAayB,EAASK,UAAU,EAChCjC,GAAW,GAEf,EAQMkC,EAAsB,MAAOC,EAAiBC,KAClDxB,EAAeyB,KAAK,CAAGF,EACvBvB,EAAewB,IAAI,CAAGA,EACtBpC,GAAW,GACX,IAAM4B,EAAW,MAAMC,EAAAA,CAAUA,CAACC,GAAG,CAAC,YAAalB,GAC/CgB,GAAYA,EAASG,IAAI,EAAIH,EAASG,IAAI,CAACC,MAAM,CAAG,GAAG,CACzDlC,EAAe8B,EAASG,IAAI,EAC5B1B,EAAW8B,GACXnC,GAAW,GAEf,EAEM0B,EAAa,MAAOY,IACxB7B,EAAkB6B,EAAIhB,GAAG,EACzBf,GAAS,EACX,EAEMgC,EAAe,UACnB,MAAMV,EAAAA,CAAUA,CAACW,MAAM,CAAC,aAA4B,OAAfhC,IACrCmB,IACApB,GAAS,EACX,EAEMkC,EAAY,IAAMlC,GAAS,GAMjC,MAJAmC,CAAAA,EAAAA,EAAAA,SAAAA,CAASA,CAAC,KACRf,GACF,EAAG,EAAE,EAGH,WAACV,MAAAA,WACC,WAAC0B,EAAAA,CAAKA,CAAAA,CAACC,KAAMtC,EAAauC,OAAQJ,YAChC,UAACE,EAAAA,CAAKA,CAACG,MAAM,EAACC,WAAW,aACvB,UAACJ,EAAAA,CAAKA,CAACK,KAAK,WAAEtC,EAAE,sBAElB,WAACiC,EAAAA,CAAKA,CAACM,IAAI,YAAEvC,EAAE,sCAAsC,OACrD,WAACiC,EAAAA,CAAKA,CAACO,MAAM,YACX,UAACC,EAAAA,CAAMA,CAAAA,CAACC,QAAQ,YAAY3B,QAASgB,WACpC/B,EAAE,YAEH,UAACyC,EAAAA,CAAMA,CAAAA,CAACC,QAAQ,UAAU3B,QAASc,WAClC7B,EAAE,eAKP,UAAC2C,EAAAA,CAAQA,CAAAA,CACPxC,QAASA,EACTkB,KAAMlC,EACNK,UAAWA,EACXoD,WAAW,EACXpB,oBAAqBA,EACrBqB,iBA1DmB,CA0DDA,GAzDtB3C,EAAeyB,KAAK,CAAGjC,EACvBQ,EAAewB,IAAI,CAAGA,EACtBT,GACF,MA0DF,6GC3EA,SAAS0B,EAASG,CAAoB,EACpC,GAAM,GAAE9C,CAAC,CAAE,CAAGC,CAAAA,EAAAA,EAAAA,EAAAA,CAAcA,CAAC,UACvB8C,EAA6B,CACjCC,gBAAiBhD,EAAE,cACnB,EACI,SACJG,CAAO,MACPkB,CAAI,WACJ7B,CAAS,uBACTyD,CAAqB,WACrBC,CAAS,oBACTC,CAAkB,CAClB3B,qBAAmB,kBACnBqB,CAAgB,aAChBO,CAAW,oBACXC,CAAkB,gBAClBC,CAAc,SACdC,CAAO,WACPX,CAAS,CACTY,sBAAoB,mBACpBC,CAAiB,YACjBC,CAAU,QACVC,CAAM,kBACNC,CAAgB,cAChBC,CAAY,CAEZ,CADA,EACGC,EACJ,CAAGhB,EAGEiB,EAAiB,CACrBhB,6BACAiB,gBAAiBhE,EAAE,IAP0C,MAQ7DiE,UAAU,UACV9D,EACAkB,KAAMA,GAAQ,EAAE,CAChB6C,OAAO,EACPC,2BAA4BlB,EAC5BmB,UAAWlB,EACXmB,gBAAiBd,EACjBJ,qBACAmB,YAAY,EACZC,iBAAkB3B,EAClB4B,kBAAmBnB,GAA0C,GAC7DoB,eADwCpB,cACVD,GAA4B,CAAC,GAAI,GAAI,GAAI,CAA3BA,EAA+B,GAAG,CAC9EsB,oBAAqBlF,EACrBmF,oBAAqBnD,EACrBoD,aAAc/B,EACdS,sCACAE,oBACAC,EACAoB,kBAAmB,UAACC,EAAAA,CAAYA,CAAAA,CAAAA,GAChCC,SAAU,UAAClE,IAAAA,CAAEC,UAAU,6CACvB4C,SACAC,eACAE,mBACAD,EACA9C,UAAW,WACb,EACA,MACE,UAACkE,EAAAA,EAASA,CAAAA,CAAE,GAAGjB,CAAc,EAEjC,CAEApB,EAASsC,YAAY,CAAG,CACtBb,UAAW,GACXE,YAAY,EACZ9E,UAAW,KACXoD,WAAW,EACXY,qBAAsB,KACtBC,mBAAmB,EACnBC,YAAY,EACZE,iBAAkB,EACpB,EAEA,MAAejB,QAAQA,EAAC", "sources": ["webpack://_N_E/?1cf9", "webpack://_N_E/./pages/adminsettings/categories/categoryTable.tsx", "webpack://_N_E/./components/common/RKITable.tsx"], "sourcesContent": ["\n    (window.__NEXT_P = window.__NEXT_P || []).push([\n      \"/adminsettings/categories/categoryTable\",\n      function () {\n        return require(\"private-next-pages/adminsettings/categories/categoryTable.tsx\");\n      }\n    ]);\n    if(module.hot) {\n      module.hot.dispose(function () {\n        window.__NEXT_P.push([\"/adminsettings/categories/categoryTable\"])\n      });\n    }\n  ", "//Import Library\r\nimport { useState, useEffect } from \"react\";\r\nimport { <PERSON><PERSON>, <PERSON><PERSON> } from \"react-bootstrap\";\r\nimport Link from \"next/link\";\r\n\r\n//Import services/components\r\nimport RKITable from \"../../../components/common/RKITable\";\r\nimport apiService from \"../../../services/apiService\";\r\nimport { useTranslation } from 'next-i18next';\r\n\r\n\r\nconst CategoryTable = (_props: any) => {\r\n  const [tabledata, setDataToTable] = useState([]);\r\n  const [ ,setLoading] = useState(false);\r\n  const [totalRows, setTotalRows] = useState(0);\r\n  const [perPage, setPerPage] = useState(10);\r\n  const [isModalShow, setModal] = useState(false);\r\n  const [selectCategory, setSelectCategory] = useState({});\r\n  const { t } = useTranslation('common');\r\n\r\n\r\n  const categoryParams = {\r\n    \"sort\": { \"title\": \"asc\" },\r\n    \"limit\": perPage,\r\n    \"page\": 1,\r\n    \"query\": {}\r\n  };\r\n\r\n  const columns = [\r\n    {\r\n      name: 'Title',\r\n      selector: 'title',\r\n    },\r\n    {\r\n      name: 'Action',\r\n      selector: \"\",\r\n      cell: (d: any) => <div><Link href=\"/adminsettings/[...routes]\" as={`/adminsettings/edit_category/${d._id}`} ><i className=\"icon fas fa-edit\" /></Link>&nbsp;<Link href=\"#\" onClick={() => userAction(d)}><i className=\"icon fas fa-trash-alt\" /></Link> </div>\r\n    }\r\n  ];\r\n\r\n  const getCategoryData = async () => {\r\n    setLoading(true);\r\n    const response = await apiService.get('/category', categoryParams);\r\n    if (response && response.data && response.data.length > 0) {\r\n      setDataToTable(response.data);\r\n      setTotalRows(response.totalCount);\r\n      setLoading(false);\r\n    }\r\n  };\r\n\r\n  const handlePageChange = (page: any) => {\r\n    categoryParams.limit = perPage;\r\n    categoryParams.page = page;\r\n    getCategoryData();\r\n  };\r\n\r\n  const handlePerRowsChange = async (newPerPage: any, page: any) => {\r\n    categoryParams.limit = newPerPage;\r\n    categoryParams.page = page;\r\n    setLoading(true);\r\n    const response = await apiService.get('/category', categoryParams);\r\n    if (response && response.data && response.data.length > 0) {\r\n      setDataToTable(response.data);\r\n      setPerPage(newPerPage);\r\n      setLoading(false);\r\n    }\r\n  };\r\n\r\n  const userAction = async (row: any) => {\r\n    setSelectCategory(row._id);\r\n    setModal(true);\r\n  }\r\n\r\n  const modalConfirm = async () => {\r\n    await apiService.remove(`/category/${selectCategory}`);\r\n    getCategoryData();\r\n    setModal(false);\r\n  }\r\n\r\n  const modalHide = () => setModal(false);\r\n\r\n  useEffect(() => {\r\n    getCategoryData();\r\n  }, []);\r\n\r\n  return (\r\n    <div>\r\n      <Modal show={isModalShow} onHide={modalHide}>\r\n        <Modal.Header closeButton>\r\n          <Modal.Title>{t(\"DeleteCategory\")}</Modal.Title>\r\n        </Modal.Header>\r\n        <Modal.Body>{t(\"Areyousurewanttodeletethiscategory\")} </Modal.Body>\r\n        <Modal.Footer>\r\n          <Button variant=\"secondary\" onClick={modalHide}>\r\n          {t(\"cancel\")}\r\n        </Button>\r\n          <Button variant=\"primary\" onClick={modalConfirm}>\r\n          {t(\"yes\")}\r\n        </Button>\r\n        </Modal.Footer>\r\n      </Modal>\r\n\r\n      <RKITable\r\n        columns={columns}\r\n        data={tabledata}\r\n        totalRows={totalRows}\r\n        pagServer={true}\r\n        handlePerRowsChange={handlePerRowsChange}\r\n        handlePageChange={handlePageChange}\r\n      />\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default CategoryTable;", "//Import Library\r\nimport React from 'react'\r\nimport DataTable  from 'react-data-table-component';\r\n\r\n//Import services/components\r\nimport { useTranslation } from 'next-i18next';\r\nimport CustomLoader from './CustomLoader';\r\n\r\ninterface RKITableProps {\r\n  columns: any[];\r\n  data: any[];\r\n  totalRows?: number;\r\n  resetPaginationToggle?: boolean;\r\n  subheader?: boolean;\r\n  subHeaderComponent?: React.ReactNode;\r\n  handlePerRowsChange?: (newPerPage: number, page: number) => void;\r\n  handlePageChange?: (page: number) => void;\r\n  rowsPerPage?: number[];\r\n  defaultRowsPerPage?: number;\r\n  selectableRows?: boolean;\r\n  loading?: boolean;\r\n  pagServer?: boolean;\r\n  onSelectedRowsChange?: (selectedRows: any) => void;\r\n  clearSelectedRows?: boolean;\r\n  sortServer?: boolean;\r\n  onSort?: (column: any, sortDirection: string) => void;\r\n  persistTableHead?: boolean;\r\n  sortFunction?: (rows: any[], field: any, direction: 'asc' | 'desc') => any[];\r\n  noHeader?: boolean;\r\n  dense?: boolean;\r\n  pagination?: boolean;\r\n  paginationServer?: boolean;\r\n  paginationTotalRows?: number;\r\n  subHeaderAlign?: string;\r\n  [key: string]: any;\r\n}\r\n\r\nfunction RKITable(props: RKITableProps) {\r\n  const { t } = useTranslation('common');\r\n  const paginationComponentOptions = {\r\n    rowsPerPageText: t('Rowsperpage'),\r\n    };\r\n  const {\r\n    columns,\r\n    data,\r\n    totalRows,\r\n    resetPaginationToggle,\r\n    subheader,\r\n    subHeaderComponent,\r\n    handlePerRowsChange,\r\n    handlePageChange,\r\n    rowsPerPage,\r\n    defaultRowsPerPage,\r\n    selectableRows,\r\n    loading,\r\n    pagServer,\r\n    onSelectedRowsChange,\r\n    clearSelectedRows,\r\n    sortServer,\r\n    onSort,\r\n    persistTableHead,\r\n    sortFunction,\r\n    // Filter out any unknown props that might cause DOM warnings\r\n    ...otherProps\r\n  } = props;\r\n\r\n  // Only pass known DataTable props to avoid DOM warnings\r\n  const dataTableProps = {\r\n    paginationComponentOptions,\r\n    noDataComponent: t(\"NoData\"),\r\n    noHeader: true,\r\n    columns,\r\n    data: data || [],\r\n    dense: true,\r\n    paginationResetDefaultPage: resetPaginationToggle,\r\n    subHeader: subheader,\r\n    progressPending: loading,\r\n    subHeaderComponent,\r\n    pagination: true,\r\n    paginationServer: pagServer,\r\n    paginationPerPage: defaultRowsPerPage ? defaultRowsPerPage : 10,\r\n    paginationRowsPerPageOptions: rowsPerPage ? rowsPerPage : [10, 15, 20, 25, 30],\r\n    paginationTotalRows: totalRows,\r\n    onChangeRowsPerPage: handlePerRowsChange,\r\n    onChangePage: handlePageChange,\r\n    selectableRows,\r\n    onSelectedRowsChange,\r\n    clearSelectedRows,\r\n    progressComponent: <CustomLoader/>,\r\n    sortIcon: <i className=\"sort-icon fas fa-exchange-alt\" />,\r\n    sortServer,\r\n    onSort,\r\n    sortFunction,\r\n    persistTableHead,\r\n    className: \"rki-table\"\r\n  };\r\n  return (\r\n    <DataTable {...dataTableProps} />\r\n  )\r\n};\r\n\r\nRKITable.defaultProps = {\r\n  subHeader: false,\r\n  pagination: true,\r\n  totalRows: null,\r\n  pagServer: true,\r\n  onSelectedRowsChange: null,\r\n  clearSelectedRows: false,\r\n  sortServer: false,\r\n  persistTableHead: false\r\n}\r\n\r\nexport default RKITable;\r\n\r\n"], "names": ["tabledata", "setDataToTable", "useState", "setLoading", "CategoryTable", "totalRows", "setTotalRows", "perPage", "setPerPage", "isModalShow", "setModal", "selectCategory", "setSelectCategory", "t", "useTranslation", "categoryParams", "columns", "name", "selector", "cell", "div", "Link", "href", "as", "d", "_id", "i", "className", "onClick", "userAction", "getCategoryData", "response", "apiService", "get", "data", "length", "totalCount", "handlePerRowsChange", "newPerPage", "page", "limit", "row", "modalConfirm", "remove", "modalHide", "useEffect", "Modal", "show", "onHide", "Header", "closeButton", "Title", "Body", "Footer", "<PERSON><PERSON>", "variant", "RKITable", "pagServer", "handlePageChange", "props", "paginationComponentOptions", "rowsPerPageText", "resetPaginationToggle", "subheader", "subHeaderComponent", "rowsPerPage", "defaultRowsPerPage", "selectableRows", "loading", "onSelectedRowsChange", "clearSelectedRows", "sortServer", "onSort", "persistTableHead", "sortFunction", "otherProps", "dataTableProps", "noDataComponent", "<PERSON><PERSON><PERSON><PERSON>", "dense", "paginationResetDefaultPage", "subHeader", "progressPending", "pagination", "paginationServer", "paginationPerPage", "paginationRowsPerPageOptions", "paginationTotalRows", "onChangeRowsPerPage", "onChangePage", "progressComponent", "CustomLoader", "sortIcon", "DataTable", "defaultProps"], "sourceRoot": "", "ignoreList": []}