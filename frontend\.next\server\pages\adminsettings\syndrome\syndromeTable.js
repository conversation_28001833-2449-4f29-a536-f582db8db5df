"use strict";(()=>{var e={};e.id=6918,e.ids=[636,3220,6918],e.modules={123:e=>{e.exports=require("dom-helpers/removeEventListener")},1332:e=>{e.exports=require("react-custom-scrollbars-2")},1428:e=>{e.exports=import("axios")},1680:e=>{e.exports=require("@restart/ui/utils")},1919:e=>{e.exports=require("react-transition-group/Transition")},3892:e=>{e.exports=require("classnames")},4048:e=>{e.exports=require("react-truncate")},6009:e=>{e.exports=require("dom-helpers/ownerDocument")},6952:e=>{e.exports=require("@restart/ui/Modal")},7374:e=>{e.exports=require("@restart/ui/Dropdown")},8732:e=>{e.exports=require("react/jsx-runtime")},9653:e=>{e.exports=require("@restart/ui/Overlay")},11242:e=>{e.exports=require("redux-persist/integration/react")},11688:e=>{e.exports=require("@restart/hooks/useWillUnmount")},12388:e=>{e.exports=require("dom-helpers/addEventListener")},13364:e=>{e.exports=require("redux-saga/effects")},14062:e=>{e.exports=import("react-redux")},14078:e=>{e.exports=import("swr")},14332:e=>{e.exports=require("uncontrollable")},16116:e=>{e.exports=require("invariant")},18622:e=>{e.exports=require("yup")},19121:e=>{e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},22326:e=>{e.exports=require("react-dom")},22541:e=>{e.exports=require("redux-persist/lib/storage")},25303:e=>{e.exports=require("@restart/ui/NavItem")},26324:e=>{e.exports=require("warning")},27825:e=>{e.exports=require("lodash")},27910:e=>{e.exports=require("stream")},28217:e=>{e.exports=require("@restart/ui/DropdownItem")},29021:e=>{e.exports=require("fs")},29294:e=>{e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},29628:e=>{e.exports=require("dom-helpers/addClass")},29825:e=>{e.exports=require("prop-types")},29841:e=>{e.exports=require("dom-helpers/hasClass")},30362:e=>{e.exports=require("@restart/ui/DropdownToggle")},33873:e=>{e.exports=require("path")},36653:e=>{e.exports=require("nprogress")},36955:e=>{e.exports=require("@restart/hooks/useBreakpoint")},37766:e=>{e.exports=require("@restart/hooks/useMergedRefs")},38609:e=>{e.exports=require("react-data-table-component")},39756:e=>{e.exports=import("redux")},40051:e=>{e.exports=require("dom-helpers/removeClass")},40361:e=>{e.exports=require("next/dist/compiled/next-server/pages.runtime.prod.js")},42893:e=>{e.exports=import("react-hot-toast")},43294:e=>{e.exports=require("formik")},43455:(e,r,t)=>{t.a(e,async(e,s)=>{try{t.r(r),t.d(r,{config:()=>q,default:()=>c,getServerSideProps:()=>g,getStaticPaths:()=>m,getStaticProps:()=>x,reportWebVitals:()=>h,routeModule:()=>A,unstable_getServerProps:()=>f,unstable_getServerSideProps:()=>v,unstable_getStaticParams:()=>S,unstable_getStaticPaths:()=>y,unstable_getStaticProps:()=>P});var o=t(63885),a=t(80237),n=t(81413),i=t(9616),u=t.n(i),p=t(72386),d=t(79348),l=e([p,d]);[p,d]=l.then?(await l)():l;let c=(0,n.M)(d,"default"),x=(0,n.M)(d,"getStaticProps"),m=(0,n.M)(d,"getStaticPaths"),g=(0,n.M)(d,"getServerSideProps"),q=(0,n.M)(d,"config"),h=(0,n.M)(d,"reportWebVitals"),P=(0,n.M)(d,"unstable_getStaticProps"),y=(0,n.M)(d,"unstable_getStaticPaths"),S=(0,n.M)(d,"unstable_getStaticParams"),f=(0,n.M)(d,"unstable_getServerProps"),v=(0,n.M)(d,"unstable_getServerSideProps"),A=new o.PagesRouteModule({definition:{kind:a.A.PAGES,page:"/adminsettings/syndrome/syndromeTable",pathname:"/adminsettings/syndrome/syndromeTable",bundlePath:"",filename:""},components:{App:p.default,Document:u()},userland:d});s()}catch(e){s(e)}})},50009:e=>{e.exports=require("@restart/ui/DropdownMenu")},56084:(e,r,t)=>{t.d(r,{A:()=>p});var s=t(8732);t(82015);var o=t(38609),a=t.n(o),n=t(88751),i=t(30370);function u(e){let{t:r}=(0,n.useTranslation)("common"),t={rowsPerPageText:r("Rowsperpage")},{columns:o,data:u,totalRows:p,resetPaginationToggle:d,subheader:l,subHeaderComponent:c,handlePerRowsChange:x,handlePageChange:m,rowsPerPage:g,defaultRowsPerPage:q,selectableRows:h,loading:P,pagServer:y,onSelectedRowsChange:S,clearSelectedRows:f,sortServer:v,onSort:A,persistTableHead:b,sortFunction:w,...j}=e,M={paginationComponentOptions:t,noDataComponent:r("NoData"),noHeader:!0,columns:o,data:u||[],dense:!0,paginationResetDefaultPage:d,subHeader:l,progressPending:P,subHeaderComponent:c,pagination:!0,paginationServer:y,paginationPerPage:q||10,paginationRowsPerPageOptions:g||[10,15,20,25,30],paginationTotalRows:p,onChangeRowsPerPage:x,onChangePage:m,selectableRows:h,onSelectedRowsChange:S,clearSelectedRows:f,progressComponent:(0,s.jsx)(i.A,{}),sortIcon:(0,s.jsx)("i",{className:"sort-icon fas fa-exchange-alt"}),sortServer:v,onSort:A,sortFunction:w,persistTableHead:b,className:"rki-table"};return(0,s.jsx)(a(),{...M})}u.defaultProps={subHeader:!1,pagination:!0,totalRows:null,pagServer:!0,onSelectedRowsChange:null,clearSelectedRows:!1,sortServer:!1,persistTableHead:!1};let p=u},57664:e=>{e.exports=require("dom-helpers/scrollbarSize")},58928:e=>{e.exports=require("dom-helpers/canUseDOM")},59717:e=>{e.exports=require("@restart/ui/DropdownContext")},60560:e=>{e.exports=require("dom-helpers/contains")},63033:e=>{e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},65447:e=>{e.exports=require("@restart/ui/Button")},67364:e=>{e.exports=require("@restart/hooks/useCallbackRef")},68455:e=>{e.exports=import("redux-saga")},69722:e=>{e.exports=require("es6-promise")},74075:e=>{e.exports=require("zlib")},74716:e=>{e.exports=require("moment")},74987:e=>{e.exports=require("@restart/ui/ModalManager")},78097:e=>{e.exports=require("next-redux-saga")},78634:e=>{e.exports=require("@restart/ui/Anchor")},79348:(e,r,t)=>{t.a(e,async(e,s)=>{try{t.r(r),t.d(r,{default:()=>g});var o=t(8732),a=t(19918),n=t.n(a),i=t(82015),u=t(12403),p=t(91353),d=t(42893),l=t(56084),c=t(63487),x=t(88751),m=e([d,c]);[d,c]=m.then?(await m)():m;let g=e=>{let{t:r}=(0,x.useTranslation)("common"),[t,s]=(0,i.useState)([]),[,a]=(0,i.useState)(!1),[m,g]=(0,i.useState)(0),[q,h]=(0,i.useState)(10),[P,y]=(0,i.useState)(!1),[S,f]=(0,i.useState)({}),v=[{name:r("adminsetting.syndrome.Syndromes"),selector:"title"},{name:r("adminsetting.syndrome.Code"),selector:"code",cell:e=>e.code},{name:r("adminsetting.syndrome.Description"),selector:"description",cell:e=>e.description.replace(/<[^>]+>/g,"")},{name:r("adminsetting.syndrome.Action"),selector:"",cell:e=>(0,o.jsxs)("div",{children:[(0,o.jsx)(n(),{href:"/adminsettings/[...routes]",as:`/adminsettings/edit_syndrome/${e._id}`,children:(0,o.jsx)("i",{className:"icon fas fa-edit"})}),"\xa0",(0,o.jsx)("a",{onClick:()=>j(e),children:(0,o.jsx)("i",{className:"icon fas fa-trash-alt"})})," "]})}];(0,i.useEffect)(()=>{b()},[]);let A={sort:{title:"asc"},limit:q,page:1,query:{}},b=async()=>{a(!0);let e=await c.A.get("/syndrome",A);e&&e.data&&e.data.length>0&&(s(e.data),g(e.totalCount),a(!1))},w=async(e,r)=>{A.limit=e,A.page=r,a(!0);let t=await c.A.get("/syndrome",A);t&&t.data&&t.data.length>0&&(s(t.data),h(e),a(!1))},j=async e=>{f(e._id),y(!0)},M=async()=>{try{await c.A.remove(`/syndrome/${S}`),b(),y(!1),d.default.success(r("adminsetting.syndrome.Table.syndromeDeletedSuccessfully"))}catch(e){d.default.error(r("adminsetting.syndrome.Table.errorDeletingSyndrome"))}},C=()=>y(!1);return(0,o.jsxs)("div",{children:[(0,o.jsxs)(u.A,{show:P,onHide:C,children:[(0,o.jsx)(u.A.Header,{closeButton:!0,children:(0,o.jsx)(u.A.Title,{children:r("adminsetting.syndrome.Deletesyndrome")})}),(0,o.jsx)(u.A.Body,{children:r("adminsetting.syndrome.Areyousurewanttodeletethissyndrome?")}),(0,o.jsxs)(u.A.Footer,{children:[(0,o.jsx)(p.A,{variant:"secondary",onClick:C,children:r("adminsetting.syndrome.Cancel")}),(0,o.jsx)(p.A,{variant:"primary",onClick:M,children:r("adminsetting.syndrome.Yes")})]})]}),(0,o.jsx)(l.A,{columns:v,data:t,totalRows:m,pagServer:!0,handlePerRowsChange:w,handlePageChange:e=>{A.limit=q,A.page=e,b()}})]})};s()}catch(e){s(e)}})},80237:(e,r)=>{Object.defineProperty(r,"A",{enumerable:!0,get:function(){return t}});var t=function(e){return e.PAGES="PAGES",e.PAGES_API="PAGES_API",e.APP_PAGE="APP_PAGE",e.APP_ROUTE="APP_ROUTE",e.IMAGE="IMAGE",e}({})},81366:e=>{e.exports=require("redux-auth-wrapper/connectedAuthWrapper")},81413:(e,r)=>{Object.defineProperty(r,"M",{enumerable:!0,get:function(){return function e(r,t){return t in r?r[t]:"then"in r&&"function"==typeof r.then?r.then(r=>e(r,t)):"function"==typeof r&&"default"===t?r:void 0}}})},81521:e=>{e.exports=require("dom-helpers/querySelectorAll")},81895:e=>{e.exports=require("@restart/hooks/useEventCallback")},82015:e=>{e.exports=require("react")},86842:e=>{e.exports=require("@restart/ui/SelectableContext")},87571:e=>{e.exports=require("dom-helpers/transitionEnd")},88751:e=>{e.exports=require("next-i18next")},93787:e=>{e.exports=require("redux-persist")},94947:e=>{e.exports=require("@restart/hooks/useTimeout")},96196:e=>{e.exports=require("next-redux-wrapper")},98320:e=>{e.exports=require("@restart/hooks/useIsomorphicEffect")},99460:e=>{e.exports=require("dom-helpers/css")}};var r=require("../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[6089,9216,9616,2386],()=>t(43455));module.exports=s})();