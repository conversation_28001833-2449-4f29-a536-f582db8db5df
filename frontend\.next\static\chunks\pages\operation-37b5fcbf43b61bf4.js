(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[3451],{15214:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>u});var a=r(37876),s=r(14232),l=r(82851),n=r.n(l),o=r(66619),i=r(15641),c=r(31753);let u=e=>{let{i18n:t}=(0,c.Bd)("common"),r=t.language,{operations:l}=e,[u,d]=(0,s.useState)([]),[p,y]=(0,s.useState)({}),[m,h]=(0,s.useState)({}),[g,f]=(0,s.useState)({}),x=()=>{y(null),h(null)},T=(e,t,r)=>{x(),y(t),h({name:e.name,id:e.id,countryId:e.countryId})},A=()=>{let e=[];n().forEach(l,t=>{e.push({title:t.title,id:t._id,lat:t.country&&t.country.coordinates&&parseFloat(t.country.coordinates[0].latitude),lng:t.country&&t.country.coordinates&&parseFloat(t.country.coordinates[0].longitude),countryId:t.country&&t.country._id})}),d([...e])};return(0,s.useEffect)(()=>{A(),l&&l.length>0&&f(n().groupBy(l,"country._id"))},[l]),(0,a.jsx)(o.A,{onClose:x,points:u,language:r,activeMarker:p,markerInfo:(0,a.jsx)(e=>{let{info:t}=e;return t&&t.countryId&&g[t.countryId]?(0,a.jsx)("ul",{children:g[t.countryId].map((e,t)=>(0,a.jsx)("li",{children:(0,a.jsx)("a",{href:"".concat(r,"/operation/show/").concat(e._id),children:e.title})},t))}):null},{info:m}),children:u.length>=1?u.map((e,t)=>(0,a.jsx)(i.A,{name:e.title,id:e.id,countryId:e.countryId,icon:{url:"/images/map-marker-white.svg"},onClick:T,position:e},t)):null})}},15641:(e,t,r)=>{"use strict";r.d(t,{A:()=>l});var a=r(37876);r(14232);var s=r(62945);let l=e=>{let{name:t="Marker",id:r="",countryId:l="",type:n,icon:o,position:i,onClick:c,title:u,draggable:d=!1}=e;return i&&"number"==typeof i.lat&&"number"==typeof i.lng?(0,a.jsx)(s.pH,{position:i,icon:o,title:u||t,draggable:d,onClick:e=>{c&&c({name:t,id:r,countryId:l,type:n,position:i},{position:i,getPosition:()=>i},e)}}):null}},22352:(e,t,r)=>{"use strict";r.r(t),r.d(t,{canAddOperation:()=>n,canAddOperationForm:()=>o,canEditOperation:()=>i,canEditOperationForm:()=>c,canViewDiscussionUpdate:()=>u,default:()=>d});var a=r(37876);r(14232);var s=r(8178),l=r(59626);let n=(0,s.A)({authenticatedSelector:e=>!!e.permissions&&!!e.permissions.operation&&!!e.permissions.operation["create:any"],wrapperDisplayName:"CanAddOperation"}),o=(0,s.A)({authenticatedSelector:e=>!!e.permissions&&!!e.permissions.operation&&!!e.permissions.operation["create:any"],wrapperDisplayName:"CanAddOperationForm",FailureComponent:()=>(0,a.jsx)(l.default,{})}),i=(0,s.A)({authenticatedSelector:(e,t)=>{if(e.permissions&&e.permissions.operation){if(e.permissions.operation["update:any"])return!0;else if(e.permissions.operation["update:own"]&&t.operation&&t.operation.user&&t.operation.user._id===e.user._id)return!0}return!1},wrapperDisplayName:"CanEditOperation"}),c=(0,s.A)({authenticatedSelector:(e,t)=>{if(e.permissions&&e.permissions.operation){if(e.permissions.operation["update:any"])return!0;else if(e.permissions.operation["update:own"]&&t.operation&&t.operation.user&&t.operation.user._id===e.user._id)return!0}return!1},wrapperDisplayName:"CanEditOperationForm",FailureComponent:()=>(0,a.jsx)(l.default,{})}),u=(0,s.A)({authenticatedSelector:e=>!!e.permissions&&!!e.permissions.update&&!!e.permissions.update["read:any"],wrapperDisplayName:"CanViewDiscussionUpdate"}),d=n},45047:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>p});var a=r(37876),s=r(14232),l=r(49589),n=r(56970),o=r(37784),i=r(12697),c=r(29504),u=r(53718),d=r(31753);let p=e=>{let{filterText:t,onFilter:r,onFilterStatusChange:p,onClear:y,filterStatus:m}=e,[h,g]=(0,s.useState)([]),{t:f}=(0,d.Bd)("common"),x=async e=>{let t=await u.A.get("/operation_status",e);t&&Array.isArray(t.data)&&g(t.data)};return(0,s.useEffect)(()=>{x({query:{},sort:{title:"asc"}})},[]),(0,a.jsx)(l.A,{fluid:!0,className:"p-0",children:(0,a.jsxs)(n.A,{children:[(0,a.jsx)(o.A,{xs:6,className:"ps-0 align-self-end mb-3",children:(0,a.jsx)(i.A,{type:"text",className:"searchInput",placeholder:f("search"),"aria-label":"Search",value:t,onChange:r})}),(0,a.jsx)(o.A,{xs:6,children:(0,a.jsx)(c.A,{children:(0,a.jsxs)(c.A.Group,{as:n.A,controlId:"statusFilter",children:[(0,a.jsx)(c.A.Label,{column:!0,sm:"3",lg:"2",children:f("Status")}),(0,a.jsx)(o.A,{className:"ps-0 pe-1",children:(0,a.jsxs)(i.A,{as:"select","aria-label":"Status",onChange:p,value:m,children:[(0,a.jsx)("option",{value:"",children:"All"}),h.map((e,t)=>(0,a.jsx)("option",{value:e._id,children:e.title},t))]})})]})})})]})})}},50749:(e,t,r)=>{"use strict";r.d(t,{A:()=>i});var a=r(37876);r(14232);var s=r(89773),l=r(31753),n=r(5507);function o(e){let{t}=(0,l.Bd)("common"),r={rowsPerPageText:t("Rowsperpage")},{columns:o,data:i,totalRows:c,resetPaginationToggle:u,subheader:d,subHeaderComponent:p,handlePerRowsChange:y,handlePageChange:m,rowsPerPage:h,defaultRowsPerPage:g,selectableRows:f,loading:x,pagServer:T,onSelectedRowsChange:A,clearSelectedRows:j,sortServer:_,onSort:v,persistTableHead:b,sortFunction:C,...w}=e,S={paginationComponentOptions:r,noDataComponent:t("NoData"),noHeader:!0,columns:o,data:i||[],dense:!0,paginationResetDefaultPage:u,subHeader:d,progressPending:x,subHeaderComponent:p,pagination:!0,paginationServer:T,paginationPerPage:g||10,paginationRowsPerPageOptions:h||[10,15,20,25,30],paginationTotalRows:c,onChangeRowsPerPage:y,onChangePage:m,selectableRows:f,onSelectedRowsChange:A,clearSelectedRows:j,progressComponent:(0,a.jsx)(n.A,{}),sortIcon:(0,a.jsx)("i",{className:"sort-icon fas fa-exchange-alt"}),sortServer:_,onSort:v,sortFunction:C,persistTableHead:b,className:"rki-table"};return(0,a.jsx)(s.Ay,{...S})}o.defaultProps={subHeader:!1,pagination:!0,totalRows:null,pagServer:!0,onSelectedRowsChange:null,clearSelectedRows:!1,sortServer:!1,persistTableHead:!1};let i=o},66619:(e,t,r)=>{"use strict";r.d(t,{A:()=>y});var a=r(37876);r(14232);var s=r(62945);let l=e=>{let{position:t,onCloseClick:r,children:l}=e;return(0,a.jsx)(s.Fu,{position:t,onCloseClick:r,children:(0,a.jsx)("div",{children:l})})},n="labels.text.fill",o="labels.text.stroke",i="road.highway",c="geometry.stroke",u=[{elementType:"geometry",stylers:[{color:"#1d2c4d"}]},{elementType:n,stylers:[{color:"#8ec3b9"}]},{elementType:o,stylers:[{color:"#1a3646"}]},{featureType:"administrative",elementType:"geometry",stylers:[{visibility:"off"}]},{featureType:"administrative.country",elementType:c,stylers:[{color:"#4b6878"}]},{featureType:"administrative.land_parcel",elementType:"labels",stylers:[{visibility:"off"}]},{featureType:"administrative.land_parcel",elementType:n,stylers:[{color:"#64779e"}]},{featureType:"administrative.province",elementType:c,stylers:[{color:"#4b6878"}]},{featureType:"landscape.man_made",elementType:c,stylers:[{color:"#334e87"}]},{featureType:"landscape.natural",elementType:"geometry",stylers:[{color:"#023e58"}]},{featureType:"poi",stylers:[{visibility:"off"}]},{featureType:"poi",elementType:"geometry",stylers:[{color:"#283d6a"}]},{featureType:"poi",elementType:"labels.text",stylers:[{visibility:"off"}]},{featureType:"poi",elementType:n,stylers:[{color:"#6f9ba5"}]},{featureType:"poi",elementType:o,stylers:[{color:"#1d2c4d"}]},{featureType:"poi.park",elementType:"geometry.fill",stylers:[{color:"#023e58"}]},{featureType:"poi.park",elementType:n,stylers:[{color:"#3C7680"}]},{featureType:"road",stylers:[{visibility:"off"}]},{featureType:"road",elementType:"geometry",stylers:[{color:"#304a7d"}]},{featureType:"road",elementType:"labels.icon",stylers:[{visibility:"off"}]},{featureType:"road",elementType:n,stylers:[{color:"#98a5be"}]},{featureType:"road",elementType:o,stylers:[{color:"#1d2c4d"}]},{featureType:i,elementType:"geometry",stylers:[{color:"#2c6675"}]},{featureType:i,elementType:c,stylers:[{color:"#255763"}]},{featureType:i,elementType:n,stylers:[{color:"#b0d5ce"}]},{featureType:i,elementType:o,stylers:[{color:"#023e58"}]},{featureType:"road.local",elementType:"labels",stylers:[{visibility:"off"}]},{featureType:"transit",stylers:[{visibility:"off"}]},{featureType:"transit",elementType:n,stylers:[{color:"#98a5be"}]},{featureType:"transit",elementType:o,stylers:[{color:"#1d2c4d"}]},{featureType:"transit.line",elementType:"geometry.fill",stylers:[{color:"#283d6a"}]},{featureType:"transit.station",elementType:"geometry",stylers:[{color:"#3a4762"}]},{featureType:"water",elementType:"geometry",stylers:[{color:"#0e1626"}]},{featureType:"water",elementType:n,stylers:[{color:"#4e6d70"}]}];var d=r(89099),p=r(55316);let y=e=>{let{markerInfo:t,activeMarker:r,initialCenter:n,children:o,height:i=300,width:c="114%",language:y,zoom:m=1,minZoom:h=1,onClose:g}=e,{locale:f}=(0,d.useRouter)(),{isLoaded:x,loadError:T}=(0,p._)();return T?(0,a.jsx)("div",{children:"Error loading maps"}):x?(0,a.jsx)("div",{className:"map-container",children:(0,a.jsx)("div",{className:"mapprint",style:{width:c,height:i,position:"relative"},children:(0,a.jsxs)(s.u6,{mapContainerStyle:{width:c,height:"number"==typeof i?"".concat(i,"px"):i},center:n||{lat:52.520017,lng:13.404195},zoom:m,onLoad:e=>{e.setOptions({styles:u})},options:{minZoom:h,draggable:!0,keyboardShortcuts:!1,streetViewControl:!1,panControl:!1,clickableIcons:!1,mapTypeControl:!1,fullscreenControl:!0},children:[o,t&&r&&r.getPosition&&(0,a.jsx)(l,{position:r.getPosition(),onCloseClick:()=>{console.log("close click"),null==g||g()},children:t})]})})}):(0,a.jsx)("div",{children:"Loading Maps..."})}},69438:(e,t,r)=>{"use strict";r.d(t,{A:()=>p});var a=r(37876),s=r(14232),l=r(82851),n=r.n(l),o=r(29504),i=r(60282),c=r(53718),u=r(31753);function d(e){let{filtreg:t}=e,[r,l]=(0,s.useState)(!0),[d,p]=(0,s.useState)([]),[y,m]=(0,s.useState)([]),{t:h}=(0,u.Bd)("common"),g={query:{},limit:"~",sort:{title:"asc"}},f=async e=>{let r=await c.A.get("/worldregion",e);if(r&&Array.isArray(r.data)){let e=[],a=[];n().each(r.data,(t,r)=>{let s={...t,isChecked:!0};e.push(s),a.push(t._id)}),t(a),m(a),p(e)}};(0,s.useEffect)(()=>{f(g)},[]);let x=e=>{let r=[...d],a=[...y];r.forEach((t,s)=>{t.code===e.target.id&&(r[s].isChecked=e.target.checked,e.target.checked?a.push(t._id):a=a.filter(e=>e!==t._id))}),m(a),t(a),l(!1),p(r)};return(0,a.jsxs)("div",{className:"regions-multi-checkboxes",children:[(0,a.jsx)(o.A.Check,{type:"checkbox",id:"all",label:h("AllRegions"),checked:r,onChange:e=>{let r=d.map(t=>({...t,isChecked:e.target.checked})),a=[];e.target.checked&&(a=r.map(e=>e._id)),t(a),m(a),l(e.target.checked),p(r)}}),d.map((e,t)=>(0,a.jsx)(o.A.Check,{type:"checkbox",id:e.code,label:e.title,value:e.code,onChange:x,checked:d[t].isChecked},t)),(0,a.jsx)(i.A,{onClick:()=>{let e=d.map(e=>({...e,isChecked:!1}));m([]),l(!1),p(e),t([])},className:"btn-plain ps-2",children:h("ClearAll")})]})}d.defaultProps={filtreg:()=>{}};let p=d},69600:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});var a=r(37876);function s(e){return(0,a.jsx)("h2",{className:"page-heading",children:e.title})}},84265:(e,t,r)=>{(window.__NEXT_P=window.__NEXT_P||[]).push(["/operation",function(){return r(89231)}])},89231:(e,t,r)=>{"use strict";r.r(t),r.d(t,{__N_SSG:()=>f,default:()=>x});var a=r(37876),s=r(49589),l=r(56970),n=r(37784),o=r(48230),i=r.n(o),c=r(60282),u=r(14232),d=r(69438),p=r(69600),y=r(99293),m=r(15214),h=r(31753),g=r(22352),f=!0;let x=e=>{let{t}=(0,h.Bd)("common"),[r,o]=(0,u.useState)([]),[f,x]=(0,u.useState)(null),T=()=>(0,a.jsx)(i(),{href:"/operation/[...routes]",as:"/operation/create",children:(0,a.jsx)(c.A,{variant:"secondary",size:"sm",children:t("addOperation")})}),A=(0,g.canAddOperation)(()=>(0,a.jsx)(T,{})),j=e=>{x(e)};return(0,a.jsxs)(s.A,{fluid:!0,className:"p-0",children:[(0,a.jsx)(l.A,{children:(0,a.jsx)(n.A,{xs:12,children:(0,a.jsx)(p.A,{title:t("menu.operations")})})}),(0,a.jsx)(l.A,{children:(0,a.jsx)(n.A,{xs:!0,lg:12,children:(0,a.jsx)(m.default,{operations:r})})}),(0,a.jsx)(l.A,{children:(0,a.jsx)(n.A,{xs:!0,lg:12,children:(0,a.jsx)(d.A,{filtreg:e=>j(e),selectedRegions:[],regionHandler:j})})}),(0,a.jsx)(l.A,{children:(0,a.jsx)(n.A,{xs:12,className:"ps-4",children:(0,a.jsx)(A,{})})}),(0,a.jsx)(l.A,{className:"mt-3",children:(0,a.jsx)(n.A,{xs:12,children:(0,a.jsx)(y.default,{selectedRegions:f,setOperations:o})})})]})}},99293:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>f});var a=r(37876),s=r(14232),l=r(48230),n=r.n(l),o=r(82851),i=r.n(o),c=r(89099),u=r(10841),d=r.n(u),p=r(50749),y=r(53718),m=r(45047),h=r(31753);let g=e=>{let{partners:t}=e;return t&&t.length>0?(0,a.jsx)("ul",{children:t.map((e,t)=>{if(e.institution)return(0,a.jsx)("li",{children:(0,a.jsx)(n(),{href:"/institution/[...routes]",as:"/institution/show/".concat(e.institution._id),children:e.institution.title})},t)})}):null},f=function(e){let{t}=(0,h.Bd)("common"),r=(0,c.useRouter)(),{setOperations:l,selectedRegions:o}=e,[u,f]=(0,s.useState)(""),[x,T]=(0,s.useState)(""),[A,j]=(0,s.useState)(!1),[_,v]=(0,s.useState)([]),[b,C]=(0,s.useState)(!1),[w,S]=(0,s.useState)(0),[k,q]=(0,s.useState)(10),[N,P]=(0,s.useState)(1),[O,E]=(0,s.useState)(null),R={sort:{created_at:"desc"},lean:!0,populate:[{path:"partners.status",select:"title"},{path:"partners.institution",select:"title"},{path:"status",select:"title"},{path:"country",select:"coordinates"}],limit:k,page:1,query:{},select:"-timeline -region -hazard -description -end_date -syndrome -hazard_type -created_at -updated_at"},[I,D]=(0,s.useState)(R),F=[{name:t("Operations"),selector:"title",sortable:!0,cell:e=>(0,a.jsx)(n(),{href:"/operation/[...routes]",as:"/operation/show/".concat(e._id),children:e.title})},{name:t("Status"),selector:"status",sortable:!0,cell:e=>e.status&&e.status.title?e.status.title:""},{name:t("StartDate"),selector:"start_date",sortable:!0,cell:e=>e&&e.start_date?d()(e.start_date).format("M/D/Y"):""},{name:t("Partners"),selector:"partners",cell:e=>(0,a.jsx)(g,{partners:e.partners})}],H=async e=>{C(!0),r.query&&r.query.country&&(e.query.country=r.query.country),null===o?delete e.query.world_region:0===o.length?e.query.world_region=["__NO_MATCH__"]:e.query.world_region=o;let t=await y.A.get("/operation",e);t&&Array.isArray(t.data)&&(v(t.data),l(t.data),S(t.totalCount),C(!1))},B=async(e,t)=>{R.limit=e,R.page=t,C(!0),r.query&&r.query.country&&(R.query.country=r.query.country),null===o?delete R.query.world_region:0===o.length?R.query.world_region=["__NO_MATCH__"]:R.query.world_region=o,x&&(R.query={...R.query,status:x}),O&&(R.sort=O.sort);let a=await y.A.get("/operation",R);a&&Array.isArray(a.data)&&(v(a.data),l(a.data),q(e),C(!1)),P(t)};(0,s.useEffect)(()=>{I.page=1,H(I)},[o,r]),(0,s.useEffect)(()=>{H(I)},[I]);let M=async(e,t)=>{C(!0),R.sort={[e.selector]:t},x&&(R.query={...R.query,status:x}),""!==u&&(R.query={...R.query,title:u}),await H(R),E(R),C(!1)},z=(e,t)=>{e?(I.query.title=e,I.page=t):delete I.query.title,D({...I})},L=(0,s.useRef)(i().debounce((e,t)=>z(e,t),Number("500")||300)).current,V=(0,s.useMemo)(()=>{let e=e=>{T(e),e?(I.query.status=e,I.page=N):delete I.query.status,D({...I})};return(0,a.jsx)(m.default,{onFilter:e=>{f(e.target.value),L(e.target.value,N)},onFilterStatusChange:t=>e(t.target.value),onClear:()=>{u&&(j(!A),f(""))},filterText:u,filterStatus:x})},[u,x,A,o]);return(0,a.jsx)(p.A,{columns:F,data:_,totalRows:w,loading:b,subheader:!0,persistTableHead:!0,onSort:M,sortServer:!0,pagServer:!0,resetPaginationToggle:A,subHeaderComponent:V,handlePerRowsChange:B,handlePageChange:e=>{R.limit=k,R.page=e,""!==u&&(R.query={title:u}),x&&(R.query={...R.query,status:x}),O&&(R.sort=O.sort),H(R),P(e)}})}}},e=>{var t=t=>e(e.s=t);e.O(0,[9759,9773,636,6593,8792],()=>t(84265)),_N_E=e.O()}]);
//# sourceMappingURL=operation-37b5fcbf43b61bf4.js.map