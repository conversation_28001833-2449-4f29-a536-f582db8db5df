{"version": 3, "file": "static/chunks/pages/institution/permission-87bc4b5d251a55e4.js", "mappings": "+EACA,4CACA,0BACA,WACA,OAAe,EAAQ,KAA+C,CACtE,EACA,SAFsB,yQCGf,IAAMA,EAAoBC,CAAAA,EAAAA,EAAAA,CAAAA,CAAoBA,CAAC,CACpDC,sBAAuB,KACjBC,EAAMC,WAAW,IAAID,EAAMC,WAAW,CAACC,WAAW,IAAIF,EAAMC,WAAW,CAACC,WAAW,CAAC,aAAa,CAKvGC,CALyG,kBAKrF,mBACtB,GAAG,EAEkCL,CAAAA,EAAAA,EAAAA,CAAAA,CAAoBA,CAAC,CACxDC,sBAAuB,KACjBC,EAAMC,WAAW,IAAID,EAAMC,WAAW,CAACC,WAAW,IAAIF,EAAMC,WAAW,CAACC,WAAW,CAAC,aAAa,CAKvGC,CALyG,kBAKrF,wBACpBC,iBAAkB,IAAM,UAACC,EAAAA,OAAIA,CAAAA,CAAAA,EAC/B,GAAG,EAE+BP,CAAAA,EAAAA,EAAAA,CAAAA,CAAoBA,CAAC,CACrDC,sBAAuB,CAACC,EAAOM,KAC7B,GAAIN,EAAMC,WAAW,EAAID,EAAMC,WAAW,CAACC,WAAW,EAAE,GAClDF,EAAMC,WAAW,CAACC,WAAW,CAAC,aAAa,CAC7C,CAD+C,MACxC,OAEP,GAAIF,EAAMC,WAAW,CAACC,WAAW,CAAC,aAAa,EAAE,EACrCA,WAAW,EAAII,EAAMJ,WAAW,CAACK,IAAI,EAAID,EAAMJ,WAAW,CAACK,IAAI,GAAKP,EAAMO,IAAI,CAACC,GAAG,CAC1F,CAD4F,MACrF,CAGb,CAEF,OAAO,CACT,EACAL,mBAAoB,oBACtB,GAAG,EAEmCL,CAAAA,EAAAA,EAAAA,CAAAA,CAAoBA,CAAC,CACzDC,sBAAuB,CAACC,EAAOM,KAC7B,GAAIN,EAAMC,WAAW,EAAID,EAAMC,WAAW,CAACC,WAAW,EAAE,GAClDF,EAAMC,WAAW,CAACC,WAAW,CAAC,aAAa,CAC7C,CAD+C,MACxC,OAEP,GAAIF,EAAMC,WAAW,CAACC,WAAW,CAAC,aAAa,EAAE,EACrCA,WAAW,EAAII,EAAMJ,WAAW,CAACK,IAAI,EAAID,EAAMJ,WAAW,CAACK,IAAI,GAAKP,EAAMO,IAAI,CAACC,GAAG,CAC1F,CAD4F,MACrF,CAGb,CAEF,OAAO,CACT,EACAL,mBAAoB,yBACpBC,iBAAkB,IAAM,UAACC,EAAAA,OAAIA,CAAAA,CAAAA,EAC/B,GAAG,EAEoCP,CAAAA,EAAAA,EAAAA,CAAAA,CAAoBA,CAAC,CAC1DC,sBAAwBC,KAClBA,EAAMC,WAAW,IAAID,EAAMC,WAAW,CAACQ,MAAM,IAAIT,EAAMC,WAAW,CAACQ,MAAM,CAAC,WAAW,CAK3FN,CAL6F,kBAKzE,yBACtB,GAEaO,EAAuBZ,CAAAA,EAAAA,EAAAA,CAAAA,CAAoBA,CAAC,CACvDC,sBAAuB,CAACC,EAAOM,KAC7B,GAAIN,EAAMC,WAAW,EAAID,EAAMC,WAAW,CAACU,uBAAuB,EAAE,GAC9DX,EAAMC,WAAW,CAACU,uBAAuB,CAAC,aAAa,CACzD,CAD2D,MACpD,OAEP,GAAIX,EAAMC,WAAW,CAACU,uBAAuB,CAAC,aAAa,EAAE,EAEnDT,WAAW,EACjBI,EAAMJ,WAAW,CAACK,IAAI,EACtBD,EAAMJ,WAAW,CAACK,IAAI,GAAKP,EAAMO,IAAI,CAACC,GAAG,CAEzC,CADA,KACO,EAGb,CAEF,OAAO,CACT,EACAL,mBAAoB,sBACtB,GAAG,EAEYN,iBAAiBA,EAAC", "sources": ["webpack://_N_E/?b7bd", "webpack://_N_E/./pages/institution/permission.tsx"], "sourcesContent": ["\n    (window.__NEXT_P = window.__NEXT_P || []).push([\n      \"/institution/permission\",\n      function () {\n        return require(\"private-next-pages/institution/permission.tsx\");\n      }\n    ]);\n    if(module.hot) {\n      module.hot.dispose(function () {\n        window.__NEXT_P.push([\"/institution/permission\"])\n      });\n    }\n  ", "//Import Library\r\nimport React from 'react';\r\nimport connectedAuthWrapper from 'redux-auth-wrapper/connectedAuthWrapper';\r\n\r\n//Import services/components\r\nimport R403 from \"../r403\";\r\n\r\nexport const canAddInstitution = connectedAuthWrapper({\r\n  authenticatedSelector: (state) => {\r\n    if (state.permissions && state.permissions.institution && state.permissions.institution['create:any']) {\r\n      return true;\r\n    }\r\n    return false;\r\n  },\r\n  wrapperDisplayName: 'CanAddInstitution',\r\n});\r\n\r\nexport const canAddInstitutionForm = connectedAuthWrapper({\r\n  authenticatedSelector: (state) => {\r\n    if (state.permissions && state.permissions.institution && state.permissions.institution['create:any']) {\r\n      return true;\r\n    }\r\n    return false;\r\n  },\r\n  wrapperDisplayName: 'CanAddInstitutionForm',\r\n  FailureComponent: () => <R403/>\r\n});\r\n\r\nexport const canEditInstitution = connectedAuthWrapper({\r\n  authenticatedSelector: (state, props) => {\r\n    if (state.permissions && state.permissions.institution) {\r\n      if (state.permissions.institution['update:any']) {\r\n        return true;\r\n      } else { //if update:own\r\n        if (state.permissions.institution['update:own']) {\r\n          if (props.institution && props.institution.user && props.institution.user === state.user._id) {\r\n            return true;\r\n          }\r\n        }\r\n      }\r\n    }\r\n    return false;\r\n  },\r\n  wrapperDisplayName: 'CanEditInstitution',\r\n});\r\n\r\nexport const canEditInstitutionForm = connectedAuthWrapper({\r\n  authenticatedSelector: (state, props) => {\r\n    if (state.permissions && state.permissions.institution) {\r\n      if (state.permissions.institution['update:any']) {\r\n        return true;\r\n      } else { //if update:own\r\n        if (state.permissions.institution['update:own']) {\r\n          if (props.institution && props.institution.user && props.institution.user === state.user._id) {\r\n            return true;\r\n          }\r\n        }\r\n      }\r\n    }\r\n    return false;\r\n  },\r\n  wrapperDisplayName: 'CanEditInstitutionForm',\r\n  FailureComponent: () => <R403/>\r\n});\r\n\r\nexport const canViewDiscussionUpdate = connectedAuthWrapper({\r\n  authenticatedSelector: (state) => {\r\n    if (state.permissions && state.permissions.update && state.permissions.update['read:any']) {\r\n      return true;\r\n    }\r\n    return false;\r\n  },\r\n  wrapperDisplayName: 'CanViewDiscussionUpdate',\r\n});\r\n\r\nexport const canManageFocalPoints = connectedAuthWrapper({\r\n  authenticatedSelector: (state, props) => {\r\n    if (state.permissions && state.permissions.institution_focal_point) {\r\n      if (state.permissions.institution_focal_point[\"update:any\"]) {\r\n        return true;\r\n      } else {\r\n        if (state.permissions.institution_focal_point[\"update:own\"]) {\r\n          if (\r\n            props.institution &&\r\n            props.institution.user &&\r\n            props.institution.user === state.user._id\r\n          ) {\r\n            return true;\r\n          }\r\n        }\r\n      }\r\n    }\r\n    return false;\r\n  },\r\n  wrapperDisplayName: \"canManageFocalPoints\",\r\n});\r\n\r\nexport default canAddInstitution;"], "names": ["canAddInstitution", "connectedAuthWrapper", "authenticatedSelector", "state", "permissions", "institution", "wrapperDisplayName", "FailureComponent", "R403", "props", "user", "_id", "update", "canManageFocalPoints", "institution_focal_point"], "sourceRoot": "", "ignoreList": []}