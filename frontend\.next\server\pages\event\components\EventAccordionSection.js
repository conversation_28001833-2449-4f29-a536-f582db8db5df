(()=>{var e={};e.id=665,e.ids=[636,665,3220],e.modules={123:e=>{"use strict";e.exports=require("dom-helpers/removeEventListener")},1332:e=>{"use strict";e.exports=require("react-custom-scrollbars-2")},1428:e=>{"use strict";e.exports=import("axios")},1680:e=>{"use strict";e.exports=require("@restart/ui/utils")},1919:e=>{"use strict";e.exports=require("react-transition-group/Transition")},3892:e=>{"use strict";e.exports=require("classnames")},4048:e=>{"use strict";e.exports=require("react-truncate")},5517:(e,s,r)=>{"use strict";r.a(e,async(e,t)=>{try{r.r(s),r.d(s,{default:()=>m});var i=r(8732);r(82015);var n=r(83551),a=r(49481),o=r(93024),c=r(79029),u=r(75268),l=r(32566),d=r(28241),p=r(30336),x=e([c,l,d,p]);[c,l,d,p]=x.then?(await x)():x;let m=e=>{let s=(0,u.canViewDiscussionUpdate)(()=>(0,i.jsx)(l.default,{...e.routeData}));return(0,i.jsx)(i.Fragment,{children:(0,i.jsx)(n.A,{children:(0,i.jsxs)(a.A,{className:"eventAccordion",xs:12,children:[(0,i.jsx)(o.A,{children:(0,i.jsx)(c.default,{...e.eventData})}),(0,i.jsx)(o.A,{children:(0,i.jsx)(d.default,{...e.eventData})}),(0,i.jsx)(o.A,{children:(0,i.jsx)(p.default,{...e.eventData})}),(0,i.jsx)(o.A,{children:(0,i.jsx)(s,{})})]})})})};t()}catch(e){t(e)}})},6009:e=>{"use strict";e.exports=require("dom-helpers/ownerDocument")},6952:e=>{"use strict";e.exports=require("@restart/ui/Modal")},7374:e=>{"use strict";e.exports=require("@restart/ui/Dropdown")},8732:e=>{"use strict";e.exports=require("react/jsx-runtime")},9653:e=>{"use strict";e.exports=require("@restart/ui/Overlay")},11242:e=>{"use strict";e.exports=require("redux-persist/integration/react")},11688:e=>{"use strict";e.exports=require("@restart/hooks/useWillUnmount")},12388:e=>{"use strict";e.exports=require("dom-helpers/addEventListener")},13364:e=>{"use strict";e.exports=require("redux-saga/effects")},14062:e=>{"use strict";e.exports=import("react-redux")},14078:e=>{"use strict";e.exports=import("swr")},14332:e=>{"use strict";e.exports=require("uncontrollable")},16116:e=>{"use strict";e.exports=require("invariant")},18622:e=>{"use strict";e.exports=require("yup")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},22326:e=>{"use strict";e.exports=require("react-dom")},22541:e=>{"use strict";e.exports=require("redux-persist/lib/storage")},25303:e=>{"use strict";e.exports=require("@restart/ui/NavItem")},26324:e=>{"use strict";e.exports=require("warning")},27825:e=>{"use strict";e.exports=require("lodash")},27910:e=>{"use strict";e.exports=require("stream")},28217:e=>{"use strict";e.exports=require("@restart/ui/DropdownItem")},28241:(e,s,r)=>{"use strict";r.a(e,async(e,t)=>{try{r.r(s),r.d(s,{default:()=>p});var i=r(8732),n=r(82015),a=r(93024),o=r(82053),c=r(54131),u=r(88751),l=r(63349),d=e([c]);c=(d.then?(await d)():d)[0];let p=e=>{let{t:s}=(0,u.useTranslation)("common"),[r,t]=(0,n.useState)(!1);return(0,i.jsx)(i.Fragment,{children:(0,i.jsxs)(a.A.Item,{eventKey:"0",children:[(0,i.jsxs)(a.A.Header,{onClick:()=>t(!r),children:[(0,i.jsx)("div",{className:"cardTitle",children:s("Events.show.MoreInformation")}),(0,i.jsx)("div",{className:"cardArrow",children:r?(0,i.jsx)(o.FontAwesomeIcon,{icon:c.faMinus,color:"#fff"}):(0,i.jsx)(o.FontAwesomeIcon,{icon:c.faPlus,color:"#fff"})})]}),(0,i.jsx)(a.A.Body,{children:(0,i.jsx)(l.A,{description:e.more_info})})]})})};t()}catch(e){t(e)}})},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},29628:e=>{"use strict";e.exports=require("dom-helpers/addClass")},29825:e=>{"use strict";e.exports=require("prop-types")},29841:e=>{"use strict";e.exports=require("dom-helpers/hasClass")},30336:(e,s,r)=>{"use strict";r.a(e,async(e,t)=>{try{r.r(s),r.d(s,{default:()=>p});var i=r(8732),n=r(82015),a=r(54131),o=r(82053),c=r(93024),u=r(42447),l=r(88751),d=e([a,u]);[a,u]=d.then?(await d)():d;let p=e=>{let{t:s}=(0,l.useTranslation)("common"),[r,t]=(0,n.useState)(!1);return(0,i.jsx)(i.Fragment,{children:(0,i.jsxs)(c.A.Item,{eventKey:"0",children:[(0,i.jsxs)(c.A.Header,{onClick:()=>t(!r),children:[(0,i.jsx)("div",{className:"cardTitle",children:s("Events.show.MediaGallery")}),(0,i.jsx)("div",{className:"cardArrow",children:r?(0,i.jsx)(o.FontAwesomeIcon,{icon:a.faMinus,color:"#fff"}):(0,i.jsx)(o.FontAwesomeIcon,{icon:a.faPlus,color:"#fff"})})]}),(0,i.jsx)(c.A.Body,{children:(0,i.jsx)(u.A,{gallery:e.images,imageSource:e.images_src})})]})})};t()}catch(e){t(e)}})},30362:e=>{"use strict";e.exports=require("@restart/ui/DropdownToggle")},32566:(e,s,r)=>{"use strict";r.a(e,async(e,t)=>{try{r.r(s),r.d(s,{default:()=>p});var i=r(8732),n=r(82015),a=r(93024),o=r(82053),c=r(54131),u=r(88751),l=r(82491),d=e([c,l]);[c,l]=d.then?(await d)():d;let p=e=>{let{t:s}=(0,u.useTranslation)("common"),[r,t]=(0,n.useState)(!1);return(0,i.jsx)(i.Fragment,{children:(0,i.jsxs)(a.A.Item,{eventKey:"0",children:[(0,i.jsxs)(a.A.Header,{onClick:()=>t(!r),children:[(0,i.jsx)("div",{className:"cardTitle",children:s("Events.show.Discussions")}),(0,i.jsx)("div",{className:"cardArrow",children:r?(0,i.jsx)(o.FontAwesomeIcon,{icon:c.faMinus,color:"#fff"}):(0,i.jsx)(o.FontAwesomeIcon,{icon:c.faPlus,color:"#fff"})})]}),(0,i.jsx)(a.A.Body,{children:(0,i.jsx)(l.A,{type:"event",id:e?.routes?e.routes[1]:null})})]})})};t()}catch(e){t(e)}})},33873:e=>{"use strict";e.exports=require("path")},36653:e=>{"use strict";e.exports=require("nprogress")},36955:e=>{"use strict";e.exports=require("@restart/hooks/useBreakpoint")},37766:e=>{"use strict";e.exports=require("@restart/hooks/useMergedRefs")},39756:e=>{"use strict";e.exports=import("redux")},40051:e=>{"use strict";e.exports=require("dom-helpers/removeClass")},40361:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/pages.runtime.prod.js")},42447:(e,s,r)=>{"use strict";r.a(e,async(e,t)=>{try{r.d(s,{A:()=>p});var i=r(8732),n=r(82015),a=r(81149),o=r(82053),c=r(54131),u=r(91353),l=r(88751);r(72025);var d=e([c]);c=(d.then?(await d)():d)[0];let p=e=>{let{t:s}=(0,l.useTranslation)("common"),[r,t]=(0,n.useState)([]),d=e=>{let r=/(http|https):\/\/(\w+:{0,1}\w*)?(\S+)(:[0-9]+)?(\/|\/([\w#!:.?+=&%!\-\/]))?/.test(e.description);return(0,i.jsxs)("div",{className:"carousel-legend",children:[(0,i.jsxs)("p",{className:"lead",children:[(0,i.jsx)("b",{children:s("Filename")})," ",e.originalName||"No Name found"]}),e.description&&(0,i.jsxs)("div",{className:"source_link",children:[(0,i.jsx)("p",{children:(0,i.jsx)("b",{children:s("Source")})}),r?(0,i.jsxs)("div",{children:[(0,i.jsx)(o.FontAwesomeIcon,{icon:c.faLink,size:"1x",color:"#999",className:"me-1"}),(0,i.jsx)("a",{className:"source_link",href:e.description,target:"_blank",rel:"noopener noreferrer",children:e.description})]}):(0,i.jsx)("div",{children:(0,i.jsx)("p",{className:"ps-0 py-0",style:{wordBreak:"break-all"},children:e.description})})]}),e.downloadLink&&(0,i.jsxs)(u.A,{className:"btn btn-success mt-2 btn--download",href:e.downloadLink,children:[s("Download"),(0,i.jsx)(o.FontAwesomeIcon,{icon:c.faDownload,size:"1x",className:"ms-1"})]})]})};return(0,n.useEffect)(()=>{let s=[];e&&e.gallery&&Array.isArray(e.gallery)&&e.gallery.map((r,t)=>{let i,n=r&&r.name.split(".").pop();switch(n){case"JPG":case"jpg":case"jpeg":case"png":i=`http://localhost:3001/api/v1/image/show/${r._id}`;break;case"pdf":i="/images/fileIcons/pdfFile.png";break;case"docx":i="/images/fileIcons/wordFile.png";break;case"xls":case"xlsx":i="/images/fileIcons/xlsFile.png";break;default:i="/images/fileIcons/otherFile.png"}let a=("docx"===n||"pdf"===n||"xls"===n||"xlsx"===n)&&`http://localhost:3001/api/v1/files/download/${r._id}`,o=`${r&&r.original_name?r.original_name:"No Name found"}`,c=e.imageSource&&Array.isArray(e.imageSource)&&e.imageSource.length>0?e.imageSource[t]:"";s.push({src:i,description:c,originalName:o,downloadLink:a})}),t(s)},[e]),(0,i.jsx)("div",{children:r&&0===r.length?(0,i.jsx)("div",{className:"border border-info my-3 mx-0",children:(0,i.jsx)("p",{className:"d-flex d-flex justify-content-center p-2 m-0",children:s("NoFilesFound!")})}):(0,i.jsx)(a.Carousel,{showThumbs:!0,showStatus:!0,showIndicators:!0,infiniteLoop:!0,useKeyboardArrows:!0,autoPlay:!1,stopOnHover:!0,swipeable:!0,dynamicHeight:!1,emulateTouch:!0,renderThumbs:()=>r.map((e,s)=>(0,i.jsx)("img",{src:e.src,alt:`Thumbnail ${s+1}`,style:{width:"60px",height:"60px",objectFit:"cover"}},s)),children:r.map((e,s)=>(0,i.jsxs)("div",{children:[(0,i.jsx)("img",{src:e.src,alt:e.originalName||"Gallery image",style:{maxHeight:"500px",objectFit:"contain"}}),d(e)]},s))})})};t()}catch(e){t(e)}})},42893:e=>{"use strict";e.exports=import("react-hot-toast")},43294:e=>{"use strict";e.exports=require("formik")},50009:e=>{"use strict";e.exports=require("@restart/ui/DropdownMenu")},54131:e=>{"use strict";e.exports=import("@fortawesome/free-solid-svg-icons")},57664:e=>{"use strict";e.exports=require("dom-helpers/scrollbarSize")},58928:e=>{"use strict";e.exports=require("dom-helpers/canUseDOM")},59717:e=>{"use strict";e.exports=require("@restart/ui/DropdownContext")},60560:e=>{"use strict";e.exports=require("dom-helpers/contains")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},63349:(e,s,r)=>{"use strict";r.d(s,{A:()=>a});var t=r(8732),i=r(82015),n=r(88751);let a=e=>{let{t:s}=(0,n.useTranslation)("common"),r=parseInt("255"),[a,o]=(0,i.useState)(!1);return(0,t.jsxs)(t.Fragment,{children:[e.description?(0,t.jsx)("div",{dangerouslySetInnerHTML:((s,t)=>({__html:!t&&s.length>r?s.substring(0,r)+"...":e.description}))(e.description,a),className:"operationDesc"}):null,e.description&&e.description.length>r?(0,t.jsx)("button",{type:"button",className:"readMoreText",onClick:()=>o(!a),children:s(a?"readLess":"readMore")}):null]})}},65447:e=>{"use strict";e.exports=require("@restart/ui/Button")},67364:e=>{"use strict";e.exports=require("@restart/hooks/useCallbackRef")},68455:e=>{"use strict";e.exports=import("redux-saga")},69722:e=>{"use strict";e.exports=require("es6-promise")},72025:()=>{},74075:e=>{"use strict";e.exports=require("zlib")},74716:e=>{"use strict";e.exports=require("moment")},74987:e=>{"use strict";e.exports=require("@restart/ui/ModalManager")},75268:(e,s,r)=>{"use strict";r.r(s),r.d(s,{canAddEvent:()=>o,canAddEventForm:()=>c,canEditEvent:()=>u,canEditEventForm:()=>l,canViewDiscussionUpdate:()=>d,default:()=>p});var t=r(8732);r(82015);var i=r(81366),n=r.n(i),a=r(61421);let o=n()({authenticatedSelector:e=>!!e.permissions&&!!e.permissions.event&&!!e.permissions.event["create:any"],wrapperDisplayName:"CanAddEvent"}),c=n()({authenticatedSelector:e=>!!e.permissions&&!!e.permissions.event&&!!e.permissions.event["create:any"],wrapperDisplayName:"CanAddEventForm",FailureComponent:()=>(0,t.jsx)(a.default,{})}),u=n()({authenticatedSelector:(e,s)=>{if(e.permissions&&e.permissions.event){if(e.permissions.event["update:any"])return!0;else if(e.permissions.event["update:own"]&&s.event&&s.event.user&&s.event.user._id===e.user._id)return!0}return!1},wrapperDisplayName:"CanEditEvent"}),l=n()({authenticatedSelector:(e,s)=>{if(e.permissions&&e.permissions.event){if(e.permissions.event["update:any"])return!0;else if(e.permissions.event["update:own"]&&s.event&&s.event.user&&s.event.user._id===e.user._id)return!0}return!1},wrapperDisplayName:"CanEditEventForm",FailureComponent:()=>(0,t.jsx)(a.default,{})}),d=n()({authenticatedSelector:e=>!!e.permissions&&!!e.permissions.update&&!!e.permissions.update["read:any"],wrapperDisplayName:"CanViewDiscussionUpdate"}),p=o},78097:e=>{"use strict";e.exports=require("next-redux-saga")},78634:e=>{"use strict";e.exports=require("@restart/ui/Anchor")},79029:(e,s,r)=>{"use strict";r.a(e,async(e,t)=>{try{r.r(s),r.d(s,{default:()=>x});var i=r(8732),n=r(82015),a=r(54131),o=r(82053),c=r(93024),u=r(88751),l=r(63349),d=e([a]);a=(d.then?(await d)():d)[0];let p={Low:"risk0",Medium:"risk1",High:"risk2","Very High":"risk3"},x=e=>{let{t:s}=(0,u.useTranslation)("common"),[r,t]=(0,n.useState)(!1),{risk_assessment:d}=e;return(0,i.jsx)(i.Fragment,{children:(0,i.jsxs)(c.A.Item,{eventKey:"0",children:[(0,i.jsxs)(c.A.Header,{onClick:()=>t(!r),children:[(0,i.jsx)("div",{className:"cardTitle",children:s("Events.show.RiskAssessment")}),(0,i.jsx)("div",{className:"cardArrow",children:r?(0,i.jsx)(o.FontAwesomeIcon,{icon:a.faMinus,color:"#fff"}):(0,i.jsx)(o.FontAwesomeIcon,{icon:a.faPlus,color:"#fff"})})]}),(0,i.jsx)(c.A.Body,{children:d.country?(0,i.jsxs)("div",{children:[function(e,s){return(0,i.jsxs)("div",{className:"riskDetails",children:[e.country?(0,i.jsxs)("div",{className:"riskItems",children:[(0,i.jsx)("div",{className:`riskIcon ${p[e.country.title]}`,children:(0,i.jsx)("img",{src:"/images/event_country.png",width:"30",height:"30",alt:"Risk Assessment Info"})}),(0,i.jsxs)("div",{className:"riskInfo",children:[(0,i.jsx)("h5",{children:s("Events.show.Country")}),(0,i.jsx)("h4",{children:e.country.title})]})]}):(0,i.jsx)(i.Fragment,{}),e.region?(0,i.jsxs)("div",{className:"riskItems",children:[(0,i.jsx)("div",{className:`riskIcon ${e&&e.region?p[e.region.title]:""}`,children:(0,i.jsx)("img",{src:"/images/event_region.png",width:"35",height:"26",alt:"Risk Assessment Info"})}),(0,i.jsxs)("div",{className:"riskInfo",children:[(0,i.jsx)("h5",{children:s("Events.show.Region")}),(0,i.jsx)("h4",{children:e&&e.region?e.region.title:""})]})]}):(0,i.jsx)(i.Fragment,{}),e.international?(0,i.jsxs)("div",{className:"riskItems",children:[(0,i.jsx)("div",{className:`riskIcon ${e&&e.international?p[e.international.title]:""}`,children:(0,i.jsx)("img",{src:"/images/event_international.png",width:"38",height:"38",alt:"Risk Assessment Info"})}),(0,i.jsxs)("div",{className:"riskInfo",children:[(0,i.jsx)("h5",{children:s("Events.show.International")}),(0,i.jsx)("h4",{children:e&&e.international?e.international.title:""})]})]}):(0,i.jsx)(i.Fragment,{})]})}(d,s),function(e){return(0,i.jsx)("div",{className:"mt-4",children:(0,i.jsx)(l.A,{description:e&&e.risk_assessment.description?e.risk_assessment.description:""})})}(e)]}):null})]})})};t()}catch(e){t(e)}})},80237:(e,s)=>{"use strict";Object.defineProperty(s,"A",{enumerable:!0,get:function(){return r}});var r=function(e){return e.PAGES="PAGES",e.PAGES_API="PAGES_API",e.APP_PAGE="APP_PAGE",e.APP_ROUTE="APP_ROUTE",e.IMAGE="IMAGE",e}({})},81149:e=>{"use strict";e.exports=require("react-responsive-carousel")},81366:e=>{"use strict";e.exports=require("redux-auth-wrapper/connectedAuthWrapper")},81413:(e,s)=>{"use strict";Object.defineProperty(s,"M",{enumerable:!0,get:function(){return function e(s,r){return r in s?s[r]:"then"in s&&"function"==typeof s.then?s.then(s=>e(s,r)):"function"==typeof s&&"default"===r?s:void 0}}})},81521:e=>{"use strict";e.exports=require("dom-helpers/querySelectorAll")},81895:e=>{"use strict";e.exports=require("@restart/hooks/useEventCallback")},82015:e=>{"use strict";e.exports=require("react")},82053:e=>{"use strict";e.exports=require("@fortawesome/react-fontawesome")},85087:(e,s,r)=>{"use strict";r.a(e,async(e,t)=>{try{r.r(s),r.d(s,{config:()=>v,default:()=>p,getServerSideProps:()=>h,getStaticPaths:()=>m,getStaticProps:()=>x,reportWebVitals:()=>f,routeModule:()=>q,unstable_getServerProps:()=>A,unstable_getServerSideProps:()=>y,unstable_getStaticParams:()=>w,unstable_getStaticPaths:()=>j,unstable_getStaticProps:()=>g});var i=r(63885),n=r(80237),a=r(81413),o=r(9616),c=r.n(o),u=r(72386),l=r(5517),d=e([u,l]);[u,l]=d.then?(await d)():d;let p=(0,a.M)(l,"default"),x=(0,a.M)(l,"getStaticProps"),m=(0,a.M)(l,"getStaticPaths"),h=(0,a.M)(l,"getServerSideProps"),v=(0,a.M)(l,"config"),f=(0,a.M)(l,"reportWebVitals"),g=(0,a.M)(l,"unstable_getStaticProps"),j=(0,a.M)(l,"unstable_getStaticPaths"),w=(0,a.M)(l,"unstable_getStaticParams"),A=(0,a.M)(l,"unstable_getServerProps"),y=(0,a.M)(l,"unstable_getServerSideProps"),q=new i.PagesRouteModule({definition:{kind:n.A.PAGES,page:"/event/components/EventAccordionSection",pathname:"/event/components/EventAccordionSection",bundlePath:"",filename:""},components:{App:u.default,Document:c()},userland:l});t()}catch(e){t(e)}})},86842:e=>{"use strict";e.exports=require("@restart/ui/SelectableContext")},86843:e=>{"use strict";e.exports=require("moment/locale/de")},87571:e=>{"use strict";e.exports=require("dom-helpers/transitionEnd")},88751:e=>{"use strict";e.exports=require("next-i18next")},93787:e=>{"use strict";e.exports=require("redux-persist")},94947:e=>{"use strict";e.exports=require("@restart/hooks/useTimeout")},96196:e=>{"use strict";e.exports=require("next-redux-wrapper")},98320:e=>{"use strict";e.exports=require("@restart/hooks/useIsomorphicEffect")},99460:e=>{"use strict";e.exports=require("dom-helpers/css")}};var s=require("../../../webpack-runtime.js");s.C(e);var r=e=>s(s.s=e),t=s.X(0,[6089,9216,9616,2386,2491],()=>r(85087));module.exports=t})();