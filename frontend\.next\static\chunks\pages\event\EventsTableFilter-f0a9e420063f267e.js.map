{"version": 3, "file": "static/chunks/pages/event/EventsTableFilter-f0a9e420063f267e.js", "mappings": "oNAqFA,MAnEqB,OAAC,CACpBA,YAAU,QAkEGC,EAjEbC,CAAQ,SACRC,CAAO,CAgEmB,qBA/D1BC,CAAoB,cACpBC,CAAY,CAOb,GACO,CAACC,EAAYC,EAAc,CAAGC,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAC,EAAE,EACzC,GAAEC,CAAC,CAAE,CAAGC,CAAAA,EAAAA,EAAAA,EAAAA,CAAcA,CAAC,UAIvBC,EAAc,MAAOC,IACzB,IAAMC,EAAW,MAAMC,EAAAA,CAAUA,CAACC,GAAG,CAAC,cAAeH,GAEjDC,GAAYG,MAAMC,OAAO,CAACJ,EAASK,IAAI,GAAG,EAC9BL,EAASK,IAAI,CAE/B,EAMA,MAJAC,CAAAA,EAAAA,EAAAA,SAAAA,CAASA,CAAC,KACRR,EAAY,CAAES,MAAO,CAAC,EAAGC,KAAM,CAAEC,MAAO,KAAM,CAAE,EAClD,EAAG,EAAE,EAGH,UAACC,EAAAA,CAASA,CAAAA,CAACC,KAAK,IAACC,UAAU,eACzB,WAACC,EAAAA,CAAGA,CAAAA,WACF,UAACC,EAAAA,CAAGA,CAAAA,CAACC,GAAI,EAAGH,UAAU,eACpB,UAACI,EAAAA,CAAWA,CAAAA,CACVC,KAAK,OACLL,UAAU,cACVM,YAActB,EAAE,uBAChBuB,aAAW,SACXC,MAAOjC,EACPkC,SAAUhC,MAId,UAACyB,EAAAA,CAAGA,CAAAA,CAACC,GAAI,WACP,WAACC,EAAAA,CAAWA,CAAAA,CACVM,GAAG,SACHH,aAAW,aACXI,mBAAiB,cACjBF,SAAU9B,EACV6B,MAAO5B,YAEP,UAACgC,SAAAA,CAAOJ,MAAO,YAAKxB,EAAE,mCACrBH,EAAWgC,GAAG,CAAC,CAACC,EAAWC,IAExB,UAACH,SAAAA,CAAmBJ,MAAOM,EAAKE,GAAG,UAChCF,EAAKjB,KAAK,EADAkB,aAU7B,kBClFA,4CACA,2BACA,WACA,OAAe,EAAQ,GAAgD,CACvE,EACA,WAFsB", "sources": ["webpack://_N_E/./pages/event/EventsTableFilter.tsx", "webpack://_N_E/?82ee"], "sourcesContent": ["//Import Library\r\nimport { useEffect, useState } from \"react\";\r\nimport _ from \"lodash\";\r\nimport {\r\n  Col,\r\n  Container,\r\n  FormControl,\r\n  FormGroup,\r\n  FormLabel,\r\n  Row,\r\n} from \"react-bootstrap\";\r\n\r\n//Import services/components\r\nimport apiService from \"../../services/apiService\";\r\nimport React from \"react\";\r\nimport { useTranslation } from 'next-i18next';\r\n\r\n\r\nconst EventsFilter = ({\r\n  filterText,\r\n  onFilter,\r\n  onClear,\r\n  onFilterHazardChange,\r\n  filterHazard,\r\n}: {\r\n  filterText: any,\r\n  onFilter: any,\r\n  onClear: any,\r\n  onFilterHazardChange: any,\r\n  filterHazard: any,\r\n}) => {\r\n  const [hazardType, setHazardType] = useState([]);\r\n  const { t } = useTranslation('common');\r\n\r\n\r\n\r\n  const getNetworks = async (params: any) => {\r\n    const response = await apiService.get(\"/hazardtype\", params);\r\n\r\n    if (response && Array.isArray(response.data)) {\r\n      setHazardType(response.data);\r\n    }\r\n  };\r\n\r\n  useEffect(() => {\r\n    getNetworks({ query: {}, sort: { title: \"asc\" } });\r\n  }, []);\r\n\r\n  return (\r\n    <Container fluid className=\"p-0\">\r\n      <Row>\r\n        <Col xs={6} className=\"p-0\">\r\n          <FormControl\r\n            type=\"text\"\r\n            className=\"searchInput\"\r\n            placeholder= {t(\"Events.table.Search\")}\r\n            aria-label=\"Search\"\r\n            value={filterText}\r\n            onChange={onFilter}\r\n          />\r\n        </Col>\r\n\r\n        <Col xs={6}>\r\n          <FormControl\r\n            as=\"select\"\r\n            aria-label=\"HazardType\"\r\n            aria-placeholder=\"Hazard Type\"\r\n            onChange={onFilterHazardChange}\r\n            value={filterHazard}\r\n          >\r\n            <option value={\"\"}>{t(\"Events.forms.SelectHazardType\")}</option>\r\n            {hazardType.map((item: any, index) => {\r\n              return (\r\n                <option key={index} value={item._id}>\r\n                  {item.title}\r\n                </option>\r\n              );\r\n            })}\r\n          </FormControl>\r\n        </Col>\r\n      </Row>\r\n    </Container>\r\n  );\r\n};\r\n\r\nexport default EventsFilter;\r\n", "\n    (window.__NEXT_P = window.__NEXT_P || []).push([\n      \"/event/EventsTableFilter\",\n      function () {\n        return require(\"private-next-pages/event/EventsTableFilter.tsx\");\n      }\n    ]);\n    if(module.hot) {\n      module.hot.dispose(function () {\n        window.__NEXT_P.push([\"/event/EventsTableFilter\"])\n      });\n    }\n  "], "names": ["filterText", "<PERSON><PERSON><PERSON>er", "onFilter", "onClear", "onFilterHazardChange", "filterHazard", "hazardType", "setHazardType", "useState", "t", "useTranslation", "getNetworks", "params", "response", "apiService", "get", "Array", "isArray", "data", "useEffect", "query", "sort", "title", "Container", "fluid", "className", "Row", "Col", "xs", "FormControl", "type", "placeholder", "aria-label", "value", "onChange", "as", "aria-placeholder", "option", "map", "item", "index", "_id"], "sourceRoot": "", "ignoreList": []}